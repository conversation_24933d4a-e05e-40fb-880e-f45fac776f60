import{a as q}from"./chunk-2K2Z5CD6.js";import"./chunk-O3A2VMJ6.js";import"./chunk-W3ZRPNOX.js";import{Ma as T,S as W,z as I}from"./chunk-JD6NH5K6.js";import{c as M}from"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import{a as F}from"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as h}from"./chunk-X3ESGVCB.js";import{q as z,r as j}from"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as $}from"./chunk-CCQRCL2K.js";import{h as P}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{a,b as R,c as D,d as N,e as w,n as H}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import"./chunk-OKP6DFCI.js";import{o as v,rb as b}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import{b as V}from"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{b as g,m as y}from"./chunk-56SJOU6P.js";import{ia as A}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as x}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as L,h as u,n as f}from"./chunk-3KENBVE7.js";u();f();var o=L(x());u();f();var l=L(x());var K=v(h)`
  border-radius: ${R.radiusRow};
  background-color: #2a2a2a;
  opacity: ${e=>e.disabled?.5:1};
  cursor: pointer;
  &:hover {
    background: ${e=>e.disabled?"#2a2a2a":"#333333"};
  }
`,Q=v(h)`
  margin-left: 10px;
  margin-right: 16px;
  margin-top: 15px;
  margin-bottom: 15px;
`,B=e=>{let{topLeft:i,topRight:n,bottomLeft:r,bottomRight:s,image:m,disabled:p,onClick:d}=e;return l.createElement(K,{disabled:p,onClick:d},l.createElement(Q,null,m,l.createElement($,{width:`${V*.69}px`,margin:"0 0 0 10px"},l.createElement(h,{justify:"space-between"},l.createElement(b,{size:16,weight:600,color:i?.color,margin:"0",noWrap:!0,maxWidth:"80%",lineHeight:21},i?.label||""),l.createElement(b,{weight:500,margin:"0",size:14,noWrap:!0,color:n?.color||"#777777",lineHeight:21},n?.label||"")),l.createElement(h,{justify:"space-between"},l.createElement(b,{weight:500,margin:"0",size:14,noWrap:!0,color:r?.color||"#777777",lineHeight:21},r?.label||""),l.createElement(b,{weight:500,size:14,color:s?.color,margin:"0",noWrap:!0,lineHeight:21},s?.label||"")))))};u();f();var S=L(x());var X=()=>{let{t:e}=y(),{popDetailView:i}=P(),{handleShowModalVisibility:n,handleHideModalVisibility:r}=T(),s=(0,S.useCallback)(()=>i(),[i]),m=(0,S.useCallback)(()=>{r("convertStakeAccounts"),n("convertStakeStatus")},[r,n]),p=e("liquidStakeReviewPageTitle"),d=j(m);return{headerTitle:p,onBack:s,...d}},U=()=>{let e=X();return S.default.createElement(q,{...e})};var Y=()=>{let{t:e}=y(),{pushDetailView:i}=P(),{handleHideModalVisibility:n}=T(),r=(0,o.useCallback)(()=>{n("convertStakeAccounts")},[n]),s=(0,o.useCallback)(()=>{i(o.default.createElement(U,null))},[i]),m=e("convertToJitoSOL"),p=(0,o.useMemo)(()=>({type:"close",onClick:r}),[r]),{eligibleStakeAccounts:d,inelegibleStakeAccounts:k,isLoading:C,error:t,onSelectStakeAccount:E,onReload:_,tokenSymbol:O,tokenDecimals:J}=z(s);return{headerTitle:m,leftButton:p,navigateToReviewPage:s,eligibleStakeAccounts:d,inelegibleStakeAccounts:k,isLoading:C,error:t,onSelectStakeAccount:E,onReload:_,tokenSymbol:O,tokenDecimals:J}},c={button:a({marginTop:16,padding:16}),errorContainer:a({padding:16,height:"100%",display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",gap:8}),errorDescription:a({textAlign:"center"}),errorTitle:a({marginTop:16}),iconContainer:a({backgroundColor:"accentWarning",borderRadius:"circle",padding:8,height:48,width:48}),ineligibleSectionHeaderContainer:a({display:"flex",flexDirection:"row",alignItems:"center",gap:4,marginTop:16,marginBottom:8}),page:a({height:"100%"}),body:a({height:"100%",paddingBottom:32,overflow:"auto"}),empty:a({display:"flex",alignItems:"center",justifyContent:"center",width:"100%",height:"100%"})},Z=v.div`
  padding: 16px;
  width: 256px;
`,oo=o.default.memo(({eligibleStakeAccounts:e,error:i,headerTitle:n,inelegibleStakeAccounts:r,isLoading:s,leftButton:m,onReload:p,onSelectStakeAccount:d,tokenDecimals:k,tokenSymbol:C})=>o.default.createElement("div",{className:c.page},o.default.createElement(I,{leftButton:m,titleSize:"regular"},n),o.default.createElement("div",{className:c.body},s?o.default.createElement(to,null):i!==null?o.default.createElement(io,{onReload:p}):!e.length&&!r.length?o.default.createElement("div",{className:c.empty},o.default.createElement(w,{color:"textSecondary",font:"bodySemibold",children:g("stakeAccountListNoStakingAccounts")})):o.default.createElement(N,{element:"ul",gap:"list"},e.map(t=>o.default.createElement("li",{key:t.pubkey},o.default.createElement(B,{image:o.default.createElement(F,{width:48,iconUrl:t.imageUri}),title:t.pubkey,bottomLeft:{label:`${A(t.amount.toString(),k)} ${C}`,color:"#777777"},topLeft:{label:t.title,color:"#FFFFFF"},onClick:()=>d(t)}))),r.length>0?o.default.createElement(o.default.Fragment,null,o.default.createElement("li",{className:c.ineligibleSectionHeaderContainer},o.default.createElement(w,{font:"bodyMedium",color:"textTertiary",children:g("convertStakeAccountListPageIneligibleSectionTitle")}),o.default.createElement(W,{alignment:"bottom",content:o.default.createElement(Z,null,o.default.createElement(w,{font:"body",children:g("convertStakeAccountIneligibleBottomSheetDescription")})),index:0},o.default.createElement(D.Info,{size:16,color:"textTertiary"}))),r.map(t=>o.default.createElement("li",{key:t.pubkey},o.default.createElement(B,{disabled:!0,image:o.default.createElement(F,{width:48,iconUrl:t.imageUri}),title:t.pubkey,bottomLeft:{label:`${A(t.amount.toString(),k)} ${C}`,color:"#777777"},topLeft:{label:t.title,color:"#FFFFFF"}})))):null)))),eo=()=>{let e=Y();return o.default.createElement(oo,{...e})},to=()=>o.default.createElement(o.default.Fragment,null,[...Array(5)].map((e,i)=>o.default.createElement(M,{key:`account-row-loader-${i}`,hideTextRight:!0}))),io=({onReload:e})=>o.default.createElement("div",{className:c.errorContainer},o.default.createElement("div",{className:c.iconContainer},o.default.createElement(D.AlertCircle,{size:32,color:"bgWallet"})),o.default.createElement(w,{className:c.errorTitle,font:"bodySemibold",children:g("convertStakeAccountListPageErrorTitle")}),o.default.createElement(w,{className:c.errorDescription,font:"body",color:"textSecondary",children:g("convertStakeAccountListPageErrorDescription")}),o.default.createElement("div",null,o.default.createElement(H,{className:c.button,theme:"secondary",onClick:e},g("commandTryAgain")))),Io=eo;export{eo as ConvertStakeAccountListPage,Io as default};
//# sourceMappingURL=ConvertStakeAccountListPage-OMMK2DUX.js.map
