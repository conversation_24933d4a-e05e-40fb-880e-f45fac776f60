import{a as c,c as d}from"./chunk-HKFEOKHC.js";import"./chunk-ZNZZRKNQ.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import{h as u}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import{a}from"./chunk-IVMV7P4T.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import{d as i}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import{j as p}from"./chunk-OKP6DFCI.js";import"./chunk-WIQ4WVKX.js";import"./chunk-MNXYIK2W.js";import"./chunk-WFPABEAU.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as l}from"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as y}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as w,h as n,n as m}from"./chunk-3KENBVE7.js";n();m();var o=w(y());var h=o.default.memo(({account:f,onCloseClick:t})=>{let{t:C}=l(),{pushDetailViewCallback:s}=u(),{addresses:r}=f,g=(0,o.useMemo)(()=>r.length>0?r.map(e=>{let k=s(o.default.createElement(d,{address:e.address,networkID:e.networkID,headerType:"page",onCloseClick:t}));return o.default.createElement("li",null,o.default.createElement(c,{key:`${e.networkID}-${e.addressType}`,chainAddress:e,onQRClick:k}))}):[],[r,t,s]);return o.default.createElement(i,{style:{display:"flex",flexDirection:"column",height:"100%"}},o.default.createElement(i,{element:"ul",gap:"list",style:{flex:1,overflowY:"auto",paddingBottom:"16px"}},g),o.default.createElement(a,null,o.default.createElement(p,{onClick:t},C("commandClose"))))}),M=h;export{h as ReceivePage,M as default};
//# sourceMappingURL=ReceivePage-PWVMHECE.js.map
