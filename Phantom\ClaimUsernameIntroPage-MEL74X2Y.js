import{A as De,B as Be,C as Le,D as z,E as je,F as Oe,H as We,a as he,b as ge,c as Ce,d as be,e as Se,f as xe,h as Ie,i as Ue,j as O,k as V,l as Ae,m as Te,n as Ne,o as ye,p as ke,q as Fe,r as Pe,s as we,t as Me,u as W,v as Z,w as _,x as Ee,y as Ve,z as He}from"./chunk-SYICDMYM.js";import"./chunk-ESXKWKRD.js";import{a as j,b as ve}from"./chunk-QZG7YQTK.js";import"./chunk-7A6HLO4U.js";import{Ma as Ze,ua as fe}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{b as ue}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import{h as k,n as E}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import{a as pe}from"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{W as me,a as f,ba as ce,c as K,e as a,i as ae,n as M,v as le}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{h as de}from"./chunk-OKP6DFCI.js";import"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{h as ie,i as te}from"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import{j as ne}from"./chunk-SLQBAOEK.js";import{$d as se,Bc as ee,Cc as oe,Kd as re,ve as T,xb as J,xc as L}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as I}from"./chunk-56SJOU6P.js";import{V as R}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as w}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as P,h as p,n as u}from"./chunk-3KENBVE7.js";p();u();var o=P(w());p();u();var i=P(w());p();u();var e=P(w());var io=({showHeader:n=!1})=>{let{t}=I(),{scrollContainerRef:r,scrollPosition:m}=Ce(),{data:s}=re(),{data:l}=se(),{pushDetailView:g}=k(),d=T(x=>x.username),U=T(x=>x.setIcon),{accountIcon:N,accountIndex:C}=(0,e.useMemo)(()=>{let Y=s?.findIndex(no=>no.identifier===l?.identifier)??0;return{accountIcon:s?.[Y]?.icon,accountIndex:Y}},[s,l]),{mutate:y}=te(),{data:b}=ie(),F=b?b.skinTone:ne.skinTone,[c,S]=(0,e.useState)(!1),[G,X]=(0,e.useState)(0),[A,B]=(0,e.useState)(N??J),Ke=(0,e.useCallback)(()=>{S(!0)},[]),$=(0,e.useCallback)(()=>{S(!1)},[]),qe=(0,e.useCallback)(x=>{B({unicode:x,imageUrl:void 0,type:"emoji"})},[]),Qe=(0,e.useCallback)(x=>{B({unicode:void 0,imageUrl:x,type:"image"})},[]),Ye=(0,e.useCallback)(x=>{y({skinTone:x}),$()},[$,y]),Q=L(),Re=(0,e.useCallback)(()=>{Q.onAvatarSaved(A.type==="image"?"collectible":A.type==="emoji"?"emoji":"default"),U(A),g(e.default.createElement(We,{showHeader:n}))},[Q,A,U,g,n]),eo=(0,e.useCallback)(()=>{B(J)},[B]),oo=(0,e.useMemo)(()=>[t("settingsEmojis"),t("collectiblesTab")],[t]);return{username:d,accountIndex:C,selectedAccount:l,currentSkinTone:F,onCollectibleSelect:Qe,onEmojiSelect:qe,onSave:Re,onRemove:eo,openSkinToneMenu:Ke,hideSkinToneMenu:$,onSkinToneSelect:Ye,previewIcon:A,scrollContainerRef:r,scrollPosition:m,selectedTabIndex:G,setSelectedTabIndex:X,skinToneMenuVisible:c,tabs:oo}},_e=({showHeader:n=!1})=>{let{t}=I(),{popDetailView:r}=k();return e.default.createElement("div",{className:V},n?e.default.createElement(E,{disableIconBackground:!0,onLeftButtonClick:r},e.default.createElement(ve,{numOfItems:6,currentIndex:2,maxVisible:5})):null,e.default.createElement("div",{className:z},e.default.createElement(a,{font:"title1Semibold",children:t("settingsClaimUsernameChooseAvatarTitle")})),e.default.createElement(to,{showHeader:n}))},to=({showHeader:n=!1})=>{let{t}=I(),{username:r,accountIndex:m,selectedAccount:s,currentSkinTone:l,onCollectibleSelect:g,onEmojiSelect:d,onSave:U,openSkinToneMenu:N,hideSkinToneMenu:C,onSkinToneSelect:y,previewIcon:b,scrollContainerRef:F,scrollPosition:c,selectedTabIndex:S,setSelectedTabIndex:G,skinToneMenuVisible:X,tabs:A}=io({showHeader:n});return e.default.createElement(e.default.Fragment,null,s&&e.default.createElement("div",{className:Ue},e.default.createElement(fe,{accountIndex:m,accountName:s.name,accountIcon:b,size:"medium"}),e.default.createElement(a,{className:je,children:`@${r}`})),e.default.createElement(he,{tabs:A,selectedIndex:S,setSelectedIndex:G},e.default.createElement("div",{className:Le},S===0&&e.default.createElement(xe,{currentSkinTone:l,isVisible:X,onClose:C,onClick:N,onSelect:y}))),e.default.createElement("div",{className:De,ref:F},e.default.createElement(ge,{tabs:A,selectedIndex:S},e.default.createElement("div",{className:Ne},e.default.createElement(Se,{onEmojiSelect:d,containerRef:F,scrollPosition:c,skinTone:l,previewIcon:b})),s&&e.default.createElement(be,{accountIdentifier:s.identifier,onCollectibleSelect:g,previewIcon:b}))),e.default.createElement("div",{className:He},e.default.createElement(pe,null,e.default.createElement("div",{className:f({width:"100%",paddingLeft:8,paddingRight:8,paddingBottom:8})},e.default.createElement(M,{disabled:!b.imageUrl&&!b.unicode,theme:"primary",className:f({width:"100%"}),onClick:U},t("commandSave"))))))};var ze=2,q=20,ro=/^[a-zA-Z0-9]+$/,so=i.default.memo(({available:n=!1,loading:t,error:r})=>{let m=!n||r,s=n&&!r;return t?i.default.createElement(de,{diameter:14,color:"#999999"}):!t&&s?i.default.createElement(K.CheckCircle,{size:14,color:"accentSuccess"}):!t&&m?i.default.createElement(K.XCircle,{size:14,color:"accentAlert"}):null}),ao=i.default.memo(({available:n=!1,loading:t,error:r,invalid:m})=>{let{t:s}=I(),l=!t&&!r;return m?i.default.createElement("div",{className:f({gap:8})},i.default.createElement(a,{font:"labelMedium",color:"accentAlert",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationErrorLine1")}),i.default.createElement(a,{font:"labelMedium",color:"accentAlert",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationErrorLine2",{minChar:ze,maxChar:q})})):r?i.default.createElement(a,{font:"labelMedium",color:"accentAlert",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationServerError")}):t?i.default.createElement(a,{font:"labelMedium",color:"textSecondary",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationLoading")}):l&&n?i.default.createElement(a,{font:"labelMedium",color:"accentSuccess",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationAvailable")}):l&&!n?i.default.createElement(a,{font:"labelMedium",color:"accentAlert",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationUnavailable")}):i.default.createElement(a,{font:"labelMedium",color:"textSecondary",className:f({textAlign:"left"}),children:s("settingsClaimUsernameValidationDefault")})}),Ge=({showHeader:n=!1})=>{let{t}=I(),r=T(c=>c.username),m=T(c=>c.setUsername),s=R(r,200),{data:l,isPending:g,isError:d}=ee(s),{pushDetailView:U,popDetailView:N}=k(),C=l,y=c=>c.length<ze||c.length>q?!1:ro.test(c),b=c=>{let S=c.target.value;S.startsWith("@")?m(S.slice(1)):m(S.trim())},F=()=>m("");return i.default.createElement("div",{className:V},n?i.default.createElement(E,{onLeftButtonClick:N},i.default.createElement(j,{numOfItems:6,currentIndex:1,maxVisible:5})):null,i.default.createElement("div",{className:z},i.default.createElement(a,{font:"heading3Semibold",children:t("settingsClaimUsernameTitle")}),i.default.createElement(a,{font:"caption",color:"textSecondary",children:t("settingsClaimUsernameHelperText")})),i.default.createElement("div",{className:ye},i.default.createElement("div",{className:Fe},i.default.createElement(ue,{backgroundColor:"#2A2A2A",className:Be,value:r?`@${r.trim()}`:"",type:"text",autoComplete:"off",placeholder:r?"":"@username",maxLength:q,onChange:b,borderRadius:"16px"}),r?i.default.createElement(le,{className:ke,icon:"X",size:16,onClick:F,backgroundColor:"bgRow",color:"textSecondary",label:"Clear"}):null),r?i.default.createElement("div",{className:Oe},i.default.createElement(so,{available:C,loading:g,error:!y(r)||d}),i.default.createElement(ao,{available:C,loading:g,error:d,invalid:!y(r)})):null),i.default.createElement(M,{className:O,theme:C===!1?"destructive":"primary",disabled:!C||!r,onClick:()=>U(i.default.createElement(_e,{showHeader:n}))},t("settingsClaimUsernameSaveAndContinue")))};p();u();var v=P(w()),Xe=({width:n=24,height:t=24})=>v.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:n,height:t,fill:"none"},v.default.createElement("g",{clipPath:"url(#a)"},v.default.createElement("circle",{cx:12,cy:12,r:10,fill:"#2EC08B"}),v.default.createElement("path",{fill:"#E2DFFE",d:"m18.273.42 1.105 2.78c.21.526.687.899 1.25.973l2.971.394c.28.037.328.422.065.527l-2.788 1.11c-.527.21-.901.687-.977 1.249l-.4 2.967c-.039.28-.424.328-.529.066l-1.105-2.78a1.565 1.565 0 0 0-1.25-.974l-2.97-.394c-.28-.037-.328-.422-.065-.527l2.787-1.109c.528-.21.902-.688.977-1.25l.401-2.967c.038-.28.424-.328.528-.065Z"}),v.default.createElement("circle",{cx:9.523,cy:9.738,r:1.075,fill:"#3C315B"}),v.default.createElement("circle",{cx:14.362,cy:9.738,r:1.075,fill:"#3C315B"}),v.default.createElement("path",{fill:"#3C315B",d:"M12 18c3.133 0 5.785-1.764 6.678-4.196.342-.931-.525-1.735-1.513-1.654-3.438.28-6.892.28-10.33 0-.988-.08-1.855.723-1.513 1.654C6.215 16.236 8.867 18 12 18Z"})),v.default.createElement("defs",null,v.default.createElement("clipPath",{id:"a"},v.default.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"}))));p();u();var h=P(w()),$e=({width:n=24,height:t=24})=>h.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:n,height:t,fill:"none"},h.default.createElement("g",{clipPath:"url(#a)"},h.default.createElement("circle",{cx:12,cy:13.44,r:9.56,fill:"#FFD13F",stroke:"#FF7243",strokeWidth:2}),h.default.createElement("path",{stroke:"#FF7243",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:1.5,d:"m8.16 9.6 3.92 3.84 2.8-2.743"}),h.default.createElement("circle",{cx:12,cy:13,r:1,fill:"#FF7243"}),h.default.createElement("path",{fill:"#FF7243",fillRule:"evenodd",d:"M14.38 0a.5.5 0 0 1 .5.5v.94a.48.48 0 0 1-.*********** 0 0 0-.48.48v.94a.5.5 0 0 1-.5.5h-2.84a.5.5 0 0 1-.5-.5V2.4a.48.48 0 0 0-.48-.48.48.48 0 0 1-.48-.48V.5a.5.5 0 0 1 .5-.5h4.76Zm6.197 3.666a.5.5 0 0 1 .136.694l-.435.646a.426.426 0 0 1-.592.116.426.426 0 0 0-.592.115l-.435.646a.5.5 0 0 1-.694.135l-.585-.394a.5.5 0 0 1-.136-.694l.436-.646a.426.426 0 0 0-.116-.592.426.426 0 0 1-.115-.591l.435-.646a.5.5 0 0 1 .694-.136l2 1.347Z",clipRule:"evenodd"}),h.default.createElement("path",{fill:"#FFFFC4",d:"m5.372 14.344.61 2.679c.116.507.497.912.996 1.06l2.64.776c.*************-.015.487l-2.686.613a1.444 1.444 0 0 0-1.063.996l-.783 2.635c-.073.249-.43.238-.488-.015l-.61-2.678a1.437 1.437 0 0 0-.996-1.06l-2.64-.777c-.25-.073-.238-.429.016-.487l2.685-.613a1.444 1.444 0 0 0 1.063-.995l.783-2.636c.074-.248.43-.237.488.015Z"})),h.default.createElement("defs",null,h.default.createElement("clipPath",{id:"a"},h.default.createElement("path",{fill:"#fff",d:"M0 0h24v24H0z"}))));p();u();var H=P(w()),Je=({width:n=24,height:t=24})=>H.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:n,height:t,fill:"none"},H.default.createElement("path",{fill:"#AB9FF2",d:"M4.374 20.07s-1.463-1.08-1.37-3.244c.093-2.164.737-4.03 1.42-5.424.683-1.393 1.135-3.144 1.135-4.33 0-1.185-.328-3.378 1.132-3.408 1.46-.03 1.49 3.765 1.18 5.366 0 0 1.087-2.254 1.366-4.003.28-1.75.809-2.646 1.696-2.624 1.307.031 1.15 4.152-.248 7.621 0 0 1.603-4.48 2.095-5.999.492-1.518.72-2.06 1.817-2.023 1.098.036 1.022 2.122.568 3.88-.455 1.757-1.742 4.684-1.742 4.684s1.044-2.108 3.693-1.629a2.02 2.02 0 0 1 1.417.994c.279.503.527 1.264.454 2.334-.151 2.24-.559 2.749-1.778 2.71-1-.032-1.084-2.215-1.174-2.858a.29.29 0 0 0-.29-.246c-.316.003-1.226.413-1.967 1.774-1.147 2.107-.773 3.835-.314 3.973.46.137 1.121.521 2.415-1.51 1.294-2.03 2.745-.765 2.53.33-.216 1.094-1.179 4.39-5.72 5.324-4.542.933-7.778-1.136-8.315-1.69v-.002Z"}),H.default.createElement("path",{fill:"#FFFDF8",d:"m3.678 5.771.325 1.734a.923.923 0 0 0 .612.704l1.675.565c.158.053.142.281-.022.312l-1.739.326a.927.927 0 0 0-.706.612l-.568 1.671c-.054.158-.283.142-.313-.021L2.617 9.94a.923.923 0 0 0-.612-.705L.33 8.671C.172 8.618.19 8.39.353 8.359l1.738-.326a.927.927 0 0 0 .707-.612l.568-1.672c.053-.157.282-.141.312.022Z"}),H.default.createElement("path",{fill:"#FFFFC4",d:"M23.51 15.621a.793.793 0 0 1-.95.703l-2.66-.774c-.274-.08-.206-.497.086-.525l2.819-.267a.172.172 0 0 1 .035 0 .777.777 0 0 1 .67.863ZM23.463 10.775a.793.793 0 0 1-.377 1.12l-2.623.89c-.27.092-.454-.29-.23-.48l2.156-1.833c.01-.008.019-.016.029-.02a.777.777 0 0 1 1.044.323Z"}),H.default.createElement("path",{fill:"#FFFDF8",d:"M6.26 17.152c-.31-1.108.413-2.183-.375-1.954-.788.23-1.176 1.314-.865 2.422.31 1.108 1.2 1.821 1.989 1.592.788-.229-.439-.951-.749-2.06Z",opacity:.5}));var lo=()=>{let{t:n}=I(),{pushDetailView:t}=k(),{handleHideModalVisibility:r}=Ze(),{data:m}=oe(),s=L(),l=self.innerHeight>me.height,g=T(C=>C.reset);(0,o.useEffect)(g,[g]);let[d,U]=(0,o.useState)(!!m?.username);(0,o.useEffect)(()=>{s.onClaimFlowStart()},[s]);let N=ae(Pe,l?Me:we);return o.default.createElement(o.default.Fragment,null,o.default.createElement("div",{className:V},o.default.createElement(ce,{onIntroComplete:()=>U(!0),hasUsername:d,isSidebar:l,className:N}),o.default.createElement("div",{className:Ie},o.default.createElement(E,{onLeftButtonClick:()=>r("claimUsername")},o.default.createElement(j,{numOfItems:6,currentIndex:0,maxVisible:5}))),o.default.createElement("div",{className:l?Te:Ae},o.default.createElement("div",{className:Ee,style:{opacity:d?1:0}},o.default.createElement(a,{font:"heading3Semibold",children:n("settingsClaimUsernameTitle")}),o.default.createElement(a,{font:"caption",color:"textSecondary",children:n("settingsClaimUsernameDescription")})),o.default.createElement("div",{className:Ve,style:{opacity:d?1:0}},o.default.createElement("div",{className:Z},o.default.createElement("div",{className:W},o.default.createElement(Xe,null)),o.default.createElement("div",{className:_},o.default.createElement(a,{font:"body",children:n("settingsClaimUsernameValueProp1")}),o.default.createElement(a,{font:"caption",color:"textSecondary",children:n("settingsClaimUsernameValueProp1Description")}))),o.default.createElement("div",{className:Z},o.default.createElement("div",{className:W},o.default.createElement($e,null)),o.default.createElement("div",{className:_},o.default.createElement(a,{font:"body",children:n("settingsClaimUsernameValueProp2")}),o.default.createElement(a,{font:"caption",color:"textSecondary",children:n("settingsClaimUsernameValueProp2Description")}))),o.default.createElement("div",{className:Z},o.default.createElement("div",{className:W},o.default.createElement(Je,null)),o.default.createElement("div",{className:_},o.default.createElement(a,{font:"body",children:n("settingsClaimUsernameValueProp3")}),o.default.createElement(a,{font:"caption",color:"textSecondary",children:n("settingsClaimUsernameValueProp3Description")}))))),o.default.createElement(M,{className:O,style:{opacity:d?1:0},disabled:!d,theme:"primary",onClick:()=>t(o.default.createElement(Ge,{showHeader:!0}))},n("settingsClaimUsernameContinue"))))},bn=lo;export{lo as ClaimUsernameIntroPage,bn as default};
//# sourceMappingURL=ClaimUsernameIntroPage-MEL74X2Y.js.map
