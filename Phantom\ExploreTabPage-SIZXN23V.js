import{a as wo}from"./chunk-5IF7UAPA.js";import{A as Ie,B as _o,C as Q,a as ko,b as de,c as So,d as D,e as Po,f as A,g as To,h as bo,i as Qo,j as Io,k as Lo,l as Bo,m as vo,n as Fo,o as Do,p as Ao,q as Uo,r as Oo,s as No,t as Ho,u as Mo,v as qo,w as Vo,x as W,y as be,z as Qe}from"./chunk-P6BZRSOH.js";import{a as Co}from"./chunk-DATME33X.js";import"./chunk-PDSYJ4DQ.js";import{a as m}from"./chunk-DZR774A2.js";import"./chunk-QZG7YQTK.js";import{E as j,Ia as se,Ja as Pe,La as Te,Ma as ho,S as Eo}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import{a as yo}from"./chunk-AHRYSG4W.js";import{a as _}from"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as fo}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{g as xo}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as Se}from"./chunk-CCQRCL2K.js";import{h as L,m as V}from"./chunk-75L54KUM.js";import{a as go}from"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{a as he,d as g,e as Ve}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import{a as q}from"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{a as we,c as uo,j as ke,l as _e}from"./chunk-OKP6DFCI.js";import{La as co,b as po,ga as mo,o as E,rb as H}from"./chunk-WIQ4WVKX.js";import{C as eo,F as G,G as ro,K as io,L as no,M as so,N as lo,O as ao}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import{c as oo,d as to}from"./chunk-QALJXKGR.js";import{c as Ze}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{k as Ye}from"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import{a as Ct}from"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as Je,Dd as Ke,Pa as Ee,Xa as Ce}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as b}from"./chunk-56SJOU6P.js";import{Q as ne,V as je,ma as We}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{Ya as ye}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as h}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as y,h as i,n}from"./chunk-3KENBVE7.js";i();n();var xt=y(Ct()),k=y(h());i();n();var U=y(h());var ht=120,Re=o=>"actionUrl"in o,wt=({item:o,index:e,datasourceId:r})=>{let p=(0,U.useCallback)(({item:l,index:s})=>{m.onExploreCarouselItemClickedByUser({datasourceId:r,carouselName:Re(l)?"transactions":"sites",itemDetails:{position:s,title:l.name}}),Re(l)||self.open(ne(l.url),"_blank","noopener noreferrer")},[r]);return U.default.createElement("div",{onClick:()=>p({item:o,index:e}),className:he({flex:1})},U.default.createElement(Ho,{item:o,image:U.default.createElement(yo,{className:Vo.image,src:Re(o)?o.icon:o.imageUrl}),index:e,onPress:p}))},Ro=U.default.memo(()=>{let{error:o,data:e,isPending:r,refetch:p}=eo(),l=e?.uuid??"",s=(0,U.useMemo)(()=>(e?.data??[]).map((a,d)=>({key:a.domain,node:U.default.createElement(wt,{item:a,index:d,datasourceId:l})})),[l,e?.data]);return o||e?.data.length===0?U.default.createElement(Mo,{onRetry:p}):r||typeof e>"u"?U.default.createElement(qo,null):U.default.createElement(g,{paddingX:"screen"},U.default.createElement(wo,{items:s,onIndexChange:We,itemHeight:ht}))});i();n();var re=y(h());i();n();var ee=y(h());i();n();var F=y(h());i();n();var Z=y(h());var Xo=Z.default.memo(()=>{let o=A(t=>t.networkOptions),e=A(t=>t.network),r=A(t=>t.setNetwork),p=A(t=>t.sortByOptions),l=A(t=>t.sortBy),s=A(t=>t.setSortBy),a=A(t=>t.timeFrameOptions),d=A(t=>t.timeFrame),x=A(t=>t.setTimeFrame);return Z.default.createElement(Z.default.Fragment,null,Z.default.createElement(Qe,{value:l,options:p,onChange:t=>{s(t),m.onExploreFilterChangedByUser({filterType:"sort",filterValue:t,listName:"collections"})}}),Z.default.createElement(be,{value:e,options:o,onChange:t=>{r(t),m.onExploreFilterChangedByUser({filterType:"network",filterValue:t,listName:"collections"})}}),Z.default.createElement(Ie,{value:d,options:a,onChange:t=>{x(t),m.onExploreFilterChangedByUser({filterType:"timeframe",filterValue:t,listName:"collections"})}}))});var kt=F.default.memo(({rankAlgo:o,limit:e})=>{let{t:r}=b(),p=A(c=>c.sortBy),l=A(c=>c.timeFrame),s=A(c=>c.network),{data:a,error:d,refetch:x,isLoading:t}=lo({sortBy:p,timeFrame:l,network:s,limit:e,rankAlgo:o}),P=({item:c,index:T,datasourceId:$=""})=>{m.onExploreListItemClickedByUser({datasourceId:$,listName:"collections",itemDetails:{position:T,title:c.name,id:c.id}}),self.open(c.marketplacePages[0].collectionUrl,"_blank","noopener noreferrer")},w=!!d||a?.data.length===0,v=t||typeof a>"u";return F.default.createElement(g,{direction:"column",flex:1},F.default.createElement(g,{paddingX:"screen",paddingTop:"screen"},F.default.createElement(V,null,r("exploreCollections"))),F.default.createElement(W,{isError:w,isLoading:v,refetch:x,filters:F.default.createElement(Xo,null),listHeader:F.default.createElement(Io,null),rows:(a?.data??[]).map((c,T)=>({...vo({item:c,image:F.default.createElement(Q,{index:T,displayRank:!0,image:F.default.createElement(j,{image:{type:"collectible",src:c.imageUrl,fallback:c.name||c.id},size:48})})}),onClick:()=>P({item:c,index:T})}))}))}),Le=F.default.memo(()=>{let o=G(),e=(0,F.useMemo)(()=>o.sections.find(r=>r.type==="collections"),[o.sections]);return e?F.default.createElement(Po,{sortByOptions:e.sortBy,sortBy:e.sortByDefault,networkOptions:e.network,network:e.networkDefault,timeFrameOptions:e.timeFrame,timeFrame:e.timeFrameDefault},F.default.createElement(kt,{rankAlgo:e.rankAlgo,limit:e.listLimit})):null});var zo=o=>{let{pushDetailView:e}=L(),r=(0,ee.useCallback)(()=>{m.onExploreSectionClickedByUser({listName:o.type}),e(ee.default.createElement(Le,null))},[e,o.type]);return ee.default.createElement(Fo,{...o,ErrorBoundary:q,onSeeMore:r,onRowPress:({item:l,index:s,datasourceId:a=""})=>{m.onExploreListItemClickedByUser({datasourceId:a,listName:"collections",itemDetails:{position:s,title:l.name,id:l.id}}),self.open(l.marketplacePages[0].collectionUrl,"_blank","noopener noreferrer")},ImageComponent:St})},St=({item:o,index:e})=>ee.default.createElement(Q,{index:e,displayMedallionBadge:!0,image:ee.default.createElement(j,{image:{type:"collectible",src:o.imageUrl,fallback:o.name||o.id},size:48})});i();n();var ae=y(h());i();n();var le=y(h());var Pt={learnArticle:"article",learnTip:"tip"},Be=()=>{let{t:o}=b(),{data:e,isPending:r,error:p,refetch:l}=ao(),s=se(),a=({item:t,index:P})=>{m.onExploreLearnItemClickedByUser({itemDetails:{position:P,id:t.id,title:t.title,type:Pt[t.type]}}),t.type==="learnTip"&&t.page?s({},{destinationType:t.page,url:t.url}):t.type==="learnArticle"&&self.open(t.url,"_blank","noopener noreferrer")},d=!!p||e?.data.length===0,x=r||typeof e>"u";return le.default.createElement(g,{direction:"column",flex:1},le.default.createElement(g,{paddingX:"screen",paddingTop:"screen"},le.default.createElement(V,null,o("exploreLearn"))),le.default.createElement(W,{isError:d,isLoading:x,refetch:l,rows:(e?.data??[]).map((t,P)=>({...Uo({item:t,image:le.default.createElement(Q,{image:t.type==="learnArticle"?t.backgroundImageUrl:t.icon})}),onClick:()=>a({item:t,index:P})}))}))};var Tt={learnArticle:"article",learnTip:"tip"},$o=o=>{let e=se(),{pushDetailView:r}=L(),p=(0,ae.useCallback)(()=>{m.onExploreSectionClickedByUser({listName:o.type}),r(ae.default.createElement(Be,null))},[r,o.type]);return ae.default.createElement(Oo,{...o,ErrorBoundary:q,onSeeMore:p,onRowPress:({item:s,index:a})=>{m.onExploreLearnItemClickedByUser({itemDetails:{position:a,id:s.id,title:s.title,type:Tt[s.type]}}),s.type==="learnTip"&&s.page?e({},{destinationType:s.page,url:s.url}):s.type==="learnArticle"&&self.open(s.url,"_blank","noopener noreferrer")},ImageComponent:bt})},bt=({item:o})=>ae.default.createElement(Q,{image:o.type==="learnArticle"?o.backgroundImageUrl:o.icon});i();n();var me=y(h());i();n();var ge=y(h());i();n();var B=y(h());i();n();var Go={image:"bobq3w0"};i();n();var I=y(h());var ve="14px",K="8px",ue="#484848",Qt=E(_)`
  width: 100%;
  height: 144px;
  border-top-left-radius: ${K};
  border-top-right-radius: ${K};
`,It=E(_).attrs({backgroundColor:"#484848",borderRadius:"8px"})``,Fe=E.div`
  background: #474747;
  height: 1px;
  opacity: 0.6;
  width: 100%;
`,jo=()=>I.default.createElement(_,{align:"center",width:"150px",height:"30px",backgroundColor:"#2D2D2D",borderRadius:K,margin:"0 auto 17px auto"}),Xe=()=>I.default.createElement(I.default.Fragment,null,I.default.createElement(_,{width:"100%",height:"308px",backgroundColor:"#2D2D2D",borderRadius:K,margin:"0 0 10px 0"},I.default.createElement(Se,null,I.default.createElement(Qt,{align:"flex-start",justify:"flex-end",margin:"0 0 10px"},I.default.createElement(It,{align:"flex-start",justify:"flex-end",width:"65px",height:"20px",margin:"10px 10px 0 0",borderRadius:"32px"})),I.default.createElement(Se,{padding:"16px"},I.default.createElement(_,{justify:"flex-start",width:"60px",height:ve,backgroundColor:ue,borderRadius:"8px",margin:"0 0 17px 0"}),I.default.createElement(_,{justify:"flex-start",width:"125px",height:ve,backgroundColor:ue,borderRadius:K})),I.default.createElement(Fe,null),I.default.createElement(fo,{padding:"16px"},I.default.createElement(_,{width:"40px",height:"40px",backgroundColor:ue,borderRadius:K}),I.default.createElement(Se,{width:"auto",margin:"0 0 0 10px"},I.default.createElement(_,{justify:"flex-start",width:"60px",height:ve,backgroundColor:ue,borderRadius:K,margin:"0 0 5px 0"}),I.default.createElement(_,{justify:"flex-start",width:"125px",height:ve,backgroundColor:ue,borderRadius:K}))))));i();n();var Ae=y(h());i();n();var u=y(h());i();n();i();n();i();n();var fe=class{constructor(e){this.onQuestSelected=e=>{this.#e.capture("questSelectedByUser",{data:{quest:e}})};this.onQuestCollectibleMinted=e=>{this.#e.capture("questCollectibleMinted",{data:{quest:e}})};this.onQuestRewardClaimed=e=>{this.#e.capture("questRewardClaimedByUser",{data:{quest:e}})};this.onQuestInterstitialPrimaryClick=e=>{this.#e.capture("questInterstitialPrimaryButtonClickedByUser",{data:{quest:e}})};this.onQuestInterstitialSecondaryClick=e=>{this.#e.capture("questInterstitialSecondaryButtonClickedByUser",{data:{quest:e}})};this.onQuestInterstitialDismiss=e=>{this.#e.capture("questInterstitialDismissedByUser",{data:{quest:e}})};this.onQuestPreviewUrlClick=(e,r)=>{this.#e.capture("questPreviewUrlClickedByUser",{data:{quest:e,url:r}})};this.#e=e}#e};i();n();var oe=new fe(Ze);i();n();var Wo=y(h()),Ko=({hideAnimation:o,...e})=>Wo.default.createElement(we.div,{whileHover:o?void 0:{scale:.97},transition:{ease:[.16,1,.3,1],duration:.4},...e});i();n();var M=y(h());var Lt=E.div`
  padding: 16px;
`,Bt=E.div`
  align-self: center;
  margin-right: 8px;
`,vt=E(ke).attrs({theme:"primary"})`
  align-self: center;
  height: 32px;
  max-width: 80px;
  padding: 8px 14px;
  width: auto;
`,Yo=({isQuestCompleted:o,questState:e,reward:r,onClickQuestRewardPreview:p,onClaimQuestReward:l})=>{let{t:s}=b(),a=e==="completed"||e==="claimed",d=s(e==="claimed"?"pastParticipleClaimed":"commandClaim");return M.default.createElement(Lt,null,M.default.createElement("div",{style:{display:"flex"}},M.default.createElement("div",{onClick:p,style:{cursor:o?"auto":"pointer",display:"flex",flexGrow:1,marginRight:16}},M.default.createElement(Bt,null,M.default.createElement(co,null)),M.default.createElement("div",null,M.default.createElement(H,{color:"#999",textAlign:"left",size:13,lineHeight:16},s("reward")),M.default.createElement(H,{textAlign:"left",size:14,weight:600,lineHeight:16},r.title))),a&&M.default.createElement(vt,{onClick:l,disabled:e==="claimed"},M.default.createElement(H,{color:"#222",weight:600,lineHeight:16,size:13},d))))};i();n();var R=y(h());i();n();var xe=y(h());var Zo=({children:o,lineOne:e,lineTwo:r})=>xe.createElement(Eo,{content:xe.createElement(Ft,{onClick:l=>{l.stopPropagation()}},xe.createElement(Jo,null,e),r&&xe.createElement(Jo,null,r)),alignment:"left",index:0},o),Ft=E.div`
  display: flex;
  cursor: default;
  flex-direction: column;
  gap: 6px;
  margin-top: 8px;
  padding: 12px 16px;
  min-width: 226px;
  width: auto;
`,Jo=E(H).attrs({lineHeight:17,size:14,textAlign:"start"})``;var rt=({text:o,color:e,tooltip:r})=>r?R.default.createElement(et,null,R.default.createElement(Zo,{...r},R.default.createElement(ot,null,R.default.createElement(tt,{color:e},o),R.default.createElement(mo,{width:16,fill:e,"data-testid":"quest-status-badge-info-icon"})))):R.default.createElement(et,null,R.default.createElement(ot,null,R.default.createElement(tt,{color:e},o))),et=E.div`
  position: absolute;
  top: 8px;
  right: 8px;
`,ot=E.div`
  display: flex;
  flex-direction: row;
  gap: 4px;
  background: rgba(34, 34, 34, 0.8);
  backdrop-filter: blur(2px);
  border-radius: 32px;
  padding: 4px 8px;
`,tt=E(H)`
  color: ${o=>o.color};
  font-size: 13px;
  font-weight: 600;
  line-height: normal;
`;var De=16,it=19,Dt=E(Ko)`
  border-radius: ${De}px;
  cursor: ${o=>o.isDisabled||o.isPreview?"auto":"pointer"};
  margin-bottom: 24px;
  position: relative;
  background: #2a2a2a;
`,At=E.div`
  background-color: rgba(44, 45, 48, 0.5);
  border-radius: ${De}px;
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
`,Ut=E.div`
  align-items: center;
  border-top-left-radius: ${De}px;
  border-top-right-radius: ${De}px;
  display: flex;
  height: 144px;
  justify-content: center;
  overflow: hidden;
`,Ot=E.img`
  max-width: 100%;
`,Nt=E.div`
  padding: 16px;
`,Ht=E(H)`
  margin-bottom: 8px;
`,Mt=E(H)``,qt=E.img`
  margin-top: 32px;
  max-width: 94px;
  max-height: 94px;
`,Vt=E(go).attrs({diameter:32})`
  background-color: transparent;
  border: 1px solid #333333;
`,_t=o=>{let{t:e}=b(),{id:r,titleShort:p,networkIds:l,interstitial:s,reward:a,isEligible:d,isPreview:x,previewUrl:t,questState:P}=o,{handleShowModalVisibility:w,handleHideModalVisibility:v}=ho(),c=se(),T=u.useMemo(()=>["claimed","completed_unclaimable","completed"].includes(P),[P]),$=u.useCallback(J=>{if(x&&t!=null){oe.onQuestPreviewUrlClick({questId:r,shortName:p},t),c(J,{destinationType:"External Link",url:t});return}if(T||s==null)return;let{destinationType:Ne,url:He,lineItems:Me,primaryButtonText:ie,secondaryButtonText:qe,title:gt}=s,yt=ie.length>it||qe.length>it;oe.onQuestSelected({questId:r,shortName:p}),w("interstitial",{bodyTitle:gt,details:Me.map((Ge,Et)=>({icon:u.createElement(Vt,null,u.createElement(H,{size:14},Et+1)),title:Ge.title,subtitle:Ge.description})),icon:d?s.imageUrl:u.createElement(qt,{src:s.imageUrl}),onDismiss:()=>{oe.onQuestInterstitialDismiss({questId:r,shortName:p})},FooterComponent:()=>u.createElement(_e,{vertical:yt,buttons:[{text:qe,onClick:()=>{oe.onQuestInterstitialSecondaryClick({questId:r,shortName:p,buttonText:qe}),v("interstitial")}},{text:ie,theme:"primary",onClick:()=>{oe.onQuestInterstitialPrimaryClick({questId:r,shortName:p,buttonText:ie}),c(J,{destinationType:Ne,url:He}),v("interstitial")}}]})})},[x,t,T,s,r,p,w,d,c,v]),C=u.useCallback(J=>{if(T||a==null||a.hasClaimed)return;J.stopPropagation();let{title:Ne,description:He,imageUrl:Me,link:ie}=a;w("interstitial",{bodyTitle:Ne,bodyDescription:He,icon:Me,FooterComponent:()=>ie?u.createElement(_e,{buttons:[{text:e("commandDismiss"),onClick:()=>{v("interstitial")}},{text:"View Terms",theme:"primary",onClick:()=>{self.open(ie,"_blank")}}]}):u.createElement(ke,{onClick:()=>v("interstitial")},e("commandDismiss"))})},[T,w,v,a,e]),Y=u.useCallback(()=>{a==null||a.hasClaimed||(oe.onQuestRewardClaimed({questId:r,shortName:p}),w("claimReward",{questId:r,networkIds:l,onPressDismiss:()=>v("claimReward")}))},[a,r,l,w,v,p]);return u.useMemo(()=>({...o,isCompleted:T,onClickQuestRewardPreview:C,onClaimQuestReward:Y,onClickQuest:$}),[T,o,$,C,Y])},Rt=u.memo(o=>{let{titleLong:e,imageUrl:r,description:p,isCompleted:l,reward:s,badge:a,isPreview:d,questState:x,onClickQuest:t,onClickQuestRewardPreview:P,onClaimQuestReward:w}=o;return u.createElement(Dt,{onClick:t,isDisabled:l,isPreview:d,"data-testid":"quest-list-item"},u.createElement(Ut,null,u.createElement(Ot,{src:r}),a&&u.createElement(rt,{...a}),d&&u.createElement(At,null)),u.createElement(Fe,null),u.createElement(Nt,null,u.createElement(Ht,{textAlign:"left",size:16,weight:500,lineHeight:19},e),u.createElement(Mt,{color:"#999",textAlign:"left",weight:400,lineHeight:17,size:14},p)),s&&u.createElement(u.Fragment,null,u.createElement(Fe,null),u.createElement(Yo,{isQuestCompleted:l,questState:x,reward:s,onClickQuestRewardPreview:P,onClaimQuestReward:w})))}),nt=o=>{let e=_t(o);return u.createElement(Rt,{...e})};var st=({quests:o})=>Ae.default.createElement(Ae.default.Fragment,null,o.map(e=>Ae.default.createElement(nt,{key:e.id,...e})));var Xt=()=>{let{data:o=[],isPending:e,error:r}=oo(),{data:p}=Je(),{data:l}=Ye();return(0,B.useMemo)(()=>({shouldShowQuests:!l&&p?.type!=="readOnly"&&!r,quests:o,isPending:e}),[p,o,l,e,r])},zt=B.default.memo(({quests:o,isPending:e})=>{let{t:r}=b();return e?B.default.createElement("div",null,B.default.createElement(jo,null),B.default.createElement(Xe,null),B.default.createElement(Xe,null)):o.length===0?B.default.createElement(g,{flex:1,alignItems:"center",justifyContent:"center",padding:"screen"},B.default.createElement("img",{src:"/images/quests/no-quests.webp",className:Go.image}),B.default.createElement(Ve,{font:"title2",color:"textPrimary",marginBottom:8},r("questsNoQuestsAvailable")),B.default.createElement(Ve,{align:"center",font:"body",color:"textSecondary"},r("questsNoQuestsAvailableDescription"))):B.default.createElement("div",null,B.default.createElement(st,{quests:o}))}),lt=()=>{let{shouldShowQuests:o,...e}=Xt();return o?B.default.createElement(g,{display:"flex",direction:"column",flex:1,padding:"screen",paddingTop:0,"data-testid":"quests"},B.default.createElement(zt,{...e})):null};var pe=()=>{let{t:o}=b();return ge.default.createElement(g,{direction:"column",flex:1},ge.default.createElement(g,{paddingX:"screen",paddingTop:"screen"},ge.default.createElement(V,null,o("exploreQuests"))),ge.default.createElement(lt,null))};var at=o=>{let{pushDetailView:e}=L(),r=(0,me.useCallback)(()=>{m.onExploreSectionClickedByUser({listName:o.type}),e(me.default.createElement(pe,null))},[e,o.type]);return me.default.createElement(No,{...o,ErrorBoundary:q,onSeeMore:r,onRowPress:({item:l,index:s})=>{m.onExploreListItemClickedByUser({datasourceId:"",listName:"quests",itemDetails:{position:s,title:l.titleShort,id:l.id}}),r()},ImageComponent:$t})},$t=({item:o})=>me.default.createElement(Q,{image:o.imageUrl});i();n();var ce=y(h());i();n();var O=y(h());i();n();var te=y(h());var pt=te.default.memo(()=>{let o=D(t=>t.networkOptions),e=D(t=>t.network),r=D(t=>t.setNetwork),p=D(t=>t.sortByOptions),l=D(t=>t.sortBy),s=D(t=>t.setSortBy),a=D(t=>t.timeFrameOptions),d=D(t=>t.timeFrame),x=D(t=>t.setTimeFrame);return te.default.createElement(te.default.Fragment,null,te.default.createElement(Qe,{value:l,options:p,onChange:t=>{s(t),m.onExploreFilterChangedByUser({filterType:"sort",filterValue:t,listName:"sites"})}}),te.default.createElement(be,{value:e,options:o,onChange:t=>{r(t),m.onExploreFilterChangedByUser({filterType:"network",filterValue:t,listName:"sites"})}}),te.default.createElement(Ie,{value:d,options:a,onChange:t=>{x(t),m.onExploreFilterChangedByUser({filterType:"timeframe",filterValue:t,listName:"sites"})}}))});var Gt=O.default.memo(({rankAlgo:o,limit:e})=>{let{t:r}=b(),p=D(c=>c.sortBy),l=D(c=>c.timeFrame),s=D(c=>c.network),{data:a,error:d,isLoading:x,refetch:t}=so({sortBy:p,timeFrame:l,rankAlgo:o,limit:e,network:s}),P=({item:c,index:T,datasourceId:$=""})=>{m.onExploreListItemClickedByUser({datasourceId:$,listName:"sites",itemDetails:{position:T,title:c.name,id:c.domain}}),self.open(ne(c.url),"_blank","noopener noreferrer")},w=!!d||a?.data.length===0,v=x||typeof a>"u";return O.default.createElement(g,{direction:"column",flex:1},O.default.createElement(g,{paddingX:"screen",paddingTop:"screen"},O.default.createElement(V,null,r("exploreSites"))),O.default.createElement(W,{isError:w,isLoading:v,refetch:t,filters:O.default.createElement(pt,null),rows:(a?.data??[]).map((c,T)=>({...Do({item:c,image:O.default.createElement(Q,{image:c.imageUrl,index:T,displayRank:!0})}),onClick:()=>P({item:c,index:T})}))}))}),Ue=O.default.memo(()=>{let o=G(),e=(0,O.useMemo)(()=>o.sections.find(r=>r.type==="sites"),[o.sections]);return e?O.default.createElement(So,{sortByOptions:e.sortBy,sortBy:e.sortByDefault,networkOptions:e.network,network:e.networkDefault,timeFrameOptions:e.timeFrame,timeFrame:e.timeFrameDefault},O.default.createElement(Gt,{rankAlgo:e.rankAlgo,limit:e.listLimit})):null});var mt=o=>{let{pushDetailView:e}=L(),r=(0,ce.useCallback)(()=>{m.onExploreSectionClickedByUser({listName:o.type}),e(ce.default.createElement(Ue,null))},[e,o.type]);return ce.default.createElement(Ao,{...o,ErrorBoundary:q,onSeeMore:r,onRowPress:({item:l,index:s,datasourceId:a=""})=>{m.onExploreListItemClickedByUser({datasourceId:a,listName:"sites",itemDetails:{position:s,title:l.name,id:l.domain}}),self.open(ne(l.url),"_blank","noopener noreferrer")},ImageComponent:jt})},jt=({item:o,index:e})=>ce.default.createElement(Q,{image:o.imageUrl,index:e,displayMedallionBadge:!0});i();n();var X=y(h());i();n();var S=y(h());var Kt=S.default.memo(({rankAlgo:o,limit:e})=>{let{t:r}=b(),p=de(C=>C.sortBy),l=de(C=>C.sortDirection),s=de(C=>C.timeFrame),a=de(C=>C.network),{data:[d]}=ye(["enable-unified-token-pages"]),{data:x,error:t,isLoading:P,refetch:w}=io({sortBy:p,sortDirection:l,timeFrame:s,limit:e,rankAlgo:o,network:a}),{pushDetailView:v}=L(),c=(0,S.useCallback)(({item:C,index:Y})=>{let J=Ce({chainId:C.chainId,address:C.address,resourceType:"address"});m.onExploreListItemClickedByUser({datasourceId:"",listName:"tokens",context:"explore",itemDetails:{position:Y,title:C.name||C.symbol,id:`${C.address}-${C.chainId}`}}),v(d?S.default.createElement(Pe,{caip19:J,title:C.name??void 0,entryPoint:"explore"}):S.default.createElement(Te,{caip19:J,title:C.name??void 0,entryPoint:"explore"}))},[v,d]),T=!!t||x?.items.length===0,$=P||typeof x>"u";return S.default.createElement(g,{direction:"column",flex:1},S.default.createElement(g,{paddingX:"screen",paddingTop:"screen"},S.default.createElement(V,null,r("exploreTokens"))),S.default.createElement(W,{isError:T,isLoading:$,refetch:w,filters:S.default.createElement(_o,null),listHeader:S.default.createElement(Qo,null),rows:(x?.items??[]).map((C,Y)=>({...Lo({item:C,sortBy:p,image:S.default.createElement(Q,{index:Y,displayRank:!0,image:S.default.createElement(j,{image:{type:"fungible",src:C.logoUrl,fallback:C.symbol||C.name},size:48,badge:{type:"network",preset:Ee.getChainID(C.chainId)}})})}),onClick:()=>c({item:C,index:Y})}))}))}),Oe=S.default.memo(()=>{let o=G(),e=(0,S.useMemo)(()=>o.sections.find(r=>r.type==="tokens"),[o.sections]);return e?S.default.createElement(ko,{sortByOptions:e.sortBy,sortBy:e.sortByDefault,sortDirection:e.sortDirectionDefault,networkOptions:e.network,network:e.networkDefault,timeFrameOptions:e.timeFrame,timeFrame:e.timeFrameDefault},S.default.createElement(Kt,{rankAlgo:e.rankAlgo,limit:e.listLimit})):null});var ct=o=>{let{data:[e]}=ye(["enable-unified-token-pages"]),{pushDetailView:r}=L(),p=(0,X.useCallback)(()=>{m.onExploreSectionClickedByUser({listName:o.type}),r(X.default.createElement(Oe,null))},[r,o.type]);return X.default.createElement(Bo,{...o,ErrorBoundary:q,onSeeMore:p,onRowPress:({item:s,index:a})=>{let d=Ce({chainId:s.chainId,address:s.address,resourceType:"address"});m.onExploreListItemClickedByUser({datasourceId:"",listName:"tokens",context:"explore",itemDetails:{position:a,title:s.name||s.symbol,id:`${s.address}-${s.chainId}`}}),r(e?X.default.createElement(Pe,{caip19:d,title:s.name??void 0,entryPoint:"explore"}):X.default.createElement(Te,{caip19:d,title:s.name??void 0,entryPoint:"explore"}))},ImageComponent:Yt})},Yt=({item:o,index:e})=>X.default.createElement(Q,{index:e,displayMedallionBadge:!0,image:X.default.createElement(j,{image:{type:"fungible",src:o.logoUrl,fallback:o.symbol||o.name},size:48,badge:e>=3?{type:"network",preset:Ee.getChainID(o.chainId)}:void 0})});var dt=re.default.memo(({section:o})=>{switch(o.type){case"tokens":return re.default.createElement(ct,{...o});case"sites":return re.default.createElement(mt,{...o});case"collections":return re.default.createElement(zo,{...o});case"quests":return re.default.createElement(at,{...o});case"learn":return re.default.createElement($o,{...o});default:return null}});i();n();var z=y(h());i();n();var N=y(h()),Jt=16,Zt=he({display:"flex",flexDirection:"row",gap:8,paddingX:"screen",cursor:{base:"default",hover:"pointer"}});function ze({values:o}){let e=(0,N.useRef)(null),[r,p]=(0,N.useState)(null),[l,s]=(0,N.useState)(0);(0,N.useEffect)(()=>{e.current&&s(e.current.scrollWidth)},[o]);let a=uo(0),d=(0,N.useMemo)(()=>{let x=self.innerWidth-l-Jt;return Math.min(0,x)},[l]);return N.default.createElement("div",{ref:e},N.default.createElement(we.div,{className:Zt,drag:"x",style:{x:a},dragConstraints:{left:d,right:0},onDragStart:()=>{p(a.get())},onDragEnd:()=>{p(null)},onWheel:x=>{if(x.deltaX===0)return;let t=a.get()-x.deltaX;return t>0?a.set(0):t<d?a.set(d):a.set(t)}},o.map(({key:x,onClick:t,node:P})=>N.default.createElement("div",{key:x,onClick:w=>{if(r!==null){w.stopPropagation(),w.preventDefault();return}t()}},P))))}var er={tokens:{navigationPage:z.default.createElement(Oe,null)},sites:{navigationPage:z.default.createElement(Ue,null)},collections:{navigationPage:z.default.createElement(Le,null)},quests:{navigationPage:z.default.createElement(pe,null)},learn:{navigationPage:z.default.createElement(Be,null)}},ut=()=>{let o=ro(),{pushDetailView:e}=L();return z.default.createElement(g,null,z.default.createElement(ze,{values:o.map(r=>({node:z.default.createElement(To,{type:r.type}),key:r.type,onClick:()=>{m.onExploreShortcutClickedByUser({listName:r.type}),e(er[r.type].navigationPage)}}))}))};i();n();var $e=y(h());var ft=({value:o,isLoading:e,onChange:r})=>{let{t:p}=b();return $e.default.createElement(or,null,$e.default.createElement(xo,{placeholder:p("dappBrowserExtSearchPlaceholder"),value:o,onChange:l=>{"value"in l.target&&typeof l.target.value=="string"&&r(l.target.value)},showClearIcon:!!o,showLoadingIcon:e,onClear:()=>{r("")}}))},or=E.div`
  padding: 16px;
  padding-bottom: 0;
`;var tr=15,rr=(0,xt.default)((o,e)=>{o&&m.searchedByUser({searchTerm:o,uiContext:e})},1e3),ir=({section:o})=>o.type==="shortcuts"?k.default.createElement(g,{marginBottom:16},k.default.createElement(ut,null)):o.type==="carousel"?k.default.createElement(Ro,null):o.type==="tokens"||o.type==="sites"||o.type==="collections"||o.type==="learn"||o.type==="quests"?k.default.createElement(g,{paddingX:"screen",marginBottom:20},k.default.createElement(dt,{section:o})):null,nr=()=>{let o=G(),e=po(),{pushDetailView:r}=L(),{data:p}=to(),[l,s]=(0,k.useState)(""),a=je(l,250),d=Ke(),x=no({searchQuery:a,networkIds:d,minSearchResults:tr,excludePlaceholderTokens:!0}),{showSpinner:t,showSearchResults:P}=x;return(0,k.useEffect)(()=>{rr(a,"explore")},[a]),(0,k.useEffect)(()=>{e.state?.page==="quests"&&p&&r(k.default.createElement(pe,null))},[e.state?.page,p,r]),k.default.createElement(k.default.Fragment,null,k.default.createElement(ft,{value:l,onChange:s,isLoading:t}),P?k.default.createElement(Co,{...x,entryPoint:"explore",searchQuery:l}):k.default.createElement(g,{direction:"column",marginY:"screen"},o.sections.map(w=>k.default.createElement(g,{key:w.type},ir({section:w}))),k.default.createElement(g,{paddingX:"screen",paddingBottom:"screen"},k.default.createElement(bo,null))))},qa=nr;export{qa as default};
//# sourceMappingURL=ExploreTabPage-SIZXN23V.js.map
