import{a as P}from"./chunk-LDMZMUWY.js";import{a as l}from"./chunk-7X4NV6OJ.js";import{f as i,h as c,n as m}from"./chunk-3KENBVE7.js";c();m();var w=i(P()),e=i(l()),b=500,p=({itemSize:t,margin:r,horizontalPadding:s})=>{let n=u({itemSize:t,margin:r,horizontalPadding:s}),[a,d]=(0,e.useState)(n),o=(0,e.useRef)((0,w.default)(()=>{let R=u({itemSize:t,margin:r,horizontalPadding:s});d(R)},b)).current;return(0,e.useLayoutEffect)(()=>(self.addEventListener("resize",o),()=>{self.removeEventListener("resize",o),o.cancel()}),[o,t]),a},u=({itemSize:t,margin:r,horizontalPadding:s})=>{let n=Math.floor((self.innerWidth-s+r)/(t+r));return Math.max(n,1)};export{p as a};
//# sourceMappingURL=chunk-ESXKWKRD.js.map
