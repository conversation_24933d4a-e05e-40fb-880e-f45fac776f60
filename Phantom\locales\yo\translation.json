{"commandAdd": "Ṣàfikún", "commandAccept": "Gbàá", "commandApply": "<PERSON> lò", "commandApprove": "Fọwọ́ sí", "commandAllow": "Gbàáláàyè", "commandBack": "Padà", "commandBuy": "Ràá", "commandCancel": "<PERSON><PERSON><PERSON>", "commandClaim": "Gba èrè", "commandClaimReward": "Gba èrè rẹ", "commandClear": "<PERSON> kúr<PERSON>", "commandClose": "<PERSON><PERSON><PERSON><PERSON>", "commandConfirm": "Jẹ́rìsí", "commandConnect": "Sopọ̀", "commandContinue": "Tẹ̀síwájú", "commandConvert": "Yipadà", "commandCopy": "<PERSON><PERSON><PERSON><PERSON>", "commandCopyAddress": "Da àdírẹ́sì kọ", "commandCopyTokenAddress": "Da àdírẹ́sì tókìnì kọ", "commandCreate": "Ṣẹ̀dá", "commandCreateTicket": "Ṣẹ̀dá Àwáwí", "commandDeny": "Kọ̀ọ́", "commandDismiss": "<PERSON><PERSON> <PERSON>́ kúrò", "commandDontAllow": "Má gbàáláàyè", "commandDownload": "Sọ̀káalẹ̀", "commandEdit": "Ṣàtúnṣe", "commandEditProfile": "Tún Profaili ṣe", "commandEnableNow": "Mú ṣiṣẹ́ Nísìnyí", "commandFilter": "Wáa", "commandFollow": "Tẹ̀lé e", "commandHelp": "Ìrànlọ́wọ́", "commandLearnMore": "Kọ́ ẹ̀kọ́ síwájú si", "commandLearnMore2": "Kọ́ ẹ̀kọ́ Síwájú si", "commandMint": "Ṣẹ́dá", "commandMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si", "commandNext": "Èyítí ó kàn", "commandNotNow": "<PERSON><PERSON>í <PERSON> Nísìny<PERSON>", "commandOpen": "Ṣi", "commandOpenSettings": "Ṣí àwọn <PERSON>", "commandPaste": "Lẹ̀ẹ́mọ", "commandReceive": "Gbàá", "commandReconnect": "Tunsopọ̀mọ", "commandRecordVideo": "Gba fidio sílẹ̀", "commandRequest": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandRetry": "Tún gbì<PERSON>ànjú", "commandReview": "Tunyẹ̀wò", "commandRevoke": "<PERSON><PERSON><PERSON>", "commandSave": "Fipamọ́", "commandScanQRCode": "<PERSON>ù QR", "commandSelect": "Yà<PERSON>-<PERSON>n", "commandSelectMedia": "<PERSON> Ọ̀nà láti <PERSON>", "commandSell": "Tàá", "commandSend": "Firánṣẹ́", "commandShare": "Ṣe àj<PERSON><PERSON><PERSON>", "commandShowBalance": "Fi Iye tí ó kù hàn", "commandSign": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSignOut": "Sign Out", "commandStake": "<PERSON>ow<PERSON> si", "commandMintLST": "Ṣẹ́dá JitoSOL", "commandSwap": "Pààrọ̀", "commandSwapAgain": "Pààrọ̀ Lẹ́ẹ̀kansi", "commandTakePhoto": "<PERSON>", "commandTryAgain": "Tún gbìyànjú <PERSON>", "commandViewTransaction": "Wo Ìdúnàádúrà", "commandReportAsNotSpam": "Jábọ̀ bíi ìfiráńṣẹ́ tí a béèrè fún", "commandReportAsSpam": "Jábọ̀ bíi ìfiráńṣẹ́ tí a kò bèèrè fún", "commandPin": "Lẹ̀ẹ́ mọ", "commandBlock": "<PERSON><PERSON><PERSON><PERSON>", "commandUnblock": "<PERSON><PERSON> d<PERSON>", "commandUnstake": "<PERSON><PERSON> owó kúrò", "commandUnpin": "<PERSON><PERSON><PERSON><PERSON> kúrò", "commandHide": "Fipamọ́", "commandUnhide": "<PERSON><PERSON><PERSON><PERSON> kúrò ní ìpamọ́", "commandBurn": "<PERSON><PERSON>", "commandReport": "Jábọ̀", "commandView": "<PERSON><PERSON><PERSON>", "commandProceedAnywayUnsafe": "Tẹ̀síwájú lọ́nàkọnà (tí ó léwu)", "commandUnfollow": "Máṣe tẹ̀lé e", "commandUnwrap": "Tu", "commandConfirmUnsafe": "Jẹ́rìsí (tí <PERSON> léwu)", "commandYesConfirmUnsafe": "Bẹ́ẹ̀ni, jẹ́rìsí (tí <PERSON> léwu)", "commandConfirmAnyway": "Jẹ́rìsíi lọ́nàkọnà", "commandReportIssue": "Jábọ̀ Ìṣòro kan", "commandSearch": "Ṣàwárí", "commandShowMore": "<PERSON> púpọ̀ hàn", "commandShowLess": "<PERSON> díẹ̀ hàn", "pastParticipleClaimed": "Ti gba èrè", "pastParticipleCompleted": "Ti parí", "pastParticipleCopied": "<PERSON><PERSON>", "pastParticipleDone": "Ó ti parí", "pastParticipleDisabled": "Ti mu má ṣiṣẹ́", "pastParticipleRequested": "<PERSON><PERSON> b<PERSON><PERSON>", "nounName": "Orúkọ", "nounNetwork": "Nẹtiwọki", "nounNetworkFee": "Owó Nẹtiwọki", "nounSymbol": "Àmì", "nounType": "<PERSON><PERSON><PERSON>", "nounDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nounYes": "Bẹ́ẹ̀ni", "nounNo": "Bẹ́ẹ̀kọ́", "amount": "<PERSON><PERSON>", "limit": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON><PERSON>", "gotIt": "Ti gbàá", "internal": "Tinú", "reward": "<PERSON><PERSON><PERSON>", "seeAll": "Wo gbogbo rẹ̀", "seeLess": "Wo díẹ̀", "viewAll": "Wo gbogbo rẹ̀", "homeTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesTab": "<PERSON>w<PERSON>n àgbà sílẹ̀", "swapTab": "Pààrọ̀", "activityTab": "Iṣẹ́-ṣíṣe", "exploreTab": "Wíw<PERSON><PERSON>", "accountHeaderConnectedInterpolated": "A ti gbà ẹ́ wọlé si {{orísun}}", "accountHeaderConnectedToSite": "A ti gbà ẹ́ wọlé si orí saiti yí", "accountHeaderCopyToClipboard": "Ṣe ẹ̀dà ẹ sori clipboard", "accountHeaderNotConnected": "A kò ti gbà ẹ́ wọlé sí", "accountHeaderNotConnectedInterpolated": "A kò ti gbà ẹ́ wọlé si {{orísun}}", "accountHeaderNotConnectedToSite": "A kò ti gbà ẹ́ wọlé si orí saiti yí", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "SOL tí kò tó", "accountWithoutEnoughSolSecondaryText": "Ibi-ìpamọ́ kan tí ó lọ́wọ́sí ìdúnàádúrà yìí kò ní SOL tí ó tó. Ibi-ìpamọ́ náà lè jẹ́ tìrẹ tàbí ti ẹlòmíràn. Ìdúnàádúrà yìí yóò padà bí a bá ti filélẹ̀.", "accountSwitcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>-<PERSON><PERSON> padà", "addAccountHardwareWalletPrimaryText": "So <PERSON>un èlò <PERSON>́wọ́ pọ̀mọ", "addAccountHardwareWalletSecondaryText": "Lo Ìwe àkọọ́lẹ̀ ohun èlò àpamọ́wọ́ rẹ", "addAccountHardwareWalletSecondaryTextMobile": "<PERSON>́wọ́ {{supportedHardwareWallets}} rẹ", "addAccountSeedVaultWalletPrimaryText": "So Ọ̀rọ̀ ìgbaniwọlé pọ̀mọ́ Ibi ìfi nǹkan pamọ́ sí", "addAccountSeedVaultWalletSecondaryText": "<PERSON> àpamọ́wọ́ kan láti Ọ̀rọ̀ ìgbaniwọlé Ibi ìfi nǹkan pamọ́ sí", "addAccountImportSeedPhrasePrimaryText": "Gbé Àṣírí G<PERSON>hùn Ọ̀rọ̀ ìgbaniwọlé kúkurú wọlé", "addAccountImportSeedPhraseSecondaryText": "<PERSON><PERSON><PERSON> àwọn ibi-ìpamọ́ láti àpamọ́wọ́ mìíràn wọlé", "addAccountImportWalletPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wọlé", "addAccountImportWalletSecondaryText": "G<PERSON>é ibi-ìpamọ́ oní-ṣéènì kan wọlé", "addAccountImportWalletSolanaSecondaryText": "<PERSON><PERSON><PERSON> k<PERSON>́k<PERSON><PERSON><PERSON><PERSON><PERSON> aládàáni <PERSON> kan wọlé", "addAccountLimitReachedText": "O ti dé gbèd<PERSON>ke ibi-ìpamọ́ {{accountsCount}} ní Phantom. Jọ̀wọ́ yọ àwọn ibi-ìpamọ́ kúrò ṣáájú fífi àwọn àfikún kún.", "addAccountNoSeedAvailableText": "O kò ní gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó wà. Jọ̀wọ́ gbé ọ̀rọ̀ ìgbaniwọlé tí ó ti wà tẹ́lẹ̀ wọlé láti ṣẹ̀dá ibi-ìpamọ́ kan.", "addAccountNewWalletPrimaryText": "Ṣẹ̀dá Ibi-<PERSON>pa<PERSON>ọ́ <PERSON>", "addAccountNewWalletSecondaryText": "Gbé adirẹsi àpamọ́wọ́ tuntun jáde", "addAccountNewMultiChainWalletSecondaryText": "Fi ibi-ìpamọ́ tuntun èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́ kan kun", "addAccountNewSingleChainWalletSecondaryText": "Fi ibi-ìpamọ́ tuntun kan kun", "addAccountPrimaryText": "Ṣàfikún / Sopọ̀ mọ́ Àpamọ́wọ́", "addAccountSecretPhraseLabel": "Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí", "addAccountSeedLabel": "Ọ̀rọ̀ ìgbaniwọlé", "addAccountSeedIDLabel": "ÌDÁNIMỌ̀ Ọ̀rọ̀ ìgbaniwọlé", "addAccountSecretPhraseDefaultLabel": "Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{number}}", "addAccountZeroAccountsForSeed": "àwọn ibi-ìpamọ́ 0", "addAccountShowAccountForSeed": "Ṣàfihàn ibi-ìpamọ́ 1", "addAccountShowAccountsForSeed": "Ṣàfihàn àwọn ibi-ìpamọ́ {{numOfAccounts}}", "addAccountHideAccountForSeed": "Fi ibi-ìpamọ́ 1 pamọ́", "addAccountHideAccountsForSeed": "<PERSON> àwọn ibi-ì<PERSON>́ {{numOfAccounts}} pamọ́", "addAccountSelectSeedDescription": "Ibi-ìpamọ́ rẹ tuntun ni a ó ṣẹ̀dá láti <PERSON>ólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí yìí", "addAccountNumAccountsForSeed": "<PERSON>w<PERSON><PERSON> ibi-<PERSON><PERSON> {{numOfAccounts}}", "addAccountOneAccountsForSeed": "ibi-ìpamọ́ 1", "addAccountGenerateAccountFromSeed": "Ṣẹ̀dá Ibi-ìpamọ́", "addAccountReadOnly": "Kíyèsí Àdírẹ́sì", "addAccountReadOnlySecondaryText": "Tọpasẹ̀ èyíkéyìí àdírẹ́sì àpamọ́wọ́ gbogbogbò", "addAccountSolanaAddress": "Àdírẹ́sì Solana", "addAccountEVMAddress": "Àdírẹ́sì EVM", "addAccountBitcoinAddress": "Àdírẹ́sì Bitcoin", "addAccountCreateSeedTitle": "Ṣẹ̀dá Ibi-ìpamọ́ kan", "addAccountCreateSeedExplainer": "Àpamọ́wọ́ rẹ kò tíì ní gbólóhùn ọ̀rọ̀ ìgbaniwọlé kan tí ó jẹ́ àṣírí! Láti ṣẹ̀da àpamọ́wọ́ titun kan, a ó gbé gbólóhùn gbígbà padà kan jáde. Kọ èyí sílẹ̀ kí o sì fipamọ́ fún ìwọ nìkan.", "addAccountSecretPhraseHeader": "Gbólóhùn ọ̀rọ̀ ìgbaniwọlé Tí ó jẹ́ àṣírí Rẹ", "addAccountNoSecretPhrases": "<PERSON><PERSON> sí àwọn Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé <PERSON>nkan", "addAccountImportAccountActionButtonImport": "Gbe e wọlé", "addAccountImportAccountDuplicatePrivateKey": "<PERSON><PERSON><PERSON> yí wà lórí àpamọ́wọ́ rẹ lọ́wọ́ lọ́wọ́", "addAccountImportAccountIncorrectFormat": "Ìgbékalẹ̀ tí kò tọ́", "addAccountImportAccountInvalidPrivateKey": "K<PERSON><PERSON>k<PERSON>́rọ́ Aladani tí Kò Ṣiṣẹ́", "addAccountImportAccountName": "Orúkọ", "addAccountImportAccountPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "addAccountImportAccountPrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aladani", "addAccountImportAccountPublicKey": "Àdírẹ́sì tàbí <PERSON>è lórí a<PERSON>", "addAccountImportAccountPrivateKeyRequired": "O nílò k<PERSON>́kọ́rọ́ aladani", "addAccountImportAccountNameRequired": "<PERSON> ní<PERSON> or<PERSON>", "addAccountImportAccountPublicKeyRequired": "Nílò àdírẹ́sì gbogbogbò", "addAccountImportAccountDuplicateAddress": "Àdírẹ́sì yìí ti wà lórí àpamọ́wọ́ rẹ télẹ̀", "addAddressAddressAlreadyAdded": "<PERSON><PERSON> adirẹsi tẹ́lẹ̀", "addAddressAddressAlreadyExists": "Àdírẹ́ẹ́sì tí wà tẹ́lẹ̀", "addAddressAddressInvalid": "Àdírẹ́sì kò wúlò", "addAddressAddressIsRequired": "O nílò àdírẹ́ẹ́sì", "addAddressAddressPlaceholder": "Adirẹsi", "addAddressLabelIsRequired": "O nílò lébẹli", "addAddressLabelPlaceholder": "Lébẹli", "addAddressPrimaryText": "Ṣàfikún Adirẹsi", "addAddressToast": "Ti fi àdírẹ́sì kun", "createAssociatedTokenAccountCostLabelInterpolated": "<PERSON><PERSON><PERSON> yóò náa ní {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "O ti ní ibi-ìpamọ́ tókìnì yìí tẹ́lẹ̀", "createAssociatedTokenAccountErrorInsufficientFunds": "Àpapọ̀ owó tí kò to", "createAssociatedTokenAccountErrorInvalidMint": "Àdírẹ́sì ṣíṣẹ́dá tí kò ṣiṣẹ́", "createAssociatedTokenAccountErrorInvalidName": "Orúkọ tí kò ṣiṣẹ́", "createAssociatedTokenAccountErrorInvalidSymbol": "Àmì tí ko ṣiṣẹ́", "createAssociatedTokenAccountErrorUnableToCreateMessage": "A kò lè ṣẹ̀dá ibi-ìpamọ́ tókìnì rẹ. Jọ̀wọ́ gbìyànjú si nígbàmíì.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Ti kùnà làti ṣẹ̀dá ibi-ìpamọ́", "createAssociatedTokenAccountErrorUnableToSendMessage": "A kò lè fi ìdúnàádúrà rẹ ráńṣẹ́.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Ti kùnà láti fi <PERSON>dúnàádúrà ráńṣẹ́", "createAssociatedTokenAccountInputPlaceholderMint": "Àdírẹ́sì Ṣíṣẹ́dá", "createAssociatedTokenAccountInputPlaceholderName": "Orúkọ", "createAssociatedTokenAccountInputPlaceholderSymbol": "Àmì", "createAssociatedTokenAccountLoadingMessage": "À ń ṣẹ̀dá ibi-ìpamọ́ tókìnì rẹ.", "createAssociatedTokenAccountLoadingTitle": "Ń ṣẹ̀dá ibi-ìpamọ́ tókìnì", "createAssociatedTokenAccountPageHeader": "Ṣẹ̀dá Ibi-ìpamọ́ Tókìnì", "createAssociatedTokenAccountSuccessMessage": "Ṣíṣẹ́dá ibi-ìpamọ́ tókìnì rẹ yọrí sí rere!", "createAssociatedTokenAccountSuccessTitle": "Ti ṣẹ̀dá ibi-ìpamọ́ tókìnì", "createAssociatedTokenAccountViewTransaction": "<PERSON><PERSON>", "assetDetailRecentActivity": "<PERSON><PERSON> tó ṣẹlẹ̀ Laipẹ", "assetDetailStakeSOL": "Fowó sì SOL", "assetDetailUnknownToken": "Tokini Tí akò mọ̀", "assetDetailUnwrapAll": "Ṣi Gbogbo ẹ̀", "assetDetailUnwrappingSOL": "Títú SOL sílẹ̀", "assetDetailUnwrappingSOLFailed": "Títú SOL sílẹ̀ ti kùnà", "assetDetailViewOnExplorer": "<PERSON><PERSON><PERSON> l<PERSON> {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assetDetailSaveToPhotos": "Fipamọ́ sí àwọn <PERSON>", "assetDetailSaveToPhotosToast": "Ti fipamọ́ sí àwọn <PERSON>", "assetDetailPinCollection": "Lẹ Àkójọpọ̀ mọ", "assetDetailUnpinCollection": "Yọ Àkójọpọ̀ kúrò", "assetDetailHideCollection": "Fi Àkójọpọ̀ pamọ́", "assetDetailUnhideCollection": "Yọ Àkójọpọ̀ kúrò ní ìpamọ́", "assetDetailTokenNameLabel": "Orúkọ Tókìnì", "assetDetailNetworkLabel": "Nẹtiwọki", "assetDetailAddressLabel": "Àdírẹ́sì", "assetDetailPriceLabel": "<PERSON><PERSON> ow<PERSON>", "collectibleDetailSetAsAvatar": "Ṣètò b<PERSON>i à<PERSON><PERSON><PERSON>", "collectibleDetailSetAsAvatarSingleWorkAlt": "<PERSON><PERSON><PERSON>", "collectibleDetailSetAsAvatarSuccess": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "collectibleDetailShare": "<PERSON><PERSON> Àgbàsílẹ̀", "assetDetailTokenAddressCopied": "Ti da àdírẹ́sì kọ", "assetDetailStakingLabel": "<PERSON><PERSON><PERSON> owó si", "assetDetailAboutLabel": "Nípa {{fungibleName}}", "assetDetailPriceDetail": "<PERSON><PERSON><PERSON><PERSON><PERSON> owó", "assetDetailHighlights": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailAllTimeReturn": "Gbogbo Àpapọ̀ Èrè", "assetDetailAverageCost": "<PERSON>ye Ìpèsè ọjà", "assetDetailPriceHistoryUnavailable": "Ìtàn iye owó kò sí fún tókìnì yìí", "assetDetailPriceHistoryInsufficientData": "Ìtàn iye owó kò sí fún ìwọ̀n àkókò yìí", "assetDetailPriceDataUnavailable": "Kò sí détà iye owó", "assetDetailPriceHistoryError": "Àṣìṣe wà pẹ̀lú wíwá ìtàn iye owó rí", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "Ọjọ́ 1", "assetDetailTimeFrame24h": "Iye owó ní 24h", "assetDetailTimeFrame1W": "ọ̀sẹ̀ 1", "assetDetailTimeFrame1M": "Oṣù 1", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "GBOGBO RẸ̀", "sendAssetAmountLabelInterpolated": "Tí ó wà {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "<PERSON><PERSON><PERSON><PERSON>", "fiatRampNewQuote": "Ìdíyelé tuntun", "assetListSelectToken": "<PERSON>", "assetListSearch": "Ṣàwárí...", "assetListUnknownToken": "Tokini Tí akò mọ̀", "buyFlowHealthWarning": "<PERSON><PERSON><PERSON> àwọn olù<PERSON><PERSON>è owó sísan wa ń ní ìrírí ọ̀pọ̀ èrò. Fífi owó si lè pẹ́ fún ọ̀pọ̀ wákàtí.", "assetVisibilityUnknownToken": "Tokini Tí akò mọ̀", "buyAssetInterpolated": "Ra {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "Iye tí ó ga jùlọ láti rà ni {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Iye tí ó kéré jùlọ láti rà ni {{amount}}", "buyNoAssetsAvailable": "<PERSON>ò sí àwọn ohun ìní Ethereum tàbí Polygon", "buyThirdPartyScreenPaymentMethodSelector": "Sanwó pẹ̀lú", "buyThirdPartyScreenPaymentMethod": "<PERSON> ọ̀nà ìsanwó", "buyThirdPartyScreenChoseQuote": "Tẹ iye tí ó wúlò fún ìdíyelé si", "buyThirdPartyScreenProviders": "<PERSON><PERSON><PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodTitle": "Àwọn ọ̀nà Ìsanwó", "buyThirdPartyScreenPaymentMethodEmptyState": "Kò sí àwọn ọ̀nà ìsanwó kankan tí ó wà ní ẹkùn rẹ", "buyThirdPartyScreenPaymentMethodFooter": "Àwọn nẹtiwọki alábàáṣiṣẹ́pọ̀ ńṣe agbátẹrù owó sísan. Owó fún sísàn lè yàtọ̀. Àwọn ọ̀nà láti san owó kan kò sí ní àwọn ẹkùn rẹ.", "buyThirdPartyScreenProvidersEmptyState": "Kò sí àwọn olùpèsè kankan tí ó wà ní ẹkùn rẹ", "buyThirdPartyScreenLoadingQuote": "Ń kó ìd<PERSON>yelé jọpọ̀...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON>", "gasEstimationErrorWarning": "Ìṣòro kan wà pẹ̀lú ṣíṣírò iye owó fún ìdúnàádúrà yìí. Ó lè kùnà.", "gasEstimationCouldNotFetch": "<PERSON>ò lè wá ìṣírò iye owó fún mímú ìdúnàádúrà ṣẹ rí", "networkFeeCouldNotFetch": "Kò lè wá iye owó nẹtiwọki rí", "nativeTokenBalanceErrorWarning": "Ìṣòro kan wà pẹ̀lú iye tókìnì rẹ tí ó ṣẹ́kù fún ìdúnàádúrà yìí. Ó lè kùn<PERSON>.", "blocklistOriginCommunityDatabaseInterpolated": "A ti ṣàfihàn saiti yí gẹ́gẹ́bí àrà <1>itọju-àgbègbè ibi ìfipamọ́ data</1> latọdọ àwọn saiti ayédèrú àti ìtànjẹ. Tí ó bá gbagbọ wípé àti ṣàfihàn saiti na nípa àṣìṣe,<3>jọ̀wọ́ jẹki ẹ̀sùn na ó di mímọ</3>.", "blocklistOriginDomainIsBlocked": "A ti di {{Orúkọàṣẹ}} pa!", "blocklistOriginIgnoreWarning": "Má ṣe ka ikilọ yí sí, mú mi lọ sí {{Orúkọàṣẹ}} biotilewu. ", "blocklistOriginSiteIsMalicious": "Phantom gbagbọ pé saiti yí ni ẹ̀tan kosi ni ààbò tó péye fún lílo.", "blocklistOriginThisDomain": "àṣẹ yí", "blocklistProceedAnyway": "Fojú fo ìkìlọ̀, ṣáà tẹ̀síwájú lọ́nàkọnà", "maliciousTransactionWarning": "Phantom ni ìgbàgbọ́ pé idunọdura yí ni ẹtan kosi ni ààbò tó péye láti buwọlu. A ti dá ọ̀nà àti buwọlu dúró láti dabobo ìwọ àti àwọn owó rẹ.", "maliciousTransactionWarningIgnoreWarning": "Má ṣe ká i<PERSON> kún, wọlé biotilewu", "maliciousTransactionWarningTitle": "Iṣà<PERSON>hàn <PERSON>!", "maliciousRequestBlockedTitle": "Ti dínàmọ́ Ìbéèrè", "maliciousRequestWarning": "Ti fi òpó lórí ayélujára yìí hàn bí èyítí ó jẹ́ ẹ̀tan. Ó lè máa gbìyànjú láti jí àwọn owó rẹ tàbí tàn ọ́ jẹ láti fọwọ́sí ìbéèrè ẹ̀tàn kan.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON> ààbò rẹ, <PERSON> ti dínàmọ́ ìb<PERSON>è<PERSON><PERSON> y<PERSON>.", "maliciousRequestBlocked": "<PERSON><PERSON> ààbò rẹ, <PERSON> ti dínàmọ́ ìb<PERSON>è<PERSON><PERSON> y<PERSON>.", "maliciousRequestFrictionDescription": "Títẹ̀síwájú jẹ́ èyítí ó léwu, nítorínáà Phantom ti dínàmọ́ ìbéèrè yìí. O gbọ́dọ̀ pa ìjíròrò yíì dé.", "maliciousRequestAcknowledge": "Ó yé mi wípé mo lè pàdánù gbogbo owó mi nípa lílo ojú òpó yìí.", "maliciousRequestAreYouSure": "Ṣé ó dá ọ lójú?", "siwErrorPopupTitle": "Ìbéèrè Ìbuwọ́lù Kò ṣiṣẹ́", "siwParseErrorDescription": "Ìbéèrè ìbuwọ́lù ti ohun-èlò kò ṣeé fihàn nítorí ìgbékalẹ̀ tí kò ṣiṣẹ́.", "siwVerificationErrorDescription": "(Àwón) àṣìṣe 1 tàbí jù bẹ́ẹ̀ lọ wà pẹ̀lú ìbéèrè ìbuwọ́lù ìfiráńṣẹ́ náà. Fún ààbò rẹ, jọ̀wọ́ ri dájú wípé ò ńlo ohun-èlò tí ó tọ́ kí o sì gbìyànjú si.", "siwErrorPagination": "{{n}} ti {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Ìkìlọ̀: àdírẹ́sì ohun-èlò kò bá àdírẹ́sì tí a ti pèsè fún bíbuwọ́lù mu.", "siwErrorMessage_DOMAIN_MISMATCH": "Ìkìlọ̀: à<PERSON><PERSON><PERSON> ohun-è<PERSON>ò kò bá ààyè tí a ti pèsè fún jíjẹ́rìsíí mu.", "siwErrorMessage_URI_MISMATCH": "Ìkìlọ̀: or<PERSON>k<PERSON> olùgbanilálejò URI kò bá ààyè mu.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Ìkìlọ̀: ÌDÁNIMỌ̀ ṣéènì kò bá ÌDÁNIMỌ̀ ṣéènì tí a ti pèsè fún jíjẹ́rìsíí mu.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Ìkìlọ̀: ọjọ<PERSON> pínpín ìfiráńṣẹ́ ti pẹ́ jù sẹ́yìn.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Ìkìlọ̀: ọjọ́ pínpín ìfiráńṣẹ́ ti pẹ́ jù sí ọjọ́ iwájú.", "siwErrorMessage_EXPIRED": "Ìkìlọ̀: ìfiráńṣẹ́ ti kọjá <PERSON>.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Ìkìlọ̀: ìfiráńṣẹ́ ti kọjá <PERSON>ò ṣaájú pínpín.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Ìkìlọ̀: ìfiráńṣẹ́ yóò kọjá àkókò ṣaájú kí ó tó má ṣiṣẹ́ mọ́.", "siwErrorShowErrorDetails": "<PERSON> àw<PERSON>n àl<PERSON><PERSON>é àṣì<PERSON>e hàn", "siwErrorHideErrorDetails": "<PERSON> àwọn àlàyé àṣìṣe pamọ́", "siwErrorIgnoreWarning": "Fojú fo ìkìlọ̀, ṣáà tẹ̀síwájú lọ́nàkọnà", "siwsTitle": "Ìbéèrè <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "siwsPermissions": "Ìgbaniláàyè", "siwsAgreement": "Ìfiránṣẹ́", "siwsAdvancedDetails": "<PERSON><PERSON><PERSON>n à<PERSON>é Tí ó jinlẹ̀", "siwsAlternateStatement": "{{domain}} ńfẹ́ kí o buwọ́lù wọlé pẹ̀lú ibi-ìpamọ́ Solana rẹ:\n{{address}}", "siwsFieldLable_domain": "Ààyè ló<PERSON>", "siwsFieldLable_address": "Àdírẹ́sì", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Ẹ̀yà", "siwsFieldLable_chainId": "ÌDÁNIMỌ̀ Ṣéènì", "siwsFieldLable_nonce": "Ọ̀rọ̀ tí a ṣẹ̀dá fún àkókò kan", "siwsFieldLable_issuedAt": "Tí a ṣẹ̀dá Ní", "siwsFieldLable_expirationTime": "<PERSON>í y<PERSON><PERSON> dó<PERSON>", "siwsFieldLable_requestId": "Bé<PERSON><PERSON><PERSON> ÌDÁNIMỌ̀", "siwsFieldLable_resources": "Àwọn àlùmọ́ọ̀nì", "siwsVerificationErrorDescription": "Ìbéèrè bíbuwọ́lù wọlé yìí kò ṣiṣẹ́. Yálà èyí túmọ̀ sí wípé òpó lórí ayélujára náà kò ní ààbò, tàbí ẹni tí ó ṣẹ̀dá rẹ ṣe àṣìṣe nígbàtí ó ńfi ìbéèrè náà ráńṣẹ́.", "siwsErrorNumIssues": "{{n}} <PERSON><PERSON><PERSON><PERSON>", "siwsErrorMessage_CHAIN_ID_MISMATCH": "ÌDÁNIMỌ̀ ṣéènì yìí kò bá nẹtiwọki tí o wà lórí rẹ̀ mu.", "siwsErrorMessage_DOMAIN_MISMATCH": "Ààyè lórí a<PERSON> yìí kìí ṣe èyítí ò ńbuwọ́lù sí.", "siwsErrorMessage_URI_MISMATCH": "URI yìí kìí ṣe èyítí ò ńbuwọ́lù sí.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Ọjọ́ pínpín ìfiráńṣẹ́ ti pẹ́ jù sẹ́yìn.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Ọjọ́ pínpín ìfiráńṣẹ́ ti pẹ́ jù sí ọjọ́ iwájú.", "siwsErrorMessage_EXPIRED": "Ìfiráńṣẹ́ ti kọjá <PERSON>.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Ìfiráńṣẹ́ ti kọjá <PERSON> ṣaájú p<PERSON>.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Ìfiráńṣẹ́ yóò kọjá àkókò ṣaájú kí ó tó má ṣiṣẹ́ mọ́.", "changeLockTimerPrimaryText": "<PERSON><PERSON>-Alaiṣefọ̀wọ́yi", "changeLockTimerSecondaryText": "Ìyè ìgbà wo lani láti dúró dè láti tí àpamọ́wọ́ rẹ pa lẹ́yìn ìgbà tí kò ti ṣiṣẹ́ mọ́?", "changeLockTimerToast": "Ti mú aago titipa-alaiṣefọ̀wọ́yi dójú ìwọ̀n", "changePasswordConfirmNewPassword": "Jẹ́rìsí ọ̀rọ̀ ìgbaniw<PERSON>lé tuntun", "changePasswordCurrentPassword": "Ọ̀rọ̀ ìgbaniwọlé to ń lo lọ́wọ́ lọ́wọ́", "changePasswordErrorIncorrectCurrentPassword": "Ọ̀rọ̀ ìgbaniwọlé to ń lo lọ́wọ́ lọ́wọ́ tí kò tọ́", "changePasswordErrorGeneric": "Nkan ti ko tọ́ kan ti ṣẹlẹ̀, jọ̀wọ́ tún gbìyànjú tó bá ṣe díẹ̀", "changePasswordNewPassword": "Ọ̀rọ̀ ìgban<PERSON><PERSON><PERSON><PERSON> tuntun", "changePasswordPrimaryText": "Pàrọ ọ̀rọ̀ ìgbaniwọlé rẹ", "changePasswordToast": "Ti mú ọ̀rọ̀ ìgbaniwọlé dójú ìwọ̀n", "collectionsSpamCollections": "Àwọn àkójọpọ̀ ìfiráńṣẹ́ tí a kò bèèrè fún", "collectionsHiddenCollections": "Àwọn Àkójọpọ̀ Tí a fipamọ́", "collectiblesReportAsSpam": "Jábọ̀ bíi Ìfiráńṣẹ́ tí a kò bèèrè fún", "collectiblesReportAsSpamAndHide": "Jábọ̀ bíi Ìfiráńṣẹ́ tí a kò bèèrè fún kí o sì fipamọ́", "collectiblesReportAsNotSpam": "Jábọ̀ bíi èyítí Kìí ṣe Ìfiráńṣẹ́ tí a kò bèèrè fún", "collectiblesReportAsNotSpamAndUnhide": "Yọ ọ́ kúrò ní ìpamọ́ kí o sì jábọ̀ bíi ìfiráńṣẹ́ tí a béèrè fún", "collectiblesReportNotSpam": "Ìfiráńṣẹ́ Tí a béèrè fún", "collectionsManageCollectibles": "Ṣe àk<PERSON>o àwọn àkójọ àgbàsílẹ̀", "collectibleDetailDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailProperties": "<PERSON><PERSON><PERSON><PERSON> ohun ì<PERSON>", "collectibleDetailOrdinalInfo": "Àlàyé Ìlànà", "collectibleDetailRareSatsInfo": "Àlàyé Rare Sats", "collectibleDetailSatsInUtxo": "Iye BTC tí ó kéré jùlọ nínú ìdúnàádúrà tí a kò ná", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Nọ́ńbà Iye Btc tí ó kéré jùlọ", "collectibleDetailSatName": "Orúkọ Iye Btc tí ó kéré jùlọ", "collectibleDetailInscriptionId": "ÌDÁNIMỌ̀ Ohun tí a kọ sára nǹkan", "collectibleDetailInscriptionNumber": "Nọ́ńbà Ohun tí a kọ sára nǹkan", "collectibleDetailStandard": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailCreated": "Tí a ṣẹ̀dá", "collectibleDetailViewOnExplorer": "<PERSON><PERSON><PERSON> l<PERSON> {{explorer}}", "collectibleDetailList": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailSellNow": "Tàá fún {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Ṣí Bitcoin tí o kò lo sílẹ̀", "collectibleDetailUtxoSplitterCtaSubtitle": "O ní {{value}} BTC láti <PERSON>", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "<PERSON><PERSON><PERSON> dá<PERSON><PERSON><PERSON><PERSON> àwọn owó rẹ, àwa dènà BTC nínú àwọn UTXO pẹ̀lú Rare Sats láti má ṣée firáńṣẹ́. Lo olùpín UXTO ti Magic Eden láti ṣí {{value}} BTC láti inú Rare Sats rẹ sílẹ̀.", "collectibleDetailUtxoSplitterModalCtaButton": "Lo Olùpín UTXO", "collectibleDetailEasilyAccept": "Gba ìdíyelé tí ó ga jùlọ", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "A fi àwọn àgbà sílẹ̀ yìí pamọ́ nítorí Phantom gbàgbọ́ wípé ó jẹ́ Ìfiráńṣẹ́ tí a kò béèrè fún.", "collectibleDetailSpamOverlayReveal": "<PERSON> àwọn Àgbà sílẹ̀ hàn", "collectibleBurnTermsOfService": "Ó yé mi wípé èyí kò ṣeé túnṣe", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON>", "collectibleBurnTitleWithCount_other": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "collectibleBurnDescriptionWithCount_one": "Ìṣe yìí yóò ba tókìnì yìí jẹ́ tí yóò sì yọ ọ́ kúrò nínú àpamọ́wọ́ rẹ títíláí.", "collectibleBurnDescriptionWithCount_other": "Ìṣe yìí yóò ba àwọn tókìnì wọ̀nyìí jẹ́ tí yóò sì yọ ọ́ kúrò nínú àpamọ́wọ́ rẹ títíláí.", "collectibleBurnTokenWithCount_one": "Tókìnì", "collectibleBurnTokenWithCount_other": "<PERSON><PERSON><PERSON><PERSON>", "collectibleBurnCta": "<PERSON><PERSON>", "collectibleBurnRebate": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> owó pad<PERSON>", "collectibleBurnRebateTooltip": "A ó fi iye SOL díẹ̀ sí àpamọ́wọ́ rẹ láìsí ìlọ́wọ́si rẹ fún jíjó tókììnì yìí níná.", "collectibleBurnNetworkFee": "Owó Nẹtiwọki", "collectibleBurnNetworkFeeTooltip": "Ìyè owó tí nẹtiwọki Solana nílò láti fi ṣeto ìdúnààádúrà náà", "unwrapButtonSwapTo": "Pàrọ̀ sí {{chainSymbol}}", "unwrapButtonWithdrawFrom": "<PERSON>ọ <PERSON>́ kúr<PERSON> láti {{withdrawalSource}} fún {{chainSymbol}}", "unwrapModalEstimatedTime": "Iye àkókò Tí a gbèrò", "unwrapModalNetwork": "Nẹtiwọki", "unwrapModalNetworkFee": "Owó Nẹtiwọki", "unwrapModalTitle": "Ni <PERSON>", "unsupportedChain": "Ṣéènì tí kò ní àtìlẹyìn", "unsupportedChainDescription": "Ó jọ wípé a kò ṣe àtilẹyìn {{action}} fún nẹtiwọki {{chainName}} náà.", "networkFeesTooltipLabel": "<PERSON>w<PERSON>n owó nẹtiwọki {{chainName}}", "networkFeesTooltipDescription": "àwọn owó {{chainName}} yàtọ̀ látàrí ọ̀pọ̀lọpọ̀ àwọn okùnfà. Ìwọ lè fi wọ́n ṣe bí a bá ṣe fẹ́ láti jẹ́ kí ìdúnàádúrà rẹ túnbọ̀ yá (tí ó wọ́n si) tàbí lọ́ra si (tí kò wọ́n).", "burnStatusErrorTitleWithCount_one": "Tókìnì ti kùnà láti j<PERSON>", "burnStatusErrorTitleWithCount_other": "Àwọn tókìnì ti kùnà láti j<PERSON>", "burnStatusSuccessTitleWithCount_one": "Tókìnì ti jóná!", "burnStatusSuccessTitleWithCount_other": "Àwọn tókìnì ti jóná!", "burnStatusLoadingTitleWithCount_one": "Ńjó tókìnì níná...", "burnStatusLoadingTitleWithCount_other": "Ńjó àwọn tókìnì níná...", "burnStatusErrorMessageWithCount_one": "Tókìnì y<PERSON>í kò ṣeé jó níná. Jọ̀wọ́ gbìyànjú si nígbàmíì.", "burnStatusErrorMessageWithCount_other": "Àwọn tókìnì wọ̀nyìí kò ṣeé jó níná. Jọ̀wọ́ gbìyànjú si nígbàmíì.", "burnStatusSuccessMessageWithCount_one": "A ti ba tókìnì yìí jẹ́ títíláí tí a sì ti fi {{rebateAmount}} SOL sínú àpamọ́wọ́ rẹ.", "burnStatusSuccessMessageWithCount_other": "A ti ba àwọn tókìnì wọ̀nyìí jẹ́ títíláí tí a sì ti fi {{rebateAmount}} SOL sínú àpamọ́wọ́ rẹ.", "burnStatusLoadingMessageWithCount_one": "A ti ńba tókìnì yìí jẹ́ títíláí tí a yóò sì fi {{rebateAmount}} SOL sínú àpamọ́wọ́ rẹ.", "burnStatusLoadingMessageWithCount_other": "A ti ńba àwọn tókìnì wọ̀nyìí jẹ́ títíláí tí a yóò sì fi {{rebateAmount}} SOL sínú àpamọ́wọ́ rẹ.", "burnStatusViewTransactionText": "<PERSON><PERSON>", "collectibleDisplayLoading": "<PERSON><PERSON>ajọpọ...", "collectiblesNoCollectibles": "Kò sí àwọn àgbà sílẹ̀", "collectiblesPrimaryText": "Àwọn àgbà sílẹ̀ rẹ", "collectiblesReceiveCollectible": "Gba Àgbà sílẹ̀", "collectiblesUnknownCollection": "Ìgbà sílẹ̀ Tí akò mọ̀", "collectiblesUnknownCollectible": "Ìgbàsílẹ̀ Tí a kò mọ̀", "collectiblesUniqueHolders": "Àwọn ohun ìkó ǹkan sí Tí ó jẹ́ àkànṣe", "collectiblesSupply": "Ìpèsè", "collectiblesUnknownTokens": "Àwọn Tokini Tí akò mọ̀", "collectiblesNrOfListed": "{{ nrTiOríàrmtẹ }} Ó wà lórí àtẹ", "collectiblesListed": "Ówà l'ori àtẹ", "collectiblesMintCollectible": "Ṣẹ́dá Àgbàsílẹ̀", "collectiblesYouMint": "Tí o Ṣẹ́dá", "collectiblesMintCost": "Iye tí ṣíṣẹ̀dá ná ni", "collectiblesMintFail": "Ṣíṣẹ̀dá ti kùnà", "collectiblesMintFailMessage": "Ìṣòro kan wà pẹ̀lu ṣíṣẹ̀dá àgbàsílẹ̀ rẹ. Jọ̀wọ́ gbìyànjú si.", "collectiblesMintCostFree": "Lọ́fẹ̀ẹ́", "collectiblesMinting": "Ńṣẹ̀dá...", "collectiblesMintingMessage": "Ńṣẹ̀dá àgbàsílẹ̀ rẹ lọ́wọ́", "collectiblesMintShareSubject": "Yẹ èyí wò", "collectiblesMintShareMessage": "Mo ṣẹ̀dá è<PERSON>í ló<PERSON>í @phantom!", "collectiblesMintSuccess": "Ṣíṣẹ̀dá yọrí sí rere", "collectiblesMintSuccessMessage": "Ti ṣẹ̀dá àgbàsílẹ̀ rẹ nísìnyí", "collectiblesMintSuccessQuestMessage": "O ti parí àwọn àmúyẹ fún Ìbéèrè Phantom. Tẹ Gba èrè rẹ láti gba àwọn àgbà sílẹ̀ rẹ lọ́fẹ̀ẹ́.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMintMaxLengthErrorMessage": "Ti kọjá ìwọ̀n gígùn tí ó gajù lọ", "collectiblesMintSafelyDismiss": "O lè yọ fèrèsé y<PERSON>í kúrò láìsí wàh<PERSON>là.", "collectiblesTrimmed": "A ti dé gbèdéke fún nọ́ńbà àwọn àgbà sílẹ̀ tí a lè ṣe àfihàn lọ́wọ́lọ́wọ́ nísìnyí.", "collectiblesNonTransferable": "Kò ṣe é gbé sí ibòmíràn", "collectiblesNonTransferableYes": "Bẹ́ẹ̀ni", "collectiblesSellOfferDetails": "<PERSON><PERSON><PERSON><PERSON> à<PERSON> Ìdíyelé", "collectiblesSellYouSell": "O Tàá", "collectiblesSellGotIt": "Ti gbàá", "collectiblesSellYouReceive": "O Gba", "collectiblesSellOffer": "<PERSON><PERSON><PERSON>", "collectiblesSoldCollectible": "Tí ta Àgbàsílẹ̀", "collectiblesSellMarketplace": "<PERSON><PERSON>", "collectiblesSellCollectionFloor": "Orí àtẹ Àkójọpọ̀", "collectiblesSellDifferenceFromFloor": "Ìyàtọ́ sí ti orí àtẹ", "collectiblesSellLastSalePrice": "Ọjà tí a tà Kẹ́yìn", "collectiblesSellEstimatedFees": "Ti gbèrò iye Owó", "collectiblesSellEstimatedProfitAndLoss": "Ti g<PERSON><PERSON><PERSON><PERSON>è/Àdánù", "collectiblesSellViewOnMarketplace": "<PERSON><PERSON><PERSON> ló<PERSON>í {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "<PERSON>ye owó 'Ràá Nísìnyí' tí ó lọ sílẹ̀ jùlọ nínú àkójọpọ̀ jákèjádò ọ̀pọ̀lọpọ̀ àwọn ibi ìta<PERSON>.", "collectiblesSellProfitLossTooltip": "Iye owó Èrè/Àdánù tí a ti gbèrò ní a ti ṣírò látàrí ìyàtọ̀ láàárín iye owó ọjà tí a tà kẹ́yìn àti iye owó tí a pèsè ọjà.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "<PERSON>ye owó fún ìlò ohun tí a pilẹ̀ṣe ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "<PERSON><PERSON><PERSON> ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "<PERSON><PERSON><PERSON>", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Nẹtiwọki {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Iye owó tí a ṣ<PERSON><PERSON>ò ní {{phantomFeePercentage}} owó Phantom nínú", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Iye owó tí a ṣírò ní Àwọn Owó Òṣèré, <PERSON><PERSON><PERSON> Nẹtiwọki, <PERSON><PERSON><PERSON>, <PERSON><PERSON> owó <PERSON> {{phantomFeePercentage}} nínú", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "Ìdíyelé ní àwọn Owó Òṣèré, <PERSON><PERSON><PERSON> Nẹtiwọki, <PERSON><PERSON><PERSON> n<PERSON>", "collectiblesSellTransactionFeeTooltipTitle": "Owó Ìdúnàádúrà", "collectiblesSellStatusLoadingTitle": "Ńgba Ìdíyelé...", "collectiblesSellStatusLoadingIsSellingFor": "ó wà fún títà ní", "collectiblesSellStatusSuccessTitle": "Ti ta {{collectibleName}}!", "collectiblesSellStatusSuccessWasSold": "ti ṣe àṣeyọrí pẹ̀lú títàá fún", "collectiblesSellStatusErrorTitle": "Nǹkankan Kò lọ Déédéé", "collectiblesSellStatusErrorSubtitle": "Ìṣòro kan wà pẹ̀lú gbígbìyànjú láti tàá", "collectiblesSellStatusViewTransaction": "Wo Ìdúnàádúrà", "collectiblesSellInsufficientFundsTitle": "Àpapọ̀ owó tí kò to", "collectiblesSellInsufficientFundsSubtitle": "Àwa kò lè gba ìdíyelé kan lórí àgbàsílẹ̀ yìí nítorí kò sí àwọn owó tí ó tó láti san owó nẹtiwọki náà.", "collectiblesSellRecentlyTransferedNFTTitle": "Tí a firáńṣẹ́ láìpẹ́", "collectiblesSellRecentlyTransferedNFTSubtitle": "O gbọ́dọ̀ dúró fún wákàtí 1 láti gba fífi owó sílẹ̀ lẹ́yìn ìfiráńṣẹ́ kan.", "collectiblesApproveCollection": "Ti fọwọ́ sí {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "<PERSON><PERSON> s<PERSON>", "collectiblesSellNotAvailableAnymoreSubtitle": "Ìdíyelé náà kò sí mọ́. Fagilé ìfowó sílẹ̀ yìí kí ó sì tún gbìyànjú si", "collectiblesSellFlaggedTokenTitle": "A ti ṣàfihàn àwọn àgbà sílẹ̀ bí èyítí kò dára", "collectiblesSellFlaggedTokenSubtitle": "Àwọn àgbà sílẹ̀ kò ṣeé rà tàbí tà, ó lè jẹ́ fún ọ̀pọ̀lọpọ̀ ìdí bíi kí a ti jábọ̀ wípé a jíi tàbi ti fowó si láì tìí pa", "collectiblesListOnMagicEden": "<PERSON> wà lórí <PERSON> Édẹ́nì", "collectiblesListPrice": "Iye Owó <PERSON>", "collectiblesUseFloor": "Lo Ilẹ̀lẹ̀", "collectiblesFloorPrice": "<PERSON><PERSON> Ow<PERSON> Ilẹ̀lẹ̀", "collectiblesLastSalePrice": "<PERSON>ye Owó Tí a tà Kẹ́yìn", "collectiblesTotalReturn": "Àpapọ̀ iye Tí a dápadà", "collectiblesOriginalPurchasePrice": "Iye Owó tí ó rà Gangan", "collectiblesMagicEdenFee": "<PERSON><PERSON> Ow<PERSON> Édẹ́nì", "collectiblesArtistRoyalties": "<PERSON><PERSON><PERSON><PERSON> Owó Òṣèré", "collectiblesListNowButton": "Ko jọ Nísinsìnyí", "collectiblesListAnywayButton": "Too jọ Bí ó tilẹ̀ wù", "collectiblesCreateListingTermsOfService": "Nípa títẹ <1>\"Fi sórí àtẹ fún rírà àti títà Nísìnyí\"</1> ìwọ gba <3>Àwọn òfin Iṣẹ́</3> Magic Eden", "collectiblesViewListing": "<PERSON><PERSON>", "collectiblesListingViewTransaction": "Wo Ìdunọdura", "collectiblesRemoveListing": "<PERSON><PERSON>", "collectiblesEditListing": "Ṣe àtúnṣe sì <PERSON>", "collectiblesEditListPrice": "Ṣe àtúnṣe Iyeowó <PERSON>òjọ", "collectiblesListPriceTooltip": "Iye Owó tí ó wà lórí àtẹ ni iye owó tí a ń tà ọjà. Àwọn tí ó tà ọjà ni wọn fi Iye Owó orí àtẹ sílẹ̀ láti jẹ́ kí ó wà ní tàbí kí ó ju Iye Owó Pẹ̀tẹ́lẹ̀ lọ.", "collectiblesFloorPriceTooltip": "Owó Pẹ̀tẹ́lẹ̀ ni Iye Owó Orí àtẹ tí ó kéré jù lọ fún ọjà nínú àwọn àkójọpọ̀ yí.", "collectiblesOriginalPurchasePriceTooltip": "O ra ọjà yí tẹ́lẹ̀ tẹ́lẹ̀ fún iye owó yí.", "collectiblesPurchasedForSol": "Ti ràá fún {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "<PERSON>ò ṣeé ṣe láti ṣì àwọn àtòjọ", "collectiblesUnableToLoadListingsFrom": "<PERSON>ò ṣeé ṣe láti sì àwọn atòjọ látinu {{iléìtajà}}", "collectiblesUnableToLoadListingsDescription": "À<PERSON><PERSON>ò wá fun àwọn atòjọ àti àwọn ohùn ìní rẹ ṣùgbọ́n kò ṣeé ṣe fún wa láti sì wọn láti nù {{iléìtajà}} ní báyìí. Jọ̀wọ́ gbìyànjú sì ti o ba ṣe diẹ. ", "collectiblesBelowFloorPrice": "<PERSON> kéré sí Owó Pẹ̀tẹ́lẹ̀", "collectiblesBelowFloorPriceMessage": "Ṣé o dá ẹ́ lójú pé o fẹ́ pàtẹ NFT rẹ ni iye tó kéré sì owó pẹ̀tẹ́lẹ̀?", "collectiblesMinimumListingPrice": "Iye owó tó kéré jù ni 0.01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden ńgba owó lórí àwọn ìdúnàádúrà tí ó ti parí.", "collectiblesArtistRoyaltiesTooltip": "Ẹni tí ó dà àkójọpọ̀ yí sílẹ̀ má gba owó % lórí ọjà kọ̀ọ̀kan tí ó bá ti di títa.", "collectibleScreenCollectionLabel": "Àkójọpọ̀", "collectibleScreenPhotosPermissionTitle": "Gbígba <PERSON><PERSON><PERSON><PERSON>", "collectibleScreenPhotosPermissionMessage": "Àwa nílò ìgbaniláàyè rẹ láti ní ààyè sí àwọn àwòrán rẹ. Jọ̀wọ́ lọ sí àwọn Ètò kí o sì mú àwọn ìgbaniláàyè rẹ dé ojú òṣùwọ̀n.", "collectibleScreenPhotosPermissionOpenSettings": "Ṣí àwọn <PERSON>", "listStatusErrorTitle": "Àt<PERSON>j<PERSON> ni Ìkùnà", "editListStatusErrorTitle": "Ìmúdójúwọ̀n kò ṣeé ṣe", "removeListStatusErrorTitle": "Yíyọ <PERSON>tòjọ ni Ìkùnà", "listStatusSuccessTitle": "A ti ṣe Ìdásílẹ̀ Àtòjọ!", "editListingStatusSuccessTitle": "Àtòjọ tí a ti Mú dójú wọn!", "removeListStatusSuccessTitle": "<PERSON><PERSON> y<PERSON> Àtòjọ kúrò lórí Idán Édẹ́nì", "listStatusLoadingTitle": "Ṣíṣe Ìdásílẹ̀ Àtòjọ...", "editListingStatusLoadingTitle": "Ṣíṣe Ìmúdójúwọ̀n Àtòjọ...", "removeListStatusLoadingTitle": "Yíyọ Àtòjọ...", "listStatusErrorMessage": "{{orúkọ}} a kò lè too jọ sórí <PERSON> Édẹ́nì", "removeListStatusErrorMessage": "{{orúkọ}} a kò lè yọ kúrò lórí Idán Édẹ́nì", "listStatusSuccessMessage": "{{orúkọ}} tí wà lórí atòjọ bayi lórí Idán Édẹ́nì fún {{IyeOwóÌgbàsilẹ̀oriàtẹ}} SOL", "editListingStatusSuccessMessage": "{{orúkọ}} tí wà ní ìmúdójúwọ̀n bayi lórí <PERSON>d<PERSON> Édẹ́nì fún {{ṢéàtúnṣeIyeOwóÌgbàsilẹ̀Oríàtẹ}} SOL", "removeListStatusSuccessMessage": "Ati ṣe àṣeyọrí nípa yíyọ {{orúkọ}} kúr<PERSON> lórí Idán Édẹ́nì", "listStatusLoadingMessage": "{{orúk<PERSON>}} Àtòjọ lórí Idán Édẹ́nì fún {{IyeOwóÌgbàsilẹ̀oriàtẹ}} SOL.", "editListingStatusLoadingMessage": "Ṣíṣe Ìmúdójúwọ̀n {{orúkọ}} l<PERSON><PERSON><PERSON>án Édẹ́nì fún {{ṣíṣeàtúnṣeIyeOwóÌgbàsilẹ̀Oríàtẹ}} SOL.", "removeListStatusLoadingMessage": "<PERSON><PERSON><PERSON><PERSON> {{name}} k<PERSON><PERSON><PERSON> l<PERSON>ti Magic Eden. <PERSON><PERSON>í lè gba ìgbà pípẹ́ díẹ̀.", "listStatusLoadingSafelyDismiss": "O lè yọ fèrèsé yí kúrò lai si wàhálà.", "listStatusViewOnMagicEden": "Wo ó lórí Idán Édẹ́nì", "listStatusViewOnMarketplace": "<PERSON><PERSON><PERSON> ló<PERSON>í {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON> k<PERSON>", "listStatusViewTransaction": "Wo Ìdúnàádúrà", "connectHardwareConnectedPrimaryText": "Ìwé <PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareConnectedSecondaryText": "Ṣe àsopọ̀ àwọn ohùn èlò àpamọ́wọ́ rẹ kosi rí wípé ó wà ní ṣíṣí. Lọwọ kan tí a bati dámọ̀ ó lè yàn adirẹsi tí ó bá wun ọ láti lo.", "connectHardwareFailedPrimaryText": "Àsopọ̀ ni ìkùnà", "connectHardwareFailedSecondaryText": "Jọ̀wọ́ ṣe àsopọ̀ àwọn ohùn èlò àpamọ́wọ́ rẹ kosi rí wípé ó wà ní ṣíṣí. Lọwọ kan tí a bati ri ó lè yàn adirẹsi tí ó bá wun ọ láti lo.", "connectHardwareFinishPrimaryText": "<PERSON><PERSON> rẹ!", "connectHardwareFinishSecondaryText": "O ti wa ni anfaani lati lo Ìwé àkọọlẹ̀ àpamọ́wọ́ Nano rẹ láti inú Phantom. Jọ̀wọ́ padà sí ibi itẹsiwaju.", "connectHardwareNeedsPermissionPrimaryText": "Ṣé àsopọ̀ àpamọ́wọ́ tuntun", "connectHardwareNeedsPermissionSecondaryText": "Tẹ̀ b<PERSON><PERSON>i to wá ní ìsàlẹ̀ yí láti bẹ̀rẹ̀ ṣí ṣe iṣẹ́ àsopọ̀ na.", "connectHardwareSearchingPrimaryText": "<PERSON><PERSON> ṣàw<PERSON><PERSON><PERSON> à<PERSON>ọ́wọ́...", "connectHardwareSearchingSecondaryText": "Ṣé àsopọ̀ ohùn èlò àpamọ́wọ́ rẹ, ri dájú pé o wa ni ṣíṣí, àti wípé otí fọwọ́ si àwọn igbalaye lórí ẹ̀rọ a ṣàwárí kiri rẹ.", "connectHardwarePermissionDeniedPrimary": "Ti k<PERSON> ìgbaniláàyè", "connectHardwarePermissionDeniedSecondary": "Gba Phantom láàyè láti sopọ̀ mọ́ ẹ̀rọ Ìwé àkọọ́lẹ̀ rẹ", "connectHardwarePermissionUnableToConnect": "Kò lè sopọ̀ mọ́ ọ", "connectHardwarePermissionUnableToConnectDescription": "Àwa kò lè sọpọ̀ mọ́ ẹ̀rọ Ìwé àkọọ́lẹ̀ rẹ. Àwa lè nílò àwọn ìgbaniláàyè síwájú si.", "connectHardwareSelectAddressAllAddressesImported": "<PERSON><PERSON> gbé gbogbo adirẹsi wọlé", "connectHardwareSelectAddressDerivationPath": "Ọ̀nà ìtọ́sẹ̀", "connectHardwareSelectAddressSearching": "Ṣí ṣàwárí...", "connectHardwareSelectAddressSelectWalletAddress": "Yan adirẹsi àpamọ́wọ́", "connectHardwareSelectAddressWalletAddress": "Adirẹsi àpamọ́wọ́", "connectHardwareWaitingForApplicationSecondaryText": "Jọ̀wọ́ ṣe àsopọ̀ ohùn èlò àpamọ́wọ́ rẹ kosi rí wípé ó wà ní ṣíṣí.", "connectHardwareWaitingForPermissionPrimaryText": "O nílò i<PERSON>", "connectHardwareWaitingForPermissionSecondaryText": "Ṣé àsopọ̀ ohùn èlò àpamọ́wọ́ rẹ, ri dájú pé o wa ni ṣíṣí, àti wípé oti fọwọ́ sì àwọn igbalaye lórí ẹ̀rọ a ṣàwárí kiri rẹ.", "connectHardwareAddAccountButton": "Fi ibi-ìpamọ́ kun", "connectHardwareLedger": "So Ìwé àkọọ́lẹ̀ rẹ pọ̀mọ", "connectHardwareStartConnection": "Tẹ bọ́tìnì tí ó wà nísàlẹ̀ láti bẹ̀rẹ̀ síso Ìwé àkọọ́lẹ̀ rẹ pọ̀mọ́ ohun èlò àpamọ́wọ́", "connectHardwarePairSuccessPrimary": "Ti so {{productName}} pọ̀mọ", "connectHardwarePairSuccessSecondary": "O ti ṣe àṣeyọrí ní síso {{productName}} rẹ pọ̀mọ.", "connectHardwareSelectChains": "<PERSON> àwọn ṣé<PERSON><PERSON><PERSON> láti so pọ̀mọ", "connectHardwareSearching": "Ńṣàwárí...", "connectHardwareMakeSureConnected": "So ohun èlò àpamọ́wọ́ rẹ pọ̀mọ kí o sì ṣíi sílẹ̀. Jọ̀wọ́ fọwọ́ sí àwọn ìgbaniláàyè ẹ̀rọ a ṣàwárí kiri tí ó yẹ.", "connectHardwareOpenAppDescription": "Jọ̀wọ́ ṣí ohun èlò àpamọ́wọ́ rẹ sílẹ̀", "connectHardwareConnecting": "Ńsopọ̀mọ...", "connectHardwareConnectingDescription": "Àwa ńsopọ̀mọ́ ẹ̀rọ Ìwé àkọọ́lẹ̀ rẹ.", "connectHardwareConnectingAccounts": "Ńso àwọn ibi-ìpamọ́ rẹ pọ̀mọ...", "connectHardwareDiscoveringAccounts": "Ńṣàwárí àwọn ibi-ìpamọ́...", "connectHardwareDiscoveringAccountsDescription": "Àwa ńwá àwọn ibi-ìpamọ́ rẹ fún iṣẹ́ tí ó ńlọ lọ́wọ́.", "connectHardwareErrorLedgerLocked": "Ìwé àkọọ́lẹ̀ ti wà ní títìpa", "connectHardwareErrorLedgerLockedDescription": "Ri dájú wípé ẹ̀rọ Ìwé àkọọ́lẹ̀ rẹ wà ní ṣíṣí sílẹ̀, lẹ́yìn náà tún gbìyànjú si.", "connectHardwareErrorLedgerGeneric": "Nǹkankan kò lọ dédé", "connectHardwareErrorLedgerGenericDescription": "<PERSON>ò lè wá àwọn ibi-ìpamọ́ rí. Ri dájú wípé ẹ̀rọ Ìwé àkọọ́lẹ̀ rẹ wà ní ṣíṣí sílẹ̀, lẹ́yìn náà tún gbìyànjú si.", "connectHardwareErrorLedgerPhantomLocked": "Jọ̀wọ́ tún Phantom ṣí kí o sì tún gbìyànjú láti so ohun-èlò rẹ pọ̀mọ́ lẹ́ẹ̀kansi.", "connectHardwareFindingAccountsWithActivity": "Ńwá àwọn ibi-ìpamọ́ {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "Ńwá àwọn ibi-ìpamọ́ {{chainName1}} àbí {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Àwa ti rí àwọn ibi-ìpamọ́ {{numOfAccounts}} tí ó ńṣiṣẹ́ lórí Ìwé àkọọ́lẹ̀ rẹ.", "connectHardwareFoundAccountsWithActivitySingular": "Àwa ti rí ibi-ìpamọ́ 1 tí ó ńṣiṣẹ́ lórí Ìwé àkọọ́lẹ̀ rẹ.", "connectHardwareFoundSomeAccounts": "Àwa ti rí àwọn ibi-ìpamọ́ kan lórí Ìwé àkọọ́lẹ̀ rẹ.", "connectHardwareViewAccounts": "<PERSON>o à<PERSON><PERSON><PERSON>", "connectHardwareConnectAccounts": "Tí so àwọn ibi-ìpamọ́ pọ̀mọ", "connectHardwareSelectAccounts": "<PERSON>", "connectHardwareChooseAccountsToConnect": "<PERSON> àwọn ibi-ìpamọ́ àpamọ́wọ́ láti so pọ̀.", "connectHardwareAccountsAddedInterpolated": "Ti so àwọn ibi-ìpamọ́ {{numOfAccounts}} pọ̀mọ", "connectHardwareAccountsStepOfSteps": "Ìgbésẹ̀ {{stepNum}} ti {{totalSteps}}", "connectHardwareMobile": "So Ìwé àkọọ́lẹ̀ pọ̀mọ", "connectHardwareMobileTitle": "So ohun èlò Ìwé àkọọ́lẹ̀ àpamọ́wọ́ rẹ pọ̀mọ", "connectHardwareMobileEnableBluetooth": "Mú Bluetooth ṣiṣẹ́", "connectHardwareMobileEnableBluetoothDescription": "Gba ìgbaniláàyè láàyè láti lo Bluetooth láti ní àsopọ̀", "connectHardwareMobileEnableBluetoothSettings": "Lọ sí àwọn Ètò láti gba Phantom láàyè láti lo ìgbaniláàyè Ipò àti àwọn Ẹ̀rọ tí ó wà Nítòsí.", "connectHardwareMobilePairWithDevice": "Sopọ̀ pẹ̀lú ẹ̀rọ Ìwé àkọọ́lẹ̀ rẹ", "connectHardwareMobilePairWithDeviceDescription": "Jẹ́ kí ẹ̀rọ rẹ tí ó wà nítòsí kí ó ní ìfihàn agbára tí ó dára jùlọ", "connectHardwareMobileConnectAccounts": "So àwọn ibi-ìpamọ́ pọ̀mọ", "connectHardwareMobileConnectAccountsDescription": "Àwa yóò wò fún iṣẹ́-ṣíṣe nínú èyíkéyìí àwọn ibi-ìpamọ́ tí o lè ti lò", "connectHardwareMobileConnectLedgerDevice": "So Ìwé àkọọ́lẹ̀ rẹ pọ̀mọ", "connectHardwareMobileLookingForDevices": "Ńwá àwọn ẹ̀rọ tí ó wà nítòsí...", "connectHardwareMobileLookingForDevicesDescription": "Jọ̀wọ́ so ohun èlò Ìwé àkọọ́lẹ̀ rẹ pọ̀mọ kí o sì rí wípé ó wà ní ṣíṣí.", "connectHardwareMobileFoundDeviceSingular": "Àwa ti rí Ìwé àkọọ́lẹ̀ ẹ̀rọ 1", "connectHardwareMobileFoundDevices": "Àwa rí Ìwé àkọọ́lẹ̀ {{numDevicesFound}} àwọn ẹ̀rọ", "connectHardwareMobileFoundDevicesDescription": "Yan Ìwé àkọọ́lẹ̀ ẹ̀rọ kan nísàlẹ̀ láti bẹ̀rẹ̀ sísopòmọ́.", "connectHardwareMobilePairingWith": "Sísopọ̀mọ́ {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Tẹ̀lé àwọn ìtọ́nisọ́nà lórí Ìwé àkọọ́lẹ̀ ẹ̀rọ rẹ nígbà sísopọ̀mọ́.", "connectHardwareMobilePairingFailed": "Sísopọ̀mọ́ yọrí sí rere", "connectHardwareMobilePairingFailedDescription": "<PERSON>ò lè sopọ̀mọ́ {{deviceName}}. Ri dájú wípé ẹ̀rọ rẹ wà ní ṣíṣí sílẹ̀.", "connectHardwareMobilePairingSuccessful": "Sísopọ̀mọ́ yọrí sí rere", "connectHardwareMobilePairingSuccessfulDescription": "O ti ṣe àṣeyọrí ní síso Ìwé àkọọ́lẹ̀ ẹ̀rọ rẹ pọ̀mọ.", "connectHardwareMobileOpenAppSingleChain": "Ṣí ohun-èlò {{chainName}} náà lórí Ìwé àkọọ́lẹ̀ rẹ", "connectHardwareMobileOpenAppDualChain": "Ṣí ohun-èlò {{chainName1}} àbí {{chainName2}} náà lórí Ìwé àkọọ́lẹ̀ rẹ", "connectHardwareMobileOpenAppDescription": "Ri dájú wípé ẹ̀rọ rẹ wà ní ṣíṣí.", "connectHardwareMobileStillCantFindDevice": "Ṣé o kò rí ẹ̀rọ rẹ síbẹ̀ bí?", "connectHardwareMobileLostConnection": "Ti pàdánù àsopọ̀", "connectHardwareMobileLostConnectionDescription": "Àwa ti pàdánù àsopọ̀ sí {{deviceName}}. Ri dájú wípé ẹ̀rọ rẹ wà ní ṣíṣí sílẹ̀, lẹ́yìn náà tún gbìyànjú si.", "connectHardwareMobileGenericLedgerDevice": "Ẹ̀rọ Ìwé àkọọ́lẹ̀", "connectHardwareMobileConnectDeviceSigning": "So {{deviceName}} rẹ pọ̀mọ", "connectHardwareMobileConnectDeviceSigningDescription": "Ṣí Ìwé àkọọ́lẹ̀ ẹ̀rọ rẹ kí o sì jẹ́ kí ó wà nítòsí.", "connectHardwareMobileBluetoothDisabled": "Ti mú Bluetooth má ṣiṣẹ́", "connectHardwareMobileBluetoothDisabledDescription": "Jọ̀wọ́ mú Bluetooth rẹ ṣiṣẹ́ kí o sì ri dájú wípé Ìwé àkọọ́lẹ̀ ẹ̀rọ rẹ wà ní ṣíṣí.", "connectHardwareMobileLearnMore": "Kọ́ ẹ̀kọ́ Síwájú si", "connectHardwareMobileBlindSigningDisabled": "Mú Bíbuwọ́lù Láìmọ kúlẹ̀kúlẹ̀ má ṣiṣẹ́", "connectHardwareMobileBlindSigningDisabledDescription": "<PERSON>i dájú wí<PERSON>é bíbuwọ́lù láìmọ kúlẹ̀kúlẹ̀ ń ṣiṣẹ́ lórí ẹ̀rọ rẹ.", "connectHardwareMobileConfirmSingleChain": "O nílò láti fọwọ́ sí ìdúnàádúrà lórí ohun èlò àpamọ́wọ́ rẹ. Rí dájú pé o wa ni ṣíṣí.", "metamaskExplainerBottomSheetHeader": "Òpó lórí a<PERSON> yìí ńṣiṣẹ́ pẹ̀lú Phantom", "metamaskExplainerBottomSheetSubheader": "Yan MetaMask láti ìbánisọ̀rọ̀ àsopọ̀ àpamọ́wọ́ láti tẹ̀síwájú.", "metamaskExplainerBottomSheetDontShowAgain": "Má<PERSON><PERSON> tún fihàn mọ́", "ledgerStatusNotConnected": "K<PERSON> so Ìwé àkọọ́lẹ̀ pọ̀mọ", "ledgerStatusConnectedInterpolated": "Ti so {{productName}} pọ̀mọ", "connectionClusterInterpolated": "O wa lori {{àkójọpọ̀}} lọ́wọ́ lọ́wọ́", "connectionClusterTestnetMode": "Ìwọ wà ní Ipò Testnẹti lọ́wọ́lọ́wọ́", "featureNotSupportedOnLocalNet": "Ẹ̀yà yìí kò ní àtìlẹyìn nígbàtí a bá ti mú Localnet Solana ṣíṣẹ́.", "readOnlyAccountBannerWarning": "Ò ńwo ibi-ìpamọ́ yìí", "depositAddress": "Gba Àdírẹ́sì", "depositAddressChainInterpolated": "Àdírẹ́sì {{chain}} Rẹ", "depositAssetDepositInterpolated": "Gba {{tokenSymbol}}", "depositAssetSecondaryText": "Àdírẹ́sì yìí ṣeé lo láti gba àwọn tókìnì tí ó bamu nìkan ṣoṣo.", "depositAssetTextInterpolated": "Lo àdírẹ́sì yìí láti gba àwọn tókìnì àti àwọn àkójọpọ̀ lórí <1>{{network}}</1>.", "depositAssetTransferFromExchange": "<PERSON><PERSON><PERSON> k<PERSON><PERSON><PERSON> l<PERSON>ti ibi pàṣípààrọ̀", "depositAssetShareAddress": "Sọ adirẹsi", "depositAssetBuyOrDeposit": "Ra tàbí kí ó Firánṣẹ́", "depositAssetBuyOrDepositDesc": "Fi owó sínú àpamọ́wọ́ rẹ kì ó lè bẹ̀rẹ̀", "depositAssetTransfer": "Gbé sí ibò<PERSON>í<PERSON>àn", "editAddressAddressAlreadyAdded": "<PERSON><PERSON> adirẹsi tẹ́lẹ̀", "editAddressAddressAlreadyExists": "Adirẹsi tí wà nbẹ tẹ́lẹ̀", "editAddressAddressIsRequired": "O nílò adirẹsi", "editAddressPrimaryText": "Ṣé àtúnṣe Adirẹsi", "editAddressRemove": "Y<PERSON> kúrò nínú Ìwe Adirẹsi", "editAddressToast": "Ti mú àdírẹ́sì dójú ìwọ̀n", "removeSavedAddressToast": "Ti yọ àdírẹ́sì kúrò", "exportSecretErrorGeneric": "Nkan ti ko tọ́ kan ti ṣẹlẹ̀, jọ̀wọ́ tún gbìyànjú tó bá ṣe díẹ̀", "exportSecretErrorIncorrectPassword": "Ọ̀rọ̀ ìgbaniwọlé tí kò tọ́", "exportSecretPassword": "Ọ̀rọ̀ ìgbaniwọlé", "exportSecretPrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aladani", "exportSecretSecretPhrase": "gbólóhùn ìkọ̀kọ̀", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "gbólóhùn ì<PERSON> ìkọ̀kọ̀", "exportSecretSelectYourAccount": "<PERSON> ibi-ìpamọ́ rẹ", "exportSecretShowPrivateKey": "<PERSON>́<PERSON><PERSON><PERSON><PERSON><PERSON> hàn", "exportSecretShowSecretRecoveryPhrase": "Ṣàfihàn gbólóhùn ìràpadà ìkọ̀kọ̀", "exportSecretShowSecret": "Fi {{secretNameText}} hàn", "exportSecretWarningPrimaryInterpolated": "Má <1>ṣe</1> sọ {{Ọ̀rọ̀Orúkọìkókó}} rẹ!", "exportSecretWarningSecondaryInterpolated": "Tí ẹnì kan bá ní {{Ọ̀rọ̀Orúkọìkòkò}} rẹ wọn má ni àṣẹ kíkún sì àpamọ́wọ́ rẹ.", "exportSecretOnlyWay": "{{secretNameText}} nìkan ni ọ̀nà kanṣosọ láti dá àpamọ́wọ́ rẹ padà", "exportSecretDoNotShow": "Máṣe jẹ́ kí ẹnikẹ́ni rí {{secretNameText}} rẹ", "exportSecretWillNotShare": "<PERSON><PERSON> kò ní ṣe àjọ<PERSON>ín {{secretNameText}} mi pẹ̀lú ẹnikẹ́ni, àti <PERSON> p<PERSON>.", "exportSecretNeverShare": "Máṣe ṣe àjọpín {{secretNameText}} rẹ pẹ̀lú ẹnikẹ́ni", "exportSecretYourPrivateKey": "K<PERSON><PERSON>k<PERSON><PERSON>r<PERSON><PERSON> Rẹ", "exportSecretYourSecretRecoveryPhrase": "Gbólóhùn ìràpadà ìkọ̀kọ̀ rẹ", "exportSecretResetPin": "Tún PIN rẹ ṣe", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "Ìrànlọ́wọ́", "gasUpTo": "<PERSON><PERSON><PERSON><PERSON> dé {{ amount }}", "timeDescription1hour": "Bíi wákàtí 1", "timeDescription30minutes": "Bíi 30 ìṣẹ́jú", "timeDescription10minutes": "Bíi ìṣẹ́jú 10", "timeDescription2minutes": "Bíi ìṣẹ́jú 2", "timeDescription30seconds": "Bíi 30 ìṣẹ́jú-àáyá", "timeDescription15seconds": "Bíi ìṣẹ́jú-àáyá 15", "timeDescription10seconds": "Bíi ìṣẹ́jú-àáyá 10", "timeDescription5seconds": "Bíi ìṣẹ́jú-àáyá 5", "timeDescriptionAbbrev1hour": "1hr", "timeDescriptionAbbrev30minutes": "30ìṣẹ́jú", "timeDescriptionAbbrev10minutes": "ìṣẹ́jú10", "timeDescriptionAbbrev2minutes": "ìṣẹ́jú2", "timeDescriptionAbbrev30seconds": "30s", "timeDescriptionAbbrev15seconds": "15s", "timeDescriptionAbbrev10seconds": "10s", "timeDescriptionAbbrev5seconds": "5s", "gasSlow": "<PERSON><PERSON><PERSON><PERSON>", "gasAverage": "<PERSON><PERSON><PERSON><PERSON>", "gasFast": "Ní k<PERSON>", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "Tún gbìyànjú <PERSON>", "homeErrorDescription": "Àṣìṣe kan wà nígbà tí ó fẹ́ gba àwọn ohun ìní rẹ padà. Jọ̀wọ́ tún tún ṣe kí ó si gbìyànjú sì", "homeErrorTitle": "<PERSON><PERSON><PERSON><PERSON> láti rí àwọn ohun ìní gbà", "homeManageTokenList": "Ṣé àkóso àkójọpọ̀ tokini", "interstitialDismissUnderstood": "Àgbóyé", "interstitialBaseWelcomeTitle": "Phantom ńṣe àtìlẹyìn Ìpìlẹ̀ nísìnyí!", "interstitialBaseWelcomeItemTitle_1": "Fi àwọn tókìnì ránṣẹ́, gb<PERSON><PERSON>, kí o sì ràá", "interstitialBaseWelcomeItemTitle_2": "Ṣe àwákiri Ìpìlẹ̀ ìlọ́lùpọ̀ọ́ náà", "interstitialBaseWelcomeItemTitle_3": "Tí kò léwu tí ó sì ní ìdáàbòbò", "interstitialBaseWelcomeItemDescription_1": "Gbé USDC àti ETH sí ibòmíràn kí o sì ra lórí Ìpìlẹ̀ nípa lílo {{paymentMethod}}, àwọn káàdì, tàbí Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Lo Phantom pẹ̀lú gbogbo àwọn ohun èlò DeFi àti NFT rẹ tí o yàn láàyò.", "interstitialBaseWelcomeItemDescription_3": "Wà láì léwu pẹ̀lú àtìlẹyìn Ìwé àkọọ́lẹ̀, wíwá ìfiráńṣẹ́ tí a kò bèèrè fún, àti fí<PERSON><PERSON>é ìdúnàádúrà.", "privacyPolicyChangedInterpolated": "Ìlànà Pípa àṣ<PERSON><PERSON><PERSON> mọ́ wa ti yípadà. <1>Mọ̀ síwájú si</1>", "bitcoinAddressTypesBodyTitle": "Àwọn oríṣiríṣi àdírẹ́sì Bitcoin", "bitcoinAddressTypesFeature1Title": "Nípa àwọn àdírẹ́sì Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom ńṣe àtìlẹyìn Native Seg<PERSON>t à<PERSON>, ọ̀kọ̀ọ̀kan pẹ̀lú iye owó rẹ̀ tó kù. O lè fi BTC tàbí àwọn Ìlànà ráńṣẹ́ pẹ̀lú irú àdírẹ́sì èyíkéyìí.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Àdírẹ́sì àtilẹ̀bá Bitcoin nínú Phantom. Tí ó ju Taproot lọ ṣùgbọ́n tí ó báramu pẹ̀lú àwọn àpamọ́wọ́ àti àwọn ibi pàṣípààrọ̀.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Tí ó dára jùlọ fún àwọn Ìlànà àti BRC-20s, tí owó rẹ̀ kò wọ́n jùlọ. Ṣàtúnṣe àwọn àdírẹ́sì nínú Tí o fẹ́ràn jù -> Àdírẹ́sì Bitcoin Tí o fẹ́ràn jù.", "headerTitleInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "<PERSON><PERSON><PERSON> ni àdírẹ́sì rẹ<1>{{addressType}}</1>.", "invalidChecksumTitle": "A ti mú gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí rẹ dójúìwọ̀n!", "invalidChecksumFeature1ExportPhrase": "Gbé Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí rẹ tuntun jáde", "invalidChecksumFeature1ExportPhraseDescription": "Jọ̀wọ́ fi gbólóhùn ọ̀rọ̀ ìgbaniwọlé tuntun rẹ tí ó jẹ́ àṣírí àti pẹ̀lú àwọn kọ́kọ́rọ́ aládàáni àwọn ibi-ìpamọ́ rẹ àtijọ́ pamọ́.", "invalidChecksumFeature2FundsAreSafe": "Àwọn owó rẹ wà ní ìpamọ́ tí ó sì ní ààbò", "invalidChecksumFeature2FundsAreSafeDescription": "Ìmúdójúìwọ̀n yìí jẹ́ aládàáṣiṣẹ́. <PERSON><PERSON> sí ẹnìkan ní Phantom tí ó mọ gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí rẹ tàbí tí ó ní ààyè sí àwọn owó rẹ.", "invalidChecksumFeature3LearnMore": "Kọ́ ẹ̀kọ́ síwájú si", "invalidChecksumFeature3LearnMoreDescription": "O ti ní gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí kò báramu pẹ̀lú ọ̀pọ̀lọpọ̀ àwọn àpamọ́wọ́. Ka <1>àyọkà ìrànlọ́wọ́ yìí</1> láti kọ́ ẹ̀kọ́ síwájú si nípa èyí.", "invalidChecksumBackUpSecretPhrase": "Fi gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí pamọ́", "migrationFailureTitle": "Nǹkankan kò lọ déédéé pẹ̀lú gbígbé ibi-ìpamọ́ rẹ lọ sí ibòmíràn", "migrationFailureFeature1": "Gbé gbólóhùn ọ̀rọ̀ ìgbaniwọlé rẹ tí ó jẹ́ àṣírí jáde", "migrationFailureFeature1Description": "Jọ̀wọ́ fi gbólóhùn ọ̀rọ̀ ìgbaniwọlé rẹ tí ó jẹ́ àṣírí pamọ́ ṣaájú bíbẹ̀rẹ̀.", "migrationFailureFeature2": "Bẹ̀rẹ̀ ní Phantom", "migrationFailureFeature2Description": "Ìwọ yóò nílò láti tún bíbẹ̀rẹ̀ sí Phantom ṣe láti wo ibi-ìpamọ́ rẹ.", "migrationFailureFeature3": "Kọ́ ẹ̀kọ́ síwájú si", "migrationFailureFeature3Description": "Ka <1>ày<PERSON><PERSON><PERSON> ìrànlọ́wọ́ yìí</1> láti kọ́ ẹ̀kọ́ síwájú si nípa èyí.", "migrationFailureContinueToOnboarding": "Tẹ̀síwájú pẹ̀lú bíbẹ̀rẹ̀", "migrationFailureUnableToFetchMnemonic": "A kò lè kó gbólóhùn ọ̀rọ̀ ìgbaniwọlé rẹ tí ó jẹ́ àṣírí jọpọ̀", "migrationFailureUnableToFetchMnemonicDescription": "Jọ̀wọ́ kànsí àtìlẹyìn kí o sì sọ àkọsílẹ̀ ohun-èlò kalẹ̀ láti mọ àṣìṣe kí o sì wá ojútùú sí ìṣòro", "migrationFailureContactSupport": "Kànsí àtìlẹyìn", "ledgerActionConfirm": "Jẹ́rìsíi lórí Ìwé àkọọlẹ̀ Nano rẹ", "ledgerActionErrorBlindSignDisabledPrimaryText": "Dá ibuwọlu ìfipamọ́ dúró", "ledgerActionErrorBlindSignDisabledSecondaryText": "Jọ̀wọ́ rí dájú pé o fi àyè gba ibuwọlu ìfipamọ́ lórí ohun èlò rẹ kì ó si tún igbesẹ na gbé", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "A ti tú ohun èlò ká nígbà tí iṣẹ́ nlọ lọ́wọ́", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Jọ̀wọ́ pa itẹsiwaju Phantom dé kí ó sì tún gbìyànjú iṣẹ́ yẹn sì", "ledgerActionErrorDeviceLockedPrimaryText": "A ti tí àwọn ohun èlò pa", "ledgerActionErrorDeviceLockedSecondaryText": "Jọ̀wọ́ sí ohun èlò kí ó sì tún gbìyànjú iṣẹ́ yẹn sì", "ledgerActionErrorHeader": "Àṣìṣe Iṣẹ́ Ìwé Àkọọlẹ̀", "ledgerActionErrorUserRejectionPrimaryText": "<PERSON><PERSON>n<PERSON><PERSON>ra tí aṣamulo kọ̀", "ledgerActionErrorUserRejectionSecondaryText": "Aṣamulo kọ iṣẹ́ na lórí ohun èlò", "ledgerActionNeedPermission": "O nílò i<PERSON>", "ledgerActionNeedToConfirm": "O nílò láti jẹ́rìsí ìdúnàádúrà lórí ohun èlò àpamọ́wọ́ rẹ. Ríi dájú wípé ó wà ní ṣíṣí sílẹ̀, lórí ohun èlo {{chainType}}.", "ledgerActionNeedToConfirmMany": "Ìwọ yóò nílò láti jẹ́rìsí àwọn ìdúnàádúrà {{numberOfTransactions}} lórí ohun èlò àpamọ́wọ́ rẹ. Ríi dájú wípé ó wà ní ṣíṣí sílẹ̀, lórí ohun èlo {{chainType}}.", "ledgerActionNeedToConfirmBlind": "O nílò láti jẹ́rìsí ìdúnàádúrà lórí ohun èlò àpamọ́wọ́ rẹ. Ríi dájú wípé ó wà ní ṣíṣí sílẹ̀, lórí ohun èlo {{chainType}}, kí o sì ri wípé jíjẹ́rìsí ìdókòwò láìmọ ohun gbogbo nípa rẹ̀ ńṣiṣẹ́.", "ledgerActionNeedToConfirmBlindMany": "Ìwọ yóò nílò láti jẹ́rìsí ìdúnàádúrà {{numberOfTransactions}} lórí ohun èlò àpamọ́wọ́ rẹ. Ríi dájú wípé ó wà ní ṣíṣí sílẹ̀, lórí ohun èlo {{chainType}}, kí o sì ri wípé jíjẹ́rìsí ìdókòwò láìmọ ohun gbogbo nípa rẹ̀ ńṣiṣẹ́.", "ledgerActionPleaseConnect": "Jọ̀wọ́ ṣe àsopọ̀ Ìwé Àkọọlẹ̀ Nano rẹ", "ledgerActionPleaseConnectAndConfirm": "Jọ̀wọ́ ṣé àsopọ̀ ohùn èlò àpamọ́wọ́ rẹ kì ó si ri dájú pé o wa ni ṣíṣí. Rí dájú pé o ti fọwọ́ sì àwọn ìgbàláàyè lórí ẹ̀rọ a ṣàwárí kiri rẹ.", "maxInputAmount": "<PERSON><PERSON>", "maxInputMax": "<PERSON> pọ̀jù", "notEnoughSolPrimaryText": "SOL tí kòtó", "notEnoughSolSecondaryText": "O kò ní SOL tí ó tó nínú àpamọ́wọ́ rẹ láti sanwó fún ìdúnàádúrà yìí. Jọ̀wọ́ fi sílẹ̀ síi kí o sì tún gbìyànjú si.", "insufficientBalancePrimaryText": "{{tokenSymbol}} tí kò tó", "insufficientBalanceSecondaryText": "O kò ní {{tokenSymbol}} tí ó tó nínú àpamọ́wọ́ rẹ fún ìdúnàádúrà yìí.", "insufficientBalanceRemaining": "Tí ó ṣẹ́kù", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "<PERSON><PERSON> àwọn tókìnì kò tó", "notEnoughSplTokensDescription": "O kò ní àwọn tókìnì tí ó tó nínú àpamọ́wọ́ rẹ fún ìdúnàádúrà yìí. Ìdúnàádúrà yìí yóò padà bí o bá ti filélẹ̀.", "transactionExpiredPrimaryText": "Ìdúnàádúrà ti kọjá <PERSON>", "transactionExpiredSecondaryText": "O ti dúró pẹ́ jù láti jẹ́rìsí ìdúnàádúrà náà tí ó sì ti kọjá àkókò. ìdúnàádúrà yìí yóò padà bí o bá fi lélẹ̀.", "transactionHasWarning": "Ìkìlọ̀ nípa ìdúnàádúrà", "tokens": "<PERSON><PERSON><PERSON><PERSON>", "notificationApplicationApprovalPermissionsAddressVerification": "Jẹ́rìsíi wí<PERSON>é ìwọ lo ni àdírẹ́sì yìí", "notificationApplicationApprovalPermissionsTransactionApproval": "<PERSON><PERSON><PERSON><PERSON> fún ìfọwọ́si fún àwọn idunọdura", "notificationApplicationApprovalPermissionsViewWalletActivity": "Wo iye tí ó kú nínú àpamọ́wọ́ rẹ àti bí ó ṣe ń ṣẹ", "notificationApplicationApprovalParagraphText": "Jíjẹ́rìí si yóò gba òpó lórí ayélujára yìí láàyè láti wo àwọn owó tí ó ṣẹ́kù àti iṣẹ́-ṣíṣe fún ibi-ìpamọ́ náà tí a ti yàn.", "notificationApplicationApprovalActionButtonConnect": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalActionButtonSignIn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "Gba saiti láàyè láti w<PERSON>?", "notificationApplicationApprovalAutoConfirm": "Jẹ́rìsí àwọn ìdúnàádúrà láìsí ìlọ́wọ́si", "notificationApplicationApprovalConnectDisclaimer": "Wọ inú àwọn saiti tí ó fọkàn tán nikan", "notificationApplicationApprovalSignInDisclaimer": "Buwọ<PERSON><PERSON><PERSON> wọlé si àwọn òpó lórí a<PERSON>éluj<PERSON>ra tí ó fọkàn tán nikan", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Ààyè lórí a<PERSON>u<PERSON>ra y<PERSON>í léwu láti lò tí ó sì lè gbìyànjú láti jí àwọn owó rẹ.", "notificationApplicationApprovalConnectUnknownApp": "Tí a kò mọ̀", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "<PERSON>ò lè sopọ̀ mọ́ ohun-èlò", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "<PERSON>un-<PERSON><PERSON><PERSON> y<PERSON>í ńg<PERSON>njú láti láti sopọ̀ mọ́ {{appNetworkName}}, ṣùgbọ́n ó ti yan {{phantomNetworkName}}.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "<PERSON><PERSON><PERSON> {{networkName}}, lọ sí Àwọn ètò <PERSON> → <PERSON><PERSON><PERSON> Testnẹti.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Nẹtiwọki tí a kò mọ̀", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Sísopọ̀mọ́ àwọn ohun èlò alágbèkáá mìíràn ní a kò ṣe àtìlẹyìn fún lọ́wọ́lọ́wọ́ nípa Ìwé àkọọ́lẹ̀.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Jọ̀wọ́ yipadà sí ibi-ìpamọ́ tí kìí ṣe Ìwé àkọọ́lẹ̀ tàbí lo ohun èlò wíwá ayélujára kiri nínú ohun èlò kí o sì sì tún gbìyànjú si.", "notificationSignatureRequestConfirmTransaction": "Jẹ́rìsí ìdúnàádú<PERSON>à", "notificationSignatureRequestConfirmTransactionCapitalized": "Jẹ́rìsí Ìdúnàádúrà", "notificationSignatureRequestConfirmTransactions": "Jẹ́rìsí àwọn ìd<PERSON>", "notificationSignatureRequestConfirmTransactionsCapitalized": "Jẹ́rìsí àwọn Ìdúnàádúrà", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON> bè<PERSON><PERSON> fún Ìbuwọ́lù", "notificationMessageHeader": "Ìfiránṣẹ́", "notificationMessageCopied": "Ti da ìfiránṣẹ́ kọ", "notificationAutoConfirm": "Jẹ́rìsí Láìsí <PERSON>lọ́wọ́si", "notificationAutoConfirmOff": "Paá", "notificationAutoConfirmOn": "Tàn-<PERSON>n", "notificationConfirmFooter": "Jẹ́rìsí bí o bá fọkàntán òpó lórí ayélujára yìí nìkan.", "notificationEstimatedTime": "Iye àkókò Tí a gbèrò", "notificationPermissionRequestText": "Èyí jẹ́ ìb<PERSON><PERSON><PERSON><PERSON> ì<PERSON> nìkan. Ìdúnàádúrà náà lè má ṣẹlẹ̀ lẹ́sẹ̀kẹsẹ̀.", "notificationBalanceChangesText": "A ti ṣírò àwọn àyípadà iye owó tí ó kù. Iye owó àti àwọn ohun ìní tí ó nííṣe ni a kò múdánilójú.", "notificationContractAddress": "Àdírẹ́sì Àd<PERSON><PERSON>ùn", "notificationAdvancedDetailsText": "Tí ó jinlẹ̀", "notificationUnableToSimulateWarningText": "A kò lè ṣírò àwọn àyípadà tí ó ti wà lórí iye owó tí ó kú lọ́wọ́lọ́wọ́. O lè gbìyànjú si nígbàmíì, tàbí jẹ́rìsí bí o bá fi ọkàn tan òpó lórí ayélujára yìí.", "notificationSignMessageParagraphText": "Bíbuwọ́lu ìfiránṣẹ́ yìí yóò fi níni ibi-ìpamọ́ tí ó ti yàn hàn.", "notificationSignatureRequestScanFailedDescription": "<PERSON><PERSON> lè ya ìfiráńṣẹ́ nítorí ọ̀ràn ìdábòbòbò. Jọ̀wọ́ tẹ̀síwájú pẹ̀lú ìfura.", "notificationFailedToScan": "Ti kùnà láti ṣ'àfarawé àwọn àbájáde ìbéèrè yìí.\nJíjẹ́rìí si léwu tí ó sì lè fa ìpàdánù.", "notificationScanLoading": "Ńyẹ Ìbéèrè wò", "notificationTransactionApprovalActionButtonConfirm": "Jẹ́rìsí", "notificationTransactionApprovalActionButtonBack": "Padà", "notificationTransactionApprovalEstimatedChanges": "<PERSON><PERSON><PERSON><PERSON> Tí a ti gbèrò", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Àwọn iye tí a ti lérò to dá lórí àwọn afijọ idunọdura tí wọn kò sì ní ẹ̀rí", "notificationTransactionApprovalHideAdvancedDetails": "Fi àwọn àlàyé nípa idunọdura tí a ti ṣe ṣíwájú pamọ́", "notificationTransactionApprovalNetworkFee": "Owo Nẹtiwọki", "notificationTransactionApprovalNetwork": "Nẹtiwọki", "notificationTransactionApprovalEstimatedTime": "Iye àkókò tí a gbèrò", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "A kò rí àwọn àyípadà tí ó ńní ipá lórí níní ohun àlùmọ́ọ̀ní", "notificationTransactionApprovalSolanaAmountRequired": "Ìyè owó tí nẹtiwọki Solana nílò láti fi ṣeto idunọdura na", "notificationTransactionApprovalUnableToSimulate": "Kò lè ṣe àfarawé rẹ̀. Ríi dájú wípé o ní ìgbẹkẹ̀lé nínú ààyè lórí ayélujára yìí níwọ̀n wípé fífi ọwọ́ si lè ṣe okùnfà pípàdánù àwọn owó.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "O <PERSON>é alairi àwọn ì<PERSON>pad<PERSON> lórí iye owó tí ó kù", "notificationTransactionApprovalViewAdvancedDetails": "Wo àwọn àlàyé nípa idunọdura tí a ti ṣe ṣíwájú", "notificationTransactionApprovalKnownMalicious": "Idunọ<PERSON>ra yí ni àwọn ẹtan. Ibuwọlu rẹ lè jásí ì<PERSON>àd<PERSON>ù àwọn owó.", "notificationTransactionApprovalSuspectedMalicious": "A fura pé idunọdura yí ni àwọn ẹtan. Ibuwọlu rẹ lè jásí ìpàdánù àwọn owó.", "notificationTransactionApprovalNetworkFeeHighWarning": "Owó nẹtiwọki ga nítorí èrò pọ̀ lórí nẹtiwọki.", "notificationTransactionERC20ApprovalDescription": "Jíjẹ́rìsí yóò gba ohun èlò yìí láàyè láti wọlé sí iye owó rẹ tí ó kù nígbàkigbà, títí dé gbèdéke tí ó wà nísàlẹ̀.", "notificationTransactionERC20ApprovalContractAddress": "Àdírẹ́sì Àd<PERSON><PERSON>ùn", "notificationTransactionERC20Unlimited": "tí kò ní gbè<PERSON><PERSON>ke", "notificationTransactionERC20ApprovalTitle": "Fọwọ́ sí níná {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Fagilé níná {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Fagilé níní à<PERSON>è sí {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Gbogbo {{tokenSymbol}} rẹ", "notificationIncorrectModeTitle": "I<PERSON>ò tí kò tọ́", "notificationIncorrectModeInTestnetTitle": "Ìwọ wà ní ipò Testnẹti", "notificationIncorrectModeNotInTestnetTitle": "Ìwọ kò sí ní ipò Testnẹti", "notificationIncorrectModeInTestnetDescription": "{{origin}} ńgb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> láti lo mainnẹti kan, ṣùgb<PERSON>́n o wà ní ipò Testnẹti", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} ńgb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> láti lo testnẹti kan, ṣùgb<PERSON>́n o kò sí ní ipò Testnẹti", "notificationIncorrectModeInTestnetProceed": "<PERSON><PERSON><PERSON> tẹ̀síwájú, pa ip<PERSON> Testnẹti.", "notificationIncorrectModeNotInTestnetProceed": "<PERSON><PERSON><PERSON> tẹ̀síwájú, tan ip<PERSON> Testnẹti.", "notificationIncorrectEIP712ChainId": "Àwa ti dí ọ́ lọ́wọ́ láti má lè buwọlù ìfiráńṣẹ́ kan tí a kò gbèrò fún nẹtiwọki náà tí o ti ní àsopọ̀ pẹ̀lú lọ́wọ́ lọ́wọ́", "notificationIncorrectEIP712ChainIdDescription": "Ìfiráńṣẹ́ ti béèrè {{messageChainId}}, ìwọ ti ní àsopọ̀ sí {{connectedChainId}}", "notificationUnsupportedNetwork": "Nẹtiwọki tí kò ní àtìlẹyìn", "notificationUnsupportedNetworkDescription": "Òpó lórí a<PERSON> yìí ńgb<PERSON>yànjú láti lo nẹtiwọki èyítí Phantom kò ṣe àtilẹyìn fún lọ́wọ́lọ́wọ́.", "notificationUnsupportedNetworkDescriptionInterpolated": "<PERSON><PERSON><PERSON> tẹ̀síwájú pẹ̀lú ì<PERSON>ín à<PERSON>kún tí ó yàtọ̀, pa àwọn <1><PERSON><PERSON><PERSON> → Àpamọ́wọ́ Ohun èlò Àtilẹ̀bá, kí o sì yan B<PERSON><PERSON><PERSON><PERSON>ígbàgbogbo</1>. Lẹ́yìn náà tun ojú-ìwé náà sọjí kí o sì tun sopọ̀mọ.", "notificationUnsupportedAccount": "Ibi-ìpamọ́ tí kò ní àtìlẹyìn", "notificationUnsupportedAccountDescription": "Òpó lórí a<PERSON>lu<PERSON>ra yìí ńg<PERSON>yànjú láti lo {{targetChainType}}, è<PERSON><PERSON><PERSON><PERSON> ibi-ìpamọ́ {{chainType}} yìí kò ṣe àtìlẹyìn fún.", "notificationUnsupportedAccountDescription2": "<PERSON>í padàsí ibi-ìpamọ́ kan láti gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó bamu kan tàbí kọ́kọ́rọ́ aládàáni kí o sì tún gbìyànjú.", "notificationInvalidTransaction": "Ìdúnàádúrà tí kò ṣíṣe", "notificationInvalidTransactionDescription": "Ìdúnàádúrà yìí tí a gbà láti ohun èlò yìí kò ṣiṣẹ́ déédé kí a sì má fi lélẹ̀. Jọ̀wọ́ kàn sí olùgbéejáde ohun èlò yìí láti jábọ̀ ìṣòro yìí pẹ̀lú wọn.", "notificationCopyTransactionText": "Da ìdúnàád<PERSON><PERSON><PERSON> kọ", "notificationTransactionCopied": "<PERSON><PERSON> da ìdúnàád<PERSON><PERSON> k<PERSON>", "onboardingImportOptionsPageTitle": "<PERSON><PERSON><PERSON>́wọ́ kan wọlé", "onboardingImportOptionsPageSubtitle": "Gbé àpamọ́wọ́ kan tí ó ti wà tẹ́lẹ̀ wọlé pẹ̀lú gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí, kọ́k<PERSON>́r<PERSON><PERSON> alá<PERSON>, tàbí ohun èlò àpamọ́wọ́ rẹ.", "onboardingImportPrivateKeyPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>áni kan wọlé", "onboardingImportPrivateKeyPageSubtitle": "G<PERSON><PERSON> àpamọ́wọ́ oní-ṣéènì kan tí ó ti wà tẹ́lẹ̀ wọlé", "onboardingCreatePassword": "Da ọ̀rọ̀ ìgbaniwọlé silẹ", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<PERSON> fọwọ́ sì <1>Àwọn Òfin Ìṣe</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Jẹ́rìsí ọ̀rọ̀ ìgbaniwọlé", "onboardingCreatePasswordDescription": "O má lo eleyi láti ṣí àpamọ́wọ́ rẹ.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Gbólóhùn ìràpadà ìkọ̀kọ̀ tí kò ṣiṣẹ́", "onboardingCreatePasswordPasswordPlaceholder": "Ọ̀rọ̀ ìgbaniwọlé", "onboardingCreatePasswordPasswordStrengthWeak": "Ailágb<PERSON>ra", "onboardingCreatePasswordPasswordStrengthMedium": "<PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthStrong": "Lágbára", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Mo fi Gbólóhùn Ìràpadà Ikọ̀kọ̀ mi pamọ́", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Gbólóhùn Ìràpadà Ìkọ̀kọ̀", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Gbólóhùn yí NÌKAN ni ọ̀nà tí ó lè fi gba àpamọ́wọ́ rẹ padà. MÁ ṣe sọ́ fún ẹnikẹ́ni!", "onboardingImportWallet": "<PERSON><PERSON><PERSON>wọ<PERSON> wọlé", "onboardingImportWalletImportExistingWallet": "Dá àpamọ́wọ́ tí ó nlo lọ́wọ́ lọ́wọ́ padà pẹ̀lú gbólóhùn ìràpadà ìkọ̀kọ̀ òní ọ̀rọ̀ méjìlá tàbí mẹrin lè lógún.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON>mọ́wọ́ pada", "onboardingImportWalletSecretRecoveryPhrase": "Gbólóhùn Ìràpadà Ìkọ̀kọ̀", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Gbólóhùn Ìràpadà Ìkọ̀kọ̀ tí kò ṣiṣẹ́", "onboardingImportWalletIHaveWords": "Mo ní {{iyeAwọnọ̀rọ̀}-ọ̀rọ̀ gbólóhùn ìràpadà", "onboardingImportWalletIncorrectOrMisspelledWord": "Ọ̀rọ̀ {{wordIndex}} kò tọ́ tàbí a ṣìí kọ", "onboardingImportWalletIncorrectOrMisspelledWords": "Àwọn ọ̀rọ̀ {{wordIndexes}} kò tọ́ tàbí a ṣì wọ́n kọ", "onboardingImportWalletScrollDown": "Yi lọ sí ìsàlẹ̀", "onboardingImportWalletScrollUp": "Yi lọ sí òkè", "onboardingSelectAccountsImportAccounts": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> wọ lé", "onboardingSelectAccountsImportAccountsDescription": "<PERSON> àwọn akanti àpamọ́wọ́ tí ó fẹ́ gbé wọlé.", "onboardingSelectAccountsImportSelectedAccounts": "<PERSON><PERSON><PERSON> àwọn <PERSON> tí ó Yan wọlé", "onboardingSelectAccountsFindMoreAccounts": "Wá àwọn ibi-ìpamọ́ síwájú si", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON> rí <PERSON>wọn ibi-ìpamọ́ kankan", "onboardingSelectAccountsNoOfAccountsSelected": "Ti yan àwọn ibi-<PERSON> {{numOfAccounts}}", "onboardingSelectAccountSelectAllText": "Yan Gbogbo rẹ̀", "onboardingAdditionalPermissionsTitle": "<PERSON> àwọn ohun èlò pẹ̀lú Phantom", "onboardingAdditionalPermissionsSubtitle": "Fún ìrírí lílo ohun èlò tí ó dára jùlọ, a gbà gbígba Phantom láàyè níyànjú láti ka kí ó sì yí détà padà lórí gbogbo òpó lórí ayélujára.", "interstitialAdditionalPermissionsTitle": "<PERSON> àwọn ohun èlò pẹ̀lú Phantom", "interstitialAdditionalPermissionsSubtitle": "<PERSON><PERSON>ti tẹ̀síwájú pẹ̀lú ìrírí rẹ nípa lílo àwọn ohun èlò láìsí ìdáwọ́dúró, a gbà gbígba Phantom láàyè níyànjú láti ka kí ó sì yí détà padà lórí gbogbo òpó lórí ayélujára.", "recentActivityPrimaryText": "<PERSON><PERSON> tó ṣẹlẹ̀ Laipẹ", "removeAccountActionButtonRemove": "<PERSON><PERSON> k<PERSON>", "removeAccountRemoveWallet": "<PERSON><PERSON> ibi-ìpamọ́ kúrò", "removeAccountInterpolated": "Y<PERSON> {{accountName}} k<PERSON>r<PERSON>", "removeAccountWarningLedger": "Bí ó tilẹ̀ jẹ́ pé ó ń yọ àpamọ́wọ́ yí kúrò nínú Phantom, ó má ni anfaani lati tún ṣe afikun rẹ padà nípa lílo ṣiṣàn \"Àsopọ̀ Ohun èlò Àpamọ́wọ́\".", "removeAccountWarningSeedVault": "Bí ó tilẹ̀ jẹ́ wípé ó ńyọ àpamọ́wọ́ yìí kúrò nínú Phantom, ìwọ yóò lè tún ṣe àfikún rẹ padà nípa lílo ṣiṣàn \"So Ọ̀rọ̀ ìgbaniwọlé Àpamọ́wọ́ pọ̀mọ́ Ibi ìfi nǹkan pamọ́ sí\".", "removeAccountWarningPrivateKey": "Lẹ́yìn tí ó bá ti yọ àpamọ́wọ́ yí, Phantom kò ní lè bá ẹ gba padà. Rí dájú pé o ṣe ìfipamọ́ kọ́kọ́rọ́ aladani rẹ.", "removeAccountWarningSeed": "Bí ó ti lẹ jẹ́ pé ó ń yọ àpamọ́wọ́ yí kúrò nínú Phantom, ó má ni anfaani lati le tún rí gbà padà nípa lílo ohun èlò ìrántí rẹ tàbí àpamọ́wọ́ miran.", "removeAccountWarningReadOnly": "Pípa ibi-ìpamọ́ yìí rẹ́ kò ní fa ìdíwọ́ fún àpamọ́wọ́ rẹ, nítorí ó jẹ́ àpamọ́wọ́ fún wíwò nìkan.", "removeSeedPrimaryText": "Ńyọ Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí {{number}} kúrò", "removeSeedSecondaryText": "<PERSON><PERSON><PERSON> yóò yọ gbogbo àwọn ibi-ìpamọ́ tí ó wà lọ́wọ́ lọ́wọ́ kúrò nínú Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí {{number}}. Ríi dájú wípé o ní gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí tí ó wà lọ́wọ́ lọ́wọ́ rẹ ni ìpamọ́.", "resetSeedPrimaryText": "Ṣé àtúnṣe ohun-èlò pẹ̀lú gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí tuntun", "resetSeedSecondaryText": "<PERSON><PERSON><PERSON> yóò yọ gbogbo àwọn àpamọ́wọ́ tí ó wà lọ́wọ́ lọ́wọ́ kúrò tí yóò sì fi àwọn tuntun dípò wọn. Ríi dájú wípé o ní gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí àti àwọn kọ́kọ́rọ́ aládàáni rẹ tí ó wà lọ́wọ́ lọ́wọ́ ni ìpamọ́.", "resetAppPrimaryText": "Tún ohun-è<PERSON>ò <PERSON>ò kí o sì paárẹ́", "resetAppSecondaryText": "<PERSON><PERSON><PERSON> yóò yọ gbogbo àwọn ibi-ìpamọ́ àti détà tí ó wà lọ́wọ́ lọ́wọ́ kúrò. Ríi dájú wípé o ní gbólóhùn ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ àṣírí àti àwọn kọ́kọ́rọ́ aládàáni rẹ wà ni ìpamọ́.", "richTransactionsDays": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionsToday": "Òní", "richTransactionsYesterday": "Àná", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "Ibaṣepọ Appu", "richTransactionDetailAt": "ní", "richTransactionDetailBid": "Fowó sílẹ̀", "richTransactionDetailBidDetails": "<PERSON><PERSON><PERSON><PERSON> Ìfowósílẹ̀", "richTransactionDetailBought": "<PERSON><PERSON>", "richTransactionDetailBurned": "Ti jóná", "richTransactionDetailCancelBid": "Fagilé Ìfowósílẹ̀", "richTransactionDetailCompleted": "O ti parí", "richTransactionDetailConfirmed": "Jẹ́rìsí", "richTransactionDetailDate": "Ọjọ́", "richTransactionDetailFailed": "Ìjákulẹ̀", "richTransactionDetailFrom": "<PERSON><PERSON><PERSON>", "richTransactionDetailItem": "Nkan", "richTransactionDetailListed": "Ówà l'ori àtẹ", "richTransactionDetailListingDetails": "<PERSON><PERSON><PERSON><PERSON> orí àtẹ", "richTransactionDetailListingPrice": "<PERSON><PERSON> owó <PERSON>́j<PERSON> orí àtẹ", "richTransactionDetailMarketplace": "<PERSON><PERSON>", "richTransactionDetailNetworkFee": "Owo Nẹtiwọki", "richTransactionDetailOriginalListingPrice": "Iye owó Àkójọ orí àtẹ tí ó jẹ́ ojúlówó", "richTransactionDetailPending": "Wíwà ní ìdá<PERSON>ú<PERSON>ó", "richTransactionDetailPrice": "<PERSON><PERSON> ow<PERSON>", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailPurchaseDetails": "<PERSON><PERSON><PERSON><PERSON> Ọjà rírà", "richTransactionDetailRebate": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> owó pad<PERSON>", "richTransactionDetailReceived": "Gbígbà", "richTransactionDetailSaleDetails": "<PERSON><PERSON><PERSON><PERSON> Ọjà títà", "richTransactionDetailSent": "Firanṣẹ́", "richTransactionDetailSold": "A ti taa", "richTransactionDetailStaked": "<PERSON>ow<PERSON> si", "richTransactionDetailStatus": "<PERSON><PERSON><PERSON>", "richTransactionDetailSwap": "Pàrọ̀", "richTransactionDetailSwapDetails": "Pàrọ̀ àw<PERSON>n <PERSON>", "richTransactionDetailTo": "Sí", "richTransactionDetailTokenSwap": "Ìpàrọ̀ Tokini", "richTransactionDetailUnknownNFT": "NFT Aimọ", "richTransactionDetailUnlisted": "Kò sí lórí àtẹ", "richTransactionDetailUnstaked": "<PERSON><PERSON> ow<PERSON> si", "richTransactionDetailValidator": "<PERSON><PERSON><PERSON><PERSON><PERSON>w<PERSON>s<PERSON>", "richTransactionDetailViewOnExplorer": "<PERSON><PERSON><PERSON> l<PERSON> {{explorer}}", "richTransactionDetailWithdrawStake": "<PERSON><PERSON>", "richTransactionDetailYouPaid": "O san owó", "richTransactionDetailYouReceived": "O gba", "richTransactionDetailUnwrapDetails": "Ṣí àwọn <PERSON> sílẹ̀", "richTransactionDetailTokenUnwrap": "Ṣí tókìnì sílẹ̀", "activityItemsRefreshFailed": "Ti kùnà láti kó àwọn ìdúnàádúrà tuntun jọpọ̀.", "activityItemsPagingFailed": "Ti kùnà láti kó àwọn ìdúnàádúrà àtijọ́ jọpọ̀.", "activityItemsTestnetNotAvailable": "<PERSON><PERSON> sí ìtàn ìdúnàádúrà Testnẹti ní àkókò yìí", "historyUnknownDappName": "Tí a kò mọ̀", "historyStatusSucceeded": "Ti yọrí sí rere", "historyNetwork": "Nẹtiwọki", "historyAttemptedAmount": "Iye tí ó g<PERSON>njú", "historyAmount": "<PERSON><PERSON>", "sendAddressBookButtonLabel": "Ìwé Adirẹsi", "addressBookSelectAddressBook": "Ìwé Adirẹsi", "sendAddressBookNoAddressesSaved": "A o fi adirẹsi kankan pamọ́", "sendAddressBookRecentlyUsed": "<PERSON>yi ti o lo laipẹ", "addressBookSelectRecentlyUsed": "<PERSON>yi ti o lo Laipẹ", "sendConfirmationLabel": "Lébẹ̀lì", "sendConfirmationMessage": "Ìfiránṣẹ̀", "sendConfirmationNetworkFee": "Owo Nẹtiwọki", "sendConfirmationPrimaryText": "Jẹ́rìsí Ìfiránṣẹ́", "sendWarning_INSUFFICIENT_FUNDS": "Owó tí kò tó, ó ṣeé ṣe kí ìdúnàádúrà yìí kùnà bí o bá filélẹ̀.", "sendFungibleSummaryNetwork": "Nẹtiwọki", "sendFungibleSummaryNetworkFee": "Owó nẹtiwọki", "sendFungibleSummaryEstimatedTime": "Iye àkókò tí a gbèrò", "sendFungiblePendingEstimatedTime": "Àkókò Tí a gbèrò", "sendFungibleSummaryEstimatedTimeDescription": "Yíyára ìdúnàádúrà Ethereum dá lórí ọ̀pọ̀lọpọ̀ àwọn okùnfà. Ìwọ lè jẹ́ kí wọ́n yára nípa títẹ “Owó Nẹtiwọki”.", "sendSummaryBitcoinPendingTxTitle": "Kò lè fi gbígbé sí ibòmíràn lélẹ̀", "sendSummaryBitcoinPendingTxDescription": "O kàn le ní gbígbé BTC kan sí ibòmíràn tí ó ńlọ lọ́wọ́ ní àkókò kan. Jọ̀wọ́ dúró títí tí yóò fi parí láti fi gbígbé sí ibòmíràn tuntun lélẹ̀.", "sendFungibleSatProtectionTitle": "Fífiráńṣẹ́ pẹ̀lú Ìdádààbò Satalaiti", "sendFungibleSatProtectionExplainer": "Phantom <PERSON>ri dájú wípé àwọn Ìlànà àti BRC20 rẹ ni a kò ní lò fún owó ìdúnàádúrà tàbí fífi Bitcoin ráńṣẹ́.", "sendFungibleTransferFee": "<PERSON>ye owó fífi tó<PERSON>nì ráńṣẹ́", "sendFungibleTransferFeeToolTip": "Olùṣẹ̀dá tókìnì yìí ńgba owó lórí fífi ráńṣẹ́ kọ̀ọ̀kan. <PERSON><PERSON><PERSON> kìí ṣe owó tí Phantom file tàbí gbà.", "sendFungibleInterestBearingPercent": "<PERSON>ye Àfikún èrè L<PERSON>́wọ́ lọ́wọ́", "sendFungibleNonTransferable": "Kò ṣe é gbé sí ibòmíràn", "sendFungibleNonTransferableToolTip": "Tókìnì yìí kò ṣeé gbé lọ sí ibi-ìpamọ́ mìíràn.", "sendFungibleNonTransferableYes": "Bẹ́ẹ̀ni", "sendStatusErrorMessageInterpolated": "Àṣìṣe kan wà nígbà tí a fẹ́ fi àwọn tokini ránṣẹ́ sí <1>{{uiEnitiomagba}}</1>", "sendStatusErrorMessageInsufficientBalance": "O kò ní owó tí ó tó láti parí ìdúnàádúrà náà.", "sendStatusErrorTitle": "Ko ṣé fi ránṣẹ́", "sendStatusLoadingTitle": "<PERSON><PERSON> ránṣẹ́...", "sendStatusSuccessMessageInterpolated": "A ṣe àṣeyọr<PERSON> láti fi àwọn tokini rẹ ránṣẹ́ sì <1>{{uiEnitiomagba}}</1>", "sendStatusSuccessTitle": "A ti fi ránṣẹ́!", "sendStatusConfirmedSuccessTitle": "Ti fi ránṣẹ́!", "sendStatusSubmittedSuccessTitle": "Ti fi Ìdúnàádúrà lélẹ̀", "sendStatusEstimatedTransactionTime": "<PERSON>ye <PERSON> Ìdúnàádúrà Tí a gbèrò: {{time}}", "sendStatusViewTransaction": "<PERSON><PERSON>", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> sí <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> tí lọ pẹ̀lú àṣeyọrí sí <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> tí lọ pẹ̀lú àṣeyọrí sí <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> ti kùnà láti lọ sí <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{code}}", "sendFormErrorInsufficientBalance": "Iye tó kù tí kò tó", "sendFormErrorEmptyAmount": "Iye tí ó nílò", "sendFormInvalidAddress": "Àdírẹ́sì {{assetName}} kò wúlò", "sendFormInvalidUsernameOrAddress": "Orúkọ olùmúlò tàbí àdírẹ́sì tí kò ṣiṣẹ́", "sendFormErrorInvalidSolanaAddress": "Adirẹsi Solana tí kò ṣiṣẹ́", "sendFormErrorInvalidTwitterHandle": "Orúkọ Twitter yí kò sì ní àkọọlẹ̀", "sendFormErrorInvalidDomain": "Àṣẹ yi kò sì nínú àkọọlẹ̀", "sendFormErrorInvalidUsername": "Orúkọ olùmúlò yìí kò sì nínú àkọọlẹ̀", "sendFormErrorMinRequiredInterpolated": "O kéré jù {{Ìyètokereju}} {{<PERSON><PERSON><PERSON><PERSON>}} nilo", "sendRecipientTextareaPlaceholder": "Adirẹsi SOL ẹni tó ń gba", "sendRecipientTextAreaPlaceholder2": "Àdírẹ́sì {{symbol}} olùgbà", "sendMemoOptional": "Ìfiráńṣẹ́ tí a sopọ̀ mọ (kò pọn dandan)", "sendMemo": "Ìfiráńṣẹ́ tí a sopọ̀ mọ", "sendOptional": "k<PERSON> pọn dandan", "settings": "<PERSON><PERSON><PERSON><PERSON>", "settingsDapps": "dApps", "settingsSelectedAccount": "Ibi-ìpamọ́ tí a ti yàn", "settingsAddressBookNoLabel": "<PERSON><PERSON> Lébẹ̀lì", "settingsAddressBookPrimary": "Ìwé Adirẹsi", "settingsAddressBookRecentlyUsed": "<PERSON>yi ti o lo Laipẹ", "settingsAddressBookSecondary": "Ṣàkóso àwọn adirẹsi tí ó má ń lò nígbà gbogbo", "settingsAutoLockTimerPrimary": "<PERSON><PERSON>-Alaiṣefọ̀wọ́yi", "settingsAutoLockTimerSecondary": "Pàrọ̀ iye ìgbà aago titipa-alaiṣefọwọyi rẹ", "settingsChangeLanguagePrimary": "Pàrọ̀ Èdè", "settingsChangeLanguageSecondary": "Pàrọ̀ èdè tí ó ń fara hàn", "settingsChangeNetworkPrimary": "Pàrọ̀ Nẹtiwọki", "settingsChangeNetworkSecondary": "Ṣe atunto àwọn ètò nẹtiwọki rẹ", "settingsChangePasswordPrimary": "Pàrọ̀ Ọ̀rọ̀ Ìgbaniwọlé", "settingsChangePasswordSecondary": "Pàrọ̀ ọ̀rọ̀ ìgbaniwọlé tí ó fi ń tí ojú àwòrán rẹ", "settingsCompleteBetaSurvey": "Parí Ìwádìí tí ó wà Ṣaájú ìfilọ́lẹ̀", "settingsDisplayLanguage": "<PERSON><PERSON><PERSON> n <PERSON>n", "settingsErrorCannotExportLedgerPrivateKey": "Kò lè gbé Ìwé Àkọsílẹ̀ kọ́kọ́rọ́ aladani jade", "settingsErrorCannotRemoveAllWallets": "<PERSON>ò lè yọ gbogbo àwọn ibi-ìpamọ́ kúrò", "settingsExportPrivateKey": "<PERSON>́<PERSON><PERSON><PERSON><PERSON><PERSON> hàn", "settingsNetworkMainnetBeta": "Beta Mainnẹti", "settingsNetworkTestnet": "Testnẹti", "settingsNetworkDevnet": "Devnẹti", "settingsNetworkLocalhost": "<PERSON><PERSON><PERSON>", "settingsNetworkPhantomRPC": "Nẹtiwọki Phantom RPC", "settingsTestNetworks": "<PERSON><PERSON>w<PERSON>n Nẹtiwọki wò", "settingsUseCustomNetworks": "Lo àwọn Nẹtiwọki Àkànṣe", "settingsTestnetMode": "Ipò Testnẹti", "settingsTestnetModeDescription": "Nípá lórí àwọn iye tí ó ku àti àwọn àsopọ̀ ohun èlò.", "settingsWebViewDebugging": "Mímọ àṣìṣe Wíwo Àyélujára kí o sì wá ojútùú si", "settingsWebViewDebuggingDescription": "Ńgbà ọ́ láàyè láti yẹ ohun-èlò inú wíwá àyélujára kiri wò àti mímọ àṣìṣe kí o sì wá ojútùú si.", "settingsTestNetworksInfo": "Yíyí sí nẹtiwọki èyíkẹ́yìí tí kò tíì bẹ̀rẹ̀ iṣẹ́ lẹ́kùnrẹ́rẹ́, <PERSON>net, ni ó wà fún àwọn èrèdí dídànwò nìkan. Jọ̀wọ́ kíyèsí wípé àwọn tókìnì lórí àwọn nẹtiwọki tí kò tíì bẹ̀rẹ̀ iṣẹ́ lẹ́kùnrẹ́rẹ́ kò ní iye owó kankan lórí.", "settingsEmojis": "<PERSON><PERSON><PERSON><PERSON> à<PERSON><PERSON><PERSON><PERSON> ì<PERSON> ìmọ̀lára hàn", "settingsNoAddresses": "<PERSON><PERSON> sí àwọn adirẹsi", "settingsAddressBookEmptyHeading": "Ìwé Àdírẹ́sì rẹ ṣófo", "settingsAddressBookEmptyText": "Tẹ “+” tàbí àwọn bọ́tìnì “Fi Ìwé Àdírẹ́sì rẹ kun” láti fi àwọn àdírẹ́sì tí o yàn láàyò kun", "settingsEditWallet": "Ṣe àtúnṣe Ibi-ìpamọ́", "settingsNoTrustedApps": "Kò sí àwọn appu tí a fi ọkàn tan", "settingsNoConnections": "Kò sí àsopọ̀ kankan síbẹ̀.", "settingsRemoveWallet": "<PERSON><PERSON>-ìpamọ́ kúrò", "settingsResetApp": "<PERSON><PERSON>-<PERSON><PERSON><PERSON>", "settingsBlocked": "<PERSON>i dín<PERSON>", "settingsBlockedAccounts": "Àwọn ibi-ìpamọ́ Tí a ti dínàmọ́", "settingsNoBlockedAccounts": "<PERSON>ò sí àwọn ibi-ìpamọ́ kankan tí a ti dínàmọ́.", "settingsRemoveSecretPhrase": "Yọ Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí kúrò", "settingsResetAppWithSecretPhrase": "Tún Ohun-<PERSON><PERSON> pẹ̀lú Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí", "settingsResetSecretRecoveryPhrase": "Ṣe àtúnṣe Gbólóhùn Ìràpadà Ìkọ̀kọ̀", "settingsShowSecretRecoveryPhrase": "Ṣàfihàn Gbólóhùn Ìràpadà Ìkọ̀kọ̀", "settingsShowSecretRecoveryPhraseSecondary": "Fi Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí hàn", "settingsShowSecretRecoveryPhraseTertiary": "Fi Gbólóhùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí hàn", "settingsTrustedAppsAutoConfirmActiveUntil": "<PERSON><PERSON><PERSON><PERSON> di {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Jẹ́rìsí Láìsí <PERSON>lọ́wọ́si", "settingsTrustedAppsDisclaimer": "Gba ìjẹ́rìsíi láìsí ìlọ́wọ́si láàyè lóri àwọn òpó lórí ayélujára tí ó fọkàntán nìkan", "settingsTrustedAppsLastUsed": "Tí lo {{formattedTimestamp}} sẹ́yìn", "settingsTrustedAppsPrimary": "Awọn ohun èlò Tí ó ti ní àsopọ̀", "settingsTrustedApps": "Àwọn Ohun èlò tí a Fọkàntán", "settingsTrustedAppsRevoke": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsRevokeToast": "Tí já àsopọ̀ pẹ̀lú {{trustedApp}}", "settingsTrustedAppsSecondary": "Ṣé àtúnto àwọn appu afọkàn tán rẹ", "settingsTrustedAppsToday": "Òní", "settingsTrustedAppsYesterday": "Àná", "settingsTrustedAppsLastWeek": "Ọ̀sẹ̀ Tí ó kọjá", "settingsTrustedAppsBeforeYesterday": "Ṣíwájú", "settingsTrustedAppsDisconnectAll": "Já àsopọ̀ pẹ̀lú gbogbo rẹ̀", "settingsTrustedAppsDisconnectAllToast": "Ti já àsopọ̀ gbogbo àwọn ohun èlò", "settingsTrustedAppsEndAutoConfirmForAll": "Fòpin sí ìjẹ́rìsíi láìsí ìlọ́wọ́si fún gbogbo rẹ̀", "settingsTrustedAppsEndAutoConfirmForAllToast": "Gbogbo sáà ìjẹ́rìsíi láìsí ìlọ́wọ́si ti parí", "settingsSecurityPrimary": "Ààbò & Ìkọ̀kọ̀", "settingsSecuritySecondary": "Ṣe ìmúdójúwọ̀n àwọn ètò ìdábòbò", "settingsActiveNetworks": "Àwọn nẹtiwọki Tí ó ńṣíṣẹ́", "settingsActiveNetworksAll": "Gbogbo rẹ̀", "settingsActiveNetworksSolana": "<PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana ni nẹtiwọki àtilẹ̀bá tí yóò sì wà ní títàn nígbàgbogbo.", "settingsDeveloperPrimary": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "settingsAdvanced": "Àwọn ètò Tí ó jinlẹ̀", "settingsTransactions": "Àwọn ètò Ìdúnàádúrà", "settingsAutoConfirm": "Àwọn ètò Ìjẹ́rìsí Láìsí ìlọ́wọ́si", "settingsSecurityAnalyticsPrimary": "Ṣe àjọpín Ìtúpalẹ̀ pẹ̀lú ọgbọ́n Láì lo orúkọ", "settingsSecurityAnalyticsSecondary": "Tí a mú ṣiṣẹ́ láti ràn wá lọ́wọ́ láti gbèrú", "settingsSecurityAnalyticsHelper": "Phantom kìí lo àlàyé ti ara ẹni rẹ fún àwọn èrèdí ìtúpalẹ̀ pẹ̀lú ọgbọ́n", "settingsSuspiciousCollectiblesPrimary": "Fi àwọn Ìgbàsilẹ̀ tí ó ní Ìfura pamọ́", "settingsSuspiciousCollectiblesSecondary": "Ṣé àyípadà láti tọ́jú àwọn ìgbàsilẹ̀ tí a ti ṣe àfihàn", "settingsPreferredBitcoinAddress": "Àdírẹ́sì Bitcoin Tí o fẹ́ràn jù", "settingsEnabledAddressesUpdated": "Ti mú àwọn àdírẹ́sì tí a lè f'ojúrí dójú ìwọ̀n!", "settingsEnabledAddresses": "Àwọn Àdírẹ́sì Tí a mú ṣiṣẹ́", "settingsBitcoinPaymentAddressForApps": "Àdírẹ́sì àwọn Ìsanwó fún àwọn Ohun èlò", "settingsBitcoinOrdinalsAddressForApps": "Àdírẹ́sì àwọn Ìlànà fún àwọn Ohun èlò", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Nígbàtí a bá mú irú àwọn àdírẹ́sì méjìjì tí ó wà lókè ṣiṣẹ́, fún àwọn ohun èlò bíi Magic Eden, àdírẹ́sì Native Segwit rẹ ni á ò lò láti san owó ọjà rírà.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Àdírẹ́sì àtilẹ̀bá Bitcoin nínú Phantom ni láti ri dájú wípé ó bamu.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Àtilẹ̀bá)", "settingsPreferredBitcoinAddressTaprootExplainer": "Irú àdírẹ́sì tí ó jẹ́ ìgbàlódé jùlọ, ní ọ̀pọ̀ ìgbà èyí tí àwọn owó ìdúnàádúrà kò wọ́n.", "settingsPreferredExplorers": "Olùwákiri Tí o fẹ́ràn jù", "settingsPreferredExplorersSecondary": "Yipadà sí olùwákiri àkójọ àlàyé oní-nọ́ńbà rẹ tí o fẹràn jù", "settingsCustomGasControls": "Ṣe àk<PERSON>o <PERSON> Owó <PERSON>ú<PERSON>à", "settingsSupportDesk": "Tábìlì Àtìlẹyìn", "settingsSubmitATicket": "<PERSON>áw<PERSON> kan lélẹ̀", "settingsAttachApplicationLogs": "So àwọn Àkọsílẹ̀ Ohun èlò mọ́ọ", "settingsDownloadApplicationLogs": "Sọ àwọn Àkọsílẹ̀ Ohun èlò kalẹ̀", "settingsDownloadApplicationLogsShort": "Sọ àwọn Àkọsílẹ̀ kalẹ̀", "settingsDownloadApplicationLogsHelper": "<PERSON><PERSON><PERSON> ní détà agbègbè, àwọn ìjábọ̀ kíkùnà àti àwọn àdírẹ́sì àpamọ́wọ́ gbogbogbò nínú láti ṣe ìrànlọ́wọ́ láti yanjú àwọn ọ̀ràn Àtìlẹyìn Phantom", "settingsDownloadApplicationLogsWarning": "Kò ní détà kankan tí ó ṣe kókó bíi gbólóhùn ọ̀rọ̀ ìgbaniwọlé tàbí àwọn kọ́kọ́rọ́ aládàáni nínú.", "settingsWallet": "<PERSON><PERSON><PERSON><PERSON>w<PERSON>", "settingsPreferences": "<PERSON>w<PERSON>n tí o fẹ́ràn jù", "settingsSecurity": "Ìdábòbòbò", "settingsDeveloper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSupport": "Atilẹyin", "settingsWalletShortcutsPrimary": "Fi àwọn Ọ̀nà àbújá <PERSON>mọ́wọ́ hàn", "settingsAppIcon": "<PERSON><PERSON><PERSON><PERSON> ìdá Ohun èlò mọ̀", "settingsAppIconDefault": "Àtilẹ̀bá", "settingsAppIconLight": "Mọ́lẹ̀", "settingsAppIconDark": "Dúd<PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "Ibi-ìpa<PERSON>ọ́", "settingsSearchResultSelected": "<PERSON>i yàn-án", "settingsSearchResultExport": "<PERSON><PERSON><PERSON>", "settingsSearchResultSeed": "Ọ̀rọ̀ ìgbaniwọlé", "settingsSearchResultTrusted": "Tí a fọkàntán", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "<PERSON><PERSON><PERSON>", "settingsSearchResultLogs": "Àwọn àkọsílẹ̀", "settingsSearchResultBiometric": "Ìdánimọ̀ nípa tara", "settingsSearchResultTouch": "Fọwọ́kàn", "settingsSearchResultFace": "<PERSON><PERSON><PERSON>", "settingsSearchResultShortcuts": "Àwọn ọ̀nà àbújá", "settingsAllSitesPermissionsTitle": "<PERSON><PERSON> sí Phantom lórí gbogbo àwọn òpó lórí a<PERSON>", "settingsAllSitesPermissionsSubtitle": "Ń gbà ọ́ láàyè láti lo àwọn ohun èlò pẹ̀lú Phantom láìní láti tẹ ì<PERSON><PERSON> àfikún", "settingsAllSitesPermissionsDisabled": "Aṣàwárí rẹ kò ṣe àtìlẹyìn fún yíyí ètò yìí padà", "settingsSolanaCopyTransaction": "Mú Dída Ìdúnàádúrà kọ ṣiṣẹ́", "settingsSolanaCopyTransactionDetails": "<PERSON> détà ìdúnàádúrà tí a tólẹ́sẹsẹ kọ sí klipbọọdu", "settingsAutoConfirmHeader": "Jẹ́rìsí Láìsí <PERSON>lọ́wọ́si", "refreshWebpageToApplyChanges": "Tún ojú-<PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON> sọjí láti mú àwọn àyípadà lò", "settingsExperimentalTitle": "Àwọn ẹ̀ya fún Ìdánwò", "settingsExprimentalSolanaActionsSubtitle": "Fẹ àwọn bọ́tìnì Ìgbésẹ̀ Solana nígbàtí a bá dá àwọn àsopọ̀ tí ó yẹ mọ̀ láìsí ìlọ́wọ́si lórí X.com", "stakeAccountCardActiveStake": "Ìfi owó sí ti o n lọ lọ́wọ́ lọ́wọ́", "stakeAccountCardBalance": "Ìyè tí ó ku", "stakeAccountCardRentReserve": "Ìfipamọ́ fún <PERSON>ò", "stakeAccountCardRewards": "<PERSON><PERSON><PERSON> tí ó Kẹ́yìn", "stakeAccountCardRewardsTooltip": "Èyí ni èrè tí o gbà láìpẹ́ jùlọ fún fífi owó si. Ìwọ ńgba èrè ni gbogbo ọjọ́ 3.", "stakeAccountCardStakeAccount": "Àdírẹ́sì", "stakeAccountCardLockup": "<PERSON><PERSON><PERSON>", "stakeRewardsHistoryTitle": "Ìtàn àkọọ́lẹ̀ àwọn <PERSON>rè", "stakeRewardsActivityItemTitle": "<PERSON><PERSON><PERSON><PERSON>", "stakeRewardsHistoryEmptyList": "<PERSON><PERSON> sí àwọn èrè", "stakeRewardsTime_zero": "Òní", "stakeRewardsTime_one": "Àná", "stakeRewardsTime_other": "{{count}} àw<PERSON><PERSON> ọjọ́ sẹ́yìn", "stakeRewardsItemsPagingFailed": "Ti kùnà láti kó àwọn èrè àtijọ́ jọpọ̀.", "stakeAccountCreateAndDelegateErrorStaking": "Ìṣòro kan wà nípa fífi owó sì inú olùfọwọ́sí yi. Jọ̀wọ́ gbìyànjú sì.", "stakeAccountCreateAndDelegateSolStaked": "O ti fi owó sí inú SOL!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "SOL rẹ má bẹ̀rẹ̀ síní jẹ àwọn èrè <1></1> ni ọjọ́ díẹ̀ sì ìsinsìnyí yí lọwọ kan tí akanti tí ó fowó sí bati ń ṣiṣẹ́.", "stakeAccountCreateAndDelegateStakingFailed": "Ìfi owó sí ti ní Ìjákulẹ̀", "stakeAccountCreateAndDelegateStakingSol": "Fifi owó sì SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "A n da akanti ìfi owó sí sílẹ̀, lẹhin ìgbà na fífa SOL rẹ le", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "A ńṣe idasile akanti làti má fowo sì, lẹhin na fífi SOL rẹ sínú {{Orúkọ Olùfọwọ́sí}}", "stakeAccountCreateAndDelegateViewTransaction": "Wo Ìdunọdura", "stakeAccountDeactivateStakeSolUnstaked": "Ko si owó ninu SOL!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "A má ni anfaani lati yọ owó rẹ kúrò <1></1> ni àwọn ọjọ́ díẹ̀ sì ìsinsìnyí lọwọ kan tí akanti tí ó fowó sí kò bá ṣiṣẹ́ mọ́.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Ìwọ kò ní lè yọ owó rẹ kúrò ní àwọn ọjọ́ díẹ̀ sì ìsinsìnyí lọ́gán tí ibi-ìpamọ́ tí ó fowó sí bá bẹ̀rẹ̀ sí ní ṣiṣẹ́.", "stakeAccountDeactivateStakeUnstakingFailed": "Yíyọ owó kúrò ti ní Ìjakulẹ", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Ìṣòro wá nípa yíyọ owó kúrò nínú olùfọwọ́sí yi. Jọ̀wọ́ gbìyànjú sì.", "stakeAccountDeactivateStakeUnstakingSol": "Yíyọ owó kúrò nínú SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "A ti ń bẹ̀rẹ̀ igbesẹ láti yọ owó kúrò nínú SOL rẹ.", "stakeAccountDeactivateStakeViewTransaction": "Wo Ìdunọdura", "stakeAccountDelegateStakeSolStaked": "O ti fi owó sí inú SOL!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "SOL rẹ má bẹ̀rẹ̀ síní jẹ àwọn èrè <1></1> ni àwọn ọjọ́ díẹ̀ sì ìsinsìnyí lọwọ kan tí akanti tí ó fowó sí bati ń ṣiṣẹ́.", "stakeAccountDelegateStakeStakingFailed": "Ìfi owó sí ti ní Ìjákulẹ̀", "stakeAccountDelegateStakeStakingFailedDescription": "Ìṣòro kan wà nípa fífi owó sì inú olùfọwọ́sí yi. Jọ̀wọ́ gbìyànjú sì.", "stakeAccountDelegateStakeStakingSol": "Fifi owó sì SOL...", "stakeAccountDelegateStakeStakingSolDescription": "A n fà SOL rẹ lé.", "stakeAccountDelegateStakeViewTransaction": "Wo Ìdunọdura", "stakeAccountListActivationActivating": "Jíjẹ kí ó bẹ̀rẹ̀ síní ṣíṣe", "stakeAccountListActivationActive": "O n ṣiṣẹ́", "stakeAccountListActivationInactive": "Ko ṣiṣẹ́", "stakeAccountListActivationDeactivating": "Jíjẹ kí ó má ṣiṣẹ́ mọ́", "stakeAccountListErrorFetching": "A kò lè wá àwọn ibi-ìpamọ́ tí o fowó sí rí. Jọ̀wọ́ tún gbìyànjú si nígbàm<PERSON>àn.", "stakeAccountListNoStakingAccounts": "<PERSON><PERSON> tí Ó fowó sí", "stakeAccountListReload": "Tún ń bẹ̀rẹ̀", "stakeAccountListViewPrimaryText": "Ìfi owó sí Rẹ", "stakeAccountListViewStakeSOL": "Fowó sì SOL", "stakeAccountListItemStakeFee": "{{fee}} owó", "stakeAccountViewActionButtonRestake": "Tún owó fi si", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON> owó kúrò", "stakeAccountViewError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewPrimaryText": "Ìfi owó sí Rẹ", "stakeAccountViewRestake": "Tún owó fi si", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Owó wá nínú SOL rẹ lọ́wọ́ lọ́wọ́ pẹlu olùfọwọ́sí. Ó má nílò láti yọ owó kúrò láti <1></1> ni anfaani sì àwọn owó yí. <3>Mọ̀ si</3>", "stakeAccountViewStakeInactive": {"part1": "<PERSON><PERSON><PERSON> tí owó wá ninu rẹ yí kò ṣiṣẹ̀. <PERSON><PERSON><PERSON><PERSON><PERSON> láti yọ owó inú rẹ̀ tàbí kí ó wà olùfọwọ́sí láti fà lè lọ́wọ́.", "part2": "Mọ̀ si"}, "stakeAccountViewStakeNotFound": "A ò rí akanti tí owó wá ninu rẹ yí.", "stakeAccountViewViewOnExplorer": "<PERSON><PERSON><PERSON> l<PERSON> {{explorer}}", "stakeAccountViewWithdrawStake": "<PERSON><PERSON>", "stakeAccountViewWithdrawUnstakedSOL": "Yọ SOL tí Kò sí owó ninu ẹ kúrò", "stakeAccountInsufficientFunds": "Kò sí SOL tí ó tó láti yọ owó kúrò tàbí yọ ọ́ kúrò.", "stakeAccountWithdrawStakeSolWithdrawn": "A ti yọ SOL kúrò!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "A ti yọ SOL rẹ kúrò.", "part2": "<PERSON><PERSON><PERSON> tí owó wá ninu rẹ yí má yọ ara rẹ̀ kúrò ní ìṣẹ́jú díẹ̀ sì ìsinsìnyí."}, "stakeAccountWithdrawStakeViewTransaction": "Wo Ìdunọdura", "stakeAccountWithdrawStakeWithdrawalFailed": "Yíyọ kúrò ní Ijakulẹ", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Ìṣòro wá nípa yíyọ owó kúrò nínú akanti tí owó wá yi. Jọ̀wọ́ gbìyànjú sì.", "stakeAccountWithdrawStakeWithdrawingSol": "Yíyọ SOL kúrò...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "A n yọ SOL rẹ kúrò nínú akanti tí owó wá ninu rẹ yí.", "startEarningSolAccount": "akanti", "startEarningSolAccounts": "<PERSON><PERSON><PERSON><PERSON> akanti", "startEarningSolErrorClosePhantom": "Tẹ ibí yìí kì ó sì tún gbìyànjú si", "startEarningSolErrorTroubleLoading": "Ìṣòro nípa fífi owó si", "startEarningSolLoading": "<PERSON><PERSON>ajọpọ...", "startEarningSolPrimaryText": "Bẹ̀rẹ̀ síní gba SOL", "startEarningSolSearching": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> àwọn akanti làti fowó sí", "startEarningSolStakeTokens": "Fowó sí àwọn tokini kí ó sì jẹ àwọn èrè", "startEarningSolYourStake": "Ìfi owó sí rẹ", "unwrapFungibleTitle": "Pàrọ̀ sí {{tokenSymbol}}", "unwrapFungibleDescription": "<PERSON><PERSON> <PERSON>́ kúrò láti {{fromToken}} fún {{toToken}}", "unwrapFungibleConfirmSwap": "Jẹ́rìsí pípààrọ̀", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "<PERSON><PERSON><PERSON>n Owó tí a ti Gbèrò", "swapFeesFees": "<PERSON><PERSON><PERSON>", "swapFeesPhantomFee": "Owó Phantom", "swapFeesPhantomFeeDisclaimer": "Àwa máa ńwá iye owó tí ó dára jùlọ láti ọ̀dọ̀ àwọn olùpèsè owó tí ó gajù. A ti ṣírò owó {{feePercentage}} mọ iye owó yìí láìsí ìlọ́wọ́sí.", "swapFeesRate": "<PERSON><PERSON> ow<PERSON>", "swapFeesRateDisclaimer": "Iye òṣùwọ̀n tí Aṣàpapọ Jupita rí káàkiri onírúurú pàṣípàrọ̀ oní ọ̀tọ̀ọ̀tọ̀.", "swapFeesRateDisclaimerMultichain": "Iye owó tí ó dára jùlọ tí a rí lórí àwọn onírúurú pàṣípàrọ̀ tí ó dá dúró.", "swapFeesPriceImpact": "Ipa ti Iye owó ni", "swapFeesHighPriceImpact": "Ipa Gíga ti Iye owó ni", "swapFeesPriceImpactDisclaimer": "Ìyàtọ̀ tí ó wà láàrin iye tí wọn ń tà lọ́jà àti iye owó tí a gbèrò lórí òṣùwọ̀n ìṣòwò rẹ.", "swapFeesSlippage": "Ìyàtọ̀ l<PERSON><PERSON><PERSON><PERSON><PERSON> ow<PERSON>", "swapFeesHighSlippage": "Ìfàyè gbà Dídà<PERSON>ù Gíga", "swapFeesHighSlippageDisclaimer": "Ìdúnàádúrà rẹ yóò kùnà bí iye owó ba yípadà ní èyítí kò dára ju {{slippage}}% lọ.", "swapTransferFee": "Owó Ìfiráńṣẹ́", "swapTransferFeeDisclaimer": "Ṣíṣòwò ${{symbol}} ńfa owó ìfiráńṣẹ́ {{feePercent}}% tí olùṣẹ̀dá tókìnì ṣètò, kìí ṣe Phantom.", "swapTransferFeeDisclaimerMany": "Ṣíṣòwò àwọn tókìnì tí a yàn náà ńfa owó ìfiráńṣẹ́ {{feePercent}}% tí àwọn olùṣẹ̀dá tókìnì náà ṣètò, kìí ṣe Phantom.", "swapFeesSlippageDisclaimer": "Iye bí owó ọjà tí ò ńṣòwò lè fi yàbàrà sí ìdíyelé tí a ti pèsè.", "swapFeesProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapFeesProviderDisclaimer": "Pàṣípàrọ̀ oní ọ̀tọ̀ọ̀tọ̀ tí a fi parí okòwò rẹ.", "swapEstimatedTime": "Iye àkókò Tí a gbèrò", "swapEstimatedTimeShort": "Iye àkókò tí a gbèrò", "swapEstimatedTimeDisclaimer": "Iye àkókò tí a gbèrò fún píparí àsopọ̀ bulọkiṣeeni yóò yàtọ̀ lórí ọ̀pọ̀lọpọ̀ àwọn okùnfà tí ó lè ní ipa lórí yíyára ìdúnàádúrà.", "swapSettingsButtonCommand": "Ṣí àwọn Ètò Ipààrọ̀", "swapQuestionRetry": "Tún gbìyànjú?", "swapUnverifiedTokens": "Àwọn tókìnì Tí a kò jẹ́rìsíí", "swapSectionTitleTokens": "{{section}} <PERSON><PERSON><PERSON><PERSON>", "swapFlowYouPay": "O San", "swapFlowYouReceive": "O gba", "swapFlowActionButtonText": "Ṣe agbeyẹwo Ibere", "swapAssetCardTokenNetwork": "{{symbol}} l<PERSON><PERSON><PERSON> {{network}}", "swapAssetCardMaxButton": "<PERSON> pọ̀jù", "swapAssetCardSelectTokenAndNetwork": "<PERSON> à<PERSON> Nẹtiwọki", "swapAssetCardBuyTitle": "O Gba", "swapAssetCardSellTitle": "O San", "swapAssetWarningUnverified": "A kò jẹ́rìsíí tókìnì yìí. Ní ìbáṣepọ̀ pẹ̀lú àwọn tókìnì tí o fọkàntán nìkan.", "swapAssetWarningPermanentDelegate": "Àsojú kan lè jó tàbí gbé àwọn tókìnì wọ̀nyìí sí ibòmíràn títí láí.", "swapSlippageSettingsTitle": "<PERSON><PERSON><PERSON> Ìyàtọ̀ owó <PERSON>", "swapSlippageSettingsSubtitle": "Ìdúnàádúrà rẹ yóò kùnà bí iye owó ba yípadà ju owó ìdíyelé lọ. Iye tí ó ga jù yóò yọrí sí okòwò tí kò dára.", "swapSlippageSettingsCustom": "Àkànṣe", "swapSlippageSettingsHighSlippageWarning": "Ìdúnàádúrà rẹ lè ṣe màgòmágò kí ó sì fa okòwò tí kò dára.", "swapSlippageSettingsCustomMinError": "Jọ̀wọ́ tẹ iye kan tí ó gaju {{minSlippage}}% si.", "swapSlippageSettingsCustomMaxError": "Jọ̀wọ́ tẹ iye kan tí ó kéré sí {{maxSlippage}}% si.", "swapSlippageSettingsCustomInvalidValue": "Jọ̀wọ́ tẹ iye tí ó jẹ́ ojúlówó si.", "swapSlippageSettingsAutoSubtitle": "Phantom y<PERSON>ò wá owó ìdíyelé tí o lọ sílẹ̀ jùlọ fún pàṣípàrọ̀ kan tí ó yọrí sí rere.", "swapSlippageSettingsAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swapSlippageSettingsFixed": "Tí kò yípadà", "swapSlippageOptInTitle": "Ìdíyelé Láìsí <PERSON>lọ́wọ́si", "swapSlippageOptInSubtitle": "Phantom yóò wá owó ìdíyelé tí o lọ sílẹ̀ jùlọ fún pàṣípàrọ̀ kan tí ó yọrí sí rere. O lè yí èyí padà nígbàkigbà nínú Apàrọ̀ nǹkan → Àwọn ètò Ìdíyelé.", "swapSlippageOptInEnableOption": "Mú Ìdíyelé Láìsí ìlọ́wọ́si ṣiṣẹ́", "swapSlippageOptInRejectOption": "Tẹ̀síwájú pẹ̀lú Ìdíyelé Tí ó wà títí", "swapQuoteFeeDisclaimer": "Iye owó tí a ṣ<PERSON>rò ní {{feePercentage}} owó Phantom nínú", "swapQuoteMissingContext": "Ọ̀nà ètò ipàrọ̀ ìdíyelé tí ó sọnù", "swapQuoteErrorNoQuotes": "Igb<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> láti pàrọ̀ láìsí àwọn ìd<PERSON>yelé", "swapQuoteSolanaNetwork": "Nẹtiwọki solana", "swapQuoteNetwork": "Nẹtiwọki", "swapQuoteOneTimeSerumAccount": "<PERSON><PERSON><PERSON> ó nígbà kan ṣoṣo", "swapQuoteOneTimeTokenAccount": "<PERSON><PERSON><PERSON> to<PERSON> òní-ìgb<PERSON> kan ṣoṣo", "swapQuoteBridgeFee": "<PERSON><PERSON> owó Pípàrọ̀ láti Ṣéènì kan sí òmíràn", "swapQuoteDestinationNetwork": "Òpin àjò Nẹtiwọki", "swapQuoteLiquidityProvider": "Olùpèsè <PERSON> owó si", "swapReviewFlowActionButtonPrimary": "Pàrọ̀", "swapReviewFlowPrimaryText": "Ṣe agbeyẹwo Ibere", "swapReviewFlowYouPay": "O San", "swapReviewFlowYouReceive": "O gba", "swapReviewInsufficientBalance": "Àpapọ̀ owó Tí kò to", "ugcSwapWarningTitle": "Ìkìlọ̀", "ugcSwapWarningBody1": "Ìdókòwò tókìnì yìí <PERSON> lórí pẹpẹ-ìtajà {{programName}}.", "ugcSwapWarningBody2": "Iye àwọn tókìnì wọ̀nyìí lè má dúrólójúkan gan-an, tí ó sì lè fa èrè tàbí ìpàdúnù owó tí ó pọ̀. Jọ̀wọ́ dókòwò sí ewu ara rẹ.", "ugcSwapWarningConfirm": "Ó yé mi", "bondingCurveProgressLabel": "Ìlọsíwájú Bonding Curve", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Ní àwòṣe bonding curve, àwọn iye owó tókìnì ni à ń pinnu nípa gbogbo ìrísí tí ó wà, tí ó ń pọ̀si bí a ṣe ń ra àwọn tókìnì si tí ó sì ń dínkù bí a ṣe ń ta àwọn tókìnì. Nígbàtí a bá ta àwọn tókìnì náà tán, gbogbo fífi owó si náà ni a ó fi sínú Raydium tí a ó sì jó níná.", "ugcFungibleWarningBanner": "Ìdókòwò tókìn<PERSON> yìí ń lọ lórí {{programName}}", "ugcCreatedRowLabel": "Tí a ṣẹ̀dá Ní", "ugcStatusRowLabel": "<PERSON><PERSON><PERSON>", "ugcStatusRowValue": "Ti ní ìgbésókè", "swapTxConfirmationReceived": "Gbígbà!", "swapTxConfirmationSwapFailed": "Ìpàrọ̀ ni ìjákulẹ̀", "swapTxConfirmationSwapFailedStaleQuota": "Ìdíyelé náà kò wúlò mọ́. Jọ̀wọ́ gbìyànjú si.", "swapTxConfirmationSwapFailedSlippageLimit": "Ìyàtọ̀ owó ìdíyelé rẹ ti lọ sílẹ̀ jù fún pàṣípààrọ̀ yìí. Jọ̀wọ́ mú kí ìyàtọ̀ owó ìdíyelé rẹ ní òkè fèrèsé Pààrọ̀ lọ sókè kí o sì tún gbìyànjú si.", "swapTxConfirmationSwapFailedInsufficientBalance": "A kò lè parí ìbéèrè náà. O kò ní iye owó tí ó tó láti parí ìdúnàádúrà náà.", "swapTxConfirmationSwapFailedEmptyRoute": "Owó tí ó ti tókìnì yìí lẹ́yìn ti yípadà. A kò lè ṣe àwárí ìdíyelé tí ó tọ́ kan. Jọ̀wọ́ gbìyànjú si tàbí kí o tún iye tókìnì ṣe.", "swapTxConfirmationSwapFailedAcountFrozen": "Olùṣẹ̀dá tókìnì yìí ti gbégile. O kò lè fi tókìnì yìí ráńṣẹ́ tàbí pààrò rẹ̀.", "swapTxConfirmationSwapFailedTryAgain": "Ìpàrọ̀ na ti ní ìjákulẹ̀, jọ̀wọ́ gb<PERSON>yànjú si", "swapTxConfirmationSwapFailedUnknownError": "A kò lè parí pípàrọ̀ náà. Kò sí nǹkankan tí ó ṣe àwọn owó rẹ. Jọ̀wọ́ gbìyànjú si. ", "swapTxConfirmationSwapFailedSimulationTimeout": "A kò lè farawé pípàrọ̀ náà. Kò sí nǹkankan tí ó ṣe àwọn owó rẹ. Jọ̀wọ́ gbìyànjú si.", "swapTxConfirmationSwapFailedSimulationUnknownError": "A kò lè parí pípàrọ̀ náà. Kò sí nǹkankan tí ó ṣe àwọn owó rẹ. Jọ̀wọ́ gbìyànjú si. ", "swapTxConfirmationSwapFailedInsufficientGas": "Ibi-ìpamọ́ rẹ kò ní àwọn owó tí ó tó láti parí ìdúnàádúrà náà. Jọ̀wọ́ fi owó kun ibi-ìpamọ́ rẹ si kí o sì gbìyànjú si.", "swapTxConfirmationSwapFailedLedgerReject": "A ti kọ pàṣípàrọ̀ náà láti ọwọ́ olùmúlò lórí ẹ̀rọ ohun èlò náà.", "swapTxConfirmationSwapFailedLedgerConnectionError": "A kọ pàṣípàrọ̀ náà nítorí àṣìṣe àsopọ̀ ẹ̀rọ kan. Jọ̀wọ́ gbìyànjú si.", "swapTxConfirmationSwapFailedLedgerSignError": "A kọ pàṣípàrọ̀ náà nítorí àṣìṣe ààmi ẹ̀rọ kan. Jọ̀wọ́ gbìyànjú si.", "swapTxConfirmationSwapFailedLedgerError": "A kọ pàṣípàrọ̀ náà nítorí àṣìṣe ẹ̀rọ kan. Jọ̀wọ́ gbìyànjú si.", "swapTxConfirmationSwappingTokens": "Ìpàrọ̀ àwọn tokini...", "swapTxConfirmationTokens": "<PERSON><PERSON><PERSON><PERSON> to<PERSON>", "swapTxConfirmationTokensDeposited": "O ti parí! A ti fi àwọn tokini pamọ́ sínú àpamọ́wọ́ rẹ", "swapTxConfirmationTokensDepositedTitle": "Ó ti parí!", "swapTxConfirmationTokensDepositedBody": "A ti fi àwọn tókìnì pamọ́ sínú àpamọ́wọ́ rẹ", "swapTxConfirmationTokensWillBeDeposited": "a má fi pamọ́ sínú àpamọ́wọ́ rẹ lọ́wọ́ kan tí idunọdura bati parí", "swapTxConfirmationViewTransaction": "Wo Ìdunọdura", "swapTxBridgeSubmitting": "Ńfi Ìdúnàádúrà lélẹ̀", "swapTxBridgeSubmittingDescription": "Pípàrọ̀ {{sellAmount}} on {{sellNetwork}} for {{buyAmount}} on {{buyNetwork}}", "swapTxBridgeFailed": "Ti <PERSON> láti fi Ìdúnàádúrà lélẹ̀", "swapTxBridgeFailedDescription": "A kò lè parí ìbéèrè n<PERSON>à.", "swapTxBridgeSubmitted": "Ti fi Ìdúnàádúrà lélẹ̀", "swapTxBridgeSubmittedDescription": "<PERSON>ye à<PERSON>ó<PERSON>ò Ìdúnàádúrà Tí a gbèrò: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "O lè yọ fèrèsé y<PERSON>í kúrò láìsí wàh<PERSON>là.", "swapperSwitchTokens": "<PERSON><PERSON> tó<PERSON> pad<PERSON>", "swapperMax": "<PERSON> pọ̀jù", "swapperTooltipNetwork": "Nẹtiwọki", "swapperTooltipPrice": "<PERSON><PERSON> ow<PERSON>", "swapperTooltipAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapperTrendingSortBy": "Tòó lẹ́sẹsẹ Nípa", "swapperTrendingTimeFrame": "Ìwọ̀n Àkókò", "swapperTrendingNetwork": "Nẹtiwọki", "swapperTrendingRank": "<PERSON><PERSON><PERSON>", "swapperTrendingTokens": "Àwọn tókìnì Tí ó gbajúmọ̀ lọ́wọ́", "swapperTrendingVolume": "<PERSON><PERSON>", "swapperTrendingPrice": "<PERSON><PERSON> ow<PERSON>", "swapperTrendingPriceChange": "<PERSON>ye owó Ti yípadà", "swapperTrendingMarketCap": "Gbogbo Iye Ìdókòwò Ọjà", "swapperTrendingTimeFrame1h": "1h", "swapperTrendingTimeFrame24h": "24h", "swapperTrendingTimeFrame7d": "7d", "swapperTrendingTimeFrame30d": "30d", "swapperTrendingNoTokensFound": "<PERSON><PERSON> rí <PERSON>w<PERSON>n tókìn<PERSON> kankan.", "switchToggle": "Figagbága", "termsOfServiceActionButtonAgree": "<PERSON>", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Nípa títẹ <1>\"<PERSON> Gbà\"</1> o gba àwọn <3>Òfin àti Àsọtẹ́lẹ̀</3> nípa ipàrọ̀ àwọn tokini pẹlu Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "A ti ṣe àtúnyẹ̀wò àwọn Òfin Ìṣe wa. Nípa títẹ <1>\"Mo Gbà\"</1>o gba àwọn <3>Òfin Ìṣe wá tuntun</3>.<5></5><6></6>Àwọn Òfin Ìṣe wá tuntun ni<8>ìgbékalẹ̀ owó</8>fún àwọn ọjà kan ni pàtó.", "termsOfServicePrimaryText": "Àwọn Òfin Ìṣe", "tokenRowUnknownToken": "Tokini Aimọ̀", "transactionsAppInteraction": "Ìbáṣepọ̀ ohun-èlò", "transactionsFailedAppInteraction": "Ìbáṣepọ̀ ohun-èlò ti kùnà", "transactionsBidOnInterpolated": "Fowó sílẹ̀ lori {{<PERSON><PERSON><PERSON><PERSON>}}", "transactionsBidFailed": "<PERSON><PERSON><PERSON> owó sílẹ̀ ti kùnà", "transactionsBoughtInterpolated": "<PERSON><PERSON> {{<PERSON><PERSON><PERSON><PERSON>}}", "transactionsBoughtCollectible": "Ti ra Àgbà sílẹ̀", "transactionBridgeInitiated": "Ti bẹ̀rẹ̀ Àsopọ̀ bulọkiṣeeni", "transactionBridgeInitiatedFailed": "Bíbẹ̀rẹ̀ Àsopọ̀ bulọkiṣeeni Ti kùnà", "transactionBridgeStatusLink": "Yẹ Ipò wò lórí LI.FI", "transactionsBuyFailed": "<PERSON><PERSON><PERSON><PERSON> ti kùnà", "transactionsBurnedSpam": "Jó ìfiráńṣẹ́ tí a kò bèèrè fún níná", "transactionsBurned": "Ti jóná", "transactionsUnwrapped": "Ti wà ní ṣíṣí", "transactionsUnwrappedFailed": "Ṣíṣí sílẹ̀ ti kùnà", "transactionsCancelBidOnInterpolated": "Ti fagilé fífi owó sílẹ̀ lori {{name}}", "transactionsCancelBidOnFailed": "Ti kùnà láti fagilé fífi owó sílẹ̀", "transactionsError": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsFailed": "Ìjákulẹ̀", "transactionsSwapped": "Ti pààrọ̀ rẹ̀", "transactionsFailedSwap": "Pípàrọ̀ ti kùnà", "transactionsFailedBurn": "Jíjóná ti kùnà", "transactionsFrom": "<PERSON><PERSON><PERSON>", "transactionsListedInterpolated": "O wa lori àtẹ {{or<PERSON>k<PERSON>}}", "transactionsListedFailed": "Ti kùnà láti <PERSON>ój<PERSON>", "transactionsNoActivity": "<PERSON> si <PERSON> k<PERSON>", "transactionsReceived": "Gbígbà", "transactionsReceivedInterpolated": "Gbígbà {{iye}} SOL", "transactionsSending": "<PERSON><PERSON> ránṣẹ́...", "transactionsPendingCreateListingInterpolated": "Ṣíṣẹ̀dá {{name}}", "transactionsPendingEditListingInterpolated": "Ṣíṣé àtúnṣe {{name}}", "transactionsPendingSolanaPayTransaction": "Ńjẹ́rìsíí S<PERSON>an owó Ìdúnàádúrà Solana", "transactionsPendingRemoveListingInterpolated": "Y<PERSON><PERSON><PERSON> {{name}} k<PERSON><PERSON><PERSON> lórí àtẹ", "transactionsPendingBurningInterpolated": "<PERSON><PERSON><PERSON><PERSON> {{name}} níná", "transactionsPendingSending": "<PERSON><PERSON><PERSON> ráńṣẹ́", "transactionsPendingSwapping": "Pípàrọ̀", "transactionsPendingBridging": "Síso bulọkiṣeeni pọ̀", "transactionsPendingApproving": "Ńfọwọ́ si", "transactionsPendingCreatingAndDelegatingStake": "Ṣíṣẹ̀dá àti fífa fífi owó si sílẹ̀", "transactionsPendingDeactivatingStake": "Jíjẹ́ kí fífi owó si má ṣiṣẹ́ mọ́", "transactionsPendingDelegatingStake": "Fífa fífi owó si sílẹ̀", "transactionsPendingWithdrawingStake": "Yíyọ fífi owó si kúrò", "transactionsPendingAppInteraction": "Ohun-èlò ìbáṣepọ̀ wà ní ìdádúró", "transactionsPendingBitcoinTransaction": "Ìdúnàádúrà BTC wà ní ìdádú<PERSON>ó", "transactionsSent": "A ti fi ránṣẹ́", "transactionsSendFailed": "Fífiráńṣẹ́ ti kùnà", "transactionsSwapOn": "Pàrọ̀ lórí {{dappName}}", "transactionsSentInterpolated": "A ti fi SOL {{<PERSON><PERSON><PERSON>}} ránṣẹ́", "transactionsSoldInterpolated": "Ta a {{orúk<PERSON>}}", "transactionsSoldCollectible": "Tí ta Àgbàsílẹ̀", "transactionsSoldFailed": "T<PERSON>tà ti kùnà", "transactionsStaked": "A ti fowó si", "transactionsStakedFailed": "<PERSON><PERSON><PERSON> owó si ti kùnà", "transactionsSuccess": "<PERSON><PERSON><PERSON><PERSON>", "transactionsTo": "Sí", "transactionsTokenSwap": "Ìpàrọ̀ Tokini", "transactionsUnknownAmount": "Tí a kò mọ̀", "transactionsUnlistedInterpolated": "Ko sí lori àtẹ {{orúk<PERSON>}}", "transactionsUnstaked": "<PERSON><PERSON> owó kúrò", "transactionsUnlistedFailed": "Ti kùnà láti má<PERSON>e <PERSON>ój<PERSON>", "transactionsDeactivateStake": "Ti mú fífi owó si má ṣiṣẹ́ mọ́", "transactionsDeactivateStakeFailed": "Ti kùnà láti mú fífi owó si má ṣiṣẹ́ mọ́", "transactionsWaitingForConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> de ijẹrisi", "transactionsWithdrawStake": "<PERSON><PERSON> kúr<PERSON>", "transactionsWithdrawStakeFailed": "Yíyọ owó kúrò ti kùnà", "transactionCancelled": "Ti fagile", "transactionCancelledFailed": "<PERSON>i kùn<PERSON> láti fagilé", "transactionApproveToken": "Ti fọwọ́ sí {{tokenSymbol}}", "transactionApproveTokenFailed": "Ti kùnà láti fọwọ́ sí {{tokenSymbol}}", "transactionApprovalFailed": "<PERSON><PERSON><PERSON>w<PERSON>́ sí ti kùnà", "transactionRevokeApproveToken": "Ti fagilé {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "Ti kùn<PERSON> láti fagilé {{tokenSymbol}}", "transactionRevokeFailed": "<PERSON>í fagilé ti kùnà", "transactionApproveDetailsTitle": "<PERSON>ọwọ<PERSON> sí àwọn <PERSON>", "transactionCancelOrder": "<PERSON><PERSON><PERSON>", "transactionCancelOrderFailed": "Fífagilé <PERSON> ti kùnà", "transactionApproveAppLabel": "<PERSON><PERSON>", "transactionApproveAmountLabel": "<PERSON><PERSON>", "transactionApproveTokenLabel": "Tókìnì", "transactionApproveCollectionLabel": "Àkójọpọ̀", "transactionApproveAllItems": "Fọwọ́ sí gbogbo nǹkan", "transactionSpendUpTo": "Náwó títí dé", "transactionCancel": "Fagilé Ìdúnàádúrà", "transactionPrioritizeCancel": "Fí fagilé Ṣe àkọ́kọ́", "transactionSpeedUp": "<PERSON> <PERSON>", "transactionCancelHelperText": "Ìdúnàádúrà tí ó jẹ́ ojúlówó lè parí ṣáájú kí a tó fagile.", "transactionSpeedUplHelperText": "<PERSON><PERSON><PERSON> yóò mú ìdúnàádúrà rẹ yára dé ìwọ̀n tí ó ga jù látàrí àwọn àmúyẹ nẹtiwọki.", "transactionCancelHelperMobile": "<PERSON><PERSON><PERSON> ná ọ ní <1>títí dé {{amount}}</1> láti gbìyànjú láti fagile ìdúnàádúrà yìí. Ìdúnàádúrà tí ó jẹ́ ojúlówó lè parí ṣáájú kí a tó fagile.", "transactionCancelHelperMobileWithEstimate": "<PERSON><PERSON><PERSON> gbà tó iye <1>títí dé {{amount}}</1> láti gbìyànjú láti fagile ìdúnàádúrà yìí. Ó yẹ kí ó parí ní bíi {{timeEstimate}}. Ìdúnàádúrà tí ó jẹ́ ojúlówó lè parí ṣáájú kí a tó fagile.", "transactionSpeedUpHelperMobile": "<PERSON><PERSON><PERSON> gbà tó iye <1>títí dé {{amount}}</1> láti mú ìdúnàádúrà yìí yára dé ìwọ̀n tí ó ga jù.", "transactionSpeedUpHelperMobileWithEstimate": "<PERSON><PERSON><PERSON> gbà tó iye <1>títí dé {{amount}}</1> láti gbìyànjú láti fagile ìdúnàádúrà yìí. Ó yẹ kí ó parí ní bíi {{timeEstimate}}.", "transactionEstimatedTime": "Iye àkókò tí a gbèrò", "transactionCancelingSend": "Fífagilé fífiráńṣẹ́", "transactionPrioritizingCancel": "Ńfí fífagile ṣe àkọ́kọ́", "transactionCanceling": "Fífagilé", "transactionReplaceError": "Àṣìṣe kan ti wáyé. A kò díye kankan lé ibi-ìpamọ́ rẹ. O lè tun gbìyànjú.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} tí kò tó", "transactionGasLimitError": "Ti kùnà láti <PERSON> gbèd<PERSON>ke iye owó fún mímú ìdúnàádúrà ṣẹ", "transactionGasEstimationError": "Ti kùnà láti <PERSON> iye owó fún mímú ìdúnàádúrà ṣẹ", "pendingTransactionCancel": "<PERSON><PERSON><PERSON>", "pendingTransactionSpeedUp": "<PERSON>", "pendingTransactionStatus": "<PERSON><PERSON><PERSON>", "pendingTransactionPending": "Wíwà ní ìdá<PERSON>ú<PERSON>ó", "pendingTransactionPendingInteraction": "Ìbáṣepọ̀ Wà ní ìdád<PERSON>ó", "pendingTransactionCancelling": "Fífagilé", "pendingTransactionDate": "Ọjọ́", "pendingTransactionNetworkFee": "Owó nẹtiwọki", "pendingTransactionEstimatedTime": "Iye àkókò tí a gbèrò", "pendingTransactionEstimatedTimeHM": "{{hours}}h {{minutes}}m", "pendingTransactionEstimatedTimeMS": "{{minutes}}m {{seconds}}s", "pendingTransactionEstimatedTimeS": "{{seconds}}s", "pendingTransactionsSendingTitle": "Ńfi {{assetSymbol}} ráńṣẹ́", "pendingTransactionsUnknownEstimatedTime": "Tí a kò mọ̀", "pendingTransactionUnknownApp": "Ohun-è<PERSON>ò Tí a kò mọ̀", "permanentDelegateTitle": "Ti pín oj<PERSON>e", "permanentDelegateValue": "Títí láí", "permanentDelegateTooltipTitle": "Pínpín ojúṣe Títí láí", "permanentDelegateTooltipValue": "Aṣojú títí láí ń gba ibi-ìpamọ́ mìíràn láàyè láti <PERSON>o àwọn tókìnì fún ọ, èyí ní jíjóníná tàbí gbígbé lọ síbòmíràn nínú.", "unlockActionButtonUnlock": "Ṣi i", "unlockEnterPassword": "Tẹ ọ̀rọ̀ ìgbaniwọlé rẹ", "unlockErrorIncorrectPassword": "Ọ̀rọ̀ ìgbaniwọlé tí kò tọ́", "unlockErrorSomethingWentWrong": "Nkan ti ko tọ́ kan ti ṣẹlẹ̀, jọ̀wọ́ tún gbìyànjú tó bá ṣe díẹ̀", "unlockForgotPassword": "Ti gbàgbé ọ̀rọ̀ ìgbaniwọlé", "unlockPassword": "Ọ̀rọ̀ ìgbaniwọlé", "forgotPasswordText": "O lè tún ọ̀rọ̀ ìgbaniwọlé rẹ ṣe nípa títẹ ọ̀rọ̀ 12-24 gbólóhùn kúkurú gbígbà àpamọ́wọ́ rẹ padà si. Phantom kò lè gba ọ̀rọ̀ ìgbaniwọlé rẹ padà fún ọ.", "appInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "lastUsed": "Tí a lò Kẹ́yìn", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "<PERSON>ò sí pẹ̀lú àwọn ibi-ìpamọ́ ohun èlò.", "trustedAppAutoConfirmDisclaimer1": "Nígbàtí ó ńṣiṣẹ́ lọ́wọ́, Phantom yóò jẹ́rìsí gbogbo àwọn ìbéè<PERSON>è láti orí ohun èlò yìí láì wífún ọ tàbí béèrè ìjẹ́rìsíí rẹ.", "trustedAppAutoConfirmDisclaimer2": "Mímu ṣiṣẹ́ lè fi àwọn owó rẹ sí ewu jìbìtì. Lo ẹ̀yà yìí pẹ̀lú àwọn ohun èlò tí o fi ọkàn tán nìkan.", "validationUtilsPasswordIsRequired": "O nílò ọ̀rọ̀ ìgbaniwọlé", "validationUtilsPasswordLength": "Ọ̀rọ̀ ìgbaniwọlé gboọ́dọ̀ gun to onka mẹjọ", "validationUtilsPasswordsDontMatch": "Àwọn ọ̀rọ̀ ìgbaniwọlé kò wọlé", "validationUtilsPasswordCantBeSame": "O kò lè lo ọ̀rọ̀ ìgbaniwọlé tí ó ń lo tẹ́lẹ̀", "validatorCardEstimatedApy": "Ti ṣírò APY", "validatorCardCommission": "<PERSON><PERSON><PERSON>", "validatorCardTotalStake": "Gbogbo Owó tí o wà ", "validatorCardNumberOfDelegators": "# <PERSON><PERSON><PERSON><PERSON>", "validatorListChooseAValidator": "<PERSON><PERSON><PERSON><PERSON> kan", "validatorListErrorFetching": "A kò lè wá àwọn olùfọwọ́sí rí. Jọ̀wọ́ tún gbìyànjú si nígbàm<PERSON>àn.", "validatorListNoResults": "<PERSON>n è<PERSON>", "validatorListReload": "Tún ń bẹ̀rẹ̀", "validatorInfoTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>w<PERSON>s<PERSON>", "validatorInfoTitle": "<PERSON><PERSON><PERSON><PERSON> o<PERSON>", "validatorInfoDescription": "Nípa fífi SOL rẹ sòwò lórí olùfọwọ́sí kan ìwọ ńkópa sí ṣíṣeṣẹ́ àti ààbò nẹtiwọki Solana, nígbàtí ò ńjèrè SOL dípò.", "validatorApyInfoTooltip": "Ṣírò APY", "validatorApyInfoTitle": "Ti ṣírò APY", "validatorApyInfoDescription": "Èyí ni iye èrè tí ò ńgbà fún fífi SOL sòwò lórí olùfọwọ́sí náà.", "validatorViewActionButtonStake": "<PERSON>ow<PERSON> si", "validatorViewErrorFetching": "<PERSON> le gbe àwọn olùfọwọ<PERSON><PERSON><PERSON> j<PERSON>de.", "validatorViewInsufficientBalance": "Iye tó kù tí kò tó", "validatorViewMax": "<PERSON> pọ̀ju", "validatorViewPrimaryText": "Bẹ̀rẹ̀ síní fi owó si", "validatorViewDescriptionInterpolated": "Yan iye SOL tí ó wù ọ́ <1></1> l<PERSON><PERSON> fi owó sí pẹ̀lú olùfọwọ́sí yìí. <3>Mọ̀ síwájú si</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{iye}} SOL tí ó nílò láti fi owó si", "validatorViewValidator": "<PERSON><PERSON><PERSON><PERSON><PERSON>w<PERSON>s<PERSON>", "walletMenuItemsAddConnectWallet": "Ṣàfikún / Sopọ̀ mọ́ Àpamọ́wọ́", "walletMenuItemsBridgeAssets": "<PERSON> <PERSON><PERSON><PERSON> <PERSON><PERSON> ohun ini", "walletMenuItemsHelpAndSupport": "Iranlọ́wọ<PERSON> àti Atilẹyin", "walletMenuItemsLockWallet": "<PERSON><PERSON> pa", "walletMenuItemsResetSecretPhrase": "Ṣé àtúnṣe Gbólóhùn Ikọ̀kọ̀", "walletMenuItemsShowMoreAccounts": "Fi hàn {{ó nka}} sì...", "walletMenuItemsHideAccounts": "<PERSON> <PERSON><PERSON><PERSON>n akanti pam<PERSON>", "toggleMultiChainHeader": "Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́", "disableMultiChainHeader": "<PERSON><PERSON><PERSON>", "disableMultiChainDetail1Header": "Fi gbogbo ara lọ fún Solana", "disableMultiChainDetail1SecondaryText": "Ṣe àkóso àwọn Ibi-ìpamọ<PERSON>, àwọn tókìn<PERSON>, àti àwọn àgbà sílẹ̀ rẹ láìrí àwọn ṣéènì mìíràn.", "disableMultiChainDetail2Header": "Padà sí Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́ nígbàkigbà", "disableMultiChainDetail2SecondaryText": "Iye owó Ethereum àti Polygon rẹ tí ó wà tẹ́lẹ̀ yóò wà ní ìpamọ́ nígbàtí o bá tún mú Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́.", "disableMultiChainButton": "Mú <PERSON>-nìkan ṣiṣẹ́", "disabledMultiChainHeader": "Ti mú Solana-nìkan ṣiṣẹ́", "disabledMultiChainText": "O lè mú èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́ nígbàkigbà.", "enableMultiChainHeader": "Mú Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́", "enabledMultiChainHeader": "Ti mú Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́", "enabledMultiChainText": "Ethereum àti Polgon ti ní àtìlẹyìn nínú àpamọ́wọ́ rẹ nísìnyí.", "incompatibleAccountHeader": "Ibi-ìpamọ́ Tí kò báramu", "incompatibleAccountInterpolated": "Jọ̀wọ́ yọ àwọn ibi-ìpamọ́ Ethereum-nìkan wọ̀nyìí kúrò ṣaájú kí o tó mú ipò Solana-nìkan ṣiṣẹ́: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Kínni ó jẹ́ Tuntun!", "welcomeToMultiChainPrimaryText": "À<PERSON><PERSON><PERSON>́wọ́ kan fún ohun gbogbo", "welcomeToMultiChainDetail1Header": "Àtìlẹyìn Ethereum àti Polygon", "welcomeToMultiChainDetail1SecondaryText": "Gbogbo àwọn tókìnì àti àwọn NFT láti <PERSON>, Ethereum, àti Polygon ní ojú k<PERSON>.", "welcomeToMultiChainDetail2Header": "Lo gbogbo àwọn ohun-èlò tí o nífẹ̀ẹ́", "welcomeToMultiChainDetail2SecondaryText": "Sopọ̀ mọ́ àwọn ohun-è<PERSON>ò lórí ọ̀pọ̀lọpọ̀ àwọn ṣéènì láì yí àwọn nẹtiwọki padà.", "welcomeToMultiChainDetail3Header": "Gbe àpamọ́wọ́ MetaMask rẹ wọlé", "welcomeToMultiChainDetail3SecondaryText": "Gbé gbogbo gbólóhùn ọ̀rọ̀ ìgbaniwọlé Ethereum àti Polygon rẹ wọlé ní ìrọ̀rùn.", "welcomeToMultiChainIntro": "Káàbọ̀ sí Phantom Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́", "welcomeToMultiChainIntroDesc": "Gbogbo àwọn tókìnì àti àwọn NFT rẹ láti <PERSON>, Ethereum, àti Polygon lójú kann<PERSON>. Àpamọ́wọ́ rẹ kan fún ohun gbogbo.", "welcomeToMultiChainAccounts": "Àwọn ibi-ìpamọ́ Èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́ tí a ti túnṣe", "welcomeToMultiChainAccountsDesc": "Tí a ti túnṣe fún èyítí ó lè bá ọ̀pọ̀lọpọ̀ nẹtiwọki ṣiṣẹ́, ibi-ìpamọ́ kọ̀ọ̀kan ti ní àwọn àdírẹ́sì ETH àti Poly tí ó baamu nísìnyí.", "welcomeToMultiChainApps": "Ń ṣiṣẹ́ Níbi gbogbo", "welcomeToMultiChainAppsDesc": "Phantom kò báramu pẹ̀lú gbogbo ohun-<PERSON><PERSON>ò lórí Ethereum, Polygon, àti <PERSON>. Tẹ “sopọ̀ mọ́ MetaMask” o sì ti setán láti lọ.", "welcomeToMultiChainImport": "<PERSON><PERSON><PERSON> e wọlé l<PERSON>ti <PERSON>, lẹ́sẹ̀kẹsẹ̀", "welcomeToMultiChainImportDesc": "Gbé àwọn gbólóhùn ọ̀rọ̀ ìgbaniwọlé tàbí kọ́kọ́rọ́ aláàdáni rẹ wọlé bíi Àpamọ́wọ́ MetaMask tàbí Coinbase. Gbogbo rẹ̀ lójúkan.", "welcomeToMultiChainImportInterpolated": "<0>G<PERSON><PERSON> àwọn gbólóhùn ọ̀rọ̀ ìgbaniwọlé</0> tàbí kọ́kọ́rọ́ aláàdáni rẹ wọlé bíi Àpamọ́wọ́ MetaMask tàbí Coinbase. Gbogbo rẹ̀ lójúkan.", "welcomeToMultiChainTakeTour": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "welcomeToMultiChainSwapperTitle": "Pààrọ̀ l<PERSON><PERSON><PERSON>, Polygon, àti <PERSON>", "welcomeToMultiChainSwapperDetail1Header": "Àtìlẹyìn Ethereum àti Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Nísìnyí o lè pààrọ̀ àwọn tókìnì ERC-20 rẹ nírọ̀rùn láti inú àpamọ́wọ́ rẹ.", "welcomeToMultiChainSwapperDetail2Header": "Iye owó Tí ó dára jùlọ àti owó Tí ó lọ Sílẹ̀ Jùlọ", "welcomeToMultiChainSwapperDetail2SecondaryText": "Àwọn irísun sísọ d'owó 100+ àti rírà tàbí títà tí ó jáfáfá fún àwọn èrè tí ó ga jùlọ.", "networkErrorTitle": "Àṣìṣe Nẹtiwọki", "networkError": "O ṣeni lanu pé a ó kó ní anfaani si nẹtiwọki na. Jọ̀wọ́ gbìyànjú sì ti o ba ṣe diẹ.", "authenticationUnlockPhantom": "Ṣí Phantom", "errorAndOfflineSomethingWentWrong": "Nkan kan ó lọ dédé", "errorAndOfflineSomethingWentWrongTryAgain": "Jọ̀wọ́ gbìyànjú si.", "errorAndOfflineUnableToFetchAssets": "A kò rí àwọn ohun ìní gbé jáde. Jọ̀wọ́ gbìyànjú sì ti o ba ṣe diẹ.", "errorAndOfflineUnableToFetchCollectibles": "A kò rí àwọn ìgbàsilẹ̀ gbé jáde. Jọ̀wọ́ gbìyànjú sì ti o ba ṣe diẹ.", "errorAndOfflineUnableToFetchSwap": "A kò rí alaye ipàrọ̀ gbé jáde. Jọ̀wọ́ gbìyànjú sì ti o ba ṣe diẹ.", "errorAndOfflineUnableToFetchTransactionHistory": "A kò lè rí ìtàn ìdúnàádúrà ní báyìí. Yẹ àsopọ̀ nẹtiwọki rẹ wò, tàbí gbìyànjú si nígbàmíì.", "errorAndOfflineUnableToFetchRewardsHistory": "A kò lè wá ìtàn àwọn èrè rí. Jọ̀wọ́ tún gbìyànjú si nígbàmíràn.", "errorAndOfflineUnableToFetchBlockedUsers": "A kò lè wá àwọn olùmúlò tí a ti dínàmọ́ rí. Jọ̀wọ́ tún gbìyànjú si nígbàm<PERSON>ràn.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Nkan kan ó lọ dédé nígbà tí a ń ṣe agbeyẹwo ibere rẹ, jọ̀wọ́ gbìyànjú si.", "sendSelectToken": "<PERSON>", "swapBalance": "Ìyè tí ó ku:", "swapTitle": "Pàrọ̀ àwọn <PERSON>", "swapSelectToken": "<PERSON>", "swapYouPay": "Ìwọ Sanwó", "swapYouReceive": "Ìwọ Gbàá", "aboutPrivacyPolicy": "Ìlànà-ìṣe Ìpamọ́", "aboutVersion": "<PERSON><PERSON><PERSON> {{irú <PERSON>}}", "aboutVisitWebsite": "Bẹ <PERSON>ti wo", "bottomSheetConnectTitle": "Sopọ̀", "A11YbottomSheetConnectTitle": "So <PERSON><PERSON> pọ̀mọ", "A11YbottomSheetCommandClose": "<PERSON>i k<PERSON> à<PERSON>n <PERSON>", "A11YbottomSheetCommandBack": "<PERSON><PERSON>", "bottomSheetSignTypedDataTitle": "Buwọ<PERSON>lu ìfiráńṣẹ́", "bottomSheetSignMessageTitle": "Buwọ<PERSON>lu ìfiráńṣẹ́", "bottomSheetSignInTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "bottomSheetSignInAndConnectTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> w<PERSON>", "bottomSheetConfirmTransactionTitle": "Jẹ́rìsí ìdúnàádú<PERSON>à", "bottomSheetConfirmTransactionsTitle": "Jẹ́rìsí àwọn ìd<PERSON>", "bottomSheetSolanaPayTitle": "Ìbéèrè Solana Pay", "bottomSheetAdvancedTitle": "Tí ó jinlẹ̀", "bottomSheetReadOnlyAccountTitle": "<PERSON>pò fún Wíwò <PERSON>", "bottomSheetTransactionSettingsTitle": "Owó Nẹtiwọki", "bottomSheetConnectDescription": "Sísopọ̀mọ yóò gba òpó lórí ayélujára láàyè yìí láti wo àwọn owó tí ó ṣẹ́kù àti iṣẹ́-ṣíṣe fún ibi-ìpamọ́ náà tí a ti yàn.", "bottomSheetSignInDescription": "Bíbuwọ́lu ìfiránṣẹ́ yìí yóò fi níni ibi-ìpamọ́ tí ó ti yàn hàn. Buwọ́lu àwọn ìfiránṣẹ́ láti àwọn ohun èlò tí ó fọkàn tán nikan.", "bottomSheetSignInAndConnectDescription": "<PERSON><PERSON><PERSON> ọwọ́ sí yóò gba òpó yìí lórí ayélujára láàyè láti wo àwọn owó tí ó ṣẹ́kù àti iṣẹ́-ṣíṣe fún ibi-ìpamọ́ náà tí a ti yàn.", "bottomSheetConfirmTransactionDescription": "A ti ṣírò àwọn àyípadà iye owó tí ó kù. Iye owó àti àwọn ohun ìní tí ó nííṣe ni a kò múdánilójú.", "bottomSheetConfirmTransactionsDescription": "A ti ṣírò àwọn àyípadà iye owó tí ó kù. Iye owó àti àwọn ohun ìní tí ó nííṣe ni a kò múdánilójú.", "bottomSheetSignTypedDataDescription": "Èyí jẹ́ ìb<PERSON><PERSON><PERSON><PERSON> ì<PERSON> nìkan. Ìdúnàádúrà náà lè má ṣẹlẹ̀ lẹ́sẹ̀kẹsẹ̀.", "bottomSheetSignTypedDataSecondDescription": "A ti ṣírò àwọn àyípadà iye owó tí ó kù. Iye owó àti àwọn ohun ìní tí ó nííṣe ni a kò múdánilójú.", "bottomSheetSignMessageDescription": "Bíbuwọ́lu ìfiránṣẹ́ yìí yóò fi níni ibi-ìpamọ́ tí ó ti yàn hàn. Buwọ́lu àwọn ìfiránṣẹ́ láti àwọn ohun èlò tí ó fọkàn tán nikan.", "bottomSheetReadOnlyAccountDescription": "<PERSON>ò lè ṣe iṣẹ́ yìí ní ipò fún wíwò nìkan.", "bottomSheetMessageRow": "Ìfiránṣẹ́", "bottomSheetStatementRow": "Àlàyé àkọsílẹ̀ ìdúnàádúrà", "bottomSheetAutoConfirmRow": "Jẹ́rìsí Láìsí <PERSON>lọ́wọ́si", "bottomSheetAutoConfirmOff": "Paá", "bottomSheetAutoConfirmOn": "Tàn-<PERSON>n", "bottomSheetAccountRow": "Ibi-ìpa<PERSON>ọ́", "bottomSheetAdvancedRow": "Tí ó jinlẹ̀", "bottomSheetContractRow": "Àdírẹ́sì Àd<PERSON><PERSON>ùn", "bottomSheetSpenderRow": "Àdírẹ́sì O<PERSON><PERSON><PERSON>wó", "bottomSheetNetworkRow": "Nẹtiwọki", "bottomSheetNetworkFeeRow": "Owó Nẹtiwọki", "bottomSheetEstimatedTimeRow": "Iye àkókò Tí a gbèrò", "bottomSheetAccountRowDefaultAccountName": "Ibi-ìpa<PERSON>ọ́", "bottomSheetConnectRequestDisclaimer": "Ní àsopọ̀ sí àwọn òpó lórí ayélujára tí ó fọkàn tán nìkan", "bottomSheetSignInRequestDisclaimer": "Buwọ<PERSON><PERSON><PERSON> wọlé sí àwọn òpó lórí a<PERSON>éluj<PERSON>ra tí ó fọkàn tán nìkan", "bottomSheetSignatureRequestDisclaimer": "Jẹ́rìsí bí o bá fọkàntán òpó lórí ayélujára yìí nìkan.", "bottomSheetFeaturedTransactionDisclaimer": "Ìwọ yóò rí ìwò ìdúnáàdúrà kan ṣáájú kí o tó jẹ́rìsíi ní ìgbésẹ̀ tí ó kàn.", "bottomSheetIgnoreWarning": "Fojú fo ìkìlọ̀, ṣáà tẹ̀síwájú lọ́nàkọnà", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Kò rí àwọn àyípadà iye tí ó kù kankan. Jọ̀wọ́ tẹ̀síwájú pẹ̀lú ìfura kí o sì jẹ́rìsíi bí o bá fọkàntán òpó lórí ayélujára yìí nìkan.", "bottomSheetReadOnlyWarning": "O kàn ńwo àdírẹ́sì yìí nìkan ni. Ìwọ yóò nílò láti gbé e wọlé láti lè buwọ́lu àwọn ìdúnàádúrà àti àwọn ìfiránṣẹ̀.", "bottomSheetWebsiteIsUnsafeWarning": "Ààyè lórí a<PERSON>u<PERSON>ra y<PERSON>í léwu láti lò tí ó sì lè gbìyànjú láti jí àwọn owó rẹ.", "bottomSheetViewOnExplorer": "<PERSON><PERSON><PERSON> ló<PERSON>", "bottomSheetTransactionSubmitted": "Ti fi Ìdúnàádúrà lélẹ̀", "bottomSheetTransactionPending": "Ìdúnàádúrà Ńlọ lọ́wọ́", "bottomSheetTransactionFailed": "Ìdúnàádúrà Ti kùnà", "bottomSheetTransactionSubmittedDescription": "A ti fi ìdúnàádúrà rẹ lélẹ̀. O lè wòó lórí explorer.", "bottomSheetTransactionFailedDescription": "Ìdúnàádúrà rẹ ti kùnà. Jọ̀wọ́ gbìyànjú si.", "bottomSheetTransactionPendingDescription": "<PERSON> ń ṣiṣẹ́ lórí ìdúnàádúrà náà lọ́wọ́...", "transactionsFromInterpolated": "Lati: {{lati}}", "transactionsFromParagraphInterpolated": "<PERSON><PERSON><PERSON> {{from}}", "transactionsSolInterpolated": "{{iye}} SOL", "transactionsToday": "Òní", "transactionsToInterpolated": "Si: {{si}}", "transactionsToParagraphInterpolated": "Sí {{to}}", "transactionsYesterday": "Àná", "addEditAddressAdd": "Ṣàfikún adirẹsi", "addEditAddressDelete": "Pa adirẹsi rẹ", "addEditAddressDeleteTitle": "Ṣé o da ẹ lójú pé o fẹ́ pa adirẹsi yí rẹ́?", "addEditAddressSave": "Fi adirẹsi pamọ́", "dAppBrowserComingSoon": "Ẹ̀rọ Aṣàwárí dAppu nbọ laipẹ!", "dAppBrowserSearchPlaceholder": "Àwọn <PERSON><PERSON><PERSON>, àw<PERSON><PERSON>, URL", "dAppBrowserOpenInNewTab": "Ṣi ní táàbù tuntun", "dAppBrowserSuggested": "Tí a dá<PERSON>àá", "dAppBrowserFavorites": "<PERSON>w<PERSON>n tí o fẹ́ràn ju", "dAppBrowserBookmarks": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserBookmarkAdd": "Fi Búkúmaakì kun", "dAppBrowserBookmarkRemove": "Yọ Búkúmaakì kúrò", "dAppBrowserUsers": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserRecents": "Àwọn ti àìpẹ́", "dAppBrowserFavoritesDescription": "A má ṣàfihàn àwọn tí ó fẹ́ràn jùlọ níbi yi", "dAppBrowserBookmarksDescription": "A ó fi àwọn búkú<PERSON>ì rẹ hàn níbi", "dAppBrowserRecentsDescription": "Àwọn dapps tí o ṣe àsopọ̀ wọn láìpẹ́ yóò hàn níbí", "dAppBrowserEmptyScreenDescription": "Tẹ̀ URL tàbí kí ó ṣàwárí lórí ayélujára", "dAppBrowserBlocklistScreenTitle": "{{orísun}} tí wà ní títì pa! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom gbagbọ pé saiti yí ni ẹtan kosi ni ààbò tó péye fún lílo.", "part2": "A ti ṣàfihàn saiti yí gẹ́gẹ́bí àrà itọju-àgbègbè ibi ìfipamọ́ data latọdọ àwọn saiti ayédèrú àti ìtànjẹ. Tí ó bá gbagbọ wípé àti ṣàfihàn saiti na nípa àṣìṣe, jọ̀wọ́ jẹki ẹ̀sùn na ó di mímọ."}, "dAppBrowserLoadFailedScreenTitle": "Ti kùnà láti kórajọpọ̀", "dAppBrowserLoadFailedScreenDescription": "Àṣìṣe kan wà pẹ̀lú kíkórajọpọ̀ ojú-ìwé yìí", "dAppBrowserBlocklistScreenIgnoreButton": "Má ṣe ká i<PERSON> kún, ṣàfihàn biotilewu", "dAppBrowserActionBookmark": "Búkúmaakì", "dAppBrowserActionRemoveBookmark": "<PERSON><PERSON> búkúmaak<PERSON> kúrò", "dAppBrowserActionRefresh": "Sọọ́jí", "dAppBrowserActionShare": "Ṣe àj<PERSON><PERSON><PERSON>", "dAppBrowserActionCloseTab": "Pa táàbù dé", "dAppBrowserActionEndAutoConfirm": "<PERSON>òpin sí Jẹ́rìsí Láìsí ìlọ́wọ́si", "dAppBrowserActionDisconnectApp": "<PERSON><PERSON> àsopọ̀ ohun èlò", "dAppBrowserActionCloseAllTabs": "Pa gbogbo táàbù dé", "dAppBrowserNavigationAddressPlaceholder": "Tẹ URL kan l<PERSON><PERSON>", "dAppBrowserTabOverviewMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si", "dAppBrowserTabOverviewAddTab": "<PERSON> kun", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON> dé", "dAppBrowserClose": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "Fi Búkúmaakì kun", "dAppBrowserTabOverviewRemoveBookmark": "Yọ Búkúmaakì kúrò", "depositAssetListSuggestions": "Àwọn àmọ̀ràn", "depositUndefinedToken": "Pẹ̀lẹ́, ó kó lè fi tokini yí pamọ́", "onboardingImportRecoveryPhraseDetails": "<PERSON><PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseVerifyTitle": "Ṣé o ti kọ àwọn Gbólóhùn Ìràpadà ìkọ̀kọ̀ silẹ?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Láìsí gbólóhùn ìràpadà ìkọ̀kọ̀ ó kó ni lè ní anfaani sì kọ́kọ́rọ́ rẹ tàbí àwọn ohun ìní kankan pẹ̀lú rẹ.", "onboardingCreateRecoveryPhraseVerifyYes": "Bẹẹni", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "A kò ní àṣey<PERSON><PERSON><PERSON> láti gbé akanti j<PERSON>, jọ̀wọ́ tún gbìyànjú sì.", "onboardingDoneDescription": "O wa ni ayé púpò láti jẹ ìgbádùn àpamọ́wọ́ rẹ.", "onboardingDoneGetStarted": "<PERSON>ya Bẹ̀rẹ̀", "zeroBalanceHeading": "Jẹ́ ká bẹ̀rẹ̀!", "zeroBalanceBuyCryptoTitle": "<PERSON>", "zeroBalanceBuyCryptoDescription": "Ra krypto rẹ àkọ́kọ́ pẹ̀lú káàdì dẹ́ẹ́ẹ́bìtì tàbí kírẹ́ẹ́dìtì kan.", "zeroBalanceDepositTitle": "Fi Krypto ráńṣẹ́", "zeroBalanceDepositDescription": "Fi krypto rẹ pamọ́ sí àpamọ́wọ́ tàbí ibi pàṣípààrọ̀ mìíràn.", "onboardingImportAccountsEmptyResult": "A kò rí <PERSON> akanti", "onboardingImportAccountsAccountName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ọ́ {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Ibi-ìpamọ́ Ìdọ́rẹ̀ẹ́", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ìwé àkọọ́lẹ̀ {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Àwa ti rí ibi-ìpamọ́ {{numberOfWallets}} tí ó ńṣiṣẹ́", "onboardingImportAccountsFoundAccounts_other": "Àwa ti rí àwọn ibi-ìpamọ́ {{numberOfWallets}} tí ó ńṣiṣẹ́", "onboardingImportAccountsFoundAccountsNoActivity_one": "Àwa ti rí ibi-<PERSON> {{numberOfWallets}}", "onboardingImportAccountsFoundAccountsNoActivity_other": "Àwa ti rí àwọn ibi-<PERSON> {{numberOfWallets}}", "onboardingImportRecoveryPhraseLessThanTwelve": "Gbólóhùn gbọ́dọ̀ jẹ ó kéré jù ọ̀rọ̀ méjìlá.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Gbólóhùn gbọ́dọ̀ jẹ ọ̀rọ̀ méjìlá gere gé tàbí mẹrin lè lógún.", "onboardingImportRecoveryPhraseWrongWord": "Àwọn ọ̀rọ̀ tí kò tọ: {{ àwọn ọ̀rọ̀ }}.", "onboardingProtectTitle": "Da bobo àpamọ́wọ́ rẹ", "onboardingProtectDescription": "<PERSON><PERSON><PERSON> ìd<PERSON><PERSON><PERSON><PERSON>bò ìdánimọ̀ nípa tara kún-un yóò ríi dájú wípé ìwọ nìkan ni o lè wọlé sí àpamọ́wọ́ rẹ.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "Idanim<PERSON> O<PERSON>", "onboardingProtectButtonHeadlineFingerprint": "Tẹ̀ka", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Lo {{ Iruifasẹsi }} Ifaṣẹsi", "onboardingProtectError": "Nkan tí ó yẹ kan tí ṣẹlẹ̀ nígbà tí a ńṣe ifaṣẹsi, jọ̀wọ́ gbìyànjú si", "onboardingProtectBiometryIosError": "Fífi àṣẹ sí ìdánimọ̀ ni a ti ṣètò nínú Phantom ṣùgbọ́n tí a ti mú má ṣiṣẹ́ nínú àwọn Ètò Ẹ̀rọ. Jọ̀wọ́ ṣí Àwọn ètò > Phantom > Ìdánimọ̀ Ojú tàbí Ìdánimọ̀ Ọwọ́ láti tún mu ṣiṣẹ́.", "onboardingProtectRemoveAuth": "Da ifaṣẹsi dúró", "onboardingProtectRemoveAuthDescription": "Ṣé o da ẹ lójú pé o fẹ́ dá ifaṣẹsi dúró?", "onboardingWelcomeTitle": "Káàbọ̀ sí Phantom", "onboardingWelcomeDescription": "<PERSON><PERSON><PERSON> bẹ̀rẹ̀, ṣẹ̀da àpamọ́wọ́ titun kan tàbí gbé ọ̀kan tí ó ti wà tẹ́lẹ̀ wọlé.", "onboardingWelcomeCreateWallet": "Da àpamọ́wọ́ tuntun silẹ", "onboardingWelcomeAlreadyHaveWallet": "Mo ti ní àpamọ́wọ́ kan tẹ́lẹ̀", "onboardingWelcomeConnectSeedVault": "So Ọ̀rọ̀ ìgbaniwọlé pọ̀mọ́ Ibi ìfi nǹkan pamọ́ sí", "onboardingSlide1Title": "Tí ó wà lábẹ́ àkóso rẹ", "onboardingSlide1Description": "Àpamọ́wọ́ rẹ ní ààbò wíwọlé sí pẹ̀lú ìdánimọ̀ nípa tara, díd<PERSON> jìbìtì mọ̀ àti àtìlẹyìn 24/7.", "onboardingSlide2Title": "Ilé tí ó dára jùlọ fún àwọn NFT rẹ", "onboardingSlide2Description": "Ṣe àkóso fífi sórí àtẹ, jó ìfiráńṣẹ́ tí a kò bèèrè fún, kí o sì wà ní ìmúdójúìwọ̀n pẹ̀lú àwọn ìwífúnmi tí ó ńrannilọ́wọ́.", "onboardingSlide3Title": "Ṣe púpọ̀ si pẹ̀lú àwọn tókìnì rẹ", "onboardingSlide3Description": "Fipamọ́, pààrọ̀, fi owó si kí o sì jèrè, firáńṣẹ́ kí o gbà á — láì kúrò nínú àpamọ́wọ́ rẹ láí. ", "onboardingSlide4Title": "Ṣe àwárí Web3 tí ó dára jùlọ", "onboardingSlide4Description": "Wá àwọn ohun èlò àti àwọn àkójọpọ̀ tí ó ní olùwá-ayélujára kiri tí ó ní ohun èlò nínú.", "onboardingMultichainSlide5Title": "À<PERSON><PERSON><PERSON>́wọ́ kan fún ohun gbogbo", "onboardingMultichainSlide5Description": "Ní ìrí<PERSON>í fún gbogbo Solana, Ethereum, àti Polygon ní àyíká olùmúlò kan ṣoṣo tí ó rọrùn láti lò.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Ní ìrírí fún gbogbo Solana, Ethereum, Polygon àti Bitcoin ní àyíká olùmúlò kan ṣoṣo tí ó rọrùn láti lò.", "requireAuth": "Nílò ifaṣẹsi", "requireAuthImmediately": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> kan", "availableToSend": "<PERSON> wà <PERSON> Firáńṣẹ́", "sendEnterAmount": "Tẹẹ Iye", "sendEditMemo": "Ṣàtúnṣe Ìfiráńṣẹ́ tí a sopọ̀ mọ", "sendShowLogs": "<PERSON> àwọn <PERSON> han", "sendHideLogs": "Fi àwọn Amì <PERSON>", "sendGoBack": "Padà Sẹ́yìn", "sendTransactionSuccess": "A ti ṣe àṣeyọrí nípa fífi tokini rẹ ránṣẹ́ si", "sendInputPlaceholder": "@orúkọ tàbí àdírẹ́sì", "sendInputPlaceholderV2": "orúkọ olùmúlò tàbí àdírẹ́sì", "sendPeopleTitle": "<PERSON><PERSON><PERSON><PERSON>", "sendDomainTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ló<PERSON>", "sendFollowing": "Ń tẹ̀le", "sendRecentlyUsedAddressLabel": "Tí ó lo {{ìgbékalẹ̀Niakoko}} sẹ́yìn", "sendRecipientAddress": "Adirẹsi ẹni tó ń gba", "sendTokenInterpolated": "Fi ránṣẹ́ {{Amito<PERSON>}}", "sendPasteFromClipboard": "Lẹẹ láti inú clipboard", "sendScanQR": "Ya Koodi QR", "sendTo": "Sí:", "sendRecipientZeroBalanceWarning": "Àdírẹ́sì àpamọ́wọ́ yìí kò ní iye kankan tí ó kù tí kò sì hàn nínú ìtàn ìdúnàádúrà rẹ àìpẹ́. Jọ̀wọ́ ri dájú wípé àdírẹ́sì náà tọ́.", "sendUnknownAddressWarning": "<PERSON><PERSON><PERSON> kìí ṣe àdírẹ́sì tí o ti ní ìfarakínra pẹ̀lú láìpẹ̀ẹ́. Jọ̀wọ́ tẹ̀síwájú pẹ̀lú ìfura.", "sendSameAddressWarning": "<PERSON><PERSON><PERSON> ni àdírẹ́sì rẹ lọ́wọ́lọ́wọ́. Fífiráńṣẹ́ yóò ná owó fífiráńṣẹ́ láìsí owó mìíràn tí ó kù.", "sendMintAccountWarning": "Èyí jẹ́ ìṣẹ̀dá àdírẹ́sì ibi-ìpamọ́ kan. O kò lè fi owó ráńṣẹ́ sí àdírẹ́sì yìí ní bí yóò ṣe yọrí sí ìpàdànú títí láílái.", "sendCameraAccess": "<PERSON><PERSON><PERSON> sì Kamẹra", "sendCameraAccessSubtitle": "L<PERSON>ti yà koodi QR, ó ní láti fi àyè gba anfaani sì kamẹra. Ṣé o má fẹ́ràn lati ṣí àwọn Ètò bayi?", "sendSettings": "<PERSON><PERSON><PERSON><PERSON>", "sendOK": "OK", "invalidQRCode": "QR koodi yí kò ṣiṣẹ́.", "sendInvalidQRCode": "QR koodi yí ki ńṣe adirẹsi to ń ṣiṣẹ́", "sendInvalidQRCodeSubtitle": "Gbìyànjú sì tàbí pẹlu QR koodi miran.", "sendInvalidQRCodeSplToken": "Tokini tí kò ṣiṣẹ́ nínú koodi QR", "sendInvalidQRCodeSplTokenSubtitle": "QR koodi yí ni tokini tí kì ń ṣe tìrẹ tàbí èyí tí a kò dámọ̀.", "sendScanAddressToSend": "Ya {{<PERSON><PERSON><PERSON>}} adirẹsi láti fi àwọn owó ránṣẹ́", "sendScanAddressToSendNoSymbol": "Ya àdírẹ́sì láti fi owó ráńṣẹ́ sí", "sendScanAddressToSendCollectible": "Ya adirẹsi SOL láti fi àgbàsilẹ ránṣẹ́", "sendScanAddressToSendCollectibleMultichain": "Ya àdírẹ́sì láti fi àwọn àkójọpọ̀ ráńṣẹ́ sí", "sendSummary": "Ni <PERSON>", "sendUndefinedToken": "<PERSON><PERSON> b<PERSON>, a kò lè fi tokini yi ránṣẹ́", "sendNoTokens": "<PERSON><PERSON> sí <PERSON>wọn tokini kankan", "noBuyOptionsAvailableInCountry": "Kò sí àwọn àṣàyàn láti <PERSON> ní orílẹ̀-èdè rẹ", "swapAvailableTokenDisclaimer": "Iye awọn tókìnì díẹ̀ wà fún síso bulọkiṣeeni àwọn Nẹtiwọki pọ̀", "swapCrossSwapNetworkTooltipTitle": "Pípàrọ̀ J<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> àwọn Nẹtiwọki", "swapCrossSwapNetworkTooltipDescription": "Nígbàtí a bá páàrọ̀ jákèjádò àwọn Nẹtiwọki a gba níyànjú láti lo àwọn tókìnì tí ó wà fún iye owó tí ó lọ sílẹ̀ jùlọ àti ìdúnàádúrà tí ó yára jùlọ.", "settingsAbout": "Nípa Phantom", "settingsShareAppWithFriends": "<PERSON><PERSON> àwọn ọ̀rẹ́ rẹ wá", "settingsConfirm": "Bẹẹni", "settingsMakeSureNoOneIsWatching": "Rí dájú pé kò sí ẹnì tí ó ń wo ojú àwòrán rẹ", "settingsManageAccounts": "Ṣe à<PERSON><PERSON>o àwọn <PERSON>-<PERSON>́", "settingsPrompt": "Ṣé o da ẹ lójú pé ó fẹ́ tesiwaju?", "settingsSelectAvatar": "<PERSON><PERSON><PERSON>", "settingsSelectSecretPhrase": "<PERSON>ùn Ọ̀rọ̀ ìgbaniwọlé tí ó jẹ́ Àṣírí", "settingsShowPrivateKey": "Tẹẹ láti <PERSON><PERSON><PERSON>n kọ́kọ́rọ́ ikọ̀kọ̀ rẹ", "settingsShowRecoveryPhrase": "Tẹẹ láti <PERSON>àn gbólóhùn ìkọ̀kọ̀ rẹ", "settingsSubmitBetaFeedback": "Fi Ìjábọ̀ Beta Sílẹ̀", "settingsUpdateAccountNameToast": "Ti mú orúkọ ibi-ìpamọ́ dójú ìwọ̀n", "settingsUpdateAvatarToast": "Ti mú <PERSON>tar dójú ìwọ̀n", "settingsUpdateAvatarToastFailure": "Ti kùnà láti mú <PERSON>tar dójúìwọ̀n!", "settingsWalletAddress": "Àdírẹ́sì Ibi-ìpamọ́", "settingsWalletAddresses": "Àwọn àdírẹ́sì Ibi-ì<PERSON>m<PERSON>́", "settingsWalletNamePrimary": "Orúkọ Ibi-ìpamọ́", "settingsPlaceholderName": "Orúkọ", "settingsWalletNameSecondary": "Pàrọ̀ orúkọ àpamọ́wọ́ rẹ", "settingsYourAccounts": "<PERSON><PERSON><PERSON><PERSON> Rẹ", "settingsYourAccountsMultiChain": "Ṣeé lò fún ọ̀pọ̀lọpọ̀ bulọọkuṣeeni", "settingsReportUser": "<PERSON> ẹjọ́ O<PERSON>ù<PERSON><PERSON><PERSON> sùn", "settingsNotifications": "<PERSON><PERSON><PERSON><PERSON>", "settingsNotificationPreferences": "Iwifunni tí ó Yàn Lá<PERSON>ò", "pushNotificationsPreferencesAllowNotifications": "Fàyè gba Iwifunni", "pushNotificationsPreferencesSentTokens": "Àwọn Tokini tí ó fi ránṣẹ́", "pushNotificationsPreferencesSentTokensDescription": "Igbejade àwọn ifiranse tí àwọn tokini àti àwọn NFT", "pushNotificationsPreferencesReceivedTokens": "Àwọn Tokini tí ó gbà", "pushNotificationsPreferencesReceivedTokensDescription": "Igbewọle àwọn ifiranse tí àwọn tokini àti àwọn NFT", "pushNotificationsPreferencesDexSwap": "Àwọn ipàrọ̀", "pushNotificationsPreferencesDexSwapDescription": "Àwọn Ìpàrọ̀ l'ori àwọn appu tí a dámọ̀", "pushNotificationsPreferencesOtherBalanceChanges": "Àwọn Ìyípadà tí ó ti wà lórí Ìyè owó tí ó kú", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Àwọn idunọ<PERSON>ra tokini onír<PERSON>urú tí ó ní ipa l'ori iye owó tó kù tí ó ni", "pushNotificationsPreferencesPhantomMarketing": "<PERSON><PERSON><PERSON><PERSON> Ọwọ Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Àwọn ikéde ẹ̀yà ara ati awọn àlàyé tuntun tí gbogbogboo", "pushNotificationsPreferencesDescription": "Àwọn ètò yí ń ṣàkóso àwọn iwifunni tí ó ń fara hàn fún àpamọ́wọ́ tí ó ń ṣiṣẹ́ yí. Àpamọ́wọ́ kọ̀ọ̀kan ni àwọn ètò iwifunni tí wọn. L<PERSON>ti pa gbogbo àwọn iwifunni Phantom tí ó ń fara hàn, lọ sí <1>awon ètò ohun èlò</1>rẹ.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "O ṣòro fún ẹ láti dá àwọn iwifunni tí ó yán layo papọ.", "connectSeedVaultConnectSeed": "So Ọ̀rọ̀ ìgbaniwọlé kan pọ̀mọ́ ọ", "connectSeedVaultConnectSeedDescription": "So Phantom pọ̀mọ́ Ọ̀rọ̀ ìgbaniwọlé Ibi ìfi nǹkan pamọ́ lórí fóònù rẹ", "connectSeedVaultSelectAnAccount": "<PERSON> i<PERSON>-<PERSON><PERSON> kan", "connectSeedVaultSelectASeed": "<PERSON> Ọ̀rọ̀ ìgban<PERSON>w<PERSON> kan", "connectSeedVaultSelectASeedDescription": "Yan ọ̀rọ̀ ìgbaniwọlé tí yóò wù ọ́ láti sopọ̀ mọ́ Phantom", "connectSeedVaultSelectAnAccountDescription": "Yan ibi-ìpamọ́ tí yóò wù ọ́ láti <PERSON> pẹ̀lú Phantom", "connectSeedVaultNoAccountsFound": "<PERSON><PERSON> rí <PERSON>wọn ibi-ìpamọ́ kankan.", "connectSeedVaultSelectAccounts": "<PERSON> i<PERSON>-<PERSON>", "connectSeedVaultSelectAccountsDescription": "<PERSON> àwọn bi-ìpamọ́ tí yóò wù ọ́ láti <PERSON> pẹ̀lú Phantom", "connectSeedVaultCompleteSetup": "<PERSON><PERSON><PERSON>", "connectSeedVaultCompleteSetupDescription": "O ti ṣetán wàyí! Ṣàwárí wéèbù3 pẹ̀lú Phantom kí o sì lo Ọ̀rọ̀ ìgbaniwọlé Ibi ìfi nǹkan pamọ́ rẹ láti jẹ́rìsíí àwọn ìdúnàádúrà", "connectSeedVaultConnectAnotherSeed": "So Ọ̀rọ̀ ìgbaniwọlé mìíràn pọ̀mọ́ ọ", "connectSeedVaultConnectAllSeedsConnected": "Gbogbo ọ̀rọ̀ ìgbaniwọlé ti sopọ̀ mọ́ ọ", "connectSeedVaultNoSeedsConnected": "Kò fi àwọn ọ̀rọ̀ ìgbaniwọlé kankan pọ̀mọ́ ọ. Tẹ bọ́tìnì tí ó wà nísàlẹ̀ láti fún Ọ̀rọ̀ ìgbaniwọlé Ibi ìfi nǹkan pamọ́ sí láṣẹ.", "connectSeedVaultConnectAccount": "So ibi-ìpamọ́ pọ̀mọ", "connectSeedVaultLoadMore": "Ṣí púpọ̀ Síwájú si", "connectSeedVaultNeedPermission": "<PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultNeedPermissionDescription": "Lọ sí àwọn È<PERSON>ò láti gba Phantom láàyè láti lo àwọn ìgbaniláàyè Ọ̀rọ̀ ìgbaniwọlé Ibi ìfi nǹkan pamọ́ sí.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} owó", "stakeAmount": "<PERSON><PERSON>", "stakeAmountBalance": "Ìyè tí ó ku", "swapTopQuotes": "Àwọn Ìdíyelé {{numQuotes}} Tí ó wà lókè", "swapTopQuotesTitle": "<PERSON><PERSON><PERSON><PERSON> ì<PERSON><PERSON><PERSON><PERSON> Tí ó gbajúmọ̀ jù", "swapProvidersTitle": "<PERSON><PERSON><PERSON><PERSON>", "swapProvidersFee": "{{fee}} owó", "swapProvidersTagRecommended": "Àbájáde Tí ó dára j<PERSON>l<PERSON>", "swapProvidersTagFastest": "Tí ó yára j<PERSON>", "swapProviderEstimatedTimeHM": "{{hours}}h {{minutes}}m", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}}s", "stakeReview": "Ṣe agbeyẹwo", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "<PERSON><PERSON><PERSON>", "stakeReviewConfirm": "Jẹ́rìsí", "stakeReviewValidator": "<PERSON><PERSON><PERSON><PERSON><PERSON>w<PERSON>s<PERSON>", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Yíyí Ìfowó si padà Ti kùnà", "convertStakeStatusErrorMessage": "A kò lè yí fífi owó si rẹ padà sí {{poolTokenSymbol}}. Jọ̀wọ́ gbìyànjú si.", "convertStakeStatusLoadingTitle": "Ńyípadà sí {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "A tí ńbẹ̀rẹ̀ ìlànà láti yí fífi owó sí {{stakedTokenSymbol}} padà sí {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "<PERSON><PERSON><PERSON><PERSON>wó si {{poolTokenSymbol}} padà parí!", "convertStakeStatusSuccessMessage": "Jẹ àfikún àwọn èrè pẹ̀lú JitoSOL rẹ <1>níbí.</1>", "convertStakeStatusConvertMore": "<PERSON> Púpọ̀ padà", "convertStakePendingTitle": "Ńyi ìfowó si padà sí {{symbol}}", "convertToJitoSOL": "Yipadà sí JitoSOL", "convertToJitoSOLInfoDescription": "Yí SOL rẹ padà sí Jito SOL láti gba àwọn èrè kí o sì kópa nínú àwọn àwùjọ nẹtiwọki Jito.", "convertToJitoSOLInfoTitle": "Yipadà sí JitoSOL", "convertStakeBannerTitle": "<PERSON>í ìfowó si rẹ padà sí JitoSOL láti mú kí àwọn èrè rẹ pọ̀si ní bíi 15%", "convertStakeQuestBannerTitle": "Yí SOL tí o fowó si padà sí JitoSOL kí o sì gba àwọn èrè. Kọ́ ẹ̀kọ́ síwájú si.", "liquidStakeConvertInfoTitle": "Yipadà sí JitoSOL", "liquidStakeConvertInfoDescription": "Mú kí àwọn èrè rẹ pọ̀si nípa yíyí SOL rẹ tí o fowó sí padà sí JitoSOL. <1>Kọ́ ẹ̀kọ́ síwájú si</1>", "liquidStakeConvertInfoFeature1Title": "<PERSON>í ni ìdí láti fi owó si pẹ̀lú Jito?", "liquidStakeConvertInfoFeature1Description": "Fipamọ́ láti gba JitoSOL, èy<PERSON><PERSON> ó ń dàgbà pẹ̀lú ìfowó sí rẹ. Lòó ní àwọn ìlànà DeFi fún àwọn àfikún èrè. Pààrọ̀ JitoSOL nígbàmíì fún iye tí o kọ́kọ́ fi si pẹ̀lú àwọn ẹlẹ́kún èrè", "liquidStakeConvertInfoFeature2Title": "Àwọn èrè tí o ga gan ní àfiwé sí ọ̀pọ̀lọpọ̀", "liquidStakeConvertInfoFeature2Description": "<PERSON><PERSON> y<PERSON><PERSON> tan SOL rẹ ká láàrin àwọn olùfọwọ́sí tí ó dára jùlọ pẹ̀ú owó tí ó kéré jùlọ. Àwọn èrè MEV yóò túnbọ̀ fikún àwọn èrè rẹ.", "liquidStakeConvertInfoFeature3Title": "Ṣe àtìlẹyìn sí nẹtiwọki Solana", "liquidStakeConvertInfoFeature3Description": "Fí<PERSON>wó ńd<PERSON><PERSON><PERSON><PERSON>bò Solana nípa títan fí fowó si káàkiri sí ọ̀lọ̀lọpọ̀ àwọn olùfọwọ́sí, nípasẹ̀ èyí ó ń dín ewu iye àkókò tí àwọn olùfọwọ́sí fi wà tí wọ́n ńṣiṣẹ́ kù.", "liquidStakeConvertInfoSecondaryButton": "<PERSON><PERSON>í <PERSON> Nísinsìnyí", "liquidStakeStartStaking": "Bẹ̀rẹ̀ fífi owó si", "liquidStakeReviewOrder": "Tún Ìbéèrè ọjà yẹ̀wò", "convertStakeAccountListPageIneligibleSectionTitle": "Àwọn ibi-ìpamọ́ Ìfowó si Tí kò yẹ", "convertStakeAccountIneligibleBottomSheetTitle": "Àwọn ibi-ìpamọ́ Ìfowó si Tí kò yẹ", "convertStakeAccountListPageErrorTitle": "Ti kùnà làti gbé àwọn ibi-ìpamọ́ ìfowó si jáde", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON> bínú, nǹkankan kò lọ dédé tí a kò lè gbé àwọn ibi-ìpamọ́ ìfowó si náà jáde", "liquidStakeReviewYouPay": "Ìwọ Sanwó", "liquidStakeReviewYouReceive": "Ìwọ Gbàá", "liquidStakeReviewProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "liquidStakeReviewNetworkFee": "Owó Nẹtiwọki", "liquidStakeReviewPageTitle": "Jíjẹ́rìsíi", "liquidStakeReviewConversionFootnote": "Nígbàtí o bá fi owó sí àwọn tókìnì Solana ní pàṣípààrọ̀ fún JitoSOL ìwọ yóò gba iye JitoSOl tí ó kéré díẹ̀ kan. <1>Learn more</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Àkójọ fífi owó si ti Jito ń ṣe àtìlẹyìn fún ọ̀pọ̀ àwọn olùfọwọ́sí Solana tó ńṣiṣẹ́ jùlọ. O kò ní lè yí SOL tí o ti fowó si padà láti ọ̀dọ̀ àwọn olùfọwọ́sí tí kò ní àtìlẹyìn si JitoSol. <PERSON><PERSON>, SOL tí o ṣẹ̀ṣẹ̀ fowó si yóò gba bíi ọjọ́ 2 ṣáájú kí ó to yẹ fún yíyí padà JitoSOL.", "selectAValidator": "<PERSON><PERSON><PERSON><PERSON> kan", "validatorSelectionListTitle": "<PERSON><PERSON><PERSON><PERSON> kan", "validatorSelectionListDescription": "<PERSON> olùfọwọ́sí kan láti fowó sí SOL rẹ pẹ̀lú.", "stakeMethodDescription": "Jẹ èrè nípa lílo àwọn tókìnì SOL rẹ láti ràn Solana lọ́wọ́ kí ó gbèrú. <1>Learn more</1>", "stakeMethodRecommended": "Tí a g<PERSON>àníyàn<PERSON>ú", "stakeMethodEstApy": "Ṣírò APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "<PERSON><PERSON><PERSON>", "stakeMethodSelectionLiquidStakingDescription": "Fowó si SOL láti jẹ àwọn èrè tí ó ga, ṣe ìrànlọ́wọ́ láti dáàbòbò Solana kí o sì gba JitoSOL láti jẹ àwọn èrè àfikún.", "stakeMethodSelectionNativeStakingTitle": "<PERSON><PERSON><PERSON> owó si bíi <PERSON>ùfọwọ́sí", "stakeMethodSelectionNativeStakingDescription": "Fi owó si SOL láti gba àwọn èrè nígbàtí ò ń ṣe iranlọ́wọ́ láti dáàbò<PERSON>ò <PERSON>.", "liquidStakeMintStakeSOL": "Fi owó sì SOL", "mintJitoSOLInfoPageTitle": "Sísọ Fífowó si pẹ̀lú Jito di mímọ̀", "mintJitoSOLFeature1Title": "<PERSON>í ni ìdí láti fi owó si pẹ̀lú Jito?", "mintJitoSOLFeature1Description": "Fipamọ́ láti gba JitoSOL, èy<PERSON><PERSON> ó ń dàgbà pẹ̀lú ìfowó sí rẹ. Lòó ní àwọn ìlànà DeFi fún àwọn àfikún èrè. Pààrọ̀ JitoSOL nígbàmíì fún iye tí o kọ́kọ́ fi si pẹ̀lú àwọn àlẹ́kún èrè", "mintJitoSOLFeature2Title": "Àwọn èrè tí o ga gan ní àfiwé sí ọ̀pọ̀lọpọ̀", "mintJitoSOLFeature2Description": "<PERSON><PERSON> y<PERSON><PERSON> tan SOL rẹ ká láàrin àwọn olùfọwọ́sí tí ó dára jùlọ pẹ̀ú owó tí ó kéré jùlọ. Àwọn èrè MEV yóò túnbọ̀ mú àwọn èrè rẹ pọ̀si.", "mintJitoSOLFeature3Title": "Ṣe àtìlẹyìn sí nẹtiwọki Solana", "mintJitoSOLFeature3Description": "Fífowó si ńdáàbòbò Solana nípa títan fífi owó si káàkiri sí ọ̀lọ̀lọpọ̀ àwọn olùfọwọ́sí, nípasẹ̀ èyí ó ń dín ewu iye àkókò tí àwọn olùfọwọ́sí fi wà tí wọ́n sì ńṣiṣẹ́ kù.", "mintLiquidStakePendingTitle": "Ṣíṣẹ̀dá fífi owó si", "mintStakeStatusErrorTitle": "Ṣíṣẹ̀dá <PERSON><PERSON><PERSON> si Ti kùnà", "mintStakeStatusErrorMessage": "Kò lè ṣẹ̀dá fífi owó si {{poolTokenSymbol}} rẹ. Jọ̀wọ́ gbìyànjú si.", "mintStakeStatusSuccessTitle": "Ṣíṣẹ̀dá fífi owó si {{poolTokenSymbol}} parí!", "mintStakeStatusLoadingTitle": "Ṣíṣẹ̀dá fífi owó si {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "A tí ńbẹ̀rẹ̀ ìlànà láti ṣẹ̀dá fífi owó si {{poolTokenSymbol}} rẹ.", "mintLiquidStakeAmountDescription": "Yan iye SOL tí yóò wù ọ́ láti fi owó sí pẹ̀lú Jito", "mintLiquidStakeAmountProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mintLiquidStakeAmountApy": "Ṣírò APY", "mintLiquidStakeAmountBestPrice": "<PERSON><PERSON> ow<PERSON>", "mintLiquidStakeAmountInsufficientBalance": "Iye tó kù tí kò tó", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} tí ó nílò láti fi owó si", "swapTooltipGotIt": "O gba a", "swapTabInsufficientFunds": "<PERSON><PERSON><PERSON>n owó tí kò to", "swapNoAssetsFound": "<PERSON><PERSON> sí àwọn ohun ìní kankan", "swapNoTokensFound": "A kò rí àwọn tókìnì kankan", "swapConfirmationTryAgain": "Tún gbì<PERSON>àn<PERSON>ú si", "swapConfirmationGoBack": "Padà sẹ́yìn", "swapNoQuotesFound": "A kò rí àwọn ì<PERSON>", "swapNotProviderFound": "A kò rí olùpèsè fún ìpàrọ̀ tokini yí. Dán tokini míràn wò.", "swapAvailableOnMainnet": "<PERSON><PERSON> nìkan ni ẹ̀yà yí wa", "swapNotAvailableEVM": "Kò sí pípààrọ̀ fún àwọn ibi-ìpamọ́ EVM síbẹ̀", "swapNotAvailableOnSelectedNetwork": "Pípàrọ̀ kò sí lórí nẹtiwọki tí a yàn", "singleChainSwapTab": "Nínú Nẹtiwọki", "crossChainSwapTab": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> àwọn nẹtiwọki", "allFilter": "Gbogbo rẹ̀", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON>àádúrà pípàrọ̀ nẹtiwọki", "bridgeRefuelDescription": "Owó ìdúnààádúrà pípàrọ̀ nẹtiwọki ńri dájú wípé o lè san àwọn owó ìdúnààádúrà lẹ́yìn tí o rékojá láti nẹtiwọki kan sí òmíràn.", "bridgeRefuelLabelBalance": "{{symbol}} rẹ", "bridgeRefuelLabelReceive": "Ìwọ Gbàá", "bridgeRefuelLabelFee": "<PERSON>ye owó Tí a gbèrò", "bridgeRefuelDismiss": "Tẹ̀síwájú láìsí owó ìdúnààádúrà pípàrọ̀ nẹtiwọki", "bridgeRefuelEnable": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ìdúnààádúrà pípàrọ̀ nẹtiwọki ṣiṣẹ́", "unwrapWrappedSolError": "Ṣíṣí silẹ ti ní Ìjakulẹ", "unwrapWrappedSolLoading": "Ṣíṣí silẹ...", "unwrapWrappedSolSuccess": "O wa ni ṣ<PERSON>ṣí", "unwrapWrappedSolViewTransaction": "Wo Ìdunọdura", "dappApprovePopupSignMessage": "Buwọlu Ifiranṣẹ́ ", "solanaPayFrom": "<PERSON><PERSON><PERSON>", "solanaPayMessage": "Ìfiránṣẹ̀", "solanaPayNetworkFee": "Owo Nẹtiwọki", "solanaPayFree": "Lọfẹ", "solanaPayPay": "Sanwó {{item}}", "solanaPayPayNow": "Sanwó Báyìí", "solanaPaySending": "Ńfi {{item}} ráńṣẹ́", "solanaPayReceiving": "Ńfi {{item}} ráńṣẹ́", "solanaPayMinting": "Ńṣẹ́dá {{item}}", "solanaPayTransactionProcessing": "A n ṣíṣe l'ori idunọdura rẹ lọwọ lọwọ, jọ̀wọ́ dúró.", "solanaPaySent": "A ti fi ránṣẹ́!", "solanaPayReceived": "Ti gbàá!", "solanaPayMinted": "Ti ṣẹ́dá!", "solanaPaySentNFT": "Ti fi NFT ránṣẹ́!", "solanaPayReceivedNFT": "Ti gba NFT!", "solanaPayTokensSent": "A ti ṣe àṣeyọrí nípa fífi àwọn tókìnì rẹ ránṣẹ́ si {{to}}", "solanaPayTokensReceived": "O ti gba àwọn tókìnì tuntun láti {{from}}", "solanaPayViewTransaction": "<PERSON><PERSON>", "solanaPayTransactionFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tí ni Ìjákulẹ̀", "solanaPayConfirm": "Jẹ́rìsí", "solanaPayTo": "sí", "dappApproveConnectViewAccount": "<PERSON>o akanti <PERSON> rẹ", "deepLinkInvalidLink": "Àsopọ̀ tí kò Ṣiṣẹ́", "deepLinkInvalidSplTokenSubtitle": "Eyí ni tokini tí kì ń ṣe tìrẹ tàbí èyí tí a kò dámọ̀.", "walletAvatarShowAllAccounts": "Ṣàfihàn gbogbo àw<PERSON>n akanti", "pushNotificationsGetInstantUpdates": "Gba awọn ì<PERSON>ò<PERSON>ìn ni kiakia", "pushNotificationsEnablePushNotifications": "Fàyè gba àwọn iwifunni tó fara hàn nípa àwọn ifiranse tí ó ti parí, àwọn ipàrọ̀ àti àwọn ìkéde", "pushNotificationsEnable": "Gba laye", "pushNotificationsNotNow": "<PERSON> <PERSON> ṣ<PERSON> nísinsìnyí", "onboardingAgreeToTermsOfServiceInterpolated": "Mo gbà fún <1>Àwọn Òfin Ìṣe</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, Mo ti fi pamọ́ sibikan", "onboardingCreateNewWallet": "Da Àpamọ́wọ́ Tuntun silẹ", "onboardingErrorDuplicateSecretRecoveryPhrase": "Gbólóhùn ọ̀rọ̀ ìgbaniwọlé yìí ti wà lórí àpamọ́wọ́ rẹ télẹ̀", "onboardingErrorInvalidSecretRecoveryPhrase": "Gbólóhùn ìràpadà ìkọ̀kọ̀ tí kò ṣiṣẹ́", "onboardingFinished": "O ti parí gbogbo ẹ!", "onboardingImportAccounts": "<PERSON><PERSON><PERSON> àw<PERSON><PERSON> Wọ̀ lé", "onboardingImportImportingAccounts": "Ńgbé àwọn <PERSON>mọ́wọ́ wọlé...", "onboardingImportImportingFindingAccounts": "Ńwá àwọn àpamọ́wọ́ tí ó ńṣíṣẹ́", "onboardingImportAccountsLastActive": "{{formattedTimestamp}} ṣíṣẹ́ tẹ́lẹ̀", "onboardingImportAccountsNeverUsed": "<PERSON><PERSON>i", "onboardingImportAccountsCreateNew": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tuntun", "onboardingImportAccountsDescription": "<PERSON><PERSON>n àwọn akanti àpamọ́wọ́ tí ó fẹ́ gbé wọlé", "onboardingImportReadOnlyAccountDescription": "Fi àdírẹ́sì tàbí orúkọ ààyè lórí ayélujára kan tí yóò wù ọ́ láti kíyèsi kun. Ìwọ yóò ní ààyè fún wíwò nìkan, tí o kò sì ní lè buwọ́lu àwọn ìdúnàádúrà àti àwọn ìfiránṣẹ̀.", "onboardingImportSecretRecoveryPhrase": "Gbé Gbólóhùn ọ̀rọ̀ ìgbaniwọlé Tí ó jẹ́ àṣírí wọlé", "onboardingImportViewAccounts": "<PERSON>o à<PERSON><PERSON><PERSON>", "onboardingRestoreExistingWallet": "Dá àpamọ́wọ́ tí ó nlo lọ́wọ́ lọ́wọ́ padà pẹ̀lú gbólóhùn ìràpadà ìkọ̀kọ̀ òní ọ̀rọ̀ méjìlá tàbí mẹrin lè lógún", "onboardingShowUnusedAccounts": "Ṣàfihàn Àwọn <PERSON>ti Ti o ko ti Lo", "onboardingShowMoreAccounts": "Ṣàfihàn <PERSON><PERSON><PERSON><PERSON>", "onboardingHideUnusedAccounts": "Fi Àwọn A<PERSON>ti Ti o ko ti Lo Pamọ́", "onboardingSecretRecoveryPhrase": "Gbólóhùn Ìràpadà Ìkọ̀kọ̀", "onboardingSelectAccounts": "<PERSON> Rẹ", "onboardingStoreSecretRecoveryPhraseReminder": "Eyi nìkan ni ọ̀nà tí ó má lè fi rí akanti rẹ gbà padà. Jọ̀wọ́ fi pamọ́ síbi tí ààbò wa!", "useTokenMetasForMintsUnknownName": "Tí a kò mọ̀", "timeUnitMinute": "isẹju", "timeUnitMinutes": "àwọn isẹju", "timeUnitHour": "wakati", "timeUnitHours": "<PERSON><PERSON><PERSON><PERSON> w<PERSON>", "espNFTListWithPrice": "O tòjọ {{OrúkọÀfihànNFT}} fún {{iyeOwó}} {{iyeTokiniAmi}} lórí {{OrúkọdApp}}", "espNFTListWithPriceWithoutDApp": "O tòjọ {{Orúk<PERSON>ÀfihànNFT}} fún {{iyeOwó}} {{iyeTokiniAmi}}", "espNFTListWithoutPrice": "O tòjọ {{OrúkọÀfihànNFT}} fún títà lórí {{OrúkọdApp}}", "espNFTListWithoutPriceWithoutDApp": "O tòjọ {{OrúkọÀfihànNFT}} fún títà", "espNFTChangeListPriceWithPrice": "O ṣe ìmúdójúwọ̀n àwọn ọjà tí ó wà lórí àtẹ {{OrúkọÀfihànNFT}} sì {{iyeOwó}} {{iyeTokiniAmi}} lórí {{OrúkọdApp}}", "espNFTChangeListPriceWithPriceWithoutDApp": "O ṣe ìmúdójúwọ̀n àwọn ọjà tí ó wà lórí àtẹ {{OrúkọÀfihànNFT}} sì {{iyeOwó}} {{iyeTokiniAmi}}", "espNFTChangeListPriceWithoutPrice": "O ṣe ìmúdójúwọ̀n àwọn ọjà tí ó wà lórí àtẹ {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "O ṣe ìmúdójúwọ̀n àwọn ọjà tí ó wà lórí àtẹ fún {{OrúkọÀfihànNFT}}", "espNFTBidBidderWithPrice": "O fi owó sílẹ̀ {{iyeOwó}} {{IyeTokiniAmi}} fún {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTBidBidderWithPriceWithoutDApp": "O fi owó sílẹ̀ {{iyeOwó}} {{IyeTokiniAmi}} fún {{OrúkọÀfihànNFT}}", "espNFTBidBidderWithoutPrice": "O fi owó sílẹ̀ fún {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTBidBidderWithoutPriceWithoutDApp": "O fi owó sílẹ̀ fún {{OrúkọÀfihànNFT}}", "espNFTBidListerWithPrice": "O fi owó titun sílẹ̀ {{iyeOwó}} {{IyeTokiniAmi}} fún {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTBidListerWithPriceWithoutDApp": "Owó tuntun tí ó fi sílẹ̀ {{iyeOwó}} {{IyeTokiniAmi}} fún {{OrúkọÀfihànNFT}}", "espNFTBidListerWithoutPrice": "O fi owó titun sílẹ̀ fún {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTBidListerWithoutPriceWithoutDApp": "Owó tuntun tí ó fi sílẹ̀ fún {{OrúkọÀfihànNFT}}", "espNFTCancelBidWithPrice": "O fagilé owó tí ó fi sílẹ̀ {{iyeOwó}} {{IyeTokiniAmi}} fún {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTCancelBidWithPriceWithoutDApp": "O fagilé owó tí ó fi sílẹ̀ {{iyeOwó}} {{IyeTokiniAmi}} fún {{OrúkọÀfihànNFT}}", "espNFTCancelBidWithoutPrice": "O fagilé owó tí o fi sílẹ̀ fún {{OrúkọÀfihànNFT}} lórí {{OrúkọdApp}}", "espNFTCancelBidWithoutPriceWithoutDApp": "O fagilé owó tí ó fi sílẹ̀ fún {{OrúkọÀfihànNFT}}", "espNFTUnlist": "O yọ {{OrúkọÀfihànNFT}} kú<PERSON><PERSON> lórí {{OrúkọdApp}}", "espNFTUnlistWithoutDApp": "O yọ́ kúrò lórí àtẹ {{OrúkọÀfihànNFT}}", "espNFTBuyBuyerWithPrice": "O ra {{OrúkọÀfihànNFT}} fún {{iyeOwó}} {{iyeTokiniAmi}} lórí {{OrúkọdApp}}", "espNFTBuyBuyerWithPriceWithoutDApp": "O ra {{Orúk<PERSON>ÀfihànNFT}} fún {{iyeOwó}} {{iyeTokiniAmi}}", "espNFTBuyBuyerWithoutPrice": "O ra {{OrúkọÀfihànNFT}} lór<PERSON> {{OrúkọdApp}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "O ra {{OrúkọÀfihànNFT}}", "espNFTBuySellerWithPrice": "O ta {{OrúkọÀfihànNFT}} fún {{iyeOwó}} {{iyeTokiniAmi}} lórí {{OrúkọdApp}}", "espNFTBuySellerWithPriceWithoutDApp": "O ta {{OrúkọÀfihànNFT}} fún {{iyeOwó}} {{iyeTokiniAmi}}", "espNFTBuySellerWithoutPrice": "O ta {{OrúkọÀfihànNFT}} lór<PERSON> {{Or<PERSON>kọdApp}}", "espNFTBuySellerWithoutPriceWithoutDApp": "O ta {{OrúkọÀfihànNFT}}", "espDEXSwap": "O pàrọ̀ {{àwọnTokiniIpaỌ̀rọ̀isalẹ}} fún {{àwọnTokiniIpaỌ̀rọ̀oke}} lórí {{OrúkọdAppu}}", "espDEXDepositLPWithPoolDisplay": "O fi pamọ́ {{ÀwọnTokiniIpaỌ̀rọ̀isalẹ}} sínú {{OrúkọÀfihànadágún}} adágún tí owó wá ninu rẹ lórí {{OrúkọdAppu}}", "espDEXDepositLPWithoutPoolDisplay": "O pàrọ̀ {{àwọnTokiniIpaỌ̀rọ̀isalẹ}} fún {{àwọnTokiniIpaỌ̀rọ̀oke}} lórí {{OrúkọdAppu}}", "espDEXWithdrawLPWithPoolDisplay": "O yọ́ {{ÀwọnTokiniIpaỌ̀rọ̀òkè}} k<PERSON><PERSON><PERSON> n<PERSON> {{OrúkọÀfihànadágún}} adágún tí owó wá ninu rẹ lórí {{OrúkọdAppu}}", "espDEXWithdrawLPWithoutPoolDisplay": "O pàrọ̀ {{àwọnTokiniIpaỌ̀rọ̀isalẹ}} fún {{àwọnTokiniIpaỌ̀rọ̀oke}} lórí {{OrúkọdAppu}}", "espGenericTokenSend": "O fi ránṣẹ́ {{àwọnTokiniIpaỌ̀rọ̀isalẹ}}", "espGenericTokenReceive": "O gba {{àwọnTokiniIpaỌ̀rọ̀oke}}", "espGenericTransactionBalanceChange": "O pàrọ̀ {{àwọnTokiniIpaỌ̀rọ̀isalẹ}} fún {{àwọnTokiniIpaỌ̀rọ̀oke}}", "espUnknown": "AIMỌ", "espUnknownNFT": "NFT aimọ", "espTextFragmentAnd": "<PERSON><PERSON>", "externalLinkWarningTitle": "O ti fẹ́ fi Phantom sílẹ̀", "externalLinkWarningDescription": "Kí o sì ṣí {{url}}. Ríi dájú wípé o ní ìgbékẹ̀lé nínú orísun yìí kí o tó ní ìbáṣepọ̀ pẹ̀lú rẹ̀.", "shortcutsWarningDescription": "Àwọn ọ̀nà-àbúj<PERSON> tí {{url}} pèsè. Ríi dájú wípé o ní ìgbékẹ̀lé nínú orísun yìí kí o tó ní ìbáṣepọ̀ pẹ̀lú rẹ̀.", "lowTpsBanner": "<PERSON><PERSON><PERSON> pọ̀ lórí nẹtiwọki Solana lọ́wọ́ lọ́wọ́", "lowTpsMessageTitle": "Ọ̀pọ̀ èrò lórí nẹtiwọki Solana", "lowTpsMessage": "Nítorí pé èrò pọ̀ lórí <PERSON>, àwọn ìdúnàádúrà rẹ lè kùnà tàbí ní ìdádúró. Jọ̀wọ́ tún àwọn ìdúnàádúrà tí ó kùnà gbìyànjú.", "solanaSlow": "Nẹtiwọki Solana n kojú <PERSON>lọ́ra tí kò wọ́pọ̀", "solanaNetworkTemporarilyDown": "Nẹtiwọki Solana na kò ṣe lò fún ìgbà rampẹ", "waitForNetworkRestart": "Jọ̀wọ́ ni suuru fún nẹtiwọki na kí ó ṣẹ̀ṣẹ̀ bẹ̀rẹ̀. Àwọn owó yín kò ní ìdíwọ́.", "exploreCollectionsCarouselTitle": "Kínní ó Gbajúmọ̀", "exploreDropsCarouselTitle": "Kínni ó jẹ́ Tuntun", "exploreSortFloor": "Orí àtẹ", "exploreSortListed": "Tí wà fún rírà àti títà lórí àtẹ", "exploreSortVolume": "<PERSON><PERSON>", "exploreFetchErrorSubtitle": "Jọ̀wọ́ gbìyànjú si nígbàmíì.", "exploreFetchErrorTitle": "<PERSON>i kùnà láti wa rí.", "exploreTopCollectionsTitle": "Àwọn àkójọpọ̀ NFT Tí ó ga jùlọ", "exploreTopListLess": "<PERSON><PERSON><PERSON>", "exploreTopListMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> si", "exploreSeeMore": "<PERSON><PERSON><PERSON>", "exploreTrendingTokens": "Àwọn tókìnì Tí ó gbajúmọ̀ lọ́wọ́", "exploreVolumeTokens": "Iye Gíga jùlọ", "explorePriceChangeTokens": "Àwọn tí ó ńjèrè Púpọ̀ jùlọ", "explorePriceTokens": "Àwọn tókìnì nípa Iye owó", "exploreMarketCapTokens": "Àwọn tókìnì Tí ó ga jùlọ", "exploreTrendingSites": "Àwọn òpó lórí a<PERSON>élujára Tí ó gbajúmọ̀ lọ́wọ́", "exploreTopSites": "Àwọn òpó lórí ayélujára Tí ó ga jùlọ", "exploreTrendingCollections": "Àwọn àkójọpọ̀ Tí ó gbajúmọ̀ lọ́wọ́", "exploreTopCollections": "Àwọn àkójọpọ̀ Tí ó ga jùlọ", "collectiblesSearchCollectionsSection": "Àwọn àkójọpọ̀", "collectiblesSearchItemsSection": "Àwọn nǹkan", "collectiblesSearchNrOfItems": "Àwọn nǹkan {{ nrOfItems }}", "collectiblesSearchPlaceholderText": "Ṣàwárí àwọn àkójọpọ̀ rẹ", "collectionPinSuccess": "Ti lẹ àkójọpọ̀ mọ", "collectionPinFail": "Àkójọpọ̀ ti kùnà láti lẹ̀ mọ", "collectionUnpinSuccess": "Ti yọ àkójọpọ̀ kúrò", "collectionUnpinFail": "Àkójọpọ̀ ti kùnà láti yọ kúrò", "collectionHideSuccess": "Ti fi àkójọpọ̀ pamọ́", "collectionHideFail": "Àkójọpọ̀ ti kùnà láti wà ní ìpamọ́", "collectionUnhideSuccess": "Ti yọ àkójọpọ̀ kúrò ní ìpamọ́", "collectionUnhideFail": "Àkójọpọ̀ ti kùnà láti yọ kúrò ní ìpamọ́", "collectiblesSpamSuccess": "Ti jábọ̀ bíi ìfiráńṣẹ́ tí a kò béèrè fún", "collectiblesSpamFail": "Jíjábọ̀ bíi ìfiráńṣẹ́ tí a kò béèrè fún ti kùnà", "collectiblesSpamAndHiddenSuccess": "Ti jábọ̀ bíi ìfiráńṣẹ́ tí a kò bèèrè fún tí ó sì ti fipamọ́", "collectiblesNotSpamSuccess": "Ti jábọ̀ bíi ìfiráńṣẹ́ tí a béèrè fún", "collectiblesNotSpamFail": "Jíjábọ̀ bíi ìfiráńṣẹ́ tí a béèrè fún ti kùnà", "collectiblesNotSpamAndUnhiddenSuccess": "Ti jábọ̀ bíi ìfiráńṣẹ́ tí a béèrè fún tí kò sì fipamọ́", "tokenPageSpamWarning": "A kò jẹ́rìsíí tókìnì yìí. Ní ìbáṣepọ̀ pẹ̀lú àwọn tókìnì tí o fọkàntán nìkan.", "tokenSpamWarning": "A fi tókìnì yìí pamọ́ nítorí Phantom gbàgbọ́ wípé ó jẹ́ ìfiráńṣẹ́ tí a kò béèrè fún.", "collectibleSpamWarning": "A fi àwọn àgbàsílẹ̀ yìí pamọ́ nítorí Phantom gbàgbọ́ wípé ó jẹ́ ìfiráńṣẹ́ tí a kò béèrè fún.", "collectionSpamWarning": "A fi àwọn àgbàsílẹ̀ wọ̀nyìí pamọ́ nítorí Phantom gbàgbọ́ wípé ó jẹ́ ìfiráńṣẹ́ tí a kò béèrè fún.", "emojiNoResults": "<PERSON><PERSON> rí àw<PERSON><PERSON><PERSON> ìfi ìmọ̀lára hàn", "emojiSearchResults": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>", "emojiSuggested": "Tí a dá<PERSON>àá", "emojiSmileys": "Àwọn àwòrán ojú tó ńrẹ́rìn-ín & àwọn È<PERSON>n", "emojiAnimals": "Àwọn ẹrankọ & Ìṣẹ̀dá", "emojiFood": "Oúnjẹ & <PERSON><PERSON> mímu", "emojiTravel": "Ìrìn-<PERSON><PERSON><PERSON> & <PERSON><PERSON>ọn ibi", "emojiActivities": "Àwọn iṣẹ́-ṣíṣe", "emojiObjects": "Àwọn nǹkan", "emojiSymbols": "<PERSON><PERSON><PERSON><PERSON>", "emojiFlags": "<PERSON><PERSON><PERSON><PERSON>", "whichExtensionToConnectWith": "Ìpín àfikún wo lo fẹ́ sopọ̀ pẹ̀lú?", "configureInSettings": "Ṣeé ṣe àtúnto nínú àwọn Ètò → Àpamọ́wọ́ Ohun èlò Àtilẹ̀bá.", "continueWith": "Tẹ̀síwájú pẹ̀lú", "useMetaMask": "Lo MetaMask", "usePhantom": "Lo Phantom", "alwaysAsk": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dontAskMeAgain": "Máṣe tún bi mí mọ́", "selectWalletSettingDescriptionLine1": "Àwọn ohun èlò kan lè má pèsè àṣàyàn kan láti sopọ̀mọ́ Phantom.", "selectWalletSettingDescriptionLinePhantom": "Gẹ́gẹ́ bíi wíwá ọ̀nà àbáyọ, sísopọ̀ pẹ̀lú MetaMask yóò ṣí Phantom sílẹ̀ nígbàgbogbo dípò.", "selectWalletSettingDescriptionLineAlwaysAsk": "Gẹ́gẹ́ bíi wíwá ọ̀nà àbáyọ, nígbàtí o bá sopọ̀ pẹ̀lú MetaMask, àwa yóò bé<PERSON><PERSON><PERSON> lọ́wọ́ rẹ bí o bá fẹ́ lo Phantom dípò.", "selectWalletSettingDescriptionLineMetaMask": "Ṣíṣètò MetaMask bíi àtilẹ̀bá yóò mú àwọn dappu láti sopọ̀mọ Phantom.", "metaMaskOverride": "Àpa<PERSON>ọ́wọ́ Ohun èlò Àtilẹ̀bá", "metaMaskOverrideSettingDescriptionLine1": "Fún sísopọ̀ pẹ̀lú àwọn òpó lórí a<PERSON>élujára tí kò pèsè àṣàyàn kan láti lo <PERSON>.", "refreshAndReconnectToast": "Tun sọjí kí o sì tun sopọ̀mọ láti mú àwọn àyípadà rẹ lò", "autoConfirmUnavailable": "Kò sí", "autoConfirmReasonDappNotWhitelisted": "Kò sí nítorí àdéhùn nínú èyítí o ti wá kó sì nínú àwọn àkójọ tí a gbà láàyè fún ohun èlò yìí.", "autoConfirmReasonSessionNotActive": "Kò sí nítorí kò sí sáà ìjẹ́rìsí láìsí ìlọ́wọ́si tí ó ńṣiṣẹ́ lọ́wọ́. Jọ̀wọ́ mu ṣiṣẹ́ nísàlẹ̀.", "autoConfirmReasonRateLimited": "Kò sí nítorí dapp tí ò ńlò ńfi àwọn ìbéèrè tí ó pọ̀jù ráńṣẹ́.", "autoConfirmReasonUnsupportedNetwork": "Kò sí nítorí ìjẹ́rìsí láìsí ìlọ́wọ́sí kò tíì ṣe àtìlẹyìn fún nẹtiwọki yìí.", "autoConfirmReasonSimulationFailed": "Kò sí nítorí a kò lè mú ìdábòbòbò dánilój<PERSON>.", "autoConfirmReasonTabNotFocused": "Kò sí nítorí táàbù ààyè lórí ayélujára tí ò ńgbìyànjú láti jẹ́rìsí láìsí ìlọ́wọ́si kò ṣiṣẹ́.", "autoConfirmReasonNotUnlocked": "Kò sí nítorí àpamọ́wọ́ kò sí ní ṣíṣí.", "rpcErrorUnauthorizedWrongAccount": "Ìdúnàádúrà láti àdírẹ́sì kò bá àdírẹ́sì ibi-ìpamọ́ tí a ti yàn mu.", "rpcErrorUnauthorizedUnknownSource": "<PERSON><PERSON> lè pinnu or<PERSON><PERSON> ìb<PERSON>èrè RPC náà.", "transactionsDisabledTitle": "Ti mú àwọn ìdúnàádúrà má ṣiṣẹ́", "transactionsDisabledMessage": "Àdírẹ́sì rẹ kò lè dúnàádúrà ní líló Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "<PERSON> ńṣiṣẹ́ lọ́wọ́", "settingsTrustedAppDetailsCopiedToClipboardToast": "A ti da URL kọ sí klipboodu", "notEnoughSolScanTransactionWarning": "Ìdúnàádúrà yìí kùnà nítorí o kò ní SOL tí ó tó nínú ibi-ìpamọ́ rẹ. Jọ̀wọ́ fi SOL kún ibi-ìpamọ́ rẹ síwájú si kí o sì gbìyànjú si.", "transactionRevertedWarning": "Ìdúnàádúrà yìí ti padà sí bí ó ti wà ní ìgbà fífarawé. A lè pàdánù àwọn owó bí a bá filélẹ̀.", "slippageToleranceExceeded": "Ìdúnàádúrà yìí ti padà sí bí ó ti wà ní ìgbà fífarawé. Ti kọjá ìyàtọ̀ owó ìdíyelé tí a fàyègbà.", "simulationWarningKnownMalicious": "A gbàgbọ́ wípé ibi-ìpamọ́ yìí jẹ́ ẹ̀tàn. Fífọwọ́ sí rẹ lè jásí pípàdánù àwọn owó.", "simulationWarningPoisonedAddress": "Àdírẹ́sì yìí ní ìfurasí tí ó jọra pẹ̀lú àdírẹ́sì kan tí o fi owó ráńṣẹ́ sí láìpẹ́. Jọ̀wọ́ jẹ́rìsíi wípé èyí ni àdírẹ́sì náà tí ó tọ́ láti dènà pípàdánù àwọn owó sí jìbìtì kan.", "simulationWarningInteractingWithAccountWithoutActivity": "Èyí jẹ́ ibi-ìpamọ́ tí kò ní iṣẹ́-ṣíṣe kankan tẹ́lẹ̀. Fífi owó ráńṣẹ́ sí ibi-ìpamọ́ tí kò sí lè yọrí sí pípàdànú owó", "quests": "<PERSON><PERSON><PERSON><PERSON>", "questsClaimInProgress": "Gbígba èrè <PERSON> lọ́wọ́", "questsVerifyingCompletion": "Ńjẹ́rìsí píparí fún ìbéèrè", "questsClaimError": "Àṣìṣe wà pẹ̀lú gbígba èrè", "questsClaimErrorDescription": "Àṣìṣe kan wà pẹ̀lú gbígba èrè rẹ. Jọ̀wọ́ gbìyànjú si nígbàmíì.", "questsBadgeMobileOnly": "Alágbèkáá <PERSON>", "questsBadgeExtensionOnly": "Ìpín à<PERSON>k<PERSON>", "questsExplainerSheetButtonLabel": "Ti gbàá", "questsNoQuestsAvailable": "<PERSON><PERSON> sí àwọn ì<PERSON><PERSON><PERSON><PERSON><PERSON> kankan", "questsNoQuestsAvailableDescription": "Kò sí àwọn ìbéèrè kankan lọ́wọ́lọ́wọ́. Àwa yóò wífún ọ ní kété tí a bá fí àwọn tuntun kun.", "exploreLearn": "Kọ́ ẹ̀kọ́", "exploreSites": "Àwọn òpó lórí a<PERSON>", "exploreTokens": "<PERSON><PERSON><PERSON><PERSON>", "exploreQuests": "<PERSON><PERSON><PERSON><PERSON>", "exploreCollections": "Àwọn àkójọpọ̀", "exploreFilterByall_networks": "Gbogbo àwọn Nẹtiwọki", "exploreSortByrank": "Tí ó gbajúmọ̀ lọ́wọ́", "exploreSortBytrending": "Tí ó gbajúmọ̀ lọ́wọ́", "exploreSortByprice": "<PERSON><PERSON> ow<PERSON>", "exploreSortByprice-change": "<PERSON>ye owó Ti yípadà", "exploreSortBytop": "Tí ó ga", "exploreSortByvolume": "<PERSON><PERSON>", "exploreSortBygainers": "<PERSON>wọn tí ó <PERSON>", "exploreSortBylosers": "Àwọn tí ó <PERSON>", "exploreSortBymarket-cap": "Gbogbo ìye ì<PERSON>ó<PERSON>òwò Ọjà", "exploreSortBymarket_cap": "Gbogbo Iye Ìdókòwò Ọjà", "exploreTimeFrame1h": "1h", "exploreTimeFrame24h": "24h", "exploreTimeFrame7d": "7d", "exploreTimeFrame30d": "30d", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "<PERSON>w<PERSON>n àgbà sílẹ̀", "exploreCategoryMarketplace": "<PERSON><PERSON>", "exploreCategoryGaming": "Ṣíṣeré", "exploreCategoryBridges": "Ńso bulọkiṣeeni pọ̀", "exploreCategoryOther": "Òmíràn", "exploreCategorySocial": "Ìdọ́rẹ̀ẹ́", "exploreCategoryCommunity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "<PERSON><PERSON><PERSON> owó si", "exploreCategoryArt": "Iṣẹ́ ọnà", "exploreCategoryTools": "<PERSON><PERSON><PERSON><PERSON> ohun <PERSON>", "exploreCategoryDeveloperTools": "<PERSON><PERSON><PERSON><PERSON> ohun <PERSON>", "exploreCategoryHackathon": "Ìdíje ọlọ́jọ́-pípé", "exploreCategoryNFTStaking": "Fífi owó sí NFT", "exploreCategoryExplorer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryInscriptions": "Àwọn ohun tí a kọ sára nǹkan", "exploreCategoryBridge": "So bulọkiṣeeni pọ̀", "exploreCategoryAirdrop": "Kọ́ọ̀nì tí a pín fún ni", "exploreCategoryAirdropChecker": "Olùṣàyẹ̀wò Kọ́ọ̀nì tí a pín fún ni", "exploreCategoryPoints": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryQuests": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryShop": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryProtocol": "Ìlànà", "exploreCategoryNamingService": "Sísọ Ìṣẹ́ lórúkọ", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Olùtọpasẹ̀ àkójọpọ̀ ìdókòwò", "exploreCategoryFitness": "Àjíǹde-ara", "exploreCategoryDePIN": "DePIN", "exploreVolume": "<PERSON><PERSON>", "exploreFloor": "Orí àtẹ", "exploreCap": "Gbogbo Iye Ìdókòwò Ọjà", "exploreToken": "Tókìnì", "explorePrice": "<PERSON><PERSON> ow<PERSON>", "explore24hVolume": "Iye ni 24h", "exploreErrorButtonText": "<PERSON>ún G<PERSON>ì<PERSON> si", "exploreErrorDescription": "Àṣìṣe kan wà nígbàtí ó ńgbìyànjú láti kó àkóónú wíwákiri náà jọpọ̀. Jọ̀wọ́ tún sọjí kí ó si gbìyànjú si", "exploreErrorTitle": "Ti kùnà láti kó àkóónú wíwákiri jọpọ̀", "exploreNetworkError": "Àṣìṣe nẹtiwọki kan wáyé. Jọ̀wọ́ gbìyànjú si nígbàmíì.", "exploreTokensLegalDisclaimer": "Àwọn àtójọ tókìnì ni a ṣẹ̀dá nípa lílo détà ọjà tí a pèsè láti ọwọ́ olùpèsè onírúurú àwọn ilé-iṣẹ́ mìíràn tí ó ní CoinGecko, Birdeye àti Jupiter nínú. ṣíṣe iṣẹ́ dá lórí sáà wákàtí 24 ṣáájú. Ṣíṣeṣẹ́ àtẹ̀yìnwá kó tọ́kasí ṣíṣeṣẹ́ ọjọ́ iwájú.", "swapperTokensLegalDisclaimer": "Àwọn àtójọ tókìnì tí ó gbajúmọ̀ lọ́wọ́ ni a ṣẹ̀dá nípa lílo détà ọjà tí a pèsè láti ọwọ́ olùpèsè onírúurú àwọn ilé-iṣẹ́ mìíràn tí ó ní CoinGecko, Birdeye àti Jupiter nínú àtí látàrí àwọn tókìnì tí ó gbajúmọ̀ tí a ti ṣe pàṣípàrò láti ọwọ́ àwọn olùmúlò Phantom nípasẹ̀ Ẹnití ó ń ṣe pàṣípàrọ̀ ní sáà àkókò tí a ti sọ. Ṣíṣeṣẹ́ àtẹ̀yìnwá kó tọ́kasí ṣíṣeṣẹ́ ọjọ́ iwájú.", "exploreLearnErrorTitle": "Ti kùnà láti kó àkóónú ohun tí o kọ́ jọpọ̀", "exploreLearnErrorDescription": "Àṣìṣe kan wà nígbàtí ó ńgbìyànjú láti kó àkóónú ohun tí o kọ́ náà jọpọ̀. Jọ̀wọ́ tún sọjí kí ó si gbìyànjú si", "exploreShowMore": "<PERSON> púpọ̀ hàn", "exploreShowLess": "<PERSON> díẹ̀ hàn", "exploreVisitSite": "Bẹ Òpó lór<PERSON> a<PERSON> wò", "dappBrowserSearchScreenVisitSite": "Bẹ òpó lórí a<PERSON>lu<PERSON> wò", "dappBrowserSearchScreenSearchWithGoogle": "Ṣàwárí pẹ̀lú Google", "dappBrowserSearchScreenSearchLinkYouCopied": "Àsopọ̀ Tí o Ti dàkọ", "dappBrowserExtSearchPlaceholder": "<PERSON><PERSON> àwọn ò<PERSON><PERSON> ló<PERSON>, àwọ<PERSON> tó<PERSON>", "dappBrowserSearchNoAppsTokens": "<PERSON><PERSON> rí àwọn ohun èlò tàbí àwọn tókìnì kankan", "dappBrowserTabsLimitExceededScreenTitle": "Pa Táàbù Tí ó tipẹ́ dé bí?", "dappBrowserTabsLimitExceededScreenDescription": "O ní àwọn táàbù {{tabsCount}} ní ṣíṣí sílẹ̀. Láti ṣí sílẹ̀ si, ìwọ yóò nílò láti pa àwọn táàbù kan dé.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "Pa Gbogbo Táàbù dé", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: <PERSON><PERSON> sí à<PERSON> y<PERSON>í lórí a<PERSON>", "dappBrowserTabErrorHttp": "Ti dín<PERSON>m<PERSON>́, jọ̀wọ́ lo HTTPS", "dappBrowserTabError401Unauthorized": "401 A kò fun-ún láṣẹ", "dappBrowserTabError501UnhandledRequest": "501 Èsì tí a kò fọwọ́ kan", "dappBrowserTabErrorTimeout": "ÀKÓKÒÌSINMI: Olùpínṣẹ́ gba à<PERSON>ókò gan láti f<PERSON>ì", "dappBrowserTabErrorInvalidResponse": "Èsì tí kò ṣiṣẹ́", "dappBrowserTabErrorEmptyResponse": "<PERSON><PERSON><PERSON> tó <PERSON>fo", "dappBrowserTabErrorGeneric": "Àṣìṣe kan ti wáyé", "localizedErrorUnknownError": "N[kan tí kò tọ́ kan ti ṣẹlẹ̀, jọ̀wọ́ gbìyànjú si nígbàmíì.", "localizedErrorUnsupportedCountry": "<PERSON><PERSON> b<PERSON>ú, orilẹ̀-èdè rẹ kò ní àtilẹyìn lọ́wọ́lọ́wọ́.", "localizedErrorTokensNotLoading": "Ìṣòro kan wà nípa kíkó àwọn tókìnì jọpọ̀. Jọ̀wọ́ gbìyànjú sì.", "localizedErrorSwapperNoQuotes": "Kò sí pàṣípàrọ̀ nítorí kò ṣe àtìlẹyìn méjèèjì, f<PERSON><PERSON> owó si kéré, tàbí iye owó kéré. Gbìyànjú láti tún iye tókìnì tàbí iye owó ṣe.", "localizedErrorSwapperRefuelNoQuotes": "Kò rí àwọn ìdíyelé kankan. Gbìyànjú tókìnì tàbí iye mìíràn wò tàbí kí o mú owó ìdúnàádúrà pípàrọ̀ nẹtiwọki má ṣiṣẹ́.", "localizedErrorInsufficientSellAmount": "Iye tókìnì ti kéré jù. Mú iye pípàrọ̀ láti Ṣéènì kan sí òmíràn lọ sókè.", "localizedErrorCrossChainUnavailable": "<PERSON>ò sí pípàrọ̀ láti <PERSON> kan sí òmíràn lọ́wọ́lọ́wọ́, jọ̀wọ́ gbìyànjú si nígbàmíì.", "localizedErrorTokenNotTradable": "Ọ̀kan lára àwọn tókìnì tí a yàn kò ṣeé tà. Jọ̀wọ́ yan tókìnì mìíràn.", "localizedErrorCollectibleLocked": "Titi ibi-ìpamọ́ tókìnì pa.", "localizedErrorCollectibleListed": "Ibi-ìpamọ́ tí wà fún rírà àti títà lórí àtẹ.", "spamActivityAction": "Wo àwọn nǹkan tí a fipamọ́", "spamActivityTitle": "Iṣẹ́-ṣíṣe Tí a fipamọ́", "spamActivityWarning": "A fi ìdúnàádúrà yìí pamọ́ nítorí Phantom gbàgbọ́ wípé ó lè jẹ́ awúrúju.", "appAuthenticationFailed": "Ti kùnà láti fàṣẹsi", "appAuthenticationFailedDescription": "Ìṣòro kan wà pẹ̀lú ìgbìyànjú fífiàṣẹsí rẹ. Jọ̀wọ́ gbìyànjú si.", "partialErrorBalanceChainName": "À ńní ìṣòro pẹ̀lú mímú iye àwọn owó {{chainName}} rẹ tókù dójú òṣùwọ̀n. Àwọn owó rẹ wà ní ìpamọ́.", "partialErrorGeneric": "À ńní ìṣòro pẹ̀lú mímú àwọn nẹtiwọki dójú òṣùwọ̀n, l<PERSON>ra iye àwọn tókìnì rẹ tí ó kù àti iye owó dẹ̀ lè ti kọjá. Àwọn owó rẹ wà ní ìpamọ́.", "partialErrorTokenDetail": "À ńní ìṣòro pẹ̀lú mímú iye tókìnì rẹ tí ó kù dójú òṣùwọ̀n. Àwọn owó rẹ wà ní ìpamọ́.", "partialErrorTokenPrices": "À ńní ìṣòro pẹ̀lú mímú iye owó tókìnì rẹ dójú òṣùwọ̀n. Àwọn owó rẹ wà ní ìpamọ́.", "partialErrorTokensTrimmed": "À ń ní ìṣòro láti fi gbogbo àwọn tókìnì nínú àkójọpọ̀ ìdókòwò rẹ hàn. Àwọn owó rẹ wà ní ìpamọ́.", "publicFungibleDetailAbout": "Nípa", "publicFungibleDetailYourBalance": "<PERSON>ye owó Rẹ tó kù", "publicFungibleDetailInfo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailShowMore": "<PERSON> Púpọ̀ hàn", "publicFungibleDetailShowLess": "<PERSON> Díẹ̀ hàn", "publicFungibleDetailPerformance": "Ṣíṣeṣẹ́ 24h", "publicFungibleDetailSecurity": "Ìdábòbòbò", "publicFungibleDetailMarketCap": "Gbogbo Iye Ìdókòwò Ọjà", "publicFungibleDetailTotalSupply": "Àpapọ̀ Ìpèsè", "publicFungibleDetailCirculatingSupply": "Iye Ìpèsè tó wà", "publicFungibleDetailMaxSupply": "Iye Ìpèsè tó ga jùlọ", "publicFungibleDetailHolders": "Ẹnítí ó ní kryto", "publicFungibleDetailVolume": "<PERSON><PERSON>", "publicFungibleDetailTrades": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailTraders": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailUniqueWallets": "Àwọn àpamọ́wọ́ Tí ó jẹ́ àkànṣe", "publicFungibleDetailTop10Holders": "À<PERSON>ọ<PERSON> ohun ìkò nǹkan sí 10 Tí ó ga jùlọ", "publicFungibleDetailTop10HoldersTooltip": "Ń tọ́kasí ìdá nínú ọgọ́rùn-ún àpapọ̀ tí a pèsè lọ́wọ́lọ́wọ́ tí àwọn ẹni tí ó ní tókìnì 10 tí ó ga jùlọ ní. Ó jẹ́ òdiwọ̀n kan nípa bí ó ṣe rọrùn láti ṣe màgòmágó nípa iye owó rẹ̀.", "publicFungibleDetailMintable": "Tí ó ṣée ṣẹ̀dá", "publicFungibleDetailMintableTooltip": "A lè mú iye ìpèsè tókìnì pọ̀si láti ọwọ́ ẹni tí ó ni àdéhùn bí tókìnì kan bá ṣeé ṣẹ̀dá.", "publicFungibleDetailMutableInfo": "À<PERSON>à<PERSON>é Tí ó ṣée yípadà", "publicFungibleDetailMutableInfoTooltip": "Bí àlàyé tókìnì bíi or<PERSON>, l<PERSON><PERSON><PERSON>, àti àdírẹ́sì òpó lórí a<PERSON>luj<PERSON>ra bá ṣeé yípadà ó ṣeé yípadà láti ọwọ́ ẹni náà tí ó ni àdéhùn.", "publicFungibleDetailOwnershipRenounced": "Ti kọ Nínií sílẹ̀", "publicFungibleDetailOwnershipRenouncedTooltip": "Bí a bá ti kọ níni tókìnì sílẹ̀, kò sí ẹni tí ó lè mú kí àwọn iṣẹ́ bíi ṣẹ̀dá àwọn tókìnì síwájú si ṣẹlẹ̀.", "publicFungibleDetailUpdateAuthority": "Mú Àṣẹ dójúìwọ̀n", "publicFungibleDetailUpdateAuthorityTooltip": "Àṣẹ ìmúdójúwọ̀n ni àdírẹ́sì àpamọ́wọ́ tí ó lè yí àlàyé padà bí tókìnì kan bá ṣeé yípadà.", "publicFungibleDetailFreezeAuthority": "Gbégilé Àṣẹ", "publicFungibleDetailFreezeAuthorityTooltip": "Àṣẹ gbígbégilé náà ní àdírẹ́sì àpamọ́wọ́ tí ó lè dènà fífi àwọn owó ráńṣẹ́.", "publicFungibleUnverifiedToken": "A kò jẹ́rìsíí tókìnì yìí. Ní ìbáṣepọ̀ pẹ̀lú àwọn tókìnì tí o fọkàntán nìkan.", "publicFungibleDetailSwap": "Pààrọ̀ {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Pààrọ̀ {{tokenSymbol}} pẹ̀lú ohun èlò Phantom", "publicFungibleDetailLinkCopied": "Ti dàákọ sí klipb<PERSON>du", "publicFungibleDetailContract": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailMint": "Ṣẹ́dá", "unifiedTokenDetailTransactionActivity": "Iṣẹ́-ṣíṣe", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivityError": "Ti kùnà láti kó iṣẹ́-ṣíṣe láìpẹ́ jọpọ̀", "additionalNetworksTitle": "Àwọn nẹtiwọki Àfikún", "copyAddressRowAdditionalNetworks": "Àwọn nẹtiwọki Àfikún", "copyAddressRowAdditionalNetworksHeader": "Àwọn nẹtiwọki wọ̀nyìí ń lo àdírẹ́sì kannáà bíi Ethereum:", "copyAddressRowAdditionalNetworksDescription": "O lè lo àdírẹ́sì Ethereum rẹ láì l'éwu láti fi àwọn ohun ìní ráńṣẹ́ kí o sì gbà á lórí èyíkéyìí àwọn nẹtiwọki wọ̀nyìí.", "cpeUnknownError": "Àṣìṣe tí a kò mọ̀", "cpeUnknownInstructionError": "Àṣìṣe ìtọ́nisọ́nà tí a kò mọ̀", "cpeAccountFrozen": "A ti gbégilé ibi-ìpamọ́", "cpeAssetFrozen": "A ti gbégilé ohun ìní", "cpeInsufficientFunds": "Àpapọ̀ owó tí kò to", "cpeInvalidAuthority": "Àṣẹ tí kò ṣiṣẹ́", "cpeBalanceBelowRent": "Iye owó tí ó kù tí ó lọ sílẹ̀ ju ààlà owó àyálò ní a yọ kúrò", "cpeNotApprovedForConfidentialTransfers": "A kò fọwọ́ sí ibi-ìpamọ́ fún fífiráńṣẹ́ tí ó ní àṣírí", "cpeNotAcceptingDepositsOrTransfers": "<PERSON><PERSON>-ìpamọ́ kò gba àwọn ìfipamọ́ tàbí àwọn ìfiráńṣẹ́", "cpeNoMemoButRequired": "Kò sí ìfiráńṣẹ́ tí a sopọ̀ mọ ìtọ́nisọ́nà ti tẹ́lẹ̀; tí ó nílò fún olùgbà láti gba ìfáńṣẹ́ kan", "cpeTransferDisabledForMint": "A ti mú fífiráńṣẹ́ má ṣiṣẹ́ fún ṣíṣẹ̀dá yìí", "cpeDepositAmountExceedsLimit": "<PERSON><PERSON>ọ́ kọ já gbèdéke tí ó ga jùlọ", "cpeInsufficientFundsForRent": "Àpapọ̀ owó tí kò tó fún àyálò", "reportIssueScreenTitle": "Jábọ̀ Ìṣòro kan", "publicFungibleReportIssuePrompt": "Ìṣòro wo ni o fẹ́ jábọ̀ nípa {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "<PERSON><PERSON><PERSON><PERSON>é tí kò tọ́", "publicFungibleReportIssuePriceStale": "Iye owó kò dójú ìwọ̀n", "publicFungibleReportIssuePriceMissing": "Kò sí iye owó níbẹ̀", "publicFungibleReportIssuePerformanceIncorrect": "Ṣíṣeṣẹ́ 24h kò tọ́", "publicFungibleReportIssueLinkBroken": "Àwọn òpó Ìdọ́ọ̀rẹ́ kò ṣeé kànsí", "publicFungibleDetailErrorLoading": "Kò sí détà tókìnì", "reportUserPrompt": "Ìṣòro wo ni o fẹ́ jábọ̀ nípa @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "<PERSON><PERSON><PERSON><PERSON> àti Ìyọlẹ́nu", "reportUserOptionAbuseAndHarrassmentDescription": "Ìyọlẹ́nu tí a dar<PERSON> s<PERSON>, rí<PERSON><PERSON> ìyọlẹ́nu sókè, ìwà ipá nípa àwọn ìhalẹ̀mọ́ ni, àwọn àkóónú àti àwọn atọ́ka ìwà tí ó kún fún ìkórira", "reportUserOptionPrivacyAndImpersonationTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> láti jẹ́ ẹlòmíràn", "reportUserOptionPrivacyAndImpersonationDescription": "Ṣíṣe àjọ<PERSON>ín tàbí híhalẹ̀ láti tú àlàyé ti ara ẹni síta, díd<PERSON><PERSON><PERSON><PERSON>n láti jẹ́ ẹlòmíràn", "reportUserOptionSpamTitle": "Ìfiráńṣẹ́ tí a kò béèrè fún", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, àti àwọn òpó láti fa ewu", "reportUserSuccess": "Ti fi Ìjábọ́ Olùmúlò lélẹ̀.", "settingsClaimUsernameTitle": "Ṣẹ̀dá Orúkọ olùmúlò", "settingsClaimUsernameDescription": "Ìdánimọ̀ kan tí ó dáyàtọ́ tí ó dáyàtọ́ bíi àpamọ́wọ́ rẹ", "settingsClaimUsernameValueProp1": "Ìdánimọ̀ tí a mú rọrùn", "settingsClaimUsernameValueProp1Description": "Sọ wípé ódìgbà sí àwọn àdírẹ́sì tí ó díjú kí o sì sọ wípé ẹǹlẹ́ sí ìdánimọ̀ kan tí ó rọrùn láti lò", "settingsClaimUsernameValueProp2": "Tí ó yára tí ó sì Rọrùn", "settingsClaimUsernameValueProp2Description": "Fi ìrọ̀rùn fi krypto ráńṣẹ́ kí o sì gbà á, buwọ́lù wọlé sí àpamọ́wọ́ rẹ, kí o sì ní àsopọ̀ pẹ̀lú àwọn ọ̀rẹ́", "settingsClaimUsernameValueProp3": "<PERSON><PERSON><PERSON><PERSON> ní Múṣiṣẹ́pọ̀", "settingsClaimUsernameValueProp3Description": "So èyíkéyìí ibi-ìpamọ́ pọ̀mọ́ orúkọ olùmúlò rẹ yóò sì ṣiṣẹ́pọ̀ jákèjádò gbogbo àwọ ẹ̀rọ rẹ", "settingsClaimUsernameHelperText": "Orúkọ rẹ tí ó dáyàtọ̀ fún Ibi-ipamọ́ Phantom rẹ", "settingsClaimUsernameValidationDefault": "A kò lè yí orúkọ olùmúlò yìí padà nígbàmíì", "settingsClaimUsernameValidationAvailable": "Orúkọ olùmúlò ti wà", "settingsClaimUsernameValidationUnavailable": "Orúkọ olùmúlò kò sí", "settingsClaimUsernameValidationServerError": "<PERSON>ò lè ṣàyẹ̀wò bí or<PERSON>k<PERSON> olùmúlò bá wà, jọ̀wọ́ gbìyànjú si nígbàmíì", "settingsClaimUsernameValidationErrorLine1": "Orúkọ olùmúlò tí kò ṣiṣẹ́.", "settingsClaimUsernameValidationErrorLine2": "Àwọn orúk<PERSON> olùmúlò gbọ́dọ̀ wà láàrín {{minChar}} àti {{maxChar}} àwọn lẹ́tà tí ó gùn tí ó sì lè jẹ́ àwọn lẹ́tà àti nọ́ńbà nìkan.", "settingsClaimUsernameValidationLoading": "Ń ṣàyẹ̀wò bóyá orúkọ olùmúlò yìí wà...", "settingsClaimUsernameSaveAndContinue": "Fipamọ́ kí o sì Tẹ̀síwájú", "settingsClaimUsernameChooseAvatarTitle": "<PERSON>", "settingsClaimUsernameAnonymousAuthTitle": "Ìfaṣẹsí L<PERSON>ì lo orú<PERSON>", "settingsClaimUsernameAnonymousAuthDescription": "Buw<PERSON><PERSON><PERSON><PERSON> wọlé sí Ibi-ìpamọ́ Phantom rẹ láì lo orúkọ pẹ̀lú ìbuwọ́lù kan", "settingsClaimUsernameAnonymousAuthBadge": "<PERSON><PERSON><PERSON> bí èyí ṣe ń ṣiṣẹ́", "settingsClaimUsernameLinkWalletsTitle": "So àwọn àpamọ́wọ́ rẹ pọ̀ mọ", "settingsClaimUsernameLinkWalletsDescription": "Yan àwọn àpamọ́wọ́ tí ó farahàn lórí àwọn ẹ̀rọ mìíràn pẹ̀lú orúkọ olùmúlò rẹ", "settingsClaimUsernameLinkWalletsBadge": "Kò Ṣeé Wò Fún Gbogbogbò", "settingsClaimUsernameConnectAccountsTitle": "So àwọn <PERSON>-ì<PERSON>mọ́ pọ̀mọ", "settingsClaimUsernameConnectAccountsHelperText": "Àdírẹ́sì ṣéènì kọ̀ọ̀kan yóò ní àsopọ̀ pẹ̀lú orúkọ olùmúlù rẹ. Ìwọ lè yí ìwọ̀nyìí padà nígbàmíì.", "settingsClaimUsernameContinue": "Tẹ̀síwájú", "settingsClaimUsernameCreateUsername": "Ṣẹ̀dá Orúkọ olùmúlò", "settingsClaimUsernameCreating": "Ń ṣẹ̀dá Orúkọ olùmúlò...", "settingsClaimUsernameSuccess": "Ti ṣẹ̀dá Orúkọ olùmúlò!", "settingsClaimUsernameError": "A bá àṣìṣe kan pàdé ní ṣíṣẹ̀dá orúkọ olùmúlò rẹ", "settingsClaimUsernameTryAgain": "<PERSON>ún G<PERSON>ì<PERSON> si", "settingsClaimUsernameSuccessHelperText": "O lè lo orúkọ olùmúlò rẹ nínú gbogbo àwọn àpamọ́wọ́ Phantom rẹ", "settingsClaimUsernameSettingsSyncedTitle": "Tí <PERSON>ú Àwọn ètò ṣiṣẹ́pọ̀", "settingsClaimUsernameSettingsSyncedHelperText": "Sọ wípé ódìgbà sí àwọn àdírẹ́sì tí ó díjú kí o sì sọ wípé ẹǹlẹ́ sí ìdánimọ̀ kan tí ó rọrùn láti lò", "settingsClaimUsernameSendToUsernameTitle": "Firáńṣẹ́ sí Orúkọ olùmúlò", "settingsClaimUsernameSendToUsernameHelperText": "Fi ìrọ̀rùn fi krypto ráńṣẹ́ kí o sì gbà á, buwọ́lù wọlé sí àpamọ́wọ́ rẹ, kí o sì ní àsopọ̀ pẹ̀lú àwọn ọ̀rẹ́", "settingsClaimUsernameManageAddressesTitle": "Àwọn àdírẹ́sì Gbogbogbò", "settingsClaimUsernameManageAddressesHelperText": "Èyíkéyìí àwọn tókìnì tàbí àwọn àgbà sílẹ̀ tí a firáńṣẹ́ sí orúkọ olùmúlò rẹ ní yóò firáńṣẹ́ sí àwọn àdírẹ́sì wọ̀nyìí", "settingsClaimUsernameManageAddressesBadge": "Ṣeé Wò Fún Gbogbogbò", "settingsClaimUsernameEditAddressesTitle": "Ṣàkóso Àwọn àdírẹ́sì Gbogbogbò", "settingsClaimUsernameEditAddressesHelperText": "Èyíkéyìí àwọn tókìnì tàbí àwọn àgbà sílẹ̀ tí a firáńṣẹ́ sí orúkọ olùmúlò rẹ ní yóò firáńṣẹ́ sí àwọn àdírẹ́sì wọ̀nyìí. Yan àdírẹ́sì kan fún ṣéènì kan.", "settingsClaimUsernameEditAddressesError": "Àdírẹ́sì kan fún nẹtiwọki kan nìkan ni a gbà láàyè.", "settingsClaimUsernameEditAddressesEditAddress": "Ṣé àtúnṣe Àwọn àdírẹ́sì", "settingsClaimUsernameNoAddressesSaved": "A kò fi àwọn àdírẹ́sì kankan pamọ́", "settingsClaimUsernameSave": "Fipamọ́", "settingsClaimUsernameDone": "Ó ti parí", "settingsClaimUsernameWatching": "Ń wòó", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} (àwọn) <PERSON>wọn ibi-<PERSON><PERSON><PERSON>́", "settingsClaimUsernameNoOfAccountsSingular": "Ibi-ìpamọ́ 1", "settingsClaimUsernameEmptyAccounts": "<PERSON><PERSON> (àw<PERSON>n) Ibi-ìpamọ́ kankan", "settingsClaimUsernameSettingTitle": "Ṣẹ̀dá @orúkọolùmúlò rẹ", "settingsClaimUsernameSettingDescription": "Ìdánimọ̀ kan tí ó dáyàtọ́ fún àpamọ́wọ́ rẹ", "settingsManageUserProfileAbout": "Nípa", "settingsManageUserProfileAboutUsername": "Orúkọ olù<PERSON>", "settingsManageUserProfileAboutBio": "Ìtàn ara ẹni", "settingsManageUserProfileTitle": "Ṣàkóso <PERSON><PERSON><PERSON>", "settingsManageUserProfileManage": "Ṣàkóso", "settingsManageUserProfileAuthFactors": "Àwọn ọ̀nà Ìfàṣẹsí", "settingsManageUserProfileAuthFactorsDescription": "Yan àwọn gbólóhùn ọ̀rọ̀ ìgbaniwọlé tàbí àwọn kọ́kọ́rọ́ alád<PERSON><PERSON>i láti buwọ́lù wọlé sí Ibi-ìpamọ́ Phantom rẹ.", "settingsManageUserProfileUpdateAuthFactorsToast": "Ti mú àwọn ọ̀nà ìfàṣẹsí dójúìwọ̀n!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Ti kùnà láti mú àwọn ọ̀nà ìfàṣẹsí dójúìwọ̀n!", "settingsManageUserProfileBiography": "Tún Ìtàn ara ẹni kọ", "settingsManageUserProfileBiographyDescription": "Fi ìtàn ara ẹni kúkurú kan kún profaili rẹ", "settingsManageUserProfileUpdateBiographyToast": "Ti mú Ìtàn ara ẹni dójúìwọ̀n!", "settingsManageUserProfileUpdateBiographyToastFailure": "Ǹǹkankan Ṣẹlẹ̀. <PERSON><PERSON> si", "settingsManageUserProfileBiographyNoUrlMessage": "Jọ̀wọ́ yọ èyíkéyìí URLs kúrò ní ìtàn ara ẹni rẹ", "settingsManageUserProfileLinkedWallets": "Àwọn àpamọ́wọ́ Tí a sopọ̀mọ", "settingsManageUserProfileLinkedWalletsDescription": "Yan àwọn àpamọ́wọ́ tí ó farahàn lórí àwọn ẹ̀rọ mìíràn nígbàtí ò ń buwọ́lù wọlé sí Ibi-ìpamọ́ Phantom rẹ.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Ti mú àwọn àpamọ́wọ́ tí o sopọ̀mọ dójúìwọ̀n!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Ti kùnà láti mú àwọn àpamọ́wọ́ tí o sopọ̀mọ dójúìwọ̀n!", "settingsManageUserProfilePrivacy": "Àṣírí", "settingsManageUserProfileUpdatePrivacyStateToast": "Ti mú àṣírí dójúìwọ̀n!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Ti kùnà láti mú àṣírí dójúìwọ̀n!", "settingsManageUserProfilePublicAddresses": "Àwọn àdírẹ́sì Gbogbogbò", "settingsManageUserProfileUpdatePublicAddressToast": "Ti mú àwọn àdírẹ́sì gbogbogbò dójúìwọ̀n!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Ti kùnà láti mú àwọn àdírẹ́sì gbogbogbò dójúìwọ̀n!", "settingsManageUserProfilePrivacyStatePublic": "Gbogbogbò", "settingsManageUserProfilePrivacyStatePublicDescription": "Profaili àti àwọn àdírẹ́sì gbogbogbò rẹ hàn tí ó sì ṣeé wá láti ọwọ́ ẹnikẹ́ni", "settingsManageUserProfilePrivacyStatePrivate": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePrivateDescription": "Profaili rẹ ṣeé wá láti ọwọ́ ẹnikẹ́ni ṣùgbọ́n àwọn mìíràn gbọ́dọ̀ gba ààyè láti wo profaili àti àwọn àdírẹ́sì gbogbogbò rẹ", "settingsManageUserProfilePrivacyStateInvisible": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Profaili àti àwọn àdírẹ́sì gbogbogbò rẹ wà ní ìpamọ́ tí kò dẹ̀ ṣée wá láti ibikíbi", "settingsDownloadPhantom": "Sọ̀ Phantom kalẹ̀", "settingsLogOut": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "<PERSON>́wọ́ kan kun", "seedlessAddAWalletSecondaryText": "Buw<PERSON>́<PERSON>ù wọlé sí tàbí gbe àpamọ́wọ́ kan tí ó ti wà tẹ́lẹ̀ wọlé ", "seedlessAddSeedlessWalletPrimaryText": "Fi Àpamọ́wọ́ Tí kò nílò gbólóhùn ọ̀rọ̀ ìgbaniwọlé kun", "seedlessAddSeedlessWalletSecondaryText": "Lo ÌDÁNIMỌ̀ Apple, Google tàbí Imeeli", "seedlessCreateNewWalletPrimaryText": "Ṣẹ̀dá Àpamọ́wọ́ <PERSON>?", "seedlessCreateNewWalletSecondaryText": "<PERSON><PERSON><PERSON> yìí kò ní àpamọ́wọ́ kan, ṣé yóò wù ọ́ láti ṣẹ̀dá ọ̀kan bí?", "seedlessCreateNewWalletButtonText": "Ṣẹ̀dá Àpamọ́wọ́", "seedlessCreateNewWalletNoBundlePrimaryText": "A kò rí àpamọ́wọ́ kankan", "seedlessCreateNewWalletNoBundleSecondaryText": "<PERSON><PERSON><PERSON> y<PERSON>í kò ní àpamọ́wọ́ kankan", "seedlessCreateNewWalletNoBundleButtonText": "Padà sẹ́yìn", "seedlessEmailOptionsPrimaryText": "<PERSON> Rẹ", "seedlessEmailOptionsSecondaryText": "<PERSON> àpamọ́wọ́ kan pẹ̀lú ibi-ìpamọ́ Apple, tàb<PERSON> Google kún ", "seedlessEmailOptionsButtonText": "Tẹ̀síwájú pẹ̀lú <PERSON><PERSON><PERSON>", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Ṣẹ̀dá àpamọ́wọ́ pẹ̀lú ÌDÁNIMỌ̀ Apple rẹ", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Ṣẹ̀dá àpamọ́wọ́ pẹ̀lú imeeli Google rẹ", "seedlessAlreadyExistsPrimaryText": "Ibi-ìpamọ́ Ti wà Tẹ́lẹ̀", "seedlessAlreadyExistsSecondaryText": "I<PERSON>li yìí ti ní àpamọ́wọ́ kan ẹ́lẹ̀, ṣé yóò wù ọ́ láti buwọ́lù wọlé sí dípò bí?", "seedlessSignUpWithAppleButtonText": "Forúkọsílẹ̀ pẹ̀lú Apple", "seedlessContinueWithAppleButtonText": "Tẹ̀síwájú pẹ̀lú Apple", "seedlessSignUpWithGoogleButtonText": "Forúkọsílẹ̀ pẹ̀lú Google", "seedlessContinueWithGoogleButtonText": "Tẹ̀síwájú pẹ̀lú Google", "seedlessCreateAPinPrimaryText": "Ṣẹ̀dá PIN kan", "seedlessCreateAPinSecondaryText": "À ńlo èyí láti dáàbò<PERSON><PERSON> àpamọ́wọ́ rẹ lórí gbogbo àwọn ẹ̀rọ rẹ. <1><PERSON><PERSON><PERSON> kò ṣeé dápadà.</1>", "seedlessContinueText": "Tẹ̀síwájú", "seedlessConfirmPinPrimaryText": "Jẹ́rìsí PIN rẹ", "seedlessConfirmPinSecondaryText": "Bí o bá gbàgbé PIN yìí, o kò ní lè dá àpamọ́wọ́ rẹ padà lórí ẹ̀rọ tuntun kan.", "seedlessConfirmPinButtonText": "Ṣẹ̀dá PIN", "seedlessConfirmPinError": "PIN tí kò tọ́. Jọ̀wọ́ gbìyànjú si", "seedlessAccountsImportedPrimaryText": "Ti gbe Àwọn ibi-ìpamọ́ wọlé", "seedlessAccountsImportedSecondaryText": "<PERSON><PERSON><PERSON> gbé àwọn ibi-ìpamọ́ wọ̀nyìí wọlé sínú àpamọ́wọ́ rẹ láìsí ìlọ́wọ́si", "seedlessPreviouslyImportedTag": "Ti gbe wọlé tẹ́lẹ̀", "seedlessEnterPinPrimaryText": "Tẹ PIN rẹ si", "seedlessEnterPinInvalidPinError": "Ti tẹ PIN tí kò tọ́ si. Àwọn nọ́ńbà oní-nọ́nbà 4 nìkan ni a gbà láàyè", "seedlessEnterPinNumTriesLeft": "ìgbìyànjú {{numTries}} tí ó ṣẹ́kù.", "seedlessEnterPinCooldown": "Tún gbìyànjú ní {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN gbọ́dọ̀ jẹ́ oní-nọ́nbà 4 ní gẹ́lẹ́", "seedlessEnterPinMatch": "Àwọn PIN báramu", "seedlessDoneText": "Ó ti parí", "seedlessEnterPinToSign": "Tẹ PIN rẹ si láti buwọ́lù ìdúnàádúrà yìí", "seedlessSigning": "Bíbuw<PERSON><PERSON><PERSON><PERSON> wọ<PERSON>", "seedlessCreateSeed": "Ṣẹ̀dá gbólóhùn ọ̀rọ̀ ìgbaniwọlé àpamọ́wọ́ kan", "seedlessImportOptions": "Àwọn à<PERSON>à<PERSON>àn gbígbé<PERSON><PERSON><PERSON> mìíràn", "seedlessImportPrimaryText": "Àwọn à<PERSON><PERSON><PERSON><PERSON>n <PERSON>", "seedlessImportSecondaryText": "Gbé àpamọ́wọ́ kan tí ó ti wà tẹ́lẹ̀ wọlé pẹ̀lú gbólóhùn ọ̀rọ̀ ìgbaniwọlé, kọ́kọ́rọ́ alád<PERSON><PERSON>i, tàbí ohun èlò àpamọ́wọ́ rẹ", "seedlessImportSeedPhrase": "G<PERSON>é Gbólóhùn ọ̀rọ̀ ìgbaniwọlé wọlé", "seedlessImportPrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wọlé", "seedlessConnectHardwareWallet": "So <PERSON>un èlò <PERSON>́wọ́ pọ̀mọ", "seedlessTryAgain": "Tún gbì<PERSON>àn<PERSON>ú si", "seedlessCreatingWalletPrimaryText": "Ń ṣẹ̀dá àpamọ́wọ́", "seedlessCreatingWalletSecondaryText": "Ń fi àpamọ́wọ́ ìdọ́rẹ̀ẹ́ kan kun", "seedlessLoadingWalletPrimaryText": "Ń kó àpamọ́wọ́ jọpọ̀", "seedlessLoadingWalletSecondaryText": "Ń gbé àwọn àpamọ́wọ́ rẹ tí o ti sopọ̀mọ tí o sì ń wò wọlé", "seedlessLoadingWalletErrorPrimaryText": "Ti kùnà làti kó <PERSON>pamọ́wọ́ jọpọ̀", "seedlessCreatingWalletErrorPrimaryText": "Ti kùnà làti ṣẹ̀dá àpamọ́wọ́", "seedlessErrorSecondaryText": "Jọ̀wọ́ gbìyànjú si", "seedlessAuthAlreadyExistsErrorText": "<PERSON><PERSON><PERSON> tí a pèsè ti wà tẹ́lẹ̀ fún ibi-ìpamọ́ Phantom kan tí ó yàtọ̀", "seedlessAuthUnknownErrorText": "Àṣìṣe kan tí a kò mọ̀ wáyé, jọ̀wọ́ gbìyànjú si nígbàmíì", "seedlessAuthUnknownErrorTextRefresh": "Àṣìṣe kan tí a kò mọ̀ wáyé, jọ̀wọ́ gbìyànjú si nígbàmíì. Sọ ojú-ìwé náà jí láti gbìyànjú si.", "seedlessAuthErrorCloseWindow": "<PERSON> dé", "seedlessWalletExistsErrorPrimaryText": "Àpamọ́wọ́ ìdọ́rẹ̀ẹ́ kan ti wà lórí ẹ̀rọ rẹ tẹ́lẹ̀", "seedlessWalletExistsErrorSecondaryText": "Jọ̀wọ́ padà tàbí pa ìbòjú yìí dé", "seedlessValueProp1PrimaryText": "Ìṣètò láìs<PERSON><PERSON>nu", "seedlessValueProp1SecondaryText": "Ṣẹ̀dá àpamọ́wọ́ kan nípa lílo Ibi-ìpamọ́ Google tàbí Apple kan kí o sì bẹ̀rẹ̀ wíwá web3 kiri pẹ̀lú ìrọ̀rùn", "seedlessValueProp2PrimaryText": "Ti mú ìdá<PERSON><PERSON><PERSON><PERSON>bò gbèrú", "seedlessValueProp2SecondaryText": "Ti fi àpamọ́wọ́ rẹ pamọ́ pẹ̀lú ààbò tí ó sì wà jákèjádò ọ̀pọ̀lọpọ̀ àwọn onírúurú pàṣípàrọ̀", "seedlessValueProp3PrimaryText": "Gbígbàpadà nírọ̀rùn", "seedlessValueProp3SecondaryText": "Gba wíwọlé sí àpamọ́wọ́ rẹ padà pẹ̀lú Ibi-ìpamọ́ Google tàbí Apple rẹ àti PIN oní-nọ́nbà 4 kan", "seedlessLoggingIn": "Ń buwọ<PERSON><PERSON>ù wọlé...", "seedlessSignUpOrLogin": "Forúkọsílẹ̀ tàbí Buwọ́lù wọlé sí", "seedlessContinueByEnteringYourEmail": "Tẹ̀síwájú nípa títẹ̀ imeeli rẹ si", "seedless": "<PERSON><PERSON> nílò gbólóhùn ọ̀rọ̀ ìgbaniwọlé", "seed": "Àwọn gbólóhùn Ọ̀rọ̀ ìgbaniwọlé", "seedlessVerifyPinPrimaryText": "Jẹ́rìsíi PIN", "seedlessVerifyPinSecondaryText": "Jọ̀wọ́ tẹ nọ́ńbà PIN rẹ si láti tẹ̀síwájú", "seedlessVerifyPinVerifyButtonText": "Jẹ́rìsíi", "seedlessVerifyPinForgotButtonText": "Ti gbàgbé bí?", "seedlessPinConfirmButtonText": "Jẹ́rìsíi", "seedlessVerifyToastPrimaryText": "Jẹ́rìsíi PIN Rẹ", "seedlessVerifyToastSecondaryText": "A máa ńbéèrè lọ́wọ́ rẹ lẹ́ẹ́kọ̀kan láti jẹ́rìsíi sí PIN rẹ kí o ba a lè rántí rẹ̀. Bí o bá gbàgbé, o kò ní lè gba àpamọ́wọ́ rẹ padà.", "seedlessVerifyToastSuccessText": "A ti jẹ́rìsíi sí nọ́ńbà PIN rẹ!", "seedlessForgotPinPrimaryText": "Tún PIN ṣètò nípa lílo ẹ̀rọ mìíràn", "seedlessForgotPinSecondaryText": "<PERSON>ún ìd<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, o lè tún PIN rẹ ṣètò lórí àwọn ẹ̀rọ mìíràn ní èyítí o ti buwọ́lù wọlé sí nìkan", "seedlessForgotPinInstruction1PrimaryText": "Ṣí Àwọn ẹrọ Mìíràn", "seedlessForgotPinInstruction1SecondaryText": "Lọ sí àwọn rọ mìíràn ní ibití a ti buwọ́lù wọlé sí ibi-ìpamọ́ Phantom rẹ pẹ̀lú imeeli rẹ", "seedlessForgotPinInstruction2PrimaryText": "Lọ sí Àwọn è<PERSON>ò", "seedlessForgotPinInstruction2SecondaryText": "<PERSON><PERSON><PERSON> Àwọn ètò yan \"Ìdáàbòbò àti Pípa àṣírí mọ́\" kí o sì yan \"Tún PIN ṣètò\"", "seedlessForgotPinInstruction3PrimaryText": "Ṣètò PIN Rẹ Tuntun", "seedlessForgotPinInstruction3SecondaryText": "Lọ́gán tí o bá ti ṣètò PIN rẹ tuntun, o lè wá buwọ́lù wọlé sí àpamọ́wọ́ rẹ lórí ẹ̀rọ yìí nísìnyí", "seedlessForgotPinButtonText": "Mo ti ṣe àwọn ìgbésẹ̀ wọ̀nyìí", "seedlessResetPinPrimaryText": "<PERSON>ún PIN <PERSON>", "seedlessResetPinSecondaryText": "Tẹ PIN tuntun kan èyítí ìwọ yóò rántí si. Èyí ni à ńlò láti dáàbòbò àpamọ́wọ́ rẹ lórí gbogbo àwọn ẹ̀rọ rẹ", "seedlessResetPinSuccessText": "A ti mú nọ́ńbà PIN rẹ dójúìwọ́n!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Nípa ṣíṣẹ̀dà ibi-ìpamọ́ kan, o faramọ́ <1>Àwọn Òfin Ìṣe</1> àti <5>Ìlànà-ìṣe Pípa àṣírí mọ́</5>wa", "pageNotFound": "A kò rí ojú-<PERSON>wé kankan", "pageNotFoundDescription": "A kò tí já ọ sílẹ̀! Ojú-ìwé yìí kò sí, tàbí a ti gbe kúrò.", "webTokenPagesLegalDisclaimer": "Àlàyé dída owó lórí nǹkan ni a pèsè fún èrèdí ti àlàyé nìkan tí kìí sì ṣe fún ìmọ̀ràn tí ìnáwó. Détà ọjà tí àwọn ilé-iṣẹ́ mìíràn àti Phantom ti pèsè kò ṣ'ojú kankan lórí bí àlàyé náà ṣe péye tó.", "signUpOrLogin": "Forúkọsílẹ̀ tàbí buwọ́lù wọlé sí", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Nípa ṣíṣẹ̀dà ibi-ìpamọ́ kan, o faramọ́ <1>Àwọn Òfin Ìṣe</1> àti <5>Ìlànà-ìṣe Pípa àṣírí mọ́</5>wa", "feedNoActivity": "Kò sí iṣẹ́-ṣíṣe kánkán síbẹ̀", "followRequests": "Tẹ̀lé Àwọn ì<PERSON>", "following": "Ń tẹ̀le", "followers": "Àwọn olùtẹ̀lé", "follower": "Olùtẹ̀lé", "joined": "<PERSON>i da<PERSON>", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON><PERSON> sí <PERSON>n olùm<PERSON><PERSON> kankan", "noFollowing": "Kò sí Olùtẹ̀lé kankan", "noUsersFound": "<PERSON><PERSON> rí <PERSON> ol<PERSON><PERSON><PERSON><PERSON>", "viewProfile": "<PERSON><PERSON>", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}