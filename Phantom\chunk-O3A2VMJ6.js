import{E as r,R as s}from"./chunk-JD6NH5K6.js";import{o as t,rb as o}from"./chunk-WIQ4WVKX.js";import{a as g}from"./chunk-7X4NV6OJ.js";import{f as x,h as i,n}from"./chunk-3KENBVE7.js";i();n();var e=x(g());var A=t.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  width: 100%;
  height: 83px;
  padding: 16px;
`,b=t.div`
  margin-left: 12px;
  width: 100%;
`,h=t(o).attrs({size:14,weight:400,color:"#999",textAlign:"left"})``,u=t.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`,y=t(o).attrs({size:28,lineHeight:32,weight:600,color:"#FFFFFF",textAlign:"left"})`
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
`,L=({title:l,network:a,tokenType:p,symbol:m,logoUri:c,tokenAddress:f,amount:w,amountUsd:d})=>e.default.createElement(A,null,e.default.createElement(r,{image:{type:"fungible",src:c,fallback:m||f},size:44,tokenType:p,chainMeta:a}),e.default.createElement(b,null,e.default.createElement(u,null,e.default.createElement(h,null,l),e.default.createElement(s,{value:d,font:"caption",color:"textSecondary"})),e.default.createElement(y,null,w)));export{L as a};
//# sourceMappingURL=chunk-O3A2VMJ6.js.map
