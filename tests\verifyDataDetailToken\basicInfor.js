const axios = require('axios');

// Đ<PERSON>a chỉ token cần so sánh.
const TOKEN_ADDRESS = '6kjzL6NBtwhzdhZJASqHMimfaxNBGqKd2h6JHnnbRuao';

const solscanHeaders = {
    'accept': 'application/json, text/plain, */*',
    'origin': 'https://solscan.io',
    'referer': 'https://solscan.io/',
    'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
};

const projectApiHeaders = {
    'Content-Type': 'application/json',
    // *** CÁC GIÁ TRỊ ĐÃ ĐƯỢC CẬP NHẬT TỪ CURL MỚI CỦA BẠN ***
    'Cookie': '_hjSessionUser_6397832=eyJpZCI6IjJkZjEwZDc3LWJkNzQtNTQwZi1iZWI2LThkZTc5OTQ1NDBmOCIsImNyZWF0ZWQiOjE3NTEzNjcyMzQ2NDIsImV4aXN0aW5nIjp0cnVlfQ==; _ga=GA1.1.1282429955.1751367234; refreshToken=s%3AeyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6NCwiamlkIjoiMzU2M2M2ZmEtMzg3Yi00NTU4LThiMGQtNzk1YTkxMzgxNGJjIiwicmVmcmVzaFRva2VuIjp0cnVlLCJpYXQiOjE3NTU2NzEwMzMsImV4cCI6MTc1Njk2NzAzM30.5c7Z1yrKhHGF_2imhfH3o4yakBGkzxcI2MKgFhhZC_M.NX3wLuO5bz4clVls3XSxx68JdQcaUyoMYLDwVKf9SJA; _hjSession_6397832=eyJpZCI6IjYzY2MzMGMyLTlhNzgtNDJkMC1hYzE2LTMwNzZhYTU3ODJiMiIsImMiOjE3NTU2ODI1NTg4ODAsInMiOjEsInIiOjEsInNiIjowLCJzciI6MCwic2UiOjAsImZzIjowLCJzcCI6MH0=; authorization=s%3ABearer%20eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************.PKP9uX_7QyGi15C29FxZGQaWyiK0GHOgUQwULTwmNpQ.C%2BSyKzPUUBw7BLyrqDqapWflWqhWUZvnRHR79vstguE; AWSALB=KjcB0zD6g4Z+B82EGie+pJL0u8ld6th3LUR2vNa1fU31sKJY/jaI++4do96iTw65a5wM7EDs8l77673HsJWYr6Clcs07Zm6EE0Z/TW7d+9ea7u6//j9TF8yCffT3; AWSALBCORS=KjcB0zD6g4Z+B82EGie+pJL0u8ld6th3LUR2vNa1fU31sKJY/jaI++4do96iTw65a5wM7EDs8l77673HsJWYr6Clcs07Zm6EE0Z/TW7d+9ea7u6//j9TF8yCffT3; _ga_ME8GZB3L70=GS2.1.s1755681907%24o167%24g1%24t1755685361%24j57%24l0%24h0',
    'authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************.PKP9uX_7QyGi15C29FxZGQaWyiK0GHOgUQwULTwmNpQ',
    'origin': 'https://dex3.ai',
    'referer': 'https://dex3.ai/',
};


// --- Các hàm gọi API ---

async function getSolscanTokenInfo(address) {
    const url = `https://api-v2.solscan.io/v2/account?address=${address}&view_as=token`;
    const response = await axios.get(url, { headers: solscanHeaders });
    return response.data;
}

async function getSolscanHolderCount(address) {
    const url = `https://api-v2.solscan.io/v2/token/holder/total?address=${address}`;
    const response = await axios.get(url, { headers: solscanHeaders });
    return response.data;
}

async function getSolscanPoolsInfo(address) {
    const url = `https://api-v2.solscan.io/v2/token/pools?page=1&page_size=100&token[]=${address}`;
    const response = await axios.get(url, { headers: solscanHeaders });
    return response.data;
}

async function getProjectTokenInfo(address) {
    const url = 'https://api.dex3.ai/v2/token-detail/basic-info';
    const body = { address: address };
    const response = await axios.post(url, body, { headers: projectApiHeaders });
    return response.data;
}

async function getDex3HoldersInfo(address) {
    const url = 'https://api.dex3.ai/pool/holders/v2';
    // Lưu ý: Body của request này là địa chỉ token bạn đang phân tích, không phải địa chỉ trong curl ví dụ
    const body = { address: TOKEN_ADDRESS, tag: "all" }; 
    projectApiHeaders['clienttimestamp'] = Date.now().toString();
    const response = await axios.post(url, body, { headers: projectApiHeaders });
    return response.data;
}

// --- Hàm chính để chạy và so sánh ---

async function main() {
    try {
        console.log(`Đang lấy dữ liệu cho token: ${TOKEN_ADDRESS}...\n`);

        const [solscanTokenResponse, solscanHolderResponse, projectResponse, solscanPoolsResponse, dex3HoldersResponse] = await Promise.all([
            getSolscanTokenInfo(TOKEN_ADDRESS),
            getSolscanHolderCount(TOKEN_ADDRESS),
            getProjectTokenInfo(TOKEN_ADDRESS),
            getSolscanPoolsInfo(TOKEN_ADDRESS),
            getDex3HoldersInfo(TOKEN_ADDRESS)
        ]);
        
        console.log("\n--- Response từ API Holder của Dự án (dex3.ai) ---");
        console.log(JSON.stringify(dex3HoldersResponse, null, 2));
        console.log("---------------------------------------------------\n");


        // Tính tổng từ các pool của Solscan
        let solscanTotalVolume24h = 0;
        let solscanTotalTvl = 0;
        let solscanNumTraders24h = 0;
        if (solscanPoolsResponse.data && Array.isArray(solscanPoolsResponse.data)) {
            solscanPoolsResponse.data.forEach(pool => {
                solscanTotalVolume24h += (pool.total_volume_24h || 0);
                solscanTotalTvl += (pool.total_tvl || 0);
                solscanNumTraders24h += (pool.num_trader_24h || 0);
            });
        }

        // Tính Top 10 holding cho dex3.ai API
        let dex3Top10HoldingPct = 'Không có dữ liệu holder';
        if (dex3HoldersResponse && Array.isArray(dex3HoldersResponse.data) && dex3HoldersResponse.data.length > 0) {
            const holdersWithKolData = dex3HoldersResponse.data.filter(holder => 'kol_data' in holder);
            const top10Holders = holdersWithKolData.slice(0, 10);
            dex3Top10HoldingPct = top10Holders.reduce((sum, holder) => sum + holder.holding_pct, 0);
        }

        // 1. Trích xuất dữ liệu từ Solscan
        const solscanData = {
            name: solscanTokenResponse.data.metadata.data.name,
            symbol: solscanTokenResponse.data.metadata.data.symbol,
            description: solscanTokenResponse.data.tokenInfo.ownExtensions.description,
            website: solscanTokenResponse.data.tokenInfo.ownExtensions.website,
            twitter: solscanTokenResponse.data.tokenInfo.ownExtensions.twitter,
            totalSupply: parseInt(solscanTokenResponse.data.tokenInfo.supply) / Math.pow(10, solscanTokenResponse.data.tokenInfo.decimals),
            holderCount: solscanHolderResponse.data,
            price: solscanTokenResponse.metadata.tokens[TOKEN_ADDRESS].price_usdt,
            liquidity: solscanTotalTvl,
            marketCap: (parseInt(solscanTokenResponse.data.tokenInfo.supply) / Math.pow(10, solscanTokenResponse.data.tokenInfo.decimals)) * solscanTokenResponse.metadata.tokens[TOKEN_ADDRESS].price_usdt,
            creator: solscanTokenResponse.data.metadata.data.creators[0].address,
            createdAt: new Date(solscanTokenResponse.data.tokenInfo.created_time * 1000).toISOString(),
            totalVolume24h: solscanTotalVolume24h,
            numTraders24h: solscanNumTraders24h,
            top10HoldingsPct: "N/A"
        };

        // 2. Trích xuất dữ liệu từ API dự án
        const projectData = {
            name: projectResponse.data.baseToken.name,
            symbol: projectResponse.data.baseToken.symbol,
            description: projectResponse.data.baseToken.description,
            website: projectResponse.data.baseToken.website,
            twitter: projectResponse.data.baseToken.twitter,
            totalSupply: projectResponse.data.totalSupply,
            holderCount: projectResponse.data.holderCount,
            price: projectResponse.data.price,
            liquidity: projectResponse.data.liquidity,
            marketCap: projectResponse.data.marketCap,
            creator: projectResponse.data.tokenInfo.devs,
            createdAt: projectResponse.data.createdAt,
            totalVolume24h: projectResponse.data.data.h24.volume,
            numTraders24h: projectResponse.data.data.h24.traders,
            top10HoldingsPct: dex3Top10HoldingPct
        };

        // 3. Tạo đối tượng so sánh cho bảng
        const comparisonTable = {
            'Tên Token': { 'API Solscan': solscanData.name, 'API Dự án': projectData.name },
            'Ký hiệu': { 'API Solscan': solscanData.symbol, 'API Dự án': projectData.symbol },
            'Website': { 'API Solscan': solscanData.website, 'API Dự án': projectData.website },
            'Twitter': { 'API Solscan': solscanData.twitter, 'API Dự án': projectData.twitter },
            'Tổng cung': { 'API Solscan': solscanData.totalSupply.toLocaleString(), 'API Dự án': projectData.totalSupply.toLocaleString() },
            'Số người nắm giữ': { 'API Solscan': solscanData.holderCount.toLocaleString(), 'API Dự án': projectData.holderCount.toLocaleString() },
            'Giá (USD)': { 'API Solscan': solscanData.price, 'API Dự án': projectData.price },
            'Market Cap (USD)': { 'API Solscan': solscanData.marketCap.toLocaleString(undefined, { maximumFractionDigits: 0 }), 'API Dự án': projectData.marketCap.toLocaleString(undefined, { maximumFractionDigits: 0 }) },
            'Tổng Volume 24h (USD)': { 'API Solscan': solscanData.totalVolume24h.toLocaleString(undefined, { maximumFractionDigits: 0 }), 'API Dự án': projectData.totalVolume24h.toLocaleString(undefined, { maximumFractionDigits: 0 }) },
            'Tổng TVL (USD)': { 'API Solscan': solscanData.liquidity.toLocaleString(undefined, { maximumFractionDigits: 0 }), 'API Dự án': projectData.liquidity.toLocaleString(undefined, { maximumFractionDigits: 0 }) },
            'Số Lượng Traders 24h': { 'API Solscan': solscanData.numTraders24h.toLocaleString(), 'API Dự án': projectData.numTraders24h.toLocaleString() },
            'Top 10 Holding (%) (Chỉ ví có kol_data)': { 
                'API Solscan': solscanData.top10HoldingsPct, 
                'API Dự án': typeof projectData.top10HoldingsPct === 'number' ? projectData.top10HoldingsPct.toFixed(2) + '%' : projectData.top10HoldingsPct 
            },
            'Địa chỉ người tạo': { 'API Solscan': solscanData.creator, 'API Dự án': projectData.creator },
            'Ngày tạo (UTC)': { 'API Solscan': solscanData.createdAt.replace('.000Z', 'Z'), 'API Dự án': projectData.createdAt.replace('.000Z', 'Z') },
        };

        // 4. In kết quả
        console.log("--- Bảng So Sánh Dữ Liệu Token (Dữ liệu trực tiếp - Phiên bản đầy đủ) ---");
        console.table(comparisonTable);

        // 5. In riêng phần so sánh mô tả đầy đủ
        console.log("\n--- So Sánh Mô Tả Đầy Đủ ---");
        console.log("\n[API Solscan]");
        console.log(solscanData.description || "(Không có mô tả)");
        console.log("\n[API Dự án]");
        console.log(projectData.description || "(Không có mô tả)");
        console.log("\n---------------------------------");

    } catch (error) {
        console.error("Đã xảy ra lỗi khi gọi API hoặc xử lý dữ liệu!");
        if (error.response) {
            console.error('Lỗi từ API:', error.response.status, error.response.statusText);
            console.error('URL bị lỗi:', error.config.url);
            console.error('Data phản hồi:', JSON.stringify(error.response.data, null, 2));
        } else if (error.request) {
            console.error('Không nhận được phản hồi từ API. Kiểm tra lại kết nối mạng hoặc URL API.');
        } else {
            console.error('Lỗi khác:', error.message);
        }
    }
}

// Chạy hàm chính
main();