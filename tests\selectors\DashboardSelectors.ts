export const DashboardSelectors = {
    // Phantom wallet import selectors
    importWalletBtn: "//button[text()='I already have a wallet']",
    importPrivateKeyBtn: "//button//div[text()='Import Private Key']",
    walletNameInput: "//input[@placeholder='Name']",
    privateKeyInput: "//textarea[@placeholder='Private key']",
    importBtn: "//button[text()='Import']",
    passwordInput: "//input[@name='password']",
    confirmPasswordInput: "//input[@name='confirmPassword']",
    agreeCheckbox: "//input[@data-testid='onboarding-form-terms-of-service-checkbox']",
    continueBtn: "//button[@data-testid='onboarding-form-submit-button']",
    getStartedBtn: "//button[text()='Get Started']",
    connectWalletBtn: 'button:has-text("Connect wallet")',
    phantomBtn: "//p[text()='Phantom']",
    connectBtn: "//button[text()='Connect']",
    menuBtn: "//button[contains(., 'SOL')]",
    connectPhantomBtn: 'button[type="submit"][data-testid="primary-button"]',
    confirmPhantomBtn: 'button:nth-child(2)',

    // Navigation tabs
    allTokenNavigation: "text='All tokens'",
    newPairsNavigation: "text='New pairs'",
    trendingNavigation: "text='Trending'",

    //header token list tabs
    allTokenTab:"//p[text()='All tokens']",
    newPairsTab:"//p[text()='New pairs']",
    trendingTab: "//p[text()='Trending']",
    dexPaidTab: "//p[text()='DEX paid']",

    // Default DEXes
    defaultDexes: ['pump.fun', 'Pumpswap', 'moonshot', 'raydium', 'orca', 'meteora', 'fluxbeam'],
    // Filter token listing
    DEXXes: '//button[.//p[contains(text(), "DEXes:")]]',
    DEXpaid: "//button[.//p[contains(text(), 'DEX paid:')]]",
    securityFilter: '//button[.//p[contains(text(), "security:")]]',
    liquidityFilter: '//button[.//p[contains(text(), "liquidity:")]]',
    marketCapFilter: '//button[.//p[contains(text(), "market cap:")]]',
    ageFilter: '//button[.//p[contains(text(), "age:")]]',
    tokenFilter: '//button[.//p[contains(text(), "token:")]]',
    clearAllDexesOld: '//button[text()="Clear"]',

    // Phương thức để tạo selector cho DEX container (dựa trên cấu trúc HTML)
    dexContainer: (dexName: string) => `div[role="menu"] div:has(> p.chakra-text:text-is("${dexName}"))`,

    // Phương thức để tạo selector cho label để click
    dexCheckboxLabel: (dexName: string) => `div[role="menu"] div.css-i2h706:has(img[alt="${dexName}"]) label`,

    // Phương thức để tạo selector cho input checkbox (để kiểm tra trạng thái, không để click)
    dexCheckbox: (dexName: string) => `div[role="menu"] div:has(> p.chakra-text:text-is("${dexName}")) label input[type="checkbox"]`,

    // Phương thức để tạo selector XPath cho checkbox DEX
    dexCheckboxXPath: (dexName: string) => `//div[@role="menu"]//p[contains(text(), "${dexName}")]/parent::div//label/input[@type="checkbox"]`,

    // Phương thức để tạo selector XPath cho label của checkbox DEX
    dexLabelXPath: (dexName: string) => `//div[@role="menu"]//p[contains(text(), "${dexName}")]/parent::div//label`,

    // Nút Clear All DEXes
    clearAllDexes: 'div[role="menu"] p:text-is("Clear")',

    // Phương thức cũ (giữ lại để tương thích ngược)
    oldDexCheckbox: (dexName: string) => `//div[contains(text(), "${dexName}")]/preceding-sibling::input[@type="checkbox"]`,
    oldDexCheckboxLabel: (dexName: string) => `//div[contains(text(), "${dexName}")]`,

    // Phương thức để tạo selector cho DEX của token theo index
    tokenDex: (index: number) => `//div[contains(@class, "css-")][${index}]//div[contains(@class, "css-")][last()]`,

    // Token list selectors
    tokenRow: "//a[contains(@class, 'chakra-link') and contains(@class, 'token-virtual-row')]",
    firstTokenRow: "(//a[contains(@class, 'chakra-link') and contains(@class, 'token-virtual-row')])[1]",
    tokenIcon: "//img",
    tokenName: "//div[contains(@class, 'chakra-text')]",
    tokenPrice: "//div[contains(text(), 'SOL')]",
    tokenAge: "//td[1]",
    tokenMcap: "//td[2]",

    // Column headers
    columnHeader: (headerName: string) => `//th[contains(text(), "${headerName}")]`,
    ageHeader: "//th[contains(text(), 'Age')]",

    // Column header names
    columnHeaders: [
      'Token',
      'Age',
      'MCap/Price',
      '%24h',
      'Liquidity',
      '24h Txns',
      '24h Volume',
      '24h Makers',
      'Dev holdings',
      'Security'
    ]
  };

