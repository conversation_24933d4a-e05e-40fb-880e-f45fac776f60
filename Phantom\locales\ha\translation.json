{"commandAdd": "<PERSON>", "commandAccept": "<PERSON><PERSON><PERSON>", "commandApply": "<PERSON> amfani da", "commandApprove": "<PERSON>in<PERSON>", "commandAllow": "Kyale", "commandBack": "Baya", "commandBuy": "<PERSON><PERSON>", "commandCancel": "Soke", "commandClaim": "<PERSON><PERSON><PERSON>", "commandClaimReward": "<PERSON><PERSON><PERSON> kyauta<PERSON>", "commandClear": "Goge", "commandClose": "Rufe", "commandConfirm": "Tabbatar", "commandConnect": "Haɗa", "commandContinue": "Cigaba", "commandConvert": "<PERSON><PERSON>", "commandCopy": "K<PERSON><PERSON>", "commandCopyAddress": "<PERSON><PERSON><PERSON>", "commandCopyTokenAddress": "<PERSON><PERSON><PERSON> <PERSON><PERSON> tokin", "commandCreate": "<PERSON><PERSON>", "commandCreateTicket": "Ƙirƙiri Tikiti", "commandDeny": "<PERSON><PERSON>", "commandDismiss": "<PERSON><PERSON>", "commandDontAllow": "Kada ka kyale", "commandDownload": "<PERSON><PERSON>", "commandEdit": "<PERSON><PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON><PERSON>", "commandEnableNow": "<PERSON><PERSON>", "commandFilter": "<PERSON><PERSON><PERSON>", "commandFollow": "Bi", "commandHelp": "<PERSON><PERSON><PERSON>", "commandLearnMore": "<PERSON><PERSON><PERSON> karin sani", "commandLearnMore2": "<PERSON><PERSON><PERSON> karin sani", "commandMint": "<PERSON><PERSON>", "commandMore": "Fiye", "commandNext": "Na gaba", "commandNotNow": "Ba Yanzu Ba", "commandOpen": "<PERSON><PERSON>", "commandOpenSettings": "<PERSON><PERSON>", "commandPaste": "<PERSON><PERSON>", "commandReceive": "<PERSON><PERSON><PERSON>", "commandReconnect": "Sake haɗawa", "commandRecordVideo": "Naɗi bidiyo", "commandRequest": "<PERSON><PERSON>", "commandRetry": "<PERSON>ke gwa<PERSON>wa", "commandReview": "<PERSON><PERSON><PERSON>", "commandRevoke": "Soke", "commandSave": "<PERSON><PERSON>", "commandScanQRCode": "Laluba QR code", "commandSelect": "Zaɓi", "commandSelectMedia": "Zaɓi Midiya", "commandSell": "Sayarwa", "commandSend": "<PERSON><PERSON>", "commandShare": "Ka yaɗa", "commandShowBalance": "Nuna Balans", "commandSign": "Sa hannu", "commandSignOut": "Sign Out", "commandStake": "<PERSON><PERSON>", "commandMintLST": "Fitar da JitoSOL", "commandSwap": "Musanya", "commandSwapAgain": "<PERSON>ke yin <PERSON>", "commandTakePhoto": "Ɗauki hoto", "commandTryAgain": "<PERSON>ke gwa<PERSON>wa", "commandViewTransaction": "<PERSON><PERSON> ciniki", "commandReportAsNotSpam": "<PERSON> rahoto cewa ba sakon banza ba ne", "commandReportAsSpam": "<PERSON> rahoto cewa sakon banza ne", "commandPin": "Lika", "commandBlock": "<PERSON><PERSON>", "commandUnblock": "Cire toshewa", "commandUnstake": "Cire jingina", "commandUnpin": "Cire liki", "commandHide": "Ɓoye", "commandUnhide": "Cire ɓuya", "commandBurn": "<PERSON><PERSON>", "commandReport": "Ra<PERSON><PERSON>", "commandView": "<PERSON><PERSON>", "commandProceedAnywayUnsafe": "<PERSON><PERSON> gaba kawai (ba amin<PERSON>)", "commandUnfollow": "Cire bi", "commandUnwrap": "Buɗe", "commandConfirmUnsafe": "<PERSON><PERSON><PERSON> (ba aminci)", "commandYesConfirmUnsafe": "<PERSON>, <PERSON><PERSON><PERSON> (ba aminci)", "commandConfirmAnyway": "<PERSON><PERSON><PERSON> ba komai", "commandReportIssue": "Yi rahoton matsala", "commandSearch": "<PERSON><PERSON><PERSON>", "commandShowMore": "<PERSON><PERSON> mai yawa", "commandShowLess": "<PERSON><PERSON> kaɗan", "pastParticipleClaimed": "<PERSON> karba", "pastParticipleCompleted": "<PERSON> kammala", "pastParticipleCopied": "An kwafa", "pastParticipleDone": "An gama", "pastParticipleDisabled": "<PERSON> kashe", "pastParticipleRequested": "An nema", "nounName": "<PERSON><PERSON>", "nounNetwork": "Netwok", "nounNetworkFee": "<PERSON><PERSON><PERSON>", "nounSymbol": "Alama", "nounType": "<PERSON><PERSON><PERSON><PERSON>", "nounDescription": "Kwatance", "nounYes": "E", "nounNo": "A'a", "amount": "<PERSON><PERSON>", "limit": "<PERSON><PERSON><PERSON>", "new": "<PERSON><PERSON>", "gotIt": "<PERSON> gane", "internal": "Na ciki", "reward": "<PERSON><PERSON><PERSON>", "seeAll": "Kalli duka", "seeLess": "Ka<PERSON> kaɗan", "viewAll": "<PERSON>a duka", "homeTab": "Gida", "collectiblesTab": "Abubu<PERSON> masu ƙima", "swapTab": "Musanya", "activityTab": "<PERSON><PERSON>", "exploreTab": "<PERSON>ci<PERSON>", "accountHeaderConnectedInterpolated": "An hada ka da {{origin}}", "accountHeaderConnectedToSite": "An hada ka da wannan shafin intanet", "accountHeaderCopyToClipboard": "<PERSON><PERSON><PERSON> zuwa allon-a<PERSON>ya", "accountHeaderNotConnected": "Ba a hada ka ba da", "accountHeaderNotConnectedInterpolated": "Ba haɗa ka da {{origin}} ba", "accountHeaderNotConnectedToSite": "Ba a haɗa ka ba da wannan shafin intanet ba", "accountWithoutEnoughSolActionButtonCancel": "Soke", "accountWithoutEnoughSolPrimaryText": "<PERSON><PERSON><PERSON>", "accountWithoutEnoughSolSecondaryText": "<PERSON><PERSON><PERSON> da ke da alaka da wannan ciniki ba shi da isasshen SOL. <PERSON><PERSON> yiwu asusunka ne ko na wani. Wannan ciniki zai dawo in an mika.", "accountSwitcher": "<PERSON><PERSON> can<PERSON> asusu", "addAccountHardwareWalletPrimaryText": "Haɗa walat na hadwaya", "addAccountHardwareWalletSecondaryText": "<PERSON> amfani da walat na hadwaya na Ledger dinka", "addAccountHardwareWalletSecondaryTextMobile": "<PERSON> amfani da {{supportedHardwareWallets}} na Walat dinka", "addAccountSeedVaultWalletPrimaryText": "Haɗa Seed <PERSON>ault", "addAccountSeedVaultWalletSecondaryText": "<PERSON> amfani da walat daga See<PERSON>", "addAccountImportSeedPhrasePrimaryText": "<PERSON><PERSON> da jimlar sirri na dawo da bayanai", "addAccountImportSeedPhraseSecondaryText": "<PERSON><PERSON> da asusu daga wani walat", "addAccountImportWalletPrimaryText": "<PERSON><PERSON> da lamban sirri", "addAccountImportWalletSecondaryText": "Shigo da asusun chain-guda", "addAccountImportWalletSolanaSecondaryText": "<PERSON><PERSON> da mabudin sirri na Solana", "addAccountLimitReachedText": "Ka kai iyakar asusun {{accountsCount}} a Phantom. Da fatan za ka cire asusun da ba a amfani da su ba kafin ƙara wasu.", "addAccountNoSeedAvailableText": "Ba ka da jimlar mabudi tare da kai. Da fatan zaka shigo da mabudin da akwai don samar da wani asusu.", "addAccountNewWalletPrimaryText": "<PERSON><PERSON>", "addAccountNewWalletSecondaryText": "Ƙirƙiri sabon ad<PERSON>hin walat", "addAccountNewMultiChainWalletSecondaryText": "Kara sabon asusun chain-mai yawa", "addAccountNewSingleChainWalletSecondaryText": "Ƙara sabon asusu", "addAccountPrimaryText": "Ƙara / haɗa walat", "addAccountSecretPhraseLabel": "<PERSON><PERSON>", "addAccountSeedLabel": "Mabudi", "addAccountSeedIDLabel": "<PERSON><PERSON>", "addAccountSecretPhraseDefaultLabel": "<PERSON><PERSON> sirri {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON><PERSON> {{number}}", "addAccountZeroAccountsForSeed": "Asusu 0", "addAccountShowAccountForSeed": "Nuno asusu 1", "addAccountShowAccountsForSeed": "<PERSON><PERSON> {{numOfAccounts}}", "addAccountHideAccountForSeed": "Boye asusu 1", "addAccountHideAccountsForSeed": "<PERSON><PERSON> asusu {{numOfAccounts}}", "addAccountSelectSeedDescription": "Za a samar da sabon asusunka daga wannan <PERSON><PERSON><PERSON> sirrin", "addAccountNumAccountsForSeed": "Asusu {{numOfAccounts}}", "addAccountOneAccountsForSeed": "Asusu 1", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON>", "addAccountReadOnly": "<PERSON><PERSON><PERSON>", "addAccountReadOnlySecondaryText": "<PERSON>i sahun kowane ad<PERSON>hin walat na jama'a", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Ad<PERSON>hin EVM", "addAccountBitcoinAddress": "Adireshin Bitcoin", "addAccountCreateSeedTitle": "<PERSON><PERSON> sabon asusu", "addAccountCreateSeedExplainer": "Walat dinka ba shi da jimlar sirri har yanzu! Don ƙirƙirar sabon walat, zamu kaga maka sabuwar jimlar sirri. <PERSON><PERSON><PERSON> wannan kuma adana a gun ka.", "addAccountSecretPhraseHeader": "<PERSON><PERSON> sirri naka", "addAccountNoSecretPhrases": "<PERSON>", "addAccountImportAccountActionButtonImport": "<PERSON><PERSON> <PERSON>", "addAccountImportAccountDuplicatePrivateKey": "<PERSON><PERSON><PERSON> wannan asusun da ma a walat naka", "addAccountImportAccountIncorrectFormat": "<PERSON><PERSON> da ba daidai ba", "addAccountImportAccountInvalidPrivateKey": "<PERSON>an sirri mara inganci", "addAccountImportAccountName": "<PERSON><PERSON>", "addAccountImportAccountPrimaryText": "<PERSON><PERSON>", "addAccountImportAccountPrivateKey": "<PERSON><PERSON>", "addAccountImportAccountPublicKey": "<PERSON><PERSON><PERSON> ko <PERSON> intanet", "addAccountImportAccountPrivateKeyRequired": "<PERSON> b<PERSON><PERSON>", "addAccountImportAccountNameRequired": "<PERSON> bukatar suna", "addAccountImportAccountPublicKeyRequired": "<PERSON> bukatar ad<PERSON>hin jama'a", "addAccountImportAccountDuplicateAddress": "<PERSON><PERSON><PERSON> wannan ad<PERSON>hin da ma a walat naka", "addAddressAddressAlreadyAdded": "An riga an sa adireshin", "addAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON> na nan da ma", "addAddressAddressInvalid": "<PERSON><PERSON><PERSON> mara aiki", "addAddressAddressIsRequired": "<PERSON> buka<PERSON> ad<PERSON>hi", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "<PERSON> bukatar lakabi", "addAddressLabelPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressPrimaryText": "Sa ad<PERSON>hi", "addAddressToast": "An kara ad<PERSON>hi", "createAssociatedTokenAccountCostLabelInterpolated": "Wannan za a biya SOL {{solAmountFormatted}}", "createAssociatedTokenAccountErrorAccountExists": "<PERSON>na da wannan asusun tokin da ma", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON> <PERSON><PERSON><PERSON>", "createAssociatedTokenAccountErrorInvalidMint": "<PERSON><PERSON><PERSON> kirk<PERSON> kuɗi mara inganci", "createAssociatedTokenAccountErrorInvalidName": "<PERSON>a mara inganci", "createAssociatedTokenAccountErrorInvalidSymbol": "Alama mara inganci", "createAssociatedTokenAccountErrorUnableToCreateMessage": "<PERSON><PERSON> iya kirkirar asusun tokin naka ba. Da fatan zaka sake gwadawa anjima.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "<PERSON><PERSON><PERSON> asusun tokin bai yiwu ba", "createAssociatedTokenAccountErrorUnableToSendMessage": "<PERSON><PERSON> iya aika cinikinka ba.", "createAssociatedTokenAccountErrorUnableToSendTitle": "<PERSON><PERSON> ciniki bai yiwu ba", "createAssociatedTokenAccountInputPlaceholderMint": "<PERSON><PERSON><PERSON> kirk<PERSON> kuɗi", "createAssociatedTokenAccountInputPlaceholderName": "<PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderSymbol": "Alama", "createAssociatedTokenAccountLoadingMessage": "<PERSON>na ƙirƙiran asusun tokin naka.", "createAssociatedTokenAccountLoadingTitle": "<PERSON> ƙirƙiran asusun tokin", "createAssociatedTokenAccountPageHeader": "Ƙirƙiri asusun tokin", "createAssociatedTokenAccountSuccessMessage": "An yi nasarar ƙirƙirar asusun tokin naka!", "createAssociatedTokenAccountSuccessTitle": "An ƙirƙiri asusun tokin", "createAssociatedTokenAccountViewTransaction": "<PERSON><PERSON> ciniki", "assetDetailRecentActivity": "<PERSON>kin baya-bayan nan", "assetDetailStakeSOL": "<PERSON><PERSON><PERSON>", "assetDetailUnknownToken": "Tokin da ba a sani ba", "assetDetailUnwrapAll": "<PERSON><PERSON><PERSON>", "assetDetailUnwrappingSOL": "Ana cire jinginar SOL", "assetDetailUnwrappingSOLFailed": "Cire Jinginan SOL bai yiwu ba", "assetDetailViewOnExplorer": "<PERSON><PERSON> a {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorer", "assetDetailSaveToPhotos": "<PERSON>na cikin hotuna", "assetDetailSaveToPhotosToast": "<PERSON>na cikin hotuna", "assetDetailPinCollection": "<PERSON><PERSON><PERSON> k<PERSON>hin kayayyaki", "assetDetailUnpinCollection": "<PERSON>ire kunshin kayayyaki", "assetDetailHideCollection": "<PERSON><PERSON> kunshin kayayyaki", "assetDetailUnhideCollection": "<PERSON><PERSON><PERSON> kayayyaki na musamman", "assetDetailTokenNameLabel": "<PERSON><PERSON>", "assetDetailNetworkLabel": "Netwok", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "<PERSON><PERSON>", "collectibleDetailSetAsAvatar": "<PERSON>ta a matsayin <PERSON> shafi", "collectibleDetailSetAsAvatarSingleWorkAlt": "<PERSON><PERSON>", "collectibleDetailSetAsAvatarSuccess": "<PERSON><PERSON>", "collectibleDetailShare": "<PERSON><PERSON> kaya", "assetDetailTokenAddressCopied": "<PERSON> kwafi ad<PERSON>hi", "assetDetailStakingLabel": "<PERSON><PERSON>", "assetDetailAboutLabel": "<PERSON><PERSON><PERSON> da {{fungibleName}}", "assetDetailPriceDetail": "<PERSON><PERSON><PERSON>", "assetDetailHighlights": "<PERSON>", "assetDetailAllTimeReturn": "<PERSON><PERSON> Riba ko Faduwa", "assetDetailAverageCost": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailPriceHistoryUnavailable": "<PERSON> ta<PERSON><PERSON> farashi ga wannan tokin", "assetDetailPriceHistoryInsufficientData": "<PERSON> ta<PERSON><PERSON> farashi na wannan tsakanin", "assetDetailPriceDataUnavailable": "<PERSON> bayanin farashi", "assetDetailPriceHistoryError": "An sami matsalar kawo tarihin farashi", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "Kwana1", "assetDetailTimeFrame24h": "Farashin 24h", "assetDetailTimeFrame1W": "Sati1", "assetDetailTimeFrame1M": "Wata1", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "DUKA", "sendAssetAmountLabelInterpolated": "Akwai {{amount}}{{tokenSymbol}}", "fiatRampQuotes": "<PERSON><PERSON>", "fiatRampNewQuote": "<PERSON><PERSON> far<PERSON>", "assetListSelectToken": "Zaɓi Tokin", "assetListSearch": "Ka bincika...", "assetListUnknownToken": "Tokin da ba a sani ba", "buyFlowHealthWarning": "<PERSON>u daga cikin masu ba da kuɗin mu suna fuskantar cunkoson hanyar sadarwa. Ajiyar zai iya samun jinkirin sa'o'i.", "assetVisibilityUnknownToken": "Tokin da ba a sani ba", "buyAssetInterpolated": "Saya {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "<PERSON><PERSON> girman say<PERSON>ya shine {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "<PERSON><PERSON> ka<PERSON> sayayya shine {{amount}}", "buyNoAssetsAvailable": "<PERSON> dukiyoyin Ethereum ko Polygon a kasa", "buyThirdPartyScreenPaymentMethodSelector": "Ka biya da", "buyThirdPartyScreenPaymentMethod": "Zaɓi hanyar biya", "buyThirdPartyScreenChoseQuote": "<PERSON><PERSON> da adadin mai kyau na farashi", "buyThirdPartyScreenProviders": "<PERSON><PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodTitle": "<PERSON><PERSON><PERSON><PERSON>sar<PERSON>", "buyThirdPartyScreenPaymentMethodEmptyState": "Ba tsare-tsaren biya a yankinku", "buyThirdPartyScreenPaymentMethodFooter": "<PERSON><PERSON><PERSON> hulda ne ke aiwatar da biya. Babu wasu tsarin biya a yankinku.", "buyThirdPartyScreenProvidersEmptyState": "<PERSON> masu yan canji a yankinku", "buyThirdPartyScreenLoadingQuote": "Ana koda farashi...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON>", "gasEstimationErrorWarning": "An sami matsalar ƙiyasta cajin wannan ciniki. <PERSON><PERSON><PERSON> yiwuwar ya kasa.", "gasEstimationCouldNotFetch": "Ba a iya dauko kiyasin gas ba", "networkFeeCouldNotFetch": "Ba a iya dauko cajin netwok ba", "nativeTokenBalanceErrorWarning": "An sami matsalar samun balans na tokin dinka na wannan ciniki. <PERSON><PERSON><PERSON> yiwu<PERSON> ya kasa.", "blocklistOriginCommunityDatabaseInterpolated": "An alamta wannan shafin intanet <1>a matatt<PERSON>r bayanan al'umma </1> cikin sanannun shafukan intanet masu satar bayanai da yin zamba. Idan kuna ganin an yiwa shafin alama cikin kuskure, <3> Da fatan za shigar da korafi</3>.", "blocklistOriginDomainIsBlocked": "An toshe {{domainName}}!", "blocklistOriginIgnoreWarning": "<PERSON>ta da wannan garga<PERSON>, kai ni {{domainName}} kawai.", "blocklistOriginSiteIsMalicious": "Phantom na ganin wannan shafin intanet na tattare da cuta kuma babu amincin amfani da shi.", "blocklistOriginThisDomain": "wannan shafin intanet", "blocklistProceedAnyway": "<PERSON><PERSON> da garga<PERSON>, ci gaba kawai", "maliciousTransactionWarning": "Phantom na ganin akwai cuta a wannan ciniki kuma babu amincin shiga. Mun rufe yiwuwar sa ma shi hannu domin kare ka da kudaden ka.", "maliciousTransactionWarningIgnoreWarning": "Manta da gargadi, cigaba kawai", "maliciousTransactionWarningTitle": "An alamta wannan ciniki da hatsari!", "maliciousRequestBlockedTitle": "An toshe nema", "maliciousRequestWarning": "An yi wa wannan shafin intanet alamar haɗari. <PERSON>ai yiwu ana neman sace kuɗinka ne ko a yaudare ka ne ka tabbatar da nema ta boge.", "maliciousSignatureRequestBlocked": "<PERSON>, <PERSON> ya toshe wannan buƙatar.", "maliciousRequestBlocked": "<PERSON>, <PERSON> ya toshe wannan buƙatar.", "maliciousRequestFrictionDescription": "Ba aminci a ci gaba, don haka Phantom ya toshe wannan buƙatar. Ya kamata ka rufe wannan tatta<PERSON>war.", "maliciousRequestAcknowledge": "Na fahimta zan iya asarar duk kuɗina ta amfani da wannan shafin.", "maliciousRequestAreYouSure": "Ka tabbatar?", "siwErrorPopupTitle": "<PERSON><PERSON> sa-hannu mara kyau", "siwParseErrorDescription": "Ba za iya nuna neman sa-hannun manhaja ba saboda tsari mara kyau.", "siwVerificationErrorDescription": "An sami kuskure 1 ko yawan (kurakurai) a sakon neman sa-hannu. <PERSON><PERSON><PERSON> tsar<PERSON><PERSON>, ka tabbatar kana amfani da manhajar da ta dace sannan ka sake gwadawa.", "siwErrorPagination": "{{n}} na {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Gargadi: <PERSON><PERSON><PERSON> manhaja bai yi daidai da adireshin da ka bayar don shiga ba.", "siwErrorMessage_DOMAIN_MISMATCH": "Gargadi: shafin intanet na manhaja bai yi daidai da shafin intanet ta ka bayar don tantancewa ba.", "siwErrorMessage_URI_MISMATCH": "Gargaɗi: URI na sunan sabis bai dace da shafin intanet ba.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Gargaɗi: <PERSON><PERSON> sarkar-krifto bai yi daidai da <PERSON><PERSON> sarkar-krifto da ka bayar don tabbatarwa ba.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Gargadi: kwanan watan fitar da sako yayi nisa da wucewa.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Gargadi: kwanan watan fitar da sako yayi gaba da yawa.", "siwErrorMessage_EXPIRED": "Gargadi: kwanan watan fitar da sako ya wuce.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Gargadi: sako ya wuce kafin a fitar da shi.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Gargadi: sako zai wuce kafin ya zama mai amfani.", "siwErrorShowErrorDetails": "<PERSON><PERSON> bay<PERSON>n kuskure", "siwErrorHideErrorDetails": "<PERSON><PERSON> bayanan kuskure", "siwErrorIgnoreWarning": "<PERSON><PERSON> da garga<PERSON>, ci gaba kawai", "siwsTitle": "<PERSON><PERSON>", "siwsPermissions": "<PERSON><PERSON><PERSON>", "siwsAgreement": "<PERSON><PERSON>", "siwsAdvancedDetails": "Bayanan Da Aka Gabatar", "siwsAlternateStatement": "{{domain}} na neman ka shiga da asusunka na Solana {{address}}", "siwsFieldLable_domain": "<PERSON><PERSON><PERSON> in<PERSON>", "siwsFieldLable_address": "<PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Siga", "siwsFieldLable_chainId": "S<PERSON>dar <PERSON>", "siwsFieldLable_nonce": "Ɗaya Tilo", "siwsFieldLable_issuedAt": "An Fitar A", "siwsFieldLable_expirationTime": "<PERSON><PERSON>", "siwsFieldLable_requestId": "<PERSON><PERSON>-kai na <PERSON>", "siwsFieldLable_resources": "Albarkatu", "siwsVerificationErrorDescription": "<PERSON>n neman shiga ya lalace, ko <PERSON> ya kirkire ta ya yi kuskure lokacin aika neman.", "siwsErrorNumIssues": "{{n}} mat<PERSON><PERSON>", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Wannan shaidar chain ba ta yi daidai da netwok da kake a kai ba.", "siwsErrorMessage_DOMAIN_MISMATCH": "<PERSON> wannan shafin intanet ne ka ke neman shiga ba.", "siwsErrorMessage_URI_MISMATCH": "<PERSON> wannan URI ne ka ke neman shiga ba.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "<PERSON><PERSON><PERSON> watan fitar da sako ya yi baya da yawa.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "<PERSON><PERSON><PERSON> watan fitar da sako ya yi gaba da yawa.", "siwsErrorMessage_EXPIRED": "<PERSON>ko ya tashi aiki.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "<PERSON>ko ya tashi aiki kafin a fitar.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "<PERSON>ko zai tashi aiki kafin zama mai amfani.", "changeLockTimerPrimaryText": "Agogo mai kulle kansa", "changeLockTimerSecondaryText": "<PERSON><PERSON><PERSON> Wan<PERSON> lokaci za mu jira kafin mu kulle walat naka bayan ka bar aiki da shi?", "changeLockTimerToast": "An sabunta agogo mai rufe kansa", "changePasswordConfirmNewPassword": "<PERSON><PERSON><PERSON> da sabon kalman sirri", "changePasswordCurrentPassword": "<PERSON><PERSON><PERSON> almar shiga", "changePasswordErrorIncorrectCurrentPassword": "<PERSON><PERSON><PERSON> kalmar shiga ba daidai ba", "changePasswordErrorGeneric": "An sami matsala, sake gwadawa anjima", "changePasswordNewPassword": "<PERSON><PERSON><PERSON> kalmar shiga", "changePasswordPrimaryText": "<PERSON><PERSON> kalmar shiga", "changePasswordToast": "An sabunta kalmar sirri", "collectionsSpamCollections": "<PERSON><PERSON>", "collectionsHiddenCollections": "<PERSON><PERSON><PERSON> kunshin kayayyaki", "collectiblesReportAsSpam": "<PERSON> korafin cewa sakon banza ne", "collectiblesReportAsSpamAndHide": "<PERSON> korafi cewa sakon banza ne sannan ka boye", "collectiblesReportAsNotSpam": "<PERSON> korafin cewa Ba Sakon banza ba ne", "collectiblesReportAsNotSpamAndUnhide": "Cire ɓuya kuma ba da rahoto ba sakon banza ba ne", "collectiblesReportNotSpam": "Ba sakon banza ba", "collectionsManageCollectibles": "<PERSON><PERSON> da jerin kayayyaki", "collectibleDetailDescription": "Kwatance", "collectibleDetailProperties": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailOrdinalInfo": "Bayani Daƙi-daƙi", "collectibleDetailRareSatsInfo": "Bayanin Sats na-daban", "collectibleDetailSatsInUtxo": "Sats cikin UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Lambar Sat", "collectibleDetailSatName": "<PERSON><PERSON>", "collectibleDetailInscriptionId": "<PERSON><PERSON>", "collectibleDetailInscriptionNumber": "<PERSON><PERSON>", "collectibleDetailStandard": "Daidai", "collectibleDetailCreated": "<PERSON> kirkira", "collectibleDetailViewOnExplorer": "<PERSON><PERSON> a {{explorer}}", "collectibleDetailList": "<PERSON><PERSON>", "collectibleDetailSellNow": "Sayar a {{amount}}{{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Rage fili ga karin <PERSON>in", "collectibleDetailUtxoSplitterCtaSubtitle": "<PERSON>na da {{value}} na BT<PERSON> da zaka bude", "collectibleDetailUtxoSplitterModalCtaTitle": "Sats na-daban", "collectibleDetailUtxoSplitterModalCtaSubtitle": "<PERSON>in ka<PERSON>, mun hana aika BTC da UTXOs masu Sats na-daban. <PERSON> amfani da masarin Magic Eden's UTXOs do rage yawan BTC {{value}} na Sats Na-daban.", "collectibleDetailUtxoSplitterModalCtaButton": "<PERSON> amfani da masarin UTXO", "collectibleDetailEasilyAccept": "<PERSON><PERSON><PERSON> da farashin tayi mafi tsada", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "An ɓoye wannan kayan saboda Phantom na gani na ganin sakon banza ne.", "collectibleDetailSpamOverlayReveal": "<PERSON><PERSON> kaya", "collectibleBurnTermsOfService": "Na gane ba za a iya warware wannan ba", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON>", "collectibleBurnTitleWithCount_other": "<PERSON><PERSON>", "collectibleBurnDescriptionWithCount_one": "Wannan aiki zai lalata da kuma cire wannan tokin daga walat ɗinka.", "collectibleBurnDescriptionWithCount_other": "Wannan aiki zai lalata da kuma cire wadannan tokuna daga walat ɗinka.", "collectibleBurnTokenWithCount_one": "<PERSON><PERSON>", "collectibleBurnTokenWithCount_other": "<PERSON><PERSON><PERSON>", "collectibleBurnCta": "<PERSON><PERSON>", "collectibleBurnRebate": "<PERSON><PERSON>", "collectibleBurnRebateTooltip": "Za a saka SOL kadan kai tsaye cikin walat ɗinka saboda kone wannan tokin.", "collectibleBurnNetworkFee": "Kuɗin Netwok", "collectibleBurnNetworkFeeTooltip": "<PERSON><PERSON> da netwok na Solana ke buƙata don aiwatar da ciniki", "unwrapButtonSwapTo": "<PERSON><PERSON> zuwa {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Cire daga {{withdrawalSource}} domin {{chainSymbol}}", "unwrapModalEstimatedTime": "<PERSON><PERSON><PERSON>", "unwrapModalNetwork": "Netwok", "unwrapModalNetworkFee": "<PERSON><PERSON><PERSON>", "unwrapModalTitle": "Taƙaitawa", "unsupportedChain": "Chain da ba a aiki da shi", "unsupportedChainDescription": "<PERSON><PERSON> ba ma aiki {{action}} domin netwok na {{chainName}}.", "networkFeesTooltipLabel": "{{chainName}} <PERSON><PERSON><PERSON>", "networkFeesTooltipDescription": "<PERSON><PERSON><PERSON> {{chainName}} sun bambanta saboda dalilai mabanbanta. Za ka iya canza su don cinikinka ya fi sauri (ya fi tsada) ko mafi jinkiri (ya fi arha).", "burnStatusErrorTitleWithCount_one": "<PERSON><PERSON> tokin bai yiwu ba", "burnStatusErrorTitleWithCount_other": "<PERSON><PERSON> tokuna bai yiwu ba", "burnStatusSuccessTitleWithCount_one": "An kone Tokin!", "burnStatusSuccessTitleWithCount_other": "An kone Tokuna!", "burnStatusLoadingTitleWithCount_one": "Ana kone tokin...", "burnStatusLoadingTitleWithCount_other": "Ana kone Tokuna...", "burnStatusErrorMessageWithCount_one": "<PERSON><PERSON> wannan tokin bai yiwu ba. Da fatan zaka sake gwadawa anjima.", "burnStatusErrorMessageWithCount_other": "<PERSON><PERSON> wadannan tokuna bai yiwu ba. Da fatan zaka sake gwadawa anjima.", "burnStatusSuccessMessageWithCount_one": "An lalata wannan tokin har abada kuma an saka SOL {{rebateAmount}} a Walat dinka.", "burnStatusSuccessMessageWithCount_other": "An lalata wadannan tokuna har abada kuma an saka SOL {{rebateAmount}} a Walat dinka.", "burnStatusLoadingMessageWithCount_one": "An lalata wannan tokin har abada kuma za a saka SOL {{rebateAmount}} a walat dinka.", "burnStatusLoadingMessageWithCount_other": "An lalata wadannan tokuna har abada kuma za a saka SOL {{rebateAmount}} a walat dinka.", "burnStatusViewTransactionText": "<PERSON><PERSON> ciniki", "collectibleDisplayLoading": "Lodawa...", "collectiblesNoCollectibles": "Ba ababen da za iya karba", "collectiblesPrimaryText": "<PERSON><PERSON>ben da za ka iya karba", "collectiblesReceiveCollectible": "Karbi ababen da za iya karba", "collectiblesUnknownCollection": "<PERSON><PERSON><PERSON><PERSON> da ba a sansu ba", "collectiblesUnknownCollectible": "<PERSON><PERSON><PERSON><PERSON> da ba a sansu ba", "collectiblesUniqueHolders": "<PERSON>su riko na musamman", "collectiblesSupply": "<PERSON><PERSON>", "collectiblesUnknownTokens": "Tokin da ba a san su ba", "collectiblesNrOfListed": "An jera {{ nrOfListed }}", "collectiblesListed": "An jera", "collectiblesMintCollectible": "<PERSON><PERSON>", "collectiblesYouMint": "Ka kirkiri", "collectiblesMintCost": "<PERSON><PERSON><PERSON>", "collectiblesMintFail": "Kirkira ta gaza", "collectiblesMintFailMessage": "An sami matsala wajen kirk<PERSON>r kayanka. Da fatan za ka sake gwadawa.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "An kirkirar...", "collectiblesMintingMessage": "An kirkiri kayanka", "collectiblesMintShareSubject": "<PERSON><PERSON> wannan", "collectiblesMintShareMessage": "Na buga wannan a @phantom!", "collectiblesMintSuccess": "<PERSON><PERSON> ta yi nasara", "collectiblesMintSuccessMessage": "An kirkiri kayanka yanzu", "collectiblesMintSuccessQuestMessage": "Ka cika sharuddan aikin la'ada na <PERSON>. Taba don karbar kyautarka don samun naka kyautar kaya.", "collectiblesMintRequired": "<PERSON> b<PERSON>", "collectiblesMintMaxLengthErrorMessage": "An zarce iyakar tsayi", "collectiblesMintSafelyDismiss": "Za ka iya sallamar wannan window cikin aminci.", "collectiblesTrimmed": "Mun kai iyakar kayayyakin da zamu mu iya nunawa yanzu.", "collectiblesNonTransferable": "Ba abin da za a iya transfa ba ne", "collectiblesNonTransferableYes": "E", "collectiblesSellOfferDetails": "<PERSON><PERSON><PERSON>", "collectiblesSellYouSell": "<PERSON><PERSON>", "collectiblesSellGotIt": "<PERSON> gane", "collectiblesSellYouReceive": "<PERSON>", "collectiblesSellOffer": "<PERSON>yi", "collectiblesSoldCollectible": "<PERSON><PERSON><PERSON> masu kima aka sayar", "collectiblesSellMarketplace": "<PERSON><PERSON><PERSON>", "collectiblesSellCollectionFloor": "<PERSON><PERSON> ƙasan farashin kaya", "collectiblesSellDifferenceFromFloor": "Bambanci daga mafi kankantan farashi", "collectiblesSellLastSalePrice": "Sayarwa ta karshe", "collectiblesSellEstimatedFees": "<PERSON><PERSON><PERSON> aka ƙiyasta", "collectiblesSellEstimatedProfitAndLoss": "Riba/Fad<PERSON>wa da aka ƙiyasta", "collectiblesSellViewOnMarketplace": "Duba a {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> mafi arha na kayayyaki a kasuwanni mabanbanta.", "collectiblesSellProfitLossTooltip": "An gina <PERSON>/<PERSON><PERSON><PERSON><PERSON> ne akan kimar farashin sayarwa na karshe da yawan farashin tayi in an cire caji.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Kuɗin aiki ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "<PERSON><PERSON><PERSON> ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "<PERSON><PERSON><PERSON>", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}} Netwok", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "<PERSON><PERSON><PERSON> sayarwa ya kunshi {{phantomFeePercentage}} cajin Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "<PERSON><PERSON><PERSON> sayarwa ya kunshi Kuɗin aiki, cajin <PERSON>, cajin <PERSON> da {{phantomFeePercentage}} cajin Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "<PERSON><PERSON><PERSON>wa ya kunshi Kuɗin <PERSON><PERSON>, <PERSON><PERSON><PERSON>, da wani <PERSON><PERSON><PERSON>", "collectiblesSellTransactionFeeTooltipTitle": "<PERSON><PERSON><PERSON>", "collectiblesSellStatusLoadingTitle": "<PERSON><PERSON>...", "collectiblesSellStatusLoadingIsSellingFor": "na sayarwa a kan", "collectiblesSellStatusSuccessTitle": "An sayar da {{collectibleName}}!", "collectiblesSellStatusSuccessWasSold": "an yi nasarar sayarwa a kan", "collectiblesSellStatusErrorTitle": "An sami matsala", "collectiblesSellStatusErrorSubtitle": "An sami matsala a kokarin sayarwa", "collectiblesSellStatusViewTransaction": "<PERSON><PERSON> ciniki", "collectiblesSellInsufficientFundsTitle": "<PERSON> is<PERSON><PERSON> kuɗaɗe", "collectiblesSellInsufficientFundsSubtitle": "Ba mu iya karbar tayin sayan wadannan kayayyakin ba saboda babu isasshen kuɗaɗen biya cajin netwok.", "collectiblesSellRecentlyTransferedNFTTitle": "An yi transifa ba da dadewa ba", "collectiblesSellRecentlyTransferedNFTSubtitle": "Za ka jira hawa 1 kafin karbar tayin sayarwa bayan yin transifa.", "collectiblesApproveCollection": "An tabbatar da {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "Ba a samun tayi", "collectiblesSellNotAvailableAnymoreSubtitle": "<PERSON> wannan tayin kuma. Soke wannan farashin sannan ka sake gwadawa", "collectiblesSellFlaggedTokenTitle": "An yi wa kaya alamar matsala", "collectiblesSellFlaggedTokenSubtitle": "<PERSON><PERSON><PERSON> kayan ba zai yiwu ba, sa<PERSON><PERSON> da da<PERSON>lai da yawa, ka<PERSON> an yi korafin kayan sata ne ko an jingina ta babu kulle", "collectiblesListOnMagicEden": "<PERSON><PERSON> a <PERSON>", "collectiblesListPrice": "<PERSON><PERSON><PERSON>", "collectiblesUseFloor": "<PERSON> amfani da mafi ƙarancin farashi", "collectiblesFloorPrice": "<PERSON><PERSON> ma<PERSON> ka<PERSON>", "collectiblesLastSalePrice": "<PERSON><PERSON><PERSON>", "collectiblesTotalReturn": "<PERSON><PERSON>", "collectiblesOriginalPurchasePrice": "<PERSON><PERSON><PERSON> sayowa na asali", "collectiblesMagicEdenFee": "<PERSON><PERSON> Magic Eden", "collectiblesArtistRoyalties": "<PERSON><PERSON> mai basira", "collectiblesListNowButton": "<PERSON><PERSON>", "collectiblesListAnywayButton": "<PERSON><PERSON>an, jera", "collectiblesCreateListingTermsOfService": "Da taba <1>\"<PERSON><PERSON>\"</1> ka yarda da <PERSON><PERSON><3> do<PERSON><PERSON> aikin </3>", "collectiblesViewListing": "<PERSON><PERSON> j<PERSON>", "collectiblesListingViewTransaction": "<PERSON><PERSON> ciniki", "collectiblesRemoveListing": "<PERSON><PERSON>", "collectiblesEditListing": "<PERSON><PERSON><PERSON>", "collectiblesEditListPrice": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesListPriceTooltip": "<PERSON><PERSON><PERSON> Jerawa ne farashin sayarwa na kaya. <PERSON><PERSON> sayarwa ne suka fi saka farashin Jerawa daidai ko sama da farashi mafi ƙaranci.", "collectiblesFloorPriceTooltip": "<PERSON><PERSON><PERSON>sa shine farashi mafi ƙaranci ga kowane kaya a wannan kunshi.", "collectiblesOriginalPurchasePriceTooltip": "Ka saya wannan kaya tun farko a wannan farashin.", "collectiblesPurchasedForSol": "An sayo a kan SOL {{lastPurchasePrice}}", "collectiblesUnableToLoadListings": "Ba iya loda Jerawa ba", "collectiblesUnableToLoadListingsFrom": "<PERSON><PERSON>a bai yiwu ba daga {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "<PERSON><PERSON><PERSON><PERSON> da kadarorinka na cikin aminci amma loda su daga {{marketplace}} bai yiwu ba yanzu. <PERSON>ke gwadawa anjima.", "collectiblesBelowFloorPrice": "<PERSON><PERSON> da mafi ƙarancin farashi", "collectiblesBelowFloorPriceMessage": "Ka tabbata kana so ka jera NFT dinka a kasa da da mafi ƙarancin farashi?", "collectiblesMinimumListingPrice": "SOL 0.01 ne mafi karancin farashi", "collectiblesMagicEdenFeeTooltip": "Magic Eden kan cire kudin a ciniki da aka kulla.", "collectiblesArtistRoyaltiesTooltip": "Wanda ya kirkiri wannan kunshi zai karbi % la'ada a kan duk sayarwa da aka yi.", "collectibleScreenCollectionLabel": "<PERSON><PERSON><PERSON> kaya", "collectibleScreenPhotosPermissionTitle": "<PERSON><PERSON><PERSON>", "collectibleScreenPhotosPermissionMessage": "<PERSON><PERSON> bukatar i<PERSON>a don amfani da hotunan ka. Da fatan za ka je setuna sannan ka sabunta tsare-tsar<PERSON> izinink<PERSON>.", "collectibleScreenPhotosPermissionOpenSettings": "<PERSON><PERSON>", "listStatusErrorTitle": "<PERSON><PERSON><PERSON> bai yiwu ba", "editListStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON> bai yiwu ba", "removeListStatusErrorTitle": "<PERSON><PERSON> <PERSON><PERSON>a bai yiwu ba", "listStatusSuccessTitle": "An kirkiri Je<PERSON>!", "editListingStatusSuccessTitle": "<PERSON><PERSON><PERSON> da aka sabu<PERSON>!", "removeListStatusSuccessTitle": "An cire Jerawar daga Magic Eden", "listStatusLoadingTitle": "<PERSON> kirk<PERSON>...", "editListingStatusLoadingTitle": "Ana sabunta <PERSON>...", "removeListStatusLoadingTitle": "Ana cire Jerawa...", "listStatusErrorMessage": "Ba za iya jera {{name}} a Magic Eden ba", "removeListStatusErrorMessage": "Cire {{name}} a Jerawar Magic Eden bai yiwu ba", "listStatusSuccessMessage": "An jera {{name}} a Magic Eden a halin yanzu a kan SOL {{listCollectiblePrice}}", "editListingStatusSuccessMessage": "An sabunta {{name}} a Magic Eden a halin yanzu a kan SOL {{editListCollectiblePrice}}", "removeListStatusSuccessMessage": "An yi nasarar cire {{name}} daga Magic Eden", "listStatusLoadingMessage": "<PERSON> jera {{name}} a Magic Eden a kan SOL {{listCollectiblePrice}}.", "editListingStatusLoadingMessage": "<PERSON> sabunta {{name}} a Magic Eden a kan SOL {{editListCollectiblePrice}}.", "removeListStatusLoadingMessage": "Ana cire {{name}} daga Magic Eden. Wannan zai dauki lokaci.", "listStatusLoadingSafelyDismiss": "<PERSON><PERSON> iya sallamar wannan window cikin aminci.", "listStatusViewOnMagicEden": "Duba a Magic Eden", "listStatusViewOnMarketplace": "Duba a {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON><PERSON>", "listStatusViewTransaction": "<PERSON><PERSON> ciniki", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Ka haɗa walat naka na hadwaya kuma ka tabbatar yana bude. Da zarar mun ango shi zaka iya zabar ad<PERSON>hin da zaka yi amfani da shi.", "connectHardwareFailedPrimaryText": "Haɗawa bai yiwu ba", "connectHardwareFailedSecondaryText": "<PERSON>na fatan za ka haɗa walat naka na hadwaya kuma ka tabbatar yana bude. Da zarar mun ango shi zaka iya zabar ad<PERSON>hin da zaka yi amfani da shi.", "connectHardwareFinishPrimaryText": "An kara asusu!", "connectHardwareFinishSecondaryText": "<PERSON>aka iya amfani da walat naka na Led<PERSON> a cikin Phantom. <PERSON>na fatan zaka koma mahadi.", "connectHardwareNeedsPermissionPrimaryText": "Haɗa sabon walat", "connectHardwareNeedsPermissionSecondaryText": "<PERSON>na madannin dake ƙasa domin fara haɗawa.", "connectHardwareSearchingPrimaryText": "<PERSON> bincika walat...", "connectHardwareSearchingSecondaryText": "Haɗa walat naka na hadwaya, ka tabbatar yana bude, kuma ka tabbatar ka ba da izini a brauzar ka.", "connectHardwarePermissionDeniedPrimary": "An ƙi yin izini", "connectHardwarePermissionDeniedSecondary": "Ba Phantom izini haɗewa da na'urar Ledger dinka", "connectHardwarePermissionUnableToConnect": "Haɗewa bai yiwu ba", "connectHardwarePermissionUnableToConnectDescription": "Ba mu iya haɗewa da na'urar <PERSON> dinka ba. <PERSON>a yiwuwa muna bukatar ƙarin wasu izini.", "connectHardwareSelectAddressAllAddressesImported": "An shigo da dukkanin ad<PERSON>hi", "connectHardwareSelectAddressDerivationPath": "<PERSON><PERSON><PERSON>", "connectHardwareSelectAddressSearching": "<PERSON> bincike...", "connectHardwareSelectAddressSelectWalletAddress": "Zaɓi ad<PERSON>hin walat", "connectHardwareSelectAddressWalletAddress": "<PERSON><PERSON><PERSON> wa<PERSON>", "connectHardwareWaitingForApplicationSecondaryText": "<PERSON>na fatan zaka haɗa walat naka na hadwaya kuma ka tabbatar yana bude.", "connectHardwareWaitingForPermissionPrimaryText": "Akwai bukatar i<PERSON>ni", "connectHardwareWaitingForPermissionSecondaryText": "Haɗa walat naka na hadwaya kuma ka tabbatar yana bude, kuma ka tabbatar ka bada izini a brauzar ka.", "connectHardwareAddAccountButton": "<PERSON>", "connectHardwareLedger": "Haɗa Ledger naka", "connectHardwareStartConnection": "<PERSON>na madanni na kasa domin fara matakan haɗa Ledger hadwayar walat dinka", "connectHardwarePairSuccessPrimary": "An haɗa {{productName}}", "connectHardwarePairSuccessSecondary": "Ka yi nasarar haɗa {{productName}} naka.", "connectHardwareSelectChains": "Zaɓi chains da za a haɗa", "connectHardwareSearching": "<PERSON> bincike...", "connectHardwareMakeSureConnected": "Haɗa sannan ka bude walat na had<PERSON>a. Da fatan zaka bada izinin burauza da suka dace.", "connectHardwareOpenAppDescription": "Da fatan zaka bude walat na had<PERSON>a", "connectHardwareConnecting": "Ana haɗawa...", "connectHardwareConnectingDescription": "<PERSON>na haɗewa da na'urar <PERSON> dinka.", "connectHardwareConnectingAccounts": "Ana haɗa asusunka...", "connectHardwareDiscoveringAccounts": "<PERSON><PERSON> asusu...", "connectHardwareDiscoveringAccountsDescription": "<PERSON>na duba aiki cikin asusunka.", "connectHardwareErrorLedgerLocked": "<PERSON> kunle Ledger", "connectHardwareErrorLedgerLockedDescription": "<PERSON><PERSON><PERSON> na'<PERSON><PERSON>ger naka na a bude, sannan ka sake gwadawa.", "connectHardwareErrorLedgerGeneric": "An sami matsala", "connectHardwareErrorLedgerGenericDescription": "Ba a iya ganin ausunka ba. <PERSON><PERSON><PERSON> na'urar <PERSON> naka na a bude, sannan ka sake gwadawa.", "connectHardwareErrorLedgerPhantomLocked": "<PERSON>na fatan za a sake bude Phantom sannan a yi kokarin haɗewa da hadwaya.", "connectHardwareFindingAccountsWithActivity": "<PERSON> {{chain<PERSON>ame}} asusu...", "connectHardwareFindingAccountsWithActivityDualChain": "<PERSON> neman asusu {{chainName1}} ko {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Mun sami asusu {{numOfAccounts}} masu aiki a naka Ledger.", "connectHardwareFoundAccountsWithActivitySingular": "Mun sami asusu 1 mai aiki a Ledger dinka.", "connectHardwareFoundSomeAccounts": "<PERSON>n sami wasu asusu a na'urar Ledger dinka.", "connectHardwareViewAccounts": "<PERSON><PERSON>", "connectHardwareConnectAccounts": "An haɗa asusu", "connectHardwareSelectAccounts": "Zaɓi wasu <PERSON>", "connectHardwareChooseAccountsToConnect": "Zaɓi asusun walat da za a haɗa.", "connectHardwareAccountsAddedInterpolated": "An kara <PERSON>u {{numOfAccounts}}", "connectHardwareAccountsStepOfSteps": "<PERSON><PERSON> {{stepNum}} na {{totalSteps}}", "connectHardwareMobile": "Haɗa Ledger", "connectHardwareMobileTitle": "Haɗa Ledger na haɗawayar walat dinka", "connectHardwareMobileEnableBluetooth": "Bude Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> am<PERSON>i da Bluetooth domin haɗewa", "connectHardwareMobileEnableBluetoothSettings": "Je ka Saituna don ba Phantom izinin amfani da wuri da izinin Na<PERSON><PERSON><PERSON><PERSON>.", "connectHardwareMobilePairWithDevice": "Dai<PERSON><PERSON> na'urar <PERSON> dinka", "connectHardwareMobilePairWithDeviceDescription": "<PERSON><PERSON><PERSON> da na'urarka don samun sigina mafi inganci", "connectHardwareMobileConnectAccounts": "Haɗa asusu", "connectHardwareMobileConnectAccountsDescription": "<PERSON><PERSON>u nemi harkoki a kowanne daga cikin asusun da ko ka taba amfani da su", "connectHardwareMobileConnectLedgerDevice": "Haɗa na'u<PERSON> dinka", "connectHardwareMobileLookingForDevices": "Ana neman na'u<PERSON>rin da ke kusa...", "connectHardwareMobileLookingForDevicesDescription": "<PERSON>na fatan zaka haɗa na'urar <PERSON>ger dinka kuma ka tabbatar tana a bude.", "connectHardwareMobileFoundDeviceSingular": "Mun sami na'urar Ledger 1", "connectHardwareMobileFoundDevices": "Mun sami na'<PERSON><PERSON><PERSON> {{numDevicesFound}}", "connectHardwareMobileFoundDevicesDescription": "Zaɓi na'urar <PERSON>ger daga kasa domin fara daidaitawa.", "connectHardwareMobilePairingWith": "<PERSON> da<PERSON> da {{deviceName}}", "connectHardwareMobilePairingWithDescription": "<PERSON>i umarnin kan na'urar <PERSON>ger yayin da<PERSON>wa.", "connectHardwareMobilePairingFailed": "Dai<PERSON>ta<PERSON> bata yi nasara ba", "connectHardwareMobilePairingFailedDescription": "Ba a iya daidaitawa da {{deviceName}} ba. <PERSON><PERSON><PERSON> na'urarka na bude.", "connectHardwareMobilePairingSuccessful": "Daidaitawa ta yi nasara", "connectHardwareMobilePairingSuccessfulDescription": "Ka yi nasarar daidaita da haɗa na'urar <PERSON> dinka.", "connectHardwareMobileOpenAppSingleChain": "<PERSON><PERSON> manhajar {{chainName}} a Ledger dinka", "connectHardwareMobileOpenAppDualChain": "<PERSON><PERSON> manhajar {{chainName1}} ko {{chainName2}} a Ledger dinka", "connectHardwareMobileOpenAppDescription": "Tabbatar na'urarka na a bude.", "connectHardwareMobileStillCantFindDevice": "Har yanzu ba ka ga na'urarka ba?", "connectHardwareMobileLostConnection": "An rasa haɗi", "connectHardwareMobileLostConnectionDescription": "An rasa haɗi zuwa {{deviceName}} tabbatar na'urarka na bude sannan ka sake gwadawa.", "connectHardwareMobileGenericLedgerDevice": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileConnectDeviceSigning": "Haɗa naka {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "<PERSON><PERSON> na'urar Ledger dinka sannan ka kawo ta kusa.", "connectHardwareMobileBluetoothDisabled": "<PERSON> kashe <PERSON>", "connectHardwareMobileBluetoothDisabledDescription": "Da fatan zaka kunna Bluetooth naka sannan ka tabbatar na'u<PERSON> dinka na a bude.", "connectHardwareMobileLearnMore": "<PERSON><PERSON><PERSON> karin sani", "connectHardwareMobileBlindSigningDisabled": "An rufe sa-hannu a makance", "connectHardwareMobileBlindSigningDisabledDescription": "<PERSON><PERSON><PERSON> sa-hannu a makance na a kunne a na'urarka.", "connectHardwareMobileConfirmSingleChain": "<PERSON>le sai ka tabbatar da cinikin a walat dinka na haɗawaya. Tabbatar yana a bude.", "metamaskExplainerBottomSheetHeader": "<PERSON>n shafi na aiki da <PERSON>", "metamaskExplainerBottomSheetSubheader": "Zaɓi MetaMask daga haɗa tatta<PERSON>war walat don cigaba.", "metamaskExplainerBottomSheetDontShowAgain": "Kada a sake nunawa", "ledgerStatusNotConnected": "Ba a haɗa Ledger ba", "ledgerStatusConnectedInterpolated": "An haɗa {{productName}}", "connectionClusterInterpolated": "A halin yanzu kana kan {{cluster}}", "connectionClusterTestnetMode": "<PERSON><PERSON> cikin Testnet Mode yanzu", "featureNotSupportedOnLocalNet": "Wannan tsari ba ya aiki lokacin da an kunna Solana Localnet.", "readOnlyAccountBannerWarning": "<PERSON>na kallon <PERSON>n asusu", "depositAddress": "Karɓi <PERSON><PERSON><PERSON>", "depositAddressChainInterpolated": "<PERSON><PERSON><PERSON> {{chain}} naka", "depositAssetDepositInterpolated": "Karɓi {{tokenSymbol}}", "depositAssetSecondaryText": "Za a iya amfani da wannan adireshin ne domin karbar tokin da suka dace da juna kawai.", "depositAssetTextInterpolated": "Yi amfani da wannan adireshin don karbar tokin da kayayyaki a <1>{{network}}</1>.", "depositAssetTransferFromExchange": "<PERSON>ifa daga wurin musaya", "depositAssetShareAddress": "<PERSON><PERSON><PERSON>", "depositAssetBuyOrDeposit": "Saya ko Transifa", "depositAssetBuyOrDepositDesc": "Sa kuɗi a Walat dinka don farawa", "depositAssetTransfer": "Transifa", "editAddressAddressAlreadyAdded": "An riga an sa adireshi", "editAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON> na nan da ma", "editAddressAddressIsRequired": "<PERSON> buka<PERSON> ad<PERSON>hi", "editAddressPrimaryText": "<PERSON><PERSON><PERSON>", "editAddressRemove": "Cire daga kundin ad<PERSON>hi", "editAddressToast": "An sabunta adireshi", "removeSavedAddressToast": "An cire adireshi", "exportSecretErrorGeneric": "An sami matsala, sake gwadawa anjima", "exportSecretErrorIncorrectPassword": "<PERSON><PERSON><PERSON> shiga ba daidai ba", "exportSecretPassword": "<PERSON><PERSON><PERSON> shiga", "exportSecretPrivateKey": "lamban sirri", "exportSecretSecretPhrase": "<PERSON><PERSON><PERSON> sirri", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "ji<PERSON><PERSON> sirri na dawo da bayanai", "exportSecretSelectYourAccount": "<PERSON><PERSON>", "exportSecretShowPrivateKey": "<PERSON><PERSON>", "exportSecretShowSecretRecoveryPhrase": "<PERSON><PERSON> ji<PERSON> sirri na dawo da bayanai", "exportSecretShowSecret": "<PERSON><PERSON> {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "Kada <1>ka</1> yaɗa {{secretNameText}} naka!", "exportSecretWarningSecondaryInterpolated": "Idan wani yana da {{secretNameText}} naka zai sami cikakken iko da walat naka.", "exportSecretOnlyWay": "Naka {{secretNameText}} ita ce kawai hanyar dawo da walat dinka", "exportSecretDoNotShow": "Kada ka bari wani ya ga {{secretNameText}} naka", "exportSecretWillNotShare": "Ba zan gaya wa kowa {{secretNameText}} ba, har da <PERSON>.", "exportSecretNeverShare": "Kada ka nuna wa kowa {{secretNameText}} naka", "exportSecretYourPrivateKey": "<PERSON><PERSON> naka", "exportSecretYourSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai naka", "exportSecretResetPin": "Sake saita PIN dinka", "fullPageHeaderBeta": "Na gwaji!", "fullPageHeaderHelp": "<PERSON><PERSON><PERSON>", "gasUpTo": "<PERSON><PERSON><PERSON> {{ amount }}", "timeDescription1hour": "Kimanin hawa 1", "timeDescription30minutes": "Kimanin mintina 30", "timeDescription10minutes": "Kimanin mintina 10", "timeDescription2minutes": "Kimanin mintina 2", "timeDescription30seconds": "<PERSON><PERSON><PERSON> 30", "timeDescription15seconds": "<PERSON><PERSON><PERSON> 15", "timeDescription10seconds": "<PERSON><PERSON><PERSON> 10", "timeDescription5seconds": "<PERSON><PERSON><PERSON> 5", "timeDescriptionAbbrev1hour": "Hawa1", "timeDescriptionAbbrev30minutes": "Minti30", "timeDescriptionAbbrev10minutes": "Minti10", "timeDescriptionAbbrev2minutes": "Mint2", "timeDescriptionAbbrev30seconds": "Sakan30", "timeDescriptionAbbrev15seconds": "Sakan15", "timeDescriptionAbbrev10seconds": "Sakan10", "timeDescriptionAbbrev5seconds": "Sakan5", "gasSlow": "<PERSON><PERSON><PERSON>", "gasAverage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gasFast": "<PERSON><PERSON>", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "<PERSON>ke gwa<PERSON>wa", "homeErrorDescription": "An sami matsala wajen debo kadarorin ka. Da fatan zaka sabunta sannan ka sake gwadawa", "homeErrorTitle": "<PERSON><PERSON> ka<PERSON> bai yiwu ba", "homeManageTokenList": "<PERSON><PERSON> da jerin tokin", "interstitialDismissUnderstood": "An gane", "interstitialBaseWelcomeTitle": "Base Na Aiki A Phantom Now!", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON>, karɓi kuma ka sayi tokin", "interstitialBaseWelcomeItemTitle_2": "Bincika tsarin Base", "interstitialBaseWelcomeItemTitle_3": "<PERSON><PERSON><PERSON> da tsaro", "interstitialBaseWelcomeItemDescription_1": "Yi transfa da daya USDC da ETH a Base ta amfani da {{paymentMethod}} kati ko Coinbase.", "interstitialBaseWelcomeItemDescription_2": "<PERSON> am<PERSON>i da <PERSON> da Manha<PERSON>jin De<PERSON>i da NFT da ka fi so.", "interstitialBaseWelcomeItemDescription_3": "<PERSON><PERSON> cikin aminci tare da ta<PERSON><PERSON> Led<PERSON>, mat<PERSON><PERSON> sakon <PERSON>, da cinikin kwaik<PERSON>.", "privacyPolicyChangedInterpolated": "<PERSON><PERSON>. <1>Ƙara <PERSON>yi</1>", "bitcoinAddressTypesBodyTitle": "<PERSON><PERSON>'o'in Adireshin Bitcoin", "bitcoinAddressTypesFeature1Title": "Game da Adireshoshin Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom na aiki da <PERSON> <PERSON><PERSON><PERSON><PERSON>, kowanne na da balans dinshi. Kana iya aika BTC ko Ordinals tare da kowane nau'in adireshi.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "<PERSON><PERSON><PERSON> BTC na asali a Phantom. Ya girmi <PERSON> amma yana aiki da duk walat da dillalai.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "<PERSON><PERSON> k<PERSON>u ga Ordinals da BRC-20s, tare da mafi arha caje-caje. <PERSON><PERSON><PERSON> ad<PERSON>hi a cikin Zaɓuɓɓuka -> <PERSON><PERSON><PERSON> Bitcoin da aka <PERSON> so.", "headerTitleInfo": "<PERSON><PERSON>", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Wannan ne adireshinka na <1>{{addressType}}</1>.", "invalidChecksumTitle": "Mun haɓaka jumlar sirrinka!", "invalidChecksumFeature1ExportPhrase": "<PERSON><PERSON> da sabuwar jumlar sirri", "invalidChecksumFeature1ExportPhraseDescription": "Da fatan za ka adana sabon kalmomin sirrinka tare da mabudin sirri na tsoffin asusunka.", "invalidChecksumFeature2FundsAreSafe": "Kuɗinka na cikin aminci da tsaro", "invalidChecksumFeature2FundsAreSafeDescription": "Wannan haɓakawa ta atomatik ce. <PERSON> wani a Phantom da ya san jumlar sirrinka ko kuma yana da damar yin amfani da kuɗin ka.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON><PERSON> karin sani", "invalidChecksumFeature3LearnMoreDescription": "<PERSON>na da jumlar da ba ta dace da yawancin walat ba. Karanta <1>wannan bayanin ta<PERSON>ko</1> don ƙarin sani game da wannan.", "invalidChecksumBackUpSecretPhrase": "<PERSON><PERSON> madadin jumlar sirri", "migrationFailureTitle": "An sami matsala ana canza gurbin asusunka", "migrationFailureFeature1": "<PERSON><PERSON> da kalmomin sir<PERSON>ka", "migrationFailureFeature1Description": "Da fatan za ka adana kalmomin sirrinka kafin shigowa.", "migrationFailureFeature2": "Shiga Phantom", "migrationFailureFeature2Description": "<PERSON>le sai ka sake shiga Phantom domin duba asusunka.", "migrationFailureFeature3": "<PERSON><PERSON><PERSON> karin sani", "migrationFailureFeature3Description": "Karanta <1>wannan bayani </1> don koyon ƙarin sani a kan wannan.", "migrationFailureContinueToOnboarding": "<PERSON>i gaba da shigowa", "migrationFailureUnableToFetchMnemonic": "Ba mu iya loda kalmomin sirrinka ba", "migrationFailureUnableToFetchMnemonicDescription": "Da fatan za a tuntuɓi masu kula sannan ka sauke manhajar rijistar ayyuka don gyara kuskure", "migrationFailureContactSupport": "Tuntuɓi masu kula", "ledgerActionConfirm": "<PERSON><PERSON><PERSON> a Ledger <PERSON> naka", "ledgerActionErrorBlindSignDisabledPrimaryText": "An rufe sa-hannu a makance", "ledgerActionErrorBlindSignDisabledSecondaryText": "<PERSON>na fatan zaka bude sa-hannu a makance a hadwayar na'urarka sannan ka sake aikin", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "An yanke sadarwar hadwayar na'ura lokacin da ta ke aiki", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "<PERSON>na fatan zaka zabi mahadin Phantom sannan ka sake aikin", "ledgerActionErrorDeviceLockedPrimaryText": "An kulle hadwayar na'ura", "ledgerActionErrorDeviceLockedSecondaryText": "<PERSON>na fatan zaka bude hadwayar na'urarka sannan ka sake aikin", "ledgerActionErrorHeader": "Matsalar Ledger Action", "ledgerActionErrorUserRejectionPrimaryText": "Mai amfani ya kori ciniki", "ledgerActionErrorUserRejectionSecondaryText": "Mai amfani ya kori aikin daga hadwayar na'ura", "ledgerActionNeedPermission": "<PERSON><PERSON><PERSON> buka<PERSON>i", "ledgerActionNeedToConfirm": "<PERSON>le sai ka amince da cinikin walat naka na hadwaya. Ka tabbatar yana bude a manhajar {{chainType}}.", "ledgerActionNeedToConfirmMany": "<PERSON>le sai ka tabbatar da ciniki {{numberOfTransactions}} a walat na hadwaya naka. Ka tabbatar yana bude a manhajar {{chainType}}.", "ledgerActionNeedToConfirmBlind": "<PERSON>le sai ka tabbatar da ciniki a walat naka na hadwaya. Ka tabbatar yana bude a manhajar {{chainType}}, kuma ma<PERSON><PERSON>yar sa-hannu na a kunne.", "ledgerActionNeedToConfirmBlindMany": "<PERSON>le sai ka amince da cinikin {{numberOfTransactions}} a walat naka na hadwaya. Ka tabbatar yana bude a manhajar {{chainType}}, kuma ma<PERSON>yar sa-hannu na a kunne.", "ledgerActionPleaseConnect": "<PERSON>na fatan zaka haɗa Ledger <PERSON><PERSON> naka", "ledgerActionPleaseConnectAndConfirm": "Da fatan walat dinka na haɗawaya na a bude. Tabbatar ka amince da izini a brauzarka.", "maxInputAmount": "<PERSON><PERSON>", "maxInputMax": "<PERSON><PERSON>", "notEnoughSolPrimaryText": "<PERSON><PERSON><PERSON>", "notEnoughSolSecondaryText": "Baka da isassun SOL a Walat dinka don wannan ciniki. Da fatan zaka saka kari sannan ka sake gwadawa anjima.", "insufficientBalancePrimaryText": "<PERSON> is<PERSON>hen {{tokenSymbol}}", "insufficientBalanceSecondaryText": "Ba ka da isassun {{tokenSymbol}} a cikin walat ɗinka don wannan ciniki.", "insufficientBalanceRemaining": "<PERSON><PERSON>", "insufficientBalanceRequired": "<PERSON> b<PERSON>", "notEnoughSplTokensTitle": "<PERSON> <PERSON><PERSON><PERSON>", "notEnoughSplTokensDescription": "<PERSON> ka da isasshen tokin a wannan walat ga wannan ciniki. Wannan ciniki zai dawo in an gabatar da shi.", "transactionExpiredPrimaryText": "<PERSON><PERSON><PERSON> ciniki ya ƙare", "transactionExpiredSecondaryText": "Ka jinkirta da yawa kafin ka tabbatar da cinikin kuma ya wuce. Wannan cinikin zai dawo in an mika shi.", "transactionHasWarning": "G<PERSON><PERSON>din ciniki", "tokens": "tokin", "notificationApplicationApprovalPermissionsAddressVerification": "<PERSON><PERSON><PERSON> wannan ad<PERSON>oshin naka ne", "notificationApplicationApprovalPermissionsTransactionApproval": "<PERSON><PERSON><PERSON> amin<PERSON>war yin ciniki", "notificationApplicationApprovalPermissionsViewWalletActivity": "Dubi balans da ayyukan walat naka", "notificationApplicationApprovalParagraphText": "Tabbatarwa zai ba wannan sha<PERSON> i<PERSON>in duba balans da ayyukan asusun da aka zaɓa.", "notificationApplicationApprovalActionButtonConnect": "Haɗa", "notificationApplicationApprovalActionButtonSignIn": "Shiga", "notificationApplicationApprovalAllowApproval": "A kyale shafi ya sadu?", "notificationApplicationApprovalAutoConfirm": "<PERSON><PERSON><PERSON>-Atomatik", "notificationApplicationApprovalConnectDisclaimer": "Ka haɗu da shafukan intanet wadanda ka yarda da su", "notificationApplicationApprovalSignInDisclaimer": "<PERSON>ga shafukan intanet da ka aminta da su kawai", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "<PERSON>n shafi ba shi da amincin amfani kuma ya mabiya kokarin satar kuɗaɗenka.", "notificationApplicationApprovalConnectUnknownApp": "<PERSON> ba a sani ba", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Haɗewa da manhaja bai yiwu ba", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "<PERSON>n manhajar na ƙoƙarin haɗewa da {{appNetworkName}}, amma an zaɓi {{phantomNetworkName}}.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "<PERSON> am<PERSON> da {{networkName}}, je ka <PERSON> Haɓakawa → <PERSON><PERSON><PERSON> Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Netwok da ba a sani ba", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Haɗewa da wasu manhajojin mobayil ba ya aiki a Ledger.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Da fatan zaka canza zuwa asusu mara-Ledger ko ka yi amfani da brauza ta cikin manhaja sannan ka sake gwadawa.", "notificationSignatureRequestConfirmTransaction": "<PERSON><PERSON><PERSON> da ciniki", "notificationSignatureRequestConfirmTransactionCapitalized": "<PERSON><PERSON><PERSON> da Ciniki", "notificationSignatureRequestConfirmTransactions": "<PERSON><PERSON><PERSON> da ciniki", "notificationSignatureRequestConfirmTransactionsCapitalized": "<PERSON><PERSON><PERSON> da ciniki", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON> sa-hannu", "notificationMessageHeader": "<PERSON><PERSON>", "notificationMessageCopied": "An kwafi sako", "notificationAutoConfirm": "Tabbatarwar-Atomatik", "notificationAutoConfirmOff": "<PERSON><PERSON>", "notificationAutoConfirmOn": "<PERSON><PERSON>", "notificationConfirmFooter": "<PERSON><PERSON><PERSON> kawai in ka amince da wannan shafin intanet.", "notificationEstimatedTime": "<PERSON><PERSON><PERSON>", "notificationPermissionRequestText": "<PERSON>n neman izni ne kawai. <PERSON><PERSON><PERSON> ba zai aiwatu ba nan take.", "notificationBalanceChangesText": "An ƙiyasta canje-canjen balans. <PERSON><PERSON> kudade da kadarorin da ke ciki ba su da tabbas.", "notificationContractAddress": "<PERSON><PERSON><PERSON>", "notificationAdvancedDetailsText": "An gabatar", "notificationUnableToSimulateWarningText": "A halin yanzu ba za mu iya ƙiyasta canje-canjen balans ba. Za ka iya gwadawa anjima, ko ka tabbatar in ka amince da wannan shafin intanet.", "notificationSignMessageParagraphText": "Sa hannu a wannan sako zai tabbatar kai kake da mulkin asusun da aka zaɓa.", "notificationSignatureRequestScanFailedDescription": "Ba a iya laluba sako ba don da<PERSON><PERSON> tsaro. Da fatan zaka ci gaba cikin taka tsantsan.", "notificationFailedToScan": "An kasa yin kwaikwayon sakamakon wannan buƙatar.\n Tabbatar<PERSON> bashi da aminci kuma yana iya haifar da hasara.", "notificationScanLoading": "<PERSON><PERSON>", "notificationTransactionApprovalActionButtonConfirm": "Tabbatar", "notificationTransactionApprovalActionButtonBack": "Baya", "notificationTransactionApprovalEstimatedChanges": "<PERSON><PERSON><PERSON> can<PERSON>can<PERSON>", "notificationTransactionApprovalEstimatesBasedOnSimulations": "<PERSON><PERSON><PERSON> ya dogara da cinikin gwaji kuma ba abin dogaro bane", "notificationTransactionApprovalHideAdvancedDetails": "<PERSON><PERSON> bayanan cinikin da aka gaba<PERSON>", "notificationTransactionApprovalNetworkFee": "<PERSON><PERSON>", "notificationTransactionApprovalNetwork": "Netwok", "notificationTransactionApprovalEstimatedTime": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "<PERSON> canje-canjen da suka yi tasiri a kan mallakar kadarorin da aka samu", "notificationTransactionApprovalSolanaAmountRequired": "<PERSON><PERSON> da Netwok na Solana ke bukata kafin sarrafa ciniki", "notificationTransactionApprovalUnableToSimulate": "<PERSON><PERSON><PERSON> bai yiwu ba. Ka tabbatar ka amince wa wannan shafin intanet, domin zartar da ciniki zai iya kai ga asarar kudade.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Ba iya debo canje-canjen balans ba", "notificationTransactionApprovalViewAdvancedDetails": "<PERSON><PERSON> bayanan cinikin da aka gaba<PERSON>", "notificationTransactionApprovalKnownMalicious": "<PERSON><PERSON><PERSON> cuta a wannan ciniki, sa-hannu zai kai ga asaran kudade.", "notificationTransactionApprovalSuspectedMalicious": "<PERSON>na zargin akwai cuta a wannan ciniki, amin<PERSON><PERSON> zai iya kai ga asaran kudade.", "notificationTransactionApprovalNetworkFeeHighWarning": "An kara kuɗin netwok saboda cunkoson netwok.", "notificationTransactionERC20ApprovalDescription": "Tabbatarwa zai ba wannan manhajar i<PERSON>in shiga balans dinka kowane lokaci, har zuwa iyaka da ke kasa.", "notificationTransactionERC20ApprovalContractAddress": "<PERSON><PERSON><PERSON>", "notificationTransactionERC20Unlimited": "ba iyaka", "notificationTransactionERC20ApprovalTitle": "<PERSON><PERSON><PERSON> da kashe kuɗin {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Soke kashe kuɗin {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Soke izinin shiga {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Duk {{tokenSymbol}} dinka", "notificationIncorrectModeTitle": "<PERSON><PERSON>i mara daidai", "notificationIncorrectModeInTestnetTitle": "<PERSON>na cikin ya<PERSON>in <PERSON>", "notificationIncorrectModeNotInTestnetTitle": "Ba ka cikin yanayin <PERSON>", "notificationIncorrectModeInTestnetDescription": "{{origin}} na ƙoƙarin amfani da mainnet, amma kana cikin yanayin <PERSON>net", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} na ƙoƙarin amfani da testnet, amma ba ka cikin yanayin Testnet", "notificationIncorrectModeInTestnetProceed": "<PERSON> ciga<PERSON>, kashe ya<PERSON>in <PERSON>.", "notificationIncorrectModeNotInTestnetProceed": "<PERSON> ciga<PERSON>, kunna ya<PERSON>in <PERSON>.", "notificationIncorrectEIP712ChainId": "Mun hana ka sa hannu a sakon da ba na netwok da ka haɗe da shi yanzu aka nufa ba", "notificationIncorrectEIP712ChainIdDescription": "An nemi sako {{messageChainId}}, an haɗa ka da {{connectedChainId}}", "notificationUnsupportedNetwork": "Netwok da ba a aiki da shi", "notificationUnsupportedNetworkDescription": "Wannan shafi na ƙoƙarin amfani da netwok da Phantom baya aiki da shi a halin yanzu.", "notificationUnsupportedNetworkDescriptionInterpolated": "Don ci gaba da wani shafin brauza, ka<PERSON> <1><PERSON><PERSON><PERSON> → walat na asali na <PERSON>, kuma zaɓi <PERSON><PERSON></1>. Sannan sake sabunta shafin kuma sake haɗawa.", "notificationUnsupportedAccount": "<PERSON><PERSON><PERSON> da ba a aiki da shi", "notificationUnsupportedAccountDescription": "Wannan shafi na kokarin amfani da {{targetChainType}} wanda wannan {{chainType}} as<PERSON>un ba ya aiki da shi.", "notificationUnsupportedAccountDescription2": "Canza zuwa asusu daga <PERSON> seed da ta yi daidai da mabudin sirri sannan ka sake gwadawa.", "notificationInvalidTransaction": "<PERSON>iniki mara inganci", "notificationInvalidTransactionDescription": "<PERSON><PERSON><PERSON> da aka karɓo daga wannan manhaja bai cika ka'ida ba kuma bai kamata a mika shi ba. Da fatan za a tuntuɓi makagin wannan manhaja don mika korafi a kan wannan batu gare su.", "notificationCopyTransactionText": "Kwafi ciniki", "notificationTransactionCopied": "An kwafi ciniki", "onboardingImportOptionsPageTitle": "<PERSON><PERSON> da walat", "onboardingImportOptionsPageSubtitle": "<PERSON>go da walat ɗin da ka ke da shi tare da kalmar sirrin naka, ma<PERSON><PERSON> sirri, ko walat ɗin hadwaya.", "onboardingImportPrivateKeyPageTitle": "<PERSON><PERSON>", "onboardingImportPrivateKeyPageSubtitle": "<PERSON><PERSON> da walat da ka ke da shi na single-chain", "onboardingCreatePassword": "<PERSON><PERSON> kalmar shiga", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Na yarda da <1>Doko<PERSON> aiki</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "<PERSON><PERSON><PERSON> da kalmar shiga", "onboardingCreatePasswordDescription": "<PERSON>aka yi amfani da wannan don bude walat naka.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai mara inganci", "onboardingCreatePasswordPasswordPlaceholder": "<PERSON><PERSON><PERSON> shiga", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthMedium": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthStrong": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Na adana jimlar sirri na dawo da bayanai tawa", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Wannan jimlar ita KADAI ce hanyar dawo da walat naka. KADA ka ba kowa!", "onboardingImportWallet": "<PERSON><PERSON> da walat", "onboardingImportWalletImportExistingWallet": "<PERSON><PERSON> da walat da kake da shi mai dauke da kalmomin sirri 12 ko 24 naka na dawo da bayanai.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON> da walat", "onboardingImportWalletSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai mara inganci", "onboardingImportWalletIHaveWords": "Ina da {{numWords}}- na kalmar dawo da bayanai", "onboardingImportWalletIncorrectOrMisspelledWord": "<PERSON><PERSON> {{wordIndex}} ba daidai ba ko an yi kuskuren rubutu", "onboardingImportWalletIncorrectOrMisspelledWords": "Kalmomi {{wordIndexes}} ba daidai ba ko an yi kuskuren rubutu", "onboardingImportWalletScrollDown": "<PERSON> kasan rubutu", "onboardingImportWalletScrollUp": "<PERSON><PERSON> saman rubutu", "onboardingSelectAccountsImportAccounts": "<PERSON><PERSON> da asusu", "onboardingSelectAccountsImportAccountsDescription": "<PERSON><PERSON> walat da zaka shigo su.", "onboardingSelectAccountsImportSelectedAccounts": "<PERSON><PERSON> da asusun da aka zaba", "onboardingSelectAccountsFindMoreAccounts": "Nemo ƙarin asusu", "onboardingSelectAccountsFindMoreNoneFound": "Ba a sami asusu ba", "onboardingSelectAccountsNoOfAccountsSelected": "An zaɓi asusu {{numOfAccounts}}", "onboardingSelectAccountSelectAllText": "Zaɓi duka", "onboardingAdditionalPermissionsTitle": "<PERSON> amfani da <PERSON>ji tare da <PERSON>", "onboardingAdditionalPermissionsSubtitle": "<PERSON> mafi daɗin amfani da manhaja babu tangarda, muna ba da shaw<PERSON>r barin Phantom ya karanta tare da canza bayanai a duk shafuka.", "interstitialAdditionalPermissionsTitle": "<PERSON> amfani da <PERSON>ji tare da <PERSON>", "interstitialAdditionalPermissionsSubtitle": "Don ci gaba da amfani da <PERSON> ba tare da katsewa ba, muna ba da shawarar barin Phantom ya karanta tare da canza bayanai a duk shafuka.", "recentActivityPrimaryText": "<PERSON>kin baya-bayan nan", "removeAccountActionButtonRemove": "Cire", "removeAccountRemoveWallet": "Cire asusu", "removeAccountInterpolated": "Cire {{accountName}}", "removeAccountWarningLedger": "Duk da yake zaka cire wannan walat daga <PERSON>, zaka iya mayar da shi ta hanyan amfani da matakan \"Connect Hardware Wallet\".", "removeAccountWarningSeedVault": "Duk da yake zaka cire wannan walat daga <PERSON>, zaka iya mayar da shi ta hanyar amfani da matakan \"Connect Seed Vault Wallet\".", "removeAccountWarningPrivateKey": "<PERSON> zarar ka cire wannan walat, <PERSON> ba zai iya dawo maka da shi ba. Ka tabbata ka adana lambar sirri naka.", "removeAccountWarningSeed": "Duk da yake zaka cire wannan walat daga <PERSON>, zaka iya dawo da shi ta hanyar amfani da jerin-lambobi naka a wannan ko wani walat.", "removeAccountWarningReadOnly": "Goge wannan as<PERSON>un ba zai shafi walat dinka ba, saboda walat na kallo-kawai ne.", "removeSeedPrimaryText": "Ana cire jimlar sirri {{number}}", "removeSeedSecondaryText": "Wannan zai cire duk asusun da suke cikin <PERSON> {{number}}. Tabbatar ka adana <PERSON> naka da kake da su yanzu.", "resetSeedPrimaryText": "Sake saita manhaja da sabon <PERSON> sirri", "resetSeedSecondaryText": "Wannan zai cire duk asusun da ke akwai kuma ya maye gurbin su da sabbi. Tabba<PERSON> kana da jimlar sirrin da ke akwai da kuma mabudin sirri a ma'ajiya.", "resetAppPrimaryText": "Sake saitawa & goge manhaja", "resetAppSecondaryText": "Wannan zai cire duk asusu da bayanai da ka ke da su yanzu. <PERSON><PERSON><PERSON> ka adana <PERSON> sirri da mabudin sirrinka.", "richTransactionsDays": "ranaku", "richTransactionsToday": "<PERSON><PERSON>", "richTransactionsYesterday": "<PERSON><PERSON>", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "<PERSON><PERSON><PERSON>", "richTransactionDetailAt": "a", "richTransactionDetailBid": "<PERSON>yi", "richTransactionDetailBidDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailBought": "An saya", "richTransactionDetailBurned": "An kone", "richTransactionDetailCancelBid": "<PERSON><PERSON>", "richTransactionDetailCompleted": "<PERSON> kammala", "richTransactionDetailConfirmed": "An tabbatar", "richTransactionDetailDate": "<PERSON><PERSON><PERSON> wata", "richTransactionDetailFailed": "Ba yiwu ba", "richTransactionDetailFrom": "Daga", "richTransactionDetailItem": "<PERSON><PERSON>", "richTransactionDetailListed": "An jera", "richTransactionDetailListingDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailListingPrice": "<PERSON><PERSON><PERSON>", "richTransactionDetailMarketplace": "<PERSON><PERSON><PERSON>", "richTransactionDetailNetworkFee": "<PERSON><PERSON>", "richTransactionDetailOriginalListingPrice": "<PERSON><PERSON><PERSON>", "richTransactionDetailPending": "<PERSON><PERSON>", "richTransactionDetailPrice": "<PERSON><PERSON>", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON>", "richTransactionDetailPurchaseDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailRebate": "<PERSON><PERSON>", "richTransactionDetailReceived": "<PERSON> karba", "richTransactionDetailSaleDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailSent": "An aika", "richTransactionDetailSold": "An sayar", "richTransactionDetailStaked": "<PERSON> jinginar", "richTransactionDetailStatus": "<PERSON><PERSON><PERSON>", "richTransactionDetailSwap": "Musanya", "richTransactionDetailSwapDetails": "Bayanan musa<PERSON>", "richTransactionDetailTo": "<PERSON><PERSON>wa ga", "richTransactionDetailTokenSwap": "<PERSON><PERSON><PERSON>", "richTransactionDetailUnknownNFT": "NFT da ba sani ba", "richTransactionDetailUnlisted": "Cire <PERSON>", "richTransactionDetailUnstaked": "An cire daga jingina", "richTransactionDetailValidator": "<PERSON> kula da ciniki", "richTransactionDetailViewOnExplorer": "<PERSON><PERSON> a {{explorer}}", "richTransactionDetailWithdrawStake": "Cire jingina", "richTransactionDetailYouPaid": "Ka biya", "richTransactionDetailYouReceived": "<PERSON> karba", "richTransactionDetailUnwrapDetails": "Bayanan cire jingina", "richTransactionDetailTokenUnwrap": "An cire <PERSON><PERSON>", "activityItemsRefreshFailed": "<PERSON>da sabbin ciniki bai yiwu ba.", "activityItemsPagingFailed": "<PERSON><PERSON> tsaffin ciniki bai yiwu ba.", "activityItemsTestnetNotAvailable": "<PERSON> ta<PERSON><PERSON> cinikin Testnet yanzu", "historyUnknownDappName": "<PERSON> ba a sani ba", "historyStatusSucceeded": "An yi nasara", "historyNetwork": "Netwok", "historyAttemptedAmount": "<PERSON><PERSON> aka gwada sawa", "historyAmount": "<PERSON><PERSON>", "sendAddressBookButtonLabel": "<PERSON><PERSON><PERSON>", "addressBookSelectAddressBook": "<PERSON><PERSON><PERSON>", "sendAddressBookNoAddressesSaved": "Ba adana kowane ad<PERSON>hi ba", "sendAddressBookRecentlyUsed": "<PERSON> aka yi amfani da su baya-bayan nan", "addressBookSelectRecentlyUsed": "<PERSON> aka yi amfani da su baya-bayan nan", "sendConfirmationLabel": "<PERSON><PERSON><PERSON>", "sendConfirmationMessage": "<PERSON><PERSON>", "sendConfirmationNetworkFee": "<PERSON><PERSON>", "sendConfirmationPrimaryText": "Ka tabbatar an aika", "sendWarning_INSUFFICIENT_FUNDS": "<PERSON> isassun kuɗade, wannan ciniki zai iya kasawa in an mika.", "sendFungibleSummaryNetwork": "Netwok", "sendFungibleSummaryNetworkFee": "<PERSON><PERSON> netwok", "sendFungibleSummaryEstimatedTime": "<PERSON><PERSON><PERSON>", "sendFungiblePendingEstimatedTime": "<PERSON><PERSON><PERSON>", "sendFungibleSummaryEstimatedTimeDescription": "<PERSON><PERSON>n cinikin Ethereum na bambanta saboda dalilai. <PERSON>aka iya kara masa sauri in ka danna \"<PERSON><PERSON><PERSON> Netwok\".", "sendSummaryBitcoinPendingTxTitle": "<PERSON><PERSON> transfa bai yiwu ba", "sendSummaryBitcoinPendingTxDescription": "Transfa na BTC ɗaya za ka iya samu cikin jira a lokaci daya. Da fatan za ka bari ta kammala kafin ka mika sabuwar transfa.", "sendFungibleSatProtectionTitle": "<PERSON><PERSON><PERSON>", "sendFungibleSatProtectionExplainer": "Phantom na tabbatar da cewa ba a amfani da Ordinals ɗinku da BRC20 a biyan cajin ciniki ko transfa na Bitcoin.", "sendFungibleTransferFee": "<PERSON><PERSON><PERSON> trans<PERSON> na tokin", "sendFungibleTransferFeeToolTip": "<PERSON> ya kirkiri wannan tokin na karɓar caji akan kowane transfa. Ba Phantom ke yi ko karbar wannan caji.", "sendFungibleInterestBearingPercent": "<PERSON>bon Kuɗin Ruwa", "sendFungibleNonTransferable": "Ba Abin Da Za Iya Transfa Ba Ne", "sendFungibleNonTransferableToolTip": "Baza iya transfa na wannan tokin zuwa wani asusun ba.", "sendFungibleNonTransferableYes": "E", "sendStatusErrorMessageInterpolated": "An sami matsala wajen aikewa da tokin zuwa ga <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "<PERSON> ka da isasshen balans na kammala wannan ciniki.", "sendStatusErrorTitle": "Ba a iya aikawa ba", "sendStatusLoadingTitle": "<PERSON> a<PERSON>...", "sendStatusSuccessMessageInterpolated": "An yi nasarar aikawa da tokin naka <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "An aika!", "sendStatusConfirmedSuccessTitle": "An aika!", "sendStatusSubmittedSuccessTitle": "An mika ciniki", "sendStatusEstimatedTransactionTime": "<PERSON><PERSON><PERSON> c<PERSON> {{time}}", "sendStatusViewTransaction": "<PERSON><PERSON> ciniki", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> zuwa ga <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "An yi nasarar aika <2>{{uiAmount}} {{assetSymbol}}</2> zuwa ga <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "An yi nasarar aika <2>{{uiAmount}} {{assetSymbol}}</2> zuwa ga <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "Aika <2>{{uiAmount}} {{assetSymbol}}</2> bai yiwu ba zuwa ga <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON><PERSON> {{code}}", "sendFormErrorInsufficientBalance": "<PERSON> is<PERSON><PERSON> balans", "sendFormErrorEmptyAmount": "<PERSON><PERSON> da ake bukata", "sendFormInvalidAddress": "<PERSON><PERSON><PERSON> {{assetName}} mara aiki", "sendFormInvalidUsernameOrAddress": "<PERSON>an mai amfani ko ad<PERSON>hi ba daidai ba", "sendFormErrorInvalidSolanaAddress": "<PERSON><PERSON><PERSON> mara inganci", "sendFormErrorInvalidTwitterHandle": "Ba a yi rijistar wannan mahadin Twitter ba", "sendFormErrorInvalidDomain": "<PERSON><PERSON> shafin intanet ba shi da rijista", "sendFormErrorInvalidUsername": "Ba a rijistar wannan sunan mai amfani ba", "sendFormErrorMinRequiredInterpolated": "<PERSON> bukatar akalla {{minAmount}}{{tokenName}}", "sendRecipientTextareaPlaceholder": "<PERSON><PERSON><PERSON> mai karbar SOL", "sendRecipientTextAreaPlaceholder2": "<PERSON><PERSON><PERSON> {{symbol}} mai karba", "sendMemoOptional": "<PERSON><PERSON> (zaɓi ne)", "sendMemo": "Memo", "sendOptional": "na zaɓi", "settings": "<PERSON><PERSON><PERSON>", "settingsDapps": "dApps", "settingsSelectedAccount": "<PERSON><PERSON><PERSON> da aka zaɓa", "settingsAddressBookNoLabel": "<PERSON>", "settingsAddressBookPrimary": "<PERSON><PERSON><PERSON>", "settingsAddressBookRecentlyUsed": "An yi amfani da su baya-bayan nan", "settingsAddressBookSecondary": "<PERSON><PERSON> da ad<PERSON>hin da aka saba amfani da su", "settingsAutoLockTimerPrimary": "Agogo Mai kulle kansa", "settingsAutoLockTimerSecondary": "Canza lokacin agogon ka mai kulle kansa", "settingsChangeLanguagePrimary": "<PERSON>za Ya<PERSON>", "settingsChangeLanguageSecondary": "<PERSON>za yaren sikirin", "settingsChangeNetworkPrimary": "Canza Netwok", "settingsChangeNetworkSecondary": "Saka saitunan netwok naka", "settingsChangePasswordPrimary": "<PERSON><PERSON> kalmar shiga", "settingsChangePasswordSecondary": "<PERSON><PERSON> kalmar kulle sikirin naka", "settingsCompleteBetaSurvey": "<PERSON><PERSON>", "settingsDisplayLanguage": "<PERSON><PERSON>", "settingsErrorCannotExportLedgerPrivateKey": "Ba iya aiko da lambar sir<PERSON> ba", "settingsErrorCannotRemoveAllWallets": "<PERSON>ire dukan asusu ba zai yiwu ba", "settingsExportPrivateKey": "<PERSON><PERSON>", "settingsNetworkMainnetBeta": "Mainnet ta Gwaji", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Netwok din RPC a Phantom", "settingsTestNetworks": "Netwok na Gwaji", "settingsUseCustomNetworks": "<PERSON> amfani da Netwok na Asali", "settingsTestnetMode": "<PERSON><PERSON><PERSON>", "settingsTestnetModeDescription": "<PERSON> shafi balans da sadarwar manhaja.", "settingsWebViewDebugging": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "settingsWebViewDebuggingDescription": "<PERSON>ai ba ka damar bincika da kuma gyara matsalar babban shafi na brauzar cikin manhaja.", "settingsTestNetworksInfo": "<PERSON><PERSON><PERSON> kan kowane netwok na Testnet don gwa<PERSON> ne kawai. Ka san cewa tokin na Netwok na Testnet ba ta dauke da kimar kowane kudi.", "settingsEmojis": "<PERSON><PERSON><PERSON><PERSON>", "settingsNoAddresses": "<PERSON> <PERSON><PERSON>", "settingsAddressBookEmptyHeading": "<PERSON> komai a Address Book naka", "settingsAddressBookEmptyText": "Latsa madannar \"+\" ko \"Add Address\" don kara ad<PERSON><PERSON><PERSON> da ka fi so", "settingsEditWallet": "<PERSON><PERSON><PERSON>", "settingsNoTrustedApps": "<PERSON> <PERSON><PERSON><PERSON>", "settingsNoConnections": "<PERSON> haɗewa har yanzu.", "settingsRemoveWallet": "Cire Asusu", "settingsResetApp": "Sake saita manhaja", "settingsBlocked": "An toshe", "settingsBlockedAccounts": "<PERSON><PERSON><PERSON> da aka toshe", "settingsNoBlockedAccounts": "<PERSON> as<PERSON><PERSON> da aka toshe.", "settingsRemoveSecretPhrase": "Cire Jim<PERSON>", "settingsResetAppWithSecretPhrase": "<PERSON>ke saita manhaja da <PERSON>", "settingsResetSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai", "settingsShowSecretRecoveryPhrase": "<PERSON><PERSON> ji<PERSON> sirri na dawo da bayanai", "settingsShowSecretRecoveryPhraseSecondary": "<PERSON><PERSON> da<PERSON> da bayanai", "settingsShowSecretRecoveryPhraseTertiary": "<PERSON><PERSON> da <PERSON>", "settingsTrustedAppsAutoConfirmActiveUntil": "<PERSON><PERSON> <PERSON><PERSON> {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Tabbatarwar-Atomatik", "settingsTrustedAppsDisclaimer": "<PERSON><PERSON> tabba<PERSON>-atomatik a amintattun shafuka kawai", "settingsTrustedAppsLastUsed": "An yi amfani da su {{formattedTimestamp}} da suka wuce", "settingsTrustedAppsPrimary": "Man<PERSON><PERSON>jin Da Ke Haɗe", "settingsTrustedApps": "<PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsRevoke": "Soke", "settingsTrustedAppsRevokeToast": "An zare {{trustedApp}}", "settingsTrustedAppsSecondary": "Seta amintattun manhajojin ka", "settingsTrustedAppsToday": "<PERSON><PERSON>", "settingsTrustedAppsYesterday": "<PERSON><PERSON>", "settingsTrustedAppsLastWeek": "Makon da ya gabata", "settingsTrustedAppsBeforeYesterday": "A baya", "settingsTrustedAppsDisconnectAll": "Zare daga duka", "settingsTrustedAppsDisconnectAllToast": "An zare dukkan <PERSON>", "settingsTrustedAppsEndAutoConfirmForAll": "Kawo ƙarshen tabbatarwar-atomatik ga duka", "settingsTrustedAppsEndAutoConfirmForAllToast": "An kawo karshen lokutan tabbatarwar-atomatik", "settingsSecurityPrimary": "Tsaro & Kare sirri", "settingsSecuritySecondary": "Sabunta saitunan tsaron ka", "settingsActiveNetworks": "Netwok masu aiki", "settingsActiveNetworksAll": "<PERSON><PERSON>", "settingsActiveNetworksSolana": "<PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana ce netwok na asali kuma kullum tana a kunne.", "settingsDeveloperPrimary": "<PERSON><PERSON><PERSON>", "settingsAdvanced": "<PERSON><PERSON>", "settingsTransactions": "<PERSON><PERSON><PERSON> ciniki", "settingsAutoConfirm": "<PERSON><PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "settingsSecurityAnalyticsPrimary": "Faɗi ra'ayi a sirce", "settingsSecurityAnalyticsSecondary": "<PERSON><PERSON> don taimaka mu ingantawa", "settingsSecurityAnalyticsHelper": "Phantom ba ya amfani da bayananka na ainihi don dalilan nazari", "settingsSuspiciousCollectiblesPrimary": "<PERSON><PERSON> hajo<PERSON> da ake tuhuma", "settingsSuspiciousCollectiblesSecondary": "<PERSON><PERSON>, don boye hajoji masu alamar tuhuma", "settingsPreferredBitcoinAddress": "<PERSON><PERSON><PERSON> Bit<PERSON>in da aka fi so", "settingsEnabledAddressesUpdated": "An sabunta adireshin fili!", "settingsEnabledAddresses": "<PERSON><PERSON><PERSON><PERSON> da aka kunna", "settingsBitcoinPaymentAddressForApps": "<PERSON><PERSON><PERSON>", "settingsBitcoinOrdinalsAddressForApps": "<PERSON><PERSON><PERSON> ga <PERSON>", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "<PERSON><PERSON><PERSON> da aka kunna nau'o'in ad<PERSON><PERSON><PERSON> biyu, ga wasu <PERSON> kamar <PERSON>, za a yi amfani da adireshinku na Native Segwit ɗinku don biyan kuɗin sayayya. Za a karɓi kadarorin da aka siya a adireshin ku na Taproot.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "<PERSON><PERSON><PERSON>in na asali na cikin Phantom don tabbatar da daidaito.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(<PERSON><PERSON>)", "settingsPreferredBitcoinAddressTaprootExplainer": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON><PERSON> na <PERSON>aman<PERSON>, yawan<PERSON> tare da caje-caje masu rahusa.", "settingsPreferredExplorers": "Explorer da aka fi so", "settingsPreferredExplorersSecondary": "<PERSON>za zuwa Explorer din blockchain da ka fi so", "settingsCustomGasControls": "Custom Gas Control", "settingsSupportDesk": "<PERSON><PERSON><PERSON>", "settingsSubmitATicket": "<PERSON><PERSON>", "settingsAttachApplicationLogs": "Lika rajistar a<PERSON><PERSON><PERSON> manhaja", "settingsDownloadApplicationLogs": "<PERSON><PERSON>", "settingsDownloadApplicationLogsShort": "<PERSON><PERSON> ayyuka", "settingsDownloadApplicationLogsHelper": "Ta ƙunshi bayanan cikin gida, rah<PERSON><PERSON><PERSON> kasawa da ad<PERSON>oshin walat domin taimakawa wajen warware matsal<PERSON> tallafawar Phantom", "settingsDownloadApplicationLogsWarning": "<PERSON> kunshi bayanan sirri kamar jimlar seed ko mabudin sirri ba.", "settingsWallet": "<PERSON><PERSON><PERSON>", "settingsPreferences": "Fifit<PERSON>", "settingsSecurity": "<PERSON><PERSON>", "settingsDeveloper": "<PERSON> k<PERSON>", "settingsSupport": "<PERSON><PERSON><PERSON>", "settingsWalletShortcutsPrimary": "<PERSON><PERSON> ha<PERSON> ratse na walat a", "settingsAppIcon": "<PERSON><PERSON><PERSON>", "settingsAppIconDefault": "<PERSON><PERSON>", "settingsAppIconLight": "<PERSON><PERSON>", "settingsAppIconDark": "<PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON><PERSON>", "settingsSearchResultSelected": "An zaɓa", "settingsSearchResultExport": "<PERSON><PERSON> da", "settingsSearchResultSeed": "Mabudi", "settingsSearchResultTrusted": "Amintacce", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "<PERSON><PERSON><PERSON>", "settingsSearchResultLogs": "<PERSON><PERSON>", "settingsSearchResultBiometric": "Bayometrik", "settingsSearchResultTouch": "Taba", "settingsSearchResultFace": "Fu<PERSON>", "settingsSearchResultShortcuts": "<PERSON><PERSON><PERSON><PERSON> ha<PERSON>i", "settingsAllSitesPermissionsTitle": "<PERSON> a duk shafuka", "settingsAllSitesPermissionsSubtitle": "<PERSON>a ba ku damar am<PERSON>i da <PERSON> ba tare da tangarda ba da <PERSON> ba tare da danna reshen brauzar ba", "settingsAllSitesPermissionsDisabled": "<PERSON><PERSON><PERSON><PERSON> bai ba da damar canza wannan <PERSON><PERSON> ba", "settingsSolanaCopyTransaction": "<PERSON><PERSON>", "settingsSolanaCopyTransactionDetails": "<PERSON><PERSON><PERSON> tsar<PERSON>ren bayanin ciniki zuwa allon ajiye", "settingsAutoConfirmHeader": "Tabbatarwar-Atomatik", "refreshWebpageToApplyChanges": "<PERSON><PERSON><PERSON> shafin intanet don wanzar da canje-canje", "settingsExperimentalTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsExprimentalSolanaActionsSubtitle": "<PERSON><PERSON> b<PERSON> mafannan <PERSON> lokacin da ya gano muhimman link a X.com", "stakeAccountCardActiveStake": "<PERSON><PERSON> mai aiki", "stakeAccountCardBalance": "Balans", "stakeAccountCardRentReserve": "<PERSON><PERSON><PERSON>a", "stakeAccountCardRewards": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "stakeAccountCardRewardsTooltip": "<PERSON>n shi ne ladar baya-bayan nan da ka samu na jingina. <PERSON> biyan ka duk kwanaki 3.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "<PERSON><PERSON>", "stakeRewardsHistoryTitle": "<PERSON><PERSON><PERSON>", "stakeRewardsActivityItemTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeRewardsHistoryEmptyList": "<PERSON> kyaututtuka", "stakeRewardsTime_zero": "<PERSON><PERSON>", "stakeRewardsTime_one": "<PERSON><PERSON>", "stakeRewardsTime_other": "<PERSON><PERSON><PERSON> {{count}} da suka wuce", "stakeRewardsItemsPagingFailed": "<PERSON><PERSON> tsa<PERSON> lad<PERSON>i bai yiwu ba.", "stakeAccountCreateAndDelegateErrorStaking": "An sami matsalar jinginarwa zuwa ga wannan mai kula da ciniki. <PERSON>na fatan za sake gwadawa.", "stakeAccountCreateAndDelegateSolStaked": "An jinginar da SOL!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "SOL naka zai fara samun kyaututtuka <1></1> nan da yan kwanaki idan asusun jinginar ka ya fara aiki.", "stakeAccountCreateAndDelegateStakingFailed": "<PERSON><PERSON><PERSON><PERSON> bai yiwu ba", "stakeAccountCreateAndDelegateStakingSol": "<PERSON> jing<PERSON> da SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "<PERSON><PERSON> k<PERSON>n as<PERSON> ji<PERSON>, sannan mu wakilta SOL naka ga", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "<PERSON><PERSON> k<PERSON>n as<PERSON>un ji<PERSON>, sannan mu wakilta SOL naka ga {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "<PERSON><PERSON> ciniki", "stakeAccountDeactivateStakeSolUnstaked": "An cire jinginar SOL!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "<PERSON>aka iya cire jinginar ka <1></1> nan da yan kwanaki da zarar asusun jingina ya dena aiki.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Zaka iya ciri jinginarka nan da yan kwanaki da zarar asusun jingina ya daina aiki.", "stakeAccountDeactivateStakeUnstakingFailed": "Cire jingina bai yiwu ba", "stakeAccountDeactivateStakeUnstakingFailedDescription": "An sami matsala wajen cire jingina daga wannan mai kula da ciniki. <PERSON>na fatan zaka sake gwadawa.", "stakeAccountDeactivateStakeUnstakingSol": "Ana cire jinginar SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "<PERSON><PERSON>u fara aikin cire jinginar SOL naka.", "stakeAccountDeactivateStakeViewTransaction": "<PERSON><PERSON> ciniki", "stakeAccountDelegateStakeSolStaked": "An cire jinginar SOL!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "SOL naka zai fara samun kyaututtuka <1></1> nan da yan kwanaki idan asusun jinginar ka ya fara aiki.", "stakeAccountDelegateStakeStakingFailed": "<PERSON><PERSON><PERSON><PERSON> bai yiwu ba", "stakeAccountDelegateStakeStakingFailedDescription": "An sami matsalar jinginarwa zuwa ga wannan mai kula da ciniki. <PERSON>na fatan za sake gwadawa.", "stakeAccountDelegateStakeStakingSol": "<PERSON> jing<PERSON> da SOL...", "stakeAccountDelegateStakeStakingSolDescription": "Zamu wakilta SOL naka.", "stakeAccountDelegateStakeViewTransaction": "<PERSON><PERSON> ciniki", "stakeAccountListActivationActivating": "<PERSON><PERSON><PERSON>", "stakeAccountListActivationActive": "<PERSON><PERSON> aiki", "stakeAccountListActivationInactive": "A rufe", "stakeAccountListActivationDeactivating": "<PERSON><PERSON><PERSON>", "stakeAccountListErrorFetching": "<PERSON><PERSON> iya debo asusun jingina ba. Da fatan zaka sake gwadawa anjima.", "stakeAccountListNoStakingAccounts": "<PERSON> <PERSON><PERSON><PERSON> jing<PERSON>", "stakeAccountListReload": "<PERSON><PERSON> lodawa", "stakeAccountListViewPrimaryText": "Jing<PERSON><PERSON>", "stakeAccountListViewStakeSOL": "<PERSON><PERSON><PERSON>", "stakeAccountListItemStakeFee": "{{fee}} caji", "stakeAccountViewActionButtonRestake": "<PERSON><PERSON> jing<PERSON>", "stakeAccountViewActionButtonUnstake": "Cire jingina", "stakeAccountViewError": "<PERSON><PERSON><PERSON>", "stakeAccountViewPrimaryText": "Jing<PERSON><PERSON>", "stakeAccountViewRestake": "<PERSON><PERSON> jing<PERSON>", "stakeAccountViewSOLCurrentlyStakedInterpolated": "An jinginar da SOL naka wajen mai kula da ciniki. <PERSON><PERSON> bukaci cire jingina zuwa <1></1> don samun kuɗaɗen.<3><PERSON><PERSON><PERSON> karin sani</3>", "stakeAccountViewStakeInactive": {"part1": "Wannan jingina bata aiki. <PERSON><PERSON> shaw<PERSON>r cire jinginar ko ka nemi mai kula da ciniki da zaka wakilta wa.", "part2": "<PERSON><PERSON><PERSON> karin sani"}, "stakeAccountViewStakeNotFound": "Ba a ga asusun wannan jingina ba.", "stakeAccountViewViewOnExplorer": "<PERSON><PERSON> a {{explorer}}", "stakeAccountViewWithdrawStake": "Cire jingina", "stakeAccountViewWithdrawUnstakedSOL": "Cire SOL da ba jingina ba", "stakeAccountInsufficientFunds": "Ba isasshen SOL da za a iya cire jingina ko fitar da kuɗi.", "stakeAccountWithdrawStakeSolWithdrawn": "An cire SOL!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "An cire SOL naka.", "part2": "Za cire wannan asusun jingina kai tsaye nan da yan mintina."}, "stakeAccountWithdrawStakeViewTransaction": "<PERSON><PERSON> ciniki", "stakeAccountWithdrawStakeWithdrawalFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "An sami matsalar cirewa daga wannan as<PERSON>un jingina. <PERSON>na fatan zaka sake gwadawa.", "stakeAccountWithdrawStakeWithdrawingSol": "Ana cire SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Muna kan cire SOL naka daga wannan asusun jingina.", "startEarningSolAccount": "<PERSON><PERSON>u", "startEarningSolAccounts": "<PERSON><PERSON>u", "startEarningSolErrorClosePhantom": "<PERSON>sa nan sannan ka sake gwadawa", "startEarningSolErrorTroubleLoading": "An sami matsala wajen loda jingina", "startEarningSolLoading": "<PERSON><PERSON> lo<PERSON>...", "startEarningSolPrimaryText": "Fara moran S<PERSON>", "startEarningSolSearching": "<PERSON> bincika asusun jingina", "startEarningSolStakeTokens": "<PERSON><PERSON><PERSON> da tokin don moran kyaut<PERSON>uka", "startEarningSolYourStake": "Jing<PERSON><PERSON>", "unwrapFungibleTitle": "<PERSON> musanya zuwa {{tokenSymbol}}", "unwrapFungibleDescription": "Cire daga {{fromToken}} domin {{toToken}}", "unwrapFungibleConfirmSwap": "<PERSON><PERSON><PERSON> da musanya", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "<PERSON><PERSON><PERSON> kuɗi", "swapFeesFees": "Caje-caje", "swapFeesPhantomFee": "<PERSON><PERSON>", "swapFeesPhantomFeeDisclaimer": "<PERSON>llum mukan nemi farashi mafi kyau da yake samuwa daga manyan dillalai. <PERSON> lissafa ladan {{feePercentage}} kai tsaye cikin farashin.", "swapFeesRate": "<PERSON><PERSON>", "swapFeesRateDisclaimer": "<PERSON><PERSON> k<PERSON> ƙimar da Jupiter Aggregator ya samo a masu canji mabanbanta.", "swapFeesRateDisclaimerMultichain": "<PERSON><PERSON> k<PERSON>n <PERSON> da aka samu a kasuwan<PERSON> wurare mabanbanta.", "swapFeesPriceImpact": "<PERSON><PERSON><PERSON>", "swapFeesHighPriceImpact": "<PERSON><PERSON><PERSON>", "swapFeesPriceImpactDisclaimer": "Banban<PERSON> tsakanin farashin kasuwa da farashin-kiyasi bisa la'akari da girman cinikin ka.", "swapFeesSlippage": "Gocewa", "swapFeesHighSlippage": "<PERSON><PERSON><PERSON>", "swapFeesHighSlippageDisclaimer": "Cinikin<PERSON> ba zai yiwu ba in farashin ya canza a rashin sa'a fiye da {{slippage}}%.", "swapTransferFee": "<PERSON><PERSON><PERSON>", "swapTransferFeeDisclaimer": "Cinikin ${{symbol}} na dauke da {{feePercent}}% na cajin transfa da wanda ya kirkiri tokin ya saka, ba Phantom ba.", "swapTransferFeeDisclaimerMany": "C<PERSON>kin tokin da aka zaba na dauke da {{feePercent}}% na cajin transfa da wanda ya kirkiri tokin ya saka, ba Phantom ba.", "swapFeesSlippageDisclaimer": "An ba da yawan ka<PERSON>war farashin ciniki daga na tayi.", "swapFeesProvider": "<PERSON><PERSON><PERSON>", "swapFeesProviderDisclaimer": "<PERSON><PERSON> canji mabanbanta kan karasa maka ciniki.", "swapEstimatedTime": "<PERSON><PERSON><PERSON>", "swapEstimatedTimeShort": "<PERSON><PERSON><PERSON>", "swapEstimatedTimeDisclaimer": "<PERSON><PERSON><PERSON> kammala musayar zai bambanta domin dalilai da yawa da suke shafiar saurin ciniki.", "swapSettingsButtonCommand": "<PERSON><PERSON>", "swapQuestionRetry": "Sake <PERSON>?", "swapUnverifiedTokens": "Tokin da ba a tantwce su ba", "swapSectionTitleTokens": "{{section}} <PERSON><PERSON>", "swapFlowYouPay": "Ka biya", "swapFlowYouReceive": "<PERSON> karba", "swapFlowActionButtonText": "Sake duba oda", "swapAssetCardTokenNetwork": "{{symbol}} a {{network}}", "swapAssetCardMaxButton": "<PERSON><PERSON>", "swapAssetCardSelectTokenAndNetwork": "Zaɓi <PERSON><PERSON> da <PERSON>", "swapAssetCardBuyTitle": "Ka karɓi", "swapAssetCardSellTitle": "Ka biya", "swapAssetWarningUnverified": "Ba a tantance wannan tokin ba. Yi mu'amala da tokin da ka yarda da su kaɗai.", "swapAssetWarningPermanentDelegate": "<PERSON>i wakili zai iya kone ko transfa na wadannan tokintokin har abada.", "swapSlippageSettingsTitle": "<PERSON><PERSON><PERSON>", "swapSlippageSettingsSubtitle": "Cinikinka ba zai yiwu ba in farashin ya canza fiye da gocewa. In adadi ya yi sama da yawa za a sami rashin sa'a a ciniki.", "swapSlippageSettingsCustom": "<PERSON><PERSON><PERSON>", "swapSlippageSettingsHighSlippageWarning": "<PERSON><PERSON> yiwu a murda cinikinka kuma zai aifar da ciniki mara sa'a.", "swapSlippageSettingsCustomMinError": "Da fatan za ka shigar da adadin sama da {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "Da fatan za ka shigar da adadi ƙasa da {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "Da fatan za a shigar da ingantaccen ƙima.", "swapSlippageSettingsAutoSubtitle": "Phantom zai nemi gocewa mafi ƙaranci don musaya cikin nasara.", "swapSlippageSettingsAuto": "Atomatik", "swapSlippageSettingsFixed": "<PERSON> gyara", "swapSlippageOptInTitle": "<PERSON><PERSON><PERSON>", "swapSlippageOptInSubtitle": "Phantom zai sami mafi ƙarancin gocewa do musanya cikin nasara. Zaka iya canza wannan kowani lokaci a Swapper. → Saitunan Gocewa.", "swapSlippageOptInEnableOption": "<PERSON><PERSON>", "swapSlippageOptInRejectOption": "Cigaba da kayyadadden Gocewa", "swapQuoteFeeDisclaimer": "<PERSON><PERSON>n ya kunshi {{feePercentage}} na kudin Phantom", "swapQuoteMissingContext": "Ba ƙiyasta farashin musanya ba", "swapQuoteErrorNoQuotes": "Ana ƙoƙarin musanya ba tare da farashi ba", "swapQuoteSolanaNetwork": "Netwok ta Solana", "swapQuoteNetwork": "Netwok", "swapQuoteOneTimeSerumAccount": "<PERSON><PERSON><PERSON>um na sa hannu lokaci-guda", "swapQuoteOneTimeTokenAccount": "<PERSON><PERSON><PERSON> tokin sa-hannu lokaci-guda", "swapQuoteBridgeFee": "<PERSON><PERSON><PERSON>", "swapQuoteDestinationNetwork": "Netwok ns Destination", "swapQuoteLiquidityProvider": "Mai samar da kuɗin ciniki", "swapReviewFlowActionButtonPrimary": "Musanya", "swapReviewFlowPrimaryText": "Sake duba oda", "swapReviewFlowYouPay": "Ka biya", "swapReviewFlowYouReceive": "<PERSON> karba", "swapReviewInsufficientBalance": "<PERSON> <PERSON><PERSON><PERSON> kuɗaɗe", "ugcSwapWarningTitle": "Gargadi", "ugcSwapWarningBody1": "A Tokin launcher {{programName}}.", "ugcSwapWarningBody2": "Ƙimar waɗannan tokin na iya canzawa da sauri, wanda zai iya zama babban riba ko asara. Da fatan za ka kula da asara in kana ciniki.", "ugcSwapWarningConfirm": "<PERSON> fahimta", "bondingCurveProgressLabel": "Cigaban Bonding Curve", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "A tsarin bonding curve surar curve ne ke nuna yadda za sa farashi, yakan karu san da sayayya ya karu da raguwa san da sayayya ya ragu. In an gama sayar da tokin, za a ajiye duk kuɗaɗen a Raydium sannan a kona.", "ugcFungibleWarningBanner": "Ana cinikin wannan tokin a {{programName}}", "ugcCreatedRowLabel": "An Kirkira A", "ugcStatusRowLabel": "<PERSON><PERSON><PERSON>", "ugcStatusRowValue": "<PERSON> kammala", "swapTxConfirmationReceived": "An karba!", "swapTxConfirmationSwapFailed": "<PERSON><PERSON> bai yiwu ba", "swapTxConfirmationSwapFailedStaleQuota": "<PERSON><PERSON>n ya wuce. Da fatan za a sake gwadawa.", "swapTxConfirmationSwapFailedSlippageLimit": "Gocewarka ta yi ƙasa sosai don wannan musanya. Da fatan za a ƙara zgocewarka a saman allon musanya sannan a sake gwadawa.", "swapTxConfirmationSwapFailedInsufficientBalance": "Ba mu iya kammala wannan bukata ba. Ba ka da is<PERSON>hen balans na kammala wannan ciniki.", "swapTxConfirmationSwapFailedEmptyRoute": "<PERSON><PERSON> wannan gwamin tokin ya canza. Ba mu iya samun farashin da ya dace ba. Da fatan za a sake gwadawa ko ka canza yawan tokin.", "swapTxConfirmationSwapFailedAcountFrozen": "Ba za ka iya aika ko musayar wannan tokin ba.", "swapTxConfirmationSwapFailedTryAgain": "<PERSON><PERSON> bai yiwu ba, sake gwadawa", "swapTxConfirmationSwapFailedUnknownError": "<PERSON><PERSON> iya kammala musanyar ba. Ba abinda ya shafi kuɗaɗenka. Da fatan za ka sake gwadawa. ", "swapTxConfirmationSwapFailedSimulationTimeout": "<PERSON>u iya gwajin musanyar ba. Ba abinda ya sami kuɗaɗenka. Da fatan za ka sake gwadawa.", "swapTxConfirmationSwapFailedSimulationUnknownError": "<PERSON><PERSON> iya kammala musanyar ba. Ba abu da ya shafi kuɗadenka. Da fatan za a sake gwadawa. ", "swapTxConfirmationSwapFailedInsufficientGas": "Asusunka ba shi da isassun kuɗaɗe da za a iya kammala wannan ciniki. Da fatan za ka ƙara kuɗi a asusunka sannan ka sake gwadawa.", "swapTxConfirmationSwapFailedLedgerReject": "<PERSON> amfani yaƙi karbar musanyar a na'urar hadwaya.", "swapTxConfirmationSwapFailedLedgerConnectionError": "An hana musanyar saboda matsalar sadarwar na'u<PERSON>ri. Da fatan zaka sake gwadawa.", "swapTxConfirmationSwapFailedLedgerSignError": "An ƙi musanyawa saboda kuskuren alamar na'urar. Da fatan za a sake gwadawa.", "swapTxConfirmationSwapFailedLedgerError": "An hana musanyar saboda matsalar na'ura. Da fatan zaka sake gwadawa.", "swapTxConfirmationSwappingTokens": "<PERSON> musanyan tokin...", "swapTxConfirmationTokens": "<PERSON><PERSON>", "swapTxConfirmationTokensDeposited": "An gama! An saka tokin cikin walat naka", "swapTxConfirmationTokensDepositedTitle": "An gama!", "swapTxConfirmationTokensDepositedBody": "An saka tokin a walat dinka", "swapTxConfirmationTokensWillBeDeposited": "za saka a walat naka da zarar cinikin ya kammala", "swapTxConfirmationViewTransaction": "<PERSON><PERSON> ciniki", "swapTxBridgeSubmitting": "<PERSON> mika ciniki", "swapTxBridgeSubmittingDescription": "Musayar {{sellAmount}} a {{sellNetwork}} a kan {{buyAmount}} a {{buyNetwork}}", "swapTxBridgeFailed": "Mika ciniki ta gaza", "swapTxBridgeFailedDescription": "Ba mu iya kammala mika bukata ba.", "swapTxBridgeSubmitted": "An mika ciniki", "swapTxBridgeSubmittedDescription": "<PERSON><PERSON><PERSON> c<PERSON> {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON><PERSON> iya sallamar wannan window cikin aminci.", "swapperSwitchTokens": "<PERSON><PERSON> to<PERSON>", "swapperMax": "<PERSON><PERSON>", "swapperTooltipNetwork": "Netwok", "swapperTooltipPrice": "<PERSON><PERSON>", "swapperTooltipAddress": "Kwang<PERSON>", "swapperTrendingSortBy": "Ware Da", "swapperTrendingTimeFrame": "<PERSON><PERSON><PERSON>", "swapperTrendingNetwork": "Netwok", "swapperTrendingRank": "<PERSON><PERSON>", "swapperTrendingTokens": "<PERSON><PERSON>", "swapperTrendingVolume": "<PERSON><PERSON>", "swapperTrendingPrice": "<PERSON><PERSON>", "swapperTrendingPriceChange": "<PERSON><PERSON> farashi", "swapperTrendingMarketCap": "<PERSON><PERSON>", "swapperTrendingTimeFrame1h": "Hawa1", "swapperTrendingTimeFrame24h": "Hawa24", "swapperTrendingTimeFrame7d": "Kwana7", "swapperTrendingTimeFrame30d": "Kwana30", "swapperTrendingNoTokensFound": "Ba a ga kowane tokin ba.", "switchToggle": "Toggle", "termsOfServiceActionButtonAgree": "Na yarda", "termsOfServiceDisclaimerFeesDisabledInterpolated": "In ka danna <1>\"Na Amince\"</1> ka karbi <3><PERSON><PERSON><PERSON> da Sharuɗɗa</3> na musanya tokin da Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Mun gyara <PERSON> ayyukanmu. In ka danna <1>\"Na Amince\"</1> ka karbi sabbin <3>Sharuɗɗan ayyukanmu</3>.<5></5><6></6>Sabbin Sharuɗɗan ayyukanmu sun haɗa da sabon <8>tsarin kuɗi</8> ga wasu kayayyaki.", "termsOfServicePrimaryText": "Dokokin aiki", "tokenRowUnknownToken": "Tokin da ba sani ba", "transactionsAppInteraction": "Hulɗar manhaja", "transactionsFailedAppInteraction": "Hulɗar manhaja da bai yiwu ba", "transactionsBidOnInterpolated": "Yi tayin {{name}}", "transactionsBidFailed": "<PERSON><PERSON> bai yiwu ba", "transactionsBoughtInterpolated": "An sayi {{name}}", "transactionsBoughtCollectible": "<PERSON><PERSON><PERSON> masu ƙima da aka saya", "transactionBridgeInitiated": "An fara musaya", "transactionBridgeInitiatedFailed": "Fara Musaya ta gaza", "transactionBridgeStatusLink": "Dubi Mataki a LI.FI", "transactionsBuyFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "transactionsBurnedSpam": "<PERSON><PERSON> banza da aka kone", "transactionsBurned": "An kone", "transactionsUnwrapped": "An cire jingina", "transactionsUnwrappedFailed": "Cire jingina bai yiwu ba", "transactionsCancelBidOnInterpolated": "Soke tayi a {{name}}", "transactionsCancelBidOnFailed": "<PERSON>ke tayi bai yiwu ba", "transactionsError": "<PERSON><PERSON><PERSON>", "transactionsFailed": "<PERSON> yiwu ba", "transactionsSwapped": "An musanya", "transactionsFailedSwap": "<PERSON><PERSON> bai yiwu ba", "transactionsFailedBurn": "<PERSON><PERSON><PERSON> bai yiwu ba", "transactionsFrom": "Daga", "transactionsListedInterpolated": "An jera {{name}}", "transactionsListedFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "transactionsNoActivity": "<PERSON> aiki", "transactionsReceived": "<PERSON> karba", "transactionsReceivedInterpolated": "An karba SOL {{amount}}", "transactionsSending": "<PERSON> a<PERSON>...", "transactionsPendingCreateListingInterpolated": "<PERSON> ƙirƙirar {{name}}", "transactionsPendingEditListingInterpolated": "<PERSON> gyara {{name}}", "transactionsPendingSolanaPayTransaction": "<PERSON><PERSON><PERSON> da cinikin Solana Pay", "transactionsPendingRemoveListingInterpolated": "Ana cire {{name}}", "transactionsPendingBurningInterpolated": "<PERSON> kona {{name}}", "transactionsPendingSending": "<PERSON>", "transactionsPendingSwapping": "<PERSON> musanya", "transactionsPendingBridging": "<PERSON><PERSON>", "transactionsPendingApproving": "<PERSON><PERSON>cewa", "transactionsPendingCreatingAndDelegatingStake": "<PERSON> ƙirƙira da wakilta jingina", "transactionsPendingDeactivatingStake": "Ana rufe jingina", "transactionsPendingDelegatingStake": "<PERSON> wakiltar jingina", "transactionsPendingWithdrawingStake": "Ana cire jingina", "transactionsPendingAppInteraction": "<PERSON> jiran hulɗar manhaja", "transactionsPendingBitcoinTransaction": "Cinikin BTC Mai Jira", "transactionsSent": "An aika", "transactionsSendFailed": "<PERSON><PERSON> bai yiwu ba", "transactionsSwapOn": "Yi musanya a {{dappName}}", "transactionsSentInterpolated": "An aika SOL {{amount}}", "transactionsSoldInterpolated": "An sayar da {{name}}", "transactionsSoldCollectible": "<PERSON><PERSON><PERSON> masu ƙima da aka sayar", "transactionsSoldFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "transactionsStaked": "<PERSON> jinginar", "transactionsStakedFailed": "<PERSON><PERSON><PERSON><PERSON> bai yiwu ba", "transactionsSuccess": "<PERSON><PERSON><PERSON>", "transactionsTo": "<PERSON><PERSON><PERSON>", "transactionsTokenSwap": "<PERSON><PERSON><PERSON>", "transactionsUnknownAmount": "<PERSON> ba a sani ba", "transactionsUnlistedInterpolated": "An cire {{name}} daga jeri", "transactionsUnstaked": "An cire daga jingina", "transactionsUnlistedFailed": "Cire daga jerawa bai yiwu ba", "transactionsDeactivateStake": "<PERSON><PERSON><PERSON><PERSON> da aka rufe", "transactionsDeactivateStakeFailed": "<PERSON><PERSON><PERSON> jing<PERSON> bai yiwu ba", "transactionsWaitingForConfirmation": "<PERSON> jiran am<PERSON>wa", "transactionsWithdrawStake": "Cire jingina", "transactionsWithdrawStakeFailed": "<PERSON><PERSON><PERSON> daga jingina bai yiwu ba", "transactionCancelled": "An soke", "transactionCancelledFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "transactionApproveToken": "An yarda {{tokenSymbol}}", "transactionApproveTokenFailed": "<PERSON><PERSON><PERSON> da {{tokenSymbol}} bai yiwu ba", "transactionApprovalFailed": "<PERSON><PERSON><PERSON><PERSON> bai yiwu ba", "transactionRevokeApproveToken": "An soke {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "Soke {{tokenSymbol}} bai yiwu ba", "transactionRevokeFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "transactionApproveDetailsTitle": "<PERSON><PERSON><PERSON>", "transactionCancelOrder": "<PERSON><PERSON><PERSON>", "transactionCancelOrderFailed": "<PERSON><PERSON><PERSON> sokewa ya kasa", "transactionApproveAppLabel": "<PERSON><PERSON><PERSON>", "transactionApproveAmountLabel": "<PERSON><PERSON>", "transactionApproveTokenLabel": "<PERSON><PERSON>", "transactionApproveCollectionLabel": "<PERSON><PERSON><PERSON>", "transactionApproveAllItems": "<PERSON><PERSON><PERSON> da duk abubuwa", "transactionSpendUpTo": "<PERSON><PERSON> k<PERSON>", "transactionCancel": "<PERSON>ke ciniki", "transactionPrioritizeCancel": "<PERSON><PERSON><PERSON> sokewa", "transactionSpeedUp": "Gaggauta ciniki", "transactionCancelHelperText": "<PERSON><PERSON><PERSON> asali zai iya kammala kafin a soke shi.", "transactionSpeedUplHelperText": "<PERSON>n zai gaggauta cinikinka saboda da yanayin netwok.", "transactionCancelHelperMobile": "Za a biya <1>abinda ya kai {{amount}}</1> a ƙoƙarin soke wannan ciniki. <PERSON><PERSON><PERSON> asali zai iya kammala kafin a soke ta.", "transactionCancelHelperMobileWithEstimate": "Za a caji <1>kimanin {{amount}}</1> in ana so a soke wannan ciniki. Zai kammala cikin {{timeEstimate}}. <PERSON><PERSON><PERSON> asali zai iya kammala kafin a soke shi.", "transactionSpeedUpHelperMobile": "Za a caji <1>kimanin {{amount}}</1> don gag<PERSON>ta wannan ciniki.", "transactionSpeedUpHelperMobileWithEstimate": "Za a caji <1>kimanin {{amount}}</1> don gag<PERSON>ta wannan cinikin. <PERSON>ai kammala nan da {{timeEstimate}}.", "transactionEstimatedTime": "<PERSON><PERSON><PERSON>", "transactionCancelingSend": "An aika sokewa", "transactionPrioritizingCancel": "An fifita sokewa", "transactionCanceling": "Sokewa", "transactionReplaceError": "An sami kuskure. Ba a caji asusun ka ba. Za ka iya sake gwadawa.", "transactionNotEnoughNative": "<PERSON> is<PERSON>hen {{nativeTokenSymbol}}", "transactionGasLimitError": "Ba a iya kimanta iyakar gas ba", "transactionGasEstimationError": "Ba a iya kimanta gas ba", "pendingTransactionCancel": "Soke", "pendingTransactionSpeedUp": "Gaggauta", "pendingTransactionStatus": "<PERSON><PERSON><PERSON>", "pendingTransactionPending": "<PERSON> jira", "pendingTransactionPendingInteraction": "<PERSON> jiran hulɗa", "pendingTransactionCancelling": "Sokewa", "pendingTransactionDate": "<PERSON><PERSON><PERSON> wata", "pendingTransactionNetworkFee": "<PERSON><PERSON><PERSON>", "pendingTransactionEstimatedTime": "<PERSON><PERSON><PERSON>", "pendingTransactionEstimatedTimeHM": "h{{hours}} m{{minutes}}", "pendingTransactionEstimatedTimeMS": "m{{minutes}} s{{seconds}}", "pendingTransactionEstimatedTimeS": "s{{seconds}}", "pendingTransactionsSendingTitle": "<PERSON> {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "<PERSON> ba a sani ba", "pendingTransactionUnknownApp": "<PERSON><PERSON><PERSON> da ba a sani ba", "permanentDelegateTitle": "An wakiltar", "permanentDelegateValue": "<PERSON><PERSON><PERSON>", "permanentDelegateTooltipTitle": "Wakiltawa ta Dindindin", "permanentDelegateTooltipValue": "<PERSON><PERSON><PERSON><PERSON> na ba wani asusu kula da tokin a madadi<PERSON>, wannan ya kunshi konewa da yin transfa.", "unlockActionButtonUnlock": "<PERSON><PERSON>", "unlockEnterPassword": "<PERSON><PERSON> da kalmar shiga", "unlockErrorIncorrectPassword": "<PERSON><PERSON><PERSON> shiga ba daidai ba", "unlockErrorSomethingWentWrong": "An sami matsala, sake gwadawa anjima", "unlockForgotPassword": "An manta da kalmar shiga", "unlockPassword": "<PERSON><PERSON><PERSON> shiga", "forgotPasswordText": "Zaka iya sake saita kalmar shiga ta hanyar shigar da kalmomi 12-24 na jumlar dawo da kalmar shiga. Phantom ba zai iya dawo maka da kalmar shiga ba.", "appInfo": "<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON><PERSON><PERSON>", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "<PERSON> a tare da asusun haɗawaya.", "trustedAppAutoConfirmDisclaimer1": "A yayin aiki, <PERSON> zai tabbatar da kowane bukata daga wannan manhajar ba tare da sanar maka ko neman tabbatarwa ba.", "trustedAppAutoConfirmDisclaimer2": "<PERSON><PERSON><PERSON> zai sanya kuɗinka cikin hatsarin dam<PERSON>. <PERSON> amfani da wannan tsari da manhajojin da ka aminta da su kawai.", "validationUtilsPasswordIsRequired": "<PERSON> bukatar kalmar shiga", "validationUtilsPasswordLength": "<PERSON><PERSON> kalmar shiga ta yi tsawon harufa 8", "validationUtilsPasswordsDontMatch": "<PERSON><PERSON><PERSON><PERSON> shiga basu yi kama ba", "validationUtilsPasswordCantBeSame": "Ba za ka iya amfani da tsohon kalmar shigar ka ba", "validatorCardEstimatedApy": "<PERSON><PERSON><PERSON>", "validatorCardCommission": "<PERSON><PERSON><PERSON>", "validatorCardTotalStake": "<PERSON><PERSON><PERSON><PERSON><PERSON> jing<PERSON>", "validatorCardNumberOfDelegators": "# na <PERSON><PERSON><PERSON>", "validatorListChooseAValidator": "Zaɓi mai kula da ciniki", "validatorListErrorFetching": "<PERSON><PERSON> iya debo masu kula da ciniki ba. Da fatan zaka sake gwadawa anjima.", "validatorListNoResults": "Ba Sakamako", "validatorListReload": "<PERSON><PERSON> lodawa", "validatorInfoTooltip": "<PERSON> kula da ciniki", "validatorInfoTitle": "<PERSON><PERSON> kula da ciniki", "validatorInfoDescription": "Da zarar ka jinginar da SOL dinka ga mai kula da ciniki a netwok na Solana, zaka rinka samun ladar SOL a maimakon haka.", "validatorApyInfoTooltip": "Kiyasin. APY", "validatorApyInfoTitle": "<PERSON><PERSON><PERSON>", "validatorApyInfoDescription": "<PERSON>n shi ne kimar lada da ka samu na jinginar da SOL ga mai kula da cinikin.", "validatorViewActionButtonStake": "<PERSON><PERSON>", "validatorViewErrorFetching": "Ba iya debo masu kula da ciniki ba.", "validatorViewInsufficientBalance": "<PERSON> is<PERSON><PERSON> balans", "validatorViewMax": "<PERSON><PERSON>", "validatorViewPrimaryText": "<PERSON><PERSON>", "validatorViewDescriptionInterpolated": "Zaɓi SOL nawa kake so ka jinginar <1></1> da wannan mai kula da ciniki. <3>Ƙara koyo</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "<PERSON> bukatar SOL {{amount}} kafin a yi jingina", "validatorViewValidator": "<PERSON> kula da ciniki", "walletMenuItemsAddConnectWallet": "<PERSON> / haɗa walat", "walletMenuItemsBridgeAssets": "<PERSON><PERSON><PERSON><PERSON> musanya", "walletMenuItemsHelpAndSupport": "Taimako & Tallafi", "walletMenuItemsLockWallet": "<PERSON><PERSON>", "walletMenuItemsResetSecretPhrase": "Sake seta jimlar sirri", "walletMenuItemsShowMoreAccounts": "<PERSON><PERSON> {{count}}...", "walletMenuItemsHideAccounts": "<PERSON><PERSON>", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "<PERSON><PERSON><PERSON>", "disableMultiChainDetail1Header": "Jeka bakiɗaya a Solana", "disableMultiChainDetail1SecondaryText": "<PERSON><PERSON> da asusu, tokin da kayayyaki ba tare da ka ga wasu chain ba.", "disableMultiChainDetail2Header": "<PERSON><PERSON> Multichain kowane lokaci", "disableMultiChainDetail2SecondaryText": "Za a adana balans naka da ke Ethereum da Polygon lokacin da sake buɗe Multichain.", "disableMultiChainButton": "<PERSON><PERSON>", "disabledMultiChainHeader": "<PERSON><PERSON> aka kunna", "disabledMultiChainText": "Za ka iya sake bude Multichain kowane lokaci.", "enableMultiChainHeader": "<PERSON><PERSON>in", "enabledMultiChainHeader": "<PERSON> kunna Multichain", "enabledMultiChainText": "Ethereum da Polygon suna aiki da walat dinka.", "incompatibleAccountHeader": "<PERSON><PERSON><PERSON> bai dace ba", "incompatibleAccountInterpolated": "Da fatan za ka cire wannan asusun <PERSON>-kadai kafin bude yanayin asusun <PERSON>-Kadai <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Me ake ciki!", "welcomeToMultiChainPrimaryText": "Walat guda mai komai", "welcomeToMultiChainDetail1Header": "Sashen taimako na Ethereum da Polygon", "welcomeToMultiChainDetail1SecondaryText": "<PERSON>k tokin naka da NFTs daga Solana, Ethereum, da Polygon a wuri guda.", "welcomeToMultiChainDetail2Header": "<PERSON> amfani da duk manhajojin da ka ke kauna", "welcomeToMultiChainDetail2SecondaryText": "Haɗu da manhajoji a chain masu yawa ba tare da canja netwok ba.", "welcomeToMultiChainDetail3Header": "<PERSON><PERSON> da walat dinka na MetaMask", "welcomeToMultiChainDetail3SecondaryText": "<PERSON>go da duk jimlolin sirri naka tsakanin Ethereum da Polygon cikin sauki.", "welcomeToMultiChainIntro": "<PERSON><PERSON> da zuwa Phantom Multichain", "welcomeToMultiChainIntroDesc": "<PERSON>k tokin naka da NFTs daga Solana, Ethereum, da Polygon a wuri guda. Walat dinka guda don kowane harka.", "welcomeToMultiChainAccounts": "An sake tsara <PERSON>", "welcomeToMultiChainAccountsDesc": "A sabon tsarin multichain, kowane asusu yanzu ya na da makamantan adiresoshi a ETH da Polygon.", "welcomeToMultiChainApps": "<PERSON>a aiki a <PERSON>a", "welcomeToMultiChainAppsDesc": "Phantom ya dace da kowace manhaja a kan Ethereum, Polygon, da Solana. <PERSON><PERSON> \"Haɗa zuwa MetaMask\" kuma ka shirya kenan.", "welcomeToMultiChainImport": "<PERSON><PERSON> daga <PERSON>, nan take", "welcomeToMultiChainImportDesc": "<PERSON><PERSON> da jimlar sirri naka ko mabudin sirri daga walat kamar <PERSON> ko walat din Coinbase. Duk a wuri guda.", "welcomeToMultiChainImportInterpolated": "<0> <PERSON><PERSON> da jimlar sirri naka </0> ko mabudin sirri daga walat kamar MetaMask ko walat din Coinbase. Duk a wuri guda.", "welcomeToMultiChainTakeTour": "Ka yi zagayen", "welcomeToMultiChainSwapperTitle": "Yin musanya a Ethereum, Polygon, & Solana", "welcomeToMultiChainSwapperDetail1Header": "Taimakon Ethereum da Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "<PERSON><PERSON> za ka iya musanyar tokin na ERC-20 daga walat dinka cikin sauki.", "welcomeToMultiChainSwapperDetail2Header": "<PERSON><PERSON> da suka fi kyau da caji masu sauki sosai", "welcomeToMultiChainSwapperDetail2SecondaryText": "Kafofi 100+ na sauƙin musanya da kulla ciniki cikin sauri don samun riba masu yawa.", "networkErrorTitle": "<PERSON><PERSON><PERSON>", "networkError": "<PERSON><PERSON> ta<PERSON> bamu sami <PERSON> ba. Da fatan za ka sake gwadawa anjima.", "authenticationUnlockPhantom": "Bude Phantom", "errorAndOfflineSomethingWentWrong": "An sami matsala", "errorAndOfflineSomethingWentWrongTryAgain": "Da fatan za sake gwadawa.", "errorAndOfflineUnableToFetchAssets": "<PERSON><PERSON> iya debo kadarori ba. Da fatan za sake gwadawa.", "errorAndOfflineUnableToFetchCollectibles": "<PERSON><PERSON> iya debo masu kula da ciniki ba. Da fatan zaka sake gwadawa.", "errorAndOfflineUnableToFetchSwap": "Ba mu iya debo bayanan musanya ba. Da fatan zaka sake gwadawa anjima.", "errorAndOfflineUnableToFetchTransactionHistory": "Ba mu iya samun tarihin cinikinka ba yanzu. <PERSON>a netwok na sadarwarka, ko ka sake gwadawa anjima.", "errorAndOfflineUnableToFetchRewardsHistory": "<PERSON>mu iya debo tarihin lada ba. Da fatan zaka sake gwadawa anjima.", "errorAndOfflineUnableToFetchBlockedUsers": "<PERSON><PERSON> iya debi masu amfani da aka toshe ba. Da fatan zaka sake gwadawa anjima.", "networkHealthSheetCloseButtonText": "To", "swapReviewError": "An sami matsala lokacin sake duba odar ka, da fatan zaka sake gwadawa.", "sendSelectToken": "Zaɓi Tokin", "swapBalance": "Balans:", "swapTitle": "<PERSON> musanyan <PERSON>", "swapSelectToken": "Zaɓi Tokin", "swapYouPay": "Ka Biya", "swapYouReceive": "<PERSON>", "aboutPrivacyPolicy": "<PERSON><PERSON><PERSON> kare sirri", "aboutVersion": "<PERSON><PERSON><PERSON> {{version}}", "aboutVisitWebsite": "<PERSON><PERSON><PERSON><PERSON> shafin in<PERSON>et", "bottomSheetConnectTitle": "Haɗa", "A11YbottomSheetConnectTitle": "Haɗin <PERSON><PERSON><PERSON>", "A11YbottomSheetCommandClose": "<PERSON>n Haɗin <PERSON><PERSON><PERSON>", "A11YbottomSheetCommandBack": "<PERSON><PERSON>", "bottomSheetSignTypedDataTitle": "Sa hannu a sako", "bottomSheetSignMessageTitle": "Sa hannu a sako", "bottomSheetSignInTitle": "Shiga", "bottomSheetSignInAndConnectTitle": "Shiga", "bottomSheetConfirmTransactionTitle": "<PERSON><PERSON><PERSON> da ciniki", "bottomSheetConfirmTransactionsTitle": "<PERSON><PERSON><PERSON> da ciniki", "bottomSheetSolanaPayTitle": "<PERSON><PERSON>tar B<PERSON> ta <PERSON>ana", "bottomSheetAdvancedTitle": "An gabatar", "bottomSheetReadOnlyAccountTitle": "<PERSON><PERSON>", "bottomSheetTransactionSettingsTitle": "<PERSON><PERSON><PERSON>", "bottomSheetConnectDescription": "Haɗiewa zai ba wannan sha<PERSON> i<PERSON>in duba balans da ayyukan asusun da aka zaɓa.", "bottomSheetSignInDescription": "Sa hannu a wannan sako zai tabbatar cewa kana da mallakin asusun da aka zaba. Sa hannu kawai a sakonni daga Man<PERSON>jin da ka amince da su.", "bottomSheetSignInAndConnectDescription": "<PERSON>in<PERSON><PERSON> zai ba wannan shafin intanet izinin ganin balans da harkokin asusun da aka zaɓa.", "bottomSheetConfirmTransactionDescription": "An ƙiyasta canje-canjen balans. <PERSON><PERSON> kudade da kadarorin da ke ciki ba su da tabbas.", "bottomSheetConfirmTransactionsDescription": "An ƙiyasta canje-canjen balans. <PERSON><PERSON><PERSON> <PERSON> da ke ciki ba su da tabbas.", "bottomSheetSignTypedDataDescription": "<PERSON>n neman izni ne kawai. <PERSON><PERSON><PERSON> ba zai aiwatu ba nan take.", "bottomSheetSignTypedDataSecondDescription": "An ƙiyasta canje-canjen balans. <PERSON><PERSON> kuɗi da kadarorin da ke ciki basu da tabbas.", "bottomSheetSignMessageDescription": "Sa hannu a wannan saƙon zai tabbatar cewa kana da mallakin asusun da aka zaɓa. Sa hannu kawai daga manhajojin da ka amince da su.", "bottomSheetReadOnlyAccountDescription": "<PERSON> a iya wannan aikin a tsarin kallo-kawai.", "bottomSheetMessageRow": "<PERSON><PERSON>", "bottomSheetStatementRow": "<PERSON><PERSON>", "bottomSheetAutoConfirmRow": "Tabbatarwar-Atomatik", "bottomSheetAutoConfirmOff": "<PERSON><PERSON>", "bottomSheetAutoConfirmOn": "<PERSON><PERSON>", "bottomSheetAccountRow": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedRow": "An gabatar", "bottomSheetContractRow": "<PERSON><PERSON><PERSON>", "bottomSheetSpenderRow": "<PERSON><PERSON><PERSON> kashe kudi", "bottomSheetNetworkRow": "Netwok", "bottomSheetNetworkFeeRow": "<PERSON><PERSON><PERSON>", "bottomSheetEstimatedTimeRow": "<PERSON><PERSON><PERSON>", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Ka haɗe da shafukan intanet da ka amince da su kawai", "bottomSheetSignInRequestDisclaimer": "Ka shiga shafukan intanet da ka amince da su kawai", "bottomSheetSignatureRequestDisclaimer": "<PERSON><PERSON><PERSON> kawai in ka amince da wannan shafin intanet.", "bottomSheetFeaturedTransactionDisclaimer": "<PERSON>aka ga bayanin cinikin kafin ka tabbatar a mataki na gaba.", "bottomSheetIgnoreWarning": "<PERSON><PERSON> da garga<PERSON>, ci gaba kawai", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Ba a ga canje-canjen balans ba. Da fatan Za ka ci gaba cikin taka tsantsan kuma ka tabbatar kawai in ka amince da wannan shafi.", "bottomSheetReadOnlyWarning": "Kana kallon wannan ad<PERSON>hin ne kadai. Kana buƙatar shigarwa don sa hannu a ciniki da saƙonni.", "bottomSheetWebsiteIsUnsafeWarning": "<PERSON>n shafi ba shi da amincin yin amfani kuma yana iya ƙoƙarin satar kuɗaɗenka.", "bottomSheetViewOnExplorer": "<PERSON><PERSON> a Explorer", "bottomSheetTransactionSubmitted": "An mika ciniki", "bottomSheetTransactionPending": "<PERSON>", "bottomSheetTransactionFailed": "Ciniki <PERSON>", "bottomSheetTransactionSubmittedDescription": "An mika cinikinka. <PERSON><PERSON> <PERSON> a explorer.", "bottomSheetTransactionFailedDescription": "Cinikinka bai yiwu ba. Da fatan za a sake gwadawa.", "bottomSheetTransactionPendingDescription": "Ana kan sarrafa cinikin...", "transactionsFromInterpolated": "Daga: {{from}}", "transactionsFromParagraphInterpolated": "Daga {{from}}", "transactionsSolInterpolated": "SOL {{amount}}", "transactionsToday": "<PERSON><PERSON>", "transactionsToInterpolated": "<PERSON><PERSON><PERSON> ga: {{to}}", "transactionsToParagraphInterpolated": "<PERSON><PERSON><PERSON> {{to}}", "transactionsYesterday": "<PERSON><PERSON>", "addEditAddressAdd": "Sa ad<PERSON>hi", "addEditAddressDelete": "<PERSON><PERSON> ad<PERSON>hi", "addEditAddressDeleteTitle": "Ka tabbata kana so ka goge wannan ad<PERSON>hin?", "addEditAddressSave": "<PERSON><PERSON>", "dAppBrowserComingSoon": "brauzar dApp ta kusa fitowa!", "dAppBrowserSearchPlaceholder": "<PERSON><PERSON><PERSON><PERSON>, tokin, URL", "dAppBrowserOpenInNewTab": "<PERSON>e a sabon shafi", "dAppBrowserSuggested": "<PERSON><PERSON>", "dAppBrowserFavorites": "<PERSON><PERSON> <PERSON>", "dAppBrowserBookmarks": "Alamomi", "dAppBrowserBookmarkAdd": "<PERSON><PERSON>", "dAppBrowserBookmarkRemove": "Cire Alama", "dAppBrowserUsers": "<PERSON><PERSON>", "dAppBrowserRecents": "<PERSON><PERSON><PERSON>", "dAppBrowserFavoritesDescription": "Za nuna mafi so a nan", "dAppBrowserBookmarksDescription": "Za a nuna alamominnka a nan", "dAppBrowserRecentsDescription": "<PERSON><PERSON> aka haɗa baya-bayan nan zasu bayyana anan", "dAppBrowserEmptyScreenDescription": "Rubuta URL Ko bincika intanet", "dAppBrowserBlocklistScreenTitle": "An toshe {{origin}}! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom na ganin wannan shafin intanet na tattare da cuta kuma babu amincin amfani da shi.", "part2": "An alamta wannan shafin intanet a matattarar bayanan al'umma cikin sanannun shafukan intanet masu satar bayanai da yin zamba. Idan kuna ganin an yiwa shafin alama cikin kuskure, Da fatan za shigar da korafi."}, "dAppBrowserLoadFailedScreenTitle": "Bai loda na", "dAppBrowserLoadFailedScreenDescription": "An sami matsala wajen koda wannan shafi", "dAppBrowserBlocklistScreenIgnoreButton": "<PERSON><PERSON> da garga<PERSON>, nuno kawai", "dAppBrowserActionBookmark": "Alama", "dAppBrowserActionRemoveBookmark": "Cire alama", "dAppBrowserActionRefresh": "Sabunta", "dAppBrowserActionShare": "Yaɗa", "dAppBrowserActionCloseTab": "<PERSON><PERSON><PERSON> shafi", "dAppBrowserActionEndAutoConfirm": "<PERSON><PERSON>-<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserActionDisconnectApp": "Cire haɗin manhaja", "dAppBrowserActionCloseAllTabs": "<PERSON>ufe duk shafuka", "dAppBrowserNavigationAddressPlaceholder": "Tapa URL don bin<PERSON>", "dAppBrowserTabOverviewMore": "Fiye", "dAppBrowserTabOverviewAddTab": "<PERSON> shafi", "dAppBrowserTabOverviewClose": "Rufe", "dAppBrowserCloseTab": "<PERSON><PERSON><PERSON> shafi", "dAppBrowserClose": "Rufe", "dAppBrowserTabOverviewAddBookmark": "Sa Alama", "dAppBrowserTabOverviewRemoveBookmark": "Cire Alama", "depositAssetListSuggestions": "Shawarwari", "depositUndefinedToken": "A yi hakuri, ba za iya ajiyar wannan tokin ba", "onboardingImportRecoveryPhraseDetails": "Bayanai", "onboardingCreateRecoveryPhraseVerifyTitle": "An rubuta jimlar sirri na dawo da bayanai?", "onboardingCreateRecoveryPhraseVerifySubtitle": "<PERSON><PERSON> babu jimlar sirri na dawo da bayanai ba za ka iya samun mabudin ka ba ko kadarori dake da dangantaka da shi.", "onboardingCreateRecoveryPhraseVerifyYes": "E", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON>mu yi nasarar samo asusu ba, da fatan zaka sake gwadawa.", "onboardingDoneDescription": "Zaka iya cin cikakkiyar moriyar walat naka yanzu.", "onboardingDoneGetStarted": "Fara", "zeroBalanceHeading": "Zo mu fara!", "zeroBalanceBuyCryptoTitle": "<PERSON><PERSON>", "zeroBalanceBuyCryptoDescription": "<PERSON><PERSON> k<PERSON>ka a karon farko da katin debit ko credit.", "zeroBalanceDepositTitle": "Yi transfa na krifto", "zeroBalanceDepositDescription": "Sa krifto daga wani walat ko dillali.", "onboardingImportAccountsEmptyResult": "<PERSON> asusun da aka gani", "onboardingImportAccountsAccountName": "Asusu {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON>", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Mun sami asusun {{numberOfWallets}} na aiki", "onboardingImportAccountsFoundAccounts_other": "Mun sami asusun {{numberOfWallets}} suna aik", "onboardingImportAccountsFoundAccountsNoActivity_one": "Mun ga asusu {{numberOfWallets}}", "onboardingImportAccountsFoundAccountsNoActivity_other": "Mun ga asusu {{numberOfWallets}}", "onboardingImportRecoveryPhraseLessThanTwelve": "<PERSON><PERSON> ji<PERSON> ta kai kalmomi 12.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Jimlar na bukatar kalmomi 12 ko 24.", "onboardingImportRecoveryPhraseWrongWord": "<PERSON><PERSON><PERSON><PERSON> da ba daidai ba {{ words }}.", "onboardingProtectTitle": "<PERSON><PERSON> wa<PERSON> naka", "onboardingProtectDescription": "Kara tsaron biometric zai tabbatar kai kadai ne za ka iya amfani da walat naka.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON><PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "<PERSON><PERSON>", "onboardingProtectButtonHeadlineFingerprint": "<PERSON><PERSON> yatsa", "onboardingProtectButtonHeadlinePIN": "<PERSON><PERSON>", "onboardingProtectButtonSubheadline": "<PERSON> amfani da tantancewar {{ authType }}", "onboardingProtectError": "An sami matsala yayin tantan<PERSON>, da fatan zaka sake gwadawa", "onboardingProtectBiometryIosError": "An saita tantancewar Biometric a Phantom amma an kashe ta a Saitunan Tsari. Da fatan zaka kunna saituna > Phantom > <PERSON><PERSON> ko Taba She<PERSON> d'on sake kunnawa.", "onboardingProtectRemoveAuth": "<PERSON><PERSON>", "onboardingProtectRemoveAuthDescription": "Ka tabbata kana so ka kashe tantancewa?", "onboardingWelcomeTitle": "<PERSON><PERSON> da zuwa <PERSON>", "onboardingWelcomeDescription": "<PERSON>, ƙirƙiri sabon walat ko ka shigo da wanda ka ke da shi.", "onboardingWelcomeCreateWallet": "<PERSON><PERSON> sabon walat", "onboardingWelcomeAlreadyHaveWallet": "Da ma ina da walat", "onboardingWelcomeConnectSeedVault": "Haɗa <PERSON><PERSON><PERSON><PERSON><PERSON> Seed", "onboardingSlide1Title": "<PERSON> kake sa<PERSON>wa", "onboardingSlide1Description": "Walat dinka na cikin aminci da tsarin amfani da bayometrik, gane zamba da kula<PERSON> 24/7.", "onboardingSlide2Title": "Ka zo inda ya dace dangane da NFTs naka", "onboardingSlide2Description": "<PERSON><PERSON>, k<PERSON> sa<PERSON><PERSON>, da samun sabbin sa<PERSON>.", "onboardingSlide3Title": "<PERSON> abu<PERSON><PERSON> da dama da tokin naka", "onboardingSlide3Description": "<PERSON><PERSON>, yi m<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, da karba -- ba tare da ka bar walat dinka ba. ", "onboardingSlide4Title": "Bincika kayatattun ababen Web3", "onboardingSlide4Description": "<PERSON><PERSON><PERSON> kuma ka haɗe da manyan manhajoji tare da brauzar cikin-manhaja.", "onboardingMultichainSlide5Title": "Walat guda mai komai", "onboardingMultichainSlide5Description": "<PERSON> amfani da <PERSON>, Ethereum, da Polygon cikin tsari guda mai saukin amfani.", "onboardingMultichainSlide5DescriptionWithBitcoin": "<PERSON><PERSON><PERSON>, E<PERSON><PERSON>, Polygon da Bitcoin duk a allon mai amfani ɗaya-mai saukin sarra<PERSON>wa.", "requireAuth": "<PERSON> bukatar tantan<PERSON>wa", "requireAuthImmediately": "<PERSON> take", "availableToSend": "<PERSON><PERSON><PERSON>", "sendEnterAmount": "<PERSON><PERSON> da yawan kudi", "sendEditMemo": "<PERSON><PERSON><PERSON>", "sendShowLogs": "<PERSON><PERSON> jerin a<PERSON><PERSON>ka masu matsala", "sendHideLogs": "<PERSON><PERSON> jerin a<PERSON><PERSON>ka masu matsala", "sendGoBack": "<PERSON><PERSON> baya", "sendTransactionSuccess": "An yi nasarar aika tokin naka zuwa ga", "sendInputPlaceholder": "@username ko ad<PERSON>hi", "sendInputPlaceholderV2": "sunan mai amfani ko ad<PERSON>hi", "sendPeopleTitle": "<PERSON><PERSON><PERSON>", "sendDomainTitle": "Shafukan intanet", "sendFollowing": "Da ke biye", "sendRecentlyUsedAddressLabel": "An yi amfani da su {{formattedTimestamp}} da suka wuce", "sendRecipientAddress": "<PERSON><PERSON><PERSON> mai karba", "sendTokenInterpolated": "<PERSON><PERSON> da {{tokenSymbol}}", "sendPasteFromClipboard": "<PERSON>a a allon ajiya", "sendScanQR": "Laluba QR code", "sendTo": "Zuwa ga:", "sendRecipientZeroBalanceWarning": "<PERSON>n ad<PERSON>hin walat ba shi da balans kuma bai fito a sabon tarihin cinikayyanka ba. Da fatan zaka ka tantance in walat din daidai ne.", "sendUnknownAddressWarning": "<PERSON>n ba ad<PERSON>hin da ka yi amfani da shi dazun bane. Da fatan zaka yi taka tsantsan wajen cigaba.", "sendSameAddressWarning": "<PERSON>n shi ne sabon adireshinka. <PERSON><PERSON> zai aifar da cajin transfa ba tare da canje-canjen balans ba.", "sendMintAccountWarning": "Wannan adireshin as<PERSON>un kirkira ne. Ba zaka iya aika kuɗi zuwa wannan adireshin ba saboda zai kai ga amsar din<PERSON>.", "sendCameraAccess": "<PERSON><PERSON><PERSON>", "sendCameraAccessSubtitle": "Don laluba QR code, Sai an bude amfani da <PERSON>mara. Kana so ka bude saituna yanzu?", "sendSettings": "<PERSON><PERSON><PERSON>", "sendOK": "To", "invalidQRCode": "QR code bai da inganci.", "sendInvalidQRCode": "Wannan QR code ba ingantattacen ad<PERSON>hi ba ne", "sendInvalidQRCodeSubtitle": "Sake gwadawa ko da wani QR code.", "sendInvalidQRCodeSplToken": "Tokin mara inganci a QR code", "sendInvalidQRCodeSplTokenSubtitle": "Wannan QR code ya kunshi tokin da ba naka ba ko ba ka iya tantancewa ba.", "sendScanAddressToSend": "<PERSON><PERSON> adireshin {{tokenSymbol}} don a<PERSON> kudade", "sendScanAddressToSendNoSymbol": "<PERSON><PERSON> adireshin don aika kuɗi", "sendScanAddressToSendCollectible": "Laluba adireshin SOL don aika abun da za iya karba", "sendScanAddressToSendCollectibleMultichain": "<PERSON><PERSON> adireshin don aika ababen da ke karɓuwa", "sendSummary": "Taƙaitawa", "sendUndefinedToken": "<PERSON> ha<PERSON>, ba a iya aika wannan tokin ba", "sendNoTokens": "<PERSON> tokin a ƙasa", "noBuyOptionsAvailableInCountry": "<PERSON> zaɓin saya a kasarku", "swapAvailableTokenDisclaimer": "<PERSON><PERSON><PERSON> karancin tokin na yin musaya tsakanin netwok da netwok", "swapCrossSwapNetworkTooltipTitle": "Musanya Tsakanin <PERSON> da Netwok", "swapCrossSwapNetworkTooltipDescription": "A lokacin musanya tsakanin <PERSON> da Netwok ana so a yi amfani da tokin da suke akwai don sauƙin farashi da saurin ciniki.", "settingsAbout": "<PERSON><PERSON> a kan Phantom", "settingsShareAppWithFriends": "<PERSON><PERSON><PERSON>", "settingsConfirm": "E", "settingsMakeSureNoOneIsWatching": "Ka tabbatar kana kallon sikirin naka", "settingsManageAccounts": "<PERSON><PERSON> da <PERSON>u", "settingsPrompt": "Ka tabbata kana so ka cigaba?", "settingsSelectAvatar": "Zaɓi <PERSON>on furofayil", "settingsSelectSecretPhrase": "Zaɓi <PERSON><PERSON>", "settingsShowPrivateKey": "<PERSON><PERSON> don bayyana lamban sirrin naka", "settingsShowRecoveryPhrase": "<PERSON><PERSON> don bayyana jimlar sirrin naka", "settingsSubmitBetaFeedback": "Ka mika martanin Beta", "settingsUpdateAccountNameToast": "An sabunta sunan asusu", "settingsUpdateAvatarToast": "An sabunta Avatar", "settingsUpdateAvatarToastFailure": "Ba a iya sabunta Avatar ba!", "settingsWalletAddress": "<PERSON><PERSON><PERSON>", "settingsWalletAddresses": "<PERSON><PERSON><PERSON><PERSON>", "settingsWalletNamePrimary": "<PERSON><PERSON>", "settingsPlaceholderName": "<PERSON><PERSON>", "settingsWalletNameSecondary": "<PERSON><PERSON> sunan walat naka", "settingsYourAccounts": "Asusunka", "settingsYourAccountsMultiChain": "<PERSON><PERSON><PERSON> hada-hada", "settingsReportUser": "<PERSON> karar mai amfani", "settingsNotifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsNotificationPreferences": "Zaɓin sanarwarwoyi", "pushNotificationsPreferencesAllowNotifications": "<PERSON><PERSON> <PERSON><PERSON>", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON>", "pushNotificationsPreferencesSentTokensDescription": "<PERSON><PERSON> to<PERSON> da N<PERSON>s zuwa waje", "pushNotificationsPreferencesReceivedTokens": "<PERSON><PERSON> da aka karba", "pushNotificationsPreferencesReceivedTokensDescription": "<PERSON><PERSON> to<PERSON> da NFTs zuwa cikin gida", "pushNotificationsPreferencesDexSwap": "Musanya", "pushNotificationsPreferencesDexSwapDescription": "Yi musanya a sanannun manhajoji", "pushNotificationsPreferencesOtherBalanceChanges": "Sauran balans na canzawa", "pushNotificationsPreferencesOtherBalanceChangesDescription": "<PERSON>u mu'amalar tokin mabanbanta da suke da tasiri a balans naka", "pushNotificationsPreferencesPhantomMarketing": "Sabuntawa daga Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> sa<PERSON> da sabu<PERSON> bai daya", "pushNotificationsPreferencesDescription": "Waɗannan saitunan suna sarrafa sanarwarwoyin turi ga wannan walat mai aiki. <PERSON>wane walat na da saitunan sanarwarwoyin kansa. <PERSON> kashe duk sanarwarwoyin turi na <PERSON>, je ka <1>saitunan na'urarka</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Ba a iya haɗa zaɓin sanarwarwoyi ba.", "connectSeedVaultConnectSeed": "Haɗa Seed", "connectSeedVaultConnectSeedDescription": "Haɗa Phantom da Seed Vault na wayarka", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON> wani as<PERSON>u", "connectSeedVaultSelectASeed": "Zaɓi Seed", "connectSeedVaultSelectASeedDescription": "Zaɓi Seed da ka ke so ka haɗa da Phantom", "connectSeedVaultSelectAnAccountDescription": "Zaɓi asusun da zaka so ka bude da Phantom", "connectSeedVaultNoAccountsFound": "Ba a sami wani asusu ba.", "connectSeedVaultSelectAccounts": "Zaɓi wasu asusu", "connectSeedVaultSelectAccountsDescription": "Zaɓi asusun da za ka so ka buɗe da Phantom", "connectSeedVaultCompleteSetup": "<PERSON><PERSON> b<PERSON>", "connectSeedVaultCompleteSetupDescription": "Ka shirya tsaf! Bincika web3 da Phantom sannan ka yi amfani da Seed Vault naka don tabbatar da ciniki", "connectSeedVaultConnectAnotherSeed": "<PERSON> wa<PERSON>", "connectSeedVaultConnectAllSeedsConnected": "An haɗa duk seed", "connectSeedVaultNoSeedsConnected": "Ba a haɗa kowane seed ba. <PERSON><PERSON> madannin dake kasa domin ba da umarnin daga Seed <PERSON>.", "connectSeedVaultConnectAccount": "Haɗa asusu", "connectSeedVaultLoadMore": "Kara lodawa", "connectSeedVaultNeedPermission": "<PERSON><PERSON><PERSON> buka<PERSON>i", "connectSeedVaultNeedPermissionDescription": "Je ka Saituna don ba <PERSON> i<PERSON>in amfani da i<PERSON>in <PERSON>.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} caji", "stakeAmount": "<PERSON><PERSON>", "stakeAmountBalance": "Balans", "swapTopQuotes": "<PERSON>ka farashi {{numQuotes}} na sama", "swapTopQuotesTitle": "<PERSON><PERSON>", "swapProvidersTitle": "<PERSON><PERSON><PERSON>", "swapProvidersFee": "{{fee}} caji", "swapProvidersTagRecommended": "Gwagwabar Riba", "swapProvidersTagFastest": "<PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}}h {{minutes}}m", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}}s", "stakeReview": "<PERSON><PERSON><PERSON>", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "<PERSON><PERSON> ka<PERSON>o", "stakeReviewConfirm": "Tabbatar", "stakeReviewValidator": "<PERSON> kula da ciniki", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "<PERSON><PERSON> <PERSON> ya <PERSON>", "convertStakeStatusErrorMessage": "Ba iya mayar da jinginarka zuwa {{poolTokenSymbol}} ba. Da fatan za a sake gwadawa.", "convertStakeStatusLoadingTitle": "<PERSON><PERSON><PERSON> {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "<PERSON><PERSON>u fara matakan mayar da {{stakedTokenSymbol}} da ka jinginar zuwa {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "<PERSON><PERSON><PERSON> {{poolTokenSymbol}} ya kammala!", "convertStakeStatusSuccessMessage": "<PERSON> ƙarin lad<PERSON><PERSON> da JitoSOL dinka <1>a nan.</1>", "convertStakeStatusConvertMore": "<PERSON><PERSON> da Ƙari", "convertStakePendingTitle": "<PERSON><PERSON> <PERSON> jing<PERSON> {{symbol}}", "convertToJitoSOL": "<PERSON><PERSON>toSOL", "convertToJitoSOLInfoDescription": "<PERSON><PERSON> da SOL dinka zuwa Jito SOL don samun lada da shiga duniyar <PERSON>.", "convertToJitoSOLInfoTitle": "<PERSON><PERSON>toSOL", "convertStakeBannerTitle": "<PERSON><PERSON> da jing<PERSON><PERSON> zuwa JitoSOL don bunkasa lada da kimanin 15%", "convertStakeQuestBannerTitle": "<PERSON>ya SOL da aka yi musanya zuwa jitoSOL don samun lada. <PERSON><PERSON>i karin sani.", "liquidStakeConvertInfoTitle": "<PERSON><PERSON>toSOL", "liquidStakeConvertInfoDescription": "Bunkasa ladanka ta hanyar mayar da SOL dinka zuwa JitoSOL.<1>nemi ƙarin sani</1>", "liquidStakeConvertInfoFeature1Title": "Don me za a jing<PERSON>r da Jito?", "liquidStakeConvertInfoFeature1Description": "Jinginar don samun <PERSON>, wanda ke karuwa da jinginarka. Yi amfani da shi a tsarin <PERSON><PERSON>i don samun ƙari. Yi musayar JitoSOL dinka daga baya don samun uwar kuɗi + lada da ya taru", "liquidStakeConvertInfoFeature2Title": "Matsakaitar ladar ta fi", "liquidStakeConvertInfoFeature2Description": "Jigo zai rarraba SOL dinka tsakanin masu kula masu sauƙin caji. Ladar MEV zai kara bunkasa samunka.", "liquidStakeConvertInfoFeature3Title": "Goya wa netwok na Sol<PERSON> baya", "liquidStakeConvertInfoFeature3Description": "Jinginar haɗaka na tsare <PERSON> ta hanyar rarraba jingina tsakanin masu kula masu yawa, don rage kasada daga balideto masu karancin kula.", "liquidStakeConvertInfoSecondaryButton": "Ba Yanzu Ba", "liquidStakeStartStaking": "<PERSON><PERSON>", "liquidStakeReviewOrder": "Sake duba oda", "convertStakeAccountListPageIneligibleSectionTitle": "<PERSON><PERSON><PERSON> Basu Cancanta Ba", "convertStakeAccountIneligibleBottomSheetTitle": "<PERSON><PERSON><PERSON> Basu Cancanta Ba", "convertStakeAccountListPageErrorTitle": "An kasa debo asusun jingina", "convertStakeAccountListPageErrorDescription": "<PERSON>ku<PERSON>, an sami matsala, mun kasa debo asusun jingina", "liquidStakeReviewYouPay": "Ka biya", "liquidStakeReviewYouReceive": "<PERSON>", "liquidStakeReviewProvider": "<PERSON>", "liquidStakeReviewNetworkFee": "<PERSON><PERSON><PERSON>", "liquidStakeReviewPageTitle": "Tabbatarwa", "liquidStakeReviewConversionFootnote": "<PERSON>n ka jing<PERSON>r da tokin na Solana don musaya da JitoSOL za ka sami adadin JitoSOL dan kasa kadan. <1><PERSON> sani</1>", "convertStakeAccountIneligibleBottomSheetDescription": "<PERSON><PERSON><PERSON> jing<PERSON>r Jito na aiki da mafi yawan masu kula na Solana da ke aiki. Ba za ka iya mayar da jinginar SOL zuwa JitoSOL ba daga masu kula da b a aiki da su. <PERSON><PERSON><PERSON> da kari, sabbin SOL da aka jinginar za su ɗauki kwanaki ~2 kafin samun damar mayar da su JitoSOL.", "selectAValidator": "Zaɓi mai kula", "validatorSelectionListTitle": "Zaɓi mai kula", "validatorSelectionListDescription": "Zaɓi mai kula da zaka Jing<PERSON> da SOL dinka da.", "stakeMethodDescription": "<PERSON> kuɗin ruwa ta hanyar amfani da tokin na SOL don taimakawa <PERSON>ana. <1><PERSON> sani</1>", "stakeMethodRecommended": "An fi so", "stakeMethodEstApy": "Est. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "<PERSON><PERSON>", "stakeMethodSelectionLiquidStakingDescription": "Jinginar da SOL don manyan lada, ta<PERSON><PERSON> wajen tsare <PERSON> kuma ka sami JitoSOL don samun ƙarin lada.", "stakeMethodSelectionNativeStakingTitle": "<PERSON><PERSON>", "stakeMethodSelectionNativeStakingDescription": "Jinginar da SOL don samun lada a lokaci guda kana taimaka wajen tsare <PERSON>.", "liquidStakeMintStakeSOL": "<PERSON><PERSON><PERSON>", "mintJitoSOLInfoPageTitle": "<PERSON><PERSON><PERSON> da <PERSON><PERSON> a <PERSON>", "mintJitoSOLFeature1Title": "Don me za a yi jingina tare da Jito?", "mintJitoSOLFeature1Description": "Jinginar don samun <PERSON>, wanda ke karuwa tare da jinginarka. Yi amfani da shi a tsarin De<PERSON>i don samun ƙari. <PERSON> musayar JitoSOL dinka daga baya don samun uwar kuɗi+da ya taru", "mintJitoSOLFeature2Title": "Mat<PERSON>kaitar ladar da ta fi girma", "mintJitoSOLFeature2Description": "Jito na rarraba SOL dinka tsakanin masu kula mafi kyau da karancin caji. Ladar MEV zai kara bunkasa samunka.", "mintJitoSOLFeature3Title": "Goya wa netwok na Sol<PERSON> baya", "mintJitoSOLFeature3Description": "<PERSON><PERSON> mara riko na tsare <PERSON>ana ta hanyar rarraba jingina tsakanin masu kula da yawa, don rage kasada daga masu kula yan algus.", "mintLiquidStakePendingTitle": "<PERSON><PERSON> da jingina mara riko", "mintStakeStatusErrorTitle": "<PERSON><PERSON> da jingina mara riko bai yiwu ba", "mintStakeStatusErrorMessage": "Ba a iya fitar da naka {{poolTokenSymbol}} jingina mara riko ba. Da fatan zaka sake gwadawa.", "mintStakeStatusSuccessTitle": "<PERSON><PERSON> da naka {{poolTokenSymbol}} jingina mara riko ya kammala!", "mintStakeStatusLoadingTitle": "<PERSON><PERSON> da {{poolTokenSymbol}} jingina mara riko", "mintStakeStatusLoadingMessage": "<PERSON><PERSON>u fara aikin fitar da naka {{poolTokenSymbol}} jingina mara riko.", "mintLiquidStakeAmountDescription": "Zaɓi JitoSOL nawa kake so ka jing<PERSON><PERSON> da <PERSON>to", "mintLiquidStakeAmountProvider": "<PERSON>", "mintLiquidStakeAmountApy": "Kiyashin. APY", "mintLiquidStakeAmountBestPrice": "<PERSON><PERSON>", "mintLiquidStakeAmountInsufficientBalance": "<PERSON> is<PERSON><PERSON> balans", "mintLiquidStakeAmountMinRequired": "<PERSON> bukatar {{amount}}{{symbol}} don yin jingina", "swapTooltipGotIt": "<PERSON> gane", "swapTabInsufficientFunds": "<PERSON> is<PERSON><PERSON> balans", "swapNoAssetsFound": "<PERSON>", "swapNoTokensFound": "Ba a ga kowane tokin ba", "swapConfirmationTryAgain": "<PERSON>ke gwa<PERSON>wa", "swapConfirmationGoBack": "<PERSON><PERSON> baya", "swapNoQuotesFound": "Ba a ga farashi ba", "swapNotProviderFound": "<PERSON>mu iya samun mai samarwa ga musanyan wannan Tokin ba. <PERSON><PERSON>da wani tokin.", "swapAvailableOnMainnet": "A Mainnet ne kawai za sami wannan tsari", "swapNotAvailableEVM": "<PERSON> musanyan as<PERSON>un EVM har yanzu", "swapNotAvailableOnSelectedNetwork": "<PERSON> musanya a asusun da aka zaba", "singleChainSwapTab": "Netwok na Cikin Gida", "crossChainSwapTab": "Netwok na Ketare", "allFilter": "<PERSON><PERSON>", "bridgeRefuelTitle": "<PERSON> jari", "bridgeRefuelDescription": "Kara jari zai ba ka damar iya biyan kudin ciniki bayan ka yi musaya.", "bridgeRefuelLabelBalance": "Naka {{symbol}}", "bridgeRefuelLabelReceive": "<PERSON> karba", "bridgeRefuelLabelFee": "<PERSON><PERSON><PERSON>", "bridgeRefuelDismiss": "Ci gaba ba tare da kara jari ba", "bridgeRefuelEnable": "<PERSON><PERSON>", "unwrapWrappedSolError": "Cire jingina bai yiwu ba", "unwrapWrappedSolLoading": "Ana cire jingina...", "unwrapWrappedSolSuccess": "An cire jingina", "unwrapWrappedSolViewTransaction": "<PERSON><PERSON> ciniki", "dappApprovePopupSignMessage": "Sa hannun a sako", "solanaPayFrom": "Daga", "solanaPayMessage": "<PERSON><PERSON>", "solanaPayNetworkFee": "Kuɗin Netwok", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Biya {{item}}", "solanaPayPayNow": "Ka biya yanzu", "solanaPaySending": "<PERSON> aika {{item}}", "solanaPayReceiving": "<PERSON> karbar {{item}}", "solanaPayMinting": "<PERSON> buga {{item}}", "solanaPayTransactionProcessing": "Cinikin<PERSON> na gudana, da fatan zaka jira.", "solanaPaySent": "An aika!", "solanaPayReceived": "An karba!", "solanaPayMinted": "An buga!", "solanaPaySentNFT": "NFT da aka aika!", "solanaPayReceivedNFT": "NFT da aka karba!", "solanaPayTokensSent": "An yi nasarar aika tokin dinka zuwa ga {{to}}", "solanaPayTokensReceived": "Ka karbi sabbin tokin daga {{from}}", "solanaPayViewTransaction": "<PERSON><PERSON> ciniki", "solanaPayTransactionFailed": "<PERSON><PERSON><PERSON> bai yiwu ba", "solanaPayConfirm": "Tabbatar", "solanaPayTo": "zuwa ga", "dappApproveConnectViewAccount": "Duba asusunka na Solana", "deepLinkInvalidLink": "<PERSON><PERSON><PERSON> mara inganci", "deepLinkInvalidSplTokenSubtitle": "Wannan ya kunshi tokin da ba naka ba ko ba ka iya tantancewa ba.", "walletAvatarShowAllAccounts": "<PERSON><PERSON> du<PERSON>n as<PERSON>un", "pushNotificationsGetInstantUpdates": "<PERSON> sa<PERSON> nan da nan", "pushNotificationsEnablePushNotifications": "<PERSON><PERSON> sanar<PERSON><PERSON>yin turi na kammala tura kudi, musa<PERSON> da shela", "pushNotificationsEnable": "<PERSON><PERSON>", "pushNotificationsNotNow": "Ba yanzu ba", "onboardingAgreeToTermsOfServiceInterpolated": "Na yarda da <1>Doko<PERSON> aiki</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "To, na adana shi a wani wuri", "onboardingCreateNewWallet": "<PERSON><PERSON>", "onboardingErrorDuplicateSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> wannan <PERSON><PERSON> da ma a walat naka", "onboardingErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai mara inganci", "onboardingFinished": "Ka gama gaba-daya!", "onboardingImportAccounts": "<PERSON><PERSON> da asusu", "onboardingImportImportingAccounts": "<PERSON> shigowa da wasu asusu...", "onboardingImportImportingFindingAccounts": "<PERSON> neman asusu masu aiki", "onboardingImportAccountsLastActive": "<PERSON><PERSON> aiki {{formattedTimestamp}} da suka wuce", "onboardingImportAccountsNeverUsed": "Ba a taba amfani da shi ba", "onboardingImportAccountsCreateNew": "<PERSON><PERSON> walat", "onboardingImportAccountsDescription": "<PERSON><PERSON> walat da zaka shigo su", "onboardingImportReadOnlyAccountDescription": "Sa adireshi ko sunan shafi da ka ke so ka kalla kana da damar kallo-kawai ne kuma ba za ka iya sa hannu a ciniki ko saƙonni ba.", "onboardingImportSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingImportViewAccounts": "<PERSON><PERSON>", "onboardingRestoreExistingWallet": "<PERSON><PERSON> da walat da yake nan da ma da jimlar sirri mai kalmomi 12 ko 24", "onboardingShowUnusedAccounts": "<PERSON><PERSON> asusun da ba a yi amfani da su ba", "onboardingShowMoreAccounts": "<PERSON><PERSON> karin asusu", "onboardingHideUnusedAccounts": "<PERSON><PERSON> as<PERSON><PERSON> da ba a yi amfani da su ba", "onboardingSecretRecoveryPhrase": "<PERSON><PERSON> sirri na dawo da bayanai", "onboardingSelectAccounts": "Zaɓi asusunka", "onboardingStoreSecretRecoveryPhraseReminder": "Wannan ce hanya kadai da za ka iya dawo da asusunka. Da fatan zaka adana ta a wuri mai aminci!", "useTokenMetasForMintsUnknownName": "<PERSON> ba a sani ba", "timeUnitMinute": "minti", "timeUnitMinutes": "mintina", "timeUnitHour": "awa", "timeUnitHours": "<PERSON><PERSON><PERSON>", "espNFTListWithPrice": "Ka ƙasa {{NFTDisplayName}} a kan {{priceAmount}} {{priceTokenSymbol}} a {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Ka jera {{NFTDisplayName}} a kan {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Ka ƙasa {{NFTDisplayName}} din sayarwa a {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "<PERSON> jera {{NFTDisplayName}} don say<PERSON><PERSON>", "espNFTChangeListPriceWithPrice": "Ka sabunta ƙashin ka na {{NFTDisplayName}} zuwa {{priceAmount}} {{priceTokenSymbol}} a {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Ka sabunta Jeri na {{NFTDisplayName}} zuwa {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Ka sabunta ƙashin ka na {{NFTDisplayName}} a {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Ka sabunta Jeri na {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Ka yi tayin {{priceAmount}} {{priceTokenSymbol}} ga {{NFTDisplayName}} a {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Ka yi tayin {{priceAmount}} {{priceTokenSymbol}} a kan {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Ka sa yayi ga {{NFTDisplayName}} a {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Ka saka tayi na {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Sabon tayi na {{priceAmount}} {{priceTokenSymbol}} ga {{NFTDisplayName}} a {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Sabon tayi na {{priceAmount}} {{priceTokenSymbol}} a kan{{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "<PERSON>bon tayi ga {{NFTDisplayName}} a {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "<PERSON>bon tayi na {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Ka soke tayi na {{priceAmount}} {{priceTokenSymbol}} ga {{NFTDisplayName}} a {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Ka soke tayi na {{priceAmount}} {{priceTokenSymbol}} a kan {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Ka soke tayi na {{NFTDisplayName}} a {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Ka soke tayi na {{NFTDisplayName}}", "espNFTUnlist": "Ka cire ƙashin {{NFTDisplayName}} a {{dAppName}}", "espNFTUnlistWithoutDApp": "Ka cire Jeri na {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Ka saya {{NFTDisplayName}} a kan {{priceAmount}} {{priceTokenSymbol}} a {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Ka saya {{NFTDisplayName}} a kan {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Ka saya {{NFTDisplayName}} a {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Ka saya {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Ka sayar da {{NFTDisplayName}} a kan {{priceAmount}} {{priceTokenSymbol}} a {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Ka sayar da {{NFTDisplayName}} a kan {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Ka sayar da {{NFTDisplayName}} a {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Ka sayar da {{NFTDisplayName}}", "espDEXSwap": "Ka yi musanyar {{downTokensTextFragment}} a kan {{upTokensTextFragment}} a {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Ka ajiye {{downTokensTextFragment}} ya zuwa {{poolDisplayName}} matattarar ruwan kudin a {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Ka yi musanyar {{downTokensTextFragment}} ga {{upTokensTextFragment}} a {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Ka cire {{upTokensTextFragment}} daga {{poolDisplayName}} ruwan kudin {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Ka yi musanyar {{downTokensTextFragment}} ga {{upTokensTextFragment}} a {{dAppName}}", "espGenericTokenSend": "Ka aika da {{downTokensTextFragment}}", "espGenericTokenReceive": "Ka karba {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Ka yi musanyar {{downTokensTextFragment}} ga {{upTokensTextFragment}}", "espUnknown": "BA SANI BA", "espUnknownNFT": "NFT da ba a sani ba", "espTextFragmentAnd": "da", "externalLinkWarningTitle": "<PERSON>na dab da fita daga <PERSON>", "externalLinkWarningDescription": "<PERSON>ma ka bude {{url}}. Ka tabbatar ka aminta da wannan ƙafa kafin yin mu'amala da ita.", "shortcutsWarningDescription": "An samar da hanyoyin ratse da {{url}}. Tabbatar ka aminta da wannan kafa kafin hulɗa da ita.", "lowTpsBanner": "Solana na fama da cunkushewar netwok", "lowTpsMessageTitle": "Cunkushewar netwok na Solana", "lowTpsMessage": "<PERSON><PERSON>da yawan cu<PERSON>, cinikinka na iya gazawa ko yin jinkiri. Da fatan za a sake gwada cinikin da ya gaza.", "solanaSlow": "Netwok na Solana bata da sauri", "solanaNetworkTemporarilyDown": "Netwok na Solana ya yi kasa a halin yanzu", "waitForNetworkRestart": "Da fatan zaka jira Netwok na Solana ya farfaɗo. Ba abinda zai shafi kudadenka.", "exploreCollectionsCarouselTitle": "<PERSON><PERSON><PERSON>hara", "exploreDropsCarouselTitle": "Me ake ciki", "exploreSortFloor": "<PERSON><PERSON> ƙarancin farashi", "exploreSortListed": "An jera", "exploreSortVolume": "<PERSON><PERSON>", "exploreFetchErrorSubtitle": "<PERSON>na fatan za a sake gwadawa anjima.", "exploreFetchErrorTitle": "Ba a iya debowa ba.", "exploreTopCollectionsTitle": "Manyan ka<PERSON>kin NFT", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "Fiye", "exploreSeeMore": "<PERSON><PERSON>", "exploreTrendingTokens": "<PERSON><PERSON>", "exploreVolumeTokens": "<PERSON><PERSON>", "explorePriceChangeTokens": "<PERSON><PERSON>", "explorePriceTokens": "<PERSON><PERSON> daidai da farashi", "exploreMarketCapTokens": "<PERSON><PERSON>", "exploreTrendingSites": "<PERSON><PERSON><PERSON><PERSON> masu tashe", "exploreTopSites": "<PERSON><PERSON>", "exploreTrendingCollections": "<PERSON><PERSON><PERSON><PERSON> masu tashe", "exploreTopCollections": "<PERSON><PERSON>", "collectiblesSearchCollectionsSection": "<PERSON><PERSON><PERSON> kaya<PERSON>ki", "collectiblesSearchItemsSection": "Ababe", "collectiblesSearchNrOfItems": "Ababen {{ nrOfItems }}", "collectiblesSearchPlaceholderText": "<PERSON><PERSON><PERSON> kayayyakin ka", "collectionPinSuccess": "An makala kunshin kayayyaki", "collectionPinFail": "Ma<PERSON>a kunshin kayayyaki bai yiwu ba", "collectionUnpinSuccess": "An cire kunshin kayayyaki", "collectionUnpinFail": "Cire kunshin kayayyaki bai yiwu ba", "collectionHideSuccess": "An boye kunshin kayayyaki", "collectionHideFail": "<PERSON><PERSON> kunshin kayayyaki bai yiwu ba", "collectionUnhideSuccess": "<PERSON><PERSON><PERSON> kayayyaki bai biyu ba", "collectionUnhideFail": "<PERSON><PERSON> kunshin kayayyaki bai yiwu ba", "collectiblesSpamSuccess": "An yi korafi cewa sakon banza ne", "collectiblesSpamFail": "<PERSON> kora<PERSON> sakon banza", "collectiblesSpamAndHiddenSuccess": "An yi korafin cewa sakon banza ne", "collectiblesNotSpamSuccess": "An yi rahoton ba sakon banza ba ne", "collectiblesNotSpamFail": "<PERSON> rahoton ba sakon banza ba ya kasa", "collectiblesNotSpamAndUnhiddenSuccess": "An yi rahoto ba sakon banza ba ne kuma an cire ɓuya", "tokenPageSpamWarning": "Ba a tantance wannan wannan tokin ba. Yi mu'amala da tokin da la yarda da su kaɗai.", "tokenSpamWarning": "An ɓoye wannan tokin saboda Phantom na ganin sakon banza ne.", "collectibleSpamWarning": "An ɓoye wannan kaya saboda Phantom na ganin sakon banza ne.", "collectionSpamWarning": "An ɓoye waɗannan kayayyakin ne saboda Phantom na ganin sakonnin banza ne.", "emojiNoResults": "Ba a sami emoji ba", "emojiSearchResults": "<PERSON><PERSON><PERSON> saka<PERSON>", "emojiSuggested": "<PERSON> shawarata", "emojiSmileys": "<PERSON><PERSON><PERSON> mur<PERSON>hi & mutane", "emojiAnimals": "Dabbobi & Yanayi", "emojiFood": "<PERSON><PERSON><PERSON> & <PERSON>bin sha", "emojiTravel": "Tafiya & wurare", "emojiActivities": "Ai<PERSON><PERSON>-a<PERSON><PERSON>", "emojiObjects": "Ababe", "emojiSymbols": "<PERSON><PERSON><PERSON>", "emojiFlags": "<PERSON><PERSON><PERSON>", "whichExtensionToConnectWith": "Da wace brauza ka ke son hadewa?", "configureInSettings": "Za a iya saitawa cikin <PERSON> → ManhajarWallet ta Asali.", "continueWith": "<PERSON>i gaba da", "useMetaMask": "<PERSON> <PERSON><PERSON><PERSON> da MetaMask", "usePhantom": "<PERSON><PERSON> da <PERSON>", "alwaysAsk": "<PERSON> tambaya kullum", "dontAskMeAgain": "Kada ka kara tambaya ta", "selectWalletSettingDescriptionLine1": "<PERSON><PERSON> yiwu wasu manhajoji ba za su ba da zabin sadarwa da <PERSON> ba.", "selectWalletSettingDescriptionLinePhantom": "<PERSON><PERSON><PERSON>, sad<PERSON><PERSON> da <PERSON>aM<PERSON> zai buɗe Phantom kullum.", "selectWalletSettingDescriptionLineAlwaysAsk": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> da <PERSON>, zamu tambaye ka ko zaka yi amfani da <PERSON> a maimako.", "selectWalletSettingDescriptionLineMetaMask": "<PERSON><PERSON> a matsayin asali zai hana wadancan dapps sadarwa da <PERSON>.", "metaMaskOverride": "<PERSON><PERSON><PERSON>", "metaMaskOverrideSettingDescriptionLine1": "<PERSON>in sad<PERSON>wa da shafukan intanet da ba su ba da da zabin amfani da <PERSON> ba.", "refreshAndReconnectToast": "Sake sabuntawa sannan sake haɗawa don aiwatar da canje-canjenka", "autoConfirmUnavailable": "<PERSON>", "autoConfirmReasonDappNotWhitelisted": "<PERSON> shi saboda ya fito daga kontragin da ba ya cikin jerin izni a wannan manhaja.", "autoConfirmReasonSessionNotActive": "<PERSON> shi saboda babu yanayin tabba<PERSON>war atomatik dake aiki. Da fatan kunna shi a kasa.", "autoConfirmReasonRateLimited": "<PERSON> shi saboda dapp da kake amfani da shi na aika buƙata da yawa.", "autoConfirmReasonUnsupportedNetwork": "<PERSON> shi saboda tabbatarwar atomatik bai fara aiki a wannan netwok ba.", "autoConfirmReasonSimulationFailed": "<PERSON> shi saboda ba mu da tabbas na tsaro.", "autoConfirmReasonTabNotFocused": "<PERSON> shi saboda madannin da kake kokarin yin tabbatarwar atomatik ba ya aiki.", "autoConfirmReasonNotUnlocked": "<PERSON> shi saboda ba a bude walat din ba.", "rpcErrorUnauthorizedWrongAccount": "<PERSON><PERSON><PERSON> daga adireshi bai yi daidai da adireshin asusu ba.", "rpcErrorUnauthorizedUnknownSource": "Ba a iya tantance tushen buƙatar RPC ba.", "transactionsDisabledTitle": "An hana ciniki", "transactionsDisabledMessage": "<PERSON><PERSON><PERSON><PERSON> bai iya yin ciniki ba ta hanyar amfani da <PERSON>", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "<PERSON><PERSON> aiki", "settingsTrustedAppDetailsCopiedToClipboardToast": "An kwafi URL zuwa allon ajiya", "notEnoughSolScanTransactionWarning": "Wannan ciniki zai iya kasa saboda rashin isassun SOL a asusunka. Da fatan za ka sa ƙarin SOL a asusunka sannan ka sake gwadawa.", "transactionRevertedWarning": "An dawo da ciniki ya koma yayin kwaik<PERSON>o. <PERSON> iya yin asarar kuɗi in an mika shi.", "slippageToleranceExceeded": "An dawo da wannan ciniki lokacin gwaji. Ya wuce juriyar gocewa.", "simulationWarningKnownMalicious": "<PERSON>na zargin akwai cuta a wannan asusu. Amincewa na iya kai ga asarar kudade.", "simulationWarningPoisonedAddress": "Wannan adireshin yana kama da adireshin da ka aika kuɗi zuwa gare shi ba da dadewa ba. Da fatan za a tabbatar da wannan shine adireshin daidai don hana asarar kuɗi cikin zamba.", "simulationWarningInteractingWithAccountWithoutActivity": "Wannan asusun ba a taɓa aiki da shi ba. Aika kuɗi zuwa asusun da babu shi na iya haifar da asarar kuɗi", "quests": "Ayyukan <PERSON>a", "questsClaimInProgress": "<PERSON> kan nema", "questsVerifyingCompletion": "Ana tabbatar da kammala aikin <PERSON>a", "questsClaimError": "<PERSON><PERSON><PERSON> karbar kyauta", "questsClaimErrorDescription": "An sami kuskure a neman kyautarka.", "questsBadgeMobileOnly": "<PERSON><PERSON><PERSON>", "questsBadgeExtensionOnly": "<PERSON><PERSON><PERSON>", "questsExplainerSheetButtonLabel": "<PERSON> gane", "questsNoQuestsAvailable": "<PERSON> aikin lada a kasa", "questsNoQuestsAvailableDescription": "<PERSON> aikin lada a kasa. <PERSON><PERSON>u sanar da kai da zarar an saka sabbi.", "exploreLearn": "<PERSON><PERSON>", "exploreSites": "Shafukan intanet", "exploreTokens": "<PERSON><PERSON>", "exploreQuests": "Ayyukan <PERSON>", "exploreCollections": "<PERSON><PERSON><PERSON>", "exploreFilterByall_networks": "Duka Net<PERSON>k", "exploreSortByrank": "<PERSON>she", "exploreSortBytrending": "<PERSON>she", "exploreSortByprice": "<PERSON><PERSON>", "exploreSortByprice-change": "<PERSON><PERSON>", "exploreSortBytop": "<PERSON><PERSON>", "exploreSortByvolume": "<PERSON><PERSON>", "exploreSortBygainers": "<PERSON><PERSON>a", "exploreSortBylosers": "<PERSON><PERSON>", "exploreSortBymarket-cap": "<PERSON><PERSON> jari", "exploreSortBymarket_cap": "<PERSON><PERSON>", "exploreTimeFrame1h": "Hawa1", "exploreTimeFrame24h": "Hawa24", "exploreTimeFrame7d": "Kwana7", "exploreTimeFrame30d": "Kwana30", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "<PERSON><PERSON><PERSON>", "exploreCategoryMarketplace": "<PERSON><PERSON><PERSON>", "exploreCategoryGaming": "<PERSON> wasa", "exploreCategoryBridges": "<PERSON><PERSON>", "exploreCategoryOther": "<PERSON><PERSON>", "exploreCategorySocial": "Zamantake<PERSON>", "exploreCategoryCommunity": "Al'umma", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "<PERSON><PERSON>", "exploreCategoryArt": "<PERSON>", "exploreCategoryTools": "<PERSON><PERSON> aiki", "exploreCategoryDeveloperTools": "<PERSON><PERSON> aikin <PERSON>", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Jinginar NFT", "exploreCategoryExplorer": "Explorer", "exploreCategoryInscriptions": "<PERSON><PERSON>", "exploreCategoryBridge": "Transfa", "exploreCategoryAirdrop": "<PERSON><PERSON><PERSON><PERSON> tokin", "exploreCategoryAirdropChecker": "<PERSON><PERSON> Duba Airdrop", "exploreCategoryPoints": "<PERSON><PERSON>", "exploreCategoryQuests": "Ayyukan <PERSON>a", "exploreCategoryShop": "Shago", "exploreCategoryProtocol": "Doko<PERSON>", "exploreCategoryNamingService": "<PERSON><PERSON>", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "<PERSON><PERSON>", "exploreCategoryFitness": "Dacewa", "exploreCategoryDePIN": "DePIN", "exploreVolume": "<PERSON><PERSON>", "exploreFloor": "<PERSON><PERSON>", "exploreCap": "<PERSON><PERSON>", "exploreToken": "<PERSON><PERSON>", "explorePrice": "<PERSON><PERSON>", "explore24hVolume": "Yawa a 24h", "exploreErrorButtonText": "<PERSON>ke gwa<PERSON>wa", "exploreErrorDescription": "An sami matsala a kokarin loda kunshin bincike. Da fatan za a sabunta sannan s sake gwadawa", "exploreErrorTitle": "An kasa loda kunshin bincike", "exploreNetworkError": "An sami matsalar netwok. Da fatan za ka sake gwadawa an jima.", "exploreTokensLegalDisclaimer": "Ana fitar da matsayin tokin ne ta hanyar amfani da kididdigar kasuwa da wasu kamfan<PERSON> mabambata i<PERSON>, <PERSON><PERSON> da Jupiter ke fitarwa. Ana dogara ne da ayyukan hawa 24 baya. Ayyukan baya ba sa nuni zuwa ga ayyukan nan gaba.", "swapperTokensLegalDisclaimer": "Ana fitar da matsayin tokin masu tashe ne ta hanyar amfani da kididdigar kasuwa da kamfanoni mabambata irin <PERSON>inG<PERSON>, <PERSON><PERSON> da Jupiter ke fitarwa. Ana dogara ne da bayanin fitattun tokin da masu amfani da Phantom suka yi musanyar su ta Swapper a wani ayyanannen lokaci. Ayyukan baya ba sa nuni zuwa ga ayyukan nan gaba.", "exploreLearnErrorTitle": "An kasa loda kunshin koya", "exploreLearnErrorDescription": "An sami matsalar loda kunshin bincike. Da fatan za sabunta sannan a sake gwadawa", "exploreShowMore": "<PERSON><PERSON>wa", "exploreShowLess": "<PERSON><PERSON> kaɗan", "exploreVisitSite": "<PERSON><PERSON><PERSON><PERSON>", "dappBrowserSearchScreenVisitSite": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>", "dappBrowserSearchScreenSearchWithGoogle": "<PERSON><PERSON><PERSON> da <PERSON>", "dappBrowserSearchScreenSearchLinkYouCopied": "<PERSON> da ka kwafa", "dappBrowserExtSearchPlaceholder": "<PERSON><PERSON><PERSON> shafuka da tokin", "dappBrowserSearchNoAppsTokens": "Ba a sami <PERSON> ko tokin ba", "dappBrowserTabsLimitExceededScreenTitle": "A rufe tsaffin shafuka?", "dappBrowserTabsLimitExceededScreenDescription": "Kana da shafuka {{tabsCount}} a bude. Don buɗe ƙarin, kana buƙatar rufe wasu shafuka.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "Rufe Duk Shafuka", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: <PERSON> sunan intanet", "dappBrowserTabErrorHttp": "<PERSON> toshe. Da fatan za yi amfani da HTTPS", "dappBrowserTabError401Unauthorized": "401 Ba a yarda ba", "dappBrowserTabError501UnhandledRequest": "501 Bukatar da ba kula ba", "dappBrowserTabErrorTimeout": "LOKACI YA WUCE: Sava ta yi jinkiri martani da yawa", "dappBrowserTabErrorInvalidResponse": "<PERSON><PERSON> mara am<PERSON>i", "dappBrowserTabErrorEmptyResponse": "<PERSON> martani", "dappBrowserTabErrorGeneric": "An sami matsala", "localizedErrorUnknownError": "<PERSON> sami mat<PERSON>, <PERSON> fatan zaka sake gwadawa anjima.", "localizedErrorUnsupportedCountry": "<PERSON> hakuri. <PERSON><PERSON><PERSON> ba ta cikin wadanda muke aiki da su a halin yanzu.", "localizedErrorTokensNotLoading": "An sami matsalar loda tokin dinku. Da fatan za a sake gwadawa.", "localizedErrorSwapperNoQuotes": "<PERSON> musanya saboda haɗin kuɗade da ba amfani da su, karancin kuɗi, ko karancin kumar kuɗi. <PERSON>za tokin ko kimar kuɗi.", "localizedErrorSwapperRefuelNoQuotes": "Ba a sami farashi ba. <PERSON><PERSON><PERSON> wani tokin ko farashi na daban, ko kashe kara jari.", "localizedErrorInsufficientSellAmount": "<PERSON>din tokin yayi ƙasa sosai. Ƙara yawan don musanya a Cross-Chain.", "localizedErrorCrossChainUnavailable": "<PERSON> musanyar Cross-chain yanzu, da fatan za a sake gwadawa an jima.", "localizedErrorTokenNotTradable": "Cinikin ɗaya daga cikin tokin da aka zaba ba zai yiwu ba. Da fatan za a zaɓi wani aikin.", "localizedErrorCollectibleLocked": "<PERSON><PERSON>un tokin na a rufe.", "localizedErrorCollectibleListed": "An jera asusun tokin.", "spamActivityAction": "<PERSON><PERSON> da aka ɓoye", "spamActivityTitle": "<PERSON><PERSON><PERSON><PERSON> da aka ɓoye", "spamActivityWarning": "An ɓoye wannan cinikin saboda Phantom na ganin yiiwuwar sakon banza ne.", "appAuthenticationFailed": "An kasa tantancewa", "appAuthenticationFailedDescription": "An sami matsala da tantancewarka. Da fatan za ka sake gwadawa.", "partialErrorBalanceChainName": "<PERSON>na samun matsalar sabunta balans naka na {{chainName}}. Kuɗaɗenka na cikin aminci.", "partialErrorGeneric": "<PERSON>na samun matsalar sabunta netwok, wasu balans na tokin naka kuma zai yiwu farashi sun tsufa. Kuɗaɗenka na cikin aminci.", "partialErrorTokenDetail": "<PERSON>na samun matsalar sabunta balans na tokin naka. Kuɗaɗenka na cikin aminci.", "partialErrorTokenPrices": "<PERSON>na samun matsalar sabunta farashi na tokin naka. Kuɗaɗenka na cikin aminci.", "partialErrorTokensTrimmed": "<PERSON>na samun matsalar nuna duk tokin dake cikin fotifoliyonka. Kuɗaɗenka na cikin aminci.", "publicFungibleDetailAbout": "Game da", "publicFungibleDetailYourBalance": "Balans dinka", "publicFungibleDetailInfo": "<PERSON><PERSON>", "publicFungibleDetailShowMore": "<PERSON><PERSON> mai yawa", "publicFungibleDetailShowLess": "<PERSON><PERSON> Kaɗan", "publicFungibleDetailPerformance": "Aiki hawa<PERSON>", "publicFungibleDetailSecurity": "<PERSON><PERSON>", "publicFungibleDetailMarketCap": "<PERSON><PERSON>", "publicFungibleDetailTotalSupply": "<PERSON><PERSON>", "publicFungibleDetailCirculatingSupply": "<PERSON><PERSON> da ke zagayewa", "publicFungibleDetailMaxSupply": "<PERSON><PERSON>", "publicFungibleDetailHolders": "<PERSON><PERSON> jari", "publicFungibleDetailVolume": "<PERSON><PERSON>", "publicFungibleDetailTrades": "Cinikayya", "publicFungibleDetailTraders": "<PERSON>", "publicFungibleDetailUniqueWallets": "Walat na Musamman", "publicFungibleDetailTop10Holders": "<PERSON><PERSON> 10", "publicFungibleDetailTop10HoldersTooltip": "Ya kan nuna kason duka kaya da ke hannun manyan masu jarin tokin 10. Ma'auni ne yadda za a iya murda farashi cikin sauki.", "publicFungibleDetailMintable": "Za iya buga", "publicFungibleDetailMintableTooltip": "<PERSON> kongila zai iya kara yawan tokin in tokin in za iya fitar da tokin din.", "publicFungibleDetailMutableInfo": "Bayanan da za iya rufewa", "publicFungibleDetailMutableInfoTooltip": "<PERSON>n za a iya rufe bayanai kamar suna, tamba<PERSON>, ad<PERSON><PERSON> in<PERSON>, mai kongila zai iya canza shi.", "publicFungibleDetailOwnershipRenounced": "<PERSON>", "publicFungibleDetailOwnershipRenouncedTooltip": "<PERSON>n an yi kori mallakar tokin, ba wanda zai iya aiwatar da ayyuka kamar fitar da ƙarin tokin.", "publicFungibleDetailUpdateAuthority": "Sabunta Iko", "publicFungibleDetailUpdateAuthorityTooltip": "Mai ikon sabuntawa shine adireshin walat ɗin da zai iya canza bayanai idan za iya rufe tokin.", "publicFungibleDetailFreezeAuthority": "<PERSON><PERSON>", "publicFungibleDetailFreezeAuthorityTooltip": "Kin rikewa shi ne walat da zai iya hana transfa na kudade.", "publicFungibleUnverifiedToken": "Ba a tantance wannan tokin ba. Yi mu'amala da tokin da ka yarda da su kaɗai.", "publicFungibleDetailSwap": "<PERSON> m<PERSON>yar {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "<PERSON> m<PERSON>yar {{tokenSymbol}} da manhajar Phantom", "publicFungibleDetailLinkCopied": "An kwafa zuwa allon-ajiya", "publicFungibleDetailContract": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailMint": "<PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "<PERSON><PERSON>", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON>a kari", "unifiedTokenDetailTransactionActivityError": "Koda aikin baya-bayan nan bai yiwu ba", "additionalNetworksTitle": "<PERSON>", "copyAddressRowAdditionalNetworks": "<PERSON>", "copyAddressRowAdditionalNetworksHeader": "Netwok da suke tafe na amfani da adireshi iri ɗaya da Ethereum:", "copyAddressRowAdditionalNetworksDescription": "<PERSON>aka iya amfani da adireshin Ethereum dinka wajen aikawa da karbar dukiyar ka a kowanne daga wadannan netwok.", "cpeUnknownError": "<PERSON><PERSON><PERSON> da ba a sani ba", "cpeUnknownInstructionError": "<PERSON><PERSON><PERSON> matsala da ba a sani ba", "cpeAccountFrozen": "An rike asusun", "cpeAssetFrozen": "An rike kdara", "cpeInsufficientFunds": "<PERSON> <PERSON><PERSON><PERSON> kuɗaɗe", "cpeInvalidAuthority": "<PERSON>ko mare inganci", "cpeBalanceBelowRent": "Balans bai kai kimar yafe kuɗin haya ba", "cpeNotApprovedForConfidentialTransfers": "Ba a amince wa asusun yin transfa na sirri ba", "cpeNotAcceptingDepositsOrTransfers": "<PERSON><PERSON><PERSON> ba ya karɓar ajiyar ko transfa", "cpeNoMemoButRequired": "<PERSON> bayani na umurnin baya; dole ne kafin makarbi ya karɓi transfa", "cpeTransferDisabledForMint": "An rufe transfa ga wannan fitarwa", "cpeDepositAmountExceedsLimit": "<PERSON><PERSON> a<PERSON> ya wuce babban iyaka", "cpeInsufficientFundsForRent": "<PERSON> is<PERSON><PERSON> kuɗaɗen haya", "reportIssueScreenTitle": "Yi rahoton matsala", "publicFungibleReportIssuePrompt": "Wace matsala kake son yin rahoto a kai{{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "<PERSON><PERSON><PERSON> da ba daidai ba", "publicFungibleReportIssuePriceStale": "<PERSON><PERSON> ba ya sabuntawa", "publicFungibleReportIssuePriceMissing": "<PERSON>", "publicFungibleReportIssuePerformanceIncorrect": "Ayyukan 24h ba daidai ba ne", "publicFungibleReportIssueLinkBroken": "Ba a samun link na kafofin zumunta", "publicFungibleDetailErrorLoading": "<PERSON> bay<PERSON> tokin", "reportUserPrompt": "Ƙarar me kake so ka yi a kan mai amfani @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "<PERSON><PERSON> <PERSON>", "reportUserOptionAbuseAndHarrassmentDescription": "<PERSON><PERSON> da <PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ta<PERSON>, f<PERSON>in da isharar ƙiyayya", "reportUserOptionPrivacyAndImpersonationTitle": "<PERSON><PERSON>", "reportUserOptionPrivacyAndImpersonationDescription": "Yaɗawa ko baranar fitar da bayanin sirri, sojan gona", "reportUserOptionSpamTitle": "<PERSON><PERSON>", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON> boge, zamba, link masu ɗauke da cuta", "reportUserSuccess": "<PERSON> <PERSON><PERSON>.", "settingsClaimUsernameTitle": "<PERSON><PERSON>", "settingsClaimUsernameDescription": "<PERSON><PERSON><PERSON> musamman na musamman ne\n kamar na walat ɗin ka", "settingsClaimUsernameValueProp1": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameValueProp1Description": "<PERSON> <PERSON><PERSON> da dogayen ad<PERSON><PERSON><PERSON> masu wahala kuma ka yi maraba da shaida mai saukin amfani", "settingsClaimUsernameValueProp2": "Ta fi sauri & Sauki", "settingsClaimUsernameValueProp2Description": "Ka aika tare da karba <PERSON><PERSON> cikin sauki, shiga walat dinka, kuma sadu da abokai", "settingsClaimUsernameValueProp3": "Zauna a Daidaita Bayanai", "settingsClaimUsernameValueProp3Description": "Haɗa kowane asusu da sunan mai amfani naka, zata da<PERSON>ita bayanai tsakanin duk na'u<PERSON><PERSON>ka", "settingsClaimUsernameHelperText": "<PERSON><PERSON><PERSON> na musamman na <PERSON>un Phantom", "settingsClaimUsernameValidationDefault": "Ba za iya canza wannan sunan mai amfani daga baya", "settingsClaimUsernameValidationAvailable": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameValidationUnavailable": "<PERSON>", "settingsClaimUsernameValidationServerError": "Ba a iya bincika ko akwai sunan mai amfani ba, da fatan za a sake gwadawa", "settingsClaimUsernameValidationErrorLine1": "<PERSON>an mai amfani mara kyau.", "settingsClaimUsernameValidationErrorLine2": "<PERSON><PERSON> tsawon sunan mai amfani ta kasance tsakanin haruffa {{minChar}} da {{maxChar}} kuma ta kunshi bakake da lambobi kawai.", "settingsClaimUsernameValidationLoading": "<PERSON> bincike in akwai wannan sunan mai amfani...", "settingsClaimUsernameSaveAndContinue": "Adana & Cigaba", "settingsClaimUsernameChooseAvatarTitle": "Zaɓi Avata", "settingsClaimUsernameAnonymousAuthTitle": "Ɓoye <PERSON>", "settingsClaimUsernameAnonymousAuthDescription": "<PERSON>ga asusunka na Phantom a ɓoye da wata sa hannu", "settingsClaimUsernameAnonymousAuthBadge": "<PERSON><PERSON> yadda wannan ke aiki", "settingsClaimUsernameLinkWalletsTitle": "Haɗa walat ɗin ka", "settingsClaimUsernameLinkWalletsDescription": "<PERSON> amfani da walat da suke bayyana da sunan mai amfani naka a wasu na'urori", "settingsClaimUsernameLinkWalletsBadge": "Ba a iya gani baina jama'a", "settingsClaimUsernameConnectAccountsTitle": "Harhaɗa <PERSON><PERSON><PERSON>", "settingsClaimUsernameConnectAccountsHelperText": "Za a haɗa kowane adireshin chain da sunan mai amfani naka. Zaka iya canza wadannan daga baya.", "settingsClaimUsernameContinue": "Cigaba", "settingsClaimUsernameCreateUsername": "<PERSON><PERSON>", "settingsClaimUsernameCreating": "Ana ƙirƙirar sunan mai amfani...", "settingsClaimUsernameSuccess": "An <PERSON>!", "settingsClaimUsernameError": "Mun sami matsala wajen kirkiro sunan mai amfani naka", "settingsClaimUsernameTryAgain": "<PERSON>ke gwa<PERSON>wa", "settingsClaimUsernameSuccessHelperText": "<PERSON>aka iya amfani da sabon sunan mai amfani naka a dukan Phantom walat dinka", "settingsClaimUsernameSettingsSyncedTitle": "<PERSON>", "settingsClaimUsernameSettingsSyncedHelperText": "<PERSON> <PERSON>wana da dogayen ad<PERSON><PERSON>hi masu wahala kuma ka yi maraba da shaida me sauƙin amfani", "settingsClaimUsernameSendToUsernameTitle": "<PERSON><PERSON>", "settingsClaimUsernameSendToUsernameHelperText": "<PERSON> aika da karba <PERSON><PERSON>o cikin sauki, s<PERSON>ga walat dinka, kuma sadu da abokai", "settingsClaimUsernameManageAddressesTitle": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameManageAddressesHelperText": "<PERSON><PERSON><PERSON> tokin ko kaya da aka aika zuwa sunan mai amfani naka zai aika zuwa wadannan adireshoshin", "settingsClaimUsernameManageAddressesBadge": "Ana iya gani baina jama'a", "settingsClaimUsernameEditAddressesTitle": "<PERSON><PERSON>", "settingsClaimUsernameEditAddressesHelperText": "<PERSON><PERSON><PERSON> tokin ko kaya da aka aika zuwa sunan mai amfani naka zai aika zuwa wadannan adireshoshin. Zaɓi adireshi guda ga kowane chain.", "settingsClaimUsernameEditAddressesError": "<PERSON><PERSON><PERSON> ɗaya ne aka yarje wa kowane netwok.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameNoAddressesSaved": "Ba a adana wani ad<PERSON>hin fili ba", "settingsClaimUsernameSave": "<PERSON><PERSON>", "settingsClaimUsernameDone": "An gama", "settingsClaimUsernameWatching": "<PERSON>", "settingsClaimUsernameNoOfAccounts": "<PERSON><PERSON><PERSON> {{noOfAccounts}}", "settingsClaimUsernameNoOfAccountsSingular": "Asusu 1", "settingsClaimUsernameEmptyAccounts": "Ba asusu", "settingsClaimUsernameSettingTitle": "<PERSON><PERSON> @username naka", "settingsClaimUsernameSettingDescription": "<PERSON><PERSON><PERSON> musamman ga walat dinka", "settingsManageUserProfileAbout": "Game da", "settingsManageUserProfileAboutUsername": "<PERSON><PERSON> mai amfani", "settingsManageUserProfileAboutBio": "<PERSON><PERSON><PERSON> waye kai", "settingsManageUserProfileTitle": "<PERSON><PERSON>", "settingsManageUserProfileManage": "<PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "<PERSON><PERSON>", "settingsManageUserProfileAuthFactorsDescription": "Zaɓi kalmomin sirri ko Mabudin sirri da zai iya shiga asusun ka na Phantom.", "settingsManageUserProfileUpdateAuthFactorsToast": "An sabunta tsaro na musamman!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Sabunta tsaro na musamman bai yiwu ba!", "settingsManageUserProfileBiography": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileBiographyDescription": "Ƙara gajeren bayanin waye kai a frofayil dinka", "settingsManageUserProfileUpdateBiographyToast": "An Sabunta Bayanin Waye Kai!", "settingsManageUserProfileUpdateBiographyToastFailure": "<PERSON><PERSON>. <PERSON><PERSON>", "settingsManageUserProfileBiographyNoUrlMessage": "Da fatan zaka cire duk IRLs a bayanin waye kai", "settingsManageUserProfileLinkedWallets": "<PERSON><PERSON><PERSON> da aka haɗe", "settingsManageUserProfileLinkedWalletsDescription": "Zaɓi Walat da zai bayyana a wasu na'urori san ɗa kake shiga asusun Phantom dinka.", "settingsManageUserProfileUpdateLinkedWalletsToast": "An sabunta walat da aka haɗa!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "<PERSON><PERSON><PERSON> walat da aka haɗa bai yiwu ba!", "settingsManageUserProfilePrivacy": "<PERSON><PERSON> kare sirri", "settingsManageUserProfileUpdatePrivacyStateToast": "An sabunta dokar sirri!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Sabunta dokar sirri bai yiwu ba!", "settingsManageUserProfilePublicAddresses": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileUpdatePublicAddressToast": "An sabunta adireshin fili!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Sabunta ad<PERSON>hin fili bai yiwu ba!", "settingsManageUserProfilePrivacyStatePublic": "<PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePublicDescription": "<PERSON>wa zai iya binciken frofayil da adireshin fili naka", "settingsManageUserProfilePrivacyStatePrivate": "<PERSON> <PERSON>ri", "settingsManageUserProfilePrivacyStatePrivateDescription": "Kowa zai iya binciken frofayil dinka amma wanin ka sai sun nemi iznin ganin frofayil da adireshin fili naka", "settingsManageUserProfilePrivacyStateInvisible": "Baya ganuwa", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Fr<PERSON><PERSON><PERSON> da adireshin fili naka na a ɓoye kuma baza iya gano su ba a ko'ina", "settingsDownloadPhantom": "<PERSON>uke <PERSON>", "settingsLogOut": "Fita", "seedlessAddAWalletPrimaryText": "<PERSON> wa<PERSON>", "seedlessAddAWalletSecondaryText": "<PERSON>ga ko dauko walat da ke akwai ", "seedlessAddSeedlessWalletPrimaryText": "<PERSON> as<PERSON>u mara makulli", "seedlessAddSeedlessWalletSecondaryText": "<PERSON> amfani da shedar Apple, Google ko Imel", "seedlessCreateNewWalletPrimaryText": "A kirkiri sabon walat?", "seedlessCreateNewWalletSecondaryText": "Wannan imel ba shi da walat, kana so ka kirkiro wani?", "seedlessCreateNewWalletButtonText": "<PERSON><PERSON>", "seedlessCreateNewWalletNoBundlePrimaryText": "Ba a ga walat ba", "seedlessCreateNewWalletNoBundleSecondaryText": "<PERSON>n imel ba shi da walat", "seedlessCreateNewWalletNoBundleButtonText": "<PERSON><PERSON> baya", "seedlessEmailOptionsPrimaryText": "Zaɓi <PERSON><PERSON>", "seedlessEmailOptionsSecondaryText": "<PERSON> wani <PERSON>t da asusun Google ko Apple dinka ", "seedlessEmailOptionsButtonText": "Cigaba da Imel", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "<PERSON><PERSON> wa<PERSON> da <PERSON>", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "<PERSON><PERSON> walat da imel dinka na Google", "seedlessAlreadyExistsPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessAlreadyExistsSecondaryText": "Wannan imel na da asusu da aka kirk<PERSON>, ko zaka so ka shiga?", "seedlessSignUpWithAppleButtonText": "<PERSON> rijista da <PERSON>", "seedlessContinueWithAppleButtonText": "Cigaba da Apple", "seedlessSignUpWithGoogleButtonText": "<PERSON> rijista da <PERSON>", "seedlessContinueWithGoogleButtonText": "Cigaba da Google", "seedlessCreateAPinPrimaryText": "<PERSON>iri <PERSON>", "seedlessCreateAPinSecondaryText": "An yi amfani da wannan don tsare walat dinka a duk na'urorinka <1> baza a iya maido da wannan ba.</1>", "seedlessContinueText": "Cigaba", "seedlessConfirmPinPrimaryText": "<PERSON><PERSON><PERSON> da PIN dinka", "seedlessConfirmPinSecondaryText": "In ka manta wannan PIN ba zaka iya maido da walat dinka a sabon na'ura ba.", "seedlessConfirmPinButtonText": "<PERSON>iri <PERSON>", "seedlessConfirmPinError": "Pin ba daidai ba. <PERSON><PERSON> gwa<PERSON>wa", "seedlessAccountsImportedPrimaryText": "An shigo da asusu", "seedlessAccountsImportedSecondaryText": "Za a shigar da wadannan asusu kai tsaye cikin walat dinka", "seedlessPreviouslyImportedTag": "An shigo da su a baya", "seedlessEnterPinPrimaryText": "<PERSON><PERSON> da PIN dinka", "seedlessEnterPinInvalidPinError": "An shigar da PIN mara kyau. Lambobi 4 kaɗai aka ba da dama", "seedlessEnterPinNumTriesLeft": "<PERSON><PERSON> y<PERSON> {{numTries}}.", "seedlessEnterPinCooldown": "Sake gwadawa nan da {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "Dole PIN ya kasance lambobi 4", "seedlessEnterPinMatch": "PIN sun yi daidai", "seedlessDoneText": "An gama", "seedlessEnterPinToSign": "<PERSON><PERSON> da PIN dinka don sa hannu a wannan ciniki", "seedlessSigning": "Sa hannu", "seedlessCreateSeed": "<PERSON><PERSON> walat mai kalmomin sirri", "seedlessImportOptions": "<PERSON><PERSON> zaɓin <PERSON>wa", "seedlessImportPrimaryText": "Zaɓin Shi<PERSON>wa", "seedlessImportSecondaryText": "<PERSON><PERSON> da walat da akwai da kalmomin sirri naka, <PERSON><PERSON><PERSON> ko walat din hadwaya", "seedlessImportSeedPhrase": "<PERSON><PERSON> da Kalmomin <PERSON>", "seedlessImportPrivateKey": "<PERSON><PERSON>", "seedlessConnectHardwareWallet": "Haɗa Walat <PERSON>", "seedlessTryAgain": "<PERSON>ke gwa<PERSON>wa", "seedlessCreatingWalletPrimaryText": "<PERSON> ƙirƙirar walat", "seedlessCreatingWalletSecondaryText": "<PERSON> kara walat na zumunta", "seedlessLoadingWalletPrimaryText": "<PERSON> koda walat", "seedlessLoadingWalletSecondaryText": "<PERSON> shigowa da kallon walat dinka da aka haɗa", "seedlessLoadingWalletErrorPrimaryText": "Ba a iya koda walat ba", "seedlessCreatingWalletErrorPrimaryText": "Ba a iya kirkiro walat ba", "seedlessErrorSecondaryText": "Da fatan za a sake gwadawa", "seedlessAuthAlreadyExistsErrorText": "<PERSON><PERSON> aka bayar mallakar wani asusun Phantom daban ne da ma", "seedlessAuthUnknownErrorText": "An sami matsala da ba a sani ba, da fatan za a sake gwadawa anjima", "seedlessAuthUnknownErrorTextRefresh": "An sami matsala da ba a gano ba, da fatan za a sake gwadawa anjima. Sabunta shafin don kara gwadawa.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON>", "seedlessWalletExistsErrorPrimaryText": "Da ma akwai asusun zumunta a na'urarka", "seedlessWalletExistsErrorSecondaryText": "Da fatan zaka koma baya ko la kulle sikirin", "seedlessValueProp1PrimaryText": "<PERSON><PERSON><PERSON> mai sauki", "seedlessValueProp1SecondaryText": "<PERSON><PERSON> walat ta hanyar amfani da asusun Google ko Apple ka fara amfani da web3 cikin sauki", "seedlessValueProp2PrimaryText": "<PERSON><PERSON>", "seedlessValueProp2SecondaryText": "An adana walat dinka cikin tsaro kuma an rarraba shi tsakanin abu<PERSON> da dama", "seedlessValueProp3PrimaryText": "Sauƙin maidowa", "seedlessValueProp3SecondaryText": "Dawo da aiki da walat dinka da asusun Google ko Apple da kuma PIN mai lambobin 4", "seedlessLoggingIn": "<PERSON> shigowa...", "seedlessSignUpOrLogin": "Yi Rijista ko Shiga", "seedlessContinueByEnteringYourEmail": "Cigaba ta hanayar shigar da imel dinka", "seedless": "<PERSON> ma<PERSON>", "seed": "<PERSON><PERSON><PERSON><PERSON>", "seedlessVerifyPinPrimaryText": "<PERSON><PERSON><PERSON> da PIN", "seedlessVerifyPinSecondaryText": "Da fatan zaka shigar da lambar PIN dinka domin cigaba", "seedlessVerifyPinVerifyButtonText": "Tabbatar", "seedlessVerifyPinForgotButtonText": "An forgot?", "seedlessPinConfirmButtonText": "Tabbatar", "seedlessVerifyToastPrimaryText": "Tabbatar Da PIN Dinka", "seedlessVerifyToastSecondaryText": "<PERSON><PERSON>u nemi ka tabbatar da PIN dinka a kai a kai don ka tuna. In ka manta, ba zaka iya dawo da walat dinka ba.", "seedlessVerifyToastSuccessText": "An tabbatar da lambar PIN dinka!", "seedlessForgotPinPrimaryText": "<PERSON>ke saita <PERSON> da wata na'ura", "seedlessForgotPinSecondaryText": "<PERSON><PERSON>, zaka iya sake saita PIN dinka sauran na'urori ne kaɗai in ka shiga cikinsu", "seedlessForgotPinInstruction1PrimaryText": "<PERSON><PERSON>", "seedlessForgotPinInstruction1SecondaryText": "Je ka sauran na'uran da ka shiga asusun Phantom da imel dinka", "seedlessForgotPinInstruction2PrimaryText": "Je ka saituna", "seedlessForgotPinInstruction2SecondaryText": "A saituna, zaɓi \" <PERSON><PERSON> & <PERSON>ri\" sannan zaɓi \"Canza PIN\"", "seedlessForgotPinInstruction3PrimaryText": "<PERSON><PERSON>", "seedlessForgotPinInstruction3SecondaryText": "Da zarar ka saita PIN dinka, zaka iya shiga walat dinka a wannan na'ura", "seedlessForgotPinButtonText": "Na yi wadannan matakai", "seedlessResetPinPrimaryText": "<PERSON><PERSON> PIN", "seedlessResetPinSecondaryText": "<PERSON><PERSON> da sabon PIN da zaka tuna. Ana amfani da wannan don tsare walat dinka a duk na'urorinka", "seedlessResetPinSuccessText": "An sabunta PIN dinka!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Da zarar ka kirkiri walat, ka yarda kenan da <1>Dokokin aiki </1>da<5><PERSON>'<PERSON><PERSON><PERSON> sirri </5>", "pageNotFound": "Ba a sami wani shafi ba", "pageNotFoundDescription": "<PERSON>mu gudu da kudinku ba! <PERSON> wannan shafin, ko an cire shi.", "webTokenPagesLegalDisclaimer": "<PERSON> fitar da bayanan farashi ne don ilman<PERSON>wa kaɗai, ba shaw<PERSON><PERSON> kasu<PERSON> ba ne. <PERSON><PERSON> ne ke fitar da kidid<PERSON> kasuwa, <PERSON> ba ta cewa bayanan daidai ne.", "signUpOrLogin": "Yi rijista ko shiga", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Da zarar ka kirkiri asusu, ka yarda kenan da <1>Dokokin aiki</1> da<5><PERSON>'<PERSON><PERSON><PERSON> sirri</5>", "feedNoActivity": "<PERSON> aiki har yanzu", "followRequests": "Bi Buƙatu", "following": "Da ke biye", "followers": "<PERSON><PERSON><PERSON>", "follower": "<PERSON><PERSON><PERSON>", "joined": "An shiga", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON>", "noFollowing": "<PERSON>", "noUsersFound": "Ba A Ga Masu Amfani Ba", "viewProfile": "<PERSON><PERSON>", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}