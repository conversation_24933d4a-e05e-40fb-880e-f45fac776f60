{"accountHeaderConnectedInterpolated": "Sei connesso a {{origin}}", "accountHeaderConnectedToSite": "Sei connesso a questo sito", "accountHeaderCopied": "Copiato!", "accountHeaderCopyToClipboard": "Copia negli appunti", "accountHeaderNotConnected": "Non sei connesso a", "accountHeaderNotConnectedInterpolated": "Non sei connesso a {{origin}}", "accountHeaderNotConnectedToSite": "Non sei connesso a questo sito", "addAccountActionButtonClose": "<PERSON><PERSON>", "addAccountHardwareWalletPrimaryText": "Collega portafoglio hardware", "addAccountHardwareWalletSecondaryText": "<PERSON>a il tuo portaf<PERSON>lio <PERSON>", "addAccountHardwareWalletSecondaryTextMobile": "Usa il tuo portafoglio Ledger Nano S/X", "addAccountImportWalletPrimaryText": "Importa chiave privata", "addAccountImportWalletSecondaryText": "Importa un portafoglio esistente", "addAccountNewWalletPrimaryText": "Crea un nuovo portafoglio", "addAccountNewWalletSecondaryText": "Genera un nuovo indirizzo di portafoglio", "addAccountPrimaryText": "Aggiungi/Collega portafoglio", "addAccountImportAccountActionButtonImport": "Importa", "addAccountImportAccountDuplicatePrivateKey": "Questo account esiste già nel tuo portafoglio", "addAccountImportAccountIncorrectFormat": "Formato errato", "addAccountImportAccountInvalidPrivateKey": "Chiave privata non valida", "addAccountImportAccountName": "Nome", "addAccountImportAccountPrimaryText": "Importa chiave privata", "addAccountImportAccountPrivateKey": "Chiave privata", "addAccountImportAccountPrivateKeyRequired": "La chiave privata è obbligatoria", "addAddressActionButtonPrimary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAddressActionButtonSecondary": "<PERSON><PERSON><PERSON>", "addAddressAddressAlreadyAdded": "L'indirizzo è già stato aggiunto", "addAddressAddressAlreadyExists": "L'indirizzo esiste già", "addAddressAddressIsRequired": "L'indirizzo è obbligatorio", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "L'etichetta è obbligatoria", "addAddressLabelPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "addAddressPrimaryText": "Aggiungi indirizzo", "addEditTokenActionButtonAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEditTokenActionButtonCancel": "<PERSON><PERSON><PERSON>", "addEditTokenActionButtonSave": "<PERSON><PERSON>", "addEditTokenAddMetadata": "Aggiungi metadati del token", "addEditTokenEditMetadata": "Modifica metadati del token", "addEditTokenErrorAccountNotFound": "Impossibile trovare l'account token", "addEditTokenErrorDuplicateToken": "Hai già questo token", "addEditTokenErrorInvalidMint": "Indirizzo di zecca non valido", "addEditTokenErrorInvalidName": "Nome non valido", "addEditTokenErrorInvalidSymbol": "Simbolo non valido", "addEditTokenMintAddress": "Indirizzo di zecca", "addEditTokenName": "Nome", "addEditTokenSymbol": "Simbolo", "addEditTokenThisWillCost": "Costerà", "addEditTokenThisWillCostInterpolated": "Costerà {{amount}} SOL", "assetDetailActionButtonDeposit": "Versa", "assetDetailActionButtonSend": "Invia", "assetDetailButtonCancel": "<PERSON><PERSON><PERSON>", "assetDetailEditTokenMetadata": "Modifica metadati del token", "assetDetailRecentActivity": "Attività recente", "assetDetailStakeSOL": "Stake SOL", "assetDetailUnknownToken": "<PERSON><PERSON> s<PERSON>", "assetDetailUnwrapAll": "<PERSON><PERSON><PERSON><PERSON> tutto", "assetDetailViewOnExplorer": "<PERSON>ra su <PERSON>", "assetDetailViewOnSolscan": "Mostra su Solscan", "assetListAddCustomToken": "Aggiungi token personalizzato", "assetListSearch": "Cerca...", "assetListUnknownToken": "<PERSON><PERSON> s<PERSON>", "assetSelectionClose": "<PERSON><PERSON>", "assetVisibilityClose": "<PERSON><PERSON>", "assetVisibilityUnknownToken": "<PERSON><PERSON> s<PERSON>", "blocklistConnectionActionButtonClose": "<PERSON><PERSON>", "blocklistConnectionDisabled": "Phantom ritiene che questo sito Web sia dannoso e non sicuro da usare. Abbiamo disabilitato la possibilità d'interagire con esso per proteggere te e i tuoi fondi.", "blocklistConnectionIgnoreWarning": "Ignora l'avviso, connetti comunque", "blocklistOriginCommunityDatabaseInterpolated": "Questo sito è stato segnalato come parte di un <1>database gestito dalla community</1> di siti Web di phishing e truffe noti. Se ritieni che il sito sia stato segnalato per errore, <3>segnala un problema</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} è bloccato!", "blocklistOriginIgnoreWarning": "Ignora l'avviso, portami comunque a {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom ritiene che questo sito Web sia dannoso e non sicuro da usare.", "blocklistOriginThisDomain": "questo dominio", "maliciousTransactionWarningButtonClose": "<PERSON><PERSON>", "maliciousTransactionWarning": "Phantom ritiene che questa transazione sia dannosa e non sicura da firmare. Abbiamo disabilitato la possibilità di firmarlo per proteggere te e i tuoi fondi.", "maliciousTransactionWarningIgnoreWarning": "Ignora l'avviso, procedi comunque", "maliciousTransactionWarningTitle": "Transazione segnalata!", "changeLockTimerActionButtonPrimary": "<PERSON><PERSON>", "changeLockTimerActionButtonSecondary": "<PERSON><PERSON><PERSON>", "changeLockTimerPrimaryText": "Timer di blocco automatico", "changeLockTimerSecondaryText": "Quanto tempo dobbiamo aspettare per bloccare il tuo portafoglio dopo che è rimasto inattivo?", "changePasswordActionButtonPrimary": "<PERSON><PERSON>", "changePasswordActionButtonSecondary": "<PERSON><PERSON><PERSON>", "changePasswordConfirmNewPassword": "Conferma la nuova password", "changePasswordCurrentPassword": "Password attuale", "changePasswordErrorIncorrectCurrentPassword": "Password attuale errata", "changePasswordErrorGeneric": "Qualcosa è andato storto, riprova più tardi", "changePasswordNewPassword": "Nuova password", "changePasswordPrimaryText": "Cambia la password", "collectibleDetailDescription": "Descrizione", "collectibleDetailProperties": "Proprietà", "collectibleDetailSend": "Invia", "collectibleDetailViewOnSolscan": "Mostra su Solscan", "collectibleDisplayLoading": "Caricamento...", "collectiblesNoCollectibles": "<PERSON>essun colleziona<PERSON>e", "collectiblesPrimaryText": "I tuoi collezionabili", "collectiblesReceiveCollectible": "Ricevi collezionabile", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato. Una volta rilevato, puoi scegliere quale indirizzo desideri utilizzare.", "connectHardwareContinueActionButtonText": "Continua", "connectHardwareFailedPrimaryText": "Connessione fallita", "connectHardwareFailedRetryActionButtonText": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareFailedSecondaryText": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato. Una volta rilevato, puoi scegliere quale indirizzo utilizzare.", "connectHardwareFinishPrimaryText": "Account aggiunto!", "connectHardwareFinishSecondaryText": "Ora puoi accedere al tuo portafoglio Ledger Nano da Phantom. Torna all'estensione.", "connectHardwareNeedsPermissionPrimaryText": "Connetti un nuovo portafoglio", "connectHardwareNeedsPermissionSecondaryText": "Clicca sul pulsante in basso per avviare il processo di connessione.", "connectHardwareSearchingPrimaryText": "Ricerca portafoglio...", "connectHardwareSearchingSecondaryText": "Collega il tuo portafoglio hardware, assicurati che sia sbloccato e di avere le autorizzazioni approvate nel tuo browser.", "connectHardwareSelectAddressAllAddressesImported": "Tutti gli indirizzi importati", "connectHardwareSelectAddressDerivationPath": "Percorso di derivazione", "connectHardwareSelectAddressSearching": "Ricerca...", "connectHardwareSelectAddressSelectWalletAddress": "Seleziona l'indirizzo del portafoglio", "connectHardwareSelectAddressWalletAddress": "Indirizzo del portafoglio", "connectHardwareWaitingForApplicationPrimaryText": "Apri l'app Solana sul tuo Ledger", "connectHardwareWaitingForApplicationSecondaryText": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato.", "connectHardwareWaitingForPermissionPrimaryText": "Serve il permesso", "connectHardwareWaitingForPermissionSecondaryText": "Collega il tuo portafoglio hardware, assicurati che sia sbloccato e di avere le autorizzazioni approvate nel tuo browser.", "assetQueriesUnableToConnect": "Non riusciamo a connetterci a Solana", "assetQueriesUnableToFetchTokenPrices": "Non siamo riusciti a recuperare i prezzi dei token", "connectionClusterInterpolated": "Attualmente sei su {{cluster}}", "copyDefaultCopyText": "Copia", "copyCopiedText": "Copiato!", "depositAssetActionButtonClose": "<PERSON><PERSON>", "depositAssetBuyWithMoonpay": "Acquista con Moon<PERSON>", "depositAssetDeposit": "Versamento", "depositAssetDepositInterpolated": "Versa {{tokenSymbol}}", "depositAssetFTXTooltipLabel": "Trasferisci token SOL e SPL direttamente dal tuo account FTX.us.", "depositAssetIntermediateDepositActionButtonClose": "<PERSON><PERSON>", "depositAssetIntermediateDepositDeposit": "Versa", "depositAssetMoonPayTooltipLabel": "Acquista facilmente SOL con una carta di debito, carta di credito o bonifico bancario.", "depositAssetPrimaryText": "Versa", "depositAssetSecondaryText": "<PERSON><PERSON> indirizzo può essere utilizzato solo per ricevere token SOL e SPL su Solana.", "depositAssetSendFrom": "Invia dal portafoglio / exchange", "depositAssetTransferFromFTX": "Trasferisci da FTX", "depositAssetShareAddress": "Condividi indirizzo", "depositFlowActionButtonClose": "<PERSON><PERSON>", "depositRowDepositSOL": "Versa SOL", "depositRowDepositDisclaimer": "SOL viene utilizzato per pagare le transazioni", "editAddressActionButtonCancel": "<PERSON><PERSON><PERSON>", "editAddressActionButtonSave": "<PERSON><PERSON>", "editAddressAddressAlreadyAdded": "L'indirizzo è già stato aggiunto", "editAddressAddressAlreadyExists": "L'indirizzo esiste già", "editAddressAddressIsRequired": "L'indirizzo è obbligatorio", "editAddressPrimaryText": "Modifica indirizzo", "editAddressRemove": "<PERSON><PERSON><PERSON><PERSON> dalla rubrica", "exportSecretActionButtonDone": "<PERSON><PERSON>", "exportSecretActionButtonPrimary": "<PERSON><PERSON>", "exportSecretActionButtonSecondary": "<PERSON><PERSON><PERSON>", "exportSecretErrorGeneric": "Qualcosa è andato storto, riprova più tardi", "exportSecretErrorIncorrectPassword": "Password non corretta", "exportSecretPassword": "Password", "exportSecretPrivateKey": "chiave privata", "exportSecretSecretPhrase": "frase segreta", "exportSecretSecretRecoveryPhrase": "frase di recupero segreta", "exportSecretShowPrivateKey": "Mostra chiave privata", "exportSecretShowSecretRecoveryPhrase": "Mostra frase di recupero segreta", "exportSecretWarningPrimaryInterpolated": "<1>Non</1> condividere la tua {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "Se qualcuno scopre la tua {{secretNameText}} avrà il pieno controllo del tuo portafoglio.", "exportSecretYourPrivateKey": "La tua chiave privata", "exportSecretYourSecretRecoveryPhrase": "La tua frase di recupero segreta", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "<PERSON><PERSON>", "homeManageTokenList": "Gestisci l'elenco dei token", "homeDeposit": "Versa", "homeSend": "Invia", "ledgerActionActionButtonCancel": "<PERSON><PERSON><PERSON>", "ledgerActionActionButtonContinue": "Continua", "ledgerActionApprove": "Approva sul tuo Ledger <PERSON>", "ledgerActionActionButtonRetry": "<PERSON><PERSON><PERSON><PERSON>", "ledgerActionErrorBlindSignDisabledPrimaryText": "Firma cieca disabilitata", "ledgerActionErrorBlindSignDisabledSecondaryText": "Assicurati che la firma cieca sia abilitata sul tuo dispositivo hardware e riprova l'azione", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Dispositivo hardware disconnesso durante l'operazione", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Chiudi l'estensione Phantom e riprova l'azione", "ledgerActionErrorDeviceLockedPrimaryText": "Dispositivo hardware bloccato", "ledgerActionErrorDeviceLockedSecondaryText": "Sblocca il dispositivo hardware e riprova", "ledgerActionErrorHeader": "Errore di azione nel Ledger", "ledgerActionErrorUserRejectionPrimaryText": "Transazione rifiutata dall'utente", "ledgerActionErrorUserRejectionSecondaryText": "L'azione è stata rifiutata sul dispositivo hardware dall'utente", "ledgerActionNeedPermission": "Serve il permesso", "ledgerActionNeedToApprove": "Devi approvare la transazione sul tuo portafoglio hardware. Assicurati che sia sbloccato e presente sull'app Solana", "ledgerActionPleaseConnect": "Collega il tuo Ledger Nano", "ledgerActionPleaseConnectAndApprove": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato. Assicurati di avere le autorizzazioni approvate nel tuo browser.", "maxInputAmount": "Importo", "maxInputMax": "Max", "notEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON>", "notEnoughSolPrimaryText": "SOL insufficienti", "notEnoughSolSecondaryText": "Non hai abbastanza SOL nel tuo portafoglio per pagare la commissione di transazione. Versane di più e riprova.", "notificationApplicationApprovalPermissionsPrimary": "Questa app vorrebbe:", "notificationApplicationApprovalPermissionsTransactionApproval": "Richiedere l'approvazione per le transazioni", "notificationApplicationApprovalPermissionsViewWalletActivity": "Visualizzare il saldo e l'attività del tuo portafoglio", "notificationApplicationApprovalActionButtonConnect": "Collega", "notificationApplicationApprovalActionButtonCancel": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "Consentire al sito di connettersi?", "notificationApplicationApprovalAutoApprove": "Approva automaticamente le transazioni", "notificationApplicationApprovalConnectDisclaimer": "Collegati solo a siti Web di cui ti fidi", "notificationSignatureRequestApproveTransaction": "Approva transazione", "notificationSignatureRequestApproveTransactionCapitalized": "Approva transazione", "notificationSignatureRequestSignatureRequest": "Richiesta di firma", "notificationTransactionApprovalActionButtonApprove": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonCancel": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatedBalanceChanges": "Cambiamenti di saldo stimati", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Le stime si basano su simulazioni di transazioni e non costituiscono una garanzia", "notificationTransactionApprovalHideAdvancedDetails": "Nascondi i dettagli avanzati delle transazioni", "notificationTransactionApprovalNetworkFee": "Commissione di rete", "notificationTransactionApprovalNoBalanceChanges": "Nessuna modifica del saldo trovata", "notificationTransactionApprovalSolanaAmountRequired": "Importo richiesto dalla rete Solana per elaborare la transazione", "notificationTransactionApprovalTransactionMayFailToConfirm": "La transazione potrebbe non essere confermata", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Impossibile recuperare le modifiche al saldo", "notificationTransactionApprovalViewAdvancedDetails": "Mostra i dettagli avanzati delle transazioni", "notificationTransactionApprovalSignUnableToSimulate": "Questa transazione non può essere simulata. L'approvazione può comportare la perdita di fondi.", "notificationTransactionApprovalKnownMalicious": "Questa transazione è dannosa. La firma comporterà la perdita di fondi.", "notificationTransactionApprovalSuspectedMalicious": "Sospettiamo che questa transazione sia dannosa. L'approvazione può comportare la perdita di fondi.", "onboardingCreatePassword": "Crea una password", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Accetto i <1>Termini di servizio</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Conferma la password", "onboardingCreatePasswordContinue": "Continua", "onboardingCreatePasswordDescription": "La userai per sbloccare il tuo portafoglio.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Frase di recupero segreta non valida", "onboardingCreatePasswordPasswordPlaceholder": "Password", "onboardingCreatePasswordPasswordStrengthWeak": "De<PERSON>e", "onboardingCreatePasswordPasswordStrengthMedium": "Media", "onboardingCreatePasswordPasswordStrengthStrong": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseContinue": "Continua", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Ho salvato la mia frase di recupero segreta", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Frase di recupero segreta", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Questa frase è l'UNICO modo per recuperare il tuo portafoglio. NON condividerla con nessuno!", "onboardingCreateRecoveryPhraseSaveIn1Password": "<PERSON><PERSON> in 1Password", "onboardingCreateRecoveryPhraseSaved": "<PERSON><PERSON><PERSON>!", "onboardingImportWallet": "Importa portafoglio", "onboardingImportWalletImportExistingWallet": "Importa un portafoglio esistente con la tua frase di recupero segreta di 12 o 24 parole.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON><PERSON><PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "Frase di recupero segreta", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Frase di recupero segreta non valida", "onboardingImportWalletIHaveWords": "Ho una frase di recupero di {{numWords}} parole", "onboardingImportWalletScrollDown": "<PERSON><PERSON><PERSON> in basso", "onboardingImportWalletScrollUp": "<PERSON><PERSON><PERSON> in alto", "onboardingKeyboardShortcut": "Scorciatoia da tastiera", "onboardingKeyboardShortcutContinue": "Continua", "onboardingKeyboardShortcutDescription": "Puoi aprire Phantom in qualsiasi momento utilizzando questa pratica scorciatoia da tastiera.", "onboardingKeyboardShortcutMac": "Opzione + Maiusc + P", "onboardingKeyboardShortcutNotMac": "Alt + Maiusc + P", "onboardingKeyboardShortcutTry": "Prova:", "onboardingPathSelectionCreateWallet": "Crea un nuovo portafoglio", "onboardingPathSelectionTagline": "Un portafoglio crittografico reinventato per DeFi e NFT", "onboardingPathSelectionIHaveAWallet": "Ho già un portafoglio", "onboardingSelectAccountsImportAccounts": "Importa account", "onboardingSelectAccountsImportAccountsDescription": "Scegli gli account portafoglio da importare.", "onboardingSelectAccountsImportSelectedAccounts": "Importa account selezionati", "onboardingSocialsFinishAction": "Fine", "onboardingSocialsFinished": "<PERSON><PERSON>!", "onboardingSocialsFinishedDescription": "Segui gli aggiornamenti del prodotto o contattaci in caso di domande.", "onboardingSocialsFollowOnTwitter": "<PERSON><PERSON><PERSON> su <PERSON>", "onboardingSocialsVisitHelpCenter": "Visita il centro assistenza", "recentActivityPrimaryText": "Attività recente", "removeAccountActionButtonCancel": "<PERSON><PERSON><PERSON>", "removeAccountActionButtonRemove": "<PERSON><PERSON><PERSON><PERSON>", "removeAccountRemoveWallet": "<PERSON><PERSON><PERSON><PERSON>af<PERSON>lio", "removeAccountWarningLedger": "Anche se stai rimuovendo questo portafoglio da Phantom, potrai aggiungerlo nuovamente utilizzando la procedura \"Collega portafoglio hardware\".", "removeAccountWarningPrivateKey": "Una volta rimosso questo port<PERSON>, Phantom non potrà più recuperarlo per te. Assicurati di aver eseguito il backup della tua chiave privata.", "removeAccountWarningSeed": "Anche se stai rimuovendo questo portafoglio da Phantom, potrai derivarlo nuovamente utilizzando il tuo mnemonico in questo o in un altro portafoglio.", "resetSeedActionButtonPrimary": "Continua", "resetSeedActionButtonSecondary": "<PERSON><PERSON><PERSON>", "resetSeedPrimaryText": "Reimpostazione della tua frase di recupero segreta", "resetSeedSecondaryText": "Questo rimuoverà tutti i portafogli esistenti e li sostituirà con quelli nuovi. Assicurati di aver eseguito il backup della frase segreta e delle chiavi private esistenti.", "richTransactionsDays": "<PERSON>ior<PERSON>", "richTransactionsToday": "<PERSON><PERSON><PERSON>", "richTransactionsYesterday": "<PERSON><PERSON>", "richTransactionDetailAccount": "Account", "richTransactionDetailAt": "alle", "richTransactionDetailCompleted": "Completata", "richTransactionDetailConfirmed": "Confermata", "richTransactionDetailAppInteraction": "Interazione con l'app", "richTransactionDetailDate": "Data", "richTransactionDetailFailed": "Non riuscita", "richTransactionDetailFrom": "Da", "richTransactionDetailNetworkFee": "Commissione di rete", "richTransactionDetailPending": "In sospeso", "richTransactionDetailProvider": "Fornitore", "richTransactionDetailReceived": "Ricevuto", "richTransactionDetailSent": "Inviato", "richTransactionDetailStaked": "In stake", "richTransactionDetailStatus": "Stato", "richTransactionDetailSwap": "Scambio", "richTransactionDetailSwapDetails": "<PERSON><PERSON><PERSON> scambio", "richTransactionDetailTo": "A", "richTransactionDetailTokenSwap": "Scambio di token", "richTransactionDetailUnknownNFT": "NFT sconosciuto", "richTransactionDetailUnstaked": "<PERSON><PERSON><PERSON> da <PERSON>", "richTransactionDetailValidator": "Validator", "richTransactionDetailViewOnSolscan": "Mostra su Solscan", "richTransactionDetailWithdrawStake": "Withdraw Stake", "richTransactionDetailYouPaid": "<PERSON> pagato", "richTransactionDetailYouReceived": "Hai ricevuto", "sendAddressBookButtonLabel": "Rubrica", "addressBookSelectAddressBook": "Rubrica", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON><PERSON> in<PERSON> sal<PERSON>", "sendAddressBookRecentlyUsed": "Usati di recente", "addressBookSelectRecentlyUsed": "Usati di recente", "sendConfirmationActionButtonCancel": "<PERSON><PERSON><PERSON>", "sendConfirmationActionButtonSend": "Invia", "sendConfirmationLabel": "<PERSON><PERSON><PERSON><PERSON>", "sendConfirmationMessage": "Messaggio", "sendConfirmationNetworkFee": "Commissione di rete", "sendConfirmationPrimaryText": "Invio conferma", "sendStatusErrorActionButtonCancel": "<PERSON><PERSON><PERSON>", "sendStatusErrorActionButtonRetry": "<PERSON><PERSON><PERSON><PERSON>", "sendStatusErrorMessageInterpolated": "Si è verificato un errore durante il tentativo di inviare i token a <1>{{uiRecipient}}</1>", "sendStatusErrorTitle": "Impossibile inviare", "sendStatusLoadingTitle": "Invio...", "sendStatusSuccessClose": "<PERSON><PERSON>", "sendStatusSuccessMessageInterpolated": "I tuoi token sono stati inviati correttamente a <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Inviati!", "sendFormActionButtonNext": "<PERSON><PERSON>", "sendFormActionButtonCancel": "<PERSON><PERSON><PERSON>", "sendFormErrorInsufficientBalance": "<PERSON><PERSON>", "sendFormErrorInvalidSolanaAddress": "Indirizzo di Solana non valido", "sendFormErrorInvalidTwitterHandle": "Questo handle di Twitter non è registrato", "sendFormErrorInvalidDomain": "Questo dominio non è registrato", "sendFormErrorMinRequiredInterpolated": "<PERSON><PERSON><PERSON> {{minAmount}} {{tokenName}}", "sendFormSend": "Invia", "sendRecipientTextareaPlaceholder": "Indirizzo SOL del destinatario", "sendSelectionActionButtonClose": "<PERSON><PERSON>", "settings": "Impostazioni", "settingsAddressBookNoLabel": "<PERSON><PERSON>", "settingsAddressBookPrimary": "Rubrica", "settingsAddressBookRecentlyUsed": "Usati di recente", "settingsAddressBookSecondary": "Gestisci gli indirizzi di uso comune", "settingsAutoLockTimerPrimary": "Timer di blocco automatico", "settingsAutoLockTimerSecondary": "Modifica la durata del timer di blocco automatico", "settingsChangeLanguagePrimary": "Cambia lingua", "settingsChangeLanguageSecondary": "Cambia la lingua di visualizzazione", "settingsChangeNetworkPrimary": "Cambia rete", "settingsChangeNetworkSecondary": "Configura le tue impostazioni di rete", "settingsChangePasswordPrimary": "Cambia password", "settingsChangePasswordSecondary": "Modifica la password della schermata di blocco", "settingsDisplayLanguage": "Lingua di visualizzazione", "settingsErrorCannotExportLedgerPrivateKey": "Impossibile esportare la chiave privata Ledger", "settingsErrorCannotRemoveAllWallets": "Impossibile rimuovere tutti i portafogli", "settingsExportPrivateKey": "Esporta chiave privata", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNoAddresses": "<PERSON><PERSON><PERSON>", "settingsNoTrustedApps": "Nessuna app affidabile", "settingsRemoveWallet": "<PERSON><PERSON><PERSON><PERSON>af<PERSON>lio", "settingsResetSecretRecoveryPhrase": "Resetta frase di recupero segreta", "settingsShowSecretRecoveryPhrase": "Mostra frase di recupero segreta", "settingsTrustedAppsAutoApprove": "Approvazione automatica", "settingsTrustedAppsDisclaimer": "Abilita l'approvazione automatica solo su siti attendibili", "settingsTrustedAppsPrimary": "App affidabili", "settingsTrustedAppsRevoke": "Revoca", "settingsTrustedAppsSecondary": "Configura le tue applicazioni affidabili", "stakeAccountCardActiveStake": "Stake attivo", "stakeAccountCardBalance": "<PERSON><PERSON>", "stakeAccountCardRentReserve": "Riserva di affitto", "stakeAccountCardRewards": "<PERSON><PERSON>", "stakeAccountCardStakeAccount": "Account di stake", "stakeAccountCreateAndDelegateErrorStaking": "Si è verificato un problema con lo stake di questo validatore. Riprova.", "stakeAccountCreateAndDelegateSolStaked": "SOL in stake!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "I tuoi SOL inizieranno a guadagnare premi <1></1> nei prossimi due giorni una volta che il conto di stake sarà attivo.", "stakeAccountCreateAndDelegateStakingFailed": "Stake non riuscito", "stakeAccountCreateAndDelegateStakingSol": "Stake SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Stiamo creando un account di staking, quindi delegheremo i tuoi SOL a", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Stiamo creando un account di staking, quindi delegheremo i tuoi SOL a {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Mostra transazione", "stakeAccountDeactivateStakeSolUnstaked": "SOL non più in stake!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Potrai ritirare il tuo stake <1></1> nei prossimi due giorni una volta che l'account di stake diventa inattivo.", "stakeAccountDeactivateStakeUnstakingFailed": "Rimozione da stake non riuscita", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Si è verificato un problema con la rimozione dello stake di questo validatore. Riprova.", "stakeAccountDeactivateStakeUnstakingSol": "Rimozione stake SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Stiamo avviando il processo per annullare lo stake dei tuoi SOL.", "stakeAccountDeactivateStakeViewTransaction": "Mostra transazione", "stakeAccountDelegateStakeSolStaked": "SOL in stake!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "I tuoi SOL inizieranno a guadagnare premi <1></1> nei prossimi due giorni una volta che il conto di stake sarà attivo.", "stakeAccountDelegateStakeStakingFailed": "Stake non riuscito", "stakeAccountDelegateStakeStakingFailedDescription": "Si è verificato un problema con lo stake di questo validatore. Riprova.", "stakeAccountDelegateStakeStakingSol": "Stake SOL...", "stakeAccountDelegateStakeStakingSolDescription": "Stiamo delegando i tuoi SOL.", "stakeAccountDelegateStakeViewTransaction": "Mostra transazione", "stakeAccountListActivationActivating": "Attivazione", "stakeAccountListActivationActive": "Attivo", "stakeAccountListActivationInactive": "Non attivo", "stakeAccountListActivationDeactivating": "In disattivazione", "stakeAccountListErrorFetching": "Si è verificato un problema durante il recupero degli account di stake:", "stakeAccountListNoStakingAccounts": "Nessun account di stake", "stakeAccountListReload": "Ricarica", "stakeAccountListViewPrimaryText": "Il tuo stake", "stakeAccountListViewStakeSOL": "Stake SOL", "stakeAccountViewActionButtonClose": "<PERSON><PERSON>", "stakeAccountViewActionButtonRestake": "Rifai stake", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "stakeAccountViewError": "Errore", "stakeAccountViewPrimaryText": "Il tuo stake", "stakeAccountViewRestake": "Rifai stake", "stakeAccountViewSOLCurrentlyStakedInterpolated": "I tuoi SOL sono attualmente in stake con un validatore. Dovrai annullare lo stake per <1></1>accedere a questi fondi. <3>Ulteriori informazioni</3>", "stakeAccountViewStakeInactive": {"part1": "Questo account di stake è inattivo. Considera la possibilità di ritirare lo stake o di trovare un validatore a cui delegare.", "part2": "Ulteriori informazioni"}, "stakeAccountViewStakeNotFound": "Impossibile trovare questo account di stake.", "stakeAccountViewViewOnExplorer": "<PERSON>ra su <PERSON>", "stakeAccountViewViewOnSolscan": "Mostra su Solscan", "stakeAccountViewWithdrawStake": "Ritira stake", "stakeAccountViewWithdrawUnstakedSOL": "Preleva SOL non in stake", "stakeAccountWithdrawStakeSolWithdrawn": "SOL prelevati!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "I tuoi SOL sono stati prelevati.", "part2": "Questo account di stake verrà automaticamente rimosso entro pochi minuti."}, "stakeAccountWithdrawStakeViewTransaction": "Mostra transazione", "stakeAccountWithdrawStakeWithdrawalFailed": "Prelievo fallito", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Si è verificato un problema col prelievo di questo account di stake. Riprova.", "stakeAccountWithdrawStakeWithdrawingSol": "Prelievo SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Stiamo prelevando i tuoi SOL da questo account di stake.", "startEarningSolAccount": "account", "startEarningSolAccounts": "account", "startEarningSolErrorClosePhantom": "Chiudi Phantom e riprova", "startEarningSolErrorTroubleLoading": "Problema durante il caricamento dello stake", "startEarningSolLoading": "Caricamento...", "startEarningSolPrimaryText": "Inizia a guadagnare SOL", "startEarningSolSearching": "Ricerca di account di stake in corso", "startEarningSolStakeTokens": "Fai stake dei token e guadagna premi", "startEarningSolYourStake": "Il tuo stake", "swapFeesEstimatedFees": "Commissioni stimate", "swapFeesRate": "Tasso", "swapFeesSlippage": "Slittamento", "swapFeesSlippageDisclaimer": "La tua transazione fallirà se il prezzo \ncambia sfavorevolmente più di questa percentuale.", "swapFeesSlippageTolerance": "Tolleranza allo slittamento", "swapFeesPriceImpact": "Impatto sul prezzo", "swapFeesPriceImpactDisclaimer": "La differenza tra il prezzo di mercato e il prezzo stimato in base alla dimensione dell'operazione.", "swapFlowYouPay": "<PERSON><PERSON><PERSON>", "swapFlowYouReceive": "<PERSON><PERSON>", "swapFlowActionButtonText": "Controlla l'ordine", "swapQuoteFeeDisclaimer": "La tariffa include una commissione Phantom del {{feePercentage}}", "swapQuoteMissingContext": "Contesto di scambio mancante", "swapQuoteErrorNoQuotes": "Stai cercando di scambiare senza contesto", "swapQuoteSolanaNetwork": "<PERSON><PERSON>", "swapQuoteOneTimeTokenAccount": "Account token una tantum", "swapReviewFlowActionButtonPrimary": "Scambio", "swapReviewFlowActionButtonSecondary": "<PERSON><PERSON><PERSON>", "swapReviewFlowPrimaryText": "Controlla l'ordine", "swapReviewFlowYouPay": "<PERSON><PERSON><PERSON>", "swapReviewFlowYouReceive": "<PERSON><PERSON>", "swapTxConfirmationActionButtonClose": "<PERSON><PERSON>", "swapTxConfirmationReceived": "Ricevuto!", "swapTxConfirmationSwapFailed": "<PERSON><PERSON><PERSON>", "swapTxConfirmationSwapFailedSlippageLimit": "Lo scambio ha raggiunto il limite di slittamento, riprova.", "swapTxConfirmationSwapFailedTryAgain": "Lo scambio non è riuscito, riprova", "swapTxConfirmationSwappingTokens": "Scambio di token...", "swapTxConfirmationTokens": "I token", "swapTxConfirmationTokensDeposited": "Fatto! I token sono stati versati nel tuo portafoglio", "swapTxConfirmationTokensWillBeDeposited": "verranno versati nel tuo portafoglio una volta completata la transazione", "swapTxConfirmationViewTransaction": "Mostra transazione", "swapperMax": "Max", "switchToggle": "Interruttore", "termsOfServiceActionButtonAgree": "Accetto", "termsOfServiceActionButtonCancel": "<PERSON><PERSON><PERSON>", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Facendo clic su <1>\"Accetto\"</1> accetti i <3>Termini e condizioni</3> relativi allo scambio di token con Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Abbiamo rivisto i nostri Termini di servizio. Facendo clic su <1>\"Accetto\"</1> accetti i nostri nuovi <3>Termini di servizio</3>.<5></5><6></6>I nostri nuovi Termini di servizio includono un nuova <8>struttura tariffaria</8> per determinati prodotti.", "termsOfServicePrimaryText": "Termini di servizio", "tokenRowUnknownToken": "<PERSON><PERSON> s<PERSON>", "transactionStatusDetailActionButtonClose": "<PERSON><PERSON>", "transactionsAppInteraction": "Interazione con l'app", "transactionsError": "Errore", "transactionsFailed": "Non riuscita", "transactionsFrom": "Da", "transactionsNoActivity": "<PERSON><PERSON><PERSON> attivit<PERSON>", "transactionsReceived": "Rice<PERSON><PERSON>", "transactionsReceivedInterpolated": "Ricevu<PERSON> {{amount}} SOL", "transactionsSending": "Invio...", "transactionsSent": "Inviata", "transactionsSwapOn": "Scambio il", "transactionsSentInterpolated": "Inviati {{amount}} SOL", "transactionsStaked": "In stake", "transactionsSuccess": "<PERSON><PERSON><PERSON><PERSON>", "transactionsTo": "A", "transactionsTokenSwap": "Scambio di token", "transactionsUnstaked": "<PERSON><PERSON><PERSON> da <PERSON>", "transactionsWaitingForConfirmation": "In attesa di conferma", "transactionsWithdrawStake": "Withdraw Stake", "unlockActionButtonUnlock": "S<PERSON><PERSON>ca", "unlockEnterPassword": "Inserisci la tua password", "unlockErrorIncorrectPassword": "Password non corretta", "unlockErrorSomethingWentWrong": "Qualcosa è andato storto, riprova più tardi", "unlockForgotPassword": "Ha dimenticato la password?", "unlockPassword": "Password", "validationUtilsPasswordIsRequired": "Password obbligatoria", "validationUtilsPasswordLength": "La password deve essere lunga 8 caratteri", "validationUtilsPasswordsDontMatch": "Le password non corrispondono", "validationUtilsPasswordCantBeSame": "Non puoi usare la tua vecchia password", "validatorCardCommission": "Commissione", "validatorCardTotalStake": "Stake totale", "validatorCardNumberOfDelegators": "Num di delegatori", "validatorListActionButtonCancel": "<PERSON><PERSON><PERSON>", "validatorListChooseAValidator": "Scegli un validatore", "validatorListErrorFetching": "Si è verificato un problema durante il recupero dei validatori:", "validatorListNoResults": "<PERSON><PERSON><PERSON> r<PERSON>", "validatorListReload": "Ricarica", "validatorListSearch": "Cerca", "validatorViewActionButtonClose": "<PERSON><PERSON>", "validatorViewActionButtonStake": "Stake", "validatorViewEdit": "Modifica", "validatorViewErrorFetching": "Impossibile recuperare i validatori.", "validatorViewInsufficientBalance": "<PERSON><PERSON>", "validatorViewMax": "Max", "validatorViewPrimaryText": "Inizia lo stake", "validatorViewSecondaryTextInterpolated": "Scegli quanti SOL vuoi <1></1> mettere in stake con questo validatore. <3>Ulteriori informazioni</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL necessari per lo stake", "validatorViewValidator": "Validatore", "walletMenuItemsAddConnectWallet": "Aggiungi/Collega portafoglio", "walletMenuItemsBridgeAssets": "<PERSON>lega risorse", "walletMenuItemsHelpAndSupport": "Supporto", "walletMenuItemsLockWallet": "Blocca portafoglio", "walletMenuItemsResetSecretPhrase": "Reimposta la frase segreta", "walletMenuItemsShowMoreAccounts": "<PERSON>ra altri {{count}}...", "walletMenuItemsHideAccounts": "Nascondi account", "whatsNewOverlayActionButtonClose": "<PERSON><PERSON>", "whatsNewOverlayNew": "Novità!", "whatsNewOverlayv1ActionGetAppNow": "Scarica subito l'app", "whatsNewOverlayv1PrimaryText": "Phantom per iOS è ora disponibile!", "whatsNewOverlayv1ScanWithCamera": "Scansiona con la fotocamera del tuo iPhone", "whatsNewOverlayv1SecondaryText": "Siamo lieti di annunciare che Phantom per iOS è ora disponibile nell'App Store! Sperimenta il potere di Phantom in tasca!", "networkErrorTitle": "Errore di rete", "networkError": "Purtroppo non siamo riusciti ad accedere alla rete. Riprova più tardi.", "networkRetry": "<PERSON><PERSON><PERSON><PERSON>", "authenticationUnlockPhantom": "Sblocca Phantom", "errorAndOfflineSomethingWentWrong": "Qualcosa è andato storto", "errorAndOfflineSomethingWentWrongTryAgain": "R<PERSON>rova.", "errorAndOfflineUnableToFetchAssets": "Impossibile recuperare le risorse. Riprova più tardi.", "errorAndOfflineUnableToFetchCollectibles": "Impossibile recuperare i collezionabili. Riprova più tardi.", "errorAndOfflineUnableToFetchSwap": "Impossibile recuperare le informazioni di scambio. Riprova più tardi.", "errorAndOfflineUnableToFetchTransactionHistory": "Impossibile recuperare la cronologia delle transazioni. Riprova più tardi.", "swapReviewError": "Qualcosa è andato storto durante la revisione del tuo ordine, riprova.", "sendSelectToken": "Seleziona token", "swapBalance": "Saldo:", "swapTitle": "Scambia i token", "swapSelectToken": "Seleziona token", "aboutPrivacyPolicy": "Informativa sulla privacy", "aboutVersion": "Versione {{version}}", "aboutVisitWebsite": "Visita il sito web", "transactionsFromInterpolated": "Da: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON><PERSON>", "transactionsToInterpolated": "A: {{to}}", "transactionsYesterday": "<PERSON><PERSON>", "addEditTokenSuccessMessage": "Account token associato creato", "addEditTokenFailureMessage": "Si è verificato un problema durante la creazione di un account token associato. Riprova.", "addEditTokenLoadingMessage": "Stiamo creando un account token associato", "addEditTokenSuccessTitle": "Token aggiunto con successo", "addEditTokenFailureTitle": "Aggiunta di token non riuscita", "addEditTokenLoadingTitle": "Aggiunta di token", "addEditTokenAlreadyAdded": "Hai già questo token", "addEditTokenContinue": "Continua", "addEditTokenPaste": "<PERSON><PERSON><PERSON>", "addEditTokenRequired": "Obbligatorio", "addEditTokenViewTransaction": "Mostra transazione", "addEditTokenMintAddressError": "Indirizzo non valido o non supportato", "addEditTokenNameError": "Solo lettere, numeri, trattini bassi, trattini e spazi", "addEditTokenSymbolError": "Solo lettere", "addEditAddressAdd": "Aggiungi indirizzo", "addEditAddressCancel": "<PERSON><PERSON><PERSON>", "addEditAddressDelete": "Elimina indirizzo", "addEditAddressDeleteTitle": "Vuoi davvero eliminare questo indirizzo?", "addEditAddressPaste": "<PERSON><PERSON><PERSON>", "addEditAddressSave": "<PERSON><PERSON> in<PERSON>", "dAppBrowserComingSoon": "Browser dApp in arrivo!", "dAppBrowserSearchPlaceholder": "Cerca o accedi al sito web", "dAppBrowserFavorites": "Preferiti", "dAppBrowserTrustedApps": "<PERSON><PERSON><PERSON> recente", "dAppBrowserFavoritesDescription": "I tuoi preferiti verranno mostrati qui", "dAppBrowserEmptyScreenDescription": "Digita un URL o cerca per accedere alle tue app Solana preferite", "dAppBrowserBlocklistScreenTitle": "{{origin}} è bloccato! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom ritiene che questo sito Web sia dannoso e non sicuro da usare.", "part2": "Questo sito è stato segnalato come parte di un database gestito dalla community di siti Web di phishing e truffe noti. Se ritieni che il sito sia stato segnalato per errore, segnala un problema."}, "dAppBrowserBlocklistScreenIgnoreButton": "Ignora l'avviso, mostra comunque", "depositAssetListSuggestions": "<PERSON><PERSON><PERSON><PERSON>", "depositUndefinedToken": "S<PERSON><PERSON>i, non è possibile depositare questo token", "onboardingImportRecoveryPhraseDetails": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseVerifyTitle": "Hai trascritto la frase segreta per il recupero?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Senza la frase di recupero segreta non potrai accedere alla tua chiave o alle risorse a essa associate.", "onboardingCreateRecoveryPhraseVerifyYes": "Sì", "onboardingCreateRecoveryPhraseErrorTitle": "Errore", "onboardingCreateRecoveryPhraseErrorSubtitle": "Non siamo riusciti a generare un account, riprova.", "onboardingDoneDescription": "Ora puoi goderti appieno il tuo portafoglio.", "onboardingDoneGetStarted": "Inizia", "onboardingImportAccountsEmptyResult": "Nessun account trovato", "onboardingImportAccountsWalletName": "Portafoglio {{walletIndex}}", "onboardingImportRecoveryPhraseLessThanTwelve": "La frase deve essere di almeno 12 parole.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "La frase deve essere esattamente di 12 o 24 parole.", "onboardingImportRecoveryPhraseWrongWord": "Parole errate: {{ words }}.", "onboardingProtectTitle": "Proteggi il tuo portafoglio", "onboardingProtectDescription": "L'aggiunta di sicurezza biometrica assicurerà che solo tu sia possa accedere al tuo portafoglio.", "onboardingProtectButtonHeadlineDevice": "Dispositivo", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Impronta digitale", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Usa l'autenticazione {{ authType }}", "onboardingProtectError": "Qualcosa è andato storto durante l'autenticazione, riprova", "onboardingProtectRemoveAuth": "Disabilita l'autenticazione", "onboardingProtectRemoveAuthDescription": "Vuoi davvero disabilitare l'autenticazione?", "onboardingProtectNext": "<PERSON><PERSON>", "onboardingWelcomeTitlePhantom": "Phantom è un portafoglio\nSolana semplice creato per\nDeFi e NFT", "onboardingWelcomeTitle": "A friendly Solana\nwallet built for\nDeFi & NFTs", "onboardingWelcomeCreateWallet": "Crea un nuovo portafoglio", "onboardingWelcomeAlreadyHaveWallet": "Ho già un portafoglio", "onboardingSlide1Title": "Non custodito", "onboardingSlide1Description": "<1>Non avremo mai accesso</1> a nessuno dei tuoi dati o fondi. Mai.", "onboardingSlide2Title": "La casa dei tuoi NFT", "onboardingSlide2Description": "Abbiamo prestato particolare attenzione per assicurarci che i tuoi <1>NFT</1> abb<PERSON> un bell'aspetto!", "onboardingSlide3Title": "Fai stake coi token e scambiali", "onboardingSlide3Description": "Usa il nostro sistema di scambio per <1>scam<PERSON>re in sicurezza</1> i token ai <4>mi<PERSON><PERSON> prezzi</4>, istantaneamente.", "onboardingSlide4Title": "Usa dApps", "onboardingSlide4Description": "Esplora il mondo delle <1>applicazioni blockchain</1> basate su Solana.", "requireAuth": "Occorre l'autenticazione", "requireAuthImmediately": "Immediatamente", "sendEnterAmount": "Inserisci l'importo", "sendShowLogs": "Mostra registri errori", "sendHideLogs": "Nascondi registri errori", "sendGoBack": "Indietro", "sendTransactionSuccess": "I tuoi token sono stati inviati con successo a", "sendInputPlaceholder": "Nome o indirizzo", "sendRecentlyUsedAddressLabel": "Usato {{formattedTimestamp}} fa", "sendRecipientAddress": "Indirizzo del destinatario", "sendTokenInterpolated": "Invia {{tokenSymbol}}", "sendPaste": "<PERSON><PERSON><PERSON>", "sendPasteFromClipboard": "Incolla dagli appunti", "sendScanQR": "Scansiona il codice QR", "sendTo": "A:", "sendCameraAccess": "Accesso alla fotocamera", "sendCameraAccessSubtitle": "Per eseguire la scansione di un codice QR, è necessario abilitare l'accesso alla fotocamera. Vuoi aprire le Impostazioni ora?", "sendCancel": "<PERSON><PERSON><PERSON>", "sendSettings": "Impostazioni", "sendOK": "OK", "invalidQRCode": "Questo codice QR non è valido.", "sendInvalidQRCode": "Questo codice QR non è un indirizzo valido", "sendInvalidQRCodeSubtitle": "Riprova o prova con un altro codice QR.", "sendInvalidQRCodeSplToken": "Token non valido nel codice QR", "sendInvalidQRCodeSplTokenSubtitle": "Questo codice QR contiene un token che non possiedi o impossibile da identificare.", "sendScanAddressToSend": "Scansiona l'indirizzo {{tokenSymbol}} per inviare fondi", "sendScanAddressToSendCollectible": "Scansiona l'indirizzo SOL per inviare il collezionabile", "sendSummary": "Resoconto", "sendUndefinedToken": "<PERSON><PERSON><PERSON><PERSON>, non è possibile inviare questo token", "sendNoTokens": "Nessun token disponibile", "settingsAbout": "Informazioni su Phantom", "settingsCancel": "<PERSON><PERSON><PERSON>", "settingsConfirm": "Sì", "settingsEdit": "Modifica", "settingsEditWallet": "Modifica portafoglio", "settingsPrompt": "Vuoi davvero continuare?", "settingsShowPrivateKey": "Tocca per rivelare la tua chiave privata", "settingsShowRecoveryPhrase": "Tocca per rivelare la tua frase segreta", "settingsMakeSureNoOneIsWatching": "Assicurati che nessuno stia guardando il tuo schermo", "settingsSecurity": "Sicurezza del dispositivo", "settingsSubmitBetaFeedback": "Invia feedback sulla versione beta", "settingsWalletAddress": "Indirizzo portafoglio", "settingsWalletNamePrimary": "Nome <PERSON>", "settingsWalletNameSecondary": "Cambia il nome del tuo portafoglio", "settingsYourAccounts": "I tuoi account", "settingsNotifications": "Notifiche", "settingsNotificationPreferences": "Preferenze di notifica", "pushNotificationsPreferencesAllowNotifications": "Consenti notifiche", "pushNotificationsPreferencesSentTokens": "Token inviati", "pushNotificationsPreferencesSentTokensDescription": "Trasferimenti in uscita di token e NFT", "pushNotificationsPreferencesReceivedTokens": "Token rice<PERSON>ti", "pushNotificationsPreferencesReceivedTokensDescription": "Trasferimenti in entrata di token e NFT", "pushNotificationsPreferencesDexSwap": "Sc<PERSON><PERSON>", "pushNotificationsPreferencesDexSwapDescription": "Scambia su applicazioni riconosciute", "pushNotificationsPreferencesOtherBalanceChanges": "Altri cambiamenti di saldo", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Altre transazioni multi-token che influiscono sul tuo saldo", "pushNotificationsPreferencesPhantomMarketing": "Aggiornamenti da Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Annunci di funzionalità e aggiornamenti generali", "pushNotificationsPreferencesDescription": "Queste impostazioni controllano le notifiche push per questo portafoglio attivo. Ogni portafoglio ha le proprie impostazioni di notifica. Per disattivare tutte le notifiche push Phantom, vai alle tue <1>impostazioni dispositivo</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Impossibile sincronizzare le preferenze di notifica.", "addAccountHardwareWalletComingSoon": "In arrivo", "stakeAmount": "Importo", "stakeAmountNext": "<PERSON><PERSON>", "stakeAmountBalance": "<PERSON><PERSON>", "stakeReview": "Controlla", "stakeReviewAccount": "Account", "stakeReviewCommissionFee": "Quota commissione", "stakeReviewConfirm": "Conferma", "stakeReviewValidator": "Validatore", "swapTooltipGotIt": "Capito", "swapSetSlippageContinue": "Continua", "swapSetSlippageWarning": "Potresti ricevere il {{slippage}}% in meno a causa del livello di slittamento", "swapTabInsufficientFunds": "Fondi insufficienti", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "swapConfirmationGoBack": "Indietro", "unwrapWrappedSolClose": "<PERSON><PERSON>", "unwrapWrappedSolError": "Spacchettamento non riuscito", "unwrapWrappedSolLoading": "Spacchettamento...", "unwrapWrappedSolSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unwrapWrappedSolViewTransaction": "Mostra transazione", "dappApprovePopupSignMessage": "<PERSON><PERSON> messaggio", "solanaPayFrom": "Da", "solanaPayMessage": "Messaggio", "solanaPayNetworkFee": "Commissione di rete", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Paga", "solanaPayPayNow": "Paga subito", "solanaPaySent": "Inviati!", "solanaPayTokensSent": "I tuoi token sono stati inviati con successo a", "solanaPayViewTransaction": "Mostra la mia transazione", "solanaPayTransactionFailed": "Transazione non riuscita", "solanaPayApprove": "<PERSON><PERSON><PERSON><PERSON>", "dappApproveConnectViewAccount": "Visualizza il tuo account Solana", "deepLinkInvalidLink": "Link non valido", "deepLinkInvalidSplTokenSubtitle": "Contiene un token che non possiedi o impossibile da identificare.", "walletAvatarShowAllAccounts": "Mostra tutti gli account", "pushNotificationsGetInstantUpdates": "Ricevi aggiornamenti immediati", "pushNotificationsEnablePushNotifications": "Abilita le notifiche push su trasferimenti completati, scambi e annunci", "pushNotificationsEnable": "Abilita", "pushNotificationsNotNow": "Non ora", "onboardingAgreeToTermsOfServiceInterpolated": "Accetto i <1>Termini di servizio</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, l'ho salvata da qualche parte", "onboardingCreateNewWallet": "Crea un nuovo portafoglio", "onboardingErrorInvalidSecretRecoveryPhrase": "Frase di recupero segreta non valida", "onboardingFinished": "<PERSON><PERSON>!", "onboardingImportAccounts": "Importa account", "onboardingImportAccountsLastUsed": "Usato {{formattedTimestamp}} fa", "onboardingImportAccountsNeverUsed": "<PERSON> usato", "onboardingImportAccountsDescription": "Scegli gli account portafoglio da importare", "onboardingImportSecretRecoveryPhrase": "Importa frase di recupero segreta", "onboardingImportSelectedAccounts": "Importa account selezionati", "onboardingRestoreExistingWallet": "Ripristina un portafoglio esistente con la tua frase di recupero segreta di 12 o 24 parole", "onboardingShowUnusedAccounts": "Mostra account inutilizzati", "onboardingShowMoreAccounts": "Mostra altri account", "onboardingHideUnusedAccounts": "Nascondi account inutilizzati", "onboardingSecretRecoveryPhrase": "Frase di recupero segreta", "onboardingSelectAccounts": "Seleziona i tuoi account", "onboardingStoreSecretRecoveryPhraseReminder": "È l'unico modo in cui potrai recuperare il tuo account. Conser<PERSON>a in un posto sicuro!", "timeUnitMinute": "minuto", "timeUnitMinutes": "minuti", "timeUnitHour": "ora", "timeUnitHours": "ore", "espDexSwap": "<PERSON> scambiato {{downTokensTextFragment}} per {{upTokensTextFragment}} su {{dAppName}}", "espNFTBid": "Hai offerto {{downTokensTextFragment}} per {{upTokensTextFragment}} su {{dAppName}}", "espNFTBuy": "Hai acquistato {{nftName}} per {{downTokensTextFragment}} su {{dAppName}}", "espNFTCancelBid": "Hai annullato un'offerta e hai ricevuto {{upTokensTextFragment}} su {{dAppName}}", "espNFTList": "Hai messo in vendita {{downTokensTextFragment}} su {{dAppName}}", "espNFTUnlist": "Hai tolto dalla vendita {{upTokensTextFragment}} su {{dAppName}}", "espTokenReceive": "Hai ricevuto {{upTokensTextFragment}}", "espTokenSend": "Hai inviato {{downTokensTextFragment}}", "espTokenTextFragment": "{{token1}} e {{token2}}", "espTransactionBalanceChange": "<PERSON> scambiato {{downTokensTextFragment}} per {{upTokensTextFragment}}", "espUnknown": "SCONOSCIUTO", "espUnknownNFT": "NFT sconosciuto"}