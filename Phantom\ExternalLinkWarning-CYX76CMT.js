import{a as g}from"./chunk-XJWRT6N6.js";import{b as f}from"./chunk-S24UABH5.js";import"./chunk-SHAEZV7V.js";import{m as s}from"./chunk-75L54KUM.js";import{a as x}from"./chunk-ROF5SDVA.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-H3FFS4GT.js";import{k as p}from"./chunk-OKP6DFCI.js";import{o as c,wa as d}from"./chunk-WIQ4WVKX.js";import"./chunk-MNXYIK2W.js";import{c as m}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-WFPABEAU.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as l}from"./chunk-56SJOU6P.js";import{S as a}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as u}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as C,h as i,n as t}from"./chunk-3KENBVE7.js";i();t();var r=C(u());var y=c.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`,h=r.default.memo(({url:n})=>{let{t:e}=l(),{hideExternalLinkWarningModal:o}=f(),k=(0,r.useCallback)(()=>{m.capture("collectibleExternalLinkOpened",{data:{url:n.toString()}}),o(),self.open(n.href,"_blank")},[o,n]);return r.default.createElement(y,null,r.default.createElement(s,null),r.default.createElement(g,{icon:r.default.createElement(x,{color:a("#ffdc62",.1),diameter:94},r.default.createElement(d,{width:54,height:54,circleFill:"#ffdc62"})),primaryText:e("externalLinkWarningTitle"),secondaryText:e("externalLinkWarningDescription",{url:n.host})}),r.default.createElement(p,{primaryText:e("commandCancel"),secondaryText:e("commandContinue"),onPrimaryClicked:o,onSecondaryClicked:k}))}),I=h;export{h as ExternalLinkWarning,I as default};
//# sourceMappingURL=ExternalLinkWarning-CYX76CMT.js.map
