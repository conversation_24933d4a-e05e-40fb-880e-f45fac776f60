import{a as Re}from"./chunk-5IF7UAPA.js";import{a as de}from"./chunk-PDSYJ4DQ.js";import{a as Ye}from"./chunk-ANFAK3NI.js";import{a as Je,b as Xe}from"./chunk-TVONLZ7I.js";import{a as et}from"./chunk-HUU4WO6F.js";import{a as At}from"./chunk-UM364UVK.js";import"./chunk-QZG7YQTK.js";import{Ia as Ke,Ja as me,Ka as qe,La as Qe,Ma as Z,R as ce,T as _e,U as Oe,V as Ve,ca as Ue,da as Ge,ea as We,ha as $e}from"./chunk-JD6NH5K6.js";import{c as Le}from"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import{a as Ne}from"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as G}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as F}from"./chunk-CCQRCL2K.js";import{h as le}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import{i as je,j as J,k as Ze}from"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import{b as He}from"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{D as ve,G as Se,H as Pe,d as ie,e as j}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{a as Q,b as Me,k as De}from"./chunk-OKP6DFCI.js";import{U as Fe,Z as Ee,o as a,ra as ze,rb as v,wa as Ie}from"./chunk-WIQ4WVKX.js";import{aa as oe,ba as Ae}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import{d as Ce,o as we}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as U}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{Ra as Be,fc as Te,gc as ke}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as q,Id as te,Pa as ge,Xa as fe,_d as be,ae as he,ge as ye,ne,re as xe}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as H}from"./chunk-56SJOU6P.js";import{S as se,x as ae}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{Ya as ee}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as D}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as z,h as p,n as u}from"./chunk-3KENBVE7.js";p();u();var o=z(D());p();u();var E=z(D()),nt=z(At());p();u();var C=z(D());p();u();var N=new je(U);var vt=a(Q.button)`
  background: none;
  background-color: rgba(60, 49, 91, 0.4);
  border: 1px solid rgb(60, 49, 91);
  border-radius: 8px;
  cursor: pointer;
  height: 100%;
  padding: 10px 12px;
  width: 100%;
  &:hover {
    background-color: rgba(60, 49, 91, 0.6);
  }
`,St=a(Q.div)`
  align-items: center;
  display: flex;
`,Pt=a.img`
  margin-right: 12px;
  width: 44px;
`,Ft=a(v).attrs({lineHeight:17,size:14})`
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  flex: 1;
  overflow: hidden;
  text-align: left;
`,Et=a.div`
  position: relative;
  top: -15px;
  right: -3px;
  background-color: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  height: 20px;
  justify-content: center;
  width: 20px;
`,zt=({banner:e})=>{let{t}=H(),n=Ke(),{mutateAsync:i}=Ae(),{handleShowModalVisibility:r,handleHideModalVisibility:c}=Z(),s=(0,C.useCallback)(b=>{let y=J(e);switch(N.onBannerClick(y),e.bannerType){case"Deep Link":{let{destinationType:d,url:g}=e;n(b,{destinationType:d,url:g});break}case"Modal":{let{interstitial:d,destinationType:g,url:x}=e,{title:h,lineItems:T=[],imageUrl:k,primaryButtonText:S=t("commandContinue"),secondaryButtonText:B=t("commandDismiss")}=d,A=Ze(e),w=T.map(P=>({icon:P.imageUrl,subtitle:P.description,title:P.title}));r("interstitial",{bodyTitle:h,details:w,icon:k,onDismiss:()=>{N.onInterstitialDismiss(A)},FooterComponent:()=>C.default.createElement(De,{primaryText:S,secondaryText:B,onPrimaryClicked:()=>{n(b,{destinationType:g,url:x}),N.onInterstitialPrimaryClick(A),c("interstitial")},onSecondaryClicked:()=>{N.onInterstitialSecondaryClick(A),c("interstitial")}})}),N.onInterstitialSeen(A);break}}},[e,r,c,t,n]),m=(0,C.useCallback)(b=>{b.stopPropagation(),i({actionBannerId:e.id});let y=J(e);N.onBannerDismiss(y)},[e,i]);return(0,C.useMemo)(()=>({banner:e,onClickBanner:s,onCloseBanner:m}),[e,s,m])},It=C.default.memo(({banner:e,onClickBanner:t,onCloseBanner:n})=>C.default.createElement(vt,{layout:!0,onClick:t},C.default.createElement(St,{layout:!0},C.default.createElement(Pt,{src:e.imageUrl}),C.default.createElement(Ft,{weight:600},e.description),C.default.createElement(Et,{onClick:n},C.default.createElement(Ee,{fill:"#ffffff",width:8}))))),tt=e=>{let t=zt(e);return C.default.createElement(It,{...t})};var Mt=()=>{let{data:e={banners:[]}}=oe(),{data:t}=be(),{banners:n}=e,i=(0,nt.default)(t),r=(0,E.useCallback)(s=>{if(!t||i!==t)return;let m=n[s],b=J(m);N.onBannerSeen(b)},[n,t,i]),c=(0,E.useMemo)(()=>n.map(s=>({key:s.id,node:E.default.createElement(tt,{banner:s})})),[n]);return(0,E.useMemo)(()=>({identifier:t??"",items:c,itemHeight:74,onIndexChange:r}),[c,t,r])},Dt=E.default.memo(({identifier:e,items:t,onIndexChange:n,itemHeight:i})=>E.default.createElement(Re,{items:t,onIndexChange:n,key:e,itemHeight:i})),Ht=()=>{let e=Mt();return E.default.createElement(Dt,{...e})},ot=()=>{let{data:[e]}=ee(["kill-action-banners"]);return e?null:E.default.createElement(Ht,null)};p();u();var l=z(D());p();u();var L=z(D()),it=({children:e,maxFontSize:t})=>{let n=(0,L.useRef)(null),[i,r]=(0,L.useState)(t);return(0,L.useEffect)(()=>{let c=n.current;if(!c)return;let s=new ResizeObserver(()=>{let m=i;c.querySelectorAll("*").forEach(y=>{for(;y.scrollWidth>c.clientWidth&&m>10;)m-=1,y.style.fontSize=`${m}px`}),r(m)});return s.observe(c),()=>{s.disconnect()}},[i]),L.default.createElement(ie,{ref:n,overflow:"hidden",width:"100%",display:"flex",direction:"column",alignItems:"center"},L.default.createElement("div",{style:{fontSize:`${i}px`,whiteSpace:"nowrap"}},e))};var Nt=a(F).attrs({align:"center"})`
  width: 100%;
  background: ${e=>e.background};
`,Lt=a(F).attrs({align:"center"})`
  margin-top: 2rem;
`,pe=a(F).attrs({align:"center",justify:"center",width:"100%"})`
  height: 5.3rem;
`,rt=a(Ne).attrs({height:"8px",borderRadius:"6px",backgroundColor:"#484848"})`
  opacity: 0.2;
`,at=a(G)`
  height: 8px;
  border-radius: 6px;
  background-color: ${se("#999999",.5)};
  opacity: 0.5;
`,_t=a.div`
  display: flex;
  flex-direction: row;
  padding: 16px 5px;
  justify-content: center;
  align-items: center;
  gap: 6px;
  flex: 1 0 0;
  border-radius: 62px;
  backdrop-filter: blur(2px);
  background: rgba(0, 0, 0, 0.2);
`,Ot=a(v).attrs({size:15,weight:"600",color:"#FFF",lineHeight:20})``,Vt=a(v).attrs({size:36,weight:"bold",color:"#777"})``,Ut=a(G).attrs({justify:"center"})``,Gt=a(v).attrs({weight:500,size:18})`
  border-radius: 6px;
  padding: 2px 5px;
`,Wt=a.div`
  display: flex;
  flex-direction: row;
  gap: 8px;
  width: 326px;
  margin-top: 2rem;
  margin-bottom: 22px;
  > * {
    box-shadow: 0px 2px 6px rgba(0, 0, 0, 0.16);
  }
`,$t=a.div`
  padding: 16px;
  padding-bottom: 0px;
`,jt=a.div`
  padding: 32px 16px;
  width: 100%;
`,Zt={minimumFractionDigits:2,maximumFractionDigits:2},st=l.default.memo(({hasFungibles:e,isErrorTokens:t,isLoading:n,isHidingAllFungibles:i,isReadOnlyAccount:r,value:c,earnings:s,showDollarValues:m,shouldShowPartialError:b,partialErrorMessage:y,ctaActions:d})=>{let{t:g}=H(),x=qt(s),h=Kt({earnings:s,isNeutral:!m||n||i}),T=Qt(c,s),{buttonDisabled:k}=Jt({isLoading:n,isEnabled:e||i,isErrorTokens:t});return l.default.createElement(Nt,{background:h},b?l.default.createElement($t,null,l.default.createElement($e,{partialErrorMessage:y})):null,l.default.createElement(Lt,null,m?n?l.default.createElement(pe,null,l.default.createElement(rt,{width:"184px",margin:"0 0 10px 0"}),l.default.createElement(rt,{width:"112px"})):e||i?l.default.createElement(l.default.Fragment,null,l.default.createElement(ie,{width:"100%",marginBottom:10},l.default.createElement(it,{maxFontSize:38},l.default.createElement(ce,{font:"inherit",fontWeight:"semibold",value:c,intlNumberFormatOptions:Zt}))),l.default.createElement(Ut,null,l.default.createElement(ce,{signConfig:"always",marginRight:6,font:"title1",color:"sentiment",value:s}),l.default.createElement(Gt,{color:x,backgroundColor:se(x,.1)},T))):t?l.default.createElement(pe,null,l.default.createElement(at,{width:"184px",margin:"0 0 10px 0"}),l.default.createElement(at,{width:"112px"})):null:l.default.createElement(pe,null,l.default.createElement(Vt,null,"\u2013"))),r?l.default.createElement(Wt,null,l.default.createElement(_t,null,l.default.createElement(Fe,{width:20,height:20,fill:"#FFFFFF"}),l.default.createElement(Ot,null,g("readOnlyAccountBannerWarning")))):l.default.createElement(jt,null,l.default.createElement(Ge,{disabled:k,actions:d,uiContextName:"home",maxButtons:4})))}),Kt=({earnings:e,isNeutral:t})=>t||e===void 0||e===0?"linear-gradient(180deg, rgba(136, 136, 136, 0.05) 0%, rgba(136, 136, 136, 0) 100%)":e>0?"linear-gradient(180deg, rgba(33, 229, 111, 0.05) 0%, rgba(33, 229, 111, 0) 100%)":"linear-gradient(180deg, rgba(235, 55, 66, 0.05) 0%, rgba(235, 55, 66, 0) 100%)",qt=e=>e===void 0||e===0?"#777777":e>0?"#21E56F":"#EB3742",Qt=(e,t)=>{let n=e===void 0,i=t===void 0,r=i||t>=0?"+":"-";return n||i?"-":`${r}${Math.abs(Be(e-t,e)).toFixed(2)}%`},Jt=({isLoading:e,isEnabled:t,isErrorTokens:n})=>{let i="primary",r=!1;switch(!0){case e:i="secondary",r=!0;break;case t:i="primary",r=!1;break;case n:i="secondary",r=!0}return{buttonTheme:i,buttonDisabled:r}};p();u();var _=z(D());var Xt=a(F).attrs({align:"center"})``,Yt=a.div`
  width: 48px;
  height: 48px;
  position: relative;
  margin-bottom: 15px;
  border-radius: 100%;
  background: rgba(255, 220, 98, 0.2);
`,Rt=a(G).attrs({align:"center",justify:"center"})`
  height: 100%;
`,en=a(v).attrs({size:17,weight:500,lineHeight:22,margin:"0 0 10px 0"})``,tn=a(v).attrs({size:15,weight:500,lineHeight:21,margin:"0 0 15px 0",color:"#777777"})``,nn=a(v).attrs({size:16,weight:500,lineHeight:22,margin:"0",color:"#AB9FF2"})``,lt=_.default.memo(e=>_.default.createElement(Xt,null,_.default.createElement(Yt,null,_.default.createElement(Rt,null,_.default.createElement(Ie,{width:22,exclamationFill:"transparent",circleFill:"#FFE920"}))),_.default.createElement(en,null,e.title),_.default.createElement(tn,null,e.description),_.default.createElement(nn,{onClick:e.refetch},e.buttonText)));p();u();var K=z(D());var on=a(G).attrs({justify:"center",margin:"0 auto",width:"auto"})`
  cursor: pointer;
  height: 48px;
  margin-bottom: 10px;
  p {
    font-weight: 500;
  }
  &:hover {
    p {
      color: #ab9ff2 !important;
    }
    svg {
      fill: #ab9ff2;
      path {
        stroke: #ab9ff2;
      }
      circle {
        stroke: #ab9ff2;
      }
    }
  }
`,rn=a(v).attrs({size:16,color:"#777777",weight:500,margin:"0 0 0 10px",lineHeight:19,noWrap:!0})``,ct=K.default.memo(e=>K.default.createElement(on,{onClick:e.onClick},K.default.createElement(He,null,K.default.createElement(ze,null)),K.default.createElement(rn,null,e.buttonText)));p();u();var f=z(D());p();u();var mt={bannersStack:"_51gazn18w _51gazn1c3 _51gazn129 _51gazngl _51gazncx _51gazneb",bannersStackHeading:"_51gaznm _51gazn332",banner:"_1lb45bv2",bannerTitle:"_51gazn10 _51gazn33h _51gazn7",bannerDescription:"_51gaznr _51gazn332 _51gaznbn _51gazn128",bannerImage:"_51gazn11k _51gazn12u"};var ft=()=>{let{data:e}=q(),{data:t}=te(e?.identifier),n=t?.value===0,{data:i}=oe();return i&&i.banners.length>0?null:f.default.createElement(Me,null,n&&f.default.createElement(Q.div,{key:"zero-balance-banners",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},style:{width:"100%"}},f.default.createElement(an,null)))},an=()=>{let{t:e}=H(),{handleShowModalVisibility:t,handleHideModalVisibility:n}=Z(),{data:i}=q(),r=(0,f.useCallback)(()=>{U.capture("zeroBalanceBannerBuyCryptoClickedByUser"),t("onramp")},[t]),c=(0,f.useCallback)(()=>{U.capture("zeroBalanceBannerDepositCryptoClickedByUser"),i&&t("receive",{account:i,onCloseClick:()=>n("receive")})},[n,t,i]);return f.default.createElement("div",{className:sn},f.default.createElement(j,{className:ln,children:e("zeroBalanceHeading")}),f.default.createElement("div",{className:dt,onClick:r},f.default.createElement(F,null,f.default.createElement(j,{className:pt,color:"textSecondary",children:e("zeroBalanceBuyCryptoTitle")}),f.default.createElement(j,{className:ut,children:e("zeroBalanceBuyCryptoDescription")})),f.default.createElement("img",{className:gt,src:"/images/zero-balance/buy-crypto.svg"})),f.default.createElement("div",{className:dt,onClick:c},f.default.createElement(F,null,f.default.createElement(j,{className:pt,color:"textSecondary",children:e("zeroBalanceDepositTitle")}),f.default.createElement(j,{className:ut,children:e("zeroBalanceDepositDescription")})),f.default.createElement("img",{className:gt,src:"/images/zero-balance/transfer-crypto.svg"})))},{bannersStack:sn,bannersStackHeading:ln,banner:dt,bannerTitle:pt,bannerDescription:ut,bannerImage:gt}=mt;p();u();var X=z(D());function bt(e,t,n){let i=ne(),[r,c]=(0,X.useState)(!1);(0,X.useEffect)(()=>{i||c(!1)},[n,i]),(0,X.useEffect)(()=>{r||!t.length||!e.length||i||(Ue.walletBalance(n,t,e),c(!0))},[n,t,e,r,i])}var cn=o.default.memo(({visibilityOverrides:e,fungibles:t,isMainnet:n,onMouseEnter:i,enableUnifiedTokenPage:r})=>{let{t:c}=H(),{pushDetailView:s}=le(),m=(0,o.useRef)(document.getElementById("home-tab"));(0,o.useEffect)(()=>{let d=document.getElementById("home-tab");d&&(m.current=d)},[]);let b=(0,o.useCallback)(({networkID:d,chainName:g,fungibleKey:x,name:h,symbol:T,tokenAddress:k,type:S,walletAddress:B,spamStatus:A,splTokenAccount:w,caip19:P})=>{U.capture("assetDetailClick",{data:{address:k,chain:g,chainId:ge.getChainID(d),isNativeOfType:g,networkId:d,type:"fungible",spamStatus:A,name:h}}),s(r?o.default.createElement(me,{caip19:P,fungibleKey:x,splTokenAccount:w,entryPoint:"home"}):o.default.createElement(qe,{networkID:d,chainName:g,name:h,symbol:T,fungibleKey:x,tokenAddress:k,splTokenAccount:w,type:S,walletAddress:B}))},[s,r]),y=(0,o.useCallback)(({key:d,index:g,style:x})=>{let T=Math.min(g+1,t.length),k=[];for(let S=g;S<T;S++){let B=t[S],A=B.type,{chain:w,name:P,symbol:O,key:M,tokenAddress:V,walletAddress:Y}=B.data,W=P??c("assetDetailUnknownToken"),re=fe(Te(B));k.push(o.default.createElement(Oe,{...Ve(B,e),key:`${M}-${g}`,onClick:()=>b({networkID:w.id,chainName:w.name,fungibleKey:M,name:W,symbol:O,tokenAddress:V,type:A,walletAddress:Y,splTokenAccount:B.type==="SPL"?B.data.splTokenAccountPubkey:void 0,spamStatus:B.data.spamStatus,caip19:re}),onMouseEnter:i,showBalance:!0,showCurrencyValues:n}))}return o.default.createElement("div",{key:d,style:x},k)},[t,n,b,i,c,e]);return o.default.createElement(Pe,{scrollElement:m.current??void 0},({height:d=0,isScrolling:g,registerChild:x,scrollTop:h})=>o.default.createElement(ve,{disableHeight:!0,style:{width:"100%"}},({width:T})=>o.default.createElement("div",{ref:x},o.default.createElement(Se,{autoHeight:!0,width:T,height:d,scrollTop:h,isScrolling:g,rowCount:t.length,rowHeight:_e+10,rowRenderer:y}))))}),mn=ae({seconds:5}),dn=ae({seconds:10}),pn=()=>{let{data:e}=q(),{data:t}=te(e?.identifier),{data:[n]}=ee(["enable-unified-token-pages"]),{shouldShowAdditionalPermissionsInterstitial:i}=Xe(),{pushDetailView:r}=le(),c=We(),{handleShowModalVisibility:s,handleHideModalVisibility:m,closeAllModals:b}=Z(),y=(0,o.useCallback)(()=>{e&&s("onramp")},[s,e]),d=(0,o.useCallback)(()=>{e&&s("receive",{account:e,onCloseClick:()=>m("receive")})},[m,s,e]),g=(0,o.useCallback)(()=>{c({})},[c]),x=(0,o.useCallback)(()=>{s("sendFungibleSelect")},[s]),{t:h}=H(),T=(0,o.useMemo)(()=>({manageTokenList:h("homeManageTokenList"),errorTitle:h("homeErrorTitle"),errorDescription:h("homeErrorDescription"),errorButton:h("homeErrorButtonText")}),[h]),{ctaActions:k,shouldShowPartialError:S,partialErrorMessage:B}=we({onTappingBuy:y,onTappingReceive:d,onTappingSend:x,onTappingSwap:g,account:e}),{accountBalance:A,accountId:w}=(0,o.useMemo)(()=>({accountBalance:t?.value,accountId:e?.identifier??""}),[t?.value,e?.identifier]),P=e?.isReadOnly,O=!ne(),M=Ce($=>$.resetSendSlice);(0,o.useEffect)(function(){w&&M()},[w,M]);let{fungibles:V,visibilityOverrides:Y,portfolio:W,isHidingAllFungibles:re,isLoadingVisibilityOverrides:ht,isLoadingTokens:yt,isLoadingPrices:xt,isErrorTokens:Bt,refetch:Ct}=ke({useTokenQueryOptions:{staleTime:mn,refetchInterval:dn}}),{mutate:Tt}=he();xe({accountBalance:A,accountId:w,enabled:O,value:W.value,setAccountBalance:Tt});let kt=ye();bt(V,kt,w);let wt=(0,o.useMemo)(()=>({fungibles:V,earnings:W.earnings,value:W.value,isMainnet:O,enableUnifiedTokenPage:n,translations:T,visibilityOverrides:Y,ctaActions:k,showReceiveModal:d,handleShowModalVisibility:s,handleHideModalVisibility:m}),[Y,V,W,T,O,d,s,m,k,n]),R=de($=>$.publicFungibleDetailProps),ue=de($=>$.clearNotifyHomeFungiblePush);return(0,o.useEffect)(()=>{R&&(r(n?o.default.createElement(me,{...R}):o.default.createElement(Qe,{...R})),ue(),setTimeout(()=>b(),50))},[R,r,ue,b,n]),{data:wt,isHidingAllFungibles:re,isLoading:ht||yt||xt,isErrorTokens:Bt,isReadOnlyAccount:P,refetch:Ct,shouldShowPartialError:S,partialErrorMessage:B,shouldShowAdditionalPermissionsInterstitial:i}},un=()=>{let{data:e,isHidingAllFungibles:t,isLoading:n,isErrorTokens:i,isReadOnlyAccount:r,refetch:c,shouldShowPartialError:s,partialErrorMessage:m,shouldShowAdditionalPermissionsInterstitial:b}=pn(),{fungibles:y,translations:d,isMainnet:g,earnings:x,value:h,visibilityOverrides:T,ctaActions:k,enableUnifiedTokenPage:S,handleShowModalVisibility:B}=e,{manageTokenList:A,errorTitle:w,errorDescription:P,errorButton:O}=d,M=y.length>0;return o.default.createElement("div",{id:"home-tab"},o.default.createElement(st,{earnings:x,value:h,hasFungibles:M,isErrorTokens:i,isLoading:n,isHidingAllFungibles:t,isReadOnlyAccount:r,showDollarValues:g,shouldShowPartialError:s,partialErrorMessage:m,ctaActions:k}),o.default.createElement(F,{align:"center",padding:"0 16px 16px"},!r&&o.default.createElement(o.default.Fragment,null,o.default.createElement(ft,null),o.default.createElement(ot,null)),!et()&&o.default.createElement(o.default.Fragment,null,o.default.createElement(Ye,null)),b&&o.default.createElement(Je,null),n?[1,2,3].map(V=>o.default.createElement(Le,{key:`fungible-token-row-loader-${V}`})):M?o.default.createElement(cn,{visibilityOverrides:T,fungibles:y,isMainnet:g,enableUnifiedTokenPage:S}):t?null:o.default.createElement(lt,{title:w,description:P,buttonText:O,refetch:c}),n?null:M||t?o.default.createElement(ct,{buttonText:A,onClick:()=>B("fungibleVisibility")}):null))},Qi=un;export{Qi as default};
//# sourceMappingURL=HomeTabPage-6SNOPNNY.js.map
