import{a as $e}from"./chunk-4QLXYFPC.js";import{B as Xe,C as Ye,a as He,b as G,i as Qe,k as qe,x as Ge}from"./chunk-P6BZRSOH.js";import{a as _e}from"./chunk-RKY2OTIK.js";import"./chunk-DZR774A2.js";import{e as Ue}from"./chunk-W3ZRPNOX.js";import"./chunk-2WECCVZD.js";import{E as R,Ja as We,La as Ve,Ma as ee,R as ze}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import{a as K}from"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{h as O}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as J}from"./chunk-CCQRCL2K.js";import{h as Oe}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{L as U,M as Be,a as m,b as De,c as Ne,d as q,e as x,q as V,u as j,v as Le}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{j as Me}from"./chunk-OKP6DFCI.js";import{j as Ee,o as k,rb as te}from"./chunk-WIQ4WVKX.js";import{Ca as Ce,Da as Pe,Ga as ke,H as we,K as Se,Wa as Te,Xa as Ie,Ya as Ae,hb as Fe,ja as ye,ma as be,na as xe,ua as he,za as ve}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as ge}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{Pa as Z,Xa as de,ge as fe,wc as ue}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as y}from"./chunk-56SJOU6P.js";import{S as oe}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{Ya as me,ba as ce}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as v}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as h,h as t,n}from"./chunk-3KENBVE7.js";t();n();var d=h(v());t();n();var P=h(v());t();n();var g=h(v());t();n();var wr=h(v());var _=k.div`
  display: flex;
  align-items: center;
  height: 36px;
  input {
    font-size: ${e=>e.fontSize??34}px;
    line-height: 1;
    font-weight: 600;
    padding: 0;
    background: none;
  }
`,Er=k.div`
  width: 100%;
  margin-top: 11px;
  margin-bottom: 10px;
`;var Mr=k.div`
  color: #ab9ff2;
  font-size: 14px;
  font-weight: 400;
  white-space: nowrap;
  cursor: pointer;
  margin-left: 5px;
  border-radius: 8px;
  padding: 8px;
  &:hover {
    background-color: #333333;
  }
`;t();n();var T=h(v()),X={container:m({display:"flex",flexDirection:"column",gap:0,backgroundColor:"bgRow",borderRadius:"table",padding:20}),tokenContainer:m({display:"flex",flexDirection:"row",alignItems:"center",minHeight:48}),accessoryContainer:m({alignItems:"center",display:"flex",flexDirection:"row",gap:6,marginTop:6}),actionsContainer:m({display:"flex",flexDirection:"row",gap:0,backgroundColor:"accentPrimary"}),spacer:m({flex:1}),selector:m({display:"flex",gap:8,flexDirection:"row",backgroundColor:{base:"gray",hover:"borderPrimary"},paddingY:4,paddingLeft:4,paddingRight:8,alignItems:"center",borderRadius:"full",cursor:"pointer"})},$=({input:e,isLoading:r,onSelectorPress:o,perspective:l,selectorIcon:c,selectorLabel:s,accessories:i})=>{let{t:a}=y(),b=a(l==="buy"?"swapAssetCardBuyTitle":"swapAssetCardSellTitle"),u=(0,T.useMemo)(()=>{if(i)return T.default.createElement("div",{className:X.accessoryContainer},i)},[i]);return T.default.createElement("div",{className:X.container},T.default.createElement(x,{font:"labelSemibold",color:"textTertiary",children:b}),T.default.createElement("div",{className:X.tokenContainer},e,T.default.createElement("div",{className:X.spacer}),r?T.default.createElement(U,{width:112,height:24}):T.default.createElement("div",{onClick:o,className:X.selector,"data-testid":`${l}-token-button`},c,T.default.createElement(x,{font:"captionSemibold",color:"textPrimary",children:s}),T.default.createElement(Ne.ChevronDown,{size:14}))),u)};t();n();var Ze=32,re="_4ag7710",je="_4ag7712 _51gaznqk _51gaznvd _51gaznrp";t();n();var L=h(v()),Je={secondaryContainer:m({display:"flex",flexDirection:"row",alignItems:"center",gap:6}),pointer:m({cursor:"pointer"})},Ke=({isLoading:e,usdAmount:r,cryptoSubtitle:o,supportsCurrencyDenominationSwitching:l,switchCurrencyDenomination:c})=>{let s=ye(a=>a.primaryCurrency),i=(0,L.useMemo)(()=>e?L.default.createElement(U,{width:40,height:18,borderRadius:12,testID:"secondary-price-loader"}):s==="crypto"&&r?L.default.createElement(ze,{font:"caption",color:"textSecondary",value:r,intlNumberFormatOptions:{notation:"compact"}}):s==="fiat"&&o?L.default.createElement(x,{font:"caption",color:"textSecondary",children:o}):null,[o,s,e,r]);return L.default.createElement("div",{className:Je.secondaryContainer,onClick:c},i,l?L.default.createElement(V,{icon:"SwapVertical",size:20,shape:"circle",backgroundColor:{base:"gray",hover:"borderPrimary"},color:"textSecondary",className:Je.pointer}):null)};var ne={filters:m({display:"flex",flexDirection:"row",alignItems:"center",gap:4,marginLeft:2}),currencyPrefix:m({marginRight:2}),accessoryContainer:m({alignItems:"center",display:"flex",flexDirection:"row",gap:6,marginTop:6}),spacer:m({flex:1})},Re=({asset:e,perspective:r,onInputChange:o,onFungibleTap:l,onFilterTap:c,switchCurrencyDenomination:s,onBalanceTap:i})=>{let{amount:a,balance:b,fungible:u,isAmountEditable:F,isAmountLoading:A,isSecondaryCryptoSubtitleLoading:f,isWarning:D,secondaryCryptoSubtitle:B,amountPrefix:N,selectedFilter:Y,filters:p,supportsCurrencyDenominationSwitching:E,usdAmount:Q}=e,{t:se}=y(),dr=g.default.createElement(R,{image:{type:"fungible",src:u.data?.logoUri,fallback:u.data?.symbol||u.data?.tokenAddress},size:32,tokenType:u.type,chainMeta:u.data?.chain}),ur=M=>M.length>16?16:M.length>14?18:M.length>12?20:22,fr=(0,g.useMemo)(()=>{let M=ur(a);return A?g.default.createElement(U,{width:100,height:18,borderRadius:12}):g.default.createElement(_,{hasWarning:D,fontSize:M},g.default.createElement(x,{font:"heading3",children:N,className:ne.currencyPrefix}),g.default.createElement(O,{name:"amount",border:"0",placeholder:"0",warning:D,value:a,decimalLimit:u.data?.decimals,disabled:!F,onUserInput:o}))},[A,D,N,a,u.data?.decimals,F,o]),pe=(0,g.useMemo)(()=>g.default.createElement(Ke,{isLoading:f,usdAmount:Q,cryptoSubtitle:B,supportsCurrencyDenominationSwitching:E,switchCurrencyDenomination:s}),[f,Q,B,E,s]),le=(0,g.useMemo)(()=>{let M=W=>{switch(W){case .25:return"25%";case .5:return"50%";case 1:return se("swapAssetCardMaxButton")}};return p.map(W=>g.default.createElement(j,{key:W,active:W===Y,children:M(W),onChange:()=>c(W),className:re}))},[p,se,Y,c]),gr=(0,g.useMemo)(()=>g.default.createElement(g.default.Fragment,null,pe,g.default.createElement("div",{className:ne.spacer}),g.default.createElement(x,{font:"caption",color:{base:"textSecondary",hover:i?"textPrimary":void 0},children:b,onPress:i}),g.default.createElement("div",{className:ne.filters},le)),[b,le,i,pe]);return g.default.createElement($,{perspective:r,input:fr,selectorIcon:dr,selectorLabel:u.data?.symbol??"",onSelectorPress:l,accessories:gr})};t();n();var H=h(v());var er=({onSelectorTap:e,perspective:r})=>{let{t:o}=y();return H.default.createElement($,{perspective:r,input:H.default.createElement(_,{hasWarning:!1,fontSize:32},H.default.createElement(O,{name:"amount",border:"0",placeholder:"0",warning:!1,value:"",decimalLimit:0,disabled:!0,onUserInput:()=>{}})),selectorIcon:H.default.createElement(V,{icon:"Plus",size:28,shape:"circle",color:"textPrimary",backgroundColor:"bgArea"}),selectorLabel:o("commandSelect"),onSelectorPress:e,accessories:H.default.createElement(x,{font:"caption",color:"textSecondary",children:"$0"})})};t();n();var w=h(v());var ie={filters:m({display:"flex",flexDirection:"row",alignItems:"center",gap:4,marginLeft:2}),secondaryContainer:m({display:"flex",flexDirection:"row",alignItems:"center",gap:6}),currencyPrefix:m({marginRight:2}),accessoryContainer:m({alignItems:"center",display:"flex",flexDirection:"row",gap:6,marginTop:6}),spacer:m({flex:1})},Sr=[.5,1],rr=({perspective:e})=>{let{t:r}=y(),o=(0,w.useMemo)(()=>{let i=Number(De.typography.font.heading3.fontSize.replace("px",""));return w.default.createElement(_,{fontSize:i},w.default.createElement(O,{name:"amount",border:"0",placeholder:"0",warning:!1,value:e==="sell"?"":"0",onUserInput:()=>{}}))},[e]),l=(0,w.useMemo)(()=>w.default.createElement(x,{font:"caption",color:"textSecondary",children:"$0"}),[]),c=(0,w.useMemo)(()=>{let i=a=>{switch(a){case .25:return"25%";case .5:return"50%";case 1:return r("swapAssetCardMaxButton")}};return Sr.map(a=>w.default.createElement(j,{active:!1,children:i(a),onChange:()=>{},className:re}))},[r]),s=(0,w.useMemo)(()=>w.default.createElement(w.default.Fragment,null,w.default.createElement("div",{className:ie.secondaryContainer},l,e==="sell"?w.default.createElement(V,{icon:"SwapVertical",size:20,shape:"circle",backgroundColor:"gray",color:"textSecondary"}):null),w.default.createElement("div",{className:ie.spacer}),w.default.createElement(Be,{font:"caption",width:64}),e==="sell"?w.default.createElement("div",{className:ie.filters},c):null),[c,l,e]);return w.default.createElement($,{perspective:e,input:o,isLoading:!0,selectorIcon:null,selectorLabel:"",onSelectorPress:()=>{},accessories:s})};var or={container:m({display:"flex",flexDirection:"column",flex:1}),assets:m({gap:12,display:"flex",flexDirection:"column"})},tr=({onSelectAsset:e,isLoadingInitialAssets:r})=>{let{buy:o,sell:l,switchPerspective:c,setAmount:s,switchCurrencyDenomination:i,setFilter:a}=Pe(),{t:b}=y(),u=(0,P.useCallback)(async(A,f)=>{await ge.capture("swapperSwapDefaultQuoteSelected",{data:{quotePercentageValue:A}}),a(A,f)},[a]),F=(0,P.useMemo)(()=>[{perspective:"sell",asset:l},{perspective:"buy",asset:o}].map(({asset:A,perspective:f})=>{if(!A)return P.default.createElement(er,{key:f+"select",perspective:f,onSelectorTap:()=>e(f)});if(r)return P.default.createElement(rr,{key:f+"loading",perspective:f});let D=A.filters.length>0?()=>a(1,f):void 0,B=P.default.createElement(Re,{key:f,asset:A,perspective:f,switchCurrencyDenomination:i,onFungibleTap:()=>e(f),onInputChange:N=>{s(f,N)},onFilterTap:N=>u(N,f),onBalanceTap:D});return f==="buy"?P.default.createElement(q,{position:"relative",key:f+"swap-button"},B,P.default.createElement("div",{className:je,style:{pointerEvents:"none"}},P.default.createElement(Le,{label:b("swapperSwitchTokens"),size:Ze,shape:"circle",onClick:c,backgroundColor:{base:"accentPrimary",hover:"accentPrimaryLight"},icon:"SwapVertical"}))):B}),[l,o,r,i,u,e,s,a,c,b]);return P.default.createElement("div",{className:or.container},P.default.createElement("div",{className:or.assets},F))};t();n();var S=h(v());var yr=S.default.memo(({rankAlgo:e,limit:r})=>{let{t:o}=y(),l=ue(),c=G(p=>p.sortBy),s=G(p=>p.sortDirection),i=G(p=>p.timeFrame),a=G(p=>p.network),{data:[b]}=me(["enable-unified-token-pages"]),{data:u,error:F,isLoading:A,refetch:f}=Se({sortBy:c,sortDirection:s,timeFrame:i,limit:r,rankAlgo:e,network:a}),{pushDetailView:D}=Oe(),B=(0,S.useCallback)(({item:p,index:E})=>{let Q=de({chainId:p.chainId,address:p.address,resourceType:"address"});l.capture("onExploreListItemClickedByUser",{data:{explore:{context:"swap",listName:"tokens",itemDetails:{position:E,title:p.name||p.symbol,id:`${p.address}-${p.chainId}`}}}}),D(b?S.default.createElement(We,{caip19:Q,title:p.name??void 0,entryPoint:"exploreInSwap"}):S.default.createElement(Ve,{caip19:Q,title:p.name??void 0,entryPoint:"exploreInSwap"}))},[l,D,b]),N=!!F||u?.items.length===0,Y=A||typeof u>"u";return S.default.createElement(q,{direction:"column",paddingTop:24,flex:1},S.default.createElement(q,{paddingX:"screen",paddingBottom:10},S.default.createElement(x,{font:"title2",color:"textPrimary"},o("exploreTokens"))),S.default.createElement(Ge,{isError:N,isLoading:Y,refetch:f,filters:S.default.createElement(Xe,null),listHeader:S.default.createElement(Qe,null),rows:(u?.items??[]).map((p,E)=>({...qe({item:p,sortBy:c,image:S.default.createElement(Ye,{index:E,displayRank:!0,image:S.default.createElement(R,{image:{type:"fungible",src:p.logoUrl,fallback:p.symbol||p.name},size:48,badge:{type:"network",preset:Z.getChainID(p.chainId)}})})}),onClick:()=>B({item:p,index:E})}))}))});function br(){let e=we("tokens");return e?S.default.createElement(He,{sortByOptions:e.sortBy,sortBy:e.sortByDefault,sortDirection:e.sortDirectionDefault,networkOptions:e.network,network:e.networkDefault,timeFrameOptions:e.timeFrame,timeFrame:e.timeFrameDefault},S.default.createElement(yr,{rankAlgo:e.rankAlgo,limit:10})):null}var nr=S.default.memo(br);t();n();var sr=h(v());t();n();var ir=h(v());var xr={autoSlippageOptIn:"autoSlippageOptIn"},ar=()=>{let{handleShowModalVisibility:e}=ee();return(0,ir.useCallback)(r=>{let o=xr[r];if(!o)throw new Error(`Swap review interceptor modal not found or configured for ${r}.`);e(o)},[e])};var pr=()=>{let{goToInsufficientBalance:e,goToSwapReview:r,goToSwapTermsOfService:o,goToConfirmation:l}=$e(),c=ar(),{disabled:s,title:i,type:a,onClick:b}=Ae({goToInsufficientBalance:e,goToSwapReview:r,goToSwapTermsOfService:o,goToConfirmation:l,showOverlay:c});function u(F){return F==="primary"?"primary":F==="alert"?"warning":"default"}return sr.default.createElement(Me,{type:"submit",theme:u(a),disabled:s,onClick:b},i)};t();n();var z=h(v());t();n();var I=h(v());var hr=k.div`
  overflow-y: "scroll";
  padding-top: 16px;
`,vr=k.fieldset.attrs({disabled:!0})`
  pointer-events: none;
  user-select: none;
`,Cr=k.div`
  position: absolute;
  z-index: 1;
  top: 0;
  height: 100vh;
  width: 100%;
  background-color: ${oe("#222222",.75)};
`,lr=k.div`
  background-color: ${oe("#E5A221",.7)};
  padding: 12px 15px;
  position: absolute;
  /* TODO: change 15px to 16px and create a screen padding constant like on mobile */
  top: -15px;
  left: -15px;
  right: 15px;
  width: calc(100% + 2 * 15px);
`,cr=k(te).attrs({size:14,lineHeight:19,weight:500,color:"#fff",textAlign:"left"})``,ae=({message:e,swapDisabled:r=!0,children:o})=>r?I.default.createElement(I.default.Fragment,null,I.default.createElement(Cr,{"data-testid":"disable-overlay"},I.default.createElement(lr,null,I.default.createElement(cr,null,e))),I.default.createElement(vr,{"data-testid":"disable-wrapper"},o)):I.default.createElement(hr,null,I.default.createElement(lr,null,I.default.createElement(cr,null,e)),I.default.createElement(I.default.Fragment,null,o));var Pr=k.div`
  display: flex;
  flex: 1;
  padding-bottom: 16px;
  margin-bottom: -16px; // fix extension padding issue when scrollable or not
`,mr=z.default.memo(({children:e})=>{let r=fe(),o=r.every(a=>Z.isMainnetNetworkID(a)),c=xe().some(a=>r.includes(a)),{t:s}=y(),i=(0,z.useMemo)(()=>({availableOnlyOnMainnet:s("swapAvailableOnMainnet"),notAvailableOnSelectedNetwork:s("swapNotAvailableOnSelectedNetwork")}),[s]);return o?c?z.default.createElement(Pr,null,e):z.default.createElement(ae,{message:i.notAvailableOnSelectedNetwork,swapDisabled:!0},e):z.default.createElement(ae,{message:i.availableOnlyOnMainnet,swapDisabled:!0},e)});var kr=()=>{let[e]=Ee(),r=e.get("sellFungible")??void 0,o=e.get("sellAmount")??void 0,l=e.get("buyFungible")??void 0,c=ke(),{getSwapQuotesStrategy:s}=Ie();Ce({getSwapQuotesStrategy:s,skipInitialBuyFungible:!0}),he();let{isLoading:i}=ve({paramsSellFungible:r,paramsSellAmount:o,paramsBuyFungible:l});return(0,d.useLayoutEffect)(()=>c,[c]),{isLoadingInitialAssets:i}},Tr=({isLoadingInitialAssets:e})=>{let{quoteResponse:r,hasNoRoutes:o}=be(),{handleShowModalVisibility:l}=ee(),c=(0,d.useCallback)(u=>{l(u==="sell"?"swapSellAssetSelect":"swapBuyAssetSelect")},[l]),s=Fe({goToAssetSelect:c}),i=(0,d.useMemo)(()=>d.default.createElement(tr,{onSelectAsset:s,isLoadingInitialAssets:e}),[e,s]),a=ce.isFeatureEnabled("enable-swapper-token-discovery-ext"),b=(0,d.useMemo)(()=>a&&!r&&!o,[a,r,o]);return d.default.createElement(mr,null,d.default.createElement(J,{justify:"space-between",flex:1},d.default.createElement(J,{align:"normal",padding:"16px 16px 0 16px"},i,r||o?d.default.createElement(_e,null):null),b?d.default.createElement(nr,null):null,d.default.createElement(J,{padding:"0 16px"},r?d.default.createElement(pr,null):null)))},Ir=()=>d.default.createElement(Te,null,d.default.createElement(Ar,null)),Ar=()=>{let{isLoadingInitialAssets:e}=kr();return e?d.default.createElement(d.default.Fragment,null,d.default.createElement(K,{height:"120px",borderRadius:"8px 8px 0 0"}),d.default.createElement(Ue,{gap:1}),d.default.createElement(K,{height:"120px",borderRadius:"0 0 8px 8px"})):d.default.createElement(Tr,{isLoadingInitialAssets:e})},Sn=Ir;export{Sn as default};
//# sourceMappingURL=SwapTabPage-5MR26SMB.js.map
