{"commandAdd": "添加", "commandAccept": "接受", "commandApply": "应用", "commandApprove": "批准", "commandAllow": "允许", "commandBack": "返回", "commandBuy": "购买", "commandCancel": "取消", "commandClaim": "领取", "commandClaimReward": "领取奖励", "commandClear": "清除", "commandClose": "关闭", "commandConfirm": "确认", "commandConnect": "连接", "commandContinue": "继续", "commandConvert": "转换", "commandCopy": "复制", "commandCopyAddress": "复制地址", "commandCopyTokenAddress": "复制代币地址", "commandCreate": "创建", "commandCreateTicket": "创建工单", "commandDeny": "拒绝", "commandDismiss": "关闭", "commandDontAllow": "不允许", "commandDownload": "下载", "commandEdit": "编辑", "commandEditProfile": "编辑资料", "commandEnableNow": "立即启用", "commandFilter": "筛选", "commandFollow": "关注", "commandHelp": "帮助", "commandLearnMore": "了解详情", "commandLearnMore2": "了解详情", "commandMint": "铸造", "commandMore": "更多", "commandNext": "下一步", "commandNotNow": "以后再说", "commandOpen": "打开", "commandOpenSettings": "打开“设置”", "commandPaste": "粘贴", "commandReceive": "接收", "commandReconnect": "重新连接", "commandRecordVideo": "录制视频", "commandRequest": "请求", "commandRetry": "重试", "commandReview": "检查", "commandRevoke": "撤销", "commandSave": "保存", "commandScanQRCode": "扫描 QR 码", "commandSelect": "选择", "commandSelectMedia": "选择媒体", "commandSell": "出售", "commandSend": "发送", "commandShare": "分享", "commandShowBalance": "显示余额", "commandSign": "登录", "commandSignOut": "Sign Out", "commandStake": "质押", "commandMintLST": "铸造 JitoSOL", "commandSwap": "兑换", "commandSwapAgain": "再次兑换", "commandTakePhoto": "拍摄照片", "commandTryAgain": "重试", "commandViewTransaction": "查看交易", "commandReportAsNotSpam": "报告为非垃圾内容", "commandReportAsSpam": "报告为垃圾内容", "commandPin": "固定", "commandBlock": "屏蔽", "commandUnblock": "取消屏蔽", "commandUnstake": "取消质押", "commandUnpin": "取消固定", "commandHide": "隐藏", "commandUnhide": "取消隐藏", "commandBurn": "焚毁", "commandReport": "报告", "commandView": "查看", "commandProceedAnywayUnsafe": "仍然继续（不安全）", "commandUnfollow": "取消关注", "commandUnwrap": "解封", "commandConfirmUnsafe": "确认（不安全）", "commandYesConfirmUnsafe": "是，确认（不安全）", "commandConfirmAnyway": "仍然确认", "commandReportIssue": "报告问题", "commandSearch": "搜索", "commandShowMore": "展开", "commandShowLess": "收起", "pastParticipleClaimed": "已领取", "pastParticipleCompleted": "已完成", "pastParticipleCopied": "已复制", "pastParticipleDone": "完成", "pastParticipleDisabled": "已禁用", "pastParticipleRequested": "已请求", "nounName": "名称", "nounNetwork": "网络", "nounNetworkFee": "网络费用", "nounSymbol": "符号", "nounType": "类型", "nounDescription": "描述", "nounYes": "是", "nounNo": "否", "amount": "金额", "limit": "限额", "new": "新", "gotIt": "知道了", "internal": "内部", "reward": "奖励", "seeAll": "查看全部", "seeLess": "收起", "viewAll": "查看全部", "homeTab": "首页", "collectiblesTab": "收藏品", "swapTab": "兑换", "activityTab": "活动", "exploreTab": "浏览器", "accountHeaderConnectedInterpolated": "您已连接到 {{origin}}", "accountHeaderConnectedToSite": "您已连接到此网站", "accountHeaderCopyToClipboard": "复制到剪贴板", "accountHeaderNotConnected": "您还未连接到", "accountHeaderNotConnectedInterpolated": "您未连接到 {{origin}}", "accountHeaderNotConnectedToSite": "您还未连接到这个网站", "accountWithoutEnoughSolActionButtonCancel": "取消", "accountWithoutEnoughSolPrimaryText": "SOL 不足", "accountWithoutEnoughSolSecondaryText": "此交易涉及的账户没有足够的 SOL。此账户可能属于您或其他人。如果提交，此交易将被撤销。", "accountSwitcher": "账户切换器", "addAccountHardwareWalletPrimaryText": "连接硬件钱包", "addAccountHardwareWalletSecondaryText": "使用 Ledger 硬件钱包", "addAccountHardwareWalletSecondaryTextMobile": "使用您的 {{supportedHardwareWallets}} 钱包", "addAccountSeedVaultWalletPrimaryText": "关联种子库", "addAccountSeedVaultWalletSecondaryText": "使用种子库中的钱包", "addAccountImportSeedPhrasePrimaryText": "导入密钥恢复短语", "addAccountImportSeedPhraseSecondaryText": "从另一钱包导入账户", "addAccountImportWalletPrimaryText": "导入私钥", "addAccountImportWalletSecondaryText": "导入单链账户", "addAccountImportWalletSolanaSecondaryText": "导入 Solana 私钥", "addAccountLimitReachedText": "您已达到 Phantom 中的 {{accountsCount}} 个账户限制。请先删除未使用的账户，然后再添加其他账户。", "addAccountNoSeedAvailableText": "您没有可用种子短语。请导入现有种子以生成账户。", "addAccountNewWalletPrimaryText": "创建新账户", "addAccountNewWalletSecondaryText": "生成一个新的钱包地址", "addAccountNewMultiChainWalletSecondaryText": "添加新的多链账户", "addAccountNewSingleChainWalletSecondaryText": "添加新账户", "addAccountPrimaryText": "添加/连接钱包", "addAccountSecretPhraseLabel": "密钥短语", "addAccountSeedLabel": "种子", "addAccountSeedIDLabel": "种子 ID", "addAccountSecretPhraseDefaultLabel": "密钥短语 {{number}}", "addAccountPrivateKeyDefaultLabel": "私钥 {{number}}", "addAccountZeroAccountsForSeed": "0 个账户", "addAccountShowAccountForSeed": "显示 1 个账户", "addAccountShowAccountsForSeed": "显示 {{numOfAccounts}} 个账户", "addAccountHideAccountForSeed": "隐藏 1 个账户", "addAccountHideAccountsForSeed": "隐藏 {{numOfAccounts}} 个账户", "addAccountSelectSeedDescription": "您的新账户将通过此密钥短语生成", "addAccountNumAccountsForSeed": "{{numOfAccounts}} 个账户", "addAccountOneAccountsForSeed": "1 个账户", "addAccountGenerateAccountFromSeed": "创建账户", "addAccountReadOnly": "监视地址", "addAccountReadOnlySecondaryText": "跟踪任何公共钱包地址", "addAccountSolanaAddress": "Solana 地址", "addAccountEVMAddress": "EVM 地址", "addAccountBitcoinAddress": "比特币地址", "addAccountCreateSeedTitle": "创建新账户", "addAccountCreateSeedExplainer": "您的钱包还没有密钥短语！要创建一个新钱包，我们将为您生成一个恢复短语。请把它写下来妥善保存，不能透露给其他人。", "addAccountSecretPhraseHeader": "您的密钥短语", "addAccountNoSecretPhrases": "无可用密钥短语", "addAccountImportAccountActionButtonImport": "导入", "addAccountImportAccountDuplicatePrivateKey": "此账户在您的钱包中已存在", "addAccountImportAccountIncorrectFormat": "格式不正确", "addAccountImportAccountInvalidPrivateKey": "私钥无效", "addAccountImportAccountName": "名称", "addAccountImportAccountPrimaryText": "导入私钥", "addAccountImportAccountPrivateKey": "私钥", "addAccountImportAccountPublicKey": "地址或域", "addAccountImportAccountPrivateKeyRequired": "私钥是必填项", "addAccountImportAccountNameRequired": "名称为必填项", "addAccountImportAccountPublicKeyRequired": "公共地址为必填项", "addAccountImportAccountDuplicateAddress": "此地址在您的钱包中已存在", "addAddressAddressAlreadyAdded": "已成功添加地址", "addAddressAddressAlreadyExists": "地址已存在", "addAddressAddressInvalid": "地址无效", "addAddressAddressIsRequired": "地址为必填项", "addAddressAddressPlaceholder": "地址", "addAddressLabelIsRequired": "标签为必填项", "addAddressLabelPlaceholder": "标签", "addAddressPrimaryText": "添加地址", "addAddressToast": "地址已添加", "createAssociatedTokenAccountCostLabelInterpolated": "这将花费 {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "您已拥有此代币账户", "createAssociatedTokenAccountErrorInsufficientFunds": "资金不足", "createAssociatedTokenAccountErrorInvalidMint": "铸造地址无效", "createAssociatedTokenAccountErrorInvalidName": "名称无效", "createAssociatedTokenAccountErrorInvalidSymbol": "符号无效", "createAssociatedTokenAccountErrorUnableToCreateMessage": "我们无法创建您的代币账户。请重试。", "createAssociatedTokenAccountErrorUnableToCreateTitle": "无法创建账户", "createAssociatedTokenAccountErrorUnableToSendMessage": "我们无法发送您的交易。", "createAssociatedTokenAccountErrorUnableToSendTitle": "无法发送交易", "createAssociatedTokenAccountInputPlaceholderMint": "铸造地址", "createAssociatedTokenAccountInputPlaceholderName": "名称", "createAssociatedTokenAccountInputPlaceholderSymbol": "符号", "createAssociatedTokenAccountLoadingMessage": "我们正在创建您的代币账户。", "createAssociatedTokenAccountLoadingTitle": "正在创建代币账户", "createAssociatedTokenAccountPageHeader": "创建代币账户", "createAssociatedTokenAccountSuccessMessage": "您的代币账户已成功创建！", "createAssociatedTokenAccountSuccessTitle": "已创建代币账户", "createAssociatedTokenAccountViewTransaction": "查看交易", "assetDetailRecentActivity": "最近交易记录", "assetDetailStakeSOL": "质押 SOL 代币", "assetDetailUnknownToken": "未知代币", "assetDetailUnwrapAll": "解封所有代币", "assetDetailUnwrappingSOL": "正在解封 SOL", "assetDetailUnwrappingSOLFailed": "解封 SOL 失败", "assetDetailViewOnExplorer": "在 {{explorer}} 上查看", "assetDetailViewOnExplorerDefaultExplorer": "浏览器", "assetDetailSaveToPhotos": "保存到照片", "assetDetailSaveToPhotosToast": "已保存到“照片”", "assetDetailPinCollection": "置顶收藏", "assetDetailUnpinCollection": "取消置顶收藏", "assetDetailHideCollection": "隐藏收藏", "assetDetailUnhideCollection": "取消隐藏收藏", "assetDetailTokenNameLabel": "代币名称", "assetDetailNetworkLabel": "网络", "assetDetailAddressLabel": "地址", "assetDetailPriceLabel": "价格", "collectibleDetailSetAsAvatar": "设为头像", "collectibleDetailSetAsAvatarSingleWorkAlt": "头像", "collectibleDetailSetAsAvatarSuccess": "头像已设置", "collectibleDetailShare": "分享收藏品", "assetDetailTokenAddressCopied": "地址已复制", "assetDetailStakingLabel": "质押", "assetDetailAboutLabel": "关于 {{fungibleName}}", "assetDetailPriceDetail": "价格详细信息", "assetDetailHighlights": "亮点", "assetDetailAllTimeReturn": "所有时间回报", "assetDetailAverageCost": "平均成本", "assetDetailPriceHistoryUnavailable": "此代币的价格历史记录不可用", "assetDetailPriceHistoryInsufficientData": "此时间范围的价格历史记录不可用", "assetDetailPriceDataUnavailable": "价格数据不可用", "assetDetailPriceHistoryError": "提取价格历史记录时出错", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1 天", "assetDetailTimeFrame24h": "24 小时价格", "assetDetailTimeFrame1W": "1 周", "assetDetailTimeFrame1M": "1 个月", "assetDetailTimeFrameYTD": "年初至今", "assetDetailTimeFrameAll": "所有", "sendAssetAmountLabelInterpolated": "可用 {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "报价", "fiatRampNewQuote": "新报价", "assetListSelectToken": "选择代币", "assetListSearch": "搜索…", "assetListUnknownToken": "未知代币", "buyFlowHealthWarning": "我们的一些付款提供商正在经历高负荷。存款可能会延迟几个小时。", "assetVisibilityUnknownToken": "未知代币", "buyAssetInterpolated": "购买 {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "最大购买金额为 {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "最小购买金额为 {{amount}}", "buyNoAssetsAvailable": "无可用以太坊或 Polygon 资产", "buyThirdPartyScreenPaymentMethodSelector": "支付方式", "buyThirdPartyScreenPaymentMethod": "选择付款方式", "buyThirdPartyScreenChoseQuote": "输入有效的报价金额", "buyThirdPartyScreenProviders": "提供商", "buyThirdPartyScreenPaymentMethodTitle": "付款方式", "buyThirdPartyScreenPaymentMethodEmptyState": "您所在地区没有可用的付款方式", "buyThirdPartyScreenPaymentMethodFooter": "付款由网络合作伙伴提供支持。费用可能有所不同。某些付款方式在您的区域无法使用。", "buyThirdPartyScreenProvidersEmptyState": "您所在地区没有可用的提供商", "buyThirdPartyScreenLoadingQuote": "正在加载报价…", "buyThirdPartyScreenViewQuote": "查看报价", "gasEstimationErrorWarning": "估算本次交易的费用时出错。交易可能失败。", "gasEstimationCouldNotFetch": "无法获取 gas 估算", "networkFeeCouldNotFetch": "无法获取网络费用", "nativeTokenBalanceErrorWarning": "获取本次交易的代币余额时出错。交易可能失败。", "blocklistOriginCommunityDatabaseInterpolated": "此网站已被标记为<1>由社区维护的已知网络钓鱼网站和诈骗数据库</1>的一部分。如果您认为该网站被错误标记，<3>请提交问题</3>。", "blocklistOriginDomainIsBlocked": "{{domainName}} 已被屏蔽！", "blocklistOriginIgnoreWarning": "忽略此警告，仍然连接到 {{domainName}}。", "blocklistOriginSiteIsMalicious": "Phantom 认为这是恶意网站，存在安全隐患。", "blocklistOriginThisDomain": "此域", "blocklistProceedAnyway": "忽略警告，仍然继续", "maliciousTransactionWarning": "Phantom 认为这笔交易是恶意的，并且不安全。我们已禁用签署以保护您和您的资金。", "maliciousTransactionWarningIgnoreWarning": "忽略警告，仍然继续", "maliciousTransactionWarningTitle": "交易已被标记！", "maliciousRequestBlockedTitle": "请求被阻止", "maliciousRequestWarning": "此网站已被标记为恶意网站。它可能试图窃取您的资金或诱骗您确认欺骗性请求。", "maliciousSignatureRequestBlocked": "为了您的安全，Phantom 已阻止此请求。", "maliciousRequestBlocked": "为了您的安全，Phantom 已阻止此请求。", "maliciousRequestFrictionDescription": "继续操作不安全，因此 Phantom 阻止了此请求。您应关闭此对话框。", "maliciousRequestAcknowledge": "我了解使用此网站可能会导致我损失所有资金。", "maliciousRequestAreYouSure": "确定吗？", "siwErrorPopupTitle": "签名请求无效", "siwParseErrorDescription": "应用的签名请求格式无效，无法显示。", "siwVerificationErrorDescription": "签名请求消息存在 1 个或多个错误。为了您的安全考虑，请确保您使用的应用正确无误并重试。", "siwErrorPagination": "第 {{n}} 页，共 {{total}} 页", "siwErrorMessage_ADDRESS_MISMATCH": "警告：应用的地址与提供的签名地址不匹配。", "siwErrorMessage_DOMAIN_MISMATCH": "警告：应用的域与提供的验证域不匹配。", "siwErrorMessage_URI_MISMATCH": "警告：URI 主机名与域不匹配。", "siwErrorMessage_CHAIN_ID_MISMATCH": "警告：链 ID 与提供的验证链 ID 不匹配。", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "警告：消息发布日期在很久远的过去。", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "警告：消息发布日期在很遥远的未来。", "siwErrorMessage_EXPIRED": "警告：消息已过期。", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "警告：消息在发布前过期。", "siwErrorMessage_VALID_AFTER_EXPIRATION": "警告：消息将在生效前过期。", "siwErrorShowErrorDetails": "显示错误详细信息", "siwErrorHideErrorDetails": "隐藏错误详细信息", "siwErrorIgnoreWarning": "忽略警告，仍然继续", "siwsTitle": "登录请求", "siwsPermissions": "权限", "siwsAgreement": "消息", "siwsAdvancedDetails": "高级详细信息", "siwsAlternateStatement": "{{domain}} 希望您使用 Solana 账户登录：\n{{address}}", "siwsFieldLable_domain": "域", "siwsFieldLable_address": "地址", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "版本", "siwsFieldLable_chainId": "链 ID", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "发布时间", "siwsFieldLable_expirationTime": "过期时间", "siwsFieldLable_requestId": "请求 ID", "siwsFieldLable_resources": "资源", "siwsVerificationErrorDescription": "此登录请求无效。这意味着网站不安全，或者其开发者在发送请求时出错。", "siwsErrorNumIssues": "{{n}} 个问题", "siwsErrorMessage_CHAIN_ID_MISMATCH": "此链 ID 与您所在的网络不匹配。", "siwsErrorMessage_DOMAIN_MISMATCH": "此域不是您要登录的域。", "siwsErrorMessage_URI_MISMATCH": "此 URI 不是您要登录的 URI。", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "消息发布日期在很久远的过去。", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "消息发布日期在很遥远的未来。", "siwsErrorMessage_EXPIRED": "消息已过期。", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "消息在发出之前过期。", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "消息将在生效之前过期。", "changeLockTimerPrimaryText": "自动锁定时间", "changeLockTimerSecondaryText": "在钱包无活动多长时间后锁定您的钱包？", "changeLockTimerToast": "自动锁定计时器已更新", "changePasswordConfirmNewPassword": "确认新密码", "changePasswordCurrentPassword": "当前密码", "changePasswordErrorIncorrectCurrentPassword": "当前密码不正确", "changePasswordErrorGeneric": "出错了，请稍后再试", "changePasswordNewPassword": "新密码", "changePasswordPrimaryText": "更改密码", "changePasswordToast": "密码已更新", "collectionsSpamCollections": "垃圾收藏", "collectionsHiddenCollections": "已隐藏收藏", "collectiblesReportAsSpam": "报告为垃圾", "collectiblesReportAsSpamAndHide": "报告为垃圾并隐藏", "collectiblesReportAsNotSpam": "报告为非垃圾", "collectiblesReportAsNotSpamAndUnhide": "取消隐藏并报告非垃圾内容", "collectiblesReportNotSpam": "非垃圾内容", "collectionsManageCollectibles": "管理收藏品列表", "collectibleDetailDescription": "描述", "collectibleDetailProperties": "属性", "collectibleDetailOrdinalInfo": "序数信息", "collectibleDetailRareSatsInfo": "稀有聪信息", "collectibleDetailSatsInUtxo": "UTXO 中的聪", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} 聪", "collectibleDetailSatNumber": "聪编号", "collectibleDetailSatName": "聪名称", "collectibleDetailInscriptionId": "铭文 ID", "collectibleDetailInscriptionNumber": "铭文编号", "collectibleDetailStandard": "标准", "collectibleDetailCreated": "创建时间", "collectibleDetailViewOnExplorer": "在 {{explorer}} 上查看", "collectibleDetailList": "上架", "collectibleDetailSellNow": "出售价格为 {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "释放备用比特币", "collectibleDetailUtxoSplitterCtaSubtitle": "您有 {{value}} 的 BTC 可以解锁", "collectibleDetailUtxoSplitterModalCtaTitle": "稀有聪", "collectibleDetailUtxoSplitterModalCtaSubtitle": "为了保护您的资金，我们会阻止在包含稀有聪的 UTXO 中发送 BTC。使用 Magic Eden 的 UTXO 分割工具从您的稀有聪中释放 {{value}} 的 BTC。", "collectibleDetailUtxoSplitterModalCtaButton": "使用 UTXO 分割工具", "collectibleDetailEasilyAccept": "接受最高报价", "collectibleDetailSatsCount_one": "{{count}} 聪", "collectibleDetailSatsCount_other": "{{count}} 聪", "collectibleDetailSpamOverlayDescription": "此收藏品被隐藏，因为 Phantom 认为它是垃圾内容。", "collectibleDetailSpamOverlayReveal": "显示收藏品", "collectibleBurnTermsOfService": "我了解此操作无法撤消", "collectibleBurnTitleWithCount_one": "焚毁代币", "collectibleBurnTitleWithCount_other": "焚毁代币", "collectibleBurnDescriptionWithCount_one": "此操作将永久销毁此代币并将其从您的钱包中移除。", "collectibleBurnDescriptionWithCount_other": "此操作将永久销毁这些代币并将其从您的钱包中移除。", "collectibleBurnTokenWithCount_one": "代币", "collectibleBurnTokenWithCount_other": "代币", "collectibleBurnCta": "焚毁", "collectibleBurnRebate": "折扣", "collectibleBurnRebateTooltip": "少量的 SOL 将自动存入您的钱包，用于补偿代币焚毁。", "collectibleBurnNetworkFee": "网络费用", "collectibleBurnNetworkFeeTooltip": "Solana 网络处理交易所需的金额", "unwrapButtonSwapTo": "兑换为 {{chainSymbol}}", "unwrapButtonWithdrawFrom": "从 {{withdrawalSource}} 提取 {{chainSymbol}}", "unwrapModalEstimatedTime": "预计时间", "unwrapModalNetwork": "网络", "unwrapModalNetworkFee": "网络费用", "unwrapModalTitle": "汇总", "unsupportedChain": "链不受支持", "unsupportedChainDescription": "我们似乎不支持 {{chainName}} 网络的{{action}}操作。", "networkFeesTooltipLabel": "{{chainName}} 网络费用", "networkFeesTooltipDescription": "{{chainName}} 费用因多种因素而异。您可以自定义它们，以使您的交易更快（更昂贵）或更慢（更便宜）。", "burnStatusErrorTitleWithCount_one": "代币无法焚毁", "burnStatusErrorTitleWithCount_other": "代币无法焚毁", "burnStatusSuccessTitleWithCount_one": "代币已焚毁！", "burnStatusSuccessTitleWithCount_other": "代币已焚毁！", "burnStatusLoadingTitleWithCount_one": "正在焚毁代币…", "burnStatusLoadingTitleWithCount_other": "正在焚毁代币…", "burnStatusErrorMessageWithCount_one": "此代币无法焚毁。请稍后再试。", "burnStatusErrorMessageWithCount_other": "这些代币无法焚毁。请稍后再试。", "burnStatusSuccessMessageWithCount_one": "此代币已被永久销毁，并且 {{rebateAmount}} SOL 已存入您的钱包。", "burnStatusSuccessMessageWithCount_other": "这些代币已被永久销毁，并且 {{rebateAmount}} SOL 已存入您的钱包。", "burnStatusLoadingMessageWithCount_one": "此代币正被永久销毁，并且 {{rebateAmount}} SOL 将存入您的钱包。", "burnStatusLoadingMessageWithCount_other": "这些代币正被永久销毁，并且 {{rebateAmount}} SOL 将存入您的钱包。", "burnStatusViewTransactionText": "查看交易", "collectibleDisplayLoading": "正在加载…", "collectiblesNoCollectibles": "没有收藏品", "collectiblesPrimaryText": "您的收藏品", "collectiblesReceiveCollectible": "接收收藏品", "collectiblesUnknownCollection": "未知收藏", "collectiblesUnknownCollectible": "未知收藏品", "collectiblesUniqueHolders": "唯一身份持有者", "collectiblesSupply": "供应", "collectiblesUnknownTokens": "未知代币", "collectiblesNrOfListed": "{{ nrOfListed }} 已上架", "collectiblesListed": "已上架", "collectiblesMintCollectible": "铸造收藏品", "collectiblesYouMint": "您铸造", "collectiblesMintCost": "铸造成本", "collectiblesMintFail": "铸造失败", "collectiblesMintFailMessage": "铸造您的收藏品时出现问题。请重试。", "collectiblesMintCostFree": "免费", "collectiblesMinting": "正在铸造…", "collectiblesMintingMessage": "您的收藏品正在铸造中", "collectiblesMintShareSubject": "快来看看", "collectiblesMintShareMessage": "这是我在 @phantom 上铸造的！", "collectiblesMintSuccess": "铸造成功", "collectiblesMintSuccessMessage": "您的收藏品正在铸造中", "collectiblesMintSuccessQuestMessage": "您已满足 Phantom 任务的要求。点按“领取奖励”可获取免费收藏品。", "collectiblesMintRequired": "必需", "collectiblesMintMaxLengthErrorMessage": "超出最大长度", "collectiblesMintSafelyDismiss": "您可以安全关闭此窗口。", "collectiblesTrimmed": "我们已达到目前可以展示的收藏品数量上限。", "collectiblesNonTransferable": "不可转让", "collectiblesNonTransferableYes": "是", "collectiblesSellOfferDetails": "报价详细信息", "collectiblesSellYouSell": "出售", "collectiblesSellGotIt": "知道了", "collectiblesSellYouReceive": "收到", "collectiblesSellOffer": "报价", "collectiblesSoldCollectible": "出售的收藏品", "collectiblesSellMarketplace": "市场", "collectiblesSellCollectionFloor": "收藏底价", "collectiblesSellDifferenceFromFloor": "与底价的差异", "collectiblesSellLastSalePrice": "最后售价", "collectiblesSellEstimatedFees": "预估费用", "collectiblesSellEstimatedProfitAndLoss": "预估利润/损失", "collectiblesSellViewOnMarketplace": "在 {{marketplace}} 上查看", "collectiblesSellCollectionFloorTooltip": "多个市场中收藏的最低“立即购买”价格。", "collectiblesSellProfitLossTooltip": "估算的利润/损失是根据最终销售价格和报价金额扣除费用计算的。", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "版税 ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "市场费用 ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "市场费用", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}} 网络", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "报价包含 {{phantomFeePercentage}} 的 Phantom 费用", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "报价包含版税、网络费用、市场费用和 {{phantomFeePercentage}} 的 Phantom 费用", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "报价包含版税、网络费用和市场费用", "collectiblesSellTransactionFeeTooltipTitle": "交易费用", "collectiblesSellStatusLoadingTitle": "正在接受报价…", "collectiblesSellStatusLoadingIsSellingFor": "出售价格为", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} 已出售！", "collectiblesSellStatusSuccessWasSold": "成功出售，价格为", "collectiblesSellStatusErrorTitle": "出错了", "collectiblesSellStatusErrorSubtitle": "尝试出售时出现问题", "collectiblesSellStatusViewTransaction": "查看交易", "collectiblesSellInsufficientFundsTitle": "资金不足", "collectiblesSellInsufficientFundsSubtitle": "我们无法接受对此收藏品的报价，因为没有足够的资金来支付网络费用。", "collectiblesSellRecentlyTransferedNFTTitle": "最近转账", "collectiblesSellRecentlyTransferedNFTSubtitle": "转账后，您必须等待 1 小时才能接受出价。", "collectiblesApproveCollection": "已批准的 {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "报价不可用", "collectiblesSellNotAvailableAnymoreSubtitle": "该报价已不再可用。请取消此出价并重试", "collectiblesSellFlaggedTokenTitle": "收藏品被举报", "collectiblesSellFlaggedTokenSubtitle": "该收藏品无法交易，原因可能有多种，例如被举报为失窃或在没有锁定的情况下被质押", "collectiblesListOnMagicEden": "在 Magic Eden 上架", "collectiblesListPrice": "上架价格", "collectiblesUseFloor": "使用底价", "collectiblesFloorPrice": "底价", "collectiblesLastSalePrice": "最后售价", "collectiblesTotalReturn": "总回报", "collectiblesOriginalPurchasePrice": "原始购买价格", "collectiblesMagicEdenFee": "Magic Eden 费用", "collectiblesArtistRoyalties": "艺术家版税", "collectiblesListNowButton": "立即上架", "collectiblesListAnywayButton": "仍然上架", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "查看上架", "collectiblesListingViewTransaction": "查看交易", "collectiblesRemoveListing": "移除上架", "collectiblesEditListing": "编辑上架", "collectiblesEditListPrice": "编辑上架价格", "collectiblesListPriceTooltip": "上架价格是商品的销售价格。卖家通常将上架价格设定为等于或高于底价。", "collectiblesFloorPriceTooltip": "底价是此收藏中物品的最低有效上架价格。", "collectiblesOriginalPurchasePriceTooltip": "您最初按此金额购买了此物品。", "collectiblesPurchasedForSol": "以 {{lastPurchasePrice}} SOL 购买", "collectiblesUnableToLoadListings": "无法加载上架", "collectiblesUnableToLoadListingsFrom": "无法从 {{marketplace}} 加载上架", "collectiblesUnableToLoadListingsDescription": "您的上架和资产是安全的，但我们目前无法从 {{marketplace}} 加载它们。请稍后再试。", "collectiblesBelowFloorPrice": "低于底价", "collectiblesBelowFloorPriceMessage": "确定要以低于底价的价格上架您的 NFT 吗？", "collectiblesMinimumListingPrice": "最低价格为 0.01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden 对完成的交易收取一定的费用。", "collectiblesArtistRoyaltiesTooltip": "此收藏的创作者从每笔完成的销售中获得一定比例的版税。", "collectibleScreenCollectionLabel": "收藏", "collectibleScreenPhotosPermissionTitle": "照片权限", "collectibleScreenPhotosPermissionMessage": "我们需要您的许可才能访问您的照片。请转到“设置”并更新您的权限。", "collectibleScreenPhotosPermissionOpenSettings": "打开“设置”", "listStatusErrorTitle": "上架失败", "editListStatusErrorTitle": "无法更新", "removeListStatusErrorTitle": "移除上架失败", "listStatusSuccessTitle": "已创建上架！", "editListingStatusSuccessTitle": "已更新上架！", "removeListStatusSuccessTitle": "已从 Magic Eden 中移除上架", "listStatusLoadingTitle": "正在创建上架…", "editListingStatusLoadingTitle": "正在更新上架…", "removeListStatusLoadingTitle": "正在移除上架…", "listStatusErrorMessage": "{{name}} 无法在 Magic Eden 上架", "removeListStatusErrorMessage": "{{name}} 无法在 Magic Eden 下架", "listStatusSuccessMessage": "{{name}} 现在在 Magic Eden 上以 {{listCollectiblePrice}} SOL 上架", "editListingStatusSuccessMessage": "{{name}} 现在在 Magic Eden 上已更新为 {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} 已成功从 Magic Eden 中移除", "listStatusLoadingMessage": "正在 Magic Eden 上以 {{listCollectiblePrice}} SOL 上架 {{name}}。", "editListingStatusLoadingMessage": "正在 Magic Eden 上将 {{name}} 更新为 {{editListCollectiblePrice}} SOL。", "removeListStatusLoadingMessage": "正在从 Magic Eden 中移除 {{name}}。这可能需要一段时间。", "listStatusLoadingSafelyDismiss": "您可以安全地关闭此窗口。", "listStatusViewOnMagicEden": "在 Magic Eden 上查看", "listStatusViewOnMarketplace": "在 {{marketplace}} 上查看", "listStatusLoadingDismiss": "关闭", "listStatusViewTransaction": "查看交易", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "关联您的硬件钱包并确保它已解锁。一旦我们检测到它，您就可以选择要使用的地址。", "connectHardwareFailedPrimaryText": "连接失败", "connectHardwareFailedSecondaryText": "请关联您的硬件钱包并确保它已解锁。一旦我们发现它，您就可以选择要使用的地址。", "connectHardwareFinishPrimaryText": "账户已添加！", "connectHardwareFinishSecondaryText": "您现在可以从 Phantom 中访问您的 Ledger Nano 钱包。请返回扩展程序。", "connectHardwareNeedsPermissionPrimaryText": "连接新钱包", "connectHardwareNeedsPermissionSecondaryText": "点击下面的按钮开始连接过程。", "connectHardwareSearchingPrimaryText": "正在搜索钱包…", "connectHardwareSearchingSecondaryText": "关联您的硬件钱包并确保它已解锁，同时确保您已在浏览器中批准权限。", "connectHardwarePermissionDeniedPrimary": "权限被拒绝", "connectHardwarePermissionDeniedSecondary": "授予 Phantom 与您的 Ledger 设备连接的权限", "connectHardwarePermissionUnableToConnect": "无法连接", "connectHardwarePermissionUnableToConnectDescription": "我们无法连接到您的 Ledger 设备，可能需要更多权限。", "connectHardwareSelectAddressAllAddressesImported": "所有地址已导入", "connectHardwareSelectAddressDerivationPath": "派生路径", "connectHardwareSelectAddressSearching": "正在搜索…", "connectHardwareSelectAddressSelectWalletAddress": "选择钱包地址", "connectHardwareSelectAddressWalletAddress": "钱包地址", "connectHardwareWaitingForApplicationSecondaryText": "请关联您的硬件钱包并确保它已解锁。", "connectHardwareWaitingForPermissionPrimaryText": "需要权限", "connectHardwareWaitingForPermissionSecondaryText": "关联您的硬件钱包并确保它已解锁，同时确保您已在浏览器中批准权限。", "connectHardwareAddAccountButton": "添加账户", "connectHardwareLedger": "关联您的 Ledger", "connectHardwareStartConnection": "点击下方按钮开始连接 Ledger 硬件钱包", "connectHardwarePairSuccessPrimary": "{{productName}} 已连接", "connectHardwarePairSuccessSecondary": "您已成功连接 {{productName}}。", "connectHardwareSelectChains": "选择要连接的链", "connectHardwareSearching": "正在搜索…", "connectHardwareMakeSureConnected": "请连接并解锁您的硬件钱包。请批准相关浏览器权限。", "connectHardwareOpenAppDescription": "请解锁您的硬件钱包", "connectHardwareConnecting": "正在连接…", "connectHardwareConnectingDescription": "我们正在连接到您的 Ledger 设备。", "connectHardwareConnectingAccounts": "正在关联您的账户…", "connectHardwareDiscoveringAccounts": "正在搜索账户…", "connectHardwareDiscoveringAccountsDescription": "我们正在查找您账户中的活动。", "connectHardwareErrorLedgerLocked": "Ledger 已被锁定", "connectHardwareErrorLedgerLockedDescription": "请确保您的 Ledger 设备已解锁，然后重试。", "connectHardwareErrorLedgerGeneric": "出错了", "connectHardwareErrorLedgerGenericDescription": "无法找到账户。请确保您的 Ledger 设备已解锁，然后重试。", "connectHardwareErrorLedgerPhantomLocked": "请重新打开 Phantom 并尝试再次关联您的硬件。", "connectHardwareFindingAccountsWithActivity": "正在查找 {{chainName}} 账户…", "connectHardwareFindingAccountsWithActivityDualChain": "正在查找 {{chainName1}} 或 {{chainName2}} 账户…", "connectHardwareFoundAccountsWithActivity": "我们在您的 Ledger 上找到了 {{numOfAccounts}} 个有活动的账户。", "connectHardwareFoundAccountsWithActivitySingular": "我们在您的 Ledger 上找到了 1 个有活动的账户。", "connectHardwareFoundSomeAccounts": "我们在您的 Ledger 设备上找到了一些账户。", "connectHardwareViewAccounts": "查看账户", "connectHardwareConnectAccounts": "账户已关联", "connectHardwareSelectAccounts": "选择账户", "connectHardwareChooseAccountsToConnect": "选择要连接的钱包账户。", "connectHardwareAccountsAddedInterpolated": "已添加 {{numOfAccounts}} 个账户", "connectHardwareAccountsStepOfSteps": "第 {{stepNum}} 步，共 {{totalSteps}} 步", "connectHardwareMobile": "关联 Ledger", "connectHardwareMobileTitle": "关联您的 Ledger 硬件钱包", "connectHardwareMobileEnableBluetooth": "启用蓝牙", "connectHardwareMobileEnableBluetoothDescription": "允许使用蓝牙连接的权限", "connectHardwareMobileEnableBluetoothSettings": "前往“设置”，允许 Phantom 使用“位置”和“附近设备”权限。", "connectHardwareMobilePairWithDevice": "与您的 Ledger 设备配对", "connectHardwareMobilePairWithDeviceDescription": "将设备放在附近以获得最佳信号", "connectHardwareMobileConnectAccounts": "关联账户", "connectHardwareMobileConnectAccountsDescription": "我们将查找您可能使用过的任何账户中的活动", "connectHardwareMobileConnectLedgerDevice": "关联您的 Ledger 设备", "connectHardwareMobileLookingForDevices": "正在查找附近的设备…", "connectHardwareMobileLookingForDevicesDescription": "请关联您的 Ledger 设备并确保它已解锁。", "connectHardwareMobileFoundDeviceSingular": "我们找到了 1 个 Ledger 设备", "connectHardwareMobileFoundDevices": "我们找到了 {{numDevicesFound}} 个 Ledger 设备", "connectHardwareMobileFoundDevicesDescription": "在下方选择一个 Ledger 设备以开始配对。", "connectHardwareMobilePairingWith": "正在与 {{deviceName}} 配对", "connectHardwareMobilePairingWithDescription": "配对时，请遵循您的 Ledger 设备上的说明。", "connectHardwareMobilePairingFailed": "配对未成功", "connectHardwareMobilePairingFailedDescription": "无法与 {{device<PERSON>ame}} 配对。请确保您的设备已解锁。", "connectHardwareMobilePairingSuccessful": "配对成功", "connectHardwareMobilePairingSuccessfulDescription": "您已成功配对并关联 Ledger 设备。", "connectHardwareMobileOpenAppSingleChain": "在您的 Ledger 上打开 {{chainName}} 应用", "connectHardwareMobileOpenAppDualChain": "在您的 Ledger 上打开 {{chainName1}} 或 {{chainName2}} 应用", "connectHardwareMobileOpenAppDescription": "确保您的设备已解锁。", "connectHardwareMobileStillCantFindDevice": "仍然找不到您的设备？", "connectHardwareMobileLostConnection": "失去连接", "connectHardwareMobileLostConnectionDescription": "我们失去了与 {{deviceName}} 的连接。请确保您的设备已解锁，然后重试。", "connectHardwareMobileGenericLedgerDevice": "Ledger 设备", "connectHardwareMobileConnectDeviceSigning": "关联您的 {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "解锁您的 Ledger 设备并将其放在附近。", "connectHardwareMobileBluetoothDisabled": "蓝牙已禁用", "connectHardwareMobileBluetoothDisabledDescription": "请启用蓝牙并确保您的 Ledger 设备已解锁。", "connectHardwareMobileLearnMore": "了解详情", "connectHardwareMobileBlindSigningDisabled": "盲签名已禁用", "connectHardwareMobileBlindSigningDisabledDescription": "确保已在设备上启用盲签名。", "connectHardwareMobileConfirmSingleChain": "您需要在硬件钱包上确认交易。请确保钱包已解锁。", "metamaskExplainerBottomSheetHeader": "本网站支持 Phantom", "metamaskExplainerBottomSheetSubheader": "从“关联钱包”对话框中选择 MetaMask 以继续。", "metamaskExplainerBottomSheetDontShowAgain": "不再显示", "ledgerStatusNotConnected": "Ledger 未连接", "ledgerStatusConnectedInterpolated": "{{productName}} 已连接", "connectionClusterInterpolated": "您目前位于 {{cluster}}", "connectionClusterTestnetMode": "您目前处于测试网模式", "featureNotSupportedOnLocalNet": "启用 Solana Localnet 时不支持此功能。", "readOnlyAccountBannerWarning": "您正在监视此账户", "depositAddress": "接收地址", "depositAddressChainInterpolated": "您的 {{chain}} 地址", "depositAssetDepositInterpolated": "接收 {{tokenSymbol}}", "depositAssetSecondaryText": "此地址只能用于接收兼容的代币。", "depositAssetTextInterpolated": "使用此地址在 <1>{{network}}</1> 上接收代币和收藏品。", "depositAssetTransferFromExchange": "从交易所转移", "depositAssetShareAddress": "分享地址", "depositAssetBuyOrDeposit": "购买或转移", "depositAssetBuyOrDepositDesc": "为您的钱包充值以开始使用", "depositAssetTransfer": "转移", "editAddressAddressAlreadyAdded": "已成功添加地址", "editAddressAddressAlreadyExists": "地址已存在", "editAddressAddressIsRequired": "地址为必填项", "editAddressPrimaryText": "编辑地址", "editAddressRemove": "从地址簿移除", "editAddressToast": "地址已更新", "removeSavedAddressToast": "地址已移除", "exportSecretErrorGeneric": "出错了，请稍后再试", "exportSecretErrorIncorrectPassword": "密码不正确", "exportSecretPassword": "密码", "exportSecretPrivateKey": "私钥", "exportSecretSecretPhrase": "密钥短语", "exportSecretPIN": "PIN 码", "exportSecretSecretRecoveryPhrase": "助记词", "exportSecretSelectYourAccount": "选择您的账户", "exportSecretShowPrivateKey": "显示私钥", "exportSecretShowSecretRecoveryPhrase": "显示密钥恢复短语", "exportSecretShowSecret": "显示{{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "<1>请勿</1>分享您的 {{secretNameText}}！", "exportSecretWarningSecondaryInterpolated": "如果有人获得您的 {{secretNameText}}，他们就能够完全控制您的钱包。", "exportSecretOnlyWay": "您的{{secretNameText}}是找回钱包的唯一方式", "exportSecretDoNotShow": "请勿让任何人看到您的{{secretNameText}}", "exportSecretWillNotShare": "我不会与任何人分享我的{{secretNameText}}，包括 Phantom。", "exportSecretNeverShare": "请勿与任何人分享您的{{secretNameText}}", "exportSecretYourPrivateKey": "您的私钥", "exportSecretYourSecretRecoveryPhrase": "您的密钥恢复短语", "exportSecretResetPin": "重置您的 PIN 码", "fullPageHeaderBeta": "Beta！", "fullPageHeaderHelp": "帮助中心", "gasUpTo": "最多 {{ amount }}", "timeDescription1hour": "大约 1 小时", "timeDescription30minutes": "大约 30 分钟", "timeDescription10minutes": "大约 10 分钟", "timeDescription2minutes": "大约 2 分钟", "timeDescription30seconds": "大约 30 秒", "timeDescription15seconds": "大约 15 秒", "timeDescription10seconds": "大约 10 秒", "timeDescription5seconds": "大约 5 秒", "timeDescriptionAbbrev1hour": "1 小时", "timeDescriptionAbbrev30minutes": "30 分钟", "timeDescriptionAbbrev10minutes": "10 分钟", "timeDescriptionAbbrev2minutes": "2 分钟", "timeDescriptionAbbrev30seconds": "30 秒", "timeDescriptionAbbrev15seconds": "15 秒", "timeDescriptionAbbrev10seconds": "10 秒", "timeDescriptionAbbrev5seconds": "5 秒", "gasSlow": "慢", "gasAverage": "中", "gasFast": "快", "satsPerVirtualByte": "{{satsPerVirtualByte}} 聪/vB", "satsAmount": "{{sats}} 聪", "homeErrorButtonText": "重试", "homeErrorDescription": "尝试检索您的资产时出错。请刷新并重试", "homeErrorTitle": "无法获取资产", "homeManageTokenList": "管理代币列表", "interstitialDismissUnderstood": "知道了", "interstitialBaseWelcomeTitle": "Phantom 现已支持 Base！", "interstitialBaseWelcomeItemTitle_1": "发送、接收和购买代币", "interstitialBaseWelcomeItemTitle_2": "探索 Base 生态系统", "interstitialBaseWelcomeItemTitle_3": "安全无忧", "interstitialBaseWelcomeItemDescription_1": "在 Base 上转移和使用 {{paymentMethod}}、银行卡或 Coinbase 购买 USDC 与 ETH。", "interstitialBaseWelcomeItemDescription_2": "将 Phantom 与您最喜爱的 DeFi 和 NFT 应用搭配使用。", "interstitialBaseWelcomeItemDescription_3": "使用 Ledger 支持、垃圾内容过滤和交易模拟保持安全。", "privacyPolicyChangedInterpolated": "我们的《隐私政策》已更改。<1>了解详情</1>", "bitcoinAddressTypesBodyTitle": "比特币地址类型", "bitcoinAddressTypesFeature1Title": "关于比特币地址", "bitcoinAddressTypesFeature1Subtitle": "Phantom 支持原生隔离见证和 Taproot，每种都有自己的余额。您可以使用任一地址类型发送 BTC 或 Ordinals。", "bitcoinAddressTypesFeature2Title": "原生隔离见证", "bitcoinAddressTypesFeature2Subtitle": "Phantom 中的默认 BTC 地址。比 Taproot 更古老，但兼容所有钱包和交易所。", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "最适合 Ordinals 和 BRC-20，费用最便宜。在“偏好设置 -> 首选比特币地址”中调整地址。", "headerTitleInfo": "信息", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "这是您的 <1>{{addressType}}</1> 地址。", "invalidChecksumTitle": "我们已升级您的密钥短语！", "invalidChecksumFeature1ExportPhrase": "导出新的密钥短语", "invalidChecksumFeature1ExportPhraseDescription": "请备份您的新密钥短语以及您原有账户的私钥。", "invalidChecksumFeature2FundsAreSafe": "您的资金安全无忧", "invalidChecksumFeature2FundsAreSafeDescription": "本次升级是自动完成的。Phantom 中的任何人均无法获取您的密钥短语或存取您的资金。", "invalidChecksumFeature3LearnMore": "了解详情", "invalidChecksumFeature3LearnMoreDescription": "您的助记词与大多数钱包不兼容。请阅读<1>这篇帮助文章</1>了解详情。", "invalidChecksumBackUpSecretPhrase": "备份密钥短语", "migrationFailureTitle": "迁移账户时出错", "migrationFailureFeature1": "导出您的密钥短语", "migrationFailureFeature1Description": "登录前请备份您的密钥短语。", "migrationFailureFeature2": "登录 Phantom", "migrationFailureFeature2Description": "您需要重新登录 Phantom 才能查看您的账户。", "migrationFailureFeature3": "了解详情", "migrationFailureFeature3Description": "阅读<1>此帮助文章</1>详细了解相关信息。", "migrationFailureContinueToOnboarding": "继续登录", "migrationFailureUnableToFetchMnemonic": "我们无法加载您的密钥短语", "migrationFailureUnableToFetchMnemonicDescription": "请联系支持团队并下载应用日志进行调试", "migrationFailureContactSupport": "联系支持团队", "ledgerActionConfirm": "在 Ledger Nano 上确认", "ledgerActionErrorBlindSignDisabledPrimaryText": "盲签名已禁用", "ledgerActionErrorBlindSignDisabledSecondaryText": "请确保在您的硬件设备上启用了盲签名，然后重试该操作", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "硬件设备在运行过程中断开", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "请关闭 Phantom 扩展程序，然后重试该操作", "ledgerActionErrorDeviceLockedPrimaryText": "硬件设备已锁定", "ledgerActionErrorDeviceLockedSecondaryText": "请解锁您的硬件设备，然后重试该操作", "ledgerActionErrorHeader": "分类账操作出错", "ledgerActionErrorUserRejectionPrimaryText": "用户拒绝了交易", "ledgerActionErrorUserRejectionSecondaryText": "用户在硬件设备上拒绝了该操作", "ledgerActionNeedPermission": "需要权限", "ledgerActionNeedToConfirm": "您需要在硬件钱包上确认交易。确保它在 {{chainType}} 应用上已解锁。", "ledgerActionNeedToConfirmMany": "您将需要在硬件钱包上确认 {{numberOfTransactions}} 个交易。确保它在 {{chainType}} 应用上已解锁。", "ledgerActionNeedToConfirmBlind": "您需要在硬件钱包上确认交易。确保它在 {{chainType}} 应用上已解锁，并且已启用盲签名。", "ledgerActionNeedToConfirmBlindMany": "您将需要在硬件钱包上确认 {{numberOfTransactions}} 个交易。确保它在 {{chainType}} 应用上已解锁，并且已启用盲签名。", "ledgerActionPleaseConnect": "请关联您的 Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "请关联您的硬件钱包并确保它已解锁。确保您在浏览器中拥有批准交易的权限。", "maxInputAmount": "金额", "maxInputMax": "最大", "notEnoughSolPrimaryText": "SOL 不足", "notEnoughSolSecondaryText": "您的钱包中没有足够的 SOL 来完成此交易。请存入更多，然后重试。", "insufficientBalancePrimaryText": "{{tokenSymbol}} 不足", "insufficientBalanceSecondaryText": "您的钱包中没有足够的 {{tokenSymbol}} 来完成此交易。", "insufficientBalanceRemaining": "剩余", "insufficientBalanceRequired": "需要", "notEnoughSplTokensTitle": "代币不足", "notEnoughSplTokensDescription": "您的钱包中没有足够的代币来完成此交易。如果提交，此交易将被撤销。", "transactionExpiredPrimaryText": "交易过期", "transactionExpiredSecondaryText": "您过了太久才确认交易，交易已过期。如果提交，此交易将被撤销。", "transactionHasWarning": "交易警告", "tokens": "代币", "notificationApplicationApprovalPermissionsAddressVerification": "验证您拥有此地址", "notificationApplicationApprovalPermissionsTransactionApproval": "请求批准交易", "notificationApplicationApprovalPermissionsViewWalletActivity": "查看您的钱包余额和活动", "notificationApplicationApprovalParagraphText": "确认将允许此站点查看所选账户的余额和活动。", "notificationApplicationApprovalActionButtonConnect": "连接", "notificationApplicationApprovalActionButtonSignIn": "登录", "notificationApplicationApprovalAllowApproval": "允许该网站连接钱包？", "notificationApplicationApprovalAutoConfirm": "自动确认交易", "notificationApplicationApprovalConnectDisclaimer": "仅连接到您信任的网站", "notificationApplicationApprovalSignInDisclaimer": "仅登录您信任的网站", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "此网站不安全，可能会试图窃取您的资金。", "notificationApplicationApprovalConnectUnknownApp": "未知", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "无法连接到应用", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "此应用正在尝试连接到 {{appNetworkName}}，但选择了 {{phantomNetworkName}}。", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "要使用 {{networkName}}，请转到“开发者设置 → 测试网模式”。", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "未知网络", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Ledger 目前不支持关联到其他移动应用。", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "请切换到非 Ledger 账户或使用应用内浏览器再试一次。", "notificationSignatureRequestConfirmTransaction": "确认交易", "notificationSignatureRequestConfirmTransactionCapitalized": "确认交易", "notificationSignatureRequestConfirmTransactions": "确认交易", "notificationSignatureRequestConfirmTransactionsCapitalized": "确认交易", "notificationSignatureRequestSignatureRequest": "签名请求", "notificationMessageHeader": "消息", "notificationMessageCopied": "消息已复制", "notificationAutoConfirm": "自动确认", "notificationAutoConfirmOff": "关", "notificationAutoConfirmOn": "开", "notificationConfirmFooter": "仅当您信任此网站时确认。", "notificationEstimatedTime": "预计时间", "notificationPermissionRequestText": "这只是一个权限请求。交易可能不会立即执行。", "notificationBalanceChangesText": "余额变动为估计值。不保证所涉及的金额和资产。", "notificationContractAddress": "合约地址", "notificationAdvancedDetailsText": "高级", "notificationUnableToSimulateWarningText": "我们目前无法估计余额变动。您可以稍后再试，或确认是否信任此网站。", "notificationSignMessageParagraphText": "签署此消息将证明您拥有所选账户的所有权。", "notificationSignatureRequestScanFailedDescription": "无法扫描消息是否存在安全问题。继续操作时请多加留意。", "notificationFailedToScan": "无法模拟此请求的结果。\n确认操作不安全，可能会导致损失。", "notificationScanLoading": "正在扫描请求", "notificationTransactionApprovalActionButtonConfirm": "确认", "notificationTransactionApprovalActionButtonBack": "返回", "notificationTransactionApprovalEstimatedChanges": "预估变化", "notificationTransactionApprovalEstimatesBasedOnSimulations": "预估值基于交易模拟，并非一种保证", "notificationTransactionApprovalHideAdvancedDetails": "隐藏高级交易详情", "notificationTransactionApprovalNetworkFee": "网络费用", "notificationTransactionApprovalNetwork": "网络", "notificationTransactionApprovalEstimatedTime": "预计时间", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "找不到影响资产所有权的更改", "notificationTransactionApprovalSolanaAmountRequired": "Solana 网络处理交易所需的金额", "notificationTransactionApprovalUnableToSimulate": "无法模拟。确保您信任此网站，因为批准可能会导致资金损失。", "notificationTransactionApprovalUnableToFetchBalanceChanges": "无法获取余额变动", "notificationTransactionApprovalViewAdvancedDetails": "查看高级交易详情", "notificationTransactionApprovalKnownMalicious": "此交易是恶意交易。签署将导致资金损失。", "notificationTransactionApprovalSuspectedMalicious": "我们怀疑这笔交易是恶意交易。批准可能会导致资金损失。", "notificationTransactionApprovalNetworkFeeHighWarning": "由于网络拥堵，网络费用提高。", "notificationTransactionERC20ApprovalDescription": "确认后，此应用即可随时存取您的余额，限额如下。", "notificationTransactionERC20ApprovalContractAddress": "合约地址", "notificationTransactionERC20Unlimited": "无限", "notificationTransactionERC20ApprovalTitle": "批准 {{tokenSymbol}} 支出", "notificationTransactionERC20RevokeTitle": "撤销 {{tokenSymbol}} 支出", "notificationTransactionERC721RevokeTitle": "撤销 {{tokenSymbol}} 存取权限", "notificationTransactionERC20ApprovalAll": "您的全部 {{tokenSymbol}}", "notificationIncorrectModeTitle": "模式不正确", "notificationIncorrectModeInTestnetTitle": "您处于测试网模式", "notificationIncorrectModeNotInTestnetTitle": "您未处于测试网模式", "notificationIncorrectModeInTestnetDescription": "{{origin}} 正尝试使用 Mainnet，但您处于测试网模式", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} 正尝试使用测试网，但您未处于测试网模式", "notificationIncorrectModeInTestnetProceed": "要继续操作，请关闭测试网模式。", "notificationIncorrectModeNotInTestnetProceed": "要继续操作，请开启测试网模式。", "notificationIncorrectEIP712ChainId": "我们已阻止您为不适用于您当前连接的网络的消息签名", "notificationIncorrectEIP712ChainIdDescription": "消息请求的是 {{messageChainId}}，您已连接到 {{connectedChainId}}", "notificationUnsupportedNetwork": "网络不受支持", "notificationUnsupportedNetworkDescription": "此网站正试图使用 Phantom 目前不支持的网络。", "notificationUnsupportedNetworkDescriptionInterpolated": "要使用其他扩展继续，请关闭<1>设置 → 默认应用钱包</1>，并选择“始终询问”。然后，刷新页面并重新连接。", "notificationUnsupportedAccount": "账户不受支持", "notificationUnsupportedAccountDescription": "此网站正尝试使用 {{targetChainType}}，但您的 {{chainType}} 账户不支持。", "notificationUnsupportedAccountDescription2": "从兼容的种子短语或私钥切换到账户，然后重试。", "notificationInvalidTransaction": "交易无效", "notificationInvalidTransactionDescription": "从此应用接收的交易格式错误，不应提交。请联系此应用的开发者，向他们报告此问题。", "notificationCopyTransactionText": "复制交易", "notificationTransactionCopied": "交易已复制", "onboardingImportOptionsPageTitle": "导入钱包", "onboardingImportOptionsPageSubtitle": "使用您的密钥短语、私钥或硬件钱包导入现有钱包。", "onboardingImportPrivateKeyPageTitle": "导入私钥", "onboardingImportPrivateKeyPageSubtitle": "导入现有单链钱包", "onboardingCreatePassword": "创建密码", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "我同意<1>服务条款</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "确认密码", "onboardingCreatePasswordDescription": "您需要使用此密码解锁钱包.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "密钥恢复短语无效", "onboardingCreatePasswordPasswordPlaceholder": "密码", "onboardingCreatePasswordPasswordStrengthWeak": "弱", "onboardingCreatePasswordPasswordStrengthMedium": "中", "onboardingCreatePasswordPasswordStrengthStrong": "强", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "我已保存我的密钥恢复短语", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "助记词", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "此短语是恢复您的钱包的唯一方式。请勿与任何人分享！", "onboardingImportWallet": "导入钱包", "onboardingImportWalletImportExistingWallet": "使用您的 12 或 24 字密钥恢复短语导入现有钱包。", "onboardingImportWalletRestoreWallet": "恢复钱包", "onboardingImportWalletSecretRecoveryPhrase": "助记词", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "密钥恢复短语无效", "onboardingImportWalletIHaveWords": "我有一个 {{numWords}} 词恢复短语", "onboardingImportWalletIncorrectOrMisspelledWord": "单词 {{wordIndex}} 不正确或拼写错误", "onboardingImportWalletIncorrectOrMisspelledWords": "单词 {{wordIndexes}} 不正确或拼写错误", "onboardingImportWalletScrollDown": "向下滚动", "onboardingImportWalletScrollUp": "向上滚动", "onboardingSelectAccountsImportAccounts": "导入账户", "onboardingSelectAccountsImportAccountsDescription": "选择要导入的钱包账户.", "onboardingSelectAccountsImportSelectedAccounts": "导入所选账户", "onboardingSelectAccountsFindMoreAccounts": "查找更多账户", "onboardingSelectAccountsFindMoreNoneFound": "找不到账户", "onboardingSelectAccountsNoOfAccountsSelected": "已选择 {{numOfAccounts}} 个账户", "onboardingSelectAccountSelectAllText": "全选", "onboardingAdditionalPermissionsTitle": "将应用与 Phantom 结合使用", "onboardingAdditionalPermissionsSubtitle": "为了获得最流畅的应用体验，我们建议允许 Phantom 读取和更改所有网站上的数据。", "interstitialAdditionalPermissionsTitle": "将应用与 Phantom 结合使用", "interstitialAdditionalPermissionsSubtitle": "为了不间断地继续您使用应用的体验，我们建议允许 Phantom 读取和更改所有网站上的数据。", "recentActivityPrimaryText": "最近交易记录", "removeAccountActionButtonRemove": "移除", "removeAccountRemoveWallet": "移除账户", "removeAccountInterpolated": "移除 {{accountName}}", "removeAccountWarningLedger": "即使将此钱包从 Phantom 中移除，您也可以通过“连接硬件钱包”的方式重新添加。", "removeAccountWarningSeedVault": "即使将此钱包从 Phantom 中移除，您也可以通过“关联种子库钱包”的方式重新添加。", "removeAccountWarningPrivateKey": "一旦移除此钱包，Phantom 将无法为您恢复。请确保已备份您的私钥。", "removeAccountWarningSeed": "即使从 Phantom 中删除了这个钱包，您也可以使用本钱包或其他钱包中的助记符重新获取。", "removeAccountWarningReadOnly": "删除此账户不会影响您的钱包，因为它是一个仅监视钱包。", "removeSeedPrimaryText": "移除密钥短语 {{number}}", "removeSeedSecondaryText": "此操作将移除密钥短语 {{number}} 中的所有现有账户。请确保您已保存现有的密钥短语。", "resetSeedPrimaryText": "使用新密钥短语重置应用", "resetSeedSecondaryText": "该操作将删除所有的现有账户并使用新钱包进行替换。请确保您备份了现有的密钥短语和私钥。", "resetAppPrimaryText": "重置并擦除应用", "resetAppSecondaryText": "此操作将移除所有现有账户和数据。请确保您已备份密钥短语和私钥。", "richTransactionsDays": "天", "richTransactionsToday": "今天", "richTransactionsYesterday": "昨天", "richTransactionDetailAccount": "账户", "richTransactionDetailAppInteraction": "应用互动", "richTransactionDetailAt": "，", "richTransactionDetailBid": "出价", "richTransactionDetailBidDetails": "出价详细信息", "richTransactionDetailBought": "已购买", "richTransactionDetailBurned": "已焚毁", "richTransactionDetailCancelBid": "取消出价", "richTransactionDetailCompleted": "已完成", "richTransactionDetailConfirmed": "已确认", "richTransactionDetailDate": "日期", "richTransactionDetailFailed": "失败", "richTransactionDetailFrom": "从", "richTransactionDetailItem": "条目", "richTransactionDetailListed": "已上架", "richTransactionDetailListingDetails": "上架详细信息", "richTransactionDetailListingPrice": "上架价格", "richTransactionDetailMarketplace": "市场", "richTransactionDetailNetworkFee": "网络费用", "richTransactionDetailOriginalListingPrice": "原始上架价格", "richTransactionDetailPending": "待定", "richTransactionDetailPrice": "价格", "richTransactionDetailProvider": "提供商", "richTransactionDetailPurchaseDetails": "购买详细信息", "richTransactionDetailRebate": "折扣", "richTransactionDetailReceived": "已接收", "richTransactionDetailSaleDetails": "出售详细信息", "richTransactionDetailSent": "已发送", "richTransactionDetailSold": "已出售", "richTransactionDetailStaked": "已质押", "richTransactionDetailStatus": "状态", "richTransactionDetailSwap": "兑换", "richTransactionDetailSwapDetails": "兑换详细信息", "richTransactionDetailTo": "到", "richTransactionDetailTokenSwap": "代币兑换", "richTransactionDetailUnknownNFT": "未知 NFT", "richTransactionDetailUnlisted": "已下架", "richTransactionDetailUnstaked": "已取消质押", "richTransactionDetailValidator": "验证节点", "richTransactionDetailViewOnExplorer": "在 {{explorer}} 上查看", "richTransactionDetailWithdrawStake": "取回质押", "richTransactionDetailYouPaid": "您支付", "richTransactionDetailYouReceived": "您收到", "richTransactionDetailUnwrapDetails": "解封详细信息", "richTransactionDetailTokenUnwrap": "代币解封", "activityItemsRefreshFailed": "无法加载较新的交易。", "activityItemsPagingFailed": "无法加载较旧的交易。", "activityItemsTestnetNotAvailable": "目前无法获得测试网交易历史记录", "historyUnknownDappName": "未知", "historyStatusSucceeded": "成功", "historyNetwork": "网络", "historyAttemptedAmount": "尝试的金额", "historyAmount": "金额", "sendAddressBookButtonLabel": "地址簿", "addressBookSelectAddressBook": "地址簿", "sendAddressBookNoAddressesSaved": "没有保存的地址", "sendAddressBookRecentlyUsed": "最近使用的地址", "addressBookSelectRecentlyUsed": "最近使用的地址", "sendConfirmationLabel": "标签", "sendConfirmationMessage": "消息", "sendConfirmationNetworkFee": "网络费用", "sendConfirmationPrimaryText": "确认发送", "sendWarning_INSUFFICIENT_FUNDS": "资金不足，如果提交此交易很可能会失败。", "sendFungibleSummaryNetwork": "网络", "sendFungibleSummaryNetworkFee": "网络费用", "sendFungibleSummaryEstimatedTime": "预估时间", "sendFungiblePendingEstimatedTime": "估计时间", "sendFungibleSummaryEstimatedTimeDescription": "以太坊交易速度因多种因素而异。您可以通过点击“网络费用”来加快速度。", "sendSummaryBitcoinPendingTxTitle": "无法提交转账", "sendSummaryBitcoinPendingTxDescription": "一次只能有一个待处理的 BTC 转账。请等到该转账完成后再提交新的转账。", "sendFungibleSatProtectionTitle": "使用聪保护发送", "sendFungibleSatProtectionExplainer": "Phantom 将确保您的 Ordinals 和 BRC20 代币不会用于交易费用或比特币转账。", "sendFungibleTransferFee": "代币转账费", "sendFungibleTransferFeeToolTip": "此代币的创建者会对每次转账收取费用。这不是由 Phantom 收取或征收的费用。", "sendFungibleInterestBearingPercent": "当前利率", "sendFungibleNonTransferable": "不可转让", "sendFungibleNonTransferableToolTip": "此代币无法转移到其他账户。", "sendFungibleNonTransferableYes": "是", "sendStatusErrorMessageInterpolated": "尝试将代币发送到 <1>{{uiRecipient}}</1> 时出错", "sendStatusErrorMessageInsufficientBalance": "您没有足够的余额来完成交易。", "sendStatusErrorTitle": "无法发送", "sendStatusLoadingTitle": "正在发送…", "sendStatusSuccessMessageInterpolated": "您的代币已成功发送至 <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "已发送！", "sendStatusConfirmedSuccessTitle": "已发送！", "sendStatusSubmittedSuccessTitle": "交易已提交", "sendStatusEstimatedTransactionTime": "预计交易时间：{{time}}", "sendStatusViewTransaction": "查看交易", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> 至 <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> 已成功发送至 <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> 已成功发送至 <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> 未能发送至 <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "错误代码 {{code}}", "sendFormErrorInsufficientBalance": "余额不足", "sendFormErrorEmptyAmount": "“金额”为必填项", "sendFormInvalidAddress": "无效的 {{assetName}} 地址", "sendFormInvalidUsernameOrAddress": "用户名或地址无效", "sendFormErrorInvalidSolanaAddress": "无效的 Solana 地址", "sendFormErrorInvalidTwitterHandle": "此 Twitter 账号未注册", "sendFormErrorInvalidDomain": "此域未注册", "sendFormErrorInvalidUsername": "此用户名未注册", "sendFormErrorMinRequiredInterpolated": "至少需要 {{minAmount}} {{tokenName}}", "sendRecipientTextareaPlaceholder": "接收方的 SOL 地址", "sendRecipientTextAreaPlaceholder2": "接收方的 {{symbol}} 地址", "sendMemoOptional": "备忘录（可选）", "sendMemo": "备忘录", "sendOptional": "可选", "settings": "设置", "settingsDapps": "dApp", "settingsSelectedAccount": "已选账户", "settingsAddressBookNoLabel": "无标签", "settingsAddressBookPrimary": "地址簿", "settingsAddressBookRecentlyUsed": "最近使用的地址", "settingsAddressBookSecondary": "管理常用地址", "settingsAutoLockTimerPrimary": "自动锁定时间", "settingsAutoLockTimerSecondary": "更改钱包自动锁定的时间", "settingsChangeLanguagePrimary": "选择语言", "settingsChangeLanguageSecondary": "更改显示语言", "settingsChangeNetworkPrimary": "更改网络", "settingsChangeNetworkSecondary": "配置网络设置", "settingsChangePasswordPrimary": "更改密码", "settingsChangePasswordSecondary": "更改屏幕锁定密码", "settingsCompleteBetaSurvey": "完成测试版调查", "settingsDisplayLanguage": "显示语言", "settingsErrorCannotExportLedgerPrivateKey": "无法导出 Ledger 私钥", "settingsErrorCannotRemoveAllWallets": "无法移除所有账户", "settingsExportPrivateKey": "显示私钥", "settingsNetworkMainnetBeta": "主网 Beta", "settingsNetworkTestnet": "测试网", "settingsNetworkDevnet": "开发者网络", "settingsNetworkLocalhost": "本地主机", "settingsNetworkPhantomRPC": "Phantom RPC 网络", "settingsTestNetworks": "测试网络", "settingsUseCustomNetworks": "使用自定义网络", "settingsTestnetMode": "测试网模式", "settingsTestnetModeDescription": "适用于余额和应用连接。", "settingsWebViewDebugging": "Web 视图调试", "settingsWebViewDebuggingDescription": "允许您检查和调试应用内浏览器 Web 视图。", "settingsTestNetworksInfo": "切换到任何测试网网络仅用于测试目的。请注意，测试网网络上的代币不具有任何货币价值。", "settingsEmojis": "表情符号", "settingsNoAddresses": "地址为空", "settingsAddressBookEmptyHeading": "您的地址簿为空", "settingsAddressBookEmptyText": "点击“+”或“添加地址”按钮添加您喜欢的地址", "settingsEditWallet": "编辑账户", "settingsNoTrustedApps": "受信任的应用为空", "settingsNoConnections": "尚无关联。", "settingsRemoveWallet": "移除账户", "settingsResetApp": "重置应用", "settingsBlocked": "已屏蔽", "settingsBlockedAccounts": "被屏蔽的账户", "settingsNoBlockedAccounts": "没有被屏蔽的账户。", "settingsRemoveSecretPhrase": "移除密钥短语", "settingsResetAppWithSecretPhrase": "使用密钥短语重置应用", "settingsResetSecretRecoveryPhrase": "重置助记词", "settingsShowSecretRecoveryPhrase": "显示密钥恢复短语", "settingsShowSecretRecoveryPhraseSecondary": "显示恢复短语", "settingsShowSecretRecoveryPhraseTertiary": "显示密钥短语", "settingsTrustedAppsAutoConfirmActiveUntil": "{{formattedTimestamp}} 前", "settingsTrustedAppsAutoConfirm": "自动确认", "settingsTrustedAppsDisclaimer": "仅在信任的网站上启用自动确认", "settingsTrustedAppsLastUsed": "{{formattedTimestamp}}前使用", "settingsTrustedAppsPrimary": "关联的应用", "settingsTrustedApps": "受信任的应用", "settingsTrustedAppsRevoke": "撤销", "settingsTrustedAppsRevokeToast": "{{trustedApp}} 已断开连接", "settingsTrustedAppsSecondary": "配置受信任的应用程序", "settingsTrustedAppsToday": "今天", "settingsTrustedAppsYesterday": "昨天", "settingsTrustedAppsLastWeek": "上周", "settingsTrustedAppsBeforeYesterday": "更早", "settingsTrustedAppsDisconnectAll": "断开连接所有应用", "settingsTrustedAppsDisconnectAllToast": "所有应用已断开连接", "settingsTrustedAppsEndAutoConfirmForAll": "结束所有应用的自动确认", "settingsTrustedAppsEndAutoConfirmForAllToast": "所有自动确认会话已结束", "settingsSecurityPrimary": "安全性与隐私", "settingsSecuritySecondary": "更新您的安全性设置", "settingsActiveNetworks": "有效网络", "settingsActiveNetworksAll": "所有", "settingsActiveNetworksSolana": "仅 Solana", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana 为默认网络，始终保持开启状态。", "settingsDeveloperPrimary": "开发者设置", "settingsAdvanced": "高级设置", "settingsTransactions": "交易设置", "settingsAutoConfirm": "自动确认设置", "settingsSecurityAnalyticsPrimary": "共享匿名分析", "settingsSecurityAnalyticsSecondary": "启用以帮助我们改进", "settingsSecurityAnalyticsHelper": "Phantom 不会将您的个人信息用于分析目的", "settingsSuspiciousCollectiblesPrimary": "隐藏可疑收藏品", "settingsSuspiciousCollectiblesSecondary": "切换以隐藏被举报的收藏品", "settingsPreferredBitcoinAddress": "首选比特币地址", "settingsEnabledAddressesUpdated": "可见地址已更新！", "settingsEnabledAddresses": "启用的地址", "settingsBitcoinPaymentAddressForApps": "应用的付款地址", "settingsBitcoinOrdinalsAddressForApps": "应用的 Ordinals 地址", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "当上述两种地址类型都启用时，对于像 Magic Eden 这样的某些应用，将使用您的原生隔离见证地址来为购买提供资金。购买的资产将接收到您的 Taproot 地址中。", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Phantom 中用于确保兼容性的默认比特币地址。", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "（默认）", "settingsPreferredBitcoinAddressTaprootExplainer": "最新的地址类型，通常具有更低廉的交易费用。", "settingsPreferredExplorers": "首选浏览器", "settingsPreferredExplorersSecondary": "更改为您的首选区块链浏览器", "settingsCustomGasControls": "自定义 Gas 控制", "settingsSupportDesk": "支持中心", "settingsSubmitATicket": "提交工单", "settingsAttachApplicationLogs": "附加应用程序日志", "settingsDownloadApplicationLogs": "下载应用日志", "settingsDownloadApplicationLogsShort": "下载日志", "settingsDownloadApplicationLogsHelper": "包含本地数据、崩溃报告和公共钱包地址，以帮助解决 Phantom 支持问题", "settingsDownloadApplicationLogsWarning": "不包含种子短语或私钥等敏感数据。", "settingsWallet": "钱包", "settingsPreferences": "偏好设置", "settingsSecurity": "安全性", "settingsDeveloper": "开发者", "settingsSupport": "支持", "settingsWalletShortcutsPrimary": "显示钱包快捷键", "settingsAppIcon": "应用图标", "settingsAppIconDefault": "默认", "settingsAppIconLight": "浅色", "settingsAppIconDark": "深色", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "账户", "settingsSearchResultSelected": "已选择", "settingsSearchResultExport": "导出", "settingsSearchResultSeed": "种子", "settingsSearchResultTrusted": "受信任", "settingsSearchResultTestnet": "测试网", "settingsSearchResultState": "状态", "settingsSearchResultLogs": "日志", "settingsSearchResultBiometric": "生物识别", "settingsSearchResultTouch": "触控", "settingsSearchResultFace": "面容", "settingsSearchResultShortcuts": "快捷方式", "settingsAllSitesPermissionsTitle": "在所有网站上访问 Phantom", "settingsAllSitesPermissionsSubtitle": "允许您无需点击扩展程序即可无缝地将应用与 Phantom 结合使用", "settingsAllSitesPermissionsDisabled": "您的浏览器不支持更改此设置", "settingsSolanaCopyTransaction": "启用交易复制", "settingsSolanaCopyTransactionDetails": "将序列化交易数据复制到剪贴板", "settingsAutoConfirmHeader": "自动确认", "refreshWebpageToApplyChanges": "刷新网页以应用更改", "settingsExperimentalTitle": "实验性功能", "settingsExprimentalSolanaActionsSubtitle": "在 X.com 上检测到相关链接时，自动展开 Solana 操作按钮。", "stakeAccountCardActiveStake": "激活的质押", "stakeAccountCardBalance": "余额", "stakeAccountCardRentReserve": "租金储备金", "stakeAccountCardRewards": "上一个奖励", "stakeAccountCardRewardsTooltip": "这是您最近赚取的质押奖励。您每 3 天会获得一次奖励。", "stakeAccountCardStakeAccount": "地址", "stakeAccountCardLockup": "锁定截止时间", "stakeRewardsHistoryTitle": "奖励历史", "stakeRewardsActivityItemTitle": "奖励", "stakeRewardsHistoryEmptyList": "无奖励", "stakeRewardsTime_zero": "今天", "stakeRewardsTime_one": "昨天", "stakeRewardsTime_other": "{{count}} 天前", "stakeRewardsItemsPagingFailed": "无法加载较旧的奖励。", "stakeAccountCreateAndDelegateErrorStaking": "质押给该验证节点出错。请重试。", "stakeAccountCreateAndDelegateSolStaked": "SOL 代币质押成功！", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "一旦质押账户生效，您的 SOL 将在接下来的几天内开始赚取奖励<1></1>。", "stakeAccountCreateAndDelegateStakingFailed": "质押失败", "stakeAccountCreateAndDelegateStakingSol": "SOL 代币质押中…", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "我们正在创建一个质押账户，然后会将您的 SOL 委托给", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "我们正在创建一个质押账户，然后会将您的 SOL 委托给 {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "查看交易", "stakeAccountDeactivateStakeSolUnstaked": "SOL 取消质押成功！", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "一旦质押账户变为闲置状态，您即可在接下来的几天内取回您的质押<1></1>。", "stakeAccountDeactivateStakeSolUnstakedDescription": "一旦质押账户变为闲置状态，您即可在接下来的几天内取回您的质押。", "stakeAccountDeactivateStakeUnstakingFailed": "取消质押失败", "stakeAccountDeactivateStakeUnstakingFailedDescription": "从验证节点取消质押出现问题。请重试。", "stakeAccountDeactivateStakeUnstakingSol": "SOL 代币取消质押中…", "stakeAccountDeactivateStakeUnstakingSolDescription": "正在启动取消质押 SOL 的程序。", "stakeAccountDeactivateStakeViewTransaction": "查看交易", "stakeAccountDelegateStakeSolStaked": "SOL 代币质押成功！", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "一旦质押账户生效，您的 SOL 将在接下来的几天内开始赚取奖励<1></1>。", "stakeAccountDelegateStakeStakingFailed": "质押失败", "stakeAccountDelegateStakeStakingFailedDescription": "质押给该验证节点出错。请重试。", "stakeAccountDelegateStakeStakingSol": "SOL 代币质押中…", "stakeAccountDelegateStakeStakingSolDescription": "我们正在委托您的 SOL。", "stakeAccountDelegateStakeViewTransaction": "查看交易", "stakeAccountListActivationActivating": "激活中", "stakeAccountListActivationActive": "已激活", "stakeAccountListActivationInactive": "未激活", "stakeAccountListActivationDeactivating": "正在停用", "stakeAccountListErrorFetching": "我们无法获取质押账户。请稍后再试。", "stakeAccountListNoStakingAccounts": "无质押账号", "stakeAccountListReload": "重新加载", "stakeAccountListViewPrimaryText": "您的质押", "stakeAccountListViewStakeSOL": "质押 SOL 代币", "stakeAccountListItemStakeFee": "{{fee}} 费用", "stakeAccountViewActionButtonRestake": "重新质押", "stakeAccountViewActionButtonUnstake": "取消质押", "stakeAccountViewError": "错误", "stakeAccountViewPrimaryText": "您的质押", "stakeAccountViewRestake": "重新质押", "stakeAccountViewSOLCurrentlyStakedInterpolated": "您的 SOL 目前已质押给验证节点。您需要取消质押才能<1></1>取回这些资金。<3>了解详情</3>", "stakeAccountViewStakeInactive": {"part1": "该质押账户处于非激活状态。您可以取回质押或者委托给一个验证节点。", "part2": "了解详情"}, "stakeAccountViewStakeNotFound": "无法找到该质押账户。", "stakeAccountViewViewOnExplorer": "在 {{explorer}} 上查看", "stakeAccountViewWithdrawStake": "取回质押", "stakeAccountViewWithdrawUnstakedSOL": "取回未质押的 SOL 代币", "stakeAccountInsufficientFunds": "没有足够的可用 SOL 进行取消质押或取回。", "stakeAccountWithdrawStakeSolWithdrawn": "成功取回 SOL！", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL 代币已成功取回。", "part2": "该质押账户将在几分钟内自动删除。"}, "stakeAccountWithdrawStakeViewTransaction": "查看交易", "stakeAccountWithdrawStakeWithdrawalFailed": "取回失败", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "从该质押账户取回质押出现问题。请重试。", "stakeAccountWithdrawStakeWithdrawingSol": "SOL 代币取回中…", "stakeAccountWithdrawStakeWithdrawingSolDescription": "正在从该质押账户取回您的 SOL。", "startEarningSolAccount": "账户", "startEarningSolAccounts": "账户", "startEarningSolErrorClosePhantom": "点按此处并重试", "startEarningSolErrorTroubleLoading": "无法加载质押", "startEarningSolLoading": "正在加载…", "startEarningSolPrimaryText": "开始赚取 SOL 代币收益", "startEarningSolSearching": "正在搜索质押账户", "startEarningSolStakeTokens": "质押代币，赚取奖励", "startEarningSolYourStake": "您的质押", "unwrapFungibleTitle": "兑换为 {{tokenSymbol}}", "unwrapFungibleDescription": "将 {{fromToken}} 提取为 {{toToken}}", "unwrapFungibleConfirmSwap": "确认兑换", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "预估费用", "swapFeesFees": "费用", "swapFeesPhantomFee": "Phantom 费用", "swapFeesPhantomFeeDisclaimer": "我们始终从顶级流动性提供商那里寻找最优惠的价格。{{feePercentage}} 的费用会自动计入此报价。", "swapFeesRate": "价格", "swapFeesRateDisclaimer": "Jupiter Aggregator 在多个去中心化交易所中找到的最低价格。", "swapFeesRateDisclaimerMultichain": "在多个去中心化交易所中找到的最低价格。", "swapFeesPriceImpact": "价格冲击", "swapFeesHighPriceImpact": "高价影响", "swapFeesPriceImpactDisclaimer": "市场价格与基于您的交易规模的估计价格之间的差异。", "swapFeesSlippage": "滑点", "swapFeesHighSlippage": "高滑点容差", "swapFeesHighSlippageDisclaimer": "如果价格变动超过 {{slippage}}%，您的交易将会失败。", "swapTransferFee": "转账费用", "swapTransferFeeDisclaimer": "交易 ${{symbol}} 将产生 {{feePercent}}% 的转账费用，这笔费用是代币创建者设置的，而不是 Phantom 设置的。", "swapTransferFeeDisclaimerMany": "交易所选代币将产生 {{feePercent}}% 的费用，这笔费用是代币创建者设置的，而不是 Phantom 设置的。", "swapFeesSlippageDisclaimer": "您的交易的价格可与提供的报价偏离的金额。", "swapFeesProvider": "提供商", "swapFeesProviderDisclaimer": "用于完成您的交易的去中心化交易所。", "swapEstimatedTime": "预计时间", "swapEstimatedTimeShort": "预计时间", "swapEstimatedTimeDisclaimer": "根据多种影响交易速度的因素，跨链的预计完成时间会有所不同。", "swapSettingsButtonCommand": "打开兑换设置", "swapQuestionRetry": "是否重试？", "swapUnverifiedTokens": "未经验证的代币", "swapSectionTitleTokens": "{{section}} 代币", "swapFlowYouPay": "支付", "swapFlowYouReceive": "换回", "swapFlowActionButtonText": "审查订单", "swapAssetCardTokenNetwork": "{{network}} 上的 {{symbol}}", "swapAssetCardMaxButton": "最大", "swapAssetCardSelectTokenAndNetwork": "选择代币和网络", "swapAssetCardBuyTitle": "收到", "swapAssetCardSellTitle": "支付", "swapAssetWarningUnverified": "此代币未经验证。仅与您信任的代币进行交互。", "swapAssetWarningPermanentDelegate": "委托可以永久销毁或转移这些代币。", "swapSlippageSettingsTitle": "滑点设置", "swapSlippageSettingsSubtitle": "如果价格变动超过该滑点，您的交易将会失败。值过高会导致逆差。", "swapSlippageSettingsCustom": "自定义", "swapSlippageSettingsHighSlippageWarning": "您的交易可能被抢先执行，并导致不利交易。", "swapSlippageSettingsCustomMinError": "请输入大于 {{minSlippage}}% 的值。", "swapSlippageSettingsCustomMaxError": "请输入小于 {{maxSlippage}}% 的值。", "swapSlippageSettingsCustomInvalidValue": "请输入一个有效的值。", "swapSlippageSettingsAutoSubtitle": "Phantom 将查找最低滑点以实现成功兑换。", "swapSlippageSettingsAuto": "自动", "swapSlippageSettingsFixed": "固定", "swapSlippageOptInTitle": "自动滑点", "swapSlippageOptInSubtitle": "Phantom 将查找最低滑点以实现成功兑换。您可以随时在“Swapper → 滑点设置”中更改此设置。", "swapSlippageOptInEnableOption": "启用自动滑点", "swapSlippageOptInRejectOption": "继续使用固定滑点", "swapQuoteFeeDisclaimer": "报价包括 {{feePercentage}} 的 Phantom 费用", "swapQuoteMissingContext": "缺少掉期报价上下文", "swapQuoteErrorNoQuotes": "尝试无报价掉期", "swapQuoteSolanaNetwork": "Solana 网络", "swapQuoteNetwork": "网络", "swapQuoteOneTimeSerumAccount": "一次性 Serum 账户", "swapQuoteOneTimeTokenAccount": "一次性代币账户", "swapQuoteBridgeFee": "跨链兑换费", "swapQuoteDestinationNetwork": "目标网络", "swapQuoteLiquidityProvider": "流动性提供商", "swapReviewFlowActionButtonPrimary": "兑换", "swapReviewFlowPrimaryText": "审查订单", "swapReviewFlowYouPay": "支付", "swapReviewFlowYouReceive": "换回", "swapReviewInsufficientBalance": "资金不足", "ugcSwapWarningTitle": "警告", "ugcSwapWarningBody1": "此代币在代币启动平台 {{programName}} 上交易。", "ugcSwapWarningBody2": "这些代币的价值可能会剧烈波动，导致巨大的财务收益或损失。请自行承担交易风险。", "ugcSwapWarningConfirm": "我理解", "bondingCurveProgressLabel": "联合曲线进度", "bondingCurveInfoTitle": "联合曲线", "bondingCurveInfoDescription": "在联合曲线模型中，代币价格由曲线的形状决定，随着购买的代币数量增加而上升，随着代币出售而下降。当代币售出时，所有流动性都会被存入 Raydium 并销毁。", "ugcFungibleWarningBanner": "此代币在 {{programName}} 上交易", "ugcCreatedRowLabel": "创建日期", "ugcStatusRowLabel": "状态", "ugcStatusRowValue": "分阶", "swapTxConfirmationReceived": "已接收!", "swapTxConfirmationSwapFailed": "兑换失败", "swapTxConfirmationSwapFailedStaleQuota": "该报价不再有效。请重试。", "swapTxConfirmationSwapFailedSlippageLimit": "您的滑点设置对于此兑换来说过低。请在“兑换”屏幕顶部增加您的滑点并重试。", "swapTxConfirmationSwapFailedInsufficientBalance": "我们无法完成该请求。您没有足够的余额来完成交易。", "swapTxConfirmationSwapFailedEmptyRoute": "此代币对的流动性已发生变化。我们无法找到合适的报价。请重试或调整代币数量。", "swapTxConfirmationSwapFailedAcountFrozen": "此代币已被其创建者冻结。您无法发送或兑换此代币。", "swapTxConfirmationSwapFailedTryAgain": "兑换失败，请重试。", "swapTxConfirmationSwapFailedUnknownError": "我们无法完成兑换。您的资金均未受到影响。请重试。", "swapTxConfirmationSwapFailedSimulationTimeout": "我们无法模拟兑换。您的资金均未受到影响。请重试。", "swapTxConfirmationSwapFailedSimulationUnknownError": "我们无法完成兑换。您的资金均未受到影响。请重试。", "swapTxConfirmationSwapFailedInsufficientGas": "您的账户资金不足，无法完成该交易。请向您的账户中添加更多资金并重试。", "swapTxConfirmationSwapFailedLedgerReject": "用户在硬件设备上拒绝了兑换。", "swapTxConfirmationSwapFailedLedgerConnectionError": "兑换因设备连接错误而被拒绝。请重试。", "swapTxConfirmationSwapFailedLedgerSignError": "兑换因设备登录错误而被拒绝。请重试。", "swapTxConfirmationSwapFailedLedgerError": "兑换因设备错误而被拒绝。请重试。", "swapTxConfirmationSwappingTokens": "兑换代币中…", "swapTxConfirmationTokens": "代币", "swapTxConfirmationTokensDeposited": "兑换成功！代币已直接存入您的钱包", "swapTxConfirmationTokensDepositedTitle": "完成！", "swapTxConfirmationTokensDepositedBody": "代币已直接存入您的钱包", "swapTxConfirmationTokensWillBeDeposited": "在交易完成后将直接存入您的钱包", "swapTxConfirmationViewTransaction": "查看交易", "swapTxBridgeSubmitting": "正在提交交易", "swapTxBridgeSubmittingDescription": "正在将 {{sellNetwork}} 上的 {{sellAmount}} 兑换为 {{buyNetwork}} 上的 {{buyAmount}}", "swapTxBridgeFailed": "交易提交失败", "swapTxBridgeFailedDescription": "无法完成此请求。", "swapTxBridgeSubmitted": "交易已提交", "swapTxBridgeSubmittedDescription": "预计交易时间：{{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "您可以安全关闭此窗口。", "swapperSwitchTokens": "切换代币", "swapperMax": "最大", "swapperTooltipNetwork": "网络", "swapperTooltipPrice": "价格", "swapperTooltipAddress": "合约", "swapperTrendingSortBy": "排序依据", "swapperTrendingTimeFrame": "时间范围", "swapperTrendingNetwork": "网络", "swapperTrendingRank": "排名", "swapperTrendingTokens": "流行代币", "swapperTrendingVolume": "交易量", "swapperTrendingPrice": "价格", "swapperTrendingPriceChange": "价格变动", "swapperTrendingMarketCap": "市值", "swapperTrendingTimeFrame1h": "1 小时", "swapperTrendingTimeFrame24h": "24 小时", "swapperTrendingTimeFrame7d": "7 天", "swapperTrendingTimeFrame30d": "30 天", "swapperTrendingNoTokensFound": "找不到代币。", "switchToggle": "切换", "termsOfServiceActionButtonAgree": "我同意", "termsOfServiceDisclaimerFeesDisabledInterpolated": "点击“<1>我同意</1>”，即表示您接受与 Phantom 进行代币兑换的<3>条款与条件</3>。", "termsOfServiceDiscliamerFeesEnabledInterpolated": "我们修订了《服务条款》。点击“<1>我同意</1>”，即表示您同意新的<3>服务条款</3>。<5></5><6></6>我们的新《服务条款》包括某些产品的新<8>费用结构</8>。", "termsOfServicePrimaryText": "服务条款", "tokenRowUnknownToken": "未知代币", "transactionsAppInteraction": "应用互动", "transactionsFailedAppInteraction": "应用互动失败", "transactionsBidOnInterpolated": "对 {{name}} 出价", "transactionsBidFailed": "出价失败", "transactionsBoughtInterpolated": "已购买 {{name}}", "transactionsBoughtCollectible": "已购买的收藏品", "transactionBridgeInitiated": "跨链已发起", "transactionBridgeInitiatedFailed": "跨链发起失败", "transactionBridgeStatusLink": "在 LI.FI 上检查状态", "transactionsBuyFailed": "购买失败", "transactionsBurnedSpam": "已焚毁的垃圾", "transactionsBurned": "已焚毁", "transactionsUnwrapped": "已解封", "transactionsUnwrappedFailed": "解封失败", "transactionsCancelBidOnInterpolated": "已取消对 {{name}} 出价", "transactionsCancelBidOnFailed": "未能取消出价", "transactionsError": "错误", "transactionsFailed": "失败", "transactionsSwapped": "已兑换", "transactionsFailedSwap": "兑换失败", "transactionsFailedBurn": "焚毁失败", "transactionsFrom": "从", "transactionsListedInterpolated": "已上架 {{name}}", "transactionsListedFailed": "未能上架", "transactionsNoActivity": "无记录", "transactionsReceived": "已接收", "transactionsReceivedInterpolated": "已收到 {{amount}} SOL", "transactionsSending": "正在发送…", "transactionsPendingCreateListingInterpolated": "创建 {{name}}", "transactionsPendingEditListingInterpolated": "编辑 {{name}}", "transactionsPendingSolanaPayTransaction": "正在确认 Solana Pay 交易", "transactionsPendingRemoveListingInterpolated": "下架 {{name}}", "transactionsPendingBurningInterpolated": "焚毁 {{name}}", "transactionsPendingSending": "正在发送", "transactionsPendingSwapping": "兑换", "transactionsPendingBridging": "跨链", "transactionsPendingApproving": "等待审批", "transactionsPendingCreatingAndDelegatingStake": "正在创建和委托质押", "transactionsPendingDeactivatingStake": "正在停用质押", "transactionsPendingDelegatingStake": "正在委托质押", "transactionsPendingWithdrawingStake": "正在取回质押", "transactionsPendingAppInteraction": "待处理的应用互动", "transactionsPendingBitcoinTransaction": "待处理 BTC 交易", "transactionsSent": "已发送", "transactionsSendFailed": "发送失败", "transactionsSwapOn": "在 {{dappName}} 上兑换", "transactionsSentInterpolated": "已发送 {{amount}} SOL", "transactionsSoldInterpolated": "已出售 {{name}}", "transactionsSoldCollectible": "已出售的收藏品", "transactionsSoldFailed": "出售失败", "transactionsStaked": "已质押", "transactionsStakedFailed": "质押失败", "transactionsSuccess": "成功", "transactionsTo": "到", "transactionsTokenSwap": "代币兑换", "transactionsUnknownAmount": "未知", "transactionsUnlistedInterpolated": "已下架 {{name}}", "transactionsUnstaked": "已取消质押", "transactionsUnlistedFailed": "未能下架", "transactionsDeactivateStake": "停用的质押", "transactionsDeactivateStakeFailed": "未能停用质押", "transactionsWaitingForConfirmation": "等待交易确认", "transactionsWithdrawStake": "取回质押", "transactionsWithdrawStakeFailed": "取消质押失败", "transactionCancelled": "已取消", "transactionCancelledFailed": "未能取消", "transactionApproveToken": "已批准的 {{tokenSymbol}}", "transactionApproveTokenFailed": "未能批准 {{tokenSymbol}}", "transactionApprovalFailed": "审批失败", "transactionRevokeApproveToken": "已撤销 {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "未能撤销 {{tokenSymbol}}", "transactionRevokeFailed": "撤销失败", "transactionApproveDetailsTitle": "审批详细信息", "transactionCancelOrder": "取消订单", "transactionCancelOrderFailed": "取消订单失败", "transactionApproveAppLabel": "应用", "transactionApproveAmountLabel": "金额", "transactionApproveTokenLabel": "代币", "transactionApproveCollectionLabel": "收藏", "transactionApproveAllItems": "批准所有条目", "transactionSpendUpTo": "最多花费", "transactionCancel": "取消交易", "transactionPrioritizeCancel": "优先处理取消", "transactionSpeedUp": "加速交易", "transactionCancelHelperText": "原始交易可能在取消前完成。", "transactionSpeedUplHelperText": "此操作将根据网络状况最大限度加快交易速度。", "transactionCancelHelperMobile": "尝试取消此交易最多将花费 <1>{{amount}}</1>。原始交易可能在取消前完成。", "transactionCancelHelperMobileWithEstimate": "尝试取消此交易将花费<1>最多 {{amount}}</1>。取消操作应在大约 {{timeEstimate}}后完成。原始交易可能在取消前完成。", "transactionSpeedUpHelperMobile": "最大限度加快此交易的速度将花费<1>最多 {{amount}}</1>。", "transactionSpeedUpHelperMobileWithEstimate": "最大限度加快此交易的速度将花费<1>最多 {{amount}}</1>。取消操作应在大约 {{timeEstimate}}后完成。", "transactionEstimatedTime": "预计时间", "transactionCancelingSend": "正在取消发送", "transactionPrioritizingCancel": "正在优先处理取消", "transactionCanceling": "正在取消", "transactionReplaceError": "出错了。未向您的账户收取任何费用。您可以重试。", "transactionNotEnoughNative": "{{nativeTokenSymbol}} 不足", "transactionGasLimitError": "无法预估 gas 限额", "transactionGasEstimationError": "无法预估 gas", "pendingTransactionCancel": "取消", "pendingTransactionSpeedUp": "加速", "pendingTransactionStatus": "状态", "pendingTransactionPending": "待处理", "pendingTransactionPendingInteraction": "待处理互动", "pendingTransactionCancelling": "正在取消", "pendingTransactionDate": "日期", "pendingTransactionNetworkFee": "网络费用", "pendingTransactionEstimatedTime": "预计时间", "pendingTransactionEstimatedTimeHM": "{{hours}} 小时 {{minutes}} 分钟", "pendingTransactionEstimatedTimeMS": "{{minutes}} 分钟 {{seconds}} 秒", "pendingTransactionEstimatedTimeS": "{{seconds}} 秒", "pendingTransactionsSendingTitle": "正在发送 {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "未知", "pendingTransactionUnknownApp": "未知应用", "permanentDelegateTitle": "已委托", "permanentDelegateValue": "永久", "permanentDelegateTooltipTitle": "永久委托", "permanentDelegateTooltipValue": "永久委托将允许其他账户代表您管理代币，包括焚毁或转移。", "unlockActionButtonUnlock": "解锁", "unlockEnterPassword": "输入您的密码", "unlockErrorIncorrectPassword": "密码不正确", "unlockErrorSomethingWentWrong": "出错了，请稍后再试", "unlockForgotPassword": "忘记了密码", "unlockPassword": "密码", "forgotPasswordText": "您可以通过输入钱包的 12-24 字恢复短语来重置密码。Phantom 无法为您恢复密码。", "appInfo": "应用信息", "lastUsed": "上次使用", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "不适用于硬件账户。", "trustedAppAutoConfirmDisclaimer1": "在处于有效状态时，Phantom 将确认来自此应用的所有请求，而无需通知您或要求确认。", "trustedAppAutoConfirmDisclaimer2": "启用后可能会使您的资金面临欺诈风险。请仅对您信任的应用使用此功能。", "validationUtilsPasswordIsRequired": "请先输入密码", "validationUtilsPasswordLength": "密码至少需要 8 个字符", "validationUtilsPasswordsDontMatch": "密码不匹配", "validationUtilsPasswordCantBeSame": "您不能使用旧密码", "validatorCardEstimatedApy": "预计 APY", "validatorCardCommission": "佣金", "validatorCardTotalStake": "全部的质押", "validatorCardNumberOfDelegators": "委托者编号", "validatorListChooseAValidator": "选择一个验证节点", "validatorListErrorFetching": "我们无法获取验证节点。请稍后再试。", "validatorListNoResults": "无结果", "validatorListReload": "重新加载", "validatorInfoTooltip": "验证节点", "validatorInfoTitle": "验证节点", "validatorInfoDescription": "通过在验证节点上质押 SOL，您可以为 Solana 网络的性能和安全做出贡献，同时赚取 SOL 作为回报。", "validatorApyInfoTooltip": "预计 APY", "validatorApyInfoTitle": "预计 APY", "validatorApyInfoDescription": "这是您在验证节点上质押 SOL 所获得的回报率。", "validatorViewActionButtonStake": "质押", "validatorViewErrorFetching": "无法获取验证节点。", "validatorViewInsufficientBalance": "余额不足", "validatorViewMax": "最大", "validatorViewPrimaryText": "开始质押", "validatorViewDescriptionInterpolated": "选择要将多少 SOL <1></1>质押给此验证节点。<3>了解详情</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "需要质押 {{amount}} SOL", "validatorViewValidator": "验证节点", "walletMenuItemsAddConnectWallet": "添加/连接钱包", "walletMenuItemsBridgeAssets": "桥接资产", "walletMenuItemsHelpAndSupport": "帮助与支持", "walletMenuItemsLockWallet": "锁定钱包", "walletMenuItemsResetSecretPhrase": "重置密钥短语", "walletMenuItemsShowMoreAccounts": "显示其他 {{count}} 个…", "walletMenuItemsHideAccounts": "隐藏账户", "toggleMultiChainHeader": "多链", "disableMultiChainHeader": "仅 Solana 模式", "disableMultiChainDetail1Header": "全力投入 Solana", "disableMultiChainDetail1SecondaryText": "管理您的 Solana 账户、代币和收藏品，而无需查看其他链。", "disableMultiChainDetail2Header": "随时返回多链", "disableMultiChainDetail2SecondaryText": "当您重新启用多链时，将保留您现有的以太坊和 Polygon 余额。", "disableMultiChainButton": "启用仅 Solana 模式", "disabledMultiChainHeader": "仅 Solana 模式已禁用", "disabledMultiChainText": "您可以随时重新启用多链。", "enableMultiChainHeader": "启用多链", "enabledMultiChainHeader": "已启用多链", "enabledMultiChainText": "您的钱包现在支持以太坊和 Polygon。", "incompatibleAccountHeader": "账户不兼容", "incompatibleAccountInterpolated": "请先移除这些仅以太坊账户，然后再启用仅 Solana 模式：<1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "最新变化", "welcomeToMultiChainPrimaryText": "一个钱包搞定一切", "welcomeToMultiChainDetail1Header": "以太坊和 Polygon 支持", "welcomeToMultiChainDetail1SecondaryText": "集中管理 Solana、以太坊和 Polygon 的所有代币和 NFT。", "welcomeToMultiChainDetail2Header": "使用您喜爱的所有应用", "welcomeToMultiChainDetail2SecondaryText": "无需切换网络即可连接多个链上的应用。", "welcomeToMultiChainDetail3Header": "导入 MetaMask 钱包", "welcomeToMultiChainDetail3SecondaryText": "在以太网和 Polygon 中轻松导入所有种子短语。", "welcomeToMultiChainIntro": "欢迎使用 Phantom Multichain", "welcomeToMultiChainIntroDesc": "集中管理 Solana、以太坊和 Polygon 的所有代币和 NFT。一个钱包即可满足您的一切需求。", "welcomeToMultiChainAccounts": "重新设计了多链账户", "welcomeToMultiChainAccountsDesc": "针对多链进行了重新设计，每个账户现在都有对应的 ETH 和 Polygon 地址。", "welcomeToMultiChainApps": "随时随地使用", "welcomeToMultiChainAppsDesc": "Phantom 兼容以太坊、Polygon 和 Solana 上的所有应用。点击“连接到 MetaMask”即可使用。", "welcomeToMultiChainImport": "立即从 MetaMask 导入", "welcomeToMultiChainImportDesc": "从 MetaMask 或 Coinbase Wallet 等钱包导入您的密钥短语或私钥。在一个地方集中管理一切。", "welcomeToMultiChainImportInterpolated": "从 MetaMask 或 Coinbase Wallet 等钱包<0>导入您的密钥短语</0>或私钥。在一个地方集中管理一切。", "welcomeToMultiChainTakeTour": "观看导览", "welcomeToMultiChainSwapperTitle": "在以太坊、Polygon 和\nSolana 上兑换", "welcomeToMultiChainSwapperDetail1Header": "以太坊和 Polygon 支持", "welcomeToMultiChainSwapperDetail1SecondaryText": "现在，您可以从钱包内轻松兑换 ERC-20 代币。", "welcomeToMultiChainSwapperDetail2Header": "最优惠的价格、超低的费用", "welcomeToMultiChainSwapperDetail2SecondaryText": "100 多个流动性来源和智能订单路由，助力获得最大回报。", "networkErrorTitle": "网络错误", "networkError": "很遗憾，我们无法访问网络。请稍后再试。", "authenticationUnlockPhantom": "解锁 Phantom", "errorAndOfflineSomethingWentWrong": "出错了", "errorAndOfflineSomethingWentWrongTryAgain": "请重试。", "errorAndOfflineUnableToFetchAssets": "我们无法获取资产。请稍后再试。", "errorAndOfflineUnableToFetchCollectibles": "我们无法获取收藏品。请稍后再试。", "errorAndOfflineUnableToFetchSwap": "我们无法获取兑换信息。请稍后再试。", "errorAndOfflineUnableToFetchTransactionHistory": "我们目前无法获得您的交易历史记录。请检查您的网络连接或稍后再试。", "errorAndOfflineUnableToFetchRewardsHistory": "我们无法获取奖励历史。请稍后再试。", "errorAndOfflineUnableToFetchBlockedUsers": "我们无法获取被屏蔽的用户。请稍后再试。", "networkHealthSheetCloseButtonText": "确定", "swapReviewError": "检查您的订单时出错，请重试。", "sendSelectToken": "选择代币", "swapBalance": "余额：", "swapTitle": "兑换代币", "swapSelectToken": "选择代币", "swapYouPay": "支付", "swapYouReceive": "收到", "aboutPrivacyPolicy": "隐私政策", "aboutVersion": "版本 {{version}}", "aboutVisitWebsite": "访问网站", "bottomSheetConnectTitle": "关联", "A11YbottomSheetConnectTitle": "底页连接", "A11YbottomSheetCommandClose": "底页拒绝", "A11YbottomSheetCommandBack": "底页返回", "bottomSheetSignTypedDataTitle": "签署消息", "bottomSheetSignMessageTitle": "签署消息", "bottomSheetSignInTitle": "登录", "bottomSheetSignInAndConnectTitle": "登录", "bottomSheetConfirmTransactionTitle": "确认交易", "bottomSheetConfirmTransactionsTitle": "确认交易", "bottomSheetSolanaPayTitle": "Solana Pay 请求", "bottomSheetAdvancedTitle": "高级", "bottomSheetReadOnlyAccountTitle": "仅查看模式", "bottomSheetTransactionSettingsTitle": "网络费用", "bottomSheetConnectDescription": "关联将允许此站点查看所选账户的余额和活动。", "bottomSheetSignInDescription": "签署此消息将证明您拥有所选账户的所有权。请仅通过您信任的应用程序签署消息。", "bottomSheetSignInAndConnectDescription": "批准将允许此站点查看所选账户的余额和活动。", "bottomSheetConfirmTransactionDescription": "余额变动为估计值。不保证所涉及的金额和资产。", "bottomSheetConfirmTransactionsDescription": "余额变动为估计值。不保证所涉及的金额和资产。", "bottomSheetSignTypedDataDescription": "这只是一个权限请求。交易可能不会立即执行。", "bottomSheetSignTypedDataSecondDescription": "余额变动为估计值。不保证所涉及的金额和资产。", "bottomSheetSignMessageDescription": "签署此消息将证明您拥有所选账户的所有权。请仅通过您信任的应用程序签署消息。", "bottomSheetReadOnlyAccountDescription": "无法在仅查看模式下执行此操作。", "bottomSheetMessageRow": "消息", "bottomSheetStatementRow": "声明", "bottomSheetAutoConfirmRow": "自动确认", "bottomSheetAutoConfirmOff": "关", "bottomSheetAutoConfirmOn": "开", "bottomSheetAccountRow": "账户", "bottomSheetAdvancedRow": "高级", "bottomSheetContractRow": "合约地址", "bottomSheetSpenderRow": "支出者地址", "bottomSheetNetworkRow": "网络", "bottomSheetNetworkFeeRow": "网络费用", "bottomSheetEstimatedTimeRow": "预计时间", "bottomSheetAccountRowDefaultAccountName": "账户", "bottomSheetConnectRequestDisclaimer": "仅连接到您信任的网站", "bottomSheetSignInRequestDisclaimer": "仅登录您信任的网站", "bottomSheetSignatureRequestDisclaimer": "仅当您信任此网站时确认。", "bottomSheetFeaturedTransactionDisclaimer": "在下一步中确认之前，您将看到交易的预览。", "bottomSheetIgnoreWarning": "忽略警告，仍然继续", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "未发现余额变化。请谨慎操作，仅在您信任此网站时确认。", "bottomSheetReadOnlyWarning": "您仅仅在监视此地址。您需要导入其助记词才能签署交易和消息。", "bottomSheetWebsiteIsUnsafeWarning": "此网站不安全，可能会试图窃取您的资金。", "bottomSheetViewOnExplorer": "在浏览器上查看", "bottomSheetTransactionSubmitted": "交易已提交", "bottomSheetTransactionPending": "交易待处理", "bottomSheetTransactionFailed": "交易失败", "bottomSheetTransactionSubmittedDescription": "您的交易已提交。您可以在浏览器上查看。", "bottomSheetTransactionFailedDescription": "您的交易失败。请重试。", "bottomSheetTransactionPendingDescription": "该交易正在处理…", "transactionsFromInterpolated": "自：{{from}}", "transactionsFromParagraphInterpolated": "从 {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "今天", "transactionsToInterpolated": "到：{{to}}", "transactionsToParagraphInterpolated": "到 {{to}}", "transactionsYesterday": "昨天", "addEditAddressAdd": "添加地址", "addEditAddressDelete": "删除地址", "addEditAddressDeleteTitle": "确定要删除此地址吗？", "addEditAddressSave": "保存地址", "dAppBrowserComingSoon": "dApp 浏览器即将推出！", "dAppBrowserSearchPlaceholder": "网站，代币，URL", "dAppBrowserOpenInNewTab": "在新选项卡中打开", "dAppBrowserSuggested": "建议", "dAppBrowserFavorites": "收藏夹", "dAppBrowserBookmarks": "书签", "dAppBrowserBookmarkAdd": "添加书签", "dAppBrowserBookmarkRemove": "移除书签", "dAppBrowserUsers": "用户", "dAppBrowserRecents": "最近", "dAppBrowserFavoritesDescription": "您的收藏夹将显示在此处", "dAppBrowserBookmarksDescription": "您的书签将显示在此处", "dAppBrowserRecentsDescription": "最近连接的 dapp 将显示在此处", "dAppBrowserEmptyScreenDescription": "输入 URL 或搜索网络", "dAppBrowserBlocklistScreenTitle": "{{origin}} 已被屏蔽！", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom 认为这是恶意网站，存在安全隐患。", "part2": "此网站已被标记为由社区维护的已知网络钓鱼网站和诈骗数据库的一部分。如果您认为该网站被错误标记，请提交问题。"}, "dAppBrowserLoadFailedScreenTitle": "无法加载", "dAppBrowserLoadFailedScreenDescription": "加载此页面时出错", "dAppBrowserBlocklistScreenIgnoreButton": "忽略警告，仍然显示", "dAppBrowserActionBookmark": "书签", "dAppBrowserActionRemoveBookmark": "移除书签", "dAppBrowserActionRefresh": "刷新", "dAppBrowserActionShare": "分享", "dAppBrowserActionCloseTab": "关闭选项卡", "dAppBrowserActionEndAutoConfirm": "结束自动确认", "dAppBrowserActionDisconnectApp": "断开连接应用", "dAppBrowserActionCloseAllTabs": "关闭所有选项卡", "dAppBrowserNavigationAddressPlaceholder": "输入 URL 以搜索", "dAppBrowserTabOverviewMore": "更多", "dAppBrowserTabOverviewAddTab": "添加选项卡", "dAppBrowserTabOverviewClose": "关闭", "dAppBrowserCloseTab": "关闭选项卡", "dAppBrowserClose": "关闭", "dAppBrowserTabOverviewAddBookmark": "添加书签", "dAppBrowserTabOverviewRemoveBookmark": "移除书签", "depositAssetListSuggestions": "建议", "depositUndefinedToken": "抱歉，无法存入此代币", "onboardingImportRecoveryPhraseDetails": "详细信息", "onboardingCreateRecoveryPhraseVerifyTitle": "已写下密钥恢复短语？", "onboardingCreateRecoveryPhraseVerifySubtitle": "如果没有密钥恢复短语，您将无法访问您的密钥或与之关联的任何资产。", "onboardingCreateRecoveryPhraseVerifyYes": "是", "onboardingCreateRecoveryPhraseErrorTitle": "错误", "onboardingCreateRecoveryPhraseErrorSubtitle": "我们未能成功生成账户，请重试。", "onboardingDoneDescription": "您现在可以充分体验您的钱包了。", "onboardingDoneGetStarted": "开始", "zeroBalanceHeading": "开始！", "zeroBalanceBuyCryptoTitle": "购买加密货币", "zeroBalanceBuyCryptoDescription": "使用借记卡或信用卡购买您的第一笔加密货币。", "zeroBalanceDepositTitle": "转移加密货币", "zeroBalanceDepositDescription": "从另一个钱包或交易所存入加密货币。", "onboardingImportAccountsEmptyResult": "找不到账户", "onboardingImportAccountsAccountName": "账户 {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "社交账户", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "我们找到了 {{numberOfWallets}} 个有活动的钱包", "onboardingImportAccountsFoundAccounts_other": "我们找到了 {{numberOfWallets}} 个有活动的账户", "onboardingImportAccountsFoundAccountsNoActivity_one": "我们找到了 {{numberOfWallets}} 个账户", "onboardingImportAccountsFoundAccountsNoActivity_other": "我们找到了 {{numberOfWallets}} 个账户", "onboardingImportRecoveryPhraseLessThanTwelve": "短语需要至少为 12 个单词。", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "短语需要为 12 个或 24 个单词。", "onboardingImportRecoveryPhraseWrongWord": "单词不正确：{{ words }}。", "onboardingProtectTitle": "保护您的钱包", "onboardingProtectDescription": "添加生物识别安全性将确保您是唯一可以访问您的钱包的人。", "onboardingProtectButtonHeadlineDevice": "设备", "onboardingProtectButtonHeadlineFaceID": "面容 ID", "onboardingProtectButtonHeadlineFingerprint": "指纹", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "使用 {{ authType }} 身份验证", "onboardingProtectError": "进行身份验证时出错，请重试", "onboardingProtectBiometryIosError": "生物识别身份验证已在 Phantom 中配置，但在系统设置中被禁用。请打开“设置 > Phantom > 面容 ID 或触控 ID”以重新启用。", "onboardingProtectRemoveAuth": "禁用身份验证", "onboardingProtectRemoveAuthDescription": "确定要禁用身份验证吗？", "onboardingWelcomeTitle": "欢迎使用 Phantom", "onboardingWelcomeDescription": "首先，请创建一个新钱包或导入现有钱包。", "onboardingWelcomeCreateWallet": "创建新钱包", "onboardingWelcomeAlreadyHaveWallet": "我已经有一个钱包", "onboardingWelcomeConnectSeedVault": "连接种子库", "onboardingSlide1Title": "由您掌控", "onboardingSlide1Description": "您的钱包通过生物识别访问、诈骗检测和全天候支持来保障安全。", "onboardingSlide2Title": "您最理想的 \nNFT 之家", "onboardingSlide2Description": "管理上架，焚毁垃圾，并借助实用的推送通知掌握最新动态。", "onboardingSlide3Title": "更大程度发挥您的代币的价值", "onboardingSlide3Description": "存储、兑换、质押、发送和接收 – 无需离开您的钱包。", "onboardingSlide4Title": "发现 Web3 的最佳优势", "onboardingSlide4Description": "使用应用内浏览器查找并关联到领先的应用和收藏。", "onboardingMultichainSlide5Title": "一个钱包搞定一切", "onboardingMultichainSlide5Description": "在一个人性化界面中体验所有 Solana 、以太坊和 Polygon。", "onboardingMultichainSlide5DescriptionWithBitcoin": "在一个人性化界面中管理 Solana 、以太坊、Polygon 和比特币。", "requireAuth": "要求身份验证", "requireAuthImmediately": "立即", "availableToSend": "可以发送", "sendEnterAmount": "输入金额", "sendEditMemo": "编辑备忘录", "sendShowLogs": "显示错误日志", "sendHideLogs": "隐藏错误日志", "sendGoBack": "返回", "sendTransactionSuccess": "您的代币已成功发送至", "sendInputPlaceholder": "@用户名或地址", "sendInputPlaceholderV2": "用户名或地址", "sendPeopleTitle": "人员", "sendDomainTitle": "域", "sendFollowing": "已关注", "sendRecentlyUsedAddressLabel": "{{formattedTimestamp}}前使用", "sendRecipientAddress": "接收方的地址", "sendTokenInterpolated": "发送 {{tokenSymbol}}", "sendPasteFromClipboard": "从剪贴板粘贴", "sendScanQR": "扫描 QR 码", "sendTo": "至：", "sendRecipientZeroBalanceWarning": "此钱包地址没有余额，且在您最近的交易记录中未出现。请确保地址正确。", "sendUnknownAddressWarning": "这不是您最近互动过的地址。继续操作时请多加留意。", "sendSameAddressWarning": "这是您的当前地址。发送将产生转账费用，不会有其他余额变动。", "sendMintAccountWarning": "这是一个铸造账户地址。您不能向此地址发送资金，因为这将导致永久损失。", "sendCameraAccess": "摄像头访问权限", "sendCameraAccessSubtitle": "要扫描 QR 码，需要启用摄像头访问权限。您想现在打开“设置”吗？", "sendSettings": "设置", "sendOK": "确定", "invalidQRCode": "此 QR 码无效。", "sendInvalidQRCode": "此 QR 码不是有效地址", "sendInvalidQRCodeSubtitle": "重试或使用其他 QR 码。", "sendInvalidQRCodeSplToken": "二维码中的代币无效", "sendInvalidQRCodeSplTokenSubtitle": "此二维码包含您未拥有或我们无法识别的代币。", "sendScanAddressToSend": "扫描 {{tokenSymbol}} 地址来发送资金", "sendScanAddressToSendNoSymbol": "扫描地址来发送资金", "sendScanAddressToSendCollectible": "扫描 SOL 地址来发送收藏品", "sendScanAddressToSendCollectibleMultichain": "扫描地址来发送收藏品", "sendSummary": "汇总", "sendUndefinedToken": "抱歉，无法发送此代币", "sendNoTokens": "无可用代币", "noBuyOptionsAvailableInCountry": "您所在的国家/地区无购买选项。", "swapAvailableTokenDisclaimer": "可用于在网络之间跨链的代币数量有限", "swapCrossSwapNetworkTooltipTitle": "跨网络兑换", "swapCrossSwapNetworkTooltipDescription": "跨网络兑换时，建议使用可用代币以获得最低的价格和最快的交易。", "settingsAbout": "关于 Phantom", "settingsShareAppWithFriends": "邀请好友", "settingsConfirm": "是", "settingsMakeSureNoOneIsWatching": "确保没有人在看您的屏幕", "settingsManageAccounts": "管理账户", "settingsPrompt": "确定要继续吗？", "settingsSelectAvatar": "选择头像", "settingsSelectSecretPhrase": "选择密钥短语", "settingsShowPrivateKey": "点按以显示您的私钥", "settingsShowRecoveryPhrase": "点按以显示您的密钥短语", "settingsSubmitBetaFeedback": "提交测试版反馈", "settingsUpdateAccountNameToast": "账户名称已更新", "settingsUpdateAvatarToast": "头像已更新", "settingsUpdateAvatarToastFailure": "无法更新头像！", "settingsWalletAddress": "账户地址", "settingsWalletAddresses": "账户地址", "settingsWalletNamePrimary": "账户名称", "settingsPlaceholderName": "名称", "settingsWalletNameSecondary": "更改钱包名称", "settingsYourAccounts": "您的账户", "settingsYourAccountsMultiChain": "多链", "settingsReportUser": "举报用户", "settingsNotifications": "通知", "settingsNotificationPreferences": "通知偏好设置", "pushNotificationsPreferencesAllowNotifications": "允许通知", "pushNotificationsPreferencesSentTokens": "发送的代币", "pushNotificationsPreferencesSentTokensDescription": "代币和 NFT 的转出", "pushNotificationsPreferencesReceivedTokens": "收到的代币", "pushNotificationsPreferencesReceivedTokensDescription": "代币和 NFT 的转入", "pushNotificationsPreferencesDexSwap": "兑换", "pushNotificationsPreferencesDexSwapDescription": "受信任的应用程序上的兑换", "pushNotificationsPreferencesOtherBalanceChanges": "其他余额变动", "pushNotificationsPreferencesOtherBalanceChangesDescription": "其他影响您的余额的多代币交易", "pushNotificationsPreferencesPhantomMarketing": "Phantom 动态", "pushNotificationsPreferencesPhantomMarketingDescription": "功能公告和一般动态", "pushNotificationsPreferencesDescription": "这些设置控制此有效钱包的推送通知。每个钱包都有自己的通知设置。要关闭所有 Phantom 推送通知，请转到您的<1>设备设置</1>。", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "无法同步通知偏好设置。", "connectSeedVaultConnectSeed": "关联种子", "connectSeedVaultConnectSeedDescription": "将 Phantom 关联到您手机上的种子库", "connectSeedVaultSelectAnAccount": "选择账户", "connectSeedVaultSelectASeed": "选择一个种子", "connectSeedVaultSelectASeedDescription": "选择要连接到 Phantom 的种子", "connectSeedVaultSelectAnAccountDescription": "选择您要使用 Phantom 设置的账户", "connectSeedVaultNoAccountsFound": "找不到账户。", "connectSeedVaultSelectAccounts": "选择账户", "connectSeedVaultSelectAccountsDescription": "选择您要使用 Phantom 设置的账户", "connectSeedVaultCompleteSetup": "完成设置", "connectSeedVaultCompleteSetupDescription": "设置完毕！使用 Phantom 探索 web3 并使用您的种子库确认交易", "connectSeedVaultConnectAnotherSeed": "再关联一个种子", "connectSeedVaultConnectAllSeedsConnected": "已连接所有种子", "connectSeedVaultNoSeedsConnected": "没有关联种子。点按下方按钮从种子库授权。", "connectSeedVaultConnectAccount": "关联账户", "connectSeedVaultLoadMore": "加载更多", "connectSeedVaultNeedPermission": "需要权限", "connectSeedVaultNeedPermissionDescription": "前往“设置”，允许 Phantom 使用“种子库”权限。", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} 费用", "stakeAmount": "金额", "stakeAmountBalance": "余额", "swapTopQuotes": "前 {{numQuotes}} 个报价", "swapTopQuotesTitle": "热门报价", "swapProvidersTitle": "提供商", "swapProvidersFee": "{{fee}} 费用", "swapProvidersTagRecommended": "最佳回报", "swapProvidersTagFastest": "最快", "swapProviderEstimatedTimeHM": "{{hours}} 小时 {{minutes}} 分钟", "swapProviderEstimatedTimeM": "{{minutes}} 分钟", "swapProviderEstimatedTimeS": "{{seconds}} 秒", "stakeReview": "检查", "stakeReviewAccount": "账户", "stakeReviewCommissionFee": "佣金", "stakeReviewConfirm": "确认", "stakeReviewValidator": "验证节点", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "质押转换失败", "convertStakeStatusErrorMessage": "您的质押无法转换为 {{poolTokenSymbol}}。请重试。", "convertStakeStatusLoadingTitle": "正在转换为 {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "我们正在开始将您的质押 {{stakedTokenSymbol}} 转换为 {{poolTokenSymbol}} 的过程。", "convertStakeStatusSuccessTitle": "到 {{poolTokenSymbol}} 的转换完成！", "convertStakeStatusSuccessMessage": "在<1>此处</1>使用您的 JitoSOL 赚取额外奖励。", "convertStakeStatusConvertMore": "转换更多", "convertStakePendingTitle": "正在将质押转换为 {{symbol}}", "convertToJitoSOL": "转换为 JitoSOL", "convertToJitoSOLInfoDescription": "将您的 SOL 转换为 JitoSOL，以赚取奖励并参与 Jito 生态系统。", "convertToJitoSOLInfoTitle": "转换为 JitoSOL", "convertStakeBannerTitle": "将您的质押转换为 JitoSOL 以便将奖励提高高达 15%", "convertStakeQuestBannerTitle": "将质押的 SOL 转换为 JitoSOL 并赚取奖励。了解详情。", "liquidStakeConvertInfoTitle": "转换为 JitoSOL", "liquidStakeConvertInfoDescription": "通过将 SOL 质押转换为 JitoSOL 来提高您的奖励。<1>了解详情</1>", "liquidStakeConvertInfoFeature1Title": "为何使用 Jito 质押？", "liquidStakeConvertInfoFeature1Description": "存入资金以获得 JitoSOL，它将随着您的质押增长。在 DeFi 协议中使用它以获得额外收益。之后，兑换您的 JitoSOL 以获得初始金额 + 累积奖励", "liquidStakeConvertInfoFeature2Title": "更高的平均奖励", "liquidStakeConvertInfoFeature2Description": "Jito 会将您的 SOL 分配给费用最低的最佳验证节点。MEV 奖励将进一步增加您的收益。", "liquidStakeConvertInfoFeature3Title": "支持 Solana 网络", "liquidStakeConvertInfoFeature3Description": "流动性质押通过将质押分散到多个验证节点来确保 Solana 的安全，降低了正常运行时间低的验证节点所带来的风险。", "liquidStakeConvertInfoSecondaryButton": "以后再说", "liquidStakeStartStaking": "开始质押", "liquidStakeReviewOrder": "审查订单", "convertStakeAccountListPageIneligibleSectionTitle": "质押账户不符合条件", "convertStakeAccountIneligibleBottomSheetTitle": "质押账户不符合条件", "convertStakeAccountListPageErrorTitle": "无法获取质押账户", "convertStakeAccountListPageErrorDescription": "抱歉，出错了，我们无法获取质押账户", "liquidStakeReviewYouPay": "支付", "liquidStakeReviewYouReceive": "收到", "liquidStakeReviewProvider": "提供商", "liquidStakeReviewNetworkFee": "网络费用", "liquidStakeReviewPageTitle": "确认", "liquidStakeReviewConversionFootnote": "当您质押 Solana 代币以换取 JitoSOL 时，您将获得稍微少一些的 JitoSOL。<1>了解详情</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Jito 的质押池支持大多数有效的 Solana 验证节点。您无法将来自不受支持的验证节点的已质押 SOL 转换为 JitoSOL。此外，新质押的 SOL 需要约 2 天时间才能符合转换为 JitoSOL 的条件。", "selectAValidator": "选择验证节点", "validatorSelectionListTitle": "选择验证节点", "validatorSelectionListDescription": "选择一个用于质押您的 SOL 的验证节点。", "stakeMethodDescription": "通过使用您的 SOL 代币帮助 Solana 扩容来赚取利息。<1>了解详情</1>", "stakeMethodRecommended": "推荐", "stakeMethodEstApy": "预计年化收益率：~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "流动性质押", "stakeMethodSelectionLiquidStakingDescription": "质押 SOL 以获得更高奖励，帮助保障 Solana 并接收 JitoSOL 以赚取额外奖励。", "stakeMethodSelectionNativeStakingTitle": "原生质押", "stakeMethodSelectionNativeStakingDescription": "质押 SOL 以在帮助保障 Solana 的同时获得奖励。", "liquidStakeMintStakeSOL": "质押 SOL", "mintJitoSOLInfoPageTitle": "隆重推出使用 Jito 进行流动性质押", "mintJitoSOLFeature1Title": "为何使用 Jito 质押？", "mintJitoSOLFeature1Description": "存入资金以获得 JitoSOL，它将随着您的质押增长。在 DeFi 协议中使用它以获得额外收益。之后，兑换您的 JitoSOL 以获得初始金额 + 累积奖励", "mintJitoSOLFeature2Title": "更高的平均奖励", "mintJitoSOLFeature2Description": "Jito 会将您的 SOL 分配给费用最低的最佳验证节点。MEV 奖励将进一步增加您的收益。", "mintJitoSOLFeature3Title": "支持 Solana 网络", "mintJitoSOLFeature3Description": "流动性质押通过将质押分散到多个验证节点来确保 Solana 的安全，降低了正常运行时间低的验证节点所带来的风险。", "mintLiquidStakePendingTitle": "铸造流动性质押", "mintStakeStatusErrorTitle": "铸造流动性质押失败", "mintStakeStatusErrorMessage": "您的 {{poolTokenSymbol}} 流动性质押无法铸造。请重试。", "mintStakeStatusSuccessTitle": "铸造 {{poolTokenSymbol}} 流动性质押完成！", "mintStakeStatusLoadingTitle": "正在铸造 {{poolTokenSymbol}} 流动性质押", "mintStakeStatusLoadingMessage": "我们正在开始铸造您的 {{poolTokenSymbol}} 流动性质押的过程。", "mintLiquidStakeAmountDescription": "选择您想要通过 Jito 质押的 SOL 数量", "mintLiquidStakeAmountProvider": "提供商", "mintLiquidStakeAmountApy": "预计 APY", "mintLiquidStakeAmountBestPrice": "价格", "mintLiquidStakeAmountInsufficientBalance": "余额不足", "mintLiquidStakeAmountMinRequired": "需要质押 {{amount}} {{symbol}}", "swapTooltipGotIt": "知道了", "swapTabInsufficientFunds": "资金不足", "swapNoAssetsFound": "无资产", "swapNoTokensFound": "找不到代币", "swapConfirmationTryAgain": "重试", "swapConfirmationGoBack": "返回", "swapNoQuotesFound": "找不到报价", "swapNotProviderFound": "我们无法找到此代币兑换的提供商。尝试其他代币。", "swapAvailableOnMainnet": "此功能仅在 Mainnet 上提供", "swapNotAvailableEVM": "EVM 账户尚不支持兑换", "swapNotAvailableOnSelectedNetwork": "所选网络不支持兑换", "singleChainSwapTab": "网络内", "crossChainSwapTab": "跨网络", "allFilter": "所有", "bridgeRefuelTitle": "加油", "bridgeRefuelDescription": "加油确保您可以在跨链后支付交易费用。", "bridgeRefuelLabelBalance": "您的 {{symbol}}", "bridgeRefuelLabelReceive": "收到", "bridgeRefuelLabelFee": "估计成本", "bridgeRefuelDismiss": "继续而不加油", "bridgeRefuelEnable": "启用加油", "unwrapWrappedSolError": "解封失败", "unwrapWrappedSolLoading": "正在解封…", "unwrapWrappedSolSuccess": "已解封", "unwrapWrappedSolViewTransaction": "查看交易", "dappApprovePopupSignMessage": "签署消息", "solanaPayFrom": "从", "solanaPayMessage": "消息", "solanaPayNetworkFee": "网络费用", "solanaPayFree": "免费", "solanaPayPay": "支付 {{item}}", "solanaPayPayNow": "立即支付", "solanaPaySending": "正在发送 {{item}}", "solanaPayReceiving": "正在接收 {{item}}", "solanaPayMinting": "正在铸造 {{item}}", "solanaPayTransactionProcessing": "您的交易正在处理，请稍候。", "solanaPaySent": "已发送！", "solanaPayReceived": "已接收！", "solanaPayMinted": "已铸造！", "solanaPaySentNFT": "已发送 NFT！", "solanaPayReceivedNFT": "已接收 NFT！", "solanaPayTokensSent": "您的代币已成功发送至 {{to}}", "solanaPayTokensReceived": "您收到了来自 {{from}} 的新代币", "solanaPayViewTransaction": "查看交易", "solanaPayTransactionFailed": "交易失败", "solanaPayConfirm": "确认", "solanaPayTo": "至", "dappApproveConnectViewAccount": "查看您的 Solana 账户", "deepLinkInvalidLink": "链接无效", "deepLinkInvalidSplTokenSubtitle": "其中包含您未拥有或我们无法识别的代币。", "walletAvatarShowAllAccounts": "显示所有账户", "pushNotificationsGetInstantUpdates": "即时获取动态", "pushNotificationsEnablePushNotifications": "启用已完成转移、兑换和公告的推送通知", "pushNotificationsEnable": "启用", "pushNotificationsNotNow": "以后再说", "onboardingAgreeToTermsOfServiceInterpolated": "我同意<1>服务条款</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "我已经将它保存好了", "onboardingCreateNewWallet": "创建新钱包", "onboardingErrorDuplicateSecretRecoveryPhrase": "此密钥短语在您的钱包中已存在", "onboardingErrorInvalidSecretRecoveryPhrase": "密钥恢复短语无效", "onboardingFinished": "一切准备就绪！", "onboardingImportAccounts": "导入账户", "onboardingImportImportingAccounts": "正在导入账户…", "onboardingImportImportingFindingAccounts": "正在查找有活动的账户", "onboardingImportAccountsLastActive": "{{formattedTimestamp}}前有效", "onboardingImportAccountsNeverUsed": "从未使用", "onboardingImportAccountsCreateNew": "新钱包", "onboardingImportAccountsDescription": "选择要导入的钱包账户", "onboardingImportReadOnlyAccountDescription": "添加您想要监视的地址或域名。您将拥有只读权限，无法签署交易或消息。", "onboardingImportSecretRecoveryPhrase": "导入密钥短语", "onboardingImportViewAccounts": "查看账户", "onboardingRestoreExistingWallet": "通过 12 或 24 个的秘密助记词恢复已有的钱包", "onboardingShowUnusedAccounts": "显示未使用的账户", "onboardingShowMoreAccounts": "显示更多账户", "onboardingHideUnusedAccounts": "隐藏未使用的账户", "onboardingSecretRecoveryPhrase": "助记词", "onboardingSelectAccounts": "选择您的账户", "onboardingStoreSecretRecoveryPhraseReminder": "这是恢复帐户的唯一方法。请务必存放在安全的地方！", "useTokenMetasForMintsUnknownName": "未知", "timeUnitMinute": "分钟", "timeUnitMinutes": "分钟", "timeUnitHour": "小时", "timeUnitHours": "小时", "espNFTListWithPrice": "您在 {{dAppName}} 上将 {{NFTDisplayName}} 上架为 {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithPriceWithoutDApp": "您将 {{NFTDisplayName}} 上架为 {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "您在 {{dAppName}} 上将 {{NFTDisplayName}} 上架出售", "espNFTListWithoutPriceWithoutDApp": "您将 {{NFTDisplayName}} 上架出售", "espNFTChangeListPriceWithPrice": "您在 {{dAppName}} 上将 {{NFTDisplayName}} 的上架详情更新为 {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithPriceWithoutDApp": "您将 {{NFTDisplayName}} 的上架详情更新为 {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "您更新了 {{dAppName}} 上的 {{NFTDisplayName}} 上架详情", "espNFTChangeListPriceWithoutPriceWithoutDApp": "您更新了 {{NFTDisplayName}} 的上架详情", "espNFTBidBidderWithPrice": "您对 {{dAppName}} 上的 {{NFTDisplayName}} 出价 {{priceAmount}}{{priceTokenSymbol}}", "espNFTBidBidderWithPriceWithoutDApp": "您对 {{NFTDisplayName}} 出价 {{priceAmount}}{{priceTokenSymbol}}", "espNFTBidBidderWithoutPrice": "您已对 {{dAppName}} 上的 {{NFTDisplayName}} 出价", "espNFTBidBidderWithoutPriceWithoutDApp": "您已对 {{NFTDisplayName}} 出价", "espNFTBidListerWithPrice": "对 {{dAppName}} 上的 {{NFTDisplayName}} 的新出价 {{priceAmount}} {{priceTokenSymbol}}", "espNFTBidListerWithPriceWithoutDApp": "对 {{NFTDisplayName}} 的新出价 {{priceAmount}} {{priceTokenSymbol}}", "espNFTBidListerWithoutPrice": "对 {{dAppName}} 上的 {{NFTDisplayName}} 的新出价", "espNFTBidListerWithoutPriceWithoutDApp": "对 {{NFTDisplayName}} 的新出价", "espNFTCancelBidWithPrice": "您取消了对 {{dAppName}} 上的 {{NFTDisplayName}} 的出价 {{priceAmount}} {{priceTokenSymbol}}", "espNFTCancelBidWithPriceWithoutDApp": "您取消了对 {{NFTDisplayName}} 的出价 {{priceAmount}} {{priceTokenSymbol}}", "espNFTCancelBidWithoutPrice": "您取消了对 {{dAppName}} 上的 {{NFTDisplayName}} 的出价", "espNFTCancelBidWithoutPriceWithoutDApp": "您取消了对 {{NFTDisplayName}} 的出价", "espNFTUnlist": "您在 {{dAppName}} 上下架了 {{NFTDisplayName}}", "espNFTUnlistWithoutDApp": "您下架了 {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "您在 {{dAppName}} 上以 {{priceAmount}} {{priceTokenSymbol}} 购买了 {{NFTDisplayName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "您以 {{priceAmount}} {{priceTokenSymbol}} 购买了 {{NFTDisplayName}}", "espNFTBuyBuyerWithoutPrice": "您在 {{dAppName}} 上购买了 {{NFTDisplayName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "您购买了 {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "您在 {{dAppName}} 上以 {{priceAmount}} {{priceTokenSymbol}} 出售了 {{NFTDisplayName}}", "espNFTBuySellerWithPriceWithoutDApp": "您以 {{priceAmount}} {{priceTokenSymbol}} 出售了 {{NFTDisplayName}}", "espNFTBuySellerWithoutPrice": "您在 {{dAppName}} 上出售了 {{NFTDisplayName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "您出售了 {{NFTDisplayName}}", "espDEXSwap": "您在 {{dAppName}} 上将 {{downTokensTextFragment}} 兑换为 {{upTokensTextFragment}}", "espDEXDepositLPWithPoolDisplay": "您在 {{dAppName}} 上将 {{downTokensTextFragment}} 存入 {{poolDisplayName}} 流动性池", "espDEXDepositLPWithoutPoolDisplay": "您在 {{dAppName}} 上将 {{downTokensTextFragment}} 兑换为 {{upTokensTextFragment}}", "espDEXWithdrawLPWithPoolDisplay": "您在 {{dAppName}} 上将 {{upTokensTextFragment}} 从 {{poolDisplayName}} 流动性池中取出", "espDEXWithdrawLPWithoutPoolDisplay": "您在 {{dAppName}} 上将 {{downTokensTextFragment}} 兑换为 {{upTokensTextFragment}}", "espGenericTokenSend": "您发送了 {{downTokensTextFragment}}", "espGenericTokenReceive": "您收到了 {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "您将 {{downTokensTextFragment}} 兑换为 {{upTokensTextFragment}}", "espUnknown": "未知", "espUnknownNFT": "未知 NFT", "espTextFragmentAnd": "和", "externalLinkWarningTitle": "您将离开 Phantom", "externalLinkWarningDescription": "并打开 {{url}}。在与之交互之前，请确保您信任此来源。", "shortcutsWarningDescription": "快捷方式由 {{url}} 提供。确保您已信任此源，然后再与其进行交互。", "lowTpsBanner": "Solana 当前出现网络拥塞", "lowTpsMessageTitle": "Solana 网络拥塞", "lowTpsMessage": "由于 Solana 高度拥塞，您的交易可能会失败或延迟。请重试失败的交易。", "solanaSlow": "Solana 网络异乎寻常的慢", "solanaNetworkTemporarilyDown": "Solana 网络暂时停止服务", "waitForNetworkRestart": "请等待网络重启。您的资金不受影响。", "exploreCollectionsCarouselTitle": "最受欢迎", "exploreDropsCarouselTitle": "最新变化", "exploreSortFloor": "底价", "exploreSortListed": "已上架", "exploreSortVolume": "交易量", "exploreFetchErrorSubtitle": "请稍后再试。", "exploreFetchErrorTitle": "无法获取。", "exploreTopCollectionsTitle": "热门 NFT 收藏", "exploreTopListLess": "收起", "exploreTopListMore": "展开", "exploreSeeMore": "查看更多", "exploreTrendingTokens": "流行代币", "exploreVolumeTokens": "最高交易量", "explorePriceChangeTokens": "最大升幅", "explorePriceTokens": "按价格排列的代币", "exploreMarketCapTokens": "热门代币", "exploreTrendingSites": "流行网站", "exploreTopSites": "热门网站", "exploreTrendingCollections": "流行收藏", "exploreTopCollections": "热门收藏", "collectiblesSearchCollectionsSection": "收藏", "collectiblesSearchItemsSection": "条目", "collectiblesSearchNrOfItems": "{{ nrOfItems }} 个条目", "collectiblesSearchPlaceholderText": "搜索您的收藏品", "collectionPinSuccess": "已置顶收藏", "collectionPinFail": "无法置顶收藏", "collectionUnpinSuccess": "已取消置顶收藏", "collectionUnpinFail": "无法取消置顶收藏", "collectionHideSuccess": "已隐藏收藏", "collectionHideFail": "无法隐藏收藏", "collectionUnhideSuccess": "已取消隐藏收藏", "collectionUnhideFail": "无法取消隐藏收藏", "collectiblesSpamSuccess": "已报告为垃圾", "collectiblesSpamFail": "报告为垃圾失败", "collectiblesSpamAndHiddenSuccess": "已报告为垃圾并隐藏", "collectiblesNotSpamSuccess": "报告为非垃圾内容", "collectiblesNotSpamFail": "报告为非垃圾内容失败", "collectiblesNotSpamAndUnhiddenSuccess": "已报告为非垃圾内容并取消隐藏", "tokenPageSpamWarning": "此代币未经验证。仅与您信任的代币进行交互。", "tokenSpamWarning": "此代币被隐藏，因为 Phantom 认为它是垃圾内容。", "collectibleSpamWarning": "此收藏品被隐藏，因为 Phantom 认为它是垃圾内容。", "collectionSpamWarning": "这些收藏品被隐藏，因为 Phantom 认为它们是垃圾内容。", "emojiNoResults": "找不到表情符号", "emojiSearchResults": "搜索结果", "emojiSuggested": "建议", "emojiSmileys": "笑脸与人", "emojiAnimals": "动物与自然", "emojiFood": "食品与饮料", "emojiTravel": "旅行与地点", "emojiActivities": "活动", "emojiObjects": "物品", "emojiSymbols": "符号", "emojiFlags": "旗帜", "whichExtensionToConnectWith": "您要关联哪个扩展程序？", "configureInSettings": "可以在“设置 → 默认应用钱包”中配置。", "continueWith": "继续使用", "useMetaMask": "使用 MetaMask", "usePhantom": "使用 Phantom", "alwaysAsk": "始终询问", "dontAskMeAgain": "不再询问", "selectWalletSettingDescriptionLine1": "某些应用可能不提供连接 Phantom 的选项。", "selectWalletSettingDescriptionLinePhantom": "解决方法是，连接 MetaMask 时将始终打开 Phantom。", "selectWalletSettingDescriptionLineAlwaysAsk": "解决方法是，当您连接 MetaMask 时，我们将询问您是否想要使用 Phantom。", "selectWalletSettingDescriptionLineMetaMask": "设置默认使用 MetaMask 将禁止这些 DApp 连接 Phantom。", "metaMaskOverride": "默认应用钱包", "metaMaskOverrideSettingDescriptionLine1": "用于连接不提供使用 Phantom 这一选项的网站。", "refreshAndReconnectToast": "刷新并重新连接，以应用更改", "autoConfirmUnavailable": "不可用", "autoConfirmReasonDappNotWhitelisted": "不可用，因为它所来自的合约不在此应用的许可名单中。", "autoConfirmReasonSessionNotActive": "不可用，因为没有有效的自动确认会话。请在下方启用。", "autoConfirmReasonRateLimited": "不可用，因为您使用的 dapp 发送的请求过多。", "autoConfirmReasonUnsupportedNetwork": "不可用，因为自动确认尚不支持此网络。", "autoConfirmReasonSimulationFailed": "不可用，因为我们无法保证安全性。", "autoConfirmReasonTabNotFocused": "不可用，因为您尝试自动确认的域选项卡未处于有效状态。", "autoConfirmReasonNotUnlocked": "不可用，因为钱包未解锁。", "rpcErrorUnauthorizedWrongAccount": "来自地址的交易与所选账户地址不匹配。", "rpcErrorUnauthorizedUnknownSource": "无法确定 RPC 请求源。", "transactionsDisabledTitle": "交易已禁用", "transactionsDisabledMessage": "您的地址无法使用 Phantom 进行交易", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "有效", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL 已复制到剪贴板", "notEnoughSolScanTransactionWarning": "由于您账户中的 SOL 不足，此交易可能失败。请向您的账户添加更多 SOL 并重试。", "transactionRevertedWarning": "此交易在模拟过程中已被撤销。如果提交，资金可能会丢失。", "slippageToleranceExceeded": "此交易在模拟过程中已被撤销。超过滑点容差。", "simulationWarningKnownMalicious": "我们认为此账户是恶意账户。批准可能会导致资金损失。", "simulationWarningPoisonedAddress": "此地址与您最近转账到的一个地址惊人得相似。请确认这是正确的地址，以防止资金被骗。", "simulationWarningInteractingWithAccountWithoutActivity": "这是一个没有任何历史活动的账户。向一个不存在的账户发送资金可能会导致资金损失。", "quests": "任务", "questsClaimInProgress": "正在领取", "questsVerifyingCompletion": "正在验证任务是否完成", "questsClaimError": "领取奖励时出错", "questsClaimErrorDescription": "领取奖励时出错。请稍后再试。", "questsBadgeMobileOnly": "仅移动版", "questsBadgeExtensionOnly": "仅扩展程序", "questsExplainerSheetButtonLabel": "知道了", "questsNoQuestsAvailable": "无可用任务", "questsNoQuestsAvailableDescription": "目前没有可用任务。一旦有新任务添加，我们会立即通知您。", "exploreLearn": "了解", "exploreSites": "网站", "exploreTokens": "代币", "exploreQuests": "任务", "exploreCollections": "收藏", "exploreFilterByall_networks": "所有网络", "exploreSortByrank": "流行", "exploreSortBytrending": "流行", "exploreSortByprice": "价格", "exploreSortByprice-change": "价格变动", "exploreSortBytop": "热门", "exploreSortByvolume": "交易量", "exploreSortBygainers": "获利者", "exploreSortBylosers": "损失者", "exploreSortBymarket-cap": "市值", "exploreSortBymarket_cap": "市值", "exploreTimeFrame1h": "1 小时", "exploreTimeFrame24h": "24 小时", "exploreTimeFrame7d": "7 天", "exploreTimeFrame30d": "30 天", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "收藏品", "exploreCategoryMarketplace": "市场", "exploreCategoryGaming": "游戏", "exploreCategoryBridges": "跨链", "exploreCategoryOther": "其他", "exploreCategorySocial": "社交", "exploreCategoryCommunity": "社区", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "质押", "exploreCategoryArt": "艺术", "exploreCategoryTools": "工具", "exploreCategoryDeveloperTools": "开发者工具", "exploreCategoryHackathon": "黑客马拉松", "exploreCategoryNFTStaking": "NFT 质押", "exploreCategoryExplorer": "浏览器", "exploreCategoryInscriptions": "铭文", "exploreCategoryBridge": "跨链", "exploreCategoryAirdrop": "空投", "exploreCategoryAirdropChecker": "空投检查器", "exploreCategoryPoints": "积分", "exploreCategoryQuests": "任务", "exploreCategoryShop": "商店", "exploreCategoryProtocol": "协议", "exploreCategoryNamingService": "命名服务", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "组合跟踪器", "exploreCategoryFitness": "健身", "exploreCategoryDePIN": "DePIN", "exploreVolume": "交易量", "exploreFloor": "底价", "exploreCap": "市值", "exploreToken": "代币", "explorePrice": "价格", "explore24hVolume": "24 小时交易量", "exploreErrorButtonText": "重试", "exploreErrorDescription": "尝试加载“探索”内容时出错。请刷新并重试", "exploreErrorTitle": "无法加载“探索”内容", "exploreNetworkError": "存在网络错误。请稍后再试。", "exploreTokensLegalDisclaimer": "代币列表使用各种第三方提供商（包括 CoinGecko、<PERSON><PERSON> 和 Jupiter）提供的市场数据生成。表现基于过去 24 小时的数据。过去的表现并不代表未来的表现。", "swapperTokensLegalDisclaimer": "趋势代币列表使用各种第三方提供商（包括 CoinGecko、<PERSON><PERSON> 和 Jupiter）的市场数据，基于 Phantom 用户在规定时间内通过 Swapper 兑换的流行代币生成。过去的表现并不代表未来的表现。", "exploreLearnErrorTitle": "无法加载“学习”内容", "exploreLearnErrorDescription": "尝试加载“学习”内容时出错。请刷新并重试", "exploreShowMore": "展开", "exploreShowLess": "收起", "exploreVisitSite": "访问网站", "dappBrowserSearchScreenVisitSite": "访问网站", "dappBrowserSearchScreenSearchWithGoogle": "使用 Google 搜索", "dappBrowserSearchScreenSearchLinkYouCopied": "您复制的链接", "dappBrowserExtSearchPlaceholder": "搜索网站、代币", "dappBrowserSearchNoAppsTokens": "找不到应用或代币", "dappBrowserTabsLimitExceededScreenTitle": "关闭较旧的选项卡？", "dappBrowserTabsLimitExceededScreenDescription": "您打开了 {{tabsCount}} 个选项卡。要打开更多选项卡，您需要关闭一些选项卡。", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "关闭所有选项卡", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN：此域不存在", "dappBrowserTabErrorHttp": "已被屏蔽，请使用 HTTPS", "dappBrowserTabError401Unauthorized": "401 未授权", "dappBrowserTabError501UnhandledRequest": "501 未处理的请求", "dappBrowserTabErrorTimeout": "超时：服务器响应时间过长", "dappBrowserTabErrorInvalidResponse": "响应无效", "dappBrowserTabErrorEmptyResponse": "空响应", "dappBrowserTabErrorGeneric": "出错了", "localizedErrorUnknownError": "出错了，请稍后再试。", "localizedErrorUnsupportedCountry": "很抱歉，目前我们不支持您的国家/地区。", "localizedErrorTokensNotLoading": "加载您的代币时出错。请重试。", "localizedErrorSwapperNoQuotes": "由于不受支持的币对、流动性低或金额低，无法进行兑换。尝试调整代币或金额。", "localizedErrorSwapperRefuelNoQuotes": "找不到任何报价。请尝试不同的代币、金额，或禁用加油。", "localizedErrorInsufficientSellAmount": "代币金额过低。增加价值以进行跨链兑换。", "localizedErrorCrossChainUnavailable": "跨链兑换目前不可用，请稍后再试。", "localizedErrorTokenNotTradable": "所选代币中有一种无法交易。请选择另一种代币。", "localizedErrorCollectibleLocked": "代币账户已锁定。", "localizedErrorCollectibleListed": "代币账户已上架。", "spamActivityAction": "查看隐藏的条目", "spamActivityTitle": "隐藏的活动", "spamActivityWarning": "此交易被隐藏，因为 Phantom 认为它可能是垃圾内容。", "appAuthenticationFailed": "无法验证身份", "appAuthenticationFailedDescription": "您的身份验证尝试出现问题，请重试。", "partialErrorBalanceChainName": "我们在更新您的 {{chainName}} 余额时遇到了问题。您的资金是安全的。", "partialErrorGeneric": "我们在更新网络时遇到了问题，您的一些代币余额和价格可能已过时。您的资金是安全的。", "partialErrorTokenDetail": "我们在更新您的代币余额时遇到了问题。您的资金是安全的。", "partialErrorTokenPrices": "我们在更新您的代币价格时遇到了问题。您的资金是安全的。", "partialErrorTokensTrimmed": "我们在显示您的投资组合中的所有代币时遇到了问题。您的资金很安全。", "publicFungibleDetailAbout": "关于", "publicFungibleDetailYourBalance": "余额", "publicFungibleDetailInfo": "信息", "publicFungibleDetailShowMore": "展开", "publicFungibleDetailShowLess": "收起", "publicFungibleDetailPerformance": "24 小时表现", "publicFungibleDetailSecurity": "安全性", "publicFungibleDetailMarketCap": "市值", "publicFungibleDetailTotalSupply": "总供应量", "publicFungibleDetailCirculatingSupply": "流通供应量", "publicFungibleDetailMaxSupply": "最大供应量", "publicFungibleDetailHolders": "持有者", "publicFungibleDetailVolume": "交易量", "publicFungibleDetailTrades": "交易数", "publicFungibleDetailTraders": "交易者数", "publicFungibleDetailUniqueWallets": "唯一钱包数", "publicFungibleDetailTop10Holders": "排名前 10 的持有者", "publicFungibleDetailTop10HoldersTooltip": "指示前十大持有者所持有的代币占当前总供应量的百分比。这是衡量价格易于被操纵程度的一个指标。", "publicFungibleDetailMintable": "可铸造", "publicFungibleDetailMintableTooltip": "如果代币是可铸造的，合约所有者可以增加代币供应量。", "publicFungibleDetailMutableInfo": "可变信息", "publicFungibleDetailMutableInfoTooltip": "如果诸如名称、徽标和网站地址等代币信息是可变的，它可以由合约所有者更改。", "publicFungibleDetailOwnershipRenounced": "所有权已放弃", "publicFungibleDetailOwnershipRenouncedTooltip": "如果代币所有权被放弃，那么没有人能执行诸如铸造更多代币之类的功能。", "publicFungibleDetailUpdateAuthority": "更新权限", "publicFungibleDetailUpdateAuthorityTooltip": "更新权限是如果代币可变，可以更改信息的钱包地址。", "publicFungibleDetailFreezeAuthority": "冻结权限", "publicFungibleDetailFreezeAuthorityTooltip": "冻结权限是可以防止资金被转移的钱包地址。", "publicFungibleUnverifiedToken": "此代币未经验证。仅与您信任的代币进行交互。", "publicFungibleDetailSwap": "兑换 {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "使用 Phantom 应用兑换 {{tokenSymbol}}", "publicFungibleDetailLinkCopied": "已复制到剪贴板", "publicFungibleDetailContract": "合约", "publicFungibleDetailMint": "铸造", "unifiedTokenDetailTransactionActivity": "活动", "unifiedTokenDetailSeeMoreTransactionActivity": "查看更多", "unifiedTokenDetailTransactionActivityError": "无法加载最近的活动", "additionalNetworksTitle": "其他网络", "copyAddressRowAdditionalNetworks": "其他网络", "copyAddressRowAdditionalNetworksHeader": "以下网络使用与以太坊相同的地址：", "copyAddressRowAdditionalNetworksDescription": "您可以安全地使用以太坊地址在其中的任何网络上发送和接收资产。", "cpeUnknownError": "未知错误", "cpeUnknownInstructionError": "未知指令错误", "cpeAccountFrozen": "账户被冻结", "cpeAssetFrozen": "资产被冻结", "cpeInsufficientFunds": "资金不足", "cpeInvalidAuthority": "权限无效", "cpeBalanceBelowRent": "余额低于免租金阈值", "cpeNotApprovedForConfidentialTransfers": "账户未获批准进行机密转账", "cpeNotAcceptingDepositsOrTransfers": "账户不接受存款或转账", "cpeNoMemoButRequired": "上一个指令中没有备忘录；接收者需要此项才能接收转账", "cpeTransferDisabledForMint": "已对此铸造禁用转账", "cpeDepositAmountExceedsLimit": "存款金额超过最高限额", "cpeInsufficientFundsForRent": "用于租金的资金不足", "reportIssueScreenTitle": "报告问题", "publicFungibleReportIssuePrompt": "您想报告 {{tokenName}} 的什么问题？", "publicFungibleReportIssueIncorrectInformation": "信息不正确", "publicFungibleReportIssuePriceStale": "价格未更新", "publicFungibleReportIssuePriceMissing": "价格缺失", "publicFungibleReportIssuePerformanceIncorrect": "24 小时表现不正确", "publicFungibleReportIssueLinkBroken": "社交链接无法访问", "publicFungibleDetailErrorLoading": "代币数据不可用", "reportUserPrompt": "您想举报 @{{username}} 的什么问题？", "reportUserOptionAbuseAndHarrassmentTitle": "滥用和骚扰", "reportUserOptionAbuseAndHarrassmentDescription": "针对性的骚扰、煽动骚扰、暴力威胁、仇恨内容和引用", "reportUserOptionPrivacyAndImpersonationTitle": "隐私和冒充", "reportUserOptionPrivacyAndImpersonationDescription": "分享或威胁曝光私人信息，假装成其他人", "reportUserOptionSpamTitle": "垃圾内容", "reportUserOptionSpamDescription": "假账户、诈骗、恶意链接", "reportUserSuccess": "用户举报已提交。", "settingsClaimUsernameTitle": "创建用户名", "settingsClaimUsernameDescription": "一个独一无二的身份，就像您的钱包一样独特", "settingsClaimUsernameValueProp1": "简化身份", "settingsClaimUsernameValueProp1Description": "向长而复杂的地址说再见，迎接人性化的身份", "settingsClaimUsernameValueProp2": "更快、更简单", "settingsClaimUsernameValueProp2Description": "轻松发送和接收加密货币，登录您的钱包，并与朋友建立联系", "settingsClaimUsernameValueProp3": "保持同步", "settingsClaimUsernameValueProp3Description": "将任何账户关联到您的用户名，它将在所有设备上同步", "settingsClaimUsernameHelperText": "您的 Phantom 账户的唯一名称", "settingsClaimUsernameValidationDefault": "此用户名后续无法更改", "settingsClaimUsernameValidationAvailable": "用户名可用", "settingsClaimUsernameValidationUnavailable": "用户名不可用", "settingsClaimUsernameValidationServerError": "无法检查用户名是否可用，请稍后再试", "settingsClaimUsernameValidationErrorLine1": "用户名无效。", "settingsClaimUsernameValidationErrorLine2": "用户名的长度必须介于 {{minChar}} 和 {{maxChar}} 个字符之间，并且只能包含字母和数字。", "settingsClaimUsernameValidationLoading": "正在检查此用户名是否可用…", "settingsClaimUsernameSaveAndContinue": "保存并继续", "settingsClaimUsernameChooseAvatarTitle": "选择头像", "settingsClaimUsernameAnonymousAuthTitle": "匿名身份验证", "settingsClaimUsernameAnonymousAuthDescription": "使用签名匿名登录您的 Phantom 账户", "settingsClaimUsernameAnonymousAuthBadge": "了解运作方式", "settingsClaimUsernameLinkWalletsTitle": "关联您的钱包", "settingsClaimUsernameLinkWalletsDescription": "选择在其他设备上以您的用户名显示的钱包", "settingsClaimUsernameLinkWalletsBadge": "不可公开查看", "settingsClaimUsernameConnectAccountsTitle": "关联账户", "settingsClaimUsernameConnectAccountsHelperText": "每个链地址都将与您的用户名关联。您稍后可以进行更改。", "settingsClaimUsernameContinue": "继续", "settingsClaimUsernameCreateUsername": "创建用户名", "settingsClaimUsernameCreating": "正在创建用户名…", "settingsClaimUsernameSuccess": "用户名已创建！", "settingsClaimUsernameError": "我们在创建您的用户名时遇到了错误", "settingsClaimUsernameTryAgain": "重试", "settingsClaimUsernameSuccessHelperText": "您现在可以在所有的 Phantom 钱包中使用新用户名了", "settingsClaimUsernameSettingsSyncedTitle": "同步的设置", "settingsClaimUsernameSettingsSyncedHelperText": "向长而复杂的地址说再见，迎接人性化的身份", "settingsClaimUsernameSendToUsernameTitle": "发送到用户名", "settingsClaimUsernameSendToUsernameHelperText": "轻松发送和接收加密货币，登录您的钱包，并与朋友建立联系", "settingsClaimUsernameManageAddressesTitle": "公开地址", "settingsClaimUsernameManageAddressesHelperText": "任何发送到您的用户名的代币或收藏品都将发送到这些地址", "settingsClaimUsernameManageAddressesBadge": "可公开查看", "settingsClaimUsernameEditAddressesTitle": "管理公开地址", "settingsClaimUsernameEditAddressesHelperText": "任何发送到您的用户名的代币或收藏品都将发送到这些地址。为每个链选择一个地址。", "settingsClaimUsernameEditAddressesError": "每个网络只允许一个地址。", "settingsClaimUsernameEditAddressesEditAddress": "编辑地址", "settingsClaimUsernameNoAddressesSaved": "没有保存的公开地址", "settingsClaimUsernameSave": "保存", "settingsClaimUsernameDone": "完成", "settingsClaimUsernameWatching": "正在监视", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} 个账户", "settingsClaimUsernameNoOfAccountsSingular": "1 个账户", "settingsClaimUsernameEmptyAccounts": "无账户", "settingsClaimUsernameSettingTitle": "创建您的 @用户名", "settingsClaimUsernameSettingDescription": "您的钱包的唯一身份", "settingsManageUserProfileAbout": "关于", "settingsManageUserProfileAboutUsername": "用户名", "settingsManageUserProfileAboutBio": "个人简介", "settingsManageUserProfileTitle": "管理资料", "settingsManageUserProfileManage": "管理", "settingsManageUserProfileAuthFactors": "身份验证因素", "settingsManageUserProfileAuthFactorsDescription": "选择可以登录您的 Phantom 账户的种子短语或私钥。", "settingsManageUserProfileUpdateAuthFactorsToast": "身份验证因素已更新！", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "无法更新身份验证因素！", "settingsManageUserProfileBiography": "编辑个人简介", "settingsManageUserProfileBiographyDescription": "在您的个人资料中添加简短的个人简介", "settingsManageUserProfileUpdateBiographyToast": "个人简介已更新！", "settingsManageUserProfileUpdateBiographyToastFailure": "出错了。请重试", "settingsManageUserProfileBiographyNoUrlMessage": "请从您的个人简介中移除任何 URL", "settingsManageUserProfileLinkedWallets": "关联的钱包", "settingsManageUserProfileLinkedWalletsDescription": "选择在登录您的 Phantom 账户时会出现在其他设备上的钱包。", "settingsManageUserProfileUpdateLinkedWalletsToast": "关联的钱包已更新！", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "无法更新关联的钱包！", "settingsManageUserProfilePrivacy": "隐私", "settingsManageUserProfileUpdatePrivacyStateToast": "隐私已更新！", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "无法更新隐私！", "settingsManageUserProfilePublicAddresses": "公开地址", "settingsManageUserProfileUpdatePublicAddressToast": "公开网址已更新！", "settingsManageUserProfileUpdatePublicAddressToastFailure": "无法更新公开网址！", "settingsManageUserProfilePrivacyStatePublic": "公开", "settingsManageUserProfilePrivacyStatePublicDescription": "您的资料和公开网址对任何人可见并且可被任何人搜索", "settingsManageUserProfilePrivacyStatePrivate": "不公开", "settingsManageUserProfilePrivacyStatePrivateDescription": "您的资料可被任何人搜索，但其他人必须请求权限才能查看您的资料和公开网址。", "settingsManageUserProfilePrivacyStateInvisible": "不可见", "settingsManageUserProfilePrivacyStateInvisibleDescription": "您的资料和公开网址在任何地方都将隐藏并且无法被发现", "settingsDownloadPhantom": "下载 Phantom", "settingsLogOut": "退出登录", "seedlessAddAWalletPrimaryText": "添加钱包", "seedlessAddAWalletSecondaryText": "登录或导入现有钱包", "seedlessAddSeedlessWalletPrimaryText": "添加无种子钱包", "seedlessAddSeedlessWalletSecondaryText": "使用您的 Apple ID、Google 账号或电子邮件地址", "seedlessCreateNewWalletPrimaryText": "创建新钱包？", "seedlessCreateNewWalletSecondaryText": "此电子邮件地址没有钱包，您想创建一个吗？", "seedlessCreateNewWalletButtonText": "创建钱包", "seedlessCreateNewWalletNoBundlePrimaryText": "找不到钱包", "seedlessCreateNewWalletNoBundleSecondaryText": "此电子邮件地址没有钱包", "seedlessCreateNewWalletNoBundleButtonText": "返回", "seedlessEmailOptionsPrimaryText": "选择您的电子邮件地址", "seedlessEmailOptionsSecondaryText": "使用 Apple 账户或者 Google 账号添加钱包", "seedlessEmailOptionsButtonText": "继续使用电子邮件地址", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "使用您的 Apple ID 创建钱包", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "使用您的 Google 账号创建钱包", "seedlessAlreadyExistsPrimaryText": "账户已存在", "seedlessAlreadyExistsSecondaryText": "此电子邮件地址已创建过钱包，您是否想要登录？", "seedlessSignUpWithAppleButtonText": "使用 Apple 注册", "seedlessContinueWithAppleButtonText": "继续使用 Apple", "seedlessSignUpWithGoogleButtonText": "使用 Google 注册", "seedlessContinueWithGoogleButtonText": "继续使用 Google", "seedlessCreateAPinPrimaryText": "创建 PIN 码", "seedlessCreateAPinSecondaryText": "这用于在您所有的设备上保护您的钱包。<1>无法恢复。</1>", "seedlessContinueText": "继续", "seedlessConfirmPinPrimaryText": "确认您的 PIN 码", "seedlessConfirmPinSecondaryText": "如果您忘记此 PIN 码，您将无法在新设备上恢复您的钱包。", "seedlessConfirmPinButtonText": "创建 PIN 码", "seedlessConfirmPinError": "PIN 码不正确。请重试", "seedlessAccountsImportedPrimaryText": "账户已导入", "seedlessAccountsImportedSecondaryText": "这些账户将被自动导入您的钱包", "seedlessPreviouslyImportedTag": "之前导入", "seedlessEnterPinPrimaryText": "输入您的 PIN 码", "seedlessEnterPinInvalidPinError": "输入的 PIN 码不正确。仅允许 4 位数字", "seedlessEnterPinNumTriesLeft": "剩余 {{numTries}} 次尝试机会。", "seedlessEnterPinCooldown": "在 {{minutesLeft}}:{{secondsLeft}} 后重试", "seedlessEnterPinIncorrectLength": "PIN 码必须恰好为 4 个数字", "seedlessEnterPinMatch": "PIN 码匹配", "seedlessDoneText": "完成", "seedlessEnterPinToSign": "输入您的 PIN 码以签署此交易", "seedlessSigning": "签署", "seedlessCreateSeed": "创建种子短语钱包", "seedlessImportOptions": "其他导入选项", "seedlessImportPrimaryText": "导入选项", "seedlessImportSecondaryText": "使用您的种子短语、私钥或硬件钱包导入现有钱包", "seedlessImportSeedPhrase": "导入种子短语", "seedlessImportPrivateKey": "导入私钥", "seedlessConnectHardwareWallet": "关联硬件钱包", "seedlessTryAgain": "重试", "seedlessCreatingWalletPrimaryText": "正在创建钱包", "seedlessCreatingWalletSecondaryText": "正在添加社交钱包", "seedlessLoadingWalletPrimaryText": "正在加载钱包", "seedlessLoadingWalletSecondaryText": "正在导入和监视您的关联钱包", "seedlessLoadingWalletErrorPrimaryText": "无法加载钱包", "seedlessCreatingWalletErrorPrimaryText": "无法创建钱包", "seedlessErrorSecondaryText": "请重试", "seedlessAuthAlreadyExistsErrorText": "提供的电子邮件地址已属于另一个 Phantom 账户", "seedlessAuthUnknownErrorText": "发生未知错误，请稍后再试", "seedlessAuthUnknownErrorTextRefresh": "发生未知错误，请稍后再试。刷新页面以重试。", "seedlessAuthErrorCloseWindow": "关闭窗口", "seedlessWalletExistsErrorPrimaryText": "您的设备上已经存在一个社交钱包", "seedlessWalletExistsErrorSecondaryText": "请返回或关闭此屏幕", "seedlessValueProp1PrimaryText": "无缝设置", "seedlessValueProp1SecondaryText": "使用 Google 账号或者 Apple 帐户创建钱包，并开始轻松探索 web3", "seedlessValueProp2PrimaryText": "增强的安全性", "seedlessValueProp2SecondaryText": "您的钱包安全、分散地跨多个因素存储", "seedlessValueProp3PrimaryText": "轻松恢复", "seedlessValueProp3SecondaryText": "使用您的 Google 账号或者 Apple 帐户和 4 位 PIN 码恢复钱包访问权限", "seedlessLoggingIn": "正在登录…", "seedlessSignUpOrLogin": "注册或登录", "seedlessContinueByEnteringYourEmail": "输入您的电子邮件地址以继续", "seedless": "无种子", "seed": "种子短语", "seedlessVerifyPinPrimaryText": "验证 PIN 码", "seedlessVerifyPinSecondaryText": "请输入您的 PIN 码以继续", "seedlessVerifyPinVerifyButtonText": "验证", "seedlessVerifyPinForgotButtonText": "忘记了 PIN 码？", "seedlessPinConfirmButtonText": "确认", "seedlessVerifyToastPrimaryText": "验证您的 PIN 码", "seedlessVerifyToastSecondaryText": "我们将不定期要求您验证 PIN 码，以加强您的记忆。如果您忘记 PIN 码，将无法找回钱包。", "seedlessVerifyToastSuccessText": "您的 PIN 码已验证！", "seedlessForgotPinPrimaryText": "使用其他设备重置 PIN 码", "seedlessForgotPinSecondaryText": "出于安全考虑，您只能在其他登录的设备中重置 PIN 码", "seedlessForgotPinInstruction1PrimaryText": "打开其他设备", "seedlessForgotPinInstruction1SecondaryText": "转到另一台使用您的电子邮件地址登录您的 Phantom 账户的设备", "seedlessForgotPinInstruction2PrimaryText": "转到“设置”", "seedlessForgotPinInstruction2SecondaryText": "在“设置”中，选择“安全性与隐私”，并选择“重置 PIN 码”", "seedlessForgotPinInstruction3PrimaryText": "设置您的新 PIN 码", "seedlessForgotPinInstruction3SecondaryText": "设置新 PIN 码后，您就可以在这台设备上登录您的钱包", "seedlessForgotPinButtonText": "我已完成这些操作步骤", "seedlessResetPinPrimaryText": "重置 PIN 码", "seedlessResetPinSecondaryText": "输入您会记住的新 PIN 码。此 PIN 码用于保护您所有设备上的钱包", "seedlessResetPinSuccessText": "您的 PIN 码已更新！", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "创建钱包，即表示您同意我们的<1>服务条款</1>和<5>隐私政策</5>", "pageNotFound": "找不到页面", "pageNotFoundDescription": "我们没有故意忽视您！此页面不存在，或者已被移动。", "webTokenPagesLegalDisclaimer": "提供的价格信息仅供参考，并不作为财务建议。市场数据由第三方提供，Phantom 对信息的准确性不做任何保证。", "signUpOrLogin": "注册或登录", "portalOnboardingAgreeToTermsOfServiceInterpolated": "创建账户，即表示您同意我们的<1>服务条款</1>和<5>隐私政策</5>", "feedNoActivity": "尚无记录", "followRequests": "关注请求", "following": "已关注", "followers": "关注者", "follower": "关注者", "joined": "已加入", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "无关注者", "noFollowing": "无已关注", "noUsersFound": "找不到用户", "viewProfile": "查看资料", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}