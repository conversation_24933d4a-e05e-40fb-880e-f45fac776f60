import{a as me}from"./chunk-X3ESGVCB.js";import{a as B,b as de,f as le,g as fe,i as Q,m as pe,w as F,x as he}from"./chunk-HPOS2V3B.js";import{a as De}from"./chunk-H3FFS4GT.js";import{G as se,o as ue}from"./chunk-WIQ4WVKX.js";import{a as W}from"./chunk-7X4NV6OJ.js";import{f as V,h as _,i as v,n as M}from"./chunk-3KENBVE7.js";_();M();_();M();var a=V(W());_();M();var y=V(W());_();M();var D;(function(e){e[e.NotStarted=0]="NotStarted",e[e.Running=1]="Running",e[e.Stopped=2]="Stopped"})(D||(D={}));var Re={type:"xstate.init"};function Ce(e){return e===void 0?[]:[].concat(e)}function H(e){return{type:"xstate.assign",assignment:e}}function ve(e,n){return typeof(e=typeof e=="string"&&n&&n[e]?n[e]:e)=="string"?{type:e}:typeof e=="function"?{type:e.name,exec:e}:e}function X(e){return function(n){return e===n}}function ke(e){return typeof e=="string"?{type:e}:e}function ge(e,n){return{value:e,context:n,actions:[],changed:!1,matches:X(e)}}function Y(e,n){n===void 0&&(n={});var t={config:e,_options:n,initialState:{value:e.initial,actions:Ce(e.states[e.initial].entry).map(function(o){return ve(o,n.actions)}),context:e.context,matches:X(e.initial)},transition:function(o,r){var i,c,u=typeof o=="string"?{value:o,context:e.context}:o,f=u.value,l=u.context,g=ke(r),p=e.states[f];if(p.on){var s=Ce(p.on[g.type]),x=function(h){if(h===void 0)return{value:ge(f,l)};var b=typeof h=="string"?{target:h}:h,O=b.target,R=O===void 0?f:O,U=b.actions,K=U===void 0?[]:U,ie=b.cond,P=l;if((ie===void 0?function(){return!0}:ie)(l,g)){var Te=e.states[R],ce=!1,ae=[].concat(p.exit,K,Te.entry).filter(function(S){return S}).map(function(S){return ve(S,t._options.actions)}).filter(function(S){if(S.type==="xstate.assign"){ce=!0;var J=Object.assign({},P);return typeof S.assignment=="function"?J=S.assignment(P,g):Object.keys(S.assignment).forEach(function(G){J[G]=typeof S.assignment[G]=="function"?S.assignment[G](P,g):S.assignment[G]}),P=J,!1}return!0});return{value:{value:R,context:P,actions:ae,changed:R!==f||ae.length>0||ce,matches:X(R)}}}};try{for(var C=function(h){var b=typeof Symbol=="function"&&h[Symbol.iterator],O=0;return b?b.call(h):{next:function(){return h&&O>=h.length&&(h=void 0),{value:h&&h[O++],done:!h}}}}(s),k=C.next();!k.done;k=C.next()){var N=x(k.value);if(typeof N=="object")return N.value}}catch(h){i={error:h}}finally{try{k&&!k.done&&(c=C.return)&&c.call(C)}finally{if(i)throw i.error}}}return ge(f,l)}};return t}var xe=function(e,n){return e.actions.forEach(function(t){var o=t.exec;return o&&o(e.context,n)})};function Z(e){var n=e.initialState,t=D.NotStarted,o=new Set,r={_machine:e,send:function(i){t===D.Running&&(n=e.transition(n,i),xe(n,ke(i)),o.forEach(function(c){return c(n)}))},subscribe:function(i){return o.add(i),i(n),{unsubscribe:function(){return o.delete(i)}}},start:function(){return t=D.Running,xe(n,Re),r},stop:function(){return t=D.Stopped,o.clear(),r},get state(){return n},get status(){return t}};return r}_();M();var be=V(W());function ee(e){var n=(0,be.useRef)();return n.current||(n.current={v:e()}),n.current.v}function L(){return L=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},L.apply(this,arguments)}var Fe=function(n){var t;return n.subscribe(function(o){t=o}).unsubscribe(),t};function ye(e,n,t){var o=(0,y.useRef)(e),r=ee(function(){return Z(o.current).start()}),i=(0,y.useRef)(null),c=(0,y.useState)(function(){return Fe(r)}),u=c[0],f=c[1],l=(0,y.useCallback)(function(p){var s=fe(p)?{type:p}:p,x=Pe(n);r.send(L({},s,{lastEventType:i.current,refs:x})),i.current=s.type,v.NODE_ENV!=="production"&&t&&(console.group("Event Sent"),console.log("Event:",s),console.groupEnd())},[t]);(0,y.useEffect)(function(){return r.subscribe(function(s){s.changed&&f(s)}),function(){r.stop()}},[r]),(0,y.useEffect)(function(){v.NODE_ENV!=="production"&&t&&u.changed&&(console.group("State Updated"),console.log("State:",u),console.groupEnd())},[t,u]);var g=(0,y.useMemo)(function(){return L({},u,{matches:function(s){return s===u.value}})},[u.changed,u.context,u.value]);return[g,l,r]}function Pe(e){return Object.entries(e).reduce(function(n,t){var o=t[0],r=t[1];return n[o]=r.current,n},{})}function Se(e,n){return ee(function(){return Y(e,n)})}var m=V(De());function E(){return E=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var o in t)Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o])}return e},E.apply(this,arguments)}function q(e,n){if(e==null)return{};var t={},o=Object.keys(e),r,i;for(i=0;i<o.length;i++)r=o[i],!(n.indexOf(r)>=0)&&(t[r]=e[r]);return t}var Ve=["as","checked","defaultChecked","disabled","onChange"],$,$e=!1,d;(function(e){e.Checked="checked",e.Mixed="mixed",e.Unchecked="unchecked"})(d||(d={}));var w;(function(e){e.GetDerivedData="GET_DERIVED_DATA",e.Mount="MOUNT",e.Set="SET",e.Toggle="TOGGLE",e.Unmount="UNMOUNT"})(w||(w={}));function ne(e){return!!(e&&!e.isControlled&&!e.disabled)}function te(e){return function(n,t){return n&&n.isControlled&&t.state===e}}var I=H(function(e,n){return E({},e,{refs:n.refs})}),oe=($={},$[w.Mount]={actions:I},$[w.GetDerivedData]={actions:[I,H(function(e,n){return E({},e,n.data)})]},$[w.Set]=[{target:d.Checked,cond:te(d.Checked)},{target:d.Unchecked,cond:te(d.Unchecked)},{target:d.Mixed,cond:te(d.Mixed)}],$),Ie=function(n,t){var o,r,i,c;return{id:"mixed-checkbox",initial:n,context:{disabled:t.disabled,isControlled:t.isControlled,refs:{input:null}},states:(c={},c[d.Unchecked]={entry:I,on:E((o={},o[w.Toggle]={target:d.Checked,cond:ne},o),oe)},c[d.Checked]={entry:I,on:E((r={},r[w.Toggle]={target:d.Unchecked,cond:ne},r),oe)},c[d.Mixed]={entry:I,on:E((i={},i[w.Toggle]={target:d.Checked,cond:ne},i),oe)},c)}},Ee=(0,a.forwardRef)(function(n,t){var o=n.as,r=o===void 0?"input":o,i=n.checked,c=n.defaultChecked,u=n.disabled,f=n.onChange,l=q(n,Ve),g=(0,a.useRef)(null),p=Q(t,g),s=Me(g,{onChange:f,checked:i,defaultChecked:c,disabled:u},"MixedCheckbox"),x=s[0];return Ne(i,"checked","MixedCheckbox"),(0,a.createElement)(r,E({},l,x,{"data-reach-mixed-checkbox":"",ref:p}))});v.NODE_ENV!=="production"&&(Ee.displayName="MixedCheckbox",Ee.propTypes={checked:m.default.oneOfType([m.default.bool,m.default.oneOf(["mixed"])]),onChange:m.default.func});function Me(e,n,t){t===void 0&&(t="useMixedCheckbox");var o=n||{},r=o.checked,i=o.defaultChecked,c=o.disabled,u=o.onChange,f=o.onClick,l=r!=null,g=Se(Ie(re(l?r:i),{disabled:!!c,isControlled:l})),p=ye(g,{input:e},$e),s=p[0],x=p[1],C={"aria-checked":we(s.value),checked:je(s.value),disabled:!!c,onChange:F(u,N),onClick:F(f,h),type:"checkbox"},k={checked:we(s.value)};function N(){l||x(w.Toggle)}function h(){b()}function b(){e.current&&(e.current.indeterminate=s.value===d.Mixed)}return Ae(e,"A ref was not assigned to an input element in "+t+"."),(0,a.useEffect)(function(){l&&x({type:w.Set,state:re(r)})},[l,r,x]),de(b),(0,a.useEffect)(function(){x({type:w.GetDerivedData,data:{disabled:c,isControlled:l}})},[c,l,x]),[C,k]}function re(e){return e===!0?d.Checked:e==="mixed"?d.Mixed:d.Unchecked}function we(e){return e===d.Checked?!0:e===d.Mixed?"mixed":!1}function je(e){return e===d.Checked}function Ne(e,n,t){var o=e!=null,r=(0,a.useRef)(o),i=r.current;(0,a.useEffect)(function(){v.NODE_ENV!=="production"&&(v.NODE_ENV!=="production"&&B(!(!o&&i),t+" is changing from controlled to uncontrolled. "+t+" should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled "+t+" for the lifetime of the component. Check the `"+n+"` prop being passed in."),v.NODE_ENV!=="production"&&B(!(o&&!i),t+" is changing from uncontrolled to controlled. "+t+" should not switch from uncontrolled to controlled (or vice versa). Decide between using a controlled or uncontrolled "+t+" for the lifetime of the component. Check the `"+n+"` prop being passed in."))},[t,n,o,i])}function Ae(e,n){if(v.NODE_ENV!=="production"){var t=(0,a.useRef)(n);(0,a.useEffect)(function(){t.current=n},[n]),(0,a.useEffect)(function(){v.NODE_ENV!=="production"&&B(e.current,t.current)},[e])}}var Ue=["as","checked","children","defaultChecked","disabled","onClick","onChange","__componentName"],Ge=["as","onBlur","onFocus"],We=["children","id","name","value"],Oe=pe("CustomCheckboxContext",{});function Be(){return(0,a.useContext)(Oe)}var j=(0,a.forwardRef)(function(n,t){var o=n.as,r=o===void 0?"span":o,i=n.checked,c=n.children,u=n.defaultChecked,f=n.disabled,l=n.onClick,g=n.onChange,p=n.__componentName,s=p===void 0?"CustomCheckboxContainer":p,x=q(n,Ue),C=(0,a.useRef)(null),k=Me(C,{defaultChecked:u,checked:i,disabled:f,onChange:g},s),N=k[0],h=k[1],b=(0,a.useState)(!1),O=b[0],R=b[1];function U(){self.requestAnimationFrame(function(){C.current&&C.current.focus()})}var K={defaultChecked:u,disabled:f,focused:O,inputProps:N,inputRef:C,setFocused:R};return Ne(i,"checked",s),he("checkbox"),(0,a.createElement)(Oe.Provider,{value:K},(0,a.createElement)(r,E({},x,{ref:t,"data-reach-custom-checkbox-container":"","data-focused":O?"":void 0,"data-state":re(h.checked),onClick:F(l,U)}),le(c)?c({checked:N["aria-checked"],inputRef:C,focused:O}):c))});v.NODE_ENV!=="production"&&(j.displayName="CustomCheckboxContainer",j.propTypes={checked:m.default.oneOfType([m.default.bool,m.default.oneOf(["mixed"])]),defaultChecked:m.default.bool,disabled:m.default.bool,onChange:m.default.func});var A=(0,a.forwardRef)(function(n,t){var o=n.as,r=o===void 0?"input":o,i=n.onBlur,c=n.onFocus,u=q(n,Ge),f=Be(),l=f.focused,g=f.inputProps,p=f.inputRef,s=f.setFocused,x=Q(t,p),C=(0,a.useRef)(!0);function k(){self.requestAnimationFrame(function(){C.current&&s(!1)})}function N(){self.requestAnimationFrame(function(){C.current&&s(!0)})}return(0,a.useEffect)(function(){return function(){return void(C.current=!1)}},[]),(0,a.createElement)(r,E({},u,g,{ref:x,type:"checkbox","data-reach-custom-checkbox-input":"","data-focused":l?"":void 0,onBlur:F(i,k),onFocus:F(c,N)}))});v.NODE_ENV!=="production"&&(A.displayName="CustomCheckboxInput",A.propTypes={});var _e=(0,a.forwardRef)(function(n,t){var o=n.children,r=n.id,i=n.name,c=n.value,u=q(n,We);return(0,a.createElement)(j,E({},u,{"data-reach-custom-checkbox":"",__componentName:"CustomCheckbox"}),(0,a.createElement)(A,{id:r,name:i,ref:t,value:c}),o)});v.NODE_ENV!=="production"&&(_e.displayName="CustomCheckbox",_e.propTypes={checked:m.default.oneOfType([m.default.bool,m.default.oneOf(["mixed"])]),disabled:m.default.bool,name:m.default.string,onChange:m.default.func,value:m.default.string});var T=V(W());var z=22;function He(e,n="#AB9FF2"){return(0,T.useMemo)(()=>({background:"#282828",border:`1px solid ${e?n:"#4d4d4d"}`,borderRadius:"6px",height:`${z}px`,minHeight:`${z}px`,width:`${z}px`,minWidth:`${z}px`,cursor:"pointer",userSelect:"none",position:"relative",outlineColor:"transparent",outlineStyle:"none",boxShadow:"none"}),[e,n])}function Le(e,n="#AB9FF2"){return(0,T.useMemo)(()=>({display:"flex",alignItems:"center",justifyContent:"center",position:"absolute",width:"100%",height:"100%",userSelect:"none",pointerEvents:"none",borderRadius:"4px",opacity:`${e?1:0}`,zIndex:1,background:e?n:"transparent"}),[e,n])}var En=ue(me)`
  margin: ${e=>e.margin??0};
  > * {
    margin-right: 10px;
  }

  label {
    color: #ab9ff2;
    cursor: pointer;

    &:hover {
      color: #e2dffe;
    }
  }
`,wn=e=>{let{checked:n,onChange:t,color:o,checkColor:r,...i}=e,c=He(n,o),u=Le(n,o);return T.default.createElement(j,{checked:n,onChange:t,style:c},T.default.createElement(A,{...i}),T.default.createElement("span",{"aria-hidden":!0,style:u},T.default.createElement(se,{width:"0.9em",fill:r||"#222"})))};export{H as a,ye as b,Se as c,En as d,wn as e};
/*! Bundled license information:

@xstate/fsm/es/index.js:
  (*! *****************************************************************************
  Copyright (c) Microsoft Corporation. All rights reserved.
  Licensed under the Apache License, Version 2.0 (the "License"); you may not use
  this file except in compliance with the License. You may obtain a copy of the
  License at http://www.apache.org/licenses/LICENSE-2.0
  
  THIS CODE IS PROVIDED ON AN *AS IS* BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, EITHER EXPRESS OR IMPLIED, INCLUDING WITHOUT LIMITATION ANY IMPLIED
  WARRANTIES OR CONDITIONS OF TITLE, FITNESS FOR A PARTICULAR PURPOSE,
  MERCHANTABLITY OR NON-INFRINGEMENT.
  
  See the Apache Version 2.0 License for specific language governing permissions
  and limitations under the License.
  ***************************************************************************** *)
*/
//# sourceMappingURL=chunk-CCUXU2GU.js.map
