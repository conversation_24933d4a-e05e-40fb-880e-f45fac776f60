#!/usr/bin/env node

const { Cluster } = require('ioredis');
const fs = require('fs');
const path = require('path');

async function main() {
  const key = 'verified_tokens';

  const client = new Cluster([{ host: 'dex3-golive-redis-metric.saheuh.clustercfg.use2.cache.amazonaws.com', port: 6379 }]);

  const closeClient = async () => {
    try {
      if (client.isOpen) await client.quit();
    } catch (e) {
      // ignore on shutdown
    }
  };

  process.on('SIGINT', async () => {
    await closeClient();
    process.exit(1);
  });
  process.on('SIGTERM', async () => {
    await closeClient();
    process.exit(1);
  });

  try {

    const value = await client.get(key);
    if (value == null) {
      console.log(`Key not found: ${key}`);
      return;
    }

    let outputData;
    try {
      outputData = JSON.parse(value);
    } catch {
      outputData = { value };
    }

    const outputPath = path.join(__dirname, 'tokenList.json');
    fs.writeFileSync(outputPath, JSON.stringify(outputData, null, 2), 'utf8');
    console.log(`Saved to ${outputPath}`);
  } catch (error) {
    console.error('Failed to read from Redis:', error.message || error);
    process.exitCode = 1;
  } finally {
    await closeClient();
  }
}

main();
