import{l as c}from"./chunk-SMVAXKUF.js";import{z as g}from"./chunk-HPOS2V3B.js";import{a,c as e,d as r,e as p}from"./chunk-2NGYUYTC.js";import{o as m}from"./chunk-WIQ4WVKX.js";import{m as s}from"./chunk-56SJOU6P.js";import{B as l}from"./chunk-L3A2KHJO.js";import{a as b}from"./chunk-7X4NV6OJ.js";import{f as u,h as i,n}from"./chunk-3KENBVE7.js";i();n();var o=u(b());var k=m.a`
  transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  transition-duration: 250ms;
  transition-property: color;
  &:hover {
    svg {
      opacity: 0.8;
    }
  }
`,C=()=>{let{t}=s(),{downloadLogs:d,goSupportDesk:f}=c();return o.default.createElement(r,{direction:"row",justifyContent:"space-between",alignItems:"center",className:a({position:"absolute",top:0,right:0,left:0,paddingY:32,paddingX:40})},o.default.createElement(k,{href:l,target:"_blank",rel:"noopener noreferrer"},o.default.createElement(e.LogoFill,{size:36,color:"bgWallet"})),o.default.createElement(g,{items:[{label:t("settingsSupportDesk"),key:t("settingsSupportDesk"),onClick:f},{label:t("settingsDownloadApplicationLogs"),key:t("settingsDownloadApplicationLogs"),onClick:d}]},o.default.createElement(r,{direction:"row",alignItems:"center",gap:6,cursor:"pointer"},o.default.createElement(e.HelpCircle,{size:20,color:"accentPrimaryLight",fill:"bgWallet"}),o.default.createElement(p,{children:t("fullPageHeaderHelp"),font:"bodyMedium",color:"bgWallet"}))))};export{C as a};
//# sourceMappingURL=chunk-2JNGRO7L.js.map
