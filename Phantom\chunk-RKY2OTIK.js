import{a as N,b as P,c as re,d as C,e as H}from"./chunk-W3ZRPNOX.js";import{a as te}from"./chunk-2WECCVZD.js";import{Ma as Q}from"./chunk-JD6NH5K6.js";import{a as _}from"./chunk-QEXGR5WT.js";import{a as $,c as ee,e as oe}from"./chunk-P5LBFEHG.js";import{a as d,c as E,d as D,e as G}from"./chunk-2NGYUYTC.js";import{D as q,Ia as K,Ja as U,ga as J,o as L,rb as Z}from"./chunk-WIQ4WVKX.js";import{Fa as X,fb as Y,ja as A,ka as j,va as T}from"./chunk-SD2LXVLD.js";import{a as i,m as b}from"./chunk-56SJOU6P.js";import{$ as M,e as z}from"./chunk-ALUTR72U.js";import{a as I}from"./chunk-7X4NV6OJ.js";import{f as h,h as s,n as p}from"./chunk-3KENBVE7.js";s();p();var a=h(I());s();p();var l=h(I());s();p();var u=h(I());var ne={labelWrapper:d({display:"flex",flexDirection:"row",alignItems:"center",cursor:"pointer"}),iconWrapper:d({position:"relative",top:1,marginLeft:6})},ge=u.default.memo(({label:e,uiRefuelAmount:o,isSwapReview:t,refuelEnabled:r,setRefuelEnabled:n,onShowRefuelModal:f})=>u.default.createElement(P,{label:t?e:u.default.createElement("span",{className:ne.labelWrapper,onClick:f},e,u.default.createElement("span",{className:ne.iconWrapper},u.default.createElement(J,{fill:"#777",width:12})))},t?u.default.createElement(C,null,`+${o}`):u.default.createElement(te,{id:"refuel-toggle",checked:r,onChange:w=>n(w.target.checked)}))),W=u.default.memo(({isSwapReview:e})=>{let o=T(),{handleShowModalVisibility:t}=Q(),r=(0,u.useCallback)(()=>t("bridgeRefuel"),[t]);return u.default.createElement(ge,{...o,isSwapReview:e,onShowRefuelModal:r})});var Se=L.div`
  margin: 10px 0 0;
  width: 100%;
`,ie=L.div`
  display: flex;
  flex-direction: row;
  margin: 10px 0 20px;
  padding: 16px;
  width: 100%;
  background-color: ${e=>e.boxColor};
  border-radius: 8px;
`,ae=L(Z).attrs({size:15,weight:400,color:"#222",lineheight:20,textAlign:"left",margin:"-3px 0 0 10px"})``,ye=l.default.memo(({retryString:e,message:o,errorCode:t,displayRefuelRow:r,onRetry:n})=>t==="InsufficientSellAmount"?l.default.createElement(ie,{boxColor:"#eb3742"},l.default.createElement(D,{flexShrink:0},l.default.createElement(K,{width:25,fill:"#222222"})),l.default.createElement(ae,null,o)):l.default.createElement(l.default.Fragment,null,r?l.default.createElement(Se,null,l.default.createElement(N,{roundedBottom:!0,roundedTop:!0},l.default.createElement(W,null))):null,l.default.createElement(ie,{boxColor:"#ffdc62"},l.default.createElement(D,{flexShrink:0},l.default.createElement(U,{fill:"#222222"})),l.default.createElement(ae,null,o+" ",l.default.createElement("span",{style:{textDecoration:"underline"},onClick:n},e))))),se=l.default.memo(({message:e,errorCode:o,onRetry:t})=>{let{t:r}=b(),{refuelSupported:n}=T(),f=r("swapQuestionRetry");return l.default.createElement(ye,{retryString:f,message:e,errorCode:o,displayRefuelRow:n,onRetry:t})});s();p();var v=h(I());s();p();var m=h(I());var pe={disclaimerText:d({textAlign:"left"}),secondaryTooltipText:d({textAlign:"left",marginLeft:4})},le=({networkFee:e,tokenAccountFee:o,bridgeFee:t,phantomFeePercent:r,destinationNetworkFee:n,liquidityProviderFee:f})=>m.default.createElement(m.default.Fragment,null,m.default.createElement(V,{...e}),m.default.createElement(V,{...t}),m.default.createElement(V,{...o}),m.default.createElement(V,{...n}),m.default.createElement(V,{...f}),r?.value&&m.default.createElement(G,{className:pe.disclaimerText,font:"captionItalic",color:"textTertiary",children:r.label})),V=({label:e,maxValue:o,value:t})=>{let{t:r}=b();return t?m.default.createElement(ee,null,m.default.createElement($,null,e,o&&m.default.createElement(G,{className:pe.secondaryTooltipText,color:"textTertiary",font:"captionItalic",children:` ${r("swapperMax")} \xB7 ${o}`})),m.default.createElement($,null,t)):null};s();p();var F=h(I());var O={row:d({display:"flex",flexDirection:"row",minWidth:0}),iconContainer:d({position:"relative",top:1,marginLeft:4})},fe=({label:e,tooltipContent:o,isLoading:t,value:r,disabled:n,onClick:f,accessoryIcon:w})=>{let g=w?F.default.createElement("span",null,w==="chevron-right"?F.default.createElement(E.ChevronRight,{className:O.iconContainer,color:"textSecondary",size:16}):F.default.createElement(E.Swap,{className:O.iconContainer,color:"textSecondary",size:16})):null;return F.default.createElement(P,{label:e,tooltipContent:o,isLoading:t,onClick:n?void 0:f},F.default.createElement("div",{className:O.row},F.default.createElement(C,null,r),n?null:g))};s();p();var c=h(I());var we=({label:e,value:o,tooltipContent:t,onClick:r})=>{let[n,f]=(0,c.useState)(o),[w,g]=(0,c.useState)(!1),S=(0,c.useRef)(!1);(0,c.useEffect)(()=>{if(o===n)return;if(S.current){S.current=!1,f(o);return}g(!0);let y=setTimeout(()=>{let x=setTimeout(()=>{g(!1),f(o)},150);return()=>clearTimeout(x)},150);return()=>clearTimeout(y)},[n,o]);let k=(0,c.useMemo)(()=>w?c.default.createElement(_,{width:"75px",height:"15px",borderRadius:"50px",backgroundColor:"#434343"}):c.default.createElement(C,{onClick:()=>{r&&(S.current=!0,r())}},n),[w,r,n]);return c.default.createElement(D,{direction:"row",gap:16,backgroundColor:"bgRow",padding:16,alignItems:"center",justifyContent:"space-between"},c.default.createElement(oe,{tooltipAlignment:"topLeft",iconSize:12,lineHeight:17,fontWeight:400,info:t?c.default.createElement(re,{tooltipContent:t}):null,textColor:"#FFF"},e),k)};s();p();var ce=e=>{switch(e.type){case"provider":return{type:"generic",label:i.t("swapFeesProvider"),isLoading:e.isFetchingQuote,value:e.provider,tooltipContent:i.t("swapFeesProviderDisclaimer")};case"providerPressable":return{type:"generic",label:i.t("swapFeesProvider"),isLoading:e.isFetchingQuote,value:e.provider,accessoryIcon:"chevron-right",disabled:!e.selectProviderEnabled,onClick:e.showProviders,tooltipContent:i.t("swapFeesProviderDisclaimer")};case"exchangeRate":return{type:"generic",label:i.t("swapFeesRate"),tooltipContent:i.t("swapFeesRateDisclaimerMultichain"),isLoading:e.isFetchingQuote,value:e.exchangeRate.rate,onClick:e.exchangeRate.flip,accessoryIcon:"swap"};case"slippage":return{type:"generic",isLoading:e.isFetchingQuote,label:i.t("swapFeesSlippage"),value:e.slippage?.text,tooltipContent:i.t("swapFeesHighSlippageDisclaimer",{slippage:e.slippage?.percentage})};case"priceImpact":return{type:"generic",isLoading:e.isFetchingQuote,value:e.priceImpact?.text,label:i.t("swapFeesPriceImpact"),tooltipContent:i.t("swapFeesPriceImpactDisclaimer")};case"network":return{type:"generic",isLoading:e.isFetchingQuote,label:i.t("nounNetwork"),value:`${e.sellNetwork} \u2794 ${e.buyNetwork}`};case"executionDuration":return{type:"generic",isLoading:e.isFetchingQuote,label:i.t("swapEstimatedTimeShort"),value:e.uiEstimatedTime,tooltipContent:i.t("swapEstimatedTimeDisclaimer")};case"fees":return{type:"generic",label:i.t("swapFeesFees"),isLoading:e.isFetchingQuote||e.fees.isFetchingFees,value:e.fees.totalFees.uiAmount};case"highPriceImpact":return{type:"warning",isLoading:e.isFetchingQuote,value:e.priceImpact?.text,label:i.t("swapFeesHighPriceImpact"),tooltipContent:i.t("swapFeesPriceImpactDisclaimer")};case"highSlippage":return{type:"warning",isLoading:e.isFetchingQuote,value:e.slippage?.text,label:i.t("swapFeesHighSlippage"),tooltipContent:i.t("swapFeesHighSlippageDisclaimer",{slippage:e.slippage?.percentage})};case"transferFees":return{type:"warning",isLoading:e.isFetchingQuote,value:`${e.transferFees.totalPercentFees}%`,label:i.t("swapTransferFee"),tooltipContent:e.transferFees.fees.length>1?i.t("swapTransferFeeDisclaimerMany",{feePercent:e.transferFees.totalPercentFees}):i.t("swapTransferFeeDisclaimer",{symbol:e.transferFees.fees[0].symbol,feePercent:e.transferFees.fees[0].percentFee})};case"refuel":return{type:"refuel",isSwapReview:e.isSwapReview}}};s();p();var B=h(I());var me=({label:e,tooltipContent:o,isLoading:t,value:r})=>B.default.createElement(P,{label:e,icon:B.default.createElement(q,{width:"14",height:"14",fill:"#222"}),tooltipContent:o,customBackground:"#FFDC62",color:"#222",isLoading:t},B.default.createElement(C,{color:"#222"},r));var ue=e=>{let o=ce(e);switch(o?.type){case"generic":let t=o,r=e.type==="fees"?e.feeBreakdown&&v.default.createElement(le,{...e.feeBreakdown}):t.tooltipContent;return e.rowDisplayStrategy===0?v.default.createElement(fe,{tooltipContent:r,...o}):v.default.createElement(we,{tooltipContent:r,...o});case"warning":return v.default.createElement(me,{...o});case"refuel":return v.default.createElement(W,{isSwapReview:e.isSwapReview});default:return null}};var Ie=e=>{let{t:o}=b(),{handleShowModalVisibility:t}=Q(),r=(0,a.useCallback)(()=>{t("swapProviders")},[t]),n=Y({isSwapReview:e,showProviders:r}),{exchangeRate:f,fees:w}=n,g=f.rate?f.isBestRate?"#21E56F":"#fff":"",S=X(w),{showRefuelOption:k,refuelAmount:y}=T();return{...n,exchangeRateColor:g,feeBreakdown:S,isSwapReview:e,displayRefuelRow:k&&(!e||!!y),showSwapProvidersModal:r,t:o}},Pe=a.default.memo(({isSwapReview:e,fees:o,isFetchingQuote:t,rows:r,t:n,sharedRowProps:f,rowDisplayStrategy:w})=>{let g=!!o.phantomFee.pct,S=(0,a.useMemo)(()=>{switch(w){case 1:return e&&g;case 0:return e&&g&&!t}},[t,e,g,w]),k=r.map((y,x)=>a.default.createElement(ue,{key:`r-${x}`,...f,type:y,rowDisplayStrategy:w}));return a.default.createElement(a.default.Fragment,null,a.default.createElement(N,{roundedTop:!0,roundedBottom:!S},z(k,a.default.createElement(H,{gap:1})).map((y,x)=>a.default.createElement("div",{key:`r-${x}`},y))),S?a.default.createElement(N,{roundedBottom:!0},a.default.createElement(H,{gap:1}),a.default.createElement(P,{fontWeight:400,lineHeight:18,label:n("swapQuoteFeeDisclaimer",{feePercentage:M(o.phantomFee.pct)}),tooltipContent:n("swapFeesPhantomFeeDisclaimer",{feePercentage:M(o.phantomFee.pct)}),color:"#777"})):null)}),Ce=({isSwapReview:e,rowDisplayStrategy:o})=>{let t=Ie(e);return a.default.createElement(Pe,{...t,rowDisplayStrategy:o})},de={feeContainer:d({marginX:0,marginY:10,width:"100%"})},Eo=({isSwapReview:e,rowDisplayStrategy:o})=>{let t=A(w=>w.hasNoRoutes),r=A(w=>w.error),{clearQuotes:n}=j(),f=(0,a.useCallback)(()=>{n()},[n]);return r&&t?a.default.createElement("div",{className:de.feeContainer},a.default.createElement(se,{message:r.message,errorCode:r.code,onRetry:f})):a.default.createElement("div",{className:de.feeContainer},a.default.createElement(Ce,{isSwapReview:e,rowDisplayStrategy:o??0}))};export{Eo as a};
//# sourceMappingURL=chunk-RKY2OTIK.js.map
