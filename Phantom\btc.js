"use strict";(()=>{var Uk=Object.create;var yp=Object.defineProperty;var Dk=Object.getOwnPropertyDescriptor;var Fk=Object.getOwnPropertyNames;var jk=Object.getPrototypeOf,Hk=Object.prototype.hasOwnProperty;var O=(r,e)=>()=>(r&&(e=r(r=0)),e);var _e=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ue=(r,e)=>{for(var t in e)yp(r,t,{get:e[t],enumerable:!0})},Wk=(r,e,t,n)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of Fk(e))!Hk.call(r,o)&&o!==t&&yp(r,o,{get:()=>e[o],enumerable:!(n=Dk(e,o))||n.enumerable});return r};var Ke=(r,e,t)=>(t=r!=null?Uk(jk(r)):{},Wk(e||!r||!r.__esModule?yp(t,"default",{value:r,enumerable:!0}):t,r));var gp=_e(Pa=>{"use strict";y();Pa.byteLength=Vk;Pa.toByteArray=Zk;Pa.fromByteArray=Jk;var Gr=[],Rr=[],Kk=typeof Uint8Array<"u"?Uint8Array:Array,mp="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";for(qo=0,Md=mp.length;qo<Md;++qo)Gr[qo]=mp[qo],Rr[mp.charCodeAt(qo)]=qo;var qo,Md;Rr[45]=62;Rr[95]=63;function Ld(r){var e=r.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var t=r.indexOf("=");t===-1&&(t=e);var n=t===e?0:4-t%4;return[t,n]}function Vk(r){var e=Ld(r),t=e[0],n=e[1];return(t+n)*3/4-n}function Gk(r,e,t){return(e+t)*3/4-t}function Zk(r){var e,t=Ld(r),n=t[0],o=t[1],s=new Kk(Gk(r,n,o)),i=0,u=o>0?n-4:n,f;for(f=0;f<u;f+=4)e=Rr[r.charCodeAt(f)]<<18|Rr[r.charCodeAt(f+1)]<<12|Rr[r.charCodeAt(f+2)]<<6|Rr[r.charCodeAt(f+3)],s[i++]=e>>16&255,s[i++]=e>>8&255,s[i++]=e&255;return o===2&&(e=Rr[r.charCodeAt(f)]<<2|Rr[r.charCodeAt(f+1)]>>4,s[i++]=e&255),o===1&&(e=Rr[r.charCodeAt(f)]<<10|Rr[r.charCodeAt(f+1)]<<4|Rr[r.charCodeAt(f+2)]>>2,s[i++]=e>>8&255,s[i++]=e&255),s}function Yk(r){return Gr[r>>18&63]+Gr[r>>12&63]+Gr[r>>6&63]+Gr[r&63]}function $k(r,e,t){for(var n,o=[],s=e;s<t;s+=3)n=(r[s]<<16&16711680)+(r[s+1]<<8&65280)+(r[s+2]&255),o.push(Yk(n));return o.join("")}function Jk(r){for(var e,t=r.length,n=t%3,o=[],s=16383,i=0,u=t-n;i<u;i+=s)o.push($k(r,i,i+s>u?u:i+s));return n===1?(e=r[t-1],o.push(Gr[e>>2]+Gr[e<<4&63]+"==")):n===2&&(e=(r[t-2]<<8)+r[t-1],o.push(Gr[e>>10]+Gr[e>>4&63]+Gr[e<<2&63]+"=")),o.join("")}});var wp=_e(xp=>{y();xp.read=function(r,e,t,n,o){var s,i,u=o*8-n-1,f=(1<<u)-1,g=f>>1,b=-7,E=t?o-1:0,q=t?-1:1,C=r[e+E];for(E+=q,s=C&(1<<-b)-1,C>>=-b,b+=u;b>0;s=s*256+r[e+E],E+=q,b-=8);for(i=s&(1<<-b)-1,s>>=-b,b+=n;b>0;i=i*256+r[e+E],E+=q,b-=8);if(s===0)s=1-g;else{if(s===f)return i?NaN:(C?-1:1)*(1/0);i=i+Math.pow(2,n),s=s-g}return(C?-1:1)*i*Math.pow(2,s-n)};xp.write=function(r,e,t,n,o,s){var i,u,f,g=s*8-o-1,b=(1<<g)-1,E=b>>1,q=o===23?Math.pow(2,-24)-Math.pow(2,-77):0,C=n?0:s-1,K=n?1:-1,ee=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,i=b):(i=Math.floor(Math.log(e)/Math.LN2),e*(f=Math.pow(2,-i))<1&&(i--,f*=2),i+E>=1?e+=q/f:e+=q*Math.pow(2,1-E),e*f>=2&&(i++,f/=2),i+E>=b?(u=0,i=b):i+E>=1?(u=(e*f-1)*Math.pow(2,o),i=i+E):(u=e*Math.pow(2,E-1)*Math.pow(2,o),i=0));o>=8;r[t+C]=u&255,C+=K,u/=256,o-=8);for(i=i<<o|u,g+=o;g>0;r[t+C]=i&255,C+=K,i/=256,g-=8);r[t+C-K]|=ee*128}});var vs=_e(bs=>{"use strict";y();var bp=gp(),xs=wp(),Nd=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;bs.Buffer=Z;bs.SlowBuffer=nA;bs.INSPECT_MAX_BYTES=50;var Ta=2147483647;bs.kMaxLength=Ta;Z.TYPED_ARRAY_SUPPORT=Qk();!Z.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function Qk(){try{let r=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(r,e),r.foo()===42}catch{return!1}}Object.defineProperty(Z.prototype,"parent",{enumerable:!0,get:function(){if(Z.isBuffer(this))return this.buffer}});Object.defineProperty(Z.prototype,"offset",{enumerable:!0,get:function(){if(Z.isBuffer(this))return this.byteOffset}});function hn(r){if(r>Ta)throw new RangeError('The value "'+r+'" is invalid for option "size"');let e=new Uint8Array(r);return Object.setPrototypeOf(e,Z.prototype),e}function Z(r,e,t){if(typeof r=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return _p(r)}return Ud(r,e,t)}Z.poolSize=8192;function Ud(r,e,t){if(typeof r=="string")return eA(r,e);if(ArrayBuffer.isView(r))return tA(r);if(r==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r);if(Zr(r,ArrayBuffer)||r&&Zr(r.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(Zr(r,SharedArrayBuffer)||r&&Zr(r.buffer,SharedArrayBuffer)))return Sp(r,e,t);if(typeof r=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();if(n!=null&&n!==r)return Z.from(n,e,t);let o=rA(r);if(o)return o;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]=="function")return Z.from(r[Symbol.toPrimitive]("string"),e,t);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r)}Z.from=function(r,e,t){return Ud(r,e,t)};Object.setPrototypeOf(Z.prototype,Uint8Array.prototype);Object.setPrototypeOf(Z,Uint8Array);function Dd(r){if(typeof r!="number")throw new TypeError('"size" argument must be of type number');if(r<0)throw new RangeError('The value "'+r+'" is invalid for option "size"')}function Xk(r,e,t){return Dd(r),r<=0?hn(r):e!==void 0?typeof t=="string"?hn(r).fill(e,t):hn(r).fill(e):hn(r)}Z.alloc=function(r,e,t){return Xk(r,e,t)};function _p(r){return Dd(r),hn(r<0?0:kp(r)|0)}Z.allocUnsafe=function(r){return _p(r)};Z.allocUnsafeSlow=function(r){return _p(r)};function eA(r,e){if((typeof e!="string"||e==="")&&(e="utf8"),!Z.isEncoding(e))throw new TypeError("Unknown encoding: "+e);let t=Fd(r,e)|0,n=hn(t),o=n.write(r,e);return o!==t&&(n=n.slice(0,o)),n}function vp(r){let e=r.length<0?0:kp(r.length)|0,t=hn(e);for(let n=0;n<e;n+=1)t[n]=r[n]&255;return t}function tA(r){if(Zr(r,Uint8Array)){let e=new Uint8Array(r);return Sp(e.buffer,e.byteOffset,e.byteLength)}return vp(r)}function Sp(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(r.byteLength<e+(t||0))throw new RangeError('"length" is outside of buffer bounds');let n;return e===void 0&&t===void 0?n=new Uint8Array(r):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(r,e,t),Object.setPrototypeOf(n,Z.prototype),n}function rA(r){if(Z.isBuffer(r)){let e=kp(r.length)|0,t=hn(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)return typeof r.length!="number"||Ip(r.length)?hn(0):vp(r);if(r.type==="Buffer"&&Array.isArray(r.data))return vp(r.data)}function kp(r){if(r>=Ta)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Ta.toString(16)+" bytes");return r|0}function nA(r){return+r!=r&&(r=0),Z.alloc(+r)}Z.isBuffer=function(e){return e!=null&&e._isBuffer===!0&&e!==Z.prototype};Z.compare=function(e,t){if(Zr(e,Uint8Array)&&(e=Z.from(e,e.offset,e.byteLength)),Zr(t,Uint8Array)&&(t=Z.from(t,t.offset,t.byteLength)),!Z.isBuffer(e)||!Z.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,o=t.length;for(let s=0,i=Math.min(n,o);s<i;++s)if(e[s]!==t[s]){n=e[s],o=t[s];break}return n<o?-1:o<n?1:0};Z.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};Z.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(e.length===0)return Z.alloc(0);let n;if(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;let o=Z.allocUnsafe(t),s=0;for(n=0;n<e.length;++n){let i=e[n];if(Zr(i,Uint8Array))s+i.length>o.length?(Z.isBuffer(i)||(i=Z.from(i)),i.copy(o,s)):Uint8Array.prototype.set.call(o,i,s);else if(Z.isBuffer(i))i.copy(o,s);else throw new TypeError('"list" argument must be an Array of Buffers');s+=i.length}return o};function Fd(r,e){if(Z.isBuffer(r))return r.length;if(ArrayBuffer.isView(r)||Zr(r,ArrayBuffer))return r.byteLength;if(typeof r!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof r);let t=r.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&t===0)return 0;let o=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":return Ep(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return t*2;case"hex":return t>>>1;case"base64":return $d(r).length;default:if(o)return n?-1:Ep(r).length;e=(""+e).toLowerCase(),o=!0}}Z.byteLength=Fd;function oA(r,e,t){let n=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=0)||(t>>>=0,e>>>=0,t<=e))return"";for(r||(r="utf8");;)switch(r){case"hex":return dA(this,e,t);case"utf8":case"utf-8":return Hd(this,e,t);case"ascii":return lA(this,e,t);case"latin1":case"binary":return hA(this,e,t);case"base64":return pA(this,e,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return yA(this,e,t);default:if(n)throw new TypeError("Unknown encoding: "+r);r=(r+"").toLowerCase(),n=!0}}Z.prototype._isBuffer=!0;function Uo(r,e,t){let n=r[e];r[e]=r[t],r[t]=n}Z.prototype.swap16=function(){let e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)Uo(this,t,t+1);return this};Z.prototype.swap32=function(){let e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)Uo(this,t,t+3),Uo(this,t+1,t+2);return this};Z.prototype.swap64=function(){let e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)Uo(this,t,t+7),Uo(this,t+1,t+6),Uo(this,t+2,t+5),Uo(this,t+3,t+4);return this};Z.prototype.toString=function(){let e=this.length;return e===0?"":arguments.length===0?Hd(this,0,e):oA.apply(this,arguments)};Z.prototype.toLocaleString=Z.prototype.toString;Z.prototype.equals=function(e){if(!Z.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:Z.compare(this,e)===0};Z.prototype.inspect=function(){let e="",t=bs.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"};Nd&&(Z.prototype[Nd]=Z.prototype.inspect);Z.prototype.compare=function(e,t,n,o,s){if(Zr(e,Uint8Array)&&(e=Z.from(e,e.offset,e.byteLength)),!Z.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=0),n===void 0&&(n=e?e.length:0),o===void 0&&(o=0),s===void 0&&(s=this.length),t<0||n>e.length||o<0||s>this.length)throw new RangeError("out of range index");if(o>=s&&t>=n)return 0;if(o>=s)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,o>>>=0,s>>>=0,this===e)return 0;let i=s-o,u=n-t,f=Math.min(i,u),g=this.slice(o,s),b=e.slice(t,n);for(let E=0;E<f;++E)if(g[E]!==b[E]){i=g[E],u=b[E];break}return i<u?-1:u<i?1:0};function jd(r,e,t,n,o){if(r.length===0)return-1;if(typeof t=="string"?(n=t,t=0):t>2147483647?t=2147483647:t<-2147483648&&(t=-2147483648),t=+t,Ip(t)&&(t=o?0:r.length-1),t<0&&(t=r.length+t),t>=r.length){if(o)return-1;t=r.length-1}else if(t<0)if(o)t=0;else return-1;if(typeof e=="string"&&(e=Z.from(e,n)),Z.isBuffer(e))return e.length===0?-1:Cd(r,e,t,n,o);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?o?Uint8Array.prototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.call(r,e,t):Cd(r,[e],t,n,o);throw new TypeError("val must be string, number or Buffer")}function Cd(r,e,t,n,o){let s=1,i=r.length,u=e.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(r.length<2||e.length<2)return-1;s=2,i/=2,u/=2,t/=2}function f(b,E){return s===1?b[E]:b.readUInt16BE(E*s)}let g;if(o){let b=-1;for(g=t;g<i;g++)if(f(r,g)===f(e,b===-1?0:g-b)){if(b===-1&&(b=g),g-b+1===u)return b*s}else b!==-1&&(g-=g-b),b=-1}else for(t+u>i&&(t=i-u),g=t;g>=0;g--){let b=!0;for(let E=0;E<u;E++)if(f(r,g+E)!==f(e,E)){b=!1;break}if(b)return g}return-1}Z.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1};Z.prototype.indexOf=function(e,t,n){return jd(this,e,t,n,!0)};Z.prototype.lastIndexOf=function(e,t,n){return jd(this,e,t,n,!1)};function sA(r,e,t,n){t=Number(t)||0;let o=r.length-t;n?(n=Number(n),n>o&&(n=o)):n=o;let s=e.length;n>s/2&&(n=s/2);let i;for(i=0;i<n;++i){let u=parseInt(e.substr(i*2,2),16);if(Ip(u))return i;r[t+i]=u}return i}function iA(r,e,t,n){return za(Ep(e,r.length-t),r,t,n)}function aA(r,e,t,n){return za(wA(e),r,t,n)}function cA(r,e,t,n){return za($d(e),r,t,n)}function uA(r,e,t,n){return za(bA(e,r.length-t),r,t,n)}Z.prototype.write=function(e,t,n,o){if(t===void 0)o="utf8",n=this.length,t=0;else if(n===void 0&&typeof t=="string")o=t,n=this.length,t=0;else if(isFinite(t))t=t>>>0,isFinite(n)?(n=n>>>0,o===void 0&&(o="utf8")):(o=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let s=this.length-t;if((n===void 0||n>s)&&(n=s),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");let i=!1;for(;;)switch(o){case"hex":return sA(this,e,t,n);case"utf8":case"utf-8":return iA(this,e,t,n);case"ascii":case"latin1":case"binary":return aA(this,e,t,n);case"base64":return cA(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return uA(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),i=!0}};Z.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function pA(r,e,t){return e===0&&t===r.length?bp.fromByteArray(r):bp.fromByteArray(r.slice(e,t))}function Hd(r,e,t){t=Math.min(r.length,t);let n=[],o=e;for(;o<t;){let s=r[o],i=null,u=s>239?4:s>223?3:s>191?2:1;if(o+u<=t){let f,g,b,E;switch(u){case 1:s<128&&(i=s);break;case 2:f=r[o+1],(f&192)===128&&(E=(s&31)<<6|f&63,E>127&&(i=E));break;case 3:f=r[o+1],g=r[o+2],(f&192)===128&&(g&192)===128&&(E=(s&15)<<12|(f&63)<<6|g&63,E>2047&&(E<55296||E>57343)&&(i=E));break;case 4:f=r[o+1],g=r[o+2],b=r[o+3],(f&192)===128&&(g&192)===128&&(b&192)===128&&(E=(s&15)<<18|(f&63)<<12|(g&63)<<6|b&63,E>65535&&E<1114112&&(i=E))}}i===null?(i=65533,u=1):i>65535&&(i-=65536,n.push(i>>>10&1023|55296),i=56320|i&1023),n.push(i),o+=u}return fA(n)}var Od=4096;function fA(r){let e=r.length;if(e<=Od)return String.fromCharCode.apply(String,r);let t="",n=0;for(;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=Od));return t}function lA(r,e,t){let n="";t=Math.min(r.length,t);for(let o=e;o<t;++o)n+=String.fromCharCode(r[o]&127);return n}function hA(r,e,t){let n="";t=Math.min(r.length,t);for(let o=e;o<t;++o)n+=String.fromCharCode(r[o]);return n}function dA(r,e,t){let n=r.length;(!e||e<0)&&(e=0),(!t||t<0||t>n)&&(t=n);let o="";for(let s=e;s<t;++s)o+=vA[r[s]];return o}function yA(r,e,t){let n=r.slice(e,t),o="";for(let s=0;s<n.length-1;s+=2)o+=String.fromCharCode(n[s]+n[s+1]*256);return o}Z.prototype.slice=function(e,t){let n=this.length;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);let o=this.subarray(e,t);return Object.setPrototypeOf(o,Z.prototype),o};function Vt(r,e,t){if(r%1!==0||r<0)throw new RangeError("offset is not uint");if(r+e>t)throw new RangeError("Trying to access beyond buffer length")}Z.prototype.readUintLE=Z.prototype.readUIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||Vt(e,t,this.length);let o=this[e],s=1,i=0;for(;++i<t&&(s*=256);)o+=this[e+i]*s;return o};Z.prototype.readUintBE=Z.prototype.readUIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||Vt(e,t,this.length);let o=this[e+--t],s=1;for(;t>0&&(s*=256);)o+=this[e+--t]*s;return o};Z.prototype.readUint8=Z.prototype.readUInt8=function(e,t){return e=e>>>0,t||Vt(e,1,this.length),this[e]};Z.prototype.readUint16LE=Z.prototype.readUInt16LE=function(e,t){return e=e>>>0,t||Vt(e,2,this.length),this[e]|this[e+1]<<8};Z.prototype.readUint16BE=Z.prototype.readUInt16BE=function(e,t){return e=e>>>0,t||Vt(e,2,this.length),this[e]<<8|this[e+1]};Z.prototype.readUint32LE=Z.prototype.readUInt32LE=function(e,t){return e=e>>>0,t||Vt(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216};Z.prototype.readUint32BE=Z.prototype.readUInt32BE=function(e,t){return e=e>>>0,t||Vt(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])};Z.prototype.readBigUInt64LE=jn(function(e){e=e>>>0,ws(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&xi(e,this.length-8);let o=t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24,s=this[++e]+this[++e]*2**8+this[++e]*2**16+n*2**24;return BigInt(o)+(BigInt(s)<<BigInt(32))});Z.prototype.readBigUInt64BE=jn(function(e){e=e>>>0,ws(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&xi(e,this.length-8);let o=t*2**24+this[++e]*2**16+this[++e]*2**8+this[++e],s=this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n;return(BigInt(o)<<BigInt(32))+BigInt(s)});Z.prototype.readIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||Vt(e,t,this.length);let o=this[e],s=1,i=0;for(;++i<t&&(s*=256);)o+=this[e+i]*s;return s*=128,o>=s&&(o-=Math.pow(2,8*t)),o};Z.prototype.readIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||Vt(e,t,this.length);let o=t,s=1,i=this[e+--o];for(;o>0&&(s*=256);)i+=this[e+--o]*s;return s*=128,i>=s&&(i-=Math.pow(2,8*t)),i};Z.prototype.readInt8=function(e,t){return e=e>>>0,t||Vt(e,1,this.length),this[e]&128?(255-this[e]+1)*-1:this[e]};Z.prototype.readInt16LE=function(e,t){e=e>>>0,t||Vt(e,2,this.length);let n=this[e]|this[e+1]<<8;return n&32768?n|4294901760:n};Z.prototype.readInt16BE=function(e,t){e=e>>>0,t||Vt(e,2,this.length);let n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n};Z.prototype.readInt32LE=function(e,t){return e=e>>>0,t||Vt(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24};Z.prototype.readInt32BE=function(e,t){return e=e>>>0,t||Vt(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]};Z.prototype.readBigInt64LE=jn(function(e){e=e>>>0,ws(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&xi(e,this.length-8);let o=this[e+4]+this[e+5]*2**8+this[e+6]*2**16+(n<<24);return(BigInt(o)<<BigInt(32))+BigInt(t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24)});Z.prototype.readBigInt64BE=jn(function(e){e=e>>>0,ws(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&xi(e,this.length-8);let o=(t<<24)+this[++e]*2**16+this[++e]*2**8+this[++e];return(BigInt(o)<<BigInt(32))+BigInt(this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n)});Z.prototype.readFloatLE=function(e,t){return e=e>>>0,t||Vt(e,4,this.length),xs.read(this,e,!0,23,4)};Z.prototype.readFloatBE=function(e,t){return e=e>>>0,t||Vt(e,4,this.length),xs.read(this,e,!1,23,4)};Z.prototype.readDoubleLE=function(e,t){return e=e>>>0,t||Vt(e,8,this.length),xs.read(this,e,!0,52,8)};Z.prototype.readDoubleBE=function(e,t){return e=e>>>0,t||Vt(e,8,this.length),xs.read(this,e,!1,52,8)};function dr(r,e,t,n,o,s){if(!Z.isBuffer(r))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<s)throw new RangeError('"value" argument is out of bounds');if(t+n>r.length)throw new RangeError("Index out of range")}Z.prototype.writeUintLE=Z.prototype.writeUIntLE=function(e,t,n,o){if(e=+e,t=t>>>0,n=n>>>0,!o){let u=Math.pow(2,8*n)-1;dr(this,e,t,n,u,0)}let s=1,i=0;for(this[t]=e&255;++i<n&&(s*=256);)this[t+i]=e/s&255;return t+n};Z.prototype.writeUintBE=Z.prototype.writeUIntBE=function(e,t,n,o){if(e=+e,t=t>>>0,n=n>>>0,!o){let u=Math.pow(2,8*n)-1;dr(this,e,t,n,u,0)}let s=n-1,i=1;for(this[t+s]=e&255;--s>=0&&(i*=256);)this[t+s]=e/i&255;return t+n};Z.prototype.writeUint8=Z.prototype.writeUInt8=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,1,255,0),this[t]=e&255,t+1};Z.prototype.writeUint16LE=Z.prototype.writeUInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,2,65535,0),this[t]=e&255,this[t+1]=e>>>8,t+2};Z.prototype.writeUint16BE=Z.prototype.writeUInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2};Z.prototype.writeUint32LE=Z.prototype.writeUInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+4};Z.prototype.writeUint32BE=Z.prototype.writeUInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};function Wd(r,e,t,n,o){Yd(e,n,o,r,t,7);let s=Number(e&BigInt(4294967295));r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s;let i=Number(e>>BigInt(32)&BigInt(4294967295));return r[t++]=i,i=i>>8,r[t++]=i,i=i>>8,r[t++]=i,i=i>>8,r[t++]=i,t}function Kd(r,e,t,n,o){Yd(e,n,o,r,t,7);let s=Number(e&BigInt(4294967295));r[t+7]=s,s=s>>8,r[t+6]=s,s=s>>8,r[t+5]=s,s=s>>8,r[t+4]=s;let i=Number(e>>BigInt(32)&BigInt(4294967295));return r[t+3]=i,i=i>>8,r[t+2]=i,i=i>>8,r[t+1]=i,i=i>>8,r[t]=i,t+8}Z.prototype.writeBigUInt64LE=jn(function(e,t=0){return Wd(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))});Z.prototype.writeBigUInt64BE=jn(function(e,t=0){return Kd(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))});Z.prototype.writeIntLE=function(e,t,n,o){if(e=+e,t=t>>>0,!o){let f=Math.pow(2,8*n-1);dr(this,e,t,n,f-1,-f)}let s=0,i=1,u=0;for(this[t]=e&255;++s<n&&(i*=256);)e<0&&u===0&&this[t+s-1]!==0&&(u=1),this[t+s]=(e/i>>0)-u&255;return t+n};Z.prototype.writeIntBE=function(e,t,n,o){if(e=+e,t=t>>>0,!o){let f=Math.pow(2,8*n-1);dr(this,e,t,n,f-1,-f)}let s=n-1,i=1,u=0;for(this[t+s]=e&255;--s>=0&&(i*=256);)e<0&&u===0&&this[t+s+1]!==0&&(u=1),this[t+s]=(e/i>>0)-u&255;return t+n};Z.prototype.writeInt8=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=e&255,t+1};Z.prototype.writeInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,2,32767,-32768),this[t]=e&255,this[t+1]=e>>>8,t+2};Z.prototype.writeInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=e&255,t+2};Z.prototype.writeInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,4,2147483647,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4};Z.prototype.writeInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||dr(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};Z.prototype.writeBigInt64LE=jn(function(e,t=0){return Wd(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});Z.prototype.writeBigInt64BE=jn(function(e,t=0){return Kd(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function Vd(r,e,t,n,o,s){if(t+n>r.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function Gd(r,e,t,n,o){return e=+e,t=t>>>0,o||Vd(r,e,t,4,34028234663852886e22,-34028234663852886e22),xs.write(r,e,t,n,23,4),t+4}Z.prototype.writeFloatLE=function(e,t,n){return Gd(this,e,t,!0,n)};Z.prototype.writeFloatBE=function(e,t,n){return Gd(this,e,t,!1,n)};function Zd(r,e,t,n,o){return e=+e,t=t>>>0,o||Vd(r,e,t,8,17976931348623157e292,-17976931348623157e292),xs.write(r,e,t,n,52,8),t+8}Z.prototype.writeDoubleLE=function(e,t,n){return Zd(this,e,t,!0,n)};Z.prototype.writeDoubleBE=function(e,t,n){return Zd(this,e,t,!1,n)};Z.prototype.copy=function(e,t,n,o){if(!Z.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),!o&&o!==0&&(o=this.length),t>=e.length&&(t=e.length),t||(t=0),o>0&&o<n&&(o=n),o===n||e.length===0||this.length===0)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),e.length-t<o-n&&(o=e.length-t+n);let s=o-n;return this===e&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(t,n,o):Uint8Array.prototype.set.call(e,this.subarray(n,o),t),s};Z.prototype.fill=function(e,t,n,o){if(typeof e=="string"){if(typeof t=="string"?(o=t,t=0,n=this.length):typeof n=="string"&&(o=n,n=this.length),o!==void 0&&typeof o!="string")throw new TypeError("encoding must be a string");if(typeof o=="string"&&!Z.isEncoding(o))throw new TypeError("Unknown encoding: "+o);if(e.length===1){let i=e.charCodeAt(0);(o==="utf8"&&i<128||o==="latin1")&&(e=i)}}else typeof e=="number"?e=e&255:typeof e=="boolean"&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t=t>>>0,n=n===void 0?this.length:n>>>0,e||(e=0);let s;if(typeof e=="number")for(s=t;s<n;++s)this[s]=e;else{let i=Z.isBuffer(e)?e:Z.from(e,o),u=i.length;if(u===0)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(s=0;s<n-t;++s)this[s+t]=i[s%u]}return this};var gs={};function Ap(r,e,t){gs[r]=class extends t{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${r}]`,this.stack,delete this.name}get code(){return r}set code(o){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:o,writable:!0})}toString(){return`${this.name} [${r}]: ${this.message}`}}}Ap("ERR_BUFFER_OUT_OF_BOUNDS",function(r){return r?`${r} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError);Ap("ERR_INVALID_ARG_TYPE",function(r,e){return`The "${r}" argument must be of type number. Received type ${typeof e}`},TypeError);Ap("ERR_OUT_OF_RANGE",function(r,e,t){let n=`The value of "${r}" is out of range.`,o=t;return Number.isInteger(t)&&Math.abs(t)>2**32?o=qd(String(t)):typeof t=="bigint"&&(o=String(t),(t>BigInt(2)**BigInt(32)||t<-(BigInt(2)**BigInt(32)))&&(o=qd(o)),o+="n"),n+=` It must be ${e}. Received ${o}`,n},RangeError);function qd(r){let e="",t=r.length,n=r[0]==="-"?1:0;for(;t>=n+4;t-=3)e=`_${r.slice(t-3,t)}${e}`;return`${r.slice(0,t)}${e}`}function mA(r,e,t){ws(e,"offset"),(r[e]===void 0||r[e+t]===void 0)&&xi(e,r.length-(t+1))}function Yd(r,e,t,n,o,s){if(r>t||r<e){let i=typeof e=="bigint"?"n":"",u;throw s>3?e===0||e===BigInt(0)?u=`>= 0${i} and < 2${i} ** ${(s+1)*8}${i}`:u=`>= -(2${i} ** ${(s+1)*8-1}${i}) and < 2 ** ${(s+1)*8-1}${i}`:u=`>= ${e}${i} and <= ${t}${i}`,new gs.ERR_OUT_OF_RANGE("value",u,r)}mA(n,o,s)}function ws(r,e){if(typeof r!="number")throw new gs.ERR_INVALID_ARG_TYPE(e,"number",r)}function xi(r,e,t){throw Math.floor(r)!==r?(ws(r,t),new gs.ERR_OUT_OF_RANGE(t||"offset","an integer",r)):e<0?new gs.ERR_BUFFER_OUT_OF_BOUNDS:new gs.ERR_OUT_OF_RANGE(t||"offset",`>= ${t?1:0} and <= ${e}`,r)}var gA=/[^+/0-9A-Za-z-_]/g;function xA(r){if(r=r.split("=")[0],r=r.trim().replace(gA,""),r.length<2)return"";for(;r.length%4!==0;)r=r+"=";return r}function Ep(r,e){e=e||1/0;let t,n=r.length,o=null,s=[];for(let i=0;i<n;++i){if(t=r.charCodeAt(i),t>55295&&t<57344){if(!o){if(t>56319){(e-=3)>-1&&s.push(239,191,189);continue}else if(i+1===n){(e-=3)>-1&&s.push(239,191,189);continue}o=t;continue}if(t<56320){(e-=3)>-1&&s.push(239,191,189),o=t;continue}t=(o-55296<<10|t-56320)+65536}else o&&(e-=3)>-1&&s.push(239,191,189);if(o=null,t<128){if((e-=1)<0)break;s.push(t)}else if(t<2048){if((e-=2)<0)break;s.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=3)<0)break;s.push(t>>12|224,t>>6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;s.push(t>>18|240,t>>12&63|128,t>>6&63|128,t&63|128)}else throw new Error("Invalid code point")}return s}function wA(r){let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(t)&255);return e}function bA(r,e){let t,n,o,s=[];for(let i=0;i<r.length&&!((e-=2)<0);++i)t=r.charCodeAt(i),n=t>>8,o=t%256,s.push(o),s.push(n);return s}function $d(r){return bp.toByteArray(xA(r))}function za(r,e,t,n){let o;for(o=0;o<n&&!(o+t>=e.length||o>=r.length);++o)e[o+t]=r[o];return o}function Zr(r,e){return r instanceof e||r!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===e.name}function Ip(r){return r!==r}var vA=function(){let r="0123456789abcdef",e=new Array(256);for(let t=0;t<16;++t){let n=t*16;for(let o=0;o<16;++o)e[n+o]=r[t]+r[o]}return e}();function jn(r){return typeof BigInt>"u"?SA:r}function SA(){throw new Error("BigInt not supported")}});var ty=_e((UN,ey)=>{y();var Ft=ey.exports={},Yr,$r;function Rp(){throw new Error("setTimeout has not been defined")}function Bp(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?Yr=setTimeout:Yr=Rp}catch{Yr=Rp}try{typeof clearTimeout=="function"?$r=clearTimeout:$r=Bp}catch{$r=Bp}})();function Jd(r){if(Yr===setTimeout)return setTimeout(r,0);if((Yr===Rp||!Yr)&&setTimeout)return Yr=setTimeout,setTimeout(r,0);try{return Yr(r,0)}catch{try{return Yr.call(null,r,0)}catch{return Yr.call(this,r,0)}}}function EA(r){if($r===clearTimeout)return clearTimeout(r);if(($r===Bp||!$r)&&clearTimeout)return $r=clearTimeout,clearTimeout(r);try{return $r(r)}catch{try{return $r.call(null,r)}catch{return $r.call(this,r)}}}var dn=[],Ss=!1,Do,Ma=-1;function _A(){!Ss||!Do||(Ss=!1,Do.length?dn=Do.concat(dn):Ma=-1,dn.length&&Qd())}function Qd(){if(!Ss){var r=Jd(_A);Ss=!0;for(var e=dn.length;e;){for(Do=dn,dn=[];++Ma<e;)Do&&Do[Ma].run();Ma=-1,e=dn.length}Do=null,Ss=!1,EA(r)}}Ft.nextTick=function(r){var e=new Array(arguments.length-1);if(arguments.length>1)for(var t=1;t<arguments.length;t++)e[t-1]=arguments[t];dn.push(new Xd(r,e)),dn.length===1&&!Ss&&Jd(Qd)};function Xd(r,e){this.fun=r,this.array=e}Xd.prototype.run=function(){this.fun.apply(null,this.array)};Ft.title="browser";Ft.browser=!0;Ft.env={};Ft.argv=[];Ft.version="";Ft.versions={};function yn(){}Ft.on=yn;Ft.addListener=yn;Ft.once=yn;Ft.off=yn;Ft.removeListener=yn;Ft.removeAllListeners=yn;Ft.emit=yn;Ft.prependListener=yn;Ft.prependOnceListener=yn;Ft.listeners=function(r){return[]};Ft.binding=function(r){throw new Error("process.binding is not supported")};Ft.cwd=function(){return"/"};Ft.chdir=function(r){throw new Error("process.chdir is not supported")};Ft.umask=function(){return 0}});var ry,ny,B,I,y=O(()=>{"use strict";ry=Ke(vs()),ny=Ke(ty()),B=ny.default,I=ry.Buffer});function kA(r,e){if(r.length!==e.length)return!1;for(let t=0;t<r.length;t++)if(r[t]!==e[t])return!1;return!0}function oy(r,e){if(r.length!==e.length)return!1;for(let t=0;t<r.length;t++)if(r[t]!==e[t])return!1;return!0}var Hn,sy=O(()=>{"use strict";y();Hn=class r{#e;#t;#o;#s;#r;#n;get address(){return this.#e}get publicKey(){return this.#t.slice()}get chains(){return this.#o.slice()}get features(){return this.#s.slice()}get label(){return this.#r}get icon(){return this.#n}constructor({address:e,publicKey:t,label:n,icon:o,chains:s,features:i}){new.target===r&&Object.freeze(this),this.#e=e,this.#t=t,this.#o=s,this.#s=i,this.#r=n,this.#n=o}equals(e){return this.#e===e.address&&kA(this.#t,e.publicKey)&&oy(this.#o,e.chains)&&oy(this.#s,e.features)}}});var wi,iy=O(()=>{"use strict";y();wi="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTA4IiBoZWlnaHQ9IjEwOCIgdmlld0JveD0iMCAwIDEwOCAxMDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIxMDgiIGhlaWdodD0iMTA4IiByeD0iMjYiIGZpbGw9IiNBQjlGRjIiLz4KPHBhdGggZmlsbC1ydWxlPSJldmVub2RkIiBjbGlwLXJ1bGU9ImV2ZW5vZGQiIGQ9Ik00Ni41MjY3IDY5LjkyMjlDNDIuMDA1NCA3Ni44NTA5IDM0LjQyOTIgODUuNjE4MiAyNC4zNDggODUuNjE4MkMxOS41ODI0IDg1LjYxODIgMTUgODMuNjU2MyAxNSA3NS4xMzQyQzE1IDUzLjQzMDUgNDQuNjMyNiAxOS44MzI3IDcyLjEyNjggMTkuODMyN0M4Ny43NjggMTkuODMyNyA5NCAzMC42ODQ2IDk0IDQzLjAwNzlDOTQgNTguODI1OCA4My43MzU1IDc2LjkxMjIgNzMuNTMyMSA3Ni45MTIyQzcwLjI5MzkgNzYuOTEyMiA2OC43MDUzIDc1LjEzNDIgNjguNzA1MyA3Mi4zMTRDNjguNzA1MyA3MS41NzgzIDY4LjgyNzUgNzAuNzgxMiA2OS4wNzE5IDY5LjkyMjlDNjUuNTg5MyA3NS44Njk5IDU4Ljg2ODUgODEuMzg3OCA1Mi41NzU0IDgxLjM4NzhDNDcuOTkzIDgxLjM4NzggNDUuNjcxMyA3OC41MDYzIDQ1LjY3MTMgNzQuNDU5OEM0NS42NzEzIDcyLjk4ODQgNDUuOTc2OCA3MS40NTU2IDQ2LjUyNjcgNjkuOTIyOVpNODMuNjc2MSA0Mi41Nzk0QzgzLjY3NjEgNDYuMTcwNCA4MS41NTc1IDQ3Ljk2NTggNzkuMTg3NSA0Ny45NjU4Qzc2Ljc4MTYgNDcuOTY1OCA3NC42OTg5IDQ2LjE3MDQgNzQuNjk4OSA0Mi41Nzk0Qzc0LjY5ODkgMzguOTg4NSA3Ni43ODE2IDM3LjE5MzEgNzkuMTg3NSAzNy4xOTMxQzgxLjU1NzUgMzcuMTkzMSA4My42NzYxIDM4Ljk4ODUgODMuNjc2MSA0Mi41Nzk0Wk03MC4yMTAzIDQyLjU3OTVDNzAuMjEwMyA0Ni4xNzA0IDY4LjA5MTYgNDcuOTY1OCA2NS43MjE2IDQ3Ljk2NThDNjMuMzE1NyA0Ny45NjU4IDYxLjIzMyA0Ni4xNzA0IDYxLjIzMyA0Mi41Nzk1QzYxLjIzMyAzOC45ODg1IDYzLjMxNTcgMzcuMTkzMSA2NS43MjE2IDM3LjE5MzFDNjguMDkxNiAzNy4xOTMxIDcwLjIxMDMgMzguOTg4NSA3MC4yMTAzIDQyLjU3OTVaIiBmaWxsPSIjRkZGREY4Ii8+Cjwvc3ZnPgo="});function bi(r){let e=({register:t})=>t(r);try{window.dispatchEvent(new Pp(e))}catch(t){console.error(`wallet-standard:register-wallet event could not be dispatched
`,t)}try{window.addEventListener("wallet-standard:app-ready",({detail:t})=>e(t))}catch(t){console.error(`wallet-standard:app-ready event listener could not be added
`,t)}}var Pp,ay=O(()=>{"use strict";y();Pp=class extends Event{#e;get detail(){return this.#e}get type(){return"wallet-standard:register-wallet"}constructor(e){super("wallet-standard:register-wallet",{bubbles:!1,cancelable:!1,composed:!1}),this.#e=e}preventDefault(){throw new Error("preventDefault cannot be called")}stopImmediatePropagation(){throw new Error("stopImmediatePropagation cannot be called")}stopPropagation(){throw new Error("stopPropagation cannot be called")}}});function Tp(r){return vi.includes(r)}var cy,uy,py,fy,vi,ly=O(()=>{"use strict";y();cy="solana:mainnet",uy="solana:devnet",py="solana:testnet",fy="solana:localnet",vi=[cy,uy,py,fy]});var hy,dy,yy,Si,my=O(()=>{"use strict";y();hy="bitcoin:mainnet",dy="bitcoin:testnet",yy="bitcoin:regtest",Si=[hy,dy,yy]});function zp(r,e){return gy(r,e)}function gy(r,e){if(r===e)return!0;let t=r.length;if(t!==e.length)return!1;for(let n=0;n<t;n++)if(r[n]!==e[n])return!1;return!0}var xy=O(()=>{"use strict";y()});var Ei=O(()=>{"use strict";y();sy();iy();ay();ly();my();xy()});function IA(r){vy=r}function La(){return vy}function he(r,e){let t=La(),n=Na({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===ks?void 0:ks].filter(o=>!!o)});r.common.issues.push(n)}function Ca(r,e,t,n){if(t==="a"&&!n)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?n:t==="a"?n.call(r):n?n.value:e.get(r)}function Sy(r,e,t,n,o){if(n==="m")throw new TypeError("Private method is not writable");if(n==="a"&&!o)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!o:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return n==="a"?o.call(r,t):o?o.value=t:e.set(r,t),t}function Ne(r){if(!r)return{};let{errorMap:e,invalid_type_error:t,required_error:n,description:o}=r;if(e&&(t||n))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:o}:{errorMap:(i,u)=>{var f,g;let{message:b}=r;return i.code==="invalid_enum_value"?{message:b??u.defaultError}:typeof u.data>"u"?{message:(f=b??n)!==null&&f!==void 0?f:u.defaultError}:i.code!=="invalid_type"?{message:u.defaultError}:{message:(g=b??t)!==null&&g!==void 0?g:u.defaultError}},description:o}}function _y(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function FA(r){return new RegExp(`^${_y(r)}$`)}function ky(r){let e=`${Ey}T${_y(r)}`,t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function jA(r,e){return!!((e==="v4"||!e)&&OA.test(r)||(e==="v6"||!e)&&qA.test(r))}function HA(r,e){let t=(r.toString().split(".")[1]||"").length,n=(e.toString().split(".")[1]||"").length,o=t>n?t:n,s=parseInt(r.toFixed(o).replace(".","")),i=parseInt(e.toFixed(o).replace(".",""));return s%i/Math.pow(10,o)}function Es(r){if(r instanceof yr){let e={};for(let t in r.shape){let n=r.shape[t];e[t]=Br.create(Es(n))}return new yr({...r._def,shape:()=>e})}else return r instanceof xn?new xn({...r._def,type:Es(r.element)}):r instanceof Br?Br.create(Es(r.unwrap())):r instanceof Qr?Qr.create(Es(r.unwrap())):r instanceof Jr?Jr.create(r.items.map(e=>Es(e))):r}function Op(r,e){let t=Wn(r),n=Wn(e);if(r===e)return{valid:!0,data:r};if(t===de.object&&n===de.object){let o=je.objectKeys(e),s=je.objectKeys(r).filter(u=>o.indexOf(u)!==-1),i={...r,...e};for(let u of s){let f=Op(r[u],e[u]);if(!f.valid)return{valid:!1};i[u]=f.data}return{valid:!0,data:i}}else if(t===de.array&&n===de.array){if(r.length!==e.length)return{valid:!1};let o=[];for(let s=0;s<r.length;s++){let i=r[s],u=e[s],f=Op(i,u);if(!f.valid)return{valid:!1};o.push(f.data)}return{valid:!0,data:o}}else return t===de.date&&n===de.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}function Ay(r,e){return new Jo({values:r,typeName:Ae.ZodEnum,...Ne(e)})}function Iy(r,e={},t){return r?Vn.create().superRefine((n,o)=>{var s,i;if(!r(n)){let u=typeof e=="function"?e(n):typeof e=="string"?{message:e}:e,f=(i=(s=u.fatal)!==null&&s!==void 0?s:t)!==null&&i!==void 0?i:!0,g=typeof u=="string"?{message:u}:u;o.addIssue({code:"custom",...g,fatal:f})}}):Vn.create()}var je,Lp,de,Wn,ne,AA,Ht,ks,vy,Na,RA,Xt,Re,_s,ur,Np,Cp,Ai,Ii,Se,_i,ki,Pr,wy,Ce,BA,PA,TA,zA,MA,LA,NA,CA,Mp,OA,qA,UA,Ey,DA,Kn,Fo,jo,Ho,Wo,As,Ko,Vo,Vn,gn,qr,Is,xn,yr,Go,mn,Oa,Zo,Jr,qa,Rs,Bs,Ua,Yo,$o,Jo,Qo,Gn,br,Br,Qr,Xo,es,Ps,WA,Ri,Bi,ts,KA,Ae,VA,Ry,By,GA,ZA,Py,YA,$A,JA,QA,XA,eI,tI,rI,nI,oI,sI,iI,aI,cI,uI,pI,fI,lI,hI,dI,yI,mI,gI,xI,by,wI,bI,vI,SI,EI,_I,kI,AI,II,a,V=O(()=>{y();(function(r){r.assertEqual=o=>o;function e(o){}r.assertIs=e;function t(o){throw new Error}r.assertNever=t,r.arrayToEnum=o=>{let s={};for(let i of o)s[i]=i;return s},r.getValidEnumValues=o=>{let s=r.objectKeys(o).filter(u=>typeof o[o[u]]!="number"),i={};for(let u of s)i[u]=o[u];return r.objectValues(i)},r.objectValues=o=>r.objectKeys(o).map(function(s){return o[s]}),r.objectKeys=typeof Object.keys=="function"?o=>Object.keys(o):o=>{let s=[];for(let i in o)Object.prototype.hasOwnProperty.call(o,i)&&s.push(i);return s},r.find=(o,s)=>{for(let i of o)if(s(i))return i},r.isInteger=typeof Number.isInteger=="function"?o=>Number.isInteger(o):o=>typeof o=="number"&&isFinite(o)&&Math.floor(o)===o;function n(o,s=" | "){return o.map(i=>typeof i=="string"?`'${i}'`:i).join(s)}r.joinValues=n,r.jsonStringifyReplacer=(o,s)=>typeof s=="bigint"?s.toString():s})(je||(je={}));(function(r){r.mergeShapes=(e,t)=>({...e,...t})})(Lp||(Lp={}));de=je.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),Wn=r=>{switch(typeof r){case"undefined":return de.undefined;case"string":return de.string;case"number":return isNaN(r)?de.nan:de.number;case"boolean":return de.boolean;case"function":return de.function;case"bigint":return de.bigint;case"symbol":return de.symbol;case"object":return Array.isArray(r)?de.array:r===null?de.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?de.promise:typeof Map<"u"&&r instanceof Map?de.map:typeof Set<"u"&&r instanceof Set?de.set:typeof Date<"u"&&r instanceof Date?de.date:de.object;default:return de.unknown}},ne=je.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),AA=r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),Ht=class r extends Error{constructor(e){super(),this.issues=[],this.addIssue=n=>{this.issues=[...this.issues,n]},this.addIssues=(n=[])=>{this.issues=[...this.issues,...n]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){let t=e||function(s){return s.message},n={_errors:[]},o=s=>{for(let i of s.issues)if(i.code==="invalid_union")i.unionErrors.map(o);else if(i.code==="invalid_return_type")o(i.returnTypeError);else if(i.code==="invalid_arguments")o(i.argumentsError);else if(i.path.length===0)n._errors.push(t(i));else{let u=n,f=0;for(;f<i.path.length;){let g=i.path[f];f===i.path.length-1?(u[g]=u[g]||{_errors:[]},u[g]._errors.push(t(i))):u[g]=u[g]||{_errors:[]},u=u[g],f++}}};return o(this),n}static assert(e){if(!(e instanceof r))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,je.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){let t={},n=[];for(let o of this.issues)o.path.length>0?(t[o.path[0]]=t[o.path[0]]||[],t[o.path[0]].push(e(o))):n.push(e(o));return{formErrors:n,fieldErrors:t}}get formErrors(){return this.flatten()}};Ht.create=r=>new Ht(r);ks=(r,e)=>{let t;switch(r.code){case ne.invalid_type:r.received===de.undefined?t="Required":t=`Expected ${r.expected}, received ${r.received}`;break;case ne.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,je.jsonStringifyReplacer)}`;break;case ne.unrecognized_keys:t=`Unrecognized key(s) in object: ${je.joinValues(r.keys,", ")}`;break;case ne.invalid_union:t="Invalid input";break;case ne.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${je.joinValues(r.options)}`;break;case ne.invalid_enum_value:t=`Invalid enum value. Expected ${je.joinValues(r.options)}, received '${r.received}'`;break;case ne.invalid_arguments:t="Invalid function arguments";break;case ne.invalid_return_type:t="Invalid function return type";break;case ne.invalid_date:t="Invalid date";break;case ne.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:je.assertNever(r.validation):r.validation!=="regex"?t=`Invalid ${r.validation}`:t="Invalid";break;case ne.too_small:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:t="Invalid input";break;case ne.too_big:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?t=`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:t="Invalid input";break;case ne.custom:t="Invalid input";break;case ne.invalid_intersection_types:t="Intersection results could not be merged";break;case ne.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case ne.not_finite:t="Number must be finite";break;default:t=e.defaultError,je.assertNever(r)}return{message:t}},vy=ks;Na=r=>{let{data:e,path:t,errorMaps:n,issueData:o}=r,s=[...t,...o.path||[]],i={...o,path:s};if(o.message!==void 0)return{...o,path:s,message:o.message};let u="",f=n.filter(g=>!!g).slice().reverse();for(let g of f)u=g(i,{data:e,defaultError:u}).message;return{...o,path:s,message:u}},RA=[];Xt=class r{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){let n=[];for(let o of t){if(o.status==="aborted")return Re;o.status==="dirty"&&e.dirty(),n.push(o.value)}return{status:e.value,value:n}}static async mergeObjectAsync(e,t){let n=[];for(let o of t){let s=await o.key,i=await o.value;n.push({key:s,value:i})}return r.mergeObjectSync(e,n)}static mergeObjectSync(e,t){let n={};for(let o of t){let{key:s,value:i}=o;if(s.status==="aborted"||i.status==="aborted")return Re;s.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),s.value!=="__proto__"&&(typeof i.value<"u"||o.alwaysSet)&&(n[s.value]=i.value)}return{status:e.value,value:n}}},Re=Object.freeze({status:"aborted"}),_s=r=>({status:"dirty",value:r}),ur=r=>({status:"valid",value:r}),Np=r=>r.status==="aborted",Cp=r=>r.status==="dirty",Ai=r=>r.status==="valid",Ii=r=>typeof Promise<"u"&&r instanceof Promise;(function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e?.message})(Se||(Se={}));Pr=class{constructor(e,t,n,o){this._cachedPath=[],this.parent=e,this.data=t,this._path=n,this._key=o}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}},wy=(r,e)=>{if(Ai(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new Ht(r.common.issues);return this._error=t,this._error}}};Ce=class{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return Wn(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:Wn(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new Xt,ctx:{common:e.parent.common,data:e.data,parsedType:Wn(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(Ii(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){let t=this._parse(e);return Promise.resolve(t)}parse(e,t){let n=this.safeParse(e,t);if(n.success)return n.data;throw n.error}safeParse(e,t){var n;let o={common:{issues:[],async:(n=t?.async)!==null&&n!==void 0?n:!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Wn(e)},s=this._parseSync({data:e,path:o.path,parent:o});return wy(o,s)}async parseAsync(e,t){let n=await this.safeParseAsync(e,t);if(n.success)return n.data;throw n.error}async safeParseAsync(e,t){let n={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:Wn(e)},o=this._parse({data:e,path:n.path,parent:n}),s=await(Ii(o)?o:Promise.resolve(o));return wy(n,s)}refine(e,t){let n=o=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(o):t;return this._refinement((o,s)=>{let i=e(o),u=()=>s.addIssue({code:ne.custom,...n(o)});return typeof Promise<"u"&&i instanceof Promise?i.then(f=>f?!0:(u(),!1)):i?!0:(u(),!1)})}refinement(e,t){return this._refinement((n,o)=>e(n)?!0:(o.addIssue(typeof t=="function"?t(n,o):t),!1))}_refinement(e){return new br({schema:this,typeName:Ae.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return Br.create(this,this._def)}nullable(){return Qr.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return xn.create(this,this._def)}promise(){return Gn.create(this,this._def)}or(e){return Go.create([this,e],this._def)}and(e){return Zo.create(this,e,this._def)}transform(e){return new br({...Ne(this._def),schema:this,typeName:Ae.ZodEffects,effect:{type:"transform",transform:e}})}default(e){let t=typeof e=="function"?e:()=>e;return new Xo({...Ne(this._def),innerType:this,defaultValue:t,typeName:Ae.ZodDefault})}brand(){return new Ri({typeName:Ae.ZodBranded,type:this,...Ne(this._def)})}catch(e){let t=typeof e=="function"?e:()=>e;return new es({...Ne(this._def),innerType:this,catchValue:t,typeName:Ae.ZodCatch})}describe(e){let t=this.constructor;return new t({...this._def,description:e})}pipe(e){return Bi.create(this,e)}readonly(){return ts.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}},BA=/^c[^\s-]{8,}$/i,PA=/^[0-9a-z]+$/,TA=/^[0-9A-HJKMNP-TV-Z]{26}$/,zA=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,MA=/^[a-z0-9_-]{21}$/i,LA=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,NA=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,CA="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",OA=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,qA=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,UA=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Ey="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",DA=new RegExp(`^${Ey}$`);Kn=class r extends Ce{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==de.string){let s=this._getOrReturnCtx(e);return he(s,{code:ne.invalid_type,expected:de.string,received:s.parsedType}),Re}let n=new Xt,o;for(let s of this._def.checks)if(s.kind==="min")e.data.length<s.value&&(o=this._getOrReturnCtx(e,o),he(o,{code:ne.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="max")e.data.length>s.value&&(o=this._getOrReturnCtx(e,o),he(o,{code:ne.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!1,message:s.message}),n.dirty());else if(s.kind==="length"){let i=e.data.length>s.value,u=e.data.length<s.value;(i||u)&&(o=this._getOrReturnCtx(e,o),i?he(o,{code:ne.too_big,maximum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}):u&&he(o,{code:ne.too_small,minimum:s.value,type:"string",inclusive:!0,exact:!0,message:s.message}),n.dirty())}else if(s.kind==="email")NA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"email",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="emoji")Mp||(Mp=new RegExp(CA,"u")),Mp.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"emoji",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="uuid")zA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"uuid",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="nanoid")MA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"nanoid",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid")BA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"cuid",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="cuid2")PA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"cuid2",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="ulid")TA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"ulid",code:ne.invalid_string,message:s.message}),n.dirty());else if(s.kind==="url")try{new URL(e.data)}catch{o=this._getOrReturnCtx(e,o),he(o,{validation:"url",code:ne.invalid_string,message:s.message}),n.dirty()}else s.kind==="regex"?(s.regex.lastIndex=0,s.regex.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"regex",code:ne.invalid_string,message:s.message}),n.dirty())):s.kind==="trim"?e.data=e.data.trim():s.kind==="includes"?e.data.includes(s.value,s.position)||(o=this._getOrReturnCtx(e,o),he(o,{code:ne.invalid_string,validation:{includes:s.value,position:s.position},message:s.message}),n.dirty()):s.kind==="toLowerCase"?e.data=e.data.toLowerCase():s.kind==="toUpperCase"?e.data=e.data.toUpperCase():s.kind==="startsWith"?e.data.startsWith(s.value)||(o=this._getOrReturnCtx(e,o),he(o,{code:ne.invalid_string,validation:{startsWith:s.value},message:s.message}),n.dirty()):s.kind==="endsWith"?e.data.endsWith(s.value)||(o=this._getOrReturnCtx(e,o),he(o,{code:ne.invalid_string,validation:{endsWith:s.value},message:s.message}),n.dirty()):s.kind==="datetime"?ky(s).test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{code:ne.invalid_string,validation:"datetime",message:s.message}),n.dirty()):s.kind==="date"?DA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{code:ne.invalid_string,validation:"date",message:s.message}),n.dirty()):s.kind==="time"?FA(s).test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{code:ne.invalid_string,validation:"time",message:s.message}),n.dirty()):s.kind==="duration"?LA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"duration",code:ne.invalid_string,message:s.message}),n.dirty()):s.kind==="ip"?jA(e.data,s.version)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"ip",code:ne.invalid_string,message:s.message}),n.dirty()):s.kind==="base64"?UA.test(e.data)||(o=this._getOrReturnCtx(e,o),he(o,{validation:"base64",code:ne.invalid_string,message:s.message}),n.dirty()):je.assertNever(s);return{status:n.value,value:e.data}}_regex(e,t,n){return this.refinement(o=>e.test(o),{validation:t,code:ne.invalid_string,...Se.errToObj(n)})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...Se.errToObj(e)})}url(e){return this._addCheck({kind:"url",...Se.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...Se.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...Se.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...Se.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...Se.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...Se.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...Se.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...Se.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...Se.errToObj(e)})}datetime(e){var t,n;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:(t=e?.offset)!==null&&t!==void 0?t:!1,local:(n=e?.local)!==null&&n!==void 0?n:!1,...Se.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...Se.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...Se.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...Se.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...Se.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...Se.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...Se.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...Se.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...Se.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...Se.errToObj(t)})}nonempty(e){return this.min(1,Se.errToObj(e))}trim(){return new r({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new r({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new r({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get minLength(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};Kn.create=r=>{var e;return new Kn({checks:[],typeName:Ae.ZodString,coerce:(e=r?.coerce)!==null&&e!==void 0?e:!1,...Ne(r)})};Fo=class r extends Ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==de.number){let s=this._getOrReturnCtx(e);return he(s,{code:ne.invalid_type,expected:de.number,received:s.parsedType}),Re}let n,o=new Xt;for(let s of this._def.checks)s.kind==="int"?je.isInteger(e.data)||(n=this._getOrReturnCtx(e,n),he(n,{code:ne.invalid_type,expected:"integer",received:"float",message:s.message}),o.dirty()):s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),he(n,{code:ne.too_small,minimum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),o.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),he(n,{code:ne.too_big,maximum:s.value,type:"number",inclusive:s.inclusive,exact:!1,message:s.message}),o.dirty()):s.kind==="multipleOf"?HA(e.data,s.value)!==0&&(n=this._getOrReturnCtx(e,n),he(n,{code:ne.not_multiple_of,multipleOf:s.value,message:s.message}),o.dirty()):s.kind==="finite"?Number.isFinite(e.data)||(n=this._getOrReturnCtx(e,n),he(n,{code:ne.not_finite,message:s.message}),o.dirty()):je.assertNever(s);return{status:o.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Se.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Se.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Se.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Se.toString(t))}setLimit(e,t,n,o){return new r({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:Se.toString(o)}]})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:Se.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:Se.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:Se.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:Se.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:Se.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Se.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:Se.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:Se.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:Se.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&je.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let n of this._def.checks){if(n.kind==="finite"||n.kind==="int"||n.kind==="multipleOf")return!0;n.kind==="min"?(t===null||n.value>t)&&(t=n.value):n.kind==="max"&&(e===null||n.value<e)&&(e=n.value)}return Number.isFinite(t)&&Number.isFinite(e)}};Fo.create=r=>new Fo({checks:[],typeName:Ae.ZodNumber,coerce:r?.coerce||!1,...Ne(r)});jo=class r extends Ce{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce&&(e.data=BigInt(e.data)),this._getType(e)!==de.bigint){let s=this._getOrReturnCtx(e);return he(s,{code:ne.invalid_type,expected:de.bigint,received:s.parsedType}),Re}let n,o=new Xt;for(let s of this._def.checks)s.kind==="min"?(s.inclusive?e.data<s.value:e.data<=s.value)&&(n=this._getOrReturnCtx(e,n),he(n,{code:ne.too_small,type:"bigint",minimum:s.value,inclusive:s.inclusive,message:s.message}),o.dirty()):s.kind==="max"?(s.inclusive?e.data>s.value:e.data>=s.value)&&(n=this._getOrReturnCtx(e,n),he(n,{code:ne.too_big,type:"bigint",maximum:s.value,inclusive:s.inclusive,message:s.message}),o.dirty()):s.kind==="multipleOf"?e.data%s.value!==BigInt(0)&&(n=this._getOrReturnCtx(e,n),he(n,{code:ne.not_multiple_of,multipleOf:s.value,message:s.message}),o.dirty()):je.assertNever(s);return{status:o.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,Se.toString(t))}gt(e,t){return this.setLimit("min",e,!1,Se.toString(t))}lte(e,t){return this.setLimit("max",e,!0,Se.toString(t))}lt(e,t){return this.setLimit("max",e,!1,Se.toString(t))}setLimit(e,t,n,o){return new r({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:n,message:Se.toString(o)}]})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:Se.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:Se.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:Se.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:Se.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:Se.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};jo.create=r=>{var e;return new jo({checks:[],typeName:Ae.ZodBigInt,coerce:(e=r?.coerce)!==null&&e!==void 0?e:!1,...Ne(r)})};Ho=class extends Ce{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==de.boolean){let n=this._getOrReturnCtx(e);return he(n,{code:ne.invalid_type,expected:de.boolean,received:n.parsedType}),Re}return ur(e.data)}};Ho.create=r=>new Ho({typeName:Ae.ZodBoolean,coerce:r?.coerce||!1,...Ne(r)});Wo=class r extends Ce{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==de.date){let s=this._getOrReturnCtx(e);return he(s,{code:ne.invalid_type,expected:de.date,received:s.parsedType}),Re}if(isNaN(e.data.getTime())){let s=this._getOrReturnCtx(e);return he(s,{code:ne.invalid_date}),Re}let n=new Xt,o;for(let s of this._def.checks)s.kind==="min"?e.data.getTime()<s.value&&(o=this._getOrReturnCtx(e,o),he(o,{code:ne.too_small,message:s.message,inclusive:!0,exact:!1,minimum:s.value,type:"date"}),n.dirty()):s.kind==="max"?e.data.getTime()>s.value&&(o=this._getOrReturnCtx(e,o),he(o,{code:ne.too_big,message:s.message,inclusive:!0,exact:!1,maximum:s.value,type:"date"}),n.dirty()):je.assertNever(s);return{status:n.value,value:new Date(e.data.getTime())}}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:Se.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:Se.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};Wo.create=r=>new Wo({checks:[],coerce:r?.coerce||!1,typeName:Ae.ZodDate,...Ne(r)});As=class extends Ce{_parse(e){if(this._getType(e)!==de.symbol){let n=this._getOrReturnCtx(e);return he(n,{code:ne.invalid_type,expected:de.symbol,received:n.parsedType}),Re}return ur(e.data)}};As.create=r=>new As({typeName:Ae.ZodSymbol,...Ne(r)});Ko=class extends Ce{_parse(e){if(this._getType(e)!==de.undefined){let n=this._getOrReturnCtx(e);return he(n,{code:ne.invalid_type,expected:de.undefined,received:n.parsedType}),Re}return ur(e.data)}};Ko.create=r=>new Ko({typeName:Ae.ZodUndefined,...Ne(r)});Vo=class extends Ce{_parse(e){if(this._getType(e)!==de.null){let n=this._getOrReturnCtx(e);return he(n,{code:ne.invalid_type,expected:de.null,received:n.parsedType}),Re}return ur(e.data)}};Vo.create=r=>new Vo({typeName:Ae.ZodNull,...Ne(r)});Vn=class extends Ce{constructor(){super(...arguments),this._any=!0}_parse(e){return ur(e.data)}};Vn.create=r=>new Vn({typeName:Ae.ZodAny,...Ne(r)});gn=class extends Ce{constructor(){super(...arguments),this._unknown=!0}_parse(e){return ur(e.data)}};gn.create=r=>new gn({typeName:Ae.ZodUnknown,...Ne(r)});qr=class extends Ce{_parse(e){let t=this._getOrReturnCtx(e);return he(t,{code:ne.invalid_type,expected:de.never,received:t.parsedType}),Re}};qr.create=r=>new qr({typeName:Ae.ZodNever,...Ne(r)});Is=class extends Ce{_parse(e){if(this._getType(e)!==de.undefined){let n=this._getOrReturnCtx(e);return he(n,{code:ne.invalid_type,expected:de.void,received:n.parsedType}),Re}return ur(e.data)}};Is.create=r=>new Is({typeName:Ae.ZodVoid,...Ne(r)});xn=class r extends Ce{_parse(e){let{ctx:t,status:n}=this._processInputParams(e),o=this._def;if(t.parsedType!==de.array)return he(t,{code:ne.invalid_type,expected:de.array,received:t.parsedType}),Re;if(o.exactLength!==null){let i=t.data.length>o.exactLength.value,u=t.data.length<o.exactLength.value;(i||u)&&(he(t,{code:i?ne.too_big:ne.too_small,minimum:u?o.exactLength.value:void 0,maximum:i?o.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:o.exactLength.message}),n.dirty())}if(o.minLength!==null&&t.data.length<o.minLength.value&&(he(t,{code:ne.too_small,minimum:o.minLength.value,type:"array",inclusive:!0,exact:!1,message:o.minLength.message}),n.dirty()),o.maxLength!==null&&t.data.length>o.maxLength.value&&(he(t,{code:ne.too_big,maximum:o.maxLength.value,type:"array",inclusive:!0,exact:!1,message:o.maxLength.message}),n.dirty()),t.common.async)return Promise.all([...t.data].map((i,u)=>o.type._parseAsync(new Pr(t,i,t.path,u)))).then(i=>Xt.mergeArray(n,i));let s=[...t.data].map((i,u)=>o.type._parseSync(new Pr(t,i,t.path,u)));return Xt.mergeArray(n,s)}get element(){return this._def.type}min(e,t){return new r({...this._def,minLength:{value:e,message:Se.toString(t)}})}max(e,t){return new r({...this._def,maxLength:{value:e,message:Se.toString(t)}})}length(e,t){return new r({...this._def,exactLength:{value:e,message:Se.toString(t)}})}nonempty(e){return this.min(1,e)}};xn.create=(r,e)=>new xn({type:r,minLength:null,maxLength:null,exactLength:null,typeName:Ae.ZodArray,...Ne(e)});yr=class r extends Ce{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;let e=this._def.shape(),t=je.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==de.object){let g=this._getOrReturnCtx(e);return he(g,{code:ne.invalid_type,expected:de.object,received:g.parsedType}),Re}let{status:n,ctx:o}=this._processInputParams(e),{shape:s,keys:i}=this._getCached(),u=[];if(!(this._def.catchall instanceof qr&&this._def.unknownKeys==="strip"))for(let g in o.data)i.includes(g)||u.push(g);let f=[];for(let g of i){let b=s[g],E=o.data[g];f.push({key:{status:"valid",value:g},value:b._parse(new Pr(o,E,o.path,g)),alwaysSet:g in o.data})}if(this._def.catchall instanceof qr){let g=this._def.unknownKeys;if(g==="passthrough")for(let b of u)f.push({key:{status:"valid",value:b},value:{status:"valid",value:o.data[b]}});else if(g==="strict")u.length>0&&(he(o,{code:ne.unrecognized_keys,keys:u}),n.dirty());else if(g!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{let g=this._def.catchall;for(let b of u){let E=o.data[b];f.push({key:{status:"valid",value:b},value:g._parse(new Pr(o,E,o.path,b)),alwaysSet:b in o.data})}}return o.common.async?Promise.resolve().then(async()=>{let g=[];for(let b of f){let E=await b.key,q=await b.value;g.push({key:E,value:q,alwaysSet:b.alwaysSet})}return g}).then(g=>Xt.mergeObjectSync(n,g)):Xt.mergeObjectSync(n,f)}get shape(){return this._def.shape()}strict(e){return Se.errToObj,new r({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,n)=>{var o,s,i,u;let f=(i=(s=(o=this._def).errorMap)===null||s===void 0?void 0:s.call(o,t,n).message)!==null&&i!==void 0?i:n.defaultError;return t.code==="unrecognized_keys"?{message:(u=Se.errToObj(e).message)!==null&&u!==void 0?u:f}:{message:f}}}:{}})}strip(){return new r({...this._def,unknownKeys:"strip"})}passthrough(){return new r({...this._def,unknownKeys:"passthrough"})}extend(e){return new r({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new r({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:Ae.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new r({...this._def,catchall:e})}pick(e){let t={};return je.objectKeys(e).forEach(n=>{e[n]&&this.shape[n]&&(t[n]=this.shape[n])}),new r({...this._def,shape:()=>t})}omit(e){let t={};return je.objectKeys(this.shape).forEach(n=>{e[n]||(t[n]=this.shape[n])}),new r({...this._def,shape:()=>t})}deepPartial(){return Es(this)}partial(e){let t={};return je.objectKeys(this.shape).forEach(n=>{let o=this.shape[n];e&&!e[n]?t[n]=o:t[n]=o.optional()}),new r({...this._def,shape:()=>t})}required(e){let t={};return je.objectKeys(this.shape).forEach(n=>{if(e&&!e[n])t[n]=this.shape[n];else{let s=this.shape[n];for(;s instanceof Br;)s=s._def.innerType;t[n]=s}}),new r({...this._def,shape:()=>t})}keyof(){return Ay(je.objectKeys(this.shape))}};yr.create=(r,e)=>new yr({shape:()=>r,unknownKeys:"strip",catchall:qr.create(),typeName:Ae.ZodObject,...Ne(e)});yr.strictCreate=(r,e)=>new yr({shape:()=>r,unknownKeys:"strict",catchall:qr.create(),typeName:Ae.ZodObject,...Ne(e)});yr.lazycreate=(r,e)=>new yr({shape:r,unknownKeys:"strip",catchall:qr.create(),typeName:Ae.ZodObject,...Ne(e)});Go=class extends Ce{_parse(e){let{ctx:t}=this._processInputParams(e),n=this._def.options;function o(s){for(let u of s)if(u.result.status==="valid")return u.result;for(let u of s)if(u.result.status==="dirty")return t.common.issues.push(...u.ctx.common.issues),u.result;let i=s.map(u=>new Ht(u.ctx.common.issues));return he(t,{code:ne.invalid_union,unionErrors:i}),Re}if(t.common.async)return Promise.all(n.map(async s=>{let i={...t,common:{...t.common,issues:[]},parent:null};return{result:await s._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}})).then(o);{let s,i=[];for(let f of n){let g={...t,common:{...t.common,issues:[]},parent:null},b=f._parseSync({data:t.data,path:t.path,parent:g});if(b.status==="valid")return b;b.status==="dirty"&&!s&&(s={result:b,ctx:g}),g.common.issues.length&&i.push(g.common.issues)}if(s)return t.common.issues.push(...s.ctx.common.issues),s.result;let u=i.map(f=>new Ht(f));return he(t,{code:ne.invalid_union,unionErrors:u}),Re}}get options(){return this._def.options}};Go.create=(r,e)=>new Go({options:r,typeName:Ae.ZodUnion,...Ne(e)});mn=r=>r instanceof Yo?mn(r.schema):r instanceof br?mn(r.innerType()):r instanceof $o?[r.value]:r instanceof Jo?r.options:r instanceof Qo?je.objectValues(r.enum):r instanceof Xo?mn(r._def.innerType):r instanceof Ko?[void 0]:r instanceof Vo?[null]:r instanceof Br?[void 0,...mn(r.unwrap())]:r instanceof Qr?[null,...mn(r.unwrap())]:r instanceof Ri||r instanceof ts?mn(r.unwrap()):r instanceof es?mn(r._def.innerType):[],Oa=class r extends Ce{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==de.object)return he(t,{code:ne.invalid_type,expected:de.object,received:t.parsedType}),Re;let n=this.discriminator,o=t.data[n],s=this.optionsMap.get(o);return s?t.common.async?s._parseAsync({data:t.data,path:t.path,parent:t}):s._parseSync({data:t.data,path:t.path,parent:t}):(he(t,{code:ne.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[n]}),Re)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,n){let o=new Map;for(let s of t){let i=mn(s.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let u of i){if(o.has(u))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(u)}`);o.set(u,s)}}return new r({typeName:Ae.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:o,...Ne(n)})}};Zo=class extends Ce{_parse(e){let{status:t,ctx:n}=this._processInputParams(e),o=(s,i)=>{if(Np(s)||Np(i))return Re;let u=Op(s.value,i.value);return u.valid?((Cp(s)||Cp(i))&&t.dirty(),{status:t.value,value:u.data}):(he(n,{code:ne.invalid_intersection_types}),Re)};return n.common.async?Promise.all([this._def.left._parseAsync({data:n.data,path:n.path,parent:n}),this._def.right._parseAsync({data:n.data,path:n.path,parent:n})]).then(([s,i])=>o(s,i)):o(this._def.left._parseSync({data:n.data,path:n.path,parent:n}),this._def.right._parseSync({data:n.data,path:n.path,parent:n}))}};Zo.create=(r,e,t)=>new Zo({left:r,right:e,typeName:Ae.ZodIntersection,...Ne(t)});Jr=class r extends Ce{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==de.array)return he(n,{code:ne.invalid_type,expected:de.array,received:n.parsedType}),Re;if(n.data.length<this._def.items.length)return he(n,{code:ne.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),Re;!this._def.rest&&n.data.length>this._def.items.length&&(he(n,{code:ne.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let s=[...n.data].map((i,u)=>{let f=this._def.items[u]||this._def.rest;return f?f._parse(new Pr(n,i,n.path,u)):null}).filter(i=>!!i);return n.common.async?Promise.all(s).then(i=>Xt.mergeArray(t,i)):Xt.mergeArray(t,s)}get items(){return this._def.items}rest(e){return new r({...this._def,rest:e})}};Jr.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new Jr({items:r,typeName:Ae.ZodTuple,rest:null,...Ne(e)})};qa=class r extends Ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==de.object)return he(n,{code:ne.invalid_type,expected:de.object,received:n.parsedType}),Re;let o=[],s=this._def.keyType,i=this._def.valueType;for(let u in n.data)o.push({key:s._parse(new Pr(n,u,n.path,u)),value:i._parse(new Pr(n,n.data[u],n.path,u)),alwaysSet:u in n.data});return n.common.async?Xt.mergeObjectAsync(t,o):Xt.mergeObjectSync(t,o)}get element(){return this._def.valueType}static create(e,t,n){return t instanceof Ce?new r({keyType:e,valueType:t,typeName:Ae.ZodRecord,...Ne(n)}):new r({keyType:Kn.create(),valueType:e,typeName:Ae.ZodRecord,...Ne(t)})}},Rs=class extends Ce{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==de.map)return he(n,{code:ne.invalid_type,expected:de.map,received:n.parsedType}),Re;let o=this._def.keyType,s=this._def.valueType,i=[...n.data.entries()].map(([u,f],g)=>({key:o._parse(new Pr(n,u,n.path,[g,"key"])),value:s._parse(new Pr(n,f,n.path,[g,"value"]))}));if(n.common.async){let u=new Map;return Promise.resolve().then(async()=>{for(let f of i){let g=await f.key,b=await f.value;if(g.status==="aborted"||b.status==="aborted")return Re;(g.status==="dirty"||b.status==="dirty")&&t.dirty(),u.set(g.value,b.value)}return{status:t.value,value:u}})}else{let u=new Map;for(let f of i){let g=f.key,b=f.value;if(g.status==="aborted"||b.status==="aborted")return Re;(g.status==="dirty"||b.status==="dirty")&&t.dirty(),u.set(g.value,b.value)}return{status:t.value,value:u}}}};Rs.create=(r,e,t)=>new Rs({valueType:e,keyType:r,typeName:Ae.ZodMap,...Ne(t)});Bs=class r extends Ce{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.parsedType!==de.set)return he(n,{code:ne.invalid_type,expected:de.set,received:n.parsedType}),Re;let o=this._def;o.minSize!==null&&n.data.size<o.minSize.value&&(he(n,{code:ne.too_small,minimum:o.minSize.value,type:"set",inclusive:!0,exact:!1,message:o.minSize.message}),t.dirty()),o.maxSize!==null&&n.data.size>o.maxSize.value&&(he(n,{code:ne.too_big,maximum:o.maxSize.value,type:"set",inclusive:!0,exact:!1,message:o.maxSize.message}),t.dirty());let s=this._def.valueType;function i(f){let g=new Set;for(let b of f){if(b.status==="aborted")return Re;b.status==="dirty"&&t.dirty(),g.add(b.value)}return{status:t.value,value:g}}let u=[...n.data.values()].map((f,g)=>s._parse(new Pr(n,f,n.path,g)));return n.common.async?Promise.all(u).then(f=>i(f)):i(u)}min(e,t){return new r({...this._def,minSize:{value:e,message:Se.toString(t)}})}max(e,t){return new r({...this._def,maxSize:{value:e,message:Se.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};Bs.create=(r,e)=>new Bs({valueType:r,minSize:null,maxSize:null,typeName:Ae.ZodSet,...Ne(e)});Ua=class r extends Ce{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==de.function)return he(t,{code:ne.invalid_type,expected:de.function,received:t.parsedType}),Re;function n(u,f){return Na({data:u,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,La(),ks].filter(g=>!!g),issueData:{code:ne.invalid_arguments,argumentsError:f}})}function o(u,f){return Na({data:u,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,La(),ks].filter(g=>!!g),issueData:{code:ne.invalid_return_type,returnTypeError:f}})}let s={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof Gn){let u=this;return ur(async function(...f){let g=new Ht([]),b=await u._def.args.parseAsync(f,s).catch(C=>{throw g.addIssue(n(f,C)),g}),E=await Reflect.apply(i,this,b);return await u._def.returns._def.type.parseAsync(E,s).catch(C=>{throw g.addIssue(o(E,C)),g})})}else{let u=this;return ur(function(...f){let g=u._def.args.safeParse(f,s);if(!g.success)throw new Ht([n(f,g.error)]);let b=Reflect.apply(i,this,g.data),E=u._def.returns.safeParse(b,s);if(!E.success)throw new Ht([o(b,E.error)]);return E.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new r({...this._def,args:Jr.create(e).rest(gn.create())})}returns(e){return new r({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,n){return new r({args:e||Jr.create([]).rest(gn.create()),returns:t||gn.create(),typeName:Ae.ZodFunction,...Ne(n)})}},Yo=class extends Ce{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}};Yo.create=(r,e)=>new Yo({getter:r,typeName:Ae.ZodLazy,...Ne(e)});$o=class extends Ce{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return he(t,{received:t.data,code:ne.invalid_literal,expected:this._def.value}),Re}return{status:"valid",value:e.data}}get value(){return this._def.value}};$o.create=(r,e)=>new $o({value:r,typeName:Ae.ZodLiteral,...Ne(e)});Jo=class r extends Ce{constructor(){super(...arguments),_i.set(this,void 0)}_parse(e){if(typeof e.data!="string"){let t=this._getOrReturnCtx(e),n=this._def.values;return he(t,{expected:je.joinValues(n),received:t.parsedType,code:ne.invalid_type}),Re}if(Ca(this,_i,"f")||Sy(this,_i,new Set(this._def.values),"f"),!Ca(this,_i,"f").has(e.data)){let t=this._getOrReturnCtx(e),n=this._def.values;return he(t,{received:t.data,code:ne.invalid_enum_value,options:n}),Re}return ur(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return r.create(e,{...this._def,...t})}exclude(e,t=this._def){return r.create(this.options.filter(n=>!e.includes(n)),{...this._def,...t})}};_i=new WeakMap;Jo.create=Ay;Qo=class extends Ce{constructor(){super(...arguments),ki.set(this,void 0)}_parse(e){let t=je.getValidEnumValues(this._def.values),n=this._getOrReturnCtx(e);if(n.parsedType!==de.string&&n.parsedType!==de.number){let o=je.objectValues(t);return he(n,{expected:je.joinValues(o),received:n.parsedType,code:ne.invalid_type}),Re}if(Ca(this,ki,"f")||Sy(this,ki,new Set(je.getValidEnumValues(this._def.values)),"f"),!Ca(this,ki,"f").has(e.data)){let o=je.objectValues(t);return he(n,{received:n.data,code:ne.invalid_enum_value,options:o}),Re}return ur(e.data)}get enum(){return this._def.values}};ki=new WeakMap;Qo.create=(r,e)=>new Qo({values:r,typeName:Ae.ZodNativeEnum,...Ne(e)});Gn=class extends Ce{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==de.promise&&t.common.async===!1)return he(t,{code:ne.invalid_type,expected:de.promise,received:t.parsedType}),Re;let n=t.parsedType===de.promise?t.data:Promise.resolve(t.data);return ur(n.then(o=>this._def.type.parseAsync(o,{path:t.path,errorMap:t.common.contextualErrorMap})))}};Gn.create=(r,e)=>new Gn({type:r,typeName:Ae.ZodPromise,...Ne(e)});br=class extends Ce{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===Ae.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:n}=this._processInputParams(e),o=this._def.effect||null,s={addIssue:i=>{he(n,i),i.fatal?t.abort():t.dirty()},get path(){return n.path}};if(s.addIssue=s.addIssue.bind(s),o.type==="preprocess"){let i=o.transform(n.data,s);if(n.common.async)return Promise.resolve(i).then(async u=>{if(t.value==="aborted")return Re;let f=await this._def.schema._parseAsync({data:u,path:n.path,parent:n});return f.status==="aborted"?Re:f.status==="dirty"||t.value==="dirty"?_s(f.value):f});{if(t.value==="aborted")return Re;let u=this._def.schema._parseSync({data:i,path:n.path,parent:n});return u.status==="aborted"?Re:u.status==="dirty"||t.value==="dirty"?_s(u.value):u}}if(o.type==="refinement"){let i=u=>{let f=o.refinement(u,s);if(n.common.async)return Promise.resolve(f);if(f instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return u};if(n.common.async===!1){let u=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});return u.status==="aborted"?Re:(u.status==="dirty"&&t.dirty(),i(u.value),{status:t.value,value:u.value})}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(u=>u.status==="aborted"?Re:(u.status==="dirty"&&t.dirty(),i(u.value).then(()=>({status:t.value,value:u.value}))))}if(o.type==="transform")if(n.common.async===!1){let i=this._def.schema._parseSync({data:n.data,path:n.path,parent:n});if(!Ai(i))return i;let u=o.transform(i.value,s);if(u instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:u}}else return this._def.schema._parseAsync({data:n.data,path:n.path,parent:n}).then(i=>Ai(i)?Promise.resolve(o.transform(i.value,s)).then(u=>({status:t.value,value:u})):i);je.assertNever(o)}};br.create=(r,e,t)=>new br({schema:r,typeName:Ae.ZodEffects,effect:e,...Ne(t)});br.createWithPreprocess=(r,e,t)=>new br({schema:e,effect:{type:"preprocess",transform:r},typeName:Ae.ZodEffects,...Ne(t)});Br=class extends Ce{_parse(e){return this._getType(e)===de.undefined?ur(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};Br.create=(r,e)=>new Br({innerType:r,typeName:Ae.ZodOptional,...Ne(e)});Qr=class extends Ce{_parse(e){return this._getType(e)===de.null?ur(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};Qr.create=(r,e)=>new Qr({innerType:r,typeName:Ae.ZodNullable,...Ne(e)});Xo=class extends Ce{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return t.parsedType===de.undefined&&(n=this._def.defaultValue()),this._def.innerType._parse({data:n,path:t.path,parent:t})}removeDefault(){return this._def.innerType}};Xo.create=(r,e)=>new Xo({innerType:r,typeName:Ae.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...Ne(e)});es=class extends Ce{_parse(e){let{ctx:t}=this._processInputParams(e),n={...t,common:{...t.common,issues:[]}},o=this._def.innerType._parse({data:n.data,path:n.path,parent:{...n}});return Ii(o)?o.then(s=>({status:"valid",value:s.status==="valid"?s.value:this._def.catchValue({get error(){return new Ht(n.common.issues)},input:n.data})})):{status:"valid",value:o.status==="valid"?o.value:this._def.catchValue({get error(){return new Ht(n.common.issues)},input:n.data})}}removeCatch(){return this._def.innerType}};es.create=(r,e)=>new es({innerType:r,typeName:Ae.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...Ne(e)});Ps=class extends Ce{_parse(e){if(this._getType(e)!==de.nan){let n=this._getOrReturnCtx(e);return he(n,{code:ne.invalid_type,expected:de.nan,received:n.parsedType}),Re}return{status:"valid",value:e.data}}};Ps.create=r=>new Ps({typeName:Ae.ZodNaN,...Ne(r)});WA=Symbol("zod_brand"),Ri=class extends Ce{_parse(e){let{ctx:t}=this._processInputParams(e),n=t.data;return this._def.type._parse({data:n,path:t.path,parent:t})}unwrap(){return this._def.type}},Bi=class r extends Ce{_parse(e){let{status:t,ctx:n}=this._processInputParams(e);if(n.common.async)return(async()=>{let s=await this._def.in._parseAsync({data:n.data,path:n.path,parent:n});return s.status==="aborted"?Re:s.status==="dirty"?(t.dirty(),_s(s.value)):this._def.out._parseAsync({data:s.value,path:n.path,parent:n})})();{let o=this._def.in._parseSync({data:n.data,path:n.path,parent:n});return o.status==="aborted"?Re:o.status==="dirty"?(t.dirty(),{status:"dirty",value:o.value}):this._def.out._parseSync({data:o.value,path:n.path,parent:n})}}static create(e,t){return new r({in:e,out:t,typeName:Ae.ZodPipeline})}},ts=class extends Ce{_parse(e){let t=this._def.innerType._parse(e),n=o=>(Ai(o)&&(o.value=Object.freeze(o.value)),o);return Ii(t)?t.then(o=>n(o)):n(t)}unwrap(){return this._def.innerType}};ts.create=(r,e)=>new ts({innerType:r,typeName:Ae.ZodReadonly,...Ne(e)});KA={object:yr.lazycreate};(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(Ae||(Ae={}));VA=(r,e={message:`Input not instance of ${r.name}`})=>Iy(t=>t instanceof r,e),Ry=Kn.create,By=Fo.create,GA=Ps.create,ZA=jo.create,Py=Ho.create,YA=Wo.create,$A=As.create,JA=Ko.create,QA=Vo.create,XA=Vn.create,eI=gn.create,tI=qr.create,rI=Is.create,nI=xn.create,oI=yr.create,sI=yr.strictCreate,iI=Go.create,aI=Oa.create,cI=Zo.create,uI=Jr.create,pI=qa.create,fI=Rs.create,lI=Bs.create,hI=Ua.create,dI=Yo.create,yI=$o.create,mI=Jo.create,gI=Qo.create,xI=Gn.create,by=br.create,wI=Br.create,bI=Qr.create,vI=br.createWithPreprocess,SI=Bi.create,EI=()=>Ry().optional(),_I=()=>By().optional(),kI=()=>Py().optional(),AI={string:r=>Kn.create({...r,coerce:!0}),number:r=>Fo.create({...r,coerce:!0}),boolean:r=>Ho.create({...r,coerce:!0}),bigint:r=>jo.create({...r,coerce:!0}),date:r=>Wo.create({...r,coerce:!0})},II=Re,a=Object.freeze({__proto__:null,defaultErrorMap:ks,setErrorMap:IA,getErrorMap:La,makeIssue:Na,EMPTY_PATH:RA,addIssueToContext:he,ParseStatus:Xt,INVALID:Re,DIRTY:_s,OK:ur,isAborted:Np,isDirty:Cp,isValid:Ai,isAsync:Ii,get util(){return je},get objectUtil(){return Lp},ZodParsedType:de,getParsedType:Wn,ZodType:Ce,datetimeRegex:ky,ZodString:Kn,ZodNumber:Fo,ZodBigInt:jo,ZodBoolean:Ho,ZodDate:Wo,ZodSymbol:As,ZodUndefined:Ko,ZodNull:Vo,ZodAny:Vn,ZodUnknown:gn,ZodNever:qr,ZodVoid:Is,ZodArray:xn,ZodObject:yr,ZodUnion:Go,ZodDiscriminatedUnion:Oa,ZodIntersection:Zo,ZodTuple:Jr,ZodRecord:qa,ZodMap:Rs,ZodSet:Bs,ZodFunction:Ua,ZodLazy:Yo,ZodLiteral:$o,ZodEnum:Jo,ZodNativeEnum:Qo,ZodPromise:Gn,ZodEffects:br,ZodTransformer:br,ZodOptional:Br,ZodNullable:Qr,ZodDefault:Xo,ZodCatch:es,ZodNaN:Ps,BRAND:WA,ZodBranded:Ri,ZodPipeline:Bi,ZodReadonly:ts,custom:Iy,Schema:Ce,ZodSchema:Ce,late:KA,get ZodFirstPartyTypeKind(){return Ae},coerce:AI,any:XA,array:nI,bigint:ZA,boolean:Py,date:YA,discriminatedUnion:aI,effect:by,enum:mI,function:hI,instanceof:VA,intersection:cI,lazy:dI,literal:yI,map:fI,nan:GA,nativeEnum:gI,never:tI,null:QA,nullable:bI,number:By,object:oI,oboolean:kI,onumber:_I,optional:wI,ostring:EI,pipeline:SI,preprocess:vI,promise:xI,record:pI,set:lI,strictObject:sI,string:Ry,symbol:$A,transformer:by,tuple:uI,undefined:JA,union:iI,unknown:eI,void:rI,NEVER:II,ZodIssueCode:ne,quotelessJson:AA,ZodError:Ht})});var Ty,zy=O(()=>{y();Ty="logger/5.7.0"});function RI(){try{let r=[];if(["NFD","NFC","NFKD","NFKC"].forEach(e=>{try{if("test".normalize(e)!=="test")throw new Error("bad normalize")}catch{r.push(e)}}),r.length)throw new Error("missing "+r.join(", "));if("\xE9".normalize("NFD")!=="e\u0301")throw new Error("broken implementation")}catch(r){return r.message}return null}var My,Ly,Da,Ny,qp,Cy,Up,Ur,Oy,Xr,Fa=O(()=>{"use strict";y();zy();My=!1,Ly=!1,Da={debug:1,default:2,info:2,warning:3,error:4,off:5},Ny=Da.default,qp=null;Cy=RI();(function(r){r.DEBUG="DEBUG",r.INFO="INFO",r.WARNING="WARNING",r.ERROR="ERROR",r.OFF="OFF"})(Up||(Up={}));(function(r){r.UNKNOWN_ERROR="UNKNOWN_ERROR",r.NOT_IMPLEMENTED="NOT_IMPLEMENTED",r.UNSUPPORTED_OPERATION="UNSUPPORTED_OPERATION",r.NETWORK_ERROR="NETWORK_ERROR",r.SERVER_ERROR="SERVER_ERROR",r.TIMEOUT="TIMEOUT",r.BUFFER_OVERRUN="BUFFER_OVERRUN",r.NUMERIC_FAULT="NUMERIC_FAULT",r.MISSING_NEW="MISSING_NEW",r.INVALID_ARGUMENT="INVALID_ARGUMENT",r.MISSING_ARGUMENT="MISSING_ARGUMENT",r.UNEXPECTED_ARGUMENT="UNEXPECTED_ARGUMENT",r.CALL_EXCEPTION="CALL_EXCEPTION",r.INSUFFICIENT_FUNDS="INSUFFICIENT_FUNDS",r.NONCE_EXPIRED="NONCE_EXPIRED",r.REPLACEMENT_UNDERPRICED="REPLACEMENT_UNDERPRICED",r.UNPREDICTABLE_GAS_LIMIT="UNPREDICTABLE_GAS_LIMIT",r.TRANSACTION_REPLACED="TRANSACTION_REPLACED",r.ACTION_REJECTED="ACTION_REJECTED"})(Ur||(Ur={}));Oy="0123456789abcdef",Xr=class r{constructor(e){Object.defineProperty(this,"version",{enumerable:!0,value:e,writable:!1})}_log(e,t){let n=e.toLowerCase();Da[n]==null&&this.throwArgumentError("invalid log level name","logLevel",e),!(Ny>Da[n])&&console.log.apply(console,t)}debug(...e){this._log(r.levels.DEBUG,e)}info(...e){this._log(r.levels.INFO,e)}warn(...e){this._log(r.levels.WARNING,e)}makeError(e,t,n){if(Ly)return this.makeError("censored error",t,{});t||(t=r.errors.UNKNOWN_ERROR),n||(n={});let o=[];Object.keys(n).forEach(f=>{let g=n[f];try{if(g instanceof Uint8Array){let b="";for(let E=0;E<g.length;E++)b+=Oy[g[E]>>4],b+=Oy[g[E]&15];o.push(f+"=Uint8Array(0x"+b+")")}else o.push(f+"="+JSON.stringify(g))}catch{o.push(f+"="+JSON.stringify(n[f].toString()))}}),o.push(`code=${t}`),o.push(`version=${this.version}`);let s=e,i="";switch(t){case Ur.NUMERIC_FAULT:{i="NUMERIC_FAULT";let f=e;switch(f){case"overflow":case"underflow":case"division-by-zero":i+="-"+f;break;case"negative-power":case"negative-width":i+="-unsupported";break;case"unbound-bitwise-result":i+="-unbound-result";break}break}case Ur.CALL_EXCEPTION:case Ur.INSUFFICIENT_FUNDS:case Ur.MISSING_NEW:case Ur.NONCE_EXPIRED:case Ur.REPLACEMENT_UNDERPRICED:case Ur.TRANSACTION_REPLACED:case Ur.UNPREDICTABLE_GAS_LIMIT:i=t;break}i&&(e+=" [ See: https://links.ethers.org/v5-errors-"+i+" ]"),o.length&&(e+=" ("+o.join(", ")+")");let u=new Error(e);return u.reason=s,u.code=t,Object.keys(n).forEach(function(f){u[f]=n[f]}),u}throwError(e,t,n){throw this.makeError(e,t,n)}throwArgumentError(e,t,n){return this.throwError(e,r.errors.INVALID_ARGUMENT,{argument:t,value:n})}assert(e,t,n,o){e||this.throwError(t,n,o)}assertArgument(e,t,n,o){e||this.throwArgumentError(t,n,o)}checkNormalize(e){e==null&&(e="platform missing String.prototype.normalize"),Cy&&this.throwError("platform missing String.prototype.normalize",r.errors.UNSUPPORTED_OPERATION,{operation:"String.prototype.normalize",form:Cy})}checkSafeUint53(e,t){typeof e=="number"&&(t==null&&(t="value not safe"),(e<0||e>=9007199254740991)&&this.throwError(t,r.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"out-of-safe-range",value:e}),e%1&&this.throwError(t,r.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"non-integer",value:e}))}checkArgumentCount(e,t,n){n?n=": "+n:n="",e<t&&this.throwError("missing argument"+n,r.errors.MISSING_ARGUMENT,{count:e,expectedCount:t}),e>t&&this.throwError("too many arguments"+n,r.errors.UNEXPECTED_ARGUMENT,{count:e,expectedCount:t})}checkNew(e,t){(e===Object||e==null)&&this.throwError("missing new",r.errors.MISSING_NEW,{name:t.name})}checkAbstract(e,t){e===t?this.throwError("cannot instantiate abstract class "+JSON.stringify(t.name)+" directly; use a sub-class",r.errors.UNSUPPORTED_OPERATION,{name:e.name,operation:"new"}):(e===Object||e==null)&&this.throwError("missing new",r.errors.MISSING_NEW,{name:t.name})}static globalLogger(){return qp||(qp=new r(Ty)),qp}static setCensorship(e,t){if(!e&&t&&this.globalLogger().throwError("cannot permanently disable censorship",r.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"}),My){if(!e)return;this.globalLogger().throwError("error censorship permanent",r.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"})}Ly=!!e,My=!!t}static setLogLevel(e){let t=Da[e.toLowerCase()];if(t==null){r.globalLogger().warn("invalid log level - "+e);return}Ny=t}static from(e){return new r(e)}};Xr.errors=Ur;Xr.levels=Up});var qy,Uy=O(()=>{y();qy="bytes/5.7.0"});function BI(r){return!!r.toHexString}function ja(r){return r.slice||(r.slice=function(){let e=Array.prototype.slice.call(arguments);return ja(new Uint8Array(Array.prototype.slice.apply(r,e)))}),r}function Dy(r){return typeof r=="number"&&r==r&&r%1===0}function PI(r){if(r==null)return!1;if(r.constructor===Uint8Array)return!0;if(typeof r=="string"||!Dy(r.length)||r.length<0)return!1;for(let e=0;e<r.length;e++){let t=r[e];if(!Dy(t)||t<0||t>=256)return!1}return!0}function Ha(r,e){if(e||(e={}),typeof r=="number"){Dp.checkSafeUint53(r,"invalid arrayify value");let t=[];for(;r;)t.unshift(r&255),r=parseInt(String(r/256));return t.length===0&&t.push(0),ja(new Uint8Array(t))}if(e.allowMissingPrefix&&typeof r=="string"&&r.substring(0,2)!=="0x"&&(r="0x"+r),BI(r)&&(r=r.toHexString()),Fp(r)){let t=r.substring(2);t.length%2&&(e.hexPad==="left"?t="0"+t:e.hexPad==="right"?t+="0":Dp.throwArgumentError("hex data is odd-length","value",r));let n=[];for(let o=0;o<t.length;o+=2)n.push(parseInt(t.substring(o,o+2),16));return ja(new Uint8Array(n))}return PI(r)?ja(new Uint8Array(r)):Dp.throwArgumentError("invalid arrayify value","value",r)}function Fp(r,e){return!(typeof r!="string"||!r.match(/^0x[0-9A-Fa-f]*$/)||e&&r.length!==2+2*e)}var Dp,jp=O(()=>{"use strict";y();Fa();Uy();Dp=new Xr(qy)});var Fy=_e(()=>{y()});var Wa=_e((jy,Hp)=>{y();(function(r,e){"use strict";function t(m,c){if(!m)throw new Error(c||"Assertion failed")}function n(m,c){m.super_=c;var p=function(){};p.prototype=c.prototype,m.prototype=new p,m.prototype.constructor=m}function o(m,c,p){if(o.isBN(m))return m;this.negative=0,this.words=null,this.length=0,this.red=null,m!==null&&((c==="le"||c==="be")&&(p=c,c=10),this._init(m||0,c||10,p||"be"))}typeof r=="object"?r.exports=o:e.BN=o,o.BN=o,o.wordSize=26;var s;try{typeof window<"u"&&typeof window.Buffer<"u"?s=window.Buffer:s=Fy().Buffer}catch{}o.isBN=function(c){return c instanceof o?!0:c!==null&&typeof c=="object"&&c.constructor.wordSize===o.wordSize&&Array.isArray(c.words)},o.max=function(c,p){return c.cmp(p)>0?c:p},o.min=function(c,p){return c.cmp(p)<0?c:p},o.prototype._init=function(c,p,h){if(typeof c=="number")return this._initNumber(c,p,h);if(typeof c=="object")return this._initArray(c,p,h);p==="hex"&&(p=16),t(p===(p|0)&&p>=2&&p<=36),c=c.toString().replace(/\s+/g,"");var d=0;c[0]==="-"&&(d++,this.negative=1),d<c.length&&(p===16?this._parseHex(c,d,h):(this._parseBase(c,p,d),h==="le"&&this._initArray(this.toArray(),p,h)))},o.prototype._initNumber=function(c,p,h){c<0&&(this.negative=1,c=-c),c<67108864?(this.words=[c&67108863],this.length=1):c<4503599627370496?(this.words=[c&67108863,c/67108864&67108863],this.length=2):(t(c<9007199254740992),this.words=[c&67108863,c/67108864&67108863,1],this.length=3),h==="le"&&this._initArray(this.toArray(),p,h)},o.prototype._initArray=function(c,p,h){if(t(typeof c.length=="number"),c.length<=0)return this.words=[0],this.length=1,this;this.length=Math.ceil(c.length/3),this.words=new Array(this.length);for(var d=0;d<this.length;d++)this.words[d]=0;var x,S,P=0;if(h==="be")for(d=c.length-1,x=0;d>=0;d-=3)S=c[d]|c[d-1]<<8|c[d-2]<<16,this.words[x]|=S<<P&67108863,this.words[x+1]=S>>>26-P&67108863,P+=24,P>=26&&(P-=26,x++);else if(h==="le")for(d=0,x=0;d<c.length;d+=3)S=c[d]|c[d+1]<<8|c[d+2]<<16,this.words[x]|=S<<P&67108863,this.words[x+1]=S>>>26-P&67108863,P+=24,P>=26&&(P-=26,x++);return this._strip()};function i(m,c){var p=m.charCodeAt(c);if(p>=48&&p<=57)return p-48;if(p>=65&&p<=70)return p-55;if(p>=97&&p<=102)return p-87;t(!1,"Invalid character in "+m)}function u(m,c,p){var h=i(m,p);return p-1>=c&&(h|=i(m,p-1)<<4),h}o.prototype._parseHex=function(c,p,h){this.length=Math.ceil((c.length-p)/6),this.words=new Array(this.length);for(var d=0;d<this.length;d++)this.words[d]=0;var x=0,S=0,P;if(h==="be")for(d=c.length-1;d>=p;d-=2)P=u(c,p,d)<<x,this.words[S]|=P&67108863,x>=18?(x-=18,S+=1,this.words[S]|=P>>>26):x+=8;else{var v=c.length-p;for(d=v%2===0?p+1:p;d<c.length;d+=2)P=u(c,p,d)<<x,this.words[S]|=P&67108863,x>=18?(x-=18,S+=1,this.words[S]|=P>>>26):x+=8}this._strip()};function f(m,c,p,h){for(var d=0,x=0,S=Math.min(m.length,p),P=c;P<S;P++){var v=m.charCodeAt(P)-48;d*=h,v>=49?x=v-49+10:v>=17?x=v-17+10:x=v,t(v>=0&&x<h,"Invalid character"),d+=x}return d}o.prototype._parseBase=function(c,p,h){this.words=[0],this.length=1;for(var d=0,x=1;x<=67108863;x*=p)d++;d--,x=x/p|0;for(var S=c.length-h,P=S%d,v=Math.min(S,S-P)+h,l=0,k=h;k<v;k+=d)l=f(c,k,k+d,p),this.imuln(x),this.words[0]+l<67108864?this.words[0]+=l:this._iaddn(l);if(P!==0){var se=1;for(l=f(c,k,c.length,p),k=0;k<P;k++)se*=p;this.imuln(se),this.words[0]+l<67108864?this.words[0]+=l:this._iaddn(l)}this._strip()},o.prototype.copy=function(c){c.words=new Array(this.length);for(var p=0;p<this.length;p++)c.words[p]=this.words[p];c.length=this.length,c.negative=this.negative,c.red=this.red};function g(m,c){m.words=c.words,m.length=c.length,m.negative=c.negative,m.red=c.red}if(o.prototype._move=function(c){g(c,this)},o.prototype.clone=function(){var c=new o(null);return this.copy(c),c},o.prototype._expand=function(c){for(;this.length<c;)this.words[this.length++]=0;return this},o.prototype._strip=function(){for(;this.length>1&&this.words[this.length-1]===0;)this.length--;return this._normSign()},o.prototype._normSign=function(){return this.length===1&&this.words[0]===0&&(this.negative=0),this},typeof Symbol<"u"&&typeof Symbol.for=="function")try{o.prototype[Symbol.for("nodejs.util.inspect.custom")]=b}catch{o.prototype.inspect=b}else o.prototype.inspect=b;function b(){return(this.red?"<BN-R: ":"<BN: ")+this.toString(16)+">"}var E=["","0","00","000","0000","00000","000000","0000000","00000000","000000000","0000000000","00000000000","000000000000","0000000000000","00000000000000","000000000000000","0000000000000000","00000000000000000","000000000000000000","0000000000000000000","00000000000000000000","000000000000000000000","0000000000000000000000","00000000000000000000000","000000000000000000000000","0000000000000000000000000"],q=[0,0,25,16,12,11,10,9,8,8,7,7,7,7,6,6,6,6,6,6,6,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5],C=[0,0,33554432,43046721,16777216,48828125,60466176,40353607,16777216,43046721,1e7,19487171,35831808,62748517,7529536,11390625,16777216,24137569,34012224,47045881,64e6,4084101,5153632,6436343,7962624,9765625,11881376,14348907,17210368,20511149,243e5,28629151,33554432,39135393,45435424,52521875,60466176];o.prototype.toString=function(c,p){c=c||10,p=p|0||1;var h;if(c===16||c==="hex"){h="";for(var d=0,x=0,S=0;S<this.length;S++){var P=this.words[S],v=((P<<d|x)&16777215).toString(16);x=P>>>24-d&16777215,d+=2,d>=26&&(d-=26,S--),x!==0||S!==this.length-1?h=E[6-v.length]+v+h:h=v+h}for(x!==0&&(h=x.toString(16)+h);h.length%p!==0;)h="0"+h;return this.negative!==0&&(h="-"+h),h}if(c===(c|0)&&c>=2&&c<=36){var l=q[c],k=C[c];h="";var se=this.clone();for(se.negative=0;!se.isZero();){var w=se.modrn(k).toString(c);se=se.idivn(k),se.isZero()?h=w+h:h=E[l-w.length]+w+h}for(this.isZero()&&(h="0"+h);h.length%p!==0;)h="0"+h;return this.negative!==0&&(h="-"+h),h}t(!1,"Base should be between 2 and 36")},o.prototype.toNumber=function(){var c=this.words[0];return this.length===2?c+=this.words[1]*67108864:this.length===3&&this.words[2]===1?c+=4503599627370496+this.words[1]*67108864:this.length>2&&t(!1,"Number can only safely store up to 53 bits"),this.negative!==0?-c:c},o.prototype.toJSON=function(){return this.toString(16,2)},s&&(o.prototype.toBuffer=function(c,p){return this.toArrayLike(s,c,p)}),o.prototype.toArray=function(c,p){return this.toArrayLike(Array,c,p)};var K=function(c,p){return c.allocUnsafe?c.allocUnsafe(p):new c(p)};o.prototype.toArrayLike=function(c,p,h){this._strip();var d=this.byteLength(),x=h||Math.max(1,d);t(d<=x,"byte array longer than desired length"),t(x>0,"Requested array length <= 0");var S=K(c,x),P=p==="le"?"LE":"BE";return this["_toArrayLike"+P](S,d),S},o.prototype._toArrayLikeLE=function(c,p){for(var h=0,d=0,x=0,S=0;x<this.length;x++){var P=this.words[x]<<S|d;c[h++]=P&255,h<c.length&&(c[h++]=P>>8&255),h<c.length&&(c[h++]=P>>16&255),S===6?(h<c.length&&(c[h++]=P>>24&255),d=0,S=0):(d=P>>>24,S+=2)}if(h<c.length)for(c[h++]=d;h<c.length;)c[h++]=0},o.prototype._toArrayLikeBE=function(c,p){for(var h=c.length-1,d=0,x=0,S=0;x<this.length;x++){var P=this.words[x]<<S|d;c[h--]=P&255,h>=0&&(c[h--]=P>>8&255),h>=0&&(c[h--]=P>>16&255),S===6?(h>=0&&(c[h--]=P>>24&255),d=0,S=0):(d=P>>>24,S+=2)}if(h>=0)for(c[h--]=d;h>=0;)c[h--]=0},Math.clz32?o.prototype._countBits=function(c){return 32-Math.clz32(c)}:o.prototype._countBits=function(c){var p=c,h=0;return p>=4096&&(h+=13,p>>>=13),p>=64&&(h+=7,p>>>=7),p>=8&&(h+=4,p>>>=4),p>=2&&(h+=2,p>>>=2),h+p},o.prototype._zeroBits=function(c){if(c===0)return 26;var p=c,h=0;return p&8191||(h+=13,p>>>=13),p&127||(h+=7,p>>>=7),p&15||(h+=4,p>>>=4),p&3||(h+=2,p>>>=2),p&1||h++,h},o.prototype.bitLength=function(){var c=this.words[this.length-1],p=this._countBits(c);return(this.length-1)*26+p};function ee(m){for(var c=new Array(m.bitLength()),p=0;p<c.length;p++){var h=p/26|0,d=p%26;c[p]=m.words[h]>>>d&1}return c}o.prototype.zeroBits=function(){if(this.isZero())return 0;for(var c=0,p=0;p<this.length;p++){var h=this._zeroBits(this.words[p]);if(c+=h,h!==26)break}return c},o.prototype.byteLength=function(){return Math.ceil(this.bitLength()/8)},o.prototype.toTwos=function(c){return this.negative!==0?this.abs().inotn(c).iaddn(1):this.clone()},o.prototype.fromTwos=function(c){return this.testn(c-1)?this.notn(c).iaddn(1).ineg():this.clone()},o.prototype.isNeg=function(){return this.negative!==0},o.prototype.neg=function(){return this.clone().ineg()},o.prototype.ineg=function(){return this.isZero()||(this.negative^=1),this},o.prototype.iuor=function(c){for(;this.length<c.length;)this.words[this.length++]=0;for(var p=0;p<c.length;p++)this.words[p]=this.words[p]|c.words[p];return this._strip()},o.prototype.ior=function(c){return t((this.negative|c.negative)===0),this.iuor(c)},o.prototype.or=function(c){return this.length>c.length?this.clone().ior(c):c.clone().ior(this)},o.prototype.uor=function(c){return this.length>c.length?this.clone().iuor(c):c.clone().iuor(this)},o.prototype.iuand=function(c){var p;this.length>c.length?p=c:p=this;for(var h=0;h<p.length;h++)this.words[h]=this.words[h]&c.words[h];return this.length=p.length,this._strip()},o.prototype.iand=function(c){return t((this.negative|c.negative)===0),this.iuand(c)},o.prototype.and=function(c){return this.length>c.length?this.clone().iand(c):c.clone().iand(this)},o.prototype.uand=function(c){return this.length>c.length?this.clone().iuand(c):c.clone().iuand(this)},o.prototype.iuxor=function(c){var p,h;this.length>c.length?(p=this,h=c):(p=c,h=this);for(var d=0;d<h.length;d++)this.words[d]=p.words[d]^h.words[d];if(this!==p)for(;d<p.length;d++)this.words[d]=p.words[d];return this.length=p.length,this._strip()},o.prototype.ixor=function(c){return t((this.negative|c.negative)===0),this.iuxor(c)},o.prototype.xor=function(c){return this.length>c.length?this.clone().ixor(c):c.clone().ixor(this)},o.prototype.uxor=function(c){return this.length>c.length?this.clone().iuxor(c):c.clone().iuxor(this)},o.prototype.inotn=function(c){t(typeof c=="number"&&c>=0);var p=Math.ceil(c/26)|0,h=c%26;this._expand(p),h>0&&p--;for(var d=0;d<p;d++)this.words[d]=~this.words[d]&67108863;return h>0&&(this.words[d]=~this.words[d]&67108863>>26-h),this._strip()},o.prototype.notn=function(c){return this.clone().inotn(c)},o.prototype.setn=function(c,p){t(typeof c=="number"&&c>=0);var h=c/26|0,d=c%26;return this._expand(h+1),p?this.words[h]=this.words[h]|1<<d:this.words[h]=this.words[h]&~(1<<d),this._strip()},o.prototype.iadd=function(c){var p;if(this.negative!==0&&c.negative===0)return this.negative=0,p=this.isub(c),this.negative^=1,this._normSign();if(this.negative===0&&c.negative!==0)return c.negative=0,p=this.isub(c),c.negative=1,p._normSign();var h,d;this.length>c.length?(h=this,d=c):(h=c,d=this);for(var x=0,S=0;S<d.length;S++)p=(h.words[S]|0)+(d.words[S]|0)+x,this.words[S]=p&67108863,x=p>>>26;for(;x!==0&&S<h.length;S++)p=(h.words[S]|0)+x,this.words[S]=p&67108863,x=p>>>26;if(this.length=h.length,x!==0)this.words[this.length]=x,this.length++;else if(h!==this)for(;S<h.length;S++)this.words[S]=h.words[S];return this},o.prototype.add=function(c){var p;return c.negative!==0&&this.negative===0?(c.negative=0,p=this.sub(c),c.negative^=1,p):c.negative===0&&this.negative!==0?(this.negative=0,p=c.sub(this),this.negative=1,p):this.length>c.length?this.clone().iadd(c):c.clone().iadd(this)},o.prototype.isub=function(c){if(c.negative!==0){c.negative=0;var p=this.iadd(c);return c.negative=1,p._normSign()}else if(this.negative!==0)return this.negative=0,this.iadd(c),this.negative=1,this._normSign();var h=this.cmp(c);if(h===0)return this.negative=0,this.length=1,this.words[0]=0,this;var d,x;h>0?(d=this,x=c):(d=c,x=this);for(var S=0,P=0;P<x.length;P++)p=(d.words[P]|0)-(x.words[P]|0)+S,S=p>>26,this.words[P]=p&67108863;for(;S!==0&&P<d.length;P++)p=(d.words[P]|0)+S,S=p>>26,this.words[P]=p&67108863;if(S===0&&P<d.length&&d!==this)for(;P<d.length;P++)this.words[P]=d.words[P];return this.length=Math.max(this.length,P),d!==this&&(this.negative=1),this._strip()},o.prototype.sub=function(c){return this.clone().isub(c)};function $(m,c,p){p.negative=c.negative^m.negative;var h=m.length+c.length|0;p.length=h,h=h-1|0;var d=m.words[0]|0,x=c.words[0]|0,S=d*x,P=S&67108863,v=S/67108864|0;p.words[0]=P;for(var l=1;l<h;l++){for(var k=v>>>26,se=v&67108863,w=Math.min(l,c.length-1),U=Math.max(0,l-m.length+1);U<=w;U++){var F=l-U|0;d=m.words[F]|0,x=c.words[U]|0,S=d*x+se,k+=S/67108864|0,se=S&67108863}p.words[l]=se|0,v=k|0}return v!==0?p.words[l]=v|0:p.length--,p._strip()}var le=function(c,p,h){var d=c.words,x=p.words,S=h.words,P=0,v,l,k,se=d[0]|0,w=se&8191,U=se>>>13,F=d[1]|0,X=F&8191,oe=F>>>13,we=d[2]|0,ie=we&8191,ae=we>>>13,qe=d[3]|0,ce=qe&8191,me=qe>>>13,So=d[4]|0,rt=So&8191,nt=So>>>13,Eo=d[5]|0,ot=Eo&8191,st=Eo>>>13,_o=d[6]|0,it=_o&8191,at=_o>>>13,ko=d[7]|0,ct=ko&8191,ut=ko>>>13,Ao=d[8]|0,pt=Ao&8191,ft=Ao>>>13,Io=d[9]|0,lt=Io&8191,ht=Io>>>13,Ro=x[0]|0,dt=Ro&8191,yt=Ro>>>13,Bo=x[1]|0,mt=Bo&8191,gt=Bo>>>13,Po=x[2]|0,xt=Po&8191,wt=Po>>>13,To=x[3]|0,bt=To&8191,vt=To>>>13,zo=x[4]|0,St=zo&8191,Et=zo>>>13,Mo=x[5]|0,_t=Mo&8191,kt=Mo>>>13,Lo=x[6]|0,At=Lo&8191,It=Lo>>>13,No=x[7]|0,Rt=No&8191,Bt=No>>>13,Co=x[8]|0,Pt=Co&8191,Tt=Co>>>13,Oo=x[9]|0,zt=Oo&8191,Mt=Oo>>>13;h.negative=c.negative^p.negative,h.length=19,v=Math.imul(w,dt),l=Math.imul(w,yt),l=l+Math.imul(U,dt)|0,k=Math.imul(U,yt);var On=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(On>>>26)|0,On&=67108863,v=Math.imul(X,dt),l=Math.imul(X,yt),l=l+Math.imul(oe,dt)|0,k=Math.imul(oe,yt),v=v+Math.imul(w,mt)|0,l=l+Math.imul(w,gt)|0,l=l+Math.imul(U,mt)|0,k=k+Math.imul(U,gt)|0;var qn=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(qn>>>26)|0,qn&=67108863,v=Math.imul(ie,dt),l=Math.imul(ie,yt),l=l+Math.imul(ae,dt)|0,k=Math.imul(ae,yt),v=v+Math.imul(X,mt)|0,l=l+Math.imul(X,gt)|0,l=l+Math.imul(oe,mt)|0,k=k+Math.imul(oe,gt)|0,v=v+Math.imul(w,xt)|0,l=l+Math.imul(w,wt)|0,l=l+Math.imul(U,xt)|0,k=k+Math.imul(U,wt)|0;var Un=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(Un>>>26)|0,Un&=67108863,v=Math.imul(ce,dt),l=Math.imul(ce,yt),l=l+Math.imul(me,dt)|0,k=Math.imul(me,yt),v=v+Math.imul(ie,mt)|0,l=l+Math.imul(ie,gt)|0,l=l+Math.imul(ae,mt)|0,k=k+Math.imul(ae,gt)|0,v=v+Math.imul(X,xt)|0,l=l+Math.imul(X,wt)|0,l=l+Math.imul(oe,xt)|0,k=k+Math.imul(oe,wt)|0,v=v+Math.imul(w,bt)|0,l=l+Math.imul(w,vt)|0,l=l+Math.imul(U,bt)|0,k=k+Math.imul(U,vt)|0;var Dn=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(Dn>>>26)|0,Dn&=67108863,v=Math.imul(rt,dt),l=Math.imul(rt,yt),l=l+Math.imul(nt,dt)|0,k=Math.imul(nt,yt),v=v+Math.imul(ce,mt)|0,l=l+Math.imul(ce,gt)|0,l=l+Math.imul(me,mt)|0,k=k+Math.imul(me,gt)|0,v=v+Math.imul(ie,xt)|0,l=l+Math.imul(ie,wt)|0,l=l+Math.imul(ae,xt)|0,k=k+Math.imul(ae,wt)|0,v=v+Math.imul(X,bt)|0,l=l+Math.imul(X,vt)|0,l=l+Math.imul(oe,bt)|0,k=k+Math.imul(oe,vt)|0,v=v+Math.imul(w,St)|0,l=l+Math.imul(w,Et)|0,l=l+Math.imul(U,St)|0,k=k+Math.imul(U,Et)|0;var Fn=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(Fn>>>26)|0,Fn&=67108863,v=Math.imul(ot,dt),l=Math.imul(ot,yt),l=l+Math.imul(st,dt)|0,k=Math.imul(st,yt),v=v+Math.imul(rt,mt)|0,l=l+Math.imul(rt,gt)|0,l=l+Math.imul(nt,mt)|0,k=k+Math.imul(nt,gt)|0,v=v+Math.imul(ce,xt)|0,l=l+Math.imul(ce,wt)|0,l=l+Math.imul(me,xt)|0,k=k+Math.imul(me,wt)|0,v=v+Math.imul(ie,bt)|0,l=l+Math.imul(ie,vt)|0,l=l+Math.imul(ae,bt)|0,k=k+Math.imul(ae,vt)|0,v=v+Math.imul(X,St)|0,l=l+Math.imul(X,Et)|0,l=l+Math.imul(oe,St)|0,k=k+Math.imul(oe,Et)|0,v=v+Math.imul(w,_t)|0,l=l+Math.imul(w,kt)|0,l=l+Math.imul(U,_t)|0,k=k+Math.imul(U,kt)|0;var tp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(tp>>>26)|0,tp&=67108863,v=Math.imul(it,dt),l=Math.imul(it,yt),l=l+Math.imul(at,dt)|0,k=Math.imul(at,yt),v=v+Math.imul(ot,mt)|0,l=l+Math.imul(ot,gt)|0,l=l+Math.imul(st,mt)|0,k=k+Math.imul(st,gt)|0,v=v+Math.imul(rt,xt)|0,l=l+Math.imul(rt,wt)|0,l=l+Math.imul(nt,xt)|0,k=k+Math.imul(nt,wt)|0,v=v+Math.imul(ce,bt)|0,l=l+Math.imul(ce,vt)|0,l=l+Math.imul(me,bt)|0,k=k+Math.imul(me,vt)|0,v=v+Math.imul(ie,St)|0,l=l+Math.imul(ie,Et)|0,l=l+Math.imul(ae,St)|0,k=k+Math.imul(ae,Et)|0,v=v+Math.imul(X,_t)|0,l=l+Math.imul(X,kt)|0,l=l+Math.imul(oe,_t)|0,k=k+Math.imul(oe,kt)|0,v=v+Math.imul(w,At)|0,l=l+Math.imul(w,It)|0,l=l+Math.imul(U,At)|0,k=k+Math.imul(U,It)|0;var rp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(rp>>>26)|0,rp&=67108863,v=Math.imul(ct,dt),l=Math.imul(ct,yt),l=l+Math.imul(ut,dt)|0,k=Math.imul(ut,yt),v=v+Math.imul(it,mt)|0,l=l+Math.imul(it,gt)|0,l=l+Math.imul(at,mt)|0,k=k+Math.imul(at,gt)|0,v=v+Math.imul(ot,xt)|0,l=l+Math.imul(ot,wt)|0,l=l+Math.imul(st,xt)|0,k=k+Math.imul(st,wt)|0,v=v+Math.imul(rt,bt)|0,l=l+Math.imul(rt,vt)|0,l=l+Math.imul(nt,bt)|0,k=k+Math.imul(nt,vt)|0,v=v+Math.imul(ce,St)|0,l=l+Math.imul(ce,Et)|0,l=l+Math.imul(me,St)|0,k=k+Math.imul(me,Et)|0,v=v+Math.imul(ie,_t)|0,l=l+Math.imul(ie,kt)|0,l=l+Math.imul(ae,_t)|0,k=k+Math.imul(ae,kt)|0,v=v+Math.imul(X,At)|0,l=l+Math.imul(X,It)|0,l=l+Math.imul(oe,At)|0,k=k+Math.imul(oe,It)|0,v=v+Math.imul(w,Rt)|0,l=l+Math.imul(w,Bt)|0,l=l+Math.imul(U,Rt)|0,k=k+Math.imul(U,Bt)|0;var np=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(np>>>26)|0,np&=67108863,v=Math.imul(pt,dt),l=Math.imul(pt,yt),l=l+Math.imul(ft,dt)|0,k=Math.imul(ft,yt),v=v+Math.imul(ct,mt)|0,l=l+Math.imul(ct,gt)|0,l=l+Math.imul(ut,mt)|0,k=k+Math.imul(ut,gt)|0,v=v+Math.imul(it,xt)|0,l=l+Math.imul(it,wt)|0,l=l+Math.imul(at,xt)|0,k=k+Math.imul(at,wt)|0,v=v+Math.imul(ot,bt)|0,l=l+Math.imul(ot,vt)|0,l=l+Math.imul(st,bt)|0,k=k+Math.imul(st,vt)|0,v=v+Math.imul(rt,St)|0,l=l+Math.imul(rt,Et)|0,l=l+Math.imul(nt,St)|0,k=k+Math.imul(nt,Et)|0,v=v+Math.imul(ce,_t)|0,l=l+Math.imul(ce,kt)|0,l=l+Math.imul(me,_t)|0,k=k+Math.imul(me,kt)|0,v=v+Math.imul(ie,At)|0,l=l+Math.imul(ie,It)|0,l=l+Math.imul(ae,At)|0,k=k+Math.imul(ae,It)|0,v=v+Math.imul(X,Rt)|0,l=l+Math.imul(X,Bt)|0,l=l+Math.imul(oe,Rt)|0,k=k+Math.imul(oe,Bt)|0,v=v+Math.imul(w,Pt)|0,l=l+Math.imul(w,Tt)|0,l=l+Math.imul(U,Pt)|0,k=k+Math.imul(U,Tt)|0;var op=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(op>>>26)|0,op&=67108863,v=Math.imul(lt,dt),l=Math.imul(lt,yt),l=l+Math.imul(ht,dt)|0,k=Math.imul(ht,yt),v=v+Math.imul(pt,mt)|0,l=l+Math.imul(pt,gt)|0,l=l+Math.imul(ft,mt)|0,k=k+Math.imul(ft,gt)|0,v=v+Math.imul(ct,xt)|0,l=l+Math.imul(ct,wt)|0,l=l+Math.imul(ut,xt)|0,k=k+Math.imul(ut,wt)|0,v=v+Math.imul(it,bt)|0,l=l+Math.imul(it,vt)|0,l=l+Math.imul(at,bt)|0,k=k+Math.imul(at,vt)|0,v=v+Math.imul(ot,St)|0,l=l+Math.imul(ot,Et)|0,l=l+Math.imul(st,St)|0,k=k+Math.imul(st,Et)|0,v=v+Math.imul(rt,_t)|0,l=l+Math.imul(rt,kt)|0,l=l+Math.imul(nt,_t)|0,k=k+Math.imul(nt,kt)|0,v=v+Math.imul(ce,At)|0,l=l+Math.imul(ce,It)|0,l=l+Math.imul(me,At)|0,k=k+Math.imul(me,It)|0,v=v+Math.imul(ie,Rt)|0,l=l+Math.imul(ie,Bt)|0,l=l+Math.imul(ae,Rt)|0,k=k+Math.imul(ae,Bt)|0,v=v+Math.imul(X,Pt)|0,l=l+Math.imul(X,Tt)|0,l=l+Math.imul(oe,Pt)|0,k=k+Math.imul(oe,Tt)|0,v=v+Math.imul(w,zt)|0,l=l+Math.imul(w,Mt)|0,l=l+Math.imul(U,zt)|0,k=k+Math.imul(U,Mt)|0;var sp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(sp>>>26)|0,sp&=67108863,v=Math.imul(lt,mt),l=Math.imul(lt,gt),l=l+Math.imul(ht,mt)|0,k=Math.imul(ht,gt),v=v+Math.imul(pt,xt)|0,l=l+Math.imul(pt,wt)|0,l=l+Math.imul(ft,xt)|0,k=k+Math.imul(ft,wt)|0,v=v+Math.imul(ct,bt)|0,l=l+Math.imul(ct,vt)|0,l=l+Math.imul(ut,bt)|0,k=k+Math.imul(ut,vt)|0,v=v+Math.imul(it,St)|0,l=l+Math.imul(it,Et)|0,l=l+Math.imul(at,St)|0,k=k+Math.imul(at,Et)|0,v=v+Math.imul(ot,_t)|0,l=l+Math.imul(ot,kt)|0,l=l+Math.imul(st,_t)|0,k=k+Math.imul(st,kt)|0,v=v+Math.imul(rt,At)|0,l=l+Math.imul(rt,It)|0,l=l+Math.imul(nt,At)|0,k=k+Math.imul(nt,It)|0,v=v+Math.imul(ce,Rt)|0,l=l+Math.imul(ce,Bt)|0,l=l+Math.imul(me,Rt)|0,k=k+Math.imul(me,Bt)|0,v=v+Math.imul(ie,Pt)|0,l=l+Math.imul(ie,Tt)|0,l=l+Math.imul(ae,Pt)|0,k=k+Math.imul(ae,Tt)|0,v=v+Math.imul(X,zt)|0,l=l+Math.imul(X,Mt)|0,l=l+Math.imul(oe,zt)|0,k=k+Math.imul(oe,Mt)|0;var ip=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(ip>>>26)|0,ip&=67108863,v=Math.imul(lt,xt),l=Math.imul(lt,wt),l=l+Math.imul(ht,xt)|0,k=Math.imul(ht,wt),v=v+Math.imul(pt,bt)|0,l=l+Math.imul(pt,vt)|0,l=l+Math.imul(ft,bt)|0,k=k+Math.imul(ft,vt)|0,v=v+Math.imul(ct,St)|0,l=l+Math.imul(ct,Et)|0,l=l+Math.imul(ut,St)|0,k=k+Math.imul(ut,Et)|0,v=v+Math.imul(it,_t)|0,l=l+Math.imul(it,kt)|0,l=l+Math.imul(at,_t)|0,k=k+Math.imul(at,kt)|0,v=v+Math.imul(ot,At)|0,l=l+Math.imul(ot,It)|0,l=l+Math.imul(st,At)|0,k=k+Math.imul(st,It)|0,v=v+Math.imul(rt,Rt)|0,l=l+Math.imul(rt,Bt)|0,l=l+Math.imul(nt,Rt)|0,k=k+Math.imul(nt,Bt)|0,v=v+Math.imul(ce,Pt)|0,l=l+Math.imul(ce,Tt)|0,l=l+Math.imul(me,Pt)|0,k=k+Math.imul(me,Tt)|0,v=v+Math.imul(ie,zt)|0,l=l+Math.imul(ie,Mt)|0,l=l+Math.imul(ae,zt)|0,k=k+Math.imul(ae,Mt)|0;var ap=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(ap>>>26)|0,ap&=67108863,v=Math.imul(lt,bt),l=Math.imul(lt,vt),l=l+Math.imul(ht,bt)|0,k=Math.imul(ht,vt),v=v+Math.imul(pt,St)|0,l=l+Math.imul(pt,Et)|0,l=l+Math.imul(ft,St)|0,k=k+Math.imul(ft,Et)|0,v=v+Math.imul(ct,_t)|0,l=l+Math.imul(ct,kt)|0,l=l+Math.imul(ut,_t)|0,k=k+Math.imul(ut,kt)|0,v=v+Math.imul(it,At)|0,l=l+Math.imul(it,It)|0,l=l+Math.imul(at,At)|0,k=k+Math.imul(at,It)|0,v=v+Math.imul(ot,Rt)|0,l=l+Math.imul(ot,Bt)|0,l=l+Math.imul(st,Rt)|0,k=k+Math.imul(st,Bt)|0,v=v+Math.imul(rt,Pt)|0,l=l+Math.imul(rt,Tt)|0,l=l+Math.imul(nt,Pt)|0,k=k+Math.imul(nt,Tt)|0,v=v+Math.imul(ce,zt)|0,l=l+Math.imul(ce,Mt)|0,l=l+Math.imul(me,zt)|0,k=k+Math.imul(me,Mt)|0;var cp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(cp>>>26)|0,cp&=67108863,v=Math.imul(lt,St),l=Math.imul(lt,Et),l=l+Math.imul(ht,St)|0,k=Math.imul(ht,Et),v=v+Math.imul(pt,_t)|0,l=l+Math.imul(pt,kt)|0,l=l+Math.imul(ft,_t)|0,k=k+Math.imul(ft,kt)|0,v=v+Math.imul(ct,At)|0,l=l+Math.imul(ct,It)|0,l=l+Math.imul(ut,At)|0,k=k+Math.imul(ut,It)|0,v=v+Math.imul(it,Rt)|0,l=l+Math.imul(it,Bt)|0,l=l+Math.imul(at,Rt)|0,k=k+Math.imul(at,Bt)|0,v=v+Math.imul(ot,Pt)|0,l=l+Math.imul(ot,Tt)|0,l=l+Math.imul(st,Pt)|0,k=k+Math.imul(st,Tt)|0,v=v+Math.imul(rt,zt)|0,l=l+Math.imul(rt,Mt)|0,l=l+Math.imul(nt,zt)|0,k=k+Math.imul(nt,Mt)|0;var up=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(up>>>26)|0,up&=67108863,v=Math.imul(lt,_t),l=Math.imul(lt,kt),l=l+Math.imul(ht,_t)|0,k=Math.imul(ht,kt),v=v+Math.imul(pt,At)|0,l=l+Math.imul(pt,It)|0,l=l+Math.imul(ft,At)|0,k=k+Math.imul(ft,It)|0,v=v+Math.imul(ct,Rt)|0,l=l+Math.imul(ct,Bt)|0,l=l+Math.imul(ut,Rt)|0,k=k+Math.imul(ut,Bt)|0,v=v+Math.imul(it,Pt)|0,l=l+Math.imul(it,Tt)|0,l=l+Math.imul(at,Pt)|0,k=k+Math.imul(at,Tt)|0,v=v+Math.imul(ot,zt)|0,l=l+Math.imul(ot,Mt)|0,l=l+Math.imul(st,zt)|0,k=k+Math.imul(st,Mt)|0;var pp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(pp>>>26)|0,pp&=67108863,v=Math.imul(lt,At),l=Math.imul(lt,It),l=l+Math.imul(ht,At)|0,k=Math.imul(ht,It),v=v+Math.imul(pt,Rt)|0,l=l+Math.imul(pt,Bt)|0,l=l+Math.imul(ft,Rt)|0,k=k+Math.imul(ft,Bt)|0,v=v+Math.imul(ct,Pt)|0,l=l+Math.imul(ct,Tt)|0,l=l+Math.imul(ut,Pt)|0,k=k+Math.imul(ut,Tt)|0,v=v+Math.imul(it,zt)|0,l=l+Math.imul(it,Mt)|0,l=l+Math.imul(at,zt)|0,k=k+Math.imul(at,Mt)|0;var fp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(fp>>>26)|0,fp&=67108863,v=Math.imul(lt,Rt),l=Math.imul(lt,Bt),l=l+Math.imul(ht,Rt)|0,k=Math.imul(ht,Bt),v=v+Math.imul(pt,Pt)|0,l=l+Math.imul(pt,Tt)|0,l=l+Math.imul(ft,Pt)|0,k=k+Math.imul(ft,Tt)|0,v=v+Math.imul(ct,zt)|0,l=l+Math.imul(ct,Mt)|0,l=l+Math.imul(ut,zt)|0,k=k+Math.imul(ut,Mt)|0;var lp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(lp>>>26)|0,lp&=67108863,v=Math.imul(lt,Pt),l=Math.imul(lt,Tt),l=l+Math.imul(ht,Pt)|0,k=Math.imul(ht,Tt),v=v+Math.imul(pt,zt)|0,l=l+Math.imul(pt,Mt)|0,l=l+Math.imul(ft,zt)|0,k=k+Math.imul(ft,Mt)|0;var hp=(P+v|0)+((l&8191)<<13)|0;P=(k+(l>>>13)|0)+(hp>>>26)|0,hp&=67108863,v=Math.imul(lt,zt),l=Math.imul(lt,Mt),l=l+Math.imul(ht,zt)|0,k=Math.imul(ht,Mt);var dp=(P+v|0)+((l&8191)<<13)|0;return P=(k+(l>>>13)|0)+(dp>>>26)|0,dp&=67108863,S[0]=On,S[1]=qn,S[2]=Un,S[3]=Dn,S[4]=Fn,S[5]=tp,S[6]=rp,S[7]=np,S[8]=op,S[9]=sp,S[10]=ip,S[11]=ap,S[12]=cp,S[13]=up,S[14]=pp,S[15]=fp,S[16]=lp,S[17]=hp,S[18]=dp,P!==0&&(S[19]=P,h.length++),h};Math.imul||(le=$);function W(m,c,p){p.negative=c.negative^m.negative,p.length=m.length+c.length;for(var h=0,d=0,x=0;x<p.length-1;x++){var S=d;d=0;for(var P=h&67108863,v=Math.min(x,c.length-1),l=Math.max(0,x-m.length+1);l<=v;l++){var k=x-l,se=m.words[k]|0,w=c.words[l]|0,U=se*w,F=U&67108863;S=S+(U/67108864|0)|0,F=F+P|0,P=F&67108863,S=S+(F>>>26)|0,d+=S>>>26,S&=67108863}p.words[x]=P,h=S,S=d}return h!==0?p.words[x]=h:p.length--,p._strip()}function be(m,c,p){return W(m,c,p)}o.prototype.mulTo=function(c,p){var h,d=this.length+c.length;return this.length===10&&c.length===10?h=le(this,c,p):d<63?h=$(this,c,p):d<1024?h=W(this,c,p):h=be(this,c,p),h};function Te(m,c){this.x=m,this.y=c}Te.prototype.makeRBT=function(c){for(var p=new Array(c),h=o.prototype._countBits(c)-1,d=0;d<c;d++)p[d]=this.revBin(d,h,c);return p},Te.prototype.revBin=function(c,p,h){if(c===0||c===h-1)return c;for(var d=0,x=0;x<p;x++)d|=(c&1)<<p-x-1,c>>=1;return d},Te.prototype.permute=function(c,p,h,d,x,S){for(var P=0;P<S;P++)d[P]=p[c[P]],x[P]=h[c[P]]},Te.prototype.transform=function(c,p,h,d,x,S){this.permute(S,c,p,h,d,x);for(var P=1;P<x;P<<=1)for(var v=P<<1,l=Math.cos(2*Math.PI/v),k=Math.sin(2*Math.PI/v),se=0;se<x;se+=v)for(var w=l,U=k,F=0;F<P;F++){var X=h[se+F],oe=d[se+F],we=h[se+F+P],ie=d[se+F+P],ae=w*we-U*ie;ie=w*ie+U*we,we=ae,h[se+F]=X+we,d[se+F]=oe+ie,h[se+F+P]=X-we,d[se+F+P]=oe-ie,F!==v&&(ae=l*w-k*U,U=l*U+k*w,w=ae)}},Te.prototype.guessLen13b=function(c,p){var h=Math.max(p,c)|1,d=h&1,x=0;for(h=h/2|0;h;h=h>>>1)x++;return 1<<x+1+d},Te.prototype.conjugate=function(c,p,h){if(!(h<=1))for(var d=0;d<h/2;d++){var x=c[d];c[d]=c[h-d-1],c[h-d-1]=x,x=p[d],p[d]=-p[h-d-1],p[h-d-1]=-x}},Te.prototype.normalize13b=function(c,p){for(var h=0,d=0;d<p/2;d++){var x=Math.round(c[2*d+1]/p)*8192+Math.round(c[2*d]/p)+h;c[d]=x&67108863,x<67108864?h=0:h=x/67108864|0}return c},Te.prototype.convert13b=function(c,p,h,d){for(var x=0,S=0;S<p;S++)x=x+(c[S]|0),h[2*S]=x&8191,x=x>>>13,h[2*S+1]=x&8191,x=x>>>13;for(S=2*p;S<d;++S)h[S]=0;t(x===0),t((x&-8192)===0)},Te.prototype.stub=function(c){for(var p=new Array(c),h=0;h<c;h++)p[h]=0;return p},Te.prototype.mulp=function(c,p,h){var d=2*this.guessLen13b(c.length,p.length),x=this.makeRBT(d),S=this.stub(d),P=new Array(d),v=new Array(d),l=new Array(d),k=new Array(d),se=new Array(d),w=new Array(d),U=h.words;U.length=d,this.convert13b(c.words,c.length,P,d),this.convert13b(p.words,p.length,k,d),this.transform(P,S,v,l,d,x),this.transform(k,S,se,w,d,x);for(var F=0;F<d;F++){var X=v[F]*se[F]-l[F]*w[F];l[F]=v[F]*w[F]+l[F]*se[F],v[F]=X}return this.conjugate(v,l,d),this.transform(v,l,U,S,d,x),this.conjugate(U,S,d),this.normalize13b(U,d),h.negative=c.negative^p.negative,h.length=c.length+p.length,h._strip()},o.prototype.mul=function(c){var p=new o(null);return p.words=new Array(this.length+c.length),this.mulTo(c,p)},o.prototype.mulf=function(c){var p=new o(null);return p.words=new Array(this.length+c.length),be(this,c,p)},o.prototype.imul=function(c){return this.clone().mulTo(c,this)},o.prototype.imuln=function(c){var p=c<0;p&&(c=-c),t(typeof c=="number"),t(c<67108864);for(var h=0,d=0;d<this.length;d++){var x=(this.words[d]|0)*c,S=(x&67108863)+(h&67108863);h>>=26,h+=x/67108864|0,h+=S>>>26,this.words[d]=S&67108863}return h!==0&&(this.words[d]=h,this.length++),p?this.ineg():this},o.prototype.muln=function(c){return this.clone().imuln(c)},o.prototype.sqr=function(){return this.mul(this)},o.prototype.isqr=function(){return this.imul(this.clone())},o.prototype.pow=function(c){var p=ee(c);if(p.length===0)return new o(1);for(var h=this,d=0;d<p.length&&p[d]===0;d++,h=h.sqr());if(++d<p.length)for(var x=h.sqr();d<p.length;d++,x=x.sqr())p[d]!==0&&(h=h.mul(x));return h},o.prototype.iushln=function(c){t(typeof c=="number"&&c>=0);var p=c%26,h=(c-p)/26,d=67108863>>>26-p<<26-p,x;if(p!==0){var S=0;for(x=0;x<this.length;x++){var P=this.words[x]&d,v=(this.words[x]|0)-P<<p;this.words[x]=v|S,S=P>>>26-p}S&&(this.words[x]=S,this.length++)}if(h!==0){for(x=this.length-1;x>=0;x--)this.words[x+h]=this.words[x];for(x=0;x<h;x++)this.words[x]=0;this.length+=h}return this._strip()},o.prototype.ishln=function(c){return t(this.negative===0),this.iushln(c)},o.prototype.iushrn=function(c,p,h){t(typeof c=="number"&&c>=0);var d;p?d=(p-p%26)/26:d=0;var x=c%26,S=Math.min((c-x)/26,this.length),P=67108863^67108863>>>x<<x,v=h;if(d-=S,d=Math.max(0,d),v){for(var l=0;l<S;l++)v.words[l]=this.words[l];v.length=S}if(S!==0)if(this.length>S)for(this.length-=S,l=0;l<this.length;l++)this.words[l]=this.words[l+S];else this.words[0]=0,this.length=1;var k=0;for(l=this.length-1;l>=0&&(k!==0||l>=d);l--){var se=this.words[l]|0;this.words[l]=k<<26-x|se>>>x,k=se&P}return v&&k!==0&&(v.words[v.length++]=k),this.length===0&&(this.words[0]=0,this.length=1),this._strip()},o.prototype.ishrn=function(c,p,h){return t(this.negative===0),this.iushrn(c,p,h)},o.prototype.shln=function(c){return this.clone().ishln(c)},o.prototype.ushln=function(c){return this.clone().iushln(c)},o.prototype.shrn=function(c){return this.clone().ishrn(c)},o.prototype.ushrn=function(c){return this.clone().iushrn(c)},o.prototype.testn=function(c){t(typeof c=="number"&&c>=0);var p=c%26,h=(c-p)/26,d=1<<p;if(this.length<=h)return!1;var x=this.words[h];return!!(x&d)},o.prototype.imaskn=function(c){t(typeof c=="number"&&c>=0);var p=c%26,h=(c-p)/26;if(t(this.negative===0,"imaskn works only with positive numbers"),this.length<=h)return this;if(p!==0&&h++,this.length=Math.min(h,this.length),p!==0){var d=67108863^67108863>>>p<<p;this.words[this.length-1]&=d}return this._strip()},o.prototype.maskn=function(c){return this.clone().imaskn(c)},o.prototype.iaddn=function(c){return t(typeof c=="number"),t(c<67108864),c<0?this.isubn(-c):this.negative!==0?this.length===1&&(this.words[0]|0)<=c?(this.words[0]=c-(this.words[0]|0),this.negative=0,this):(this.negative=0,this.isubn(c),this.negative=1,this):this._iaddn(c)},o.prototype._iaddn=function(c){this.words[0]+=c;for(var p=0;p<this.length&&this.words[p]>=67108864;p++)this.words[p]-=67108864,p===this.length-1?this.words[p+1]=1:this.words[p+1]++;return this.length=Math.max(this.length,p+1),this},o.prototype.isubn=function(c){if(t(typeof c=="number"),t(c<67108864),c<0)return this.iaddn(-c);if(this.negative!==0)return this.negative=0,this.iaddn(c),this.negative=1,this;if(this.words[0]-=c,this.length===1&&this.words[0]<0)this.words[0]=-this.words[0],this.negative=1;else for(var p=0;p<this.length&&this.words[p]<0;p++)this.words[p]+=67108864,this.words[p+1]-=1;return this._strip()},o.prototype.addn=function(c){return this.clone().iaddn(c)},o.prototype.subn=function(c){return this.clone().isubn(c)},o.prototype.iabs=function(){return this.negative=0,this},o.prototype.abs=function(){return this.clone().iabs()},o.prototype._ishlnsubmul=function(c,p,h){var d=c.length+h,x;this._expand(d);var S,P=0;for(x=0;x<c.length;x++){S=(this.words[x+h]|0)+P;var v=(c.words[x]|0)*p;S-=v&67108863,P=(S>>26)-(v/67108864|0),this.words[x+h]=S&67108863}for(;x<this.length-h;x++)S=(this.words[x+h]|0)+P,P=S>>26,this.words[x+h]=S&67108863;if(P===0)return this._strip();for(t(P===-1),P=0,x=0;x<this.length;x++)S=-(this.words[x]|0)+P,P=S>>26,this.words[x]=S&67108863;return this.negative=1,this._strip()},o.prototype._wordDiv=function(c,p){var h=this.length-c.length,d=this.clone(),x=c,S=x.words[x.length-1]|0,P=this._countBits(S);h=26-P,h!==0&&(x=x.ushln(h),d.iushln(h),S=x.words[x.length-1]|0);var v=d.length-x.length,l;if(p!=="mod"){l=new o(null),l.length=v+1,l.words=new Array(l.length);for(var k=0;k<l.length;k++)l.words[k]=0}var se=d.clone()._ishlnsubmul(x,1,v);se.negative===0&&(d=se,l&&(l.words[v]=1));for(var w=v-1;w>=0;w--){var U=(d.words[x.length+w]|0)*67108864+(d.words[x.length+w-1]|0);for(U=Math.min(U/S|0,67108863),d._ishlnsubmul(x,U,w);d.negative!==0;)U--,d.negative=0,d._ishlnsubmul(x,1,w),d.isZero()||(d.negative^=1);l&&(l.words[w]=U)}return l&&l._strip(),d._strip(),p!=="div"&&h!==0&&d.iushrn(h),{div:l||null,mod:d}},o.prototype.divmod=function(c,p,h){if(t(!c.isZero()),this.isZero())return{div:new o(0),mod:new o(0)};var d,x,S;return this.negative!==0&&c.negative===0?(S=this.neg().divmod(c,p),p!=="mod"&&(d=S.div.neg()),p!=="div"&&(x=S.mod.neg(),h&&x.negative!==0&&x.iadd(c)),{div:d,mod:x}):this.negative===0&&c.negative!==0?(S=this.divmod(c.neg(),p),p!=="mod"&&(d=S.div.neg()),{div:d,mod:S.mod}):this.negative&c.negative?(S=this.neg().divmod(c.neg(),p),p!=="div"&&(x=S.mod.neg(),h&&x.negative!==0&&x.isub(c)),{div:S.div,mod:x}):c.length>this.length||this.cmp(c)<0?{div:new o(0),mod:this}:c.length===1?p==="div"?{div:this.divn(c.words[0]),mod:null}:p==="mod"?{div:null,mod:new o(this.modrn(c.words[0]))}:{div:this.divn(c.words[0]),mod:new o(this.modrn(c.words[0]))}:this._wordDiv(c,p)},o.prototype.div=function(c){return this.divmod(c,"div",!1).div},o.prototype.mod=function(c){return this.divmod(c,"mod",!1).mod},o.prototype.umod=function(c){return this.divmod(c,"mod",!0).mod},o.prototype.divRound=function(c){var p=this.divmod(c);if(p.mod.isZero())return p.div;var h=p.div.negative!==0?p.mod.isub(c):p.mod,d=c.ushrn(1),x=c.andln(1),S=h.cmp(d);return S<0||x===1&&S===0?p.div:p.div.negative!==0?p.div.isubn(1):p.div.iaddn(1)},o.prototype.modrn=function(c){var p=c<0;p&&(c=-c),t(c<=67108863);for(var h=(1<<26)%c,d=0,x=this.length-1;x>=0;x--)d=(h*d+(this.words[x]|0))%c;return p?-d:d},o.prototype.modn=function(c){return this.modrn(c)},o.prototype.idivn=function(c){var p=c<0;p&&(c=-c),t(c<=67108863);for(var h=0,d=this.length-1;d>=0;d--){var x=(this.words[d]|0)+h*67108864;this.words[d]=x/c|0,h=x%c}return this._strip(),p?this.ineg():this},o.prototype.divn=function(c){return this.clone().idivn(c)},o.prototype.egcd=function(c){t(c.negative===0),t(!c.isZero());var p=this,h=c.clone();p.negative!==0?p=p.umod(c):p=p.clone();for(var d=new o(1),x=new o(0),S=new o(0),P=new o(1),v=0;p.isEven()&&h.isEven();)p.iushrn(1),h.iushrn(1),++v;for(var l=h.clone(),k=p.clone();!p.isZero();){for(var se=0,w=1;!(p.words[0]&w)&&se<26;++se,w<<=1);if(se>0)for(p.iushrn(se);se-- >0;)(d.isOdd()||x.isOdd())&&(d.iadd(l),x.isub(k)),d.iushrn(1),x.iushrn(1);for(var U=0,F=1;!(h.words[0]&F)&&U<26;++U,F<<=1);if(U>0)for(h.iushrn(U);U-- >0;)(S.isOdd()||P.isOdd())&&(S.iadd(l),P.isub(k)),S.iushrn(1),P.iushrn(1);p.cmp(h)>=0?(p.isub(h),d.isub(S),x.isub(P)):(h.isub(p),S.isub(d),P.isub(x))}return{a:S,b:P,gcd:h.iushln(v)}},o.prototype._invmp=function(c){t(c.negative===0),t(!c.isZero());var p=this,h=c.clone();p.negative!==0?p=p.umod(c):p=p.clone();for(var d=new o(1),x=new o(0),S=h.clone();p.cmpn(1)>0&&h.cmpn(1)>0;){for(var P=0,v=1;!(p.words[0]&v)&&P<26;++P,v<<=1);if(P>0)for(p.iushrn(P);P-- >0;)d.isOdd()&&d.iadd(S),d.iushrn(1);for(var l=0,k=1;!(h.words[0]&k)&&l<26;++l,k<<=1);if(l>0)for(h.iushrn(l);l-- >0;)x.isOdd()&&x.iadd(S),x.iushrn(1);p.cmp(h)>=0?(p.isub(h),d.isub(x)):(h.isub(p),x.isub(d))}var se;return p.cmpn(1)===0?se=d:se=x,se.cmpn(0)<0&&se.iadd(c),se},o.prototype.gcd=function(c){if(this.isZero())return c.abs();if(c.isZero())return this.abs();var p=this.clone(),h=c.clone();p.negative=0,h.negative=0;for(var d=0;p.isEven()&&h.isEven();d++)p.iushrn(1),h.iushrn(1);do{for(;p.isEven();)p.iushrn(1);for(;h.isEven();)h.iushrn(1);var x=p.cmp(h);if(x<0){var S=p;p=h,h=S}else if(x===0||h.cmpn(1)===0)break;p.isub(h)}while(!0);return h.iushln(d)},o.prototype.invm=function(c){return this.egcd(c).a.umod(c)},o.prototype.isEven=function(){return(this.words[0]&1)===0},o.prototype.isOdd=function(){return(this.words[0]&1)===1},o.prototype.andln=function(c){return this.words[0]&c},o.prototype.bincn=function(c){t(typeof c=="number");var p=c%26,h=(c-p)/26,d=1<<p;if(this.length<=h)return this._expand(h+1),this.words[h]|=d,this;for(var x=d,S=h;x!==0&&S<this.length;S++){var P=this.words[S]|0;P+=x,x=P>>>26,P&=67108863,this.words[S]=P}return x!==0&&(this.words[S]=x,this.length++),this},o.prototype.isZero=function(){return this.length===1&&this.words[0]===0},o.prototype.cmpn=function(c){var p=c<0;if(this.negative!==0&&!p)return-1;if(this.negative===0&&p)return 1;this._strip();var h;if(this.length>1)h=1;else{p&&(c=-c),t(c<=67108863,"Number is too big");var d=this.words[0]|0;h=d===c?0:d<c?-1:1}return this.negative!==0?-h|0:h},o.prototype.cmp=function(c){if(this.negative!==0&&c.negative===0)return-1;if(this.negative===0&&c.negative!==0)return 1;var p=this.ucmp(c);return this.negative!==0?-p|0:p},o.prototype.ucmp=function(c){if(this.length>c.length)return 1;if(this.length<c.length)return-1;for(var p=0,h=this.length-1;h>=0;h--){var d=this.words[h]|0,x=c.words[h]|0;if(d!==x){d<x?p=-1:d>x&&(p=1);break}}return p},o.prototype.gtn=function(c){return this.cmpn(c)===1},o.prototype.gt=function(c){return this.cmp(c)===1},o.prototype.gten=function(c){return this.cmpn(c)>=0},o.prototype.gte=function(c){return this.cmp(c)>=0},o.prototype.ltn=function(c){return this.cmpn(c)===-1},o.prototype.lt=function(c){return this.cmp(c)===-1},o.prototype.lten=function(c){return this.cmpn(c)<=0},o.prototype.lte=function(c){return this.cmp(c)<=0},o.prototype.eqn=function(c){return this.cmpn(c)===0},o.prototype.eq=function(c){return this.cmp(c)===0},o.red=function(c){return new D(c)},o.prototype.toRed=function(c){return t(!this.red,"Already a number in reduction context"),t(this.negative===0,"red works only with positives"),c.convertTo(this)._forceRed(c)},o.prototype.fromRed=function(){return t(this.red,"fromRed works only with numbers in reduction context"),this.red.convertFrom(this)},o.prototype._forceRed=function(c){return this.red=c,this},o.prototype.forceRed=function(c){return t(!this.red,"Already a number in reduction context"),this._forceRed(c)},o.prototype.redAdd=function(c){return t(this.red,"redAdd works only with red numbers"),this.red.add(this,c)},o.prototype.redIAdd=function(c){return t(this.red,"redIAdd works only with red numbers"),this.red.iadd(this,c)},o.prototype.redSub=function(c){return t(this.red,"redSub works only with red numbers"),this.red.sub(this,c)},o.prototype.redISub=function(c){return t(this.red,"redISub works only with red numbers"),this.red.isub(this,c)},o.prototype.redShl=function(c){return t(this.red,"redShl works only with red numbers"),this.red.shl(this,c)},o.prototype.redMul=function(c){return t(this.red,"redMul works only with red numbers"),this.red._verify2(this,c),this.red.mul(this,c)},o.prototype.redIMul=function(c){return t(this.red,"redMul works only with red numbers"),this.red._verify2(this,c),this.red.imul(this,c)},o.prototype.redSqr=function(){return t(this.red,"redSqr works only with red numbers"),this.red._verify1(this),this.red.sqr(this)},o.prototype.redISqr=function(){return t(this.red,"redISqr works only with red numbers"),this.red._verify1(this),this.red.isqr(this)},o.prototype.redSqrt=function(){return t(this.red,"redSqrt works only with red numbers"),this.red._verify1(this),this.red.sqrt(this)},o.prototype.redInvm=function(){return t(this.red,"redInvm works only with red numbers"),this.red._verify1(this),this.red.invm(this)},o.prototype.redNeg=function(){return t(this.red,"redNeg works only with red numbers"),this.red._verify1(this),this.red.neg(this)},o.prototype.redPow=function(c){return t(this.red&&!c.red,"redPow(normalNum)"),this.red._verify1(this),this.red.pow(this,c)};var Oe={k256:null,p224:null,p192:null,p25519:null};function Ie(m,c){this.name=m,this.p=new o(c,16),this.n=this.p.bitLength(),this.k=new o(1).iushln(this.n).isub(this.p),this.tmp=this._tmp()}Ie.prototype._tmp=function(){var c=new o(null);return c.words=new Array(Math.ceil(this.n/13)),c},Ie.prototype.ireduce=function(c){var p=c,h;do this.split(p,this.tmp),p=this.imulK(p),p=p.iadd(this.tmp),h=p.bitLength();while(h>this.n);var d=h<this.n?-1:p.ucmp(this.p);return d===0?(p.words[0]=0,p.length=1):d>0?p.isub(this.p):p.strip!==void 0?p.strip():p._strip(),p},Ie.prototype.split=function(c,p){c.iushrn(this.n,0,p)},Ie.prototype.imulK=function(c){return c.imul(this.k)};function Fe(){Ie.call(this,"k256","ffffffff ffffffff ffffffff ffffffff ffffffff ffffffff fffffffe fffffc2f")}n(Fe,Ie),Fe.prototype.split=function(c,p){for(var h=4194303,d=Math.min(c.length,9),x=0;x<d;x++)p.words[x]=c.words[x];if(p.length=d,c.length<=9){c.words[0]=0,c.length=1;return}var S=c.words[9];for(p.words[p.length++]=S&h,x=10;x<c.length;x++){var P=c.words[x]|0;c.words[x-10]=(P&h)<<4|S>>>22,S=P}S>>>=22,c.words[x-10]=S,S===0&&c.length>10?c.length-=10:c.length-=9},Fe.prototype.imulK=function(c){c.words[c.length]=0,c.words[c.length+1]=0,c.length+=2;for(var p=0,h=0;h<c.length;h++){var d=c.words[h]|0;p+=d*977,c.words[h]=p&67108863,p=d*64+(p/67108864|0)}return c.words[c.length-1]===0&&(c.length--,c.words[c.length-1]===0&&c.length--),c};function _(){Ie.call(this,"p224","ffffffff ffffffff ffffffff ffffffff 00000000 00000000 00000001")}n(_,Ie);function A(){Ie.call(this,"p192","ffffffff ffffffff ffffffff fffffffe ffffffff ffffffff")}n(A,Ie);function z(){Ie.call(this,"25519","7fffffffffffffff ffffffffffffffff ffffffffffffffff ffffffffffffffed")}n(z,Ie),z.prototype.imulK=function(c){for(var p=0,h=0;h<c.length;h++){var d=(c.words[h]|0)*19+p,x=d&67108863;d>>>=26,c.words[h]=x,p=d}return p!==0&&(c.words[c.length++]=p),c},o._prime=function(c){if(Oe[c])return Oe[c];var p;if(c==="k256")p=new Fe;else if(c==="p224")p=new _;else if(c==="p192")p=new A;else if(c==="p25519")p=new z;else throw new Error("Unknown prime "+c);return Oe[c]=p,p};function D(m){if(typeof m=="string"){var c=o._prime(m);this.m=c.p,this.prime=c}else t(m.gtn(1),"modulus must be greater than 1"),this.m=m,this.prime=null}D.prototype._verify1=function(c){t(c.negative===0,"red works only with positives"),t(c.red,"red works only with red numbers")},D.prototype._verify2=function(c,p){t((c.negative|p.negative)===0,"red works only with positives"),t(c.red&&c.red===p.red,"red works only with red numbers")},D.prototype.imod=function(c){return this.prime?this.prime.ireduce(c)._forceRed(this):(g(c,c.umod(this.m)._forceRed(this)),c)},D.prototype.neg=function(c){return c.isZero()?c.clone():this.m.sub(c)._forceRed(this)},D.prototype.add=function(c,p){this._verify2(c,p);var h=c.add(p);return h.cmp(this.m)>=0&&h.isub(this.m),h._forceRed(this)},D.prototype.iadd=function(c,p){this._verify2(c,p);var h=c.iadd(p);return h.cmp(this.m)>=0&&h.isub(this.m),h},D.prototype.sub=function(c,p){this._verify2(c,p);var h=c.sub(p);return h.cmpn(0)<0&&h.iadd(this.m),h._forceRed(this)},D.prototype.isub=function(c,p){this._verify2(c,p);var h=c.isub(p);return h.cmpn(0)<0&&h.iadd(this.m),h},D.prototype.shl=function(c,p){return this._verify1(c),this.imod(c.ushln(p))},D.prototype.imul=function(c,p){return this._verify2(c,p),this.imod(c.imul(p))},D.prototype.mul=function(c,p){return this._verify2(c,p),this.imod(c.mul(p))},D.prototype.isqr=function(c){return this.imul(c,c.clone())},D.prototype.sqr=function(c){return this.mul(c,c)},D.prototype.sqrt=function(c){if(c.isZero())return c.clone();var p=this.m.andln(3);if(t(p%2===1),p===3){var h=this.m.add(new o(1)).iushrn(2);return this.pow(c,h)}for(var d=this.m.subn(1),x=0;!d.isZero()&&d.andln(1)===0;)x++,d.iushrn(1);t(!d.isZero());var S=new o(1).toRed(this),P=S.redNeg(),v=this.m.subn(1).iushrn(1),l=this.m.bitLength();for(l=new o(2*l*l).toRed(this);this.pow(l,v).cmp(P)!==0;)l.redIAdd(P);for(var k=this.pow(l,d),se=this.pow(c,d.addn(1).iushrn(1)),w=this.pow(c,d),U=x;w.cmp(S)!==0;){for(var F=w,X=0;F.cmp(S)!==0;X++)F=F.redSqr();t(X<U);var oe=this.pow(k,new o(1).iushln(U-X-1));se=se.redMul(oe),k=oe.redSqr(),w=w.redMul(k),U=X}return se},D.prototype.invm=function(c){var p=c._invmp(this.m);return p.negative!==0?(p.negative=0,this.imod(p).redNeg()):this.imod(p)},D.prototype.pow=function(c,p){if(p.isZero())return new o(1).toRed(this);if(p.cmpn(1)===0)return c.clone();var h=4,d=new Array(1<<h);d[0]=new o(1).toRed(this),d[1]=c;for(var x=2;x<d.length;x++)d[x]=this.mul(d[x-1],c);var S=d[0],P=0,v=0,l=p.bitLength()%26;for(l===0&&(l=26),x=p.length-1;x>=0;x--){for(var k=p.words[x],se=l-1;se>=0;se--){var w=k>>se&1;if(S!==d[0]&&(S=this.sqr(S)),w===0&&P===0){v=0;continue}P<<=1,P|=w,v++,!(v!==h&&(x!==0||se!==0))&&(S=this.mul(S,d[P]),v=0,P=0)}l=26}return S},D.prototype.convertTo=function(c){var p=c.umod(this.m);return p===c?p.clone():p},D.prototype.convertFrom=function(c){var p=c.clone();return p.red=null,p},o.mont=function(c){return new R(c)};function R(m){D.call(this,m),this.shift=this.m.bitLength(),this.shift%26!==0&&(this.shift+=26-this.shift%26),this.r=new o(1).iushln(this.shift),this.r2=this.imod(this.r.sqr()),this.rinv=this.r._invmp(this.m),this.minv=this.rinv.mul(this.r).isubn(1).div(this.m),this.minv=this.minv.umod(this.r),this.minv=this.r.sub(this.minv)}n(R,D),R.prototype.convertTo=function(c){return this.imod(c.ushln(this.shift))},R.prototype.convertFrom=function(c){var p=this.imod(c.mul(this.rinv));return p.red=null,p},R.prototype.imul=function(c,p){if(c.isZero()||p.isZero())return c.words[0]=0,c.length=1,c;var h=c.imul(p),d=h.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),x=h.isub(d).iushrn(this.shift),S=x;return x.cmp(this.m)>=0?S=x.isub(this.m):x.cmpn(0)<0&&(S=x.iadd(this.m)),S._forceRed(this)},R.prototype.mul=function(c,p){if(c.isZero()||p.isZero())return new o(0)._forceRed(this);var h=c.mul(p),d=h.maskn(this.shift).mul(this.minv).imaskn(this.shift).mul(this.m),x=h.isub(d).iushrn(this.shift),S=x;return x.cmp(this.m)>=0?S=x.isub(this.m):x.cmpn(0)<0&&(S=x.iadd(this.m)),S._forceRed(this)},R.prototype.invm=function(c){var p=this.imod(c._invmp(this.m).mul(this.r2));return p._forceRed(this)}})(typeof Hp>"u"||Hp,jy)});var Hy,Wy=O(()=>{y();Hy="bignumber/5.7.0"});function Wp(r){return new TI(r,36).toString(16)}var Ky,TI,NC,Vy=O(()=>{"use strict";y();Ky=Ke(Wa());Fa();Wy();TI=Ky.default.BN,NC=new Xr(Hy)});var Gy=O(()=>{y();Vy()});var Zy=_e((FC,Ka)=>{y();(function(){"use strict";var r="input is invalid type",e="finalize already called",t=typeof window=="object",n=t?window:{};n.JS_SHA3_NO_WINDOW&&(t=!1);var o=!t&&typeof self=="object",s=!n.JS_SHA3_NO_NODE_JS&&typeof B=="object"&&B.versions&&B.versions.node;s?n=global:o&&(n=self);var i=!n.JS_SHA3_NO_COMMON_JS&&typeof Ka=="object"&&Ka.exports,u=typeof define=="function"&&define.amd,f=!n.JS_SHA3_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",g="0123456789abcdef".split(""),b=[31,7936,2031616,520093696],E=[4,1024,262144,67108864],q=[1,256,65536,16777216],C=[6,1536,393216,100663296],K=[0,8,16,24],ee=[1,0,32898,0,32906,2147483648,2147516416,2147483648,32907,0,2147483649,0,2147516545,2147483648,32777,2147483648,138,0,136,0,2147516425,0,2147483658,0,2147516555,0,139,2147483648,32905,2147483648,32771,2147483648,32770,2147483648,128,2147483648,32778,0,2147483658,2147483648,2147516545,2147483648,32896,2147483648,2147483649,0,2147516424,2147483648],$=[224,256,384,512],le=[128,256],W=["hex","buffer","arrayBuffer","array","digest"],be={128:168,256:136};(n.JS_SHA3_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(w){return Object.prototype.toString.call(w)==="[object Array]"}),f&&(n.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(w){return typeof w=="object"&&w.buffer&&w.buffer.constructor===ArrayBuffer});for(var Te=function(w,U,F){return function(X){return new l(w,U,w).update(X)[F]()}},Oe=function(w,U,F){return function(X,oe){return new l(w,U,oe).update(X)[F]()}},Ie=function(w,U,F){return function(X,oe,we,ie){return c["cshake"+w].update(X,oe,we,ie)[F]()}},Fe=function(w,U,F){return function(X,oe,we,ie){return c["kmac"+w].update(X,oe,we,ie)[F]()}},_=function(w,U,F,X){for(var oe=0;oe<W.length;++oe){var we=W[oe];w[we]=U(F,X,we)}return w},A=function(w,U){var F=Te(w,U,"hex");return F.create=function(){return new l(w,U,w)},F.update=function(X){return F.create().update(X)},_(F,Te,w,U)},z=function(w,U){var F=Oe(w,U,"hex");return F.create=function(X){return new l(w,U,X)},F.update=function(X,oe){return F.create(oe).update(X)},_(F,Oe,w,U)},D=function(w,U){var F=be[w],X=Ie(w,U,"hex");return X.create=function(oe,we,ie){return!we&&!ie?c["shake"+w].create(oe):new l(w,U,oe).bytepad([we,ie],F)},X.update=function(oe,we,ie,ae){return X.create(we,ie,ae).update(oe)},_(X,Ie,w,U)},R=function(w,U){var F=be[w],X=Fe(w,U,"hex");return X.create=function(oe,we,ie){return new k(w,U,we).bytepad(["KMAC",ie],F).bytepad([oe],F)},X.update=function(oe,we,ie,ae){return X.create(oe,ie,ae).update(we)},_(X,Fe,w,U)},m=[{name:"keccak",padding:q,bits:$,createMethod:A},{name:"sha3",padding:C,bits:$,createMethod:A},{name:"shake",padding:b,bits:le,createMethod:z},{name:"cshake",padding:E,bits:le,createMethod:D},{name:"kmac",padding:E,bits:le,createMethod:R}],c={},p=[],h=0;h<m.length;++h)for(var d=m[h],x=d.bits,S=0;S<x.length;++S){var P=d.name+"_"+x[S];if(p.push(P),c[P]=d.createMethod(x[S],d.padding),d.name!=="sha3"){var v=d.name+x[S];p.push(v),c[v]=c[P]}}function l(w,U,F){this.blocks=[],this.s=[],this.padding=U,this.outputBits=F,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(w<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=F>>5,this.extraBytes=(F&31)>>3;for(var X=0;X<50;++X)this.s[X]=0}l.prototype.update=function(w){if(this.finalized)throw new Error(e);var U,F=typeof w;if(F!=="string"){if(F==="object"){if(w===null)throw new Error(r);if(f&&w.constructor===ArrayBuffer)w=new Uint8Array(w);else if(!Array.isArray(w)&&(!f||!ArrayBuffer.isView(w)))throw new Error(r)}else throw new Error(r);U=!0}for(var X=this.blocks,oe=this.byteCount,we=w.length,ie=this.blockCount,ae=0,qe=this.s,ce,me;ae<we;){if(this.reset)for(this.reset=!1,X[0]=this.block,ce=1;ce<ie+1;++ce)X[ce]=0;if(U)for(ce=this.start;ae<we&&ce<oe;++ae)X[ce>>2]|=w[ae]<<K[ce++&3];else for(ce=this.start;ae<we&&ce<oe;++ae)me=w.charCodeAt(ae),me<128?X[ce>>2]|=me<<K[ce++&3]:me<2048?(X[ce>>2]|=(192|me>>6)<<K[ce++&3],X[ce>>2]|=(128|me&63)<<K[ce++&3]):me<55296||me>=57344?(X[ce>>2]|=(224|me>>12)<<K[ce++&3],X[ce>>2]|=(128|me>>6&63)<<K[ce++&3],X[ce>>2]|=(128|me&63)<<K[ce++&3]):(me=65536+((me&1023)<<10|w.charCodeAt(++ae)&1023),X[ce>>2]|=(240|me>>18)<<K[ce++&3],X[ce>>2]|=(128|me>>12&63)<<K[ce++&3],X[ce>>2]|=(128|me>>6&63)<<K[ce++&3],X[ce>>2]|=(128|me&63)<<K[ce++&3]);if(this.lastByteIndex=ce,ce>=oe){for(this.start=ce-oe,this.block=X[ie],ce=0;ce<ie;++ce)qe[ce]^=X[ce];se(qe),this.reset=!0}else this.start=ce}return this},l.prototype.encode=function(w,U){var F=w&255,X=1,oe=[F];for(w=w>>8,F=w&255;F>0;)oe.unshift(F),w=w>>8,F=w&255,++X;return U?oe.push(X):oe.unshift(X),this.update(oe),oe.length},l.prototype.encodeString=function(w){var U,F=typeof w;if(F!=="string"){if(F==="object"){if(w===null)throw new Error(r);if(f&&w.constructor===ArrayBuffer)w=new Uint8Array(w);else if(!Array.isArray(w)&&(!f||!ArrayBuffer.isView(w)))throw new Error(r)}else throw new Error(r);U=!0}var X=0,oe=w.length;if(U)X=oe;else for(var we=0;we<w.length;++we){var ie=w.charCodeAt(we);ie<128?X+=1:ie<2048?X+=2:ie<55296||ie>=57344?X+=3:(ie=65536+((ie&1023)<<10|w.charCodeAt(++we)&1023),X+=4)}return X+=this.encode(X*8),this.update(w),X},l.prototype.bytepad=function(w,U){for(var F=this.encode(U),X=0;X<w.length;++X)F+=this.encodeString(w[X]);var oe=U-F%U,we=[];return we.length=oe,this.update(we),this},l.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var w=this.blocks,U=this.lastByteIndex,F=this.blockCount,X=this.s;if(w[U>>2]|=this.padding[U&3],this.lastByteIndex===this.byteCount)for(w[0]=w[F],U=1;U<F+1;++U)w[U]=0;for(w[F-1]|=2147483648,U=0;U<F;++U)X[U]^=w[U];se(X)}},l.prototype.toString=l.prototype.hex=function(){this.finalize();for(var w=this.blockCount,U=this.s,F=this.outputBlocks,X=this.extraBytes,oe=0,we=0,ie="",ae;we<F;){for(oe=0;oe<w&&we<F;++oe,++we)ae=U[oe],ie+=g[ae>>4&15]+g[ae&15]+g[ae>>12&15]+g[ae>>8&15]+g[ae>>20&15]+g[ae>>16&15]+g[ae>>28&15]+g[ae>>24&15];we%w===0&&(se(U),oe=0)}return X&&(ae=U[oe],ie+=g[ae>>4&15]+g[ae&15],X>1&&(ie+=g[ae>>12&15]+g[ae>>8&15]),X>2&&(ie+=g[ae>>20&15]+g[ae>>16&15])),ie},l.prototype.arrayBuffer=function(){this.finalize();var w=this.blockCount,U=this.s,F=this.outputBlocks,X=this.extraBytes,oe=0,we=0,ie=this.outputBits>>3,ae;X?ae=new ArrayBuffer(F+1<<2):ae=new ArrayBuffer(ie);for(var qe=new Uint32Array(ae);we<F;){for(oe=0;oe<w&&we<F;++oe,++we)qe[we]=U[oe];we%w===0&&se(U)}return X&&(qe[oe]=U[oe],ae=ae.slice(0,ie)),ae},l.prototype.buffer=l.prototype.arrayBuffer,l.prototype.digest=l.prototype.array=function(){this.finalize();for(var w=this.blockCount,U=this.s,F=this.outputBlocks,X=this.extraBytes,oe=0,we=0,ie=[],ae,qe;we<F;){for(oe=0;oe<w&&we<F;++oe,++we)ae=we<<2,qe=U[oe],ie[ae]=qe&255,ie[ae+1]=qe>>8&255,ie[ae+2]=qe>>16&255,ie[ae+3]=qe>>24&255;we%w===0&&se(U)}return X&&(ae=we<<2,qe=U[oe],ie[ae]=qe&255,X>1&&(ie[ae+1]=qe>>8&255),X>2&&(ie[ae+2]=qe>>16&255)),ie};function k(w,U,F){l.call(this,w,U,F)}k.prototype=new l,k.prototype.finalize=function(){return this.encode(this.outputBits,!0),l.prototype.finalize.call(this)};var se=function(w){var U,F,X,oe,we,ie,ae,qe,ce,me,So,rt,nt,Eo,ot,st,_o,it,at,ko,ct,ut,Ao,pt,ft,Io,lt,ht,Ro,dt,yt,Bo,mt,gt,Po,xt,wt,To,bt,vt,zo,St,Et,Mo,_t,kt,Lo,At,It,No,Rt,Bt,Co,Pt,Tt,Oo,zt,Mt,On,qn,Un,Dn,Fn;for(X=0;X<48;X+=2)oe=w[0]^w[10]^w[20]^w[30]^w[40],we=w[1]^w[11]^w[21]^w[31]^w[41],ie=w[2]^w[12]^w[22]^w[32]^w[42],ae=w[3]^w[13]^w[23]^w[33]^w[43],qe=w[4]^w[14]^w[24]^w[34]^w[44],ce=w[5]^w[15]^w[25]^w[35]^w[45],me=w[6]^w[16]^w[26]^w[36]^w[46],So=w[7]^w[17]^w[27]^w[37]^w[47],rt=w[8]^w[18]^w[28]^w[38]^w[48],nt=w[9]^w[19]^w[29]^w[39]^w[49],U=rt^(ie<<1|ae>>>31),F=nt^(ae<<1|ie>>>31),w[0]^=U,w[1]^=F,w[10]^=U,w[11]^=F,w[20]^=U,w[21]^=F,w[30]^=U,w[31]^=F,w[40]^=U,w[41]^=F,U=oe^(qe<<1|ce>>>31),F=we^(ce<<1|qe>>>31),w[2]^=U,w[3]^=F,w[12]^=U,w[13]^=F,w[22]^=U,w[23]^=F,w[32]^=U,w[33]^=F,w[42]^=U,w[43]^=F,U=ie^(me<<1|So>>>31),F=ae^(So<<1|me>>>31),w[4]^=U,w[5]^=F,w[14]^=U,w[15]^=F,w[24]^=U,w[25]^=F,w[34]^=U,w[35]^=F,w[44]^=U,w[45]^=F,U=qe^(rt<<1|nt>>>31),F=ce^(nt<<1|rt>>>31),w[6]^=U,w[7]^=F,w[16]^=U,w[17]^=F,w[26]^=U,w[27]^=F,w[36]^=U,w[37]^=F,w[46]^=U,w[47]^=F,U=me^(oe<<1|we>>>31),F=So^(we<<1|oe>>>31),w[8]^=U,w[9]^=F,w[18]^=U,w[19]^=F,w[28]^=U,w[29]^=F,w[38]^=U,w[39]^=F,w[48]^=U,w[49]^=F,Eo=w[0],ot=w[1],kt=w[11]<<4|w[10]>>>28,Lo=w[10]<<4|w[11]>>>28,ht=w[20]<<3|w[21]>>>29,Ro=w[21]<<3|w[20]>>>29,qn=w[31]<<9|w[30]>>>23,Un=w[30]<<9|w[31]>>>23,St=w[40]<<18|w[41]>>>14,Et=w[41]<<18|w[40]>>>14,gt=w[2]<<1|w[3]>>>31,Po=w[3]<<1|w[2]>>>31,st=w[13]<<12|w[12]>>>20,_o=w[12]<<12|w[13]>>>20,At=w[22]<<10|w[23]>>>22,It=w[23]<<10|w[22]>>>22,dt=w[33]<<13|w[32]>>>19,yt=w[32]<<13|w[33]>>>19,Dn=w[42]<<2|w[43]>>>30,Fn=w[43]<<2|w[42]>>>30,Pt=w[5]<<30|w[4]>>>2,Tt=w[4]<<30|w[5]>>>2,xt=w[14]<<6|w[15]>>>26,wt=w[15]<<6|w[14]>>>26,it=w[25]<<11|w[24]>>>21,at=w[24]<<11|w[25]>>>21,No=w[34]<<15|w[35]>>>17,Rt=w[35]<<15|w[34]>>>17,Bo=w[45]<<29|w[44]>>>3,mt=w[44]<<29|w[45]>>>3,pt=w[6]<<28|w[7]>>>4,ft=w[7]<<28|w[6]>>>4,Oo=w[17]<<23|w[16]>>>9,zt=w[16]<<23|w[17]>>>9,To=w[26]<<25|w[27]>>>7,bt=w[27]<<25|w[26]>>>7,ko=w[36]<<21|w[37]>>>11,ct=w[37]<<21|w[36]>>>11,Bt=w[47]<<24|w[46]>>>8,Co=w[46]<<24|w[47]>>>8,Mo=w[8]<<27|w[9]>>>5,_t=w[9]<<27|w[8]>>>5,Io=w[18]<<20|w[19]>>>12,lt=w[19]<<20|w[18]>>>12,Mt=w[29]<<7|w[28]>>>25,On=w[28]<<7|w[29]>>>25,vt=w[38]<<8|w[39]>>>24,zo=w[39]<<8|w[38]>>>24,ut=w[48]<<14|w[49]>>>18,Ao=w[49]<<14|w[48]>>>18,w[0]=Eo^~st&it,w[1]=ot^~_o&at,w[10]=pt^~Io&ht,w[11]=ft^~lt&Ro,w[20]=gt^~xt&To,w[21]=Po^~wt&bt,w[30]=Mo^~kt&At,w[31]=_t^~Lo&It,w[40]=Pt^~Oo&Mt,w[41]=Tt^~zt&On,w[2]=st^~it&ko,w[3]=_o^~at&ct,w[12]=Io^~ht&dt,w[13]=lt^~Ro&yt,w[22]=xt^~To&vt,w[23]=wt^~bt&zo,w[32]=kt^~At&No,w[33]=Lo^~It&Rt,w[42]=Oo^~Mt&qn,w[43]=zt^~On&Un,w[4]=it^~ko&ut,w[5]=at^~ct&Ao,w[14]=ht^~dt&Bo,w[15]=Ro^~yt&mt,w[24]=To^~vt&St,w[25]=bt^~zo&Et,w[34]=At^~No&Bt,w[35]=It^~Rt&Co,w[44]=Mt^~qn&Dn,w[45]=On^~Un&Fn,w[6]=ko^~ut&Eo,w[7]=ct^~Ao&ot,w[16]=dt^~Bo&pt,w[17]=yt^~mt&ft,w[26]=vt^~St&gt,w[27]=zo^~Et&Po,w[36]=No^~Bt&Mo,w[37]=Rt^~Co&_t,w[46]=qn^~Dn&Pt,w[47]=Un^~Fn&Tt,w[8]=ut^~Eo&st,w[9]=Ao^~ot&_o,w[18]=Bo^~pt&Io,w[19]=mt^~ft&lt,w[28]=St^~gt&xt,w[29]=Et^~Po&wt,w[38]=Bt^~Mo&kt,w[39]=Co^~_t&Lo,w[48]=Dn^~Pt&Oo,w[49]=Fn^~Tt&zt,w[0]^=ee[X],w[1]^=ee[X+1]};if(i)Ka.exports=c;else{for(h=0;h<p.length;++h)n[p[h]]=c[p[h]];u&&define(function(){return c})}})()});function $y(r){return"0x"+Yy.default.keccak_256(Ha(r))}var Yy,Jy=O(()=>{"use strict";y();Yy=Ke(Zy());jp()});var Qy,Xy=O(()=>{y();Qy="address/5.7.0"});function em(r){Fp(r,20)||Pi.throwArgumentError("invalid address","address",r),r=r.toLowerCase();let e=r.substring(2).split(""),t=new Uint8Array(40);for(let o=0;o<40;o++)t[o]=e[o].charCodeAt(0);let n=Ha($y(t));for(let o=0;o<40;o+=2)n[o>>1]>>4>=8&&(e[o]=e[o].toUpperCase()),(n[o>>1]&15)>=8&&(e[o+1]=e[o+1].toUpperCase());return"0x"+e.join("")}function LI(r){return Math.log10?Math.log10(r):Math.log(r)/Math.LN10}function NI(r){r=r.toUpperCase(),r=r.substring(4)+r.substring(0,2)+"00";let e=r.split("").map(n=>Kp[n]).join("");for(;e.length>=tm;){let n=e.substring(0,tm);e=parseInt(n,10)%97+e.substring(n.length)}let t=String(98-parseInt(e,10)%97);for(;t.length<2;)t="0"+t;return t}function CI(r){let e=null;if(typeof r!="string"&&Pi.throwArgumentError("invalid address","address",r),r.match(/^(0x)?[0-9a-fA-F]{40}$/))r.substring(0,2)!=="0x"&&(r="0x"+r),e=em(r),r.match(/([A-F].*[a-f])|([a-f].*[A-F])/)&&e!==r&&Pi.throwArgumentError("bad address checksum","address",r);else if(r.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)){for(r.substring(2,4)!==NI(r)&&Pi.throwArgumentError("bad icap checksum","address",r),e=Wp(r.substring(4));e.length<40;)e="0"+e;e=em("0x"+e)}else Pi.throwArgumentError("invalid address","address",r);return e}function rm(r){try{return CI(r),!0}catch{}return!1}var Pi,MI,Kp,tm,nm=O(()=>{"use strict";y();jp();Gy();Jy();Fa();Xy();Pi=new Xr(Qy);MI=9007199254740991;Kp={};for(let r=0;r<10;r++)Kp[String(r)]=String(r);for(let r=0;r<26;r++)Kp[String.fromCharCode(65+r)]=String(10+r);tm=Math.floor(LI(MI))});var im=_e((Vp,sm)=>{y();var Va=vs(),en=Va.Buffer;function om(r,e){for(var t in r)e[t]=r[t]}en.from&&en.alloc&&en.allocUnsafe&&en.allocUnsafeSlow?sm.exports=Va:(om(Va,Vp),Vp.Buffer=rs);function rs(r,e,t){return en(r,e,t)}rs.prototype=Object.create(en.prototype);om(en,rs);rs.from=function(r,e,t){if(typeof r=="number")throw new TypeError("Argument must not be a number");return en(r,e,t)};rs.alloc=function(r,e,t){if(typeof r!="number")throw new TypeError("Argument must be a number");var n=en(r);return e!==void 0?typeof t=="string"?n.fill(e,t):n.fill(e):n.fill(0),n};rs.allocUnsafe=function(r){if(typeof r!="number")throw new TypeError("Argument must be a number");return en(r)};rs.allocUnsafeSlow=function(r){if(typeof r!="number")throw new TypeError("Argument must be a number");return Va.SlowBuffer(r)}});var cm=_e((aO,am)=>{"use strict";y();var Ga=im().Buffer;function OI(r){if(r.length>=255)throw new TypeError("Alphabet too long");for(var e=new Uint8Array(256),t=0;t<e.length;t++)e[t]=255;for(var n=0;n<r.length;n++){var o=r.charAt(n),s=o.charCodeAt(0);if(e[s]!==255)throw new TypeError(o+" is ambiguous");e[s]=n}var i=r.length,u=r.charAt(0),f=Math.log(i)/Math.log(256),g=Math.log(256)/Math.log(i);function b(C){if((Array.isArray(C)||C instanceof Uint8Array)&&(C=Ga.from(C)),!Ga.isBuffer(C))throw new TypeError("Expected Buffer");if(C.length===0)return"";for(var K=0,ee=0,$=0,le=C.length;$!==le&&C[$]===0;)$++,K++;for(var W=(le-$)*g+1>>>0,be=new Uint8Array(W);$!==le;){for(var Te=C[$],Oe=0,Ie=W-1;(Te!==0||Oe<ee)&&Ie!==-1;Ie--,Oe++)Te+=256*be[Ie]>>>0,be[Ie]=Te%i>>>0,Te=Te/i>>>0;if(Te!==0)throw new Error("Non-zero carry");ee=Oe,$++}for(var Fe=W-ee;Fe!==W&&be[Fe]===0;)Fe++;for(var _=u.repeat(K);Fe<W;++Fe)_+=r.charAt(be[Fe]);return _}function E(C){if(typeof C!="string")throw new TypeError("Expected String");if(C.length===0)return Ga.alloc(0);for(var K=0,ee=0,$=0;C[K]===u;)ee++,K++;for(var le=(C.length-K)*f+1>>>0,W=new Uint8Array(le);C[K];){var be=e[C.charCodeAt(K)];if(be===255)return;for(var Te=0,Oe=le-1;(be!==0||Te<$)&&Oe!==-1;Oe--,Te++)be+=i*W[Oe]>>>0,W[Oe]=be%256>>>0,be=be/256>>>0;if(be!==0)throw new Error("Non-zero carry");$=Te,K++}for(var Ie=le-$;Ie!==le&&W[Ie]===0;)Ie++;var Fe=Ga.allocUnsafe(ee+(le-Ie));Fe.fill(0,0,ee);for(var _=ee;Ie!==le;)Fe[_++]=W[Ie++];return Fe}function q(C){var K=E(C);if(K)return K;throw new Error("Non-base"+i+" character")}return{encode:b,decodeUnsafe:E,decode:q}}am.exports=OI});var Zn=_e((uO,um)=>{y();var qI=cm(),UI="123456789ABCDEFGHJKLMNPQRSTUVWXYZabcdefghijkmnopqrstuvwxyz";um.exports=qI(UI)});var pm,Gp,$e,G,Ue,Ts,Zp,Yp,ve,Ee,$p,tn,Ti,zi,wn,Za,fm,Jp,Ya,Qp,$a,lm,hm,dm,ym,mm,gm,xm,wm,bm,DI,Xp,vm,ns,rn,ef,et,bn,Mi,Li,Sm,Em,os,FI,fe,Me,_m,Ja,zs,Ni,Ms,Qa,tf,vn,Ls,M,km,Xa,rf,Ns,Am,Im,jI,HI,Rm,WI,KI,Bm,VI,GI,Pm,ZI,YI,Tm,$I,JI,zm,QI,XI,Mm,e2,t2,Lm,r2,n2,o2,Ci,ec,Cs,nf,Oi,tc,qi,rc,Tr,nn,s2,i2,Nm,Cm,Os,of,Q=O(()=>{"use strict";y();pm=Ke(vs());nm();Gp=Ke(Zn());V();$e=a.string().min(2,{message:"Must be 2 or more characters long"}).regex(/^0x[0-9A-Fa-f]*$/,{message:"String must be '0x'-prefixed and followed by valid hex characters"}),G=a.number().transform(r=>`0x${r.toString(16)}`).or($e.min(3,{message:"Must be 3 or more characters long (should always have at least one digit - zero is '0x0')."})).refine(r=>r==="0x0"?!0:r[2]!=="0",{message:"Invalid hex quantity: leading zero digits are not allowed."}),Ue=$e.refine(r=>r.length%2===0,{message:"Invalid hex-encoded data: must be even number of digits"}),Ts=a.string().transform(r=>{let e=Ue.safeParse(r);return e.success?e.data:`0x${pm.Buffer.from(r).toString("hex")}`}),Zp=a.union([a.string(),a.number()]).transform((r,e)=>{if(typeof r=="number")return r;let t=r.startsWith("0x")?16:10,n=parseInt(r,t);return Number.isNaN(n)&&e.addIssue({code:a.ZodIssueCode.custom,message:"Could not parse as LenientInteger"}),n}),Yp=r=>Ue.refine(e=>e.length===r*2+2,{message:`Invalid byte length. (Expected ${r} bytes)`}),ve=$e.refine(rm,{message:"Invalid Ethereum address."}),Ee=Yp(32),$p=Yp(256),tn=a.object({blockHash:Ee,address:ve,logIndex:G,data:Ue,removed:a.boolean().optional(),topics:a.array(Ee),blockNumber:G.nullish().default(null),transactionIndex:G,transactionHash:Ee}),Ti=a.object({transactionHash:Ee,transactionIndex:G,blockHash:Ee,blockNumber:G,from:ve,to:ve.nullish().default(null).optional(),root:Ee.optional(),status:a.literal("0x1").or(a.literal("0x0")).optional(),cumulativeGasUsed:G,gasUsed:G,contractAddress:ve.nullish().default(null),logs:a.array(tn),logsBloom:$p,effectiveGasPrice:G.optional(),type:G.optional()}),zi=a.object({from:ve.optional(),chainId:G.optional(),to:ve.optional(),gas:G.optional(),gasPrice:G.optional(),value:G.optional(),data:Ue.optional(),nonce:G.optional()}),wn=a.object({name:a.string(),type:a.string()}),Za=a.array(wn),fm=a.object({chainId:Zp.optional(),name:a.string(),verifyingContract:ve,version:a.string().optional()}),Jp=a.object({type:a.string(),name:a.string(),value:a.string()}).array(),Ya=a.object({domain:fm,message:a.record(a.any()),primaryType:a.string(),types:a.object({EIP712Domain:Za}).and(a.record(wn.array()))}),Qp=a.object({chainId:Zp.optional(),name:a.string().optional(),verifyingContract:ve.optional(),version:a.string().optional(),salt:a.string().optional()}),$a=a.object({domain:Qp,message:a.record(a.any()),primaryType:a.string(),types:a.object({EIP712Domain:Za}).and(a.record(wn.array()))}),lm=a.object({name:a.literal("owner"),type:a.literal("address")}),hm=a.object({name:a.literal("spender"),type:a.literal("address")}),dm=a.object({name:a.literal("value"),type:a.literal("uint256")}),ym=a.object({name:a.literal("value"),type:a.literal("uint256")}),mm=a.object({name:a.literal("deadline"),type:a.literal("uint256")}),gm=a.tuple([wn,wn,wn,wn,wn]).refine(r=>{let e=[lm,hm,dm,ym,mm],t=new Set(["owner","spender","value","nonce","deadline"]);for(let n of r)for(let o of e){let s=o.safeParse(n);s.success&&t.delete(s.data.name)}return t.size===0}).transform(()=>[{name:"owner",type:"address"},{name:"spender",type:"address"},{name:"value",type:"uint256"},{name:"nonce",type:"uint256"},{name:"deadline",type:"uint256"}]),xm=a.literal("Permit"),wm=a.object({EIP712Domain:Za,Permit:gm}),bm=a.object({owner:ve,spender:ve,value:G,nonce:G,deadline:G}),DI=a.object({domain:Qp,primaryType:xm,types:wm,message:bm}),Xp=(n=>(n.legacy="0x0",n.eip2930="0x1",n.eip1559="0x2",n))(Xp||{}),vm=a.tuple([ve,a.array(Ee)]),ns=a.object({chainId:G.optional(),data:Ue.optional(),from:ve,gas:G.optional(),gasPrice:G.optional(),nonce:G.optional(),to:ve.optional(),value:G.optional(),type:a.nativeEnum(Xp).optional(),accessList:a.array(vm).optional(),maxPriorityFeePerGas:G.optional(),maxFeePerGas:G.optional(),gasLimit:G.optional()}).transform(r=>(r.gas==null&&r.gasLimit!=null&&(r.gas=r.gasLimit,delete r.gasLimit),r)).brand("EthUnsignedTransactionObject"),rn=a.object({blockHash:Ee.nullish(),blockNumber:G.nullish(),from:ve,gas:G,gasPrice:G.nullish(),hash:Ee,input:Ue,nonce:G,to:ve.nullish().default(null),transactionIndex:G.nullish(),value:G,v:G,r:G,s:G}),ef=a.object({address:ve.optional(),balance:G,codeHash:Ee,nonce:G,storageHash:Ee,accountProof:a.array($e),storageProof:a.array(a.object({key:G,value:G,proof:a.array($e)}))}),et=a.literal("latest").or(a.literal("earliest")).or(a.literal("pending")).or(a.literal("finalized")),bn=a.object({number:G.nullish().default(null),hash:Ee.nullish().default(null),parentHash:Ee,nonce:Yp(8).nullish().default(null),sha3Uncles:Ee,logsBloom:$p.nullish().default(null),transactionsRoot:Ee,stateRoot:Ee,receiptsRoot:Ee,miner:ve.nullish().default(null),mixHash:Ee.optional(),difficulty:G,totalDifficulty:G.nullish().default(null),extraData:Ue,size:G,gasLimit:G,gasUsed:G,timestamp:G,transactions:a.array(rn).or(a.array(Ee)),uncles:a.array(Ee),baseFeePerGas:G.optional()}),Mi=a.enum(["CONTINUE_WITH_PHANTOM","CONTINUE_WITH_METAMASK","ALWAYS_USE_PHANTOM","ALWAYS_USE_METAMASK"]),Li=a.string().refine(r=>{try{return Gp.default.decode(r).byteLength===32}catch{return!1}},{message:"String must be a valid solana public key of 32 bytes"}),Sm=a.union([a.literal("bip122_p2tr"),a.literal("bip122_p2wpkh"),a.literal("bip122_p2sh"),a.literal("bip122_p2pkh")]),Em=a.object({address:a.string(),publicKey:a.string(),addressType:Sm}),os=Em.and(a.object({purpose:a.union([a.literal("payment"),a.literal("ordinals")])})),FI=a.string().refine(r=>{try{return Gp.default.decode(r).byteLength===64}catch{return!1}},{message:"String must be a valid solana address of 64 bytes"}),fe=a.string().regex(/^[**********************************************************]*$/),Me=a.object({url:a.string().url(),icon:a.string().nullish().default(null),tabId:a.number().optional()}),_m=a.array(a.any()),Ja=a.unknown().transform((r,e)=>typeof r=="object"&&r!==null?r:(e.addIssue({code:a.ZodIssueCode.custom,message:"Not an object"}),a.NEVER)),zs=a.union([a.null(),a.string(),a.number(),a.boolean(),_m,Ja]),Ni=a.literal("2.0"),Ms=a.union([a.string(),a.number(),a.null()]),Qa=a.object({jsonrpc:a.literal("2.0"),id:Ms,method:a.string(),params:zs.optional()}),tf=a.array(Qa),vn=a.object({jsonrpc:a.literal("2.0"),method:a.string(),params:zs}),Ls=($=>($[$.ParseError=-32700]="ParseError",$[$.InternalError=-32603]="InternalError",$[$.InvalidParams=-32602]="InvalidParams",$[$.MethodNotFound=-32601]="MethodNotFound",$[$.InvalidRequest=-32600]="InvalidRequest",$[$.RequestCancelled=-32800]="RequestCancelled",$[$.TransactionRejected=-32003]="TransactionRejected",$[$.ResourceUnavailable=-32002]="ResourceUnavailable",$[$.InvalidInput=-32e3]="InvalidInput",$[$.UserRejectedRequest=4001]="UserRejectedRequest",$[$.Unauthorized=4100]="Unauthorized",$[$.UnsupportedMethod=4200]="UnsupportedMethod",$[$.RateLimited=4290]="RateLimited",$[$.Disconnected=4900]="Disconnected",$[$.ChainDisconnected=4901]="ChainDisconnected",$[$.ExecutionReverted=3]="ExecutionReverted",$))(Ls||{}),M=a.object({code:a.nativeEnum(Ls).or(a.number()),message:a.string()}),km=a.object({error:M}).or(a.object({result:zs})),Xa=a.object({jsonrpc:a.literal("2.0"),id:Ms}).and(km),rf=a.array(Xa),Ns=a.object({domain:a.string().optional(),address:a.string().optional(),statement:a.string().optional(),uri:a.string().optional(),version:a.string().optional(),chainId:a.string().optional(),nonce:a.string().optional(),issuedAt:a.string().optional(),expirationTime:a.string().optional(),notBefore:a.string().optional(),requestId:a.string().optional(),resources:a.array(a.string()).optional()}),Am=a.literal("mainnet"),Im=a.literal("testnet"),jI=a.literal("devnet"),HI=a.literal("localnet"),Rm=a.enum([Am.value,Im.value,jI.value,HI.value]),WI=a.literal("1"),KI=a.literal("11155111"),Bm=a.enum([WI.value,KI.value]),VI=a.literal("0x1"),GI=a.literal("0xaa36a7"),Pm=a.enum([VI.value,GI.value]),ZI=a.literal("137"),YI=a.literal("80002"),Tm=a.enum([ZI.value,YI.value]),$I=a.literal("0x89"),JI=a.literal("0x13882"),zm=a.enum([$I.value,JI.value]),QI=a.literal("8453"),XI=a.literal("84532"),Mm=a.enum([QI.value,XI.value]),e2=a.literal("0x2105"),t2=a.literal("0x14a34"),Lm=a.enum([e2.value,t2.value]),r2=a.literal("mainnet-beta"),n2=a.literal("testnet"),o2=a.literal("devnet"),Ci=fe,ec=fe,Cs=fe,nf=fe,Oi=fe,tc=a.string().url(),qi=a.string(),rc=a.enum([r2.value,n2.value,o2.value]),Tr=a.object({dapp_encryption_public_key:Ci,nonce:Cs,redirect_link:qi,payload:nf}),nn=a.object({nonce:Cs,data:Oi}),s2=a.object({name:a.string(),label:a.string().optional(),required:a.boolean().optional()}),i2=a.object({message:a.string()}),Nm=a.object({href:a.string(),label:a.string(),parameters:a.array(s2).optional()}),Cm=a.object({label:a.string(),url:a.string()}),Os=a.object({successMessage:a.string().optional(),failureMessage:a.string().optional(),pendingMessage:a.string().optional(),onSuccessAction:Cm.optional()}),of=a.object({domain:a.string(),name:a.string(),category:a.string(),actionUrl:a.string(),icon:a.string(),title:a.string(),description:a.string(),label:a.string(),disabled:a.boolean().optional(),links:a.object({actions:a.array(Nm)}).optional(),error:i2.optional(),postAction:Os.optional()})});function L(r,e){return a.object({jsonrpc:Ni,id:Ms,method:r,params:e})}function N(r,e){return a.object({jsonrpc:Ni,id:Ms}).and(a.object({result:r}).or(a.object({error:e})))}function vr(r,e){return a.object({jsonrpc:Ni,method:r,params:e})}var J=O(()=>{"use strict";y();V();Q()});var sf={};ue(sf,{error:()=>Dm,method:()=>Om,params:()=>qm,request:()=>a2,response:()=>c2,result:()=>Um});var Om,qm,Um,Dm,a2,c2,Fm=O(()=>{"use strict";y();V();Q();J();Om=a.literal("btc_requestAccounts"),qm=a.tuple([]),Um=a.array(os),Dm=M,a2=L(Om,qm),c2=N(Um,Dm)});var af={};ue(af,{error:()=>Km,method:()=>jm,params:()=>Hm,request:()=>u2,response:()=>p2,result:()=>Wm});var jm,Hm,Wm,Km,u2,p2,Vm=O(()=>{"use strict";y();V();Q();J();jm=a.literal("btc_signPSBT"),Hm=a.tuple([a.instanceof(Uint8Array),a.object({inputsToSign:a.array(a.object({address:a.string(),signingIndexes:a.array(a.number()),sigHash:a.number().optional()})),finalize:a.boolean()})]),Wm=a.instanceof(Uint8Array),Km=M,u2=L(jm,Hm),p2=N(Wm,Km)});var cf={};ue(cf,{error:()=>$m,method:()=>Gm,params:()=>Zm,request:()=>f2,response:()=>l2,result:()=>Ym});var Gm,Zm,Ym,$m,f2,l2,Jm=O(()=>{"use strict";y();V();Q();J();Gm=a.literal("btc_signMessage"),Zm=a.object({address:a.string(),message:a.instanceof(Uint8Array)}),Ym=a.object({signature:a.instanceof(Uint8Array),signedMessage:a.instanceof(Uint8Array)}),$m=M,f2=L(Gm,Zm),l2=N(Ym,$m)});var nc={};ue(nc,{btc_requestAccounts:()=>sf,btc_signMessage:()=>cf,btc_signPSBT:()=>af});var Qm=O(()=>{"use strict";y();Fm();Vm();Jm()});var uf={};ue(uf,{error:()=>r0,method:()=>Xm,params:()=>e0,request:()=>h2,response:()=>d2,result:()=>t0});var Xm,e0,t0,r0,h2,d2,n0=O(()=>{"use strict";y();V();Q();J();Xm=a.literal("eth_accounts"),e0=a.tuple([]),t0=a.array(ve),r0=M,h2=L(Xm,e0),d2=N(t0,r0)});var pf={};ue(pf,{error:()=>a0,method:()=>o0,params:()=>s0,request:()=>y2,response:()=>m2,result:()=>i0});var o0,s0,i0,a0,y2,m2,c0=O(()=>{"use strict";y();V();Q();J();o0=a.literal("eth_blockNumber"),s0=a.tuple([]),i0=G,a0=M,y2=L(o0,s0),m2=N(i0,a0)});var ff={};ue(ff,{error:()=>l0,method:()=>u0,params:()=>p0,request:()=>g2,response:()=>x2,result:()=>f0});var u0,p0,f0,l0,g2,x2,h0=O(()=>{"use strict";y();V();Q();J();u0=a.literal("eth_call"),p0=a.tuple([zi,G.or(et)]),f0=Ue,l0=M,g2=L(u0,p0),x2=N(f0,l0)});var lf={};ue(lf,{error:()=>g0,method:()=>d0,params:()=>y0,request:()=>w2,response:()=>b2,result:()=>m0});var d0,y0,m0,g0,w2,b2,x0=O(()=>{"use strict";y();V();Q();J();d0=a.literal("eth_cancelPrivateTransaction"),y0=a.tuple([a.object({txHash:Ee})]),m0=a.boolean(),g0=M,w2=L(d0,y0),b2=N(m0,g0)});var hf={};ue(hf,{error:()=>S0,method:()=>w0,params:()=>b0,request:()=>v2,response:()=>S2,result:()=>v0});var w0,b0,v0,S0,v2,S2,E0=O(()=>{"use strict";y();V();Q();J();w0=a.literal("eth_chainId"),b0=a.tuple([]),v0=$e,S0=M,v2=L(w0,b0),S2=N(v0,S0)});var df={};ue(df,{error:()=>I0,method:()=>_0,params:()=>k0,request:()=>E2,response:()=>_2,result:()=>A0});var _0,k0,A0,I0,E2,_2,R0=O(()=>{"use strict";y();V();Q();J();_0=a.literal("eth_estimateGas"),k0=a.tuple([zi]),A0=G,I0=M,E2=L(_0,k0),_2=N(A0,I0)});var yf={};ue(yf,{error:()=>z0,method:()=>B0,params:()=>P0,request:()=>k2,response:()=>A2,result:()=>T0});var B0,P0,T0,z0,k2,A2,M0=O(()=>{"use strict";y();V();Q();J();B0=a.literal("eth_feeHistory"),P0=a.tuple([a.number(),G.or(et),a.array(a.number()).optional()]),T0=a.object({oldestBlock:a.number(),reward:a.array(a.tuple([G,G])).optional(),baseFeePerGas:a.array(G),gasUsedRatio:a.array(a.number())}),z0=M,k2=L(B0,P0),A2=N(T0,z0)});var mf={};ue(mf,{error:()=>O0,method:()=>L0,params:()=>N0,request:()=>I2,response:()=>R2,result:()=>C0});var L0,N0,C0,O0,I2,R2,q0=O(()=>{"use strict";y();V();Q();J();L0=a.literal("eth_gasPrice"),N0=a.tuple([]),C0=G,O0=M,I2=L(L0,N0),R2=N(C0,O0)});var gf={};ue(gf,{error:()=>j0,method:()=>U0,params:()=>D0,request:()=>B2,response:()=>P2,result:()=>F0});var U0,D0,F0,j0,B2,P2,H0=O(()=>{"use strict";y();V();Q();J();U0=a.literal("eth_getBalance"),D0=a.tuple([ve,G.or(et)]),F0=G,j0=M,B2=L(U0,D0),P2=N(F0,j0)});var xf={};ue(xf,{error:()=>G0,method:()=>W0,params:()=>K0,request:()=>T2,response:()=>z2,result:()=>V0});var W0,K0,V0,G0,T2,z2,Z0=O(()=>{"use strict";y();V();Q();J();W0=a.literal("eth_getBlockByHash"),K0=a.tuple([Ee,a.boolean()]),V0=bn,G0=M,T2=L(W0,K0),z2=N(V0,G0)});var wf={};ue(wf,{error:()=>Q0,method:()=>Y0,params:()=>$0,request:()=>M2,response:()=>L2,result:()=>J0});var Y0,$0,J0,Q0,M2,L2,X0=O(()=>{"use strict";y();V();Q();J();Y0=a.literal("eth_getBlockByNumber"),$0=a.tuple([G.or(et),a.boolean()]),J0=bn,Q0=M,M2=L(Y0,$0),L2=N(J0,Q0)});var bf={};ue(bf,{error:()=>ng,method:()=>eg,params:()=>tg,request:()=>N2,response:()=>C2,result:()=>rg});var eg,tg,rg,ng,N2,C2,og=O(()=>{"use strict";y();V();Q();J();eg=a.literal("eth_getBlockReceipts"),tg=a.tuple([Ee.or(G).or(et)]),rg=a.array(Ti),ng=M,N2=L(eg,tg),C2=N(rg,ng)});var vf={};ue(vf,{error:()=>cg,method:()=>sg,params:()=>ig,request:()=>O2,response:()=>q2,result:()=>ag});var sg,ig,ag,cg,O2,q2,ug=O(()=>{"use strict";y();V();Q();J();sg=a.literal("eth_getBlockTransactionCountByHash"),ig=a.tuple([Ee]),ag=G,cg=M,O2=L(sg,ig),q2=N(ag,cg)});var Sf={};ue(Sf,{error:()=>hg,method:()=>pg,params:()=>fg,request:()=>U2,response:()=>D2,result:()=>lg});var pg,fg,lg,hg,U2,D2,dg=O(()=>{"use strict";y();V();Q();J();pg=a.literal("eth_getBlockTransactionCountByNumber"),fg=a.tuple([G]),lg=G,hg=M,U2=L(pg,fg),D2=N(lg,hg)});var Ef={};ue(Ef,{error:()=>xg,method:()=>yg,params:()=>mg,request:()=>F2,response:()=>j2,result:()=>gg});var yg,mg,gg,xg,F2,j2,wg=O(()=>{"use strict";y();V();Q();J();yg=a.literal("eth_getCode"),mg=a.tuple([ve,a.union([G,et])]),gg=Ue,xg=M,F2=L(yg,mg),j2=N(gg,xg)});var _f={};ue(_f,{error:()=>Eg,method:()=>bg,params:()=>vg,request:()=>H2,response:()=>W2,result:()=>Sg});var bg,vg,Sg,Eg,H2,W2,_g=O(()=>{"use strict";y();V();Q();J();bg=a.literal("eth_getFilterChanges"),vg=a.tuple([G]),Sg=a.array(tn),Eg=M,H2=L(bg,vg),W2=N(Sg,Eg)});var kf={};ue(kf,{error:()=>Rg,method:()=>kg,params:()=>Ag,request:()=>K2,response:()=>V2,result:()=>Ig});var kg,Ag,Ig,Rg,K2,V2,Bg=O(()=>{"use strict";y();V();Q();J();kg=a.literal("eth_getFilterLogs"),Ag=a.tuple([G]),Ig=a.array(tn),Rg=M,K2=L(kg,Ag),V2=N(Ig,Rg)});var Af={};ue(Af,{error:()=>Mg,method:()=>Pg,params:()=>Tg,request:()=>G2,response:()=>Z2,result:()=>zg});var Pg,Tg,zg,Mg,G2,Z2,Lg=O(()=>{"use strict";y();V();Q();J();Pg=a.literal("eth_getLogs"),Tg=a.tuple([a.object({fromBlock:G.or(et).optional(),toBlock:a.string().optional(),address:ve.optional(),topics:a.array(Ee).optional(),blockHash:Ee.optional()})]),zg=a.array(tn),Mg=M,G2=L(Pg,Tg),Z2=N(zg,Mg)});var If={};ue(If,{error:()=>qg,method:()=>Ng,params:()=>Cg,request:()=>Y2,response:()=>$2,result:()=>Og});var Ng,Cg,Og,qg,Y2,$2,Ug=O(()=>{"use strict";y();V();Q();J();Ng=a.literal("eth_getProof"),Cg=a.tuple([ve,a.array(Ee),G.or(et)]),Og=ef,qg=M,Y2=L(Ng,Cg),$2=N(Og,qg)});var Rf={};ue(Rf,{error:()=>Hg,method:()=>Dg,params:()=>Fg,request:()=>J2,response:()=>Q2,result:()=>jg});var Dg,Fg,jg,Hg,J2,Q2,Wg=O(()=>{"use strict";y();V();Q();J();Dg=a.literal("eth_getStorageAt"),Fg=a.tuple([ve,G,G.or(et)]),jg=Ue,Hg=M,J2=L(Dg,Fg),Q2=N(jg,Hg)});var Bf={};ue(Bf,{error:()=>Zg,method:()=>Kg,params:()=>Vg,request:()=>X2,response:()=>eR,result:()=>Gg});var Kg,Vg,Gg,Zg,X2,eR,Yg=O(()=>{"use strict";y();V();Q();J();Kg=a.literal("eth_getTransactionByBlockHashAndIndex"),Vg=a.tuple([Ee,G]),Gg=rn.nullish().default(null),Zg=M,X2=L(Kg,Vg),eR=N(Gg,Zg)});var Pf={};ue(Pf,{error:()=>Xg,method:()=>$g,params:()=>Jg,request:()=>tR,response:()=>rR,result:()=>Qg});var $g,Jg,Qg,Xg,tR,rR,ex=O(()=>{"use strict";y();V();Q();J();$g=a.literal("eth_getTransactionByBlockNumberAndIndex"),Jg=a.tuple([a.string(),G]),Qg=rn,Xg=M,tR=L($g,Jg),rR=N(Qg,Xg)});var Tf={};ue(Tf,{error:()=>ox,method:()=>tx,params:()=>rx,request:()=>nR,response:()=>oR,result:()=>nx});var tx,rx,nx,ox,nR,oR,sx=O(()=>{"use strict";y();V();Q();J();tx=a.literal("eth_getTransactionByHash"),rx=a.tuple([Ee]),nx=rn.nullable(),ox=M,nR=L(tx,rx),oR=N(nx,ox)});var zf={};ue(zf,{error:()=>ux,method:()=>ix,params:()=>ax,request:()=>sR,response:()=>iR,result:()=>cx});var ix,ax,cx,ux,sR,iR,px=O(()=>{"use strict";y();V();Q();J();ix=a.literal("eth_getTransactionCount"),ax=a.tuple([ve,G.or(et)]),cx=G,ux=M,sR=L(ix,ax),iR=N(cx,ux)});var Mf={};ue(Mf,{error:()=>dx,method:()=>fx,params:()=>lx,request:()=>aR,response:()=>cR,result:()=>hx});var fx,lx,hx,dx,aR,cR,yx=O(()=>{"use strict";y();V();Q();J();fx=a.literal("eth_getTransactionReceipt"),lx=a.tuple([Ee]),hx=Ti.nullish().default(null),dx=M,aR=L(fx,lx),cR=N(hx,dx)});var Lf={};ue(Lf,{error:()=>wx,method:()=>mx,params:()=>gx,request:()=>uR,response:()=>pR,result:()=>xx});var mx,gx,xx,wx,uR,pR,bx=O(()=>{"use strict";y();V();Q();J();mx=a.literal("eth_getUncleByBlockHashAndIndex"),gx=a.tuple([G.or(et),G]),xx=bn,wx=M,uR=L(mx,gx),pR=N(xx,wx)});var Nf={};ue(Nf,{error:()=>_x,method:()=>vx,params:()=>Sx,request:()=>fR,response:()=>lR,result:()=>Ex});var vx,Sx,Ex,_x,fR,lR,kx=O(()=>{"use strict";y();V();Q();J();vx=a.literal("eth_getUncleByBlockNumberAndIndex"),Sx=a.tuple([G.or(et),G]),Ex=bn,_x=M,fR=L(vx,Sx),lR=N(Ex,_x)});var Cf={};ue(Cf,{error:()=>Bx,method:()=>Ax,params:()=>Ix,request:()=>hR,response:()=>dR,result:()=>Rx});var Ax,Ix,Rx,Bx,hR,dR,Px=O(()=>{"use strict";y();V();Q();J();Ax=a.literal("eth_getUncleCountByBlockHash"),Ix=a.tuple([Ee]),Rx=G,Bx=M,hR=L(Ax,Ix),dR=N(Rx,Bx)});var Of={};ue(Of,{error:()=>Lx,method:()=>Tx,params:()=>zx,request:()=>yR,response:()=>mR,result:()=>Mx});var Tx,zx,Mx,Lx,yR,mR,Nx=O(()=>{"use strict";y();V();Q();J();Tx=a.literal("eth_getUncleCountByBlockNumber"),zx=a.tuple([G.or(et)]),Mx=G,Lx=M,yR=L(Tx,zx),mR=N(Mx,Lx)});var qf={};ue(qf,{error:()=>Ux,method:()=>Cx,params:()=>Ox,request:()=>gR,response:()=>xR,result:()=>qx});var Cx,Ox,qx,Ux,gR,xR,Dx=O(()=>{"use strict";y();V();Q();J();Cx=a.literal("eth_maxPriorityFeePerGas"),Ox=a.tuple([]),qx=G,Ux=M,gR=L(Cx,Ox),xR=N(qx,Ux)});var Uf={};ue(Uf,{error:()=>Wx,method:()=>Fx,params:()=>jx,request:()=>wR,response:()=>bR,result:()=>Hx});var Fx,jx,Hx,Wx,wR,bR,Kx=O(()=>{"use strict";y();V();Q();J();Fx=a.literal("eth_newBlockFilter"),jx=a.tuple([]),Hx=G,Wx=M,wR=L(Fx,jx),bR=N(Hx,Wx)});var Df={};ue(Df,{error:()=>Yx,method:()=>Vx,params:()=>Gx,request:()=>vR,response:()=>SR,result:()=>Zx});var Vx,Gx,Zx,Yx,vR,SR,$x=O(()=>{"use strict";y();V();Q();J();Vx=a.literal("eth_newFilter"),Gx=a.tuple([a.object({fromBlock:G.optional(),toBlock:G.optional(),address:ve.or(a.array(ve)).optional(),topics:a.array(Ue.nullish().default(null).or(a.array(Ue.nullish().default(null)))).optional()})]),Zx=G,Yx=M,vR=L(Vx,Gx),SR=N(Zx,Yx)});var Ff={};ue(Ff,{error:()=>ew,method:()=>Jx,params:()=>Qx,request:()=>ER,response:()=>_R,result:()=>Xx});var Jx,Qx,Xx,ew,ER,_R,tw=O(()=>{"use strict";y();V();Q();J();Jx=a.literal("eth_newPendingTransactionFilter"),Qx=a.tuple([]),Xx=G,ew=M,ER=L(Jx,Qx),_R=N(Xx,ew)});var jf={};ue(jf,{error:()=>sw,method:()=>rw,params:()=>nw,request:()=>kR,response:()=>AR,result:()=>ow});var rw,nw,ow,sw,kR,AR,iw=O(()=>{"use strict";y();V();Q();J();rw=a.literal("personal_sign"),nw=a.union([a.tuple([Ts,ve]),a.tuple([Ts,ve,a.unknown()])]),ow=Ue,sw=M,kR=L(rw,nw),AR=N(ow,sw)});var Hf={};ue(Hf,{error:()=>pw,method:()=>aw,params:()=>cw,request:()=>IR,response:()=>RR,result:()=>uw});var aw,cw,uw,pw,IR,RR,fw=O(()=>{"use strict";y();V();Q();J();aw=a.literal("eth_protocolVersion"),cw=a.tuple([]),uw=a.string(),pw=M,IR=L(aw,cw),RR=N(uw,pw)});var Wf={};ue(Wf,{error:()=>yw,method:()=>lw,params:()=>hw,request:()=>BR,response:()=>PR,result:()=>dw});var lw,hw,dw,yw,BR,PR,mw=O(()=>{"use strict";y();V();Q();J();lw=a.literal("eth_requestAccounts"),hw=a.tuple([]),dw=a.array(ve),yw=M,BR=L(lw,hw),PR=N(dw,yw)});var Kf={};ue(Kf,{error:()=>bw,method:()=>gw,params:()=>xw,request:()=>TR,response:()=>zR,result:()=>ww});var gw,xw,ww,bw,TR,zR,vw=O(()=>{"use strict";y();V();Q();J();gw=a.literal("eth_sendPrivateTransaction"),xw=a.tuple([a.object({tx:Ee,maxBlockNumber:G.optional(),preferences:a.object({fast:a.boolean()}).optional()})]),ww=Ee,bw=M,TR=L(gw,xw),zR=N(ww,bw)});var Vf={};ue(Vf,{error:()=>kw,method:()=>Sw,params:()=>Ew,request:()=>MR,response:()=>LR,result:()=>_w});var Sw,Ew,_w,kw,MR,LR,Aw=O(()=>{"use strict";y();V();Q();J();Sw=a.literal("eth_sendRawTransaction"),Ew=a.tuple([Ue]),_w=Ee,kw=M,MR=L(Sw,Ew),LR=N(_w,kw)});var Gf={};ue(Gf,{error:()=>Pw,method:()=>Iw,params:()=>Rw,request:()=>NR,response:()=>CR,result:()=>Bw});var Iw,Rw,Bw,Pw,NR,CR,Tw=O(()=>{"use strict";y();V();Q();J();Iw=a.literal("eth_sendTransaction"),Rw=a.tuple([ns]),Bw=Ee,Pw=M,NR=L(Iw,Rw),CR=N(Bw,Pw)});var Zf={};ue(Zf,{error:()=>Nw,method:()=>zw,params:()=>Mw,request:()=>OR,response:()=>qR,result:()=>Lw});var zw,Mw,Lw,Nw,OR,qR,Cw=O(()=>{"use strict";y();V();Q();J();zw=a.literal("eth_sign"),Mw=a.tuple([ve,Ts]),Lw=Ue,Nw=M,OR=L(zw,Mw),qR=N(Lw,Nw)});var Yf={};ue(Yf,{error:()=>Dw,method:()=>Ow,params:()=>qw,request:()=>UR,response:()=>DR,result:()=>Uw});var Ow,qw,Uw,Dw,UR,DR,Fw=O(()=>{"use strict";y();V();Q();J();Ow=a.literal("eth_signTransaction"),qw=a.tuple([ns]),Uw=Ue,Dw=M,UR=L(Ow,qw),DR=N(Uw,Dw)});var $f={};ue($f,{error:()=>Kw,method:()=>jw,params:()=>Hw,request:()=>FR,response:()=>jR,result:()=>Ww});var jw,Hw,Ww,Kw,FR,jR,Vw=O(()=>{"use strict";y();V();Q();J();jw=a.literal("eth_signTypedData"),Hw=a.tuple([Jp,ve]),Ww=Ue,Kw=M,FR=L(jw,Hw),jR=N(Ww,Kw)});var Jf={};ue(Jf,{error:()=>$w,method:()=>Gw,params:()=>Zw,request:()=>HR,response:()=>WR,result:()=>Yw});var Gw,Zw,Yw,$w,HR,WR,Jw=O(()=>{"use strict";y();V();Q();J();Gw=a.literal("eth_signTypedData_v3"),Zw=a.tuple([ve,a.string().transform((r,e)=>{try{let t=JSON.parse(r);return Ya.parse(t)}catch(t){return e.addIssue({code:a.ZodIssueCode.custom,message:"Invalid typed data:"+t.message,fatal:!0}),a.NEVER}}).or(Ya)]),Yw=Ue,$w=M,HR=L(Gw,Zw),WR=N(Yw,$w)});var Qf={};ue(Qf,{error:()=>tb,method:()=>Qw,params:()=>Xw,request:()=>KR,response:()=>VR,result:()=>eb});var Qw,Xw,eb,tb,KR,VR,rb=O(()=>{"use strict";y();V();Q();J();Qw=a.literal("eth_signTypedData_v4"),Xw=a.tuple([ve,a.string().transform((r,e)=>{try{let t=JSON.parse(r);return $a.parse(t)}catch(t){return e.addIssue({code:a.ZodIssueCode.custom,message:"Invalid typed data:"+t.message,fatal:!0}),a.NEVER}}).or($a)]),eb=Ue,tb=M,KR=L(Qw,Xw),VR=N(eb,tb)});var Xf={};ue(Xf,{error:()=>ib,method:()=>nb,params:()=>ob,request:()=>GR,response:()=>ZR,result:()=>sb});var nb,ob,sb,ib,GR,ZR,ab=O(()=>{"use strict";y();V();Q();J();nb=a.literal("eth_subscribe"),ob=a.any(),sb=a.union([$e,a.object({result:rn,subscription:$e}),a.object({result:Ee,subscription:$e}),a.object({result:a.object({difficulty:$e,extraData:$e,gasLimit:$e,gasUsed:$e,logsBloom:$e,miner:ve,nonce:$e,number:$e,parentHash:Ee,receiptRoot:Ee,sha3Uncles:Ee,stateRoot:Ee,timestamp:$e,transactionsRoot:Ee}),subscription:$e}),a.object({result:tn,subscription:$e})]),ib=M,GR=L(nb,ob),ZR=N(sb,ib)});var el={};ue(el,{error:()=>fb,method:()=>cb,params:()=>ub,request:()=>YR,response:()=>$R,result:()=>pb});var cb,ub,pb,fb,YR,$R,lb=O(()=>{"use strict";y();V();Q();J();cb=a.literal("eth_syncing"),ub=a.tuple([]),pb=a.union([a.object({currentBlock:G,highestBlock:G,startingBlock:G}),a.literal(!1)]),fb=M,YR=L(cb,ub),$R=N(pb,fb)});var tl={};ue(tl,{error:()=>mb,method:()=>hb,params:()=>db,request:()=>JR,response:()=>QR,result:()=>yb});var hb,db,yb,mb,JR,QR,gb=O(()=>{"use strict";y();V();Q();J();hb=a.literal("eth_uninstallFilter"),db=a.tuple([G]),yb=a.boolean(),mb=M,JR=L(hb,db),QR=N(yb,mb)});var rl={};ue(rl,{error:()=>vb,method:()=>xb,params:()=>wb,request:()=>XR,response:()=>eB,result:()=>bb});var xb,wb,bb,vb,XR,eB,Sb=O(()=>{"use strict";y();V();Q();J();xb=a.literal("eth_unsubscribe"),wb=a.any(),bb=a.boolean(),vb=M,XR=L(xb,wb),eB=N(bb,vb)});var nl={};ue(nl,{error:()=>Ab,method:()=>Eb,params:()=>_b,request:()=>tB,response:()=>rB,result:()=>kb});var Eb,_b,kb,Ab,tB,rB,Ib=O(()=>{"use strict";y();V();Q();J();Eb=a.literal("net_listening"),_b=a.tuple([]),kb=a.boolean(),Ab=M,tB=L(Eb,_b),rB=N(kb,Ab)});var ol={};ue(ol,{error:()=>Tb,method:()=>Rb,params:()=>Bb,request:()=>nB,response:()=>oB,result:()=>Pb});var Rb,Bb,Pb,Tb,nB,oB,zb=O(()=>{"use strict";y();V();Q();J();Rb=a.literal("net_version"),Bb=a.tuple([]),Pb=a.string(),Tb=M,nB=L(Rb,Bb),oB=N(Pb,Tb)});var sl={};ue(sl,{error:()=>Cb,method:()=>Mb,params:()=>Lb,request:()=>sB,response:()=>iB,result:()=>Nb});var Mb,Lb,Nb,Cb,sB,iB,Ob=O(()=>{"use strict";y();V();Q();J();Mb=a.literal("wallet_addEthereumChain"),Lb=a.tuple([a.object({chainId:$e,chainName:a.string(),nativeCurrency:a.object({name:a.string(),symbol:a.string().refine(r=>{let{length:e}=r;return e>=2&&e<=6},{message:"Value is not a valid symbol."}),decimals:a.number()}),rpcUrls:a.array(a.string()),blockExplorerUrls:a.union([a.tuple([a.string()]),a.null()]).optional(),iconUrls:a.array(a.string()).optional()})]),Nb=a.null(),Cb=M,sB=L(Mb,Lb),iB=N(Nb,Cb)});var il={};ue(il,{error:()=>Fb,method:()=>qb,params:()=>Ub,request:()=>aB,response:()=>cB,result:()=>Db});var qb,Ub,Db,Fb,aB,cB,jb=O(()=>{"use strict";y();V();Q();J();qb=a.literal("wallet_selectEthereumProvider"),Ub=a.tuple([]),Db=Mi,Fb=M,aB=L(qb,Ub),cB=N(Db,Fb)});var al={};ue(al,{error:()=>Vb,method:()=>Hb,params:()=>Wb,request:()=>uB,response:()=>pB,result:()=>Kb});var Hb,Wb,Kb,Vb,uB,pB,Gb=O(()=>{"use strict";y();V();Q();J();Hb=a.literal("wallet_switchEthereumChain"),Wb=a.tuple([a.object({chainId:$e})]),Kb=a.null(),Vb=M,uB=L(Hb,Wb),pB=N(Kb,Vb)});var cl={};ue(cl,{error:()=>Jb,method:()=>Zb,params:()=>Yb,request:()=>fB,response:()=>lB,result:()=>$b});var Zb,Yb,$b,Jb,fB,lB,Qb=O(()=>{"use strict";y();V();Q();J();Zb=a.literal("wallet_watchAsset"),Yb=a.object({type:a.literal("ERC20"),options:a.object({address:ve,symbol:a.string(),decimals:a.number(),image:a.string()})}),$b=a.boolean(),Jb=M,fB=L(Zb,Yb),lB=N($b,Jb)});var ul={};ue(ul,{error:()=>r1,method:()=>Xb,params:()=>e1,request:()=>hB,response:()=>dB,result:()=>t1});var Xb,e1,t1,r1,hB,dB,n1=O(()=>{"use strict";y();V();Q();J();Xb=a.literal("web3_clientVersion"),e1=a.tuple([]),t1=a.string(),r1=M,hB=L(Xb,e1),dB=N(t1,r1)});var pl={};ue(pl,{error:()=>a1,method:()=>o1,params:()=>s1,request:()=>yB,response:()=>mB,result:()=>i1});var o1,s1,i1,a1,yB,mB,c1=O(()=>{"use strict";y();V();Q();J();o1=a.literal("web3_sha3"),s1=a.tuple([Ue]),i1=Ue,a1=M,yB=L(o1,s1),mB=N(i1,a1)});var fl={};ue(fl,{error:()=>l1,method:()=>u1,params:()=>p1,request:()=>bB,response:()=>vB,result:()=>f1});var gB,xB,wB,u1,p1,f1,l1,bB,vB,h1=O(()=>{"use strict";y();V();Q();J();gB=a.record(a.string(),a.any()),xB=a.record(a.string(),gB),wB=a.object({parentCapability:a.string(),date:a.number().optional()}),u1=a.literal("wallet_requestPermissions"),p1=a.tuple([xB]),f1=a.array(wB),l1=M,bB=L(u1,p1),vB=N(f1,l1)});var ll={};ue(ll,{error:()=>g1,method:()=>d1,params:()=>y1,request:()=>_B,response:()=>kB,result:()=>m1});var SB,EB,d1,y1,m1,g1,_B,kB,x1=O(()=>{"use strict";y();V();Q();J();SB=a.object({type:a.string(),value:a.any()}),EB=a.object({invoker:a.string().url(),parentCapability:a.string(),caveats:a.array(SB)}),d1=a.literal("wallet_getPermissions"),y1=a.tuple([]),m1=a.array(EB),g1=M,_B=L(d1,y1),kB=N(m1,g1)});var qs={};ue(qs,{eth_accounts:()=>uf,eth_blockNumber:()=>pf,eth_call:()=>ff,eth_cancelPrivateTransaction:()=>lf,eth_chainId:()=>hf,eth_estimateGas:()=>df,eth_feeHistory:()=>yf,eth_gasPrice:()=>mf,eth_getBalance:()=>gf,eth_getBlockByHash:()=>xf,eth_getBlockByNumber:()=>wf,eth_getBlockReceipts:()=>bf,eth_getBlockTransactionCountByHash:()=>vf,eth_getBlockTransactionCountByNumber:()=>Sf,eth_getCode:()=>Ef,eth_getFilterChanges:()=>_f,eth_getFilterLogs:()=>kf,eth_getLogs:()=>Af,eth_getProof:()=>If,eth_getStorageAt:()=>Rf,eth_getTransactionByBlockHashAndIndex:()=>Bf,eth_getTransactionByBlockNumberAndIndex:()=>Pf,eth_getTransactionByHash:()=>Tf,eth_getTransactionCount:()=>zf,eth_getTransactionReceipt:()=>Mf,eth_getUncleByBlockHashAndIndex:()=>Lf,eth_getUncleByBlockNumberAndIndex:()=>Nf,eth_getUncleCountByBlockHash:()=>Cf,eth_getUncleCountByBlockNumber:()=>Of,eth_maxPriorityFeePerGas:()=>qf,eth_newBlockFilter:()=>Uf,eth_newFilter:()=>Df,eth_newPendingTransactionFilter:()=>Ff,eth_protocolVersion:()=>Hf,eth_requestAccounts:()=>Wf,eth_sendPrivateTransaction:()=>Kf,eth_sendRawTransaction:()=>Vf,eth_sendTransaction:()=>Gf,eth_sign:()=>Zf,eth_signTransaction:()=>Yf,eth_signTypedData:()=>$f,eth_signTypedData_v3:()=>Jf,eth_signTypedData_v4:()=>Qf,eth_subscribe:()=>Xf,eth_syncing:()=>el,eth_uninstallFilter:()=>tl,eth_unsubscribe:()=>rl,net_listening:()=>nl,net_version:()=>ol,personal_sign:()=>jf,wallet_addEthereumChain:()=>sl,wallet_getPermissions:()=>ll,wallet_requestPermissions:()=>fl,wallet_selectEthereumProvider:()=>il,wallet_switchEthereumChain:()=>al,wallet_watchAsset:()=>cl,web3_clientVersion:()=>ul,web3_sha3:()=>pl});var w1=O(()=>{"use strict";y();n0();c0();h0();x0();E0();R0();M0();q0();H0();Z0();X0();og();ug();dg();wg();_g();Bg();Lg();Ug();Wg();Yg();ex();sx();px();yx();bx();kx();Px();Nx();Dx();Kx();$x();tw();iw();fw();mw();vw();Aw();Tw();Cw();Fw();Vw();Jw();rb();ab();lb();gb();Sb();Ib();zb();Ob();jb();Gb();Qb();n1();c1();h1();x1()});var hl={};ue(hl,{method:()=>b1,notification:()=>AB,params:()=>v1});var b1,v1,AB,S1=O(()=>{"use strict";y();V();Q();J();b1=a.literal("phantom_accountChanged"),v1=a.object({evm:a.optional(ve),sol:a.optional(Li),btc:a.array(os).optional()}).nullish().default(null),AB=vr(b1,v1)});var dl={};ue(dl,{method:()=>E1,notification:()=>IB,params:()=>_1});var E1,_1,IB,k1=O(()=>{"use strict";y();V();J();E1=a.literal("phantom_metaMaskOverrideSettingsChanged"),_1=a.null(),IB=vr(E1,_1)});var yl={};ue(yl,{method:()=>A1,notification:()=>RB,params:()=>I1});var A1,I1,RB,R1=O(()=>{"use strict";y();V();Q();J();A1=a.literal("phantom_chainChanged"),I1=a.object({evm:G}).nullish().default(null),RB=vr(A1,I1)});var ml={};ue(ml,{method:()=>B1,notification:()=>BB,params:()=>P1});var B1,P1,BB,T1=O(()=>{"use strict";y();V();J();B1=a.literal("phantom_dappIcon"),P1=a.string().nullish().default(null),BB=vr(B1,P1)});var gl={};ue(gl,{method:()=>z1,notification:()=>PB,params:()=>M1});var z1,M1,PB,L1=O(()=>{"use strict";y();V();J();z1=a.literal("phantom_dappMeta"),M1=a.object({title:a.string(),url:a.string(),icons:a.object({href:a.string(),size:a.object({width:a.number(),height:a.number()})}).array()}),PB=vr(z1,M1)});var xl={};ue(xl,{method:()=>N1,notification:()=>TB,params:()=>C1});var N1,C1,TB,O1=O(()=>{"use strict";y();V();Q();J();N1=a.literal("phantom_trustRevoked"),C1=a.object({evm:a.optional(ve),sol:a.optional(Li),btc:a.array(os).optional()}).nullish().default(null),TB=vr(N1,C1)});var Sr={};ue(Sr,{phantom_accountChanged:()=>hl,phantom_chainChanged:()=>yl,phantom_dappIcon:()=>ml,phantom_dappMeta:()=>gl,phantom_metaMaskOverrideSettingsChanged:()=>dl,phantom_trustRevoked:()=>xl});var q1=O(()=>{"use strict";y();S1();k1();R1();T1();L1();O1()});var zB,MB,LB,NB,bF,vF,U1=O(()=>{"use strict";y();V();Q();J();zB=a.literal("phantom_deep_link_browse"),MB=a.object({url:a.string(),ref:a.string()}),LB=a.null(),NB=M,bF=L(zB,MB),vF=N(LB,NB)});var OB,qB,UB,DB,AF,IF,D1=O(()=>{"use strict";y();V();Q();J();OB=a.literal("phantom_deep_link_swap"),qB=a.object({buy:a.string(),sell:a.string()}),UB=a.null(),DB=M,AF=L(OB,qB),IF=N(UB,DB)});var jB,HB,WB,KB,zF,MF,F1=O(()=>{"use strict";y();V();Q();J();jB=a.literal("phantom_deep_link_fungible"),HB=a.object({token:a.string()}),WB=a.null(),KB=M,zF=L(jB,HB),MF=N(WB,KB)});var GB,ZB,YB,$B,qF,UF,DF,j1=O(()=>{"use strict";y();V();Q();J();GB=a.literal("phantom_deep_link_connect"),ZB=a.object({app_url:tc,dapp_encryption_public_key:Ci,redirect_link:qi,cluster:rc.optional()}),YB=a.object({phantom_encryption_public_key:ec,nonce:Cs,data:Oi}),$B=M,qF=L(GB,ZB),UF=N(YB,$B),DF=a.object({public_key:fe,session:fe})});var QB,XB,eP,tP,KF,VF,GF,H1=O(()=>{"use strict";y();V();Q();J();QB=a.literal("phantom_deep_link_disconnect"),XB=Tr,eP=a.null(),tP=M,KF=L(QB,XB),VF=N(eP,tP),GF=a.object({session:fe})});var nP,oP,sP,iP,QF,XF,e7,t7,W1=O(()=>{"use strict";y();V();Q();J();nP=a.literal("phantom_deep_link_signMessage"),oP=Tr,sP=nn,iP=M,QF=L(nP,oP),XF=N(sP,iP),e7=a.object({session:fe,message:fe,display:a.union([a.literal("utf8"),a.literal("hex")]).optional()}),t7=a.object({signature:fe,publicKey:fe})});var cP,uP,pP,fP,i7,a7,c7,K1=O(()=>{"use strict";y();V();Q();J();cP=a.literal("phantom_deep_link_signIn"),uP=a.object({app_url:tc,dapp_encryption_public_key:Ci,redirect_link:qi,cluster:rc.optional(),payload:nf}),pP=a.object({phantom_encryption_public_key:ec,nonce:Cs,data:Oi}),fP=M,i7=L(cP,uP),a7=N(pP,fP),c7=a.object({address:fe,signedMessage:fe,signature:fe,session:fe})});var hP,dP,yP,mP,h7,d7,y7,m7,V1=O(()=>{"use strict";y();V();Q();J();hP=a.literal("phantom_deep_link_signTransaction"),dP=Tr,yP=nn,mP=M,h7=L(hP,dP),d7=N(yP,mP),y7=a.object({session:fe,transaction:fe}),m7=a.object({transaction:fe})});var xP,wP,bP,vP,v7,S7,E7,_7,G1=O(()=>{"use strict";y();V();Q();J();xP=a.literal("phantom_deep_link_signAllTransactions"),wP=Tr,bP=nn,vP=M,v7=L(xP,wP),S7=N(bP,vP),E7=a.object({session:fe,transactions:a.array(fe)}),_7=a.object({transactions:a.array(fe)})});var bl={};ue(bl,{SolanaProviderEvent:()=>wl,SolanaSendOptions:()=>Sn});var Sn,wl,I7,Us=O(()=>{"use strict";y();V();Sn=a.optional(a.object({skipPreflight:a.optional(a.boolean()),preflightCommitment:a.optional(a.union([a.literal("processed"),a.literal("confirmed"),a.literal("finalized"),a.literal("recent"),a.literal("single"),a.literal("singleGossip"),a.literal("root"),a.literal("max")])),maxRetries:a.optional(a.number()),minContextSlot:a.optional(a.number())})),wl=(n=>(n.Connect="connect",n.Disconnect="disconnect",n.AccountChanged="accountChanged",n))(wl||{}),I7=a.nativeEnum(wl)});var EP,_P,kP,AP,M7,L7,N7,C7,Z1=O(()=>{"use strict";y();V();Q();Us();J();EP=a.literal("phantom_deep_link_signAndSendTransaction"),_P=Tr,kP=nn,AP=M,M7=L(EP,_P),L7=N(kP,AP),N7=a.object({session:fe,transaction:fe,sendOptions:Sn.optional()}),C7=a.object({signature:fe})});var RP,BP,PP,TP,j7,H7,W7,K7,Y1=O(()=>{"use strict";y();V();Q();Us();J();RP=a.literal("phantom_deep_link_signAndSendAllTransactions"),BP=Tr,PP=nn,TP=M,j7=L(RP,BP),H7=N(PP,TP),W7=a.object({session:fe,transactions:a.array(fe),sendOptions:Sn.optional()}),K7=a.object({signatures:a.array(a.union([fe,a.null()]))})});var MP,LP,NP,CP,$7,J7,$1=O(()=>{"use strict";y();V();Q();J();MP=a.literal("phantom_deep_link_tokens"),LP=a.object({chain:a.string(),address:a.string().optional(),referralId:a.string().optional()}),NP=a.null(),CP=M,$7=L(MP,LP),J7=N(NP,CP)});var qP,UP,DP,FP,rj,nj,J1=O(()=>{"use strict";y();V();Q();J();qP=a.literal("phantom_deep_link_onboard"),UP=a.object({value:a.string().optional(),accounts:a.string().optional()}),DP=a.null(),FP=M,rj=L(qP,UP),nj=N(DP,FP)});var Q1=O(()=>{"use strict";y();U1();D1();F1();j1();H1();W1();K1();V1();G1();Z1();Y1();$1();J1()});var sc={};ue(sc,{error:()=>tv,method:()=>X1,params:()=>ev,request:()=>WP,response:()=>KP,result:()=>oc});var X1,ev,oc,tv,WP,KP,vl=O(()=>{"use strict";y();V();Q();J();X1=a.literal("sol_connect"),ev=a.object({onlyIfTrusted:a.optional(a.boolean())}),oc=a.object({publicKey:a.string()}),tv=M,WP=L(X1,ev),KP=N(oc,tv)});var Sl={};ue(Sl,{error:()=>sv,method:()=>rv,params:()=>nv,request:()=>VP,response:()=>GP,result:()=>ov});var rv,nv,ov,sv,VP,GP,iv=O(()=>{"use strict";y();V();Q();J();rv=a.literal("sol_disconnect"),nv=zs.optional(),ov=a.null(),sv=M,VP=L(rv,nv),GP=N(ov,sv)});var El={};ue(El,{error:()=>pv,method:()=>av,params:()=>cv,request:()=>ZP,response:()=>YP,result:()=>uv});var av,cv,uv,pv,ZP,YP,fv=O(()=>{"use strict";y();V();Q();J();av=a.literal("sol_signAllTransactions"),cv=a.object({transactions:a.array(fe)}),uv=a.array(a.object({signature:a.string(),transaction:fe,version:a.union([a.literal("legacy"),a.number()])})),pv=M,ZP=L(av,cv),YP=N(uv,pv)});var _l={};ue(_l,{error:()=>yv,method:()=>lv,params:()=>hv,request:()=>$P,response:()=>JP,result:()=>dv});var lv,hv,dv,yv,$P,JP,mv=O(()=>{"use strict";y();V();Q();J();Us();lv=a.literal("sol_signAndSendTransaction"),hv=a.object({transaction:fe,options:Sn,showConfirmation:a.boolean().optional(),postAction:Os.optional()}),dv=a.object({signature:a.string(),publicKey:a.string()}),yv=M,$P=L(lv,hv),JP=N(dv,yv)});var cc={};ue(cc,{error:()=>xv,method:()=>gv,params:()=>ic,request:()=>QP,response:()=>XP,result:()=>ac});var gv,ic,ac,xv,QP,XP,kl=O(()=>{"use strict";y();V();Q();J();Us();gv=a.literal("sol_signAndSendAllTransactions"),ic=a.object({transactions:a.array(fe),options:Sn}),ac=a.object({signatures:a.array(a.union([a.string(),a.null()])),publicKey:a.string()}),xv=M,QP=L(gv,ic),XP=N(ac,xv)});var Al={};ue(Al,{error:()=>Sv,method:()=>wv,params:()=>bv,request:()=>eT,response:()=>tT,result:()=>vv});var wv,bv,vv,Sv,eT,tT,Ev=O(()=>{"use strict";y();V();Q();J();wv=a.literal("sol_signMessage"),bv=a.object({message:fe,display:a.union([a.literal("utf8"),a.literal("hex")])}),vv=a.object({signature:a.string(),publicKey:a.string()}),Sv=M,eT=L(wv,bv),tT=N(vv,Sv)});var Il={};ue(Il,{error:()=>Iv,method:()=>_v,params:()=>kv,request:()=>rT,response:()=>nT,result:()=>Av});var _v,kv,Av,Iv,rT,nT,Rv=O(()=>{"use strict";y();V();Q();J();_v=a.literal("sol_signIn"),kv=a.object({signInData:Ns}),Av=a.object({address:a.string(),signedMessage:a.string(),signature:a.string()}),Iv=M,rT=L(_v,kv),nT=N(Av,Iv)});var Rl={};ue(Rl,{error:()=>zv,method:()=>Bv,params:()=>Pv,request:()=>oT,response:()=>sT,result:()=>Tv});var Bv,Pv,Tv,zv,oT,sT,Mv=O(()=>{"use strict";y();V();Q();J();Bv=a.literal("sol_signTransaction"),Pv=a.object({transaction:fe}),Tv=a.object({signature:a.string(),transaction:fe,version:a.union([a.literal("legacy"),a.number()])}),zv=M,oT=L(Bv,Pv),sT=N(Tv,zv)});var zr={};ue(zr,{common:()=>bl,sol_connect:()=>sc,sol_disconnect:()=>Sl,sol_signAllTransactions:()=>El,sol_signAndSendAllTransactions:()=>cc,sol_signAndSendTransaction:()=>_l,sol_signIn:()=>Il,sol_signMessage:()=>Al,sol_signTransaction:()=>Rl});var Lv=O(()=>{"use strict";y();vl();iv();fv();mv();kl();Ev();Rv();Mv();Us()});var uc,Bl=O(()=>{"use strict";y();V();uc=a.object({blinkUrl:a.string().url(),blinkTitle:a.string().optional()})});var iT,aT,cT,uT,Zj,Yj,Nv=O(()=>{"use strict";y();V();Q();vl();J();Bl();iT=a.literal("sol_blink_connect"),aT=a.object({context:uc}),cT=oc,uT=M,Zj=L(iT,aT),Yj=N(cT,uT)});var fT,lT,hT,dT,t9,r9,Cv=O(()=>{"use strict";y();V();Q();kl();J();Bl();fT=a.literal("sol_blink_signAndSendAllTransactions"),lT=ic.merge(a.object({context:uc})),hT=ac,dT=M,t9=L(fT,lT),r9=N(hT,dT)});var Ov=O(()=>{"use strict";y();Nv();Cv()});var pc,gT,fc,Pl,Tl,zl,Ml,Ll,i9,Nl,Cl,Ui,Ol,xT,ss=O(()=>{"use strict";y();V();pc=a.object({identityName:a.string().nullish(),identityUri:a.string().nullish(),iconRelativeUri:a.string().nullish()}),gT=a.object({identity:pc,authorizationScope:a.string()}),fc=a.object({verifiableIdentity:gT,publicKey:a.string(),payloads:a.array(a.string())}),Pl=a.object({identity:pc,cluster:a.string().optional()}),Tl=a.object({verifiableIdentity:pc}),zl=a.object({signPayloads:fc,minContextSlot:a.number()}),Ml=a.object({signPayloads:fc}),Ll=a.object({signPayloads:fc}),i9=a.union([Pl,Tl,zl,Ml,Ll]),Nl=a.union([a.object({type:a.literal("AUTHORIZE_SUCCESS"),publicKey:a.string(),accountLabel:a.string().optional(),walletUriBase:a.string().optional(),scope:a.string().optional()}),a.object({type:a.literal("AUTHORIZE_DECLINE")})]),Cl=a.union([a.object({type:a.literal("REAUTHORIZE_SUCCESS")}),a.object({type:a.literal("REAUTHORIZE_DECLINE")})]),Ui=a.union([a.object({type:a.literal("SIGN_PAYLOADS_SUCCESS"),signedPayloads:a.array(a.string())}),a.object({type:a.literal("SIGN_PAYLOADS_DECLINE")}),a.object({type:a.literal("SIGN_PAYLOADS_ERROR_INVALID_PAYLOADS"),valid:a.array(a.boolean())}),a.object({type:a.literal("SIGN_PAYLOADS_ERROR_AUTHORIZATION_NOT_VALID")}),a.object({type:a.literal("SIGN_PAYLOADS_ERROR_TOO_MANY_PAYLOADS")})]),Ol=a.union([a.object({type:a.literal("SIGN_AND_SEND_TRANSACTIONS_SUCCESS"),signedPayloads:a.array(a.string())}),a.object({type:a.literal("SIGN_AND_SEND_TRANSACTIONS_DECLINE")}),a.object({type:a.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_INVALID_PAYLOADS"),valid:a.array(a.boolean())}),a.object({type:a.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_NOT_SUBMITTED"),signatures:a.array(a.string())}),a.object({type:a.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_TOO_MANY_PAYLOADS")}),a.object({type:a.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_AUTHORIZATION_NOT_VALID")})]),xT=a.union([Nl,Cl,Ui,Ol])});var wT,bT,vT,ST,h9,d9,qv=O(()=>{"use strict";y();V();Q();ss();J();wT=a.literal("sol_mwa_authorize"),bT=Pl,vT=Nl,ST=M,h9=L(wT,bT),d9=N(vT,ST)});var _T,kT,AT,IT,b9,v9,Uv=O(()=>{"use strict";y();V();Q();ss();J();_T=a.literal("sol_mwa_reauthorize"),kT=Tl,AT=Cl,IT=M,b9=L(_T,kT),v9=N(AT,IT)});var BT,PT,TT,zT,I9,R9,Dv=O(()=>{"use strict";y();V();Q();ss();J();BT=a.literal("sol_mwa_sign_transactions"),PT=Ml,TT=Ui,zT=M,I9=L(BT,PT),R9=N(TT,zT)});var LT,NT,CT,OT,L9,N9,Fv=O(()=>{"use strict";y();V();Q();ss();J();LT=a.literal("sol_mwa_sign_messages"),NT=Ll,CT=Ui,OT=M,L9=L(LT,NT),N9=N(CT,OT)});var UT,DT,FT,jT,F9,j9,jv=O(()=>{"use strict";y();V();Q();ss();J();UT=a.literal("sol_mwa_sign_and_send_transactions"),DT=zl,FT=Ol,jT=M,F9=L(UT,DT),j9=N(FT,jT)});var Hv=O(()=>{"use strict";y();qv();Uv();Dv();Fv();jv()});function Kv(r){var e,t,n,o=W.prototype={constructor:W,toString:null,valueOf:null},s=new W(1),i=20,u=4,f=-7,g=21,b=-1e7,E=1e7,q=!1,C=1,K=0,ee={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:"\xA0",suffix:""},$="0123456789abcdefghijklmnopqrstuvwxyz",le=!0;function W(_,A){var z,D,R,m,c,p,h,d,x=this;if(!(x instanceof W))return new W(_,A);if(A==null){if(_&&_._isBigNumber===!0){x.s=_.s,!_.c||_.e>E?x.c=x.e=null:_.e<b?x.c=[x.e=0]:(x.e=_.e,x.c=_.c.slice());return}if((p=typeof _=="number")&&_*0==0){if(x.s=1/_<0?(_=-_,-1):1,_===~~_){for(m=0,c=_;c>=10;c/=10,m++);m>E?x.c=x.e=null:(x.e=m,x.c=[_]);return}d=String(_)}else{if(!KT.test(d=String(_)))return n(x,d,p);x.s=d.charCodeAt(0)==45?(d=d.slice(1),-1):1}(m=d.indexOf("."))>-1&&(d=d.replace(".","")),(c=d.search(/e/i))>0?(m<0&&(m=c),m+=+d.slice(c+1),d=d.substring(0,c)):m<0&&(m=d.length)}else{if(Ut(A,2,$.length,"Base"),A==10&&le)return x=new W(_),Ie(x,i+x.e+1,u);if(d=String(_),p=typeof _=="number"){if(_*0!=0)return n(x,d,p,A);if(x.s=1/_<0?(d=d.slice(1),-1):1,W.DEBUG&&d.replace(/^0\.0*|\./,"").length>15)throw Error(Wv+_)}else x.s=d.charCodeAt(0)===45?(d=d.slice(1),-1):1;for(z=$.slice(0,A),m=c=0,h=d.length;c<h;c++)if(z.indexOf(D=d.charAt(c))<0){if(D=="."){if(c>m){m=h;continue}}else if(!R&&(d==d.toUpperCase()&&(d=d.toLowerCase())||d==d.toLowerCase()&&(d=d.toUpperCase()))){R=!0,c=-1,m=0;continue}return n(x,String(_),p,A)}p=!1,d=t(d,A,10,x.s),(m=d.indexOf("."))>-1?d=d.replace(".",""):m=d.length}for(c=0;d.charCodeAt(c)===48;c++);for(h=d.length;d.charCodeAt(--h)===48;);if(d=d.slice(c,++h)){if(h-=c,p&&W.DEBUG&&h>15&&(_>Ul||_!==on(_)))throw Error(Wv+x.s*_);if((m=m-c-1)>E)x.c=x.e=null;else if(m<b)x.c=[x.e=0];else{if(x.e=m,x.c=[],c=(m+1)%Le,m<0&&(c+=Le),c<h){for(c&&x.c.push(+d.slice(0,c)),h-=Le;c<h;)x.c.push(+d.slice(c,c+=Le));c=Le-(d=d.slice(c)).length}else c-=h;for(;c--;d+="0");x.c.push(+d)}}else x.c=[x.e=0]}W.clone=Kv,W.ROUND_UP=0,W.ROUND_DOWN=1,W.ROUND_CEIL=2,W.ROUND_FLOOR=3,W.ROUND_HALF_UP=4,W.ROUND_HALF_DOWN=5,W.ROUND_HALF_EVEN=6,W.ROUND_HALF_CEIL=7,W.ROUND_HALF_FLOOR=8,W.EUCLID=9,W.config=W.set=function(_){var A,z;if(_!=null)if(typeof _=="object"){if(_.hasOwnProperty(A="DECIMAL_PLACES")&&(z=_[A],Ut(z,0,Gt,A),i=z),_.hasOwnProperty(A="ROUNDING_MODE")&&(z=_[A],Ut(z,0,8,A),u=z),_.hasOwnProperty(A="EXPONENTIAL_AT")&&(z=_[A],z&&z.pop?(Ut(z[0],-Gt,0,A),Ut(z[1],0,Gt,A),f=z[0],g=z[1]):(Ut(z,-Gt,Gt,A),f=-(g=z<0?-z:z))),_.hasOwnProperty(A="RANGE"))if(z=_[A],z&&z.pop)Ut(z[0],-Gt,-1,A),Ut(z[1],1,Gt,A),b=z[0],E=z[1];else if(Ut(z,-Gt,Gt,A),z)b=-(E=z<0?-z:z);else throw Error(mr+A+" cannot be zero: "+z);if(_.hasOwnProperty(A="CRYPTO"))if(z=_[A],z===!!z)if(z)if(typeof crypto<"u"&&crypto&&(crypto.getRandomValues||crypto.randomBytes))q=z;else throw q=!z,Error(mr+"crypto unavailable");else q=z;else throw Error(mr+A+" not true or false: "+z);if(_.hasOwnProperty(A="MODULO_MODE")&&(z=_[A],Ut(z,0,9,A),C=z),_.hasOwnProperty(A="POW_PRECISION")&&(z=_[A],Ut(z,0,Gt,A),K=z),_.hasOwnProperty(A="FORMAT"))if(z=_[A],typeof z=="object")ee=z;else throw Error(mr+A+" not an object: "+z);if(_.hasOwnProperty(A="ALPHABET"))if(z=_[A],typeof z=="string"&&!/^.?$|[+\-.\s]|(.).*\1/.test(z))le=z.slice(0,10)=="0123456789",$=z;else throw Error(mr+A+" invalid: "+z)}else throw Error(mr+"Object expected: "+_);return{DECIMAL_PLACES:i,ROUNDING_MODE:u,EXPONENTIAL_AT:[f,g],RANGE:[b,E],CRYPTO:q,MODULO_MODE:C,POW_PRECISION:K,FORMAT:ee,ALPHABET:$}},W.isBigNumber=function(_){if(!_||_._isBigNumber!==!0)return!1;if(!W.DEBUG)return!0;var A,z,D=_.c,R=_.e,m=_.s;e:if({}.toString.call(D)=="[object Array]"){if((m===1||m===-1)&&R>=-Gt&&R<=Gt&&R===on(R)){if(D[0]===0){if(R===0&&D.length===1)return!0;break e}if(A=(R+1)%Le,A<1&&(A+=Le),String(D[0]).length==A){for(A=0;A<D.length;A++)if(z=D[A],z<0||z>=Dr||z!==on(z))break e;if(z!==0)return!0}}}else if(D===null&&R===null&&(m===null||m===1||m===-1))return!0;throw Error(mr+"Invalid BigNumber: "+_)},W.maximum=W.max=function(){return Te(arguments,o.lt)},W.minimum=W.min=function(){return Te(arguments,o.gt)},W.random=function(){var _=9007199254740992,A=Math.random()*_&2097151?function(){return on(Math.random()*_)}:function(){return(Math.random()*1073741824|0)*8388608+(Math.random()*8388608|0)};return function(z){var D,R,m,c,p,h=0,d=[],x=new W(s);if(z==null?z=i:Ut(z,0,Gt),c=ql(z/Le),q)if(crypto.getRandomValues){for(D=crypto.getRandomValues(new Uint32Array(c*=2));h<c;)p=D[h]*131072+(D[h+1]>>>11),p>=9e15?(R=crypto.getRandomValues(new Uint32Array(2)),D[h]=R[0],D[h+1]=R[1]):(d.push(p%1e14),h+=2);h=c/2}else if(crypto.randomBytes){for(D=crypto.randomBytes(c*=7);h<c;)p=(D[h]&31)*281474976710656+D[h+1]*1099511627776+D[h+2]*4294967296+D[h+3]*16777216+(D[h+4]<<16)+(D[h+5]<<8)+D[h+6],p>=9e15?crypto.randomBytes(7).copy(D,h):(d.push(p%1e14),h+=7);h=c/7}else throw q=!1,Error(mr+"crypto unavailable");if(!q)for(;h<c;)p=A(),p<9e15&&(d[h++]=p%1e14);for(c=d[--h],z%=Le,c&&z&&(p=Dl[Le-z],d[h]=on(c/p)*p);d[h]===0;d.pop(),h--);if(h<0)d=[m=0];else{for(m=-1;d[0]===0;d.splice(0,1),m-=Le);for(h=1,p=d[0];p>=10;p/=10,h++);h<Le&&(m-=Le-h)}return x.e=m,x.c=d,x}}(),W.sum=function(){for(var _=1,A=arguments,z=new W(A[0]);_<A.length;)z=z.plus(A[_++]);return z},t=function(){var _="0123456789";function A(z,D,R,m){for(var c,p=[0],h,d=0,x=z.length;d<x;){for(h=p.length;h--;p[h]*=D);for(p[0]+=m.indexOf(z.charAt(d++)),c=0;c<p.length;c++)p[c]>R-1&&(p[c+1]==null&&(p[c+1]=0),p[c+1]+=p[c]/R|0,p[c]%=R)}return p.reverse()}return function(z,D,R,m,c){var p,h,d,x,S,P,v,l,k=z.indexOf("."),se=i,w=u;for(k>=0&&(x=K,K=0,z=z.replace(".",""),l=new W(D),P=l.pow(z.length-k),K=x,l.c=A(En(Mr(P.c),P.e,"0"),10,R,_),l.e=l.c.length),v=A(z,D,R,c?(p=$,_):(p=_,$)),d=x=v.length;v[--x]==0;v.pop());if(!v[0])return p.charAt(0);if(k<0?--d:(P.c=v,P.e=d,P.s=m,P=e(P,l,se,w,R),v=P.c,S=P.r,d=P.e),h=d+se+1,k=v[h],x=R/2,S=S||h<0||v[h+1]!=null,S=w<4?(k!=null||S)&&(w==0||w==(P.s<0?3:2)):k>x||k==x&&(w==4||S||w==6&&v[h-1]&1||w==(P.s<0?8:7)),h<1||!v[0])z=S?En(p.charAt(1),-se,p.charAt(0)):p.charAt(0);else{if(v.length=h,S)for(--R;++v[--h]>R;)v[h]=0,h||(++d,v=[1].concat(v));for(x=v.length;!v[--x];);for(k=0,z="";k<=x;z+=p.charAt(v[k++]));z=En(z,d,p.charAt(0))}return z}}(),e=function(){function _(D,R,m){var c,p,h,d,x=0,S=D.length,P=R%Yn,v=R/Yn|0;for(D=D.slice();S--;)h=D[S]%Yn,d=D[S]/Yn|0,c=v*h+d*P,p=P*h+c%Yn*Yn+x,x=(p/m|0)+(c/Yn|0)+v*d,D[S]=p%m;return x&&(D=[x].concat(D)),D}function A(D,R,m,c){var p,h;if(m!=c)h=m>c?1:-1;else for(p=h=0;p<m;p++)if(D[p]!=R[p]){h=D[p]>R[p]?1:-1;break}return h}function z(D,R,m,c){for(var p=0;m--;)D[m]-=p,p=D[m]<R[m]?1:0,D[m]=p*c+D[m]-R[m];for(;!D[0]&&D.length>1;D.splice(0,1));}return function(D,R,m,c,p){var h,d,x,S,P,v,l,k,se,w,U,F,X,oe,we,ie,ae,qe=D.s==R.s?1:-1,ce=D.c,me=R.c;if(!ce||!ce[0]||!me||!me[0])return new W(!D.s||!R.s||(ce?me&&ce[0]==me[0]:!me)?NaN:ce&&ce[0]==0||!me?qe*0:qe/0);for(k=new W(qe),se=k.c=[],d=D.e-R.e,qe=m+d+1,p||(p=Dr,d=Lr(D.e/Le)-Lr(R.e/Le),qe=qe/Le|0),x=0;me[x]==(ce[x]||0);x++);if(me[x]>(ce[x]||0)&&d--,qe<0)se.push(1),S=!0;else{for(oe=ce.length,ie=me.length,x=0,qe+=2,P=on(p/(me[0]+1)),P>1&&(me=_(me,P,p),ce=_(ce,P,p),ie=me.length,oe=ce.length),X=ie,w=ce.slice(0,ie),U=w.length;U<ie;w[U++]=0);ae=me.slice(),ae=[0].concat(ae),we=me[0],me[1]>=p/2&&we++;do{if(P=0,h=A(me,w,ie,U),h<0){if(F=w[0],ie!=U&&(F=F*p+(w[1]||0)),P=on(F/we),P>1)for(P>=p&&(P=p-1),v=_(me,P,p),l=v.length,U=w.length;A(v,w,l,U)==1;)P--,z(v,ie<l?ae:me,l,p),l=v.length,h=1;else P==0&&(h=P=1),v=me.slice(),l=v.length;if(l<U&&(v=[0].concat(v)),z(w,v,U,p),U=w.length,h==-1)for(;A(me,w,ie,U)<1;)P++,z(w,ie<U?ae:me,U,p),U=w.length}else h===0&&(P++,w=[0]);se[x++]=P,w[0]?w[U++]=ce[X]||0:(w=[ce[X]],U=1)}while((X++<oe||w[0]!=null)&&qe--);S=w[0]!=null,se[0]||se.splice(0,1)}if(p==Dr){for(x=1,qe=se[0];qe>=10;qe/=10,x++);Ie(k,m+(k.e=x+d*Le-1)+1,c,S)}else k.e=d,k.r=+S;return k}}();function be(_,A,z,D){var R,m,c,p,h;if(z==null?z=u:Ut(z,0,8),!_.c)return _.toString();if(R=_.c[0],c=_.e,A==null)h=Mr(_.c),h=D==1||D==2&&(c<=f||c>=g)?hc(h,c):En(h,c,"0");else if(_=Ie(new W(_),A,z),m=_.e,h=Mr(_.c),p=h.length,D==1||D==2&&(A<=m||m<=f)){for(;p<A;h+="0",p++);h=hc(h,m)}else if(A-=c,h=En(h,m,"0"),m+1>p){if(--A>0)for(h+=".";A--;h+="0");}else if(A+=m-p,A>0)for(m+1==p&&(h+=".");A--;h+="0");return _.s<0&&R?"-"+h:h}function Te(_,A){for(var z,D=1,R=new W(_[0]);D<_.length;D++)if(z=new W(_[D]),z.s)A.call(R,z)&&(R=z);else{R=z;break}return R}function Oe(_,A,z){for(var D=1,R=A.length;!A[--R];A.pop());for(R=A[0];R>=10;R/=10,D++);return(z=D+z*Le-1)>E?_.c=_.e=null:z<b?_.c=[_.e=0]:(_.e=z,_.c=A),_}n=function(){var _=/^(-?)0([xbo])(?=\w[\w.]*$)/i,A=/^([^.]+)\.$/,z=/^\.([^.]+)$/,D=/^-?(Infinity|NaN)$/,R=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(m,c,p,h){var d,x=p?c:c.replace(R,"");if(D.test(x))m.s=isNaN(x)?null:x<0?-1:1;else{if(!p&&(x=x.replace(_,function(S,P,v){return d=(v=v.toLowerCase())=="x"?16:v=="b"?2:8,!h||h==d?P:S}),h&&(d=h,x=x.replace(A,"$1").replace(z,"0.$1")),c!=x))return new W(x,d);if(W.DEBUG)throw Error(mr+"Not a"+(h?" base "+h:"")+" number: "+c);m.s=null}m.c=m.e=null}}();function Ie(_,A,z,D){var R,m,c,p,h,d,x,S=_.c,P=Dl;if(S){e:{for(R=1,p=S[0];p>=10;p/=10,R++);if(m=A-R,m<0)m+=Le,c=A,h=S[d=0],x=h/P[R-c-1]%10|0;else if(d=ql((m+1)/Le),d>=S.length)if(D){for(;S.length<=d;S.push(0));h=x=0,R=1,m%=Le,c=m-Le+1}else break e;else{for(h=p=S[d],R=1;p>=10;p/=10,R++);m%=Le,c=m-Le+R,x=c<0?0:h/P[R-c-1]%10|0}if(D=D||A<0||S[d+1]!=null||(c<0?h:h%P[R-c-1]),D=z<4?(x||D)&&(z==0||z==(_.s<0?3:2)):x>5||x==5&&(z==4||D||z==6&&(m>0?c>0?h/P[R-c]:0:S[d-1])%10&1||z==(_.s<0?8:7)),A<1||!S[0])return S.length=0,D?(A-=_.e+1,S[0]=P[(Le-A%Le)%Le],_.e=-A||0):S[0]=_.e=0,_;if(m==0?(S.length=d,p=1,d--):(S.length=d+1,p=P[Le-m],S[d]=c>0?on(h/P[R-c]%P[c])*p:0),D)for(;;)if(d==0){for(m=1,c=S[0];c>=10;c/=10,m++);for(c=S[0]+=p,p=1;c>=10;c/=10,p++);m!=p&&(_.e++,S[0]==Dr&&(S[0]=1));break}else{if(S[d]+=p,S[d]!=Dr)break;S[d--]=0,p=1}for(m=S.length;S[--m]===0;S.pop());}_.e>E?_.c=_.e=null:_.e<b&&(_.c=[_.e=0])}return _}function Fe(_){var A,z=_.e;return z===null?_.toString():(A=Mr(_.c),A=z<=f||z>=g?hc(A,z):En(A,z,"0"),_.s<0?"-"+A:A)}return o.absoluteValue=o.abs=function(){var _=new W(this);return _.s<0&&(_.s=1),_},o.comparedTo=function(_,A){return Ds(this,new W(_,A))},o.decimalPlaces=o.dp=function(_,A){var z,D,R,m=this;if(_!=null)return Ut(_,0,Gt),A==null?A=u:Ut(A,0,8),Ie(new W(m),_+m.e+1,A);if(!(z=m.c))return null;if(D=((R=z.length-1)-Lr(this.e/Le))*Le,R=z[R])for(;R%10==0;R/=10,D--);return D<0&&(D=0),D},o.dividedBy=o.div=function(_,A){return e(this,new W(_,A),i,u)},o.dividedToIntegerBy=o.idiv=function(_,A){return e(this,new W(_,A),0,1)},o.exponentiatedBy=o.pow=function(_,A){var z,D,R,m,c,p,h,d,x,S=this;if(_=new W(_),_.c&&!_.isInteger())throw Error(mr+"Exponent not an integer: "+Fe(_));if(A!=null&&(A=new W(A)),p=_.e>14,!S.c||!S.c[0]||S.c[0]==1&&!S.e&&S.c.length==1||!_.c||!_.c[0])return x=new W(Math.pow(+Fe(S),p?_.s*(2-lc(_)):+Fe(_))),A?x.mod(A):x;if(h=_.s<0,A){if(A.c?!A.c[0]:!A.s)return new W(NaN);D=!h&&S.isInteger()&&A.isInteger(),D&&(S=S.mod(A))}else{if(_.e>9&&(S.e>0||S.e<-1||(S.e==0?S.c[0]>1||p&&S.c[1]>=24e7:S.c[0]<8e13||p&&S.c[0]<=9999975e7)))return m=S.s<0&&lc(_)?-0:0,S.e>-1&&(m=1/m),new W(h?1/m:m);K&&(m=ql(K/Le+2))}for(p?(z=new W(.5),h&&(_.s=1),d=lc(_)):(R=Math.abs(+Fe(_)),d=R%2),x=new W(s);;){if(d){if(x=x.times(S),!x.c)break;m?x.c.length>m&&(x.c.length=m):D&&(x=x.mod(A))}if(R){if(R=on(R/2),R===0)break;d=R%2}else if(_=_.times(z),Ie(_,_.e+1,1),_.e>14)d=lc(_);else{if(R=+Fe(_),R===0)break;d=R%2}S=S.times(S),m?S.c&&S.c.length>m&&(S.c.length=m):D&&(S=S.mod(A))}return D?x:(h&&(x=s.div(x)),A?x.mod(A):m?Ie(x,K,u,c):x)},o.integerValue=function(_){var A=new W(this);return _==null?_=u:Ut(_,0,8),Ie(A,A.e+1,_)},o.isEqualTo=o.eq=function(_,A){return Ds(this,new W(_,A))===0},o.isFinite=function(){return!!this.c},o.isGreaterThan=o.gt=function(_,A){return Ds(this,new W(_,A))>0},o.isGreaterThanOrEqualTo=o.gte=function(_,A){return(A=Ds(this,new W(_,A)))===1||A===0},o.isInteger=function(){return!!this.c&&Lr(this.e/Le)>this.c.length-2},o.isLessThan=o.lt=function(_,A){return Ds(this,new W(_,A))<0},o.isLessThanOrEqualTo=o.lte=function(_,A){return(A=Ds(this,new W(_,A)))===-1||A===0},o.isNaN=function(){return!this.s},o.isNegative=function(){return this.s<0},o.isPositive=function(){return this.s>0},o.isZero=function(){return!!this.c&&this.c[0]==0},o.minus=function(_,A){var z,D,R,m,c=this,p=c.s;if(_=new W(_,A),A=_.s,!p||!A)return new W(NaN);if(p!=A)return _.s=-A,c.plus(_);var h=c.e/Le,d=_.e/Le,x=c.c,S=_.c;if(!h||!d){if(!x||!S)return x?(_.s=-A,_):new W(S?c:NaN);if(!x[0]||!S[0])return S[0]?(_.s=-A,_):new W(x[0]?c:u==3?-0:0)}if(h=Lr(h),d=Lr(d),x=x.slice(),p=h-d){for((m=p<0)?(p=-p,R=x):(d=h,R=S),R.reverse(),A=p;A--;R.push(0));R.reverse()}else for(D=(m=(p=x.length)<(A=S.length))?p:A,p=A=0;A<D;A++)if(x[A]!=S[A]){m=x[A]<S[A];break}if(m&&(R=x,x=S,S=R,_.s=-_.s),A=(D=S.length)-(z=x.length),A>0)for(;A--;x[z++]=0);for(A=Dr-1;D>p;){if(x[--D]<S[D]){for(z=D;z&&!x[--z];x[z]=A);--x[z],x[D]+=Dr}x[D]-=S[D]}for(;x[0]==0;x.splice(0,1),--d);return x[0]?Oe(_,x,d):(_.s=u==3?-1:1,_.c=[_.e=0],_)},o.modulo=o.mod=function(_,A){var z,D,R=this;return _=new W(_,A),!R.c||!_.s||_.c&&!_.c[0]?new W(NaN):!_.c||R.c&&!R.c[0]?new W(R):(C==9?(D=_.s,_.s=1,z=e(R,_,0,3),_.s=D,z.s*=D):z=e(R,_,0,C),_=R.minus(z.times(_)),!_.c[0]&&C==1&&(_.s=R.s),_)},o.multipliedBy=o.times=function(_,A){var z,D,R,m,c,p,h,d,x,S,P,v,l,k,se,w=this,U=w.c,F=(_=new W(_,A)).c;if(!U||!F||!U[0]||!F[0])return!w.s||!_.s||U&&!U[0]&&!F||F&&!F[0]&&!U?_.c=_.e=_.s=null:(_.s*=w.s,!U||!F?_.c=_.e=null:(_.c=[0],_.e=0)),_;for(D=Lr(w.e/Le)+Lr(_.e/Le),_.s*=w.s,h=U.length,S=F.length,h<S&&(l=U,U=F,F=l,R=h,h=S,S=R),R=h+S,l=[];R--;l.push(0));for(k=Dr,se=Yn,R=S;--R>=0;){for(z=0,P=F[R]%se,v=F[R]/se|0,c=h,m=R+c;m>R;)d=U[--c]%se,x=U[c]/se|0,p=v*d+x*P,d=P*d+p%se*se+l[m]+z,z=(d/k|0)+(p/se|0)+v*x,l[m--]=d%k;l[m]=z}return z?++D:l.splice(0,1),Oe(_,l,D)},o.negated=function(){var _=new W(this);return _.s=-_.s||null,_},o.plus=function(_,A){var z,D=this,R=D.s;if(_=new W(_,A),A=_.s,!R||!A)return new W(NaN);if(R!=A)return _.s=-A,D.minus(_);var m=D.e/Le,c=_.e/Le,p=D.c,h=_.c;if(!m||!c){if(!p||!h)return new W(R/0);if(!p[0]||!h[0])return h[0]?_:new W(p[0]?D:R*0)}if(m=Lr(m),c=Lr(c),p=p.slice(),R=m-c){for(R>0?(c=m,z=h):(R=-R,z=p),z.reverse();R--;z.push(0));z.reverse()}for(R=p.length,A=h.length,R-A<0&&(z=h,h=p,p=z,A=R),R=0;A;)R=(p[--A]=p[A]+h[A]+R)/Dr|0,p[A]=Dr===p[A]?0:p[A]%Dr;return R&&(p=[R].concat(p),++c),Oe(_,p,c)},o.precision=o.sd=function(_,A){var z,D,R,m=this;if(_!=null&&_!==!!_)return Ut(_,1,Gt),A==null?A=u:Ut(A,0,8),Ie(new W(m),_,A);if(!(z=m.c))return null;if(R=z.length-1,D=R*Le+1,R=z[R]){for(;R%10==0;R/=10,D--);for(R=z[0];R>=10;R/=10,D++);}return _&&m.e+1>D&&(D=m.e+1),D},o.shiftedBy=function(_){return Ut(_,-Ul,Ul),this.times("1e"+_)},o.squareRoot=o.sqrt=function(){var _,A,z,D,R,m=this,c=m.c,p=m.s,h=m.e,d=i+4,x=new W("0.5");if(p!==1||!c||!c[0])return new W(!p||p<0&&(!c||c[0])?NaN:c?m:1/0);if(p=Math.sqrt(+Fe(m)),p==0||p==1/0?(A=Mr(c),(A.length+h)%2==0&&(A+="0"),p=Math.sqrt(+A),h=Lr((h+1)/2)-(h<0||h%2),p==1/0?A="5e"+h:(A=p.toExponential(),A=A.slice(0,A.indexOf("e")+1)+h),z=new W(A)):z=new W(p+""),z.c[0]){for(h=z.e,p=h+d,p<3&&(p=0);;)if(R=z,z=x.times(R.plus(e(m,R,d,1))),Mr(R.c).slice(0,p)===(A=Mr(z.c)).slice(0,p))if(z.e<h&&--p,A=A.slice(p-3,p+1),A=="9999"||!D&&A=="4999"){if(!D&&(Ie(R,R.e+i+2,0),R.times(R).eq(m))){z=R;break}d+=4,p+=4,D=1}else{(!+A||!+A.slice(1)&&A.charAt(0)=="5")&&(Ie(z,z.e+i+2,1),_=!z.times(z).eq(m));break}}return Ie(z,z.e+i+1,u,_)},o.toExponential=function(_,A){return _!=null&&(Ut(_,0,Gt),_++),be(this,_,A,1)},o.toFixed=function(_,A){return _!=null&&(Ut(_,0,Gt),_=_+this.e+1),be(this,_,A)},o.toFormat=function(_,A,z){var D,R=this;if(z==null)_!=null&&A&&typeof A=="object"?(z=A,A=null):_&&typeof _=="object"?(z=_,_=A=null):z=ee;else if(typeof z!="object")throw Error(mr+"Argument not an object: "+z);if(D=R.toFixed(_,A),R.c){var m,c=D.split("."),p=+z.groupSize,h=+z.secondaryGroupSize,d=z.groupSeparator||"",x=c[0],S=c[1],P=R.s<0,v=P?x.slice(1):x,l=v.length;if(h&&(m=p,p=h,h=m,l-=m),p>0&&l>0){for(m=l%p||p,x=v.substr(0,m);m<l;m+=p)x+=d+v.substr(m,p);h>0&&(x+=d+v.slice(m)),P&&(x="-"+x)}D=S?x+(z.decimalSeparator||"")+((h=+z.fractionGroupSize)?S.replace(new RegExp("\\d{"+h+"}\\B","g"),"$&"+(z.fractionGroupSeparator||"")):S):x}return(z.prefix||"")+D+(z.suffix||"")},o.toFraction=function(_){var A,z,D,R,m,c,p,h,d,x,S,P,v=this,l=v.c;if(_!=null&&(p=new W(_),!p.isInteger()&&(p.c||p.s!==1)||p.lt(s)))throw Error(mr+"Argument "+(p.isInteger()?"out of range: ":"not an integer: ")+Fe(p));if(!l)return new W(v);for(A=new W(s),d=z=new W(s),D=h=new W(s),P=Mr(l),m=A.e=P.length-v.e-1,A.c[0]=Dl[(c=m%Le)<0?Le+c:c],_=!_||p.comparedTo(A)>0?m>0?A:d:p,c=E,E=1/0,p=new W(P),h.c[0]=0;x=e(p,A,0,1),R=z.plus(x.times(D)),R.comparedTo(_)!=1;)z=D,D=R,d=h.plus(x.times(R=d)),h=R,A=p.minus(x.times(R=A)),p=R;return R=e(_.minus(z),D,0,1),h=h.plus(R.times(d)),z=z.plus(R.times(D)),h.s=d.s=v.s,m=m*2,S=e(d,D,m,u).minus(v).abs().comparedTo(e(h,z,m,u).minus(v).abs())<1?[d,D]:[h,z],E=c,S},o.toNumber=function(){return+Fe(this)},o.toPrecision=function(_,A){return _!=null&&Ut(_,1,Gt),be(this,_,A,2)},o.toString=function(_){var A,z=this,D=z.s,R=z.e;return R===null?D?(A="Infinity",D<0&&(A="-"+A)):A="NaN":(_==null?A=R<=f||R>=g?hc(Mr(z.c),R):En(Mr(z.c),R,"0"):_===10&&le?(z=Ie(new W(z),i+R+1,u),A=En(Mr(z.c),z.e,"0")):(Ut(_,2,$.length,"Base"),A=t(En(Mr(z.c),R,"0"),10,_,D,!0)),D<0&&z.c[0]&&(A="-"+A)),A},o.valueOf=o.toJSON=function(){return Fe(this)},o._isBigNumber=!0,o[Symbol.toStringTag]="BigNumber",o[Symbol.for("nodejs.util.inspect.custom")]=o.valueOf,r!=null&&W.set(r),W}function Lr(r){var e=r|0;return r>0||r===e?e:e-1}function Mr(r){for(var e,t,n=1,o=r.length,s=r[0]+"";n<o;){for(e=r[n++]+"",t=Le-e.length;t--;e="0"+e);s+=e}for(o=s.length;s.charCodeAt(--o)===48;);return s.slice(0,o+1||1)}function Ds(r,e){var t,n,o=r.c,s=e.c,i=r.s,u=e.s,f=r.e,g=e.e;if(!i||!u)return null;if(t=o&&!o[0],n=s&&!s[0],t||n)return t?n?0:-u:i;if(i!=u)return i;if(t=i<0,n=f==g,!o||!s)return n?0:!o^t?1:-1;if(!n)return f>g^t?1:-1;for(u=(f=o.length)<(g=s.length)?f:g,i=0;i<u;i++)if(o[i]!=s[i])return o[i]>s[i]^t?1:-1;return f==g?0:f>g^t?1:-1}function Ut(r,e,t,n){if(r<e||r>t||r!==on(r))throw Error(mr+(n||"Argument")+(typeof r=="number"?r<e||r>t?" out of range: ":" not an integer: ":" not a primitive number: ")+String(r))}function lc(r){var e=r.c.length-1;return Lr(r.e/Le)==e&&r.c[e]%2!=0}function hc(r,e){return(r.length>1?r.charAt(0)+"."+r.slice(1):r)+(e<0?"e":"e+")+e}function En(r,e,t){var n,o;if(e<0){for(o=t+".";++e;o+=t);r=o+r}else if(n=r.length,++e>n){for(o=t,e-=n;--e;o+=t);r+=o}else e<n&&(r=r.slice(0,e)+"."+r.slice(e));return r}var KT,ql,on,mr,Wv,Dr,Le,Ul,Dl,Yn,Gt,dc,Vv,Fl=O(()=>{y();KT=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,ql=Math.ceil,on=Math.floor,mr="[BigNumber Error] ",Wv=mr+"Number primitive has more than 15 significant digits: ",Dr=1e14,Le=14,Ul=9007199254740991,Dl=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],Yn=1e7,Gt=1e9;dc=Kv(),Vv=dc});var VT,GT,ZT,YT,J9,Q9,Gv=O(()=>{"use strict";y();Fl();V();Q();J();VT=a.literal("sol_pay_transfer"),GT=a.object({amount:a.instanceof(Vv).optional(),recipient:a.string(),splToken:a.string().optional(),reference:a.array(a.string()).optional(),memo:a.string().optional(),label:a.string().optional(),message:a.string().optional()}),ZT=a.null(),YT=M,J9=L(VT,GT),Q9=N(ZT,YT)});var JT,QT,XT,ez,nH,oH,Zv=O(()=>{"use strict";y();V();Q();J();JT=a.literal("sol_pay_transaction"),QT=a.object({link:a.string().url()}),XT=a.null(),ez=M,nH=L(JT,QT),oH=N(XT,ez)});var Yv=O(()=>{"use strict";y();Gv();Zv()});var nz,oz,sz,iz,pH,fH,$v=O(()=>{"use strict";y();V();Q();J();nz=a.literal("user_approveBtcRequestAccounts"),oz=a.tuple([Me]),sz=a.null(),iz=M,pH=L(nz,oz),fH=N(sz,iz)});var cz,uz,pz,fz,mH,gH,Jv=O(()=>{"use strict";y();V();Q();J();cz=a.literal("user_approveBtcSignPSBT"),uz=a.tuple([Me,a.object({psbt:a.instanceof(Uint8Array),inputsToSign:a.array(a.object({address:a.string(),signingIndexes:a.array(a.number()),sigHash:a.number().optional()})),finalize:a.boolean()})]),pz=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend")}),a.object({type:a.literal("send"),signature:a.instanceof(Uint8Array)})]),fz=M,mH=L(cz,uz),gH=N(pz,fz)});var hz,dz,yz,mz,SH,EH,Qv=O(()=>{"use strict";y();V();Q();J();hz=a.literal("user_approveBtcSignMessage"),dz=a.tuple([Me,a.object({message:a.instanceof(Uint8Array)})]),yz=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend")}),a.object({type:a.literal("send"),signature:a.instanceof(Uint8Array),signedMessage:a.instanceof(Uint8Array)})]),mz=M,SH=L(hz,dz),EH=N(yz,mz)});var xz,wz,bz,vz,RH,BH,Xv=O(()=>{"use strict";y();V();Q();J();xz=a.literal("user_approveEthRequestAccounts"),wz=a.tuple([Me]),bz=a.null(),vz=M,RH=L(xz,wz),BH=N(bz,vz)});var Ez,_z,kz,Az,LH,NH,eS=O(()=>{"use strict";y();V();Q();J();Ez=a.literal("user_approveWalletRequestPermissions"),_z=a.tuple([Me]),kz=a.null(),Az=M,LH=L(Ez,_z),NH=N(kz,Az)});var er,_n=O(()=>{"use strict";y();er=(C=>(C.OK="OK",C.FeatureKilled="FEATURE_KILLED",C.WalletLocked="WALLET_LOCKED",C.TabNotFocused="TAB_NOT_FOCUSED",C.Disabled="DISABLED",C.SessionExpired="SESSION_EXPIRED",C.RateLimitExceeded="RATE_LIMIT_EXCEEDED",C.SimulationFailed="SIMULATION_FAILED",C.UnsupportedDapp="UNSUPPORTED_DAPP",C.UnsupportedNetworkId="UNSUPPORTED_NETWORK_ID",C.UnsupportedMethod="UNSUPPORTED_METHOD",C.Unimplemented="UNIMPLEMENTED",C.Unknown="UNKNOWN",C))(er||{})});var Rz,Bz,Pz,Tz,HH,WH,tS=O(()=>{"use strict";y();V();_n();Q();J();Rz=a.literal("user_approveEthSendTransaction"),Bz=a.tuple([Me,a.object({transaction:ns,autoConfirmStatusCode:a.nativeEnum(er)})]),Pz=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend"),maxFeePerGas:G,maxPriorityFeePerGas:G}),a.object({type:a.literal("send"),signature:$e,maxFeePerGas:G,maxPriorityFeePerGas:G})]),Tz=M,HH=L(Rz,Bz),WH=N(Pz,Tz)});var Mz,Lz,Nz,Cz,$H,JH,rS=O(()=>{"use strict";y();V();_n();Q();J();Mz=a.literal("user_approveEthSignMessage"),Lz=a.tuple([Me,a.object({signer:ve,message:Ue,originalMethod:a.enum(["eth_sign","personal_sign","eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4"]),chainId:a.string(),autoConfirmStatusCode:a.nativeEnum(er)})]),Nz=a.discriminatedUnion("approvalType",[a.object({approvalType:a.literal("user")}),a.object({approvalType:a.literal("hardware"),signature:$e})]),Cz=M,$H=L(Mz,Lz),JH=N(Nz,Cz)});var qz,Uz,Dz,Fz,rW,nW,nS=O(()=>{"use strict";y();V();Q();J();qz=a.literal("user_approveSolConnect"),Uz=a.tuple([Me]),Dz=a.null(),Fz=M,rW=L(qz,Uz),nW=N(Dz,Fz)});var Hz,Wz,Kz,Vz,uW,pW,oS=O(()=>{"use strict";y();V();_n();Q();J();Hz=a.literal("user_approveSolSignAllTransactions"),Wz=a.tuple([Me,a.object({transactions:a.array(fe),autoConfirmStatusCode:a.nativeEnum(er)})]),Kz=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend"),overwriteTransactions:a.array(fe).optional()}),a.object({type:a.literal("send"),result:a.array(a.object({signedTransaction:fe,signature:fe,version:a.union([a.literal("legacy"),a.number()])}))})]),Vz=M,uW=L(Hz,Wz),pW=N(Kz,Vz)});var Zz,Yz,$z,Jz,mW,gW,sS=O(()=>{"use strict";y();V();_n();Q();J();Zz=a.literal("user_approveSolSignAndSendTransaction"),Yz=a.tuple([Me,a.object({transaction:a.string(),autoConfirmStatusCode:a.nativeEnum(er)})]),$z=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend"),overwriteTransactions:a.array(fe).optional()}),a.object({type:a.literal("send"),signedTransaction:fe,signature:fe,version:a.union([a.literal("legacy"),a.number()])})]),Jz=M,mW=L(Zz,Yz),gW=N($z,Jz)});var Xz,e3,t3,r3,EW,_W,iS=O(()=>{"use strict";y();V();_n();Q();J();Xz=a.literal("user_approveSolSignAndSendAllTransactions"),e3=a.tuple([Me,a.object({transactions:a.array(fe),autoConfirmStatusCode:a.nativeEnum(er)})]),t3=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend"),overwriteTransactions:a.array(fe).optional()}),a.object({type:a.literal("send"),result:a.array(a.object({signedTransaction:fe,signature:fe,version:a.union([a.literal("legacy"),a.number()])}))})]),r3=M,EW=L(Xz,e3),_W=N(t3,r3)});var o3,s3,i3,a3,BW,PW,aS=O(()=>{"use strict";y();V();Q();J();o3=a.literal("user_approveSolSignIn"),s3=a.tuple([Me,a.object({connect:a.boolean(),signInData:Ns,message:fe,errorDetails:a.array(a.object({label:a.string(),message:a.string()})).optional()})]),i3=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend")}),a.object({type:a.literal("send"),signature:fe})]),a3=M,BW=L(o3,s3),PW=N(i3,a3)});var u3,p3,f3,l3,CW,OW,cS=O(()=>{"use strict";y();V();_n();Q();J();u3=a.literal("user_approveSolSignMessage"),p3=a.tuple([Me,a.object({message:fe,display:a.union([a.literal("utf8"),a.literal("hex")]),autoConfirmStatusCode:a.nativeEnum(er)})]),f3=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend")}),a.object({type:a.literal("send"),signature:fe})]),l3=M,CW=L(u3,p3),OW=N(f3,l3)});var d3,y3,m3,g3,HW,WW,uS=O(()=>{"use strict";y();V();_n();Q();J();d3=a.literal("user_approveSolSignTransaction"),y3=a.tuple([Me,a.object({transaction:a.string(),autoConfirmStatusCode:a.nativeEnum(er)})]),m3=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend"),overwriteTransactions:a.array(fe).optional()}),a.object({type:a.literal("send"),signedTransaction:fe,signature:fe,version:a.union([a.literal("legacy"),a.number()])})]),g3=M,HW=L(d3,y3),WW=N(m3,g3)});var w3,b3,v3,S3,YW,$W,pS=O(()=>{"use strict";y();V();Q();J();w3=a.literal("user_confirmEIP712IncorrectChainId"),b3=a.tuple([Me,a.object({connectedChainId:a.string(),messageChainId:a.string()})]),v3=a.null(),S3=M,YW=L(w3,b3),$W=N(v3,S3)});var _3,k3,A3,I3,tK,rK,fS=O(()=>{"use strict";y();V();Q();J();_3=a.literal("user_confirmIncorrectMode"),k3=a.tuple([Me,a.enum(["mainnet","testnet"])]),A3=a.null(),I3=M,tK=L(_3,k3),rK=N(A3,I3)});var B3,P3,T3,z3,aK,cK,lS=O(()=>{"use strict";y();V();Q();J();B3=a.literal("user_confirmUnsupportedAccount"),P3=a.tuple([Me,a.literal("ethereum").or(a.literal("solana"))]),T3=a.null(),z3=M,aK=L(B3,P3),cK=N(T3,z3)});var L3,N3,C3,O3,hK,dK,hS=O(()=>{"use strict";y();V();Q();J();L3=a.literal("user_confirmUnsupportedNetwork"),N3=a.tuple([Me,a.string()]),C3=a.null(),O3=M,hK=L(L3,N3),dK=N(C3,O3)});var U3,D3,F3,j3,wK,bK,dS=O(()=>{"use strict";y();V();Q();J();U3=a.literal("user_selectEthWallet"),D3=a.tuple([Me]),F3=Mi,j3=M,wK=L(U3,D3),bK=N(F3,j3)});var W3,K3,V3,G3,kK,AK,yS=O(()=>{"use strict";y();V();Q();J();W3=a.literal("user_approveSolPayTransaction"),K3=a.tuple([Me,a.object({label:a.string().optional(),transaction:a.string()})]),V3=a.discriminatedUnion("type",[a.object({type:a.literal("signAndSend")}),a.object({type:a.literal("send"),signedTransaction:fe,signature:fe,version:a.union([a.literal("legacy"),a.number()])})]),G3=M,kK=L(W3,K3),AK=N(V3,G3)});var Y3,$3,J3,Q3,TK,zK,mS=O(()=>{"use strict";y();V();Q();J();Y3=a.literal("user_approveSolFeaturedAction"),$3=a.tuple([Me,a.object({featuredTransaction:of})]),J3=a.object({transaction:a.string().optional(),message:a.string().optional()}),Q3=M,TK=L(Y3,$3),zK=N(J3,Q3)});var e8,t8,r8,n8,OK,qK,gS=O(()=>{"use strict";y();V();Q();J();e8=a.literal("user_solTransactionConfirmation"),t8=a.tuple([Me,a.object({signature:fe,postAction:Os.optional()})]),r8=a.null(),n8=M,OK=L(e8,t8),qK=N(r8,n8)});var xS=O(()=>{"use strict";y();$v();Jv();Qv();Xv();eS();tS();rS();nS();oS();sS();iS();aS();cS();uS();pS();fS();lS();hS();dS();yS();mS();gS()});var Fs=O(()=>{"use strict";y();Qm();w1();q1();Q1();Lv();Ov();Hv();Yv();xS();Q();ss();_n();J()});var Ks=_e(j=>{"use strict";y();Object.defineProperty(j,"__esModule",{value:!0});j.s16=j.s8=j.nu64be=j.u48be=j.u40be=j.u32be=j.u24be=j.u16be=j.nu64=j.u48=j.u40=j.u32=j.u24=j.u16=j.u8=j.offset=j.greedy=j.Constant=j.UTF8=j.CString=j.Blob=j.Boolean=j.BitField=j.BitStructure=j.VariantLayout=j.Union=j.UnionLayoutDiscriminator=j.UnionDiscriminator=j.Structure=j.Sequence=j.DoubleBE=j.Double=j.FloatBE=j.Float=j.NearInt64BE=j.NearInt64=j.NearUInt64BE=j.NearUInt64=j.IntBE=j.Int=j.UIntBE=j.UInt=j.OffsetLayout=j.GreedyCount=j.ExternalLayout=j.bindConstructorLayout=j.nameWithProperty=j.Layout=j.uint8ArrayToBuffer=j.checkUint8Array=void 0;j.constant=j.utf8=j.cstr=j.blob=j.unionLayoutDiscriminator=j.union=j.seq=j.bits=j.struct=j.f64be=j.f64=j.f32be=j.f32=j.ns64be=j.s48be=j.s40be=j.s32be=j.s24be=j.s16be=j.ns64=j.s48=j.s40=j.s32=j.s24=void 0;var Hl=vs();function Ws(r){if(!(r instanceof Uint8Array))throw new TypeError("b must be a Uint8Array")}j.checkUint8Array=Ws;function Ge(r){return Ws(r),Hl.Buffer.from(r.buffer,r.byteOffset,r.length)}j.uint8ArrayToBuffer=Ge;var Ye=class{constructor(e,t){if(!Number.isInteger(e))throw new TypeError("span must be an integer");this.span=e,this.property=t}makeDestinationObject(){return{}}getSpan(e,t){if(0>this.span)throw new RangeError("indeterminate span");return this.span}replicate(e){let t=Object.create(this.constructor.prototype);return Object.assign(t,this),t.property=e,t}fromArray(e){}};j.Layout=Ye;function Wl(r,e){return e.property?r+"["+e.property+"]":r}j.nameWithProperty=Wl;function i8(r,e){if(typeof r!="function")throw new TypeError("Class must be constructor");if(Object.prototype.hasOwnProperty.call(r,"layout_"))throw new Error("Class is already bound to a layout");if(!(e&&e instanceof Ye))throw new TypeError("layout must be a Layout");if(Object.prototype.hasOwnProperty.call(e,"boundConstructor_"))throw new Error("layout is already bound to a constructor");r.layout_=e,e.boundConstructor_=r,e.makeDestinationObject=()=>new r,Object.defineProperty(r.prototype,"encode",{value(t,n){return e.encode(this,t,n)},writable:!0}),Object.defineProperty(r,"decode",{value(t,n){return e.decode(t,n)},writable:!0})}j.bindConstructorLayout=i8;var tr=class extends Ye{isCount(){throw new Error("ExternalLayout is abstract")}};j.ExternalLayout=tr;var yc=class extends tr{constructor(e=1,t){if(!Number.isInteger(e)||0>=e)throw new TypeError("elementSpan must be a (positive) integer");super(-1,t),this.elementSpan=e}isCount(){return!0}decode(e,t=0){Ws(e);let n=e.length-t;return Math.floor(n/this.elementSpan)}encode(e,t,n){return 0}};j.GreedyCount=yc;var Di=class extends tr{constructor(e,t=0,n){if(!(e instanceof Ye))throw new TypeError("layout must be a Layout");if(!Number.isInteger(t))throw new TypeError("offset must be integer or undefined");super(e.span,n||e.property),this.layout=e,this.offset=t}isCount(){return this.layout instanceof gr||this.layout instanceof Er}decode(e,t=0){return this.layout.decode(e,t+this.offset)}encode(e,t,n=0){return this.layout.encode(e,t,n+this.offset)}};j.OffsetLayout=Di;var gr=class extends Ye{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ge(e).readUIntLE(t,this.span)}encode(e,t,n=0){return Ge(t).writeUIntLE(e,n,this.span),this.span}};j.UInt=gr;var Er=class extends Ye{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ge(e).readUIntBE(t,this.span)}encode(e,t,n=0){return Ge(t).writeUIntBE(e,n,this.span),this.span}};j.UIntBE=Er;var kn=class extends Ye{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ge(e).readIntLE(t,this.span)}encode(e,t,n=0){return Ge(t).writeIntLE(e,n,this.span),this.span}};j.Int=kn;var $n=class extends Ye{constructor(e,t){if(super(e,t),6<this.span)throw new RangeError("span must not exceed 6 bytes")}decode(e,t=0){return Ge(e).readIntBE(t,this.span)}encode(e,t,n=0){return Ge(t).writeIntBE(e,n,this.span),this.span}};j.IntBE=$n;var jl=Math.pow(2,32);function zc(r){let e=Math.floor(r/jl),t=r-e*jl;return{hi32:e,lo32:t}}function Mc(r,e){return r*jl+e}var mc=class extends Ye{constructor(e){super(8,e)}decode(e,t=0){let n=Ge(e),o=n.readUInt32LE(t),s=n.readUInt32LE(t+4);return Mc(s,o)}encode(e,t,n=0){let o=zc(e),s=Ge(t);return s.writeUInt32LE(o.lo32,n),s.writeUInt32LE(o.hi32,n+4),8}};j.NearUInt64=mc;var gc=class extends Ye{constructor(e){super(8,e)}decode(e,t=0){let n=Ge(e),o=n.readUInt32BE(t),s=n.readUInt32BE(t+4);return Mc(o,s)}encode(e,t,n=0){let o=zc(e),s=Ge(t);return s.writeUInt32BE(o.hi32,n),s.writeUInt32BE(o.lo32,n+4),8}};j.NearUInt64BE=gc;var xc=class extends Ye{constructor(e){super(8,e)}decode(e,t=0){let n=Ge(e),o=n.readUInt32LE(t),s=n.readInt32LE(t+4);return Mc(s,o)}encode(e,t,n=0){let o=zc(e),s=Ge(t);return s.writeUInt32LE(o.lo32,n),s.writeInt32LE(o.hi32,n+4),8}};j.NearInt64=xc;var wc=class extends Ye{constructor(e){super(8,e)}decode(e,t=0){let n=Ge(e),o=n.readInt32BE(t),s=n.readUInt32BE(t+4);return Mc(o,s)}encode(e,t,n=0){let o=zc(e),s=Ge(t);return s.writeInt32BE(o.hi32,n),s.writeUInt32BE(o.lo32,n+4),8}};j.NearInt64BE=wc;var bc=class extends Ye{constructor(e){super(4,e)}decode(e,t=0){return Ge(e).readFloatLE(t)}encode(e,t,n=0){return Ge(t).writeFloatLE(e,n),4}};j.Float=bc;var vc=class extends Ye{constructor(e){super(4,e)}decode(e,t=0){return Ge(e).readFloatBE(t)}encode(e,t,n=0){return Ge(t).writeFloatBE(e,n),4}};j.FloatBE=vc;var Sc=class extends Ye{constructor(e){super(8,e)}decode(e,t=0){return Ge(e).readDoubleLE(t)}encode(e,t,n=0){return Ge(t).writeDoubleLE(e,n),8}};j.Double=Sc;var Ec=class extends Ye{constructor(e){super(8,e)}decode(e,t=0){return Ge(e).readDoubleBE(t)}encode(e,t,n=0){return Ge(t).writeDoubleBE(e,n),8}};j.DoubleBE=Ec;var _c=class extends Ye{constructor(e,t,n){if(!(e instanceof Ye))throw new TypeError("elementLayout must be a Layout");if(!(t instanceof tr&&t.isCount()||Number.isInteger(t)&&0<=t))throw new TypeError("count must be non-negative integer or an unsigned integer ExternalLayout");let o=-1;!(t instanceof tr)&&0<e.span&&(o=t*e.span),super(o,n),this.elementLayout=e,this.count=t}getSpan(e,t=0){if(0<=this.span)return this.span;let n=0,o=this.count;if(o instanceof tr&&(o=o.decode(e,t)),0<this.elementLayout.span)n=o*this.elementLayout.span;else{let s=0;for(;s<o;)n+=this.elementLayout.getSpan(e,t+n),++s}return n}decode(e,t=0){let n=[],o=0,s=this.count;for(s instanceof tr&&(s=s.decode(e,t));o<s;)n.push(this.elementLayout.decode(e,t)),t+=this.elementLayout.getSpan(e,t),o+=1;return n}encode(e,t,n=0){let o=this.elementLayout,s=e.reduce((i,u)=>i+o.encode(u,t,n+i),0);return this.count instanceof tr&&this.count.encode(e.length,t,n),s}};j.Sequence=_c;var kc=class extends Ye{constructor(e,t,n){if(!(Array.isArray(e)&&e.reduce((s,i)=>s&&i instanceof Ye,!0)))throw new TypeError("fields must be array of Layout instances");typeof t=="boolean"&&n===void 0&&(n=t,t=void 0);for(let s of e)if(0>s.span&&s.property===void 0)throw new Error("fields cannot contain unnamed variable-length layout");let o=-1;try{o=e.reduce((s,i)=>s+i.getSpan(),0)}catch{}super(o,t),this.fields=e,this.decodePrefixes=!!n}getSpan(e,t=0){if(0<=this.span)return this.span;let n=0;try{n=this.fields.reduce((o,s)=>{let i=s.getSpan(e,t);return t+=i,o+i},0)}catch{throw new RangeError("indeterminate span")}return n}decode(e,t=0){Ws(e);let n=this.makeDestinationObject();for(let o of this.fields)if(o.property!==void 0&&(n[o.property]=o.decode(e,t)),t+=o.getSpan(e,t),this.decodePrefixes&&e.length===t)break;return n}encode(e,t,n=0){let o=n,s=0,i=0;for(let u of this.fields){let f=u.span;if(i=0<f?f:0,u.property!==void 0){let g=e[u.property];g!==void 0&&(i=u.encode(g,t,n),0>f&&(f=u.getSpan(t,n)))}s=n,n+=f}return s+i-o}fromArray(e){let t=this.makeDestinationObject();for(let n of this.fields)n.property!==void 0&&0<e.length&&(t[n.property]=e.shift());return t}layoutFor(e){if(typeof e!="string")throw new TypeError("property must be string");for(let t of this.fields)if(t.property===e)return t}offsetOf(e){if(typeof e!="string")throw new TypeError("property must be string");let t=0;for(let n of this.fields){if(n.property===e)return t;0>n.span?t=-1:0<=t&&(t+=n.span)}}};j.Structure=kc;var Fi=class{constructor(e){this.property=e}decode(e,t){throw new Error("UnionDiscriminator is abstract")}encode(e,t,n){throw new Error("UnionDiscriminator is abstract")}};j.UnionDiscriminator=Fi;var Hs=class extends Fi{constructor(e,t){if(!(e instanceof tr&&e.isCount()))throw new TypeError("layout must be an unsigned integer ExternalLayout");super(t||e.property||"variant"),this.layout=e}decode(e,t){return this.layout.decode(e,t)}encode(e,t,n){return this.layout.encode(e,t,n)}};j.UnionLayoutDiscriminator=Hs;var ji=class extends Ye{constructor(e,t,n){let o;if(e instanceof gr||e instanceof Er)o=new Hs(new Di(e));else if(e instanceof tr&&e.isCount())o=new Hs(e);else if(e instanceof Fi)o=e;else throw new TypeError("discr must be a UnionDiscriminator or an unsigned integer layout");if(t===void 0&&(t=null),!(t===null||t instanceof Ye))throw new TypeError("defaultLayout must be null or a Layout");if(t!==null){if(0>t.span)throw new Error("defaultLayout must have constant span");t.property===void 0&&(t=t.replicate("content"))}let s=-1;t&&(s=t.span,0<=s&&(e instanceof gr||e instanceof Er)&&(s+=o.layout.span)),super(s,n),this.discriminator=o,this.usesPrefixDiscriminator=e instanceof gr||e instanceof Er,this.defaultLayout=t,this.registry={};let i=this.defaultGetSourceVariant.bind(this);this.getSourceVariant=function(u){return i(u)},this.configGetSourceVariant=function(u){i=u.bind(this)}}getSpan(e,t=0){if(0<=this.span)return this.span;let n=this.getVariant(e,t);if(!n)throw new Error("unable to determine span for unrecognized variant");return n.getSpan(e,t)}defaultGetSourceVariant(e){if(Object.prototype.hasOwnProperty.call(e,this.discriminator.property)){if(this.defaultLayout&&this.defaultLayout.property&&Object.prototype.hasOwnProperty.call(e,this.defaultLayout.property))return;let t=this.registry[e[this.discriminator.property]];if(t&&(!t.layout||t.property&&Object.prototype.hasOwnProperty.call(e,t.property)))return t}else for(let t in this.registry){let n=this.registry[t];if(n.property&&Object.prototype.hasOwnProperty.call(e,n.property))return n}throw new Error("unable to infer src variant")}decode(e,t=0){let n,o=this.discriminator,s=o.decode(e,t),i=this.registry[s];if(i===void 0){let u=this.defaultLayout,f=0;this.usesPrefixDiscriminator&&(f=o.layout.span),n=this.makeDestinationObject(),n[o.property]=s,n[u.property]=u.decode(e,t+f)}else n=i.decode(e,t);return n}encode(e,t,n=0){let o=this.getSourceVariant(e);if(o===void 0){let s=this.discriminator,i=this.defaultLayout,u=0;return this.usesPrefixDiscriminator&&(u=s.layout.span),s.encode(e[s.property],t,n),u+i.encode(e[i.property],t,n+u)}return o.encode(e,t,n)}addVariant(e,t,n){let o=new Ac(this,e,t,n);return this.registry[e]=o,o}getVariant(e,t=0){let n;return e instanceof Uint8Array?n=this.discriminator.decode(e,t):n=e,this.registry[n]}};j.Union=ji;var Ac=class extends Ye{constructor(e,t,n,o){if(!(e instanceof ji))throw new TypeError("union must be a Union");if(!Number.isInteger(t)||0>t)throw new TypeError("variant must be a (non-negative) integer");if(typeof n=="string"&&o===void 0&&(o=n,n=null),n){if(!(n instanceof Ye))throw new TypeError("layout must be a Layout");if(e.defaultLayout!==null&&0<=n.span&&n.span>e.defaultLayout.span)throw new Error("variant span exceeds span of containing union");if(typeof o!="string")throw new TypeError("variant must have a String property")}let s=e.span;0>e.span&&(s=n?n.span:0,0<=s&&e.usesPrefixDiscriminator&&(s+=e.discriminator.layout.span)),super(s,o),this.union=e,this.variant=t,this.layout=n||null}getSpan(e,t=0){if(0<=this.span)return this.span;let n=0;this.union.usesPrefixDiscriminator&&(n=this.union.discriminator.layout.span);let o=0;return this.layout&&(o=this.layout.getSpan(e,t+n)),n+o}decode(e,t=0){let n=this.makeDestinationObject();if(this!==this.union.getVariant(e,t))throw new Error("variant mismatch");let o=0;return this.union.usesPrefixDiscriminator&&(o=this.union.discriminator.layout.span),this.layout?n[this.property]=this.layout.decode(e,t+o):this.property?n[this.property]=!0:this.union.usesPrefixDiscriminator&&(n[this.union.discriminator.property]=this.variant),n}encode(e,t,n=0){let o=0;if(this.union.usesPrefixDiscriminator&&(o=this.union.discriminator.layout.span),this.layout&&!Object.prototype.hasOwnProperty.call(e,this.property))throw new TypeError("variant lacks property "+this.property);this.union.discriminator.encode(this.variant,t,n);let s=o;if(this.layout&&(this.layout.encode(e[this.property],t,n+o),s+=this.layout.getSpan(t,n+o),0<=this.union.span&&s>this.union.span))throw new Error("encoded variant overruns containing union");return s}fromArray(e){if(this.layout)return this.layout.fromArray(e)}};j.VariantLayout=Ac;function js(r){return 0>r&&(r+=4294967296),r}var Hi=class extends Ye{constructor(e,t,n){if(!(e instanceof gr||e instanceof Er))throw new TypeError("word must be a UInt or UIntBE layout");if(typeof t=="string"&&n===void 0&&(n=t,t=!1),4<e.span)throw new RangeError("word cannot exceed 32 bits");super(e.span,n),this.word=e,this.msb=!!t,this.fields=[];let o=0;this._packedSetValue=function(s){return o=js(s),this},this._packedGetValue=function(){return o}}decode(e,t=0){let n=this.makeDestinationObject(),o=this.word.decode(e,t);this._packedSetValue(o);for(let s of this.fields)s.property!==void 0&&(n[s.property]=s.decode(e));return n}encode(e,t,n=0){let o=this.word.decode(t,n);this._packedSetValue(o);for(let s of this.fields)if(s.property!==void 0){let i=e[s.property];i!==void 0&&s.encode(i)}return this.word.encode(this._packedGetValue(),t,n)}addField(e,t){let n=new Wi(this,e,t);return this.fields.push(n),n}addBoolean(e){let t=new Ic(this,e);return this.fields.push(t),t}fieldFor(e){if(typeof e!="string")throw new TypeError("property must be string");for(let t of this.fields)if(t.property===e)return t}};j.BitStructure=Hi;var Wi=class{constructor(e,t,n){if(!(e instanceof Hi))throw new TypeError("container must be a BitStructure");if(!Number.isInteger(t)||0>=t)throw new TypeError("bits must be positive integer");let o=8*e.span,s=e.fields.reduce((i,u)=>i+u.bits,0);if(t+s>o)throw new Error("bits too long for span remainder ("+(o-s)+" of "+o+" remain)");this.container=e,this.bits=t,this.valueMask=(1<<t)-1,t===32&&(this.valueMask=4294967295),this.start=s,this.container.msb&&(this.start=o-s-t),this.wordMask=js(this.valueMask<<this.start),this.property=n}decode(e,t){let n=this.container._packedGetValue();return js(n&this.wordMask)>>>this.start}encode(e){if(typeof e!="number"||!Number.isInteger(e)||e!==js(e&this.valueMask))throw new TypeError(Wl("BitField.encode",this)+" value must be integer not exceeding "+this.valueMask);let t=this.container._packedGetValue(),n=js(e<<this.start);this.container._packedSetValue(js(t&~this.wordMask)|n)}};j.BitField=Wi;var Ic=class extends Wi{constructor(e,t){super(e,1,t)}decode(e,t){return!!super.decode(e,t)}encode(e){typeof e=="boolean"&&(e=+e),super.encode(e)}};j.Boolean=Ic;var Rc=class extends Ye{constructor(e,t){if(!(e instanceof tr&&e.isCount()||Number.isInteger(e)&&0<=e))throw new TypeError("length must be positive integer or an unsigned integer ExternalLayout");let n=-1;e instanceof tr||(n=e),super(n,t),this.length=e}getSpan(e,t){let n=this.span;return 0>n&&(n=this.length.decode(e,t)),n}decode(e,t=0){let n=this.span;return 0>n&&(n=this.length.decode(e,t)),Ge(e).slice(t,t+n)}encode(e,t,n){let o=this.length;if(this.length instanceof tr&&(o=e.length),!(e instanceof Uint8Array&&o===e.length))throw new TypeError(Wl("Blob.encode",this)+" requires (length "+o+") Uint8Array as src");if(n+o>t.length)throw new RangeError("encoding overruns Uint8Array");let s=Ge(e);return Ge(t).write(s.toString("hex"),n,o,"hex"),this.length instanceof tr&&this.length.encode(o,t,n),o}};j.Blob=Rc;var Bc=class extends Ye{constructor(e){super(-1,e)}getSpan(e,t=0){Ws(e);let n=t;for(;n<e.length&&e[n]!==0;)n+=1;return 1+n-t}decode(e,t=0){let n=this.getSpan(e,t);return Ge(e).slice(t,t+n-1).toString("utf-8")}encode(e,t,n=0){typeof e!="string"&&(e=String(e));let o=Hl.Buffer.from(e,"utf8"),s=o.length;if(n+s>t.length)throw new RangeError("encoding overruns Buffer");let i=Ge(t);return o.copy(i,n),i[n+s]=0,s+1}};j.CString=Bc;var Pc=class extends Ye{constructor(e,t){if(typeof e=="string"&&t===void 0&&(t=e,e=void 0),e===void 0)e=-1;else if(!Number.isInteger(e))throw new TypeError("maxSpan must be an integer");super(-1,t),this.maxSpan=e}getSpan(e,t=0){return Ws(e),e.length-t}decode(e,t=0){let n=this.getSpan(e,t);if(0<=this.maxSpan&&this.maxSpan<n)throw new RangeError("text length exceeds maxSpan");return Ge(e).slice(t,t+n).toString("utf-8")}encode(e,t,n=0){typeof e!="string"&&(e=String(e));let o=Hl.Buffer.from(e,"utf8"),s=o.length;if(0<=this.maxSpan&&this.maxSpan<s)throw new RangeError("text length exceeds maxSpan");if(n+s>t.length)throw new RangeError("encoding overruns Buffer");return o.copy(Ge(t),n),s}};j.UTF8=Pc;var Tc=class extends Ye{constructor(e,t){super(0,t),this.value=e}decode(e,t){return this.value}encode(e,t,n){return 0}};j.Constant=Tc;j.greedy=(r,e)=>new yc(r,e);j.offset=(r,e,t)=>new Di(r,e,t);j.u8=r=>new gr(1,r);j.u16=r=>new gr(2,r);j.u24=r=>new gr(3,r);j.u32=r=>new gr(4,r);j.u40=r=>new gr(5,r);j.u48=r=>new gr(6,r);j.nu64=r=>new mc(r);j.u16be=r=>new Er(2,r);j.u24be=r=>new Er(3,r);j.u32be=r=>new Er(4,r);j.u40be=r=>new Er(5,r);j.u48be=r=>new Er(6,r);j.nu64be=r=>new gc(r);j.s8=r=>new kn(1,r);j.s16=r=>new kn(2,r);j.s24=r=>new kn(3,r);j.s32=r=>new kn(4,r);j.s40=r=>new kn(5,r);j.s48=r=>new kn(6,r);j.ns64=r=>new xc(r);j.s16be=r=>new $n(2,r);j.s24be=r=>new $n(3,r);j.s32be=r=>new $n(4,r);j.s40be=r=>new $n(5,r);j.s48be=r=>new $n(6,r);j.ns64be=r=>new wc(r);j.f32=r=>new bc(r);j.f32be=r=>new vc(r);j.f64=r=>new Sc(r);j.f64be=r=>new Ec(r);j.struct=(r,e,t)=>new kc(r,e,t);j.bits=(r,e,t)=>new Hi(r,e,t);j.seq=(r,e,t)=>new _c(r,e,t);j.union=(r,e,t)=>new ji(r,e,t);j.unionLayoutDiscriminator=(r,e)=>new Hs(r,e);j.blob=(r,e)=>new Rc(r,e);j.cstr=r=>new Bc(r);j.utf8=(r,e)=>new Pc(r,e);j.constant=(r,e)=>new Tc(r,e)});var Vi=_e(Ys=>{"use strict";y();var Kl=gp(),Gs=wp(),wS=typeof Symbol=="function"&&typeof Symbol.for=="function"?Symbol.for("nodejs.util.inspect.custom"):null;Ys.Buffer=Y;Ys.SlowBuffer=l8;Ys.INSPECT_MAX_BYTES=50;var Lc=2147483647;Ys.kMaxLength=Lc;Y.TYPED_ARRAY_SUPPORT=a8();!Y.TYPED_ARRAY_SUPPORT&&typeof console<"u"&&typeof console.error=="function"&&console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function a8(){try{let r=new Uint8Array(1),e={foo:function(){return 42}};return Object.setPrototypeOf(e,Uint8Array.prototype),Object.setPrototypeOf(r,e),r.foo()===42}catch{return!1}}Object.defineProperty(Y.prototype,"parent",{enumerable:!0,get:function(){if(Y.isBuffer(this))return this.buffer}});Object.defineProperty(Y.prototype,"offset",{enumerable:!0,get:function(){if(Y.isBuffer(this))return this.byteOffset}});function An(r){if(r>Lc)throw new RangeError('The value "'+r+'" is invalid for option "size"');let e=new Uint8Array(r);return Object.setPrototypeOf(e,Y.prototype),e}function Y(r,e,t){if(typeof r=="number"){if(typeof e=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return Yl(r)}return ES(r,e,t)}Y.poolSize=8192;function ES(r,e,t){if(typeof r=="string")return u8(r,e);if(ArrayBuffer.isView(r))return p8(r);if(r==null)throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r);if(sn(r,ArrayBuffer)||r&&sn(r.buffer,ArrayBuffer)||typeof SharedArrayBuffer<"u"&&(sn(r,SharedArrayBuffer)||r&&sn(r.buffer,SharedArrayBuffer)))return Gl(r,e,t);if(typeof r=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');let n=r.valueOf&&r.valueOf();if(n!=null&&n!==r)return Y.from(n,e,t);let o=f8(r);if(o)return o;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof r[Symbol.toPrimitive]=="function")return Y.from(r[Symbol.toPrimitive]("string"),e,t);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof r)}Y.from=function(r,e,t){return ES(r,e,t)};Object.setPrototypeOf(Y.prototype,Uint8Array.prototype);Object.setPrototypeOf(Y,Uint8Array);function _S(r){if(typeof r!="number")throw new TypeError('"size" argument must be of type number');if(r<0)throw new RangeError('The value "'+r+'" is invalid for option "size"')}function c8(r,e,t){return _S(r),r<=0?An(r):e!==void 0?typeof t=="string"?An(r).fill(e,t):An(r).fill(e):An(r)}Y.alloc=function(r,e,t){return c8(r,e,t)};function Yl(r){return _S(r),An(r<0?0:$l(r)|0)}Y.allocUnsafe=function(r){return Yl(r)};Y.allocUnsafeSlow=function(r){return Yl(r)};function u8(r,e){if((typeof e!="string"||e==="")&&(e="utf8"),!Y.isEncoding(e))throw new TypeError("Unknown encoding: "+e);let t=kS(r,e)|0,n=An(t),o=n.write(r,e);return o!==t&&(n=n.slice(0,o)),n}function Vl(r){let e=r.length<0?0:$l(r.length)|0,t=An(e);for(let n=0;n<e;n+=1)t[n]=r[n]&255;return t}function p8(r){if(sn(r,Uint8Array)){let e=new Uint8Array(r);return Gl(e.buffer,e.byteOffset,e.byteLength)}return Vl(r)}function Gl(r,e,t){if(e<0||r.byteLength<e)throw new RangeError('"offset" is outside of buffer bounds');if(r.byteLength<e+(t||0))throw new RangeError('"length" is outside of buffer bounds');let n;return e===void 0&&t===void 0?n=new Uint8Array(r):t===void 0?n=new Uint8Array(r,e):n=new Uint8Array(r,e,t),Object.setPrototypeOf(n,Y.prototype),n}function f8(r){if(Y.isBuffer(r)){let e=$l(r.length)|0,t=An(e);return t.length===0||r.copy(t,0,0,e),t}if(r.length!==void 0)return typeof r.length!="number"||Ql(r.length)?An(0):Vl(r);if(r.type==="Buffer"&&Array.isArray(r.data))return Vl(r.data)}function $l(r){if(r>=Lc)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+Lc.toString(16)+" bytes");return r|0}function l8(r){return+r!=r&&(r=0),Y.alloc(+r)}Y.isBuffer=function(e){return e!=null&&e._isBuffer===!0&&e!==Y.prototype};Y.compare=function(e,t){if(sn(e,Uint8Array)&&(e=Y.from(e,e.offset,e.byteLength)),sn(t,Uint8Array)&&(t=Y.from(t,t.offset,t.byteLength)),!Y.isBuffer(e)||!Y.isBuffer(t))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;let n=e.length,o=t.length;for(let s=0,i=Math.min(n,o);s<i;++s)if(e[s]!==t[s]){n=e[s],o=t[s];break}return n<o?-1:o<n?1:0};Y.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};Y.concat=function(e,t){if(!Array.isArray(e))throw new TypeError('"list" argument must be an Array of Buffers');if(e.length===0)return Y.alloc(0);let n;if(t===void 0)for(t=0,n=0;n<e.length;++n)t+=e[n].length;let o=Y.allocUnsafe(t),s=0;for(n=0;n<e.length;++n){let i=e[n];if(sn(i,Uint8Array))s+i.length>o.length?(Y.isBuffer(i)||(i=Y.from(i)),i.copy(o,s)):Uint8Array.prototype.set.call(o,i,s);else if(Y.isBuffer(i))i.copy(o,s);else throw new TypeError('"list" argument must be an Array of Buffers');s+=i.length}return o};function kS(r,e){if(Y.isBuffer(r))return r.length;if(ArrayBuffer.isView(r)||sn(r,ArrayBuffer))return r.byteLength;if(typeof r!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof r);let t=r.length,n=arguments.length>2&&arguments[2]===!0;if(!n&&t===0)return 0;let o=!1;for(;;)switch(e){case"ascii":case"latin1":case"binary":return t;case"utf8":case"utf-8":return Zl(r).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return t*2;case"hex":return t>>>1;case"base64":return LS(r).length;default:if(o)return n?-1:Zl(r).length;e=(""+e).toLowerCase(),o=!0}}Y.byteLength=kS;function h8(r,e,t){let n=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((t===void 0||t>this.length)&&(t=this.length),t<=0)||(t>>>=0,e>>>=0,t<=e))return"";for(r||(r="utf8");;)switch(r){case"hex":return E8(this,e,t);case"utf8":case"utf-8":return IS(this,e,t);case"ascii":return v8(this,e,t);case"latin1":case"binary":return S8(this,e,t);case"base64":return w8(this,e,t);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return _8(this,e,t);default:if(n)throw new TypeError("Unknown encoding: "+r);r=(r+"").toLowerCase(),n=!0}}Y.prototype._isBuffer=!0;function is(r,e,t){let n=r[e];r[e]=r[t],r[t]=n}Y.prototype.swap16=function(){let e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(let t=0;t<e;t+=2)is(this,t,t+1);return this};Y.prototype.swap32=function(){let e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(let t=0;t<e;t+=4)is(this,t,t+3),is(this,t+1,t+2);return this};Y.prototype.swap64=function(){let e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(let t=0;t<e;t+=8)is(this,t,t+7),is(this,t+1,t+6),is(this,t+2,t+5),is(this,t+3,t+4);return this};Y.prototype.toString=function(){let e=this.length;return e===0?"":arguments.length===0?IS(this,0,e):h8.apply(this,arguments)};Y.prototype.toLocaleString=Y.prototype.toString;Y.prototype.equals=function(e){if(!Y.isBuffer(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:Y.compare(this,e)===0};Y.prototype.inspect=function(){let e="",t=Ys.INSPECT_MAX_BYTES;return e=this.toString("hex",0,t).replace(/(.{2})/g,"$1 ").trim(),this.length>t&&(e+=" ... "),"<Buffer "+e+">"};wS&&(Y.prototype[wS]=Y.prototype.inspect);Y.prototype.compare=function(e,t,n,o,s){if(sn(e,Uint8Array)&&(e=Y.from(e,e.offset,e.byteLength)),!Y.isBuffer(e))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(t===void 0&&(t=0),n===void 0&&(n=e?e.length:0),o===void 0&&(o=0),s===void 0&&(s=this.length),t<0||n>e.length||o<0||s>this.length)throw new RangeError("out of range index");if(o>=s&&t>=n)return 0;if(o>=s)return-1;if(t>=n)return 1;if(t>>>=0,n>>>=0,o>>>=0,s>>>=0,this===e)return 0;let i=s-o,u=n-t,f=Math.min(i,u),g=this.slice(o,s),b=e.slice(t,n);for(let E=0;E<f;++E)if(g[E]!==b[E]){i=g[E],u=b[E];break}return i<u?-1:u<i?1:0};function AS(r,e,t,n,o){if(r.length===0)return-1;if(typeof t=="string"?(n=t,t=0):t>2147483647?t=2147483647:t<-2147483648&&(t=-2147483648),t=+t,Ql(t)&&(t=o?0:r.length-1),t<0&&(t=r.length+t),t>=r.length){if(o)return-1;t=r.length-1}else if(t<0)if(o)t=0;else return-1;if(typeof e=="string"&&(e=Y.from(e,n)),Y.isBuffer(e))return e.length===0?-1:bS(r,e,t,n,o);if(typeof e=="number")return e=e&255,typeof Uint8Array.prototype.indexOf=="function"?o?Uint8Array.prototype.indexOf.call(r,e,t):Uint8Array.prototype.lastIndexOf.call(r,e,t):bS(r,[e],t,n,o);throw new TypeError("val must be string, number or Buffer")}function bS(r,e,t,n,o){let s=1,i=r.length,u=e.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(r.length<2||e.length<2)return-1;s=2,i/=2,u/=2,t/=2}function f(b,E){return s===1?b[E]:b.readUInt16BE(E*s)}let g;if(o){let b=-1;for(g=t;g<i;g++)if(f(r,g)===f(e,b===-1?0:g-b)){if(b===-1&&(b=g),g-b+1===u)return b*s}else b!==-1&&(g-=g-b),b=-1}else for(t+u>i&&(t=i-u),g=t;g>=0;g--){let b=!0;for(let E=0;E<u;E++)if(f(r,g+E)!==f(e,E)){b=!1;break}if(b)return g}return-1}Y.prototype.includes=function(e,t,n){return this.indexOf(e,t,n)!==-1};Y.prototype.indexOf=function(e,t,n){return AS(this,e,t,n,!0)};Y.prototype.lastIndexOf=function(e,t,n){return AS(this,e,t,n,!1)};function d8(r,e,t,n){t=Number(t)||0;let o=r.length-t;n?(n=Number(n),n>o&&(n=o)):n=o;let s=e.length;n>s/2&&(n=s/2);let i;for(i=0;i<n;++i){let u=parseInt(e.substr(i*2,2),16);if(Ql(u))return i;r[t+i]=u}return i}function y8(r,e,t,n){return Nc(Zl(e,r.length-t),r,t,n)}function m8(r,e,t,n){return Nc(R8(e),r,t,n)}function g8(r,e,t,n){return Nc(LS(e),r,t,n)}function x8(r,e,t,n){return Nc(B8(e,r.length-t),r,t,n)}Y.prototype.write=function(e,t,n,o){if(t===void 0)o="utf8",n=this.length,t=0;else if(n===void 0&&typeof t=="string")o=t,n=this.length,t=0;else if(isFinite(t))t=t>>>0,isFinite(n)?(n=n>>>0,o===void 0&&(o="utf8")):(o=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");let s=this.length-t;if((n===void 0||n>s)&&(n=s),e.length>0&&(n<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");o||(o="utf8");let i=!1;for(;;)switch(o){case"hex":return d8(this,e,t,n);case"utf8":case"utf-8":return y8(this,e,t,n);case"ascii":case"latin1":case"binary":return m8(this,e,t,n);case"base64":return g8(this,e,t,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return x8(this,e,t,n);default:if(i)throw new TypeError("Unknown encoding: "+o);o=(""+o).toLowerCase(),i=!0}};Y.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function w8(r,e,t){return e===0&&t===r.length?Kl.fromByteArray(r):Kl.fromByteArray(r.slice(e,t))}function IS(r,e,t){t=Math.min(r.length,t);let n=[],o=e;for(;o<t;){let s=r[o],i=null,u=s>239?4:s>223?3:s>191?2:1;if(o+u<=t){let f,g,b,E;switch(u){case 1:s<128&&(i=s);break;case 2:f=r[o+1],(f&192)===128&&(E=(s&31)<<6|f&63,E>127&&(i=E));break;case 3:f=r[o+1],g=r[o+2],(f&192)===128&&(g&192)===128&&(E=(s&15)<<12|(f&63)<<6|g&63,E>2047&&(E<55296||E>57343)&&(i=E));break;case 4:f=r[o+1],g=r[o+2],b=r[o+3],(f&192)===128&&(g&192)===128&&(b&192)===128&&(E=(s&15)<<18|(f&63)<<12|(g&63)<<6|b&63,E>65535&&E<1114112&&(i=E))}}i===null?(i=65533,u=1):i>65535&&(i-=65536,n.push(i>>>10&1023|55296),i=56320|i&1023),n.push(i),o+=u}return b8(n)}var vS=4096;function b8(r){let e=r.length;if(e<=vS)return String.fromCharCode.apply(String,r);let t="",n=0;for(;n<e;)t+=String.fromCharCode.apply(String,r.slice(n,n+=vS));return t}function v8(r,e,t){let n="";t=Math.min(r.length,t);for(let o=e;o<t;++o)n+=String.fromCharCode(r[o]&127);return n}function S8(r,e,t){let n="";t=Math.min(r.length,t);for(let o=e;o<t;++o)n+=String.fromCharCode(r[o]);return n}function E8(r,e,t){let n=r.length;(!e||e<0)&&(e=0),(!t||t<0||t>n)&&(t=n);let o="";for(let s=e;s<t;++s)o+=P8[r[s]];return o}function _8(r,e,t){let n=r.slice(e,t),o="";for(let s=0;s<n.length-1;s+=2)o+=String.fromCharCode(n[s]+n[s+1]*256);return o}Y.prototype.slice=function(e,t){let n=this.length;e=~~e,t=t===void 0?n:~~t,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),t<0?(t+=n,t<0&&(t=0)):t>n&&(t=n),t<e&&(t=e);let o=this.subarray(e,t);return Object.setPrototypeOf(o,Y.prototype),o};function Zt(r,e,t){if(r%1!==0||r<0)throw new RangeError("offset is not uint");if(r+e>t)throw new RangeError("Trying to access beyond buffer length")}Y.prototype.readUintLE=Y.prototype.readUIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||Zt(e,t,this.length);let o=this[e],s=1,i=0;for(;++i<t&&(s*=256);)o+=this[e+i]*s;return o};Y.prototype.readUintBE=Y.prototype.readUIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||Zt(e,t,this.length);let o=this[e+--t],s=1;for(;t>0&&(s*=256);)o+=this[e+--t]*s;return o};Y.prototype.readUint8=Y.prototype.readUInt8=function(e,t){return e=e>>>0,t||Zt(e,1,this.length),this[e]};Y.prototype.readUint16LE=Y.prototype.readUInt16LE=function(e,t){return e=e>>>0,t||Zt(e,2,this.length),this[e]|this[e+1]<<8};Y.prototype.readUint16BE=Y.prototype.readUInt16BE=function(e,t){return e=e>>>0,t||Zt(e,2,this.length),this[e]<<8|this[e+1]};Y.prototype.readUint32LE=Y.prototype.readUInt32LE=function(e,t){return e=e>>>0,t||Zt(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216};Y.prototype.readUint32BE=Y.prototype.readUInt32BE=function(e,t){return e=e>>>0,t||Zt(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])};Y.prototype.readBigUInt64LE=Jn(function(e){e=e>>>0,Zs(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Ki(e,this.length-8);let o=t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24,s=this[++e]+this[++e]*2**8+this[++e]*2**16+n*2**24;return BigInt(o)+(BigInt(s)<<BigInt(32))});Y.prototype.readBigUInt64BE=Jn(function(e){e=e>>>0,Zs(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Ki(e,this.length-8);let o=t*2**24+this[++e]*2**16+this[++e]*2**8+this[++e],s=this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n;return(BigInt(o)<<BigInt(32))+BigInt(s)});Y.prototype.readIntLE=function(e,t,n){e=e>>>0,t=t>>>0,n||Zt(e,t,this.length);let o=this[e],s=1,i=0;for(;++i<t&&(s*=256);)o+=this[e+i]*s;return s*=128,o>=s&&(o-=Math.pow(2,8*t)),o};Y.prototype.readIntBE=function(e,t,n){e=e>>>0,t=t>>>0,n||Zt(e,t,this.length);let o=t,s=1,i=this[e+--o];for(;o>0&&(s*=256);)i+=this[e+--o]*s;return s*=128,i>=s&&(i-=Math.pow(2,8*t)),i};Y.prototype.readInt8=function(e,t){return e=e>>>0,t||Zt(e,1,this.length),this[e]&128?(255-this[e]+1)*-1:this[e]};Y.prototype.readInt16LE=function(e,t){e=e>>>0,t||Zt(e,2,this.length);let n=this[e]|this[e+1]<<8;return n&32768?n|4294901760:n};Y.prototype.readInt16BE=function(e,t){e=e>>>0,t||Zt(e,2,this.length);let n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n};Y.prototype.readInt32LE=function(e,t){return e=e>>>0,t||Zt(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24};Y.prototype.readInt32BE=function(e,t){return e=e>>>0,t||Zt(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]};Y.prototype.readBigInt64LE=Jn(function(e){e=e>>>0,Zs(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Ki(e,this.length-8);let o=this[e+4]+this[e+5]*2**8+this[e+6]*2**16+(n<<24);return(BigInt(o)<<BigInt(32))+BigInt(t+this[++e]*2**8+this[++e]*2**16+this[++e]*2**24)});Y.prototype.readBigInt64BE=Jn(function(e){e=e>>>0,Zs(e,"offset");let t=this[e],n=this[e+7];(t===void 0||n===void 0)&&Ki(e,this.length-8);let o=(t<<24)+this[++e]*2**16+this[++e]*2**8+this[++e];return(BigInt(o)<<BigInt(32))+BigInt(this[++e]*2**24+this[++e]*2**16+this[++e]*2**8+n)});Y.prototype.readFloatLE=function(e,t){return e=e>>>0,t||Zt(e,4,this.length),Gs.read(this,e,!0,23,4)};Y.prototype.readFloatBE=function(e,t){return e=e>>>0,t||Zt(e,4,this.length),Gs.read(this,e,!1,23,4)};Y.prototype.readDoubleLE=function(e,t){return e=e>>>0,t||Zt(e,8,this.length),Gs.read(this,e,!0,52,8)};Y.prototype.readDoubleBE=function(e,t){return e=e>>>0,t||Zt(e,8,this.length),Gs.read(this,e,!1,52,8)};function xr(r,e,t,n,o,s){if(!Y.isBuffer(r))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>o||e<s)throw new RangeError('"value" argument is out of bounds');if(t+n>r.length)throw new RangeError("Index out of range")}Y.prototype.writeUintLE=Y.prototype.writeUIntLE=function(e,t,n,o){if(e=+e,t=t>>>0,n=n>>>0,!o){let u=Math.pow(2,8*n)-1;xr(this,e,t,n,u,0)}let s=1,i=0;for(this[t]=e&255;++i<n&&(s*=256);)this[t+i]=e/s&255;return t+n};Y.prototype.writeUintBE=Y.prototype.writeUIntBE=function(e,t,n,o){if(e=+e,t=t>>>0,n=n>>>0,!o){let u=Math.pow(2,8*n)-1;xr(this,e,t,n,u,0)}let s=n-1,i=1;for(this[t+s]=e&255;--s>=0&&(i*=256);)this[t+s]=e/i&255;return t+n};Y.prototype.writeUint8=Y.prototype.writeUInt8=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,1,255,0),this[t]=e&255,t+1};Y.prototype.writeUint16LE=Y.prototype.writeUInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,2,65535,0),this[t]=e&255,this[t+1]=e>>>8,t+2};Y.prototype.writeUint16BE=Y.prototype.writeUInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=e&255,t+2};Y.prototype.writeUint32LE=Y.prototype.writeUInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=e&255,t+4};Y.prototype.writeUint32BE=Y.prototype.writeUInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};function RS(r,e,t,n,o){MS(e,n,o,r,t,7);let s=Number(e&BigInt(4294967295));r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s,s=s>>8,r[t++]=s;let i=Number(e>>BigInt(32)&BigInt(4294967295));return r[t++]=i,i=i>>8,r[t++]=i,i=i>>8,r[t++]=i,i=i>>8,r[t++]=i,t}function BS(r,e,t,n,o){MS(e,n,o,r,t,7);let s=Number(e&BigInt(4294967295));r[t+7]=s,s=s>>8,r[t+6]=s,s=s>>8,r[t+5]=s,s=s>>8,r[t+4]=s;let i=Number(e>>BigInt(32)&BigInt(4294967295));return r[t+3]=i,i=i>>8,r[t+2]=i,i=i>>8,r[t+1]=i,i=i>>8,r[t]=i,t+8}Y.prototype.writeBigUInt64LE=Jn(function(e,t=0){return RS(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))});Y.prototype.writeBigUInt64BE=Jn(function(e,t=0){return BS(this,e,t,BigInt(0),BigInt("0xffffffffffffffff"))});Y.prototype.writeIntLE=function(e,t,n,o){if(e=+e,t=t>>>0,!o){let f=Math.pow(2,8*n-1);xr(this,e,t,n,f-1,-f)}let s=0,i=1,u=0;for(this[t]=e&255;++s<n&&(i*=256);)e<0&&u===0&&this[t+s-1]!==0&&(u=1),this[t+s]=(e/i>>0)-u&255;return t+n};Y.prototype.writeIntBE=function(e,t,n,o){if(e=+e,t=t>>>0,!o){let f=Math.pow(2,8*n-1);xr(this,e,t,n,f-1,-f)}let s=n-1,i=1,u=0;for(this[t+s]=e&255;--s>=0&&(i*=256);)e<0&&u===0&&this[t+s+1]!==0&&(u=1),this[t+s]=(e/i>>0)-u&255;return t+n};Y.prototype.writeInt8=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=e&255,t+1};Y.prototype.writeInt16LE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,2,32767,-32768),this[t]=e&255,this[t+1]=e>>>8,t+2};Y.prototype.writeInt16BE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=e&255,t+2};Y.prototype.writeInt32LE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,4,2147483647,-2147483648),this[t]=e&255,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4};Y.prototype.writeInt32BE=function(e,t,n){return e=+e,t=t>>>0,n||xr(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=e&255,t+4};Y.prototype.writeBigInt64LE=Jn(function(e,t=0){return RS(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});Y.prototype.writeBigInt64BE=Jn(function(e,t=0){return BS(this,e,t,-BigInt("0x8000000000000000"),BigInt("0x7fffffffffffffff"))});function PS(r,e,t,n,o,s){if(t+n>r.length)throw new RangeError("Index out of range");if(t<0)throw new RangeError("Index out of range")}function TS(r,e,t,n,o){return e=+e,t=t>>>0,o||PS(r,e,t,4,34028234663852886e22,-34028234663852886e22),Gs.write(r,e,t,n,23,4),t+4}Y.prototype.writeFloatLE=function(e,t,n){return TS(this,e,t,!0,n)};Y.prototype.writeFloatBE=function(e,t,n){return TS(this,e,t,!1,n)};function zS(r,e,t,n,o){return e=+e,t=t>>>0,o||PS(r,e,t,8,17976931348623157e292,-17976931348623157e292),Gs.write(r,e,t,n,52,8),t+8}Y.prototype.writeDoubleLE=function(e,t,n){return zS(this,e,t,!0,n)};Y.prototype.writeDoubleBE=function(e,t,n){return zS(this,e,t,!1,n)};Y.prototype.copy=function(e,t,n,o){if(!Y.isBuffer(e))throw new TypeError("argument should be a Buffer");if(n||(n=0),!o&&o!==0&&(o=this.length),t>=e.length&&(t=e.length),t||(t=0),o>0&&o<n&&(o=n),o===n||e.length===0||this.length===0)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("Index out of range");if(o<0)throw new RangeError("sourceEnd out of bounds");o>this.length&&(o=this.length),e.length-t<o-n&&(o=e.length-t+n);let s=o-n;return this===e&&typeof Uint8Array.prototype.copyWithin=="function"?this.copyWithin(t,n,o):Uint8Array.prototype.set.call(e,this.subarray(n,o),t),s};Y.prototype.fill=function(e,t,n,o){if(typeof e=="string"){if(typeof t=="string"?(o=t,t=0,n=this.length):typeof n=="string"&&(o=n,n=this.length),o!==void 0&&typeof o!="string")throw new TypeError("encoding must be a string");if(typeof o=="string"&&!Y.isEncoding(o))throw new TypeError("Unknown encoding: "+o);if(e.length===1){let i=e.charCodeAt(0);(o==="utf8"&&i<128||o==="latin1")&&(e=i)}}else typeof e=="number"?e=e&255:typeof e=="boolean"&&(e=Number(e));if(t<0||this.length<t||this.length<n)throw new RangeError("Out of range index");if(n<=t)return this;t=t>>>0,n=n===void 0?this.length:n>>>0,e||(e=0);let s;if(typeof e=="number")for(s=t;s<n;++s)this[s]=e;else{let i=Y.isBuffer(e)?e:Y.from(e,o),u=i.length;if(u===0)throw new TypeError('The value "'+e+'" is invalid for argument "value"');for(s=0;s<n-t;++s)this[s+t]=i[s%u]}return this};var Vs={};function Jl(r,e,t){Vs[r]=class extends t{constructor(){super(),Object.defineProperty(this,"message",{value:e.apply(this,arguments),writable:!0,configurable:!0}),this.name=`${this.name} [${r}]`,this.stack,delete this.name}get code(){return r}set code(o){Object.defineProperty(this,"code",{configurable:!0,enumerable:!0,value:o,writable:!0})}toString(){return`${this.name} [${r}]: ${this.message}`}}}Jl("ERR_BUFFER_OUT_OF_BOUNDS",function(r){return r?`${r} is outside of buffer bounds`:"Attempt to access memory outside buffer bounds"},RangeError);Jl("ERR_INVALID_ARG_TYPE",function(r,e){return`The "${r}" argument must be of type number. Received type ${typeof e}`},TypeError);Jl("ERR_OUT_OF_RANGE",function(r,e,t){let n=`The value of "${r}" is out of range.`,o=t;return Number.isInteger(t)&&Math.abs(t)>2**32?o=SS(String(t)):typeof t=="bigint"&&(o=String(t),(t>BigInt(2)**BigInt(32)||t<-(BigInt(2)**BigInt(32)))&&(o=SS(o)),o+="n"),n+=` It must be ${e}. Received ${o}`,n},RangeError);function SS(r){let e="",t=r.length,n=r[0]==="-"?1:0;for(;t>=n+4;t-=3)e=`_${r.slice(t-3,t)}${e}`;return`${r.slice(0,t)}${e}`}function k8(r,e,t){Zs(e,"offset"),(r[e]===void 0||r[e+t]===void 0)&&Ki(e,r.length-(t+1))}function MS(r,e,t,n,o,s){if(r>t||r<e){let i=typeof e=="bigint"?"n":"",u;throw s>3?e===0||e===BigInt(0)?u=`>= 0${i} and < 2${i} ** ${(s+1)*8}${i}`:u=`>= -(2${i} ** ${(s+1)*8-1}${i}) and < 2 ** ${(s+1)*8-1}${i}`:u=`>= ${e}${i} and <= ${t}${i}`,new Vs.ERR_OUT_OF_RANGE("value",u,r)}k8(n,o,s)}function Zs(r,e){if(typeof r!="number")throw new Vs.ERR_INVALID_ARG_TYPE(e,"number",r)}function Ki(r,e,t){throw Math.floor(r)!==r?(Zs(r,t),new Vs.ERR_OUT_OF_RANGE(t||"offset","an integer",r)):e<0?new Vs.ERR_BUFFER_OUT_OF_BOUNDS:new Vs.ERR_OUT_OF_RANGE(t||"offset",`>= ${t?1:0} and <= ${e}`,r)}var A8=/[^+/0-9A-Za-z-_]/g;function I8(r){if(r=r.split("=")[0],r=r.trim().replace(A8,""),r.length<2)return"";for(;r.length%4!==0;)r=r+"=";return r}function Zl(r,e){e=e||1/0;let t,n=r.length,o=null,s=[];for(let i=0;i<n;++i){if(t=r.charCodeAt(i),t>55295&&t<57344){if(!o){if(t>56319){(e-=3)>-1&&s.push(239,191,189);continue}else if(i+1===n){(e-=3)>-1&&s.push(239,191,189);continue}o=t;continue}if(t<56320){(e-=3)>-1&&s.push(239,191,189),o=t;continue}t=(o-55296<<10|t-56320)+65536}else o&&(e-=3)>-1&&s.push(239,191,189);if(o=null,t<128){if((e-=1)<0)break;s.push(t)}else if(t<2048){if((e-=2)<0)break;s.push(t>>6|192,t&63|128)}else if(t<65536){if((e-=3)<0)break;s.push(t>>12|224,t>>6&63|128,t&63|128)}else if(t<1114112){if((e-=4)<0)break;s.push(t>>18|240,t>>12&63|128,t>>6&63|128,t&63|128)}else throw new Error("Invalid code point")}return s}function R8(r){let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(t)&255);return e}function B8(r,e){let t,n,o,s=[];for(let i=0;i<r.length&&!((e-=2)<0);++i)t=r.charCodeAt(i),n=t>>8,o=t%256,s.push(o),s.push(n);return s}function LS(r){return Kl.toByteArray(I8(r))}function Nc(r,e,t,n){let o;for(o=0;o<n&&!(o+t>=e.length||o>=r.length);++o)e[o+t]=r[o];return o}function sn(r,e){return r instanceof e||r!=null&&r.constructor!=null&&r.constructor.name!=null&&r.constructor.name===e.name}function Ql(r){return r!==r}var P8=function(){let r="0123456789abcdef",e=new Array(256);for(let t=0;t<16;++t){let n=t*16;for(let o=0;o<16;++o)e[n+o]=r[t]+r[o]}return e}();function Jn(r){return typeof BigInt>"u"?T8:r}function T8(){throw new Error("BigInt not supported")}});function Xl(r){if(!Number.isSafeInteger(r)||r<0)throw new Error(`Wrong positive integer: ${r}`)}function z8(r){if(typeof r!="boolean")throw new Error(`Expected boolean, not ${r}`)}function NS(r,...e){if(!(r instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(e.length>0&&!e.includes(r.length))throw new TypeError(`Expected Uint8Array of length ${e}, not of length=${r.length}`)}function M8(r){if(typeof r!="function"||typeof r.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Xl(r.outputLen),Xl(r.blockLen)}function L8(r,e=!0){if(r.destroyed)throw new Error("Hash instance has been destroyed");if(e&&r.finished)throw new Error("Hash#digest() has already been called")}function N8(r,e){NS(r);let t=e.outputLen;if(r.length<t)throw new Error(`digestInto() expects output buffer of length at least ${t}`)}var C8,rr,Cc=O(()=>{y();C8={number:Xl,bool:z8,bytes:NS,hash:M8,exists:L8,output:N8},rr=C8});var O8,CS=O(()=>{y();O8={node:void 0,web:typeof self=="object"&&"crypto"in self?self.crypto:void 0}});function U8(r){if(typeof r!="string")throw new TypeError(`utf8ToBytes expected string, got ${typeof r}`);return new TextEncoder().encode(r)}function Xn(r){if(typeof r=="string"&&(r=U8(r)),!(r instanceof Uint8Array))throw new TypeError(`Expected input type is Uint8Array (got ${typeof r})`);return r}function an(r){let e=n=>r().update(Xn(n)).digest(),t=r();return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=()=>r(),e}function qS(r){let e=(n,o)=>r(o).update(Xn(n)).digest(),t=r({});return e.outputLen=t.outputLen,e.blockLen=t.blockLen,e.create=n=>r(n),e}var OS,Oc,Fr,q8,mV,Qn,$s=O(()=>{y();CS();OS=r=>new Uint32Array(r.buffer,r.byteOffset,Math.floor(r.byteLength/4)),Oc=r=>new DataView(r.buffer,r.byteOffset,r.byteLength),Fr=(r,e)=>r<<32-e|r>>>e,q8=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!q8)throw new Error("Non little-endian hardware is not supported");mV=Array.from({length:256},(r,e)=>e.toString(16).padStart(2,"0"));Qn=class{clone(){return this._cloneInto()}}});function D8(r,e,t,n){if(typeof r.setBigUint64=="function")return r.setBigUint64(e,t,n);let o=BigInt(32),s=BigInt(4294967295),i=Number(t>>o&s),u=Number(t&s),f=n?4:0,g=n?0:4;r.setUint32(e+f,i,n),r.setUint32(e+g,u,n)}var Js,eh=O(()=>{y();Cc();$s();Js=class extends Qn{constructor(e,t,n,o){super(),this.blockLen=e,this.outputLen=t,this.padOffset=n,this.isLE=o,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Oc(this.buffer)}update(e){rr.exists(this);let{view:t,buffer:n,blockLen:o}=this;e=Xn(e);let s=e.length;for(let i=0;i<s;){let u=Math.min(o-this.pos,s-i);if(u===o){let f=Oc(e);for(;o<=s-i;i+=o)this.process(f,i);continue}n.set(e.subarray(i,i+u),this.pos),this.pos+=u,i+=u,this.pos===o&&(this.process(t,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){rr.exists(this),rr.output(e,this),this.finished=!0;let{buffer:t,view:n,blockLen:o,isLE:s}=this,{pos:i}=this;t[i++]=128,this.buffer.subarray(i).fill(0),this.padOffset>o-i&&(this.process(n,0),i=0);for(let E=i;E<o;E++)t[E]=0;D8(n,o-8,BigInt(this.length*8),s),this.process(n,0);let u=Oc(e),f=this.outputLen;if(f%4)throw new Error("_sha2: outputLen should be aligned to 32bit");let g=f/4,b=this.get();if(g>b.length)throw new Error("_sha2: outputLen bigger than state");for(let E=0;E<g;E++)u.setUint32(4*E,b[E],s)}digest(){let{buffer:e,outputLen:t}=this;this.digestInto(e);let n=e.slice(0,t);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());let{blockLen:t,buffer:n,length:o,finished:s,destroyed:i,pos:u}=this;return e.length=o,e.pos=u,e.finished=s,e.destroyed=i,o%t&&e.buffer.set(n),e}}});function US(r,e=!1){return e?{h:Number(r&qc),l:Number(r>>th&qc)}:{h:Number(r>>th&qc)|0,l:Number(r&qc)|0}}function F8(r,e=!1){let t=new Uint32Array(r.length),n=new Uint32Array(r.length);for(let o=0;o<r.length;o++){let{h:s,l:i}=US(r[o],e);[t[o],n[o]]=[s,i]}return[t,n]}function tM(r,e,t,n){let o=(e>>>0)+(n>>>0);return{h:r+t+(o/2**32|0)|0,l:o|0}}var qc,th,j8,H8,W8,K8,V8,G8,Z8,Y8,$8,J8,Q8,X8,eM,rM,nM,oM,sM,iM,aM,cM,ze,rh=O(()=>{y();qc=BigInt(4294967295),th=BigInt(32);j8=(r,e)=>BigInt(r>>>0)<<th|BigInt(e>>>0),H8=(r,e,t)=>r>>>t,W8=(r,e,t)=>r<<32-t|e>>>t,K8=(r,e,t)=>r>>>t|e<<32-t,V8=(r,e,t)=>r<<32-t|e>>>t,G8=(r,e,t)=>r<<64-t|e>>>t-32,Z8=(r,e,t)=>r>>>t-32|e<<64-t,Y8=(r,e)=>e,$8=(r,e)=>r,J8=(r,e,t)=>r<<t|e>>>32-t,Q8=(r,e,t)=>e<<t|r>>>32-t,X8=(r,e,t)=>e<<t-32|r>>>64-t,eM=(r,e,t)=>r<<t-32|e>>>64-t;rM=(r,e,t)=>(r>>>0)+(e>>>0)+(t>>>0),nM=(r,e,t,n)=>e+t+n+(r/2**32|0)|0,oM=(r,e,t,n)=>(r>>>0)+(e>>>0)+(t>>>0)+(n>>>0),sM=(r,e,t,n,o)=>e+t+n+o+(r/2**32|0)|0,iM=(r,e,t,n,o)=>(r>>>0)+(e>>>0)+(t>>>0)+(n>>>0)+(o>>>0),aM=(r,e,t,n,o,s)=>e+t+n+o+s+(r/2**32|0)|0,cM={fromBig:US,split:F8,toBig:j8,shrSH:H8,shrSL:W8,rotrSH:K8,rotrSL:V8,rotrBH:G8,rotrBL:Z8,rotr32H:Y8,rotr32L:$8,rotlSH:J8,rotlSL:Q8,rotlBH:X8,rotlBL:eM,add:tM,add3L:rM,add3H:nM,add4L:oM,add4H:sM,add5H:aM,add5L:iM},ze=cM});var uM,pM,eo,to,Qs,nh,oh,sh,DS,RV,BV,PV,FS=O(()=>{y();eh();rh();$s();[uM,pM]=ze.split(["0x428a2f98d728ae22","0x7137449123ef65cd","0xb5c0fbcfec4d3b2f","0xe9b5dba58189dbbc","0x3956c25bf348b538","0x59f111f1b605d019","0x923f82a4af194f9b","0xab1c5ed5da6d8118","0xd807aa98a3030242","0x12835b0145706fbe","0x243185be4ee4b28c","0x550c7dc3d5ffb4e2","0x72be5d74f27b896f","0x80deb1fe3b1696b1","0x9bdc06a725c71235","0xc19bf174cf692694","0xe49b69c19ef14ad2","0xefbe4786384f25e3","0x0fc19dc68b8cd5b5","0x240ca1cc77ac9c65","0x2de92c6f592b0275","0x4a7484aa6ea6e483","0x5cb0a9dcbd41fbd4","0x76f988da831153b5","0x983e5152ee66dfab","0xa831c66d2db43210","0xb00327c898fb213f","0xbf597fc7beef0ee4","0xc6e00bf33da88fc2","0xd5a79147930aa725","0x06ca6351e003826f","0x142929670a0e6e70","0x27b70a8546d22ffc","0x2e1b21385c26c926","0x4d2c6dfc5ac42aed","0x53380d139d95b3df","0x650a73548baf63de","0x766a0abb3c77b2a8","0x81c2c92e47edaee6","0x92722c851482353b","0xa2bfe8a14cf10364","0xa81a664bbc423001","0xc24b8b70d0f89791","0xc76c51a30654be30","0xd192e819d6ef5218","0xd69906245565a910","0xf40e35855771202a","0x106aa07032bbd1b8","0x19a4c116b8d2d0c8","0x1e376c085141ab53","0x2748774cdf8eeb99","0x34b0bcb5e19b48a8","0x391c0cb3c5c95a63","0x4ed8aa4ae3418acb","0x5b9cca4f7763e373","0x682e6ff3d6b2b8a3","0x748f82ee5defb2fc","0x78a5636f43172f60","0x84c87814a1f0ab72","0x8cc702081a6439ec","0x90befffa23631e28","0xa4506cebde82bde9","0xbef9a3f7b2c67915","0xc67178f2e372532b","0xca273eceea26619c","0xd186b8c721c0c207","0xeada7dd6cde0eb1e","0xf57d4f7fee6ed178","0x06f067aa72176fba","0x0a637dc5a2c898a6","0x113f9804bef90dae","0x1b710b35131c471b","0x28db77f523047d84","0x32caab7b40c72493","0x3c9ebe0a15c9bebc","0x431d67c49c100d4c","0x4cc5d4becb3e42b6","0x597f299cfc657e2a","0x5fcb6fab3ad6faec","0x6c44198c4a475817"].map(r=>BigInt(r))),eo=new Uint32Array(80),to=new Uint32Array(80),Qs=class extends Js{constructor(){super(128,64,16,!1),this.Ah=1779033703,this.Al=-205731576,this.Bh=-1150833019,this.Bl=-2067093701,this.Ch=1013904242,this.Cl=-23791573,this.Dh=-1521486534,this.Dl=1595750129,this.Eh=1359893119,this.El=-1377402159,this.Fh=-1694144372,this.Fl=725511199,this.Gh=528734635,this.Gl=-79577749,this.Hh=1541459225,this.Hl=327033209}get(){let{Ah:e,Al:t,Bh:n,Bl:o,Ch:s,Cl:i,Dh:u,Dl:f,Eh:g,El:b,Fh:E,Fl:q,Gh:C,Gl:K,Hh:ee,Hl:$}=this;return[e,t,n,o,s,i,u,f,g,b,E,q,C,K,ee,$]}set(e,t,n,o,s,i,u,f,g,b,E,q,C,K,ee,$){this.Ah=e|0,this.Al=t|0,this.Bh=n|0,this.Bl=o|0,this.Ch=s|0,this.Cl=i|0,this.Dh=u|0,this.Dl=f|0,this.Eh=g|0,this.El=b|0,this.Fh=E|0,this.Fl=q|0,this.Gh=C|0,this.Gl=K|0,this.Hh=ee|0,this.Hl=$|0}process(e,t){for(let be=0;be<16;be++,t+=4)eo[be]=e.getUint32(t),to[be]=e.getUint32(t+=4);for(let be=16;be<80;be++){let Te=eo[be-15]|0,Oe=to[be-15]|0,Ie=ze.rotrSH(Te,Oe,1)^ze.rotrSH(Te,Oe,8)^ze.shrSH(Te,Oe,7),Fe=ze.rotrSL(Te,Oe,1)^ze.rotrSL(Te,Oe,8)^ze.shrSL(Te,Oe,7),_=eo[be-2]|0,A=to[be-2]|0,z=ze.rotrSH(_,A,19)^ze.rotrBH(_,A,61)^ze.shrSH(_,A,6),D=ze.rotrSL(_,A,19)^ze.rotrBL(_,A,61)^ze.shrSL(_,A,6),R=ze.add4L(Fe,D,to[be-7],to[be-16]),m=ze.add4H(R,Ie,z,eo[be-7],eo[be-16]);eo[be]=m|0,to[be]=R|0}let{Ah:n,Al:o,Bh:s,Bl:i,Ch:u,Cl:f,Dh:g,Dl:b,Eh:E,El:q,Fh:C,Fl:K,Gh:ee,Gl:$,Hh:le,Hl:W}=this;for(let be=0;be<80;be++){let Te=ze.rotrSH(E,q,14)^ze.rotrSH(E,q,18)^ze.rotrBH(E,q,41),Oe=ze.rotrSL(E,q,14)^ze.rotrSL(E,q,18)^ze.rotrBL(E,q,41),Ie=E&C^~E&ee,Fe=q&K^~q&$,_=ze.add5L(W,Oe,Fe,pM[be],to[be]),A=ze.add5H(_,le,Te,Ie,uM[be],eo[be]),z=_|0,D=ze.rotrSH(n,o,28)^ze.rotrBH(n,o,34)^ze.rotrBH(n,o,39),R=ze.rotrSL(n,o,28)^ze.rotrBL(n,o,34)^ze.rotrBL(n,o,39),m=n&s^n&u^s&u,c=o&i^o&f^i&f;le=ee|0,W=$|0,ee=C|0,$=K|0,C=E|0,K=q|0,{h:E,l:q}=ze.add(g|0,b|0,A|0,z|0),g=u|0,b=f|0,u=s|0,f=i|0,s=n|0,i=o|0;let p=ze.add3L(z,R,c);n=ze.add3H(p,A,D,m),o=p|0}({h:n,l:o}=ze.add(this.Ah|0,this.Al|0,n|0,o|0)),{h:s,l:i}=ze.add(this.Bh|0,this.Bl|0,s|0,i|0),{h:u,l:f}=ze.add(this.Ch|0,this.Cl|0,u|0,f|0),{h:g,l:b}=ze.add(this.Dh|0,this.Dl|0,g|0,b|0),{h:E,l:q}=ze.add(this.Eh|0,this.El|0,E|0,q|0),{h:C,l:K}=ze.add(this.Fh|0,this.Fl|0,C|0,K|0),{h:ee,l:$}=ze.add(this.Gh|0,this.Gl|0,ee|0,$|0),{h:le,l:W}=ze.add(this.Hh|0,this.Hl|0,le|0,W|0),this.set(n,o,s,i,u,f,g,b,E,q,C,K,ee,$,le,W)}roundClean(){eo.fill(0),to.fill(0)}destroy(){this.buffer.fill(0),this.set(0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0)}},nh=class extends Qs{constructor(){super(),this.Ah=-1942145080,this.Al=424955298,this.Bh=1944164710,this.Bl=-1982016298,this.Ch=502970286,this.Cl=855612546,this.Dh=1738396948,this.Dl=1479516111,this.Eh=258812777,this.El=2077511080,this.Fh=2011393907,this.Fl=79989058,this.Gh=1067287976,this.Gl=1780299464,this.Hh=286451373,this.Hl=-1848208735,this.outputLen=28}},oh=class extends Qs{constructor(){super(),this.Ah=573645204,this.Al=-64227540,this.Bh=-1621794909,this.Bl=-934517566,this.Ch=596883563,this.Cl=1867755857,this.Dh=-1774684391,this.Dl=1497426621,this.Eh=-1775747358,this.El=-1467023389,this.Fh=-1101128155,this.Fl=1401305490,this.Gh=721525244,this.Gl=746961066,this.Hh=246885852,this.Hl=-2117784414,this.outputLen=32}},sh=class extends Qs{constructor(){super(),this.Ah=-876896931,this.Al=-1056596264,this.Bh=1654270250,this.Bl=914150663,this.Ch=-1856437926,this.Cl=812702999,this.Dh=355462360,this.Dl=-150054599,this.Eh=1731405415,this.El=-4191439,this.Fh=-1900787065,this.Fl=1750603025,this.Gh=-619958771,this.Gl=1694076839,this.Hh=1203062813,this.Hl=-1090891868,this.outputLen=48}},DS=an(()=>new Qs),RV=an(()=>new nh),BV=an(()=>new oh),PV=an(()=>new sh)});var ih=_e(()=>{y()});function HS(r,e){let t=e.negate();return r?t:e}function WS(r){if(!(r instanceof Wt))throw new TypeError("ExtendedPoint expected")}function ah(r){if(!(r instanceof as))throw new TypeError("RistrettoPoint expected")}function ch(){throw new Error("Legacy method: switch to RistrettoPoint")}function KS(...r){if(!r.every(n=>n instanceof Uint8Array))throw new Error("Expected Uint8Array list");if(r.length===1)return r[0];let e=r.reduce((n,o)=>n+o.length,0),t=new Uint8Array(e);for(let n=0,o=0;n<r.length;n++){let s=r[n];t.set(s,o),o+=s.length}return t}function Ji(r){if(!(r instanceof Uint8Array))throw new Error("Uint8Array expected");let e="";for(let t=0;t<r.length;t++)e+=gM[r[t]];return e}function fh(r){if(typeof r!="string")throw new TypeError("hexToBytes: expected string, got "+typeof r);if(r.length%2)throw new Error("hexToBytes: received invalid unpadded hex");let e=new Uint8Array(r.length/2);for(let t=0;t<e.length;t++){let n=t*2,o=r.slice(n,n+2),s=Number.parseInt(o,16);if(Number.isNaN(s)||s<0)throw new Error("Invalid byte sequence");e[t]=s}return e}function ZS(r){let t=r.toString(16).padStart(64,"0");return fh(t)}function $i(r){return ZS(r).reverse()}function no(r){return(te(r)&He)===He}function Qi(r){if(!(r instanceof Uint8Array))throw new Error("Expected Uint8Array");return BigInt("0x"+Ji(Uint8Array.from(r).reverse()))}function uh(r){return te(Qi(r)&xM)}function te(r,e=Lt.P){let t=r%e;return t>=Yt?t:e+t}function jc(r,e=Lt.P){if(r===Yt||e<=Yt)throw new Error(`invert: expected positive integers, got n=${r} mod=${e}`);let t=te(r,e),n=e,o=Yt,s=He,i=He,u=Yt;for(;t!==Yt;){let g=n/t,b=n%t,E=o-i*g,q=s-u*g;n=t,t=b,o=i,s=u,i=E,u=q}if(n!==He)throw new Error("invert: does not exist");return te(o,e)}function wM(r,e=Lt.P){let t=new Array(r.length),n=r.reduce((s,i,u)=>i===Yt?s:(t[u]=s,te(s*i,e)),He),o=jc(n,e);return r.reduceRight((s,i,u)=>i===Yt?s:(t[u]=te(s*t[u],e),te(s*i,e)),o),t}function cn(r,e){let{P:t}=Lt,n=r;for(;e-- >Yt;)n*=n,n%=t;return n}function bM(r){let{P:e}=Lt,t=BigInt(5),n=BigInt(10),o=BigInt(20),s=BigInt(40),i=BigInt(80),f=r*r%e*r%e,g=cn(f,oo)*f%e,b=cn(g,He)*r%e,E=cn(b,t)*b%e,q=cn(E,n)*E%e,C=cn(q,o)*q%e,K=cn(C,s)*C%e,ee=cn(K,i)*K%e,$=cn(ee,i)*K%e,le=cn($,n)*E%e;return{pow_p_5_8:cn(le,oo)*r%e,b2:f}}function lh(r,e){let t=te(e*e*e),n=te(t*t*e),o=bM(r*n).pow_p_5_8,s=te(r*t*o),i=te(e*s*s),u=s,f=te(s*Gi),g=i===r,b=i===te(-r),E=i===te(-r*Gi);return g&&(s=u),(b||E)&&(s=f),no(s)&&(s=te(-s)),{isValid:g||b,value:s}}function VS(r){return lh(He,r)}function Uc(r){return te(Qi(r),Lt.l)}function vM(r,e){if(r.length!==e.length)return!1;for(let t=0;t<r.length;t++)if(r[t]!==e[t])return!1;return!0}function so(r,e){let t=r instanceof Uint8Array?Uint8Array.from(r):fh(r);if(typeof e=="number"&&t.length!==e)throw new Error(`Expected ${e} bytes`);return t}function Dc(r,e,t=!0){if(!e)throw new TypeError("Specify max value");if(typeof r=="number"&&Number.isSafeInteger(r)&&(r=BigInt(r)),typeof r=="bigint"&&r<e){if(t){if(Yt<r)return r}else if(Yt<=r)return r}throw new TypeError("Expected valid scalar: 0 < scalar < max")}function SM(r){return r[0]&=248,r[31]&=127,r[31]|=64,r}function YS(r){if(r=typeof r=="bigint"||typeof r=="number"?ZS(Dc(r,GS)):so(r),r.length!==32)throw new Error("Expected 32 bytes");return r}function $S(r){let e=SM(r.slice(0,32)),t=r.slice(32,64),n=Uc(e),o=Dt.BASE.multiply(n),s=o.toRawBytes();return{head:e,prefix:t,scalar:n,point:o,pointBytes:s}}function Fc(...r){if(typeof Zi!="function")throw new Error("utils.sha512Sync must be set to use sync methods");return Zi(...r)}async function JS(r){return $S(await io.sha512(YS(r)))}function hh(r){return $S(Fc(YS(r)))}function EM(r){return hh(r).pointBytes}function _M(r,e){r=so(r);let{prefix:t,scalar:n,pointBytes:o}=hh(e),s=Uc(Fc(t,r)),i=Dt.BASE.multiply(s),u=Uc(Fc(i.toRawBytes(),o,r)),f=te(s+u*n,Lt.l);return new Yi(i,f).toRawBytes()}function kM(r,e,t){e=so(e),t instanceof Dt||(t=Dt.fromHex(t,!1));let{r:n,s:o}=r instanceof Yi?r.assertValidity():Yi.fromHex(r),s=Wt.BASE.multiplyUnsafe(o);return{r:n,s:o,SB:s,pub:t,msg:e}}function AM(r,e,t,n){let o=Uc(n),s=Wt.fromAffine(r).multiplyUnsafe(o);return Wt.fromAffine(e).add(s).subtract(t).multiplyUnsafe(Lt.h).equals(Wt.ZERO)}function IM(r,e,t){let{r:n,SB:o,msg:s,pub:i}=kM(r,e,t),u=Fc(n.toRawBytes(),i.toRawBytes(),s);return AM(i,n,o,u)}var fM,Yt,He,oo,lM,jS,Lt,GS,Gi,CV,hM,dM,yM,mM,Wt,as,ph,Dt,Yi,gM,xM,Zi,Hc,ro,io,QS=O(()=>{y();fM=Ke(ih(),1);Yt=BigInt(0),He=BigInt(1),oo=BigInt(2),lM=BigInt(8),jS=BigInt("7237005577332262213973186563042994240857116359379907606001950938285454250989"),Lt=Object.freeze({a:BigInt(-1),d:BigInt("37095705934669439343138083508754565189542113879843219016388785533085940283555"),P:BigInt("57896044618658097711785492504343953926634992332820282019728792003956564819949"),l:jS,n:jS,h:BigInt(8),Gx:BigInt("15112221349535400772501151409588531511454012693041857206046113283949847762202"),Gy:BigInt("46316835694926478169428394003475163141307993866256225615783033603165251855960")}),GS=BigInt("0x10000000000000000000000000000000000000000000000000000000000000000"),Gi=BigInt("19681161376707505956807079304988542015446066515923890162744021073123829784752"),CV=BigInt("6853475219497561581579357271197624642482790079785650197046958215289687604742"),hM=BigInt("25063068953384623474111414158702152701244531502492656460079210482610430750235"),dM=BigInt("54469307008909316920995813868745141605393597292927456921205312896311721017578"),yM=BigInt("1159843021668779879193775521855586647937357759715417654439879720876111806838"),mM=BigInt("40440834346308536858101042469323190826248399146238708352240133220865137265952"),Wt=class r{constructor(e,t,n,o){this.x=e,this.y=t,this.z=n,this.t=o}static fromAffine(e){if(!(e instanceof Dt))throw new TypeError("ExtendedPoint#fromAffine: expected Point");return e.equals(Dt.ZERO)?r.ZERO:new r(e.x,e.y,He,te(e.x*e.y))}static toAffineBatch(e){let t=wM(e.map(n=>n.z));return e.map((n,o)=>n.toAffine(t[o]))}static normalizeZ(e){return this.toAffineBatch(e).map(this.fromAffine)}equals(e){WS(e);let{x:t,y:n,z:o}=this,{x:s,y:i,z:u}=e,f=te(t*u),g=te(s*o),b=te(n*u),E=te(i*o);return f===g&&b===E}negate(){return new r(te(-this.x),this.y,this.z,te(-this.t))}double(){let{x:e,y:t,z:n}=this,{a:o}=Lt,s=te(e*e),i=te(t*t),u=te(oo*te(n*n)),f=te(o*s),g=e+t,b=te(te(g*g)-s-i),E=f+i,q=E-u,C=f-i,K=te(b*q),ee=te(E*C),$=te(b*C),le=te(q*E);return new r(K,ee,le,$)}add(e){WS(e);let{x:t,y:n,z:o,t:s}=this,{x:i,y:u,z:f,t:g}=e,b=te((n-t)*(u+i)),E=te((n+t)*(u-i)),q=te(E-b);if(q===Yt)return this.double();let C=te(o*oo*g),K=te(s*oo*f),ee=K+C,$=E+b,le=K-C,W=te(ee*q),be=te($*le),Te=te(ee*le),Oe=te(q*$);return new r(W,be,Oe,Te)}subtract(e){return this.add(e.negate())}precomputeWindow(e){let t=1+256/e,n=[],o=this,s=o;for(let i=0;i<t;i++){s=o,n.push(s);for(let u=1;u<2**(e-1);u++)s=s.add(o),n.push(s);o=s.double()}return n}wNAF(e,t){!t&&this.equals(r.BASE)&&(t=Dt.BASE);let n=t&&t._WINDOW_SIZE||1;if(256%n)throw new Error("Point#wNAF: Invalid precomputation window, must be power of 2");let o=t&&ph.get(t);o||(o=this.precomputeWindow(n),t&&n!==1&&(o=r.normalizeZ(o),ph.set(t,o)));let s=r.ZERO,i=r.BASE,u=1+256/n,f=2**(n-1),g=BigInt(2**n-1),b=2**n,E=BigInt(n);for(let q=0;q<u;q++){let C=q*f,K=Number(e&g);e>>=E,K>f&&(K-=b,e+=He);let ee=C,$=C+Math.abs(K)-1,le=q%2!==0,W=K<0;K===0?i=i.add(HS(le,o[ee])):s=s.add(HS(W,o[$]))}return r.normalizeZ([s,i])[0]}multiply(e,t){return this.wNAF(Dc(e,Lt.l),t)}multiplyUnsafe(e){let t=Dc(e,Lt.l,!1),n=r.BASE,o=r.ZERO;if(t===Yt)return o;if(this.equals(o)||t===He)return this;if(this.equals(n))return this.wNAF(t);let s=o,i=this;for(;t>Yt;)t&He&&(s=s.add(i)),i=i.double(),t>>=He;return s}isSmallOrder(){return this.multiplyUnsafe(Lt.h).equals(r.ZERO)}isTorsionFree(){let e=this.multiplyUnsafe(Lt.l/oo).double();return Lt.l%oo&&(e=e.add(this)),e.equals(r.ZERO)}toAffine(e){let{x:t,y:n,z:o}=this,s=this.equals(r.ZERO);e==null&&(e=s?lM:jc(o));let i=te(t*e),u=te(n*e),f=te(o*e);if(s)return Dt.ZERO;if(f!==He)throw new Error("invZ was invalid");return new Dt(i,u)}fromRistrettoBytes(){ch()}toRistrettoBytes(){ch()}fromRistrettoHash(){ch()}};Wt.BASE=new Wt(Lt.Gx,Lt.Gy,He,te(Lt.Gx*Lt.Gy));Wt.ZERO=new Wt(Yt,He,He,Yt);as=class r{constructor(e){this.ep=e}static calcElligatorRistrettoMap(e){let{d:t}=Lt,n=te(Gi*e*e),o=te((n+He)*yM),s=BigInt(-1),i=te((s-t*n)*te(n+t)),{isValid:u,value:f}=lh(o,i),g=te(f*e);no(g)||(g=te(-g)),u||(f=g),u||(s=n);let b=te(s*(n-He)*mM-i),E=f*f,q=te((f+f)*i),C=te(b*hM),K=te(He-E),ee=te(He+E);return new Wt(te(q*ee),te(K*C),te(C*ee),te(q*K))}static hashToCurve(e){e=so(e,64);let t=uh(e.slice(0,32)),n=this.calcElligatorRistrettoMap(t),o=uh(e.slice(32,64)),s=this.calcElligatorRistrettoMap(o);return new r(n.add(s))}static fromHex(e){e=so(e,32);let{a:t,d:n}=Lt,o="RistrettoPoint.fromHex: the hex is not valid encoding of RistrettoPoint",s=uh(e);if(!vM($i(s),e)||no(s))throw new Error(o);let i=te(s*s),u=te(He+t*i),f=te(He-t*i),g=te(u*u),b=te(f*f),E=te(t*n*g-b),{isValid:q,value:C}=VS(te(E*b)),K=te(C*f),ee=te(C*K*E),$=te((s+s)*K);no($)&&($=te(-$));let le=te(u*ee),W=te($*le);if(!q||no(W)||le===Yt)throw new Error(o);return new r(new Wt($,le,He,W))}toRawBytes(){let{x:e,y:t,z:n,t:o}=this.ep,s=te(te(n+t)*te(n-t)),i=te(e*t),u=te(i*i),{value:f}=VS(te(s*u)),g=te(f*s),b=te(f*i),E=te(g*b*o),q;if(no(o*E)){let K=te(t*Gi),ee=te(e*Gi);e=K,t=ee,q=te(g*dM)}else q=b;no(e*E)&&(t=te(-t));let C=te((n-t)*q);return no(C)&&(C=te(-C)),$i(C)}toHex(){return Ji(this.toRawBytes())}toString(){return this.toHex()}equals(e){ah(e);let t=this.ep,n=e.ep,o=te(t.x*n.y)===te(t.y*n.x),s=te(t.y*n.y)===te(t.x*n.x);return o||s}add(e){return ah(e),new r(this.ep.add(e.ep))}subtract(e){return ah(e),new r(this.ep.subtract(e.ep))}multiply(e){return new r(this.ep.multiply(e))}multiplyUnsafe(e){return new r(this.ep.multiplyUnsafe(e))}};as.BASE=new as(Wt.BASE);as.ZERO=new as(Wt.ZERO);ph=new WeakMap,Dt=class r{constructor(e,t){this.x=e,this.y=t}_setWindowSize(e){this._WINDOW_SIZE=e,ph.delete(this)}static fromHex(e,t=!0){let{d:n,P:o}=Lt;e=so(e,32);let s=e.slice();s[31]=e[31]&-129;let i=Qi(s);if(t&&i>=o)throw new Error("Expected 0 < hex < P");if(!t&&i>=GS)throw new Error("Expected 0 < hex < 2**256");let u=te(i*i),f=te(u-He),g=te(n*u+He),{isValid:b,value:E}=lh(f,g);if(!b)throw new Error("Point.fromHex: invalid y coordinate");let q=(E&He)===He;return(e[31]&128)!==0!==q&&(E=te(-E)),new r(E,i)}static async fromPrivateKey(e){return(await JS(e)).point}toRawBytes(){let e=$i(this.y);return e[31]|=this.x&He?128:0,e}toHex(){return Ji(this.toRawBytes())}toX25519(){let{y:e}=this,t=te((He+e)*jc(He-e));return $i(t)}isTorsionFree(){return Wt.fromAffine(this).isTorsionFree()}equals(e){return this.x===e.x&&this.y===e.y}negate(){return new r(te(-this.x),this.y)}add(e){return Wt.fromAffine(this).add(Wt.fromAffine(e)).toAffine()}subtract(e){return this.add(e.negate())}multiply(e){return Wt.fromAffine(this).multiply(e,this).toAffine()}};Dt.BASE=new Dt(Lt.Gx,Lt.Gy);Dt.ZERO=new Dt(Yt,He);Yi=class r{constructor(e,t){this.r=e,this.s=t,this.assertValidity()}static fromHex(e){let t=so(e,64),n=Dt.fromHex(t.slice(0,32),!1),o=Qi(t.slice(32,64));return new r(n,o)}assertValidity(){let{r:e,s:t}=this;if(!(e instanceof Dt))throw new Error("Expected Point instance");return Dc(t,Lt.l,!1),this}toRawBytes(){let e=new Uint8Array(64);return e.set(this.r.toRawBytes()),e.set($i(this.s),32),e}toHex(){return Ji(this.toRawBytes())}};gM=Array.from({length:256},(r,e)=>e.toString(16).padStart(2,"0"));xM=BigInt("0x7fffffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff");Hc={getExtendedPublicKey:hh,getPublicKey:EM,sign:_M,verify:IM};Dt.BASE._setWindowSize(8);ro={node:fM,web:typeof self=="object"&&"crypto"in self?self.crypto:void 0},io={bytesToHex:Ji,hexToBytes:fh,concatBytes:KS,getExtendedPublicKey:JS,mod:te,invert:jc,TORSION_SUBGROUP:["0100000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac037a","0000000000000000000000000000000000000000000000000000000000000080","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc05","ecffffffffffffffffffffffffffffffffffffffffffffffffffffffffffff7f","26e8958fc2b227b045c3f489f2ef98f0d5dfac05d3c63339b13802886d53fc85","0000000000000000000000000000000000000000000000000000000000000000","c7176a703d4dd84fba3c0b760d10670f2a2053fa2c39ccc64ec7fd7792ac03fa"],hashToPrivateScalar:r=>{if(r=so(r),r.length<40||r.length>1024)throw new Error("Expected 40-1024 bytes of private key as per FIPS 186");return te(Qi(r),Lt.l-He)+He},randomBytes:(r=32)=>{if(ro.web)return ro.web.getRandomValues(new Uint8Array(r));if(ro.node){let{randomBytes:e}=ro.node;return new Uint8Array(e(r).buffer)}else throw new Error("The environment doesn't have randomBytes function")},randomPrivateKey:()=>io.randomBytes(32),sha512:async(...r)=>{let e=KS(...r);if(ro.web){let t=await ro.web.subtle.digest("SHA-512",e.buffer);return new Uint8Array(t)}else{if(ro.node)return Uint8Array.from(ro.node.createHash("sha512").update(e).digest());throw new Error("The environment doesn't have sha512 function")}},precompute(r=8,e=Dt.BASE){let t=e.equals(Dt.BASE)?e:new Dt(e.x,e.y);return t._setWindowSize(r),t.multiply(oo),t},sha512Sync:void 0};Object.defineProperties(io,{sha512Sync:{configurable:!1,get(){return Zi},set(r){Zi||(Zi=r)}}})});var BM,PM,TM,ao,co,Wc,dh,Kc,DV,XS=O(()=>{y();eh();$s();BM=(r,e,t)=>r&e^~r&t,PM=(r,e,t)=>r&e^r&t^e&t,TM=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),ao=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),co=new Uint32Array(64),Wc=class extends Js{constructor(){super(64,32,8,!1),this.A=ao[0]|0,this.B=ao[1]|0,this.C=ao[2]|0,this.D=ao[3]|0,this.E=ao[4]|0,this.F=ao[5]|0,this.G=ao[6]|0,this.H=ao[7]|0}get(){let{A:e,B:t,C:n,D:o,E:s,F:i,G:u,H:f}=this;return[e,t,n,o,s,i,u,f]}set(e,t,n,o,s,i,u,f){this.A=e|0,this.B=t|0,this.C=n|0,this.D=o|0,this.E=s|0,this.F=i|0,this.G=u|0,this.H=f|0}process(e,t){for(let E=0;E<16;E++,t+=4)co[E]=e.getUint32(t,!1);for(let E=16;E<64;E++){let q=co[E-15],C=co[E-2],K=Fr(q,7)^Fr(q,18)^q>>>3,ee=Fr(C,17)^Fr(C,19)^C>>>10;co[E]=ee+co[E-7]+K+co[E-16]|0}let{A:n,B:o,C:s,D:i,E:u,F:f,G:g,H:b}=this;for(let E=0;E<64;E++){let q=Fr(u,6)^Fr(u,11)^Fr(u,25),C=b+q+BM(u,f,g)+TM[E]+co[E]|0,ee=(Fr(n,2)^Fr(n,13)^Fr(n,22))+PM(n,o,s)|0;b=g,g=f,f=u,u=i+C|0,i=s,s=o,o=n,n=C+ee|0}n=n+this.A|0,o=o+this.B|0,s=s+this.C|0,i=i+this.D|0,u=u+this.E|0,f=f+this.F|0,g=g+this.G|0,b=b+this.H|0,this.set(n,o,s,i,u,f,g,b)}roundClean(){co.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}},dh=class extends Wc{constructor(){super(),this.A=-1056596264,this.B=914150663,this.C=812702999,this.D=-150054599,this.E=-4191439,this.F=1750603025,this.G=1694076839,this.H=-1090891868,this.outputLen=28}},Kc=an(()=>new Wc),DV=an(()=>new dh)});var eE=_e(gh=>{"use strict";y();function In(r,e,t){return e<=r&&r<=t}function $c(r){if(r===void 0)return{};if(r===Object(r))return r;throw TypeError("Could not convert argument to dictionary")}function zM(r){for(var e=String(r),t=e.length,n=0,o=[];n<t;){var s=e.charCodeAt(n);if(s<55296||s>57343)o.push(s);else if(56320<=s&&s<=57343)o.push(65533);else if(55296<=s&&s<=56319)if(n===t-1)o.push(65533);else{var i=r.charCodeAt(n+1);if(56320<=i&&i<=57343){var u=s&1023,f=i&1023;o.push(65536+(u<<10)+f),n+=1}else o.push(65533)}n+=1}return o}function MM(r){for(var e="",t=0;t<r.length;++t){var n=r[t];n<=65535?e+=String.fromCharCode(n):(n-=65536,e+=String.fromCharCode((n>>10)+55296,(n&1023)+56320))}return e}var Vc=-1;function mh(r){this.tokens=[].slice.call(r)}mh.prototype={endOfStream:function(){return!this.tokens.length},read:function(){return this.tokens.length?this.tokens.shift():Vc},prepend:function(r){if(Array.isArray(r))for(var e=r;e.length;)this.tokens.unshift(e.pop());else this.tokens.unshift(r)},push:function(r){if(Array.isArray(r))for(var e=r;e.length;)this.tokens.push(e.shift());else this.tokens.push(r)}};var Xs=-1;function yh(r,e){if(r)throw TypeError("Decoder error");return e||65533}var Gc="utf-8";function Zc(r,e){if(!(this instanceof Zc))return new Zc(r,e);if(r=r!==void 0?String(r).toLowerCase():Gc,r!==Gc)throw new Error("Encoding not supported. Only utf-8 is supported");e=$c(e),this._streaming=!1,this._BOMseen=!1,this._decoder=null,this._fatal=!!e.fatal,this._ignoreBOM=!!e.ignoreBOM,Object.defineProperty(this,"encoding",{value:"utf-8"}),Object.defineProperty(this,"fatal",{value:this._fatal}),Object.defineProperty(this,"ignoreBOM",{value:this._ignoreBOM})}Zc.prototype={decode:function(e,t){var n;typeof e=="object"&&e instanceof ArrayBuffer?n=new Uint8Array(e):typeof e=="object"&&"buffer"in e&&e.buffer instanceof ArrayBuffer?n=new Uint8Array(e.buffer,e.byteOffset,e.byteLength):n=new Uint8Array(0),t=$c(t),this._streaming||(this._decoder=new LM({fatal:this._fatal}),this._BOMseen=!1),this._streaming=!!t.stream;for(var o=new mh(n),s=[],i;!o.endOfStream()&&(i=this._decoder.handler(o,o.read()),i!==Xs);)i!==null&&(Array.isArray(i)?s.push.apply(s,i):s.push(i));if(!this._streaming){do{if(i=this._decoder.handler(o,o.read()),i===Xs)break;i!==null&&(Array.isArray(i)?s.push.apply(s,i):s.push(i))}while(!o.endOfStream());this._decoder=null}return s.length&&["utf-8"].indexOf(this.encoding)!==-1&&!this._ignoreBOM&&!this._BOMseen&&(s[0]===65279?(this._BOMseen=!0,s.shift()):this._BOMseen=!0),MM(s)}};function Yc(r,e){if(!(this instanceof Yc))return new Yc(r,e);if(r=r!==void 0?String(r).toLowerCase():Gc,r!==Gc)throw new Error("Encoding not supported. Only utf-8 is supported");e=$c(e),this._streaming=!1,this._encoder=null,this._options={fatal:!!e.fatal},Object.defineProperty(this,"encoding",{value:"utf-8"})}Yc.prototype={encode:function(e,t){e=e?String(e):"",t=$c(t),this._streaming||(this._encoder=new NM(this._options)),this._streaming=!!t.stream;for(var n=[],o=new mh(zM(e)),s;!o.endOfStream()&&(s=this._encoder.handler(o,o.read()),s!==Xs);)Array.isArray(s)?n.push.apply(n,s):n.push(s);if(!this._streaming){for(;s=this._encoder.handler(o,o.read()),s!==Xs;)Array.isArray(s)?n.push.apply(n,s):n.push(s);this._encoder=null}return new Uint8Array(n)}};function LM(r){var e=r.fatal,t=0,n=0,o=0,s=128,i=191;this.handler=function(u,f){if(f===Vc&&o!==0)return o=0,yh(e);if(f===Vc)return Xs;if(o===0){if(In(f,0,127))return f;if(In(f,194,223))o=1,t=f-192;else if(In(f,224,239))f===224&&(s=160),f===237&&(i=159),o=2,t=f-224;else if(In(f,240,244))f===240&&(s=144),f===244&&(i=143),o=3,t=f-240;else return yh(e);return t=t<<6*o,null}if(!In(f,s,i))return t=o=n=0,s=128,i=191,u.prepend(f),yh(e);if(s=128,i=191,n+=1,t+=f-128<<6*(o-n),n!==o)return null;var g=t;return t=o=n=0,g}}function NM(r){var e=r.fatal;this.handler=function(t,n){if(n===Vc)return Xs;if(In(n,0,127))return n;var o,s;In(n,128,2047)?(o=1,s=192):In(n,2048,65535)?(o=2,s=224):In(n,65536,1114111)&&(o=3,s=240);for(var i=[(n>>6*o)+s];o>0;){var u=n>>6*(o-1);i.push(128|u&63),o-=1}return i}}gh.TextEncoder=Yc;gh.TextDecoder=Zc});var sE=_e(Je=>{"use strict";y();var CM=Je&&Je.__createBinding||(Object.create?function(r,e,t,n){n===void 0&&(n=t),Object.defineProperty(r,n,{enumerable:!0,get:function(){return e[t]}})}:function(r,e,t,n){n===void 0&&(n=t),r[n]=e[t]}),OM=Je&&Je.__setModuleDefault||(Object.create?function(r,e){Object.defineProperty(r,"default",{enumerable:!0,value:e})}:function(r,e){r.default=e}),un=Je&&Je.__decorate||function(r,e,t,n){var o=arguments.length,s=o<3?e:n===null?n=Object.getOwnPropertyDescriptor(e,t):n,i;if(typeof Reflect=="object"&&typeof Reflect.decorate=="function")s=Reflect.decorate(r,e,t,n);else for(var u=r.length-1;u>=0;u--)(i=r[u])&&(s=(o<3?i(s):o>3?i(e,t,s):i(e,t))||s);return o>3&&s&&Object.defineProperty(e,t,s),s},qM=Je&&Je.__importStar||function(r){if(r&&r.__esModule)return r;var e={};if(r!=null)for(var t in r)t!=="default"&&Object.hasOwnProperty.call(r,t)&&CM(e,r,t);return OM(e,r),e},tE=Je&&Je.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(Je,"__esModule",{value:!0});Je.deserializeUnchecked=Je.deserialize=Je.serialize=Je.BinaryReader=Je.BinaryWriter=Je.BorshError=Je.baseDecode=Je.baseEncode=void 0;var uo=tE(Wa()),rE=tE(Zn()),UM=qM(eE()),DM=typeof TextDecoder!="function"?UM.TextDecoder:TextDecoder,FM=new DM("utf-8",{fatal:!0});function jM(r){return typeof r=="string"&&(r=I.from(r,"utf8")),rE.default.encode(I.from(r))}Je.baseEncode=jM;function HM(r){return I.from(rE.default.decode(r))}Je.baseDecode=HM;var xh=1024,$t=class extends Error{constructor(e){super(e),this.fieldPath=[],this.originalMessage=e}addToFieldPath(e){this.fieldPath.splice(0,0,e),this.message=this.originalMessage+": "+this.fieldPath.join(".")}};Je.BorshError=$t;var Jc=class{constructor(){this.buf=I.alloc(xh),this.length=0}maybeResize(){this.buf.length<16+this.length&&(this.buf=I.concat([this.buf,I.alloc(xh)]))}writeU8(e){this.maybeResize(),this.buf.writeUInt8(e,this.length),this.length+=1}writeU16(e){this.maybeResize(),this.buf.writeUInt16LE(e,this.length),this.length+=2}writeU32(e){this.maybeResize(),this.buf.writeUInt32LE(e,this.length),this.length+=4}writeU64(e){this.maybeResize(),this.writeBuffer(I.from(new uo.default(e).toArray("le",8)))}writeU128(e){this.maybeResize(),this.writeBuffer(I.from(new uo.default(e).toArray("le",16)))}writeU256(e){this.maybeResize(),this.writeBuffer(I.from(new uo.default(e).toArray("le",32)))}writeU512(e){this.maybeResize(),this.writeBuffer(I.from(new uo.default(e).toArray("le",64)))}writeBuffer(e){this.buf=I.concat([I.from(this.buf.subarray(0,this.length)),e,I.alloc(xh)]),this.length+=e.length}writeString(e){this.maybeResize();let t=I.from(e,"utf8");this.writeU32(t.length),this.writeBuffer(t)}writeFixedArray(e){this.writeBuffer(I.from(e))}writeArray(e,t){this.maybeResize(),this.writeU32(e.length);for(let n of e)this.maybeResize(),t(n)}toArray(){return this.buf.subarray(0,this.length)}};Je.BinaryWriter=Jc;function pn(r,e,t){let n=t.value;t.value=function(...o){try{return n.apply(this,o)}catch(s){if(s instanceof RangeError){let i=s.code;if(["ERR_BUFFER_OUT_OF_BOUNDS","ERR_OUT_OF_RANGE"].indexOf(i)>=0)throw new $t("Reached the end of buffer when deserializing")}throw s}}}var pr=class{constructor(e){this.buf=e,this.offset=0}readU8(){let e=this.buf.readUInt8(this.offset);return this.offset+=1,e}readU16(){let e=this.buf.readUInt16LE(this.offset);return this.offset+=2,e}readU32(){let e=this.buf.readUInt32LE(this.offset);return this.offset+=4,e}readU64(){let e=this.readBuffer(8);return new uo.default(e,"le")}readU128(){let e=this.readBuffer(16);return new uo.default(e,"le")}readU256(){let e=this.readBuffer(32);return new uo.default(e,"le")}readU512(){let e=this.readBuffer(64);return new uo.default(e,"le")}readBuffer(e){if(this.offset+e>this.buf.length)throw new $t(`Expected buffer length ${e} isn't within bounds`);let t=this.buf.slice(this.offset,this.offset+e);return this.offset+=e,t}readString(){let e=this.readU32(),t=this.readBuffer(e);try{return FM.decode(t)}catch(n){throw new $t(`Error decoding UTF-8 string: ${n}`)}}readFixedArray(e){return new Uint8Array(this.readBuffer(e))}readArray(e){let t=this.readU32(),n=Array();for(let o=0;o<t;++o)n.push(e());return n}};un([pn],pr.prototype,"readU8",null);un([pn],pr.prototype,"readU16",null);un([pn],pr.prototype,"readU32",null);un([pn],pr.prototype,"readU64",null);un([pn],pr.prototype,"readU128",null);un([pn],pr.prototype,"readU256",null);un([pn],pr.prototype,"readU512",null);un([pn],pr.prototype,"readString",null);un([pn],pr.prototype,"readFixedArray",null);un([pn],pr.prototype,"readArray",null);Je.BinaryReader=pr;function nE(r){return r.charAt(0).toUpperCase()+r.slice(1)}function cs(r,e,t,n,o){try{if(typeof n=="string")o[`write${nE(n)}`](t);else if(n instanceof Array)if(typeof n[0]=="number"){if(t.length!==n[0])throw new $t(`Expecting byte array of length ${n[0]}, but got ${t.length} bytes`);o.writeFixedArray(t)}else if(n.length===2&&typeof n[1]=="number"){if(t.length!==n[1])throw new $t(`Expecting byte array of length ${n[1]}, but got ${t.length} bytes`);for(let s=0;s<n[1];s++)cs(r,null,t[s],n[0],o)}else o.writeArray(t,s=>{cs(r,e,s,n[0],o)});else if(n.kind!==void 0)switch(n.kind){case"option":{t==null?o.writeU8(0):(o.writeU8(1),cs(r,e,t,n.type,o));break}case"map":{o.writeU32(t.size),t.forEach((s,i)=>{cs(r,e,i,n.key,o),cs(r,e,s,n.value,o)});break}default:throw new $t(`FieldType ${n} unrecognized`)}else oE(r,t,o)}catch(s){throw s instanceof $t&&s.addToFieldPath(e),s}}function oE(r,e,t){if(typeof e.borshSerialize=="function"){e.borshSerialize(t);return}let n=r.get(e.constructor);if(!n)throw new $t(`Class ${e.constructor.name} is missing in schema`);if(n.kind==="struct")n.fields.map(([o,s])=>{cs(r,o,e[o],s,t)});else if(n.kind==="enum"){let o=e[n.field];for(let s=0;s<n.values.length;++s){let[i,u]=n.values[s];if(i===o){t.writeU8(s),cs(r,i,e[i],u,t);break}}}else throw new $t(`Unexpected schema kind: ${n.kind} for ${e.constructor.name}`)}function WM(r,e,t=Jc){let n=new t;return oE(r,e,n),n.toArray()}Je.serialize=WM;function us(r,e,t,n){try{if(typeof t=="string")return n[`read${nE(t)}`]();if(t instanceof Array){if(typeof t[0]=="number")return n.readFixedArray(t[0]);if(typeof t[1]=="number"){let o=[];for(let s=0;s<t[1];s++)o.push(us(r,null,t[0],n));return o}else return n.readArray(()=>us(r,e,t[0],n))}if(t.kind==="option")return n.readU8()?us(r,e,t.type,n):void 0;if(t.kind==="map"){let o=new Map,s=n.readU32();for(let i=0;i<s;i++){let u=us(r,e,t.key,n),f=us(r,e,t.value,n);o.set(u,f)}return o}return wh(r,t,n)}catch(o){throw o instanceof $t&&o.addToFieldPath(e),o}}function wh(r,e,t){if(typeof e.borshDeserialize=="function")return e.borshDeserialize(t);let n=r.get(e);if(!n)throw new $t(`Class ${e.name} is missing in schema`);if(n.kind==="struct"){let o={};for(let[s,i]of r.get(e).fields)o[s]=us(r,s,i,t);return new e(o)}if(n.kind==="enum"){let o=t.readU8();if(o>=n.values.length)throw new $t(`Enum index: ${o} is out of range`);let[s,i]=n.values[o],u=us(r,s,i,t);return new e({[s]:u})}throw new $t(`Unexpected schema kind: ${n.kind} for ${e.constructor.name}`)}function KM(r,e,t,n=pr){let o=new n(t),s=wh(r,e,o);if(o.offset<t.length)throw new $t(`Unexpected ${t.length-o.offset} bytes after deserialized data`);return s}Je.deserialize=KM;function VM(r,e,t,n=pr){let o=new n(t);return wh(r,e,o)}Je.deserializeUnchecked=VM});var bh=_e(ei=>{"use strict";y();Object.defineProperty(ei,"__esModule",{value:!0});var Qc;function GM(r){{let e=I.from(r);e.reverse();let t=e.toString("hex");return t.length===0?BigInt(0):BigInt(`0x${t}`)}return Qc.toBigInt(r,!1)}ei.toBigIntLE=GM;function ZM(r){{let e=r.toString("hex");return e.length===0?BigInt(0):BigInt(`0x${e}`)}return Qc.toBigInt(r,!0)}ei.toBigIntBE=ZM;function YM(r,e){{let t=r.toString(16),n=I.from(t.padStart(e*2,"0").slice(0,e*2),"hex");return n.reverse(),n}return Qc.fromBigInt(r,I.allocUnsafe(e),!1)}ei.toBufferLE=YM;function $M(r,e){{let t=r.toString(16);return I.from(t.padStart(e*2,"0").slice(0,e*2),"hex")}return Qc.fromBigInt(r,I.allocUnsafe(e),!0)}ei.toBufferBE=$M});function JM(r){return po(r)&&typeof r[Symbol.iterator]=="function"}function po(r){return typeof r=="object"&&r!=null}function jr(r){return typeof r=="string"?JSON.stringify(r):""+r}function QM(r){let{done:e,value:t}=r.next();return e?void 0:t}function XM(r,e,t,n){if(r===!0)return;r===!1?r={}:typeof r=="string"&&(r={message:r});let{path:o,branch:s}=e,{type:i}=t,{refinement:u,message:f="Expected a value of type `"+i+"`"+(u?" with refinement `"+u+"`":"")+", but received: `"+jr(n)+"`"}=r;return{value:n,type:i,refinement:u,key:o[o.length-1],path:o,branch:s,...r,message:f}}function*iE(r,e,t,n){JM(r)||(r=[r]);for(let o of r){let s=XM(o,e,t,n);s&&(yield s)}}function*Sh(r,e,t={}){let{path:n=[],branch:o=[r],coerce:s=!1,mask:i=!1}=t,u={path:n,branch:o};if(s&&(r=e.coercer(r,u),i&&e.type!=="type"&&po(e.schema)&&po(r)&&!Array.isArray(r)))for(let g in r)e.schema[g]===void 0&&delete r[g];let f=!0;for(let g of e.validator(r,u))f=!1,yield[g,void 0];for(let[g,b,E]of e.entries(r,u)){let q=Sh(b,E,{path:g===void 0?n:[...n,g],branch:g===void 0?o:[...o,b],coerce:s,mask:i});for(let C of q)C[0]?(f=!1,yield[C[0],void 0]):s&&(b=C[1],g===void 0?r=b:r instanceof Map?r.set(g,b):r instanceof Set?r.add(b):po(r)&&(r[g]=b))}if(f)for(let g of e.refiner(r,u))f=!1,yield[g,void 0];f&&(yield[void 0,r])}function aE(r,e){let t=Xi(r,e);if(t[0])throw t[0]}function ti(r,e){let t=Xi(r,e,{coerce:!0});if(t[0])throw t[0];return t[1]}function eL(r,e){let t=Xi(r,e,{coerce:!0,mask:!0});if(t[0])throw t[0];return t[1]}function cE(r,e){return!Xi(r,e)[0]}function Xi(r,e,t={}){let n=Sh(r,e,t),o=QM(n);return o[0]?[new vh(o[0],function*(){for(let i of n)i[0]&&(yield i[0])}),void 0]:[void 0,o[1]]}function ps(r,e){return new Nr({type:r,schema:null,validator:e})}function uE(){return ps("any",()=>!0)}function xe(r){return new Nr({type:"array",schema:r,*entries(e){if(r&&Array.isArray(e))for(let[t,n]of e.entries())yield[t,n,r]},coercer(e){return Array.isArray(e)?e.slice():e},validator(e){return Array.isArray(e)||"Expected an array value, but received: "+jr(e)}})}function fn(){return ps("boolean",r=>typeof r=="boolean")}function Xc(r){return ps("instance",e=>e instanceof r||"Expected a `"+r.name+"` instance, but received: "+jr(e))}function qt(r){let e=jr(r),t=typeof r;return new Nr({type:"literal",schema:t==="string"||t==="number"||t==="boolean"?r:null,validator(n){return n===r||"Expected the literal `"+e+"`, but received: "+jr(n)}})}function tL(){return ps("never",()=>!1)}function ye(r){return new Nr({...r,validator:(e,t)=>e===null||r.validator(e,t),refiner:(e,t)=>e===null||r.refiner(e,t)})}function H(){return ps("number",r=>typeof r=="number"&&!isNaN(r)||"Expected a number, but received: "+jr(r))}function ke(r){return new Nr({...r,validator:(e,t)=>e===void 0||r.validator(e,t),refiner:(e,t)=>e===void 0||r.refiner(e,t)})}function Eh(r,e){return new Nr({type:"record",schema:null,*entries(t){if(po(t))for(let n in t){let o=t[n];yield[n,n,r],yield[n,o,e]}},validator(t){return po(t)||"Expected an object, but received: "+jr(t)}})}function pe(){return ps("string",r=>typeof r=="string"||"Expected a string, but received: "+jr(r))}function eu(r){let e=tL();return new Nr({type:"tuple",schema:null,*entries(t){if(Array.isArray(t)){let n=Math.max(r.length,t.length);for(let o=0;o<n;o++)yield[o,t[o],r[o]||e]}},validator(t){return Array.isArray(t)||"Expected an array, but received: "+jr(t)}})}function re(r){let e=Object.keys(r);return new Nr({type:"type",schema:r,*entries(t){if(po(t))for(let n of e)yield[n,t[n],r[n]]},validator(t){return po(t)||"Expected an object, but received: "+jr(t)}})}function fr(r){let e=r.map(t=>t.type).join(" | ");return new Nr({type:"union",schema:null,validator(t,n){let o=[];for(let s of r){let[...i]=Sh(t,s,n),[u]=i;if(u[0])for(let[f]of i)f&&o.push(f);else return[]}return["Expected the value to satisfy a union of `"+e+"`, but received: "+jr(t),...o]}})}function ri(){return ps("unknown",()=>!0)}function ni(r,e,t){return new Nr({...r,coercer:(n,o)=>cE(n,e)?r.coercer(t(n,o),o):r.coercer(n,o)})}var vh,Nr,pE=O(()=>{y();vh=class extends TypeError{constructor(e,t){let n,{message:o,...s}=e,{path:i}=e,u=i.length===0?o:"At path: "+i.join(".")+" -- "+o;super(u),Object.assign(this,s),this.name=this.constructor.name,this.failures=()=>{var f;return(f=n)!=null?f:n=[e,...t()]}}};Nr=class{constructor(e){let{type:t,schema:n,validator:o,refiner:s,coercer:i=f=>f,entries:u=function*(){}}=e;this.type=t,this.schema=n,this.entries=u,this.coercer=i,o?this.validator=(f,g)=>{let b=o(f,g);return iE(b,g,this,f)}:this.validator=()=>[],s?this.refiner=(f,g)=>{let b=s(f,g);return iE(b,g,this,f)}:this.refiner=()=>[]}assert(e){return aE(e,this)}create(e){return ti(e,this)}is(e){return cE(e,this)}mask(e){return eL(e,this)}validate(e,t={}){return Xi(e,this,t)}}});var kh=_e(_h=>{"use strict";y();Object.defineProperty(_h,"__esModule",{value:!0});_h.default=nL;var tu,rL=new Uint8Array(16);function nL(){if(!tu&&(tu=typeof crypto<"u"&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto),!tu))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return tu(rL)}});var fE=_e(ru=>{"use strict";y();Object.defineProperty(ru,"__esModule",{value:!0});ru.default=void 0;var oL=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;ru.default=oL});var ea=_e(nu=>{"use strict";y();Object.defineProperty(nu,"__esModule",{value:!0});nu.default=void 0;var sL=iL(fE());function iL(r){return r&&r.__esModule?r:{default:r}}function aL(r){return typeof r=="string"&&sL.default.test(r)}var cL=aL;nu.default=cL});var ra=_e(ta=>{"use strict";y();Object.defineProperty(ta,"__esModule",{value:!0});ta.default=void 0;ta.unsafeStringify=lE;var uL=pL(ea());function pL(r){return r&&r.__esModule?r:{default:r}}var Jt=[];for(let r=0;r<256;++r)Jt.push((r+256).toString(16).slice(1));function lE(r,e=0){return Jt[r[e+0]]+Jt[r[e+1]]+Jt[r[e+2]]+Jt[r[e+3]]+"-"+Jt[r[e+4]]+Jt[r[e+5]]+"-"+Jt[r[e+6]]+Jt[r[e+7]]+"-"+Jt[r[e+8]]+Jt[r[e+9]]+"-"+Jt[r[e+10]]+Jt[r[e+11]]+Jt[r[e+12]]+Jt[r[e+13]]+Jt[r[e+14]]+Jt[r[e+15]]}function fL(r,e=0){let t=lE(r,e);if(!(0,uL.default)(t))throw TypeError("Stringified UUID is invalid");return t}var lL=fL;ta.default=lL});var dE=_e(ou=>{"use strict";y();Object.defineProperty(ou,"__esModule",{value:!0});ou.default=void 0;var hL=yL(kh()),dL=ra();function yL(r){return r&&r.__esModule?r:{default:r}}var hE,Ah,Ih=0,Rh=0;function mL(r,e,t){let n=e&&t||0,o=e||new Array(16);r=r||{};let s=r.node||hE,i=r.clockseq!==void 0?r.clockseq:Ah;if(s==null||i==null){let q=r.random||(r.rng||hL.default)();s==null&&(s=hE=[q[0]|1,q[1],q[2],q[3],q[4],q[5]]),i==null&&(i=Ah=(q[6]<<8|q[7])&16383)}let u=r.msecs!==void 0?r.msecs:Date.now(),f=r.nsecs!==void 0?r.nsecs:Rh+1,g=u-Ih+(f-Rh)/1e4;if(g<0&&r.clockseq===void 0&&(i=i+1&16383),(g<0||u>Ih)&&r.nsecs===void 0&&(f=0),f>=1e4)throw new Error("uuid.v1(): Can't create more than 10M uuids/sec");Ih=u,Rh=f,Ah=i,u+=122192928e5;let b=((u&268435455)*1e4+f)%4294967296;o[n++]=b>>>24&255,o[n++]=b>>>16&255,o[n++]=b>>>8&255,o[n++]=b&255;let E=u/4294967296*1e4&268435455;o[n++]=E>>>8&255,o[n++]=E&255,o[n++]=E>>>24&15|16,o[n++]=E>>>16&255,o[n++]=i>>>8|128,o[n++]=i&255;for(let q=0;q<6;++q)o[n+q]=s[q];return e||(0,dL.unsafeStringify)(o)}var gL=mL;ou.default=gL});var Bh=_e(su=>{"use strict";y();Object.defineProperty(su,"__esModule",{value:!0});su.default=void 0;var xL=wL(ea());function wL(r){return r&&r.__esModule?r:{default:r}}function bL(r){if(!(0,xL.default)(r))throw TypeError("Invalid UUID");let e,t=new Uint8Array(16);return t[0]=(e=parseInt(r.slice(0,8),16))>>>24,t[1]=e>>>16&255,t[2]=e>>>8&255,t[3]=e&255,t[4]=(e=parseInt(r.slice(9,13),16))>>>8,t[5]=e&255,t[6]=(e=parseInt(r.slice(14,18),16))>>>8,t[7]=e&255,t[8]=(e=parseInt(r.slice(19,23),16))>>>8,t[9]=e&255,t[10]=(e=parseInt(r.slice(24,36),16))/1099511627776&255,t[11]=e/4294967296&255,t[12]=e>>>24&255,t[13]=e>>>16&255,t[14]=e>>>8&255,t[15]=e&255,t}var vL=bL;su.default=vL});var Ph=_e(fs=>{"use strict";y();Object.defineProperty(fs,"__esModule",{value:!0});fs.URL=fs.DNS=void 0;fs.default=AL;var SL=ra(),EL=_L(Bh());function _L(r){return r&&r.__esModule?r:{default:r}}function kL(r){r=unescape(encodeURIComponent(r));let e=[];for(let t=0;t<r.length;++t)e.push(r.charCodeAt(t));return e}var yE="6ba7b810-9dad-11d1-80b4-00c04fd430c8";fs.DNS=yE;var mE="6ba7b811-9dad-11d1-80b4-00c04fd430c8";fs.URL=mE;function AL(r,e,t){function n(o,s,i,u){var f;if(typeof o=="string"&&(o=kL(o)),typeof s=="string"&&(s=(0,EL.default)(s)),((f=s)===null||f===void 0?void 0:f.length)!==16)throw TypeError("Namespace must be array-like (16 iterable integer values, 0-255)");let g=new Uint8Array(16+o.length);if(g.set(s),g.set(o,s.length),g=t(g),g[6]=g[6]&15|e,g[8]=g[8]&63|128,i){u=u||0;for(let b=0;b<16;++b)i[u+b]=g[b];return i}return(0,SL.unsafeStringify)(g)}try{n.name=r}catch{}return n.DNS=yE,n.URL=mE,n}});var xE=_e(au=>{"use strict";y();Object.defineProperty(au,"__esModule",{value:!0});au.default=void 0;function IL(r){if(typeof r=="string"){let e=unescape(encodeURIComponent(r));r=new Uint8Array(e.length);for(let t=0;t<e.length;++t)r[t]=e.charCodeAt(t)}return RL(BL(PL(r),r.length*8))}function RL(r){let e=[],t=r.length*32,n="0123456789abcdef";for(let o=0;o<t;o+=8){let s=r[o>>5]>>>o%32&255,i=parseInt(n.charAt(s>>>4&15)+n.charAt(s&15),16);e.push(i)}return e}function gE(r){return(r+64>>>9<<4)+14+1}function BL(r,e){r[e>>5]|=128<<e%32,r[gE(e)-1]=e;let t=1732584193,n=-271733879,o=-1732584194,s=271733878;for(let i=0;i<r.length;i+=16){let u=t,f=n,g=o,b=s;t=nr(t,n,o,s,r[i],7,-680876936),s=nr(s,t,n,o,r[i+1],12,-389564586),o=nr(o,s,t,n,r[i+2],17,606105819),n=nr(n,o,s,t,r[i+3],22,-1044525330),t=nr(t,n,o,s,r[i+4],7,-176418897),s=nr(s,t,n,o,r[i+5],12,1200080426),o=nr(o,s,t,n,r[i+6],17,-1473231341),n=nr(n,o,s,t,r[i+7],22,-45705983),t=nr(t,n,o,s,r[i+8],7,1770035416),s=nr(s,t,n,o,r[i+9],12,-1958414417),o=nr(o,s,t,n,r[i+10],17,-42063),n=nr(n,o,s,t,r[i+11],22,-1990404162),t=nr(t,n,o,s,r[i+12],7,1804603682),s=nr(s,t,n,o,r[i+13],12,-40341101),o=nr(o,s,t,n,r[i+14],17,-1502002290),n=nr(n,o,s,t,r[i+15],22,1236535329),t=or(t,n,o,s,r[i+1],5,-165796510),s=or(s,t,n,o,r[i+6],9,-1069501632),o=or(o,s,t,n,r[i+11],14,643717713),n=or(n,o,s,t,r[i],20,-373897302),t=or(t,n,o,s,r[i+5],5,-701558691),s=or(s,t,n,o,r[i+10],9,38016083),o=or(o,s,t,n,r[i+15],14,-660478335),n=or(n,o,s,t,r[i+4],20,-405537848),t=or(t,n,o,s,r[i+9],5,568446438),s=or(s,t,n,o,r[i+14],9,-1019803690),o=or(o,s,t,n,r[i+3],14,-187363961),n=or(n,o,s,t,r[i+8],20,1163531501),t=or(t,n,o,s,r[i+13],5,-1444681467),s=or(s,t,n,o,r[i+2],9,-51403784),o=or(o,s,t,n,r[i+7],14,1735328473),n=or(n,o,s,t,r[i+12],20,-1926607734),t=sr(t,n,o,s,r[i+5],4,-378558),s=sr(s,t,n,o,r[i+8],11,-2022574463),o=sr(o,s,t,n,r[i+11],16,1839030562),n=sr(n,o,s,t,r[i+14],23,-35309556),t=sr(t,n,o,s,r[i+1],4,-1530992060),s=sr(s,t,n,o,r[i+4],11,1272893353),o=sr(o,s,t,n,r[i+7],16,-155497632),n=sr(n,o,s,t,r[i+10],23,-1094730640),t=sr(t,n,o,s,r[i+13],4,681279174),s=sr(s,t,n,o,r[i],11,-358537222),o=sr(o,s,t,n,r[i+3],16,-722521979),n=sr(n,o,s,t,r[i+6],23,76029189),t=sr(t,n,o,s,r[i+9],4,-640364487),s=sr(s,t,n,o,r[i+12],11,-421815835),o=sr(o,s,t,n,r[i+15],16,530742520),n=sr(n,o,s,t,r[i+2],23,-995338651),t=ir(t,n,o,s,r[i],6,-198630844),s=ir(s,t,n,o,r[i+7],10,1126891415),o=ir(o,s,t,n,r[i+14],15,-1416354905),n=ir(n,o,s,t,r[i+5],21,-57434055),t=ir(t,n,o,s,r[i+12],6,1700485571),s=ir(s,t,n,o,r[i+3],10,-1894986606),o=ir(o,s,t,n,r[i+10],15,-1051523),n=ir(n,o,s,t,r[i+1],21,-2054922799),t=ir(t,n,o,s,r[i+8],6,1873313359),s=ir(s,t,n,o,r[i+15],10,-30611744),o=ir(o,s,t,n,r[i+6],15,-1560198380),n=ir(n,o,s,t,r[i+13],21,1309151649),t=ir(t,n,o,s,r[i+4],6,-145523070),s=ir(s,t,n,o,r[i+11],10,-1120210379),o=ir(o,s,t,n,r[i+2],15,718787259),n=ir(n,o,s,t,r[i+9],21,-343485551),t=fo(t,u),n=fo(n,f),o=fo(o,g),s=fo(s,b)}return[t,n,o,s]}function PL(r){if(r.length===0)return[];let e=r.length*8,t=new Uint32Array(gE(e));for(let n=0;n<e;n+=8)t[n>>5]|=(r[n/8]&255)<<n%32;return t}function fo(r,e){let t=(r&65535)+(e&65535);return(r>>16)+(e>>16)+(t>>16)<<16|t&65535}function TL(r,e){return r<<e|r>>>32-e}function iu(r,e,t,n,o,s){return fo(TL(fo(fo(e,r),fo(n,s)),o),t)}function nr(r,e,t,n,o,s,i){return iu(e&t|~e&n,r,e,o,s,i)}function or(r,e,t,n,o,s,i){return iu(e&n|t&~n,r,e,o,s,i)}function sr(r,e,t,n,o,s,i){return iu(e^t^n,r,e,o,s,i)}function ir(r,e,t,n,o,s,i){return iu(t^(e|~n),r,e,o,s,i)}var zL=IL;au.default=zL});var bE=_e(cu=>{"use strict";y();Object.defineProperty(cu,"__esModule",{value:!0});cu.default=void 0;var ML=wE(Ph()),LL=wE(xE());function wE(r){return r&&r.__esModule?r:{default:r}}var NL=(0,ML.default)("v3",48,LL.default),CL=NL;cu.default=CL});var vE=_e(uu=>{"use strict";y();Object.defineProperty(uu,"__esModule",{value:!0});uu.default=void 0;var OL=typeof crypto<"u"&&crypto.randomUUID&&crypto.randomUUID.bind(crypto),qL={randomUUID:OL};uu.default=qL});var _E=_e(pu=>{"use strict";y();Object.defineProperty(pu,"__esModule",{value:!0});pu.default=void 0;var SE=EE(vE()),UL=EE(kh()),DL=ra();function EE(r){return r&&r.__esModule?r:{default:r}}function FL(r,e,t){if(SE.default.randomUUID&&!e&&!r)return SE.default.randomUUID();r=r||{};let n=r.random||(r.rng||UL.default)();if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,e){t=t||0;for(let o=0;o<16;++o)e[t+o]=n[o];return e}return(0,DL.unsafeStringify)(n)}var jL=FL;pu.default=jL});var kE=_e(fu=>{"use strict";y();Object.defineProperty(fu,"__esModule",{value:!0});fu.default=void 0;function HL(r,e,t,n){switch(r){case 0:return e&t^~e&n;case 1:return e^t^n;case 2:return e&t^e&n^t&n;case 3:return e^t^n}}function Th(r,e){return r<<e|r>>>32-e}function WL(r){let e=[1518500249,1859775393,2400959708,3395469782],t=[1732584193,4023233417,2562383102,271733878,3285377520];if(typeof r=="string"){let i=unescape(encodeURIComponent(r));r=[];for(let u=0;u<i.length;++u)r.push(i.charCodeAt(u))}else Array.isArray(r)||(r=Array.prototype.slice.call(r));r.push(128);let n=r.length/4+2,o=Math.ceil(n/16),s=new Array(o);for(let i=0;i<o;++i){let u=new Uint32Array(16);for(let f=0;f<16;++f)u[f]=r[i*64+f*4]<<24|r[i*64+f*4+1]<<16|r[i*64+f*4+2]<<8|r[i*64+f*4+3];s[i]=u}s[o-1][14]=(r.length-1)*8/Math.pow(2,32),s[o-1][14]=Math.floor(s[o-1][14]),s[o-1][15]=(r.length-1)*8&4294967295;for(let i=0;i<o;++i){let u=new Uint32Array(80);for(let C=0;C<16;++C)u[C]=s[i][C];for(let C=16;C<80;++C)u[C]=Th(u[C-3]^u[C-8]^u[C-14]^u[C-16],1);let f=t[0],g=t[1],b=t[2],E=t[3],q=t[4];for(let C=0;C<80;++C){let K=Math.floor(C/20),ee=Th(f,5)+HL(K,g,b,E)+q+e[K]+u[C]>>>0;q=E,E=b,b=Th(g,30)>>>0,g=f,f=ee}t[0]=t[0]+f>>>0,t[1]=t[1]+g>>>0,t[2]=t[2]+b>>>0,t[3]=t[3]+E>>>0,t[4]=t[4]+q>>>0}return[t[0]>>24&255,t[0]>>16&255,t[0]>>8&255,t[0]&255,t[1]>>24&255,t[1]>>16&255,t[1]>>8&255,t[1]&255,t[2]>>24&255,t[2]>>16&255,t[2]>>8&255,t[2]&255,t[3]>>24&255,t[3]>>16&255,t[3]>>8&255,t[3]&255,t[4]>>24&255,t[4]>>16&255,t[4]>>8&255,t[4]&255]}var KL=WL;fu.default=KL});var IE=_e(lu=>{"use strict";y();Object.defineProperty(lu,"__esModule",{value:!0});lu.default=void 0;var VL=AE(Ph()),GL=AE(kE());function AE(r){return r&&r.__esModule?r:{default:r}}var ZL=(0,VL.default)("v5",80,GL.default),YL=ZL;lu.default=YL});var RE=_e(hu=>{"use strict";y();Object.defineProperty(hu,"__esModule",{value:!0});hu.default=void 0;var $L="00000000-0000-0000-0000-000000000000";hu.default=$L});var BE=_e(du=>{"use strict";y();Object.defineProperty(du,"__esModule",{value:!0});du.default=void 0;var JL=QL(ea());function QL(r){return r&&r.__esModule?r:{default:r}}function XL(r){if(!(0,JL.default)(r))throw TypeError("Invalid UUID");return parseInt(r.slice(14,15),16)}var e4=XL;du.default=e4});var zh=_e(Hr=>{"use strict";y();Object.defineProperty(Hr,"__esModule",{value:!0});Object.defineProperty(Hr,"NIL",{enumerable:!0,get:function(){return s4.default}});Object.defineProperty(Hr,"parse",{enumerable:!0,get:function(){return u4.default}});Object.defineProperty(Hr,"stringify",{enumerable:!0,get:function(){return c4.default}});Object.defineProperty(Hr,"v1",{enumerable:!0,get:function(){return t4.default}});Object.defineProperty(Hr,"v3",{enumerable:!0,get:function(){return r4.default}});Object.defineProperty(Hr,"v4",{enumerable:!0,get:function(){return n4.default}});Object.defineProperty(Hr,"v5",{enumerable:!0,get:function(){return o4.default}});Object.defineProperty(Hr,"validate",{enumerable:!0,get:function(){return a4.default}});Object.defineProperty(Hr,"version",{enumerable:!0,get:function(){return i4.default}});var t4=Rn(dE()),r4=Rn(bE()),n4=Rn(_E()),o4=Rn(IE()),s4=Rn(RE()),i4=Rn(BE()),a4=Rn(ea()),c4=Rn(ra()),u4=Rn(Bh());function Rn(r){return r&&r.__esModule?r:{default:r}}});var TE=_e((BG,PE)=>{"use strict";y();var p4=zh().v4,f4=function(r,e,t,n){if(typeof r!="string")throw new TypeError(r+" must be a string");n=n||{};let o=typeof n.version=="number"?n.version:2;if(o!==1&&o!==2)throw new TypeError(o+" must be 1 or 2");let s={method:r};if(o===2&&(s.jsonrpc="2.0"),e){if(typeof e!="object"&&!Array.isArray(e))throw new TypeError(e+" must be an object, array or omitted");s.params=e}if(typeof t>"u"){let i=typeof n.generator=="function"?n.generator:function(){return p4()};s.id=i(s,n)}else o===2&&t===null?n.notificationIdNull&&(s.id=null):s.id=t;return s};PE.exports=f4});var ME=_e((TG,zE)=>{"use strict";y();var l4=zh().v4,h4=TE(),na=function(r,e){if(!(this instanceof na))return new na(r,e);e||(e={}),this.options={reviver:typeof e.reviver<"u"?e.reviver:null,replacer:typeof e.replacer<"u"?e.replacer:null,generator:typeof e.generator<"u"?e.generator:function(){return l4()},version:typeof e.version<"u"?e.version:2,notificationIdNull:typeof e.notificationIdNull=="boolean"?e.notificationIdNull:!1},this.callServer=r};zE.exports=na;na.prototype.request=function(r,e,t,n){let o=this,s=null,i=Array.isArray(r)&&typeof e=="function";if(this.options.version===1&&i)throw new TypeError("JSON-RPC 1.0 does not support batching");if(i||!i&&r&&typeof r=="object"&&typeof e=="function")n=e,s=r;else{typeof t=="function"&&(n=t,t=void 0);let g=typeof n=="function";try{s=h4(r,e,t,{generator:this.options.generator,version:this.options.version,notificationIdNull:this.options.notificationIdNull})}catch(b){if(g)return n(b);throw b}if(!g)return s}let f;try{f=JSON.stringify(s,this.options.replacer)}catch(g){return n(g)}return this.callServer(f,function(g,b){o._parseResponse(g,b,n)}),s};na.prototype._parseResponse=function(r,e,t){if(r){t(r);return}if(!e)return t();let n;try{n=JSON.parse(e,this.options.reviver)}catch(o){return t(o)}if(t.length===3)if(Array.isArray(n)){let o=function(i){return typeof i.error<"u"},s=function(i){return!o(i)};return t(null,n.filter(o),n.filter(s))}else return t(null,n.error,n.result);t(null,n)}});var Mh=_e((MG,oa)=>{y();function d4(r){return r&&r.__esModule?r:{default:r}}oa.exports=d4,oa.exports.__esModule=!0,oa.exports.default=oa.exports});var oi=_e((NG,Bn)=>{y();function Lh(r){"@babel/helpers - typeof";return Bn.exports=Lh=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Bn.exports.__esModule=!0,Bn.exports.default=Bn.exports,Lh(r)}Bn.exports=Lh,Bn.exports.__esModule=!0,Bn.exports.default=Bn.exports});var NE=_e((OG,Pn)=>{y();var y4=oi().default;function LE(){"use strict";Pn.exports=LE=function(){return r},Pn.exports.__esModule=!0,Pn.exports.default=Pn.exports;var r={},e=Object.prototype,t=e.hasOwnProperty,n=Object.defineProperty||function(R,m,c){R[m]=c.value},o=typeof Symbol=="function"?Symbol:{},s=o.iterator||"@@iterator",i=o.asyncIterator||"@@asyncIterator",u=o.toStringTag||"@@toStringTag";function f(R,m,c){return Object.defineProperty(R,m,{value:c,enumerable:!0,configurable:!0,writable:!0}),R[m]}try{f({},"")}catch{f=function(c,p,h){return c[p]=h}}function g(R,m,c,p){var h=m&&m.prototype instanceof q?m:q,d=Object.create(h.prototype),x=new A(p||[]);return n(d,"_invoke",{value:Oe(R,c,x)}),d}function b(R,m,c){try{return{type:"normal",arg:R.call(m,c)}}catch(p){return{type:"throw",arg:p}}}r.wrap=g;var E={};function q(){}function C(){}function K(){}var ee={};f(ee,s,function(){return this});var $=Object.getPrototypeOf,le=$&&$($(z([])));le&&le!==e&&t.call(le,s)&&(ee=le);var W=K.prototype=q.prototype=Object.create(ee);function be(R){["next","throw","return"].forEach(function(m){f(R,m,function(c){return this._invoke(m,c)})})}function Te(R,m){function c(h,d,x,S){var P=b(R[h],R,d);if(P.type!=="throw"){var v=P.arg,l=v.value;return l&&y4(l)=="object"&&t.call(l,"__await")?m.resolve(l.__await).then(function(k){c("next",k,x,S)},function(k){c("throw",k,x,S)}):m.resolve(l).then(function(k){v.value=k,x(v)},function(k){return c("throw",k,x,S)})}S(P.arg)}var p;n(this,"_invoke",{value:function(d,x){function S(){return new m(function(P,v){c(d,x,P,v)})}return p=p?p.then(S,S):S()}})}function Oe(R,m,c){var p="suspendedStart";return function(h,d){if(p==="executing")throw new Error("Generator is already running");if(p==="completed"){if(h==="throw")throw d;return D()}for(c.method=h,c.arg=d;;){var x=c.delegate;if(x){var S=Ie(x,c);if(S){if(S===E)continue;return S}}if(c.method==="next")c.sent=c._sent=c.arg;else if(c.method==="throw"){if(p==="suspendedStart")throw p="completed",c.arg;c.dispatchException(c.arg)}else c.method==="return"&&c.abrupt("return",c.arg);p="executing";var P=b(R,m,c);if(P.type==="normal"){if(p=c.done?"completed":"suspendedYield",P.arg===E)continue;return{value:P.arg,done:c.done}}P.type==="throw"&&(p="completed",c.method="throw",c.arg=P.arg)}}}function Ie(R,m){var c=m.method,p=R.iterator[c];if(p===void 0)return m.delegate=null,c==="throw"&&R.iterator.return&&(m.method="return",m.arg=void 0,Ie(R,m),m.method==="throw")||c!=="return"&&(m.method="throw",m.arg=new TypeError("The iterator does not provide a '"+c+"' method")),E;var h=b(p,R.iterator,m.arg);if(h.type==="throw")return m.method="throw",m.arg=h.arg,m.delegate=null,E;var d=h.arg;return d?d.done?(m[R.resultName]=d.value,m.next=R.nextLoc,m.method!=="return"&&(m.method="next",m.arg=void 0),m.delegate=null,E):d:(m.method="throw",m.arg=new TypeError("iterator result is not an object"),m.delegate=null,E)}function Fe(R){var m={tryLoc:R[0]};1 in R&&(m.catchLoc=R[1]),2 in R&&(m.finallyLoc=R[2],m.afterLoc=R[3]),this.tryEntries.push(m)}function _(R){var m=R.completion||{};m.type="normal",delete m.arg,R.completion=m}function A(R){this.tryEntries=[{tryLoc:"root"}],R.forEach(Fe,this),this.reset(!0)}function z(R){if(R){var m=R[s];if(m)return m.call(R);if(typeof R.next=="function")return R;if(!isNaN(R.length)){var c=-1,p=function h(){for(;++c<R.length;)if(t.call(R,c))return h.value=R[c],h.done=!1,h;return h.value=void 0,h.done=!0,h};return p.next=p}}return{next:D}}function D(){return{value:void 0,done:!0}}return C.prototype=K,n(W,"constructor",{value:K,configurable:!0}),n(K,"constructor",{value:C,configurable:!0}),C.displayName=f(K,u,"GeneratorFunction"),r.isGeneratorFunction=function(R){var m=typeof R=="function"&&R.constructor;return!!m&&(m===C||(m.displayName||m.name)==="GeneratorFunction")},r.mark=function(R){return Object.setPrototypeOf?Object.setPrototypeOf(R,K):(R.__proto__=K,f(R,u,"GeneratorFunction")),R.prototype=Object.create(W),R},r.awrap=function(R){return{__await:R}},be(Te.prototype),f(Te.prototype,i,function(){return this}),r.AsyncIterator=Te,r.async=function(R,m,c,p,h){h===void 0&&(h=Promise);var d=new Te(g(R,m,c,p),h);return r.isGeneratorFunction(m)?d:d.next().then(function(x){return x.done?x.value:d.next()})},be(W),f(W,u,"Generator"),f(W,s,function(){return this}),f(W,"toString",function(){return"[object Generator]"}),r.keys=function(R){var m=Object(R),c=[];for(var p in m)c.push(p);return c.reverse(),function h(){for(;c.length;){var d=c.pop();if(d in m)return h.value=d,h.done=!1,h}return h.done=!0,h}},r.values=z,A.prototype={constructor:A,reset:function(m){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(_),!m)for(var c in this)c.charAt(0)==="t"&&t.call(this,c)&&!isNaN(+c.slice(1))&&(this[c]=void 0)},stop:function(){this.done=!0;var m=this.tryEntries[0].completion;if(m.type==="throw")throw m.arg;return this.rval},dispatchException:function(m){if(this.done)throw m;var c=this;function p(v,l){return x.type="throw",x.arg=m,c.next=v,l&&(c.method="next",c.arg=void 0),!!l}for(var h=this.tryEntries.length-1;h>=0;--h){var d=this.tryEntries[h],x=d.completion;if(d.tryLoc==="root")return p("end");if(d.tryLoc<=this.prev){var S=t.call(d,"catchLoc"),P=t.call(d,"finallyLoc");if(S&&P){if(this.prev<d.catchLoc)return p(d.catchLoc,!0);if(this.prev<d.finallyLoc)return p(d.finallyLoc)}else if(S){if(this.prev<d.catchLoc)return p(d.catchLoc,!0)}else{if(!P)throw new Error("try statement without catch or finally");if(this.prev<d.finallyLoc)return p(d.finallyLoc)}}}},abrupt:function(m,c){for(var p=this.tryEntries.length-1;p>=0;--p){var h=this.tryEntries[p];if(h.tryLoc<=this.prev&&t.call(h,"finallyLoc")&&this.prev<h.finallyLoc){var d=h;break}}d&&(m==="break"||m==="continue")&&d.tryLoc<=c&&c<=d.finallyLoc&&(d=null);var x=d?d.completion:{};return x.type=m,x.arg=c,d?(this.method="next",this.next=d.finallyLoc,E):this.complete(x)},complete:function(m,c){if(m.type==="throw")throw m.arg;return m.type==="break"||m.type==="continue"?this.next=m.arg:m.type==="return"?(this.rval=this.arg=m.arg,this.method="return",this.next="end"):m.type==="normal"&&c&&(this.next=c),E},finish:function(m){for(var c=this.tryEntries.length-1;c>=0;--c){var p=this.tryEntries[c];if(p.finallyLoc===m)return this.complete(p.completion,p.afterLoc),_(p),E}},catch:function(m){for(var c=this.tryEntries.length-1;c>=0;--c){var p=this.tryEntries[c];if(p.tryLoc===m){var h=p.completion;if(h.type==="throw"){var d=h.arg;_(p)}return d}}throw new Error("illegal catch attempt")},delegateYield:function(m,c,p){return this.delegate={iterator:z(m),resultName:c,nextLoc:p},this.method==="next"&&(this.arg=void 0),E}},r}Pn.exports=LE,Pn.exports.__esModule=!0,Pn.exports.default=Pn.exports});var OE=_e((UG,CE)=>{y();var yu=NE()();CE.exports=yu;try{regeneratorRuntime=yu}catch{typeof globalThis=="object"?globalThis.regeneratorRuntime=yu:Function("r","regeneratorRuntime = r")(yu)}});var UE=_e((FG,sa)=>{y();function qE(r,e,t,n,o,s,i){try{var u=r[s](i),f=u.value}catch(g){t(g);return}u.done?e(f):Promise.resolve(f).then(n,o)}function m4(r){return function(){var e=this,t=arguments;return new Promise(function(n,o){var s=r.apply(e,t);function i(f){qE(s,n,o,i,u,"next",f)}function u(f){qE(s,n,o,i,u,"throw",f)}i(void 0)})}}sa.exports=m4,sa.exports.__esModule=!0,sa.exports.default=sa.exports});var Nh=_e((HG,ia)=>{y();function g4(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}ia.exports=g4,ia.exports.__esModule=!0,ia.exports.default=ia.exports});var FE=_e((KG,aa)=>{y();var DE=oi().default;function x4(r,e){if(DE(r)!=="object"||r===null)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var n=t.call(r,e||"default");if(DE(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}aa.exports=x4,aa.exports.__esModule=!0,aa.exports.default=aa.exports});var jE=_e((GG,ca)=>{y();var w4=oi().default,b4=FE();function v4(r){var e=b4(r,"string");return w4(e)==="symbol"?e:String(e)}ca.exports=v4,ca.exports.__esModule=!0,ca.exports.default=ca.exports});var Ch=_e((YG,ua)=>{y();var S4=jE();function HE(r,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,S4(n.key),n)}}function E4(r,e,t){return e&&HE(r.prototype,e),t&&HE(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}ua.exports=E4,ua.exports.__esModule=!0,ua.exports.default=ua.exports});var WE=_e((JG,Tn)=>{y();function Oh(r,e){return Tn.exports=Oh=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,o){return n.__proto__=o,n},Tn.exports.__esModule=!0,Tn.exports.default=Tn.exports,Oh(r,e)}Tn.exports=Oh,Tn.exports.__esModule=!0,Tn.exports.default=Tn.exports});var qh=_e((XG,pa)=>{y();var _4=WE();function k4(r,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");r.prototype=Object.create(e&&e.prototype,{constructor:{value:r,writable:!0,configurable:!0}}),Object.defineProperty(r,"prototype",{writable:!1}),e&&_4(r,e)}pa.exports=k4,pa.exports.__esModule=!0,pa.exports.default=pa.exports});var KE=_e((tZ,fa)=>{y();function A4(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}fa.exports=A4,fa.exports.__esModule=!0,fa.exports.default=fa.exports});var Uh=_e((nZ,la)=>{y();var I4=oi().default,R4=KE();function B4(r,e){if(e&&(I4(e)==="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return R4(r)}la.exports=B4,la.exports.__esModule=!0,la.exports.default=la.exports});var Fh=_e((sZ,zn)=>{y();function Dh(r){return zn.exports=Dh=Object.setPrototypeOf?Object.getPrototypeOf.bind():function(t){return t.__proto__||Object.getPrototypeOf(t)},zn.exports.__esModule=!0,zn.exports.default=zn.exports,Dh(r)}zn.exports=Dh,zn.exports.__esModule=!0,zn.exports.default=zn.exports});var si=_e((aZ,jh)=>{"use strict";y();var P4=Object.prototype.hasOwnProperty,lr="~";function ha(){}Object.create&&(ha.prototype=Object.create(null),new ha().__proto__||(lr=!1));function T4(r,e,t){this.fn=r,this.context=e,this.once=t||!1}function VE(r,e,t,n,o){if(typeof t!="function")throw new TypeError("The listener must be a function");var s=new T4(t,n||r,o),i=lr?lr+e:e;return r._events[i]?r._events[i].fn?r._events[i]=[r._events[i],s]:r._events[i].push(s):(r._events[i]=s,r._eventsCount++),r}function mu(r,e){--r._eventsCount===0?r._events=new ha:delete r._events[e]}function ar(){this._events=new ha,this._eventsCount=0}ar.prototype.eventNames=function(){var e=[],t,n;if(this._eventsCount===0)return e;for(n in t=this._events)P4.call(t,n)&&e.push(lr?n.slice(1):n);return Object.getOwnPropertySymbols?e.concat(Object.getOwnPropertySymbols(t)):e};ar.prototype.listeners=function(e){var t=lr?lr+e:e,n=this._events[t];if(!n)return[];if(n.fn)return[n.fn];for(var o=0,s=n.length,i=new Array(s);o<s;o++)i[o]=n[o].fn;return i};ar.prototype.listenerCount=function(e){var t=lr?lr+e:e,n=this._events[t];return n?n.fn?1:n.length:0};ar.prototype.emit=function(e,t,n,o,s,i){var u=lr?lr+e:e;if(!this._events[u])return!1;var f=this._events[u],g=arguments.length,b,E;if(f.fn){switch(f.once&&this.removeListener(e,f.fn,void 0,!0),g){case 1:return f.fn.call(f.context),!0;case 2:return f.fn.call(f.context,t),!0;case 3:return f.fn.call(f.context,t,n),!0;case 4:return f.fn.call(f.context,t,n,o),!0;case 5:return f.fn.call(f.context,t,n,o,s),!0;case 6:return f.fn.call(f.context,t,n,o,s,i),!0}for(E=1,b=new Array(g-1);E<g;E++)b[E-1]=arguments[E];f.fn.apply(f.context,b)}else{var q=f.length,C;for(E=0;E<q;E++)switch(f[E].once&&this.removeListener(e,f[E].fn,void 0,!0),g){case 1:f[E].fn.call(f[E].context);break;case 2:f[E].fn.call(f[E].context,t);break;case 3:f[E].fn.call(f[E].context,t,n);break;case 4:f[E].fn.call(f[E].context,t,n,o);break;default:if(!b)for(C=1,b=new Array(g-1);C<g;C++)b[C-1]=arguments[C];f[E].fn.apply(f[E].context,b)}}return!0};ar.prototype.on=function(e,t,n){return VE(this,e,t,n,!1)};ar.prototype.once=function(e,t,n){return VE(this,e,t,n,!0)};ar.prototype.removeListener=function(e,t,n,o){var s=lr?lr+e:e;if(!this._events[s])return this;if(!t)return mu(this,s),this;var i=this._events[s];if(i.fn)i.fn===t&&(!o||i.once)&&(!n||i.context===n)&&mu(this,s);else{for(var u=0,f=[],g=i.length;u<g;u++)(i[u].fn!==t||o&&!i[u].once||n&&i[u].context!==n)&&f.push(i[u]);f.length?this._events[s]=f.length===1?f[0]:f:mu(this,s)}return this};ar.prototype.removeAllListeners=function(e){var t;return e?(t=lr?lr+e:e,this._events[t]&&mu(this,t)):(this._events=new ha,this._eventsCount=0),this};ar.prototype.off=ar.prototype.removeListener;ar.prototype.addListener=ar.prototype.on;ar.prefixed=lr;ar.EventEmitter=ar;typeof jh<"u"&&(jh.exports=ar)});var ZE=_e(xu=>{"use strict";y();var ho=Mh();Object.defineProperty(xu,"__esModule",{value:!0});xu.default=void 0;var lo=ho(OE()),gu=ho(UE()),z4=ho(oi()),M4=ho(Nh()),L4=ho(Ch()),N4=ho(qh()),C4=ho(Uh()),GE=ho(Fh()),O4=si();function q4(r){var e=U4();return function(){var n=(0,GE.default)(r),o;if(e){var s=(0,GE.default)(this).constructor;o=Reflect.construct(n,arguments,s)}else o=n.apply(this,arguments);return(0,C4.default)(this,o)}}function U4(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}var D4=function(r,e){var t={};for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&e.indexOf(n)<0&&(t[n]=r[n]);if(r!=null&&typeof Object.getOwnPropertySymbols=="function")for(var o=0,n=Object.getOwnPropertySymbols(r);o<n.length;o++)e.indexOf(n[o])<0&&Object.prototype.propertyIsEnumerable.call(r,n[o])&&(t[n[o]]=r[n[o]]);return t},F4=function(r){(0,N4.default)(t,r);var e=q4(t);function t(n){var o,s=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"ws://localhost:8080",i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},u=arguments.length>3?arguments[3]:void 0;(0,M4.default)(this,t);var f=i.autoconnect,g=f===void 0?!0:f,b=i.reconnect,E=b===void 0?!0:b,q=i.reconnect_interval,C=q===void 0?1e3:q,K=i.max_reconnects,ee=K===void 0?5:K,$=D4(i,["autoconnect","reconnect","reconnect_interval","max_reconnects"]);return o=e.call(this),o.webSocketFactory=n,o.queue={},o.rpc_id=0,o.address=s,o.autoconnect=g,o.ready=!1,o.reconnect=E,o.reconnect_timer_id=void 0,o.reconnect_interval=C,o.max_reconnects=ee,o.rest_options=$,o.current_reconnects=0,o.generate_request_id=u||function(){return++o.rpc_id},o.autoconnect&&o._connect(o.address,Object.assign({autoconnect:o.autoconnect,reconnect:o.reconnect,reconnect_interval:o.reconnect_interval,max_reconnects:o.max_reconnects},o.rest_options)),o}return(0,L4.default)(t,[{key:"connect",value:function(){this.socket||this._connect(this.address,Object.assign({autoconnect:this.autoconnect,reconnect:this.reconnect,reconnect_interval:this.reconnect_interval,max_reconnects:this.max_reconnects},this.rest_options))}},{key:"call",value:function(o,s,i,u){var f=this;return!u&&(0,z4.default)(i)==="object"&&(u=i,i=null),new Promise(function(g,b){if(!f.ready)return b(new Error("socket not ready"));var E=f.generate_request_id(o,s),q={jsonrpc:"2.0",method:o,params:s||null,id:E};f.socket.send(JSON.stringify(q),u,function(C){if(C)return b(C);f.queue[E]={promise:[g,b]},i&&(f.queue[E].timeout=setTimeout(function(){delete f.queue[E],b(new Error("reply timeout"))},i))})})}},{key:"login",value:function(){var n=(0,gu.default)(lo.default.mark(function s(i){var u;return lo.default.wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return g.next=2,this.call("rpc.login",i);case 2:if(u=g.sent,u){g.next=5;break}throw new Error("authentication failed");case 5:return g.abrupt("return",u);case 6:case"end":return g.stop()}},s,this)}));function o(s){return n.apply(this,arguments)}return o}()},{key:"listMethods",value:function(){var n=(0,gu.default)(lo.default.mark(function s(){return lo.default.wrap(function(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.call("__listMethods");case 2:return u.abrupt("return",u.sent);case 3:case"end":return u.stop()}},s,this)}));function o(){return n.apply(this,arguments)}return o}()},{key:"notify",value:function(o,s){var i=this;return new Promise(function(u,f){if(!i.ready)return f(new Error("socket not ready"));var g={jsonrpc:"2.0",method:o,params:s||null};i.socket.send(JSON.stringify(g),function(b){if(b)return f(b);u()})})}},{key:"subscribe",value:function(){var n=(0,gu.default)(lo.default.mark(function s(i){var u;return lo.default.wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return typeof i=="string"&&(i=[i]),g.next=3,this.call("rpc.on",i);case 3:if(u=g.sent,!(typeof i=="string"&&u[i]!=="ok")){g.next=6;break}throw new Error("Failed subscribing to an event '"+i+"' with: "+u[i]);case 6:return g.abrupt("return",u);case 7:case"end":return g.stop()}},s,this)}));function o(s){return n.apply(this,arguments)}return o}()},{key:"unsubscribe",value:function(){var n=(0,gu.default)(lo.default.mark(function s(i){var u;return lo.default.wrap(function(g){for(;;)switch(g.prev=g.next){case 0:return typeof i=="string"&&(i=[i]),g.next=3,this.call("rpc.off",i);case 3:if(u=g.sent,!(typeof i=="string"&&u[i]!=="ok")){g.next=6;break}throw new Error("Failed unsubscribing from an event with: "+u);case 6:return g.abrupt("return",u);case 7:case"end":return g.stop()}},s,this)}));function o(s){return n.apply(this,arguments)}return o}()},{key:"close",value:function(o,s){this.socket.close(o||1e3,s)}},{key:"_connect",value:function(o,s){var i=this;clearTimeout(this.reconnect_timer_id),this.socket=this.webSocketFactory(o,s),this.socket.addEventListener("open",function(){i.ready=!0,i.emit("open"),i.current_reconnects=0}),this.socket.addEventListener("message",function(u){var f=u.data;f instanceof ArrayBuffer&&(f=I.from(f).toString());try{f=JSON.parse(f)}catch{return}if(f.notification&&i.listeners(f.notification).length){if(!Object.keys(f.params).length)return i.emit(f.notification);var g=[f.notification];if(f.params.constructor===Object)g.push(f.params);else for(var b=0;b<f.params.length;b++)g.push(f.params[b]);return Promise.resolve().then(function(){i.emit.apply(i,g)})}if(!i.queue[f.id])return f.method&&f.params?Promise.resolve().then(function(){i.emit(f.method,f.params)}):void 0;"error"in f=="result"in f&&i.queue[f.id].promise[1](new Error('Server response malformed. Response must include either "result" or "error", but not both.')),i.queue[f.id].timeout&&clearTimeout(i.queue[f.id].timeout),f.error?i.queue[f.id].promise[1](f.error):i.queue[f.id].promise[0](f.result),delete i.queue[f.id]}),this.socket.addEventListener("error",function(u){return i.emit("error",u)}),this.socket.addEventListener("close",function(u){var f=u.code,g=u.reason;i.ready&&setTimeout(function(){return i.emit("close",f,g)},0),i.ready=!1,i.socket=void 0,f!==1e3&&(i.current_reconnects++,i.reconnect&&(i.max_reconnects>i.current_reconnects||i.max_reconnects===0)&&(i.reconnect_timer_id=setTimeout(function(){return i._connect(o,s)},i.reconnect_interval)))})}}]),t}(O4.EventEmitter);xu.default=F4});var $E=_e(Hh=>{"use strict";y();var da=Mh();Object.defineProperty(Hh,"__esModule",{value:!0});Hh.default=$4;var j4=da(Nh()),H4=da(Ch()),W4=da(qh()),K4=da(Uh()),YE=da(Fh()),V4=si();function G4(r){var e=Z4();return function(){var n=(0,YE.default)(r),o;if(e){var s=(0,YE.default)(this).constructor;o=Reflect.construct(n,arguments,s)}else o=n.apply(this,arguments);return(0,K4.default)(this,o)}}function Z4(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}var Y4=function(r){(0,W4.default)(t,r);var e=G4(t);function t(n,o,s){var i;return(0,j4.default)(this,t),i=e.call(this),i.socket=new self.WebSocket(n,s),i.socket.onopen=function(){return i.emit("open")},i.socket.onmessage=function(u){return i.emit("message",u.data)},i.socket.onerror=function(u){return i.emit("error",u)},i.socket.onclose=function(u){i.emit("close",u.code,u.reason)},i}return(0,H4.default)(t,[{key:"send",value:function(o,s,i){var u=i||s;try{this.socket.send(o),u()}catch(f){u(f)}}},{key:"close",value:function(o,s){this.socket.close(o,s)}},{key:"addEventListener",value:function(o,s,i){this.socket.addEventListener(o,s,i)}}]),t}(V4.EventEmitter);function $4(r,e){return new Y4(r,e)}});function o6(r,e=24){let t=new Uint32Array(10);for(let n=24-e;n<24;n++){for(let i=0;i<10;i++)t[i]=r[i]^r[i+10]^r[i+20]^r[i+30]^r[i+40];for(let i=0;i<10;i+=2){let u=(i+8)%10,f=(i+2)%10,g=t[f],b=t[f+1],E=JE(g,b,1)^t[u],q=QE(g,b,1)^t[u+1];for(let C=0;C<50;C+=10)r[i+C]^=E,r[i+C+1]^=q}let o=r[2],s=r[3];for(let i=0;i<24;i++){let u=e_[i],f=JE(o,s,u),g=QE(o,s,u),b=XE[i];o=r[b],s=r[b+1],r[b]=f,r[b+1]=g}for(let i=0;i<50;i+=10){for(let u=0;u<10;u++)t[u]=r[i+u];for(let u=0;u<10;u++)r[i+u]^=~t[(u+2)%10]&t[(u+4)%10]}r[0]^=r6[n],r[1]^=n6[n]}t.fill(0)}var XE,e_,t_,J4,ya,Q4,X4,e6,t6,r6,n6,JE,QE,wu,yo,mZ,gZ,xZ,wZ,bZ,Wh,vZ,SZ,r_,EZ,_Z,n_=O(()=>{y();Cc();rh();$s();[XE,e_,t_]=[[],[],[]],J4=BigInt(0),ya=BigInt(1),Q4=BigInt(2),X4=BigInt(7),e6=BigInt(256),t6=BigInt(113);for(let r=0,e=ya,t=1,n=0;r<24;r++){[t,n]=[n,(2*t+3*n)%5],XE.push(2*(5*n+t)),e_.push((r+1)*(r+2)/2%64);let o=J4;for(let s=0;s<7;s++)e=(e<<ya^(e>>X4)*t6)%e6,e&Q4&&(o^=ya<<(ya<<BigInt(s))-ya);t_.push(o)}[r6,n6]=ze.split(t_,!0),JE=(r,e,t)=>t>32?ze.rotlBH(r,e,t):ze.rotlSH(r,e,t),QE=(r,e,t)=>t>32?ze.rotlBL(r,e,t):ze.rotlSL(r,e,t);wu=class r extends Qn{constructor(e,t,n,o=!1,s=24){if(super(),this.blockLen=e,this.suffix=t,this.outputLen=n,this.enableXOF=o,this.rounds=s,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,rr.number(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=OS(this.state)}keccak(){o6(this.state32,this.rounds),this.posOut=0,this.pos=0}update(e){rr.exists(this);let{blockLen:t,state:n}=this;e=Xn(e);let o=e.length;for(let s=0;s<o;){let i=Math.min(t-this.pos,o-s);for(let u=0;u<i;u++)n[this.pos++]^=e[s++];this.pos===t&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:e,suffix:t,pos:n,blockLen:o}=this;e[n]^=t,t&128&&n===o-1&&this.keccak(),e[o-1]^=128,this.keccak()}writeInto(e){rr.exists(this,!1),rr.bytes(e),this.finish();let t=this.state,{blockLen:n}=this;for(let o=0,s=e.length;o<s;){this.posOut>=n&&this.keccak();let i=Math.min(n-this.posOut,s-o);e.set(t.subarray(this.posOut,this.posOut+i),o),this.posOut+=i,o+=i}return e}xofInto(e){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(e)}xof(e){return rr.number(e),this.xofInto(new Uint8Array(e))}digestInto(e){if(rr.output(e,this),this.finished)throw new Error("digest() was already called");return this.writeInto(e),this.destroy(),e}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(e){let{blockLen:t,suffix:n,outputLen:o,rounds:s,enableXOF:i}=this;return e||(e=new r(t,n,o,i,s)),e.state32.set(this.state32),e.pos=this.pos,e.posOut=this.posOut,e.finished=this.finished,e.rounds=s,e.suffix=n,e.outputLen=o,e.enableXOF=i,e.destroyed=this.destroyed,e}},yo=(r,e,t)=>an(()=>new wu(e,r,t)),mZ=yo(6,144,224/8),gZ=yo(6,136,256/8),xZ=yo(6,104,384/8),wZ=yo(6,72,512/8),bZ=yo(1,144,224/8),Wh=yo(1,136,256/8),vZ=yo(1,104,384/8),SZ=yo(1,72,512/8),r_=(r,e,t)=>qS((n={})=>new wu(e,r,n.dkLen===void 0?t:n.dkLen,!0)),EZ=r_(31,168,128/8),_Z=r_(31,136,256/8)});var bu,Kh,o_=O(()=>{y();Cc();$s();bu=class extends Qn{constructor(e,t){super(),this.finished=!1,this.destroyed=!1,rr.hash(e);let n=Xn(t);if(this.iHash=e.create(),typeof this.iHash.update!="function")throw new TypeError("Expected instance of class which extends utils.Hash");this.blockLen=this.iHash.blockLen,this.outputLen=this.iHash.outputLen;let o=this.blockLen,s=new Uint8Array(o);s.set(n.length>o?e.create().update(n).digest():n);for(let i=0;i<s.length;i++)s[i]^=54;this.iHash.update(s),this.oHash=e.create();for(let i=0;i<s.length;i++)s[i]^=106;this.oHash.update(s),s.fill(0)}update(e){return rr.exists(this),this.iHash.update(e),this}digestInto(e){rr.exists(this),rr.bytes(e,this.outputLen),this.finished=!0,this.iHash.digestInto(e),this.oHash.update(e),this.oHash.digestInto(e),this.destroy()}digest(){let e=new Uint8Array(this.oHash.outputLen);return this.digestInto(e),e}_cloneInto(e){e||(e=Object.create(Object.getPrototypeOf(this),{}));let{oHash:t,iHash:n,finished:o,destroyed:s,blockLen:i,outputLen:u}=this;return e=e,e.finished=o,e.destroyed=s,e.blockLen=i,e.outputLen=u,e.oHash=t._cloneInto(e.oHash),e.iHash=n._cloneInto(e.iHash),e}destroy(){this.destroyed=!0,this.oHash.destroy(),this.iHash.destroy()}},Kh=(r,e,t)=>new bu(r,e).update(t).digest();Kh.create=(r,e)=>new bu(r,e)});function u_(r){let{a:e,b:t}=jt,n=ge(r*r),o=ge(n*r);return ge(o+e*r+t)}function p_(r){if(!(r instanceof hr))throw new TypeError("JacobianPoint expected")}function Eu(r,e){let t=e.negate();return r?t:e}function f_(r){return Number.parseInt(r[0],16)>=8?"00"+r:r}function l_(r){if(r.length<2||r[0]!==2)throw new Error(`Invalid signature integer tag: ${pi(r)}`);let e=r[1],t=r.subarray(2,e+2);if(!e||t.length!==e)throw new Error("Invalid signature integer: wrong length");if(t[0]===0&&t[1]<=127)throw new Error("Invalid signature integer: trailing length");return{data:xo(t),left:r.subarray(e+2)}}function a6(r){if(r.length<2||r[0]!=48)throw new Error(`Invalid signature tag: ${pi(r)}`);if(r[1]!==r.length-2)throw new Error("Invalid signature: incorrect length");let{data:e,left:t}=l_(r.subarray(2)),{data:n,left:o}=l_(t);if(o.length)throw new Error(`Invalid signature: left bytes after parsing: ${pi(o)}`);return{r:e,s:n}}function mo(...r){if(!r.every(n=>n instanceof Uint8Array))throw new Error("Uint8Array list expected");if(r.length===1)return r[0];let e=r.reduce((n,o)=>n+o.length,0),t=new Uint8Array(e);for(let n=0,o=0;n<r.length;n++){let s=r[n];t.set(s,o),o+=s.length}return t}function pi(r){if(!(r instanceof Uint8Array))throw new Error("Expected Uint8Array");let e="";for(let t=0;t<r.length;t++)e+=c6[r[t]];return e}function ai(r){if(typeof r!="bigint")throw new Error("Expected bigint");if(!(We<=r&&r<u6))throw new Error("Expected number 0 <= n < 2^256");return r.toString(16).padStart(64,"0")}function Zh(r){let e=ls(ai(r));if(e.length!==32)throw new Error("Error: expected 32 bytes");return e}function ma(r){let e=r.toString(16);return e.length&1?`0${e}`:e}function Au(r){if(typeof r!="string")throw new TypeError("hexToNumber: expected string, got "+typeof r);return BigInt(`0x${r}`)}function ls(r){if(typeof r!="string")throw new TypeError("hexToBytes: expected string, got "+typeof r);if(r.length%2)throw new Error("hexToBytes: received invalid unpadded hex"+r.length);let e=new Uint8Array(r.length/2);for(let t=0;t<e.length;t++){let n=t*2,o=r.slice(n,n+2),s=Number.parseInt(o,16);if(Number.isNaN(s)||s<0)throw new Error("Invalid byte sequence");e[t]=s}return e}function xo(r){return Au(pi(r))}function wa(r){return r instanceof Uint8Array?Uint8Array.from(r):ls(r)}function h_(r){if(typeof r=="number"&&Number.isSafeInteger(r)&&r>0)return BigInt(r);if(typeof r=="bigint"&&ba(r))return r;throw new TypeError("Expected valid private scalar: 0 < scalar < curve.n")}function ge(r,e=jt.P){let t=r%e;return t>=We?t:e+t}function Cr(r,e){let{P:t}=jt,n=r;for(;e-- >We;)n*=n,n%=t;return n}function p6(r){let{P:e}=jt,t=BigInt(6),n=BigInt(11),o=BigInt(22),s=BigInt(23),i=BigInt(44),u=BigInt(88),f=r*r*r%e,g=f*f*r%e,b=Cr(g,ga)*g%e,E=Cr(b,ga)*g%e,q=Cr(E,go)*f%e,C=Cr(q,n)*q%e,K=Cr(C,o)*C%e,ee=Cr(K,i)*K%e,$=Cr(ee,u)*ee%e,le=Cr($,i)*K%e,W=Cr(le,ga)*g%e,be=Cr(W,s)*C%e,Te=Cr(be,t)*f%e,Oe=Cr(Te,go);if(Oe*Oe%e!==r)throw new Error("Cannot find square root");return Oe}function va(r,e=jt.P){if(r===We||e<=We)throw new Error(`invert: expected positive integers, got n=${r} mod=${e}`);let t=ge(r,e),n=e,o=We,s=Nt,i=Nt,u=We;for(;t!==We;){let g=n/t,b=n%t,E=o-i*g,q=s-u*g;n=t,t=b,o=i,s=u,i=E,u=q}if(n!==Nt)throw new Error("invert: does not exist");return ge(o,e)}function f6(r,e=jt.P){let t=new Array(r.length),n=r.reduce((s,i,u)=>i===We?s:(t[u]=s,ge(s*i,e)),Nt),o=va(n,e);return r.reduceRight((s,i,u)=>i===We?s:(t[u]=ge(s*t[u],e),ge(s*i,e)),o),t}function l6(r){let e=r.length*8-ui*8,t=xo(r);return e>0?t>>BigInt(e):t}function d_(r,e=!1){let t=l6(r);if(e)return t;let{n}=jt;return t>=n?t-n:t}function ba(r){return We<r&&r<jt.n}function Vh(r){return We<r&&r<jt.P}function h6(r,e,t,n=!0){let{n:o}=jt,s=d_(r,!0);if(!ba(s))return;let i=va(s,o),u=Qt.BASE.multiply(s),f=ge(u.x,o);if(f===We)return;let g=ge(i*ge(e+t*f,o),o);if(g===We)return;let b=new ii(f,g),E=(u.x===b.r?0:2)|Number(u.y&Nt);return n&&b.hasHighS()&&(b=b.normalizeS(),E^=1),{sig:b,recovery:E}}function Iu(r){let e;if(typeof r=="bigint")e=r;else if(typeof r=="number"&&Number.isSafeInteger(r)&&r>0)e=BigInt(r);else if(typeof r=="string"){if(r.length!==2*ui)throw new Error("Expected 32 bytes of private key");e=Au(r)}else if(r instanceof Uint8Array){if(r.length!==ui)throw new Error("Expected 32 bytes of private key");e=xo(r)}else throw new TypeError("Expected valid private key");if(!ba(e))throw new Error("Expected private key: 0 < key < n");return e}function d6(r){if(r instanceof ii)return r.assertValidity(),r;try{return ii.fromDER(r)}catch{return ii.fromCompact(r)}}function y_(r,e=!1){return Qt.fromPrivateKey(r).toRawBytes(e)}function m_(r){let e=r.length>Wr?r.slice(0,Wr):r;return xo(e)}function y6(r){let e=m_(r),t=ge(e,jt.n);return g_(t<We?e:t)}function g_(r){return Zh(r)}function m6(r,e,t){if(r==null)throw new Error(`sign: expected valid message hash, not "${r}"`);let n=wa(r),o=Iu(e),s=[g_(o),y6(n)];if(t!=null){t===!0&&(t=ln.randomBytes(Wr));let f=wa(t);if(f.length!==Wr)throw new Error(`sign: Expected ${Wr} bytes of extra data`);s.push(f)}let i=mo(...s),u=m_(n);return{seed:i,m:u,d:o}}function g6(r,e){let{sig:t,recovery:n}=r,{der:o,recovered:s}=Object.assign({canonical:!0,der:!0},e),i=o?t.toDERRawBytes():t.toCompactRawBytes();return s?[i,n]:i}function x_(r,e,t={}){let{seed:n,m:o,d:s}=m6(r,e,t.extraEntropy),i=new Yh(i6,ui);i.reseedSync(n);let u;for(;!(u=h6(i.generateSync(),o,s,t.canonical));)i.reseedSync();return g6(u,t)}var s6,We,Nt,go,ga,s_,jt,i_,vu,Wr,ui,i6,a_,c_,Su,ku,hr,Gh,Qt,ii,c6,u6,ci,xa,Yh,_r,_u,ln,w_=O(()=>{y();s6=Ke(ih(),1);We=BigInt(0),Nt=BigInt(1),go=BigInt(2),ga=BigInt(3),s_=BigInt(8),jt=Object.freeze({a:We,b:BigInt(7),P:BigInt("0xfffffffffffffffffffffffffffffffffffffffffffffffffffffffefffffc2f"),n:BigInt("0xfffffffffffffffffffffffffffffffebaaedce6af48a03bbfd25e8cd0364141"),h:Nt,Gx:BigInt("55066263022277343669578718895168534326250603453777594175500187360389116729240"),Gy:BigInt("32670510020758816978083085130507043184471273380659243275938904335757337482424"),beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee")}),i_=(r,e)=>(r+e/go)/e,vu={beta:BigInt("0x7ae96a2b657c07106e64479eac3434e99cf0497512f58995c1396c28719501ee"),splitScalar(r){let{n:e}=jt,t=BigInt("0x3086d221a7d46bcde86c90e49284eb15"),n=-Nt*BigInt("0xe4437ed6010e88286f547fa90abfe4c3"),o=BigInt("0x114ca50f7a8e2f3f657c1108d9d44cfd8"),s=t,i=BigInt("0x100000000000000000000000000000000"),u=i_(s*r,e),f=i_(-n*r,e),g=ge(r-u*t-f*o,e),b=ge(-u*n-f*s,e),E=g>i,q=b>i;if(E&&(g=e-g),q&&(b=e-b),g>i||b>i)throw new Error("splitScalarEndo: Endomorphism failed, k="+r);return{k1neg:E,k1:g,k2neg:q,k2:b}}},Wr=32,ui=32,i6=32,a_=Wr+1,c_=2*Wr+1;Su=jt.a===We,ku=class extends Error{constructor(e){super(e)}};hr=class r{constructor(e,t,n){this.x=e,this.y=t,this.z=n}static fromAffine(e){if(!(e instanceof Qt))throw new TypeError("JacobianPoint#fromAffine: expected Point");return e.equals(Qt.ZERO)?r.ZERO:new r(e.x,e.y,Nt)}static toAffineBatch(e){let t=f6(e.map(n=>n.z));return e.map((n,o)=>n.toAffine(t[o]))}static normalizeZ(e){return r.toAffineBatch(e).map(r.fromAffine)}equals(e){p_(e);let{x:t,y:n,z:o}=this,{x:s,y:i,z:u}=e,f=ge(o*o),g=ge(u*u),b=ge(t*g),E=ge(s*f),q=ge(ge(n*u)*g),C=ge(ge(i*o)*f);return b===E&&q===C}negate(){return new r(this.x,ge(-this.y),this.z)}double(){let{x:e,y:t,z:n}=this,o=ge(e*e),s=ge(t*t),i=ge(s*s),u=e+s,f=ge(go*(ge(u*u)-o-i)),g=ge(ga*o),b=ge(g*g),E=ge(b-go*f),q=ge(g*(f-E)-s_*i),C=ge(go*t*n);return new r(E,q,C)}add(e){p_(e);let{x:t,y:n,z:o}=this,{x:s,y:i,z:u}=e;if(s===We||i===We)return this;if(t===We||n===We)return e;let f=ge(o*o),g=ge(u*u),b=ge(t*g),E=ge(s*f),q=ge(ge(n*u)*g),C=ge(ge(i*o)*f),K=ge(E-b),ee=ge(C-q);if(K===We)return ee===We?this.double():r.ZERO;let $=ge(K*K),le=ge(K*$),W=ge(b*$),be=ge(ee*ee-le-go*W),Te=ge(ee*(W-be)-q*le),Oe=ge(o*u*K);return new r(be,Te,Oe)}subtract(e){return this.add(e.negate())}multiplyUnsafe(e){let t=r.ZERO;if(typeof e=="bigint"&&e===We)return t;let n=h_(e);if(n===Nt)return this;if(!Su){let E=t,q=this;for(;n>We;)n&Nt&&(E=E.add(q)),q=q.double(),n>>=Nt;return E}let{k1neg:o,k1:s,k2neg:i,k2:u}=vu.splitScalar(n),f=t,g=t,b=this;for(;s>We||u>We;)s&Nt&&(f=f.add(b)),u&Nt&&(g=g.add(b)),b=b.double(),s>>=Nt,u>>=Nt;return o&&(f=f.negate()),i&&(g=g.negate()),g=new r(ge(g.x*vu.beta),g.y,g.z),f.add(g)}precomputeWindow(e){let t=Su?128/e+1:256/e+1,n=[],o=this,s=o;for(let i=0;i<t;i++){s=o,n.push(s);for(let u=1;u<2**(e-1);u++)s=s.add(o),n.push(s);o=s.double()}return n}wNAF(e,t){!t&&this.equals(r.BASE)&&(t=Qt.BASE);let n=t&&t._WINDOW_SIZE||1;if(256%n)throw new Error("Point#wNAF: Invalid precomputation window, must be power of 2");let o=t&&Gh.get(t);o||(o=this.precomputeWindow(n),t&&n!==1&&(o=r.normalizeZ(o),Gh.set(t,o)));let s=r.ZERO,i=r.BASE,u=1+(Su?128/n:256/n),f=2**(n-1),g=BigInt(2**n-1),b=2**n,E=BigInt(n);for(let q=0;q<u;q++){let C=q*f,K=Number(e&g);e>>=E,K>f&&(K-=b,e+=Nt);let ee=C,$=C+Math.abs(K)-1,le=q%2!==0,W=K<0;K===0?i=i.add(Eu(le,o[ee])):s=s.add(Eu(W,o[$]))}return{p:s,f:i}}multiply(e,t){let n=h_(e),o,s;if(Su){let{k1neg:i,k1:u,k2neg:f,k2:g}=vu.splitScalar(n),{p:b,f:E}=this.wNAF(u,t),{p:q,f:C}=this.wNAF(g,t);b=Eu(i,b),q=Eu(f,q),q=new r(ge(q.x*vu.beta),q.y,q.z),o=b.add(q),s=E.add(C)}else{let{p:i,f:u}=this.wNAF(n,t);o=i,s=u}return r.normalizeZ([o,s])[0]}toAffine(e){let{x:t,y:n,z:o}=this,s=this.equals(r.ZERO);e==null&&(e=s?s_:va(o));let i=e,u=ge(i*i),f=ge(u*i),g=ge(t*u),b=ge(n*f),E=ge(o*i);if(s)return Qt.ZERO;if(E!==Nt)throw new Error("invZ was invalid");return new Qt(g,b)}};hr.BASE=new hr(jt.Gx,jt.Gy,Nt);hr.ZERO=new hr(We,Nt,We);Gh=new WeakMap,Qt=class r{constructor(e,t){this.x=e,this.y=t}_setWindowSize(e){this._WINDOW_SIZE=e,Gh.delete(this)}hasEvenY(){return this.y%go===We}static fromCompressedHex(e){let t=e.length===32,n=xo(t?e:e.subarray(1));if(!Vh(n))throw new Error("Point is not on curve");let o=u_(n),s=p6(o),i=(s&Nt)===Nt;t?i&&(s=ge(-s)):(e[0]&1)===1!==i&&(s=ge(-s));let u=new r(n,s);return u.assertValidity(),u}static fromUncompressedHex(e){let t=xo(e.subarray(1,Wr+1)),n=xo(e.subarray(Wr+1,Wr*2+1)),o=new r(t,n);return o.assertValidity(),o}static fromHex(e){let t=wa(e),n=t.length,o=t[0];if(n===Wr)return this.fromCompressedHex(t);if(n===a_&&(o===2||o===3))return this.fromCompressedHex(t);if(n===c_&&o===4)return this.fromUncompressedHex(t);throw new Error(`Point.fromHex: received invalid point. Expected 32-${a_} compressed bytes or ${c_} uncompressed bytes, not ${n}`)}static fromPrivateKey(e){return r.BASE.multiply(Iu(e))}static fromSignature(e,t,n){let{r:o,s}=d6(t);if(![0,1,2,3].includes(n))throw new Error("Cannot recover: invalid recovery bit");let i=d_(wa(e)),{n:u}=jt,f=n===2||n===3?o+u:o,g=va(f,u),b=ge(-i*g,u),E=ge(s*g,u),q=n&1?"03":"02",C=r.fromHex(q+ai(f)),K=r.BASE.multiplyAndAddUnsafe(C,b,E);if(!K)throw new Error("Cannot recover signature: point at infinify");return K.assertValidity(),K}toRawBytes(e=!1){return ls(this.toHex(e))}toHex(e=!1){let t=ai(this.x);return e?`${this.hasEvenY()?"02":"03"}${t}`:`04${t}${ai(this.y)}`}toHexX(){return this.toHex(!0).slice(2)}toRawX(){return this.toRawBytes(!0).slice(1)}assertValidity(){let e="Point is not on elliptic curve",{x:t,y:n}=this;if(!Vh(t)||!Vh(n))throw new Error(e);let o=ge(n*n),s=u_(t);if(ge(o-s)!==We)throw new Error(e)}equals(e){return this.x===e.x&&this.y===e.y}negate(){return new r(this.x,ge(-this.y))}double(){return hr.fromAffine(this).double().toAffine()}add(e){return hr.fromAffine(this).add(hr.fromAffine(e)).toAffine()}subtract(e){return this.add(e.negate())}multiply(e){return hr.fromAffine(this).multiply(e,this).toAffine()}multiplyAndAddUnsafe(e,t,n){let o=hr.fromAffine(this),s=t===We||t===Nt||this!==r.BASE?o.multiplyUnsafe(t):o.multiply(t),i=hr.fromAffine(e).multiplyUnsafe(n),u=s.add(i);return u.equals(hr.ZERO)?void 0:u.toAffine()}};Qt.BASE=new Qt(jt.Gx,jt.Gy);Qt.ZERO=new Qt(We,We);ii=class r{constructor(e,t){this.r=e,this.s=t,this.assertValidity()}static fromCompact(e){let t=e instanceof Uint8Array,n="Signature.fromCompact";if(typeof e!="string"&&!t)throw new TypeError(`${n}: Expected string or Uint8Array`);let o=t?pi(e):e;if(o.length!==128)throw new Error(`${n}: Expected 64-byte hex`);return new r(Au(o.slice(0,64)),Au(o.slice(64,128)))}static fromDER(e){let t=e instanceof Uint8Array;if(typeof e!="string"&&!t)throw new TypeError("Signature.fromDER: Expected string or Uint8Array");let{r:n,s:o}=a6(t?e:ls(e));return new r(n,o)}static fromHex(e){return this.fromDER(e)}assertValidity(){let{r:e,s:t}=this;if(!ba(e))throw new Error("Invalid Signature: r must be 0 < r < n");if(!ba(t))throw new Error("Invalid Signature: s must be 0 < s < n")}hasHighS(){let e=jt.n>>Nt;return this.s>e}normalizeS(){return this.hasHighS()?new r(this.r,ge(-this.s,jt.n)):this}toDERRawBytes(){return ls(this.toDERHex())}toDERHex(){let e=f_(ma(this.s)),t=f_(ma(this.r)),n=e.length/2,o=t.length/2,s=ma(n),i=ma(o);return`30${ma(o+n+4)}02${i}${t}02${s}${e}`}toRawBytes(){return this.toDERRawBytes()}toHex(){return this.toDERHex()}toCompactRawBytes(){return ls(this.toCompactHex())}toCompactHex(){return ai(this.r)+ai(this.s)}};c6=Array.from({length:256},(r,e)=>e.toString(16).padStart(2,"0"));u6=BigInt("0x10000000000000000000000000000000000000000000000000000000000000000");Yh=class{constructor(e,t){if(this.hashLen=e,this.qByteLen=t,typeof e!="number"||e<2)throw new Error("hashLen must be a number");if(typeof t!="number"||t<2)throw new Error("qByteLen must be a number");this.v=new Uint8Array(e).fill(1),this.k=new Uint8Array(e).fill(0),this.counter=0}hmac(...e){return ln.hmacSha256(this.k,...e)}hmacSync(...e){return xa(this.k,...e)}checkSync(){if(typeof xa!="function")throw new ku("hmacSha256Sync needs to be set")}incr(){if(this.counter>=1e3)throw new Error("Tried 1,000 k values for sign(), all were invalid");this.counter+=1}async reseed(e=new Uint8Array){this.k=await this.hmac(this.v,Uint8Array.from([0]),e),this.v=await this.hmac(this.v),e.length!==0&&(this.k=await this.hmac(this.v,Uint8Array.from([1]),e),this.v=await this.hmac(this.v))}reseedSync(e=new Uint8Array){this.checkSync(),this.k=this.hmacSync(this.v,Uint8Array.from([0]),e),this.v=this.hmacSync(this.v),e.length!==0&&(this.k=this.hmacSync(this.v,Uint8Array.from([1]),e),this.v=this.hmacSync(this.v))}async generate(){this.incr();let e=0,t=[];for(;e<this.qByteLen;){this.v=await this.hmac(this.v);let n=this.v.slice();t.push(n),e+=this.v.length}return mo(...t)}generateSync(){this.checkSync(),this.incr();let e=0,t=[];for(;e<this.qByteLen;){this.v=this.hmacSync(this.v);let n=this.v.slice();t.push(n),e+=this.v.length}return mo(...t)}};Qt.BASE._setWindowSize(8);_r={node:s6,web:typeof self=="object"&&"crypto"in self?self.crypto:void 0},_u={},ln={bytesToHex:pi,hexToBytes:ls,concatBytes:mo,mod:ge,invert:va,isValidPrivateKey(r){try{return Iu(r),!0}catch{return!1}},_bigintTo32Bytes:Zh,_normalizePrivateKey:Iu,hashToPrivateKey:r=>{r=wa(r);let e=ui+8;if(r.length<e||r.length>1024)throw new Error("Expected valid bytes of private key as per FIPS 186");let t=ge(xo(r),jt.n-Nt)+Nt;return Zh(t)},randomBytes:(r=32)=>{if(_r.web)return _r.web.getRandomValues(new Uint8Array(r));if(_r.node){let{randomBytes:e}=_r.node;return Uint8Array.from(e(r))}else throw new Error("The environment doesn't have randomBytes function")},randomPrivateKey:()=>ln.hashToPrivateKey(ln.randomBytes(ui+8)),precompute(r=8,e=Qt.BASE){let t=e===Qt.BASE?e:new Qt(e.x,e.y);return t._setWindowSize(r),t.multiply(ga),t},sha256:async(...r)=>{if(_r.web){let e=await _r.web.subtle.digest("SHA-256",mo(...r));return new Uint8Array(e)}else if(_r.node){let{createHash:e}=_r.node,t=e("sha256");return r.forEach(n=>t.update(n)),Uint8Array.from(t.digest())}else throw new Error("The environment doesn't have sha256 function")},hmacSha256:async(r,...e)=>{if(_r.web){let t=await _r.web.subtle.importKey("raw",r,{name:"HMAC",hash:{name:"SHA-256"}},!1,["sign"]),n=mo(...e),o=await _r.web.subtle.sign("HMAC",t,n);return new Uint8Array(o)}else if(_r.node){let{createHmac:t}=_r.node,n=t("sha256",r);return e.forEach(o=>n.update(o)),Uint8Array.from(n.digest())}else throw new Error("The environment doesn't have hmac-sha256 function")},sha256Sync:void 0,hmacSha256Sync:void 0,taggedHash:async(r,...e)=>{let t=_u[r];if(t===void 0){let n=await ln.sha256(Uint8Array.from(r,o=>o.charCodeAt(0)));t=mo(n,n),_u[r]=t}return ln.sha256(t,...e)},taggedHashSync:(r,...e)=>{if(typeof ci!="function")throw new ku("sha256Sync is undefined, you need to set it");let t=_u[r];if(t===void 0){let n=ci(Uint8Array.from(r,o=>o.charCodeAt(0)));t=mo(n,n),_u[r]=t}return ci(t,...e)},_JacobianPoint:hr};Object.defineProperties(ln,{sha256Sync:{configurable:!1,get(){return ci},set(r){ci||(ci=r)}},hmacSha256Sync:{configurable:!1,get(){return xa},set(r){xa||(xa=r)}}})});function v_(r){try{return Dt.fromHex(r,!0),!0}catch{return!1}}function _6(r){return r._bn!==void 0}function L_(r,e){let t=o=>{if(o.span>=0)return o.span;if(typeof o.alloc=="function")return o.alloc(e[o.property]);if("count"in o&&"elementLayout"in o){let s=e[o.property];if(Array.isArray(s))return s.length*t(o.elementLayout)}else if("fields"in o)return L_({layout:o},e[o.property]);return 0},n=0;return r.layout.fields.forEach(o=>{n+=t(o)}),n}function kr(r){let e=0,t=0;for(;;){let n=r.shift();if(e|=(n&127)<<t*7,t+=1,!(n&128))break}return e}function Ar(r,e){let t=e;for(;;){let n=t&127;if(t>>=7,t==0){r.push(n);break}else n|=128,r.push(n)}}function Ct(r,e){if(!r)throw new Error(e||"Assertion failed")}function Ln(r){if(r.length===0)throw new Error(N_);return r.shift()}function Ir(r,...e){let[t]=e;if(e.length===2?t+(e[1]??0)>r.length:t>=r.length)throw new Error(N_);return r.splice(...e)}async function Qh(r,e,t,n){let o=n&&{skipPreflight:n.skipPreflight,preflightCommitment:n.preflightCommitment||n.commitment,maxRetries:n.maxRetries,minContextSlot:n.minContextSlot},s=await r.sendTransaction(e,t,o),i;if(e.recentBlockhash!=null&&e.lastValidBlockHeight!=null)i=(await r.confirmTransaction({abortSignal:n?.abortSignal,signature:s,blockhash:e.recentBlockhash,lastValidBlockHeight:e.lastValidBlockHeight},n&&n.commitment)).value;else if(e.minNonceContextSlot!=null&&e.nonceInfo!=null){let{nonceInstruction:u}=e.nonceInfo,f=u.keys[0].pubkey;i=(await r.confirmTransaction({abortSignal:n?.abortSignal,minContextSlot:e.minNonceContextSlot,nonceAccountPubkey:f,nonceValue:e.nonceInfo.nonce,signature:s},n&&n.commitment)).value}else n?.abortSignal!=null&&console.warn("sendAndConfirmTransaction(): A transaction with a deprecated confirmation strategy was supplied along with an `abortSignal`. Only transactions having `lastValidBlockHeight` or a combination of `nonceInfo` and `minNonceContextSlot` are abortable."),i=(await r.confirmTransaction(s,n&&n.commitment)).value;if(i.err)throw new Error(`Transaction ${s} failed (${JSON.stringify(i)})`);return s}function T6(r){return new Promise(e=>setTimeout(e,r))}function Ve(r,e){let t=r.layout.span>=0?r.layout.span:L_(r,e),n=Be.Buffer.alloc(t),o=Object.assign({instruction:r.index},e);return r.layout.encode(o,n),n}function O_(r){return fr([re({jsonrpc:qt("2.0"),id:pe(),result:r}),re({jsonrpc:qt("2.0"),id:pe(),error:re({code:ri(),message:pe(),data:ke(uE())})})])}function Ot(r){return ni(O_(r),D6,e=>"error"in e?e:{...e,result:ti(e.result,r)})}function Vr(r){return Ot(re({context:re({slot:H()}),value:r}))}function qu(r){return re({context:re({slot:H()}),value:r})}var Be,td,cr,di,T,z_,ka,w6,b6,v6,OZ,b_,rd,dd,S6,Ze,nd,Pu,M_,E6,bo,S_,Pe,qZ,hs,yd,Tu,od,sd,id,li,De,k6,fi,A6,I6,R6,B6,zu,N_,ds,Mu,Ou,E_,P6,Qe,tt,Kr,Mn,UZ,DZ,$h,Ea,FZ,jZ,HZ,Jh,z6,M6,__,L6,N6,hi,Or,wr,C6,ad,WZ,KZ,O6,q6,U6,VZ,GZ,Kt,C_,md,ZZ,D6,F6,YZ,j6,H6,W6,K6,ys,V6,G6,$Z,JZ,QZ,XZ,eY,tY,rY,nY,oY,sY,Z6,iY,aY,cd,cY,uY,gd,pY,Y6,$6,fY,lY,hY,dY,yY,J6,mY,Q6,gY,X6,xY,wY,bY,vY,k_,SY,eN,tN,EY,_Y,q_,xd,U_,D_,F_,j_,rN,nN,H_,W_,Lu,K_,Uu,wd,yi,ms,kY,AY,IY,RY,BY,PY,TY,zY,MY,LY,NY,CY,oN,OY,qY,UY,DY,sN,FY,ud,Sa,pd,Ru,fd,A_,I_,R_,B_,ld,iN,aN,P_,Xh,T_,cN,ed,hd,uN,_a,wo,jY,Nu,Bu,HY,Cu,WY,KY,VY,GY,Du=O(()=>{y();Be=Ke(Vi());FS();QS();td=Ke(Wa()),cr=Ke(Zn());XS();di=Ke(sE()),T=Ke(Ks()),z_=Ke(Ks()),ka=Ke(bh());pE();w6=Ke(ME()),b6=Ke(ZE()),v6=Ke($E());n_();o_();w_();io.sha512Sync=(...r)=>DS(io.concatBytes(...r));OZ=io.randomPrivateKey,b_=()=>{let r=io.randomPrivateKey(),e=rd(r),t=new Uint8Array(64);return t.set(r),t.set(e,32),{publicKey:e,secretKey:t}},rd=Hc.getPublicKey;dd=(r,e)=>Hc.sign(r,e.slice(0,32)),S6=Hc.verify,Ze=r=>Be.Buffer.isBuffer(r)?r:r instanceof Uint8Array?Be.Buffer.from(r.buffer,r.byteOffset,r.byteLength):Be.Buffer.from(r),nd=class{constructor(e){Object.assign(this,e)}encode(){return Be.Buffer.from((0,di.serialize)(Pu,this))}static decode(e){return(0,di.deserialize)(Pu,this,e)}static decodeUnchecked(e){return(0,di.deserializeUnchecked)(Pu,this,e)}},Pu=new Map,E6=32,bo=32;S_=1;M_=Symbol.toStringTag;Pe=class r extends nd{constructor(e){if(super({}),this._bn=void 0,_6(e))this._bn=e._bn;else{if(typeof e=="string"){let t=cr.default.decode(e);if(t.length!=bo)throw new Error("Invalid public key input");this._bn=new td.default(t)}else this._bn=new td.default(e);if(this._bn.byteLength()>bo)throw new Error("Invalid public key input")}}static unique(){let e=new r(S_);return S_+=1,new r(e.toBuffer())}equals(e){return this._bn.eq(e._bn)}toBase58(){return cr.default.encode(this.toBytes())}toJSON(){return this.toBase58()}toBytes(){let e=this.toBuffer();return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}toBuffer(){let e=this._bn.toArrayLike(Be.Buffer);if(e.length===bo)return e;let t=Be.Buffer.alloc(32);return e.copy(t,32-e.length),t}get[M_](){return`PublicKey(${this.toString()})`}toString(){return this.toBase58()}static async createWithSeed(e,t,n){let o=Be.Buffer.concat([e.toBuffer(),Be.Buffer.from(t),n.toBuffer()]),s=Kc(o);return new r(s)}static createProgramAddressSync(e,t){let n=Be.Buffer.alloc(0);e.forEach(function(s){if(s.length>E6)throw new TypeError("Max seed length exceeded");n=Be.Buffer.concat([n,Ze(s)])}),n=Be.Buffer.concat([n,t.toBuffer(),Be.Buffer.from("ProgramDerivedAddress")]);let o=Kc(n);if(v_(o))throw new Error("Invalid seeds, address must fall off the curve");return new r(o)}static async createProgramAddress(e,t){return this.createProgramAddressSync(e,t)}static findProgramAddressSync(e,t){let n=255,o;for(;n!=0;){try{let s=e.concat(Be.Buffer.from([n]));o=this.createProgramAddressSync(s,t)}catch(s){if(s instanceof TypeError)throw s;n--;continue}return[o,n]}throw new Error("Unable to find a viable program address nonce")}static async findProgramAddress(e,t){return this.findProgramAddressSync(e,t)}static isOnCurve(e){let t=new r(e);return v_(t.toBytes())}};Pe.default=new Pe("11111111111111111111111111111111");Pu.set(Pe,{kind:"struct",fields:[["_bn","u256"]]});qZ=new Pe("BPFLoader1111111111111111111111111111111111"),hs=1232,yd=127,Tu=64,od=class extends Error{constructor(e){super(`Signature ${e} has expired: block height exceeded.`),this.signature=void 0,this.signature=e}};Object.defineProperty(od.prototype,"name",{value:"TransactionExpiredBlockheightExceededError"});sd=class extends Error{constructor(e,t){super(`Transaction was not confirmed in ${t.toFixed(2)} seconds. It is unknown if it succeeded or failed. Check signature ${e} using the Solana Explorer or CLI tools.`),this.signature=void 0,this.signature=e}};Object.defineProperty(sd.prototype,"name",{value:"TransactionExpiredTimeoutError"});id=class extends Error{constructor(e){super(`Signature ${e} has expired: the nonce is no longer valid.`),this.signature=void 0,this.signature=e}};Object.defineProperty(id.prototype,"name",{value:"TransactionExpiredNonceInvalidError"});li=class{constructor(e,t){this.staticAccountKeys=void 0,this.accountKeysFromLookups=void 0,this.staticAccountKeys=e,this.accountKeysFromLookups=t}keySegments(){let e=[this.staticAccountKeys];return this.accountKeysFromLookups&&(e.push(this.accountKeysFromLookups.writable),e.push(this.accountKeysFromLookups.readonly)),e}get(e){for(let t of this.keySegments()){if(e<t.length)return t[e];e-=t.length}}get length(){return this.keySegments().flat().length}compileInstructions(e){if(this.length>256)throw new Error("Account index overflow encountered during compilation");let n=new Map;this.keySegments().flat().forEach((s,i)=>{n.set(s.toBase58(),i)});let o=s=>{let i=n.get(s.toBase58());if(i===void 0)throw new Error("Encountered an unknown instruction account key during compilation");return i};return e.map(s=>({programIdIndex:o(s.programId),accountKeyIndexes:s.keys.map(i=>o(i.pubkey)),data:s.data}))}},De=(r="publicKey")=>T.blob(32,r),k6=(r="signature")=>T.blob(64,r),fi=(r="string")=>{let e=T.struct([T.u32("length"),T.u32("lengthPadding"),T.blob(T.offset(T.u32(),-8),"chars")],r),t=e.decode.bind(e),n=e.encode.bind(e),o=e;return o.decode=(s,i)=>t(s,i).chars.toString(),o.encode=(s,i,u)=>{let f={chars:Be.Buffer.from(s,"utf8")};return n(f,i,u)},o.alloc=s=>T.u32().span+T.u32().span+Be.Buffer.from(s,"utf8").length,o},A6=(r="authorized")=>T.struct([De("staker"),De("withdrawer")],r),I6=(r="lockup")=>T.struct([T.ns64("unixTimestamp"),T.ns64("epoch"),De("custodian")],r),R6=(r="voteInit")=>T.struct([De("nodePubkey"),De("authorizedVoter"),De("authorizedWithdrawer"),T.u8("commission")],r),B6=(r="voteAuthorizeWithSeedArgs")=>T.struct([T.u32("voteAuthorizationType"),De("currentAuthorityDerivedKeyOwnerPubkey"),fi("currentAuthorityDerivedKeySeed"),De("newAuthorized")],r);zu=class r{constructor(e,t){this.payer=void 0,this.keyMetaMap=void 0,this.payer=e,this.keyMetaMap=t}static compile(e,t){let n=new Map,o=i=>{let u=i.toBase58(),f=n.get(u);return f===void 0&&(f={isSigner:!1,isWritable:!1,isInvoked:!1},n.set(u,f)),f},s=o(t);s.isSigner=!0,s.isWritable=!0;for(let i of e){o(i.programId).isInvoked=!0;for(let u of i.keys){let f=o(u.pubkey);f.isSigner||=u.isSigner,f.isWritable||=u.isWritable}}return new r(t,n)}getMessageComponents(){let e=[...this.keyMetaMap.entries()];Ct(e.length<=256,"Max static account keys length exceeded");let t=e.filter(([,f])=>f.isSigner&&f.isWritable),n=e.filter(([,f])=>f.isSigner&&!f.isWritable),o=e.filter(([,f])=>!f.isSigner&&f.isWritable),s=e.filter(([,f])=>!f.isSigner&&!f.isWritable),i={numRequiredSignatures:t.length+n.length,numReadonlySignedAccounts:n.length,numReadonlyUnsignedAccounts:s.length};{Ct(t.length>0,"Expected at least one writable signer key");let[f]=t[0];Ct(f===this.payer.toBase58(),"Expected first writable signer key to be the fee payer")}let u=[...t.map(([f])=>new Pe(f)),...n.map(([f])=>new Pe(f)),...o.map(([f])=>new Pe(f)),...s.map(([f])=>new Pe(f))];return[i,u]}extractTableLookup(e){let[t,n]=this.drainKeysFoundInLookupTable(e.state.addresses,i=>!i.isSigner&&!i.isInvoked&&i.isWritable),[o,s]=this.drainKeysFoundInLookupTable(e.state.addresses,i=>!i.isSigner&&!i.isInvoked&&!i.isWritable);if(!(t.length===0&&o.length===0))return[{accountKey:e.key,writableIndexes:t,readonlyIndexes:o},{writable:n,readonly:s}]}drainKeysFoundInLookupTable(e,t){let n=new Array,o=new Array;for(let[s,i]of this.keyMetaMap.entries())if(t(i)){let u=new Pe(s),f=e.findIndex(g=>g.equals(u));f>=0&&(Ct(f<256,"Max lookup table index exceeded"),n.push(f),o.push(u),this.keyMetaMap.delete(s))}return[n,o]}},N_="Reached end of buffer unexpectedly";ds=class r{constructor(e){this.header=void 0,this.accountKeys=void 0,this.recentBlockhash=void 0,this.instructions=void 0,this.indexToProgramIds=new Map,this.header=e.header,this.accountKeys=e.accountKeys.map(t=>new Pe(t)),this.recentBlockhash=e.recentBlockhash,this.instructions=e.instructions,this.instructions.forEach(t=>this.indexToProgramIds.set(t.programIdIndex,this.accountKeys[t.programIdIndex]))}get version(){return"legacy"}get staticAccountKeys(){return this.accountKeys}get compiledInstructions(){return this.instructions.map(e=>({programIdIndex:e.programIdIndex,accountKeyIndexes:e.accounts,data:cr.default.decode(e.data)}))}get addressTableLookups(){return[]}getAccountKeys(){return new li(this.staticAccountKeys)}static compile(e){let t=zu.compile(e.instructions,e.payerKey),[n,o]=t.getMessageComponents(),i=new li(o).compileInstructions(e.instructions).map(u=>({programIdIndex:u.programIdIndex,accounts:u.accountKeyIndexes,data:cr.default.encode(u.data)}));return new r({header:n,accountKeys:o,recentBlockhash:e.recentBlockhash,instructions:i})}isAccountSigner(e){return e<this.header.numRequiredSignatures}isAccountWritable(e){let t=this.header.numRequiredSignatures;if(e>=this.header.numRequiredSignatures){let n=e-t,s=this.accountKeys.length-t-this.header.numReadonlyUnsignedAccounts;return n<s}else{let n=t-this.header.numReadonlySignedAccounts;return e<n}}isProgramId(e){return this.indexToProgramIds.has(e)}programIds(){return[...this.indexToProgramIds.values()]}nonProgramIds(){return this.accountKeys.filter((e,t)=>!this.isProgramId(t))}serialize(){let e=this.accountKeys.length,t=[];Ar(t,e);let n=this.instructions.map(E=>{let{accounts:q,programIdIndex:C}=E,K=Array.from(cr.default.decode(E.data)),ee=[];Ar(ee,q.length);let $=[];return Ar($,K.length),{programIdIndex:C,keyIndicesCount:Be.Buffer.from(ee),keyIndices:q,dataLength:Be.Buffer.from($),data:K}}),o=[];Ar(o,n.length);let s=Be.Buffer.alloc(hs);Be.Buffer.from(o).copy(s);let i=o.length;n.forEach(E=>{let C=T.struct([T.u8("programIdIndex"),T.blob(E.keyIndicesCount.length,"keyIndicesCount"),T.seq(T.u8("keyIndex"),E.keyIndices.length,"keyIndices"),T.blob(E.dataLength.length,"dataLength"),T.seq(T.u8("userdatum"),E.data.length,"data")]).encode(E,s,i);i+=C}),s=s.slice(0,i);let u=T.struct([T.blob(1,"numRequiredSignatures"),T.blob(1,"numReadonlySignedAccounts"),T.blob(1,"numReadonlyUnsignedAccounts"),T.blob(t.length,"keyCount"),T.seq(De("key"),e,"keys"),De("recentBlockhash")]),f={numRequiredSignatures:Be.Buffer.from([this.header.numRequiredSignatures]),numReadonlySignedAccounts:Be.Buffer.from([this.header.numReadonlySignedAccounts]),numReadonlyUnsignedAccounts:Be.Buffer.from([this.header.numReadonlyUnsignedAccounts]),keyCount:Be.Buffer.from(t),keys:this.accountKeys.map(E=>Ze(E.toBytes())),recentBlockhash:cr.default.decode(this.recentBlockhash)},g=Be.Buffer.alloc(2048),b=u.encode(f,g);return s.copy(g,b),g.slice(0,b+s.length)}static from(e){let t=[...e],n=Ln(t);if(n!==(n&yd))throw new Error("Versioned messages must be deserialized with VersionedMessage.deserialize()");let o=Ln(t),s=Ln(t),i=kr(t),u=[];for(let q=0;q<i;q++){let C=Ir(t,0,bo);u.push(new Pe(Be.Buffer.from(C)))}let f=Ir(t,0,bo),g=kr(t),b=[];for(let q=0;q<g;q++){let C=Ln(t),K=kr(t),ee=Ir(t,0,K),$=kr(t),le=Ir(t,0,$),W=cr.default.encode(Be.Buffer.from(le));b.push({programIdIndex:C,accounts:ee,data:W})}let E={header:{numRequiredSignatures:n,numReadonlySignedAccounts:o,numReadonlyUnsignedAccounts:s},recentBlockhash:cr.default.encode(Be.Buffer.from(f)),accountKeys:u,instructions:b};return new r(E)}},Mu=class r{constructor(e){this.header=void 0,this.staticAccountKeys=void 0,this.recentBlockhash=void 0,this.compiledInstructions=void 0,this.addressTableLookups=void 0,this.header=e.header,this.staticAccountKeys=e.staticAccountKeys,this.recentBlockhash=e.recentBlockhash,this.compiledInstructions=e.compiledInstructions,this.addressTableLookups=e.addressTableLookups}get version(){return 0}get numAccountKeysFromLookups(){let e=0;for(let t of this.addressTableLookups)e+=t.readonlyIndexes.length+t.writableIndexes.length;return e}getAccountKeys(e){let t;if(e&&"accountKeysFromLookups"in e&&e.accountKeysFromLookups){if(this.numAccountKeysFromLookups!=e.accountKeysFromLookups.writable.length+e.accountKeysFromLookups.readonly.length)throw new Error("Failed to get account keys because of a mismatch in the number of account keys from lookups");t=e.accountKeysFromLookups}else if(e&&"addressLookupTableAccounts"in e&&e.addressLookupTableAccounts)t=this.resolveAddressTableLookups(e.addressLookupTableAccounts);else if(this.addressTableLookups.length>0)throw new Error("Failed to get account keys because address table lookups were not resolved");return new li(this.staticAccountKeys,t)}isAccountSigner(e){return e<this.header.numRequiredSignatures}isAccountWritable(e){let t=this.header.numRequiredSignatures,n=this.staticAccountKeys.length;if(e>=n){let o=e-n,s=this.addressTableLookups.reduce((i,u)=>i+u.writableIndexes.length,0);return o<s}else if(e>=this.header.numRequiredSignatures){let o=e-t,i=n-t-this.header.numReadonlyUnsignedAccounts;return o<i}else{let o=t-this.header.numReadonlySignedAccounts;return e<o}}resolveAddressTableLookups(e){let t={writable:[],readonly:[]};for(let n of this.addressTableLookups){let o=e.find(s=>s.key.equals(n.accountKey));if(!o)throw new Error(`Failed to find address lookup table account for table key ${n.accountKey.toBase58()}`);for(let s of n.writableIndexes)if(s<o.state.addresses.length)t.writable.push(o.state.addresses[s]);else throw new Error(`Failed to find address for index ${s} in address lookup table ${n.accountKey.toBase58()}`);for(let s of n.readonlyIndexes)if(s<o.state.addresses.length)t.readonly.push(o.state.addresses[s]);else throw new Error(`Failed to find address for index ${s} in address lookup table ${n.accountKey.toBase58()}`)}return t}static compile(e){let t=zu.compile(e.instructions,e.payerKey),n=new Array,o={writable:new Array,readonly:new Array},s=e.addressLookupTableAccounts||[];for(let b of s){let E=t.extractTableLookup(b);if(E!==void 0){let[q,{writable:C,readonly:K}]=E;n.push(q),o.writable.push(...C),o.readonly.push(...K)}}let[i,u]=t.getMessageComponents(),g=new li(u,o).compileInstructions(e.instructions);return new r({header:i,staticAccountKeys:u,recentBlockhash:e.recentBlockhash,compiledInstructions:g,addressTableLookups:n})}serialize(){let e=Array();Ar(e,this.staticAccountKeys.length);let t=this.serializeInstructions(),n=Array();Ar(n,this.compiledInstructions.length);let o=this.serializeAddressTableLookups(),s=Array();Ar(s,this.addressTableLookups.length);let i=T.struct([T.u8("prefix"),T.struct([T.u8("numRequiredSignatures"),T.u8("numReadonlySignedAccounts"),T.u8("numReadonlyUnsignedAccounts")],"header"),T.blob(e.length,"staticAccountKeysLength"),T.seq(De(),this.staticAccountKeys.length,"staticAccountKeys"),De("recentBlockhash"),T.blob(n.length,"instructionsLength"),T.blob(t.length,"serializedInstructions"),T.blob(s.length,"addressTableLookupsLength"),T.blob(o.length,"serializedAddressTableLookups")]),u=new Uint8Array(hs),g=i.encode({prefix:128,header:this.header,staticAccountKeysLength:new Uint8Array(e),staticAccountKeys:this.staticAccountKeys.map(b=>b.toBytes()),recentBlockhash:cr.default.decode(this.recentBlockhash),instructionsLength:new Uint8Array(n),serializedInstructions:t,addressTableLookupsLength:new Uint8Array(s),serializedAddressTableLookups:o},u);return u.slice(0,g)}serializeInstructions(){let e=0,t=new Uint8Array(hs);for(let n of this.compiledInstructions){let o=Array();Ar(o,n.accountKeyIndexes.length);let s=Array();Ar(s,n.data.length);let i=T.struct([T.u8("programIdIndex"),T.blob(o.length,"encodedAccountKeyIndexesLength"),T.seq(T.u8(),n.accountKeyIndexes.length,"accountKeyIndexes"),T.blob(s.length,"encodedDataLength"),T.blob(n.data.length,"data")]);e+=i.encode({programIdIndex:n.programIdIndex,encodedAccountKeyIndexesLength:new Uint8Array(o),accountKeyIndexes:n.accountKeyIndexes,encodedDataLength:new Uint8Array(s),data:n.data},t,e)}return t.slice(0,e)}serializeAddressTableLookups(){let e=0,t=new Uint8Array(hs);for(let n of this.addressTableLookups){let o=Array();Ar(o,n.writableIndexes.length);let s=Array();Ar(s,n.readonlyIndexes.length);let i=T.struct([De("accountKey"),T.blob(o.length,"encodedWritableIndexesLength"),T.seq(T.u8(),n.writableIndexes.length,"writableIndexes"),T.blob(s.length,"encodedReadonlyIndexesLength"),T.seq(T.u8(),n.readonlyIndexes.length,"readonlyIndexes")]);e+=i.encode({accountKey:n.accountKey.toBytes(),encodedWritableIndexesLength:new Uint8Array(o),writableIndexes:n.writableIndexes,encodedReadonlyIndexesLength:new Uint8Array(s),readonlyIndexes:n.readonlyIndexes},t,e)}return t.slice(0,e)}static deserialize(e){let t=[...e],n=Ln(t),o=n&yd;Ct(n!==o,"Expected versioned message but received legacy message");let s=o;Ct(s===0,`Expected versioned message with version 0 but found version ${s}`);let i={numRequiredSignatures:Ln(t),numReadonlySignedAccounts:Ln(t),numReadonlyUnsignedAccounts:Ln(t)},u=[],f=kr(t);for(let K=0;K<f;K++)u.push(new Pe(Ir(t,0,bo)));let g=cr.default.encode(Ir(t,0,bo)),b=kr(t),E=[];for(let K=0;K<b;K++){let ee=Ln(t),$=kr(t),le=Ir(t,0,$),W=kr(t),be=new Uint8Array(Ir(t,0,W));E.push({programIdIndex:ee,accountKeyIndexes:le,data:be})}let q=kr(t),C=[];for(let K=0;K<q;K++){let ee=new Pe(Ir(t,0,bo)),$=kr(t),le=Ir(t,0,$),W=kr(t),be=Ir(t,0,W);C.push({accountKey:ee,writableIndexes:le,readonlyIndexes:be})}return new r({header:i,staticAccountKeys:u,recentBlockhash:g,compiledInstructions:E,addressTableLookups:C})}},Ou={deserializeMessageVersion(r){let e=r[0],t=e&yd;return t===e?"legacy":t},deserialize:r=>{let e=Ou.deserializeMessageVersion(r);if(e==="legacy")return ds.from(r);if(e===0)return Mu.deserialize(r);throw new Error(`Transaction message version ${e} deserialization is not supported`)}};(function(r){r[r.BLOCKHEIGHT_EXCEEDED=0]="BLOCKHEIGHT_EXCEEDED",r[r.PROCESSED=1]="PROCESSED",r[r.TIMED_OUT=2]="TIMED_OUT",r[r.NONCE_INVALID=3]="NONCE_INVALID"})(E_||(E_={}));P6=Be.Buffer.alloc(Tu).fill(0),Qe=class{constructor(e){this.keys=void 0,this.programId=void 0,this.data=Be.Buffer.alloc(0),this.programId=e.programId,this.keys=e.keys,e.data&&(this.data=e.data)}toJSON(){return{keys:this.keys.map(({pubkey:e,isSigner:t,isWritable:n})=>({pubkey:e.toJSON(),isSigner:t,isWritable:n})),programId:this.programId.toJSON(),data:[...this.data]}}},tt=class r{get signature(){return this.signatures.length>0?this.signatures[0].signature:null}constructor(e){if(this.signatures=[],this.feePayer=void 0,this.instructions=[],this.recentBlockhash=void 0,this.lastValidBlockHeight=void 0,this.nonceInfo=void 0,this.minNonceContextSlot=void 0,this._message=void 0,this._json=void 0,!!e)if(e.feePayer&&(this.feePayer=e.feePayer),e.signatures&&(this.signatures=e.signatures),Object.prototype.hasOwnProperty.call(e,"nonceInfo")){let{minContextSlot:t,nonceInfo:n}=e;this.minNonceContextSlot=t,this.nonceInfo=n}else if(Object.prototype.hasOwnProperty.call(e,"lastValidBlockHeight")){let{blockhash:t,lastValidBlockHeight:n}=e;this.recentBlockhash=t,this.lastValidBlockHeight=n}else{let{recentBlockhash:t,nonceInfo:n}=e;n&&(this.nonceInfo=n),this.recentBlockhash=t}}toJSON(){return{recentBlockhash:this.recentBlockhash||null,feePayer:this.feePayer?this.feePayer.toJSON():null,nonceInfo:this.nonceInfo?{nonce:this.nonceInfo.nonce,nonceInstruction:this.nonceInfo.nonceInstruction.toJSON()}:null,instructions:this.instructions.map(e=>e.toJSON()),signers:this.signatures.map(({publicKey:e})=>e.toJSON())}}add(...e){if(e.length===0)throw new Error("No instructions");return e.forEach(t=>{"instructions"in t?this.instructions=this.instructions.concat(t.instructions):"data"in t&&"programId"in t&&"keys"in t?this.instructions.push(t):this.instructions.push(new Qe(t))}),this}compileMessage(){if(this._message&&JSON.stringify(this.toJSON())===JSON.stringify(this._json))return this._message;let e,t;if(this.nonceInfo?(e=this.nonceInfo.nonce,this.instructions[0]!=this.nonceInfo.nonceInstruction?t=[this.nonceInfo.nonceInstruction,...this.instructions]:t=this.instructions):(e=this.recentBlockhash,t=this.instructions),!e)throw new Error("Transaction recentBlockhash required");t.length<1&&console.warn("No instructions provided");let n;if(this.feePayer)n=this.feePayer;else if(this.signatures.length>0&&this.signatures[0].publicKey)n=this.signatures[0].publicKey;else throw new Error("Transaction fee payer required");for(let ee=0;ee<t.length;ee++)if(t[ee].programId===void 0)throw new Error(`Transaction instruction index ${ee} has undefined program id`);let o=[],s=[];t.forEach(ee=>{ee.keys.forEach(le=>{s.push({...le})});let $=ee.programId.toString();o.includes($)||o.push($)}),o.forEach(ee=>{s.push({pubkey:new Pe(ee),isSigner:!1,isWritable:!1})});let i=[];s.forEach(ee=>{let $=ee.pubkey.toString(),le=i.findIndex(W=>W.pubkey.toString()===$);le>-1?(i[le].isWritable=i[le].isWritable||ee.isWritable,i[le].isSigner=i[le].isSigner||ee.isSigner):i.push(ee)}),i.sort(function(ee,$){return ee.isSigner!==$.isSigner?ee.isSigner?-1:1:ee.isWritable!==$.isWritable?ee.isWritable?-1:1:ee.pubkey.toBase58().localeCompare($.pubkey.toBase58())});let u=i.findIndex(ee=>ee.pubkey.equals(n));if(u>-1){let[ee]=i.splice(u,1);ee.isSigner=!0,ee.isWritable=!0,i.unshift(ee)}else i.unshift({pubkey:n,isSigner:!0,isWritable:!0});for(let ee of this.signatures){let $=i.findIndex(le=>le.pubkey.equals(ee.publicKey));if($>-1)i[$].isSigner||(i[$].isSigner=!0,console.warn("Transaction references a signature that is unnecessary, only the fee payer and instruction signer accounts should sign a transaction. This behavior is deprecated and will throw an error in the next major version release."));else throw new Error(`unknown signer: ${ee.publicKey.toString()}`)}let f=0,g=0,b=0,E=[],q=[];i.forEach(({pubkey:ee,isSigner:$,isWritable:le})=>{$?(E.push(ee.toString()),f+=1,le||(g+=1)):(q.push(ee.toString()),le||(b+=1))});let C=E.concat(q),K=t.map(ee=>{let{data:$,programId:le}=ee;return{programIdIndex:C.indexOf(le.toString()),accounts:ee.keys.map(W=>C.indexOf(W.pubkey.toString())),data:cr.default.encode($)}});return K.forEach(ee=>{Ct(ee.programIdIndex>=0),ee.accounts.forEach($=>Ct($>=0))}),new ds({header:{numRequiredSignatures:f,numReadonlySignedAccounts:g,numReadonlyUnsignedAccounts:b},accountKeys:C,recentBlockhash:e,instructions:K})}_compile(){let e=this.compileMessage(),t=e.accountKeys.slice(0,e.header.numRequiredSignatures);return this.signatures.length===t.length&&this.signatures.every((o,s)=>t[s].equals(o.publicKey))||(this.signatures=t.map(n=>({signature:null,publicKey:n}))),e}serializeMessage(){return this._compile().serialize()}async getEstimatedFee(e){return(await e.getFeeForMessage(this.compileMessage())).value}setSigners(...e){if(e.length===0)throw new Error("No signers");let t=new Set;this.signatures=e.filter(n=>{let o=n.toString();return t.has(o)?!1:(t.add(o),!0)}).map(n=>({signature:null,publicKey:n}))}sign(...e){if(e.length===0)throw new Error("No signers");let t=new Set,n=[];for(let s of e){let i=s.publicKey.toString();t.has(i)||(t.add(i),n.push(s))}this.signatures=n.map(s=>({signature:null,publicKey:s.publicKey}));let o=this._compile();this._partialSign(o,...n)}partialSign(...e){if(e.length===0)throw new Error("No signers");let t=new Set,n=[];for(let s of e){let i=s.publicKey.toString();t.has(i)||(t.add(i),n.push(s))}let o=this._compile();this._partialSign(o,...n)}_partialSign(e,...t){let n=e.serialize();t.forEach(o=>{let s=dd(n,o.secretKey);this._addSignature(o.publicKey,Ze(s))})}addSignature(e,t){this._compile(),this._addSignature(e,t)}_addSignature(e,t){Ct(t.length===64);let n=this.signatures.findIndex(o=>e.equals(o.publicKey));if(n<0)throw new Error(`unknown signer: ${e.toString()}`);this.signatures[n].signature=Be.Buffer.from(t)}verifySignatures(e){return this._verifySignatures(this.serializeMessage(),e===void 0?!0:e)}_verifySignatures(e,t){for(let{signature:n,publicKey:o}of this.signatures)if(n===null){if(t)return!1}else if(!S6(n,e,o.toBytes()))return!1;return!0}serialize(e){let{requireAllSignatures:t,verifySignatures:n}=Object.assign({requireAllSignatures:!0,verifySignatures:!0},e),o=this.serializeMessage();if(n&&!this._verifySignatures(o,t))throw new Error("Signature verification failed");return this._serialize(o)}_serialize(e){let{signatures:t}=this,n=[];Ar(n,t.length);let o=n.length+t.length*64+e.length,s=Be.Buffer.alloc(o);return Ct(t.length<256),Be.Buffer.from(n).copy(s,0),t.forEach(({signature:i},u)=>{i!==null&&(Ct(i.length===64,"signature has invalid length"),Be.Buffer.from(i).copy(s,n.length+u*64))}),e.copy(s,n.length+t.length*64),Ct(s.length<=hs,`Transaction too large: ${s.length} > ${hs}`),s}get keys(){return Ct(this.instructions.length===1),this.instructions[0].keys.map(e=>e.pubkey)}get programId(){return Ct(this.instructions.length===1),this.instructions[0].programId}get data(){return Ct(this.instructions.length===1),this.instructions[0].data}static from(e){let t=[...e],n=kr(t),o=[];for(let s=0;s<n;s++){let i=Ir(t,0,Tu);o.push(cr.default.encode(Be.Buffer.from(i)))}return r.populate(ds.from(t),o)}static populate(e,t=[]){let n=new r;return n.recentBlockhash=e.recentBlockhash,e.header.numRequiredSignatures>0&&(n.feePayer=e.accountKeys[0]),t.forEach((o,s)=>{let i={signature:o==cr.default.encode(P6)?null:cr.default.decode(o),publicKey:e.accountKeys[s]};n.signatures.push(i)}),e.instructions.forEach(o=>{let s=o.accounts.map(i=>{let u=e.accountKeys[i];return{pubkey:u,isSigner:n.signatures.some(f=>f.publicKey.toString()===u.toString())||e.isAccountSigner(i),isWritable:e.isAccountWritable(i)}});n.instructions.push(new Qe({keys:s,programId:e.accountKeys[o.programIdIndex],data:cr.default.decode(o.data)}))}),n._message=e,n._json=n.toJSON(),n}},Kr=class r{get version(){return this.message.version}constructor(e,t){if(this.signatures=void 0,this.message=void 0,t!==void 0)Ct(t.length===e.header.numRequiredSignatures,"Expected signatures length to be equal to the number of required signatures"),this.signatures=t;else{let n=[];for(let o=0;o<e.header.numRequiredSignatures;o++)n.push(new Uint8Array(Tu));this.signatures=n}this.message=e}serialize(){let e=this.message.serialize(),t=Array();Ar(t,this.signatures.length);let n=T.struct([T.blob(t.length,"encodedSignaturesLength"),T.seq(k6(),this.signatures.length,"signatures"),T.blob(e.length,"serializedMessage")]),o=new Uint8Array(2048),s=n.encode({encodedSignaturesLength:new Uint8Array(t),signatures:this.signatures,serializedMessage:e},o);return o.slice(0,s)}static deserialize(e){let t=[...e],n=[],o=kr(t);for(let i=0;i<o;i++)n.push(new Uint8Array(Ir(t,0,Tu)));let s=Ou.deserialize(new Uint8Array(t));return new r(s,n)}sign(e){let t=this.message.serialize(),n=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures);for(let o of e){let s=n.findIndex(i=>i.equals(o.publicKey));Ct(s>=0,`Cannot sign with non signer key ${o.publicKey.toBase58()}`),this.signatures[s]=dd(t,o.secretKey)}}addSignature(e,t){Ct(t.byteLength===64,"Signature must be 64 bytes long");let o=this.message.staticAccountKeys.slice(0,this.message.header.numRequiredSignatures).findIndex(s=>s.equals(e));Ct(o>=0,`Can not add signature; \`${e.toBase58()}\` is not required to sign this transaction`),this.signatures[o]=t}},Mn=new Pe("SysvarC1ock11111111111111111111111111111111"),UZ=new Pe("SysvarEpochSchedu1e111111111111111111111111"),DZ=new Pe("Sysvar1nstructions1111111111111111111111111"),$h=new Pe("SysvarRecentB1ockHashes11111111111111111111"),Ea=new Pe("SysvarRent111111111111111111111111111111111"),FZ=new Pe("SysvarRewards111111111111111111111111111111"),jZ=new Pe("SysvarS1otHashes111111111111111111111111111"),HZ=new Pe("SysvarS1otHistory11111111111111111111111111"),Jh=new Pe("SysvarStakeHistory1111111111111111111111111");z6=T.nu64("lamportsPerSignature"),M6=T.struct([T.u32("version"),T.u32("state"),De("authorizedPubkey"),De("nonce"),T.struct([z6],"feeCalculator")]),__=M6.span,L6=r=>{let e=r.decode.bind(r),t=r.encode.bind(r);return{decode:e,encode:t}},N6=r=>e=>{let t=(0,z_.blob)(r,e),{encode:n,decode:o}=L6(t),s=t;return s.decode=(i,u)=>{let f=o(i,u);return(0,ka.toBigIntLE)(Be.Buffer.from(f))},s.encode=(i,u,f)=>{let g=(0,ka.toBufferLE)(i,r);return n(g,u,f)},s},hi=N6(8),Or=Object.freeze({Create:{index:0,layout:T.struct([T.u32("instruction"),T.ns64("lamports"),T.ns64("space"),De("programId")])},Assign:{index:1,layout:T.struct([T.u32("instruction"),De("programId")])},Transfer:{index:2,layout:T.struct([T.u32("instruction"),hi("lamports")])},CreateWithSeed:{index:3,layout:T.struct([T.u32("instruction"),De("base"),fi("seed"),T.ns64("lamports"),T.ns64("space"),De("programId")])},AdvanceNonceAccount:{index:4,layout:T.struct([T.u32("instruction")])},WithdrawNonceAccount:{index:5,layout:T.struct([T.u32("instruction"),T.ns64("lamports")])},InitializeNonceAccount:{index:6,layout:T.struct([T.u32("instruction"),De("authorized")])},AuthorizeNonceAccount:{index:7,layout:T.struct([T.u32("instruction"),De("authorized")])},Allocate:{index:8,layout:T.struct([T.u32("instruction"),T.ns64("space")])},AllocateWithSeed:{index:9,layout:T.struct([T.u32("instruction"),De("base"),fi("seed"),T.ns64("space"),De("programId")])},AssignWithSeed:{index:10,layout:T.struct([T.u32("instruction"),De("base"),fi("seed"),De("programId")])},TransferWithSeed:{index:11,layout:T.struct([T.u32("instruction"),hi("lamports"),fi("seed"),De("programId")])},UpgradeNonceAccount:{index:12,layout:T.struct([T.u32("instruction")])}}),wr=class r{constructor(){}static createAccount(e){let t=Or.Create,n=Ve(t,{lamports:e.lamports,space:e.space,programId:Ze(e.programId.toBuffer())});return new Qe({keys:[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.newAccountPubkey,isSigner:!0,isWritable:!0}],programId:this.programId,data:n})}static transfer(e){let t,n;if("basePubkey"in e){let o=Or.TransferWithSeed;t=Ve(o,{lamports:BigInt(e.lamports),seed:e.seed,programId:Ze(e.programId.toBuffer())}),n=[{pubkey:e.fromPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0}]}else{let o=Or.Transfer;t=Ve(o,{lamports:BigInt(e.lamports)}),n=[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0}]}return new Qe({keys:n,programId:this.programId,data:t})}static assign(e){let t,n;if("basePubkey"in e){let o=Or.AssignWithSeed;t=Ve(o,{base:Ze(e.basePubkey.toBuffer()),seed:e.seed,programId:Ze(e.programId.toBuffer())}),n=[{pubkey:e.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1}]}else{let o=Or.Assign;t=Ve(o,{programId:Ze(e.programId.toBuffer())}),n=[{pubkey:e.accountPubkey,isSigner:!0,isWritable:!0}]}return new Qe({keys:n,programId:this.programId,data:t})}static createAccountWithSeed(e){let t=Or.CreateWithSeed,n=Ve(t,{base:Ze(e.basePubkey.toBuffer()),seed:e.seed,lamports:e.lamports,space:e.space,programId:Ze(e.programId.toBuffer())}),o=[{pubkey:e.fromPubkey,isSigner:!0,isWritable:!0},{pubkey:e.newAccountPubkey,isSigner:!1,isWritable:!0}];return e.basePubkey!=e.fromPubkey&&o.push({pubkey:e.basePubkey,isSigner:!0,isWritable:!1}),new Qe({keys:o,programId:this.programId,data:n})}static createNonceAccount(e){let t=new tt;"basePubkey"in e&&"seed"in e?t.add(r.createAccountWithSeed({fromPubkey:e.fromPubkey,newAccountPubkey:e.noncePubkey,basePubkey:e.basePubkey,seed:e.seed,lamports:e.lamports,space:__,programId:this.programId})):t.add(r.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.noncePubkey,lamports:e.lamports,space:__,programId:this.programId}));let n={noncePubkey:e.noncePubkey,authorizedPubkey:e.authorizedPubkey};return t.add(this.nonceInitialize(n)),t}static nonceInitialize(e){let t=Or.InitializeNonceAccount,n=Ve(t,{authorized:Ze(e.authorizedPubkey.toBuffer())}),o={keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:$h,isSigner:!1,isWritable:!1},{pubkey:Ea,isSigner:!1,isWritable:!1}],programId:this.programId,data:n};return new Qe(o)}static nonceAdvance(e){let t=Or.AdvanceNonceAccount,n=Ve(t),o={keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:$h,isSigner:!1,isWritable:!1},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:n};return new Qe(o)}static nonceWithdraw(e){let t=Or.WithdrawNonceAccount,n=Ve(t,{lamports:e.lamports});return new Qe({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:e.toPubkey,isSigner:!1,isWritable:!0},{pubkey:$h,isSigner:!1,isWritable:!1},{pubkey:Ea,isSigner:!1,isWritable:!1},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static nonceAuthorize(e){let t=Or.AuthorizeNonceAccount,n=Ve(t,{authorized:Ze(e.newAuthorizedPubkey.toBuffer())});return new Qe({keys:[{pubkey:e.noncePubkey,isSigner:!1,isWritable:!0},{pubkey:e.authorizedPubkey,isSigner:!0,isWritable:!1}],programId:this.programId,data:n})}static allocate(e){let t,n;if("basePubkey"in e){let o=Or.AllocateWithSeed;t=Ve(o,{base:Ze(e.basePubkey.toBuffer()),seed:e.seed,space:e.space,programId:Ze(e.programId.toBuffer())}),n=[{pubkey:e.accountPubkey,isSigner:!1,isWritable:!0},{pubkey:e.basePubkey,isSigner:!0,isWritable:!1}]}else{let o=Or.Allocate;t=Ve(o,{space:e.space}),n=[{pubkey:e.accountPubkey,isSigner:!0,isWritable:!0}]}return new Qe({keys:n,programId:this.programId,data:t})}};wr.programId=new Pe("11111111111111111111111111111111");C6=hs-300,ad=class r{constructor(){}static getMinNumSignatures(e){return 2*(Math.ceil(e/r.chunkSize)+1+1)}static async load(e,t,n,o,s){{let E=await e.getMinimumBalanceForRentExemption(s.length),q=await e.getAccountInfo(n.publicKey,"confirmed"),C=null;if(q!==null){if(q.executable)return console.error("Program load failed, account is already executable"),!1;q.data.length!==s.length&&(C=C||new tt,C.add(wr.allocate({accountPubkey:n.publicKey,space:s.length}))),q.owner.equals(o)||(C=C||new tt,C.add(wr.assign({accountPubkey:n.publicKey,programId:o}))),q.lamports<E&&(C=C||new tt,C.add(wr.transfer({fromPubkey:t.publicKey,toPubkey:n.publicKey,lamports:E-q.lamports})))}else C=new tt().add(wr.createAccount({fromPubkey:t.publicKey,newAccountPubkey:n.publicKey,lamports:E>0?E:1,space:s.length,programId:o}));C!==null&&await Qh(e,C,[t,n],{commitment:"confirmed"})}let i=T.struct([T.u32("instruction"),T.u32("offset"),T.u32("bytesLength"),T.u32("bytesLengthPadding"),T.seq(T.u8("byte"),T.offset(T.u32(),-8),"bytes")]),u=r.chunkSize,f=0,g=s,b=[];for(;g.length>0;){let E=g.slice(0,u),q=Be.Buffer.alloc(u+16);i.encode({instruction:0,offset:f,bytes:E,bytesLength:0,bytesLengthPadding:0},q);let C=new tt().add({keys:[{pubkey:n.publicKey,isSigner:!0,isWritable:!0}],programId:o,data:q});b.push(Qh(e,C,[t,n],{commitment:"confirmed"})),e._rpcEndpoint.includes("solana.com")&&await T6(1e3/4),f+=u,g=g.slice(u)}await Promise.all(b);{let E=T.struct([T.u32("instruction")]),q=Be.Buffer.alloc(E.span);E.encode({instruction:1},q);let C=new tt().add({keys:[{pubkey:n.publicKey,isSigner:!0,isWritable:!0},{pubkey:Ea,isSigner:!1,isWritable:!1}],programId:o,data:q});await Qh(e,C,[t,n],{commitment:"confirmed"})}return!0}};ad.chunkSize=C6;WZ=new Pe("BPFLoader2111111111111111111111111111111111"),KZ=globalThis.fetch,O6=160,q6=64,U6=O6/q6,VZ=1e3/U6,GZ={index:1,layout:T.struct([T.u32("typeIndex"),hi("deactivationSlot"),T.nu64("lastExtendedSlot"),T.u8("lastExtendedStartIndex"),T.u8(),T.seq(De(),T.offset(T.u8(),-1),"authority")])},Kt=ni(Xc(Pe),pe(),r=>new Pe(r)),C_=eu([pe(),qt("base64")]),md=ni(Xc(Be.Buffer),C_,r=>Be.Buffer.from(r[0],"base64")),ZZ=30*1e3;D6=O_(ri());F6=re({foundation:H(),foundationTerm:H(),initial:H(),taper:H(),terminal:H()}),YZ=Ot(xe(ye(re({epoch:H(),effectiveSlot:H(),amount:H(),postBalance:H(),commission:ke(ye(H()))})))),j6=re({total:H(),validator:H(),foundation:H(),epoch:H()}),H6=re({epoch:H(),slotIndex:H(),slotsInEpoch:H(),absoluteSlot:H(),blockHeight:ke(H()),transactionCount:ke(H())}),W6=re({slotsPerEpoch:H(),leaderScheduleSlotOffset:H(),warmup:fn(),firstNormalEpoch:H(),firstNormalSlot:H()}),K6=Eh(pe(),xe(H())),ys=ye(fr([re({}),pe()])),V6=re({err:ys}),G6=qt("receivedSignature"),$Z=re({"solana-core":pe(),"feature-set":ke(H())}),JZ=Vr(re({err:ye(fr([re({}),pe()])),logs:ye(xe(pe())),accounts:ke(ye(xe(ye(re({executable:fn(),owner:pe(),lamports:H(),data:xe(pe()),rentEpoch:ke(H())}))))),unitsConsumed:ke(H()),returnData:ke(ye(re({programId:pe(),data:eu([pe(),qt("base64")])})))})),QZ=Vr(re({byIdentity:Eh(pe(),xe(H())),range:re({firstSlot:H(),lastSlot:H()})})),XZ=Ot(F6),eY=Ot(j6),tY=Ot(H6),rY=Ot(W6),nY=Ot(K6),oY=Ot(H()),sY=Vr(re({total:H(),circulating:H(),nonCirculating:H(),nonCirculatingAccounts:xe(Kt)})),Z6=re({amount:pe(),uiAmount:ye(H()),decimals:H(),uiAmountString:ke(pe())}),iY=Vr(xe(re({address:Kt,amount:pe(),uiAmount:ye(H()),decimals:H(),uiAmountString:ke(pe())}))),aY=Vr(xe(re({pubkey:Kt,account:re({executable:fn(),owner:Kt,lamports:H(),data:md,rentEpoch:H()})}))),cd=re({program:pe(),parsed:ri(),space:H()}),cY=Vr(xe(re({pubkey:Kt,account:re({executable:fn(),owner:Kt,lamports:H(),data:cd,rentEpoch:H()})}))),uY=Vr(xe(re({lamports:H(),address:Kt}))),gd=re({executable:fn(),owner:Kt,lamports:H(),data:md,rentEpoch:H()}),pY=re({pubkey:Kt,account:gd}),Y6=ni(fr([Xc(Be.Buffer),cd]),fr([C_,cd]),r=>Array.isArray(r)?ti(r,md):r),$6=re({executable:fn(),owner:Kt,lamports:H(),data:Y6,rentEpoch:H()}),fY=re({pubkey:Kt,account:$6}),lY=re({state:fr([qt("active"),qt("inactive"),qt("activating"),qt("deactivating")]),active:H(),inactive:H()}),hY=Ot(xe(re({signature:pe(),slot:H(),err:ys,memo:ye(pe()),blockTime:ke(ye(H()))}))),dY=Ot(xe(re({signature:pe(),slot:H(),err:ys,memo:ye(pe()),blockTime:ke(ye(H()))}))),yY=re({subscription:H(),result:qu(gd)}),J6=re({pubkey:Kt,account:gd}),mY=re({subscription:H(),result:qu(J6)}),Q6=re({parent:H(),slot:H(),root:H()}),gY=re({subscription:H(),result:Q6}),X6=fr([re({type:fr([qt("firstShredReceived"),qt("completed"),qt("optimisticConfirmation"),qt("root")]),slot:H(),timestamp:H()}),re({type:qt("createdBank"),parent:H(),slot:H(),timestamp:H()}),re({type:qt("frozen"),slot:H(),timestamp:H(),stats:re({numTransactionEntries:H(),numSuccessfulTransactions:H(),numFailedTransactions:H(),maxTransactionsPerEntry:H()})}),re({type:qt("dead"),slot:H(),timestamp:H(),err:pe()})]),xY=re({subscription:H(),result:X6}),wY=re({subscription:H(),result:qu(fr([V6,G6]))}),bY=re({subscription:H(),result:H()}),vY=re({pubkey:pe(),gossip:ye(pe()),tpu:ye(pe()),rpc:ye(pe()),version:ye(pe())}),k_=re({votePubkey:pe(),nodePubkey:pe(),activatedStake:H(),epochVoteAccount:fn(),epochCredits:xe(eu([H(),H(),H()])),commission:H(),lastVote:H(),rootSlot:ye(H())}),SY=Ot(re({current:xe(k_),delinquent:xe(k_)})),eN=fr([qt("processed"),qt("confirmed"),qt("finalized")]),tN=re({slot:H(),confirmations:ye(H()),err:ys,confirmationStatus:ke(eN)}),EY=Vr(xe(ye(tN))),_Y=Ot(H()),q_=re({accountKey:Kt,writableIndexes:xe(H()),readonlyIndexes:xe(H())}),xd=re({signatures:xe(pe()),message:re({accountKeys:xe(pe()),header:re({numRequiredSignatures:H(),numReadonlySignedAccounts:H(),numReadonlyUnsignedAccounts:H()}),instructions:xe(re({accounts:xe(H()),data:pe(),programIdIndex:H()})),recentBlockhash:pe(),addressTableLookups:ke(xe(q_))})}),U_=re({pubkey:Kt,signer:fn(),writable:fn(),source:ke(fr([qt("transaction"),qt("lookupTable")]))}),D_=re({accountKeys:xe(U_),signatures:xe(pe())}),F_=re({parsed:ri(),program:pe(),programId:Kt}),j_=re({accounts:xe(Kt),data:pe(),programId:Kt}),rN=fr([j_,F_]),nN=fr([re({parsed:ri(),program:pe(),programId:pe()}),re({accounts:xe(pe()),data:pe(),programId:pe()})]),H_=ni(rN,nN,r=>"accounts"in r?ti(r,j_):ti(r,F_)),W_=re({signatures:xe(pe()),message:re({accountKeys:xe(U_),instructions:xe(H_),recentBlockhash:pe(),addressTableLookups:ke(ye(xe(q_)))})}),Lu=re({accountIndex:H(),mint:pe(),owner:ke(pe()),uiTokenAmount:Z6}),K_=re({writable:xe(Kt),readonly:xe(Kt)}),Uu=re({err:ys,fee:H(),innerInstructions:ke(ye(xe(re({index:H(),instructions:xe(re({accounts:xe(H()),data:pe(),programIdIndex:H()}))})))),preBalances:xe(H()),postBalances:xe(H()),logMessages:ke(ye(xe(pe()))),preTokenBalances:ke(ye(xe(Lu))),postTokenBalances:ke(ye(xe(Lu))),loadedAddresses:ke(K_),computeUnitsConsumed:ke(H())}),wd=re({err:ys,fee:H(),innerInstructions:ke(ye(xe(re({index:H(),instructions:xe(H_)})))),preBalances:xe(H()),postBalances:xe(H()),logMessages:ke(ye(xe(pe()))),preTokenBalances:ke(ye(xe(Lu))),postTokenBalances:ke(ye(xe(Lu))),loadedAddresses:ke(K_),computeUnitsConsumed:ke(H())}),yi=fr([qt(0),qt("legacy")]),ms=re({pubkey:pe(),lamports:H(),postBalance:ye(H()),rewardType:ye(pe()),commission:ke(ye(H()))}),kY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),transactions:xe(re({transaction:xd,meta:ye(Uu),version:ke(yi)})),rewards:ke(xe(ms)),blockTime:ye(H()),blockHeight:ye(H())}))),AY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),rewards:ke(xe(ms)),blockTime:ye(H()),blockHeight:ye(H())}))),IY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),transactions:xe(re({transaction:D_,meta:ye(Uu),version:ke(yi)})),rewards:ke(xe(ms)),blockTime:ye(H()),blockHeight:ye(H())}))),RY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),transactions:xe(re({transaction:W_,meta:ye(wd),version:ke(yi)})),rewards:ke(xe(ms)),blockTime:ye(H()),blockHeight:ye(H())}))),BY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),transactions:xe(re({transaction:D_,meta:ye(wd),version:ke(yi)})),rewards:ke(xe(ms)),blockTime:ye(H()),blockHeight:ye(H())}))),PY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),rewards:ke(xe(ms)),blockTime:ye(H()),blockHeight:ye(H())}))),TY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),transactions:xe(re({transaction:xd,meta:ye(Uu)})),rewards:ke(xe(ms)),blockTime:ye(H())}))),zY=Ot(ye(re({blockhash:pe(),previousBlockhash:pe(),parentSlot:H(),signatures:xe(pe()),blockTime:ye(H())}))),MY=Ot(ye(re({slot:H(),meta:Uu,blockTime:ke(ye(H())),transaction:xd,version:ke(yi)}))),LY=Ot(ye(re({slot:H(),transaction:W_,meta:ye(wd),blockTime:ke(ye(H())),version:ke(yi)}))),NY=Vr(re({blockhash:pe(),feeCalculator:re({lamportsPerSignature:H()})})),CY=Vr(re({blockhash:pe(),lastValidBlockHeight:H()})),oN=re({slot:H(),numTransactions:H(),numSlots:H(),samplePeriodSecs:H()}),OY=Ot(xe(oN)),qY=Vr(ye(re({feeCalculator:re({lamportsPerSignature:H()})}))),UY=Ot(pe()),DY=Ot(pe()),sN=re({err:ys,logs:xe(pe()),signature:pe()}),FY=re({result:qu(sN),subscription:H()}),ud=class r{constructor(e){this._keypair=void 0,this._keypair=e??b_()}static generate(){return new r(b_())}static fromSecretKey(e,t){if(e.byteLength!==64)throw new Error("bad secret key size");let n=e.slice(32,64);if(!t||!t.skipValidation){let o=e.slice(0,32),s=rd(o);for(let i=0;i<32;i++)if(n[i]!==s[i])throw new Error("provided secretKey is invalid")}return new r({publicKey:n,secretKey:e})}static fromSeed(e){let t=rd(e),n=new Uint8Array(64);return n.set(e),n.set(t,32),new r({publicKey:t,secretKey:n})}get publicKey(){return new Pe(this._keypair.publicKey)}get secretKey(){return new Uint8Array(this._keypair.secretKey)}},Sa=Object.freeze({CreateLookupTable:{index:0,layout:T.struct([T.u32("instruction"),hi("recentSlot"),T.u8("bumpSeed")])},FreezeLookupTable:{index:1,layout:T.struct([T.u32("instruction")])},ExtendLookupTable:{index:2,layout:T.struct([T.u32("instruction"),hi(),T.seq(De(),T.offset(T.u32(),-8),"addresses")])},DeactivateLookupTable:{index:3,layout:T.struct([T.u32("instruction")])},CloseLookupTable:{index:4,layout:T.struct([T.u32("instruction")])}}),pd=class{constructor(){}static createLookupTable(e){let[t,n]=Pe.findProgramAddressSync([e.authority.toBuffer(),(0,ka.toBufferLE)(BigInt(e.recentSlot),8)],this.programId),o=Sa.CreateLookupTable,s=Ve(o,{recentSlot:BigInt(e.recentSlot),bumpSeed:n}),i=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1},{pubkey:e.payer,isSigner:!0,isWritable:!0},{pubkey:wr.programId,isSigner:!1,isWritable:!1}];return[new Qe({programId:this.programId,keys:i,data:s}),t]}static freezeLookupTable(e){let t=Sa.FreezeLookupTable,n=Ve(t),o=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return new Qe({programId:this.programId,keys:o,data:n})}static extendLookupTable(e){let t=Sa.ExtendLookupTable,n=Ve(t,{addresses:e.addresses.map(s=>s.toBytes())}),o=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return e.payer&&o.push({pubkey:e.payer,isSigner:!0,isWritable:!0},{pubkey:wr.programId,isSigner:!1,isWritable:!1}),new Qe({programId:this.programId,keys:o,data:n})}static deactivateLookupTable(e){let t=Sa.DeactivateLookupTable,n=Ve(t),o=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1}];return new Qe({programId:this.programId,keys:o,data:n})}static closeLookupTable(e){let t=Sa.CloseLookupTable,n=Ve(t),o=[{pubkey:e.lookupTable,isSigner:!1,isWritable:!0},{pubkey:e.authority,isSigner:!0,isWritable:!1},{pubkey:e.recipient,isSigner:!1,isWritable:!0}];return new Qe({programId:this.programId,keys:o,data:n})}};pd.programId=new Pe("AddressLookupTab1e1111111111111111111111111");Ru=Object.freeze({RequestUnits:{index:0,layout:T.struct([T.u8("instruction"),T.u32("units"),T.u32("additionalFee")])},RequestHeapFrame:{index:1,layout:T.struct([T.u8("instruction"),T.u32("bytes")])},SetComputeUnitLimit:{index:2,layout:T.struct([T.u8("instruction"),T.u32("units")])},SetComputeUnitPrice:{index:3,layout:T.struct([T.u8("instruction"),hi("microLamports")])}}),fd=class{constructor(){}static requestUnits(e){let t=Ru.RequestUnits,n=Ve(t,e);return new Qe({keys:[],programId:this.programId,data:n})}static requestHeapFrame(e){let t=Ru.RequestHeapFrame,n=Ve(t,e);return new Qe({keys:[],programId:this.programId,data:n})}static setComputeUnitLimit(e){let t=Ru.SetComputeUnitLimit,n=Ve(t,e);return new Qe({keys:[],programId:this.programId,data:n})}static setComputeUnitPrice(e){let t=Ru.SetComputeUnitPrice,n=Ve(t,{microLamports:BigInt(e.microLamports)});return new Qe({keys:[],programId:this.programId,data:n})}};fd.programId=new Pe("ComputeBudget111111111111111111111111111111");A_=64,I_=32,R_=64,B_=T.struct([T.u8("numSignatures"),T.u8("padding"),T.u16("signatureOffset"),T.u16("signatureInstructionIndex"),T.u16("publicKeyOffset"),T.u16("publicKeyInstructionIndex"),T.u16("messageDataOffset"),T.u16("messageDataSize"),T.u16("messageInstructionIndex")]),ld=class r{constructor(){}static createInstructionWithPublicKey(e){let{publicKey:t,message:n,signature:o,instructionIndex:s}=e;Ct(t.length===I_,`Public Key must be ${I_} bytes but received ${t.length} bytes`),Ct(o.length===R_,`Signature must be ${R_} bytes but received ${o.length} bytes`);let i=B_.span,u=i+t.length,f=u+o.length,g=1,b=Be.Buffer.alloc(f+n.length),E=s??65535;return B_.encode({numSignatures:g,padding:0,signatureOffset:u,signatureInstructionIndex:E,publicKeyOffset:i,publicKeyInstructionIndex:E,messageDataOffset:f,messageDataSize:n.length,messageInstructionIndex:E},b),b.fill(t,i),b.fill(o,u),b.fill(n,f),new Qe({keys:[],programId:r.programId,data:b})}static createInstructionWithPrivateKey(e){let{privateKey:t,message:n,instructionIndex:o}=e;Ct(t.length===A_,`Private key must be ${A_} bytes but received ${t.length} bytes`);try{let s=ud.fromSecretKey(t),i=s.publicKey.toBytes(),u=dd(n,s.secretKey);return this.createInstructionWithPublicKey({publicKey:i,message:n,signature:u,instructionIndex:o})}catch(s){throw new Error(`Error creating instruction; ${s}`)}}};ld.programId=new Pe("Ed25519SigVerify111111111111111111111111111");ln.hmacSha256Sync=(r,...e)=>{let t=Kh.create(Kc,r);return e.forEach(n=>t.update(n)),t.digest()};iN=(r,e)=>x_(r,e,{der:!1,recovered:!0});ln.isValidPrivateKey;aN=y_,P_=32,Xh=20,T_=64,cN=11,ed=T.struct([T.u8("numSignatures"),T.u16("signatureOffset"),T.u8("signatureInstructionIndex"),T.u16("ethAddressOffset"),T.u8("ethAddressInstructionIndex"),T.u16("messageDataOffset"),T.u16("messageDataSize"),T.u8("messageInstructionIndex"),T.blob(20,"ethAddress"),T.blob(64,"signature"),T.u8("recoveryId")]),hd=class r{constructor(){}static publicKeyToEthAddress(e){Ct(e.length===T_,`Public key must be ${T_} bytes but received ${e.length} bytes`);try{return Be.Buffer.from(Wh(Ze(e))).slice(-Xh)}catch(t){throw new Error(`Error constructing Ethereum address: ${t}`)}}static createInstructionWithPublicKey(e){let{publicKey:t,message:n,signature:o,recoveryId:s,instructionIndex:i}=e;return r.createInstructionWithEthAddress({ethAddress:r.publicKeyToEthAddress(t),message:n,signature:o,recoveryId:s,instructionIndex:i})}static createInstructionWithEthAddress(e){let{ethAddress:t,message:n,signature:o,recoveryId:s,instructionIndex:i=0}=e,u;typeof t=="string"?t.startsWith("0x")?u=Be.Buffer.from(t.substr(2),"hex"):u=Be.Buffer.from(t,"hex"):u=t,Ct(u.length===Xh,`Address must be ${Xh} bytes but received ${u.length} bytes`);let f=1+cN,g=f,b=f+u.length,E=b+o.length+1,q=1,C=Be.Buffer.alloc(ed.span+n.length);return ed.encode({numSignatures:q,signatureOffset:b,signatureInstructionIndex:i,ethAddressOffset:g,ethAddressInstructionIndex:i,messageDataOffset:E,messageDataSize:n.length,messageInstructionIndex:i,signature:Ze(o),ethAddress:Ze(u),recoveryId:s},C),C.fill(Ze(n),ed.span),new Qe({keys:[],programId:r.programId,data:C})}static createInstructionWithPrivateKey(e){let{privateKey:t,message:n,instructionIndex:o}=e;Ct(t.length===P_,`Private key must be ${P_} bytes but received ${t.length} bytes`);try{let s=Ze(t),i=aN(s,!1).slice(1),u=Be.Buffer.from(Wh(Ze(n))),[f,g]=iN(u,s);return this.createInstructionWithPublicKey({publicKey:i,message:n,signature:f,recoveryId:g,instructionIndex:o})}catch(s){throw new Error(`Error creating instruction; ${s}`)}}};hd.programId=new Pe("KeccakSecp256k11111111111111111111111111111");uN=new Pe("StakeConfig11111111111111111111111111111111"),_a=class{constructor(e,t,n){this.unixTimestamp=void 0,this.epoch=void 0,this.custodian=void 0,this.unixTimestamp=e,this.epoch=t,this.custodian=n}};_a.default=new _a(0,0,Pe.default);wo=Object.freeze({Initialize:{index:0,layout:T.struct([T.u32("instruction"),A6(),I6()])},Authorize:{index:1,layout:T.struct([T.u32("instruction"),De("newAuthorized"),T.u32("stakeAuthorizationType")])},Delegate:{index:2,layout:T.struct([T.u32("instruction")])},Split:{index:3,layout:T.struct([T.u32("instruction"),T.ns64("lamports")])},Withdraw:{index:4,layout:T.struct([T.u32("instruction"),T.ns64("lamports")])},Deactivate:{index:5,layout:T.struct([T.u32("instruction")])},Merge:{index:7,layout:T.struct([T.u32("instruction")])},AuthorizeWithSeed:{index:8,layout:T.struct([T.u32("instruction"),De("newAuthorized"),T.u32("stakeAuthorizationType"),fi("authoritySeed"),De("authorityOwner")])}}),jY=Object.freeze({Staker:{index:0},Withdrawer:{index:1}}),Nu=class{constructor(){}static initialize(e){let{stakePubkey:t,authorized:n,lockup:o}=e,s=o||_a.default,i=wo.Initialize,u=Ve(i,{authorized:{staker:Ze(n.staker.toBuffer()),withdrawer:Ze(n.withdrawer.toBuffer())},lockup:{unixTimestamp:s.unixTimestamp,epoch:s.epoch,custodian:Ze(s.custodian.toBuffer())}}),f={keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Ea,isSigner:!1,isWritable:!1}],programId:this.programId,data:u};return new Qe(f)}static createAccountWithSeed(e){let t=new tt;t.add(wr.createAccountWithSeed({fromPubkey:e.fromPubkey,newAccountPubkey:e.stakePubkey,basePubkey:e.basePubkey,seed:e.seed,lamports:e.lamports,space:this.space,programId:this.programId}));let{stakePubkey:n,authorized:o,lockup:s}=e;return t.add(this.initialize({stakePubkey:n,authorized:o,lockup:s}))}static createAccount(e){let t=new tt;t.add(wr.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.stakePubkey,lamports:e.lamports,space:this.space,programId:this.programId}));let{stakePubkey:n,authorized:o,lockup:s}=e;return t.add(this.initialize({stakePubkey:n,authorized:o,lockup:s}))}static delegate(e){let{stakePubkey:t,authorizedPubkey:n,votePubkey:o}=e,s=wo.Delegate,i=Ve(s);return new tt().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:o,isSigner:!1,isWritable:!1},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:Jh,isSigner:!1,isWritable:!1},{pubkey:uN,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:i})}static authorize(e){let{stakePubkey:t,authorizedPubkey:n,newAuthorizedPubkey:o,stakeAuthorizationType:s,custodianPubkey:i}=e,u=wo.Authorize,f=Ve(u,{newAuthorized:Ze(o.toBuffer()),stakeAuthorizationType:s.index}),g=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Mn,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1}];return i&&g.push({pubkey:i,isSigner:!1,isWritable:!1}),new tt().add({keys:g,programId:this.programId,data:f})}static authorizeWithSeed(e){let{stakePubkey:t,authorityBase:n,authoritySeed:o,authorityOwner:s,newAuthorizedPubkey:i,stakeAuthorizationType:u,custodianPubkey:f}=e,g=wo.AuthorizeWithSeed,b=Ve(g,{newAuthorized:Ze(i.toBuffer()),stakeAuthorizationType:u.index,authoritySeed:o,authorityOwner:Ze(s.toBuffer())}),E=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1},{pubkey:Mn,isSigner:!1,isWritable:!1}];return f&&E.push({pubkey:f,isSigner:!1,isWritable:!1}),new tt().add({keys:E,programId:this.programId,data:b})}static splitInstruction(e){let{stakePubkey:t,authorizedPubkey:n,splitStakePubkey:o,lamports:s}=e,i=wo.Split,u=Ve(i,{lamports:s});return new Qe({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:o,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:u})}static split(e){let t=new tt;return t.add(wr.createAccount({fromPubkey:e.authorizedPubkey,newAccountPubkey:e.splitStakePubkey,lamports:0,space:this.space,programId:this.programId})),t.add(this.splitInstruction(e))}static splitWithSeed(e){let{stakePubkey:t,authorizedPubkey:n,splitStakePubkey:o,basePubkey:s,seed:i,lamports:u}=e,f=new tt;return f.add(wr.allocate({accountPubkey:o,basePubkey:s,seed:i,space:this.space,programId:this.programId})),f.add(this.splitInstruction({stakePubkey:t,authorizedPubkey:n,splitStakePubkey:o,lamports:u}))}static merge(e){let{stakePubkey:t,sourceStakePubKey:n,authorizedPubkey:o}=e,s=wo.Merge,i=Ve(s);return new tt().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!1,isWritable:!0},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:Jh,isSigner:!1,isWritable:!1},{pubkey:o,isSigner:!0,isWritable:!1}],programId:this.programId,data:i})}static withdraw(e){let{stakePubkey:t,authorizedPubkey:n,toPubkey:o,lamports:s,custodianPubkey:i}=e,u=wo.Withdraw,f=Ve(u,{lamports:s}),g=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:o,isSigner:!1,isWritable:!0},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:Jh,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}];return i&&g.push({pubkey:i,isSigner:!1,isWritable:!1}),new tt().add({keys:g,programId:this.programId,data:f})}static deactivate(e){let{stakePubkey:t,authorizedPubkey:n}=e,o=wo.Deactivate,s=Ve(o);return new tt().add({keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:s})}};Nu.programId=new Pe("Stake11111111111111111111111111111111111111");Nu.space=200;Bu=Object.freeze({InitializeAccount:{index:0,layout:T.struct([T.u32("instruction"),R6()])},Authorize:{index:1,layout:T.struct([T.u32("instruction"),De("newAuthorized"),T.u32("voteAuthorizationType")])},Withdraw:{index:3,layout:T.struct([T.u32("instruction"),T.ns64("lamports")])},AuthorizeWithSeed:{index:10,layout:T.struct([T.u32("instruction"),B6()])}}),HY=Object.freeze({Voter:{index:0},Withdrawer:{index:1}}),Cu=class r{constructor(){}static initializeAccount(e){let{votePubkey:t,nodePubkey:n,voteInit:o}=e,s=Bu.InitializeAccount,i=Ve(s,{voteInit:{nodePubkey:Ze(o.nodePubkey.toBuffer()),authorizedVoter:Ze(o.authorizedVoter.toBuffer()),authorizedWithdrawer:Ze(o.authorizedWithdrawer.toBuffer()),commission:o.commission}}),u={keys:[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Ea,isSigner:!1,isWritable:!1},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}],programId:this.programId,data:i};return new Qe(u)}static createAccount(e){let t=new tt;return t.add(wr.createAccount({fromPubkey:e.fromPubkey,newAccountPubkey:e.votePubkey,lamports:e.lamports,space:this.space,programId:this.programId})),t.add(this.initializeAccount({votePubkey:e.votePubkey,nodePubkey:e.voteInit.nodePubkey,voteInit:e.voteInit}))}static authorize(e){let{votePubkey:t,authorizedPubkey:n,newAuthorizedPubkey:o,voteAuthorizationType:s}=e,i=Bu.Authorize,u=Ve(i,{newAuthorized:Ze(o.toBuffer()),voteAuthorizationType:s.index}),f=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!0,isWritable:!1}];return new tt().add({keys:f,programId:this.programId,data:u})}static authorizeWithSeed(e){let{currentAuthorityDerivedKeyBasePubkey:t,currentAuthorityDerivedKeyOwnerPubkey:n,currentAuthorityDerivedKeySeed:o,newAuthorizedPubkey:s,voteAuthorizationType:i,votePubkey:u}=e,f=Bu.AuthorizeWithSeed,g=Ve(f,{voteAuthorizeWithSeedArgs:{currentAuthorityDerivedKeyOwnerPubkey:Ze(n.toBuffer()),currentAuthorityDerivedKeySeed:o,newAuthorized:Ze(s.toBuffer()),voteAuthorizationType:i.index}}),b=[{pubkey:u,isSigner:!1,isWritable:!0},{pubkey:Mn,isSigner:!1,isWritable:!1},{pubkey:t,isSigner:!0,isWritable:!1}];return new tt().add({keys:b,programId:this.programId,data:g})}static withdraw(e){let{votePubkey:t,authorizedWithdrawerPubkey:n,lamports:o,toPubkey:s}=e,i=Bu.Withdraw,u=Ve(i,{lamports:o}),f=[{pubkey:t,isSigner:!1,isWritable:!0},{pubkey:s,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!0,isWritable:!1}];return new tt().add({keys:f,programId:this.programId,data:u})}static safeWithdraw(e,t,n){if(e.lamports>t-n)throw new Error("Withdraw will leave vote account with insuffcient funds.");return r.withdraw(e)}};Cu.programId=new Pe("Vote111111111111111111111111111111111111111");Cu.space=3731;WY=new Pe("Va1idator1nfo111111111111111111111111111111"),KY=re({name:pe(),website:ke(pe()),details:ke(pe()),keybaseUsername:ke(pe())}),VY=new Pe("Vote111111111111111111111111111111111111111"),GY=T.struct([De("nodePubkey"),De("authorizedWithdrawer"),T.u8("commission"),T.nu64(),T.seq(T.struct([T.nu64("slot"),T.u32("confirmationCount")]),T.offset(T.u32(),-8),"votes"),T.u8("rootSlotValid"),T.nu64("rootSlot"),T.nu64(),T.seq(T.struct([T.nu64("epoch"),De("authorizedVoter")]),T.offset(T.u32(),-8),"authorizedVoters"),T.struct([T.seq(T.struct([De("authorizedPubkey"),T.nu64("epochOfLastAuthorizedSwitch"),T.nu64("targetEpoch")]),32,"buf"),T.nu64("idx"),T.u8("isEmpty")],"priorVoters"),T.nu64(),T.seq(T.struct([T.nu64("epoch"),T.nu64("credits"),T.nu64("prevCredits")]),T.offset(T.u32(),-8),"epochCredits"),T.struct([T.nu64("slot"),T.nu64("timestamp")],"lastTimestamp")])});var V_,G_,Fu,pN,ju,fN,JY,QY,XY,Z_=O(()=>{y();V_=Ke(Vi()),G_=Ke(Ks()),Fu=Ke(bh()),pN=r=>{let e=r.decode.bind(r),t=r.encode.bind(r);return{decode:e,encode:t}},ju=r=>e=>{let t=(0,G_.blob)(r,e),{encode:n,decode:o}=pN(t),s=t;return s.decode=(i,u)=>{let f=o(i,u);return(0,Fu.toBigIntLE)(V_.Buffer.from(f))},s.encode=(i,u,f)=>{let g=(0,Fu.toBufferLE)(i,r);return n(g,u,f)},s},fN=ju(8),JY=ju(16),QY=ju(24),XY=ju(32)});var lN,mi,Y_=O(()=>{y();lN=Ke(Vi()),mi=Ke(Ks())});var dN,$_=O(()=>{y();dN=Ke(Vi());Y_()});var J_=O(()=>{"use strict";y();Fl();dc.config({EXPONENTIAL_AT:1e3,DECIMAL_PLACES:78})});var Q_=O(()=>{"use strict";y()});var Hu,bd,m$,Wu,X_,gi,Aa,ek=O(()=>{"use strict";y();Hu=Ke(vs());Du();bd=Ke(Zn());J_();Q_();m$=Hu.Buffer.alloc(64).fill(0),Wu=r=>r&&"version"in r,X_=(r,e)=>Wu(r)?Hu.Buffer.from(r.serialize()):r.serialize(e??{requireAllSignatures:!1,verifySignatures:!1}),gi=(r,e)=>bd.default.encode(X_(r,e)),Aa=(r,e)=>{let t;if(e==="base64")t=Hu.Buffer.from(r,"base64");else if(e==="bs58")t=bd.default.decode(r);else throw new Error("Unsupported encoding");return Kr.deserialize(t)}});var W$,Ku=O(()=>{"use strict";y();W$=Ke(Ks());Du();Du();Z_();$_();ek()});var Xe,Ia=O(()=>{"use strict";y();Xe=class extends Error{constructor({code:e,message:t},n){super(t),this.code=e,this.message=t,typeof n<"u"&&(this.data=n)}toString(){return this.message}}});var tk=O(()=>{"use strict";y()});var yN,mN,gN,xN,wN,Vu,rk=O(()=>{"use strict";y();tk();yN={canSerialize:r=>typeof r=="number"&&Number.isNaN(r),serialize:r=>({["$NAN"]:1}),deserialize:r=>NaN},mN={canSerialize:r=>r instanceof Date,serialize:r=>({["$DATE"]:r.valueOf()}),deserialize:r=>new Date(r["$DATE"])},gN={canSerialize:r=>r instanceof Uint8Array,serialize:r=>({["$UINT8ARRAY"]:Array.from(r)}),deserialize:r=>Uint8Array.from(r["$UINT8ARRAY"])},xN={canSerialize:r=>typeof r=="bigint",serialize:r=>({["$BIGINT"]:r.toString()}),deserialize:r=>BigInt(r["$BIGINT"])},wN={canSerialize:r=>r instanceof URL,serialize:r=>({["$URL"]:r.href}),deserialize:r=>new URL(r["$URL"])},Vu={NAN:yN,DATE:mN,UINT8ARRAY:gN,BIGINT:xN,URL:wN}});var bN,vN,Ra,Gu,vd=O(()=>{"use strict";y();rk();bN=function(r){let e=this[r];for(let t of Object.values(Vu))if(t.canSerialize(e))return t.serialize(e);return e},vN=(r,e)=>{if(e&&typeof e=="object"&&Object.keys(e)[0]){let t=Object.keys(e)[0].slice(1);if(t in Vu)return Vu[t].deserialize(e)}return e},Ra=r=>JSON.stringify(r,bN),Gu=r=>JSON.parse(r,vN)});var nk=O(()=>{"use strict";y();vd()});var ok=O(()=>{"use strict";y();vd();nk()});var sk,ik=O(()=>{"use strict";y();sk=r=>{try{if(r==null)throw"JSON must be set";if(!isNaN(parseInt(r)))throw"Numbers are not valid JSON";return JSON.parse(r),!0}catch{return!1}}});var Sd,vo,Zu=O(()=>{"use strict";y();ok();Fs();ik();Sd=class{constructor({port:e}){this.#e=e,this.#t=!1}#e;#t=!1;get status(){return this.#t?"started":"stopped"}async start(){if(this.#t)return;let e=this.#e.onClose(()=>{this.#t=!1,e()});await this.#e.start(),this.#t=!0}async close(){this.#t&&(await this.#e.close(),this.#t=!1)}async send(e){if(!this.#t)throw new Error("Cannot read stream: RpcTransport has not been started");if(Array.isArray(e)){let t=tf.parse(e),n=new Set(t.map(s=>s.id)),o=Ra(t);this.#e.postMessage(o);for await(let s of this.recv())if(Array.isArray(s)&&s.every(i=>n.has(i.id)))return s;throw new Error("Failed to get response: request ids="+n)}else{let t=Qa.parse(e),{id:n}=t,o=Ra(t);this.#e.postMessage(o);for await(let s of this.recv())if("id"in s&&s.id===n&&!("method"in s))return s;throw new Error("Failed to get response: request id="+n)}}addListener(e){let t=async o=>{let s=this.recv()[Symbol.asyncIterator](),i=()=>{},u=()=>new Promise(g=>i=()=>g(null));function f(){s.return?.(),i(),o.signal.removeEventListener("abort",f)}try{for(o.signal.addEventListener("abort",f);;){let g=await Promise.race([s.next(),u()]);if(!g)break;try{e(g.value)}catch(b){console.error(b)}}f()}catch(g){throw f(),g}},n=new AbortController;return t(n),()=>n.abort()}recv(){let e=[],t=()=>{},n=()=>new Promise(g=>t=g),o=!1,s=this.#e.onClose(()=>{o=!0,t(),s()}),i=this.#e.onMessage(g=>{if(sk(g))try{let b=Gu(g);if("method"in b&&"id"in b){t();return}if(Array.isArray(b)){let E=rf.parse(b);e.push(E),t()}else if("id"in Ja.parse(b)){let q=Xa.parse(b);e.push(q),t()}else{let q=vn.parse(b);e.push(q),t()}}catch(b){console.error(b)}}),u=()=>{s(),i()},f={async next(){for(;!o;){let g=e.shift();if(g)return{done:!1,value:g};await n()}return u(),{done:!0,value:void 0}},async throw(){return u(),{done:!0,value:void 0}},async return(){return u(),{done:!0,value:void 0}}};return{[Symbol.asyncIterator](){return f}}}},vo=class r extends Sd{static createPort(){let e=[],t=[];function n(s){for(let i of e)try{i(s.detail)}catch(u){console.error(u)}}function o(){for(let s of t)try{s()}catch(i){console.error(i)}}return{async start(){window.addEventListener("phantomRpcMessage",n)},async close(){window.removeEventListener("phantomRpcMessage",n),o()},postMessage(s){setTimeout(()=>{window.dispatchEvent(new CustomEvent("dappRpcMessage",{detail:s}))},0)},onMessage(s){return e.push(s),()=>{e.splice(e.indexOf(s),1)}},onClose(s){return t.push(s),()=>{t.splice(t.indexOf(s),1)}}}}constructor(){super({port:r.createPort()})}}});var ak=O(()=>{"use strict";y()});function _d(r){return Wu(r)?"versioned":"legacy"}function EN(r){let e=Ed.default.decode(r),t=Kr.deserialize(e);return{transaction:t,metadata:{numInstructions:t.message.compiledInstructions.length,type:0}}}function kd(r){let{transaction:e,type:t}=r,n=Ed.default.decode(e);if(t==="legacy"){let o=tt.from(n);return{transaction:o,metadata:{numInstructions:o.instructions.length,type:t}}}else{if(t===0)return EN(e);throw new Error(`Unknown transaction type => ${t}`)}}var Ed,ck,Ad=O(()=>{"use strict";y();Fs();Ku();Ed=Ke(Zn());ak();ck=()=>B.env.ENVIRONMENT==="e2e"});var Ba,fk,kN,AN,uk,IN,Id,Yu,lk=O(()=>{"use strict";y();Fs();Ku();Ba=Ke(Zn()),fk=Ke(si());V();Ia();Zu();Ad();kN=new Error("Unsupported path."),AN=(r,e=kN)=>{throw e},uk=r=>zr.common.SolanaSendOptions.safeParse(r).success,IN=1,Id=class extends fk.EventEmitter{constructor(t){super();this._injectionEndMs=null;this._injectionStartMs=null;this.isPhantom=!0;this._publicKey=null;this.#t=()=>{this.addListener(zr.common.SolanaProviderEvent.AccountChanged,t=>{t?this._publicKey=t:(this._publicKey=null,this.emit(zr.common.SolanaProviderEvent.Disconnect))})};this.#o=async t=>{let n;try{let{method:o}=t,s="params"in t?t.params??[]:[],i=zr[o];if(!i)throw new Error("MethodNotFound");let u=i.request.safeParse({jsonrpc:"2.0",id:(IN++).toString(),method:o,params:s});if(!u.success)throw u.error;let f=u.data;if(await this.#e.start(),n=i.response.parse(await this.#e.send(f)),"error"in n)throw new Xe(n.error);try{o==="sol_connect"?(this._publicKey=new Pe(n.result.publicKey),this.emit(zr.common.SolanaProviderEvent.Connect,this._publicKey)):o==="sol_signIn"&&!this.isConnected&&(this._publicKey=new Pe(n.result.address),this.emit(zr.common.SolanaProviderEvent.Connect,this._publicKey)),o==="sol_disconnect"&&(this._publicKey=null,this.emit(zr.common.SolanaProviderEvent.Disconnect))}catch(g){console.error("event emitter error",g)}return n.result}catch(o){throw console.error("GOT ERROR",o),o instanceof Xe?o:o instanceof Ht?new Xe({code:-32e3,message:"Missing or invalid parameters."},{method:t.method}):o instanceof Error&&o.message==="MethodNotFound"?new Xe({code:-32601,message:`The method ${t.method} does not exist / is not available.`},{method:t.method}):new Xe({code:-32603,message:"Internal JSON-RPC error."},{method:t.method})}};this.connect=async t=>{let n=await this.#o({method:"sol_connect",params:t?.onlyIfTrusted===void 0?{}:{onlyIfTrusted:t?.onlyIfTrusted}});return{publicKey:new Pe(n.publicKey)}};this.disconnect=async()=>{this.isConnected&&await this.#o({method:"sol_disconnect",params:void 0})};this.signTransaction=async t=>{if(!t)throw new Xe({code:-32e3,message:"Missing or invalid parameters."});let n=_d(t),o=await this.#o({method:"sol_signTransaction",params:{transaction:gi(t)}});return kd({transaction:o.transaction,type:n==="versioned"?0:"legacy"}).transaction};this.signAllTransactions=async(t=[])=>{if(!t||t.length===0)throw new Xe({code:-32e3,message:"Missing or invalid parameters."});let n=t.map(s=>_d(s)),o=await this.#o({method:"sol_signAllTransactions",params:{transactions:t.map(s=>gi(s))}});if(o.length!==t.length)throw new Error("Invalid number of transactions returned");return o.map((s,i)=>{let u=n[i];return kd({transaction:s.transaction,type:u==="versioned"?0:"legacy"}).transaction})};this.signAndSendTransaction=async(t,n={})=>{if(!t)throw new Xe({code:-32e3,message:"Missing or invalid parameters."});let o={};if(n)if(uk(n))o.skipPreflight=n.skipPreflight,o.preflightCommitment=n.preflightCommitment;else throw new Xe({code:-32e3,message:"Missing or invalid parameters."});return await this.#o({method:"sol_signAndSendTransaction",params:{transaction:gi(t),options:o}})};this.signAndSendAllTransactions=async(t,n={})=>{if(!t)throw new Xe({code:-32e3,message:"Missing or invalid parameters."});let o={};if(n)if(uk(n))o.skipPreflight=n.skipPreflight,o.preflightCommitment=n.preflightCommitment;else throw new Xe({code:-32e3,message:"Missing or invalid parameters."});return await this.#o({method:"sol_signAndSendAllTransactions",params:{transactions:t.map(i=>gi(i)),options:o}})};this.signMessage=async(t,n="utf8")=>{let o=await this.#o({method:"sol_signMessage",params:{message:Ba.default.encode(t),display:n}}),s=Ba.default.decode(o.signature),i=new Pe(o.publicKey);return{signature:s,publicKey:i}};this.signIn=async t=>{let n=await this.#o({method:"sol_signIn",params:{signInData:t}}),o=new Pe(n.address),s=Ba.default.decode(n.signedMessage),i=Ba.default.decode(n.signature);return{address:o,signedMessage:s,signature:i}};this.handleNotification=async t=>{switch(t.method){case"phantom_accountChanged":{let n=Sr.phantom_accountChanged.notification.safeParse(t);if(!n.success)return;let o=n.data.params?.sol;o!==this.publicKey?.toBase58()&&this.emit(zr.common.SolanaProviderEvent.AccountChanged,o?new Pe(o):null);break}case"phantom_trustRevoked":{let n=Sr.phantom_trustRevoked.notification.safeParse(t);if(!n.success)return;n.data.params?.sol===this.publicKey?.toBase58()&&this.emit(zr.common.SolanaProviderEvent.AccountChanged,null);break}}};this.request=async({method:t,params:n})=>{switch(t){case"connect":return await this.connect(n);case"disconnect":return await this.disconnect();case"signMessage":return await this.signMessage(n?.message,n?.display);case"signTransaction":{let o=Aa(n?.message,"bs58");return await this.signTransaction(o)}case"signAllTransactions":{let o=[];n?.message&&typeof n?.message=="string"?o=[n?.message]:n?.message&&Array.isArray(n?.message)?o=n?.message:n?.messages&&(o=n?.messages);let s=o.map(i=>Aa(i,"bs58"));return await this.signAllTransactions(s)}case"signAndSendTransaction":{let o=Aa(n?.message,"bs58");return await this.signAndSendTransaction(o)}default:throw AN(t),new Error("unsupported method: "+t)}};this.removeAllListeners=t=>{try{return super.removeAllListeners(t)}finally{this.#t()}};this.#e=t,this.#e.start(),t.addListener(n=>{let o=vn.safeParse(n);o.success&&this.handleNotification(o.data)}),this.#t()}#e;#t;#o;get publicKey(){return this._publicKey}get isConnected(){return this._publicKey!==null}},Yu=class r extends Id{static inject(e){let t=window;t.isPhantomInstalled=!0;let n=new r;Object.defineProperty(window,"solana",{value:n,writable:!1}),"phantom"in window||Object.defineProperty(window,"phantom",{value:{},writable:!1}),Object.defineProperty(window.phantom,"solana",{value:n,writable:!1}),window.dispatchEvent(new Event("phantom#initialized")),n._injectionStartMs=e,n._injectionEndMs=window.performance.now()}constructor(){super(new vo)}}});var hk,RN,$u,dk=O(()=>{"use strict";y();Ku();Ei();hk=Ke(Zn()),RN=["solana:signAndSendTransaction","solana:signMessage","solana:signTransaction","solana:signIn"],$u=class r{#e={};#t="1.0.0";#o="Phantom";#s=wi;#r=null;#n;get version(){return this.#t}get name(){return this.#o}get icon(){return this.#s}get chains(){return vi.slice()}get features(){return{"standard:connect":{version:"1.0.0",connect:this.#l},"standard:disconnect":{version:"1.0.0",disconnect:this.#h},"standard:events":{version:"1.0.0",on:this.#u},"solana:signAndSendTransaction":{version:"1.0.0",supportedTransactionVersions:["legacy",0],signAndSendTransaction:this.#d},"solana:signTransaction":{version:"1.0.0",supportedTransactionVersions:["legacy",0],signTransaction:this.#y},"solana:signMessage":{version:"1.0.0",signMessage:this.#m},"solana:signIn":{version:"1.0.0",signIn:this.#g},"phantom:":{phantom:this.#n}}}get accounts(){return this.#r?[this.#r]:[]}constructor(e){new.target===r&&Object.freeze(this),this.#n=e,e.on("connect",this.#a,this),e.on("disconnect",this.#c,this),e.on("accountChanged",this.#f,this),this.#a()}#u=(e,t)=>(this.#e[e]?.push(t)||(this.#e[e]=[t]),()=>this.#p(e,t));#i(e,...t){this.#e[e]?.forEach(n=>n.apply(null,t))}#p(e,t){this.#e[e]=this.#e[e]?.filter(n=>t!==n)}#a=()=>{let e=this.#n.publicKey?.toBase58();if(e){let t=this.#n.publicKey.toBytes(),n=this.#r;(!n||n.address!==e||!zp(n.publicKey,t))&&(this.#r=new Hn({address:e,publicKey:t,chains:vi,features:RN}),this.#i("change",{accounts:this.accounts}))}};#c=()=>{this.#r&&(this.#r=null,this.#i("change",{accounts:this.accounts}))};#f=()=>{this.#n.publicKey?this.#a():this.#c()};#l=async({silent:e}={})=>(this.#r||await this.#n.connect(e?{onlyIfTrusted:!0}:void 0),this.#a(),{accounts:this.accounts});#h=async()=>{await this.#n.disconnect()};#d=async(...e)=>{if(!this.#r)throw new Error("not connected");let t=[];for(let n of e){let{transaction:o,account:s,chain:i,options:u}=n,{preflightCommitment:f,skipPreflight:g,maxRetries:b}=u||{};if(!this.#r.equals(s))throw new Error("invalid account");if(!Tp(i))throw new Error("invalid chain");let{signature:E}=await this.#n.signAndSendTransaction(Kr.deserialize(o),{preflightCommitment:f,maxRetries:b,skipPreflight:g});t.push({signature:hk.default.decode(E)})}return t};#y=async(...e)=>{if(!this.#r)throw new Error("not connected");let t=[];if(e.length===1){let n=e[0],o=Kr.deserialize(n.transaction),s=await this.#n.signTransaction(o);if(!s)return[];t.push({signedTransaction:s.serialize()})}else if(e.length>1){let n=new Map;for(let[o,s]of e.entries()){let i=n.get(s.account);i||(i=[],n.set(s.account,i)),i.push([o,Kr.deserialize(s.transaction)])}for(let[o,s]of n.entries()){let[i,u]=s.reduce(([g,b],[E,q])=>(g.push(E),b.push(q),[g,b]),[[],[]]),f=await this.#n.signAllTransactions(u);for(let[g,b]of i.entries())t[b]={signedTransaction:f[g].serialize()}}}return t};#m=async(...e)=>{if(!this.#r)throw new Error("not connected");let t=[];for(let n of e){let{message:o,account:s}=n;if(!this.#r.equals(s))throw new Error("invalid account");let{signature:i}=await this.#n.signMessage(o);t.push({signedMessage:o,signature:i})}return t};#g=async(...e)=>{let t=[];for(let n of e){let o={...n,resources:n.resources?Array.from(n.resources):void 0},{signedMessage:s,signature:i}=await this.#n.signIn(o);t.push({account:this.#r,signedMessage:s,signature:i})}return t}}});var yk,mk=O(()=>{"use strict";y();Ei();lk();dk();yk=r=>{try{Yu.inject(r),bi(new $u(window.phantom.solana))}catch(e){console.error(e)}}});function xk(r,e,t){for(t of r.keys())if(Nn(t,e))return t}function Nn(r,e){var t,n,o;if(r===e)return!0;if(r&&e&&(t=r.constructor)===e.constructor){if(t===Date)return r.getTime()===e.getTime();if(t===RegExp)return r.toString()===e.toString();if(t===Array){if((n=r.length)===e.length)for(;n--&&Nn(r[n],e[n]););return n===-1}if(t===Set){if(r.size!==e.size)return!1;for(n of r)if(o=n,o&&typeof o=="object"&&(o=xk(e,o),!o)||!e.has(o))return!1;return!0}if(t===Map){if(r.size!==e.size)return!1;for(n of r)if(o=n[0],o&&typeof o=="object"&&(o=xk(e,o),!o)||!Nn(n[1],e.get(o)))return!1;return!0}if(t===ArrayBuffer)r=new Uint8Array(r),e=new Uint8Array(e);else if(t===DataView){if((n=r.byteLength)===e.byteLength)for(;n--&&r.getInt8(n)===e.getInt8(n););return n===-1}if(ArrayBuffer.isView(r)){if((n=r.byteLength)===e.byteLength)for(;n--&&r[n]===e[n];);return n===-1}if(!t||typeof r=="object"){n=0;for(t in r)if(gk.call(r,t)&&++n&&!gk.call(e,t)||!(t in e)||!Nn(r[t],e[t]))return!1;return Object.keys(e).length===n}}return r!==r&&e!==e}var gk,Rd=O(()=>{y();gk=Object.prototype.hasOwnProperty});var wk,BN,Bd,Ju,bk=O(()=>{"use strict";y();Fs();Rd();wk=Ke(si());V();Ia();Zu();BN=1,Bd=class extends wk.default{constructor(t){super();this._injectionEndMs=null;this._injectionStartMs=null;this.isPhantom=!0;this.#s=async t=>{let n;try{let{method:o}=t,s="params"in t?t.params??[]:[],i=nc[o];if(!i)throw new Error("MethodNotFound");let u=i.request.safeParse({jsonrpc:"2.0",id:(BN++).toString(),method:o,params:s});if(!u.success)throw u.error;let f=u.data,g=await this.#e.send(f);if(n=i.response.parse(g),"error"in n)throw new Xe(n.error);return n.result}catch(o){throw o instanceof Xe?o:o instanceof Ht?new Xe({code:-32e3,message:"Missing or invalid parameters."},{method:t.method}):o instanceof Error&&o.message==="MethodNotFound"?new Xe({code:-32601,message:`The method ${t.method} does not exist / is not available.`},{method:t.method}):new Xe({code:-32603,message:"Internal JSON-RPC error."},{method:t.method})}};this.requestAccounts=async()=>{let n=(await this.#s({method:"btc_requestAccounts",params:[]})).map(o=>({address:o.address,publicKey:o.publicKey,addressType:o.addressType.replace("bip122_",""),purpose:o.purpose}));return this.emit("accountsChanged",n),n};this.signPSBT=async(t,n)=>(n.finalize=n.finalize??!1,await this.#s({method:"btc_signPSBT",params:[t,n]}));this.signMessage=async(t,n)=>await this.#s({method:"btc_signMessage",params:{address:t,message:n}});this.handleNotification=async t=>{switch(t.method){case"phantom_accountChanged":{let n=Sr.phantom_accountChanged.notification.safeParse(t);if(!n.success)return;let o=n.data.params?.btc?.map(s=>({address:s.address,publicKey:s.publicKey,addressType:s.addressType.replace("bip122_",""),purpose:s.purpose}))??[];if(o.length===0&&this.#t.length>0){this.emit("accountsChanged",[]);return}Nn(o,this.#t)||this.emit("accountsChanged",o);break}case"phantom_trustRevoked":{let n=Sr.phantom_trustRevoked.notification.safeParse(t);if(!n.success)return;let o=n.data.params?.btc?.map(s=>({address:s.address,publicKey:s.publicKey,addressType:s.addressType.replace("bip122_",""),purpose:s.purpose}))??[];Nn(o,this.#t)&&this.emit("accountsChanged",[]);break}}};this.removeAllListeners=t=>{try{return super.removeAllListeners(t)}finally{this.#o()}};this.#e=t,this.#e.start(),this.#t=[],t.addListener(n=>{let o=vn.safeParse(n);o.success&&this.handleNotification(o.data)}),this.#o()}#e;#t;#o(){this.on("accountsChanged",t=>{t.length===0?this.#t=[]:this.#t=JSON.parse(JSON.stringify(t))})}#s},Ju=class r extends Bd{static inject(e){let t=window,n=new r;t.phantom||Object.defineProperty(window,"phantom",{value:{},writable:!1}),Object.defineProperty(window.phantom,"bitcoin",{value:n,writable:!1}),n._injectionEndMs=window.performance.now(),n._injectionStartMs=e}constructor(){super(new vo)}}});function Sk(r){return Uint8Array.from(I.from(r,"hex"))}var vk,PN,Qu,Ek=O(()=>{"use strict";y();Ei();Rd();vk=["bitcoin:connect","bitcoin:signTransaction","bitcoin:signMessage"],PN={ALL:1,NONE:2,SINGLE:3,"ALL|ANYONECANPAY":129,"NONE|ANYONECANPAY":130,"SINGLE|ANYONECANPAY":131},Qu=class r{#e={};#t="1.0.0";#o="Phantom";#s=wi;#r=[];#n;get version(){return this.#t}get name(){return this.#o}get icon(){return this.#s}get chains(){return Si.slice()}get features(){return{"standard:events":{version:"1.0.0",on:this.#u},"bitcoin:connect":{version:"1.0.0",connect:this.#c},"bitcoin:signTransaction":{version:"1.0.0",signTransaction:this.#f},"bitcoin:signMessage":{version:"1.0.0",signMessage:this.#l}}}get accounts(){return this.#r}constructor(e){new.target===r&&Object.freeze(this),this.#n=e,e.on("accountsChanged",this.#a,this)}#u=(e,t)=>(this.#e[e]?.push(t)||(this.#e[e]=[t]),()=>this.#p(e,t));#i(e,...t){this.#e[e]?.forEach(n=>n.apply(null,t))}#p(e,t){this.#e[e]=this.#e[e]?.filter(n=>t!==n)}#a=e=>{if(e.length===0&&this.#r.length>0){this.#r=[],this.#i("change",{accounts:this.accounts});return}let t=e.map(n=>new Hn({address:n.address,publicKey:Sk(n.publicKey),chains:Si,features:vk}));e.length>0&&!Nn(this.#r,t)&&(this.#r=t,this.#i("change",{accounts:this.accounts}))};#c=async(...e)=>{let t=[];e.length>0&&(t=[...e[0].purposes]),t.length==0&&(t=["payment","ordinals"]),t.length===1&&t[0]==="payment"&&(t=["payment","ordinals"]),t.length===1&&t[0]==="ordinals"&&(t=["ordinals","payment"]);let n=await this.#n.requestAccounts();return n&&(n.sort((o,s)=>t.indexOf(o.purpose)-t.indexOf(s.purpose)),this.#r=n.map(o=>new Hn({address:o.address,publicKey:Sk(o.publicKey),chains:Si,features:vk})),this.#i("change",{accounts:this.accounts})),{accounts:this.accounts}};#f=async(...e)=>{let[{psbt:t,inputsToSign:n}]=e;return[{signedPsbt:await this.#n.signPSBT(t,{inputsToSign:n.map(s=>({address:s.account.address,signingIndexes:s.signingIndexes,sigHash:s.sigHash?PN[s.sigHash]:void 0})),finalize:!1})}]};#l=async(...e)=>{let[{message:t,account:{address:n}}]=e;return[await this.#n.signMessage(n,t)]}}});var _k,kk=O(()=>{"use strict";y();Ei();bk();Ek();_k=r=>{try{Ju.inject(r),bi(new Qu(window.phantom.bitcoin))}catch(e){console.error(e)}}});var Ak,Ik,Pd=O(()=>{"use strict";y();Ak=`
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPoAAAB4CAYAAADblO/uAAAACXBIWXMAACE4AAAh OAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAB+OSURBVHgB7Z1r cBvXdccP+AYfEmlTkvU0pDhSYlsSJUfUw54xaCdO0jQWFduZ2E1HZOM2/ZCJyNqd Sd2ZiEymcTNtRlI/Na5bUk0bTxJ7RLmTSZzaITRjPUjFFmVJHku1Rej9DsGXBD4A 9PyXu9ICxGMfdwEQuL8ZaMHdBQQs9n/Pueeeey6RRCKRSCQSiUQikUgkkmygkCSG 8Xq91fcw58+fD5BEMoMoIIlhgsFgXSQS6d6wYYMn1bloFEgiyRJMW/T169e3fPrT n/7I7/cHKc9gS+5ftGhRIz/dyVvP4sWLj+qtO8Q9b968v+ZjL09OTrovXLhwiCR5 QV1dneLtMXT58uWs04bLzMmbNm2qC4VCR9iqbent7e2iPAQNHW926Hb5+OHnh4ev S53L5YIl7+vp6VlDkpwHAmdN4H6AAVC8OL4H+vhe6CssLNzd19fnoyygyMzJbKU8 /CXwRbz8Z14KfWxsrLO0tFQvdK/2BNdG3W4hSV4Aw8cbj34fGnzewCg2rVy50s/P 21n0Pha9nzKEqT4638Cb1aerKU/hHwuuuj/Rcb5GnYcOHfKTJOdhETdRjMjjgOMd LPp+Pr+DPYBU5zuC2WCcV9vKYFNCdpMkX9hK5mhSBb8j3YI3LHSONHtJ13rdunWr ifIQNeLuSXKKnyT5goes0cKC71Y9grRguI/O/Y7Y1gtu/E7KP5K24gUFBVnr6TS2 DVSXl094aIK4DxnxcEfj3ohLuVnxIFfEVR2hSNTn54hMIOKKqN2VSODc7/7yaGjs Wh93UfwHDhzoozyGr8E+1oWHrOHhRweL/VHuv7c73X83FHWHFeMv1B+7n2/qLQcP HsyboByuQzgcPqJG1hPRyhH3rGgAv/HyJU9BuNDLTx9lQXspYtkC3SZ44xhd3P+S 8pzviQAizPx0L4JN+Sb8Bx980Mvfv5vs4+fr1+Ck2FMKHX1xdtOnRRZV8EOvyZfg Ew+t4UfFj+vjm3wvbwO8reZtnc7j8bHQGygDwGK7S8a9bJnZ23I1xlpnUdw4/m80 ePrNeIf8/Oji67ErX+4JFnsbf9/tZB94Tc3Hjh1zxHAmFbpqyffw07okp/n5izbk +g+rxii2shezK57lUvvubRC8ej18lAYUd7x4opEt9lYWeJ1T4tYTnhilC/u20cTN K8lO0wTvoxwHgTXuc0Pwq9WhNcvwe7R98MEH7SSYuEJXrfhW/tBtKdxUPZ18bnu+ Dy2x4Nuw5evQRg7yzZevecMR2sbuuDcd4o5F78KnwMduaWu+uPWq6L00FcvxkgWc EHuU0GG1WNyb+dFkQuCx+Pi1GGLyyfFksSjWu3RyG4WpxYy4J8eG6cIHP6cb/T56 4Ms/odKqBSSCSwdeolvXjxk9Pe8MgR3R87VqZbELi/UoQue+5x4Wt9eGuBPhd7vd a3w+n5ztZYNvvDzgcUUmtrFr3mTWeo/eOEkf/uYFCg5fVP6+d923acnnvk0iMGHV Nfyq2Dspz9C5948ajdTzuU0sdiF5GYrQ2ZI38WYrR5TrRIgd0Vjk+fJTXz5F5UWj ued8PRvJArDiZ//wU8Wia8xb8SQtf6yNRGHSqmt0sgFozVcDgPFzBPAMCD7Ammw4 ceKE7W7PtD66GnRqijNunhRN3LztyocAjJPAghdEJndYFTis96nft9HgxT9MO1ZR u4LWPvMaiWLw9F6Owr9KFsiLIG4yDAoeQ29r1NRryySMuuujyJTqTXi4iTfNsk9u j6nhscnt3Gq2kEUGL77HIt9+21WPpai0ijb+xT4SBSLwZ9/+lrK1AG7iLfmeeGNg iK6Lh91sTZRKOY6OKDKLPeGH4GPtvb29bSSxjNUgmx6452fYTb/I7noqIHQIXhQW 3XcNeIIN+S529OHZTe9OZN3tBucMZcZxsA7/wbY4h7p6enrklEwboB8eikQ67GSt wXoj4IbAmxHWfP01qrx7BYnChvuukbVih2erJkRVq8lR+oYY3Y+AyHRgtu47+f3i aS2guvB+soAhoWNyfUlJSX9soI7/XirddWsoVrxkssNqP1wjXsAtFSsea6e5K75K opi8eVVx322S8SxLtbCKl6amYWPrIXP4+IGMyS473yOJ2H3swlvKujRcYSbWhVfn XTeTxDTP/vBKo6ugoMNOoguEfaq7jcfGzadaf+rhF2nBqudIJP7ffMNqPz3qbdIZ oENiWDAYbOT7GkNejYKHl23lDXCgrpPiTKDiz9lw/PhxH5nE8Ow1viA7S0tL9X11 Oe/aJHorziInq8BVP7b3rxIG3FIxcv0UiaaofB6ND54mmyDluoO3js0V0Il7661b t7zafq06kEAwctXE3V5LgmfLjeo0eBoldn4vXJ+lZBLD89HV8L5P/dMvh9DMgb64 u2TiiF1XHcBVtypyMDk+RKIpnWX63kuEd926dTtIMBg2ZtF13Lx5s19tTLyUHiD4 bnQLyCQQO93RnIZn1apVpkdlzJaS0sZljpLEMM/+6NqOUDjSLWKaKLAbSBt1xKLP JVEUFBS0qPkctoD1RjFPfqCBRR+nySU++9MIyIo7wt/JbEUawvCjOhX4NuhCI25G JjBbSsoXs5UkARHbRz7/dLedcfF4oH89e8HnyCp2vIF0AatrtVwZXldfX78d1pum KvbamlEmCv5OnWYbMHjSqPuAyL5uNyrPmvIMTQmd+zZKyxLbwkimw+6nsthDJDTm JQe4/0s/oTIbk1PGhi+RSAqKK0gwntHRUVPzvNGwsvXeAYFjBliGrHdSMO3byAIg ejCkxsLGMLY+O87UtTEldLQubre7RvbPkwMXTa084iHBYAbaBxyIA/d/+SdklZEb H5FIBETcp6G68J5U56kC71CrILVko8B1VKsxAlMg3x1BPd0uDyrcGH29qbruwOpE hF91DHv5dqhzkWt1mL+sSy12r8AeQjgS2VdCVb4tza4ZPdGBb7rt6jx+coKi0kol hx1ZcBgmw+OT/f9MZhkbEmvRnSJZFB4uOlvvbeFwONvFHQsCgy1mS44hM44j8ajV 6MXfatqsz8hrnbkbVRRxu2izi4cZInphJ/4wgQi5Ootosn1Lc82MEzzcRt5M648v e/J/SBQYPz/4H48qz+G+3720gT787Qumx9NFTlcFScpL2Sa2Yo8mcMp+650MeMdL zRpOdborSrsp35uDdTVGJrw4ssgiBP6rjiG+8yJKIMqIyMHUeZGWEBUe2dNxy0Mz BDW6C8sTN+gm0q1Fjnpl7VTUHQkzYxxYW97QZqq/jvdAAyGSyVtXySn0iVoIZqGG Ybb2wU2Axsp0kBb99RgX3tA1EGrRp9xz5Ufxkk1cmNlExQ1bmt1+ymLUslswpwkj uwse/hGV3b2SRHGaXfUL6uSVCh5qW/v115Q8d/Td9amwEDQaAExNLSqpVLdVHLF/ SOikFnB+3zYRCTMJ4Zu7WZ1J6aXcwZJVB1r/3GiWnOk+ejz2dESqJ2lkO6wxCYKt u2eSJhzNkrKLEZGDMRaASKFX1N4ZR4fA0UdHXx2ufJCj6bD4pZXzhYs5GU6KHFgJ YM0AkKWHe8dHJjGbBmtb6LDiIRrGj+Ah8Xjx/s80V/koyzAqcjA+2E8igUXWg6mp tUu9yth6BVv0sZFLdMPvo9D4MAWHLiriH71+UqkVt2rzKyQalJSSWEPtlvjIYWwJ /fWOkW0RCu+0nrVthPRcCDOYETkYvXyI5pA44I7DWuvddLjtsftiX4MAnBOMOWzN c5w63E9WR7M4OIcZd9AIprG2JgrMWRY6W9odLHKhGV8J8O7pGKhOFYXHWCqWQ+Kh Fi044XdqFhSL3FS2FYJxsHoi3fdqtt7XYyLt8UQO8S9c9RwtWPmcY678zcs9JLGM ZfcdsMix7oIHz/neP8ObtnjnWRL6LzuG2FWPNJEplOV7utD3Ri3yqEMu8mFojcfS Mc7uiX3lOCnLCnVpf+unF5I6b5if40tHvY4j4dggi6+PG4G9paWlPrsFCdVx8iYy Cay6SKHPXvjQNKHHUr3wc0pEXlR553gg2m6jusyMB/fd8PAwcVCNiouLyQrqRCcf WaM6wfMoTAsdImdRNpFJJim85tmmGj+ev94xyO7+1MR6fq+up5tmKVVqXusY8BRS QXes2AtoqryOWnceOcxmqtUqi9Jza9fElpjUaYO7rWT3ackwZIHhs+9QzfLnhKWK Vty9POExWG4IXPQQWjyGz75NuQgEPDo6SuXl5cjQi3vOxMSEInJgVeQqq8kiqBLL nw8eJnLi2xKdZ2p4zarIYbGfaZp1+66DKz5JhQPqwQZ9sE3fCGj0n3uv641ffw/C 9pI4TK0gsnHjxka+qHvIBnPWtFDV4sdJBPrEGT1w05EIk66I+7m3n0+1NNOMBUJn o6KImL1BKioqUuatT05OYt6HInRQUVGhNAg2CPT09NSQgxhOmOE++XZLIicsxxu9 Zniy/nYkOnFf4dKVE3BtvCQWLzKMkOiSKp8ax/lc28M7AydTF240ijJGPuuOSw43 HePpy3iYLV0iHzn3Ts6KHEDAEDcEPTIyQoFAgAYGBhQrrokcoBGwSbXVmXpGMSR0 RNdZgm1kERZvo/7vPR0jukBW2Ks/hoXqKL0ohQHURSymgR8Ax0VkYaG22jCLQxRa 4gvG0Fc++YqSPJNOBk6Kqw+frVRWViY9DgvPniHZZXx83EMOkrKPjlTUSZrYSfao Zref3d6CdhdFqkMU1llH1zY+5i+mYt8kTcZdlWRsfIQcRilhtG7dutWHDx9u1R9Q I+weEgRywivu2SCkrw43fdmmF9OaGKMBkeeyNdeA246H3oLrSdR/NwtWSaKpwLEj JP2UyHgL0YS52RIJYLe/0UXhI8h/j0QLBzPZOrgx6U+UWTc05FwetR5Mi2RXvl9z 5VUr30QCwVDbwCkxlhAWPBMiR6RdZDck20nmmrNASQQoJU0OklToSGuNOJPxZorB kcuURjyqK+9NtnCFHQY/2Tujs8mu9P4D5RPJphwjOp/I2pv8PzIj9F8q/WixJZCs MDR8ha5e/4TSjEetMeYhh7h6ZCdbxpnn+sJlz7dMuFRWG9F5AdxLDlKQ+IC9oSRR nLuYm3UoEZi70vsjRyqzOAXmm+eTy67BgbKkx2HRMQyXzcQVOgfHmrLBZQcf9x+g XAWW8crhmeEGo6uBQGK+AWuuueYlJSVUXV1NtbW1NGfOHKqpqaGqqiolWAehY3zd KkbXTDdCXV2dFyu16vfFjbqz+rc7O1HFGHDb/8+/n3IZpI9C7HPqWpwosCgEiPxy nvXLNeCWI7KOhBikuerBGDseZWVlSmOA/roNPNqTmHkbnjjn+vEP/7998VK6Q6GQ EkDHAhDHjh3rVD5r7EmYFhohca2LHY6ffIvygdFLh9iVf4nm1b9ERe55lE1g3P/a EbujqzMbWPFUY+U2U2CV/wYjPtpijrHzNuKBlO76+nrMIUHhSKyc5FMncu3mvzdz Y+HTzp0WTny9c6iDG6YmyjCw5r948wUaHM79sVoNLIKAajTZInaskIqVUiUzirhL QE3ro2eDyAGseT6JHCirkv7v8xnPOMM4+YV926TIZybI9OzHAhb6nVEWXa35JiRB xg6w5q/89zcpn4F1v6f+76lk1jJKFxgBQGQdAk/naEBBWTFVLJtL5Z65VHxXBZXd w66yu0TZr2diYJQmAqMUvBigscsBGu2/quyTxAczLXt7e5VCkjFCH2zjXZaSRLhf 7483lzweY2OjVFpakfDYf77+7byz5omoWvI4zV72pKOCz4TAIeKah5ZR5WcXUPlS 6+u2QejDH16gPx48JUUfB61UdpTQuX++h133RjKJKnI/GZhhBms9OHyZFi+YPncF Ike//OqNtCfIZD3u2pVUufhxYXnyANH0W9ePp1XgsNyz1yylqvsXTrPYdrnJFv76 70/Q6On0pEzPEHw9PT0NMRYdtdjNTgeNBFBUopAKu10pxt61ANvaVV+jh1Z+bdqx rt9ulyI3QMX8DVTOgi+dvdSUpYeYx4dOK9VugizwdGa4QdT3fGUtzV7rIaeB4C++ 0SstvAqsuu0qsGFyNT/bXOPn/n1AmZCaAH0U/cAffkZlJZWKVQ+OjdDH/gP0/rE3 +Ln8YYyA4Tg8AKx76exlyhb9+oKiaGuPAF94clSpRJup2WZ3Pbyc5jz2oHALngh0 Be578U/Zuh+na++coHyH++p1MUKPnDFTdIZD9s3PNM9Sa7mFeRzPFbdgItJY9761 /baQx1jcv+n+J5LYB1Y6W2u2QdgLn17P/fCFlAlquXFBN+HMv3fntXVHjYfY4TWD 82EjgTDRlqeaZ3Vqe55pnr2T97fjmHYOSkj94s0XCQ9prfOL4poKWvadL2ZM5PrP sZQ/R1WGP0emiTLfqOU2QQVHkkbPWbyTkZDirlMKMJ87R1fYkCQB4rr3Ww3KNpu4 +EYPDb7vp3yDLXpnlEVHLbcQhRvYGu+OPhWlmqlTKeTYNKvBiMiVVzk0n1uSvWSr yMGCp9anJRiYhZxJ2iGfWtE0GLCyhLG05vlHNotcIxScoLOvdlPw0gDlC6zDLY6t j44EfcqSqa6S9LD0O09Q2XxHqxYLAYG5fArQud3uGkfWR0cZJpIizytqH39gRogc wONY8FQ95QnK6kSOCF1dx1qSJ0A4GCefSWCsHeP7uY46fVXM+uh6MGneytpkkpkL +uV2mbw5Rmd/9z4NfHSeqpbMoWWNG6mo3PbCCEnBOHvgvX4KB+0Xd8xifPhHuNDj 1WWX5C7Va5faCr5B2Ke7DvL2nG7fObr2/sf00Pe+TmW1s8gpCsuK6e6HV9C1d45T LoJhNW1eunChM9tIkjfUPvYAmSV4fYguvnuCzrEFn2BLHo9bfM57//hLWvXdzYqF d4qaTcvpxv6TuWrV27Un9teS0aEG4TJeIjrbQN0xVBJFccFcAtlmNevvM/UaWOrD P3xNseThiTvlkorLy2hRwyqa/an5ND54U3Hl8bjQ/YFyvOYzi8kJCopYAqGwMhEm l1Ct+e18GKEWXQbh4oN6Y1iYD8UDUTU0V5jNbrtZIHA9EPDctffR/Efuv90nX/LE Wrbmv2KrPqj8Ddceol/+nJecAFY9l9x3vs9QR65dv0+oRV+0aBGqCDq64sRMBELX ygHjgSV+kq3+kaX4SDdkiv7tgqfXk1n8vz6suO4AAbcHnv+iYsULiu/YHAh+wSMP KBZ/8JNLyj5s8bo5a815EEaAVb/Vf02pXpMj/Linp6dLv0PY8NqmTZswc81DkmlA **************************************/dfg6hJwJihwWHddcY1r1WNJX3 58ykF39vb29b7E5hQg+FQl6SJERfDhilfG/cuHF7nW2tHji2EHYwGFSO4ZyhoSHK NChcwJ8tqiRQ1f2LyCxwvyduBqdev8RYQ6G34E5G4CtslLPKJvBbxdsvso++mSQJ iVcXHILGI9lrZs+eTRmmFUM09fX1Xn13o2y++R6a3iIbFe3kzTvXx8noeyl/H8yf n8nRd26M29ll98c7JkzoqGIxA/udWYsm8lQLBzgJbhx2A3eiW8ZeSJSySy0IXe+2 uw0KHY2DPmCnAe8Agb0gB+xg9UVY+5K7Kil4ccZOdumK57JrCBF6vBtBYo9Zs2Zl VOSku3E4nhBVOciKyIEWRQdGhYk+ur4vD3FjiO4Sj8NrY/DIqBORXIMy03GEjmIs dZTdIIbSmuwEIUKPvREk0zGyxI5GRUWFsqZXBvG73e5m7Q94a/qDqLluhUldcgyS ZeYasMQIyuF1EPbV9z+JyqDTQHLNoe//TGkQ9ME7sxS4o2vaYSyaN2div382gaG0 goKChtiVWWIREozL5guRLYyNjRk6D1YcC/plEFiHhpjF+6ICcYWl1oo86gNrWuZb skg6rPepn/to/4uv0kne6kWOBBsMwblrp2IYaAxwLsbcrVJYFtWA+fnRDiFRFsP3 S3MqkQNRZmM1SRKCgJtRi57h7Dm/WvDfH7M/yle3atHnrP2U4mJ/+OpbihsPsfew JdYPo2mTW66x9R4+Oz1bLV6CDQSO1wAIHY3HCn5PO668tn7Zhg0bAjZXSXWS1oMH D3YZOVGU0GX/PAEYL0eyjFGQTJMhEokcCPPYaj6ziMX+TFTmG4QKYYN4rjmsN4QN jwCvjwUNBUSvWXP04Ue4kVj/gz+3NANOjV534jn/foFsDDJrgVKj54sSunTd4wBL EAgETPXPM3RTJRO5cGBp1//gm4owNUscT+Cw3uh3Y1gtlWCnzptLR/9lamFIeAsQ /PxHTE+6iYpec/+3L9ssuiryNjOvsS30uro6ac3jYEXkgG8sSjNJRa7WF4jaNy4g VVTLfNNbYgDrvfiJNYorr4l78qax+Aa6BmgctEajrNZcDkJBcWGfPggJ1BwCWPWs uM+tiBzYFnpZWVl1FvdhMgZErqWvIisOUXRNxHDn0QAgHTYWZMql0X23ZMlF1lqD JUaf+ypbX4hU75rrh9LWct/ebMKMufNdfXd5P9vwZvsr04JvfI0wxOalDGNV5ECE 6+4hSRRo+CButg5JJ7BA7FoarH5fmjAkcg4kBmIbHggdGWSilliqZEFWxhHlqZ93 347KGxWtlpQDb8B4/5yFXEgNnQ2tiSLsRymDQleH0Fq1uIEV0u4n5gMQdmVlJbyd pH1uDKVVV1cr4+Ya8ay8A5/Px43QGiOWvK+vL+7Nn45yyZrI3YbTZc3n0vPV8KUQ Oa6Xoci2Q/hZ6GiQO8kGGc3KkEyBcXO49fpJLk4F5fi9d7NlaCJz+CnGcwteCtha 1zwV2lRWMBVY+0TpgydDPyZv0Jrv6vx8a8pCKVhfPEP99D7+P7ccPnzYTzYRYdH9 JLENrL9WlMIpq6728ZrIPNPW5Bv+8AI5CYTq1gXTEE1PlQyDvrxGSlc/Emnt/Pzf GK6GxA3xLkovu7hBXiNqJES67lkExI6JLKLTX9WKI81WAznMvtgdKL3k5EwvCB3j 7XqxQ+ixYoe7jn093/8vpQ6dRkLX3eXyU6FrTecXXjA8Bg04VrEzHVlyan98C4tc aEk227Mmzp8/H1i4cGEL30hlJLEN+u2Ch9j8/H4b4X6SRRYvXozftil2f1FVGbkX 301OoVWaQQ05zS3XIvHYd7rrEH20+21l3/jgnZEANA6er6yL577vYpE/y/1xP5nk 8uXLQb4ObnI2KNfF3biGd9991+CqxsYR0hFcv379EcqepBkEL6qzZdwzk6A/zjdO S0zeuiXi/caYxYalkdNBPGseC4bnlvAYPLZRIocVjxC76q22gmrIGeERCFwHD4kF IyDNdhrjVAiZB8ktHeYRZoPQd3E0+VkeokID5qU8RXXV/45d9e/5/f4gCWDRokWw Zl/S7wuNBKli2dy0LKoI8cId/+OxMxSeuFNeCwk2sN6rv7tZqSJbMf+uqPpzpFnx x1ttW0nVqiP1rpEEpH2rbjrqu21hz9hPDiLEomfByqlRLSJa3pKSkv48ter+cDiM SK1Q9y+RNUPk/d7n7a/UYhRE40+8+hYLvJQWP7E2bu77FDxsFqH2zi+0+kgwarZg N1m07KrAd3FMZqcIb8sIQoSeYWHBirfFXrCNGze28A2/g/KLuNdCFKjbr97gUcz7 kzVZtI6ZcwLXg3uehdrC12O7kfNVcaPrsNtJFz0RwgZrWVh7WFiNlD4wxtia7KJx vxI3pZdyH8f7eBos9rbYmxuln+/7268Ky5SzRnoEHgusO2/QAG6GoeOtRz2ExtbP j31IoeVGoS9d1jsewoSeqLUXjVacnvs1KYdH8CNw43Mkx114R614POKJPd0u/B0y I/CZhrCiZAgmcMAGC2RvIAfQAhcItu3fv99n5DUY+luyZMkYv/ZLlGMgjRVWnBu8 fxUVcDMKX1cfB6WiAp5Tix+4lOCc47hcAb4hfkxFBVs4yPbTvp+95SdJUoTmWaoB G1h1YRF4EYELduFh/XNi8UczHo3TqG5rB38mr7ZvwVPrafZaDwlnSty7KeLqktbb PMITqu1GJDVERybr6+v3sEDSGUMQDgSezkitUfjaNvJvtQ2CR3/93ucfs1wpNhp2 yylyVIrbPo6VM7FiRVVxY1ioXXRgyQlvI410qYFHP2UxWmCqqKJs84I/2+QpX1Jr /FpPJbX4+I48SmHqoyLqSzajTGIOR+sW4YfnGxTDXI9SHIGpbihSNPeFQiFfeXm5 z0lrpQ4DdswUy45+ODnQ6KWLpu4d1TTBnp2Lqm8/NMIEYQcoRIHOL5tPSXUCtaHy zNTrnYy0FijTLiSpM94yZaHiRY2zCbWe+O5cvOEygdfrrU5lQNRRow61+msn5Rh5 u4aSms0HsXsoC8hEtlS+gBgCCxjJUz6tjLN2bN26dXWIL9DUpB0/BznNL/o+A8jr xdJUDwPWfStlCHWYbBfHD3xS4M6hjxmpXUbEgjwU3dDvEj09NFuQqyJS+gWv9r33 SeudXljs/ZTEg1Nr6PkoB5FC16ETPIKHHhKEakF8/HSf2+3ulOLODKmyN/k3Wprt IxtWkTXjdKg/chOeq303Lz/VRG9oqEgbSYBryM+P8oiDT/RMMok1gsFgXwZXwsko 0qKbAOIvLCzUhog86tavbdkVD0hrnd2kcN9bsyHj0Amk0CV5BUfgB5JMcsrZqLss DinJGzZt2lSXYiajhxuCNspBpNAleUMoFLqdko2kJI6hbEEADtF23tVKU/P6tyNo RzmGDMZJ8gkvqYsixETX8dzHj52qyD0kkUhmHhCwmjQjkUgkEolEIpFIJBKJRCKR SCTO8f8Sw1p3XxsDBAAAAABJRU5ErkJggg==
`,Ik=`
data:image/png;base64,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
`});var Xu,Rk=O(()=>{"use strict";y();Pd();Ad();Xu=class r{constructor(){this.showPopup=()=>{let e=document.createElement("link");e.href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap",e.rel="stylesheet",document.head.appendChild(e);let t=document.createElement("div");t.style.fontFamily="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif",t.id="metamask-explainer",t.style.transform="scale(0.9125) translateY(15px)",t.style.willChange="transform",t.style.opacity="0",t.style.transition="all 300ms cubic-bezier(0.16, 1, 0.3, 1)",t.style.width="262px",t.style.position="fixed",t.style.top="20px",t.style.right="20px",t.style.zIndex="99999",t.style.background="#222",t.style.backgroundClip="border-box",t.style.border="1px solid rgba(80, 80, 80, 0.2)",t.style.borderRadius="4px",t.style.fontSize="13px",t.style.padding="1.5em",t.style.boxShadow="0px 54px 22px rgba(24, 24, 27, 0.02), 0px 31px 18px rgba(24, 24, 27, 0.07), 0px 14px 14px rgba(24, 24, 27, 0.12), 0px 3px 7px rgba(24, 24, 27, 0.14), rgba(24, 24, 27, 0.3) 0px 0px 2px 0px",t.style.userSelect="none",t.style["-webkit-font-smoothing"]="antialiased",t.style.MozUserSelect="none",t.style.msUserSelect="none";let n=document.createElement("div");n.style.display="flex",n.style.flexDirection="column",n.style.justifyContent="center",n.style.gap="1em",n.style.marginBottom="1em",t.appendChild(n);let o=document.createElementNS("http://www.w3.org/2000/svg","svg");o.setAttribute("width","8"),o.setAttribute("height","8"),o.setAttribute("viewBox","0 0 8 8"),o.setAttribute("fill","none"),o.style.width="0.8em",o.style.height="0.8em",o.style.display="flex",o.innerHTML=`
    <path d="M1 1L7 7M7 1L1 7" stroke="currentColor" stroke-linecap="round"></path>
    `;let s=document.createElement("div");s.id="metamask-explainer__close-button",s.style.position="absolute",s.style.top="0.5em",s.style.right="0.5em",s.style.padding="0.5em",s.style.cursor="pointer",s.style.color="rgb(153, 153, 153)",s.style.borderRadius="6px",s.style.transition="all 300ms cubic-bezier(0.16, 1, 0.3, 1)",s.tabIndex=1,s.addEventListener("mouseover",()=>{s.style.color="#fff",s.style.background="#333"}),s.addEventListener("mouseout",()=>{s.style.color="rgb(153, 153, 153)",s.style.background="none"}),s.addEventListener("click",()=>{t.style.opacity="0",t.style.transform="scale(0.96) translateY(10px)",setTimeout(()=>t.remove(),300)}),s.appendChild(o),t.appendChild(s);let i=document.createElement("div");i.id="logo-container",i.style.display="flex",i.style.flexDirection="row";let u=document.createElement("img");u.src=Ak,u.style.height="2.7em",u.style.position="relative",u.style.outline="2px solid #222",u.style.background="#222",i.appendChild(u),n.appendChild(i);let f=document.createElement("div");f.style.display="flex",f.style.flexDirection="column",f.style.gap="0.25em",f.style.lineHeight="1.2",n.appendChild(f);let g=document.createElement("div");g.id="metamask-explainer__header",g.textContent="Phantom now supports Ethereum & Polygon!",g.style.color="#ffffff",g.style.fontSize="1.125em",g.style.fontWeight="600",g.style.fontFamily="Inter",f.appendChild(g);let b=document.createElement("div");b.style.display="flex",b.style.flexDirection="column",b.style.gap="1em",b.style.alignItems="flex-start",t.appendChild(b);let E=document.createElement("div");E.id="metamask-explainer__body",E.textContent="Connecting with MetaMask will give you the option to use Phantom.",E.style.color="#ffffff",E.style.fontSize="1em",E.style.fontWeight="400",E.style.lineHeight="1.4",E.style.fontFamily="Inter",b.appendChild(E);let q=document.createElement("div");q.id="metamask-explainer__dont-show-again-button",q.textContent="Don't show again",q.style.cursor="pointer",q.style.color="#AB9FF2",q.style.fontSize="1em",q.style.fontWeight="400",q.style.lineHeight="1.2",q.style.transition="color 300ms cubic-bezier(0.16, 1, 0.3, 1)",q.tabIndex=1,q.addEventListener("mouseover",({target:C})=>{C.style.color="#ffffff"}),q.addEventListener("mouseout",({target:C})=>{C.style.color="#AB9FF2"}),q.addEventListener("click",()=>{t.style.opacity="0",t.style.transform="scale(0.96) translateY(10px)",setTimeout(()=>t.remove(),300),this.setHasBeenDismissed()}),b.appendChild(q),document.body.appendChild(t),setTimeout(()=>{t.style.transform="none",t.style.opacity="1"},0),r.hasBeenShown=!0}}static{this.hasBeenShown=!1}shouldShowPopup(){return window._phantomShowMetamaskExplainer&&!r.hasBeenShown&&!this.hasBeenDismissed()&&!ck()}hasBeenDismissed(){return window.localStorage.getItem("phantomwallet-metamask-explainer-dismissed")==="true"}setHasBeenDismissed(){window.localStorage.setItem("phantomwallet-metamask-explainer-dismissed","true")}findFaviconUrl(){let e=document.querySelectorAll('link[rel="icon"], link[rel="shortcut icon"], link[rel="apple-touch-icon"]');return e.length>0?e[0].href:""}}});function Bk(r,e){window.dispatchEvent(new CustomEvent("eip6963:announceProvider",{detail:Object.freeze({info:r,provider:e})}))}var Pk,Tk,Td,Cn,zd=O(()=>{"use strict";y();Fs();Pk=Ke(si());V();Pd();Ia();Zu();Rk();Tk=1,Td=class extends Pk.EventEmitter{constructor(t){super();this._injectionEndMs=null;this._injectionStartMs=null;this.isPhantom=!0;this.isMetaMask=!0;this.#t=()=>{this.addListener("accountsChanged",t=>{let[n]=t;n?(this.selectedAddress=n.toLowerCase(),this.request({method:"eth_chainId",params:[]})):this.selectedAddress=null})};this.request=async t=>{let n;try{let{method:o}=t,s="params"in t?t.params??[]:[],i=qs[o];if(!i)throw new Error("MethodNotFound");let u=i.request.safeParse({jsonrpc:"2.0",id:(Tk++).toString(),method:o,params:s});if(!u.success){if(o==="personal_sign"){let g=qs.eth_sign.params.safeParse(s);if(g.success){let[b,E]=g.data;return this.request({method:"personal_sign",params:[E,b]})}}if(o==="eth_signTypedData"){let g=qs.eth_signTypedData_v4.params.safeParse(s);if(g.success)return this.request({method:"eth_signTypedData_v4",params:g.data})}throw u.error}let f=u.data;if(n=i.response.parse(await this.#e.send(f)),"error"in n)throw new Xe(n.error);try{if(o==="eth_requestAccounts"){let g=n.result?.[0].toLowerCase();g!==this.selectedAddress&&this.emit("accountsChanged",[g])}if(o==="eth_accounts"){let g=n.result?.[0]?.toLowerCase();g&&g!==this.selectedAddress&&(this.selectedAddress=g)}if(o==="eth_chainId"){let g=n.result;g!==this.chainId&&(this.chainId=g,this.networkVersion=parseInt(g.substring(2),16).toString(),this.emit("chainChanged",this.chainId))}if(o==="wallet_addEthereumChain"){let g=f.params[0].chainId;g!==this.chainId&&(this.chainId=g,this.networkVersion=parseInt(g.substring(2),16).toString(),this.emit("chainChanged",this.chainId))}if(o==="wallet_switchEthereumChain"){let g=f.params[0].chainId;g!==this.chainId&&(this.chainId=g,this.networkVersion=parseInt(g.substring(2),16).toString(),this.emit("chainChanged",this.chainId))}}catch(g){console.error("event emitter error",g)}return n.result}catch(o){throw o instanceof Xe?o:o instanceof Ht?new Xe({code:-32e3,message:"Missing or invalid parameters."},{method:t.method}):o instanceof Error&&o.message==="MethodNotFound"?new Xe({code:-32601,message:`The method ${t.method} does not exist / is not available.`},{method:t.method}):new Xe({code:-32603,message:"Internal JSON-RPC error."},{method:t.method})}};this._metamask={isUnlocked:()=>!!this.selectedAddress};this.handleNotification=async t=>{switch(t.method){case"phantom_accountChanged":{let n=Sr.phantom_accountChanged.notification.safeParse(t);if(!n.success)return;let o=n.data.params?.evm?.toLowerCase()??null;o!==this.selectedAddress&&this.emit("accountsChanged",o?[o]:[]);break}case"phantom_trustRevoked":{let n=Sr.phantom_trustRevoked.notification.safeParse(t);if(!n.success)return;n.data.params?.evm?.toLowerCase()===this.selectedAddress&&this.emit("accountsChanged",[]);break}case"phantom_chainChanged":{let n=Sr.phantom_chainChanged.notification.safeParse(t);if(!n.success)return;let o=n.data.params?.evm?.toLowerCase()??null;o&&o!==this.chainId&&(this.chainId=o,this.networkVersion=parseInt(o.substring(2),16).toString(),this.emit("chainChanged",o));break}}};this.removeAllListeners=t=>{try{return super.removeAllListeners(t)}finally{this.#t()}};this.#e=t,this.#e.start(),this.selectedAddress=null,this.chainId="0x1",this.networkVersion="1",this.request=this.request.bind(this),t.addListener(n=>{let o=vn.safeParse(n);o.success&&this.handleNotification(o.data)}),this.#t(),this.emit("connect",{chainId:this.chainId})}#e;#t;isConnected(){return navigator.onLine}enable(){return this.request({method:"eth_requestAccounts",params:[]})}sendAsync(t,n){let o="id"in t&&typeof t.id<"u"?t.id:null;this.request(t).then(s=>n(null,{jsonrpc:"2.0",id:o,result:s})).catch(s=>n(s,null))}send(t,n){return typeof t!="string"?this.sendAsync(t,n):this.request({method:t,params:n})}},Cn=class r extends Td{constructor(){super(new vo);this.isMetamaskExplainerEnabled=!1;this.initializeMetamaskExplainer=async()=>{this.#e||(this.#e=new Xu),this.#e.shouldShowPopup()&&this.#e.showPopup()};document.addEventListener("DOMContentLoaded",()=>{this.initializeMetamaskExplainer()})}#e;static inject(t,n){let o=window;o.isPhantomInstalled=!0;try{delete window.web3}catch{}let s=new r;if(t){try{Object.defineProperty(window,"ethereum",{get(){return s},set(u){},configurable:!1})}catch{console.error("Error redefining provider into window.ethereum")}window.dispatchEvent(new Event("ethereum#initialized"))}window.phantom||Object.defineProperty(window,"phantom",{value:{},writable:!1}),Object.defineProperty(window.phantom,"ethereum",{value:s,writable:!1}),window.dispatchEvent(new Event("phantom#initialized")),s._injectionEndMs=window.performance.now(),s._injectionStartMs=n;let i={uuid:(Tk++).toString(),name:"Phantom",icon:Ik,rdns:"app.phantom"};try{window.addEventListener("eip6963:requestProvider",()=>{Bk(i,s)}),Bk(i,s)}catch{console.error("PHANTOM error announcing multi-injection provider")}}}});var ep,zk=O(()=>{"use strict";y();Ia();zd();ep=class r{constructor(){this.w=window;this._injectionEndMs=null;this._injectionStartMs=null;this.initCallbacks=[];this.detected=[];this.#e=e=>{this.initCallbacks.push(e)};this.setProvider=e=>{if(!this.targetProvider){this.isSelectingExtension=!1,this.targetProvider=e;for(let t of this.initCallbacks)t(this.targetProvider);this.initCallbacks=[]}};this.addProvider=e=>{this.detected.push(e)};this.isMetaMask=!0;this.isSelectingExtension=!1;this.selectExtension=async()=>{if(!this.targetProvider&&!this.isSelectingExtension){this.isSelectingExtension=!0;try{let e=this.w.phantom?.ethereum;if(e){if(!this.detected.length){this.setProvider(e),window.dispatchEvent(new Event("ethereum#initialized"));return}switch(await e.request({method:"wallet_selectEthereumProvider",params:[]})){case"ALWAYS_USE_PHANTOM":case"CONTINUE_WITH_PHANTOM":{this.setProvider(e);break}case"CONTINUE_WITH_METAMASK":case"ALWAYS_USE_METAMASK":{let[n]=this.detected;for(let o of this.detected)o.isMetaMask&&this.setProvider(o);!this.targetProvider&&n&&this.setProvider(n);break}}}}catch(e){console.error(e)}finally{this.isSelectingExtension=!1}}};this.request=async(...e)=>{if(this.targetProvider)return this.targetProvider.request(...e);let t=!0,n=new Promise((o,s)=>{try{this.#e(i=>{if(!i){let u=new Xe({code:-326034,message:"Internal JSON-RPC error in the EthProviderPrxy."});return s(u)}!this.selectedAddress&&e[0].method==="eth_accounts"?i.request({method:"eth_requestAccounts",params:[]}).then(o).catch(s):i.request(...e).then(o).catch(s)})}catch(i){throw console.error(i),i}finally{t=!1}});for(;t;)await new Promise(o=>setTimeout(o,0));return this.selectExtension(),n};this.sendAsync=(...e)=>{if(this.targetProvider){this.targetProvider.sendAsync(...e);return}this.#e(t=>{t&&t.sendAsync(...e)})};this.send=(e,t)=>typeof e=="string"?this.request({method:e,params:t}):this.sendAsync(e,t);this._metamask={isUnlocked:()=>!!this.selectedAddress};this.eventNames=()=>this.targetProvider?.eventNames()??[];this.listenerCount=e=>this.targetProvider?.listenerCount(e)??0;this.listeners=e=>this.targetProvider?.listeners(e)??[];this.emit=(e,...t)=>this.targetProvider?.emit(e,...t)??!1;this.once=(...e)=>(this.targetProvider?this.targetProvider.once(...e):this.#e(t=>{t&&t.once(...e)}),this);this.on=(...e)=>{if(this.targetProvider){if(!this.targetProvider.on)return this.targetProvider.addListener(...e);this.targetProvider.on(...e)}else this.#e(t=>{if(t){if(!t.on)return t.addListener(...e);t.on(...e)}});return this};this.off=(...e)=>{if(this.targetProvider){if(!this.targetProvider.off)return this.targetProvider.removeListener(...e);this.targetProvider.off(...e)}else this.#e(t=>{if(t){if(!t.off)return t.removeListener(...e);t.off(...e)}});return this};this.addListener=(...e)=>(this.targetProvider?this.targetProvider.addListener(...e):this.#e(t=>{t&&t.addListener(...e)}),this);this.removeListener=(...e)=>(this.targetProvider?this.targetProvider.removeListener(...e):this.#e(t=>{t&&t.removeListener(...e)}),this);this.removeAllListeners=(...e)=>(this.targetProvider?this.targetProvider.removeAllListeners(...e):this.#e(t=>{t&&t.removeAllListeners(...e)}),this)}static inject(e){let t=window;t.isPhantomInstalled=!0,Cn.inject(!1,e);let n=new r;for(let s of t.providers??[])n.addProvider(s);t.ethereum&&n.addProvider(t.ethereum);let o=new Proxy(n,{get(s,i,u){return Reflect.get(s,i,u)}});Object.defineProperty(window,"ethereum",{get(){return o},set(s){s&&n.addProvider(s)},configurable:!0})}#e;get providers(){if(!this.w._phantomHideProvidersArray)return this.detected}get isPhantom(){return this.targetProvider?.isPhantom}get selectedAddress(){return this.targetProvider?.selectedAddress??null}get chainId(){return this.targetProvider?.chainId??"0x1"}get networkVersion(){return this.targetProvider?.networkVersion??"0x1"}isConnected(){return this.targetProvider?.isConnected?.()??!0}enable(){return this.request({method:"eth_requestAccounts",params:[]})}get host(){return this.targetProvider?this.targetProvider.host:void 0}get path(){return this.targetProvider?this.targetProvider.path:void 0}}});var Mk,Lk,Nk,Ck=O(()=>{"use strict";y();zk();zd();Mk=r=>{try{window.addEventListener("phantom#provider_injection_options",e=>{let{hideProvidersArray:t,showMetamaskExplainer:n,dontOverrideWindowEthereum:o}=JSON.parse(e.detail);window._phantomHideProvidersArray=t,window._phantomShowMetamaskExplainer=n,o?Cn.inject(!1,r):ep.inject(r)},{once:!0}),window.dispatchEvent(new CustomEvent("phantom#get_provider_injection_options"))}catch(e){console.error(e)}},Lk=r=>{try{window.addEventListener("phantom#provider_injection_options",e=>{let{hideProvidersArray:t,showMetamaskExplainer:n,dontOverrideWindowEthereum:o}=JSON.parse(e.detail);window._phantomHideProvidersArray=t,window._phantomShowMetamaskExplainer=n,o?Cn.inject(!1,r):Cn.inject(!0,r)},{once:!0}),window.dispatchEvent(new CustomEvent("phantom#get_provider_injection_options"))}catch(e){console.error(e)}},Nk=r=>{try{try{window.addEventListener("phantom#provider_injection_options",e=>{let{hideProvidersArray:t,showMetamaskExplainer:n}=JSON.parse(e.detail);window._phantomHideProvidersArray=t,window._phantomShowMetamaskExplainer=n,Cn.inject(!1,r)},{once:!0}),window.dispatchEvent(new CustomEvent("phantom#get_provider_injection_options"))}catch(e){console.error(e)}}catch(e){console.error(e)}}});var Ok={};ue(Ok,{injectInPageBtc:()=>_k,injectInPageEvmAsk:()=>Mk,injectInPageEvmMetamask:()=>Nk,injectInPageEvmPhantom:()=>Lk,injectInPageSol:()=>yk});var qk=O(()=>{"use strict";y();mk();kk();Ck()});y();(async()=>{let r=window.performance.now(),{injectInPageBtc:e}=await Promise.resolve().then(()=>(qk(),Ok));e(r)})();})();
/*! Bundled license information:

ieee754/index.js:
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)

buffer/index.js:
  (*!
   * The buffer module from node.js, for the browser.
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)

js-sha3/src/sha3.js:
  (**
   * [js-sha3]{@link https://github.com/emn178/js-sha3}
   *
   * @version 0.8.0
   * <AUTHOR> Yi-Cyuan [<EMAIL>]
   * @copyright Chen, Yi-Cyuan 2015-2018
   * @license MIT
   *)

safe-buffer/index.js:
  (*! safe-buffer. MIT License. Feross Aboukhadijeh <https://feross.org/opensource> *)

@solana/buffer-layout/lib/Layout.js:
  (**
   * Support for translating between Uint8Array instances and JavaScript
   * native types.
   *
   * {@link module:Layout~Layout|Layout} is the basis of a class
   * hierarchy that associates property names with sequences of encoded
   * bytes.
   *
   * Layouts are supported for these scalar (numeric) types:
   * * {@link module:Layout~UInt|Unsigned integers in little-endian
   *   format} with {@link module:Layout.u8|8-bit}, {@link
   *   module:Layout.u16|16-bit}, {@link module:Layout.u24|24-bit},
   *   {@link module:Layout.u32|32-bit}, {@link
   *   module:Layout.u40|40-bit}, and {@link module:Layout.u48|48-bit}
   *   representation ranges;
   * * {@link module:Layout~UIntBE|Unsigned integers in big-endian
   *   format} with {@link module:Layout.u16be|16-bit}, {@link
   *   module:Layout.u24be|24-bit}, {@link module:Layout.u32be|32-bit},
   *   {@link module:Layout.u40be|40-bit}, and {@link
   *   module:Layout.u48be|48-bit} representation ranges;
   * * {@link module:Layout~Int|Signed integers in little-endian
   *   format} with {@link module:Layout.s8|8-bit}, {@link
   *   module:Layout.s16|16-bit}, {@link module:Layout.s24|24-bit},
   *   {@link module:Layout.s32|32-bit}, {@link
   *   module:Layout.s40|40-bit}, and {@link module:Layout.s48|48-bit}
   *   representation ranges;
   * * {@link module:Layout~IntBE|Signed integers in big-endian format}
   *   with {@link module:Layout.s16be|16-bit}, {@link
   *   module:Layout.s24be|24-bit}, {@link module:Layout.s32be|32-bit},
   *   {@link module:Layout.s40be|40-bit}, and {@link
   *   module:Layout.s48be|48-bit} representation ranges;
   * * 64-bit integral values that decode to an exact (if magnitude is
   *   less than 2^53) or nearby integral Number in {@link
   *   module:Layout.nu64|unsigned little-endian}, {@link
   *   module:Layout.nu64be|unsigned big-endian}, {@link
   *   module:Layout.ns64|signed little-endian}, and {@link
   *   module:Layout.ns64be|unsigned big-endian} encodings;
   * * 32-bit floating point values with {@link
   *   module:Layout.f32|little-endian} and {@link
   *   module:Layout.f32be|big-endian} representations;
   * * 64-bit floating point values with {@link
   *   module:Layout.f64|little-endian} and {@link
   *   module:Layout.f64be|big-endian} representations;
   * * {@link module:Layout.const|Constants} that take no space in the
   *   encoded expression.
   *
   * and for these aggregate types:
   * * {@link module:Layout.seq|Sequence}s of instances of a {@link
   *   module:Layout~Layout|Layout}, with JavaScript representation as
   *   an Array and constant or data-dependent {@link
   *   module:Layout~Sequence#count|length};
   * * {@link module:Layout.struct|Structure}s that aggregate a
   *   heterogeneous sequence of {@link module:Layout~Layout|Layout}
   *   instances, with JavaScript representation as an Object;
   * * {@link module:Layout.union|Union}s that support multiple {@link
   *   module:Layout~VariantLayout|variant layouts} over a fixed
   *   (padded) or variable (not padded) span of bytes, using an
   *   unsigned integer at the start of the data or a separate {@link
   *   module:Layout.unionLayoutDiscriminator|layout element} to
   *   determine which layout to use when interpreting the buffer
   *   contents;
   * * {@link module:Layout.bits|BitStructure}s that contain a sequence
   *   of individual {@link
   *   module:Layout~BitStructure#addField|BitField}s packed into an 8,
   *   16, 24, or 32-bit unsigned integer starting at the least- or
   *   most-significant bit;
   * * {@link module:Layout.cstr|C strings} of varying length;
   * * {@link module:Layout.blob|Blobs} of fixed- or variable-{@link
   *   module:Layout~Blob#length|length} raw data.
   *
   * All {@link module:Layout~Layout|Layout} instances are immutable
   * after construction, to prevent internal state from becoming
   * inconsistent.
   *
   * @local Layout
   * @local ExternalLayout
   * @local GreedyCount
   * @local OffsetLayout
   * @local UInt
   * @local UIntBE
   * @local Int
   * @local IntBE
   * @local NearUInt64
   * @local NearUInt64BE
   * @local NearInt64
   * @local NearInt64BE
   * @local Float
   * @local FloatBE
   * @local Double
   * @local DoubleBE
   * @local Sequence
   * @local Structure
   * @local UnionDiscriminator
   * @local UnionLayoutDiscriminator
   * @local Union
   * @local VariantLayout
   * @local BitStructure
   * @local BitField
   * @local Boolean
   * @local Blob
   * @local CString
   * @local Constant
   * @local bindConstructorLayout
   * @module Layout
   * @license MIT
   * <AUTHOR> A. Bigot
   * @see {@link https://github.com/pabigot/buffer-layout|buffer-layout on GitHub}
   *)

buffer/index.js:
  (*!
   * The buffer module from node.js, for the browser.
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)

@noble/hashes/esm/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)

@noble/ed25519/lib/esm/index.js:
  (*! noble-ed25519 - MIT License (c) 2019 Paul Miller (paulmillr.com) *)

@babel/runtime/helpers/regeneratorRuntime.js:
  (*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE *)

@noble/secp256k1/lib/esm/index.js:
  (*! noble-secp256k1 - MIT License (c) 2019 Paul Miller (paulmillr.com) *)
*/
