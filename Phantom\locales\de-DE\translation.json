{"accountHeaderConnectedInterpolated": "Sie sind verbunden mit {{origin}}", "accountHeaderConnectedToSite": "Sie sind verbunden mit dieser Seite", "accountHeaderCopied": "Kopiert!", "accountHeaderCopyToClipboard": "In die Zwischenablage kopieren", "accountHeaderNotConnected": "Sie sind nicht verbunden mit", "accountHeaderNotConnectedInterpolated": "Sie sind nicht verbunden mit {{origin}}", "accountHeaderNotConnectedToSite": "Sie sind nicht verbunden mit dieser Seite", "addAccountActionButtonClose": "Schließen", "addAccountHardwareWalletPrimaryText": "Hardware Wallet verbinden", "addAccountHardwareWalletSecondaryText": "Ledger <PERSON> benutzen", "addAccountHardwareWalletSecondaryTextMobile": "Ledger Nano S/X Wallet benutzen", "addAccountImportWalletPrimaryText": "Privaten Schlüssel importieren", "addAccountImportWalletSecondaryText": "Bestehendes Wallet importieren", "addAccountNewWalletPrimaryText": "Neues Wallet erstellen", "addAccountNewWalletSecondaryText": "<PERSON>eue Wallet-<PERSON><PERSON><PERSON> gene<PERSON>", "addAccountPrimaryText": "Wallet hinzufügen/verbinden", "addAccountImportAccountActionButtonImport": "Importieren", "addAccountImportAccountDuplicatePrivateKey": "Dieses <PERSON> existiert in Ihrem Wallet bereits", "addAccountImportAccountIncorrectFormat": "Falsches Format", "addAccountImportAccountInvalidPrivateKey": "Ungültiger privater <PERSON><PERSON><PERSON><PERSON>", "addAccountImportAccountName": "Name", "addAccountImportAccountPrimaryText": "Privaten Schlüssel importieren", "addAccountImportAccountPrivateKey": "Private<PERSON><PERSON><PERSON>", "addAccountImportAccountPrivateKeyRequired": "Privater <PERSON><PERSON><PERSON><PERSON> ist erforderlich", "addAddressActionButtonPrimary": "Hinzufügen", "addAddressActionButtonSecondary": "Abbrechen", "addAddressAddressAlreadyAdded": "<PERSON>resse wurde bereits hinzugefügt", "addAddressAddressAlreadyExists": "Adresse existiert bereits", "addAddressAddressIsRequired": "<PERSON>resse ist erforderlich", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "Label ist erforderlich", "addAddressLabelPlaceholder": "Label", "addAddressPrimaryText": "<PERSON><PERSON><PERSON>", "addEditTokenActionButtonAdd": "Hinzufügen", "addEditTokenActionButtonCancel": "Abbrechen", "addEditTokenActionButtonSave": "Speichern", "addEditTokenAddMetadata": "Token Metadaten hinzufügen", "addEditTokenEditMetadata": "Token Metadaten bearbeiten", "addEditTokenErrorAccountNotFound": "Token-Konto konnte nicht gefunden werden", "addEditTokenErrorDuplicateToken": "Sie haben diesen Token bereits", "addEditTokenErrorInvalidMint": "Ungültige Mint-Adresse", "addEditTokenErrorInvalidName": "Ungültiger Name", "addEditTokenErrorInvalidSymbol": "Ungültiges Symbol", "addEditTokenMintAddress": "Mint<PERSON><PERSON><PERSON><PERSON>", "addEditTokenName": "Name", "addEditTokenSymbol": "Symbol", "addEditTokenThisWillCost": "<PERSON><PERSON> kostet", "addEditTokenThisWillCostInterpolated": "Dies kostet {{amount}} SOL", "assetDetailActionButtonDeposit": "Ein<PERSON><PERSON><PERSON>", "assetDetailActionButtonSend": "Senden", "assetDetailButtonCancel": "Abbrechen", "assetDetailEditTokenMetadata": "Token Metadaten bearbeiten", "assetDetailRecentActivity": "Neueste Aktivität", "assetDetailStakeSOL": "SOL investieren", "assetDetailUnknownToken": "Unbekannter Token", "assetDetailUnwrapAll": "Alle auspacken", "assetDetailViewOnExplorer": "<PERSON><PERSON> <PERSON>", "assetDetailViewOnSolscan": "<PERSON><PERSON>", "assetListAddCustomToken": "Benutzerdefinierten Token hinzufügen", "assetListSearch": "Suche ...", "assetListUnknownToken": "Unbekannter Token", "assetSelectionClose": "Schließen", "assetVisibilityClose": "Schließen", "assetVisibilityUnknownToken": "Unbekannter Token", "blocklistConnectionActionButtonClose": "Schließen", "blocklistConnectionDisabled": "Phantom hält diese Website für bösartig und unsicher. Wir haben die Möglichkeit der Interaktion mit dieser Website deaktiviert, um Si<PERSON> und Ihr Geld zu schützen.", "blocklistConnectionIgnoreWarning": "Warnung ignorieren, trotzdem verbinden", "blocklistOriginCommunityDatabaseInterpolated": "Diese Seite wurde als Teil einer <1>von der Gemeinschaft gepflegten Datenbank</1> bekan<PERSON>-Webseiten und Betrugsversuche gekennzeichnet. Wenn <PERSON> glauben, dass die Seite fälschlicherweise gekennzeichnet wurde, <3>melden bitte Sie einen Fehler</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} ist blockiert!", "blocklistOriginIgnoreWarning": "<PERSON><PERSON> ignorier<PERSON>, <PERSON>ch möchte trotzdem zu {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom ist der Meinung, dass diese Website bösartig ist und nicht sicher verwendet werden kann.", "blocklistOriginThisDomain": "diese Domain", "maliciousTransactionWarningButtonClose": "Schließen", "maliciousTransactionWarning": "Phantom ist der Meinung, dass diese Transaktion bösartig ist und nicht sicher signiert werden kann. Wir haben die Möglichkeit, sie zu signieren, deakti<PERSON>t, um Si<PERSON> und Ihr Geld zu schützen.", "maliciousTransactionWarningIgnoreWarning": "<PERSON>nung ignorieren, trotzdem fortfahren", "maliciousTransactionWarningTitle": "Transaktion angezeigt!", "changeLockTimerActionButtonPrimary": "Speichern", "changeLockTimerActionButtonSecondary": "Abbrechen", "changeLockTimerPrimaryText": "Timer für automatische Sperre", "changeLockTimerSecondaryText": "Wie lange sollten wir warten, Ihr Wallet zu sperren, wenn nicht benutzt worden ist?", "changePasswordActionButtonPrimary": "Speichern", "changePasswordActionButtonSecondary": "Abbrechen", "changePasswordConfirmNewPassword": "Neues Passwort bestätigen", "changePasswordCurrentPassword": "Aktuelles Passwort", "changePasswordErrorIncorrectCurrentPassword": "Falsches aktuelles Passwort", "changePasswordErrorGeneric": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal", "changePasswordNewPassword": "Neues Passwort", "changePasswordPrimaryText": "Passwort ändern", "collectibleDetailDescription": "Beschreibung", "collectibleDetailProperties": "Eigenschaften", "collectibleDetailSend": "Senden", "collectibleDetailViewOnSolscan": "<PERSON><PERSON>", "collectibleDisplayLoading": "Lade ...", "collectiblesNoCollectibles": "<PERSON><PERSON>", "collectiblesPrimaryText": "Ihre Sammelobjekte", "collectiblesReceiveCollectible": "Sammelobjekte erhalten", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Verbinden Sie Ihr Hardware-Wallet und stellen Sie sicher, dass es freigeschaltet ist. <PERSON><PERSON><PERSON> wir es erkannt haben, können Sie die Adresse auswählen, die Si<PERSON> verwenden möchten.", "connectHardwareContinueActionButtonText": "Fortfahren", "connectHardwareFailedPrimaryText": "Verbindung fehlgeschlagen", "connectHardwareFailedRetryActionButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareFailedSecondaryText": "Bitte schließen Sie Ihr Hardware-Wallet an und stellen Si<PERSON> sicher, dass es freigeschaltet ist. <PERSON><PERSON>d wir es erkannt haben, können <PERSON> w<PERSON>hlen, welche Adresse Sie verwenden möchten.", "connectHardwareFinishPrimaryText": "Konto hinzugefügt!", "connectHardwareFinishSecondaryText": "<PERSON><PERSON> können nun von Phantom aus auf Ihr Ledger Nano Wallet zugreifen. Bitte kehren Sie zur Erweiterung zurück.", "connectHardwareNeedsPermissionPrimaryText": "Neues Wallet verbinden", "connectHardwareNeedsPermissionSecondaryText": "<PERSON>licken Sie auf die Schaltfläche unten, um den Verbindungsvorgang zu starten.", "connectHardwareSearchingPrimaryText": "Suche nach Wallet ...", "connectHardwareSearchingSecondaryText": "Schließen Sie Ihr Hardware-Wallet an, vergewissern <PERSON> sich, dass es freigeschaltet ist und dass Sie in Ihrem Browser über die entsprechenden Berechtigungen verfügen.", "connectHardwareSelectAddressAllAddressesImported": "Alle Adressen importiert", "connectHardwareSelectAddressDerivationPath": "Ableitungspfad", "connectHardwareSelectAddressSearching": "Suche ...", "connectHardwareSelectAddressSelectWalletAddress": "Wallet-Ad<PERSON><PERSON> au<PERSON>wählen", "connectHardwareSelectAddressWalletAddress": "Wall<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareWaitingForApplicationPrimaryText": "Öffnen Sie die Solana-App auf Ihrem Ledger", "connectHardwareWaitingForApplicationSecondaryText": "Bitte schließen Sie Ihr Hardware-Wallet an und stellen Si<PERSON> sicher, dass es entsperrt ist.", "connectHardwareWaitingForPermissionPrimaryText": "Benö<PERSON><PERSON>", "connectHardwareWaitingForPermissionSecondaryText": "Schließen Sie Ihr Hardware-Wallet an, vergewissern <PERSON> sich, dass es freigeschaltet ist und dass Sie in Ihrem Browser über die entsprechenden Berechtigungen verfügen.", "assetQueriesUnableToConnect": "Wir können keine Verbindung zu Solana herstellen", "assetQueriesUnableToFetchTokenPrices": "Wir waren nicht in der Lage, Token-Preise abzurufen", "connectionClusterInterpolated": "Sie sind momentan auf {{cluster}}", "copyDefaultCopyText": "<PERSON><PERSON><PERSON>", "copyCopiedText": "Kopiert!", "depositAssetActionButtonClose": "Schließen", "depositAssetBuyWithMoonpay": "<PERSON><PERSON> kaufen", "depositAssetDeposit": "Ein<PERSON><PERSON><PERSON>", "depositAssetDepositInterpolated": "{{tokenSymbol}} e<PERSON><PERSON><PERSON><PERSON>", "depositAssetFTXTooltipLabel": "Übertragen Sie SOL- und SPL-Token direkt von Ihrem FTX.us-Konto.", "depositAssetIntermediateDepositActionButtonClose": "Schließen", "depositAssetIntermediateDepositDeposit": "Ein<PERSON><PERSON><PERSON>", "depositAssetMoonPayTooltipLabel": "Kaufen Sie SOL ganz einfach mit einer Debitkarte, Kreditkarte oder Banküberweisung.", "depositAssetPrimaryText": "Ein<PERSON><PERSON><PERSON>", "depositAssetSecondaryText": "Diese Adresse kann nur für den Empfang von SOL- und SPL-Tokens auf Solana verwendet werden.", "depositAssetSendFrom": "<PERSON>/<PERSON><PERSON><PERSON> senden", "depositAssetTransferFromFTX": "Von FTX überweisen", "depositAssetShareAddress": "<PERSON><PERSON><PERSON> te<PERSON>n", "depositFlowActionButtonClose": "Schließen", "depositRowDepositSOL": "SOL einzahlen", "depositRowDepositDisclaimer": "SOL wird zur Bezahlung von Transaktionen verwendet", "editAddressActionButtonCancel": "Abbrechen", "editAddressActionButtonSave": "Speichern", "editAddressAddressAlreadyAdded": "<PERSON>resse wurde bereits hinzugefügt", "editAddressAddressAlreadyExists": "Adresse existiert bereits", "editAddressAddressIsRequired": "<PERSON>resse ist erforderlich", "editAddressPrimaryText": "<PERSON><PERSON><PERSON> bear<PERSON>", "editAddressRemove": "Aus Adressbuch entfernen", "exportSecretActionButtonDone": "<PERSON><PERSON><PERSON>", "exportSecretActionButtonPrimary": "<PERSON><PERSON>", "exportSecretActionButtonSecondary": "Abbrechen", "exportSecretErrorGeneric": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal", "exportSecretErrorIncorrectPassword": "Falsches Passwort", "exportSecretPassword": "Passwort", "exportSecretPrivateKey": "Private<PERSON><PERSON><PERSON>", "exportSecretSecretPhrase": "Geheime Phrase", "exportSecretSecretRecoveryPhrase": "Geheime Recovery-Phrase", "exportSecretShowPrivateKey": "Privaten Schlüssel anzeigen", "exportSecretShowSecretRecoveryPhrase": "Geheime Recovery-Phrase anzeigen", "exportSecretWarningPrimaryInterpolated": "Verraten Sie <1>nie</1> Ihre/n {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "<PERSON><PERSON> jemand <PERSON>e/n {{secretNameText}} hat, hat er die volle Kontrolle über Ihr Wallet.", "exportSecretYourPrivateKey": "Ihr Privater <PERSON>", "exportSecretYourSecretRecoveryPhrase": "<PERSON>hre geheime Recovery-Phrase", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "<PERSON><PERSON><PERSON>", "homeManageTokenList": "Token-Liste verwalten", "homeDeposit": "Ein<PERSON><PERSON><PERSON>", "homeSend": "Senden", "ledgerActionActionButtonCancel": "Abbrechen", "ledgerActionActionButtonContinue": "Fortfahren", "ledgerActionApprove": "Ledger <PERSON><PERSON>", "ledgerActionActionButtonRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledgerActionErrorBlindSignDisabledPrimaryText": "Blinde Signatur deaktiviert", "ledgerActionErrorBlindSignDisabledSecondaryText": "Vergewissern <PERSON>h, dass die blinde Signatur auf Ihrem Gerät aktiviert ist, und versuchen Sie die Aktion erneut", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Hardware-Gerät wurde während des Betriebs getrennt", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Bitte schließen Sie die Phantom-Erweiterung und versuchen Sie die Aktion erneut", "ledgerActionErrorDeviceLockedPrimaryText": "Hardware-<PERSON><PERSON><PERSON> g<PERSON>", "ledgerActionErrorDeviceLockedSecondaryText": "Bitte entsperren Sie Ihr Gerät und versuchen Sie die Aktion erneut", "ledgerActionErrorHeader": "Fehler bei Ledger-Aktionen", "ledgerActionErrorUserRejectionPrimaryText": "Nutzer lehnte Transaktion ab", "ledgerActionErrorUserRejectionSecondaryText": "Die Aktion wurde auf dem Hardware-Gerät vom Benutzer abgelehnt", "ledgerActionNeedPermission": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledgerActionNeedToApprove": "Sie müssen die Transaktion auf Ihrem Hardware-Wallet genehmigen. <PERSON><PERSON><PERSON>, dass es entsperrt und ist in der Solana-App aufgeführt wird", "ledgerActionPleaseConnect": "Bitte verbinden Sie Ihr Ledger Nano", "ledgerActionPleaseConnectAndApprove": "Bitte schließen Sie Ihr Hardware-Wallet an und stellen Si<PERSON> sicher, dass es freigeschaltet ist. Vergewissern Si<PERSON> sich, dass Sie in Ihrem Browser die entsprechenden Berechtigungen haben.", "maxInputAmount": "Betrag", "maxInputMax": "<PERSON>.", "notEnoughSolActionButtonCancel": "Abbrechen", "notEnoughSolPrimaryText": "Nicht genügend SOL", "notEnoughSolSecondaryText": "Sie haben nicht genug SOL in Ihrem Wallet, um die Transaktionsgebühr zu bezahlen. Bitte zahlen Sie mehr ein und versuchen Sie es erneut.", "notificationApplicationApprovalPermissionsPrimary": "<PERSON><PERSON> möchte:", "notificationApplicationApprovalPermissionsTransactionApproval": "Genehmigung von Transaktionen beantragen", "notificationApplicationApprovalPermissionsViewWalletActivity": "Anzeige des Kontostands und der Aktivitäten in Ihres Wallets", "notificationApplicationApprovalActionButtonConnect": "Verbinden", "notificationApplicationApprovalActionButtonCancel": "Abbrechen", "notificationApplicationApprovalAllowApproval": "Verbinden der Website zulassen?", "notificationApplicationApprovalAutoApprove": "Transaktionen automatisch genehmigen", "notificationApplicationApprovalConnectDisclaimer": "<PERSON>ur Verbindungen zu Websites herstellen, denen Si<PERSON> vertrauen", "notificationSignatureRequestApproveTransaction": "Transaktion genehmigen", "notificationSignatureRequestApproveTransactionCapitalized": "Transaktion genehmigen", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON>g auf Signatur", "notificationTransactionApprovalActionButtonApprove": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonCancel": "Abbrechen", "notificationTransactionApprovalEstimatedBalanceChanges": "Geschätzte Guthabenänderungen", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Die Schätzungen basieren auf Transaktionssimulationen und stellen keine Garantie dar", "notificationTransactionApprovalHideAdvancedDetails": "Erweiterte Transaktionsdetails ausblenden", "notificationTransactionApprovalNetworkFee": "Netzwerkgebühr", "notificationTransactionApprovalNoBalanceChanges": "Keine Änderungen am Guthaben gefunden", "notificationTransactionApprovalSolanaAmountRequired": "Bet<PERSON>, den das Solana-Netz zur Abwicklung der Transaktion benötigt", "notificationTransactionApprovalTransactionMayFailToConfirm": "Transaktion wird möglicherweise nicht bestätigt", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Änderungen am Guthaben können nicht abgerufen werden", "notificationTransactionApprovalViewAdvancedDetails": "Erweiterte Transaktionsdetails anzeigen", "notificationTransactionApprovalSignUnableToSimulate": "Diese Transaktion kann nicht simuliert werden. Eine Genehmigung kann zum Verlust von Geldern führen.", "notificationTransactionApprovalKnownMalicious": "Diese Transaktion ist betrügerisch. Die Unterzeichnung führt zum Verlust von Geldern.", "notificationTransactionApprovalSuspectedMalicious": "Wir vermuten, dass diese Transaktion betrügerisch ist. Die Genehmigung kann zum Verlust von Geldern führen.", "onboardingCreatePassword": "Passwort erstellen", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Ich akzeptiere die <1>Nutzungsbedingungen</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Passwort bestätigen", "onboardingCreatePasswordContinue": "Fortfahren", "onboardingCreatePasswordDescription": "Damit können Sie Ihr Wallet entsperren.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Ungültige geheime Recovery-Phrase", "onboardingCreatePasswordPasswordPlaceholder": "Passwort", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthMedium": "Medium", "onboardingCreatePasswordPasswordStrengthStrong": "<PERSON>", "onboardingCreateRecoveryPhraseContinue": "Fortfahren", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Ich habe meine geheime Recovery-Phrase gespeichert", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Geheime Recovery-Phrase", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Diese Phrase ist der EINZIGE Weg, um Ihr Wallet wiederherzustellen. Teilen Sie ihn NICHT mit anderen!", "onboardingCreateRecoveryPhraseSaveIn1Password": "In 1Password speichern", "onboardingCreateRecoveryPhraseSaved": "Gespeichert!", "onboardingImportWallet": "Wallet importieren", "onboardingImportWalletImportExistingWallet": "Importieren Sie ein bestehendes Wallet mit Ihrer geheimen 12- oder 24-Wort-Recovery-Phrase.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON> wied<PERSON><PERSON><PERSON>len", "onboardingImportWalletSecretRecoveryPhrase": "Geheime Recovery-Phrase", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Ungültige geheime Recovery-Phrase", "onboardingImportWalletIHaveWords": "Ich habe eine {{numWords}}-Recovery-Phrase", "onboardingImportWalletScrollDown": "Runterscrollen", "onboardingImportWalletScrollUp": "Raufscrollen", "onboardingKeyboardShortcut": "Tastaturkürzel", "onboardingKeyboardShortcutContinue": "Fortfahren", "onboardingKeyboardShortcutDescription": "Sie können Phantom jederzeit mit diesem praktischen Tastaturkürzel ö<PERSON>nen.", "onboardingKeyboardShortcutMac": "Option + Umschalttaste + P", "onboardingKeyboardShortcutNotMac": "Alt + Umschalttaste + P", "onboardingKeyboardShortcutTry": "Versuchen Sie:", "onboardingPathSelectionCreateWallet": "Neues Wallet erstellen", "onboardingPathSelectionTagline": "Ein neu gestaltetes Wallet für DeFi & NFTs", "onboardingPathSelectionIHaveAWallet": "Ich habe bereits ein <PERSON>et", "onboardingSelectAccountsImportAccounts": "Konten importieren", "onboardingSelectAccountsImportAccountsDescription": "Wählen Sie Wallet-Konten zum Importieren aus.", "onboardingSelectAccountsImportSelectedAccounts": "Ausgewählte Konten importieren", "onboardingSocialsFinishAction": "Abschließen", "onboardingSocialsFinished": "Sie sind fertig!", "onboardingSocialsFinishedDescription": "Verfolgen Sie die Produktaktualisierungen oder wenden Si<PERSON> sich an uns, wenn Sie Fragen haben.", "onboardingSocialsFollowOnTwitter": "Folgen Sie uns auf Twitter", "onboardingSocialsVisitHelpCenter": "Besuchen Sie das Hilfezentrum", "recentActivityPrimaryText": "Neueste Aktivität", "removeAccountActionButtonCancel": "Abbrechen", "removeAccountActionButtonRemove": "Entfernen", "removeAccountRemoveWallet": "<PERSON><PERSON> ent<PERSON>nen", "removeAccountWarningLedger": "Auch wenn Sie dieses Wallet aus Phantom entfernen, können Sie es über den Workflow \"Hardware Wallet Verbinden\" wieder hinzufügen.", "removeAccountWarningPrivateKey": "<PERSON><PERSON><PERSON> dies<PERSON> en<PERSON>fernen, wird <PERSON> es nicht mehr für Si<PERSON> wiederherstellen können. <PERSON><PERSON><PERSON>, dass Si<PERSON> eine Sicherungskopie Ihres privaten Schlüssels haben.", "removeAccountWarningSeed": "Auch wenn Sie dieses Wallet aus Phantom entfernen, können Sie es unter Verwendung Ihrer Mnemonik in diesem oder einem anderen Wallet wiederherstellen.", "resetSeedActionButtonPrimary": "Fortfahren", "resetSeedActionButtonSecondary": "Abbrechen", "resetSeedPrimaryText": "Setze Ihre geheime Recovery-Phrase zurück", "resetSeedSecondaryText": "Dadurch werden alle vorhandenen Wallets gelöscht und durch neue Wallets ersetzt. <PERSON><PERSON><PERSON>, dass Sie Ihre bestehende geheime Phrase und privaten Schlüssel gesichert haben.", "richTransactionsDays": "Tage", "richTransactionsToday": "<PERSON><PERSON>", "richTransactionsYesterday": "Gestern", "richTransactionDetailAccount": "Account", "richTransactionDetailAt": "am", "richTransactionDetailCompleted": "Abgeschlossen", "richTransactionDetailConfirmed": "Bestätigt", "richTransactionDetailAppInteraction": "App-Interaktion", "richTransactionDetailDate": "Datum", "richTransactionDetailFailed": "Fehlgeschlagen", "richTransactionDetailFrom": "<PERSON>", "richTransactionDetailNetworkFee": "Netzwerkgebühr", "richTransactionDetailPending": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON>", "richTransactionDetailReceived": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailSent": "Gesendet", "richTransactionDetailStaked": "Investieren", "richTransactionDetailStatus": "Status", "richTransactionDetailSwap": "Tauschen", "richTransactionDetailSwapDetails": "Tausch-Details", "richTransactionDetailTo": "An", "richTransactionDetailTokenSwap": "Token-Tausch", "richTransactionDetailUnknownNFT": "Unbekanntes NFT", "richTransactionDetailUnstaked": "<PERSON><PERSON> investiert", "richTransactionDetailValidator": "Validator", "richTransactionDetailViewOnSolscan": "<PERSON><PERSON>", "richTransactionDetailWithdrawStake": "Withdraw Stake", "richTransactionDetailYouPaid": "<PERSON><PERSON> be<PERSON>", "richTransactionDetailYouReceived": "<PERSON>e er<PERSON>ten", "sendAddressBookButtonLabel": "Adressbuch", "addressBookSelectAddressBook": "Adressbuch", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON>", "sendAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>", "addressBookSelectRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>", "sendConfirmationActionButtonCancel": "Abbrechen", "sendConfirmationActionButtonSend": "Senden", "sendConfirmationLabel": "Label", "sendConfirmationMessage": "Nachricht", "sendConfirmationNetworkFee": "Netzwerkgebühr", "sendConfirmationPrimaryText": "Senden bestätigen", "sendStatusErrorActionButtonCancel": "Abbrechen", "sendStatusErrorActionButtonRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendStatusErrorMessageInterpolated": "Es ist ein Fehler beim Versuch aufgetreten, Token an <1>{{uiRecipient}}</1> zu senden", "sendStatusErrorTitle": "Senden nicht möglich", "sendStatusLoadingTitle": "Sende ...", "sendStatusSuccessClose": "Schließen", "sendStatusSuccessMessageInterpolated": "Ihre Token wurden erfolgreich an <1>{{uiRecipient}}</1> gesendet", "sendStatusSuccessTitle": "Gesendet!", "sendFormActionButtonNext": "<PERSON><PERSON>", "sendFormActionButtonCancel": "Abbrechen", "sendFormErrorInsufficientBalance": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "sendFormErrorInvalidSolanaAddress": "Ungültige Solana-Adresse", "sendFormErrorInvalidTwitterHandle": "<PERSON>ses Twitter-Name ist nicht registriert", "sendFormErrorInvalidDomain": "Diese Domain ist nicht registriert", "sendFormErrorMinRequiredInterpolated": "Mindestens {{minAmount}} {{tokenName}} er<PERSON><PERSON><PERSON>", "sendFormSend": "Senden", "sendRecipientTextareaPlaceholder": "SOL-Adresse des Empfängers", "sendSelectionActionButtonClose": "Schließen", "settings": "Einstellungen", "settingsAddressBookNoLabel": "Kein Label", "settingsAddressBookPrimary": "Adressbuch", "settingsAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>", "settingsAddressBookSecondary": "Häufig verwendete Adressen verwalten", "settingsAutoLockTimerPrimary": "Timer für automatische Sperre", "settingsAutoLockTimerSecondary": "Dauer des Timers für Ihre automatische Sperre ändern", "settingsChangeLanguagePrimary": "Sprache ändern", "settingsChangeLanguageSecondary": "Anzeigesprache ändern", "settingsChangeNetworkPrimary": "Netzwerk ändern", "settingsChangeNetworkSecondary": "Ihre Netzwerkeinstellungen konfigurieren", "settingsChangePasswordPrimary": "Passwort ändern", "settingsChangePasswordSecondary": "Passwort für die Bildschirmsperre ändern", "settingsDisplayLanguage": "Anzeigesprache", "settingsErrorCannotExportLedgerPrivateKey": "Privater Ledger-Schlüssel kann nicht exportiert werden", "settingsErrorCannotRemoveAllWallets": "Kann nicht alle Wallets entfernen", "settingsExportPrivateKey": "Privaten Schlüssel exportieren", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNoAddresses": "<PERSON><PERSON>", "settingsNoTrustedApps": "<PERSON><PERSON> vertrauenswürdigen Anwendungen", "settingsRemoveWallet": "<PERSON><PERSON> ent<PERSON>nen", "settingsResetSecretRecoveryPhrase": "Geheime Recovery-<PERSON><PERSON>", "settingsShowSecretRecoveryPhrase": "Geheime Recovery-Phrase anzeigen", "settingsTrustedAppsAutoApprove": "Automatisch genehmigen", "settingsTrustedAppsDisclaimer": "Automatische Freigabe nur für vertrauenswürdige Websites aktivieren", "settingsTrustedAppsPrimary": "Vertrauenswürdige Apps", "settingsTrustedAppsRevoke": "Widerrufen", "settingsTrustedAppsSecondary": "Ihre vertrauenswürdigen Anwendungen konfigurieren", "stakeAccountCardActiveStake": "Aktiver Einsatz", "stakeAccountCardBalance": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountCardRentReserve": "Mietreserve", "stakeAccountCardRewards": "Bel<PERSON>nungen", "stakeAccountCardStakeAccount": "Einsatzkonto", "stakeAccountCreateAndDelegateErrorStaking": "Bei dem Einsatz mit diesem Validierer ist ein Problem aufgetreten. Bitte versuchen Sie es erneut.", "stakeAccountCreateAndDelegateSolStaked": "SOL investiert!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Ihre SOL werden in den nächsten Tagen mit dem Gewinn von Provisionen <1></1> beginnen, sobald das Einsatzkonto aktiv wird.", "stakeAccountCreateAndDelegateStakingFailed": "Einsatz fehlgeschlagen", "stakeAccountCreateAndDelegateStakingSol": "Setze SOL ein ...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Wir erstellen ein Einsatzkonto und delegieren dann Ihr SOL an", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Wir erstellen ein Einsatzkonto und delegieren dann Ihr SOL an {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Transaktion ansehen", "stakeAccountDeactivateStakeSolUnstaked": "SOL abgehoben!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Sie werden Ihren Einsatz <1></1> in den nächsten Tagen abheben können, sobald das Einsatzkonto inaktiv wird.", "stakeAccountDeactivateStakeUnstakingFailed": "Zurücknehmen des Einsatzes fehlgeschlagen", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Es gab ein Problem beim Zurücknehmen des Einsatzes mit diesem Validierer. Bitte versuchen Sie es erneut.", "stakeAccountDeactivateStakeUnstakingSol": "Nehme SOL-Einsatz zurück ...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Wir beginnen jetzt mit dem Verfahren zum Abheben Ihrer SOL.", "stakeAccountDeactivateStakeViewTransaction": "Transaktion ansehen", "stakeAccountDelegateStakeSolStaked": "SOL investiert!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Ihre SOL werden in den nächsten Tagen mit dem Gewinn von Provisionen <1></1> beginnen, sobald das Investitionskonto aktiv wird.", "stakeAccountDelegateStakeStakingFailed": "Einsatz fehlgeschlagen", "stakeAccountDelegateStakeStakingFailedDescription": "Bei dem Einsatz mit diesem Validierer ist ein Problem aufgetreten. Bitte versuchen Sie es erneut.", "stakeAccountDelegateStakeStakingSol": "Setze SOL ein ...", "stakeAccountDelegateStakeStakingSolDescription": "Wir delegieren Ihre SOL.", "stakeAccountDelegateStakeViewTransaction": "Transaktion ansehen", "stakeAccountListActivationActivating": "Aktiviere", "stakeAccountListActivationActive": "Aktiv", "stakeAccountListActivationInactive": "Inaktiv", "stakeAccountListActivationDeactivating": "Deaktiviere", "stakeAccountListErrorFetching": "Es gab ein Problem beim Abrufen von Einsatzkonten:", "stakeAccountListNoStakingAccounts": "<PERSON><PERSON>", "stakeAccountListReload": "Neu laden", "stakeAccountListViewPrimaryText": "Ihr Einsatz", "stakeAccountListViewStakeSOL": "SOL investieren", "stakeAccountViewActionButtonClose": "Schließen", "stakeAccountViewActionButtonRestake": "<PERSON><PERSON><PERSON> investieren", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewError": "<PERSON><PERSON>", "stakeAccountViewPrimaryText": "Ihr Einsatz", "stakeAccountViewRestake": "<PERSON><PERSON><PERSON> investieren", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Ihre SOL sind derzeit mit einem Validierer eingesetzt. Sie müssen den Einsatz aufheben, um <1></1>auf diese Mittel zugreifen zu können. <3><PERSON>hr erfahren</3>", "stakeAccountViewStakeInactive": {"part1": "Dieses Einsatzkonto ist inaktiv. Ziehen Sie den Einsatz zurück oder suchen Si<PERSON> einen Validierer, an den Si<PERSON> ihn delegieren können.", "part2": "<PERSON><PERSON> er<PERSON>"}, "stakeAccountViewStakeNotFound": "Dieses Einsatzkonto konnte nicht gefunden werden.", "stakeAccountViewViewOnExplorer": "<PERSON><PERSON> <PERSON>", "stakeAccountViewViewOnSolscan": "<PERSON><PERSON>", "stakeAccountViewWithdrawStake": "Einsatz abheben", "stakeAccountViewWithdrawUnstakedSOL": "Nicht investierte SOL abheben", "stakeAccountWithdrawStakeSolWithdrawn": "SOL abgehoben!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Ihr SOL wurde abgehoben.", "part2": "Dieses Einsatzkonto wird in den nächsten Minuten automatisch entfernt werden."}, "stakeAccountWithdrawStakeViewTransaction": "Transaktion ansehen", "stakeAccountWithdrawStakeWithdrawalFailed": "Abheben fehlgeschlagen", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Es gab ein Problem bei der Abhebung von diesem Einsatzkonto. Bitte versuchen Sie es erneut.", "stakeAccountWithdrawStakeWithdrawingSol": "Hebe SOL ab ...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Wir heben Ihre SOL von diesem Einsatzkonto ab.", "startEarningSolAccount": "Ko<PERSON>", "startEarningSolAccounts": "Konten", "startEarningSolErrorClosePhantom": "Phantom schließen und erneut versuchen", "startEarningSolErrorTroubleLoading": "Probleme beim Laden des Einsatzes", "startEarningSolLoading": "Lade ...", "startEarningSolPrimaryText": "Beginnen Sie SOL zu verdienen", "startEarningSolSearching": "Suche nach Einsatzkonten", "startEarningSolStakeTokens": "Token investieren und Belohnungen verdienen", "startEarningSolYourStake": "Ihr Einsatz", "swapFeesEstimatedFees": "Geschätzte Gebühren", "swapFeesRate": "<PERSON><PERSON>", "swapFeesSlippage": "Verschiebung", "swapFeesSlippageDisclaimer": "Ihre Transaktion wird scheitern, wenn der Preis \nsich um mehr als diesen Prozentsatz ungünstig verändert.", "swapFeesSlippageTolerance": "Verschiebungstoleranz", "swapFeesPriceImpact": "Auswirkungen auf den Preis", "swapFeesPriceImpactDisclaimer": "Die Differenz zwischen dem Marktpreis und dem geschätzten Preis auf der Grundlage Ihrer Handelsgröße.", "swapFlowYouPay": "<PERSON><PERSON> zahlen", "swapFlowYouReceive": "<PERSON>e erhalten", "swapFlowActionButtonText": "Bestellung überprüfen", "swapQuoteFeeDisclaimer": "<PERSON><PERSON> enthält eine {{feePercentage}} Phantom-Gebühr", "swapQuoteMissingContext": "Fehlender Kontext der Tauschnotierung", "swapQuoteErrorNoQuotes": "Versuch eines Tausches ohne Preisangaben", "swapQuoteSolanaNetwork": "Solana Netzwerk", "swapQuoteOneTimeTokenAccount": "Einmaliges Token-Konto", "swapReviewFlowActionButtonPrimary": "Tauschen", "swapReviewFlowActionButtonSecondary": "Abbrechen", "swapReviewFlowPrimaryText": "Bestellung überprüfen", "swapReviewFlowYouPay": "<PERSON><PERSON> zahlen", "swapReviewFlowYouReceive": "<PERSON>e erhalten", "swapTxConfirmationActionButtonClose": "Schließen", "swapTxConfirmationReceived": "<PERSON><PERSON><PERSON><PERSON>!", "swapTxConfirmationSwapFailed": "Tausch fehlgeschlagen", "swapTxConfirmationSwapFailedSlippageLimit": "Der Tausch hat das Verschiebungslimit erreicht, bitte versuchen Si<PERSON> es erneut.", "swapTxConfirmationSwapFailedTryAgain": "Der Tausch ist fehlgeschlagen, bitte versuchen Sie es erneut", "swapTxConfirmationSwappingTokens": "Tausche Token ...", "swapTxConfirmationTokens": "Token", "swapTxConfirmationTokensDeposited": "Es ist vollbracht! Die Token wurden in Ihr Wallet eingezahlt", "swapTxConfirmationTokensWillBeDeposited": "wird nach Abschluss der Transaktion auf Ihr Wallet überwiesen", "swapTxConfirmationViewTransaction": "Transaktion ansehen", "swapperMax": "<PERSON>.", "switchToggle": "Umschalten", "termsOfServiceActionButtonAgree": "Ich stimme zu", "termsOfServiceActionButtonCancel": "Abbrechen", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Indem Sie auf <1>\"Ich stimme zu\"</1> klick<PERSON>, akzeptieren Sie die <3>Bedingungen und Konditionen</3> für den Tausch von Token mit Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Wir haben unsere Servicebedingungen überarbeitet. Indem Sie auf <1>\"Ich stimme zu\"</1> klick<PERSON>, erkl<PERSON>ren Sie sich mit unseren neuen <3>Nutzungsbedingungen</3> einverstanden.<5></5><6></6>Unsere neuen Nutzungsbedingungen beinhalten eine neue <8>Gebührenstruktur</8> für bestimmte Produkte.", "termsOfServicePrimaryText": "Nutzungsbedingungen", "tokenRowUnknownToken": "Unbekannter Token", "transactionStatusDetailActionButtonClose": "Schließen", "transactionsAppInteraction": "App-Interaktion", "transactionsError": "<PERSON><PERSON>", "transactionsFailed": "Fehlgeschlagen", "transactionsFrom": "<PERSON>", "transactionsNoActivity": "Keine Aktivität", "transactionsReceived": "<PERSON><PERSON><PERSON><PERSON>", "transactionsReceivedInterpolated": "{{amount}} SOL erhalten", "transactionsSending": "Sende ...", "transactionsSent": "Gesendet", "transactionsSwapOn": "Tausch an", "transactionsSentInterpolated": "{{amount}} SOL gesendet", "transactionsStaked": "Investiert", "transactionsSuccess": "Erfolg", "transactionsTo": "An", "transactionsTokenSwap": "Token-Tausch", "transactionsUnstaked": "<PERSON><PERSON> investiert", "transactionsWaitingForConfirmation": "Warte auf Bestätigung", "transactionsWithdrawStake": "Withdraw Stake", "unlockActionButtonUnlock": "Freischalten", "unlockEnterPassword": "Geben Sie Ihr Passwort ein", "unlockErrorIncorrectPassword": "Falsches Passwort", "unlockErrorSomethingWentWrong": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal", "unlockForgotPassword": "Passwort vergessen?", "unlockPassword": "Passwort", "validationUtilsPasswordIsRequired": "Passwort ist erforderlich", "validationUtilsPasswordLength": "Das Passwort muss 8 <PERSON><PERSON><PERSON> lang sein", "validationUtilsPasswordsDontMatch": "Passwörter stimmen nicht überein", "validationUtilsPasswordCantBeSame": "Sie können Ihr altes Passwort nicht mehr verwenden", "validatorCardCommission": "Kommission", "validatorCardTotalStake": "Einsatz insgesamt", "validatorCardNumberOfDelegators": "# Delegierte", "validatorListActionButtonCancel": "Abbrechen", "validatorListChooseAValidator": "Wählen Sie einen Validierer", "validatorListErrorFetching": "Es gab ein Problem beim Abru<PERSON> von Validier<PERSON>:", "validatorListNoResults": "<PERSON><PERSON>", "validatorListReload": "Neu laden", "validatorListSearch": "<PERSON><PERSON>", "validatorViewActionButtonClose": "Schließen", "validatorViewActionButtonStake": "Einsatz", "validatorViewEdit": "<PERSON><PERSON><PERSON>", "validatorViewErrorFetching": "Konnte keine Validierer abrufen.", "validatorViewInsufficientBalance": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "validatorViewMax": "<PERSON>.", "validatorViewPrimaryText": "Starten Sie die Einsätze", "validatorViewSecondaryTextInterpolated": "<PERSON><PERSON><PERSON><PERSON>, wie viel SOL Sie <1></1>mit diesem Validierer einsetzen möchten. <3><PERSON><PERSON> erfahren</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL erforderlich für den Einsatz", "validatorViewValidator": "<PERSON><PERSON><PERSON><PERSON>", "walletMenuItemsAddConnectWallet": "Wallet hinzufügen/verbinden", "walletMenuItemsBridgeAssets": "Vermögenswerte überbrücken", "walletMenuItemsHelpAndSupport": "Hilfe & Support", "walletMenuItemsLockWallet": "Wallet sperren", "walletMenuItemsResetSecretPhrase": "Geheime Phrase zurücksetzen", "walletMenuItemsShowMoreAccounts": "{{count}} mehr anzeigen ...", "walletMenuItemsHideAccounts": "Konten ausblenden", "whatsNewOverlayActionButtonClose": "Schließen", "whatsNewOverlayNew": "Neu!", "whatsNewOverlayv1ActionGetAppNow": "<PERSON>n Si<PERSON> sich jetzt die App", "whatsNewOverlayv1PrimaryText": "Phantom für iOS ist jetzt verfügbar!", "whatsNewOverlayv1ScanWithCamera": "<PERSON>annen mit der iPhone-Kamera", "whatsNewOverlayv1SecondaryText": "Wir freuen uns, an<PERSON><PERSON><PERSON><PERSON> zu <PERSON>ö<PERSON>n, dass Phantom für iOS jetzt im App Store verfügbar ist! Erleben Sie die Leistung von Phantom in Ihrer Tasche!", "networkErrorTitle": "Netzwerkfehler", "networkError": "<PERSON><PERSON> können wir nicht auf das Netzwerk zugreifen. Bitte versuchen Si<PERSON> es später noch einmal.", "networkRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "authenticationUnlockPhantom": "Phantom ferischalten", "errorAndOfflineSomethingWentWrong": "Etwas ist schief gelaufen", "errorAndOfflineSomethingWentWrongTryAgain": "Bitte versuchen Sie es erneut.", "errorAndOfflineUnableToFetchAssets": "Wir konnten keine Vermögenswerte abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchCollectibles": "Wir konnten keine Sammelobjekte abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchSwap": "Wir konnten keine Tauschinformationen abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchTransactionHistory": "Wir konnten keine Transaktionsinformationen abrufen. Bitte versuchen Sie es später noch einmal.", "swapReviewError": "Bei der Überprüfung Ihrer Bestellung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "sendSelectToken": "Token wählen", "swapBalance": "Guthaben:", "swapTitle": "<PERSON><PERSON> tauschen", "swapSelectToken": "Token wählen", "aboutPrivacyPolicy": "Datenschutz", "aboutVersion": "Version {{version}}", "aboutVisitWebsite": "Website besuchen", "transactionsFromInterpolated": "Von: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON>", "transactionsToInterpolated": "An: {{to}}", "transactionsYesterday": "Gestern", "addEditTokenSuccessMessage": "Zugehö<PERSON><PERSON>-Konto erstellt", "addEditTokenFailureMessage": "Es gab ein Problem beim E<PERSON>ellen eines zugehörigen Token-Kontos. Bitte versuchen Sie es erneut.", "addEditTokenLoadingMessage": "Wir erstellen ein zugehöriges Token-Konto", "addEditTokenSuccessTitle": "To<PERSON> erfolgreich hinzugefügt", "addEditTokenFailureTitle": "Hinzufügen des Tokens fehlgeschlagen", "addEditTokenLoadingTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEditTokenAlreadyAdded": "Sie haben diesen Token bereits", "addEditTokenContinue": "Fortfahren", "addEditTokenPaste": "Einfügen", "addEditTokenRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addEditTokenViewTransaction": "Transaktion ansehen", "addEditTokenMintAddressError": "Ungültige oder nicht unterstützte Adresse", "addEditTokenNameError": "<PERSON><PERSON>staben, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>he, Bindestriche und Leerzeichen", "addEditTokenSymbolError": "Nur Buchstaben", "addEditAddressAdd": "<PERSON><PERSON><PERSON>", "addEditAddressCancel": "Abbrechen", "addEditAddressDelete": "<PERSON><PERSON><PERSON>", "addEditAddressDeleteTitle": "Sind <PERSON> sicher, dass Sie diese Adresse löschen möchten?", "addEditAddressPaste": "Einfügen", "addEditAddressSave": "<PERSON><PERSON><PERSON> s<PERSON>", "dAppBrowserComingSoon": "dApp Browser ist demnächst verfügbar!", "dAppBrowserSearchPlaceholder": "Website suchen oder e<PERSON>ben", "dAppBrowserFavorites": "<PERSON><PERSON>", "dAppBrowserTrustedApps": "Zuletzt verbunden", "dAppBrowserFavoritesDescription": "Ihre Favoriten werden hier angezeigt", "dAppBrowserEmptyScreenDescription": "<PERSON><PERSON>en Si<PERSON> eine URL ein oder suchen Si<PERSON>, um auf Ihre bevorzugten Solana-<PERSON><PERSON> zuzugreifen", "dAppBrowserBlocklistScreenTitle": "{{origin}} ist blockiert! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom ist der Meinung, dass diese Website bösartig ist und nicht sicher verwendet werden kann.", "part2": "Diese Seite wurde als Teil einer von der Gemeinschaft gepflegten Datenbank bekannter Phishing-Webseiten und Betrugsversuche gekennzeichnet. <PERSON><PERSON> glauben, dass die Seite fälschlicherweise gekennzeichnet wurde, melden bitte Si<PERSON> einen <PERSON>hler."}, "dAppBrowserBlocklistScreenIgnoreButton": "<PERSON>nu<PERSON> ignorieren, trotzdem anzeigen", "depositAssetListSuggestions": "Vorschläge", "depositUndefinedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dieser Token kann nicht eingezahlt werden", "onboardingImportRecoveryPhraseDetails": "Details", "onboardingCreateRecoveryPhraseVerifyTitle": "Haben Sie die geheime Recovery-Phrase aufgeschrieben?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Ohne die geheime Recovery-Phrase können Sie weder auf Ihren Schlüssel noch auf die damit verbundenen Vermögenswerte zugreifen.", "onboardingCreateRecoveryPhraseVerifyYes": "<PERSON>a", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON>s ist uns nicht gelungen, ein Konto zu erstellen. Bitte versuchen Si<PERSON> es erneut.", "onboardingDoneDescription": "Jetzt können Sie Ihr Wallet in vollen Zügen genießen.", "onboardingDoneGetStarted": "Loslegen", "onboardingImportAccountsEmptyResult": "<PERSON><PERSON>n gefunden", "onboardingImportAccountsWalletName": "Wallet {{walletIndex}}", "onboardingImportRecoveryPhraseLessThanTwelve": "Die Phrase muss mindestens 12 Wörter umfassen.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Die Phrase muss genau 12 oder 24 Wörter umfassen.", "onboardingImportRecoveryPhraseWrongWord": "Falsche Wörter: {{ words }}.", "onboardingProtectTitle": "Schützen Sie Ihr Wallet", "onboardingProtectDescription": "Mit einer biometrischen Sicherheitsfunktion können Sie sicherstellen, dass nur Sie selbst Zugriff auf Ihr Wallet haben.", "onboardingProtectButtonHeadlineDevice": "G<PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Fingerabdruck", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "{{ authType }} Authentifizierung benutzen", "onboardingProtectError": "Bei der Authentifizierung ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut", "onboardingProtectRemoveAuth": "Authentifizierung deaktivieren", "onboardingProtectRemoveAuthDescription": "Sind <PERSON> sicher, dass Sie die Authentifizierung deaktivieren möchten?", "onboardingProtectNext": "<PERSON><PERSON>", "onboardingWelcomeTitlePhantom": "Phantom ist ein freundliches\nSolana-Wwallet konzipiert für\nDeFi & NFTs", "onboardingWelcomeTitle": "A friendly Solana\nwallet built for\nDeFi & NFTs", "onboardingWelcomeCreateWallet": "Neues Wallet erstellen", "onboardingWelcomeAlreadyHaveWallet": "Ich habe bereits ein <PERSON>et", "onboardingSlide1Title": "<PERSON><PERSON>", "onboardingSlide1Description": "Wir haben <1>ni<PERSON><PERSON></1> zu Ihren Daten oder Geldern. Niemals.", "onboardingSlide2Title": "Ein Zuhause für Ihre NFTs", "onboardingSlide2Description": "Wir haben besonders darauf g<PERSON>, dass Ihre <1>NFTs</1> großartig aussehen!", "onboardingSlide3Title": "Einsatz & Tausch von <PERSON>ken", "onboardingSlide3Description": "<PERSON>t unserem S<PERSON>pper können Sie auf <1><PERSON><PERSON></1> <PERSON><PERSON> zu den <4>besten Preisen</4> tauschen, sofort.", "onboardingSlide4Title": "dApps nutzen", "onboardingSlide4Description": "Erkunden Sie die Welt der <1>Blockchain-Apps</1>, die auf Solana basieren.", "requireAuth": "Authentifizierung verlangen", "requireAuthImmediately": "Unmittelbar", "sendEnterAmount": "<PERSON><PERSON> e<PERSON>ben", "sendShowLogs": "Fehlerprotokolle anzeigen", "sendHideLogs": "Fehlerprotokolle ausblenden", "sendGoBack": "Zurück", "sendTransactionSuccess": "Ihre Token wurden erfolgreich gesendet an", "sendInputPlaceholder": "Name oder <PERSON><PERSON>e", "sendRecentlyUsedAddressLabel": "Vor {{formattedTimestamp}} benutzt", "sendRecipientAddress": "Adresse des Empfängers", "sendTokenInterpolated": "{{tokenSymbol}} senden", "sendPaste": "Einfügen", "sendPasteFromClipboard": "Aus Zwischenablage einfügen", "sendScanQR": "QR Code scannen", "sendTo": "An:", "sendCameraAccess": "Kamerazugriff", "sendCameraAccessSubtitle": "Um einen QR-Code zu scannen, muss der Kamerazugriff aktiviert sein. Möchten Sie die Einstellungen jetzt öffnen?", "sendCancel": "Abbrechen", "sendSettings": "Einstellungen", "sendOK": "OK", "invalidQRCode": "Dieser QR-Code ist nicht gültig.", "sendInvalidQRCode": "Dieser QR-Code ist keine gültige Adresse", "sendInvalidQRCodeSubtitle": "Versuchen Sie es erneut oder mit einem anderen QR-Code.", "sendInvalidQRCodeSplToken": "Ungültiger Token im QR-Code", "sendInvalidQRCodeSplTokenSubtitle": "Dieser QR-Code enthält einen Token, den Si<PERSON> nicht besitzen oder den wir nicht identifizieren können.", "sendScanAddressToSend": "<PERSON><PERSON><PERSON>e die {{tokenSymbol}} <PERSON><PERSON><PERSON>, um Geld zu senden", "sendScanAddressToSendCollectible": "Scannen Sie die SOL-Adresse, um das Sammelobjekt zu versenden", "sendSummary": "Zusammenfassung", "sendUndefinedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dieser Token kann nicht gesendet werden", "sendNoTokens": "<PERSON><PERSON> verfügbar", "settingsAbout": "Über Phantom", "settingsCancel": "Abbrechen", "settingsConfirm": "<PERSON>a", "settingsEdit": "<PERSON><PERSON><PERSON>", "settingsEditWallet": "Wallet bearbeiten", "settingsPrompt": "Sind Si<PERSON> sicher, dass Sie fortfahren wollen?", "settingsShowPrivateKey": "<PERSON><PERSON><PERSON> hi<PERSON>, um Ihren privaten Schlüssel zu offenbaren", "settingsShowRecoveryPhrase": "<PERSON><PERSON><PERSON>, um Ihre geheime Phrase zu offenbaren", "settingsMakeSureNoOneIsWatching": "<PERSON><PERSON><PERSON>, dass niemand Ihren Bildschirm beobachtet", "settingsSecurity": "Gerätesicherheit", "settingsSubmitBetaFeedback": "Beta-Feedback e<PERSON><PERSON><PERSON>n", "settingsWalletAddress": "Wall<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsWalletNamePrimary": "Wallet-Name", "settingsWalletNameSecondary": "Den Namen Ihres Wallets ändern", "settingsYourAccounts": "<PERSON><PERSON><PERSON>", "settingsNotifications": "Benachrichtigungen", "settingsNotificationPreferences": "Einstellungen für Benachrichtigungen", "pushNotificationsPreferencesAllowNotifications": "Benachrichtigungen erlauben", "pushNotificationsPreferencesSentTokens": "Gesendete Token", "pushNotificationsPreferencesSentTokensDescription": "Ausgehende Transfers von Token und NFTs", "pushNotificationsPreferencesReceivedTokens": "<PERSON><PERSON><PERSON><PERSON>", "pushNotificationsPreferencesReceivedTokensDescription": "Eingehende Transfers von Token und NFTs", "pushNotificationsPreferencesDexSwap": "Tauschvorgänge", "pushNotificationsPreferencesDexSwapDescription": "Tauschvorgänge bei erkannten Anwendungen", "pushNotificationsPreferencesOtherBalanceChanges": "Andere Guthabenänderungen", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Andere Transaktionen mit mehreren Token, die sich auf Ihr Guthaben auswirken", "pushNotificationsPreferencesPhantomMarketing": "Aktualisierungen von Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Funktionsankündigungen und allgemeine Aktualisierungen", "pushNotificationsPreferencesDescription": "Diese Einstellungen steuern die Push-Benachrichtigungen für dieses aktive Wallet. Jedes Wallet hat seine eigenen Benachrichtigungseinstellungen. Um alle Phantom-Push-Benachrichtigungen zu deaktivieren, gehen <PERSON><PERSON> zu Ihren <1>Geräteeinstellungen</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Die Benachrichtigungseinstellungen können nicht synchronisiert werden.", "addAccountHardwareWalletComingSoon": "Demnächst verfügbar", "stakeAmount": "Betrag", "stakeAmountNext": "<PERSON><PERSON>", "stakeAmountBalance": "<PERSON><PERSON><PERSON><PERSON>", "stakeReview": "Überprüfen", "stakeReviewAccount": "Ko<PERSON>", "stakeReviewCommissionFee": "Kommissionsgebühr", "stakeReviewConfirm": "Bestätigen", "stakeReviewValidator": "<PERSON><PERSON><PERSON><PERSON>", "swapTooltipGotIt": "Verstanden", "swapSetSlippageContinue": "Fortfahren", "swapSetSlippageWarning": "Sie könnten {{slippage}}% weniger erhalten, je nach Höhe der Verschiebung", "swapTabInsufficientFunds": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON> versuchen", "swapConfirmationGoBack": "Zurück", "unwrapWrappedSolClose": "Schließen", "unwrapWrappedSolError": "Auspacken fehlgeschlagen", "unwrapWrappedSolLoading": "Packe aus ...", "unwrapWrappedSolSuccess": "Ausgepackt", "unwrapWrappedSolViewTransaction": "Transaktion ansehen", "dappApprovePopupSignMessage": "Nachricht signieren", "solanaPayFrom": "<PERSON>", "solanaPayMessage": "Nachricht", "solanaPayNetworkFee": "Netzwerkgebühr", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "<PERSON><PERSON><PERSON>", "solanaPayPayNow": "Jetzt bezahlen", "solanaPaySent": "Gesendet!", "solanaPayTokensSent": "Ihre Token wurden erfolgreich gesendet an", "solanaPayViewTransaction": "Meine Transaktion ansehen", "solanaPayTransactionFailed": "Transaktion fehlgeschlagen", "solanaPayApprove": "<PERSON><PERSON><PERSON><PERSON>", "dappApproveConnectViewAccount": "<PERSON>hr <PERSON>ana-<PERSON><PERSON> anzeigen", "deepLinkInvalidLink": "Ungültiger Link", "deepLinkInvalidSplTokenSubtitle": "Dies enthält einen To<PERSON>, den <PERSON> nicht besitzen oder den wir nicht identifizieren können.", "walletAvatarShowAllAccounts": "Alle Konten anzeigen", "pushNotificationsGetInstantUpdates": "Sofortige Updates erhalten", "pushNotificationsEnablePushNotifications": "Aktivier<PERSON> von Push-Benachrichtigungen über abgeschlossene Überweisungen, Tauschvorgänge und Ankündigungen", "pushNotificationsEnable": "Einschalten", "pushNotificationsNotNow": "<PERSON><PERSON>t nicht", "onboardingAgreeToTermsOfServiceInterpolated": "Ich akzeptiere die <1>Nutzungsbedingungen</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, ich habe es gespeichert", "onboardingCreateNewWallet": "Neues Wallet erstellen", "onboardingErrorInvalidSecretRecoveryPhrase": "Ungültige geheime Recovery-Phrase", "onboardingFinished": "Sie sind fertig!", "onboardingImportAccounts": "Konten importieren", "onboardingImportAccountsLastUsed": "Vor {{formattedTimestamp}} benutzt", "onboardingImportAccountsNeverUsed": "<PERSON><PERSON>", "onboardingImportAccountsDescription": "Wählen Sie Wallet-Konten zum Importieren aus", "onboardingImportSecretRecoveryPhrase": "Geheime Recovery-Phrase importieren", "onboardingImportSelectedAccounts": "Ausgewählte Konten importieren", "onboardingRestoreExistingWallet": "<PERSON><PERSON><PERSON> ein bestehendes Wallet mit Ihrer geheimen 12- oder 24-Wort-Recovery-<PERSON><PERSON> wieder her", "onboardingShowUnusedAccounts": "Ungenutzte Konten anzeigen", "onboardingShowMoreAccounts": "Mehr Konten anzeigen", "onboardingHideUnusedAccounts": "Ungenutzte Konten ausblenden", "onboardingSecretRecoveryPhrase": "Geheime Recovery-Phrase", "onboardingSelectAccounts": "Wählen Sie Ihre Konten", "onboardingStoreSecretRecoveryPhraseReminder": "Dies ist die einzige Möglichkeit, Ihr Konto wiederherzustellen. Bitte bewahren Si<PERSON> es an einem sicheren Ort auf!", "timeUnitMinute": "Minute", "timeUnitMinutes": "Minutes", "timeUnitHour": "Stunde", "timeUnitHours": "Stunden", "espDexSwap": "Sie haben {{downTokensTextFragment}} für {{upTokensTextFragment}} auf {{dAppName}} getauscht", "espNFTBid": "Sie boten {{downTokensTextFragment}} für {{upTokensTextFragment}} auf {{dAppName}}", "espNFTBuy": "Sie haben {{nftName}} für {{downTokensTextFragment}} auf {{dAppName}} gekauft", "espNFTCancelBid": "Sie haben ein <PERSON>ot storniert und {{upTokensTextFragment}} auf {{dAppName}} erhalten", "espNFTList": "Sie haben {{downTokensTextFragment}} auf {{dAppName}} gelistet", "espNFTUnlist": "Sie haben {{upTokensTextFragment}} auf {{dAppName}} aufgehoben", "espTokenReceive": "Sie haben {{upTokensTextFragment}} erhalten", "espTokenSend": "Sie haben {{downTokensTextFragment}} gesendet", "espTokenTextFragment": "{{token1}} und {{token2}}", "espTransactionBalanceChange": "Sie haben {{downTokensTextFragment}} für {{upTokensTextFragment}} getauscht", "espUnknown": "UNBEKANNT", "espUnknownNFT": "Unbekanntes NFT"}