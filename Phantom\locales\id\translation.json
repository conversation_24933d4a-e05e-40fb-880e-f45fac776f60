{"commandAdd": "Tambahkan", "commandAccept": "Terima", "commandApply": "Terapkan", "commandApprove": "<PERSON><PERSON><PERSON><PERSON>", "commandAllow": "Izinkan", "commandBack": "Kembali", "commandBuy": "Bel<PERSON>", "commandCancel": "<PERSON><PERSON>", "commandClaim": "<PERSON><PERSON><PERSON>", "commandClaimReward": "<PERSON><PERSON><PERSON> <PERSON>", "commandClear": "Hapus", "commandClose": "<PERSON><PERSON><PERSON>", "commandConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandConnect": "Hubungkan", "commandContinue": "Lanjutkan", "commandConvert": "Kon<PERSON><PERSON>", "commandCopy": "<PERSON><PERSON>", "commandCopyAddress": "<PERSON><PERSON>", "commandCopyTokenAddress": "<PERSON><PERSON> alamat token", "commandCreate": "Buat", "commandCreateTicket": "<PERSON><PERSON><PERSON>", "commandDeny": "<PERSON><PERSON>", "commandDismiss": "<PERSON><PERSON><PERSON><PERSON>", "commandDontAllow": "<PERSON><PERSON>", "commandDownload": "<PERSON><PERSON><PERSON>", "commandEdit": "Edit", "commandEditProfile": "Edit Profil", "commandEnableNow": "Aktifkan <PERSON>", "commandFilter": "Filter", "commandFollow": "<PERSON><PERSON><PERSON>", "commandHelp": "Bantuan", "commandLearnMore": "<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t", "commandLearnMore2": "<PERSON><PERSON><PERSON><PERSON>", "commandMint": "Mint", "commandMore": "<PERSON><PERSON><PERSON>", "commandNext": "Berikutnya", "commandNotNow": "<PERSON><PERSON>", "commandOpen": "<PERSON><PERSON>", "commandOpenSettings": "<PERSON><PERSON>", "commandPaste": "Tempel", "commandReceive": "Terima", "commandReconnect": "<PERSON><PERSON><PERSON><PERSON> ulang", "commandRecordVideo": "Rekam Video", "commandRequest": "Minta", "commandRetry": "Coba lagi", "commandReview": "Tinjau", "commandRevoke": "<PERSON><PERSON>", "commandSave": "Simpan", "commandScanQRCode": "Pindai Kode QR", "commandSelect": "<PERSON><PERSON><PERSON>", "commandSelectMedia": "Pilih Media", "commandSell": "<PERSON><PERSON>", "commandSend": "<PERSON><PERSON>", "commandShare": "Bagikan", "commandShowBalance": "<PERSON><PERSON><PERSON><PERSON>", "commandSign": "<PERSON>da tangani", "commandSignOut": "Sign Out", "commandStake": "Staking", "commandMintLST": "Mint JitoSOL", "commandSwap": "<PERSON><PERSON>", "commandSwapAgain": "<PERSON><PERSON>", "commandTakePhoto": "Ambil Foto", "commandTryAgain": "<PERSON><PERSON>", "commandViewTransaction": "Li<PERSON>", "commandReportAsNotSpam": "Laporkan sebagai bukan spam", "commandReportAsSpam": "Laporkan sebagai spam", "commandPin": "Sematkan", "commandBlock": "Blokir", "commandUnblock": "<PERSON><PERSON> blokir", "commandUnstake": "Batalkan Staking", "commandUnpin": "<PERSON><PERSON>", "commandHide": "Sembunyikan", "commandUnhide": "<PERSON>al <PERSON>", "commandBurn": "<PERSON><PERSON>", "commandReport": "Laporkan", "commandView": "Lihat", "commandProceedAnywayUnsafe": "<PERSON><PERSON><PERSON> (tidak aman)", "commandUnfollow": "<PERSON><PERSON><PERSON><PERSON>", "commandUnwrap": "Unwrap", "commandConfirmUnsafe": "<PERSON><PERSON><PERSON><PERSON><PERSON> (tidak aman)", "commandYesConfirmUnsafe": "<PERSON>, kon<PERSON><PERSON><PERSON> (tidak aman)", "commandConfirmAnyway": "Te<PERSON>p k<PERSON>", "commandReportIssue": "Laporkan Masalah", "commandSearch": "<PERSON><PERSON>", "commandShowMore": "Selengkapnya", "commandShowLess": "<PERSON><PERSON><PERSON><PERSON>", "pastParticipleClaimed": "<PERSON><PERSON><PERSON>", "pastParticipleCompleted": "Se<PERSON><PERSON>", "pastParticipleCopied": "<PERSON><PERSON><PERSON>", "pastParticipleDone": "Se<PERSON><PERSON>", "pastParticipleDisabled": "<PERSON><PERSON><PERSON><PERSON>", "pastParticipleRequested": "<PERSON><PERSON><PERSON>", "nounName": "<PERSON><PERSON>", "nounNetwork": "<PERSON><PERSON><PERSON>", "nounNetworkFee": "<PERSON><PERSON><PERSON>", "nounSymbol": "Simbol", "nounType": "Tipe", "nounDescription": "<PERSON><PERSON><PERSON><PERSON>", "nounYes": "Ya", "nounNo": "Tidak", "amount": "<PERSON><PERSON><PERSON>", "limit": "Batas", "new": "<PERSON><PERSON>", "gotIt": "<PERSON><PERSON><PERSON>", "internal": "Internal", "reward": "Imbalan", "seeAll": "<PERSON><PERSON>a", "seeLess": "Kurangi", "viewAll": "<PERSON><PERSON>a", "homeTab": "Be<PERSON><PERSON>", "collectiblesTab": "<PERSON><PERSON><PERSON><PERSON>", "swapTab": "<PERSON><PERSON>", "activityTab": "Aktivitas", "exploreTab": "<PERSON><PERSON><PERSON><PERSON>", "accountHeaderConnectedInterpolated": "Anda terhubung ke {{origin}}", "accountHeaderConnectedToSite": "Anda terhubung ke situs ini", "accountHeaderCopyToClipboard": "<PERSON>in ke papan klip", "accountHeaderNotConnected": "Anda tidak terhubung ke", "accountHeaderNotConnectedInterpolated": "Anda tidak terhubung ke {{origin}}", "accountHeaderNotConnectedToSite": "Anda tidak terhubung ke situs ini", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "SOL tidak cukup", "accountWithoutEnoughSolSecondaryText": "Sebuah akun yang terlibat dalam transaksi ini tidak memiliki SOL yang mencukupi. Akun tersebut mungkin milik <PERSON>a atau orang lain. Transaksi ini akan dibatalkan jika dilakukan.", "accountSwitcher": "Pen<PERSON><PERSON>", "addAccountHardwareWalletPrimaryText": "Hubungkan Dompet Perangkat Keras", "addAccountHardwareWalletSecondaryText": "Gunakan dompet perangkat keras Ledger Anda", "addAccountHardwareWalletSecondaryTextMobile": "Gunakan dompet {{supportedHardwareWallets}} And<PERSON>", "addAccountSeedVaultWalletPrimaryText": "Hubungkan Seed Vault", "addAccountSeedVaultWalletSecondaryText": "<PERSON><PERSON><PERSON> dompet dari <PERSON>", "addAccountImportSeedPhrasePrimaryText": "<PERSON><PERSON><PERSON>", "addAccountImportSeedPhraseSecondaryText": "<PERSON><PERSON><PERSON> akun dari dompet lain", "addAccountImportWalletPrimaryText": "Kunci Privat Anda", "addAccountImportWalletSecondaryText": "I<PERSON>r akun rantai tunggal", "addAccountImportWalletSolanaSecondaryText": "<PERSON><PERSON>r kunci privat Solana", "addAccountLimitReachedText": "<PERSON>a telah mencapai batas akun {{accountsCount}} di Phantom. Hapuslah akun yang tidak digunakan sebelum menambahkan akun lain.", "addAccountNoSeedAvailableText": "Anda tidak memiliki frasa seed yang tersedia. Imporlah seed yang ada untuk membuat akun.", "addAccountNewWalletPrimaryText": "Buat A<PERSON>", "addAccountNewWalletSecondaryText": "<PERSON><PERSON>t alamat dompet baru", "addAccountNewMultiChainWalletSecondaryText": "Tambahkan akun multi-rantai baru", "addAccountNewSingleChainWalletSecondaryText": "Tambahkan akun baru", "addAccountPrimaryText": "Buat / Hubungkan Dompet", "addAccountSecretPhraseLabel": "Frasa <PERSON>has<PERSON>", "addAccountSeedLabel": "Seed", "addAccountSeedIDLabel": "ID Seed", "addAccountSecretPhraseDefaultLabel": "Frasa Rahasia {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON><PERSON> Privat {{angka}}", "addAccountZeroAccountsForSeed": "0 akun", "addAccountShowAccountForSeed": "Tampilkan 1 akun", "addAccountShowAccountsForSeed": "<PERSON><PERSON><PERSON><PERSON> {{numOfAccounts}} 1 akun", "addAccountHideAccountForSeed": "Sembunyikan 1 akun", "addAccountHideAccountsForSeed": "Sembunyikan {{numOfAccounts}} akun", "addAccountSelectSeedDescription": "Akun baru Anda akan dibuat dari Frasa <PERSON> ini", "addAccountNumAccountsForSeed": "{{numOfAccounts}} akun", "addAccountOneAccountsForSeed": "1 akun", "addAccountGenerateAccountFromSeed": "Buat A<PERSON>n", "addAccountReadOnly": "<PERSON><PERSON><PERSON>", "addAccountReadOnlySecondaryText": "Lacak alamat dompet umum apa pun", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Alamat EVM", "addAccountBitcoinAddress": "Alamat Bitcoin", "addAccountCreateSeedTitle": "Buat akun baru", "addAccountCreateSeedExplainer": "Dompet Anda belum memiliki frasa rahasia! Untuk membuat dompet baru, kami akan membuatkan frasa pemulihan. <PERSON><PERSON><PERSON> frasa ini dan jangan sebarkan kepada siapa pun.", "addAccountSecretPhraseHeader": "Fr<PERSON>", "addAccountNoSecretPhrases": "Tidak Ada Frasa Rahasia yang Tersedia", "addAccountImportAccountActionButtonImport": "Impor", "addAccountImportAccountDuplicatePrivateKey": "Akun ini sudah ada di dompet Anda", "addAccountImportAccountIncorrectFormat": "Format salah", "addAccountImportAccountInvalidPrivateKey": "Kunci Privat tidak valid", "addAccountImportAccountName": "<PERSON><PERSON>", "addAccountImportAccountPrimaryText": "Impor Kunci Privat", "addAccountImportAccountPrivateKey": "Kunci Privat", "addAccountImportAccountPublicKey": "<PERSON><PERSON>t atau Domain", "addAccountImportAccountPrivateKeyRequired": "Kunci Privat diperlukan", "addAccountImportAccountNameRequired": "<PERSON><PERSON>", "addAccountImportAccountPublicKeyRequired": "Alamat publik diperlukan", "addAccountImportAccountDuplicateAddress": "<PERSON><PERSON><PERSON> ini sudah ada di dompet Anda", "addAddressAddressAlreadyAdded": "<PERSON><PERSON><PERSON> telah ditam<PERSON>", "addAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON> sudah ada", "addAddressAddressInvalid": "<PERSON><PERSON><PERSON> tida<PERSON> valid", "addAddressAddressIsRequired": "<PERSON><PERSON><PERSON>", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "Label diperlukan", "addAddressLabelPlaceholder": "Label", "addAddressPrimaryText": "Tambah<PERSON>", "addAddressToast": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountCostLabelInterpolated": "Ini akan berbiaya {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Anda sudah memiliki akun token ini", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON> tidak cukup", "createAssociatedTokenAccountErrorInvalidMint": "Alamat mint tidak valid", "createAssociatedTokenAccountErrorInvalidName": "<PERSON><PERSON> tidak valid", "createAssociatedTokenAccountErrorInvalidSymbol": "Simbol tidak valid", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Kami tidak bisa membuat akun token Anda. Cobalah lagi.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "<PERSON><PERSON> membuat akun", "createAssociatedTokenAccountErrorUnableToSendMessage": "<PERSON>mi tidak dapat mengirimkan transaksi Anda.", "createAssociatedTokenAccountErrorUnableToSendTitle": "<PERSON><PERSON> mengirim transaksi", "createAssociatedTokenAccountInputPlaceholderMint": "Alamat Mint", "createAssociatedTokenAccountInputPlaceholderName": "<PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderSymbol": "Simbol", "createAssociatedTokenAccountLoadingMessage": "<PERSON><PERSON> sedang membuat akun token And<PERSON>.", "createAssociatedTokenAccountLoadingTitle": "Membuat akun token", "createAssociatedTokenAccountPageHeader": "Buat Akun <PERSON>", "createAssociatedTokenAccountSuccessMessage": "Akun token Anda berhasil dibuat!", "createAssociatedTokenAccountSuccessTitle": "Akun token telah dibuat", "createAssociatedTokenAccountViewTransaction": "<PERSON><PERSON>", "assetDetailRecentActivity": "Aktivitas Terbaru", "assetDetailStakeSOL": "Staking SOL", "assetDetailUnknownToken": "Token Tidak Diketahui", "assetDetailUnwrapAll": "<PERSON>w<PERSON>", "assetDetailUnwrappingSOL": "Meng-unwrap SOL", "assetDetailUnwrappingSOLFailed": "Unwrapping <PERSON><PERSON> gagal", "assetDetailViewOnExplorer": "<PERSON><PERSON> di {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorer", "assetDetailSaveToPhotos": "Simpan ke Foto", "assetDetailSaveToPhotosToast": "<PERSON>rs<PERSON><PERSON> ke Foto", "assetDetailPinCollection": "Sematkan <PERSON>", "assetDetailUnpinCollection": "Batal Sematkan Koleksi", "assetDetailHideCollection": "Sembunyikan Koleksi", "assetDetailUnhideCollection": "Perlihatkan Koleksi", "assetDetailTokenNameLabel": "<PERSON><PERSON>", "assetDetailNetworkLabel": "<PERSON><PERSON><PERSON>", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "<PERSON><PERSON>", "collectibleDetailSetAsAvatar": "<PERSON><PERSON><PERSON>", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar terpasang", "collectibleDetailShare": "Bagikan Collectible", "assetDetailTokenAddressCopied": "<PERSON><PERSON><PERSON>", "assetDetailStakingLabel": "Staking", "assetDetailAboutLabel": "Tentang {{fungibleName}}", "assetDetailPriceDetail": "<PERSON><PERSON>", "assetDetailHighlights": "<PERSON><PERSON><PERSON>", "assetDetailAllTimeReturn": "Pengembalian <PERSON>", "assetDetailAverageCost": "<PERSON><PERSON><PERSON>", "assetDetailPriceHistoryUnavailable": "Riwayat harga tidak tersedia untuk token ini", "assetDetailPriceHistoryInsufficientData": "Riwayat harga tidak tersedia untuk rentang waktu ini", "assetDetailPriceDataUnavailable": "Data harga tidak tersedia", "assetDetailPriceHistoryError": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mengambil riwayat harga", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1H", "assetDetailTimeFrame24h": "Harga 24 jam", "assetDetailTimeFrame1W": "1M", "assetDetailTimeFrame1M": "1B", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "SEMUA", "sendAssetAmountLabelInterpolated": "Tersedia {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Pengajuan harga", "fiatRampNewQuote": "Pengajuan harga baru", "assetListSelectToken": "<PERSON><PERSON><PERSON>", "assetListSearch": "<PERSON><PERSON><PERSON>...", "assetListUnknownToken": "Token Tidak Diketahui", "buyFlowHealthWarning": "Beberapa penyedia pembayaran kami mengalami lalu lintas yang tinggi. Penyetoran mungkin tertunda beberapa jam.", "assetVisibilityUnknownToken": "Token Tidak Dikenal", "buyAssetInterpolated": "Beli {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "Pembelian maksimum sejumlah {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Pembelian minimum sejumlah {{amount}}", "buyNoAssetsAvailable": "Tidak ada aset Ethereum atau Polygon yang tersedia", "buyThirdPartyScreenPaymentMethodSelector": "Bayar men<PERSON>kan", "buyThirdPartyScreenPaymentMethod": "<PERSON><PERSON><PERSON> metode pembayaran", "buyThirdPartyScreenChoseQuote": "<PERSON><PERSON><PERSON>n jumlah yang valid untuk pengajuan harga", "buyThirdPartyScreenProviders": "Penyedia", "buyThirdPartyScreenPaymentMethodTitle": "<PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodEmptyState": "Tidak ada metode pembayaran yang tersedia di wilayah Anda", "buyThirdPartyScreenPaymentMethodFooter": "Pembayaran didukung oleh mitra jaringan. Biaya dapat bervaria<PERSON>. Beberapa metode pembayaran tidak tersedia di wilayah Anda.", "buyThirdPartyScreenProvidersEmptyState": "Tidak ada penyedia yang tersedia di wilayah Anda", "buyThirdPartyScreenLoadingQuote": "<PERSON><PERSON><PERSON> penga<PERSON>an harga...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON> harga", "gasEstimationErrorWarning": "<PERSON> masalah dalam memperkirakan biaya untuk transaksi ini. <PERSON><PERSON><PERSON> mungkin gagal.", "gasEstimationCouldNotFetch": "Tidak dapat mengambil estimasi gas", "networkFeeCouldNotFetch": "Tidak dapat mengambil biaya jaringan", "nativeTokenBalanceErrorWarning": "Ada masalah dalam mendapatkan saldo token Anda untuk transaksi ini. <PERSON><PERSON><PERSON> mungkin gagal.", "blocklistOriginCommunityDatabaseInterpolated": "Situs ini telah ditandai sebagai bagian dari <1>basis data yang dikelola komunitas</1> yang berisi situs phishing dan penipuan. <PERSON><PERSON> menurut Anda penandaan situs ini adalah suatu kesalahan, <3>silakan ajukan laporan</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} diblokir!", "blocklistOriginIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON> peringatan ini, tetap bawa saya ke {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom menganggap situs ini berbahaya dan tidak aman digunakan.", "blocklistOriginThisDomain": "domain ini", "blocklistProceedAnyway": "<PERSON><PERSON><PERSON><PERSON>, tetap lan<PERSON>", "maliciousTransactionWarning": "Phantom menganggap transaksi ini berbahaya dan tidak aman untuk ditandatangani. Kami telah menonaktifkan tanda tangan untuknya demi melindungi Anda dan dana Anda.", "maliciousTransactionWarningIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON>, tetap lan<PERSON>", "maliciousTransactionWarningTitle": "Transaksi di<PERSON>dai!", "maliciousRequestBlockedTitle": "<PERSON>min<PERSON><PERSON>", "maliciousRequestWarning": "Situs web ini ditandai sebagai berb<PERSON>, karena mungkin mencoba mencuri dana <PERSON>a atau mengelabui Anda agar mengonfirmasi permintaan yang menipu.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON>, <PERSON> telah memblokir permintaan ini.", "maliciousRequestBlocked": "<PERSON><PERSON>, Phantom telah memblokir permintaan ini.", "maliciousRequestFrictionDescription": "Melan<PERSON><PERSON><PERSON> tida<PERSON> aman, jadi <PERSON> memblokir permintaan ini. Tutuplah dialog ini.", "maliciousRequestAcknowledge": "<PERSON>a memahami bahwa saya bisa kehilangan seluruh dana dengan menggunakan situs web ini.", "maliciousRequestAreYouSure": "Kamu yakin?", "siwErrorPopupTitle": "<PERSON><PERSON><PERSON><PERSON>", "siwParseErrorDescription": "Permintaan tanda tangan apli tidak dapat ditampilkan karena format yang tidak valid.", "siwVerificationErrorDescription": "Ada satu kesalahan atau lebih terkait permintaan tanda tangan pesan. <PERSON><PERSON>, pastikan <PERSON>a menggunakan aplikasi yang benar dan coba lagi.", "siwErrorPagination": "{{n}} dari {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Peringatan: <PERSON><PERSON><PERSON> aplikasi tidak sama dengan alamat yang diberikan untuk penandatanganan.", "siwErrorMessage_DOMAIN_MISMATCH": "Peringatan: Domain aplikasi tidak sama dengan domain yang diberikan untuk verifikasi.", "siwErrorMessage_URI_MISMATCH": "Peringatan: Nama host URI tidak cocok dengan domain.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Peringatan: ID rantai tidak sama dengan ID rantai yang diberikan untuk verifikasi.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Peringatan: <PERSON><PERSON> penerbitan pesan terlalu jauh di masa lalu.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Peringatan: <PERSON><PERSON> penerbitan pesan terlalu jauh di masa depan.", "siwErrorMessage_EXPIRED": "Peringatan: <PERSON><PERSON> telah ked<PERSON>.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Peringatan: <PERSON><PERSON> sebelum penerbitan.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Peringatan: <PERSON><PERSON> akan kedal<PERSON>sa sebelum menjadi sah.", "siwErrorShowErrorDetails": "<PERSON><PERSON><PERSON><PERSON> <PERSON> kesalahan", "siwErrorHideErrorDetails": "Sembunyikan detail kesalahan", "siwErrorIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON>, tetap lan<PERSON>", "siwsTitle": "<PERSON><PERSON><PERSON><PERSON>", "siwsPermissions": "<PERSON><PERSON>", "siwsAgreement": "<PERSON><PERSON>", "siwsAdvancedDetails": "Detail Lanjutan", "siwsAlternateStatement": "{{domain}} ingin <PERSON>a masuk dengan akun <PERSON>: {{address}}", "siwsFieldLable_domain": "Domain", "siwsFieldLable_address": "<PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "<PERSON><PERSON><PERSON>", "siwsFieldLable_chainId": "Chain ID", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "Diterbitkan Pada", "siwsFieldLable_expirationTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "siwsFieldLable_requestId": "ID Permintaan", "siwsFieldLable_resources": "<PERSON><PERSON> <PERSON>", "siwsVerificationErrorDescription": "Permintaan masuk ini tidak sah. Ini berarti situs tersebut tidak aman, atau pengembangnya membuat kesalahan saat mengirim permintaan.", "siwsErrorNumIssues": "{{n}} masalah", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Chain ID ini tidak cocok dengan jaringan tempat Anda berada.", "siwsErrorMessage_DOMAIN_MISMATCH": "Bukan domain ini yang Anda coba masuki.", "siwsErrorMessage_URI_MISMATCH": "Bukan URI ini yang Anda coba masuki.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Tanggal penerbitan pesan terlalu jauh di masa lalu.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Tanggal penerbitan pesan terlalu jauh di masa depan.", "siwsErrorMessage_EXPIRED": "<PERSON><PERSON> telah kedal<PERSON>.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "<PERSON><PERSON> kedaluwarsa sebelum penerbitan.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "<PERSON><PERSON> akan kedalu<PERSON>sa sebelum menjadi sah.", "changeLockTimerPrimaryText": "<PERSON><PERSON><PERSON>", "changeLockTimerSecondaryText": "<PERSON><PERSON><PERSON> lama kami harus menunggu untuk mengunci dompet Anda setelah tidak digunakan?", "changeLockTimerToast": "<PERSON><PERSON><PERSON> waktu kunci otomatis diper<PERSON>ui", "changePasswordConfirmNewPassword": "Konfirmasi kata sandi baru", "changePasswordCurrentPassword": "Kata sandi saat ini", "changePasswordErrorIncorrectCurrentPassword": "Kata sandi saat ini salah", "changePasswordErrorGeneric": "<PERSON> k<PERSON>, harap coba lagi nanti", "changePasswordNewPassword": "Kata sandi baru", "changePasswordPrimaryText": "Ubah kata sandi", "changePasswordToast": "<PERSON>a sandi diper<PERSON>ui", "collectionsSpamCollections": "<PERSON><PERSON><PERSON><PERSON>", "collectionsHiddenCollections": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesReportAsSpam": "Laporkan Spam", "collectiblesReportAsSpamAndHide": "Laporkan Spam dan <PERSON>", "collectiblesReportAsNotSpam": "Laporkan Bukan Spam", "collectiblesReportAsNotSpamAndUnhide": "Batal sembunyikan dan laporkan sebagai bukan spam", "collectiblesReportNotSpam": "<PERSON><PERSON><PERSON>", "collectionsManageCollectibles": "<PERSON><PERSON><PERSON> daftar collectible", "collectibleDetailDescription": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailProperties": "Properti", "collectibleDetailOrdinalInfo": "Info Ordinal", "collectibleDetailRareSatsInfo": "Info Sat Langka", "collectibleDetailSatsInUtxo": "Sat dalam UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sat", "collectibleDetailSatNumber": "<PERSON>mor <PERSON>", "collectibleDetailSatName": "<PERSON><PERSON>", "collectibleDetailInscriptionId": "ID Inskripsi", "collectibleDetailInscriptionNumber": "<PERSON><PERSON>", "collectibleDetailStandard": "<PERSON>ar", "collectibleDetailCreated": "Dibuat", "collectibleDetailViewOnExplorer": "<PERSON><PERSON> di {{explorer}}", "collectibleDetailList": "Cantumkan", "collectibleDetailSellNow": "<PERSON><PERSON> se<PERSON>ga {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Bebaskan Bitcoin tak terpakai", "collectibleDetailUtxoSplitterCtaSubtitle": "<PERSON><PERSON> memiliki {{value}} BTC untuk dibuka", "collectibleDetailUtxoSplitterModalCtaTitle": "Sat Langka", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Untuk melindungi dana <PERSON>, kami mence<PERSON>h pengiriman BTC dalam UTXO dengan Sat Langka. Gunakan pembagi UTXO Magic Eden untuk membebaskan {{value}} BTC dari Sat Langka Anda.", "collectibleDetailUtxoSplitterModalCtaButton": "Gunakan Pembagi UTXO", "collectibleDetailEasilyAccept": "<PERSON><PERSON> tawaran tertinggi", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sat", "collectibleDetailSpamOverlayDescription": "Collectible ini disembunyikan karena Phantom meyakini ini spam.", "collectibleDetailSpamOverlayReveal": "Tampilkan Collectible", "collectibleBurnTermsOfService": "<PERSON>a mengerti ini tidak dapat diurungkan", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON>", "collectibleBurnTitleWithCount_other": "<PERSON><PERSON>", "collectibleBurnDescriptionWithCount_one": "Tindakan ini akan menghancurkan dan menghapus token ini secara permanen dari dompet Anda.", "collectibleBurnDescriptionWithCount_other": "Tindakan ini akan memusnahkan dan menghapus token-token ini secara permanen dari dompet Anda.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Token", "collectibleBurnCta": "<PERSON><PERSON>", "collectibleBurnRebate": "Potongan", "collectibleBurnRebateTooltip": "Sejumlah kecil SOL akan otomatis disetorkan ke dompet Anda untuk membakar token ini.", "collectibleBurnNetworkFee": "<PERSON><PERSON><PERSON>", "collectibleBurnNetworkFeeTooltip": "<PERSON><PERSON><PERSON> yang dip<PERSON><PERSON>an oleh Solana Network untuk memproses transaksi", "unwrapButtonSwapTo": "<PERSON><PERSON> ke {{chainSymbol}}", "unwrapButtonWithdrawFrom": "<PERSON><PERSON> dari {{withdrawalSource}} se<PERSON>ga {{chainSymbol}}", "unwrapModalEstimatedTime": "Estimasi <PERSON>", "unwrapModalNetwork": "<PERSON><PERSON><PERSON>", "unwrapModalNetworkFee": "<PERSON><PERSON><PERSON>", "unwrapModalTitle": "<PERSON><PERSON><PERSON><PERSON>", "unsupportedChain": "Chain Tidak Didukung", "unsupportedChainDescription": "Tampaknya kami tidak mendukung {{action}} untuk jaringan {{chainName}}.", "networkFeesTooltipLabel": "<PERSON><PERSON><PERSON> {{chainName}}", "networkFeesTooltipDescription": "Biaya {{chainName}} berva<PERSON>si berdasarkan beberapa faktor. <PERSON>a dapat menyesuaikannya agar transaksi Anda lebih cepat (lebih mahal) atau lebih lambat (lebih murah).", "burnStatusErrorTitleWithCount_one": "Token gagal dibakar", "burnStatusErrorTitleWithCount_other": "Token gagal dibakar", "burnStatusSuccessTitleWithCount_one": "Token dibakar!", "burnStatusSuccessTitleWithCount_other": "Token dibakar!", "burnStatusLoadingTitleWithCount_one": "<PERSON><PERSON><PERSON> token...", "burnStatusLoadingTitleWithCount_other": "<PERSON><PERSON><PERSON> token...", "burnStatusErrorMessageWithCount_one": "Token ini tidak dapat dibakar. Mohon coba lagi nanti.", "burnStatusErrorMessageWithCount_other": "Token ini tidak dapat dibakar. Mohon coba lagi nanti.", "burnStatusSuccessMessageWithCount_one": "Token ini telah dimusnahkan secara permanen dan {{rebateAmount}} SOL telah disetorkan ke dompet Anda.", "burnStatusSuccessMessageWithCount_other": "Token-token ini telah dimusnahkan secara permanen dan {{rebateAmount}} SOL telah disetorkan ke dompet Anda.", "burnStatusLoadingMessageWithCount_one": "Token ini sedang dimusnahkan secara permanen dan {{rebateAmount}} SOL akan disetorkan ke dompet Anda.", "burnStatusLoadingMessageWithCount_other": "Token-token ini sedang dimusnahkan secara permanen dan {{rebateAmount}} SOL akan disetorkan ke dompet Anda.", "burnStatusViewTransactionText": "<PERSON><PERSON>", "collectibleDisplayLoading": "Memuat...", "collectiblesNoCollectibles": "Tidak ada Collectible", "collectiblesPrimaryText": "Collectible Anda", "collectiblesReceiveCollectible": "Terima Collectible", "collectiblesUnknownCollection": "Koleksi Tidak Diketahui", "collectiblesUnknownCollectible": "Collectible Tidak Diketahui", "collectiblesUniqueHolders": "Pemegang Unik", "collectiblesSupply": "<PERSON><PERSON><PERSON>", "collectiblesUnknownTokens": "Token Tidak Diketahui", "collectiblesNrOfListed": "{{ nrOfListed }} Dicantumkan", "collectiblesListed": "Dicantumkan", "collectiblesMintCollectible": "Mint Koleksi", "collectiblesYouMint": "<PERSON><PERSON> Minting", "collectiblesMintCost": "Biaya mint", "collectiblesMintFail": "Minting gagal", "collectiblesMintFailMessage": "Ada masalah saat melakukan minting koleksi Anda. Mohon coba lagi.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "Proses minting...", "collectiblesMintingMessage": "Koleksi Anda sedang dalam proses minting", "collectiblesMintShareSubject": "Lihat ini", "collectiblesMintShareMessage": "<PERSON><PERSON> te<PERSON> minting ini di @phantom!", "collectiblesMintSuccess": "Minting berhasil", "collectiblesMintSuccessMessage": "Proses minting koleksi Anda telah se<PERSON>ai", "collectiblesMintSuccessQuestMessage": "Anda telah memenuhi persyaratan untuk Misi Phantom. <PERSON><PERSON><PERSON> hadiah Anda untuk mendapatkan koleksi gratis.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMintMaxLengthErrorMessage": "Panjang maks. terlampaui", "collectiblesMintSafelyDismiss": "<PERSON>a dapat menutup jendela ini dengan aman.", "collectiblesTrimmed": "<PERSON>mi telah mencapai batas jumlah collectible yang dapat ditampilkan saat ini.", "collectiblesNonTransferable": "Tidak Dapat Ditransfer", "collectiblesNonTransferableYes": "Ya", "collectiblesSellOfferDetails": "<PERSON><PERSON>", "collectiblesSellYouSell": "<PERSON><PERSON>", "collectiblesSellGotIt": "<PERSON><PERSON><PERSON>", "collectiblesSellYouReceive": "<PERSON><PERSON>", "collectiblesSellOffer": "<PERSON><PERSON><PERSON>", "collectiblesSoldCollectible": "Telah Menjual Collectible", "collectiblesSellMarketplace": "Marketplace", "collectiblesSellCollectionFloor": "<PERSON><PERSON>", "collectiblesSellDifferenceFromFloor": "<PERSON><PERSON><PERSON> dengan dasar", "collectiblesSellLastSalePrice": "Penjualan Terakhir", "collectiblesSellEstimatedFees": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSellEstimatedProfitAndLoss": "Per<PERSON>raan Profit/Kerugian", "collectiblesSellViewOnMarketplace": "<PERSON><PERSON> di {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "<PERSON>rga \"<PERSON><PERSON>\" terendah dalam koleksi di berbagai marketplace.", "collectiblesSellProfitLossTooltip": "Estimasi Profit/Kerugian dihitung berdasarkan harga jual terakhir dan jumlah tawaran dikurangi biaya.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Royalti ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Biaya Marketplace ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Biaya Marketplace", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "<PERSON><PERSON><PERSON> {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Pengajuan harga sudah termasuk biaya Phantom {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "<PERSON><PERSON><PERSON><PERSON> harga sudah term<PERSON><PERSON>, <PERSON><PERSON><PERSON>, Biaya Marketplace, dan biaya <PERSON> {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "<PERSON><PERSON><PERSON><PERSON> harga sudah <PERSON>, <PERSON><PERSON><PERSON>, dan <PERSON><PERSON> Marketplace", "collectiblesSellTransactionFeeTooltipTitle": "Biaya Transaksi", "collectiblesSellStatusLoadingTitle": "<PERSON><PERSON><PERSON>...", "collectiblesSellStatusLoadingIsSellingFor": "sedang dijual seharga", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} Terjual!", "collectiblesSellStatusSuccessWasSold": "<PERSON><PERSON><PERSON><PERSON> di<PERSON><PERSON> se<PERSON>ga", "collectiblesSellStatusErrorTitle": "<PERSON>", "collectiblesSellStatusErrorSubtitle": "Ada masalah saat mencoba menjual", "collectiblesSellStatusViewTransaction": "Li<PERSON>", "collectiblesSellInsufficientFundsTitle": "<PERSON> tidak cukup", "collectiblesSellInsufficientFundsSubtitle": "<PERSON>mi tidak dapat menerima tawaran atas koleksi ini karena dana tidak cukup untuk membayar biaya jaringan.", "collectiblesSellRecentlyTransferedNFTTitle": "Ditransfer baru-baru ini", "collectiblesSellRecentlyTransferedNFTSubtitle": "<PERSON>a ha<PERSON> menunggu 1 jam untuk menerima tawaran setelah transfer.", "collectiblesApproveCollection": "Telah menyetujui {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "<PERSON><PERSON><PERSON> tidak tersedia", "collectiblesSellNotAvailableAnymoreSubtitle": "<PERSON><PERSON>n ini tidak tersedia lagi. <PERSON><PERSON><PERSON> tawaran ini dan coba lagi.", "collectiblesSellFlaggedTokenTitle": "Barang koleksi ditandai", "collectiblesSellFlaggedTokenSubtitle": "Barang koleksi ini tidak dapat diperdagangkan, bisa jadi karena berbagai alasan seperti dilaporkan sebagai barang curian atau staking tanpa penguncian", "collectiblesListOnMagicEden": "Cantumkan di Magic Eden", "collectiblesListPrice": "<PERSON><PERSON>", "collectiblesUseFloor": "<PERSON><PERSON><PERSON>", "collectiblesFloorPrice": "<PERSON><PERSON>", "collectiblesLastSalePrice": "<PERSON><PERSON> <PERSON>", "collectiblesTotalReturn": "Total Pengembalian", "collectiblesOriginalPurchasePrice": "<PERSON><PERSON>", "collectiblesMagicEdenFee": "Biaya Magic Eden", "collectiblesArtistRoyalties": "Royalti Artis", "collectiblesListNowButton": "Cantumkan Skrg", "collectiblesListAnywayButton": "Tetap <PERSON>", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "<PERSON><PERSON>", "collectiblesListingViewTransaction": "Li<PERSON>", "collectiblesRemoveListing": "<PERSON><PERSON>", "collectiblesEditListing": "<PERSON>", "collectiblesEditListPrice": "<PERSON>", "collectiblesListPriceTooltip": "<PERSON><PERSON> <PERSON>al adalah harga jual untuk sebuah item. <PERSON><PERSON><PERSON> biasanya memasang harga jual sama dengan atau di atas Harga <PERSON>.", "collectiblesFloorPriceTooltip": "<PERSON><PERSON> <PERSON><PERSON> ad<PERSON>h <PERSON>rga <PERSON>al aktif terendah untuk sebuah item dalam koleksi ini.", "collectiblesOriginalPurchasePriceTooltip": "Anda awalnya membeli item ini seharga angka ini.", "collectiblesPurchasedForSol": "<PERSON><PERSON><PERSON>ga {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Tidak dapat memuat cantuman", "collectiblesUnableToLoadListingsFrom": "Tidak dapat memuat cantuman dari {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "<PERSON><PERSON><PERSON> dan aset <PERSON>, tetapi kami tidak dapat memuatnya dari {{marketplace}} untuk saat ini. Mohon coba lagi nanti.", "collectiblesBelowFloorPrice": "<PERSON> <PERSON><PERSON><PERSON>", "collectiblesBelowFloorPriceMessage": "Yakin ingin mencantumkan NFT Anda di bawah harga dasar?", "collectiblesMinimumListingPrice": "Harga minimum adalah 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden memungut biaya untuk transaksi yang selesai.", "collectiblesArtistRoyaltiesTooltip": "Kreator koleksi ini menerima % royalti dari setiap penjualan yang selesai.", "collectibleScreenCollectionLabel": "<PERSON><PERSON><PERSON><PERSON>", "collectibleScreenPhotosPermissionTitle": "<PERSON><PERSON>", "collectibleScreenPhotosPermissionMessage": "<PERSON><PERSON> izin Anda untuk mengakses foto Anda. <PERSON><PERSON> Pengaturan dan perbarui izin Anda.", "collectibleScreenPhotosPermissionOpenSettings": "<PERSON><PERSON>", "listStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "editListStatusErrorTitle": "Tidak dapat me<PERSON>", "removeListStatusErrorTitle": "Penghapusan <PERSON>", "listStatusSuccessTitle": "<PERSON><PERSON><PERSON>!", "editListingStatusSuccessTitle": "<PERSON><PERSON><PERSON>!", "removeListStatusSuccessTitle": "<PERSON><PERSON><PERSON> di<PERSON>pus dari <PERSON>", "listStatusLoadingTitle": "<PERSON><PERSON><PERSON><PERSON>...", "editListingStatusLoadingTitle": "<PERSON><PERSON><PERSON><PERSON>...", "removeListStatusLoadingTitle": "<PERSON><PERSON><PERSON><PERSON>...", "listStatusErrorMessage": "{{name}} tidak dapat dicantumkan di Magic Eden", "removeListStatusErrorMessage": "{{name}} tidak dapat dibatalkan cantumannya di Magic Eden", "listStatusSuccessMessage": "{{name}} sekarang dicantumkan di Magic Eden seharga {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "{{name}} sekarang diperbarui di Magic Eden seharga {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} ber<PERSON><PERSON> dihapus dari Magic Eden", "listStatusLoadingMessage": "Mencantumkan {{name}} di Magic Eden seharga {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Memperbarui {{name}} di Magic <PERSON> seharga {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Menghapus {{name}} dari <PERSON> Eden. Proses ini perlu waktu.", "listStatusLoadingSafelyDismiss": "<PERSON>a dapat menutup jendela ini.", "listStatusViewOnMagicEden": "<PERSON><PERSON> di Magic Eden", "listStatusViewOnMarketplace": "<PERSON><PERSON> di {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON><PERSON><PERSON>", "listStatusViewTransaction": "Li<PERSON>", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Hubungkan dompet perangkat keras Anda dan pastikan tidak terkunci. <PERSON><PERSON><PERSON> kami <PERSON>, <PERSON><PERSON> dapat memilih alamat yang ingin Anda gunakan.", "connectHardwareFailedPrimaryText": "<PERSON><PERSON><PERSON><PERSON> gagal", "connectHardwareFailedSecondaryText": "Hubungkan dompet perangkat keras Anda dan pastikan tidak terkunci. <PERSON><PERSON><PERSON> kami <PERSON>, <PERSON><PERSON> dapat memilih alamat yang akan digunakan.", "connectHardwareFinishPrimaryText": "Akun <PERSON>!", "connectHardwareFinishSecondaryText": "<PERSON>a kini dapat mengakses dompet Ledger Nano dari dalam Phantom. <PERSON><PERSON><PERSON> kembali ke ekstensi.", "connectHardwareNeedsPermissionPrimaryText": "Hubungkan dompet baru", "connectHardwareNeedsPermissionSecondaryText": "<PERSON><PERSON> tombol di bawah untuk memulai proses pengh<PERSON>.", "connectHardwareSearchingPrimaryText": "<PERSON><PERSON><PERSON> dompet...", "connectHardwareSearchingSecondaryText": "<PERSON><PERSON> hubungkan dompet perangkat keras <PERSON>, pastikan tidak terk<PERSON>, dan setujui izin akses pada peramban <PERSON>.", "connectHardwarePermissionDeniedPrimary": "<PERSON><PERSON>", "connectHardwarePermissionDeniedSecondary": "Beri Phantom izin untuk terhubung dengan perangkat Ledger Anda", "connectHardwarePermissionUnableToConnect": "Tidak dapat terhubung", "connectHardwarePermissionUnableToConnectDescription": "Kami tidak dapat terhubung ke perangkat Ledger Anda. Kami mungkin memerlukan lebih banyak izin.", "connectHardwareSelectAddressAllAddressesImported": "<PERSON><PERSON><PERSON> alamat telah diimpor", "connectHardwareSelectAddressDerivationPath": "Path derivasi", "connectHardwareSelectAddressSearching": "<PERSON><PERSON><PERSON>...", "connectHardwareSelectAddressSelectWalletAddress": "<PERSON><PERSON><PERSON> do<PERSON>", "connectHardwareSelectAddressWalletAddress": "<PERSON><PERSON><PERSON>", "connectHardwareWaitingForApplicationSecondaryText": "Hubungkan dompet perangkat keras Anda dan pastikan dompet tidak terkunci.", "connectHardwareWaitingForPermissionPrimaryText": "<PERSON><PERSON> i<PERSON>", "connectHardwareWaitingForPermissionSecondaryText": "Hubungkan dompet perangkat keras <PERSON>, pastikan dompet tidak terkun<PERSON>, dan <PERSON>a telah menyetujui izin di browser And<PERSON>.", "connectHardwareAddAccountButton": "Tambahkan Akun", "connectHardwareLedger": "Hubungkan Ledger Anda", "connectHardwareStartConnection": "Klik tombol di bawah untuk mulai menghubungkan dompet perangkat keras Ledger Anda", "connectHardwarePairSuccessPrimary": "{{productName}} terhubung", "connectHardwarePairSuccessSecondary": "<PERSON><PERSON> ber<PERSON><PERSON> mengh<PERSON> {{productName}}.", "connectHardwareSelectChains": "<PERSON><PERSON><PERSON> rantai untuk menghubungkan", "connectHardwareSearching": "<PERSON><PERSON><PERSON>...", "connectHardwareMakeSureConnected": "Hubungkan dan buka kunci dompet perangkat keras Anda. Setujui izin browser yang relevan.", "connectHardwareOpenAppDescription": "<PERSON>uka kunci dompet perangkat keras Anda", "connectHardwareConnecting": "Menghubungkan...", "connectHardwareConnectingDescription": "<PERSON><PERSON>g mencoba terhubung dengan perangkat Ledger Anda.", "connectHardwareConnectingAccounts": "Menghubungkan akun <PERSON>a...", "connectHardwareDiscoveringAccounts": "<PERSON><PERSON>i akun...", "connectHardwareDiscoveringAccountsDescription": "<PERSON><PERSON>g mencari aktivitas di akun Anda.", "connectHardwareErrorLedgerLocked": "Ledger terkunci", "connectHardwareErrorLedgerLockedDescription": "Pastikan perangkat Ledger <PERSON>a te<PERSON>, lalu coba lagi.", "connectHardwareErrorLedgerGeneric": "<PERSON> yang salah", "connectHardwareErrorLedgerGenericDescription": "<PERSON>l menemukan akun. Pastikan perangkat Ledger <PERSON>a telah di<PERSON>, lalu coba lagi.", "connectHardwareErrorLedgerPhantomLocked": "<PERSON><PERSON> ulang Phantom dan coba hubungkan kembali perangkat keras Anda.", "connectHardwareFindingAccountsWithActivity": "<PERSON><PERSON>i akun {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "<PERSON><PERSON><PERSON> akun {{chainName1}} atau {{chainName2}} ...", "connectHardwareFoundAccountsWithActivity": "<PERSON><PERSON> {{numOfAccounts}} akun berakti<PERSON>tas pada Ledger <PERSON>.", "connectHardwareFoundAccountsWithActivitySingular": "Kami menemukan 1 akun beraktivitas pada Ledger Anda.", "connectHardwareFoundSomeAccounts": "<PERSON><PERSON> men<PERSON>ukan beberapa akun pada perangkat Ledger Anda.", "connectHardwareViewAccounts": "<PERSON><PERSON>", "connectHardwareConnectAccounts": "<PERSON>kun ter<PERSON><PERSON>", "connectHardwareSelectAccounts": "<PERSON><PERSON><PERSON>", "connectHardwareChooseAccountsToConnect": "<PERSON><PERSON>h akun dompet untuk menghubungkan.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} <PERSON><PERSON><PERSON> di<PERSON>", "connectHardwareAccountsStepOfSteps": "<PERSON><PERSON><PERSON> {{stepNum}} dari {{totalSteps}}", "connectHardwareMobile": "<PERSON><PERSON><PERSON><PERSON> Ledger", "connectHardwareMobileTitle": "Hubungkan dompet perangkat keras Ledger Anda", "connectHardwareMobileEnableBluetooth": "Aktifkan Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Izinkan penggunaan Bluetooth untuk terhubung", "connectHardwareMobileEnableBluetoothSettings": "Buka Pengaturan agar Phantom dapat menggunakan izin Lokasi dan Perangkat di Sekitar.", "connectHardwareMobilePairWithDevice": "Pasangkan dengan perangkat Ledger Anda", "connectHardwareMobilePairWithDeviceDescription": "Letakkan perangkat di dekat Anda untuk mendapatkan sinyal terbaik", "connectHardwareMobileConnectAccounts": "Hubungkan akun", "connectHardwareMobileConnectAccountsDescription": "<PERSON><PERSON> akan mencari aktivitas di setiap akun yang mungkin telah <PERSON>a gunakan", "connectHardwareMobileConnectLedgerDevice": "Hubungkan perangkat Ledger Anda", "connectHardwareMobileLookingForDevices": "<PERSON><PERSON>i perangkat di sekitar...", "connectHardwareMobileLookingForDevicesDescription": "Hubungkan perangkat Ledger Anda dan pastikan tidak terkunci.", "connectHardwareMobileFoundDeviceSingular": "<PERSON><PERSON> 1 perangkat Ledger", "connectHardwareMobileFoundDevices": "<PERSON><PERSON> {{numDevicesFound}} per<PERSON><PERSON> Ledger", "connectHardwareMobileFoundDevicesDescription": "<PERSON><PERSON><PERSON> per<PERSON> Ledger di bawah untuk mulai memasangkan.", "connectHardwareMobilePairingWith": "Memasang<PERSON> dengan {{deviceName}}", "connectHardwareMobilePairingWithDescription": "<PERSON><PERSON><PERSON> instruksi pada perangkat Ledger Anda saat memasangkan.", "connectHardwareMobilePairingFailed": "<PERSON><PERSON><PERSON><PERSON> tidak ber<PERSON>", "connectHardwareMobilePairingFailedDescription": "Tidak dapat memasangkan dengan {{deviceName}}. Pastikan perangkat Anda tidak terkunci.", "connectHardwareMobilePairingSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobilePairingSuccessfulDescription": "<PERSON><PERSON> be<PERSON><PERSON><PERSON> memasangkan dan menghubungkan perangkat Ledger.", "connectHardwareMobileOpenAppSingleChain": "<PERSON><PERSON> aplikasi {{chainName}} di Led<PERSON>", "connectHardwareMobileOpenAppDualChain": "<PERSON><PERSON> aplikasi {{chainName1}} atau {{chainName2}} di Led<PERSON>a", "connectHardwareMobileOpenAppDescription": "Pastikan perangkat Anda tidak terkunci.", "connectHardwareMobileStillCantFindDevice": "Ma<PERSON>h belum menemukan perang<PERSON>?", "connectHardwareMobileLostConnection": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileLostConnectionDescription": "<PERSON><PERSON> kehilangan koneksi dengan {{deviceName}}. Pastikan perangkat Anda tidak terkunci, lalu coba lagi.", "connectHardwareMobileGenericLedgerDevice": "<PERSON><PERSON><PERSON> Ledger", "connectHardwareMobileConnectDeviceSigning": "Hubungkan {{deviceName}} And<PERSON>", "connectHardwareMobileConnectDeviceSigningDescription": "<PERSON><PERSON> perangkat Ledger dan letakkan di dekat <PERSON>.", "connectHardwareMobileBluetoothDisabled": "Bluetooth dinonaktifkan", "connectHardwareMobileBluetoothDisabledDescription": "Aktifkan Bluetooth Anda dan pastikan perangkat Ledger Anda tidak terkunci.", "connectHardwareMobileLearnMore": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileBlindSigningDisabled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileBlindSigningDisabledDescription": "<PERSON><PERSON><PERSON> pen<PERSON>an buta diaktifkan pada perangkat <PERSON>a.", "connectHardwareMobileConfirmSingleChain": "<PERSON><PERSON> harus mengonfirmasi transaksi di dompet perangkat keras Anda. Pastikan dompet tidak terkunci.", "metamaskExplainerBottomSheetHeader": "Situs ini dapat bekerja dengan <PERSON>", "metamaskExplainerBottomSheetSubheader": "<PERSON><PERSON>h MetaMask dari dialog \"hubungkan dompet\" untuk melanjutkan.", "metamaskExplainerBottomSheetDontShowAgain": "<PERSON>an tampilkan lagi", "ledgerStatusNotConnected": "Ledger tidak terhubung", "ledgerStatusConnectedInterpolated": "{{productName}} terhubung", "connectionClusterInterpolated": "Anda saat ini berada di {{cluster}}", "connectionClusterTestnetMode": "<PERSON><PERSON> sedang dalam Mode Testnet", "featureNotSupportedOnLocalNet": "Fitur ini tidak didukung saat Localnet Solana diaktifkan.", "readOnlyAccountBannerWarning": "Anda memantau akun ini", "depositAddress": "<PERSON><PERSON><PERSON>", "depositAddressChainInterpolated": "<PERSON><PERSON><PERSON> {{chain}} And<PERSON>", "depositAssetDepositInterpolated": "Terima {{tokenSymbol}}", "depositAssetSecondaryText": "<PERSON><PERSON>t ini hanya dapat digunakan untuk menerima token yang kompatibel.", "depositAssetTextInterpolated": "<PERSON><PERSON><PERSON> alamat ini untuk menerima token dan koleksi di <1>{{network}}</1> .", "depositAssetTransferFromExchange": "Transfer dari bursa", "depositAssetShareAddress": "Bagikan alamat", "depositAssetBuyOrDeposit": "Beli atau Transfer", "depositAssetBuyOrDepositDesc": "<PERSON><PERSON>kkan dana ke dompet Anda untuk memulai", "depositAssetTransfer": "Transfer", "editAddressAddressAlreadyAdded": "<PERSON><PERSON><PERSON> telah ditam<PERSON>", "editAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON> sudah ada", "editAddressAddressIsRequired": "<PERSON><PERSON><PERSON>", "editAddressPrimaryText": "<PERSON><PERSON> Alamat", "editAddressRemove": "<PERSON><PERSON> da<PERSON>", "editAddressToast": "<PERSON><PERSON><PERSON>", "removeSavedAddressToast": "<PERSON><PERSON><PERSON>", "exportSecretErrorGeneric": "<PERSON> k<PERSON>, harap coba lagi nanti", "exportSecretErrorIncorrectPassword": "Kata sandi salah", "exportSecretPassword": "<PERSON>a sandi", "exportSecretPrivateKey": "kunci privat", "exportSecretSecretPhrase": "frasa rahasia", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "frasa pemulihan rahasia", "exportSecretSelectYourAccount": "<PERSON><PERSON><PERSON> aku<PERSON>", "exportSecretShowPrivateKey": "<PERSON><PERSON><PERSON><PERSON> Kunci Privat", "exportSecretShowSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON> frasa pemulihan rahasia", "exportSecretShowSecret": "<PERSON><PERSON><PERSON><PERSON> {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "<1><PERSON><PERSON></1> bag<PERSON>n {{secretNameText}} Anda!", "exportSecretWarningSecondaryInterpolated": "<PERSON>ka seseorang memiliki {{secretNameText}} <PERSON><PERSON>, mereka bisa mengontrol dompet <PERSON>a se<PERSON>.", "exportSecretOnlyWay": "{{secretNameText}} Anda adalah cara satu-satunya untuk memulihkan dompet", "exportSecretDoNotShow": "<PERSON><PERSON> sampai orang lain melihat {{secretNameText}} Anda", "exportSecretWillNotShare": "Saya tidak akan membagikan {{secretNameText}} saya dengan siapa pun, termasuk <PERSON>.", "exportSecretNeverShare": "<PERSON><PERSON> pernah bagikan {{secretNameText}} Anda dengan siapa pun", "exportSecretYourPrivateKey": "Kunci Privat Anda", "exportSecretYourSecretRecoveryPhrase": "<PERSON>asa pemulihan rahasia <PERSON>", "exportSecretResetPin": "Reset PIN Anda", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "Bantuan", "gasUpTo": "Hingga {{ amount }}", "timeDescription1hour": "Sekitar 1 jam", "timeDescription30minutes": "Sekitar 30 menit", "timeDescription10minutes": "Sekitar 10 menit", "timeDescription2minutes": "Sekitar 2 menit", "timeDescription30seconds": "Sekitar 30 detik", "timeDescription15seconds": "Sekitar 15 detik", "timeDescription10seconds": "Sekitar 10 detik", "timeDescription5seconds": "Sekitar 5 detik", "timeDescriptionAbbrev1hour": "1 jam", "timeDescriptionAbbrev30minutes": "30 mnt", "timeDescriptionAbbrev10minutes": "10 mnt", "timeDescriptionAbbrev2minutes": "2 mnt", "timeDescriptionAbbrev30seconds": "30 dtk", "timeDescriptionAbbrev15seconds": "15 dtk", "timeDescriptionAbbrev10seconds": "10 dtk", "timeDescriptionAbbrev5seconds": "5 dtk", "gasSlow": "Lambat", "gasAverage": "<PERSON><PERSON><PERSON>", "gasFast": "Cepat", "satsPerVirtualByte": "{{satsPerVirtualByte}} sat/vB", "satsAmount": "{{sats}} sat", "homeErrorButtonText": "<PERSON><PERSON>", "homeErrorDescription": "<PERSON> kesalahan saat mencoba mengambil kembali aset <PERSON>. <PERSON><PERSON> refresh dan coba lagi.", "homeErrorTitle": "<PERSON><PERSON> menda<PERSON> aset", "homeManageTokenList": "Atur daftar token", "interstitialDismissUnderstood": "<PERSON><PERSON><PERSON>", "interstitialBaseWelcomeTitle": "Phantom kini mendukung Base!", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON>, te<PERSON>, dan beli <PERSON>", "interstitialBaseWelcomeItemTitle_2": "Jelajahi ekosistem Base", "interstitialBaseWelcomeItemTitle_3": "<PERSON>an dan ter<PERSON>min", "interstitialBaseWelcomeItemDescription_1": "Transfer dan beli USDC dan ETH di Base menggunakan {{paymentMethod}}, kartu, atau Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Gunakan Phantom dengan semua aplikasi DeFi dan NFT favorit And<PERSON>.", "interstitialBaseWelcomeItemDescription_3": "Tetap aman dengan du<PERSON>, <PERSON><PERSON><PERSON><PERSON> spam, dan simulasi trans<PERSON>i.", "privacyPolicyChangedInterpolated": "<PERSON><PERSON><PERSON><PERSON> kami telah be<PERSON>. <1><PERSON><PERSON><PERSON><PERSON></1>", "bitcoinAddressTypesBodyTitle": "<PERSON><PERSON>", "bitcoinAddressTypesFeature1Title": "Tentang alamat Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom mendukung Native Se<PERSON><PERSON><PERSON> dan <PERSON>, masing-masing dengan saldonya sendiri. <PERSON>a dapat mengirim BTC atau Ordinals dengan kedua jenis alamat.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Alamat BTC default di Phantom. Lebih lama daripada Taproot tetapi kompatibel dengan semua dompet dan bursa.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Terbaik untuk Ordinals dan BRC-20, dengan biaya termurah. Sesuaikan alamat di Preferensi -> Alamat Bitcoin Pilihan.", "headerTitleInfo": "Info", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Ini adalah alamat <1>{{addressType}}</1> And<PERSON>.", "invalidChecksumTitle": "Kami telah meningkatkan frasa rahasia <PERSON>a!", "invalidChecksumFeature1ExportPhrase": "Ekspor Frasa Rahasia baru Anda", "invalidChecksumFeature1ExportPhraseDescription": "Cadangkan frasa rahasia baru Anda bersama dengan kunci privat akun lama <PERSON>.", "invalidChecksumFeature2FundsAreSafe": "<PERSON> aman dan terjamin", "invalidChecksumFeature2FundsAreSafeDescription": "Peningkatan ini dilakukan secara otomatis. Tidak seorang pun di Phantom mengetahui frasa rahasia Anda atau memiliki akses ke dana Anda.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON><PERSON><PERSON>", "invalidChecksumFeature3LearnMoreDescription": "Anda memiliki frasa yang tidak kompatibel dengan sebagian besar dompet. Baca <1>artikel bantuan ini</1> untuk informasi selengkapnya.", "invalidChecksumBackUpSecretPhrase": "Cadangkan frasa rahasia", "migrationFailureTitle": "<PERSON><PERSON><PERSON><PERSON> masalah saat memigrasikan akun <PERSON>a", "migrationFailureFeature1": "Ekspor frasa rahasia Anda", "migrationFailureFeature1Description": "Cadangkan frasa rahasia Anda sebelum melakukan proses masuk.", "migrationFailureFeature2": "<PERSON><PERSON><PERSON> ke Phantom", "migrationFailureFeature2Description": "<PERSON>a harus masuk kembali ke Phantom untuk melihat akun <PERSON>a.", "migrationFailureFeature3": "Selengkapnya", "migrationFailureFeature3Description": "Baca <1>artikel bantuan ini</1> untuk keterangan selengkapnya tentang ini.", "migrationFailureContinueToOnboarding": "Lanjutkan ke proses masuk", "migrationFailureUnableToFetchMnemonic": "<PERSON>mi tidak dapat memuat frasa rahasia Anda", "migrationFailureUnableToFetchMnemonicDescription": "Hubungi dukungan dan unduh log aplikasi untuk melakukan debug", "migrationFailureContactSupport": "Hubungi <PERSON>", "ledgerActionConfirm": "Konfirmasikan pada Ledger <PERSON>", "ledgerActionErrorBlindSignDisabledPrimaryText": "<PERSON>da tangan buta dinonakt<PERSON><PERSON>", "ledgerActionErrorBlindSignDisabledSecondaryText": "Pastikan tanda tangan buta diaktifkan pada perangkat kerasmu, lalu coba lagi", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "<PERSON>neksi perangkat keras terputus da<PERSON>i", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Tutup ekstensi Phantom, lalu coba lagi", "ledgerActionErrorDeviceLockedPrimaryText": "Perangkat keras terkunci", "ledgerActionErrorDeviceLockedSecondaryText": "<PERSON>uka kunci perangkat keras <PERSON>, lalu coba lagi", "ledgerActionErrorHeader": "<PERSON><PERSON><PERSON>", "ledgerActionErrorUserRejectionPrimaryText": "Pengguna menolak transaksi", "ledgerActionErrorUserRejectionSecondaryText": "Tindakan ditolak pada perangkat keras oleh pengguna", "ledgerActionNeedPermission": "<PERSON><PERSON> aks<PERSON>", "ledgerActionNeedToConfirm": "<PERSON>a harus mengonfirmasi transaksi di dompet perangkat keras Anda. Pastikan dompet tidak terkunci, pada aplikasi {{chainType}}.", "ledgerActionNeedToConfirmMany": "<PERSON>a harus mengonfirmasi {{numberOfTransactions}} transaksi di dompet perangkat keras Anda. Pastikan dompet tidak terkunci, pada aplikasi {{chainType}}.", "ledgerActionNeedToConfirmBlind": "<PERSON>a harus mengonfirmasi transaksi di dompet perangkat keras Anda. Pastikan dompet tidak terkunci, pada aplikasi {{chainType}}, dan tanda tangan buta diaktifkan.", "ledgerActionNeedToConfirmBlindMany": "<PERSON>a harus mengonfirmasi {{numberOfTransactions}} trans<PERSON>i di dompet perangkat keras Anda. Pastikan dompet tidak terkunci, pada aplik<PERSON> {{chainType}}, dan tanda tangan buta diaktifkan.", "ledgerActionPleaseConnect": "<PERSON><PERSON> hubungkan Ledger Nan<PERSON>", "ledgerActionPleaseConnectAndConfirm": "Harap hubungkan dompet perangkat keras Anda dan pastikan dompet tidak terkunci. Pastikan Anda telah menyetujui izin di browser Anda.", "maxInputAmount": "<PERSON><PERSON><PERSON>", "maxInputMax": "<PERSON><PERSON>", "notEnoughSolPrimaryText": "SOL tidak cukup", "notEnoughSolSecondaryText": "SOL di dompet Anda tidak cukup untuk transaksi ini. <PERSON><PERSON><PERSON> setorkan lebih banyak dan coba lagi.", "insufficientBalancePrimaryText": "{{tokenSymbol}} tidak cukup", "insufficientBalanceSecondaryText": "{{tokenSymbol}} Anda di dompet tidak cukup untuk transaksi ini.", "insufficientBalanceRemaining": "<PERSON><PERSON><PERSON>", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "Token tidak cukup", "notEnoughSplTokensDescription": "Anda tidak memiliki token yang cukup di dompet untuk transaksi ini. Transaksi ini akan dibatalkan jika dilakukan.", "transactionExpiredPrimaryText": "Transaksi <PERSON>", "transactionExpiredSecondaryText": "<PERSON>a menunggu terlalu lama untuk mengonfirmasi transaksi dan sekarang transaksi kedaluwarsa. Transaksi ini akan dibatalkan jika dilakukan.", "transactionHasWarning": "Peringatan transaksi", "tokens": "token", "notificationApplicationApprovalPermissionsAddressVerification": "Verifi<PERSON><PERSON> bahwa Anda memiliki alamat ini", "notificationApplicationApprovalPermissionsTransactionApproval": "<PERSON><PERSON><PERSON> untuk <PERSON>i", "notificationApplicationApprovalPermissionsViewWalletActivity": "Melihat saldo dan aktivitas dompet", "notificationApplicationApprovalParagraphText": "Mengonfirmasi berarti mengizinkan situs ini untuk melihat saldo dan aktivitas akun terpilih.", "notificationApplicationApprovalActionButtonConnect": "Hubungkan", "notificationApplicationApprovalActionButtonSignIn": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "Izinkan situs untuk menghubungkan?", "notificationApplicationApprovalAutoConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationApplicationApprovalConnectDisclaimer": "<PERSON><PERSON> mengh<PERSON>kan ke situs yang tepercaya", "notificationApplicationApprovalSignInDisclaimer": "Hanya masuk ke situs web yang tepercaya", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Situs web ini tidak aman untuk digunakan dan mungkin mencoba mencuri dana <PERSON>.", "notificationApplicationApprovalConnectUnknownApp": "Tidak diketahui", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Tidak dapat terhubung ke aplikasi", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Aplikasi ini mencoba terhubung ke {{appNetworkName}}, tetapi {{phantomNetworkName}} dipilih.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Untuk menggunakan {{networkName}}, buka Pengaturan Pengembang → Mode Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Sambungan dengan perangkat seluler lain saat ini tidak didukung o<PERSON>h <PERSON>.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "<PERSON><PERSON> beralih ke akun non-Ledger atau gunakan peramban dalam aplikasi dan coba lagi.", "notificationSignatureRequestConfirmTransaction": "Konfirma<PERSON>", "notificationSignatureRequestConfirmTransactionCapitalized": "Konfirmasi <PERSON>", "notificationSignatureRequestConfirmTransactions": "Konfirma<PERSON>", "notificationSignatureRequestConfirmTransactionsCapitalized": "Konfirmasi <PERSON>", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON><PERSON><PERSON>", "notificationMessageHeader": "<PERSON><PERSON>", "notificationMessageCopied": "<PERSON><PERSON> tersalin", "notificationAutoConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationAutoConfirmOff": "<PERSON><PERSON><PERSON><PERSON>", "notificationAutoConfirmOn": "Aktif", "notificationConfirmFooter": "<PERSON><PERSON> konfirmasi jika Anda memercayai situs web ini.", "notificationEstimatedTime": "Estimasi <PERSON>", "notificationPermissionRequestText": "Ini hanyalah permintaan izin. Transaksi mungkin tidak langsung dieksekusi.", "notificationBalanceChangesText": "<PERSON>bahan saldo adalah per<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "notificationContractAddress": "<PERSON><PERSON><PERSON>", "notificationAdvancedDetailsText": "Lanjutan", "notificationUnableToSimulateWarningText": "Saat ini kami tidak dapat memperkirakan perubahan saldo. Anda dapat mencoba lagi nanti, atau mengonfirmasi jika Anda memercayai situs ini.", "notificationSignMessageParagraphText": "Menandatangani pesan ini akan membuktikan bahwa Anda memiliki kepemilikan atas akun yang dipilih.", "notificationSignatureRequestScanFailedDescription": "Tidak dapat memindai pesan untuk mencari masalah keamanan. Lanjutkan dengan hati-hati.", "notificationFailedToScan": "<PERSON><PERSON> men<PERSON>n hasil permintaan ini.\nMengonfirmasi tidaklah aman dan dapat menyebab<PERSON> kerugian.", "notificationScanLoading": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonBack": "Kembali", "notificationTransactionApprovalEstimatedChanges": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatesBasedOnSimulations": "<PERSON><PERSON><PERSON><PERSON> didasarkan pada simulasi transaksi dan bukan merupakan jaminan", "notificationTransactionApprovalHideAdvancedDetails": "Sembunyikan detail transaksi lan<PERSON>", "notificationTransactionApprovalNetworkFee": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalNetwork": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatedTime": "Estimasi waktu", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Tidak ditemukan perubahan yang berdampak pada kepemilikan aset", "notificationTransactionApprovalSolanaAmountRequired": "<PERSON><PERSON><PERSON> yang dip<PERSON><PERSON>an oleh Solana Network untuk memproses transaksi", "notificationTransactionApprovalUnableToSimulate": "Tidak dapat menyimulasi transaksi. Pastikan Anda percaya dengan situs web ini karena persetujuan dapat menyebabkan kehilangan dana.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Tidak dapat mengambil perubahan saldo", "notificationTransactionApprovalViewAdvancedDetails": "Lihat detail transaksi lan<PERSON>", "notificationTransactionApprovalKnownMalicious": "Transaksi ini berbahaya. Tanda tangan akan berakibat kehilangan dana.", "notificationTransactionApprovalSuspectedMalicious": "<PERSON><PERSON> menduga transaksi ini berbahaya. Persetujuan dapat mengakibatkan kehilangan dana.", "notificationTransactionApprovalNetworkFeeHighWarning": "<PERSON><PERSON><PERSON> jaringan bertambah karena kemacetan jaringan.", "notificationTransactionERC20ApprovalDescription": "Mengonfirmasi berarti mengizinkan aplikasi ini untuk mengakses saldo <PERSON>a kapan saja, hingga batas di bawah ini.", "notificationTransactionERC20ApprovalContractAddress": "<PERSON><PERSON><PERSON>", "notificationTransactionERC20Unlimited": "tak terbatas", "notificationTransactionERC20ApprovalTitle": "<PERSON><PERSON><PERSON><PERSON> belanja {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Batalkan belanja {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Batalkan akses {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Semu<PERSON> {{tokenSymbol}} Anda", "notificationIncorrectModeTitle": "Mode salah", "notificationIncorrectModeInTestnetTitle": "<PERSON>a da<PERSON> mode Testnet", "notificationIncorrectModeNotInTestnetTitle": "Anda tidak dalam mode Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} mencoba menggunakan mainnet, tetapi Anda dalam mode Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} mencoba menggunakan testnet, tetapi Anda tidak dalam mode Testnet", "notificationIncorrectModeInTestnetProceed": "Untuk melanjutkan, matikan mode Testnet.", "notificationIncorrectModeNotInTestnetProceed": "Untuk melanjutkan, nyalakan mode Testnet.", "notificationIncorrectEIP712ChainId": "<PERSON><PERSON> men<PERSON>h penanda<PERSON>ganan pesan yang tidak dimaksudkan untuk jaringan yang saat ini terhubung dengan Anda.", "notificationIncorrectEIP712ChainIdDescription": "<PERSON><PERSON> meminta {{messageChainId}}, <PERSON><PERSON> te<PERSON><PERSON><PERSON><PERSON> dengan {{connectedChainId}}", "notificationUnsupportedNetwork": "<PERSON><PERSON><PERSON> tidak didukung", "notificationUnsupportedNetworkDescription": "Situs web ini mencoba menggunakan jaringan yang belum didukung Phantom.", "notificationUnsupportedNetworkDescriptionInterpolated": "Untuk melanjutkan dengan ekstensi lain, mat<PERSON><PERSON> <1>Pengaturan → Dom<PERSON>, la<PERSON> p<PERSON>h <PERSON></1>. <PERSON><PERSON><PERSON><PERSON><PERSON>, segarkan halaman dan hubungkan kembali.", "notificationUnsupportedAccount": "<PERSON>kun tidak didukung", "notificationUnsupportedAccountDescription": "Situs web ini mencoba menggunakan {{targetChainType}}, yang tidak didukung oleh akun {{chainType}} ini.", "notificationUnsupportedAccountDescription2": "Be<PERSON><PERSON>lah ke akun dari frasa seed atau kunci privat yang kompatibel, lalu coba lagi.", "notificationInvalidTransaction": "Transaksi tidak sah", "notificationInvalidTransactionDescription": "Transaksi yang diterima dari aplikasi ini salah format dan tidak boleh dikirimkan. Hubungi pengembang aplikasi ini untuk melaporkan masalah ini kepada mereka.", "notificationCopyTransactionText": "<PERSON><PERSON>", "notificationTransactionCopied": "Transaksi tersalin", "onboardingImportOptionsPageTitle": "Impor do<PERSON>", "onboardingImportOptionsPageSubtitle": "Impor dompet yang ada dengan frasa rahasia, kunci privat, atau dompet perangkat keras Anda.", "onboardingImportPrivateKeyPageTitle": "Impor Kunci Privat", "onboardingImportPrivateKeyPageSubtitle": "Impor dompet rantai tunggal yang ada", "onboardingCreatePassword": "<PERSON>uat kata sandi", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> <1><PERSON><PERSON><PERSON><PERSON></1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "<PERSON>n<PERSON><PERSON><PERSON>", "onboardingCreatePasswordDescription": "<PERSON>a akan menggunakan ini untuk membuka dompet Anda.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Frasa pemulihan rahasia tidak valid", "onboardingCreatePasswordPasswordPlaceholder": "<PERSON>a sandi", "onboardingCreatePasswordPasswordStrengthWeak": "Lemah", "onboardingCreatePasswordPasswordStrengthMedium": "Sedang", "onboardingCreatePasswordPasswordStrengthStrong": "Ku<PERSON>", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Frasa ini adalah cara SATU-SATUNYA untuk memulihkan dompet Anda. JANGAN beri tahu siapa pun!", "onboardingImportWallet": "<PERSON><PERSON><PERSON>", "onboardingImportWalletImportExistingWallet": "Impor dompet yang sudah ada dengan frasa pemulihan rahasia yang terdiri dari 12 atau 24 kata.", "onboardingImportWalletRestoreWallet": "Pulihkan Dompet", "onboardingImportWalletSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Frasa Pemulihan <PERSON> tidak valid", "onboardingImportWalletIHaveWords": "<PERSON>a memiliki frasa pemulihan {{numWords}} kata", "onboardingImportWalletIncorrectOrMisspelledWord": "Kata {{wordIndex}} tidak tepat atau salah eja", "onboardingImportWalletIncorrectOrMisspelledWords": "Kata {{wordIndexes}} tidak tepat atau salah eja", "onboardingImportWalletScrollDown": "<PERSON><PERSON><PERSON> ke bawah", "onboardingImportWalletScrollUp": "<PERSON><PERSON><PERSON> ke atas", "onboardingSelectAccountsImportAccounts": "<PERSON><PERSON><PERSON>", "onboardingSelectAccountsImportAccountsDescription": "<PERSON><PERSON>h akun dompet untuk mengimpor.", "onboardingSelectAccountsImportSelectedAccounts": "<PERSON><PERSON><PERSON> ya<PERSON>", "onboardingSelectAccountsFindMoreAccounts": "<PERSON>i akun la<PERSON>ya", "onboardingSelectAccountsFindMoreNoneFound": "Tidak ada akun yang di<PERSON>ukan", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} akun dipilih", "onboardingSelectAccountSelectAllText": "<PERSON><PERSON><PERSON>", "onboardingAdditionalPermissionsTitle": "<PERSON><PERSON><PERSON> a<PERSON> dengan <PERSON>", "onboardingAdditionalPermissionsSubtitle": "Untuk pengalaman aplikasi yang benar-benar lancar, sebaiknya izinkan Phantom membaca dan mengubah data di semua situs.", "interstitialAdditionalPermissionsTitle": "<PERSON><PERSON><PERSON> a<PERSON> dengan <PERSON>", "interstitialAdditionalPermissionsSubtitle": "Untuk melanjutkan penggunaan aplikasi tanpa gangguan, sebaiknya izinkan Phantom membaca dan mengubah data di semua situs.", "recentActivityPrimaryText": "Aktivitas Terbaru", "removeAccountActionButtonRemove": "Hapus", "removeAccountRemoveWallet": "<PERSON><PERSON> akun", "removeAccountInterpolated": "Hapus {{accountName}}", "removeAccountWarningLedger": "Meskipun Anda menghapus dompet ini dari Phantom, <PERSON>a bisa menambahkannya kembali menggunakan alur \"Hubungkan Dompet Perangkat Keras\".", "removeAccountWarningSeedVault": "Meskipun Anda menghapus dompet ini dari Phantom, <PERSON>a bisa menambahkannya kembali menggunakan alur \"Hubungkan Dompet Seed Vault\".", "removeAccountWarningPrivateKey": "<PERSON><PERSON><PERSON> men<PERSON> dompet ini, Phantom tidak akan dapat memulihkannya untuk Anda. Pastikan Anda telah mencadangkan kunci privat Anda.", "removeAccountWarningSeed": "Meskipun Anda menghapus dompet ini dari Phantom, <PERSON>a bisa mendapatkannya kembali menggunakan mnemonic Anda di dompet ini atau dompet yang lain.", "removeAccountWarningReadOnly": "Penghapusan akun ini tidak akan memengaruhi dompet Anda, karena akun ini hanya dompet untuk memantau.", "removeSeedPrimaryText": "<PERSON><PERSON><PERSON><PERSON> Rahas<PERSON> {{number}}", "removeSeedSecondaryText": "Tindakan ini akan menghapus semua akun yang ada dalam <PERSON>asa <PERSON>has<PERSON> {{number}}. Pastikan Anda telah menyimpan frasa rahasia yang sudah ada.", "resetSeedPrimaryText": "Atur ulang aplikasi dengan frasa rahasia baru", "resetSeedSecondaryText": "Tindakan ini akan menghapus semua akun yang ada dan menggantinya dengan yang baru. Pastikan Anda telah mencadangkan frasa rahasia dan kunci pribadi yang sudah ada.", "resetAppPrimaryText": "Reset & sapu bersih aplikasi", "resetAppSecondaryText": "Tindakan ini akan menghapus semua akun dan data yang ada. Pastikan Anda telah mencadangkan frasa rahasia dan kunci pribadi.", "richTransactionsDays": "hari", "richTransactionsToday": "<PERSON> ini", "richTransactionsYesterday": "<PERSON><PERSON><PERSON>", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "Interaksi App", "richTransactionDetailAt": "pada", "richTransactionDetailBid": "<PERSON><PERSON>", "richTransactionDetailBidDetails": "<PERSON><PERSON>", "richTransactionDetailBought": "<PERSON><PERSON><PERSON>", "richTransactionDetailBurned": "<PERSON><PERSON><PERSON>", "richTransactionDetailCancelBid": "<PERSON><PERSON><PERSON>", "richTransactionDetailCompleted": "Se<PERSON><PERSON>", "richTransactionDetailConfirmed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailDate": "Tanggal", "richTransactionDetailFailed": "Gaga<PERSON>", "richTransactionDetailFrom": "<PERSON><PERSON>", "richTransactionDetailItem": "<PERSON><PERSON>", "richTransactionDetailListed": "<PERSON><PERSON><PERSON>", "richTransactionDetailListingDetails": "Detail <PERSON>", "richTransactionDetailListingPrice": "<PERSON><PERSON>", "richTransactionDetailMarketplace": "Marketplace", "richTransactionDetailNetworkFee": "<PERSON><PERSON><PERSON>", "richTransactionDetailOriginalListingPrice": "<PERSON><PERSON>", "richTransactionDetailPending": "Tertunda", "richTransactionDetailPrice": "<PERSON><PERSON>", "richTransactionDetailProvider": "Penyedia", "richTransactionDetailPurchaseDetails": "Detail <PERSON>", "richTransactionDetailRebate": "Potongan", "richTransactionDetailReceived": "Diterima", "richTransactionDetailSaleDetails": "Detail <PERSON>", "richTransactionDetailSent": "Terkirim", "richTransactionDetailSold": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailStaked": "Sudah stake", "richTransactionDetailStatus": "Status", "richTransactionDetailSwap": "<PERSON><PERSON>", "richTransactionDetailSwapDetails": "<PERSON><PERSON>", "richTransactionDetailTo": "<PERSON>", "richTransactionDetailTokenSwap": "Pertukaran Token", "richTransactionDetailUnknownNFT": "NFT tidak diketahui", "richTransactionDetailUnlisted": "<PERSON><PERSON>", "richTransactionDetailUnstaked": "Belum stake", "richTransactionDetailValidator": "Validator", "richTransactionDetailViewOnExplorer": "<PERSON><PERSON> di {{explorer}}", "richTransactionDetailWithdrawStake": "<PERSON><PERSON>", "richTransactionDetailYouPaid": "<PERSON><PERSON> te<PERSON>", "richTransactionDetailYouReceived": "<PERSON><PERSON> te<PERSON>", "richTransactionDetailUnwrapDetails": "Detail Unwrap", "richTransactionDetailTokenUnwrap": "Unwrap Token", "activityItemsRefreshFailed": "<PERSON>l memuat transaksi baru.", "activityItemsPagingFailed": "<PERSON><PERSON> memuat transaksi lama.", "activityItemsTestnetNotAvailable": "Riwayat transaksi Testnet saat ini tidak tersedia", "historyUnknownDappName": "Tidak diketahui", "historyStatusSucceeded": "<PERSON><PERSON><PERSON><PERSON>", "historyNetwork": "<PERSON><PERSON><PERSON>", "historyAttemptedAmount": "<PERSON><PERSON><PERSON> upaya", "historyAmount": "<PERSON><PERSON><PERSON>", "sendAddressBookButtonLabel": "<PERSON><PERSON>", "addressBookSelectAddressBook": "<PERSON><PERSON>", "sendAddressBookNoAddressesSaved": "Tidak ada alamat disimpan", "sendAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON>", "addressBookSelectRecentlyUsed": "<PERSON><PERSON><PERSON>", "sendConfirmationLabel": "Label", "sendConfirmationMessage": "<PERSON><PERSON>", "sendConfirmationNetworkFee": "<PERSON><PERSON><PERSON>", "sendConfirmationPrimaryText": "Konfirmasi & Kirim", "sendWarning_INSUFFICIENT_FUNDS": "Dana tidak cukup. Transaksi ini kemungkinan besar akan gagal jika diajukan.", "sendFungibleSummaryNetwork": "<PERSON><PERSON><PERSON>", "sendFungibleSummaryNetworkFee": "<PERSON><PERSON><PERSON>", "sendFungibleSummaryEstimatedTime": "Estimasi waktu", "sendFungiblePendingEstimatedTime": "Estimasi <PERSON>", "sendFungibleSummaryEstimatedTimeDescription": "Kecepatan transaksi Ethereum bervariasi berdasarkan beberapa faktor. <PERSON><PERSON> dapat mempercepatnya dengan mengeklik \"Biaya Jaringan\".", "sendSummaryBitcoinPendingTxTitle": "Tidak dapat mengajukan transfer", "sendSummaryBitcoinPendingTxDescription": "Tidak boleh ada lebih dari satu transfer BTC yang tertunda. Tunggulah hingga transfer selesai untuk mengajukan transfer baru.", "sendFungibleSatProtectionTitle": "Mengirim dengan <PERSON>", "sendFungibleSatProtectionExplainer": "Phantom memastikan bahwa Ordinals dan BRC20 Anda tidak akan digunakan untuk biaya transaksi atau transfer Bitcoin.", "sendFungibleTransferFee": "Biaya transfer token", "sendFungibleTransferFeeToolTip": "Pembuat token ini menerima biaya dari setiap transfer. Ini bukan biaya yang dibebankan atau dipungut oleh Phantom.", "sendFungibleInterestBearingPercent": "Tingkat Bunga Saat Ini", "sendFungibleNonTransferable": "Tidak Dapat Ditransfer", "sendFungibleNonTransferableToolTip": "Token ini tidak dapat ditransfer ke akun lain.", "sendFungibleNonTransferableYes": "Ya", "sendStatusErrorMessageInterpolated": "<PERSON><PERSON><PERSON><PERSON> eror saat mencoba mengirim token ke <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "Saldo Anda tidak cukup untuk menyelesaikan transaksi ini.", "sendStatusErrorTitle": "Tidak bisa mengirim", "sendStatusLoadingTitle": "Mengirim...", "sendStatusSuccessMessageInterpolated": "Token Anda berhasil dikirim ke <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Terkirim!", "sendStatusConfirmedSuccessTitle": "Terkirim!", "sendStatusSubmittedSuccessTitle": "Transaksi Terkirim", "sendStatusEstimatedTransactionTime": "<PERSON><PERSON><PERSON><PERSON>: {{time}}", "sendStatusViewTransaction": "<PERSON><PERSON>", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> kepada <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> berhasil dikirimkan kepada <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> berhasil dikirimkan kepada <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> gagal dikirimkan kepada <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON><PERSON> {{code}}", "sendFormErrorInsufficientBalance": "Saldo tidak men<PERSON>", "sendFormErrorEmptyAmount": "<PERSON><PERSON><PERSON> yang <PERSON>", "sendFormInvalidAddress": "Alamat {{assetName}} tidak valid", "sendFormInvalidUsernameOrAddress": "<PERSON>a pengguna atau alamat tidak sah", "sendFormErrorInvalidSolanaAddress": "<PERSON><PERSON><PERSON> tidak valid", "sendFormErrorInvalidTwitterHandle": "<PERSON><PERSON> pen<PERSON>una Twitter ini tidak terdaftar", "sendFormErrorInvalidDomain": "Domain ini tidak terdaftar", "sendFormErrorInvalidUsername": "<PERSON>a pengguna ini tidak terdaftar", "sendFormErrorMinRequiredInterpolated": "Minimal {{minAmount}} {{tokenName}} diperlukan", "sendRecipientTextareaPlaceholder": "Alamat SOL penerima", "sendRecipientTextAreaPlaceholder2": "<PERSON><PERSON><PERSON> {{symbol}} penerima", "sendMemoOptional": "Memo (opsional)", "sendMemo": "Memo", "sendOptional": "opsional", "settings": "<PERSON><PERSON><PERSON><PERSON>", "settingsDapps": "dApp", "settingsSelectedAccount": "<PERSON><PERSON><PERSON> te<PERSON>h", "settingsAddressBookNoLabel": "Tidak Ada Label", "settingsAddressBookPrimary": "<PERSON><PERSON>", "settingsAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON>", "settingsAddressBookSecondary": "<PERSON><PERSON><PERSON> alamat yang umum digunakan", "settingsAutoLockTimerPrimary": "<PERSON><PERSON><PERSON>", "settingsAutoLockTimerSecondary": "Ubah durasi waktu kunci otomatis Anda", "settingsChangeLanguagePrimary": "Ganti Bahasa", "settingsChangeLanguageSecondary": "Ganti bahasa tampilan", "settingsChangeNetworkPrimary": "Ubah Jaringan", "settingsChangeNetworkSecondary": "Kon<PERSON>gu<PERSON><PERSON>n pengaturan jaringan <PERSON>a", "settingsChangePasswordPrimary": "Ubah kata sandi", "settingsChangePasswordSecondary": "Ubah kata sandi layar kunci <PERSON>a", "settingsCompleteBetaSurvey": "Selesaikan Survei Beta", "settingsDisplayLanguage": "Bahasa Tampilan", "settingsErrorCannotExportLedgerPrivateKey": "Tidak dapat mengekspor kunci privat Ledger", "settingsErrorCannotRemoveAllWallets": "Tidak dapat menghapus semua akun", "settingsExportPrivateKey": "<PERSON><PERSON><PERSON><PERSON> Kunci Privat", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Jaringan RPC Phantom", "settingsTestNetworks": "<PERSON><PERSON><PERSON>", "settingsUseCustomNetworks": "<PERSON><PERSON><PERSON>", "settingsTestnetMode": "Mode Testnet", "settingsTestnetModeDescription": "Diterapkan untuk saldo dan hubungan aplikasi.", "settingsWebViewDebugging": "Debug Tampilan Web", "settingsWebViewDebuggingDescription": "Memungkinkan Anda memeriksa dan mendebug tampilan web browser dalam aplikasi.", "settingsTestNetworksInfo": "<PERSON><PERSON><PERSON> ke jaringan Testnet mana pun hanya dimaksudkan untuk tujuan pengujian. <PERSON><PERSON> ketahui bahwa token di Jaringan Testnet tidak memiliki nilai uang apa pun.", "settingsEmojis": "<PERSON><PERSON><PERSON>", "settingsNoAddresses": "Tidak ada alamat", "settingsAddressBookEmptyHeading": "<PERSON><PERSON> kosong", "settingsAddressBookEmptyText": "Klik tombol \"+\" atau \"Tambahkan Alamat\" untuk menambahkan alamat favorit Anda", "settingsEditWallet": "<PERSON>", "settingsNoTrustedApps": "Tidak ada aplikasi yang dapat dipercaya", "settingsNoConnections": "Belum ada hubungan.", "settingsRemoveWallet": "Hapus Akun", "settingsResetApp": "Atur Ulang Aplikasi", "settingsBlocked": "Diblokir", "settingsBlockedAccounts": "<PERSON><PERSON><PERSON>", "settingsNoBlockedAccounts": "Tidak ada akun di<PERSON>lo<PERSON>.", "settingsRemoveSecretPhrase": "<PERSON>pus <PERSON>", "settingsResetAppWithSecretPhrase": "<PERSON><PERSON> ulang Aplikasi dengan Frasa Rahasia", "settingsResetSecretRecoveryPhrase": "<PERSON><PERSON> <PERSON>", "settingsShowSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON>", "settingsShowSecretRecoveryPhraseSecondary": "<PERSON><PERSON><PERSON><PERSON>", "settingsShowSecretRecoveryPhraseTertiary": "<PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsAutoConfirmActiveUntil": "Sampai {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsDisclaimer": "<PERSON><PERSON> mengizin<PERSON> konfirmasi otomatis pada situs tepercaya", "settingsTrustedAppsLastUsed": "Dipakai {{formattedTimestamp}} yang lalu", "settingsTrustedAppsPrimary": "Aplikasi Terhubung", "settingsTrustedApps": "Aplikasi Tepercaya", "settingsTrustedAppsRevoke": "<PERSON><PERSON>", "settingsTrustedAppsRevokeToast": "Hubungan {{trustedApp}} terputus", "settingsTrustedAppsSecondary": "Konfigurasikan aplikasi tepercaya Anda", "settingsTrustedAppsToday": "<PERSON> ini", "settingsTrustedAppsYesterday": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsLastWeek": "<PERSON><PERSON>", "settingsTrustedAppsBeforeYesterday": "Sebelumnya", "settingsTrustedAppsDisconnectAll": "Putuskan sambungan dari semua", "settingsTrustedAppsDisconnectAllToast": "Sambungan semua aplikasi terputus", "settingsTrustedAppsEndAutoConfirmForAll": "<PERSON><PERSON><PERSON> kon<PERSON><PERSON><PERSON> otomatis untuk semua", "settingsTrustedAppsEndAutoConfirmForAllToast": "<PERSON><PERSON><PERSON> sesi konfi<PERSON><PERSON> otomati<PERSON> be<PERSON>", "settingsSecurityPrimary": "Keamanan & Privasi", "settingsSecuritySecondary": "<PERSON><PERSON><PERSON> pengaturan keamanan <PERSON>", "settingsActiveNetworks": "Jaringan Aktif", "settingsActiveNetworksAll": "<PERSON><PERSON><PERSON>", "settingsActiveNetworksSolana": "<PERSON><PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "<PERSON><PERSON> adalah jaringan default dan akan selalu aktif.", "settingsDeveloperPrimary": "Pengaturan Pengembang", "settingsAdvanced": "Pengaturan Lanju<PERSON>", "settingsTransactions": "Pengaturan <PERSON>", "settingsAutoConfirm": "Pengat<PERSON><PERSON>", "settingsSecurityAnalyticsPrimary": "Berbagi Analitik Anonim", "settingsSecurityAnalyticsSecondary": "Aktifkan untuk membantu peningkatan kami", "settingsSecurityAnalyticsHelper": "Phantom tidak menggunakan informasi pribadi Anda untuk tujuan analisis", "settingsSuspiciousCollectiblesPrimary": "Sembunyikan Koleksi Mencurigakan", "settingsSuspiciousCollectiblesSecondary": "Alihkan untuk menyembunyikan koleksi yang ditandai", "settingsPreferredBitcoinAddress": "Alamat Bitcoin Favorit", "settingsEnabledAddressesUpdated": "<PERSON><PERSON>t yang terlihat diperbarui!", "settingsEnabledAddresses": "<PERSON><PERSON><PERSON> ya<PERSON>", "settingsBitcoinPaymentAddressForApps": "<PERSON><PERSON><PERSON> untuk Aplikasi", "settingsBitcoinOrdinalsAddressForApps": "Alamat Ordinals untuk Aplikasi", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Jika kedua jenis alamat di atas diaktifkan, untuk aplikasi tertentu seperti Magic Eden, alamat Native Segwit Anda akan digunakan untuk mendanai pembelian. Aset yang dibeli akan diterima di alamat Taproot Anda.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Alamat Bitcoin default di Phantom untuk memastikan kompatibilitas.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(<PERSON><PERSON><PERSON>)", "settingsPreferredBitcoinAddressTaprootExplainer": "<PERSON><PERSON> paling modern, <PERSON><PERSON>a dengan biaya transaksi lebih murah.", "settingsPreferredExplorers": "Explorer <PERSON><PERSON><PERSON><PERSON>", "settingsPreferredExplorersSecondary": "Ganti ke explorer blockchain favorit <PERSON>a", "settingsCustomGasControls": "Kendali Gas Kustom", "settingsSupportDesk": "Dukungan Teknis", "settingsSubmitATicket": "<PERSON><PERSON><PERSON>", "settingsAttachApplicationLogs": "Lampirkan Log Aplikasi", "settingsDownloadApplicationLogs": "Unduh Log Aplikasi", "settingsDownloadApplicationLogsShort": "<PERSON><PERSON><PERSON>g", "settingsDownloadApplicationLogsHelper": "Berisi data lokal, laporan crash, dan alamat dompet publik untuk membantu menyelesaikan masalah Dukungan Phantom", "settingsDownloadApplicationLogsWarning": "Tidak ada data sensitif seperti frasa seed atau kunci privat yang disertakan.", "settingsWallet": "Dompet", "settingsPreferences": "<PERSON><PERSON><PERSON><PERSON>", "settingsSecurity": "<PERSON><PERSON><PERSON>", "settingsDeveloper": "Pengembang", "settingsSupport": "Dukungan", "settingsWalletShortcutsPrimary": "<PERSON><PERSON><PERSON><PERSON>", "settingsAppIcon": "<PERSON><PERSON>", "settingsAppIconDefault": "<PERSON><PERSON><PERSON>", "settingsAppIconLight": "Terang", "settingsAppIconDark": "<PERSON><PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON><PERSON>", "settingsSearchResultSelected": "<PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultExport": "Ekspor", "settingsSearchResultSeed": "Seed", "settingsSearchResultTrusted": "Terpercaya", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Status", "settingsSearchResultLogs": "Log", "settingsSearchResultBiometric": "Biometrik", "settingsSearchResultTouch": "<PERSON><PERSON><PERSON>", "settingsSearchResultFace": "<PERSON><PERSON><PERSON>", "settingsSearchResultShortcuts": "<PERSON><PERSON><PERSON>", "settingsAllSitesPermissionsTitle": "<PERSON><PERSON><PERSON> di semua situs", "settingsAllSitesPermissionsSubtitle": "Memungkinkan Anda menggunakan aplikasi secara lancar dengan Phantom tanpa mengeklik ekstensinya", "settingsAllSitesPermissionsDisabled": "Browser <PERSON><PERSON> tidak mendukung perubahan pengaturan ini", "settingsSolanaCopyTransaction": "Aktifkan Penyalinan Transaksi", "settingsSolanaCopyTransactionDetails": "Salin data transaksi berseri ke papan klip", "settingsAutoConfirmHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "refreshWebpageToApplyChanges": "Segarkan halaman web untuk menerapkan perubahan", "settingsExperimentalTitle": "Fitur Eksperimental", "settingsExprimentalSolanaActionsSubtitle": "Membentangkan tombol Solana Action secara otomatis ketika tautan yang relevan terdeteksi di X.com", "stakeAccountCardActiveStake": "Staking Aktif", "stakeAccountCardBalance": "<PERSON><PERSON>", "stakeAccountCardRentReserve": "Cadanga<PERSON>", "stakeAccountCardRewards": "<PERSON><PERSON>", "stakeAccountCardRewardsTooltip": "<PERSON>i adalah hadiah terbaru yang Anda peroleh dari staking. <PERSON>a di<PERSON>i hadiah 3 hari sekali.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "<PERSON><PERSON><PERSON>", "stakeRewardsHistoryTitle": "<PERSON><PERSON><PERSON><PERSON>", "stakeRewardsActivityItemTitle": "<PERSON><PERSON>", "stakeRewardsHistoryEmptyList": "Tidak ada hadiah", "stakeRewardsTime_zero": "<PERSON> ini", "stakeRewardsTime_one": "<PERSON><PERSON><PERSON>", "stakeRewardsTime_other": "{{count}} hari lalu", "stakeRewardsItemsPagingFailed": "<PERSON><PERSON> memuat hadiah lama.", "stakeAccountCreateAndDelegateErrorStaking": "<PERSON> permas<PERSON> dalam Staking pada validator ini. <PERSON><PERSON><PERSON> coba lagi.", "stakeAccountCreateAndDelegateSolStaked": "Staking SOL berhasil!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "SOL Anda akan mulai men<PERSON><PERSON><PERSON><PERSON> hadiah <1></1> dalam beberapa hari berikutnya setelah akun Staking mulai aktif.", "stakeAccountCreateAndDelegateStakingFailed": "Staking Gagal", "stakeAccountCreateAndDelegateStakingSol": "Staking SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "<PERSON><PERSON>g membuat akun <PERSON>, dan akan mendelegasikan SOL Anda ke", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "<PERSON><PERSON>g membuat akun <PERSON>, dan akan mendelegasikan SOL Anda ke {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Li<PERSON>", "stakeAccountDeactivateStakeSolUnstaked": "Staking SOL dibatalkan!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Anda bisa membatalkan Staking Anda <1></1> dalam beberapa hari berikutnya setelah akun Staking mulai tidak aktif.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Anda bisa membatalkan Staking Anda dalam beberapa hari berikutnya setelah akun Staking mulai tidak aktif.", "stakeAccountDeactivateStakeUnstakingFailed": "Pembatalan Staking Gagal", "stakeAccountDeactivateStakeUnstakingFailedDescription": "<PERSON> permas<PERSON> dalam Staking pada validator ini. <PERSON><PERSON><PERSON> coba lagi.", "stakeAccountDeactivateStakeUnstakingSol": "Membatalkan Staking SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "<PERSON><PERSON>g memulai proses pembatalan Staking SOL Anda.", "stakeAccountDeactivateStakeViewTransaction": "Li<PERSON>", "stakeAccountDelegateStakeSolStaked": "Staking SOL berhasil!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "SOL Anda akan mulai men<PERSON><PERSON><PERSON><PERSON> hadiah <1></1> dalam beberapa hari berikutnya setelah akun Staking mulai aktif.", "stakeAccountDelegateStakeStakingFailed": "Staking Gagal", "stakeAccountDelegateStakeStakingFailedDescription": "<PERSON> permas<PERSON> dalam Staking pada validator ini. <PERSON><PERSON><PERSON> coba lagi.", "stakeAccountDelegateStakeStakingSol": "Staking SOL...", "stakeAccountDelegateStakeStakingSolDescription": "<PERSON><PERSON>g mendelegasikan SOL Anda.", "stakeAccountDelegateStakeViewTransaction": "Li<PERSON>", "stakeAccountListActivationActivating": "Aktifkan", "stakeAccountListActivationActive": "Aktif", "stakeAccountListActivationInactive": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountListActivationDeactivating": "Menonaktifkan", "stakeAccountListErrorFetching": "<PERSON>mi tidak dapat mengambil akun staking. Cobalah lagi nanti.", "stakeAccountListNoStakingAccounts": "Tidak Ada Akun Staking", "stakeAccountListReload": "<PERSON><PERSON> ul<PERSON>", "stakeAccountListViewPrimaryText": "Staking Anda", "stakeAccountListViewStakeSOL": "Staking SOL", "stakeAccountListItemStakeFee": "Biaya {{fee}}", "stakeAccountViewActionButtonRestake": "Staking ulang", "stakeAccountViewActionButtonUnstake": "Batalkan Staking", "stakeAccountViewError": "<PERSON><PERSON><PERSON>", "stakeAccountViewPrimaryText": "Staking Anda", "stakeAccountViewRestake": "Staking ulang", "stakeAccountViewSOLCurrentlyStakedInterpolated": "SOL Anda saat ini sedang di-Staking dengan validator. Anda perlu membatalkan Staking untuk <1></1>mengakses dana ini. <3><PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t</3>", "stakeAccountViewStakeInactive": {"part1": "Akun Staking ini tidak aktif. Pertimbangkan untuk menarik Staking atau menemukan validator untuk didelegasikan.", "part2": "<PERSON><PERSON><PERSON><PERSON> lebih lan<PERSON>t"}, "stakeAccountViewStakeNotFound": "<PERSON><PERSON>n Staking ini tidak dapat ditemukan.", "stakeAccountViewViewOnExplorer": "<PERSON><PERSON> di {{explorer}}", "stakeAccountViewWithdrawStake": "<PERSON><PERSON>", "stakeAccountViewWithdrawUnstakedSOL": "Tarik SOL yang tidak di-Staking", "stakeAccountInsufficientFunds": "SOL yang tersedia tidak cukup untuk membatalkan staking atau melakukan penarikan.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL ditarik!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL Anda telah ditarik.", "part2": "Akun Staking ini akan dihapus secara otomatis dalam beberapa menit."}, "stakeAccountWithdrawStakeViewTransaction": "Li<PERSON>", "stakeAccountWithdrawStakeWithdrawalFailed": "Penarikan <PERSON>", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "<PERSON> permasalahan penarikan dari akun Staking ini. <PERSON>lakan coba lagi.", "stakeAccountWithdrawStakeWithdrawingSol": "Menarik SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "<PERSON><PERSON> sedang menarik SOL Anda dari akun Staking ini.", "startEarningSolAccount": "akun", "startEarningSolAccounts": "akun-akun", "startEarningSolErrorClosePhantom": "Ketuk di sini dan coba lagi", "startEarningSolErrorTroubleLoading": "<PERSON><PERSON><PERSON><PERSON> dalam memuat Staking", "startEarningSolLoading": "Memuat...", "startEarningSolPrimaryText": "<PERSON><PERSON>", "startEarningSolSearching": "<PERSON><PERSON><PERSON> akun-akun <PERSON>", "startEarningSolStakeTokens": "Staking token dan dapa<PERSON><PERSON> hadiah", "startEarningSolYourStake": "Staking Anda", "unwrapFungibleTitle": "<PERSON><PERSON> ke {{tokenSymbol}}", "unwrapFungibleDescription": "<PERSON><PERSON> dari {{fromToken}} se<PERSON><PERSON> {{toToken}}", "unwrapFungibleConfirmSwap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "<PERSON><PERSON><PERSON><PERSON>", "swapFeesFees": "Biaya", "swapFeesPhantomFee": "Biaya Phantom", "swapFeesPhantomFeeDisclaimer": "<PERSON><PERSON> se<PERSON>u menemukan harga terbaik dari penyedia likuiditas teratas. Biaya sebesar {{feePercentage}} secara otomatis diperhitungkan dalam pengajuan harga ini.", "swapFeesRate": "<PERSON><PERSON>", "swapFeesRateDisclaimer": "<PERSON><PERSON> terbaik yang di<PERSON>ukan Jupiter Aggregator dari berb<PERSON>i bursa terdesentralisasi.", "swapFeesRateDisclaimerMultichain": "<PERSON><PERSON> terbaik yang ditemukan dari berbagai bursa terdesentralisasi.", "swapFeesPriceImpact": "<PERSON><PERSON><PERSON>", "swapFeesHighPriceImpact": "<PERSON><PERSON><PERSON>", "swapFeesPriceImpactDisclaimer": "<PERSON><PERSON><PERSON> antara harga pasar dan perkiraan harga berdasarkan ukuran perdagangan <PERSON>.", "swapFeesSlippage": "<PERSON><PERSON>", "swapFeesHighSlippage": "Toleransi Selip <PERSON>", "swapFeesHighSlippageDisclaimer": "Transaksi Anda akan gagal jika harga berubah secara tidak menguntungkan melebihi {{slippage}}%.", "swapTransferFee": "Biaya Transfer", "swapTransferFeeDisclaimer": "Perdagangan ${{symbol}} dikenakan biaya transfer {{feePercent}}% yang ditetapkan oleh pembuat token, bukan Phantom.", "swapTransferFeeDisclaimerMany": "Perdagangan token yang dipilih dikenakan biaya transfer {{feePercent}}% yang ditetapkan oleh pembuat token, bukan Phantom.", "swapFeesSlippageDisclaimer": "Penyimpangan harga perdagangan yang mungkin terjadi dari pengajuan harga yang diberikan.", "swapFeesProvider": "Penyedia", "swapFeesProviderDisclaimer": "Bursa terdesentralisasi yang digunakan untuk menyelesaikan perdagangan Anda.", "swapEstimatedTime": "Estimasi <PERSON>", "swapEstimatedTimeShort": "<PERSON><PERSON><PERSON><PERSON> waktu", "swapEstimatedTimeDisclaimer": "Perkiraan waktu penyelesaian untuk jembatan akan bervariasi tergantung pada beberapa faktor yang memengaruhi kecepatan transaksi.", "swapSettingsButtonCommand": "<PERSON><PERSON>", "swapQuestionRetry": "Coba lagi?", "swapUnverifiedTokens": "Token Tak Terverifikasi", "swapSectionTitleTokens": "Token {{section}}", "swapFlowYouPay": "<PERSON><PERSON>", "swapFlowYouReceive": "<PERSON><PERSON>", "swapFlowActionButtonText": "Tinjau Order", "swapAssetCardTokenNetwork": "{{symbol}} di {{network}}", "swapAssetCardMaxButton": "Ma<PERSON>.", "swapAssetCardSelectTokenAndNetwork": "<PERSON><PERSON><PERSON> dan <PERSON>", "swapAssetCardBuyTitle": "<PERSON><PERSON>", "swapAssetCardSellTitle": "<PERSON><PERSON>", "swapAssetWarningUnverified": "Token ini belum diverifikasi. Berinteraksilah dengan token yang Anda percayai saja.", "swapAssetWarningPermanentDelegate": "Delegasi dapat membakar atau mentransfer token ini secara permanen.", "swapSlippageSettingsTitle": "<PERSON><PERSON><PERSON><PERSON>", "swapSlippageSettingsSubtitle": "Transaksi Anda akan gagal jika harga berubah lebih dari selip. <PERSON><PERSON> yang terlalu tinggi akan mengakibatkan perdagangan yang tidak menguntungkan.", "swapSlippageSettingsCustom": "Kustom", "swapSlippageSettingsHighSlippageWarning": "Transaksi <PERSON> mungkin disalip (frontrun) dan men<PERSON><PERSON><PERSON>an perdagangan yang tidak menguntun<PERSON>.", "swapSlippageSettingsCustomMinError": "<PERSON><PERSON><PERSON><PERSON> nilai yang lebih besar dari {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "<PERSON><PERSON><PERSON><PERSON> nilai yang lebih kecil dari {{minSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "<PERSON><PERSON> masukkan nilai yang sah.", "swapSlippageSettingsAutoSubtitle": "Phantom akan menemukan selip terendah untuk pertukaran yang berhasil.", "swapSlippageSettingsAuto": "<PERSON><PERSON><PERSON><PERSON>", "swapSlippageSettingsFixed": "Tetap", "swapSlippageOptInTitle": "<PERSON><PERSON>", "swapSlippageOptInSubtitle": "Phantom akan menemukan selip terendah untuk penukaran yang berhasil. <PERSON><PERSON> dapat mengu<PERSON>ya kapan saja di Penukar → Pengaturan Selip.", "swapSlippageOptInEnableOption": "Aktifkan <PERSON>", "swapSlippageOptInRejectOption": "Lan<PERSON><PERSON><PERSON> dengan <PERSON>", "swapQuoteFeeDisclaimer": "Pengajuan harga sudah termasuk {{feePercentage}} biaya Phantom", "swapQuoteMissingContext": "Tidak ada konteks pengajuan penukaran", "swapQuoteErrorNoQuotes": "Mencoba menukar tanpa pen<PERSON>", "swapQuoteSolanaNetwork": "<PERSON><PERSON><PERSON>", "swapQuoteNetwork": "<PERSON><PERSON><PERSON>", "swapQuoteOneTimeSerumAccount": "<PERSON><PERSON><PERSON>um sekali pakai", "swapQuoteOneTimeTokenAccount": "Akun token sekali pakai", "swapQuoteBridgeFee": "Biaya <PERSON>", "swapQuoteDestinationNetwork": "<PERSON><PERSON><PERSON>", "swapQuoteLiquidityProvider": "<PERSON><PERSON><PERSON>", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON>", "swapReviewFlowPrimaryText": "Tinjau Order", "swapReviewFlowYouPay": "<PERSON><PERSON>", "swapReviewFlowYouReceive": "<PERSON><PERSON>", "swapReviewInsufficientBalance": "<PERSON>", "ugcSwapWarningTitle": "<PERSON><PERSON><PERSON>", "ugcSwapWarningBody1": "Token ini diperdagangkan di peluncur token {{programName}}.", "ugcSwapWarningBody2": "<PERSON>lai token ini dapat berfluktuasi secara drastis, yang mengakibatkan keuntungan atau kerugian finansial yang besar. Harap ketahui bahwa risiko Anda tanggung sendiri.", "ugcSwapWarningConfirm": "<PERSON><PERSON>", "bondingCurveProgressLabel": "<PERSON><PERSON><PERSON><PERSON>", "bondingCurveInfoTitle": "<PERSON><PERSON>", "bondingCurveInfoDescription": "Dalam model kurva bonding, harga token ditentukan oleh bentuk kurva. Harga meningkat seiring bertambahnya token yang dibeli dan menurun seiring terjualnya token. <PERSON><PERSON><PERSON> token terjual habis, semua likuiditas akan disetor ke Raydium dan dibakar.", "ugcFungibleWarningBanner": "Token ini diperdagangkan di {{programName}}", "ugcCreatedRowLabel": "Dibuat Pada", "ugcStatusRowLabel": "Status", "ugcStatusRowValue": "<PERSON><PERSON>", "swapTxConfirmationReceived": "<PERSON>terima!", "swapTxConfirmationSwapFailed": "<PERSON><PERSON><PERSON>", "swapTxConfirmationSwapFailedStaleQuota": "Pengajuan harga ini sudah tidak berlaku. Mohon coba lagi.", "swapTxConfirmationSwapFailedSlippageLimit": "<PERSON><PERSON> terlalu rendah untuk penukaran ini. Tingkatkan selip <PERSON><PERSON> di bagian atas layar dan coba lagi.", "swapTxConfirmationSwapFailedInsufficientBalance": "Kami tidak dapat menyelesaikan permintaan tersebut. Saldo Anda tidak cukup untuk menyelesaikan transaksi.", "swapTxConfirmationSwapFailedEmptyRoute": "Likuiditas untuk pasangan token ini telah berubah. Kami tidak dapat menemukan pengajuan harga yang sesuai. Cobalah lagi atau ubah jumlah token.", "swapTxConfirmationSwapFailedAcountFrozen": "Token ini telah dibekukan oleh pembuatnya. Anda tidak dapat mengirim atau menukar token ini.", "swapTxConfirmationSwapFailedTryAgain": "<PERSON><PERSON><PERSON>, silakan coba lagi", "swapTxConfirmationSwapFailedUnknownError": "Kami tidak dapat menyelesaikan pertukaran. Tidak ada dana Anda yang terdampak. Silakan coba lagi.", "swapTxConfirmationSwapFailedSimulationTimeout": "Kami tidak dapat melakukan simulasi pertukaran. Tidak ada dana Anda yang terdampak. Silakan coba lagi.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Kami tidak dapat menyelesaikan pertukaran. Tidak ada dana Anda yang terdampak. Silakan coba lagi. ", "swapTxConfirmationSwapFailedInsufficientGas": "Dana di akun Anda tidak cukup untuk menyelesaikan transaksi. Harap tambahkan lebih banyak dana ke akun Anda lalu coba lagi.", "swapTxConfirmationSwapFailedLedgerReject": "<PERSON><PERSON><PERSON> di<PERSON>lak oleh pengguna pada perangkat keras.", "swapTxConfirmationSwapFailedLedgerConnectionError": "<PERSON><PERSON><PERSON> di<PERSON>lak karena kes<PERSON>han koneksi perangkat. Harap coba lagi.", "swapTxConfirmationSwapFailedLedgerSignError": "<PERSON><PERSON><PERSON> di<PERSON>lak karena kesalahan penandatanganan di perangkat. Harap coba lagi.", "swapTxConfirmationSwapFailedLedgerError": "<PERSON><PERSON><PERSON> di<PERSON>lak karena kes<PERSON>han perang<PERSON>. Harap coba lagi.", "swapTxConfirmationSwappingTokens": "<PERSON><PERSON><PERSON> token...", "swapTxConfirmationTokens": "Token", "swapTxConfirmationTokensDeposited": "Selesai! Token sudah disetorkan ke dompet Anda", "swapTxConfirmationTokensDepositedTitle": "Selesai!", "swapTxConfirmationTokensDepositedBody": "Token telah disetorkan ke dompet Anda", "swapTxConfirmationTokensWillBeDeposited": "akan disetorkan ke dompet Anda setelah transaksi selesai", "swapTxConfirmationViewTransaction": "Li<PERSON>", "swapTxBridgeSubmitting": "Mengajukan <PERSON>aksi", "swapTxBridgeSubmittingDescription": "Menukar {{sellAmount}} di {{sellNetwork}} seharga {{buyAmount}} di {{buyNetwork}}", "swapTxBridgeFailed": "Transaksi Gagal Diajukan", "swapTxBridgeFailedDescription": "<PERSON>mi tidak dapat menyelesaikan permintaan tersebut.", "swapTxBridgeSubmitted": "Transaksi <PERSON>", "swapTxBridgeSubmittedDescription": "<PERSON><PERSON><PERSON><PERSON>: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON>a dapat menutup jendela ini dengan aman.", "swapperSwitchTokens": "Ganti token", "swapperMax": "<PERSON><PERSON>", "swapperTooltipNetwork": "<PERSON><PERSON><PERSON>", "swapperTooltipPrice": "<PERSON><PERSON>", "swapperTooltipAddress": "Kontrak", "swapperTrendingSortBy": "<PERSON><PERSON><PERSON><PERSON>", "swapperTrendingTimeFrame": "<PERSON><PERSON>", "swapperTrendingNetwork": "<PERSON><PERSON><PERSON>", "swapperTrendingRank": "<PERSON><PERSON><PERSON>", "swapperTrendingTokens": "Token yang Sedang Tren", "swapperTrendingVolume": "Volume", "swapperTrendingPrice": "<PERSON><PERSON>", "swapperTrendingPriceChange": "<PERSON><PERSON><PERSON>", "swapperTrendingMarketCap": "Batas Pasar", "swapperTrendingTimeFrame1h": "1 jam", "swapperTrendingTimeFrame24h": "24 jam", "swapperTrendingTimeFrame7d": "7 hari", "swapperTrendingTimeFrame30d": "30 hari", "swapperTrendingNoTokensFound": "Token tidak ditemukan.", "switchToggle": "<PERSON><PERSON><PERSON>", "termsOfServiceActionButtonAgree": "<PERSON><PERSON>", "termsOfServiceDisclaimerFeesDisabledInterpolated": "<PERSON><PERSON> <1>\"<PERSON><PERSON>\"</1>, <PERSON><PERSON> <3><PERSON><PERSON><PERSON> <PERSON>n <PERSON></3> penukaran token dengan <PERSON>.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "<PERSON><PERSON> telah mengubah Persyaratan Layanan kami. <PERSON><PERSON> men<PERSON> <1>\"<PERSON><PERSON>\"</1>, <PERSON><PERSON> <3><PERSON><PERSON><PERSON><PERSON></3> baru kami.<5></5><6></6>Persyaratan Layanan baru kami berisi <8>struktur biaya</8> baru untuk produk-produk tertentu.", "termsOfServicePrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "tokenRowUnknownToken": "Token Tidak Diketahui", "transactionsAppInteraction": "Interaksi Aplikasi", "transactionsFailedAppInteraction": "Interaksi aplikasi gagal", "transactionsBidOnInterpolated": "<PERSON><PERSON> {{name}}", "transactionsBidFailed": "<PERSON><PERSON><PERSON> gagal", "transactionsBoughtInterpolated": "<PERSON><PERSON> membeli {{name}}", "transactionsBoughtCollectible": "Telah Membeli Kolektibel", "transactionBridgeInitiated": "<PERSON><PERSON><PERSON>", "transactionBridgeInitiatedFailed": "<PERSON><PERSON><PERSON><PERSON>", "transactionBridgeStatusLink": "Periksa Status di LI.FI", "transactionsBuyFailed": "P<PERSON><PERSON><PERSON> gagal", "transactionsBurnedSpam": "Telah membakar spam", "transactionsBurned": "<PERSON><PERSON><PERSON>", "transactionsUnwrapped": "Di-unwrap", "transactionsUnwrappedFailed": "Unwrap gagal", "transactionsCancelBidOnInterpolated": "<PERSON><PERSON><PERSON><PERSON> tawaran pada {{name}}", "transactionsCancelBidOnFailed": "<PERSON><PERSON> memba<PERSON> tawaran", "transactionsError": "<PERSON><PERSON><PERSON>", "transactionsFailed": "Gaga<PERSON>", "transactionsSwapped": "<PERSON><PERSON><PERSON>", "transactionsFailedSwap": "<PERSON><PERSON><PERSON>", "transactionsFailedBurn": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "transactionsFrom": "<PERSON><PERSON>", "transactionsListedInterpolated": "Menjual {{name}}", "transactionsListedFailed": "<PERSON><PERSON>", "transactionsNoActivity": "Tidak ada aktivitas", "transactionsReceived": "Diterima", "transactionsReceivedInterpolated": "<PERSON><PERSON><PERSON> {{amount}} SOL", "transactionsSending": "Mengirim...", "transactionsPendingCreateListingInterpolated": "Membuat {{name}}", "transactionsPendingEditListingInterpolated": "Mengedit {{name}}", "transactionsPendingSolanaPayTransaction": "Mengonfirmasi Transaksi Solana Pay", "transactionsPendingRemoveListingInterpolated": "Membatalkan penjualan {{name}}", "transactionsPendingBurningInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsPendingSending": "Mengirim", "transactionsPendingSwapping": "<PERSON><PERSON><PERSON>", "transactionsPendingBridging": "<PERSON><PERSON><PERSON><PERSON>", "transactionsPendingApproving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsPendingCreatingAndDelegatingStake": "Membuat dan mendelegasi<PERSON> stake", "transactionsPendingDeactivatingStake": "Menonaktifkan stake", "transactionsPendingDelegatingStake": "Mendelegasikan stake", "transactionsPendingWithdrawingStake": "Menarik stake", "transactionsPendingAppInteraction": "Interaksi apli tertunda", "transactionsPendingBitcoinTransaction": "Transaksi BTC tertunda", "transactionsSent": "Terkirim", "transactionsSendFailed": "<PERSON><PERSON><PERSON> gagal", "transactionsSwapOn": "<PERSON><PERSON> di {{dappName}}", "transactionsSentInterpolated": "Mengirim {{amount}} SOL", "transactionsSoldInterpolated": "<PERSON><PERSON> menjual {{name}}", "transactionsSoldCollectible": "Telah Menjual Collectible", "transactionsSoldFailed": "Penju<PERSON> gagal", "transactionsStaked": "Sudah stake", "transactionsStakedFailed": "<PERSON><PERSON><PERSON><PERSON> gagal", "transactionsSuccess": "<PERSON><PERSON><PERSON><PERSON>", "transactionsTo": "<PERSON><PERSON><PERSON>", "transactionsTokenSwap": "Pertukaran Token", "transactionsUnknownAmount": "Tidak diketahui", "transactionsUnlistedInterpolated": "<PERSON><PERSON> menjual {{name}}", "transactionsUnstaked": "Belum stake", "transactionsUnlistedFailed": "Gagal membatal<PERSON> pencantuman", "transactionsDeactivateStake": "Telah menonaktifkan tumpukan", "transactionsDeactivateStakeFailed": "Gagal menonaktifkan tumpukan", "transactionsWaitingForConfirmation": "<PERSON><PERSON><PERSON> konfi<PERSON><PERSON>", "transactionsWithdrawStake": "<PERSON><PERSON>", "transactionsWithdrawStakeFailed": "Gagal membatal<PERSON>n", "transactionCancelled": "Di<PERSON><PERSON><PERSON>", "transactionCancelledFailed": "<PERSON><PERSON>", "transactionApproveToken": "Telah menyetujui {{tokenSymbol}}", "transactionApproveTokenFailed": "<PERSON><PERSON> {{tokenSymbol}}", "transactionApprovalFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> gagal", "transactionRevokeApproveToken": "Telah menarik kembali {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "<PERSON>l menarik kembali {{tokenSymbol}}", "transactionRevokeFailed": "Penarikan kembali gagal", "transactionApproveDetailsTitle": "<PERSON><PERSON>", "transactionCancelOrder": "Batalkan order", "transactionCancelOrderFailed": "Pembatalan order gagal", "transactionApproveAppLabel": "Aplikasi", "transactionApproveAmountLabel": "<PERSON><PERSON><PERSON>", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "<PERSON><PERSON><PERSON><PERSON>", "transactionApproveAllItems": "Setujui semua item", "transactionSpendUpTo": "<PERSON><PERSON><PERSON><PERSON> hingga", "transactionCancel": "Batalkan Transaksi", "transactionPrioritizeCancel": "Prioritaskan Pembatalan", "transactionSpeedUp": "Percepat Transaksi", "transactionCancelHelperText": "Transaksi asli mungkin selesai sebelum dibatalkan.", "transactionSpeedUplHelperText": "Ini akan memaksimalkan kecepatan transaksi Anda berdasarkan kondisi jaringan.", "transactionCancelHelperMobile": "Perlu <1>hingga {{amount}}</1> untuk upaya membatalkan transaksi ini. Transaksi asli mungkin selesai sebelum dibatalkan.", "transactionCancelHelperMobileWithEstimate": "Akan perlu <1>hingga {{amount}}</1> untuk upaya membatalkan transaksi ini. Pembatalan kira-kira akan selesai dalam {{timeEstimate}}. Transaksi asli mungkin selesai sebelum dibatalkan.", "transactionSpeedUpHelperMobile": "<PERSON>kan perlu <1>hingga {{amount}}</1> untuk memaksimalkan kecepatan transaksi ini.", "transactionSpeedUpHelperMobileWithEstimate": "<PERSON>kan perlu <1>hingga {{amount}}</1> untuk memaksimalkan kecepatan transaksi ini. Transaksi kira-kira akan selesai dalam {{timeEstimate}}.", "transactionEstimatedTime": "Estimasi waktu", "transactionCancelingSend": "Membatalkan pengiriman", "transactionPrioritizingCancel": "Memprioritaskan pembatalan", "transactionCanceling": "Membatalkan", "transactionReplaceError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>. Tidak ada biaya yang dibebankan ke akun Anda. <PERSON>a dapat mencoba lagi.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} tidak cukup", "transactionGasLimitError": "<PERSON>l me<PERSON>an batas gas", "transactionGasEstimationError": "Gagal memperkirakan gas", "pendingTransactionCancel": "<PERSON><PERSON>", "pendingTransactionSpeedUp": "Percepat", "pendingTransactionStatus": "Status", "pendingTransactionPending": "Tertunda", "pendingTransactionPendingInteraction": "Interaksi Tertunda", "pendingTransactionCancelling": "Membatalkan", "pendingTransactionDate": "Tanggal", "pendingTransactionNetworkFee": "<PERSON><PERSON><PERSON>", "pendingTransactionEstimatedTime": "Estimasi waktu", "pendingTransactionEstimatedTimeHM": "{{hours}}j {{minutes}}m", "pendingTransactionEstimatedTimeMS": "{{minutes}}m {{seconds}}d", "pendingTransactionEstimatedTimeS": "{{seconds}}d", "pendingTransactionsSendingTitle": "Mengirim {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Tidak diketahui", "pendingTransactionUnknownApp": "<PERSON><PERSON>li Tidak Diketahu<PERSON>", "permanentDelegateTitle": "<PERSON><PERSON><PERSON><PERSON>", "permanentDelegateValue": "<PERSON><PERSON><PERSON>", "permanentDelegateTooltipTitle": "<PERSON><PERSON><PERSON>", "permanentDelegateTooltipValue": "Delegasi <PERSON><PERSON><PERSON> memung<PERSON>kan akun lain untuk mengelola token atas nama And<PERSON>, termasuk membakar atau mentransfer.", "unlockActionButtonUnlock": "<PERSON><PERSON> kunci", "unlockEnterPassword": "<PERSON><PERSON><PERSON><PERSON> kata sandi", "unlockErrorIncorrectPassword": "Kata sandi salah", "unlockErrorSomethingWentWrong": "<PERSON> k<PERSON>, harap coba lagi nanti", "unlockForgotPassword": "<PERSON>pa kata sandi", "unlockPassword": "<PERSON>a sandi", "forgotPasswordText": "<PERSON>a dapat mereset kata sandi dengan memasukkan frasa pemulihan 12-24 kata. Phantom tidak dapat memulihkan kata sandi Anda langsung.", "appInfo": "Info Aplikasi", "lastUsed": "<PERSON><PERSON><PERSON>", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Tidak tersedia dengan akun perangkat keras.", "trustedAppAutoConfirmDisclaimer1": "<PERSON><PERSON> akt<PERSON>, Phantom akan men<PERSON> semua permintaan dari aplikasi ini tanpa memberi tahu Anda atau meminta konfirmasi.", "trustedAppAutoConfirmDisclaimer2": "<PERSON><PERSON> ini <PERSON>, dana <PERSON>a dapat terkena risiko pen<PERSON>. <PERSON><PERSON> gunakan fitur ini dengan aplikasi yang Anda percayai.", "validationUtilsPasswordIsRequired": "<PERSON>a sandi <PERSON>an", "validationUtilsPasswordLength": "Kata sandi harus terdiri dari 8 karakter", "validationUtilsPasswordsDontMatch": "Kata sandi tidak sama", "validationUtilsPasswordCantBeSame": "Anda tidak dapat menggunakan kata sandi lama", "validatorCardEstimatedApy": "Perkiraan APY", "validatorCardCommission": "<PERSON><PERSON><PERSON>", "validatorCardTotalStake": "Total Staking", "validatorCardNumberOfDelegators": "<PERSON><PERSON><PERSON>", "validatorListChooseAValidator": "<PERSON><PERSON><PERSON>", "validatorListErrorFetching": "<PERSON><PERSON> tidak dapat mengambil validator. Cobalah lagi nanti.", "validatorListNoResults": "Tidak ada hasil", "validatorListReload": "<PERSON><PERSON> ul<PERSON>", "validatorInfoTooltip": "Validator", "validatorInfoTitle": "Validator", "validatorInfoDescription": "Dengan me<PERSON>n staking SOL Anda pada validator, <PERSON><PERSON> pada kinerja dan keamanan jar<PERSON>n <PERSON>, se<PERSON><PERSON>us mendapatkan SOL sebagai imbalan.", "validatorApyInfoTooltip": "Perk. APY", "validatorApyInfoTitle": "Perkiraan APY", "validatorApyInfoDescription": "Ini adalah tingkat pengembalian yang Anda peroleh dari staking SOL Anda pada validator.", "validatorViewActionButtonStake": "Staking", "validatorViewErrorFetching": "Tidak dapat menemukan validator.", "validatorViewInsufficientBalance": "Saldo tidak men<PERSON>", "validatorViewMax": "<PERSON><PERSON>", "validatorViewPrimaryText": "<PERSON><PERSON>", "validatorViewDescriptionInterpolated": "<PERSON><PERSON>h seberapa banyak SOL yang ingin Anda <1></1> Staking dengan validator ini. <3><PERSON><PERSON><PERSON><PERSON> lebih lanjut</3>.", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL diperlukan untuk staking", "validatorViewValidator": "Validator", "walletMenuItemsAddConnectWallet": "Buat / Hubungkan Dompet", "walletMenuItemsBridgeAssets": "Aset Bridge", "walletMenuItemsHelpAndSupport": "Bantuan & Layanan", "walletMenuItemsLockWallet": "<PERSON><PERSON><PERSON>", "walletMenuItemsResetSecretPhrase": "Atur Ulang Frasa Rahasia", "walletMenuItemsShowMoreAccounts": "<PERSON><PERSON><PERSON><PERSON> {{count}} lagi...", "walletMenuItemsHideAccounts": "Sembunyikan akun", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "<PERSON> k<PERSON><PERSON>", "disableMultiChainDetail1Header": "All-in di Solana", "disableMultiChainDetail1SecondaryText": "<PERSON><PERSON><PERSON>, token, dan koleksi <PERSON> Anda tanpa melihat chain lain.", "disableMultiChainDetail2Header": "<PERSON><PERSON><PERSON> ke Multichain kapan saja", "disableMultiChainDetail2SecondaryText": "Saldo Ethereum dan Polygon Anda yang ada akan dipertahankan saat Anda mengaktifkan kembali Multichain.", "disableMultiChainButton": "<PERSON><PERSON><PERSON><PERSON>", "disabledMultiChainHeader": "<PERSON><PERSON><PERSON>", "disabledMultiChainText": "<PERSON>a dapat mengaktifkan multichain kembali kapan saja.", "enableMultiChainHeader": "Aktifkan Multichain", "enabledMultiChainHeader": "<PERSON><PERSON><PERSON>", "enabledMultiChainText": "Ethereum dan Polygon kini didukung dalam dompet Anda.", "incompatibleAccountHeader": "<PERSON><PERSON>n <PERSON>", "incompatibleAccountInterpolated": "Hapuslah akun khusus Ethereum ini sebelum mengaktifkan mode khusus <PERSON>ana: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Apa yang Baru?", "welcomeToMultiChainPrimaryText": "Satu <PERSON>pet untuk Segalanya", "welcomeToMultiChainDetail1Header": "Dukungan Ethereum dan Polygon", "welcomeToMultiChainDetail1SecondaryText": "Semua token dan <PERSON><PERSON>, Ethereum, dan Poly<PERSON> dalam satu tempat.", "welcomeToMultiChainDetail2Header": "<PERSON><PERSON><PERSON> semua aplikasi yang <PERSON>a sukai", "welcomeToMultiChainDetail2SecondaryText": "Terhubung ke aplikasi di berbagai rantai tanpa berganti jaringan.", "welcomeToMultiChainDetail3Header": "Impor dompet MetaMask Anda", "welcomeToMultiChainDetail3SecondaryText": "Impor semua frasa seed Anda di seluruh Ethereum dan Polygon dengan mudah.", "welcomeToMultiChainIntro": "Selamat datang di Phantom Multichain", "welcomeToMultiChainIntroDesc": "Semua token dan <PERSON><PERSON>, Ethereum, dan Poly<PERSON> di satu tempat. Satu dompet Anda untuk semuanya.", "welcomeToMultiChainAccounts": "<PERSON>kun Multichain dirancang ulang", "welcomeToMultiChainAccountsDesc": "<PERSON>ran<PERSON><PERSON> ulang untuk multichain, setiap akun kini memiliki alamat ETH dan Polygon yang sesuai.", "welcomeToMultiChainApps": "Berfungsi di Mana Saja", "welcomeToMultiChainAppsDesc": "Phantom kompatibel dengan setiap aplikasi di Ethereum, Polygon, dan <PERSON>. Klik \"Hubungkan ke MetaMask\" dan <PERSON>a bisa memulai.", "welcomeToMultiChainImport": "<PERSON><PERSON><PERSON> <PERSON>", "welcomeToMultiChainImportDesc": "Impor Fr<PERSON>has<PERSON> atau Kunci Privat Anda dari dompet seperti MetaMask atau Coinbase Wallet. Semua di satu tempat.", "welcomeToMultiChainImportInterpolated": "<0><PERSON><PERSON><PERSON></0> atau Kunci Privat Anda dari dompet seperti MetaMask atau Coinbase Wallet. Semua di satu tempat.", "welcomeToMultiChainTakeTour": "<PERSON><PERSON>", "welcomeToMultiChainSwapperTitle": "Lakukan penukaran di Ethereum,\nPolygon, & Solana", "welcomeToMultiChainSwapperDetail1Header": "Dukungan Ethereum dan Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Sekarang Anda dapat bertukar token ERC-20 dengan mudah dari dompet Anda.", "welcomeToMultiChainSwapperDetail2Header": "<PERSON>rga Terbaik dan Biaya Super-Rendah ", "welcomeToMultiChainSwapperDetail2SecondaryText": "100+ sumber likuiditas dan perutean order cerdas untuk pengembalian maksimal.", "networkErrorTitle": "<PERSON><PERSON><PERSON>", "networkError": "<PERSON><PERSON>, kami tidak dapat mengaks<PERSON> jar<PERSON>n. <PERSON>lakan coba lagi nanti.", "authenticationUnlockPhantom": "Buka Phantom", "errorAndOfflineSomethingWentWrong": "<PERSON> yang salah", "errorAndOfflineSomethingWentWrongTryAgain": "Mohon coba lagi.", "errorAndOfflineUnableToFetchAssets": "<PERSON>mi tidak dapat mengambil aset. Cobalah lagi nanti.", "errorAndOfflineUnableToFetchCollectibles": "Kami tidak dapat mengambil collectible. Cobalah lagi nanti.", "errorAndOfflineUnableToFetchSwap": "Kami tidak dapat mengambil info penukaran. Cobalah lagi nanti.", "errorAndOfflineUnableToFetchTransactionHistory": "<PERSON>mi tidak dapat memperoleh riwayat transaksi Anda saat ini. Periksa koneksi jaringan Anda atau coba lagi nanti.", "errorAndOfflineUnableToFetchRewardsHistory": "<PERSON>mi tidak dapat mengambil riwayat hadiah. Cobalah lagi nanti.", "errorAndOfflineUnableToFetchBlockedUsers": "<PERSON>mi tidak dapat mengambil pengguna yang diblokir. Cobalah lagi nanti.", "networkHealthSheetCloseButtonText": "<PERSON>e", "swapReviewError": "<PERSON><PERSON><PERSON><PERSON> masalah saat meninjau p<PERSON>, harap coba lagi.", "sendSelectToken": "<PERSON><PERSON><PERSON>", "swapBalance": "Saldo:", "swapTitle": "<PERSON><PERSON>", "swapSelectToken": "<PERSON><PERSON><PERSON>", "swapYouPay": "<PERSON><PERSON>", "swapYouReceive": "<PERSON><PERSON>", "aboutPrivacyPolicy": "<PERSON><PERSON><PERSON><PERSON>", "aboutVersion": "Versi {{version}}", "aboutVisitWebsite": "Kunjungi Situs Web", "bottomSheetConnectTitle": "Hubungkan", "A11YbottomSheetConnectTitle": "Bottom Sheet Hu<PERSON>", "A11YbottomSheetCommandClose": "Bottom Sheet <PERSON>", "A11YbottomSheetCommandBack": "Bottom Sheet <PERSON>", "bottomSheetSignTypedDataTitle": "<PERSON>da tangani pesan", "bottomSheetSignMessageTitle": "<PERSON>da tangani pesan", "bottomSheetSignInTitle": "<PERSON><PERSON><PERSON>", "bottomSheetSignInAndConnectTitle": "<PERSON><PERSON><PERSON>", "bottomSheetConfirmTransactionTitle": "Konfirma<PERSON>", "bottomSheetConfirmTransactionsTitle": "Konfirma<PERSON>", "bottomSheetSolanaPayTitle": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetAdvancedTitle": "Lanjutan", "bottomSheetReadOnlyAccountTitle": "Mode <PERSON><PERSON>", "bottomSheetTransactionSettingsTitle": "<PERSON><PERSON><PERSON>", "bottomSheetConnectDescription": "Menghubungkan berarti mengizinkan situs ini untuk melihat saldo dan aktivitas akun terpilih.", "bottomSheetSignInDescription": "Menandatangani pesan ini akan membuktikan bahwa Anda memiliki kepemilikan atas akun yang dipilih. <PERSON><PERSON> tanda tangani pesan dari aplikasi yang Anda percayai.", "bottomSheetSignInAndConnectDescription": "Menyetujui berarti mengizinkan situs ini untuk melihat saldo dan aktivitas akun terpilih.", "bottomSheetConfirmTransactionDescription": "<PERSON>bahan saldo adalah per<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "bottomSheetConfirmTransactionsDescription": "<PERSON>bahan saldo adalah per<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "bottomSheetSignTypedDataDescription": "Ini hanyalah permintaan izin. Transaksi mungkin tidak langsung dieksekusi.", "bottomSheetSignTypedDataSecondDescription": "<PERSON>bahan saldo adalah per<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "bottomSheetSignMessageDescription": "Menandatangani pesan ini akan membuktikan bahwa Anda memiliki kepemilikan atas akun yang dipilih. <PERSON><PERSON> tanda tangani pesan dari aplikasi yang Anda percayai.", "bottomSheetReadOnlyAccountDescription": "Tidak dapat melakukan tindakan ini dalam mode Hanya Lihat.", "bottomSheetMessageRow": "<PERSON><PERSON>", "bottomSheetStatementRow": "<PERSON><PERSON><PERSON>", "bottomSheetAutoConfirmRow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bottomSheetAutoConfirmOff": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetAutoConfirmOn": "Aktif", "bottomSheetAccountRow": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedRow": "Lanjutan", "bottomSheetContractRow": "<PERSON><PERSON><PERSON>", "bottomSheetSpenderRow": "<PERSON><PERSON><PERSON>", "bottomSheetNetworkRow": "<PERSON><PERSON><PERSON>", "bottomSheetNetworkFeeRow": "<PERSON><PERSON><PERSON>", "bottomSheetEstimatedTimeRow": "Estimasi <PERSON>", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Hanya hubungkan ke situs yang tepercaya", "bottomSheetSignInRequestDisclaimer": "Hanya masuk ke situs web yang tepercaya", "bottomSheetSignatureRequestDisclaimer": "<PERSON><PERSON> konfirmasi jika Anda memercayai situs web ini.", "bottomSheetFeaturedTransactionDisclaimer": "<PERSON>a akan melihat pratinjau transaksi sebelum mengonfirmasi pada langkah berikutnya.", "bottomSheetIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON>, tetap lan<PERSON>", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Perubahan saldo tidak ditemukan. La<PERSON><PERSON><PERSON><PERSON> dengan waspada dan berikan konfirmasi hanya jika Anda memercayai situs ini.", "bottomSheetReadOnlyWarning": "Anda hanya memantau alamat ini. Anda harus mengimpor agar dapat menandatangani transaksi dan pesan.", "bottomSheetWebsiteIsUnsafeWarning": "Situs web ini tidak aman untuk digunakan dan mungkin mencoba mencuri dana <PERSON>.", "bottomSheetViewOnExplorer": "<PERSON><PERSON>", "bottomSheetTransactionSubmitted": "Transaksi <PERSON>", "bottomSheetTransactionPending": "Transaksi Tertunda", "bottomSheetTransactionFailed": "Transaksi <PERSON>l", "bottomSheetTransactionSubmittedDescription": "Transaksi Anda telah terkirim. <PERSON>a dapat melihatnya di explorer.", "bottomSheetTransactionFailedDescription": "Transaksi <PERSON>a gagal. Cobalah lagi.", "bottomSheetTransactionPendingDescription": "Transaksi sedang diproses...", "transactionsFromInterpolated": "Dar<PERSON>: {{from}}", "transactionsFromParagraphInterpolated": "<PERSON><PERSON> {{dari}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON>", "transactionsToInterpolated": "Untuk: {{to}}", "transactionsToParagraphInterpolated": "Untuk {{to}}", "transactionsYesterday": "<PERSON><PERSON><PERSON>", "addEditAddressAdd": "<PERSON><PERSON><PERSON> al<PERSON>", "addEditAddressDelete": "<PERSON><PERSON>", "addEditAddressDeleteTitle": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin menghapus alamat ini?", "addEditAddressSave": "<PERSON><PERSON><PERSON>", "dAppBrowserComingSoon": "<PERSON><PERSON><PERSON> dApp segera hadir!", "dAppBrowserSearchPlaceholder": "Situs, token, URL", "dAppBrowserOpenInNewTab": "<PERSON>uka di tab baru", "dAppBrowserSuggested": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserFavorites": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserBookmarks": "Bookmark", "dAppBrowserBookmarkAdd": "Tambah Bookmark", "dAppBrowserBookmarkRemove": "Hapus Bookmark", "dAppBrowserUsers": "Pengguna", "dAppBrowserRecents": "<PERSON><PERSON><PERSON>", "dAppBrowserFavoritesDescription": "<PERSON><PERSON><PERSON><PERSON>a akan ditampilkan di sini", "dAppBrowserBookmarksDescription": "Bookmark Anda akan ditampilkan di sini", "dAppBrowserRecentsDescription": "dapp yang tersambung baru-baru ini akan terlihat di sini", "dAppBrowserEmptyScreenDescription": "Ketikkan URL atau telusuri web", "dAppBrowserBlocklistScreenTitle": "{{origin}} diblokir! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom menganggap situs ini berbahaya dan tidak aman digunakan.", "part2": "Situs ini telah ditandai sebagai bagian dari basis data yang dikelola komunitas yang berisi situs phishing dan penipuan. <PERSON><PERSON> menurut Anda penandaan situs ini adalah suatu k<PERSON>han, silakan ajukan laporan."}, "dAppBrowserLoadFailedScreenTitle": "<PERSON><PERSON> dimuat", "dAppBrowserLoadFailedScreenDescription": "<PERSON> kesalahan dalam memuat halaman ini", "dAppBrowserBlocklistScreenIgnoreButton": "<PERSON><PERSON><PERSON><PERSON>, tetap tampilkan", "dAppBrowserActionBookmark": "Bookmark", "dAppBrowserActionRemoveBookmark": "Hapus bookmark", "dAppBrowserActionRefresh": "Segarkan", "dAppBrowserActionShare": "Bagikan", "dAppBrowserActionCloseTab": "Tutup tab", "dAppBrowserActionEndAutoConfirm": "<PERSON><PERSON><PERSON>", "dAppBrowserActionDisconnectApp": "Putuskan sambungan apli", "dAppBrowserActionCloseAllTabs": "Tutup semua tab", "dAppBrowserNavigationAddressPlaceholder": "Ketikkan URL untuk mencari", "dAppBrowserTabOverviewMore": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddTab": "Tambah Tab", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON><PERSON>", "dAppBrowserClose": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "Tambah Bookmark", "dAppBrowserTabOverviewRemoveBookmark": "Hapus Bookmark", "depositAssetListSuggestions": "Saran", "depositUndefinedToken": "Ma<PERSON>, tidak dapat menyetorkan token ini", "onboardingImportRecoveryPhraseDetails": "Detail", "onboardingCreateRecoveryPhraseVerifyTitle": "Sudah mencatat frasa <PERSON>uli<PERSON>?", "onboardingCreateRecoveryPhraseVerifySubtitle": "<PERSON><PERSON> frasa pemulihan rah<PERSON>a, <PERSON><PERSON> tidak akan dapat mengakses kunci Anda atau aset apa pun yang terkait dengannya.", "onboardingCreateRecoveryPhraseVerifyYes": "Ya", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON><PERSON> tidak ber<PERSON>il membuat akun, harap coba lagi.", "onboardingDoneDescription": "<PERSON>a sekarang dapat menikmati dompet Anda se<PERSON>.", "onboardingDoneGetStarted": "<PERSON><PERSON><PERSON>", "zeroBalanceHeading": "Ayo kita mulai!", "zeroBalanceBuyCryptoTitle": "<PERSON><PERSON>", "zeroBalanceBuyCryptoDescription": "Beli kripto pertama Anda dengan kartu debit atau kredit.", "zeroBalanceDepositTitle": "Transfer Kripto", "zeroBalanceDepositDescription": "<PERSON>or<PERSON> kripto dari dompet atau bursa lain.", "onboardingImportAccountsEmptyResult": "Tidak ada akun yang di<PERSON>ukan", "onboardingImportAccountsAccountName": "Akun {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON>", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "<PERSON><PERSON> {{numberOfWallets}} akun be<PERSON>", "onboardingImportAccountsFoundAccounts_other": "<PERSON><PERSON> {{numberOfWallets}} akun be<PERSON>", "onboardingImportAccountsFoundAccountsNoActivity_one": "<PERSON><PERSON> {{numberOfWallets}} akun", "onboardingImportAccountsFoundAccountsNoActivity_other": "<PERSON><PERSON> {{numberOfWallets}} akun", "onboardingImportRecoveryPhraseLessThanTwelve": "Frasa harus 12 kata atau lebih.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Frasa harus persis 12 atau 24 kata.", "onboardingImportRecoveryPhraseWrongWord": "<PERSON>a salah: {{ words }}.", "onboardingProtectTitle": "Lindungi dompet Anda", "onboardingProtectDescription": "Penambahan keamanan biometrik akan memastikan bahwa Anda orang satu-satunya yang dapat mengakses dompet Anda.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "ID Wajah", "onboardingProtectButtonHeadlineFingerprint": "<PERSON><PERSON> jari", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "<PERSON><PERSON><PERSON> {{ authType }}", "onboardingProtectError": "<PERSON><PERSON><PERSON><PERSON> masalah saat autentikasi, harap coba lagi.", "onboardingProtectBiometryIosError": "Autentikasi biometrik dikonfigurasi di Phantom, tetapi dinonaktifkan di Pengaturan Sistem. Buka Pengaturan > Phantom > Face ID atau Touch ID untuk mengaktifkan kembali.", "onboardingProtectRemoveAuth": "Nonaktifkan autentikasi", "onboardingProtectRemoveAuthDescription": "Yakin ingin menonaktifkan autentikasi?", "onboardingWelcomeTitle": "Selamat datang di Phantom", "onboardingWelcomeDescription": "<PERSON><PERSON><PERSON> memu<PERSON>, buat dompet baru atau impor dompet yang sudah ada.", "onboardingWelcomeCreateWallet": "Buat dompet baru", "onboardingWelcomeAlreadyHaveWallet": "Saya sudah punya dompet", "onboardingWelcomeConnectSeedVault": "Hubungkan Seed Vault", "onboardingSlide1Title": "Diken<PERSON><PERSON><PERSON> o<PERSON>", "onboardingSlide1Description": "<PERSON><PERSON>kan dengan aks<PERSON> biometrik, de<PERSON><PERSON><PERSON>, dan duku<PERSON> 24/7.", "onboardingSlide2Title": "Rumah terbaik untuk\nNFT Anda", "onboardingSlide2Description": "<PERSON><PERSON><PERSON> da<PERSON>ar jual, bakar spam, dan peroleh info terbaru melalui pemberitahuan push yang bermanfaat.", "onboardingSlide3Title": "<PERSON><PERSON><PERSON> lebih dengan <PERSON>a", "onboardingSlide3Description": "<PERSON><PERSON><PERSON>, tukar, stake, dan terima — tanpa meninggalkan dompet Anda. ", "onboardingSlide4Title": "Temukan yang terbaik dari Web3", "onboardingSlide4Description": "Temukan dan terhubung ke aplikasi dan koleksi ternama dengan peramban dalam aplikasi.", "onboardingMultichainSlide5Title": "Satu dompet untuk segalanya", "onboardingMultichainSlide5Description": "<PERSON><PERSON><PERSON>, E<PERSON><PERSON>, dan Poly<PERSON> dalam satu antarmuka ramah pengguna.", "onboardingMultichainSlide5DescriptionWithBitcoin": "<PERSON><PERSON><PERSON>, Ethereum, Polygon, dan Bitcoin dalam satu antarmuka ramah pengguna.", "requireAuth": "<PERSON><PERSON>", "requireAuthImmediately": "<PERSON><PERSON><PERSON>", "availableToSend": "Tersedia untuk Dikirim", "sendEnterAmount": "<PERSON><PERSON><PERSON><PERSON>", "sendEditMemo": "Edit Memo", "sendShowLogs": "<PERSON><PERSON><PERSON><PERSON>", "sendHideLogs": "Sembunyikan Log Kesalahan", "sendGoBack": "Kembali", "sendTransactionSuccess": "Token Anda ber<PERSON>il di<PERSON> ke", "sendInputPlaceholder": "@namapengguna atau alamat", "sendInputPlaceholderV2": "nama pengguna atau alamat", "sendPeopleTitle": "Orang", "sendDomainTitle": "Domain", "sendFollowing": "<PERSON><PERSON><PERSON><PERSON>", "sendRecentlyUsedAddressLabel": "Dipakai {{formattedTimestamp}} yang lalu", "sendRecipientAddress": "<PERSON><PERSON><PERSON>", "sendTokenInterpolated": "<PERSON>rim {{tokenSymbol}}", "sendPasteFromClipboard": "Tempel dari papan klip", "sendScanQR": "Pindai Kode QR", "sendTo": "Ke:", "sendRecipientZeroBalanceWarning": "<PERSON>amat dompet ini tidak memiliki saldo dan tidak muncul dalam riwayat transaksi terkini Anda. Pastikan alamat<PERSON> benar.", "sendUnknownAddressWarning": "Ini bukan alamat yang Anda gunakan untuk berinteraksi baru-baru ini. <PERSON><PERSON> lanjutkan dengan hati-hati.", "sendSameAddressWarning": "Ini alamat Anda saat ini. Pengiriman akan dikenakan biaya transfer tanpa perubahan saldo lainnya.", "sendMintAccountWarning": "Ini adalah alamat akun mint. Anda tidak dapat mengirim dana ke alamat ini karena akan mengakibatkan hilang permanen.", "sendCameraAccess": "<PERSON><PERSON><PERSON>", "sendCameraAccessSubtitle": "Untuk memindai kode QR, aks<PERSON> kamera harus diaktifkan. <PERSON><PERSON>ka<PERSON> Anda ingin membuka <PERSON>n sekarang?", "sendSettings": "<PERSON><PERSON><PERSON><PERSON>", "sendOK": "<PERSON>e", "invalidQRCode": "Kode QR ini tidak valid.", "sendInvalidQRCode": "Kode QR ini bukan alamat yang valid", "sendInvalidQRCodeSubtitle": "Coba lagi atau coba kode QR lain.", "sendInvalidQRCodeSplToken": "Token dalam kode QR tidak valid", "sendInvalidQRCodeSplTokenSubtitle": "Kode QR ini mengandung token yang tidak Anda miliki atau tidak dapat kami identifikasi.", "sendScanAddressToSend": "Pindai alamat {{tokenSymbol}} untuk mengirim dana", "sendScanAddressToSendNoSymbol": "<PERSON><PERSON><PERSON> alamat untuk mengirim dana", "sendScanAddressToSendCollectible": "Pindai alamat SOL untuk mengirim collectible", "sendScanAddressToSendCollectibleMultichain": "Pindai alamat untuk mengirim collectible", "sendSummary": "<PERSON><PERSON><PERSON><PERSON>", "sendUndefinedToken": "<PERSON><PERSON>, tidak dapat mengirim token ini", "sendNoTokens": "Tidak ada token yang tersedia", "noBuyOptionsAvailableInCountry": "Tidak ada opsi Beli yang tersedia di negara Anda", "swapAvailableTokenDisclaimer": "Token dalam jumlah terbatas tersedia untuk menjembatani antar Jaringan", "swapCrossSwapNetworkTooltipTitle": "Pertukaran Lintas Jaringan", "swapCrossSwapNetworkTooltipDescription": "Saat bertukar lintas <PERSON>, se<PERSON><PERSON><PERSON> gunakan token yang tersedia untuk harga terendah dan transaksi tercepat.", "settingsAbout": "Tentang Phantom", "settingsShareAppWithFriends": "Undang teman Anda", "settingsConfirm": "Ya", "settingsMakeSureNoOneIsWatching": "Pastikan tidak ada orang yang melihat layar Anda", "settingsManageAccounts": "<PERSON><PERSON><PERSON>", "settingsPrompt": "<PERSON><PERSON><PERSON><PERSON> Anda yakin ingin melanjutkan?", "settingsSelectAvatar": "<PERSON><PERSON><PERSON>", "settingsSelectSecretPhrase": "<PERSON><PERSON><PERSON>", "settingsShowPrivateKey": "Ketuk untuk melihat kunci privat Anda", "settingsShowRecoveryPhrase": "Ketuk untuk melihat frasa rahasia Anda", "settingsSubmitBetaFeedback": "<PERSON><PERSON>", "settingsUpdateAccountNameToast": "<PERSON><PERSON> akun <PERSON>ui", "settingsUpdateAvatarToast": "Avatar <PERSON>er<PERSON>", "settingsUpdateAvatarToastFailure": "<PERSON>l me<PERSON>barui Avatar!", "settingsWalletAddress": "<PERSON><PERSON><PERSON>", "settingsWalletAddresses": "<PERSON><PERSON><PERSON>", "settingsWalletNamePrimary": "<PERSON><PERSON>", "settingsPlaceholderName": "<PERSON><PERSON>", "settingsWalletNameSecondary": "Ubah nama dompet Anda", "settingsYourAccounts": "<PERSON><PERSON><PERSON>", "settingsYourAccountsMultiChain": "Multi-chain", "settingsReportUser": "Laporkan Pengguna", "settingsNotifications": "Pemberitahuan", "settingsNotificationPreferences": "Preferensi Pemberitahuan", "pushNotificationsPreferencesAllowNotifications": "Izinkan Pemberitahuan", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON>", "pushNotificationsPreferencesSentTokensDescription": "Transfer keluar token dan NFT", "pushNotificationsPreferencesReceivedTokens": "Token <PERSON>", "pushNotificationsPreferencesReceivedTokensDescription": "Transfer masuk token dan NFT", "pushNotificationsPreferencesDexSwap": "<PERSON><PERSON><PERSON>", "pushNotificationsPreferencesDexSwapDescription": "<PERSON><PERSON>ran pada aplikasi yang dikenal", "pushNotificationsPreferencesOtherBalanceChanges": "<PERSON><PERSON><PERSON>", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Transaksi multi-token lainnya yang berdampak pada saldo <PERSON>a", "pushNotificationsPreferencesPhantomMarketing": "Pembaruan dari Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "<PERSON><PERSON><PERSON> fitur dan pembaruan umum", "pushNotificationsPreferencesDescription": "Pengaturan ini mengontrol pemberitahuan push untuk dompet aktif ini. Setiap dompet memiliki pengaturan pemberitahuannya sendiri. Untuk mematikan semua pemberitahuan push Phantom, buka <1>pengaturan perangkat</1> <PERSON><PERSON>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Tidak dapat menyinkronkan preferensi pemberitahuan.", "connectSeedVaultConnectSeed": "Hubungkan Seed", "connectSeedVaultConnectSeedDescription": "Hubungkan Phantom dengan Seed Vault di ponsel Anda", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON><PERSON> akun", "connectSeedVaultSelectASeed": "<PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeedDescription": "Pilih seed yang ingin Anda hubungkan dengan Phantom", "connectSeedVaultSelectAnAccountDescription": "<PERSON><PERSON><PERSON> akun guna disiapkan untuk Phantom", "connectSeedVaultNoAccountsFound": "Tidak ada akun yang di<PERSON>ukan.", "connectSeedVaultSelectAccounts": "<PERSON><PERSON><PERSON> akun", "connectSeedVaultSelectAccountsDescription": "<PERSON><PERSON><PERSON> akun guna disiapkan untuk Phantom", "connectSeedVaultCompleteSetup": "Selesaikan <PERSON>yi<PERSON>n", "connectSeedVaultCompleteSetupDescription": "Beres! Jelajahi web3 dengan Phantom dan gunakan Seed Vault Anda untuk mengonfirmasi transaksi.", "connectSeedVaultConnectAnotherSeed": "Hubung<PERSON> Seed lain", "connectSeedVaultConnectAllSeedsConnected": "Semua seed terhubung", "connectSeedVaultNoSeedsConnected": "Tidak ada seed yang terhubung. Ketuk tombol di bawah ini untuk memberikan izin dari Seed <PERSON>.", "connectSeedVaultConnectAccount": "Hubungkan akun", "connectSeedVaultLoadMore": "<PERSON><PERSON> yang <PERSON>n", "connectSeedVaultNeedPermission": "<PERSON><PERSON> i<PERSON>", "connectSeedVaultNeedPermissionDescription": "Buka Pengaturan agar Phantom dapat menggunakan izin Seed Vault.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} biaya", "stakeAmount": "<PERSON><PERSON><PERSON>", "stakeAmountBalance": "<PERSON><PERSON>", "swapTopQuotes": "{{numQuotes}} <PERSON><PERSON><PERSON><PERSON>", "swapTopQuotesTitle": "<PERSON><PERSON><PERSON><PERSON>", "swapProvidersTitle": "Penyedia", "swapProvidersFee": "Biaya {{biaya}}", "swapProvidersTagRecommended": "Pengembalian Terbaik", "swapProvidersTagFastest": "Tercepat", "swapProviderEstimatedTimeHM": "{{hours}}j {{minutes}}m", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}}d", "stakeReview": "Tinjau", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "<PERSON><PERSON><PERSON>", "stakeReviewConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeReviewValidator": "Validator", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Konversi Stake Gagal", "convertStakeStatusErrorMessage": "<PERSON>ake Anda tidak dapat dikonversi ke {{poolTokenSymbol}}. Silakan coba lagi.", "convertStakeStatusLoadingTitle": "Mengonversi ke {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "<PERSON><PERSON> akan memulai proses untuk mengonversi {{stakedTokenSymbol}} Anda yang sudah stake ke {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Kon<PERSON>i ke {{poolTokenSymbol}} selesai!", "convertStakeStatusSuccessMessage": "Da<PERSON><PERSON><PERSON> hadiah tambahan dengan JitoSOL Anda <1>di sini.</1>", "convertStakeStatusConvertMore": "<PERSON>n<PERSON><PERSON>", "convertStakePendingTitle": "<PERSON><PERSON><PERSON><PERSON> stake ke {{symbol}}", "convertToJitoSOL": "Konversikan ke JitoSOL", "convertToJitoSOLInfoDescription": "Konversikan SOL Anda menjadi Jito SOL untuk mendapatkan hadiah dan berpartisipasi dalam ekosistem <PERSON>.", "convertToJitoSOLInfoTitle": "Konversikan ke JitoSOL", "convertStakeBannerTitle": "Konversikan stake Anda ke JitoSOL untuk meningkatkan hadiah hingga 15%", "convertStakeQuestBannerTitle": "Konversikan SOL yang di-stake menjadi JitoSOL dan raih hadiah. Pelajari selengkapnya.", "liquidStakeConvertInfoTitle": "Konversikan ke JitoSOL", "liquidStakeConvertInfoDescription": "Tingkatkan hadiah <PERSON>a dengan mengonversi stake SOL menjadi JitoSOL. <1>Pelajari selengkapnya</1>.", "liquidStakeConvertInfoFeature1Title": "Mengapa harus staking dengan <PERSON>?", "liquidStakeConvertInfoFeature1Description": "Setor untuk mendapatkan JitoSOL, yang berkembang seiring dengan stake Anda. Gunakan JitoSOL dalam protokol DeFi untuk meraih penghasilan tambahan. Tukar JitoSOL Anda nanti seharga jumlah awal + imbalan yang terkumpul.", "liquidStakeConvertInfoFeature2Title": "<PERSON><PERSON> rata-rata lebih tinggi", "liquidStakeConvertInfoFeature2Description": "Jito menyebar SOL Anda di antara validator terbaik dengan biaya terendah. Hadiah MEV akan semakin meningkatkan penghasilan Anda.", "liquidStakeConvertInfoFeature3Title": "<PERSON><PERSON><PERSON> jar<PERSON>", "liquidStakeConvertInfoFeature3Description": "Staking cair mengamankan Solana dengan menyebar stake ke beberapa validator, se<PERSON><PERSON> mengurangi risiko dari validator dengan waktu aktif rendah.", "liquidStakeConvertInfoSecondaryButton": "<PERSON><PERSON>", "liquidStakeStartStaking": "<PERSON><PERSON>", "liquidStakeReviewOrder": "Tinjau Order", "convertStakeAccountListPageIneligibleSectionTitle": "Akun Staking Tidak Memenuhi Syarat", "convertStakeAccountIneligibleBottomSheetTitle": "Akun Staking Tidak Memenuhi Syarat", "convertStakeAccountListPageErrorTitle": "Gagal mengambil akun stake", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON>, ter<PERSON><PERSON> k<PERSON><PERSON>han se<PERSON>ga kami tidak dapat mengambil akun stake.", "liquidStakeReviewYouPay": "<PERSON><PERSON>", "liquidStakeReviewYouReceive": "<PERSON><PERSON>", "liquidStakeReviewProvider": "Penyedia", "liquidStakeReviewNetworkFee": "<PERSON><PERSON><PERSON>", "liquidStakeReviewPageTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "liquidStakeReviewConversionFootnote": "Saat Anda men-stake token Solana demi mendapatkan JitoSOL, <PERSON><PERSON> akan menerima JitoSOL dengan jumlah yang sedikit lebih rendah. <1>Pelajari selengkapnya</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Stake pool Jito mendukung sebagian besar validator <PERSON><PERSON> yang aktif. SOL yang di-stake dari validator yang tidak didukung tidak dapat dikonversi ke JitoSOL. <PERSON><PERSON> itu, SOL yang baru di-stake membutuhkan waktu kurang lebih 2 hari agar memenuhi syarat konversi JitoSOL.", "selectAValidator": "<PERSON><PERSON><PERSON>", "validatorSelectionListTitle": "<PERSON><PERSON><PERSON>", "validatorSelectionListDescription": "<PERSON><PERSON>h validator untuk staking SOL Anda.", "stakeMethodDescription": "Dapatkan bunga dengan menggunakan token SOL Anda untuk membantu Solana berkembang. <1><PERSON><PERSON><PERSON><PERSON> lebih lanjut</1>", "stakeMethodRecommended": "<PERSON><PERSON><PERSON><PERSON>", "stakeMethodEstApy": "Est. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Staking Cair", "stakeMethodSelectionLiquidStakingDescription": "Stake SOL untuk mendapatkan hadiah lebih tinggi, membant<PERSON> men<PERSON><PERSON>, dan me<PERSON><PERSON>h JitoSOL demi mendapatkan hadiah tambahan.", "stakeMethodSelectionNativeStakingTitle": "Staking Asli", "stakeMethodSelectionNativeStakingDescription": "Stake SOL untuk menerima hadiah sekaligus membantu mengamankan <PERSON>.", "liquidStakeMintStakeSOL": "Staking SOL", "mintJitoSOLInfoPageTitle": "Memperkenalkan Staking Cair dengan <PERSON>", "mintJitoSOLFeature1Title": "Mengapa harus staking dengan <PERSON>?", "mintJitoSOLFeature1Description": "Setor untuk mendapatkan JitoSOL, yang berkembang seiring dengan stake Anda. Gunakan JitoSOL dalam protokol DeFi untuk meraih penghasilan tambahan. Tukar JitoSOL Anda nanti seharga jumlah awal + imbalan yang terkumpul.", "mintJitoSOLFeature2Title": "<PERSON><PERSON> rata-rata lebih tinggi", "mintJitoSOLFeature2Description": "Jito menyebar SOL Anda di antara validator terbaik dengan biaya terendah. Hadiah MEV akan semakin meningkatkan penghasilan Anda.", "mintJitoSOLFeature3Title": "<PERSON><PERSON><PERSON> jar<PERSON>", "mintJitoSOLFeature3Description": "Staking cair mengamankan Solana dengan menyebar stake ke beberapa validator, se<PERSON><PERSON> mengurangi risiko dari validator dengan waktu aktif rendah.", "mintLiquidStakePendingTitle": "Minting stake cair", "mintStakeStatusErrorTitle": "Minting Stake Cair <PERSON>", "mintStakeStatusErrorMessage": "Stake cair {{poolTokenSymbol}} Anda tidak dapat di-mint. Silakan coba lagi.", "mintStakeStatusSuccessTitle": "Minting stake cair {{poolTokenSymbol}} selesai!", "mintStakeStatusLoadingTitle": "Memproses minting stake cair {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "<PERSON><PERSON> akan memulai proses minting stake cair {{poolTokenSymbol}} <PERSON><PERSON>.", "mintLiquidStakeAmountDescription": "<PERSON><PERSON><PERSON> jumlah SOL yang ingin Anda stake dengan <PERSON>to", "mintLiquidStakeAmountProvider": "Penyedia", "mintLiquidStakeAmountApy": "Perk. APY", "mintLiquidStakeAmountBestPrice": "<PERSON><PERSON>", "mintLiquidStakeAmountInsufficientBalance": "Saldo tidak men<PERSON>", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} diperlukan untuk staking", "swapTooltipGotIt": "<PERSON><PERSON><PERSON>", "swapTabInsufficientFunds": "<PERSON> tidak cukup", "swapNoAssetsFound": "Tidak Ada Aset", "swapNoTokensFound": "Tak ada token yang di<PERSON>ukan", "swapConfirmationTryAgain": "Coba lagi", "swapConfirmationGoBack": "Kembali", "swapNoQuotesFound": "Pengajuan harga tidak di<PERSON>ukan", "swapNotProviderFound": "<PERSON>mi tidak dapat menemukan penyedia untuk pertukaran token ini. Cobalah token yang lain.", "swapAvailableOnMainnet": "Fitur ini hanya tersedia di Mainnet", "swapNotAvailableEVM": "Penukaran belum tersedia untuk akun EVM", "swapNotAvailableOnSelectedNetwork": "<PERSON><PERSON><PERSON> tidak tersedia pada jaringan yang dipilih", "singleChainSwapTab": "<PERSON><PERSON>", "crossChainSwapTab": "<PERSON><PERSON>", "allFilter": "<PERSON><PERSON><PERSON>", "bridgeRefuelTitle": "<PERSON><PERSON> ulang bahan bakar", "bridgeRefuelDescription": "<PERSON><PERSON><PERSON> ul<PERSON> bahan bakar memastikan Anda dapat membayar transaksi setelah Anda membuat jembatan.", "bridgeRefuelLabelBalance": "{{symbol}} Anda", "bridgeRefuelLabelReceive": "<PERSON><PERSON>", "bridgeRefuelLabelFee": "Estimasi Biaya", "bridgeRefuelDismiss": "<PERSON><PERSON><PERSON><PERSON><PERSON> tanpa <PERSON>", "bridgeRefuelEnable": "Aktif<PERSON> Is<PERSON>", "unwrapWrappedSolError": "Unwrapping gagal", "unwrapWrappedSolLoading": "Unwrapping...", "unwrapWrappedSolSuccess": "Unwrap ber<PERSON>il", "unwrapWrappedSolViewTransaction": "Li<PERSON>", "dappApprovePopupSignMessage": "<PERSON><PERSON>", "solanaPayFrom": "<PERSON><PERSON>", "solanaPayMessage": "<PERSON><PERSON>", "solanaPayNetworkFee": "<PERSON><PERSON><PERSON>", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Bayar {{item}}", "solanaPayPayNow": "<PERSON><PERSON>", "solanaPaySending": "Mengirim {{item}}", "solanaPayReceiving": "<PERSON><PERSON><PERSON> {{item}}", "solanaPayMinting": "Proses minting {{item}}", "solanaPayTransactionProcessing": "Transaksi Anda sedang diproses,\nharap tunggu.", "solanaPaySent": "Terkirim!", "solanaPayReceived": "<PERSON>terima!", "solanaPayMinted": "Minting selesai!", "solanaPaySentNFT": "NFT terkirim!", "solanaPayReceivedNFT": "NFT diterima!", "solanaPayTokensSent": "Token Anda berhasil dikirim ke {{to}}", "solanaPayTokensReceived": "Anda menerima token baru dari {{from}}", "solanaPayViewTransaction": "<PERSON><PERSON>", "solanaPayTransactionFailed": "Transaksi <PERSON>l", "solanaPayConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "solanaPayTo": "ke", "dappApproveConnectViewAccount": "<PERSON><PERSON> aku<PERSON>", "deepLinkInvalidLink": "<PERSON><PERSON> tidak valid", "deepLinkInvalidSplTokenSubtitle": "Ini mengandung token yang tidak Anda miliki atau tidak dapat kami identifikasi.", "walletAvatarShowAllAccounts": "<PERSON><PERSON><PERSON><PERSON> semua akun", "pushNotificationsGetInstantUpdates": "Dapatkan pembaruan instan", "pushNotificationsEnablePushNotifications": "Aktifkan pemberitahuan push tentang transfer yang telah se<PERSON>, pen<PERSON><PERSON>, dan pengum<PERSON>", "pushNotificationsEnable": "Aktifkan", "pushNotificationsNotNow": "<PERSON><PERSON> saja", "onboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> <1><PERSON><PERSON><PERSON><PERSON></1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OKE, sudah saya simpan", "onboardingCreateNewWallet": "Buat Dompet Baru", "onboardingErrorDuplicateSecretRecoveryPhrase": "Frasa rahasia ini sudah ada di dompet Anda", "onboardingErrorInvalidSecretRecoveryPhrase": "Frasa pemulihan rahasia tidak valid", "onboardingFinished": "Anda sudah siap!", "onboardingImportAccounts": "<PERSON><PERSON><PERSON>", "onboardingImportImportingAccounts": "Mengimpor <PERSON>...", "onboardingImportImportingFindingAccounts": "<PERSON><PERSON><PERSON> akun be<PERSON>", "onboardingImportAccountsLastActive": "Aktif {{formattedTimestamp}} yang lalu", "onboardingImportAccountsNeverUsed": "Tidak Per<PERSON>", "onboardingImportAccountsCreateNew": "<PERSON>pet baru", "onboardingImportAccountsDescription": "<PERSON><PERSON>h akun dompet untuk mengimpor", "onboardingImportReadOnlyAccountDescription": "Tambahkan alamat atau nama domain yang ingin Anda pantau. <PERSON>a akan memiliki akses hanya-lihat, se<PERSON>ga tidak akan bisa menandatangani transaksi atau pesan.", "onboardingImportSecretRecoveryPhrase": "<PERSON><PERSON><PERSON>", "onboardingImportViewAccounts": "<PERSON><PERSON>", "onboardingRestoreExistingWallet": "Pulihkan dompet yang sudah ada dengan frasa pemulihan rahasia yang terdiri dari 12 atau 24 kata", "onboardingShowUnusedAccounts": "<PERSON><PERSON><PERSON><PERSON> A<PERSON>", "onboardingShowMoreAccounts": "<PERSON><PERSON><PERSON><PERSON>", "onboardingHideUnusedAccounts": "Sembunyikan Akun Tak Terpakai", "onboardingSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingSelectAccounts": "<PERSON><PERSON><PERSON>", "onboardingStoreSecretRecoveryPhraseReminder": "In<PERSON>h satu-satunya cara untuk memulihkan akun Anda. <PERSON>p disimpan di tempat yang aman!", "useTokenMetasForMintsUnknownName": "Tidak diketahui", "timeUnitMinute": "menit", "timeUnitMinutes": "menit", "timeUnitHour": "jam", "timeUnitHours": "jam", "espNFTListWithPrice": "Anda men<PERSON> {{NFTDisplayName}} untuk dijual seharga {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTListWithPriceWithoutDApp": "<PERSON>a men<PERSON> {{NFTDisplayName}} untuk dijual seharga {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "<PERSON>a men<PERSON> {{NFTDisplayName}} untuk dijual di {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "<PERSON><PERSON> {{NFTDisplayName}} untuk dijual", "espNFTChangeListPriceWithPrice": "Anda memperbarui cantuman untuk {{NFTDisplayName}} menja<PERSON> {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Anda memperbarui cantuman untuk {{NFTDisplayName}} menja<PERSON> {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Anda memperbarui cantuman untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Anda memperbarui cantuman untuk {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "<PERSON><PERSON> men<PERSON>n {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "<PERSON><PERSON> men<PERSON>n {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "<PERSON><PERSON> menga<PERSON> tawaran untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "<PERSON><PERSON> menga<PERSON> tawaran untuk {{NFTDisplayName}}", "espNFTBidListerWithPrice": "<PERSON><PERSON><PERSON> baru seharga {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "<PERSON><PERSON><PERSON> baru seharga {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "<PERSON><PERSON><PERSON> baru untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "<PERSON><PERSON><PERSON> baru untuk {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Anda membatalkan tawaran seharga {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Anda membatalkan tawaran seharga {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Anda membatalkan tawaran untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Anda membatalkan tawaran untuk {{NFTDisplayName}}", "espNFTUnlist": "Anda batal mencantumkan {{NFTDisplayName}} untuk dijual di {{dAppName}}", "espNFTUnlistWithoutDApp": "<PERSON>a batal mencantumkan {{NFTDisplayName}} untuk dijual", "espNFTBuyBuyerWithPrice": "Anda membeli {{NFTDisplayName}} seharga {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Anda membeli {{NFTDisplayName}} seharga {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "<PERSON><PERSON> membeli {{NFTDisplayName}} untuk dijual di {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "<PERSON><PERSON> me<PERSON> {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "<PERSON><PERSON> telah menjual {{NFTDisplayName}} se<PERSON>ga {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "<PERSON><PERSON> telah menjual {{NFTDisplayName}} se<PERSON>ga {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "<PERSON>a telah menjual {{NFTDisplayName}} di {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "<PERSON><PERSON> telah men<PERSON> {{NFTDisplayName}}", "espDEXSwap": "<PERSON><PERSON> menu<PERSON>kan {{downTokensTextFragment}} dengan {{upTokensTextFragment}} di {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Anda menyetor {{downTokensTextFragment}} ke liquidity pool {{poolDisplayName}} di {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "<PERSON><PERSON> menu<PERSON>kan {{downTokensTextFragment}} dengan {{upTokensTextFragment}} di {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Anda menarik {{upTokensTextFragment}} dari liquidity pool {{poolDisplayName}} di {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "<PERSON><PERSON> menu<PERSON>kan {{downTokensTextFragment}} dengan {{upTokensTextFragment}} di {{dAppName}}", "espGenericTokenSend": "Anda telah men<PERSON>m {{downTokensTextFragment}}", "espGenericTokenReceive": "Anda men<PERSON> {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "<PERSON><PERSON> {{downTokensTextFragment}} dengan {{upTokensTextFragment}}", "espUnknown": "TAK DIKETAHUI", "espUnknownNFT": "NFT tak diketahui", "espTextFragmentAnd": "dan", "externalLinkWarningTitle": "<PERSON><PERSON> akan keluar dari <PERSON>", "externalLinkWarningDescription": "<PERSON> buka {{url}}. Pastikan Anda percaya dengan sumber ini sebelum berinteraksi.", "shortcutsWarningDescription": "<PERSON><PERSON><PERSON> disediakan oleh {{url}}. Pastikan Anda percaya dengan sumber ini sebelum berinteraksi.", "lowTpsBanner": "Solana sedang mengalami kemacetan jaringan", "lowTpsMessageTitle": "<PERSON><PERSON><PERSON><PERSON>", "lowTpsMessage": "<PERSON><PERSON> men<PERSON> kema<PERSON>tan berat, transaksi <PERSON>a mungkin gagal atau tertunda. Harap coba lagi transaksi yang gagal.", "solanaSlow": "Tidak se<PERSON>, <PERSON><PERSON><PERSON>", "solanaNetworkTemporarilyDown": "Jaringan <PERSON>ana sedang tidak beroperasi sementara", "waitForNetworkRestart": "<PERSON><PERSON> tunggu jaringan dimulai ulang. <PERSON> tidak akan terdampak.", "exploreCollectionsCarouselTitle": "<PERSON><PERSON> yang Populer", "exploreDropsCarouselTitle": "<PERSON><PERSON> yang <PERSON>", "exploreSortFloor": "<PERSON><PERSON>", "exploreSortListed": "Tercantum", "exploreSortVolume": "Volume", "exploreFetchErrorSubtitle": "<PERSON>hon coba lagi nanti.", "exploreFetchErrorTitle": "<PERSON><PERSON>.", "exploreTopCollectionsTitle": "Koleksi NFT Teratas", "exploreTopListLess": "Kurangi", "exploreTopListMore": "<PERSON><PERSON><PERSON>", "exploreSeeMore": "Selengkapnya", "exploreTrendingTokens": "Token yang Sedang Tren", "exploreVolumeTokens": "Volume Tertinggi", "explorePriceChangeTokens": "<PERSON><PERSON><PERSON>", "explorePriceTokens": "<PERSON><PERSON>", "exploreMarketCapTokens": "To<PERSON>", "exploreTrendingSites": "Situs yang Sedang Tren", "exploreTopSites": "<PERSON><PERSON>", "exploreTrendingCollections": "Ko<PERSON><PERSON><PERSON> yang Sedang Tren", "exploreTopCollections": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchCollectionsSection": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchItemsSection": "<PERSON><PERSON>", "collectiblesSearchNrOfItems": "{{ nrOfItems }} Item", "collectiblesSearchPlaceholderText": "Cari di koleksi Anda", "collectionPinSuccess": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "collectionPinFail": "<PERSON><PERSON><PERSON><PERSON> gagal disematkan", "collectionUnpinSuccess": "<PERSON><PERSON><PERSON><PERSON> batal disematkan", "collectionUnpinFail": "<PERSON><PERSON><PERSON><PERSON> gagal batal disematkan", "collectionHideSuccess": "<PERSON><PERSON><PERSON><PERSON> disembunyikan", "collectionHideFail": "<PERSON><PERSON><PERSON><PERSON> gagal disembunyikan", "collectionUnhideSuccess": "<PERSON><PERSON><PERSON><PERSON>", "collectionUnhideFail": "<PERSON><PERSON><PERSON><PERSON> gagal dip<PERSON>", "collectiblesSpamSuccess": "Dilaporkan sebagai spam", "collectiblesSpamFail": "Gagal melaporkan sebagai spam", "collectiblesSpamAndHiddenSuccess": "Dilaporkan sebagai spam dan disembunyikan", "collectiblesNotSpamSuccess": "Dilaporkan sebagai bukan spam", "collectiblesNotSpamFail": "Gagal melaporkan sebagai bukan spam", "collectiblesNotSpamAndUnhiddenSuccess": "Dilaporkan sebagai bukan spam dan batal disembunyikan", "tokenPageSpamWarning": "Token ini belum diverifikasi. Berinteraksilah dengan token yang Anda percayai saja.", "tokenSpamWarning": "Token ini disembunyikan karena Phantom meyakini ini spam.", "collectibleSpamWarning": "Collectible ini disembunyikan karena Phantom meyakini ini spam.", "collectionSpamWarning": "Collectible ini disembunyikan karena Phantom meyakini ini spam.", "emojiNoResults": "Emoji tidak di<PERSON>n", "emojiSearchResults": "<PERSON><PERSON>", "emojiSuggested": "<PERSON><PERSON><PERSON><PERSON>", "emojiSmileys": "Smiley & Orang", "emojiAnimals": "Binatang & Alam", "emojiFood": "Makanan & Minuman", "emojiTravel": "Perjalanan & Tempat", "emojiActivities": "Aktivitas", "emojiObjects": "<PERSON><PERSON>", "emojiSymbols": "Simbol", "emojiFlags": "<PERSON><PERSON>", "whichExtensionToConnectWith": "<PERSON>ks<PERSON>i mana yang ingin Anda hubungkan?", "configureInSettings": "Dapat dikonfigurasi di Pengaturan → Dompet Aplikasi Default.", "continueWith": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "useMetaMask": "<PERSON><PERSON><PERSON>", "usePhantom": "Gunakan <PERSON>", "alwaysAsk": "<PERSON><PERSON><PERSON>", "dontAskMeAgain": "<PERSON>an tanya lagi", "selectWalletSettingDescriptionLine1": "Beberapa aplikasi mungkin tidak menawarkan opsi untuk terhubung dengan Phantom.", "selectWalletSettingDescriptionLinePhantom": "<PERSON><PERSON><PERSON><PERSON>, setiap kali <PERSON>a terhubung dengan MetaMask maka Phantom akan terbuka.", "selectWalletSettingDescriptionLineAlwaysAsk": "<PERSON><PERSON><PERSON><PERSON>, jika <PERSON>a <PERSON> dengan <PERSON>, kami akan bertanya apakah <PERSON>a ingin menggunakan <PERSON> saja.", "selectWalletSettingDescriptionLineMetaMask": "Jika MetaMask ditetapkan sebagai default, maka dapp-dapp tersebut tidak akan terhubung dengan Phantom.", "metaMaskOverride": "Dompet Aplikasi Default", "metaMaskOverrideSettingDescriptionLine1": "Untuk terhubung dengan situs web yang tidak menawarkan opsi menggunakan Phantom.", "refreshAndReconnectToast": "Segarkan dan sambungkan ulang untuk menerapkan perubahan", "autoConfirmUnavailable": "Tidak tersedia", "autoConfirmReasonDappNotWhitelisted": "Tidak tersedia karena kontrak asalnya tidak ada dalam daftar izin kami untuk aplikasi ini.", "autoConfirmReasonSessionNotActive": "Tidak tersedia karena tidak ada sesi konfirmasi otomatis yang aktif. Harap aktifkan di bawah.", "autoConfirmReasonRateLimited": "Tidak tersedia karena dapp yang Anda gunakan mengirim terlalu banyak permintaan.", "autoConfirmReasonUnsupportedNetwork": "Tidak tersedia karena konfirmasi otomatis belum mendukung jaringan ini.", "autoConfirmReasonSimulationFailed": "Tidak tersedia karena kami tidak dapat menjamin keamanan.", "autoConfirmReasonTabNotFocused": "Tidak tersedia karena tab domain yang Anda coba konfirmasi otomatis tidak aktif.", "autoConfirmReasonNotUnlocked": "Tidak tersedia karena dompet belum dibuka kuncinya.", "rpcErrorUnauthorizedWrongAccount": "Transaksi dari alamat tidak sesuai dengan alamat akun yang dipilih.", "rpcErrorUnauthorizedUnknownSource": "Sumber permintaan RPC tidak dapat ditentukan.", "transactionsDisabledTitle": "Transaksi dinonaktifkan", "transactionsDisabledMessage": "<PERSON><PERSON><PERSON> Anda tidak dapat bertransaksi menggunakan Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Aktif", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL tersalin ke papan klip", "notEnoughSolScanTransactionWarning": "Transaksi ini mungkin gagal karena SOL di akun Anda tidak mencukupi. Tambahkan lebih banyak SOL ke akun Anda dan coba lagi.", "transactionRevertedWarning": "Transaksi ini dibatalkan selama simulasi. <PERSON> mungkin hilang jika transaksi diajukan.", "slippageToleranceExceeded": "Transaksi ini dibatalkan selama simulasi. Toleransi selip te<PERSON>.", "simulationWarningKnownMalicious": "<PERSON>mi meyakini akun ini berbahaya. Persetujuan dapat mengakibatkan kehilangan dana.", "simulationWarningPoisonedAddress": "<PERSON><PERSON>t ini mencurigakan karena mirip dengan alamat yang baru-baru ini Anda kirimi dana. <PERSON><PERSON> konfirmasi bahwa ini adalah alamat yang benar untuk mencegah hilangnya dana akibat penipuan.", "simulationWarningInteractingWithAccountWithoutActivity": "Akun ini belum pernah melakukan aktivitas. Mengirim dana ke akun yang tidak ada dapat mengakibatkan hilangnya dana.", "quests": "<PERSON><PERSON>", "questsClaimInProgress": "K<PERSON>m sedang diproses", "questsVerifyingCompletion": "Memverifikasi pen<PERSON> misi", "questsClaimError": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat meng<PERSON><PERSON> hadiah", "questsClaimErrorDescription": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han saat meng<PERSON><PERSON> hadiah <PERSON>. <PERSON><PERSON> coba lagi nanti.", "questsBadgeMobileOnly": "<PERSON><PERSON><PERSON>", "questsBadgeExtensionOnly": "<PERSON><PERSON><PERSON>", "questsExplainerSheetButtonLabel": "<PERSON><PERSON><PERSON>", "questsNoQuestsAvailable": "Tidak ada misi yang tersedia", "questsNoQuestsAvailableDescription": "Saat ini tidak ada misi yang tersedia. Kami akan memberi tahu Anda segera setelah misi baru ditambahkan.", "exploreLearn": "<PERSON><PERSON><PERSON><PERSON>", "exploreSites": "Situs", "exploreTokens": "Token", "exploreQuests": "<PERSON><PERSON>", "exploreCollections": "<PERSON><PERSON><PERSON><PERSON>", "exploreFilterByall_networks": "<PERSON><PERSON><PERSON>", "exploreSortByrank": "Sedang Tren", "exploreSortBytrending": "Sedang Tren", "exploreSortByprice": "<PERSON><PERSON>", "exploreSortByprice-change": "<PERSON><PERSON><PERSON>", "exploreSortBytop": "Terata<PERSON>", "exploreSortByvolume": "Volume", "exploreSortBygainers": "<PERSON>", "exploreSortBylosers": "<PERSON>", "exploreSortBymarket-cap": "Batas Pasar", "exploreSortBymarket_cap": "Batas Pasar", "exploreTimeFrame1h": "1 jam", "exploreTimeFrame24h": "24 jam", "exploreTimeFrame7d": "7 hari", "exploreTimeFrame30d": "30 hari", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "<PERSON><PERSON>", "exploreCategoryMarketplace": "Marketplace", "exploreCategoryGaming": "Gaming", "exploreCategoryBridges": "Jembatan", "exploreCategoryOther": "<PERSON><PERSON><PERSON>", "exploreCategorySocial": "Sosial", "exploreCategoryCommunity": "Komunitas", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Staking", "exploreCategoryArt": "<PERSON><PERSON>", "exploreCategoryTools": "Peralatan", "exploreCategoryDeveloperTools": "Peralatan Pengembang", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Staking NFT", "exploreCategoryExplorer": "Explorer", "exploreCategoryInscriptions": "Inskripsi", "exploreCategoryBridge": "Jembatan", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Pemeriksa Airdrop", "exploreCategoryPoints": "Poin", "exploreCategoryQuests": "<PERSON><PERSON>", "exploreCategoryShop": "<PERSON><PERSON>", "exploreCategoryProtocol": "Protokol", "exploreCategoryNamingService": "<PERSON><PERSON><PERSON>", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Pelacak Portofolio", "exploreCategoryFitness": "Kebugara<PERSON>", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volume", "exploreFloor": "<PERSON><PERSON>", "exploreCap": "Batas Pasar", "exploreToken": "Token", "explorePrice": "<PERSON><PERSON>", "explore24hVolume": "Volume 24 jam", "exploreErrorButtonText": "<PERSON><PERSON>", "exploreErrorDescription": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba memuat konten jelajah. Segarkan dan coba lagi.", "exploreErrorTitle": "<PERSON>l memuat konten j<PERSON>jah", "exploreNetworkError": "<PERSON> kes<PERSON>han jar<PERSON>n. <PERSON><PERSON>ah lagi nanti.", "exploreTokensLegalDisclaimer": "Daftar token dibuat menggunakan data pasar yang disediakan oleh berbagai penyedia pihak ketiga termasuk CoinGecko, Birdeye, dan <PERSON>. Ki<PERSON>ja didasarkan pada periode 24 jam sebelumnya. Ki<PERSON>ja masa lalu bukan merupakan indikasi kinerja masa mendatang.", "swapperTokensLegalDisclaimer": "Daftar token yang sedang tren dibuat menggunakan data pasar dari berbagai penyedia pihak ketiga termasuk CoinGecko, Bird<PERSON>, dan <PERSON> dan berdasarkan token populer yang ditukarkan oleh pengguna Phantom melalui Penukar selama periode waktu yang ditentukan. Kinerja masa lalu bukan merupakan indikasi kinerja masa depan.", "exploreLearnErrorTitle": "<PERSON><PERSON> memuat konten be<PERSON>jar", "exploreLearnErrorDescription": "<PERSON><PERSON><PERSON><PERSON> kes<PERSON>han saat mencoba memuat konten belajar. Segarkan dan coba lagi.", "exploreShowMore": "Selengkapnya", "exploreShowLess": "<PERSON><PERSON><PERSON><PERSON>", "exploreVisitSite": "<PERSON><PERSON><PERSON><PERSON>", "dappBrowserSearchScreenVisitSite": "<PERSON><PERSON><PERSON><PERSON> situs", "dappBrowserSearchScreenSearchWithGoogle": "<PERSON><PERSON>", "dappBrowserSearchScreenSearchLinkYouCopied": "<PERSON><PERSON> yang <PERSON>", "dappBrowserExtSearchPlaceholder": "Cari situs, token", "dappBrowserSearchNoAppsTokens": "Aplikasi atau token tidak ditemukan", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON><PERSON><PERSON>?", "dappBrowserTabsLimitExceededScreenDescription": "Ada {{tabsCount}} tab yang terbuka. Untuk membuka lebih banyak, <PERSON><PERSON> harus menutup beberapa tab.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON><PERSON>", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: Domain ini tidak ada", "dappBrowserTabErrorHttp": "<PERSON><PERSON><PERSON><PERSON><PERSON>, silakan gunakan HTTPS", "dappBrowserTabError401Unauthorized": "401 Tidak diizinkan", "dappBrowserTabError501UnhandledRequest": "501 Permintaan tak tertangani", "dappBrowserTabErrorTimeout": "HABIS WAKTU: Server memer<PERSON>an waktu terlalu lama untuk merespons", "dappBrowserTabErrorInvalidResponse": "Respons tidak valid", "dappBrowserTabErrorEmptyResponse": "Respons kosong", "dappBrowserTabErrorGeneric": "<PERSON><PERSON><PERSON><PERSON>", "localizedErrorUnknownError": "<PERSON> masalah. Mohon coba lagi nanti.", "localizedErrorUnsupportedCountry": "<PERSON><PERSON>, negara Anda saat ini tidak didukung.", "localizedErrorTokensNotLoading": "Ada masalah saat memuat token Anda. Mohon coba lagi.", "localizedErrorSwapperNoQuotes": "Tidak ada penukaran yang tersedia akibat pasangan tidak didukung, likuiditas rendah, atau jumlah rendah. Coba sesuaikan token atau jumlah.", "localizedErrorSwapperRefuelNoQuotes": "Pengajuan harga tidak ditemukan. Coba token atau jumlah lain, atau nonaktifkan isi ulang bahan bakar.", "localizedErrorInsufficientSellAmount": "Jumlah token terlalu rendah. Tingkatkan nilai untuk bertukar <PERSON>.", "localizedErrorCrossChainUnavailable": "Penukaran lintas chain saat ini tidak tersedia. Cobalah lagi nanti.", "localizedErrorTokenNotTradable": "Salah satu token yang dipilih tidak dapat diperdagangkan. <PERSON><PERSON> pilih token lain.", "localizedErrorCollectibleLocked": "Akun token terkunci.", "localizedErrorCollectibleListed": "Akun token dicantumkan.", "spamActivityAction": "Lihat item tersembunyi", "spamActivityTitle": "Aktivitas Tersembunyi", "spamActivityWarning": "Transaksi ini disembunyikan karena Phantom meyakini ini mungkin spam.", "appAuthenticationFailed": "<PERSON><PERSON>", "appAuthenticationFailedDescription": "<PERSON> masalah dalam proses autentikasi <PERSON>. Harap coba lagi.", "partialErrorBalanceChainName": "<PERSON><PERSON> men<PERSON> masalah saat memperbarui saldo {{chainName}} <PERSON><PERSON>. <PERSON> aman.", "partialErrorGeneric": "<PERSON><PERSON> mengalami masalah saat memperbarui jaringan. Sebagian saldo dan harga token Anda mungkin sudah kedal<PERSON>. <PERSON> aman.", "partialErrorTokenDetail": "<PERSON><PERSON> men<PERSON>ami masalah saat memperbarui saldo token <PERSON><PERSON>. <PERSON> aman.", "partialErrorTokenPrices": "<PERSON><PERSON> men<PERSON>ami masalah saat memperbarui harga token <PERSON>a. <PERSON> aman.", "partialErrorTokensTrimmed": "<PERSON><PERSON> mengalami masalah saat menampilkan semua token dalam portofolio Anda. <PERSON> aman.", "publicFungibleDetailAbout": "Tentang", "publicFungibleDetailYourBalance": "<PERSON><PERSON>", "publicFungibleDetailInfo": "Info", "publicFungibleDetailShowMore": "Selengkapnya", "publicFungibleDetailShowLess": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailPerformance": "Performa 24 jam", "publicFungibleDetailSecurity": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMarketCap": "Batas Pasar", "publicFungibleDetailTotalSupply": "Suplai Total", "publicFungibleDetailCirculatingSupply": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMaxSupply": "<PERSON><PERSON><PERSON>.", "publicFungibleDetailHolders": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailVolume": "Volume", "publicFungibleDetailTrades": "Perdagangan", "publicFungibleDetailTraders": "Trader", "publicFungibleDetailUniqueWallets": "Dompet Unik", "publicFungibleDetailTop10Holders": "10 P<PERSON>egang <PERSON>", "publicFungibleDetailTop10HoldersTooltip": "Menunjukkan persentase total suplai saat ini yang dimiliki oleh 10 pemegang token teratas. Ini adalah ukuran seberapa mudah harga dapat dimanipulasi.", "publicFungibleDetailMintable": "Dapat Di-mint", "publicFungibleDetailMintableTooltip": "<PERSON><PERSON><PERSON> token dapat ditingkatkan oleh pemilik kontrak jika token dapat di-mint.", "publicFungibleDetailMutableInfo": "Info yang <PERSON>", "publicFungibleDetailMutableInfoTooltip": "Jika info token seperti nama, logo, dan alamat situs web dapat diubah, maka pemilik kontrak dapat mengubahnya.", "publicFungibleDetailOwnershipRenounced": "Ke<PERSON><PERSON>lik<PERSON>", "publicFungibleDetailOwnershipRenouncedTooltip": "Jika kepemilikan token dilepaskan, tidak ada yang dapat menjalankan fungsi seperti minting lebih banyak token.", "publicFungibleDetailUpdateAuthority": "<PERSON><PERSON><PERSON>", "publicFungibleDetailUpdateAuthorityTooltip": "Otoritas pembaruan adalah alamat dompet yang dapat mengubah informasi jika token dapat diubah.", "publicFungibleDetailFreezeAuthority": "Otoritas Pembekuan", "publicFungibleDetailFreezeAuthorityTooltip": "Otoritas pembekuan adalah alamat dompet yang dapat mencegah transfer dana.", "publicFungibleUnverifiedToken": "Token ini belum diverifikasi. Berinteraksilah dengan token yang Anda percayai saja.", "publicFungibleDetailSwap": "<PERSON><PERSON> {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "<PERSON><PERSON> {{tokenSymbol}} dengan aplikasi Phantom", "publicFungibleDetailLinkCopied": "<PERSON><PERSON><PERSON> ke papan klip", "publicFungibleDetailContract": "Kontrak", "publicFungibleDetailMint": "Mint", "unifiedTokenDetailTransactionActivity": "Aktivitas", "unifiedTokenDetailSeeMoreTransactionActivity": "Selengkapnya", "unifiedTokenDetailTransactionActivityError": "Gagal memuat aktivitas terkini", "additionalNetworksTitle": "<PERSON><PERSON><PERSON>", "copyAddressRowAdditionalNetworks": "<PERSON><PERSON><PERSON>", "copyAddressRowAdditionalNetworksHeader": "Jaringan berikut menggunakan alamat yang sama dengan Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Anda dapat menggunakan alamat Ethereum dengan aman untuk mengirim dan menerima aset di salah satu jaringan ini.", "cpeUnknownError": "Kesalahan tak dikenal", "cpeUnknownInstructionError": "Kesalahan instruksi tak dikenal", "cpeAccountFrozen": "<PERSON><PERSON><PERSON>", "cpeAssetFrozen": "<PERSON><PERSON>", "cpeInsufficientFunds": "<PERSON> tidak cukup", "cpeInvalidAuthority": "Otoritas tidak sah", "cpeBalanceBelowRent": "<PERSON><PERSON> di bawah ambang batas bebas sewa", "cpeNotApprovedForConfidentialTransfers": "Akun tidak disetujui untuk transfer rahasia", "cpeNotAcceptingDepositsOrTransfers": "<PERSON>kun tidak men<PERSON>ma setoran atau transfer", "cpeNoMemoButRequired": "Tidak ada memo dalam instruksi sebelumnya; diperlukan agar penerima dapat menerima transfer", "cpeTransferDisabledForMint": "Transfer dinonaktifkan untuk mint ini", "cpeDepositAmountExceedsLimit": "<PERSON><PERSON><PERSON> set<PERSON> mele<PERSON>hi batas maksimum", "cpeInsufficientFundsForRent": "Dana tidak cukup untuk menyewa", "reportIssueScreenTitle": "Laporkan Masalah", "publicFungibleReportIssuePrompt": "Ma<PERSON>ah apa yang ingin Anda laporkan tentang {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "Informasi <PERSON>", "publicFungibleReportIssuePriceStale": "<PERSON><PERSON> tidak diper<PERSON>ui", "publicFungibleReportIssuePriceMissing": "<PERSON>rga tidak ada", "publicFungibleReportIssuePerformanceIncorrect": "Performa 24 jam salah", "publicFungibleReportIssueLinkBroken": "Tautan medsos tidak dapat dijangkau", "publicFungibleDetailErrorLoading": "Data token tidak tersedia", "reportUserPrompt": "Ma<PERSON><PERSON> apa yang ingin Anda laporkan tentang @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "<PERSON><PERSON><PERSON> dan <PERSON>", "reportUserOptionAbuseAndHarrassmentDescription": "<PERSON><PERSON><PERSON><PERSON> tertarget, hasutan per<PERSON>, an<PERSON><PERSON> kek<PERSON>, konten dan sebutan yang mengandung keben<PERSON>", "reportUserOptionPrivacyAndImpersonationTitle": "Privas<PERSON> dan <PERSON>", "reportUserOptionPrivacyAndImpersonationDescription": "Berbagi atau mengancam untuk mengungkapkan informasi pribadi, berpura-pura menjadi orang lain", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON> p<PERSON>, pen<PERSON>uan, tautan berb<PERSON>", "reportUserSuccess": "Laporan Pengguna Telah Dikirim.", "settingsClaimUsernameTitle": "Buat Nama Pengguna", "settingsClaimUsernameDescription": "Identitas unik seunik dompet Anda", "settingsClaimUsernameValueProp1": "Identitas yang Disederhanakan", "settingsClaimUsernameValueProp1Description": "Ucapkan selamat tinggal kepada alamat yang panjang dan rumit dan sambutlah identitas yang mudah digunakan", "settingsClaimUsernameValueProp2": "Lebih Cepat & Mudah", "settingsClaimUsernameValueProp2Description": "<PERSON><PERSON> dan terima kripto dengan mudah, masuk ke dompet <PERSON>, dan terhu<PERSON>ng dengan teman", "settingsClaimUsernameValueProp3": "Tetap Te<PERSON>inkron", "settingsClaimUsernameValueProp3Description": "Hubungkan akun apa pun ke nama pengguna <PERSON>, maka akan tersinkron di semua perangkat <PERSON>a", "settingsClaimUsernameHelperText": "Nama unik untuk Akun Phantom Anda", "settingsClaimUsernameValidationDefault": "<PERSON>a pengguna ini tidak dapat diubah lagi nanti", "settingsClaimUsernameValidationAvailable": "<PERSON><PERSON> pengguna tersedia", "settingsClaimUsernameValidationUnavailable": "<PERSON>a pengguna tidak tersedia", "settingsClaimUsernameValidationServerError": "Tidak dapat memeriksa apakah nama pengguna tersedia. Cobalah lagi nanti.", "settingsClaimUsernameValidationErrorLine1": "Nama pengguna tidak valid.", "settingsClaimUsernameValidationErrorLine2": "<PERSON>a pengguna harus terdiri dari {{minChar}} hingga {{maxChar}} karakter dan hanya boleh berisi huruf dan angka.", "settingsClaimUsernameValidationLoading": "Memeriksa apakah nama pengguna ini tersedia...", "settingsClaimUsernameSaveAndContinue": "Simpan & Lanjutkan", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameAnonymousAuthTitle": "Otorisasi <PERSON>", "settingsClaimUsernameAnonymousAuthDescription": "<PERSON><PERSON><PERSON> ke Akun Phantom <PERSON>a secara anonim dengan tanda tangan", "settingsClaimUsernameAnonymousAuthBadge": "<PERSON><PERSON><PERSON><PERSON> cara kerjanya", "settingsClaimUsernameLinkWalletsTitle": "Tautkan dompet Anda", "settingsClaimUsernameLinkWalletsDescription": "<PERSON><PERSON><PERSON> dompet yang muncul di perangkat lain dengan nama pengguna <PERSON>a", "settingsClaimUsernameLinkWalletsBadge": "Tidak Dapat Dilihat Publik", "settingsClaimUsernameConnectAccountsTitle": "Hubungkan Akun", "settingsClaimUsernameConnectAccountsHelperText": "<PERSON><PERSON><PERSON> alamat rantai akan terhubung dengan nama pengguna Anda. <PERSON>a dapat mengu<PERSON>ya nanti.", "settingsClaimUsernameContinue": "Lanjutkan", "settingsClaimUsernameCreateUsername": "Buat Nama Pengguna", "settingsClaimUsernameCreating": "Membuat nama pengguna...", "settingsClaimUsernameSuccess": "Nama Pengguna Dibuat!", "settingsClaimUsernameError": "<PERSON><PERSON> menemui kesalahan saat membuat nama pengguna", "settingsClaimUsernameTryAgain": "<PERSON><PERSON>", "settingsClaimUsernameSuccessHelperText": "<PERSON>a sekarang dapat menggunakan nama pengguna baru di semua dompet Phantom Anda", "settingsClaimUsernameSettingsSyncedTitle": "Pengaturan Te<PERSON>ink<PERSON>", "settingsClaimUsernameSettingsSyncedHelperText": "Ucapkan selamat tinggal kepada alamat yang panjang dan rumit dan sambutlah identitas yang mudah digunakan", "settingsClaimUsernameSendToUsernameTitle": "<PERSON><PERSON> ke <PERSON>", "settingsClaimUsernameSendToUsernameHelperText": "<PERSON><PERSON> dan terima kripto dengan mudah, masuk ke dompet <PERSON>, dan terhu<PERSON>ng dengan teman", "settingsClaimUsernameManageAddressesTitle": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameManageAddressesHelperText": "Token atau barang koleksi apa pun yang dikirim ke nama pengguna Anda akan dikirimkan ke alamat ini", "settingsClaimUsernameManageAddressesBadge": "Dapat <PERSON>hat Publik", "settingsClaimUsernameEditAddressesTitle": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameEditAddressesHelperText": "Token atau barang koleksi apa pun yang dikirim ke nama pengguna Anda akan dikirimkan ke alamat ini. Pilih satu alamat per chain.", "settingsClaimUsernameEditAddressesError": "<PERSON>ya satu alamat per jaringan yang diperbolehkan.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON>", "settingsClaimUsernameNoAddressesSaved": "Tidak ada alamat publik yang disimpan", "settingsClaimUsernameSave": "Simpan", "settingsClaimUsernameDone": "Se<PERSON><PERSON>", "settingsClaimUsernameWatching": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} <PERSON><PERSON>n", "settingsClaimUsernameNoOfAccountsSingular": "1 Akun", "settingsClaimUsernameEmptyAccounts": "Tidak Ada Akun", "settingsClaimUsernameSettingTitle": "Buat @namapengguna Anda", "settingsClaimUsernameSettingDescription": "Identitas unik untuk dompet Anda", "settingsManageUserProfileAbout": "Tentang", "settingsManageUserProfileAboutUsername": "<PERSON><PERSON>", "settingsManageUserProfileAboutBio": "Biodata", "settingsManageUserProfileTitle": "<PERSON><PERSON>la Profil", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactorsDescription": "<PERSON><PERSON><PERSON> frasa seed atau kunci privat mana yang dapat masuk ke Akun Phantom Anda.", "settingsManageUserProfileUpdateAuthFactorsToast": "Faktor autentikasi diperbarui!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "<PERSON>l memperbarui faktor autenti<PERSON>!", "settingsManageUserProfileBiography": "Edit Biodata", "settingsManageUserProfileBiographyDescription": "Tambahkan biodata singkat ke profil Anda", "settingsManageUserProfileUpdateBiographyToast": "Biodata Diperbarui!", "settingsManageUserProfileUpdateBiographyToastFailure": "<PERSON><PERSON><PERSON><PERSON>. Coba Lagi.", "settingsManageUserProfileBiographyNoUrlMessage": "Harap hapus semua URL dari biodata Anda", "settingsManageUserProfileLinkedWallets": "<PERSON><PERSON>", "settingsManageUserProfileLinkedWalletsDescription": "<PERSON><PERSON><PERSON> dompet yang muncul di perangkat lain saat masuk ke Akun Phantom Anda.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Dompet tertaut diperbarui!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Gagal memperbarui dompet yang tertaut!", "settingsManageUserProfilePrivacy": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfileUpdatePrivacyStateToast": "<PERSON>rivasi diperbarui!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "<PERSON>l memperbarui privasi!", "settingsManageUserProfilePublicAddresses": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileUpdatePublicAddressToast": "<PERSON><PERSON>t umum diperbarui!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "<PERSON>l memperbarui alamat umum!", "settingsManageUserProfilePrivacyStatePublic": "<PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePublicDescription": "Profil dan alamat umum Anda dapat dilihat dan dicari oleh siapa saja", "settingsManageUserProfilePrivacyStatePrivate": "Privat", "settingsManageUserProfilePrivacyStatePrivateDescription": "Profil Anda dapat dicari oleh siapa saja, tetapi orang lain harus meminta izin untuk melihat profil dan alamat umum Anda", "settingsManageUserProfilePrivacyStateInvisible": "Tak terlihat", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Profil dan alamat umum Anda disembunyikan dan tidak dapat ditemukan di mana pun", "settingsDownloadPhantom": "Unduh Phantom", "settingsLogOut": "<PERSON><PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "Tambahkan Dompet", "seedlessAddAWalletSecondaryText": "<PERSON><PERSON><PERSON> atau impor dompet yang ada ", "seedlessAddSeedlessWalletPrimaryText": "Tambahkan Dompet Tanpa Seed", "seedlessAddSeedlessWalletSecondaryText": "Gunakan ID Apple, Google, atau Email Anda", "seedlessCreateNewWalletPrimaryText": "Buat Dompet Baru?", "seedlessCreateNewWalletSecondaryText": "Email ini tidak memiliki dompet. A<PERSON>kah Anda ingin membuatnya?", "seedlessCreateNewWalletButtonText": "Buat Dompet", "seedlessCreateNewWalletNoBundlePrimaryText": "Dompet tidak ditem<PERSON>n", "seedlessCreateNewWalletNoBundleSecondaryText": "Tidak ada dompet di email ini", "seedlessCreateNewWalletNoBundleButtonText": "Kembali", "seedlessEmailOptionsPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessEmailOptionsSecondaryText": "Tambahkan dompet dengan akun Apple atau Google Anda ", "seedlessEmailOptionsButtonText": "Lan<PERSON><PERSON><PERSON> den<PERSON>", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Buat dompet dengan ID Apple Anda", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Buat dompet dengan email Google Anda", "seedlessAlreadyExistsPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessAlreadyExistsSecondaryText": "Sudah ada dompet yang dibuat dengan email ini. Apakah Anda ingin masuk?", "seedlessSignUpWithAppleButtonText": "Mendaftar dengan Apple", "seedlessContinueWithAppleButtonText": "Lanjutkan den<PERSON>", "seedlessSignUpWithGoogleButtonText": "Daftar dengan Google", "seedlessContinueWithGoogleButtonText": "Lanjutkan dengan Google", "seedlessCreateAPinPrimaryText": "Buat PIN", "seedlessCreateAPinSecondaryText": "Ini digunakan untuk mengamankan dompet Anda di semua perangkat. <1>Ini tidak dapat dipulihkan.</1>", "seedlessContinueText": "Lanjutkan", "seedlessConfirmPinPrimaryText": "Konfirmasikan PIN Anda", "seedlessConfirmPinSecondaryText": "Jika lupa PIN ini, <PERSON><PERSON> tidak akan dapat memulihkan dompet di perangkat baru.", "seedlessConfirmPinButtonText": "Buat PIN", "seedlessConfirmPinError": "PIN salah. Silakan coba lagi.", "seedlessAccountsImportedPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessAccountsImportedSecondaryText": "Akun-akun ini akan secara otomatis diimpor di dompet Anda", "seedlessPreviouslyImportedTag": "Telah diimpor sebelumnya", "seedlessEnterPinPrimaryText": "Masukkan PIN Anda", "seedlessEnterPinInvalidPinError": "PIN yang dimasukkan salah. Hanya 4 digit angka yang diperbolehkan.", "seedlessEnterPinNumTriesLeft": "<PERSON><PERSON> {{numTries}} percobaan.", "seedlessEnterPinCooldown": "Coba lagi dalam {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN harus tepat 4 digit", "seedlessEnterPinMatch": "PIN cocok", "seedlessDoneText": "Se<PERSON><PERSON>", "seedlessEnterPinToSign": "Masukkan PIN Anda untuk menandatangani transaksi ini", "seedlessSigning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seedlessCreateSeed": "Buat dompet frasa seed", "seedlessImportOptions": "Opsi impor lainnya", "seedlessImportPrimaryText": "Opsi Impor", "seedlessImportSecondaryText": "Impor dompet yang ada dengan frasa seed, kunci privat, atau dompet perangkat keras Anda.", "seedlessImportSeedPhrase": "<PERSON><PERSON><PERSON>", "seedlessImportPrivateKey": "Kunci Privat Anda", "seedlessConnectHardwareWallet": "Hubungkan Dompet Perangkat Keras", "seedlessTryAgain": "Coba lagi", "seedlessCreatingWalletPrimaryText": "Membuat do<PERSON>et", "seedlessCreatingWalletSecondaryText": "Menambahkan dompet sosial", "seedlessLoadingWalletPrimaryText": "Memuat dompet", "seedlessLoadingWalletSecondaryText": "Mengi<PERSON><PERSON> dan memantau dompet Anda yang terhubung", "seedlessLoadingWalletErrorPrimaryText": "Gagal memuat dompet", "seedlessCreatingWalletErrorPrimaryText": "<PERSON><PERSON> membuat dompet", "seedlessErrorSecondaryText": "Mohon coba lagi", "seedlessAuthAlreadyExistsErrorText": "<PERSON>ail yang diberikan sudah menjadi milik akun Phantom lain", "seedlessAuthUnknownErrorText": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han yang tidak di<PERSON>, silakan coba lagi nanti", "seedlessAuthUnknownErrorTextRefresh": "<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>han yang tidak di<PERSON>, cobalah lagi nanti. Segarkan halaman untuk mencoba lagi.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON>", "seedlessWalletExistsErrorPrimaryText": "<PERSON><PERSON> sosial sudah ada di perangkat <PERSON>a", "seedlessWalletExistsErrorSecondaryText": "<PERSON><PERSON><PERSON> kembali atau tutup layar ini", "seedlessValueProp1PrimaryText": "Pengaturan yang mulus", "seedlessValueProp1SecondaryText": "Buat dompet menggunakan akun Google atau Apple dan mulailah menjelajahi web3 dengan mudah", "seedlessValueProp2PrimaryText": "<PERSON><PERSON><PERSON> yang di<PERSON>", "seedlessValueProp2SecondaryText": "Dom<PERSON>a disimpan dengan aman dan terdesentralisasi di berbagai faktor", "seedlessValueProp3PrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessValueProp3SecondaryText": "Pulihkan akses ke dompet Anda dengan akun Google atau Apple dan PIN 4 digit", "seedlessLoggingIn": "Proses masuk...", "seedlessSignUpOrLogin": "<PERSON>ft<PERSON> atau Masuk", "seedlessContinueByEnteringYourEmail": "Lanjutkan dengan memasukkan email Anda", "seedless": "<PERSON><PERSON>d", "seed": "Frasa Seed", "seedlessVerifyPinPrimaryText": "Verifikasi PIN", "seedlessVerifyPinSecondaryText": "Ma<PERSON>kkan nomor PIN Anda untuk melanjutkan", "seedlessVerifyPinVerifyButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seedlessVerifyPinForgotButtonText": "<PERSON><PERSON>?", "seedlessPinConfirmButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "seedlessVerifyToastPrimaryText": "Verifikasi PIN Anda", "seedlessVerifyToastSecondaryText": "Sesekali kami akan meminta Anda untuk memverifikasi PIN agar Anda mengingatnya. <PERSON><PERSON> lupa, <PERSON><PERSON> tidak akan dapat memulihkan dompet.", "seedlessVerifyToastSuccessText": "Nomor PIN Anda telah diverifikasi!", "seedlessForgotPinPrimaryText": "Reset PIN menggunakan perangkat lain", "seedlessForgotPinSecondaryText": "<PERSON><PERSON>, <PERSON><PERSON> hanya dapat mereset PIN di perangkat lain jika telah masuk", "seedlessForgotPinInstruction1PrimaryText": "<PERSON><PERSON>", "seedlessForgotPinInstruction1SecondaryText": "<PERSON><PERSON> perangkat lain di mana Anda masuk ke akun Phantom dengan email <PERSON>a", "seedlessForgotPinInstruction2PrimaryText": "<PERSON><PERSON>", "seedlessForgotPinInstruction2SecondaryText": "<PERSON>, <PERSON><PERSON><PERSON> \"Keamanan & Privasi\", <PERSON><PERSON> \"Reset PIN\"", "seedlessForgotPinInstruction3PrimaryText": "Tetapkan PIN Baru Anda", "seedlessForgotPinInstruction3SecondaryText": "<PERSON><PERSON><PERSON> mengatur PIN baru, <PERSON><PERSON> dapat masuk ke dompet Anda di perangkat ini", "seedlessForgotPinButtonText": "Saya sudah melakukan langkah ini", "seedlessResetPinPrimaryText": "Setel Ulang PIN", "seedlessResetPinSecondaryText": "Masukkan PIN baru yang akan Anda ingat. PIN ini digunakan untuk mengamankan dompet Anda di semua perangkat.", "seedlessResetPinSuccessText": "Nomor PIN Anda telah diperbarui!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> do<PERSON>, <PERSON><PERSON> <1><PERSON><PERSON><PERSON><PERSON></1> dan <5><PERSON><PERSON><PERSON><PERSON></5> kami.", "pageNotFound": "Halaman tidak ditemukan", "pageNotFoundDescription": "Kami tidak mengabaikan Anda! Halaman ini tidak ada atau telah dipindahkan.", "webTokenPagesLegalDisclaimer": "Informasi harga disediakan hanya untuk tujuan informasi dan bukan merupakan saran keuangan. Data pasar disediakan oleh pihak ketiga dan Phantom tidak membuat pernyataan apa pun mengenai keakuratan informasi tersebut.", "signUpOrLogin": "<PERSON><PERSON><PERSON> atau masuk", "portalOnboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> a<PERSON>, <PERSON><PERSON> <1><PERSON><PERSON><PERSON><PERSON></1> dan <5><PERSON><PERSON><PERSON><PERSON></5> kami", "feedNoActivity": "Belum ada aktivitas", "followRequests": "<PERSON><PERSON><PERSON><PERSON>", "following": "<PERSON><PERSON><PERSON><PERSON>", "followers": "Pengikut", "follower": "Pengikut", "joined": "Bergabung", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "Tidak Ada Pengikut", "noFollowing": "Tidak Mengikuti", "noUsersFound": "Pengguna Tidak Ditemukan", "viewProfile": "<PERSON><PERSON>", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}