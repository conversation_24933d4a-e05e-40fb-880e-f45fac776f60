import{a as q}from"./chunk-T27XGMXK.js";import{$ as ae,Ma as ne,U as J,X,Y as Z,Z as ee,_ as te,aa as oe}from"./chunk-JD6NH5K6.js";import{h as z}from"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{g as j}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import{h as Y}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import{a as H}from"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{d as M,e as K}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{j as Q}from"./chunk-OKP6DFCI.js";import"./chunk-WIQ4WVKX.js";import{R as W,W as V,Z as U,_ as G}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as C}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{Tb as O}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as B,Pa as A,fb as L}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{a as S,m as $}from"./chunk-56SJOU6P.js";import{V as P,X as _,b as E}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as x}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as N,h as k,n as F}from"./chunk-3KENBVE7.js";k();F();var e=N(x());k();F();var I=N(x());var R=I.default.memo(m=>{let{data:d,index:p,style:n}=m,{listItems:l,moveToNextPage:c,availableAddresses:u}=d,f=l[p],{token:t,addressType:s}=f,{caip19:h}=t,{chainId:a}=h,o=t.caip19.resourceType==="address"?t.caip19.address:void 0,r=u?.find(i=>s?i.addressType===s&&i.networkID===a:i.networkID===a)?.address??"";return I.default.createElement("div",{key:`${t.symbol}-${t.caip19.chainId}-${o}-${p}`,style:n?{...n,top:`${parseFloat(n.top)+te}px`}:void 0},I.default.createElement(J,{spamStatus:"VERIFIED",chainMetadata:L(t.caip19.chainId),logoUri:t.logoURI,name:t.name??"",symbol:t.symbol,tokenAddress:o,walletAddress:r,onClick:()=>c(f),type:O(t.caip19),showBalance:!1,showCurrencyValues:!1,balance:new E(0),currencyValue:void 0,currencyChange:void 0}))},z);var ce=()=>{let{t:m}=$(),{fiatRampGroupedTokens:d,isSearching:p,searchResults:n,handleSearch:l,moveToNextPage:c,closeModal:u,isLoading:f,availableAddresses:t,enabledAddressTypes:s}=ue(),h=(0,e.useMemo)(()=>p?e.default.createElement(M,{gap:8,marginTop:16,marginBottom:96},n.map(({token:a,addressType:o},r)=>o&&!s.includes(o)?null:e.default.createElement(R,{key:`${a.symbol}-${a.caip19.chainId}-${r}`,index:r,data:{listItems:n,moveToNextPage:c,availableAddresses:t}}))):e.default.createElement(e.default.Fragment,null,e.default.createElement(X,null),d?.groups?.map((a,o)=>e.default.createElement(M,{key:`${a.groupName}-${o}`,gap:8,marginTop:o===0?16:24,marginBottom:o===d.groups.length-1?96:0},e.default.createElement(K,{font:"bodyMedium",color:"textSecondary",children:a.groupName}),a.tokens.map(({token:r,addressType:i},y)=>i&&!s.includes(i)?null:e.default.createElement(R,{key:`${r.symbol}-${r.caip19.chainId}-${y}`,index:y,data:{listItems:a.tokens,moveToNextPage:c,availableAddresses:t}}))))),[t,s,d?.groups,p,c,n]);return e.default.createElement(ae,{isLoading:f,isEmpty:n.length===0,emptyStateText:m("noBuyOptionsAvailableInCountry"),header:e.default.createElement(j,{tabIndex:0,placeholder:m("assetListSearch"),onChange:l,maxLength:50}),content:h,footer:e.default.createElement(H,null,e.default.createElement(Q,{onClick:u},m("commandClose")))})},Ke=ce,ue=()=>{let{setState:m}=ee(),{handleHideModalVisibility:d,closeAllModals:p}=ne(),{pushDetailView:n}=Y(),{data:l}=B(),c=Array.from(new Set(l?.addresses?.map(g=>g.addressType))),{version:u}=chrome.runtime.getManifest(),{data:f,isLoading:t,isError:s}=U("web",u,S.language),{data:h=[]}=G("web",u,S.language),[a,o]=(0,e.useState)(""),r=P(a)??"",i=g=>o(g.currentTarget.value),y=a.length>0,re=_(h,r,W),se=g=>{let{token:v,addressType:D,quickSelectDenominations:ie}=g,{name:me,symbol:de,caip19:pe}=v,{chainId:T}=pe,b=l?.addresses.find(w=>D?w.addressType===D&&w.networkID===T:w.networkID===T)?.address??"";A.isEVMNetworkID(T)&&(b=b.toLowerCase()),C.capture("fiatOnrampFungibleSelected",{data:{networkId:T,chainId:A.getChainID(T),asset:{name:me,symbol:de,type:"fungible"}}}),m({...Z,onClose:p,ownerPublicKey:b,fungible:v,quickSelectDenominations:ie??V}),n(e.default.createElement(oe,null))};return q(()=>void C.capture("fiatOnrampInitiated"),!0),{fiatRampGroupedTokens:f,isSearching:y,searchResults:re,handleSearch:i,moveToNextPage:se,closeModal:()=>d("onramp"),isLoading:t||s,availableAddresses:l?.addresses,enabledAddressTypes:c}};export{ce as FiatRampFungiblesPage,Ke as default};
//# sourceMappingURL=FiatRampFungiblesPage-ZOWJ5EXG.js.map
