const { request } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
    // Sử dụng apiListStock.js để lấy danh sách stockList đã được setup sẵn
const getStockList = require('./apiListStock.js');

// Load config
const config = require('./configRedis.json');
const redisConfig = config.redis_search;

// Load token list
const tokenListPath = path.join(__dirname, 'tokenList.json');
let tokenList = [];
try {
  tokenList = JSON.parse(fs.readFileSync(tokenListPath, 'utf8'));
} catch (e) {
  console.error('Failed to read tokenList.json:', e.message || e);
}

if (!Array.isArray(tokenList)) {
  if (tokenList?.tokens) tokenList = tokenList.tokens;
  else if (tokenList?.addresses) tokenList = tokenList.addresses;
  else if (tokenList?.data) tokenList = tokenList.data;
  else tokenList = [];
}

tokenList = tokenList
  .filter((x) => typeof x === 'string' && x.trim().length > 0)
  .map((x) => x.trim());




const apiUrl = 'https://api.dex3.ai/dev/redisearch/token';
const headers = {
  'x-api-key': redisConfig['x-api-key'],
  'Content-Type': 'application/json',
};

async function main() {
  if (!tokenList.length) {
    console.error('tokenList is empty after normalization.');
    process.exitCode = 1;
    return;
  }

  const reqContext = await request.newContext();
  const tonghopdata = [];
  const batchSize = 400;

// get list address stock
const stockList = await getStockList();

//lấy thông tin của token stock
const responseStock = await reqContext.post(apiUrl, {
    headers,
    data: { tokens: stockList },
});
 const dataStock = await responseStock.json();
  // Tổng marketCap top stock
     const stockMarketCap = dataStock.reduce((sum, item) => {
        if (item && !isNaN(Number(item.marketCap))) {
          return sum + Number(item.marketCap);
        }
        return sum;
      }, 0);
  
      //tổng volume top stock
      const stockVolume = dataStock.reduce((sum, item) => {
        if (item && !isNaN(Number(item.volume24h))) {
          return sum + Number(item.volume24h);
        }
        return sum;
      }, 0);
// Màn hình stock
    console.log('Tổng stock:', stockList.length);
    console.log('Tổng marketCap stock:', stockMarketCap);
    console.log('Tổng volume stock:', stockVolume);




  //duyet từng token để get ra thông tin của token đó, mỗi lần truyền 400 token
    try {
    for (let i = 0; i < tokenList.length; i += batchSize) {
      const batch = { tokens: tokenList.slice(i, i + batchSize) };

      if (!batch.tokens.length) continue;

      const response = await reqContext.post(apiUrl, {
        headers,
        data: batch,
      });

      if (!response.ok()) {
        console.error(`Failed batch at index ${i}:`, response.status());
        console.error('Response:', await response.text());
        continue;
      }
      
      const data = await response.json();
      // Lọc các item có baseTokenName chứa 'stock' (không phân biệt hoa thường)
    //   const stockItems = (Array.isArray(data) ? data : [data]).filter(
    //     (item) =>
    //       item &&
    //       typeof item.baseTokenName === 'string' 
    //     //   && item.baseTokenName.toLowerCase().includes('stock') 
    //       &&Array.isArray(item.tags) &&
    //       item.tags.some(
    //         (t) =>
    //           typeof t === 'string' &&
    //           (t.toLowerCase().includes('stock') || t.toLowerCase().includes('prestock'))
    //       )
    //   );




      tonghopdata.push(...(Array.isArray(data) ? data : [data]));

      // Lưu tạm sau mỗi batch để tránh mất dữ liệu
      fs.writeFileSync(
        path.join(__dirname, 'tonghopdata.json'),
        JSON.stringify(tonghopdata, null, 2)
      );
    }

    // Tổng marketCap top token
    const totalMarketCap = tonghopdata.reduce((sum, item) => {
      if (item && !isNaN(Number(item.marketCap))) {
        return sum + Number(item.marketCap);
      }
      return sum;
    }, 0);

    //tổng volume top token
    const totalVolume = tonghopdata.reduce((sum, item) => {
      if (item && !isNaN(Number(item.volume24h))) {
        return sum + Number(item.volume24h);
      }
      return sum;
    }, 0);


    // màn hình top token
    console.log('Tổng marketCap:', totalMarketCap);
    console.log('Tổng volume:', totalVolume);

  } catch (error) {
    console.error('Error during API requests:', error.message || error);
    process.exitCode = 1;
  } finally {
    await reqContext.dispose();
  }
}

main();
