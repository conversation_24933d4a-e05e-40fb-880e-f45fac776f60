import{f as Tr,g as Zn,jb as ei}from"./chunk-SD2LXVLD.js";import{t as wr}from"./chunk-LURFXJDV.js";import{a as $e,b as Xn,c as ue}from"./chunk-MHOQBMVI.js";import{a as Ar}from"./chunk-BTKBODVJ.js";import{a as Or}from"./chunk-LDMZMUWY.js";import{a as yr}from"./chunk-OXFZHPMY.js";import{r as Yn}from"./chunk-SLQBAOEK.js";import{a as Ke,i as Qn,j as At,k as Wn}from"./chunk-56SJOU6P.js";import{j as Jn}from"./chunk-ALUTR72U.js";import{$a as Hn,G as Ge,Q as Bn,R as kn,S as Un,Ta as Gn,Ua as Kn,Xa as $n,ba as Ce,ca as jn,ta as le,u as xn,ua as qn,va as Vn,wa as Ie,xa as yt,ya as zn,z as Pn}from"./chunk-L3A2KHJO.js";import{c as y,f as _t,h as m,i as E,m as Buffer,n as p}from"./chunk-3KENBVE7.js";var Cr=y(Re=>{"use strict";m();p();var Ne=yr();E.NODE_ENV==="production"?(Re.createRoot=Ne.createRoot,Re.hydrateRoot=Ne.hydrateRoot):(Le=Ne.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,Re.createRoot=function(n,e){Le.usingClientEntryPoint=!0;try{return Ne.createRoot(n,e)}finally{Le.usingClientEntryPoint=!1}},Re.hydrateRoot=function(n,e,t){Le.usingClientEntryPoint=!0;try{return Ne.hydrateRoot(n,e,t)}finally{Le.usingClientEntryPoint=!1}});var Le});var Se=y(T=>{"use strict";m();p();Object.defineProperty(T,"__esModule",{value:!0});T.BANDIT_ASSIGNMENT_SHARDS=T.MAX_EVENT_QUEUE_SIZE=T.NULL_SENTINEL=T.SESSION_ASSIGNMENT_CONFIG_LOADED=T.BANDIT_ENDPOINT=T.UFC_ENDPOINT=T.BASE_URL=T.DEFAULT_POLL_CONFIG_REQUEST_RETRIES=T.DEFAULT_INITIAL_CONFIG_REQUEST_RETRIES=T.POLL_JITTER_PCT=T.POLL_INTERVAL_MS=T.REQUEST_TIMEOUT_MILLIS=T.DEFAULT_REQUEST_TIMEOUT_MS=void 0;T.DEFAULT_REQUEST_TIMEOUT_MS=5e3;T.REQUEST_TIMEOUT_MILLIS=T.DEFAULT_REQUEST_TIMEOUT_MS;T.POLL_INTERVAL_MS=3e4;T.POLL_JITTER_PCT=.1;T.DEFAULT_INITIAL_CONFIG_REQUEST_RETRIES=1;T.DEFAULT_POLL_CONFIG_REQUEST_RETRIES=7;T.BASE_URL="https://fscdn.eppo.cloud/api";T.UFC_ENDPOINT="/flag-config/v1/config";T.BANDIT_ENDPOINT="/flag-config/v1/bandits";T.SESSION_ASSIGNMENT_CONFIG_LOADED="eppo-session-assignment-config-loaded";T.NULL_SENTINEL="EPPO_NULL";T.MAX_EVENT_QUEUE_SIZE=100;T.BANDIT_ASSIGNMENT_SHARDS=1e4});var Ct=y(Ot=>{"use strict";m();p();Object.defineProperty(Ot,"__esModule",{value:!0});var wt=Se(),Tt=class{constructor(e){var t;this.params=e,this.params.baseUrl=(t=e.baseUrl)!==null&&t!==void 0?t:wt.BASE_URL}endpoint(e){var t;let i=new URL(this.params.baseUrl+e);return Object.entries((t=this.params.queryParams)!==null&&t!==void 0?t:{}).forEach(([r,o])=>i.searchParams.append(r,o)),i}ufcEndpoint(){return this.endpoint(wt.UFC_ENDPOINT)}banditParametersEndpoint(){return this.endpoint(wt.BANDIT_ENDPOINT)}};Ot.default=Tt});var ni=y((To,ti)=>{"use strict";m();p();function Ir(n){try{return JSON.stringify(n)}catch{return'"[Circular]"'}}ti.exports=Nr;function Nr(n,e,t){var i=t&&t.stringify||Ir,r=1;if(typeof n=="object"&&n!==null){var o=e.length+r;if(o===1)return n;var g=new Array(o);g[0]=i(n);for(var a=1;a<o;a++)g[a]=i(e[a]);return g.join(" ")}if(typeof n!="string")return n;var f=e.length;if(f===0)return n;for(var s="",u=1-r,l=-1,d=n&&n.length||0,c=0;c<d;){if(n.charCodeAt(c)===37&&c+1<d){switch(l=l>-1?l:0,n.charCodeAt(c+1)){case 100:case 102:if(u>=f||e[u]==null)break;l<c&&(s+=n.slice(l,c)),s+=Number(e[u]),l=c+2,c++;break;case 105:if(u>=f||e[u]==null)break;l<c&&(s+=n.slice(l,c)),s+=Math.floor(Number(e[u])),l=c+2,c++;break;case 79:case 111:case 106:if(u>=f||e[u]===void 0)break;l<c&&(s+=n.slice(l,c));var S=typeof e[u];if(S==="string"){s+="'"+e[u]+"'",l=c+2,c++;break}if(S==="function"){s+=e[u].name||"<anonymous>",l=c+2,c++;break}s+=i(e[u]),l=c+2,c++;break;case 115:if(u>=f)break;l<c&&(s+=n.slice(l,c)),s+=String(e[u]),l=c+2,c++;break;case 37:l<c&&(s+=n.slice(l,c)),s+="%",l=c+2,c++,u--;break}++u}++c}return l===-1?n:(l<d&&(s+=n.slice(l)),s)}});var li=y((Io,We)=>{"use strict";m();p();var ii=ni();We.exports=Y;var Fe=Gr().console||{},Lr={mapHttpRequest:He,mapHttpResponse:He,wrapRequestSerializer:It,wrapResponseSerializer:It,wrapErrorSerializer:It,req:He,res:He,err:si,errWithCause:si};function Je(n,e){return n==="silent"?1/0:e.levels.values[n]}var Rt=Symbol("pino.logFuncs"),Nt=Symbol("pino.hierarchy"),Rr={error:"log",fatal:"error",warn:"error",info:"log",debug:"log",trace:"log"};function ri(n,e){let t={logger:e,parent:n[Nt]};e[Nt]=t}function Fr(n,e,t){let i={};e.forEach(r=>{i[r]=t[r]?t[r]:Fe[r]||Fe[Rr[r]||"log"]||De}),n[Rt]=i}function Dr(n,e){return Array.isArray(n)?n.filter(function(i){return i!=="!stdSerializers.err"}):n===!0?Object.keys(e):!1}function Y(n){n=n||{},n.browser=n.browser||{};let e=n.browser.transmit;if(e&&typeof e.send!="function")throw Error("pino: transmit option must have a send function");let t=n.browser.write||Fe;n.browser.write&&(n.browser.asObject=!0);let i=n.serializers||{},r=Dr(n.browser.serialize,i),o=n.browser.serialize;Array.isArray(n.browser.serialize)&&n.browser.serialize.indexOf("!stdSerializers.err")>-1&&(o=!1);let g=Object.keys(n.customLevels||{}),a=["error","fatal","warn","info","debug","trace"].concat(g);typeof t=="function"&&a.forEach(function(b){t[b]=t}),(n.enabled===!1||n.browser.disabled)&&(n.level="silent");let f=n.level||"info",s=Object.create(t);s.log||(s.log=De),Fr(s,a,t),ri({},s),Object.defineProperty(s,"levelVal",{get:l}),Object.defineProperty(s,"level",{get:d,set:c});let u={transmit:e,serialize:r,asObject:n.browser.asObject,formatters:n.browser.formatters,levels:a,timestamp:qr(n)};s.levels=Mr(n),s.level=f,s.setMaxListeners=s.getMaxListeners=s.emit=s.addListener=s.on=s.prependListener=s.once=s.prependOnceListener=s.removeListener=s.removeAllListeners=s.listeners=s.listenerCount=s.eventNames=s.write=s.flush=De,s.serializers=i,s._serialize=r,s._stdErrSerialize=o,s.child=S,e&&(s._logEvent=Lt());function l(){return Je(this.level,this)}function d(){return this._level}function c(b){if(b!=="silent"&&!this.levels.values[b])throw Error("unknown level "+b);this._level=b,ce(this,u,s,"error"),ce(this,u,s,"fatal"),ce(this,u,s,"warn"),ce(this,u,s,"info"),ce(this,u,s,"debug"),ce(this,u,s,"trace"),g.forEach(_=>{ce(this,u,s,_)})}function S(b,_){if(!b)throw new Error("missing bindings for child Pino");_=_||{},r&&b.serializers&&(_.serializers=b.serializers);let A=_.serializers;if(r&&A){var O=Object.assign({},i,A),N=n.browser.serialize===!0?Object.keys(O):r;delete b.serializers,Qe([b],N,O,this._stdErrSerialize)}function M(B){this._childLevel=(B._childLevel|0)+1,this.bindings=b,O&&(this.serializers=O,this._serialize=N),e&&(this._logEvent=Lt([].concat(B._logEvent.bindings,b)))}M.prototype=this;let C=new M(this);return ri(this,C),C.level=this.level,C}return s}function Mr(n){let e=n.customLevels||{},t=Object.assign({},Y.levels.values,e),i=Object.assign({},Y.levels.labels,xr(e));return{values:t,labels:i}}function xr(n){let e={};return Object.keys(n).forEach(function(t){e[n[t]]=t}),e}Y.levels={values:{fatal:60,error:50,warn:40,info:30,debug:20,trace:10},labels:{10:"trace",20:"debug",30:"info",40:"warn",50:"error",60:"fatal"}};Y.stdSerializers=Lr;Y.stdTimeFunctions=Object.assign({},{nullTime:oi,epochTime:ai,unixTime:Vr,isoTime:zr});function Pr(n){let e=[];n.bindings&&e.push(n.bindings);let t=n[Nt];for(;t.parent;)t=t.parent,t.logger.bindings&&e.push(t.logger.bindings);return e.reverse()}function ce(n,e,t,i){if(Object.defineProperty(n,i,{value:Je(n.level,t)>Je(i,t)?De:t[Rt][i],writable:!0,enumerable:!0,configurable:!0}),!e.transmit&&n[i]===De)return;n[i]=kr(n,e,t,i);let r=Pr(n);r.length!==0&&(n[i]=Br(r,n[i]))}function Br(n,e){return function(){return e.apply(this,[...n,...arguments])}}function kr(n,e,t,i){return function(r){return function(){let g=e.timestamp(),a=new Array(arguments.length),f=Object.getPrototypeOf&&Object.getPrototypeOf(this)===Fe?Fe:this;for(var s=0;s<a.length;s++)a[s]=arguments[s];if(e.serialize&&!e.asObject&&Qe(a,this._serialize,this.serializers,this._stdErrSerialize),e.asObject||e.formatters?r.call(f,Ur(this,i,a,g,e.formatters)):r.apply(f,a),e.transmit){let u=e.transmit.level||n._level,l=t.levels.values[u],d=t.levels.values[i];if(d<l)return;jr(this,{ts:g,methodLevel:i,methodValue:d,transmitLevel:u,transmitValue:t.levels.values[e.transmit.level||n._level],send:e.transmit.send,val:Je(n._level,t)},a)}}}(n[Rt][i])}function Ur(n,e,t,i,r={}){let{level:o=()=>n.levels.values[e],log:g=d=>d}=r;n._serialize&&Qe(t,n._serialize,n.serializers,n._stdErrSerialize);let a=t.slice(),f=a[0],s={};i&&(s.time=i),s.level=o(e,n.levels.values[e]);let u=(n._childLevel|0)+1;if(u<1&&(u=1),f!==null&&typeof f=="object"){for(;u--&&typeof a[0]=="object";)Object.assign(s,a.shift());f=a.length?ii(a.shift(),a):void 0}else typeof f=="string"&&(f=ii(a.shift(),a));return f!==void 0&&(s.msg=f),g(s)}function Qe(n,e,t,i){for(let r in n)if(i&&n[r]instanceof Error)n[r]=Y.stdSerializers.err(n[r]);else if(typeof n[r]=="object"&&!Array.isArray(n[r]))for(let o in n[r])e&&e.indexOf(o)>-1&&o in t&&(n[r][o]=t[o](n[r][o]))}function jr(n,e,t){let i=e.send,r=e.ts,o=e.methodLevel,g=e.methodValue,a=e.val,f=n._logEvent.bindings;Qe(t,n._serialize||Object.keys(n.serializers),n.serializers,n._stdErrSerialize===void 0?!0:n._stdErrSerialize),n._logEvent.ts=r,n._logEvent.messages=t.filter(function(s){return f.indexOf(s)===-1}),n._logEvent.level.label=o,n._logEvent.level.value=g,i(o,n._logEvent,a),n._logEvent=Lt(f)}function Lt(n){return{ts:0,messages:[],bindings:n||[],level:{label:"",value:0}}}function si(n){let e={type:n.constructor.name,msg:n.message,stack:n.stack};for(let t in n)e[t]===void 0&&(e[t]=n[t]);return e}function qr(n){return typeof n.timestamp=="function"?n.timestamp:n.timestamp===!1?oi:ai}function He(){return{}}function It(n){return n}function De(){}function oi(){return!1}function ai(){return Date.now()}function Vr(){return Math.round(Date.now()/1e3)}function zr(){return new Date(Date.now()).toISOString()}function Gr(){function n(e){return typeof e<"u"&&e}try{return typeof globalThis<"u"||Object.defineProperty(Object.prototype,"globalThis",{get:function(){return delete Object.prototype.globalThis,this.globalThis=this},configurable:!0}),globalThis}catch{return n(self)||n(self)||n(this)||{}}}We.exports.default=Y;We.exports.pino=Y});var Me=y(Ee=>{"use strict";m();p();Object.defineProperty(Ee,"__esModule",{value:!0});Ee.logger=Ee.loggerPrefix=void 0;var Kr=li();Ee.loggerPrefix="[Eppo SDK]";Ee.logger=(0,Kr.default)({level:E.NODE_ENV==="production"?"warn":"info",browser:{disabled:!0}})});var ui=y((Ft,Dt)=>{m();p();(function(n,e){typeof Ft=="object"&&typeof Dt<"u"?Dt.exports=e():typeof define=="function"&&define.amd?define(e):function(){var t=n.Base64,i=e();i.noConflict=function(){return n.Base64=t,i},n.Meteor&&(Base64=i),n.Base64=i}()})(typeof self<"u"||typeof self<"u"||typeof self<"u"?self:Ft,function(){"use strict";var n="3.7.7",e=n,t=typeof Buffer=="function",i=typeof TextDecoder=="function"?new TextDecoder:void 0,r=typeof TextEncoder=="function"?new TextEncoder:void 0,o="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",g=Array.prototype.slice.call(o),a=function(h){var v={};return h.forEach(function(I,k){return v[I]=k}),v}(g),f=/^(?:[A-Za-z\d+\/]{4})*?(?:[A-Za-z\d+\/]{2}(?:==)?|[A-Za-z\d+\/]{3}=?)?$/,s=String.fromCharCode.bind(String),u=typeof Uint8Array.from=="function"?Uint8Array.from.bind(Uint8Array):function(h){return new Uint8Array(Array.prototype.slice.call(h,0))},l=function(h){return h.replace(/=/g,"").replace(/[+\/]/g,function(v){return v=="+"?"-":"_"})},d=function(h){return h.replace(/[^A-Za-z0-9\+\/]/g,"")},c=function(h){for(var v,I,k,ae,W="",bt=h.length%3,ze=0;ze<h.length;){if((I=h.charCodeAt(ze++))>255||(k=h.charCodeAt(ze++))>255||(ae=h.charCodeAt(ze++))>255)throw new TypeError("invalid character found");v=I<<16|k<<8|ae,W+=g[v>>18&63]+g[v>>12&63]+g[v>>6&63]+g[v&63]}return bt?W.slice(0,bt-3)+"===".substring(bt):W},S=typeof btoa=="function"?function(h){return btoa(h)}:t?function(h){return Buffer.from(h,"binary").toString("base64")}:c,b=t?function(h){return Buffer.from(h).toString("base64")}:function(h){for(var v=4096,I=[],k=0,ae=h.length;k<ae;k+=v)I.push(s.apply(null,h.subarray(k,k+v)));return S(I.join(""))},_=function(h,v){return v===void 0&&(v=!1),v?l(b(h)):b(h)},A=function(h){if(h.length<2){var v=h.charCodeAt(0);return v<128?h:v<2048?s(192|v>>>6)+s(128|v&63):s(224|v>>>12&15)+s(128|v>>>6&63)+s(128|v&63)}else{var v=65536+(h.charCodeAt(0)-55296)*1024+(h.charCodeAt(1)-56320);return s(240|v>>>18&7)+s(128|v>>>12&63)+s(128|v>>>6&63)+s(128|v&63)}},O=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,N=function(h){return h.replace(O,A)},M=t?function(h){return Buffer.from(h,"utf8").toString("base64")}:r?function(h){return b(r.encode(h))}:function(h){return S(N(h))},C=function(h,v){return v===void 0&&(v=!1),v?l(M(h)):M(h)},B=function(h){return C(h,!0)},ee=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,te=function(h){switch(h.length){case 4:var v=(7&h.charCodeAt(0))<<18|(63&h.charCodeAt(1))<<12|(63&h.charCodeAt(2))<<6|63&h.charCodeAt(3),I=v-65536;return s((I>>>10)+55296)+s((I&1023)+56320);case 3:return s((15&h.charCodeAt(0))<<12|(63&h.charCodeAt(1))<<6|63&h.charCodeAt(2));default:return s((31&h.charCodeAt(0))<<6|63&h.charCodeAt(1))}},Cn=function(h){return h.replace(ee,te)},In=function(h){if(h=h.replace(/\s+/g,""),!f.test(h))throw new TypeError("malformed base64.");h+="==".slice(2-(h.length&3));for(var v,I="",k,ae,W=0;W<h.length;)v=a[h.charAt(W++)]<<18|a[h.charAt(W++)]<<12|(k=a[h.charAt(W++)])<<6|(ae=a[h.charAt(W++)]),I+=k===64?s(v>>16&255):ae===64?s(v>>16&255,v>>8&255):s(v>>16&255,v>>8&255,v&255);return I},St=typeof atob=="function"?function(h){return atob(d(h))}:t?function(h){return Buffer.from(h,"base64").toString("binary")}:In,Nn=t?function(h){return u(Buffer.from(h,"base64"))}:function(h){return u(St(h).split("").map(function(v){return v.charCodeAt(0)}))},Ln=function(h){return Nn(Rn(h))},Er=t?function(h){return Buffer.from(h,"base64").toString("utf8")}:i?function(h){return i.decode(Nn(h))}:function(h){return Cn(St(h))},Rn=function(h){return d(h.replace(/[-_]/g,function(v){return v=="-"?"+":"/"}))},Et=function(h){return Er(Rn(h))},br=function(h){if(typeof h!="string")return!1;var v=h.replace(/\s+/g,"").replace(/={0,2}$/,"");return!/[^\s0-9a-zA-Z\+/]/.test(v)||!/[^\s0-9a-zA-Z\-_]/.test(v)},Fn=function(h){return{value:h,enumerable:!1,writable:!0,configurable:!0}},Dn=function(){var h=function(v,I){return Object.defineProperty(String.prototype,v,Fn(I))};h("fromBase64",function(){return Et(this)}),h("toBase64",function(v){return C(this,v)}),h("toBase64URI",function(){return C(this,!0)}),h("toBase64URL",function(){return C(this,!0)}),h("toUint8Array",function(){return Ln(this)})},Mn=function(){var h=function(v,I){return Object.defineProperty(Uint8Array.prototype,v,Fn(I))};h("toBase64",function(v){return _(this,v)}),h("toBase64URI",function(){return _(this,!0)}),h("toBase64URL",function(){return _(this,!0)})},_r=function(){Dn(),Mn()},Oe={version:n,VERSION:e,atob:St,atobPolyfill:In,btoa:S,btoaPolyfill:c,fromBase64:Et,toBase64:C,encode:C,encodeURI:B,encodeURL:B,utob:N,btou:Cn,decode:Et,isValid:br,fromUint8Array:_,toUint8Array:Ln,extendString:Dn,extendUint8Array:Mn,extendBuiltins:_r};return Oe.Base64={},Object.keys(Oe).forEach(function(h){return Oe.Base64[h]=Oe[h]}),Oe})});var di=y((Po,gi)=>{m();p();gi.exports=function(n){return n!=null&&(ci(n)||$r(n)||!!n._isBuffer)};function ci(n){return!!n.constructor&&typeof n.constructor.isBuffer=="function"&&n.constructor.isBuffer(n)}function $r(n){return typeof n.readFloatLE=="function"&&typeof n.slice=="function"&&ci(n.slice(0,0))}});var hi=y((Uo,fi)=>{m();p();(function(){var n=Tr(),e=Zn().utf8,t=di(),i=Zn().bin,r=function(o,g){o.constructor==String?g&&g.encoding==="binary"?o=i.stringToBytes(o):o=e.stringToBytes(o):t(o)?o=Array.prototype.slice.call(o,0):!Array.isArray(o)&&o.constructor!==Uint8Array&&(o=o.toString());for(var a=n.bytesToWords(o),f=o.length*8,s=1732584193,u=-271733879,l=-1732584194,d=271733878,c=0;c<a.length;c++)a[c]=(a[c]<<8|a[c]>>>24)&16711935|(a[c]<<24|a[c]>>>8)&4278255360;a[f>>>5]|=128<<f%32,a[(f+64>>>9<<4)+14]=f;for(var S=r._ff,b=r._gg,_=r._hh,A=r._ii,c=0;c<a.length;c+=16){var O=s,N=u,M=l,C=d;s=S(s,u,l,d,a[c+0],7,-680876936),d=S(d,s,u,l,a[c+1],12,-389564586),l=S(l,d,s,u,a[c+2],17,606105819),u=S(u,l,d,s,a[c+3],22,-1044525330),s=S(s,u,l,d,a[c+4],7,-176418897),d=S(d,s,u,l,a[c+5],12,1200080426),l=S(l,d,s,u,a[c+6],17,-1473231341),u=S(u,l,d,s,a[c+7],22,-45705983),s=S(s,u,l,d,a[c+8],7,1770035416),d=S(d,s,u,l,a[c+9],12,-1958414417),l=S(l,d,s,u,a[c+10],17,-42063),u=S(u,l,d,s,a[c+11],22,-1990404162),s=S(s,u,l,d,a[c+12],7,1804603682),d=S(d,s,u,l,a[c+13],12,-40341101),l=S(l,d,s,u,a[c+14],17,-1502002290),u=S(u,l,d,s,a[c+15],22,1236535329),s=b(s,u,l,d,a[c+1],5,-165796510),d=b(d,s,u,l,a[c+6],9,-1069501632),l=b(l,d,s,u,a[c+11],14,643717713),u=b(u,l,d,s,a[c+0],20,-373897302),s=b(s,u,l,d,a[c+5],5,-701558691),d=b(d,s,u,l,a[c+10],9,38016083),l=b(l,d,s,u,a[c+15],14,-660478335),u=b(u,l,d,s,a[c+4],20,-405537848),s=b(s,u,l,d,a[c+9],5,568446438),d=b(d,s,u,l,a[c+14],9,-1019803690),l=b(l,d,s,u,a[c+3],14,-187363961),u=b(u,l,d,s,a[c+8],20,1163531501),s=b(s,u,l,d,a[c+13],5,-1444681467),d=b(d,s,u,l,a[c+2],9,-51403784),l=b(l,d,s,u,a[c+7],14,1735328473),u=b(u,l,d,s,a[c+12],20,-1926607734),s=_(s,u,l,d,a[c+5],4,-378558),d=_(d,s,u,l,a[c+8],11,-2022574463),l=_(l,d,s,u,a[c+11],16,1839030562),u=_(u,l,d,s,a[c+14],23,-35309556),s=_(s,u,l,d,a[c+1],4,-1530992060),d=_(d,s,u,l,a[c+4],11,1272893353),l=_(l,d,s,u,a[c+7],16,-155497632),u=_(u,l,d,s,a[c+10],23,-1094730640),s=_(s,u,l,d,a[c+13],4,681279174),d=_(d,s,u,l,a[c+0],11,-358537222),l=_(l,d,s,u,a[c+3],16,-722521979),u=_(u,l,d,s,a[c+6],23,76029189),s=_(s,u,l,d,a[c+9],4,-640364487),d=_(d,s,u,l,a[c+12],11,-421815835),l=_(l,d,s,u,a[c+15],16,530742520),u=_(u,l,d,s,a[c+2],23,-995338651),s=A(s,u,l,d,a[c+0],6,-198630844),d=A(d,s,u,l,a[c+7],10,1126891415),l=A(l,d,s,u,a[c+14],15,-1416354905),u=A(u,l,d,s,a[c+5],21,-57434055),s=A(s,u,l,d,a[c+12],6,1700485571),d=A(d,s,u,l,a[c+3],10,-1894986606),l=A(l,d,s,u,a[c+10],15,-1051523),u=A(u,l,d,s,a[c+1],21,-2054922799),s=A(s,u,l,d,a[c+8],6,1873313359),d=A(d,s,u,l,a[c+15],10,-30611744),l=A(l,d,s,u,a[c+6],15,-1560198380),u=A(u,l,d,s,a[c+13],21,1309151649),s=A(s,u,l,d,a[c+4],6,-145523070),d=A(d,s,u,l,a[c+11],10,-1120210379),l=A(l,d,s,u,a[c+2],15,718787259),u=A(u,l,d,s,a[c+9],21,-343485551),s=s+O>>>0,u=u+N>>>0,l=l+M>>>0,d=d+C>>>0}return n.endian([s,u,l,d])};r._ff=function(o,g,a,f,s,u,l){var d=o+(g&a|~g&f)+(s>>>0)+l;return(d<<u|d>>>32-u)+g},r._gg=function(o,g,a,f,s,u,l){var d=o+(g&f|a&~f)+(s>>>0)+l;return(d<<u|d>>>32-u)+g},r._hh=function(o,g,a,f,s,u,l){var d=o+(g^a^f)+(s>>>0)+l;return(d<<u|d>>>32-u)+g},r._ii=function(o,g,a,f,s,u,l){var d=o+(a^(g|~f))+(s>>>0)+l;return(d<<u|d>>>32-u)+g},r._blocksize=16,r._digestsize=16,fi.exports=function(o,g){if(o==null)throw new Error("Illegal argument "+o);var a=n.wordsToBytes(r(o,g));return g&&g.asBytes?a:g&&g.asString?i.bytesToString(a):n.bytesToHex(a)}})()});var ge=y(ne=>{"use strict";m();p();Object.defineProperty(ne,"__esModule",{value:!0});ne.decodeBase64=ne.encodeBase64=ne.getMD5Hash=void 0;var mi=ui(),Hr=hi();function Jr(n){return Hr(n)}ne.getMD5Hash=Jr;function Qr(n){return mi.btoaPolyfill(n)}ne.encodeBase64=Qr;function Wr(n){return mi.atobPolyfill(n)}ne.decodeBase64=Wr});var pi=y(Ye=>{"use strict";m();p();Object.defineProperty(Ye,"__esModule",{value:!0});Ye.LRUCache=void 0;var Mt=class{constructor(e){this.capacity=e,this.cache=new Map,this.size=this.cache.size}[Symbol.iterator](){return this.cache[Symbol.iterator]()}forEach(e,t){this.cache.forEach(e,t)}entries(){return this.cache.entries()}clear(){this.cache.clear()}delete(e){return this.cache.delete(e)}keys(){return this.cache.keys()}values(){return this.cache.values()}has(e){return this.cache.has(e)}get(e){if(!this.has(e))return;let t=this.cache.get(e);return t!==void 0&&(this.delete(e),this.cache.set(e,t)),t}set(e,t){if(this.capacity===0)return this;if(this.cache.has(e))this.cache.delete(e);else if(this.cache.size>=this.capacity){let i=this.cache.keys().next().value;this.delete(i)}return this.cache.set(e,t),this}};Ye.LRUCache=Mt});var Ut=y(U=>{"use strict";m();p();Object.defineProperty(U,"__esModule",{value:!0});U.LRUInMemoryAssignmentCache=U.NonExpiringInMemoryAssignmentCache=U.AbstractAssignmentCache=U.assignmentCacheValueToString=U.assignmentCacheKeyToString=void 0;var vi=ge(),Yr=pi();function xt({subjectKey:n,flagKey:e}){return(0,vi.getMD5Hash)([n,e].join(";"))}U.assignmentCacheKeyToString=xt;function Pt({allocationKey:n,variationKey:e}){return(0,vi.getMD5Hash)([n,e].join(";"))}U.assignmentCacheValueToString=Pt;var xe=class{constructor(e){this.delegate=e}has(e){return this.get(e)===Pt(e)}get(e){return this.delegate.get(xt(e))}set(e){this.delegate.set(xt(e),Pt(e))}entries(){return this.delegate.entries()}};U.AbstractAssignmentCache=xe;var Bt=class extends xe{constructor(e=new Map){super(e)}};U.NonExpiringInMemoryAssignmentCache=Bt;var kt=class extends xe{constructor(e){super(new Yr.LRUCache(e))}};U.LRUInMemoryAssignmentCache=kt});var Vt=y(ie=>{"use strict";m();p();Object.defineProperty(ie,"__esModule",{value:!0});ie.DeterministicSharder=ie.MD5Sharder=ie.Sharder=void 0;var Zr=ge(),Pe=class{};ie.Sharder=Pe;var jt=class extends Pe{getShard(e,t){let i=(0,Zr.getMD5Hash)(e);return parseInt(i.slice(0,8),16)%t}};ie.MD5Sharder=jt;var qt=class extends Pe{constructor(e){super(),this.lookup=e}getShard(e,t){var i;return(i=this.lookup[e])!==null&&i!==void 0?i:0}};ie.DeterministicSharder=qt});var Si=y(Ze=>{"use strict";m();p();Object.defineProperty(Ze,"__esModule",{value:!0});Ze.BanditEvaluator=void 0;var Xr=Se(),es=Vt(),zt=class{constructor(){this.assignmentShards=Xr.BANDIT_ASSIGNMENT_SHARDS,this.sharder=new es.MD5Sharder}evaluateBandit(e,t,i,r,o){let g=this.scoreActions(i,r,o),a=this.weighActions(g,o.gamma,o.actionProbabilityFloor),f=this.selectAction(e,t,a),u=Object.values(g).reduce((l,d)=>d>l?d:l,-1/0)-g[f];return{flagKey:e,subjectKey:t,subjectAttributes:i,actionKey:f,actionAttributes:r[f],actionScore:g[f],actionWeight:a[f],gamma:o.gamma,optimalityGap:u}}scoreActions(e,t,i){let r={};return Object.entries(t).forEach(([o,g])=>{let a=i.defaultActionScore,f=i.coefficients[o];f&&(a=f.intercept,a+=this.scoreNumericAttributes(f.subjectNumericCoefficients,e.numericAttributes),a+=this.scoreCategoricalAttributes(f.subjectCategoricalCoefficients,e.categoricalAttributes),a+=this.scoreNumericAttributes(f.actionNumericCoefficients,g.numericAttributes),a+=this.scoreCategoricalAttributes(f.actionCategoricalCoefficients,g.categoricalAttributes)),r[o]=a}),r}scoreNumericAttributes(e,t){return e.reduce((i,r)=>{let o=t[r.attributeKey];return typeof o=="number"&&isFinite(o)?i+=o*r.coefficient:i+=r.missingValueCoefficient,i},0)}scoreCategoricalAttributes(e,t){return e.reduce((i,r)=>{var o;let g=(o=t[r.attributeKey])===null||o===void 0?void 0:o.toString(),a=g&&r.valueCoefficients[g];return i+=typeof a=="number"?a:r.missingValueCoefficient,i},0)}weighActions(e,t,i){let r={},o=Object.entries(e);if(!o.length)return r;let g=null,a=null;if(o.forEach(([c,S])=>{(g===null||S>g)&&(g=S,a=c)}),g===null||a===null)throw new Error("Unable to find top score");let f=g,s=a,u=o.length,l=i/u,d=0;return o.forEach(([c,S])=>{if(c===s)return;let b=1/(u+t*(f-S)),_=Math.max(b,l);d+=_,r[c]=_}),r[s]=Math.max(1-d,0),r}selectAction(e,t,i){let r=Object.entries(i).sort((s,u)=>{let l=this.sharder.getShard(`${e}-${t}-${s[0]}`,this.assignmentShards),d=this.sharder.getShard(`${e}-${t}-${u[0]}`,this.assignmentShards),c=l-d;return c===0&&(c=s[0]<u[0]?-1:1),c}),g=this.sharder.getShard(`${e}-${t}`,this.assignmentShards)/this.assignmentShards,a=0,f=null;for(let s of r)if(a+=s[1],a>g){f=s[0];break}if(f===null)throw new Error(`No bandit action selected for flag "${e}" and subject "${t}"`);return f}};Ze.BanditEvaluator=zt});var $t=y(Kt=>{"use strict";m();p();Object.defineProperty(Kt,"__esModule",{value:!0});var Gt=class{constructor(e,t,i,r){this.httpClient=e,this.flagConfigurationStore=t,this.banditVariationConfigurationStore=i,this.banditModelConfigurationStore=r}async fetchAndStoreConfigurations(){var e;let t=await this.httpClient.getUniversalFlagConfiguration();if(!t?.flags)return;await this.hydrateConfigurationStore(this.flagConfigurationStore,{entries:t.flags,environment:t.environment,createdAt:t.createdAt});let i=Object.keys((e=t.bandits)!==null&&e!==void 0?e:{}).length>0,r=!!(this.banditVariationConfigurationStore&&this.banditModelConfigurationStore);if(i&&r){let o=this.indexBanditVariationsByFlagKey(t.bandits);await this.hydrateConfigurationStore(this.banditVariationConfigurationStore,{entries:o,environment:t.environment,createdAt:t.createdAt});let g=await this.httpClient.getBanditParameters();if(g?.bandits){if(!this.banditModelConfigurationStore)throw new Error("Bandit parameters fetched but no bandit configuration store provided");await this.hydrateConfigurationStore(this.banditModelConfigurationStore,{entries:g.bandits,environment:t.environment,createdAt:t.createdAt})}}}async hydrateConfigurationStore(e,t){e&&await e.setEntries(t.entries)&&(e.setEnvironment(t.environment),e.setConfigFetchedAt(new Date().toISOString()),e.setConfigPublishedAt(t.createdAt))}indexBanditVariationsByFlagKey(e){let t={};return Object.values(e).forEach(i=>{i.forEach(r=>{let o=t[r.flagKey];o||(o=[],t[r.flagKey]=o),o.push(r)})}),t}};Kt.default=Gt});var be=y(Be=>{"use strict";m();p();Object.defineProperty(Be,"__esModule",{value:!0});Be.VariationType=void 0;var ts;(function(n){n.STRING="STRING",n.INTEGER="INTEGER",n.NUMERIC="NUMERIC",n.BOOLEAN="BOOLEAN",n.JSON="JSON"})(ts=Be.VariationType||(Be.VariationType={}))});var Ti=y(L=>{"use strict";m();p();Object.defineProperty(L,"__esModule",{value:!0});L.decodeObject=L.decodeShard=L.decodeSplit=L.decodeAllocation=L.decodeValue=L.decodeVariations=L.decodeFlag=void 0;var Ht=be(),V=ge();function ns(n){return Object.assign(Object.assign({},n),{variations:Ei(n.variations,n.variationType),allocations:n.allocations.map(_i)})}L.decodeFlag=ns;function Ei(n,e){return Object.fromEntries(Object.entries(n).map(([,t])=>{let i=(0,V.decodeBase64)(t.key);return[i,{key:i,value:bi(t.value,e)}]}))}L.decodeVariations=Ei;function bi(n,e){switch(e){case Ht.VariationType.INTEGER:case Ht.VariationType.NUMERIC:return Number((0,V.decodeBase64)(n));case Ht.VariationType.BOOLEAN:return(0,V.decodeBase64)(n)==="true";default:return(0,V.decodeBase64)(n)}}L.decodeValue=bi;function _i(n){return Object.assign(Object.assign({},n),{key:(0,V.decodeBase64)(n.key),splits:n.splits.map(yi),startAt:n.startAt?new Date((0,V.decodeBase64)(n.startAt)).toISOString():void 0,endAt:n.endAt?new Date((0,V.decodeBase64)(n.endAt)).toISOString():void 0})}L.decodeAllocation=_i;function yi(n){return{extraLogging:n.extraLogging?wi(n.extraLogging):void 0,variationKey:(0,V.decodeBase64)(n.variationKey),shards:n.shards.map(Ai)}}L.decodeSplit=yi;function Ai(n){return Object.assign(Object.assign({},n),{salt:(0,V.decodeBase64)(n.salt)})}L.decodeShard=Ai;function wi(n){return Object.fromEntries(Object.entries(n).map(([e,t])=>[(0,V.decodeBase64)(e),(0,V.decodeBase64)(t)]))}L.decodeObject=wi});var Oi=y(de=>{"use strict";m();p();Object.defineProperty(de,"__esModule",{value:!0});de.EppoValue=de.EppoValueType=void 0;var ke=be(),is=ge(),z;(function(n){n[n.NullType=0]="NullType",n[n.BoolType=1]="BoolType",n[n.NumericType=2]="NumericType",n[n.StringType=3]="StringType",n[n.JSONType=4]="JSONType"})(z=de.EppoValueType||(de.EppoValueType={}));var Jt=class n{constructor(e,t,i,r,o){this.valueType=e,this.boolValue=t,this.numericValue=i,this.stringValue=r,this.objectValue=o}static valueOf(e,t){if(e==null)return n.Null();switch(t){case ke.VariationType.BOOLEAN:return n.Bool(e);case ke.VariationType.NUMERIC:return n.Numeric(e);case ke.VariationType.INTEGER:return n.Numeric(e);case ke.VariationType.STRING:return n.String(e);case ke.VariationType.JSON:return n.JSON(e);default:return n.String(e)}}toString(){var e,t,i;switch(this.valueType){case z.NullType:return"null";case z.BoolType:return this.boolValue?"true":"false";case z.NumericType:return this.numericValue?this.numericValue.toString():"0";case z.StringType:return(e=this.stringValue)!==null&&e!==void 0?e:"";case z.JSONType:try{return(t=JSON.stringify(this.objectValue))!==null&&t!==void 0?t:""}catch{return(i=this.stringValue)!==null&&i!==void 0?i:""}}}toHashedString(){let e=this.toString();return(0,is.getMD5Hash)(e)}static Bool(e){return new n(z.BoolType,e,void 0,void 0,void 0)}static Numeric(e){return new n(z.NumericType,void 0,e,void 0,void 0)}static String(e){return new n(z.StringType,void 0,void 0,e,void 0)}static JSON(e){return new n(z.JSONType,void 0,void 0,void 0,typeof e=="string"?JSON.parse(e):e)}static Null(){return new n(z.NullType,void 0,void 0,void 0,void 0)}};de.EppoValue=Jt});var Wt=y(Z=>{"use strict";m();p();Object.defineProperty(Z,"__esModule",{value:!0});Z.FlagEvaluationDetailsBuilder=Z.AllocationEvaluationCode=Z.flagEvaluationCodes=void 0;var rs=be();Z.flagEvaluationCodes=["MATCH","FLAG_UNRECOGNIZED_OR_DISABLED","TYPE_MISMATCH","ASSIGNMENT_ERROR","DEFAULT_ALLOCATION_NULL","NO_ACTIONS_SUPPLIED_FOR_BANDIT"];var Xe;(function(n){n.UNEVALUATED="UNEVALUATED",n.MATCH="MATCH",n.BEFORE_START_TIME="BEFORE_START_TIME",n.TRAFFIC_EXPOSURE_MISS="TRAFFIC_EXPOSURE_MISS",n.AFTER_END_TIME="AFTER_END_TIME",n.FAILING_RULE="FAILING_RULE"})(Xe=Z.AllocationEvaluationCode||(Z.AllocationEvaluationCode={}));var Qt=class{constructor(e,t,i,r){this.environmentName=e,this.allocations=t,this.configFetchedAt=i,this.configPublishedAt=r,this.variationKey=null,this.variationValue=null,this.matchedRule=null,this.matchedAllocation=null,this.unmatchedAllocations=[],this.unevaluatedAllocations=[],this.setNone=()=>(this.variationKey=null,this.variationValue=null,this.matchedRule=null,this.matchedAllocation=null,this.unmatchedAllocations=[],this.unevaluatedAllocations=this.allocations.map((o,g)=>({key:o.key,allocationEvaluationCode:Xe.UNEVALUATED,orderPosition:g+1})),this),this.setNoMatchFound=(o=[])=>(this.variationKey=null,this.variationValue=null,this.matchedAllocation=null,this.matchedRule=null,this.unmatchedAllocations=o,this.unevaluatedAllocations=[],this),this.setMatch=(o,g,a,f,s,u)=>{this.variationKey=g.key,this.variationValue=u===rs.VariationType.JSON&&typeof g.value=="string"?JSON.parse(g.value):g.value,this.matchedRule=f,this.matchedAllocation={key:a.key,allocationEvaluationCode:Xe.MATCH,orderPosition:o+1},this.unmatchedAllocations=s;let l=o+1,d=l+1;return this.unevaluatedAllocations=this.allocations.slice(l).map((c,S)=>({key:c.key,allocationEvaluationCode:Xe.UNEVALUATED,orderPosition:d+S})),this},this.buildForNoneResult=(o,g)=>this.setNone().build(o,g),this.build=(o,g)=>({environmentName:this.environmentName,flagEvaluationCode:o,flagEvaluationDescription:g,variationKey:this.variationKey,variationValue:this.variationValue,banditKey:null,banditAction:null,configFetchedAt:this.configFetchedAt,configPublishedAt:this.configPublishedAt,matchedRule:this.matchedRule,matchedAllocation:this.matchedAllocation,unmatchedAllocations:this.unmatchedAllocations,unevaluatedAllocations:this.unevaluatedAllocations}),this.setNone()}};Z.FlagEvaluationDetailsBuilder=Qt});var Ri=y(J=>{"use strict";m();p();Object.defineProperty(J,"__esModule",{value:!0});J.matchesRule=J.ObfuscatedOperatorType=J.OperatorType=void 0;var G=wr(),x=ge(),$;(function(n){n.MATCHES="MATCHES",n.NOT_MATCHES="NOT_MATCHES",n.GTE="GTE",n.GT="GT",n.LTE="LTE",n.LT="LT",n.ONE_OF="ONE_OF",n.NOT_ONE_OF="NOT_ONE_OF",n.IS_NULL="IS_NULL"})($=J.OperatorType||(J.OperatorType={}));var H;(function(n){n.MATCHES="05015086bdd8402218f6aad6528bef08",n.NOT_MATCHES="8323761667755378c3a78e0a6ed37a78",n.GTE="32d35312e8f24bc1669bd2b45c00d47c",n.GT="cd6a9bd2a175104eed40f0d33a8b4020",n.LTE="cc981ecc65ecf63ad1673cbec9c64198",n.LT="c562607189d77eb9dfb707464c1e7b0b",n.ONE_OF="27457ce369f2a74203396a35ef537c0b",n.NOT_ONE_OF="602f5ee0b6e84fe29f43ab48b9e1addf",n.IS_NULL="dbd9c38e0339e6c34bd48cafc59be388"})(H=J.ObfuscatedOperatorType||(J.ObfuscatedOperatorType={}));var F;(function(n){n.PLAIN_STRING="PLAIN_STRING",n.STRING_ARRAY="STRING_ARRAY",n.SEM_VER="SEM_VER",n.NUMERIC="NUMERIC"})(F||(F={}));function ss(n,e,t){return!os(e,n.conditions,t).includes(!1)}J.matchesRule=ss;function os(n,e,t){return e.map(i=>t?ls(Object.entries(n).reduce((r,[o,g])=>Object.assign({[(0,x.getMD5Hash)(o)]:g},r),{}),i):as(n,i))}function as(n,e){let t=n[e.attribute],i=Li(e.value);if(e.operator===$.IS_NULL)return e.value?t==null:t!=null;if(t!=null)switch(e.operator){case $.GTE:return i===F.SEM_VER?se(t,e.value,G.gte):re(t,e.value,(r,o)=>r>=o);case $.GT:return i===F.SEM_VER?se(t,e.value,G.gt):re(t,e.value,(r,o)=>r>o);case $.LTE:return i===F.SEM_VER?se(t,e.value,G.lte):re(t,e.value,(r,o)=>r<=o);case $.LT:return i===F.SEM_VER?se(t,e.value,G.lt):re(t,e.value,(r,o)=>r<o);case $.MATCHES:return new RegExp(e.value).test(t);case $.NOT_MATCHES:return!new RegExp(e.value).test(t);case $.ONE_OF:return Ci(t.toString(),e.value);case $.NOT_ONE_OF:return Ii(t.toString(),e.value)}return!1}function ls(n,e){let t=n[e.attribute],i=Li(t);if(e.operator===H.IS_NULL)return e.value===(0,x.getMD5Hash)("true")?t==null:t!=null;if(t!=null)switch(e.operator){case H.GTE:return i===F.SEM_VER?se(t,(0,x.decodeBase64)(e.value),G.gte):re(t,Number((0,x.decodeBase64)(e.value)),(r,o)=>r>=o);case H.GT:return i===F.SEM_VER?se(t,(0,x.decodeBase64)(e.value),G.gt):re(t,Number((0,x.decodeBase64)(e.value)),(r,o)=>r>o);case H.LTE:return i===F.SEM_VER?se(t,(0,x.decodeBase64)(e.value),G.lte):re(t,Number((0,x.decodeBase64)(e.value)),(r,o)=>r<=o);case H.LT:return i===F.SEM_VER?se(t,(0,x.decodeBase64)(e.value),G.lt):re(t,Number((0,x.decodeBase64)(e.value)),(r,o)=>r<o);case H.MATCHES:return new RegExp((0,x.decodeBase64)(e.value)).test(t);case H.NOT_MATCHES:return!new RegExp((0,x.decodeBase64)(e.value)).test(t);case H.ONE_OF:return Ci((0,x.getMD5Hash)(t.toString()),e.value);case H.NOT_ONE_OF:return Ii((0,x.getMD5Hash)(t.toString()),e.value)}return!1}function Ci(n,e){return Ni(n,e).length>0}function Ii(n,e){return Ni(n,e).length===0}function Ni(n,e){return e.filter(t=>t===n)}function re(n,e,t){return t(Number(n),Number(e))}function se(n,e,t){return!!(0,G.valid)(n)&&!!(0,G.valid)(e)&&t(n,e)}function Li(n){return typeof n=="number"?F.NUMERIC:Array.isArray(n)?F.STRING_ARRAY:typeof n=="string"&&(0,G.valid)(n)?F.SEM_VER:isNaN(Number(n))?F.PLAIN_STRING:F.NUMERIC}});var xi=y(j=>{"use strict";m();p();Object.defineProperty(j,"__esModule",{value:!0});j.matchesRules=j.noneResult=j.hashKey=j.isInShardRange=j.Evaluator=void 0;var Ue=Wt(),us=Ri(),cs=Vt(),Yt=class{constructor(e){this.getMatchedEvaluationDetailsMessage=(t,i,r)=>{var o;let g=!!(!((o=t.rules)===null||o===void 0)&&o.length),a=t.splits.length>1,f=i.shards.length>1,s=a||f;return g&&s?`Supplied attributes match rules defined in allocation "${t.key}" and ${r} belongs to the range of traffic assigned to "${i.variationKey}".`:g&&!s?`Supplied attributes match rules defined in allocation "${t.key}".`:`${r} belongs to the range of traffic assigned to "${i.variationKey}" defined in allocation "${t.key}".`},this.sharder=e??new cs.MD5Sharder}evaluateFlag(e,t,i,r,o,g){var a,f;let s=new Ue.FlagEvaluationDetailsBuilder(t.configEnvironment.name,e.allocations,t.configFetchedAt,t.configPublishedAt);if(!e.enabled)return Zt(e.key,i,r,s.buildForNoneResult("FLAG_UNRECOGNIZED_OR_DISABLED",`Unrecognized or disabled flag: ${e.key}`));let u=new Date,l=[];for(let d=0;d<e.allocations.length;d++){let c=e.allocations[d],S=A=>{l.push({key:c.key,allocationEvaluationCode:A,orderPosition:d+1})};if(c.startAt&&u<new Date(c.startAt)){S(Ue.AllocationEvaluationCode.BEFORE_START_TIME);continue}if(c.endAt&&u>new Date(c.endAt)){S(Ue.AllocationEvaluationCode.AFTER_END_TIME);continue}let{matched:b,matchedRule:_}=Mi((a=c?.rules)!==null&&a!==void 0?a:[],Object.assign({id:i},r),o);if(b){for(let A of c.splits)if(A.shards.every(O=>this.matchesShard(O,i,e.totalShards))){let O=e.variations[A.variationKey],N=s.setMatch(d,O,c,_,l,g).build("MATCH",this.getMatchedEvaluationDetailsMessage(c,A,i));return{flagKey:e.key,subjectKey:i,subjectAttributes:r,allocationKey:c.key,variation:O,extraLogging:(f=A.extraLogging)!==null&&f!==void 0?f:{},doLog:c.doLog,flagEvaluationDetails:N}}S(Ue.AllocationEvaluationCode.TRAFFIC_EXPOSURE_MISS)}else S(Ue.AllocationEvaluationCode.FAILING_RULE)}return Zt(e.key,i,r,s.setNoMatchFound(l).build("DEFAULT_ALLOCATION_NULL",'No allocations matched. Falling back to "Default Allocation", serving NULL'))}matchesShard(e,t,i){let r=this.sharder.getShard(Di(e.salt,t),i);return e.ranges.some(o=>Fi(r,o))}};j.Evaluator=Yt;function Fi(n,e){return e.start<=n&&n<e.end}j.isInShardRange=Fi;function Di(n,e){return`${n}-${e}`}j.hashKey=Di;function Zt(n,e,t,i){return{flagKey:n,subjectKey:e,subjectAttributes:t,allocationKey:null,variation:null,extraLogging:{},doLog:!1,flagEvaluationDetails:i}}j.noneResult=Zt;function Mi(n,e,t){if(!n.length)return{matched:!0,matchedRule:null};let i=null;return n.some(o=>{let g=(0,us.matchesRule)(o,e,t);return g&&(i=o),g})?{matched:!0,matchedRule:i}:{matched:!1,matchedRule:null}}j.matchesRules=Mi});var en=y(je=>{"use strict";m();p();Object.defineProperty(je,"__esModule",{value:!0});je.HttpRequestError=void 0;var fe=class extends Error{constructor(e,t,i){super(e),this.message=e,this.status=t,this.cause=i,i&&(this.cause=i)}};je.HttpRequestError=fe;var Xt=class{constructor(e,t){this.apiEndpoints=e,this.timeout=t}async getUniversalFlagConfiguration(){let e=this.apiEndpoints.ufcEndpoint();return await this.rawGet(e)}async getBanditParameters(){let e=this.apiEndpoints.banditParametersEndpoint();return await this.rawGet(e)}async rawGet(e){try{let t=new AbortController,i=t.signal,r=setTimeout(()=>t.abort(),this.timeout),o=await fetch(e.toString(),{signal:i});if(clearTimeout(r),!o?.ok)throw new fe("Failed to fetch data",o?.status);return await o.json()}catch(t){throw t.name==="AbortError"?new fe("Request timed out",408,t):t instanceof fe?t:new fe("Network error",0,t)}}};je.default=Xt});var Pi=y(et=>{"use strict";m();p();Object.defineProperty(et,"__esModule",{value:!0});et.waitForMs=void 0;async function gs(n){await new Promise(e=>setTimeout(e,n))}et.waitForMs=gs});var ki=y(tn=>{"use strict";m();p();Object.defineProperty(tn,"__esModule",{value:!0});var P=Me(),tt=Se(),ds=Pi();function fs(n,e,t){let i=!1,r=0,o=n,g=!1,a,f=async()=>{var l;i=!1;let d=!1,c=t?.skipInitialPoll?0:1+((l=t?.maxStartRetries)!==null&&l!==void 0?l:tt.DEFAULT_INITIAL_CONFIG_REQUEST_RETRIES),S=null;for(;!d&&c>0;)try{await e(),d=!0,g=!1,P.logger.info("Eppo SDK successfully requested initial configuration")}catch(_){if(g=!0,P.logger.warn(`Eppo SDK encountered an error with initial poll of configurations: ${_.message}`),--c>0){let A=Bi(n);P.logger.warn(`Eppo SDK will retry the initial poll again in ${A} ms (${c} attempts remaining)`),await(0,ds.waitForMs)(A)}else t?.pollAfterFailedStart?P.logger.warn("Eppo SDK initial poll failed; will attempt regular polling"):(P.logger.error("Eppo SDK initial poll failed. Aborting polling"),s()),t?.errorOnFailedStart&&(S=_)}if(!i&&(d&&t?.pollAfterSuccessfulStart||!d&&t?.pollAfterFailedStart)?(P.logger.info(`Eppo SDK starting regularly polling every ${n} ms`),a=setTimeout(u,n)):P.logger.info("Eppo SDK will not poll for configuration updates"),S)throw P.logger.info("Eppo SDK rethrowing start error"),S},s=()=>{i||(i=!0,a&&clearTimeout(a),P.logger.info("Eppo SDK polling stopped"))};async function u(){var l;if(!i){try{await e(),r=0,o=n,g&&(g=!1,P.logger.info("Eppo SDK poll successful; resuming normal polling"))}catch(d){g=!0,P.logger.warn(`Eppo SDK encountered an error polling configurations: ${d.message}`);let c=1+((l=t?.maxPollRetries)!==null&&l!==void 0?l:tt.DEFAULT_POLL_CONFIG_REQUEST_RETRIES);if(++r<c){let S=Math.pow(2,r),b=Bi(n);o=S*n+b,P.logger.warn(`Eppo SDK will try polling again in ${o} ms (${c-r} attempts remaining)`)}else P.logger.error(`Eppo SDK reached maximum of ${r} failed polling attempts. Stopping polling`),s()}setTimeout(u,o)}}return{start:f,stop:s}}tn.default=fs;function Bi(n){let e=n*tt.POLL_JITTER_PCT/2,t=Math.max(Math.floor(Math.random()*n*tt.POLL_JITTER_PCT/2),1);return e+t}});var nn=y(_e=>{"use strict";m();p();Object.defineProperty(_e,"__esModule",{value:!0});_e.validateNotBlank=_e.InvalidArgumentError=void 0;var nt=class extends Error{};_e.InvalidArgumentError=nt;function hs(n,e){if(n==null||n.length===0)throw new nt(e)}_e.validateNotBlank=hs});var Ui=y((Pa,ms)=>{ms.exports={name:"@eppo/js-client-sdk-common",version:"4.0.0",description:"Eppo SDK for client-side JavaScript applications (base for both web and react native)",main:"dist/index.js",files:["/dist","/src","!*.spec.ts"],types:"./dist/index.d.ts",engines:{node:">=18.x"},exports:{".":{types:"./dist/index.d.ts",default:"./dist/index.js"}},scripts:{lint:"eslint '**/*.{ts,tsx}' --cache","lint:fix":"eslint --fix '**/*.{ts,tsx}' --cache","lint:fix-pre-commit":"eslint -c .eslintrc.pre-commit.js --fix '**/*.{ts,tsx}' --no-eslintrc --cache",prepare:"make prepare","pre-commit":"lint-staged && tsc",typecheck:"tsc",test:"yarn test:unit","test:unit":"NODE_ENV=test jest '.*\\.spec\\.ts'","obfuscate-mock-ufc":"ts-node test/writeObfuscatedMockUFC"},jsdelivr:"dist/eppo-sdk.js",repository:{type:"git",url:"git+https://github.com/Eppo-exp/js-client-sdk-common.git"},author:"",license:"MIT",bugs:{url:"https://github.com/Eppo-exp/js-client-sdk-common/issues"},homepage:"https://github.com/Eppo-exp/js-client-sdk-common#readme",devDependencies:{"@types/jest":"^29.5.11","@types/js-base64":"^3.3.1","@types/lodash":"^4.17.5","@types/md5":"^2.3.2","@types/semver":"^7.5.6","@typescript-eslint/eslint-plugin":"^5.13.0","@typescript-eslint/parser":"^5.13.0",eslint:"^8.17.0","eslint-config-prettier":"^8.5.0","eslint-import-resolver-typescript":"^2.5.0","eslint-plugin-import":"^2.25.4","eslint-plugin-prettier":"^4.0.0","eslint-plugin-promise":"^6.0.0",jest:"^29.7.0","jest-environment-jsdom":"^29.7.0",lodash:"^4.17.21",prettier:"^2.7.1","terser-webpack-plugin":"^5.3.3",testdouble:"^3.20.1","ts-jest":"^29.1.1","ts-loader":"^9.3.1","ts-node":"^10.9.1",typescript:"^4.7.4",webpack:"^5.73.0","webpack-cli":"^4.10.0"},dependencies:{"js-base64":"^3.7.7",md5:"^2.3.0",pino:"^8.19.0",semver:"^7.5.4"}}});var ji=y(it=>{"use strict";m();p();Object.defineProperty(it,"__esModule",{value:!0});it.LIB_VERSION=void 0;var ps=Ui();it.LIB_VERSION=ps.version});var $i=y(me=>{"use strict";m();p();Object.defineProperty(me,"__esModule",{value:!0});me.checkValueTypeMatch=me.checkTypeMatch=void 0;var vs=Ct(),X=Me(),Ss=Si(),qi=Ut(),Es=$t(),ye=Se(),bs=Ti(),he=Oi(),rt=xi(),Vi=Wt(),_s=en(),Q=be(),ys=ge(),As=ki(),zi=nn(),ws=ji(),rn=class{constructor(e,t,i,r,o=!1){this.flagConfigurationStore=e,this.banditVariationConfigurationStore=t,this.banditModelConfigurationStore=i,this.configurationRequestParameters=r,this.isObfuscated=o,this.queuedAssignmentEvents=[],this.queuedBanditEvents=[],this.isGracefulFailureMode=!0,this.evaluator=new rt.Evaluator,this.banditEvaluator=new Ss.BanditEvaluator}setConfigurationRequestParameters(e){this.configurationRequestParameters=e}setFlagConfigurationStore(e){this.flagConfigurationStore=e}setBanditVariationConfigurationStore(e){this.banditVariationConfigurationStore=e}setBanditModelConfigurationStore(e){this.banditModelConfigurationStore=e}setIsObfuscated(e){this.isObfuscated=e}async fetchFlagConfigurations(){var e,t;if(!this.configurationRequestParameters)throw new Error("Eppo SDK unable to fetch flag configurations without configuration request parameters");if(this.requestPoller&&this.requestPoller.stop(),!await this.flagConfigurationStore.isExpired()){X.logger.info("[Eppo SDK] Configuration store is not expired. Skipping fetching flag configurations");return}let{apiKey:r,sdkName:o,sdkVersion:g,baseUrl:a,requestTimeoutMs:f=ye.DEFAULT_REQUEST_TIMEOUT_MS,numInitialRequestRetries:s=ye.DEFAULT_INITIAL_CONFIG_REQUEST_RETRIES,numPollRequestRetries:u=ye.DEFAULT_POLL_CONFIG_REQUEST_RETRIES,pollAfterSuccessfulInitialization:l=!1,pollAfterFailedInitialization:d=!1,throwOnFailedInitialization:c=!1,skipInitialPoll:S=!1}=this.configurationRequestParameters,b=new vs.default({baseUrl:a,queryParams:{apiKey:r,sdkName:o,sdkVersion:g}}),_=new _s.default(b,f),A=new Es.default(_,this.flagConfigurationStore,(e=this.banditVariationConfigurationStore)!==null&&e!==void 0?e:null,(t=this.banditModelConfigurationStore)!==null&&t!==void 0?t:null);this.requestPoller=(0,As.default)(ye.POLL_INTERVAL_MS,A.fetchAndStoreConfigurations.bind(A),{maxStartRetries:s,maxPollRetries:u,pollAfterSuccessfulStart:l,pollAfterFailedStart:d,errorOnFailedStart:c,skipInitialPoll:S}),await this.requestPoller.start()}stopPolling(){this.requestPoller&&this.requestPoller.stop()}getStringAssignment(e,t,i,r){return this.getStringAssignmentDetails(e,t,i,r).variation}getStringAssignmentDetails(e,t,i,r){var o;let{eppoValue:g,flagEvaluationDetails:a}=this.getAssignmentVariation(e,t,i,he.EppoValue.String(r),Q.VariationType.STRING);return{variation:(o=g.stringValue)!==null&&o!==void 0?o:r,action:null,evaluationDetails:a}}getBoolAssignment(e,t,i,r){return this.getBooleanAssignment(e,t,i,r)}getBooleanAssignment(e,t,i,r){return this.getBooleanAssignmentDetails(e,t,i,r).variation}getBooleanAssignmentDetails(e,t,i,r){var o;let{eppoValue:g,flagEvaluationDetails:a}=this.getAssignmentVariation(e,t,i,he.EppoValue.Bool(r),Q.VariationType.BOOLEAN);return{variation:(o=g.boolValue)!==null&&o!==void 0?o:r,action:null,evaluationDetails:a}}getIntegerAssignment(e,t,i,r){return this.getIntegerAssignmentDetails(e,t,i,r).variation}getIntegerAssignmentDetails(e,t,i,r){var o;let{eppoValue:g,flagEvaluationDetails:a}=this.getAssignmentVariation(e,t,i,he.EppoValue.Numeric(r),Q.VariationType.INTEGER);return{variation:(o=g.numericValue)!==null&&o!==void 0?o:r,action:null,evaluationDetails:a}}getNumericAssignment(e,t,i,r){return this.getNumericAssignmentDetails(e,t,i,r).variation}getNumericAssignmentDetails(e,t,i,r){var o;let{eppoValue:g,flagEvaluationDetails:a}=this.getAssignmentVariation(e,t,i,he.EppoValue.Numeric(r),Q.VariationType.NUMERIC);return{variation:(o=g.numericValue)!==null&&o!==void 0?o:r,action:null,evaluationDetails:a}}getJSONAssignment(e,t,i,r){return this.getJSONAssignmentDetails(e,t,i,r).variation}getJSONAssignmentDetails(e,t,i,r){var o;let{eppoValue:g,flagEvaluationDetails:a}=this.getAssignmentVariation(e,t,i,he.EppoValue.JSON(r),Q.VariationType.JSON);return{variation:(o=g.objectValue)!==null&&o!==void 0?o:r,action:null,evaluationDetails:a}}getBanditAction(e,t,i,r,o){let{variation:g,action:a}=this.getBanditActionDetails(e,t,i,r,o);return{variation:g,action:a}}getBanditActionDetails(e,t,i,r,o){var g,a,f;let s=this.flagEvaluationDetailsBuilder(e),u={variation:o,action:null},l=o,d=null;try{let c=(g=this.banditVariationConfigurationStore)===null||g===void 0?void 0:g.get(e);if(c&&!Object.keys(r).length)return Object.assign(Object.assign({},u),{evaluationDetails:s.buildForNoneResult("NO_ACTIONS_SUPPLIED_FOR_BANDIT","No bandit actions passed for a flag known to have an active bandit")});let S=this.ensureNonContextualSubjectAttributes(i),{variation:b,evaluationDetails:_}=this.getStringAssignmentDetails(e,t,S,o);l=b;let A=(a=c?.find(O=>O.variationValue===l))===null||a===void 0?void 0:a.key;if(A){let O=(f=this.banditModelConfigurationStore)===null||f===void 0?void 0:f.get(A);if(!O)throw new Error("No model parameters for bandit "+A);let N=O.modelData,M=this.ensureContextualSubjectAttributes(i),C=this.ensureActionsWithContextualAttributes(r),B=this.banditEvaluator.evaluateBandit(e,t,M,C,N);d=B.actionKey,_.banditAction=d,_.banditKey=A;let ee={timestamp:new Date().toISOString(),featureFlag:e,bandit:A,subject:t,action:d,actionProbability:B.actionWeight,optimalityGap:B.optimalityGap,modelVersion:O.modelVersion,subjectNumericAttributes:M.numericAttributes,subjectCategoricalAttributes:M.categoricalAttributes,actionNumericAttributes:C[d].numericAttributes,actionCategoricalAttributes:C[d].categoricalAttributes,metaData:this.buildLoggerMetadata(),evaluationDetails:_};this.logBanditAction(ee)}return{variation:l,action:d,evaluationDetails:_}}catch(c){if(X.logger.error("Error evaluating bandit action",c),!this.isGracefulFailureMode)throw c;return Object.assign(Object.assign({},u),{evaluationDetails:s.buildForNoneResult("ASSIGNMENT_ERROR",`Error evaluating bandit action: ${c.message}`)})}}ensureNonContextualSubjectAttributes(e){let t;if(this.isInstanceOfContextualAttributes(e)){let i=e;t=Object.assign(Object.assign({},i.numericAttributes),i.categoricalAttributes)}else t=e;return t}ensureContextualSubjectAttributes(e){let t;return this.isInstanceOfContextualAttributes(e)?t=e:t=this.deduceAttributeContext(e),t}ensureActionsWithContextualAttributes(e){let t={};return Array.isArray(e)?e.forEach(i=>{t[i]={numericAttributes:{},categoricalAttributes:{}}}):Object.values(e).every(this.isInstanceOfContextualAttributes)?t=e:Object.entries(e).forEach(([i,r])=>{t[i]=this.deduceAttributeContext(r)}),t}isInstanceOfContextualAttributes(e){return!!(typeof e=="object"&&e&&"numericAttributes"in e&&"categoricalAttributes"in e)}deduceAttributeContext(e){let t={numericAttributes:{},categoricalAttributes:{}};return Object.entries(e).forEach(([i,r])=>{typeof r=="number"&&isFinite(r)?t.numericAttributes[i]=r:t.categoricalAttributes[i]=r}),t}logBanditAction(e){if(!this.banditLogger){this.queuedBanditEvents.length<ye.MAX_EVENT_QUEUE_SIZE&&this.queuedBanditEvents.push(e);return}try{this.banditLogger.logBanditAction(e)}catch(t){X.logger.warn("Error encountered logging bandit action",t)}}getAssignmentVariation(e,t,i,r,o){try{let g=this.getAssignmentDetail(e,t,i,o);return g.variation?{eppoValue:he.EppoValue.valueOf(g.variation.value,o),flagEvaluationDetails:g.flagEvaluationDetails}:{eppoValue:r,flagEvaluationDetails:g.flagEvaluationDetails}}catch(g){let a=this.rethrowIfNotGraceful(g,r),f=new Vi.FlagEvaluationDetailsBuilder("",[],"","").buildForNoneResult("ASSIGNMENT_ERROR",`Assignment Error: ${g.message}`);return{eppoValue:a,flagEvaluationDetails:f}}}rethrowIfNotGraceful(e,t){if(this.isGracefulFailureMode)return X.logger.error(`[Eppo SDK] Error getting assignment: ${e.message}`),t??he.EppoValue.Null();throw e}getAssignmentDetail(e,t,i={},r){(0,zi.validateNotBlank)(t,"Invalid argument: subjectKey cannot be blank"),(0,zi.validateNotBlank)(e,"Invalid argument: flagKey cannot be blank");let o=this.flagEvaluationDetailsBuilder(e),g=this.getConfigDetails(),a=this.getFlag(e);if(a===null){X.logger.warn(`[Eppo SDK] No assigned variation. Flag not found: ${e}`);let s=o.buildForNoneResult("FLAG_UNRECOGNIZED_OR_DISABLED",`Unrecognized or disabled flag: ${e}`);return(0,rt.noneResult)(e,t,i,s)}if(!Gi(r,a.variationType))throw new TypeError(`Variation value does not have the correct type. Found: ${a.variationType} != ${r} for flag ${e}`);if(!a.enabled){X.logger.info(`[Eppo SDK] No assigned variation. Flag is disabled: ${e}`);let s=o.buildForNoneResult("FLAG_UNRECOGNIZED_OR_DISABLED",`Unrecognized or disabled flag: ${e}`);return(0,rt.noneResult)(e,t,i,s)}let f=this.evaluator.evaluateFlag(a,g,t,i,this.isObfuscated,r);if(this.isObfuscated&&(f.flagKey=e),f?.variation&&!Ki(r,f.variation.value)){let{key:s,value:u}=f.variation,l=`Expected variation type ${r} does not match for variation '${s}' with value ${u}`,d=o.buildForNoneResult("TYPE_MISMATCH",l);return(0,rt.noneResult)(e,t,i,d)}try{f?.doLog&&this.logAssignment(f)}catch(s){X.logger.error(`[Eppo SDK] Error logging assignment event: ${s}`)}return f}flagEvaluationDetailsBuilder(e){var t;let i=this.getFlag(e),r=this.getConfigDetails();return new Vi.FlagEvaluationDetailsBuilder(r.configEnvironment.name,(t=i?.allocations)!==null&&t!==void 0?t:[],r.configFetchedAt,r.configPublishedAt)}getConfigDetails(){var e,t,i;return{configFetchedAt:(e=this.flagConfigurationStore.getConfigFetchedAt())!==null&&e!==void 0?e:"",configPublishedAt:(t=this.flagConfigurationStore.getConfigPublishedAt())!==null&&t!==void 0?t:"",configEnvironment:(i=this.flagConfigurationStore.getEnvironment())!==null&&i!==void 0?i:{name:""}}}getFlag(e){return this.isObfuscated?this.getObfuscatedFlag(e):this.flagConfigurationStore.get(e)}getObfuscatedFlag(e){let t=this.flagConfigurationStore.get((0,ys.getMD5Hash)(e));return t?(0,bs.decodeFlag)(t):null}getFlagKeys(){return this.flagConfigurationStore.getKeys()}isInitialized(){return this.flagConfigurationStore.isInitialized()&&(!this.banditVariationConfigurationStore||this.banditVariationConfigurationStore.isInitialized())&&(!this.banditModelConfigurationStore||this.banditModelConfigurationStore.isInitialized())}setLogger(e){this.setAssignmentLogger(e)}setAssignmentLogger(e){var t;this.assignmentLogger=e,this.flushQueuedEvents(this.queuedAssignmentEvents,(t=this.assignmentLogger)===null||t===void 0?void 0:t.logAssignment)}setBanditLogger(e){var t;this.banditLogger=e,this.flushQueuedEvents(this.queuedBanditEvents,(t=this.banditLogger)===null||t===void 0?void 0:t.logBanditAction)}disableAssignmentCache(){this.assignmentCache=void 0}useNonExpiringInMemoryAssignmentCache(){this.assignmentCache=new qi.NonExpiringInMemoryAssignmentCache}useLRUInMemoryAssignmentCache(e){this.assignmentCache=new qi.LRUInMemoryAssignmentCache(e)}useCustomAssignmentCache(e){this.assignmentCache=e}setIsGracefulFailureMode(e){this.isGracefulFailureMode=e}getFlagConfigurations(){return this.flagConfigurationStore.entries()}flushQueuedEvents(e,t){let i=[...e];e.length=0,t&&i.forEach(r=>{try{t(r)}catch(o){X.logger.error(`[Eppo SDK] Error flushing event to logger: ${o.message}`)}})}logAssignment(e){var t,i,r,o,g;let{flagKey:a,subjectKey:f,allocationKey:s,subjectAttributes:u,variation:l}=e,d=Object.assign(Object.assign({},(t=e.extraLogging)!==null&&t!==void 0?t:{}),{allocation:s??null,experiment:s?`${a}-${s}`:null,featureFlag:a,variation:(i=l?.key)!==null&&i!==void 0?i:null,subject:f,timestamp:new Date().toISOString(),subjectAttributes:u,metaData:this.buildLoggerMetadata(),evaluationDetails:e.flagEvaluationDetails});if(!(l&&s&&(!((r=this.assignmentCache)===null||r===void 0)&&r.has({flagKey:a,subjectKey:f,allocationKey:s,variationKey:l.key})))){if(!this.assignmentLogger){this.queuedAssignmentEvents.length<ye.MAX_EVENT_QUEUE_SIZE&&this.queuedAssignmentEvents.push(d);return}try{this.assignmentLogger.logAssignment(d),(o=this.assignmentCache)===null||o===void 0||o.set({flagKey:a,subjectKey:f,allocationKey:s??"__eppo_no_allocation",variationKey:(g=l?.key)!==null&&g!==void 0?g:"__eppo_no_variation"})}catch(c){X.logger.error(`[Eppo SDK] Error logging assignment event: ${c.message}`)}}}buildLoggerMetadata(){return{obfuscated:this.isObfuscated,sdkLanguage:"javascript",sdkLibVersion:ws.LIB_VERSION}}};me.default=rn;function Gi(n,e){return n===void 0||e===n}me.checkTypeMatch=Gi;function Ki(n,e){if(n==null)return!0;switch(n){case Q.VariationType.STRING:return typeof e=="string";case Q.VariationType.BOOLEAN:return typeof e=="boolean";case Q.VariationType.INTEGER:return typeof e=="number"&&Number.isInteger(e);case Q.VariationType.NUMERIC:return typeof e=="number";case Q.VariationType.JSON:return typeof e=="string";default:return!1}}me.checkValueTypeMatch=Ki});var Hi=y(ot=>{"use strict";m();p();Object.defineProperty(ot,"__esModule",{value:!0});ot.HybridConfigurationStore=void 0;var st=Me(),sn=class{constructor(e,t){this.servingStore=e,this.persistentStore=t,this.environment=null,this.configFetchedAt=null,this.configPublishedAt=null}async init(){if(!this.persistentStore)return;this.persistentStore.isInitialized()||st.logger.warn(`${st.loggerPrefix} Persistent store is not initialized from remote configuration. Serving assignments that may be stale.`);let e=await this.persistentStore.entries();this.servingStore.setEntries(e)}isInitialized(){var e,t;return this.servingStore.isInitialized()&&((t=(e=this.persistentStore)===null||e===void 0?void 0:e.isInitialized())!==null&&t!==void 0?t:!0)}async isExpired(){var e;let t=await((e=this.persistentStore)===null||e===void 0?void 0:e.isExpired());return t??!0}get(e){return this.servingStore.isInitialized()||st.logger.warn(`${st.loggerPrefix} getting a value from a ServingStore that is not initialized.`),this.servingStore.get(e)}entries(){return this.servingStore.entries()}getKeys(){return this.servingStore.getKeys()}async setEntries(e){return this.persistentStore&&await this.persistentStore.setEntries(e),this.servingStore.setEntries(e),!0}setEnvironment(e){this.environment=e}getEnvironment(){return this.environment}getConfigFetchedAt(){return this.configFetchedAt}setConfigFetchedAt(e){this.configFetchedAt=e}getConfigPublishedAt(){return this.configPublishedAt}setConfigPublishedAt(e){this.configPublishedAt=e}};ot.HybridConfigurationStore=sn});var Ji=y(Ae=>{"use strict";m();p();Object.defineProperty(Ae,"__esModule",{value:!0});Ae.MemoryOnlyConfigurationStore=Ae.MemoryStore=void 0;var at=class{constructor(){this.store={},this.initialized=!1}get(e){var t;return(t=this.store[e])!==null&&t!==void 0?t:null}entries(){return this.store}getKeys(){return Object.keys(this.store)}isInitialized(){return this.initialized}setEntries(e){this.store=Object.assign({},e),this.initialized=!0}};Ae.MemoryStore=at;var on=class{constructor(){this.servingStore=new at,this.initialized=!1,this.configFetchedAt=null,this.configPublishedAt=null,this.environment=null}init(){return this.initialized=!0,Promise.resolve()}get(e){return this.servingStore.get(e)}entries(){return this.servingStore.entries()}getKeys(){return this.servingStore.getKeys()}async isExpired(){return!0}isInitialized(){return this.initialized}async setEntries(e){return this.servingStore.setEntries(e),this.initialized=!0,!0}getEnvironment(){return this.environment}setEnvironment(e){this.environment=e}getConfigFetchedAt(){return this.configFetchedAt}setConfigFetchedAt(e){this.configFetchedAt=e}getConfigPublishedAt(){return this.configPublishedAt}setConfigPublishedAt(e){this.configPublishedAt=e}};Ae.MemoryOnlyConfigurationStore=on});var pe=y(w=>{"use strict";m();p();Object.defineProperty(w,"__esModule",{value:!0});w.VariationType=w.assignmentCacheValueToString=w.assignmentCacheKeyToString=w.LRUInMemoryAssignmentCache=w.NonExpiringInMemoryAssignmentCache=w.MemoryOnlyConfigurationStore=w.HybridConfigurationStore=w.MemoryStore=w.validation=w.HttpClient=w.FlagConfigRequestor=w.ApiEndpoints=w.constants=w.EppoClient=w.AbstractAssignmentCache=w.applicationLogger=void 0;var Ts=Ct();w.ApiEndpoints=Ts.default;var Os=Me();Object.defineProperty(w,"applicationLogger",{enumerable:!0,get:function(){return Os.logger}});var qe=Ut();Object.defineProperty(w,"AbstractAssignmentCache",{enumerable:!0,get:function(){return qe.AbstractAssignmentCache}});Object.defineProperty(w,"NonExpiringInMemoryAssignmentCache",{enumerable:!0,get:function(){return qe.NonExpiringInMemoryAssignmentCache}});Object.defineProperty(w,"LRUInMemoryAssignmentCache",{enumerable:!0,get:function(){return qe.LRUInMemoryAssignmentCache}});Object.defineProperty(w,"assignmentCacheKeyToString",{enumerable:!0,get:function(){return qe.assignmentCacheKeyToString}});Object.defineProperty(w,"assignmentCacheValueToString",{enumerable:!0,get:function(){return qe.assignmentCacheValueToString}});var Cs=$i();w.EppoClient=Cs.default;var Is=$t();w.FlagConfigRequestor=Is.default;var Ns=Hi();Object.defineProperty(w,"HybridConfigurationStore",{enumerable:!0,get:function(){return Ns.HybridConfigurationStore}});var Qi=Ji();Object.defineProperty(w,"MemoryStore",{enumerable:!0,get:function(){return Qi.MemoryStore}});Object.defineProperty(w,"MemoryOnlyConfigurationStore",{enumerable:!0,get:function(){return Qi.MemoryOnlyConfigurationStore}});var Ls=Se();w.constants=Ls;var Rs=en();w.HttpClient=Rs.default;var Fs=be();Object.defineProperty(w,"VariationType",{enumerable:!0,get:function(){return Fs.VariationType}});var Ds=nn();w.validation=Ds});var un=y(ln=>{"use strict";m();p();Object.defineProperty(ln,"__esModule",{value:!0});var an=class{constructor(e){this.storage=e}async has(e){return!!await this.get(e)}async get(e){var t;let i=await this.storage.get(e);return(t=i?.[e])!==null&&t!==void 0?t:void 0}async entries(){return await this.storage.get(null)}async set(e,t){await this.storage.set({[e]:t})}};ln.default=an});var cn=y(we=>{"use strict";m();p();Object.defineProperty(we,"__esModule",{value:!0});we.META_KEY=we.CONFIGURATION_KEY=void 0;we.CONFIGURATION_KEY="eppo-configuration";we.META_KEY="eppo-configuration-meta"});var dn=y(lt=>{"use strict";m();p();Object.defineProperty(lt,"__esModule",{value:!0});lt.ChromeStorageEngine=void 0;var Wi=cn(),gn=class{constructor(e,t){this.storageMap=e,this.getContentsJsonString=async()=>{let r=await this.storageMap.get(this.contentsKey);return r??null},this.getMetaJsonString=async()=>{let r=await this.storageMap.get(this.metaKey);return r??null},this.setContentsJsonString=async r=>{await this.storageMap.set(this.contentsKey,r)},this.setMetaJsonString=async r=>{await this.storageMap.set(this.metaKey,r)};let i=t?`-${t}`:"";this.contentsKey=Wi.CONFIGURATION_KEY+i,this.metaKey=Wi.META_KEY+i}};lt.ChromeStorageEngine=gn});var Yi=y(ut=>{"use strict";m();p();Object.defineProperty(ut,"__esModule",{value:!0});ut.IsolatableHybridConfigurationStore=void 0;var Ms=pe(),fn=class extends Ms.HybridConfigurationStore{constructor(e,t,i="always"){super(e,t),this.servingStoreUpdateStrategy=i}async setEntries(e){var t;this.persistentStore&&await this.persistentStore.setEntries(e);let i=!this.persistentStore||await this.persistentStore.isExpired(),r=!(!((t=this.servingStore.getKeys())===null||t===void 0)&&t.length),o=this.servingStoreUpdateStrategy==="always"||i&&this.servingStoreUpdateStrategy==="expired"||i&&r;return o&&this.servingStore.setEntries(e),o}};ut.IsolatableHybridConfigurationStore=fn});var Xi=y(ct=>{"use strict";m();p();Object.defineProperty(ct,"__esModule",{value:!0});ct.LocalStorageEngine=void 0;var Zi=cn(),hn=class{constructor(e,t){this.localStorage=e,this.getContentsJsonString=async()=>this.localStorage.getItem(this.contentsKey),this.getMetaJsonString=async()=>this.localStorage.getItem(this.metaKey),this.setContentsJsonString=async r=>{this.localStorage.setItem(this.contentsKey,r)},this.setMetaJsonString=async r=>{this.localStorage.setItem(this.metaKey,r)};let i=t?`-${t}`:"";this.contentsKey=Zi.CONFIGURATION_KEY+i,this.metaKey=Zi.META_KEY+i}};ct.LocalStorageEngine=hn});var er=y(gt=>{"use strict";m();p();Object.defineProperty(gt,"__esModule",{value:!0});gt.StringValuedAsyncStore=void 0;var mn=class{constructor(e,t=0){this.storageEngine=e,this.cooldownSeconds=t,this.initialized=!1}isInitialized(){return this.initialized}async isExpired(){if(!this.cooldownSeconds)return!0;let e=await this.storageEngine.getMetaJsonString(),t=!0;if(e){let r=JSON.parse(e).lastUpdatedAtMs;t=!r||Date.now()-r>this.cooldownSeconds*1e3}return t}async entries(){let e=await this.storageEngine.getContentsJsonString();return e?JSON.parse(e):{}}async setEntries(e){await this.storageEngine.setContentsJsonString(JSON.stringify(e));let t={lastUpdatedAtMs:new Date().getTime()};await this.storageEngine.setMetaJsonString(JSON.stringify(t)),this.initialized=!0}};gt.StringValuedAsyncStore=mn});var dt=y(q=>{"use strict";m();p();Object.defineProperty(q,"__esModule",{value:!0});q.localStorageIfAvailable=q.hasWindowLocalStorage=q.chromeStorageIfAvailable=q.hasChromeStorage=q.configurationStorageFactory=void 0;var Ve=pe(),xs=un(),Ps=dn(),pn=Yi(),Bs=Xi(),tr=er();function ks({maxAgeSeconds:n=0,servingStoreUpdateStrategy:e="always",hasChromeStorage:t=!1,hasWindowLocalStorage:i=!1,persistentStore:r=void 0,forceMemoryOnly:o=!1},{chromeStorage:g,windowLocalStorage:a,storageKeySuffix:f}={}){if(o)return new Ve.MemoryOnlyConfigurationStore;if(r)return new pn.IsolatableHybridConfigurationStore(new Ve.MemoryStore,r,e);if(t&&g){let s=new Ps.ChromeStorageEngine(new xs.default(g),f??"");return new pn.IsolatableHybridConfigurationStore(new Ve.MemoryStore,new tr.StringValuedAsyncStore(s,n),e)}else if(i&&a){let s=new Bs.LocalStorageEngine(a,f??"");return new pn.IsolatableHybridConfigurationStore(new Ve.MemoryStore,new tr.StringValuedAsyncStore(s,n),e)}return new Ve.MemoryOnlyConfigurationStore}q.configurationStorageFactory=ks;function nr(){return typeof chrome<"u"&&!!chrome.storage}q.hasChromeStorage=nr;function Us(){return nr()?chrome.storage.local:void 0}q.chromeStorageIfAvailable=Us;function ir(){try{return typeof self<"u"&&!!self.localStorage}catch{return!1}}q.hasWindowLocalStorage=ir;function js(){return ir()?self.localStorage:void 0}q.localStorageIfAvailable=js});var sr=y(Sn=>{"use strict";m();p();Object.defineProperty(Sn,"__esModule",{value:!0});var rr=pe(),qs=un(),vn=class{constructor(e){this.storage=new qs.default(e)}set(e){this.storage.set((0,rr.assignmentCacheKeyToString)(e),(0,rr.assignmentCacheValueToString)(e))}has(e){throw new Error("This should never be called for ChromeStorageAssignmentCache, use getEntries() instead.")}async getEntries(){let e=await this.storage.entries();return Object.entries(e).map(([t,i])=>[t,i])}};Sn.default=vn});var _n=y(bn=>{"use strict";m();p();Object.defineProperty(bn,"__esModule",{value:!0});var En=class{constructor(e,t){this.servingStore=e,this.persistentStore=t}async init(){let e=await this.persistentStore.getEntries();e&&this.servingStore.setEntries(e)}set(e){this.servingStore.set(e),this.persistentStore.set(e)}has(e){return this.servingStore.has(e)}};bn.default=En});var or=y(ft=>{"use strict";m();p();Object.defineProperty(ft,"__esModule",{value:!0});ft.LocalStorageAssignmentCache=void 0;var Vs=pe(),zs=dt(),yn=class extends Vs.AbstractAssignmentCache{constructor(e){super(new An(e))}setEntries(e){e.forEach(([t,i])=>{t&&i&&this.delegate.set(t,i)})}getEntries(){return Promise.resolve(Array.from(this.entries()))}};ft.LocalStorageAssignmentCache=yn;var An=class{constructor(e){if(!(0,zs.hasWindowLocalStorage)())throw new Error("LocalStorage is not available");let t=e?`-${e}`:"";this.localStorageKey=`eppo-assignment${t}`}clear(){this.getCache().clear()}delete(e){return this.getCache().delete(e)}forEach(e,t){this.getCache().forEach(e,t)}entries(){return this.getCache().entries()}keys(){return this.getCache().keys()}values(){return this.getCache().values()}[Symbol.iterator](){return this.getCache()[Symbol.iterator]()}has(e){return this.getCache().has(e)}get(e){var t;return(t=this.getCache().get(e))!==null&&t!==void 0?t:void 0}set(e,t){return this.setCache(this.getCache().set(e,t))}getCache(){let e=self.localStorage.getItem(this.localStorageKey);return e?new Map(JSON.parse(e)):new Map}setCache(e){return self.localStorage.setItem(this.localStorageKey,JSON.stringify(Array.from(e.entries()))),this}}});var ar=y(Tn=>{"use strict";m();p();Object.defineProperty(Tn,"__esModule",{value:!0});var Gs=pe(),wn=class{constructor(){this.store=new Map,this.cache=new Gs.NonExpiringInMemoryAssignmentCache(this.store)}set(e){this.cache.set(e)}has(e){return this.cache.has(e)}setEntries(e){let{store:t}=this;e.forEach(([i,r])=>t.set(i,r))}getEntries(){return Promise.resolve(Array.from(this.cache.entries()))}};Tn.default=wn});var ur=y(ht=>{"use strict";m();p();Object.defineProperty(ht,"__esModule",{value:!0});ht.assignmentCacheFactory=void 0;var Ks=dt(),$s=sr(),lr=_n(),Hs=or(),Js=ar();function Qs({forceMemoryOnly:n=!1,chromeStorage:e,storageKeySuffix:t}){let i=new Js.default;if(n)return i;if(e){let r=new $s.default(e);return new lr.default(i,r)}else if((0,Ks.hasWindowLocalStorage)()){let r=new Hs.LocalStorageAssignmentCache(t);return new lr.default(i,r)}else return i}ht.assignmentCacheFactory=Qs});var cr=y((Ml,Ws)=>{Ws.exports={name:"@eppo/js-client-sdk",version:"3.4.0",description:"Eppo SDK for client-side JavaScript applications",main:"dist/index.js",files:["/dist","/src","!*.spec.ts"],types:"dist/index.d.ts",scripts:{lint:"eslint '**/*.{ts,tsx}' '**/*.d.{ts,tsx}' --cache","lint:fix":"eslint --fix '**/*.{ts,tsx}' --cache","lint:fix-pre-commit":"eslint -c .eslintrc.pre-commit.js --fix '**/*.{ts,tsx}' --no-eslintrc --cache",prepare:"make prepare","pre-commit":"lint-staged && tsc && yarn docs",typecheck:"tsc",test:"yarn test:unit","test:unit":"NODE_ENV=test jest '.*\\.spec\\.ts'",docs:"api-documenter markdown -i ./temp -o ./docs"},jsdelivr:"dist/eppo-sdk.js",repository:{type:"git",url:"git+https://github.com/Eppo-exp/js-client-sdk.git"},author:"",license:"MIT",bugs:{url:"https://github.com/Eppo-exp/js-client-sdk/issues"},homepage:"https://github.com/Eppo-exp/js-client-sdk#readme",devDependencies:{"@microsoft/api-documenter":"^7.23.9","@microsoft/api-extractor":"^7.38.0","@types/chrome":"^0.0.268","@types/jest":"^29.5.11","@typescript-eslint/eslint-plugin":"^5.13.0","@typescript-eslint/parser":"^5.13.0",eslint:"^8.17.0","eslint-config-prettier":"^8.5.0","eslint-import-resolver-typescript":"^2.5.0","eslint-plugin-import":"^2.25.4","eslint-plugin-prettier":"^4.0.0","eslint-plugin-promise":"^6.0.0",husky:"^8.0.1",jest:"^29.7.0","jest-environment-jsdom":"^29.7.0","lint-staged":"^12.3.5",prettier:"^2.7.1","terser-webpack-plugin":"^5.3.3",testdouble:"^3.16.6","ts-jest":"^29.1.1","ts-loader":"^9.3.1","ts-node":"^10.9.2",typescript:"^4.7.4",webpack:"^5.73.0","webpack-cli":"^4.10.0"},dependencies:{"@eppo/js-client-sdk-common":"4.0.0"}}});var gr=y(Te=>{"use strict";m();p();Object.defineProperty(Te,"__esModule",{value:!0});Te.sdkName=Te.sdkVersion=void 0;var Ys=cr();Te.sdkVersion=Ys.version;Te.sdkName="js-client-sdk"});var hr=y(R=>{"use strict";m();p();Object.defineProperty(R,"__esModule",{value:!0});R.getConfigUrl=R.getInstance=R.init=R.offlineInit=R.buildStorageKeySuffix=R.EppoJSClient=R.ChromeStorageEngine=void 0;var K=pe(),dr=ur(),Zs=_n(),oe=dt(),mt=gr(),Xs=dn();Object.defineProperty(R,"ChromeStorageEngine",{enumerable:!0,get:function(){return Xs.ChromeStorageEngine}});var eo=(0,oe.configurationStorageFactory)({forceMemoryOnly:!0}),D=class n extends K.EppoClient{getStringAssignment(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getStringAssignment(e,t,i,r)}getStringAssignmentDetails(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getStringAssignmentDetails(e,t,i,r)}getBoolAssignment(e,t,i,r){return this.getBooleanAssignment(e,t,i,r)}getBooleanAssignment(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getBooleanAssignment(e,t,i,r)}getBooleanAssignmentDetails(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getBooleanAssignmentDetails(e,t,i,r)}getIntegerAssignment(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getIntegerAssignment(e,t,i,r)}getIntegerAssignmentDetails(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getIntegerAssignmentDetails(e,t,i,r)}getNumericAssignment(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getNumericAssignment(e,t,i,r)}getNumericAssignmentDetails(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getNumericAssignmentDetails(e,t,i,r)}getJSONAssignment(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getJSONAssignment(e,t,i,r)}getJSONAssignmentDetails(e,t,i,r){return n.getAssignmentInitializationCheck(),super.getJSONAssignmentDetails(e,t,i,r)}getBanditAction(e,t,i,r,o){return n.getAssignmentInitializationCheck(),super.getBanditAction(e,t,i,r,o)}getBanditActionDetails(e,t,i,r,o){return n.getAssignmentInitializationCheck(),super.getBanditActionDetails(e,t,i,r,o)}static getAssignmentInitializationCheck(){n.initialized||K.applicationLogger.warn("Eppo SDK assignment requested before init() completed")}};R.EppoJSClient=D;D.instance=new D(eo,void 0,void 0,void 0,!0);D.initialized=!1;function fr(n){return n.replace(/\W/g,"").substring(0,8)}R.buildStorageKeySuffix=fr;function to(n){var e,t;let i=(e=n.isObfuscated)!==null&&e!==void 0?e:!1,r=(t=n.throwOnFailedInitialization)!==null&&t!==void 0?t:!0;try{let o=(0,oe.configurationStorageFactory)({forceMemoryOnly:!0});o.setEntries(n.flagsConfiguration).catch(f=>K.applicationLogger.warn("Error setting flags for memory-only configuration store",f)),D.instance.setFlagConfigurationStore(o),D.instance.setIsObfuscated(i),n.assignmentLogger&&D.instance.setAssignmentLogger(n.assignmentLogger);let a=(0,dr.assignmentCacheFactory)({storageKeySuffix:"offline",forceMemoryOnly:!0});D.instance.useCustomAssignmentCache(a)}catch(o){if(K.applicationLogger.warn("Eppo SDK encountered an error initializing, assignment calls will return the default value and not be logged"),r)throw o}return D.initialized=!0,D.instance}R.offlineInit=to;async function no(n){var e,t,i,r,o,g,a;K.validation.validateNotBlank(n.apiKey,"API key required");let f,s=D.instance,{apiKey:u,persistentStore:l,baseUrl:d,maxCacheAgeSeconds:c,updateOnFetch:S}=n;try{s.stopPolling(),s.setAssignmentLogger(n.assignmentLogger),s.setIsObfuscated(!0);let b=fr(u),_=(0,oe.configurationStorageFactory)({maxAgeSeconds:c,servingStoreUpdateStrategy:S,persistentStore:l,hasChromeStorage:(0,oe.hasChromeStorage)(),hasWindowLocalStorage:(0,oe.hasWindowLocalStorage)()},{chromeStorage:(0,oe.chromeStorageIfAvailable)(),windowLocalStorage:(0,oe.localStorageIfAvailable)(),storageKeySuffix:b});s.setFlagConfigurationStore(_);let A=(0,dr.assignmentCacheFactory)({chromeStorage:(0,oe.chromeStorageIfAvailable)(),storageKeySuffix:b});A instanceof Zs.default&&await A.init(),s.useCustomAssignmentCache(A);let O={apiKey:u,sdkName:mt.sdkName,sdkVersion:mt.sdkVersion,baseUrl:d,requestTimeoutMs:(e=n.requestTimeoutMs)!==null&&e!==void 0?e:void 0,numInitialRequestRetries:(t=n.numInitialRequestRetries)!==null&&t!==void 0?t:void 0,numPollRequestRetries:(i=n.numPollRequestRetries)!==null&&i!==void 0?i:void 0,pollAfterSuccessfulInitialization:(r=n.pollAfterSuccessfulInitialization)!==null&&r!==void 0?r:!1,pollAfterFailedInitialization:(o=n.pollAfterFailedInitialization)!==null&&o!==void 0?o:!1,throwOnFailedInitialization:!0,skipInitialPoll:(g=n.skipInitialRequest)!==null&&g!==void 0?g:!1};s.setConfigurationRequestParameters(O);let N,M,C=_.init().then(async()=>_.getKeys().length?await _.isExpired()&&!n.useExpiredCache?(K.applicationLogger.warn("Eppo SDK set not to use expired cached configuration"),N=new Error("Configuration store was expired"),""):"config store":(K.applicationLogger.warn("Eppo SDK cached configuration is empty"),N=new Error("Configuration store was empty"),"")).catch(te=>{K.applicationLogger.warn("Eppo SDK encountered an error initializing from the configuration store",te),N=te}),B=s.fetchFlagConfigurations().then(()=>"fetch").catch(te=>{K.applicationLogger.warn("Eppo SDK encountered an error initializing from fetching",te),M=te}),ee=await Promise.race([C,B]);ee||(N?ee=await B:ee=await C),ee||(f=M)}catch(b){f=b}if(f&&(K.applicationLogger.warn("Eppo SDK was unable to initialize with a configuration, assignment calls will return the default value and not be logged"+(n.pollAfterFailedInitialization?" until an experiment configuration is successfully retrieved":"")),!((a=n.throwOnFailedInitialization)!==null&&a!==void 0)||a))throw f;return D.initialized=!0,s}R.init=no;function io(){return D.instance}R.getInstance=io;function ro(n,e){let t={sdkName:mt.sdkName,sdkVersion:mt.sdkVersion,apiKey:n};return new K.ApiEndpoints({baseUrl:e,queryParams:t}).ufcEndpoint()}R.getConfigUrl=ro});m();p();var ao=_t(hr());m();p();var On=class{constructor(){this.requests=[];this.maxRequests=0;this.allowedDomains=["phantom.app","phantom.com"];this.ignoredHosts=new Set;this.ignoredUrls=new Set}init(e={maxRequests:20,ignoredUrls:[],ignoredHosts:[]}){this.maxRequests=e.maxRequests,this.ignoredUrls=new Set(e.ignoredUrls),this.ignoredHosts=new Set(e.ignoredHosts);let t=self.fetch;self.fetch=async(...i)=>{let[r,o]=i,g=o?.method||"GET",a={};if(o?.headers instanceof Headers?o.headers.forEach((c,S)=>{a[S]=c}):Array.isArray(o?.headers)?o.headers.forEach(([c,S])=>{a[c]=S}):a=o?.headers||{},this.shouldIgnoreRequest(r.toString()))return t(...i);let s={id:`${Date.now()}-${Math.random()}`,method:g,url:r.toString(),requestHeaders:a,startTime:Date.now()};this.requests.length>=this.maxRequests&&this.requests.pop(),this.requests.unshift(s);let u=await t(...i),d=await u.clone().text();return s.status=u.status,s.responseHeaders={},u.headers.forEach((c,S)=>{s.responseHeaders[S]=c}),s.endTime=Date.now(),s.response=d,u}}shouldIgnoreRequest(e){try{let t=new URL(e).hostname;return this.ignoredHosts&&this.ignoredHosts.has(t)||this.ignoredUrls&&this.ignoredUrls.has(e)||!this.allowedDomains.some(i=>t.includes(i))?!0:!!(this.ignoredPatterns&&this.ignoredPatterns.some(i=>i.test(e)))}catch{return!0}}getRequests(){return this.requests}clearRequests(){this.requests=[]}setIgnoredHosts(e){this.ignoredHosts=new Set(e)}setIgnoredUrls(e){this.ignoredUrls=new Set(e)}setIgnoredPatterns(e){this.ignoredPatterns=e}clearIgnoredHosts(){this.ignoredHosts=new Set}clearIgnoredUrls(){this.ignoredUrls=new Set}clearIgnoredPatterns(){this.ignoredPatterns=void 0}},so=new On,mr=so;var pr=new $e,lo=async()=>{if(xn()){let n=[new Kn({eppoSDK:ao,apiKey:kn,subjectId:await ue.getDeviceId(),deviceAttributes:{appVersion:Ge,platform:"browser-ext",deviceId:await ue.getDeviceId()},maxCacheAgeSeconds:Un,analytics:ue}),new jn(Bn)];Pn&&n.push(new Gn({storage:pr})),Ce.setProviders(n)}else Ce.setProviders([new $n(pr)]);await Ce.initializeFeatureFlags()},Yl=()=>{ue.getDeviceId().then(n=>{lo().then(()=>{let e=Xn("enable-datadog");le.init({appVersion:Ge,enableDatadog:e,platform:"browser-ext",clientToken:E.DATADOG_CLIENT_TOKEN}),le.setUser({id:n}),uo(),Hn(ei)})}).catch(n=>{le.init({appVersion:Ge,enableDatadog:!1,platform:"browser-ext",clientToken:E.DATADOG_CLIENT_TOKEN}),le.captureError(n,"startUp")})},uo=()=>{let n=Ce.getMultivariateAssignment("network-logger-config-json");if(n)try{let e=JSON.parse(n),t=zn.parse(e);mr.init({ignoredHosts:t.ignoredHosts,maxRequests:t.maxRequests,ignoredUrls:t.ignoredUrls})}catch(e){console.error("Error initializing network logger",e)}};m();p();m();p();var Sr=_t(Or()),pt=_t(Ar());var vr=new $e,co=".phantom-labs.log",go="state-log",vt=class n{constructor(e){this.maxSize=1024*1024;this.maxDepth=5;this.autoTrim=!1;this.disableDebounce=!1;this.includeSessionMetadata=!1;this.quoteStrings=!1;this.useLocalStorage=!1;this.useTimestamps=!1;this.indent="  ";this.logFilename=go;this.storageKey=co;this.logs="";this.logQueueSize=256;this.debounceSaveAndFlushQueuedLogs=(0,Sr.default)(this.saveAndFlushQueuedLogs,4e3);if(e)for(let t in e)e[t]!==void 0&&(this[t]=e[t])}static async init(e){let t=new n(e);if(t.useLocalStorage){let i=await t.getDataFromStorage();if(i){t.logs=i.log,t.startTime=new Date(i.startTime);let r=new Date(i.lastLog);t.addSessionMetadataToLog(`Last session end: ${i.lastLog}`),t.addSessionMetadataToLog(`Last ${t.formatSessionDuration(t.startTime,r)}`)}}return t.addSessionStartToLog(),t.logQueue=new Jn(t.disableDebounce?1:t.logQueueSize),t}async downloadLog(e,t){try{let r=Vn(e,t).replaceAll(/seedIdentifier/gm,"rootID");await this.saveAndFlushQueuedLogs();let o=this.useLocalStorage?await this.getLogsFromStorage():this.logs,g=`${r}${o}`,a=new Blob([g],{type:"text/plain;charset=utf-8"}),f=URL.createObjectURL(a),s=new Date().toISOString().replace(/:|\./g,"-").slice(0,-5),u=`${this.logFilename}-${s}.txt`;if(pt.default.downloads&&typeof pt.default.downloads.download=="function")await pt.default.downloads.download({url:f,filename:u,saveAs:!0}),URL.revokeObjectURL(f);else{let l=document.createElement("a");l.href=self.URL.createObjectURL(a),l.target="_blank",l.rel="noopener noreferrer",l.download=u,document.body.appendChild(l),l.click(),document.body.removeChild(l),URL.revokeObjectURL(l.href)}}catch(i){let r=`Error downloading log file: ${i}`;le.captureError(new Error(r),"fileLogger"),console.error(r)}}getRecentLogs(e){let t=new TextEncoder,i=this.logs.split(`
`),r=0,o=i.length;for(let g=i.length-1;g>=0;g--){let a=t.encode(i[g]).length+1;if(r+a>e)break;r+=a,o=g}return i.slice(o).join(`
`)+`
`}write(...e){this.queueAndBatchWriteLogs(...e)}async queueAndBatchWriteLogs(...e){let t=this.formatNewLogData(...e);this.logQueue.add(t),this.debounceSaveAndFlushQueuedLogs.cancel(),this.logQueue.isFull()?await this.saveAndFlushQueuedLogs():await this.debounceSaveAndFlushQueuedLogs()}async saveAndFlushQueuedLogs(){this.logQueue.isEmpty()||(this.addItemToLogs(this.logQueue.toArray()),this.useLocalStorage&&await this.saveDataToStorage(this.logs),this.logQueue.clear())}formatNewLogData(...e){let t="";return this.useTimestamps&&(t+=this.formatDate()+" "),t+=e.map(i=>qn({obj:i,depth:0,maxDepth:this.maxDepth,indent:this.indent,quoteStrings:this.quoteStrings})).join(" "),t+=`
`,t}addItemToLogs(e){Array.isArray(e)?e.forEach(t=>this.logs+=t):this.logs+=e,this.autoTrim&&(this.logs=this.trimLogByPercentage(.5,this.maxSize))}async saveDataToStorage(e){let t={startTime:this.startTime,log:e,lastLog:new Date};await vr.set(this.storageKey,JSON.stringify(t))}async getLogsFromStorage(){let e=new Date,t=await this.getDataFromStorage();return t&&(this.startTime=new Date(t.startTime),this.logs=t.log),this.includeSessionMetadata?this.logs+`---- ${this.formatSessionDuration(this.startTime,e)} ----
`:this.logs}async getDataFromStorage(){let e=await vr.get(this.storageKey);return e&&typeof e=="string"?JSON.parse(e):null}trimLogByPercentage(e,t){if(e<0||e>1)throw new Error("Percentage must be between 0 and 1");if(new TextEncoder().encode(this.logs).length<=t)return this.logs;let o=this.logs.split(`
`);o.pop();let g=Math.floor(o.length*e);return o.splice(0,g),o.join(`
`)+`
`}formatSessionDuration(e,t){let i=t.getTime()-e.getTime(),r=Math.floor(i/1e3/60/60),o=("0"+r).slice(-2);i-=r*1e3*60*60;let g=Math.floor(i/1e3/60),a=("0"+g).slice(-2);i-=g*1e3*60;let f=Math.floor(i/1e3),s=("0"+f).slice(-2);return i-=f*1e3,"Session duration: "+o+":"+a+":"+s}formatDate(e=new Date){return`[${e.toISOString()}]`}addSessionMetadataToLog(e){this.includeSessionMetadata&&(this.logs+=`---- ${e} ----
`)}addSessionStartToLog(){this.startTime=new Date,this.addSessionMetadataToLog(`Session started: ${this.formatDate(this.startTime)}`)}async clear(){this.logs="",this.logQueue.clear(),this.addSessionMetadataToLog(`Session started: ${this.formatDate(this.startTime)}`),this.addSessionMetadataToLog("Log cleared "+this.formatDate()),this.useLocalStorage&&await this.saveDataToStorage(this.logs)}search(e){let t=new RegExp(e,"ig"),i=this.logs.split(`
`),r=[];for(let g=0;g<i.length;g++){let a=`[${g}] `;i[g].match(t)&&r.push(a+i[g].trim())}let o=r.join(`
`);return o.length||(o=`Nothing found for "${e}".`),o}slice(...e){return this.logs.split(`
`).slice(...e).join(`
`)}};var fo=1024*10,ho={autoTrim:!0,useLocalStorage:!0,useTimestamps:!0},ve=null,uu={init:async()=>{try{ve=await vt.init(ho)}catch{console.error(Ie)}},downloadLog:async n=>{if(!ve){console.error(Ie);return}await ve.saveAndFlushQueuedLogs()},getRecentLogs:async()=>ve?ve.getRecentLogs(fo):(console.error(Ie),""),write:(n,e,t,i)=>{if(!ve){console.error(Ie);return}let r=i?` :: ${JSON.stringify(i)}`:"",o=`${t.toUpperCase()}: ${e}: ${n}${r}`;ve.write(o)},getLogFilePaths:()=>{throw new Error(yt)},sendLogFilesByEmail:async n=>{throw new Error(yt)}};m();p();var mo=new At(null,{loadPath:n=>{let e=n[0];return e.substring(0,3)==="fil"?`/locales/${e.substring(0,3)}/translation.json`:e.substring(0,2)==="zh"?e==="zh-CN"||e==="zh-TW"?`/locales/${e}/translation.json`:"/locales/zh-CN/translation.json":`/locales/${e.substring(0,2)}/translation.json`},parse:n=>JSON.parse(n)});Ke.use(At).use(Qn).use(Wn).use(mo).init({supportedLngs:Yn(),fallbackLng:"en",nonExplicitSupportedLngs:!0,debug:!1,returnNull:!1,detection:{order:["querystring","localStorage","cookie","sessionStorage","navigator","htmlTag","path","subdomain"],caches:["localStorage"]},react:{useSuspense:!1},interpolation:{escapeValue:!1}});Ke.on("initialized",()=>{ue.setDisplayLanguage(Ke.language)});export{Cr as a,mr as b,Yl as c,ve as d,uu as e};
/*! Bundled license information:

is-buffer/index.js:
  (*!
   * Determine if an object is a Buffer
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)
*/
//# sourceMappingURL=chunk-AVT3M45V.js.map
