import{a as dn}from"./chunk-K5EEWGKQ.js";import{pa as ne,qa as it,ra as Ee,sa as cr}from"./chunk-JD6NH5K6.js";import{e as $a}from"./chunk-KJMFZ7XX.js";import{X as ln}from"./chunk-2NGYUYTC.js";import{a as Ka}from"./chunk-H3FFS4GT.js";import{P as sn,Q as cn,R as fn,S as pn,ia as yn,o as re}from"./chunk-WIQ4WVKX.js";import{$ as sr,j as un}from"./chunk-F3RUX6TF.js";import{ma as Se,x as an}from"./chunk-ALUTR72U.js";import{a as b}from"./chunk-7X4NV6OJ.js";import{c as O,f as te,h as y,n as d}from"./chunk-3KENBVE7.js";var hn=O((rc,ut)=>{y();d();(function(){"use strict";var e={}.hasOwnProperty;function r(){for(var n=[],t=0;t<arguments.length;t++){var o=arguments[t];if(o){var a=typeof o;if(a==="string"||a==="number")n.push(o);else if(Array.isArray(o)){if(o.length){var i=r.apply(null,o);i&&n.push(i)}}else if(a==="object")if(o.toString===Object.prototype.toString)for(var u in o)e.call(o,u)&&o[u]&&n.push(u);else n.push(o.toString())}}return n.join(" ")}typeof ut<"u"&&ut.exports?(r.default=r,ut.exports=r):typeof define=="function"&&typeof define.amd=="object"&&define.amd?define("classnames",[],function(){return r}):self.classNames=r})()});var Pn=O((ac,mn)=>{y();d();mn.exports=function(r,n,t){var o=document.head||document.getElementsByTagName("head")[0],a=document.createElement("script");typeof n=="function"&&(t=n,n={}),n=n||{},t=t||function(){},a.type=n.type||"text/javascript",a.charset=n.charset||"utf8",a.async="async"in n?!!n.async:!0,a.src=r,n.attrs&&Fa(a,n.attrs),n.text&&(a.text=""+n.text);var i="onload"in a?vn:za;i(a,t),a.onload||vn(a,t),o.appendChild(a)};function Fa(e,r){for(var n in r)e.setAttribute(n,r[n])}function vn(e,r){e.onload=function(){this.onerror=this.onload=null,r(null,e)},e.onerror=function(){this.onerror=this.onload=null,r(new Error("Failed to load "+this.src),e)}}function za(e,r){e.onreadystatechange=function(){this.readyState!="complete"&&this.readyState!="loaded"||(this.onreadystatechange=null,r(null,e))}}});var M=O(H=>{"use strict";y();d();Object.defineProperty(H,"__esModule",{value:!0});H.parseStartTime=ai;H.parseEndTime=ii;H.randomString=ui;H.queryString=li;H.getSDK=si;H.getConfig=ci;H.omit=fi;H.callPlayer=pi;H.isMediaStream=yi;H.isBlobUrl=di;H.supportsWebKitPresentationMode=hi;var Ya=_n(Pn()),Xa=_n(ln());function _n(e){return e&&e.__esModule?e:{default:e}}function Ga(e,r){return ei(e)||Ja(e,r)||Qa(e,r)||Za()}function Za(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Qa(e,r){if(e){if(typeof e=="string")return gn(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return gn(e,r)}}function gn(e,r){(r==null||r>e.length)&&(r=e.length);for(var n=0,t=new Array(r);n<r;n++)t[n]=e[n];return t}function Ja(e,r){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],t=!0,o=!1,a=void 0;try{for(var i=e[Symbol.iterator](),u;!(t=(u=i.next()).done)&&(n.push(u.value),!(r&&n.length===r));t=!0);}catch(l){o=!0,a=l}finally{try{!t&&i.return!=null&&i.return()}finally{if(o)throw a}}return n}}function ei(e){if(Array.isArray(e))return e}var ti=/[?&#](?:start|t)=([0-9hms]+)/,ri=/[?&#]end=([0-9hms]+)/,pr=/(\d+)(h|m|s)/g,ni=/^\d+$/;function On(e,r){if(!(e instanceof Array)){var n=e.match(r);if(n){var t=n[1];if(t.match(pr))return oi(t);if(ni.test(t))return parseInt(t)}}}function oi(e){for(var r=0,n=pr.exec(e);n!==null;){var t=n,o=Ga(t,3),a=o[1],i=o[2];i==="h"&&(r+=parseInt(a,10)*60*60),i==="m"&&(r+=parseInt(a,10)*60),i==="s"&&(r+=parseInt(a,10)),n=pr.exec(e)}return r}function ai(e){return On(e,ti)}function ii(e){return On(e,ri)}function ui(){return Math.random().toString(36).substr(2,5)}function li(e){return Object.keys(e).map(function(r){return"".concat(r,"=").concat(e[r])}).join("&")}function fr(e){return self[e]?self[e]:self.exports&&self.exports[e]?self.exports[e]:self.module&&self.module.exports&&self.module.exports[e]?self.module.exports[e]:null}var Re={};function si(e,r){var n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:null,t=arguments.length>3&&arguments[3]!==void 0?arguments[3]:function(){return!0},o=arguments.length>4&&arguments[4]!==void 0?arguments[4]:Ya.default,a=fr(r);return a&&t(a)?Promise.resolve(a):new Promise(function(i,u){if(Re[e]){Re[e].push({resolve:i,reject:u});return}Re[e]=[{resolve:i,reject:u}];var l=function(f){Re[e].forEach(function(p){return p.resolve(f)})};if(n){var s=self[n];self[n]=function(){s&&s(),l(fr(r))}}o(e,function(c){c?(Re[e].forEach(function(f){return f.reject(c)}),Re[e]=null):n||l(fr(r))})})}function ci(e,r){return(0,Xa.default)(r.config,e.config)}function fi(e){for(var r,n=arguments.length,t=new Array(n>1?n-1:0),o=1;o<n;o++)t[o-1]=arguments[o];for(var a=(r=[]).concat.apply(r,t),i={},u=Object.keys(e),l=0,s=u;l<s.length;l++){var c=s[l];a.indexOf(c)===-1&&(i[c]=e[c])}return i}function pi(e){var r;if(!this.player||!this.player[e]){var n="ReactPlayer: ".concat(this.constructor.displayName," player could not call %c").concat(e,"%c \u2013 ");return this.player?this.player[e]||(n+="The method was not available"):n+="The player was not available",console.warn(n,"font-weight: bold",""),null}for(var t=arguments.length,o=new Array(t>1?t-1:0),a=1;a<t;a++)o[a-1]=arguments[a];return(r=this.player)[e].apply(r,o)}function yi(e){return typeof self<"u"&&typeof self.MediaStream<"u"&&e instanceof self.MediaStream}function di(e){return/^blob:/.test(e)}function hi(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:document.createElement("video"),r=/iPhone|iPod/.test(navigator.userAgent)===!1;return e.webkitSupportsPresentationMode&&typeof e.webkitSetPresentationMode=="function"&&r}});var U=O(m=>{"use strict";y();d();Object.defineProperty(m,"__esModule",{value:!0});m.canPlay=m.FLV_EXTENSIONS=m.DASH_EXTENSIONS=m.HLS_EXTENSIONS=m.VIDEO_EXTENSIONS=m.AUDIO_EXTENSIONS=m.MATCH_URL_KALTURA=m.MATCH_URL_VIDYARD=m.MATCH_URL_MIXCLOUD=m.MATCH_URL_DAILYMOTION=m.MATCH_URL_TWITCH_CHANNEL=m.MATCH_URL_TWITCH_VIDEO=m.MATCH_URL_WISTIA=m.MATCH_URL_STREAMABLE=m.MATCH_URL_FACEBOOK_WATCH=m.MATCH_URL_FACEBOOK=m.MATCH_URL_VIMEO=m.MATCH_URL_SOUNDCLOUD=m.MATCH_URL_YOUTUBE=void 0;var bn=M();function vi(e,r){var n;if(typeof Symbol>"u"||e[Symbol.iterator]==null){if(Array.isArray(e)||(n=mi(e))||r&&e&&typeof e.length=="number"){n&&(e=n);var t=0,o=function(){};return{s:o,n:function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}},e:function(s){throw s},f:o}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var a=!0,i=!1,u;return{s:function(){n=e[Symbol.iterator]()},n:function(){var s=n.next();return a=s.done,s},e:function(s){i=!0,u=s},f:function(){try{!a&&n.return!=null&&n.return()}finally{if(i)throw u}}}}function mi(e,r){if(e){if(typeof e=="string")return wn(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return wn(e,r)}}function wn(e,r){(r==null||r>e.length)&&(r=e.length);for(var n=0,t=new Array(r);n<r;n++)t[n]=e[n];return t}var yr=/(?:youtu\.be\/|youtube(?:-nocookie)?\.com\/(?:embed\/|v\/|watch\/|watch\?v=|watch\?.+&v=))((\w|-){11})|youtube\.com\/playlist\?list=|youtube\.com\/user\//;m.MATCH_URL_YOUTUBE=yr;var Sn=/(?:soundcloud\.com|snd\.sc)\/[^.]+$/;m.MATCH_URL_SOUNDCLOUD=Sn;var En=/vimeo\.com\/.+/;m.MATCH_URL_VIMEO=En;var Rn=/^https?:\/\/(www\.)?facebook\.com.*\/(video(s)?|watch|story)(\.php?|\/).+$/;m.MATCH_URL_FACEBOOK=Rn;var Dn=/^https?:\/\/fb\.watch\/.+$/;m.MATCH_URL_FACEBOOK_WATCH=Dn;var Tn=/streamable\.com\/([a-z0-9]+)$/;m.MATCH_URL_STREAMABLE=Tn;var Ln=/(?:wistia\.(?:com|net)|wi\.st)\/(?:medias|embed)\/(?:iframe\/)?(.*)$/;m.MATCH_URL_WISTIA=Ln;var Mn=/(?:www\.|go\.)?twitch\.tv\/videos\/(\d+)($|\?)/;m.MATCH_URL_TWITCH_VIDEO=Mn;var In=/(?:www\.|go\.)?twitch\.tv\/([a-zA-Z0-9_]+)($|\?)/;m.MATCH_URL_TWITCH_CHANNEL=In;var An=/^(?:(?:https?):)?(?:\/\/)?(?:www\.)?(?:(?:dailymotion\.com(?:\/embed)?\/video)|dai\.ly)\/([a-zA-Z0-9]+)(?:_[\w_-]+)?$/;m.MATCH_URL_DAILYMOTION=An;var Cn=/mixcloud\.com\/([^/]+\/[^/]+)/;m.MATCH_URL_MIXCLOUD=Cn;var kn=/vidyard.com\/(?:watch\/)?([a-zA-Z0-9-]+)/;m.MATCH_URL_VIDYARD=kn;var Un=/^https?:\/\/[a-zA-Z]+\.kaltura.(com|org)\/p\/([0-9]+)\/sp\/([0-9]+)00\/embedIframeJs\/uiconf_id\/([0-9]+)\/partner_id\/([0-9]+)(.*)entry_id.([a-zA-Z0-9-_]+)$/;m.MATCH_URL_KALTURA=Un;var dr=/\.(m4a|mp4a|mpga|mp2|mp2a|mp3|m2a|m3a|wav|weba|aac|oga|spx)($|\?)/i;m.AUDIO_EXTENSIONS=dr;var hr=/\.(mp4|og[gv]|webm|mov|m4v)($|\?)/i;m.VIDEO_EXTENSIONS=hr;var vr=/\.(m3u8)($|\?)/i;m.HLS_EXTENSIONS=vr;var Nn=/\.(mpd)($|\?)/i;m.DASH_EXTENSIONS=Nn;var qn=/\.(flv)($|\?)/i;m.FLV_EXTENSIONS=qn;var Pi=function e(r){if(r instanceof Array){var n=vi(r),t;try{for(n.s();!(t=n.n()).done;){var o=t.value;if(typeof o=="string"&&e(o)||e(o.src))return!0}}catch(a){n.e(a)}finally{n.f()}return!1}return(0,bn.isMediaStream)(r)||(0,bn.isBlobUrl)(r)?!0:dr.test(r)||hr.test(r)||vr.test(r)||Nn.test(r)||qn.test(r)},gi={youtube:function(r){return r instanceof Array?r.every(function(n){return yr.test(n)}):yr.test(r)},soundcloud:function(r){return Sn.test(r)&&!dr.test(r)},vimeo:function(r){return En.test(r)&&!hr.test(r)&&!vr.test(r)},facebook:function(r){return Rn.test(r)||Dn.test(r)},streamable:function(r){return Tn.test(r)},wistia:function(r){return Ln.test(r)},twitch:function(r){return Mn.test(r)||In.test(r)},dailymotion:function(r){return An.test(r)},mixcloud:function(r){return Cn.test(r)},vidyard:function(r){return kn.test(r)},kaltura:function(r){return Un.test(r)},file:Pi};m.canPlay=gi});var $n=O(ct=>{"use strict";y();d();function Ce(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ce=function(n){return typeof n}:Ce=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ce(e)}Object.defineProperty(ct,"__esModule",{value:!0});ct.default=void 0;var mr=_i(b()),De=M(),Bn=U();function Kn(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return Kn=function(){return e},e}function _i(e){if(e&&e.__esModule)return e;if(e===null||Ce(e)!=="object"&&typeof e!="function")return{default:e};var r=Kn();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Vn(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Pr(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?Vn(Object(n),!0).forEach(function(t){oe(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Vn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Hn(e,r){return Si(e)||wi(e,r)||bi(e,r)||Oi()}function Oi(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function bi(e,r){if(e){if(typeof e=="string")return xn(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return xn(e,r)}}function xn(e,r){(r==null||r>e.length)&&(r=e.length);for(var n=0,t=new Array(r);n<r;n++)t[n]=e[n];return t}function wi(e,r){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],t=!0,o=!1,a=void 0;try{for(var i=e[Symbol.iterator](),u;!(t=(u=i.next()).done)&&(n.push(u.value),!(r&&n.length===r));t=!0);}catch(l){o=!0,a=l}finally{try{!t&&i.return!=null&&i.return()}finally{if(o)throw a}}return n}}function Si(e){if(Array.isArray(e))return e}function Ei(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Wn(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Ri(e,r,n){return r&&Wn(e.prototype,r),n&&Wn(e,n),e}function Di(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&_r(e,r)}function _r(e,r){return _r=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},_r(e,r)}function Ti(e){var r=Mi();return function(){var t=st(e),o;if(r){var a=st(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Li(this,o)}}function Li(e,r){return r&&(Ce(r)==="object"||typeof r=="function")?r:ge(e)}function ge(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Mi(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function st(e){return st=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},st(e)}function oe(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Ii="https://www.youtube.com/iframe_api",jn="YT",Ai="onYouTubeIframeAPIReady",lt=/[?&](?:list|channel)=([a-zA-Z0-9_-]+)/,gr=/user\/([a-zA-Z0-9_-]+)\/?/,Ci=/youtube-nocookie\.com/,ki="https://www.youtube-nocookie.com",Or=function(e){Di(n,e);var r=Ti(n);function n(){var t;Ei(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),oe(ge(t),"callPlayer",De.callPlayer),oe(ge(t),"parsePlaylist",function(u){if(u instanceof Array)return{listType:"playlist",playlist:u.map(t.getID).join(",")};if(lt.test(u)){var l=u.match(lt),s=Hn(l,2),c=s[1];return{listType:"playlist",list:c.replace(/^UC/,"UU")}}if(gr.test(u)){var f=u.match(gr),p=Hn(f,2),h=p[1];return{listType:"user_uploads",list:h}}return{}}),oe(ge(t),"onStateChange",function(u){var l=u.data,s=t.props,c=s.onPlay,f=s.onPause,p=s.onBuffer,h=s.onBufferEnd,v=s.onEnded,_=s.onReady,A=s.loop,k=s.config,G=k.playerVars,ee=k.onUnstarted,V=self[jn].PlayerState,Ie=V.UNSTARTED,nt=V.PLAYING,me=V.PAUSED,Ae=V.BUFFERING,ur=V.ENDED,ot=V.CUED;if(l===Ie&&ee(),l===nt&&(c(),h()),l===me&&f(),l===Ae&&p(),l===ur){var at=!!t.callPlayer("getPlaylist");A&&!at&&(G.start?t.seekTo(G.start):t.play()),v()}l===ot&&_()}),oe(ge(t),"mute",function(){t.callPlayer("mute")}),oe(ge(t),"unmute",function(){t.callPlayer("unMute")}),oe(ge(t),"ref",function(u){t.container=u}),t}return Ri(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"getID",value:function(o){return!o||o instanceof Array||lt.test(o)?null:o.match(Bn.MATCH_URL_YOUTUBE)[1]}},{key:"load",value:function(o,a){var i=this,u=this.props,l=u.playing,s=u.muted,c=u.playsinline,f=u.controls,p=u.loop,h=u.config,v=u.onError,_=h.playerVars,A=h.embedOptions,k=this.getID(o);if(a){if(lt.test(o)||gr.test(o)||o instanceof Array){this.player.loadPlaylist(this.parsePlaylist(o));return}this.player.cueVideoById({videoId:k,startSeconds:(0,De.parseStartTime)(o)||_.start,endSeconds:(0,De.parseEndTime)(o)||_.end});return}(0,De.getSDK)(Ii,jn,Ai,function(G){return G.loaded}).then(function(G){i.container&&(i.player=new G.Player(i.container,Pr({width:"100%",height:"100%",videoId:k,playerVars:Pr(Pr({autoplay:l?1:0,mute:s?1:0,controls:f?1:0,start:(0,De.parseStartTime)(o),end:(0,De.parseEndTime)(o),origin:self.location.origin,playsinline:c?1:0},i.parsePlaylist(o)),_),events:{onReady:function(){p&&i.player.setLoop(!0),i.props.onReady()},onStateChange:i.onStateChange,onError:function(V){return v(V.data)}},host:Ci.test(o)?ki:void 0},A)))},v),A.events&&console.warn("Using `embedOptions.events` will likely break things. Use ReactPlayer\u2019s callback props instead, eg onReady, onPlay, onPause")}},{key:"play",value:function(){this.callPlayer("playVideo")}},{key:"pause",value:function(){this.callPlayer("pauseVideo")}},{key:"stop",value:function(){document.body.contains(this.callPlayer("getIframe"))&&this.callPlayer("stopVideo")}},{key:"seekTo",value:function(o){this.callPlayer("seekTo",o),this.props.playing||this.pause()}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o*100)}},{key:"setPlaybackRate",value:function(o){this.callPlayer("setPlaybackRate",o)}},{key:"setLoop",value:function(o){this.callPlayer("setLoop",o)}},{key:"getDuration",value:function(){return this.callPlayer("getDuration")}},{key:"getCurrentTime",value:function(){return this.callPlayer("getCurrentTime")}},{key:"getSecondsLoaded",value:function(){return this.callPlayer("getVideoLoadedFraction")*this.getDuration()}},{key:"render",value:function(){var o=this.props.display,a={width:"100%",height:"100%",display:o};return mr.default.createElement("div",{style:a},mr.default.createElement("div",{ref:this.ref}))}}]),n}(mr.Component);ct.default=Or;oe(Or,"displayName","YouTube");oe(Or,"canPlay",Bn.canPlay.youtube)});var Qn=O(yt=>{"use strict";y();d();function ke(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?ke=function(n){return typeof n}:ke=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},ke(e)}Object.defineProperty(yt,"__esModule",{value:!0});yt.default=void 0;var Fn=Ni(b()),zn=M(),Ui=U();function Zn(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return Zn=function(){return e},e}function Ni(e){if(e&&e.__esModule)return e;if(e===null||ke(e)!=="object"&&typeof e!="function")return{default:e};var r=Zn();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Yn(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Xn(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?Yn(Object(n),!0).forEach(function(t){Z(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Yn(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function qi(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Gn(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Vi(e,r,n){return r&&Gn(e.prototype,r),n&&Gn(e,n),e}function Hi(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&br(e,r)}function br(e,r){return br=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},br(e,r)}function xi(e){var r=ji();return function(){var t=ft(e),o;if(r){var a=ft(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Wi(this,o)}}function Wi(e,r){return r&&(ke(r)==="object"||typeof r=="function")?r:fe(e)}function fe(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ji(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function ft(e){return ft=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},ft(e)}function Z(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Bi="https://w.soundcloud.com/player/api.js",Ki="SC",pt=function(e){Hi(n,e);var r=xi(n);function n(){var t;qi(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),Z(fe(t),"callPlayer",zn.callPlayer),Z(fe(t),"duration",null),Z(fe(t),"currentTime",null),Z(fe(t),"fractionLoaded",null),Z(fe(t),"mute",function(){t.setVolume(0)}),Z(fe(t),"unmute",function(){t.props.volume!==null&&t.setVolume(t.props.volume)}),Z(fe(t),"ref",function(u){t.iframe=u}),t}return Vi(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o,a){var i=this;(0,zn.getSDK)(Bi,Ki).then(function(u){if(i.iframe){var l=u.Widget.Events,s=l.PLAY,c=l.PLAY_PROGRESS,f=l.PAUSE,p=l.FINISH,h=l.ERROR;a||(i.player=u.Widget(i.iframe),i.player.bind(s,i.props.onPlay),i.player.bind(f,function(){var v=i.duration-i.currentTime;v<.05||i.props.onPause()}),i.player.bind(c,function(v){i.currentTime=v.currentPosition/1e3,i.fractionLoaded=v.loadedProgress}),i.player.bind(p,function(){return i.props.onEnded()}),i.player.bind(h,function(v){return i.props.onError(v)})),i.player.load(o,Xn(Xn({},i.props.config.options),{},{callback:function(){i.player.getDuration(function(_){i.duration=_/1e3,i.props.onReady()})}}))}})}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){}},{key:"seekTo",value:function(o){this.callPlayer("seekTo",o*1e3)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o*100)}},{key:"getDuration",value:function(){return this.duration}},{key:"getCurrentTime",value:function(){return this.currentTime}},{key:"getSecondsLoaded",value:function(){return this.fractionLoaded*this.duration}},{key:"render",value:function(){var o=this.props.display,a={width:"100%",height:"100%",display:o};return Fn.default.createElement("iframe",{ref:this.ref,src:"https://w.soundcloud.com/player/?url=".concat(encodeURIComponent(this.props.url)),style:a,frameBorder:0,allow:"autoplay"})}}]),n}(Fn.Component);yt.default=pt;Z(pt,"displayName","SoundCloud");Z(pt,"canPlay",Ui.canPlay.soundcloud);Z(pt,"loopOnEnded",!0)});var oo=O(vt=>{"use strict";y();d();function Ue(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ue=function(n){return typeof n}:Ue=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ue(e)}Object.defineProperty(vt,"__esModule",{value:!0});vt.default=void 0;var Jn=Fi(b()),eo=M(),$i=U();function no(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return no=function(){return e},e}function Fi(e){if(e&&e.__esModule)return e;if(e===null||Ue(e)!=="object"&&typeof e!="function")return{default:e};var r=no();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function to(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function zi(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?to(Object(n),!0).forEach(function(t){Q(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):to(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Yi(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ro(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Xi(e,r,n){return r&&ro(e.prototype,r),n&&ro(e,n),e}function Gi(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&wr(e,r)}function wr(e,r){return wr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},wr(e,r)}function Zi(e){var r=Ji();return function(){var t=dt(e),o;if(r){var a=dt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Qi(this,o)}}function Qi(e,r){return r&&(Ue(r)==="object"||typeof r=="function")?r:pe(e)}function pe(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Ji(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function dt(e){return dt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},dt(e)}function Q(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var eu="https://player.vimeo.com/api/player.js",tu="Vimeo",ht=function(e){Gi(n,e);var r=Zi(n);function n(){var t;Yi(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),Q(pe(t),"callPlayer",eo.callPlayer),Q(pe(t),"duration",null),Q(pe(t),"currentTime",null),Q(pe(t),"secondsLoaded",null),Q(pe(t),"mute",function(){t.setVolume(0)}),Q(pe(t),"unmute",function(){t.props.volume!==null&&t.setVolume(t.props.volume)}),Q(pe(t),"ref",function(u){t.container=u}),t}return Xi(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this;this.duration=null,(0,eo.getSDK)(eu,tu).then(function(i){a.container&&(a.player=new i.Player(a.container,zi({url:o,autoplay:a.props.playing,muted:a.props.muted,loop:a.props.loop,playsinline:a.props.playsinline,controls:a.props.controls},a.props.config.playerOptions)),a.player.ready().then(function(){var u=a.container.querySelector("iframe");u.style.width="100%",u.style.height="100%"}).catch(a.props.onError),a.player.on("loaded",function(){a.props.onReady(),a.refreshDuration()}),a.player.on("play",function(){a.props.onPlay(),a.refreshDuration()}),a.player.on("pause",a.props.onPause),a.player.on("seeked",function(u){return a.props.onSeek(u.seconds)}),a.player.on("ended",a.props.onEnded),a.player.on("error",a.props.onError),a.player.on("timeupdate",function(u){var l=u.seconds;a.currentTime=l}),a.player.on("progress",function(u){var l=u.seconds;a.secondsLoaded=l}),a.player.on("bufferstart",a.props.onBuffer),a.player.on("bufferend",a.props.onBufferEnd))},this.props.onError)}},{key:"refreshDuration",value:function(){var o=this;this.player.getDuration().then(function(a){o.duration=a})}},{key:"play",value:function(){var o=this.callPlayer("play");o&&o.catch(this.props.onError)}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){this.callPlayer("unload")}},{key:"seekTo",value:function(o){this.callPlayer("setCurrentTime",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o)}},{key:"setLoop",value:function(o){this.callPlayer("setLoop",o)}},{key:"setPlaybackRate",value:function(o){this.callPlayer("setPlaybackRate",o)}},{key:"getDuration",value:function(){return this.duration}},{key:"getCurrentTime",value:function(){return this.currentTime}},{key:"getSecondsLoaded",value:function(){return this.secondsLoaded}},{key:"render",value:function(){var o=this.props.display,a={width:"100%",height:"100%",overflow:"hidden",display:o};return Jn.default.createElement("div",{key:this.props.url,ref:this.ref,style:a})}}]),n}(Jn.Component);vt.default=ht;Q(ht,"displayName","Vimeo");Q(ht,"canPlay",$i.canPlay.vimeo);Q(ht,"forceLoad",!0)});var fo=O(_t=>{"use strict";y();d();function qe(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?qe=function(n){return typeof n}:qe=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},qe(e)}Object.defineProperty(_t,"__esModule",{value:!0});_t.default=void 0;var ao=nu(b()),mt=M(),ru=U();function co(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return co=function(){return e},e}function nu(e){if(e&&e.__esModule)return e;if(e===null||qe(e)!=="object"&&typeof e!="function")return{default:e};var r=co();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Sr(){return Sr=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},Sr.apply(this,arguments)}function ou(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function io(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function au(e,r,n){return r&&io(e.prototype,r),n&&io(e,n),e}function iu(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Er(e,r)}function Er(e,r){return Er=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Er(e,r)}function uu(e){var r=su();return function(){var t=Pt(e),o;if(r){var a=Pt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return lu(this,o)}}function lu(e,r){return r&&(qe(r)==="object"||typeof r=="function")?r:Ne(e)}function Ne(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function su(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Pt(e){return Pt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Pt(e)}function _e(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var uo="https://connect.facebook.net/en_US/sdk.js",lo="FB",so="fbAsyncInit",cu="facebook-player-",gt=function(e){iu(n,e);var r=uu(n);function n(){var t;ou(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),_e(Ne(t),"callPlayer",mt.callPlayer),_e(Ne(t),"playerID",t.props.config.playerId||"".concat(cu).concat((0,mt.randomString)())),_e(Ne(t),"mute",function(){t.callPlayer("mute")}),_e(Ne(t),"unmute",function(){t.callPlayer("unmute")}),t}return au(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o,a){var i=this;if(a){(0,mt.getSDK)(uo,lo,so).then(function(u){return u.XFBML.parse()});return}(0,mt.getSDK)(uo,lo,so).then(function(u){u.init({appId:i.props.config.appId,xfbml:!0,version:i.props.config.version}),u.Event.subscribe("xfbml.render",function(l){i.props.onLoaded()}),u.Event.subscribe("xfbml.ready",function(l){l.type==="video"&&l.id===i.playerID&&(i.player=l.instance,i.player.subscribe("startedPlaying",i.props.onPlay),i.player.subscribe("paused",i.props.onPause),i.player.subscribe("finishedPlaying",i.props.onEnded),i.player.subscribe("startedBuffering",i.props.onBuffer),i.player.subscribe("finishedBuffering",i.props.onBufferEnd),i.player.subscribe("error",i.props.onError),i.props.muted?i.callPlayer("mute"):i.callPlayer("unmute"),i.props.onReady(),document.getElementById(i.playerID).querySelector("iframe").style.visibility="visible")})})}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){}},{key:"seekTo",value:function(o){this.callPlayer("seek",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o)}},{key:"getDuration",value:function(){return this.callPlayer("getDuration")}},{key:"getCurrentTime",value:function(){return this.callPlayer("getCurrentPosition")}},{key:"getSecondsLoaded",value:function(){return null}},{key:"render",value:function(){var o=this.props.config.attributes,a={width:"100%",height:"100%"};return ao.default.createElement("div",Sr({style:a,id:this.playerID,className:"fb-video","data-href":this.props.url,"data-autoplay":this.props.playing?"true":"false","data-allowfullscreen":"true","data-controls":this.props.controls?"true":"false"},o))}}]),n}(ao.Component);_t.default=gt;_e(gt,"displayName","Facebook");_e(gt,"canPlay",ru.canPlay.facebook);_e(gt,"loopOnEnded",!0)});var Po=O(bt=>{"use strict";y();d();function Ve(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Ve=function(n){return typeof n}:Ve=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Ve(e)}Object.defineProperty(bt,"__esModule",{value:!0});bt.default=void 0;var po=fu(b()),yo=M(),vo=U();function mo(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return mo=function(){return e},e}function fu(e){if(e&&e.__esModule)return e;if(e===null||Ve(e)!=="object"&&typeof e!="function")return{default:e};var r=mo();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function pu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ho(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function yu(e,r,n){return r&&ho(e.prototype,r),n&&ho(e,n),e}function du(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Rr(e,r)}function Rr(e,r){return Rr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Rr(e,r)}function hu(e){var r=mu();return function(){var t=Ot(e),o;if(r){var a=Ot(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return vu(this,o)}}function vu(e,r){return r&&(Ve(r)==="object"||typeof r=="function")?r:ye(e)}function ye(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function mu(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Ot(e){return Ot=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Ot(e)}function ae(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Pu="https://cdn.embed.ly/player-0.1.0.min.js",gu="playerjs",Dr=function(e){du(n,e);var r=hu(n);function n(){var t;pu(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),ae(ye(t),"callPlayer",yo.callPlayer),ae(ye(t),"duration",null),ae(ye(t),"currentTime",null),ae(ye(t),"secondsLoaded",null),ae(ye(t),"mute",function(){t.callPlayer("mute")}),ae(ye(t),"unmute",function(){t.callPlayer("unmute")}),ae(ye(t),"ref",function(u){t.iframe=u}),t}return yu(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this;(0,yo.getSDK)(Pu,gu).then(function(i){a.iframe&&(a.player=new i.Player(a.iframe),a.player.setLoop(a.props.loop),a.player.on("ready",a.props.onReady),a.player.on("play",a.props.onPlay),a.player.on("pause",a.props.onPause),a.player.on("seeked",a.props.onSeek),a.player.on("ended",a.props.onEnded),a.player.on("error",a.props.onError),a.player.on("timeupdate",function(u){var l=u.duration,s=u.seconds;a.duration=l,a.currentTime=s}),a.player.on("buffered",function(u){var l=u.percent;a.duration&&(a.secondsLoaded=a.duration*l)}),a.props.muted&&a.player.mute())},this.props.onError)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){}},{key:"seekTo",value:function(o){this.callPlayer("setCurrentTime",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o*100)}},{key:"setLoop",value:function(o){this.callPlayer("setLoop",o)}},{key:"getDuration",value:function(){return this.duration}},{key:"getCurrentTime",value:function(){return this.currentTime}},{key:"getSecondsLoaded",value:function(){return this.secondsLoaded}},{key:"render",value:function(){var o=this.props.url.match(vo.MATCH_URL_STREAMABLE)[1],a={width:"100%",height:"100%"};return po.default.createElement("iframe",{ref:this.ref,src:"https://streamable.com/o/".concat(o),frameBorder:"0",scrolling:"no",style:a,allowFullScreen:!0})}}]),n}(po.Component);bt.default=Dr;ae(Dr,"displayName","Streamable");ae(Dr,"canPlay",vo.canPlay.streamable)});var So=O(Et=>{"use strict";y();d();function He(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?He=function(n){return typeof n}:He=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},He(e)}Object.defineProperty(Et,"__esModule",{value:!0});Et.default=void 0;var go=_u(b()),Tr=M(),bo=U();function wo(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return wo=function(){return e},e}function _u(e){if(e&&e.__esModule)return e;if(e===null||He(e)!=="object"&&typeof e!="function")return{default:e};var r=wo();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function _o(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Ou(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?_o(Object(n),!0).forEach(function(t){B(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_o(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function bu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Oo(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function wu(e,r,n){return r&&Oo(e.prototype,r),n&&Oo(e,n),e}function Su(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Lr(e,r)}function Lr(e,r){return Lr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Lr(e,r)}function Eu(e){var r=Du();return function(){var t=wt(e),o;if(r){var a=wt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Ru(this,o)}}function Ru(e,r){return r&&(He(r)==="object"||typeof r=="function")?r:ie(e)}function ie(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Du(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function wt(e){return wt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},wt(e)}function B(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Tu="https://fast.wistia.com/assets/external/E-v1.js",Lu="Wistia",Mu="wistia-player-",St=function(e){Su(n,e);var r=Eu(n);function n(){var t;bu(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),B(ie(t),"callPlayer",Tr.callPlayer),B(ie(t),"playerID",t.props.config.playerId||"".concat(Mu).concat((0,Tr.randomString)())),B(ie(t),"onPlay",function(){var u;return(u=t.props).onPlay.apply(u,arguments)}),B(ie(t),"onPause",function(){var u;return(u=t.props).onPause.apply(u,arguments)}),B(ie(t),"onSeek",function(){var u;return(u=t.props).onSeek.apply(u,arguments)}),B(ie(t),"onEnded",function(){var u;return(u=t.props).onEnded.apply(u,arguments)}),B(ie(t),"mute",function(){t.callPlayer("mute")}),B(ie(t),"unmute",function(){t.callPlayer("unmute")}),t}return wu(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this,i=this.props,u=i.playing,l=i.muted,s=i.controls,c=i.onReady,f=i.config,p=i.onError;(0,Tr.getSDK)(Tu,Lu).then(function(h){f.customControls&&f.customControls.forEach(function(v){return h.defineControl(v)}),self._wq=self._wq||[],self._wq.push({id:a.playerID,options:Ou({autoPlay:u,silentAutoPlay:"allow",muted:l,controlsVisibleOnLoad:s,fullscreenButton:s,playbar:s,playbackRateControl:s,qualityControl:s,volumeControl:s,settingsControl:s,smallPlayButton:s},f.options),onReady:function(_){a.player=_,a.unbind(),a.player.bind("play",a.onPlay),a.player.bind("pause",a.onPause),a.player.bind("seek",a.onSeek),a.player.bind("end",a.onEnded),c()}})},p)}},{key:"unbind",value:function(){this.player.unbind("play",this.onPlay),this.player.unbind("pause",this.onPause),this.player.unbind("seek",this.onSeek),this.player.unbind("end",this.onEnded)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){this.unbind(),this.callPlayer("remove")}},{key:"seekTo",value:function(o){this.callPlayer("time",o)}},{key:"setVolume",value:function(o){this.callPlayer("volume",o)}},{key:"setPlaybackRate",value:function(o){this.callPlayer("playbackRate",o)}},{key:"getDuration",value:function(){return this.callPlayer("duration")}},{key:"getCurrentTime",value:function(){return this.callPlayer("time")}},{key:"getSecondsLoaded",value:function(){return null}},{key:"render",value:function(){var o=this.props.url,a=o&&o.match(bo.MATCH_URL_WISTIA)[1],i="wistia_embed wistia_async_".concat(a),u={width:"100%",height:"100%"};return go.default.createElement("div",{id:this.playerID,key:a,className:i,style:u})}}]),n}(go.Component);Et.default=St;B(St,"displayName","Wistia");B(St,"canPlay",bo.canPlay.wistia);B(St,"loopOnEnded",!0)});var Lo=O(Mt=>{"use strict";y();d();function We(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?We=function(n){return typeof n}:We=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},We(e)}Object.defineProperty(Mt,"__esModule",{value:!0});Mt.default=void 0;var Eo=Iu(b()),Rt=M(),Dt=U();function To(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return To=function(){return e},e}function Iu(e){if(e&&e.__esModule)return e;if(e===null||We(e)!=="object"&&typeof e!="function")return{default:e};var r=To();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Ro(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Au(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?Ro(Object(n),!0).forEach(function(t){de(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ro(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Cu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Do(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function ku(e,r,n){return r&&Do(e.prototype,r),n&&Do(e,n),e}function Uu(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Mr(e,r)}function Mr(e,r){return Mr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Mr(e,r)}function Nu(e){var r=Vu();return function(){var t=Tt(e),o;if(r){var a=Tt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return qu(this,o)}}function qu(e,r){return r&&(We(r)==="object"||typeof r=="function")?r:xe(e)}function xe(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Vu(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Tt(e){return Tt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Tt(e)}function de(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Hu="https://player.twitch.tv/js/embed/v1.js",xu="Twitch",Wu="twitch-player-",Lt=function(e){Uu(n,e);var r=Nu(n);function n(){var t;Cu(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),de(xe(t),"callPlayer",Rt.callPlayer),de(xe(t),"playerID",t.props.config.playerId||"".concat(Wu).concat((0,Rt.randomString)())),de(xe(t),"mute",function(){t.callPlayer("setMuted",!0)}),de(xe(t),"unmute",function(){t.callPlayer("setMuted",!1)}),t}return ku(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o,a){var i=this,u=this.props,l=u.playsinline,s=u.onError,c=u.config,f=u.controls,p=Dt.MATCH_URL_TWITCH_CHANNEL.test(o),h=p?o.match(Dt.MATCH_URL_TWITCH_CHANNEL)[1]:o.match(Dt.MATCH_URL_TWITCH_VIDEO)[1];if(a){p?this.player.setChannel(h):this.player.setVideo("v"+h);return}(0,Rt.getSDK)(Hu,xu).then(function(v){i.player=new v.Player(i.playerID,Au({video:p?"":h,channel:p?h:"",height:"100%",width:"100%",playsinline:l,autoplay:i.props.playing,muted:i.props.muted,controls:p?!0:f,time:(0,Rt.parseStartTime)(o)},c.options));var _=v.Player,A=_.READY,k=_.PLAYING,G=_.PAUSE,ee=_.ENDED,V=_.ONLINE,Ie=_.OFFLINE;i.player.addEventListener(A,i.props.onReady),i.player.addEventListener(k,i.props.onPlay),i.player.addEventListener(G,i.props.onPause),i.player.addEventListener(ee,i.props.onEnded),i.player.addEventListener(V,i.props.onLoaded),i.player.addEventListener(Ie,i.props.onLoaded)},s)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){this.callPlayer("pause")}},{key:"seekTo",value:function(o){this.callPlayer("seek",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o)}},{key:"getDuration",value:function(){return this.callPlayer("getDuration")}},{key:"getCurrentTime",value:function(){return this.callPlayer("getCurrentTime")}},{key:"getSecondsLoaded",value:function(){return null}},{key:"render",value:function(){var o={width:"100%",height:"100%"};return Eo.default.createElement("div",{style:o,id:this.playerID})}}]),n}(Eo.Component);Mt.default=Lt;de(Lt,"displayName","Twitch");de(Lt,"canPlay",Dt.canPlay.twitch);de(Lt,"loopOnEnded",!0)});var Uo=O(kt=>{"use strict";y();d();function je(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?je=function(n){return typeof n}:je=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},je(e)}Object.defineProperty(kt,"__esModule",{value:!0});kt.default=void 0;var Ir=ju(b()),It=M(),Co=U();function ko(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return ko=function(){return e},e}function ju(e){if(e&&e.__esModule)return e;if(e===null||je(e)!=="object"&&typeof e!="function")return{default:e};var r=ko();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Mo(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Bu(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?Mo(Object(n),!0).forEach(function(t){ue(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Mo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function Ku(e,r){return Yu(e)||zu(e,r)||Fu(e,r)||$u()}function $u(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Fu(e,r){if(e){if(typeof e=="string")return Io(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Io(e,r)}}function Io(e,r){(r==null||r>e.length)&&(r=e.length);for(var n=0,t=new Array(r);n<r;n++)t[n]=e[n];return t}function zu(e,r){if(!(typeof Symbol>"u"||!(Symbol.iterator in Object(e)))){var n=[],t=!0,o=!1,a=void 0;try{for(var i=e[Symbol.iterator](),u;!(t=(u=i.next()).done)&&(n.push(u.value),!(r&&n.length===r));t=!0);}catch(l){o=!0,a=l}finally{try{!t&&i.return!=null&&i.return()}finally{if(o)throw a}}return n}}function Yu(e){if(Array.isArray(e))return e}function Xu(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Ao(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Gu(e,r,n){return r&&Ao(e.prototype,r),n&&Ao(e,n),e}function Zu(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Ar(e,r)}function Ar(e,r){return Ar=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Ar(e,r)}function Qu(e){var r=el();return function(){var t=At(e),o;if(r){var a=At(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Ju(this,o)}}function Ju(e,r){return r&&(je(r)==="object"||typeof r=="function")?r:Te(e)}function Te(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function el(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function At(e){return At=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},At(e)}function ue(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var tl="https://api.dmcdn.net/all.js",rl="DM",nl="dmAsyncInit",Ct=function(e){Zu(n,e);var r=Qu(n);function n(){var t;Xu(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),ue(Te(t),"callPlayer",It.callPlayer),ue(Te(t),"onDurationChange",function(){var u=t.getDuration();t.props.onDuration(u)}),ue(Te(t),"mute",function(){t.callPlayer("setMuted",!0)}),ue(Te(t),"unmute",function(){t.callPlayer("setMuted",!1)}),ue(Te(t),"ref",function(u){t.container=u}),t}return Gu(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this,i=this.props,u=i.controls,l=i.config,s=i.onError,c=i.playing,f=o.match(Co.MATCH_URL_DAILYMOTION),p=Ku(f,2),h=p[1];if(this.player){this.player.load(h,{start:(0,It.parseStartTime)(o),autoplay:c});return}(0,It.getSDK)(tl,rl,nl,function(v){return v.player}).then(function(v){if(a.container){var _=v.player;a.player=new _(a.container,{width:"100%",height:"100%",video:h,params:Bu({controls:u,autoplay:a.props.playing,mute:a.props.muted,start:(0,It.parseStartTime)(o),origin:self.location.origin},l.params),events:{apiready:a.props.onReady,seeked:function(){return a.props.onSeek(a.player.currentTime)},video_end:a.props.onEnded,durationchange:a.onDurationChange,pause:a.props.onPause,playing:a.props.onPlay,waiting:a.props.onBuffer,error:function(k){return s(k)}}})}},s)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){}},{key:"seekTo",value:function(o){this.callPlayer("seek",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o)}},{key:"getDuration",value:function(){return this.player.duration||null}},{key:"getCurrentTime",value:function(){return this.player.currentTime}},{key:"getSecondsLoaded",value:function(){return this.player.bufferedTime}},{key:"render",value:function(){var o=this.props.display,a={width:"100%",height:"100%",display:o};return Ir.default.createElement("div",{style:a},Ir.default.createElement("div",{ref:this.ref}))}}]),n}(Ir.Component);kt.default=Ct;ue(Ct,"displayName","DailyMotion");ue(Ct,"canPlay",Co.canPlay.dailymotion);ue(Ct,"loopOnEnded",!0)});var jo=O(qt=>{"use strict";y();d();function Be(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Be=function(n){return typeof n}:Be=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Be(e)}Object.defineProperty(qt,"__esModule",{value:!0});qt.default=void 0;var No=ol(b()),Cr=M(),xo=U();function Wo(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return Wo=function(){return e},e}function ol(e){if(e&&e.__esModule)return e;if(e===null||Be(e)!=="object"&&typeof e!="function")return{default:e};var r=Wo();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function qo(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Vo(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?qo(Object(n),!0).forEach(function(t){J(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):qo(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function al(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Ho(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function il(e,r,n){return r&&Ho(e.prototype,r),n&&Ho(e,n),e}function ul(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&kr(e,r)}function kr(e,r){return kr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},kr(e,r)}function ll(e){var r=cl();return function(){var t=Ut(e),o;if(r){var a=Ut(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return sl(this,o)}}function sl(e,r){return r&&(Be(r)==="object"||typeof r=="function")?r:he(e)}function he(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function cl(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Ut(e){return Ut=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Ut(e)}function J(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var fl="https://widget.mixcloud.com/media/js/widgetApi.js",pl="Mixcloud",Nt=function(e){ul(n,e);var r=ll(n);function n(){var t;al(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),J(he(t),"callPlayer",Cr.callPlayer),J(he(t),"duration",null),J(he(t),"currentTime",null),J(he(t),"secondsLoaded",null),J(he(t),"mute",function(){}),J(he(t),"unmute",function(){}),J(he(t),"ref",function(u){t.iframe=u}),t}return il(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this;(0,Cr.getSDK)(fl,pl).then(function(i){a.player=i.PlayerWidget(a.iframe),a.player.ready.then(function(){a.player.events.play.on(a.props.onPlay),a.player.events.pause.on(a.props.onPause),a.player.events.ended.on(a.props.onEnded),a.player.events.error.on(a.props.error),a.player.events.progress.on(function(u,l){a.currentTime=u,a.duration=l}),a.props.onReady()})},this.props.onError)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){}},{key:"seekTo",value:function(o){this.callPlayer("seek",o)}},{key:"setVolume",value:function(o){}},{key:"getDuration",value:function(){return this.duration}},{key:"getCurrentTime",value:function(){return this.currentTime}},{key:"getSecondsLoaded",value:function(){return null}},{key:"render",value:function(){var o=this.props,a=o.url,i=o.config,u=a.match(xo.MATCH_URL_MIXCLOUD)[1],l={width:"100%",height:"100%"},s=(0,Cr.queryString)(Vo(Vo({},i.options),{},{feed:"/".concat(u,"/")}));return No.default.createElement("iframe",{key:u,ref:this.ref,style:l,src:"https://www.mixcloud.com/widget/iframe/?".concat(s),frameBorder:"0"})}}]),n}(No.Component);qt.default=Nt;J(Nt,"displayName","Mixcloud");J(Nt,"canPlay",xo.canPlay.mixcloud);J(Nt,"loopOnEnded",!0)});var Yo=O(Ht=>{"use strict";y();d();function $e(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?$e=function(n){return typeof n}:$e=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},$e(e)}Object.defineProperty(Ht,"__esModule",{value:!0});Ht.default=void 0;var Ur=yl(b()),Bo=M(),Fo=U();function zo(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return zo=function(){return e},e}function yl(e){if(e&&e.__esModule)return e;if(e===null||$e(e)!=="object"&&typeof e!="function")return{default:e};var r=zo();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Ko(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function dl(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?Ko(Object(n),!0).forEach(function(t){Oe(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ko(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function hl(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function $o(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function vl(e,r,n){return r&&$o(e.prototype,r),n&&$o(e,n),e}function ml(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Nr(e,r)}function Nr(e,r){return Nr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Nr(e,r)}function Pl(e){var r=_l();return function(){var t=Vt(e),o;if(r){var a=Vt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return gl(this,o)}}function gl(e,r){return r&&($e(r)==="object"||typeof r=="function")?r:Ke(e)}function Ke(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function _l(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Vt(e){return Vt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Vt(e)}function Oe(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Ol="https://play.vidyard.com/embed/v4.js",bl="VidyardV4",wl="onVidyardAPI",qr=function(e){ml(n,e);var r=Pl(n);function n(){var t;hl(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),Oe(Ke(t),"callPlayer",Bo.callPlayer),Oe(Ke(t),"mute",function(){t.setVolume(0)}),Oe(Ke(t),"unmute",function(){t.props.volume!==null&&t.setVolume(t.props.volume)}),Oe(Ke(t),"ref",function(u){t.container=u}),t}return vl(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this,i=this.props,u=i.playing,l=i.config,s=i.onError,c=i.onDuration,f=o&&o.match(Fo.MATCH_URL_VIDYARD)[1];this.player&&this.stop(),(0,Bo.getSDK)(Ol,bl,wl).then(function(p){a.container&&(p.api.addReadyListener(function(h,v){a.player=v,a.player.on("ready",a.props.onReady),a.player.on("play",a.props.onPlay),a.player.on("pause",a.props.onPause),a.player.on("seek",a.props.onSeek),a.player.on("playerComplete",a.props.onEnded)},f),p.api.renderPlayer(dl({uuid:f,container:a.container,autoplay:u?1:0},l.options)),p.api.getPlayerMetadata(f).then(function(h){a.duration=h.length_in_seconds,c(h.length_in_seconds)}))},s)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){self.VidyardV4.api.destroyPlayer(this.player)}},{key:"seekTo",value:function(o){this.callPlayer("seek",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o)}},{key:"setPlaybackRate",value:function(o){this.callPlayer("setPlaybackSpeed",o)}},{key:"getDuration",value:function(){return this.duration}},{key:"getCurrentTime",value:function(){return this.callPlayer("currentTime")}},{key:"getSecondsLoaded",value:function(){return null}},{key:"render",value:function(){var o=this.props.display,a={width:"100%",height:"100%",display:o};return Ur.default.createElement("div",{style:a},Ur.default.createElement("div",{ref:this.ref}))}}]),n}(Ur.Component);Ht.default=qr;Oe(qr,"displayName","Vidyard");Oe(qr,"canPlay",Fo.canPlay.vidyard)});var Jo=O(Wt=>{"use strict";y();d();function Fe(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Fe=function(n){return typeof n}:Fe=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Fe(e)}Object.defineProperty(Wt,"__esModule",{value:!0});Wt.default=void 0;var Xo=El(b()),Go=M(),Sl=U();function Qo(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return Qo=function(){return e},e}function El(e){if(e&&e.__esModule)return e;if(e===null||Fe(e)!=="object"&&typeof e!="function")return{default:e};var r=Qo();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Rl(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function Zo(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Dl(e,r,n){return r&&Zo(e.prototype,r),n&&Zo(e,n),e}function Tl(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Vr(e,r)}function Vr(e,r){return Vr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Vr(e,r)}function Ll(e){var r=Il();return function(){var t=xt(e),o;if(r){var a=xt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Ml(this,o)}}function Ml(e,r){return r&&(Fe(r)==="object"||typeof r=="function")?r:ve(e)}function ve(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Il(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function xt(e){return xt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},xt(e)}function le(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Al="https://cdn.embed.ly/player-0.1.0.min.js",Cl="playerjs",Hr=function(e){Tl(n,e);var r=Ll(n);function n(){var t;Rl(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),le(ve(t),"callPlayer",Go.callPlayer),le(ve(t),"duration",null),le(ve(t),"currentTime",null),le(ve(t),"secondsLoaded",null),le(ve(t),"mute",function(){t.callPlayer("mute")}),le(ve(t),"unmute",function(){t.callPlayer("unmute")}),le(ve(t),"ref",function(u){t.iframe=u}),t}return Dl(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this)}},{key:"load",value:function(o){var a=this;(0,Go.getSDK)(Al,Cl).then(function(i){a.iframe&&(a.player=new i.Player(a.iframe),a.player.on("ready",function(){a.player.isReady=!0,a.player.on("play",a.props.onPlay),a.player.on("pause",a.props.onPause),a.player.on("seeked",a.props.onSeek),a.player.on("ended",a.props.onEnded),a.player.on("error",a.props.onError),a.player.on("timeupdate",function(u){var l=u.duration,s=u.seconds;a.duration=l,a.currentTime=s}),a.player.on("buffered",function(u){var l=u.percent;a.duration&&(a.secondsLoaded=a.duration*l)}),a.player.setLoop(a.props.loop),a.props.muted&&a.player.mute(),setTimeout(function(){a.props.onReady()})}))},this.props.onError)}},{key:"play",value:function(){this.callPlayer("play")}},{key:"pause",value:function(){this.callPlayer("pause")}},{key:"stop",value:function(){}},{key:"seekTo",value:function(o){this.callPlayer("setCurrentTime",o)}},{key:"setVolume",value:function(o){this.callPlayer("setVolume",o)}},{key:"setLoop",value:function(o){this.callPlayer("setLoop",o)}},{key:"getDuration",value:function(){return this.duration}},{key:"getCurrentTime",value:function(){return this.currentTime}},{key:"getSecondsLoaded",value:function(){return this.secondsLoaded}},{key:"render",value:function(){var o={width:"100%",height:"100%"};return Xo.default.createElement("iframe",{ref:this.ref,src:this.props.url,frameBorder:"0",scrolling:"no",style:o,allowFullScreen:!0,allow:"encrypted-media",referrerPolicy:"no-referrer-when-downgrade"})}}]),n}(Xo.Component);Wt.default=Hr;le(Hr,"displayName","Kaltura");le(Hr,"canPlay",Sl.canPlay.kaltura)});var oa=O(Bt=>{"use strict";y();d();function Xe(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Xe=function(n){return typeof n}:Xe=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Xe(e)}Object.defineProperty(Bt,"__esModule",{value:!0});Bt.default=void 0;var ze=kl(b()),se=M(),Ye=U();function ra(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return ra=function(){return e},e}function kl(e){if(e&&e.__esModule)return e;if(e===null||Xe(e)!=="object"&&typeof e!="function")return{default:e};var r=ra();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Ge(){return Ge=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},Ge.apply(this,arguments)}function Ul(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ea(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Nl(e,r,n){return r&&ea(e.prototype,r),n&&ea(e,n),e}function ql(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Wr(e,r)}function Wr(e,r){return Wr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Wr(e,r)}function Vl(e){var r=xl();return function(){var t=jt(e),o;if(r){var a=jt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return Hl(this,o)}}function Hl(e,r){return r&&(Xe(r)==="object"||typeof r=="function")?r:T(e)}function T(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function xl(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function jt(e){return jt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},jt(e)}function D(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var na=typeof navigator<"u",Wl=na&&navigator.platform==="MacIntel"&&navigator.maxTouchPoints>1,ta=na&&(/iPad|iPhone|iPod/.test(navigator.userAgent)||Wl)&&!self.MSStream,jl="https://cdn.jsdelivr.net/npm/hls.js@VERSION/dist/hls.min.js",Bl="Hls",Kl="https://cdnjs.cloudflare.com/ajax/libs/dashjs/VERSION/dash.all.min.js",$l="dashjs",Fl="https://cdn.jsdelivr.net/npm/flv.js@VERSION/dist/flv.min.js",zl="flvjs",Yl=/www\.dropbox\.com\/.+/,xr=/https:\/\/watch\.cloudflarestream\.com\/([a-z0-9]+)/,Xl="https://videodelivery.net/{id}/manifest/video.m3u8",jr=function(e){ql(n,e);var r=Vl(n);function n(){var t;Ul(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),D(T(t),"onReady",function(){var u;return(u=t.props).onReady.apply(u,arguments)}),D(T(t),"onPlay",function(){var u;return(u=t.props).onPlay.apply(u,arguments)}),D(T(t),"onBuffer",function(){var u;return(u=t.props).onBuffer.apply(u,arguments)}),D(T(t),"onBufferEnd",function(){var u;return(u=t.props).onBufferEnd.apply(u,arguments)}),D(T(t),"onPause",function(){var u;return(u=t.props).onPause.apply(u,arguments)}),D(T(t),"onEnded",function(){var u;return(u=t.props).onEnded.apply(u,arguments)}),D(T(t),"onError",function(){var u;return(u=t.props).onError.apply(u,arguments)}),D(T(t),"onEnablePIP",function(){var u;return(u=t.props).onEnablePIP.apply(u,arguments)}),D(T(t),"onDisablePIP",function(u){var l=t.props,s=l.onDisablePIP,c=l.playing;s(u),c&&t.play()}),D(T(t),"onPresentationModeChange",function(u){if(t.player&&(0,se.supportsWebKitPresentationMode)(t.player)){var l=t.player.webkitPresentationMode;l==="picture-in-picture"?t.onEnablePIP(u):l==="inline"&&t.onDisablePIP(u)}}),D(T(t),"onSeek",function(u){t.props.onSeek(u.target.currentTime)}),D(T(t),"mute",function(){t.player.muted=!0}),D(T(t),"unmute",function(){t.player.muted=!1}),D(T(t),"renderSourceElement",function(u,l){return typeof u=="string"?ze.default.createElement("source",{key:l,src:u}):ze.default.createElement("source",Ge({key:l},u))}),D(T(t),"renderTrack",function(u,l){return ze.default.createElement("track",Ge({key:l},u))}),D(T(t),"ref",function(u){t.player&&(t.prevPlayer=t.player),t.player=u}),t}return Nl(n,[{key:"componentDidMount",value:function(){this.props.onMount&&this.props.onMount(this),this.addListeners(this.player),ta&&this.player.load()}},{key:"componentDidUpdate",value:function(o){this.shouldUseAudio(this.props)!==this.shouldUseAudio(o)&&(this.removeListeners(this.prevPlayer,o.url),this.addListeners(this.player)),this.props.url!==o.url&&!(0,se.isMediaStream)(this.props.url)&&(this.player.srcObject=null)}},{key:"componentWillUnmount",value:function(){this.removeListeners(this.player),this.hls&&this.hls.destroy()}},{key:"addListeners",value:function(o){var a=this.props,i=a.url,u=a.playsinline;o.addEventListener("play",this.onPlay),o.addEventListener("waiting",this.onBuffer),o.addEventListener("playing",this.onBufferEnd),o.addEventListener("pause",this.onPause),o.addEventListener("seeked",this.onSeek),o.addEventListener("ended",this.onEnded),o.addEventListener("error",this.onError),o.addEventListener("enterpictureinpicture",this.onEnablePIP),o.addEventListener("leavepictureinpicture",this.onDisablePIP),o.addEventListener("webkitpresentationmodechanged",this.onPresentationModeChange),this.shouldUseHLS(i)||o.addEventListener("canplay",this.onReady),u&&(o.setAttribute("playsinline",""),o.setAttribute("webkit-playsinline",""),o.setAttribute("x5-playsinline",""))}},{key:"removeListeners",value:function(o,a){o.removeEventListener("canplay",this.onReady),o.removeEventListener("play",this.onPlay),o.removeEventListener("waiting",this.onBuffer),o.removeEventListener("playing",this.onBufferEnd),o.removeEventListener("pause",this.onPause),o.removeEventListener("seeked",this.onSeek),o.removeEventListener("ended",this.onEnded),o.removeEventListener("error",this.onError),o.removeEventListener("enterpictureinpicture",this.onEnablePIP),o.removeEventListener("leavepictureinpicture",this.onDisablePIP),o.removeEventListener("webkitpresentationmodechanged",this.onPresentationModeChange),this.shouldUseHLS(a)||o.removeEventListener("canplay",this.onReady)}},{key:"shouldUseAudio",value:function(o){return o.config.forceVideo||o.config.attributes.poster?!1:Ye.AUDIO_EXTENSIONS.test(o.url)||o.config.forceAudio}},{key:"shouldUseHLS",value:function(o){return this.props.config.forceHLS?!0:ta?!1:Ye.HLS_EXTENSIONS.test(o)||xr.test(o)}},{key:"shouldUseDASH",value:function(o){return Ye.DASH_EXTENSIONS.test(o)||this.props.config.forceDASH}},{key:"shouldUseFLV",value:function(o){return Ye.FLV_EXTENSIONS.test(o)||this.props.config.forceFLV}},{key:"load",value:function(o){var a=this,i=this.props.config,u=i.hlsVersion,l=i.hlsOptions,s=i.dashVersion,c=i.flvVersion;if(this.hls&&this.hls.destroy(),this.dash&&this.dash.reset(),this.shouldUseHLS(o)&&(0,se.getSDK)(jl.replace("VERSION",u),Bl).then(function(f){if(a.hls=new f(l),a.hls.on(f.Events.MANIFEST_PARSED,function(){a.props.onReady()}),a.hls.on(f.Events.ERROR,function(h,v){a.props.onError(h,v,a.hls,f)}),xr.test(o)){var p=o.match(xr)[1];a.hls.loadSource(Xl.replace("{id}",p))}else a.hls.loadSource(o);a.hls.attachMedia(a.player),a.props.onLoaded()}),this.shouldUseDASH(o)&&(0,se.getSDK)(Kl.replace("VERSION",s),$l).then(function(f){a.dash=f.MediaPlayer().create(),a.dash.initialize(a.player,o,a.props.playing),a.dash.on("error",a.props.onError),parseInt(s)<3?a.dash.getDebug().setLogToBrowserConsole(!1):a.dash.updateSettings({debug:{logLevel:f.Debug.LOG_LEVEL_NONE}}),a.props.onLoaded()}),this.shouldUseFLV(o)&&(0,se.getSDK)(Fl.replace("VERSION",c),zl).then(function(f){a.flv=f.createPlayer({type:"flv",url:o}),a.flv.attachMediaElement(a.player),a.flv.load(),a.props.onLoaded()}),o instanceof Array)this.player.load();else if((0,se.isMediaStream)(o))try{this.player.srcObject=o}catch{this.player.src=self.URL.createObjectURL(o)}}},{key:"play",value:function(){var o=this.player.play();o&&o.catch(this.props.onError)}},{key:"pause",value:function(){this.player.pause()}},{key:"stop",value:function(){this.player.removeAttribute("src"),this.dash&&this.dash.reset()}},{key:"seekTo",value:function(o){this.player.currentTime=o}},{key:"setVolume",value:function(o){this.player.volume=o}},{key:"enablePIP",value:function(){this.player.requestPictureInPicture&&document.pictureInPictureElement!==this.player?this.player.requestPictureInPicture():(0,se.supportsWebKitPresentationMode)(this.player)&&this.player.webkitPresentationMode!=="picture-in-picture"&&this.player.webkitSetPresentationMode("picture-in-picture")}},{key:"disablePIP",value:function(){document.exitPictureInPicture&&document.pictureInPictureElement===this.player?document.exitPictureInPicture():(0,se.supportsWebKitPresentationMode)(this.player)&&this.player.webkitPresentationMode!=="inline"&&this.player.webkitSetPresentationMode("inline")}},{key:"setPlaybackRate",value:function(o){this.player.playbackRate=o}},{key:"getDuration",value:function(){if(!this.player)return null;var o=this.player,a=o.duration,i=o.seekable;return a===1/0&&i.length>0?i.end(i.length-1):a}},{key:"getCurrentTime",value:function(){return this.player?this.player.currentTime:null}},{key:"getSecondsLoaded",value:function(){if(!this.player)return null;var o=this.player.buffered;if(o.length===0)return 0;var a=o.end(o.length-1),i=this.getDuration();return a>i?i:a}},{key:"getSource",value:function(o){var a=this.shouldUseHLS(o),i=this.shouldUseDASH(o),u=this.shouldUseFLV(o);if(!(o instanceof Array||(0,se.isMediaStream)(o)||a||i||u))return Yl.test(o)?o.replace("www.dropbox.com","dl.dropboxusercontent.com"):o}},{key:"render",value:function(){var o=this.props,a=o.url,i=o.playing,u=o.loop,l=o.controls,s=o.muted,c=o.config,f=o.width,p=o.height,h=this.shouldUseAudio(this.props),v=h?"audio":"video",_={width:f==="auto"?f:"100%",height:p==="auto"?p:"100%"};return ze.default.createElement(v,Ge({ref:this.ref,src:this.getSource(a),style:_,preload:"auto",autoPlay:i||void 0,controls:l,muted:s,loop:u},c.attributes),a instanceof Array&&a.map(this.renderSourceElement),c.tracks.map(this.renderTrack))}}]),n}(ze.Component);Bt.default=jr;D(jr,"displayName","FilePlayer");D(jr,"canPlay",Ye.canPlay.file)});var ia=O($t=>{"use strict";y();d();Object.defineProperty($t,"__esModule",{value:!0});$t.default=void 0;var K=b(),Gl=M(),N=U();function Kt(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Kt=function(n){return typeof n}:Kt=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Kt(e)}function aa(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return aa=function(){return e},e}function $(e){if(e&&e.__esModule)return e;if(e===null||Kt(e)!=="object"&&typeof e!="function")return{default:e};var r=aa();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}var Zl=[{key:"youtube",name:"YouTube",canPlay:N.canPlay.youtube,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $($n())})})},{key:"soundcloud",name:"SoundCloud",canPlay:N.canPlay.soundcloud,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(Qn())})})},{key:"vimeo",name:"Vimeo",canPlay:N.canPlay.vimeo,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(oo())})})},{key:"facebook",name:"Facebook",canPlay:N.canPlay.facebook,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(fo())})})},{key:"streamable",name:"Streamable",canPlay:N.canPlay.streamable,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(Po())})})},{key:"wistia",name:"Wistia",canPlay:N.canPlay.wistia,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(So())})})},{key:"twitch",name:"Twitch",canPlay:N.canPlay.twitch,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(Lo())})})},{key:"dailymotion",name:"DailyMotion",canPlay:N.canPlay.dailymotion,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(Uo())})})},{key:"mixcloud",name:"Mixcloud",canPlay:N.canPlay.mixcloud,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(jo())})})},{key:"vidyard",name:"Vidyard",canPlay:N.canPlay.vidyard,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(Yo())})})},{key:"kaltura",name:"Kaltura",canPlay:N.canPlay.kaltura,lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(Jo())})})},{key:"file",name:"FilePlayer",canPlay:N.canPlay.file,canEnablePIP:function(r){return N.canPlay.file(r)&&(document.pictureInPictureEnabled||(0,Gl.supportsWebKitPresentationMode)())&&!N.AUDIO_EXTENSIONS.test(r)},lazyPlayer:(0,K.lazy)(function(){return Promise.resolve().then(function(){return $(oa())})})}];$t.default=Zl});var Br=O((Jc,ua)=>{y();d();var Ql=typeof Element<"u",Jl=typeof Map=="function",es=typeof Set=="function",ts=typeof ArrayBuffer=="function"&&!!ArrayBuffer.isView;function Ft(e,r){if(e===r)return!0;if(e&&r&&typeof e=="object"&&typeof r=="object"){if(e.constructor!==r.constructor)return!1;var n,t,o;if(Array.isArray(e)){if(n=e.length,n!=r.length)return!1;for(t=n;t--!==0;)if(!Ft(e[t],r[t]))return!1;return!0}var a;if(Jl&&e instanceof Map&&r instanceof Map){if(e.size!==r.size)return!1;for(a=e.entries();!(t=a.next()).done;)if(!r.has(t.value[0]))return!1;for(a=e.entries();!(t=a.next()).done;)if(!Ft(t.value[1],r.get(t.value[0])))return!1;return!0}if(es&&e instanceof Set&&r instanceof Set){if(e.size!==r.size)return!1;for(a=e.entries();!(t=a.next()).done;)if(!r.has(t.value[0]))return!1;return!0}if(ts&&ArrayBuffer.isView(e)&&ArrayBuffer.isView(r)){if(n=e.length,n!=r.length)return!1;for(t=n;t--!==0;)if(e[t]!==r[t])return!1;return!0}if(e.constructor===RegExp)return e.source===r.source&&e.flags===r.flags;if(e.valueOf!==Object.prototype.valueOf)return e.valueOf()===r.valueOf();if(e.toString!==Object.prototype.toString)return e.toString()===r.toString();if(o=Object.keys(e),n=o.length,n!==Object.keys(r).length)return!1;for(t=n;t--!==0;)if(!Object.prototype.hasOwnProperty.call(r,o[t]))return!1;if(Ql&&e instanceof Element)return!1;for(t=n;t--!==0;)if(!((o[t]==="_owner"||o[t]==="__v"||o[t]==="__o")&&e.$$typeof)&&!Ft(e[o[t]],r[o[t]]))return!1;return!0}return e!==e&&r!==r}ua.exports=function(r,n){try{return Ft(r,n)}catch(t){if((t.message||"").match(/stack|recursion/i))return console.warn("react-fast-compare cannot handle circular refs"),!1;throw t}}});var $r=O(Me=>{"use strict";y();d();Object.defineProperty(Me,"__esModule",{value:!0});Me.defaultProps=Me.propTypes=void 0;var ce=rs(Ka());function rs(e){return e&&e.__esModule?e:{default:e}}var x=ce.default.string,W=ce.default.bool,Le=ce.default.number,Kr=ce.default.array,Ze=ce.default.oneOfType,F=ce.default.shape,q=ce.default.object,L=ce.default.func,la=ce.default.node,ns={url:Ze([x,Kr,q]),playing:W,loop:W,controls:W,volume:Le,muted:W,playbackRate:Le,width:Ze([x,Le]),height:Ze([x,Le]),style:q,progressInterval:Le,playsinline:W,pip:W,stopOnUnmount:W,light:Ze([W,x]),playIcon:la,previewTabIndex:Le,fallback:la,wrapper:Ze([x,L,F({render:L.isRequired})]),config:F({soundcloud:F({options:q}),youtube:F({playerVars:q,embedOptions:q,onUnstarted:L}),facebook:F({appId:x,version:x,playerId:x,attributes:q}),dailymotion:F({params:q}),vimeo:F({playerOptions:q}),file:F({attributes:q,tracks:Kr,forceVideo:W,forceAudio:W,forceHLS:W,forceDASH:W,forceFLV:W,hlsOptions:q,hlsVersion:x,dashVersion:x,flvVersion:x}),wistia:F({options:q,playerId:x,customControls:Kr}),mixcloud:F({options:q}),twitch:F({options:q,playerId:x}),vidyard:F({options:q})}),onReady:L,onStart:L,onPlay:L,onPause:L,onBuffer:L,onBufferEnd:L,onEnded:L,onError:L,onDuration:L,onSeek:L,onProgress:L,onClickPreview:L,onEnablePIP:L,onDisablePIP:L};Me.propTypes=ns;var C=function(){},os={playing:!1,loop:!1,controls:!1,volume:null,muted:!1,playbackRate:1,width:"640px",height:"360px",style:{},progressInterval:1e3,playsinline:!1,pip:!1,stopOnUnmount:!0,light:!1,fallback:null,wrapper:"div",previewTabIndex:0,config:{soundcloud:{options:{visual:!0,buying:!1,liking:!1,download:!1,sharing:!1,show_comments:!1,show_playcount:!1}},youtube:{playerVars:{playsinline:1,showinfo:0,rel:0,iv_load_policy:3,modestbranding:1},embedOptions:{},onUnstarted:C},facebook:{appId:"1309697205772819",version:"v3.3",playerId:null,attributes:{}},dailymotion:{params:{api:1,"endscreen-enable":!1}},vimeo:{playerOptions:{autopause:!1,byline:!1,portrait:!1,title:!1}},file:{attributes:{},tracks:[],forceVideo:!1,forceAudio:!1,forceHLS:!1,forceDASH:!1,forceFLV:!1,hlsOptions:{},hlsVersion:"0.14.16",dashVersion:"3.1.3",flvVersion:"1.5.0"},wistia:{options:{},playerId:null,customControls:null},mixcloud:{options:{hide_cover:1}},twitch:{options:{},playerId:null},vidyard:{options:{}}},onReady:C,onStart:C,onPlay:C,onPause:C,onBuffer:C,onBufferEnd:C,onEnded:C,onError:C,onDuration:C,onSeek:C,onProgress:C,onClickPreview:C,onEnablePIP:C,onDisablePIP:C};Me.defaultProps=os});var ya=O(Xt=>{"use strict";y();d();function Qe(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Qe=function(n){return typeof n}:Qe=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Qe(e)}Object.defineProperty(Xt,"__esModule",{value:!0});Xt.default=void 0;var sa=us(b()),as=is(Br()),fa=$r();function is(e){return e&&e.__esModule?e:{default:e}}function pa(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return pa=function(){return e},e}function us(e){if(e&&e.__esModule)return e;if(e===null||Qe(e)!=="object"&&typeof e!="function")return{default:e};var r=pa();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function Fr(){return Fr=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},Fr.apply(this,arguments)}function ls(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ca(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function ss(e,r,n){return r&&ca(e.prototype,r),n&&ca(e,n),e}function cs(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&zr(e,r)}function zr(e,r){return zr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},zr(e,r)}function fs(e){var r=ys();return function(){var t=zt(e),o;if(r){var a=zt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return ps(this,o)}}function ps(e,r){return r&&(Qe(r)==="object"||typeof r=="function")?r:R(e)}function R(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function ys(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function zt(e){return zt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},zt(e)}function S(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var ds=5e3,Yt=function(e){cs(n,e);var r=fs(n);function n(){var t;ls(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),S(R(t),"mounted",!1),S(R(t),"isReady",!1),S(R(t),"isPlaying",!1),S(R(t),"isLoading",!0),S(R(t),"loadOnReady",null),S(R(t),"startOnPlay",!0),S(R(t),"seekOnPlay",null),S(R(t),"onDurationCalled",!1),S(R(t),"handlePlayerMount",function(u){t.player=u,t.player.load(t.props.url),t.progress()}),S(R(t),"getInternalPlayer",function(u){return t.player?t.player[u]:null}),S(R(t),"progress",function(){if(t.props.url&&t.player&&t.isReady){var u=t.getCurrentTime()||0,l=t.getSecondsLoaded(),s=t.getDuration();if(s){var c={playedSeconds:u,played:u/s};l!==null&&(c.loadedSeconds=l,c.loaded=l/s),(c.playedSeconds!==t.prevPlayed||c.loadedSeconds!==t.prevLoaded)&&t.props.onProgress(c),t.prevPlayed=c.playedSeconds,t.prevLoaded=c.loadedSeconds}}t.progressTimeout=setTimeout(t.progress,t.props.progressFrequency||t.props.progressInterval)}),S(R(t),"handleReady",function(){if(t.mounted){t.isReady=!0,t.isLoading=!1;var u=t.props,l=u.onReady,s=u.playing,c=u.volume,f=u.muted;l(),!f&&c!==null&&t.player.setVolume(c),t.loadOnReady?(t.player.load(t.loadOnReady,!0),t.loadOnReady=null):s&&t.player.play(),t.handleDurationCheck()}}),S(R(t),"handlePlay",function(){t.isPlaying=!0,t.isLoading=!1;var u=t.props,l=u.onStart,s=u.onPlay,c=u.playbackRate;t.startOnPlay&&(t.player.setPlaybackRate&&c!==1&&t.player.setPlaybackRate(c),l(),t.startOnPlay=!1),s(),t.seekOnPlay&&(t.seekTo(t.seekOnPlay),t.seekOnPlay=null),t.handleDurationCheck()}),S(R(t),"handlePause",function(u){t.isPlaying=!1,t.isLoading||t.props.onPause(u)}),S(R(t),"handleEnded",function(){var u=t.props,l=u.activePlayer,s=u.loop,c=u.onEnded;l.loopOnEnded&&s&&t.seekTo(0),s||(t.isPlaying=!1,c())}),S(R(t),"handleError",function(){var u;t.isLoading=!1,(u=t.props).onError.apply(u,arguments)}),S(R(t),"handleDurationCheck",function(){clearTimeout(t.durationCheckTimeout);var u=t.getDuration();u?t.onDurationCalled||(t.props.onDuration(u),t.onDurationCalled=!0):t.durationCheckTimeout=setTimeout(t.handleDurationCheck,100)}),S(R(t),"handleLoaded",function(){t.isLoading=!1}),t}return ss(n,[{key:"componentDidMount",value:function(){this.mounted=!0}},{key:"componentWillUnmount",value:function(){clearTimeout(this.progressTimeout),clearTimeout(this.durationCheckTimeout),this.isReady&&this.props.stopOnUnmount&&(this.player.stop(),this.player.disablePIP&&this.player.disablePIP()),this.mounted=!1}},{key:"componentDidUpdate",value:function(o){var a=this;if(this.player){var i=this.props,u=i.url,l=i.playing,s=i.volume,c=i.muted,f=i.playbackRate,p=i.pip,h=i.loop,v=i.activePlayer;if(!(0,as.default)(o.url,u)){if(this.isLoading&&!v.forceLoad){console.warn("ReactPlayer: the attempt to load ".concat(u," is being deferred until the player has loaded")),this.loadOnReady=u;return}this.isLoading=!0,this.startOnPlay=!0,this.onDurationCalled=!1,this.player.load(u,this.isReady)}!o.playing&&l&&!this.isPlaying&&this.player.play(),o.playing&&!l&&this.isPlaying&&this.player.pause(),!o.pip&&p&&this.player.enablePIP&&this.player.enablePIP(),o.pip&&!p&&this.player.disablePIP&&this.player.disablePIP(),o.volume!==s&&s!==null&&this.player.setVolume(s),o.muted!==c&&(c?this.player.mute():(this.player.unmute(),s!==null&&setTimeout(function(){return a.player.setVolume(s)}))),o.playbackRate!==f&&this.player.setPlaybackRate&&this.player.setPlaybackRate(f),o.loop!==h&&this.player.setLoop&&this.player.setLoop(h)}}},{key:"getDuration",value:function(){return this.isReady?this.player.getDuration():null}},{key:"getCurrentTime",value:function(){return this.isReady?this.player.getCurrentTime():null}},{key:"getSecondsLoaded",value:function(){return this.isReady?this.player.getSecondsLoaded():null}},{key:"seekTo",value:function(o,a){var i=this;if(!this.isReady&&o!==0){this.seekOnPlay=o,setTimeout(function(){i.seekOnPlay=null},ds);return}var u=a?a==="fraction":o>0&&o<1;if(u){var l=this.player.getDuration();if(!l){console.warn("ReactPlayer: could not seek using fraction \u2013\xA0duration not yet available");return}this.player.seekTo(l*o);return}this.player.seekTo(o)}},{key:"render",value:function(){var o=this.props.activePlayer;return o?sa.default.createElement(o,Fr({},this.props,{onMount:this.handlePlayerMount,onReady:this.handleReady,onPlay:this.handlePlay,onPause:this.handlePause,onEnded:this.handleEnded,onLoaded:this.handleLoaded,onError:this.handleError})):null}}]),n}(sa.Component);Xt.default=Yt;S(Yt,"displayName","Player");S(Yt,"propTypes",fa.propTypes);S(Yt,"defaultProps",fa.defaultProps)});var Pa=O(er=>{"use strict";y();d();function Je(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Je=function(n){return typeof n}:Je=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},Je(e)}Object.defineProperty(er,"__esModule",{value:!0});er.default=void 0;var Gt=hs(b());function ma(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return ma=function(){return e},e}function hs(e){if(e&&e.__esModule)return e;if(e===null||Je(e)!=="object"&&typeof e!="function")return{default:e};var r=ma();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}function da(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function ha(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?da(Object(n),!0).forEach(function(t){Qt(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):da(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function vs(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function va(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function ms(e,r,n){return r&&va(e.prototype,r),n&&va(e,n),e}function Ps(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&Gr(e,r)}function Gr(e,r){return Gr=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},Gr(e,r)}function gs(e){var r=Os();return function(){var t=Jt(e),o;if(r){var a=Jt(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return _s(this,o)}}function _s(e,r){return r&&(Je(r)==="object"||typeof r=="function")?r:Zt(e)}function Zt(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Os(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function Jt(e){return Jt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Jt(e)}function Qt(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}var Yr="64px",Xr={},bs=function(e){Ps(n,e);var r=gs(n);function n(){var t;vs(this,n);for(var o=arguments.length,a=new Array(o),i=0;i<o;i++)a[i]=arguments[i];return t=r.call.apply(r,[this].concat(a)),Qt(Zt(t),"mounted",!1),Qt(Zt(t),"state",{image:null}),Qt(Zt(t),"handleKeyPress",function(u){(u.key==="Enter"||u.key===" ")&&t.props.onClick()}),t}return ms(n,[{key:"componentDidMount",value:function(){this.mounted=!0,this.fetchImage(this.props)}},{key:"componentDidUpdate",value:function(o){var a=this.props,i=a.url,u=a.light;(o.url!==i||o.light!==u)&&this.fetchImage(this.props)}},{key:"componentWillUnmount",value:function(){this.mounted=!1}},{key:"fetchImage",value:function(o){var a=this,i=o.url,u=o.light;if(typeof u=="string"){this.setState({image:u});return}if(Xr[i]){this.setState({image:Xr[i]});return}return this.setState({image:null}),self.fetch("https://noembed.com/embed?url=".concat(i)).then(function(l){return l.json()}).then(function(l){if(l.thumbnail_url&&a.mounted){var s=l.thumbnail_url.replace("height=100","height=480");a.setState({image:s}),Xr[i]=s}})}},{key:"render",value:function(){var o=this.props,a=o.onClick,i=o.playIcon,u=o.previewTabIndex,l=this.state.image,s={display:"flex",alignItems:"center",justifyContent:"center"},c={preview:ha({width:"100%",height:"100%",backgroundImage:l?"url(".concat(l,")"):void 0,backgroundSize:"cover",backgroundPosition:"center",cursor:"pointer"},s),shadow:ha({background:"radial-gradient(rgb(0, 0, 0, 0.3), rgba(0, 0, 0, 0) 60%)",borderRadius:Yr,width:Yr,height:Yr},s),playIcon:{borderStyle:"solid",borderWidth:"16px 0 16px 26px",borderColor:"transparent transparent transparent white",marginLeft:"7px"}},f=Gt.default.createElement("div",{style:c.shadow,className:"react-player__shadow"},Gt.default.createElement("div",{style:c.playIcon,className:"react-player__play-icon"}));return Gt.default.createElement("div",{style:c.preview,className:"react-player__preview",onClick:a,tabIndex:u,onKeyPress:this.handleKeyPress},i||f)}}]),n}(Gt.Component);er.default=bs});var Ea=O(or=>{"use strict";y();d();Object.defineProperty(or,"__esModule",{value:!0});or.createReactPlayer=void 0;var be=Sa(b()),ws=nr(ln()),Zr=nr($a()),ga=nr(Br()),tt=$r(),Ss=M(),Es=nr(ya());function nr(e){return e&&e.__esModule?e:{default:e}}function rt(e){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?rt=function(n){return typeof n}:rt=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},rt(e)}function _a(e,r){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var t=Object.getOwnPropertySymbols(e);r&&(t=t.filter(function(o){return Object.getOwnPropertyDescriptor(e,o).enumerable})),n.push.apply(n,t)}return n}function Oa(e){for(var r=1;r<arguments.length;r++){var n=arguments[r]!=null?arguments[r]:{};r%2?_a(Object(n),!0).forEach(function(t){w(e,t,n[t])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):_a(Object(n)).forEach(function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))})}return e}function tr(){return tr=Object.assign||function(e){for(var r=1;r<arguments.length;r++){var n=arguments[r];for(var t in n)Object.prototype.hasOwnProperty.call(n,t)&&(e[t]=n[t])}return e},tr.apply(this,arguments)}function Qr(e){return Ls(e)||Ts(e)||Ds(e)||Rs()}function Rs(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function Ds(e,r){if(e){if(typeof e=="string")return Jr(e,r);var n=Object.prototype.toString.call(e).slice(8,-1);if(n==="Object"&&e.constructor&&(n=e.constructor.name),n==="Map"||n==="Set")return Array.from(e);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return Jr(e,r)}}function Ts(e){if(typeof Symbol<"u"&&Symbol.iterator in Object(e))return Array.from(e)}function Ls(e){if(Array.isArray(e))return Jr(e)}function Jr(e,r){(r==null||r>e.length)&&(r=e.length);for(var n=0,t=new Array(r);n<r;n++)t[n]=e[n];return t}function Ms(e,r){if(!(e instanceof r))throw new TypeError("Cannot call a class as a function")}function ba(e,r){for(var n=0;n<r.length;n++){var t=r[n];t.enumerable=t.enumerable||!1,t.configurable=!0,"value"in t&&(t.writable=!0),Object.defineProperty(e,t.key,t)}}function Is(e,r,n){return r&&ba(e.prototype,r),n&&ba(e,n),e}function As(e,r){if(typeof r!="function"&&r!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(r&&r.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),r&&en(e,r)}function en(e,r){return en=Object.setPrototypeOf||function(t,o){return t.__proto__=o,t},en(e,r)}function Cs(e){var r=Us();return function(){var t=rr(e),o;if(r){var a=rr(this).constructor;o=Reflect.construct(t,arguments,a)}else o=t.apply(this,arguments);return ks(this,o)}}function ks(e,r){return r&&(rt(r)==="object"||typeof r=="function")?r:I(e)}function I(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function Us(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],function(){})),!0}catch{return!1}}function rr(e){return rr=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},rr(e)}function w(e,r,n){return r in e?Object.defineProperty(e,r,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[r]=n,e}function wa(){if(typeof WeakMap!="function")return null;var e=new WeakMap;return wa=function(){return e},e}function Sa(e){if(e&&e.__esModule)return e;if(e===null||rt(e)!=="object"&&typeof e!="function")return{default:e};var r=wa();if(r&&r.has(e))return r.get(e);var n={},t=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var o in e)if(Object.prototype.hasOwnProperty.call(e,o)){var a=t?Object.getOwnPropertyDescriptor(e,o):null;a&&(a.get||a.set)?Object.defineProperty(n,o,a):n[o]=e[o]}return n.default=e,r&&r.set(e,n),n}var Ns=(0,be.lazy)(function(){return Promise.resolve().then(function(){return Sa(Pa())})}),qs=typeof self<"u"&&self.document,Vs=typeof self<"u"&&self.window&&self.window.document,Hs=Object.keys(tt.propTypes),xs=qs||Vs?be.Suspense:function(){return null},et=[],Ws=function(r,n){var t,o;return o=t=function(a){As(u,a);var i=Cs(u);function u(){var l;Ms(this,u);for(var s=arguments.length,c=new Array(s),f=0;f<s;f++)c[f]=arguments[f];return l=i.call.apply(i,[this].concat(c)),w(I(l),"state",{showPreview:!!l.props.light}),w(I(l),"references",{wrapper:function(h){l.wrapper=h},player:function(h){l.player=h}}),w(I(l),"handleClickPreview",function(p){l.setState({showPreview:!1}),l.props.onClickPreview(p)}),w(I(l),"showPreview",function(){l.setState({showPreview:!0})}),w(I(l),"getDuration",function(){return l.player?l.player.getDuration():null}),w(I(l),"getCurrentTime",function(){return l.player?l.player.getCurrentTime():null}),w(I(l),"getSecondsLoaded",function(){return l.player?l.player.getSecondsLoaded():null}),w(I(l),"getInternalPlayer",function(){var p=arguments.length>0&&arguments[0]!==void 0?arguments[0]:"player";return l.player?l.player.getInternalPlayer(p):null}),w(I(l),"seekTo",function(p,h){if(!l.player)return null;l.player.seekTo(p,h)}),w(I(l),"handleReady",function(){l.props.onReady(I(l))}),w(I(l),"getActivePlayer",(0,Zr.default)(function(p){for(var h=0,v=[].concat(et,Qr(r));h<v.length;h++){var _=v[h];if(_.canPlay(p))return _}return n||null})),w(I(l),"getConfig",(0,Zr.default)(function(p,h){var v=l.props.config;return ws.default.all([tt.defaultProps.config,tt.defaultProps.config[h]||{},v,v[h]||{}])})),w(I(l),"getAttributes",(0,Zr.default)(function(p){return(0,Ss.omit)(l.props,Hs)})),w(I(l),"renderActivePlayer",function(p){if(!p)return null;var h=l.getActivePlayer(p);if(!h)return null;var v=l.getConfig(p,h.key);return be.default.createElement(Es.default,tr({},l.props,{key:h.key,ref:l.references.player,config:v,activePlayer:h.lazyPlayer||h,onReady:l.handleReady}))}),l}return Is(u,[{key:"shouldComponentUpdate",value:function(s,c){return!(0,ga.default)(this.props,s)||!(0,ga.default)(this.state,c)}},{key:"componentDidUpdate",value:function(s){var c=this.props.light;!s.light&&c&&this.setState({showPreview:!0}),s.light&&!c&&this.setState({showPreview:!1})}},{key:"renderPreview",value:function(s){if(!s)return null;var c=this.props,f=c.light,p=c.playIcon,h=c.previewTabIndex;return be.default.createElement(Ns,{url:s,light:f,playIcon:p,previewTabIndex:h,onClick:this.handleClickPreview})}},{key:"render",value:function(){var s=this.props,c=s.url,f=s.style,p=s.width,h=s.height,v=s.fallback,_=s.wrapper,A=this.state.showPreview,k=this.getAttributes(c);return be.default.createElement(_,tr({ref:this.references.wrapper,style:Oa(Oa({},f),{},{width:p,height:h})},k),be.default.createElement(xs,{fallback:v},A?this.renderPreview(c):this.renderActivePlayer(c)))}}]),u}(be.Component),w(t,"displayName","ReactPlayer"),w(t,"propTypes",tt.propTypes),w(t,"defaultProps",tt.defaultProps),w(t,"addCustomPlayer",function(a){et.push(a)}),w(t,"removeCustomPlayers",function(){et.length=0}),w(t,"canPlay",function(a){for(var i=0,u=[].concat(et,Qr(r));i<u.length;i++){var l=u[i];if(l.canPlay(a))return!0}return!1}),w(t,"canEnablePIP",function(a){for(var i=0,u=[].concat(et,Qr(r));i<u.length;i++){var l=u[i];if(l.canEnablePIP&&l.canEnablePIP(a))return!0}return!1}),o};or.createReactPlayer=Ws});var Ra=O(ar=>{"use strict";y();d();Object.defineProperty(ar,"__esModule",{value:!0});ar.default=void 0;var tn=Bs(ia()),js=Ea();function Bs(e){return e&&e.__esModule?e:{default:e}}var Ks=tn.default[tn.default.length-1],$s=(0,js.createReactPlayer)(tn.default,Ks);ar.default=$s});y();d();var j=te(b());y();d();var z=te(b());y();d();var rn=te(hn()),P=te(b()),La=te(Ra());y();d();var Da=te(b());var Fs=re.div.attrs(e=>({style:{width:`${e.completed}%`}}))`
  height: ${e=>`${e.height}px`};
  background-color: ${e=>`${e.color}`};
  transition: ${e=>`width ${e.delay}ms linear`};
`,Ta=({completed:e=0,color:r="#AB9FF2",height:n=5,delay:t=200})=>Da.default.createElement(Fs,{completed:e,color:r,height:n,delay:t});var zs=an({minutes:1}),Ys=re.div`
  width: 100%;
  height: 100%;
  .MediaPlayerContainer__ReactPlayer {
    visibility: ${e=>e.type!=="video"&&"hidden"};
  }
  svg {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    fill: "#34333f";
    transition: opacity 0.2s ease-in;
  }
  .MediaPlayerContainer__Icon--show {
    opacity: 1;
  }
  .MediaPlayerContainer__Icon--hide {
    opacity: 0;
  }
  video {
    object-fit: contain;
  }
`,Xs=re.div`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
`,Gs={isReady:!1,isPlaying:!1,progress:0,duration:0},Zs=({src:e,type:r,disabled:n,onLoad:t=Se,onError:o=Se,isHidden:a=!1})=>{let i=(0,P.useRef)(null),[u,l]=(0,P.useState)(Gs),[s,c]=(0,P.useState)(void 0),f=s?s+100:1e3,[p,h]=(0,P.useState)(!1),[v,_]=(0,P.useState)(!1),[A,k]=(0,P.useState)(!0),[G,ee]=(0,P.useState)(!0),[V,Ie]=(0,P.useState)(!1),{isReady:nt,isPlaying:me,progress:Ae,duration:ur}=u,ot=A&&!G,at=ot&&!me,nn=ot&&me,on=(0,P.useCallback)(()=>{let E=me;v&&_(!1),l(Pe=>({...Pe,isPlaying:!E}))},[me,v]);(0,P.useEffect)(()=>{V&&Ae===0&&on()},[Ae]),(0,P.useEffect)(()=>{let E;return A||(E=setTimeout(()=>{l(Pe=>({...Pe,isPlaying:!1,progress:0})),k(!0)},s)),()=>{E&&clearTimeout(E)}},[A]),(0,P.useEffect)(()=>{let E=setTimeout(()=>{ee(!0)},2e3);return()=>{clearTimeout(E)}},[G]);let Na=()=>{!nt||!A||(V||Ie(!0),ee(!1),on())},qa=()=>l(E=>({...E,isPlaying:!1})),Va=()=>ee(!1),Ha=()=>{t(),l(E=>({...E,isReady:!0}))},xa=()=>{o(),h(!0)},Wa=E=>{(0,P.startTransition)(()=>{let Pe=Math.floor(E*1e3)<=zs?200:1e3;c(Pe),l(lr=>({...lr,duration:E}))})},ja=E=>{let Pe=E/ur*100;l(lr=>({...lr,progress:Pe}))},Ba=()=>{setTimeout(()=>{_(!0),k(!1)},f)};if(p)switch(r){case"video":return P.default.createElement(cn,null);case"audio":return P.default.createElement(sn,null)}return P.default.createElement(P.default.Fragment,null,P.default.createElement(Ys,{ref:i,className:"MediaPlayerContainer",type:r,hidden:n||a,onClick:Na,onMouseMove:Va},nt&&P.default.createElement(P.default.Fragment,null,P.default.createElement(fn,{className:(0,rn.default)({"MediaPlayerContainer__Icon--show":at,"MediaPlayerContainer__Icon--hide":!at})}),P.default.createElement(pn,{className:(0,rn.default)({"MediaPlayerContainer__Icon--show":nn,"MediaPlayerContainer__Icon--hide":!nn})}),P.default.createElement(Xs,{hidden:v},P.default.createElement(Ta,{completed:Ae,delay:s}))),P.default.createElement(La.default,{url:e,className:"MediaPlayerContainer__ReactPlayer",width:r==="video"?"100%":0,height:r==="video"?"100%":0,playing:me,progressInterval:s,onReady:Ha,onError:xa,onPause:qa,onDuration:E=>Wa(E),onProgress:E=>ja(E.playedSeconds),onEnded:Ba})))},ir=Zs;var Ma=z.default.memo(e=>{let{uri:r,previewImageUri:n}=e,[t,o]=(0,z.useState)(!0),[a,i]=(0,z.useState)(!1);return z.default.createElement(z.default.Fragment,null,a?z.default.createElement(ne,null,z.default.createElement(it,{type:"audio"})):z.default.createElement(ne,{previewImage:n},z.default.createElement(ir,{type:"audio",src:r,onLoad:()=>{o(!1),i(!1)},onError:()=>{o(!1),i(!0)},isHidden:t})),t?z.default.createElement(Ee,{showBadge:!1}):null)});y();d();var Y=te(b());y();d();var we=te(b());var Qs=re.div`
  visibility: ${e=>e.isHidden?"hidden":"visible"};
  model-viewer {
    --poster-color: transparent;
    --progress-bar-color: transparent;
    --progress-mask: transparent;
    width: ${e=>e.width}px;
    height: ${e=>e.height}px;
  }
`,Ia=!1;function Js(){Ia||(Ia=!0,import("./model-viewer-PTTUPFDS.js"))}var ec=({src:e,alt:r,autoRotate:n,autoPlay:t,cameraControls:o,loading:a,width:i=154,height:u=154,onLoad:l=Se,onError:s=Se,isHidden:c=!1})=>{Js();let f=(0,we.useRef)(null);return(0,we.useEffect)(()=>{let p=f.current;if(p)return p.addEventListener("load",l),p.addEventListener("error",s),()=>{p.removeEventListener("load",l),p.removeEventListener("error",s)}},[s,l,f]),we.default.createElement(Qs,{width:i,height:u,isHidden:c},we.default.createElement("model-viewer",{alt:r,loading:a??"eager","auto-rotate-delay":0,"auto-rotate":n||void 0,autoplay:t||void 0,"camera-controls":o||void 0,ref:f,src:e}))},Aa=ec;var Ca=Y.default.memo(e=>{let{uri:r,width:n,height:t,isCameraControlsEnabled:o}=e,[a,i]=(0,Y.useState)(!0),[u,l]=(0,Y.useState)(!1);return Y.default.createElement(Y.default.Fragment,null,u?Y.default.createElement(ne,null,Y.default.createElement(yn,null)):Y.default.createElement(ne,null,Y.default.createElement(Aa,{src:r,autoRotate:!0,autoPlay:!0,cameraControls:o,onLoad:()=>{i(!1),l(!1)},onError:()=>{i(!1),l(!0)},width:n,height:t,isHidden:a})),a?Y.default.createElement(Ee,{showBadge:!1}):null)});y();d();var X=te(b());var ka=X.default.memo(e=>{let{uri:r}=e,[n,t]=(0,X.useState)(!0),[o,a]=(0,X.useState)(!1);return X.default.createElement(X.default.Fragment,null,o?X.default.createElement(ne,null,X.default.createElement(it,{type:"video"})):X.default.createElement(ne,{isPurple:!0},X.default.createElement(ir,{type:"video",src:r,onLoad:()=>{t(!1),a(!1)},onError:()=>{t(!1),a(!0)},isHidden:n})),n?X.default.createElement(Ee,{showBadge:!1}):null)});var Ua=328,tc=re.div`
  width: ${e=>e.width}px;
  height: ${e=>e.height}px;
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  position: relative;
`,sp=j.default.memo(({media:e,collectibleChainData:r,width:n=328,height:t=328})=>{let o=e?.type??"image",a=sr(e,o,!0),i=sr(e,"image",!1,"large"),u=o==="image",l=o==="video",s=o==="audio",c=o==="model",f=o==="other",p=(0,j.useMemo)(()=>{if(a)return j.default.createElement(j.default.Fragment,null,u?j.default.createElement(cr,{width:Ua,height:Ua,uri:a,isZoomControlsEnabled:!0,showSkeletonBadge:!1}):l?j.default.createElement(ka,{uri:a}):s?j.default.createElement(Ma,{uri:a,previewImageUri:i}):c?j.default.createElement(Ca,{uri:a,width:n,height:t,isCameraControlsEnabled:!0}):f?j.default.createElement(cr,{uri:i??"",width:n,height:t}):null);if(un(r))return j.default.createElement(dn,{...r.utxoDetails})},[r,t,s,u,c,f,l,a,i,n]);return j.default.createElement(tc,{width:n,height:t},p)});export{sp as a};
/*! Bundled license information:

classnames/index.js:
  (*!
    Copyright (c) 2018 Jed Watson.
    Licensed under the MIT License (MIT), see
    http://jedwatson.github.io/classnames
  *)
*/
//# sourceMappingURL=chunk-LUKK5BMR.js.map
