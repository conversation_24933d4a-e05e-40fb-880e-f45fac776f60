import { Page, expect, Locator } from '@playwright/test';
import { BasePage } from '../base/pages/BasePage';
import { StocksSelectors } from '../selectors/StocksSelectors';
import UIActions from '../base/actions/UIActions';

/**
 * StocksPage - Page Object Model cho trang Stocks của CashDrop
 * Kế thừa từ BasePage để sử dụng các chức năng chung
 * Triển khai theo mô hình POM với các thành phần:
 * - Navigation và filtering
 * - Danh sách stocks và tương tác
 * - Chi tiết stock và trading
 * - Watchlist management
 * - Market data display
 */
export class StocksPage extends BasePage {
  private readonly STOCKS_URL = 'https://dev.cashdrop.click/stocks';

  constructor(page: Page) {
    super(page);
  }

  /**
   * Đi<PERSON>u hướng đến trang Stocks
   */
  async navigateToStocks(): Promise<void> {
    this.logAction('Navigating to Stocks page');
    await this.navigateTo(this.STOCKS_URL);
    await this.waitForPageLoad();
    await this.verifyStocksPageLoaded();
  }

  /**
   * <PERSON><PERSON><PERSON> minh trang Stocks đã được load thành công
   */
  async verifyStocksPageLoaded(): Promise<void> {
    this.logAction('Verifying Stocks page is loaded');
    
    // Kiểm tra URL
    expect(this.getCurrentUrl()).toContain('stocks');
    
    // Kiểm tra container chính
    await this.verifyElementVisible(
      StocksSelectors.pageContainer,
      'Stocks page container'
    );
    
    // Kiểm tra title hoặc header
    const headerElement = this.page.locator(StocksSelectors.headerTitle).first();
    if (await headerElement.count() > 0) {
      await expect(headerElement).toBeVisible();
      console.log('✓ Stocks page header is visible');
    }
  }

  /**
   * Kiểm tra trang có đang loading không
   */
  async isPageLoading(): Promise<boolean> {
    const loadingElement = this.page.locator(StocksSelectors.loadingSpinner);
    return await loadingElement.isVisible();
  }

  /**
   * Đợi cho đến khi trang load xong
   */
  async waitForPageLoadComplete(timeout: number = 30000): Promise<void> {
    this.logAction('Waiting for page load to complete');
    
    // Đợi loading spinner biến mất nếu có
    const loadingElement = this.page.locator(StocksSelectors.loadingSpinner);
    if (await loadingElement.count() > 0) {
      await loadingElement.waitFor({ state: 'hidden', timeout });
    }
    
    // Đợi stocks list xuất hiện
    await this.page.locator(StocksSelectors.stocksList).waitFor({ 
      state: 'visible', 
      timeout 
    });
  }

  // ==================== NAVIGATION METHODS ====================

  /**
   * Click vào tab All Stocks
   */
  async clickAllStocksTab(): Promise<void> {
    this.logAction('Clicking All Stocks tab');
    await this.clickElement(StocksSelectors.allStocksTab, 'All Stocks tab');
    await this.waitForPageLoadComplete();
  }

  /**
   * Click vào tab Trending
   */
  async clickTrendingTab(): Promise<void> {
    this.logAction('Clicking Trending tab');
    await this.clickElement(StocksSelectors.trendingTab, 'Trending tab');
    await this.waitForPageLoadComplete();
  }

  /**
   * Click vào tab Gainers
   */
  async clickGainersTab(): Promise<void> {
    this.logAction('Clicking Gainers tab');
    await this.clickElement(StocksSelectors.gainersTab, 'Gainers tab');
    await this.waitForPageLoadComplete();
  }

  /**
   * Click vào tab Losers
   */
  async clickLosersTab(): Promise<void> {
    this.logAction('Clicking Losers tab');
    await this.clickElement(StocksSelectors.losersTab, 'Losers tab');
    await this.waitForPageLoadComplete();
  }

  /**
   * Kiểm tra navigation tabs có hiển thị không
   */
  async verifyNavigationTabsVisible(): Promise<boolean> {
    this.logAction('Verifying navigation tabs are visible');
    
    const tabs = [
      { selector: StocksSelectors.allStocksTab, name: 'All Stocks' },
      { selector: StocksSelectors.trendingTab, name: 'Trending' },
      { selector: StocksSelectors.gainersTab, name: 'Gainers' },
      { selector: StocksSelectors.losersTab, name: 'Losers' }
    ];

    for (const tab of tabs) {
      const element = this.page.locator(tab.selector).first();
      if (await element.count() > 0) {
        const isVisible = await element.isVisible();
        console.log(`${tab.name} tab visible: ${isVisible}`);
        if (!isVisible) return false;
      }
    }
    
    return true;
  }

  // ==================== SEARCH METHODS ====================

  /**
   * Tìm kiếm stock theo từ khóa
   */
  async searchStock(searchTerm: string): Promise<void> {
    this.logAction(`Searching for stock: ${searchTerm}`);
    
    // Nhập từ khóa vào search input
    await this.fillInput(StocksSelectors.searchInput, searchTerm, 'Search input');
    
    // Click search button nếu có
    const searchButton = this.page.locator(StocksSelectors.searchButton);
    if (await searchButton.count() > 0) {
      await this.clickElement(StocksSelectors.searchButton, 'Search button');
    } else {
      // Nhấn Enter nếu không có search button
      await this.page.locator(StocksSelectors.searchInput).press('Enter');
    }
    
    await this.waitForPageLoadComplete();
  }

  /**
   * Xóa search input
   */
  async clearSearch(): Promise<void> {
    this.logAction('Clearing search input');
    await this.page.locator(StocksSelectors.searchInput).fill('');
    await this.page.locator(StocksSelectors.searchInput).press('Enter');
    await this.waitForPageLoadComplete();
  }

  // ==================== STOCK LIST METHODS ====================

  /**
   * Lấy danh sách tất cả stocks hiển thị trên trang
   */
  async getDisplayedStocks(): Promise<any[]> {
    this.logAction('Getting displayed stocks');
    
    await this.waitForPageLoadComplete();
    const stockItems = this.page.locator(StocksSelectors.stockItem);
    const count = await stockItems.count();
    
    const stocks = [];
    for (let i = 0; i < count; i++) {
      const item = stockItems.nth(i);
      
      const stock = {
        symbol: await this.getTextContent(item.locator(StocksSelectors.stockSymbol.split(', ')[0]).first()),
        name: await this.getTextContent(item.locator(StocksSelectors.stockName.split(', ')[0]).first()),
        price: await this.getTextContent(item.locator(StocksSelectors.stockPrice.split(', ')[0]).first()),
        change: await this.getTextContent(item.locator(StocksSelectors.stockChange.split(', ')[0]).first()),
        percentChange: await this.getTextContent(item.locator(StocksSelectors.stockPercentChange.split(', ')[0]).first()),
        volume: await this.getTextContent(item.locator(StocksSelectors.stockVolume.split(', ')[0]).first())
      };
      
      stocks.push(stock);
    }
    
    console.log(`Found ${stocks.length} stocks on page`);
    return stocks;
  }

  /**
   * Click vào một stock cụ thể theo symbol
   */
  async clickStockBySymbol(symbol: string): Promise<void> {
    this.logAction(`Clicking stock: ${symbol}`);
    
    const stockSelector = StocksSelectors.stockBySymbol(symbol);
    await this.clickElement(stockSelector, `Stock ${symbol}`);
    
    // Đợi modal hoặc detail page load
    await this.page.waitForTimeout(2000);
  }

  /**
   * Kiểm tra stock có hiển thị trong danh sách không
   */
  async isStockDisplayed(symbol: string): Promise<boolean> {
    this.logAction(`Checking if stock ${symbol} is displayed`);
    
    const stockSelector = StocksSelectors.stockBySymbol(symbol);
    const element = this.page.locator(stockSelector);
    return await element.count() > 0 && await element.first().isVisible();
  }

  /**
   * Lấy thông tin giá của một stock
   */
  async getStockPrice(symbol: string): Promise<string> {
    this.logAction(`Getting price for stock: ${symbol}`);
    
    const priceSelector = StocksSelectors.stockPriceBySymbol(symbol);
    return await this.getTextContent(this.page.locator(priceSelector).first());
  }

  /**
   * Lấy thông tin thay đổi giá của một stock
   */
  async getStockChange(symbol: string): Promise<string> {
    this.logAction(`Getting price change for stock: ${symbol}`);
    
    const changeSelector = StocksSelectors.stockChangeBySymbol(symbol);
    return await this.getTextContent(this.page.locator(changeSelector).first());
  }

  // ==================== STOCK DETAIL METHODS ====================

  /**
   * Kiểm tra modal chi tiết stock có mở không
   */
  async isStockDetailModalOpen(): Promise<boolean> {
    const modal = this.page.locator(StocksSelectors.stockDetailModal);
    return await modal.count() > 0 && await modal.first().isVisible();
  }

  /**
   * Đóng modal chi tiết stock
   */
  async closeStockDetailModal(): Promise<void> {
    this.logAction('Closing stock detail modal');
    
    // Thử các cách đóng modal khác nhau
    const closeButton = this.page.locator('button:has-text("Close"), button:has-text("×"), .close-button');
    if (await closeButton.count() > 0) {
      await closeButton.first().click();
    } else {
      await this.page.keyboard.press('Escape');
    }
    
    await this.page.waitForTimeout(1000);
  }

  /**
   * Click nút Buy trong stock detail
   */
  async clickBuyButton(): Promise<void> {
    this.logAction('Clicking Buy button');
    await this.clickElement(StocksSelectors.buyButton, 'Buy button');
    await this.page.waitForTimeout(2000);
  }

  /**
   * Click nút Sell trong stock detail
   */
  async clickSellButton(): Promise<void> {
    this.logAction('Clicking Sell button');
    await this.clickElement(StocksSelectors.sellButton, 'Sell button');
    await this.page.waitForTimeout(2000);
  }

  /**
   * Thêm stock vào watchlist
   */
  async addToWatchlist(): Promise<void> {
    this.logAction('Adding stock to watchlist');
    await this.clickElement(StocksSelectors.addToWatchlistButton, 'Add to Watchlist button');
    await this.page.waitForTimeout(2000);
  }

  // ==================== WATCHLIST METHODS ====================

  /**
   * Kiểm tra watchlist section có hiển thị không
   */
  async isWatchlistVisible(): Promise<boolean> {
    const watchlist = this.page.locator(StocksSelectors.watchlistSection);
    return await watchlist.count() > 0 && await watchlist.first().isVisible();
  }

  /**
   * Lấy số lượng items trong watchlist
   */
  async getWatchlistItemsCount(): Promise<number> {
    this.logAction('Getting watchlist items count');
    
    const items = this.page.locator(StocksSelectors.watchlistItem);
    return await items.count();
  }

  /**
   * Xóa stock khỏi watchlist
   */
  async removeFromWatchlist(symbol: string): Promise<void> {
    this.logAction(`Removing ${symbol} from watchlist`);
    
    // Tìm item trong watchlist theo symbol
    const watchlistItems = this.page.locator(StocksSelectors.watchlistItem);
    const count = await watchlistItems.count();
    
    for (let i = 0; i < count; i++) {
      const item = watchlistItems.nth(i);
      const itemText = await item.textContent();
      
      if (itemText && itemText.includes(symbol)) {
        const removeButton = item.locator(StocksSelectors.removeFromWatchlistButton);
        if (await removeButton.count() > 0) {
          await removeButton.click();
          break;
        }
      }
    }
    
    await this.page.waitForTimeout(2000);
  }

  // ==================== MARKET DATA METHODS ====================

  /**
   * Kiểm tra market summary có hiển thị không
   */
  async isMarketSummaryVisible(): Promise<boolean> {
    const summary = this.page.locator(StocksSelectors.marketSummary);
    return await summary.count() > 0 && await summary.first().isVisible();
  }

  /**
   * Lấy thông tin market indices
   */
  async getMarketIndices(): Promise<any[]> {
    this.logAction('Getting market indices');
    
    const indices = this.page.locator(StocksSelectors.marketIndex);
    const count = await indices.count();
    
    const marketData = [];
    for (let i = 0; i < count; i++) {
      const index = indices.nth(i);
      const name = await this.getTextContent(index.locator('.name, .index-name').first());
      const value = await this.getTextContent(index.locator(StocksSelectors.marketValue.split(', ')[0]).first());
      
      marketData.push({ name, value });
    }
    
    return marketData;
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Refresh trang stocks
   */
  async refreshPage(): Promise<void> {
    this.logAction('Refreshing stocks page');
    
    const refreshButton = this.page.locator(StocksSelectors.refreshButton);
    if (await refreshButton.count() > 0) {
      await refreshButton.click();
    } else {
      await this.page.reload();
    }
    
    await this.waitForPageLoadComplete();
  }

  /**
   * Chụp ảnh màn hình trang stocks
   */
  async takeStocksScreenshot(name: string = 'stocks-page'): Promise<string> {
    this.logAction(`Taking screenshot: ${name}`);
    return await this.takeScreenshot(name, true);
  }

  /**
   * Kiểm tra trang có lỗi không
   */
  async hasErrorMessage(): Promise<boolean> {
    const errorElement = this.page.locator(StocksSelectors.errorMessage);
    return await errorElement.count() > 0 && await errorElement.first().isVisible();
  }

  /**
   * Lấy text của error message nếu có
   */
  async getErrorMessage(): Promise<string> {
    this.logAction('Getting error message');
    
    const errorElement = this.page.locator(StocksSelectors.errorMessage);
    if (await errorElement.count() > 0) {
      return await this.getTextContent(errorElement.first());
    }
    
    return '';
  }

  // ==================== VALIDATION METHODS ====================

  /**
   * Xác minh trang stocks hoạt động đúng
   */
  async verifyStocksPageFunctionality(): Promise<boolean> {
    this.logAction('Verifying stocks page functionality');
    
    try {
      // Kiểm tra page load
      await this.verifyStocksPageLoaded();
      
      // Kiểm tra navigation
      const hasNavigation = await this.verifyNavigationTabsVisible();
      
      // Kiểm tra stocks list
      const stocks = await this.getDisplayedStocks();
      const hasStocks = stocks.length > 0;
      
      console.log(`Navigation visible: ${hasNavigation}`);
      console.log(`Stocks count: ${stocks.length}`);
      
      return hasNavigation && hasStocks;
    } catch (error) {
      console.error('Error verifying stocks page functionality:', error);
      return false;
    }
  }

  /**
   * Get text content safely
   */
  private async getTextContent(locator: Locator): Promise<string> {
    try {
      if (await locator.count() > 0) {
        const text = await locator.first().textContent();
        return text?.trim() || '';
      }
      return '';
    } catch {
      return '';
    }
  }
}
