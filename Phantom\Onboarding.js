import{a as he,b as ko,d as Ao,e as So,f as Co,g as To}from"./chunk-GOKFGGQI.js";import{a as uo}from"./chunk-HUU4WO6F.js";import{a as Me}from"./chunk-P2CLTFKC.js";import"./chunk-XYFNIIUY.js";import{a as yo}from"./chunk-5RLYH252.js";import{a as bo}from"./chunk-2JNGRO7L.js";import{a as Ie,c as ao,d as so,e as lo,f as co}from"./chunk-WE6RAXEH.js";import{a as xo}from"./chunk-VHCQKD7Y.js";import"./chunk-24U56MUI.js";import"./chunk-RLZITNCL.js";import{a as io}from"./chunk-7A6HLO4U.js";import{a as Qt}from"./chunk-T27XGMXK.js";import"./chunk-AUOG6CT3.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{Ca as ro,Da as no,O as to,Q as D,a as Bt,d as Dt,e as pe,f as Lt,g as Nt,n as Wt,o as Q0,p as Gt,ua as Ee}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import{d as ge,e as fe}from"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as S0}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{b as I0}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as u0}from"./chunk-CCQRCL2K.js";import{e as Rt,f as eo}from"./chunk-75L54KUM.js";import{a as oo}from"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import{a as N0}from"./chunk-YF76YZSL.js";import"./chunk-XJZOYN2T.js";import"./chunk-ZON27MKP.js";import{a as mo,b as po,c as ho}from"./chunk-JLLUQF3V.js";import{a as Ze,b as go}from"./chunk-QSVSNR6K.js";import{a as Ht,c as zt,h as _t,m as Y0}from"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import{b as lt}from"./chunk-MTQZ2G7K.js";import{h as ht,i as yt}from"./chunk-W27Z2YZM.js";import{$ as Mt,I as me,U as Ft,Z as Et,_ as It,a as h0,b as E0,c as H0,d as c,e as p,f as wt,h as se,n as Z,q as le,r as ce}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import{a as $0}from"./chunk-4VDZJDFB.js";import{a as fo}from"./chunk-26OZJBRY.js";import"./chunk-XJTFMD4C.js";import{d as Xt}from"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import{a as Jt}from"./chunk-VQVTLSDS.js";import{a as z0,b as ue,h as qt,j as xe}from"./chunk-OKP6DFCI.js";import{T as jt,b as de,c as Zt,h as Ot,l as Vt,o as x,rb as _,s as Kt,sa as Ut,ta as $t,wa as Yt}from"./chunk-WIQ4WVKX.js";import{a as ar,c as vt}from"./chunk-AVT3M45V.js";import{Oa as kt,Pa as At,Qa as ie,Sa as St,Ta as Ct,Ua as Tt,ia as bt,jb as ae,kb as Pt}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{a as xt,c as v,d as ft}from"./chunk-MHOQBMVI.js";import{a as gt}from"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{Ab as te,Ad as re,Bd as st,Dc as U0,Ha as Qe,Ic as nt,Kd as ne,Nd as ct,Oc as Fe,Sd as mt,Ud as dt,ee as pt,kc as Re,kd as it,lc as et,qc as tt,rd as at,tb as Je,wc as L0,xe as ut,yc as ot,zc as rt,zd as oe}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as b}from"./chunk-56SJOU6P.js";import{d as qe,g as Xe}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{C as je,Qa as $e,Ya as ee,ab as Ye,qa as Ue,ta as D0,x as Ke}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as P}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as S,h as n,n as i}from"./chunk-3KENBVE7.js";n();i();var R=S(P());var rr=S(ar());n();i();var W=S(P());var Po=({children:e})=>{let{search:t}=de(),[o,r]=(0,W.useState)(null),a=new URLSearchParams(t).get("restore"),s=new URLSearchParams(t).get("append"),[d,m]=(0,W.useState)(null),[u,y]=(0,W.useState)([]),[l,g]=(0,W.useState)(null),[f,T]=(0,W.useState)(null),[h,F]=(0,W.useState)(null),B=(0,W.useCallback)(async t0=>{if(!t0)m(null),F(null);else{let o0=null;try{o0=await t0.getMnemonic();let V0=$e.from(o0);m(V0),F(t0)}finally{o0?.fill(0)}}},[]),U=(0,W.useMemo)(()=>a?"restore":s?"append":"initial",[s,a]),[H,Q]=(0,W.useState)(U),[L,z]=(0,W.useState)([]),[s0,N]=(0,W.useState)({}),e0=Rt(L,(t0,o0)=>t0?.length===o0.length)??[],M=qe(L),G=kt(),{mutateAsync:l0}=At(),w0=ie(),{mutateAsync:F0}=St(),K0=(0,W.useCallback)(t0=>{z(o0=>Fe(o0,V0=>{V0.push(t0)}))},[]),f0=(0,W.useCallback)(()=>{z(t0=>Fe(t0,o0=>{o0.pop()}))},[]);return(0,W.useEffect)(()=>{let t0=!0;return(async()=>{let o0=await ht();t0&&r(!o0)})(),()=>{t0=!1}},[r]),(0,W.useEffect)(()=>{G===void 0||w0===void 0||(G?(v.capture("onboardingOpen"),l0(!1)):w0.isResettingApp&&w0.isFirstTimeResettingApp&&(v.capture("reonboardingOpen"),F0(!1)))},[G,l0,w0,F0]),W.default.createElement(vo.Provider,{value:{secureMnemonic:d,mnemonic:h,setMnemonic:B,privateKey:l,setPrivateKey:g,ledgerAccounts:f,setLedgerAccounts:T,accounts:u,setAccounts:y,onboardingType:H,setOnboardingType:Q,onboardingStack:L,setOnboardingStack:z,prevOnboardingStack:e0,currentStep:M,pushStep:K0,popStep:f0,analyticsChainInfo:s0,setAnalyticsChainInfo:N,wantsAllSitePermissions:o}},e)},vo=(0,W.createContext)(null),A=()=>{let e=(0,W.useContext)(vo);if(!e)throw new Error("Missing onboarding context");return e};n();i();var b0=S(P());n();i();var w=S(P());n();i();var $=S(P());n();i();var V=S(P());n();i();var r0=S(P());n();i();var G0=S(P());n();i();var J=S(P());var x0=J.default.memo(({onSubmit:e,title:t,subtitle:o,SubtitleComponent:r,buttonTheme:a,buttonText:s,buttonDisabled:d,buttonLoading:m,hasScroll:u,hideButtons:y,children:l,layout:g,extraButtons:f})=>{let T=B=>{B.preventDefault(),e()},h=u?ur:J.default.Fragment,F=u?sr:J.default.Fragment;return J.default.createElement(lr,{onSubmit:T,"data-testid":"onboarding-form"},J.default.createElement(F,null,J.default.createElement(Oe,{children:l,layout:g,subtitle:o,SubtitleComponent:r,title:t}),y?null:J.default.createElement(h,null,f,J.default.createElement(xe,{"data-testid":"onboarding-form-submit-button",type:"submit",theme:a,disabled:d||m,loading:m,height:"47px"},s))))});x0.defaultProps={buttonTheme:"default",hideButtons:!1,layout:"default",SubtitleComponent:void 0};var Oe=({children:e,layout:t,subtitle:o,SubtitleComponent:r,title:a})=>J.default.createElement(pr,{layout:t},J.default.createElement(cr,null,J.default.createElement(mr,null,a),r?J.default.createElement(r,{children:o}):J.default.createElement(dr,{children:o})),e),sr=({children:e})=>J.default.createElement(xr,null,J.default.createElement(gr,null,e)),lr=x.form`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
`,cr=x(u0).attrs({align:"center"})`
  margin: 30px 0 20px;
`,mr=x(_).attrs({color:"#FFFFFF",weight:500,size:28,lineHeight:34,textAlign:"center",margin:"0 0 10px 0"})``,dr=x(_).attrs(e=>({size:18,lineHeight:25,color:e.color??"#999"}))`
  max-width: 352px;
`,pr=x(u0).attrs(e=>({direction:e.layout==="reverse"?"column-reverse":"column",align:"center",justify:"center",flex:1}))``,ur=x.div`
  background: #222222;
  box-shadow: 0px -4px 6px rgba(0, 0, 0, 0.2);
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  padding: 18px 20px 20px;
`,xr=x(S0).attrs({justify:"center"})`
  flex-flow: column;
  max-height: 426px;
`,gr=x(u0)`
  overflow-y: scroll;
  padding-bottom: 40px;
`;n();i();var C0=S(P());var Fo=()=>C0.default.createElement(fr,{animate:{opacity:1},initial:{opacity:0},transition:{delay:.4}},C0.default.createElement(hr,null,C0.default.createElement(u0,{justify:"center"},C0.default.createElement(_,{size:16,weight:600},"Pin the Phantom extension"),C0.default.createElement(_,{size:16,weight:400},"Click"," ",C0.default.createElement(wo,{top:"1px"},C0.default.createElement(Ut,{width:16,height:16}))," ","and then"," ",C0.default.createElement(wo,{top:"3px"},C0.default.createElement($t,{width:9.49,height:16}))," ","and voil\xE0!")))),fr=x(z0.div)`
  position: fixed;
  top: 16px;
  right: 16px;
`,hr=x(S0)`
  background-color: #ab9ff2;
  height: 74px;
  border-radius: 8px;
  padding: 15px;

  * {
    color: #222;
    fill: currentColor;
  }
`,wo=x.span`
  display: inline;
  position: relative;
  top: ${e=>e.top};
  margin-left: 2px;
  margin-right: 2px;
`;var k0=()=>{let{t:e}=b(),{mutate:t}=bt();return(0,G0.useEffect)(()=>{t()},[t]),G0.default.createElement(G0.default.Fragment,null,G0.default.createElement(x0,{onSubmit:()=>{self.close()},title:e("onboardingFinished"),subtitle:e("onboardingDoneDescription"),buttonTheme:"primary",buttonText:e("onboardingDoneGetStarted"),layout:"reverse"},G0.default.createElement(Mt,null)),G0.default.createElement(Fo,null))};var Eo=()=>{let{t:e}=b(),t=nt(),{pushStep:o}=A(),{data:r}=ne(),{data:a=[]}=U0();return r0.default.createElement(c,{flex:1},r0.default.createElement(c,{marginY:28,gap:8},r0.default.createElement(p,{align:"center",font:"heading3Semibold",children:e("seedlessAccountsImportedPrimaryText")}),r0.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:e("seedlessAccountsImportedSecondaryText")})),r0.default.createElement(c,{gap:24},t&&r0.default.createElement(p,{font:"bodySemibold",color:"white",children:`@${t}`}),r0.default.createElement(Ft,{className:D.table,rows:a.map((s,d)=>{let m=io(s.icon??""),u=s.icon?m?{type:"emoji",unicode:s.icon}:{type:"image",imageUrl:s.icon}:{type:"default"},y=r?.find(l=>l.identifier===s.accountHash)?.isReadOnly;return{topLeft:s.name,bottomLeft:y?e("seedlessPreviouslyImportedTag"):null,start:y?r0.default.createElement(c,{position:"relative"},r0.default.createElement(Ee,{size:"medium",accountIcon:u,accountName:s.name??"",accountIndex:d}),r0.default.createElement(c,{position:"absolute",bottom:0,right:0},r0.default.createElement(le,{icon:"Eye",shape:"circle",size:16,borderColor:"bgWallet"}))):r0.default.createElement(Ee,{size:"medium",accountIcon:u,accountName:s.name??"",accountIndex:d})}})})),r0.default.createElement(Z,{className:D.buttonWrapper,theme:"primary",onClick:()=>o(r0.default.createElement(k0,null))},e("seedlessContinueText")))};n();i();n();i();var Io=".phantom-labs.internal.multichainEnabledOverride";var yr=new xt,Mo=async()=>{await yr.set(Io,{version:2,forceMultichainEnabled:!0,multiChainGTMEnabled:!0}),await v.capture("migrationMultichainSetGTMOverride")};n();i();var c0=S(P());n();i();var T0={stepWrapper:"_51gazn1bq _51gazn18w _51gazn1c3 _51gazn1ar _51gazn1b8",content:"_51gazn129 _51gazn2",headerText:"yp0no33 _51gazn332 _51gazn17 _51gazn2 _51gazne5",subTitleText:"yp0no35 _51gazn33h",buttonGroup:"_51gazn18w _51gazn129 _51gazngj",button:"yp0no37",image:"_51gazn129",imageWrapper:"yp0no3a _51gazn129"};var q0=()=>{let{t:e}=b(),t=L0(),{pushStep:o,onboardingType:r}=A();(0,c0.useEffect)(()=>{t.capture("allSitesPermissionsSeen",{data:{uiContext:"onboarding",onboardingType:r}})},[t,r]);let a=async()=>{try{let d=await yt();t.capture("allSitesPermissions",{data:{uiContext:"onboarding",oldValue:!1,newValue:d,choice:"commandAllow"}})}catch(d){console.error("Failed to set permissions"),console.error(d)}o(c0.default.createElement(k0,null))},s=async()=>{t.capture("allSitesPermissions",{data:{uiContext:"onboarding",oldValue:!1,newValue:!1,choice:"commandDontAllow"}}),o(c0.default.createElement(k0,null))};return c0.default.createElement("div",{className:T0.stepWrapper},c0.default.createElement("div",{className:T0.imageWrapper},c0.default.createElement("img",{className:T0.image,src:"/images/additional-permissions/onboarding.webp",alt:"Arrangement of floating icons"})),c0.default.createElement("div",{className:T0.content},c0.default.createElement("div",{className:T0.headerText},e("onboardingAdditionalPermissionsTitle")),c0.default.createElement("div",{className:T0.subTitleText},e("onboardingAdditionalPermissionsSubtitle"))),c0.default.createElement("div",{className:T0.buttonGroup},c0.default.createElement(Z,{type:"button",onClick:s,className:T0.button},e("commandDontAllow")),c0.default.createElement(Z,{type:"button",onClick:a,theme:"primary",className:T0.button},e("commandAllow"))))};n();i();var j=S(P());n();i();var Y=S(P());var ye=({blur:e,mnemonicIndexes:t,readOnly:o,wordlist:r,onChange:a,onFocus:s})=>{let d=(0,Y.useRef)([]);(0,Y.useEffect)(()=>{d.current=d.current.slice(0,t.length)},[t]);let m=(0,Y.useRef)(!1),u=(0,Y.useCallback)((l,g)=>{let f=g+1,T=f===t.length;(l.key===" "||l.key==="Enter"&&!T)&&(l.preventDefault(),f<t.length&&d?.current[f]?.focus()),l.key!==" "&&l.key!=="Enter"&&!m.current&&(v.capture("onboardingImportSeedStartType"),m.current=!0)},[t.length,d]),y=(0,Y.useCallback)((l,g)=>{l.preventDefault(),l.clipboardData.getData("text").replace(/[\r\n]+/gm,"").split(" ").forEach((F,B)=>{let U=g+B;if(U<t.length){let H=d?.current[U];H&&(H.value=F,a?.(F,U))}})},[t.length,a]);return Y.default.createElement(Ar,{blur:e},t.map((l,g)=>Y.default.createElement(br,{key:`mnemonic-input-${g}`,index:g,mnemonicIndex:l,readOnly:o,wordlist:r,onChange:a,onFocus:s,onPaste:y,onKeyPress:u,ref:f=>d.current[g]=f})))},br=(0,Y.forwardRef)(({index:e,mnemonicIndex:t,readOnly:o,wordlist:r,onChange:a,onFocus:s,onPaste:d,onKeyPress:m},u)=>{let[y,l]=(0,Y.useState)(!1),[g,f]=(0,Y.useState)(!1),T=o&&r[t]?{value:r[t]}:{};return Y.default.createElement(kr,{key:`word-container-${e}`,readOnly:o,invalid:y&&g&&t===-1},Y.default.createElement(Sr,null,`${e+1}.`),Y.default.createElement(Cr,{"data-testid":`secret-recovery-phrase-word-input-${e}`,...T,readOnly:o,autoComplete:"off",autoCorrect:"off",spellCheck:!1,pattern:"[A-Za-z\\s]+",onPaste:h=>d(h,e),onFocus:()=>s?.(e,g),onBlur:()=>{s?.(-1,g),l(!0)},onChange:h=>{a?.(h.target.value,e),f(h.target.value!=="")},onKeyPress:h=>m(h,e),ref:u}))}),kr=x(S0).attrs({align:"center",justify:"center"})`
  color: #fff;
  background: #181818;
  border: ${({invalid:e})=>e?"1px solid #eb3742":"1px solid #2f2f2f"};
  border-radius: 6px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
  align-self: center;
  height: 41px;
  &:focus-within {
    border: ${({readOnly:e})=>e?"auto":"1px solid #AB9FF2"};
  }
  transition: all 0.1s linear;
`,Ar=x.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  row-gap: 10px;
  column-gap: 10px;
  width: 100%;
  filter: ${({blur:e})=>e?"blur(4px)":"blur(0px)"};
  transition: all 0.1s linear;
`,Sr=x(_).attrs({size:14,color:"#999999"})`
  padding-left: 10px;
  padding-right: 5px;
`,Cr=x.input`
  color: #fff;
  background: none;
  border: none;
  width: 100%;
  font-size: 15px;
  font-weight: 500;
  cursor: ${({readOnly:e})=>e?"default":"text"};
  &:focus {
    outline: 0;
  }
`;var Zo=({initialIndexes:e=[],initialWordList:t=[]})=>{let{t:o}=b(),{mnemonic:r,pushStep:a,wantsAllSitePermissions:s}=A(),[{indexes:d,wordlist:m},u]=(0,j.useState)({indexes:e,wordlist:t});(0,j.useEffect)(()=>{r&&N0().then(async Q=>{let{wordlist:L}=Q,z=Array.from(await r.getIndexes());u({indexes:z,wordlist:L})}).catch(console.error)},[r]);let[y,l]=(0,j.useState)(!0),[g,f]=(0,j.useState)(!1),h=(0,j.useRef)(null).current,F=()=>{l(!0),h?.setSelectionRange(0,0),h?.blur()},B=()=>{y&&(l(!1),h?.focus(),h?.setSelectionRange(0,0))},U=()=>{f(!g)};return j.default.createElement(x0,{onSubmit:async()=>{v.capture("onboardingCreateNewWallet"),a(s?j.default.createElement(q0,null):j.default.createElement(k0,null))},title:o("onboardingCreateRecoveryPhraseSecretRecoveryPhrase"),subtitle:o("onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder"),SubtitleComponent:Fr,buttonTheme:g?"primary":"default",buttonText:o("commandContinue"),buttonDisabled:!g,extraButtons:[j.default.createElement(wr,{key:"onboarding-form-saved-secret-recovery-phrase-checkbox"},j.default.createElement(fe,{"data-testid":"onboarding-form-saved-secret-recovery-phrase-checkbox",checked:g,onChange:U}),j.default.createElement(_,{size:16,lineHeight:19,color:"#999"},j.default.createElement($0,{i18nKey:"onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase"})))]},j.default.createElement(Tr,{onMouseEnter:B,onMouseLeave:F},j.default.createElement(Pr,{onClick:B},y&&j.default.createElement(vr,null,j.default.createElement(jt,null)),j.default.createElement(ye,{readOnly:!0,blur:y,mnemonicIndexes:d,wordlist:m}))))},Tr=x(u0).attrs({justify:"space-between"})`
  position: relative;
  text-align: center;
  margin-top: -10px;
  margin-bottom: 10px;
  padding: 10px 0;
`,Pr=x(u0)`
  align-items: center;
  display: flex;
  position: relative;
`,vr=x(S0).attrs({justify:"center"})`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
`,wr=x(ge)`
  margin-bottom: 16px;
`,Fr=x(_).attrs({size:16,lineHeight:22,color:"#FFDC62"})`
  max-width: 343px;
`;var Er={password:"",confirmPassword:""},m0=()=>{let{t:e}=b(),{handleImportSeed:t}=Ie("seed"),{handleImportSeed:o}=Ie("seedless"),r=re(),{mutateAsync:a}=oe(),{mutate:s}=dt(),{mutateAsync:d}=mt(),{data:m=[]}=U0(),{mnemonic:u,setMnemonic:y,privateKey:l,ledgerAccounts:g,onboardingType:f,pushStep:T,accounts:h,analyticsChainInfo:F,wantsAllSitePermissions:B}=A(),U=ie(),H=f==="seedless",Q=f==="create",L=f==="importSeed",z=f==="importPrivateKey",s0=f==="connectHardware",N=f==="restore",{data:e0}=Tt(),{mutateAsync:M}=Ct(),G=(0,V.useMemo)(()=>e0??!1,[e0]),l0=()=>{v.capture("onboardingTermsClick"),self.open(je,"_blank")},w0=()=>{M(!G)},[F0,K0]=(0,V.useState)(Er),{password:f0,confirmPassword:t0}=F0,[o0,V0]=(0,V.useState)(void 0),R0=(0,V.useMemo)(()=>{let p0=f0.length;switch(!0){case(p0>0&&p0<8):return e("onboardingCreatePasswordPasswordStrengthWeak");case(p0>=8&&p0<21):return e("onboardingCreatePasswordPasswordStrengthMedium");case p0>=21:return e("onboardingCreatePasswordPasswordStrengthStrong");default:return}},[f0.length,e]),nr=(0,V.useMemo)(()=>{switch(R0){case e("onboardingCreatePasswordPasswordStrengthWeak"):return"#EB3742";case e("onboardingCreatePasswordPasswordStrengthMedium"):return"#FFDC62";case e("onboardingCreatePasswordPasswordStrengthStrong"):return"#21E56F";default:return"#777777"}},[R0,e]),Ve=!N&&!G||!f0||!t0,[ir,O0]=(0,V.useState)(!1),He=p0=>{let{name:j0,value:B0}=p0.currentTarget;o0&&V0(void 0),K0({...F0,[j0]:B0})},X0=async()=>{try{if(!await to(f0))throw new Error("Failed to set password");if(v.capture("onboardingCreatePassword"),Q||L||z||H){let j0=U?.isResettingApp?"reonboardingDone":"onboardingDone",B0={onboardingType:f,...L&&F};v.capture(j0,{data:B0})}return!0}catch{return V0(e("onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase")),!1}};return V.default.createElement(x0,{onSubmit:async()=>{O0(!0);let p0=ro(f0),j0=no(f0,t0);if(p0||j0)return V0(p0??j0),O0(!1);if(H){if(!u||h.length===0)throw new Error("Invalid mnemonic");if(!await X0())return O0(!1);await o(u,h,e("onboardingImportAccountsAccountName",{walletIndex:h[0].derivationIndex+1}))}else if(Q){let ze=(await N0()).fromSentenceLength(12);if(await y(ze),!await X0())return O0(!1);let _e=0;await t(ze,[{derivationIndex:_e,derivationPathTypes:te(r)}],e("onboardingImportAccountsAccountName",{walletIndex:_e+1})),await a({addressTypes:["bip122_p2wpkh"]})}else if(z){if(!l?.privateKey)throw new Error("Invalid private key");if(!await X0())return O0(!1);s(l),Qe(l.chainType)&&await a({addressTypes:[l.chainType]})}else if(s0){if(!g)throw new Error("Invalid Ledger accounts");if(!await X0())return O0(!1);await d(g)}else{if(!u||h.length===0)throw new Error("Invalid mnemonic");if(!await X0())return O0(!1);await t(u,h,e("onboardingImportAccountsAccountName",{walletIndex:h[0].derivationIndex+1})),N&&v.capture("restoreWalletCreatePassword")}H||await Y0.logOut(),v.capture("onboardingTermsAgreedByUser"),await Mo(),await ae.invalidateQueries({refetchType:"active"}),O0(!1),H&&v.capture("onboardingSeedlessDone"),T(H&&m.length>1?V.default.createElement(Eo,null):Q?V.default.createElement(Zo,null):B?V.default.createElement(q0,null):V.default.createElement(k0,null))},title:e("onboardingCreatePassword"),subtitle:e("onboardingCreatePasswordDescription"),buttonTheme:Ve?"default":"primary",buttonText:e("commandContinue"),buttonDisabled:Ve,buttonLoading:ir,extraButtons:N?void 0:[V.default.createElement(Or,{key:"onboarding-form-terms-of-service-checkbox"},V.default.createElement(fe,{"data-testid":"onboarding-form-terms-of-service-checkbox",checked:G,onChange:w0}),V.default.createElement(Br,null,V.default.createElement($0,{i18nKey:"onboardingCreatePasswordAgreeToTermsOfServiceInterpolated"},"I agree to the ",V.default.createElement("label",{onClick:l0},"Terms of Service"))))]},V.default.createElement(Ir,{needsExtraMargin:!!o0},V.default.createElement(I0,{"data-testid":"onboarding-form-password-input",type:"password",name:"password",placeholder:e("onboardingCreatePasswordPasswordPlaceholder"),onChange:He}),V.default.createElement(Mr,null,V.default.createElement(I0.WithWarning,{"data-testid":"onboarding-form-confirm-password-input",type:"password",name:"confirmPassword",placeholder:e("onboardingCreatePasswordConfirmPasswordPlaceholder"),onChange:He,warningMessage:o0}),!!R0&&V.default.createElement(Zr,{color:nr},R0))))},Ir=x(u0)`
  margin-bottom: ${e=>e.needsExtraMargin?37:17}px;
  input[name="password"] {
    margin-bottom: 10px;
  }
`,Mr=x.div`
  position: relative;
  width: 100%;
`,Zr=x(_).attrs({size:14,lineHeight:19,margin:"0",weight:600})`
  position: absolute;
  top: 15px;
  right: 14px;
`,Or=x(ge)`
  margin-bottom: 16px;
`,Br=x(_).attrs({size:16,lineHeight:19,color:"#999"})``;n();i();var d0=S(P());var Oo={v:"4.8.0",meta:{g:"LottieFiles AE 3.4.5",a:"",k:"",d:"",tc:""},fr:60,ip:0,op:169,w:300,h:300,nm:"Ico-ImportWallet-Rebrand-2",ddd:0,assets:[{id:"comp_0",layers:[{ddd:0,ind:1,ty:0,nm:"burst-green-01",refId:"comp_1",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:45,ix:10},p:{a:0,k:[181.211,129.711,0],ix:2},a:{a:0,k:[18.5,75,0],ix:1},s:{a:0,k:[40,40,100],ix:6}},ao:0,w:37,h:150,ip:5,op:33,st:5,bm:0},{ddd:0,ind:2,ty:4,nm:"check",parent:5,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:5,s:[91]},{t:25,s:[0]}],ix:10},p:{a:0,k:[-1.804,2.431,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.12,y:1},o:{x:.001,y:.88},t:5,s:[{i:[[3.277,-3.251],[0,0],[0,0],[3.095,-3.251],[0,0],[-3.094,-3.07],[0,0],[-3.277,3.251],[0,0],[3.277,3.251],[0,0]],o:[[0,0],[0,0],[-3.277,-3.251],[0,0],[-3.094,3.251],[0,0],[3.277,3.251],[0,0],[3.277,-3.07],[0,0],[-3.277,-3.251]],v:[[14.745,-17.571],[-16.181,12.789],[-43.121,-13.94],[-54.771,-13.94],[-64.237,-4.729],[-64.237,6.829],[-22.006,48.729],[-10.174,48.729],[35.861,3.199],[35.861,-8.36],[26.577,-17.571]],c:!0}]},{t:25,s:[{i:[[3.277,-3.251],[0,0],[0,0],[3.095,-3.251],[0,0],[-3.094,-3.07],[0,0],[-3.277,3.251],[0,0],[3.277,3.251],[0,0]],o:[[0,0],[0,0],[-3.277,-3.251],[0,0],[-3.094,3.251],[0,0],[3.277,3.251],[0,0],[3.277,-3.07],[0,0],[-3.277,-3.251]],v:[[55.904,-58.729],[-16.181,12.789],[-43.121,-13.94],[-54.771,-13.94],[-64.237,-4.729],[-64.237,6.829],[-22.006,48.729],[-10.174,48.729],[77.02,-37.96],[77.02,-49.519],[67.736,-58.729]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.129411756992,.898039221764,.436862826347,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[43.733,43.733],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Vector",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:5,op:61,st:-19,bm:0},{ddd:0,ind:3,ty:4,nm:"loader",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[150.182,138.496,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[90,90,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.12,.12],y:[1,1]},o:{x:[.001,.001],y:[.88,.88]},t:-40,s:[4,4]},{i:{x:[.833,.833],y:[1,1]},o:{x:[.167,.167],y:[0,0]},t:0,s:[48,48]},{t:4,s:[35.5,35.5]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:1,k:[{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:0,s:[25.31]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:4,s:[39.91]},{i:{x:[.8],y:[.5]},o:{x:[.7],y:[0]},t:28,s:[25.31]},{t:58,s:[74.876]}],ix:1},e:{a:1,k:[{i:{x:[.676],y:[.815]},o:{x:[.348],y:[.188]},t:0,s:[24.979]},{i:{x:[.659],y:[.793]},o:{x:[.331],y:[.217]},t:2,s:[40.449]},{i:{x:[.382],y:[1]},o:{x:[.169],y:[.86]},t:4,s:[53.971]},{t:30,s:[75.021]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:4,s:[32.2]},{t:60,s:[483]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[.116485595703,.808227539063,.391937255859,1],ix:3},o:{a:0,k:100,ix:4},w:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:1,s:[5]},{t:4,s:[10]}],ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:0,op:5,st:0,bm:0},{ddd:0,ind:4,ty:4,nm:"bg - outer",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:2,s:[10]},{t:22,s:[0]}],ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:150,ix:3},y:{a:0,k:150,ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.166,.166],y:[1,1]},o:{x:[.099,.099],y:[.892,.892]},t:0,s:[25,25]},{t:32,s:[141,141]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.129411756992,.898039221764,.436862826347,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:23,st:0,bm:0},{ddd:0,ind:5,ty:4,nm:"bg",sr:1,ks:{o:{a:0,k:10,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:150,ix:3},y:{a:0,k:150,ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.487,.487,.833],y:[1,1,1]},o:{x:[.3,.3,.167],y:[.356,.356,0]},t:0,s:[100,100,100]},{i:{x:[.309,.309,.833],y:[1,1,1]},o:{x:[.498,.498,.167],y:[0,0,0]},t:2,s:[128.634,128.634,100]},{i:{x:[.55,.55,.833],y:[1,1,1]},o:{x:[.386,.386,.167],y:[-.01,-.01,0]},t:8,s:[93.131,93.131,100]},{i:{x:[.373,.373,.833],y:[1,1,1]},o:{x:[.18,.181,0],y:[0,0,0]},t:18,s:[104.691,104.691,100]},{i:{x:[.442,.442,.833],y:[1,1,1]},o:{x:[.146,.146,0],y:[0,0,0]},t:37,s:[98.292,98.292,100]},{t:60,s:[100,100,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[82,82],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.129411756992,.898039221764,.436862826347,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:61,st:0,bm:0}]},{id:"comp_1",layers:[{ddd:0,ind:1,ty:4,nm:"dot",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:18.5,ix:3},y:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:0,s:[102.5]},{t:20,s:[40.5]}],ix:4}},a:{a:0,k:[32.5,-192.5,0],ix:1},s:{a:1,k:[{i:{x:[.8,.8,.8],y:[.5,.5,1]},o:{x:[.7,.7,.7],y:[0,0,0]},t:10,s:[100,100,100]},{t:20,s:[0,0,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[37,37],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.12939453125,.898010253906,.436859130859,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[32.5,-192.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:28,st:-150,bm:0}]}],layers:[{ddd:0,ind:1,ty:0,nm:"Ico-Success-01",refId:"comp_0",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:150,ix:3},y:{a:0,k:150,ix:4}},a:{a:0,k:[150,150,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:300,h:300,ip:108,op:169,st:108,bm:0},{ddd:0,ind:2,ty:4,nm:"loader",parent:3,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:28,s:[0]},{t:107,s:[720]}],ix:10},p:{a:0,k:[0,0,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[48,48],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.167],y:[0]},t:28,s:[40.002]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:40,s:[40.002]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:61,s:[58.002]},{i:{x:[.682],y:[.993]},o:{x:[.349],y:[0]},t:106,s:[34.002]},{t:107,s:[41.082]}],ix:1},e:{a:1,k:[{i:{x:[.52],y:[.54]},o:{x:[.3],y:[.28]},t:11,s:[42.744]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:28,s:[68.744]},{t:107,s:[68.744]}],ix:2},o:{a:1,k:[{i:{x:[.52],y:[.54]},o:{x:[.3],y:[.28]},t:11,s:[-476.332]},{i:{x:[.615],y:[1]},o:{x:[.285],y:[1.02]},t:28,s:[-356.332]},{i:{x:[.833],y:[1]},o:{x:[0],y:[0]},t:32,s:[-350.783]},{i:{x:[.427],y:[1]},o:{x:[.661],y:[0]},t:42,s:[-350.798]},{i:{x:[.667],y:[1]},o:{x:[0],y:[0]},t:68,s:[-165.534]},{i:{x:[.587],y:[.799]},o:{x:[.707],y:[0]},t:86,s:[-165.551]},{t:107,s:[20.461]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:3,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:11,op:108,st:3,bm:0},{ddd:0,ind:3,ty:4,nm:"ring",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[150.223,150.225,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.18,.18,.18],y:[1,1,1]},o:{x:[.09,.09,.09],y:[.47,.47,0]},t:0,s:[30.8,30.8,100]},{t:28,s:[122.8,122.8,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[48,48],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"st",c:{a:0,k:[.235294133425,.192156881094,.35686275363,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:3,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:0,op:108,st:3,bm:0},{ddd:0,ind:4,ty:3,nm:"MASTER 1",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[150,150,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:1,k:[{i:{x:[.654,.654,.667],y:[1.004,1.004,1]},o:{x:[.314,.314,.333],y:[0,0,0]},t:-10,s:[100,100,100]},{i:{x:[.057,.057,.667],y:[1,1,1]},o:{x:[.09,.09,.09],y:[.3,.3,0]},t:0,s:[20.513,20.513,100]},{i:{x:[.667,.667,.667],y:[1,1,1]},o:{x:[.167,.167,.167],y:[0,0,0]},t:17,s:[102.173,102.173,100]},{i:{x:[.776,.776,.667],y:[1,1,1]},o:{x:[.568,.568,.333],y:[0,0,0]},t:21,s:[100,100,100]},{i:{x:[.582,.582,.667],y:[1,1,1]},o:{x:[.553,.553,.333],y:[0,0,0]},t:28,s:[103.331,103.331,100]},{i:{x:[.057,.057,.667],y:[1,1,1]},o:{x:[.136,.136,.333],y:[0,0,0]},t:32,s:[95,95,100]},{i:{x:[.667,.667,.667],y:[1,1,1]},o:{x:[.167,.167,.167],y:[0,0,0]},t:50,s:[100,100,100]},{i:{x:[.667,.667,.667],y:[1,1,1]},o:{x:[.568,.568,.333],y:[0,0,0]},t:53,s:[100,100,100]},{i:{x:[.667,.667,.667],y:[1,1,1]},o:{x:[.604,.604,.333],y:[0,0,0]},t:60,s:[110,110,100]},{i:{x:[.057,.057,.667],y:[1,1,1]},o:{x:[.136,.136,.333],y:[0,0,0]},t:67,s:[95,95,100]},{i:{x:[.667,.667,.667],y:[1,1,1]},o:{x:[.167,.167,.167],y:[0,0,0]},t:86,s:[100,100,100]},{i:{x:[.667,.667,.667],y:[1,1,1]},o:{x:[.568,.568,.333],y:[0,0,0]},t:96,s:[100,100,100]},{i:{x:[.776,.776,.667],y:[.482,.482,1]},o:{x:[.489,.489,.333],y:[0,0,0]},t:103,s:[110,110,100]},{i:{x:[.582,.582,.667],y:[1,1,1]},o:{x:[.347,.347,.333],y:[.481,.481,0]},t:107,s:[103.331,103.331,100]},{i:{x:[.057,.057,.667],y:[1,1,1]},o:{x:[.136,.136,.333],y:[0,0,0]},t:110,s:[95,95,100]},{t:129,s:[100,100,100]}],ix:6}},ao:0,ip:0,op:108,st:-6,bm:0},{ddd:0,ind:5,ty:4,nm:"$-2",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.715],y:[1]},o:{x:[.479],y:[.489]},t:100,s:[12.508]},{i:{x:[.374],y:[1]},o:{x:[.36],y:[0]},t:104,s:[54.968]},{t:108,s:[48.483]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[90.733,90.733,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,-3.743],[0,-3.777],[2.79,0],[2.11,1.395],[0,0],[-3.403,-.238],[0,0],[0,0],[0,0],[0,3.607],[0,3.641],[-2.824,0],[-2.28,-1.225],[0,0],[2.518,.204]],o:[[0,0],[0,0],[-5.513,.681],[0,8.848],[0,1.157],[-2.756,0],[0,0],[1.974,1.361],[0,0],[0,0],[0,0],[5.309,-.783],[0,-8.779],[0,-1.191],[2.076,0],[0,0],[-1.906,-1.123],[0,0]],v:[[2.442,-20.687],[-1.641,-20.687],[-1.641,-16.944],[-9.843,-9.355],[3.701,.513],[-.179,2.453],[-8.039,.104],[-10.251,5.073],[-1.641,7.659],[-1.641,11.3],[2.442,11.3],[2.442,7.524],[10.337,.003],[-3.173,-9.695],[.673,-11.839],[7.274,-10.002],[9.35,-15.004],[2.442,-17.011]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.235294133425,.192156881094,.35686275363,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,-4.471],ix:2},a:{a:0,k:[0,-4.471],ix:1},s:{a:0,k:[89.687,89.687],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:102,op:108,st:30,bm:0},{ddd:0,ind:6,ty:4,nm:"dot-5",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.321],y:[.823]},t:95,s:[-63]},{i:{x:[.087],y:[1.201]},o:{x:[.136],y:[0]},t:99,s:[65.863]},{t:108,s:[49.508]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:1,k:[{i:{x:[.737,.737,.583],y:[.475,.475,1]},o:{x:[.438,.438,.162],y:[0,0,0]},t:105,s:[100,100,100]},{t:108,s:[95.79,95.79,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[1,1,1,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:96,s:[-45]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:100,s:[0]},{i:{x:[.667],y:[.429]},o:{x:[.333],y:[0]},t:106,s:[0]},{t:108,s:[-11.667]}],ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:97,op:108,st:36,bm:0},{ddd:0,ind:7,ty:4,nm:"grad-5",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:0,k:49.508,ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[325,325,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[.670588254929,.623529434204,.949019670486,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-45,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:98,op:108,st:-8,bm:0},{ddd:0,ind:8,ty:4,nm:"$-3",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.411],y:[1]},o:{x:[.203],y:[.295]},t:70,s:[48.598]},{i:{x:[.667],y:[1]},o:{x:[.307],y:[0]},t:76,s:[49.508]},{i:{x:[.715],y:[1]},o:{x:[.547],y:[0]},t:79,s:[49.508]},{i:{x:[.783],y:[.44]},o:{x:[.534],y:[0]},t:86,s:[47.118]},{t:95,s:[119.198]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[90.733,90.733,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,-3.743],[0,-3.777],[2.79,0],[2.11,1.395],[0,0],[-3.403,-.238],[0,0],[0,0],[0,0],[0,3.607],[0,3.641],[-2.824,0],[-2.28,-1.225],[0,0],[2.518,.204]],o:[[0,0],[0,0],[-5.513,.681],[0,8.848],[0,1.157],[-2.756,0],[0,0],[1.974,1.361],[0,0],[0,0],[0,0],[5.309,-.783],[0,-8.779],[0,-1.191],[2.076,0],[0,0],[-1.906,-1.123],[0,0]],v:[[2.442,-20.687],[-1.641,-20.687],[-1.641,-16.944],[-9.843,-9.355],[3.701,.513],[-.179,2.453],[-8.039,.104],[-10.251,5.073],[-1.641,7.659],[-1.641,11.3],[2.442,11.3],[2.442,7.524],[10.337,.003],[-3.173,-9.695],[.673,-11.839],[7.274,-10.002],[9.35,-15.004],[2.442,-17.011]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.235294133425,.192156881094,.35686275363,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,-4.471],ix:2},a:{a:0,k:[0,-4.471],ix:1},s:{a:0,k:[89.687,89.687],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:70,op:94,st:-9,bm:0},{ddd:0,ind:9,ty:4,nm:"$-4",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.715],y:[1]},o:{x:[.479],y:[.489]},t:62,s:[12.508]},{i:{x:[.374],y:[1]},o:{x:[.36],y:[0]},t:66,s:[54.968]},{t:70,s:[48.483]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[90.733,90.733,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,-3.743],[0,-3.777],[2.79,0],[2.11,1.395],[0,0],[-3.403,-.238],[0,0],[0,0],[0,0],[0,3.607],[0,3.641],[-2.824,0],[-2.28,-1.225],[0,0],[2.518,.204]],o:[[0,0],[0,0],[-5.513,.681],[0,8.848],[0,1.157],[-2.756,0],[0,0],[1.974,1.361],[0,0],[0,0],[0,0],[5.309,-.783],[0,-8.779],[0,-1.191],[2.076,0],[0,0],[-1.906,-1.123],[0,0]],v:[[2.442,-20.687],[-1.641,-20.687],[-1.641,-16.944],[-9.843,-9.355],[3.701,.513],[-.179,2.453],[-8.039,.104],[-10.251,5.073],[-1.641,7.659],[-1.641,11.3],[2.442,11.3],[2.442,7.524],[10.337,.003],[-3.173,-9.695],[.673,-11.839],[7.274,-10.002],[9.35,-15.004],[2.442,-17.011]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.235294133425,.192156881094,.35686275363,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,-4.471],ix:2},a:{a:0,k:[0,-4.471],ix:1},s:{a:0,k:[89.687,89.687],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:64,op:70,st:-8,bm:0},{ddd:0,ind:11,ty:4,nm:"$-1",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.715],y:[1]},o:{x:[.479],y:[.489]},t:13,s:[12.508]},{i:{x:[.374],y:[1]},o:{x:[.36],y:[0]},t:17,s:[54.968]},{i:{x:[.679],y:[-3.289]},o:{x:[.246],y:[0]},t:21,s:[48.483]},{i:{x:[.411],y:[1]},o:{x:[.203],y:[.295]},t:28,s:[48.598]},{i:{x:[.667],y:[1]},o:{x:[.307],y:[0]},t:34,s:[49.508]},{i:{x:[.715],y:[1]},o:{x:[.547],y:[0]},t:37,s:[49.508]},{i:{x:[.783],y:[.44]},o:{x:[.534],y:[0]},t:44,s:[47.118]},{t:53,s:[119.198]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[90.733,90.733,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,0],[0,0],[0,0],[0,-3.743],[0,-3.777],[2.79,0],[2.11,1.395],[0,0],[-3.403,-.238],[0,0],[0,0],[0,0],[0,3.607],[0,3.641],[-2.824,0],[-2.28,-1.225],[0,0],[2.518,.204]],o:[[0,0],[0,0],[-5.513,.681],[0,8.848],[0,1.157],[-2.756,0],[0,0],[1.974,1.361],[0,0],[0,0],[0,0],[5.309,-.783],[0,-8.779],[0,-1.191],[2.076,0],[0,0],[-1.906,-1.123],[0,0]],v:[[2.442,-20.687],[-1.641,-20.687],[-1.641,-16.944],[-9.843,-9.355],[3.701,.513],[-.179,2.453],[-8.039,.104],[-10.251,5.073],[-1.641,7.659],[-1.641,11.3],[2.442,11.3],[2.442,7.524],[10.337,.003],[-3.173,-9.695],[.673,-11.839],[7.274,-10.002],[9.35,-15.004],[2.442,-17.011]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.235294133425,.192156881094,.35686275363,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,-4.471],ix:2},a:{a:0,k:[0,-4.471],ix:1},s:{a:0,k:[89.687,89.687],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:13,op:52,st:-51,bm:0},{ddd:0,ind:12,ty:4,nm:"dot-3",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.321],y:[.823]},t:55,s:[-63]},{i:{x:[.087],y:[1.201]},o:{x:[.136],y:[0]},t:59,s:[65.863]},{t:68,s:[49.508]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:1,k:[{i:{x:[.633,.633,.88],y:[1,1,1]},o:{x:[.49,.49,.167],y:[0,0,0]},t:65,s:[100,100,100]},{i:{x:[.88,.88,.88],y:[.438,.438,1]},o:{x:[.87,.87,.87],y:[0,0,0]},t:71,s:[90,90,100]},{t:98,s:[325,325,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-45,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:57,op:96,st:-4,bm:0},{ddd:0,ind:13,ty:4,nm:"grad-2",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:0,k:49.508,ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:1,k:[{i:{x:[.594,.594,.877],y:[1,1,1]},o:{x:[.325,.325,.44],y:[.472,.472,0]},t:28,s:[95.79,95.79,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.732,.732,.732],y:[0,0,0]},t:31,s:[90,90,100]},{t:57,s:[331.6,331.6,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[.670588254929,.623529434204,.949019670486,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-45,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:54,op:98,st:-44,bm:0},{ddd:0,ind:14,ty:4,nm:"grad-6",parent:4,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:0,k:49.508,ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[325,325,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[.670588254929,.623529434204,.949019670486,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-45,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:54,st:-90,bm:0},{ddd:0,ind:15,ty:4,nm:"dot-1",parent:4,tt:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.321],y:[.823]},t:10,s:[-63]},{i:{x:[.087],y:[1.201]},o:{x:[.136],y:[0]},t:14,s:[65.863]},{t:23,s:[49.508]}],ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:1,k:[{i:{x:[.607,.607,.863],y:[1,1,1]},o:{x:[.3,.3,.466],y:[.563,.563,0]},t:28,s:[92.91,92.91,100]},{i:{x:[.88,.88,.88],y:[.479,.479,1]},o:{x:[.87,.87,.87],y:[0,0,0]},t:30,s:[90,90,100]},{t:55,s:[325,325,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[.5]},t:28,s:[-22.5]},{t:31,s:[-45]}],ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:12,op:54,st:-45,bm:0},{ddd:0,ind:16,ty:4,nm:"grad-1",parent:4,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:0,k:49.508,ix:4}},a:{a:0,k:[.5,-4.5,0],ix:1},s:{a:0,k:[325,325,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"rc",d:1,s:{a:0,k:[50,50],ix:2},p:{a:0,k:[0,0],ix:3},r:{a:0,k:25,ix:4},nm:"Rectangle Path 1",mn:"ADBE Vector Shape - Rect",hd:!1},{ty:"fl",c:{a:0,k:[.670588254929,.623529434204,.949019670486,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[.5,-4.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-45,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:54,st:-90,bm:0}],markers:[{tm:28,cm:"",dr:0},{tm:108,cm:"",dr:0}]};var Lr=[{segment:[0,27],loop:!1,autoAdvance:!0},{segment:[28,107],loop:!0,autoAdvance:!1},{identifier:"success",segment:[108,168],loop:!1,autoAdvance:!0}],Bo=d0.default.memo(({findMoreAccountsButtonText:e,isFetchingMoreAccounts:t,renderedPages:o,selectedAccounts:r,findMoreAccounts:a,onSelect:s,onSelectAll:d})=>{let m=Object.values(r).length,y=Object.values(r).filter(l=>l)?.length??0;return d0.default.createElement(d0.default.Fragment,null,d0.default.createElement(Co,{totalAccounts:m,selectedAccounts:y,onPress:d}),o.map((l,g)=>{let{accounts:f}=l,T=!!r[g];return d0.default.createElement(So,{key:f[0].address,accountType:"seed",accounts:f,accountIndex:g,checked:T,onPress:()=>s(g)})}),d0.default.createElement(Wr,null,t?d0.default.createElement(qt,{diameter:24}):d0.default.createElement(_,{color:"#AB9FF2",size:16,lineHeight:24,weight:600,onClick:a},e)))}),Do=d0.default.memo(({status:e})=>{let t=d0.default.useRef(null);return d0.default.useEffect(()=>{e==="success"&&t.current?.setStage("success")},[e]),d0.default.createElement(Nr,null,d0.default.createElement(xo,{stages:Lr,stageRef:t,path:Oo,autoplay:!1,loop:!1}))}),Nr=x(u0).attrs({align:"center",justify:"center"})`
  width: 150px;
  height: 150px;
  margin-bottom: -36px;
`,Wr=x.div`
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 40px;
`;var Lo=()=>{let{t:e}=b(),{mnemonic:t,pushStep:o,setAccounts:r,setAnalyticsChainInfo:a,onboardingType:s,wantsAllSitePermissions:d}=A(),[m,u]=(0,$.useState)(new Uint8Array),y=re(),l=st(),{mutateAsync:g}=oe(),{data:f}=ne(),{mutateAsync:T}=pt(),{mutateAsync:h}=ct(),F=(0,$.useCallback)(async(L,z,s0)=>{let N=e("onboardingImportAccountsAccountName",{walletIndex:z[0].derivationIndex+1}),e0=await Ht(L,z,N),M=f?.length??0,G={};if(e0.forEach((l0,w0)=>{let F0=e("onboardingImportAccountsAccountName",{walletIndex:w0+1+M});G[l0.identifier]={name:F0}}),await h({metadataBatch:G}),e0.length===0)throw new Error("Failed to set se*d phrase");await T({identifier:e0[0].identifier}),await Ze.set(Me,!await Ze.get(Me)),v.capture("multiMnemonicDone",{data:{numOfAccounts:e0.length,...s0}})},[h,f?.length,T,e]),B=(0,$.useCallback)(async L=>{let z=new Set;for(let s0 of L)for(let N of s0.accounts)(!("amount"in N)||parseFloat(N.amount)!==0)&&z.add(N.chainType);await g({addressTypes:Array.from(z)})},[g]),U=(0,$.useCallback)(L=>{a(L),s==="append"?o(d?$.default.createElement(q0,null):$.default.createElement(k0,null)):o($.default.createElement(m0,null))},[s,a,o,d]),H=(0,$.useCallback)(async(L,z)=>{if(t&&L.length>0){let s0=L.map((N,e0)=>({name:e("onboardingImportAccountsAccountName",{walletIndex:e0+1}),derivationIndex:N.seedIndex,derivationPathTypes:N.accounts.map(M=>M.derivationPathType)}));r(s0),s==="append"&&await F(t,s0,z),await B(L)}},[B,F,t,s,r,e]);(0,$.useEffect)(()=>{t&&t.deriveSeed().then(u).catch(console.error)},[t]),(0,$.useEffect)(()=>{s==="append"?v.capture("multiMnemonicImportAccounts"):v.capture("onboardingImportAccounts")},[s]);let Q=lt({seed:m,enabledAddressTypes:y,enabledChains:l,navigationCallback:U,storageCallback:H});return $.default.createElement(Gr,{...Q})},Gr=$.default.memo(({expanded:e,findMoreAccountsButtonText:t,isFetchingMoreAccounts:o,isImportButtonDisabled:r,isImporting:a,renderedPages:s,selectedAccounts:d,status:m,subtitle:u,findMoreAccounts:y,onExpand:l,onImport:g,onSelect:f,onSelectAll:T})=>{let{t:h}=b(),F=m==="success"&&!e?[$.default.createElement(Vr,{key:"alt","data-testid":"onboarding-form-secondary-button",type:"button",theme:"default",disabled:r,onClick:l},h("onboardingImportViewAccounts"))]:void 0;return $.default.createElement(x0,{buttonDisabled:r,buttonLoading:a,buttonText:h("commandContinue"),buttonTheme:r?"default":"primary",hasScroll:e&&s.length>1,hideButtons:m==="loading",layout:e?"default":"reverse",onSubmit:g,subtitle:u,title:h("onboardingImportAccounts"),extraButtons:F},e?$.default.createElement(Bo,{findMoreAccountsButtonText:t,isFetchingMoreAccounts:o,renderedPages:s,selectedAccounts:d,findMoreAccounts:y,onSelect:f,onSelectAll:T}):$.default.createElement(Do,{status:m}))}),Vr=x(xe)`
  margin-bottom: 10px;
`;var J0=()=>{let{t:e}=b(),[t,o]=(0,w.useState)(null),[r,a]=(0,w.useState)([]);(0,w.useEffect)(()=>{N0().then(M=>{let{wordlist:G}=M;a(G),o(()=>M)}).catch(M=>D0.captureError(M,"account"))},[]),(0,w.useEffect)(()=>{v.capture("onboardingImportSeedStart")},[]);let{onboardingType:s,pushStep:d,setMnemonic:m}=A(),u=s==="restore",y=s==="append",[l,g]=(0,w.useState)(()=>new Array(12).fill(-1)),[f,T]=(0,w.useState)([]),[h,F]=(0,w.useState)(null),[B,U]=(0,w.useState)(-1),H=l.every(M=>M!==-1),Q=l.length,L=(0,w.useMemo)(()=>l.reduce((M,G,l0)=>(G===-1&&f.includes(l0)&&B!==l0&&M.push(l0),M),[]),[l,f,B]),z=(0,w.useCallback)((M,G)=>{U(G?-1:M)},[U]),s0=(0,w.useCallback)((M,G)=>{h&&F(null);let l0=M.replaceAll(/\s+/g," ").toLowerCase().trim();T(l0?[...f.filter(f0=>f0!==G),G]:[...f.filter(f0=>f0!==G)]);let F0=(t?.wordlist??[]).indexOf(l0),K0=l;K0[G]=F0,g(K0)},[t?.wordlist,h,l,f,T]),N=(0,w.useCallback)(()=>{y||(u?v.capture("restoreWalletRestore"):v.capture("onboardingImportWallet"))},[u,y]),e0=(0,w.useCallback)(async()=>{try{if(!t)throw new Error("Bip39 class is not configured");let M=Uint32Array.from(l),G=await t.fromIndexes(M);if(await zt(G)){F(e("onboardingErrorDuplicateSecretRecoveryPhrase"));return}await m(G),N(),d(w.default.createElement(Lo,null))}catch(M){F(e("onboardingImportWalletErrorInvalidSecretRecoveryPhrase")),D0.captureMessage(`Encountered error when restoring or importing wallet: ${M}`,"account")}},[t,l,d,m,e,N]);return w.default.createElement(x0,{onSubmit:e0,title:e("onboardingImportWalletSecretRecoveryPhrase"),subtitle:e("onboardingImportWalletImportExistingWallet"),buttonTheme:H?"primary":"default",buttonText:e(u?"onboardingImportWalletRestoreWallet":"onboardingImportWallet"),buttonDisabled:!H},w.default.createElement(ye,{mnemonicIndexes:l,wordlist:r,onChange:s0,onFocus:z}),w.default.createElement(zr,null,w.default.createElement(Hr,{indexes:L}),h&&w.default.createElement(Be,{"data-testid":"onboarding-import-secret-recovery-phrase-error-message"},h),!h&&w.default.createElement(_r,null,w.default.createElement(Kr,{onClick:()=>g(l.length===12?l.concat(new Array(12).fill(-1)):l.slice(0,12))},e("onboardingImportWalletIHaveWords",{numWords:Q===12?24:12})))))},Hr=({indexes:e})=>{let{t,i18n:o}=b(),r=(0,w.useMemo)(()=>{let m=e.map(l=>(l+1).toString()),y=new Intl.ListFormat(o.resolvedLanguage,{style:"long",type:"conjunction"}).format(m);return e.length===1?t("onboardingImportWalletIncorrectOrMisspelledWord",{wordIndex:y}):t("onboardingImportWalletIncorrectOrMisspelledWords",{wordIndexes:y})},[e,o,t]),[a,{height:s}]=eo(),d=(0,w.useMemo)(()=>({initial:{opacity:0,height:0},animate:{opacity:1,height:s},exit:{opacity:0,height:0},transition:{delay:.2,duration:.2}}),[s]);return w.default.createElement(ue,{mode:"wait"},e.length>0&&w.default.createElement(z0.div,{...d},w.default.createElement(jr,{ref:a},w.default.createElement(Be,null,w.default.createElement(Yt,{width:12,height:12}),r))))},zr=x.div`
  padding: 0 5px;
  margin: 10px 0 20px;
  width: 100%;
`,_r=x(S0).attrs({align:"center",justify:"center"})`
  margin-top: 10px;
`,Kr=x.a`
  cursor: pointer;
  text-decoration: none;
  font-weight: 500;
  color: #777;
  size: 14px;
  line-height: 17px;
  &:hover {
    color: #ab9ff2;
    p {
      color: #ab9ff2;
    }
  }
`,Be=x(_).attrs({color:"#eb3742",size:14,textAlign:"left"})``,jr=x.div`
  ${Be} {
    padding-bottom: 20px; // NOTE: animation requires padding instead of margin
    svg {
      margin-right: 6px;
      transform: translateY(1px);
    }
  }
`;n();i();var X=S(P());var Ur=3,$r=3,Yr=4,qr=4,Wo=()=>{let{onboardingType:e,onboardingStack:t,popStep:o,wantsAllSitePermissions:r}=A(),a=Zt(),{hardwareStepStack:s,popStep:d}=he(),m=de(),u=(0,X.useRef)(m.search),y=e==="restore",l=e==="append",g=e==="seedless",f=t.length,T=(0,X.useMemo)(()=>{switch(e){case"restore":return f-1;case"append":return f-1;default:return f-2}},[e,f]),h=(0,X.useMemo)(()=>!y&&!l,[l,y]),F=r?1:0,B=(0,X.useMemo)(()=>{switch(e){case"create":return $r;case"importSeed":case"importPrivateKey":case"connectHardware":return Yr;case"append":return Ur;default:return qr}},[e])+F,U=(0,X.useCallback)(async()=>{T===1&&m.search.length>0&&a({search:u.current},{replace:!0}),s.length>1?t.length===4?o():t.length<4?d():o():o()},[a,T,m.search.length,s.length,t.length,d,o]);return X.default.createElement("div",{className:be.container},h?X.default.createElement("button",{className:be.button,onClick:U},X.default.createElement(H0.ArrowLeft,{size:20})):X.default.createElement("div",null),g?null:X.default.createElement("div",{className:be.stepCircles},Xe(B).map(H=>X.default.createElement(oo,{key:H,diameter:12,color:H<=T?"#AB9FF2":"#333"}))),g&&T===1?X.default.createElement("a",{className:be.helpButton,href:Bt,target:"_blank",referrerPolicy:"no-referrer"},X.default.createElement(H0.HelpCircle,{size:20})):null)},be={button:h0({color:["textTertiary","textPrimary"],left:16,position:"absolute",reset:"button"}),helpButton:h0({color:["textTertiary","textPrimary"],left:16,reset:"button",marginLeft:"auto"}),container:h0({borderBottomColor:"borderSecondary",borderBottomWidth:1,borderBottomStyle:"solid",display:"row",height:48,alignItems:"center",justifyContent:"space-between",paddingX:10,paddingY:16,position:"relative"}),stepCircles:h0({alignItems:"center",display:"row",justifyContent:"center",width:"100%",gap:10})};n();i();var De={content:"_175sik91 _51gazn1x _51gazn37 _51gazn18p _51gazn5r",placeholderNav:"_175sik92"};n();i();var y0=S(P());n();i();var q=S(P());n();i();var g0=S(P());var Go=g0.default.memo(({features:e,image:t,title:o,description:r})=>g0.default.createElement(g0.default.Fragment,null,t,g0.default.createElement(c,null,g0.default.createElement(Xr,null,o),typeof r=="string"?g0.default.createElement(_,{content:r}):r),g0.default.createElement(Rr,null,e.map(a=>g0.default.createElement(tn,{key:a.title},g0.default.createElement(en,null,a.icon),g0.default.createElement("div",null,g0.default.createElement(Qr,null,a.title),typeof a.description=="string"?g0.default.createElement(Jr,null,a.description):a.description)))))),Ha=x.div`
  flex: 1;
  padding: 0 20px;
  text-align: center;
  overflow: auto;
`,za=x.div`
  flex: 1;
  overflow: auto;
  position: relative;
`,Xr=x(_).attrs({size:28,weight:600,lineHeight:34,margin:"0 0 24px"})`
  align-self: center;
  text-align: center;
`,Qr=x(_).attrs({size:18,weight:600,lineHeight:24,margin:"0 0 4px"})`
  text-align: left;
`,Jr=x(_).attrs({size:14,weight:400,lineHeight:17,color:"#999"})`
  text-align: left;
`,Rr=x.ul`
  align-self: center;
  max-width: 500px;
  margin: 0 auto;
`,en=x.div`
  display: flex;
  justify-content: center;
  margin-right: 16px;
  margin-top: 4px;
  min-width: 26px;
`,tn=x.li`
  display: flex;
  margin-bottom: 24px;
`;n();i();var I=S(P());n();i();var M0=S(P());n();i();var K=S(P()),Vo=()=>K.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:150,height:150,fill:"none"},K.default.createElement("path",{fill:"#8D8C8A",fillRule:"evenodd",d:"M72.594 11.531a64.288 64.288 0 0 1 4.012 0 .5.5 0 0 1-.03 1 63.213 63.213 0 0 0-3.951 0 .5.5 0 1 1-.031-1Zm-4.439.795a.5.5 0 0 1-.442.551c-1.314.146-2.615.331-3.903.557a.5.5 0 1 1-.172-.985 63.02 63.02 0 0 1 3.965-.565.5.5 0 0 1 .552.442Zm12.89 0a.5.5 0 0 1 .552-.442 63.01 63.01 0 0 1 3.965.565.5.5 0 0 1-.172.985 62.155 62.155 0 0 0-3.903-.556.5.5 0 0 1-.442-.552Zm-21.687 1.54a.5.5 0 0 1-.36.61c-1.277.33-2.538.7-3.782 1.11a.5.5 0 0 1-.312-.951 62.587 62.587 0 0 1 3.844-********* 0 0 1 .61.359Zm30.484 0a.5.5 0 0 1 .61-.358c1.298.336 2.58.712 3.844 1.127a.5.5 0 0 1-.312.95 61.604 61.604 0 0 0-3.783-********* 0 0 1-.359-.61ZM50.87 16.653a.5.5 0 0 1-.268.655 61.798 61.798 0 0 0-3.584 1.64.5.5 0 0 1-.445-.896 62.784 62.784 0 0 1 3.642-1.667.5.5 0 0 1 .655.268Zm47.46 0a.5.5 0 0 1 .655-.268 62.845 62.845 0 0 1 3.642 1.666.5.5 0 0 1-.445.896 61.728 61.728 0 0 0-3.584-1.64.5.5 0 0 1-.268-.654Zm-55.462 3.972a.5.5 0 0 1-.171.686 62.215 62.215 0 0 0-3.314 2.134.5.5 0 0 1-.568-.823 63.305 63.305 0 0 1 3.367-2.168.5.5 0 0 1 .686.171Zm63.464 0a.5.5 0 0 1 .686-.171 63.159 63.159 0 0 1 3.366 2.168.5.5 0 1 1-.567.823 62.633 62.633 0 0 0-3.314-2.134.5.5 0 0 1-.171-.686Zm-70.817 5.072a.5.5 0 0 1-.073.703c-1.02.83-2.013 1.69-2.977 2.582a.5.5 0 0 1-.679-.734c.98-.906 1.989-1.78 3.025-2.624a.5.5 0 0 1 .703.073Zm78.171 0a.5.5 0 0 1 .703-.073 63.482 63.482 0 0 1 3.025 2.624.5.5 0 0 1-.679.734 61.99 61.99 0 0 0-2.977-2.582.5.5 0 0 1-.072-.703Zm-84.733 6.062a.5.5 0 0 1 .028.707 62.443 62.443 0 0 0-2.582 2.977.5.5 0 0 1-.776-.63 63.435 63.435 0 0 1 2.624-3.026.5.5 0 0 1 .706-.028Zm91.294 0a.5.5 0 0 1 .706.028c.906.98 1.781 1.989 2.624 3.025a.5.5 0 0 1-.776.631 62.96 62.96 0 0 0-2.582-2.977.5.5 0 0 1 .028-.707Zm-96.93 6.93a.5.5 0 0 1 .127.695 62.215 62.215 0 0 0-2.134 3.314.5.5 0 1 1-.857-.515 63.162 63.162 0 0 1 2.168-3.367.5.5 0 0 1 .696-.127Zm102.566 0a.5.5 0 0 1 .696.127 63.325 63.325 0 0 1 2.168 3.367.5.5 0 0 1-.857.515 62.281 62.281 0 0 0-2.134-3.314.5.5 0 0 1 .127-.695Zm-107.162 7.66a.5.5 0 0 1 .225.67 61.798 61.798 0 0 0-1.64 3.584.5.5 0 1 1-.922-.387 62.784 62.784 0 0 1 1.666-3.642.5.5 0 0 1 .67-.226Zm111.758 0a.5.5 0 0 1 .67.225 62.594 62.594 0 0 1 1.667 3.642.501.501 0 0 1-.923.387 61.524 61.524 0 0 0-1.639-3.584.5.5 0 0 1 .225-.67ZM15.266 54.584a.5.5 0 0 1 .32.63A61.604 61.604 0 0 0 14.475 59a.5.5 0 0 1-.968-.25c.336-1.299.712-2.58 1.127-3.845a.5.5 0 0 1 .631-.32Zm-2.238 8.646a.5.5 0 0 1 .406.58 62.155 62.155 0 0 0-.556 3.902.5.5 0 1 1-.994-.11 63.01 63.01 0 0 1 .565-3.965.5.5 0 0 1 .579-.407Zm-.981 8.878a.5.5 0 0 1 .484.516 63.213 63.213 0 0 0 0 3.95.5.5 0 1 1-1 .031 64.288 64.288 0 0 1 0-4.012.5.5 0 0 1 .516-.484Zm.279 8.936a.5.5 0 0 1 .551.442c.146 1.314.331 2.615.557 3.903a.5.5 0 1 1-.985.172 63.01 63.01 0 0 1-.565-3.965.5.5 0 0 1 .442-.552Zm124.548 0c.275.03.473.277.442.552a63.009 63.009 0 0 1-.565 3.965.5.5 0 0 1-.985-.172c.226-1.288.412-2.59.556-3.903a.5.5 0 0 1 .552-.442ZM13.867 89.842a.5.5 0 0 1 .609.36c.33 1.277.7 2.538 1.11 3.782a.5.5 0 0 1-.951.312 62.587 62.587 0 0 1-1.127-3.844.5.5 0 0 1 .359-.61Zm121.466 0a.5.5 0 0 1 .359.61 62.727 62.727 0 0 1-1.127 3.844.5.5 0 0 1-.95-.312 61.533 61.533 0 0 0 1.109-3.783.5.5 0 0 1 .609-.359ZM16.653 98.33a.5.5 0 0 1 .654.268 61.728 61.728 0 0 0 1.64 3.584.5.5 0 1 1-.896.445 62.845 62.845 0 0 1-1.667-3.642.5.5 0 0 1 .268-.655Zm115.895 0c.254.107.374.4.268.655a62.655 62.655 0 0 1-1.667 3.642.5.5 0 1 1-.895-.445 61.454 61.454 0 0 0 1.639-3.584.5.5 0 0 1 .655-.268Zm-111.924 8.002a.5.5 0 0 1 .686.171 62.633 62.633 0 0 0 2.134 3.314.5.5 0 1 1-.823.567 63.159 63.159 0 0 1-2.168-3.366.5.5 0 0 1 .171-.686Zm107.952 0a.5.5 0 0 1 .171.686 63.22 63.22 0 0 1-2.168 3.366.5.5 0 1 1-.823-.567 62.7 62.7 0 0 0 2.134-3.314.5.5 0 0 1 .686-.171Zm-102.88 7.354a.5.5 0 0 1 .703.072 61.99 61.99 0 0 0 2.582 2.977.5.5 0 0 1-.734.679 63.482 63.482 0 0 1-2.624-3.025.5.5 0 0 1 .073-.703Zm97.808 0a.5.5 0 0 1 .073.703 63.436 63.436 0 0 1-2.624 3.025.5.5 0 1 1-.734-.679 62.552 62.552 0 0 0 2.582-2.977.499.499 0 0 1 .703-.072Zm-91.746 6.561a.5.5 0 0 1 .707-.028 63.013 63.013 0 0 0 2.977 2.582.5.5 0 1 1-.63.776 63.389 63.389 0 0 1-3.026-2.624.5.5 0 0 1-.028-.706Zm85.684 0a.5.5 0 0 1-.028.706 63.436 63.436 0 0 1-3.025 2.624.5.5 0 0 1-.631-.776 62.552 62.552 0 0 0 2.977-2.582.5.5 0 0 1 .707.028Zm-78.754 5.636a.5.5 0 0 1 .695-.127 62.281 62.281 0 0 0 3.314 2.134.5.5 0 1 1-.515.857 63.325 63.325 0 0 1-3.367-2.168.5.5 0 0 1-.127-.696Zm71.824 0a.5.5 0 0 1-.128.696 63.22 63.22 0 0 1-3.366 2.168.5.5 0 0 1-.515-.857 62.7 62.7 0 0 0 3.314-2.134.5.5 0 0 1 .695.127Zm-64.165 4.596a.5.5 0 0 1 .67-.225c1.175.583 2.37 1.13 3.585 1.639a.5.5 0 0 1-.387.923 62.594 62.594 0 0 1-3.642-1.667.5.5 0 0 1-.226-.67Zm56.505 0a.5.5 0 0 1-.225.67 62.655 62.655 0 0 1-3.642 1.667.501.501 0 0 1-.387-.923 61.454 61.454 0 0 0 3.584-1.639.5.5 0 0 1 .67.225Zm-48.267 3.455a.5.5 0 0 1 .63-.319c1.245.408 2.506.779 3.784 1.109a.5.5 0 1 1-.25.968 62.727 62.727 0 0 1-3.845-********* 0 0 1-.32-.631Zm40.03 0a.5.5 0 0 1-.319.631 62.727 62.727 0 0 1-3.844 ********* 0 1 1-.25-.968 61.533 61.533 0 0 0 3.782-********* 0 0 1 .631.319Zm-31.383 2.238a.5.5 0 0 1 .578-.406c1.288.226 2.59.412 3.903.556a.5.5 0 0 1-.11.994 63.009 63.009 0 0 1-3.965-.565.5.5 0 0 1-.407-.579Zm22.737 0a.5.5 0 0 1-.407.579c-1.308.229-2.63.418-3.965.565a.499.499 0 1 1-.11-.994 61.067 61.067 0 0 0 3.903-.556.5.5 0 0 1 .579.406Zm-13.86.981a.5.5 0 0 1 .516-.484 61.712 61.712 0 0 0 3.95 0 .5.5 0 0 1 .031 1 63.68 63.68 0 0 1-4.012 0 .5.5 0 0 1-.484-.516Z",clipRule:"evenodd"}),K.default.createElement("mask",{id:"a",width:71,height:71,x:39,y:40,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},K.default.createElement("path",{fill:"#fff",d:"M109.5 75.6c0-19.275-15.625-34.9-34.9-34.9S39.7 56.324 39.7 75.6c0 19.274 15.625 34.9 34.9 34.9s34.9-15.626 34.9-34.9Z"})),K.default.createElement("g",{mask:"url(#a)"},K.default.createElement("mask",{id:"b",width:71,height:71,x:39,y:40,maskUnits:"userSpaceOnUse",style:{maskType:"luminance"}},K.default.createElement("path",{fill:"#fff",d:"M109.5 40.7H39.7v69.8h69.8V40.7Z"})),K.default.createElement("g",{mask:"url(#b)"},K.default.createElement("path",{fill:"#9886E5",d:"M109.5 75.6c0-19.275-15.625-34.9-34.9-34.9S39.7 56.324 39.7 75.6c0 19.274 15.625 34.9 34.9 34.9s34.9-15.626 34.9-34.9Z"}),K.default.createElement("path",{fill:"#FFD13F",d:"M51.085 138.57c8.066-3.369 11.2-12.911 12.503-19.961.072 1.409.34 2.737.83 3.913 1.352 3.236 4.172 4.763 7.838 3.232 5.035-2.103 9.075-6.727 9.875-12.646.942 2.255 2.807 3.146 5.397 2.063 8.164-3.41 10.336-21.3 5.053-33.947-4.116-9.853-12.726-16.446-25.24-11.219-21.998 9.19-34.485 45.955-27.237 63.308 2.846 6.813 7.168 6.85 10.98 5.257Z"}),K.default.createElement("path",{stroke:"#1C1C1C",strokeLinecap:"round",strokeWidth:1.105,d:"M77.83 91.708c-.07-3.51-1.68-6.27-3.598-6.163-1.917.107-3.415 3.039-3.345 6.549M88.416 90.236c-.07-3.51-1.68-6.269-3.597-6.162-1.918.106-3.415 3.038-3.345 6.548"}),K.default.createElement("path",{fill:"#FFD13F",d:"M100.487 80.695c.175.619.894.576 1.017-.06.693-3.512 1.493-10.9-2.19-17.031-5.028-8.37-10.696-1.594-6.66 2.792 3.68 4.002 5.975 7.723 7.833 14.3ZM90.832 57.014c-2.707 1.413-3.283 2.404-3.104 5.486.015.264-.376.37-.495.135-1.405-2.75-2.401-3.314-5.452-3.164-.275.013-.366-.322-.121-.45 2.706-1.412 3.28-2.402 3.102-5.486-.014-.263.376-.37.496-.134 1.404 2.75 2.4 3.313 5.451 3.164.275-.014.366.321.122.449h.001Z"}))),K.default.createElement("path",{fill:"#FFD13F",d:"m63.4 52.257-.478 4.541c-.09.86.29 1.702.996 2.201l3.727 2.64c.352.248.143.802-.286.758l-4.542-.477c-.86-.09-1.702.29-2.201.996l-2.64 3.727c-.25.351-.803.143-.758-.286l.477-4.542a2.39 2.39 0 0 0-.996-2.2l-3.727-2.64c-.352-.249-.143-.803.286-.758l4.542.477c.86.09 1.702-.29 2.201-.996l2.64-3.727c.25-.352.803-.143.759.286Z"}),K.default.createElement("path",{fill:"#8D8C8A",fillRule:"evenodd",d:"M135.5 54.5c-6.351 0-11.5 5.149-11.5 11.5s5.149 11.5 11.5 11.5S147 72.351 147 66s-5.149-11.5-11.5-11.5ZM123 66c0-6.904 5.596-12.5 12.5-12.5S148 59.096 148 66s-5.596 12.5-12.5 12.5S123 72.904 123 66Z",clipRule:"evenodd"}),K.default.createElement("path",{fill:"#8D8C8A",fillRule:"evenodd",d:"M135.5 59.215a.5.5 0 0 1 .5.5V72.27a.5.5 0 0 1-1 0V59.715a.5.5 0 0 1 .5-.5Z",clipRule:"evenodd"}),K.default.createElement("path",{fill:"#8D8C8A",fillRule:"evenodd",d:"M128.715 66a.5.5 0 0 1 .5-.5h12.557a.5.5 0 0 1 0 1h-12.557a.5.5 0 0 1-.5-.5Z",clipRule:"evenodd"}),K.default.createElement("path",{fill:"#9886E5",d:"M91.5 29c6.627 0 12-5.373 12-12s-5.373-12-12-12-12 5.373-12 12 5.373 12 12 12Z"}),K.default.createElement("path",{stroke:"#222",strokeLinecap:"round",strokeLinejoin:"round",d:"m85.581 17.515 3.638 3.801 8.2-8.62"}),K.default.createElement("path",{fill:"#9886E5",d:"M34.5 133c6.627 0 12-5.373 12-12s-5.373-12-12-12-12 5.373-12 12 5.373 12 12 12Z"}),K.default.createElement("path",{stroke:"#222",strokeLinecap:"round",strokeLinejoin:"round",d:"m28.581 121.515 3.638 3.801 8.2-8.62"}),K.default.createElement("path",{fill:"#8D8C8A",d:"m16.553 3.71 1.762 6.722a3.642 3.642 0 0 0 2.605 2.598l6.74 1.758c.635.166.635 1.066 0 1.233l-6.74 1.758a3.643 3.643 0 0 0-2.607 2.598L16.55 27.1c-.166.634-1.07.634-1.236 0l-1.761-6.722a3.642 3.642 0 0 0-2.606-2.598L4.208 16.02c-.636-.166-.635-1.066 0-1.233l6.74-1.758a3.644 3.644 0 0 0 2.606-2.598l1.764-6.722c.166-.634 1.069-.634 1.236 0ZM116.11 129.528l-4.74.211a2.483 2.483 0 0 0-2.106 1.364l-2.135 4.236c-.202.4-.803.271-.823-.177l-.213-4.739a2.483 2.483 0 0 0-1.363-2.107l-4.236-2.136c-.4-.201-.271-.803.176-.823l4.739-.211a2.486 2.486 0 0 0 2.107-1.364l2.135-4.236c.201-.4.802-.271.823.176l.212 4.74c.04.897.562 1.703 1.363 2.107l4.237 2.136c.4.201.271.802-.176.823Z"}));n();i();var C=S(P());n();i();var Ho=S(P());var ke=()=>{let e=rt(),t=at();return(0,Ho.useCallback)(async({entropy:o})=>{let r=await N0(),a=o.fold(r.fromEntropy),s=it(o),d=[];try{d=(await e.getSyncedAccounts()).walletAccounts}catch{}let m=[];for(let u of d){if(u.accountOrigin!==`seedless:${s}`||u.derivationIndex===null||u.derivationIndex<0)continue;let y=u.addresses.reduce((l,g)=>(g.pathType!=="privateKey"&&l.push(g.pathType),l),[]);m.push({derivationIndex:u.derivationIndex,derivationPathTypes:y})}if(m.length===0){let y=(await Je(t)).enabledAddressTypes,l=te(y);m.push({derivationIndex:0,derivationPathTypes:l})}return{mnemonic:a,accountCreationMetas:m}},[t,e])};n();i();var O=S(P()),zo=({goBack:e})=>{let{t}=b(),o=[{icon:O.default.createElement(c,{alignSelf:"stretch",paddingTop:2,paddingRight:2},O.default.createElement(c,{backgroundColor:"accentPrimary",alignItems:"center",justifyContent:"center",borderRadius:"circle",size:24},O.default.createElement(p,{children:1,font:"captionSemibold"}))),title:()=>O.default.createElement(p,{font:"bodySemibold",children:t("seedlessForgotPinInstruction1PrimaryText")}),description:()=>O.default.createElement(c,{paddingTop:4},O.default.createElement(p,{font:"caption",color:"textSecondary",children:t("seedlessForgotPinInstruction1SecondaryText")}))},{icon:O.default.createElement(c,{alignSelf:"stretch",paddingTop:2,paddingRight:2},O.default.createElement(c,{backgroundColor:"accentPrimary",alignItems:"center",justifyContent:"center",borderRadius:"circle",size:24},O.default.createElement(p,{children:2,font:"captionSemibold"}))),title:()=>O.default.createElement(p,{font:"bodySemibold",children:t("seedlessForgotPinInstruction2PrimaryText")}),description:()=>O.default.createElement(c,{paddingTop:4},O.default.createElement(p,{font:"caption",color:"textSecondary",children:t("seedlessForgotPinInstruction2SecondaryText")}))},{icon:O.default.createElement(c,{alignSelf:"stretch",paddingTop:2,paddingRight:2},O.default.createElement(c,{backgroundColor:"accentPrimary",alignItems:"center",justifyContent:"center",borderRadius:"circle",size:24},O.default.createElement(p,{children:3,font:"captionSemibold"}))),title:()=>O.default.createElement(p,{font:"bodySemibold",children:t("seedlessForgotPinInstruction3PrimaryText")}),description:()=>O.default.createElement(c,{paddingTop:4},O.default.createElement(p,{font:"caption",color:"textSecondary",children:t("seedlessForgotPinInstruction3SecondaryText")}))}];return O.default.createElement(c,{flex:1},O.default.createElement(c,{padding:"screen",alignItems:"center",justifyContent:"center",gap:8},O.default.createElement(p,{align:"center",font:"heading4",children:t("seedlessForgotPinPrimaryText")}),O.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:t("seedlessForgotPinSecondaryText")})),O.default.createElement(me,{rows:o.map((r,a)=>({start:r.icon,topLeft:r.title,bottomLeft:r.description,key:`seedless-forgot-instruction-${a}`})),background:!1}),O.default.createElement(c,{marginTop:"auto"},O.default.createElement(ce,null,O.default.createElement(Z,{children:t("seedlessForgotPinButtonText"),onClick:e}))))};var on=(0,C.memo)(({isLoading:e,isError:t,isPinError:o,hasCooldownTimer:r,handleInput:a,onForgot:s,onSubmit:d,pinAttemptsLeft:m,disabled:u,pin:y,cooldownTimerText:l,continueButtonText:g,forgotButtonText:f,attemptsLeftText:T,invalidPinText:h,title:F,loadingTitle:B,loadingDescription:U,errorTitle:H,errorDescription:Q,tryAgainButtonText:L})=>t?C.default.createElement(c,{flex:1},C.default.createElement(c,{flex:1,gap:8,alignItems:"center",justifyContent:"center"},C.default.createElement(p,{align:"center",font:"heading3Semibold",children:H}),C.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:Q})),C.default.createElement(Z,{className:D.buttonWrapper,theme:"primary",onClick:d},L)):e?C.default.createElement(c,{flex:1,alignItems:"center",justifyContent:"center"},C.default.createElement(se,{autoPlay:!0}),C.default.createElement(c,{marginTop:28,gap:8},C.default.createElement(p,{align:"center",font:"heading3Semibold",children:B}),C.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:U}))):C.default.createElement(c,{flex:1},C.default.createElement(c,{marginY:28,gap:8},C.default.createElement(p,{align:"center",font:"heading3Semibold",children:F})),C.default.createElement(c,{className:D.pinInputContainer},C.default.createElement(I0,{className:D.pinInput,disabled:r,value:y,onChange:z=>a(z.target.value),maxLength:4,placeholder:"\u2022\u2022\u2022\u2022",color:o?E0.colors.legacy.accentAlert:E0.colors.legacy.textPrimary,type:"password",inputMode:"numeric",borderRadius:"16px"})),o||r?C.default.createElement(c,null,C.default.createElement(c,{className:D.errorTextContainer,gap:4},C.default.createElement(c,null,C.default.createElement(H0.XCircle,{size:14,color:"accentAlert"})),C.default.createElement(p,{color:"accentAlert",font:"label",children:h})),C.default.createElement(c,{className:D.errorTextContainer,gap:4},m?C.default.createElement(p,{marginLeft:"screen",color:"accentAlert",font:"label",children:T}):null,r?C.default.createElement(p,{marginLeft:m?0:"screen",color:"accentAlert",font:"label",children:l}):null)):null,C.default.createElement(c,{marginTop:"auto"},C.default.createElement(ce,null,C.default.createElement(Z,{children:f,onClick:s}),C.default.createElement(Z,{children:g,onClick:d,disabled:u}))))),Ae=()=>{let{t:e}=b(),{pushStep:t,popStep:o,setMnemonic:r,setAccounts:a}=A(),s=ke(),{data:d=[]}=U0(),m=Gt({t:e,hasEnrolledAuthentication:!0,syncedAccounts:d,onOnboardingProtect:(0,C.useCallback)(()=>t(C.default.createElement(m0,null)),[t]),onImportAndSyncAccounts:(0,C.useCallback)(async u=>{let{mnemonic:y,accountCreationMetas:l}=await s({entropy:u});r(y),a(l)},[s,r,a]),onForgot:(0,C.useCallback)(()=>t(C.default.createElement(zo,{goBack:o})),[t,o])});return C.default.createElement(on,{...m})};var Se=()=>{let{t:e}=b(),{pushStep:t}=A();return M0.default.createElement(c,{flex:1},M0.default.createElement(c,{flex:1,alignItems:"center",justifyContent:"center",padding:"screen",gap:8},M0.default.createElement(c,{marginBottom:24},M0.default.createElement(Vo,null)),M0.default.createElement(p,{align:"center",font:"heading3Semibold",children:e("seedlessAlreadyExistsPrimaryText")}),M0.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:e("seedlessAlreadyExistsSecondaryText")})),M0.default.createElement(Z,{className:D.buttonWrapper,theme:"primary",onClick:()=>t(M0.default.createElement(Ae,null))},e("seedlessContinueText")))};n();i();var Z0=S(P());n();i();var n0=S(P()),_o=()=>n0.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:210,height:154,fill:"none"},n0.default.createElement("g",{clipPath:"url(#a)"},n0.default.createElement("path",{fill:"#393939",d:"M189.905 42.65v87.39c0 13.1-10.59 23.73-23.63 23.73H51.155c-13.04 0-23.62-10.63-23.62-23.73V42.65c0-6.6 5.33-11.95 11.9-11.95h2.11s.02.06.02.09l.4 3.98a.75.75 0 0 1-.67.82l.34 3.48h.01c.41-.***********.67l.4 3.98c.04.42-.26.78-.67.82l.35 3.49c.41-.***********.67l.4 3.98a.75.75 0 0 1-.67.82l.35 3.48c.41-.***********.67l.4 3.98c.04.42-.25.77-.67.83l.35 3.48c.41-.***********.67l.07.64 1.87-.15c.42-.***********.69l3.49-.28c-.03-.41.27-.77.69-.81l3.99-.32c.41-.**********.69l3.49-.28c-.03-.41.28-.77.69-.81l3.99-.32c.41-.**********.69l3.49-.28c-.03-.41.28-.78.69-.81l3.99-.32c.41-.**********.69l3.49-.28c-.03-.42.28-.78.69-.81l3.99-.32c.42-.***********.69l3.48-.28c-.03-.42.28-.78.69-.81l3.99-.32c.42-.***********.68l3.49-.28c-.04-.41.27-.77.68-.8l3.99-.32c.42-.***********.68l3.49-.28c-.04-.41.27-.77.68-.8l3.99-.32c.42-.***********.68l3.49-.28a.75.75 0 0 1 .69-.81l3.98-.32c.41-.***********.69l3.49-.28c-.03-.41.27-.77.69-.81l3.98-.32c.42-.02.78.28.81.69l3.49-.28c-.03-.41.27-.77.69-.81l3.98-.32c.42-.***********.69l3.49-.28c-.03-.42.28-.78.69-.81l3.99-.32c.41-.**********.69l3.49-.28c-.03-.42.28-.78.69-.81l3.99-.32c.41-.**********.69l3.49-.28c-.03-.42.28-.78.69-.81l3.99-.32c.41-.***********.69l3.48-.28c-.03-.42.28-.78.69-.81l3.99-.32c.41-.***********.68l3.49-.28c-.03-.29.12-.55.35-.7l-.3-2.87c-.05-.41.25-.78.66-.82h.01l-.37-3.48h-.08c-.38 0-.71-.28-.75-.67l-.42-3.98a.75.75 0 0 1 .67-.82l-.37-3.48h-.08a.74.74 0 0 1-.74-.67l-.42-3.98c-.04-.41.25-.78.67-.82l-.37-3.48h-.08c-.38 0-.71-.29-.75-.67l-.03-.27h3.41c6.57 0 11.91 5.35 11.91 11.95Z"}),n0.default.createElement("path",{fill:"#A89EEB",d:"M141.745 96.78s-9.13-.41-14.79 4.42c-5.66 4.83-6.69 14.01-17.74 14.01-11.05 0-12.08-9.19-17.74-14.01-5.66-4.82-14.79-4.42-14.79-4.42h65.07-.01Z"}),n0.default.createElement("path",{fill:"#222",d:"m42.185 44.54-.54-5.47c.41-.***********.67l.4 3.98c.04.42-.26.78-.67.82ZM43.085 53.5l-.55-5.47c.41-.***********.67l.4 3.98a.75.75 0 0 1-.67.82ZM43.985 62.46l-.55-5.48c.41-.***********.67l.4 3.98c.04.42-.25.77-.67.83ZM47.895 67.79l-3.35.27-.21-2.12c.41-.***********.67l.07.64 1.87-.15c.42-.***********.69ZM56.865 67.07l-5.48.44c-.03-.41.27-.77.69-.81l3.99-.32c.41-.**********.69ZM65.835 66.35l-5.48.44c-.03-.41.28-.77.69-.81l3.99-.32c.41-.**********.69ZM74.805 65.63l-5.48.44c-.03-.41.28-.78.69-.81l3.99-.32c.41-.**********.69ZM83.785 64.91l-5.49.44c-.03-.42.28-.78.69-.81l3.99-.32c.42-.***********.69ZM92.755 64.18l-5.49.45c-.03-.42.28-.78.69-.81l3.99-.32c.42-.***********.68ZM101.725 63.46l-5.48.44c-.04-.41.27-.77.68-.8l3.99-.32c.42-.***********.68ZM110.695 62.74l-5.48.44c-.04-.41.27-.77.68-.8l3.99-.32c.42-.***********.68ZM119.665 62.02l-5.48.44a.75.75 0 0 1 .69-.81l3.98-.32c.41-.***********.69ZM128.635 61.3l-5.48.44c-.03-.41.27-.77.69-.81l3.98-.32c.42-.02.78.28.81.69ZM137.605 60.58l-5.48.44c-.03-.41.27-.77.69-.81l3.98-.32c.42-.***********.69ZM146.575 59.86l-5.48.44c-.03-.42.28-.78.69-.81l3.99-.32c.41-.**********.69ZM155.545 59.14l-5.48.44c-.03-.42.28-.78.69-.81l3.99-.32c.41-.**********.69ZM164.525 58.42l-5.49.44c-.03-.42.28-.78.69-.81l3.99-.32c.41-.***********.69ZM173.495 57.69l-5.49.45c-.03-.42.28-.78.69-.81l3.99-.32c.41-.***********.68ZM40.555 28.28l-.07-.74c-.16-1.63-.02-3.21.38-4.7.4.08.65.48.56.87a12.531 12.531 0 0 0-.2 3.75c.05.42-.26.78-.67.82ZM41.565 30.79l.4 3.98a.75.75 0 0 1-.67.82l-.49-4.89-.06-.58c.38-.05.72.22.8.58.01.03.02.06.02.09ZM45.925 15.6c.25.32.2.78-.13 1.04-.98.79-1.84 1.72-2.54 2.76-.15.22-.38.33-.63.33-.14 0-.28-.04-.4-.12.94-1.57 2.2-2.94 3.7-4.01ZM52.065 13.3l2.36-.25c.04.41-.26.78-.67.83l-1.61.17c-.74.07-1.48.22-2.2.43a.747.747 0 0 1-.92-.49c.96-.34 1.98-.58 3.04-.69ZM57.905 12.68l5.47-.57a.75.75 0 0 1-.67.82l-3.98.42s-.05.01-.08.01c-.37 0-.7-.29-.74-.68ZM66.855 11.74l5.47-.58a.76.76 0 0 1-.67.83l-3.98.42h-.07c-.38 0-.71-.28-.75-.67ZM75.805 10.8l5.47-.58a.75.75 0 0 1-.67.82l-3.97.42s-.06.01-.08.01c-.38 0-.71-.29-.75-.67ZM84.755 9.85l5.47-.57c.04.41-.25.78-.67.82l-3.97.42h-.08c-.38 0-.71-.28-.75-.67ZM93.705 8.91l5.47-.58c.04.41-.25.78-.67.83l-3.97.42h-.08c-.38 0-.71-.29-.75-.67ZM102.655 7.97l5.47-.58c.04.41-.25.78-.67.82l-3.97.42s-.06.01-.08.01c-.38 0-.7-.28-.75-.67ZM111.605 7.02l5.47-.57c.05.42-.25.77-.67.82l-3.97.42h-.08c-.38 0-.71-.28-.75-.67ZM120.555 6.08l5.47-.58c.05.41-.25.78-.66.82l-3.98.42s-.06.01-.08.01c-.38 0-.71-.29-.75-.67ZM129.505 5.13l5.47-.57c.05.41-.25.78-.66.82l-3.98.42h-.08c-.38 0-.71-.28-.75-.67ZM138.455 4.19l5.47-.58c.05.41-.25.78-.66.83l-3.98.42h-.08c-.38 0-.71-.29-.75-.67ZM147.405 3.25l5.47-.58c.05.41-.25.78-.66.82l-3.98.42s-.06.01-.08.01c-.38 0-.7-.28-.75-.67ZM161.835 1.99c-.03.41-.37.72-.77.69-.58-.02-1.17 0-1.75.06l-2.13.23h-.08c-.38 0-.71-.28-.75-.67l2.88-.3c.88-.09 1.75-.1 2.6-.01ZM165.235 2.78c1.72.66 3.28 1.68 4.57 2.98-.15.15-.34.23-.54.23-.17 0-.35-.07-.5-.19-.94-.85-2-1.53-3.15-2.05a.763.763 0 0 1-.38-.97ZM173.465 13.74c-.37 0-.69-.26-.74-.64-.19-1.25-.57-2.45-1.12-3.58a.72.72 0 0 1 .32-.98c.85 1.5 1.42 3.2 1.61 5.03l.02.16s-.06.01-.09.01ZM174.415 22.69c-.37 0-.7-.29-.74-.67l-.42-3.98a.75.75 0 0 1 .67-.82l.57 5.47h-.08ZM175.445 31.64h-.08c-.38 0-.71-.29-.75-.67l-.03-.27-.39-3.71a.75.75 0 0 1 .67-.82l.48 4.53.1.94ZM176.305 40.59a.74.74 0 0 1-.74-.67l-.42-3.98c-.04-.41.25-.78.67-.82l.57 5.47h-.08ZM177.255 49.54c-.38 0-.71-.28-.75-.67l-.42-3.98a.75.75 0 0 1 .67-.82l.58 5.47h-.08ZM177.335 56.71l-.3-2.87c-.05-.41.25-.78.66-.82h.01l.45 4.3-1.17.09c-.03-.29.12-.55.35-.7Z"}),n0.default.createElement("path",{fill:"#393939",d:"m47.085 67.1-1.87.15-.07-.64a.735.735 0 0 0-.81-.67.75.75 0 0 0-.67.82l.14 1.38c.04.38.36.67.75.67h.06l2.6-.21a.751.751 0 1 0-.12-1.5h-.01Zm8.98-.72-3.99.32c-.42.04-.72.4-.69.81.03.39.36.69.75.69h.06l3.99-.32c.41-.04.72-.4.68-.81a.749.749 0 0 0-.8-.69Zm8.97-.72-3.99.32c-.41.04-.72.4-.69.81.03.39.36.69.75.69h.06l3.99-.32c.41-.04.72-.4.68-.81a.749.749 0 0 0-.8-.69Zm8.97-.72-3.99.32c-.41.03-.72.4-.69.81.03.39.36.69.75.69h.06l3.99-.32c.41-.04.72-.4.68-.81a.742.742 0 0 0-.8-.69Zm8.97-.72-3.99.32c-.41.03-.72.39-.69.81.04.39.36.69.75.69.02 0 .04 0 .06-.01l3.99-.32c.41-.03.72-.39.69-.8-.04-.42-.39-.73-.81-.69Zm8.97-.72-3.99.32c-.41.03-.72.39-.69.81.04.39.36.69.75.69h.06l3.99-.32c.41-.03.72-.39.69-.8-.05-.43-.39-.72-.81-.69v-.01Zm8.97-.72-3.99.32c-.41.03-.72.39-.68.8.03.4.36.69.74.69h.06l3.99-.32c.41-.03.72-.39.69-.81a.75.75 0 0 0-.81-.68Zm8.97-.72-3.99.32c-.41.03-.72.39-.68.8.03.4.36.69.74.69h.06l3.99-.32c.41-.03.72-.39.69-.81a.738.738 0 0 0-.81-.68Zm8.97-.73-3.98.32a.75.75 0 0 0-.69.81c.03.39.36.69.74.69h.07l3.98-.32a.751.751 0 1 0-.12-1.5Zm-74.6-3.68a.75.75 0 0 0-.82-.67c-.42.04-.72.41-.68.82l.4 3.98c.04.39.37.68.75.68h.08c.42-.06.71-.41.67-.83l-.4-3.98Zm83.57 2.96-3.98.32c-.42.04-.72.4-.69.81.03.39.36.69.75.69h.06l3.98-.32c.42-.04.72-.4.69-.81-.03-.41-.39-.71-.81-.69Zm8.97-.72-3.98.32c-.42.04-.72.4-.69.81.03.39.36.69.75.69h.06l3.98-.32c.42-.04.73-.4.69-.81a.752.752 0 0 0-.81-.69Zm8.98-.72-3.99.32a.752.752 0 0 0 .06 1.5h.06l3.99-.32c.41-.04.72-.4.68-.81a.742.742 0 0 0-.8-.69Zm8.97-.72-3.99.32a.752.752 0 0 0 .06 1.5c.02 0 .04 0 .06-.01l3.99-.32c.41-.03.72-.39.68-.8a.742.742 0 0 0-.8-.69Zm8.97-.72-3.99.32a.752.752 0 0 0 .06 1.5h.06l3.99-.32c.41-.03.72-.39.69-.8a.757.757 0 0 0-.81-.69v-.01Zm8.97-.72-3.99.32c-.41.03-.72.39-.69.8.05.41.36.69.75.69h.06l3.99-.32c.41-.03.72-.39.69-.81a.756.756 0 0 0-.81-.68Zm5.84-3.32a.744.744 0 0 0-.82-.67h-.01c-.41.04-.71.41-.66.82l.3 2.87c-.23.15-.38.41-.35.7.03.4.36.69.74.69h.06l.43-.03a.757.757 0 0 0 .68-.83l-.37-3.55ZM43.355 48.7a.744.744 0 0 0-.82-.67.75.75 0 0 0-.67.82l.4 3.98c.04.39.36.67.74.67h.08a.75.75 0 0 0 .67-.82l-.4-3.98Zm134.22-3.97a.743.743 0 0 0-.82-.66.75.75 0 0 0-.67.82l.42 3.98c.***********.75.67h.08c.41-.05.71-.41.66-.83l-.42-3.98Zm-135.12-4.99c-.04-.41-.4-.7-.81-.67h-.01a.75.75 0 0 0-.67.82l.4 3.98c.04.39.36.68.75.68h.07c.41-.04.71-.4.67-.82l-.4-3.98v-.01Zm134.18-3.96a.743.743 0 0 0-.82-.66c-.42.04-.71.41-.67.82l.42 3.98c.04.39.36.67.74.67h.08c.41-.05.71-.42.67-.83l-.42-3.98Zm-135.07-4.99s0-.06-.02-.09c-.08-.36-.42-.63-.8-.58-.33.03-.59.28-.66.58-.02.07-.03.16-.02.24l.4 3.98c.***********.75.67h.08a.75.75 0 0 0 .67-.82l-.4-3.98Zm134.54-.09-.41-3.87a.753.753 0 0 0-.83-.66.75.75 0 0 0-.67.82l.39 3.71.03.27c.04.38.37.67.75.67h.08c.41-.05.71-.42.67-.83v-.11h-.01Zm-135.24-7.86h-.02a.75.75 0 0 0-.89.57c-.19.93-.29 1.88-.29 2.83 0 .46.03.92.07 1.38.04.39.37.68.75.68.02 0 .05 0 .07-.01.41-.04.72-.4.67-.82a12.531 12.531 0 0 1 .2-3.75.724.724 0 0 0-.56-.87v-.01Zm133.88-4.96a.743.743 0 0 0-.82-.66.75.75 0 0 0-.67.82l.42 3.98c.04.38.37.67.74.67h.08c.42-.05.72-.42.67-.83l-.42-3.98ZM45.925 15.6l-.01-.01a.739.739 0 0 0-1.05-.12c-1.11.88-2.07 1.92-2.86 3.09-.23.35-.14.81.21 1.05h.01c.12.08.26.12.4.12.25 0 .48-.11.63-.33.7-1.04 1.56-1.97 2.54-2.76.33-.26.38-.72.13-1.04Zm7.67-3.22-1.61.17c-.83.09-1.66.26-2.46.49a.75.75 0 0 0-.51.93v.02a.747.747 0 0 0 .92.49c.72-.21 1.46-.36 2.2-.43l1.61-.17c.41-.05.71-.42.67-.83-.04-.41-.41-.7-.83-.67h.01Zm119.35-3.52a.74.74 0 0 0-1-.34c-.01 0-.01.01-.02.02a.72.72 0 0 0-.32.98c.55 1.13.93 2.33 1.12 3.58.05.38.37.64.74.64.03 0 .06 0 .09-.01h.02c.41-.06.69-.44.63-.85-.21-1.4-.63-2.76-1.26-4.02Zm-110.39 2.58-3.98.42a.75.75 0 0 0 .07 1.5c.03 0 .06-.01.08-.01l3.98-.42a.75.75 0 0 0 .67-.82.744.744 0 0 0-.82-.67Zm8.95-.94-3.98.42a.75.75 0 0 0-.67.82c.***********.75.67h.07l3.98-.42a.76.76 0 0 0 .67-.83.749.749 0 0 0-.82-.66Zm8.95-.95-3.98.42c-.41.04-.71.41-.67.83.04.38.37.67.75.67.02 0 .05 0 .08-.01l3.97-.42a.75.75 0 0 0 .67-.82.757.757 0 0 0-.82-.67Zm8.95-.94-3.98.42a.75.75 0 0 0-.67.82c.***********.75.67h.08l3.97-.42c.42-.04.71-.41.67-.82-.04-.42-.42-.7-.82-.67Zm8.95-.95-3.98.42c-.41.05-.71.42-.67.83.04.38.37.67.75.67h.08l3.97-.42c.42-.05.71-.42.67-.83a.758.758 0 0 0-.82-.67Zm8.95-.94-3.98.42a.75.75 0 0 0 .08 1.5c.02 0 .05-.01.08-.01l3.97-.42c.42-.04.71-.41.67-.82a.75.75 0 0 0-.82-.67Zm8.95-.94-3.98.42a.75.75 0 0 0-.67.82c.***********.75.67h.08l3.97-.42c.42-.05.72-.4.67-.82a.744.744 0 0 0-.82-.67Zm8.95-.95-3.98.42c-.41.05-.71.42-.67.83.04.38.37.67.75.67.02 0 .05 0 .08-.01l3.98-.42c.41-.04.71-.41.66-.82a.75.75 0 0 0-.82-.67Zm44.56-.15a13.5 13.5 0 0 0-3.53-2.3c-.38-.17-.82 0-.99.38l-.01.02c-.15.37.02.8.38.97 1.15.52 2.21 1.2 3.15 2.05.15.12.33.19.5.19.2 0 .39-.08.54-.23l.02-.02c.28-.31.25-.79-.06-1.06Zm-35.61-.79-3.98.42a.75.75 0 0 0-.67.82c.***********.75.67h.08l3.98-.42c.41-.04.71-.41.66-.82a.744.744 0 0 0-.82-.67Zm8.95-.95-3.98.42c-.41.05-.71.42-.67.83.04.38.37.67.75.67h.08l3.98-.42c.41-.05.71-.42.66-.83a.758.758 0 0 0-.82-.67Zm8.95-.94-3.98.42a.75.75 0 0 0 .08 1.5c.02 0 .05-.01.08-.01l3.98-.42c.41-.04.71-.41.66-.82a.75.75 0 0 0-.82-.67Zm9.06-.82c-.65-.02-1.31 0-1.96.07l-2.13.23a.75.75 0 0 0-.67.82c.***********.75.67h.08l2.13-.23c.58-.06 1.17-.08 1.75-.**********-.28.77-.69v-.03a.75.75 0 0 0-.72-.78Z"}),n0.default.createElement("path",{fill:"#393939",fillRule:"evenodd",d:"M109.065 23.85c.51-.05.97.32 1.03.83l.79 7.44 7.44-.78c.51-.05.97.32 **********.51-.32.97-.83 1.03l-********** 7.44c.05.51-.32.97-.83 1.03a.947.947 0 0 1-1.03-.83l-.79-7.44-7.44.78a.947.947 0 0 1-1.03-.83c-.05-.51.32-.97.83-1.03l7.44-.78-.78-7.44c-.05-.51.32-.97.83-1.03Z",clipRule:"evenodd"}),n0.default.createElement("path",{fill:"#60BC8E",d:"M40.745 52.53c.55-7.13 6.74-12.48 13.88-11.99l111.28 7.73c7.17.5 12.58 6.71 12.08 13.88l-2.01 28.92-137.27-11.99 2.04-26.55Z"}),n0.default.createElement("path",{fill:"#A89EEB",d:"M38.535 81.65c0-7.19 5.83-13.01 13.01-13.01h115.97c7.19 0 13.01 5.83 13.01 13.01v15.99H38.535V81.65Z"}),n0.default.createElement("path",{fill:"#FFFFC9",d:"M200.225 127.27c-6.17 4.36-7.26 6.94-5.83 ***********-.8 1.02-1.17.5-4.32-6.08-6.93-7.07-14.34-5.61-.67.13-1-.64-.44-1.03 6.16-4.36 7.25-6.94 5.82-14.26-.12-.63.8-1.02 1.17-.5 4.32 6.08 6.93 7.07 14.34 5.61.67-.13 1 .64.44 1.03h.01ZM14.025 6.75l2.89 7.65a4.289 4.289 0 0 0 3.37 2.72l8.11 1.22c.76.12.88 1.17.15 1.44l-7.67 2.89c-1.45.55-2.5 1.83-2.73 3.36l-1.23 8.09c-.12.76-1.17.87-1.45.15l-2.89-7.65a4.289 4.289 0 0 0-3.37-2.72l-8.11-1.22c-.76-.12-.88-1.17-.15-1.44l7.67-2.89c1.45-.55 2.5-1.83 2.73-3.36l1.23-8.09c.12-.76 1.17-.87 1.45-.15ZM207.535 139.63a2 2 0 1 0-.001-4.001 2 2 0 0 0 .001 4.001ZM28.035 41.25a2.5 2.5 0 1 0 0-5 2.5 2.5 0 0 0 0 5ZM37.365 5c-3.1 1.05-3.88 1.98-4.19 5.2-.03.28-.46.32-.55.06-1.04-3.06-2-3.8-5.26-4.11-.29-.03-.34-.39-.06-.48 3.1-1.05 3.87-1.98 4.19-5.2.03-.27.46-.32.55-.06 1.04 3.06 2 3.8 5.26 4.11.29.0**********.48Z"}),n0.default.createElement("path",{fill:"#FFFCF7",d:"M52.965 89.63a7 7 0 1 0 0-14 7 7 0 0 0 0 14ZM123.465 75.63h46c3.04 0 5.5 2.46 5.5 5.5s-2.46 5.5-5.5 5.5h-46c-3.04 0-5.5-2.46-5.5-5.5s2.46-5.5 5.5-5.5ZM54.635 61.25a7.07 7.07 0 1 0 0-14.14 7.07 7.07 0 0 0 0 14.14ZM122.875 52.17l43.47 3.02c3.03.21 5.32 2.84 5.11 5.87a5.506 5.506 0 0 1-5.87 5.11l-43.47-3.02a5.506 5.506 0 0 1-5.11-5.87 5.506 5.506 0 0 1 5.87-5.11Z",opacity:.4})),n0.default.createElement("defs",null,n0.default.createElement("clipPath",{id:"a"},n0.default.createElement("path",{fill:"#fff",d:"M.465.23h209.07v153.54H.465z"}))));n();i();var i0=S(P());n();i();var E=S(P());var Ko=({pin:e})=>{let{t}=b(),{pushStep:o,setMnemonic:r,setAccounts:a}=A(),{mutateAsync:s}=Wt(),d=ke(),[m,u]=(0,E.useState)(""),{mutateAsync:y}=Nt(),[l,g]=(0,E.useState)(!1),{mutateAsync:f,isError:T}=Lt(),[h,F]=(0,E.useState)(!1),B=L0(),{refetch:U}=pe(),H=N=>{u(N.target.value)},Q=async()=>{let{data:N}=await U();if(N){o(E.default.createElement(Se,null));return}L()},L=async()=>{if(!h)try{g(!1),F(!0);let{entropy:N}=await f(m),{mnemonic:e0,accountCreationMetas:M}=await d({entropy:N});r(e0),a(M),await s(),await y(),B.capture("seedlessConfirmPinDone"),o(E.default.createElement(m0,null))}catch(N){D0.addBreadcrumb("se*dless","An error occured in pin confirm trying to backup the bundle","info"),D0.captureError(N,"se*dless"),g(!0),F(!1)}},z=m.length===4&&Q0(e)&&e!==m,s0=m.length===4&&Q0(e)&&e===m;return l||T?E.default.createElement(c,{flex:1},E.default.createElement(c,{flex:1,gap:8,alignItems:"center",justifyContent:"center"},E.default.createElement(p,{align:"center",font:"heading3Semibold",children:t("seedlessLoadingWalletErrorPrimaryText")}),E.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:t("seedlessErrorSecondaryText")})),E.default.createElement(Z,{theme:"primary",className:D.buttonWrapper,onClick:Q},t("seedlessTryAgain"))):h?E.default.createElement(c,{flex:1},E.default.createElement(c,{flex:1,alignItems:"center",justifyContent:"center"},E.default.createElement(se,{autoPlay:!0}),E.default.createElement(c,{marginTop:32,gap:8},E.default.createElement(p,{align:"center",font:"heading3Semibold",children:t("seedlessLoadingWalletPrimaryText")}),E.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:t("seedlessLoadingWalletSecondaryText")})))):E.default.createElement(c,{flex:1},E.default.createElement(c,{gap:8,marginY:28},E.default.createElement(p,{align:"center",font:"heading3Semibold",children:t("seedlessConfirmPinPrimaryText")}),E.default.createElement(p,{align:"center",font:"body",color:"accentWarning",children:t("seedlessConfirmPinSecondaryText")})),E.default.createElement(c,{className:D.pinInputContainer},E.default.createElement(I0,{style:z?{color:E0.colors.legacy.accentAlert}:s0?{color:E0.colors.legacy.accentSuccess}:{},className:D.pinInput,value:m,type:"password",inputMode:"numeric",autoComplete:"off",placeholder:"\u2022\u2022\u2022\u2022",maxLength:4,borderRadius:"16px",onChange:H})),z?E.default.createElement(c,{direction:"row",alignItems:"center"},E.default.createElement(c,{marginRight:4},E.default.createElement(H0.XCircle,{size:14,color:"accentAlert"})),E.default.createElement(p,{color:"accentAlert",font:"label",children:t("seedlessConfirmPinError")})):null,E.default.createElement(Z,{theme:"primary",disabled:e!==m||h,className:D.buttonWrapper,onClick:L},t("seedlessConfirmPinButtonText")))};var Ce=()=>{let{t:e}=b(),{pushStep:t}=A(),[o,r]=(0,i0.useState)(""),a=L0(),s=u=>{r(u.target.value)},d=(0,i0.useCallback)(()=>{a.capture("seedlessNewPinContinue"),t(i0.default.createElement(Ko,{pin:o}))},[t,o,a]),m=!Q0(o);return i0.default.createElement(c,{flex:1},i0.default.createElement(c,{gap:8,marginY:28,alignItems:"center",justifyContent:"center"},i0.default.createElement(p,{font:"heading3Semibold",children:e("seedlessCreateAPinPrimaryText")}),i0.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:i0.default.createElement($0,{i18nKey:"seedlessCreateAPinSecondaryText"},"This is used to secure your wallet on all your devices.",i0.default.createElement(p,{font:"body",color:"accentWarning",children:"This cannot be recovered."}))})),i0.default.createElement(c,{className:D.pinInputContainer},i0.default.createElement(I0,{className:D.pinInput,value:o,onChange:s,maxLength:4,placeholder:"\u2022\u2022\u2022\u2022",color:m?E0.colors.legacy.accentAlert:E0.colors.legacy.textPrimary,type:"password",inputMode:"numeric",borderRadius:"16px"})),i0.default.createElement(Z,{className:D.buttonWrapper,theme:"primary",disabled:m,onClick:d},e("seedlessContinueText")))};var jo=()=>{let{t:e}=b(),{pushStep:t,popStep:o}=A(),{data:[r]}=ee(["enable-social-login-v2"]),a=!Ke()&&r,s=()=>{o(),o(),o(),o()};return Z0.default.createElement(c,{flex:1},Z0.default.createElement(c,{flex:1,alignItems:"center",justifyContent:"center",padding:"screen",gap:8},Z0.default.createElement(c,{marginBottom:24},Z0.default.createElement(_o,null)),Z0.default.createElement(p,{align:"center",font:"heading3Semibold",children:e(a?"seedlessCreateNewWalletPrimaryText":"seedlessCreateNewWalletNoBundlePrimaryText")}),Z0.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:e(a?"seedlessCreateNewWalletSecondaryText":"seedlessCreateNewWalletNoBundleSecondaryText")})),Z0.default.createElement(Z,{className:D.buttonWrapper,theme:"primary",onClick:()=>a?t(Z0.default.createElement(Ce,null)):s()},e(a?"seedlessCreateNewWalletButtonText":"seedlessCreateNewWalletNoBundleButtonText")))};var Uo=({type:e})=>{let{t}=b(),[o,r]=(0,I.useState)(""),[a,s]=(0,I.useState)(null),{refetch:d}=pe(),{pushStep:m}=A(),u=L0(),y=async()=>{r("")},l=(0,I.useCallback)(async T=>{if(a)return;s(T);try{await Y0.logIn({type:"email",prompt:"login",provider:T}),u.capture(T==="apple"?"seedlessIntentSignInApple":"seedlessIntentSignInGoogle"),await tt(ae)}catch(B){s(null),D0.captureError(B,"se*dless"),B instanceof Error&&B.cause==="already_connected"?r(t("seedlessAuthAlreadyExistsErrorText")):r(t("seedlessAuthUnknownErrorText"));return}let{data:h,isError:F}=await d();F&&s(null),m(h?e==="create"?I.default.createElement(Se,null):I.default.createElement(Ae,null):e==="import"?I.default.createElement(jo,null):I.default.createElement(Ce,null))},[a,e,m,d,t,u]),g=[{start:I.default.createElement(c,{size:32,borderRadius:"circle",alignItems:"center",justifyContent:"center",backgroundColor:"gray"},I.default.createElement(Et,null)),topLeft:I.default.createElement(p,{font:"bodySemibold",children:a==="apple"?t("seedlessLoggingIn"):t("seedlessEmailOptionsApplePrimaryText")}),bottomLeft:t("seedlessEmailOptionsAppleSecondaryText"),onClick:()=>l("apple")},{start:I.default.createElement(c,{size:32,borderRadius:"circle",alignItems:"center",justifyContent:"center",backgroundColor:"gray"},I.default.createElement(It,null)),topLeft:I.default.createElement(p,{font:"bodySemibold",children:a==="google"?t("seedlessLoggingIn"):t("seedlessEmailOptionsGooglePrimaryText")}),bottomLeft:t("seedlessEmailOptionsGoogleSecondaryText"),onClick:()=>l("google")}],f=I.default.createElement(c,{flex:1},I.default.createElement(c,{flex:1,gap:8,justifyContent:"center",alignItems:"center",padding:"screen"},I.default.createElement(p,{align:"center",font:"heading3Semibold"},t("seedlessLoadingWalletErrorPrimaryText")),I.default.createElement(p,{align:"center",font:"caption",color:"textSecondary"},o)),I.default.createElement(Z,{theme:"secondary",className:D.buttonWrapper,onClick:y},t("seedlessTryAgain")));return I.default.createElement(I.default.Fragment,null,o?f:I.default.createElement(c,{flex:1},I.default.createElement(c,{marginY:28,gap:8},I.default.createElement(p,{align:"center",font:"heading3Semibold",children:t("seedlessEmailOptionsPrimaryText")}),I.default.createElement(p,{align:"center",font:"body",color:"textSecondary",children:t("seedlessEmailOptionsSecondaryText")})),I.default.createElement(me,{rows:g})))};n();i();var a0=S(P()),$o=()=>a0.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:151,height:111,fill:"none"},a0.default.createElement("g",{clipPath:"url(#a)"},a0.default.createElement("path",{fill:"#393939",d:"M136.416 30.435v62.7c0 9.398-7.598 17.025-16.953 17.025H36.868c-9.356 0-16.946-7.627-16.946-17.026V30.435c0-4.735 3.824-8.574 8.538-8.574h1.513s.015.043.015.065l.287 2.855a.538.538 0 0 1-.481.589l.244 2.496h.007a.537.537 0 0 1 .581.481l.287 2.856a.535.535 0 0 1-.48.588l.25 2.504a.534.534 0 0 1 .589.48l.287 2.856a.539.539 0 0 1-.48.588l.25 2.497a.539.539 0 0 1 .589.481l.287 2.855a.544.544 0 0 1-.481.596l.251 2.497a.527.527 0 0 1 .581.48l.05.46 1.342-.108a.54.54 0 0 1 .581.495l2.504-.2a.543.543 0 0 1 .495-.582l2.863-.23c.294-.021.552.201.574.495l2.504-.2a.548.548 0 0 1 .495-.582l2.863-.23c.294-.02.552.202.574.496l2.503-.201a.544.544 0 0 1 .496-.581l2.862-.23a.532.532 0 0 1 .574.495l2.504-.2a.54.54 0 0 1 .495-.582l2.863-.23a.534.534 0 0 1 .581.496l2.497-.201a.54.54 0 0 1 .495-.581l2.862-.23a.538.538 0 0 1 .582.488l2.504-.2a.527.527 0 0 1 .488-.575l2.862-.23a.538.538 0 0 1 .581.488l2.504-.2a.527.527 0 0 1 .488-.574l2.863-.23a.53.53 0 0 1 .581.488l2.504-.201a.538.538 0 0 1 .495-.581l2.856-.23c.294-.021.56.201.58.495l2.505-.2a.543.543 0 0 1 .495-.582l2.855-.23c.301-.014.56.202.581.496l2.504-.201a.543.543 0 0 1 .495-.581l2.856-.23a.54.54 0 0 1 .58.495l2.505-.2a.54.54 0 0 1 .495-.582l2.863-.23a.533.533 0 0 1 .574.496l2.504-.201a.539.539 0 0 1 .495-.581l2.862-.23a.532.532 0 0 1 .574.495l2.504-.2a.54.54 0 0 1 .495-.582l2.863-.23a.543.543 0 0 1 .581.495l2.497-.2a.54.54 0 0 1 .495-.582l2.863-.23a.543.543 0 0 1 .581.489l2.504-.201a.535.535 0 0 1 .251-.502l-.216-2.06a.53.53 0 0 1 .474-.588h.007l-.265-2.497h-.058a.538.538 0 0 1-.538-.48l-.301-2.856a.538.538 0 0 1 .481-.588l-.266-2.497h-.057a.53.53 0 0 1-.531-.48l-.302-2.856a.535.535 0 0 1 .481-.588l-.265-2.497h-.058a.544.544 0 0 1-.538-.481l-.021-.194h2.446c4.714 0 8.545 3.839 8.545 8.574Z"}),a0.default.createElement("path",{fill:"#A89EEB",d:"M101.863 69.27s-6.55-.293-10.611 3.172c-4.06 3.465-4.8 10.052-12.728 10.052S69.857 75.9 65.796 72.442c-4.06-3.458-10.61-3.171-10.61-3.171h46.684-.007Z"}),a0.default.createElement("path",{fill:"#222",d:"m30.433 31.79-.388-3.924a.537.537 0 0 1 .581.481l.287 2.855a.535.535 0 0 1-.48.589ZM31.078 38.22l-.394-3.925a.534.534 0 0 1 .588.48l.287 2.856a.539.539 0 0 1-.48.588ZM31.724 44.648l-.395-3.932a.539.539 0 0 1 .589.48l.287 2.856a.544.544 0 0 1-.481.596ZM34.53 48.472l-2.404.194-.15-1.522a.527.527 0 0 1 .58.481l.05.46 1.342-.108a.54.54 0 0 1 .581.495ZM40.965 47.955l-3.932.316a.543.543 0 0 1 .495-.581l2.863-.23c.294-.022.552.2.574.495ZM47.4 47.438l-3.931.316a.548.548 0 0 1 .495-.581l2.863-.23c.294-.021.552.201.574.495ZM53.836 46.922l-3.931.316a.544.544 0 0 1 .495-.582l2.862-.23a.532.532 0 0 1 .574.496ZM60.279 46.405l-3.939.316a.54.54 0 0 1 .495-.581l2.863-.23a.534.534 0 0 1 .58.495ZM66.715 45.882l-3.94.322a.54.54 0 0 1 .496-.58l2.862-.23a.538.538 0 0 1 .582.488ZM73.15 45.365l-3.931.316a.527.527 0 0 1 .487-.574l2.863-.23a.538.538 0 0 1 .581.488ZM79.586 44.849l-3.932.316a.527.527 0 0 1 .488-.575l2.863-.23a.53.53 0 0 1 .58.489ZM86.022 44.332l-3.932.316a.538.538 0 0 1 .495-.582l2.855-.23c.295-.02.56.202.582.496ZM92.457 43.816l-3.931.315a.543.543 0 0 1 .495-.58l2.855-.23c.301-.015.56.2.581.495ZM98.893 43.299l-3.932.315a.543.543 0 0 1 .495-.58l2.856-.23a.54.54 0 0 1 .58.495ZM105.329 42.782l-3.932.316a.54.54 0 0 1 .495-.581l2.863-.23a.533.533 0 0 1 .574.495ZM111.764 42.266l-3.932.315a.54.54 0 0 1 .496-.58l2.862-.23a.532.532 0 0 1 .574.495ZM118.207 41.749l-3.939.316a.54.54 0 0 1 .495-.581l2.863-.23a.543.543 0 0 1 .581.495ZM124.643 41.225l-3.939.323a.54.54 0 0 1 .495-.581l2.862-.23a.543.543 0 0 1 .582.488ZM29.263 20.125l-.05-.531a9.412 9.412 0 0 1 .272-3.372.52.52 0 0 1 .402.624 8.988 8.988 0 0 0-.143 2.69.53.53 0 0 1-.48.589ZM29.988 21.925l.287 2.856a.538.538 0 0 1-.481.588l-.352-3.508-.043-.416a.528.528 0 0 1 .574.416.21.21 0 0 1 .015.064ZM33.116 11.027c.18.23.143.56-.093.747a9.014 9.014 0 0 0-1.823 1.98.534.534 0 0 1-.452.237c-.1 0-.2-.03-.287-.087a9.586 9.586 0 0 1 2.655-2.877ZM37.521 9.377l1.693-.18a.55.55 0 0 1-.48.596l-1.156.122c-.53.05-1.061.158-1.578.309a.536.536 0 0 1-.66-.352 9.46 9.46 0 0 1 2.181-.495ZM41.711 8.932l3.925-.409a.539.539 0 0 1-.481.589l-2.856.301s-.035.007-.057.007a.542.542 0 0 1-.53-.488ZM48.132 8.258l3.925-.416a.545.545 0 0 1-.48.595l-2.856.302h-.05c-.273 0-.51-.201-.539-.481ZM54.554 7.584l3.924-.417a.539.539 0 0 1-.48.589l-2.849.301s-.043.007-.057.007a.543.543 0 0 1-.538-.48ZM60.975 6.902l3.925-.409a.534.534 0 0 1-.481.588l-2.848.302h-.058c-.273 0-.51-.201-.538-.48ZM67.396 6.228l3.925-.416a.544.544 0 0 1-.48.595l-2.85.301h-.057a.543.543 0 0 1-.538-.48ZM73.817 5.553l3.925-.416a.534.534 0 0 1-.48.589l-2.849.3s-.043.008-.057.008a.542.542 0 0 1-.538-.48ZM80.239 4.871l3.924-.409a.529.529 0 0 1-.48.589l-2.849.301h-.057c-.273 0-.51-.2-.538-.48ZM86.66 4.197l3.925-.416a.528.528 0 0 1-.474.588l-2.855.301s-.043.008-.058.008a.543.543 0 0 1-.538-.481ZM93.081 3.515l3.925-.409a.528.528 0 0 1-.474.589l-2.855.301h-.058c-.272 0-.509-.2-.538-.48ZM99.503 2.84l3.924-.415a.539.539 0 0 1-.473.595l-2.856.302h-.057a.543.543 0 0 1-.538-.481ZM105.924 2.167l3.924-.417a.528.528 0 0 1-.473.589l-2.856.301s-.043.007-.057.007a.542.542 0 0 1-.538-.48ZM116.277 1.263a.523.523 0 0 1-.553.495c-.416-.015-.839 0-1.255.043l-1.528.165h-.058a.538.538 0 0 1-.538-.48l2.067-.216a9.007 9.007 0 0 1 1.865-.007ZM118.716 1.83a9.333 9.333 0 0 1 3.279 2.138.543.543 0 0 1-.387.165.59.59 0 0 1-.359-.137 8.683 8.683 0 0 0-2.26-1.47.549.549 0 0 1-.273-.696ZM124.621 9.693a.528.528 0 0 1-.531-.46 8.928 8.928 0 0 0-.803-2.568.517.517 0 0 1 .229-.703 9.236 9.236 0 0 1 1.155 3.609l.015.115s-.043.007-.065.007ZM125.303 16.114a.54.54 0 0 1-.531-.48l-.302-2.856a.54.54 0 0 1 .481-.588l.409 3.924h-.057ZM126.042 22.535h-.058a.544.544 0 0 1-.538-.48l-.021-.194-.28-2.662a.538.538 0 0 1 .481-.588l.344 3.25.072.674ZM126.659 28.957a.53.53 0 0 1-.531-.481l-.302-2.855a.535.535 0 0 1 .481-.589l.409 3.925h-.057ZM127.34 35.378a.538.538 0 0 1-.538-.48l-.301-2.856a.538.538 0 0 1 .48-.588l.417 3.924h-.058ZM127.398 40.522l-.216-2.059a.53.53 0 0 1 .474-.588h.007l.323 3.085-.839.065a.535.535 0 0 1 .251-.503Z"}),a0.default.createElement("path",{fill:"#393939",d:"m33.948 47.977-1.342.107-.05-.459a.527.527 0 0 0-.58-.48.539.539 0 0 0-.482.588l.1.99c.03.273.26.48.539.48h.043l1.865-.15a.539.539 0 1 0-.086-1.076h-.007Zm6.443-.517-2.863.23a.54.54 0 0 0 .043 1.076h.043l2.863-.23a.538.538 0 0 0 .488-.58.537.537 0 0 0-.574-.496Zm6.436-.516-2.863.23a.548.548 0 0 0-.495.58.54.54 0 0 0 .538.495h.043l2.863-.23a.538.538 0 0 0 .488-.58.537.537 0 0 0-.575-.495Zm6.435-.517-2.862.23a.544.544 0 0 0-.495.58.54.54 0 0 0 .538.496h.043l2.862-.23a.538.538 0 0 0 .488-.581.532.532 0 0 0-.574-.495Zm6.436-.517-2.863.23a.54.54 0 0 0-.495.581c.029.28.258.495.538.495.015 0 .029 0 .043-.007l2.863-.23a.537.537 0 1 0-.086-1.069Zm6.435-.516-2.862.23a.54.54 0 0 0-.495.58c.028.28.258.496.538.496h.043l2.863-.23a.537.537 0 0 0 .495-.574.538.538 0 0 0-.582-.495v-.007Zm6.436-.517-2.863.23a.527.527 0 0 0-.487.574.532.532 0 0 0 .53.495h.044l2.862-.23a.54.54 0 0 0 .495-.58.538.538 0 0 0-.58-.489Zm6.436-.516-2.863.23a.527.527 0 0 0-.488.573.532.532 0 0 0 .531.495h.043l2.863-.23a.54.54 0 0 0 .495-.58.53.53 0 0 0-.581-.488Zm6.435-.524-2.855.23a.538.538 0 0 0 .036 1.076h.05l2.855-.23a.539.539 0 1 0-.086-1.076Zm-53.522-2.64a.539.539 0 0 0-.589-.481.54.54 0 0 0-.488.588l.287 2.856c.03.28.266.488.539.488h.057a.545.545 0 0 0 .48-.596l-.286-2.855Zm59.958 2.123-2.856.23a.54.54 0 0 0 .043 1.076h.044l2.855-.23a.543.543 0 0 0 .495-.58.545.545 0 0 0-.58-.496Zm6.436-.516-2.856.23a.543.543 0 0 0-.495.58.54.54 0 0 0 .538.496h.043l2.856-.23a.54.54 0 1 0-.086-1.076Zm6.443-.517-2.863.23a.54.54 0 0 0 .043 1.076h.043l2.863-.23a.537.537 0 0 0 .487-.58.531.531 0 0 0-.573-.496Zm6.435-.516-2.863.23a.54.54 0 0 0 .044 1.075c.014 0 .028 0 .043-.007l2.862-.23a.527.527 0 0 0 .488-.573.532.532 0 0 0-.574-.495Zm6.436-.517-2.863.23a.54.54 0 0 0 .043 1.076h.043l2.863-.23a.538.538 0 0 0 .495-.574.543.543 0 0 0-.581-.495v-.007Zm6.435-.517-2.862.23a.537.537 0 0 0-.495.574.538.538 0 0 0 .538.495h.043l2.863-.23a.54.54 0 0 0 .495-.58.543.543 0 0 0-.582-.489Zm4.19-2.382a.533.533 0 0 0-.588-.48h-.007a.53.53 0 0 0-.474.588l.216 2.06a.535.535 0 0 0-.251.502.531.531 0 0 0 .53.495h.044l.308-.022a.542.542 0 0 0 .488-.595l-.266-2.547Zm-96.98-3.58a.534.534 0 0 0-.587-.48.539.539 0 0 0-.481.588l.287 2.855a.53.53 0 0 0 .53.481h.058a.539.539 0 0 0 .48-.588l-.286-2.856Zm96.299-2.848a.538.538 0 0 0-1.069.115l.301 2.855c.029.28.266.481.538.481h.058a.535.535 0 0 0 .473-.595l-.301-2.856Zm-96.944-3.58a.537.537 0 0 0-.58-.48h-.008a.539.539 0 0 0-.48.587l.286 2.856c.029.28.258.488.538.488h.05a.535.535 0 0 0 .481-.588l-.287-2.856v-.007Zm96.269-2.841a.533.533 0 0 0-.588-.474.535.535 0 0 0-.481.589l.302 2.855a.53.53 0 0 0 .531.48h.057a.55.55 0 0 0 .481-.595l-.302-2.855Zm-96.907-3.58s0-.043-.015-.065a.528.528 0 0 0-.574-.416.543.543 0 0 0-.473.416c-.014.05-.022.115-.014.172l.287 2.856c.028.28.265.48.538.48h.057a.538.538 0 0 0 .48-.588l-.286-2.855Zm96.527-.065-.294-2.777a.54.54 0 0 0-.595-.473.538.538 0 0 0-.481.588l.28 2.662.021.194c.029.272.266.48.538.48h.058a.55.55 0 0 0 .48-.595v-.079h-.007Zm-97.03-5.64h-.014a.539.539 0 0 0-.638.41 10.159 10.159 0 0 0-.208 2.03c0 .33.021.66.05.99.028.28.265.488.538.488.014 0 .036 0 .05-.007a.53.53 0 0 0 .48-.588 8.995 8.995 0 0 1 .143-2.69.52.52 0 0 0-.4-.625v-.007Zm96.054-3.558a.533.533 0 0 0-.588-.473.54.54 0 0 0-.481.588l.302 2.855a.54.54 0 0 0 .531.481h.057a.54.54 0 0 0 .481-.595l-.302-2.856Zm-92.423-1.636-.007-.007a.53.53 0 0 0-.754-.086 9.875 9.875 0 0 0-2.052 2.217.544.544 0 0 0 .151.753h.007a.518.518 0 0 0 .287.087c.18 0 .345-.08.452-.237a9.014 9.014 0 0 1 1.823-1.98.529.529 0 0 0 .093-.747Zm5.503-2.31-1.155.122a10.53 10.53 0 0 0-1.765.352.538.538 0 0 0-.366.667v.014a.536.536 0 0 0 .66.352 8.524 8.524 0 0 1 1.578-.309l1.155-.122a.55.55 0 0 0 .481-.595.546.546 0 0 0-.595-.48h.007Zm85.629-2.525a.53.53 0 0 0-.717-.244c-.008 0-.008.007-.015.014a.517.517 0 0 0-.229.703c.394.81.667 1.672.803 2.569.036.272.266.459.531.459.022 0 .043 0 .065-.007h.014a.536.536 0 0 0 .452-.61 9.658 9.658 0 0 0-.904-2.884Zm-79.2 1.85-2.856.302a.539.539 0 0 0 .05 1.076c.022 0 .043-.007.057-.007l2.856-.301a.539.539 0 0 0 .48-.589.534.534 0 0 0-.588-.48Zm6.42-.674-2.855.302a.539.539 0 0 0-.48.588c.028.28.265.48.537.48h.05l2.856-.3a.545.545 0 0 0 .48-.596.537.537 0 0 0-.587-.474Zm6.422-.681-2.856.301a.541.541 0 0 0-.48.596c.028.272.265.48.538.48.014 0 .036 0 .057-.007l2.848-.301a.539.539 0 0 0 .481-.589.543.543 0 0 0-.588-.48Zm6.421-.675-2.855.302a.539.539 0 0 0-.481.588c.029.28.265.48.538.48h.057l2.849-.3a.534.534 0 0 0 .48-.589.543.543 0 0 0-.588-.48Zm6.421-.681-2.855.301a.55.55 0 0 0-.48.596c.028.272.265.48.537.48h.058l2.848-.301a.544.544 0 0 0 .48-.596.544.544 0 0 0-.588-.48Zm6.422-.675-2.856.302a.539.539 0 0 0 .058 1.076c.014 0 .035-.007.057-.007l2.848-.302a.534.534 0 0 0 .481-.588.539.539 0 0 0-.588-.48Zm6.421-.674-2.856.301a.539.539 0 0 0-.48.589c.028.28.265.48.538.48h.057l2.849-.301a.529.529 0 0 0 .48-.588.534.534 0 0 0-.588-.481Zm6.421-.682-2.855.302a.55.55 0 0 0-.48.595c.028.273.265.48.537.48.015 0 .036 0 .058-.006l2.855-.302a.528.528 0 0 0 .474-.588.539.539 0 0 0-.589-.48Zm31.97-.107a9.685 9.685 0 0 0-2.532-1.65.535.535 0 0 0-.71.272l-.008.014a.549.549 0 0 0 .273.696 8.684 8.684 0 0 1 2.26 1.471.59.59 0 0 0 .359.137c.143 0 .28-.058.387-.165l.014-.015a.535.535 0 0 0-.043-.76Zm-25.548-.567-2.856.301a.537.537 0 0 0 .057 1.069h.058l2.855-.301a.528.528 0 0 0 .474-.588.534.534 0 0 0-.588-.481Zm6.421-.682-2.856.302a.55.55 0 0 0-.48.595c.028.273.265.48.538.48h.057l2.856-.3a.539.539 0 0 0 .473-.596.543.543 0 0 0-.588-.48Zm6.421-.674-2.855.301a.539.539 0 0 0 .057 1.076c.014 0 .036-.007.057-.007l2.856-.301a.53.53 0 0 0 .474-.588.54.54 0 0 0-.589-.481Zm6.5-.588c-.466-.015-.939 0-1.406.05l-1.528.165a.54.54 0 0 0-.481.588c.029.28.266.48.538.48h.058l1.528-.164a9.182 9.182 0 0 1 1.256-.043c.287.021.53-.201.552-.495V1.24a.538.538 0 0 0-.517-.56Z"}),a0.default.createElement("path",{fill:"#393939",fillRule:"evenodd",d:"M78.416 16.947a.68.68 0 0 1 .74.595l.566 5.338 5.338-.56a.68.68 0 0 1 .74.596.68.68 0 0 1-.596.739l-5.338.56.56 5.337a.68.68 0 0 1-.596.74.68.68 0 0 1-.74-.596l-.566-5.338-5.338.56a.68.68 0 0 1-.739-.596.68.68 0 0 1 .596-.739l5.338-.56-.56-5.337a.68.68 0 0 1 .596-.74Z",clipRule:"evenodd"}),a0.default.createElement("path",{fill:"#60BC8E",d:"M29.4 37.523a9.341 9.341 0 0 1 9.958-8.602l79.839 5.546c5.144.358 9.026 4.814 8.667 9.958l-1.442 20.75-98.486-8.603 1.463-19.049Z"}),a0.default.createElement("path",{fill:"#A89EEB",d:"M27.814 58.416c0-5.159 4.183-9.334 9.334-9.334h83.204c5.159 0 9.334 4.182 9.334 9.334v11.472H27.814V58.416Z"}),a0.default.createElement("path",{fill:"#FFFFC9",d:"M143.82 91.146c-4.426 3.128-5.208 4.98-4.182 10.231.086.452-.574.732-.84.359-3.099-4.362-4.972-5.072-10.288-4.025-.481.093-.718-.46-.316-.739 4.42-3.128 5.202-4.98 4.176-10.23-.086-.453.574-.733.839-.36 3.1 4.362 4.972 5.073 10.289 4.025.48-.093.717.46.315.74h.007ZM10.229 4.678l2.073 5.489a3.077 3.077 0 0 0 2.418 1.951l5.819.875c.545.086.631.84.107 1.034L15.143 16.1a3.075 3.075 0 0 0-1.958 2.41l-.883 5.805c-.086.545-.84.624-1.04.108l-2.074-5.489a3.077 3.077 0 0 0-2.417-1.951l-5.819-.876c-.545-.086-.631-.84-.108-1.033l5.503-2.073a3.075 3.075 0 0 0 1.959-2.411l.882-5.804c.087-.546.84-.625 1.04-.108ZM149.065 100.014a1.434 1.434 0 1 0 .001-2.869 1.434 1.434 0 0 0-.001 2.869ZM20.28 29.43a1.794 1.794 0 1 0 0-3.587 1.794 1.794 0 0 0 0 3.588ZM26.974 3.422c-2.224.753-2.783 1.42-3.006 3.73-.021.202-.33.23-.394.044-.747-2.195-1.435-2.726-3.774-2.949-.208-.021-.244-.28-.043-.344 2.224-.754 2.776-1.42 3.006-3.731.021-.194.33-.23.394-.043.747 2.195 1.435 2.726 3.774 2.949.208.021.244.28.043.344Z"}),a0.default.createElement("path",{fill:"#FFFCF7",d:"M38.167 64.141a5.022 5.022 0 1 0 0-10.044 5.022 5.022 0 0 0 0 10.044ZM88.748 54.097h33.003a3.944 3.944 0 0 1 3.946 3.946 3.944 3.944 0 0 1-3.946 3.946H88.748a3.944 3.944 0 0 1-3.946-3.946 3.944 3.944 0 0 1 3.946-3.946ZM39.365 43.78a5.072 5.072 0 1 0 0-10.145 5.072 5.072 0 0 0 0 10.145ZM88.325 37.265l31.188 2.167a3.95 3.95 0 0 1 3.666 4.211 3.95 3.95 0 0 1-4.212 3.666l-31.188-2.166a3.95 3.95 0 0 1-3.666-4.212 3.95 3.95 0 0 1 4.212-3.666Z",opacity:.4})),a0.default.createElement("defs",null,a0.default.createElement("clipPath",{id:"a"},a0.default.createElement("path",{fill:"#fff",d:"M.5 0h150v110.159H.5z"}))));n();i();var Te=S(P()),Yo=()=>Te.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:25,fill:"none"},Te.default.createElement("path",{fill:"#FF7243",d:"M21.948 7.76c-.175-1.2-.722-2.336-1.607-3.193a5.172 5.172 0 0 0-.9-.7c-2.016-1.234-4.597-.802-6.25.922l-.926 1.051a.348.348 0 0 1-.528 0l-.925-1.05c-1.656-1.728-4.234-2.16-6.253-.925a5.17 5.17 0 0 0-.9.7 5.552 5.552 0 0 0-1.606 3.194c-.238 1.637.342 3.29 1.46 4.466l8.37 8.822a.348.348 0 0 0 .515-.004l8.089-8.818c1.116-1.176 1.698-2.83 1.46-4.466Z"}),Te.default.createElement("path",{fill:"#FFFDF8",fillRule:"evenodd",d:"M13.366 7.087a.35.35 0 0 0-.217.445l.62 1.797-1.796.62a.35.35 0 0 0-.217.446l.507 1.466a.35.35 0 0 0 .445.216l1.797-.62.62 1.796a.35.35 0 0 0 .445.217l1.466-.506a.35.35 0 0 0 .217-.446l-.62-1.796 1.796-.621a.35.35 0 0 0 .217-.445l-.507-1.466a.35.35 0 0 0-.445-.217l-1.797.621-.62-1.797a.35.35 0 0 0-.445-.216l-1.466.506Z",clipRule:"evenodd"}));n();i();var P0=S(P()),qo=()=>P0.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:23,fill:"none"},P0.default.createElement("g",{clipPath:"url(#a)"},P0.default.createElement("path",{fill:"#AB9FF2",d:"M7.945 16.002c-.726-.484-1.675-.59-2.73-.307-.469.13-.926.319-1.383.566-.129-.2-.258-.424-.398-.648-2.32-4.023-1.711-7.668-.61-9.956.094-.19.188-.378.293-.543.094-.189.2-.366.317-.52l-.106-.105a11.951 11.951 0 0 1-.68-.802 6.294 6.294 0 0 0-.351.507c-.106.177-.211.354-.305.554-1.36 2.536-2.273 6.795.434 11.454.14.248.281.484.422.708-1.102.85-2.098 2.053-2.79 3.456a.599.599 0 0 0 .*********** 0 0 0 .785-.271c.598-1.203 1.453-2.264 2.414-3.008 1.7 2.218 3.059 2.43 3.785 2.313.844-.13 1.477-.767 1.665-1.64a2.417 2.417 0 0 0-1.032-2.548ZM7.84 18.29c-.035.154-.188.65-.703.732-.586.082-1.512-.366-2.625-1.793.34-.165.668-.307 1.008-.39.503-.141 1.218-.224 1.769.142.54.366.633.92.55 1.31Z"}),P0.default.createElement("path",{fill:"#A4A3A0",d:"M7.383 6.046a4.282 4.282 0 0 0-.281-.744c-.399-.814-1.055-1.734-1.875-2.607C4.242 1.634 2.53.16 1.324.16 1.031.16.762.254.551.454c-.95.897.293 2.926 1.441 4.294.164.2.328.377.48.543.118.13.235.247.352.365.703.708 1.442 1.298 2.11 1.687.351.2.656.33.925.401L20.812 22.16 7.382 6.046ZM4.37 3.498c.973 1.05 1.512 1.934 1.758 2.524.129.283.187.495.187.625-.129-.012-.34-.082-.609-.224-.55-.283-1.348-.85-2.273-1.828l-.106-.107a11.951 11.951 0 0 1-.68-.802C1.723 2.53 1.394 1.657 1.383 1.34c.386.035 1.558.613 2.988 2.159Z"}),P0.default.createElement("path",{fill:"#AB9FF2",d:"M19.547 9.454c-1.57 0-3.34-.825-5.297-2.465-5.461-4.577-8.93-3.68-9.797-2.95a.575.575 0 0 1-.82-.07.584.584 0 0 1 .07-.826C5.144 1.928 9.153 1.185 15 6.093c2.156 1.804 4.055 2.512 5.484 2.04 1.313-.436 2.075-1.793 2.356-2.914a.588.588 0 1 1 1.137.295c-.352 1.428-1.36 3.174-3.13 3.752-.41.141-.855.2-1.3.2v-.012Z"}),P0.default.createElement("path",{fill:"#FFFFC4",d:"m7.833 8.3.293.957a.522.522 0 0 0 .387.358l.973.212c.092.02.097.15.008.177l-.951.295a.523.523 0 0 0-.356.39l-.21.98c-.02.092-.149.098-.177.007l-.293-.957a.522.522 0 0 0-.388-.358l-.972-.212c-.092-.02-.097-.15-.008-.177l.951-.295a.523.523 0 0 0 .356-.39l.21-.98c.02-.092.149-.098.177-.007ZM18.349 10.552l1.587.74c.3.14.653.107.921-.088l1.419-1.03c.134-.098.308.047.24.198l-.737 1.598a.927.927 0 0 0 .088.927l1.024 1.428c.097.135-.047.31-.197.24l-1.587-.74a.91.91 0 0 0-.922.089l-1.418 1.03c-.134.098-.309-.048-.24-.199l.736-1.597a.927.927 0 0 0-.088-.927l-1.024-1.428c-.096-.135.048-.311.198-.241Z"})),P0.default.createElement("defs",null,P0.default.createElement("clipPath",{id:"a"},P0.default.createElement("path",{fill:"#fff",d:"M0 .16h24v22H0z"}))));n();i();var v0=S(P()),Xo=()=>v0.default.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:24,height:25,fill:"none"},v0.default.createElement("g",{clipPath:"url(#a)"},v0.default.createElement("rect",{width:19.765,height:12.988,x:2.225,y:11.171,fill:"#2EC08B",rx:1.694}),v0.default.createElement("path",{fill:"#FFFFC4",fillRule:"evenodd",d:"M12.107 3.665a4.01 4.01 0 0 0-4.01 4.01v.877a.501.501 0 0 1-.5.501H6.093a.501.501 0 0 1-.501-.5v-.878a6.516 6.516 0 0 1 13.031 0v.877a.501.501 0 0 1-.501.501h-1.504a.501.501 0 0 1-.5-.5v-.878a4.01 4.01 0 0 0-4.01-4.01Z",clipRule:"evenodd"}),v0.default.createElement("path",{fill:"#1C1C1C",fillRule:"evenodd",d:"M13.73 15.407c0 .51-.225.968-.582 1.278-.152.133-.265.314-.265.515v.02c0 .**************.203l1.265 3.287a.565.565 0 0 1-.527.768h-3.245a.565.565 0 0 1-.527-.768l1.265-3.286a.565.565 0 0 0 .037-.203v-.02c0-.202-.112-.383-.264-.516a1.694 1.694 0 1 1 2.806-1.278Z",clipRule:"evenodd"}),v0.default.createElement("path",{fill:"#FFFDF8",d:"m.272 14.121 1.8.84c.*********** 1.044-.1l1.61-1.169c.15-.************.225l-.834 1.812c-.158.343-.12.745.1 1.052l1.161 1.62c.11.152-.054.352-.224.273l-1.8-.84c-.34-.16-.74-.12-1.045.1l-1.609 1.17c-.151.11-.35-.055-.271-.226l.835-1.812c.157-.343.12-.745-.1-1.052l-1.161-1.62c-.11-.152.054-.352.224-.273Z"})),v0.default.createElement("defs",null,v0.default.createElement("clipPath",{id:"a"},v0.default.createElement("path",{fill:"#fff",d:"M0 .16h24v24H0z"}))));var Pe=({type:e})=>{let{t}=b(),{setOnboardingType:o,pushStep:r}=A(),a=(0,q.useCallback)(()=>{o("seedless"),r(q.default.createElement(Uo,{type:e}))},[r,o,e]),s=(0,q.useCallback)(()=>{o("create"),r(q.default.createElement(m0,null))},[r,o]),d=q.default.createElement(c,{width:"100%",gap:8,alignItems:"center"},q.default.createElement(Z,{theme:"primary",onClick:a},t("seedlessEmailOptionsButtonText")),e==="create"&&q.default.createElement("button",{"data-testid":"create-manual-seed-phrase",className:D.textButton,onClick:s},q.default.createElement(p,{align:"center",font:"captionSemibold",children:t("seedlessCreateSeed"),color:"current"})));return q.default.createElement(q.default.Fragment,null,q.default.createElement(Go,{image:q.default.createElement(c,{alignItems:"center",justifyContent:"center",marginTop:"screen",marginBottom:32},q.default.createElement($o,null)),title:t("seedlessAddAWalletPrimaryText"),description:t("seedlessAddAWalletSecondaryText"),features:[{icon:q.default.createElement(qo,null),title:t("seedlessValueProp1PrimaryText"),description:t("seedlessValueProp1SecondaryText")},{icon:q.default.createElement(Xo,null),title:t("seedlessValueProp2PrimaryText"),description:t("seedlessValueProp2SecondaryText")},{icon:q.default.createElement(Yo,null),title:t("seedlessValueProp3PrimaryText"),description:t("seedlessValueProp3SecondaryText")}]}),d)};n();i();var A0=S(P());n();i();var Le=S(P());var Qo=({onClick:e,disabled:t})=>{let{t:o}=b();return Le.default.createElement(wt,{topLeft:{text:o("seedlessAddSeedlessWalletPrimaryText"),font:"bodyMedium"},bottomLeft:{text:o("seedlessAddSeedlessWalletSecondaryText")},start:Le.default.createElement(le,{backgroundColor:"borderPrimary",color:"textPrimary",icon:"AtSign",shape:"circle",size:32}),onClick:e,disabled:t})};n();i();var ve=S(P());var Jo=()=>{let{pushStep:e,setLedgerAccounts:t}=A(),{hardwareStepStack:o,pushStep:r,currentStep:a,setOnConnectHardwareAccounts:s,setOnConnectHardwareDone:d,setExistingAccounts:m}=he();return Qt(()=>{m({data:[],isFetched:!0,isError:!1}),s(async u=>t(u)),d(()=>{e(ve.default.createElement(m0,null)),v.capture("onboardingImportWalletLedger")}),r(ve.default.createElement(To,null))},o.length===0),ve.default.createElement(rn,null,a())},rn=x(Ao)`
  padding: 0;
  padding-top: 20px;
`;n();i();var we=S(P());var Ro=()=>{let{t:e}=b(),{pushStep:t,setPrivateKey:o}=A(),r=ut({importPrivateKeyCallback:d=>{o(d),t(we.default.createElement(m0,null)),v.capture("onboardingImportWalletPrivateKey")},existingAccounts:[],analytics:v}),{formOnSubmitHandler:a,canSubmit:s}=r;return we.default.createElement(x0,{onSubmit:a,title:e("onboardingImportPrivateKeyPageTitle"),subtitle:e("onboardingImportPrivateKeyPageSubtitle"),buttonTheme:s?"primary":"default",buttonText:e("addAccountImportAccountActionButtonImport"),buttonDisabled:!s},we.default.createElement(so,{...r}))};var er=()=>{let{t:e}=b(),{pushStep:t,setOnboardingType:o}=A(),r=()=>{o("seedless"),t(A0.default.createElement(Pe,{type:"import"}))},a=()=>{o("importSeed"),t(A0.default.createElement(J0,null))},s=()=>{o("importPrivateKey"),t(A0.default.createElement(Ro,null))},d=()=>{o("connectHardware"),t(A0.default.createElement(Jo,null))};return A0.default.createElement(Oe,{title:e("onboardingImportOptionsPageTitle"),subtitle:e("onboardingImportOptionsPageSubtitle"),children:A0.default.createElement(c,{width:"100%",gap:8},A0.default.createElement(Qo,{onClick:r}),A0.default.createElement(co,{onClick:a}),A0.default.createElement(ao,{onClick:s}),A0.default.createElement(lo,{onClick:d}))})};var tr=()=>{let{t:e}=b(),{setOnboardingType:t,pushStep:o}=A(),{data:[r]}=ee(["enable-social-login-v2"]),a=!uo()&&r,s=et(),d=async()=>{await s.logOut(),v.capture("onboardingSplashScreenCreateNewWalletClickedByUser"),a?(v.capture("onboardingSeedlessLogin"),t("seedless"),o(y0.default.createElement(Pe,{type:"create"}))):(t("create"),o(y0.default.createElement(m0,null)))},m=()=>{v.capture("onboardingSplashScreenImportWalletClickedByUser"),o(y0.default.createElement(er,null))};return y0.default.createElement("div",{className:_0.wrapper},y0.default.createElement("div",{className:_0.upperSection},y0.default.createElement("div",{className:_0.header},y0.default.createElement(Kt,{width:256}),y0.default.createElement(p,{font:"title1",children:e("onboardingWelcomeDescription"),color:"textSecondary",className:_0.subtitle}))),y0.default.createElement("div",{className:_0.footer},y0.default.createElement(Z,{className:_0.button,theme:"primary",onClick:d},e("onboardingWelcomeCreateWallet")),y0.default.createElement(Z,{className:_0.button,onClick:m},e("onboardingWelcomeAlreadyHaveWallet"))))},_0={button:h0({width:"100%",flex:"grow"}),footer:h0({alignItems:"center",display:"column",rowGap:10,width:"100%"}),header:h0({alignItems:"center",display:"column"}),subtitle:h0({marginTop:24,textAlign:"center"}),upperSection:h0({display:"column",flex:1,justifyContent:"center"}),wrapper:h0({display:"column",flex:1})};var Ne=.2,We=0,Ge=1,or=()=>{let{onboardingType:e,onboardingStack:t,setOnboardingStack:o,prevOnboardingStack:r,currentStep:a}=A(),s=e==="restore",d=e==="append",m=t.length;(0,b0.useEffect)(()=>{if(t.length===0)switch(e){case"restore":v.capture("restoreWalletOpen"),o([b0.default.createElement(J0,null)]);break;case"append":v.capture("multiMnemonicOpen"),o([b0.default.createElement(J0,null)]);break;default:o([b0.default.createElement(tr,null)])}},[e,o,t]);let u=m>r.length,y=m>1||s||d,l=r.length===0,g=l?Ge:We,f=`${m}_${r.length}`,T={initial:{opacity:g},animate:{opacity:Ge},exit:{opacity:We},transition:{delay:Ne,duration:Ne}},h={initial:{x:l?0:u?150:-150,opacity:g},animate:{x:0,opacity:Ge},exit:{opacity:We},transition:{duration:Ne}};return b0.default.createElement(ko,null,y?b0.default.createElement(z0.div,{...T},b0.default.createElement(Wo,null)):b0.default.createElement("div",{className:De.placeholderNav}),b0.default.createElement(ue,{mode:"wait"},b0.default.createElement(z0.div,{className:De.content,key:f,...h},a)))};gt();Ue.init({provider:fo});vt();var nn=document.getElementById("root"),an=(0,rr.createRoot)(nn);an.render(R.default.createElement(Ot,null,R.default.createElement(Vt,{theme:Jt},R.default.createElement(ho,{backgroundColor:"#E2DFFE"}),R.default.createElement(Pt,null,R.default.createElement(Ye,null,R.default.createElement(_t,null,R.default.createElement(go,null,R.default.createElement(ft,null,R.default.createElement(Re,{authRepository:Y0},R.default.createElement(ot,{userRepository:po,claimUsernameSigner:mo},R.default.createElement(Dt,{seedlessRepository:yo},R.default.createElement(bo,null),R.default.createElement(Xt,null,R.default.createElement(Po,null,R.default.createElement(or,null))))))))))))));
//# sourceMappingURL=Onboarding.js.map
