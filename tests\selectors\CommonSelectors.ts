export const CommonSelectors = {
  notificationPopup: 'iframe[src*="notification.html"]',
  confirmButton: 'button[type="button"][data-testid="primary-button"]',

  // Left header
  exploreLink: 'a.chakra-link[href="/token-list?category=all_tokens_view"]',
  moonLink: 'a.chakra-link[href="/pump-to-moon"]',
  topWalletsLink: 'a.chakra-link[href="/top-wallets"]',
  copyTradingLink: 'a.chakra-link[href="/portfolio?type=copy-trading"]',
  deepSignalsLink: 'a.chakra-link[href="/deep-signals"]',
  followingLink: 'a.chakra-link[href="/follow"]',
  holdingLink: 'a.chakra-link[href="/portfolio?type=assets"]',
  referralLink: 'a.chakra-link[href="/referral-tracking"]',

  // right header
  searchButton: 'button.chakra-button:has(p:has-text("Search"))',
  favoritesButton: 'button.chakra-button:has(svg.chakra-icon.css-kua5no)',
  themeToggleButton: 'button.chakra-button:has(svg.chakra-icon.css-1xop50t)',
  notificationsButton: 'button.chakra-button:has(svg.chakra-icon.css-1qhru4j)',
  walletButton: 'div.css-79elbk button.chakra-button',

  // wallet menu
  walletsItem: 'text="Wallets"',
  depositItem: 'text="Deposit"',
  withdrawItem: 'text="Withdraw"',
  reclaimSolItem: 'text="Reclaim SOL"',
  myPositionsItem: 'text="My positions"',
  openOrdersItem: 'text="Open orders"',
  copyTradingItem: 'text="Copy trading"',
  autoSellItem: 'text="Auto sell"',
  preferencesItem: 'text="Preferences"',
  disconnectItem: 'text="Disconnect"',
};