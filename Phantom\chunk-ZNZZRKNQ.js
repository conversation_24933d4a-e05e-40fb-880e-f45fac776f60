import{b as et}from"./chunk-EGXLQXDH.js";import{a as Kt}from"./chunk-H3FFS4GT.js";import{m as tt}from"./chunk-56SJOU6P.js";import{a as Z}from"./chunk-7X4NV6OJ.js";import{c as B,f as Ut,h as g,i as _,n as v}from"./chunk-3KENBVE7.js";var U=B((qt,rt)=>{g();v();rt.exports={MODE_NUMBER:1,MODE_ALPHA_NUM:2,MODE_8BIT_BYTE:4,MODE_KANJI:8}});var ot=B((ee,it)=>{g();v();var Ft=U();function nt(e){this.mode=Ft.MODE_8BIT_BYTE,this.data=e}nt.prototype={getLength:function(e){return this.data.length},write:function(e){for(var t=0;t<this.data.length;t++)e.put(this.data.charCodeAt(t),8)}};it.exports=nt});var K=B((ie,at)=>{g();v();at.exports={L:1,M:0,Q:3,H:2}});var st=B((ue,ut)=>{g();v();var I=K();function D(e,t){this.totalCount=e,this.dataCount=t}D.RS_BLOCK_TABLE=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]];D.getRSBlocks=function(e,t){var n=D.getRsBlockTable(e,t);if(n==null)throw new Error("bad rs block @ typeNumber:"+e+"/errorCorrectLevel:"+t);for(var r=n.length/3,i=new Array,o=0;o<r;o++)for(var u=n[o*3+0],a=n[o*3+1],f=n[o*3+2],l=0;l<u;l++)i.push(new D(a,f));return i};D.getRsBlockTable=function(e,t){switch(t){case I.L:return D.RS_BLOCK_TABLE[(e-1)*4+0];case I.M:return D.RS_BLOCK_TABLE[(e-1)*4+1];case I.Q:return D.RS_BLOCK_TABLE[(e-1)*4+2];case I.H:return D.RS_BLOCK_TABLE[(e-1)*4+3];default:return}};ut.exports=D});var ht=B((le,lt)=>{g();v();function ft(){this.buffer=new Array,this.length=0}ft.prototype={get:function(e){var t=Math.floor(e/8);return(this.buffer[t]>>>7-e%8&1)==1},put:function(e,t){for(var n=0;n<t;n++)this.putBit((e>>>t-n-1&1)==1)},getLengthInBits:function(){return this.length},putBit:function(e){var t=Math.floor(this.length/8);this.buffer.length<=t&&this.buffer.push(0),e&&(this.buffer[t]|=128>>>this.length%8),this.length++}};lt.exports=ft});var F=B((ve,gt)=>{g();v();var L={glog:function(e){if(e<1)throw new Error("glog("+e+")");return L.LOG_TABLE[e]},gexp:function(e){for(;e<0;)e+=255;for(;e>=256;)e-=255;return L.EXP_TABLE[e]},EXP_TABLE:new Array(256),LOG_TABLE:new Array(256)};for(d=0;d<8;d++)L.EXP_TABLE[d]=1<<d;var d;for(d=8;d<256;d++)L.EXP_TABLE[d]=L.EXP_TABLE[d-4]^L.EXP_TABLE[d-5]^L.EXP_TABLE[d-6]^L.EXP_TABLE[d-8];var d;for(d=0;d<255;d++)L.LOG_TABLE[L.EXP_TABLE[d]]=d;var d;gt.exports=L});var X=B((ce,vt)=>{g();v();var x=F();function H(e,t){if(e.length==null)throw new Error(e.length+"/"+t);for(var n=0;n<e.length&&e[n]==0;)n++;this.num=new Array(e.length-n+t);for(var r=0;r<e.length-n;r++)this.num[r]=e[r+n]}H.prototype={get:function(e){return this.num[e]},getLength:function(){return this.num.length},multiply:function(e){for(var t=new Array(this.getLength()+e.getLength()-1),n=0;n<this.getLength();n++)for(var r=0;r<e.getLength();r++)t[n+r]^=x.gexp(x.glog(this.get(n))+x.glog(e.get(r)));return new H(t,0)},mod:function(e){if(this.getLength()-e.getLength()<0)return this;for(var t=x.glog(this.get(0))-x.glog(e.get(0)),n=new Array(this.getLength()),r=0;r<this.getLength();r++)n[r]=this.get(r);for(var r=0;r<e.getLength();r++)n[r]^=x.gexp(x.glog(e.get(r))+t);return new H(n,0).mod(e)}};vt.exports=H});var ct=B((Pe,mt)=>{g();v();var w=U(),pt=X(),Xt=F(),R={PATTERN000:0,PATTERN001:1,PATTERN010:2,PATTERN011:3,PATTERN100:4,PATTERN101:5,PATTERN110:6,PATTERN111:7},y={PATTERN_POSITION_TABLE:[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],G15:1335,G18:7973,G15_MASK:21522,getBCHTypeInfo:function(e){for(var t=e<<10;y.getBCHDigit(t)-y.getBCHDigit(y.G15)>=0;)t^=y.G15<<y.getBCHDigit(t)-y.getBCHDigit(y.G15);return(e<<10|t)^y.G15_MASK},getBCHTypeNumber:function(e){for(var t=e<<12;y.getBCHDigit(t)-y.getBCHDigit(y.G18)>=0;)t^=y.G18<<y.getBCHDigit(t)-y.getBCHDigit(y.G18);return e<<12|t},getBCHDigit:function(e){for(var t=0;e!=0;)t++,e>>>=1;return t},getPatternPosition:function(e){return y.PATTERN_POSITION_TABLE[e-1]},getMask:function(e,t,n){switch(e){case R.PATTERN000:return(t+n)%2==0;case R.PATTERN001:return t%2==0;case R.PATTERN010:return n%3==0;case R.PATTERN011:return(t+n)%3==0;case R.PATTERN100:return(Math.floor(t/2)+Math.floor(n/3))%2==0;case R.PATTERN101:return t*n%2+t*n%3==0;case R.PATTERN110:return(t*n%2+t*n%3)%2==0;case R.PATTERN111:return(t*n%3+(t+n)%2)%2==0;default:throw new Error("bad maskPattern:"+e)}},getErrorCorrectPolynomial:function(e){for(var t=new pt([1],0),n=0;n<e;n++)t=t.multiply(new pt([1,Xt.gexp(n)],0));return t},getLengthInBits:function(e,t){if(1<=t&&t<10)switch(e){case w.MODE_NUMBER:return 10;case w.MODE_ALPHA_NUM:return 9;case w.MODE_8BIT_BYTE:return 8;case w.MODE_KANJI:return 8;default:throw new Error("mode:"+e)}else if(t<27)switch(e){case w.MODE_NUMBER:return 12;case w.MODE_ALPHA_NUM:return 11;case w.MODE_8BIT_BYTE:return 16;case w.MODE_KANJI:return 10;default:throw new Error("mode:"+e)}else if(t<41)switch(e){case w.MODE_NUMBER:return 14;case w.MODE_ALPHA_NUM:return 13;case w.MODE_8BIT_BYTE:return 16;case w.MODE_KANJI:return 12;default:throw new Error("mode:"+e)}else throw new Error("type:"+t)},getLostPoint:function(e){for(var t=e.getModuleCount(),n=0,r=0;r<t;r++)for(var i=0;i<t;i++){for(var o=0,u=e.isDark(r,i),a=-1;a<=1;a++)if(!(r+a<0||t<=r+a))for(var f=-1;f<=1;f++)i+f<0||t<=i+f||a==0&&f==0||u==e.isDark(r+a,i+f)&&o++;o>5&&(n+=3+o-5)}for(var r=0;r<t-1;r++)for(var i=0;i<t-1;i++){var l=0;e.isDark(r,i)&&l++,e.isDark(r+1,i)&&l++,e.isDark(r,i+1)&&l++,e.isDark(r+1,i+1)&&l++,(l==0||l==4)&&(n+=3)}for(var r=0;r<t;r++)for(var i=0;i<t-6;i++)e.isDark(r,i)&&!e.isDark(r,i+1)&&e.isDark(r,i+2)&&e.isDark(r,i+3)&&e.isDark(r,i+4)&&!e.isDark(r,i+5)&&e.isDark(r,i+6)&&(n+=40);for(var i=0;i<t;i++)for(var r=0;r<t-6;r++)e.isDark(r,i)&&!e.isDark(r+1,i)&&e.isDark(r+2,i)&&e.isDark(r+3,i)&&e.isDark(r+4,i)&&!e.isDark(r+5,i)&&e.isDark(r+6,i)&&(n+=40);for(var s=0,i=0;i<t;i++)for(var r=0;r<t;r++)e.isDark(r,i)&&s++;var C=Math.abs(100*s/t/t-50)/5;return n+=C*10,n}};mt.exports=y});var yt=B((_e,Pt)=>{g();v();var jt=ot(),dt=st(),Et=ht(),S=ct(),$t=X();function O(e,t){this.typeNumber=e,this.errorCorrectLevel=t,this.modules=null,this.moduleCount=0,this.dataCache=null,this.dataList=[]}var T=O.prototype;T.addData=function(e){var t=new jt(e);this.dataList.push(t),this.dataCache=null};T.isDark=function(e,t){if(e<0||this.moduleCount<=e||t<0||this.moduleCount<=t)throw new Error(e+","+t);return this.modules[e][t]};T.getModuleCount=function(){return this.moduleCount};T.make=function(){if(this.typeNumber<1){var e=1;for(e=1;e<40;e++){for(var t=dt.getRSBlocks(e,this.errorCorrectLevel),n=new Et,r=0,i=0;i<t.length;i++)r+=t[i].dataCount;for(var i=0;i<this.dataList.length;i++){var o=this.dataList[i];n.put(o.mode,4),n.put(o.getLength(),S.getLengthInBits(o.mode,e)),o.write(n)}if(n.getLengthInBits()<=r*8)break}this.typeNumber=e}this.makeImpl(!1,this.getBestMaskPattern())};T.makeImpl=function(e,t){this.moduleCount=this.typeNumber*4+17,this.modules=new Array(this.moduleCount);for(var n=0;n<this.moduleCount;n++){this.modules[n]=new Array(this.moduleCount);for(var r=0;r<this.moduleCount;r++)this.modules[n][r]=null}this.setupPositionProbePattern(0,0),this.setupPositionProbePattern(this.moduleCount-7,0),this.setupPositionProbePattern(0,this.moduleCount-7),this.setupPositionAdjustPattern(),this.setupTimingPattern(),this.setupTypeInfo(e,t),this.typeNumber>=7&&this.setupTypeNumber(e),this.dataCache==null&&(this.dataCache=O.createData(this.typeNumber,this.errorCorrectLevel,this.dataList)),this.mapData(this.dataCache,t)};T.setupPositionProbePattern=function(e,t){for(var n=-1;n<=7;n++)if(!(e+n<=-1||this.moduleCount<=e+n))for(var r=-1;r<=7;r++)t+r<=-1||this.moduleCount<=t+r||(0<=n&&n<=6&&(r==0||r==6)||0<=r&&r<=6&&(n==0||n==6)||2<=n&&n<=4&&2<=r&&r<=4?this.modules[e+n][t+r]=!0:this.modules[e+n][t+r]=!1)};T.getBestMaskPattern=function(){for(var e=0,t=0,n=0;n<8;n++){this.makeImpl(!0,n);var r=S.getLostPoint(this);(n==0||e>r)&&(e=r,t=n)}return t};T.createMovieClip=function(e,t,n){var r=e.createEmptyMovieClip(t,n),i=1;this.make();for(var o=0;o<this.modules.length;o++)for(var u=o*i,a=0;a<this.modules[o].length;a++){var f=a*i,l=this.modules[o][a];l&&(r.beginFill(0,100),r.moveTo(f,u),r.lineTo(f+i,u),r.lineTo(f+i,u+i),r.lineTo(f,u+i),r.endFill())}return r};T.setupTimingPattern=function(){for(var e=8;e<this.moduleCount-8;e++)this.modules[e][6]==null&&(this.modules[e][6]=e%2==0);for(var t=8;t<this.moduleCount-8;t++)this.modules[6][t]==null&&(this.modules[6][t]=t%2==0)};T.setupPositionAdjustPattern=function(){for(var e=S.getPatternPosition(this.typeNumber),t=0;t<e.length;t++)for(var n=0;n<e.length;n++){var r=e[t],i=e[n];if(this.modules[r][i]==null)for(var o=-2;o<=2;o++)for(var u=-2;u<=2;u++)o==-2||o==2||u==-2||u==2||o==0&&u==0?this.modules[r+o][i+u]=!0:this.modules[r+o][i+u]=!1}};T.setupTypeNumber=function(e){for(var t=S.getBCHTypeNumber(this.typeNumber),n=0;n<18;n++){var r=!e&&(t>>n&1)==1;this.modules[Math.floor(n/3)][n%3+this.moduleCount-8-3]=r}for(var n=0;n<18;n++){var r=!e&&(t>>n&1)==1;this.modules[n%3+this.moduleCount-8-3][Math.floor(n/3)]=r}};T.setupTypeInfo=function(e,t){for(var n=this.errorCorrectLevel<<3|t,r=S.getBCHTypeInfo(n),i=0;i<15;i++){var o=!e&&(r>>i&1)==1;i<6?this.modules[i][8]=o:i<8?this.modules[i+1][8]=o:this.modules[this.moduleCount-15+i][8]=o}for(var i=0;i<15;i++){var o=!e&&(r>>i&1)==1;i<8?this.modules[8][this.moduleCount-i-1]=o:i<9?this.modules[8][15-i-1+1]=o:this.modules[8][15-i-1]=o}this.modules[this.moduleCount-8][8]=!e};T.mapData=function(e,t){for(var n=-1,r=this.moduleCount-1,i=7,o=0,u=this.moduleCount-1;u>0;u-=2)for(u==6&&u--;;){for(var a=0;a<2;a++)if(this.modules[r][u-a]==null){var f=!1;o<e.length&&(f=(e[o]>>>i&1)==1);var l=S.getMask(t,r,u-a);l&&(f=!f),this.modules[r][u-a]=f,i--,i==-1&&(o++,i=7)}if(r+=n,r<0||this.moduleCount<=r){r-=n,n=-n;break}}};O.PAD0=236;O.PAD1=17;O.createData=function(e,t,n){for(var r=dt.getRSBlocks(e,t),i=new Et,o=0;o<n.length;o++){var u=n[o];i.put(u.mode,4),i.put(u.getLength(),S.getLengthInBits(u.mode,e)),u.write(i)}for(var a=0,o=0;o<r.length;o++)a+=r[o].dataCount;if(i.getLengthInBits()>a*8)throw new Error("code length overflow. ("+i.getLengthInBits()+">"+a*8+")");for(i.getLengthInBits()+4<=a*8&&i.put(0,4);i.getLengthInBits()%8!=0;)i.putBit(!1);for(;!(i.getLengthInBits()>=a*8||(i.put(O.PAD0,8),i.getLengthInBits()>=a*8));)i.put(O.PAD1,8);return O.createBytes(i,r)};O.createBytes=function(e,t){for(var n=0,r=0,i=0,o=new Array(t.length),u=new Array(t.length),a=0;a<t.length;a++){var f=t[a].dataCount,l=t[a].totalCount-f;r=Math.max(r,f),i=Math.max(i,l),o[a]=new Array(f);for(var s=0;s<o[a].length;s++)o[a][s]=255&e.buffer[s+n];n+=f;var C=S.getErrorCorrectPolynomial(l),P=new $t(o[a],C.getLength()-1),h=P.mod(C);u[a]=new Array(C.getLength()-1);for(var s=0;s<u[a].length;s++){var p=s+h.getLength()-u[a].length;u[a][s]=p>=0?h.get(p):0}}for(var m=0,s=0;s<t.length;s++)m+=t[s].totalCount;for(var c=new Array(m),E=0,s=0;s<r;s++)for(var a=0;a<t.length;a++)s<o[a].length&&(c[E++]=o[a][s]);for(var s=0;s<i;s++)for(var a=0;a<t.length;a++)s<u[a].length&&(c[E++]=u[a][s]);return c};Pt.exports=O});var Wt=B((we,kt)=>{"use strict";g();v();function z(e){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?z=function(n){return typeof n}:z=function(n){return n&&typeof Symbol=="function"&&n.constructor===Symbol&&n!==Symbol.prototype?"symbol":typeof n},z(e)}function Q(){return Q=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Q.apply(this,arguments)}function Ct(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(e,i).enumerable})),n.push.apply(n,r)}return n}function Tt(e){for(var t=1;t<arguments.length;t++){var n=arguments[t]!=null?arguments[t]:{};t%2?Ct(n,!0).forEach(function(r){N(e,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):Ct(n).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(n,r))})}return e}function $(e,t){if(e==null)return{};var n=Vt(e,t),r,i;if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(i=0;i<o.length;i++)r=o[i],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}function Vt(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,o;for(o=0;o<r.length;o++)i=r[o],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function bt(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _t(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}function wt(e,t,n){return t&&_t(e.prototype,t),n&&_t(e,n),e}function Lt(e,t){return t&&(z(t)==="object"||typeof t=="function")?t:k(e)}function G(e){return G=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},G(e)}function k(e){if(e===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e}function At(e,t){if(typeof t!="function"&&t!==null)throw new TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),t&&j(e,t)}function j(e,t){return j=Object.setPrototypeOf||function(r,i){return r.__proto__=i,r},j(e,t)}function N(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var A=Z(),b=Kt(),Bt=yt(),Dt=K();function Ot(e){for(var t="",n=0;n<e.length;n++){var r=e.charCodeAt(n);r<128?t+=String.fromCharCode(r):r<2048?(t+=String.fromCharCode(192|r>>6),t+=String.fromCharCode(128|r&63)):r<55296||r>=57344?(t+=String.fromCharCode(224|r>>12),t+=String.fromCharCode(128|r>>6&63),t+=String.fromCharCode(128|r&63)):(n++,r=65536+((r&1023)<<10|e.charCodeAt(n)&1023),t+=String.fromCharCode(240|r>>18),t+=String.fromCharCode(128|r>>12&63),t+=String.fromCharCode(128|r>>6&63),t+=String.fromCharCode(128|r&63))}return t}var V={size:128,level:"L",bgColor:"#FFFFFF",fgColor:"#000000",includeMargin:!1},Mt=_.NODE_ENV!=="production"?{value:b.string.isRequired,size:b.number,level:b.oneOf(["L","M","Q","H"]),bgColor:b.string,fgColor:b.string,includeMargin:b.bool,imageSettings:b.shape({src:b.string.isRequired,height:b.number.isRequired,width:b.number.isRequired,excavate:b.bool,x:b.number,y:b.number})}:{},Y=4,Yt=.1;function Rt(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:0,n=[];return e.forEach(function(r,i){var o=null;r.forEach(function(u,a){if(!u&&o!==null){n.push("M".concat(o+t," ").concat(i+t,"h").concat(a-o,"v1H").concat(o+t,"z")),o=null;return}if(a===r.length-1){if(!u)return;o===null?n.push("M".concat(a+t,",").concat(i+t," h1v1H").concat(a+t,"z")):n.push("M".concat(o+t,",").concat(i+t," h").concat(a+1-o,"v1H").concat(o+t,"z"));return}u&&o===null&&(o=a)})}),n.join("")}function St(e,t){return e.slice().map(function(n,r){return r<t.y||r>=t.y+t.h?n:n.map(function(i,o){return o<t.x||o>=t.x+t.w?i:!1})})}function xt(e,t){var n=e.imageSettings,r=e.size,i=e.includeMargin;if(n==null)return null;var o=i?Y:0,u=t.length+o*2,a=Math.floor(r*Yt),f=u/r,l=(n.width||a)*f,s=(n.height||a)*f,C=n.x==null?t.length/2-l/2:n.x*f,P=n.y==null?t.length/2-s/2:n.y*f,h=null;if(n.excavate){var p=Math.floor(C),m=Math.floor(P),c=Math.ceil(l+C-p),E=Math.ceil(s+P-m);h={x:p,y:m,w:c,h:E}}return{x:C,y:P,h:s,w:l,excavation:h}}var Jt=function(){try{new Path2D().addPath(new Path2D)}catch{return!1}return!0}(),J=function(e){At(t,e);function t(){var n,r;bt(this,t);for(var i=arguments.length,o=new Array(i),u=0;u<i;u++)o[u]=arguments[u];return r=Lt(this,(n=G(t)).call.apply(n,[this].concat(o))),N(k(r),"_canvas",void 0),N(k(r),"_image",void 0),N(k(r),"state",{imgLoaded:!1}),N(k(r),"handleImageLoad",function(){r.setState({imgLoaded:!0})}),r}return wt(t,[{key:"componentDidMount",value:function(){this._image&&this._image.complete&&this.handleImageLoad(),this.update()}},{key:"componentWillReceiveProps",value:function(r){var i,o,u=(i=this.props.imageSettings)===null||i===void 0?void 0:i.src,a=(o=r.imageSettings)===null||o===void 0?void 0:o.src;u!==a&&this.setState({imgLoaded:!1})}},{key:"componentDidUpdate",value:function(){this.update()}},{key:"update",value:function(){var r=this.props,i=r.value,o=r.size,u=r.level,a=r.bgColor,f=r.fgColor,l=r.includeMargin,s=r.imageSettings,C=new Bt(-1,Dt[u]);if(C.addData(Ot(i)),C.make(),this._canvas!=null){var P=this._canvas,h=P.getContext("2d");if(!h)return;var p=C.modules;if(p===null)return;var m=l?Y:0,c=p.length+m*2,E=xt(this.props,p);s!=null&&E!=null&&E.excavation!=null&&(p=St(p,E.excavation));var M=self.devicePixelRatio||1;P.height=P.width=o*M;var q=o/c*M;h.scale(q,q),h.fillStyle=a,h.fillRect(0,0,c,c),h.fillStyle=f,Jt?h.fill(new Path2D(Rt(p,m))):p.forEach(function(Ht,zt){Ht.forEach(function(Qt,Gt){Qt&&h.fillRect(Gt+m,zt+m,1,1)})}),this.state.imgLoaded&&this._image&&E!=null&&h.drawImage(this._image,E.x+m,E.y+m,E.w,E.h)}}},{key:"render",value:function(){var r=this,i=this.props,o=i.value,u=i.size,a=i.level,f=i.bgColor,l=i.fgColor,s=i.style,C=i.includeMargin,P=i.imageSettings,h=$(i,["value","size","level","bgColor","fgColor","style","includeMargin","imageSettings"]),p=Tt({height:u,width:u},s),m=null,c=P&&P.src;return P!=null&&c!=null&&(m=A.createElement("img",{src:c,style:{display:"none"},onLoad:this.handleImageLoad,ref:function(M){return r._image=M}})),A.createElement(A.Fragment,null,A.createElement("canvas",Q({style:p,height:u,width:u,ref:function(M){return r._canvas=M}},h)),m)}}]),t}(A.PureComponent);N(J,"defaultProps",V);_.NODE_ENV!=="production"&&(J.propTypes=Mt);var W=function(e){At(t,e);function t(){return bt(this,t),Lt(this,G(t).apply(this,arguments))}return wt(t,[{key:"render",value:function(){var r=this.props,i=r.value,o=r.size,u=r.level,a=r.bgColor,f=r.fgColor,l=r.includeMargin,s=r.imageSettings,C=$(r,["value","size","level","bgColor","fgColor","includeMargin","imageSettings"]),P=new Bt(-1,Dt[u]);P.addData(Ot(i)),P.make();var h=P.modules;if(h===null)return null;var p=l?Y:0,m=h.length+p*2,c=xt(this.props,h),E=null;s!=null&&c!=null&&(c.excavation!=null&&(h=St(h,c.excavation)),E=A.createElement("image",{xlinkHref:s.src,height:c.h,width:c.w,x:c.x+p,y:c.y+p,preserveAspectRatio:"none"}));var M=Rt(h,p);return A.createElement("svg",Q({shapeRendering:"crispEdges",height:o,width:o,viewBox:"0 0 ".concat(m," ").concat(m)},C),A.createElement("path",{fill:a,d:"M0,0 h".concat(m,"v").concat(m,"H0z")}),A.createElement("path",{fill:f,d:M}),E)}}]),t}(A.PureComponent);N(W,"defaultProps",V);_.NODE_ENV!=="production"&&(W.propTypes=Mt);var Nt=function(t){var n=t.renderAs,r=$(t,["renderAs"]),i=n==="svg"?W:J;return A.createElement(i,r)};Nt.defaultProps=Tt({renderAs:"canvas"},V);kt.exports=Nt});g();v();var It=Ut(Z());var Oe=(e,t)=>{let{t:n}=tt(),[r,i,o]=et(e??""),u=r?n("pastParticipleCopied"):t||n("commandCopy");return(0,It.useEffect)(()=>{let a;return r&&(a=setTimeout(()=>{o(!1)},3e3)),()=>{a&&clearTimeout(a)}},[r,o]),{buttonText:u,copied:r,copy:i}};export{Oe as a,Wt as b};
//# sourceMappingURL=chunk-ZNZZRKNQ.js.map
