// Đ<PERSON>u tiên đăng ký ts-node để có thể import file TypeScript
require('ts-node').register({
  transpileOnly: true,
  compilerOptions: {
    module: 'CommonJS'
  }
});

// <PERSON><PERSON>y giờ có thể nhập file TypeScript
const globalTeardown = require('./global-teardown').default;

// Đặt biến môi trường để biết là đang chạy teardown trực tiếp
process.env.RESET_LOGIN = 'true';

// Chạy global teardown
(async () => {
  console.log('Đang chạy global teardown riêng biệt...');
  try {
    await globalTeardown({});
    console.log('Hoàn thành global teardown.');
  } catch (error) {
    console.error('Lỗi khi chạy global teardown:', error);
    process.exit(1);
  }
})();
