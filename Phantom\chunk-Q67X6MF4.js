import{Q as y,T as m}from"./chunk-MZZEJ42N.js";import{a as t}from"./chunk-4P36KWOF.js";import{h as l,n}from"./chunk-3KENBVE7.js";l();n();var p="default",P=t.union([t.literal("vote"),t.literal("vote-2"),t.literal("stake"),t.literal("stake-2"),t.literal("view"),t.literal("chat"),t.literal("tip"),t.literal("mint"),t.literal("mint-2"),t.literal("discord"),t.literal("twitter"),t.literal("twitter-2"),t.literal("x"),t.literal("instagram"),t.literal("telegram"),t.literal("leaderboard"),t.literal("gaming"),t.literal("gaming-2"),t.literal("generic-link"),t.literal("generic-add"),t.literal(p)]).optional().default(p).catch(p),g=t.object({label:t.string(),uri:t.string().url().refine(e=>e.startsWith("https://")||e.startsWith("solana:"),e=>({message:`${e} is not a valid protocol`})),prefersExternalTarget:t.boolean().optional().default(!1),preferredPresentation:t.union([t.literal("default"),t.literal("immerse")]).optional().default("immerse"),platform:t.union([t.literal("mobile"),t.literal("desktop"),t.literal("all")]).optional().default("all"),icon:P}),x=g.extend({limitToCollections:t.string().array().optional().default([]).refine(e=>e.length===0?!0:e.every(r=>r.length),e=>({message:`${e} is not a valid array of collection ids`}))}),k=g.extend({type:t.literal("fungible"),limitToTokenAddresses:t.string().array().optional().default([]).refine(e=>e.length===0?!0:e.every(r=>r.length),e=>({message:`${e} is not a valid array of token addresses`}))}),E=x.extend({type:t.literal("collectible").optional().default("collectible")}),L=t.union([E,k]),f=t.union([t.object({version:t.literal(1),shortcuts:t.array(x).optional().default([])}),t.object({version:t.literal(2),shortcuts:t.array(L).optional().default([])})]),I=t.object({collectionId:t.string().optional(),tokenId:t.string().optional(),ownerAddress:t.string().optional()});l();n();l();n();l();n();var b=e=>{let r=e.pathname.split("/").filter(a=>a!=="").join("/"),o=new URL(`https://${e.hostname}`);return o.pathname=r+"/shortcuts.json",o.search=e.search,o},h=e=>{if(!e||!e.trim())return"";let r=e.trim();return/^https?:\/\//.test(r)?r:`https://${r}`},d=e=>{if(!e)return!1;try{return new URL(e),!0}catch{return!1}};var R="wallet-shortcuts",U=e=>({queryKey:[R,e?.toString()],async queryFn(){if(!e)throw new Error("URL is null");let o=b(e),a=await F(o);if(f.safeParse(a).success===!1)throw new Error(`Invalid shortcut response from URL ${o.toString()}`);return f.parse(a).shortcuts}}),S=new Error("Failed to fetch shortcuts for collection"),F=async e=>{let r="/collectibles/v1/shortcuts",o;try{o=await y.api().post(r,{uri:e.toString()})}catch{throw S}if(!m(o))throw S;return o.data};var A=10;function B(e,r,o){if(!r)return;let{externalUrl:a,isSpam:w,items:T}=r;if(w||!o)return;let C=(T??[]).map(i=>i.externalUrl);Array.from(new Set([a,...C])).reduce((i,s)=>{let c=h(s);return d(c)&&i.length<A?[...i,c]:i},[]).forEach(i=>{let s=h(i);d(s)&&e.prefetchQuery(U(new URL(s))).catch(c=>{console.error(`Failed to prefetch ${s}`,c)})})}export{p as a,h as b,d as c,R as d,U as e,B as f};
//# sourceMappingURL=chunk-Q67X6MF4.js.map
