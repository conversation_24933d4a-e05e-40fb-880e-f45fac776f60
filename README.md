# Dex3-Automation_Playwright

🚀 **Dự án tự động hóa testing cho nền tảng Dex3 sử dụng Playwright và TypeScript**

## 📖 Mục lục

- [Giới thiệu](#giới-thiệu)
- [<PERSON><PERSON>u cầu hệ thống](#yêu-cầu-hệ-thống)
- [Cài đặt](#cài-đặt)
- [<PERSON><PERSON><PERSON> hình](#cấu-hình)
- [Cấu trúc dự án](#cấu-trúc-dự-án)
- [Chạy tests](#chạy-tests)
- [NPM Scripts](#npm-scripts)
- [Branches và Workflow](#branches-và-workflow)
- [Troubleshooting](#troubleshooting)


## 🎯 Giới thiệu

Dự án này cung cấp bộ test tự động hóa cho nền tảng Dex3 (https://dex3.ai), bao gồm:

- ✅ **Dashboard Testing**: Kiểm tra các DEX và danh sách token
- ✅ **Search Functionality**: Test tìm kiếm token và popup
- ✅ **Filter Testing**: Kiểm tra bộ lọc DEX và token
- ✅ **Buy/Sell Testing**: Test chức năng mua bán token
- ✅ **Phantom Wallet Integration**: Tích hợp với ví Phantom

## 🔧 Yêu cầu hệ thống

- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **Phantom Wallet Extension**: Cần tải và cài đặt
- **Chrome Browser**: Để chạy tests

## 📦 Cài đặt

### 1. Clone repository

```bash
git clone https://gitlab.com/platform126/automation-testing/dex3.git
cd dex3
```

### 2. Cài đặt dependencies

```bash
npm install
```

### 3. Cài đặt Playwright browsers

```bash
npx playwright install
```

### 4. Tải Phantom Wallet Extension

1. Tải Phantom extension từ Chrome Web Store hoặc từ file .crx
2. Giải nén vào thư mục (ví dụ: `C:\Users\<USER>\Downloads\Phantom`)
3. Ghi nhớ đường dẫn này để cấu hình

## ⚙️ Cấu hình

### 1. Tạo file environment

Tạo file `tests/base/env.ts` với nội dung:

```typescript
export const ENV = {
  BASE_URL: 'https://dex3.ai',
  PHANTOM_EXTENSION_PATH: 'C:\\Users\\<USER>\\Downloads\\Phantom', // Đường dẫn đến Phantom extension
  WALLET_SEED_PHRASE: 'your wallet seed phrase here', // Seed phrase của ví test
  WALLET_PASSWORD: 'your wallet password', // Mật khẩu ví
  // Thêm các biến môi trường khác nếu cần
};
```

### 2. Cấu hình Phantom Extension Path

Có 2 cách để cấu hình đường dẫn Phantom extension:

**Cách 1: Sử dụng biến môi trường**
```bash
set PHANTOM_EXTENSION_PATH=C:\path\to\your\phantom\extension
```

**Cách 2: Chỉnh sửa trực tiếp trong `playwright.config.ts`**
```typescript
launchOptions: {
  args: [
    `--disable-extensions-except=C:\\path\\to\\your\\phantom\\extension`,
    `--load-extension=C:\\path\\to\\your\\phantom\\extension`,
  ],
}
```

## 📁 Cấu trúc dự án

```
Dex3-Automation_Playwright/
├── 📁 tests/                          # Thư mục chứa tất cả test files
│   ├── 📁 base/                       # Base classes và utilities
│   │   ├── BaseTest.ts                # Base test class
│   │   ├── TestBase.ts                # Test fixtures và setup
│   │   ├── env.ts                     # Environment configuration
│   │   ├── 📁 actions/                # UI action helpers
│   │   └── 📁 constants/              # Constants và enums
│   ├── 📁 pages/                      # Page Object Model
│   │   ├── DashboardPage.ts           # Dashboard page actions
│   │   ├── SearchPage.ts              # Search functionality
│   │   └── TokenDetailPage.ts         # Token detail page
│   ├── 📁 selectors/                  # CSS/XPath selectors
│   │   ├── CommonSelectors.ts         # Common UI selectors
│   │   ├── DashboardSelectors.ts      # Dashboard selectors
│   │   ├── SearchSelectors.ts         # Search selectors
│   │   └── TokenDetailSelectors.ts    # Token detail selectors
│   └── 📁 tests/                      # Test specifications
│       ├── dashboardTest.spec.ts      # Dashboard tests
│       ├── searchTest.spec.ts         # Search tests
│       ├── filterTest.spec.ts         # Filter tests
│       └── buySellTest.spec.ts        # Buy/Sell tests
├── 📁 screenshots/                    # Test screenshots
├── 📁 Phantom/                        # Phantom extension files
├── API.md                             # API documentation cho Page Objects
├── SETUP.md                           # Hướng dẫn setup chi tiết
├── GlobalSetupHelper.ts               # Helper cho global setup
├── global-setup.ts                    # Global test setup
├── global-teardown.ts                 # Global test cleanup
├── playwright.config.ts               # Playwright configuration
├── setup.js                          # Standalone setup script
├── teardown.js                       # Standalone teardown script
└── README.md                          # Documentation này
```

## 🚀 Chạy tests

### Trước khi chạy tests lần đầu

1. **Chạy setup để import ví Phantom:**
```bash
npm run setup
```

2. **Kiểm tra setup thành công:**
- File `storageState.json` được tạo
- File `login-state.json` được tạo
- Không có lỗi trong console

### Chạy tất cả tests

```bash
npm test
```

### Chạy tests với UI mode

```bash
npm run test:ui
```

### Chạy tests với headed mode (hiển thị browser)

```bash
npm run test:headed
```

### Chạy từng loại test riêng biệt

```bash
# Test dashboard và DEX verification
npm run test:dashboard

# Test search functionality
npm run test:search

# Test filter functionality
npm run test:filter

# Test buy/sell functionality
npm run test:buysell
```

### Xem test reports

```bash
npm run report
```

### Reset login state (khi cần đăng nhập lại)

```bash
npm run teardown
npm run setup
```

## 📜 NPM Scripts

| Script | Mô tả |
|--------|-------|
| `npm test` | Chạy tất cả tests |
| `npm run test:headed` | Chạy tests với browser hiển thị |
| `npm run test:ui` | Chạy tests với Playwright UI mode |
| `npm run test:search` | Chạy tests cho search functionality |
| `npm run test:filter` | Chạy tests cho filter functionality |
| `npm run test:dashboard` | Chạy tests cho dashboard |
| `npm run test:buysell` | Chạy tests cho buy/sell functionality |
| `npm run setup` | Setup môi trường và import ví Phantom |
| `npm run teardown` | Dọn dẹp và reset login state |
| `npm run report` | Mở test report |

## 🌿 Branches và Workflow

### Main Branches

- **`main`**: Branch chính, code ổn định
- **`master`**: Branch master, sync với main

### Development Workflow

1. **Tạo feature branch từ main:**
```bash
git checkout main
git pull origin main
git checkout -b feature/your-feature-name
```

2. **Develop và test:**
```bash
# Chạy tests để đảm bảo không break existing functionality
npm run test:dashboard
npm run test:search
```

3. **Commit và push:**
```bash
git add .
git commit -m "feat: add new feature"
git push origin feature/your-feature-name
```

4. **Tạo Merge Request trên GitLab**

### Branch Naming Convention

- `feature/` - Tính năng mới
- `bugfix/` - Sửa lỗi
- `hotfix/` - Sửa lỗi khẩn cấp
- `test/` - Cải thiện tests

## 🔍 Test Strategy

### Test Categories

1. **Dashboard Tests** (`dashboardTest.spec.ts`)
   - Kiểm tra hiển thị các DEX
   - Verify DEX names và functionality
   - Test navigation và UI elements

2. **Search Tests** (`searchTest.spec.ts`)
   - Test search popup
   - Trending tokens section (phải có đúng 3 tokens)
   - Top wallets section
   - Search functionality với keywords

3. **Filter Tests** (`filterTest.spec.ts`)
   - Test filter DEX dropdown
   - Filter theo từng DEX
   - Verify kết quả filter

4. **Buy/Sell Tests** (`buySellTest.spec.ts`)
   - Test buy token functionality
   - Test sell token functionality
   - Phantom wallet integration

### Test Data

- **DEX Names**: Raydium, LaunchLab, Pumpswap, PumpFun, Moonshot, Orca, Meteora, FluxBeam
- **Test Environment**: https://dex3.ai/pump-to-moon
- **Screenshots**: Tự động lưu trong thư mục `screenshots/`

## 🐛 Troubleshooting

### Lỗi thường gặp

#### 1. Phantom Extension không load được

**Lỗi:** `Phantom Wallet page not found`

**Giải pháp:**
```bash
# Kiểm tra đường dẫn extension
echo $PHANTOM_EXTENSION_PATH

# Đảm bảo extension path đúng trong env.ts
# Thử chạy lại setup
npm run teardown
npm run setup
```

#### 2. Login state hết hạn

**Lỗi:** `Trạng thái đăng nhập đã hết hạn`

**Giải pháp:**
```bash
# Reset login state
npm run teardown
npm run setup
```

#### 3. Tests timeout

**Lỗi:** `Test timeout after 60000ms`

**Giải pháp:**
- Kiểm tra network connection
- Tăng timeout trong `playwright.config.ts`
- Chạy tests với headed mode để debug: `npm run test:headed`

#### 4. DEX verification failed

**Lỗi:** `DEX verification failed`

**Giải pháp:**
- Kiểm tra trang Dex3 có load đúng không
- Verify các selector trong `DashboardSelectors.ts`
- Chụp screenshot để debug

### Debug Tips

1. **Chạy tests với headed mode:**
```bash
npm run test:headed
```

2. **Xem screenshots khi test fail:**
```bash
# Screenshots được lưu tự động trong thư mục screenshots/
ls screenshots/
```

3. **Xem detailed logs:**
```bash
# Logs được hiển thị trong console khi chạy tests
npm run test:dashboard
```

4. **Chạy single test:**
```bash
npx playwright test tests/tests/dashboardTest.spec.ts --headed
```

## 📞 Support

- **GitLab Issues**: Tạo issue trên GitLab repository
- **Documentation**: Xem thêm trong `API.md` và `SETUP.md` cho chi tiết implementation
- **Code Structure**: Xem thư mục `tests/` cho cấu trúc Page Object Model

## 📄 License

ISC License - Xem file LICENSE để biết thêm chi tiết.

---

**🎯 Happy Testing! 🚀**
