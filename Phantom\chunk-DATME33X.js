import{a as ve}from"./chunk-PDSYJ4DQ.js";import{a as G}from"./chunk-DZR774A2.js";import{E as V,Ja as xe,La as be,Ma as Ce,R as fe,ea as Se}from"./chunk-JD6NH5K6.js";import{a as ye}from"./chunk-AHRYSG4W.js";import{a as R}from"./chunk-QEXGR5WT.js";import{h as he}from"./chunk-75L54KUM.js";import{a as ge}from"./chunk-ROF5SDVA.js";import{a as m,b as k,c as de,d as me,e as h}from"./chunk-2NGYUYTC.js";import{a as H,c as ue}from"./chunk-OKP6DFCI.js";import{c as pe,o as D}from"./chunk-WIQ4WVKX.js";import{I as le,J as ce}from"./chunk-SD2LXVLD.js";import{fc as J}from"./chunk-OUYKWOVO.js";import{$d as se,Xa as X,rd as ae}from"./chunk-MZZEJ42N.js";import{m as ie}from"./chunk-56SJOU6P.js";import{N as oe,S as ne,ja as re,w as te}from"./chunk-ALUTR72U.js";import{Ya as ee}from"./chunk-L3A2KHJO.js";import{a as q}from"./chunk-7X4NV6OJ.js";import{f as j,h as A,n as M}from"./chunk-3KENBVE7.js";A();M();var t=j(q());A();M();var s=j(q());A();M();var T=j(q());var Ie=e=>{if(typeof e=="number")return e;switch(e??"small"){case"normal":return 48;case"small":return 32;case"xsmall":return 24;case"xxsmall":return 16}},We=e=>{switch(e){case"square":return 6;case"circle":default:return 0}},ke=D(ye)`
  border-radius: ${e=>e.borderradius}px;
  width: 100%;
  height: 100%;
`,ze=D.div`
  width: ${e=>e.width}px;
  height: ${e=>e.height}px;
  align-items: center;
  justify-content: center;
  background-color: ${e=>ne(e.color,e.alpha??.1)};
`,O=({src:e,fallback:o,alt:r,size:n="normal",shape:a="circle",style:l,onDragStart:c})=>{let d=o??T.default.createElement(je,null,"?"),i=Ie(n),g=Ie(n),f=We(a);return a==="circle"?T.default.createElement(ge,{color:"#222222",diameter:i,style:l},T.default.createElement(ke,{src:e,fallback:d,alt:r||void 0,width:i,height:g,borderradius:f,onDragStart:c,loader:T.default.createElement(R,{borderRadius:`${f}px`,width:`${i}px`,height:`${g}px`})})):T.default.createElement(ze,{color:"#222222",width:i,height:g,style:l},T.default.createElement(ke,{src:e,fallback:d,alt:r||"",width:i,height:g,borderradius:f,onDragStart:c,loader:T.default.createElement(R,{borderRadius:`${f}px`,width:`${i}px`,height:`${g}px`})}))},je=D.div`
  font-size: 22px;
  line-height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
`;var qe=({item:e,index:o,onClick:r})=>{let n=e.type==="token"?e.data.data.symbol:e.data.name,a=(0,s.useCallback)(d=>{d.preventDefault()},[]),l=e.type==="token"?s.default.createElement(V,{image:{type:"fungible",src:e.data.data.logoUri,fallback:e.data.data.symbol??""},tokenType:e.data.type,chainMeta:e.data.data.chain,size:36}):e.type==="dapp"?s.default.createElement(O,{src:e.data.imageUrl,size:36,shape:"square",onDragStart:a}):null,c=(0,s.useCallback)(()=>{r(o,e)},[o,r,e]);return s.default.createElement("div",{className:W.buttonContainer},s.default.createElement("div",{className:W.button,onClick:c},l,s.default.createElement(h,{className:W.text,color:"white",children:n??""})))},Pe=({items:e,onClick:o})=>{let r=ue(0),n=(0,s.useRef)(null),[a,l]=(0,s.useState)(0),c=(0,s.useMemo)(()=>{let d=self.innerWidth-a;return Math.min(0,d)},[a]);return(0,s.useEffect)(()=>{n.current&&l(n.current.scrollWidth)},[e]),s.default.createElement(me,{overflow:"hidden"},s.default.createElement(H.div,{ref:n},s.default.createElement(H.div,{className:W.scrollView,style:{x:r},drag:"x",dragConstraints:{left:c,right:0}},e.map((d,i)=>s.default.createElement(qe,{key:i,item:d,index:i,onClick:o})))))},W={scrollView:m({paddingTop:8,display:"flex",flexDirection:"row",paddingLeft:8}),buttonContainer:m({paddingRight:10}),button:m({display:"flex",flexDirection:"row",alignItems:"center",backgroundColor:{base:"bgRow",hover:"bgButton"},cursor:"pointer",borderRadius:12,paddingX:6,paddingY:6,height:48}),text:m({font:"body",color:"white",paddingLeft:6,whiteSpace:"nowrap"})};var Xe=D.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: solid 1px;
  width: 100%;
  padding: 10px 0;
  padding-right: 16px;
  flex: 1;
  overflow: hidden;
  border-color: ${e=>e.isLast?k.colors.legacy.bgWallet:k.colors.legacy.borderSecondary};
`,b={searchResultsContainer:m({marginTop:16,width:"100%"}),searchResultsHeaderContainer:m({paddingX:16,paddingY:4,display:"flex",alignItems:"center",justifyContent:"space-between"}),searchResult:m({display:"flex",alignItems:"center",paddingLeft:16,gap:16,height:56,maxHeight:56,cursor:"pointer",width:"100%",maxWidth:"100%"}),searchResultTextContainer:m({display:"flex",flexDirection:"column",minWidth:0,flex:1,marginRight:8}),hover:m({cursor:"pointer"}),picturePlaceholder:m({borderRadius:6,height:32,width:32}),noResultsContainer:m({position:"absolute",top:64,right:0,bottom:0,left:0,display:"flex",justifyContent:"center",alignItems:"center",paddingBottom:56}),tokenSecondaryContainer:m({display:"flex",gap:4,alignItems:"center"}),dotContainer:m({display:"flex",justifyContent:"center",alignItems:"center",width:8}),dot:m({backgroundColor:"textSecondary",width:4,height:4,borderRadius:"circle"}),secondaryAction:m({display:"flex",justifyContent:"center",alignItems:"center",width:32,height:32,borderRadius:10,padding:6})},Te=(e,o,{priceMap:r,isPriceMapLoading:n,isReadOnlyAccount:a,entryPoint:l,navigateToSwapper:c,handleHideModalVisibility:d,addRecentlySearchedItem:i,dappsItems:g,navigate:f,setNotifyHomeFungiblePush:p,pushDetailView:w,enableUnifiedTokenPage:B})=>{let y=X(J(e)),v=r[y],L=v?.price,C=L!==void 0,S=v?.usd_24h_change,F=S!==void 0,P=C||F;return{type:"generic",key:y,"data-testid":`search-result-token-${e.data.symbol}`,image:t.default.createElement(V,{image:{type:"fungible",src:e.data.logoUri,fallback:e.data.symbol??""},tokenType:e.type,chainMeta:e.data.chain,size:32}),textMain:t.default.createElement(h,{children:te(e.data.name||"",25),font:"body"}),textSecondary:t.default.createElement("div",{className:b.tokenSecondaryContainer},t.default.createElement(h,{children:e.data.symbol||"",font:"caption",color:"textSecondary"}),n?t.default.createElement(t.default.Fragment,null,t.default.createElement(R,{width:"36px",height:"14px",borderRadius:"8px"}),t.default.createElement(R,{width:"48px",height:"14px",borderRadius:"8px"})):t.default.createElement(t.default.Fragment,null,P?t.default.createElement("div",{className:b.dotContainer},t.default.createElement("div",{className:b.dot})):null,C?t.default.createElement(fe,{value:L,font:"caption",color:"textSecondary"}):null,F?t.default.createElement(h,{children:`${re(S,{includePlusPrefix:!0})}%`,font:"caption",color:S>=0?"accentSuccess":"accentAlert",truncate:"ellipsis"}):null)),secondaryAction:a?void 0:{icon:t.default.createElement(de.Swap,null),onClick:U=>{U.stopPropagation(),c({buyFungibleCaip19:y}),l==="home"&&setTimeout(()=>{d("searchPage")},50)}},onClick:async()=>{Ne({addRecentlySearchedItem:i,token:e,position:g.length+o+1,entryPoint:l,navigate:f,setNotifyHomeFungiblePush:p,handleHideModalVisibility:d,pushDetailView:w,enableUnifiedTokenPage:B})}}},Ft=({searchedDapps:e,tokens:o,trendingTokens:r,priceMap:n,isPriceMapLoading:a,showMoreDapps:l,setShowMoreDapps:c,renderShowMoreDappsButton:d,showMoreTokens:i,setShowMoreTokens:g,renderShowMoreTokensButton:f,entryPoint:p,recentlyViewedSearchItems:w=[],searchQuery:B})=>{let{t:y}=ie(),{pushDetailView:v}=he(),L=Se(),{handleHideModalVisibility:C}=Ce(),S=ve(u=>u.setNotifyHomeFungiblePush),F=p==="home",P=pe(),U=ae(),{mutate:I}=le(U),{mutate:Y}=ce(U),{data:Ae}=se(),$=!!Ae?.isReadOnly,{data:[E]}=ee(["enable-unified-token-pages"]),N=(0,t.useMemo)(()=>e.map((u,x)=>({type:"generic",key:u.id,"data-testid":`search-result-dapp-${u.name}`,image:t.default.createElement(O,{src:u.imageUrl,size:"small",shape:"square"}),textMain:t.default.createElement(h,{children:u.name,font:"body"}),textSecondary:t.default.createElement(h,{children:u.category||"",font:"caption",color:"textSecondary"}),onClick:async()=>{Ee({addRecentlySearchedItem:I,dapp:u,position:x+1,entryPoint:p})}})),[e,I,p]),_=(0,t.useMemo)(()=>o.map((u,x)=>Te(u,x,{priceMap:n,isPriceMapLoading:a,isReadOnlyAccount:$,entryPoint:p,navigateToSwapper:L,handleHideModalVisibility:C,addRecentlySearchedItem:I,dappsItems:N,navigate:P,setNotifyHomeFungiblePush:S,pushDetailView:v,enableUnifiedTokenPage:E})),[o,n,a,$,p,L,C,I,N,P,S,v,E]),Q=(0,t.useMemo)(()=>r.map((u,x)=>Te(u,x,{priceMap:n,isPriceMapLoading:a,isReadOnlyAccount:$,entryPoint:p,navigateToSwapper:L,handleHideModalVisibility:C,addRecentlySearchedItem:I,dappsItems:N,navigate:P,setNotifyHomeFungiblePush:S,pushDetailView:v,enableUnifiedTokenPage:E})),[r,n,a,$,p,L,C,I,N,P,S,v,E]),Me=(0,t.useMemo)(()=>({type:"recentSearchItems",key:"recentSearchItems",items:w,onClick:async(u,x)=>{if(x.type==="token")Ne({addRecentlySearchedItem:I,token:x.data,position:u,entryPoint:p,navigate:P,setNotifyHomeFungiblePush:S,handleHideModalVisibility:C,pushDetailView:v,enableUnifiedTokenPage:E});else if(x.type==="dapp")Ee({addRecentlySearchedItem:I,dapp:x.data,position:u,entryPoint:p});else throw new Error("Unknown item type")}}),[I,C,p,P,v,w,S,E]),De=w.length?y("commandClear"):void 0,Re=(0,t.useCallback)(()=>{Y()},[Y]),Fe=f?y(l?"exploreShowLess":"exploreShowMore"):void 0,He=(0,t.useCallback)(()=>{g(!i)},[g,i]),Be=d?y(l?"exploreShowLess":"exploreShowMore"):void 0,Ue=(0,t.useCallback)(()=>{c(!l)},[c,l]);if(!e.length&&!o.length)return t.default.createElement("div",{className:b.noResultsContainer,"data-testid":"explore-search-results-nodata"},t.default.createElement(h,{children:y("dappBrowserSearchNoAppsTokens"),font:"bodyMedium",color:"textSecondary"}));let K=B==null||B.length===0,$e=w.length&&K?t.default.createElement(z,{testId:"explore-search-results",title:t.default.createElement(h,{children:y("dAppBrowserRecents"),font:"title1Semibold"}),items:[Me],accessoryText:De,accessoryOnPress:Re}):null,Z=N.length?t.default.createElement(z,{testId:"explore-search-results",title:t.default.createElement(h,{children:y("exploreSites"),font:"title1Semibold"}),items:N,accessoryText:Be,accessoryOnPress:Ue}):null,Ve=_.length?t.default.createElement(z,{testId:"explore-search-results",title:t.default.createElement(h,{children:y("exploreTokens"),font:"title1Semibold"}),items:_,accessoryText:Fe,accessoryOnPress:He}):null,Oe=K&&Q.length?t.default.createElement(z,{testId:"explore-search-results",title:t.default.createElement(h,{children:y("exploreTrendingTokens"),font:"title1Semibold"}),items:Q}):null;return t.default.createElement(t.default.Fragment,null,$e,F?void 0:Z,Oe,Ve,F?Z:void 0)},Le=({hoverColor:e,baseColor:o,...r})=>t.default.createElement(H.div,{...r,whileHover:{background:e},style:{background:o},transition:{ease:[.5,1,.89,1],duration:.1}}),z=({items:e,title:o,testId:r,accessoryText:n,accessoryOnPress:a})=>t.default.createElement("div",{className:b.searchResultsContainer,"data-testid":r},t.default.createElement("div",{className:b.searchResultsHeaderContainer},o,n?t.default.createElement(h,{className:b.hover,children:n,font:"caption",color:"textSecondary",onPress:a}):null),e.map((l,c)=>t.default.createElement(Je,{...l,isLast:c===e.length-1}))),Je=e=>{let[o,r]=(0,t.useState)(!1),[n,a]=(0,t.useState)(!1);if(e.type==="recentSearchItems")return t.default.createElement(Pe,{items:e.items,onClick:e.onClick});let{onClick:c,image:d,isLast:i,textMain:g,textSecondary:f,secondaryAction:p,"data-testid":w}=e;return t.default.createElement(Le,{className:b.searchResult,onClick:c,"data-testid":w,baseColor:k.colors.legacy.bgWallet,hoverColor:n?k.colors.legacy.bgWallet:k.colors.legacy.bgButton,onMouseEnter:()=>r(!0),onMouseLeave:()=>r(!1)},t.default.createElement("div",null,d),t.default.createElement(Xe,{isLast:i},t.default.createElement("div",{className:b.searchResultTextContainer},g,f),p?t.default.createElement(Le,{className:b.secondaryAction,onClick:p.onClick,baseColor:o?k.colors.legacy.borderPrimary:k.colors.legacy.bgButton,hoverColor:k.colors.legacy.borderPrimary,onMouseEnter:()=>a(!0),onMouseLeave:()=>a(!1)},p.icon):null))};function Ee({addRecentlySearchedItem:e,dapp:o,position:r,entryPoint:n}){e({type:"dapp",data:o}),self.open(oe(o.domain).origin),G.searchItemClickedByUser({itemDetails:{position:r,title:o.name,id:o.id,type:"site",uiContext:n}})}function Ne({addRecentlySearchedItem:e,token:o,position:r,entryPoint:n,navigate:a,setNotifyHomeFungiblePush:l,pushDetailView:c,enableUnifiedTokenPage:d}){let i=X(J(o));G.searchItemClickedByUser({itemDetails:{position:r,title:o.data.name||o.data.symbol||i,id:i,type:"token",uiContext:n}}),n==="home"?(a("/"),setTimeout(()=>{l({caip19:i,title:o.data.name??void 0,entryPoint:"homeSearch"})},25),setTimeout(()=>{e({type:"token",data:o})},75)):(c(d?t.default.createElement(xe,{caip19:i,title:o.data.name??void 0,entryPoint:"exploreSearch"}):t.default.createElement(be,{caip19:i,title:o.data.name??void 0,entryPoint:"exploreSearch"})),e({type:"token",data:o}))}export{Ft as a};
//# sourceMappingURL=chunk-DATME33X.js.map
