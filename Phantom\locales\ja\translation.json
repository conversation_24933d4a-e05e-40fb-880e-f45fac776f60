{"commandAdd": "追加", "commandAccept": "同意", "commandApply": "適用", "commandApprove": "承認", "commandAllow": "許可する", "commandBack": "戻る", "commandBuy": "購入", "commandCancel": "キャンセル", "commandClaim": "受け取る", "commandClaimReward": "報酬を受け取る", "commandClear": "消去", "commandClose": "閉じる", "commandConfirm": "確認", "commandConnect": "接続", "commandContinue": "続行", "commandConvert": "変換", "commandCopy": "コピー", "commandCopyAddress": "アドレスをコピー", "commandCopyTokenAddress": "トークンアドレスをコピー", "commandCreate": "作成", "commandCreateTicket": "チケットを作成", "commandDeny": "拒否", "commandDismiss": "無視する", "commandDontAllow": "許可しない", "commandDownload": "ダウンロード", "commandEdit": "編集", "commandEditProfile": "プロフィールを編集", "commandEnableNow": "今すぐ有効化", "commandFilter": "フィルターをかける", "commandFollow": "フォローする", "commandHelp": "ヘルプ", "commandLearnMore": "詳細", "commandLearnMore2": "詳細", "commandMint": "ミント", "commandMore": "さらに表示", "commandNext": "次へ", "commandNotNow": "後で", "commandOpen": "開く", "commandOpenSettings": "設定を開く", "commandPaste": "貼り付ける", "commandReceive": "受信", "commandReconnect": "再接続", "commandRecordVideo": "動画を録画", "commandRequest": "リクエスト", "commandRetry": "再試行", "commandReview": "確認", "commandRevoke": "取り消し", "commandSave": "保存", "commandScanQRCode": "QR コードのスキャン", "commandSelect": "選択", "commandSelectMedia": "メディアを選択", "commandSell": "売却", "commandSend": "送信", "commandShare": "共有", "commandShowBalance": "残高を表示", "commandSign": "サインする", "commandSignOut": "Sign Out", "commandStake": "ステーク", "commandMintLST": "JitoSOL をミントする", "commandSwap": "スワップ", "commandSwapAgain": "もう一度スワップ", "commandTakePhoto": "写真を撮る", "commandTryAgain": "再試行", "commandViewTransaction": "トランザクションの表示", "commandReportAsNotSpam": "スパムではないと報告", "commandReportAsSpam": "スパムとして報告する", "commandPin": "固定", "commandBlock": "ブロック", "commandUnblock": "ブロック解除", "commandUnstake": "ステーク解除", "commandUnpin": "固定解除", "commandHide": "非表示にする", "commandUnhide": "表示する", "commandBurn": "バーン", "commandReport": "報告", "commandView": "表示", "commandProceedAnywayUnsafe": "続行する（危険）", "commandUnfollow": "フォローを解除する", "commandUnwrap": "アンラップする", "commandConfirmUnsafe": "確認（危険）", "commandYesConfirmUnsafe": "はい、確認します（危険）", "commandConfirmAnyway": "それでも確定する", "commandReportIssue": "問題を報告する", "commandSearch": "検索する", "commandShowMore": "さらに表示", "commandShowLess": "折り畳み表示", "pastParticipleClaimed": "受け取り済み", "pastParticipleCompleted": "完了", "pastParticipleCopied": "コピーしました", "pastParticipleDone": "完了", "pastParticipleDisabled": "無効", "pastParticipleRequested": "リクエスト済み", "nounName": "名前", "nounNetwork": "ネットワーク", "nounNetworkFee": "ネットワーク手数料", "nounSymbol": "シンボル", "nounType": "タイプ", "nounDescription": "説明", "nounYes": "はい", "nounNo": "いいえ", "amount": "金額", "limit": "制限", "new": "新機能", "gotIt": "分かりました", "internal": "内部使用", "reward": "報酬", "seeAll": "詳細表示", "seeLess": "折り畳み表示", "viewAll": "全て表示", "homeTab": "ホーム", "collectiblesTab": "コレクティブル", "swapTab": "スワップ", "activityTab": "活動", "exploreTab": "エクスプローラー", "accountHeaderConnectedInterpolated": "{{origin}} に接続されています", "accountHeaderConnectedToSite": "このサイトに接続されています", "accountHeaderCopyToClipboard": "クリップボードにコピー", "accountHeaderNotConnected": "次に接続されていません:", "accountHeaderNotConnectedInterpolated": "{{origin}} に接続されていません", "accountHeaderNotConnectedToSite": "このサイトに接続されていません", "accountWithoutEnoughSolActionButtonCancel": "キャンセル", "accountWithoutEnoughSolPrimaryText": "SOL が足りません", "accountWithoutEnoughSolSecondaryText": "このトランザクションに関わるアカウントに SOL が足りていません。あなたのアカウントか相手のアカウントである可能性があります。このトランザクションは送信すると取り消されます。", "accountSwitcher": "アカウントの切り替え", "addAccountHardwareWalletPrimaryText": "ハードウェアウォレットの接続", "addAccountHardwareWalletSecondaryText": "Ledger ハードウエアを使用する", "addAccountHardwareWalletSecondaryTextMobile": "{{supportedHardwareWallets}}ウォレットを使用", "addAccountSeedVaultWalletPrimaryText": "シード保管庫を接続", "addAccountSeedVaultWalletSecondaryText": "シード保管庫のウォレットを使用", "addAccountImportSeedPhrasePrimaryText": "シークレットリカバリフレーズのインポート", "addAccountImportSeedPhraseSecondaryText": "別のウォレットからのアカウントのインポート", "addAccountImportWalletPrimaryText": "秘密鍵のインポート", "addAccountImportWalletSecondaryText": "単一チェーンのアカウントのインポート", "addAccountImportWalletSolanaSecondaryText": "Solana の秘密鍵のインポート", "addAccountLimitReachedText": "Phantom の{{accountsCount}}のアカウント上限に達しまた。使用されていないアカウントを削除してから新しいアカウントを追加してください。", "addAccountNoSeedAvailableText": "利用可能なシードフレーズがありません。既存のシードをインポートしてアカウントを生成してください。", "addAccountNewWalletPrimaryText": "新規アカウントの作成", "addAccountNewWalletSecondaryText": "新規ウォレットアドレスの生成", "addAccountNewMultiChainWalletSecondaryText": "新規マルチチェーンアカウントの追加", "addAccountNewSingleChainWalletSecondaryText": "新規アカウントの追加", "addAccountPrimaryText": "ウォレットの追加 / 接続", "addAccountSecretPhraseLabel": "シークレットフレーズ", "addAccountSeedLabel": "シード", "addAccountSeedIDLabel": "シード ID", "addAccountSecretPhraseDefaultLabel": "シークレットフレーズ {{number}}", "addAccountPrivateKeyDefaultLabel": "秘密鍵 {{number}}", "addAccountZeroAccountsForSeed": "0 個のアカウント", "addAccountShowAccountForSeed": "アカウントを 1 個表示する", "addAccountShowAccountsForSeed": "アカウントを {{numOfAccounts}} 個表示する", "addAccountHideAccountForSeed": "アカウントを 1 個非表示にする", "addAccountHideAccountsForSeed": "アカウントを {{numOfAccounts}} 個非表示にする", "addAccountSelectSeedDescription": "このシークレットフレーズを使ってあなたの新しいアカウントを生成します", "addAccountNumAccountsForSeed": "{{numOfAccounts}} 個のアカウント", "addAccountOneAccountsForSeed": "1 個のアカウント", "addAccountGenerateAccountFromSeed": "アカウントの作成", "addAccountReadOnly": "アドレスを監視", "addAccountReadOnlySecondaryText": "任意の公開ウォレットアドレスを追跡できます", "addAccountSolanaAddress": "Solana アドレス", "addAccountEVMAddress": "EVM アドレス", "addAccountBitcoinAddress": "Bitcoin アドレス", "addAccountCreateSeedTitle": "新規アカウントの作成", "addAccountCreateSeedExplainer": "あなたのウォレットにはまだシークレットフレーズがありません。新しいウォレットが作成できるように、復元用のシークレットフレーズを生成します。書き留めて保管してください。", "addAccountSecretPhraseHeader": "あなたのシークレットフレーズ", "addAccountNoSecretPhrases": "シークレットフレーズはありません", "addAccountImportAccountActionButtonImport": "インポート", "addAccountImportAccountDuplicatePrivateKey": "このアカウントはすでにウォレットに存在します", "addAccountImportAccountIncorrectFormat": "書式が不正です", "addAccountImportAccountInvalidPrivateKey": "秘密鍵が無効です", "addAccountImportAccountName": "名前", "addAccountImportAccountPrimaryText": "秘密鍵のインポート", "addAccountImportAccountPrivateKey": "秘密鍵", "addAccountImportAccountPublicKey": "アドレスまたはドメイン", "addAccountImportAccountPrivateKeyRequired": "秘密鍵は必須です", "addAccountImportAccountNameRequired": "名前は必須です", "addAccountImportAccountPublicKeyRequired": "公開アドレスが必要です", "addAccountImportAccountDuplicateAddress": "このアドレスはすでにウォレットに存在します", "addAddressAddressAlreadyAdded": "アドレスはすでに追加されています", "addAddressAddressAlreadyExists": "アドレスはすでに存在します", "addAddressAddressInvalid": "アドレスが無効です", "addAddressAddressIsRequired": "アドレスは必須です", "addAddressAddressPlaceholder": "アドレス", "addAddressLabelIsRequired": "ラベルが必要です", "addAddressLabelPlaceholder": "ラベル", "addAddressPrimaryText": "アドレスの追加", "addAddressToast": "アドレスを追加しました", "createAssociatedTokenAccountCostLabelInterpolated": "これは {{solAmountFormatted}} SOL かかります", "createAssociatedTokenAccountErrorAccountExists": "すでにこのトークンアカウントを所有しています", "createAssociatedTokenAccountErrorInsufficientFunds": "資金不足", "createAssociatedTokenAccountErrorInvalidMint": "ミントアドレスが無効です", "createAssociatedTokenAccountErrorInvalidName": "名前が無効です", "createAssociatedTokenAccountErrorInvalidSymbol": "シンボルが無効です", "createAssociatedTokenAccountErrorUnableToCreateMessage": "トークンアカウントを作成できませんでした。もう一度お試しください。", "createAssociatedTokenAccountErrorUnableToCreateTitle": "アカウントの作成に失敗しました", "createAssociatedTokenAccountErrorUnableToSendMessage": "トランザクションの送信に失敗しました。", "createAssociatedTokenAccountErrorUnableToSendTitle": "トランザクションを送信できませんでした", "createAssociatedTokenAccountInputPlaceholderMint": "ミントアドレス", "createAssociatedTokenAccountInputPlaceholderName": "名前", "createAssociatedTokenAccountInputPlaceholderSymbol": "シンボル", "createAssociatedTokenAccountLoadingMessage": "トークンアカウントを作成しています。", "createAssociatedTokenAccountLoadingTitle": "トークンアカウントを作成しています", "createAssociatedTokenAccountPageHeader": "トークンアカウントの作成", "createAssociatedTokenAccountSuccessMessage": "トークンアカウントを正常に作成しました！", "createAssociatedTokenAccountSuccessTitle": "トークンアカウントを作成しました", "createAssociatedTokenAccountViewTransaction": "トランザクションの表示", "assetDetailRecentActivity": "最近の活動", "assetDetailStakeSOL": "SOL のステーク", "assetDetailUnknownToken": "不明なトークン", "assetDetailUnwrapAll": "すべてアンラップ", "assetDetailUnwrappingSOL": "SOL をアンラップ中", "assetDetailUnwrappingSOLFailed": "SQL アンラップに失敗しました", "assetDetailViewOnExplorer": "{{explorer}} で表示", "assetDetailViewOnExplorerDefaultExplorer": "エクスプローラー", "assetDetailSaveToPhotos": "写真に保存", "assetDetailSaveToPhotosToast": "写真に保存しました", "assetDetailPinCollection": "コレクションをピン", "assetDetailUnpinCollection": "コレクションをピン解除", "assetDetailHideCollection": "コレクションを非表示化", "assetDetailUnhideCollection": "コレクションの非表示化解除", "assetDetailTokenNameLabel": "トークン名", "assetDetailNetworkLabel": "ネットワーク", "assetDetailAddressLabel": "アドレス", "assetDetailPriceLabel": "価格", "collectibleDetailSetAsAvatar": "アバターに設定", "collectibleDetailSetAsAvatarSingleWorkAlt": "アバター", "collectibleDetailSetAsAvatarSuccess": "アバターを設定しました", "collectibleDetailShare": "コレクティブルを共有", "assetDetailTokenAddressCopied": "アドレスをコピーしました", "assetDetailStakingLabel": "ステーク", "assetDetailAboutLabel": "{{fungibleName}} について", "assetDetailPriceDetail": "価格の詳細", "assetDetailHighlights": "ハイライト", "assetDetailAllTimeReturn": "全期間の運用益", "assetDetailAverageCost": "平均費用", "assetDetailPriceHistoryUnavailable": "このトークンの価格履歴がありません", "assetDetailPriceHistoryInsufficientData": "この期間の価格履歴は利用できません", "assetDetailPriceDataUnavailable": "価格データがありません", "assetDetailPriceHistoryError": "価格履歴を取得する際にエラーが発生しました", "assetDetailPriceHistoryNow": "現在", "assetDetailTimeFrame1D": "1日", "assetDetailTimeFrame24h": "過去24時間の価格", "assetDetailTimeFrame1W": "1週間", "assetDetailTimeFrame1M": "1ヶ月", "assetDetailTimeFrameYTD": "年初来", "assetDetailTimeFrameAll": "全て", "sendAssetAmountLabelInterpolated": "{{amount}}{{tokenSymbol}} が利用可能", "fiatRampQuotes": "時価", "fiatRampNewQuote": "新しい時価", "assetListSelectToken": "トークンを選択", "assetListSearch": "検索...", "assetListUnknownToken": "不明なトークン", "buyFlowHealthWarning": "一部の決済プロバイダーでトラフィックが集中しているため、入金が数時間遅れる場合があります。", "assetVisibilityUnknownToken": "不明なトークン", "buyAssetInterpolated": "{{tokenSymbol}} を購入", "buyAssetScreenMaxPurchasePriceInterpolated": "最大購入額: {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "最低購入額: {{amount}}", "buyNoAssetsAvailable": "利用可能な Ethereum または Polygon がありません", "buyThirdPartyScreenPaymentMethodSelector": "支払方法：", "buyThirdPartyScreenPaymentMethod": "支払方法を選択", "buyThirdPartyScreenChoseQuote": "見積もりに有効な金額を入力してください", "buyThirdPartyScreenProviders": "プロバイダー", "buyThirdPartyScreenPaymentMethodTitle": "支払い方法", "buyThirdPartyScreenPaymentMethodEmptyState": "お住まいの地域で利用できる支払方法がありません", "buyThirdPartyScreenPaymentMethodFooter": "支払いはネットワークパートナーによって行われます。手数料は異なる場合があります。一部の支払い方法は、お住まいの地域ではご利用いただけません。", "buyThirdPartyScreenProvidersEmptyState": "お住まいの地域で利用できるプロバイダーがありません", "buyThirdPartyScreenLoadingQuote": "時価を読み込み中...", "buyThirdPartyScreenViewQuote": "時価を表示する", "gasEstimationErrorWarning": "トランザクションの手数料を見積もる際に不具合が発生しました。トランザクションに失敗する可能性があります。", "gasEstimationCouldNotFetch": "GAS の推定値を取得できませんでした", "networkFeeCouldNotFetch": "ネットワーク手数料を取得できませんでした", "nativeTokenBalanceErrorWarning": "このトランザクションではあなたのトークン残高を取得できませんでした。トランザクションに失敗する可能性があります。", "blocklistOriginCommunityDatabaseInterpolated": "このサイトは既知のフィッシングサイトや詐欺に関する<1>コミュニティ管理のデータベース</1>に登録されています。このサイトが誤って登録されていると思われる場合は、<3>問題を報告</3>してください。", "blocklistOriginDomainIsBlocked": "{{domainName}} はブロックされています！", "blocklistOriginIgnoreWarning": "警告を無視して {{domainName}} に移動する。", "blocklistOriginSiteIsMalicious": "Phantom はこの Web サイトに悪意があり、使用するのは危険だと判断しました。", "blocklistOriginThisDomain": "このドメイン", "blocklistProceedAnyway": "警告を無視して継続", "maliciousTransactionWarning": "Phantom はこのトランザクションに悪意があり、使用するのは危険だと判断しました。あなた自身と資金を保護するため、このトランザクションに署名する機能を無効にしました。", "maliciousTransactionWarningIgnoreWarning": "警告を無視して継続", "maliciousTransactionWarningTitle": "トランザクションはフラグが立てられました!", "maliciousRequestBlockedTitle": "リクエストがブロックされました", "maliciousRequestWarning": "この Web サイトは悪意があると報告されています。資金を盗んだり、不正なリクエストを確認するように騙したりしようとしている可能性があります。", "maliciousSignatureRequestBlocked": "安全のため、Phantom はこのリクエストをブロックしました。", "maliciousRequestBlocked": "安全のため、Phantom はこのリクエストをブロックしました。", "maliciousRequestFrictionDescription": "リクエストは、安全でないため、Phantom によりブロックされました。このダイアログを閉じてください。", "maliciousRequestAcknowledge": "このWeb サイトを使用すると資金を全て失う可能性があることを理解しています。", "maliciousRequestAreYouSure": "本当に続行しますか？", "siwErrorPopupTitle": "無効な署名リクエスト", "siwParseErrorDescription": "アプリの署名リクエストのフォーマットが無効のため表示できません。", "siwVerificationErrorDescription": "メッセージ署名リクエストで1つ以上のエラーがありました。セキュリティのため、正しいアプリを使用しているか確認の上、再試行してください。", "siwErrorPagination": "{{total}} の {{n}}", "siwErrorMessage_ADDRESS_MISMATCH": "警告: アプリのアドレスは署名のため提供されたアドレスと一致していません。", "siwErrorMessage_DOMAIN_MISMATCH": "警告: アプリのドメインは確認のために提供されたドメインと一致していません。", "siwErrorMessage_URI_MISMATCH": "警告: URI ホスト名はドメインと一致していません。", "siwErrorMessage_CHAIN_ID_MISMATCH": "警告: チェーン ID は確認のために提供されたチェーン ID と一致していません。", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "警告: メッセージ発行日が古過ぎます。", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "警告: メッセージ発行日が遠過ぎます。", "siwErrorMessage_EXPIRED": "警告: メッセージの期限が切れました。", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "警告: メッセージは発行される前に期限が切れます。", "siwErrorMessage_VALID_AFTER_EXPIRATION": "警告: メッセージは有効になる前に期限が切れます。", "siwErrorShowErrorDetails": "エラーの詳細を表示", "siwErrorHideErrorDetails": "エラーの詳細を隠す", "siwErrorIgnoreWarning": "警告を無視して継続", "siwsTitle": "サインインリクエスト", "siwsPermissions": "許可", "siwsAgreement": "メッセージ", "siwsAdvancedDetails": "詳細", "siwsAlternateStatement": "{{domain}} に次の Solana アカウントでサインするように求められています：\n{{address}}", "siwsFieldLable_domain": "ドメイン", "siwsFieldLable_address": "アドレス", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "バージョン", "siwsFieldLable_chainId": "チェーン ID", "siwsFieldLable_nonce": "ノンス", "siwsFieldLable_issuedAt": "発行時間", "siwsFieldLable_expirationTime": "有効期限", "siwsFieldLable_requestId": "リクエスト ID", "siwsFieldLable_resources": "リソース", "siwsVerificationErrorDescription": "サインインのリクエストが無効です。サイトが安全ではないか、リクエストを送信する際に開発者が誤ってしまった可能性があります。", "siwsErrorNumIssues": "{{n}} 件の問題", "siwsErrorMessage_CHAIN_ID_MISMATCH": "チェーン ID が現在のネットワークに一致しません", "siwsErrorMessage_DOMAIN_MISMATCH": "サインインしようとしているドメインと異なるドメインです。", "siwsErrorMessage_URI_MISMATCH": "サインインしようとしている URI と異なる URI です。", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "メッセージ発行日が古過ぎます。", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "メッセージ発行日が遠過ぎます。", "siwsErrorMessage_EXPIRED": "メッセージの有効期限が切れました。", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "メッセージは発行される前に期限が切れます。", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "メッセージは有効になる前に期限が切れます。", "changeLockTimerPrimaryText": "自動ロックタイマー", "changeLockTimerSecondaryText": "アイドル状態になってからウォレットをロックするまでの時間を指定してください", "changeLockTimerToast": "自動ロックタイマーを更新しました", "changePasswordConfirmNewPassword": "新しいパスワードの確認", "changePasswordCurrentPassword": "現在のパスワード", "changePasswordErrorIncorrectCurrentPassword": "現在のパスワードが正しくありません", "changePasswordErrorGeneric": "不具合が発生しました。後で再試行してください。", "changePasswordNewPassword": "新しいパスワード", "changePasswordPrimaryText": "パスワードの変更", "changePasswordToast": "パスワードを更新しました", "collectionsSpamCollections": "スパムコレクション", "collectionsHiddenCollections": "非表示化されたコレクション", "collectiblesReportAsSpam": "スパムとして報告", "collectiblesReportAsSpamAndHide": "スパムとして報告して隠す", "collectiblesReportAsNotSpam": "スパムではないと報告", "collectiblesReportAsNotSpamAndUnhide": "再表示してスパムではないことを報告する", "collectiblesReportNotSpam": "スパムではない", "collectionsManageCollectibles": "コレクティブルのリストを管理", "collectibleDetailDescription": "説明", "collectibleDetailProperties": "プロパティ", "collectibleDetailOrdinalInfo": "Ordinal 情報", "collectibleDetailRareSatsInfo": "Rare Sats 情報", "collectibleDetailSatsInUtxo": "UTXO での Sats", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Sat 番号", "collectibleDetailSatName": "Sat の名前", "collectibleDetailInscriptionId": "インスクリプション ID", "collectibleDetailInscriptionNumber": "インスクリプション 番号", "collectibleDetailStandard": "標準", "collectibleDetailCreated": "作成しました", "collectibleDetailViewOnExplorer": "{{explorer}} で表示", "collectibleDetailList": "上場する", "collectibleDetailSellNow": "{{amount}}{{symbol}} で売却", "collectibleDetailUtxoSplitterCtaTitle": "余った Bitcoin を解放", "collectibleDetailUtxoSplitterCtaSubtitle": "ロックを解除できる BTC が {{value}} 分あります", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "資金を保護するために、Rare Sat を含む UTXO の BTC が送信されるのを防ぎます。Magic Eden の UTXO スプリッターを使用して、Rare Sat から {{value}} の BTC を解放してください。", "collectibleDetailUtxoSplitterModalCtaButton": "UTXO スプリッターを使用", "collectibleDetailEasilyAccept": "最高額のオファーを受け入れる", "collectibleDetailSatsCount_one": "{{count}} Sat", "collectibleDetailSatsCount_other": "{{count}} Sat", "collectibleDetailSpamOverlayDescription": "このコレクティブルは、Phantom にスパムとして分類されたため、非表示にされました。", "collectibleDetailSpamOverlayReveal": "コレクティブルを表示", "collectibleBurnTermsOfService": "このアクションが不可逆であることを理解しています", "collectibleBurnTitleWithCount_one": "トークンをバーンする", "collectibleBurnTitleWithCount_other": "トークンをバーンする", "collectibleBurnDescriptionWithCount_one": "このアクションはこのトークンをあなたのウォレットから永久的に破壊し削除します。", "collectibleBurnDescriptionWithCount_other": "このアクションはこれらのトークンをあなたのウォレットから永久的に破壊し削除します。", "collectibleBurnTokenWithCount_one": "トークン", "collectibleBurnTokenWithCount_other": "トークン", "collectibleBurnCta": "バーン", "collectibleBurnRebate": "リベート", "collectibleBurnRebateTooltip": "このトークンをバーンすることで、少額のSOLがあなたのウォレットに自動で入金されます。", "collectibleBurnNetworkFee": "ネットワーク手数料", "collectibleBurnNetworkFeeTooltip": "Solana がトランザクション処理に必要とする金額", "unwrapButtonSwapTo": "{{chainSymbol}} へスワップ", "unwrapButtonWithdrawFrom": "{{chainSymbol}} 用に {{withdrawalSource}} から出金", "unwrapModalEstimatedTime": "予想時間", "unwrapModalNetwork": "ネットワーク", "unwrapModalNetworkFee": "ネットワーク手数料", "unwrapModalTitle": "概要", "unsupportedChain": "サポートされていないチェーン", "unsupportedChainDescription": "{{chainName}} ネットワークでは {{action}} はサポートされていません。", "networkFeesTooltipLabel": "{{chainName}} ネットワークの手数料", "networkFeesTooltipDescription": "{{chainName}} 手数料は複数の要素に基づいています。カスタマイズしてトランザクションを高速化（より高価）または低速化（より安価）できます。", "burnStatusErrorTitleWithCount_one": "トークンのバーンに失敗しました", "burnStatusErrorTitleWithCount_other": "トークンのバーンに失敗しました", "burnStatusSuccessTitleWithCount_one": "トークンがバーンされました！", "burnStatusSuccessTitleWithCount_other": "トークンがバーンされました！", "burnStatusLoadingTitleWithCount_one": "トークンをバーン中…", "burnStatusLoadingTitleWithCount_other": "トークンをバーン中…", "burnStatusErrorMessageWithCount_one": "このトークンはバーンできませんでした。後でもう一度お試しください。", "burnStatusErrorMessageWithCount_other": "これらのトークンはバーンできませんでした。後でもう一度お試しください。", "burnStatusSuccessMessageWithCount_one": "このトークンは永久に破壊され、{{rebateAmount}}SOLがウォレットに入金されました。", "burnStatusSuccessMessageWithCount_other": "これらのトークンは永久に破壊され、{{rebateAmount}}SOLがウォレットに入金されました。", "burnStatusLoadingMessageWithCount_one": "このトークンは永久に破壊され、{{rebateAmount}}SOLがウォレットに入金されます。", "burnStatusLoadingMessageWithCount_other": "これらのトークンは永久に破壊され、{{rebateAmount}}SOLがウォレットに入金されます。", "burnStatusViewTransactionText": "トランザクションの表示", "collectibleDisplayLoading": "読み込み中...", "collectiblesNoCollectibles": "コレクティブルはありません", "collectiblesPrimaryText": "所有コレクティブル", "collectiblesReceiveCollectible": "コレクティブルの受信", "collectiblesUnknownCollection": "不明なコレクション", "collectiblesUnknownCollectible": "不明なコレクティブル", "collectiblesUniqueHolders": "ユニークな保有者", "collectiblesSupply": "供給", "collectiblesUnknownTokens": "不明なトークン", "collectiblesNrOfListed": "上場済み{{ nrOfListed }}件", "collectiblesListed": "上場済み", "collectiblesMintCollectible": "コレクティブルをミント", "collectiblesYouMint": "ミントしました", "collectiblesMintCost": "ミントのコスト", "collectiblesMintFail": "ミントに失敗しました", "collectiblesMintFailMessage": "コレクティブルをミントする際に不具合が発生しました。もう一度お試しください。", "collectiblesMintCostFree": "無料", "collectiblesMinting": "ミント中…", "collectiblesMintingMessage": "コレクティブルをミント中", "collectiblesMintShareSubject": "これをチェックしてください", "collectiblesMintShareMessage": "@phantom でこれをミントしました！", "collectiblesMintSuccess": "正常にミントしました", "collectiblesMintSuccessMessage": "コレクティブルをミントしました", "collectiblesMintSuccessQuestMessage": "Phantom Quest の条件を満たしました。[報酬を受け取る] をタップして無料のコレクティブルを入手しましょう。", "collectiblesMintRequired": "必須", "collectiblesMintMaxLengthErrorMessage": "最大長さを超過しました", "collectiblesMintSafelyDismiss": "このウィンドウを安全に閉じることができます。", "collectiblesTrimmed": "現在表示できるコレクティブルの上限数に達しました。", "collectiblesNonTransferable": "転送不可", "collectiblesNonTransferableYes": "はい", "collectiblesSellOfferDetails": "オファーの詳細", "collectiblesSellYouSell": "売却額：", "collectiblesSellGotIt": "分かりました", "collectiblesSellYouReceive": "受領額：", "collectiblesSellOffer": "オファーする", "collectiblesSoldCollectible": "販売したコレクティブル", "collectiblesSellMarketplace": "マーケットプレイス", "collectiblesSellCollectionFloor": "コレクションのフロア価格", "collectiblesSellDifferenceFromFloor": "フロア価格との差", "collectiblesSellLastSalePrice": "終値", "collectiblesSellEstimatedFees": "予想手数料", "collectiblesSellEstimatedProfitAndLoss": "予想の利益・損失", "collectiblesSellViewOnMarketplace": "{{marketplace}} で表示", "collectiblesSellCollectionFloorTooltip": "複数の市場で最も低い「今すぐ購入」価格。", "collectiblesSellProfitLossTooltip": "推定利益/損失は、最後の販売価格と手数料を差し引いたオファー額に基づいて計算されます。", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "ロイヤルティ（{{royaltiesPercentage}}）", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "市場の手数料（{{marketplaceFeePercentage}}）", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "マーケットプレイス手数料", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}} ネットワーク", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "時価には {{phantomFeePercentage}} の Phantom 手数料が含まれています", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "時価には、ロイヤルティ、ネットワーク手数料、マーケットプレイス手数料、および {{phantomFeePercentage}} Phantom 手数料が含まれます", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "時価には、ロイヤルティ、ネットワーク手数料、マーケットプレイス手数料が含まれます", "collectiblesSellTransactionFeeTooltipTitle": "トランザクション手数料", "collectiblesSellStatusLoadingTitle": "オファーを受け入れています…", "collectiblesSellStatusLoadingIsSellingFor": "の価格は", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} を売却しました！", "collectiblesSellStatusSuccessWasSold": "の売却が完了しました", "collectiblesSellStatusErrorTitle": "不具合が発生しました", "collectiblesSellStatusErrorSubtitle": "売却する際にエラーが発生しました", "collectiblesSellStatusViewTransaction": "トランザクションの表示", "collectiblesSellInsufficientFundsTitle": "資金不足", "collectiblesSellInsufficientFundsSubtitle": "ネットワーク代を支払うための資金が不足しているため、このコレクティブルのオファーを受け入れることができませんでした。", "collectiblesSellRecentlyTransferedNFTTitle": "最近転送した項目", "collectiblesSellRecentlyTransferedNFTSubtitle": "転送を行ってから入札を受け入れらるようになるまで 1 時間かかります。", "collectiblesApproveCollection": "{{collectionName}} が承認されました", "collectiblesSellNotAvailableAnymoreTitle": "オファーは利用不可です", "collectiblesSellNotAvailableAnymoreSubtitle": "このオファーは利用できなくなりました。この入札をキャンセルしてもう一度お試しください", "collectiblesSellFlaggedTokenTitle": "コレクティブルはフラグが立てられています", "collectiblesSellFlaggedTokenSubtitle": "このコレクティブルは取引できません。盗難の報告があったり、ロックアップなしでステークされていたりするなどの理由が考えられます", "collectiblesListOnMagicEden": "Magic Edenに上場する", "collectiblesListPrice": "上場の価格", "collectiblesUseFloor": "フロアを使用", "collectiblesFloorPrice": "フロアプライス", "collectiblesLastSalePrice": "最終セール価格", "collectiblesTotalReturn": "合計リターン", "collectiblesOriginalPurchasePrice": "元の購入価格", "collectiblesMagicEdenFee": "Magic Edenの手数料", "collectiblesArtistRoyalties": "アーテイストのロイヤリティ", "collectiblesListNowButton": "今すぐ上場する", "collectiblesListAnywayButton": "このまま上場させる", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "上場の表示", "collectiblesListingViewTransaction": "トランザクションの表示", "collectiblesRemoveListing": "上場廃止", "collectiblesEditListing": "上場の編集", "collectiblesEditListPrice": "上場の価格を編集", "collectiblesListPriceTooltip": "上場の価格は、商品の販売価格です。販売者は通常、上場の価格をフロアプライス以上に設定します。", "collectiblesFloorPriceTooltip": "フロアプライスは、このコレクションの最安の商品の有効な上場の価格です。", "collectiblesOriginalPurchasePriceTooltip": "このアイテムを元々この価格で購入しました。", "collectiblesPurchasedForSol": "{{lastPurchasePrice}} SOLで購入済み", "collectiblesUnableToLoadListings": "上場コレクティブルを読み込めません", "collectiblesUnableToLoadListingsFrom": "{{marketplace}}の上場コレクティブルを読み込めません", "collectiblesUnableToLoadListingsDescription": "あなたの上場コレクティブルや資産は安全ですが、今現在{{marketplace}}から読み込むことができませんでした。後でもう一度お試しください。", "collectiblesBelowFloorPrice": "フロアプライス以下", "collectiblesBelowFloorPriceMessage": "本当にNFTをフロアプライス以下の価格で上場させますか？", "collectiblesMinimumListingPrice": "最低価格は0.01 SOLです", "collectiblesMagicEdenFeeTooltip": "Magic Edenは、完了したトランザクションに対して手数料を取ります。", "collectiblesArtistRoyaltiesTooltip": "このコレクションの作成者は、完了した取引ごとに何パーセントかのロイヤリティを受け取ります。", "collectibleScreenCollectionLabel": "コレクション", "collectibleScreenPhotosPermissionTitle": "写真のアクセス権限", "collectibleScreenPhotosPermissionMessage": "あなたの写真をアクセスするために許可が必要です。設定に移動し、アクセス権限を更新してください。", "collectibleScreenPhotosPermissionOpenSettings": "設定を開く", "listStatusErrorTitle": "上場に失敗しました", "editListStatusErrorTitle": "更新できません", "removeListStatusErrorTitle": "上場廃止に失敗しました", "listStatusSuccessTitle": "上場しました！", "editListingStatusSuccessTitle": "上場情報を更新しました！", "removeListStatusSuccessTitle": "Magic Edenから上場廃止しました", "listStatusLoadingTitle": "上場中です…", "editListingStatusLoadingTitle": "上場情報を更新中です…", "removeListStatusLoadingTitle": "上場廃止中です…", "listStatusErrorMessage": "{{name}}をMagic Edenに上場できませんでした", "removeListStatusErrorMessage": "{{name}}をMagic Edenから上場廃止できませんでした", "listStatusSuccessMessage": "Magic Edenに{{name}}を{{listCollectiblePrice}} SOLで上場しました", "editListingStatusSuccessMessage": "Magic Edenで{{name}}を{{editListCollectiblePrice}} SOLに更新しました", "removeListStatusSuccessMessage": "Magic Edenから正常に{{name}}を上場廃止しました", "listStatusLoadingMessage": "Magic Edenに{{name}}を{{listCollectiblePrice}}SOLで上場中です。", "editListingStatusLoadingMessage": "Magic Edenで{{name}}を{{editListCollectiblePrice}}SOLに更新中です。", "removeListStatusLoadingMessage": "{{name}}をMagic Edenから上場廃止しています。これには少々時間がかかるかもしれません。", "listStatusLoadingSafelyDismiss": "このウィンドウを安全に閉じることができます。", "listStatusViewOnMagicEden": "Magic Edenで表示する", "listStatusViewOnMarketplace": "{{marketplace}} で表示", "listStatusLoadingDismiss": "無視する", "listStatusViewTransaction": "トランザクションの表示", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "ハードウェアウォレットを接続して、ロック解除されていることを確認してください。こちらでウォレットを検出次第、使用アドレスを選択できるようになります。", "connectHardwareFailedPrimaryText": "接続に失敗しました", "connectHardwareFailedSecondaryText": "ハードウェアウォレットを接続して、ロック解除されていることを確認してください。こちらでウォレットを検出次第、使用アドレスを選択できるようになります。", "connectHardwareFinishPrimaryText": "アカウントが追加されました！", "connectHardwareFinishSecondaryText": "Phantom 内から Ledger Nano ウォレットがアクセス可能になりました。ブラウザにお戻りください。", "connectHardwareNeedsPermissionPrimaryText": "新規ウォレットの接続", "connectHardwareNeedsPermissionSecondaryText": "下のボタンをクリックして、接続プロセスを開始してください。", "connectHardwareSearchingPrimaryText": "ウォレットを検索中…", "connectHardwareSearchingSecondaryText": "ハードウェアウォレットを接続し、それがロック解除されていて、ブラウザーで許可を承認していることを確認してください。", "connectHardwarePermissionDeniedPrimary": "アクセスが拒否されました", "connectHardwarePermissionDeniedSecondary": "Phantom がお使いの Ledger デバイスに接続する許可をしてください", "connectHardwarePermissionUnableToConnect": "接続できません", "connectHardwarePermissionUnableToConnectDescription": "お使いの Ledger デバイスに接続できませんでした。追加のアクセス権限が必要となる場合があります。", "connectHardwareSelectAddressAllAddressesImported": "すべてのアドレスがインポートされました", "connectHardwareSelectAddressDerivationPath": "導出パス", "connectHardwareSelectAddressSearching": "検索中...", "connectHardwareSelectAddressSelectWalletAddress": "ウォレットアドレスを選択", "connectHardwareSelectAddressWalletAddress": "ウォレットアドレス", "connectHardwareWaitingForApplicationSecondaryText": "お使いのハードウェアウォレットを接続し、ロック解除されていることを確認してください。", "connectHardwareWaitingForPermissionPrimaryText": "許可が必要です", "connectHardwareWaitingForPermissionSecondaryText": "ハードウェアウォレットを接続し、それがロック解除されていて、ブラウザーで許可を承認していることを確認してください。", "connectHardwareAddAccountButton": "アカウントを追加", "connectHardwareLedger": "Ledger を接続", "connectHardwareStartConnection": "Ledger ハードウェアウォレットの接続を開始するには、以下のボタンをクリックしてください", "connectHardwarePairSuccessPrimary": "{{productName}} が接続されました", "connectHardwarePairSuccessSecondary": "{{productName}} の接続に成功しました。", "connectHardwareSelectChains": "接続するチェーンを選択", "connectHardwareSearching": "検索中...", "connectHardwareMakeSureConnected": "お使いのハードウェアウォレットを接続およびロック解除。関係するブラウザ権限を承認してください。", "connectHardwareOpenAppDescription": "お使いのハードウェアウォレットをロック解除してください。", "connectHardwareConnecting": "接続中…", "connectHardwareConnectingDescription": "お使いの Ledger デバイスに接続しています。", "connectHardwareConnectingAccounts": "お使いのアカウントを接続中…", "connectHardwareDiscoveringAccounts": "アカウントを検索中...", "connectHardwareDiscoveringAccountsDescription": "アカウントのアクティビティを確認しています。", "connectHardwareErrorLedgerLocked": "Ledger がロックされています", "connectHardwareErrorLedgerLockedDescription": "Ledger 端末のロックが解除されていることを確認してから再試行してください。", "connectHardwareErrorLedgerGeneric": "不具合が発生しました", "connectHardwareErrorLedgerGenericDescription": "アカウントが見つかりませんでした。Ledger 端末のロックが解除されていることを確認してから再試行してください。", "connectHardwareErrorLedgerPhantomLocked": "Phantom を閉じてから開き、もう一度ハードウェアを接続してみてください。", "connectHardwareFindingAccountsWithActivity": "{{chainName}} アカウントを確認中…", "connectHardwareFindingAccountsWithActivityDualChain": "{{chainName1}} または {{chainName2}} を検索中です…", "connectHardwareFoundAccountsWithActivity": "お使いの Ledger 内の {{numOfAccounts}} 個のアカウントでアクティビティが確認されました。", "connectHardwareFoundAccountsWithActivitySingular": "お使いの Ledger 内の 1 個のアカウントでアクティビティが確認されました。", "connectHardwareFoundSomeAccounts": "お使いの Ledger 端末でアカウントが見つかりました。", "connectHardwareViewAccounts": "アカウントを表示", "connectHardwareConnectAccounts": "アカウントが接続されました", "connectHardwareSelectAccounts": "アカウントを選択", "connectHardwareChooseAccountsToConnect": "接続するウォレットのアカウントを選択してください。", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} 個のアカウントが追加されました", "connectHardwareAccountsStepOfSteps": "ステップ {{stepNum}}/{{totalSteps}}", "connectHardwareMobile": "Ledger を接続", "connectHardwareMobileTitle": "Ledger ハードウエアウォレットの接続", "connectHardwareMobileEnableBluetooth": "Bluetooth を有効にする", "connectHardwareMobileEnableBluetoothDescription": "Bluetooth を使用した接続を許可", "connectHardwareMobileEnableBluetoothSettings": "[設定] に移動して、Phantom に [位置情報] および [付近のデバイス] の権限を許可してください。", "connectHardwareMobilePairWithDevice": "Ledger デバイスとペアリングする", "connectHardwareMobilePairWithDeviceDescription": "最良の信号を得るためにデバイスを近くに置いてください", "connectHardwareMobileConnectAccounts": "アカウントを接続", "connectHardwareMobileConnectAccountsDescription": "使用した可能性のあるアカウントでのアクティビティを確認します", "connectHardwareMobileConnectLedgerDevice": "Ledger デバイスを接続", "connectHardwareMobileLookingForDevices": "近くのデバイスを検索中です…", "connectHardwareMobileLookingForDevicesDescription": "お使いの Ledger デバイスを接続し、ロックが解除されていることを確認してください。", "connectHardwareMobileFoundDeviceSingular": "Ledger デバイスを 1 台検出しました", "connectHardwareMobileFoundDevices": "Ledger デバイスを {{numDevicesFound}} 台検出しました", "connectHardwareMobileFoundDevicesDescription": "以下から Ledger デバイスを選択してペアリングします", "connectHardwareMobilePairingWith": "{{deviceName}} とのペアリング中です", "connectHardwareMobilePairingWithDescription": "ペアリング中は、お使いの Ledger デバイスの指示に従ってください。", "connectHardwareMobilePairingFailed": "ペアリングに失敗しました", "connectHardwareMobilePairingFailedDescription": "{{device<PERSON>ame}} とペアリングできませんでした。お使いのデバイスのロックが解除されていることを確認してください。", "connectHardwareMobilePairingSuccessful": "ペアリングに成功しました", "connectHardwareMobilePairingSuccessfulDescription": "Ledger デバイスのペアリングおよび接続に成功しました。", "connectHardwareMobileOpenAppSingleChain": "{{chainName}} アプリをお使いの Ledger で開く", "connectHardwareMobileOpenAppDualChain": "{{chainName1}} または {{chainName2}} のアプリをお使いの Ledger で開く", "connectHardwareMobileOpenAppDescription": "お使いのデバイスのロックが解除されていることを確認してください。", "connectHardwareMobileStillCantFindDevice": "それでもデバイスが検出されませんか？", "connectHardwareMobileLostConnection": "接続が失われました", "connectHardwareMobileLostConnectionDescription": "{{deviceName}} への接続が失われました。お使いのデバイスのロックが解除されていることを確認してからもう一度お試しください。", "connectHardwareMobileGenericLedgerDevice": "Ledger デバイス", "connectHardwareMobileConnectDeviceSigning": "{{deviceName}} を接続してください", "connectHardwareMobileConnectDeviceSigningDescription": "お使いの Ledger デバイスのロックを解除して、近くに置いてください。", "connectHardwareMobileBluetoothDisabled": "Bluetooth が無効になっています", "connectHardwareMobileBluetoothDisabledDescription": "Bluetooth を有効にしてお使いの Ledger デバイスのロックが解除されていることを確認してください。", "connectHardwareMobileLearnMore": "詳細", "connectHardwareMobileBlindSigningDisabled": "ブラインド署名は無効になっています", "connectHardwareMobileBlindSigningDisabledDescription": "お使いのデバイスでブラインド署名が有効になっていることを確認してください。", "connectHardwareMobileConfirmSingleChain": "ハードウェアウォレットでトランザクションを確認する必要があります。ハードウェアウォレットのロックが解除されていることを確認してください。", "metamaskExplainerBottomSheetHeader": "このサイトは Phantom で利用できます", "metamaskExplainerBottomSheetSubheader": "[ウォレットの接続] のダイアログから [MetaMask] を選択します。", "metamaskExplainerBottomSheetDontShowAgain": "今度表示しない", "ledgerStatusNotConnected": "Ledger が接続されていません", "ledgerStatusConnectedInterpolated": "{{productName}} が接続されました", "connectionClusterInterpolated": "現在、所属しているクラスタ: {{cluster}}", "connectionClusterTestnetMode": "現在testnetモードです", "featureNotSupportedOnLocalNet": "Solana Localnet が有効化されている間、この機能は利用できません。", "readOnlyAccountBannerWarning": "このアカウントを監視しています", "depositAddress": "受信アドレス", "depositAddressChainInterpolated": "あなたの {{chain}} アドレス", "depositAssetDepositInterpolated": "{{tokenSymbol}} を受信", "depositAssetSecondaryText": "このアドレスは互換性のあるトークンを受信する目的でのみ使用できます。", "depositAssetTextInterpolated": "<1>{{network}}</1>でトークンとコレクティブルを受信するにはこのアドレスを使用してください。", "depositAssetTransferFromExchange": "取引所から転送", "depositAssetShareAddress": "アドレスをシェア", "depositAssetBuyOrDeposit": "購入または送金", "depositAssetBuyOrDepositDesc": "ウォレットに入金して始めましょう", "depositAssetTransfer": "転送", "editAddressAddressAlreadyAdded": "アドレスはすでに追加されています", "editAddressAddressAlreadyExists": "アドレスはすでに存在します", "editAddressAddressIsRequired": "アドレスは必須です", "editAddressPrimaryText": "アドレスを編集", "editAddressRemove": "アドレス帳から削除", "editAddressToast": "アドレスを更新しました", "removeSavedAddressToast": "アドレスを削除しました", "exportSecretErrorGeneric": "不具合が発生しました。後で再試行してください。", "exportSecretErrorIncorrectPassword": "パスワードが正しくありません", "exportSecretPassword": "パスワード", "exportSecretPrivateKey": "秘密鍵", "exportSecretSecretPhrase": "シークレットフレーズ", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "シークレットリカバリフレーズ", "exportSecretSelectYourAccount": "アカウントを選択", "exportSecretShowPrivateKey": "秘密鍵を表示", "exportSecretShowSecretRecoveryPhrase": "シークレットリカバリフレーズを表示する", "exportSecretShowSecret": "{{secretNameText}} を表示", "exportSecretWarningPrimaryInterpolated": "{{secretNameText}}を共有<1>しない</1>でください！", "exportSecretWarningSecondaryInterpolated": "他人が{{secretNameText}}を入手した場合、ウォレットを完全にコントロールされてしまいます。", "exportSecretOnlyWay": "あなたの {{secretNameText}} は、ウォレットを回復する唯一の方法です", "exportSecretDoNotShow": "誰にも {{secretNameText}} を見られないようにしてください", "exportSecretWillNotShare": "Phantom を含めて誰とも{{secretNameText}}を共有しません。", "exportSecretNeverShare": "絶対に誰とも {{secretNameText}} を共有しない", "exportSecretYourPrivateKey": "あなたの秘密鍵", "exportSecretYourSecretRecoveryPhrase": "あなたのシークレットリカバリフレーズ", "exportSecretResetPin": "PIN を再設定する", "fullPageHeaderBeta": "ベータ！", "fullPageHeaderHelp": "ヘルプ", "gasUpTo": "{{ amount }} まで", "timeDescription1hour": "約 1 時間", "timeDescription30minutes": "約 30 分", "timeDescription10minutes": "約 10 分", "timeDescription2minutes": "約 2 分", "timeDescription30seconds": "約 30 秒", "timeDescription15seconds": "約 15 秒", "timeDescription10seconds": "約 10 秒", "timeDescription5seconds": "約 5 秒", "timeDescriptionAbbrev1hour": "1 時間", "timeDescriptionAbbrev30minutes": "30 分", "timeDescriptionAbbrev10minutes": "10 分", "timeDescriptionAbbrev2minutes": "2 分", "timeDescriptionAbbrev30seconds": "30 秒", "timeDescriptionAbbrev15seconds": "15 秒", "timeDescriptionAbbrev10seconds": "10 秒", "timeDescriptionAbbrev5seconds": "5 秒", "gasSlow": "遅い", "gasAverage": "平均", "gasFast": "速い", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "再試行", "homeErrorDescription": "お客様の資産を引き出す際に、エラーが発生しました。再読込してもう一度お試しください。", "homeErrorTitle": "資産の取得に失敗しました", "homeManageTokenList": "トークンリストの管理", "interstitialDismissUnderstood": "了解", "interstitialBaseWelcomeTitle": "Phantom が Base をサポートするようになりました！", "interstitialBaseWelcomeItemTitle_1": "トークンの送信、受信、購入", "interstitialBaseWelcomeItemTitle_2": "Base エコシステムを探索する", "interstitialBaseWelcomeItemTitle_3": "安心・安全", "interstitialBaseWelcomeItemDescription_1": "{{paymentMethod}}、カード、または Coinbase を使用して、Base 上で USDC と ETH を転送および購入します。", "interstitialBaseWelcomeItemDescription_2": "お気に入りの DeFi および NFT アプリで Phantom を使用します。", "interstitialBaseWelcomeItemDescription_3": "Ledger サポート、スパムフィルタリング、トランザクションシミュレーションで自分を保護しましょう。", "privacyPolicyChangedInterpolated": "プライバシーポリシーが変更されました。<1>詳細はこちら</1>", "bitcoinAddressTypesBodyTitle": "Bitcoin アドレスの種類", "bitcoinAddressTypesFeature1Title": "Bitcoin アドレスについて", "bitcoinAddressTypesFeature1Subtitle": "Phantom は、個別残高で Segwit と Tap<PERSON> をサポートしています。 どちらのアドレスの種類でも BTC または Ordinals を送信できます。", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Phantom のデフォルトの BTC アドレス。 Ta<PERSON>root よりも古いですが、全てのウォレットと取引所をサポートしています。", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Ordinals と BRC-20 に最適で、手数料は最安です。 [設定] -> [優先 Bitcoin アドレス] でアドレスを調整できます。", "headerTitleInfo": "情報", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "これがあなたの<1>{{addressType}}</1>アドレスです。", "invalidChecksumTitle": "シークレットフレーズをアップグレードしました！", "invalidChecksumFeature1ExportPhrase": "新しいシークレットフレーズのエキスポート", "invalidChecksumFeature1ExportPhraseDescription": "新しいシークレットフレーズおよび以前のアカウントの秘密鍵をバックアップしてください。", "invalidChecksumFeature2FundsAreSafe": "あなたの資金は保護されており安全です", "invalidChecksumFeature2FundsAreSafeDescription": "このアップグレードは自動的に行われました。Phantom の従業員があなたのシークレットフレーズを知ること、または資金にアクセスすることはできません。", "invalidChecksumFeature3LearnMore": "詳細", "invalidChecksumFeature3LearnMoreDescription": "ほとんどのウォレットにサポートされていないフレーズを使用していました。詳細は、<1>このサポート記事</1>をご覧ください。", "invalidChecksumBackUpSecretPhrase": "シークレットフレーズのバックアップ", "migrationFailureTitle": "アカウントの移行中に不具合が発生しました", "migrationFailureFeature1": "シークレットフレーズのエキスポート", "migrationFailureFeature1Description": "オンボーディングを行う前にシークレットフレーズをバックアップしてください。", "migrationFailureFeature2": "Phantom へのオンボーディングを行う", "migrationFailureFeature2Description": "アカウントを表示するには、再度 Phantom へのオンボーディングを行う必要があります。", "migrationFailureFeature3": "詳細", "migrationFailureFeature3Description": "詳しくは<1>このヘルプ記事</1>をご覧ください。", "migrationFailureContinueToOnboarding": "オンボーディングに進む", "migrationFailureUnableToFetchMnemonic": "シークレットフレーズの読み込みに失敗しました", "migrationFailureUnableToFetchMnemonicDescription": "サポートにお問い合わせし、デバッグのためにアプリケーションログをダウンロードしてください", "migrationFailureContactSupport": "サポートに問い合わせる", "ledgerActionConfirm": "Ledger Nan<PERSON> で確認", "ledgerActionErrorBlindSignDisabledPrimaryText": "ブラインド署名が無効にされました", "ledgerActionErrorBlindSignDisabledSecondaryText": "ブラインド署名がハードウェア装置で有効であることを確認してからアクションを再試行してください", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "操作中にハードウェア装置の接続が切断されました", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Phantom エクステンションを閉じてからアクションを再試行してください", "ledgerActionErrorDeviceLockedPrimaryText": "ハードウェア装置がロックされました", "ledgerActionErrorDeviceLockedSecondaryText": "ハードウェア装置をロック解除してからアクションを再試行してください", "ledgerActionErrorHeader": "Ledger アクションエラー", "ledgerActionErrorUserRejectionPrimaryText": "ユーザーがトランザクションを拒否しました", "ledgerActionErrorUserRejectionSecondaryText": "ユーザーがハードウェア装置でアクションを拒否しました", "ledgerActionNeedPermission": "許可が必要です", "ledgerActionNeedToConfirm": "ハードウェアウォレットでトランザクションを確認する必要があります。{{chainType}} アプリ上でハードウェアウォレットがロック解除されていることを確認してください。", "ledgerActionNeedToConfirmMany": "ハードウェアウォレットで {{numberOfTransactions}} トランザクションを確認する必要があります。{{chainType}} アプリ上でハードウェアウォレットがロック解除されていることを確認してください。", "ledgerActionNeedToConfirmBlind": "ハードウェアウォレットでトランザクションを確認する必要があります。{{chainType}} アプリ上でハードウェアウォレットがロック解除されており、ブラインド署名が有効になっていることを確認してください。", "ledgerActionNeedToConfirmBlindMany": "ハードウェアウォレットで {{numberOfTransactions}} 件のトランザクションを確認する必要があります。{{chainType}} アプリ上でハードウェアウォレットがロック解除されており、ブラインド署名が有効になっていることを確認してください。", "ledgerActionPleaseConnect": "Ledger <PERSON> に接続してください", "ledgerActionPleaseConnectAndConfirm": "ハードウェアウォレットを接続し、ロック解除されていることを確認してください。ブラウザーで許可を承認していることを確認してください。", "maxInputAmount": "金額", "maxInputMax": "最大", "notEnoughSolPrimaryText": "SOL が足りません", "notEnoughSolSecondaryText": "ウォレットにトランザクション手数料に必要な SOL がありません。追加で入金を行ってから再試行してください。", "insufficientBalancePrimaryText": "{{tokenSymbol}} が足りません", "insufficientBalanceSecondaryText": "ウォレットにトランザクション手数料に必要な {{tokenSymbol}} がありません。", "insufficientBalanceRemaining": "残高", "insufficientBalanceRequired": "必要な金額", "notEnoughSplTokensTitle": "トークンが足りません", "notEnoughSplTokensDescription": "このトランザクションに必要なトークンがウォレットに足りていません。このトランザクションは送信すると取り消されます。", "transactionExpiredPrimaryText": "トランザクションの有効期限が切れました", "transactionExpiredSecondaryText": "トランザクションの確認に時間がかかり過ぎて、有効期限が切れました。このトランザクションは送信すると取り消されます。", "transactionHasWarning": "トランザクション警告", "tokens": "トークン", "notificationApplicationApprovalPermissionsAddressVerification": "あなたのアドレスであることを確認してください", "notificationApplicationApprovalPermissionsTransactionApproval": "トランザクションの承認依頼", "notificationApplicationApprovalPermissionsViewWalletActivity": "ウォレット残高と活動の表示", "notificationApplicationApprovalParagraphText": "確認することで、このサイトに選択されているアカウントの残高とアクティビティを確認する許可を与えます。", "notificationApplicationApprovalActionButtonConnect": "接続", "notificationApplicationApprovalActionButtonSignIn": "サインイン", "notificationApplicationApprovalAllowApproval": "サイトの接続を許可しますか？", "notificationApplicationApprovalAutoConfirm": "トランザクションの自動確認", "notificationApplicationApprovalConnectDisclaimer": "信頼する Web サイトにのみ接続してください", "notificationApplicationApprovalSignInDisclaimer": "信頼する Web サイトにのみサインインしてください", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "この Web サイトは、資金を盗もうとする可能性があり危険です。", "notificationApplicationApprovalConnectUnknownApp": "不明", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "アプリに接続できません", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "このアプリは、{{appNetworkName}} に接続しようとしていますが、 {{phantomNetworkName}} が選択されています。", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "{{networkName}} を使用するには、 [デベロッパー設定] → [testnetモード] に移動してください。", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "不明なネットワーク", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "他のモバイルアプリへの接続は現在、<PERSON>ger ではサポートされていません。", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "非 Ledge アカウントに切り替えるかアプリ内のブラウザーを使用してもう一度お試しください。", "notificationSignatureRequestConfirmTransaction": "トランザクションの確認", "notificationSignatureRequestConfirmTransactionCapitalized": "トランザクションの確認", "notificationSignatureRequestConfirmTransactions": "トランザクションの確認", "notificationSignatureRequestConfirmTransactionsCapitalized": "トランザクションの確認", "notificationSignatureRequestSignatureRequest": "署名の要求", "notificationMessageHeader": "メッセージ", "notificationMessageCopied": "メッセージをコピーしました", "notificationAutoConfirm": "自動確認", "notificationAutoConfirmOff": "オフ", "notificationAutoConfirmOn": "オン", "notificationConfirmFooter": "この Web サイトを信頼しているときにのみ確認してください。", "notificationEstimatedTime": "予想時間", "notificationPermissionRequestText": "これは許可のリクエストにすぎません。トランザクションはすぐには実行されない可能性があります。", "notificationBalanceChangesText": "残高の変化は推定値です。関係する金額および資産は保証されません。", "notificationContractAddress": "契約アドレス", "notificationAdvancedDetailsText": "高度", "notificationUnableToSimulateWarningText": "現在、残高の変化を推定できません。後でもう一度試すか、このサイトが信頼できるかどうかを確認してください。", "notificationSignMessageParagraphText": "このメッセージをサインすることで、選択されたアカウントの所有者であることを証明できます。", "notificationSignatureRequestScanFailedDescription": "メッセージにセキュリティの問題がないかスキャンによる確認ができません。気を付けて続行してください。", "notificationFailedToScan": "このリクエストの結果をシミュレートできませんでした。\n確認すると損失につながる可能性があり危険です。", "notificationScanLoading": "スキャンリクエスト", "notificationTransactionApprovalActionButtonConfirm": "確認", "notificationTransactionApprovalActionButtonBack": "戻る", "notificationTransactionApprovalEstimatedChanges": "予想推移", "notificationTransactionApprovalEstimatesBasedOnSimulations": "予想はトランザクションのシミュレーションに基づいており、保証されたものではありません", "notificationTransactionApprovalHideAdvancedDetails": "トランザクション詳細を非表示", "notificationTransactionApprovalNetworkFee": "ネットワーク手数料", "notificationTransactionApprovalNetwork": "ネットワーク", "notificationTransactionApprovalEstimatedTime": "予想時間", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "資産の所有に影響を与えるような推移は見つかりませんでした", "notificationTransactionApprovalSolanaAmountRequired": "Solana がトランザクション処理に必要とする金額", "notificationTransactionApprovalUnableToSimulate": "取引のシミュレーションができませんでした。承認は資産の喪失に繋がり得るので、当Web サイトが不審なサイトではないか確認してください。", "notificationTransactionApprovalUnableToFetchBalanceChanges": "残高の推移を取得できません", "notificationTransactionApprovalViewAdvancedDetails": "トランザクション詳細を表示", "notificationTransactionApprovalKnownMalicious": "このトランザクションは悪意があり、署名することで資金の損失につながります。", "notificationTransactionApprovalSuspectedMalicious": "このトランザクションは悪意があるかもしれません。承認することで資金の損失につながる可能性があります。", "notificationTransactionApprovalNetworkFeeHighWarning": "ネットワーク混雑のため、ネットワーク手数料が上昇しています。", "notificationTransactionERC20ApprovalDescription": "確認することで、このアプリに以下の上限まで残高を使用する許可を与えます。", "notificationTransactionERC20ApprovalContractAddress": "契約アドレス", "notificationTransactionERC20Unlimited": "無制限", "notificationTransactionERC20ApprovalTitle": "{{tokenSymbol}} の支出を承認する", "notificationTransactionERC20RevokeTitle": "{{tokenSymbol}} の支出を取り消す", "notificationTransactionERC721RevokeTitle": "{{tokenSymbol}} のアクセスを取り消す", "notificationTransactionERC20ApprovalAll": "すべての所有 {{tokenSymbol}}", "notificationIncorrectModeTitle": "モードが正しくありません", "notificationIncorrectModeInTestnetTitle": "testnetモードが有効です", "notificationIncorrectModeNotInTestnetTitle": "testnetモードが無効です", "notificationIncorrectModeInTestnetDescription": "{{origin}} は mainnet を使用しようとしていますが、Testnetモードが有効です", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} は testnet を使用しようとしていますが、testnetモードが無効です", "notificationIncorrectModeInTestnetProceed": "使用するにはtestnetモードをオフにしてください。", "notificationIncorrectModeNotInTestnetProceed": "使用するにはtestnetモードをオンにしてください。", "notificationIncorrectEIP712ChainId": "現在接続しているネットワークには意図しないメッセージに著名してしまうのを防ぎました", "notificationIncorrectEIP712ChainIdDescription": "メッセージは {{messageChainId}} を要求したが、あなたが接続されているのは {{connectedChainId}} です", "notificationUnsupportedNetwork": "サポートされていないネットワーク", "notificationUnsupportedNetworkDescription": "このWeb サイトは、現在 Phantom ではサポートされていないネットワークを使用しようとしています。", "notificationUnsupportedNetworkDescriptionInterpolated": "異なる拡張機能を使用するには、<1>[設定] から [デフォルトアプリウォレット] をオフにし、「常に尋ねる」を選択</1>してください。次に、ページを再読込みして再接続してください。", "notificationUnsupportedAccount": "サポートされていないアカウント", "notificationUnsupportedAccountDescription": "このWeb サイトは、この {{chainType}} のアカウントではサポートされていない {{targetChainType}} を使用しようとしています。", "notificationUnsupportedAccountDescription2": "サポートされているシードフレーズまたは秘密鍵のアカウントに切り替えてからもう一度お試しください。", "notificationInvalidTransaction": "無効なトランザクション", "notificationInvalidTransactionDescription": "このアプリから受信したトランザクションは不正な形式であるため、送信しないでください。アプリの開発者にお問い合わせし、この問題を報告してください。", "notificationCopyTransactionText": "トランザクションのコピー", "notificationTransactionCopied": "トランザクションをコピーしました", "onboardingImportOptionsPageTitle": "ウォレットのインポート", "onboardingImportOptionsPageSubtitle": "シークレットフレーズ、秘密鍵、またはハードウェアウォレットを使用して既存のウォレットをインポート。", "onboardingImportPrivateKeyPageTitle": "秘密鍵のインポート", "onboardingImportPrivateKeyPageSubtitle": "既存単一チェーンウォレットをインポート", "onboardingCreatePassword": "パスワードの作成", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<1>利用規約</1>に同意します", "onboardingCreatePasswordConfirmPasswordPlaceholder": "パスワードの確認", "onboardingCreatePasswordDescription": "ウォレットのロック解除に使用します.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "シークレットリカバリフレーズが無効です", "onboardingCreatePasswordPasswordPlaceholder": "パスワード", "onboardingCreatePasswordPasswordStrengthWeak": "弱", "onboardingCreatePasswordPasswordStrengthMedium": "中", "onboardingCreatePasswordPasswordStrengthStrong": "強", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "シークレットリカバリフレーズを保存しました", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "シークレットリカバリフレーズ", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "このフレーズはウォレットを復元する唯一の方法です。誰にも共有しないでください！", "onboardingImportWallet": "ウォレットのインポート", "onboardingImportWalletImportExistingWallet": "12 単語または 24 単語のシークレットリカバリフレーズで既存ウォレットをインポートします。", "onboardingImportWalletRestoreWallet": "ウォレットの復元", "onboardingImportWalletSecretRecoveryPhrase": "シークレットリカバリフレーズ", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "シークレットリカバリフレーズが無効です", "onboardingImportWalletIHaveWords": "{{numWords}} 字のリカバリーフレーズがあります", "onboardingImportWalletIncorrectOrMisspelledWord": "ワード {{wordIndex}} が異なるかスペルが間違っています", "onboardingImportWalletIncorrectOrMisspelledWords": "ワード {{wordIndexes}} が異なるかスペルが間違っています", "onboardingImportWalletScrollDown": "スクロールダウン", "onboardingImportWalletScrollUp": "スクロールアップ", "onboardingSelectAccountsImportAccounts": "アカウントをインポート", "onboardingSelectAccountsImportAccountsDescription": "インポートするウォレットアカウントを選択してください.", "onboardingSelectAccountsImportSelectedAccounts": "選択したアカウントをインポート", "onboardingSelectAccountsFindMoreAccounts": "アカウントをさらに探す", "onboardingSelectAccountsFindMoreNoneFound": "アカウントが見つかりませんでした", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} 個のアカウントが選択中です", "onboardingSelectAccountSelectAllText": "全て選択", "onboardingAdditionalPermissionsTitle": "Phantom でアプリを使用", "onboardingAdditionalPermissionsSubtitle": "最もシームレスなアプリエクスペリエンスを実現するには、Phantom にすべてのサイトでのデータの読み取りと変更を許可することをお勧めします。", "interstitialAdditionalPermissionsTitle": "Phantom でアプリを使用", "interstitialAdditionalPermissionsSubtitle": "アプリを中断することなく使い続けるには、Phantom にすべてのサイトでのデータの読み取りと変更を許可することをお勧めします。", "recentActivityPrimaryText": "最近の活動", "removeAccountActionButtonRemove": "削除", "removeAccountRemoveWallet": "アカウントを削除", "removeAccountInterpolated": "{{accountName}}を削除", "removeAccountWarningLedger": "このウォレットを Phantom から削除しても、\"ハードウェアウォレットの接続\" フローを使用して再度追加できます。", "removeAccountWarningSeedVault": "このウォレットを Phantom から削除しても、「シード保管庫ウォレットの接続」の工程を使用して再度追加できます。", "removeAccountWarningPrivateKey": "このウォレットを削除すると、Phantom はウォレットを復元できなくなります。秘密鍵のバックアップがあることを確認してください。", "removeAccountWarningSeed": "このウォレットを Phantom から削除しても、このウォレットか別のウォレットのニーモニックを使用して再度取得できます。", "removeAccountWarningReadOnly": "監視限定アカウントであるため、このアカウントを削除してもウォレットには影響を及びません。", "removeSeedPrimaryText": "のシークレットフレーズ {{number}} の削除中", "removeSeedSecondaryText": "シークレットフレーズ {{number}} でのすべての既存アカウントが削除されます。既存のシークレットフレーズを保存していることを確認してください。", "resetSeedPrimaryText": "アプリを新しいシークレットフレーズでリセットする", "resetSeedSecondaryText": "すべての既存のアカウントが削除され、新しいアカウントに置き換えられます。既存のシークレットフレーズと秘密鍵のバックアップがあることを確認してください。", "resetAppPrimaryText": "アプリをリセットして消去", "resetAppSecondaryText": "すべての既存のアカウントおよびデータが削除されます。既存のシークレットフレーズと秘密鍵のバックアップがあることを確認してください。", "richTransactionsDays": "日数", "richTransactionsToday": "今日", "richTransactionsYesterday": "昨日", "richTransactionDetailAccount": "アカウント", "richTransactionDetailAppInteraction": "アプリインタラクション", "richTransactionDetailAt": "時間：", "richTransactionDetailBid": "入札", "richTransactionDetailBidDetails": "入札の詳細", "richTransactionDetailBought": "購入済み", "richTransactionDetailBurned": "バーン済み", "richTransactionDetailCancelBid": "入札をキャンセル", "richTransactionDetailCompleted": "完了", "richTransactionDetailConfirmed": "確認済み", "richTransactionDetailDate": "日付", "richTransactionDetailFailed": "失敗", "richTransactionDetailFrom": "送信元", "richTransactionDetailItem": "アイテム", "richTransactionDetailListed": "上場済み", "richTransactionDetailListingDetails": "上場の詳細", "richTransactionDetailListingPrice": "上場価格", "richTransactionDetailMarketplace": "マーケットプレイス", "richTransactionDetailNetworkFee": "ネットワーク手数料", "richTransactionDetailOriginalListingPrice": "元の上場価格", "richTransactionDetailPending": "保留中", "richTransactionDetailPrice": "価格", "richTransactionDetailProvider": "プロバイダー", "richTransactionDetailPurchaseDetails": "購入詳細", "richTransactionDetailRebate": "リベート", "richTransactionDetailReceived": "受信済み", "richTransactionDetailSaleDetails": "販売詳細", "richTransactionDetailSent": "送信済み", "richTransactionDetailSold": "販売済み", "richTransactionDetailStaked": "ステーク済み", "richTransactionDetailStatus": "状態", "richTransactionDetailSwap": "スワップ", "richTransactionDetailSwapDetails": "スワップ詳細", "richTransactionDetailTo": "宛先", "richTransactionDetailTokenSwap": "トークンスワップ", "richTransactionDetailUnknownNFT": "不明な NFT", "richTransactionDetailUnlisted": "上場廃止済み", "richTransactionDetailUnstaked": "ステーク解除済み", "richTransactionDetailValidator": "バリデーター", "richTransactionDetailViewOnExplorer": "{{explorer}} で表示", "richTransactionDetailWithdrawStake": "ステークの出金", "richTransactionDetailYouPaid": "支払額", "richTransactionDetailYouReceived": "受領額", "richTransactionDetailUnwrapDetails": "アンラップ詳細", "richTransactionDetailTokenUnwrap": "トークンアンラップ", "activityItemsRefreshFailed": "新しいトランザクションの読み込みに失敗しました。", "activityItemsPagingFailed": "過去のトランザクションの読み込みに失敗しました。", "activityItemsTestnetNotAvailable": "Testnet のトランザクション履歴は現在利用できません", "historyUnknownDappName": "不明", "historyStatusSucceeded": "成功", "historyNetwork": "ネットワーク", "historyAttemptedAmount": "取引試行額", "historyAmount": "金額", "sendAddressBookButtonLabel": "アドレス帳", "addressBookSelectAddressBook": "アドレス帳", "sendAddressBookNoAddressesSaved": "保存されているアドレスはありません", "sendAddressBookRecentlyUsed": "最近使用した項目", "addressBookSelectRecentlyUsed": "最近使用した項目", "sendConfirmationLabel": "ラベル", "sendConfirmationMessage": "メッセージ", "sendConfirmationNetworkFee": "ネットワーク手数料", "sendConfirmationPrimaryText": "送信の確認", "sendWarning_INSUFFICIENT_FUNDS": "資金が不足しているため、このトランザクションを送信すると失敗する可能性があります。", "sendFungibleSummaryNetwork": "ネットワーク", "sendFungibleSummaryNetworkFee": "ネットワーク手数料", "sendFungibleSummaryEstimatedTime": "予想時間", "sendFungiblePendingEstimatedTime": "所要時間", "sendFungibleSummaryEstimatedTimeDescription": "Ethereum トランザクション速度はいくつかの要因によって異なります。「ネットワーク手数料」をクリックすると、トランザクションを高速化きます。", "sendSummaryBitcoinPendingTxTitle": "送信できませんでした", "sendSummaryBitcoinPendingTxDescription": "保留中の同時 BTC 転送は1つまでです。転送が完了するまで待ってもう一度転やり直してください。", "sendFungibleSatProtectionTitle": "Sat 保護で送信", "sendFungibleSatProtectionExplainer": "Phantom では、Ordinals や BRC20 がトランザクションの手数料または Bitcoin 転送に使用されることが保証されています。", "sendFungibleTransferFee": "トークン転送の手数料", "sendFungibleTransferFeeToolTip": "このトークンの作成者は、転送ごとに手数料を受け取ります。この手数料は、Phantom が請求または徴収するものではありません。", "sendFungibleInterestBearingPercent": "現在の金利", "sendFungibleNonTransferable": "転送不可", "sendFungibleNonTransferableToolTip": "このトークンは別のアカウントに転送できません。", "sendFungibleNonTransferableYes": "はい", "sendStatusErrorMessageInterpolated": "<1>{{uiRecipient}}</1>にトークンを送信する際にエラーが発生しました。", "sendStatusErrorMessageInsufficientBalance": "トランザクションを完了するのに十分な残高がありません。", "sendStatusErrorTitle": "送信できません", "sendStatusLoadingTitle": "送信中...", "sendStatusSuccessMessageInterpolated": "<1>{{uiRecipient}}</1>にトークンが送信されました。", "sendStatusSuccessTitle": "送信完了！", "sendStatusConfirmedSuccessTitle": "送信完了！", "sendStatusSubmittedSuccessTitle": "トランザクションを送信しました", "sendStatusEstimatedTransactionTime": "トランザクションの予定所要時間：{{time}}", "sendStatusViewTransaction": "トランザクションの表示", "sendFungibleLoadingMessageInterpolated": "<2>{{uiRecipient}}</2> に <2>{{uiAmount}} {{assetSymbol}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> を <2>{{uiRecipient}}</2> に送信しました", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> を <2>{{uiRecipient}}</2> に送信しました", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> は <2>{{uiRecipient}}</2> に送信されませんでした", "sendFungibleSolanaErrorCode": "エラーコード {{code}}", "sendFormErrorInsufficientBalance": "残高不足", "sendFormErrorEmptyAmount": "必要な量", "sendFormInvalidAddress": "無効な {{assetName}} アドレス", "sendFormInvalidUsernameOrAddress": "無効なユーザー名またはアドレス", "sendFormErrorInvalidSolanaAddress": "Solana アドレスが無効です", "sendFormErrorInvalidTwitterHandle": "この Twitter でのハンドル名は登録されていません", "sendFormErrorInvalidDomain": "このドメインは登録されていません", "sendFormErrorInvalidUsername": "このユーザー名は登録されていません", "sendFormErrorMinRequiredInterpolated": "最低 {{minAmount}} {{tokenName}} が必要です", "sendRecipientTextareaPlaceholder": "受信者の SOL アドレス", "sendRecipientTextAreaPlaceholder2": "受信者の {{symbol}} アドレス", "sendMemoOptional": "メモ（省略可能）", "sendMemo": "メモ", "sendOptional": "省略可能", "settings": "設定", "settingsDapps": "dApps", "settingsSelectedAccount": "選択されているアカウント", "settingsAddressBookNoLabel": "ラベルなし", "settingsAddressBookPrimary": "アドレス帳", "settingsAddressBookRecentlyUsed": "最近使用した項目", "settingsAddressBookSecondary": "よく使用するアドレスの管理", "settingsAutoLockTimerPrimary": "自動ロックタイマー", "settingsAutoLockTimerSecondary": "自動ロックタイマーの時間を変更", "settingsChangeLanguagePrimary": "言語の変更", "settingsChangeLanguageSecondary": "表示言語の変更", "settingsChangeNetworkPrimary": "ネットワークの変更", "settingsChangeNetworkSecondary": "ネットワーク設定の構成", "settingsChangePasswordPrimary": "パスワードの変更", "settingsChangePasswordSecondary": "ロック画面パスワードの変更", "settingsCompleteBetaSurvey": "ベータアンケートを完了する", "settingsDisplayLanguage": "表示言語", "settingsErrorCannotExportLedgerPrivateKey": "Ledger 秘密鍵をエクスポートできません", "settingsErrorCannotRemoveAllWallets": "すべてのアカウントを削除できません", "settingsExportPrivateKey": "秘密鍵を表示", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "ローカルホスト", "settingsNetworkPhantomRPC": "Phantom RPC Network", "settingsTestNetworks": "testnetワーク", "settingsUseCustomNetworks": "カスタムネットワークを使用", "settingsTestnetMode": "testnetモード", "settingsTestnetModeDescription": "残高とアプリ接続に適用されます。", "settingsWebViewDebugging": "Web ビューのデバッグ", "settingsWebViewDebuggingDescription": "アプリ内ブラウザの Web ビューを検査およびデバッグできます。", "settingsTestNetworksInfo": "いずれのtestnetワークへの切り替えは、テストのみを目的とされています。testnetネットワーク上のトークンは、金銭的価値を持たないことにご留意ください。", "settingsEmojis": "絵文字", "settingsNoAddresses": "アドレスはありません", "settingsAddressBookEmptyHeading": "アドレス帳が空です", "settingsAddressBookEmptyText": "お気に入りのアドレスを追加するには「+」または「アドレスを追加」ボタンをクリックしてください", "settingsEditWallet": "アカウントを編集", "settingsNoTrustedApps": "信頼済みアプリはありません", "settingsNoConnections": "接続はまだありません。", "settingsRemoveWallet": "アカウントを削除", "settingsResetApp": "アプリのリセット", "settingsBlocked": "ブロック済み", "settingsBlockedAccounts": "ブロックされたアカウント", "settingsNoBlockedAccounts": "ブロックされたアカウントがありません。", "settingsRemoveSecretPhrase": "シークレットフレーズの削除", "settingsResetAppWithSecretPhrase": "シークレットフレーズでのアプリのリセット", "settingsResetSecretRecoveryPhrase": "シークレットリカバリフレーズのリセット", "settingsShowSecretRecoveryPhrase": "シークレットリカバリフレーズを表示する", "settingsShowSecretRecoveryPhraseSecondary": "リカバリフレーズの表示", "settingsShowSecretRecoveryPhraseTertiary": "シークレットフレーズの表示", "settingsTrustedAppsAutoConfirmActiveUntil": "{{formattedTimestamp}} まで", "settingsTrustedAppsAutoConfirm": "自動確認", "settingsTrustedAppsDisclaimer": "信頼できるサイトのみで自動確認を有効にしてください", "settingsTrustedAppsLastUsed": "{{formattedTimestamp}} 前に使用", "settingsTrustedAppsPrimary": "接続されているアプリ", "settingsTrustedApps": "信頼済みアプリ", "settingsTrustedAppsRevoke": "取り消し", "settingsTrustedAppsRevokeToast": "{{trustedApp}} が切断されました", "settingsTrustedAppsSecondary": "信頼済みアプリの構成", "settingsTrustedAppsToday": "今日", "settingsTrustedAppsYesterday": "昨日", "settingsTrustedAppsLastWeek": "先週", "settingsTrustedAppsBeforeYesterday": "何日も前", "settingsTrustedAppsDisconnectAll": "全て切断", "settingsTrustedAppsDisconnectAllToast": "全てのアプリが切断されています", "settingsTrustedAppsEndAutoConfirmForAll": "全てのアプリの自動確認を終了", "settingsTrustedAppsEndAutoConfirmForAllToast": "全ての自動確認セッションが終了しました", "settingsSecurityPrimary": "セキュリティーとプライバシー", "settingsSecuritySecondary": "セキュリティー設定をアップデート", "settingsActiveNetworks": "有効なネットワーク", "settingsActiveNetworksAll": "全て", "settingsActiveNetworksSolana": "<PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana はデフォルトのネットワークであり、常にオンのままです。", "settingsDeveloperPrimary": "デベロッパー設定", "settingsAdvanced": "詳細設定", "settingsTransactions": "トランザクション設定", "settingsAutoConfirm": "自動確認", "settingsSecurityAnalyticsPrimary": "匿名アナリティクスを共有", "settingsSecurityAnalyticsSecondary": "有効化して改善にご協力ください", "settingsSecurityAnalyticsHelper": "Phantom は、分析の目的で個人情報を使用しません。", "settingsSuspiciousCollectiblesPrimary": "怪しいコレクティブルを隠す", "settingsSuspiciousCollectiblesSecondary": "トグルしてフラグ付きのコレクティブルを隠す", "settingsPreferredBitcoinAddress": "優先Bitcoinアドレス", "settingsEnabledAddressesUpdated": "表示されるアドレスが更新されました！", "settingsEnabledAddresses": "有効なアドレス", "settingsBitcoinPaymentAddressForApps": "アプリ用の支払いアドレス", "settingsBitcoinOrdinalsAddressForApps": "アプリ用の Ordinals アドレス", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "上記の両方のアドレスタイプが有効になっている場合、Magic Eden などの特定のアプリでは、購入時に Native Segwit アドレスが使用されます。購入した資産は、Taproot アドレスで受信されます。", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "互換性を確保するための Phantom のデフォルトの Bitcoin アドレス。", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "（デフォルト）", "settingsPreferredBitcoinAddressTaprootExplainer": "最新のアドレスタイプで、通常はトランザクション手数料が安くなります。", "settingsPreferredExplorers": "好みのエクスプローラー", "settingsPreferredExplorersSecondary": "好みのブロックチェーンエクスプローラーに変更する", "settingsCustomGasControls": "カスタム GAS 制御", "settingsSupportDesk": "サポートデスク", "settingsSubmitATicket": "チケットを送信", "settingsAttachApplicationLogs": "アプリケーションログを添付", "settingsDownloadApplicationLogs": "アプリログをダウンロード", "settingsDownloadApplicationLogsShort": "ログをダウンロード", "settingsDownloadApplicationLogsHelper": "Phantom のサポート問題の解決に役立つローカルデータ、クラッシュレポート、公開のウォレットアドレスが含まれています", "settingsDownloadApplicationLogsWarning": "シード フレーズや秘密鍵などの機密データは含まれません。", "settingsWallet": "ウォレット", "settingsPreferences": "設定", "settingsSecurity": "セキュリティー", "settingsDeveloper": "デベロッパー", "settingsSupport": "サポート", "settingsWalletShortcutsPrimary": "ウォレットのショートカットを表示", "settingsAppIcon": "アプリのアイコン", "settingsAppIconDefault": "デフォルト", "settingsAppIconLight": "ライト", "settingsAppIconDark": "ダーク", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "アカウント", "settingsSearchResultSelected": "選択中", "settingsSearchResultExport": "エキスポート", "settingsSearchResultSeed": "シード", "settingsSearchResultTrusted": "信頼されています", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "状態", "settingsSearchResultLogs": "ログ", "settingsSearchResultBiometric": "生体認証", "settingsSearchResultTouch": "指紋", "settingsSearchResultFace": "顔認証", "settingsSearchResultShortcuts": "ショートカット", "settingsAllSitesPermissionsTitle": "すべてのサイトで Phantom にアクセス", "settingsAllSitesPermissionsSubtitle": "拡張機能をクリックせずに Phantom でアプリをシームレスに使用できます", "settingsAllSitesPermissionsDisabled": "この設定はお使いのブラウザではサポートされていません", "settingsSolanaCopyTransaction": "トランザクションのコピーを有効化する", "settingsSolanaCopyTransactionDetails": "直列化されたトランザクションデータをクリップボードにコピーする", "settingsAutoConfirmHeader": "自動確認", "refreshWebpageToApplyChanges": "Web ページを更新して変更を適用します", "settingsExperimentalTitle": "実験的機能", "settingsExprimentalSolanaActionsSubtitle": "X.com で関連リンクが検出されると、Solana アクションボタンが自動的に展開されます。", "stakeAccountCardActiveStake": "有効なステーク", "stakeAccountCardBalance": "残高", "stakeAccountCardRentReserve": "支払準備金", "stakeAccountCardRewards": "最終報酬", "stakeAccountCardRewardsTooltip": "こちらがステークで得られた一番最近の報酬です。3 日ごとに報酬が付与されます。", "stakeAccountCardStakeAccount": "アドレス", "stakeAccountCardLockup": "ロックアップ期間", "stakeRewardsHistoryTitle": "報酬履歴", "stakeRewardsActivityItemTitle": "報酬", "stakeRewardsHistoryEmptyList": "報酬がありません", "stakeRewardsTime_zero": "今日", "stakeRewardsTime_one": "昨日", "stakeRewardsTime_other": "{{count}} 日前", "stakeRewardsItemsPagingFailed": "古い報酬の読み込みに失敗しました。", "stakeAccountCreateAndDelegateErrorStaking": "このバリデーターにステークする際に不具合が発生しました。再試行してください。", "stakeAccountCreateAndDelegateSolStaked": "SOL をステークしました！", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "ステークアカウントが有効になってから<1></1>数日以内に SOL に報酬が付与され始めます。", "stakeAccountCreateAndDelegateStakingFailed": "ステーク失敗", "stakeAccountCreateAndDelegateStakingSol": "SOL をステーク中...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "ステークアカウントを作成し、その後 SOL を次に委任します:", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "ステークアカウントを作成し、その後 SOL を {{validatorName}} に委任します", "stakeAccountCreateAndDelegateViewTransaction": "トランザクションの表示", "stakeAccountDeactivateStakeSolUnstaked": "SOL をステーク解除しました！", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "ステークアカウントが無効になってから数日以内に<1></1>ステークを出金できるようになります。", "stakeAccountDeactivateStakeSolUnstakedDescription": "ステークアカウントが無効になってから数日以内にステークを出金できるようになります。", "stakeAccountDeactivateStakeUnstakingFailed": "ステーク解除失敗", "stakeAccountDeactivateStakeUnstakingFailedDescription": "このバリデーターからステーク解除する際に不具合が発生しました。再試行してください。", "stakeAccountDeactivateStakeUnstakingSol": "SOL をステーク解除中...", "stakeAccountDeactivateStakeUnstakingSolDescription": "SOL のステーク解除処理を開始しています。", "stakeAccountDeactivateStakeViewTransaction": "トランザクションの表示", "stakeAccountDelegateStakeSolStaked": "SOL をステークしました！", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "ステークアカウントが有効になってから<1></1>数日以内に SOL に報酬が付与され始めます。", "stakeAccountDelegateStakeStakingFailed": "ステーク失敗", "stakeAccountDelegateStakeStakingFailedDescription": "このバリデーターにステークする際に不具合が発生しました。再試行してください。", "stakeAccountDelegateStakeStakingSol": "SOL をステーク中...", "stakeAccountDelegateStakeStakingSolDescription": "SOL を委任しています。", "stakeAccountDelegateStakeViewTransaction": "トランザクションの表示", "stakeAccountListActivationActivating": "有効化", "stakeAccountListActivationActive": "有効", "stakeAccountListActivationInactive": "無効", "stakeAccountListActivationDeactivating": "無効化", "stakeAccountListErrorFetching": "ステークのアカウントを取得できませんでした。後で再試行してください。", "stakeAccountListNoStakingAccounts": "ステークアカウントがありません", "stakeAccountListReload": "再読み込み", "stakeAccountListViewPrimaryText": "あなたのステーク", "stakeAccountListViewStakeSOL": "SOL のステーク", "stakeAccountListItemStakeFee": "{{fee}} の手数料", "stakeAccountViewActionButtonRestake": "再ステーク", "stakeAccountViewActionButtonUnstake": "ステーク解除", "stakeAccountViewError": "エラー", "stakeAccountViewPrimaryText": "あなたのステーク", "stakeAccountViewRestake": "再ステーク", "stakeAccountViewSOLCurrentlyStakedInterpolated": "現在、SOL はバリデーターでステークされています。資金にアクセスするには<1></1>ステークを解除する必要があります。<3>詳細</3>", "stakeAccountViewStakeInactive": {"part1": "このステークアカウントは無効です。ステークを出金するか、委任先のバリデーターを見つけることを検討してください。", "part2": "詳細"}, "stakeAccountViewStakeNotFound": "このステークアカウントは見つかりませんでした。", "stakeAccountViewViewOnExplorer": "{{explorer}} で表示", "stakeAccountViewWithdrawStake": "ステークの出金", "stakeAccountViewWithdrawUnstakedSOL": "ステーク解除済み SOL の出金", "stakeAccountInsufficientFunds": "ステーク解除または引き出しをするための SOL が不足しています。", "stakeAccountWithdrawStakeSolWithdrawn": "SOL を出金しました！", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL が出金されました。", "part2": "このステークアカウントは数分以内に自動的に削除されます。"}, "stakeAccountWithdrawStakeViewTransaction": "トランザクションの表示", "stakeAccountWithdrawStakeWithdrawalFailed": "出金失敗", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "このステークアカウントから出金する際に不具合が発生しました。再試行してください。", "stakeAccountWithdrawStakeWithdrawingSol": "SOL を出金中...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "このステークアカウントから SOL を出金しています。", "startEarningSolAccount": "アカウント", "startEarningSolAccounts": "アカウント", "startEarningSolErrorClosePhantom": "ここをタップして再試行", "startEarningSolErrorTroubleLoading": "ステーク読み込みの問題", "startEarningSolLoading": "読み込み中...", "startEarningSolPrimaryText": "SOL を獲得開始", "startEarningSolSearching": "ステークアカウントを検索中", "startEarningSolStakeTokens": "トークンをステークして報酬を獲得", "startEarningSolYourStake": "あなたのステーク", "unwrapFungibleTitle": "{{tokenSymbol}}へスワップ", "unwrapFungibleDescription": "{{toToken}}用に{{fromToken}}から出金", "unwrapFungibleConfirmSwap": "スワップを確定", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "予想手数料", "swapFeesFees": "手数料", "swapFeesPhantomFee": "Phantom手数料", "swapFeesPhantomFeeDisclaimer": "上位リクイディティ・プロバイダ－の中から最良価格を見つけます。{{feePercentage}}の手数料は自動的に見積に含まれます。", "swapFeesRate": "価格", "swapFeesRateDisclaimer": "Jupiter Aggregatorによって複数の分散型取引所で発見された最良のレート。", "swapFeesRateDisclaimerMultichain": "複数の分散型取引所で発見された最良のレート。", "swapFeesPriceImpact": "プライスインパクト", "swapFeesHighPriceImpact": "高価の影響", "swapFeesPriceImpactDisclaimer": "トレードの規模を基にした市場価格と推定価格の差。", "swapFeesSlippage": "スリッページ", "swapFeesHighSlippage": "高いスリッページ幅", "swapFeesHighSlippageDisclaimer": "価格が{{slippage}}%を超えて不利に変動した場合はトランザクションが失敗します。", "swapTransferFee": "転送手数料", "swapTransferFeeDisclaimer": "${{symbol}}の取引を行う際には、Phantom ではなくトークン作成者によって課せられる {{feePercent}}% の送金手数料が発生します。", "swapTransferFeeDisclaimerMany": "選択したトークンの取引を行う際には、Phantom ではなくトークン作成者によって課せられる {{feePercent}}% の手数料が発生します。", "swapFeesSlippageDisclaimer": "取引の価格が提供された時価から逸脱できる金額です。", "swapFeesProvider": "プロバイダー", "swapFeesProviderDisclaimer": "トレードに使用されている分散型取引所。", "swapEstimatedTime": "予想時間", "swapEstimatedTimeShort": "予定所要時間", "swapEstimatedTimeDisclaimer": "ブリッジの完成予想時間は、トランザクションの速度に影響する複数の要素によって決まります。", "swapSettingsButtonCommand": "スワップ設定を開く", "swapQuestionRetry": "再試行しますか？", "swapUnverifiedTokens": "未確認のトークン", "swapSectionTitleTokens": "{{section}} トークン", "swapFlowYouPay": "支払額：", "swapFlowYouReceive": "受領額：", "swapFlowActionButtonText": "注文の確認", "swapAssetCardTokenNetwork": "{{network}} で {{symbol}}", "swapAssetCardMaxButton": "最大", "swapAssetCardSelectTokenAndNetwork": "トークンとネットワークを選択", "swapAssetCardBuyTitle": "受領額：", "swapAssetCardSellTitle": "支払額：", "swapAssetWarningUnverified": "このトークンは未検証です。信頼できるトークンのみを操作してください。", "swapAssetWarningPermanentDelegate": "委任者はこれらのトークンを永久にバーンまたは転送することができます。", "swapSlippageSettingsTitle": "スリッページ設定", "swapSlippageSettingsSubtitle": "価格がスリッページよりも変動した場合はトランザクションが失敗します。値が高すぎると不利な取引になります。", "swapSlippageSettingsCustom": "カスタム", "swapSlippageSettingsHighSlippageWarning": "あなたのトランザクションがフロントランされ、不利な取引になる可能性があります。", "swapSlippageSettingsCustomMinError": "{{minSlippage}}%より大きい値を入力してください。", "swapSlippageSettingsCustomMaxError": "{{maxSlippage}}%より小さい値を入力してください。", "swapSlippageSettingsCustomInvalidValue": "有効化な値を入力してください。", "swapSlippageSettingsAutoSubtitle": "Phantom は、スワップを成功させるための最も低いスリッページを見つけます。", "swapSlippageSettingsAuto": "自動", "swapSlippageSettingsFixed": "固定", "swapSlippageOptInTitle": "自動スリッページ", "swapSlippageOptInSubtitle": "Phantom は、スワップを成功させるための最も低いスリップページを見つけます。この設定は、[Swapper] → [スリッページ設定] からいつでも変更できます。", "swapSlippageOptInEnableOption": "自動スリッページを有効化", "swapSlippageOptInRejectOption": "固定スリッページを使い続ける", "swapQuoteFeeDisclaimer": "見積には{{feePercentage}}のPhantom手数料が含まれています", "swapQuoteMissingContext": "スワップ見積のコンテキストがありません", "swapQuoteErrorNoQuotes": "見積なしでスワップしようとしています", "swapQuoteSolanaNetwork": "Solanaネットワーク", "swapQuoteNetwork": "ネットワーク", "swapQuoteOneTimeSerumAccount": "ワンタイムSerumアカウント", "swapQuoteOneTimeTokenAccount": "ワンタイムトークンアカウント", "swapQuoteBridgeFee": "クロスチェーンスワップ手数料", "swapQuoteDestinationNetwork": "宛先ネットワーク", "swapQuoteLiquidityProvider": "流動性プロバイダー", "swapReviewFlowActionButtonPrimary": "スワップ", "swapReviewFlowPrimaryText": "注文の確認", "swapReviewFlowYouPay": "支払額：", "swapReviewFlowYouReceive": "受領額：", "swapReviewInsufficientBalance": "資金不足", "ugcSwapWarningTitle": "注意", "ugcSwapWarningBody1": "このトークンはトークンランチャー {{programName}} で取引されています。", "ugcSwapWarningBody2": "これらのトークンの価値は大きく変動する可能性があり、大きな金銭的利益または損失につながる可能性があります。取引は自己責任で行ってください。", "ugcSwapWarningConfirm": "分かりました", "bondingCurveProgressLabel": "Bonding Curve の進捗", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Bonding Curve モデルでは、トークンの価格は曲線の形状によって決定され、トークンの購入が増えると上昇し、トークンの売却が増えると下がります。トークンが売り切れると、すべての流動性が Raydium に預けられ、バーンされます。", "ugcFungibleWarningBanner": "このトークンは {{programName}} で取引されています", "ugcCreatedRowLabel": "作成日", "ugcStatusRowLabel": "状態", "ugcStatusRowValue": "卒業", "swapTxConfirmationReceived": "受信しました！", "swapTxConfirmationSwapFailed": "スワップに失敗しました", "swapTxConfirmationSwapFailedStaleQuota": "時価は無効です。もう一度お試しください。", "swapTxConfirmationSwapFailedSlippageLimit": "このスワップにはスリッページが低すぎます。スワップ画面上部のスリッページを増やしてからもう一度お試しください。", "swapTxConfirmationSwapFailedInsufficientBalance": "リクエストを完了できませんでした。 トランザクションを完了するのに十分な残高がありません。", "swapTxConfirmationSwapFailedEmptyRoute": "このトークンペアの流動性が変わりました。適切な時価が見つかりませんでした。もう一度試すか、トークンの量を調整してください。", "swapTxConfirmationSwapFailedAcountFrozen": "このトークンは作成者によって凍結されています。このトークンを送信またはスワップすることはできません。", "swapTxConfirmationSwapFailedTryAgain": "スワップが失敗しました。再試行してください。", "swapTxConfirmationSwapFailedUnknownError": "スワップを完了できませんでした。資金には影響はありません。もう一度お試しください。", "swapTxConfirmationSwapFailedSimulationTimeout": "スワップのシミュレーションを実行できませんでした。資金には影響はありません。もう一度お試しください。", "swapTxConfirmationSwapFailedSimulationUnknownError": "スワップを完了できませんでした。資金には影響はありません。もう一度お試しください。", "swapTxConfirmationSwapFailedInsufficientGas": "アカウントの資金が不足しているため、取引を完了できませんでした。アカウントに資金を追加して、もう一度お試しください。", "swapTxConfirmationSwapFailedLedgerReject": "ハードウェアデバイス上のユーザーによってスワップが拒否されました。", "swapTxConfirmationSwapFailedLedgerConnectionError": "デバイス接続エラーが発生したため、スワップが拒否されました。もう一度お試しください。", "swapTxConfirmationSwapFailedLedgerSignError": "デバイスサインエラーが発生したため、スワップが拒否されました。もう一度お試しください。", "swapTxConfirmationSwapFailedLedgerError": "デバイスエラーが発生したため、スワップは拒否されました。もう一度お試しください。", "swapTxConfirmationSwappingTokens": "トークンをスワップ中...", "swapTxConfirmationTokens": "トークン", "swapTxConfirmationTokensDeposited": "完了！トークンがウォレットに入金されます", "swapTxConfirmationTokensDepositedTitle": "完了です！", "swapTxConfirmationTokensDepositedBody": "トークンがウォレットに入金されました", "swapTxConfirmationTokensWillBeDeposited": "はトランザクションが完了次第ウォレットに入金されます", "swapTxConfirmationViewTransaction": "トランザクションの表示", "swapTxBridgeSubmitting": "トランザクションを送信中", "swapTxBridgeSubmittingDescription": "{{sellNetwork}}での{{sellAmount}}を{{buyNetwork}}での{{buyAmount}}にスワップします", "swapTxBridgeFailed": "トランザクションの送信に失敗しました", "swapTxBridgeFailedDescription": "リクエストを完了できませんでした。", "swapTxBridgeSubmitted": "トランザクションを送信しました", "swapTxBridgeSubmittedDescription": "トランザクションの予定所要時間：{{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "このウィンドウを安全に閉じることができます。", "swapperSwitchTokens": "トークンを切り替える", "swapperMax": "最大", "swapperTooltipNetwork": "ネットワーク", "swapperTooltipPrice": "価格", "swapperTooltipAddress": "契約", "swapperTrendingSortBy": "並べ替え", "swapperTrendingTimeFrame": "期間", "swapperTrendingNetwork": "ネットワーク", "swapperTrendingRank": "ランク", "swapperTrendingTokens": "人気トークン", "swapperTrendingVolume": "ボリューム", "swapperTrendingPrice": "価格", "swapperTrendingPriceChange": "価格変動", "swapperTrendingMarketCap": "時価総額", "swapperTrendingTimeFrame1h": "1 時間", "swapperTrendingTimeFrame24h": "24 時間", "swapperTrendingTimeFrame7d": "7 日", "swapperTrendingTimeFrame30d": "30 日", "swapperTrendingNoTokensFound": "トークンが見つかりません。", "switchToggle": "切り替え", "termsOfServiceActionButtonAgree": "同意する", "termsOfServiceDisclaimerFeesDisabledInterpolated": "<1>\"同意する\"</1> をクリックすることで、Phantom によるトークンスワップの<3>諸条件</3>を承諾したものと見なされます。", "termsOfServiceDiscliamerFeesEnabledInterpolated": "利用規約を改定しました。<1>\"同意する\"</1> をクリックすることで、新しい<3>利用規約</3>を承諾したものと見なされます。<5></5><6></6>新しい利用規約には特定の商品向けの新しい<8>手数料システム</8>が記載されています。", "termsOfServicePrimaryText": "サービス利用規約", "tokenRowUnknownToken": "不明なトークン", "transactionsAppInteraction": "アプリインタラクション", "transactionsFailedAppInteraction": "失敗したアプリインタラクション", "transactionsBidOnInterpolated": "{{name}}に入札", "transactionsBidFailed": "入札に失敗しました", "transactionsBoughtInterpolated": "購入済み{{name}}", "transactionsBoughtCollectible": "購入したコレクティブル", "transactionBridgeInitiated": "ブリッジが開始されました", "transactionBridgeInitiatedFailed": "ブリッジの開始に失敗しました", "transactionBridgeStatusLink": "LI.FI でステータスを確認", "transactionsBuyFailed": "購入に失敗しました", "transactionsBurnedSpam": "バーンされたスパム", "transactionsBurned": "バーン済み", "transactionsUnwrapped": "アンラップされました", "transactionsUnwrappedFailed": "アンラップに失敗しました", "transactionsCancelBidOnInterpolated": "{{name}} への入札をキャンセルしました", "transactionsCancelBidOnFailed": "入札のキャンセルに失敗しました", "transactionsError": "エラー", "transactionsFailed": "失敗", "transactionsSwapped": "スワップ済み", "transactionsFailedSwap": "スワップに失敗しました", "transactionsFailedBurn": "バーンに失敗しました", "transactionsFrom": "送信元", "transactionsListedInterpolated": "上場済み{{name}}", "transactionsListedFailed": "上場に失敗しました", "transactionsNoActivity": "活動はありません", "transactionsReceived": "受信済み", "transactionsReceivedInterpolated": "{{amount}} SOL を受け取りました", "transactionsSending": "送信中...", "transactionsPendingCreateListingInterpolated": "{{name}} を作成しています", "transactionsPendingEditListingInterpolated": "{{name}} を編集しています", "transactionsPendingSolanaPayTransaction": "Solana 支払トランザクションを確認中", "transactionsPendingRemoveListingInterpolated": "{{name}} を上場廃止しています", "transactionsPendingBurningInterpolated": "{{name}} をバーンしています", "transactionsPendingSending": "送信中", "transactionsPendingSwapping": "スワップ中", "transactionsPendingBridging": "ブリッジ中", "transactionsPendingApproving": "承認中", "transactionsPendingCreatingAndDelegatingStake": "ステークの作成と委任", "transactionsPendingDeactivatingStake": "ステークを解除", "transactionsPendingDelegatingStake": "ステークを委任", "transactionsPendingWithdrawingStake": "ステークを出金中", "transactionsPendingAppInteraction": "保留中のアプリインタラクション", "transactionsPendingBitcoinTransaction": "保留中の BTC トランザクション", "transactionsSent": "送信済み", "transactionsSendFailed": "送信に失敗しました", "transactionsSwapOn": "{{dappName}}でスワップ", "transactionsSentInterpolated": "{{amount}} SOL を送信しました", "transactionsSoldInterpolated": "販売済み{{name}}", "transactionsSoldCollectible": "販売したコレクティブル", "transactionsSoldFailed": "販売に失敗しました", "transactionsStaked": "ステーク済み", "transactionsStakedFailed": "ステークに失敗しました", "transactionsSuccess": "成功", "transactionsTo": "送信先", "transactionsTokenSwap": "トークンスワップ", "transactionsUnknownAmount": "不明", "transactionsUnlistedInterpolated": "上場廃止済み{{name}}", "transactionsUnstaked": "ステーク解除済み", "transactionsUnlistedFailed": "上場廃止に失敗しました", "transactionsDeactivateStake": "解除されたステーク", "transactionsDeactivateStakeFailed": "ステークの解除に失敗しました", "transactionsWaitingForConfirmation": "確認待ち", "transactionsWithdrawStake": "ステークの出金", "transactionsWithdrawStakeFailed": "ステーク解除に失敗しました", "transactionCancelled": "キャンセルされました", "transactionCancelledFailed": "キャンセルに失敗しました", "transactionApproveToken": "承認された {{tokenSymbol}}", "transactionApproveTokenFailed": "{{tokenSymbol}} の承認に失敗しました", "transactionApprovalFailed": "承認に失敗しました", "transactionRevokeApproveToken": "取り消された {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "{{tokenSymbol}} の取り消しに失敗しました", "transactionRevokeFailed": "取り消しに失敗しました", "transactionApproveDetailsTitle": "取引承認の詳細", "transactionCancelOrder": "注文のキャンセル", "transactionCancelOrderFailed": "注文のキャンセルに失敗しました", "transactionApproveAppLabel": "アプリ", "transactionApproveAmountLabel": "金額", "transactionApproveTokenLabel": "トークン", "transactionApproveCollectionLabel": "コレクション", "transactionApproveAllItems": "項目を全て承認", "transactionSpendUpTo": "支出上限", "transactionCancel": "トランザクションのキャンセル", "transactionPrioritizeCancel": "キャンセルの優先", "transactionSpeedUp": "トランザクションを加速", "transactionCancelHelperText": "元のトランザクションはキャンセルされる前に完了できます。", "transactionSpeedUplHelperText": "これはネットワークの状態を基にトランザクションの速度を最大化します。", "transactionCancelHelperMobile": "このトランザクションをキャンセルするには <1>最大で {{amount}}</1> かかります。元のトランザクションはキャンセルされる前に完了することができます。", "transactionCancelHelperMobileWithEstimate": "このトランザクションをキャンセルするには <1>最大で {{amount}} </1> かかります。約 {{timeEstimate}} で完了します。元のトランザクションはキャンセルされる前に完了することができます。", "transactionSpeedUpHelperMobile": "このトランザクションの速度を最大化するには、<1>最大で {{amount}} </1> かかります。", "transactionSpeedUpHelperMobileWithEstimate": "このトランザクションの速度を最大化するには、<1>最大で {{amount}} </1> かかります。約 {{timeEstimate}} で完了します。", "transactionEstimatedTime": "予想時間", "transactionCancelingSend": "送信をキャンセル中", "transactionPrioritizingCancel": "キャンセルを優先中", "transactionCanceling": "キャンセル中", "transactionReplaceError": "エラーが発生しました。お使いのアカウントに料金が請求されませんでした。再試行することができます。", "transactionNotEnoughNative": "{{nativeTokenSymbol}} が足りません", "transactionGasLimitError": "GAS 制限の予想に失敗しました", "transactionGasEstimationError": "GAS の予想に失敗しました", "pendingTransactionCancel": "キャンセル", "pendingTransactionSpeedUp": "加速", "pendingTransactionStatus": "状態", "pendingTransactionPending": "保留中", "pendingTransactionPendingInteraction": "保留中のアプリインタラクション", "pendingTransactionCancelling": "キャンセル中", "pendingTransactionDate": "日付", "pendingTransactionNetworkFee": "ネットワーク手数料", "pendingTransactionEstimatedTime": "予想時間", "pendingTransactionEstimatedTimeHM": "{{hours}} 時間 {{minutes}} 分", "pendingTransactionEstimatedTimeMS": "{{minutes}} 分 {{seconds}} 秒", "pendingTransactionEstimatedTimeS": "{{seconds}} 秒", "pendingTransactionsSendingTitle": "{{assetSymbol}} を送信中", "pendingTransactionsUnknownEstimatedTime": "不明", "pendingTransactionUnknownApp": "不明アプリ", "permanentDelegateTitle": "委任しました", "permanentDelegateValue": "永久", "permanentDelegateTooltipTitle": "永久委任", "permanentDelegateTooltipValue": "永久委任を利用すると、あなたに代わって別のアカウントがトークンを管理します。これにはバーンや転送が含まれます。", "unlockActionButtonUnlock": "ロック解除", "unlockEnterPassword": "パスワードを入力してください", "unlockErrorIncorrectPassword": "パスワードが正しくありません", "unlockErrorSomethingWentWrong": "不具合が発生しました。後で再試行してください。", "unlockForgotPassword": "パスワードを忘れた", "unlockPassword": "パスワード", "forgotPasswordText": "12-24単語のリカバリフレーズを入力することでパスワードをリセットできます。Phantomはあなたに代わってパスワードを復元することはできません。", "appInfo": "アプリ情報", "lastUsed": "最終使用日", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "ハードウェアアカウントでは利用できません。", "trustedAppAutoConfirmDisclaimer1": "有効化すると、Phantom は、通知したり確認を必要としたりせずにこのアプリのリクエストをすべて確認します。", "trustedAppAutoConfirmDisclaimer2": "有効にすると資金を詐欺の危険にさらしてしまう可能性があります。この機能は、信頼できるアプリのみで使用してください。", "validationUtilsPasswordIsRequired": "パスワードは必須です", "validationUtilsPasswordLength": "パスワードの長さは 8 文字にしてください", "validationUtilsPasswordsDontMatch": "パスワードが一致していません", "validationUtilsPasswordCantBeSame": "古いパスワードは使用できません", "validatorCardEstimatedApy": "予想年利", "validatorCardCommission": "手数料", "validatorCardTotalStake": "合計ステーク", "validatorCardNumberOfDelegators": "委任者の数", "validatorListChooseAValidator": "バリデーターの選択", "validatorListErrorFetching": "バリデーターを取得できませんでした。後で再試行してください。", "validatorListNoResults": "結果はありません", "validatorListReload": "再読み込み", "validatorInfoTooltip": "バリデーター", "validatorInfoTitle": "バリデーター", "validatorInfoDescription": "SOL をバリデーターにステークすることで、SOL を獲得しつつ、 Solana ネットワークのパフォーマンスと安全に貢献することができます。", "validatorApyInfoTooltip": "予想年利", "validatorApyInfoTitle": "予想年利", "validatorApyInfoDescription": "こちらがバリデーターに SOL をステークすることで得られる利益率です。", "validatorViewActionButtonStake": "ステーク", "validatorViewErrorFetching": "バリデーターを取得できませんでした。", "validatorViewInsufficientBalance": "残高不足", "validatorViewMax": "最大", "validatorViewPrimaryText": "ステーキング開始", "validatorViewDescriptionInterpolated": "このバリデーターでステークしたい<1></1> SOL の数量を選択してください。<3>詳細</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "ステークするには {{amount}} SOL が必要です", "validatorViewValidator": "バリデーター", "walletMenuItemsAddConnectWallet": "ウォレットの追加 / 接続", "walletMenuItemsBridgeAssets": "資産のブリッジ", "walletMenuItemsHelpAndSupport": "ヘルプとサポート", "walletMenuItemsLockWallet": "ウォレットのロック", "walletMenuItemsResetSecretPhrase": "シークレットフレーズのリセット", "walletMenuItemsShowMoreAccounts": "その他 {{count}} 件を表示…", "walletMenuItemsHideAccounts": "アカウントを隠す", "toggleMultiChainHeader": "マルチチェーン", "disableMultiChainHeader": "Solana 専用モード", "disableMultiChainDetail1Header": "Solana だけで取引します", "disableMultiChainDetail1SecondaryText": "他のチェーンを表示せずに、<PERSON><PERSON> のアカウントやトークン、コレクティブルを管理します。", "disableMultiChainDetail2Header": "いつでもマルチチェーンの利用を再開できます", "disableMultiChainDetail2SecondaryText": "マルチチェーンを再有効化すると既存の Ethereum と Polygon の残高は保持されます。", "disableMultiChainButton": "Solana 専用モードを有効化する", "disabledMultiChainHeader": "Solana 専用モードを有効化しました", "disabledMultiChainText": "いつでもマルチチェーンを再有効化できます", "enableMultiChainHeader": "マルチチェーンを有効化", "enabledMultiChainHeader": "マルチチェーンが有効化されました", "enabledMultiChainText": "Ethereum と Polygon がウォレットでサポートされるようになりました。", "incompatibleAccountHeader": "サポートされていないアカウント", "incompatibleAccountInterpolated": "Solana 専用モードを有効化する前に次の Ethereum 専用のアカウントを削除してください：<1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "最新ニュース！", "welcomeToMultiChainPrimaryText": "一つのウォレットですべてが可能", "welcomeToMultiChainDetail1Header": "Ethereum と Polygon のサポート", "welcomeToMultiChainDetail1SecondaryText": "Solana、Ethereum 、そして Polygon のトークンや NFT を一元管理。", "welcomeToMultiChainDetail2Header": "好みのアプリを使用可能", "welcomeToMultiChainDetail2SecondaryText": "ネットワークを切り替えずに複数のチェーンでアプリと接続。", "welcomeToMultiChainDetail3Header": "お使いの MetaMask ウォレットをインポート", "welcomeToMultiChainDetail3SecondaryText": "すべてのシードフレーズを Ethereum と Polygon を横断して簡単にインポート。", "welcomeToMultiChainIntro": "Phantom Multichain にようこそ", "welcomeToMultiChainIntroDesc": "Solana、Ethereum、Polygon のトークンをすべて一か所に集めた万能のウォーレットです。", "welcomeToMultiChainAccounts": "マルチチェーンアカウントの作り直し", "welcomeToMultiChainAccountsDesc": "各アカウントに該当する ETH および Polygon のアドレスがあるように、マルチチェーン向けに作り直しました。", "welcomeToMultiChainApps": "どこでも利用できます", "welcomeToMultiChainAppsDesc": "Phantomは、Ethereum、Polygon、Solana 上のすべてのアプリに対応しています。「MetaMask に接続」をクリックして開始しましょう。", "welcomeToMultiChainImport": "MetaMask から瞬時にインポート", "welcomeToMultiChainImportDesc": "MetaMask や Coinbase Wallet などのウォレットからシークレットフレーズまたは秘密鍵をインポートできます。すべてを一か所で管理。", "welcomeToMultiChainImportInterpolated": "<0>MetaMask や Coinbase Wallet</0> などのウォレットからシークレットフレーズまたは秘密鍵をインポートできます。すべてを一か所で管理。", "welcomeToMultiChainTakeTour": "見る", "welcomeToMultiChainSwapperTitle": "Ethereum、Polygon、\nSolana でスワップ", "welcomeToMultiChainSwapperDetail1Header": "Ethereum と Polygon のサポート", "welcomeToMultiChainSwapperDetail1SecondaryText": "ウォレット内で ERC-20 トークンを簡単にスワップできるようになりました。", "welcomeToMultiChainSwapperDetail2Header": "ベストプライスと安価な手数料", "welcomeToMultiChainSwapperDetail2SecondaryText": "100 以上の流動性のソースとスマートオーダールーティングにより、最大の利益を実現。", "networkErrorTitle": "ネットワークエラー", "networkError": "残念ながら、ネットワークをアクセスできません。後で再試行してください。", "authenticationUnlockPhantom": "Phantom をロック解除", "errorAndOfflineSomethingWentWrong": "不具合が発生しました", "errorAndOfflineSomethingWentWrongTryAgain": "再試行してください。", "errorAndOfflineUnableToFetchAssets": "資産を取得できませんでした。後で再試行してください。", "errorAndOfflineUnableToFetchCollectibles": "コレクティブルを取得できませんでした。後で再試行してください。", "errorAndOfflineUnableToFetchSwap": "スワップ情報を取得できませんでした。後で再試行してください。", "errorAndOfflineUnableToFetchTransactionHistory": "現在は、トランザクション履歴を取得できません。ネットワークの接続を確認するか、後でもう一度お試しください。", "errorAndOfflineUnableToFetchRewardsHistory": "報酬履歴を取得できませんでした。後で再試行してください。", "errorAndOfflineUnableToFetchBlockedUsers": "ブロックされたユーザーを取得できませんでした。しばらくしてからもう一度お試しください。", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "注文の確認で不具合が発生しました。再試行してください。", "sendSelectToken": "トークンを選択", "swapBalance": "残高:", "swapTitle": "トークンをスワップ", "swapSelectToken": "トークンを選択", "swapYouPay": "支払額：", "swapYouReceive": "受領額：", "aboutPrivacyPolicy": "プライバシーポリシー", "aboutVersion": "バージョン {{version}}", "aboutVisitWebsite": "Web サイトを訪問", "bottomSheetConnectTitle": "接続", "A11YbottomSheetConnectTitle": "ボトムシート 接続", "A11YbottomSheetCommandClose": "ボトムシート 拒否", "A11YbottomSheetCommandBack": "ボトムシート 戻る", "bottomSheetSignTypedDataTitle": "メッセージのサイン", "bottomSheetSignMessageTitle": "メッセージのサイン", "bottomSheetSignInTitle": "サインイン", "bottomSheetSignInAndConnectTitle": "サインイン", "bottomSheetConfirmTransactionTitle": "トランザクションの確認", "bottomSheetConfirmTransactionsTitle": "トランザクションの確認", "bottomSheetSolanaPayTitle": "Solana Pay リクエスト", "bottomSheetAdvancedTitle": "高度", "bottomSheetReadOnlyAccountTitle": "閲覧専用モード", "bottomSheetTransactionSettingsTitle": "ネットワーク手数料", "bottomSheetConnectDescription": "紐づけることで、このサイトに選択されているアカウントの残高とアクティビティを確認する許可を与えます。", "bottomSheetSignInDescription": "このメッセージをサインすることで、選択されたアカウントの所有者であることを証明できます。信頼できるアプリケーションからのメッセージのみをサインしてください。", "bottomSheetSignInAndConnectDescription": "承認することで、このサイトに選択されているアカウントの残高とアクティビティを確認する許可を与えます。", "bottomSheetConfirmTransactionDescription": "残高の変化は推定値です。関係する金額および資産は保証されません。", "bottomSheetConfirmTransactionsDescription": "残高の変化は推定値です。関係する金額および資産は保証されません。", "bottomSheetSignTypedDataDescription": "これは許可のリクエストにすぎません。トランザクションはすぐには実行されない可能性があります。", "bottomSheetSignTypedDataSecondDescription": "残高の変化は推定値です。関係する金額および資産は保証されません。", "bottomSheetSignMessageDescription": "このメッセージをサインすることで、選択されたアカウントの所有者であることを証明できます。信頼できるアプリケーションからのメッセージのみをサインしてください。", "bottomSheetReadOnlyAccountDescription": "閲覧専用モードではこのアクションを実行することはできません。", "bottomSheetMessageRow": "メッセージ", "bottomSheetStatementRow": "明細", "bottomSheetAutoConfirmRow": "自動確認", "bottomSheetAutoConfirmOff": "オフ", "bottomSheetAutoConfirmOn": "オン", "bottomSheetAccountRow": "アカウント", "bottomSheetAdvancedRow": "高度", "bottomSheetContractRow": "契約アドレス", "bottomSheetSpenderRow": "買い手アドレス", "bottomSheetNetworkRow": "ネットワーク", "bottomSheetNetworkFeeRow": "ネットワーク手数料", "bottomSheetEstimatedTimeRow": "予想時間", "bottomSheetAccountRowDefaultAccountName": "アカウント", "bottomSheetConnectRequestDisclaimer": "信頼する Web サイトにのみ接続してください", "bottomSheetSignInRequestDisclaimer": "信頼する Web サイトにのみサインインしてください", "bottomSheetSignatureRequestDisclaimer": "この Web サイトを信頼しているときにのみ確認してください。", "bottomSheetFeaturedTransactionDisclaimer": "次のステップで確認する前に、取引のプレビューが表示されます。", "bottomSheetIgnoreWarning": "警告を無視して継続", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "残高の変更が検出されませんでした。このサイトを信頼できることを確認してから、慎重に続行してください。", "bottomSheetReadOnlyWarning": "このアドレスは監視専用として設定されています。トランザクションやメッセージにサインするには該当するシークレットフレーズをインポートする必要があります。", "bottomSheetWebsiteIsUnsafeWarning": "この Web サイトは、資金を盗もうとする可能性があり危険です。", "bottomSheetViewOnExplorer": "Explorer で表示", "bottomSheetTransactionSubmitted": "トランザクションを送信しました", "bottomSheetTransactionPending": "トランザクションは保留中", "bottomSheetTransactionFailed": "トランザクションに失敗しました", "bottomSheetTransactionSubmittedDescription": "トランザクションは送信されました。Explorer で確認できます。", "bottomSheetTransactionFailedDescription": "トランザクションに失敗しました。もう一度お試しください。", "bottomSheetTransactionPendingDescription": "トランザクションは処理中です…", "transactionsFromInterpolated": "{{from}} から", "transactionsFromParagraphInterpolated": "{{from}} から", "transactionsSolInterpolated": "{{amount}} SOL ", "transactionsToday": "今日", "transactionsToInterpolated": "{{to}} へ", "transactionsToParagraphInterpolated": "{{to}} へ", "transactionsYesterday": "昨日", "addEditAddressAdd": "アドレスの追加", "addEditAddressDelete": "アドレスの削除", "addEditAddressDeleteTitle": "本当にこのアドレスを削除しますか？", "addEditAddressSave": "アドレスを保存", "dAppBrowserComingSoon": "dApp ブラウザが間もなく登場！", "dAppBrowserSearchPlaceholder": "サイト、トークン、URL", "dAppBrowserOpenInNewTab": "新しいタブで開く", "dAppBrowserSuggested": "推奨ブラウザー", "dAppBrowserFavorites": "お気に入り", "dAppBrowserBookmarks": "お気に入り", "dAppBrowserBookmarkAdd": "お気に入りを追加", "dAppBrowserBookmarkRemove": "お気に入りを削除", "dAppBrowserUsers": "ユーザー", "dAppBrowserRecents": "最新", "dAppBrowserFavoritesDescription": "お気に入りはここに表示されます", "dAppBrowserBookmarksDescription": "ここにはお気に入りが表示されます", "dAppBrowserRecentsDescription": "ここには最近接続された dApp が表示されます", "dAppBrowserEmptyScreenDescription": "URLを入力するかWebを検索します", "dAppBrowserBlocklistScreenTitle": "{{origin}} はブロックされています！ ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom はこの Web サイトに悪意があり、使用するのは危険だと判断しました。", "part2": "このサイトは既知のフィッシングサイトや詐欺に関するコミュニティ管理のデータベースに登録されています。このサイトが誤って登録されていると思われる場合は、問題を報告してください。"}, "dAppBrowserLoadFailedScreenTitle": "読み込めませんでした", "dAppBrowserLoadFailedScreenDescription": "ページを読み込む際にエラーが発生しました", "dAppBrowserBlocklistScreenIgnoreButton": "警告を無視して表示", "dAppBrowserActionBookmark": "お気に入り", "dAppBrowserActionRemoveBookmark": "お気に入りを削除", "dAppBrowserActionRefresh": "更新", "dAppBrowserActionShare": "共有", "dAppBrowserActionCloseTab": "タブを閉じる", "dAppBrowserActionEndAutoConfirm": "自動確認を終了", "dAppBrowserActionDisconnectApp": "アプリを切断", "dAppBrowserActionCloseAllTabs": "全てのタブを閉じる", "dAppBrowserNavigationAddressPlaceholder": "URL を入力して検索", "dAppBrowserTabOverviewMore": "さらに表示", "dAppBrowserTabOverviewAddTab": "タブを追加", "dAppBrowserTabOverviewClose": "閉じる", "dAppBrowserCloseTab": "タブを閉じる", "dAppBrowserClose": "閉じる", "dAppBrowserTabOverviewAddBookmark": "お気に入りを追加", "dAppBrowserTabOverviewRemoveBookmark": "お気に入りを削除", "depositAssetListSuggestions": "提案", "depositUndefinedToken": "申し訳ございません。このトークンを入金できません", "onboardingImportRecoveryPhraseDetails": "詳細", "onboardingCreateRecoveryPhraseVerifyTitle": "シークレットリカバリフレーズを書き留めましたか？", "onboardingCreateRecoveryPhraseVerifySubtitle": "シークレットリカバリフレーズがないと、お使いの鍵や関連する資産をアクセスできません。", "onboardingCreateRecoveryPhraseVerifyYes": "はい", "onboardingCreateRecoveryPhraseErrorTitle": "エラー", "onboardingCreateRecoveryPhraseErrorSubtitle": "アカウント生成に失敗しました。再試行してください。", "onboardingDoneDescription": "これでウォレットを最大限に活用できます。", "onboardingDoneGetStarted": "始める", "zeroBalanceHeading": "始めましょう！", "zeroBalanceBuyCryptoTitle": "暗号通貨を購入", "zeroBalanceBuyCryptoDescription": "デビットカードまたはクレジットカードで初めての暗号通貨を購入します。", "zeroBalanceDepositTitle": "暗号通貨の転送", "zeroBalanceDepositDescription": "別のウォレットまたは取引所から暗号通貨を転送します。", "onboardingImportAccountsEmptyResult": "アカウントが見つかりませんでした", "onboardingImportAccountsAccountName": "アカウント {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "ソーシャルアカウント", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "{{numberOfWallets}} 個のアカウントでアクティビティが確認されました", "onboardingImportAccountsFoundAccounts_other": "{{numberOfWallets}} 個のアカウントでアクティビティが確認されました", "onboardingImportAccountsFoundAccountsNoActivity_one": "{{numberOfWallets}} アカウントを見つけました", "onboardingImportAccountsFoundAccountsNoActivity_other": "{{numberOfWallets}} アカウントを見つけました", "onboardingImportRecoveryPhraseLessThanTwelve": "フレーズは最低で 12 ワードが必要です。", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "フレーズは正確に 12 または 24 ワードでなくてはなりません。", "onboardingImportRecoveryPhraseWrongWord": "次の言葉が間違っています：{{ words }}。", "onboardingProtectTitle": "ウォレットの保護", "onboardingProtectDescription": "生体認証機能の追加は、他人がお使いのウォレットをアクセスできないようにします。", "onboardingProtectButtonHeadlineDevice": "デバイス", "onboardingProtectButtonHeadlineFaceID": "顔認証", "onboardingProtectButtonHeadlineFingerprint": "指紋認証", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "{{ authType }} 認証を使用", "onboardingProtectError": "認証で不具合が発生しました。再試行してください。", "onboardingProtectBiometryIosError": "生体認証は Phantom では設定されていますが、[システム設定] では無効になっています。[設定] > [Phantom] > [Face ID] または [Touch ID] の順で選択し、生体認証を有効にしてください。", "onboardingProtectRemoveAuth": "認証を無効にする", "onboardingProtectRemoveAuthDescription": "本当に認証を無効にしますか？", "onboardingWelcomeTitle": "Phantom にようこそ", "onboardingWelcomeDescription": "始めるには、新しいウォレットを作成するか、既存のウォレットをインポートします。", "onboardingWelcomeCreateWallet": "新規ウォレットの作成", "onboardingWelcomeAlreadyHaveWallet": "すでにウォレットを持っています", "onboardingWelcomeConnectSeedVault": "シード保管庫を接続", "onboardingSlide1Title": "あなたの手の内に", "onboardingSlide1Description": "あなたのウォレットは、生体認証アクセス、詐欺検出、24 時間 365 日のサポートにより保護されています。", "onboardingSlide2Title": "あなたの NFT の\n最善の保管先", "onboardingSlide2Description": "上場を管理し、スパムをバーンし、そしてプッシュ通知で最新ニュースを確認しましょう。", "onboardingSlide3Title": "トークンを最大限に使用", "onboardingSlide3Description": "保管、スワップ、ステーク、送信、受け取りのすべてがウォレット内で完結します。", "onboardingSlide4Title": "Web3 の最善を引き出す", "onboardingSlide4Description": "アプリ内ブラウザを使用して、主要なアプリやコレクションを見つけて接続します。", "onboardingMultichainSlide5Title": "一つのウォレットですべてが可能", "onboardingMultichainSlide5Description": "単一の使いやすいインターフェースで、Solana、Ethereum、そして Polygon の全機能を体験しましょう。", "onboardingMultichainSlide5DescriptionWithBitcoin": "単一の使いやすいインターフェースで、Solana、Ethereum、Polygon、そして Bitcoin の全機能を体験しましょう。", "requireAuth": "認証を要求", "requireAuthImmediately": "今すぐ", "availableToSend": "送信可能な金額", "sendEnterAmount": "金額を入力", "sendEditMemo": "メモを編集", "sendShowLogs": "エラーログを表示", "sendHideLogs": "エラーログを隠す", "sendGoBack": "戻る", "sendTransactionSuccess": "トークンは次の宛先に正常に送信されました：", "sendInputPlaceholder": "@ユーザー名またはアドレス", "sendInputPlaceholderV2": "ユーザー名またはアドレス", "sendPeopleTitle": "人", "sendDomainTitle": "ドメイン", "sendFollowing": "フォロー中", "sendRecentlyUsedAddressLabel": "{{formattedTimestamp}} 前に使用", "sendRecipientAddress": "受信者のアドレス", "sendTokenInterpolated": "{{tokenSymbol}} を送信", "sendPasteFromClipboard": "クリップボードから貼り付け", "sendScanQR": "QR コードのスキャン", "sendTo": "宛先:", "sendRecipientZeroBalanceWarning": "このウォレット アドレスには残高がなく、最近の取引履歴にも表示されません。アドレスが正しいことを確認してください。", "sendUnknownAddressWarning": "最近やり取りしたことがないアドレスです。注意して続行してください。", "sendSameAddressWarning": "これはあなたの現在のアドレスです。送金すると、送金手数料だけ差し引かれ、残高は変わりません。", "sendMintAccountWarning": "これはミントアカウントアドレスです。このアドレスに資金を送金すると資金が永久的に失われるため、送金できません。", "sendCameraAccess": "カメラへのアクセス", "sendCameraAccessSubtitle": "QR コードをスキャンするには、カメラへのアクセス権限を許可する必要があります。今すぐ設定を開きますか？", "sendSettings": "設定", "sendOK": "OK", "invalidQRCode": "この QR コードは有効ではありません。", "sendInvalidQRCode": "この QR コードは有効なアドレスではありません", "sendInvalidQRCodeSubtitle": "再試行するか、別の QR コードを使用しましょう。", "sendInvalidQRCodeSplToken": "QR コードに無効なトークンがあります", "sendInvalidQRCodeSplTokenSubtitle": "この QR コードはあなたが所有していない、または弊社が特定できないトークンを含んでいます。", "sendScanAddressToSend": "{{tokenSymbol}}アドレスをスキャンして資金を送信", "sendScanAddressToSendNoSymbol": "アドレスをスキャンして資金を送信", "sendScanAddressToSendCollectible": "SOL アドレスをスキャンしてコレクティブルを送信", "sendScanAddressToSendCollectibleMultichain": "アドレスをスキャンしてコレクティブルを送信", "sendSummary": "概要", "sendUndefinedToken": "申し訳ございません。このトークンを送信できません", "sendNoTokens": "利用可能なトークンがありません", "noBuyOptionsAvailableInCountry": "お住まいの地域では購入オプションがありません", "swapAvailableTokenDisclaimer": "ネットワーク間のブリッジに使用できるトークンの数には制限があります", "swapCrossSwapNetworkTooltipTitle": "ネットワーク間のスワップ", "swapCrossSwapNetworkTooltipDescription": "ネットワーク間でスワップする場合は、最低価格と最速のトランザクションを実現するために利用可能なトークンを使用することをお勧めします。", "settingsAbout": "Phantom について", "settingsShareAppWithFriends": "友達を招待する", "settingsConfirm": "はい", "settingsMakeSureNoOneIsWatching": "誰かにスクリーンが見られていないことを確認してください", "settingsManageAccounts": "アカウントの管理", "settingsPrompt": "本当に続行しますか？", "settingsSelectAvatar": "アバターの選択", "settingsSelectSecretPhrase": "シークレットフレーズの選択", "settingsShowPrivateKey": "タップして秘密鍵を表示", "settingsShowRecoveryPhrase": "タップしてシークレットフレーズを表示", "settingsSubmitBetaFeedback": "ベータ版のフィードバックを送信", "settingsUpdateAccountNameToast": "アカウント名を更新しました", "settingsUpdateAvatarToast": "アバターを更新しました", "settingsUpdateAvatarToastFailure": "アバターの更新に失敗しました。", "settingsWalletAddress": "アカウントアドレス", "settingsWalletAddresses": "アカウントアドレス", "settingsWalletNamePrimary": "アカウント名", "settingsPlaceholderName": "名前", "settingsWalletNameSecondary": "ウォレットの名前の変更", "settingsYourAccounts": "あなたのアカウント", "settingsYourAccountsMultiChain": "マルチチェーン", "settingsReportUser": "ユーザーを報告", "settingsNotifications": "通知", "settingsNotificationPreferences": "通知の設定", "pushNotificationsPreferencesAllowNotifications": "通知を許可", "pushNotificationsPreferencesSentTokens": "送信済みトークン", "pushNotificationsPreferencesSentTokensDescription": "トークンや NFT の外部への転送", "pushNotificationsPreferencesReceivedTokens": "受信済みトークン", "pushNotificationsPreferencesReceivedTokensDescription": "トークンや NFT の内部への転送", "pushNotificationsPreferencesDexSwap": "スワップ", "pushNotificationsPreferencesDexSwapDescription": "公認アプリケーションでのスワップ", "pushNotificationsPreferencesOtherBalanceChanges": "その他の残高推移", "pushNotificationsPreferencesOtherBalanceChangesDescription": "残高に影響を与えたその他のマルチトークントランザクション", "pushNotificationsPreferencesPhantomMarketing": "Phantom からのアップデート", "pushNotificationsPreferencesPhantomMarketingDescription": "新機能のお知らせとアップデート全般", "pushNotificationsPreferencesDescription": "これらの設定は当アクティブウォレットのプッシュ通知を制御します。各ウォレットは独自の通知設定があります。すべての Phantom プッシュ通知をオフにするには<1>デバイス設定</1>に移動してください。", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "通知の設定を同期することができませんでした。", "connectSeedVaultConnectSeed": "シートを接続", "connectSeedVaultConnectSeedDescription": "お使いのスマートフォンでPhantomをシード保管庫に接続", "connectSeedVaultSelectAnAccount": "アカウントを選択", "connectSeedVaultSelectASeed": "シートを選択", "connectSeedVaultSelectASeedDescription": "Phantom に接続するシードを選択します", "connectSeedVaultSelectAnAccountDescription": "Phantom にセットアップしたいアカウントを選択してください", "connectSeedVaultNoAccountsFound": "アカウントが見つかりませんでした.", "connectSeedVaultSelectAccounts": "アカウントを選択", "connectSeedVaultSelectAccountsDescription": "Phantom にセットアップしたいアカウントを選択してください", "connectSeedVaultCompleteSetup": "セットアップを完了", "connectSeedVaultCompleteSetupDescription": "準備完了！Phantomでweb3を検索し、シード保管庫を使用してトランザクションを承認しましょう", "connectSeedVaultConnectAnotherSeed": "他のシードを接続", "connectSeedVaultConnectAllSeedsConnected": "全てのシードが接続されています", "connectSeedVaultNoSeedsConnected": "シードが接続されていません。以下のボタンをタップして、シード保管庫から承認してください。", "connectSeedVaultConnectAccount": "アカウントを接続", "connectSeedVaultLoadMore": "さらに読み込む", "connectSeedVaultNeedPermission": "許可が必要です", "connectSeedVaultNeedPermissionDescription": "[設定] に移動して、Phantom がシード保管庫権限を使用できるようにします。", "stakeApy": "APY {{apyPercentage}}", "stakeFee": "{{fee}} の手数料", "stakeAmount": "金額", "stakeAmountBalance": "残高", "swapTopQuotes": "トップ{{numQuotes}}の見積", "swapTopQuotesTitle": "トップ時価", "swapProvidersTitle": "プロバイダー", "swapProvidersFee": "{{fee}} の手数料", "swapProvidersTagRecommended": "最大の利益", "swapProvidersTagFastest": "最速", "swapProviderEstimatedTimeHM": "{{hours}} 時間 {{minutes}} 分", "swapProviderEstimatedTimeM": "{{minutes}} 分", "swapProviderEstimatedTimeS": "{{seconds}} 秒", "stakeReview": "確認", "stakeReviewAccount": "アカウント", "stakeReviewCommissionFee": "手数料", "stakeReviewConfirm": "確認", "stakeReviewValidator": "バリデーター", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "ステークの変換に失敗しました", "convertStakeStatusErrorMessage": "ステークを {{poolTokenSymbol}} に変換できませんでした。もう一度試してください。", "convertStakeStatusLoadingTitle": "{{poolTokenSymbol}} に変換しています", "convertStakeStatusLoadingMessage": "ステークした {{stakedTokenSymbol}} を {{poolTokenSymbol}} に変換するプロセスを開始しています", "convertStakeStatusSuccessTitle": "{{poolTokenSymbol}} への変換が完了しました！", "convertStakeStatusSuccessMessage": "JitoSOL を使用して <1>こちらから</1> 追加の報酬を獲得しましょう", "convertStakeStatusConvertMore": "もっと変換する", "convertStakePendingTitle": "ステークを {{symbol}} に変換しています", "convertToJitoSOL": "JitoSOL に変換", "convertToJitoSOLInfoDescription": "SOL を JitoSOL に変換して報酬を獲得し、Jito エコシステムに参加しましょう。", "convertToJitoSOLInfoTitle": "JitoSOL への変換", "convertStakeBannerTitle": "ステークを JitoSOL に変換すると、報酬が最大 15% 増加します", "convertStakeQuestBannerTitle": "ステークされた SOL を JitoSOL に変換して報酬を獲得しましょう。詳細についてはこちらをご覧ください。", "liquidStakeConvertInfoTitle": "JitoSOL に変換", "liquidStakeConvertInfoDescription": "SOL ステークを JitoSOL に変換して報酬を増やしましょう。<1>詳しくはこちらをご覧ください</1>", "liquidStakeConvertInfoFeature1Title": "Jito を使用してステークするメリットは？", "liquidStakeConvertInfoFeature1Description": "入金して、ステークに応じて成長する JitoSOL を入手しましょう。DeFi プロトコルで使用すると追加の収益が得られます。後で JitoSOL を初期金額 + 獲得した報酬と交換できます", "liquidStakeConvertInfoFeature2Title": "より高い平均利益率", "liquidStakeConvertInfoFeature2Description": "Jito では、最低手数料で最高のバリデーターに SOL を分散します。MEV 報酬により収益がさらに向上します。", "liquidStakeConvertInfoFeature3Title": "Solana ネットワークを支援", "liquidStakeConvertInfoFeature3Description": "リキッドステーキングは、ステークを複数のバリデーターに分散することで Solana を保護し、稼働時間の低いバリデーターによるリスクを軽減します。", "liquidStakeConvertInfoSecondaryButton": "後で", "liquidStakeStartStaking": "ステーキング開始", "liquidStakeReviewOrder": "注文の確認", "convertStakeAccountListPageIneligibleSectionTitle": "対象外のステークアカウント", "convertStakeAccountIneligibleBottomSheetTitle": "対象外のステークアカウント", "convertStakeAccountListPageErrorTitle": "ステークアカウントの取得に失敗しました", "convertStakeAccountListPageErrorDescription": "申し訳ありません。不具合が発生したためステークアカウントを取得できませんでした", "liquidStakeReviewYouPay": "支払額：", "liquidStakeReviewYouReceive": "受領額：", "liquidStakeReviewProvider": "プロバイダー", "liquidStakeReviewNetworkFee": "ネットワーク手数料", "liquidStakeReviewPageTitle": "確認", "liquidStakeReviewConversionFootnote": "JitoSOL と引き換えに Solana トークンをステークすると、受け取れる JitoSOL の量が若干少なくなります。<1>詳細はこちらをご覧ください</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Jito のステークプールは、アクティブな Solana バリデーターのほとんどをサポートしています。サポートされていないバリデーターからステークされた SOL を JitoSOL に変換することはできません。さらに、新しくステークされた SOL は、JitoSOL 変換の対象となるまでに最大 2 日かかります。", "selectAValidator": "バリデーターを選択", "validatorSelectionListTitle": "バリデーターの選択", "validatorSelectionListDescription": "SOL をステークするバリデーターを選択します。", "stakeMethodDescription": "SOL トークンを使用して利息を得て、Solana の拡張に貢献しましょう。<1>詳細はこちらをご覧ください</1>", "stakeMethodRecommended": "おすすめ", "stakeMethodEstApy": "予想年利：~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "リキッドステーキング", "stakeMethodSelectionLiquidStakingDescription": "SOL をステークすると、より高い報酬を獲得したり、Solana の安全確保に貢献したり、JitoSOL を取得して追加の報酬を獲得したりできます。", "stakeMethodSelectionNativeStakingTitle": "ネイティブステーキング", "stakeMethodSelectionNativeStakingDescription": "SOL をステークして報酬を獲得し、<PERSON><PERSON> の安全確保に貢献しましょう。", "liquidStakeMintStakeSOL": "SOL のステーク", "mintJitoSOLInfoPageTitle": "Jito を使ったリキッドステーキングが登場", "mintJitoSOLFeature1Title": "Jito を使用してステークするメリットは？", "mintJitoSOLFeature1Description": "入金して、ステークに応じて成長する JitoSOL を入手しましょう。DeFi プロトコルで使用すると追加の収益が得られます。後で JitoSOL を初期金額 + 獲得した報酬と交換できます", "mintJitoSOLFeature2Title": "より高い平均利益率", "mintJitoSOLFeature2Description": "Jito では、最低手数料で最高のバリデーターに SOL を分散します。MEV 報酬により収益がさらに向上します。", "mintJitoSOLFeature3Title": "Solana ネットワークを支援", "mintJitoSOLFeature3Description": "リキッドステーキングは、ステークを複数のバリデーターに分散することで Solana を保護し、稼働時間の低いバリデーターによるリスクを軽減します。", "mintLiquidStakePendingTitle": "リキッドステーキングをミント中", "mintStakeStatusErrorTitle": "リキッドステーキングのミントに失敗しました", "mintStakeStatusErrorMessage": "{{poolTokenSymbol}} のリキッドステーキングをミントできませんでした。もう一度お試しください。", "mintStakeStatusSuccessTitle": "{{poolTokenSymbol}} のリキッドステーキングのミントが完了しました！", "mintStakeStatusLoadingTitle": "{{poolTokenSymbol}} リキッドステーキングをミント中", "mintStakeStatusLoadingMessage": "{{poolTokenSymbol}} リキッドステーキングのミントプロセスを開始しています。", "mintLiquidStakeAmountDescription": "Jito でステークする SOL の量を選択してください", "mintLiquidStakeAmountProvider": "プロバイダー", "mintLiquidStakeAmountApy": "予想年利", "mintLiquidStakeAmountBestPrice": "価格", "mintLiquidStakeAmountInsufficientBalance": "残高不足", "mintLiquidStakeAmountMinRequired": "ステークするには{{amount}} {{symbol}} が必要です", "swapTooltipGotIt": "分かりました", "swapTabInsufficientFunds": "資金不足", "swapNoAssetsFound": "資産がありません", "swapNoTokensFound": "トークンが見つかりません", "swapConfirmationTryAgain": "再試行", "swapConfirmationGoBack": "戻る", "swapNoQuotesFound": "見積が見つかりませんでした", "swapNotProviderFound": "このトークンスワップのためにプロバイダーを見つけることができませんでした。他のトークンをお試しください。", "swapAvailableOnMainnet": "この機能は Minnet 限定です", "swapNotAvailableEVM": "EVM アカウントでスワップはまだ利用できません", "swapNotAvailableOnSelectedNetwork": "選択したネットワークではスワップは利用できません", "singleChainSwapTab": "ネットワーク内", "crossChainSwapTab": "ネットワークの間", "allFilter": "全て", "bridgeRefuelTitle": "Refuel", "bridgeRefuelDescription": "Refuel を使用すると、ブリッジした後にあなたがトランザクションに対して支払えることが保証されます。", "bridgeRefuelLabelBalance": "あなたの {{symbol}}", "bridgeRefuelLabelReceive": "受領額：", "bridgeRefuelLabelFee": "推定コスト", "bridgeRefuelDismiss": "Refuelなしで続行", "bridgeRefuelEnable": "Refuelを有効化", "unwrapWrappedSolError": "アンラップ失敗", "unwrapWrappedSolLoading": "アンラップ中…", "unwrapWrappedSolSuccess": "アンラップされました", "unwrapWrappedSolViewTransaction": "トランザクションの表示", "dappApprovePopupSignMessage": "メッセージのサイン", "solanaPayFrom": "送信元", "solanaPayMessage": "メッセージ", "solanaPayNetworkFee": "ネットワーク手数料", "solanaPayFree": "無料", "solanaPayPay": "{{item}} を支払う", "solanaPayPayNow": "今すぐ支払う", "solanaPaySending": "{{item}} を送信中", "solanaPayReceiving": "{{item}} を受信中", "solanaPayMinting": "{{item}} をミント中", "solanaPayTransactionProcessing": "トランザクションの処理中です。\n少々お待ちください。", "solanaPaySent": "送信完了！", "solanaPayReceived": "受信しました！", "solanaPayMinted": "ミントしました！", "solanaPaySentNFT": "NFT を送信しました！", "solanaPayReceivedNFT": "NFT を受信しました！", "solanaPayTokensSent": "{{to}} にトークンが正常に送信されました", "solanaPayTokensReceived": "{{from}} から新しいトークンを受信しました", "solanaPayViewTransaction": "トランザクションの表示", "solanaPayTransactionFailed": "トランザクション失敗", "solanaPayConfirm": "確認", "solanaPayTo": "宛先:", "dappApproveConnectViewAccount": "Solana アカウントの表示", "deepLinkInvalidLink": "リンクが無効です", "deepLinkInvalidSplTokenSubtitle": "これにはあなたが所有していない、または識別不可能なトークンが含まれています。", "walletAvatarShowAllAccounts": "すべてのアカウントを表示", "pushNotificationsGetInstantUpdates": "即時アップデートの取得", "pushNotificationsEnablePushNotifications": "完了した転送やスワップ、そしてお知らせのプッシュ通知を有効化", "pushNotificationsEnable": "有効化", "pushNotificationsNotNow": "後で", "onboardingAgreeToTermsOfServiceInterpolated": "<1>利用規約</1>に同意します", "onboardingConfirmSaveSecretRecoveryPhrase": "OK、保存しました", "onboardingCreateNewWallet": "新規ウォレットの作成", "onboardingErrorDuplicateSecretRecoveryPhrase": "このシークレットフレーズはすでにウォレットに存在します", "onboardingErrorInvalidSecretRecoveryPhrase": "シークレットリカバリフレーズが無効です", "onboardingFinished": "すべて完了しました！", "onboardingImportAccounts": "アカウントをインポート", "onboardingImportImportingAccounts": "アカウントをインポート中...", "onboardingImportImportingFindingAccounts": "アクティビティのあるアカウントを検索中", "onboardingImportAccountsLastActive": "{{formattedTimestamp}} 前にアクティブ", "onboardingImportAccountsNeverUsed": "未使用", "onboardingImportAccountsCreateNew": "新規ウォレット", "onboardingImportAccountsDescription": "インポートするウォレットアカウントを選択してください", "onboardingImportReadOnlyAccountDescription": "監視したいアドレスまたはドメイン名を追加します。閲覧専用で、トランザクションやメッセージにサインすることはできません。", "onboardingImportSecretRecoveryPhrase": "シークレットフレーズのインポート", "onboardingImportViewAccounts": "アカウントを表示", "onboardingRestoreExistingWallet": "12 単語または 24 単語のシークレットリカバリフレーズで既存ウォレットを復元します", "onboardingShowUnusedAccounts": "未使用のアカウントを表示", "onboardingShowMoreAccounts": "その他のアカウントを表示", "onboardingHideUnusedAccounts": "未使用のアカウントを隠す", "onboardingSecretRecoveryPhrase": "シークレットリカバリフレーズ", "onboardingSelectAccounts": "アカウントを選択", "onboardingStoreSecretRecoveryPhraseReminder": "アカウントを復元できる唯一の手段です。安全な場所に保管してください！", "useTokenMetasForMintsUnknownName": "不明", "timeUnitMinute": "分", "timeUnitMinutes": "分", "timeUnitHour": "時", "timeUnitHours": "時", "espNFTListWithPrice": "{{dAppName}}で{{NFTDisplayName}}を{{priceAmount}}{{priceTokenSymbol}}で上場させました", "espNFTListWithPriceWithoutDApp": "{{NFTDisplayName}}を{{priceAmount}}{{priceTokenSymbol}}で上場させました", "espNFTListWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}を上場させました", "espNFTListWithoutPriceWithoutDApp": "{{NFTDisplayName}}を上場させました", "espNFTChangeListPriceWithPrice": "{{dAppName}}で{{NFTDisplayName}}の価格を{{priceAmount}}{{priceTokenSymbol}}に変更しました", "espNFTChangeListPriceWithPriceWithoutDApp": "{{NFTDisplayName}}の価格を{{priceAmount}}{{priceTokenSymbol}}に変更しました", "espNFTChangeListPriceWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}の上場情報を変更しました", "espNFTChangeListPriceWithoutPriceWithoutDApp": "{{NFTDisplayName}}の上場情報を変更しました", "espNFTBidBidderWithPrice": "{{dAppName}}で{{NFTDisplayName}}に{{priceAmount}}{{priceTokenSymbol}}を入札しました", "espNFTBidBidderWithPriceWithoutDApp": "{{NFTDisplayName}}に{{priceAmount}}{{priceTokenSymbol}}を入札しました", "espNFTBidBidderWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}に入札しました", "espNFTBidBidderWithoutPriceWithoutDApp": "{{NFTDisplayName}}に入札しました", "espNFTBidListerWithPrice": "{{dAppName}}で{{NFTDisplayName}}に{{priceAmount}}{{priceTokenSymbol}}の新しい入札がありました", "espNFTBidListerWithPriceWithoutDApp": "{{NFTDisplayName}}に{{priceAmount}}{{priceTokenSymbol}}の新しい入札がありました", "espNFTBidListerWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}に新しい入札がありました", "espNFTBidListerWithoutPriceWithoutDApp": "{{NFTDisplayName}}に新しい入札がありました", "espNFTCancelBidWithPrice": "{{dAppName}}で{{NFTDisplayName}}への{{priceAmount}}{{priceTokenSymbol}}の入札をキャンセルしました", "espNFTCancelBidWithPriceWithoutDApp": "{{NFTDisplayName}}への{{priceAmount}}{{priceTokenSymbol}}の入札をキャンセルしました", "espNFTCancelBidWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}への入札をキャンセルしました", "espNFTCancelBidWithoutPriceWithoutDApp": "{{NFTDisplayName}}への入札をキャンセルしました", "espNFTUnlist": "{{dAppName}}で{{NFTDisplayName}}を上場廃止しました", "espNFTUnlistWithoutDApp": "{{NFTDisplayName}}を上場廃止させました", "espNFTBuyBuyerWithPrice": "{{dAppName}}で{{NFTDisplayName}}を{{priceAmount}}{{priceTokenSymbol}}で購入しました", "espNFTBuyBuyerWithPriceWithoutDApp": "{{NFTDisplayName}}を{{priceAmount}}{{priceTokenSymbol}}で購入しました", "espNFTBuyBuyerWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}を購入しました", "espNFTBuyBuyerWithoutPriceWithoutDApp": "{{NFTDisplayName}}を購入しました", "espNFTBuySellerWithPrice": "{{dAppName}}で{{NFTDisplayName}}を{{priceAmount}}{{priceTokenSymbol}}で販売しました", "espNFTBuySellerWithPriceWithoutDApp": "{{NFTDisplayName}}を{{priceAmount}}{{priceTokenSymbol}}で販売しました", "espNFTBuySellerWithoutPrice": "{{dAppName}}で{{NFTDisplayName}}を販売しました", "espNFTBuySellerWithoutPriceWithoutDApp": "{{NFTDisplayName}}を販売しました", "espDEXSwap": "{{dAppName}}で{{downTokensTextFragment}}を{{upTokensTextFragment}}と交換しました", "espDEXDepositLPWithPoolDisplay": "{{dAppName}}で{{downTokensTextFragment}}を{{poolDisplayName}}の流動性プールに入金しました", "espDEXDepositLPWithoutPoolDisplay": "{{dAppName}}で{{downTokensTextFragment}}を{{upTokensTextFragment}}と交換しました", "espDEXWithdrawLPWithPoolDisplay": "{{dAppName}}で{{upTokensTextFragment}}を{{poolDisplayName}}の流動性プールから出金しました", "espDEXWithdrawLPWithoutPoolDisplay": "{{dAppName}}で{{downTokensTextFragment}}を{{upTokensTextFragment}}と交換しました", "espGenericTokenSend": "{{downTokensTextFragment}}を送信しました", "espGenericTokenReceive": "{{upTokensTextFragment}}を受け取りました", "espGenericTransactionBalanceChange": "{{downTokensTextFragment}}を{{upTokensTextFragment}}と交換しました", "espUnknown": "不明", "espUnknownNFT": "不明な NFT", "espTextFragmentAnd": "そして", "externalLinkWarningTitle": "Phantomと異なるページに移動し", "externalLinkWarningDescription": "{{url}}を開くところです。アクセスする前に信頼できるソースであることを確認してください。", "shortcutsWarningDescription": "ショートカットは {{url}} により提供されています。アクセスする前に信頼できるソースであることを確認してください。", "lowTpsBanner": "Solanaでネットワーク混雑が発生しています", "lowTpsMessageTitle": "Solanaネットワークの混雑", "lowTpsMessage": "Solanaで混雑が発生しているため、トランザクションが失敗、あるいは遅延する可能性があります。トランザクションが失敗した場合は、再試行してください。", "solanaSlow": "Solana ネットワークが通常より遅延しています", "solanaNetworkTemporarilyDown": "Solana ネットワークは一時的にアクセスできません", "waitForNetworkRestart": "ネットワークが再起動するまでお待ちください。あなたの資金には影響が及びません。", "exploreCollectionsCarouselTitle": "人気のコレクティブル", "exploreDropsCarouselTitle": "最新ニュース", "exploreSortFloor": "フロア", "exploreSortListed": "上場済み", "exploreSortVolume": "ボリューム", "exploreFetchErrorSubtitle": "後で再試行してください。", "exploreFetchErrorTitle": "取得に失敗しました。", "exploreTopCollectionsTitle": "トップNFTコレクション", "exploreTopListLess": "少なく表示", "exploreTopListMore": "さらに表示", "exploreSeeMore": "さらに表示", "exploreTrendingTokens": "人気トークン", "exploreVolumeTokens": "最高ボリューム", "explorePriceChangeTokens": "最大の値上がり", "explorePriceTokens": "価格別トークン", "exploreMarketCapTokens": "トップトークン", "exploreTrendingSites": "人気サイト", "exploreTopSites": "トップサイト", "exploreTrendingCollections": "人気コレクション", "exploreTopCollections": "トップコレクション", "collectiblesSearchCollectionsSection": "コレクション", "collectiblesSearchItemsSection": "アイテム", "collectiblesSearchNrOfItems": "{{ nrOfItems }} アイテム", "collectiblesSearchPlaceholderText": "所有コレクティブルを検索", "collectionPinSuccess": "コレクションがピンされました", "collectionPinFail": "コレクションのピンに失敗しました", "collectionUnpinSuccess": "コレクションのピンが解除されました", "collectionUnpinFail": "コレクションのピン解除に失敗しました", "collectionHideSuccess": "コレクションが非表示にされました", "collectionHideFail": "コレクションの非表示化に失敗しました", "collectionUnhideSuccess": "コレクションの非表示化が解除されました", "collectionUnhideFail": "コレクションの非表示化解除に失敗しました", "collectiblesSpamSuccess": "スパムとして報告されました", "collectiblesSpamFail": "スパムとしての報告に失敗しました", "collectiblesSpamAndHiddenSuccess": "スパムとして報告され隠されました", "collectiblesNotSpamSuccess": "スパムではないと報告されました", "collectiblesNotSpamFail": "スパムではないと報告するのに失敗しました", "collectiblesNotSpamAndUnhiddenSuccess": "スパムではないとして報告され再表示されました", "tokenPageSpamWarning": "このトークンは未検証です。信頼できるトークンのみを操作してください。", "tokenSpamWarning": "このトークンは、Phantom にスパムとして分類されたため、非表示にされました。", "collectibleSpamWarning": "このコレクティブルは、Phantom にスパムとして分類されたため、非表示にされました。", "collectionSpamWarning": "このコレクティブルは、Phantom にスパムとして分類されたため、非表示にされました。", "emojiNoResults": "絵文字が見つかりませんでした", "emojiSearchResults": "検索結果", "emojiSuggested": "推奨", "emojiSmileys": "スマイリーと人々", "emojiAnimals": "動物と自然", "emojiFood": "飲食物", "emojiTravel": "旅行と場所", "emojiActivities": "活動", "emojiObjects": "オブジェ", "emojiSymbols": "シンボル", "emojiFlags": "旗", "whichExtensionToConnectWith": "どのエクステンションで接続しますか？", "configureInSettings": "[設定] → [デフォルトアプリウォレット] から設定できます。", "continueWith": "次で続行：", "useMetaMask": "MetaMask を利用", "usePhantom": "Phantom を利用", "alwaysAsk": "常に尋ねる", "dontAskMeAgain": "選択を保存", "selectWalletSettingDescriptionLine1": "アプリによっては Phantom に接続できないものもあります。", "selectWalletSettingDescriptionLinePhantom": "この問題の回避策として、MetaMask を介して Phantom を開くことができます。", "selectWalletSettingDescriptionLineAlwaysAsk": "この問題の回避策として、 MetaMask に接続するとき、代わりに Phantom を使用したいか尋ねます。", "selectWalletSettingDescriptionLineMetaMask": "MetaMask をデフォルトとして設定することで、他の dApp の Phantom への接続を無効化します。", "metaMaskOverride": "デフォルトアプリウォレット", "metaMaskOverrideSettingDescriptionLine1": "Phantom に対応していないWeb サイトへの接続を可能にします。", "refreshAndReconnectToast": "再読込・再接続して変更を適用してください", "autoConfirmUnavailable": "利用不可", "autoConfirmReasonDappNotWhitelisted": "元の契約がこのアプリの許可リストに載っていないため、利用できません。", "autoConfirmReasonSessionNotActive": "有効な自動確認セッションがないため、利用できません。以下で有効化してください。", "autoConfirmReasonRateLimited": "使用している dapp が送信するリクエストが多すぎるため、利用できません。", "autoConfirmReasonUnsupportedNetwork": "自動確認がこのネットワークでサポートされていないため、利用できません。", "autoConfirmReasonSimulationFailed": "セキュリティを保証できないため、利用できません。", "autoConfirmReasonTabNotFocused": "自動確認しようとしているドメインのタブがアクティブではないため、利用できません。", "autoConfirmReasonNotUnlocked": "ウォレットのロックが解除されていないため、利用できません。", "rpcErrorUnauthorizedWrongAccount": "アドレスからのトランザクションが選択されているアカウントのアドレスに一致していません。", "rpcErrorUnauthorizedUnknownSource": "RPC リクエストのソースを特定できませんでした。", "transactionsDisabledTitle": "トランザクションが無効化されました", "transactionsDisabledMessage": "お使いのアドレスを使用して Phantom でトランザクションを行うことはできません", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "有効", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL をクリップボードにコピーしました", "notEnoughSolScanTransactionWarning": "アカウントの SOL が不足しているため、このトランザクションは失敗する可能性があります。アカウントに SOL を追加してからもう一度お試しください。", "transactionRevertedWarning": "このトランザクションはシミュレーション中に失敗しました。送信すると資金が失われる可能性があります。", "slippageToleranceExceeded": "このトランザクションは、スリッページの許容値を超えたため、シミュレーション中に元に戻されました。", "simulationWarningKnownMalicious": "このアカウントは悪意があるかもしれません。承認することで資金の損失につながる可能性があります。", "simulationWarningPoisonedAddress": "このアドレスは、最近送金したアドレスと非常に似ています。詐欺に遭って資金を失わないよう、これが正しいアドレスであることを確認してください。", "simulationWarningInteractingWithAccountWithoutActivity": "このアカウントは、過去にアクティビティがありません。存在しないアカウントに資金を送金すると、資金が失われる可能性があります。", "quests": "クエスト", "questsClaimInProgress": "受け取り中", "questsVerifyingCompletion": "クエストが完了したことを確認中", "questsClaimError": "報酬受け取りエラー", "questsClaimErrorDescription": "報酬の受け取り中にエラーが発生しました。後でもう一度お試しください。", "questsBadgeMobileOnly": "スマホのみ", "questsBadgeExtensionOnly": "拡張機能のみ", "questsExplainerSheetButtonLabel": "分かりました", "questsNoQuestsAvailable": "利用可能なクエストがありません", "questsNoQuestsAvailableDescription": "現在、利用可能なクエストはありません。新しいクエストが追加されたらすぐにお知らせします。", "exploreLearn": "学ぶ", "exploreSites": "サイト", "exploreTokens": "トークン", "exploreQuests": "クエスト", "exploreCollections": "コレクション", "exploreFilterByall_networks": "全てのネットワーク", "exploreSortByrank": "トレンド", "exploreSortBytrending": "トレンド", "exploreSortByprice": "価格", "exploreSortByprice-change": "価格変動", "exploreSortBytop": "人気", "exploreSortByvolume": "ボリューム", "exploreSortBygainers": "値上がり銘柄", "exploreSortBylosers": "値下がり銘柄", "exploreSortBymarket-cap": "時価総額", "exploreSortBymarket_cap": "時価総額", "exploreTimeFrame1h": "1 時間", "exploreTimeFrame24h": "24 時間", "exploreTimeFrame7d": "7 日", "exploreTimeFrame30d": "30 日", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "コレクティブル", "exploreCategoryMarketplace": "マーケットプレイス", "exploreCategoryGaming": "ゲーミング", "exploreCategoryBridges": "ブリッジ", "exploreCategoryOther": "その他", "exploreCategorySocial": "SNS", "exploreCategoryCommunity": "コミュニティ", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "ステーク", "exploreCategoryArt": "アート", "exploreCategoryTools": "ツール", "exploreCategoryDeveloperTools": "デベロッパーツール", "exploreCategoryHackathon": "ハッカソン", "exploreCategoryNFTStaking": "NFT ステーク", "exploreCategoryExplorer": "エクスプローラー", "exploreCategoryInscriptions": "刻印", "exploreCategoryBridge": "ブリッジ", "exploreCategoryAirdrop": "エアドロップ", "exploreCategoryAirdropChecker": "エアドロップチェッカー", "exploreCategoryPoints": "ポイント", "exploreCategoryQuests": "クエスト", "exploreCategoryShop": "ショップ", "exploreCategoryProtocol": "プロトコル", "exploreCategoryNamingService": "ネーミングサービス", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "ポートフォリオトラッカー", "exploreCategoryFitness": "フィットネス", "exploreCategoryDePIN": "DePIN", "exploreVolume": "ボリューム", "exploreFloor": "フロア", "exploreCap": "時価総額", "exploreToken": "トークン", "explorePrice": "価格", "explore24hVolume": "24時間ボリューム", "exploreErrorButtonText": "再試行", "exploreErrorDescription": "「検索」コンテンツを読み込む際にエラーが発生しました。更新してもう一度お試しください。", "exploreErrorTitle": "「検索」コンテンツを読み込めませんでした", "exploreNetworkError": "ネットワークエラーが発生しました。後でもう一度お試しください。", "exploreTokensLegalDisclaimer": "トークンのリストは CoinGecko、<PERSON><PERSON>、Jupiter などのさまざまなサードパーティプロバイダーによって提供される市場データを使用して生成されます。パフォーマンスは過去 24 時間に基づいており、過去のパフォーマンスは将来のパフォーマンスを示すものではありません。", "swapperTokensLegalDisclaimer": "トレンドのトークンリストは CoinGecko、<PERSON><PERSON>、Jupiter などのさまざまなサードパーティプロバイダーから提供された市場データを使用して生成され、指定された期間に Swapper 経由で Phantom ユーザーによってスワップされた人気のトークンに基づいています。過去のパフォーマンスは将来のパフォーマンスを示すものではありません。", "exploreLearnErrorTitle": "「学習」コンテンツを読み込めませんでした", "exploreLearnErrorDescription": "「学習」コンテンツを読み込む際にエラーが発生しました。更新してもう一度お試しください。", "exploreShowMore": "さらに表示", "exploreShowLess": "折り畳み表示", "exploreVisitSite": "サイトを訪問", "dappBrowserSearchScreenVisitSite": "サイトを訪問", "dappBrowserSearchScreenSearchWithGoogle": "Google で検索", "dappBrowserSearchScreenSearchLinkYouCopied": "あなたがコピーしたリンク", "dappBrowserExtSearchPlaceholder": "サイトとトークンを検索", "dappBrowserSearchNoAppsTokens": "アプリまたはトークンが見つかりませんでした", "dappBrowserTabsLimitExceededScreenTitle": "古いタブを閉じますか？", "dappBrowserTabsLimitExceededScreenDescription": "{{tabsCount}} タブが開かれています。新しいタブを開くには、一部のタブを閉じる必要があります。", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "全てのタブを閉じる", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN：このドメインは存在しません", "dappBrowserTabErrorHttp": "ブロックされています。HTTPS を使用してください", "dappBrowserTabError401Unauthorized": "401 権限がありません", "dappBrowserTabError501UnhandledRequest": "501 未処理のリクエスト", "dappBrowserTabErrorTimeout": "タイムアウト：サーバーの応答に時間がかかりすぎました", "dappBrowserTabErrorInvalidResponse": "応答が正しくありません", "dappBrowserTabErrorEmptyResponse": "応答が空です", "dappBrowserTabErrorGeneric": "エラーが発生しました", "localizedErrorUnknownError": "不具合が発生しました。後で再試行してください。", "localizedErrorUnsupportedCountry": "申し訳ございません、お住まいの地域は現在サポートされていません。", "localizedErrorTokensNotLoading": "トークンを読み込む際に不具合が発生しました。もう一度お試しください。", "localizedErrorSwapperNoQuotes": "ペアがサポートされていない、流動性が低い、または金額が低いため、スワップを利用できません。トークンまたは金額を調整してみてください。", "localizedErrorSwapperRefuelNoQuotes": "時価が見つかりませんでした。別のトークンまたは量を試すか、Refuel を無効化してください。", "localizedErrorInsufficientSellAmount": "トークンの金額が低すぎます。クロスチェーンでスワップするには、トークンの金額を増やしてください。", "localizedErrorCrossChainUnavailable": "クロスチェーンスワップは現在利用できません。後でもう一度お試しください。", "localizedErrorTokenNotTradable": "選択したトークンのいずれかが取引可能ではありません。別のトークンを選択してください。", "localizedErrorCollectibleLocked": "トークンのアカウントがロックされています。", "localizedErrorCollectibleListed": "トークンのアカウントが上場済みです。", "spamActivityAction": "非表示の項目を表示する", "spamActivityTitle": "非表示の活動", "spamActivityWarning": "このトランザクションは、Phantom にスパムとして分類されたため、非表示にされました。", "appAuthenticationFailed": "認証に失敗しました", "appAuthenticationFailedDescription": "承認する際に不具合が発生しました。もう一度お試しください。", "partialErrorBalanceChainName": "{{chainName}} の残高を更新できません。あなたの資金は安全です。", "partialErrorGeneric": "ネットワークを更新できません。トークンの残高と価格の一部が最新でない可能性があります。あなたの資金は安全です。", "partialErrorTokenDetail": "トークンの残高を更新できません。あなたの資金は安全です。", "partialErrorTokenPrices": "トークンの価格を更新できません。あなたの資金は安全です。", "partialErrorTokensTrimmed": "ポートフォリオ内のすべてのトークンが表示されない問題が発生しています。あなたの資金は安全です。", "publicFungibleDetailAbout": "情報", "publicFungibleDetailYourBalance": "残高", "publicFungibleDetailInfo": "情報", "publicFungibleDetailShowMore": "さらに表示", "publicFungibleDetailShowLess": "折り畳み表示", "publicFungibleDetailPerformance": "過去 24 時間のパフォーマンス", "publicFungibleDetailSecurity": "セキュリティー", "publicFungibleDetailMarketCap": "時価総額", "publicFungibleDetailTotalSupply": "総供給量", "publicFungibleDetailCirculatingSupply": "流通中供給", "publicFungibleDetailMaxSupply": "最大供給", "publicFungibleDetailHolders": "保有者", "publicFungibleDetailVolume": "ボリューム", "publicFungibleDetailTrades": "取引数", "publicFungibleDetailTraders": "トレーダー数", "publicFungibleDetailUniqueWallets": "固有ウォレット数", "publicFungibleDetailTop10Holders": "上位10人の保有者", "publicFungibleDetailTop10HoldersTooltip": "トークンの上位 10 人の保有者が現在保有している総供給量の割合を示します。これは、価格がどれだけ簡単に操作できるかを示す指標です。", "publicFungibleDetailMintable": "ミント可能", "publicFungibleDetailMintableTooltip": "トークンがミント可能な場合、契約所有者はトークンの供給を増やすことができます。", "publicFungibleDetailMutableInfo": "可変情報", "publicFungibleDetailMutableInfoTooltip": "名前、ロゴ、Web サイトのアドレスなどのトークン情報が変更可能な場合は、契約所有者が変更できます。", "publicFungibleDetailOwnershipRenounced": "所有権を放棄しました", "publicFungibleDetailOwnershipRenouncedTooltip": "トークンの所有権が放棄されると、追加のトークンをミントするなどの機能を誰も実行できなくなります。", "publicFungibleDetailUpdateAuthority": "更新機関", "publicFungibleDetailUpdateAuthorityTooltip": "トークンが変更可能な場合に情報を変更できるウォレットのアドレスに変更の権限があります。", "publicFungibleDetailFreezeAuthority": "凍結機関", "publicFungibleDetailFreezeAuthorityTooltip": "凍結機関とは、資金の送金を阻止できるウォレットアドレスです。", "publicFungibleUnverifiedToken": "このトークンは未検証です。信頼できるトークンのみを操作してください。", "publicFungibleDetailSwap": "{{tokenSymbol}} をスワップ", "publicFungibleDetailSwapDescription": "{{tokenSymbol}} を Phantom アプリでスワップ", "publicFungibleDetailLinkCopied": "クリップボードにコピー", "publicFungibleDetailContract": "契約", "publicFungibleDetailMint": "ミント", "unifiedTokenDetailTransactionActivity": "アクティビティ", "unifiedTokenDetailSeeMoreTransactionActivity": "さらに表示", "unifiedTokenDetailTransactionActivityError": "最近のアクティビティを読み込めませんでした", "additionalNetworksTitle": "追加のネットワーク", "copyAddressRowAdditionalNetworks": "追加のネットワーク", "copyAddressRowAdditionalNetworksHeader": "以下のネットワークは Ethereum と同じアドレスを使用します。", "copyAddressRowAdditionalNetworksDescription": "Ethereum のアドレスを安全に使用して、これらのネットワーク上で資産を送受信できます。", "cpeUnknownError": "不明なエラー", "cpeUnknownInstructionError": "不明な命令エラー", "cpeAccountFrozen": "アカウントは凍結されています", "cpeAssetFrozen": "資産は凍結されています", "cpeInsufficientFunds": "資金不足", "cpeInvalidAuthority": "無効な機関", "cpeBalanceBelowRent": "家賃免除基準額を下回る残高", "cpeNotApprovedForConfidentialTransfers": "アカウントは機密送金が承認されていません", "cpeNotAcceptingDepositsOrTransfers": "アカウントは入金や振込を受け付けていません", "cpeNoMemoButRequired": "以前の指示には、受信者が送金を受け取るために必要なメモがありません", "cpeTransferDisabledForMint": "このミントでは送金が無効になっています", "cpeDepositAmountExceedsLimit": "入金額が上限額を超えています", "cpeInsufficientFundsForRent": "レンディングの資金が足りません", "reportIssueScreenTitle": "問題を報告する", "publicFungibleReportIssuePrompt": "{{tokenName}} についてどのような問題を報告しますか？", "publicFungibleReportIssueIncorrectInformation": "情報が正しくありません", "publicFungibleReportIssuePriceStale": "価格が更新されません", "publicFungibleReportIssuePriceMissing": "価格が記載されていません", "publicFungibleReportIssuePerformanceIncorrect": "過去 24 時間のパフォーマンスが正しくありません", "publicFungibleReportIssueLinkBroken": "SNS のリンクにアクセスできません", "publicFungibleDetailErrorLoading": "トークンデータがありません", "reportUserPrompt": "@{{username}} についてどのような問題を報告しますか？", "reportUserOptionAbuseAndHarrassmentTitle": "暴言と嫌がらせ", "reportUserOptionAbuseAndHarrassmentDescription": "的を絞ったハラスメント、ハラスメントの扇動、暴力的な脅迫、憎悪的な内容や言及", "reportUserOptionPrivacyAndImpersonationTitle": "プライバシーとなりすまし", "reportUserOptionPrivacyAndImpersonationDescription": "個人情報の共有または漏洩の脅迫、もしくは他人のなりすまし", "reportUserOptionSpamTitle": "迷惑行為", "reportUserOptionSpamDescription": "偽アカウント、詐欺、悪質なリンク", "reportUserSuccess": "ユーザーを報告しました。", "settingsClaimUsernameTitle": "ユーザー名を作成", "settingsClaimUsernameDescription": "ウォレットと同様に固有の ID です", "settingsClaimUsernameValueProp1": "簡素化された ID", "settingsClaimUsernameValueProp1Description": "長くて複雑なアドレスの代わりに、ユーザーフレンドリーな ID を利用できるようになりました", "settingsClaimUsernameValueProp2": "より早くて簡単", "settingsClaimUsernameValueProp2Description": "暗号通貨の送受信、ウォレットへのログイン、友達とのつながりが簡単に", "settingsClaimUsernameValueProp3": "常に同期", "settingsClaimUsernameValueProp3Description": "任意のアカウントをユーザー名に接続すると、すべてのデバイス間で同期されます", "settingsClaimUsernameHelperText": "Phantomアカウントの固有の名前", "settingsClaimUsernameValidationDefault": "このユーザー名は後で変更できません", "settingsClaimUsernameValidationAvailable": "ユーザー名は利用可能です", "settingsClaimUsernameValidationUnavailable": "ユーザー名は利用不可です", "settingsClaimUsernameValidationServerError": "ユーザー名が使用可能かどうかを確認できません。後でもう一度お試しください。", "settingsClaimUsernameValidationErrorLine1": "ユーザー名が無効です。", "settingsClaimUsernameValidationErrorLine2": "ユーザー名は {{minChar}} ～ {{maxChar}} 文字の英数字で設定してください。", "settingsClaimUsernameValidationLoading": "ユーザー名が利用可能かどうかを確認中…", "settingsClaimUsernameSaveAndContinue": "保存して続行する", "settingsClaimUsernameChooseAvatarTitle": "アバターを選択", "settingsClaimUsernameAnonymousAuthTitle": "匿名認証", "settingsClaimUsernameAnonymousAuthDescription": "署名を介して匿名で Phantom アカウントにサインインします", "settingsClaimUsernameAnonymousAuthBadge": "仕組みについて", "settingsClaimUsernameLinkWalletsTitle": "ウォレットの紐づけ", "settingsClaimUsernameLinkWalletsDescription": "他のデバイスにあなたのユーザー名で表示されるウォレットを選択します", "settingsClaimUsernameLinkWalletsBadge": "公開閲覧不可能", "settingsClaimUsernameConnectAccountsTitle": "アカウントを接続", "settingsClaimUsernameConnectAccountsHelperText": "各チェーン アドレスはユーザー名に関連付けられます。これらは後で変更できます。", "settingsClaimUsernameContinue": "続行", "settingsClaimUsernameCreateUsername": "ユーザー名を作成", "settingsClaimUsernameCreating": "ユーザー名を作成中…", "settingsClaimUsernameSuccess": "ユーザー名を作成しました！", "settingsClaimUsernameError": "ユーザー名を作成中にエラーが発生しました", "settingsClaimUsernameTryAgain": "再試行", "settingsClaimUsernameSuccessHelperText": "すべての Phantom ウォレットで新しいユーザー名を使用できるようになりました。", "settingsClaimUsernameSettingsSyncedTitle": "設定が同期されました", "settingsClaimUsernameSettingsSyncedHelperText": "長くて複雑なアドレスの代わりに、ユーザーフレンドリーな ID を利用できるようになりました", "settingsClaimUsernameSendToUsernameTitle": "ユーザー名宛てに送信", "settingsClaimUsernameSendToUsernameHelperText": "暗号通貨の送受信、ウォレットへのログイン、友達とのつながりが簡単に", "settingsClaimUsernameManageAddressesTitle": "公開アドレス", "settingsClaimUsernameManageAddressesHelperText": "あなたのユーザー名に送信されたトークンやコレクティブルは全て、これらのアドレスに送信されます", "settingsClaimUsernameManageAddressesBadge": "公開閲覧可能", "settingsClaimUsernameEditAddressesTitle": "公開アドレスの管理", "settingsClaimUsernameEditAddressesHelperText": "あなたのユーザー名に送信されたトークンやコレクティブルは全て、これらのアドレスに送信されます。チェーンごとに 1 つのアドレスを選択します。", "settingsClaimUsernameEditAddressesError": "ネットワークごとに 1 つのアドレスのみが利用可能です。", "settingsClaimUsernameEditAddressesEditAddress": "アドレスを編集", "settingsClaimUsernameNoAddressesSaved": "保存されている公開アドレスはありません", "settingsClaimUsernameSave": "保存", "settingsClaimUsernameDone": "完了", "settingsClaimUsernameWatching": "監視中", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} 個のアカウント", "settingsClaimUsernameNoOfAccountsSingular": "1 個のアカウント", "settingsClaimUsernameEmptyAccounts": "アカウントなし", "settingsClaimUsernameSettingTitle": "@ユーザー名を作成する", "settingsClaimUsernameSettingDescription": "あなたのウォレットのための固有 ID", "settingsManageUserProfileAbout": "情報", "settingsManageUserProfileAboutUsername": "ユーザー名", "settingsManageUserProfileAboutBio": "自己紹介", "settingsManageUserProfileTitle": "プロフィールの管理", "settingsManageUserProfileManage": "管理", "settingsManageUserProfileAuthFactors": "認証要素", "settingsManageUserProfileAuthFactorsDescription": "Phantom アカウントにログインできるシードフレーズまたは秘密鍵を選択します。", "settingsManageUserProfileUpdateAuthFactorsToast": "認証要素が更新されました！", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "認証要素の更新に失敗しました。", "settingsManageUserProfileBiography": "自己紹介を編集", "settingsManageUserProfileBiographyDescription": "プロフィールに簡単な自己紹介を追加", "settingsManageUserProfileUpdateBiographyToast": "自己紹介を更新しました！", "settingsManageUserProfileUpdateBiographyToastFailure": "問題が発生しました。もう一度お試しください", "settingsManageUserProfileBiographyNoUrlMessage": "自己紹介から URL を削除してください", "settingsManageUserProfileLinkedWallets": "紐づけされたウォレット", "settingsManageUserProfileLinkedWalletsDescription": "Phantom アカウントにログインするときに他のデバイスに表示されるウォレットを選択します。", "settingsManageUserProfileUpdateLinkedWalletsToast": "紐づけられているウォレットが更新されました！", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "紐づけられているウォレットの更新に失敗しました！", "settingsManageUserProfilePrivacy": "プライバシー", "settingsManageUserProfileUpdatePrivacyStateToast": "プライバシーが更新されました！", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "プライバシーの更新に失敗しました！", "settingsManageUserProfilePublicAddresses": "公開アドレス", "settingsManageUserProfileUpdatePublicAddressToast": "公開アドレスが更新されました！", "settingsManageUserProfileUpdatePublicAddressToastFailure": "公開アドレスの更新に失敗しました！", "settingsManageUserProfilePrivacyStatePublic": "公開", "settingsManageUserProfilePrivacyStatePublicDescription": "あなたのプロフィールと公開アドレスは誰でも閲覧および検索できます", "settingsManageUserProfilePrivacyStatePrivate": "プライベート", "settingsManageUserProfilePrivacyStatePrivateDescription": "あなたのプロフィールは誰でも検索できますが、他のユーザーがあなたのプロフィールや公開アドレスを閲覧するには許可をリクエストする必要があります", "settingsManageUserProfilePrivacyStateInvisible": "プロフィールを非表示にする", "settingsManageUserProfilePrivacyStateInvisibleDescription": "あなたのプロフィールと公開アドレスは完全非表示となり、閲覧不可能となります", "settingsDownloadPhantom": "Phantom をダウンロード", "settingsLogOut": "ログアウト", "seedlessAddAWalletPrimaryText": "ウォレットを追加", "seedlessAddAWalletSecondaryText": "ログインまたは既存のウォレットをインポートします", "seedlessAddSeedlessWalletPrimaryText": "シードレスウォレットの追加", "seedlessAddSeedlessWalletSecondaryText": "Apple ID、Google、またはメールアドレスを使用します。", "seedlessCreateNewWalletPrimaryText": "新規ウォレットを作成しますか？", "seedlessCreateNewWalletSecondaryText": "このメールにはウォレットがありません。作成しますか？", "seedlessCreateNewWalletButtonText": "ウォレットを作成", "seedlessCreateNewWalletNoBundlePrimaryText": "ウォレットが見つかりません", "seedlessCreateNewWalletNoBundleSecondaryText": "このメールアドレスにはウォレットがありません", "seedlessCreateNewWalletNoBundleButtonText": "戻る", "seedlessEmailOptionsPrimaryText": "メールアドレスを選択", "seedlessEmailOptionsSecondaryText": "Apple、または Google アカウントでウォレットを追加します", "seedlessEmailOptionsButtonText": "メールアドレスで続行", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Apple ID を使用してウォレットを作成します", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Google メールアドレスを使用してウォレットを作成します", "seedlessAlreadyExistsPrimaryText": "アドレスはすでに存在します", "seedlessAlreadyExistsSecondaryText": "このメールにはすでにウォレットが作成されています。ログインしますか？", "seedlessSignUpWithAppleButtonText": "Apple で登録", "seedlessContinueWithAppleButtonText": "Apple で続行", "seedlessSignUpWithGoogleButtonText": "Google で登録", "seedlessContinueWithGoogleButtonText": "Google で続行", "seedlessCreateAPinPrimaryText": "PIN の作成", "seedlessCreateAPinSecondaryText": "これは、すべてのデバイス上のウォレットを保護するために使用されます。<1>PIN を回復することはできません。</1>", "seedlessContinueText": "続行", "seedlessConfirmPinPrimaryText": "PIN の確認", "seedlessConfirmPinSecondaryText": "この PIN を忘れると、新しいデバイスでウォレットを回復できなくなります。", "seedlessConfirmPinButtonText": "PIN を作成", "seedlessConfirmPinError": "PIN が正しくありません。もう一度お試しください", "seedlessAccountsImportedPrimaryText": "アカウントがインポートされました", "seedlessAccountsImportedSecondaryText": "これらのアカウントは自動的にウォレットにインポートされます", "seedlessPreviouslyImportedTag": "以前インポートした項目", "seedlessEnterPinPrimaryText": "PIN の入力", "seedlessEnterPinInvalidPinError": "入力した PIN が間違っています。4 桁の数字のみ入力できます。", "seedlessEnterPinNumTriesLeft": "残り試行回数は {{numTries}} 回です。", "seedlessEnterPinCooldown": "{{minutesLeft}}:{{secondsLeft}} 後にもう一度お試しください", "seedlessEnterPinIncorrectLength": "PIN は 4 桁でなければなりません", "seedlessEnterPinMatch": "PIN は一致しています", "seedlessDoneText": "完了", "seedlessEnterPinToSign": "このトランザクションにサインするには PIN を入力してください", "seedlessSigning": "サイン", "seedlessCreateSeed": "シードフレーズのウォレットを作成", "seedlessImportOptions": "その他のインポートオプション", "seedlessImportPrimaryText": "インポートオプション", "seedlessImportSecondaryText": "シードフレーズ、秘密鍵、またはハードウェアウォレットを使用して既存のウォレットをインポート", "seedlessImportSeedPhrase": "シードフレーズのインポート", "seedlessImportPrivateKey": "秘密鍵のインポート", "seedlessConnectHardwareWallet": "ハードウェアウォレットの接続", "seedlessTryAgain": "再試行", "seedlessCreatingWalletPrimaryText": "ウォレットを作成中", "seedlessCreatingWalletSecondaryText": "ソーシャルウォレットを追加中です", "seedlessLoadingWalletPrimaryText": "ウォレットを読み込み中", "seedlessLoadingWalletSecondaryText": "紐づけられているウォレットをインポートおよび監視中です", "seedlessLoadingWalletErrorPrimaryText": "ウォレットの読み込みに失敗しました", "seedlessCreatingWalletErrorPrimaryText": "ウォレットの作成に失敗しました", "seedlessErrorSecondaryText": "もう一度お試しください", "seedlessAuthAlreadyExistsErrorText": "提供されたメールはすでに別の Phantom アカウントに属しています", "seedlessAuthUnknownErrorText": "不明なエラーが発生しました。しばらくしてからもう一度お試しください。", "seedlessAuthUnknownErrorTextRefresh": "不明なエラーが発生しました。しばらくしてからもう一度お試しください。ページを更新してもう一度お試しください。", "seedlessAuthErrorCloseWindow": "ウィンドウを閉じる", "seedlessWalletExistsErrorPrimaryText": "お使いのデバイスにはすでにソーシャルウォレットが存在します", "seedlessWalletExistsErrorSecondaryText": "戻るかこの画面を閉じてください", "seedlessValueProp1PrimaryText": "スムーズなセットアップ", "seedlessValueProp1SecondaryText": "Google または Apple アカウントを使用してウォレットを作成し、簡単に Web3 を探索し始めましょう", "seedlessValueProp2PrimaryText": "強化されたセキュリティ", "seedlessValueProp2SecondaryText": "あなたのウォレットは複数の要素にわたって安全に分散して保管されます", "seedlessValueProp3PrimaryText": "簡単に復元", "seedlessValueProp3SecondaryText": "Google または Apple アカウントと 4 桁の PIN でウォレットへのアクセスを復元できます", "seedlessLoggingIn": "ログイン中…", "seedlessSignUpOrLogin": "サインアップまたはログイン", "seedlessContinueByEnteringYourEmail": "メールアドレスを入力して続行", "seedless": "シードレス", "seed": "シードフレーズ", "seedlessVerifyPinPrimaryText": "PIN の確認", "seedlessVerifyPinSecondaryText": "続行するには PIN を入力してください", "seedlessVerifyPinVerifyButtonText": "確認する", "seedlessVerifyPinForgotButtonText": "PIN をお忘れの場合", "seedlessPinConfirmButtonText": "確認", "seedlessVerifyToastPrimaryText": "PIN の確認", "seedlessVerifyToastSecondaryText": "PIN を忘れないようにするために、定期的に PIN の確認をお願いすることがあります。忘れた場合、ウォレットを回復できなくなります。", "seedlessVerifyToastSuccessText": "PIN 番号が確認されました！", "seedlessForgotPinPrimaryText": "別のデバイスを使用して PIN を再設定", "seedlessForgotPinSecondaryText": "セキュリティ上の理由から、PIN を再設定できるのはログインしている他のデバイスのみです。", "seedlessForgotPinInstruction1PrimaryText": "他のデバイスを開く", "seedlessForgotPinInstruction1SecondaryText": "Phantom アカウントがメールアドレスでサインインされている別のデバイスに移動します", "seedlessForgotPinInstruction2PrimaryText": "[設定] に移動", "seedlessForgotPinInstruction2SecondaryText": "[設定] で「セキュリティとプライバシー」を選択し、「PIN の再設定」を選択します。", "seedlessForgotPinInstruction3PrimaryText": "新しい PIN の設定", "seedlessForgotPinInstruction3SecondaryText": "新しい PIN を設定すると、このデバイスでウォレットにログインできるようになります。", "seedlessForgotPinButtonText": "これらの手順を実行しました", "seedlessResetPinPrimaryText": "PIN のリセット", "seedlessResetPinSecondaryText": "覚えやすい PIN を入力してください。PIN を使用して、すべてのデバイスでウォレットを保護します", "seedlessResetPinSuccessText": "PIN 番号が更新されました！", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "ウォレットを作成することで、<1>利用規約</1>および<5>プライバシーポリシー</5>に同意したことになります。", "pageNotFound": "ページが見つかりません", "pageNotFoundDescription": "このページは存在しないか、移動されています。", "webTokenPagesLegalDisclaimer": "価格情報は情報提供のみを目的としており、財務アドバイスではありません。市場データは第三者によって提供されており、Phantom は情報の正確性について一切保証しません。", "signUpOrLogin": "サインアップまたはログイン", "portalOnboardingAgreeToTermsOfServiceInterpolated": "アカウントを作成すると、<1>利用規約</1>および<5>プライバシーポリシー</5>に同意したことになります。", "feedNoActivity": "活動はまだありません", "followRequests": "フォローリクエスト", "following": "フォロー中", "followers": "フォロワー", "follower": "フォロワー", "joined": "参加", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "フォロワーなし", "noFollowing": "フォロー中なし", "noUsersFound": "ユーザーが見つかりません", "viewProfile": "プロフィールを表示", "followRequestAccepted": "フォローリクエストを承認しました", "followRequestDenied": "フォローリクエストを拒否しました"}