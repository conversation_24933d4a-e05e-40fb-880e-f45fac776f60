import{a as le}from"./chunk-QEXGR5WT.js";import{j as w,k as M,l as ae}from"./chunk-2NGYUYTC.js";import{o as se}from"./chunk-WIQ4WVKX.js";import{O as ie,c as ne,d as E,e as k,f as D,g as S,h as N,r as oe}from"./chunk-F3RUX6TF.js";import{a as Q}from"./chunk-V5T43K7V.js";import{Aa as ee,E as H,J as V,L as X,T as q,m as O,n as _,na as j,oa as x,p as $,q as G,s as K,sa as z,ta as Z,ua as J,va as Y}from"./chunk-OUYKWOVO.js";import{d as te,h as re}from"./chunk-OYGO47TI.js";import{O as I,P as T,Pa as h,e as Ie,v as he,wc as W}from"./chunk-MZZEJ42N.js";import{p as v}from"./chunk-E3NPIRHS.js";import{O as R,Z as U,_ as Ae}from"./chunk-ALUTR72U.js";import{a as xe}from"./chunk-7X4NV6OJ.js";import{f as C,h as n,l as Se,m as Buffer,n as o}from"./chunk-3KENBVE7.js";n();o();var l=C(xe());function Pe(e,t,r){if(e)return e.startsWith("data://")?e:typeof t=="string"||typeof r=="string"?w(e):t&&r?ae(e,t,r):t?M(e,"width",t):r?M(e,"height",r):w(e)}var Ke=({src:e,fallback:t,loader:r,onLoad:i,onError:a,loaderEnabled:m,...s})=>{let[u,b]=(0,l.useState)("init"),[p,d]=(0,l.useState)(!1),f=(0,l.useMemo)(()=>Pe(e,s.width,s.height),[s.height,s.width,e]),g=(0,l.useMemo)(()=>p?e:f,[e,f,p]),y=(0,l.useCallback)(P=>{p?(b("error"),a?.(P)):(b("retrying"),d(!0))},[p,b,a,d]);return u==="error"||!e?l.default.createElement(l.default.Fragment,null,t??null):l.default.createElement(l.default.Fragment,null,l.default.createElement("img",{src:g,onLoad:i,onError:y,...s}),u==="loading"&&m&&l.default.createElement(l.default.Fragment,null,r||l.default.createElement(Ee,null)))},Ee=se(le)`
  width: 100%;
  height: 100%;
`;n();o();n();o();n();o();var A=C(Ie(),1);var me=C(he(),1);var ce=(0,A.struct)([(0,A.u8)("instruction"),z("amount")]);function pe(e,t,r,i,a=[],m=x){let s=J([{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:t,isSigner:!1,isWritable:!0}],r,a),u=Buffer.alloc(ce.span);return ce.encode({instruction:Z.Burn,amount:BigInt(i)},u),new me.TransactionInstruction({keys:s,programId:m,data:u})}var ke=({t:e,apiErrors:t,clientErrors:r})=>{let i={title:e("collectiblesSellStatusErrorTitle"),subtitle:e("collectiblesSellStatusErrorSubtitle")};return t.isSellErrorCode?t?.sellStepsError?.code===1?{title:e("collectiblesSellRecentlyTransferedNFTTitle"),subtitle:e("collectiblesSellRecentlyTransferedNFTSubtitle")}:t?.sellStepsError?.code===3?{title:e("collectiblesSellNotAvailableAnymoreTitle"),subtitle:e("collectiblesSellNotAvailableAnymoreSubtitle")}:t?.sellStepsError?.code===9?{title:e("collectiblesSellNotAvailableAnymoreTitle"),subtitle:e("collectiblesSellNotAvailableAnymoreSubtitle")}:i:r.hasEnoughGas?r.executeSellError?r.executeSellError.message===ie?{title:e("collectiblesSellNotAvailableAnymoreTitle"),subtitle:e("collectiblesSellNotAvailableAnymoreSubtitle")}:i:null:{title:e("collectiblesSellInsufficientFundsTitle"),subtitle:e("collectiblesSellInsufficientFundsSubtitle")}};n();o();var De=e=>N(e)?e.standard==="ERC1155":S(e)?e.standard==="SemiFungible":!1;n();o();var Ne=e=>e.replace(/(\/|-|_|#)+?\d+(?!-|_|\/)+/g,"").replace(/\d+( of )\d+/g,"").trim(),we=(e,t)=>{if(e.name&&e.name.length>0)return e.name;if(e.externalUrl){let r=R({url:e.externalUrl,format:"domain"});if(r)return r}if(t?.name){let r=Ne(t.name);if(r)return r}if(t?.symbol)return t.symbol};n();o();var B=C(Se());n();o();async function ue(e){let{mint:t}=e,r=new I.Transaction,i=await oe({networkID:"solana:101",mintAddress:t});return r.add(await $({...e,...i})),r}var Me=async({mintAddress:e,owner:t,amountToBurn:r,collectionPubKey:i,tokenEdition:a,compression:m,programId:s,isProgrammable:u,connection:b})=>{let p=new I.Transaction,d=new T.PublicKey(e),f=await Y(d,t);if(m?.compressed)return ue({compression:m,mint:e});if(s===_)return j({mint:e,connection:b,owner:t.toBase58()});if(u)return K({senderAddress:t,senderAccount:f,mintAddress:new T.PublicKey(e)});if(a===null||!ne(a)){let g=pe(f,d,t,r),y=Q(f,t,t);p.add(g,y)}else{let g=B.Buffer.from(d.toBytes()),y=E(g),P=k(g),F;if(i!==null){let Te=new T.PublicKey(i),Ce=B.Buffer.from(Te.toBytes());F=D(Ce)}let ge={metadata:y,owner:t,mint:d,tokenAccount:f,masterEditionAccount:P,splTokenProgram:x,collectionMetadata:F},{createBurnNftInstruction:be}=await G(),ye=be(ge,O);p.add(ye)}return p},Be=async({mintAddress:e,owner:t,amountToBurn:r,collectionPubKey:i,tokenEdition:a,chainId:m,compression:s,programId:u,isProgrammable:b})=>{let p=X(m),d=await Me({mintAddress:e,owner:t,amountToBurn:r,collectionPubKey:i,tokenEdition:a,compression:s,programId:u,isProgrammable:b,connection:p});d.recentBlockhash=(await p.getLatestBlockhash("confirmed")).blockhash,d.feePayer=t;let f=await q(d,{connection:p}),g=await ee(m,[f.compileMessage()]),y=H(g);return{burnTxWithPriority:f,networkFeeUiAmount:y}};n();o();function de({chainID:e,amount:t,collectible:r,symbol:i}){let a=re(e);switch(a.chainName){case"solana":return{...a,asset:{amount:t.toString(),mint:"mint"in r?r.mint:"UNKNOWN",symbol:i,type:"collectible"}};case"ethereum":case"polygon":case"base":case"monad":return{...a,asset:{amount:t.toString(),address:"contract"in r?r.contract:"UNKNOWN",symbol:i,type:"collectible"}};case"bitcoin":return{...a,asset:{id:"firstCreatedInscriptionId"in r?r.firstCreatedInscriptionId:"UNKNOWN",symbol:i,type:"collectible"}}}}n();o();var Le="list",fe=e=>new te(e,Le),Fe=U(W,e=>fe(e));n();o();n();o();n();o();var ve=(e,t="standard",r,i,a,m,s)=>{if(h.isEVMNetworkID(e)){let u=V.get(e).transactionSpeedSeconds(t);return{ownerAddress:r.address,networkID:e,data:{hash:"",nonce:"",unsignedTransaction:v.parse({from:r.address}),estimatedTimeToMineInSeconds:u},type:"send",display:{summary:{topLeft:{text:`${s("transactionsPendingSending")} ${m} ${i}`},bottomLeft:{text:`${s("transactionsToParagraphInterpolated",{to:a})}`}},detail:{header:`${m} ${i}`,uiRecipient:a}}}}else{if(h.isSolanaNetworkID(e))return{ownerAddress:r.address,networkID:e,data:{signature:""},type:"send",display:{summary:{topLeft:{text:`${s("transactionsPendingSending")} ${m} ${i}`},bottomLeft:{text:`${s("transactionsToParagraphInterpolated",{to:a})}`}}}};if(h.isBitcoinNetworkID(e))return{ownerAddress:r.address,networkID:e,data:{txID:"",psbtHex:""},type:"send",display:{summary:{topLeft:{text:`${s("transactionsPendingSending")} ${m} ${i}`},bottomLeft:{text:`${s("transactionsToParagraphInterpolated",{to:a})}`}}}};throw new Error(`Unable to create pending transaction input for network id ${e}}`)}};n();o();var Re=(e,t)=>{if(S(e.chainData))return{standard:e.chainData.standard,pubkey:e.chainData.tokenAccount,mintPubKey:e.chainData.mint,ownerPubKey:t,decimals:parseInt(e.decimals??"0",10),amount:e.balance??"0",delegate:null,delegatedAmount:"",logoURI:null,name:e.name??null,symbol:e.symbol??null,state:"initialized",collectionPubKey:e.collection.id,uses:null,creators:[],uri:e.uri??"",collectionName:e.collection.name??void 0,collectibleImage:e.media?.previews?.medium??void 0}};n();o();var L=C(Ae()),Ue=1e6,We=1e4,Oe=e=>e>Ue?">1M":e<We?(0,L.default)(e).format("0,0.[000]"):(0,L.default)(e).format("0.[0]a").toUpperCase();n();o();export{Ke as a,ke as b,De as c,ve as d,we as e,Re as f,Oe as g,Be as h,de as i,Fe as j};
//# sourceMappingURL=chunk-AHRYSG4W.js.map
