import{c as C,q as V}from"./chunk-JD6NH5K6.js";import{a as R}from"./chunk-YF76YZSL.js";import{m as J}from"./chunk-SMVAXKUF.js";import{a as S}from"./chunk-MHOQBMVI.js";import{Qa as X}from"./chunk-L3A2KHJO.js";import{d as W,h as l,n as c}from"./chunk-3KENBVE7.js";l();c();l();c();l();c();var T={};W(T,{AuthTokenGenerator:()=>D,Client:()=>M,Configuration:()=>d,DeleteError:()=>EA,RecoverError:()=>h,RecoverErrorReason:()=>sA,RegisterError:()=>$A,__wbg_JuiceboxGetAuthToken_06fb1d3c42d3a182:()=>iA,__wbg_arrayBuffer_a9d862b05aaee2f9:()=>TA,__wbg_blob_c6537f3e31e66dad:()=>CA,__wbg_buffer_a448f833075b71ba:()=>Mj,__wbg_call_5da1969d7cd31ccd:()=>cj,__wbg_call_90c26b09837aba1c:()=>sj,__wbg_crypto_58f13aa23ffcb166:()=>VA,__wbg_done_5fe336b092d60cf2:()=>kj,__wbg_entries_9e2e2aa45aa5094a:()=>dj,__wbg_error_f851667af71bcfc6:()=>uA,__wbg_fetch_0c82eef617317d0a:()=>oA,__wbg_from_71add2e723d1f1b2:()=>Gj,__wbg_getRandomValues_504510b5564925af:()=>ZA,__wbg_get_7b48513de5dc5ea4:()=>$j,__wbg_get_f01601b5a68d10e3:()=>Aj,__wbg_getwithrefkey_4a92a5eca60879b9:()=>wA,__wbg_globalThis_9caa27ff917c6860:()=>Fj,__wbg_global_35dfdd59a4da3e74:()=>oj,__wbg_headers_24def508a7518df9:()=>RA,__wbg_headers_d135d2bb8cc60413:()=>DA,__wbg_instanceof_ArrayBuffer_e7d53d51371448e2:()=>lj,__wbg_instanceof_Response_4c3b1446206114d1:()=>XA,__wbg_instanceof_Uint8Array_bced6f43aed8c1aa:()=>fj,__wbg_isArray_74fb723e24f76012:()=>ij,__wbg_isSafeInteger_f93fde0dca9820f8:()=>Ij,__wbg_iterator_db7ca081358d4fb2:()=>Ej,__wbg_length_1009b1af0c481d7b:()=>jj,__wbg_length_1d25fa9e4ac21ce7:()=>bj,__wbg_msCrypto_abcb1295e768d1f2:()=>zA,__wbg_new_60f57089c7563e81:()=>vj,__wbg_new_8f67e318f15d7254:()=>_j,__wbg_new_9fb8d994e1c0aaac:()=>aj,__wbg_new_abda76e883ba8a5f:()=>pA,__wbg_newnoargs_c62ea9419c21fbac:()=>Bj,__wbg_newwithbyteoffsetandlength_d0482f893617af71:()=>Kj,__wbg_newwithlength_6c2df9e2f3028c43:()=>hj,__wbg_newwithstrandinit_f581dff0d19a8b03:()=>LA,__wbg_next_6529ee0cca8d57ed:()=>tj,__wbg_next_9b877f231f476d01:()=>rj,__wbg_node_523d7bd03ef69fba:()=>UA,__wbg_now_0343d9c3e0e8eedc:()=>HA,__wbg_now_b724952e890dc703:()=>yA,__wbg_process_5b786e71d465a513:()=>JA,__wbg_queueMicrotask_4d890031a6a5a50c:()=>MA,__wbg_queueMicrotask_adae4bc085237231:()=>KA,__wbg_randomFillSync_a0d98aa11c81fe89:()=>QA,__wbg_recovererror_new:()=>qA,__wbg_require_2784e593a4674877:()=>WA,__wbg_resolve_6e1c6553a82f85b7:()=>pj,__wbg_self_f0e34d89f33b99fd:()=>qj,__wbg_setTimeout_7864ca813139bafe:()=>GA,__wbg_set_2357bf09366ee480:()=>gj,__wbg_set_27f236f6d7a28c29:()=>NA,__wbg_set_759f75cd92b612d2:()=>xj,__wbg_set_wasm:()=>N,__wbg_stack_658279fe44541cf6:()=>OA,__wbg_status_d6d47ad2837621eb:()=>SA,__wbg_stringify_e1b19966d964d242:()=>Pj,__wbg_subarray_2e940e41c0f5a1d9:()=>mj,__wbg_then_3ab08cd4fbb91ae9:()=>Oj,__wbg_then_8371cc12cfedc5a2:()=>uj,__wbg_value_0c248a78fdc8e19f:()=>ej,__wbg_versions_c2ab80650590b6a2:()=>YA,__wbg_window_d3b084224f4774d7:()=>nj,__wbindgen_as_number:()=>mA,__wbindgen_boolean_get:()=>fA,__wbindgen_cb_drop:()=>nA,__wbindgen_closure_wrapper853:()=>Dj,__wbindgen_closure_wrapper903:()=>Lj,__wbindgen_debug_string:()=>wj,__wbindgen_error_new:()=>vA,__wbindgen_in:()=>dA,__wbindgen_is_function:()=>_A,__wbindgen_is_object:()=>cA,__wbindgen_is_string:()=>lA,__wbindgen_is_undefined:()=>IA,__wbindgen_jsval_loose_eq:()=>bA,__wbindgen_memory:()=>yj,__wbindgen_number_get:()=>hA,__wbindgen_number_new:()=>PA,__wbindgen_object_clone_ref:()=>gA,__wbindgen_object_drop_ref:()=>aA,__wbindgen_string_get:()=>FA,__wbindgen_string_new:()=>xA,__wbindgen_throw:()=>Hj});l();c();var E;function N(A){E=A}var i=new Array(128).fill(void 0);i.push(void 0,null,!0,!1);function r(A){return i[A]}var p=i.length;function Q(A){A<132||(i[A]=p,p=A)}function I(A){let j=r(A);return Q(A),j}var n=0,K=null;function O(){return(K===null||K.byteLength===0)&&(K=new Uint8Array(E.memory.buffer)),K}var Z=typeof TextEncoder>"u"?(0,module.require)("util").TextEncoder:TextEncoder,b=new Z("utf-8"),AA=typeof b.encodeInto=="function"?function(A,j){return b.encodeInto(A,j)}:function(A,j){let B=b.encode(A);return j.set(B),{read:A.length,written:B.length}};function u(A,j,B){if(B===void 0){let a=b.encode(A),G=j(a.length,1)>>>0;return O().subarray(G,G+a.length).set(a),n=a.length,G}let t=A.length,e=j(t,1)>>>0,$=O(),s=0;for(;s<t;s++){let a=A.charCodeAt(s);if(a>127)break;$[e+s]=a}if(s!==t){s!==0&&(A=A.slice(s)),e=B(e,t,t=s+A.length*3,1)>>>0;let a=O().subarray(e+s,e+t),G=AA(A,a);s+=G.written}return n=s,e}function f(A){return A==null}var _=null;function F(){return(_===null||_.byteLength===0)&&(_=new Int32Array(E.memory.buffer)),_}var jA=typeof TextDecoder>"u"?(0,module.require)("util").TextDecoder:TextDecoder,Y=new jA("utf-8",{ignoreBOM:!0,fatal:!0});Y.decode();function o(A,j){return A=A>>>0,Y.decode(O().subarray(A,A+j))}function k(A){p===i.length&&i.push(i.length+1);let j=p;return p=i[j],i[j]=A,j}var g=null;function BA(){return(g===null||g.byteLength===0)&&(g=new Float64Array(E.memory.buffer)),g}function y(A){let j=typeof A;if(j=="number"||j=="boolean"||A==null)return`${A}`;if(j=="string")return`"${A}"`;if(j=="symbol"){let e=A.description;return e==null?"Symbol":`Symbol(${e})`}if(j=="function"){let e=A.name;return typeof e=="string"&&e.length>0?`Function(${e})`:"Function"}if(Array.isArray(A)){let e=A.length,$="[";e>0&&($+=y(A[0]));for(let s=1;s<e;s++)$+=", "+y(A[s]);return $+="]",$}let B=/\[object ([^\]]+)\]/.exec(toString.call(A)),t;if(B.length>1)t=B[1];else return toString.call(A);if(t=="Object")try{return"Object("+JSON.stringify(A)+")"}catch{return"Object"}return A instanceof Error?`${A.name}: ${A.message}
${A.stack}`:t}function U(A,j,B,t){let e={a:A,b:j,cnt:1,dtor:B},$=(...s)=>{e.cnt++;let a=e.a;e.a=0;try{return t(a,e.b,...s)}finally{--e.cnt===0?E.__wbindgen_export_2.get(e.dtor)(a,e.b):e.a=a}};return $.original=e,$}function rA(A,j){E.wasm_bindgen__convert__closures__invoke0_mut__haa5973ef449a5fac(A,j)}function tA(A,j,B){E._dyn_core__ops__function__FnMut__A____Output___R_as_wasm_bindgen__closure__WasmClosure___describe__invoke__h664375554e075ae3(A,j,k(B))}function kA(A,j){if(!(A instanceof j))throw new Error(`expected instance of ${j.name}`);return A.ptr}function v(A,j){let B=j(A.length*1,1)>>>0;return O().set(A,B/1),n=A.length,B}function q(A,j){try{return A.apply(this,j)}catch(B){E.__wbindgen_exn_store(k(B))}}function eA(A,j,B,t){E.wasm_bindgen__convert__closures__invoke2_mut__h35a3737c9c8313d7(A,j,k(B),k(t))}var EA=Object.freeze({InvalidAuth:0,0:"InvalidAuth",UpgradeRequired:1,1:"UpgradeRequired",Assertion:2,2:"Assertion",Transient:3,3:"Transient"}),$A=Object.freeze({InvalidAuth:0,0:"InvalidAuth",UpgradeRequired:1,1:"UpgradeRequired",Assertion:2,2:"Assertion",Transient:3,3:"Transient"}),sA=Object.freeze({InvalidPin:0,0:"InvalidPin",NotRegistered:1,1:"NotRegistered",InvalidAuth:2,2:"InvalidAuth",UpgradeRequired:3,3:"UpgradeRequired",Assertion:4,4:"Assertion",Transient:5,5:"Transient"}),D=class{__destroy_into_raw(){let j=this.__wbg_ptr;return this.__wbg_ptr=0,j}free(){let j=this.__destroy_into_raw();E.__wbg_authtokengenerator_free(j)}constructor(j){let B=E.authtokengenerator_new(k(j));return this.__wbg_ptr=B>>>0,this}vend(j,B){let t,e;try{let a=E.__wbindgen_add_to_stack_pointer(-16),G=u(j,E.__wbindgen_malloc,E.__wbindgen_realloc),x=n,w=u(B,E.__wbindgen_malloc,E.__wbindgen_realloc),H=n;E.authtokengenerator_vend(a,this.__wbg_ptr,G,x,w,H);var $=F()[a/4+0],s=F()[a/4+1];return t=$,e=s,o($,s)}finally{E.__wbindgen_add_to_stack_pointer(16),E.__wbindgen_free(t,e,1)}}static random_secret_id(){let j,B;try{let $=E.__wbindgen_add_to_stack_pointer(-16);E.authtokengenerator_random_secret_id($);var t=F()[$/4+0],e=F()[$/4+1];return j=t,B=e,o(t,e)}finally{E.__wbindgen_add_to_stack_pointer(16),E.__wbindgen_free(j,B,1)}}},M=class{__destroy_into_raw(){let j=this.__wbg_ptr;return this.__wbg_ptr=0,j}free(){let j=this.__destroy_into_raw();E.__wbg_client_free(j)}constructor(j,B){kA(j,d);var t=j.__destroy_into_raw();let e=E.client_new(t,k(B));return this.__wbg_ptr=e>>>0,this}register(j,B,t,e){let $=v(j,E.__wbindgen_malloc),s=n,a=v(B,E.__wbindgen_malloc),G=n,x=v(t,E.__wbindgen_malloc),w=n,H=E.client_register(this.__wbg_ptr,$,s,a,G,x,w,e);return I(H)}recover(j,B){let t=v(j,E.__wbindgen_malloc),e=n,$=v(B,E.__wbindgen_malloc),s=n,a=E.client_recover(this.__wbg_ptr,t,e,$,s);return I(a)}delete(){let j=E.client_delete(this.__wbg_ptr);return I(j)}},d=class{__destroy_into_raw(){let j=this.__wbg_ptr;return this.__wbg_ptr=0,j}free(){let j=this.__destroy_into_raw();E.__wbg_configuration_free(j)}constructor(j){let B=E.configuration_new(k(j));return this.__wbg_ptr=B>>>0,this}},h=class A{static __wrap(j){j=j>>>0;let B=Object.create(A.prototype);return B.__wbg_ptr=j,B}__destroy_into_raw(){let j=this.__wbg_ptr;return this.__wbg_ptr=0,j}free(){let j=this.__destroy_into_raw();E.__wbg_recovererror_free(j)}get reason(){return E.__wbg_get_recovererror_reason(this.__wbg_ptr)}set reason(j){E.__wbg_set_recovererror_reason(this.__wbg_ptr,j)}get guesses_remaining(){let j=E.__wbg_get_recovererror_guesses_remaining(this.__wbg_ptr);return j===16777215?void 0:j}set guesses_remaining(j){E.__wbg_set_recovererror_guesses_remaining(this.__wbg_ptr,f(j)?16777215:j)}};function aA(A){I(A)}function qA(A){let j=h.__wrap(A);return k(j)}function nA(A){let j=I(A).original;return j.cnt--==1?(j.a=0,!0):!1}function FA(A,j){let B=r(j),t=typeof B=="string"?B:void 0;var e=f(t)?0:u(t,E.__wbindgen_malloc,E.__wbindgen_realloc),$=n;F()[A/4+1]=$,F()[A/4+0]=e}function oA(A){let j=fetch(r(A));return k(j)}function GA(){return q(function(A,j){let B=setTimeout(r(A),j);return k(B)},arguments)}function iA(){return q(function(A){let j=JuiceboxGetAuthToken(I(A));return k(j)},arguments)}function lA(A){return typeof r(A)=="string"}function cA(A){let j=r(A);return typeof j=="object"&&j!==null}function IA(A){return r(A)===void 0}function dA(A,j){return r(A)in r(j)}function vA(A,j){let B=new Error(o(A,j));return k(B)}function pA(){let A=new Error;return k(A)}function OA(A,j){let B=r(j).stack,t=u(B,E.__wbindgen_malloc,E.__wbindgen_realloc),e=n;F()[A/4+1]=e,F()[A/4+0]=t}function uA(A,j){let B,t;try{B=A,t=j,console.error(o(A,j))}finally{E.__wbindgen_free(B,t,1)}}function MA(A){queueMicrotask(r(A))}function KA(A){let j=r(A).queueMicrotask;return k(j)}function _A(A){return typeof r(A)=="function"}function gA(A){let j=r(A);return k(j)}function bA(A,j){return r(A)==r(j)}function fA(A){let j=r(A);return typeof j=="boolean"?j?1:0:2}function hA(A,j){let B=r(j),t=typeof B=="number"?B:void 0;BA()[A/8+1]=f(t)?0:t,F()[A/4+0]=!f(t)}function mA(A){return+r(A)}function PA(A){return k(A)}function xA(A,j){let B=o(A,j);return k(B)}function wA(A,j){let B=r(A)[r(j)];return k(B)}function HA(){return Date.now()}function yA(A){return r(A).now()}function DA(A){let j=r(A).headers;return k(j)}function LA(){return q(function(A,j,B){let t=new Request(o(A,j),r(B));return k(t)},arguments)}function NA(){return q(function(A,j,B,t,e){r(A).set(o(j,B),o(t,e))},arguments)}function TA(A){let j=r(A).arrayBuffer();return k(j)}function XA(A){let j;try{j=r(A)instanceof Response}catch{j=!1}return j}function SA(A){return r(A).status}function RA(A){let j=r(A).headers;return k(j)}function CA(){return q(function(A){let j=r(A).blob();return k(j)},arguments)}function VA(A){let j=r(A).crypto;return k(j)}function JA(A){let j=r(A).process;return k(j)}function YA(A){let j=r(A).versions;return k(j)}function UA(A){let j=r(A).node;return k(j)}function zA(A){let j=r(A).msCrypto;return k(j)}function WA(){return q(function(){let A=module.require;return k(A)},arguments)}function QA(){return q(function(A,j){r(A).randomFillSync(I(j))},arguments)}function ZA(){return q(function(A,j){r(A).getRandomValues(r(j))},arguments)}function Aj(A,j){let B=r(A)[j>>>0];return k(B)}function jj(A){return r(A).length}function Bj(A,j){let B=new Function(o(A,j));return k(B)}function rj(A){let j=r(A).next;return k(j)}function tj(){return q(function(A){let j=r(A).next();return k(j)},arguments)}function kj(A){return r(A).done}function ej(A){let j=r(A).value;return k(j)}function Ej(){return k(Symbol.iterator)}function $j(){return q(function(A,j){let B=Reflect.get(r(A),r(j));return k(B)},arguments)}function sj(){return q(function(A,j){let B=r(A).call(r(j));return k(B)},arguments)}function aj(){let A=new Object;return k(A)}function qj(){return q(function(){let A=self.self;return k(A)},arguments)}function nj(){return q(function(){let A=self.window;return k(A)},arguments)}function Fj(){return q(function(){let A=globalThis.globalThis;return k(A)},arguments)}function oj(){return q(function(){let A=self.global;return k(A)},arguments)}function Gj(A){let j=Array.from(r(A));return k(j)}function ij(A){return Array.isArray(r(A))}function lj(A){let j;try{j=r(A)instanceof ArrayBuffer}catch{j=!1}return j}function cj(){return q(function(A,j,B){let t=r(A).call(r(j),r(B));return k(t)},arguments)}function Ij(A){return Number.isSafeInteger(r(A))}function dj(A){let j=Object.entries(r(A));return k(j)}function vj(A,j){try{var B={a:A,b:j},t=($,s)=>{let a=B.a;B.a=0;try{return eA(a,B.b,$,s)}finally{B.a=a}};let e=new Promise(t);return k(e)}finally{B.a=B.b=0}}function pj(A){let j=Promise.resolve(r(A));return k(j)}function Oj(A,j){let B=r(A).then(r(j));return k(B)}function uj(A,j,B){let t=r(A).then(r(j),r(B));return k(t)}function Mj(A){let j=r(A).buffer;return k(j)}function Kj(A,j,B){let t=new Uint8Array(r(A),j>>>0,B>>>0);return k(t)}function _j(A){let j=new Uint8Array(r(A));return k(j)}function gj(A,j,B){r(A).set(r(j),B>>>0)}function bj(A){return r(A).length}function fj(A){let j;try{j=r(A)instanceof Uint8Array}catch{j=!1}return j}function hj(A){let j=new Uint8Array(A>>>0);return k(j)}function mj(A,j,B){let t=r(A).subarray(j>>>0,B>>>0);return k(t)}function Pj(){return q(function(A){let j=JSON.stringify(r(A));return k(j)},arguments)}function xj(){return q(function(A,j,B){return Reflect.set(r(A),r(j),r(B))},arguments)}function wj(A,j){let B=y(r(j)),t=u(B,E.__wbindgen_malloc,E.__wbindgen_realloc),e=n;F()[A/4+1]=e,F()[A/4+0]=t}function Hj(A,j){throw new Error(o(A,j))}function yj(){let A=E.memory;return k(A)}function Dj(A,j,B){let t=U(A,j,196,rA);return k(t)}function Lj(A,j,B){let t=U(A,j,219,tA);return k(t)}var z="./juicebox-sdk_bg-LUYFYBUJ.wasm";var Tj=async()=>{let A=await WebAssembly.instantiateStreaming(fetch(z),{"./juicebox-sdk_bg.js":T});N(A.instance.exports)};Tj();globalThis.JuiceboxTokens={};var m=class{setJuiceboxTokens(j){globalThis.JuiceboxTokens=j}async backupShare(j,B,t,e,$){await this.client(j).register(t,B,e,$)}async recoverShare(j,B,t){return await this.client(j).recover(B,t)}async deleteShare(j){await this.client(j).delete()}client(j){return new M(new d(j),[])}};var P,Xj=new S,Sj=async()=>P||(P=new C(new m),P),Rj={storage:Xj,authRepository:J,juiceboxClient:Sj,mnemonicProvider:async()=>X.from((await R()).fromSentenceLength(12).getEntropy())},s6=V(Rj);export{s6 as a};
//# sourceMappingURL=chunk-5RLYH252.js.map
