import { test, expect } from '../base/TestBase';

test.describe('Filter tests', () => {
  test('Kiểm tra filter DEXes và danh sách token', async ({ dashboardPage }) => {
    try {
      console.log('=== Bắt đầu test filter DEXes ===');

      // 1. <PERSON><PERSON>y cập All tokens
      console.log('Bước 1: T<PERSON><PERSON> cập All tokens');
      await dashboardPage.navigateToTokensList();

      // 2. <PERSON>ểm tra các DEX mặc định
      console.log('Bước 2: Kiểm tra các DEX mặc định');
      await dashboardPage.verifyDefaultDexesSelection();

      // 3. Kiểm tra từng DEX
      console.log('Bước 3: Kiểm tra từng DEX');

      // Danh sách các DEX cần kiểm tra
      const dexesToTest = [
        'pump.fun',
        // 'pumpswap',
        // 'moonshot',
        // 'raydium',
        //'orca',
        // 'fluxbeam',
        // 'meteora',
        // 'launchlab'
      ];

      // <PERSON><PERSON> lượng token cần kiểm tra cho mỗi DEX
      const tokensToCheck = 20;

      // Kiểm tra từng DEX
      for (const dex of dexesToTest) {
        console.log(`\n----- Kiểm tra DEX: ${dex} -----`);

        // Clear và chọn DEX
        await dashboardPage.selectSingleDex(dex);

        // Kiểm tra danh sách token
        const isAllTokensFromDex = await dashboardPage.verifyTokenListByDex(dex, tokensToCheck);

        // Xác nhận kết quả
        expect(isAllTokensFromDex, `Tất cả token phải thuộc về DEX ${dex}`).toBeTruthy();
        console.log(`----- Kết thúc kiểm tra DEX: ${dex} -----\n`);
      }

      console.log('=== Test filter DEXes hoàn thành ===');
    } catch (error) {
      console.error('Test thất bại:', error instanceof Error ? error.message : 'Lỗi không xác định');
      throw error;
    }
  });
});