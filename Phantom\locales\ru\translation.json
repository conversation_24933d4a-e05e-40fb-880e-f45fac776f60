{"commandAdd": "Добавить", "commandAccept": "Принять", "commandApply": "Применить", "commandApprove": "Подтвердить", "commandAllow": "Разрешить", "commandBack": "Назад", "commandBuy": "Купить", "commandCancel": "Отмена", "commandClaim": "Получить", "commandClaimReward": "Получить свою награду", "commandClear": "Очистить", "commandClose": "Закрыть", "commandConfirm": "Подтвердить", "commandConnect": "Подключить", "commandContinue": "Продолжить", "commandConvert": "Конвертировать", "commandCopy": "Копировать", "commandCopyAddress": "Копировать адрес", "commandCopyTokenAddress": "Копировать адрес токена", "commandCreate": "Создать", "commandCreateTicket": "Создать запрос", "commandDeny": "Отклонить", "commandDismiss": "Закрыть", "commandDontAllow": "Не разрешать", "commandDownload": "Скачать", "commandEdit": "Изменить", "commandEditProfile": "Изменить профиль", "commandEnableNow": "Включить", "commandFilter": "Фильтровать", "commandFollow": "Подписаться", "commandHelp": "Помощь", "commandLearnMore": "Подробнее", "commandLearnMore2": "Подробнее", "commandMint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandMore": "Больше", "commandNext": "Далее", "commandNotNow": "Не сейчас", "commandOpen": "Открыть", "commandOpenSettings": "Открыть настройки", "commandPaste": "Вставить", "commandReceive": "Получить", "commandReconnect": "Подключить повторно", "commandRecordVideo": "Записать видео", "commandRequest": "Запрос", "commandRetry": "Повторить", "commandReview": "Проверить", "commandRevoke": "Отменить", "commandSave": "Сохранить", "commandScanQRCode": "Сканировать QR-код", "commandSelect": "Выбрать", "commandSelectMedia": "Выбрать медиа", "commandSell": "Продать", "commandSend": "Отправить", "commandShare": "Поделиться", "commandShowBalance": "Показать баланс", "commandSign": "Подписать", "commandSignOut": "Sign Out", "commandStake": "Застейкать", "commandMintLST": "Ми<PERSON><PERSON><PERSON><PERSON>г JitoSOL", "commandSwap": "Своп", "commandSwapAgain": "Повторить своп", "commandTakePhoto": "Сделать фото", "commandTryAgain": "Повторить попытку", "commandViewTransaction": "Показать транзакцию", "commandReportAsNotSpam": "Не спам", "commandReportAsSpam": "Пометить как спам", "commandPin": "Закрепить", "commandBlock": "Заблокировать", "commandUnblock": "Разблокировать", "commandUnstake": "Снять со стейкинга", "commandUnpin": "Открепить", "commandHide": "Скрыть", "commandUnhide": "Показать", "commandBurn": "Сжечь", "commandReport": "Пожаловаться", "commandView": "Просмотреть", "commandProceedAnywayUnsafe": "Продолжить в любом случае (небезопасно)", "commandUnfollow": "Отписаться", "commandUnwrap": "Развернуть", "commandConfirmUnsafe": "Подтвердить (небезопасно)", "commandYesConfirmUnsafe": "Да, подтвердить (небезопасно)", "commandConfirmAnyway": "Всё равно подтвердить", "commandReportIssue": "Сообщить о проблеме", "commandSearch": "Поиск", "commandShowMore": "Показать больше", "commandShowLess": "Показать меньше", "pastParticipleClaimed": "Получено", "pastParticipleCompleted": "Выполнено", "pastParticipleCopied": "Скопировано", "pastParticipleDone": "Готово", "pastParticipleDisabled": "Отключено", "pastParticipleRequested": "Запрос отправлен", "nounName": "Название", "nounNetwork": "Сеть", "nounNetworkFee": "Комиссия сети", "nounSymbol": "Символ", "nounType": "Тип", "nounDescription": "Описание", "nounYes": "Да", "nounNo": "Нет", "amount": "Сумма", "limit": "<PERSON>и<PERSON><PERSON><PERSON>", "new": "Новое", "gotIt": "Понятно", "internal": "Служебное", "reward": "Вознаграждение", "seeAll": "Показать все", "seeLess": "Показать меньше", "viewAll": "Просмотреть все", "homeTab": "Главная", "collectiblesTab": "Предметы коллекционирования", "swapTab": "Своп", "activityTab": "Действие", "exploreTab": "Обозреватель", "accountHeaderConnectedInterpolated": "Вы подключены к {{origin}}", "accountHeaderConnectedToSite": "Вы подключены к этому сайту", "accountHeaderCopyToClipboard": "Копирование в буфер обмена", "accountHeaderNotConnected": "Вы не подключены к", "accountHeaderNotConnectedInterpolated": "Вы не подключены к {{origin}}", "accountHeaderNotConnectedToSite": "Вы не подключены к этому сайту", "accountWithoutEnoughSolActionButtonCancel": "Отмена", "accountWithoutEnoughSolPrimaryText": "Недостаточно SOL", "accountWithoutEnoughSolSecondaryText": "На счету аккаунта (вашего либо второй стороны), участвующего в этой транзакции, недостаточно SOL. Продолжение транзакции приведет к ее отмене.", "accountSwitcher": "Переключатель аккаунтов", "addAccountHardwareWalletPrimaryText": "Подключить аппаратный кошелек", "addAccountHardwareWalletSecondaryText": "Использовать свой аппаратный кошелек Ledger", "addAccountHardwareWalletSecondaryTextMobile": "Используйте свой кошелек {{supportedHardwareWallets}}", "addAccountSeedVaultWalletPrimaryText": "Подключение хранилища сид-фразы", "addAccountSeedVaultWalletSecondaryText": "Используйте кошелек из Seed Vault", "addAccountImportSeedPhrasePrimaryText": "Импортировать секретную фразу восстановления", "addAccountImportSeedPhraseSecondaryText": "Импортировать аккаунты из другого кошелька", "addAccountImportWalletPrimaryText": "Импортировать приватный ключ", "addAccountImportWalletSecondaryText": "Импортировать аккаунт для одной цепи", "addAccountImportWalletSolanaSecondaryText": "Импортировать приватный ключ Solana", "addAccountLimitReachedText": "Вы достигли лимита аккаунтов ({{accountsCount}}) в Phantom. Прежде чем добавлять новые аккаунты, удалите неиспользуемые.", "addAccountNoSeedAvailableText": "У вас нет доступной сид-фразы. Импортируйте существующую сид-фразу для создания аккаунта.", "addAccountNewWalletPrimaryText": "Создать новый аккаунт", "addAccountNewWalletSecondaryText": "Создайте новый адрес кошелька", "addAccountNewMultiChainWalletSecondaryText": "Добавить новый мультиблокчейновый аккаунт", "addAccountNewSingleChainWalletSecondaryText": "Добавьте новый аккаунт", "addAccountPrimaryText": "Добавить (подключить) кошелек", "addAccountSecretPhraseLabel": "Секретная фраза", "addAccountSeedLabel": "Сид-фраза", "addAccountSeedIDLabel": "Идентификатор сид-фразы", "addAccountSecretPhraseDefaultLabel": "Секретная фраза {{number}}", "addAccountPrivateKeyDefaultLabel": "Прива<PERSON><PERSON><PERSON> ключ {{number}}", "addAccountZeroAccountsForSeed": "0 акка<PERSON>нтов", "addAccountShowAccountForSeed": "Показать 1 аккаунт", "addAccountShowAccountsForSeed": "Показать аккаунты ({{numOfAccounts}})", "addAccountHideAccountForSeed": "Скрыть 1 аккаунт", "addAccountHideAccountsForSeed": "Скрыть аккаунты ({{numOfAccounts}})", "addAccountSelectSeedDescription": "Новый аккаунт будет создан на основе этой секретной фразы", "addAccountNumAccountsForSeed": "Аккаунты ({{numOfAccounts}})", "addAccountOneAccountsForSeed": "1 аккаунт", "addAccountGenerateAccountFromSeed": "Создать аккаунт", "addAccountReadOnly": "Адрес для просмотра", "addAccountReadOnlySecondaryText": "Отслеживайте любой публичный адрес кошелька", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Адрес EVM", "addAccountBitcoinAddress": "Адрес Bitcoin", "addAccountCreateSeedTitle": "Создайте новый аккаунт", "addAccountCreateSeedExplainer": "У вашего кошелька еще нет секретной фразы! Чтобы создать новый кошелек, мы сгенерируем вам фразу восстановления. Запишите ее и храните в надежном месте.", "addAccountSecretPhraseHeader": "Ваша секретная фраза", "addAccountNoSecretPhrases": "Секретные фразы отсутствуют", "addAccountImportAccountActionButtonImport": "Импортировать", "addAccountImportAccountDuplicatePrivateKey": "Этот аккаунт уже есть в вашем кошельке", "addAccountImportAccountIncorrectFormat": "Неверный формат", "addAccountImportAccountInvalidPrivateKey": "Недопустимый приватный ключ", "addAccountImportAccountName": "Название", "addAccountImportAccountPrimaryText": "Импортировать приватный ключ", "addAccountImportAccountPrivateKey": "Приват<PERSON><PERSON>й ключ", "addAccountImportAccountPublicKey": "Адрес или домен", "addAccountImportAccountPrivateKeyRequired": "Необходимо указать приватный ключ", "addAccountImportAccountNameRequired": "Необходимо указать название", "addAccountImportAccountPublicKeyRequired": "Необходимо указать публичный адрес", "addAccountImportAccountDuplicateAddress": "Этот адрес уже есть в вашем кошельке", "addAddressAddressAlreadyAdded": "Адрес уже добавлен", "addAddressAddressAlreadyExists": "Адрес уже существует", "addAddressAddressInvalid": "Адрес недействителен", "addAddressAddressIsRequired": "Необходимо указать адрес", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "Необходимо указать метку", "addAddressLabelPlaceholder": "Метка", "addAddressPrimaryText": "Добавить адрес", "addAddressToast": "Адрес добавлен", "createAssociatedTokenAccountCostLabelInterpolated": "Это будет стоить {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "У вас уже есть этот аккаунт токена", "createAssociatedTokenAccountErrorInsufficientFunds": "Недостаточно средств", "createAssociatedTokenAccountErrorInvalidMint": "Неверный адрес Mint", "createAssociatedTokenAccountErrorInvalidName": "Недопустимое имя", "createAssociatedTokenAccountErrorInvalidSymbol": "Недопустимый символ", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Мы не смогли создать аккаунт токена. Попробуйте еще раз.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Не удалось создать аккаунт", "createAssociatedTokenAccountErrorUnableToSendMessage": "Нам не удалось отправить вашу транзакцию.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Не удалось отправить транзакцию", "createAssociatedTokenAccountInputPlaceholderMint": "Адрес Mint", "createAssociatedTokenAccountInputPlaceholderName": "Название", "createAssociatedTokenAccountInputPlaceholderSymbol": "Символ", "createAssociatedTokenAccountLoadingMessage": "Мы создаем ваш аккаунт токена.", "createAssociatedTokenAccountLoadingTitle": "Создание аккаунта токена", "createAssociatedTokenAccountPageHeader": "Создать аккаунт токена", "createAssociatedTokenAccountSuccessMessage": "Аккаунт токена успешно создан!", "createAssociatedTokenAccountSuccessTitle": "Созданный аккаунт токена", "createAssociatedTokenAccountViewTransaction": "Показать транзакцию", "assetDetailRecentActivity": "Недавние действия", "assetDetailStakeSOL": "Застейкать SOL", "assetDetailUnknownToken": "Неизвестный токен", "assetDetailUnwrapAll": "Развернуть всё", "assetDetailUnwrappingSOL": "Разворачивание SOL", "assetDetailUnwrappingSOLFailed": "Сбой разворачивания SOL", "assetDetailViewOnExplorer": "Показать в {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Обозреватель", "assetDetailSaveToPhotos": "Сохранить в галерее", "assetDetailSaveToPhotosToast": "Сохранено в галерее", "assetDetailPinCollection": "Закрепить коллекцию", "assetDetailUnpinCollection": "Открепить коллекцию", "assetDetailHideCollection": "Скрыть коллекцию", "assetDetailUnhideCollection": "Отменить скрытие коллекции", "assetDetailTokenNameLabel": "Имя токена", "assetDetailNetworkLabel": "Сеть", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "Цена", "collectibleDetailSetAsAvatar": "Установить как аватар", "collectibleDetailSetAsAvatarSingleWorkAlt": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailSetAsAvatarSuccess": "Ава<PERSON><PERSON><PERSON> установлен", "collectibleDetailShare": "Поделиться предметом коллекционирования", "assetDetailTokenAddressCopied": "Адрес скопирован", "assetDetailStakingLabel": "Стейкинг", "assetDetailAboutLabel": "О {{fungibleName}}", "assetDetailPriceDetail": "Детализация цены", "assetDetailHighlights": "Основные моменты", "assetDetailAllTimeReturn": "Доходность за весь период", "assetDetailAverageCost": "Средняя стоимость", "assetDetailPriceHistoryUnavailable": "История цен недоступна для этого токена", "assetDetailPriceHistoryInsufficientData": "История цен недоступна для этого временного диапазона", "assetDetailPriceDataUnavailable": "Данные о ценах недоступны", "assetDetailPriceHistoryError": "Ошибка при получении истории цен", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1 сут.", "assetDetailTimeFrame24h": "Цена за 24 ч", "assetDetailTimeFrame1W": "1 нед.", "assetDetailTimeFrame1M": "1 мес.", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "ВСЕ", "sendAssetAmountLabelInterpolated": "Доступно {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Предложения цены", "fiatRampNewQuote": "Новое предложение цены", "assetListSelectToken": "Выбрать токен", "assetListSearch": "Поиск…", "assetListUnknownToken": "Неизвестный токен", "buyFlowHealthWarning": "Из-за высокой загруженности у некоторых наших платежных поставщиков время обработки депозитов может увеличиться на несколько часов.", "assetVisibilityUnknownToken": "Неизвестный токен", "buyAssetInterpolated": "Купить {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "Максимальная покупка составляет {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Минимальная покупка составляет {{amount}}", "buyNoAssetsAvailable": "Активы Ethereum и Polygon отсутствуют", "buyThirdPartyScreenPaymentMethodSelector": "Оплатить через", "buyThirdPartyScreenPaymentMethod": "Выберите способ оплаты", "buyThirdPartyScreenChoseQuote": "Введите действительную сумму для предложения цены", "buyThirdPartyScreenProviders": "Поставщики", "buyThirdPartyScreenPaymentMethodTitle": "Способы оплаты", "buyThirdPartyScreenPaymentMethodEmptyState": "Нет доступных способов оплаты в вашем регионе", "buyThirdPartyScreenPaymentMethodFooter": "Платежи проводятся с помощью сетевых партнеров. Комиссия может варьироваться. Отдельные способы оплаты недоступны в вашем регионе.", "buyThirdPartyScreenProvidersEmptyState": "Нет доступных поставщиков в вашем регионе", "buyThirdPartyScreenLoadingQuote": "Загрузка предложения цены...", "buyThirdPartyScreenViewQuote": "Показать предложение цены", "gasEstimationErrorWarning": "Возникла проблема с оценкой комиссии за эту транзакцию. Она может завершиться ошибкой.", "gasEstimationCouldNotFetch": "Не удалось определить стоимость газа", "networkFeeCouldNotFetch": "Не удалось определить комиссию сети", "nativeTokenBalanceErrorWarning": "Возникла проблема с получением баланса токенов для этой транзакции. Она может завершиться ошибкой.", "blocklistOriginCommunityDatabaseInterpolated": "Этот сайт занесен в <1>поддерживаемую сообществом базу</1> фишинговых и мошеннических сайтов. Если считаете, что сайт помечен по ошибке, <3>сообщите о проблеме</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} заблокирован!", "blocklistOriginIgnoreWarning": "Игнорировать предупреждение и перейти на {{domainName}}", "blocklistOriginSiteIsMalicious": "Phantom считает этот сайт вредоносным и небезопасным.", "blocklistOriginThisDomain": "этот домен", "blocklistProceedAnyway": "Игнорировать предупреждение, всё равно продолжить", "maliciousTransactionWarning": "Phantom считает эту транзакцию вредоносной и небезопасной. Мы отключили возможность ее подписания, чтобы защитить вас и ваши средства.", "maliciousTransactionWarningIgnoreWarning": "Игнорировать предупреждение, всё равно продолжить", "maliciousTransactionWarningTitle": "Транзакция помечена!", "maliciousRequestBlockedTitle": "Запрос заблокирован", "maliciousRequestWarning": "Этот сайт был отмечен как вредоносный. Возможно, он пытается украсть ваши средства или обманом заставить вас одобрить ложный запрос.", "maliciousSignatureRequestBlocked": "Ради вашей безопасности Phantom заблокировал этот запрос.", "maliciousRequestBlocked": "Ради вашей безопасности Phantom заблокировал этот запрос.", "maliciousRequestFrictionDescription": "Продолжать небезопасно, поэтому Phantom заблокировал этот запрос. Закройте это диалоговое окно.", "maliciousRequestAcknowledge": "Я понимаю, что могу потерять все свои средства, воспользовавшись этим сайтом.", "maliciousRequestAreYouSure": "Вы уверены?", "siwErrorPopupTitle": "Некорректный запрос подписи", "siwParseErrorDescription": "Запрос приложения на подпись нельзя показать из-за некорректного форматирования.", "siwVerificationErrorDescription": "В запросе на подпись сообщения возникла 1 или более ошибок. В целях безопасности убедитесь, что вы используете правильное приложение, и повторите попытку.", "siwErrorPagination": "{{n}} из {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Предупреждение: адрес приложения не совпадает с адресом, указанным для подписи.", "siwErrorMessage_DOMAIN_MISMATCH": "Предупреждение: домен приложения не соответствует предоставленному для проверки домену.", "siwErrorMessage_URI_MISMATCH": "Предупреждение: имя узла URI не соответствует домену.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Предупреждение: идентификатор цепочки не совпадает с предоставленным для проверки идентификатором цепочки.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Предупреждение: дата размещения сообщения относится к слишком далекому прошлому.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Предупреждение: дата размещения сообщения относится к слишком далекому будущему.", "siwErrorMessage_EXPIRED": "Предупреждение: срок действия сообщения истек.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Предупреждение: срок действия сообщения истекает до его размещения.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Предупреждение: срок действия сообщения истечет до того, как оно станет действительным.", "siwErrorShowErrorDetails": "Показать сведения об ошибке", "siwErrorHideErrorDetails": "Скрыть сведения об ошибке", "siwErrorIgnoreWarning": "Игнорировать предупреждение, всё равно продолжить", "siwsTitle": "Запрос на вход", "siwsPermissions": "Разрешения", "siwsAgreement": "Сообщение", "siwsAdvancedDetails": "Расширенная информация", "siwsAlternateStatement": "{{domain}} хочет, чтобы вы вошли в свой аккаунт Solana:\n{{address}}", "siwsFieldLable_domain": "До<PERSON><PERSON>н", "siwsFieldLable_address": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Версия", "siwsFieldLable_chainId": "ID блокчейна", "siwsFieldLable_nonce": "Одноразовое случайное число", "siwsFieldLable_issuedAt": "Выпущено", "siwsFieldLable_expirationTime": "Истекает", "siwsFieldLable_requestId": "Идентификатор запроса", "siwsFieldLable_resources": "Ресурсы", "siwsVerificationErrorDescription": "Этот запрос на вход в систему недействителен — сайт небезопасен либо его разработчик допустил ошибку при отправке запроса.", "siwsErrorNumIssues": "Число ошибок: {{n}}", "siwsErrorMessage_CHAIN_ID_MISMATCH": "ID блокчейна не соответствует сети, в которой вы находитесь.", "siwsErrorMessage_DOMAIN_MISMATCH": "Этот домен не тот, в который вы входите.", "siwsErrorMessage_URI_MISMATCH": "Этот URI не тот, в который вы входите.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Дата выпуска сообщения относится к слишком далекому прошлому.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Дата выпуска сообщения относится к слишком далекому будущему.", "siwsErrorMessage_EXPIRED": "Срок действия сообщения истек.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Срок действия сообщения истекает до его размещения.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Срок действия сообщения истечет до того, как оно станет действительным.", "changeLockTimerPrimaryText": "Таймер автоблокировки", "changeLockTimerSecondaryText": "Время бездействия, после которого кошелек будет заблокирован.", "changeLockTimerToast": "Таймер автоблокировки обновлен", "changePasswordConfirmNewPassword": "Подтвердите новый пароль", "changePasswordCurrentPassword": "Текущий пароль", "changePasswordErrorIncorrectCurrentPassword": "Неверный текущий пароль", "changePasswordErrorGeneric": "Что-то пошло не так. Повторите попытку позже", "changePasswordNewPassword": "Новый пароль", "changePasswordPrimaryText": "Изменить пароль", "changePasswordToast": "Пароль обновлен", "collectionsSpamCollections": "Коллекции спама", "collectionsHiddenCollections": "Скрытые коллекции", "collectiblesReportAsSpam": "Пометить как спам", "collectiblesReportAsSpamAndHide": "Это спам, скрыть", "collectiblesReportAsNotSpam": "Не спам", "collectiblesReportAsNotSpamAndUnhide": "Отобразить и пометить как не спам", "collectiblesReportNotSpam": "Не спам", "collectionsManageCollectibles": "Управление предметами коллекционирования", "collectibleDetailDescription": "Описание", "collectibleDetailProperties": "Характеристики", "collectibleDetailOrdinalInfo": "Данные ординала", "collectibleDetailRareSatsInfo": "Информация о редких сатоши", "collectibleDetailSatsInUtxo": "Sats в UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailSatName": "Имя Sat", "collectibleDetailInscriptionId": "ID надписи", "collectibleDetailInscriptionNumber": "Номер надписи", "collectibleDetailStandard": "Стандартный", "collectibleDetailCreated": "Создано", "collectibleDetailViewOnExplorer": "Показать в {{explorer}}", "collectibleDetailList": "Список", "collectibleDetailSellNow": "Продать за {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Высвободите запас Bitcoin", "collectibleDetailUtxoSplitterCtaSubtitle": "У вас есть {{value}} BTC для разблокировки", "collectibleDetailUtxoSplitterModalCtaTitle": "Редкие сатоши", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Чтобы защитить ваши средства, мы предотвращаем отправку BTC в UTXO с редкими сатоши. Используйте сплиттер UTXO Magic Eden, чтобы высвободить {{value}} BTC из ваших редких сатоши.", "collectibleDetailUtxoSplitterModalCtaButton": "Использовать сплиттер UTXO", "collectibleDetailEasilyAccept": "Принимайте самое высокое предложение", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "Этот предмет коллекционирования был скрыт — Phantom определил его как спам.", "collectibleDetailSpamOverlayReveal": "Показать предмет коллекционирования", "collectibleBurnTermsOfService": "Я понимаю, что это действие невозможно отменить", "collectibleBurnTitleWithCount_one": "Сжечь токен", "collectibleBurnTitleWithCount_other": "Сжечь токены", "collectibleBurnDescriptionWithCount_one": "Данное действие приведет к необратимому уничтожению этого токена из вашего кошелька.", "collectibleBurnDescriptionWithCount_other": "Данное действие приведет к необратимому уничтожению этих токенов из вашего кошелька.", "collectibleBurnTokenWithCount_one": "Токен", "collectibleBurnTokenWithCount_other": "Токены", "collectibleBurnCta": "Сжечь", "collectibleBurnRebate": "Частичный возврат", "collectibleBurnRebateTooltip": "Небольшая сумма SOL будет автоматически зачислена на ваш кошелек для сжигания этого токена.", "collectibleBurnNetworkFee": "Комиссия сети", "collectibleBurnNetworkFeeTooltip": "Сумма, необходимая сети Solana для обработки транзакции", "unwrapButtonSwapTo": "Своп на {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Вывести с {{withdrawalSource}} на {{chainSymbol}}", "unwrapModalEstimatedTime": "Предполагаемое время", "unwrapModalNetwork": "Сеть", "unwrapModalNetworkFee": "Комиссия сети", "unwrapModalTitle": "Сводка", "unsupportedChain": "Неподдерживаемый блокчейн", "unsupportedChainDescription": "Похоже, мы не поддерживаем действие ({{action}}) для сети {{chainName}}.", "networkFeesTooltipLabel": "Комиссия сети {{chainName}}", "networkFeesTooltipDescription": "Комиссия {{chainName}} зависит от нескольких факторов. Через них вы можете повлиять на скорость транзакции — ускорить (дороже) или замедлить (дешевле).", "burnStatusErrorTitleWithCount_one": "Не удалось сжечь токен", "burnStatusErrorTitleWithCount_other": "Не удалось сжечь токены", "burnStatusSuccessTitleWithCount_one": "Токен сожжен!", "burnStatusSuccessTitleWithCount_other": "Токены сожжены!", "burnStatusLoadingTitleWithCount_one": "Сжигание токена…", "burnStatusLoadingTitleWithCount_other": "Сжигание токенов…", "burnStatusErrorMessageWithCount_one": "Не удалось сжечь этот токен. Повторите попытку позже.", "burnStatusErrorMessageWithCount_other": "Не удалось сжечь эти токены. Повторите попытку позже.", "burnStatusSuccessMessageWithCount_one": "Этот токен был безвозвратно уничтожен, в результате на ваш кошелек зачислено {{rebateAmount}} SOL.", "burnStatusSuccessMessageWithCount_other": "Эти токены были безвозвратно уничтожены, в результате на ваш кошелек зачислено {{rebateAmount}} SOL.", "burnStatusLoadingMessageWithCount_one": "Этот токен будет окончательно уничтожен, в результате на ваш кошелек зачислится {{rebateAmount}} SOL.", "burnStatusLoadingMessageWithCount_other": "Эти токены будут окончательно уничтожены, в результате на ваш кошелек зачислится {{rebateAmount}} SOL.", "burnStatusViewTransactionText": "Показать транзакцию", "collectibleDisplayLoading": "Загрузка…", "collectiblesNoCollectibles": "Нет предметов коллекционирования", "collectiblesPrimaryText": "Ваши предметы коллекционирования", "collectiblesReceiveCollectible": "Получить предметы коллекционирования", "collectiblesUnknownCollection": "Неизвестная коллекция", "collectiblesUnknownCollectible": "Неизвестный предмет коллекционирования", "collectiblesUniqueHolders": "Уникальные держатели", "collectiblesSupply": "Предложение", "collectiblesUnknownTokens": "Неизвестные токены", "collectiblesNrOfListed": "Выставлено на продажу: {{ nrOfListed }}", "collectiblesListed": "Выставлено на продажу", "collectiblesMintCollectible": "Минтинг предмета коллекционирования", "collectiblesYouMint": "Ваш минтинг", "collectiblesMintCost": "Стоимость минтинга", "collectiblesMintFail": "Не удалось выполнить минтинг", "collectiblesMintFailMessage": "При минтинге вашего предмета коллекционирования произошла ошибка. Попробуйте еще раз.", "collectiblesMintCostFree": "Бесплатно", "collectiblesMinting": "Минтинг...", "collectiblesMintingMessage": "Выполняется минтинг вашего предмета коллекционирования", "collectiblesMintShareSubject": "Взгляните!", "collectiblesMintShareMessage": "Я выполнил(-а) минтинг на @phantom!", "collectiblesMintSuccess": "Успешный минтинг", "collectiblesMintSuccessMessage": "Минтинг вашего предмета коллекционирования завершен", "collectiblesMintSuccessQuestMessage": "Вы выполнили требования квеста Phantom. Нажмите «Получить свою награду», чтобы получить бесплатный предмет коллекционирования.", "collectiblesMintRequired": "Требуется", "collectiblesMintMaxLengthErrorMessage": "Превышена максимальная длина", "collectiblesMintSafelyDismiss": "Теперь это окно можно закрыть.", "collectiblesTrimmed": "Сейчас мы не можем отобразить больше предметов коллекционирования, так как достигли лимита.", "collectiblesNonTransferable": "Нельзя передать", "collectiblesNonTransferableYes": "Да", "collectiblesSellOfferDetails": "Детали предложения", "collectiblesSellYouSell": "Вы продаете", "collectiblesSellGotIt": "Понятно", "collectiblesSellYouReceive": "Вы получите", "collectiblesSellOffer": "Сделать предложение", "collectiblesSoldCollectible": "Продан предмет коллекционирования", "collectiblesSellMarketplace": "Торговая платформа", "collectiblesSellCollectionFloor": "Минимальная цена коллекции", "collectiblesSellDifferenceFromFloor": "Разница с минимальной ценой", "collectiblesSellLastSalePrice": "Последняя цена продажи", "collectiblesSellEstimatedFees": "Ориентировочные комиссии", "collectiblesSellEstimatedProfitAndLoss": "Предполагаемые цифры прибыли/убытка", "collectiblesSellViewOnMarketplace": "Показать в {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "Самая низкая цена «Купить сейчас» в коллекциях на различных торговых платформах.", "collectiblesSellProfitLossTooltip": "Предполагаемые цифры прибыли/убытка рассчитывается на основании цены последней продажи и суммы предложения за вычетом комиссионных.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Авторские гонорары ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Комиссия торговой площадки ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Комиссия торговой платформы", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Сеть {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "В предложение цены входит комиссия Phantom в размере {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "В предложение цены входят авторские гонорары, комиссия сети, комиссия торговой платформы и комиссия Phantom в размере {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "В предложение цены входят авторские гонорары, комиссия сети и комиссия торговой платформы", "collectiblesSellTransactionFeeTooltipTitle": "Комиссия за транзакцию", "collectiblesSellStatusLoadingTitle": "Принятие предложения...", "collectiblesSellStatusLoadingIsSellingFor": "продается за", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} продано!", "collectiblesSellStatusSuccessWasSold": "продано за", "collectiblesSellStatusErrorTitle": "Произошла ошибка", "collectiblesSellStatusErrorSubtitle": "При попытке продажи произошла ошибка", "collectiblesSellStatusViewTransaction": "Показать транзакцию", "collectiblesSellInsufficientFundsTitle": "Недостаточно средств", "collectiblesSellInsufficientFundsSubtitle": "Мы не смогли принять предложение по этому предмету коллекционирования, так как не хватило средств для оплаты комиссии сети.", "collectiblesSellRecentlyTransferedNFTTitle": "Недавно переведено", "collectiblesSellRecentlyTransferedNFTSubtitle": "Вы должны подождать 1 час, чтобы принимать предложения после перевода.", "collectiblesApproveCollection": "Подтверждено {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "Предложение недоступно", "collectiblesSellNotAvailableAnymoreSubtitle": "Предложение больше не доступно. Отмените это предложение цены и попробуйте снова", "collectiblesSellFlaggedTokenTitle": "Предмет коллекционирования помечен", "collectiblesSellFlaggedTokenSubtitle": "Предмет коллекционирования не подлежит обмену, причины могут быть разные: например, он объявлен как украденный или он застейкан без периода блокировки", "collectiblesListOnMagicEden": "Выставлено на продажу в Magic Eden", "collectiblesListPrice": "Цена в объявлении", "collectiblesUseFloor": "Применять мин. цену", "collectiblesFloorPrice": "Минимальная цена", "collectiblesLastSalePrice": "Последняя цена продажи", "collectiblesTotalReturn": "Суммарная доходность", "collectiblesOriginalPurchasePrice": "Исходная цена покупки", "collectiblesMagicEdenFee": "Комиссия Magic Eden", "collectiblesArtistRoyalties": "Авторские гонорары", "collectiblesListNowButton": "Разместить сейчас", "collectiblesListAnywayButton": "Разместить в любом случае", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "Показать объявление", "collectiblesListingViewTransaction": "Показать транзакцию", "collectiblesRemoveListing": "Удалить объявление", "collectiblesEditListing": "Изменить объявление", "collectiblesEditListPrice": "Изменить цену в объявлении", "collectiblesListPriceTooltip": "Цена в объявлении — это цена продажи предмета. Обычно продавцы устанавливают ее на уровне или выше минимальной цены.", "collectiblesFloorPriceTooltip": "Минимальная цена — это самая низкая действующая объявленная цена за предмет из этой коллекции.", "collectiblesOriginalPurchasePriceTooltip": "Вы изначально приобрели данный предмет за эту сумму.", "collectiblesPurchasedForSol": "Приобретено за {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Не удалось загрузить объявления", "collectiblesUnableToLoadListingsFrom": "Не удалось загрузить объявления с {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "Ваши объявления и активы в безопасности, но мы не смогли загрузить их из {{marketplace}} в данный момент. Попробуйте еще раз позже.", "collectiblesBelowFloorPrice": "Цена ниже минимальной", "collectiblesBelowFloorPriceMessage": "Вы точно хотите разместить NFT по цене ниже минимальной?", "collectiblesMinimumListingPrice": "Минимальная цена — 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden взимает комиссию с завершенных транзакций.", "collectiblesArtistRoyaltiesTooltip": "Создатель этой коллекции получает авторский гонорар в размере % от каждой завершенной сделки.", "collectibleScreenCollectionLabel": "Коллекция", "collectibleScreenPhotosPermissionTitle": "Разрешение на доступ к фото", "collectibleScreenPhotosPermissionMessage": "Нам нужно ваше разрешение на доступ к фото. Перейдите в настройки и обновите разрешения.", "collectibleScreenPhotosPermissionOpenSettings": "Открыть настройки", "listStatusErrorTitle": "Ошибка размещения объявления", "editListStatusErrorTitle": "Не удалось обновить", "removeListStatusErrorTitle": "Ошибка удаления объявления", "listStatusSuccessTitle": "Объявление создано!", "editListingStatusSuccessTitle": "Объявление обновлено!", "removeListStatusSuccessTitle": "Объявление удалено из Magic Eden", "listStatusLoadingTitle": "Создание объявления…", "editListingStatusLoadingTitle": "Обновление объявления…", "removeListStatusLoadingTitle": "Удаление объявление…", "listStatusErrorMessage": "{{name}} нельзя выставить на продажу на Magic Eden", "removeListStatusErrorMessage": "{{name}} нельзя снять с продажи на Magic Eden", "listStatusSuccessMessage": "{{name}} теперь выставлено на продажу в Magic Eden за {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "Цена {{name}} в Magic Eden теперь изменена и составляет {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} удалено из Magic Eden", "listStatusLoadingMessage": "Выставление на продажу {{name}} в Magic Eden за {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Обновление цены {{name}} в Magic Eden: {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Удаление {{name}} из Magic Eden. Это может занять некоторое время.", "listStatusLoadingSafelyDismiss": "Теперь это окно можно закрыть.", "listStatusViewOnMagicEden": "Показать в Magic Eden", "listStatusViewOnMarketplace": "Показать в {{marketplace}}", "listStatusLoadingDismiss": "Закрыть", "listStatusViewTransaction": "Показать транзакцию", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Подключите аппаратный кошелек и разблокируйте его. Как только мы его обнаружим, вы сможете выбрать, какой адрес использовать.", "connectHardwareFailedPrimaryText": "Подключиться не удалось", "connectHardwareFailedSecondaryText": "Подключите аппаратный кошелек и разблокируйте его. Как только мы его обнаружим, вы сможете выбрать, какой адрес использовать.", "connectHardwareFinishPrimaryText": "Аккаунт добавлен!", "connectHardwareFinishSecondaryText": "Кошелек Ledger Nano готов к использованию из Phantom. Вернитесь в расширение.", "connectHardwareNeedsPermissionPrimaryText": "Подключение нового кошелька", "connectHardwareNeedsPermissionSecondaryText": "Чтобы начать подключение, нажмите кнопку ниже.", "connectHardwareSearchingPrimaryText": "Поиск кошелька…", "connectHardwareSearchingSecondaryText": "Подключите аппаратный кошелек и разблокируйте его. Также проверьте, подтверждены ли разрешения в браузере.", "connectHardwarePermissionDeniedPrimary": "Отказ в разрешении", "connectHardwarePermissionDeniedSecondary": "Предоставьте Phantom разрешение на подключение к вашему устройству Ledger", "connectHardwarePermissionUnableToConnect": "Не удалось подключиться", "connectHardwarePermissionUnableToConnectDescription": "Мы не смогли подключиться к вашему устройству Ledger. Возможно, нужны дополнительные разрешения.", "connectHardwareSelectAddressAllAddressesImported": "Все адреса импортированы", "connectHardwareSelectAddressDerivationPath": "Путь вывода", "connectHardwareSelectAddressSearching": "Поиск...", "connectHardwareSelectAddressSelectWalletAddress": "Выберите адрес кошелька", "connectHardwareSelectAddressWalletAddress": "Адрес кошелька", "connectHardwareWaitingForApplicationSecondaryText": "Подключите аппаратный кошелек и разблокируйте его.", "connectHardwareWaitingForPermissionPrimaryText": "Нужно разрешение", "connectHardwareWaitingForPermissionSecondaryText": "Подключите аппаратный кошелек и разблокируйте его. Также проверьте, подтверждены ли разрешения в браузере.", "connectHardwareAddAccountButton": "Добавить аккаунт", "connectHardwareLedger": "Подключи<PERSON>е Ledger", "connectHardwareStartConnection": "Нажмите кнопку ниже, чтобы начать подключение аппаратного кошелька Ledger", "connectHardwarePairSuccessPrimary": "Устройство {{productName}} подключено", "connectHardwarePairSuccessSecondary": "Вы успешно подключили устройство {{productName}}.", "connectHardwareSelectChains": "Выберите цепочки для подключения", "connectHardwareSearching": "Поиск...", "connectHardwareMakeSureConnected": "Подключите аппаратный кошелек и разблокируйте его. Подтвердите соответствующие разрешения браузера.", "connectHardwareOpenAppDescription": "Разблокируйте аппаратный кошелек", "connectHardwareConnecting": "Подключение...", "connectHardwareConnectingDescription": "Подключаемся к вашему устройству Ledger.", "connectHardwareConnectingAccounts": "Подключение аккаунтов...", "connectHardwareDiscoveringAccounts": "Поиск аккаунтов...", "connectHardwareDiscoveringAccountsDescription": "Мы ищем активность по вашим аккаунтам.", "connectHardwareErrorLedgerLocked": "Устройство Ledger заблокировано", "connectHardwareErrorLedgerLockedDescription": "Разблокируйте устройство Ledger, затем повторите попытку.", "connectHardwareErrorLedgerGeneric": "Произошла ошибка", "connectHardwareErrorLedgerGenericDescription": "Не удалось найти аккаунты. Разблокируйте устройство Ledger, затем повторите попытку.", "connectHardwareErrorLedgerPhantomLocked": "Повторно откройте Phantom и попробуйте подключить устройство ещё раз.", "connectHardwareFindingAccountsWithActivity": "Поиск аккаунтов {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "Поиск аккаунтов {{chainName1}} или {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Мы нашли активные аккаунты в Ledger — всего {{numOfAccounts}}.", "connectHardwareFoundAccountsWithActivitySingular": "Мы нашли 1 активный аккаунт в Ledger.", "connectHardwareFoundSomeAccounts": "На вашем устройстве Ledger найдены аккаунты.", "connectHardwareViewAccounts": "Показать аккаунты", "connectHardwareConnectAccounts": "Аккаунты подключены", "connectHardwareSelectAccounts": "Выберите аккаунты", "connectHardwareChooseAccountsToConnect": "Выберите аккаунты кошельков для подключения.", "connectHardwareAccountsAddedInterpolated": "Добавлено аккаунтов: {{numOfAccounts}}", "connectHardwareAccountsStepOfSteps": "Шаг {{stepNum}} из {{totalSteps}}", "connectHardwareMobile": "Подключить Ledger", "connectHardwareMobileTitle": "Подключите аппаратный кошелек Ledger", "connectHardwareMobileEnableBluetooth": "Включить Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Разрешите использовать Bluetooth для подключения", "connectHardwareMobileEnableBluetoothSettings": "Перейдите в «Настройки», чтобы предоставить Phantom разрешения «Местоположение» и «Устройства поблизости».", "connectHardwareMobilePairWithDevice": "Выполните сопряжение с устройством Ledger", "connectHardwareMobilePairWithDeviceDescription": "Держите устройство поблизости, чтобы получить наилучший сигнал", "connectHardwareMobileConnectAccounts": "Подключить аккаунты", "connectHardwareMobileConnectAccountsDescription": "Мы будем искать активность во всех аккаунтах, которые вы могли использовать", "connectHardwareMobileConnectLedgerDevice": "Подключите устройство Ledger", "connectHardwareMobileLookingForDevices": "Ищем устройства поблизости...", "connectHardwareMobileLookingForDevicesDescription": "Подключите устройство Ledger и разблокируйте его.", "connectHardwareMobileFoundDeviceSingular": "Найдено 1 устройство Ledger", "connectHardwareMobileFoundDevices": "Найдено устройств Ledger: {{numDevicesFound}}", "connectHardwareMobileFoundDevicesDescription": "Выберите устройство Ledger ниже, чтобы начать сопряжение.", "connectHardwareMobilePairingWith": "Сопряжение с устройством {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Во время сопряжения следуйте инструкциям на устройстве Ledger.", "connectHardwareMobilePairingFailed": "Не удалось выполнить сопряжение", "connectHardwareMobilePairingFailedDescription": "Не удалось выполнить сопряжение с {{deviceName}}. Разблокируйте устройство.", "connectHardwareMobilePairingSuccessful": "Сопряжение выполнено", "connectHardwareMobilePairingSuccessfulDescription": "Вы успешно выполнили сопряжение и подключение устройства Ledger.", "connectHardwareMobileOpenAppSingleChain": "Откройте приложение {{chainName}} в Ledger", "connectHardwareMobileOpenAppDualChain": "Откройте приложение {{chainName1}} или {{chainName2}} в Ledger", "connectHardwareMobileOpenAppDescription": "Разблокируйте устройство.", "connectHardwareMobileStillCantFindDevice": "Всё еще не можете найти свое устройство?", "connectHardwareMobileLostConnection": "Подключение утеряно", "connectHardwareMobileLostConnectionDescription": "Мы потеряли соединение с устройством {{deviceName}}. Разблокируйте устройство, затем повторите попытку.", "connectHardwareMobileGenericLedgerDevice": "Устройство Ledger", "connectHardwareMobileConnectDeviceSigning": "Подключите устройство {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Разблокируйте устройство Ledger и держите его поблизости.", "connectHardwareMobileBluetoothDisabled": "Bluetooth отключен", "connectHardwareMobileBluetoothDisabledDescription": "Включите Bluetooth и разблокируйте устройство Ledger.", "connectHardwareMobileLearnMore": "Подробнее", "connectHardwareMobileBlindSigningDisabled": "Слепая подпись отключена", "connectHardwareMobileBlindSigningDisabledDescription": "Включите слепую подпись на своем устройстве.", "connectHardwareMobileConfirmSingleChain": "Транзакцию необходимо подтвердить на аппаратном кошельке. Разблокируйте его.", "metamaskExplainerBottomSheetHeader": "Этот сайт работает с Phantom", "metamaskExplainerBottomSheetSubheader": "Чтобы продолжить, выберите MetaMask в диалоговом окне подключения кошелька.", "metamaskExplainerBottomSheetDontShowAgain": "Больше не показывать", "ledgerStatusNotConnected": "Устройство Ledger не подключено", "ledgerStatusConnectedInterpolated": "Устройство {{productName}} подключено", "connectionClusterInterpolated": "Сейчас вы здесь: {{cluster}}", "connectionClusterTestnetMode": "Сейчас вы в режиме Testnet", "featureNotSupportedOnLocalNet": "Эта функция не поддерживается, если включена Solana Localnet.", "readOnlyAccountBannerWarning": "Вы наблюдаете за этим аккаунтом", "depositAddress": "Адрес получения", "depositAddressChainInterpolated": "Ваш адрес в {{chain}}", "depositAssetDepositInterpolated": "Получить {{tokenSymbol}}", "depositAssetSecondaryText": "Этот адрес можно использовать только для получения совместимых токенов.", "depositAssetTextInterpolated": "Используйте этот адрес для получения токенов и предметов коллекционирования в <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Перевести с биржи", "depositAssetShareAddress": "Поделиться адресом", "depositAssetBuyOrDeposit": "Купить или передать", "depositAssetBuyOrDepositDesc": "Чтобы начать, пополните кошелек", "depositAssetTransfer": "Перевести", "editAddressAddressAlreadyAdded": "Адрес уже добавлен", "editAddressAddressAlreadyExists": "Адрес уже существует", "editAddressAddressIsRequired": "Необходимо указать адрес", "editAddressPrimaryText": "Изменить адрес", "editAddressRemove": "Удалить из адресной книги", "editAddressToast": "Адрес обновлен", "removeSavedAddressToast": "Адрес удален", "exportSecretErrorGeneric": "Что-то пошло не так. Повторите попытку позже", "exportSecretErrorIncorrectPassword": "Неверный пароль", "exportSecretPassword": "Пароль", "exportSecretPrivateKey": "приватн<PERSON>й ключ", "exportSecretSecretPhrase": "секретную фразу", "exportSecretPIN": "PIN-код", "exportSecretSecretRecoveryPhrase": "секретную фразу восстановления", "exportSecretSelectYourAccount": "Выберите аккаунт", "exportSecretShowPrivateKey": "Показать приватный ключ", "exportSecretShowSecretRecoveryPhrase": "Показать секретную фразу восстановления", "exportSecretShowSecret": "Показать {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "{{secretNameText}} — это ваша тайна, <1>не делитесь</1> ею ни с кем!", "exportSecretWarningSecondaryInterpolated": "{{secretNameText}} дает полный контроль над вашим кошельком — эти данные не должны попасть в чужие руки.", "exportSecretOnlyWay": "{{secretNameText}} — единственный способ восстановить ваш кошелек", "exportSecretDoNotShow": "{{secretNameText}} — это ваша тайна, не позволяйте никому ее узнать", "exportSecretWillNotShare": "{{secretNameText}} — моя тайна, и я не буду делиться ей ни с кем, включая Phantom.", "exportSecretNeverShare": "{{secretNameText}} — ваша тайна, не делитесь ею ни с кем", "exportSecretYourPrivateKey": "Ваш приватный ключ", "exportSecretYourSecretRecoveryPhrase": "Ваша секретная фраза восстановления", "exportSecretResetPin": "Сбросьте PIN-код", "fullPageHeaderBeta": "Бета!", "fullPageHeaderHelp": "Помощь", "gasUpTo": "До {{ amount }}", "timeDescription1hour": "Около 1 часа", "timeDescription30minutes": "Около 30 минут", "timeDescription10minutes": "Около 10 минут", "timeDescription2minutes": "Около 2 минут", "timeDescription30seconds": "Около 30 секунд", "timeDescription15seconds": "Около 15 секунд", "timeDescription10seconds": "Около 10 секунд", "timeDescription5seconds": "Около 5 секунд", "timeDescriptionAbbrev1hour": "1 час", "timeDescriptionAbbrev30minutes": "30 мин.", "timeDescriptionAbbrev10minutes": "10 мин.", "timeDescriptionAbbrev2minutes": "2 мин.", "timeDescriptionAbbrev30seconds": "30 сек.", "timeDescriptionAbbrev15seconds": "15 сек.", "timeDescriptionAbbrev10seconds": "10 сек.", "timeDescriptionAbbrev5seconds": "5 сек.", "gasSlow": "Медленно", "gasAverage": "Средне", "gasFast": "Быстро", "satsPerVirtualByte": "{{satsPerVirtualByte}} SATS/vB", "satsAmount": "{{sats}} SATS", "homeErrorButtonText": "Повторить попытку", "homeErrorDescription": "При попытке получить активы произошла ошибка. Обновите страницу и повторите попытку", "homeErrorTitle": "Не удалось получить активы", "homeManageTokenList": "Управление списком токенов", "interstitialDismissUnderstood": "Понятно", "interstitialBaseWelcomeTitle": "Phantom теперь поддерживает Base!", "interstitialBaseWelcomeItemTitle_1": "Отправляйте, получайте и покупайте токены", "interstitialBaseWelcomeItemTitle_2": "Узнайте о возможностях экосистемы Base", "interstitialBaseWelcomeItemTitle_3": "Безопасно и надежно", "interstitialBaseWelcomeItemDescription_1": "Переводите и покупайте USDC и ETH на Base с помощью {{paymentMethod}}, карт или Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Используйте Phantom со всеми своими любимыми приложениями DeFi и NFT.", "interstitialBaseWelcomeItemDescription_3": "Обезопасьте себя благодаря поддержке Ledger, фильтрации спама и моделированию транзакций.", "privacyPolicyChangedInterpolated": "Наша политика конфиденциальности изменилась. <1> Узнать больше</1>", "bitcoinAddressTypesBodyTitle": "Ти<PERSON><PERSON> Bitcoin-адре<PERSON>ов", "bitcoinAddressTypesFeature1Title": "О Bitcoin-адресах", "bitcoinAddressTypesFeature1Subtitle": "Phantom поддерживает Native Segwit и Taproot, каждый из которых имеет свой баланс. Вы можете отправлять BTC или ординалы с любым из этих типов адресов.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "BTC-адрес по умолчанию в Phantom. Старее, чем <PERSON>, но совместим со всеми кошельками и биржами.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Лучше всего подходит для ординалов и BRC-20, имеет самые дешевые комиссии. Настройте адреса в меню «Параметры» -> «Предпочитаемый Bitcoin-адрес».", "headerTitleInfo": "Информация", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Это ваш адрес с типом <1>{{addressType}}</1>.", "invalidChecksumTitle": "Мы обновили вашу секретную фразу!", "invalidChecksumFeature1ExportPhrase": "Экспортировать новую секретную фразу", "invalidChecksumFeature1ExportPhraseDescription": "Создайте резервную копию новой секретной фразы вместе с приватными ключами старых аккаунтов.", "invalidChecksumFeature2FundsAreSafe": "Ваши средства в целости и сохранности", "invalidChecksumFeature2FundsAreSafeDescription": "Это автоматическое обновление. Никто в компании Phantom не знает вашей секретной фразы и не имеет доступа к вашим средствам.", "invalidChecksumFeature3LearnMore": "Подробнее", "invalidChecksumFeature3LearnMoreDescription": "Ваша фраза была несовместима с большинством кошельков. Прочитайте <1>эту справочную статью</1>, чтобы узнать больше.", "invalidChecksumBackUpSecretPhrase": "Сохранить секретную фразу", "migrationFailureTitle": "При переносе аккаунта произошла ошибка", "migrationFailureFeature1": "Экспортируйте секретную фразу", "migrationFailureFeature1Description": "Создайте резервную копию своей секретной фразы перед регистрацией.", "migrationFailureFeature2": "Регистрация в Phantom", "migrationFailureFeature2Description": "Вам нужно будет повторно зарегистрироваться в Phantom, чтобы просмотреть свой аккаунт.", "migrationFailureFeature3": "Подробнее", "migrationFailureFeature3Description": "Прочитайте <1>эту справочную статью</1>, чтобы узнать об этом подробнее.", "migrationFailureContinueToOnboarding": "Перейти к регистрации", "migrationFailureUnableToFetchMnemonic": "Нам не удалось загрузить вашу секретную фразу", "migrationFailureUnableToFetchMnemonicDescription": "Обратитесь в службу поддержки и загрузите журналы приложения для отладки", "migrationFailureContactSupport": "Связаться со службой поддержки", "ledgerActionConfirm": "Подтвердите на своем Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Слепая подпись недоступна", "ledgerActionErrorBlindSignDisabledSecondaryText": "Убедитесь, что на вашем аппаратном устройстве включена слепая подпись, затем повторите действие", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Аппаратное устройство отключено во время работы", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Закройте расширение Phantom, затем повторите действие", "ledgerActionErrorDeviceLockedPrimaryText": "Аппаратное устройство заблокировано", "ledgerActionErrorDeviceLockedSecondaryText": "Разблокируйте аппаратное устройство и повторите действие", "ledgerActionErrorHeader": "Ошибка действия Ledger", "ledgerActionErrorUserRejectionPrimaryText": "Пользователь отклонил транзакцию", "ledgerActionErrorUserRejectionSecondaryText": "Действие было отклонено пользователем на аппаратном устройстве", "ledgerActionNeedPermission": "Нужно разрешение", "ledgerActionNeedToConfirm": "Транзакцию нужно подтвердить на аппаратном кошельке. Разблокируйте его и перейдите в приложение {{chainType}}.", "ledgerActionNeedToConfirmMany": "Транзакции в количестве {{numberOfTransactions}} нужно подтвердить на аппаратном кошельке. Разблокируйте его и перейдите в приложение {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Транзакцию нужно подтвердить на аппаратном кошельке. Разблокируйте его и перейдите в приложение {{chainType}}. Убедитесь, что слепая подпись доступна.", "ledgerActionNeedToConfirmBlindMany": "Транзакции в количестве {{numberOfTransactions}} нужно подтвердить на аппаратном кошельке. Разблокируйте его и перейдите в приложение {{chainType}}. Убедитесь, что слепая подпись доступна.", "ledgerActionPleaseConnect": "Подключите Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "Подключите аппаратный кошелек и разблокируйте его. Также проверьте, подтверждены ли разрешения в браузере.", "maxInputAmount": "Сумма", "maxInputMax": "Максимум", "notEnoughSolPrimaryText": "Недостаточно SOL", "notEnoughSolSecondaryText": "У вас недостаточно SOL в кошельке для этой транзакции. Пополните счет и повторите попытку.", "insufficientBalancePrimaryText": "Недостаточно {{tokenSymbol}}", "insufficientBalanceSecondaryText": "У вас недостаточно токенов {{tokenSymbol}} в кошельке для этой транзакции.", "insufficientBalanceRemaining": "Осталось", "insufficientBalanceRequired": "Требуется", "notEnoughSplTokensTitle": "Недостаточно токенов", "notEnoughSplTokensDescription": "У вас недостаточно токенов в кошельке для этой транзакции. Продолжение транзакции приведет к ее отмене.", "transactionExpiredPrimaryText": "Истек срок действия транзакции", "transactionExpiredSecondaryText": "Вы слишком долго ждали, чтобы подтвердить транзакцию, срок ее действия истек. Продолжение транзакции приведет к ее отмене.", "transactionHasWarning": "Предупреждение о транзакции", "tokens": "токены", "notificationApplicationApprovalPermissionsAddressVerification": "Подтвердите, что являетесь владельцем этого адреса", "notificationApplicationApprovalPermissionsTransactionApproval": "Запрашивать подтверждение транзакций", "notificationApplicationApprovalPermissionsViewWalletActivity": "Просматривать баланс кошелька и историю операций", "notificationApplicationApprovalParagraphText": "Подтверждение даст этому сайту возможность просматривать балансы и историю операций для выбранного аккаунта.", "notificationApplicationApprovalActionButtonConnect": "Подключить", "notificationApplicationApprovalActionButtonSignIn": "Войти", "notificationApplicationApprovalAllowApproval": "Разрешить сайту подключение?", "notificationApplicationApprovalAutoConfirm": "Автоподтверждение транзакций", "notificationApplicationApprovalConnectDisclaimer": "Подключайтесь только к сайтам, которым доверяете", "notificationApplicationApprovalSignInDisclaimer": "Входите только на те сайты, которым доверяете", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Использование этого веб-сайта небезопасно и может стать причиной кражи ваших средств.", "notificationApplicationApprovalConnectUnknownApp": "Неизвестно", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Не удалось подключиться к приложению", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Это приложение пытается подключиться к {{appNetworkName}}, но выбрано {{phantomNetworkName}}.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Чтобы использовать {{networkName}}, перейдите в «Настройки разработчика» → «Режим Testnet».", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Неизвестная сеть", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Ledger пока не поддерживает подключение к другим мобильным приложениям.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Переключитесь на аккаунт, не связанный с Ledger, или воспользуйтесь встроенным в приложение браузером и повторите попытку.", "notificationSignatureRequestConfirmTransaction": "Подтверждение транзакции", "notificationSignatureRequestConfirmTransactionCapitalized": "Подтверждение транзакции", "notificationSignatureRequestConfirmTransactions": "Подтвердите транзакции", "notificationSignatureRequestConfirmTransactionsCapitalized": "Подтвердите транзакции", "notificationSignatureRequestSignatureRequest": "Запрос подписи", "notificationMessageHeader": "Сообщение", "notificationMessageCopied": "Сообщение скопировано", "notificationAutoConfirm": "Автоподтверждение", "notificationAutoConfirmOff": "Выкл.", "notificationAutoConfirmOn": "<PERSON><PERSON><PERSON>.", "notificationConfirmFooter": "Подтверждайте только в том случае, если доверяете этому сайту.", "notificationEstimatedTime": "Предполагаемое время", "notificationPermissionRequestText": "Это только запрос разрешения. Транзакция может быть выполнена не сразу.", "notificationBalanceChangesText": "Изменения баланса ориентировочные. Суммы и задействованные активы не гарантируются.", "notificationContractAddress": "Адрес контракта", "notificationAdvancedDetailsText": "Дополнительно", "notificationUnableToSimulateWarningText": "Сейчас мы не можем оценить изменения баланса. Вы можете повторить попытку позже или подтвердить, что доверяете этому сайту.", "notificationSignMessageParagraphText": "Подписание этого сообщения подтверждает право владения выбранным аккаунтом.", "notificationSignatureRequestScanFailedDescription": "Не удалось проверить безопасность сообщения. Действуйте с осторожностью.", "notificationFailedToScan": "Не удалось смоделировать результаты этого запроса.\nПодтверждение небезопасно и может привести к потерям.", "notificationScanLoading": "Запрос на сканирование", "notificationTransactionApprovalActionButtonConfirm": "Подтвердить", "notificationTransactionApprovalActionButtonBack": "Назад", "notificationTransactionApprovalEstimatedChanges": "Предполагаемые изменения", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Подсчеты основаны на моделировании транзакций и не являются точными", "notificationTransactionApprovalHideAdvancedDetails": "Скрыть дополнительные сведения о транзакции", "notificationTransactionApprovalNetworkFee": "Комиссия сети", "notificationTransactionApprovalNetwork": "Сеть", "notificationTransactionApprovalEstimatedTime": "Предполагаемое время", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Не обнаружено изменений, влияющих на владение активами", "notificationTransactionApprovalSolanaAmountRequired": "Сумма, необходимая сети Solana для обработки транзакции", "notificationTransactionApprovalUnableToSimulate": "Невозможно смоделировать результат. Убедитесь, что сайт надежный, так как одобрение может привести к потере средств.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Не удалось получить сведения об изменениях баланса", "notificationTransactionApprovalViewAdvancedDetails": "Показать дополнительные сведения о транзакции", "notificationTransactionApprovalKnownMalicious": "Эта транзакция является вредоносной. Подписание приведет к потере средств.", "notificationTransactionApprovalSuspectedMalicious": "Мы подозреваем, что эта транзакция является вредоносной. Подтверждение может привести к потере средств.", "notificationTransactionApprovalNetworkFeeHighWarning": "Плата за пользование сетью повышается из-за ее перегруженности.", "notificationTransactionERC20ApprovalDescription": "С предоставленным разрешением это приложение будет иметь доступ к вашему балансу в любое время в пределах указанного ниже лимита.", "notificationTransactionERC20ApprovalContractAddress": "Адрес контракта", "notificationTransactionERC20Unlimited": "неограниченно", "notificationTransactionERC20ApprovalTitle": "Разрешить расходование {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Отменить расходование {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Отменить доступ {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Все ваши {{tokenSymbol}}", "notificationIncorrectModeTitle": "Неправильный режим", "notificationIncorrectModeInTestnetTitle": "Вы в режиме Testnet", "notificationIncorrectModeNotInTestnetTitle": "Вы не в режиме Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} пытается использовать Mainnet, но вы находитесь в режиме Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} пытается использовать Testnet, но вы не в Testnet", "notificationIncorrectModeInTestnetProceed": "Чтобы продолжить, выключите режим Testnet.", "notificationIncorrectModeNotInTestnetProceed": "Чтобы продолжить, включите режим Testnet.", "notificationIncorrectEIP712ChainId": "Мы не дали вам подписать сообщение, не предназначенное для сети, к которой вы подключены в данный момент", "notificationIncorrectEIP712ChainIdDescription": "Сообщение запрошено {{messageChainId}}, а вы подключены к {{connectedChainId}}", "notificationUnsupportedNetwork": "Неподдерживаемая сеть", "notificationUnsupportedNetworkDescription": "Этот сайт пытается использовать сеть, которую Phantom в данный момент не поддерживает.", "notificationUnsupportedNetworkDescriptionInterpolated": "Чтобы продолжить с другим расширением, отключите опцию <1>«Настройки» → «Приложение кошелька по умолчанию» и выберите «Всегда спрашивать»</1>. Затем обновите страницу и снова подключитесь.", "notificationUnsupportedAccount": "Неподдерживаемый аккаунт", "notificationUnsupportedAccountDescription": "Этот сайт пытается использовать {{targetChainType}}, но данный аккаунт {{chainType}} это не поддерживает.", "notificationUnsupportedAccountDescription2": "Переключитесь на аккаунт с совместимой сид-фразой или приватным ключом и повторите попытку.", "notificationInvalidTransaction": "Недействительная транзакция", "notificationInvalidTransactionDescription": "Полученная от этого приложения транзакция некорректна и не подлежит отправке. Обратитесь к разработчику этого приложения, чтобы сообщить о проблеме.", "notificationCopyTransactionText": "Копировать транзакцию", "notificationTransactionCopied": "Транзакция скопирована", "onboardingImportOptionsPageTitle": "Импортируйте кошелек", "onboardingImportOptionsPageSubtitle": "Импортируйте свой кошелек с помощью секретной фразы, приватного ключа или аппаратного кошелька.", "onboardingImportPrivateKeyPageTitle": "Импортируйте приватный ключ", "onboardingImportPrivateKeyPageSubtitle": "Импортируйте свой одноблокчейновый кошелек", "onboardingCreatePassword": "Придумайте пароль", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Я принимаю <1>Условия использования</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Подтвердите пароль", "onboardingCreatePasswordDescription": "Используется для разблокировки кошелька.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Неверная секретная фраза восстановления", "onboardingCreatePasswordPasswordPlaceholder": "Пароль", "onboardingCreatePasswordPasswordStrengthWeak": "слабый", "onboardingCreatePasswordPasswordStrengthMedium": "средний", "onboardingCreatePasswordPasswordStrengthStrong": "надежный", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Я сохранил(а) секретную фразу восстановления", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Секретная фраза восстановления", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Эта фраза — ЕДИНСТВЕННЫЙ способ восстановить ваш кошелек. НЕ делитесь ею ни с кем!", "onboardingImportWallet": "Импортировать кошелек", "onboardingImportWalletImportExistingWallet": "Импортирование существующего кошелька с помощью секретной фразы восстановления из 12 или 24 слов.", "onboardingImportWalletRestoreWallet": "Восстановить кошелек", "onboardingImportWalletSecretRecoveryPhrase": "Секретная фраза восстановления", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Неверная секретная фраза восстановления", "onboardingImportWalletIHaveWords": "У меня есть фраза восстановления из {{numWords}} сл.", "onboardingImportWalletIncorrectOrMisspelledWord": "Слово «{{wordIndex}}» неправильное или написано с ошибкой", "onboardingImportWalletIncorrectOrMisspelledWords": "Слова «{{wordIndexes}}» неправильные или написаны с ошибкой", "onboardingImportWalletScrollDown": "Прокрутите вниз", "onboardingImportWalletScrollUp": "Прокрутите вверх", "onboardingSelectAccountsImportAccounts": "Импортируйте аккаунты", "onboardingSelectAccountsImportAccountsDescription": "Выберите аккаунты кошельков для импорта.", "onboardingSelectAccountsImportSelectedAccounts": "Импортировать выбранные аккаунты", "onboardingSelectAccountsFindMoreAccounts": "Найти больше аккаунтов", "onboardingSelectAccountsFindMoreNoneFound": "Аккаунты не найдены", "onboardingSelectAccountsNoOfAccountsSelected": "Выбрано аккаунтов: {{numOfAccounts}}", "onboardingSelectAccountSelectAllText": "Выбрать все", "onboardingAdditionalPermissionsTitle": "Используйте приложения с Phantom", "onboardingAdditionalPermissionsSubtitle": "Чтобы получить максимум пользы от работы с приложением, мы рекомендуем разрешить Phantom читать и изменять данные на всех сайтах.", "interstitialAdditionalPermissionsTitle": "Используйте приложения с Phantom", "interstitialAdditionalPermissionsSubtitle": "Чтобы вы могли продолжать пользоваться приложениями без перебоев, мы рекомендуем разрешить Phantom читать и изменять данные на всех сайтах.", "recentActivityPrimaryText": "Недавние действия", "removeAccountActionButtonRemove": "Удалить", "removeAccountRemoveWallet": "Удалить аккаунт", "removeAccountInterpolated": "Удалить {{accountName}}", "removeAccountWarningLedger": "Если удалить этот кошелек из Phantom, его потом можно будет вернуть с помощью процедуры «Подключить аппаратный кошелек».", "removeAccountWarningSeedVault": "Если удалить этот кошелек из Phantom, его потом можно будет вернуть с помощью процедуры «Подключить кошелек Seed Vault».", "removeAccountWarningPrivateKey": "Если вы удалите этот кошелек, Phantom не сможет его восстановить. Убедитесь, что у вас есть резервная копия приватного ключа.", "removeAccountWarningSeed": "Если удалить этот кошелек из Phantom, его потом можно будет вернуть с помощью мнемонической фразы в этом или другом кошельке.", "removeAccountWarningReadOnly": "Удаление этого аккаунта не повлияет на ваш кошелек, так как аккаунт только для просмотра.", "removeSeedPrimaryText": "Удаление секретной фразы {{number}}", "removeSeedSecondaryText": "Это приведет к удалению всех существующих аккаунтов в секретной фразе {{number}}. Обязательно сохраните резервную копию существующей секретной фразы.", "resetSeedPrimaryText": "Сбросить приложение с новой секретной фразой", "resetSeedSecondaryText": "Все существующие аккаунты будут удалены и заменены новыми. Обязательно сохраните резервную копию существующей секретной фразы и приватных ключей.", "resetAppPrimaryText": "Сбросить и стереть приложение", "resetAppSecondaryText": "Это приведет к удалению всех существующих аккаунтов и данных. Обязательно сохраните резервную копию секретной фразы и приватных ключей.", "richTransactionsDays": "дн.", "richTransactionsToday": "Сегодня", "richTransactionsYesterday": "Вчера", "richTransactionDetailAccount": "Аккаунт", "richTransactionDetailAppInteraction": "Взаимодействие приложений", "richTransactionDetailAt": "в", "richTransactionDetailBid": "Предложение", "richTransactionDetailBidDetails": "Детали предложения", "richTransactionDetailBought": "Куплено", "richTransactionDetailBurned": "Сожжено", "richTransactionDetailCancelBid": "Отменить предложение", "richTransactionDetailCompleted": "Выполнено", "richTransactionDetailConfirmed": "Подтверждено", "richTransactionDetailDate": "Дата", "richTransactionDetailFailed": "Ошибка", "richTransactionDetailFrom": "Откуда", "richTransactionDetailItem": "Предмет", "richTransactionDetailListed": "Выставлено на продажу", "richTransactionDetailListingDetails": "Детали объявления", "richTransactionDetailListingPrice": "Цена продажи", "richTransactionDetailMarketplace": "Торговая платформа", "richTransactionDetailNetworkFee": "Комиссия сети", "richTransactionDetailOriginalListingPrice": "Исходная цена продажи", "richTransactionDetailPending": "Ожидание", "richTransactionDetailPrice": "Цена", "richTransactionDetailProvider": "Поставщик", "richTransactionDetailPurchaseDetails": "Детали покупки", "richTransactionDetailRebate": "Частичный возврат", "richTransactionDetailReceived": "Получено", "richTransactionDetailSaleDetails": "Детали реализации", "richTransactionDetailSent": "Отправлено", "richTransactionDetailSold": "Продано", "richTransactionDetailStaked": "Застейкано", "richTransactionDetailStatus": "Статус", "richTransactionDetailSwap": "Своп", "richTransactionDetailSwapDetails": "Детали свопа", "richTransactionDetailTo": "Куда", "richTransactionDetailTokenSwap": "Своп токена", "richTransactionDetailUnknownNFT": "Неизвестный NFT", "richTransactionDetailUnlisted": "Снято с продажи", "richTransactionDetailUnstaked": "Снято со стейка", "richTransactionDetailValidator": "Валида<PERSON><PERSON>р", "richTransactionDetailViewOnExplorer": "Показать в {{explorer}}", "richTransactionDetailWithdrawStake": "Вывести стейк", "richTransactionDetailYouPaid": "Вы заплатили", "richTransactionDetailYouReceived": "Вы получили", "richTransactionDetailUnwrapDetails": "Детали разворачивания", "richTransactionDetailTokenUnwrap": "Разворачивание токена", "activityItemsRefreshFailed": "Не удалось загрузить новые транзакции.", "activityItemsPagingFailed": "Не удалось загрузить предыдущие транзакции.", "activityItemsTestnetNotAvailable": "История транзакций Testnet в данный момент недоступна", "historyUnknownDappName": "Неизвестно", "historyStatusSucceeded": "Успешно", "historyNetwork": "Сеть", "historyAttemptedAmount": "Сумма неудачной транзакции", "historyAmount": "Сумма", "sendAddressBookButtonLabel": "Адресная книга", "addressBookSelectAddressBook": "Адресная книга", "sendAddressBookNoAddressesSaved": "Адреса не сохранены", "sendAddressBookRecentlyUsed": "Недавно использованные", "addressBookSelectRecentlyUsed": "Недавно использованные", "sendConfirmationLabel": "Метка", "sendConfirmationMessage": "Подтверждение", "sendConfirmationNetworkFee": "Комиссия сети", "sendConfirmationPrimaryText": "Подтвердить отправку", "sendWarning_INSUFFICIENT_FUNDS": "Недостаточно средств, транзакция, вероятно, не будет выполнена.", "sendFungibleSummaryNetwork": "Сеть", "sendFungibleSummaryNetworkFee": "Комиссия сети", "sendFungibleSummaryEstimatedTime": "Предполагаемое время", "sendFungiblePendingEstimatedTime": "Предполагаемое время", "sendFungibleSummaryEstimatedTimeDescription": "Скорость транзакций Ethereum зависит от нескольких факторов. Вы можете ускорить транзакции, нажав «Комиссия сети».", "sendSummaryBitcoinPendingTxTitle": "Не удалось отправить перевод", "sendSummaryBitcoinPendingTxDescription": "Одновременно в ожидании может находиться только один перевод BTC. Для отправки нового перевода дождитесь завершения предыдущего.", "sendFungibleSatProtectionTitle": "Отправка с защитой Sat", "sendFungibleSatProtectionExplainer": "Phantom гаранти<PERSON><PERSON><PERSON><PERSON>, что ваши ординалы и BRC20 не будут использованы для оплаты транзакций или переводов Bitcoin.", "sendFungibleTransferFee": "Комиссия за передачу токенов", "sendFungibleTransferFeeToolTip": "Создатель этого токена получает комиссию за каждый перевод. Phantom не устанавливает и не собирает эту комиссию.", "sendFungibleInterestBearingPercent": "Текущая процентная ставка", "sendFungibleNonTransferable": "Нельзя передать", "sendFungibleNonTransferableToolTip": "Этот токен нельзя перевести на другой аккаунт.", "sendFungibleNonTransferableYes": "Да", "sendStatusErrorMessageInterpolated": "При попытке отправить токены получателю <1>{{uiRecipient}}</1> произошла ошибка", "sendStatusErrorMessageInsufficientBalance": "Вашего баланса недостаточно для завершения транзакции.", "sendStatusErrorTitle": "Не удалось отправить", "sendStatusLoadingTitle": "Отправка…", "sendStatusSuccessMessageInterpolated": "Ваши токены отправлены получателю <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Отправлено!", "sendStatusConfirmedSuccessTitle": "Отправлено!", "sendStatusSubmittedSuccessTitle": "Транзакция отправлена", "sendStatusEstimatedTransactionTime": "Предполагаемое время транзакции: {{time}}", "sendStatusViewTransaction": "Показать транзакцию", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> на <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> успешно отправлено получателю <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> успешно отправлено получателю <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "Не удалось отправить <2>{{uiAmount}} {{assetSymbol}}</2> на <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON>од ошиб<PERSON>и {{code}}", "sendFormErrorInsufficientBalance": "Недостаточно средств", "sendFormErrorEmptyAmount": "Нужно указать количество", "sendFormInvalidAddress": "Недопустимый адрес {{assetName}}", "sendFormInvalidUsernameOrAddress": "Неправильное имя пользователя или адрес", "sendFormErrorInvalidSolanaAddress": "Неверный адрес Solana", "sendFormErrorInvalidTwitterHandle": "Это имя пользователя Twitter не зарегистрировано", "sendFormErrorInvalidDomain": "Этот домен не зарегистрирован", "sendFormErrorInvalidUsername": "Это имя пользователя не зарегистрировано", "sendFormErrorMinRequiredInterpolated": "Требуется минимум {{minAmount}} {{tokenName}}", "sendRecipientTextareaPlaceholder": "Адрес получателя SOL", "sendRecipientTextAreaPlaceholder2": "Адрес получателя {{symbol}}", "sendMemoOptional": "Memo (необязательно)", "sendMemo": "Memo", "sendOptional": "необязательно", "settings": "Настройки", "settingsDapps": "dApp", "settingsSelectedAccount": "Выбранный аккаунт", "settingsAddressBookNoLabel": "Без метки", "settingsAddressBookPrimary": "Адресная книга", "settingsAddressBookRecentlyUsed": "Недавно использованные", "settingsAddressBookSecondary": "Управление списком часто используемых адресов", "settingsAutoLockTimerPrimary": "Таймер автоблокировки", "settingsAutoLockTimerSecondary": "Изменить длительность таймера автоблокировки", "settingsChangeLanguagePrimary": "Изменить язык", "settingsChangeLanguageSecondary": "Изменить язык отображения", "settingsChangeNetworkPrimary": "Изменить сеть", "settingsChangeNetworkSecondary": "Настроить параметры сети", "settingsChangePasswordPrimary": "Изменить пароль", "settingsChangePasswordSecondary": "Изменить пароль экрана блокировки", "settingsCompleteBetaSurvey": "Завершить бета-опрос", "settingsDisplayLanguage": "Язык интерфейса", "settingsErrorCannotExportLedgerPrivateKey": "Не удалось экспортировать приватный ключ Ledger", "settingsErrorCannotRemoveAllWallets": "Нельзя удалить все аккаунты", "settingsExportPrivateKey": "Показать приватный ключ", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "RPC сети Phantom", "settingsTestNetworks": "Тестовые сети", "settingsUseCustomNetworks": "Использовать пользовательские сети", "settingsTestnetMode": "Режим Testnet", "settingsTestnetModeDescription": "Применяется к балансам и подключениям приложений.", "settingsWebViewDebugging": "Отладка веб-представлений", "settingsWebViewDebuggingDescription": "Дает возможность проверять и отлаживать веб-представления в браузере приложения.", "settingsTestNetworksInfo": "Переключение на любую сеть Testnet предназначено только для целей тестирования. Учтите, что токены в сетях Testnet не имеют никакой денежной ценности.", "settingsEmojis": "Эмодзи", "settingsNoAddresses": "Нет адресов", "settingsAddressBookEmptyHeading": "Ваша адресная книга пуста", "settingsAddressBookEmptyText": "Нажмите кнопки «+» или «Добавить адрес», чтобы добавить избранные адреса", "settingsEditWallet": "Изменить аккаунт", "settingsNoTrustedApps": "Нет доверенных приложений", "settingsNoConnections": "Подключений пока нет.", "settingsRemoveWallet": "Удалить аккаунт", "settingsResetApp": "Сброс приложения", "settingsBlocked": "Заблокировано", "settingsBlockedAccounts": "Заблокированные аккаунты", "settingsNoBlockedAccounts": "Нет заблокированных аккаунтов.", "settingsRemoveSecretPhrase": "Убрать секретную фразу", "settingsResetAppWithSecretPhrase": "Сбросить приложение с секретной фразой", "settingsResetSecretRecoveryPhrase": "Сбросить секретную фразу", "settingsShowSecretRecoveryPhrase": "Показать секретную фразу", "settingsShowSecretRecoveryPhraseSecondary": "Показать фразу восстановления", "settingsShowSecretRecoveryPhraseTertiary": "Показать секретную фразу", "settingsTrustedAppsAutoConfirmActiveUntil": "До {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Автоподтверждение", "settingsTrustedAppsDisclaimer": "Включайте автоподтверждение только на надежных сайтах", "settingsTrustedAppsLastUsed": "Последнее использование: {{formattedTimestamp}}", "settingsTrustedAppsPrimary": "Подключенные приложения", "settingsTrustedApps": "Доверенные приложения", "settingsTrustedAppsRevoke": "Отменить", "settingsTrustedAppsRevokeToast": "Приложение {{trustedApp}} отключено", "settingsTrustedAppsSecondary": "Настроить доверенные приложения", "settingsTrustedAppsToday": "Сегодня", "settingsTrustedAppsYesterday": "Вчера", "settingsTrustedAppsLastWeek": "На прошлой неделе", "settingsTrustedAppsBeforeYesterday": "Ранее", "settingsTrustedAppsDisconnectAll": "Отключиться от всех", "settingsTrustedAppsDisconnectAllToast": "Все приложения отключены", "settingsTrustedAppsEndAutoConfirmForAll": "Завершить автоподтверждение для всех", "settingsTrustedAppsEndAutoConfirmForAllToast": "Все сессии автоподтверждения завершены", "settingsSecurityPrimary": "Безопасность и конфиденциальность", "settingsSecuritySecondary": "Обновите настройки безопасности", "settingsActiveNetworks": "Активные сети", "settingsActiveNetworksAll": "Все", "settingsActiveNetworksSolana": "Только Solana", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana является сетью по умолчанию и всегда остается включенной.", "settingsDeveloperPrimary": "Настройки разработчика", "settingsAdvanced": "Расширенные настройки", "settingsTransactions": "Настройки транзакции", "settingsAutoConfirm": "Настройки автоподтверждения", "settingsSecurityAnalyticsPrimary": "Делиться анонимной аналитикой", "settingsSecurityAnalyticsSecondary": "Включите, чтобы помочь нам улучшить сервис", "settingsSecurityAnalyticsHelper": "Phantom не использует вашу персональную информацию в аналитических целях", "settingsSuspiciousCollectiblesPrimary": "Скрыть подозрительные предметы коллекционирования", "settingsSuspiciousCollectiblesSecondary": "Переключите, чтобы скрыть отмеченные предметы коллекционирования", "settingsPreferredBitcoinAddress": "Предпочитаемый Bitcoin-адрес", "settingsEnabledAddressesUpdated": "Видимые адреса обновлены!", "settingsEnabledAddresses": "Включенные адреса", "settingsBitcoinPaymentAddressForApps": "Адрес для платежей в приложениях", "settingsBitcoinOrdinalsAddressForApps": "Адрес ординалов для приложений", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Когда оба вышеуказанных типа адресов включены, в некоторых приложениях, таких как <PERSON> Eden, для финансирования покупок будет использоваться ваш собственный Segwit-адрес. Приобретенные активы будут поступать на ваш адрес Taproot.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Bitcoin-адрес по умолчанию в Phantom для обеспечения совместимости.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(По умолчанию)", "settingsPreferredBitcoinAddressTaprootExplainer": "Самый современный тип адреса, обычно с более низкой комиссией за транзакции.", "settingsPreferredExplorers": "Предпочтительный обозреватель", "settingsPreferredExplorersSecondary": "Перейдите на предпочтительный обозреватель блокчейна", "settingsCustomGasControls": "Пользовательские настройки газа", "settingsSupportDesk": "Служба поддержки", "settingsSubmitATicket": "Отправить запрос", "settingsAttachApplicationLogs": "Приложить журналы приложения", "settingsDownloadApplicationLogs": "Скачать журналы приложения", "settingsDownloadApplicationLogsShort": "Скачать журналы", "settingsDownloadApplicationLogsHelper": "Журналы содержат локальные данные, отчеты о сбоях и публичные адреса кошельков. Эта информация поможет службе поддержки Phantom решить проблему", "settingsDownloadApplicationLogsWarning": "В журналах нет конфиденциальных данных, таких как сид-фразы или приватные ключи.", "settingsWallet": "Кошелек", "settingsPreferences": "Параметры", "settingsSecurity": "Безопасность", "settingsDeveloper": "Разработчик", "settingsSupport": "Поддержка", "settingsWalletShortcutsPrimary": "Показать ярлыки кошелька", "settingsAppIcon": "Значок приложения", "settingsAppIconDefault": "По умолчанию", "settingsAppIconLight": "Светлый", "settingsAppIconDark": "Темный", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "Аккаунт", "settingsSearchResultSelected": "Выбрано", "settingsSearchResultExport": "Экспорт", "settingsSearchResultSeed": "Сид-фраза", "settingsSearchResultTrusted": "Доверенные", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Статус", "settingsSearchResultLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultBiometric": "Биометрия", "settingsSearchResultTouch": "Отпечаток", "settingsSearchResultFace": "<PERSON>и<PERSON><PERSON>", "settingsSearchResultShortcuts": "Ярлыки", "settingsAllSitesPermissionsTitle": "Доступ <PERSON> к данным на всех сайтах", "settingsAllSitesPermissionsSubtitle": "Дает возможность использовать приложения в сочетании с Phantom без нажатия на расширение", "settingsAllSitesPermissionsDisabled": "Ваш браузер не поддерживает изменение этой настройки", "settingsSolanaCopyTransaction": "Включить копирование транзакции", "settingsSolanaCopyTransactionDetails": "Копировать сериализованные данные транзакции в буфер обмена", "settingsAutoConfirmHeader": "Автоподтверждение", "refreshWebpageToApplyChanges": "Обновите веб-страницу, чтобы применить изменения", "settingsExperimentalTitle": "Экспериментальные функции", "settingsExprimentalSolanaActionsSubtitle": "Автоматически разворачивает кнопки Solana Action при обнаружении соответствующих ссылок на X.com", "stakeAccountCardActiveStake": "Активный стейк", "stakeAccountCardBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountCardRentReserve": "Резерв оплаты", "stakeAccountCardRewards": "Последнее вознаграждение", "stakeAccountCardRewardsTooltip": "Это самое последнее вознаграждение, которое вы получили за стейкинг. Вы получаете вознаграждение каждые 3 дня.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "Блокировка до", "stakeRewardsHistoryTitle": "История вознаграждений", "stakeRewardsActivityItemTitle": "Вознаграждения", "stakeRewardsHistoryEmptyList": "Нет вознаграждений", "stakeRewardsTime_zero": "Сегодня", "stakeRewardsTime_one": "Вчера", "stakeRewardsTime_other": "{{count}} дн. назад", "stakeRewardsItemsPagingFailed": "Не удалось загрузить предыдущие вознаграждения.", "stakeAccountCreateAndDelegateErrorStaking": "Проблема со стейкингом на этом валидаторе. Повторите попытку.", "stakeAccountCreateAndDelegateSolStaked": "SOL застейканы!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Ваши SOL начнут получать вознаграждение <1></1> через несколько дней, как только стейк-аккаунт станет активным.", "stakeAccountCreateAndDelegateStakingFailed": "Ошибка стейкинга", "stakeAccountCreateAndDelegateStakingSol": "Стейкаем SOL…", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Создается стейк-аккаунт. Затем ваши SOL будут делегированы сюда:", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Создается стейк-аккаунт. Затем ваши SOL будут делегированы {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Показать транзакцию", "stakeAccountDeactivateStakeSolUnstaked": "SOL сняты со стейка!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Вывести стейк можно будет <1></1> через несколько дней, как только стейк-аккаунт станет активным.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Вывести стейк можно будет через несколько дней, как только стейк-аккаунт станет неактивным.", "stakeAccountDeactivateStakeUnstakingFailed": "Ошибка отмены стейка", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Проблема с отменой стейка на этом валидаторе. Повторите попытку.", "stakeAccountDeactivateStakeUnstakingSol": "Отмена стейка SOL…", "stakeAccountDeactivateStakeUnstakingSolDescription": "Начинаем процесс отмены стейка ваших SOL.", "stakeAccountDeactivateStakeViewTransaction": "Показать транзакцию", "stakeAccountDelegateStakeSolStaked": "SOL застейканы!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Ваши SOL начнут получать вознаграждение <1></1> через несколько дней, как только стейк-аккаунт станет активным.", "stakeAccountDelegateStakeStakingFailed": "Ошибка стейкинга", "stakeAccountDelegateStakeStakingFailedDescription": "Проблема со стейкингом на этом валидаторе. Повторите попытку.", "stakeAccountDelegateStakeStakingSol": "Стейкаем SOL…", "stakeAccountDelegateStakeStakingSolDescription": "Делегируем ваши SOL.", "stakeAccountDelegateStakeViewTransaction": "Показать транзакцию", "stakeAccountListActivationActivating": "Активация", "stakeAccountListActivationActive": "Активный", "stakeAccountListActivationInactive": "Неактивный", "stakeAccountListActivationDeactivating": "Деактивация", "stakeAccountListErrorFetching": "Не удалось получить стейк-аккаунты. Повторите попытку позже.", "stakeAccountListNoStakingAccounts": "Нет стейк-акка<PERSON><PERSON>тов", "stakeAccountListReload": "Перезагрузить", "stakeAccountListViewPrimaryText": "<PERSON><PERSON><PERSON> стейк", "stakeAccountListViewStakeSOL": "Застейкать SOL", "stakeAccountListItemStakeFee": "Комиссия {{fee}}", "stakeAccountViewActionButtonRestake": "Повторить стейк", "stakeAccountViewActionButtonUnstake": "Снять со стейкинга", "stakeAccountViewError": "Ошибка", "stakeAccountViewPrimaryText": "<PERSON><PERSON><PERSON> стейк", "stakeAccountViewRestake": "Повторить стейк", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Сейчас ваши SOL застейканы на валидаторе. Чтобы получить доступ к этим средствам, <1></1> нужно отменить стейк. <3>Подробнее</3>", "stakeAccountViewStakeInactive": {"part1": "Этот стейк-аккаунт неактивен. Его стейк можно вывести или делегировать валидатору.", "part2": "Подробнее"}, "stakeAccountViewStakeNotFound": "Этот стейк-аккаунт не найден.", "stakeAccountViewViewOnExplorer": "Показать в {{explorer}}", "stakeAccountViewWithdrawStake": "Вывести стейк", "stakeAccountViewWithdrawUnstakedSOL": "Вывести SOL, снятые со стейка", "stakeAccountInsufficientFunds": "Недостаточно доступных SOL для снятия со стейкинга или вывода.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL выведены!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Ваши SOL выведены.", "part2": "Этот стейк-аккаунт будет автоматически удален в течение нескольких минут."}, "stakeAccountWithdrawStakeViewTransaction": "Показать транзакцию", "stakeAccountWithdrawStakeWithdrawalFailed": "Ошибка вывода", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Проблема с выводом с этого стейк-аккаунта. Повторите попытку.", "stakeAccountWithdrawStakeWithdrawingSol": "Выводим SOL…", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Мы выводим SOL с этого стейк-аккаунта.", "startEarningSolAccount": "аккаунт", "startEarningSolAccounts": "аккаунты", "startEarningSolErrorClosePhantom": "Нажмите здесь и попробуйте еще раз", "startEarningSolErrorTroubleLoading": "Проблемы при загрузке стейка", "startEarningSolLoading": "Загрузка…", "startEarningSolPrimaryText": "Зарабатывайте SOL", "startEarningSolSearching": "Поиск стейк-аккаунтов", "startEarningSolStakeTokens": "Стейкайте токены и получайте вознаграждение", "startEarningSolYourStake": "<PERSON><PERSON><PERSON> стейк", "unwrapFungibleTitle": "Своп на {{tokenSymbol}}", "unwrapFungibleDescription": "Вывод средств с {{fromToken}} на {{toToken}}", "unwrapFungibleConfirmSwap": "Подтвердить своп", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Ориентировочные комиссии", "swapFeesFees": "Комиссии", "swapFeesPhantomFee": "Комиссия Phantom", "swapFeesPhantomFeeDisclaimer": "Мы всегда стараемся найти лучшую цену от ведущих поставщиков ликвидности. Комиссия в размере {{feePercentage}} автоматически учитывается в этом предложении цены.", "swapFeesRate": "Цена", "swapFeesRateDisclaimer": "<PERSON>у<PERSON><PERSON><PERSON> курс, найденный агрегатором Jupiter на нескольких децентрализованных биржах.", "swapFeesRateDisclaimerMultichain": "Лучшая ставка на нескольких децентрализованных биржах.", "swapFeesPriceImpact": "Влияние на цену", "swapFeesHighPriceImpact": "Высокое влияние на цену", "swapFeesPriceImpactDisclaimer": "Разница между рыночной ценой и ориентировочной ценой, рассчитанная по размеру сделки.", "swapFeesSlippage": "Слипедж", "swapFeesHighSlippage": "Высокий предел слипеджа", "swapFeesHighSlippageDisclaimer": "Транзакция не будет выполнена, если цена изменится в неблагоприятную сторону более чем на {{slippage}}%.", "swapTransferFee": "Комиссия за перевод", "swapTransferFeeDisclaimer": "При торговле ${{symbol}} взимается комиссия за перевод {{feePercent}}%, установленная создателем токена, а не Phantom.", "swapTransferFeeDisclaimerMany": "При торговле выбранными токенами взимается комиссия в размере {{feePercent}}%, установленная создателями токенов, а не Phantom.", "swapFeesSlippageDisclaimer": "Сумма, на которую цена вашей сделки может отклониться от предоставленного предложения цены.", "swapFeesProvider": "Поставщик", "swapFeesProviderDisclaimer": "Децентрализованная биржа, используемая для завершения вашей сделки.", "swapEstimatedTime": "Предполагаемое время", "swapEstimatedTimeShort": "Предпол. время", "swapEstimatedTimeDisclaimer": "Расчетное время завершения переноса будет зависеть от нескольких факторов, влияющих на скорость транзакций.", "swapSettingsButtonCommand": "Открыть настройки свопа", "swapQuestionRetry": "Повторить?", "swapUnverifiedTokens": "Непроверенные токены", "swapSectionTitleTokens": "Токены {{section}}", "swapFlowYouPay": "Вы заплатите", "swapFlowYouReceive": "Вы получите", "swapFlowActionButtonText": "Показать ордер", "swapAssetCardTokenNetwork": "{{symbol}} в {{network}}", "swapAssetCardMaxButton": "Максимум", "swapAssetCardSelectTokenAndNetwork": "Выберите токен и сеть", "swapAssetCardBuyTitle": "Вы получите", "swapAssetCardSellTitle": "Вы заплатите", "swapAssetWarningUnverified": "Это непроверенный токен. Взаимодействуйте только с теми токенами, которым вы доверяете.", "swapAssetWarningPermanentDelegate": "Делегат может навсегда сжечь или передать эти токены.", "swapSlippageSettingsTitle": "Настройки слипеджа", "swapSlippageSettingsSubtitle": "Транзакция не будет завершена, если изменение цены превысит слипедж. Слишком высокое значение приведет к невыгодной сделке.", "swapSlippageSettingsCustom": "Свои", "swapSlippageSettingsHighSlippageWarning": "Ваша транзакция может быть с фронтраннингом и привести к невыгодной сделке.", "swapSlippageSettingsCustomMinError": "Введите значение большее, чем {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "Введите значение меньшее, чем {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "Введите действительное значение.", "swapSlippageSettingsAutoSubtitle": "Phantom найдет самый низкий слипедж, чтобы обмен был выгодным.", "swapSlippageSettingsAuto": "Автоматические", "swapSlippageSettingsFixed": "Фиксированные", "swapSlippageOptInTitle": "Автоматический слипедж", "swapSlippageOptInSubtitle": "Phantom найдет наименьший слипедж для успешного свопа. Эту настройку в любое время можно изменить в «Swapper» → «Настройки слипеджа».", "swapSlippageOptInEnableOption": "Включить автоматический слипедж", "swapSlippageOptInRejectOption": "Продолжить с фиксированным слипеджем", "swapQuoteFeeDisclaimer": "В предложение цены входит комиссия Phantom в размере {{feePercentage}}", "swapQuoteMissingContext": "Отсутствует контекст для предложения цены свопа", "swapQuoteErrorNoQuotes": "Попытка свопа без предложения цены", "swapQuoteSolanaNetwork": "Сеть Solana", "swapQuoteNetwork": "Сеть", "swapQuoteOneTimeSerumAccount": "Одноразовый аккаунт Serum", "swapQuoteOneTimeTokenAccount": "Одноразовый аккаунт токена", "swapQuoteBridgeFee": "Комиссия за обмен между разными блокчейнами", "swapQuoteDestinationNetwork": "Сеть назначения", "swapQuoteLiquidityProvider": "Поставщик ликвидности", "swapReviewFlowActionButtonPrimary": "Своп", "swapReviewFlowPrimaryText": "Показать ордер", "swapReviewFlowYouPay": "Вы заплатите", "swapReviewFlowYouReceive": "Вы получите", "swapReviewInsufficientBalance": "Недостаточно средств", "ugcSwapWarningTitle": "Предупреждение", "ugcSwapWarningBody1": "Этот токен торгуется на платформе запуска токенов {{programName}}.", "ugcSwapWarningBody2": "Стоимость этих токенов может сильно колебаться, что может привести к значительным финансовым прибылям или убыткам. Совершайте сделки на свой страх и риск.", "ugcSwapWarningConfirm": "Я понимаю", "bondingCurveProgressLabel": "Прогресс Bonding Curve", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "В модели Bonding Curve цены на токены определяются формой кривой: они растут при покупке токенов и падают при продаже. Когда токены будут распроданы, вся ликвидность будет внесена на Raydium и сожжена.", "ugcFungibleWarningBanner": "Этот токен торгуется на {{programName}}", "ugcCreatedRowLabel": "Создано", "ugcStatusRowLabel": "Статус", "ugcStatusRowValue": "Повышен", "swapTxConfirmationReceived": "Получено!", "swapTxConfirmationSwapFailed": "Своп не прошел", "swapTxConfirmationSwapFailedStaleQuota": "Предложение цены больше не действительно. Попробуйте еще раз.", "swapTxConfirmationSwapFailedSlippageLimit": "Слипедж для этого свопа слишком мал. Увеличьте слипедж в верхней части экрана свопа и повторите попытку.", "swapTxConfirmationSwapFailedInsufficientBalance": "Мы не смогли выполнить запрос. У вас недостаточно баланса для завершения транзакции.", "swapTxConfirmationSwapFailedEmptyRoute": "Ликвидность для этой пары токенов изменилась. Мы не смогли найти подходящее предложение цены. Попробуйте еще раз или скорректируйте количество токенов.", "swapTxConfirmationSwapFailedAcountFrozen": "Создатель заморозил этот токен. Вы не можете ни отправить, ни обменять его.", "swapTxConfirmationSwapFailedTryAgain": "Ошибка свопа — повторите попытку", "swapTxConfirmationSwapFailedUnknownError": "Мы не смогли завершить своп. Ваши средства не пострадали. Попробуйте еще раз. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Мы не смогли смоделировать своп. Ваши средства не пострадали. Попробуйте еще раз.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Мы не смогли завершить своп. Ваши средства не пострадали. Попробуйте еще раз. ", "swapTxConfirmationSwapFailedInsufficientGas": "На вашем счете недостаточно средств для завершения транзакции. Пополните счет и повторите попытку.", "swapTxConfirmationSwapFailedLedgerReject": "Своп отклонен пользователем на аппаратном устройстве.", "swapTxConfirmationSwapFailedLedgerConnectionError": "Своп отклонен из-за ошибки подключения устройства. Попробуйте еще раз.", "swapTxConfirmationSwapFailedLedgerSignError": "Своп отклонен из-за ошибки подписания с помощью устройства. Попробуйте еще раз.", "swapTxConfirmationSwapFailedLedgerError": "Своп отклонен из-за ошибки устройства. Попробуйте еще раз.", "swapTxConfirmationSwappingTokens": "Свопаем токены…", "swapTxConfirmationTokens": "Токены", "swapTxConfirmationTokensDeposited": "Готово! Токены зачислены на кошелек", "swapTxConfirmationTokensDepositedTitle": "Готово!", "swapTxConfirmationTokensDepositedBody": "Токены зачислены на ваш кошелек", "swapTxConfirmationTokensWillBeDeposited": "будут зачислены на кошелек после завершения транзакции", "swapTxConfirmationViewTransaction": "Показать транзакцию", "swapTxBridgeSubmitting": "Отправка транзакции", "swapTxBridgeSubmittingDescription": "Своп {{sellAmount}} в {{sellNetwork}} на {{buyAmount}} в {{buyNetwork}}", "swapTxBridgeFailed": "Не удалось отправить транзакцию", "swapTxBridgeFailedDescription": "Мы не смогли выполнить запрос.", "swapTxBridgeSubmitted": "Транзакция отправлена", "swapTxBridgeSubmittedDescription": "Предполагаемое время транзакции: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "Теперь это окно можно закрыть.", "swapperSwitchTokens": "Обменять токены", "swapperMax": "Максимум", "swapperTooltipNetwork": "Сеть", "swapperTooltipPrice": "Цена", "swapperTooltipAddress": "Контракт", "swapperTrendingSortBy": "Сортировать по", "swapperTrendingTimeFrame": "Временной интервал", "swapperTrendingNetwork": "Сеть", "swapperTrendingRank": "<PERSON>ей<PERSON>инг", "swapperTrendingTokens": "Популярные токены", "swapperTrendingVolume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swapperTrendingPrice": "Цена", "swapperTrendingPriceChange": "Изменение цены", "swapperTrendingMarketCap": "Верхний предел по рынку", "swapperTrendingTimeFrame1h": "1 ч.", "swapperTrendingTimeFrame24h": "24 ч.", "swapperTrendingTimeFrame7d": "7 дн.", "swapperTrendingTimeFrame30d": "30 дн.", "swapperTrendingNoTokensFound": "Токены не найдены.", "switchToggle": "Переключить", "termsOfServiceActionButtonAgree": "Я принимаю", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Нажимая <1>Я принимаю</1>, вы принимаете <3>Условия использования</3> свопа токенов на платформе Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Мы пересмотрели Условия использования. Нажимая <1>Я принимаю</1>, вы соглашаетесь с новыми <3> Условиями использования</3>.<5></5><6></6>Новые Условия использования включают в себя обновленную <8>структуру комиссий</8> для определенных продуктов.", "termsOfServicePrimaryText": "Условия использования", "tokenRowUnknownToken": "Неизвестный токен", "transactionsAppInteraction": "Взаимодействие приложений", "transactionsFailedAppInteraction": "Неудачное взаимодействие приложений", "transactionsBidOnInterpolated": "Предложение по {{name}}", "transactionsBidFailed": "Предложение не прошло", "transactionsBoughtInterpolated": "Ку<PERSON>лено {{name}}", "transactionsBoughtCollectible": "Куплен предмет коллекционирования", "transactionBridgeInitiated": "Перенос инициирован", "transactionBridgeInitiatedFailed": "Не удалось инициировать перенос", "transactionBridgeStatusLink": "Проверить статус на LI.FI", "transactionsBuyFailed": "Покупка не прошла", "transactionsBurnedSpam": "Сожженный спам", "transactionsBurned": "Сожжено", "transactionsUnwrapped": "Развернуто", "transactionsUnwrappedFailed": "Сбой разворачивания", "transactionsCancelBidOnInterpolated": "Отменено предложение по «{{name}}»", "transactionsCancelBidOnFailed": "Не удалось отменить предложение", "transactionsError": "Ошибка", "transactionsFailed": "Ошибка", "transactionsSwapped": "Заключен своп", "transactionsFailedSwap": "Своп не прошел", "transactionsFailedBurn": "Неудачное сжигание", "transactionsFrom": "Откуда", "transactionsListedInterpolated": "{{name}} выставлено на продажу", "transactionsListedFailed": "Не удалось разместить", "transactionsNoActivity": "Нет действий", "transactionsReceived": "Получено", "transactionsReceivedInterpolated": "Получено: {{amount}} SOL", "transactionsSending": "Отправка…", "transactionsPendingCreateListingInterpolated": "Создание «{{name}}»", "transactionsPendingEditListingInterpolated": "Изменение «{{name}}»", "transactionsPendingSolanaPayTransaction": "Подтверждение транзакции Solana Pay", "transactionsPendingRemoveListingInterpolated": "Снятие «{{name}}» с продажи", "transactionsPendingBurningInterpolated": "Сжигание «{{name}}»", "transactionsPendingSending": "Отправка", "transactionsPendingSwapping": "Своп", "transactionsPendingBridging": "Перенос", "transactionsPendingApproving": "Подтверждение", "transactionsPendingCreatingAndDelegatingStake": "Создание и делегирование стейка", "transactionsPendingDeactivatingStake": "Деактивация стейка", "transactionsPendingDelegatingStake": "Делегирование стейка", "transactionsPendingWithdrawingStake": "Вывод стейка", "transactionsPendingAppInteraction": "Ожидание взаимодействия приложений", "transactionsPendingBitcoinTransaction": "Незавершенные транзакции BTC", "transactionsSent": "Отправлено", "transactionsSendFailed": "Не удалось отправить", "transactionsSwapOn": "Своп в {{dappName}}", "transactionsSentInterpolated": "Отправлено: {{amount}} SOL", "transactionsSoldInterpolated": "Продано {{name}}", "transactionsSoldCollectible": "Продан предмет коллекционирования", "transactionsSoldFailed": "Продажа не прошла", "transactionsStaked": "Застейкано", "transactionsStakedFailed": "Ошибка стейкинга", "transactionsSuccess": "Выполнено", "transactionsTo": "Куда", "transactionsTokenSwap": "Своп токена", "transactionsUnknownAmount": "Неизвестно", "transactionsUnlistedInterpolated": "{{name}} снято с продажи", "transactionsUnstaked": "Снято со стейка", "transactionsUnlistedFailed": "Не удалось снять с продажи", "transactionsDeactivateStake": "Деактивированный стейк", "transactionsDeactivateStakeFailed": "Не удалось деактивировать стейк", "transactionsWaitingForConfirmation": "Ожидание подтверждения", "transactionsWithdrawStake": "Вывести стейк", "transactionsWithdrawStakeFailed": "Не удалось отменить стейк", "transactionCancelled": "Отменена", "transactionCancelledFailed": "Не удалось отменить", "transactionApproveToken": "Подтверждено {{tokenSymbol}}", "transactionApproveTokenFailed": "Не удалось подтвердить {{tokenSymbol}}", "transactionApprovalFailed": "Не удалось подтвердить", "transactionRevokeApproveToken": "Отменено {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "Не удалось отменить {{tokenSymbol}}", "transactionRevokeFailed": "Не удалось отозвать", "transactionApproveDetailsTitle": "Детали подтверждения", "transactionCancelOrder": "Отменить ордер", "transactionCancelOrderFailed": "Не удалось отменить ордер", "transactionApproveAppLabel": "Приложение", "transactionApproveAmountLabel": "Сумма", "transactionApproveTokenLabel": "Токен", "transactionApproveCollectionLabel": "Коллекция", "transactionApproveAllItems": "Подтвердить все элементы", "transactionSpendUpTo": "Тратить до", "transactionCancel": "Отменить транзакцию", "transactionPrioritizeCancel": "Установить приоритет отмены", "transactionSpeedUp": "Ускорить транзакцию", "transactionCancelHelperText": "Исходная транзакция может завершиться до того, как она будет отменена.", "transactionSpeedUplHelperText": "Это позволит максимально увеличить скорость вашей транзакции в зависимости от условий сети.", "transactionCancelHelperMobile": "Попытка отменить эту транзакцию будет стоить <1>до {{amount}}</1>. Исходная транзакция может завершиться до того, как она будет отменена.", "transactionCancelHelperMobileWithEstimate": "Попытка отменить эту транзакцию будет стоить <1>до {{amount}}</1>. Это займет около {{timeEstimate}}. Исходная транзакция может завершиться до того, как она будет отменена.", "transactionSpeedUpHelperMobile": "Максимальное ускорение транзакции будет стоить <1> до {{amount}}</1>.", "transactionSpeedUpHelperMobileWithEstimate": "Максимальное ускорение транзакции будет стоить <1> до {{amount}}</1>. Примерное время выполнения — {{timeEstimate}}.", "transactionEstimatedTime": "Предполагаемое время", "transactionCancelingSend": "Отмена отправки", "transactionPrioritizingCancel": "Установка приоритета отмены", "transactionCanceling": "Отмена", "transactionReplaceError": "Произошла ошибка. С вашего счета не списывались комиссии. Можете повторить попытку.", "transactionNotEnoughNative": "Недостаточно {{nativeTokenSymbol}}", "transactionGasLimitError": "Не удалось оценить лимит газа", "transactionGasEstimationError": "Не удалось оценить газ", "pendingTransactionCancel": "Отмена", "pendingTransactionSpeedUp": "Ускорить", "pendingTransactionStatus": "Статус", "pendingTransactionPending": "Ожидание", "pendingTransactionPendingInteraction": "Ожидание взаимодействия", "pendingTransactionCancelling": "Отмена", "pendingTransactionDate": "Дата", "pendingTransactionNetworkFee": "Комиссия сети", "pendingTransactionEstimatedTime": "Предполагаемое время", "pendingTransactionEstimatedTimeHM": "{{hours}} ч. {{minutes}} мин.", "pendingTransactionEstimatedTimeMS": "{{minutes}} мин. {{seconds}} с.", "pendingTransactionEstimatedTimeS": "{{seconds}} с.", "pendingTransactionsSendingTitle": "Отправка {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Неизвестно", "pendingTransactionUnknownApp": "Неизвестное приложение", "permanentDelegateTitle": "Делегировано", "permanentDelegateValue": "Постоянный", "permanentDelegateTooltipTitle": "Постоянное делегирование", "permanentDelegateTooltipValue": "Постоянное делегирование дает возможность другому аккаунту управлять вашими токенами, включая их передачу или сжигание.", "unlockActionButtonUnlock": "Разблокировать", "unlockEnterPassword": "Введите пароль", "unlockErrorIncorrectPassword": "Неверный пароль", "unlockErrorSomethingWentWrong": "Что-то пошло не так. Повторите попытку позже", "unlockForgotPassword": "Забыли пароль?", "unlockPassword": "Пароль", "forgotPasswordText": "Пароль можно сбросить, введя фразу восстановления кошелька из 12–24 слов. Phantom не может восстановить пароль за вас.", "appInfo": "Сведения о приложении", "lastUsed": "Последнее использование", "url": "URL-адрес", "trustedAppAutoConfirmDisabledHardwareAccount": "Недоступно с аккаунтами аппаратных кошельков.", "trustedAppAutoConfirmDisclaimer1": "В активном состоянии Phantom будет одобрять все запросы из этого приложения, не уведомляя вас и не запрашивая подтверждения.", "trustedAppAutoConfirmDisclaimer2": "Включение этой функции может подвергнуть ваши средства риску мошенничества. Используйте эту функцию только в приложениях, которым доверяете.", "validationUtilsPasswordIsRequired": "Необходим пароль", "validationUtilsPasswordLength": "Пароль должен состоять из 8 символов", "validationUtilsPasswordsDontMatch": "Пароли не совпадают", "validationUtilsPasswordCantBeSame": "Использовать старый пароль нельзя", "validatorCardEstimatedApy": "Ориентировочная APY", "validatorCardCommission": "Комиссия", "validatorCardTotalStake": "<PERSON><PERSON><PERSON><PERSON> стейк", "validatorCardNumberOfDelegators": "Кол-во делегаторов", "validatorListChooseAValidator": "Выбрать валидатора", "validatorListErrorFetching": "Не удалось получить список валидаторов. Повторите попытку позже.", "validatorListNoResults": "Нет результатов", "validatorListReload": "Перезагрузить", "validatorInfoTooltip": "Валида<PERSON><PERSON>р", "validatorInfoTitle": "Валидаторы", "validatorInfoDescription": "Стейкая SOL через валидатора, вы способствуете повышению производительности и безопасности сети Solana, зарабатывая при этом SOL.", "validatorApyInfoTooltip": "Ориентир. APY", "validatorApyInfoTitle": "Ориентировочная APY", "validatorApyInfoDescription": "Это ставка доходности, которую вы получаете, стейкая SOL через валидатора.", "validatorViewActionButtonStake": "Застейкать", "validatorViewErrorFetching": "Не удалось получить список валидаторов.", "validatorViewInsufficientBalance": "Недостаточно средств", "validatorViewMax": "Максимум", "validatorViewPrimaryText": "Начать стейкинг", "validatorViewDescriptionInterpolated": "Выберите, сколько SOL <1></1> застейкать на валидаторе. <3>Подробнее</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "Для стейка требуется сумма в {{amount}} SOL", "validatorViewValidator": "Валида<PERSON><PERSON>р", "walletMenuItemsAddConnectWallet": "Добавить (подключить) кошелек", "walletMenuItemsBridgeAssets": "Перен<PERSON><PERSON> активов", "walletMenuItemsHelpAndSupport": "Помощь и поддержка", "walletMenuItemsLockWallet": "Заблокировать кошелек", "walletMenuItemsResetSecretPhrase": "Сбросить секретную фразу", "walletMenuItemsShowMoreAccounts": "Показать еще {{count}}...", "walletMenuItemsHideAccounts": "Скрыть аккаунты", "toggleMultiChainHeader": "Мультиблокчейн", "disableMultiChainHeader": "Режим «Только Solana»", "disableMultiChainDetail1Header": "Используйте только Solana", "disableMultiChainDetail1SecondaryText": "Управляйте своими аккаунта<PERSON><PERSON>, токенами и предметами коллекционирования, не переключаясь на другие блокчейны.", "disableMultiChainDetail2Header": "Вернуться к мультиблокчейну можно в любое время", "disableMultiChainDetail2SecondaryText": "При повторном включении мультиблокчейна существующий баланс Ethereum и Polygon сохранится.", "disableMultiChainButton": "Включить режим «Только Solana»", "disabledMultiChainHeader": "Режим «Только Solana» включен", "disabledMultiChainText": "Снова включить мультиблокчейн можно в любое время.", "enableMultiChainHeader": "Включить мультиблокчейн", "enabledMultiChainHeader": "Мультиблокчейн включен", "enabledMultiChainText": "Теперь ваш кошелек поддерживает Ethereum и Polygon.", "incompatibleAccountHeader": "Несовместимый аккаунт", "incompatibleAccountInterpolated": "Перед включением режима «Только Solana» удалите эти аккаунты, предназначенные лишь для Ethereum: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Новые возможности!", "welcomeToMultiChainPrimaryText": "Один кошелек для всего", "welcomeToMultiChainDetail1Header": "Поддержка Ethereum и Polygon", "welcomeToMultiChainDetail1SecondaryText": "Все ваши токены и NFT от Solana, Ethereum и Polygon в одном месте.", "welcomeToMultiChainDetail2Header": "Используйте все любимые приложения", "welcomeToMultiChainDetail2SecondaryText": "Подключайтесь к приложениям в нескольких цепочках, не переключая сети.", "welcomeToMultiChainDetail3Header": "Импортируйте кошелек MetaMask", "welcomeToMultiChainDetail3SecondaryText": "Легко импортируйте все свои сид-фразы в Ethereum и Polygon.", "welcomeToMultiChainIntro": "Добро пожаловать в Phantom Multichain", "welcomeToMultiChainIntroDesc": "Все ваши токены и NFT от Solana, Ethereum и Polygon в одном месте. Ваш единый кошелек для всего.", "welcomeToMultiChainAccounts": "Еще более удобные мультиблокчейновые аккаунты", "welcomeToMultiChainAccountsDesc": "Аккаунты адаптированы для мультиблокчейна, и у каждого из них теперь есть собственный адрес для ETH и Polygon.", "welcomeToMultiChainApps": "Работает со всеми", "welcomeToMultiChainAppsDesc": "Phantom совместим со всеми приложениями на Ethereum, Polygon и Solana. Нажмите «Подключиться к MetaMask», и можете приступать к работе.", "welcomeToMultiChainImport": "Мгновенный импорт из MetaMask", "welcomeToMultiChainImportDesc": "Импортируйте секретные фразы или приватные ключи из таких кошельков, как MetaMask или Coinbase Wallet. Всё в одном месте.", "welcomeToMultiChainImportInterpolated": "<0>Импортируйте секретные фразы</0> или приватные ключи из таких кошельков, как MetaMask или Coinbase Wallet. Всё в одном месте.", "welcomeToMultiChainTakeTour": "Совершите экскурсию", "welcomeToMultiChainSwapperTitle": "Своп на Ethereum,\nPolygon и Solana", "welcomeToMultiChainSwapperDetail1Header": "Поддержка Ethereum и Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Теперь вы можете легко обменивать токены ERC-20 внутри своего кошелька.", "welcomeToMultiChainSwapperDetail2Header": "Лучшие цены и супернизкие комиссии", "welcomeToMultiChainSwapperDetail2SecondaryText": "Более 100 источников ликвидности и интеллектуальная маршрутизация ордеров для получения максимальной прибыли.", "networkErrorTitle": "Ошибка сети", "networkError": "Не удается подключиться к сети. Повторите попытку позже.", "authenticationUnlockPhantom": "Разблокировать Phantom", "errorAndOfflineSomethingWentWrong": "Произошла ошибка", "errorAndOfflineSomethingWentWrongTryAgain": "Попробуйте снова.", "errorAndOfflineUnableToFetchAssets": "Не удалось получить активы. Повторите попытку позже.", "errorAndOfflineUnableToFetchCollectibles": "Не удалось получить предметы коллекционирования. Повторите попытку позже.", "errorAndOfflineUnableToFetchSwap": "Не удалось получить сведения о свопе. Повторите попытку позже.", "errorAndOfflineUnableToFetchTransactionHistory": "Сейчас мы не можем получить историю ваших транзакций. Проверьте подключение к Интернету или попробуйте позже.", "errorAndOfflineUnableToFetchRewardsHistory": "Не удалось получить историю вознаграждений. Повторите попытку позже.", "errorAndOfflineUnableToFetchBlockedUsers": "Не удалось получить данные о заблокированных пользователях. Попробуйте позже.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "При отображении ордера произошла ошибка. Повторите попытку.", "sendSelectToken": "Выбрать токен", "swapBalance": "Баланс:", "swapTitle": "Своп токенов", "swapSelectToken": "Выбрать токен", "swapYouPay": "Вы заплатите", "swapYouReceive": "Вы получите", "aboutPrivacyPolicy": "Политика конфиденциальности", "aboutVersion": "Версия {{version}}", "aboutVisitWebsite": "Открыть веб-сайт", "bottomSheetConnectTitle": "Подключить", "A11YbottomSheetConnectTitle": "Подключить", "A11YbottomSheetCommandClose": "Отклонить", "A11YbottomSheetCommandBack": "Назад", "bottomSheetSignTypedDataTitle": "Подпишите сообщение", "bottomSheetSignMessageTitle": "Подпишите сообщение", "bottomSheetSignInTitle": "Войдите", "bottomSheetSignInAndConnectTitle": "Войти", "bottomSheetConfirmTransactionTitle": "Подтверждение транзакции", "bottomSheetConfirmTransactionsTitle": "Подтвердите транзакции", "bottomSheetSolanaPayTitle": "Запр<PERSON>с Solana Pay", "bottomSheetAdvancedTitle": "Дополнительно", "bottomSheetReadOnlyAccountTitle": "Режим только для просмотра", "bottomSheetTransactionSettingsTitle": "Комиссия сети", "bottomSheetConnectDescription": "Подключение даст этому сайту возможность просматривать балансы и историю операций для выбранного аккаунта.", "bottomSheetSignInDescription": "Подписание этого сообщения подтверждает право владения выбранным аккаунтом. Подписывайте только сообщения от приложений, которым доверяете.", "bottomSheetSignInAndConnectDescription": "Подтверждение даст этому сайту возможность просматривать балансы и историю операций для выбранного аккаунта.", "bottomSheetConfirmTransactionDescription": "Изменения баланса ориентировочные. Суммы и задействованные активы не гарантируются.", "bottomSheetConfirmTransactionsDescription": "Изменения баланса ориентировочные. Суммы и задействованные активы не гарантируются.", "bottomSheetSignTypedDataDescription": "Это только запрос разрешения. Транзакция может быть выполнена не сразу.", "bottomSheetSignTypedDataSecondDescription": "Изменения баланса ориентировочные. Суммы и задействованные активы не гарантируются.", "bottomSheetSignMessageDescription": "Подписание этого сообщения подтверждает право владения выбранным аккаунтом. Подписывайте только сообщения от приложений, которым доверяете.", "bottomSheetReadOnlyAccountDescription": "Невозможно выполнить это действие в режиме только для просмотра.", "bottomSheetMessageRow": "Сообщение", "bottomSheetStatementRow": "Утверждение", "bottomSheetAutoConfirmRow": "Автоподтверждение", "bottomSheetAutoConfirmOff": "Выкл.", "bottomSheetAutoConfirmOn": "<PERSON><PERSON><PERSON>.", "bottomSheetAccountRow": "Аккаунт", "bottomSheetAdvancedRow": "Дополнительно", "bottomSheetContractRow": "Адрес контракта", "bottomSheetSpenderRow": "Адрес потребителя", "bottomSheetNetworkRow": "Сеть", "bottomSheetNetworkFeeRow": "Комиссия сети", "bottomSheetEstimatedTimeRow": "Предполагаемое время", "bottomSheetAccountRowDefaultAccountName": "Аккаунт", "bottomSheetConnectRequestDisclaimer": "Подключайтесь только к сайтам, которым доверяете", "bottomSheetSignInRequestDisclaimer": "Входите только на те сайты, которым доверяете", "bottomSheetSignatureRequestDisclaimer": "Подтверждайте только в том случае, если доверяете этому сайту.", "bottomSheetFeaturedTransactionDisclaimer": "На следующем шаге вы сможете просмотреть предварительные детали транзакции перед ее подтверждением.", "bottomSheetIgnoreWarning": "Игнорировать предупреждение, всё равно продолжить", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Изменений баланса не обнаружено. Действуйте с осторожностью и подтверждайте только в том случае, если доверяете этому сайту.", "bottomSheetReadOnlyWarning": "Вы только наблюдаете за этим адресом. Чтобы подписывать транзакции и сообщения, вам потребуется импортировать секретную фразу.", "bottomSheetWebsiteIsUnsafeWarning": "Использование этого веб-сайта небезопасно и может стать причиной кражи ваших средств.", "bottomSheetViewOnExplorer": "Показать в обозревателе", "bottomSheetTransactionSubmitted": "Транзакция отправлена", "bottomSheetTransactionPending": "Транзакция в обработке", "bottomSheetTransactionFailed": "Транзакция не выполнена", "bottomSheetTransactionSubmittedDescription": "Ваша транзакция отправлена. Ее можно просмотреть в обозревателе.", "bottomSheetTransactionFailedDescription": "Не удалось выполнить вашу транзакцию. Попробуйте еще раз.", "bottomSheetTransactionPendingDescription": "Транзакция обрабатывается...", "transactionsFromInterpolated": "Откуда: {{from}}", "transactionsFromParagraphInterpolated": "Откуда: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "Сегодня", "transactionsToInterpolated": "Куда: {{to}}", "transactionsToParagraphInterpolated": "Куда: {{to}}", "transactionsYesterday": "Вчера", "addEditAddressAdd": "Добавить адрес", "addEditAddressDelete": "Удалить адрес", "addEditAddressDeleteTitle": "Точно удалить этот адрес?", "addEditAddressSave": "Сохранить адрес", "dAppBrowserComingSoon": "Браузер dApp скоро выйдет!", "dAppBrowserSearchPlaceholder": "Сайты, токены, URL", "dAppBrowserOpenInNewTab": "Открыть в новой вкладке", "dAppBrowserSuggested": "Рекомендации", "dAppBrowserFavorites": "Избранное", "dAppBrowserBookmarks": "Закладки", "dAppBrowserBookmarkAdd": "Добавить закладку", "dAppBrowserBookmarkRemove": "Удалить закладку", "dAppBrowserUsers": "Пользователи", "dAppBrowserRecents": "Недавние", "dAppBrowserFavoritesDescription": "Избранное будет показано здесь", "dAppBrowserBookmarksDescription": "Здесь будут ваши закладки", "dAppBrowserRecentsDescription": "Здесь появятся недавно подключенные децентрализованные приложения", "dAppBrowserEmptyScreenDescription": "Введите URL-адрес или найдите в Интернете", "dAppBrowserBlocklistScreenTitle": "{{origin}} заблокирован! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom считает этот сайт вредоносным и небезопасным.", "part2": "Этот сайт занесен в поддерживаемую сообществом базу фишинговых и мошеннических сайтов. Если считаете, что сайт помечен по ошибке, сообщите о проблеме."}, "dAppBrowserLoadFailedScreenTitle": "Не удалось загрузить", "dAppBrowserLoadFailedScreenDescription": "При загрузке этой страницы произошла ошибка", "dAppBrowserBlocklistScreenIgnoreButton": "Игнорировать предупреждение, всё равно показать", "dAppBrowserActionBookmark": "Закладка", "dAppBrowserActionRemoveBookmark": "Удалить закладку", "dAppBrowserActionRefresh": "Обновить", "dAppBrowserActionShare": "Поделиться", "dAppBrowserActionCloseTab": "Закрыть вкладку", "dAppBrowserActionEndAutoConfirm": "Завершить автоподтверждение", "dAppBrowserActionDisconnectApp": "Отключить приложение", "dAppBrowserActionCloseAllTabs": "Закрыть все вкладки", "dAppBrowserNavigationAddressPlaceholder": "Введите URL-адрес для поиска", "dAppBrowserTabOverviewMore": "Больше", "dAppBrowserTabOverviewAddTab": "Добавить вкладку", "dAppBrowserTabOverviewClose": "Закрыть", "dAppBrowserCloseTab": "Закрыть вкладку", "dAppBrowserClose": "Закрыть", "dAppBrowserTabOverviewAddBookmark": "Добавить закладку", "dAppBrowserTabOverviewRemoveBookmark": "Удалить закладку", "depositAssetListSuggestions": "Предложения", "depositUndefinedToken": "Внести этот токен не удается", "onboardingImportRecoveryPhraseDetails": "Подробности", "onboardingCreateRecoveryPhraseVerifyTitle": "Записали секретную фразу восстановления?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Без секретной фразы восстановления нельзя будет получить доступ к ключу и связанным с ним активам.", "onboardingCreateRecoveryPhraseVerifyYes": "Да", "onboardingCreateRecoveryPhraseErrorTitle": "Ошибка", "onboardingCreateRecoveryPhraseErrorSubtitle": "Не удалось создать аккаунт. Повторите попытку.", "onboardingDoneDescription": "Теперь кошельком можно пользоваться.", "onboardingDoneGetStarted": "Начать", "zeroBalanceHeading": "Приступим!", "zeroBalanceBuyCryptoTitle": "Купите криптовалюту", "zeroBalanceBuyCryptoDescription": "Купите свою первую криптовалюту с помощью дебетовой или кредитной карты.", "zeroBalanceDepositTitle": "Переведите криптовалюту", "zeroBalanceDepositDescription": "Внесите криптовалюту с другого кошелька или биржи.", "onboardingImportAccountsEmptyResult": "Аккаунты не найдены", "onboardingImportAccountsAccountName": "Аккаунт {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Социальный аккаунт", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Мы нашли {{numberOfWallets}} активный аккаунт", "onboardingImportAccountsFoundAccounts_other": "Мы нашли активные аккаунты ({{numberOfWallets}})", "onboardingImportAccountsFoundAccountsNoActivity_one": "Мы нашли {{numberOfWallets}} аккаунт", "onboardingImportAccountsFoundAccountsNoActivity_other": "Мы нашли аккаунты ({{numberOfWallets}})", "onboardingImportRecoveryPhraseLessThanTwelve": "Фраза должна содержать минимум 12 слов.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Фраза должна содержать ровно 12 слов или 24 слова.", "onboardingImportRecoveryPhraseWrongWord": "Неправильные слова: {{ words }}.", "onboardingProtectTitle": "Защитите свой кошелек", "onboardingProtectDescription": "Добавление биометрической защиты гарантирует, что доступ к кошельку будет только у вас.", "onboardingProtectButtonHeadlineDevice": "Устройство", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Отпечаток пальца", "onboardingProtectButtonHeadlinePIN": "PIN-код", "onboardingProtectButtonSubheadline": "Использовать аутентификацию {{ authType }}", "onboardingProtectError": "При аутентификации произошла ошибка. Повторите попытку.", "onboardingProtectBiometryIosError": "Биометрическая аутентификация настроена в Phantom, но отключена в системных настройках. Чтобы включить, откройте «Настройки» > Phantom > Face ID или Touch ID.", "onboardingProtectRemoveAuth": "Отключить аутентификацию", "onboardingProtectRemoveAuthDescription": "Действительно отключить аутентификацию?", "onboardingWelcomeTitle": "Добро пожаловать в Phantom", "onboardingWelcomeDescription": "Чтобы начать, создайте новый кошелек или импортируйте существующий.", "onboardingWelcomeCreateWallet": "Создать новый кошелек", "onboardingWelcomeAlreadyHaveWallet": "У меня уже есть кошелек", "onboardingWelcomeConnectSeedVault": "Подключение хранилища сид-фразы", "onboardingSlide1Title": "Под вашим полным контролем", "onboardingSlide1Description": "Ваш кошелек надежно защищен благодаря доступу по биометрии, системам обнаружения мошенничества и круглосуточной поддержке.", "onboardingSlide2Title": "Лучшее место хранения\nваших NFT", "onboardingSlide2Description": "Управляйте объявлениями, удаляйте спам и оставайтесь в курсе событий с помощью полезных push-уведомлений.", "onboardingSlide3Title": "Гибкие возможности использования токенов", "onboardingSlide3Description": "Храните, обменивайте, стейкайте и получайте средства, не выходя из кошелька. ", "onboardingSlide4Title": "Откройте для себя лучшее из Web3", "onboardingSlide4Description": "Используйте браузер приложений, чтобы находить ведущие приложения и коллекции и подключаться к ним.", "onboardingMultichainSlide5Title": "Один кошелек для всего", "onboardingMultichainSlide5Description": "Оцените все возможности Solana, Ethereum и Polygon в едином удобном интерфейсе.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Оцените все возможности Solana, Ethereum, Polygon и Bitcoin в едином удобном интерфейсе.", "requireAuth": "Требуется аутентификация", "requireAuthImmediately": "Немедленно", "availableToSend": "Доступно к отправке", "sendEnterAmount": "Введите сумму", "sendEditMemo": "Изменить Memo", "sendShowLogs": "Показать журналы ошибок", "sendHideLogs": "Скрыть журналы ошибок", "sendGoBack": "Назад", "sendTransactionSuccess": "Ваши токены отправлены на", "sendInputPlaceholder": "@имя пользователя или адрес", "sendInputPlaceholderV2": "имя пользователя или адрес", "sendPeopleTitle": "Люди", "sendDomainTitle": "Домены", "sendFollowing": "Подписки", "sendRecentlyUsedAddressLabel": "Последнее использование: {{formattedTimestamp}}", "sendRecipientAddress": "Адрес получателя", "sendTokenInterpolated": "Отправить {{tokenSymbol}}", "sendPasteFromClipboard": "Вставка из буфера обмена", "sendScanQR": "Сканировать QR-код", "sendTo": "Куда:", "sendRecipientZeroBalanceWarning": "На этом кошельке нет средств, и он не отображается в истории ваших последних транзакций. Убедитесь, что адрес указан верно.", "sendUnknownAddressWarning": "Это не тот адрес, с которым вы недавно взаимодействовали. Действуйте с осторожностью.", "sendSameAddressWarning": "Это ваш текущий адрес. При отправке взимается комиссия за перевод средств, других изменений баланса не произойдет.", "sendMintAccountWarning": "Это адрес аккаунта для минтинга. Вы не можете отправлять средства на этот адрес, так как это приведет к их безвозвратной потере.", "sendCameraAccess": "Доступ к камере", "sendCameraAccessSubtitle": "Для сканирования QR-кода нужен доступ к камере. Открыть настройки?", "sendSettings": "Настройки", "sendOK": "OK", "invalidQRCode": "Этот QR-код недействителен.", "sendInvalidQRCode": "Этот QR-код не является действительным адресом", "sendInvalidQRCodeSubtitle": "Повторите попытку или используйте другой QR-код.", "sendInvalidQRCodeSplToken": "Недопустимый токен в QR-коде", "sendInvalidQRCodeSplTokenSubtitle": "Этот QR-код содержит токен, которым вы не владеете, или мы не можем его идентифицировать.", "sendScanAddressToSend": "Сканировать адрес {{tokenSymbol}} для отправки средств", "sendScanAddressToSendNoSymbol": "Сканировать адрес для отправки средств", "sendScanAddressToSendCollectible": "Сканировать адрес SOL для отправки предмета коллекционирования", "sendScanAddressToSendCollectibleMultichain": "Сканировать адрес для отправки предмета коллекционирования", "sendSummary": "Сводка", "sendUndefinedToken": "Отправить этот токен не удается", "sendNoTokens": "Токенов нет", "noBuyOptionsAvailableInCountry": "Нет доступных вариантов покупки в вашей стране", "swapAvailableTokenDisclaimer": "Для переноса между сетями доступно ограниченное число токенов", "swapCrossSwapNetworkTooltipTitle": "Обмен между сетями", "swapCrossSwapNetworkTooltipDescription": "При обмене между сетями рекомендуется использовать доступные токены по самой низкой цене и с наибольшей скоростью транзакций.", "settingsAbout": "О платформе Phantom", "settingsShareAppWithFriends": "Пригласите своих друзей", "settingsConfirm": "Да", "settingsMakeSureNoOneIsWatching": "Убедитесь, что никто не видит экран вашего устройства", "settingsManageAccounts": "Управление аккаунтами", "settingsPrompt": "Точно продолжить?", "settingsSelectAvatar": "Выберите аватар", "settingsSelectSecretPhrase": "Выбрать секретную фразу", "settingsShowPrivateKey": "Коснитесь, чтобы показать свой приватный ключ", "settingsShowRecoveryPhrase": "Коснитесь, чтобы показать свою секретную фразу", "settingsSubmitBetaFeedback": "Отправить отзыв о бета-версии", "settingsUpdateAccountNameToast": "Имя аккаунта обновлено", "settingsUpdateAvatarToast": "Аватар обновлен", "settingsUpdateAvatarToastFailure": "Не удалось обновить аватар!", "settingsWalletAddress": "Адрес аккаунта", "settingsWalletAddresses": "Адреса аккаунта", "settingsWalletNamePrimary": "Название аккаунта", "settingsPlaceholderName": "Название", "settingsWalletNameSecondary": "Изменить имя кошелька", "settingsYourAccounts": "Ваши аккаунты", "settingsYourAccountsMultiChain": "Мультиблокчейн", "settingsReportUser": "Пожаловаться на пользователя", "settingsNotifications": "Уведомления", "settingsNotificationPreferences": "Параметры уведомлений", "pushNotificationsPreferencesAllowNotifications": "Разрешить уведомления", "pushNotificationsPreferencesSentTokens": "Отправленные токены", "pushNotificationsPreferencesSentTokensDescription": "Исходящие переводы токенов и NFT", "pushNotificationsPreferencesReceivedTokens": "Полученные токены", "pushNotificationsPreferencesReceivedTokensDescription": "Входящие переводы токенов и NFT", "pushNotificationsPreferencesDexSwap": "Свопы", "pushNotificationsPreferencesDexSwapDescription": "Свопы по признанным приложениям", "pushNotificationsPreferencesOtherBalanceChanges": "Другие изменения баланса", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Другие транзакции с несколькими токенами, влияющие на баланс", "pushNotificationsPreferencesPhantomMarketing": "Обновления от Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Анонсы функций и общие обновления", "pushNotificationsPreferencesDescription": "Данные настройки отвечают за управление push-уведомлениями для этого активного кошелька. Каждый кошелек имеет собственные параметры уведомлений. Чтобы отключить все push-уведомления для Phantom, перейдите в <1>параметры устройства</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Не удалось синхронизировать параметры уведомлений.", "connectSeedVaultConnectSeed": "Подключение Seed", "connectSeedVaultConnectSeedDescription": "Подключите Phantom к хранилищу Seed Vault на вашем телефоне", "connectSeedVaultSelectAnAccount": "Выберите аккаунт", "connectSeedVaultSelectASeed": "Выберите сид-фразу", "connectSeedVaultSelectASeedDescription": "Выберите, какую сид-фразу вы хотите подключить к Phantom", "connectSeedVaultSelectAnAccountDescription": "Выберите, какой аккаунт вы хотели бы настроить с помощью Phantom", "connectSeedVaultNoAccountsFound": "Аккаунты не найдены.", "connectSeedVaultSelectAccounts": "Выберите аккаунты", "connectSeedVaultSelectAccountsDescription": "Выберите, какие аккаунты вы хотели бы настроить с помощью Phantom", "connectSeedVaultCompleteSetup": "Завершить настройку", "connectSeedVaultCompleteSetupDescription": "Всё готово! Исследуйте web3 с Phantom и используйте Seed Vault для подтверждения транзакций", "connectSeedVaultConnectAnotherSeed": "Подключение другого Seed", "connectSeedVaultConnectAllSeedsConnected": "Все сид-фразы подключены", "connectSeedVaultNoSeedsConnected": "Нет подключенных Seed. Нажмите на кнопку ниже, чтобы авторизоваться в Seed Vault.", "connectSeedVaultConnectAccount": "Подключить аккаунт", "connectSeedVaultLoadMore": "Подробнее", "connectSeedVaultNeedPermission": "Нужно разрешение", "connectSeedVaultNeedPermissionDescription": "Перейдите в «Настройки», чтобы предоставить Phantom разрешения для использования Seed Vault.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "Комиссия {{fee}}", "stakeAmount": "Сумма", "stakeAmountBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapTopQuotes": "Лучшие предложения цены {{numQuotes}}", "swapTopQuotesTitle": "Лучшие предложения", "swapProvidersTitle": "Поставщики", "swapProvidersFee": "Комиссия {{fee}}", "swapProvidersTagRecommended": "Лучшая доходность", "swapProvidersTagFastest": "Лучшая скорость", "swapProviderEstimatedTimeHM": "{{hours}} ч. {{minutes}} мин.", "swapProviderEstimatedTimeM": "{{minutes}} мин.", "swapProviderEstimatedTimeS": "{{seconds}} с.", "stakeReview": "Проверить", "stakeReviewAccount": "Аккаунт", "stakeReviewCommissionFee": "Комиссия", "stakeReviewConfirm": "Подтвердить", "stakeReviewValidator": "Валида<PERSON><PERSON>р", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Сбой при конвертации монет в стейкинге", "convertStakeStatusErrorMessage": "Ваши застейканные монеты не удалось конвертировать в {{poolTokenSymbol}}. Попробуйте еще раз.", "convertStakeStatusLoadingTitle": "Конвертация в {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Мы начинаем процесс конвертации застейканных монет {{stakedTokenSymbol}} в {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Конвертация в {{poolTokenSymbol}} завершена!", "convertStakeStatusSuccessMessage": "Получайте дополнительные вознаграждения с помощью JitoSOL <1>здесь.</1>", "convertStakeStatusConvertMore": "Конвертировать больше", "convertStakePendingTitle": "Конвертация стейкинга в {{symbol}}", "convertToJitoSOL": "Конвертировать в JitoSOL", "convertToJitoSOLInfoDescription": "Конвертируйте SOL в JitoSOL, чтобы получать вознаграждения и участвовать в экосистеме Jito.", "convertToJitoSOLInfoTitle": "Конвертация в JitoSOL", "convertStakeBannerTitle": "Конвертируйте позицию по стейкингу в JitoSOL, чтобы увеличить вознаграждение на 15%", "convertStakeQuestBannerTitle": "Конвертируйте застейканные SOL в JitoSOL и получайте вознаграждения. Подробнее.", "liquidStakeConvertInfoTitle": "Конвертация в JitoSOL", "liquidStakeConvertInfoDescription": "Увеличьте свои вознаграждения, конвертировав застейканные SOL в JitoSOL. <1>Подробнее</1>", "liquidStakeConvertInfoFeature1Title": "Зачем стейкать с Jito?", "liquidStakeConvertInfoFeature1Description": "Внесите монеты, чтобы получить JitoSOL, которые будут расти вместе с застейканными монетами. Используйте их в протоколах DeFi для дополнительного заработка. Затем обменяйте JitoSOL на первоначальные монеты + накопленные вознаграждения.", "liquidStakeConvertInfoFeature2Title": "В среднем более высокие вознаграждения", "liquidStakeConvertInfoFeature2Description": "Jito распределяет ваши SOL среди лучших валидаторов с наименьшими комиссиями. Вознаграждения MEV дополнительно увеличивают ваш доход.", "liquidStakeConvertInfoFeature3Title": "Поддержите работу сети Solana", "liquidStakeConvertInfoFeature3Description": "Ликвидный стейкинг обеспечивает безопасность Solana, распределяя застейканные монеты между несколькими валидаторами, что снижает риск, связанный с низким временем работы валидаторов.", "liquidStakeConvertInfoSecondaryButton": "Не сейчас", "liquidStakeStartStaking": "Начать стейкинг", "liquidStakeReviewOrder": "Показать ордер", "convertStakeAccountListPageIneligibleSectionTitle": "Недопустимые стейк-аккаунты", "convertStakeAccountIneligibleBottomSheetTitle": "Недопустимые стейк-аккаунты", "convertStakeAccountListPageErrorTitle": "Не удалось получить стейк-аккаунты", "convertStakeAccountListPageErrorDescription": "К сожалению, что-то пошло не так, и мы не смогли получить стейк-аккаунты", "liquidStakeReviewYouPay": "Вы заплатите", "liquidStakeReviewYouReceive": "Вы получите", "liquidStakeReviewProvider": "Поставщик", "liquidStakeReviewNetworkFee": "Комиссия сети", "liquidStakeReviewPageTitle": "Подтверждение", "liquidStakeReviewConversionFootnote": "При стейкинге токенов Solana в обмен на JitoSOL вы получите немного меньше JitoSOL. <1>Узнать больше</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Пул стейкинга Jito поддерживает большинство активных валидаторов Solana. Вы не сможете конвертировать застейканные SOL из неподдерживаемых валидаторов в JitoSOL. Кроме того, возможность конвертировать только что застейканные SOL в JitoSOL появится примерно через 2 дня.", "selectAValidator": "Выбрать валидатора", "validatorSelectionListTitle": "Выберите валидатора", "validatorSelectionListDescription": "Выберите валидатора, с помощью которого вы будете стейкать свои SOL.", "stakeMethodDescription": "Зарабатывайте на процентах, используя токены SOL для масштабирования Solana. <1>Узнать больше</1>", "stakeMethodRecommended": "Рекомендуется", "stakeMethodEstApy": "Ориентир. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Ликвидный стейкинг", "stakeMethodSelectionLiquidStakingDescription": "Стейкайте SOL, чтобы зарабатывать более высокие вознаграждения, обеспечивать безопасность Solana и получать JitoSOL для дополнительного заработка.", "stakeMethodSelectionNativeStakingTitle": "Нативный стейкинг", "stakeMethodSelectionNativeStakingDescription": "Стейкайте SOL, чтобы получать вознаграждения и содействовать защите Solana.", "liquidStakeMintStakeSOL": "Застейкать SOL", "mintJitoSOLInfoPageTitle": "Представляем ликвидный стейкинг с Jito", "mintJitoSOLFeature1Title": "Зачем стейкать с Jito?", "mintJitoSOLFeature1Description": "Внесите монеты, чтобы получить JitoSOL, которые будут расти вместе с застейканными монетами. Используйте их в протоколах DeFi для дополнительного заработка. Затем обменяйте JitoSOL на первоначальные монеты + накопленные вознаграждения.", "mintJitoSOLFeature2Title": "В среднем более высокие вознаграждения", "mintJitoSOLFeature2Description": "Jito распределяет ваши SOL среди лучших валидаторов с наименьшими комиссиями. Вознаграждения MEV дополнительно увеличивают ваш доход.", "mintJitoSOLFeature3Title": "Поддержите работу сети Solana", "mintJitoSOLFeature3Description": "Ликвидный стейкинг обеспечивает безопасность Solana, распределяя застейканные монеты между несколькими валидаторами, что снижает риск, связанный с низким временем работы валидаторов.", "mintLiquidStakePendingTitle": "Минтинг ликвидного стейкинга", "mintStakeStatusErrorTitle": "Минтинг ликвидного стейкинга не удался", "mintStakeStatusErrorMessage": "Не удалось выполнить минтинг вашего ликвидного стейкинга {{poolTokenSymbol}}. Попробуйте еще раз.", "mintStakeStatusSuccessTitle": "Минтинг ликвидного стейкинга {{poolTokenSymbol}} завершен!", "mintStakeStatusLoadingTitle": "Минтинг ликвидного стейкинга {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "Мы начинаем процесс минтинга вашего ликвидного стейкинга {{poolTokenSymbol}}.", "mintLiquidStakeAmountDescription": "Выберите, сколько SOL вы хотите застейкать с Jito", "mintLiquidStakeAmountProvider": "Поставщик", "mintLiquidStakeAmountApy": "Ориентир. APY", "mintLiquidStakeAmountBestPrice": "Цена", "mintLiquidStakeAmountInsufficientBalance": "Недостаточно средств", "mintLiquidStakeAmountMinRequired": "Нужно застейкать {{amount}} {{symbol}}", "swapTooltipGotIt": "Понятно", "swapTabInsufficientFunds": "Недостаточно средств", "swapNoAssetsFound": "<PERSON><PERSON><PERSON> активов", "swapNoTokensFound": "Токены не найдены", "swapConfirmationTryAgain": "Повторить попытку", "swapConfirmationGoBack": "Назад", "swapNoQuotesFound": "Предложения цены не найдены", "swapNotProviderFound": "Мы не смогли найти поставщика для этого свопа токенов. Попробуйте другой токен.", "swapAvailableOnMainnet": "Эта функция доступна только в Mainnet", "swapNotAvailableEVM": "Свопы пока недоступны для аккаунтов EVM", "swapNotAvailableOnSelectedNetwork": "В выбранной сети свопы недоступны", "singleChainSwapTab": "В сети", "crossChainSwapTab": "Между сетями", "allFilter": "Все", "bridgeRefuelTitle": "Refuel", "bridgeRefuelDescription": "Функция Refuel гарантирует, что вы сможете оплатить транзакции после переноса.", "bridgeRefuelLabelBalance": "Ваши {{symbol}}", "bridgeRefuelLabelReceive": "Вы получите", "bridgeRefuelLabelFee": "Предполагаемая стоимость", "bridgeRefuelDismiss": "Продолжить без Refuel", "bridgeRefuelEnable": "Включить Refuel", "unwrapWrappedSolError": "Сбой разворачивания", "unwrapWrappedSolLoading": "Идет разворачивание...", "unwrapWrappedSolSuccess": "Развернуто", "unwrapWrappedSolViewTransaction": "Показать транзакцию", "dappApprovePopupSignMessage": "Подпишите сообщение", "solanaPayFrom": "Откуда", "solanaPayMessage": "Сообщение", "solanaPayNetworkFee": "Комиссия сети", "solanaPayFree": "Бесплатно", "solanaPayPay": "Оплатить {{item}}", "solanaPayPayNow": "Оплатить сейчас", "solanaPaySending": "Отправление {{item}}", "solanaPayReceiving": "Получение {{item}}", "solanaPayMinting": "Минтинг {{item}}", "solanaPayTransactionProcessing": "Транзакция обрабатывается,\nподождите.", "solanaPaySent": "Отправлено!", "solanaPayReceived": "Получено!", "solanaPayMinted": "Минтинг завершен!", "solanaPaySentNFT": "NFT отправлен!", "solanaPayReceivedNFT": "NFT получен!", "solanaPayTokensSent": "Ваши токены отправлены получателю {{to}}", "solanaPayTokensReceived": "Вы получили новые токены от {{from}}", "solanaPayViewTransaction": "Показать транзакцию", "solanaPayTransactionFailed": "Ошибка транзакции", "solanaPayConfirm": "Подтвердить", "solanaPayTo": "куда", "dappApproveConnectViewAccount": "Показать ваш аккаунт Solana", "deepLinkInvalidLink": "Недопустимая ссылка", "deepLinkInvalidSplTokenSubtitle": "Содерж<PERSON><PERSON> токен, которым вы не владеете, или мы не можем его идентифицировать.", "walletAvatarShowAllAccounts": "Показать все аккаунты", "pushNotificationsGetInstantUpdates": "Получать мгновенные обновления", "pushNotificationsEnablePushNotifications": "Включить push-уведомления о завершенных переводах, свопах и анонсах", "pushNotificationsEnable": "Включить", "pushNotificationsNotNow": "Не сейчас", "onboardingAgreeToTermsOfServiceInterpolated": "Я принимаю <1>Условия использования</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, я сохранил ее", "onboardingCreateNewWallet": "Создать новый кошелек", "onboardingErrorDuplicateSecretRecoveryPhrase": "Эта секретная фраза уже есть в вашем кошельке", "onboardingErrorInvalidSecretRecoveryPhrase": "Неверная секретная фраза восстановления", "onboardingFinished": "Всё готово!", "onboardingImportAccounts": "Импортируйте аккаунты", "onboardingImportImportingAccounts": "Идет импорт аккаунтов…", "onboardingImportImportingFindingAccounts": "Поиск активных аккаунтов", "onboardingImportAccountsLastActive": "Последняя активность: {{formattedTimestamp}}", "onboardingImportAccountsNeverUsed": "Никогда", "onboardingImportAccountsCreateNew": "Новый кошелек", "onboardingImportAccountsDescription": "Выберите аккаунты кошельков для импорта", "onboardingImportReadOnlyAccountDescription": "Добавьте адрес или доменное имя, за которым вы хотите наблюдать. Вы будете иметь доступ только для просмотра и не сможете подписывать транзакции или сообщения.", "onboardingImportSecretRecoveryPhrase": "Импортировать секретную фразу", "onboardingImportViewAccounts": "Показать аккаунты", "onboardingRestoreExistingWallet": "Восстановление существующего кошелька с помощью секретной фразы восстановления из 12 или 24 слов", "onboardingShowUnusedAccounts": "Показать неиспользуемые аккаунты", "onboardingShowMoreAccounts": "Показать больше аккаунтов", "onboardingHideUnusedAccounts": "Скрыть неиспользуемые аккаунты", "onboardingSecretRecoveryPhrase": "Секретная фраза восстановления", "onboardingSelectAccounts": "Выберите аккаунты", "onboardingStoreSecretRecoveryPhraseReminder": "Это единственный способ восстановить аккаунт. Храните ее в надежном месте!", "useTokenMetasForMintsUnknownName": "Неизвестно", "timeUnitMinute": "минута", "timeUnitMinutes": "мин", "timeUnitHour": "час", "timeUnitHours": "ч", "espNFTListWithPrice": "Вы выставили на продажу {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}} в {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Вы выставили на продажу {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Вы выставили на продажу {{NFTDisplayName}} в {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Вы выставили на продажу {{NFTDisplayName}}", "espNFTChangeListPriceWithPrice": "Вы обновили объявление о продаже {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}} в {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Вы обновили объявление о продаже {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Вы обновили объявление о продаже {{NFTDisplayName}} в {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Вы обновили объявление о продаже {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Вы предложили {{priceAmount}}{{priceTokenSymbol}} за {{NFTDisplayName}} в {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Вы предложили {{priceAmount}}{{priceTokenSymbol}} за {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Вы разместили предложение по {{NFTDisplayName}} в {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Вы разместили предложение по {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Новое предложение: {{priceAmount}} {{priceTokenSymbol}} за {{NFTDisplayName}} в{{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Новое предложение: {{priceAmount}} {{priceTokenSymbol}} за {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Новое предложение по {{NFTDisplayName}} в {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Новое предложение по {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Вы отменили предложение: {{priceAmount}} {{priceTokenSymbol}} за {{NFTDisplayName}} в {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Вы отменили предложение: {{priceAmount}} {{priceTokenSymbol}} за {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Вы отменили предложение по {{NFTDisplayName}} в {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Вы отменили предложение по {{NFTDisplayName}}", "espNFTUnlist": "Вы сняли с продажи {{NFTDisplayName}} в {{dAppName}}", "espNFTUnlistWithoutDApp": "Вы сняли с продажи {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Вы купили {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}} в {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Вы купили {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Вы купили {{NFTDisplayName}} в {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Вы купили {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Вы продали {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}} в {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Вы продали {{NFTDisplayName}} за {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Вы продали {{NFTDisplayName}} в {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Вы продали {{NFTDisplayName}}", "espDEXSwap": "Вы обменяли {{downTokensTextFragment}} на {{upTokensTextFragment}} в {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Вы внесли {{downTokensTextFragment}} в пул ликвидности {{poolDisplayName}} в {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Вы обменяли {{downTokensTextFragment}} на {{upTokensTextFragment}} в {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Вы вывели {{upTokensTextFragment}} из пула ликвидности {{poolDisplayName}} в {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Вы обменяли {{downTokensTextFragment}} на {{upTokensTextFragment}} в {{dAppName}}", "espGenericTokenSend": "Вы отправили {{downTokensTextFragment}}", "espGenericTokenReceive": "Вы получили {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Вы обменяли {{downTokensTextFragment}} на {{upTokensTextFragment}}", "espUnknown": "НЕИЗВЕСТНО", "espUnknownNFT": "неизвестный NFT", "espTextFragmentAnd": "и", "externalLinkWarningTitle": "Вы собираетесь покинуть Phantom", "externalLinkWarningDescription": "и открыть {{url}}. Убедитесь, что доверяете этому источнику, прежде чем взаимодействовать с ним.", "shortcutsWarningDescription": "Ярлыки предоставлены {{url}}. Убедитесь, что доверяете этому источнику, прежде чем взаимодействовать с ними.", "lowTpsBanner": "В Solana возникла перегруженность сети", "lowTpsMessageTitle": "Перегруженность сети Solana", "lowTpsMessage": "Из-за высокой перегруженности сети Solana ваши транзакции могут не пройти или быть отложены. Повторите неудачно завершившиеся транзакции.", "solanaSlow": "Сеть Solana работает необычайно медленно", "solanaNetworkTemporarilyDown": "Сеть Solana временно не работает", "waitForNetworkRestart": "Подождите, пока сеть перезапустится. Это никак не повлияет на ваши средства.", "exploreCollectionsCarouselTitle": "Популярное", "exploreDropsCarouselTitle": "Новое", "exploreSortFloor": "Нижний предел", "exploreSortListed": "Выставлено на продажу", "exploreSortVolume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreFetchErrorSubtitle": "Попробуйте еще раз позже.", "exploreFetchErrorTitle": "Не удалось получить.", "exploreTopCollectionsTitle": "Лучшие коллекции NFT", "exploreTopListLess": "Меньше", "exploreTopListMore": "Больше", "exploreSeeMore": "Показать больше", "exploreTrendingTokens": "Популярные токены", "exploreVolumeTokens": "Наиболь<PERSON>ий объем", "explorePriceChangeTokens": "Наиболее быстро растущие активы", "explorePriceTokens": "Токены по цене", "exploreMarketCapTokens": "Лучшие токены", "exploreTrendingSites": "Популярные сайты", "exploreTopSites": "Лучшие сайты", "exploreTrendingCollections": "Популярные коллекции", "exploreTopCollections": "Лучшие коллекции", "collectiblesSearchCollectionsSection": "Коллекции", "collectiblesSearchItemsSection": "Предметы", "collectiblesSearchNrOfItems": "Предметы в количестве {{ nrOfItems }}", "collectiblesSearchPlaceholderText": "Поиск предметов коллекционирования", "collectionPinSuccess": "Коллекция закреплена", "collectionPinFail": "Не удалось закрепить коллекцию", "collectionUnpinSuccess": "Коллекция откреплена", "collectionUnpinFail": "Не удалось открепить коллекцию", "collectionHideSuccess": "Коллекция скрыта", "collectionHideFail": "Не удалось скрыть коллекцию", "collectionUnhideSuccess": "Коллекция больше не скрыта", "collectionUnhideFail": "Не удалось отменить скрытие коллекции", "collectiblesSpamSuccess": "Помечено как спам", "collectiblesSpamFail": "Не удалось пометить как спам", "collectiblesSpamAndHiddenSuccess": "Помечено как спам и скрыто", "collectiblesNotSpamSuccess": "Помечено как не спам", "collectiblesNotSpamFail": "Не удалось пометить как не спам", "collectiblesNotSpamAndUnhiddenSuccess": "Помечено как не спам и отображено", "tokenPageSpamWarning": "Это непроверенный токен. Взаимодействуйте только с теми токенами, которым вы доверяете.", "tokenSpamWarning": "Этот токен был скрыт — Phantom определил его как спам.", "collectibleSpamWarning": "Этот предмет коллекционирования был скрыт — Phantom определил его как спам.", "collectionSpamWarning": "Эти предметы коллекционирования были скрыты — Phantom определил их как спам.", "emojiNoResults": "Эмодзи не найдены", "emojiSearchResults": "Результаты поиска", "emojiSuggested": "Рекомендации", "emojiSmileys": "Смайлики и люди", "emojiAnimals": "Животные и природа", "emojiFood": "Еда и напитки", "emojiTravel": "Путешествия и места", "emojiActivities": "Занятия", "emojiObjects": "Объекты", "emojiSymbols": "Символы", "emojiFlags": "Флаги", "whichExtensionToConnectWith": "С каким расширением вы хотите подключиться?", "configureInSettings": "Данную опцию можно настроить через меню «Настройки» → «Приложение кошелька по умолчанию».", "continueWith": "Продолжить с", "useMetaMask": "Использовать MetaMask", "usePhantom": "Использовать Phantom", "alwaysAsk": "Всегда спрашивать", "dontAskMeAgain": "Больше не спрашивать", "selectWalletSettingDescriptionLine1": "Некоторые приложения могут не предлагать возможность подключения через Phantom.", "selectWalletSettingDescriptionLinePhantom": "В качестве обходного пути при подключении через MetaMask вместо него всегда будет открываться Phantom.", "selectWalletSettingDescriptionLineAlwaysAsk": "В качестве обходного пути при подключении через MetaMask мы будем спрашивать, хотите ли вы вместо этого использовать Phantom.", "selectWalletSettingDescriptionLineMetaMask": "Выбор MetaMask в качестве кошелька по умолчанию запретит этим dapp подключаться к Phantom.", "metaMaskOverride": "Приложение кошелька по умолчанию", "metaMaskOverrideSettingDescriptionLine1": "Для подключения к сайтам, которые не предлагают возможность использования Phantom.", "refreshAndReconnectToast": "Обновите и снова подключитесь, чтобы применить изменения", "autoConfirmUnavailable": "Недоступно", "autoConfirmReasonDappNotWhitelisted": "Недоступно, потому что контракт, с которого оно пришло, не входит в список разрешенных для этого приложения.", "autoConfirmReasonSessionNotActive": "Недоступно, так как нет активной сессии с автоподтверждением. Включите эту функцию ниже.", "autoConfirmReasonRateLimited": "Недоступно, потому что используемое вами децентрализованное приложение отправляет слишком много запросов.", "autoConfirmReasonUnsupportedNetwork": "Недоступно, так как эта сеть еще не поддерживает такую функцию.", "autoConfirmReasonSimulationFailed": "Недоступно, так как мы не можем гарантировать безопасность.", "autoConfirmReasonTabNotFocused": "Недоступно, так как вкладка домена, на которой вы пытаетесь выполнить автоподтверждение, неактивна.", "autoConfirmReasonNotUnlocked": "Недоступно, так как кошелек не был разблокирован.", "rpcErrorUnauthorizedWrongAccount": "Транзакция с адреса не соответствует выбранному адресу аккаунта.", "rpcErrorUnauthorizedUnknownSource": "Не удалось определить источник запроса RPC.", "transactionsDisabledTitle": "Транзакции отключены", "transactionsDisabledMessage": "Ваш адрес не позволяет совершать транзакции с использованием Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Активно", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL-адрес скопирован в буфер обмена", "notEnoughSolScanTransactionWarning": "Эта транзакция может завершиться неудачей из-за недостаточного количества SOL на вашем аккаунте. Добавьте больше SOL в аккаунт и повторите попытку.", "transactionRevertedWarning": "Эта транзакция была отменена во время моделирования. При отправке средства могут быть утеряны.", "slippageToleranceExceeded": "Эта транзакция была отменена во время моделирования. Превышен предел слипеджа.", "simulationWarningKnownMalicious": "Мы считаем, что это мошеннический аккаунт. Одобрение может привести к безвозвратной потере средств.", "simulationWarningPoisonedAddress": "Этот адрес подозрительно похож на адрес, на который вы недавно отправляли средства. Подтвердите, что это правильный адрес, чтобы избежать потери средств из-за мошенничества.", "simulationWarningInteractingWithAccountWithoutActivity": "Это аккаунт без предыдущей активности. Отправка средств на несуществующий аккаунт может привести к их безвозвратной потере", "quests": "Квесты", "questsClaimInProgress": "Идет получение", "questsVerifyingCompletion": "Проверка завершения квеста", "questsClaimError": "Ошибка получения награды", "questsClaimErrorDescription": "При получении награды произошла ошибка. Повторите попытку позже.", "questsBadgeMobileOnly": "Только мобильный", "questsBadgeExtensionOnly": "Только расширение", "questsExplainerSheetButtonLabel": "Понятно", "questsNoQuestsAvailable": "Квестов нет", "questsNoQuestsAvailableDescription": "Сейчас нет доступных квестов. Мы сообщим вам, как только появятся новые.", "exploreLearn": "Узнать больше", "exploreSites": "Сайты", "exploreTokens": "Токены", "exploreQuests": "Квесты", "exploreCollections": "Коллекции", "exploreFilterByall_networks": "Все сети", "exploreSortByrank": "Популярное", "exploreSortBytrending": "Популярное", "exploreSortByprice": "Цена", "exploreSortByprice-change": "Изменение цены", "exploreSortBytop": "Топ", "exploreSortByvolume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreSortBygainers": "Гейнеры", "exploreSortBylosers": "Лузеры", "exploreSortBymarket-cap": "Верхний предел по рынку", "exploreSortBymarket_cap": "Верхний предел по рынку", "exploreTimeFrame1h": "1 ч.", "exploreTimeFrame24h": "24 ч.", "exploreTimeFrame7d": "7 дн.", "exploreTimeFrame30d": "30 дн.", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Предметы коллекционирования", "exploreCategoryMarketplace": "Торговая платформа", "exploreCategoryGaming": "Игры", "exploreCategoryBridges": "Переносы", "exploreCategoryOther": "Другое", "exploreCategorySocial": "Социальное", "exploreCategoryCommunity": "Общество", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Стейкинг", "exploreCategoryArt": "Искусство", "exploreCategoryTools": "Инструменты", "exploreCategoryDeveloperTools": "Инструменты разработчика", "exploreCategoryHackathon": "Х<PERSON><PERSON><PERSON><PERSON><PERSON>н", "exploreCategoryNFTStaking": "Стейкинг NFT", "exploreCategoryExplorer": "Обозреватель", "exploreCategoryInscriptions": "Надписи", "exploreCategoryBridge": "Перенос", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Инструмент для проверки Airdrop", "exploreCategoryPoints": "Очки", "exploreCategoryQuests": "Квесты", "exploreCategoryShop": "Мага<PERSON>ин", "exploreCategoryProtocol": "Протокол", "exploreCategoryNamingService": "Служба именования", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Трекер портфеля", "exploreCategoryFitness": "Фит<PERSON><PERSON>с", "exploreCategoryDePIN": "DePIN", "exploreVolume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreFloor": "Нижний предел", "exploreCap": "Верхний предел по рынку", "exploreToken": "Токен", "explorePrice": "Цена", "explore24hVolume": "24-часовой объем", "exploreErrorButtonText": "Повторить попытку", "exploreErrorDescription": "При попытке загрузить содержание обозревателя произошла ошибка. Обновите страницу и попробуйте снова", "exploreErrorTitle": "Не удалось загрузить содержание обозревателя", "exploreNetworkError": "Произошла ошибка сети. Повторите попытку позже.", "exploreTokensLegalDisclaimer": "Списки токенов формируются на основе рыночных данных от различных сторонних поставщиков, включая CoinGecko, Birdeye и Jupiter. Показатели основаны на предыдущем 24-часовом периоде. Прошлые результаты не гарантируют будущей эффективности.", "swapperTokensLegalDisclaimer": "Списки популярных токенов формируются на основе рыночных данных от различных сторонних поставщиков, включая CoinGecko, Birdeye и Jupiter, а также на основе токенов, наиболее часто меняемых пользователями Phantom через Swapper в указанный период. Прошлые результаты не гарантируют будущей эффективности.", "exploreLearnErrorTitle": "Не удалось загрузить учебный контент", "exploreLearnErrorDescription": "При попытке загрузить учебный контент произошла ошибка. Обновите страницу и попробуйте снова", "exploreShowMore": "Показать больше", "exploreShowLess": "Показать меньше", "exploreVisitSite": "Посетить сайт", "dappBrowserSearchScreenVisitSite": "Открыть сайт", "dappBrowserSearchScreenSearchWithGoogle": "Поиск с Google", "dappBrowserSearchScreenSearchLinkYouCopied": "Скопированная ссылка", "dappBrowserExtSearchPlaceholder": "Поиск сайтов, токенов", "dappBrowserSearchNoAppsTokens": "Приложения или токены не найдены", "dappBrowserTabsLimitExceededScreenTitle": "Закрыть старые вкладки?", "dappBrowserTabsLimitExceededScreenDescription": "У вас открыто столько вкладок: {{tabsCount}}. Чтобы открыть еще, нужно закрыть несколько вкладок.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "Закрыть все вкладки", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: нет такого домена", "dappBrowserTabErrorHttp": "Заблокировано, используйте HTTPS", "dappBrowserTabError401Unauthorized": "Ошибка 401: аутентификация не выполнена", "dappBrowserTabError501UnhandledRequest": "Ошибка 501: невыполненный запрос", "dappBrowserTabErrorTimeout": "Истек срок ожидания: сервер слишком долго не отвечал", "dappBrowserTabErrorInvalidResponse": "Недопустимый ответ", "dappBrowserTabErrorEmptyResponse": "Пустой ответ", "dappBrowserTabErrorGeneric": "Произошла ошибка", "localizedErrorUnknownError": "Что-то пошло не так. Повторите попытку позже.", "localizedErrorUnsupportedCountry": "Извините, ваша страна пока не поддерживается.", "localizedErrorTokensNotLoading": "Возникла проблема с загрузкой ваших токенов. Попробуйте еще раз.", "localizedErrorSwapperNoQuotes": "Свопы недоступны из-за неподдерживаемой пары, низкой ликвидности или малого количества. Попробуйте изменить токен или сумму.", "localizedErrorSwapperRefuelNoQuotes": "Предложения цены не найдены. Попробуйте с другим токеном, суммой или отключите Refuel.", "localizedErrorInsufficientSellAmount": "Слишком мало токенов. Увеличьте значение, чтобы провести обмен между разными блокчейнами.", "localizedErrorCrossChainUnavailable": "В настоящее время свопы между разными блокчейнами недоступны, повторите попытку позже.", "localizedErrorTokenNotTradable": "Один из выбранных токенов недоступен для торговли. Выберите другой токен.", "localizedErrorCollectibleLocked": "Аккаунт токена заблокирован.", "localizedErrorCollectibleListed": "Аккаунт токена выставлен на продажу.", "spamActivityAction": "Показать скрытые элементы", "spamActivityTitle": "Скрытая активность", "spamActivityWarning": "Эта транзакция была скрыта, Phantom определили ее как похожую на спам.", "appAuthenticationFailed": "Не удалось выполнить аутентификацию", "appAuthenticationFailedDescription": "При попытке аутентификации возникла проблема. Попробуйте еще раз.", "partialErrorBalanceChainName": "У нас возникли проблемы с обновлением балансов {{chainName}}. Ваши средства в безопасности.", "partialErrorGeneric": "У нас возникли проблемы с обновлением сетей, поэтому отдельные балансы токенов и цены могут быть устаревшими. Ваши средства в безопасности.", "partialErrorTokenDetail": "У нас возникли проблемы с обновлением баланса токенов. Ваши средства в безопасности.", "partialErrorTokenPrices": "У нас возникли проблемы с обновлением цен на токены. Ваши средства в безопасности.", "partialErrorTokensTrimmed": "У нас возникли проблемы с отображением всех токенов в вашем портфеле. Ваши средства в безопасности.", "publicFungibleDetailAbout": "Подробнее", "publicFungibleDetailYourBalance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailInfo": "Информация", "publicFungibleDetailShowMore": "Показать больше", "publicFungibleDetailShowLess": "Показать меньше", "publicFungibleDetailPerformance": "Показатели за 24 ч", "publicFungibleDetailSecurity": "Безопасность", "publicFungibleDetailMarketCap": "Верхний предел по рынку", "publicFungibleDetailTotalSupply": "Общее предложение", "publicFungibleDetailCirculatingSupply": "Оборотное предложение", "publicFungibleDetailMaxSupply": "Макс. предложение", "publicFungibleDetailHolders": "Держатели", "publicFungibleDetailVolume": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailTrades": "Сделки", "publicFungibleDetailTraders": "Трейдеры", "publicFungibleDetailUniqueWallets": "Уникальные кошельки", "publicFungibleDetailTop10Holders": "Топ-10 держателей", "publicFungibleDetailTop10HoldersTooltip": "Указывает процент от текущего общего предложения, которым владеют 10 крупнейших держателей токена. Это показатель того, насколько легко можно манипулировать ценой.", "publicFungibleDetailMintable": "Возможен минтинг", "publicFungibleDetailMintableTooltip": "Количество токенов может быть увеличено владельцем контракта, если возможен минтинг токена.", "publicFungibleDetailMutableInfo": "Изменяемая информация", "publicFungibleDetailMutableInfoTooltip": "Если информация о токене, такая как название, логотип и адрес сайта, является изменяемой, ее может изменить владелец контракта.", "publicFungibleDetailOwnershipRenounced": "Отказ от владения", "publicFungibleDetailOwnershipRenouncedTooltip": "Если отказаться от владения токенами, никто не сможет выполнять такие функции, как минтинг новых токенов.", "publicFungibleDetailUpdateAuthority": "Полномочия на обновление", "publicFungibleDetailUpdateAuthorityTooltip": "Полномочия на обновление принадлежат только адресу кошелька владельца, и только он может изменить информацию о токене, если токен является изменяемым.", "publicFungibleDetailFreezeAuthority": "Полномочия заморозки", "publicFungibleDetailFreezeAuthorityTooltip": "Полномочия заморозки — это адрес кошелька, который обладает правом предотвращать перевод средств.", "publicFungibleUnverifiedToken": "Это непроверенный токен. Взаимодействуйте только с теми токенами, которым вы доверяете.", "publicFungibleDetailSwap": "Своп {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Своп {{tokenSymbol}} через приложение Phantom", "publicFungibleDetailLinkCopied": "Скопировано в буфер обмена", "publicFungibleDetailContract": "Контракт", "publicFungibleDetailMint": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "Действие", "unifiedTokenDetailSeeMoreTransactionActivity": "Показать больше", "unifiedTokenDetailTransactionActivityError": "Не удалось загрузить недавние действия", "additionalNetworksTitle": "Дополнительные сети", "copyAddressRowAdditionalNetworks": "Дополнительные сети", "copyAddressRowAdditionalNetworksHeader": "Следующие сети используют тот же адрес, что и Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Вы можете безопасно использовать свой адрес Ethereum для отправки и получения активов в любой из этих сетей.", "cpeUnknownError": "Неизвестная ошибка", "cpeUnknownInstructionError": "Неизвестная ошибка инструкции", "cpeAccountFrozen": "Аккаунт заморожен", "cpeAssetFrozen": "Актив заморожен", "cpeInsufficientFunds": "Недостаточно средств", "cpeInvalidAuthority": "Недействительные полномочия", "cpeBalanceBelowRent": "Баланс ниже порога освобождения от арендной платы", "cpeNotApprovedForConfidentialTransfers": "Аккаунт не одобрен для конфиденциальных переводов", "cpeNotAcceptingDepositsOrTransfers": "Аккаунт не принимает депозиты или переводы", "cpeNoMemoButRequired": "В предыдущей инструкции отсутствует Memo (примечание); оно необходимо для получения перевода получателем", "cpeTransferDisabledForMint": "Перевод для этого токена недоступен", "cpeDepositAmountExceedsLimit": "Внесенная сумма превышает максимальный лимит", "cpeInsufficientFundsForRent": "Недостаточно средств для арендной платы", "reportIssueScreenTitle": "Сообщите о проблеме", "publicFungibleReportIssuePrompt": "О какой проблеме с токеном {{tokenName}} вы хотите сообщить?", "publicFungibleReportIssueIncorrectInformation": "Неверная информация", "publicFungibleReportIssuePriceStale": "Цена не обновляется", "publicFungibleReportIssuePriceMissing": "Цена отсутствует", "publicFungibleReportIssuePerformanceIncorrect": "Показатели за 24 ч неправильные", "publicFungibleReportIssueLinkBroken": "Социальные ссылки недоступны", "publicFungibleDetailErrorLoading": "Данные токена недоступны", "reportUserPrompt": "О какой проблеме с пользователем {{username}} вы хотите сообщить?", "reportUserOptionAbuseAndHarrassmentTitle": "Оскорбления и приставания", "reportUserOptionAbuseAndHarrassmentDescription": "Целенаправленное преследование, подстрекательство к преследованию, угрозы применения насилия, ненавистнический контент и ссылки", "reportUserOptionPrivacyAndImpersonationTitle": "Конфиденциальность и выдача себя за другое лицо", "reportUserOptionPrivacyAndImpersonationDescription": "Распространение или угроза раскрытия личной информации, выдача себя за другого человека", "reportUserOptionSpamTitle": "Спам", "reportUserOptionSpamDescription": "Поддельный аккаунт, мошенничество, вредоносные ссылки", "reportUserSuccess": "Жалоба на пользователя отправлена.", "settingsClaimUsernameTitle": "Создайте имя пользователя", "settingsClaimUsernameDescription": "Уникальный идентификатор, как и ваш кошелек", "settingsClaimUsernameValueProp1": "Упрощенная идентификация", "settingsClaimUsernameValueProp1Description": "Забудьте про длинные и сложные адреса — теперь у вас есть способ простой идентификации", "settingsClaimUsernameValueProp2": "Быстрее и удобнее", "settingsClaimUsernameValueProp2Description": "Легко отправляйте и получайте криптовалюту, входите в свой кошелек и общайтесь с друзьями", "settingsClaimUsernameValueProp3": "Синхронизация на всех устройствах", "settingsClaimUsernameValueProp3Description": "Подключите любой аккаунт к своему имени пользователя, и он синхронизуется на всех ваших устройствах", "settingsClaimUsernameHelperText": "Ваше уникальное имя для аккаунта Phantom", "settingsClaimUsernameValidationDefault": "Имя пользователя нельзя изменить в дальнейшем", "settingsClaimUsernameValidationAvailable": "Имя пользователя доступно", "settingsClaimUsernameValidationUnavailable": "Имя пользователя недоступно", "settingsClaimUsernameValidationServerError": "Не удалось проверить, доступно ли имя пользователя, повторите попытку позже", "settingsClaimUsernameValidationErrorLine1": "Недопустимое имя пользователя.", "settingsClaimUsernameValidationErrorLine2": "В имени пользователя должно быть столько символов: от {{minChar}} до {{maxChar}}. Допустимы только буквенно-цифровые символы.", "settingsClaimUsernameValidationLoading": "Проверяем доступность имени пользователя...", "settingsClaimUsernameSaveAndContinue": "Сохранить и продолжить", "settingsClaimUsernameChooseAvatarTitle": "Выберите аватар", "settingsClaimUsernameAnonymousAuthTitle": "Анонимная аутентификация", "settingsClaimUsernameAnonymousAuthDescription": "Входите в свой аккаунт Phantom анонимно через подпись", "settingsClaimUsernameAnonymousAuthBadge": "Узнайте, как это работает", "settingsClaimUsernameLinkWalletsTitle": "Привяжите свои кошельки", "settingsClaimUsernameLinkWalletsDescription": "Выберите кошельки, которые будут отображаться на других устройствах с вашим именем пользователя", "settingsClaimUsernameLinkWalletsBadge": "Не видно публично", "settingsClaimUsernameConnectAccountsTitle": "Подключите аккаунты", "settingsClaimUsernameConnectAccountsHelperText": "Каждый адрес блокчейна будет связан с вашим именем пользователя. Адреса можно будет изменить позже.", "settingsClaimUsernameContinue": "Продолжить", "settingsClaimUsernameCreateUsername": "Создать имя пользователя", "settingsClaimUsernameCreating": "Создание имени пользователя...", "settingsClaimUsernameSuccess": "Имя пользователя создано!", "settingsClaimUsernameError": "При создании имени пользователя произошла ошибка", "settingsClaimUsernameTryAgain": "Повторить попытку", "settingsClaimUsernameSuccessHelperText": "Теперь вы можете использовать новое имя пользователя во всех кошельках Phantom", "settingsClaimUsernameSettingsSyncedTitle": "Синхронизируйте настройки", "settingsClaimUsernameSettingsSyncedHelperText": "Забудьте про длинные и сложные адреса — теперь у вас есть способ простой идентификации", "settingsClaimUsernameSendToUsernameTitle": "Отправляйте на имя пользователя", "settingsClaimUsernameSendToUsernameHelperText": "Легко отправляйте и получайте криптовалюту, входите в свой кошелек и общайтесь с друзьями", "settingsClaimUsernameManageAddressesTitle": "Публичные адреса", "settingsClaimUsernameManageAddressesHelperText": "Все токены или предметы коллекционирования, отправленные на ваше имя пользователя, будут отправлены на эти адреса", "settingsClaimUsernameManageAddressesBadge": "Видно публично", "settingsClaimUsernameEditAddressesTitle": "Управление публичными адресами", "settingsClaimUsernameEditAddressesHelperText": "Все токены или предметы коллекционирования, отправленные на ваше имя пользователя, будут отправлены на эти адреса. Выберите один адрес для каждого блокчейна.", "settingsClaimUsernameEditAddressesError": "Для каждой сети допускается только один адрес.", "settingsClaimUsernameEditAddressesEditAddress": "Изменить адреса", "settingsClaimUsernameNoAddressesSaved": "Публичные адреса не сохранены", "settingsClaimUsernameSave": "Сохранить", "settingsClaimUsernameDone": "Готово", "settingsClaimUsernameWatching": "Отслеживать", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} аккаунт(-а, -ов)", "settingsClaimUsernameNoOfAccountsSingular": "1 аккаунт", "settingsClaimUsernameEmptyAccounts": "Нет аккау<PERSON>тов", "settingsClaimUsernameSettingTitle": "Создайте свое @имя пользователя", "settingsClaimUsernameSettingDescription": "Уникальный идентификатор для вашего кошелька", "settingsManageUserProfileAbout": "Подробнее", "settingsManageUserProfileAboutUsername": "Имя пользователя", "settingsManageUserProfileAboutBio": "Описание", "settingsManageUserProfileTitle": "Управление профилем", "settingsManageUserProfileManage": "Управлять", "settingsManageUserProfileAuthFactors": "Факторы аутентификации", "settingsManageUserProfileAuthFactorsDescription": "Выберите, какие сид-фразы или приватные ключи будут использоваться для входа в ваш аккаунт Phantom.", "settingsManageUserProfileUpdateAuthFactorsToast": "Факторы аутентификации обновлены!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Не удалось обновить факторы аутентификации!", "settingsManageUserProfileBiography": "Изменить описание", "settingsManageUserProfileBiographyDescription": "Добавьте краткое описание в свой профиль", "settingsManageUserProfileUpdateBiographyToast": "Описание обновлено!", "settingsManageUserProfileUpdateBiographyToastFailure": "Произошел сбой. Попробуйте еще раз", "settingsManageUserProfileBiographyNoUrlMessage": "Удалите все URL-адреса из раздела описания", "settingsManageUserProfileLinkedWallets": "Связанные кошельки", "settingsManageUserProfileLinkedWalletsDescription": "Выберите кошельки, которые будут отображаться на других устройствах при входе в аккаунт Phantom.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Связанные кошельки обновлены!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Не удалось изменить связанные кошельки!", "settingsManageUserProfilePrivacy": "Конфиденциальность", "settingsManageUserProfileUpdatePrivacyStateToast": "Настройки конфиденциальности изменены!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Не удалось изменить настройки конфиденциальности!", "settingsManageUserProfilePublicAddresses": "Публичные адреса", "settingsManageUserProfileUpdatePublicAddressToast": "Публичный адрес изменен!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Не удалось изменить публичный адрес!", "settingsManageUserProfilePrivacyStatePublic": "Публичный", "settingsManageUserProfilePrivacyStatePublicDescription": "Ваш профиль и публичные адреса видны и доступны для поиска всем желающим", "settingsManageUserProfilePrivacyStatePrivate": "Приватный", "settingsManageUserProfilePrivacyStatePrivateDescription": "Ваш профиль доступен для поиска, но другие пользователи должны запросить разрешение, чтобы увидеть ваш профиль и публичные адреса", "settingsManageUserProfilePrivacyStateInvisible": "Невидимый", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Ваш профиль и публичные адреса скрыты и недоступны для поиска", "settingsDownloadPhantom": "Скачать Phantom", "settingsLogOut": "Выйти", "seedlessAddAWalletPrimaryText": "Добавить кошелек", "seedlessAddAWalletSecondaryText": "Авторизуйтесь или импортируйте уже существующий кошелек ", "seedlessAddSeedlessWalletPrimaryText": "Добавить кошелек без сид-фразы", "seedlessAddSeedlessWalletSecondaryText": "Использовать Apple ID, Google или адрес эл. почты", "seedlessCreateNewWalletPrimaryText": "Создать новый кошелек?", "seedlessCreateNewWalletSecondaryText": "На этот адрес электронной почты не зарегистрирован кошелек. Хотите его создать?", "seedlessCreateNewWalletButtonText": "Создать кошелек", "seedlessCreateNewWalletNoBundlePrimaryText": "Кошелек не найден", "seedlessCreateNewWalletNoBundleSecondaryText": "На этот адрес электронной почты не зарегистрирован кошелек", "seedlessCreateNewWalletNoBundleButtonText": "Назад", "seedlessEmailOptionsPrimaryText": "Выберите свой адрес эл. почты", "seedlessEmailOptionsSecondaryText": "Добавьте кошелек с помощью аккаунта Apple или Google ", "seedlessEmailOptionsButtonText": "Продолжить с адресом эл. почты", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Создать кошелек с помощью Apple ID", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Создать кошелек с помощью адреса эл. почты Google", "seedlessAlreadyExistsPrimaryText": "Аккаунт уже существует", "seedlessAlreadyExistsSecondaryText": "Для этого адреса эл. почты уже создан кошелек, хотите войти в него вместо этого?", "seedlessSignUpWithAppleButtonText": "Зарегистрироваться через Apple", "seedlessContinueWithAppleButtonText": "Продолжить с Apple", "seedlessSignUpWithGoogleButtonText": "Зарегистрироваться через Google", "seedlessContinueWithGoogleButtonText": "Продолжить с Google", "seedlessCreateAPinPrimaryText": "Создайте PIN-код", "seedlessCreateAPinSecondaryText": "Он используется для защиты вашего кошелька на всех устройствах. <1>Восстановить его невозможно.</1>", "seedlessContinueText": "Продолжить", "seedlessConfirmPinPrimaryText": "Подтвердите PIN-код", "seedlessConfirmPinSecondaryText": "Если вы забудете этот PIN-код, вы не сможете восстановить свой кошелек на новом устройстве.", "seedlessConfirmPinButtonText": "Создать PIN-код", "seedlessConfirmPinError": "Неверный PIN-код. Попробуйте еще раз", "seedlessAccountsImportedPrimaryText": "Аккаунты импортированы", "seedlessAccountsImportedSecondaryText": "Эти аккаунты будут автоматически импортированы в ваш кошелек", "seedlessPreviouslyImportedTag": "Импортированы ранее", "seedlessEnterPinPrimaryText": "Введите PIN-код", "seedlessEnterPinInvalidPinError": "Введен неправильный PIN-код. Допускаются только 4-значные номера", "seedlessEnterPinNumTriesLeft": "Осталось попыток: {{numTries}}.", "seedlessEnterPinCooldown": "Попробуйте снова через {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN-код должен состоять ровно из 4 цифр", "seedlessEnterPinMatch": "PIN-коды совпадают", "seedlessDoneText": "Готово", "seedlessEnterPinToSign": "Введите PIN-код, чтобы подписать эту транзакцию", "seedlessSigning": "Подписание", "seedlessCreateSeed": "Создайте кошелек с сид-фразой", "seedlessImportOptions": "Другие варианты импорта", "seedlessImportPrimaryText": "Варианты импорта", "seedlessImportSecondaryText": "Импортируйте свой кошелек с помощью сид-фразы, приватного ключа или аппаратного кошелька", "seedlessImportSeedPhrase": "Импортировать сид-фразу", "seedlessImportPrivateKey": "Импортировать приватный ключ", "seedlessConnectHardwareWallet": "Подключить аппаратный кошелек", "seedlessTryAgain": "Повторить попытку", "seedlessCreatingWalletPrimaryText": "Создание кошелька", "seedlessCreatingWalletSecondaryText": "Добавление социального кошелька", "seedlessLoadingWalletPrimaryText": "Загрузка кошелька", "seedlessLoadingWalletSecondaryText": "Импорт и просмотр связанных кошельков", "seedlessLoadingWalletErrorPrimaryText": "Не удалось загрузить кошелек", "seedlessCreatingWalletErrorPrimaryText": "Не удалось создать кошелек", "seedlessErrorSecondaryText": "Попробуйте снова", "seedlessAuthAlreadyExistsErrorText": "Указанный адрес эл. почты уже закреплен за другим аккаунтом Phantom", "seedlessAuthUnknownErrorText": "Произошла неизвестная ошибка, повторите еще раз позже", "seedlessAuthUnknownErrorTextRefresh": "Неизвестная ошибка, попробуйте позже. Обновите страницу и повторите попытку.", "seedlessAuthErrorCloseWindow": "Закрыть окно", "seedlessWalletExistsErrorPrimaryText": "Социальный кошелек уже есть на вашем устройстве", "seedlessWalletExistsErrorSecondaryText": "Закройте этот экран или вернитесь на предыдущий", "seedlessValueProp1PrimaryText": "Простая установка", "seedlessValueProp1SecondaryText": "Создайте кошелек с помощью аккаунта Google или Apple и начните осваивать web3 с легкостью", "seedlessValueProp2PrimaryText": "Повышенная безопасность", "seedlessValueProp2SecondaryText": "Ваш кошелек надежно защищен за счет использования децентрализованного подхода — информация о нем распределена между несколькими независимыми элементами безопасности", "seedlessValueProp3PrimaryText": "Легкое восстановление", "seedlessValueProp3SecondaryText": "Восстановите доступ к своему кошельку с помощью аккаунта Google или Apple и 4-значного PIN-кода", "seedlessLoggingIn": "Вход в...", "seedlessSignUpOrLogin": "Зарегистрироваться или войти", "seedlessContinueByEnteringYourEmail": "Продолжить, введя адрес эл. почты", "seedless": "Без сид-фразы", "seed": "Сид-фразы", "seedlessVerifyPinPrimaryText": "Подтвердите PIN-код", "seedlessVerifyPinSecondaryText": "Введите PIN-код, чтобы продолжить", "seedlessVerifyPinVerifyButtonText": "Подтвердить", "seedlessVerifyPinForgotButtonText": "Забыли?", "seedlessPinConfirmButtonText": "Подтвердить", "seedlessVerifyToastPrimaryText": "Подтвердите PIN-код", "seedlessVerifyToastSecondaryText": "Время от времени мы будем просить вас подтвердить PIN-код, чтобы вы его запомнили. Если вы его забудете, то не сможете восстановить свой кошелек.", "seedlessVerifyToastSuccessText": "PIN-код подтвержден!", "seedlessForgotPinPrimaryText": "Сбросьте PIN-код через другое устройство", "seedlessForgotPinSecondaryText": "В целях безопасности сбросить PIN-код можно только на других устройствах, где вы вошли в свой кошелек", "seedlessForgotPinInstruction1PrimaryText": "Перейдите на другое устройство", "seedlessForgotPinInstruction1SecondaryText": "Перейдите на другое устройство, где вы вошли в аккаунт Phantom с помощью адреса ел. почты", "seedlessForgotPinInstruction2PrimaryText": "Перейдите в настройки", "seedlessForgotPinInstruction2SecondaryText": "В настройках откройте раздел «Безопасность и конфиденциальность» и выберите «Сбросить PIN-код»", "seedlessForgotPinInstruction3PrimaryText": "Задайте новый PIN-код", "seedlessForgotPinInstruction3SecondaryText": "Задав новый PIN-код, вы сможете войти в свой кошелек на этом устройстве", "seedlessForgotPinButtonText": "Я выполнил(-а) эти действия", "seedlessResetPinPrimaryText": "Сбросить PIN-код", "seedlessResetPinSecondaryText": "Введите новый PIN-код, который вы сможете запомнить. Он необходим для обеспечения безопасности кошелька на всех ваших устройствах.", "seedlessResetPinSuccessText": "Ваш PIN-код обновлен!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Создавая кошелек, вы соглашаетесь с нашими <1>Условиями использования</1> и <5>Политикой конфиденциальности</5>", "pageNotFound": "Страница не найдена", "pageNotFoundDescription": "Мы на связи! Этой страницы нет или она была перемещена.", "webTokenPagesLegalDisclaimer": "Информация о ценах указана исключительно в ознакомительных целях и не является финансовым советом. Рыночные данные предоставляются третьими лицами, и Phantom не делает никаких заявлений относительно точности этой информации.", "signUpOrLogin": "Зарегистрируйтесь или войдите", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Создавая аккаунт, вы соглашаетесь с нашими <1>Условиями использования</1> и <5> Политикой конфиденциальности</5>", "feedNoActivity": "Пока нет действий", "followRequests": "Запросы на подписку", "following": "Подписки", "followers": "Подписчики", "follower": "Подписчик", "joined": "Участвует", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "Нет подпис<PERSON>иков", "noFollowing": "Нет подписок", "noUsersFound": "Пользователи не найдены", "viewProfile": "Просмотреть профиль", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}