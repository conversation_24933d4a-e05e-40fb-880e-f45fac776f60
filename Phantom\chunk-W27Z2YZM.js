import{a as u}from"./chunk-BTKBODVJ.js";import{f as i,h as n,n as r}from"./chunk-3KENBVE7.js";n();r();var e=i(u()),p=()=>e.default.runtime.getURL("").replace(/\/$/,"");var a=t=>e.default.tabs.create(t),d=t=>e.default.windows.create(t);var w=(t,o,s)=>e.default.windows.update(t,{left:o,top:s}),m=()=>e.default.windows.getLastFocused(),l=()=>e.default.windows.getCurrent(),x=t=>e.default.windows.remove(t);var y=async()=>await e.default.permissions.contains({origins:["http://*/*","https://*/*"]}),b=async()=>await e.default.permissions.request({origins:["http://*/*","https://*/*"]}),g=async()=>await e.default.permissions.remove({origins:["http://*/*","https://*/*"]});var W=()=>e.default.windows.getAll({windowTypes:["popup"],populate:!0});var A=()=>e.default.tabs.getCurrent();var h=t=>e.default.tabs.remove(t);export{p as a,a as b,d as c,w as d,m as e,l as f,x as g,y as h,b as i,g as j,W as k,A as l,h as m};
//# sourceMappingURL=chunk-W27Z2YZM.js.map
