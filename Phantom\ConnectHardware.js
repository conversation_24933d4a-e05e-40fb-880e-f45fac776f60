import{a as b}from"./chunk-2JNGRO7L.js";import{a as h,b as c,c as A}from"./chunk-JLLUQF3V.js";import{b as w}from"./chunk-QSVSNR6K.js";import{h as v,m as F}from"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import{a as S}from"./chunk-26OZJBRY.js";import"./chunk-XJTFMD4C.js";import{d as y}from"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import{a as g}from"./chunk-VQVTLSDS.js";import"./chunk-OKP6DFCI.js";import{h as u,l as P}from"./chunk-WIQ4WVKX.js";import{a as E,c as f}from"./chunk-AVT3M45V.js";import{kb as s}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as o}from"./chunk-MHOQBMVI.js";import{a as l}from"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{kc as d,vc as p,yc as a}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{ab as n,qa as t}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as C}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as e,h as i,n as m}from"./chunk-3KENBVE7.js";i();m();var r=e(C());var B=e(E());l();var H=r.default.lazy(()=>import("./SettingsConnectHardware-X6PPS5J5.js"));t.init({provider:S});f();var U=document.getElementById("root"),O=(0,B.createRoot)(U);O.render(r.default.createElement(u,null,r.default.createElement(P,{theme:g},r.default.createElement(w,null,r.default.createElement(v,null,r.default.createElement(p,{analytics:o},r.default.createElement(d,{authRepository:F},r.default.createElement(a,{userRepository:c,claimUsernameSigner:h},r.default.createElement(s,null,r.default.createElement(n,null,r.default.createElement(A,{backgroundColor:"#E2DFFE"}),r.default.createElement(b,null),r.default.createElement(y,null,r.default.createElement(r.Suspense,{fallback:null},r.default.createElement(H,null)))))))))))));o.capture("connectHardwareOpen");
//# sourceMappingURL=ConnectHardware.js.map
