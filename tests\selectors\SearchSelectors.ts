export const SearchSelectorsXPath = {
  // Search popup
  searchPopup: "//section[@role='dialog' and @aria-modal='true']",
  searchInput: "//input[@placeholder='Search token name, address']",
  searchCloseButton: "//button[@aria-label='Close']",

  // Trending Tokens section
  trendingTokensSection: "//div[./p[contains(text(), 'Trending tokens')]]",
  trendingTokensTitle: "//p[contains(text(), 'Trending tokens')]",
  trendingTokensList: "//div[./p[contains(text(), 'Trending tokens')]]/following-sibling::div[1]",
  trendingTokenItems: "//div[./p[contains(text(), 'Trending tokens')]]/following-sibling::div[1]//a",

  // Top Wallets section
  topWalletsSection: "//div[./p[contains(text(), 'Top wallets')]]",
  topWalletsTitle: "//p[contains(text(), 'Top wallets')]",
  topWalletsList: "//div[./p[contains(text(), 'Top wallets')]]/following-sibling::div[1]",
  topWalletItems: "//div[./p[contains(text(), 'Top wallets')]]/following-sibling::div[1]//a",

  // Token item elements (trong kết quả trending)
  tokenName: "//div[.//img]/following-sibling::div/p[1]",
  tokenSymbol: "//div[.//img]/following-sibling::div/p[2]",
  tokenPrice: "//div[contains(text(), '$')]",
  tokenLiquidity: "//div[.//p[contains(text(), 'Liquidity')]]",
  tokenVolume: "//div[.//p[contains(text(), 'Volume')]]",

  // Wallet item elements (trong kết quả top wallets)
  walletName: "//div[.//svg]/following-sibling::div/p[1]",
  walletAddress: "//div[.//svg]/following-sibling::div/p[contains(text(), 'Balance:')]",
  walletProfitSection: "//div[.//p[contains(text(), '7d PnL')]]",
  walletWinrateSection: "//div[.//p[contains(text(), 'Winrate')]]",

  // Recent section
  recentSection: "//div[./p[contains(text(), 'Recent')]]",
  recentTitle: "//p[contains(text(), 'Recent')]",
  recentItems: "//div[./p[contains(text(), 'Recent')]]/a",

  // Search results
  searchResults: "//div[@role='dialog']//a[@href] | //section[@role='dialog']//a[@href]",
  noDataMessage: "//div[contains(text(), 'No data')]",
  noDataIcon: "//svg/following-sibling::p[contains(text(), 'No data')] | //div[.//svg]/following-sibling::p[contains(text(), 'No data')]"
};
