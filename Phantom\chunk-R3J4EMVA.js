import{j as m}from"./chunk-OKP6DFCI.js";import{o as t,rb as i,ua as s}from"./chunk-WIQ4WVKX.js";import{m as l}from"./chunk-56SJOU6P.js";import{a}from"./chunk-7X4NV6OJ.js";import{f as c,h as e,n as r}from"./chunk-3KENBVE7.js";e();r();var o=c(a());var g=t.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`,h=t.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 90px;
`,u=t(i).attrs({size:28,weight:500})`
  margin-bottom: 8px;
  margin-top: 22px;
`,x=t(i).attrs({size:16,color:"#777"})`
  max-width: 275px;
  span {
    color: white;
  }
`,d=({onCancelClick:p})=>{let{t:n}=l();return o.default.createElement(g,null,o.default.createElement("div",null,o.default.createElement(h,null,o.default.createElement(s,{width:103,height:103}),o.default.createElement(u,null,n("notEnoughSolPrimaryText")),o.default.createElement(x,null,n("notEnoughSolSecondaryText")))),o.default.createElement(m,{onClick:p},n("commandCancel")))},w=d;export{d as a,w as b};
//# sourceMappingURL=chunk-R3J4EMVA.js.map
