{"commandAdd": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandAccept": "Accetta", "commandApply": "Applica", "commandApprove": "<PERSON><PERSON><PERSON><PERSON>", "commandAllow": "<PERSON><PERSON><PERSON>", "commandBack": "Indietro", "commandBuy": "Acquista", "commandCancel": "<PERSON><PERSON><PERSON>", "commandClaim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandClaimReward": "Riscuoti il tuo premio", "commandClear": "<PERSON><PERSON><PERSON><PERSON>", "commandClose": "<PERSON><PERSON>", "commandConfirm": "Conferma", "commandConnect": "Collega", "commandContinue": "Continua", "commandConvert": "<PERSON><PERSON><PERSON>", "commandCopy": "Copia", "commandCopyAddress": "Copia indirizzo", "commandCopyTokenAddress": "Copia l'indirizzo del token", "commandCreate": "<PERSON><PERSON>", "commandCreateTicket": "<PERSON>rea ticket", "commandDeny": "Nega", "commandDismiss": "<PERSON><PERSON>", "commandDontAllow": "Non consentire", "commandDownload": "Scarico", "commandEdit": "Modifica", "commandEditProfile": "Modifica profilo", "commandEnableNow": "Attiva ora", "commandFilter": "Filtra", "commandFollow": "<PERSON><PERSON><PERSON>", "commandHelp": "<PERSON><PERSON>", "commandLearnMore": "Ulteriori informazioni", "commandLearnMore2": "Ulteriori informazioni", "commandMint": "Conia", "commandMore": "Altro", "commandNext": "<PERSON><PERSON>", "commandNotNow": "Non ora", "commandOpen": "<PERSON>i", "commandOpenSettings": "Apri Impostazioni", "commandPaste": "<PERSON><PERSON><PERSON>", "commandReceive": "<PERSON><PERSON>", "commandReconnect": "<PERSON><PERSON><PERSON>", "commandRecordVideo": "Registra video", "commandRequest": "<PERSON><PERSON>", "commandRetry": "<PERSON><PERSON><PERSON><PERSON>", "commandReview": "Controlla", "commandRevoke": "Revoca", "commandSave": "<PERSON><PERSON>", "commandScanQRCode": "Scansiona il codice QR", "commandSelect": "Seleziona", "commandSelectMedia": "Seleziona file multimediale", "commandSell": "<PERSON><PERSON><PERSON>", "commandSend": "Invia", "commandShare": "Condi<PERSON><PERSON>", "commandShowBalance": "<PERSON>ra saldo", "commandSign": "Firma", "commandSignOut": "Sign Out", "commandStake": "Stake", "commandMintLST": "Conia JitoSOL", "commandSwap": "Scambia", "commandSwapAgain": "Scambia ancora", "commandTakePhoto": "Scatta foto", "commandTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "commandViewTransaction": "Mostra transazione", "commandReportAsNotSpam": "Segnala come non spam", "commandReportAsSpam": "<PERSON><PERSON><PERSON> come spam", "commandPin": "<PERSON><PERSON>", "commandBlock": "Blocca", "commandUnblock": "S<PERSON><PERSON>ca", "commandUnstake": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "commandUnpin": "S<PERSON><PERSON>ca", "commandHide": "Nascondi", "commandUnhide": "<PERSON><PERSON><PERSON>", "commandBurn": "Brucia", "commandReport": "<PERSON><PERSON><PERSON>", "commandView": "Visualizza", "commandProceedAnywayUnsafe": "Procedi comunque (non sicuro)", "commandUnfollow": "Non seguire", "commandUnwrap": "Scarta", "commandConfirmUnsafe": "Conferma (non sicuro)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, conferma (non sicuro)", "commandConfirmAnyway": "Conferma lo stesso", "commandReportIssue": "Se<PERSON>la un problema", "commandSearch": "Cerca", "commandShowMore": "Mostra di più", "commandShowLess": "<PERSON>ra meno", "pastParticipleClaimed": "R<PERSON>sso", "pastParticipleCompleted": "Completata", "pastParticipleCopied": "Copiato", "pastParticipleDone": "<PERSON><PERSON>", "pastParticipleDisabled": "Disabilitato", "pastParticipleRequested": "<PERSON><PERSON>", "nounName": "Nome", "nounNetwork": "Rete", "nounNetworkFee": "Commissione di rete", "nounSymbol": "Simbolo", "nounType": "Tipo", "nounDescription": "Descrizione", "nounYes": "Sì", "nounNo": "No", "amount": "Importo", "limit": "Limite", "new": "Novità", "gotIt": "Capito", "internal": "Interno", "reward": "Premio", "seeAll": "<PERSON><PERSON>i tutto", "seeLess": "<PERSON><PERSON><PERSON> meno", "viewAll": "<PERSON><PERSON> tutto", "homeTab": "Home", "collectiblesTab": "Collezionabili", "swapTab": "Scambio", "activityTab": "Attività", "exploreTab": "Esplora", "accountHeaderConnectedInterpolated": "Sei connesso a {{origin}}", "accountHeaderConnectedToSite": "Sei connesso a questo sito", "accountHeaderCopyToClipboard": "Copia negli appunti", "accountHeaderNotConnected": "Non sei connesso a", "accountHeaderNotConnectedInterpolated": "Non sei connesso a {{origin}}", "accountHeaderNotConnectedToSite": "Non sei connesso a questo sito", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "SOL insufficienti", "accountWithoutEnoughSolSecondaryText": "Un conto coinvolto in questa transazione non dispone di abbastanza SOL. Il conto potrebbe essere tuo o di qualcun altro. Questa transazione verrà ripristinata se inviata.", "accountSwitcher": "Cambia account", "addAccountHardwareWalletPrimaryText": "Collega portafoglio hardware", "addAccountHardwareWalletSecondaryText": "Usa il tuo portafoglio Ledger hardware", "addAccountHardwareWalletSecondaryTextMobile": "Usa il tuo portafoglio {{supportedHardwareWallets}}", "addAccountSeedVaultWalletPrimaryText": "<PERSON><PERSON><PERSON>", "addAccountSeedVaultWalletSecondaryText": "Usa un portafoglio da Seed Vault", "addAccountImportSeedPhrasePrimaryText": "Importa frase di recupero segreta", "addAccountImportSeedPhraseSecondaryText": "Importa account da un altro portafoglio", "addAccountImportWalletPrimaryText": "Importa chiave privata", "addAccountImportWalletSecondaryText": "Importa un account a catena singola", "addAccountImportWalletSolanaSecondaryText": "Importa una chiave privata Solana", "addAccountLimitReachedText": "Hai raggiunto il limite di {{accountsCount}} account in Phantom. Rimuovi gli account inutilizzati prima di aggiungerne altri.", "addAccountNoSeedAvailableText": "Non hai nessuna frase seed disponibile. Importa un seed esistente per generare un account.", "addAccountNewWalletPrimaryText": "Crea nuovo account", "addAccountNewWalletSecondaryText": "Genera un nuovo indirizzo di portafoglio", "addAccountNewMultiChainWalletSecondaryText": "Aggiungi un nuovo account multicatena", "addAccountNewSingleChainWalletSecondaryText": "Aggiungi un nuovo conto", "addAccountPrimaryText": "Aggiungi/Collega portafoglio", "addAccountSecretPhraseLabel": "Frase segreta", "addAccountSeedLabel": "Seed", "addAccountSeedIDLabel": "ID Seed", "addAccountSecretPhraseDefaultLabel": "Frase segreta {{number}}", "addAccountPrivateKeyDefaultLabel": "Chiave privata {{number}}", "addAccountZeroAccountsForSeed": "0 account", "addAccountShowAccountForSeed": "Mostra 1 account", "addAccountShowAccountsForSeed": "Mostra {{numOfAccounts}} account", "addAccountHideAccountForSeed": "Nascondi 1 account", "addAccountHideAccountsForSeed": "Nascondi {{numOfAccounts}} account", "addAccountSelectSeedDescription": "Il tuo nuovo account verrà generato da questa frase segreta", "addAccountNumAccountsForSeed": "{{numOfAccounts}} account", "addAccountOneAccountsForSeed": "1 account", "addAccountGenerateAccountFromSeed": "Crea account", "addAccountReadOnly": "Osserva indirizzo", "addAccountReadOnlySecondaryText": "Tieni traccia di qualsiasi indirizzo di portafoglio pubblico", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Indirizzo EVM", "addAccountBitcoinAddress": "Indirizzo Bitcoin", "addAccountCreateSeedTitle": "Crea un nuovo conto", "addAccountCreateSeedExplainer": "Il tuo portafoglio non ha ancora una frase segreta! Per creare un nuovo portafoglio, genereremo una frase di recupero. Scrivila e tienila per te.", "addAccountSecretPhraseHeader": "La tua frase segreta", "addAccountNoSecretPhrases": "Nessuna frase segreta disponibile", "addAccountImportAccountActionButtonImport": "Importa", "addAccountImportAccountDuplicatePrivateKey": "Questo account esiste già nel tuo portafoglio", "addAccountImportAccountIncorrectFormat": "Formato errato", "addAccountImportAccountInvalidPrivateKey": "Chiave privata non valida", "addAccountImportAccountName": "Nome", "addAccountImportAccountPrimaryText": "Importa chiave privata", "addAccountImportAccountPrivateKey": "Chiave privata", "addAccountImportAccountPublicKey": "Indirizzo o dominio", "addAccountImportAccountPrivateKeyRequired": "La chiave privata è obbligatoria", "addAccountImportAccountNameRequired": "Il nome è obbligatorio", "addAccountImportAccountPublicKeyRequired": "L'indirizzo pubblico è obbligatorio", "addAccountImportAccountDuplicateAddress": "<PERSON><PERSON> indirizzo esiste già nel tuo portafoglio", "addAddressAddressAlreadyAdded": "L'indirizzo è già stato aggiunto", "addAddressAddressAlreadyExists": "L'indirizzo esiste già", "addAddressAddressInvalid": "L'indirizzo non è valido", "addAddressAddressIsRequired": "L'indirizzo è obbligatorio", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "L'etichetta è obbligatoria", "addAddressLabelPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "addAddressPrimaryText": "Aggiungi indirizzo", "addAddressToast": "Indirizzo aggiunto", "createAssociatedTokenAccountCostLabelInterpolated": "Costerà {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Hai già questo account token", "createAssociatedTokenAccountErrorInsufficientFunds": "Fondi insufficienti", "createAssociatedTokenAccountErrorInvalidMint": "Indirizzo di zecca non valido", "createAssociatedTokenAccountErrorInvalidName": "Nome non valido", "createAssociatedTokenAccountErrorInvalidSymbol": "Simbolo non valido", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Non siamo riusciti a creare il tuo account token. Riprova.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Impossibile creare l'account", "createAssociatedTokenAccountErrorUnableToSendMessage": "Non siamo stati in grado di inviare la tua transazione.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Impossibile inviare la transazione", "createAssociatedTokenAccountInputPlaceholderMint": "Indirizzo di zecca", "createAssociatedTokenAccountInputPlaceholderName": "Nome", "createAssociatedTokenAccountInputPlaceholderSymbol": "Simbolo", "createAssociatedTokenAccountLoadingMessage": "Stiamo creando il tuo account token.", "createAssociatedTokenAccountLoadingTitle": "Creazione account token", "createAssociatedTokenAccountPageHeader": "Crea account token", "createAssociatedTokenAccountSuccessMessage": "Il tuo account token è stato creato con successo!", "createAssociatedTokenAccountSuccessTitle": "Account token creato", "createAssociatedTokenAccountViewTransaction": "Mostra transazione", "assetDetailRecentActivity": "Attività recente", "assetDetailStakeSOL": "Stake SOL", "assetDetailUnknownToken": "<PERSON><PERSON> s<PERSON>", "assetDetailUnwrapAll": "<PERSON><PERSON><PERSON><PERSON> tutto", "assetDetailUnwrappingSOL": "Spacchettamento SOL", "assetDetailUnwrappingSOLFailed": "Spacchettamento SOL non riuscito", "assetDetailViewOnExplorer": "<PERSON>ra su {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorer", "assetDetailSaveToPhotos": "<PERSON><PERSON> nelle <PERSON>", "assetDetailSaveToPhotosToast": "<PERSON><PERSON><PERSON>", "assetDetailPinCollection": "<PERSON><PERSON>", "assetDetailUnpinCollection": "Sblocca Raccolta", "assetDetailHideCollection": "Nascondi Raccolta", "assetDetailUnhideCollection": "Mostra Raccolta", "assetDetailTokenNameLabel": "Nome token", "assetDetailNetworkLabel": "Rete", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "Prezzo", "collectibleDetailSetAsAvatar": "Imposta come avatar", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar impostato", "collectibleDetailShare": "Condividi collezionabile", "assetDetailTokenAddressCopied": "<PERSON><PERSON><PERSON><PERSON> copia<PERSON>", "assetDetailStakingLabel": "Staking", "assetDetailAboutLabel": "Informazioni su {{fungibleName}}", "assetDetailPriceDetail": "Dettagli sul prezzo", "assetDetailHighlights": "In evidenza", "assetDetailAllTimeReturn": "Ritorno da sempre", "assetDetailAverageCost": "Costo medio", "assetDetailPriceHistoryUnavailable": "Cronologia dei prezzi non disponibile per questo token", "assetDetailPriceHistoryInsufficientData": "Cronologia dei prezzi non disponibile per questo intervallo", "assetDetailPriceDataUnavailable": "Dati sui prezzi non disponibili", "assetDetailPriceHistoryError": "Errore nel recupero della cronologia dei prezzi", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1G", "assetDetailTimeFrame24h": "Premio 24 ore", "assetDetailTimeFrame1W": "1S", "assetDetailTimeFrame1M": "1M", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "ALL", "sendAssetAmountLabelInterpolated": "Disponibili {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Quotazioni", "fiatRampNewQuote": "Nuova quotazione", "assetListSelectToken": "Seleziona token", "assetListSearch": "Cerca...", "assetListUnknownToken": "<PERSON><PERSON> s<PERSON>", "buyFlowHealthWarning": "Alcuni dei nostri fornitori di pagamento stanno riscontrando un traffico elevato. I depositi potrebbero subire ritardi di diverse ore.", "assetVisibilityUnknownToken": "<PERSON><PERSON> s<PERSON>", "buyAssetInterpolated": "Acquista {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "<PERSON>'ac<PERSON>o massimo <PERSON> {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "L'acquisto minimo è {{amount}}", "buyNoAssetsAvailable": "Nessun asset Ethereum o Polygon disponibile", "buyThirdPartyScreenPaymentMethodSelector": "Paga con", "buyThirdPartyScreenPaymentMethod": "Scegli il metodo di pagamento", "buyThirdPartyScreenChoseQuote": "Inserisci un importo valido per la quotazione", "buyThirdPartyScreenProviders": "Fornitori", "buyThirdPartyScreenPaymentMethodTitle": "Metodi di pagamento", "buyThirdPartyScreenPaymentMethodEmptyState": "Nessun metodo di pagamento disponibile nella tua area geografica", "buyThirdPartyScreenPaymentMethodFooter": "I pagamenti sono gestiti dai partner di rete. Le tariffe possono variare. Alcuni metodi di pagamento non sono disponibili nella tua area geografica.", "buyThirdPartyScreenProvidersEmptyState": "<PERSON><PERSON>un fornitore disponibile nella tua area geografica", "buyThirdPartyScreenLoadingQuote": "Caricamento quotazione...", "buyThirdPartyScreenViewQuote": "Vedi quotazione", "gasEstimationErrorWarning": "Si è verificato un problema nella stima della commissione per questa transazione. Potrebbe non andare a buon fine.", "gasEstimationCouldNotFetch": "Impossibile recuperare la stima di gas", "networkFeeCouldNotFetch": "Impossibile recuperare la commissione di rete", "nativeTokenBalanceErrorWarning": "Si è verificato un problema nel recupero del saldo del token per questa transazione. Potrebbe non andare a buon fine.", "blocklistOriginCommunityDatabaseInterpolated": "Questo sito è stato segnalato come parte di un <1>database gestito dalla community</1> di siti Web di phishing e truffe noti. Se ritieni che il sito sia stato segnalato per errore, <3>segnala un problema</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} è bloccato!", "blocklistOriginIgnoreWarning": "Ignora l'avviso, portami comunque a {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom ritiene che questo sito Web sia dannoso e non sicuro da usare.", "blocklistOriginThisDomain": "questo dominio", "blocklistProceedAnyway": "Ignora l'avviso, procedi comunque", "maliciousTransactionWarning": "Phantom ritiene che questa transazione sia dannosa e non sicura da firmare. Abbiamo disabilitato la possibilità di firmarlo per proteggere te e i tuoi fondi.", "maliciousTransactionWarningIgnoreWarning": "Ignora l'avviso, procedi comunque", "maliciousTransactionWarningTitle": "Transazione segnalata!", "maliciousRequestBlockedTitle": "<PERSON><PERSON> bloccata", "maliciousRequestWarning": "Questo sito web è stato contrassegnato come pericoloso. Potrebbe tentare di rubare i tuoi fondi o indurti a confermare una richiesta ingannevole.", "maliciousSignatureRequestBlocked": "<PERSON> la tua sicurezza, <PERSON> ha bloccato questa richiesta.", "maliciousRequestBlocked": "<PERSON> la tua sicurezza, <PERSON> ha bloccato questa richiesta.", "maliciousRequestFrictionDescription": "Procedere non è sicuro, quindi Phantom ha bloccato questa richiesta. Dovresti chiudere questa finestra.", "maliciousRequestAcknowledge": "Capisco che potrei perdere tutti i miei fondi utilizzando questo sito web.", "maliciousRequestAreYouSure": "È davvero sicuro?", "siwErrorPopupTitle": "Richiesta di firma non valida", "siwParseErrorDescription": "La richiesta di firma dell'app non può essere mostrata a causa di una formattazione non valida.", "siwVerificationErrorDescription": "Si sono verificati 1 o più errori con la richiesta di firma del messaggio. Per sic<PERSON>, assicurati di utilizzare l'app corretta e riprova.", "siwErrorPagination": "{{n}} di {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Avviso: l'indirizzo dell'app non corrisponde all'indirizzo fornito per la firma.", "siwErrorMessage_DOMAIN_MISMATCH": "Avviso: il dominio dell'app non corrisponde al dominio fornito per la verifica.", "siwErrorMessage_URI_MISMATCH": "Avviso: il nome host dell'URI non corrisponde al dominio.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Avviso: l'ID della catena non corrisponde all'ID della catena fornito per la verifica.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Avviso: la data di emissione del messaggio è troppo lontana nel passato.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Avviso: la data di emissione del messaggio è troppo lontana nel futuro.", "siwErrorMessage_EXPIRED": "Avviso: il messaggio è scaduto.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Avviso: il messaggio scade prima dell'emissione.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Avviso: il messaggio scadrà prima che diventi valido.", "siwErrorShowErrorDetails": "Mostra i dettagli dell'errore", "siwErrorHideErrorDetails": "Nascondi i dettagli dell'errore", "siwErrorIgnoreWarning": "Ignora l'avviso, procedi comunque", "siwsTitle": "Richiesta di accesso", "siwsPermissions": "Autorizzazioni", "siwsAgreement": "Messaggio", "siwsAdvancedDetails": "<PERSON><PERSON><PERSON>", "siwsAlternateStatement": "{{domain}} vuole che tu acceda con il tuo account Solana:\n{{address}}", "siwsFieldLable_domain": "<PERSON>inio", "siwsFieldLable_address": "<PERSON><PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Versione", "siwsFieldLable_chainId": "ID catena", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "Emissione", "siwsFieldLable_expirationTime": "Scadenza", "siwsFieldLable_requestId": "ID richiesta", "siwsFieldLable_resources": "Risorse", "siwsVerificationErrorDescription": "Questa richiesta di accesso non è valida. Ciò significa che il sito non è sicuro o che il suo sviluppatore ha commesso un errore durante l'invio della richiesta.", "siwsErrorNumIssues": "{{n}} problemi", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Questo ID catena non corrisponde alla rete in cui ti trovi.", "siwsErrorMessage_DOMAIN_MISMATCH": "Questo dominio non è quello a cui stai effettuando l'accesso.", "siwsErrorMessage_URI_MISMATCH": "Questo URI non è quello a cui stai effettuando l'accesso.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "La data di emissione del messaggio è troppo lontana nel passato.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "La data di emissione del messaggio è troppo avanti nel futuro.", "siwsErrorMessage_EXPIRED": "Il messaggio è scaduto.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Il messaggio scade prima dell'emissione.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Il messaggio scadrà prima che diventi valido.", "changeLockTimerPrimaryText": "Timer di blocco automatico", "changeLockTimerSecondaryText": "Quanto tempo dobbiamo aspettare per bloccare il tuo portafoglio dopo che è rimasto inattivo?", "changeLockTimerToast": "Timer di blocco automatico aggiornato", "changePasswordConfirmNewPassword": "Conferma la nuova password", "changePasswordCurrentPassword": "Password attuale", "changePasswordErrorIncorrectCurrentPassword": "Password attuale errata", "changePasswordErrorGeneric": "Qualcosa è andato storto, riprova più tardi", "changePasswordNewPassword": "Nuova password", "changePasswordPrimaryText": "Cambia la password", "changePasswordToast": "Password aggiornata", "collectionsSpamCollections": "Raccolte spam", "collectionsHiddenCollections": "Ra<PERSON>lte nascoste", "collectiblesReportAsSpam": "<PERSON><PERSON><PERSON> come spam", "collectiblesReportAsSpamAndHide": "Segnala come spam e nascondi", "collectiblesReportAsNotSpam": "Segnala come non spam", "collectiblesReportAsNotSpamAndUnhide": "Rendi visibile e segnala come non spam", "collectiblesReportNotSpam": "Non è spam", "collectionsManageCollectibles": "Gestisci l'elenco dei collezionabili", "collectibleDetailDescription": "Descrizione", "collectibleDetailProperties": "Proprietà", "collectibleDetailOrdinalInfo": "Informazioni ordinali", "collectibleDetailRareSatsInfo": "Informazioni su Rare Sat", "collectibleDetailSatsInUtxo": "Sat in UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sat", "collectibleDetailSatNumber": "Numero Sat", "collectibleDetailSatName": "Nome Sat", "collectibleDetailInscriptionId": "ID Iscrizione", "collectibleDetailInscriptionNumber": "Numero Iscrizione", "collectibleDetailStandard": "Standard", "collectibleDetailCreated": "<PERSON><PERSON><PERSON>", "collectibleDetailViewOnExplorer": "<PERSON>ra su {{explorer}}", "collectibleDetailList": "Elenco", "collectibleDetailSellNow": "Vendi per {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Libera Bitcoin di riserva", "collectibleDetailUtxoSplitterCtaSubtitle": "Hai {{value}} di BTC da sbloccare", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sat", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Per proteggere i tuoi fondi, impediamo l'invio di BTC in UTXO con Rare Sat. Usa lo splitter UTXO di Magic Eden per liberare {{value}} di BTC dai tuoi Rare Sat.", "collectibleDetailUtxoSplitterModalCtaButton": "Usa lo splitter UTXO", "collectibleDetailEasilyAccept": "Accetta l'offerta più alta", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sat", "collectibleDetailSpamOverlayDescription": "Questo collezionabile è stato nascosto perché Phantom ritiene che si tratti di spam.", "collectibleDetailSpamOverlayReveal": "Mostra collezionabile", "collectibleBurnTermsOfService": "Capisco che l'azione è irreversibile", "collectibleBurnTitleWithCount_one": "B<PERSON>cia token", "collectibleBurnTitleWithCount_other": "B<PERSON>cia token", "collectibleBurnDescriptionWithCount_one": "Questa azione distruggerà e rimuoverà permanentemente questo token dal tuo portafoglio.", "collectibleBurnDescriptionWithCount_other": "Questa azione distruggerà e rimuoverà permanentemente questi token dal tuo portafoglio.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Token", "collectibleBurnCta": "Brucia", "collectibleBurnRebate": "Restituzione", "collectibleBurnRebateTooltip": "Una piccola quantità di SOL verrà automaticamente depositata nel tuo portafoglio per la bruciatura di questo token.", "collectibleBurnNetworkFee": "Commissione di rete", "collectibleBurnNetworkFeeTooltip": "Importo richiesto dalla rete Solana per elaborare la transazione", "unwrapButtonSwapTo": "Scambia in {{chainSymbol}}", "unwrapButtonWithdrawFrom": "<PERSON><PERSON><PERSON> da {{withdrawalSource}} in cambio di {{chainSymbol}}", "unwrapModalEstimatedTime": "Tempo stimato", "unwrapModalNetwork": "Rete", "unwrapModalNetworkFee": "Commissione di rete", "unwrapModalTitle": "Resoconto", "unsupportedChain": "Catena non supportata", "unsupportedChainDescription": "Non supportiamo {{action}} per la rete {{chainName}}.", "networkFeesTooltipLabel": "Commissioni di rete {{chainName}}", "networkFeesTooltipDescription": "Le commissioni di {{chainName}} variano in base a diversi fattori. Puoi personalizzarli per rendere la tua transazione più veloce (più costosa) o più lenta (più economica).", "burnStatusErrorTitleWithCount_one": "Impossibile bruciare il token", "burnStatusErrorTitleWithCount_other": "Impossibile bruciare i token", "burnStatusSuccessTitleWithCount_one": "Token bruciato!", "burnStatusSuccessTitleWithCount_other": "Token bruciati!", "burnStatusLoadingTitleWithCount_one": "Bruciatura token...", "burnStatusLoadingTitleWithCount_other": "Bruciatura token...", "burnStatusErrorMessageWithCount_one": "Impossibile bruciare questo token. Riprova più tardi.", "burnStatusErrorMessageWithCount_other": "Impossibile bruciare questi token. Riprova più tardi.", "burnStatusSuccessMessageWithCount_one": "Questo token è stato definitivamente distrutto e {{rebateAmount}} SOL sono stati depositati nel tuo portafoglio.", "burnStatusSuccessMessageWithCount_other": "Questi token sono stati definitivamente distrutti e {{rebateAmount}} SOL sono stati depositati nel tuo portafoglio.", "burnStatusLoadingMessageWithCount_one": "Questo token verrà definitivamente distrutto e {{rebateAmount}} SOL verranno depositati nel tuo portafoglio.", "burnStatusLoadingMessageWithCount_other": "Questi token verranno definitivamente distrutti e {{rebateAmount}} SOL verranno depositati nel tuo portafoglio.", "burnStatusViewTransactionText": "Mostra transazione", "collectibleDisplayLoading": "Caricamento...", "collectiblesNoCollectibles": "<PERSON>essun colleziona<PERSON>e", "collectiblesPrimaryText": "I tuoi collezionabili", "collectiblesReceiveCollectible": "Ricevi collezionabile", "collectiblesUnknownCollection": "Raccolta sconosciuta", "collectiblesUnknownCollectible": "Collezionabili sconosciuti", "collectiblesUniqueHolders": "Possessori unici", "collectiblesSupply": "Fornitura", "collectiblesUnknownTokens": "<PERSON><PERSON> s<PERSON>", "collectiblesNrOfListed": "{{ nrOfListed }} in listino", "collectiblesListed": "In vendita", "collectiblesMintCollectible": "Conia collezionabile", "collectiblesYouMint": "Il tuo coniato", "collectiblesMintCost": "Costo di conio", "collectiblesMintFail": "<PERSON>io non riuscito", "collectiblesMintFailMessage": "Si è verificato un problema nella coniazione del tuo collezionabile. Riprova.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "Conio...", "collectiblesMintingMessage": "Il tuo collezionabile è in fase di conio", "collectiblesMintShareSubject": "Dai un'occhiata", "collectiblesMintShareMessage": "L'ho coniato su @phantom!", "collectiblesMintSuccess": "<PERSON><PERSON>", "collectiblesMintSuccessMessage": "Il tuo collezionabile è in fase di conio", "collectiblesMintSuccessQuestMessage": "<PERSON> soddisfatto i requisiti per una Missione Phantom. Tocca Riscuoti il tuo premio per ottenere il tuo collezionabile gratuito.", "collectiblesMintRequired": "Obbligatorio", "collectiblesMintMaxLengthErrorMessage": "Lunghezza massima superata", "collectiblesMintSafelyDismiss": "Puoi tranquillamente chiudere questa finestra.", "collectiblesTrimmed": "Abbiamo raggiunto il limite per il numero di oggetti collezionabili che possono essere visualizzati in questo momento.", "collectiblesNonTransferable": "Non trasferibile", "collectiblesNonTransferableYes": "Sì", "collectiblesSellOfferDetails": "Dettagli dell'offerta", "collectiblesSellYouSell": "<PERSON><PERSON><PERSON>", "collectiblesSellGotIt": "Capito", "collectiblesSellYouReceive": "<PERSON><PERSON>", "collectiblesSellOffer": "<PERSON><PERSON>", "collectiblesSoldCollectible": "Collezionabile venduto", "collectiblesSellMarketplace": "Marketplace", "collectiblesSellCollectionFloor": "Minimo collezione", "collectiblesSellDifferenceFromFloor": "Differenza dal minimo", "collectiblesSellLastSalePrice": "Ultima vendita", "collectiblesSellEstimatedFees": "Commissioni stimate", "collectiblesSellEstimatedProfitAndLoss": "Stima profitti/perdite", "collectiblesSellViewOnMarketplace": "Mostra su {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "Il prezzo \"Acquista ora\" più basso nella raccolta su diversi marketplace.", "collectiblesSellProfitLossTooltip": "Profitto e Perdita stimati vengono calcolati in base all'ultimo prezzo di vendita e all'importo dell'offerta meno le commissioni.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "<PERSON><PERSON><PERSON> ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Tariffa Marketplace ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Commissione marketplace", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Rete {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Il preventivo include una commissione Phantom del {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Il preventivo include i diritti, la commissione di rete, la tariffa del Marketplace e una commissione Phantom del {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "La quotazione include le royalty, la commissione di rete e una commissione di marketplace", "collectiblesSellTransactionFeeTooltipTitle": "Commissione della transazione", "collectiblesSellStatusLoadingTitle": "Accettazione dell'offerta...", "collectiblesSellStatusLoadingIsSellingFor": "si sta vendendo a", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} venduto!", "collectiblesSellStatusSuccessWasSold": "è stato venduto correttamente per", "collectiblesSellStatusErrorTitle": "Qualcosa è andato storto", "collectiblesSellStatusErrorSubtitle": "Si è verificato un errore durante il tentativo di vendita", "collectiblesSellStatusViewTransaction": "Mostra transazione", "collectiblesSellInsufficientFundsTitle": "Fondi insufficienti", "collectiblesSellInsufficientFundsSubtitle": "Non siamo riusciti ad accettare un'offerta su questo oggetto da collezione perché non c'erano fondi sufficienti per pagare la commissione di rete.", "collectiblesSellRecentlyTransferedNFTTitle": "Trasferito di recente", "collectiblesSellRecentlyTransferedNFTSubtitle": "Devi attendere 1 ora per accettare le offerte dopo un trasferimento.", "collectiblesApproveCollection": "{{collectionName}} approvati", "collectiblesSellNotAvailableAnymoreTitle": "Offerta non disponibile", "collectiblesSellNotAvailableAnymoreSubtitle": "L'offerta non è più disponibile. Annulla questa offerta e riprova", "collectiblesSellFlaggedTokenTitle": "Collezionabile contrassegnato", "collectiblesSellFlaggedTokenSubtitle": "Il collezionabile non è scambiabile, potrebbe essere per molteplici ragioni, ad esempio potrebbe essere stato segnalato come rubato o messo in stake senza blocco", "collectiblesListOnMagicEden": "<PERSON><PERSON> in vendita su Magic Eden", "collectiblesListPrice": "Prezzo di listino", "collectiblesUseFloor": "Usa minimo", "collectiblesFloorPrice": "Prezzo minimo", "collectiblesLastSalePrice": "Ultimo prezzo di vendita", "collectiblesTotalReturn": "Rendimento totale", "collectiblesOriginalPurchasePrice": "Prezzo di acquisto originale", "collectiblesMagicEdenFee": "Commissione Magic Eden", "collectiblesArtistRoyalties": "<PERSON><PERSON><PERSON> d'<PERSON>re", "collectiblesListNowButton": "<PERSON><PERSON> in listino", "collectiblesListAnywayButton": "<PERSON><PERSON> in vendita comunque", "collectiblesCreateListingTermsOfService": "Toccando <1>\"<PERSON><PERSON> in listino\"</1> accetti i <3>Termini di servizio</3> di Magic Eden", "collectiblesViewListing": "Mostra listino", "collectiblesListingViewTransaction": "Mostra transazione", "collectiblesRemoveListing": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesEditListing": "Modifica listino", "collectiblesEditListPrice": "Modifica prezzo di listino", "collectiblesListPriceTooltip": "Prezzo di listino è il prezzo di vendita di un articolo. I venditori in genere impostano il prezzo di listino in modo che sia uguale o superiore al prezzo minimo.", "collectiblesFloorPriceTooltip": "Il prezzo minimo è il prezzo di listino attivo più basso per un articolo in questa raccolta.", "collectiblesOriginalPurchasePriceTooltip": "Hai originariamente acquistato questo articolo per questo importo.", "collectiblesPurchasedForSol": "Ac<PERSON><PERSON> per {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Impossibile caricare i listini", "collectiblesUnableToLoadListingsFrom": "Impossibile caricare i listini da {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "I tuoi listini e i tuoi asset sono al sicuro, ma al momento non siamo in grado di caricarli da {{marketplace}}. Riprova più tardi.", "collectiblesBelowFloorPrice": "Al di sotto del prezzo minimo", "collectiblesBelowFloorPriceMessage": "Vuoi davvero mettere in vendita la tua NFT al di sotto del prezzo minimo?", "collectiblesMinimumListingPrice": "Il prezzo minimo è 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden addebita una commissione sulle transazioni completate.", "collectiblesArtistRoyaltiesTooltip": "Il creatore di questa collezione riceve una % in royalty da ogni vendita completata.", "collectibleScreenCollectionLabel": "Raccolta", "collectibleScreenPhotosPermissionTitle": "Autorizzazione foto", "collectibleScreenPhotosPermissionMessage": "Abbiamo bisogno della tua autorizzazione per accedere alle tue foto. Vai su Impostazioni e aggiorna le tue autorizzazioni.", "collectibleScreenPhotosPermissionOpenSettings": "Apri Impostazioni", "listStatusErrorTitle": "Messa in listino non riuscita", "editListStatusErrorTitle": "Impossibile aggiornare", "removeListStatusErrorTitle": "Rimozione da listino non riuscita", "listStatusSuccessTitle": "Listino creato!", "editListingStatusSuccessTitle": "Listino aggiornato!", "removeListStatusSuccessTitle": "<PERSON><PERSON> rim<PERSON>o da Magic Eden", "listStatusLoadingTitle": "Creazione listino...", "editListingStatusLoadingTitle": "Aggiornamento listino...", "removeListStatusLoadingTitle": "Rimozione listino...", "listStatusErrorMessage": "{{name}} non può essere messo in vendita su Magic Eden", "removeListStatusErrorMessage": "{{name}} non può essere rimosso dal listino su Magic Eden", "listStatusSuccessMessage": "{{name}} ora è in vendita su Magic Eden per {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "{{name}} ora è aggiornato su Magic Eden per {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} è stato rimosso con successo da Magic Eden", "listStatusLoadingMessage": "Listino {{name}} su <PERSON> per {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Aggiornamento {{name}} su Magic Eden per {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Rimozione di {{name}} da Magic Eden. L'operazione potrebbe richiedere del tempo.", "listStatusLoadingSafelyDismiss": "Puoi tranquillamente chiudere questa finestra.", "listStatusViewOnMagicEden": "<PERSON><PERSON><PERSON> su <PERSON>", "listStatusViewOnMarketplace": "Mostra su {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON>", "listStatusViewTransaction": "Mostra transazione", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato. Una volta rilevato, puoi scegliere quale indirizzo desideri utilizzare.", "connectHardwareFailedPrimaryText": "Connessione fallita", "connectHardwareFailedSecondaryText": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato. Una volta rilevato, puoi scegliere quale indirizzo utilizzare.", "connectHardwareFinishPrimaryText": "Account aggiunto!", "connectHardwareFinishSecondaryText": "Ora puoi accedere al tuo portafoglio Ledger Nano da Phantom. Torna all'estensione.", "connectHardwareNeedsPermissionPrimaryText": "Connetti un nuovo portafoglio", "connectHardwareNeedsPermissionSecondaryText": "Clicca sul pulsante in basso per avviare il processo di connessione.", "connectHardwareSearchingPrimaryText": "Ricerca portafoglio...", "connectHardwareSearchingSecondaryText": "Collega il tuo portafoglio hardware, assicurati che sia sbloccato e di avere le autorizzazioni approvate nel tuo browser.", "connectHardwarePermissionDeniedPrimary": "Autorizzazione negata", "connectHardwarePermissionDeniedSecondary": "Autorizza Phantom a connettersi al tuo dispositivo Ledger", "connectHardwarePermissionUnableToConnect": "Impossibile connett<PERSON>i", "connectHardwarePermissionUnableToConnectDescription": "Non siamo riusciti a connetterci al tuo dispositivo Ledger. Potremmo aver bisogno di ulteriori autorizzazioni.", "connectHardwareSelectAddressAllAddressesImported": "Tutti gli indirizzi importati", "connectHardwareSelectAddressDerivationPath": "Percorso di derivazione", "connectHardwareSelectAddressSearching": "Ricerca...", "connectHardwareSelectAddressSelectWalletAddress": "Seleziona l'indirizzo del portafoglio", "connectHardwareSelectAddressWalletAddress": "Indirizzo del portafoglio", "connectHardwareWaitingForApplicationSecondaryText": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato.", "connectHardwareWaitingForPermissionPrimaryText": "Serve il permesso", "connectHardwareWaitingForPermissionSecondaryText": "Collega il tuo portafoglio hardware, assicurati che sia sbloccato e di avere le autorizzazioni approvate nel tuo browser.", "connectHardwareAddAccountButton": "Aggiungi account", "connectHardwareLedger": "Collega il tuo Ledger", "connectHardwareStartConnection": "Fai clic sul pulsante in basso per iniziare a connettere il tuo portafoglio hardware Ledger", "connectHardwarePairSuccessPrimary": "{{productName}} connesso", "connectHardwarePairSuccessSecondary": "Hai connesso correttamente il tuo {{productName}}.", "connectHardwareSelectChains": "Seleziona le catene da connettere", "connectHardwareSearching": "Ricerca...", "connectHardwareMakeSureConnected": "Connetti e sblocca il tuo portafoglio hardware. Concedi le autorizzazioni del browser pertinenti.", "connectHardwareOpenAppDescription": "Sblocca il tuo portafoglio hardware", "connectHardwareConnecting": "Collegamento...", "connectHardwareConnectingDescription": "Ci stiamo connettendo al tuo dispositivo Ledger.", "connectHardwareConnectingAccounts": "Connessione ai tuoi conti...", "connectHardwareDiscoveringAccounts": "Ricerca di account...", "connectHardwareDiscoveringAccountsDescription": "Stiamo cercando attività nei tuoi account.", "connectHardwareErrorLedgerLocked": "Ledger bloccato", "connectHardwareErrorLedgerLockedDescription": "Assicurati che il tuo dispositivo Ledger sia sbloccato, quindi rip<PERSON>a.", "connectHardwareErrorLedgerGeneric": "Qualcosa è andato storto", "connectHardwareErrorLedgerGenericDescription": "Impossibile trovare gli account. Assicurati che il tuo dispositivo Ledger sia sbloccato, quindi riprova.", "connectHardwareErrorLedgerPhantomLocked": "Riapri Phantom e prova a connettere nuovamente il tuo hardware.", "connectHardwareFindingAccountsWithActivity": "Sto cercando account {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "Ricerca account {{chainName1}} o {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Abbiamo trovato {{numOfAccounts}} conti con attività sul tuo Ledger.", "connectHardwareFoundAccountsWithActivitySingular": "Abbiamo trovato 1 conto con attività sul tuo Ledger.", "connectHardwareFoundSomeAccounts": "Abbiamo trovato alcuni account sul tuo dispositivo Ledger.", "connectHardwareViewAccounts": "Mostra conti", "connectHardwareConnectAccounts": "Account collegati", "connectHardwareSelectAccounts": "Seleziona i conti", "connectHardwareChooseAccountsToConnect": "Scegli i conti di portafoglio da importare.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} conti aggiunti", "connectHardwareAccountsStepOfSteps": "Passo {{stepNum}} di {{totalSteps}}", "connectHardwareMobile": "<PERSON><PERSON><PERSON>", "connectHardwareMobileTitle": "Connetti il tuo portafoglio Ledger hardware", "connectHardwareMobileEnableBluetooth": "Abilita Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Consenti l'autorizzazione a utilizzare il Bluetooth per la connessione", "connectHardwareMobileEnableBluetoothSettings": "Vai su Impostazioni per consentire a Phantom di utilizzare le autorizzazioni di Posizione e Dispositivi nelle vicinanze.", "connectHardwareMobilePairWithDevice": "Accoppia con il tuo dispositivo Ledger", "connectHardwareMobilePairWithDeviceDescription": "Tieni il tuo dispositivo nelle vicinanze per ottenere il segnale migliore", "connectHardwareMobileConnectAccounts": "Connetti account", "connectHardwareMobileConnectAccountsDescription": "Cercheremo l'attività in tutti gli account che potresti aver utilizzato", "connectHardwareMobileConnectLedgerDevice": "Collega il tuo dispositivo Ledger", "connectHardwareMobileLookingForDevices": "Ricerca di dispositivi nelle vicinanze...", "connectHardwareMobileLookingForDevicesDescription": "Collega il tuo dispositivo Ledger e assicurati che sia sbloccato.", "connectHardwareMobileFoundDeviceSingular": "Abbiamo trovato 1 dispositivo Ledger", "connectHardwareMobileFoundDevices": "Abbiam<PERSON> trovato {{numDevicesFound}} dispositivi <PERSON>", "connectHardwareMobileFoundDevicesDescription": "Seleziona un dispositivo Ledger qui sotto per iniziare l'abbinamento.", "connectHardwareMobilePairingWith": "Abbinamento con {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Segui le istruzioni sul tuo dispositivo Ledger durante l'abbinamento.", "connectHardwareMobilePairingFailed": "Abbinamento non riuscito", "connectHardwareMobilePairingFailedDescription": "Impossibile abbinare con {{deviceName}}. Assicurati che il tuo dispositivo sia sbloccato.", "connectHardwareMobilePairingSuccessful": "Abbinamento riuscito", "connectHardwareMobilePairingSuccessfulDescription": "Hai abbinato e connesso correttamente il tuo dispositivo Ledger.", "connectHardwareMobileOpenAppSingleChain": "Apri l'app {{chainName}} sul tuo Ledger", "connectHardwareMobileOpenAppDualChain": "Apri l'app {{chainName1}} o {{chainName2}} sul tuo Ledger", "connectHardwareMobileOpenAppDescription": "Assicurati che il tuo dispositivo sia sbloccato.", "connectHardwareMobileStillCantFindDevice": "Non riesci ancora a trovare il tuo dispositivo?", "connectHardwareMobileLostConnection": "Connessione persa", "connectHardwareMobileLostConnectionDescription": "Abbiamo perso la connessione a {{deviceName}}. Assicurati che il tuo dispositivo sia sbloccato, quindi riprova.", "connectHardwareMobileGenericLedgerDevice": "Dispositivo Ledger", "connectHardwareMobileConnectDeviceSigning": "<PERSON><PERSON><PERSON> il tuo {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Sblocca il tuo dispositivo Ledger e tienilo vicino.", "connectHardwareMobileBluetoothDisabled": "Il bluetooth è disattivato", "connectHardwareMobileBluetoothDisabledDescription": "Abilita il bluetooth e assicurati che il tuo dispositivo Ledger sia sbloccato.", "connectHardwareMobileLearnMore": "Ulteriori informazioni", "connectHardwareMobileBlindSigningDisabled": "Firma alla cieca disabilitata", "connectHardwareMobileBlindSigningDisabledDescription": "Assicurati che la firma alla cieca sia abilitata sul tuo dispositivo.", "connectHardwareMobileConfirmSingleChain": "Devi confermare la transazione sul tuo portafoglio hardware. Assicurati che sia sbloccato.", "metamaskExplainerBottomSheetHeader": "Questo sito funziona con Phantom", "metamaskExplainerBottomSheetSubheader": "Seleziona MetaMask dalla finestra di dialogo di connessione del portafoglio per procedere.", "metamaskExplainerBottomSheetDontShowAgain": "Non mostrare più", "ledgerStatusNotConnected": "Ledger non connesso", "ledgerStatusConnectedInterpolated": "{{productName}} è connesso", "connectionClusterInterpolated": "Attualmente sei su {{cluster}}", "connectionClusterTestnetMode": "Attualmente sei in Modalità Testnet", "featureNotSupportedOnLocalNet": "Questa funzionalità non è supportata se Solana Localnet è attiva.", "readOnlyAccountBannerWarning": "Stai osservando questo account", "depositAddress": "Indirizzo di ricezione", "depositAddressChainInterpolated": "Il tuo indirizzo {{chain}}", "depositAssetDepositInterpolated": "<PERSON><PERSON> {{tokenSymbol}}", "depositAssetSecondaryText": "<PERSON><PERSON> indirizzo può essere utilizzato solo per ricevere token compatibili.", "depositAssetTextInterpolated": "Utilizza questo indirizzo per ricevere token e oggetti da collezione su <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Trasferimento dall'exchange", "depositAssetShareAddress": "Condividi indirizzo", "depositAssetBuyOrDeposit": "Acquista o trasferisci", "depositAssetBuyOrDepositDesc": "Finanzia il tuo portafoglio per iniziare", "depositAssetTransfer": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editAddressAddressAlreadyAdded": "L'indirizzo è già stato aggiunto", "editAddressAddressAlreadyExists": "L'indirizzo esiste già", "editAddressAddressIsRequired": "L'indirizzo è obbligatorio", "editAddressPrimaryText": "Modifica indirizzo", "editAddressRemove": "<PERSON><PERSON><PERSON><PERSON> dalla rubrica", "editAddressToast": "<PERSON><PERSON>iz<PERSON>", "removeSavedAddressToast": "<PERSON><PERSON><PERSON><PERSON>", "exportSecretErrorGeneric": "Qualcosa è andato storto, riprova più tardi", "exportSecretErrorIncorrectPassword": "Password non corretta", "exportSecretPassword": "Password", "exportSecretPrivateKey": "chiave privata", "exportSecretSecretPhrase": "frase segreta", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "frase di recupero segreta", "exportSecretSelectYourAccount": "Seleziona il tuo account", "exportSecretShowPrivateKey": "Mostra chiave privata", "exportSecretShowSecretRecoveryPhrase": "Mostra frase di recupero segreta", "exportSecretShowSecret": "Mostra {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "<1>Non</1> condividere la tua {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "Se qualcuno scopre la tua {{secretNameText}} avrà il pieno controllo del tuo portafoglio.", "exportSecretOnlyWay": "Il tuo {{secretNameText}} è l'unico modo per recuperare il tuo portafoglio", "exportSecretDoNotShow": "Non consentire a nessuno di vedere il tuo {{secretNameText}}", "exportSecretWillNotShare": "Non condividerò il mio {{secretNameText}} con nessuno, incluso Phantom.", "exportSecretNeverShare": "Non condividere mai la tua {{secretNameText}} con nessuno", "exportSecretYourPrivateKey": "La tua chiave privata", "exportSecretYourSecretRecoveryPhrase": "La tua frase di recupero segreta", "exportSecretResetPin": "Resetta il tuo PIN", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "<PERSON><PERSON>", "gasUpTo": "Fino a {{ amount }}", "timeDescription1hour": "Circa 1 ora", "timeDescription30minutes": "Circa 30 minuti", "timeDescription10minutes": "Circa 10 minuti", "timeDescription2minutes": "Circa 2 minuti", "timeDescription30seconds": "Circa 30 secondi", "timeDescription15seconds": "Circa 15 secondi", "timeDescription10seconds": "Circa 10 secondi", "timeDescription5seconds": "Circa 5 secondi", "timeDescriptionAbbrev1hour": "1ora", "timeDescriptionAbbrev30minutes": "30min", "timeDescriptionAbbrev10minutes": "10min", "timeDescriptionAbbrev2minutes": "2min", "timeDescriptionAbbrev30seconds": "30s", "timeDescriptionAbbrev15seconds": "15s", "timeDescriptionAbbrev10seconds": "10s", "timeDescriptionAbbrev5seconds": "5s", "gasSlow": "<PERSON><PERSON>", "gasAverage": "Media", "gasFast": "Veloce", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "<PERSON><PERSON><PERSON><PERSON>", "homeErrorDescription": "Si è verificato un errore durante il tentativo di recuperare i tuoi asset. Aggiorna e riprova", "homeErrorTitle": "Impossibile recuperare gli asset", "homeManageTokenList": "Gestisci l'elenco dei token", "interstitialDismissUnderstood": "Ho capito", "interstitialBaseWelcomeTitle": "Phantom ora supporta Base!", "interstitialBaseWelcomeItemTitle_1": "Invia, ricevi e acquista token", "interstitialBaseWelcomeItemTitle_2": "Esplora l'ecosistema Base", "interstitialBaseWelcomeItemTitle_3": "Sicuro e protetto", "interstitialBaseWelcomeItemDescription_1": "Trasferisci e acquista USDC ed ETH su Base utilizzando {{paymentMethod}}, carte o Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Usa Phantom con tutte le tue app DeFi e NFT preferite.", "interstitialBaseWelcomeItemDescription_3": "Stai al sicuro con il supporto del Ledger, il filtro antispam e la simulazione delle transazioni.", "privacyPolicyChangedInterpolated": "La nostra Informativa sulla privacy è cambiata. <1>Ulteriori informazioni</1>", "bitcoinAddressTypesBodyTitle": "Tipi di indirizzi Bitcoin", "bitcoinAddressTypesFeature1Title": "Informazioni sugli indirizzi Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom supporta Native Segwit e Taproot, ciascuno con il proprio saldo. Puoi inviare BTC o Ordinali con entrambi i tipi di indirizzo.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "L'indirizzo BTC predefinito in Phantom. Più vecchio di Taproot ma compatibile con tutti i portafogli e gli exchange.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Ideale per Ordinali e BRC-20, con le tariffe più economiche. Modifica gli indirizzi in Preferenze -> Indirizzo Bitcoin preferito.", "headerTitleInfo": "Informazioni", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Questo è il tuo indirizzo <1>{{addressType}}</1>.", "invalidChecksumTitle": "Abbiamo aggiornato la tua frase segreta!", "invalidChecksumFeature1ExportPhrase": "Esporta la tua nuova frase segreta", "invalidChecksumFeature1ExportPhraseDescription": "Ti preghiamo di eseguire il backup della tua nuova frase segreta insieme alle chiavi private dei tuoi vecchi account.", "invalidChecksumFeature2FundsAreSafe": "I tuoi fondi sono al sicuro", "invalidChecksumFeature2FundsAreSafeDescription": "Questo aggiornamento è stato automatizzato. <PERSON><PERSON><PERSON> in Phantom conosce la tua frase segreta o ha accesso ai tuoi fondi.", "invalidChecksumFeature3LearnMore": "Ulteriori informazioni", "invalidChecksumFeature3LearnMoreDescription": "Avevi una frase incompatibile con la maggior parte dei portafogli. Leggi <1>questo articolo della guida</1> per saperne di più.", "invalidChecksumBackUpSecretPhrase": "Esegui il backup della frase segreta", "migrationFailureTitle": "Qualcosa è andato storto durante la migrazione del tuo account", "migrationFailureFeature1": "Esporta la tua frase segreta", "migrationFailureFeature1Description": "Esegui il backup della tua frase segreta prima dell'accoglienza.", "migrationFailureFeature2": "<PERSON><PERSON> accogliere su <PERSON>", "migrationFailureFeature2Description": "Dovrai effettuare nuovamente l'accoglienza su Phantom per visualizzare il tuo account.", "migrationFailureFeature3": "Ulteriori informazioni", "migrationFailureFeature3Description": "Legg<PERSON> <1>questo articolo della guida</1> per ulteriori informazioni al riguardo.", "migrationFailureContinueToOnboarding": "Continua con l'accoglienza", "migrationFailureUnableToFetchMnemonic": "Non siamo riusciti a caricare la tua frase segreta", "migrationFailureUnableToFetchMnemonicDescription": "Contatta l'assistenza e scarica i registri dell'applicazione per esaminare la situazione", "migrationFailureContactSupport": "Contatta l'assistenza", "ledgerActionConfirm": "Conferma sul tuo Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Firma cieca disabilitata", "ledgerActionErrorBlindSignDisabledSecondaryText": "Assicurati che la firma cieca sia abilitata sul tuo dispositivo hardware e riprova l'azione", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Dispositivo hardware disconnesso durante l'operazione", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Chiudi l'estensione Phantom e riprova l'azione", "ledgerActionErrorDeviceLockedPrimaryText": "Dispositivo hardware bloccato", "ledgerActionErrorDeviceLockedSecondaryText": "Sblocca il dispositivo hardware e riprova", "ledgerActionErrorHeader": "Errore di azione nel Ledger", "ledgerActionErrorUserRejectionPrimaryText": "Transazione rifiutata dall'utente", "ledgerActionErrorUserRejectionSecondaryText": "L'azione è stata rifiutata sul dispositivo hardware dall'utente", "ledgerActionNeedPermission": "Serve il permesso", "ledgerActionNeedToConfirm": "Devi confermare la transazione sul tuo portafoglio hardware. Assicurati che sia sbloccato sull'app {{chainType}}.", "ledgerActionNeedToConfirmMany": "Dovrai confermare {{numberOfTransactions}} transazioni sul tuo portafoglio hardware. Assicurati che il portafoglio sia sbloccato sull'app {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Devi confermare la transazione sul tuo portafoglio hardware. Assicurati che il portafoglio sia sbloccato sull'app {{chainType}} e che la firma alla cieca sia abilitata.", "ledgerActionNeedToConfirmBlindMany": "Dovrai confermare {{numberOfTransactions}} transazioni sul tuo portafoglio hardware. Assicurati che il portafoglio sia sbloccato sull'app {{chainType}} e che la firma alla cieca sia abilitata.", "ledgerActionPleaseConnect": "Collega il tuo Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "Collega il tuo portafoglio hardware e assicurati che sia sbloccato. Assicurati di avere le autorizzazioni approvate nel tuo browser.", "maxInputAmount": "Importo", "maxInputMax": "Max", "notEnoughSolPrimaryText": "SOL insufficienti", "notEnoughSolSecondaryText": "Non hai abbastanza SOL nel tuo portafoglio per questa transazione. Versane di più e riprova.", "insufficientBalancePrimaryText": "{{tokenSymbol}} insufficienti", "insufficientBalanceSecondaryText": "Non hai abbastanza {{tokenSymbol}} nel tuo portafoglio per questa transazione.", "insufficientBalanceRemaining": "<PERSON><PERSON><PERSON><PERSON>", "insufficientBalanceRequired": "Obbligatorio", "notEnoughSplTokensTitle": "Non hai abbastanza token", "notEnoughSplTokensDescription": "Non hai abbastanza token nel tuo portafoglio per questa transazione. Questa transazione verrà ripristinata se inviata.", "transactionExpiredPrimaryText": "Transazione scaduta", "transactionExpiredSecondaryText": "Hai aspettato troppo tempo per confermare la transazione ed è scaduta. Questa transazione verrà ripristinata se inviata.", "transactionHasWarning": "Avviso di transazione", "tokens": "token", "notificationApplicationApprovalPermissionsAddressVerification": "Verifica di possedere questo indirizzo", "notificationApplicationApprovalPermissionsTransactionApproval": "Richiedere l'approvazione per le transazioni", "notificationApplicationApprovalPermissionsViewWalletActivity": "Visualizzare il saldo e l'attività del tuo portafoglio", "notificationApplicationApprovalParagraphText": "La conferma consentirà a questo sito di visualizzare saldi e attività per l'account selezionato.", "notificationApplicationApprovalActionButtonConnect": "Collega", "notificationApplicationApprovalActionButtonSignIn": "Accedi", "notificationApplicationApprovalAllowApproval": "Consentire al sito di connettersi?", "notificationApplicationApprovalAutoConfirm": "Conferma automaticamente le transazioni", "notificationApplicationApprovalConnectDisclaimer": "Collegati solo a siti Web di cui ti fidi", "notificationApplicationApprovalSignInDisclaimer": "Accedi solo a siti web di cui ti fidi", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Questo sito web non è sicuro da utilizzare e potrebbe tentare di rubare i tuoi fondi.", "notificationApplicationApprovalConnectUnknownApp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Impossibile connettersi all'app", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Questa app sta tentando di connettersi a {{appNetworkName}}, ma {{phantomNetworkName}} è selezionato.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Per utilizzare {{networkName}}, vai su Impostazioni sviluppatore → Modalità testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Rete scon<PERSON>", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "La connessione ad altre app mobili non è attualmente supportata da Ledger.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Passa a un account non Ledger o utilizza il browser in-app e riprova.", "notificationSignatureRequestConfirmTransaction": "Conferma transazione", "notificationSignatureRequestConfirmTransactionCapitalized": "Conferma transazione", "notificationSignatureRequestConfirmTransactions": "Conferma transazioni", "notificationSignatureRequestConfirmTransactionsCapitalized": "Conferma transazioni", "notificationSignatureRequestSignatureRequest": "Richiesta di firma", "notificationMessageHeader": "Messaggio", "notificationMessageCopied": "Messaggio copiato", "notificationAutoConfirm": "Conferma automatica", "notificationAutoConfirmOff": "Off", "notificationAutoConfirmOn": "On", "notificationConfirmFooter": "Conferma solo se ti fidi di questo sito web.", "notificationEstimatedTime": "Tempo stimato", "notificationPermissionRequestText": "Questa è solo una richiesta di autorizzazione. La transazione potrebbe non essere eseguita immediatamente.", "notificationBalanceChangesText": "Le variazioni di saldo sono stimate. Importi e beni coinvolti non sono garantiti.", "notificationContractAddress": "Indirizzo del contratto", "notificationAdvancedDetailsText": "Avanzate", "notificationUnableToSimulateWarningText": "Al momento non siamo in grado di stimare le modifiche al saldo. Puoi riprovare più tardi o confermare se ti fidi di questo sito.", "notificationSignMessageParagraphText": "Firmando questo messaggio dimostrerai di essere proprietario dell'account selezionato.", "notificationSignatureRequestScanFailedDescription": "Impossibile eseguire la scansione del messaggio per problemi di sicurezza. Procedi con cautela.", "notificationFailedToScan": "Impossibile simulare i risultati di questa richiesta.\nConfermare non è sicuro e può portare a perdite.", "notificationScanLoading": "Richiesta di scansione", "notificationTransactionApprovalActionButtonConfirm": "Conferma", "notificationTransactionApprovalActionButtonBack": "Indietro", "notificationTransactionApprovalEstimatedChanges": "Cambiamenti stimati", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Le stime si basano su simulazioni di transazioni e non costituiscono una garanzia", "notificationTransactionApprovalHideAdvancedDetails": "Nascondi i dettagli avanzati delle transazioni", "notificationTransactionApprovalNetworkFee": "Commissione di rete", "notificationTransactionApprovalNetwork": "Rete", "notificationTransactionApprovalEstimatedTime": "Tempo stimato", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Non sono stati rilevati cambiamenti che influiscono sulla proprietà delle risorse", "notificationTransactionApprovalSolanaAmountRequired": "Importo richiesto dalla rete Solana per elaborare la transazione", "notificationTransactionApprovalUnableToSimulate": "Impossibile simulare. Assicurati di fidarti di questo sito web poiché l'approvazione può portare alla perdita di fondi.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Impossibile recuperare le modifiche al saldo", "notificationTransactionApprovalViewAdvancedDetails": "Mostra i dettagli avanzati delle transazioni", "notificationTransactionApprovalKnownMalicious": "Questa transazione è dannosa. La firma comporterà la perdita di fondi.", "notificationTransactionApprovalSuspectedMalicious": "Sospettiamo che questa transazione sia dannosa. L'approvazione può comportare la perdita di fondi.", "notificationTransactionApprovalNetworkFeeHighWarning": "Le commissioni di rete sono elevate a causa della congestione della rete.", "notificationTransactionERC20ApprovalDescription": "La conferma consentirà a questa app di accedere al tuo saldo in qualsiasi momento, fino al limite indicato di seguito.", "notificationTransactionERC20ApprovalContractAddress": "Indirizzo del contratto", "notificationTransactionERC20Unlimited": "illimitato", "notificationTransactionERC20ApprovalTitle": "Approva la spesa di {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Revoca la spesa di {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Revoca l'accesso a {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "<PERSON><PERSON> i tuoi {{tokenSymbol}}", "notificationIncorrectModeTitle": "Modalità errata", "notificationIncorrectModeInTestnetTitle": "Sei in modalità Testnet", "notificationIncorrectModeNotInTestnetTitle": "Non sei in modalità Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} sta tentando di utilizzare una mainnet, ma sei in modalità Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} sta tentando di utilizzare una testnet, ma non sei in modalità Testnet", "notificationIncorrectModeInTestnetProceed": "Per procedere, disattiva la modalità Testnet.", "notificationIncorrectModeNotInTestnetProceed": "Per procedere, attiva la modalità Testnet.", "notificationIncorrectEIP712ChainId": "Ti abbiamo impedito di firmare un messaggio che non era destinato alla rete a cui sei attualmente connesso", "notificationIncorrectEIP712ChainIdDescription": "Messaggio richiesto {{messageChainId}}, sei connesso a {{connectedChainId}}", "notificationUnsupportedNetwork": "Rete non supportata", "notificationUnsupportedNetworkDescription": "Questo sito web sta tentando di utilizzare una rete che Phantom attualmente non supporta.", "notificationUnsupportedNetworkDescriptionInterpolated": "Per procedere con un'altra estensione, disattiva <1>Impostazioni → Portafoglio app predefinito e seleziona Chiedi sempre</1>. <PERSON><PERSON>di aggiorna la pagina e riconnettiti.", "notificationUnsupportedAccount": "Account non supportato", "notificationUnsupportedAccountDescription": "Questo sito web sta tentando di utilizzare {{targetChainType}}, che questo account {{chainType}} non supporta.", "notificationUnsupportedAccountDescription2": "Passa a un account da una frase seed o da una chiave privata compatibile e riprova.", "notificationInvalidTransaction": "Transazione non valida", "notificationInvalidTransactionDescription": "La transazione ricevuta da questa app non è corretta e non deve essere inviata. Contatta lo sviluppatore di questa app per segnalargli il problema.", "notificationCopyTransactionText": "Copia transazione", "notificationTransactionCopied": "Transazione copiata", "onboardingImportOptionsPageTitle": "Importa un portafoglio", "onboardingImportOptionsPageSubtitle": "Importa un portafoglio esistente con la tua frase segreta, chiave privata o portafoglio hardware.", "onboardingImportPrivateKeyPageTitle": "Importa una chiave privata", "onboardingImportPrivateKeyPageSubtitle": "Importa un portafoglio a catena singola esistente", "onboardingCreatePassword": "Crea una password", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Accetto i <1>Termini di servizio</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Conferma la password", "onboardingCreatePasswordDescription": "La userai per sbloccare il tuo portafoglio.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Frase di recupero segreta non valida", "onboardingCreatePasswordPasswordPlaceholder": "Password", "onboardingCreatePasswordPasswordStrengthWeak": "De<PERSON>e", "onboardingCreatePasswordPasswordStrengthMedium": "Media", "onboardingCreatePasswordPasswordStrengthStrong": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Ho salvato la mia frase di recupero segreta", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Frase di recupero segreta", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Questa frase è l'UNICO modo per recuperare il tuo portafoglio. NON condividerla con nessuno!", "onboardingImportWallet": "Importa portafoglio", "onboardingImportWalletImportExistingWallet": "Importa un portafoglio esistente con la tua frase di recupero segreta di 12 o 24 parole.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON><PERSON><PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "Frase di recupero segreta", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Frase di recupero segreta non valida", "onboardingImportWalletIHaveWords": "Ho una frase di recupero di {{numWords}} parole", "onboardingImportWalletIncorrectOrMisspelledWord": "La parola {{wordIndex}} non è corretta o è scritta in modo errato", "onboardingImportWalletIncorrectOrMisspelledWords": "Le parole {{wordIndexes}} non sono corrette o sono scritte in modo errato", "onboardingImportWalletScrollDown": "<PERSON><PERSON><PERSON> in basso", "onboardingImportWalletScrollUp": "<PERSON><PERSON><PERSON> in alto", "onboardingSelectAccountsImportAccounts": "Importa account", "onboardingSelectAccountsImportAccountsDescription": "Scegli gli account portafoglio da importare.", "onboardingSelectAccountsImportSelectedAccounts": "Importa account selezionati", "onboardingSelectAccountsFindMoreAccounts": "<PERSON><PERSON><PERSON> altri conti", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON><PERSON> conto trovato", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} conti selezionati", "onboardingSelectAccountSelectAllText": "Se<PERSON><PERSON>na tutto", "onboardingAdditionalPermissionsTitle": "Utilizza le app con Phantom", "onboardingAdditionalPermissionsSubtitle": "Per un'esperienza dell'app più fluida, ti consigliamo di consentire a Phantom di leggere e modificare i dati su tutti i siti.", "interstitialAdditionalPermissionsTitle": "Utilizza le app con Phantom", "interstitialAdditionalPermissionsSubtitle": "Per continuare la tua esperienza utilizzando le app senza interruzioni, ti consigliamo di consentire a Phantom di leggere e modificare i dati su tutti i siti.", "recentActivityPrimaryText": "Attività recente", "removeAccountActionButtonRemove": "<PERSON><PERSON><PERSON><PERSON>", "removeAccountRemoveWallet": "<PERSON><PERSON><PERSON><PERSON> conto", "removeAccountInterpolated": "R<PERSON><PERSON><PERSON> {{accountName}}", "removeAccountWarningLedger": "Anche se stai rimuovendo questo portafoglio da Phantom, potrai aggiungerlo nuovamente utilizzando la procedura \"Collega portafoglio hardware\".", "removeAccountWarningSeedVault": "Anche se stai rimuovendo questo portafoglio da Phantom, potrai aggiungerlo nuovamente utilizzando la procedura \"Collega portafoglio Seed Vault\".", "removeAccountWarningPrivateKey": "Una volta rimosso questo port<PERSON>, Phantom non potrà più recuperarlo per te. Assicurati di aver eseguito il backup della tua chiave privata.", "removeAccountWarningSeed": "Anche se stai rimuovendo questo portafoglio da Phantom, potrai derivarlo nuovamente utilizzando il tuo mnemonico in questo o in un altro portafoglio.", "removeAccountWarningReadOnly": "L'eliminazione di questo account non influirà sul tuo portafoglio, poiché è di sola osservazione.", "removeSeedPrimaryText": "Rimozione della frase segreta {{number}}", "removeSeedSecondaryText": "Questa operazione rimuoverà tutti gli account esistenti nella frase segreta {{number}}. Assicurati di aver salvato la frase segreta.", "resetSeedPrimaryText": "Ripristina l'app con una nuova frase segreta", "resetSeedSecondaryText": "Questo rimuoverà tutti gli account esistenti e li sostituirà con quelli nuovi. Assicurati di aver eseguito il backup della frase segreta e delle chiavi private esistenti.", "resetAppPrimaryText": "Ripristina e cancella l'app", "resetAppSecondaryText": "Questa operazione rimuoverà tutti gli account e i dati esistenti. Assicurati di aver eseguito il backup della frase segreta e delle chiavi private.", "richTransactionsDays": "<PERSON>ior<PERSON>", "richTransactionsToday": "<PERSON><PERSON><PERSON>", "richTransactionsYesterday": "<PERSON><PERSON>", "richTransactionDetailAccount": "Account", "richTransactionDetailAppInteraction": "Interazione con l'app", "richTransactionDetailAt": "alle", "richTransactionDetailBid": "Offerta", "richTransactionDetailBidDetails": "Dettagli offerta", "richTransactionDetailBought": "Acquistato", "richTransactionDetailBurned": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailCancelBid": "<PERSON><PERSON><PERSON> offerta", "richTransactionDetailCompleted": "Completata", "richTransactionDetailConfirmed": "Confermata", "richTransactionDetailDate": "Data", "richTransactionDetailFailed": "Non riuscita", "richTransactionDetailFrom": "Da", "richTransactionDetailItem": "Articolo", "richTransactionDetailListed": "In vendita", "richTransactionDetailListingDetails": "Dettagli di listino", "richTransactionDetailListingPrice": "Prezzo di listino", "richTransactionDetailMarketplace": "Marketplace", "richTransactionDetailNetworkFee": "Commissione di rete", "richTransactionDetailOriginalListingPrice": "Prezzo di listino originale", "richTransactionDetailPending": "In sospeso", "richTransactionDetailPrice": "Prezzo", "richTransactionDetailProvider": "Fornitore", "richTransactionDetailPurchaseDetails": "<PERSON><PERSON><PERSON> a<PERSON>", "richTransactionDetailRebate": "Restituzione", "richTransactionDetailReceived": "Ricevuto", "richTransactionDetailSaleDetails": "Det<PERSON><PERSON> vendita", "richTransactionDetailSent": "Inviato", "richTransactionDetailSold": "Venduto", "richTransactionDetailStaked": "In stake", "richTransactionDetailStatus": "Stato", "richTransactionDetailSwap": "Scambio", "richTransactionDetailSwapDetails": "<PERSON><PERSON><PERSON> scambio", "richTransactionDetailTo": "A", "richTransactionDetailTokenSwap": "Scambio di token", "richTransactionDetailUnknownNFT": "NFT sconosciuto", "richTransactionDetailUnlisted": "Non in vendita", "richTransactionDetailUnstaked": "<PERSON><PERSON><PERSON> da <PERSON>", "richTransactionDetailValidator": "Validatore", "richTransactionDetailViewOnExplorer": "<PERSON>ra su {{explorer}}", "richTransactionDetailWithdrawStake": "Ritira stake", "richTransactionDetailYouPaid": "<PERSON> pagato", "richTransactionDetailYouReceived": "Hai ricevuto", "richTransactionDetailUnwrapDetails": "Dettagli spacc<PERSON>ttamento", "richTransactionDetailTokenUnwrap": "Spacchettamento token", "activityItemsRefreshFailed": "Impossibile caricare le transazioni più recenti.", "activityItemsPagingFailed": "Impossibile caricare le transazioni meno recenti.", "activityItemsTestnetNotAvailable": "Cronologia transazioni testnet non disponibile al momento", "historyUnknownDappName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "historyStatusSucceeded": "Operazione riuscita", "historyNetwork": "Rete", "historyAttemptedAmount": "Importo del tentativo", "historyAmount": "Importo", "sendAddressBookButtonLabel": "Rubrica", "addressBookSelectAddressBook": "Rubrica", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON><PERSON> in<PERSON> sal<PERSON>", "sendAddressBookRecentlyUsed": "Usati di recente", "addressBookSelectRecentlyUsed": "Usati di recente", "sendConfirmationLabel": "<PERSON><PERSON><PERSON><PERSON>", "sendConfirmationMessage": "Messaggio", "sendConfirmationNetworkFee": "Commissione di rete", "sendConfirmationPrimaryText": "Invio conferma", "sendWarning_INSUFFICIENT_FUNDS": "Fondi insufficienti, è probabile che la transazione non vada a buon fine se inviata.", "sendFungibleSummaryNetwork": "Rete", "sendFungibleSummaryNetworkFee": "Commissione di rete", "sendFungibleSummaryEstimatedTime": "Tempo stimato", "sendFungiblePendingEstimatedTime": "Stime di tempo", "sendFungibleSummaryEstimatedTimeDescription": "Le velocità delle transazioni di Ethereum variano in base a diversi fattori. Puoi velocizzarli cliccando su “Commissione di rete”.", "sendSummaryBitcoinPendingTxTitle": "Impossibile inviare il trasferimento", "sendSummaryBitcoinPendingTxDescription": "Puoi avere un solo trasferimento BTC in sospeso alla volta. Attendi il completamento per inviare un nuovo trasferimento.", "sendFungibleSatProtectionTitle": "Invia con protezione satellitare", "sendFungibleSatProtectionExplainer": "Phantom garantisce che i tuoi Ordinali e BRC20 non verranno utilizzati per commissioni di transazione o trasferimenti Bitcoin.", "sendFungibleTransferFee": "Commissione per il trasferimento del token", "sendFungibleTransferFeeToolTip": "Il creatore di questo token riceve una commissione su ogni trasferimento. Questa non è una commissione addebitata o riscossa da Phantom.", "sendFungibleInterestBearingPercent": "Tasso di interesse attuale", "sendFungibleNonTransferable": "Non trasferibile", "sendFungibleNonTransferableToolTip": "Questo token non può essere trasferito su un altro account.", "sendFungibleNonTransferableYes": "Sì", "sendStatusErrorMessageInterpolated": "Si è verificato un errore durante il tentativo di inviare i token a <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "Saldo insufficiente per completare la transazione.", "sendStatusErrorTitle": "Impossibile inviare", "sendStatusLoadingTitle": "Invio...", "sendStatusSuccessMessageInterpolated": "I tuoi token sono stati inviati correttamente a <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Inviati!", "sendStatusConfirmedSuccessTitle": "Invio effettuato!", "sendStatusSubmittedSuccessTitle": "Transazione inviata", "sendStatusEstimatedTransactionTime": "Tempo stimato di transazione: {{time}}", "sendStatusViewTransaction": "Mostra transazione", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> a <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> inviati correttamente a <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> inviati correttamente a <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "Impossibile inviare <2>{{uiAmount}} {{assetSymbol}}</2> a <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "Codice errore {{code}}", "sendFormErrorInsufficientBalance": "<PERSON><PERSON>", "sendFormErrorEmptyAmount": "Quantità richiesta", "sendFormInvalidAddress": "Indirizzo {{assetName}} non valido", "sendFormInvalidUsernameOrAddress": "Nome utente o indirizzo non valido", "sendFormErrorInvalidSolanaAddress": "Indirizzo di Solana non valido", "sendFormErrorInvalidTwitterHandle": "Questo handle di Twitter non è registrato", "sendFormErrorInvalidDomain": "Questo dominio non è registrato", "sendFormErrorInvalidUsername": "Questo nome utente non è registrato", "sendFormErrorMinRequiredInterpolated": "<PERSON><PERSON><PERSON> {{minAmount}} {{tokenName}}", "sendRecipientTextareaPlaceholder": "Indirizzo SOL del destinatario", "sendRecipientTextAreaPlaceholder2": "Indirizzo {{symbol}} del destinatario", "sendMemoOptional": "Promemoria (facoltativo)", "sendMemo": "<PERSON>a", "sendOptional": "facoltativo", "settings": "Impostazioni", "settingsDapps": "dApps", "settingsSelectedAccount": "Account selezionato", "settingsAddressBookNoLabel": "<PERSON><PERSON>", "settingsAddressBookPrimary": "Rubrica", "settingsAddressBookRecentlyUsed": "Usati di recente", "settingsAddressBookSecondary": "Gestisci gli indirizzi di uso comune", "settingsAutoLockTimerPrimary": "Timer di blocco automatico", "settingsAutoLockTimerSecondary": "Modifica la durata del timer di blocco automatico", "settingsChangeLanguagePrimary": "Cambia lingua", "settingsChangeLanguageSecondary": "Cambia la lingua di visualizzazione", "settingsChangeNetworkPrimary": "Cambia rete", "settingsChangeNetworkSecondary": "Configura le tue impostazioni di rete", "settingsChangePasswordPrimary": "Cambia password", "settingsChangePasswordSecondary": "Modifica la password della schermata di blocco", "settingsCompleteBetaSurvey": "Completa il sondaggio sulla beta", "settingsDisplayLanguage": "Lingua di visualizzazione", "settingsErrorCannotExportLedgerPrivateKey": "Impossibile esportare la chiave privata Ledger", "settingsErrorCannotRemoveAllWallets": "Impossibile rimuovere tutti i conti", "settingsExportPrivateKey": "Mostra chiave privata", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Rete Phantom RPC", "settingsTestNetworks": "Reti di prova", "settingsUseCustomNetworks": "Usa reti personalizzate", "settingsTestnetMode": "Modalità Testnet", "settingsTestnetModeDescription": "Si applica ai saldi e alle connessioni alle app.", "settingsWebViewDebugging": "Debugging vista web", "settingsWebViewDebuggingDescription": "Consente di ispezionare ed eseguire il debug delle visualizzazioni web del browser in-app.", "settingsTestNetworksInfo": "Il passaggio a qualsiasi rete Testnet è inteso solo a scopo di test. Tieni presente che i token sulle reti Testnet non hanno alcun valore monetario.", "settingsEmojis": "<PERSON><PERSON><PERSON>", "settingsNoAddresses": "<PERSON><PERSON><PERSON>", "settingsAddressBookEmptyHeading": "La tua Rubrica è vuota", "settingsAddressBookEmptyText": "Fai clic sui pulsanti \"+\" o \"Aggiungi indirizzo\" per aggiungere i tuoi indirizzi preferiti", "settingsEditWallet": "Modifica conto", "settingsNoTrustedApps": "Nessuna app affidabile", "settingsNoConnections": "Ancora nessuna connessione.", "settingsRemoveWallet": "<PERSON><PERSON><PERSON><PERSON> conto", "settingsResetApp": "Reimposta l'app", "settingsBlocked": "Bloccato", "settingsBlockedAccounts": "Account bloc<PERSON>i", "settingsNoBlockedAccounts": "Nessun account bloccato.", "settingsRemoveSecretPhrase": "Rimuovi la frase segreta", "settingsResetAppWithSecretPhrase": "Ripristina l'app con la frase segreta", "settingsResetSecretRecoveryPhrase": "Resetta frase di recupero segreta", "settingsShowSecretRecoveryPhrase": "Mostra frase di recupero segreta", "settingsShowSecretRecoveryPhraseSecondary": "Mostra frase di recupero", "settingsShowSecretRecoveryPhraseTertiary": "Mostra frase segreta", "settingsTrustedAppsAutoConfirmActiveUntil": "Fino al {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Conferma automatica", "settingsTrustedAppsDisclaimer": "Abilita la conferma automatica solo su siti attendibili", "settingsTrustedAppsLastUsed": "Usato {{formattedTimestamp}} fa", "settingsTrustedAppsPrimary": "App connesse", "settingsTrustedApps": "App affidabili", "settingsTrustedAppsRevoke": "Revoca", "settingsTrustedAppsRevokeToast": "{{trustedApp}} disconnessa", "settingsTrustedAppsSecondary": "Configura le tue applicazioni affidabili", "settingsTrustedAppsToday": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsYesterday": "<PERSON><PERSON>", "settingsTrustedAppsLastWeek": "Settimana scorsa", "settingsTrustedAppsBeforeYesterday": "Prima", "settingsTrustedAppsDisconnectAll": "Disconnetti da tutto", "settingsTrustedAppsDisconnectAllToast": "<PERSON><PERSON> le app disconnesse", "settingsTrustedAppsEndAutoConfirmForAll": "Termina la conferma automatica per tutto", "settingsTrustedAppsEndAutoConfirmForAllToast": "Tutte le sessioni di conferma automatica sono terminate", "settingsSecurityPrimary": "Sicurezza e privacy", "settingsSecuritySecondary": "Aggiorna le tue impostazioni di sicurezza", "settingsActiveNetworks": "<PERSON><PERSON> attive", "settingsActiveNetworksAll": "<PERSON><PERSON>", "settingsActiveNetworksSolana": "Solo Solana", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana è la rete predefinita e rimane sempre attiva.", "settingsDeveloperPrimary": "Impostazioni sviluppatore", "settingsAdvanced": "Impostazioni avanzate", "settingsTransactions": "Impostazioni transazione", "settingsAutoConfirm": "Impostazioni di conferma automatica", "settingsSecurityAnalyticsPrimary": "Condividi dati analitici anonimi", "settingsSecurityAnalyticsSecondary": "Abilita per aiutarci a migliorare", "settingsSecurityAnalyticsHelper": "Phantom non utilizza le tue informazioni personali per scopi di analisi", "settingsSuspiciousCollectiblesPrimary": "Nascondi collezionabili sospetti", "settingsSuspiciousCollectiblesSecondary": "Attiva/disattiva per nascondere i collezionabili contrassegnati", "settingsPreferredBitcoinAddress": "Indirizzo Bitcoin preferito", "settingsEnabledAddressesUpdated": "Indirizzi visibili aggiornati!", "settingsEnabledAddresses": "Indirizzi abilitati", "settingsBitcoinPaymentAddressForApps": "Indirizzo pagamenti per le app", "settingsBitcoinOrdinalsAddressForApps": "Indirizzo ordinali per le app", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Quando entrambi i tipi di indirizzo sopra indicati sono abilitati, per alcune app come Magic Eden, il tuo indirizzo Segwit nativo verrà utilizzato per finanziare gli acquisti. Le risorse acquistate verranno ricevute al tuo indirizzo Taproot.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "L'indirizzo Bitcoin predefinito in Phantom per garantire la compatibilità.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Predefinito)", "settingsPreferredBitcoinAddressTaprootExplainer": "Il tipo di indirizzo più moderno, solitamente con commissioni di transazione più economiche.", "settingsPreferredExplorers": "Esploratore preferito", "settingsPreferredExplorersSecondary": "Passa al tuo esploratore blockchain preferito", "settingsCustomGasControls": "Controlli gas personalizzati", "settingsSupportDesk": "Centro assistenza", "settingsSubmitATicket": "Invia un ticket", "settingsAttachApplicationLogs": "Allega i log dell'applicazione", "settingsDownloadApplicationLogs": "Scarica i log dell'app", "settingsDownloadApplicationLogsShort": "Scarica i log", "settingsDownloadApplicationLogsHelper": "<PERSON><PERSON><PERSON> dati locali, rapporti sugli arresti anomali e indirizzi di portafogli pubblici per aiutare l'assistenza Phantom a risolvere i problemi", "settingsDownloadApplicationLogsWarning": "Non sono inclusi dati sensibili come frasi seed o chiavi private.", "settingsWallet": "Portafoglio", "settingsPreferences": "Prefer<PERSON><PERSON>", "settingsSecurity": "<PERSON><PERSON><PERSON>", "settingsDeveloper": "Sviluppatore", "settingsSupport": "Assistenza", "settingsWalletShortcutsPrimary": "Mostra scorciatoie del portafoglio", "settingsAppIcon": "Icona dell'app", "settingsAppIconDefault": "Predefinita", "settingsAppIconLight": "<PERSON><PERSON>", "settingsAppIconDark": "<PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "Account", "settingsSearchResultSelected": "Selezionato", "settingsSearchResultExport": "Esporta", "settingsSearchResultSeed": "Seed", "settingsSearchResultTrusted": "Affidabile", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Stato", "settingsSearchResultLogs": "<PERSON><PERSON>", "settingsSearchResultBiometric": "Biometrico", "settingsSearchResultTouch": "Tocco", "settingsSearchResultFace": "Viso", "settingsSearchResultShortcuts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsAllSitesPermissionsTitle": "Accedi a Phantom su tutti i siti", "settingsAllSitesPermissionsSubtitle": "Ti consente di utilizzare le app senza problemi con Phantom senza fare clic sull'estensione", "settingsAllSitesPermissionsDisabled": "Il tuo browser non supporta la modifica di questa impostazione", "settingsSolanaCopyTransaction": "Abilita la copia della transazione", "settingsSolanaCopyTransactionDetails": "Copia i dati della transazione serializzata negli appunti", "settingsAutoConfirmHeader": "Conferma automatica", "refreshWebpageToApplyChanges": "Aggiorna la pagina web per applicare le modifiche", "settingsExperimentalTitle": "Funzionalità sperimentali", "settingsExprimentalSolanaActionsSubtitle": "Espandi automaticamente i pulsanti di azione di Solana quando vengono rilevati collegamenti pertinenti su X.com", "stakeAccountCardActiveStake": "Stake attivo", "stakeAccountCardBalance": "<PERSON><PERSON>", "stakeAccountCardRentReserve": "Riserva di affitto", "stakeAccountCardRewards": "<PERSON><PERSON><PERSON> premio", "stakeAccountCardRewardsTooltip": "Questo è il premio più recente che hai guadagnato per lo staking. Ricevi un premio ogni 3 giorni.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "Blocco fino al", "stakeRewardsHistoryTitle": "Cronologia premi", "stakeRewardsActivityItemTitle": "<PERSON><PERSON>", "stakeRewardsHistoryEmptyList": "<PERSON><PERSON><PERSON> premio", "stakeRewardsTime_zero": "<PERSON><PERSON><PERSON>", "stakeRewardsTime_one": "<PERSON><PERSON>", "stakeRewardsTime_other": "{{count}} giorni fa", "stakeRewardsItemsPagingFailed": "Impossibile caricare i premi meno recenti.", "stakeAccountCreateAndDelegateErrorStaking": "Si è verificato un problema con lo stake di questo validatore. Riprova.", "stakeAccountCreateAndDelegateSolStaked": "SOL in stake!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "I tuoi SOL inizieranno a guadagnare premi <1></1> nei prossimi due giorni una volta che il conto di stake sarà attivo.", "stakeAccountCreateAndDelegateStakingFailed": "Stake non riuscito", "stakeAccountCreateAndDelegateStakingSol": "Stake SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Stiamo creando un account di staking, quindi delegheremo i tuoi SOL a", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Stiamo creando un account di staking, quindi delegheremo i tuoi SOL a {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Mostra transazione", "stakeAccountDeactivateStakeSolUnstaked": "SOL non più in stake!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Potrai ritirare il tuo stake <1></1> nei prossimi due giorni una volta che l'account di stake diventa inattivo.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Potrai ritirare il tuo stake nei prossimi due giorni una volta che l'account di stake diventa inattivo.", "stakeAccountDeactivateStakeUnstakingFailed": "Rimozione da stake non riuscita", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Si è verificato un problema con la rimozione dello stake di questo validatore. Riprova.", "stakeAccountDeactivateStakeUnstakingSol": "Rimozione stake SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Stiamo avviando il processo per annullare lo stake dei tuoi SOL.", "stakeAccountDeactivateStakeViewTransaction": "Mostra transazione", "stakeAccountDelegateStakeSolStaked": "SOL in stake!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "I tuoi SOL inizieranno a guadagnare premi <1></1> nei prossimi due giorni una volta che il conto di stake sarà attivo.", "stakeAccountDelegateStakeStakingFailed": "Stake non riuscito", "stakeAccountDelegateStakeStakingFailedDescription": "Si è verificato un problema con lo stake di questo validatore. Riprova.", "stakeAccountDelegateStakeStakingSol": "Stake SOL...", "stakeAccountDelegateStakeStakingSolDescription": "Stiamo delegando i tuoi SOL.", "stakeAccountDelegateStakeViewTransaction": "Mostra transazione", "stakeAccountListActivationActivating": "Attivazione", "stakeAccountListActivationActive": "Attivo", "stakeAccountListActivationInactive": "Non attivo", "stakeAccountListActivationDeactivating": "In disattivazione", "stakeAccountListErrorFetching": "Impossibile recuperare gli account di staking. Riprova più tardi.", "stakeAccountListNoStakingAccounts": "Nessun account di stake", "stakeAccountListReload": "Ricarica", "stakeAccountListViewPrimaryText": "Il tuo stake", "stakeAccountListViewStakeSOL": "Stake SOL", "stakeAccountListItemStakeFee": "Commissione del {{fee}}", "stakeAccountViewActionButtonRestake": "Rifai stake", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>", "stakeAccountViewError": "Errore", "stakeAccountViewPrimaryText": "Il tuo stake", "stakeAccountViewRestake": "Rifai stake", "stakeAccountViewSOLCurrentlyStakedInterpolated": "I tuoi SOL sono attualmente in stake con un validatore. Dovrai annullare lo stake per <1></1>accedere a questi fondi. <3>Ulteriori informazioni</3>", "stakeAccountViewStakeInactive": {"part1": "Questo account di stake è inattivo. Considera la possibilità di ritirare lo stake o di trovare un validatore a cui delegare.", "part2": "Ulteriori informazioni"}, "stakeAccountViewStakeNotFound": "Impossibile trovare questo account di stake.", "stakeAccountViewViewOnExplorer": "<PERSON>ra su {{explorer}}", "stakeAccountViewWithdrawStake": "Ritira stake", "stakeAccountViewWithdrawUnstakedSOL": "Preleva SOL non in stake", "stakeAccountInsufficientFunds": "SOL disponibile insufficiente per rimuovere lo stake o prelevare.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL prelevati!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "I tuoi SOL sono stati prelevati.", "part2": "Questo account di stake verrà automaticamente rimosso entro pochi minuti."}, "stakeAccountWithdrawStakeViewTransaction": "Mostra transazione", "stakeAccountWithdrawStakeWithdrawalFailed": "Prelievo fallito", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Si è verificato un problema col prelievo di questo account di stake. Riprova.", "stakeAccountWithdrawStakeWithdrawingSol": "Prelievo SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Stiamo prelevando i tuoi SOL da questo account di stake.", "startEarningSolAccount": "account", "startEarningSolAccounts": "account", "startEarningSolErrorClosePhantom": "Tocca qui e riprova", "startEarningSolErrorTroubleLoading": "Problema durante il caricamento dello stake", "startEarningSolLoading": "Caricamento...", "startEarningSolPrimaryText": "Inizia a guadagnare SOL", "startEarningSolSearching": "Ricerca di account di stake in corso", "startEarningSolStakeTokens": "Fai stake dei token e guadagna premi", "startEarningSolYourStake": "Il tuo stake", "unwrapFungibleTitle": "Scambia in {{tokenSymbol}}", "unwrapFungibleDescription": "<PERSON><PERSON><PERSON> da {{fromToken}} in cambio di {{toToken}}", "unwrapFungibleConfirmSwap": "Conferma scambio", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Commissioni stimate", "swapFeesFees": "Commissioni", "swapFeesPhantomFee": "Commissione Phantom", "swapFeesPhantomFeeDisclaimer": "Troviamo sempre il miglior prezzo possibile dai principali fornitori di liquidità. In questo preventivo, viene automaticamente tenuto conto di una commissione del {{feePercentage}}.", "swapFeesRate": "Prezzo", "swapFeesRateDisclaimer": "La migliore tariffa trovata da Jupiter Aggregator su più scambi decentralizzati.", "swapFeesRateDisclaimerMultichain": "La migliore tariffa trovata su più scambi decentralizzati.", "swapFeesPriceImpact": "Impatto sul prezzo", "swapFeesHighPriceImpact": "Alto impatto sul prezzo", "swapFeesPriceImpactDisclaimer": "La differenza tra il prezzo di mercato e il prezzo stimato in base alla dimensione dell'operazione.", "swapFeesSlippage": "Slittamento", "swapFeesHighSlippage": "Alta tolleranza allo slittamento", "swapFeesHighSlippageDisclaimer": "La tua transazione fallirà se il prezzo cambia sfavorevolmente più del {{slippage}}%.", "swapTransferFee": "Commissione di trasferimento", "swapTransferFeeDisclaimer": "Lo scambio di {{symbol}} $ comporta una commissione di trasferimento del {{feePercent}}% stabilita dal creatore del token, non da Phantom.", "swapTransferFeeDisclaimerMany": "Lo scambio dei token selezionati comporta una commissione del {{feePercent}}% stabilita dai creatori del token, non da Phantom.", "swapFeesSlippageDisclaimer": "Importo per il quale il prezzo della tua operazione può discostarsi dalla quotazione fornita.", "swapFeesProvider": "Fornitore", "swapFeesProviderDisclaimer": "Lo scambio decentralizzato utilizzato per completare la tua operazione.", "swapEstimatedTime": "Tempo stimato", "swapEstimatedTimeShort": "Tempo stimato", "swapEstimatedTimeDisclaimer": "Il tempo stimato per il completamento del ponte varierà in base a diversi fattori che influiscono sulla velocità delle transazioni.", "swapSettingsButtonCommand": "Apri Impostazioni di scambio", "swapQuestionRetry": "R<PERSON>rovare?", "swapUnverifiedTokens": "Token non verificati", "swapSectionTitleTokens": "Token {{section}}", "swapFlowYouPay": "<PERSON><PERSON><PERSON>", "swapFlowYouReceive": "<PERSON><PERSON>", "swapFlowActionButtonText": "Controlla l'ordine", "swapAssetCardTokenNetwork": "{{symbol}} su {{network}}", "swapAssetCardMaxButton": "Max", "swapAssetCardSelectTokenAndNetwork": "Seleziona token e rete", "swapAssetCardBuyTitle": "<PERSON><PERSON>", "swapAssetCardSellTitle": "<PERSON><PERSON><PERSON>", "swapAssetWarningUnverified": "Questo token non è verificato. Opera solo con token di cui ti fidi.", "swapAssetWarningPermanentDelegate": "Un delegato può bruciare o trasferire definitivamente questi token.", "swapSlippageSettingsTitle": "Impostazioni di slittamento", "swapSlippageSettingsSubtitle": "La tua transazione fallirà se il prezzo cambia più dello slittamento. Un valore troppo alto comporterà un'operazione sfavorevole.", "swapSlippageSettingsCustom": "Personalizza", "swapSlippageSettingsHighSlippageWarning": "La tua transazione potrebbe andare in frontrun e risultare in un'operazione sfavorevole.", "swapSlippageSettingsCustomMinError": "Inserisci un valore maggiore di {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "Inserisci un valore inferiore a {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "Inserisci un valore valido.", "swapSlippageSettingsAutoSubtitle": "Phantom troverà lo slittamento più basso per riuscire nello scambio.", "swapSlippageSettingsAuto": "Automatico", "swapSlippageSettingsFixed": "<PERSON><PERSON>", "swapSlippageOptInTitle": "Slittamento automatico", "swapSlippageOptInSubtitle": "Phantom identificherà lo slittamento più basso per uno scambio. <PERSON><PERSON>i modificarlo in qualsiasi momento in Swapper → Impostazioni slittamento.", "swapSlippageOptInEnableOption": "Abilita lo slittamento automatico", "swapSlippageOptInRejectOption": "Continua con lo slittamento fisso", "swapQuoteFeeDisclaimer": "Il preventivo include una commissione Phantom del {{feePercentage}}", "swapQuoteMissingContext": "Contesto di scambio mancante", "swapQuoteErrorNoQuotes": "Stai cercando di scambiare senza contesto", "swapQuoteSolanaNetwork": "<PERSON><PERSON>", "swapQuoteNetwork": "Rete", "swapQuoteOneTimeSerumAccount": "Account Serum una tantum", "swapQuoteOneTimeTokenAccount": "Account token una tantum", "swapQuoteBridgeFee": "Commissione di scambio cross-catena", "swapQuoteDestinationNetwork": "Rete di destinazione", "swapQuoteLiquidityProvider": "Fornitore di liquidità", "swapReviewFlowActionButtonPrimary": "Scambio", "swapReviewFlowPrimaryText": "Controlla l'ordine", "swapReviewFlowYouPay": "<PERSON><PERSON><PERSON>", "swapReviewFlowYouReceive": "<PERSON><PERSON>", "swapReviewInsufficientBalance": "Fondi insufficienti", "ugcSwapWarningTitle": "Avviso", "ugcSwapWarningBody1": "Questo token viene scambiato sul token launcher {{programName}}.", "ugcSwapWarningBody2": "Il valore di questi token può variare notevolmente, portando a sostanziali guadagni o perdite finanziarie. Fai trading a tuo rischio.", "ugcSwapWarningConfirm": "Capisco", "bondingCurveProgressLabel": "Progresso della curva di bonding", "bondingCurveInfoTitle": "Curva di bonding", "bondingCurveInfoDescription": "In un modello della curva di bonding, i prezzi dei token sono determinati dalla forma della curva, aumentando man mano che vengono acquistati più token e diminuendo man mano che vengono venduti. Quando i token vengono esauriti, tutta la liquidità verrà depositata in Raydium e bruciata.", "ugcFungibleWarningBanner": "Questo token è scambiato su {{programName}}", "ugcCreatedRowLabel": "Creato il", "ugcStatusRowLabel": "Stato", "ugcStatusRowValue": "<PERSON><PERSON><PERSON>", "swapTxConfirmationReceived": "Ricevuto!", "swapTxConfirmationSwapFailed": "<PERSON><PERSON><PERSON>", "swapTxConfirmationSwapFailedStaleQuota": "Questa quotazione non è più valida. Riprova.", "swapTxConfirmationSwapFailedSlippageLimit": "Il tuo slittamento è troppo basso per questo scambio. Aumenta lo slittamento nella parte superiore della schermata Scambia e riprova.", "swapTxConfirmationSwapFailedInsufficientBalance": "Non siamo riusciti a completare la richiesta. Non hai saldo sufficiente per completare la transazione.", "swapTxConfirmationSwapFailedEmptyRoute": "La liquidità per questa coppia di token è cambiata. Non siamo riusciti a trovare una quotazione adatta. Riprova o modifica gli importi dei token.", "swapTxConfirmationSwapFailedAcountFrozen": "Questo token è stato congelato dal suo creatore. Impossibile inviare o scambiare il token.", "swapTxConfirmationSwapFailedTryAgain": "Lo scambio non è riuscito, riprova", "swapTxConfirmationSwapFailedUnknownError": "Non siamo riusciti a completare lo scambio. Non c'è stato alcun impatto sui tuoi fondi. Riprova. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Non siamo riusciti a simulare lo scambio. Non c'è stato alcun impatto sui tuoi fondi. Riprova.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Non siamo riusciti a completare lo scambio. Non c'è stato alcun impatto sui tuoi fondi. Riprova. ", "swapTxConfirmationSwapFailedInsufficientGas": "Il tuo account non dispone di fondi sufficienti per completare la transazione. Aggiungi più fondi al tuo account e riprova.", "swapTxConfirmationSwapFailedLedgerReject": "Lo scambio è stato rifiutato dall'utente sul dispositivo hardware.", "swapTxConfirmationSwapFailedLedgerConnectionError": "Lo scambio è stato rifiutato a causa di un errore di connessione del dispositivo. Riprova.", "swapTxConfirmationSwapFailedLedgerSignError": "Lo scambio è stato rifiutato a causa di un errore di firma del dispositivo. Riprova.", "swapTxConfirmationSwapFailedLedgerError": "Lo scambio è stato rifiutato a causa di un errore del dispositivo. Riprova.", "swapTxConfirmationSwappingTokens": "Scambio di token...", "swapTxConfirmationTokens": "I token", "swapTxConfirmationTokensDeposited": "Fatto! I token sono stati versati nel tuo portafoglio", "swapTxConfirmationTokensDepositedTitle": "Fatto!", "swapTxConfirmationTokensDepositedBody": "I token sono stati versati nel tuo portafoglio", "swapTxConfirmationTokensWillBeDeposited": "verranno versati nel tuo portafoglio una volta completata la transazione", "swapTxConfirmationViewTransaction": "Mostra transazione", "swapTxBridgeSubmitting": "Invio della transazione", "swapTxBridgeSubmittingDescription": "Scambio di {{sellAmount}} su {{sellNetwork}} con {{buyAmount}} su {{buyNetwork}}", "swapTxBridgeFailed": "Impossibile inviare la transazione", "swapTxBridgeFailedDescription": "Non siamo riusciti a completare la richiesta.", "swapTxBridgeSubmitted": "Transazione inviata", "swapTxBridgeSubmittedDescription": "Tempo stimato di transazione: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "Puoi tranquillamente chiudere questa finestra.", "swapperSwitchTokens": "Cambia token", "swapperMax": "Max", "swapperTooltipNetwork": "Rete", "swapperTooltipPrice": "Prezzo", "swapperTooltipAddress": "<PERSON><PERSON><PERSON>", "swapperTrendingSortBy": "Ordina per", "swapperTrendingTimeFrame": "Intervallo di tempo", "swapperTrendingNetwork": "Rete", "swapperTrendingRank": "Posizione", "swapperTrendingTokens": "Token di tendenza", "swapperTrendingVolume": "Volume", "swapperTrendingPrice": "Prezzo", "swapperTrendingPriceChange": "Cambio di prezzo", "swapperTrendingMarketCap": "Market Cap", "swapperTrendingTimeFrame1h": "1o", "swapperTrendingTimeFrame24h": "24o", "swapperTrendingTimeFrame7d": "7g", "swapperTrendingTimeFrame30d": "30g", "swapperTrendingNoTokensFound": "N<PERSON>un token trovato.", "switchToggle": "Interruttore", "termsOfServiceActionButtonAgree": "Accetto", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Facendo clic su <1>\"Accetto\"</1> accetti i <3>Termini e condizioni</3> relativi allo scambio di token con Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Abbiamo rivisto i nostri Termini di servizio. Facendo clic su <1>\"Accetto\"</1> accetti i nostri nuovi <3>Termini di servizio</3>.<5></5><6></6>I nostri nuovi Termini di servizio includono un nuova <8>struttura tariffaria</8> per determinati prodotti.", "termsOfServicePrimaryText": "Termini di servizio", "tokenRowUnknownToken": "<PERSON><PERSON> s<PERSON>", "transactionsAppInteraction": "Interazione con l'app", "transactionsFailedAppInteraction": "Interazione con l'app non riuscita", "transactionsBidOnInterpolated": "Fai un'offerta per {{name}}", "transactionsBidFailed": "Offerta non riuscita", "transactionsBoughtInterpolated": "{{name}} ac<PERSON><PERSON>", "transactionsBoughtCollectible": "Collezionabile acquistato", "transactionBridgeInitiated": "Ponte iniziato", "transactionBridgeInitiatedFailed": "Inizio del ponte non riuscito", "transactionBridgeStatusLink": "Controlla lo stato su LI.FI", "transactionsBuyFailed": "Acquisto non riuscito", "transactionsBurnedSpam": "Spam bruciato", "transactionsBurned": "<PERSON><PERSON><PERSON><PERSON>", "transactionsUnwrapped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsUnwrappedFailed": "Spacchettamento non riuscito", "transactionsCancelBidOnInterpolated": "Offerta annullata su {{name}}", "transactionsCancelBidOnFailed": "Impossibile annullare l'offerta", "transactionsError": "Errore", "transactionsFailed": "Non riuscita", "transactionsSwapped": "Scambiato", "transactionsFailedSwap": "Scambio non riuscito", "transactionsFailedBurn": "Bruciatura non riuscita", "transactionsFrom": "Da", "transactionsListedInterpolated": "{{name}} messo in vendita", "transactionsListedFailed": "Messa in listino non riuscita", "transactionsNoActivity": "<PERSON><PERSON><PERSON> attivit<PERSON>", "transactionsReceived": "Rice<PERSON><PERSON>", "transactionsReceivedInterpolated": "Ricevu<PERSON> {{amount}} SOL", "transactionsSending": "Invio...", "transactionsPendingCreateListingInterpolated": "Creazione {{name}}", "transactionsPendingEditListingInterpolated": "Modifica {{name}}", "transactionsPendingSolanaPayTransaction": "Conferma della transazione Solana Pay", "transactionsPendingRemoveListingInterpolated": "R<PERSON><PERSON> {{name}} dalla vendita", "transactionsPendingBurningInterpolated": "Bruciatura {{name}}", "transactionsPendingSending": "Invio", "transactionsPendingSwapping": "Scambio", "transactionsPendingBridging": "Bridge in corso", "transactionsPendingApproving": "Approvazione", "transactionsPendingCreatingAndDelegatingStake": "Creazione e delega dello staking", "transactionsPendingDeactivatingStake": "Disattivazione staking", "transactionsPendingDelegatingStake": "Delega staking", "transactionsPendingWithdrawingStake": "Prelievo stake", "transactionsPendingAppInteraction": "In attesa di interazione con l'app", "transactionsPendingBitcoinTransaction": "Transazione BTC in sospeso", "transactionsSent": "Inviata", "transactionsSendFailed": "<PERSON>vio non riuscito", "transactionsSwapOn": "Scambia su {{dappName}}", "transactionsSentInterpolated": "Inviati {{amount}} SOL", "transactionsSoldInterpolated": "{{name}} venduto", "transactionsSoldCollectible": "Collezionabile venduto", "transactionsSoldFailed": "Vendita non riuscita", "transactionsStaked": "In stake", "transactionsStakedFailed": "Stake non riuscito", "transactionsSuccess": "<PERSON><PERSON><PERSON><PERSON>", "transactionsTo": "A", "transactionsTokenSwap": "Scambio di token", "transactionsUnknownAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsUnlistedInterpolated": "{{name}} non più in vendita", "transactionsUnstaked": "<PERSON><PERSON><PERSON> da <PERSON>", "transactionsUnlistedFailed": "Rimozione da listino non riuscita", "transactionsDeactivateStake": "Staking disattivato", "transactionsDeactivateStakeFailed": "Impossibile disattivare lo staking", "transactionsWaitingForConfirmation": "In attesa di conferma", "transactionsWithdrawStake": "Ritira stake", "transactionsWithdrawStakeFailed": "Rimozione da stake non riuscita", "transactionCancelled": "<PERSON><PERSON><PERSON>", "transactionCancelledFailed": "Impossibile annullare", "transactionApproveToken": "{{tokenSymbol}} approvati", "transactionApproveTokenFailed": "Impossibile approvare {{tokenSymbol}}", "transactionApprovalFailed": "Approvazione non riuscita", "transactionRevokeApproveToken": "{{tokenSymbol}} revocati", "transactionRevokeApproveTokenFailed": "Impossibile revocare {{tokenSymbol}}", "transactionRevokeFailed": "Revoca non riuscita", "transactionApproveDetailsTitle": "Dettagli dell'approvazione", "transactionCancelOrder": "<PERSON><PERSON><PERSON> or<PERSON>", "transactionCancelOrderFailed": "Annullamento ordine non riuscito", "transactionApproveAppLabel": "App", "transactionApproveAmountLabel": "Importo", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "Raccolta", "transactionApproveAllItems": "A<PERSON><PERSON>a tutti gli <PERSON>i", "transactionSpendUpTo": "Spendi fino a", "transactionCancel": "Annulla transazione", "transactionPrioritizeCancel": "Dai priorità all'annullamento", "transactionSpeedUp": "Velocizza la transazione", "transactionCancelHelperText": "La transazione originale può essere completata prima di essere annullata.", "transactionSpeedUplHelperText": "<PERSON>iò massimizzerà la velocità della transazione in base alle condizioni della rete.", "transactionCancelHelperMobile": "Tentare di annullare questa transazione costerà <1>fino a {{amount}}</1>. La transazione originale può essere completata prima di essere annullata.", "transactionCancelHelperMobileWithEstimate": "Tentare di annullare questa transazione costerà <1>fino a {{amount}}</1>. <PERSON><PERSON><PERSON> completarsi tra circa {{timeEstimate}}. La transazione originale può essere completata prima di essere annullata.", "transactionSpeedUpHelperMobile": "Costerà <1>fino a {{amount}}</1> massimizzare la velocità di questa transazione.", "transactionSpeedUpHelperMobileWithEstimate": "Costerà <1>fino a {{amount}}</1> massimizzare la velocità di questa transazione. Dovrebbe completarsi tra circa {{timeEstimate}}.", "transactionEstimatedTime": "Tempo stimato", "transactionCancelingSend": "Annullamento inviato", "transactionPrioritizingCancel": "Sto dando priorità all'annullamento", "transactionCanceling": "Annullamento", "transactionReplaceError": "Si è verificato un errore. Nessuna commissione è stata addebitata sul tuo account. Riprova.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} insufficienti", "transactionGasLimitError": "Impossibile stimare il limite del gas", "transactionGasEstimationError": "Impossibile stimare il gas", "pendingTransactionCancel": "<PERSON><PERSON><PERSON>", "pendingTransactionSpeedUp": "V<PERSON><PERSON>zza", "pendingTransactionStatus": "Stato", "pendingTransactionPending": "In sospeso", "pendingTransactionPendingInteraction": "In attesa di interazione", "pendingTransactionCancelling": "Annullamento", "pendingTransactionDate": "Data", "pendingTransactionNetworkFee": "Commissione di rete", "pendingTransactionEstimatedTime": "Tempo stimato", "pendingTransactionEstimatedTimeHM": "{{hours}}o {{minutes}}m", "pendingTransactionEstimatedTimeMS": "{{minutes}}m {{seconds}}s", "pendingTransactionEstimatedTimeS": "{{seconds}}s", "pendingTransactionsSendingTitle": "Invio {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingTransactionUnknownApp": "App sconosciuta", "permanentDelegateTitle": "Delegato", "permanentDelegateValue": "Permanente", "permanentDelegateTooltipTitle": "Delega permanente", "permanentDelegateTooltipValue": "La delega permanente consente a un altro account di gestire i token per tuo conto, anche tramite bruciatura o trasferimento.", "unlockActionButtonUnlock": "S<PERSON><PERSON>ca", "unlockEnterPassword": "Inserisci la tua password", "unlockErrorIncorrectPassword": "Password non corretta", "unlockErrorSomethingWentWrong": "Qualcosa è andato storto, riprova più tardi", "unlockForgotPassword": "Password dimenticata", "unlockPassword": "Password", "forgotPasswordText": "Puoi reimpostare la tua password inserendo la frase di recupero di 12-24 parole del tuo portafoglio. Phantom non può recuperare la tua password per te.", "appInfo": "Informazioni app", "lastUsed": "Ultimo uso", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Non disponibile con account hardware.", "trustedAppAutoConfirmDisclaimer1": "<PERSON><PERSON> è attivo, Phantom confermera tutte le richieste di questa app senza avvisarti o chiedere conferma.", "trustedAppAutoConfirmDisclaimer2": "L'abilitazione può mettere i tuoi fondi a rischio di frode. Utilizza questa funzione solo con le app di cui ti fidi.", "validationUtilsPasswordIsRequired": "Password obbligatoria", "validationUtilsPasswordLength": "La password deve essere lunga 8 caratteri", "validationUtilsPasswordsDontMatch": "Le password non corrispondono", "validationUtilsPasswordCantBeSame": "Non puoi usare la tua vecchia password", "validatorCardEstimatedApy": "APY stimato", "validatorCardCommission": "Commissione", "validatorCardTotalStake": "Stake totale", "validatorCardNumberOfDelegators": "Num di delegatori", "validatorListChooseAValidator": "Scegli un validatore", "validatorListErrorFetching": "Impossibile recuperare i validatori. Riprova più tardi.", "validatorListNoResults": "<PERSON><PERSON><PERSON> r<PERSON>", "validatorListReload": "Ricarica", "validatorInfoTooltip": "Validatore", "validatorInfoTitle": "Validatori", "validatorInfoDescription": "<PERSON><PERSON><PERSON> in staking i tuoi SOL su un validatore, contribuisci alle prestazioni e alla sicurezza della rete Solana, il tutto guadagnando SOL in cambio.", "validatorApyInfoTooltip": "APY stim.", "validatorApyInfoTitle": "APY stimato", "validatorApyInfoDescription": "Questo è il tasso di rendimento che guadagni mettendo in staking i tuoi SOL sul validatore.", "validatorViewActionButtonStake": "Stake", "validatorViewErrorFetching": "Impossibile recuperare i validatori.", "validatorViewInsufficientBalance": "<PERSON><PERSON>", "validatorViewMax": "Max", "validatorViewPrimaryText": "Inizia lo stake", "validatorViewDescriptionInterpolated": "Scegli quanti SOL vuoi <1></1> mettere in stake con questo validatore. <3>Ulteriori informazioni</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL necessari per lo stake", "validatorViewValidator": "Validatore", "walletMenuItemsAddConnectWallet": "Aggiungi/Collega portafoglio", "walletMenuItemsBridgeAssets": "<PERSON>lega risorse", "walletMenuItemsHelpAndSupport": "Supporto", "walletMenuItemsLockWallet": "Blocca portafoglio", "walletMenuItemsResetSecretPhrase": "Reimposta la frase segreta", "walletMenuItemsShowMoreAccounts": "<PERSON>ra altri {{count}}...", "walletMenuItemsHideAccounts": "Nascondi account", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "Modalità solo Solana", "disableMultiChainDetail1Header": "Vai all-in su Solana", "disableMultiChainDetail1SecondaryText": "Gestisci i tuoi account Solana, token e collezionabili senza vedere altre catene.", "disableMultiChainDetail2Header": "<PERSON><PERSON> a Multichain in qualsiasi momento", "disableMultiChainDetail2SecondaryText": "I tuoi saldi Ethereum e Polygon esistenti verranno preservati quando riattivi Multichain.", "disableMultiChainButton": "<PERSON><PERSON><PERSON>", "disabledMultiChainHeader": "Solo Solana abilitato", "disabledMultiChainText": "<PERSON>uoi riattivare multichain in qualsiasi momento.", "enableMultiChainHeader": "Attiva Multichain", "enabledMultiChainHeader": "Multichain at<PERSON><PERSON>o", "enabledMultiChainText": "Ethereum e Polygon sono ora supportati nel tuo portafoglio.", "incompatibleAccountHeader": "Account incompatibile", "incompatibleAccountInterpolated": "Rimuovi questi account solo Ethereum prima di abilitare la modalità solo Solana: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Novità!", "welcomeToMultiChainPrimaryText": "Un portafoglio per tutto", "welcomeToMultiChainDetail1Header": "Supporto a Ethereum e Polygon", "welcomeToMultiChainDetail1SecondaryText": "Tutti i tuoi token e NFT di Solana, Ethereum e Polygon in un unico posto.", "welcomeToMultiChainDetail2Header": "Usa tutte le app che ami", "welcomeToMultiChainDetail2SecondaryText": "Connettiti alle app su più catene senza cambiare rete.", "welcomeToMultiChainDetail3Header": "Importa il tuo wallet MetaMask", "welcomeToMultiChainDetail3SecondaryText": "Importa facilmente tutte le tue frasi seed su Ethereum e Polygon.", "welcomeToMultiChainIntro": "<PERSON><PERSON><PERSON> in Phantom Multichain", "welcomeToMultiChainIntroDesc": "Tutti i tuoi token e NFT di Solana, Ethereum e Polygon in un unico posto. Il tuo unico portafoglio per tutto.", "welcomeToMultiChainAccounts": "Account multichain <PERSON><PERSON><PERSON>", "welcomeToMultiChainAccountsDesc": "Riprogettato per multichain, ogni account ora ha indirizzi ETH e Polygon corrispondenti.", "welcomeToMultiChainApps": "Funziona ovunque", "welcomeToMultiChainAppsDesc": "Phantom è compatibile con tutte le app su Ethereum, Polygon e Solana. Fai clic su \"Connetti a MetaMask\" e parti.", "welcomeToMultiChainImport": "Importa da MetaMask, istantaneamente", "welcomeToMultiChainImportDesc": "Importa le tue frasi segrete o chiavi private da portafogli come MetaMask o Coinbase Wallet. <PERSON><PERSON> in un unico posto.", "welcomeToMultiChainImportInterpolated": "<0>I<PERSON><PERSON> le tue frasi segrete</0> o chiavi private da portafogli come MetaMask o Coinbase Wallet. Tutto in un unico posto.", "welcomeToMultiChainTakeTour": "Fai un giro", "welcomeToMultiChainSwapperTitle": "Scambia su Ethereum,\nPolygon e Solana", "welcomeToMultiChainSwapperDetail1Header": "Supporto a Ethereum e Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Ora puoi scambiare facilmente i token ERC-20 dall'interno del tuo portafoglio.", "welcomeToMultiChainSwapperDetail2Header": "<PERSON><PERSON><PERSON> prezzi e commissioni super basse", "welcomeToMultiChainSwapperDetail2SecondaryText": "Oltre 100 fonti di liquidità e instradamento intelligente degli ordini per massimi rendimenti.", "networkErrorTitle": "Errore di rete", "networkError": "Purtroppo non siamo riusciti ad accedere alla rete. Riprova più tardi.", "authenticationUnlockPhantom": "Sblocca Phantom", "errorAndOfflineSomethingWentWrong": "Qualcosa è andato storto", "errorAndOfflineSomethingWentWrongTryAgain": "R<PERSON>rova.", "errorAndOfflineUnableToFetchAssets": "Impossibile recuperare le risorse. Riprova più tardi.", "errorAndOfflineUnableToFetchCollectibles": "Impossibile recuperare i collezionabili. Riprova più tardi.", "errorAndOfflineUnableToFetchSwap": "Impossibile recuperare le informazioni di scambio. Riprova più tardi.", "errorAndOfflineUnableToFetchTransactionHistory": "Al momento è impossibile recuperare la cronologia delle tue transazioni. Controlla la tua connessione di rete o riprova più tardi.", "errorAndOfflineUnableToFetchRewardsHistory": "Impossibile recuperare la cronologia dei premi. Riprova più tardi.", "errorAndOfflineUnableToFetchBlockedUsers": "Impossibile recuperare gli utenti bloccati. Riprova più tardi.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Qualcosa è andato storto durante la revisione del tuo ordine, riprova.", "sendSelectToken": "Seleziona token", "swapBalance": "Saldo:", "swapTitle": "Scambia i token", "swapSelectToken": "Seleziona token", "swapYouPay": "<PERSON><PERSON><PERSON>", "swapYouReceive": "<PERSON><PERSON>", "aboutPrivacyPolicy": "Informativa sulla privacy", "aboutVersion": "Versione {{version}}", "aboutVisitWebsite": "Visita il sito web", "bottomSheetConnectTitle": "Collega", "A11YbottomSheetConnectTitle": "Foglio inferiore connetti", "A11YbottomSheetCommandClose": "Foglio inferiore rifiuta", "A11YbottomSheetCommandBack": "Foglio inferiore indietro", "bottomSheetSignTypedDataTitle": "<PERSON><PERSON> messaggio", "bottomSheetSignMessageTitle": "<PERSON><PERSON> messaggio", "bottomSheetSignInTitle": "Accedi", "bottomSheetSignInAndConnectTitle": "Accedi", "bottomSheetConfirmTransactionTitle": "Conferma transazione", "bottomSheetConfirmTransactionsTitle": "Conferma transazioni", "bottomSheetSolanaPayTitle": "Richiesta di pagamento Solana", "bottomSheetAdvancedTitle": "Avanzate", "bottomSheetReadOnlyAccountTitle": "Modalità di sola visualizzazione", "bottomSheetTransactionSettingsTitle": "Commissione di rete", "bottomSheetConnectDescription": "La connessione consentirà a questo sito di visualizzare saldi e attività per l'account selezionato.", "bottomSheetSignInDescription": "Firmando questo messaggio dimostrerai di essere proprietario dell'account selezionato. Firma solo i messaggi delle applicazioni di cui ti fidi.", "bottomSheetSignInAndConnectDescription": "L'approvazione consentirà a questo sito di visualizzare saldi e attività per l'account selezionato.", "bottomSheetConfirmTransactionDescription": "Le variazioni di saldo sono stimate. Importi e beni coinvolti non sono garantiti.", "bottomSheetConfirmTransactionsDescription": "Le variazioni di saldo sono stimate. Importi e beni coinvolti non sono garantiti.", "bottomSheetSignTypedDataDescription": "Questa è solo una richiesta di autorizzazione. La transazione potrebbe non essere eseguita immediatamente.", "bottomSheetSignTypedDataSecondDescription": "Le variazioni di saldo sono stimate. Importi e beni coinvolti non sono garantiti.", "bottomSheetSignMessageDescription": "Firmando questo messaggio dimostrerai di essere proprietario dell'account selezionato. Firma solo i messaggi delle applicazioni di cui ti fidi.", "bottomSheetReadOnlyAccountDescription": "Impossibile eseguire questa azione in modalità di sola visualizzazione.", "bottomSheetMessageRow": "Messaggio", "bottomSheetStatementRow": "Dichiarazione", "bottomSheetAutoConfirmRow": "Conferma automatica", "bottomSheetAutoConfirmOff": "Off", "bottomSheetAutoConfirmOn": "On", "bottomSheetAccountRow": "Account", "bottomSheetAdvancedRow": "Avanzate", "bottomSheetContractRow": "Indirizzo del contratto", "bottomSheetSpenderRow": "Indirizzo di chi spende", "bottomSheetNetworkRow": "Rete", "bottomSheetNetworkFeeRow": "Commissione di rete", "bottomSheetEstimatedTimeRow": "Tempo stimato", "bottomSheetAccountRowDefaultAccountName": "Account", "bottomSheetConnectRequestDisclaimer": "Collegati solo a siti web di cui ti fidi", "bottomSheetSignInRequestDisclaimer": "Accedi solo a siti web di cui ti fidi", "bottomSheetSignatureRequestDisclaimer": "Conferma solo se ti fidi di questo sito web.", "bottomSheetFeaturedTransactionDisclaimer": "Visualizzerai un'anteprima della transazione prima di confermare nel passaggio successivo.", "bottomSheetIgnoreWarning": "Ignora l'avviso, procedi comunque", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Nessuna modifica al saldo trovata. Procedi con cautela e conferma solo se ti fidi di questo sito.", "bottomSheetReadOnlyWarning": "Stai solo osservando questo indirizzo. Dovrai importare per firmare transazioni e messaggi.", "bottomSheetWebsiteIsUnsafeWarning": "Questo sito web non è sicuro da utilizzare e potrebbe tentare di rubare i tuoi fondi.", "bottomSheetViewOnExplorer": "<PERSON>ra su <PERSON>", "bottomSheetTransactionSubmitted": "Transazione inviata", "bottomSheetTransactionPending": "Transazione in sospeso", "bottomSheetTransactionFailed": "Transazione non riuscita", "bottomSheetTransactionSubmittedDescription": "La tua transazione è stata inviata. Puoi visualizzarla su explorer.", "bottomSheetTransactionFailedDescription": "La transazione ha avuto esito negativo. Riprova.", "bottomSheetTransactionPendingDescription": "La transazione è in fase di elaborazione...", "transactionsFromInterpolated": "Da: {{from}}", "transactionsFromParagraphInterpolated": "Da {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON><PERSON>", "transactionsToInterpolated": "A: {{to}}", "transactionsToParagraphInterpolated": "A {{to}}", "transactionsYesterday": "<PERSON><PERSON>", "addEditAddressAdd": "Aggiungi indirizzo", "addEditAddressDelete": "Elimina indirizzo", "addEditAddressDeleteTitle": "Vuoi davvero eliminare questo indirizzo?", "addEditAddressSave": "<PERSON><PERSON> in<PERSON>", "dAppBrowserComingSoon": "Browser dApp in arrivo!", "dAppBrowserSearchPlaceholder": "Siti, token, URL", "dAppBrowserOpenInNewTab": "Apri in una nuova scheda", "dAppBrowserSuggested": "Consigliato", "dAppBrowserFavorites": "Preferiti", "dAppBrowserBookmarks": "Se<PERSON><PERSON><PERSON>", "dAppBrowserBookmarkAdd": "Aggiungi segnalibro", "dAppBrowserBookmarkRemove": "<PERSON><PERSON><PERSON><PERSON> segnali<PERSON>", "dAppBrowserUsers": "<PERSON><PERSON><PERSON>", "dAppBrowserRecents": "<PERSON><PERSON>", "dAppBrowserFavoritesDescription": "I tuoi preferiti verranno mostrati qui", "dAppBrowserBookmarksDescription": "I tuoi segnalibri verranno mostrati qui", "dAppBrowserRecentsDescription": "Le dapp collegate di recente appariranno qui", "dAppBrowserEmptyScreenDescription": "Digita un URL o cerca nel Web", "dAppBrowserBlocklistScreenTitle": "{{origin}} è bloccato! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom ritiene che questo sito Web sia dannoso e non sicuro da usare.", "part2": "Questo sito è stato segnalato come parte di un database gestito dalla community di siti Web di phishing e truffe noti. Se ritieni che il sito sia stato segnalato per errore, segnala un problema."}, "dAppBrowserLoadFailedScreenTitle": "Caricamento non riuscito", "dAppBrowserLoadFailedScreenDescription": "Si è verificato un errore nel caricamento di questa pagina", "dAppBrowserBlocklistScreenIgnoreButton": "Ignora l'avviso, mostra comunque", "dAppBrowserActionBookmark": "Se<PERSON><PERSON><PERSON>", "dAppBrowserActionRemoveBookmark": "<PERSON><PERSON><PERSON><PERSON> segnali<PERSON>", "dAppBrowserActionRefresh": "Ricarica", "dAppBrowserActionShare": "Condi<PERSON><PERSON>", "dAppBrowserActionCloseTab": "<PERSON><PERSON> s<PERSON>a", "dAppBrowserActionEndAutoConfirm": "Fine conferma automatica", "dAppBrowserActionDisconnectApp": "Disconnetti app", "dAppBrowserActionCloseAllTabs": "<PERSON><PERSON> tutte le schede", "dAppBrowserNavigationAddressPlaceholder": "Digita un URL da cercare", "dAppBrowserTabOverviewMore": "Altro", "dAppBrowserTabOverviewAddTab": "Aggiungi scheda", "dAppBrowserTabOverviewClose": "<PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON> s<PERSON>a", "dAppBrowserClose": "<PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "Aggiungi segnalibro", "dAppBrowserTabOverviewRemoveBookmark": "<PERSON><PERSON><PERSON><PERSON> segnali<PERSON>", "depositAssetListSuggestions": "<PERSON><PERSON><PERSON><PERSON>", "depositUndefinedToken": "S<PERSON><PERSON>i, non è possibile depositare questo token", "onboardingImportRecoveryPhraseDetails": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseVerifyTitle": "Hai trascritto la frase segreta per il recupero?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Senza la frase di recupero segreta non potrai accedere alla tua chiave o alle risorse a essa associate.", "onboardingCreateRecoveryPhraseVerifyYes": "Sì", "onboardingCreateRecoveryPhraseErrorTitle": "Errore", "onboardingCreateRecoveryPhraseErrorSubtitle": "Non siamo riusciti a generare un account, riprova.", "onboardingDoneDescription": "Ora puoi goderti appieno il tuo portafoglio.", "onboardingDoneGetStarted": "Inizia", "zeroBalanceHeading": "Si comincia!", "zeroBalanceBuyCryptoTitle": "Acquist<PERSON> criptovalute", "zeroBalanceBuyCryptoDescription": "Acquista la tua prima criptovaluta con una carta di debito o di credito.", "zeroBalanceDepositTitle": "Trasfer<PERSON><PERSON> criptovaluta", "zeroBalanceDepositDescription": "Deposita criptovalute da un altro portafoglio o exchange.", "onboardingImportAccountsEmptyResult": "Nessun account trovato", "onboardingImportAccountsAccountName": "Conto {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Account social", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Abbiamo rilevato delle attività su {{numberOfWallets}} conto", "onboardingImportAccountsFoundAccounts_other": "Abbiamo rilevato delle attività su {{numberOfWallets}} conti", "onboardingImportAccountsFoundAccountsNoActivity_one": "Abbiam<PERSON> trovato {{numberOfWallets}} conto", "onboardingImportAccountsFoundAccountsNoActivity_other": "Abb<PERSON><PERSON> trovato {{numberOfWallets}} conti", "onboardingImportRecoveryPhraseLessThanTwelve": "La frase deve essere di almeno 12 parole.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "La frase deve essere esattamente di 12 o 24 parole.", "onboardingImportRecoveryPhraseWrongWord": "Parole errate: {{ words }}.", "onboardingProtectTitle": "Proteggi il tuo portafoglio", "onboardingProtectDescription": "L'aggiunta di sicurezza biometrica assicurerà che solo tu sia possa accedere al tuo portafoglio.", "onboardingProtectButtonHeadlineDevice": "Dispositivo", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Impronta digitale", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Usa l'autenticazione {{ authType }}", "onboardingProtectError": "Qualcosa è andato storto durante l'autenticazione, riprova", "onboardingProtectBiometryIosError": "L'autenticazione biometrica è configurata in Phantom ma disabilitata in Impostazioni di sistema. Apri Impostazioni > Phantom > Face ID o Touch ID per riattivare.", "onboardingProtectRemoveAuth": "Disabilita l'autenticazione", "onboardingProtectRemoveAuthDescription": "Vuoi davvero disabilitare l'autenticazione?", "onboardingWelcomeTitle": "<PERSON><PERSON><PERSON> su <PERSON>", "onboardingWelcomeDescription": "Per iniziare, crea un nuovo portafoglio o importane uno esistente.", "onboardingWelcomeCreateWallet": "Crea un nuovo portafoglio", "onboardingWelcomeAlreadyHaveWallet": "Ho già un portafoglio", "onboardingWelcomeConnectSeedVault": "<PERSON><PERSON><PERSON>", "onboardingSlide1Title": "Controllato da te", "onboardingSlide1Description": "Il tuo portafoglio è protetto tramite accesso biometrico, rilevamento delle truffe e assistenza 24 ore su 24, 7 giorni su 7.", "onboardingSlide2Title": "Il posto migliore\nper i tuoi NFT", "onboardingSlide2Description": "Gest<PERSON>ci le inserzioni, elimina lo spam e rimani aggiornato con utili notifiche push.", "onboardingSlide3Title": "Fai di più con i tuoi token", "onboardingSlide3Description": "Conserva, scambia, fai staking, invia e ricevi senza mai lasciare il tuo portafoglio. ", "onboardingSlide4Title": "Scopri il meglio di Web3", "onboardingSlide4Description": "Trova e connettiti alle principali app e raccolte con il browser in-app.", "onboardingMultichainSlide5Title": "Un portafoglio per tutto", "onboardingMultichainSlide5Description": "Prova tutto di Solana, Ethereum e Polygon in un'unica interfaccia intuitiva.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Prova tutto di Solana, Ethereum, Polygon e Bitcoin in un'unica interfaccia intuitiva.", "requireAuth": "Occorre l'autenticazione", "requireAuthImmediately": "Immediatamente", "availableToSend": "Disponibile per l'invio", "sendEnterAmount": "Inserisci l'importo", "sendEditMemo": "Modifica nota", "sendShowLogs": "Mostra registri errori", "sendHideLogs": "Nascondi registri errori", "sendGoBack": "Indietro", "sendTransactionSuccess": "I tuoi token sono stati inviati con successo a", "sendInputPlaceholder": "@nomeutente o indirizzo", "sendInputPlaceholderV2": "nome utente o indirizzo", "sendPeopleTitle": "<PERSON>e", "sendDomainTitle": "<PERSON><PERSON>", "sendFollowing": "<PERSON><PERSON><PERSON>", "sendRecentlyUsedAddressLabel": "Usato {{formattedTimestamp}} fa", "sendRecipientAddress": "Indirizzo del destinatario", "sendTokenInterpolated": "Invia {{tokenSymbol}}", "sendPasteFromClipboard": "Incolla dagli appunti", "sendScanQR": "Scansiona il codice QR", "sendTo": "A:", "sendRecipientZeroBalanceWarning": "L'indirizzo di questo portafoglio non ha saldo e non è visualizzato nella cronologia delle ultime transazioni. Assicurati che l'indirizzo sia corretto.", "sendUnknownAddressWarning": "Questo non è un indirizzo con cui hai interagito di recente. Procedi con cautela.", "sendSameAddressWarning": "Questo è il tuo indirizzo attuale. L'invio comporterà commissioni di trasferimento senza altre modifiche al saldo.", "sendMintAccountWarning": "Questo è l'indirizzo di un account di conio. Non puoi inviare fondi a questo indirizzo poiché ciò comporterebbe una perdita permanente.", "sendCameraAccess": "Accesso alla fotocamera", "sendCameraAccessSubtitle": "Per eseguire la scansione di un codice QR, è necessario abilitare l'accesso alla fotocamera. Vuoi aprire le Impostazioni ora?", "sendSettings": "Impostazioni", "sendOK": "OK", "invalidQRCode": "Questo codice QR non è valido.", "sendInvalidQRCode": "Questo codice QR non è un indirizzo valido", "sendInvalidQRCodeSubtitle": "Riprova o prova con un altro codice QR.", "sendInvalidQRCodeSplToken": "Token non valido nel codice QR", "sendInvalidQRCodeSplTokenSubtitle": "Questo codice QR contiene un token che non possiedi o impossibile da identificare.", "sendScanAddressToSend": "Scansiona l'indirizzo {{tokenSymbol}} per inviare fondi", "sendScanAddressToSendNoSymbol": "Scansiona l'indirizzo per inviare fondi", "sendScanAddressToSendCollectible": "Scansiona l'indirizzo SOL per inviare il collezionabile", "sendScanAddressToSendCollectibleMultichain": "Scansiona l'indirizzo per inviare il collezionabile", "sendSummary": "Resoconto", "sendUndefinedToken": "<PERSON><PERSON><PERSON><PERSON>, non è possibile inviare questo token", "sendNoTokens": "Nessun token disponibile", "noBuyOptionsAvailableInCountry": "Nessuna opzione di acquisto disponibile nel tuo paese", "swapAvailableTokenDisclaimer": "È disponibile un numero limitato di token per il ponte tra le reti", "swapCrossSwapNetworkTooltipTitle": "Scambio tra reti", "swapCrossSwapNetworkTooltipDescription": "Quando si effettuano scambi tra reti, si consiglia di utilizzare i token disponibili per il prezzo più basso e le transazioni più veloci.", "settingsAbout": "Informazioni su Phantom", "settingsShareAppWithFriends": "Invita i tuoi amici", "settingsConfirm": "Sì", "settingsMakeSureNoOneIsWatching": "Assicurati che nessuno stia guardando il tuo schermo", "settingsManageAccounts": "Gestisci account", "settingsPrompt": "Vuoi davvero continuare?", "settingsSelectAvatar": "Selezione avatar", "settingsSelectSecretPhrase": "Seleziona la frase segreta", "settingsShowPrivateKey": "Tocca per rivelare la tua chiave privata", "settingsShowRecoveryPhrase": "Tocca per rivelare la tua frase segreta", "settingsSubmitBetaFeedback": "Invia feedback sulla versione beta", "settingsUpdateAccountNameToast": "Nome account aggiornato", "settingsUpdateAvatarToast": "Avatar aggiornato", "settingsUpdateAvatarToastFailure": "Impossibile aggiornare l'avatar!", "settingsWalletAddress": "Indirizzo del conto", "settingsWalletAddresses": "Indirizzi del conto", "settingsWalletNamePrimary": "Nome del conto", "settingsPlaceholderName": "Nome", "settingsWalletNameSecondary": "Cambia il nome del tuo portafoglio", "settingsYourAccounts": "I tuoi account", "settingsYourAccountsMultiChain": "Multi-catena", "settingsReportUser": "<PERSON><PERSON><PERSON> utente", "settingsNotifications": "Notifiche", "settingsNotificationPreferences": "Preferenze di notifica", "pushNotificationsPreferencesAllowNotifications": "Consenti notifiche", "pushNotificationsPreferencesSentTokens": "Token inviati", "pushNotificationsPreferencesSentTokensDescription": "Trasferimenti in uscita di token e NFT", "pushNotificationsPreferencesReceivedTokens": "Token rice<PERSON>ti", "pushNotificationsPreferencesReceivedTokensDescription": "Trasferimenti in entrata di token e NFT", "pushNotificationsPreferencesDexSwap": "Sc<PERSON><PERSON>", "pushNotificationsPreferencesDexSwapDescription": "Scambia su applicazioni riconosciute", "pushNotificationsPreferencesOtherBalanceChanges": "Altri cambiamenti di saldo", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Altre transazioni multi-token che influiscono sul tuo saldo", "pushNotificationsPreferencesPhantomMarketing": "Aggiornamenti da Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Annunci di funzionalità e aggiornamenti generali", "pushNotificationsPreferencesDescription": "Queste impostazioni controllano le notifiche push per questo portafoglio attivo. Ogni portafoglio ha le proprie impostazioni di notifica. Per disattivare tutte le notifiche push Phantom, vai alle tue <1>impostazioni dispositivo</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Impossibile sincronizzare le preferenze di notifica.", "connectSeedVaultConnectSeed": "Collega un Seed", "connectSeedVaultConnectSeedDescription": "Collega Phantom al Seed Vault sul tuo telefono", "connectSeedVaultSelectAnAccount": "Seleziona un account", "connectSeedVaultSelectASeed": "Seleziona un Seed", "connectSeedVaultSelectASeedDescription": "Scegli quale seed vuoi connettere a Phantom", "connectSeedVaultSelectAnAccountDescription": "<PERSON><PERSON><PERSON> quale account desideri configurare con Phantom", "connectSeedVaultNoAccountsFound": "Nessun account trovato.", "connectSeedVaultSelectAccounts": "Seleziona gli account", "connectSeedVaultSelectAccountsDescription": "Scegli quali account desideri configurare con Phantom", "connectSeedVaultCompleteSetup": "Completa configurazione", "connectSeedVaultCompleteSetupDescription": "Tutto è pronto! Esplora web3 con Phantom e usa il tuo Seed Vault per confermare le transazioni", "connectSeedVaultConnectAnotherSeed": "Collega un altro Seed", "connectSeedVaultConnectAllSeedsConnected": "Tutti i seed sono connessi", "connectSeedVaultNoSeedsConnected": "<PERSON><PERSON><PERSON> Seed collegato. Tocca il pulsante in basso per autorizzare dalla Seed <PERSON>.", "connectSeedVaultConnectAccount": "Connetti account", "connectSeedVaultLoadMore": "Carica altro", "connectSeedVaultNeedPermission": "Serve il permesso", "connectSeedVaultNeedPermissionDescription": "Vai su Impostazioni per consentire a Phantom di utilizzare le autorizzazioni di Seed Vault.", "stakeApy": "APY {{apyPercentage}}", "stakeFee": "Commissione del {{fee}}", "stakeAmount": "Importo", "stakeAmountBalance": "<PERSON><PERSON>", "swapTopQuotes": "Le migliori {{numQuotes}} quotazioni", "swapTopQuotesTitle": "Migliori quotazioni", "swapProvidersTitle": "Fornitori", "swapProvidersFee": "Commissione del {{fee}}", "swapProvidersTagRecommended": "<PERSON><PERSON><PERSON> r<PERSON>", "swapProvidersTagFastest": "<PERSON><PERSON> veloce", "swapProviderEstimatedTimeHM": "{{hours}}o {{minutes}}m", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}}s", "stakeReview": "Controlla", "stakeReviewAccount": "Account", "stakeReviewCommissionFee": "Quota commissione", "stakeReviewConfirm": "Conferma", "stakeReviewValidator": "Validatore", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Conversione dello stake non riuscita", "convertStakeStatusErrorMessage": "Non è stato possibile convertire il tuo stake in {{poolTokenSymbol}}. Riprova.", "convertStakeStatusLoadingTitle": "Conversione in {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Stiamo avviando il processo per convertire i tuoi {{stakedTokenSymbol}} in staking in {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Conversione in {{poolTokenSymbol}} completata!", "convertStakeStatusSuccessMessage": "Guadagna premi aggiuntivi con il tuo JitoSOL <1>qui.</1>", "convertStakeStatusConvertMore": "Converti altro", "convertStakePendingTitle": "Conversione dello stake in {{symbol}}", "convertToJitoSOL": "<PERSON><PERSON><PERSON> in JitoSOL", "convertToJitoSOLInfoDescription": "Converti il ​​tuo SOL in Jito SOL per guadagnare premi e partecipare all'ecosistema Jito.", "convertToJitoSOLInfoTitle": "<PERSON><PERSON><PERSON> in JitoSOL", "convertStakeBannerTitle": "Converti il tuo stake in JitoSOL per aumentare i premi fino al 15%", "convertStakeQuestBannerTitle": "Converti SOL in staking in JitoSOL e guadagna premi. Scopri di più.", "liquidStakeConvertInfoTitle": "<PERSON><PERSON><PERSON> in JitoSOL", "liquidStakeConvertInfoDescription": "Aumenta i tuoi premi convertendo il tuo stake SOL in JitoSOL. <1>Ulteriori informazioni</1>", "liquidStakeConvertInfoFeature1Title": "Perché fare stake con Jito?", "liquidStakeConvertInfoFeature1Description": "Deposita per ottenere JitoSOL, che cresce col tuo stake. Usalo nei protocolli DeFi per guadagni extra. Scambia i tuoi JitoSOL in un secondo momento con importo iniziale + premi maturati", "liquidStakeConvertInfoFeature2Title": "Premi medi più alti", "liquidStakeConvertInfoFeature2Description": "Jito distribuisce i tuoi SOL tra i migliori validatori con le commissioni più basse. I premi MEV aumentano ulteriormente i tuoi guadagni.", "liquidStakeConvertInfoFeature3Title": "Sostieni la rete Solana", "liquidStakeConvertInfoFeature3Description": "Lo staking liquido protegge Solana distribuendo la partecipazione su più validatori, riducendo il rischio derivante da validatori con tempi di attività ridotti.", "liquidStakeConvertInfoSecondaryButton": "Non ora", "liquidStakeStartStaking": "Inizia lo stake", "liquidStakeReviewOrder": "Controlla l'ordine", "convertStakeAccountListPageIneligibleSectionTitle": "Conti non idonei per lo staking", "convertStakeAccountIneligibleBottomSheetTitle": "Conti non idonei per lo staking", "convertStakeAccountListPageErrorTitle": "Impossibile recuperare gli account di staking", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON><PERSON><PERSON>, qualcosa è andato storto e non siamo riusciti a recuperare gli account di staking", "liquidStakeReviewYouPay": "<PERSON><PERSON><PERSON>", "liquidStakeReviewYouReceive": "<PERSON><PERSON>", "liquidStakeReviewProvider": "Fornitore", "liquidStakeReviewNetworkFee": "Commissione di rete", "liquidStakeReviewPageTitle": "Conferma", "liquidStakeReviewConversionFootnote": "Quando fai staking di token Solana in cambio di JitoSOL riceverai un importo leggermente inferiore di JitoSOL. <1>Ulteriori informazioni</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Lo stake pool di Jito supporta la maggior parte dei validatori Solana attivi. Non potrai convertire SOL in staking da validatori non supportati in JitoSOL. Inoltre, i SOL appena messi in staking impiegano circa 2 giorni prima di essere idonei alla conversione in JitoSOL.", "selectAValidator": "Seleziona un validatore", "validatorSelectionListTitle": "Seleziona un validatore", "validatorSelectionListDescription": "Scegli un validatore con cui mettere in staking i tuoi SOL.", "stakeMethodDescription": "Guadagna interessi utilizzando i tuoi token SOL per aiutare Solana a crescere. <1>Ulteriori informazioni</1>", "stakeMethodRecommended": "Consigliato", "stakeMethodEstApy": "APY stim.: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Staking liquido", "stakeMethodSelectionLiquidStakingDescription": "Fai staking di SOL per guadagnare premi più elevati, aiuta a proteggere Solana e ricevi JitoSOL per guadagnare premi aggiuntivi.", "stakeMethodSelectionNativeStakingTitle": "Staking nativo", "stakeMethodSelectionNativeStakingDescription": "Fai staking di SOL per ricevere premi e contribuire a proteggere Solana.", "liquidStakeMintStakeSOL": "Fai staking di SOL", "mintJitoSOLInfoPageTitle": "Ti presentiamo lo staking liquido con Jito", "mintJitoSOLFeature1Title": "Perché fare stake con Jito?", "mintJitoSOLFeature1Description": "Deposita per ottenere JitoSOL, che cresce col tuo stake. Usalo nei protocolli DeFi per guadagni extra. Scambia i tuoi JitoSOL in un secondo momento con importo iniziale + premi maturati", "mintJitoSOLFeature2Title": "Premi medi più alti", "mintJitoSOLFeature2Description": "Jito distribuisce i tuoi SOL tra i migliori validatori con le commissioni più basse. I premi MEV aumentano ulteriormente i tuoi guadagni.", "mintJitoSOLFeature3Title": "Sostieni la rete Solana", "mintJitoSOLFeature3Description": "Lo staking liquido protegge Solana distribuendo la partecipazione su più validatori, riducendo il rischio derivante da validatori con tempi di attività ridotti.", "mintLiquidStakePendingTitle": "Conio di staking liquido", "mintStakeStatusErrorTitle": "Conio di staking liquido non riuscito", "mintStakeStatusErrorMessage": "Non è stato possibile coniare il tuo staking liquido {{poolTokenSymbol}}. Riprova.", "mintStakeStatusSuccessTitle": "Conio di staking liquido di {{poolTokenSymbol}} completato!", "mintStakeStatusLoadingTitle": "Conio di staking liquido di {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "Stiamo avviando il processo per coniare il tuo staking liquido di {{poolTokenSymbol}}.", "mintLiquidStakeAmountDescription": "Scegli quanto SOL vuoi mettere in staking con Jito", "mintLiquidStakeAmountProvider": "Fornitore", "mintLiquidStakeAmountApy": "APY stim.", "mintLiquidStakeAmountBestPrice": "Prezzo", "mintLiquidStakeAmountInsufficientBalance": "<PERSON><PERSON>", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} necessari per lo stake", "swapTooltipGotIt": "Capito", "swapTabInsufficientFunds": "Fondi insufficienti", "swapNoAssetsFound": "Nessun asset", "swapNoTokensFound": "<PERSON><PERSON><PERSON> token trovato", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "swapConfirmationGoBack": "Indietro", "swapNoQuotesFound": "Nessuna quotazione trovata", "swapNotProviderFound": "Non siamo riusciti a trovare un fornitore per questo scambio di token. Prova un altro token.", "swapAvailableOnMainnet": "Questa funzione è disponibile solo su Mainnet", "swapNotAvailableEVM": "Gli swap non sono ancora disponibili per gli account EVM", "swapNotAvailableOnSelectedNetwork": "<PERSON>li scambi non sono disponibili sulla rete selezionata", "singleChainSwapTab": "<PERSON>a rete", "crossChainSwapTab": "Tra più reti", "allFilter": "<PERSON><PERSON>", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bridgeRefuelDescription": "Il rifornimento ti garantisce di poter pagare le transazioni dopo il bridge.", "bridgeRefuelLabelBalance": "I tuoi {{symbol}}", "bridgeRefuelLabelReceive": "<PERSON><PERSON>", "bridgeRefuelLabelFee": "Costo stimato", "bridgeRefuelDismiss": "Continuare senza rifornimento", "bridgeRefuelEnable": "Attiva rifornimento", "unwrapWrappedSolError": "Spacchettamento non riuscito", "unwrapWrappedSolLoading": "Spacchettamento...", "unwrapWrappedSolSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unwrapWrappedSolViewTransaction": "Mostra transazione", "dappApprovePopupSignMessage": "<PERSON><PERSON> messaggio", "solanaPayFrom": "Da", "solanaPayMessage": "Messaggio", "solanaPayNetworkFee": "Commissione di rete", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Paga {{item}}", "solanaPayPayNow": "Paga subito", "solanaPaySending": "<PERSON>vio di {{item}}", "solanaPayReceiving": "Ricezione di {{item}}", "solanaPayMinting": "<PERSON>io di {{item}}", "solanaPayTransactionProcessing": "La tua transazione è in elaborazione,\nattendere prego.", "solanaPaySent": "Inviati!", "solanaPayReceived": "Ricevuti!", "solanaPayMinted": "Coniati!", "solanaPaySentNFT": "NFT inviati!", "solanaPayReceivedNFT": "NFT ricevuti!", "solanaPayTokensSent": "I tuoi token sono stati inviati correttamente a {{to}}", "solanaPayTokensReceived": "<PERSON> ricevuto nuovi token da {{from}}", "solanaPayViewTransaction": "Mostra transazione", "solanaPayTransactionFailed": "Transazione non riuscita", "solanaPayConfirm": "Conferma", "solanaPayTo": "a", "dappApproveConnectViewAccount": "Visualizza il tuo account Solana", "deepLinkInvalidLink": "Link non valido", "deepLinkInvalidSplTokenSubtitle": "Contiene un token che non possiedi o impossibile da identificare.", "walletAvatarShowAllAccounts": "Mostra tutti gli account", "pushNotificationsGetInstantUpdates": "Ricevi aggiornamenti immediati", "pushNotificationsEnablePushNotifications": "Abilita le notifiche push su trasferimenti completati, scambi e annunci", "pushNotificationsEnable": "Abilita", "pushNotificationsNotNow": "Non ora", "onboardingAgreeToTermsOfServiceInterpolated": "Accetto i <1>Termini di servizio</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, l'ho salvata da qualche parte", "onboardingCreateNewWallet": "Crea un nuovo portafoglio", "onboardingErrorDuplicateSecretRecoveryPhrase": "Questa frase segreta esiste già nel tuo portafoglio", "onboardingErrorInvalidSecretRecoveryPhrase": "Frase di recupero segreta non valida", "onboardingFinished": "<PERSON><PERSON>!", "onboardingImportAccounts": "Importa account", "onboardingImportImportingAccounts": "Importazione account...", "onboardingImportImportingFindingAccounts": "Ricerca di account con attività", "onboardingImportAccountsLastActive": "Attivo {{formattedTimestamp}} fa", "onboardingImportAccountsNeverUsed": "<PERSON> usato", "onboardingImportAccountsCreateNew": "Nuovo portafoglio", "onboardingImportAccountsDescription": "Scegli gli account portafoglio da importare", "onboardingImportReadOnlyAccountDescription": "Aggiungi un indirizzo o un nome di dominio che desideri osservare. Avrai accesso di sola visualizzazione e non potrai firmare transazioni o messaggi.", "onboardingImportSecretRecoveryPhrase": "Importa frase segreta", "onboardingImportViewAccounts": "Mostra conti", "onboardingRestoreExistingWallet": "Ripristina un portafoglio esistente con la tua frase di recupero segreta di 12 o 24 parole", "onboardingShowUnusedAccounts": "Mostra account inutilizzati", "onboardingShowMoreAccounts": "Mostra altri account", "onboardingHideUnusedAccounts": "Nascondi account inutilizzati", "onboardingSecretRecoveryPhrase": "Frase di recupero segreta", "onboardingSelectAccounts": "Seleziona i tuoi account", "onboardingStoreSecretRecoveryPhraseReminder": "È l'unico modo in cui potrai recuperare il tuo account. Conser<PERSON>a in un posto sicuro!", "useTokenMetasForMintsUnknownName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "timeUnitMinute": "minuto", "timeUnitMinutes": "minuti", "timeUnitHour": "ora", "timeUnitHours": "ore", "espNFTListWithPrice": "Hai messo in vendita {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}} su {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Hai messo in vendita {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Hai messo in vendita {{NFTDisplayName}} su {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Hai messo in vendita {{NFTDisplayName}}", "espNFTChangeListPriceWithPrice": "Hai aggiornato la vendita di {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}} su {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Hai aggiornato la vendita di {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Hai aggiornato la vendita di {{NFTDisplayName}} su {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Hai aggiornato la vendita di {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Hai offerto {{priceAmount}} {{priceTokenSymbol}} per {{NFTDisplayName}} su {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Hai offerto {{priceAmount}} {{priceTokenSymbol}} per {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Hai effettuato un'offerta per {{NFTDisplayName}} su {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Hai effettuato un'offerta per {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Nuova offerta per {{priceAmount}} {{priceTokenSymbol}} per {{NFTDisplayName}} su {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Nuova offerta per {{priceAmount}} {{priceTokenSymbol}} per {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Nuova offerta per {{NFTDisplayName}} su {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Nuova offerta per {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Hai annullato l'offerta per {{priceAmount}} {{priceTokenSymbol}} per {{NFTDisplayName}} su {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Hai annullato l'offerta per {{priceAmount}} {{priceTokenSymbol}} per {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Hai annullato l'offerta per {{NFTDisplayName}} su {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Hai annullato l'offerta per {{NFTDisplayName}}", "espNFTUnlist": "Hai tolto dalla vendita {{NFTDisplayName}} su {{dAppName}}", "espNFTUnlistWithoutDApp": "<PERSON> rimosso dal listino {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Hai acquistato {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}} su {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Hai acquistato {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Hai acquistato {{NFTDisplayName}} su {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "<PERSON> a<PERSON> {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Hai venduto {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}} su {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Hai venduto {{NFTDisplayName}} per {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Hai venduto {{NFTDisplayName}} su {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Hai venduto {{NFTDisplayName}}", "espDEXSwap": "<PERSON> scambiato {{downTokensTextFragment}} per {{upTokensTextFragment}} su {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Hai depositato {{downTokensTextFragment}} nel pool di liquidità {{poolDisplayName}} su {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "<PERSON> scambiato {{downTokensTextFragment}} per {{upTokensTextFragment}} su {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Hai prelevato {{upTokensTextFragment}} dal pool di liquidità {{poolDisplayName}} su {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "<PERSON> scambiato {{downTokensTextFragment}} per {{upTokensTextFragment}} su {{dAppName}}", "espGenericTokenSend": "Hai inviato {{downTokensTextFragment}}", "espGenericTokenReceive": "Hai ricevuto {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "<PERSON> scambiato {{downTokensTextFragment}} per {{upTokensTextFragment}}", "espUnknown": "SCONOSCIUTO", "espUnknownNFT": "NFT sconosciuto", "espTextFragmentAnd": "e", "externalLinkWarningTitle": "Stai per lasciare Phantom", "externalLinkWarningDescription": "e aprire {{url}}. Assicurati che questa fonte sia affidabile prima d'interagire con essa.", "shortcutsWarningDescription": "Collegamento creato da {{url}}. Assicurati che questa fonte sia affidabile prima d'interagire con essa.", "lowTpsBanner": "Al momento la rete Solana è congestionata", "lowTpsMessageTitle": "Congestione della rete Solana", "lowTpsMessage": "A causa dell'elevata congestione di Solana, le tue transazioni potrebbero non andare a buon fine o subire ritardi. Prova a effettuare di nuovo le transazioni non riuscite.", "solanaSlow": "La rete Solana è insolitamente lenta", "solanaNetworkTemporarilyDown": "La rete Solana è momentaneamente inattiva", "waitForNetworkRestart": "Attendi il riavvio della rete. Non ci sarà alcun impatto sui tuoi fondi.", "exploreCollectionsCarouselTitle": "Cosa è popolare", "exploreDropsCarouselTitle": "Cosa c'è di nuovo", "exploreSortFloor": "Minimo", "exploreSortListed": "In vendita", "exploreSortVolume": "Volume", "exploreFetchErrorSubtitle": "Riprova più tardi.", "exploreFetchErrorTitle": "Impossibile recuperare.", "exploreTopCollectionsTitle": "Mi<PERSON>ori raccolte NFT", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "<PERSON><PERSON>", "exploreSeeMore": "<PERSON><PERSON><PERSON><PERSON>", "exploreTrendingTokens": "Token di tendenza", "exploreVolumeTokens": "Volume più alto", "explorePriceChangeTokens": "Guadagni maggiori", "explorePriceTokens": "Token per prezzo", "exploreMarketCapTokens": "<PERSON><PERSON><PERSON> token", "exploreTrendingSites": "Siti di tendenza", "exploreTopSites": "<PERSON><PERSON><PERSON> siti", "exploreTrendingCollections": "Raccolte di tendenza", "exploreTopCollections": "<PERSON><PERSON><PERSON> r<PERSON>", "collectiblesSearchCollectionsSection": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchItemsSection": "Articoli", "collectiblesSearchNrOfItems": "{{ nrOfItems }} Articoli", "collectiblesSearchPlaceholderText": "Cerca nei tuoi collezionabili", "collectionPinSuccess": "Raccolta fissata", "collectionPinFail": "Impossibile fissare la raccolta", "collectionUnpinSuccess": "Raccolta sbloccata", "collectionUnpinFail": "Impossibile sbloccare la raccolta", "collectionHideSuccess": "Raccolta nascosta", "collectionHideFail": "Impossibile nascondere la raccolta", "collectionUnhideSuccess": "Raccolta visibile", "collectionUnhideFail": "Impossibile rendere visibile la raccolta", "collectiblesSpamSuccess": "<PERSON><PERSON><PERSON><PERSON> come spam", "collectiblesSpamFail": "Segnalazione come spam non riuscita", "collectiblesSpamAndHiddenSuccess": "Segnalato come spam e nascosto", "collectiblesNotSpamSuccess": "Se<PERSON><PERSON>o come non spam", "collectiblesNotSpamFail": "Segnalazione come non spam non riuscita", "collectiblesNotSpamAndUnhiddenSuccess": "Segnalato come non spam e reso visibile", "tokenPageSpamWarning": "Questo token non è verificato. Opera solo con token di cui ti fidi.", "tokenSpamWarning": "Questo token è stato nascosto perché Phantom ritiene che si tratti di spam.", "collectibleSpamWarning": "Questo collezionabile è stato nascosto perché Phantom ritiene che si tratti di spam.", "collectionSpamWarning": "Questi collezionabili sono stati nascosti perché Phantom ritiene che si tratti di spam.", "emojiNoResults": "Nessuna emoji trovata", "emojiSearchResults": "Risultati di ricerca", "emojiSuggested": "Consigliato", "emojiSmileys": "Smile e Persone", "emojiAnimals": "Animali e Natura", "emojiFood": "Cibo e Bevande", "emojiTravel": "Viaggi e Luoghi", "emojiActivities": "Attività", "emojiObjects": "<PERSON><PERSON><PERSON>", "emojiSymbols": "Simboli", "emojiFlags": "Bandiere", "whichExtensionToConnectWith": "Con quale estensione vuoi connetterti?", "configureInSettings": "Configurabile in Impostazioni → Portafoglio app predefinito.", "continueWith": "Continua con", "useMetaMask": "Usa MetaMask", "usePhantom": "Usa Phantom", "alwaysAsk": "Chiedi sempre", "dontAskMeAgain": "Non chiedermelo più", "selectWalletSettingDescriptionLine1": "Alcune app potrebbero non offrire un'opzione per connettersi con Phantom.", "selectWalletSettingDescriptionLinePhantom": "Come soluzione alternativa, la connessione con MetaMask aprirà sempre Phantom.", "selectWalletSettingDescriptionLineAlwaysAsk": "Per ovviare al problema, quando ti connetti con Meta<PERSON>ask, ti ​​chiederemo se desideri invece utilizzare Phantom.", "selectWalletSettingDescriptionLineMetaMask": "L'impostazione di MetaMask come predefinita disabiliterà quelle dapp dalla connessione a Phantom.", "metaMaskOverride": "Portafoglio app predefinito", "metaMaskOverrideSettingDescriptionLine1": "Per la connessione a siti Web che non offrono un'opzione per utilizzare Phantom.", "refreshAndReconnectToast": "Aggiorna e riconnettiti per applicare le modifiche", "autoConfirmUnavailable": "Non disponibile", "autoConfirmReasonDappNotWhitelisted": "Non disponibile perché il contratto da cui proviene non è nella nostra lista consentita per questa app.", "autoConfirmReasonSessionNotActive": "Non disponibile perché non è attiva alcuna sessione di conferma automatica. Abilitala di seguito.", "autoConfirmReasonRateLimited": "Non disponibile perché la dapp che stai utilizzando sta inviando troppe richieste.", "autoConfirmReasonUnsupportedNetwork": "Non disponibile perché la conferma automatica non supporta ancora questa rete.", "autoConfirmReasonSimulationFailed": "Non disponibile perché non siamo in grado di garantire la sicurezza.", "autoConfirmReasonTabNotFocused": "Non disponibile perché la scheda del dominio su cui stai tentando di eseguire la conferma automatica non è attiva.", "autoConfirmReasonNotUnlocked": "Non disponibile perché il portafoglio non è stato sbloccato.", "rpcErrorUnauthorizedWrongAccount": "La transazione dall'indirizzo non corrisponde all'indirizzo dell'account selezionato.", "rpcErrorUnauthorizedUnknownSource": "Impossibile determinare l'origine della richiesta RPC.", "transactionsDisabledTitle": "Transazione non abilitata", "transactionsDisabledMessage": "Il tuo indirizzo non è in grado di effettuare transazioni utilizzando Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Attivo", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL copiato negli appunti", "notEnoughSolScanTransactionWarning": "Questa transazione potrebbe non riuscire a causa di SOL insufficienti nel tuo account. Aggiungi altri SOL al tuo account e riprova.", "transactionRevertedWarning": "Questa transazione non è andata a buon fine durante la simulazione. I fondi potrebbero andare persi se inviati.", "slippageToleranceExceeded": "Questa transazione è stata ripristinata durante la simulazione. Tolleranza allo slittamento superata.", "simulationWarningKnownMalicious": "Crediamo che questo account sia pericoloso. L'approvazione può comportare la perdita di fondi.", "simulationWarningPoisonedAddress": "Questo indirizzo è sospettosamente simile a un indirizzo a cui hai inviato fondi di recente. Conferma che questo è l'indirizzo corretto per evitare di perdere fondi a causa di una truffa.", "simulationWarningInteractingWithAccountWithoutActivity": "Questo è un account senza alcuna attività precedente. L'invio di fondi a un conto inesistente può portare alla perdita di fondi", "quests": "Missioni", "questsClaimInProgress": "Riscossione in corso", "questsVerifyingCompletion": "Verifica del completamento della missione", "questsClaimError": "Errore nella riscossione del premio", "questsClaimErrorDescription": "Si è verificato un errore durante la riscossione del tuo premio. Riprova più tardi.", "questsBadgeMobileOnly": "Solo mobile", "questsBadgeExtensionOnly": "Solo estensione", "questsExplainerSheetButtonLabel": "Capito", "questsNoQuestsAvailable": "Nessuna missione disponibile", "questsNoQuestsAvailableDescription": "Al momento non ci sono missioni disponibili. Ti informeremo non appena ne saranno aggiunte di nuove.", "exploreLearn": "Impara", "exploreSites": "<PERSON><PERSON>", "exploreTokens": "Token", "exploreQuests": "Missioni", "exploreCollections": "<PERSON><PERSON><PERSON><PERSON>", "exploreFilterByall_networks": "<PERSON><PERSON> le reti", "exploreSortByrank": "Tendenze", "exploreSortBytrending": "Tendenze", "exploreSortByprice": "Prezzo", "exploreSortByprice-change": "Cambio di prezzo", "exploreSortBytop": "Top", "exploreSortByvolume": "Volume", "exploreSortBygainers": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBylosers": "Perdite", "exploreSortBymarket-cap": "Market Cap", "exploreSortBymarket_cap": "Market Cap", "exploreTimeFrame1h": "1o", "exploreTimeFrame24h": "24o", "exploreTimeFrame7d": "7g", "exploreTimeFrame30d": "30g", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Collezionabili", "exploreCategoryMarketplace": "Marketplace", "exploreCategoryGaming": "Gioco", "exploreCategoryBridges": "Bridge", "exploreCategoryOther": "Altro", "exploreCategorySocial": "Social", "exploreCategoryCommunity": "Community", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Staking", "exploreCategoryArt": "Arte", "exploreCategoryTools": "Strumenti", "exploreCategoryDeveloperTools": "Strumenti di sviluppo", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Staking di NFT", "exploreCategoryExplorer": "Explorer", "exploreCategoryInscriptions": "Iscrizioni", "exploreCategoryBridge": "<PERSON><PERSON>", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Controllo di Airdrop", "exploreCategoryPoints": "<PERSON><PERSON><PERSON>", "exploreCategoryQuests": "Missioni", "exploreCategoryShop": "Negozio", "exploreCategoryProtocol": "<PERSON><PERSON>", "exploreCategoryNamingService": "Servizio dei nomi", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Monitoraggio del portafoglio", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volume", "exploreFloor": "Minimo", "exploreCap": "Market Cap", "exploreToken": "Token", "explorePrice": "Prezzo", "explore24hVolume": "Volume 24h", "exploreErrorButtonText": "<PERSON><PERSON><PERSON><PERSON>", "exploreErrorDescription": "Si è verificato un errore durante il tentativo di caricare i contenuti da esplorare. Aggiorna e riprova", "exploreErrorTitle": "Impossibile caricare i contenuti da esplorare", "exploreNetworkError": "Si è verificato un errore di rete. Riprova più tardi.", "exploreTokensLegalDisclaimer": "Gli elenchi di token vengono generati utilizzando dati di mercato forniti da vari provider terzi, tra cui CoinGecko, Birdeye e Jupiter. Le performance si basano sul periodo precedente di 24 ore. Le performance passate non sono indicative delle performance future.", "swapperTokensLegalDisclaimer": "Gli elenchi di token di tendenza vengono generati utilizzando dati di mercato da vari provider terzi, tra cui CoinGecko, Birdeye e Jupiter, e si basano sui token più popolari scambiati dagli utenti Phantom tramite Swapper nel periodo di tempo indicato. Le performance passate non sono indicative delle performance future.", "exploreLearnErrorTitle": "Impossibile caricare i contenuti di formazione", "exploreLearnErrorDescription": "Si è verificato un errore durante il tentativo di caricare i contenuti di formazione. Aggiorna e riprova", "exploreShowMore": "Mostra di più", "exploreShowLess": "<PERSON>ra meno", "exploreVisitSite": "Visita sito", "dappBrowserSearchScreenVisitSite": "Visita sito", "dappBrowserSearchScreenSearchWithGoogle": "Cerca con Google", "dappBrowserSearchScreenSearchLinkYouCopied": "<PERSON> copiato", "dappBrowserExtSearchPlaceholder": "Cerca siti, token", "dappBrowserSearchNoAppsTokens": "Nessuna app o token trovati", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON><PERSON><PERSON> le schede meno recenti?", "dappBrowserTabsLimitExceededScreenDescription": "Hai {{tabsCount}} schede aperte. Per aprirne di più, dovrai chiudere alcune schede.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON> tutte le schede", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: questo dominio non esiste", "dappBrowserTabErrorHttp": "Bloccato, usa HTTPS", "dappBrowserTabError401Unauthorized": "401 Accesso negato", "dappBrowserTabError501UnhandledRequest": "501 Richiesta non gestita", "dappBrowserTabErrorTimeout": "TIMEOUT: il server ha impiegato troppo tempo per rispondere", "dappBrowserTabErrorInvalidResponse": "Risposta non valida", "dappBrowserTabErrorEmptyResponse": "Risposta vuota", "dappBrowserTabErrorGeneric": "Si è verificato un errore", "localizedErrorUnknownError": "Qualcosa è andato storto, riprova più tardi.", "localizedErrorUnsupportedCountry": "Siamo spiacenti, il tuo Paese non è attualmente supportato.", "localizedErrorTokensNotLoading": "Si è verificato un problema durante il caricamento dei token. Riprova.", "localizedErrorSwapperNoQuotes": "Nessuno scambio disponibile a causa di coppia non supportata, bassa liquidità o basso importo. Prova a modificare il token o l'importo.", "localizedErrorSwapperRefuelNoQuotes": "Nessuna quotazione trovata. Prova un token, un importo diverso o disabilita il rifornimento.", "localizedErrorInsufficientSellAmount": "Importo del token troppo basso. Aumenta il valore per scambiare cross-chain.", "localizedErrorCrossChainUnavailable": "<PERSON>li scambi cross-chain al momento non sono disponibili, riprova più tardi.", "localizedErrorTokenNotTradable": "Uno dei token selezionati non è disponibile per le operazioni. Seleziona un token diverso.", "localizedErrorCollectibleLocked": "Il conto token è bloccato.", "localizedErrorCollectibleListed": "Il conto token è in vendita.", "spamActivityAction": "Visualizza gli elementi nascosti", "spamActivityTitle": "Attività nascosta", "spamActivityWarning": "Questa transazione è stata nascosta perché Phantom ritiene che possa trattarsi di spam.", "appAuthenticationFailed": "Impossibile autenticare", "appAuthenticationFailedDescription": "Si è verificato un problema con il tuo tentativo di autenticazione. Riprova.", "partialErrorBalanceChainName": "Stiamo riscontrando problemi ad aggiornare i tuoi saldi {{chainName}}. I tuoi fondi sono al sicuro.", "partialErrorGeneric": "Stiamo riscontrando dei problemi ad aggiornare le reti, alcuni dei tuoi saldi e prezzi dei token potrebbero non essere aggiornati. I tuoi fondi sono al sicuro.", "partialErrorTokenDetail": "Stiamo riscontrando problemi ad aggiornare il tuo saldo token. I tuoi fondi sono al sicuro.", "partialErrorTokenPrices": "Stiamo riscontrando problemi ad aggiornare i prezzi dei tuoi token. I tuoi fondi sono al sicuro.", "partialErrorTokensTrimmed": "Stiamo riscontrando problemi nel visualizzare tutti i token nel tuo portafoglio. I tuoi fondi sono al sicuro.", "publicFungibleDetailAbout": "Informazioni", "publicFungibleDetailYourBalance": "Il tuo saldo", "publicFungibleDetailInfo": "Informazioni", "publicFungibleDetailShowMore": "Mostra di più", "publicFungibleDetailShowLess": "<PERSON>ra meno", "publicFungibleDetailPerformance": "Prestazioni 24h", "publicFungibleDetailSecurity": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMarketCap": "Market Cap", "publicFungibleDetailTotalSupply": "Fornitura totale", "publicFungibleDetailCirculatingSupply": "Fornitura circolante", "publicFungibleDetailMaxSupply": "Fornitura massima", "publicFungibleDetailHolders": "<PERSON><PERSON><PERSON>", "publicFungibleDetailVolume": "Volume", "publicFungibleDetailTrades": "Operazioni", "publicFungibleDetailTraders": "Trader", "publicFungibleDetailUniqueWallets": "Portafogli unici", "publicFungibleDetailTop10Holders": "I 10 migliori titolari", "publicFungibleDetailTop10HoldersTooltip": "Indica la percentuale dell'offerta totale attuale detenuta dai primi 10 detentori del token. È una misura di quanto facilmente il prezzo può essere manipolato.", "publicFungibleDetailMintable": "Coniabile", "publicFungibleDetailMintableTooltip": "L'offerta di token può essere aumentata dal proprietario del contratto se un token è coniabile.", "publicFungibleDetailMutableInfo": "Informazioni mutevoli", "publicFungibleDetailMutableInfoTooltip": "Se le informazioni sul token come nome, logo e indirizzo del sito web sono modificabili, possono essere modificate dal proprietario del contratto.", "publicFungibleDetailOwnershipRenounced": "Proprietà rinunciata", "publicFungibleDetailOwnershipRenouncedTooltip": "Se si rinuncia alla proprietà dei token, nessuno può eseguire funzioni come coniare altri token.", "publicFungibleDetailUpdateAuthority": "Aggiorna autorità", "publicFungibleDetailUpdateAuthorityTooltip": "L'autorità di aggiornamento è l'indirizzo del portafoglio che può modificare le informazioni se un token è modificabile.", "publicFungibleDetailFreezeAuthority": "Permesso di congelamento", "publicFungibleDetailFreezeAuthorityTooltip": "Il congelamento di un indirizzo di portafoglio può impedirne il trasferimento di fondi.", "publicFungibleUnverifiedToken": "Questo token non è verificato. Opera solo con token di cui ti fidi.", "publicFungibleDetailSwap": "Scambia {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Scambia {{tokenSymbol}} con l'app Phantom", "publicFungibleDetailLinkCopied": "Copiato negli appunti", "publicFungibleDetailContract": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMint": "Conia", "unifiedTokenDetailTransactionActivity": "Attività", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON><PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivityError": "Impossibile caricare l'attività recente", "additionalNetworksTitle": "<PERSON><PERSON> aggiuntive", "copyAddressRowAdditionalNetworks": "<PERSON><PERSON> aggiuntive", "copyAddressRowAdditionalNetworksHeader": "Le seguenti reti utilizzano lo stesso indirizzo di Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Puoi utilizzare in tutta sicurezza il tuo indirizzo Ethereum per inviare e ricevere risorse su una qualsiasi di queste reti.", "cpeUnknownError": "<PERSON><PERSON><PERSON> scon<PERSON>", "cpeUnknownInstructionError": "Errore di istruzione sconosciuta", "cpeAccountFrozen": "Il conto è congelato", "cpeAssetFrozen": "L'asset è congelato", "cpeInsufficientFunds": "Fondi insufficienti", "cpeInvalidAuthority": "Permesso non valido", "cpeBalanceBelowRent": "Saldo inferiore alla soglia di esenzione", "cpeNotApprovedForConfidentialTransfers": "Conto non approvato per trasferimenti riservati", "cpeNotAcceptingDepositsOrTransfers": "Il conto non accetta depositi o trasferimenti", "cpeNoMemoButRequired": "Nessuna nota nelle istruzioni precedenti: è obbligatoria affinché il destinatario riceva un trasferimento", "cpeTransferDisabledForMint": "Il trasferimento è disabilitato per questo conio", "cpeDepositAmountExceedsLimit": "L'importo del deposito supera il limite massimo", "cpeInsufficientFundsForRent": "Fondi insufficienti per l'affitto", "reportIssueScreenTitle": "Se<PERSON>la un problema", "publicFungibleReportIssuePrompt": "Quale problema vuoi segnalare relativamente a {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "Informazioni errate", "publicFungibleReportIssuePriceStale": "Il prezzo non si aggiorna", "publicFungibleReportIssuePriceMissing": "Prezzo assente", "publicFungibleReportIssuePerformanceIncorrect": "Le prestazioni 24 ore non sono corrette", "publicFungibleReportIssueLinkBroken": "Link social non raggiungibili", "publicFungibleDetailErrorLoading": "Dati sui token non disponibili", "reportUserPrompt": "Quale problema vuoi segnalare relativamente a @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "Abuso e molestie", "reportUserOptionAbuseAndHarrassmentDescription": "<PERSON><PERSON><PERSON> mirate, incitamento alle molestie, minacce violente, contenuti e riferimenti all'odio", "reportUserOptionPrivacyAndImpersonationTitle": "Privacy e sostituzione di persona", "reportUserOptionPrivacyAndImpersonationDescription": "Condivisione o minacce di condivisione di informazioni private, fingendosi qualcun altro", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "Account falsi, truffe, link pericolosi", "reportUserSuccess": "Segnalazione utente inviata.", "settingsClaimUsernameTitle": "Crea nome utente", "settingsClaimUsernameDescription": "Un'identità unica, unica come il tuo portafoglio", "settingsClaimUsernameValueProp1": "Identità semplificata", "settingsClaimUsernameValueProp1Description": "Dì addio a indirizzi lunghi e complessi e dai il benvenuto a un'identità intuitiva", "settingsClaimUsernameValueProp2": "<PERSON>ù veloce e più facile", "settingsClaimUsernameValueProp2Description": "Invia e ricevi facilmente criptovalute, accedi al tuo portafoglio e connettiti con gli amici", "settingsClaimUsernameValueProp3": "Resta sincronizzato", "settingsClaimUsernameValueProp3Description": "Collega qualsiasi account al tuo nome utente e si sincronizzerà su tutti i tuoi dispositivi", "settingsClaimUsernameHelperText": "Il tuo nome univoco per il tuo account Phantom", "settingsClaimUsernameValidationDefault": "Questo nome utente non può essere modificato in seguito", "settingsClaimUsernameValidationAvailable": "Nome utente disponibile", "settingsClaimUsernameValidationUnavailable": "Nome utente non disponibile", "settingsClaimUsernameValidationServerError": "Impossibile verificare se il nome utente è disponibile, riprova più tardi", "settingsClaimUsernameValidationErrorLine1": "Nome utente non valido.", "settingsClaimUsernameValidationErrorLine2": "I nomi utente devono avere una lunghezza compresa tra {{minChar}} e {{maxChar}} caratteri e possono contenere solo lettere e numeri.", "settingsClaimUsernameValidationLoading": "Stiamo controllando se questo nome utente è disponibile...", "settingsClaimUsernameSaveAndContinue": "Salva e continua", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON><PERSON> avatar", "settingsClaimUsernameAnonymousAuthTitle": "Autenticazione anonima", "settingsClaimUsernameAnonymousAuthDescription": "Accedi al tuo account Phantom in modo anonimo con una firma", "settingsClaimUsernameAnonymousAuthBadge": "Scopri come funziona", "settingsClaimUsernameLinkWalletsTitle": "Collega i tuoi portafogli", "settingsClaimUsernameLinkWalletsDescription": "Scegli i portafogli che verranno visualizzati su altri dispositivi con il tuo nome utente", "settingsClaimUsernameLinkWalletsBadge": "Non visibile pubblicamente", "settingsClaimUsernameConnectAccountsTitle": "Connetti account", "settingsClaimUsernameConnectAccountsHelperText": "Ogni indirizzo della catena sarà collegato al tuo nome utente. Puoi modificarli in seguito.", "settingsClaimUsernameContinue": "Continua", "settingsClaimUsernameCreateUsername": "Crea nome utente", "settingsClaimUsernameCreating": "Creazione nome utente...", "settingsClaimUsernameSuccess": "Nome utente creato!", "settingsClaimUsernameError": "Si è verificato un errore durante la creazione del tuo nome utente", "settingsClaimUsernameTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameSuccessHelperText": "Ora puoi utilizzare il tuo nuovo nome utente in tutti i tuoi portafogli Phantom", "settingsClaimUsernameSettingsSyncedTitle": "Impostazioni sincronizzate", "settingsClaimUsernameSettingsSyncedHelperText": "Dì addio a indirizzi lunghi e complessi e dai il benvenuto a un'identità intuitiva", "settingsClaimUsernameSendToUsernameTitle": "Invia a nome utente", "settingsClaimUsernameSendToUsernameHelperText": "Invia e ricevi facilmente criptovalute, accedi al tuo portafoglio e connettiti con gli amici", "settingsClaimUsernameManageAddressesTitle": "Indirizzi pubblici", "settingsClaimUsernameManageAddressesHelperText": "Eventuali token o collezionabili inviati al tuo nome utente verranno inviati a questi indirizzi", "settingsClaimUsernameManageAddressesBadge": "Visibile pubblicamente", "settingsClaimUsernameEditAddressesTitle": "Gestisci indirizzi pubblici", "settingsClaimUsernameEditAddressesHelperText": "Eventuali token o collezionabili inviati al tuo nome utente verranno inviati a questi indirizzi. Seleziona un indirizzo per catena.", "settingsClaimUsernameEditAddressesError": "È consentito un solo indirizzo per rete.", "settingsClaimUsernameEditAddressesEditAddress": "Modifica indirizzi", "settingsClaimUsernameNoAddressesSaved": "<PERSON><PERSON><PERSON> indirizzo pubblico salvato", "settingsClaimUsernameSave": "<PERSON><PERSON>", "settingsClaimUsernameDone": "<PERSON><PERSON>", "settingsClaimUsernameWatching": "Osservazione", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} account", "settingsClaimUsernameNoOfAccountsSingular": "1 Account", "settingsClaimUsernameEmptyAccounts": "Nessun account", "settingsClaimUsernameSettingTitle": "Crea il tuo @nomeutente", "settingsClaimUsernameSettingDescription": "Un'identità unica per il tuo portafoglio", "settingsManageUserProfileAbout": "Informazioni", "settingsManageUserProfileAboutUsername": "Nome utente", "settingsManageUserProfileAboutBio": "Biografia", "settingsManageUserProfileTitle": "Gestisci profilo", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "Fattori di autenticazione", "settingsManageUserProfileAuthFactorsDescription": "Scegli quali frasi seed o chiavi private possono accedere al tuo account Phantom.", "settingsManageUserProfileUpdateAuthFactorsToast": "Fattori di autenticazione aggiornati!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Impossibile aggiornare i fattori di autenticazione!", "settingsManageUserProfileBiography": "Modifica biografia", "settingsManageUserProfileBiographyDescription": "Aggiungi una breve biografia al tuo profilo", "settingsManageUserProfileUpdateBiographyToast": "Biografia aggiornata!", "settingsManageUserProfileUpdateBiographyToastFailure": "È successo qualcosa. Riprova", "settingsManageUserProfileBiographyNoUrlMessage": "Rimuovi tutti gli URL dalla tua biografia", "settingsManageUserProfileLinkedWallets": "Portafogli collegati", "settingsManageUserProfileLinkedWalletsDescription": "Scegli i portafogli che verranno visualizzati su altri dispositivi quando accedi al tuo account Phantom.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Portafogli collegati aggiornati!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Impossibile aggiornare i portafogli collegati!", "settingsManageUserProfilePrivacy": "Privacy", "settingsManageUserProfileUpdatePrivacyStateToast": "Privacy aggiornata!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Impossibile aggiornare la privacy!", "settingsManageUserProfilePublicAddresses": "Indirizzi pubblici", "settingsManageUserProfileUpdatePublicAddressToast": "Indirizzo pubblico aggiornato!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Impossibile aggiornare l'indirizzo pubblico!", "settingsManageUserProfilePrivacyStatePublic": "Pubblico", "settingsManageUserProfilePrivacyStatePublicDescription": "Il tuo profilo e i tuoi indirizzi pubblici sono visibili e ricercabili da chiunque", "settingsManageUserProfilePrivacyStatePrivate": "Privato", "settingsManageUserProfilePrivacyStatePrivateDescription": "Il tuo profilo è ricercabile da chiunque, ma gli altri devono richiedere l'autorizzazione per visualizzare il tuo profilo e gli indirizzi pubblici", "settingsManageUserProfilePrivacyStateInvisible": "Invisibile", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Il tuo profilo e i tuoi indirizzi pubblici sono nascosti e non visibili da nessuna parte", "settingsDownloadPhantom": "Scarica Phantom", "settingsLogOut": "<PERSON><PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "Aggiungi un portafoglio", "seedlessAddAWalletSecondaryText": "Accedi o importa un portafoglio esistente ", "seedlessAddSeedlessWalletPrimaryText": "Aggiungi portafoglio senza seed", "seedlessAddSeedlessWalletSecondaryText": "Usa il tuo ID Apple, Google o e-mail", "seedlessCreateNewWalletPrimaryText": "C<PERSON>re un nuovo portafoglio?", "seedlessCreateNewWalletSecondaryText": "Questa e-mail non ha un portafoglio, vorresti crearne uno?", "seedlessCreateNewWalletButtonText": "<PERSON><PERSON>", "seedlessCreateNewWalletNoBundlePrimaryText": "Portafoglio non trovato", "seedlessCreateNewWalletNoBundleSecondaryText": "Questa e-mail non ha un portafoglio", "seedlessCreateNewWalletNoBundleButtonText": "Indietro", "seedlessEmailOptionsPrimaryText": "Seleziona la tua e-mail", "seedlessEmailOptionsSecondaryText": "Aggiungi un portafoglio con il tuo account Apple o Google ", "seedlessEmailOptionsButtonText": "Continua con l'e-mail", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Crea un portafoglio con il tuo ID Apple", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Crea un portafoglio con la tua e-mail Google", "seedlessAlreadyExistsPrimaryText": "L'account esiste già", "seedlessAlreadyExistsSecondaryText": "Questa e-mail ha già un portafoglio creato, desideri invece accedere?", "seedlessSignUpWithAppleButtonText": "Registrati con Apple", "seedlessContinueWithAppleButtonText": "Continua con Apple", "seedlessSignUpWithGoogleButtonText": "Registrati con Google", "seedlessContinueWithGoogleButtonText": "Continua con Google", "seedlessCreateAPinPrimaryText": "<PERSON><PERSON> un PIN", "seedlessCreateAPinSecondaryText": "Questo viene utilizzato per proteggere il tuo portafoglio su tutti i tuoi dispositivi. <1>Non può essere recuperato.</1>", "seedlessContinueText": "Continua", "seedlessConfirmPinPrimaryText": "Conferma il tuo PIN", "seedlessConfirmPinSecondaryText": "Se dimentichi questo PIN, non potrai recuperare il tuo portafoglio su un nuovo dispositivo.", "seedlessConfirmPinButtonText": "Crea <PERSON>", "seedlessConfirmPinError": "PIN errato<PERSON>", "seedlessAccountsImportedPrimaryText": "Account importati", "seedlessAccountsImportedSecondaryText": "Questi account verranno automaticamente importati nel tuo portafoglio", "seedlessPreviouslyImportedTag": "Importato in precedenza", "seedlessEnterPinPrimaryText": "Inserisci il tuo PIN", "seedlessEnterPinInvalidPinError": "PIN inserito non corretto. Sono consentiti solo numeri di 4 cifre", "seedlessEnterPinNumTriesLeft": "{{numTries}} tentati<PERSON> rim<PERSON>.", "seedlessEnterPinCooldown": "<PERSON><PERSON><PERSON><PERSON> tra {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "Il PIN deve essere composto esattamente da 4 cifre", "seedlessEnterPinMatch": "I PIN corrispondono", "seedlessDoneText": "<PERSON><PERSON>", "seedlessEnterPinToSign": "Inserisci il tuo PIN per firmare questa transazione", "seedlessSigning": "Firma", "seedlessCreateSeed": "<PERSON><PERSON> un portafoglio con frase seed", "seedlessImportOptions": "Altre opzioni di importazione", "seedlessImportPrimaryText": "Opzioni di importazione", "seedlessImportSecondaryText": "Importa un portafoglio esistente con la tua frase seed, la tua chiave privata o il tuo portafoglio hardware", "seedlessImportSeedPhrase": "Importa frase seed", "seedlessImportPrivateKey": "Importa chiave privata", "seedlessConnectHardwareWallet": "Collega portafoglio hardware", "seedlessTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "seedlessCreatingWalletPrimaryText": "Creazione portafoglio", "seedlessCreatingWalletSecondaryText": "Aggiunta di un portafoglio sociale", "seedlessLoadingWalletPrimaryText": "Caricamento portafoglio", "seedlessLoadingWalletSecondaryText": "Importa e controlla i tuoi portafogli collegati", "seedlessLoadingWalletErrorPrimaryText": "Impossibile caricare il portafoglio", "seedlessCreatingWalletErrorPrimaryText": "Impossibile creare il portafoglio", "seedlessErrorSecondaryText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessAuthAlreadyExistsErrorText": "L'e-mail fornita appartiene già a un altro account Phantom", "seedlessAuthUnknownErrorText": "Si è verificato un errore sconosciuto, riprova più tardi", "seedlessAuthUnknownErrorTextRefresh": "Si è verificato un errore sconosciuto, riprova più tardi. Aggiorna la pagina per riprovare.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON>", "seedlessWalletExistsErrorPrimaryText": "Sul tuo dispositivo esiste già un portafoglio sociale", "seedlessWalletExistsErrorSecondaryText": "Torna indietro o chiudi questa schermata", "seedlessValueProp1PrimaryText": "Configurazione senza intoppi", "seedlessValueProp1SecondaryText": "Crea un portafoglio utilizzando un account Google o Apple e inizia a esplorare web3 con facilità", "seedlessValueProp2PrimaryText": "Sicurezza mi<PERSON>orata", "seedlessValueProp2SecondaryText": "Il tuo portafoglio è archiviato in modo sicuro e decentralizzato su molteplici fattori", "seedlessValueProp3PrimaryText": "Recupero facile", "seedlessValueProp3SecondaryText": "Recupera l'accesso al tuo portafoglio con il tuo account Google o Apple e un PIN di 4 cifre", "seedlessLoggingIn": "Accesso...", "seedlessSignUpOrLogin": "Registrati o accedi", "seedlessContinueByEnteringYourEmail": "Continua inserendo la tua e-mail", "seedless": "Senza seed", "seed": "Frase seed", "seedlessVerifyPinPrimaryText": "Verifica PIN", "seedlessVerifyPinSecondaryText": "Inserisci il tuo codice PIN per continuare", "seedlessVerifyPinVerifyButtonText": "Verifica", "seedlessVerifyPinForgotButtonText": "L'hai dimenticato?", "seedlessPinConfirmButtonText": "Conferma", "seedlessVerifyToastPrimaryText": "Verifica il tuo PIN", "seedlessVerifyToastSecondaryText": "Di tanto in tanto ti chiederemo di verificare il tuo PIN in modo che tu possa ricordarlo. Se lo dimentichi, non sarai in grado di recuperare il tuo portafoglio.", "seedlessVerifyToastSuccessText": "Il tuo codice PIN è stato verificato!", "seedlessForgotPinPrimaryText": "Reimposta il PIN utilizzando un altro dispositivo", "seedlessForgotPinSecondaryText": "Per motivi di sicurezza, puoi resettare il tuo PIN solo su altri dispositivi dove hai effettuato l'accesso", "seedlessForgotPinInstruction1PrimaryText": "Apri altro dispositivo", "seedlessForgotPinInstruction1SecondaryText": "Vai su un altro dispositivo su cui hai effettuato l'accesso al tuo account Phantom con la tua e-mail", "seedlessForgotPinInstruction2PrimaryText": "Vai alle Impostazioni", "seedlessForgotPinInstruction2SecondaryText": "In Impostazioni, seleziona “Sicurezza e privacy” e seleziona “Resetta PIN”", "seedlessForgotPinInstruction3PrimaryText": "Imposta il tuo nuovo PIN", "seedlessForgotPinInstruction3SecondaryText": "Dopo aver impostato il nuovo PIN, puoi accedere al tuo portafoglio su questo dispositivo", "seedlessForgotPinButtonText": "Ho eseguito questi passaggi", "seedlessResetPinPrimaryText": "Resetta PIN", "seedlessResetPinSecondaryText": "Inserisci un nuovo PIN che ricorderai. <PERSON><PERSON> serve per proteggere il tuo portafoglio su tutti i tuoi dispositivi", "seedlessResetPinSuccessText": "Il tuo codice PIN è stato aggiornato!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON><PERSON> un portafoglio, accetti i nostri <1>Termini di servizio</1> e l'<5>Informativa sulla privacy</5>", "pageNotFound": "Pagina non trovata", "pageNotFoundDescription": "Non ti abbiamo ghostato! Questa pagina non esiste o è stata spostata.", "webTokenPagesLegalDisclaimer": "Le informazioni sui prezzi sono fornite solo a scopo informativo e non costituiscono consulenza finanziaria. I dati di mercato sono forniti da terze parti e Phantom non rilascia alcuna dichiarazione in merito all'accuratezza delle informazioni.", "signUpOrLogin": "Registrati o accedi", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Creando un account, accetti i nostri <1>Termini di servizio</1> e l'<5>Informativa sulla privacy</5>", "feedNoActivity": "Ancora nessuna attività", "followRequests": "Richieste di seguire", "following": "<PERSON><PERSON><PERSON>", "followers": "Follower", "follower": "Follower", "joined": "Partecipi", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "N<PERSON>un follower", "noFollowing": "<PERSON><PERSON><PERSON>", "noUsersFound": "<PERSON><PERSON>un utente trovato", "viewProfile": "Visualizza profilo", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}