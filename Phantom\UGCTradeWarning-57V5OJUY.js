import{E as y,Ma as S,z as f}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import{a as u}from"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{d as n,e as a,n as g}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import"./chunk-OKP6DFCI.js";import"./chunk-WIQ4WVKX.js";import{La as c,_a as p,ja as s}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as d}from"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as F}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as w,h as m,n as l}from"./chunk-3KENBVE7.js";m();l();var o=w(F());var h=()=>{let{t:r}=d(),{closeAllModals:t,handleHideModalVisibility:T}=S(),{setSearchQuery:i}=s(),x=(0,o.useCallback)(()=>{i(""),t()},[t,i]),{asset:e,program:C,handleConfirmPress:b}=c({onClose:x});return o.default.createElement(n,{direction:"column",padding:"screen"},o.default.createElement(f,{leftButton:{type:"close",onClick:()=>T("ugcWarning")},titleSize:"small"},r("ugcSwapWarningTitle")),o.default.createElement(n,{direction:"column",alignItems:"center",gap:16,marginTop:24},e?o.default.createElement(n,{direction:"row",gap:12,padding:16},o.default.createElement(y,{image:{type:"fungible",src:e.data.logoUri,fallback:e.data.symbol||e.data.tokenAddress},tokenType:e.type,chainMeta:e.data.chain}),o.default.createElement(n,{direction:"column",gap:2},o.default.createElement(a,{font:"bodySemibold",color:"textPrimary"},e.data.name),o.default.createElement(a,{font:"caption",color:"textSecondary"},e.data.symbol))):null,o.default.createElement(n,{direction:"column",gap:16,marginBottom:16,alignItems:"center"},o.default.createElement(a,{font:"heading2Semibold",color:"textPrimary",align:"center"},r("ugcSwapWarningTitle")),o.default.createElement(a,{font:"body",color:"textSecondary",align:"center"},r("ugcSwapWarningBody1",{programName:C?.name??""})," ",r("ugcSwapWarningBody2")),o.default.createElement(a,{font:"bodyMedium",color:"accentPrimary",onPress:()=>self.open(p,"_blank")},r("commandLearnMore2"))),o.default.createElement(u,{removeFooterExpansion:!0},o.default.createElement(g,{theme:"primary",onClick:b},r("ugcSwapWarningConfirm")))))},I=h;export{h as UGCTradeWarning,I as default};
//# sourceMappingURL=UGCTradeWarning-57V5OJUY.js.map
