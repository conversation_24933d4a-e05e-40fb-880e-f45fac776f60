import{a as Ur,b as j,c as fi,d as s,e as Or,f as qr,g as Se,h as Hr,i as Ht,j as de,k as et,l as gi,m as xt,n as Et,o as yi,p as xi,q as bi,s as Ft,u as Si,w as Ri}from"../chunk-5MF3BU53.js";import{a as or}from"../chunk-YF76YZSL.js";import{c as Ai}from"../chunk-XJZOYN2T.js";import{a as wi}from"../chunk-ZON27MKP.js";import"../chunk-MTQZ2G7K.js";import{a as sr,b as ai,c as Dr,d as ci,e as pi,g as di,k as li}from"../chunk-W27Z2YZM.js";import"../chunk-2NGYUYTC.js";import"../chunk-H3FFS4GT.js";import{a as Up,c as ui,e as mi}from"../chunk-AVT3M45V.js";import{l as $o,lb as hi}from"../chunk-SD2LXVLD.js";import{a as Qe,b as at,d as mo}from"../chunk-UCBZOSRF.js";import"../chunk-F3RUX6TF.js";import"../chunk-HRJWTAGT.js";import"../chunk-LURFXJDV.js";import"../chunk-V5T43K7V.js";import{b as Wt,c as Fr}from"../chunk-MNXYIK2W.js";import"../chunk-QALJXKGR.js";import{a as ye,b as nr,c as be}from"../chunk-MHOQBMVI.js";import{a as ii}from"../chunk-GQEPK4C4.js";import{a as Ze}from"../chunk-BTKBODVJ.js";import{a as jo,b as ct,e as Vo,w as zo}from"../chunk-7ZN4F6J4.js";import{Ba as ni,Da as si,Ea as oi,L as pt,R as Jo,S as Qo,T as Zo,Y as $e,da as ei,h as Go,ha as ti,ia as ri,k as Yo,l as Xo}from"../chunk-OUYKWOVO.js";import{a as Op}from"../chunk-WFPABEAU.js";import"../chunk-THLBAMDB.js";import{a as qp}from"../chunk-LDMZMUWY.js";import"../chunk-X2SBUKU4.js";import"../chunk-OXFZHPMY.js";import{b as Pr,c as Nr,e as Ot,f as qt}from"../chunk-OYGO47TI.js";import{B as Wo,m as Fo,u as Tt,x as _t,z as Mr}from"../chunk-SLQBAOEK.js";import{$ as ke,Fb as Po,Hb as No,J as lo,K as uo,Ma as tr,Mb as Do,Nb as we,O as it,P as le,Pa as Ee,Pc as Lr,Q as xo,Rb as Uo,Wa as Eo,Wc as Oo,Xa as Br,ca as _o,db as vt,gb as Me,hb as Io,id as qo,jb as Co,lb as kt,mb as rr,od as Ho,pb as Bo,tb as Lo,v as Dp,ye as Ko,z as Rt,zb as Mo}from"../chunk-MZZEJ42N.js";import{A as Zt,B as ne,C as Ue,D as Ye,E as gt,F as co,G as po,H as At,I as Cr,J as yt,b as so,p as oo,q as io,t as Qt,u as ao,y as Er,z as Ir}from"../chunk-E3NPIRHS.js";import{a as Xe}from"../chunk-56SJOU6P.js";import{L as Ao,M as er,N as So,P as Ro,R as vo,a as St,fa as ko,l as wo,la as To,u as bo}from"../chunk-ALUTR72U.js";import"../chunk-N7UFQNLW.js";import{G as yo,Qa as Oe,ba as Ke,q as st,qa as Ut,r as xe,ta as Y,u as ho,x as fo,y as go}from"../chunk-L3A2KHJO.js";import{a as ot}from"../chunk-4P36KWOF.js";import{a as _e}from"../chunk-7X4NV6OJ.js";import"../chunk-UNDMYLJW.js";import{a as Dt,c as no,d as Ie,f as $,h as p,i as w,m as Buffer,n as d}from"../chunk-3KENBVE7.js";var Na=no((n1,Pa)=>{"use strict";p();d();function $d(e){if(e.length>=255)throw new TypeError("Alphabet too long");for(var t=new Uint8Array(256),r=0;r<t.length;r++)t[r]=255;for(var n=0;n<e.length;n++){var a=e.charAt(n),o=a.charCodeAt(0);if(t[o]!==255)throw new TypeError(a+" is ambiguous");t[o]=n}var l=e.length,f=e.charAt(0),m=Math.log(l)/Math.log(256),u=Math.log(256)/Math.log(l);function v(_){if(_ instanceof Uint8Array||(ArrayBuffer.isView(_)?_=new Uint8Array(_.buffer,_.byteOffset,_.byteLength):Array.isArray(_)&&(_=Uint8Array.from(_))),!(_ instanceof Uint8Array))throw new TypeError("Expected Uint8Array");if(_.length===0)return"";for(var g=0,A=0,y=0,k=_.length;y!==k&&_[y]===0;)y++,g++;for(var I=(k-y)*u+1>>>0,B=new Uint8Array(I);y!==k;){for(var E=_[y],D=0,P=I-1;(E!==0||D<A)&&P!==-1;P--,D++)E+=256*B[P]>>>0,B[P]=E%l>>>0,E=E/l>>>0;if(E!==0)throw new Error("Non-zero carry");A=D,y++}for(var ce=I-A;ce!==I&&B[ce]===0;)ce++;for(var Z=f.repeat(g);ce<I;++ce)Z+=e.charAt(B[ce]);return Z}function b(_){if(typeof _!="string")throw new TypeError("Expected String");if(_.length===0)return new Uint8Array;for(var g=0,A=0,y=0;_[g]===f;)A++,g++;for(var k=(_.length-g)*m+1>>>0,I=new Uint8Array(k);_[g];){var B=t[_.charCodeAt(g)];if(B===255)return;for(var E=0,D=k-1;(B!==0||E<y)&&D!==-1;D--,E++)B+=l*I[D]>>>0,I[D]=B%256>>>0,B=B/256>>>0;if(B!==0)throw new Error("Non-zero carry");y=E,g++}for(var P=k-y;P!==k&&I[P]===0;)P++;for(var ce=new Uint8Array(A+(k-P)),Z=A;P!==k;)ce[Z++]=I[P++];return ce}function S(_){var g=b(_);if(g)return g;throw new Error("Non-base"+l+" character")}return{encode:v,decodeUnsafe:b,decode:S}}Pa.exports=$d});var Ct=no((i1,Da)=>{p();d();var Gd=Na(),Yd="**********************************************************";Da.exports=Gd(Yd)});p();d();p();d();p();d();p();d();p();d();p();d();p();d();var ki=$(Dp(),1);var Pe={SOLANA_MAINNET:"solana:5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp",SOLANA_DEVNET:"solana:EtWTRABZaYq6iMfeYKouRu166VU2xqa1",SOLANA_TESTNET:"solana:4uhcVJyU9pJkvQyS88uRDiswHXSCkY3",ETHEREUM_MAINNET:"eip155:1"},du={[Pe.SOLANA_MAINNET]:"Solana Mainnet",[Pe.SOLANA_DEVNET]:"Solana Devnet",[Pe.SOLANA_TESTNET]:"Solana Testnet",[Pe.ETHEREUM_MAINNET]:"Ethereum Mainnet"};var Hp={name:"@dialectlabs/blinks",version:"0.9.2",license:"Apache-2.0",private:!1,sideEffects:!0,type:"module",repository:{type:"git",url:"https://github.com/dialectlabs/blinks"},scripts:{build:"tsup-node",dev:"tsup-node --watch"},main:"dist/index.cjs",module:"dist/index.js",types:"dist/index.d.ts",exports:{"./ext/twitter":{import:"./dist/ext/twitter.js",require:"./dist/ext/twitter.cjs",types:"./dist/ext/twitter.d.ts"},"./hooks":{import:"./dist/hooks/index.js",require:"./dist/hooks/index.cjs",types:"./dist/hooks/index.d.ts"},"./hooks/solana":{import:"./dist/hooks/solana/index.js",require:"./dist/hooks/solana/index.cjs",types:"./dist/hooks/solana/index.d.ts"},".":{import:"./dist/index.js",require:"./dist/index.cjs",types:"./dist/index.d.ts"},"./index.css":"./dist/index.css"},files:["dist"],devDependencies:{"@solana/actions-spec":"~2.2.0","@types/react":"^18.3.3","@types/react-dom":"^18.3.0","@typescript-eslint/eslint-plugin":"^7.16.1","@typescript-eslint/parser":"^7.16.1",autoprefixer:"^10.4.19",eslint:"^8.57.0","eslint-config-prettier":"^9.1.0","eslint-plugin-react":"^7.34.4","eslint-plugin-react-hooks":"^4.6.2",postcss:"^8.4.39","postcss-prefix-selector":"^1.16.1",prettier:"^3.3.3","prettier-plugin-organize-imports":"^4.0.0","prettier-plugin-tailwindcss":"^0.6.5",tailwindcss:"^3.4.3",tsup:"^8.2.0",typescript:"^5.5.3"},peerDependencies:{"@solana/wallet-adapter-react":"^0.15.0","@solana/wallet-adapter-react-ui":"^0.9.0","@solana/web3.js":"^1.95.1",react:">=18","react-dom":">=18"},dependencies:{clsx:"^2.1.1"}},Fp=Hp,lu=Fp.devDependencies["@solana/actions-spec"].replace(/[^\d.]/g,"");var vi=[Pe.SOLANA_MAINNET,Pe.SOLANA_DEVNET];var Wp=[Pe.SOLANA_MAINNET];var jp=class Wr{constructor(t,r){if(this.adapter=r,!t)throw new Error("rpcUrl or connection is required");this.connection=typeof t=="string"?new ki.Connection(t,"confirmed"):t}static CONFIRM_TIMEOUT_MS=6e4*1.2;static DEFAULT_METADATA={supportedBlockchainIds:vi};connection;get metadata(){return this.adapter.metadata??Wr.DEFAULT_METADATA}signTransaction(t,r){return this.adapter.signTransaction(t,r)}confirmTransaction(t){return new Promise((r,n)=>{let a=Date.now(),o=async()=>{if(Date.now()-a>=Wr.CONFIRM_TIMEOUT_MS){n(new Error("Unable to confirm transaction: timeout reached"));return}try{let l=await this.connection.getSignatureStatus(t);if(l.value?.err){n(new Error("Transaction execution failed, check wallet for details"));return}if(l.value&&l.value.confirmations!==null){r();return}}catch(l){console.error("[@dialectlabs/blinks] Error confirming transaction",l)}setTimeout(o,3e3)};o()})}async connect(t){try{return await this.adapter.connect(t)}catch{return null}}},uu=10*60*1e3;var td=$(Up(),1),ir=$(_e(),1);p();d();var rd=$(_e(),1);var Ii=$(xe(),1);var nd=$(xe(),1),sd=$(xe(),1),Lu=$(_e(),1),od=$(xe(),1),Mu=$(_e(),1),id=$(xe(),1),Pu=$(_e(),1),Ci=$(xe(),1),ad=$(_e(),1),jr=$(xe(),1),Bi=$(xe(),1);var cd=$(xe(),1),Li=$(xe(),1),jt=$(_e(),1),Mi=$(xe(),1);var pd=$(_e(),1),Pi=$(xe(),1),dd=$(xe(),1),ar=$(_e(),1),ld=$(xe(),1),ud=$(xe(),1),cr=$(_e(),1),md=$(xe(),1),hd=$(xe(),1);var pr=$(_e(),1);var fd=$(_e(),1),Ni=$(xe(),1),Di=$(xe(),1),dr=$(_e(),1),Ui=$(xe(),1),Vr=$(_e(),1),gd=$(xe(),1),lr=$(_e(),1),yd=$(xe(),1);var zr=$(_e(),1);var xd=$(_e(),1),wd=$(xe(),1),Oi=$(xe(),1),qi=$(xe(),1),Kr=$(_e(),1),bd=$(xe(),1),$r=$(xe(),1),Ad=$(xe(),1),Sd=$(xe(),1);p();d();p();d();p();d();p();d();p();d();p();d();var Rd=$(st());p();d();function Gr(e,t){if(!e)return{hideProvidersArray:!1,showMetamaskExplainer:!1,dontOverrideWindowEthereum:!1};let r=t.find(n=>n.hostname.toLowerCase().trim()===e.toLowerCase().trim());return r?{hideProvidersArray:r.hideProvidersArray,showMetamaskExplainer:r.showMetamaskExplainer,dontOverrideWindowEthereum:r.dontOverrideWindowEthereum}:{hideProvidersArray:!1,showMetamaskExplainer:!1,dontOverrideWindowEthereum:!1}}var ji=()=>w.ENVIRONMENT==="e2e";p();d();p();d();p();d();function Vi(e){return new Promise(t=>setTimeout(t,e))}function vd(e,t){let r=typeof t.interval=="function"?t.interval:()=>t.interval;if(typeof e!="function")throw new Error("Invalid arguments for retryInternal");let n=1;async function a(){try{return await e()}catch(o){if(n++<t.times&&t.errorFilter(o))return await Vi(r(n-1)),a();throw o}}return a()}async function zi(e,t,r=3,n=2e3,a){return vd(e,{times:r,interval:a?()=>n:o=>n*Math.pow(2,o-1),errorFilter:t})}var kd="https://sanity-proxy-v2.phantom.app",Td="production",_d=ot.object({hostname:ot.string(),hideProvidersArray:ot.boolean(),showMetamaskExplainer:ot.boolean(),dontOverrideWindowEthereum:ot.boolean()}),Ed=ot.object({dApps:ot.array(_d).optional()}).optional(),Id=`
*[_type == 'dAppsList' && _id == 'dAppsList-singleton']{
 dApps
}[0]`,Yr=()=>zi(async()=>{let e=await xo.get(`${kd}/v1/data/query/${Td}?query=${encodeURIComponent(Id)}`);if(e.status>=300)throw new Error(`status code: ${e.status}, body: ${e.data}`);let t=e.data;return Ed.parse(t.result)?.dApps??[]},()=>!0,3,50);function Xr(e){try{let t=new URL(e).hostname;if(!t||t==="localhost")return null;let r="www.";return t.startsWith(r)?t.slice(r.length):t}catch{return null}}p();d();var Pd=$(st()),Nd=$(so());var Fm=new Error("Unsupported path.");p();d();var Qr=`
data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAPoAAAB4CAYAAADblO/uAAAACXBIWXMAACE4AAAh OAFFljFgAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAB+OSURBVHgB7Z1r cBvXdccP+AYfEmlTkvU0pDhSYlsSJUfUw54xaCdO0jQWFduZ2E1HZOM2/ZCJyNqd Sd2ZiEymcTNtRlI/Na5bUk0bTxJ7RLmTSZzaITRjPUjFFmVJHku1Rej9DsGXBD4A 9PyXu9ICxGMfdwEQuL8ZaMHdBQQs9n/Pueeeey6RRCKRSCQSiUQikUgkkmygkCSG 8Xq91fcw58+fD5BEMoMoIIlhgsFgXSQS6d6wYYMn1bloFEgiyRJMW/T169e3fPrT n/7I7/cHKc9gS+5ftGhRIz/dyVvP4sWLj+qtO8Q9b968v+ZjL09OTrovXLhwiCR5 QV1dneLtMXT58uWs04bLzMmbNm2qC4VCR9iqbent7e2iPAQNHW926Hb5+OHnh4ev S53L5YIl7+vp6VlDkpwHAmdN4H6AAVC8OL4H+vhe6CssLNzd19fnoyygyMzJbKU8 /CXwRbz8Z14KfWxsrLO0tFQvdK/2BNdG3W4hSV4Aw8cbj34fGnzewCg2rVy50s/P 21n0Pha9nzKEqT4638Cb1aerKU/hHwuuuj/Rcb5GnYcOHfKTJOdhETdRjMjjgOMd LPp+Pr+DPYBU5zuC2WCcV9vKYFNCdpMkX9hK5mhSBb8j3YI3LHSONHtJ13rdunWr ifIQNeLuSXKKnyT5goes0cKC71Y9grRguI/O/Y7Y1gtu/E7KP5K24gUFBVnr6TS2 DVSXl094aIK4DxnxcEfj3ohLuVnxIFfEVR2hSNTn54hMIOKKqN2VSODc7/7yaGjs Wh93UfwHDhzoozyGr8E+1oWHrOHhRweL/VHuv7c73X83FHWHFeMv1B+7n2/qLQcP HsyboByuQzgcPqJG1hPRyhH3rGgAv/HyJU9BuNDLTx9lQXspYtkC3SZ44xhd3P+S 8pzviQAizPx0L4JN+Sb8Bx980Mvfv5vs4+fr1+Ck2FMKHX1xdtOnRRZV8EOvyZfg Ew+t4UfFj+vjm3wvbwO8reZtnc7j8bHQGygDwGK7S8a9bJnZ23I1xlpnUdw4/m80 ePrNeIf8/Oji67ErX+4JFnsbf9/tZB94Tc3Hjh1zxHAmFbpqyffw07okp/n5izbk +g+rxii2shezK57lUvvubRC8ej18lAYUd7x4opEt9lYWeJ1T4tYTnhilC/u20cTN K8lO0wTvoxwHgTXuc0Pwq9WhNcvwe7R98MEH7SSYuEJXrfhW/tBtKdxUPZ18bnu+ Dy2x4Nuw5evQRg7yzZevecMR2sbuuDcd4o5F78KnwMduaWu+uPWq6L00FcvxkgWc EHuU0GG1WNyb+dFkQuCx+Pi1GGLyyfFksSjWu3RyG4WpxYy4J8eG6cIHP6cb/T56 4Ms/odKqBSSCSwdeolvXjxk9Pe8MgR3R87VqZbELi/UoQue+5x4Wt9eGuBPhd7vd a3w+n5ztZYNvvDzgcUUmtrFr3mTWeo/eOEkf/uYFCg5fVP6+d923acnnvk0iMGHV Nfyq2Dspz9C5948ajdTzuU0sdiF5GYrQ2ZI38WYrR5TrRIgd0Vjk+fJTXz5F5UWj ued8PRvJArDiZ//wU8Wia8xb8SQtf6yNRGHSqmt0sgFozVcDgPFzBPAMCD7Ammw4 ceKE7W7PtD66GnRqijNunhRN3LztyocAjJPAghdEJndYFTis96nft9HgxT9MO1ZR u4LWPvMaiWLw9F6Owr9KFsiLIG4yDAoeQ29r1NRryySMuuujyJTqTXi4iTfNsk9u j6nhscnt3Gq2kEUGL77HIt9+21WPpai0ijb+xT4SBSLwZ9/+lrK1AG7iLfmeeGNg iK6Lh91sTZRKOY6OKDKLPeGH4GPtvb29bSSxjNUgmx6452fYTb/I7noqIHQIXhQW 3XcNeIIN+S529OHZTe9OZN3tBucMZcZxsA7/wbY4h7p6enrklEwboB8eikQ67GSt wXoj4IbAmxHWfP01qrx7BYnChvuukbVih2erJkRVq8lR+oYY3Y+AyHRgtu47+f3i aS2guvB+soAhoWNyfUlJSX9soI7/XirddWsoVrxkssNqP1wjXsAtFSsea6e5K75K opi8eVVx322S8SxLtbCKl6amYWPrIXP4+IGMyS473yOJ2H3swlvKujRcYSbWhVfn XTeTxDTP/vBKo6ugoMNOoguEfaq7jcfGzadaf+rhF2nBqudIJP7ffMNqPz3qbdIZ oENiWDAYbOT7GkNejYKHl23lDXCgrpPiTKDiz9lw/PhxH5nE8Ow1viA7S0tL9X11 Oe/aJHorziInq8BVP7b3rxIG3FIxcv0UiaaofB6ND54mmyDluoO3js0V0Il7661b t7zafq06kEAwctXE3V5LgmfLjeo0eBoldn4vXJ+lZBLD89HV8L5P/dMvh9DMgb64 u2TiiF1XHcBVtypyMDk+RKIpnWX63kuEd926dTtIMBg2ZtF13Lx5s19tTLyUHiD4 bnQLyCQQO93RnIZn1apVpkdlzJaS0sZljpLEMM/+6NqOUDjSLWKaKLAbSBt1xKLP JVEUFBS0qPkctoD1RjFPfqCBRR+nySU++9MIyIo7wt/JbEUawvCjOhX4NuhCI25G JjBbSsoXs5UkARHbRz7/dLedcfF4oH89e8HnyCp2vIF0AatrtVwZXldfX78d1pum KvbamlEmCv5OnWYbMHjSqPuAyL5uNyrPmvIMTQmd+zZKyxLbwkimw+6nsthDJDTm JQe4/0s/oTIbk1PGhi+RSAqKK0gwntHRUVPzvNGwsvXeAYFjBliGrHdSMO3byAIg ejCkxsLGMLY+O87UtTEldLQubre7RvbPkwMXTa084iHBYAbaBxyIA/d/+SdklZEb H5FIBETcp6G68J5U56kC71CrILVko8B1VKsxAlMg3x1BPd0uDyrcGH29qbruwOpE hF91DHv5dqhzkWt1mL+sSy12r8AeQjgS2VdCVb4tza4ZPdGBb7rt6jx+coKi0kol hx1ZcBgmw+OT/f9MZhkbEmvRnSJZFB4uOlvvbeFwONvFHQsCgy1mS44hM44j8ajV 6MXfatqsz8hrnbkbVRRxu2izi4cZInphJ/4wgQi5Ootosn1Lc82MEzzcRt5M648v e/J/SBQYPz/4H48qz+G+3720gT787Qumx9NFTlcFScpL2Sa2Yo8mcMp+650MeMdL zRpOdborSrsp35uDdTVGJrw4ssgiBP6rjiG+8yJKIMqIyMHUeZGWEBUe2dNxy0Mz BDW6C8sTN+gm0q1Fjnpl7VTUHQkzYxxYW97QZqq/jvdAAyGSyVtXySn0iVoIZqGG Ybb2wU2Axsp0kBb99RgX3tA1EGrRp9xz5Ufxkk1cmNlExQ1bmt1+ymLUslswpwkj uwse/hGV3b2SRHGaXfUL6uSVCh5qW/v115Q8d/Td9amwEDQaAExNLSqpVLdVHLF/ SOikFnB+3zYRCTMJ4Zu7WZ1J6aXcwZJVB1r/3GiWnOk+ejz2dESqJ2lkO6wxCYKt u2eSJhzNkrKLEZGDMRaASKFX1N4ZR4fA0UdHXx2ufJCj6bD4pZXzhYs5GU6KHFgJ YM0AkKWHe8dHJjGbBmtb6LDiIRrGj+Ah8Xjx/s80V/koyzAqcjA+2E8igUXWg6mp tUu9yth6BVv0sZFLdMPvo9D4MAWHLiriH71+UqkVt2rzKyQalJSSWEPtlvjIYWwJ /fWOkW0RCu+0nrVthPRcCDOYETkYvXyI5pA44I7DWuvddLjtsftiX4MAnBOMOWzN c5w63E9WR7M4OIcZd9AIprG2JgrMWRY6W9odLHKhGV8J8O7pGKhOFYXHWCqWQ+Kh Fi044XdqFhSL3FS2FYJxsHoi3fdqtt7XYyLt8UQO8S9c9RwtWPmcY678zcs9JLGM ZfcdsMix7oIHz/neP8ObtnjnWRL6LzuG2FWPNJEplOV7utD3Ri3yqEMu8mFojcfS Mc7uiX3lOCnLCnVpf+unF5I6b5if40tHvY4j4dggi6+PG4G9paWlPrsFCdVx8iYy Cay6SKHPXvjQNKHHUr3wc0pEXlR553gg2m6jusyMB/fd8PAwcVCNiouLyQrqRCcf WaM6wfMoTAsdImdRNpFJJim85tmmGj+ev94xyO7+1MR6fq+up5tmKVVqXusY8BRS QXes2AtoqryOWnceOcxmqtUqi9Jza9fElpjUaYO7rWT3ackwZIHhs+9QzfLnhKWK Vty9POExWG4IXPQQWjyGz75NuQgEPDo6SuXl5cjQi3vOxMSEInJgVeQqq8kiqBLL nw8eJnLi2xKdZ2p4zarIYbGfaZp1+66DKz5JhQPqwQZ9sE3fCGj0n3uv641ffw/C 9pI4TK0gsnHjxka+qHvIBnPWtFDV4sdJBPrEGT1w05EIk66I+7m3n0+1NNOMBUJn o6KImL1BKioqUuatT05OYt6HInRQUVGhNAg2CPT09NSQgxhOmOE++XZLIicsxxu9 Zniy/nYkOnFf4dKVE3BtvCQWLzKMkOiSKp8ax/lc28M7AydTF240ijJGPuuOSw43 HePpy3iYLV0iHzn3Ts6KHEDAEDcEPTIyQoFAgAYGBhQrrokcoBGwSbXVmXpGMSR0 RNdZgm1kERZvo/7vPR0jukBW2Ks/hoXqKL0ohQHURSymgR8Ax0VkYaG22jCLQxRa 4gvG0Fc++YqSPJNOBk6Kqw+frVRWViY9DgvPniHZZXx83EMOkrKPjlTUSZrYSfao Zref3d6CdhdFqkMU1llH1zY+5i+mYt8kTcZdlWRsfIQcRilhtG7dutWHDx9u1R9Q I+weEgRywivu2SCkrw43fdmmF9OaGKMBkeeyNdeA246H3oLrSdR/NwtWSaKpwLEj JP2UyHgL0YS52RIJYLe/0UXhI8h/j0QLBzPZOrgx6U+UWTc05FwetR5Mi2RXvl9z 5VUr30QCwVDbwCkxlhAWPBMiR6RdZDck20nmmrNASQQoJU0OklToSGuNOJPxZorB kcuURjyqK+9NtnCFHQY/2Tujs8mu9P4D5RPJphwjOp/I2pv8PzIj9F8q/WixJZCs MDR8ha5e/4TSjEetMeYhh7h6ZCdbxpnn+sJlz7dMuFRWG9F5AdxLDlKQ+IC9oSRR nLuYm3UoEZi70vsjRyqzOAXmm+eTy67BgbKkx2HRMQyXzcQVOgfHmrLBZQcf9x+g XAWW8crhmeEGo6uBQGK+AWuuueYlJSVUXV1NtbW1NGfOHKqpqaGqqiolWAehY3zd KkbXTDdCXV2dFyu16vfFjbqz+rc7O1HFGHDb/8+/n3IZpI9C7HPqWpwosCgEiPxy nvXLNeCWI7KOhBikuerBGDseZWVlSmOA/roNPNqTmHkbnjjn+vEP/7998VK6Q6GQ EkDHAhDHjh3rVD5r7EmYFhohca2LHY6ffIvygdFLh9iVf4nm1b9ERe55lE1g3P/a EbujqzMbWPFUY+U2U2CV/wYjPtpijrHzNuKBlO76+nrMIUHhSKyc5FMncu3mvzdz Y+HTzp0WTny9c6iDG6YmyjCw5r948wUaHM79sVoNLIKAajTZInaskIqVUiUzirhL QE3ro2eDyAGseT6JHCirkv7v8xnPOMM4+YV926TIZybI9OzHAhb6nVEWXa35JiRB xg6w5q/89zcpn4F1v6f+76lk1jJKFxgBQGQdAk/naEBBWTFVLJtL5Z65VHxXBZXd w66yu0TZr2diYJQmAqMUvBigscsBGu2/quyTxAczLXt7e5VCkjFCH2zjXZaSRLhf 7483lzweY2OjVFpakfDYf77+7byz5omoWvI4zV72pKOCz4TAIeKah5ZR5WcXUPlS 6+u2QejDH16gPx48JUUfB61UdpTQuX++h133RjKJKnI/GZhhBms9OHyZFi+YPncF Ike//OqNtCfIZD3u2pVUufhxYXnyANH0W9ePp1XgsNyz1yylqvsXTrPYdrnJFv76 70/Q6On0pEzPEHw9PT0NMRYdtdjNTgeNBFBUopAKu10pxt61ANvaVV+jh1Z+bdqx rt9ulyI3QMX8DVTOgi+dvdSUpYeYx4dOK9VugizwdGa4QdT3fGUtzV7rIaeB4C++ 0SstvAqsuu0qsGFyNT/bXOPn/n1AmZCaAH0U/cAffkZlJZWKVQ+OjdDH/gP0/rE3 +Ln8YYyA4Tg8AKx76exlyhb9+oKiaGuPAF94clSpRJup2WZ3Pbyc5jz2oHALngh0 Be578U/Zuh+na++coHyH++p1MUKPnDFTdIZD9s3PNM9Sa7mFeRzPFbdgItJY9761 /baQx1jcv+n+J5LYB1Y6W2u2QdgLn17P/fCFlAlquXFBN+HMv3fntXVHjYfY4TWD 82EjgTDRlqeaZ3Vqe55pnr2T97fjmHYOSkj94s0XCQ9prfOL4poKWvadL2ZM5PrP sZQ/R1WGP0emiTLfqOU2QQVHkkbPWbyTkZDirlMKMJ87R1fYkCQB4rr3Ww3KNpu4 +EYPDb7vp3yDLXpnlEVHLbcQhRvYGu+OPhWlmqlTKeTYNKvBiMiVVzk0n1uSvWSr yMGCp9anJRiYhZxJ2iGfWtE0GLCyhLG05vlHNotcIxScoLOvdlPw0gDlC6zDLY6t j44EfcqSqa6S9LD0O09Q2XxHqxYLAYG5fArQud3uGkfWR0cZJpIizytqH39gRogc wONY8FQ95QnK6kSOCF1dx1qSJ0A4GCefSWCsHeP7uY46fVXM+uh6MGneytpkkpkL +uV2mbw5Rmd/9z4NfHSeqpbMoWWNG6mo3PbCCEnBOHvgvX4KB+0Xd8xifPhHuNDj 1WWX5C7Va5faCr5B2Ke7DvL2nG7fObr2/sf00Pe+TmW1s8gpCsuK6e6HV9C1d45T LoJhNW1eunChM9tIkjfUPvYAmSV4fYguvnuCzrEFn2BLHo9bfM57//hLWvXdzYqF d4qaTcvpxv6TuWrV27Un9teS0aEG4TJeIjrbQN0xVBJFccFcAtlmNevvM/UaWOrD P3xNseThiTvlkorLy2hRwyqa/an5ND54U3Hl8bjQ/YFyvOYzi8kJCopYAqGwMhEm l1Ct+e18GKEWXQbh4oN6Y1iYD8UDUTU0V5jNbrtZIHA9EPDctffR/Efuv90nX/LE Wrbmv2KrPqj8Ddceol/+nJecAFY9l9x3vs9QR65dv0+oRV+0aBGqCDq64sRMBELX ygHjgSV+kq3+kaX4SDdkiv7tgqfXk1n8vz6suO4AAbcHnv+iYsULiu/YHAh+wSMP KBZ/8JNLyj5s8bo5a815EEaAVb/Vf02pXpMj/Linp6dLv0PY8NqmTZswc81DkmlA **************************************/dfg6hJwJihwWHddcY1r1WNJX3 58ykF39vb29b7E5hQg+FQl6SJERfDhilfG/cuHF7nW2tHji2EHYwGFSO4ZyhoSHK NChcwJ8tqiRQ1f2LyCxwvyduBqdev8RYQ6G34E5G4CtslLPKJvBbxdsvso++mSQJ iVcXHILGI9lrZs+eTRmmFUM09fX1Xn13o2y++R6a3iIbFe3kzTvXx8noeyl/H8yf n8nRd26M29ll98c7JkzoqGIxA/udWYsm8lQLBzgJbhx2A3eiW8ZeSJSySy0IXe+2 uw0KHY2DPmCnAe8Agb0gB+xg9UVY+5K7Kil4ccZOdumK57JrCBF6vBtBYo9Zs2Zl VOSku3E4nhBVOciKyIEWRQdGhYk+ur4vD3FjiO4Sj8NrY/DIqBORXIMy03GEjmIs dZTdIIbSmuwEIUKPvREk0zGyxI5GRUWFsqZXBvG73e5m7Q94a/qDqLluhUldcgyS ZeYasMQIyuF1EPbV9z+JyqDTQHLNoe//TGkQ9ME7sxS4o2vaYSyaN2div382gaG0 goKChtiVWWIREozL5guRLYyNjRk6D1YcC/plEFiHhpjF+6ICcYWl1oo86gNrWuZb skg6rPepn/to/4uv0kne6kWOBBsMwblrp2IYaAxwLsbcrVJYFtWA+fnRDiFRFsP3 S3MqkQNRZmM1SRKCgJtRi57h7Dm/WvDfH7M/yle3atHnrP2U4mJ/+OpbihsPsfew JdYPo2mTW66x9R4+Oz1bLV6CDQSO1wAIHY3HCn5PO668tn7Zhg0bAjZXSXWS1oMH D3YZOVGU0GX/PAEYL0eyjFGQTJMhEokcCPPYaj6ziMX+TFTmG4QKYYN4rjmsN4QN jwCvjwUNBUSvWXP04Ue4kVj/gz+3NANOjV534jn/foFsDDJrgVKj54sSunTd4wBL EAgETPXPM3RTJRO5cGBp1//gm4owNUscT+Cw3uh3Y1gtlWCnzptLR/9lamFIeAsQ /PxHTE+6iYpec/+3L9ssuiryNjOvsS30uro6ac3jYEXkgG8sSjNJRa7WF4jaNy4g VVTLfNNbYgDrvfiJNYorr4l78qax+Aa6BmgctEajrNZcDkJBcWGfPggJ1BwCWPWs uM+tiBzYFnpZWVl1FvdhMgZErqWvIisOUXRNxHDn0QAgHTYWZMql0X23ZMlF1lqD JUaf+ypbX4hU75rrh9LWct/ebMKMufNdfXd5P9vwZvsr04JvfI0wxOalDGNV5ECE 6+4hSRRo+CButg5JJ7BA7FoarH5fmjAkcg4kBmIbHggdGWSilliqZEFWxhHlqZ93 347KGxWtlpQDb8B4/5yFXEgNnQ2tiSLsRymDQleH0Fq1uIEV0u4n5gMQdmVlJbyd pH1uDKVVV1cr4+Ya8ay8A5/Px43QGiOWvK+vL+7Nn45yyZrI3YbTZc3n0vPV8KUQ Oa6Xoci2Q/hZ6GiQO8kGGc3KkEyBcXO49fpJLk4F5fi9d7NlaCJz+CnGcwteCtha 1zwV2lRWMBVY+0TpgydDPyZv0Jrv6vx8a8pCKVhfPEP99D7+P7ccPnzYTzYRYdH9 JLENrL9WlMIpq6728ZrIPNPW5Bv+8AI5CYTq1gXTEE1PlQyDvrxGSlc/Emnt/Pzf GK6GxA3xLkovu7hBXiNqJES67lkExI6JLKLTX9WKI81WAznMvtgdKL3k5EwvCB3j 7XqxQ+ixYoe7jn093/8vpQ6dRkLX3eXyU6FrTecXXjA8Bg04VrEzHVlyan98C4tc aEk227Mmzp8/H1i4cGEL30hlJLEN+u2Ch9j8/H4b4X6SRRYvXozftil2f1FVGbkX 301OoVWaQQ05zS3XIvHYd7rrEH20+21l3/jgnZEANA6er6yL577vYpE/y/1xP5nk 8uXLQb4ObnI2KNfF3biGd9991+CqxsYR0hFcv379EcqepBkEL6qzZdwzk6A/zjdO S0zeuiXi/caYxYalkdNBPGseC4bnlvAYPLZRIocVjxC76q22gmrIGeERCFwHD4kF IyDNdhrjVAiZB8ktHeYRZoPQd3E0+VkeokID5qU8RXXV/45d9e/5/f4gCWDRokWw Zl/S7wuNBKli2dy0LKoI8cId/+OxMxSeuFNeCwk2sN6rv7tZqSJbMf+uqPpzpFnx x1ttW0nVqiP1rpEEpH2rbjrqu21hz9hPDiLEomfByqlRLSJa3pKSkv48ter+cDiM SK1Q9y+RNUPk/d7n7a/UYhRE40+8+hYLvJQWP7E2bu77FDxsFqH2zi+0+kgwarZg N1m07KrAd3FMZqcIb8sIQoSeYWHBirfFXrCNGze28A2/g/KLuNdCFKjbr97gUcz7 kzVZtI6ZcwLXg3uehdrC12O7kfNVcaPrsNtJFz0RwgZrWVh7WFiNlD4wxtia7KJx vxI3pZdyH8f7eBos9rbYmxuln+/7268Ky5SzRnoEHgusO2/QAG6GoeOtRz2ExtbP j31IoeVGoS9d1jsewoSeqLUXjVacnvs1KYdH8CNw43Mkx114R614POKJPd0u/B0y I/CZhrCiZAgmcMAGC2RvIAfQAhcItu3fv99n5DUY+luyZMkYv/ZLlGMgjRVWnBu8 fxUVcDMKX1cfB6WiAp5Tix+4lOCc47hcAb4hfkxFBVs4yPbTvp+95SdJUoTmWaoB G1h1YRF4EYELduFh/XNi8UczHo3TqG5rB38mr7ZvwVPrafZaDwlnSty7KeLqktbb PMITqu1GJDVERybr6+v3sEDSGUMQDgSezkitUfjaNvJvtQ2CR3/93ucfs1wpNhp2 yylyVIrbPo6VM7FiRVVxY1ioXXRgyQlvI410qYFHP2UxWmCqqKJs84I/2+QpX1Jr /FpPJbX4+I48SmHqoyLqSzajTGIOR+sW4YfnGxTDXI9SHIGpbihSNPeFQiFfeXm5 z0lrpQ4DdswUy45+ODnQ6KWLpu4d1TTBnp2Lqm8/NMIEYQcoRIHOL5tPSXUCtaHy zNTrnYy0FijTLiSpM94yZaHiRY2zCbWe+O5cvOEygdfrrU5lQNRRow61+msn5Rh5 u4aSms0HsXsoC8hEtlS+gBgCCxjJUz6tjLN2bN26dXWIL9DUpB0/BznNL/o+A8jr xdJUDwPWfStlCHWYbBfHD3xS4M6hjxmpXUbEgjwU3dDvEj09NFuQqyJS+gWv9r33 SeudXljs/ZTEg1Nr6PkoB5FC16ETPIKHHhKEakF8/HSf2+3ulOLODKmyN/k3Wprt IxtWkTXjdKg/chOeq303Lz/VRG9oqEgbSYBryM+P8oiDT/RMMok1gsFgXwZXwsko 0qKbAOIvLCzUhog86tavbdkVD0hrnd2kcN9bsyHj0Amk0CV5BUfgB5JMcsrZqLss DinJGzZt2lSXYiajhxuCNspBpNAleUMoFLqdko2kJI6hbEEADtF23tVKU/P6tyNo RzmGDMZJ8gkvqYsixETX8dzHj52qyD0kkUhmHhCwmjQjkUgkEolEIpFIJBKJRCKR SCTO8f8Sw1p3XxsDBAAAAABJRU5ErkJggg==
`;p();d();var Ud=$(so());p();d();var Zr=class e{constructor(){this.showPopup=()=>{let t=document.createElement("link");t.href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600&display=swap",t.rel="stylesheet",document.head.appendChild(t);let r=document.createElement("div");r.style.fontFamily="Inter, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen-Sans, Ubuntu, Cantarell, 'Helvetica Neue', sans-serif",r.id="metamask-explainer",r.style.transform="scale(0.9125) translateY(15px)",r.style.willChange="transform",r.style.opacity="0",r.style.transition="all 300ms cubic-bezier(0.16, 1, 0.3, 1)",r.style.width="262px",r.style.position="fixed",r.style.top="20px",r.style.right="20px",r.style.zIndex="99999",r.style.background="#222",r.style.backgroundClip="border-box",r.style.border="1px solid rgba(80, 80, 80, 0.2)",r.style.borderRadius="4px",r.style.fontSize="13px",r.style.padding="1.5em",r.style.boxShadow="0px 54px 22px rgba(24, 24, 27, 0.02), 0px 31px 18px rgba(24, 24, 27, 0.07), 0px 14px 14px rgba(24, 24, 27, 0.12), 0px 3px 7px rgba(24, 24, 27, 0.14), rgba(24, 24, 27, 0.3) 0px 0px 2px 0px",r.style.userSelect="none",r.style["-webkit-font-smoothing"]="antialiased",r.style.MozUserSelect="none",r.style.msUserSelect="none";let n=document.createElement("div");n.style.display="flex",n.style.flexDirection="column",n.style.justifyContent="center",n.style.gap="1em",n.style.marginBottom="1em",r.appendChild(n);let a=document.createElementNS("http://www.w3.org/2000/svg","svg");a.setAttribute("width","8"),a.setAttribute("height","8"),a.setAttribute("viewBox","0 0 8 8"),a.setAttribute("fill","none"),a.style.width="0.8em",a.style.height="0.8em",a.style.display="flex",a.innerHTML=`
    <path d="M1 1L7 7M7 1L1 7" stroke="currentColor" stroke-linecap="round"></path>
    `;let o=document.createElement("div");o.id="metamask-explainer__close-button",o.style.position="absolute",o.style.top="0.5em",o.style.right="0.5em",o.style.padding="0.5em",o.style.cursor="pointer",o.style.color="rgb(153, 153, 153)",o.style.borderRadius="6px",o.style.transition="all 300ms cubic-bezier(0.16, 1, 0.3, 1)",o.tabIndex=1,o.addEventListener("mouseover",()=>{o.style.color="#fff",o.style.background="#333"}),o.addEventListener("mouseout",()=>{o.style.color="rgb(153, 153, 153)",o.style.background="none"}),o.addEventListener("click",()=>{r.style.opacity="0",r.style.transform="scale(0.96) translateY(10px)",setTimeout(()=>r.remove(),300)}),o.appendChild(a),r.appendChild(o);let l=document.createElement("div");l.id="logo-container",l.style.display="flex",l.style.flexDirection="row";let f=document.createElement("img");f.src=Qr,f.style.height="2.7em",f.style.position="relative",f.style.outline="2px solid #222",f.style.background="#222",l.appendChild(f),n.appendChild(l);let m=document.createElement("div");m.style.display="flex",m.style.flexDirection="column",m.style.gap="0.25em",m.style.lineHeight="1.2",n.appendChild(m);let u=document.createElement("div");u.id="metamask-explainer__header",u.textContent="Phantom now supports Ethereum & Polygon!",u.style.color="#ffffff",u.style.fontSize="1.125em",u.style.fontWeight="600",u.style.fontFamily="Inter",m.appendChild(u);let v=document.createElement("div");v.style.display="flex",v.style.flexDirection="column",v.style.gap="1em",v.style.alignItems="flex-start",r.appendChild(v);let b=document.createElement("div");b.id="metamask-explainer__body",b.textContent="Connecting with MetaMask will give you the option to use Phantom.",b.style.color="#ffffff",b.style.fontSize="1em",b.style.fontWeight="400",b.style.lineHeight="1.4",b.style.fontFamily="Inter",v.appendChild(b);let S=document.createElement("div");S.id="metamask-explainer__dont-show-again-button",S.textContent="Don't show again",S.style.cursor="pointer",S.style.color="#AB9FF2",S.style.fontSize="1em",S.style.fontWeight="400",S.style.lineHeight="1.2",S.style.transition="color 300ms cubic-bezier(0.16, 1, 0.3, 1)",S.tabIndex=1,S.addEventListener("mouseover",({target:_})=>{_.style.color="#ffffff"}),S.addEventListener("mouseout",({target:_})=>{_.style.color="#AB9FF2"}),S.addEventListener("click",()=>{r.style.opacity="0",r.style.transform="scale(0.96) translateY(10px)",setTimeout(()=>r.remove(),300),this.setHasBeenDismissed()}),v.appendChild(S),document.body.appendChild(r),setTimeout(()=>{r.style.transform="none",r.style.opacity="1"},0),e.hasBeenShown=!0}}static{this.hasBeenShown=!1}shouldShowPopup(){return self._phantomShowMetamaskExplainer&&!e.hasBeenShown&&!this.hasBeenDismissed()&&!ji()}hasBeenDismissed(){return self.localStorage.getItem("phantomwallet-metamask-explainer-dismissed")==="true"}setHasBeenDismissed(){self.localStorage.setItem("phantomwallet-metamask-explainer-dismissed","true")}findFaviconUrl(){let t=document.querySelectorAll('link[rel="icon"], link[rel="shortcut icon"], link[rel="apple-touch-icon"]');return t.length>0?t[0].href:""}};p();d();var qd=$(Ze());p();d();p();d();p();d();var Yi=$(_e(),1),Xi=$(_e(),1);p();d();var Hd=$(Ze());p();d();p();d();var Fd=$(Ze());p();d();p();d();var Wd=$(Ze());p();d();var $f=[gt.sol_blink_connect.method,gt.sol_blink_signAndSendAllTransactions.method];var Mp=$(Op()),De=$(Ze());p();d();var mr=$(st());async function Zi(e){try{let t=e.params;if(!Array.isArray(t))return s.invalidInput(e.id);let r=t[0];if(!Object.values(ke).includes(r))return s.invalidInput(e.id,"Invalid or missing chain.");let n=t[1];if(typeof n!="string")return s.invalidInput(e.id,"Missing private key.");let a=t[2];if(typeof n!="string")return s.invalidInput(e.id,"Missing account name.");if(!Se())return s.result(e.id,null);let l=await de.addPrivateKeyAccount(r,Oe.from(Buffer.from(n,"base64")),a);return s.result(e.id,JSON.stringify(l))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ea(e){try{let t=e.params;if(Object.values(t).length===0)return s.invalidInput(e.id);let r=Object.keys(t);if(!Object.values(ke).includes(...r))return s.invalidInput(e.id);if(Object.values(t).map(o=>typeof o!="string").reduce((o,l)=>o||l,!1))return s.invalidInput(e.id);let a=await de.addReadonlyAccount(t);return s.result(e.id,JSON.stringify(a))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ta(e){try{let t=e.params;if(!Array.isArray(t))return s.invalidInput(e.id);let r=t[0];if(typeof r!="string")return s.invalidInput(e.id);let n=t[1];if(!Array.isArray(n)||n.length===0)return s.invalidInput(e.id);let a=t[2];if(typeof a!="string")return s.invalidInput(e.id,"Missing account name.");if(!Se())return s.result(e.id,null);let f=(await or()).fromEntropy(mr.default.decode(r)),m=await de.addSeedWithMultipleAccounts(Oe.from(f.getEntropy()),a,n);return s.result(e.id,JSON.stringify(m))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ra(e){try{let t=e.params;if(!Array.isArray(t))return s.invalidInput(e.id);let r=t[0];if(typeof r!="string")return s.invalidInput(e.id);let n=t[1];if(!Array.isArray(n)||n.length===0)return s.invalidInput(e.id);let a=t[2];if(typeof a!="string")return s.invalidInput(e.id,"Missing account name.");if(!Se())return s.result(e.id,null);let f=(await or()).fromEntropy(mr.default.decode(r)),m=await de.addSeedlessWithMultipleAccounts(Oe.from(f.getEntropy()),a,n);return s.result(e.id,JSON.stringify(m))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function na(e){try{let t=e.params;if(typeof t!="string")return s.invalidInput(e.id);if(!Se())return s.result(e.id,null);let a=(await or()).fromEntropy(mr.default.decode(t)),o=await de.isExistingSeed(Oe.from(a.getEntropy()));return s.result(e.id,o)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function sa(e){try{let r=e.params[0];if(!r)return s.invalidInput(e.id,"Missing se*d identifier.");if(!Se())return s.result(e.id,null);let a=await de.fetchAllSeedMetas();if(!a||a.length===0)return s.invalidInput(e.id,"No available se*d.");if(!a.find(f=>f.identifier===r))return s.invalidInput(e.id,"Invalid se*d identifier.");let l=await de.addAccountForExistingSeed(r);return s.result(e.id,JSON.stringify(l))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function oa(e){try{let r=e.params[0];if(!r)return s.invalidInput(e.id,"Missing se*d identifier.");if(!Se())return s.result(e.id,null);let a=await de.fetchAllSeedlessMetas();if(!a||a.length===0)return s.invalidInput(e.id,"No available se*d.");if(!a.find(f=>f.identifier===r))return s.invalidInput(e.id,"Invalid se*d identifier.");let l=await de.addAccountForExistingSeedless(r);return s.result(e.id,JSON.stringify(l))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ia(e){try{let t=e.params;if(!Array.isArray(t))return s.invalidInput(e.id);let r=t[0];if(typeof r!="string")return s.invalidInput(e.id);let n=t[1];if(!Array.isArray(n)||n.some(l=>!Po.safeParse(l).success))return s.invalidInput(e.id);if(!Se())return s.result(e.id,null);let o=await de.addLedgerAccounts(r,n);return s.result(e.id,JSON.stringify(o))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function aa(e){try{let t=e.params;if(!Array.isArray(t))return s.invalidInput(e.id);let r=t[0];if(typeof r!="string")return s.invalidInput(e.id);let n=t[1];if(typeof n!="string")return s.invalidInput(e.id,"Missing account identifier.");let a=t[2];if(!Object.values(ke).includes(a))return s.invalidInput(e.id,"Invalid or missing chain.");if(!await Et(Oe.from(Buffer.from(r))))return s.invalidInput(e.id,"Invalid passw*rd.");let l=await de.exportPrivateKey(n,a);if(!l)return s.result(e.id,null);let f=l.fold(m=>Buffer.from(m.buffer).toString("hex"));return s.result(e.id,f)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ca(e){try{let t=e.params,r=t[0];if(typeof r!="string")return s.invalidInput(e.id);if(!await Et(Oe.from(Buffer.from(r))))return s.invalidInput(e.id,"Invalid passw*rd.");let a=t[1];if(!a)return s.invalidInput(e.id,"Missing se*d identifier.");let o=await de.fetchAllSeedMetas(),l=await de.fetchAllSeedlessMetas(),f=o.concat(...l);if(!f||f.length===0)return s.result(e.id,null);let m=f.find(b=>b.identifier===a);if(!m)return s.result(e.id,null);let u=await de.exportEntropy(m.identifier);if(!u)return s.result(e.id,null);let v=u.fold(b=>Buffer.from(b.buffer).toString("hex"));return s.result(e.id,v)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function pa(e){try{let t=e.params;return Array.isArray(t)?typeof t[0]!="string"?s.invalidInput(e.id):typeof t[1]!="string"?s.invalidInput(e.id):Se()?s.result(e.id,!0):s.result(e.id,null):s.invalidInput(e.id)}catch(t){return s.internalError(e.id,t.message)}}async function da(e){try{if(!Se())return s.result(e.id,null);let r=await de.deriveAddresses();return s.result(e.id,r)}catch(t){return Y.captureError(Error(`Failed to derive addresses for enabled chains in account.ts due to ${t.message}`),"account"),s.internalError(e.id,t.message)}}async function la(e){try{let t=e.params;return typeof t!="string"?s.invalidInput(e.id):Se()?(await de.removeAccount(t),s.result(e.id,!0)):s.result(e.id,null)}catch(t){return Y.captureError(Error(`Failed to remove account in account.ts due to ${t.message}`),"account"),s.internalError(e.id,t.message)}}async function ua(e){try{let{identifier:t,toIndex:r}=e.params;return typeof t!="string"||typeof r!="number"?s.invalidInput(e.id):Se()?(await de.reorderAccount(t,r),s.result(e.id,!0)):s.result(e.id,null)}catch(t){return Y.captureMessage("Failed to reorder account in account.ts","account"),s.internalError(e.id,t.message)}}async function ma(e){try{if(!Se())return s.result(e.id,null);let r=await de.fetchAllAccounts();if(!r)return s.result(e.id,null);let n=Oo({accounts:r}).accounts;return s.result(e.id,JSON.stringify(n))}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ha(e){try{if(!Se())return s.result(e.id,null);let r=await de.fetchAllSeedMetas();return r?(be.capture("walletSeeds",{data:{numOfSeeds:r.length}}),s.result(e.id,JSON.stringify(r))):s.result(e.id,null)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function fa(e){try{if(!Se())return s.result(e.id,null);let r=await de.fetchAllSeedlessMetas();return r?(be.capture("walletSeedlessSeeds",{data:{numOfSeeds:r.length}}),s.result(e.id,JSON.stringify(r))):s.result(e.id,null)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function ga(e){try{let t=e.params;if(typeof t.accountIdentifier!="string")return s.invalidInput(e.id,"Account Identifier needs to be of type string.");if(!qo(t.params))return s.invalidInput(e.id,"Invalid vault signer params.");if(!Se())return s.result(e.id,null);let n=await de.sign(t.accountIdentifier,t.params);return s.result(e.id,Qe(n))}catch(t){return Y.captureError(t,"transaction"),s.internalError(e.id,t.message)}}async function ya(e){try{let t=e.params;if("secretIdentifier"in t&&typeof t.secretIdentifier!="string")return s.invalidInput(e.id,"Secret Identifier needs to be present and of type string.");if(!Se())return s.result(e.id,null);let n=await de.getAuthenticationPublicKey(t.secretIdentifier);return s.result(e.id,Qe(n))}catch(t){return Y.captureError(t,"transaction"),s.internalError(e.id,t.message)}}async function xa(e){try{let t=e.params;if(typeof t.syncedAccounts!="object")return s.invalidInput(e.id,"syncedAccounts needs to be an object.");if(!Se())return s.result(e.id,null);let n=await de.syncAccounts(t.syncedAccounts);return s.result(e.id,n)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function wa(e){try{if(!Se())return s.result(e.id,null);let r=await de.checkVaultIntegrity();return s.result(e.id,r)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}p();d();var ba=new wi;async function Aa(e){return await ba.acquire(),s.result(e.id,!0)}async function Sa(e){return await ba.release(),s.result(e.id,!0)}p();d();async function Ra(e){try{let t=await xt.hasPersistedDeviceEncryptionKey();return s.result(e.id,t)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}async function va(e){try{return await xt.deletePersistedDeviceEncryptionKey(),s.result(e.id,!0)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}var tn=!1;function ka(e){tn=e}function rn(){return tn}async function Ta(e){try{let t=e.params;if(typeof t!="string")return s.invalidInput(e.id);let r=Oe.from(Buffer.from(t));return await Et(r)?(tn=!1,await xi(),await yi(),s.result(e.id,{isUnlocked:!0,migrationResult:"no-migration"})):s.result(e.id,{isUnlocked:!1,migrationResult:"no-migration"})}catch(t){return s.internalError(e.id,t.message)}}function _a(e){try{let t=!!Se();return s.result(e.id,t)}catch(t){return Y.captureError(t,"account"),s.internalError(e.id,t.message)}}function Ea(e){try{return Ht(),s.result(e.id,!0)}catch(t){return Y.captureError(Error(`Failed to lock extension due to ${t.message}`),"account"),s.internalError(e.id,t.message)}}async function Ia(e){try{let t=e.params;return typeof t!="string"?s.invalidInput(e.id):(await de.clearVault(),await xt.deletePersistedDeviceEncryptionKey(),Hr(Oe.from(Buffer.from(t))),await xt.persistDeviceEncryptionKey(),s.result(e.id,!0))}catch(t){return Y.captureMessage(`\u{1F6AB} Failed to Set Passw*rd in BackgroundScript: ${t.message}`,"account"),s.internalError(e.id,t.message)}}async function Ca(e){try{let t=e.params;if(typeof t!="string")return s.invalidInput(e.id);let r=await Et(Oe.from(Buffer.from(t)));return s.result(e.id,r)}catch(t){return Y.captureMessage(`\u{1F6AB} Failed to Verify Passw*rd in BackgroundScript: ${t.message}`,"account"),s.internalError(e.id,t.message)}}async function Ba(e){try{if(!Se())return s.result(e.id,null);let r=e.params;if(typeof r!="string")return s.invalidInput(e.id);let n=Oe.from(Buffer.from(r)),a=await gi.rotateEncryptionKey(n);return a&&(Y.addBreadcrumb("account","new passw*rd set in memory!","info"),Hr(n)),Y.addBreadcrumb("account",`storage key rotation success: ${a}`,"info"),s.result(e.id,a)}catch(t){return Y.captureMessage(`\u{1F6AB} Failed to Update Passw*rd in BackgroundScript: ${t.message}`,"account"),s.internalError(e.id,t.message)}}p();d();async function La(e){try{let t=e.params;if(!Array.isArray(t))return s.invalidInput(e.id);let r=t[0];if(typeof r!="string")return s.invalidInput(e.id);let n=t[1];return n.featureTag?(Ut.write(r,n.featureTag,n.severity,n.data),s.result(e.id,!0)):s.invalidInput(e.id)}catch(t){return Y.captureError(t,"bugReporter"),s.internalError(e.id,t.message)}}function Ma(e){try{return Ut.downloadLog({noop:!0}),s.result(e.id,!0)}catch(t){return Y.captureError(t,"bugReporter"),s.internalError(e.id,t.message)}}p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();p();d();var Vd=new Set(go),Q=async({source:e,request:t})=>{if(!e)return s.unauthorized(t.id);if(e.type!=="BrowserTabRpcRequestSource"&&e.type!=="InternalRpcRequestSource")return s.unauthorized(t.id);if(e.url.protocol!=="https:")return Vd.has(e.url.hostname)?void 0:s.unauthorized(t.id)};p();d();var lt=async(e,{wallet:t})=>{let{request:r}=e,n="transactions"in r.params?r.params.transactions:[r.params.transaction],a=await Promise.all(n.map(o=>t.sol.addPriorityFee(o)));"transactions"in r.params?r.params.transactions=a:r.params.transaction=a[0]};p();d();p();d();function It(e){return/\blocalhost\b|(\d{1,3}\.){3}\d{1,3}/.test(e)}var Vt=async({request:e,source:t},{analytics:r})=>t?t.type!=="BrowserTabRpcRequestSource"?s.internalError(e.id,"missing source"):async n=>{let{method:a}=e,o=t.url.hostname;if(It(o))return;let l={method:a,chainType:"bip122",chainId:"000000000019d6689c085ae165831e93",origin:o};if(a==="btc_requestAccounts"){let f={...l};"error"in n?r.declineConnection({...f,errorCode:n.error.code}):r.approveConnection(f)}if(a==="btc_signPSBT"){let f={...l};"error"in n?r.declineTransaction({...f,errorCode:n.error.code}):r.approveTransaction(f)}if(a==="btc_signMessage"){let f={...l};"error"in n?r.declineSignMessage({...f,errorCode:n.error.code}):r.approveSignMessage(f)}}:s.internalError(e.id,"missing source");p();d();var Ce=async({request:e,source:t},{analytics:r,wallet:n})=>t?t.type!=="BrowserTabRpcRequestSource"&&t.type!=="InternalRpcRequestSource"?s.internalError(e.id,"missing source"):async a=>{if("error"in a)return;let o=t.url.toString();switch(e.method){case"eth_sendTransaction":{let l=a.result;return r.eth.submittedTransaction({origin:o,request:e,signatures:[l]})}case"eth_sign":case"personal_sign":case"eth_signTypedData":case"eth_signTypedData_v3":case"eth_signTypedData_v4":return r.eth.signedMessage({origin:o,request:e,chainId:await n.eth.getNetworkId(o)});case"sol_signAllTransactions":{let l=a.result.map(({signature:f})=>f);return r.sol.submittedTransaction({origin:o,request:e,chainId:await n.sol.getNetworkId(),signatures:l})}case"sol_signAndSendTransaction":{let l=a.result.signature;return r.sol.submittedTransaction({origin:o,request:e,chainId:await n.sol.getNetworkId(),signatures:[l]})}case"sol_signAndSendAllTransactions":{let l=a.result.signatures.filter(Boolean);return r.sol.submittedTransaction({origin:o,request:e,chainId:await n.sol.getNetworkId(),signatures:l})}case"sol_signTransaction":{let l=a.result.signature;return r.sol.submittedTransaction({origin:o,request:e,chainId:await n.sol.getNetworkId(),signatures:[l]})}case"sol_signIn":case"sol_signMessage":return r.sol.signedMessage({origin:o,request:e,chainId:await n.sol.getNetworkId()})}}:s.internalError(e.id,"missing source");p();d();var nn=async(e,{wallet:t,rpc:r,utils:n})=>{let{request:a,source:o}=e;if(!o)return s.internalError(a.id,"missing source");if(o.type!=="BrowserTabRpcRequestSource")return s.internalError(a.id,"missing source");let l=await t.eth.getSelectedAccount(),f=await t.eth.getNetworkId(o.url.origin),m=await t.eth.getRpcURL(f),[u]=a.params;if(u.type="0x2",u.chainId||(u.chainId=f),u.nonce||(u.nonce=await t.eth.getAccountNonce(f,l)),!u.gas){let v=await r.eth.eth_estimateGas(m,[u]);if("error"in v)return s.error(a.id,v.error);u.gas=n.eth.getBufferedGasLimit(v.result)}delete u.gasPrice};p();d();var F=async(e,{config:t,logger:r})=>{if(t.env==="production")return;let n=Date.now();return a=>{if(e.source?.type!=="BrowserTabRpcRequestSource"&&e.source?.type!=="InternalRpcRequestSource"||!("error"in a&&t.logRequestsOnError||(typeof t.logRequestMethods=="boolean"?t.logRequestMethods:t.logRequestMethods.has(e.request.method))))return;let f=Date.now();r.debug(`
*-------------------------------------------------------------------------------
| method   | ${e.request.method}
| id       | ${e.request.id}
| params   | ${JSON.stringify(e.request.params).substring(0,67)}
*----------+--------------------------------------------------------------------
| result   | ${JSON.stringify(a.result)}
| error    | ${JSON.stringify(a.error)}
*----------+--------------------------------------------------------------------
| tabId | ${JSON.stringify(e.source?.type==="BrowserTabRpcRequestSource"?e.source.tabId:null)}
| title    | ${JSON.stringify(e.source?.title)}
| url      | ${JSON.stringify(e.source?.url)}
| icon     | ${JSON.stringify(e.source?.icon)}
*----------+--------------------------------------------------------------------
| duration | ${f-n}ms
*-------------------------------------------------------------------------------
`)}};p();d();var se=async({request:e,source:t},{analytics:r,wallet:n,utils:a})=>t?t.type!=="BrowserTabRpcRequestSource"?s.internalError(e.id,"missing source"):async o=>{let{method:l}=e,f=t.url.hostname;if(It(f))return;let m=await n.eth.getNetworkId(t.url.origin),u=a.eth.hexToCAIP2(m),b={method:l,chainType:"eip155",chainId:u,origin:f};if(l==="eth_requestAccounts"){let S={...b};"error"in o?r.declineConnection({...S,errorCode:o.error.code}):r.approveConnection(S)}if(l==="eth_sendTransaction"){let[S]=e.params,_={...b,gas:S.gas,maxFeePerGas:S.maxFeePerGas,maxPriorityFeePerGas:S.maxPriorityFeePerGas};"error"in o?r.declineTransaction({..._,errorCode:o.error.code}):r.approveTransaction(_)}if(l==="eth_sign"||l==="personal_sign"||l==="eth_signTypedData"||l==="eth_signTypedData_v3"||l==="eth_signTypedData_v4"){let S={...b};"error"in o?r.declineSignMessage({...S,errorCode:o.error.code}):r.approveSignMessage(S)}if(l==="wallet_selectEthereumProvider"&&!("error"in o)){let S=o.result,_={...b,selection:S};r.metamaskOverrideSelection(_)}}:s.internalError(e.id,"missing source");p();d();var Ne=async({source:e,request:t},{wallet:r,logger:n})=>{if(!e)return s.unauthorized(t.id);if(e.type==="InternalRpcRequestSource")return;if(e.type!=="BrowserTabRpcRequestSource")return s.unauthorized(t.id);let a=e.url.origin;if(!await r.isTrustedApp(a))return n.debug("> Dapp not trusted",a),s.unauthorized(t.id)};p();d();var ut=async({source:e,request:t},{wallet:r,analytics:n})=>{if(!e)return s.unauthorized(t.id);if(e.type!=="BrowserTabRpcRequestSource")return s.unauthorized(t.id);let a;switch(t.method){case"eth_sendTransaction":{let[u]=t.params;if(!u.chainId)return s.transactionRejected(t.id);a=u.chainId;break}case"eth_signTypedData":{let[u]=t.params,v="";for(let b of u)if(b.name==="chainId"){v=b.value;break}if(!v)return;a="0x"+parseInt(v,10).toString(16);break}case"eth_signTypedData_v3":{let[u,v]=t.params;if(!v.domain.chainId)return;a="0x"+v.domain.chainId.toString(16);break}case"eth_signTypedData_v4":{let[u,v]=t.params;if(typeof v.domain.chainId=="number")a="0x"+v.domain.chainId.toString(16);else return;break}case"wallet_switchEthereumChain":case"wallet_addEthereumChain":{let[u]=t.params;a=u.chainId;break}}let o={url:e.url.toString(),icon:e.icon?.href??null,tabId:e.tabId};if(!new Set(r.eth.getSupportedNetworks()).has(a))return n.eth.unsupportedChain({origin:e.url.origin,data:{method:t.method,hexChainId:a}}),await r.requestUserApproval({jsonrpc:"2.0",method:"user_confirmUnsupportedNetwork",id:0,params:[o,a]}),s.chainDisconnected(t.id);let f=new Set(await r.eth.getEnabledChains());if(!f.has(a))return await r.requestUserApproval({jsonrpc:"2.0",method:"user_confirmIncorrectMode",id:0,params:[o,f.has("0x1")?"mainnet":"testnet"]}),s.chainDisconnected(t.id);if(["eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4"].includes(t.method)){let u=await r.eth.getNetworkId(e.url.origin);if(u!==a)return await r.requestUserApproval({jsonrpc:"2.0",method:"user_confirmEIP712IncorrectChainId",id:0,params:[o,{connectedChainId:u,messageChainId:a}]}),s.chainDisconnected(t.id)}};p();d();var Kd=0,sn=async(e,{wallet:t,i18n:r})=>{let{request:n,source:a}=e;if(!a)return s.unauthorized(n.id,r.t("rpcErrorUnauthorizedUnknownSource"));if(a.type!=="BrowserTabRpcRequestSource")return s.unauthorized(n.id,r.t("rpcErrorUnauthorizedUnknownSource"));let o={url:a.url.toString(),icon:a.icon?.href??null,tabId:a.tabId};if(!await t.eth.doesSupportEth())return await t.requestUserApproval({jsonrpc:"2.0",method:"user_confirmUnsupportedAccount",params:[o,"ethereum"],id:Kd++}),s.resourceUnavailable(n.id)};p();d();var Ua=$(Ct()),on=async({request:e})=>{let t=Ua.default.decode(e.params.message);if(!uo(t))return s.invalidInput(e.id,"You cannot sign solana transactions using sign message")};p();d();var Xd=0,an=async(e,{wallet:t})=>{let{request:r,source:n}=e;if(!n)return s.userRejectedRequest(r.id);if(n.type!=="BrowserTabRpcRequestSource")return s.userRejectedRequest(r.id);if(await t.isTrustedApp(n.url.origin))return;let o=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveBtcRequestAccounts",id:Xd++,params:[{url:n.url.toString(),title:n.title??"",icon:n.icon?.href??null,tabId:n.tabId}]});if("error"in o)return s.error(r.id,o.error)};p();d();var cn=async({request:e,source:t},{wallet:r,i18n:n})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let a=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveBtcSignMessage",id:Date.now(),params:[{url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},{message:e.params.message}]});if("error"in a)return s.error(e.id,a.error);switch(a.result.type){case"send":return s.result(e.id,{signature:a.result.signature,signedMessage:a.result.signedMessage});case"signAndSend":return}};p();d();var pn=async({request:e,source:t},{wallet:r,i18n:n})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let a=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveBtcSignPSBT",id:Date.now(),params:[{url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},{psbt:e.params[0],inputsToSign:e.params[1].inputsToSign,finalize:e.params[1].finalize}]});if("error"in a)return s.error(e.id,a.error);switch(a.result.type){case"send":return s.result(e.id,a.result.signature);case"signAndSend":return}};p();d();var Jd=0,dn=async(e,{wallet:t})=>{let{request:r,source:n}=e;if(!n)return s.userRejectedRequest(r.id);if(n.type!=="BrowserTabRpcRequestSource")return s.userRejectedRequest(r.id);if(await t.isTrustedApp(n.url.origin))return;let o=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthRequestAccounts",id:Jd++,params:[{url:n.url.toString(),title:n.title??"",icon:n.icon?.href??null,tabId:n.tabId}]});if("error"in o)return s.error(r.id,o.error)};p();d();var Qd=0,ln=async(e,{wallet:t})=>{let{request:r,source:n}=e;if(!n)return s.userRejectedRequest(r.id);if(n.type!=="BrowserTabRpcRequestSource")return s.userRejectedRequest(r.id);if(await t.isTrustedApp(n.url.origin))return;let o=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveWalletRequestPermissions",id:Qd++,params:[{url:n.url.toString(),title:n.title??"",icon:n.icon?.href??null,tabId:n.tabId}]});if("error"in o)return s.error(r.id,o.error)};p();d();p();d();var Be=e=>["OK","RATE_LIMIT_EXCEEDED","SESSION_EXPIRED","SIMULATION_FAILED","UNKNOWN","UNSUPPORTED_NETWORK_ID","UNSUPPORTED_METHOD"].includes(e);var un=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let[o]=e.params;if(await r.eth.getSelectedAccount()!==o.from.toLowerCase())return s.transactionRejected(e.id);let f=await r.eth.autoConfirmValidateTransaction(t.url,[o]),m=f.status==="OK";if(Be(f.status)&&await a.eth.autoConfirmedTransaction({request:e,chainId:await r.eth.getNetworkId(t.url.origin),origin:t.url.origin,data:{sessionStartTime:f.sessionStartTime,sessionMaxDuration:f.sessionMaxDuration,lastStatusCode:f.status,dollarValue:f.dollarValue,auditTrail:f.auditTrail}}),m)return;let u={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},v=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthSendTransaction",id:Date.now(),params:[u,{transaction:o,autoConfirmStatusCode:f.status}]});if("error"in v)return s.error(e.id,v.error);switch(v.result.maxFeePerGas&&(o.maxFeePerGas=v.result.maxFeePerGas),v.result.maxPriorityFeePerGas&&(o.maxPriorityFeePerGas=v.result.maxPriorityFeePerGas),v.result.type){case"send":{let b=await r.eth.sendTransaction(o,v.result.signature);return s.any(e.id,{result:b})}case"signAndSend":return}};p();d();var mn=async({request:e,source:t},{wallet:r,i18n:n})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let a=await r.eth.getSelectedAccount(),o=await r.eth.getNetworkId(t.url.origin),[l,f]=e.params;if(a!==l.toLowerCase())return s.unauthorized(e.id);let m={url:t.url.href,title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},u=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthSignMessage",id:Date.now(),params:[m,{signer:l,message:f,originalMethod:"eth_sign",chainId:o,autoConfirmStatusCode:"UNSUPPORTED_METHOD"}]});if("error"in u)return s.error(e.id,u.error);switch(u.result.approvalType){case"hardware":return s.result(e.id,u.result.signature);case"user":return}};p();d();var hn=async({request:e,source:t},{wallet:r,i18n:n})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let a=await r.eth.getSelectedAccount(),o=await r.eth.getNetworkId(t.url.origin),[l,f]=e.params;if(a!==f.toLowerCase())return s.unauthorized(e.id);let m="0x"+Buffer.from(JSON.stringify(l)).toString("hex"),u={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},v=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthSignMessage",id:Date.now(),params:[u,{signer:f,message:m,originalMethod:"eth_signTypedData",chainId:o,autoConfirmStatusCode:"UNSUPPORTED_METHOD"}]});if("error"in v)return s.error(e.id,v.error)};p();d();var fn=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.eth.getSelectedAccount(),l=await r.eth.getNetworkId(t.url.origin),[f,m]=e.params;if(o!==f.toLowerCase())return s.unauthorized(e.id);let u=await r.eth.autoConfirmValidateMessage(t.url,m),v=u.status==="OK";if(Be(u.status)&&await a.eth.autoConfirmedMessage({request:e,chainId:await r.eth.getNetworkId(t.url.origin),origin:t.url.origin,data:{sessionStartTime:u.sessionStartTime,sessionMaxDuration:u.sessionMaxDuration,lastStatusCode:u.status,auditTrail:u.auditTrail}}),v)return;let b={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},S=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthSignMessage",id:Date.now(),params:[b,{signer:f,message:"0x"+Buffer.from(JSON.stringify(m)).toString("hex"),originalMethod:"eth_signTypedData_v3",chainId:l,autoConfirmStatusCode:u.status}]});if("error"in S)return s.error(e.id,S.error)};p();d();var gn=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.eth.getSelectedAccount(),l=await r.eth.getNetworkId(t.url.origin),[f,m]=e.params;if(o!==f.toLowerCase())return s.unauthorized(e.id);let u=await r.eth.autoConfirmValidateMessage(t.url,m),v=u.status==="OK";if(Be(u.status)&&await a.eth.autoConfirmedMessage({request:e,chainId:await r.eth.getNetworkId(t.url.origin),origin:t.url.origin,data:{sessionStartTime:u.sessionStartTime,sessionMaxDuration:u.sessionMaxDuration,lastStatusCode:u.status,auditTrail:u.auditTrail}}),v)return;let b={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},S=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthSignMessage",id:Date.now(),params:[b,{signer:f,message:"0x"+Buffer.from(JSON.stringify(m)).toString("hex"),originalMethod:"eth_signTypedData_v4",chainId:l,autoConfirmStatusCode:u.status}]});if("error"in S)return s.error(e.id,S.error)};p();d();var yn=async({request:e,source:t},{wallet:r,i18n:n})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let a=await r.eth.getSelectedAccount(),o=await r.eth.getNetworkId(t.url.origin),[l,f]=e.params;if(a!==f.toLowerCase())return s.unauthorized(e.id);let m={url:t.url.href,title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},u=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveEthSignMessage",id:Date.now(),params:[m,{signer:f,message:l,originalMethod:"personal_sign",chainId:o,autoConfirmStatusCode:"UNSUPPORTED_METHOD"}]});if("error"in u)return s.error(e.id,u.error);switch(u.result.approvalType){case"hardware":return s.result(e.id,u.result.signature);case"user":return}};p();d();var Zd=0,xn=async(e,{wallet:t})=>{let{request:r,source:n}=e;if(!n)return s.userRejectedRequest(r.id);if(n.type!=="BrowserTabRpcRequestSource")return s.userRejectedRequest(r.id);if(await t.isTrustedApp(n.url.origin))return;if(r.params?.onlyIfTrusted===!0)return s.userRejectedRequest(r.id);let o=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolConnect",id:Zd++,params:[{url:n.url.toString(),title:n.title??"",icon:n.icon?.href??null,tabId:n.tabId}]});if("error"in o)return s.error(r.id,o.error)};p();d();var el=0,wn=async({request:e,source:t},{wallet:r,utils:n})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);let a=e.params.context,o=n.getOrigin(a.blinkUrl);if(!a.blinkUrl||!o)return s.userRejectedRequest(e.id);if(await r.isTrustedApp(o))return;let f=n.getHostname(a.blinkUrl),m=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolConnect",id:el++,params:[{tabId:t.tabId,url:a.blinkUrl,title:a.blinkTitle??"",icon:f?n.getFaviconURL(f):null}]});if("error"in m)return s.error(e.id,m.error)};p();d();var bn=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.sol.autoConfirmValidateTransaction(t.url,e.params.transactions),l=o.status==="OK";if(Be(o.status)&&await a.sol.autoConfirmedTransaction({request:e,chainId:await r.sol.getNetworkId(),origin:t.url.origin,data:{sessionStartTime:o.sessionStartTime,sessionMaxDuration:o.sessionMaxDuration,lastStatusCode:o.status,dollarValue:o.dollarValue,auditTrail:o.auditTrail}}),l)return;let f={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},m=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAllTransactions",id:Date.now(),params:[f,{transactions:e.params.transactions,autoConfirmStatusCode:o.status}]});if("error"in m)return s.error(e.id,m.error);switch(m.result.type){case"send":return s.result(e.id,m.result.result.map(u=>({transaction:u.signedTransaction,signature:u.signature,version:u.version})));case"signAndSend":{let u=m.result.overwriteTransactions;u&&(e.params.transactions=u);return}}};p();d();var An=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.sol.autoConfirmValidateTransaction(t.url,e.params.transactions),l=o.status==="OK";if(Be(o.status)&&await a.sol.autoConfirmedTransaction({request:e,chainId:await r.sol.getNetworkId(),origin:t.url.origin,data:{sessionStartTime:o.sessionStartTime,sessionMaxDuration:o.sessionMaxDuration,lastStatusCode:o.status,dollarValue:o.dollarValue,auditTrail:o.auditTrail}}),l)return;let f={url:t.url.href,title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},m=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAndSendAllTransactions",id:Date.now(),params:[f,{transactions:e.params.transactions,autoConfirmStatusCode:o.status}]});if("error"in m)return s.error(e.id,m.error);switch(m.result.type){case"send":{let u=await r.sol.getSelectedAccount();for(let[b,S]of m.result.result.entries())e.params.transactions[b]=S.signedTransaction;let v=await r.sol.sendAllTransactions(e.params.transactions);return s.result(e.id,{publicKey:u,signatures:v})}case"signAndSend":{let u=m.result.overwriteTransactions;u&&(e.params.transactions=u);return}}};p();d();var Sn=async({request:e,source:t,id:r},{wallet:n,utils:a})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);let o=a.getHostname(e.params.context.blinkUrl),l={tabId:t.tabId,url:e.params.context.blinkUrl,title:e.params.context.blinkTitle??"",icon:o?a.getFaviconURL(o):null},f=await n.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAndSendAllTransactions",id:Date.now(),params:[l,{transactions:e.params.transactions,autoConfirmStatusCode:"UNSUPPORTED_METHOD",sequenceId:r}]});if("error"in f)return s.error(e.id,f.error);switch(f.result.type){case"send":{let m=await n.sol.getSelectedAccount();for(let[v,b]of f.result.result.entries())e.params.transactions[v]=b.signedTransaction;let u=await n.sol.sendAllTransactions(e.params.transactions);return s.result(e.id,{publicKey:m,signatures:u})}case"signAndSend":return}};p();d();var Rn=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource"&&t.type!=="InternalRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.sol.autoConfirmValidateTransaction(t.url,[e.params.transaction]),l=o.status==="OK";if(Be(o.status)&&await a.sol.autoConfirmedTransaction({request:e,chainId:await r.sol.getNetworkId(),origin:t.url.origin,data:{sessionStartTime:o.sessionStartTime,sessionMaxDuration:o.sessionMaxDuration,lastStatusCode:o.status,dollarValue:o.dollarValue,auditTrail:o.auditTrail}}),l)return;let f={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.type==="BrowserTabRpcRequestSource"?t.tabId:void 0},m=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAndSendTransaction",id:Date.now(),params:[f,{transaction:e.params.transaction,autoConfirmStatusCode:o.status}]});if("error"in m)return s.error(e.id,m.error);switch(m.result.type){case"send":{let u=await r.sol.getSelectedAccount();return await r.sol.sendTransaction(m.result.signedTransaction,e.params.options),s.result(e.id,{publicKey:u,signature:m.result.signature})}case"signAndSend":{let u=m.result.overwriteTransactions;u&&(e.params.transaction=u[0]);return}}};p();d();var vn=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.sol.autoConfirmValidateMessage(t.url,e.params.message),l=o.status==="OK";if(Be(o.status)&&await a.sol.autoConfirmedMessage({request:e,chainId:await r.sol.getNetworkId(),origin:t.url.origin,data:{sessionStartTime:o.sessionStartTime,sessionMaxDuration:o.sessionMaxDuration,lastStatusCode:o.status,auditTrail:o.auditTrail}}),l)return;let f=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignMessage",id:Date.now(),params:[{url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},{...e.params,autoConfirmStatusCode:o.status}]});if("error"in f)return s.error(e.id,f.error)};p();d();var kn=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));let o=await r.sol.autoConfirmValidateTransaction(t.url,[e.params.transaction]),l=o.status==="OK";if(Be(o.status)&&await a.sol.autoConfirmedTransaction({request:e,chainId:await r.sol.getNetworkId(),origin:t.url.origin,data:{sessionStartTime:o.sessionStartTime,sessionMaxDuration:o.sessionMaxDuration,lastStatusCode:o.status,dollarValue:o.dollarValue,auditTrail:o.auditTrail}}),l)return;let f={url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},m=await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignTransaction",id:Date.now(),params:[f,{transaction:e.params.transaction,autoConfirmStatusCode:o.status}]});if("error"in m)return s.error(e.id,m.error);switch(m.result.type){case"send":return s.result(e.id,{transaction:m.result.signedTransaction,signature:m.result.signature,version:m.result.version});case"signAndSend":{let u=m.result.overwriteTransactions;u&&(e.params.transaction=u[0]);return}}};p();d();var We=async({request:e,source:t},{analytics:r,wallet:n,utils:a})=>t?t.type!=="BrowserTabRpcRequestSource"&&t.type!=="InternalRpcRequestSource"?s.internalError(e.id,"missing source"):async o=>{let{method:l}=e,f=t.url.hostname;if(It(f))return;let m=await n.sol.getNetworkId();if(m==="localnet")return;let u=a.sol.chainIdToCAIP2(m),b={method:l,chainType:"solana",chainId:u,origin:f};"error"in o&&(b.errorCode=o.error.code),l==="sol_connect"&&tl(b,r),(l==="sol_signTransaction"||l==="sol_signAndSendTransaction"||l==="sol_signAndSendAllTransactions"||l==="sol_signAllTransactions")&&rl(b,r),(l==="sol_signMessage"||l==="sol_signIn")&&nl(b,r)}:s.internalError(e.id,"missing source"),tl=(e,t)=>{"errorCode"in e?t.declineConnection(e):t.approveConnection(e)},rl=(e,t)=>{"errorCode"in e?t.declineTransaction(e):t.approveTransaction(e)},nl=(e,t)=>{"errorCode"in e?t.declineSignMessage(e):t.approveSignMessage(e)};p();d();var ze=async({request:e},{analytics:t,wallet:r,utils:n})=>async a=>{let{method:o}=e,l=e.params&&typeof e.params=="object"&&"redirect_link"in e.params&&typeof e.params.redirect_link=="string"?e.params.redirect_link:"missing redirect_link from deep link";t.deepLinkDetected({method:o,params:JSON.stringify(e.params)});let f=await r.sol.getNetworkId();if(f==="localnet")return;let m=n.sol.chainIdToCAIP2(f),v={method:o,chainType:"solana",chainId:m,origin:l};if(o==="phantom_deep_link_connect"){let b={...v};"error"in a?t.declineConnection({...b,errorCode:a.error.code}):t.approveConnection(b)}if(o==="phantom_deep_link_signTransaction"||o==="phantom_deep_link_signAllTransactions"||o==="phantom_deep_link_signAndSendTransaction"){let b={...v};"error"in a?t.declineTransaction({...b,errorCode:a.error.code}):t.approveTransaction(b)}if(o==="phantom_deep_link_signMessage"){let b={...v};"error"in a?t.declineSignMessage({...b,errorCode:a.error.code}):t.approveSignMessage(b)}};p();d();function Tn(e){return!!("result"in e&&typeof e.result=="object"&&e.result&&"type"in e.result&&e.result?.type&&typeof e.result.type=="string"&&e.result.type.toUpperCase().endsWith("_DECLINE"))}var Bt=async({request:e},{analytics:t,wallet:r,utils:n})=>async a=>{let{method:o}=e,l="no identity or verifiable identity found on solana mobile wallet adapter request";if(e.params&&typeof e.params=="object"&&"identity"in e.params){let b=co.safeParse(e.params.identity);b.success&&(l=b.data.identityUri??l)}else if(e.params&&typeof e.params=="object"&&"signPayloads"in e.params){let b=po.safeParse(e.params.signPayloads);b.success&&(l=b.data.verifiableIdentity.identity.identityUri??l)}let f=await r.sol.getNetworkId();if(f==="localnet")return;let m=n.sol.chainIdToCAIP2(f),v={method:o,chainType:"solana",chainId:m,origin:l};if(o==="sol_mwa_authorize"){let b={...v};"error"in a?t.declineConnection({...b,errorCode:a.error.code}):Tn(a)?t.declineConnection({...b,errorCode:a.result.type}):t.approveConnection(b)}if(o==="sol_mwa_sign_transactions"||o==="sol_mwa_sign_and_send_transactions"){let b={...v};"error"in a?t.declineTransaction({...b,errorCode:a.error.code}):Tn(a)?t.declineTransaction({...b,errorCode:a.result.type}):t.approveTransaction(b)}if(o==="sol_mwa_sign_messages"){let b={...v};"error"in a?t.declineSignMessage({...b,errorCode:a.error.code}):Tn(a)?t.declineSignMessage({...b,errorCode:a.result.type}):t.approveSignMessage(b)}};p();d();var hr=async({request:e},{logger:t,featureFlags:r})=>{if(!await r.isEnabled("enable-blinks-v2-config"))return t.debug("> Blinks V2 Config feature flag is disabled"),s.cancelled(e.id)};p();d();var fr=async({source:e,request:t},r)=>{if(!e||e.type!=="BrowserTabRpcRequestSource"||!["x.com","twitter.com","pro.x.com"].includes(e.url.hostname))return s.unauthorized(t.id)};p();d();var _n=async({request:e},{wallet:t,logger:r,utils:n})=>{let a=e.params.context,o=n.getOrigin(a.blinkUrl);if(!o)return s.unauthorized(e.id);if(!await t.isTrustedApp(o))return r.debug("> Dapp not trusted",o),s.unauthorized(e.id)};p();d();var gr=[],qe=e=>async({source:t,request:r})=>{let n=Date.now(),o=`${t?.type==="DeepLinkRpcRequestSource"?t.deepLinkUrl:t?.url.href}:${r.method}:${JSON.stringify(r.params)}`;if(gr=gr.filter(f=>f.timestamp>n-1e3),gr.filter(f=>f.key===o).length>=e)return s.rateLimited(r.id);gr.push({timestamp:n,key:o})};p();d();var yr=async({request:e},{wallet:t})=>{if(!await t.isUserOnboarded()){if(e.method==="sol_connect"&&e.params.onlyIfTrusted)return s.userRejectedRequest(e.id);await t.requestUserOnboard()}};p();d();p();d();p();d();var En=async({request:e,source:t},{wallet:r})=>{if(!t)return s.result(e.id,[]);if(t.type!=="BrowserTabRpcRequestSource")return s.result(e.id,[]);if(!await r.isTrustedApp(t.url.origin))return s.result(e.id,[]);let a=await r.eth.getAccounts();return s.result(e.id,a)};p();d();var In=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_blockNumber(o,e.params);return s.any(e.id,l)};p();d();var Cn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_call(o,e.params);return s.any(e.id,l)};p();d();var Bn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_cancelPrivateTransaction(o,e.params);return s.any(e.id,l)};p();d();var Ln=async({request:e,source:t},{wallet:r})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let n=await r.eth.getNetworkId(t.url.origin);return s.result(e.id,n)};p();d();var Mn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_estimateGas(o,e.params);return s.any(e.id,l)};p();d();var Pn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_feeHistory(o,e.params);return s.any(e.id,l)};p();d();var Nn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_gasPrice(o,e.params);return s.any(e.id,l)};p();d();var Dn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getBalance(o,e.params);return s.any(e.id,l)};p();d();var Un=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getBlockByHash(o,e.params);return s.any(e.id,l)};p();d();var On=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getBlockByNumber(o,e.params);return s.any(e.id,l)};p();d();var qn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getBlockReceipts(o,e.params);return s.any(e.id,l)};p();d();var Hn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getBlockTransactionCountByHash(o,e.params);return s.any(e.id,l)};p();d();var Fn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getBlockTransactionCountByNumber(o,e.params);return s.any(e.id,l)};p();d();var Wn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getCode(o,e.params);return s.any(e.id,l)};p();d();var jn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getFilterChanges(o,e.params);return s.any(e.id,l)};p();d();var Vn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getFilterLogs(o,e.params);return s.any(e.id,l)};p();d();var zn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getLogs(o,e.params);return s.any(e.id,l)};p();d();var Kn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getProof(o,e.params);return s.any(e.id,l)};p();d();var $n=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getStorageAt(o,e.params);return s.any(e.id,l)};p();d();var Gn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getTransactionByBlockHashAndIndex(o,e.params);return s.any(e.id,l)};p();d();var Yn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getTransactionByBlockNumberAndIndex(o,e.params);return s.any(e.id,l)};p();d();var Xn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getTransactionByHash(o,e.params);return s.any(e.id,l)};p();d();var Jn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getTransactionCount(o,e.params);return s.any(e.id,l)};p();d();var Qn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getTransactionReceipt(o,e.params);return s.any(e.id,l)};p();d();var Zn=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getUncleByBlockHashAndIndex(o,e.params);return s.any(e.id,l)};p();d();var es=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getUncleByBlockNumberAndIndex(o,e.params);return s.any(e.id,l)};p();d();var ts=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getUncleCountByBlockHash(o,e.params);return s.any(e.id,l)};p();d();var rs=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_getUncleCountByBlockNumber(o,e.params);return s.any(e.id,l)};p();d();var ns=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_maxPriorityFeePerGas(o,e.params);return s.any(e.id,l)};p();d();var ss=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_newBlockFilter(o,e.params);return s.any(e.id,l)};p();d();var os=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_newFilter(o,e.params);return s.any(e.id,l)};p();d();var is=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_newPendingTransactionFilter(o,e.params);return s.any(e.id,l)};p();d();var as=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_protocolVersion(o,e.params);return s.any(e.id,l)};p();d();var cs=async({request:e,source:t},{wallet:r})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);await r.addTrustedApp(t.url.origin,t);let n=await r.eth.getSelectedAccount();return s.result(e.id,[n])};p();d();var ps=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_sendRawTransaction(o,e.params);return s.any(e.id,l)};p();d();var ds=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_sendPrivateTransaction(o,e.params);return s.any(e.id,l)};p();d();var ls=async({request:e,source:t},{wallet:r,i18n:n})=>{if(!t)return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(!await r.isTrustedApp(t.url.origin))return s.unauthorized(e.id);let[o]=e.params,l=await r.eth.getSelectedAccount();if(o.from.toLowerCase()!==l.toLowerCase())return s.unauthorized(e.id,n.t("rpcErrorUnauthorizedWrongAccount"));if(o.type!=="0x2")return s.transactionRejected(e.id);if(!o.chainId)return s.transactionRejected(e.id);if(!o.nonce)return s.transactionRejected(e.id);let f=await r.eth.signAndSendTransaction(o);return s.any(e.id,{result:f})};p();d();var us=async({request:e},{wallet:t,i18n:r})=>{let[n,a]=e.params,o=await t.eth.getSelectedAccount();if(n.toLowerCase()!==o)return s.unauthorized(e.id,r.t("Message signer address does not match the selected account address."));let l=await t.eth.signMessage(a);return s.result(e.id,l)};p();d();var ms=async({request:e},{wallet:t,i18n:r})=>{let[n,a]=e.params,o=await t.eth.getSelectedAccount();if(a.toLowerCase()!==o)return s.unauthorized(e.id,r.t("Message signer address does not match the selected account address."));let l=await t.eth.signTypedData({version:1,data:n});return s.result(e.id,l)};p();d();var hs=async({request:e},{wallet:t,i18n:r})=>{let[n,a]=e.params,o=await t.eth.getSelectedAccount();if(n.toLowerCase()!==o)return s.unauthorized(e.id,r.t("Message signer address does not match the selected account address."));let l=await t.eth.signTypedData({version:3,data:a});return s.result(e.id,l)};p();d();var fs=async({request:e},{wallet:t,i18n:r})=>{let[n,a]=e.params,o=await t.eth.getSelectedAccount();if(n.toLowerCase()!==o)return s.unauthorized(e.id,r.t("Message signer address does not match the selected account address."));let l=await t.eth.signTypedData({version:4,data:a});return s.result(e.id,l)};p();d();var gs=async({request:e},t)=>s.unsupportedMethod(e.id);p();d();var ys=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_syncing(o,e.params);return s.any(e.id,l)};p();d();var xs=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.eth_uninstallFilter(o,e.params);return s.any(e.id,l)};p();d();var ws=async({request:e},t)=>s.unsupportedMethod(e.id);p();d();var bs=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.net_listening(o,e.params);return s.any(e.id,l)};p();d();var As=async({request:e,source:t},{wallet:r})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let n=await r.eth.getNetworkId(t.url.origin);return s.result(e.id,parseInt(n,16).toString())};p();d();var Ss=async({request:e},{wallet:t,i18n:r})=>{let[n,a]=e.params,o=await t.eth.getSelectedAccount();if(a.toLowerCase()!==o)return s.unauthorized(e.id,r.t("Message signer address does not match the selected account address."));let l=await t.eth.signMessage(n);return s.result(e.id,l)};p();d();var Rs=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{let[o]=e.params;return await r.eth.addChain(o)?t?t.type!=="BrowserTabRpcRequestSource"?s.internalError(e.id,"missing source"):await r.eth.switchChain(o.chainId,t.url.origin)?s.result(e.id,null):(a.eth.unsupportedChain({origin:t.url.origin,data:{method:e.method,hexChainId:o.chainId,chainName:o.chainName}}),s.internalError(e.id,n.t("Could not switch to the requested chain."))):s.internalError(e.id,"missing source"):s.userRejectedRequest(e.id,n.t("Could not add the requested chain. It is not yet supported."))};p();d();var vs=async({request:e,source:t},{wallet:r})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);if(await r.isTrustedApp(t.url.origin))return s.result(e.id,io.enum.CONTINUE_WITH_PHANTOM);let a=await r.requestUserApproval({jsonrpc:"2.0",method:"user_selectEthWallet",id:0,params:[{url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId}]});if("error"in a)return s.error(e.id,a.error);switch(a.result){case yt.user_selectEthWallet.result.enum.CONTINUE_WITH_METAMASK:return s.result(e.id,a.result);case yt.user_selectEthWallet.result.enum.CONTINUE_WITH_PHANTOM:return s.result(e.id,a.result);case yt.user_selectEthWallet.result.enum.ALWAYS_USE_PHANTOM:return await r.setMetaMaskOverrideSetting("USE_PHANTOM"),s.result(e.id,a.result);case yt.user_selectEthWallet.result.enum.ALWAYS_USE_METAMASK:return await r.setMetaMaskOverrideSetting("USE_METAMASK"),s.result(e.id,a.result)}};p();d();var ks=async({request:e,source:t},{wallet:r,i18n:n,analytics:a})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let[{chainId:o}]=e.params;return await r.eth.switchChain(o,t.url.origin)?s.result(e.id,null):(a.eth.unsupportedChain({origin:t.url.origin,data:{method:e.method,hexChainId:o}}),s.chainDisconnected(e.id,n.t("Could not switch to the requested chain. It is not yet supported.")))};p();d();var Ts=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.web3_clientVersion(o,e.params);return s.any(e.id,l)};p();d();var _s=async({request:e,source:t},{wallet:r,rpc:n})=>{if(!t)return s.internalError(e.id,"missing source");if(t.type!=="BrowserTabRpcRequestSource")return s.internalError(e.id,"missing source");let a=await r.eth.getNetworkId(t.url.origin),o=await r.eth.getRpcURL(a),l=await n.eth.web3_sha3(o,e.params);return s.any(e.id,l)};p();d();var Es=async({request:e,source:t},{wallet:r})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);let n=e.params[0];return Object.keys(n).includes("eth_accounts")?(await r.addTrustedApp(t.url.origin,t),s.result(e.id,[{parentCapability:"eth_accounts",date:Date.now()}])):s.result(e.id,[])};p();d();var Is=async({request:e,source:t},{wallet:r})=>t?t.type!=="BrowserTabRpcRequestSource"?s.result(e.id,[]):await r.isTrustedApp(t.url.origin)?s.result(e.id,[{invoker:t.url.origin,parentCapability:"eth_accounts",caveats:[]}]):s.result(e.id,[]):s.result(e.id,[]);var Ge={};Ie(Ge,{sol_connect:()=>qa,sol_disconnect:()=>Ha,sol_signAllTransactions:()=>Fa,sol_signAndSendAllTransactions:()=>ja,sol_signAndSendTransaction:()=>Wa,sol_signIn:()=>za,sol_signMessage:()=>Va,sol_signTransaction:()=>Ka});p();d();p();d();var qa=async({request:e,source:t},{wallet:r})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);await r.addTrustedApp(t.url.origin,t);let n=await r.sol.getSelectedAccount();return s.result(e.id,{publicKey:n})};p();d();var Ha=async({request:e,source:t})=>t?t.type!=="BrowserTabRpcRequestSource"?s.unauthorized(e.id):s.result(e.id,null):s.unauthorized(e.id);p();d();var Fa=async({request:e},{wallet:t})=>{let r=await t.sol.signAllTransactions(e.params.transactions);return s.result(e.id,r)};p();d();var Wa=async({source:e,request:t},{wallet:r,i18n:n})=>{if(!e)return s.unauthorized(t.id,n.t("rpcErrorUnauthorizedUnknownSource"));if(e.type!=="BrowserTabRpcRequestSource"&&e.type!=="InternalRpcRequestSource")return s.unauthorized(t.id,n.t("rpcErrorUnauthorizedUnknownSource"));let a=await r.sol.getSelectedAccount(),o=await r.sol.signTransaction(t.params.transaction);return t.params.showConfirmation&&r.requestUserApproval({jsonrpc:"2.0",method:"user_solTransactionConfirmation",id:Date.now(),params:[{url:e.url.toString(),icon:e.icon?.href??null},{signature:o.signature,postAction:t.params.postAction}]}),await r.sol.sendTransaction(o.transaction,t.params.options),s.result(t.id,{publicKey:a,signature:o.signature})};p();d();var ja=async({request:e},{wallet:t})=>{let r=await t.sol.getSelectedAccount(),n=await Promise.all(e.params.transactions.map(o=>t.sol.signTransaction(o))),a=await t.sol.sendAllTransactions(n.map(o=>o.transaction));return s.result(e.id,{publicKey:r,signatures:a})};p();d();var Va=async({request:e},{wallet:t})=>{let r=await t.sol.getSelectedAccount(),n=await t.sol.signMessage(e.params.message);return s.result(e.id,{publicKey:r,signature:n})};p();d();var za=async({request:e,source:t},{wallet:r,utils:n,i18n:a})=>{if(!t)return s.userRejectedRequest(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.userRejectedRequest(e.id);let o=await r.isTrustedApp(t.url.origin),l=await r.sol.getNetworkId(),f=await r.sol.getSelectedAccount(),m=new URL(t.url.toString()),u=n.sol.serializeSolanaSignInMessage(e.params.signInData,{expectedChainId:l,expectedAddress:f,expectedURL:m});if(u.type==="fail"){if(u.error[0].label==="PARSE_ERROR")return s.invalidInput(e.id,a.t("The app's signature request cannot be shown due to invalid formatting."));if(u.error.some(S=>S.label==="chain"))return s.invalidInput(e.id,a.t("The app's signature request cannot be shown as the chain ID does not match the provided chain ID for verification."));if(u.error.some(S=>S.label==="address"))return s.invalidInput(e.id,a.t("The app's signature request cannot be shown as the address does not match the provided address for verification."))}if(e.params.signInData.domain||(e.params.signInData.domain=m.host),e.params.signInData.address||(e.params.signInData.address=f),"error"in await r.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignIn",params:[{url:t.url.toString(),title:t.title??"",icon:t.icon?.href??null,tabId:t.tabId},{connect:!o,signInData:e.params.signInData,message:u.data,errorDetails:u.type==="fail"?u.error:void 0}],id:Date.now()}))return u.type==="fail"?s.invalidInput(e.id,a.t("This sign-in request is invalid. This either means the site is unsafe, or its developer made an error when sending the request.")):s.userRejectedRequest(e.id);o||await r.isTrustedApp(t.url.origin)||await r.addTrustedApp(t.url.origin,t);let b=await r.sol.signMessage(u.data);return s.result(e.id,{address:f,signedMessage:u.data,signature:b})};p();d();var Ka=async({request:e},{wallet:t})=>{let r=await t.sol.signTransaction(e.params.transaction);return s.result(e.id,r)};var Lt={};Ie(Lt,{btc_requestAccounts:()=>$a,btc_signMessage:()=>Ga,btc_signPSBT:()=>Ya});p();d();p();d();var $a=async({request:e,source:t},{wallet:r})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);await r.addTrustedApp(t.url.origin,t);let n=await r.btc.getSelectedAccount();return s.result(e.id,n)};p();d();var Ga=async({request:e},{wallet:t})=>{let r=await t.btc.signMessage(e.params.address,e.params.message);return s.result(e.id,{...r})};p();d();var Ya=async({request:e,source:t},{wallet:r})=>{if(!t)return s.unauthorized(e.id);if(t.type!=="BrowserTabRpcRequestSource")return s.unauthorized(e.id);let n=await r.btc.signPSBT(...e.params);return s.result(e.id,n)};var Le={};Ie(Le,{phantom_deep_link_browse:()=>Xa,phantom_deep_link_connect:()=>Za,phantom_deep_link_disconnect:()=>ec,phantom_deep_link_fungible:()=>Qa,phantom_deep_link_onboard:()=>pc,phantom_deep_link_signAllTransactions:()=>oc,phantom_deep_link_signAndSendAllTransactions:()=>ac,phantom_deep_link_signAndSendTransaction:()=>ic,phantom_deep_link_signIn:()=>nc,phantom_deep_link_signMessage:()=>tc,phantom_deep_link_signTransaction:()=>sc,phantom_deep_link_swap:()=>Ja,phantom_deep_link_tokens:()=>cc});p();d();p();d();var Xa=async({request:e},{wallet:t})=>{let{url:r,ref:n}=e.params;return t.navigate("DetachedBrowser",{url:r,ref:n}),s.result(e.id,null)};p();d();var Ja=async({request:e},{wallet:t})=>(t.navigate("SwapTab",{screen:"Swap",params:{buyFungible:e.params.buy,sellFungible:e.params.sell,navRef:e.id}}),s.result(e.id,null));p();d();var Qa=async({request:e},{wallet:t,featureFlags:r})=>{let n=await r.isEnabled("enable-unified-token-pages");return Eo(e.params.token),t.navigate("FungibleStack",{screen:n?"UnifiedFungibleDetail":"PublicFungibleDetail",params:{caip19:e.params.token,title:void 0,entryPoint:"deepLink"}}),s.result(e.id,null)};p();d();var sl=0,Za=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolConnect",id:sl++,params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)}]});if("error"in a)return s.error(e.id,a.error);let o=new URL(e.params.app_url).origin??"",l=new URL(e.params.redirect_link).origin;l=l==="null"?o:l,await t.isTrustedApp(l)&&await t.deleteTrustedApp(l),await t.addTrustedApp(l,{url:new URL(l),icon:new URL(n.getFaviconURL(l))});let f=await t.sol.getSelectedAccount(),{sharedSecret:m,phantomKeySecret:u,phantomKeyPublicKey:v}=r.generateSharedSecret(e.params.dapp_encryption_public_key);await r.setSharedSecret(new le.PublicKey(f).toBase58(),e.params.dapp_encryption_public_key,m,u,v);let b=await r.generateSession(new le.PublicKey(f).toBase58(),e.params?.cluster??"mainnet-beta",e.params.app_url),{nonce:S,encryptedPayload:_}=await r.encryptDeepLinkDappPayload(b,new le.PublicKey(f).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{phantom_encryption_public_key:v,nonce:S,data:_})};p();d();var ec=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=new URL(e.params.redirect_link).origin??"";await t.isTrustedApp(a)&&await t.deleteTrustedApp(a);let o=await t.sol.getSelectedAccount(),l=n.sol.chainIdToCluster(await t.sol.getNetworkId());return typeof(await r.decryptDeepLinkDappPayload(e.params,e.params.dapp_encryption_public_key,new le.PublicKey(o),l)).session!="string"?s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."}):(r.deleteSharedSecret(o,e.params.dapp_encryption_public_key),s.result(e.id,null))};p();d();var tc=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=await t.sol.getSelectedAccount(),o=n.sol.chainIdToCluster(await t.sol.getNetworkId()),l=e.params,f=await r.decryptDeepLinkDappPayload(l,e.params.dapp_encryption_public_key,new le.PublicKey(a),o);if(typeof f.session!="string"||typeof f.message!="string"||!(!f.display||["utf8","hex"].includes(f.display)))return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});let m={message:f.message,display:f.display??"utf8"},u=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignMessage",id:Date.now(),params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)},{...m,autoConfirmStatusCode:"DISABLED"}]});if("error"in u)return s.error(e.id,u.error);let b={signature:await t.sol.signMessage(f.message),publicKey:a},{nonce:S,encryptedPayload:_}=await r.encryptDeepLinkDappPayload(b,new le.PublicKey(a).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{nonce:S,data:_})};p();d();var rc=$(Ct()),nc=async({request:e},{wallet:t,secrets:r,utils:n,i18n:a})=>{let o;try{o=JSON.parse(Buffer.from(rc.default.decode(e.params.payload)).toString())}catch{return s.invalidInput(e.id,a.t("The app's signature request cannot be shown due to invalid formatting. Please contact the app developer for assistance."))}let l=new URL(e.params.app_url).origin??"",f=new URL(e.params.redirect_link).origin;f=f==="null"?l:f;let m=await t.isTrustedApp(f),u=await t.sol.getNetworkId(),v=await t.sol.getSelectedAccount(),b=new URL(e.params.redirect_link),S=n.sol.serializeSolanaSignInMessage(o,{expectedChainId:u,expectedAddress:v,expectedURL:b});if(S.type==="fail"){if(S.error[0].label==="PARSE_ERROR")return s.invalidInput(e.id,a.t("The app's signature request cannot be shown due to invalid formatting."));if(S.error.some(P=>P.label==="chain"))return s.invalidInput(e.id,a.t("The app's signature request cannot be shown as the chain ID does not match the provided chain ID for verification."));if(S.error.some(P=>P.label==="address"))return s.invalidInput(e.id,a.t("The app's signature request cannot be shown as the address does not match the provided address for verification."))}if(o.domain||(o.domain=b.host),o.address||(o.address=v),"error"in await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignIn",params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)},{connect:!m,signInData:o,message:S.data,errorDetails:S.type==="fail"?S.error:void 0}],id:Date.now()}))return S.type==="fail"?s.invalidInput(e.id,a.t("This sign-in request is invalid. This either means the site is unsafe, or its developer made an error when sending the request.")):s.userRejectedRequest(e.id);m||await t.isTrustedApp(f)||await t.addTrustedApp(f,{url:new URL(f),icon:new URL(n.getFaviconURL(f))});let{sharedSecret:g,phantomKeySecret:A,phantomKeyPublicKey:y}=r.generateSharedSecret(e.params.dapp_encryption_public_key);await r.setSharedSecret(new le.PublicKey(v).toBase58(),e.params.dapp_encryption_public_key,g,A,y);let k=await r.generateSession(new le.PublicKey(v).toBase58(),e.params?.cluster??"mainnet-beta",e.params.app_url),I=await t.sol.signMessage(S.data),B={address:k.public_key,signature:I,signedMessage:S.data,session:k.session},{nonce:E,encryptedPayload:D}=await r.encryptDeepLinkDappPayload(B,new le.PublicKey(v).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{phantom_encryption_public_key:y,nonce:E,data:D})};p();d();var sc=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=await t.sol.getSelectedAccount(),o=n.sol.chainIdToCluster(await t.sol.getNetworkId()),l=await r.decryptDeepLinkDappPayload(e.params,e.params.dapp_encryption_public_key,new le.PublicKey(a),o);if(typeof l.session!="string"||typeof l.transaction!="string")return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});let f=await t.sol.addPriorityFee(l.transaction),m=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignTransaction",id:Date.now(),params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)},{transaction:f,autoConfirmStatusCode:"DISABLED"}]});if("error"in m)return s.error(e.id,m.error);let{transaction:u}=await t.sol.signTransaction(f),v={transaction:u},{nonce:b,encryptedPayload:S}=await r.encryptDeepLinkDappPayload(v,new le.PublicKey(a).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{nonce:b,data:S})};p();d();var oc=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=await t.sol.getSelectedAccount(),o=n.sol.chainIdToCluster(await t.sol.getNetworkId()),l=await r.decryptDeepLinkDappPayload(e.params,e.params.dapp_encryption_public_key,new le.PublicKey(a),o);if(typeof l.session!="string"||!Array.isArray(l.transactions)||!l.transactions.every(_=>typeof _=="string"))return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});let f=await Promise.all(l.transactions.map(_=>t.sol.addPriorityFee(_))),m=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAllTransactions",id:Date.now(),params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)},{transactions:f,autoConfirmStatusCode:"DISABLED"}]});if("error"in m)return s.error(e.id,m.error);let v={transactions:(await t.sol.signAllTransactions(f)).map(_=>_.transaction)},{nonce:b,encryptedPayload:S}=await r.encryptDeepLinkDappPayload(v,new le.PublicKey(a).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{nonce:b,data:S})};p();d();var ic=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=await t.sol.getSelectedAccount(),o=n.sol.chainIdToCluster(await t.sol.getNetworkId()),l=e.params,f=await r.decryptDeepLinkDappPayload(l,e.params.dapp_encryption_public_key,new le.PublicKey(a),o);if(typeof f.session!="string"||typeof f.transaction!="string")return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});let m=await t.sol.addPriorityFee(f.transaction),u=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAndSendTransaction",id:Date.now(),params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)},{transaction:m,autoConfirmStatusCode:"DISABLED"}]});if("error"in u)return s.error(e.id,u.error);let v=await t.sol.signTransaction(m);await t.sol.sendTransaction(v.transaction,f.sendOptions);let b={signature:v.signature},{nonce:S,encryptedPayload:_}=await r.encryptDeepLinkDappPayload(b,new le.PublicKey(a).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{nonce:S,data:_})};p();d();var ac=async({request:e},{wallet:t,secrets:r,utils:n})=>{let a=await t.sol.getSelectedAccount(),o=n.sol.chainIdToCluster(await t.sol.getNetworkId()),l=await r.decryptDeepLinkDappPayload(e.params,e.params.dapp_encryption_public_key,new le.PublicKey(a),o);if(typeof l.session!="string"||!Array.isArray(l.transactions)||!l.transactions.every(g=>typeof g=="string"))return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});let f=await Promise.all(l.transactions.map(g=>t.sol.addPriorityFee(g))),m=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAndSendAllTransactions",id:Date.now(),params:[{url:`phantom.app.deep-link-${e.params.redirect_link}`,icon:n.getFaviconURL(e.params.redirect_link)},{transactions:f,autoConfirmStatusCode:"DISABLED"}]});if("error"in m)return s.error(e.id,m.error);let u=await t.sol.signAllTransactions(f),b={signatures:await t.sol.sendAllTransactions(u.map(g=>g.transaction),l.sendOptions)},{nonce:S,encryptedPayload:_}=await r.encryptDeepLinkDappPayload(b,new le.PublicKey(a).toBase58(),e.params.dapp_encryption_public_key);return s.result(e.id,{nonce:S,data:_})};p();d();var ol=[Ee.solana,Ee.ethereum,Ee.polygon,Ee.bitcoin],il=(e,t)=>{let r=ol.find(a=>a.id===e);if(!r)throw new Error(`Chain ${e} not supported`);let n=r.mainnetID;return typeof t!="string"?Br({chainId:n,resourceType:"nativeToken",slip44:r.slip44}):Br({chainId:n,address:t,resourceType:"address"})},cc=async({request:e},{wallet:t,analytics:r,featureFlags:n})=>{let{chain:a,address:o}=e.params,l=await n.isEnabled("enable-unified-token-pages"),f=il(a,o);return r.tokenDeepLink({caip19:f,...e.params}),t.navigate("FungibleStack",{screen:l?"UnifiedFungibleDetail":"PublicFungibleDetail",params:{caip19:f,title:void 0,entryPoint:"deepLink"}}),s.result(e.id,null)};p();d();var pc=async({request:e},{wallet:t})=>{if(!fo())return s.methodNotFound(e.id);let r=null,n=null,{value:a,accounts:o}=e.params;a&&(r=new Uint8Array(Buffer.from(a,"utf-8")),n=Oe.from(r),r.fill(0));let l=o?parseInt(o):1;if(l<1)throw new Error("accounts must be greater than 0");return await t.onboardQuickly(n,l),s.result(e.id,null)};var zt={};Ie(zt,{sol_blink_connect:()=>dc,sol_blink_signAndSendAllTransactions:()=>lc});p();d();p();d();var dc=async({request:e,source:t},{wallet:r,utils:n})=>{let a=e.params.context,{origin:o,hostname:l}=n.getOriginAndHostname(a.blinkUrl);if(!t||!o||!l)return s.unauthorized(e.id);await r.addTrustedApp(o,{title:a.blinkTitle??"",icon:new URL(n.getFaviconURL(l)),url:new URL(o)});let f=await r.sol.getSelectedAccount();return s.result(e.id,{publicKey:f})};p();d();var lc=async({request:e},{wallet:t})=>{let r=await t.sol.getSelectedAccount(),n=await Promise.all(e.params.transactions.map(o=>t.sol.signTransaction(o))),a=await t.sol.sendAllTransactions(n.map(o=>o.transaction));return s.result(e.id,{publicKey:r,signatures:a})};var ht={};Ie(ht,{sol_mwa_authorize:()=>hc,sol_mwa_reauthorize:()=>uc,sol_mwa_sign_and_send_transactions:()=>fc,sol_mwa_sign_messages:()=>yc,sol_mwa_sign_transactions:()=>gc});p();d();p();d();var uc=async({request:e},t)=>s.result(e.id,{type:"REAUTHORIZE_SUCCESS"});p();d();var mc=0,hc=async({request:e},{wallet:t,logger:r,utils:n})=>{let{cluster:a="mainnet-beta",identity:o}=e.params,{identityUri:l,iconRelativeUri:f}=o;if(!l)return r.error("identityUri is not defined in sol_mwa_authorize rpc request payload"),s.result(e.id,{type:"AUTHORIZE_DECLINE"});let m=a==="mainnet-beta"?"mainnet":"testnet",u=await t.sol.getNetworkId()===ao.value?"mainnet":"testnet";if(m!==u)return await t.requestUserApproval({jsonrpc:"2.0",method:"user_confirmIncorrectMode",id:mc++,params:[{url:`phantom.app.mwa-${l}`,icon:f?`${l}/${f}`:n.getFaviconURL(l)},u]}),s.result(e.id,{type:"AUTHORIZE_DECLINE"});if("error"in await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolConnect",id:mc++,params:[{url:l,icon:f?`${l}/${f}`:n.getFaviconURL(l)}]}))return s.result(e.id,{type:"AUTHORIZE_DECLINE"});let b=await t.sol.getSelectedAccount();return s.result(e.id,{type:"AUTHORIZE_SUCCESS",publicKey:b,accountLabel:"phantom-wallet"})};p();d();var Cs=$(Ct()),fc=async({request:e},{wallet:t,logger:r,utils:n})=>{let{publicKey:a,payloads:o,verifiableIdentity:l}=e.params.signPayloads,{identityUri:f,iconRelativeUri:m}=l.identity,u=await t.sol.getSelectedAccount();if(a!==u)return s.result(e.id,{type:"SIGN_AND_SEND_TRANSACTIONS_ERROR_AUTHORIZATION_NOT_VALID"});if(!f)return r.error("identityUri is not defined in sol_mwa_sign_and_send_transactions rpc request payload"),s.result(e.id,{type:"SIGN_AND_SEND_TRANSACTIONS_DECLINE"});let v=o.map(_=>Cs.default.encode(Buffer.from(_,"base64"))),b=await Promise.all(v.map(_=>t.sol.addPriorityFee(_)));if("error"in await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAndSendAllTransactions",id:Date.now(),params:[{url:`phantom.app.mwa-${f}`,icon:m?`${f}/${m}`:n.getFaviconURL(f)},{transactions:b,autoConfirmStatusCode:"DISABLED"}]}))return s.result(e.id,{type:"SIGN_AND_SEND_TRANSACTIONS_DECLINE"});try{let _=await t.sol.signAllTransactions(b);await t.sol.sendAllTransactions(_.map(A=>A.transaction));let g=_.map(A=>Buffer.from(Cs.default.decode(A.signature)).toString("base64"));return s.result(e.id,{type:"SIGN_AND_SEND_TRANSACTIONS_SUCCESS",signedPayloads:g})}catch{return s.error(e.id,{code:-32603,message:"Internal error"})}};p();d();var Bs=$(Ct()),gc=async({request:e},{wallet:t,logger:r,utils:n})=>{let{publicKey:a,payloads:o,verifiableIdentity:l}=e.params.signPayloads,{identityUri:f,iconRelativeUri:m}=l.identity,u=await t.sol.getSelectedAccount();if(a!==u)return s.result(e.id,{type:"SIGN_PAYLOADS_ERROR_AUTHORIZATION_NOT_VALID"});if(!f)return r.error("identityUri is not defined in sol_mwa_sign_transactions rpc request payload"),s.result(e.id,{type:"SIGN_PAYLOADS_DECLINE"});let v=o.map(_=>Bs.default.encode(Buffer.from(_,"base64"))),b=await Promise.all(v.map(_=>t.sol.addPriorityFee(_)));if("error"in await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignAllTransactions",id:Date.now(),params:[{url:`phantom.app.mwa-${f}`,icon:m?`${f}/${m}`:n.getFaviconURL(f)},{transactions:b,autoConfirmStatusCode:"DISABLED"}]}))return s.result(e.id,{type:"SIGN_PAYLOADS_DECLINE"});try{let g=(await t.sol.signAllTransactions(b)).map(A=>Buffer.from(Bs.default.decode(A.transaction)).toString("base64"));return s.result(e.id,{type:"SIGN_PAYLOADS_SUCCESS",signedPayloads:g})}catch{return s.error(e.id,{code:-32603,message:"Internal error"})}};p();d();var Ls=$(Ct()),yc=async({request:e},{wallet:t,logger:r,utils:n})=>{let{publicKey:a,payloads:o,verifiableIdentity:l}=e.params.signPayloads,{identityUri:f,iconRelativeUri:m}=l.identity,u=await t.sol.getSelectedAccount();if(a!==u)return s.result(e.id,{type:"SIGN_PAYLOADS_ERROR_AUTHORIZATION_NOT_VALID"});if(!f)return r.error("identityUri is not defined in sol_mwa_sign_messages rpc request payload"),s.result(e.id,{type:"SIGN_PAYLOADS_DECLINE"});let v=Ls.default.encode(Buffer.from(o[0],"base64")),b={message:v,display:"utf8"};if("error"in await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolSignMessage",id:Date.now(),params:[{url:`phantom.app.mwa-${f}`,icon:m?`${f}/${m}`:n.getFaviconURL(f)},{...b,autoConfirmStatusCode:"DISABLED"}]}))return s.result(e.id,{type:"SIGN_PAYLOADS_DECLINE"});try{let _=await t.sol.signMessage(v),g=Buffer.from(Ls.default.decode(_)).toString("base64");return s.result(e.id,{type:"SIGN_PAYLOADS_SUCCESS",signedPayloads:[g]})}catch{return s.error(e.id,{code:-32603,message:"Internal error"})}};var Kt={};Ie(Kt,{sol_pay_transaction:()=>wc,sol_pay_transfer:()=>xc});p();d();p();d();var xc=async({request:e},{wallet:t})=>await t.sol.handleSolanaPayTransfer(e.params)?s.result(e.id,null):s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});p();d();var wc=async({request:e},{wallet:t})=>{let r=await t.sol.handleSolanaPayTransaction(e.params);if(!r)return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});let{icon:n,label:a,transaction:o}=r,l=await t.requestUserApproval({jsonrpc:"2.0",method:"user_approveSolPayTransaction",id:Date.now(),params:[{url:e.params.link,icon:n},{label:a,transaction:o}]});if("error"in l)return s.error(e.id,l.error);if(!o)return s.error(e.id,{code:-32e3,message:"Missing or invalid parameters."});switch(l.result.type){case"send":{await t.sol.sendTransaction(o);break}case"signAndSend":{let f=await t.sol.signTransaction(o);await t.sol.sendTransaction(f.transaction);break}}return s.result(e.id,null)};var bt={};Ie(bt,{btc:()=>Ns,eth:()=>Ms,phantom_deep_link:()=>Ds,sol:()=>Ps,sol_blink:()=>Us,sol_mwa:()=>Os,sol_pay:()=>qs});p();d();var Ms={};Ie(Ms,{initialize:()=>al});p();d();var al=e=>({eth_accounts:j().parse(ne.eth_accounts).context(e).use([qe(10),F,se,Q],En),eth_blockNumber:j().parse(ne.eth_blockNumber).context(e).use([F,se,Q],In),eth_call:j().parse(ne.eth_call).context(e).use([F,se,Q],Cn),eth_cancelPrivateTransaction:j().parse(ne.eth_cancelPrivateTransaction).context(e).use([F,se,Q],Bn),eth_chainId:j().parse(ne.eth_chainId).context(e).use([F,se,Q],Ln),eth_estimateGas:j().parse(ne.eth_estimateGas).context(e).use([F,se,Q],Mn),eth_feeHistory:j().parse(ne.eth_feeHistory).context(e).use([F,se,Q],Pn),eth_gasPrice:j().parse(ne.eth_gasPrice).context(e).use([F,se,Q],Nn),eth_getBalance:j().parse(ne.eth_getBalance).context(e).use([F,se,Q],Dn),eth_getBlockByHash:j().parse(ne.eth_getBlockByHash).context(e).use([F,se,Q],Un),eth_getBlockByNumber:j().parse(ne.eth_getBlockByNumber).context(e).use([F,se,Q],On),eth_getBlockReceipts:j().parse(ne.eth_getBlockReceipts).context(e).use([F,se,Q],qn),eth_getBlockTransactionCountByHash:j().parse(ne.eth_getBlockTransactionCountByHash).context(e).use([F,se,Q],Hn),eth_getBlockTransactionCountByNumber:j().parse(ne.eth_getBlockTransactionCountByNumber).context(e).use([F,se,Q],Fn),eth_getCode:j().parse(ne.eth_getCode).context(e).use([F,se,Q],Wn),eth_getFilterChanges:j().parse(ne.eth_getFilterChanges).context(e).use([F,se,Q],jn),eth_getFilterLogs:j().parse(ne.eth_getFilterLogs).context(e).use([F,se,Q],Vn),eth_getLogs:j().parse(ne.eth_getLogs).context(e).use([F,se,Q],zn),eth_getProof:j().parse(ne.eth_getProof).context(e).use([F,se,Q],Kn),eth_getStorageAt:j().parse(ne.eth_getStorageAt).context(e).use([F,se,Q],$n),eth_getTransactionByBlockHashAndIndex:j().parse(ne.eth_getTransactionByBlockHashAndIndex).context(e).use([F,se,Q],Gn),eth_getTransactionByBlockNumberAndIndex:j().parse(ne.eth_getTransactionByBlockNumberAndIndex).context(e).use([F,se,Q],Yn),eth_getTransactionByHash:j().parse(ne.eth_getTransactionByHash).context(e).use([F,se,Q],Xn),eth_getTransactionCount:j().parse(ne.eth_getTransactionCount).context(e).use([F,se,Q],Jn),eth_getTransactionReceipt:j().parse(ne.eth_getTransactionReceipt).context(e).use([F,se,Q],Qn),eth_getUncleByBlockHashAndIndex:j().parse(ne.eth_getUncleByBlockHashAndIndex).context(e).use([F,se,Q],Zn),eth_getUncleByBlockNumberAndIndex:j().parse(ne.eth_getUncleByBlockNumberAndIndex).context(e).use([F,se,Q],es),eth_getUncleCountByBlockHash:j().parse(ne.eth_getUncleCountByBlockHash).context(e).use([F,se,Q],ts),eth_getUncleCountByBlockNumber:j().parse(ne.eth_getUncleCountByBlockNumber).context(e).use([F,se,Q],rs),eth_maxPriorityFeePerGas:j().parse(ne.eth_maxPriorityFeePerGas).context(e).use([F,se,Q],ns),eth_newBlockFilter:j().parse(ne.eth_newBlockFilter).context(e).use([F,se,Q],ss),eth_newFilter:j().parse(ne.eth_newFilter).context(e).use([F,se,Q],os),eth_newPendingTransactionFilter:j().parse(ne.eth_newPendingTransactionFilter).context(e).use([F,se,Q],is),eth_protocolVersion:j().parse(ne.eth_protocolVersion).context(e).use([F,se,Q],as),eth_requestAccounts:j().parse(ne.eth_requestAccounts).context(e).use([qe(10),F,se,Q,sn,dn],cs),eth_sendRawTransaction:j().parse(ne.eth_sendRawTransaction).context(e).use([F,se,Q],ps),eth_sendPrivateTransaction:j().parse(ne.eth_sendPrivateTransaction).context(e).use([F,se,Q],ds),eth_sendTransaction:j().parse(ne.eth_sendTransaction).context(e).use([F,Ce,se,Q,Ne,nn,ut,un],ls),eth_sign:j().parse(ne.eth_sign).context(e).use([F,Ce,se,Q,Ne,mn],us),eth_signTypedData:j().parse(ne.eth_signTypedData).context(e).use([F,Ce,se,Q,Ne,ut,hn],ms),eth_signTypedData_v3:j().parse(ne.eth_signTypedData_v3).context(e).use([F,Ce,se,Q,Ne,ut,fn],hs),eth_signTypedData_v4:j().parse(ne.eth_signTypedData_v4).context(e).use([F,Ce,se,Q,Ne,ut,gn],fs),eth_subscribe:j().parse(ne.eth_subscribe).context(e).use([F,se],gs),eth_syncing:j().parse(ne.eth_syncing).context(e).use([F,se,Q],ys),eth_uninstallFilter:j().parse(ne.eth_uninstallFilter).context(e).use([F,se,Q],xs),eth_unsubscribe:j().parse(ne.eth_unsubscribe).context(e).use([F,se],ws),net_listening:j().parse(ne.net_listening).context(e).use([F,se,Q],bs),net_version:j().parse(ne.net_version).context(e).use([F,se,Q],As),personal_sign:j().parse(ne.personal_sign).context(e).use([F,Ce,se,Q,Ne,yn],Ss),wallet_addEthereumChain:j().parse(ne.wallet_addEthereumChain).context(e).use([F,se,Q,ut],Rs),wallet_selectEthereumProvider:j().parse(ne.wallet_selectEthereumProvider).context(e).use([F,se,Q],vs),wallet_switchEthereumChain:j().parse(ne.wallet_switchEthereumChain).context(e).use([F,se,Q,Ne,ut],ks),wallet_requestPermissions:j().parse(ne.wallet_requestPermissions).context(e).use([F,se,Q,ln],Es),wallet_getPermissions:j().parse(ne.wallet_getPermissions).context(e).use([F,se,Q],Is),web3_clientVersion:j().parse(ne.web3_clientVersion).context(e).use([F,se,Q],Ts),web3_sha3:j().parse(ne.web3_sha3).context(e).use([F,se,Q],_s)});var Ps={};Ie(Ps,{initialize:()=>cl});p();d();var cl=e=>({sol_connect:j().parse(Ye.sol_connect).context(e).use([qe(10),F,yr,We,Q,xn],Ge.sol_connect),sol_disconnect:j().parse(Ye.sol_disconnect).context(e).use([F,We,Q],Ge.sol_disconnect),sol_signAllTransactions:j().parse(Ye.sol_signAllTransactions).context(e).use([F,Ce,We,Q,Ne,lt,bn],Ge.sol_signAllTransactions),sol_signAndSendTransaction:j().parse(Ye.sol_signAndSendTransaction).context(e).use([F,Ce,We,Q,Ne,lt,Rn],Ge.sol_signAndSendTransaction),sol_signAndSendAllTransactions:j().parse(Ye.sol_signAndSendAllTransactions).context(e).use([F,Ce,We,Q,Ne,lt,An],Ge.sol_signAndSendAllTransactions),sol_signMessage:j().parse(Ye.sol_signMessage).context(e).use([F,Ce,We,Q,Ne,on,vn],Ge.sol_signMessage),sol_signTransaction:j().parse(Ye.sol_signTransaction).context(e).use([F,Ce,We,Q,Ne,lt,kn],Ge.sol_signTransaction),sol_signIn:j().parse(Ye.sol_signIn).context(e).use([F,yr,We,Q],Ge.sol_signIn)});var Ns={};Ie(Ns,{initialize:()=>pl});p();d();var pl=e=>({btc_requestAccounts:j().parse(Zt.btc_requestAccounts).context(e).use([qe(10),F,Vt,an],Lt.btc_requestAccounts),btc_signPSBT:j().parse(Zt.btc_signPSBT).context(e).use([F,Vt,pn],Lt.btc_signPSBT),btc_signMessage:j().parse(Zt.btc_signMessage).context(e).use([F,Vt,cn],Lt.btc_signMessage)});var Ds={};Ie(Ds,{initialize:()=>dl});p();d();var dl=e=>({phantom_deep_link_browse:j().parse(Ue.phantom_deep_link_browse).context(e).use([qe(10),F,ze],Le.phantom_deep_link_browse),phantom_deep_link_tokens:j().parse(Ue.phantom_deep_link_tokens).context(e).use([F,ze],Le.phantom_deep_link_tokens),phantom_deep_link_swap:j().parse(Ue.phantom_deep_link_swap).context(e).use([F],Le.phantom_deep_link_swap),phantom_deep_link_fungible:j().parse(Ue.phantom_deep_link_fungible).context(e).use([F],Le.phantom_deep_link_fungible),phantom_deep_link_onboard:j().parse(Ue.phantom_deep_link_onboard).context(e).use([F],Le.phantom_deep_link_onboard),phantom_deep_link_connect:j().parse(Ue.phantom_deep_link_connect).context(e).use([F,ze],Le.phantom_deep_link_connect),phantom_deep_link_disconnect:j().parse(Ue.phantom_deep_link_disconnect).context(e).use([F,ze],Le.phantom_deep_link_disconnect),phantom_deep_link_signMessage:j().parse(Ue.phantom_deep_link_signMessage).context(e).use([F,ze],Le.phantom_deep_link_signMessage),phantom_deep_link_signIn:j().parse(Ue.phantom_deep_link_signIn).context(e).use([F,ze],Le.phantom_deep_link_signIn),phantom_deep_link_signTransaction:j().parse(Ue.phantom_deep_link_signTransaction).context(e).use([F,ze],Le.phantom_deep_link_signTransaction),phantom_deep_link_signAllTransactions:j().parse(Ue.phantom_deep_link_signAllTransactions).context(e).use([F,ze],Le.phantom_deep_link_signAllTransactions),phantom_deep_link_signAndSendTransaction:j().parse(Ue.phantom_deep_link_signAndSendTransaction).context(e).use([F,ze],Le.phantom_deep_link_signAndSendTransaction),phantom_deep_link_signAndSendAllTransactions:j().parse(Ue.phantom_deep_link_signAndSendAllTransactions).context(e).use([F,ze],Le.phantom_deep_link_signAndSendAllTransactions)});var Us={};Ie(Us,{initialize:()=>ll});p();d();var ll=e=>({sol_blink_connect:j().parse(gt.sol_blink_connect).context(e).use([qe(10),hr,F,We,Q,fr,wn],zt.sol_blink_connect),sol_blink_signAndSendAllTransactions:j().parse(gt.sol_blink_signAndSendAllTransactions).context(e).use([hr,F,Ce,We,Q,fr,_n,lt,Sn],zt.sol_blink_signAndSendAllTransactions)});var Os={};Ie(Os,{initialize:()=>ul});p();d();var ul=e=>({sol_mwa_authorize:j().parse(At.sol_mwa_authorize).context(e).use([qe(10),F,Bt],ht.sol_mwa_authorize),sol_mwa_reauthorize:j().parse(At.sol_mwa_reauthorize).context(e).use([F],ht.sol_mwa_reauthorize),sol_mwa_sign_messages:j().parse(At.sol_mwa_sign_messages).context(e).use([F,Bt],ht.sol_mwa_sign_messages),sol_mwa_sign_transactions:j().parse(At.sol_mwa_sign_transactions).context(e).use([F,Bt],ht.sol_mwa_sign_transactions),sol_mwa_sign_and_send_transactions:j().parse(At.sol_mwa_sign_and_send_transactions).context(e).use([F,Bt],ht.sol_mwa_sign_and_send_transactions)});var qs={};Ie(qs,{initialize:()=>ml});p();d();var ml=e=>({sol_pay_transfer:j().parse(Cr.sol_pay_transfer).context(e).use([F],Kt.sol_pay_transfer),sol_pay_transaction:j().parse(Cr.sol_pay_transaction).context(e).use([F],Kt.sol_pay_transaction)});p();d();p();d();var bc={isEnabled:async e=>(await Ke.getFeatureFlags())[e]===!0};p();d();var nt=e=>t=>{be.capture(e,{data:t})},Ac={deepLinkDetected:nt("deepLinkDetected"),tokenDeepLink:nt("deepLinkTokens"),metamaskOverrideSelection:nt("notificationMetamaskOverrideSelection"),approveConnection:nt("connectionConfirm"),declineConnection:nt("connectionDeny"),approveSignMessage:nt("signMessageConfirm"),declineSignMessage:nt("signMessageDeny"),approveTransaction:nt("transactionConfirm"),declineTransaction:nt("transactionDeny"),eth:{submittedTransaction:async({origin:e,request:t,signatures:r})=>{try{let n=await Ot(t,{origin:e},r);if(!n.success)throw new Error(`Failed to capture signature event analytics: ${n.error}`);await Pr(be,{data:n.data,action:"dapp"})}catch(n){Y.captureError(n,"provider")}},signedMessage:async({origin:e,request:t,chainId:r})=>{try{let n=await qt(t,{origin:e,chainId:Er(r)});if(!n.success)throw new Error(`Failed to capture signature event analytics: ${n.error}`);await Nr(be,n.data)}catch(n){Y.captureError(n,"provider")}},autoConfirmedTransaction:async({origin:e,request:t,data:r})=>{try{let n=new Ft(be),a=await Ot(t,{origin:e});if(!a.success)throw new Error(`Failed to capture signature event analytics: ${a.error}`);await n.transactionAutoConfirmed({...a.data,autoConfirm:r})}catch(n){Y.captureError(n,"provider")}},autoConfirmedMessage:async({origin:e,request:t,chainId:r,data:n})=>{try{let a=new Ft(be),o=await qt(t,{origin:e,chainId:Er(r)});if(!o.success)throw new Error(`Failed to capture signature event analytics: ${o.error}`);await a.messageAutoConfirmed({...o.data,autoConfirm:n})}catch(a){Y.captureError(a,"provider")}},unsupportedChain:async({origin:e,data:t})=>{try{await be.capture("unsupportedChainId",{data:{origin:e,chainType:"eip155",chainId:vt(t.hexChainId),chainName:t.chainName,method:t.method}})}catch(r){Y.captureError(r,"provider")}}},sol:{submittedTransaction:async({origin:e,request:t,chainId:r,signatures:n})=>{try{let a=await Ot(t,{origin:e,chainId:r},n);if(!a.success)throw new Error(`Failed to capture signature event analytics: ${a.error}`);await Pr(be,{data:a.data,action:"dapp"})}catch(a){Y.captureError(a,"provider")}},signedMessage:async({origin:e,request:t,chainId:r})=>{try{let n=await qt(t,{origin:e,chainId:r});if(!n.success)throw new Error(`Failed to capture signature event analytics: ${n.error}`);await Nr(be,n.data)}catch(n){Y.captureError(n,"provider")}},autoConfirmedTransaction:async({origin:e,request:t,data:r,chainId:n})=>{try{let a=new Ft(be),o=await Ot(t,{origin:e,chainId:n});if(!o.success)throw new Error(`Failed to capture signature event analytics: ${o.error}`);await a.transactionAutoConfirmed({...o.data,autoConfirm:r})}catch(a){Y.captureError(a,"provider")}},autoConfirmedMessage:async({origin:e,request:t,chainId:r,data:n})=>{try{let a=new Ft(be),o=await qt(t,{origin:e,chainId:r});if(!o.success)throw new Error(`Failed to capture signature event analytics: ${o.error}`);await a.messageAutoConfirmed({...o.data,autoConfirm:n})}catch(a){Y.captureError(a,"provider")}}}};p();d();var Sc={env:ho()?"production":"development",logRequestMethods:new Set(["eth_requestAccounts","eth_accounts","eth_sendTransaction","eth_sign","personal_sign"]),logRequestsOnError:!0};p();d();var Rc={t(e){return e}};p();d();var vc={debug(...e){console.debug(...e)},warn(...e){console.warn(...e)},error(...e){Y.addBreadcrumb("provider",e.join(", "),"error")}};p();d();var kc={eth:Go};p();d();p();d();p();d();p();d();p();d();function wr(e,t){let{expectedAddress:r,expectedURL:n,expectedChainId:a,issuedAtThreshold:o}=t,l=[],f=Date.now();if(e.address!==r&&l.push("ADDRESS_MISMATCH"),e.domain!==n.host&&l.push("DOMAIN_MISMATCH"),e.uri&&e.uri.origin!==n.origin&&l.push("URI_MISMATCH"),e.chainId&&(e.type!=="sign-in-with-solana"&&e.chainId!==a||e.type==="sign-in-with-solana"&&e.chainId.replace(/solana:/g,"")!==a)&&l.push("CHAIN_ID_MISMATCH"),e.issuedAt){let m=e.issuedAt.getTime();Math.abs(m-f)>o&&(m<f?l.push("ISSUED_TOO_FAR_IN_THE_PAST"):l.push("ISSUED_TOO_FAR_IN_THE_FUTURE"))}if(e.expirationTime){let m=e.expirationTime.getTime();m<=f&&l.push("EXPIRED"),e.issuedAt&&m<e.issuedAt.getTime()&&l.push("EXPIRES_BEFORE_ISSUANCE"),e.notBefore&&e.notBefore.getTime()>m&&l.push("VALID_AFTER_EXPIRATION")}return l}p();d();p();d();(function e(t,r,n){function a(f,m){if(!r[f]){if(!t[f]){var u=typeof Dt=="function"&&Dt;if(!m&&u)return u(f,!0);if(o)return o(f,!0);var v=new Error("Cannot find module '"+f+"'");throw v.code="MODULE_NOT_FOUND",v}var b=r[f]={exports:{}};t[f][0].call(b.exports,function(S){return a(t[f][1][S]||S)},b,b.exports,e,t,r,n)}return r[f].exports}for(var o=typeof Dt=="function"&&Dt,l=0;l<n.length;l++)a(n[l]);return a})({1:[function(e,t,r){"use strict";r.byteLength=function(b){var S=u(b),_=S[0],g=S[1];return 3*(_+g)/4-g},r.toByteArray=function(b){var S,_,g=u(b),A=g[0],y=g[1],k=new o(function(E,D,P){return 3*(D+P)/4-P}(0,A,y)),I=0,B=y>0?A-4:A;for(_=0;_<B;_+=4)S=a[b.charCodeAt(_)]<<18|a[b.charCodeAt(_+1)]<<12|a[b.charCodeAt(_+2)]<<6|a[b.charCodeAt(_+3)],k[I++]=S>>16&255,k[I++]=S>>8&255,k[I++]=255&S;return y===2&&(S=a[b.charCodeAt(_)]<<2|a[b.charCodeAt(_+1)]>>4,k[I++]=255&S),y===1&&(S=a[b.charCodeAt(_)]<<10|a[b.charCodeAt(_+1)]<<4|a[b.charCodeAt(_+2)]>>2,k[I++]=S>>8&255,k[I++]=255&S),k},r.fromByteArray=function(b){for(var S,_=b.length,g=_%3,A=[],y=16383,k=0,I=_-g;k<I;k+=y)A.push(v(b,k,k+y>I?I:k+y));return g===1?(S=b[_-1],A.push(n[S>>2]+n[S<<4&63]+"==")):g===2&&(S=(b[_-2]<<8)+b[_-1],A.push(n[S>>10]+n[S>>4&63]+n[S<<2&63]+"=")),A.join("")};for(var n=[],a=[],o=typeof Uint8Array<"u"?Uint8Array:Array,l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",f=0,m=l.length;f<m;++f)n[f]=l[f],a[l.charCodeAt(f)]=f;function u(b){var S=b.length;if(S%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var _=b.indexOf("=");return _===-1&&(_=S),[_,_===S?0:4-_%4]}function v(b,S,_){for(var g,A,y=[],k=S;k<_;k+=3)g=(b[k]<<16&16711680)+(b[k+1]<<8&65280)+(255&b[k+2]),y.push(n[(A=g)>>18&63]+n[A>>12&63]+n[A>>6&63]+n[63&A]);return y.join("")}a[45]=62,a[95]=63},{}],2:[function(e,t,r){(function(n){(function(){"use strict";var a=e("base64-js"),o=e("ieee754");r.Buffer=m,r.SlowBuffer=function(i){return+i!=i&&(i=0),m.alloc(+i)},r.INSPECT_MAX_BYTES=50;var l=2147483647;function f(i){if(i>l)throw new RangeError('The value "'+i+'" is invalid for option "size"');var c=new Uint8Array(i);return c.__proto__=m.prototype,c}function m(i,c,h){if(typeof i=="number"){if(typeof c=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return b(i)}return u(i,c,h)}function u(i,c,h){if(typeof i=="string")return function(R,x){if(typeof x=="string"&&x!==""||(x="utf8"),!m.isEncoding(x))throw new TypeError("Unknown encoding: "+x);var L=0|g(R,x),O=f(L),N=O.write(R,x);return N!==L&&(O=O.slice(0,N)),O}(i,c);if(ArrayBuffer.isView(i))return S(i);if(i==null)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof i);if(ae(i,ArrayBuffer)||i&&ae(i.buffer,ArrayBuffer))return function(R,x,L){if(x<0||R.byteLength<x)throw new RangeError('"offset" is outside of buffer bounds');if(R.byteLength<x+(L||0))throw new RangeError('"length" is outside of buffer bounds');var O;return O=x===void 0&&L===void 0?new Uint8Array(R):L===void 0?new Uint8Array(R,x):new Uint8Array(R,x,L),O.__proto__=m.prototype,O}(i,c,h);if(typeof i=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var T=i.valueOf&&i.valueOf();if(T!=null&&T!==i)return m.from(T,c,h);var C=function(R){if(m.isBuffer(R)){var x=0|_(R.length),L=f(x);return L.length===0||R.copy(L,0,0,x),L}if(R.length!==void 0)return typeof R.length!="number"||M(R.length)?f(0):S(R);if(R.type==="Buffer"&&Array.isArray(R.data))return S(R.data)}(i);if(C)return C;if(typeof Symbol<"u"&&Symbol.toPrimitive!=null&&typeof i[Symbol.toPrimitive]=="function")return m.from(i[Symbol.toPrimitive]("string"),c,h);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof i)}function v(i){if(typeof i!="number")throw new TypeError('"size" argument must be of type number');if(i<0)throw new RangeError('The value "'+i+'" is invalid for option "size"')}function b(i){return v(i),f(i<0?0:0|_(i))}function S(i){for(var c=i.length<0?0:0|_(i.length),h=f(c),T=0;T<c;T+=1)h[T]=255&i[T];return h}function _(i){if(i>=l)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+l.toString(16)+" bytes");return 0|i}function g(i,c){if(m.isBuffer(i))return i.length;if(ArrayBuffer.isView(i)||ae(i,ArrayBuffer))return i.byteLength;if(typeof i!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof i);var h=i.length,T=arguments.length>2&&arguments[2]===!0;if(!T&&h===0)return 0;for(var C=!1;;)switch(c){case"ascii":case"latin1":case"binary":return h;case"utf8":case"utf-8":return pe(i).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*h;case"hex":return h>>>1;case"base64":return ee(i).length;default:if(C)return T?-1:pe(i).length;c=(""+c).toLowerCase(),C=!0}}function A(i,c,h){var T=!1;if((c===void 0||c<0)&&(c=0),c>this.length||((h===void 0||h>this.length)&&(h=this.length),h<=0)||(h>>>=0)<=(c>>>=0))return"";for(i||(i="utf8");;)switch(i){case"hex":return V(this,c,h);case"utf8":case"utf-8":return he(this,c,h);case"ascii":return re(this,c,h);case"latin1":case"binary":return U(this,c,h);case"base64":return ie(this,c,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return q(this,c,h);default:if(T)throw new TypeError("Unknown encoding: "+i);i=(i+"").toLowerCase(),T=!0}}function y(i,c,h){var T=i[c];i[c]=i[h],i[h]=T}function k(i,c,h,T,C){if(i.length===0)return-1;if(typeof h=="string"?(T=h,h=0):h>2147483647?h=2147483647:h<-2147483648&&(h=-2147483648),M(h=+h)&&(h=C?0:i.length-1),h<0&&(h=i.length+h),h>=i.length){if(C)return-1;h=i.length-1}else if(h<0){if(!C)return-1;h=0}if(typeof c=="string"&&(c=m.from(c,T)),m.isBuffer(c))return c.length===0?-1:I(i,c,h,T,C);if(typeof c=="number")return c&=255,typeof Uint8Array.prototype.indexOf=="function"?C?Uint8Array.prototype.indexOf.call(i,c,h):Uint8Array.prototype.lastIndexOf.call(i,c,h):I(i,[c],h,T,C);throw new TypeError("val must be string, number or Buffer")}function I(i,c,h,T,C){var R,x=1,L=i.length,O=c.length;if(T!==void 0&&((T=String(T).toLowerCase())==="ucs2"||T==="ucs-2"||T==="utf16le"||T==="utf-16le")){if(i.length<2||c.length<2)return-1;x=2,L/=2,O/=2,h/=2}function N(Ae,ve){return x===1?Ae[ve]:Ae.readUInt16BE(ve*x)}if(C){var H=-1;for(R=h;R<L;R++)if(N(i,R)===N(c,H===-1?0:R-H)){if(H===-1&&(H=R),R-H+1===O)return H*x}else H!==-1&&(R-=R-H),H=-1}else for(h+O>L&&(h=L-O),R=h;R>=0;R--){for(var me=!0,fe=0;fe<O;fe++)if(N(i,R+fe)!==N(c,fe)){me=!1;break}if(me)return R}return-1}function B(i,c,h,T){h=Number(h)||0;var C=i.length-h;T?(T=Number(T))>C&&(T=C):T=C;var R=c.length;T>R/2&&(T=R/2);for(var x=0;x<T;++x){var L=parseInt(c.substr(2*x,2),16);if(M(L))return x;i[h+x]=L}return x}function E(i,c,h,T){return te(pe(c,i.length-h),i,h,T)}function D(i,c,h,T){return te(function(C){for(var R=[],x=0;x<C.length;++x)R.push(255&C.charCodeAt(x));return R}(c),i,h,T)}function P(i,c,h,T){return D(i,c,h,T)}function ce(i,c,h,T){return te(ee(c),i,h,T)}function Z(i,c,h,T){return te(function(C,R){for(var x,L,O,N=[],H=0;H<C.length&&!((R-=2)<0);++H)L=(x=C.charCodeAt(H))>>8,O=x%256,N.push(O),N.push(L);return N}(c,i.length-h),i,h,T)}function ie(i,c,h){return c===0&&h===i.length?a.fromByteArray(i):a.fromByteArray(i.slice(c,h))}function he(i,c,h){h=Math.min(i.length,h);for(var T=[],C=c;C<h;){var R,x,L,O,N=i[C],H=null,me=N>239?4:N>223?3:N>191?2:1;if(C+me<=h)switch(me){case 1:N<128&&(H=N);break;case 2:(192&(R=i[C+1]))==128&&(O=(31&N)<<6|63&R)>127&&(H=O);break;case 3:R=i[C+1],x=i[C+2],(192&R)==128&&(192&x)==128&&(O=(15&N)<<12|(63&R)<<6|63&x)>2047&&(O<55296||O>57343)&&(H=O);break;case 4:R=i[C+1],x=i[C+2],L=i[C+3],(192&R)==128&&(192&x)==128&&(192&L)==128&&(O=(15&N)<<18|(63&R)<<12|(63&x)<<6|63&L)>65535&&O<1114112&&(H=O)}H===null?(H=65533,me=1):H>65535&&(H-=65536,T.push(H>>>10&1023|55296),H=56320|1023&H),T.push(H),C+=me}return function(fe){var Ae=fe.length;if(Ae<=J)return String.fromCharCode.apply(String,fe);for(var ve="",Te=0;Te<Ae;)ve+=String.fromCharCode.apply(String,fe.slice(Te,Te+=J));return ve}(T)}r.kMaxLength=l,m.TYPED_ARRAY_SUPPORT=function(){try{var i=new Uint8Array(1);return i.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},i.foo()===42}catch{return!1}}(),m.TYPED_ARRAY_SUPPORT||typeof console>"u"||typeof console.error!="function"||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(m.prototype,"parent",{enumerable:!0,get:function(){if(m.isBuffer(this))return this.buffer}}),Object.defineProperty(m.prototype,"offset",{enumerable:!0,get:function(){if(m.isBuffer(this))return this.byteOffset}}),typeof Symbol<"u"&&Symbol.species!=null&&m[Symbol.species]===m&&Object.defineProperty(m,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),m.poolSize=8192,m.from=function(i,c,h){return u(i,c,h)},m.prototype.__proto__=Uint8Array.prototype,m.__proto__=Uint8Array,m.alloc=function(i,c,h){return function(T,C,R){return v(T),T<=0?f(T):C!==void 0?typeof R=="string"?f(T).fill(C,R):f(T).fill(C):f(T)}(i,c,h)},m.allocUnsafe=function(i){return b(i)},m.allocUnsafeSlow=function(i){return b(i)},m.isBuffer=function(i){return i!=null&&i._isBuffer===!0&&i!==m.prototype},m.compare=function(i,c){if(ae(i,Uint8Array)&&(i=m.from(i,i.offset,i.byteLength)),ae(c,Uint8Array)&&(c=m.from(c,c.offset,c.byteLength)),!m.isBuffer(i)||!m.isBuffer(c))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(i===c)return 0;for(var h=i.length,T=c.length,C=0,R=Math.min(h,T);C<R;++C)if(i[C]!==c[C]){h=i[C],T=c[C];break}return h<T?-1:T<h?1:0},m.isEncoding=function(i){switch(String(i).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},m.concat=function(i,c){if(!Array.isArray(i))throw new TypeError('"list" argument must be an Array of Buffers');if(i.length===0)return m.alloc(0);var h;if(c===void 0)for(c=0,h=0;h<i.length;++h)c+=i[h].length;var T=m.allocUnsafe(c),C=0;for(h=0;h<i.length;++h){var R=i[h];if(ae(R,Uint8Array)&&(R=m.from(R)),!m.isBuffer(R))throw new TypeError('"list" argument must be an Array of Buffers');R.copy(T,C),C+=R.length}return T},m.byteLength=g,m.prototype._isBuffer=!0,m.prototype.swap16=function(){var i=this.length;if(i%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var c=0;c<i;c+=2)y(this,c,c+1);return this},m.prototype.swap32=function(){var i=this.length;if(i%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var c=0;c<i;c+=4)y(this,c,c+3),y(this,c+1,c+2);return this},m.prototype.swap64=function(){var i=this.length;if(i%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var c=0;c<i;c+=8)y(this,c,c+7),y(this,c+1,c+6),y(this,c+2,c+5),y(this,c+3,c+4);return this},m.prototype.toString=function(){var i=this.length;return i===0?"":arguments.length===0?he(this,0,i):A.apply(this,arguments)},m.prototype.toLocaleString=m.prototype.toString,m.prototype.equals=function(i){if(!m.isBuffer(i))throw new TypeError("Argument must be a Buffer");return this===i||m.compare(this,i)===0},m.prototype.inspect=function(){var i="",c=r.INSPECT_MAX_BYTES;return i=this.toString("hex",0,c).replace(/(.{2})/g,"$1 ").trim(),this.length>c&&(i+=" ... "),"<Buffer "+i+">"},m.prototype.compare=function(i,c,h,T,C){if(ae(i,Uint8Array)&&(i=m.from(i,i.offset,i.byteLength)),!m.isBuffer(i))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof i);if(c===void 0&&(c=0),h===void 0&&(h=i?i.length:0),T===void 0&&(T=0),C===void 0&&(C=this.length),c<0||h>i.length||T<0||C>this.length)throw new RangeError("out of range index");if(T>=C&&c>=h)return 0;if(T>=C)return-1;if(c>=h)return 1;if(this===i)return 0;for(var R=(C>>>=0)-(T>>>=0),x=(h>>>=0)-(c>>>=0),L=Math.min(R,x),O=this.slice(T,C),N=i.slice(c,h),H=0;H<L;++H)if(O[H]!==N[H]){R=O[H],x=N[H];break}return R<x?-1:x<R?1:0},m.prototype.includes=function(i,c,h){return this.indexOf(i,c,h)!==-1},m.prototype.indexOf=function(i,c,h){return k(this,i,c,h,!0)},m.prototype.lastIndexOf=function(i,c,h){return k(this,i,c,h,!1)},m.prototype.write=function(i,c,h,T){if(c===void 0)T="utf8",h=this.length,c=0;else if(h===void 0&&typeof c=="string")T=c,h=this.length,c=0;else{if(!isFinite(c))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");c>>>=0,isFinite(h)?(h>>>=0,T===void 0&&(T="utf8")):(T=h,h=void 0)}var C=this.length-c;if((h===void 0||h>C)&&(h=C),i.length>0&&(h<0||c<0)||c>this.length)throw new RangeError("Attempt to write outside buffer bounds");T||(T="utf8");for(var R=!1;;)switch(T){case"hex":return B(this,i,c,h);case"utf8":case"utf-8":return E(this,i,c,h);case"ascii":return D(this,i,c,h);case"latin1":case"binary":return P(this,i,c,h);case"base64":return ce(this,i,c,h);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return Z(this,i,c,h);default:if(R)throw new TypeError("Unknown encoding: "+T);T=(""+T).toLowerCase(),R=!0}},m.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var J=4096;function re(i,c,h){var T="";h=Math.min(i.length,h);for(var C=c;C<h;++C)T+=String.fromCharCode(127&i[C]);return T}function U(i,c,h){var T="";h=Math.min(i.length,h);for(var C=c;C<h;++C)T+=String.fromCharCode(i[C]);return T}function V(i,c,h){var T=i.length;(!c||c<0)&&(c=0),(!h||h<0||h>T)&&(h=T);for(var C="",R=c;R<h;++R)C+=X(i[R]);return C}function q(i,c,h){for(var T=i.slice(c,h),C="",R=0;R<T.length;R+=2)C+=String.fromCharCode(T[R]+256*T[R+1]);return C}function G(i,c,h){if(i%1!=0||i<0)throw new RangeError("offset is not uint");if(i+c>h)throw new RangeError("Trying to access beyond buffer length")}function W(i,c,h,T,C,R){if(!m.isBuffer(i))throw new TypeError('"buffer" argument must be a Buffer instance');if(c>C||c<R)throw new RangeError('"value" argument is out of bounds');if(h+T>i.length)throw new RangeError("Index out of range")}function z(i,c,h,T,C,R){if(h+T>i.length)throw new RangeError("Index out of range");if(h<0)throw new RangeError("Index out of range")}function oe(i,c,h,T,C){return c=+c,h>>>=0,C||z(i,0,h,4),o.write(i,c,h,T,23,4),h+4}function K(i,c,h,T,C){return c=+c,h>>>=0,C||z(i,0,h,8),o.write(i,c,h,T,52,8),h+8}m.prototype.slice=function(i,c){var h=this.length;(i=~~i)<0?(i+=h)<0&&(i=0):i>h&&(i=h),(c=c===void 0?h:~~c)<0?(c+=h)<0&&(c=0):c>h&&(c=h),c<i&&(c=i);var T=this.subarray(i,c);return T.__proto__=m.prototype,T},m.prototype.readUIntLE=function(i,c,h){i>>>=0,c>>>=0,h||G(i,c,this.length);for(var T=this[i],C=1,R=0;++R<c&&(C*=256);)T+=this[i+R]*C;return T},m.prototype.readUIntBE=function(i,c,h){i>>>=0,c>>>=0,h||G(i,c,this.length);for(var T=this[i+--c],C=1;c>0&&(C*=256);)T+=this[i+--c]*C;return T},m.prototype.readUInt8=function(i,c){return i>>>=0,c||G(i,1,this.length),this[i]},m.prototype.readUInt16LE=function(i,c){return i>>>=0,c||G(i,2,this.length),this[i]|this[i+1]<<8},m.prototype.readUInt16BE=function(i,c){return i>>>=0,c||G(i,2,this.length),this[i]<<8|this[i+1]},m.prototype.readUInt32LE=function(i,c){return i>>>=0,c||G(i,4,this.length),(this[i]|this[i+1]<<8|this[i+2]<<16)+16777216*this[i+3]},m.prototype.readUInt32BE=function(i,c){return i>>>=0,c||G(i,4,this.length),16777216*this[i]+(this[i+1]<<16|this[i+2]<<8|this[i+3])},m.prototype.readIntLE=function(i,c,h){i>>>=0,c>>>=0,h||G(i,c,this.length);for(var T=this[i],C=1,R=0;++R<c&&(C*=256);)T+=this[i+R]*C;return T>=(C*=128)&&(T-=Math.pow(2,8*c)),T},m.prototype.readIntBE=function(i,c,h){i>>>=0,c>>>=0,h||G(i,c,this.length);for(var T=c,C=1,R=this[i+--T];T>0&&(C*=256);)R+=this[i+--T]*C;return R>=(C*=128)&&(R-=Math.pow(2,8*c)),R},m.prototype.readInt8=function(i,c){return i>>>=0,c||G(i,1,this.length),128&this[i]?-1*(255-this[i]+1):this[i]},m.prototype.readInt16LE=function(i,c){i>>>=0,c||G(i,2,this.length);var h=this[i]|this[i+1]<<8;return 32768&h?4294901760|h:h},m.prototype.readInt16BE=function(i,c){i>>>=0,c||G(i,2,this.length);var h=this[i+1]|this[i]<<8;return 32768&h?4294901760|h:h},m.prototype.readInt32LE=function(i,c){return i>>>=0,c||G(i,4,this.length),this[i]|this[i+1]<<8|this[i+2]<<16|this[i+3]<<24},m.prototype.readInt32BE=function(i,c){return i>>>=0,c||G(i,4,this.length),this[i]<<24|this[i+1]<<16|this[i+2]<<8|this[i+3]},m.prototype.readFloatLE=function(i,c){return i>>>=0,c||G(i,4,this.length),o.read(this,i,!0,23,4)},m.prototype.readFloatBE=function(i,c){return i>>>=0,c||G(i,4,this.length),o.read(this,i,!1,23,4)},m.prototype.readDoubleLE=function(i,c){return i>>>=0,c||G(i,8,this.length),o.read(this,i,!0,52,8)},m.prototype.readDoubleBE=function(i,c){return i>>>=0,c||G(i,8,this.length),o.read(this,i,!1,52,8)},m.prototype.writeUIntLE=function(i,c,h,T){i=+i,c>>>=0,h>>>=0,T||W(this,i,c,h,Math.pow(2,8*h)-1,0);var C=1,R=0;for(this[c]=255&i;++R<h&&(C*=256);)this[c+R]=i/C&255;return c+h},m.prototype.writeUIntBE=function(i,c,h,T){i=+i,c>>>=0,h>>>=0,T||W(this,i,c,h,Math.pow(2,8*h)-1,0);var C=h-1,R=1;for(this[c+C]=255&i;--C>=0&&(R*=256);)this[c+C]=i/R&255;return c+h},m.prototype.writeUInt8=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,1,255,0),this[c]=255&i,c+1},m.prototype.writeUInt16LE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,2,65535,0),this[c]=255&i,this[c+1]=i>>>8,c+2},m.prototype.writeUInt16BE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,2,65535,0),this[c]=i>>>8,this[c+1]=255&i,c+2},m.prototype.writeUInt32LE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,4,4294967295,0),this[c+3]=i>>>24,this[c+2]=i>>>16,this[c+1]=i>>>8,this[c]=255&i,c+4},m.prototype.writeUInt32BE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,4,4294967295,0),this[c]=i>>>24,this[c+1]=i>>>16,this[c+2]=i>>>8,this[c+3]=255&i,c+4},m.prototype.writeIntLE=function(i,c,h,T){if(i=+i,c>>>=0,!T){var C=Math.pow(2,8*h-1);W(this,i,c,h,C-1,-C)}var R=0,x=1,L=0;for(this[c]=255&i;++R<h&&(x*=256);)i<0&&L===0&&this[c+R-1]!==0&&(L=1),this[c+R]=(i/x>>0)-L&255;return c+h},m.prototype.writeIntBE=function(i,c,h,T){if(i=+i,c>>>=0,!T){var C=Math.pow(2,8*h-1);W(this,i,c,h,C-1,-C)}var R=h-1,x=1,L=0;for(this[c+R]=255&i;--R>=0&&(x*=256);)i<0&&L===0&&this[c+R+1]!==0&&(L=1),this[c+R]=(i/x>>0)-L&255;return c+h},m.prototype.writeInt8=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,1,127,-128),i<0&&(i=255+i+1),this[c]=255&i,c+1},m.prototype.writeInt16LE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,2,32767,-32768),this[c]=255&i,this[c+1]=i>>>8,c+2},m.prototype.writeInt16BE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,2,32767,-32768),this[c]=i>>>8,this[c+1]=255&i,c+2},m.prototype.writeInt32LE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,4,2147483647,-2147483648),this[c]=255&i,this[c+1]=i>>>8,this[c+2]=i>>>16,this[c+3]=i>>>24,c+4},m.prototype.writeInt32BE=function(i,c,h){return i=+i,c>>>=0,h||W(this,i,c,4,2147483647,-2147483648),i<0&&(i=4294967295+i+1),this[c]=i>>>24,this[c+1]=i>>>16,this[c+2]=i>>>8,this[c+3]=255&i,c+4},m.prototype.writeFloatLE=function(i,c,h){return oe(this,i,c,!0,h)},m.prototype.writeFloatBE=function(i,c,h){return oe(this,i,c,!1,h)},m.prototype.writeDoubleLE=function(i,c,h){return K(this,i,c,!0,h)},m.prototype.writeDoubleBE=function(i,c,h){return K(this,i,c,!1,h)},m.prototype.copy=function(i,c,h,T){if(!m.isBuffer(i))throw new TypeError("argument should be a Buffer");if(h||(h=0),T||T===0||(T=this.length),c>=i.length&&(c=i.length),c||(c=0),T>0&&T<h&&(T=h),T===h||i.length===0||this.length===0)return 0;if(c<0)throw new RangeError("targetStart out of bounds");if(h<0||h>=this.length)throw new RangeError("Index out of range");if(T<0)throw new RangeError("sourceEnd out of bounds");T>this.length&&(T=this.length),i.length-c<T-h&&(T=i.length-c+h);var C=T-h;if(this===i&&typeof Uint8Array.prototype.copyWithin=="function")this.copyWithin(c,h,T);else if(this===i&&h<c&&c<T)for(var R=C-1;R>=0;--R)i[R+c]=this[R+h];else Uint8Array.prototype.set.call(i,this.subarray(h,T),c);return C},m.prototype.fill=function(i,c,h,T){if(typeof i=="string"){if(typeof c=="string"?(T=c,c=0,h=this.length):typeof h=="string"&&(T=h,h=this.length),T!==void 0&&typeof T!="string")throw new TypeError("encoding must be a string");if(typeof T=="string"&&!m.isEncoding(T))throw new TypeError("Unknown encoding: "+T);if(i.length===1){var C=i.charCodeAt(0);(T==="utf8"&&C<128||T==="latin1")&&(i=C)}}else typeof i=="number"&&(i&=255);if(c<0||this.length<c||this.length<h)throw new RangeError("Out of range index");if(h<=c)return this;var R;if(c>>>=0,h=h===void 0?this.length:h>>>0,i||(i=0),typeof i=="number")for(R=c;R<h;++R)this[R]=i;else{var x=m.isBuffer(i)?i:m.from(i,T),L=x.length;if(L===0)throw new TypeError('The value "'+i+'" is invalid for argument "value"');for(R=0;R<h-c;++R)this[R+c]=x[R%L]}return this};var ge=/[^+/0-9A-Za-z-_]/g;function X(i){return i<16?"0"+i.toString(16):i.toString(16)}function pe(i,c){var h;c=c||1/0;for(var T=i.length,C=null,R=[],x=0;x<T;++x){if((h=i.charCodeAt(x))>55295&&h<57344){if(!C){if(h>56319){(c-=3)>-1&&R.push(239,191,189);continue}if(x+1===T){(c-=3)>-1&&R.push(239,191,189);continue}C=h;continue}if(h<56320){(c-=3)>-1&&R.push(239,191,189),C=h;continue}h=65536+(C-55296<<10|h-56320)}else C&&(c-=3)>-1&&R.push(239,191,189);if(C=null,h<128){if((c-=1)<0)break;R.push(h)}else if(h<2048){if((c-=2)<0)break;R.push(h>>6|192,63&h|128)}else if(h<65536){if((c-=3)<0)break;R.push(h>>12|224,h>>6&63|128,63&h|128)}else{if(!(h<1114112))throw new Error("Invalid code point");if((c-=4)<0)break;R.push(h>>18|240,h>>12&63|128,h>>6&63|128,63&h|128)}}return R}function ee(i){return a.toByteArray(function(c){if((c=(c=c.split("=")[0]).trim().replace(ge,"")).length<2)return"";for(;c.length%4!=0;)c+="=";return c}(i))}function te(i,c,h,T){for(var C=0;C<T&&!(C+h>=c.length||C>=i.length);++C)c[C+h]=i[C];return C}function ae(i,c){return i instanceof c||i!=null&&i.constructor!=null&&i.constructor.name!=null&&i.constructor.name===c.name}function M(i){return i!=i}}).call(this)}).call(this,e("buffer").Buffer)},{"base64-js":1,buffer:2,ieee754:3}],3:[function(e,t,r){r.read=function(n,a,o,l,f){var m,u,v=8*f-l-1,b=(1<<v)-1,S=b>>1,_=-7,g=o?f-1:0,A=o?-1:1,y=n[a+g];for(g+=A,m=y&(1<<-_)-1,y>>=-_,_+=v;_>0;m=256*m+n[a+g],g+=A,_-=8);for(u=m&(1<<-_)-1,m>>=-_,_+=l;_>0;u=256*u+n[a+g],g+=A,_-=8);if(m===0)m=1-S;else{if(m===b)return u?NaN:1/0*(y?-1:1);u+=Math.pow(2,l),m-=S}return(y?-1:1)*u*Math.pow(2,m-l)},r.write=function(n,a,o,l,f,m){var u,v,b,S=8*m-f-1,_=(1<<S)-1,g=_>>1,A=f===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=l?0:m-1,k=l?1:-1,I=a<0||a===0&&1/a<0?1:0;for(a=Math.abs(a),isNaN(a)||a===1/0?(v=isNaN(a)?1:0,u=_):(u=Math.floor(Math.log(a)/Math.LN2),a*(b=Math.pow(2,-u))<1&&(u--,b*=2),(a+=u+g>=1?A/b:A*Math.pow(2,1-g))*b>=2&&(u++,b/=2),u+g>=_?(v=0,u=_):u+g>=1?(v=(a*b-1)*Math.pow(2,f),u+=g):(v=a*Math.pow(2,g-1)*Math.pow(2,f),u=0));f>=8;n[o+y]=255&v,y+=k,v/=256,f-=8);for(u=u<<f|v,S+=f;S>0;n[o+y]=255&u,y+=k,u/=256,S-=8);n[o+y-k]|=128*I}},{}],4:[function(e,t,r){(function(n){(function(){let a=this,o=e("./transformers"),l="UTF8",f="UTF16",m="UTF16BE",u="UTF16LE",v="UTF32",b="UTF32BE",S="UTF32LE",_="UINT7",g="ASCII",A="BINARY",y="UINT8",k="UINT16",I="UINT16LE",B="UINT16BE",E="UINT32",D="UINT32LE",P="UINT32BE",ce="ESCAPED",Z="STRING",ie=function(J,re){if(typeof J!="string"||J==="")throw new TypeError(`type: "${J}" not recognized`);let U=function(V){let q={type:"",base64:!1},G=/^(base64:)?([a-zA-Z0-9]+)$/i.exec(V);return G&&(G[2]&&(q.type=G[2].toUpperCase()),G[1]&&(q.base64=!0)),q}(J.toUpperCase());if(U.base64){if(U.type===Z)throw new TypeError(`type: "${J} "BASE64:" prefix not allowed with type STRING`);if(n.isBuffer(re))U.data=o.base64.decode(re);else{if(typeof re!="string")throw new TypeError(`type: "${J} unrecognized data type: typeof(data): ${typeof re}`);{let V=n.from(re,"ascii");U.data=o.base64.decode(V)}}}else U.data=re;switch(U.type){case l:(function(V){V.type=l;let q=V.data;V.bom=0,q.length>=3&&q[0]===239&&q[1]===187&&q[2]===191&&(V.bom=3)})(U);break;case f:case m:case u:(function(V){let q=V.data;switch(V.bom=0,V.type){case f:V.type=m,q.length>=2&&(q[0]===254&&q[1]===255?V.bom=2:q[0]===255&&q[1]===254&&(V.type=u,V.bom=2));break;case m:if(V.type=m,q.length>=2){if(q[0]===254&&q[1]===255)V.bom=2;else if(q[0]===255&&q[1]===254)throw new TypeError('src type: "UTF16BE" specified but BOM is for "UTF16LE"')}break;case u:if(V.type=u,q.length>=0){if(q[0]===254&&q[1]===255)throw new TypeError('src type: "UTF16LE" specified but BOM is for "UTF16BE"');q[0]===255&&q[1]===254&&(V.bom=2)}break;default:throw new TypeError(`UTF16 BOM: src type "${V.type}" unrecognized`)}})(U);break;case v:case b:case S:(function(V){let q=V.data;switch(V.bom=0,V.type){case v:V.type=b,q.length>=4&&(q[0]===0&&q[1]===0&&q[2]===254&&q[3]===255&&(V.bom=4),q[0]===255&&q[1]===254&&q[2]===0&&q[3]===0&&(V.type=S,V.bom=4));break;case b:if(V.type=b,q.length>=4&&(q[0]===0&&q[1]===0&&q[2]===254&&q[3]===255&&(V.bom=4),q[0]===255&&q[1]===254&&q[2]===0&&q[3]===0))throw new TypeError('src type: UTF32BE specified but BOM is for UTF32LE"');break;case S:if(V.type=S,q.length>=4){if(q[0]===0&&q[1]===0&&q[2]===254&&q[3]===255)throw new TypeError('src type: "UTF32LE" specified but BOM is for "UTF32BE"');q[0]===255&&q[1]===254&&q[2]===0&&q[3]===0&&(V.bom=4)}break;default:throw new TypeError(`UTF32 BOM: src type "${V.type}" unrecognized`)}})(U);break;case k:U.type=B;break;case E:U.type=P;break;case g:U.type=_;break;case A:U.type=y;break;case _:case y:case I:case B:case D:case P:case Z:case ce:break;default:throw new TypeError(`type: "${J}" not recognized`)}if(U.type===Z){if(typeof U.data!="string")throw new TypeError(`type: "${J}" but data is not a string`)}else if(!n.isBuffer(U.data))throw new TypeError(`type: "${J}" but data is not a Buffer`);return U},he=function(J,re){switch(J){case l:return o.utf8.encode(re);case m:return o.utf16be.encode(re);case u:return o.utf16le.encode(re);case b:return o.utf32be.encode(re);case S:return o.utf32le.encode(re);case _:return o.uint7.encode(re);case y:return o.uint8.encode(re);case B:return o.uint16be.encode(re);case I:return o.uint16le.encode(re);case P:return o.uint32be.encode(re);case D:return o.uint32le.encode(re);case Z:return o.string.encode(re);case ce:return o.escaped.encode(re);default:throw new TypeError(`encode type "${J}" not recognized`)}};r.decode=function(J,re){return function(U){switch(U.type){case l:return o.utf8.decode(U.data,U.bom);case u:return o.utf16le.decode(U.data,U.bom);case m:return o.utf16be.decode(U.data,U.bom);case b:return o.utf32be.decode(U.data,U.bom);case S:return o.utf32le.decode(U.data,U.bom);case _:return o.uint7.decode(U.data);case y:return o.uint8.decode(U.data);case B:return o.uint16be.decode(U.data);case I:return o.uint16le.decode(U.data);case P:return o.uint32be.decode(U.data);case D:return o.uint32le.decode(U.data);case Z:return o.string.decode(U.data);case ce:return o.escaped.decode(U.data);default:throw new TypeError(`decode type "${U.type}" not recognized`)}}(ie(J,re))},r.encode=function(J,re){let U,V,q=function(G,W){if(!Array.isArray(W))throw new TypeError('dst chars: not array: "'+typeof W);if(typeof G!="string")throw new TypeError('dst type: not string: "'+typeof G);let z=function(oe){let K,ge,X={crlf:!1,lf:!1,base64:!1,type:""};for(;;){if(ge=oe,K=oe.slice(0,5),K==="CRLF:"){X.crlf=!0,ge=oe.slice(5);break}if(K=oe.slice(0,3),K==="LF:"){X.lf=!0,ge=oe.slice(3);break}break}return K=ge.split(":"),K.length===1?X.type=K[0]:K.length===2&&K[1]==="BASE64"&&(X.base64=!0,X.type=K[0]),X}(G.toUpperCase());switch(z.type){case l:case m:case u:case b:case S:case _:case y:case I:case B:case D:case P:case ce:break;case Z:if(z.base64)throw new TypeError('":BASE64" suffix not allowed with type STRING');break;case g:z.type=_;break;case A:z.type=y;break;case f:z.type=m;break;case v:z.type=b;break;case k:z.type=B;break;case E:z.type=P;break;default:throw new TypeError(`dst type unrecognized: "${G}" : must have form [crlf:|lf:]type[:base64]`)}return z}(J,re);return q.crlf?(U=o.lineEnds.crlf(re),V=he(q.type,U)):q.lf?(U=o.lineEnds.lf(re),V=he(q.type,U)):V=he(q.type,re),q.base64&&(V=o.base64.encode(V)),V},r.convert=function(J,re,U){return a.encode(U,a.decode(J,re))}}).call(this)}).call(this,e("buffer").Buffer)},{"./transformers":6,buffer:2}],5:[function(e,t,r){t.exports={converter:e("./converter"),transformers:e("./transformers")}},{"./converter":4,"./transformers":6}],6:[function(e,t,r){(function(n){(function(){let a=this,o=4294967292,l=4294967293,f=4294967294,m=4294967295,u=[0,1,3,7,15,31,63,127,255,511,1023],v=["00","01","02","03","04","05","06","07","08","09","0A","0B","0C","0D","0E","0F","10","11","12","13","14","15","16","17","18","19","1A","1B","1C","1D","1E","1F","20","21","22","23","24","25","26","27","28","29","2A","2B","2C","2D","2E","2F","30","31","32","33","34","35","36","37","38","39","3A","3B","3C","3D","3E","3F","40","41","42","43","44","45","46","47","48","49","4A","4B","4C","4D","4E","4F","50","51","52","53","54","55","56","57","58","59","5A","5B","5C","5D","5E","5F","60","61","62","63","64","65","66","67","68","69","6A","6B","6C","6D","6E","6F","70","71","72","73","74","75","76","77","78","79","7A","7B","7C","7D","7E","7F","80","81","82","83","84","85","86","87","88","89","8A","8B","8C","8D","8E","8F","90","91","92","93","94","95","96","97","98","99","9A","9B","9C","9D","9E","9F","A0","A1","A2","A3","A4","A5","A6","A7","A8","A9","AA","AB","AC","AD","AE","AF","B0","B1","B2","B3","B4","B5","B6","B7","B8","B9","BA","BB","BC","BD","BE","BF","C0","C1","C2","C3","C4","C5","C6","C7","C8","C9","CA","CB","CC","CD","CE","CF","D0","D1","D2","D3","D4","D5","D6","D7","D8","D9","DA","DB","DC","DD","DE","DF","E0","E1","E2","E3","E4","E5","E6","E7","E8","E9","EA","EB","EC","ED","EE","EF","F0","F1","F2","F3","F4","F5","F6","F7","F8","F9","FA","FB","FC","FD","FE","FF"],b="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".split(""),S=[];b.forEach(g=>{S.push(g.charCodeAt(0))}),r.utf8={encode(g){let A=[];return g.forEach(y=>{if(y>=0&&y<=127)A.push(y);else if(y<=2047)A.push(192+(y>>6&u[5])),A.push(128+(y&u[6]));else if(y<55296||y>57343&&y<=65535)A.push(224+(y>>12&u[4])),A.push(128+(y>>6&u[6])),A.push(128+(y&u[6]));else{if(!(y>=65536&&y<=1114111))throw new RangeError(`utf8.encode: character out of range: char: ${y}`);{let k=y>>16&u[5];A.push(240+(k>>2)),A.push(128+((k&u[2])<<4)+(y>>12&u[4])),A.push(128+(y>>6&u[6])),A.push(128+(y&u[6]))}}}),n.from(A)},decode(g,A){function y(re,U){if((192&U)!=128)return l;let V=((re&u[5])<<6)+(U&u[6]);return V<128?o:V}function k(re,U,V){if((192&V)!=128||(192&U)!=128)return l;let q=((re&u[4])<<12)+((U&u[6])<<6)+(V&u[6]);return q<2048?o:q>=55296&&q<=57343?f:q}function I(re,U,V,q){if((192&q)!=128||(192&V)!=128||(192&U)!=128)return l;let G=(((re&u[3])<<2)+(U>>4&u[2])<<16)+((U&u[4])<<12)+((V&u[6])<<6)+(q&u[6]);return G<65536?o:G>1114111?f:G}let B,E,D,P,ce,Z,ie=g.length,he=A?3:0,J=[];for(;he<ie;){E=g[he],B=m;let re=!0;for(;re;){if(E>=0&&E<=127){B=E,Z=1;break}if(D=he+1,D<ie&&E>=194&&E<=223){B=y(E,g[D]),Z=2;break}if(P=he+2,P<ie&&E>=224&&E<=239){B=k(E,g[D],g[P]),Z=3;break}if(ce=he+3,ce<ie&&E>=240&&E<=244){B=I(E,g[D],g[P],g[ce]),Z=4;break}break}if(B>1114111){let U=`byte[${he}]`;throw B===m?new RangeError(`utf8.decode: ill-formed UTF8 byte sequence found at: ${U}`):B===l?new RangeError(`utf8.decode: illegal trailing byte found at: ${U}`):B===f?new RangeError(`utf8.decode: code point out of range found at: ${U}`):B===o?new RangeError(`utf8.decode: non-shortest form found at: ${U}`):new RangeError(`utf8.decode: unrecognized error found at: ${U}`)}J.push(B),he+=Z}return J}},r.utf16be={encode(g){let A=[],y,k,I;for(let B=0;B<g.length;B+=1)if(y=g[B],y>=0&&y<=55295||y>=57344&&y<=65535)A.push(y>>8&u[8]),A.push(y&u[8]);else{if(!(y>=65536&&y<=1114111))throw new RangeError(`utf16be.encode: UTF16BE value out of range: char[${B}]: ${y}`);I=y-65536,k=55296+(I>>10),I=56320+(I&u[10]),A.push(k>>8&u[8]),A.push(k&u[8]),A.push(I>>8&u[8]),A.push(I&u[8])}return n.from(A)},decode(g,A){if(g.length%2>0)throw new RangeError(`utf16be.decode: data length must be even multiple of 2: length: ${g.length}`);let y=[],k=g.length,I,B,E,D,P,ce,Z=A?2:0,ie=0;for(;Z<k;){for(;;){if(E=Z+1,E<k){if(P=(g[Z]<<8)+g[E],P<55296||P>57343){I=P,B=2;break}if(D=Z+3,D<k&&(ce=(g[Z+2]<<8)+g[D],P<=56319&&ce>=56320&&ce<=57343)){I=65536+(P-55296<<10)+(ce-56320),B=4;break}}throw new RangeError(`utf16be.decode: ill-formed UTF16BE byte sequence found: byte[${Z}]`)}y[ie++]=I,Z+=B}return y}},r.utf16le={encode(g){let A=[],y,k,I;for(let B=0;B<g.length;B+=1)if(y=g[B],y>=0&&y<=55295||y>=57344&&y<=65535)A.push(y&u[8]),A.push(y>>8&u[8]);else{if(!(y>=65536&&y<=1114111))throw new RangeError(`utf16le.encode: UTF16LE value out of range: char[${B}]: ${y}`);I=y-65536,k=55296+(I>>10),I=56320+(I&u[10]),A.push(k&u[8]),A.push(k>>8&u[8]),A.push(I&u[8]),A.push(I>>8&u[8])}return n.from(A)},decode(g,A){if(g.length%2>0)throw new RangeError(`utf16le.decode: data length must be even multiple of 2: length: ${g.length}`);let y=[],k=g.length,I,B,E,D,P,ce,Z=A?2:0,ie=0;for(;Z<k;){for(;;){if(E=Z+1,E<k){if(P=(g[E]<<8)+g[Z],P<55296||P>57343){I=P,B=2;break}if(D=Z+3,D<k&&(ce=(g[D]<<8)+g[Z+2],P<=56319&&ce>=56320&&ce<=57343)){I=65536+(P-55296<<10)+(ce-56320),B=4;break}}throw new RangeError(`utf16le.decode: ill-formed UTF16LE byte sequence found: byte[${Z}]`)}y[ie++]=I,Z+=B}return y}},r.utf32be={encode(g){let A=n.alloc(4*g.length),y=0;return g.forEach(k=>{if(k>=55296&&k<=57343||k>1114111)throw new RangeError(`utf32be.encode: UTF32BE character code out of range: char[${y/4}]: ${k}`);A[y++]=k>>24&u[8],A[y++]=k>>16&u[8],A[y++]=k>>8&u[8],A[y++]=k&u[8]}),A},decode(g,A){if(g.length%4>0)throw new RangeError(`utf32be.decode: UTF32BE byte length must be even multiple of 4: length: ${g.length}`);let y=[],k=A?4:0;for(;k<g.length;k+=4){let I=(g[k]<<24)+(g[k+1]<<16)+(g[k+2]<<8)+g[k+3];if(I>=55296&&I<=57343||I>1114111)throw new RangeError(`utf32be.decode: UTF32BE character code out of range: char[${k/4}]: ${I}`);y.push(I)}return y}},r.utf32le={encode(g){let A=n.alloc(4*g.length),y=0;return g.forEach(k=>{if(k>=55296&&k<=57343||k>1114111)throw new RangeError(`utf32le.encode: UTF32LE character code out of range: char[${y/4}]: ${k}`);A[y++]=k&u[8],A[y++]=k>>8&u[8],A[y++]=k>>16&u[8],A[y++]=k>>24&u[8]}),A},decode(g,A){if(g.length%4>0)throw new RangeError(`utf32be.decode: UTF32LE byte length must be even multiple of 4: length: ${g.length}`);let y=[],k=A?4:0;for(;k<g.length;k+=4){let I=(g[k+3]<<24)+(g[k+2]<<16)+(g[k+1]<<8)+g[k];if(I>=55296&&I<=57343||I>1114111)throw new RangeError(`utf32le.encode: UTF32LE character code out of range: char[${k/4}]: ${I}`);y.push(I)}return y}},r.uint7={encode(g){let A=n.alloc(g.length);for(let y=0;y<g.length;y+=1){if(g[y]>127)throw new RangeError(`uint7.encode: UINT7 character code out of range: char[${y}]: ${g[y]}`);A[y]=g[y]}return A},decode(g){let A=[];for(let y=0;y<g.length;y+=1){if(g[y]>127)throw new RangeError(`uint7.decode: UINT7 character code out of range: byte[${y}]: ${g[y]}`);A[y]=g[y]}return A}},r.uint8={encode(g){let A=n.alloc(g.length);for(let y=0;y<g.length;y+=1){if(g[y]>255)throw new RangeError(`uint8.encode: UINT8 character code out of range: char[${y}]: ${g[y]}`);A[y]=g[y]}return A},decode(g){let A=[];for(let y=0;y<g.length;y+=1)A[y]=g[y];return A}},r.uint16be={encode(g){let A=n.alloc(2*g.length),y=0;return g.forEach(k=>{if(k>65535)throw new RangeError(`uint16be.encode: UINT16BE character code out of range: char[${y/2}]: ${k}`);A[y++]=k>>8&u[8],A[y++]=k&u[8]}),A},decode(g){if(g.length%2>0)throw new RangeError(`uint16be.decode: UINT16BE byte length must be even multiple of 2: length: ${g.length}`);let A=[];for(let y=0;y<g.length;y+=2)A.push((g[y]<<8)+g[y+1]);return A}},r.uint16le={encode(g){let A=n.alloc(2*g.length),y=0;return g.forEach(k=>{if(k>65535)throw new RangeError(`uint16le.encode: UINT16LE character code out of range: char[${y/2}]: ${k}`);A[y++]=k&u[8],A[y++]=k>>8&u[8]}),A},decode(g){if(g.length%2>0)throw new RangeError(`uint16le.decode: UINT16LE byte length must be even multiple of 2: length: ${g.length}`);let A=[];for(let y=0;y<g.length;y+=2)A.push((g[y+1]<<8)+g[y]);return A}},r.uint32be={encode(g){let A=n.alloc(4*g.length),y=0;return g.forEach(k=>{A[y++]=k>>24&u[8],A[y++]=k>>16&u[8],A[y++]=k>>8&u[8],A[y++]=k&u[8]}),A},decode(g){if(g.length%4>0)throw new RangeError(`uint32be.decode: UINT32BE byte length must be even multiple of 4: length: ${g.length}`);let A=[];for(let y=0;y<g.length;y+=4)A.push((g[y]<<24)+(g[y+1]<<16)+(g[y+2]<<8)+g[y+3]);return A}},r.uint32le={encode(g){let A=n.alloc(4*g.length),y=0;return g.forEach(k=>{A[y++]=k&u[8],A[y++]=k>>8&u[8],A[y++]=k>>16&u[8],A[y++]=k>>24&u[8]}),A},decode(g){if(g.length%4>0)throw new RangeError(`uint32le.decode: UINT32LE byte length must be even multiple of 4: length: ${g.length}`);let A=[];for(let y=0;y<g.length;y+=4)A.push((g[y+3]<<24)+(g[y+2]<<16)+(g[y+1]<<8)+g[y]);return A}},r.string={encode:g=>a.utf16le.encode(g).toString("utf16le"),decode:g=>a.utf16le.decode(n.from(g,"utf16le"),0)},r.escaped={encode(g){let A=[];for(let y=0;y<g.length;y+=1){let k=g[y];if(k===96)A.push(k),A.push(k);else if(k===10)A.push(k);else if(k>=32&&k<=126)A.push(k);else{let I="";if(k>=0&&k<=31)I+=`\`x${v[k]}`;else if(k>=127&&k<=255)I+=`\`x${v[k]}`;else if(k>=256&&k<=65535)I+=`\`u${v[k>>8&u[8]]}${v[k&u[8]]}`;else{if(!(k>=65536&&k<=4294967295))throw new Error("escape.encode(char): char > 0xffffffff not allowed");{I+="`u{";let B=k>>24&u[8];B>0&&(I+=v[B]),I+=`${v[k>>16&u[8]]+v[k>>8&u[8]]+v[k&u[8]]}}`}}n.from(I).forEach(B=>{A.push(B)})}}return n.from(A)},decode(g){function A(ie){return ie>=48&&ie<=57||ie>=65&&ie<=70||ie>=97&&ie<=102}function y(ie,he,J){let re={char:null,nexti:ie+2,error:!0};if(ie+1<he&&A(J[ie])&&A(J[ie+1])){let U=String.fromCodePoint(J[ie],J[ie+1]);re.char=parseInt(U,16),Number.isNaN(re.char)||(re.error=!1)}return re}function k(ie,he,J){let re={char:null,nexti:ie+4,error:!0};if(ie+3<he&&A(J[ie])&&A(J[ie+1])&&A(J[ie+2])&&A(J[ie+3])){let U=String.fromCodePoint(J[ie],J[ie+1],J[ie+2],J[ie+3]);re.char=parseInt(U,16),Number.isNaN(re.char)||(re.error=!1)}return re}function I(ie,he,J){let re={char:null,nexti:ie+4,error:!0},U="";for(;ie<he&&A(J[ie]);)U+=String.fromCodePoint(J[ie]),ie+=1;return re.char=parseInt(U,16),J[ie]!==125||Number.isNaN(re.char)||(re.error=!1),re.nexti=ie+1,re}let B=[],E=g.length,D,P,ce,Z=0;for(;Z<E;){for(;;){if(ce=!0,g[Z]!==96){B.push(g[Z]),Z+=1,ce=!1;break}if(D=Z+1,D>=E)break;if(g[D]===96){B.push(96),Z+=2,ce=!1;break}if(g[D]===120){if(P=y(D+1,E,g),P.error)break;B.push(P.char),Z=P.nexti,ce=!1;break}if(g[D]===117){if(g[D+1]===123){if(P=I(D+2,E,g),P.error)break;B.push(P.char),Z=P.nexti,ce=!1;break}if(P=k(D+1,E,g),P.error)break;B.push(P.char),Z=P.nexti,ce=!1;break}break}if(ce)throw new Error(`escaped.decode: ill-formed escape sequence at buf[${Z}]`)}return B}};let _=10;r.lineEnds={crlf(g){let A=[],y=0;for(;y<g.length;)switch(g[y]){case 13:y+1<g.length&&g[y+1]===_?y+=2:y+=1,A.push(13),A.push(_);break;case _:A.push(13),A.push(_),y+=1;break;default:A.push(g[y]),y+=1}return A.length>0&&A[A.length-1]!==_&&(A.push(13),A.push(_)),A},lf(g){let A=[],y=0;for(;y<g.length;)switch(g[y]){case 13:y+1<g.length&&g[y+1]===_?y+=2:y+=1,A.push(_);break;case _:A.push(_),y+=1;break;default:A.push(g[y]),y+=1}return A.length>0&&A[A.length-1]!==_&&A.push(_),A}},r.base64={encode(g){if(g.length===0)return n.alloc(0);let A,y,k,I=g.length%3;I=I>0?3-I:0;let B=(g.length+I)/3,E=n.alloc(4*B);I>0&&(B-=1),A=0,y=0;for(let D=0;D<B;D+=1)k=g[A++]<<16,k+=g[A++]<<8,k+=g[A++],E[y++]=S[k>>18&u[6]],E[y++]=S[k>>12&u[6]],E[y++]=S[k>>6&u[6]],E[y++]=S[k&u[6]];return I===0?E:I===1?(k=g[A++]<<16,k+=g[A]<<8,E[y++]=S[k>>18&u[6]],E[y++]=S[k>>12&u[6]],E[y++]=S[k>>6&u[6]],E[y]=S[64],E):I===2?(k=g[A]<<16,E[y++]=S[k>>18&u[6]],E[y++]=S[k>>12&u[6]],E[y++]=S[64],E[y]=S[64],E):void 0},decode(g){if(g.length===0)return n.alloc(0);let A=function(ce){let Z=[],ie=0;for(let he=0;he<ce.length;he+=1){let J=ce[he],re=!0;for(;re&&J!==32&&J!==9&&J!==10&&J!==13;){if(J>=65&&J<=90){Z.push(J-65);break}if(J>=97&&J<=122){Z.push(J-71);break}if(J>=48&&J<=57){Z.push(J+4);break}if(J===43){Z.push(62);break}if(J===47){Z.push(63);break}if(J===61){Z.push(64),ie+=1;break}throw new RangeError(`base64.decode: invalid character buf[${he}]: ${J}`)}}if(Z.length%4>0)throw new RangeError(`base64.decode: string length not integral multiple of 4: ${Z.length}`);switch(ie){case 0:break;case 1:if(Z[Z.length-1]!==64)throw new RangeError("base64.decode: one tail character found: not last character");break;case 2:if(Z[Z.length-1]!==64||Z[Z.length-2]!==64)throw new RangeError("base64.decode: two tail characters found: not last characters");break;default:throw new RangeError(`base64.decode: more than two tail characters found: ${ie}`)}return{tail:ie,buf:n.from(Z)}}(g),{tail:y}=A,k=A.buf,I,B,E,D=k.length/4,P=n.alloc(3*D-y);y>0&&(D-=1),B=0,I=0;for(let ce=0;ce<D;ce+=1)E=k[I++]<<18,E+=k[I++]<<12,E+=k[I++]<<6,E+=k[I++],P[B++]=E>>16&u[8],P[B++]=E>>8&u[8],P[B++]=E&u[8];return y===1&&(E=k[I++]<<18,E+=k[I++]<<12,E+=k[I]<<6,P[B++]=E>>16&u[8],P[B]=E>>8&u[8]),y===2&&(E=k[I++]<<18,E+=k[I++]<<12,P[B]=E>>16&u[8]),P},toString(g){if(g.length%4>0)throw new RangeError(`base64.toString: input buffer length not multiple of 4: ${g.length}`);let A="",y=0;function k(B,E,D,P){switch(y){case 76:A+=`\r
${B}${E}${D}${P}`,y=4;break;case 75:A+=`${B}\r
${E}${D}${P}`,y=3;break;case 74:A+=`${B+E}\r
${D}${P}`,y=2;break;case 73:A+=`${B+E+D}\r
${P}`,y=1;break;default:A+=B+E+D+P,y+=4}}for(let B=0;B<g.length;B+=4){for(let E=B;E<B+4;E+=1)if(!((I=g[E])>=65&&I<=90||I>=97&&I<=122||I>=48&&I<=57||I===43||I===47||I===61))throw new RangeError(`base64.toString: buf[${E}]: ${g[E]} : not valid base64 character code`);k(String.fromCharCode(g[B]),String.fromCharCode(g[B+1]),String.fromCharCode(g[B+2]),String.fromCharCode(g[B+3]))}var I;return A}}}).call(this)}).call(this,e("buffer").Buffer)},{buffer:2}],7:[function(e,t,r){t.exports=function(){let n=e("./identifiers"),a=e("./utilities"),o=this,l=null,f=null,m=null,u=0,v=[],b=[],S=[],_=[];function g(A){let y="";for(let k=0;k<A;k+=1)y+=" ";return y}this.callbacks=[],this.astObject="astObject",this.init=function(A,y,k){let I;S.length=0,_.length=0,v.length=0,u=0,l=A,f=y,m=k;let B=[];for(I=0;I<l.length;I+=1)B.push(l[I].lower);for(I=0;I<f.length;I+=1)B.push(f[I].lower);for(u=l.length+f.length,I=0;I<u;I+=1)v[I]=!1,b[I]=null;for(let E in o.callbacks){let D=E.toLowerCase();if(I=B.indexOf(D),I<0)throw new Error(`ast.js: init: node '${E}' not a rule or udt name`);typeof o.callbacks[E]=="function"&&(v[I]=!0,b[I]=o.callbacks[E]),o.callbacks[E]===!0&&(v[I]=!0)}},this.ruleDefined=function(A){return v[A]!==!1},this.udtDefined=function(A){return v[l.length+A]!==!1},this.down=function(A,y){let k=_.length;return S.push(k),_.push({name:y,thisIndex:k,thatIndex:null,state:n.SEM_PRE,callbackIndex:A,phraseIndex:null,phraseLength:null,stack:S.length}),k},this.up=function(A,y,k,I){let B=_.length,E=S.pop();return _.push({name:y,thisIndex:B,thatIndex:E,state:n.SEM_POST,callbackIndex:A,phraseIndex:k,phraseLength:I,stack:S.length}),_[E].thatIndex=B,_[E].phraseIndex=k,_[E].phraseLength=I,B},this.translate=function(A){let y,k,I;for(let B=0;B<_.length;B+=1)I=_[B],k=b[I.callbackIndex],I.state===n.SEM_PRE?k!==null&&(y=k(n.SEM_PRE,m,I.phraseIndex,I.phraseLength,A),y===n.SEM_SKIP&&(B=I.thatIndex)):k!==null&&k(n.SEM_POST,m,I.phraseIndex,I.phraseLength,A)},this.setLength=function(A){_.length=A,S.length=A>0?_[A-1].stack:0},this.getLength=function(){return _.length},this.toXml=function(A){let y=a.charsToDec,k="decimal integer character codes";if(typeof A=="string"&&A.length>=3){let E=A.slice(0,3).toLowerCase();E==="asc"?(y=a.charsToAscii,k="ASCII for printing characters, hex for non-printing"):E==="hex"?(y=a.charsToHex,k="hexadecimal integer character codes"):E==="uni"&&(y=a.charsToUnicode,k="Unicode UTF-32 integer character codes")}let I="",B=0;return I+=`<?xml version="1.0" encoding="utf-8"?>
`,I+=`<root nodes="${_.length/2}" characters="${m.length}">
`,I+=`<!-- input string, ${k} -->
`,I+=g(B+2),I+=y(m),I+=`
`,_.forEach(E=>{E.state===n.SEM_PRE?(B+=1,I+=g(B),I+=`<node name="${E.name}" index="${E.phraseIndex}" length="${E.phraseLength}">
`,I+=g(B+2),I+=y(m,E.phraseIndex,E.phraseLength),I+=`
`):(I+=g(B),I+=`</node><!-- name="${E.name}" -->
`,B-=1)}),I+=`</root>
`,I},this.phrases=function(){let A={},y,k;for(y=0;y<_.length;y+=1)k=_[y],k.state===n.SEM_PRE&&(Array.isArray(A[k.name])||(A[k.name]=[]),A[k.name].push({index:k.phraseIndex,length:k.phraseLength}));return A}}},{"./identifiers":10,"./utilities":16}],8:[function(e,t,r){t.exports=function(){let n=-1,a=0;this.init=function(o){if(typeof o!="number"||o<=0)throw new Error("circular-buffer.js: init: circular buffer size must an integer > 0");a=Math.ceil(o),n=-1},this.increment=function(){return n+=1,(n+a)%a},this.maxSize=function(){return a},this.items=function(){return n+1},this.getListIndex=function(o){return n===-1||o<0||o>n||n-o>=a?-1:(o+a)%a},this.forEach=function(o){if(n!==-1)if(n<a)for(let l=0;l<=n;l+=1)o(l,l);else for(let l=n-a+1;l<=n;l+=1)o((l+a)%a,l)}}},{}],9:[function(e,t,r){t.exports=function(){return`/* This file automatically generated by jsonToless() and LESS. */
.apg-mono {
  font-family: monospace;
}
.apg-active {
  font-weight: bold;
  color: #000000;
}
.apg-match {
  font-weight: bold;
  color: #264BFF;
}
.apg-empty {
  font-weight: bold;
  color: #0fbd0f;
}
.apg-nomatch {
  font-weight: bold;
  color: #FF4000;
}
.apg-lh-match {
  font-weight: bold;
  color: #1A97BA;
}
.apg-lb-match {
  font-weight: bold;
  color: #5F1687;
}
.apg-remainder {
  font-weight: bold;
  color: #999999;
}
.apg-ctrl-char {
  font-weight: bolder;
  font-style: italic;
  font-size: 0.6em;
}
.apg-line-end {
  font-weight: bold;
  color: #000000;
}
.apg-error {
  font-weight: bold;
  color: #FF4000;
}
.apg-phrase {
  color: #000000;
  background-color: #8caae6;
}
.apg-empty-phrase {
  color: #0fbd0f;
}
table.apg-state {
  font-family: monospace;
  margin-top: 5px;
  font-size: 11px;
  line-height: 130%;
  text-align: left;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-state th,
table.apg-state td {
  text-align: left;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-state th:nth-last-child(2),
table.apg-state td:nth-last-child(2) {
  text-align: right;
}
table.apg-state caption {
  font-size: 125%;
  line-height: 130%;
  font-weight: bold;
  text-align: left;
}
table.apg-stats {
  font-family: monospace;
  margin-top: 5px;
  font-size: 11px;
  line-height: 130%;
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-stats th,
table.apg-stats td {
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-stats caption {
  font-size: 125%;
  line-height: 130%;
  font-weight: bold;
  text-align: left;
}
table.apg-trace {
  font-family: monospace;
  margin-top: 5px;
  font-size: 11px;
  line-height: 130%;
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-trace caption {
  font-size: 125%;
  line-height: 130%;
  font-weight: bold;
  text-align: left;
}
table.apg-trace th,
table.apg-trace td {
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-trace th:last-child,
table.apg-trace th:nth-last-child(2),
table.apg-trace td:last-child,
table.apg-trace td:nth-last-child(2) {
  text-align: left;
}
table.apg-grammar {
  font-family: monospace;
  margin-top: 5px;
  font-size: 11px;
  line-height: 130%;
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-grammar caption {
  font-size: 125%;
  line-height: 130%;
  font-weight: bold;
  text-align: left;
}
table.apg-grammar th,
table.apg-grammar td {
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-grammar th:last-child,
table.apg-grammar td:last-child {
  text-align: left;
}
table.apg-rules {
  font-family: monospace;
  margin-top: 5px;
  font-size: 11px;
  line-height: 130%;
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-rules caption {
  font-size: 125%;
  line-height: 130%;
  font-weight: bold;
  text-align: left;
}
table.apg-rules th,
table.apg-rules td {
  text-align: right;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-rules a {
  color: #003399 !important;
}
table.apg-rules a:hover {
  color: #8caae6 !important;
}
table.apg-attrs {
  font-family: monospace;
  margin-top: 5px;
  font-size: 11px;
  line-height: 130%;
  text-align: center;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-attrs caption {
  font-size: 125%;
  line-height: 130%;
  font-weight: bold;
  text-align: left;
}
table.apg-attrs th,
table.apg-attrs td {
  text-align: center;
  border: 1px solid black;
  border-collapse: collapse;
}
table.apg-attrs th:nth-child(1),
table.apg-attrs th:nth-child(2),
table.apg-attrs th:nth-child(3) {
  text-align: right;
}
table.apg-attrs td:nth-child(1),
table.apg-attrs td:nth-child(2),
table.apg-attrs td:nth-child(3) {
  text-align: right;
}
table.apg-attrs a {
  color: #003399 !important;
}
table.apg-attrs a:hover {
  color: #8caae6 !important;
}
`}},{}],10:[function(e,t,r){t.exports={ALT:1,CAT:2,REP:3,RNM:4,TRG:5,TBS:6,TLS:7,UDT:11,AND:12,NOT:13,BKR:14,BKA:15,BKN:16,ABG:17,AEN:18,ACTIVE:100,MATCH:101,EMPTY:102,NOMATCH:103,SEM_PRE:200,SEM_POST:201,SEM_OK:300,SEM_SKIP:301,ATTR_N:400,ATTR_R:401,ATTR_MR:402,LOOKAROUND_NONE:500,LOOKAROUND_AHEAD:501,LOOKAROUND_BEHIND:502,BKR_MODE_UM:601,BKR_MODE_PM:602,BKR_MODE_CS:603,BKR_MODE_CI:604}},{}],11:[function(e,t,r){t.exports={ast:e("./ast"),circular:e("./circular-buffer"),ids:e("./identifiers"),parser:e("./parser"),stats:e("./stats"),trace:e("./trace"),utils:e("./utilities"),emitcss:e("./emitcss"),style:e("./style")}},{"./ast":7,"./circular-buffer":8,"./emitcss":9,"./identifiers":10,"./parser":12,"./stats":13,"./style":14,"./trace":15,"./utilities":16}],12:[function(e,t,r){t.exports=function(){let n=e("./identifiers"),a=e("./utilities"),o=this,l;this.ast=null,this.stats=null,this.trace=null,this.callbacks=[];let f,m,u,v,b=null,S=null,_=0,g=0,A=0,y=null,k=null,I=null,B=null,E=null,D=0,P=1/0,ce=1/0,Z=function(M,i,c){let h="parser.js: evaluateRule(): ";if(M>=I.length)throw new Error(`${h}rule index: ${M} out of range`);if(i>=u)throw new Error(`${h}phrase index: ${i} out of range`);let{length:T}=b;b.push({type:n.RNM,index:M}),l(T,i,c),b.pop()},ie=function(M,i,c){let h="parser.js: evaluateUdt(): ";if(M>=B.length)throw new Error(`${h}udt index: ${M} out of range`);if(i>=u)throw new Error(`${h}phrase index: ${i} out of range`);let{length:T}=b;b.push({type:n.UDT,empty:B[M].empty,index:M}),l(T,i,c),b.pop()},he=function(){_=0,g=0,A=0,D=0,v=[{lookAround:n.LOOKAROUND_NONE,anchor:0,charsEnd:0,charsLength:0}],I=null,B=null,S=null,f=0,m=0,u=0,y=null,k=null,E=null,b=null},J=function(){let M=[];this.push=function(){M.push(function(){let i=M[M.length-1],c={};for(let h in i)c[h]=i[h];return c}())},this.pop=function(i){let c=i;if(c||(c=M.length-1),c<1||c>M.length)throw new Error(`parser.js: backRef.pop(): bad length: ${c}`);return M.length=c,M[M.length-1]},this.length=function(){return M.length},this.savePhrase=function(i,c,h){M[M.length-1][i]={phraseIndex:c,phraseLength:h}},this.getPhrase=function(i){return M[M.length-1][i]},function(){let i={};I.forEach(c=>{c.isBkr&&(i[c.lower]=null)}),B.length>0&&B.forEach(c=>{c.isBkr&&(i[c.lower]=null)}),M.push(i)}()},re=function(){let M=this;this.state=n.ACTIVE,this.phraseLength=0,this.ruleIndex=0,this.udtIndex=0,this.lookAround=v[v.length-1],this.uFrame=new J,this.pFrame=new J,this.evaluateRule=Z,this.evaluateUdt=ie,this.refresh=function(){M.state=n.ACTIVE,M.phraseLength=0,M.lookAround=v[v.length-1]}},U=function(){return v[v.length-1]},V=function(){return v.length>1},q=function(M,i,c){let h="parser.js: initializeInputChars(): ",T=M,C=i,R=c;if(T===void 0)throw new Error(`${h}input string is undefined`);if(T===null)throw new Error(`${h}input string is null`);if(typeof T=="string")T=a.stringToChars(T);else if(!Array.isArray(T))throw new Error(`${h}input string is not a string or array`);if(T.length>0&&typeof T[0]!="number")throw new Error(`${h}input string not an array of integers`);if(typeof C!="number")C=0;else if(C=Math.floor(C),C<0||C>T.length)throw new Error(`${h}input beginning index out of range: ${C}`);if(typeof R!="number")R=T.length-C;else if(R=Math.floor(R),R<0||R>T.length-C)throw new Error(`${h}input length out of range: ${R}`);S=T,f=C,m=R,u=f+m};this.setMaxTreeDepth=function(M){if(typeof M!="number")throw new Error(`parser: max tree depth must be integer > 0: ${M}`);if(P=Math.floor(M),P<=0)throw new Error(`parser: max tree depth must be integer > 0: ${M}`)},this.setMaxNodeHits=function(M){if(typeof M!="number")throw new Error(`parser: max node hits must be integer > 0: ${M}`);if(ce=Math.floor(M),ce<=0)throw new Error(`parser: max node hits must be integer > 0: ${M}`)};let G=function(M,i,c){let h;(function(R){let x="parser.js: initializeGrammar(): ";if(!R)throw new Error(`${x}grammar object undefined`);if(R.grammarObject!=="grammarObject")throw new Error(`${x}bad grammar object`);I=R.rules,B=R.udts})(M);let T=function(R){let x="parser.js: initializeStartRule(): ",L=null;if(typeof R=="number"){if(R>=I.length)throw new Error(`${x}start rule index too large: max: ${I.length}: index: ${R}`);L=R}else{if(typeof R!="string")throw new Error(`${x}type of start rule '${typeof R}' not recognized`);{let O=R.toLowerCase();for(let N=0;N<I.length;N+=1)if(O===I[N].lower){L=I[N].index;break}if(L===null)throw new Error(`${x}start rule name '${R}' not recognized`)}}return L}(i);(function(){let R="parser.js: initializeCallbacks(): ",x,L;for(y=[],k=[],x=0;x<I.length;x+=1)y[x]=null;for(x=0;x<B.length;x+=1)k[x]=null;let O=[];for(x=0;x<I.length;x+=1)O.push(I[x].lower);for(x=0;x<B.length;x+=1)O.push(B[x].lower);for(let N in o.callbacks){if(x=O.indexOf(N.toLowerCase()),x<0)throw new Error(`${R}syntax callback '${N}' not a rule or udt name`);if(L=o.callbacks[N],L||(L=null),typeof L!="function"&&L!==null)throw new Error(`${R}syntax callback[${N}] must be function reference or 'false' (false/null/undefined/etc.)`);x<I.length?y[x]=L:k[x-I.length]=L}for(x=0;x<B.length;x+=1)if(k[x]===null)throw new Error(`${R}all UDT callbacks must be defined. UDT callback[${B[x].lower}] not a function reference`)})(),function(){for(;;){if(o.trace===void 0){o.trace=null;break}if(o.trace===null)break;if(o.trace.traceObject!=="traceObject")throw new Error("parser.js: initializeTrace(): trace object not recognized");break}o.trace!==null&&o.trace.init(I,B,S)}(),function(){for(;;){if(o.stats===void 0){o.stats=null;break}if(o.stats===null)break;if(o.stats.statsObject!=="statsObject")throw new Error("parser.js: initializeStats(): stats object not recognized");break}o.stats!==null&&o.stats.init(I,B)}(),function(){for(;;){if(o.ast===void 0){o.ast=null;break}if(o.ast===null)break;if(o.ast.astObject!=="astObject")throw new Error("parser.js: initializeAst(): ast object not recognized");break}o.ast!==null&&o.ast.init(I,B,S)}();let C=new re;switch(c!=null&&(E=c),b=[{type:n.RNM,index:T}],l(0,f,C),b=null,C.state){case n.ACTIVE:throw new Error("parser.js: parse(): final state should never be 'ACTIVE'");case n.NOMATCH:h=!1;break;case n.EMPTY:case n.MATCH:h=C.phraseLength===m;break;default:throw new Error("unrecognized state")}return{success:h,state:C.state,length:m,matched:C.phraseLength,maxMatched:D,maxTreeDepth:g,nodeHits:A,inputLength:S.length,subBegin:f,subEnd:u,subLength:m}};this.parseSubstring=function(M,i,c,h,T,C){return he(),q(c,h,T),G(M,i,C)},this.parse=function(M,i,c,h){return he(),q(c,0,c.length),G(M,i,h)};let W=function(M,i,c){let h=b[M];for(let T=0;T<h.children.length&&(l(h.children[T],i,c),c.state===n.NOMATCH);T+=1);},z=function(M,i,c,h){if(i.phraseLength>c){let T=`parser.js: opRNM(${M.name}): callback function error: `;throw T+=`sysData.phraseLength: ${i.phraseLength}`,T+=` must be <= remaining chars: ${c}`,new Error(T)}switch(i.state){case n.ACTIVE:if(h!==!0)throw new Error(`parser.js: opRNM(${M.name}): callback function return error. ACTIVE state not allowed.`);break;case n.EMPTY:i.phraseLength=0;break;case n.MATCH:i.phraseLength===0&&(i.state=n.EMPTY);break;case n.NOMATCH:i.phraseLength=0;break;default:throw new Error(`parser.js: opRNM(${M.name}): callback function return error. Unrecognized return state: ${i.state}`)}},oe=function(M,i,c){let h,T,C,R,x,L,O=b[M],N=I[O.index],H=y[N.index],me=!V();if(me&&(T=o.ast&&o.ast.ruleDefined(O.index),T&&(h=o.ast.getLength(),o.ast.down(O.index,I[O.index].name)),R=c.uFrame.length(),x=c.pFrame.length(),c.uFrame.push(),c.pFrame.push(),L=c.pFrame,c.pFrame=new J),H===null)C=b,b=N.opcodes,l(0,i,c),b=C;else{let fe=u-i;c.ruleIndex=N.index,H(c,S,i,E),z(N,c,fe,!0),c.state===n.ACTIVE&&(C=b,b=N.opcodes,l(0,i,c),b=C,c.ruleIndex=N.index,H(c,S,i,E),z(N,c,fe,!1))}me&&(T&&(c.state===n.NOMATCH?o.ast.setLength(h):o.ast.up(O.index,N.name,i,c.phraseLength)),c.pFrame=L,c.state===n.NOMATCH?(c.uFrame.pop(R),c.pFrame.pop(x)):N.isBkr&&(c.pFrame.savePhrase(N.lower,i,c.phraseLength),c.uFrame.savePhrase(N.lower,i,c.phraseLength)))},K=function(M,i,c){let h,T,C,R,x,L,O=b[M],N=B[O.index];c.UdtIndex=N.index;let H=!V();H&&(C=o.ast&&o.ast.udtDefined(O.index),C&&(T=I.length+O.index,h=o.ast.getLength(),o.ast.down(T,N.name)),R=c.uFrame.length(),x=c.pFrame.length(),c.uFrame.push(),c.pFrame.push(),L=c.pFrame,c.pFrame=new J);let me=u-i;k[O.index](c,S,i,E),function(fe,Ae,ve){if(Ae.phraseLength>ve){let Te=`parser.js: opUDT(${fe.name}): callback function error: `;throw Te+=`sysData.phraseLength: ${Ae.phraseLength}`,Te+=` must be <= remaining chars: ${ve}`,new Error(Te)}switch(Ae.state){case n.ACTIVE:throw new Error(`parser.js: opUDT(${fe.name}): callback function return error. ACTIVE state not allowed.`);case n.EMPTY:if(fe.empty===!1)throw new Error(`parser.js: opUDT(${fe.name}): callback function return error. May not return EMPTY.`);Ae.phraseLength=0;break;case n.MATCH:if(Ae.phraseLength===0){if(fe.empty===!1)throw new Error(`parser.js: opUDT(${fe.name}): callback function return error. May not return EMPTY.`);Ae.state=n.EMPTY}break;case n.NOMATCH:Ae.phraseLength=0;break;default:throw new Error(`parser.js: opUDT(${fe.name}): callback function return error. Unrecognized return state: ${Ae.state}`)}}(N,c,me),H&&(C&&(c.state===n.NOMATCH?o.ast.setLength(h):o.ast.up(T,N.name,i,c.phraseLength)),c.pFrame=L,c.state===n.NOMATCH?(c.uFrame.pop(R),c.pFrame.pop(x)):N.isBkr&&(c.pFrame.savePhrase(N.lower,i,c.phraseLength),c.uFrame.savePhrase(N.lower,i,c.phraseLength)))},ge=function(M,i,c){v.push({lookAround:n.LOOKAROUND_AHEAD,anchor:i,charsEnd:u,charsLength:m}),u=S.length,m=S.length-f,l(M+1,i,c);let h=v.pop();switch(u=h.charsEnd,m=h.charsLength,c.phraseLength=0,c.state){case n.EMPTY:case n.MATCH:c.state=n.EMPTY;break;case n.NOMATCH:c.state=n.NOMATCH;break;default:throw new Error(`opAND: invalid state ${c.state}`)}},X=function(M,i,c){v.push({lookAround:n.LOOKAROUND_AHEAD,anchor:i,charsEnd:u,charsLength:m}),u=S.length,m=S.length-f,l(M+1,i,c);let h=v.pop();switch(u=h.charsEnd,m=h.charsLength,c.phraseLength=0,c.state){case n.EMPTY:case n.MATCH:c.state=n.NOMATCH;break;case n.NOMATCH:c.state=n.EMPTY;break;default:throw new Error(`opNOT: invalid state ${c.state}`)}},pe=function(M,i,c){c.state=n.NOMATCH,c.phraseLength=0,c.state=i===0?n.EMPTY:n.NOMATCH},ee=function(M,i,c){c.state=n.NOMATCH,c.phraseLength=0,c.state=i===S.length?n.EMPTY:n.NOMATCH},te=function(M,i,c){switch(v.push({lookAround:n.LOOKAROUND_BEHIND,anchor:i}),l(M+1,i,c),v.pop(),c.phraseLength=0,c.state){case n.EMPTY:case n.MATCH:c.state=n.EMPTY;break;case n.NOMATCH:c.state=n.NOMATCH;break;default:throw new Error(`opBKA: invalid state ${c.state}`)}},ae=function(M,i,c){switch(v.push({lookAround:n.LOOKAROUND_BEHIND,anchor:i}),l(M+1,i,c),v.pop(),c.phraseLength=0,c.state){case n.EMPTY:case n.MATCH:c.state=n.NOMATCH;break;case n.NOMATCH:c.state=n.EMPTY;break;default:throw new Error(`opBKN: invalid state ${c.state}`)}};l=function(M,i,c){let h=!0,T=b[M];if(A+=1,A>ce)throw new Error(`parser: maximum number of node hits exceeded: ${ce}`);if(_+=1,_>g&&(g=_,g>P))throw new Error(`parser: maximum parse tree depth exceeded: ${P}`);if(c.refresh(),o.trace!==null){let C=U();o.trace.down(T,c.state,i,c.phraseLength,C.anchor,C.lookAround)}if(v[v.length-1].lookAround===n.LOOKAROUND_BEHIND)switch(T.type){case n.ALT:W(M,i,c);break;case n.CAT:(function(C,R,x){let L,O,N,H,me=b[C],fe=x.uFrame.length(),Ae=x.pFrame.length();o.ast&&(O=o.ast.getLength()),L=!0,N=R,H=0;for(let ve=me.children.length-1;ve>=0;ve-=1)if(l(me.children[ve],N,x),N-=x.phraseLength,H+=x.phraseLength,x.state===n.NOMATCH){L=!1;break}L?(x.state=H===0?n.EMPTY:n.MATCH,x.phraseLength=H):(x.state=n.NOMATCH,x.phraseLength=0,x.uFrame.pop(fe),x.pFrame.pop(Ae),o.ast&&o.ast.setLength(O))})(M,i,c);break;case n.REP:(function(C,R,x){let L,O,N,H,me=b[C];O=R,N=0,H=0;let fe=x.uFrame.length(),Ae=x.pFrame.length();for(o.ast&&(L=o.ast.getLength());!(O<=0)&&(l(C+1,O,x),x.state!==n.NOMATCH)&&x.state!==n.EMPTY&&(H+=1,N+=x.phraseLength,O-=x.phraseLength,H!==me.max););x.state===n.EMPTY||H>=me.min?(x.state=N===0?n.EMPTY:n.MATCH,x.phraseLength=N):(x.state=n.NOMATCH,x.phraseLength=0,x.uFrame.pop(fe),x.pFrame.pop(Ae),o.ast&&o.ast.setLength(L))})(M,i,c);break;case n.RNM:oe(M,i,c);break;case n.UDT:K(M,i,c);break;case n.AND:ge(M,i,c);break;case n.NOT:X(M,i,c);break;case n.TRG:(function(C,R,x){let L=b[C];if(x.state=n.NOMATCH,x.phraseLength=0,R>0){let O=S[R-1];L.min<=O&&O<=L.max&&(x.state=n.MATCH,x.phraseLength=1)}})(M,i,c);break;case n.TBS:(function(C,R,x){let L,O=b[C];x.state=n.NOMATCH;let N=O.string.length,H=R-N;if(H>=0){for(L=0;L<N;L+=1)if(S[H+L]!==O.string[L])return;x.state=n.MATCH,x.phraseLength=N}})(M,i,c);break;case n.TLS:(function(C,R,x){let L,O=b[C];x.state=n.NOMATCH;let N=O.string.length;if(N===0)return void(x.state=n.EMPTY);let H=R-N;if(H>=0){for(let me=0;me<N;me+=1)if(L=S[H+me],L>=65&&L<=90&&(L+=32),L!==O.string[me])return;x.state=n.MATCH,x.phraseLength=N}})(M,i,c);break;case n.BKR:(function(C,R,x){let L,O,N,H,me=b[C];x.state=n.NOMATCH,x.phraseLength=0,H=me.index<I.length?I[me.index].lower:B[me.index-I.length].lower;let fe=me.bkrMode===n.BKR_MODE_PM?x.pFrame.getPhrase(H):x.uFrame.getPhrase(H),Ae=me.bkrCase===n.BKR_MODE_CI;if(fe===null)return;let ve=fe.phraseIndex,Te=fe.phraseLength;if(Te===0)return x.state=n.EMPTY,void(x.phraseLength=0);let _r=R-Te;if(_r>=0){if(Ae){for(L=0;L<Te;L+=1)if(O=S[_r+L],N=S[ve+L],O>=65&&O<=90&&(O+=32),N>=65&&N<=90&&(N+=32),O!==N)return;x.state=n.MATCH,x.phraseLength=Te}else for(L=0;L<Te;L+=1)if(O=S[_r+L],N=S[ve+L],O!==N)return;x.state=n.MATCH,x.phraseLength=Te}})(M,i,c);break;case n.BKA:te(M,i,c);break;case n.BKN:ae(M,i,c);break;case n.ABG:pe(0,i,c);break;case n.AEN:ee(0,i,c);break;default:h=!1}else switch(T.type){case n.ALT:W(M,i,c);break;case n.CAT:(function(C,R,x){let L,O,N,H,me=b[C],fe=x.uFrame.length(),Ae=x.pFrame.length();o.ast&&(O=o.ast.getLength()),L=!0,N=R,H=0;for(let ve=0;ve<me.children.length;ve+=1){if(l(me.children[ve],N,x),x.state===n.NOMATCH){L=!1;break}N+=x.phraseLength,H+=x.phraseLength}L?(x.state=H===0?n.EMPTY:n.MATCH,x.phraseLength=H):(x.state=n.NOMATCH,x.phraseLength=0,x.uFrame.pop(fe),x.pFrame.pop(Ae),o.ast&&o.ast.setLength(O))})(M,i,c);break;case n.REP:(function(C,R,x){let L,O,N,H,me=b[C];O=R,N=0,H=0;let fe=x.uFrame.length(),Ae=x.pFrame.length();for(o.ast&&(L=o.ast.getLength());!(O>=u)&&(l(C+1,O,x),x.state!==n.NOMATCH)&&x.state!==n.EMPTY&&(H+=1,N+=x.phraseLength,O+=x.phraseLength,H!==me.max););x.state===n.EMPTY||H>=me.min?(x.state=N===0?n.EMPTY:n.MATCH,x.phraseLength=N):(x.state=n.NOMATCH,x.phraseLength=0,x.uFrame.pop(fe),x.pFrame.pop(Ae),o.ast&&o.ast.setLength(L))})(M,i,c);break;case n.RNM:oe(M,i,c);break;case n.UDT:K(M,i,c);break;case n.AND:ge(M,i,c);break;case n.NOT:X(M,i,c);break;case n.TRG:(function(C,R,x){let L=b[C];x.state=n.NOMATCH,R<u&&L.min<=S[R]&&S[R]<=L.max&&(x.state=n.MATCH,x.phraseLength=1)})(M,i,c);break;case n.TBS:(function(C,R,x){let L,O=b[C],N=O.string.length;if(x.state=n.NOMATCH,R+N<=u){for(L=0;L<N;L+=1)if(S[R+L]!==O.string[L])return;x.state=n.MATCH,x.phraseLength=N}})(M,i,c);break;case n.TLS:(function(C,R,x){let L,O,N=b[C];x.state=n.NOMATCH;let H=N.string.length;if(H!==0){if(R+H<=u){for(L=0;L<H;L+=1)if(O=S[R+L],O>=65&&O<=90&&(O+=32),O!==N.string[L])return;x.state=n.MATCH,x.phraseLength=H}}else x.state=n.EMPTY})(M,i,c);break;case n.BKR:(function(C,R,x){let L,O,N,H,me=b[C];x.state=n.NOMATCH,H=me.index<I.length?I[me.index].lower:B[me.index-I.length].lower;let fe=me.bkrMode===n.BKR_MODE_PM?x.pFrame.getPhrase(H):x.uFrame.getPhrase(H),Ae=me.bkrCase===n.BKR_MODE_CI;if(fe===null)return;let ve=fe.phraseIndex,Te=fe.phraseLength;if(Te!==0){if(R+Te<=u){if(Ae){for(L=0;L<Te;L+=1)if(O=S[R+L],N=S[ve+L],O>=65&&O<=90&&(O+=32),N>=65&&N<=90&&(N+=32),O!==N)return;x.state=n.MATCH,x.phraseLength=Te}else for(L=0;L<Te;L+=1)if(O=S[R+L],N=S[ve+L],O!==N)return;x.state=n.MATCH,x.phraseLength=Te}}else x.state=n.EMPTY})(M,i,c);break;case n.BKA:te(M,i,c);break;case n.BKN:ae(M,i,c);break;case n.ABG:pe(0,i,c);break;case n.AEN:ee(0,i,c);break;default:h=!1}if(!V()&&i+c.phraseLength>D&&(D=i+c.phraseLength),o.stats!==null&&o.stats.collect(T,c),o.trace!==null){let C=U();o.trace.up(T,c.state,i,c.phraseLength,C.anchor,C.lookAround)}return _-=1,h}}},{"./identifiers":10,"./utilities":16}],13:[function(e,t,r){t.exports=function(){let n=e("./identifiers"),a=e("./utilities"),o=e("./style"),l=[],f=[],m=[],u,v=[],b=[];this.statsObject="statsObject";let S=function(E,D){return E.lower<D.lower?-1:E.lower>D.lower?1:0},_=function(E,D){return E.total<D.total?1:E.total>D.total?-1:S(E,D)},g=function(E,D){return E.index<D.index?-1:E.index>D.index?1:0},A=function(){this.empty=0,this.match=0,this.nomatch=0,this.total=0},y=function(E,D){switch(E.total+=1,D){case n.EMPTY:E.empty+=1;break;case n.MATCH:E.match+=1;break;case n.NOMATCH:E.nomatch+=1;break;default:throw new Error(`stats.js: collect(): incStat(): unrecognized state: ${D}`)}},k=function(E,D){let P="";return P+="<tr>",P+=`<td class="${o.CLASS_ACTIVE}">${E}</td>`,P+=`<td class="${o.CLASS_EMPTY}">${D.empty}</td>`,P+=`<td class="${o.CLASS_MATCH}">${D.match}</td>`,P+=`<td class="${o.CLASS_NOMATCH}">${D.nomatch}</td>`,P+=`<td class="${o.CLASS_ACTIVE}">${D.total}</td>`,P+=`</tr>
`,P},I=function(){let E="";return E+=k("ALT",m[n.ALT]),E+=k("CAT",m[n.CAT]),E+=k("REP",m[n.REP]),E+=k("RNM",m[n.RNM]),E+=k("TRG",m[n.TRG]),E+=k("TBS",m[n.TBS]),E+=k("TLS",m[n.TLS]),E+=k("UDT",m[n.UDT]),E+=k("AND",m[n.AND]),E+=k("NOT",m[n.NOT]),E+=k("BKR",m[n.BKR]),E+=k("BKA",m[n.BKA]),E+=k("BKN",m[n.BKN]),E+=k("ABG",m[n.ABG]),E+=k("AEN",m[n.AEN]),E+=k("totals",u),E},B=function(){let E="";E+=`<tr><th></th><th></th><th></th><th></th><th></th></tr>
`,E+=`<tr><th>rules</th><th></th><th></th><th></th><th></th></tr>
`;for(let D=0;D<l.length;D+=1)v[D].total>0&&(E+="<tr>",E+=`<td class="${o.CLASS_ACTIVE}">${v[D].name}</td>`,E+=`<td class="${o.CLASS_EMPTY}">${v[D].empty}</td>`,E+=`<td class="${o.CLASS_MATCH}">${v[D].match}</td>`,E+=`<td class="${o.CLASS_NOMATCH}">${v[D].nomatch}</td>`,E+=`<td class="${o.CLASS_ACTIVE}">${v[D].total}</td>`,E+=`</tr>
`);if(f.length>0){E+=`<tr><th></th><th></th><th></th><th></th><th></th></tr>
`,E+=`<tr><th>udts</th><th></th><th></th><th></th><th></th></tr>
`;for(let D=0;D<f.length;D+=1)b[D].total>0&&(E+="<tr>",E+=`<td class="${o.CLASS_ACTIVE}">${b[D].name}</td>`,E+=`<td class="${o.CLASS_EMPTY}">${b[D].empty}</td>`,E+=`<td class="${o.CLASS_MATCH}">${b[D].match}</td>`,E+=`<td class="${o.CLASS_NOMATCH}">${b[D].nomatch}</td>`,E+=`<td class="${o.CLASS_ACTIVE}">${b[D].total}</td>`,E+=`</tr>
`)}return E};this.validate=function(E){let D=!1;return typeof E=="string"&&E==="stats"&&(D=!0),D},this.init=function(E,D){l=E,f=D,function(){m.length=0,u=new A,m[n.ALT]=new A,m[n.CAT]=new A,m[n.REP]=new A,m[n.RNM]=new A,m[n.TRG]=new A,m[n.TBS]=new A,m[n.TLS]=new A,m[n.UDT]=new A,m[n.AND]=new A,m[n.NOT]=new A,m[n.BKR]=new A,m[n.BKA]=new A,m[n.BKN]=new A,m[n.ABG]=new A,m[n.AEN]=new A,v.length=0;for(let P=0;P<l.length;P+=1)v.push({empty:0,match:0,nomatch:0,total:0,name:l[P].name,lower:l[P].lower,index:l[P].index});if(f.length>0){b.length=0;for(let P=0;P<f.length;P+=1)b.push({empty:0,match:0,nomatch:0,total:0,name:f[P].name,lower:f[P].lower,index:f[P].index})}}()},this.collect=function(E,D){y(u,D.state,D.phraseLength),y(m[E.type],D.state,D.phraseLength),E.type===n.RNM&&y(v[E.index],D.state,D.phraseLength),E.type===n.UDT&&y(b[E.index],D.state,D.phraseLength)},this.toHtml=function(E,D){let P="";for(P+=`<table class="${o.CLASS_STATS}">
`,typeof D=="string"&&(P+=`<caption>${D}</caption>
`),P+=`<tr><th class="${o.CLASS_ACTIVE}">ops</th>
`,P+=`<th class="${o.CLASS_EMPTY}">EMPTY</th>
`,P+=`<th class="${o.CLASS_MATCH}">MATCH</th>
`,P+=`<th class="${o.CLASS_NOMATCH}">NOMATCH</th>
`,P+=`<th class="${o.CLASS_ACTIVE}">totals</th></tr>
`;;){if(E===void 0){P+=I();break}if(E===null){P+=I();break}if(E==="ops"){P+=I();break}if(E==="index"){v.sort(g),b.length>0&&b.sort(g),P+=I(),P+=B();break}if(E==="hits"){v.sort(_),b.length>0&&b.sort(g),P+=I(),P+=B();break}if(E==="alpha"){v.sort(S),b.length>0&&b.sort(S),P+=I(),P+=B();break}break}return P+=`</table>
`,P},this.toHtmlPage=function(E,D,P){return a.htmlToPage(this.toHtml(E,D),P)}}},{"./identifiers":10,"./style":14,"./utilities":16}],14:[function(e,t,r){t.exports={CLASS_MONOSPACE:"apg-mono",CLASS_ACTIVE:"apg-active",CLASS_EMPTY:"apg-empty",CLASS_MATCH:"apg-match",CLASS_NOMATCH:"apg-nomatch",CLASS_LOOKAHEAD:"apg-lh-match",CLASS_LOOKBEHIND:"apg-lb-match",CLASS_REMAINDER:"apg-remainder",CLASS_CTRLCHAR:"apg-ctrl-char",CLASS_LINEEND:"apg-line-end",CLASS_ERROR:"apg-error",CLASS_PHRASE:"apg-phrase",CLASS_EMPTYPHRASE:"apg-empty-phrase",CLASS_STATE:"apg-state",CLASS_STATS:"apg-stats",CLASS_TRACE:"apg-trace",CLASS_GRAMMAR:"apg-grammar",CLASS_RULES:"apg-rules",CLASS_RULESLINK:"apg-rules-link",CLASS_ATTRIBUTES:"apg-attrs"}},{}],15:[function(e,t,r){t.exports=function(){let n=e("./utilities"),a=e("./style"),o=new(e("./circular-buffer")),l=e("./identifiers"),f=this,m=16,u=80,v=[],b=5e3,S=-1,_=0,g=0,A=[],y=null,k=null,I=null,B=[],E=[],D=`<span class="${a.CLASS_LINEEND}">&bull;</span>`,P=`<span class="${a.CLASS_LINEEND}">&hellip;</span>`,ce=`<span class="${a.CLASS_EMPTY}">&#120634;</span>`;this.traceObject="traceObject",this.filter={operators:[],rules:[]},this.setMaxRecords=function(U,V){S=-1,typeof U=="number"&&U>0?(b=Math.ceil(U),typeof V=="number"&&(S=Math.floor(V),S<0&&(S=-1))):b=0},this.getMaxRecords=function(){return b},this.getLastRecord=function(){return S},this.init=function(U,V,q){v.length=0,A.length=0,_=0,g=0,y=q,k=U,I=V,function(){let G=function(z){B[l.ALT]=z,B[l.CAT]=z,B[l.REP]=z,B[l.TLS]=z,B[l.TBS]=z,B[l.TRG]=z,B[l.AND]=z,B[l.NOT]=z,B[l.BKR]=z,B[l.BKA]=z,B[l.BKN]=z,B[l.ABG]=z,B[l.AEN]=z},W=0;for(let z in f.filter.operators)W+=1;if(W!==0){for(let z in f.filter.operators){let oe=z.toUpperCase();if(oe==="<ALL>")return void G(!0);if(oe==="<NONE>")return void G(!1)}G(!1);for(let z in f.filter.operators){let oe=z.toUpperCase();if(oe==="ALT")B[l.ALT]=f.filter.operators[z]===!0;else if(oe==="CAT")B[l.CAT]=f.filter.operators[z]===!0;else if(oe==="REP")B[l.REP]=f.filter.operators[z]===!0;else if(oe==="AND")B[l.AND]=f.filter.operators[z]===!0;else if(oe==="NOT")B[l.NOT]=f.filter.operators[z]===!0;else if(oe==="TLS")B[l.TLS]=f.filter.operators[z]===!0;else if(oe==="TBS")B[l.TBS]=f.filter.operators[z]===!0;else if(oe==="TRG")B[l.TRG]=f.filter.operators[z]===!0;else if(oe==="BKR")B[l.BKR]=f.filter.operators[z]===!0;else if(oe==="BKA")B[l.BKA]=f.filter.operators[z]===!0;else if(oe==="BKN")B[l.BKN]=f.filter.operators[z]===!0;else if(oe==="ABG")B[l.ABG]=f.filter.operators[z]===!0;else{if(oe!=="AEN")throw new Error(`trace.js: initOpratorFilter: '${z}' not a valid operator name. Must be <all>, <none>, alt, cat, rep, tls, tbs, trg, and, not, bkr, bka or bkn`);B[l.AEN]=f.filter.operators[z]===!0}}}else G(!1)}(),function(){let G=function(K){B[l.RNM]=K,B[l.UDT]=K;let ge=k.length+I.length;E.length=0;for(let X=0;X<ge;X+=1)E.push(K)},W,z,oe=[];for(z=0;z<k.length;z+=1)oe.push(k[z].lower);for(z=0;z<I.length;z+=1)oe.push(I[z].lower);E.length=0,W=0;for(let K in f.filter.rules)W+=1;if(W!==0){for(let K in f.filter.rules){let ge=K.toLowerCase();if(ge==="<all>")return void G(!0);if(ge==="<none>")return void G(!1)}G(!1),B[l.RNM]=!0,B[l.UDT]=!0;for(let K in f.filter.rules){let ge=K.toLowerCase();if(z=oe.indexOf(ge),z<0)throw new Error(`trace.js: initRuleFilter: '${K}' not a valid rule or udt name`);E[z]=f.filter.rules[K]===!0}}else G(!0)}(),o.init(b)};let Z=function(U){let V=!1;return V=U.type===l.RNM?!(!B[U.type]||!E[U.index]):U.type===l.UDT?!(!B[U.type]||!E[k.length+U.index]):B[U.type],V},ie=function(U){return S===-1||U<=S};this.down=function(U,V,q,G,W,z){ie(_)&&Z(U)&&(A.push(_),v[o.increment()]={dirUp:!1,depth:g,thisLine:_,thatLine:void 0,opcode:U,state:V,phraseIndex:q,phraseLength:G,lookAnchor:W,lookAround:z},_+=1,g+=1)},this.up=function(U,V,q,G,W,z){if(ie(_)&&Z(U)){let oe=_,K=A.pop(),ge=o.getListIndex(K);ge!==-1&&(v[ge].thatLine=oe),g-=1,v[o.increment()]={dirUp:!0,depth:g,thisLine:oe,thatLine:K,opcode:U,state:V,phraseIndex:q,phraseLength:G,lookAnchor:W,lookAround:z},_+=1}},this.toTree=function(U){let V=function(){function q(R,x){let L,O,N;if(x)switch(R.op={id:x.type,name:n.opcodeToString(x.type)},R.opData=void 0,x.type){case l.RNM:R.opData=k[x.index].name;break;case l.UDT:R.opData=I[x.index].name;break;case l.BKR:L=x.index<k.length?k[x.index].name:I[x.index-k.length].name,O=x.bkrCase===l.BKR_MODE_CI?"%i":"%s",N=x.bkrMode===l.BKR_MODE_UM?"%u":"%p",R.opData=`\\\\${O}${N}${L}`;break;case l.TLS:R.opData=[];for(let H=0;H<x.string.length;H+=1)R.opData.push(x.string[H]);break;case l.TBS:R.opData=[];for(let H=0;H<x.string.length;H+=1)R.opData.push(x.string[H]);break;case l.TRG:case l.REP:R.opData=[x.min,x.max];break;default:throw new Error("unrecognized opcode")}else R.op={id:void 0,name:void 0},R.opData=void 0}function G(R,x,L){return R===l.MATCH?{index:x,length:L}:R===l.NOMATCH||R===l.EMPTY?{index:x,length:0}:null}let W=-1;function z(R,x,L){let O={id:W++,branch:-1,parent:R,up:!1,down:!1,depth:L,children:[]};return x?(O.down=!0,O.state={id:x.state,name:n.stateToString(x.state)},O.phrase=null,q(O,x.opcode)):(O.state={id:void 0,name:void 0},O.phrase=G(),q(O,void 0)),O}function oe(R,x){x&&(R.up=!0,R.state={id:x.state,name:n.stateToString(x.state)},R.phrase=G(x.state,x.phraseIndex,x.phraseLength),R.down||q(R,x.opcode))}let K=0,ge=-1,X=1,pe=[],ee,te,ae,M,i=!0,c=z(null,null,-1);for(pe.push(c),te=c,o.forEach(R=>{if(M=v[R],i&&(i=!1,M.depth>0)){let x=M.dirUp?M.depth+1:M.depth;for(let L=0;L<x;L+=1)ae=te,te=z(te,null,L),pe.push(te),ae.children.push(te)}M.dirUp?(te=pe.pop(),oe(te,M),te=pe[pe.length-1]):(ae=te,te=z(te,M,M.depth),pe.push(te),ae.children.push(te))});pe.length>1;)te=pe.pop(),oe(te,null);if(c.children.length===0)throw new Error("trace.toTree(): parse tree has no nodes");if(pe.length===0)throw new Error("trace.toTree(): integrity check: dummy root node disappeared?");ee=c.children[0];let h=ee;for(;ee&&!ee.down&&!ee.up;)h=ee,ee=ee.children[0];ee=h,ee.leftMost=!0,ee.rightMost=!0,function R(x){if(ge+=1,x.branch=X,ge>g&&(g=ge),x.children.length===0)K+=1;else for(let L=0;L<x.children.length;L+=1)L>0&&(X+=1),x.children[L].leftMost=!1,x.children[L].rightMost=!1,x.leftMost&&(x.children[L].leftMost=L===0),x.rightMost&&(x.children[L].rightMost=L===x.children.length-1),R(x.children[L]);ge-=1}(ee),ee.branch=0;let T={string:[]};for(let R=0;R<y.length;R+=1)T.string[R]=y[R];T.rules=[];for(let R=0;R<k.length;R+=1)T.rules[R]=k[R].name;T.udts=[];for(let R=0;R<I.length;R+=1)T.udts[R]=I[R].name;let C;return T.id={},T.id.ALT={id:l.ALT,name:"ALT"},T.id.CAT={id:l.CAT,name:"CAT"},T.id.REP={id:l.REP,name:"REP"},T.id.RNM={id:l.RNM,name:"RNM"},T.id.TLS={id:l.TLS,name:"TLS"},T.id.TBS={id:l.TBS,name:"TBS"},T.id.TRG={id:l.TRG,name:"TRG"},T.id.UDT={id:l.UDT,name:"UDT"},T.id.AND={id:l.AND,name:"AND"},T.id.NOT={id:l.NOT,name:"NOT"},T.id.BKR={id:l.BKR,name:"BKR"},T.id.BKA={id:l.BKA,name:"BKA"},T.id.BKN={id:l.BKN,name:"BKN"},T.id.ABG={id:l.ABG,name:"ABG"},T.id.AEN={id:l.AEN,name:"AEN"},T.id.ACTIVE={id:l.ACTIVE,name:"ACTIVE"},T.id.MATCH={id:l.MATCH,name:"MATCH"},T.id.EMPTY={id:l.EMPTY,name:"EMPTY"},T.id.NOMATCH={id:l.NOMATCH,name:"NOMATCH"},T.treeDepth=g,T.leafNodes=K,C=ee.down?ee.up?"none":"right":ee.up?"left":"both",T.branchesIncomplete=C,T.tree=function R(x,L){let O,N={};if(N.id=x.id,N.branch=x.branch,N.leftMost=x.leftMost,N.rightMost=x.rightMost,O=x.state.name?x.state.name:"ACTIVE",N.state={id:x.state.id,name:O},O=x.op.name?x.op.name:"?",N.op={id:x.op.id,name:O},typeof x.opData=="string")N.opData=x.opData;else if(Array.isArray(x.opData)){N.opData=[];for(let H=0;H<x.opData.length;H+=1)N.opData[H]=x.opData[H]}else N.opData=void 0;x.phrase?N.phrase={index:x.phrase.index,length:x.phrase.length}:N.phrase=null,N.depth=x.depth,N.children=[];for(let H=0;H<x.children.length;H+=1)x.children.length,N.children[H]=R(x.children[H],L);return N}(ee,ee.depth),T}();return U?JSON.stringify(V):V},this.toHtmlPage=function(U,V,q){return n.htmlToPage(this.toHtml(U,V),q)},this.indent=function(U){let V="";for(let q=0;q<U;q+=1)V+=".";return V};let he=function(U,V,q,G,W){if(G===0)return"";let z="",oe=W?",":"";switch(U){case m:z=oe+n.charsToHex(V,q,G);break;case 10:if(W)return`,${n.charsToDec(V,q,G)}`;z=oe+n.charsToDec(V,q,G);break;case 32:z=n.charsToUnicode(V,q,G);break;case 8:default:z=n.charsToAsciiHtml(V,q,G)}return z},J=function(U,V,q,G,W,z){let oe,K,ge,X,pe="",ee=D,te=`<span class="${a.CLASS_REMAINDER}">`,ae="</span>",M=!1;switch(q){case l.EMPTY:pe+=ce;case l.NOMATCH:case l.ACTIVE:oe=G,K=0,ge=G,X=V.length-ge;break;case l.MATCH:oe=G,K=W,ge=G+K,X=V.length-ge;break;default:throw new Error("unrecognized state")}return ee=D,K>u?(K=u,ee=P,X=0):K+X>u&&(ee=P,X=u-K),K>0&&(pe+=z,pe+=he(U,V,oe,K,M),pe+=ae,M=!0),X>0&&(pe+=te,pe+=he(U,V,ge,X,M),pe+=ae),pe+ee},re=function(U){if(k===null)return"";let V,q,G,W,z,oe,K="";return K+="<tr><th>(a)</th><th>(b)</th><th>(c)</th><th>(d)</th><th>(e)</th><th>(f)</th>",K+=`<th>operator</th><th>phrase</th></tr>
`,o.forEach(ge=>{let X=v[ge];switch(V=X.thisLine,q=X.thatLine!==void 0?X.thatLine:"--",G=!1,W=!1,z=!1,X.lookAround===l.LOOKAROUND_AHEAD&&(G=!0,z=!0,oe=X.lookAnchor),X.opcode.type!==l.AND&&X.opcode.type!==l.NOT||(G=!0,z=!0,oe=X.phraseIndex),X.lookAround===l.LOOKAROUND_BEHIND&&(W=!0,z=!0,oe=X.lookAnchor),X.opcode.type!==l.BKA&&X.opcode.type!==l.BKN||(W=!0,z=!0,oe=X.phraseIndex),K+="<tr>",K+=`<td>${V}</td><td>${q}</td>`,K+=`<td>${X.phraseIndex}</td>`,K+=`<td>${X.phraseLength}</td>`,K+=`<td>${X.depth}</td>`,K+="<td>",X.state){case l.ACTIVE:K+=`<span class="${a.CLASS_ACTIVE}">&darr;&nbsp;</span>`;break;case l.MATCH:K+=`<span class="${a.CLASS_MATCH}">&uarr;M</span>`;break;case l.NOMATCH:K+=`<span class="${a.CLASS_NOMATCH}">&uarr;N</span>`;break;case l.EMPTY:K+=`<span class="${a.CLASS_EMPTY}">&uarr;E</span>`;break;default:K+=`<span class="${a.CLASS_ACTIVE}">--</span>`}if(K+="</td>",K+="<td>",K+=f.indent(X.depth),G?K+=`<span class="${a.CLASS_LOOKAHEAD}">`:W&&(K+=`<span class="${a.CLASS_LOOKBEHIND}">`),K+=n.opcodeToString(X.opcode.type),X.opcode.type===l.RNM&&(K+=`(${k[X.opcode.index].name}) `),X.opcode.type===l.BKR){let pe=X.opcode.bkrCase===l.BKR_MODE_CI?"%i":"%s",ee=X.opcode.bkrMode===l.BKR_MODE_UM?"%u":"%p";K+=`(\\${pe}${ee}${k[X.opcode.index].name}) `}X.opcode.type===l.UDT&&(K+=`(${I[X.opcode.index].name}) `),X.opcode.type===l.TRG&&(K+=`(${function(pe,ee){let te="";if(ee.type===l.TRG)if(pe===m||pe===32){let ae=ee.min.toString(16).toUpperCase();ae.length%2!=0&&(ae=`0${ae}`),te+=pe===m?"%x":"U+",te+=ae,ae=ee.max.toString(16).toUpperCase(),ae.length%2!=0&&(ae=`0${ae}`),te+=`&ndash;${ae}`}else te=`%d${ee.min.toString(10)}&ndash;${ee.max.toString(10)}`;return te}(U,X.opcode)}) `),X.opcode.type===l.TBS&&(K+=`(${function(pe,ee){let te="";if(ee.type===l.TBS){let ae=Math.min(ee.string.length,10);if(pe===m||pe===32){te+=pe===m?"%x":"U+";for(let M=0;M<ae;M+=1){let i;M>0&&(te+="."),i=ee.string[M].toString(16).toUpperCase(),i.length%2!=0&&(i=`0${i}`),te+=i}}else{te="%d";for(let M=0;M<ae;M+=1)M>0&&(te+="."),te+=ee.string[M].toString(10)}ae<ee.string.length&&(te+=P)}return te}(U,X.opcode)}) `),X.opcode.type===l.TLS&&(K+=`(${function(pe,ee){let te="";if(ee.type===l.TLS){let ae=Math.min(ee.string.length,5);if(pe===m||pe===10){let M,i,c;pe===m?(te="%x",c=16):(te="%d",c=10);for(let h=0;h<ae;h+=1)h>0&&(te+="."),i=ee.string[h],i>=97&&i<=122?(M=i-32,te+=`${M.toString(c)}/${i.toString(c)}`.toUpperCase()):i>=65&&i<=90?(M=i,i+=32,te+=`${M.toString(c)}/${i.toString(c)}`.toUpperCase()):te+=i.toString(c).toUpperCase();ae<ee.string.length&&(te+=P)}else{te='"';for(let M=0;M<ae;M+=1)te+=n.asciiChars[ee.string[M]];ae<ee.string.length&&(te+=P),te+='"'}}return te}(U,X.opcode)}) `),X.opcode.type===l.REP&&(K+=`(${function(pe,ee){let te="";if(ee.type===l.REP)if(pe===m){let ae=ee.min.toString(16).toUpperCase();ae.length%2!=0&&(ae=`0${ae}`),te=`x${ae}`,ee.max<1/0?(ae=ee.max.toString(16).toUpperCase(),ae.length%2!=0&&(ae=`0${ae}`)):ae="inf",te+=`&ndash;${ae}`}else te=ee.max<1/0?`${ee.min.toString(10)}&ndash;${ee.max.toString(10)}`:`${ee.min.toString(10)}&ndash;inf`;return te}(U,X.opcode)}) `),z&&(K+="</span>"),K+="</td>",K+="<td>",K+=W?function(pe,ee,te,ae,M,i){let c,h,T,C,R="",x=D,L=`<span class="${a.CLASS_LOOKBEHIND}">`,O=`<span class="${a.CLASS_REMAINDER}">`,N="</span>",H=!1;switch(te){case l.EMPTY:R+=ce;case l.NOMATCH:case l.MATCH:case l.ACTIVE:c=ae-M,h=i-c,T=i,C=ee.length-T;break;default:throw new Error("unrecognized state")}return x=D,h>u?(h=u,x=P,C=0):h+C>u&&(x=P,C=u-h),h>0&&(R+=L,R+=he(pe,ee,c,h,H),R+=N,H=!0),C>0&&(R+=O,R+=he(pe,ee,T,C,H),R+=N),R+x}(U,y,X.state,X.phraseIndex,X.phraseLength,oe):G?function(pe,ee,te,ae,M){let i=`<span class="${a.CLASS_LOOKAHEAD}">`;return J(pe,ee,te,ae,M,i)}(U,y,X.state,X.phraseIndex,X.phraseLength):function(pe,ee,te,ae,M){let i=`<span class="${a.CLASS_MATCH}">`;return J(pe,ee,te,ae,M,i)}(U,y,X.state,X.phraseIndex,X.phraseLength),K+=`</td></tr>
`}),K+="<tr><th>(a)</th><th>(b)</th><th>(c)</th><th>(d)</th><th>(e)</th><th>(f)</th>",K+=`<th>operator</th><th>phrase</th></tr>
`,K+=`</table>
`,K};this.toHtml=function(U,V){let q=8;if(typeof U=="string"&&U.length>=3){let W=U.toLowerCase().slice(0,3);W==="hex"?q=m:W==="dec"?q=10:W==="uni"&&(q=32)}let G="";return G+=function(W,z){let oe;switch(W){case m:oe="hexadecimal";break;case 10:oe="decimal";break;case 8:oe="ASCII";break;case 32:oe="UNICODE";break;default:throw new Error(`trace.js: htmlHeader: unrecognized mode: ${W}`)}let K="";return K+=`<p>display mode: ${oe}</p>
`,K+=`<table class="${a.CLASS_TRACE}">
`,typeof z=="string"&&(K+=`<caption>${z}</caption>`),K}(q,V),G+=re(q),G+=function(){let W="";return W+=`</table>
`,W+=`<p class="${a.CLASS_MONOSPACE}">legend:<br>
`,W+=`(a)&nbsp;-&nbsp;line number<br>
`,W+=`(b)&nbsp;-&nbsp;matching line number<br>
`,W+=`(c)&nbsp;-&nbsp;phrase offset<br>
`,W+=`(d)&nbsp;-&nbsp;phrase length<br>
`,W+=`(e)&nbsp;-&nbsp;tree depth<br>
`,W+=`(f)&nbsp;-&nbsp;operator state<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_ACTIVE}">&darr;</span>&nbsp;&nbsp;phrase opened<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_MATCH}">&uarr;M</span> phrase matched<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_EMPTY}">&uarr;E</span> empty phrase matched<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_NOMATCH}">&uarr;N</span> phrase not matched<br>
`,W+=`operator&nbsp;-&nbsp;ALT, CAT, REP, RNM, TRG, TLS, TBS<sup>&dagger;</sup>, UDT, AND, NOT, BKA, BKN, BKR, ABG, AEN<sup>&Dagger;</sup><br>
`,W+=`phrase&nbsp;&nbsp;&nbsp;-&nbsp;up to 80 characters of the phrase being matched<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_MATCH}">matched characters</span><br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_LOOKAHEAD}">matched characters in look ahead mode</span><br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_LOOKBEHIND}">matched characters in look behind mode</span><br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_REMAINDER}">remainder characters(not yet examined by parser)</span><br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;<span class="${a.CLASS_CTRLCHAR}">control characters, TAB, LF, CR, etc. (ASCII mode only)</span><br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;${ce} empty string<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;${D} end of input string<br>
`,W+=`&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-&nbsp;${P} input string display truncated<br>
`,W+=`</p>
`,W+=`<p class="${a.CLASS_MONOSPACE}">
`,W+=`<sup>&dagger;</sup>original ABNF operators:<br>
`,W+=`ALT - alternation<br>
`,W+=`CAT - concatenation<br>
`,W+=`REP - repetition<br>
`,W+=`RNM - rule name<br>
`,W+=`TRG - terminal range<br>
`,W+=`TLS - terminal literal string (case insensitive)<br>
`,W+=`TBS - terminal binary string (case sensitive)<br>
`,W+=`<br>
`,W+=`<sup>&Dagger;</sup>super set SABNF operators:<br>
`,W+=`UDT - user-defined terminal<br>
`,W+=`AND - positive look ahead<br>
`,W+=`NOT - negative look ahead<br>
`,W+=`BKA - positive look behind<br>
`,W+=`BKN - negative look behind<br>
`,W+=`BKR - back reference<br>
`,W+=`ABG - anchor - begin of input string<br>
`,W+=`AEN - anchor - end of input string<br>
`,W+=`</p>
`,W}(),G}}},{"./circular-buffer":8,"./identifiers":10,"./style":14,"./utilities":16}],16:[function(e,t,r){let n=e("./style"),a=e("../apg-conv-api/converter"),o=e("./emitcss"),l=e("./identifiers"),f=this,m=function(u,v,b){let S,_=v;for(;;){if(u<=0){_=0,S=0;break}if(typeof _!="number"){_=0,S=u;break}if(_>=u){_=u,S=u;break}if(typeof b!="number"){S=u;break}if(S=_+b,S>u){S=u;break}break}return{beg:_,end:S}};r.htmlToPage=function(u,v){let b;if(typeof u!="string")throw new Error("utilities.js: htmlToPage: input HTML is not a string");b=typeof v!="string"?"htmlToPage":v;let S="";return S+=`<!DOCTYPE html>
`,S+=`<html lang="en">
`,S+=`<head>
`,S+=`<meta charset="utf-8">
`,S+=`<title>${b}</title>
`,S+=`<style>
`,S+=o(),S+=`</style>
`,S+=`</head>
<body>
`,S+=`<p>${new Date}</p>
`,S+=u,S+=`</body>
</html>
`,S},r.parserResultToHtml=function(u,v){let b,S,_=null;typeof v=="string"&&v!==""&&(_=v),b=u.success===!0?`<span class="${n.CLASS_MATCH}">true</span>`:`<span class="${n.CLASS_NOMATCH}">false</span>`,S=u.state===l.EMPTY?`<span class="${n.CLASS_EMPTY}">EMPTY</span>`:u.state===l.MATCH?`<span class="${n.CLASS_MATCH}">MATCH</span>`:u.state===l.NOMATCH?`<span class="${n.CLASS_NOMATCH}">NOMATCH</span>`:`<span class="${n.CLASS_NOMATCH}">unrecognized</span>`;let g="";return g+=`<table class="${n.CLASS_STATE}">
`,_&&(g+=`<caption>${_}</caption>
`),g+=`<tr><th>state item</th><th>value</th><th>description</th></tr>
`,g+=`<tr><td>parser success</td><td>${b}</td>
`,g+=`<td><span class="${n.CLASS_MATCH}">true</span> if the parse succeeded,
`,g+=` <span class="${n.CLASS_NOMATCH}">false</span> otherwise`,g+=`<br><i>NOTE: for success, entire string must be matched</i></td></tr>
`,g+=`<tr><td>parser state</td><td>${S}</td>
`,g+=`<td><span class="${n.CLASS_EMPTY}">EMPTY</span>, `,g+=`<span class="${n.CLASS_MATCH}">MATCH</span> or 
`,g+=`<span class="${n.CLASS_NOMATCH}">NOMATCH</span></td></tr>
`,g+=`<tr><td>string length</td><td>${u.length}</td><td>length of the input (sub)string</td></tr>
`,g+=`<tr><td>matched length</td><td>${u.matched}</td><td>number of input string characters matched</td></tr>
`,g+=`<tr><td>max matched</td><td>${u.maxMatched}</td><td>maximum number of input string characters matched</td></tr>
`,g+=`<tr><td>max tree depth</td><td>${u.maxTreeDepth}</td><td>maximum depth of the parse tree reached</td></tr>
`,g+=`<tr><td>node hits</td><td>${u.nodeHits}</td><td>number of parse tree node hits (opcode function calls)</td></tr>
`,g+=`<tr><td>input length</td><td>${u.inputLength}</td><td>length of full input string</td></tr>
`,g+=`<tr><td>sub-string begin</td><td>${u.subBegin}</td><td>sub-string first character index</td></tr>
`,g+=`<tr><td>sub-string end</td><td>${u.subEnd}</td><td>sub-string end-of-string index</td></tr>
`,g+=`<tr><td>sub-string length</td><td>${u.subLength}</td><td>sub-string length</td></tr>
`,g+=`</table>
`,g},r.charsToString=function(u,v,b){let S,_;if(typeof v=="number"){if(v>=u.length)return"";S=v<0?0:v}else S=0;if(typeof b=="number"){if(b<=0)return"";_=b>u.length-S?u.length:S+b}else _=u.length;return S<_?a.encode("UTF16LE",u.slice(S,_)).toString("utf16le"):""},r.stringToChars=function(u){return a.decode("STRING",u)},r.opcodeToString=function(u){let v="unknown";switch(u){case l.ALT:v="ALT";break;case l.CAT:v="CAT";break;case l.RNM:v="RNM";break;case l.UDT:v="UDT";break;case l.AND:v="AND";break;case l.NOT:v="NOT";break;case l.REP:v="REP";break;case l.TRG:v="TRG";break;case l.TBS:v="TBS";break;case l.TLS:v="TLS";break;case l.BKR:v="BKR";break;case l.BKA:v="BKA";break;case l.BKN:v="BKN";break;case l.ABG:v="ABG";break;case l.AEN:v="AEN";break;default:throw new Error("unrecognized opcode")}return v},r.stateToString=function(u){let v="unknown";switch(u){case l.ACTIVE:v="ACTIVE";break;case l.MATCH:v="MATCH";break;case l.EMPTY:v="EMPTY";break;case l.NOMATCH:v="NOMATCH";break;default:throw new Error("unrecognized state")}return v},r.asciiChars=["NUL","SOH","STX","ETX","EOT","ENQ","ACK","BEL","BS","TAB","LF","VT","FF","CR","SO","SI","DLE","DC1","DC2","DC3","DC4","NAK","SYN","ETB","CAN","EM","SUB","ESC","FS","GS","RS","US","&nbsp;","!","&#34;","#","$","%","&#38;","&#39;","(",")","*","+",",","-",".","/","0","1","2","3","4","5","6","7","8","9",":",";","&#60;","=","&#62;","?","@","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","[","&#92;","]","^","_","`","a","b","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z","{","|","}","~","DEL"],r.charToHex=function(u){let v=u.toString(16).toUpperCase();switch(v.length){case 1:case 3:case 7:v=`0${v}`;break;case 2:case 6:v=`00${v}`;break;case 4:break;case 5:v=`000${v}`;break;default:throw new Error("unrecognized option")}return v},r.charsToDec=function(u,v,b){let S="";if(!Array.isArray(u))throw new Error("utilities.js: charsToDec: input must be an array of integers");let _=m(u.length,v,b);if(_.end>_.beg){S+=u[_.beg];for(let g=_.beg+1;g<_.end;g+=1)S+=`,${u[g]}`}return S},r.charsToHex=function(u,v,b){let S="";if(!Array.isArray(u))throw new Error("utilities.js: charsToHex: input must be an array of integers");let _=m(u.length,v,b);if(_.end>_.beg){S+=`\\x${f.charToHex(u[_.beg])}`;for(let g=_.beg+1;g<_.end;g+=1)S+=`,\\x${f.charToHex(u[g])}`}return S},r.charsToHtmlEntities=function(u,v,b){let S="";if(!Array.isArray(u))throw new Error("utilities.js: charsToHex: input must be an array of integers");let _=m(u.length,v,b);if(_.end>_.beg)for(let g=_.beg;g<_.end;g+=1)S+=`&#x${u[g].toString(16)};`;return S},r.charsToUnicode=function(u,v,b){let S="";if(!Array.isArray(u))throw new Error("utilities.js: charsToUnicode: input must be an array of integers");let _=m(u.length,v,b);if(_.end>_.beg)for(let A=_.beg;A<_.end;A+=1)S+=(g=u[A])>=55296&&g<=57343||g>1114111?` U+${f.charToHex(u[A])}`:`&#${u[A]};`;var g;return S},r.charsToJsUnicode=function(u,v,b){let S="";if(!Array.isArray(u))throw new Error("utilities.js: charsToJsUnicode: input must be an array of integers");let _=m(u.length,v,b);if(_.end>_.beg){S+=`\\u${f.charToHex(u[_.beg])}`;for(let g=_.beg+1;g<_.end;g+=1)S+=`,\\u${f.charToHex(u[g])}`}return S},r.charsToAscii=function(u,v,b){let S="";if(!Array.isArray(u))throw new Error("utilities.js: charsToAscii: input must be an array of integers");let _=m(u.length,v,b);for(let g=_.beg;g<_.end;g+=1){let A=u[g];S+=A>=32&&A<=126?String.fromCharCode(A):`\\x${f.charToHex(A)}`}return S},r.charsToAsciiHtml=function(u,v,b){if(!Array.isArray(u))throw new Error("utilities.js: charsToAsciiHtml: input must be an array of integers");let S,_="",g=m(u.length,v,b);for(let A=g.beg;A<g.end;A+=1)S=u[A],_+=S<32||S===127?`<span class="${n.CLASS_CTRLCHAR}">${f.asciiChars[S]}</span>`:S>127?`<span class="${n.CLASS_CTRLCHAR}">U+${f.charToHex(S)}</span>`:f.asciiChars[S];return _},r.stringToAsciiHtml=function(u){let v=a.decode("STRING",u);return this.charsToAsciiHtml(v)}},{"../apg-conv-api/converter":4,"./emitcss":9,"./identifiers":10,"./style":14}],17:[function(e,t,r){globalThis.apgLib=e("./node-exports"),globalThis.apgConvApi=e("../apg-conv-api/node-exports")},{"../apg-conv-api/node-exports":5,"./node-exports":11}]},{},[17]);p();d();function Hs(){let e={};return e.grammarObject="grammarObject",e.rules=[],e.rules[0]={name:"ALPHA",lower:"alpha",index:0,isBkr:!1},e.rules[1]={name:"BIT",lower:"bit",index:1,isBkr:!1},e.rules[2]={name:"CHAR",lower:"char",index:2,isBkr:!1},e.rules[3]={name:"CR",lower:"cr",index:3,isBkr:!1},e.rules[4]={name:"CRLF",lower:"crlf",index:4,isBkr:!1},e.rules[5]={name:"CTL",lower:"ctl",index:5,isBkr:!1},e.rules[6]={name:"DIGIT",lower:"digit",index:6,isBkr:!1},e.rules[7]={name:"DQUOTE",lower:"dquote",index:7,isBkr:!1},e.rules[8]={name:"HEXDIG",lower:"hexdig",index:8,isBkr:!1},e.rules[9]={name:"HTAB",lower:"htab",index:9,isBkr:!1},e.rules[10]={name:"LF",lower:"lf",index:10,isBkr:!1},e.rules[11]={name:"LWSP",lower:"lwsp",index:11,isBkr:!1},e.rules[12]={name:"OCTET",lower:"octet",index:12,isBkr:!1},e.rules[13]={name:"SP",lower:"sp",index:13,isBkr:!1},e.rules[14]={name:"VCHAR",lower:"vchar",index:14,isBkr:!1},e.rules[15]={name:"WSP",lower:"wsp",index:15,isBkr:!1},e.rules[16]={name:"date-fullyear",lower:"date-fullyear",index:16,isBkr:!1},e.rules[17]={name:"date-month",lower:"date-month",index:17,isBkr:!1},e.rules[18]={name:"date-mday",lower:"date-mday",index:18,isBkr:!1},e.rules[19]={name:"time-hour",lower:"time-hour",index:19,isBkr:!1},e.rules[20]={name:"time-minute",lower:"time-minute",index:20,isBkr:!1},e.rules[21]={name:"time-second",lower:"time-second",index:21,isBkr:!1},e.rules[22]={name:"time-secfrac",lower:"time-secfrac",index:22,isBkr:!1},e.rules[23]={name:"time-numoffset",lower:"time-numoffset",index:23,isBkr:!1},e.rules[24]={name:"time-offset",lower:"time-offset",index:24,isBkr:!1},e.rules[25]={name:"partial-time",lower:"partial-time",index:25,isBkr:!1},e.rules[26]={name:"full-date",lower:"full-date",index:26,isBkr:!1},e.rules[27]={name:"full-time",lower:"full-time",index:27,isBkr:!1},e.rules[28]={name:"date-time",lower:"date-time",index:28,isBkr:!1},e.rules[29]={name:"URI",lower:"uri",index:29,isBkr:!1},e.rules[30]={name:"hier-part",lower:"hier-part",index:30,isBkr:!1},e.rules[31]={name:"URI-reference",lower:"uri-reference",index:31,isBkr:!1},e.rules[32]={name:"absolute-URI",lower:"absolute-uri",index:32,isBkr:!1},e.rules[33]={name:"relative-ref",lower:"relative-ref",index:33,isBkr:!1},e.rules[34]={name:"relative-part",lower:"relative-part",index:34,isBkr:!1},e.rules[35]={name:"scheme",lower:"scheme",index:35,isBkr:!1},e.rules[36]={name:"authority",lower:"authority",index:36,isBkr:!1},e.rules[37]={name:"userinfo",lower:"userinfo",index:37,isBkr:!1},e.rules[38]={name:"host",lower:"host",index:38,isBkr:!1},e.rules[39]={name:"port",lower:"port",index:39,isBkr:!1},e.rules[40]={name:"IP-literal",lower:"ip-literal",index:40,isBkr:!1},e.rules[41]={name:"IPvFuture",lower:"ipvfuture",index:41,isBkr:!1},e.rules[42]={name:"IPv6address",lower:"ipv6address",index:42,isBkr:!1},e.rules[43]={name:"h16",lower:"h16",index:43,isBkr:!1},e.rules[44]={name:"ls32",lower:"ls32",index:44,isBkr:!1},e.rules[45]={name:"IPv4address",lower:"ipv4address",index:45,isBkr:!1},e.rules[46]={name:"dec-octet",lower:"dec-octet",index:46,isBkr:!1},e.rules[47]={name:"reg-name",lower:"reg-name",index:47,isBkr:!1},e.rules[48]={name:"path",lower:"path",index:48,isBkr:!1},e.rules[49]={name:"path-abempty",lower:"path-abempty",index:49,isBkr:!1},e.rules[50]={name:"path-absolute",lower:"path-absolute",index:50,isBkr:!1},e.rules[51]={name:"path-noscheme",lower:"path-noscheme",index:51,isBkr:!1},e.rules[52]={name:"path-rootless",lower:"path-rootless",index:52,isBkr:!1},e.rules[53]={name:"path-empty",lower:"path-empty",index:53,isBkr:!1},e.rules[54]={name:"segment",lower:"segment",index:54,isBkr:!1},e.rules[55]={name:"segment-nz",lower:"segment-nz",index:55,isBkr:!1},e.rules[56]={name:"segment-nz-nc",lower:"segment-nz-nc",index:56,isBkr:!1},e.rules[57]={name:"pchar",lower:"pchar",index:57,isBkr:!1},e.rules[58]={name:"query",lower:"query",index:58,isBkr:!1},e.rules[59]={name:"fragment",lower:"fragment",index:59,isBkr:!1},e.rules[60]={name:"pct-encoded",lower:"pct-encoded",index:60,isBkr:!1},e.rules[61]={name:"unreserved",lower:"unreserved",index:61,isBkr:!1},e.rules[62]={name:"reserved",lower:"reserved",index:62,isBkr:!1},e.rules[63]={name:"gen-delims",lower:"gen-delims",index:63,isBkr:!1},e.rules[64]={name:"sub-delims",lower:"sub-delims",index:64,isBkr:!1},e.rules[65]={name:"sign-in-with-ethereum",lower:"sign-in-with-ethereum",index:65,isBkr:!1},e.rules[66]={name:"message-domain",lower:"message-domain",index:66,isBkr:!1},e.rules[67]={name:"message-address",lower:"message-address",index:67,isBkr:!1},e.rules[68]={name:"message-statement",lower:"message-statement",index:68,isBkr:!1},e.rules[69]={name:"message-uri",lower:"message-uri",index:69,isBkr:!1},e.rules[70]={name:"message-version",lower:"message-version",index:70,isBkr:!1},e.rules[71]={name:"message-chain-id",lower:"message-chain-id",index:71,isBkr:!1},e.rules[72]={name:"message-nonce",lower:"message-nonce",index:72,isBkr:!1},e.rules[73]={name:"message-issued-at",lower:"message-issued-at",index:73,isBkr:!1},e.rules[74]={name:"message-expiration-time",lower:"message-expiration-time",index:74,isBkr:!1},e.rules[75]={name:"message-not-before",lower:"message-not-before",index:75,isBkr:!1},e.rules[76]={name:"message-request-id",lower:"message-request-id",index:76,isBkr:!1},e.rules[77]={name:"message-resources",lower:"message-resources",index:77,isBkr:!1},e.udts=[],e.rules[0].opcodes=[],e.rules[0].opcodes[0]={type:1,children:[1,2]},e.rules[0].opcodes[1]={type:5,min:65,max:90},e.rules[0].opcodes[2]={type:5,min:97,max:122},e.rules[1].opcodes=[],e.rules[1].opcodes[0]={type:1,children:[1,2]},e.rules[1].opcodes[1]={type:7,string:[48]},e.rules[1].opcodes[2]={type:7,string:[49]},e.rules[2].opcodes=[],e.rules[2].opcodes[0]={type:5,min:1,max:127},e.rules[3].opcodes=[],e.rules[3].opcodes[0]={type:6,string:[13]},e.rules[4].opcodes=[],e.rules[4].opcodes[0]={type:2,children:[1,2]},e.rules[4].opcodes[1]={type:4,index:3},e.rules[4].opcodes[2]={type:4,index:10},e.rules[5].opcodes=[],e.rules[5].opcodes[0]={type:1,children:[1,2]},e.rules[5].opcodes[1]={type:5,min:0,max:31},e.rules[5].opcodes[2]={type:6,string:[127]},e.rules[6].opcodes=[],e.rules[6].opcodes[0]={type:5,min:48,max:57},e.rules[7].opcodes=[],e.rules[7].opcodes[0]={type:6,string:[34]},e.rules[8].opcodes=[],e.rules[8].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[8].opcodes[1]={type:4,index:6},e.rules[8].opcodes[2]={type:7,string:[97]},e.rules[8].opcodes[3]={type:7,string:[98]},e.rules[8].opcodes[4]={type:7,string:[99]},e.rules[8].opcodes[5]={type:7,string:[100]},e.rules[8].opcodes[6]={type:7,string:[101]},e.rules[8].opcodes[7]={type:7,string:[102]},e.rules[9].opcodes=[],e.rules[9].opcodes[0]={type:6,string:[9]},e.rules[10].opcodes=[],e.rules[10].opcodes[0]={type:6,string:[10]},e.rules[11].opcodes=[],e.rules[11].opcodes[0]={type:3,min:0,max:1/0},e.rules[11].opcodes[1]={type:1,children:[2,3]},e.rules[11].opcodes[2]={type:4,index:15},e.rules[11].opcodes[3]={type:2,children:[4,5]},e.rules[11].opcodes[4]={type:4,index:4},e.rules[11].opcodes[5]={type:4,index:15},e.rules[12].opcodes=[],e.rules[12].opcodes[0]={type:5,min:0,max:255},e.rules[13].opcodes=[],e.rules[13].opcodes[0]={type:6,string:[32]},e.rules[14].opcodes=[],e.rules[14].opcodes[0]={type:5,min:33,max:126},e.rules[15].opcodes=[],e.rules[15].opcodes[0]={type:1,children:[1,2]},e.rules[15].opcodes[1]={type:4,index:13},e.rules[15].opcodes[2]={type:4,index:9},e.rules[16].opcodes=[],e.rules[16].opcodes[0]={type:3,min:4,max:4},e.rules[16].opcodes[1]={type:4,index:6},e.rules[17].opcodes=[],e.rules[17].opcodes[0]={type:3,min:2,max:2},e.rules[17].opcodes[1]={type:4,index:6},e.rules[18].opcodes=[],e.rules[18].opcodes[0]={type:3,min:2,max:2},e.rules[18].opcodes[1]={type:4,index:6},e.rules[19].opcodes=[],e.rules[19].opcodes[0]={type:3,min:2,max:2},e.rules[19].opcodes[1]={type:4,index:6},e.rules[20].opcodes=[],e.rules[20].opcodes[0]={type:3,min:2,max:2},e.rules[20].opcodes[1]={type:4,index:6},e.rules[21].opcodes=[],e.rules[21].opcodes[0]={type:3,min:2,max:2},e.rules[21].opcodes[1]={type:4,index:6},e.rules[22].opcodes=[],e.rules[22].opcodes[0]={type:2,children:[1,2]},e.rules[22].opcodes[1]={type:7,string:[46]},e.rules[22].opcodes[2]={type:3,min:1,max:1/0},e.rules[22].opcodes[3]={type:4,index:6},e.rules[23].opcodes=[],e.rules[23].opcodes[0]={type:2,children:[1,4,5,6]},e.rules[23].opcodes[1]={type:1,children:[2,3]},e.rules[23].opcodes[2]={type:7,string:[43]},e.rules[23].opcodes[3]={type:7,string:[45]},e.rules[23].opcodes[4]={type:4,index:19},e.rules[23].opcodes[5]={type:7,string:[58]},e.rules[23].opcodes[6]={type:4,index:20},e.rules[24].opcodes=[],e.rules[24].opcodes[0]={type:1,children:[1,2]},e.rules[24].opcodes[1]={type:7,string:[122]},e.rules[24].opcodes[2]={type:4,index:23},e.rules[25].opcodes=[],e.rules[25].opcodes[0]={type:2,children:[1,2,3,4,5,6]},e.rules[25].opcodes[1]={type:4,index:19},e.rules[25].opcodes[2]={type:7,string:[58]},e.rules[25].opcodes[3]={type:4,index:20},e.rules[25].opcodes[4]={type:7,string:[58]},e.rules[25].opcodes[5]={type:4,index:21},e.rules[25].opcodes[6]={type:3,min:0,max:1},e.rules[25].opcodes[7]={type:4,index:22},e.rules[26].opcodes=[],e.rules[26].opcodes[0]={type:2,children:[1,2,3,4,5]},e.rules[26].opcodes[1]={type:4,index:16},e.rules[26].opcodes[2]={type:7,string:[45]},e.rules[26].opcodes[3]={type:4,index:17},e.rules[26].opcodes[4]={type:7,string:[45]},e.rules[26].opcodes[5]={type:4,index:18},e.rules[27].opcodes=[],e.rules[27].opcodes[0]={type:2,children:[1,2]},e.rules[27].opcodes[1]={type:4,index:25},e.rules[27].opcodes[2]={type:4,index:24},e.rules[28].opcodes=[],e.rules[28].opcodes[0]={type:2,children:[1,2,3]},e.rules[28].opcodes[1]={type:4,index:26},e.rules[28].opcodes[2]={type:7,string:[116]},e.rules[28].opcodes[3]={type:4,index:27},e.rules[29].opcodes=[],e.rules[29].opcodes[0]={type:2,children:[1,2,3,4,8]},e.rules[29].opcodes[1]={type:4,index:35},e.rules[29].opcodes[2]={type:7,string:[58]},e.rules[29].opcodes[3]={type:4,index:30},e.rules[29].opcodes[4]={type:3,min:0,max:1},e.rules[29].opcodes[5]={type:2,children:[6,7]},e.rules[29].opcodes[6]={type:7,string:[63]},e.rules[29].opcodes[7]={type:4,index:58},e.rules[29].opcodes[8]={type:3,min:0,max:1},e.rules[29].opcodes[9]={type:2,children:[10,11]},e.rules[29].opcodes[10]={type:7,string:[35]},e.rules[29].opcodes[11]={type:4,index:59},e.rules[30].opcodes=[],e.rules[30].opcodes[0]={type:1,children:[1,5,6,7]},e.rules[30].opcodes[1]={type:2,children:[2,3,4]},e.rules[30].opcodes[2]={type:7,string:[47,47]},e.rules[30].opcodes[3]={type:4,index:36},e.rules[30].opcodes[4]={type:4,index:49},e.rules[30].opcodes[5]={type:4,index:50},e.rules[30].opcodes[6]={type:4,index:52},e.rules[30].opcodes[7]={type:4,index:53},e.rules[31].opcodes=[],e.rules[31].opcodes[0]={type:1,children:[1,2]},e.rules[31].opcodes[1]={type:4,index:29},e.rules[31].opcodes[2]={type:4,index:33},e.rules[32].opcodes=[],e.rules[32].opcodes[0]={type:2,children:[1,2,3,4]},e.rules[32].opcodes[1]={type:4,index:35},e.rules[32].opcodes[2]={type:7,string:[58]},e.rules[32].opcodes[3]={type:4,index:30},e.rules[32].opcodes[4]={type:3,min:0,max:1},e.rules[32].opcodes[5]={type:2,children:[6,7]},e.rules[32].opcodes[6]={type:7,string:[63]},e.rules[32].opcodes[7]={type:4,index:58},e.rules[33].opcodes=[],e.rules[33].opcodes[0]={type:2,children:[1,2,6]},e.rules[33].opcodes[1]={type:4,index:34},e.rules[33].opcodes[2]={type:3,min:0,max:1},e.rules[33].opcodes[3]={type:2,children:[4,5]},e.rules[33].opcodes[4]={type:7,string:[63]},e.rules[33].opcodes[5]={type:4,index:58},e.rules[33].opcodes[6]={type:3,min:0,max:1},e.rules[33].opcodes[7]={type:2,children:[8,9]},e.rules[33].opcodes[8]={type:7,string:[35]},e.rules[33].opcodes[9]={type:4,index:59},e.rules[34].opcodes=[],e.rules[34].opcodes[0]={type:1,children:[1,5,6,7]},e.rules[34].opcodes[1]={type:2,children:[2,3,4]},e.rules[34].opcodes[2]={type:7,string:[47,47]},e.rules[34].opcodes[3]={type:4,index:36},e.rules[34].opcodes[4]={type:4,index:49},e.rules[34].opcodes[5]={type:4,index:50},e.rules[34].opcodes[6]={type:4,index:51},e.rules[34].opcodes[7]={type:4,index:53},e.rules[35].opcodes=[],e.rules[35].opcodes[0]={type:2,children:[1,2]},e.rules[35].opcodes[1]={type:4,index:0},e.rules[35].opcodes[2]={type:3,min:0,max:1/0},e.rules[35].opcodes[3]={type:1,children:[4,5,6,7,8]},e.rules[35].opcodes[4]={type:4,index:0},e.rules[35].opcodes[5]={type:4,index:6},e.rules[35].opcodes[6]={type:7,string:[43]},e.rules[35].opcodes[7]={type:7,string:[45]},e.rules[35].opcodes[8]={type:7,string:[46]},e.rules[36].opcodes=[],e.rules[36].opcodes[0]={type:2,children:[1,5,6]},e.rules[36].opcodes[1]={type:3,min:0,max:1},e.rules[36].opcodes[2]={type:2,children:[3,4]},e.rules[36].opcodes[3]={type:4,index:37},e.rules[36].opcodes[4]={type:7,string:[64]},e.rules[36].opcodes[5]={type:4,index:38},e.rules[36].opcodes[6]={type:3,min:0,max:1},e.rules[36].opcodes[7]={type:2,children:[8,9]},e.rules[36].opcodes[8]={type:7,string:[58]},e.rules[36].opcodes[9]={type:4,index:39},e.rules[37].opcodes=[],e.rules[37].opcodes[0]={type:3,min:0,max:1/0},e.rules[37].opcodes[1]={type:1,children:[2,3,4,5]},e.rules[37].opcodes[2]={type:4,index:61},e.rules[37].opcodes[3]={type:4,index:60},e.rules[37].opcodes[4]={type:4,index:64},e.rules[37].opcodes[5]={type:7,string:[58]},e.rules[38].opcodes=[],e.rules[38].opcodes[0]={type:1,children:[1,2,3]},e.rules[38].opcodes[1]={type:4,index:40},e.rules[38].opcodes[2]={type:4,index:45},e.rules[38].opcodes[3]={type:4,index:47},e.rules[39].opcodes=[],e.rules[39].opcodes[0]={type:3,min:0,max:1/0},e.rules[39].opcodes[1]={type:4,index:6},e.rules[40].opcodes=[],e.rules[40].opcodes[0]={type:2,children:[1,2,5]},e.rules[40].opcodes[1]={type:7,string:[91]},e.rules[40].opcodes[2]={type:1,children:[3,4]},e.rules[40].opcodes[3]={type:4,index:42},e.rules[40].opcodes[4]={type:4,index:41},e.rules[40].opcodes[5]={type:7,string:[93]},e.rules[41].opcodes=[],e.rules[41].opcodes[0]={type:2,children:[1,2,4,5]},e.rules[41].opcodes[1]={type:7,string:[118]},e.rules[41].opcodes[2]={type:3,min:1,max:1/0},e.rules[41].opcodes[3]={type:4,index:8},e.rules[41].opcodes[4]={type:7,string:[46]},e.rules[41].opcodes[5]={type:3,min:1,max:1/0},e.rules[41].opcodes[6]={type:1,children:[7,8,9]},e.rules[41].opcodes[7]={type:4,index:61},e.rules[41].opcodes[8]={type:4,index:64},e.rules[41].opcodes[9]={type:7,string:[58]},e.rules[42].opcodes=[],e.rules[42].opcodes[0]={type:1,children:[1,7,14,23,37,51,63,73,83]},e.rules[42].opcodes[1]={type:2,children:[2,6]},e.rules[42].opcodes[2]={type:3,min:6,max:6},e.rules[42].opcodes[3]={type:2,children:[4,5]},e.rules[42].opcodes[4]={type:4,index:43},e.rules[42].opcodes[5]={type:7,string:[58]},e.rules[42].opcodes[6]={type:4,index:44},e.rules[42].opcodes[7]={type:2,children:[8,9,13]},e.rules[42].opcodes[8]={type:7,string:[58,58]},e.rules[42].opcodes[9]={type:3,min:5,max:5},e.rules[42].opcodes[10]={type:2,children:[11,12]},e.rules[42].opcodes[11]={type:4,index:43},e.rules[42].opcodes[12]={type:7,string:[58]},e.rules[42].opcodes[13]={type:4,index:44},e.rules[42].opcodes[14]={type:2,children:[15,17,18,22]},e.rules[42].opcodes[15]={type:3,min:0,max:1},e.rules[42].opcodes[16]={type:4,index:43},e.rules[42].opcodes[17]={type:7,string:[58,58]},e.rules[42].opcodes[18]={type:3,min:4,max:4},e.rules[42].opcodes[19]={type:2,children:[20,21]},e.rules[42].opcodes[20]={type:4,index:43},e.rules[42].opcodes[21]={type:7,string:[58]},e.rules[42].opcodes[22]={type:4,index:44},e.rules[42].opcodes[23]={type:2,children:[24,31,32,36]},e.rules[42].opcodes[24]={type:3,min:0,max:1},e.rules[42].opcodes[25]={type:2,children:[26,30]},e.rules[42].opcodes[26]={type:3,min:0,max:1},e.rules[42].opcodes[27]={type:2,children:[28,29]},e.rules[42].opcodes[28]={type:4,index:43},e.rules[42].opcodes[29]={type:7,string:[58]},e.rules[42].opcodes[30]={type:4,index:43},e.rules[42].opcodes[31]={type:7,string:[58,58]},e.rules[42].opcodes[32]={type:3,min:3,max:3},e.rules[42].opcodes[33]={type:2,children:[34,35]},e.rules[42].opcodes[34]={type:4,index:43},e.rules[42].opcodes[35]={type:7,string:[58]},e.rules[42].opcodes[36]={type:4,index:44},e.rules[42].opcodes[37]={type:2,children:[38,45,46,50]},e.rules[42].opcodes[38]={type:3,min:0,max:1},e.rules[42].opcodes[39]={type:2,children:[40,44]},e.rules[42].opcodes[40]={type:3,min:0,max:2},e.rules[42].opcodes[41]={type:2,children:[42,43]},e.rules[42].opcodes[42]={type:4,index:43},e.rules[42].opcodes[43]={type:7,string:[58]},e.rules[42].opcodes[44]={type:4,index:43},e.rules[42].opcodes[45]={type:7,string:[58,58]},e.rules[42].opcodes[46]={type:3,min:2,max:2},e.rules[42].opcodes[47]={type:2,children:[48,49]},e.rules[42].opcodes[48]={type:4,index:43},e.rules[42].opcodes[49]={type:7,string:[58]},e.rules[42].opcodes[50]={type:4,index:44},e.rules[42].opcodes[51]={type:2,children:[52,59,60,61,62]},e.rules[42].opcodes[52]={type:3,min:0,max:1},e.rules[42].opcodes[53]={type:2,children:[54,58]},e.rules[42].opcodes[54]={type:3,min:0,max:3},e.rules[42].opcodes[55]={type:2,children:[56,57]},e.rules[42].opcodes[56]={type:4,index:43},e.rules[42].opcodes[57]={type:7,string:[58]},e.rules[42].opcodes[58]={type:4,index:43},e.rules[42].opcodes[59]={type:7,string:[58,58]},e.rules[42].opcodes[60]={type:4,index:43},e.rules[42].opcodes[61]={type:7,string:[58]},e.rules[42].opcodes[62]={type:4,index:44},e.rules[42].opcodes[63]={type:2,children:[64,71,72]},e.rules[42].opcodes[64]={type:3,min:0,max:1},e.rules[42].opcodes[65]={type:2,children:[66,70]},e.rules[42].opcodes[66]={type:3,min:0,max:4},e.rules[42].opcodes[67]={type:2,children:[68,69]},e.rules[42].opcodes[68]={type:4,index:43},e.rules[42].opcodes[69]={type:7,string:[58]},e.rules[42].opcodes[70]={type:4,index:43},e.rules[42].opcodes[71]={type:7,string:[58,58]},e.rules[42].opcodes[72]={type:4,index:44},e.rules[42].opcodes[73]={type:2,children:[74,81,82]},e.rules[42].opcodes[74]={type:3,min:0,max:1},e.rules[42].opcodes[75]={type:2,children:[76,80]},e.rules[42].opcodes[76]={type:3,min:0,max:5},e.rules[42].opcodes[77]={type:2,children:[78,79]},e.rules[42].opcodes[78]={type:4,index:43},e.rules[42].opcodes[79]={type:7,string:[58]},e.rules[42].opcodes[80]={type:4,index:43},e.rules[42].opcodes[81]={type:7,string:[58,58]},e.rules[42].opcodes[82]={type:4,index:43},e.rules[42].opcodes[83]={type:2,children:[84,91]},e.rules[42].opcodes[84]={type:3,min:0,max:1},e.rules[42].opcodes[85]={type:2,children:[86,90]},e.rules[42].opcodes[86]={type:3,min:0,max:6},e.rules[42].opcodes[87]={type:2,children:[88,89]},e.rules[42].opcodes[88]={type:4,index:43},e.rules[42].opcodes[89]={type:7,string:[58]},e.rules[42].opcodes[90]={type:4,index:43},e.rules[42].opcodes[91]={type:7,string:[58,58]},e.rules[43].opcodes=[],e.rules[43].opcodes[0]={type:3,min:1,max:4},e.rules[43].opcodes[1]={type:4,index:8},e.rules[44].opcodes=[],e.rules[44].opcodes[0]={type:1,children:[1,5]},e.rules[44].opcodes[1]={type:2,children:[2,3,4]},e.rules[44].opcodes[2]={type:4,index:43},e.rules[44].opcodes[3]={type:7,string:[58]},e.rules[44].opcodes[4]={type:4,index:43},e.rules[44].opcodes[5]={type:4,index:45},e.rules[45].opcodes=[],e.rules[45].opcodes[0]={type:2,children:[1,2,3,4,5,6,7]},e.rules[45].opcodes[1]={type:4,index:46},e.rules[45].opcodes[2]={type:7,string:[46]},e.rules[45].opcodes[3]={type:4,index:46},e.rules[45].opcodes[4]={type:7,string:[46]},e.rules[45].opcodes[5]={type:4,index:46},e.rules[45].opcodes[6]={type:7,string:[46]},e.rules[45].opcodes[7]={type:4,index:46},e.rules[46].opcodes=[],e.rules[46].opcodes[0]={type:1,children:[1,2,5,9,13]},e.rules[46].opcodes[1]={type:4,index:6},e.rules[46].opcodes[2]={type:2,children:[3,4]},e.rules[46].opcodes[3]={type:5,min:49,max:57},e.rules[46].opcodes[4]={type:4,index:6},e.rules[46].opcodes[5]={type:2,children:[6,7]},e.rules[46].opcodes[6]={type:7,string:[49]},e.rules[46].opcodes[7]={type:3,min:2,max:2},e.rules[46].opcodes[8]={type:4,index:6},e.rules[46].opcodes[9]={type:2,children:[10,11,12]},e.rules[46].opcodes[10]={type:7,string:[50]},e.rules[46].opcodes[11]={type:5,min:48,max:52},e.rules[46].opcodes[12]={type:4,index:6},e.rules[46].opcodes[13]={type:2,children:[14,15]},e.rules[46].opcodes[14]={type:7,string:[50,53]},e.rules[46].opcodes[15]={type:5,min:48,max:53},e.rules[47].opcodes=[],e.rules[47].opcodes[0]={type:3,min:0,max:1/0},e.rules[47].opcodes[1]={type:1,children:[2,3,4]},e.rules[47].opcodes[2]={type:4,index:61},e.rules[47].opcodes[3]={type:4,index:60},e.rules[47].opcodes[4]={type:4,index:64},e.rules[48].opcodes=[],e.rules[48].opcodes[0]={type:1,children:[1,2,3,4,5]},e.rules[48].opcodes[1]={type:4,index:49},e.rules[48].opcodes[2]={type:4,index:50},e.rules[48].opcodes[3]={type:4,index:51},e.rules[48].opcodes[4]={type:4,index:52},e.rules[48].opcodes[5]={type:4,index:53},e.rules[49].opcodes=[],e.rules[49].opcodes[0]={type:3,min:0,max:1/0},e.rules[49].opcodes[1]={type:2,children:[2,3]},e.rules[49].opcodes[2]={type:7,string:[47]},e.rules[49].opcodes[3]={type:4,index:54},e.rules[50].opcodes=[],e.rules[50].opcodes[0]={type:2,children:[1,2]},e.rules[50].opcodes[1]={type:7,string:[47]},e.rules[50].opcodes[2]={type:3,min:0,max:1},e.rules[50].opcodes[3]={type:2,children:[4,5]},e.rules[50].opcodes[4]={type:4,index:55},e.rules[50].opcodes[5]={type:3,min:0,max:1/0},e.rules[50].opcodes[6]={type:2,children:[7,8]},e.rules[50].opcodes[7]={type:7,string:[47]},e.rules[50].opcodes[8]={type:4,index:54},e.rules[51].opcodes=[],e.rules[51].opcodes[0]={type:2,children:[1,2]},e.rules[51].opcodes[1]={type:4,index:56},e.rules[51].opcodes[2]={type:3,min:0,max:1/0},e.rules[51].opcodes[3]={type:2,children:[4,5]},e.rules[51].opcodes[4]={type:7,string:[47]},e.rules[51].opcodes[5]={type:4,index:54},e.rules[52].opcodes=[],e.rules[52].opcodes[0]={type:2,children:[1,2]},e.rules[52].opcodes[1]={type:4,index:55},e.rules[52].opcodes[2]={type:3,min:0,max:1/0},e.rules[52].opcodes[3]={type:2,children:[4,5]},e.rules[52].opcodes[4]={type:7,string:[47]},e.rules[52].opcodes[5]={type:4,index:54},e.rules[53].opcodes=[],e.rules[53].opcodes[0]={type:3,min:0,max:0},e.rules[53].opcodes[1]={type:4,index:57},e.rules[54].opcodes=[],e.rules[54].opcodes[0]={type:3,min:0,max:1/0},e.rules[54].opcodes[1]={type:4,index:57},e.rules[55].opcodes=[],e.rules[55].opcodes[0]={type:3,min:1,max:1/0},e.rules[55].opcodes[1]={type:4,index:57},e.rules[56].opcodes=[],e.rules[56].opcodes[0]={type:3,min:1,max:1/0},e.rules[56].opcodes[1]={type:1,children:[2,3,4,5]},e.rules[56].opcodes[2]={type:4,index:61},e.rules[56].opcodes[3]={type:4,index:60},e.rules[56].opcodes[4]={type:4,index:64},e.rules[56].opcodes[5]={type:7,string:[64]},e.rules[57].opcodes=[],e.rules[57].opcodes[0]={type:1,children:[1,2,3,4,5]},e.rules[57].opcodes[1]={type:4,index:61},e.rules[57].opcodes[2]={type:4,index:60},e.rules[57].opcodes[3]={type:4,index:64},e.rules[57].opcodes[4]={type:7,string:[58]},e.rules[57].opcodes[5]={type:7,string:[64]},e.rules[58].opcodes=[],e.rules[58].opcodes[0]={type:3,min:0,max:1/0},e.rules[58].opcodes[1]={type:1,children:[2,3,4]},e.rules[58].opcodes[2]={type:4,index:57},e.rules[58].opcodes[3]={type:7,string:[47]},e.rules[58].opcodes[4]={type:7,string:[63]},e.rules[59].opcodes=[],e.rules[59].opcodes[0]={type:3,min:0,max:1/0},e.rules[59].opcodes[1]={type:1,children:[2,3,4]},e.rules[59].opcodes[2]={type:4,index:57},e.rules[59].opcodes[3]={type:7,string:[47]},e.rules[59].opcodes[4]={type:7,string:[63]},e.rules[60].opcodes=[],e.rules[60].opcodes[0]={type:2,children:[1,2,3]},e.rules[60].opcodes[1]={type:7,string:[37]},e.rules[60].opcodes[2]={type:4,index:8},e.rules[60].opcodes[3]={type:4,index:8},e.rules[61].opcodes=[],e.rules[61].opcodes[0]={type:1,children:[1,2,3,4,5,6]},e.rules[61].opcodes[1]={type:4,index:0},e.rules[61].opcodes[2]={type:4,index:6},e.rules[61].opcodes[3]={type:7,string:[45]},e.rules[61].opcodes[4]={type:7,string:[46]},e.rules[61].opcodes[5]={type:7,string:[95]},e.rules[61].opcodes[6]={type:7,string:[126]},e.rules[62].opcodes=[],e.rules[62].opcodes[0]={type:1,children:[1,2]},e.rules[62].opcodes[1]={type:4,index:63},e.rules[62].opcodes[2]={type:4,index:64},e.rules[63].opcodes=[],e.rules[63].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[63].opcodes[1]={type:7,string:[58]},e.rules[63].opcodes[2]={type:7,string:[47]},e.rules[63].opcodes[3]={type:7,string:[63]},e.rules[63].opcodes[4]={type:7,string:[35]},e.rules[63].opcodes[5]={type:7,string:[91]},e.rules[63].opcodes[6]={type:7,string:[93]},e.rules[63].opcodes[7]={type:7,string:[64]},e.rules[64].opcodes=[],e.rules[64].opcodes[0]={type:1,children:[1,2,3,4,5,6,7,8,9,10,11]},e.rules[64].opcodes[1]={type:7,string:[33]},e.rules[64].opcodes[2]={type:7,string:[36]},e.rules[64].opcodes[3]={type:7,string:[38]},e.rules[64].opcodes[4]={type:7,string:[39]},e.rules[64].opcodes[5]={type:7,string:[40]},e.rules[64].opcodes[6]={type:7,string:[41]},e.rules[64].opcodes[7]={type:7,string:[42]},e.rules[64].opcodes[8]={type:7,string:[43]},e.rules[64].opcodes[9]={type:7,string:[44]},e.rules[64].opcodes[10]={type:7,string:[59]},e.rules[64].opcodes[11]={type:7,string:[61]},e.rules[65].opcodes=[],e.rules[65].opcodes[0]={type:2,children:[1,2,3,4,5,6,7,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,36,41]},e.rules[65].opcodes[1]={type:4,index:66},e.rules[65].opcodes[2]={type:6,string:[32,119,97,110,116,115,32,121,111,117,32,116,111,32,115,105,103,110,32,105,110,32,119,105,116,104,32,121,111,117,114,32,69,116,104,101,114,101,117,109,32,97,99,99,111,117,110,116,58]},e.rules[65].opcodes[3]={type:4,index:10},e.rules[65].opcodes[4]={type:4,index:67},e.rules[65].opcodes[5]={type:4,index:10},e.rules[65].opcodes[6]={type:4,index:10},e.rules[65].opcodes[7]={type:3,min:0,max:1},e.rules[65].opcodes[8]={type:2,children:[9,10]},e.rules[65].opcodes[9]={type:4,index:68},e.rules[65].opcodes[10]={type:4,index:10},e.rules[65].opcodes[11]={type:4,index:10},e.rules[65].opcodes[12]={type:6,string:[85,82,73,58,32]},e.rules[65].opcodes[13]={type:4,index:69},e.rules[65].opcodes[14]={type:4,index:10},e.rules[65].opcodes[15]={type:6,string:[86,101,114,115,105,111,110,58,32]},e.rules[65].opcodes[16]={type:4,index:70},e.rules[65].opcodes[17]={type:4,index:10},e.rules[65].opcodes[18]={type:6,string:[67,104,97,105,110,32,73,68,58,32]},e.rules[65].opcodes[19]={type:4,index:71},e.rules[65].opcodes[20]={type:4,index:10},e.rules[65].opcodes[21]={type:6,string:[78,111,110,99,101,58,32]},e.rules[65].opcodes[22]={type:4,index:72},e.rules[65].opcodes[23]={type:4,index:10},e.rules[65].opcodes[24]={type:6,string:[73,115,115,117,101,100,32,65,116,58,32]},e.rules[65].opcodes[25]={type:4,index:73},e.rules[65].opcodes[26]={type:3,min:0,max:1},e.rules[65].opcodes[27]={type:2,children:[28,29,30]},e.rules[65].opcodes[28]={type:4,index:10},e.rules[65].opcodes[29]={type:6,string:[69,120,112,105,114,97,116,105,111,110,32,84,105,109,101,58,32]},e.rules[65].opcodes[30]={type:4,index:74},e.rules[65].opcodes[31]={type:3,min:0,max:1},e.rules[65].opcodes[32]={type:2,children:[33,34,35]},e.rules[65].opcodes[33]={type:4,index:10},e.rules[65].opcodes[34]={type:6,string:[78,111,116,32,66,101,102,111,114,101,58,32]},e.rules[65].opcodes[35]={type:4,index:75},e.rules[65].opcodes[36]={type:3,min:0,max:1},e.rules[65].opcodes[37]={type:2,children:[38,39,40]},e.rules[65].opcodes[38]={type:4,index:10},e.rules[65].opcodes[39]={type:6,string:[82,101,113,117,101,115,116,32,73,68,58,32]},e.rules[65].opcodes[40]={type:4,index:76},e.rules[65].opcodes[41]={type:3,min:0,max:1},e.rules[65].opcodes[42]={type:2,children:[43,44,45]},e.rules[65].opcodes[43]={type:4,index:10},e.rules[65].opcodes[44]={type:6,string:[82,101,115,111,117,114,99,101,115,58]},e.rules[65].opcodes[45]={type:4,index:77},e.rules[66].opcodes=[],e.rules[66].opcodes[0]={type:4,index:36},e.rules[67].opcodes=[],e.rules[67].opcodes[0]={type:2,children:[1,2]},e.rules[67].opcodes[1]={type:7,string:[48,120]},e.rules[67].opcodes[2]={type:3,min:40,max:40},e.rules[67].opcodes[3]={type:4,index:8},e.rules[68].opcodes=[],e.rules[68].opcodes[0]={type:3,min:1,max:1/0},e.rules[68].opcodes[1]={type:1,children:[2,3,4]},e.rules[68].opcodes[2]={type:4,index:62},e.rules[68].opcodes[3]={type:4,index:61},e.rules[68].opcodes[4]={type:7,string:[32]},e.rules[69].opcodes=[],e.rules[69].opcodes[0]={type:4,index:29},e.rules[70].opcodes=[],e.rules[70].opcodes[0]={type:7,string:[49]},e.rules[71].opcodes=[],e.rules[71].opcodes[0]={type:3,min:1,max:1/0},e.rules[71].opcodes[1]={type:4,index:6},e.rules[72].opcodes=[],e.rules[72].opcodes[0]={type:3,min:8,max:1/0},e.rules[72].opcodes[1]={type:1,children:[2,3]},e.rules[72].opcodes[2]={type:4,index:0},e.rules[72].opcodes[3]={type:4,index:6},e.rules[73].opcodes=[],e.rules[73].opcodes[0]={type:4,index:28},e.rules[74].opcodes=[],e.rules[74].opcodes[0]={type:4,index:28},e.rules[75].opcodes=[],e.rules[75].opcodes[0]={type:4,index:28},e.rules[76].opcodes=[],e.rules[76].opcodes[0]={type:3,min:0,max:1/0},e.rules[76].opcodes[1]={type:4,index:57},e.rules[77].opcodes=[],e.rules[77].opcodes[0]={type:3,min:0,max:1/0},e.rules[77].opcodes[1]={type:2,children:[2,3,4]},e.rules[77].opcodes[2]={type:4,index:10},e.rules[77].opcodes[3]={type:7,string:[45,32]},e.rules[77].opcodes[4]={type:4,index:29},e.toString=function(){let r="";return r+=`; ==============================================================================
`,r+=`; Core rules for ABNF (RFC 5234)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; References:
`,r+=`; - https://www.rfc-editor.org/rfc/rfc5234#appendix-B.1
`,r+=`
`,r+=`ALPHA          =  %x41-5A / %x61-7A
`,r+=`               ; A-Z / a-z
`,r+=`
`,r+=`BIT            =  "0" / "1"
`,r+=`
`,r+=`CHAR           =  %x01-7F
`,r+=`               ; any 7-bit US-ASCII character,
`,r+=`               ;  excluding NUL
`,r+=`
`,r+=`CR             =  %x0D
`,r+=`               ; carriage return
`,r+=`
`,r+=`CRLF           =  CR LF
`,r+=`               ; Internet standard newline
`,r+=`
`,r+=`CTL            =  %x00-1F / %x7F
`,r+=`               ; controls
`,r+=`
`,r+=`DIGIT          =  %x30-39
`,r+=`               ; 0-9
`,r+=`
`,r+=`DQUOTE         =  %x22
`,r+=`               ; " (Double Quote)
`,r+=`
`,r+=`HEXDIG         =  DIGIT / "A" / "B" / "C" / "D" / "E" / "F"
`,r+=`
`,r+=`HTAB           =  %x09
`,r+=`               ; horizontal tab
`,r+=`
`,r+=`LF             =  %x0A
`,r+=`               ; linefeed
`,r+=`
`,r+=`LWSP           =  *(WSP / CRLF WSP)
`,r+=`               ; Use of this linear-white-space rule
`,r+=`               ;  permits lines containing only white
`,r+=`               ;  space that are no longer legal in
`,r+=`               ;  mail headers and have caused
`,r+=`               ;  interoperability problems in other
`,r+=`               ;  contexts.
`,r+=`               ; Do not use when defining mail
`,r+=`               ;  headers and use with caution in
`,r+=`               ;  other contexts.
`,r+=`
`,r+=`OCTET          =  %x00-FF
`,r+=`               ; 8 bits of data
`,r+=`
`,r+=`SP             =  %x20
`,r+=`
`,r+=`VCHAR          =  %x21-7E
`,r+=`               ; visible (printing) characters
`,r+=`
`,r+=`WSP            =  SP / HTAB
`,r+=`               ; white space
`,r+=`; ==============================================================================
`,r+=`; Internet Date/Time Format (RFC 3339)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; Requires:
`,r+=`; - core.bnf
`,r+=`;
`,r+=`; References:
`,r+=`; - https://www.rfc-editor.org/rfc/rfc3339#section-5.6
`,r+=`
`,r+=`date-fullyear   = 4DIGIT
`,r+=`
`,r+=`date-month      = 2DIGIT  ; 01-12
`,r+=`
`,r+=`date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on
`,r+=`                          ; month/year
`,r+=`
`,r+=`time-hour       = 2DIGIT  ; 00-23
`,r+=`
`,r+=`time-minute     = 2DIGIT  ; 00-59
`,r+=`
`,r+=`time-second     = 2DIGIT  ; 00-58, 00-59, 00-60 based on leap second
`,r+=`                          ; rules
`,r+=`
`,r+=`time-secfrac    = "." 1*DIGIT
`,r+=`
`,r+=`time-numoffset  = ("+" / "-") time-hour ":" time-minute
`,r+=`
`,r+=`time-offset     = "Z" / time-numoffset
`,r+=`
`,r+=`partial-time    = time-hour ":" time-minute ":" time-second
`,r+=`                  [time-secfrac]
`,r+=`
`,r+=`full-date       = date-fullyear "-" date-month "-" date-mday
`,r+=`
`,r+=`full-time       = partial-time time-offset
`,r+=`
`,r+=`date-time       = full-date "T" full-time
`,r+=`; ==============================================================================
`,r+=`; Uniform Resource Identifier (RFC 3986)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; Requires:
`,r+=`; - core.bnf
`,r+=`;
`,r+=`; References:
`,r+=`; - https://www.rfc-editor.org/rfc/rfc3986#appendix-A
`,r+=`
`,r+=`URI           = scheme ":" hier-part [ "?" query ] [ "#" fragment ]
`,r+=`
`,r+=`hier-part     = "//" authority path-abempty
`,r+=`              / path-absolute
`,r+=`              / path-rootless
`,r+=`              / path-empty
`,r+=`
`,r+=`URI-reference = URI / relative-ref
`,r+=`
`,r+=`absolute-URI  = scheme ":" hier-part [ "?" query ]
`,r+=`
`,r+=`relative-ref  = relative-part [ "?" query ] [ "#" fragment ]
`,r+=`
`,r+=`relative-part = "//" authority path-abempty
`,r+=`              / path-absolute
`,r+=`              / path-noscheme
`,r+=`              / path-empty
`,r+=`
`,r+=`scheme        = ALPHA *( ALPHA / DIGIT / "+" / "-" / "." )
`,r+=`
`,r+=`authority     = [ userinfo "@" ] host [ ":" port ]
`,r+=`userinfo      = *( unreserved / pct-encoded / sub-delims / ":" )
`,r+=`host          = IP-literal / IPv4address / reg-name
`,r+=`port          = *DIGIT
`,r+=`
`,r+=`IP-literal    = "[" ( IPv6address / IPvFuture  ) "]"
`,r+=`
`,r+=`IPvFuture     = "v" 1*HEXDIG "." 1*( unreserved / sub-delims / ":" )
`,r+=`
`,r+=`IPv6address   =                            6( h16 ":" ) ls32
`,r+=`              /                       "::" 5( h16 ":" ) ls32
`,r+=`              / [               h16 ] "::" 4( h16 ":" ) ls32
`,r+=`              / [ *1( h16 ":" ) h16 ] "::" 3( h16 ":" ) ls32
`,r+=`              / [ *2( h16 ":" ) h16 ] "::" 2( h16 ":" ) ls32
`,r+=`              / [ *3( h16 ":" ) h16 ] "::"    h16 ":"   ls32
`,r+=`              / [ *4( h16 ":" ) h16 ] "::"              ls32
`,r+=`              / [ *5( h16 ":" ) h16 ] "::"              h16
`,r+=`              / [ *6( h16 ":" ) h16 ] "::"
`,r+=`
`,r+=`h16           = 1*4HEXDIG
`,r+=`ls32          = ( h16 ":" h16 ) / IPv4address
`,r+=`IPv4address   = dec-octet "." dec-octet "." dec-octet "." dec-octet
`,r+=`dec-octet     = DIGIT                 ; 0-9
`,r+=`              / %x31-39 DIGIT         ; 10-99
`,r+=`              / "1" 2DIGIT            ; 100-199
`,r+=`              / "2" %x30-34 DIGIT     ; 200-249
`,r+=`              / "25" %x30-35          ; 250-255
`,r+=`
`,r+=`reg-name      = *( unreserved / pct-encoded / sub-delims )
`,r+=`
`,r+=`path          = path-abempty    ; begins with "/" or is empty
`,r+=`              / path-absolute   ; begins with "/" but not "//"
`,r+=`              / path-noscheme   ; begins with a non-colon segment
`,r+=`              / path-rootless   ; begins with a segment
`,r+=`              / path-empty      ; zero characters
`,r+=`
`,r+=`path-abempty  = *( "/" segment )
`,r+=`path-absolute = "/" [ segment-nz *( "/" segment ) ]
`,r+=`path-noscheme = segment-nz-nc *( "/" segment )
`,r+=`path-rootless = segment-nz *( "/" segment )
`,r+=`path-empty    = 0pchar
`,r+=`
`,r+=`segment       = *pchar
`,r+=`segment-nz    = 1*pchar
`,r+=`segment-nz-nc = 1*( unreserved / pct-encoded / sub-delims / "@" )
`,r+=`; non-zero-length segment without any colon ":"
`,r+=`
`,r+=`pchar         = unreserved / pct-encoded / sub-delims / ":" / "@"
`,r+=`
`,r+=`query         = *( pchar / "/" / "?" )
`,r+=`
`,r+=`fragment      = *( pchar / "/" / "?" )
`,r+=`
`,r+=`pct-encoded   = "%" HEXDIG HEXDIG
`,r+=`
`,r+=`unreserved    = ALPHA / DIGIT / "-" / "." / "_" / "~"
`,r+=`reserved      = gen-delims / sub-delims
`,r+=`gen-delims    = ":" / "/" / "?" / "#" / "[" / "]" / "@"
`,r+=`sub-delims    = "!" / "$" / "&" / "'" / "(" / ")"
`,r+=`              / "*" / "+" / "," / ";" / "="
`,r+=`; ==============================================================================
`,r+=`; Sign in with Ethereum (EIP-4361)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; Requires:
`,r+=`; - core.bnf
`,r+=`; - datetime.bnf
`,r+=`; - uri.bnf
`,r+=`;
`,r+=`; References:
`,r+=`; - https://eips.ethereum.org/EIPS/eip-4361
`,r+=`
`,r+=`sign-in-with-ethereum =
`,r+=`  message-domain %s" wants you to sign in with your Ethereum account:" LF
`,r+=`  message-address LF
`,r+=`  LF
`,r+=`  [ message-statement LF ]
`,r+=`  LF
`,r+=`  %s"URI: " message-uri LF
`,r+=`  %s"Version: " message-version LF
`,r+=`  %s"Chain ID: " message-chain-id LF
`,r+=`  %s"Nonce: " message-nonce LF
`,r+=`  %s"Issued At: " message-issued-at
`,r+=`  [ LF %s"Expiration Time: " message-expiration-time ]
`,r+=`  [ LF %s"Not Before: " message-not-before ]
`,r+=`  [ LF %s"Request ID: " message-request-id ]
`,r+=`  [ LF %s"Resources:" message-resources ]
`,r+=`
`,r+=`message-domain          = authority
`,r+=`message-address         = "0x" 40*40HEXDIG
`,r+=`message-statement       = 1*( reserved / unreserved / " " )
`,r+=`message-uri             = URI
`,r+=`message-version         = "1"
`,r+=`message-chain-id        = 1*DIGIT
`,r+=`message-nonce           = 8*( ALPHA / DIGIT )
`,r+=`message-issued-at       = date-time
`,r+=`message-expiration-time = date-time
`,r+=`message-not-before      = date-time
`,r+=`message-request-id      = *pchar
`,r+=`message-resources       = *( LF "- " URI )
`,r},e}p();d();function Fs(){let e={};return e.grammarObject="grammarObject",e.rules=[],e.rules[0]={name:"ALPHA",lower:"alpha",index:0,isBkr:!1},e.rules[1]={name:"BIT",lower:"bit",index:1,isBkr:!1},e.rules[2]={name:"CHAR",lower:"char",index:2,isBkr:!1},e.rules[3]={name:"CR",lower:"cr",index:3,isBkr:!1},e.rules[4]={name:"CRLF",lower:"crlf",index:4,isBkr:!1},e.rules[5]={name:"CTL",lower:"ctl",index:5,isBkr:!1},e.rules[6]={name:"DIGIT",lower:"digit",index:6,isBkr:!1},e.rules[7]={name:"DQUOTE",lower:"dquote",index:7,isBkr:!1},e.rules[8]={name:"HEXDIG",lower:"hexdig",index:8,isBkr:!1},e.rules[9]={name:"HTAB",lower:"htab",index:9,isBkr:!1},e.rules[10]={name:"LF",lower:"lf",index:10,isBkr:!1},e.rules[11]={name:"LWSP",lower:"lwsp",index:11,isBkr:!1},e.rules[12]={name:"OCTET",lower:"octet",index:12,isBkr:!1},e.rules[13]={name:"SP",lower:"sp",index:13,isBkr:!1},e.rules[14]={name:"VCHAR",lower:"vchar",index:14,isBkr:!1},e.rules[15]={name:"WSP",lower:"wsp",index:15,isBkr:!1},e.rules[16]={name:"date-fullyear",lower:"date-fullyear",index:16,isBkr:!1},e.rules[17]={name:"date-month",lower:"date-month",index:17,isBkr:!1},e.rules[18]={name:"date-mday",lower:"date-mday",index:18,isBkr:!1},e.rules[19]={name:"time-hour",lower:"time-hour",index:19,isBkr:!1},e.rules[20]={name:"time-minute",lower:"time-minute",index:20,isBkr:!1},e.rules[21]={name:"time-second",lower:"time-second",index:21,isBkr:!1},e.rules[22]={name:"time-secfrac",lower:"time-secfrac",index:22,isBkr:!1},e.rules[23]={name:"time-numoffset",lower:"time-numoffset",index:23,isBkr:!1},e.rules[24]={name:"time-offset",lower:"time-offset",index:24,isBkr:!1},e.rules[25]={name:"partial-time",lower:"partial-time",index:25,isBkr:!1},e.rules[26]={name:"full-date",lower:"full-date",index:26,isBkr:!1},e.rules[27]={name:"full-time",lower:"full-time",index:27,isBkr:!1},e.rules[28]={name:"date-time",lower:"date-time",index:28,isBkr:!1},e.rules[29]={name:"URI",lower:"uri",index:29,isBkr:!1},e.rules[30]={name:"hier-part",lower:"hier-part",index:30,isBkr:!1},e.rules[31]={name:"URI-reference",lower:"uri-reference",index:31,isBkr:!1},e.rules[32]={name:"absolute-URI",lower:"absolute-uri",index:32,isBkr:!1},e.rules[33]={name:"relative-ref",lower:"relative-ref",index:33,isBkr:!1},e.rules[34]={name:"relative-part",lower:"relative-part",index:34,isBkr:!1},e.rules[35]={name:"scheme",lower:"scheme",index:35,isBkr:!1},e.rules[36]={name:"authority",lower:"authority",index:36,isBkr:!1},e.rules[37]={name:"userinfo",lower:"userinfo",index:37,isBkr:!1},e.rules[38]={name:"host",lower:"host",index:38,isBkr:!1},e.rules[39]={name:"port",lower:"port",index:39,isBkr:!1},e.rules[40]={name:"IP-literal",lower:"ip-literal",index:40,isBkr:!1},e.rules[41]={name:"IPvFuture",lower:"ipvfuture",index:41,isBkr:!1},e.rules[42]={name:"IPv6address",lower:"ipv6address",index:42,isBkr:!1},e.rules[43]={name:"h16",lower:"h16",index:43,isBkr:!1},e.rules[44]={name:"ls32",lower:"ls32",index:44,isBkr:!1},e.rules[45]={name:"IPv4address",lower:"ipv4address",index:45,isBkr:!1},e.rules[46]={name:"dec-octet",lower:"dec-octet",index:46,isBkr:!1},e.rules[47]={name:"reg-name",lower:"reg-name",index:47,isBkr:!1},e.rules[48]={name:"path",lower:"path",index:48,isBkr:!1},e.rules[49]={name:"path-abempty",lower:"path-abempty",index:49,isBkr:!1},e.rules[50]={name:"path-absolute",lower:"path-absolute",index:50,isBkr:!1},e.rules[51]={name:"path-noscheme",lower:"path-noscheme",index:51,isBkr:!1},e.rules[52]={name:"path-rootless",lower:"path-rootless",index:52,isBkr:!1},e.rules[53]={name:"path-empty",lower:"path-empty",index:53,isBkr:!1},e.rules[54]={name:"segment",lower:"segment",index:54,isBkr:!1},e.rules[55]={name:"segment-nz",lower:"segment-nz",index:55,isBkr:!1},e.rules[56]={name:"segment-nz-nc",lower:"segment-nz-nc",index:56,isBkr:!1},e.rules[57]={name:"pchar",lower:"pchar",index:57,isBkr:!1},e.rules[58]={name:"query",lower:"query",index:58,isBkr:!1},e.rules[59]={name:"fragment",lower:"fragment",index:59,isBkr:!1},e.rules[60]={name:"pct-encoded",lower:"pct-encoded",index:60,isBkr:!1},e.rules[61]={name:"unreserved",lower:"unreserved",index:61,isBkr:!1},e.rules[62]={name:"reserved",lower:"reserved",index:62,isBkr:!1},e.rules[63]={name:"gen-delims",lower:"gen-delims",index:63,isBkr:!1},e.rules[64]={name:"sub-delims",lower:"sub-delims",index:64,isBkr:!1},e.rules[65]={name:"sign-in-with-solana",lower:"sign-in-with-solana",index:65,isBkr:!1},e.rules[66]={name:"advanced-fields",lower:"advanced-fields",index:66,isBkr:!1},e.rules[67]={name:"message-domain",lower:"message-domain",index:67,isBkr:!1},e.rules[68]={name:"message-address",lower:"message-address",index:68,isBkr:!1},e.rules[69]={name:"message-statement",lower:"message-statement",index:69,isBkr:!1},e.rules[70]={name:"message-uri",lower:"message-uri",index:70,isBkr:!1},e.rules[71]={name:"message-version",lower:"message-version",index:71,isBkr:!1},e.rules[72]={name:"message-chain-id",lower:"message-chain-id",index:72,isBkr:!1},e.rules[73]={name:"message-nonce",lower:"message-nonce",index:73,isBkr:!1},e.rules[74]={name:"message-issued-at",lower:"message-issued-at",index:74,isBkr:!1},e.rules[75]={name:"message-expiration-time",lower:"message-expiration-time",index:75,isBkr:!1},e.rules[76]={name:"message-not-before",lower:"message-not-before",index:76,isBkr:!1},e.rules[77]={name:"message-request-id",lower:"message-request-id",index:77,isBkr:!1},e.rules[78]={name:"message-resources",lower:"message-resources",index:78,isBkr:!1},e.udts=[],e.rules[0].opcodes=[],e.rules[0].opcodes[0]={type:1,children:[1,2]},e.rules[0].opcodes[1]={type:5,min:65,max:90},e.rules[0].opcodes[2]={type:5,min:97,max:122},e.rules[1].opcodes=[],e.rules[1].opcodes[0]={type:1,children:[1,2]},e.rules[1].opcodes[1]={type:7,string:[48]},e.rules[1].opcodes[2]={type:7,string:[49]},e.rules[2].opcodes=[],e.rules[2].opcodes[0]={type:5,min:1,max:127},e.rules[3].opcodes=[],e.rules[3].opcodes[0]={type:6,string:[13]},e.rules[4].opcodes=[],e.rules[4].opcodes[0]={type:2,children:[1,2]},e.rules[4].opcodes[1]={type:4,index:3},e.rules[4].opcodes[2]={type:4,index:10},e.rules[5].opcodes=[],e.rules[5].opcodes[0]={type:1,children:[1,2]},e.rules[5].opcodes[1]={type:5,min:0,max:31},e.rules[5].opcodes[2]={type:6,string:[127]},e.rules[6].opcodes=[],e.rules[6].opcodes[0]={type:5,min:48,max:57},e.rules[7].opcodes=[],e.rules[7].opcodes[0]={type:6,string:[34]},e.rules[8].opcodes=[],e.rules[8].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[8].opcodes[1]={type:4,index:6},e.rules[8].opcodes[2]={type:7,string:[97]},e.rules[8].opcodes[3]={type:7,string:[98]},e.rules[8].opcodes[4]={type:7,string:[99]},e.rules[8].opcodes[5]={type:7,string:[100]},e.rules[8].opcodes[6]={type:7,string:[101]},e.rules[8].opcodes[7]={type:7,string:[102]},e.rules[9].opcodes=[],e.rules[9].opcodes[0]={type:6,string:[9]},e.rules[10].opcodes=[],e.rules[10].opcodes[0]={type:6,string:[10]},e.rules[11].opcodes=[],e.rules[11].opcodes[0]={type:3,min:0,max:1/0},e.rules[11].opcodes[1]={type:1,children:[2,3]},e.rules[11].opcodes[2]={type:4,index:15},e.rules[11].opcodes[3]={type:2,children:[4,5]},e.rules[11].opcodes[4]={type:4,index:4},e.rules[11].opcodes[5]={type:4,index:15},e.rules[12].opcodes=[],e.rules[12].opcodes[0]={type:5,min:0,max:255},e.rules[13].opcodes=[],e.rules[13].opcodes[0]={type:6,string:[32]},e.rules[14].opcodes=[],e.rules[14].opcodes[0]={type:5,min:33,max:126},e.rules[15].opcodes=[],e.rules[15].opcodes[0]={type:1,children:[1,2]},e.rules[15].opcodes[1]={type:4,index:13},e.rules[15].opcodes[2]={type:4,index:9},e.rules[16].opcodes=[],e.rules[16].opcodes[0]={type:3,min:4,max:4},e.rules[16].opcodes[1]={type:4,index:6},e.rules[17].opcodes=[],e.rules[17].opcodes[0]={type:3,min:2,max:2},e.rules[17].opcodes[1]={type:4,index:6},e.rules[18].opcodes=[],e.rules[18].opcodes[0]={type:3,min:2,max:2},e.rules[18].opcodes[1]={type:4,index:6},e.rules[19].opcodes=[],e.rules[19].opcodes[0]={type:3,min:2,max:2},e.rules[19].opcodes[1]={type:4,index:6},e.rules[20].opcodes=[],e.rules[20].opcodes[0]={type:3,min:2,max:2},e.rules[20].opcodes[1]={type:4,index:6},e.rules[21].opcodes=[],e.rules[21].opcodes[0]={type:3,min:2,max:2},e.rules[21].opcodes[1]={type:4,index:6},e.rules[22].opcodes=[],e.rules[22].opcodes[0]={type:2,children:[1,2]},e.rules[22].opcodes[1]={type:7,string:[46]},e.rules[22].opcodes[2]={type:3,min:1,max:1/0},e.rules[22].opcodes[3]={type:4,index:6},e.rules[23].opcodes=[],e.rules[23].opcodes[0]={type:2,children:[1,4,5,6]},e.rules[23].opcodes[1]={type:1,children:[2,3]},e.rules[23].opcodes[2]={type:7,string:[43]},e.rules[23].opcodes[3]={type:7,string:[45]},e.rules[23].opcodes[4]={type:4,index:19},e.rules[23].opcodes[5]={type:7,string:[58]},e.rules[23].opcodes[6]={type:4,index:20},e.rules[24].opcodes=[],e.rules[24].opcodes[0]={type:1,children:[1,2]},e.rules[24].opcodes[1]={type:7,string:[122]},e.rules[24].opcodes[2]={type:4,index:23},e.rules[25].opcodes=[],e.rules[25].opcodes[0]={type:2,children:[1,2,3,4,5,6]},e.rules[25].opcodes[1]={type:4,index:19},e.rules[25].opcodes[2]={type:7,string:[58]},e.rules[25].opcodes[3]={type:4,index:20},e.rules[25].opcodes[4]={type:7,string:[58]},e.rules[25].opcodes[5]={type:4,index:21},e.rules[25].opcodes[6]={type:3,min:0,max:1},e.rules[25].opcodes[7]={type:4,index:22},e.rules[26].opcodes=[],e.rules[26].opcodes[0]={type:2,children:[1,2,3,4,5]},e.rules[26].opcodes[1]={type:4,index:16},e.rules[26].opcodes[2]={type:7,string:[45]},e.rules[26].opcodes[3]={type:4,index:17},e.rules[26].opcodes[4]={type:7,string:[45]},e.rules[26].opcodes[5]={type:4,index:18},e.rules[27].opcodes=[],e.rules[27].opcodes[0]={type:2,children:[1,2]},e.rules[27].opcodes[1]={type:4,index:25},e.rules[27].opcodes[2]={type:4,index:24},e.rules[28].opcodes=[],e.rules[28].opcodes[0]={type:2,children:[1,2,3]},e.rules[28].opcodes[1]={type:4,index:26},e.rules[28].opcodes[2]={type:7,string:[116]},e.rules[28].opcodes[3]={type:4,index:27},e.rules[29].opcodes=[],e.rules[29].opcodes[0]={type:2,children:[1,2,3,4,8]},e.rules[29].opcodes[1]={type:4,index:35},e.rules[29].opcodes[2]={type:7,string:[58]},e.rules[29].opcodes[3]={type:4,index:30},e.rules[29].opcodes[4]={type:3,min:0,max:1},e.rules[29].opcodes[5]={type:2,children:[6,7]},e.rules[29].opcodes[6]={type:7,string:[63]},e.rules[29].opcodes[7]={type:4,index:58},e.rules[29].opcodes[8]={type:3,min:0,max:1},e.rules[29].opcodes[9]={type:2,children:[10,11]},e.rules[29].opcodes[10]={type:7,string:[35]},e.rules[29].opcodes[11]={type:4,index:59},e.rules[30].opcodes=[],e.rules[30].opcodes[0]={type:1,children:[1,5,6,7]},e.rules[30].opcodes[1]={type:2,children:[2,3,4]},e.rules[30].opcodes[2]={type:7,string:[47,47]},e.rules[30].opcodes[3]={type:4,index:36},e.rules[30].opcodes[4]={type:4,index:49},e.rules[30].opcodes[5]={type:4,index:50},e.rules[30].opcodes[6]={type:4,index:52},e.rules[30].opcodes[7]={type:4,index:53},e.rules[31].opcodes=[],e.rules[31].opcodes[0]={type:1,children:[1,2]},e.rules[31].opcodes[1]={type:4,index:29},e.rules[31].opcodes[2]={type:4,index:33},e.rules[32].opcodes=[],e.rules[32].opcodes[0]={type:2,children:[1,2,3,4]},e.rules[32].opcodes[1]={type:4,index:35},e.rules[32].opcodes[2]={type:7,string:[58]},e.rules[32].opcodes[3]={type:4,index:30},e.rules[32].opcodes[4]={type:3,min:0,max:1},e.rules[32].opcodes[5]={type:2,children:[6,7]},e.rules[32].opcodes[6]={type:7,string:[63]},e.rules[32].opcodes[7]={type:4,index:58},e.rules[33].opcodes=[],e.rules[33].opcodes[0]={type:2,children:[1,2,6]},e.rules[33].opcodes[1]={type:4,index:34},e.rules[33].opcodes[2]={type:3,min:0,max:1},e.rules[33].opcodes[3]={type:2,children:[4,5]},e.rules[33].opcodes[4]={type:7,string:[63]},e.rules[33].opcodes[5]={type:4,index:58},e.rules[33].opcodes[6]={type:3,min:0,max:1},e.rules[33].opcodes[7]={type:2,children:[8,9]},e.rules[33].opcodes[8]={type:7,string:[35]},e.rules[33].opcodes[9]={type:4,index:59},e.rules[34].opcodes=[],e.rules[34].opcodes[0]={type:1,children:[1,5,6,7]},e.rules[34].opcodes[1]={type:2,children:[2,3,4]},e.rules[34].opcodes[2]={type:7,string:[47,47]},e.rules[34].opcodes[3]={type:4,index:36},e.rules[34].opcodes[4]={type:4,index:49},e.rules[34].opcodes[5]={type:4,index:50},e.rules[34].opcodes[6]={type:4,index:51},e.rules[34].opcodes[7]={type:4,index:53},e.rules[35].opcodes=[],e.rules[35].opcodes[0]={type:2,children:[1,2]},e.rules[35].opcodes[1]={type:4,index:0},e.rules[35].opcodes[2]={type:3,min:0,max:1/0},e.rules[35].opcodes[3]={type:1,children:[4,5,6,7,8]},e.rules[35].opcodes[4]={type:4,index:0},e.rules[35].opcodes[5]={type:4,index:6},e.rules[35].opcodes[6]={type:7,string:[43]},e.rules[35].opcodes[7]={type:7,string:[45]},e.rules[35].opcodes[8]={type:7,string:[46]},e.rules[36].opcodes=[],e.rules[36].opcodes[0]={type:2,children:[1,5,6]},e.rules[36].opcodes[1]={type:3,min:0,max:1},e.rules[36].opcodes[2]={type:2,children:[3,4]},e.rules[36].opcodes[3]={type:4,index:37},e.rules[36].opcodes[4]={type:7,string:[64]},e.rules[36].opcodes[5]={type:4,index:38},e.rules[36].opcodes[6]={type:3,min:0,max:1},e.rules[36].opcodes[7]={type:2,children:[8,9]},e.rules[36].opcodes[8]={type:7,string:[58]},e.rules[36].opcodes[9]={type:4,index:39},e.rules[37].opcodes=[],e.rules[37].opcodes[0]={type:3,min:0,max:1/0},e.rules[37].opcodes[1]={type:1,children:[2,3,4,5]},e.rules[37].opcodes[2]={type:4,index:61},e.rules[37].opcodes[3]={type:4,index:60},e.rules[37].opcodes[4]={type:4,index:64},e.rules[37].opcodes[5]={type:7,string:[58]},e.rules[38].opcodes=[],e.rules[38].opcodes[0]={type:1,children:[1,2,3]},e.rules[38].opcodes[1]={type:4,index:40},e.rules[38].opcodes[2]={type:4,index:45},e.rules[38].opcodes[3]={type:4,index:47},e.rules[39].opcodes=[],e.rules[39].opcodes[0]={type:3,min:0,max:1/0},e.rules[39].opcodes[1]={type:4,index:6},e.rules[40].opcodes=[],e.rules[40].opcodes[0]={type:2,children:[1,2,5]},e.rules[40].opcodes[1]={type:7,string:[91]},e.rules[40].opcodes[2]={type:1,children:[3,4]},e.rules[40].opcodes[3]={type:4,index:42},e.rules[40].opcodes[4]={type:4,index:41},e.rules[40].opcodes[5]={type:7,string:[93]},e.rules[41].opcodes=[],e.rules[41].opcodes[0]={type:2,children:[1,2,4,5]},e.rules[41].opcodes[1]={type:7,string:[118]},e.rules[41].opcodes[2]={type:3,min:1,max:1/0},e.rules[41].opcodes[3]={type:4,index:8},e.rules[41].opcodes[4]={type:7,string:[46]},e.rules[41].opcodes[5]={type:3,min:1,max:1/0},e.rules[41].opcodes[6]={type:1,children:[7,8,9]},e.rules[41].opcodes[7]={type:4,index:61},e.rules[41].opcodes[8]={type:4,index:64},e.rules[41].opcodes[9]={type:7,string:[58]},e.rules[42].opcodes=[],e.rules[42].opcodes[0]={type:1,children:[1,7,14,23,37,51,63,73,83]},e.rules[42].opcodes[1]={type:2,children:[2,6]},e.rules[42].opcodes[2]={type:3,min:6,max:6},e.rules[42].opcodes[3]={type:2,children:[4,5]},e.rules[42].opcodes[4]={type:4,index:43},e.rules[42].opcodes[5]={type:7,string:[58]},e.rules[42].opcodes[6]={type:4,index:44},e.rules[42].opcodes[7]={type:2,children:[8,9,13]},e.rules[42].opcodes[8]={type:7,string:[58,58]},e.rules[42].opcodes[9]={type:3,min:5,max:5},e.rules[42].opcodes[10]={type:2,children:[11,12]},e.rules[42].opcodes[11]={type:4,index:43},e.rules[42].opcodes[12]={type:7,string:[58]},e.rules[42].opcodes[13]={type:4,index:44},e.rules[42].opcodes[14]={type:2,children:[15,17,18,22]},e.rules[42].opcodes[15]={type:3,min:0,max:1},e.rules[42].opcodes[16]={type:4,index:43},e.rules[42].opcodes[17]={type:7,string:[58,58]},e.rules[42].opcodes[18]={type:3,min:4,max:4},e.rules[42].opcodes[19]={type:2,children:[20,21]},e.rules[42].opcodes[20]={type:4,index:43},e.rules[42].opcodes[21]={type:7,string:[58]},e.rules[42].opcodes[22]={type:4,index:44},e.rules[42].opcodes[23]={type:2,children:[24,31,32,36]},e.rules[42].opcodes[24]={type:3,min:0,max:1},e.rules[42].opcodes[25]={type:2,children:[26,30]},e.rules[42].opcodes[26]={type:3,min:0,max:1},e.rules[42].opcodes[27]={type:2,children:[28,29]},e.rules[42].opcodes[28]={type:4,index:43},e.rules[42].opcodes[29]={type:7,string:[58]},e.rules[42].opcodes[30]={type:4,index:43},e.rules[42].opcodes[31]={type:7,string:[58,58]},e.rules[42].opcodes[32]={type:3,min:3,max:3},e.rules[42].opcodes[33]={type:2,children:[34,35]},e.rules[42].opcodes[34]={type:4,index:43},e.rules[42].opcodes[35]={type:7,string:[58]},e.rules[42].opcodes[36]={type:4,index:44},e.rules[42].opcodes[37]={type:2,children:[38,45,46,50]},e.rules[42].opcodes[38]={type:3,min:0,max:1},e.rules[42].opcodes[39]={type:2,children:[40,44]},e.rules[42].opcodes[40]={type:3,min:0,max:2},e.rules[42].opcodes[41]={type:2,children:[42,43]},e.rules[42].opcodes[42]={type:4,index:43},e.rules[42].opcodes[43]={type:7,string:[58]},e.rules[42].opcodes[44]={type:4,index:43},e.rules[42].opcodes[45]={type:7,string:[58,58]},e.rules[42].opcodes[46]={type:3,min:2,max:2},e.rules[42].opcodes[47]={type:2,children:[48,49]},e.rules[42].opcodes[48]={type:4,index:43},e.rules[42].opcodes[49]={type:7,string:[58]},e.rules[42].opcodes[50]={type:4,index:44},e.rules[42].opcodes[51]={type:2,children:[52,59,60,61,62]},e.rules[42].opcodes[52]={type:3,min:0,max:1},e.rules[42].opcodes[53]={type:2,children:[54,58]},e.rules[42].opcodes[54]={type:3,min:0,max:3},e.rules[42].opcodes[55]={type:2,children:[56,57]},e.rules[42].opcodes[56]={type:4,index:43},e.rules[42].opcodes[57]={type:7,string:[58]},e.rules[42].opcodes[58]={type:4,index:43},e.rules[42].opcodes[59]={type:7,string:[58,58]},e.rules[42].opcodes[60]={type:4,index:43},e.rules[42].opcodes[61]={type:7,string:[58]},e.rules[42].opcodes[62]={type:4,index:44},e.rules[42].opcodes[63]={type:2,children:[64,71,72]},e.rules[42].opcodes[64]={type:3,min:0,max:1},e.rules[42].opcodes[65]={type:2,children:[66,70]},e.rules[42].opcodes[66]={type:3,min:0,max:4},e.rules[42].opcodes[67]={type:2,children:[68,69]},e.rules[42].opcodes[68]={type:4,index:43},e.rules[42].opcodes[69]={type:7,string:[58]},e.rules[42].opcodes[70]={type:4,index:43},e.rules[42].opcodes[71]={type:7,string:[58,58]},e.rules[42].opcodes[72]={type:4,index:44},e.rules[42].opcodes[73]={type:2,children:[74,81,82]},e.rules[42].opcodes[74]={type:3,min:0,max:1},e.rules[42].opcodes[75]={type:2,children:[76,80]},e.rules[42].opcodes[76]={type:3,min:0,max:5},e.rules[42].opcodes[77]={type:2,children:[78,79]},e.rules[42].opcodes[78]={type:4,index:43},e.rules[42].opcodes[79]={type:7,string:[58]},e.rules[42].opcodes[80]={type:4,index:43},e.rules[42].opcodes[81]={type:7,string:[58,58]},e.rules[42].opcodes[82]={type:4,index:43},e.rules[42].opcodes[83]={type:2,children:[84,91]},e.rules[42].opcodes[84]={type:3,min:0,max:1},e.rules[42].opcodes[85]={type:2,children:[86,90]},e.rules[42].opcodes[86]={type:3,min:0,max:6},e.rules[42].opcodes[87]={type:2,children:[88,89]},e.rules[42].opcodes[88]={type:4,index:43},e.rules[42].opcodes[89]={type:7,string:[58]},e.rules[42].opcodes[90]={type:4,index:43},e.rules[42].opcodes[91]={type:7,string:[58,58]},e.rules[43].opcodes=[],e.rules[43].opcodes[0]={type:3,min:1,max:4},e.rules[43].opcodes[1]={type:4,index:8},e.rules[44].opcodes=[],e.rules[44].opcodes[0]={type:1,children:[1,5]},e.rules[44].opcodes[1]={type:2,children:[2,3,4]},e.rules[44].opcodes[2]={type:4,index:43},e.rules[44].opcodes[3]={type:7,string:[58]},e.rules[44].opcodes[4]={type:4,index:43},e.rules[44].opcodes[5]={type:4,index:45},e.rules[45].opcodes=[],e.rules[45].opcodes[0]={type:2,children:[1,2,3,4,5,6,7]},e.rules[45].opcodes[1]={type:4,index:46},e.rules[45].opcodes[2]={type:7,string:[46]},e.rules[45].opcodes[3]={type:4,index:46},e.rules[45].opcodes[4]={type:7,string:[46]},e.rules[45].opcodes[5]={type:4,index:46},e.rules[45].opcodes[6]={type:7,string:[46]},e.rules[45].opcodes[7]={type:4,index:46},e.rules[46].opcodes=[],e.rules[46].opcodes[0]={type:1,children:[1,2,5,9,13]},e.rules[46].opcodes[1]={type:4,index:6},e.rules[46].opcodes[2]={type:2,children:[3,4]},e.rules[46].opcodes[3]={type:5,min:49,max:57},e.rules[46].opcodes[4]={type:4,index:6},e.rules[46].opcodes[5]={type:2,children:[6,7]},e.rules[46].opcodes[6]={type:7,string:[49]},e.rules[46].opcodes[7]={type:3,min:2,max:2},e.rules[46].opcodes[8]={type:4,index:6},e.rules[46].opcodes[9]={type:2,children:[10,11,12]},e.rules[46].opcodes[10]={type:7,string:[50]},e.rules[46].opcodes[11]={type:5,min:48,max:52},e.rules[46].opcodes[12]={type:4,index:6},e.rules[46].opcodes[13]={type:2,children:[14,15]},e.rules[46].opcodes[14]={type:7,string:[50,53]},e.rules[46].opcodes[15]={type:5,min:48,max:53},e.rules[47].opcodes=[],e.rules[47].opcodes[0]={type:3,min:0,max:1/0},e.rules[47].opcodes[1]={type:1,children:[2,3,4]},e.rules[47].opcodes[2]={type:4,index:61},e.rules[47].opcodes[3]={type:4,index:60},e.rules[47].opcodes[4]={type:4,index:64},e.rules[48].opcodes=[],e.rules[48].opcodes[0]={type:1,children:[1,2,3,4,5]},e.rules[48].opcodes[1]={type:4,index:49},e.rules[48].opcodes[2]={type:4,index:50},e.rules[48].opcodes[3]={type:4,index:51},e.rules[48].opcodes[4]={type:4,index:52},e.rules[48].opcodes[5]={type:4,index:53},e.rules[49].opcodes=[],e.rules[49].opcodes[0]={type:3,min:0,max:1/0},e.rules[49].opcodes[1]={type:2,children:[2,3]},e.rules[49].opcodes[2]={type:7,string:[47]},e.rules[49].opcodes[3]={type:4,index:54},e.rules[50].opcodes=[],e.rules[50].opcodes[0]={type:2,children:[1,2]},e.rules[50].opcodes[1]={type:7,string:[47]},e.rules[50].opcodes[2]={type:3,min:0,max:1},e.rules[50].opcodes[3]={type:2,children:[4,5]},e.rules[50].opcodes[4]={type:4,index:55},e.rules[50].opcodes[5]={type:3,min:0,max:1/0},e.rules[50].opcodes[6]={type:2,children:[7,8]},e.rules[50].opcodes[7]={type:7,string:[47]},e.rules[50].opcodes[8]={type:4,index:54},e.rules[51].opcodes=[],e.rules[51].opcodes[0]={type:2,children:[1,2]},e.rules[51].opcodes[1]={type:4,index:56},e.rules[51].opcodes[2]={type:3,min:0,max:1/0},e.rules[51].opcodes[3]={type:2,children:[4,5]},e.rules[51].opcodes[4]={type:7,string:[47]},e.rules[51].opcodes[5]={type:4,index:54},e.rules[52].opcodes=[],e.rules[52].opcodes[0]={type:2,children:[1,2]},e.rules[52].opcodes[1]={type:4,index:55},e.rules[52].opcodes[2]={type:3,min:0,max:1/0},e.rules[52].opcodes[3]={type:2,children:[4,5]},e.rules[52].opcodes[4]={type:7,string:[47]},e.rules[52].opcodes[5]={type:4,index:54},e.rules[53].opcodes=[],e.rules[53].opcodes[0]={type:3,min:0,max:0},e.rules[53].opcodes[1]={type:4,index:57},e.rules[54].opcodes=[],e.rules[54].opcodes[0]={type:3,min:0,max:1/0},e.rules[54].opcodes[1]={type:4,index:57},e.rules[55].opcodes=[],e.rules[55].opcodes[0]={type:3,min:1,max:1/0},e.rules[55].opcodes[1]={type:4,index:57},e.rules[56].opcodes=[],e.rules[56].opcodes[0]={type:3,min:1,max:1/0},e.rules[56].opcodes[1]={type:1,children:[2,3,4,5]},e.rules[56].opcodes[2]={type:4,index:61},e.rules[56].opcodes[3]={type:4,index:60},e.rules[56].opcodes[4]={type:4,index:64},e.rules[56].opcodes[5]={type:7,string:[64]},e.rules[57].opcodes=[],e.rules[57].opcodes[0]={type:1,children:[1,2,3,4,5]},e.rules[57].opcodes[1]={type:4,index:61},e.rules[57].opcodes[2]={type:4,index:60},e.rules[57].opcodes[3]={type:4,index:64},e.rules[57].opcodes[4]={type:7,string:[58]},e.rules[57].opcodes[5]={type:7,string:[64]},e.rules[58].opcodes=[],e.rules[58].opcodes[0]={type:3,min:0,max:1/0},e.rules[58].opcodes[1]={type:1,children:[2,3,4]},e.rules[58].opcodes[2]={type:4,index:57},e.rules[58].opcodes[3]={type:7,string:[47]},e.rules[58].opcodes[4]={type:7,string:[63]},e.rules[59].opcodes=[],e.rules[59].opcodes[0]={type:3,min:0,max:1/0},e.rules[59].opcodes[1]={type:1,children:[2,3,4]},e.rules[59].opcodes[2]={type:4,index:57},e.rules[59].opcodes[3]={type:7,string:[47]},e.rules[59].opcodes[4]={type:7,string:[63]},e.rules[60].opcodes=[],e.rules[60].opcodes[0]={type:2,children:[1,2,3]},e.rules[60].opcodes[1]={type:7,string:[37]},e.rules[60].opcodes[2]={type:4,index:8},e.rules[60].opcodes[3]={type:4,index:8},e.rules[61].opcodes=[],e.rules[61].opcodes[0]={type:1,children:[1,2,3,4,5,6]},e.rules[61].opcodes[1]={type:4,index:0},e.rules[61].opcodes[2]={type:4,index:6},e.rules[61].opcodes[3]={type:7,string:[45]},e.rules[61].opcodes[4]={type:7,string:[46]},e.rules[61].opcodes[5]={type:7,string:[95]},e.rules[61].opcodes[6]={type:7,string:[126]},e.rules[62].opcodes=[],e.rules[62].opcodes[0]={type:1,children:[1,2]},e.rules[62].opcodes[1]={type:4,index:63},e.rules[62].opcodes[2]={type:4,index:64},e.rules[63].opcodes=[],e.rules[63].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[63].opcodes[1]={type:7,string:[58]},e.rules[63].opcodes[2]={type:7,string:[47]},e.rules[63].opcodes[3]={type:7,string:[63]},e.rules[63].opcodes[4]={type:7,string:[35]},e.rules[63].opcodes[5]={type:7,string:[91]},e.rules[63].opcodes[6]={type:7,string:[93]},e.rules[63].opcodes[7]={type:7,string:[64]},e.rules[64].opcodes=[],e.rules[64].opcodes[0]={type:1,children:[1,2,3,4,5,6,7,8,9,10,11]},e.rules[64].opcodes[1]={type:7,string:[33]},e.rules[64].opcodes[2]={type:7,string:[36]},e.rules[64].opcodes[3]={type:7,string:[38]},e.rules[64].opcodes[4]={type:7,string:[39]},e.rules[64].opcodes[5]={type:7,string:[40]},e.rules[64].opcodes[6]={type:7,string:[41]},e.rules[64].opcodes[7]={type:7,string:[42]},e.rules[64].opcodes[8]={type:7,string:[43]},e.rules[64].opcodes[9]={type:7,string:[44]},e.rules[64].opcodes[10]={type:7,string:[59]},e.rules[64].opcodes[11]={type:7,string:[61]},e.rules[65].opcodes=[],e.rules[65].opcodes[0]={type:2,children:[1,2,3,4,5,10]},e.rules[65].opcodes[1]={type:4,index:67},e.rules[65].opcodes[2]={type:6,string:[32,119,97,110,116,115,32,121,111,117,32,116,111,32,115,105,103,110,32,105,110,32,119,105,116,104,32,121,111,117,114,32,83,111,108,97,110,97,32,97,99,99,111,117,110,116,58]},e.rules[65].opcodes[3]={type:4,index:10},e.rules[65].opcodes[4]={type:4,index:68},e.rules[65].opcodes[5]={type:3,min:0,max:1},e.rules[65].opcodes[6]={type:2,children:[7,8,9]},e.rules[65].opcodes[7]={type:4,index:10},e.rules[65].opcodes[8]={type:4,index:10},e.rules[65].opcodes[9]={type:4,index:69},e.rules[65].opcodes[10]={type:3,min:0,max:1},e.rules[65].opcodes[11]={type:2,children:[12,13]},e.rules[65].opcodes[12]={type:4,index:10},e.rules[65].opcodes[13]={type:4,index:66},e.rules[66].opcodes=[],e.rules[66].opcodes[0]={type:2,children:[1,6,11,16,21,26,31,36,41]},e.rules[66].opcodes[1]={type:3,min:0,max:1},e.rules[66].opcodes[2]={type:2,children:[3,4,5]},e.rules[66].opcodes[3]={type:4,index:10},e.rules[66].opcodes[4]={type:6,string:[85,82,73,58,32]},e.rules[66].opcodes[5]={type:4,index:70},e.rules[66].opcodes[6]={type:3,min:0,max:1},e.rules[66].opcodes[7]={type:2,children:[8,9,10]},e.rules[66].opcodes[8]={type:4,index:10},e.rules[66].opcodes[9]={type:6,string:[86,101,114,115,105,111,110,58,32]},e.rules[66].opcodes[10]={type:4,index:71},e.rules[66].opcodes[11]={type:3,min:0,max:1},e.rules[66].opcodes[12]={type:2,children:[13,14,15]},e.rules[66].opcodes[13]={type:4,index:10},e.rules[66].opcodes[14]={type:6,string:[67,104,97,105,110,32,73,68,58,32]},e.rules[66].opcodes[15]={type:4,index:72},e.rules[66].opcodes[16]={type:3,min:0,max:1},e.rules[66].opcodes[17]={type:2,children:[18,19,20]},e.rules[66].opcodes[18]={type:4,index:10},e.rules[66].opcodes[19]={type:6,string:[78,111,110,99,101,58,32]},e.rules[66].opcodes[20]={type:4,index:73},e.rules[66].opcodes[21]={type:3,min:0,max:1},e.rules[66].opcodes[22]={type:2,children:[23,24,25]},e.rules[66].opcodes[23]={type:4,index:10},e.rules[66].opcodes[24]={type:6,string:[73,115,115,117,101,100,32,65,116,58,32]},e.rules[66].opcodes[25]={type:4,index:74},e.rules[66].opcodes[26]={type:3,min:0,max:1},e.rules[66].opcodes[27]={type:2,children:[28,29,30]},e.rules[66].opcodes[28]={type:4,index:10},e.rules[66].opcodes[29]={type:6,string:[69,120,112,105,114,97,116,105,111,110,32,84,105,109,101,58,32]},e.rules[66].opcodes[30]={type:4,index:75},e.rules[66].opcodes[31]={type:3,min:0,max:1},e.rules[66].opcodes[32]={type:2,children:[33,34,35]},e.rules[66].opcodes[33]={type:4,index:10},e.rules[66].opcodes[34]={type:6,string:[78,111,116,32,66,101,102,111,114,101,58,32]},e.rules[66].opcodes[35]={type:4,index:76},e.rules[66].opcodes[36]={type:3,min:0,max:1},e.rules[66].opcodes[37]={type:2,children:[38,39,40]},e.rules[66].opcodes[38]={type:4,index:10},e.rules[66].opcodes[39]={type:6,string:[82,101,113,117,101,115,116,32,73,68,58,32]},e.rules[66].opcodes[40]={type:4,index:77},e.rules[66].opcodes[41]={type:3,min:0,max:1},e.rules[66].opcodes[42]={type:2,children:[43,44,45]},e.rules[66].opcodes[43]={type:4,index:10},e.rules[66].opcodes[44]={type:6,string:[82,101,115,111,117,114,99,101,115,58]},e.rules[66].opcodes[45]={type:4,index:78},e.rules[67].opcodes=[],e.rules[67].opcodes[0]={type:4,index:36},e.rules[68].opcodes=[],e.rules[68].opcodes[0]={type:3,min:32,max:44},e.rules[68].opcodes[1]={type:1,children:[2,3,4,5,6,7]},e.rules[68].opcodes[2]={type:5,min:49,max:57},e.rules[68].opcodes[3]={type:5,min:65,max:72},e.rules[68].opcodes[4]={type:5,min:74,max:78},e.rules[68].opcodes[5]={type:5,min:80,max:90},e.rules[68].opcodes[6]={type:5,min:97,max:107},e.rules[68].opcodes[7]={type:5,min:109,max:122},e.rules[69].opcodes=[],e.rules[69].opcodes[0]={type:3,min:1,max:1/0},e.rules[69].opcodes[1]={type:1,children:[2,3,4]},e.rules[69].opcodes[2]={type:4,index:62},e.rules[69].opcodes[3]={type:4,index:61},e.rules[69].opcodes[4]={type:7,string:[32]},e.rules[70].opcodes=[],e.rules[70].opcodes[0]={type:4,index:29},e.rules[71].opcodes=[],e.rules[71].opcodes[0]={type:7,string:[49]},e.rules[72].opcodes=[],e.rules[72].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[72].opcodes[1]={type:6,string:[109,97,105,110,110,101,116]},e.rules[72].opcodes[2]={type:6,string:[116,101,115,116,110,101,116]},e.rules[72].opcodes[3]={type:6,string:[100,101,118,110,101,116]},e.rules[72].opcodes[4]={type:6,string:[108,111,99,97,108,110,101,116]},e.rules[72].opcodes[5]={type:6,string:[115,111,108,97,110,97,58,109,97,105,110,110,101,116]},e.rules[72].opcodes[6]={type:6,string:[115,111,108,97,110,97,58,116,101,115,116,110,101,116]},e.rules[72].opcodes[7]={type:6,string:[115,111,108,97,110,97,58,100,101,118,110,101,116]},e.rules[73].opcodes=[],e.rules[73].opcodes[0]={type:3,min:8,max:1/0},e.rules[73].opcodes[1]={type:1,children:[2,3]},e.rules[73].opcodes[2]={type:4,index:0},e.rules[73].opcodes[3]={type:4,index:6},e.rules[74].opcodes=[],e.rules[74].opcodes[0]={type:4,index:28},e.rules[75].opcodes=[],e.rules[75].opcodes[0]={type:4,index:28},e.rules[76].opcodes=[],e.rules[76].opcodes[0]={type:4,index:28},e.rules[77].opcodes=[],e.rules[77].opcodes[0]={type:3,min:0,max:1/0},e.rules[77].opcodes[1]={type:4,index:57},e.rules[78].opcodes=[],e.rules[78].opcodes[0]={type:3,min:0,max:1/0},e.rules[78].opcodes[1]={type:2,children:[2,3,4]},e.rules[78].opcodes[2]={type:4,index:10},e.rules[78].opcodes[3]={type:7,string:[45,32]},e.rules[78].opcodes[4]={type:4,index:29},e.toString=function(){let r="";return r+=`; ==============================================================================
`,r+=`; Core rules for ABNF (RFC 5234)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; References:
`,r+=`; - https://www.rfc-editor.org/rfc/rfc5234#appendix-B.1
`,r+=`
`,r+=`ALPHA          =  %x41-5A / %x61-7A
`,r+=`               ; A-Z / a-z
`,r+=`
`,r+=`BIT            =  "0" / "1"
`,r+=`
`,r+=`CHAR           =  %x01-7F
`,r+=`               ; any 7-bit US-ASCII character,
`,r+=`               ;  excluding NUL
`,r+=`
`,r+=`CR             =  %x0D
`,r+=`               ; carriage return
`,r+=`
`,r+=`CRLF           =  CR LF
`,r+=`               ; Internet standard newline
`,r+=`
`,r+=`CTL            =  %x00-1F / %x7F
`,r+=`               ; controls
`,r+=`
`,r+=`DIGIT          =  %x30-39
`,r+=`               ; 0-9
`,r+=`
`,r+=`DQUOTE         =  %x22
`,r+=`               ; " (Double Quote)
`,r+=`
`,r+=`HEXDIG         =  DIGIT / "A" / "B" / "C" / "D" / "E" / "F"
`,r+=`
`,r+=`HTAB           =  %x09
`,r+=`               ; horizontal tab
`,r+=`
`,r+=`LF             =  %x0A
`,r+=`               ; linefeed
`,r+=`
`,r+=`LWSP           =  *(WSP / CRLF WSP)
`,r+=`               ; Use of this linear-white-space rule
`,r+=`               ;  permits lines containing only white
`,r+=`               ;  space that are no longer legal in
`,r+=`               ;  mail headers and have caused
`,r+=`               ;  interoperability problems in other
`,r+=`               ;  contexts.
`,r+=`               ; Do not use when defining mail
`,r+=`               ;  headers and use with caution in
`,r+=`               ;  other contexts.
`,r+=`
`,r+=`OCTET          =  %x00-FF
`,r+=`               ; 8 bits of data
`,r+=`
`,r+=`SP             =  %x20
`,r+=`
`,r+=`VCHAR          =  %x21-7E
`,r+=`               ; visible (printing) characters
`,r+=`
`,r+=`WSP            =  SP / HTAB
`,r+=`               ; white space
`,r+=`; ==============================================================================
`,r+=`; Internet Date/Time Format (RFC 3339)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; Requires:
`,r+=`; - core.bnf
`,r+=`;
`,r+=`; References:
`,r+=`; - https://www.rfc-editor.org/rfc/rfc3339#section-5.6
`,r+=`
`,r+=`date-fullyear   = 4DIGIT
`,r+=`
`,r+=`date-month      = 2DIGIT  ; 01-12
`,r+=`
`,r+=`date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on
`,r+=`                          ; month/year
`,r+=`
`,r+=`time-hour       = 2DIGIT  ; 00-23
`,r+=`
`,r+=`time-minute     = 2DIGIT  ; 00-59
`,r+=`
`,r+=`time-second     = 2DIGIT  ; 00-58, 00-59, 00-60 based on leap second
`,r+=`                          ; rules
`,r+=`
`,r+=`time-secfrac    = "." 1*DIGIT
`,r+=`
`,r+=`time-numoffset  = ("+" / "-") time-hour ":" time-minute
`,r+=`
`,r+=`time-offset     = "Z" / time-numoffset
`,r+=`
`,r+=`partial-time    = time-hour ":" time-minute ":" time-second
`,r+=`                  [time-secfrac]
`,r+=`
`,r+=`full-date       = date-fullyear "-" date-month "-" date-mday
`,r+=`
`,r+=`full-time       = partial-time time-offset
`,r+=`
`,r+=`date-time       = full-date "T" full-time
`,r+=`; ==============================================================================
`,r+=`; Uniform Resource Identifier (RFC 3986)
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; Requires:
`,r+=`; - core.bnf
`,r+=`;
`,r+=`; References:
`,r+=`; - https://www.rfc-editor.org/rfc/rfc3986#appendix-A
`,r+=`
`,r+=`URI           = scheme ":" hier-part [ "?" query ] [ "#" fragment ]
`,r+=`
`,r+=`hier-part     = "//" authority path-abempty
`,r+=`              / path-absolute
`,r+=`              / path-rootless
`,r+=`              / path-empty
`,r+=`
`,r+=`URI-reference = URI / relative-ref
`,r+=`
`,r+=`absolute-URI  = scheme ":" hier-part [ "?" query ]
`,r+=`
`,r+=`relative-ref  = relative-part [ "?" query ] [ "#" fragment ]
`,r+=`
`,r+=`relative-part = "//" authority path-abempty
`,r+=`              / path-absolute
`,r+=`              / path-noscheme
`,r+=`              / path-empty
`,r+=`
`,r+=`scheme        = ALPHA *( ALPHA / DIGIT / "+" / "-" / "." )
`,r+=`
`,r+=`authority     = [ userinfo "@" ] host [ ":" port ]
`,r+=`userinfo      = *( unreserved / pct-encoded / sub-delims / ":" )
`,r+=`host          = IP-literal / IPv4address / reg-name
`,r+=`port          = *DIGIT
`,r+=`
`,r+=`IP-literal    = "[" ( IPv6address / IPvFuture  ) "]"
`,r+=`
`,r+=`IPvFuture     = "v" 1*HEXDIG "." 1*( unreserved / sub-delims / ":" )
`,r+=`
`,r+=`IPv6address   =                            6( h16 ":" ) ls32
`,r+=`              /                       "::" 5( h16 ":" ) ls32
`,r+=`              / [               h16 ] "::" 4( h16 ":" ) ls32
`,r+=`              / [ *1( h16 ":" ) h16 ] "::" 3( h16 ":" ) ls32
`,r+=`              / [ *2( h16 ":" ) h16 ] "::" 2( h16 ":" ) ls32
`,r+=`              / [ *3( h16 ":" ) h16 ] "::"    h16 ":"   ls32
`,r+=`              / [ *4( h16 ":" ) h16 ] "::"              ls32
`,r+=`              / [ *5( h16 ":" ) h16 ] "::"              h16
`,r+=`              / [ *6( h16 ":" ) h16 ] "::"
`,r+=`
`,r+=`h16           = 1*4HEXDIG
`,r+=`ls32          = ( h16 ":" h16 ) / IPv4address
`,r+=`IPv4address   = dec-octet "." dec-octet "." dec-octet "." dec-octet
`,r+=`dec-octet     = DIGIT                 ; 0-9
`,r+=`              / %x31-39 DIGIT         ; 10-99
`,r+=`              / "1" 2DIGIT            ; 100-199
`,r+=`              / "2" %x30-34 DIGIT     ; 200-249
`,r+=`              / "25" %x30-35          ; 250-255
`,r+=`
`,r+=`reg-name      = *( unreserved / pct-encoded / sub-delims )
`,r+=`
`,r+=`path          = path-abempty    ; begins with "/" or is empty
`,r+=`              / path-absolute   ; begins with "/" but not "//"
`,r+=`              / path-noscheme   ; begins with a non-colon segment
`,r+=`              / path-rootless   ; begins with a segment
`,r+=`              / path-empty      ; zero characters
`,r+=`
`,r+=`path-abempty  = *( "/" segment )
`,r+=`path-absolute = "/" [ segment-nz *( "/" segment ) ]
`,r+=`path-noscheme = segment-nz-nc *( "/" segment )
`,r+=`path-rootless = segment-nz *( "/" segment )
`,r+=`path-empty    = 0pchar
`,r+=`
`,r+=`segment       = *pchar
`,r+=`segment-nz    = 1*pchar
`,r+=`segment-nz-nc = 1*( unreserved / pct-encoded / sub-delims / "@" )
`,r+=`; non-zero-length segment without any colon ":"
`,r+=`
`,r+=`pchar         = unreserved / pct-encoded / sub-delims / ":" / "@"
`,r+=`
`,r+=`query         = *( pchar / "/" / "?" )
`,r+=`
`,r+=`fragment      = *( pchar / "/" / "?" )
`,r+=`
`,r+=`pct-encoded   = "%" HEXDIG HEXDIG
`,r+=`
`,r+=`unreserved    = ALPHA / DIGIT / "-" / "." / "_" / "~"
`,r+=`reserved      = gen-delims / sub-delims
`,r+=`gen-delims    = ":" / "/" / "?" / "#" / "[" / "]" / "@"
`,r+=`sub-delims    = "!" / "$" / "&" / "'" / "(" / ")"
`,r+=`              / "*" / "+" / "," / ";" / "="
`,r+=`; ==============================================================================
`,r+=`; Sign in with Solana
`,r+=`; ==============================================================================
`,r+=`;
`,r+=`; Requires:
`,r+=`; - core.bnf
`,r+=`; - datetime.bnf
`,r+=`; - uri.bnf
`,r+=`; - dns.bnf
`,r+=`;
`,r+=`; References:
`,r+=`; - https://eips.ethereum.org/EIPS/eip-4361
`,r+=` 
`,r+=`sign-in-with-solana =
`,r+=`  message-domain %s" wants you to sign in with your Solana account:" LF
`,r+=`  message-address
`,r+=`  [ LF LF message-statement ]
`,r+=`  [ LF advanced-fields ]
`,r+=`
`,r+=`advanced-fields =
`,r+=`  [ LF %s"URI: " message-uri ]
`,r+=`  [ LF %s"Version: " message-version ]
`,r+=`  [ LF %s"Chain ID: " message-chain-id ]
`,r+=`  [ LF %s"Nonce: " message-nonce ]
`,r+=`  [ LF %s"Issued At: " message-issued-at ]
`,r+=`  [ LF %s"Expiration Time: " message-expiration-time ]
`,r+=`  [ LF %s"Not Before: " message-not-before ]
`,r+=`  [ LF %s"Request ID: " message-request-id ]
`,r+=`  [ LF %s"Resources:" message-resources ]
`,r+=`
`,r+=`message-domain          = authority
`,r+=`message-address         = 32*44( %x31-39 / %x41-48 / %x4A-4E / %x50-5A / %x61-6B / %x6D-7A )
`,r+=`message-statement       = 1*( reserved / unreserved / " " )
`,r+=`message-uri             = URI
`,r+=`message-version         = "1"
`,r+=`message-chain-id        = %s"mainnet" / %s"testnet" / %s"devnet" / %s"localnet" / %s"solana:mainnet" / %s"solana:testnet" / %s"solana:devnet"
`,r+=`message-nonce           = 8*( ALPHA / DIGIT )
`,r+=`message-issued-at       = date-time
`,r+=`message-expiration-time = date-time
`,r+=`message-not-before      = date-time
`,r+=`message-request-id      = *pchar
`,r+=`message-resources       = *( LF "- " URI )
`,r},e}p();d();function Ws(){let e={};return e.grammarObject="grammarObject",e.rules=[],e.rules[0]={name:"ALPHA",lower:"alpha",index:0,isBkr:!1},e.rules[1]={name:"BIT",lower:"bit",index:1,isBkr:!1},e.rules[2]={name:"CHAR",lower:"char",index:2,isBkr:!1},e.rules[3]={name:"CR",lower:"cr",index:3,isBkr:!1},e.rules[4]={name:"CRLF",lower:"crlf",index:4,isBkr:!1},e.rules[5]={name:"CTL",lower:"ctl",index:5,isBkr:!1},e.rules[6]={name:"DIGIT",lower:"digit",index:6,isBkr:!1},e.rules[7]={name:"DQUOTE",lower:"dquote",index:7,isBkr:!1},e.rules[8]={name:"HEXDIG",lower:"hexdig",index:8,isBkr:!1},e.rules[9]={name:"HTAB",lower:"htab",index:9,isBkr:!1},e.rules[10]={name:"LF",lower:"lf",index:10,isBkr:!1},e.rules[11]={name:"LWSP",lower:"lwsp",index:11,isBkr:!1},e.rules[12]={name:"OCTET",lower:"octet",index:12,isBkr:!1},e.rules[13]={name:"SP",lower:"sp",index:13,isBkr:!1},e.rules[14]={name:"VCHAR",lower:"vchar",index:14,isBkr:!1},e.rules[15]={name:"WSP",lower:"wsp",index:15,isBkr:!1},e.rules[16]={name:"date-fullyear",lower:"date-fullyear",index:16,isBkr:!1},e.rules[17]={name:"date-month",lower:"date-month",index:17,isBkr:!1},e.rules[18]={name:"date-mday",lower:"date-mday",index:18,isBkr:!1},e.rules[19]={name:"time-hour",lower:"time-hour",index:19,isBkr:!1},e.rules[20]={name:"time-minute",lower:"time-minute",index:20,isBkr:!1},e.rules[21]={name:"time-second",lower:"time-second",index:21,isBkr:!1},e.rules[22]={name:"time-secfrac",lower:"time-secfrac",index:22,isBkr:!1},e.rules[23]={name:"time-numoffset",lower:"time-numoffset",index:23,isBkr:!1},e.rules[24]={name:"time-offset",lower:"time-offset",index:24,isBkr:!1},e.rules[25]={name:"partial-time",lower:"partial-time",index:25,isBkr:!1},e.rules[26]={name:"full-date",lower:"full-date",index:26,isBkr:!1},e.rules[27]={name:"full-time",lower:"full-time",index:27,isBkr:!1},e.rules[28]={name:"date-time",lower:"date-time",index:28,isBkr:!1},e.rules[29]={name:"URI",lower:"uri",index:29,isBkr:!1},e.rules[30]={name:"hier-part",lower:"hier-part",index:30,isBkr:!1},e.rules[31]={name:"URI-reference",lower:"uri-reference",index:31,isBkr:!1},e.rules[32]={name:"absolute-URI",lower:"absolute-uri",index:32,isBkr:!1},e.rules[33]={name:"relative-ref",lower:"relative-ref",index:33,isBkr:!1},e.rules[34]={name:"relative-part",lower:"relative-part",index:34,isBkr:!1},e.rules[35]={name:"scheme",lower:"scheme",index:35,isBkr:!1},e.rules[36]={name:"authority",lower:"authority",index:36,isBkr:!1},e.rules[37]={name:"userinfo",lower:"userinfo",index:37,isBkr:!1},e.rules[38]={name:"host",lower:"host",index:38,isBkr:!1},e.rules[39]={name:"port",lower:"port",index:39,isBkr:!1},e.rules[40]={name:"IP-literal",lower:"ip-literal",index:40,isBkr:!1},e.rules[41]={name:"IPvFuture",lower:"ipvfuture",index:41,isBkr:!1},e.rules[42]={name:"IPv6address",lower:"ipv6address",index:42,isBkr:!1},e.rules[43]={name:"h16",lower:"h16",index:43,isBkr:!1},e.rules[44]={name:"ls32",lower:"ls32",index:44,isBkr:!1},e.rules[45]={name:"IPv4address",lower:"ipv4address",index:45,isBkr:!1},e.rules[46]={name:"dec-octet",lower:"dec-octet",index:46,isBkr:!1},e.rules[47]={name:"reg-name",lower:"reg-name",index:47,isBkr:!1},e.rules[48]={name:"path",lower:"path",index:48,isBkr:!1},e.rules[49]={name:"path-abempty",lower:"path-abempty",index:49,isBkr:!1},e.rules[50]={name:"path-absolute",lower:"path-absolute",index:50,isBkr:!1},e.rules[51]={name:"path-noscheme",lower:"path-noscheme",index:51,isBkr:!1},e.rules[52]={name:"path-rootless",lower:"path-rootless",index:52,isBkr:!1},e.rules[53]={name:"path-empty",lower:"path-empty",index:53,isBkr:!1},e.rules[54]={name:"segment",lower:"segment",index:54,isBkr:!1},e.rules[55]={name:"segment-nz",lower:"segment-nz",index:55,isBkr:!1},e.rules[56]={name:"segment-nz-nc",lower:"segment-nz-nc",index:56,isBkr:!1},e.rules[57]={name:"pchar",lower:"pchar",index:57,isBkr:!1},e.rules[58]={name:"query",lower:"query",index:58,isBkr:!1},e.rules[59]={name:"fragment",lower:"fragment",index:59,isBkr:!1},e.rules[60]={name:"pct-encoded",lower:"pct-encoded",index:60,isBkr:!1},e.rules[61]={name:"unreserved",lower:"unreserved",index:61,isBkr:!1},e.rules[62]={name:"reserved",lower:"reserved",index:62,isBkr:!1},e.rules[63]={name:"gen-delims",lower:"gen-delims",index:63,isBkr:!1},e.rules[64]={name:"sub-delims",lower:"sub-delims",index:64,isBkr:!1},e.rules[65]={name:"dnsurl",lower:"dnsurl",index:65,isBkr:!1},e.rules[66]={name:"dnsauthority",lower:"dnsauthority",index:66,isBkr:!1},e.rules[67]={name:"dnsname",lower:"dnsname",index:67,isBkr:!1},e.rules[68]={name:"dnsquery",lower:"dnsquery",index:68,isBkr:!1},e.rules[69]={name:"dnsqueryelement",lower:"dnsqueryelement",index:69,isBkr:!1},e.rules[70]={name:"dnsclassval",lower:"dnsclassval",index:70,isBkr:!1},e.rules[71]={name:"dnstypeval",lower:"dnstypeval",index:71,isBkr:!1},e.rules[72]={name:"chain-id",lower:"chain-id",index:72,isBkr:!1},e.rules[73]={name:"eth-namespace",lower:"eth-namespace",index:73,isBkr:!1},e.rules[74]={name:"eth-network-id",lower:"eth-network-id",index:74,isBkr:!1},e.rules[75]={name:"eth-chain-id",lower:"eth-chain-id",index:75,isBkr:!1},e.rules[76]={name:"sol-namespace",lower:"sol-namespace",index:76,isBkr:!1},e.rules[77]={name:"sol-network-id",lower:"sol-network-id",index:77,isBkr:!1},e.rules[78]={name:"sol-chain-id",lower:"sol-chain-id",index:78,isBkr:!1},e.rules[79]={name:"account-id",lower:"account-id",index:79,isBkr:!1},e.rules[80]={name:"eth-address",lower:"eth-address",index:80,isBkr:!1},e.rules[81]={name:"eth-account-id",lower:"eth-account-id",index:81,isBkr:!1},e.rules[82]={name:"sol-address",lower:"sol-address",index:82,isBkr:!1},e.rules[83]={name:"sol-account-id",lower:"sol-account-id",index:83,isBkr:!1},e.rules[84]={name:"sign-in-with-x",lower:"sign-in-with-x",index:84,isBkr:!1},e.rules[85]={name:"sign-in-with-eth",lower:"sign-in-with-eth",index:85,isBkr:!1},e.rules[86]={name:"sign-in-with-sol",lower:"sign-in-with-sol",index:86,isBkr:!1},e.rules[87]={name:"message-domain",lower:"message-domain",index:87,isBkr:!1},e.rules[88]={name:"message-address",lower:"message-address",index:88,isBkr:!1},e.rules[89]={name:"message-statement",lower:"message-statement",index:89,isBkr:!1},e.rules[90]={name:"message-uri",lower:"message-uri",index:90,isBkr:!1},e.rules[91]={name:"message-version",lower:"message-version",index:91,isBkr:!1},e.rules[92]={name:"message-chain-id",lower:"message-chain-id",index:92,isBkr:!1},e.rules[93]={name:"message-nonce",lower:"message-nonce",index:93,isBkr:!1},e.rules[94]={name:"message-issued-at",lower:"message-issued-at",index:94,isBkr:!1},e.rules[95]={name:"message-expiration-time",lower:"message-expiration-time",index:95,isBkr:!1},e.rules[96]={name:"message-not-before",lower:"message-not-before",index:96,isBkr:!1},e.rules[97]={name:"message-request-id",lower:"message-request-id",index:97,isBkr:!1},e.rules[98]={name:"message-resources",lower:"message-resources",index:98,isBkr:!1},e.udts=[],e.rules[0].opcodes=[],e.rules[0].opcodes[0]={type:1,children:[1,2]},e.rules[0].opcodes[1]={type:5,min:65,max:90},e.rules[0].opcodes[2]={type:5,min:97,max:122},e.rules[1].opcodes=[],e.rules[1].opcodes[0]={type:1,children:[1,2]},e.rules[1].opcodes[1]={type:7,string:[48]},e.rules[1].opcodes[2]={type:7,string:[49]},e.rules[2].opcodes=[],e.rules[2].opcodes[0]={type:5,min:1,max:127},e.rules[3].opcodes=[],e.rules[3].opcodes[0]={type:6,string:[13]},e.rules[4].opcodes=[],e.rules[4].opcodes[0]={type:2,children:[1,2]},e.rules[4].opcodes[1]={type:4,index:3},e.rules[4].opcodes[2]={type:4,index:10},e.rules[5].opcodes=[],e.rules[5].opcodes[0]={type:1,children:[1,2]},e.rules[5].opcodes[1]={type:5,min:0,max:31},e.rules[5].opcodes[2]={type:6,string:[127]},e.rules[6].opcodes=[],e.rules[6].opcodes[0]={type:5,min:48,max:57},e.rules[7].opcodes=[],e.rules[7].opcodes[0]={type:6,string:[34]},e.rules[8].opcodes=[],e.rules[8].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[8].opcodes[1]={type:4,index:6},e.rules[8].opcodes[2]={type:7,string:[97]},e.rules[8].opcodes[3]={type:7,string:[98]},e.rules[8].opcodes[4]={type:7,string:[99]},e.rules[8].opcodes[5]={type:7,string:[100]},e.rules[8].opcodes[6]={type:7,string:[101]},e.rules[8].opcodes[7]={type:7,string:[102]},e.rules[9].opcodes=[],e.rules[9].opcodes[0]={type:6,string:[9]},e.rules[10].opcodes=[],e.rules[10].opcodes[0]={type:6,string:[10]},e.rules[11].opcodes=[],e.rules[11].opcodes[0]={type:3,min:0,max:1/0},e.rules[11].opcodes[1]={type:1,children:[2,3]},e.rules[11].opcodes[2]={type:4,index:15},e.rules[11].opcodes[3]={type:2,children:[4,5]},e.rules[11].opcodes[4]={type:4,index:4},e.rules[11].opcodes[5]={type:4,index:15},e.rules[12].opcodes=[],e.rules[12].opcodes[0]={type:5,min:0,max:255},e.rules[13].opcodes=[],e.rules[13].opcodes[0]={type:6,string:[32]},e.rules[14].opcodes=[],e.rules[14].opcodes[0]={type:5,min:33,max:126},e.rules[15].opcodes=[],e.rules[15].opcodes[0]={type:1,children:[1,2]},e.rules[15].opcodes[1]={type:4,index:13},e.rules[15].opcodes[2]={type:4,index:9},e.rules[16].opcodes=[],e.rules[16].opcodes[0]={type:3,min:4,max:4},e.rules[16].opcodes[1]={type:4,index:6},e.rules[17].opcodes=[],e.rules[17].opcodes[0]={type:3,min:2,max:2},e.rules[17].opcodes[1]={type:4,index:6},e.rules[18].opcodes=[],e.rules[18].opcodes[0]={type:3,min:2,max:2},e.rules[18].opcodes[1]={type:4,index:6},e.rules[19].opcodes=[],e.rules[19].opcodes[0]={type:3,min:2,max:2},e.rules[19].opcodes[1]={type:4,index:6},e.rules[20].opcodes=[],e.rules[20].opcodes[0]={type:3,min:2,max:2},e.rules[20].opcodes[1]={type:4,index:6},e.rules[21].opcodes=[],e.rules[21].opcodes[0]={type:3,min:2,max:2},e.rules[21].opcodes[1]={type:4,index:6},e.rules[22].opcodes=[],e.rules[22].opcodes[0]={type:2,children:[1,2]},e.rules[22].opcodes[1]={type:7,string:[46]},e.rules[22].opcodes[2]={type:3,min:1,max:1/0},e.rules[22].opcodes[3]={type:4,index:6},e.rules[23].opcodes=[],e.rules[23].opcodes[0]={type:2,children:[1,4,5,6]},e.rules[23].opcodes[1]={type:1,children:[2,3]},e.rules[23].opcodes[2]={type:7,string:[43]},e.rules[23].opcodes[3]={type:7,string:[45]},e.rules[23].opcodes[4]={type:4,index:19},e.rules[23].opcodes[5]={type:7,string:[58]},e.rules[23].opcodes[6]={type:4,index:20},e.rules[24].opcodes=[],e.rules[24].opcodes[0]={type:1,children:[1,2]},e.rules[24].opcodes[1]={type:7,string:[122]},e.rules[24].opcodes[2]={type:4,index:23},e.rules[25].opcodes=[],e.rules[25].opcodes[0]={type:2,children:[1,2,3,4,5,6]},e.rules[25].opcodes[1]={type:4,index:19},e.rules[25].opcodes[2]={type:7,string:[58]},e.rules[25].opcodes[3]={type:4,index:20},e.rules[25].opcodes[4]={type:7,string:[58]},e.rules[25].opcodes[5]={type:4,index:21},e.rules[25].opcodes[6]={type:3,min:0,max:1},e.rules[25].opcodes[7]={type:4,index:22},e.rules[26].opcodes=[],e.rules[26].opcodes[0]={type:2,children:[1,2,3,4,5]},e.rules[26].opcodes[1]={type:4,index:16},e.rules[26].opcodes[2]={type:7,string:[45]},e.rules[26].opcodes[3]={type:4,index:17},e.rules[26].opcodes[4]={type:7,string:[45]},e.rules[26].opcodes[5]={type:4,index:18},e.rules[27].opcodes=[],e.rules[27].opcodes[0]={type:2,children:[1,2]},e.rules[27].opcodes[1]={type:4,index:25},e.rules[27].opcodes[2]={type:4,index:24},e.rules[28].opcodes=[],e.rules[28].opcodes[0]={type:2,children:[1,2,3]},e.rules[28].opcodes[1]={type:4,index:26},e.rules[28].opcodes[2]={type:7,string:[116]},e.rules[28].opcodes[3]={type:4,index:27},e.rules[29].opcodes=[],e.rules[29].opcodes[0]={type:2,children:[1,2,3,4,8]},e.rules[29].opcodes[1]={type:4,index:35},e.rules[29].opcodes[2]={type:7,string:[58]},e.rules[29].opcodes[3]={type:4,index:30},e.rules[29].opcodes[4]={type:3,min:0,max:1},e.rules[29].opcodes[5]={type:2,children:[6,7]},e.rules[29].opcodes[6]={type:7,string:[63]},e.rules[29].opcodes[7]={type:4,index:58},e.rules[29].opcodes[8]={type:3,min:0,max:1},e.rules[29].opcodes[9]={type:2,children:[10,11]},e.rules[29].opcodes[10]={type:7,string:[35]},e.rules[29].opcodes[11]={type:4,index:59},e.rules[30].opcodes=[],e.rules[30].opcodes[0]={type:1,children:[1,5,6,7]},e.rules[30].opcodes[1]={type:2,children:[2,3,4]},e.rules[30].opcodes[2]={type:7,string:[47,47]},e.rules[30].opcodes[3]={type:4,index:36},e.rules[30].opcodes[4]={type:4,index:49},e.rules[30].opcodes[5]={type:4,index:50},e.rules[30].opcodes[6]={type:4,index:52},e.rules[30].opcodes[7]={type:4,index:53},e.rules[31].opcodes=[],e.rules[31].opcodes[0]={type:1,children:[1,2]},e.rules[31].opcodes[1]={type:4,index:29},e.rules[31].opcodes[2]={type:4,index:33},e.rules[32].opcodes=[],e.rules[32].opcodes[0]={type:2,children:[1,2,3,4]},e.rules[32].opcodes[1]={type:4,index:35},e.rules[32].opcodes[2]={type:7,string:[58]},e.rules[32].opcodes[3]={type:4,index:30},e.rules[32].opcodes[4]={type:3,min:0,max:1},e.rules[32].opcodes[5]={type:2,children:[6,7]},e.rules[32].opcodes[6]={type:7,string:[63]},e.rules[32].opcodes[7]={type:4,index:58},e.rules[33].opcodes=[],e.rules[33].opcodes[0]={type:2,children:[1,2,6]},e.rules[33].opcodes[1]={type:4,index:34},e.rules[33].opcodes[2]={type:3,min:0,max:1},e.rules[33].opcodes[3]={type:2,children:[4,5]},e.rules[33].opcodes[4]={type:7,string:[63]},e.rules[33].opcodes[5]={type:4,index:58},e.rules[33].opcodes[6]={type:3,min:0,max:1},e.rules[33].opcodes[7]={type:2,children:[8,9]},e.rules[33].opcodes[8]={type:7,string:[35]},e.rules[33].opcodes[9]={type:4,index:59},e.rules[34].opcodes=[],e.rules[34].opcodes[0]={type:1,children:[1,5,6,7]},e.rules[34].opcodes[1]={type:2,children:[2,3,4]},e.rules[34].opcodes[2]={type:7,string:[47,47]},e.rules[34].opcodes[3]={type:4,index:36},e.rules[34].opcodes[4]={type:4,index:49},e.rules[34].opcodes[5]={type:4,index:50},e.rules[34].opcodes[6]={type:4,index:51},e.rules[34].opcodes[7]={type:4,index:53},e.rules[35].opcodes=[],e.rules[35].opcodes[0]={type:2,children:[1,2]},e.rules[35].opcodes[1]={type:4,index:0},e.rules[35].opcodes[2]={type:3,min:0,max:1/0},e.rules[35].opcodes[3]={type:1,children:[4,5,6,7,8]},e.rules[35].opcodes[4]={type:4,index:0},e.rules[35].opcodes[5]={type:4,index:6},e.rules[35].opcodes[6]={type:7,string:[43]},e.rules[35].opcodes[7]={type:7,string:[45]},e.rules[35].opcodes[8]={type:7,string:[46]},e.rules[36].opcodes=[],e.rules[36].opcodes[0]={type:2,children:[1,5,6]},e.rules[36].opcodes[1]={type:3,min:0,max:1},e.rules[36].opcodes[2]={type:2,children:[3,4]},e.rules[36].opcodes[3]={type:4,index:37},e.rules[36].opcodes[4]={type:7,string:[64]},e.rules[36].opcodes[5]={type:4,index:38},e.rules[36].opcodes[6]={type:3,min:0,max:1},e.rules[36].opcodes[7]={type:2,children:[8,9]},e.rules[36].opcodes[8]={type:7,string:[58]},e.rules[36].opcodes[9]={type:4,index:39},e.rules[37].opcodes=[],e.rules[37].opcodes[0]={type:3,min:0,max:1/0},e.rules[37].opcodes[1]={type:1,children:[2,3,4,5]},e.rules[37].opcodes[2]={type:4,index:61},e.rules[37].opcodes[3]={type:4,index:60},e.rules[37].opcodes[4]={type:4,index:64},e.rules[37].opcodes[5]={type:7,string:[58]},e.rules[38].opcodes=[],e.rules[38].opcodes[0]={type:1,children:[1,2,3]},e.rules[38].opcodes[1]={type:4,index:40},e.rules[38].opcodes[2]={type:4,index:45},e.rules[38].opcodes[3]={type:4,index:47},e.rules[39].opcodes=[],e.rules[39].opcodes[0]={type:3,min:0,max:1/0},e.rules[39].opcodes[1]={type:4,index:6},e.rules[40].opcodes=[],e.rules[40].opcodes[0]={type:2,children:[1,2,5]},e.rules[40].opcodes[1]={type:7,string:[91]},e.rules[40].opcodes[2]={type:1,children:[3,4]},e.rules[40].opcodes[3]={type:4,index:42},e.rules[40].opcodes[4]={type:4,index:41},e.rules[40].opcodes[5]={type:7,string:[93]},e.rules[41].opcodes=[],e.rules[41].opcodes[0]={type:2,children:[1,2,4,5]},e.rules[41].opcodes[1]={type:7,string:[118]},e.rules[41].opcodes[2]={type:3,min:1,max:1/0},e.rules[41].opcodes[3]={type:4,index:8},e.rules[41].opcodes[4]={type:7,string:[46]},e.rules[41].opcodes[5]={type:3,min:1,max:1/0},e.rules[41].opcodes[6]={type:1,children:[7,8,9]},e.rules[41].opcodes[7]={type:4,index:61},e.rules[41].opcodes[8]={type:4,index:64},e.rules[41].opcodes[9]={type:7,string:[58]},e.rules[42].opcodes=[],e.rules[42].opcodes[0]={type:1,children:[1,7,14,23,37,51,63,73,83]},e.rules[42].opcodes[1]={type:2,children:[2,6]},e.rules[42].opcodes[2]={type:3,min:6,max:6},e.rules[42].opcodes[3]={type:2,children:[4,5]},e.rules[42].opcodes[4]={type:4,index:43},e.rules[42].opcodes[5]={type:7,string:[58]},e.rules[42].opcodes[6]={type:4,index:44},e.rules[42].opcodes[7]={type:2,children:[8,9,13]},e.rules[42].opcodes[8]={type:7,string:[58,58]},e.rules[42].opcodes[9]={type:3,min:5,max:5},e.rules[42].opcodes[10]={type:2,children:[11,12]},e.rules[42].opcodes[11]={type:4,index:43},e.rules[42].opcodes[12]={type:7,string:[58]},e.rules[42].opcodes[13]={type:4,index:44},e.rules[42].opcodes[14]={type:2,children:[15,17,18,22]},e.rules[42].opcodes[15]={type:3,min:0,max:1},e.rules[42].opcodes[16]={type:4,index:43},e.rules[42].opcodes[17]={type:7,string:[58,58]},e.rules[42].opcodes[18]={type:3,min:4,max:4},e.rules[42].opcodes[19]={type:2,children:[20,21]},e.rules[42].opcodes[20]={type:4,index:43},e.rules[42].opcodes[21]={type:7,string:[58]},e.rules[42].opcodes[22]={type:4,index:44},e.rules[42].opcodes[23]={type:2,children:[24,31,32,36]},e.rules[42].opcodes[24]={type:3,min:0,max:1},e.rules[42].opcodes[25]={type:2,children:[26,30]},e.rules[42].opcodes[26]={type:3,min:0,max:1},e.rules[42].opcodes[27]={type:2,children:[28,29]},e.rules[42].opcodes[28]={type:4,index:43},e.rules[42].opcodes[29]={type:7,string:[58]},e.rules[42].opcodes[30]={type:4,index:43},e.rules[42].opcodes[31]={type:7,string:[58,58]},e.rules[42].opcodes[32]={type:3,min:3,max:3},e.rules[42].opcodes[33]={type:2,children:[34,35]},e.rules[42].opcodes[34]={type:4,index:43},e.rules[42].opcodes[35]={type:7,string:[58]},e.rules[42].opcodes[36]={type:4,index:44},e.rules[42].opcodes[37]={type:2,children:[38,45,46,50]},e.rules[42].opcodes[38]={type:3,min:0,max:1},e.rules[42].opcodes[39]={type:2,children:[40,44]},e.rules[42].opcodes[40]={type:3,min:0,max:2},e.rules[42].opcodes[41]={type:2,children:[42,43]},e.rules[42].opcodes[42]={type:4,index:43},e.rules[42].opcodes[43]={type:7,string:[58]},e.rules[42].opcodes[44]={type:4,index:43},e.rules[42].opcodes[45]={type:7,string:[58,58]},e.rules[42].opcodes[46]={type:3,min:2,max:2},e.rules[42].opcodes[47]={type:2,children:[48,49]},e.rules[42].opcodes[48]={type:4,index:43},e.rules[42].opcodes[49]={type:7,string:[58]},e.rules[42].opcodes[50]={type:4,index:44},e.rules[42].opcodes[51]={type:2,children:[52,59,60,61,62]},e.rules[42].opcodes[52]={type:3,min:0,max:1},e.rules[42].opcodes[53]={type:2,children:[54,58]},e.rules[42].opcodes[54]={type:3,min:0,max:3},e.rules[42].opcodes[55]={type:2,children:[56,57]},e.rules[42].opcodes[56]={type:4,index:43},e.rules[42].opcodes[57]={type:7,string:[58]},e.rules[42].opcodes[58]={type:4,index:43},e.rules[42].opcodes[59]={type:7,string:[58,58]},e.rules[42].opcodes[60]={type:4,index:43},e.rules[42].opcodes[61]={type:7,string:[58]},e.rules[42].opcodes[62]={type:4,index:44},e.rules[42].opcodes[63]={type:2,children:[64,71,72]},e.rules[42].opcodes[64]={type:3,min:0,max:1},e.rules[42].opcodes[65]={type:2,children:[66,70]},e.rules[42].opcodes[66]={type:3,min:0,max:4},e.rules[42].opcodes[67]={type:2,children:[68,69]},e.rules[42].opcodes[68]={type:4,index:43},e.rules[42].opcodes[69]={type:7,string:[58]},e.rules[42].opcodes[70]={type:4,index:43},e.rules[42].opcodes[71]={type:7,string:[58,58]},e.rules[42].opcodes[72]={type:4,index:44},e.rules[42].opcodes[73]={type:2,children:[74,81,82]},e.rules[42].opcodes[74]={type:3,min:0,max:1},e.rules[42].opcodes[75]={type:2,children:[76,80]},e.rules[42].opcodes[76]={type:3,min:0,max:5},e.rules[42].opcodes[77]={type:2,children:[78,79]},e.rules[42].opcodes[78]={type:4,index:43},e.rules[42].opcodes[79]={type:7,string:[58]},e.rules[42].opcodes[80]={type:4,index:43},e.rules[42].opcodes[81]={type:7,string:[58,58]},e.rules[42].opcodes[82]={type:4,index:43},e.rules[42].opcodes[83]={type:2,children:[84,91]},e.rules[42].opcodes[84]={type:3,min:0,max:1},e.rules[42].opcodes[85]={type:2,children:[86,90]},e.rules[42].opcodes[86]={type:3,min:0,max:6},e.rules[42].opcodes[87]={type:2,children:[88,89]},e.rules[42].opcodes[88]={type:4,index:43},e.rules[42].opcodes[89]={type:7,string:[58]},e.rules[42].opcodes[90]={type:4,index:43},e.rules[42].opcodes[91]={type:7,string:[58,58]},e.rules[43].opcodes=[],e.rules[43].opcodes[0]={type:3,min:1,max:4},e.rules[43].opcodes[1]={type:4,index:8},e.rules[44].opcodes=[],e.rules[44].opcodes[0]={type:1,children:[1,5]},e.rules[44].opcodes[1]={type:2,children:[2,3,4]},e.rules[44].opcodes[2]={type:4,index:43},e.rules[44].opcodes[3]={type:7,string:[58]},e.rules[44].opcodes[4]={type:4,index:43},e.rules[44].opcodes[5]={type:4,index:45},e.rules[45].opcodes=[],e.rules[45].opcodes[0]={type:2,children:[1,2,3,4,5,6,7]},e.rules[45].opcodes[1]={type:4,index:46},e.rules[45].opcodes[2]={type:7,string:[46]},e.rules[45].opcodes[3]={type:4,index:46},e.rules[45].opcodes[4]={type:7,string:[46]},e.rules[45].opcodes[5]={type:4,index:46},e.rules[45].opcodes[6]={type:7,string:[46]},e.rules[45].opcodes[7]={type:4,index:46},e.rules[46].opcodes=[],e.rules[46].opcodes[0]={type:1,children:[1,2,5,9,13]},e.rules[46].opcodes[1]={type:4,index:6},e.rules[46].opcodes[2]={type:2,children:[3,4]},e.rules[46].opcodes[3]={type:5,min:49,max:57},e.rules[46].opcodes[4]={type:4,index:6},e.rules[46].opcodes[5]={type:2,children:[6,7]},e.rules[46].opcodes[6]={type:7,string:[49]},e.rules[46].opcodes[7]={type:3,min:2,max:2},e.rules[46].opcodes[8]={type:4,index:6},e.rules[46].opcodes[9]={type:2,children:[10,11,12]},e.rules[46].opcodes[10]={type:7,string:[50]},e.rules[46].opcodes[11]={type:5,min:48,max:52},e.rules[46].opcodes[12]={type:4,index:6},e.rules[46].opcodes[13]={type:2,children:[14,15]},e.rules[46].opcodes[14]={type:7,string:[50,53]},e.rules[46].opcodes[15]={type:5,min:48,max:53},e.rules[47].opcodes=[],e.rules[47].opcodes[0]={type:3,min:0,max:1/0},e.rules[47].opcodes[1]={type:1,children:[2,3,4]},e.rules[47].opcodes[2]={type:4,index:61},e.rules[47].opcodes[3]={type:4,index:60},e.rules[47].opcodes[4]={type:4,index:64},e.rules[48].opcodes=[],e.rules[48].opcodes[0]={type:1,children:[1,2,3,4,5]},e.rules[48].opcodes[1]={type:4,index:49},e.rules[48].opcodes[2]={type:4,index:50},e.rules[48].opcodes[3]={type:4,index:51},e.rules[48].opcodes[4]={type:4,index:52},e.rules[48].opcodes[5]={type:4,index:53},e.rules[49].opcodes=[],e.rules[49].opcodes[0]={type:3,min:0,max:1/0},e.rules[49].opcodes[1]={type:2,children:[2,3]},e.rules[49].opcodes[2]={type:7,string:[47]},e.rules[49].opcodes[3]={type:4,index:54},e.rules[50].opcodes=[],e.rules[50].opcodes[0]={type:2,children:[1,2]},e.rules[50].opcodes[1]={type:7,string:[47]},e.rules[50].opcodes[2]={type:3,min:0,max:1},e.rules[50].opcodes[3]={type:2,children:[4,5]},e.rules[50].opcodes[4]={type:4,index:55},e.rules[50].opcodes[5]={type:3,min:0,max:1/0},e.rules[50].opcodes[6]={type:2,children:[7,8]},e.rules[50].opcodes[7]={type:7,string:[47]},e.rules[50].opcodes[8]={type:4,index:54},e.rules[51].opcodes=[],e.rules[51].opcodes[0]={type:2,children:[1,2]},e.rules[51].opcodes[1]={type:4,index:56},e.rules[51].opcodes[2]={type:3,min:0,max:1/0},e.rules[51].opcodes[3]={type:2,children:[4,5]},e.rules[51].opcodes[4]={type:7,string:[47]},e.rules[51].opcodes[5]={type:4,index:54},e.rules[52].opcodes=[],e.rules[52].opcodes[0]={type:2,children:[1,2]},e.rules[52].opcodes[1]={type:4,index:55},e.rules[52].opcodes[2]={type:3,min:0,max:1/0},e.rules[52].opcodes[3]={type:2,children:[4,5]},e.rules[52].opcodes[4]={type:7,string:[47]},e.rules[52].opcodes[5]={type:4,index:54},e.rules[53].opcodes=[],e.rules[53].opcodes[0]={type:3,min:0,max:0},e.rules[53].opcodes[1]={type:4,index:57},e.rules[54].opcodes=[],e.rules[54].opcodes[0]={type:3,min:0,max:1/0},e.rules[54].opcodes[1]={type:4,index:57},e.rules[55].opcodes=[],e.rules[55].opcodes[0]={type:3,min:1,max:1/0},e.rules[55].opcodes[1]={type:4,index:57},e.rules[56].opcodes=[],e.rules[56].opcodes[0]={type:3,min:1,max:1/0},e.rules[56].opcodes[1]={type:1,children:[2,3,4,5]},e.rules[56].opcodes[2]={type:4,index:61},e.rules[56].opcodes[3]={type:4,index:60},e.rules[56].opcodes[4]={type:4,index:64},e.rules[56].opcodes[5]={type:7,string:[64]},e.rules[57].opcodes=[],e.rules[57].opcodes[0]={type:1,children:[1,2,3,4,5]},e.rules[57].opcodes[1]={type:4,index:61},e.rules[57].opcodes[2]={type:4,index:60},e.rules[57].opcodes[3]={type:4,index:64},e.rules[57].opcodes[4]={type:7,string:[58]},e.rules[57].opcodes[5]={type:7,string:[64]},e.rules[58].opcodes=[],e.rules[58].opcodes[0]={type:3,min:0,max:1/0},e.rules[58].opcodes[1]={type:1,children:[2,3,4]},e.rules[58].opcodes[2]={type:4,index:57},e.rules[58].opcodes[3]={type:7,string:[47]},e.rules[58].opcodes[4]={type:7,string:[63]},e.rules[59].opcodes=[],e.rules[59].opcodes[0]={type:3,min:0,max:1/0},e.rules[59].opcodes[1]={type:1,children:[2,3,4]},e.rules[59].opcodes[2]={type:4,index:57},e.rules[59].opcodes[3]={type:7,string:[47]},e.rules[59].opcodes[4]={type:7,string:[63]},e.rules[60].opcodes=[],e.rules[60].opcodes[0]={type:2,children:[1,2,3]},e.rules[60].opcodes[1]={type:7,string:[37]},e.rules[60].opcodes[2]={type:4,index:8},e.rules[60].opcodes[3]={type:4,index:8},e.rules[61].opcodes=[],e.rules[61].opcodes[0]={type:1,children:[1,2,3,4,5,6]},e.rules[61].opcodes[1]={type:4,index:0},e.rules[61].opcodes[2]={type:4,index:6},e.rules[61].opcodes[3]={type:7,string:[45]},e.rules[61].opcodes[4]={type:7,string:[46]},e.rules[61].opcodes[5]={type:7,string:[95]},e.rules[61].opcodes[6]={type:7,string:[126]},e.rules[62].opcodes=[],e.rules[62].opcodes[0]={type:1,children:[1,2]},e.rules[62].opcodes[1]={type:4,index:63},e.rules[62].opcodes[2]={type:4,index:64},e.rules[63].opcodes=[],e.rules[63].opcodes[0]={type:1,children:[1,2,3,4,5,6,7]},e.rules[63].opcodes[1]={type:7,string:[58]},e.rules[63].opcodes[2]={type:7,string:[47]},e.rules[63].opcodes[3]={type:7,string:[63]},e.rules[63].opcodes[4]={type:7,string:[35]},e.rules[63].opcodes[5]={type:7,string:[91]},e.rules[63].opcodes[6]={type:7,string:[93]},e.rules[63].opcodes[7]={type:7,string:[64]},e.rules[64].opcodes=[],e.rules[64].opcodes[0]={type:1,children:[1,2,3,4,5,6,7,8,9,10,11]},e.rules[64].opcodes[1]={type:7,string:[33]},e.rules[64].opcodes[2]={type:7,string:[36]},e.rules[64].opcodes[3]={type:7,string:[38]},e.rules[64].opcodes[4]={type:7,string:[39]},e.rules[64].opcodes[5]={type:7,string:[40]},e.rules[64].opcodes[6]={type:7,string:[41]},e.rules[64].opcodes[7]={type:7,string:[42]},e.rules[64].opcodes[8]={type:7,string:[43]},e.rules[64].opcodes[9]={type:7,string:[44]},e.rules[64].opcodes[10]={type:7,string:[59]},e.rules[64].opcodes[11]={type:7,string:[61]},e.rules[65].opcodes=[],e.rules[65].opcodes[0]={type:2,children:[1,2,7,8]},e.rules[65].opcodes[1]={type:7,string:[100,110,115,58]},e.rules[65].opcodes[2]={type:3,min:0,max:1},e.rules[65].opcodes[3]={type:2,children:[4,5,6]},e.rules[65].opcodes[4]={type:7,string:[47,47]},e.rules[65].opcodes[5]={type:4,index:66},e.rules[65].opcodes[6]={type:7,string:[47]},e.rules[65].opcodes[7]={type:4,index:67},e.rules[65].opcodes[8]={type:3,min:0,max:1},e.rules[65].opcodes[9]={type:2,children:[10,11]},e.rules[65].opcodes[10]={type:7,string:[63]},e.rules[65].opcodes[11]={type:4,index:68},e.rules[66].opcodes=[],e.rules[66].opcodes[0]={type:2,children:[1,2]},e.rules[66].opcodes[1]={type:4,index:38},e.rules[66].opcodes[2]={type:3,min:0,max:1},e.rules[66].opcodes[3]={type:2,children:[4,5]},e.rules[66].opcodes[4]={type:7,string:[58]},e.rules[66].opcodes[5]={type:4,index:39},e.rules[67].opcodes=[],e.rules[67].opcodes[0]={type:3,min:0,max:1/0},e.rules[67].opcodes[1]={type:4,index:57},e.rules[68].opcodes=[],e.rules[68].opcodes[0]={type:2,children:[1,2]},e.rules[68].opcodes[1]={type:4,index:69},e.rules[68].opcodes[2]={type:3,min:0,max:1},e.rules[68].opcodes[3]={type:2,children:[4,5]},e.rules[68].opcodes[4]={type:7,string:[59]},e.rules[68].opcodes[5]={type:4,index:68},e.rules[69].opcodes=[],e.rules[69].opcodes[0]={type:1,children:[1,4]},e.rules[69].opcodes[1]={type:2,children:[2,3]},e.rules[69].opcodes[2]={type:7,string:[99,108,97,115,115,61]},e.rules[69].opcodes[3]={type:4,index:70},e.rules[69].opcodes[4]={type:2,children:[5,6]},e.rules[69].opcodes[5]={type:7,string:[116,121,112,101,61]},e.rules[69].opcodes[6]={type:4,index:71},e.rules[70].opcodes=[],e.rules[70].opcodes[0]={type:1,children:[1,3,4]},e.rules[70].opcodes[1]={type:3,min:1,max:1/0},e.rules[70].opcodes[2]={type:4,index:6},e.rules[70].opcodes[3]={type:7,string:[105,110]},e.rules[70].opcodes[4]={type:7,string:[99,104]},e.rules[71].opcodes=[],e.rules[71].opcodes[0]={type:1,children:[1,3,4,5]},e.rules[71].opcodes[1]={type:3,min:1,max:1/0},e.rules[71].opcodes[2]={type:4,index:6},e.rules[71].opcodes[3]={type:7,string:[97]},e.rules[71].opcodes[4]={type:7,string:[110,115]},e.rules[71].opcodes[5]={type:7,string:[109,100]},e.rules[72].opcodes=[],e.rules[72].opcodes[0]={type:1,children:[1,2]},e.rules[72].opcodes[1]={type:4,index:75},e.rules[72].opcodes[2]={type:4,index:78},e.rules[73].opcodes=[],e.rules[73].opcodes[0]={type:6,string:[101,105,112,49,53,53]},e.rules[74].opcodes=[],e.rules[74].opcodes[0]={type:3,min:1,max:32},e.rules[74].opcodes[1]={type:4,index:6},e.rules[75].opcodes=[],e.rules[75].opcodes[0]={type:2,children:[1,2,3]},e.rules[75].opcodes[1]={type:4,index:73},e.rules[75].opcodes[2]={type:7,string:[58]},e.rules[75].opcodes[3]={type:4,index:74},e.rules[76].opcodes=[],e.rules[76].opcodes[0]={type:6,string:[115,111,108,97,110,97]},e.rules[77].opcodes=[],e.rules[77].opcodes[0]={type:1,children:[1,2,3,4]},e.rules[77].opcodes[1]={type:6,string:[109,97,105,110,110,101,116]},e.rules[77].opcodes[2]={type:6,string:[116,101,115,116,110,101,116]},e.rules[77].opcodes[3]={type:6,string:[100,101,118,110,101,116]},e.rules[77].opcodes[4]={type:6,string:[108,111,99,97,108,110,101,116]},e.rules[78].opcodes=[],e.rules[78].opcodes[0]={type:2,children:[1,2,3]},e.rules[78].opcodes[1]={type:4,index:76},e.rules[78].opcodes[2]={type:7,string:[58]},e.rules[78].opcodes[3]={type:4,index:77},e.rules[79].opcodes=[],e.rules[79].opcodes[0]={type:1,children:[1,2]},e.rules[79].opcodes[1]={type:4,index:81},e.rules[79].opcodes[2]={type:4,index:83},e.rules[80].opcodes=[],e.rules[80].opcodes[0]={type:2,children:[1,2]},e.rules[80].opcodes[1]={type:7,string:[48,120]},e.rules[80].opcodes[2]={type:3,min:40,max:40},e.rules[80].opcodes[3]={type:4,index:8},e.rules[81].opcodes=[],e.rules[81].opcodes[0]={type:2,children:[1,2,3]},e.rules[81].opcodes[1]={type:4,index:75},e.rules[81].opcodes[2]={type:7,string:[58]},e.rules[81].opcodes[3]={type:4,index:80},e.rules[82].opcodes=[],e.rules[82].opcodes[0]={type:3,min:32,max:44},e.rules[82].opcodes[1]={type:1,children:[2,3,4,5,6,7]},e.rules[82].opcodes[2]={type:5,min:49,max:57},e.rules[82].opcodes[3]={type:5,min:65,max:72},e.rules[82].opcodes[4]={type:5,min:74,max:78},e.rules[82].opcodes[5]={type:5,min:80,max:90},e.rules[82].opcodes[6]={type:5,min:97,max:107},e.rules[82].opcodes[7]={type:5,min:109,max:122},e.rules[83].opcodes=[],e.rules[83].opcodes[0]={type:2,children:[1,2,3]},e.rules[83].opcodes[1]={type:4,index:78},e.rules[83].opcodes[2]={type:7,string:[58]},e.rules[83].opcodes[3]={type:4,index:82},e.rules[84].opcodes=[],e.rules[84].opcodes[0]={type:1,children:[1,2]},e.rules[84].opcodes[1]={type:4,index:85},e.rules[84].opcodes[2]={type:4,index:86},e.rules[85].opcodes=[],e.rules[85].opcodes[0]={type:2,children:[1,2,3,4,5,6,7,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,36,41]},e.rules[85].opcodes[1]={type:4,index:87},e.rules[85].opcodes[2]={type:6,string:[32,119,97,110,116,115,32,121,111,117,32,116,111,32,115,105,103,110,32,105,110,32,119,105,116,104,32,121,111,117,114,32,69,116,104,101,114,101,117,109,32,97,99,99,111,117,110,116,58]},e.rules[85].opcodes[3]={type:4,index:10},e.rules[85].opcodes[4]={type:4,index:81},e.rules[85].opcodes[5]={type:4,index:10},e.rules[85].opcodes[6]={type:4,index:10},e.rules[85].opcodes[7]={type:3,min:0,max:1},e.rules[85].opcodes[8]={type:2,children:[9,10]},e.rules[85].opcodes[9]={type:4,index:89},e.rules[85].opcodes[10]={type:4,index:10},e.rules[85].opcodes[11]={type:4,index:10},e.rules[85].opcodes[12]={type:6,string:[85,82,73,58,32]},e.rules[85].opcodes[13]={type:4,index:90},e.rules[85].opcodes[14]={type:4,index:10},e.rules[85].opcodes[15]={type:6,string:[86,101,114,115,105,111,110,58,32]},e.rules[85].opcodes[16]={type:4,index:91},e.rules[85].opcodes[17]={type:4,index:10},e.rules[85].opcodes[18]={type:6,string:[67,104,97,105,110,32,73,68,58,32]},e.rules[85].opcodes[19]={type:4,index:75},e.rules[85].opcodes[20]={type:4,index:10},e.rules[85].opcodes[21]={type:6,string:[78,111,110,99,101,58,32]},e.rules[85].opcodes[22]={type:4,index:93},e.rules[85].opcodes[23]={type:4,index:10},e.rules[85].opcodes[24]={type:6,string:[73,115,115,117,101,100,32,65,116,58,32]},e.rules[85].opcodes[25]={type:4,index:94},e.rules[85].opcodes[26]={type:3,min:0,max:1},e.rules[85].opcodes[27]={type:2,children:[28,29,30]},e.rules[85].opcodes[28]={type:4,index:10},e.rules[85].opcodes[29]={type:6,string:[69,120,112,105,114,97,116,105,111,110,32,84,105,109,101,58,32]},e.rules[85].opcodes[30]={type:4,index:95},e.rules[85].opcodes[31]={type:3,min:0,max:1},e.rules[85].opcodes[32]={type:2,children:[33,34,35]},e.rules[85].opcodes[33]={type:4,index:10},e.rules[85].opcodes[34]={type:6,string:[78,111,116,32,66,101,102,111,114,101,58,32]},e.rules[85].opcodes[35]={type:4,index:96},e.rules[85].opcodes[36]={type:3,min:0,max:1},e.rules[85].opcodes[37]={type:2,children:[38,39,40]},e.rules[85].opcodes[38]={type:4,index:10},e.rules[85].opcodes[39]={type:6,string:[82,101,113,117,101,115,116,32,73,68,58,32]},e.rules[85].opcodes[40]={type:4,index:97},e.rules[85].opcodes[41]={type:3,min:0,max:1},e.rules[85].opcodes[42]={type:2,children:[43,44,45]},e.rules[85].opcodes[43]={type:4,index:10},e.rules[85].opcodes[44]={type:6,string:[82,101,115,111,117,114,99,101,115,58]},e.rules[85].opcodes[45]={type:4,index:98},e.rules[86].opcodes=[],e.rules[86].opcodes[0]={type:2,children:[1,2,3,4,5,6,7,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,31,36,41]},e.rules[86].opcodes[1]={type:4,index:87},e.rules[86].opcodes[2]={type:6,string:[32,119,97,110,116,115,32,121,111,117,32,116,111,32,115,105,103,110,32,105,110,32,119,105,116,104,32,121,111,117,114,32,83,111,108,97,110,97,32,97,99,99,111,117,110,116,58]},e.rules[86].opcodes[3]={type:4,index:10},e.rules[86].opcodes[4]={type:4,index:83},e.rules[86].opcodes[5]={type:4,index:10},e.rules[86].opcodes[6]={type:4,index:10},e.rules[86].opcodes[7]={type:3,min:0,max:1},e.rules[86].opcodes[8]={type:2,children:[9,10]},e.rules[86].opcodes[9]={type:4,index:89},e.rules[86].opcodes[10]={type:4,index:10},e.rules[86].opcodes[11]={type:4,index:10},e.rules[86].opcodes[12]={type:6,string:[85,82,73,58,32]},e.rules[86].opcodes[13]={type:4,index:90},e.rules[86].opcodes[14]={type:4,index:10},e.rules[86].opcodes[15]={type:6,string:[86,101,114,115,105,111,110,58,32]},e.rules[86].opcodes[16]={type:4,index:91},e.rules[86].opcodes[17]={type:4,index:10},e.rules[86].opcodes[18]={type:6,string:[67,104,97,105,110,32,73,68,58,32]},e.rules[86].opcodes[19]={type:4,index:78},e.rules[86].opcodes[20]={type:4,index:10},e.rules[86].opcodes[21]={type:6,string:[78,111,110,99,101,58,32]},e.rules[86].opcodes[22]={type:4,index:93},e.rules[86].opcodes[23]={type:4,index:10},e.rules[86].opcodes[24]={type:6,string:[73,115,115,117,101,100,32,65,116,58,32]},e.rules[86].opcodes[25]={type:4,index:94},e.rules[86].opcodes[26]={type:3,min:0,max:1},e.rules[86].opcodes[27]={type:2,children:[28,29,30]},e.rules[86].opcodes[28]={type:4,index:10},e.rules[86].opcodes[29]={type:6,string:[69,120,112,105,114,97,116,105,111,110,32,84,105,109,101,58,32]},e.rules[86].opcodes[30]={type:4,index:95},e.rules[86].opcodes[31]={type:3,min:0,max:1},e.rules[86].opcodes[32]={type:2,children:[33,34,35]},e.rules[86].opcodes[33]={type:4,index:10},e.rules[86].opcodes[34]={type:6,string:[78,111,116,32,66,101,102,111,114,101,58,32]},e.rules[86].opcodes[35]={type:4,index:96},e.rules[86].opcodes[36]={type:3,min:0,max:1},e.rules[86].opcodes[37]={type:2,children:[38,39,40]},e.rules[86].opcodes[38]={type:4,index:10},e.rules[86].opcodes[39]={type:6,string:[82,101,113,117,101,115,116,32,73,68,58,32]},e.rules[86].opcodes[40]={type:4,index:97},e.rules[86].opcodes[41]={type:3,min:0,max:1},e.rules[86].opcodes[42]={type:2,children:[43,44,45]},e.rules[86].opcodes[43]={type:4,index:10},e.rules[86].opcodes[44]={type:6,string:[82,101,115,111,117,114,99,101,115,58]},e.rules[86].opcodes[45]={type:4,index:98},e.rules[87].opcodes=[],e.rules[87].opcodes[0]={type:4,index:66},e.rules[88].opcodes=[],e.rules[88].opcodes[0]={type:1,children:[1,2]},e.rules[88].opcodes[1]={type:4,index:81},e.rules[88].opcodes[2]={type:4,index:83},e.rules[89].opcodes=[],e.rules[89].opcodes[0]={type:3,min:1,max:1/0},e.rules[89].opcodes[1]={type:1,children:[2,3,4]},e.rules[89].opcodes[2]={type:4,index:62},e.rules[89].opcodes[3]={type:4,index:61},e.rules[89].opcodes[4]={type:7,string:[32]},e.rules[90].opcodes=[],e.rules[90].opcodes[0]={type:4,index:29},e.rules[91].opcodes=[],e.rules[91].opcodes[0]={type:7,string:[49]},e.rules[92].opcodes=[],e.rules[92].opcodes[0]={type:1,children:[1,2]},e.rules[92].opcodes[1]={type:4,index:75},e.rules[92].opcodes[2]={type:4,index:78},e.rules[93].opcodes=[],e.rules[93].opcodes[0]={type:3,min:8,max:1/0},e.rules[93].opcodes[1]={type:1,children:[2,3]},e.rules[93].opcodes[2]={type:4,index:0},e.rules[93].opcodes[3]={type:4,index:6},e.rules[94].opcodes=[],e.rules[94].opcodes[0]={type:4,index:28},e.rules[95].opcodes=[],e.rules[95].opcodes[0]={type:4,index:28},e.rules[96].opcodes=[],e.rules[96].opcodes[0]={type:4,index:28},e.rules[97].opcodes=[],e.rules[97].opcodes[0]={type:3,min:0,max:1/0},e.rules[97].opcodes[1]={type:4,index:57},e.rules[98].opcodes=[],e.rules[98].opcodes[0]={type:3,min:0,max:1/0},e.rules[98].opcodes[1]={type:2,children:[2,3,4]},e.rules[98].opcodes[2]={type:4,index:10},e.rules[98].opcodes[3]={type:7,string:[45,32]},e.rules[98].opcodes[4]={type:4,index:29},e.toString=()=>{let t="";return t+=`; ==============================================================================
`,t+=`; Core rules for ABNF (RFC 5234)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; References:
`,t+=`; - https://www.rfc-editor.org/rfc/rfc5234#appendix-B.1
`,t+=`
`,t+=`ALPHA          =  %x41-5A / %x61-7A
`,t+=`               ; A-Z / a-z
`,t+=`
`,t+=`BIT            =  "0" / "1"
`,t+=`
`,t+=`CHAR           =  %x01-7F
`,t+=`               ; any 7-bit US-ASCII character,
`,t+=`               ;  excluding NUL
`,t+=`
`,t+=`CR             =  %x0D
`,t+=`               ; carriage return
`,t+=`
`,t+=`CRLF           =  CR LF
`,t+=`               ; Internet standard newline
`,t+=`
`,t+=`CTL            =  %x00-1F / %x7F
`,t+=`               ; controls
`,t+=`
`,t+=`DIGIT          =  %x30-39
`,t+=`               ; 0-9
`,t+=`
`,t+=`DQUOTE         =  %x22
`,t+=`               ; " (Double Quote)
`,t+=`
`,t+=`HEXDIG         =  DIGIT / "A" / "B" / "C" / "D" / "E" / "F"
`,t+=`
`,t+=`HTAB           =  %x09
`,t+=`               ; horizontal tab
`,t+=`
`,t+=`LF             =  %x0A
`,t+=`               ; linefeed
`,t+=`
`,t+=`LWSP           =  *(WSP / CRLF WSP)
`,t+=`               ; Use of this linear-white-space rule
`,t+=`               ;  permits lines containing only white
`,t+=`               ;  space that are no longer legal in
`,t+=`               ;  mail headers and have caused
`,t+=`               ;  interoperability problems in other
`,t+=`               ;  contexts.
`,t+=`               ; Do not use when defining mail
`,t+=`               ;  headers and use with caution in
`,t+=`               ;  other contexts.
`,t+=`
`,t+=`OCTET          =  %x00-FF
`,t+=`               ; 8 bits of data
`,t+=`
`,t+=`SP             =  %x20
`,t+=`
`,t+=`VCHAR          =  %x21-7E
`,t+=`               ; visible (printing) characters
`,t+=`
`,t+=`WSP            =  SP / HTAB
`,t+=`               ; white space
`,t+=`; ==============================================================================
`,t+=`; Internet Date/Time Format (RFC 3339)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; Requires:
`,t+=`; - core.bnf
`,t+=`;
`,t+=`; References:
`,t+=`; - https://www.rfc-editor.org/rfc/rfc3339#section-5.6
`,t+=`
`,t+=`date-fullyear   = 4DIGIT
`,t+=`
`,t+=`date-month      = 2DIGIT  ; 01-12
`,t+=`
`,t+=`date-mday       = 2DIGIT  ; 01-28, 01-29, 01-30, 01-31 based on
`,t+=`                          ; month/year
`,t+=`
`,t+=`time-hour       = 2DIGIT  ; 00-23
`,t+=`
`,t+=`time-minute     = 2DIGIT  ; 00-59
`,t+=`
`,t+=`time-second     = 2DIGIT  ; 00-58, 00-59, 00-60 based on leap second
`,t+=`                          ; rules
`,t+=`
`,t+=`time-secfrac    = "." 1*DIGIT
`,t+=`
`,t+=`time-numoffset  = ("+" / "-") time-hour ":" time-minute
`,t+=`
`,t+=`time-offset     = "Z" / time-numoffset
`,t+=`
`,t+=`partial-time    = time-hour ":" time-minute ":" time-second
`,t+=`                  [time-secfrac]
`,t+=`
`,t+=`full-date       = date-fullyear "-" date-month "-" date-mday
`,t+=`
`,t+=`full-time       = partial-time time-offset
`,t+=`
`,t+=`date-time       = full-date "T" full-time
`,t+=`; ==============================================================================
`,t+=`; Uniform Resource Identifier (RFC 3986)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; Requires:
`,t+=`; - core.bnf
`,t+=`;
`,t+=`; References:
`,t+=`; - https://www.rfc-editor.org/rfc/rfc3986#appendix-A
`,t+=`
`,t+=`URI           = scheme ":" hier-part [ "?" query ] [ "#" fragment ]
`,t+=`
`,t+=`hier-part     = "//" authority path-abempty
`,t+=`              / path-absolute
`,t+=`              / path-rootless
`,t+=`              / path-empty
`,t+=`
`,t+=`URI-reference = URI / relative-ref
`,t+=`
`,t+=`absolute-URI  = scheme ":" hier-part [ "?" query ]
`,t+=`
`,t+=`relative-ref  = relative-part [ "?" query ] [ "#" fragment ]
`,t+=`
`,t+=`relative-part = "//" authority path-abempty
`,t+=`              / path-absolute
`,t+=`              / path-noscheme
`,t+=`              / path-empty
`,t+=`
`,t+=`scheme        = ALPHA *( ALPHA / DIGIT / "+" / "-" / "." )
`,t+=`
`,t+=`authority     = [ userinfo "@" ] host [ ":" port ]
`,t+=`userinfo      = *( unreserved / pct-encoded / sub-delims / ":" )
`,t+=`host          = IP-literal / IPv4address / reg-name
`,t+=`port          = *DIGIT
`,t+=`
`,t+=`IP-literal    = "[" ( IPv6address / IPvFuture  ) "]"
`,t+=`
`,t+=`IPvFuture     = "v" 1*HEXDIG "." 1*( unreserved / sub-delims / ":" )
`,t+=`
`,t+=`IPv6address   =                            6( h16 ":" ) ls32
`,t+=`              /                       "::" 5( h16 ":" ) ls32
`,t+=`              / [               h16 ] "::" 4( h16 ":" ) ls32
`,t+=`              / [ *1( h16 ":" ) h16 ] "::" 3( h16 ":" ) ls32
`,t+=`              / [ *2( h16 ":" ) h16 ] "::" 2( h16 ":" ) ls32
`,t+=`              / [ *3( h16 ":" ) h16 ] "::"    h16 ":"   ls32
`,t+=`              / [ *4( h16 ":" ) h16 ] "::"              ls32
`,t+=`              / [ *5( h16 ":" ) h16 ] "::"              h16
`,t+=`              / [ *6( h16 ":" ) h16 ] "::"
`,t+=`
`,t+=`h16           = 1*4HEXDIG
`,t+=`ls32          = ( h16 ":" h16 ) / IPv4address
`,t+=`IPv4address   = dec-octet "." dec-octet "." dec-octet "." dec-octet
`,t+=`dec-octet     = DIGIT                 ; 0-9
`,t+=`              / %x31-39 DIGIT         ; 10-99
`,t+=`              / "1" 2DIGIT            ; 100-199
`,t+=`              / "2" %x30-34 DIGIT     ; 200-249
`,t+=`              / "25" %x30-35          ; 250-255
`,t+=`
`,t+=`reg-name      = *( unreserved / pct-encoded / sub-delims )
`,t+=`
`,t+=`path          = path-abempty    ; begins with "/" or is empty
`,t+=`              / path-absolute   ; begins with "/" but not "//"
`,t+=`              / path-noscheme   ; begins with a non-colon segment
`,t+=`              / path-rootless   ; begins with a segment
`,t+=`              / path-empty      ; zero characters
`,t+=`
`,t+=`path-abempty  = *( "/" segment )
`,t+=`path-absolute = "/" [ segment-nz *( "/" segment ) ]
`,t+=`path-noscheme = segment-nz-nc *( "/" segment )
`,t+=`path-rootless = segment-nz *( "/" segment )
`,t+=`path-empty    = 0pchar
`,t+=`
`,t+=`segment       = *pchar
`,t+=`segment-nz    = 1*pchar
`,t+=`segment-nz-nc = 1*( unreserved / pct-encoded / sub-delims / "@" )
`,t+=`; non-zero-length segment without any colon ":"
`,t+=`
`,t+=`pchar         = unreserved / pct-encoded / sub-delims / ":" / "@"
`,t+=`
`,t+=`query         = *( pchar / "/" / "?" )
`,t+=`
`,t+=`fragment      = *( pchar / "/" / "?" )
`,t+=`
`,t+=`pct-encoded   = "%" HEXDIG HEXDIG
`,t+=`
`,t+=`unreserved    = ALPHA / DIGIT / "-" / "." / "_" / "~"
`,t+=`reserved      = gen-delims / sub-delims
`,t+=`gen-delims    = ":" / "/" / "?" / "#" / "[" / "]" / "@"
`,t+=`sub-delims    = "!" / "$" / "&" / "'" / "(" / ")"
`,t+=`              / "*" / "+" / "," / ";" / "="
`,t+=`; ==============================================================================
`,t+=`; DNS URI (RFC 4501)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; Requires:
`,t+=`; - core.bnf
`,t+=`; - uri.bnf
`,t+=`;
`,t+=`; References:
`,t+=`; - https://www.rfc-editor.org/rfc/rfc4501.html#section-3
`,t+=`;
`,t+=`; Note:
`,t+=`; dnsclassval and dnstypeval are not defined exhaustively
`,t+=`; but their definitions are unused in SIW grammars.
`,t+=`
`,t+=`
`,t+=`dnsurl          = "dns:" [ "//" dnsauthority "/" ]
`,t+=`                  dnsname ["?" dnsquery]
`,t+=`
`,t+=`dnsauthority    = host [ ":" port ]
`,t+=`                ; See RFC 3986 for the
`,t+=`                ; definition of "host" and "port".
`,t+=`
`,t+=`dnsname         = *pchar
`,t+=`                ; See RFC 3986 for the
`,t+=`                ; definition of "pchar".
`,t+=`
`,t+=`                ; The "dnsname" field may be a
`,t+=`                ; "relative" or "absolute" name,
`,t+=`                ; as per RFC 1034, section 3.1.
`,t+=`
`,t+=`                ; Note further that an empty
`,t+=`                ; "dnsname" value is to be
`,t+=`                ; interpreted as the root itself.
`,t+=`                ; See below on relative dnsnames.
`,t+=`
`,t+=`dnsquery        = dnsqueryelement [";" dnsquery]
`,t+=`
`,t+=`dnsqueryelement = ( "CLASS=" dnsclassval ) / ( "TYPE=" dnstypeval )
`,t+=`                ; Each clause MUST NOT be used more
`,t+=`                ; than once.
`,t+=`
`,t+=`dnsclassval     = 1*digit / "IN" / "CH"
`,t+=`                ; omitted:
`,t+=`                ; / <Any IANA registered DNS class mnemonic>
`,t+=`
`,t+=`dnstypeval      = 1*digit / "A" / "NS" / "MD"
`,t+=`                ; omitted:
`,t+=`                ; / <Any IANA registered DNS type mnemonic>
`,t+=`; ==============================================================================
`,t+=`; Chain-agnostic IDs (CAIP-2)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; Requires:
`,t+=`; - core.bnf
`,t+=`;
`,t+=`; References:
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-2.md#syntax
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-10.md#syntax
`,t+=`
`,t+=`chain-id        = eth-chain-id
`,t+=`                / sol-chain-id
`,t+=`                ; NOTE: Only Ethereum and Solana are supported at the moment
`,t+=`
`,t+=`
`,t+=`; Ethereum
`,t+=`; ------------------------------------------------------------------------------
`,t+=`
`,t+=`eth-namespace   = %s"eip155"
`,t+=`                ; The chain-id namespace for evm chains
`,t+=`
`,t+=`eth-network-id  = 1*32DIGIT
`,t+=`                ; Accept up to 32 digits. Could probably be lowered
`,t+=`
`,t+=`eth-chain-id    = eth-namespace ":" eth-network-id
`,t+=`                ; Examples:
`,t+=`                ; - eip155:1   (eth mainnet)
`,t+=`                ; - eip155:137 (polygon mainnet)
`,t+=`
`,t+=`; Solana
`,t+=`; ------------------------------------------------------------------------------
`,t+=`
`,t+=`sol-namespace   = %s"solana"
`,t+=`                ; The chain-id namespace for solana
`,t+=`
`,t+=`sol-network-id  = %s"mainnet" / %s"testnet" / %s"devnet" / %s"localnet"
`,t+=`                ; This enumeration comes from https://github.com/solana-labs/wallet-standard/blob/master/packages/core/chains/src/index.ts
`,t+=`
`,t+=`sol-chain-id    = sol-namespace ":" sol-network-id
`,t+=`                ; Examples:
`,t+=`                ; - solana:mainnet (solana mainnet)
`,t+=`                ; - solana:testnet (solana testnet)
`,t+=`; ==============================================================================
`,t+=`; Chain-agnostic account identifiers (CAIP-10)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; Requires:
`,t+=`; - core.bnf
`,t+=`; - chain-id.bnf
`,t+=`;
`,t+=`; References:
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-2.md#syntax
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-10.md#syntax
`,t+=`
`,t+=`account-id      = eth-account-id
`,t+=`                / sol-account-id
`,t+=`                ; NOTE: Only Ethereum and Solana are supported at the moment
`,t+=`
`,t+=`; Ethereum
`,t+=`; ------------------------------------------------------------------------------
`,t+=`
`,t+=`eth-address     = "0x" 40HEXDIG
`,t+=`                ; Must also conform to captilization
`,t+=`                ; checksum encoding specified in EIP-55
`,t+=`                ; where applicable (EOAs).
`,t+=`
`,t+=`eth-account-id  = eth-chain-id ":" eth-address
`,t+=`                ; See CAIP-2 for definition of "eth-chain-id"
`,t+=`                ; Examples:
`,t+=`                ; - eip155:1:******************************************   (eth mainnet)
`,t+=`                ; - eip155:137:****************************************** (polygon mainnet)
`,t+=`
`,t+=`; Solana
`,t+=`; ------------------------------------------------------------------------------
`,t+=`
`,t+=`sol-address     = 32*44( %x31-39 / %x41-48 / %x4A-4E / %x50-5A / %x61-6B / %x6D-7A )
`,t+=`                ; Valid Solana addresses are 32-44 base58-encoded chars: 1-9 / A-H / J-N / P-Z / a-k / m-z
`,t+=`                ; Note: this doesn't validate whether the address is "on the curve"
`,t+=`
`,t+=`sol-account-id  = sol-chain-id ":" sol-address
`,t+=`                ; See CAIP-2 for definition of "sol-chain-id"
`,t+=`                ; Examples:
`,t+=`                ; - solana:mainnet:FYpB58cLw5cwiN763ayB2sFT8HLF2MRUBbbyRgHYiRpK (solana mainnet)
`,t+=`                ; - solana:testnet:QhnBSSbjScn9VHBBqwosy99icpeJrW4iuEwkJ3d9Fn9  (solana testnet)
`,t+=`; ==============================================================================
`,t+=`; Sign in with X (CAIP-122)
`,t+=`; ==============================================================================
`,t+=`;
`,t+=`; Requires:
`,t+=`; - core.bnf
`,t+=`; - datetime.bnf
`,t+=`; - uri.bnf
`,t+=`; - dns.bnf
`,t+=`; - chain-id.bnf
`,t+=`; - account-id.bnf
`,t+=`;
`,t+=`; References:
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-2.md#syntax
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-10.md#syntax
`,t+=`; - https://github.com/ChainAgnostic/CAIPs/blob/master/CAIPs/caip-122.md
`,t+=`;
`,t+=`; Note:
`,t+=`; Currently, this grammar file only supports the Ethereum and Solana blockchains
`,t+=`
`,t+=`sign-in-with-x = sign-in-with-eth
`,t+=`               / sign-in-with-sol
`,t+=`
`,t+=`; Ethereum
`,t+=`;-----------------------------------------------------------------------------
`,t+=`
`,t+=`sign-in-with-eth =
`,t+=`  message-domain %s" wants you to sign in with your Ethereum account:" LF
`,t+=`  eth-account-id LF
`,t+=`  LF
`,t+=`  [ message-statement LF ]
`,t+=`  LF
`,t+=`  %s"URI: " message-uri LF
`,t+=`  %s"Version: " message-version LF
`,t+=`  %s"Chain ID: " eth-chain-id LF
`,t+=`  %s"Nonce: " message-nonce LF
`,t+=`  %s"Issued At: " message-issued-at
`,t+=`  [ LF %s"Expiration Time: " message-expiration-time ]
`,t+=`  [ LF %s"Not Before: " message-not-before ]
`,t+=`  [ LF %s"Request ID: " message-request-id ]
`,t+=`  [ LF %s"Resources:" message-resources ]
`,t+=`
`,t+=`; Solana
`,t+=`;-----------------------------------------------------------------------------
`,t+=`
`,t+=`sign-in-with-sol =
`,t+=`  message-domain %s" wants you to sign in with your Solana account:" LF
`,t+=`  sol-account-id LF
`,t+=`  LF
`,t+=`  [ message-statement LF ]
`,t+=`  LF
`,t+=`  %s"URI: " message-uri LF
`,t+=`  %s"Version: " message-version LF
`,t+=`  %s"Chain ID: " sol-chain-id LF
`,t+=`  %s"Nonce: " message-nonce LF
`,t+=`  %s"Issued At: " message-issued-at
`,t+=`  [ LF %s"Expiration Time: " message-expiration-time ]
`,t+=`  [ LF %s"Not Before: " message-not-before ]
`,t+=`  [ LF %s"Request ID: " message-request-id ]
`,t+=`  [ LF %s"Resources:" message-resources ]
`,t+=`
`,t+=`; Common
`,t+=`;-----------------------------------------------------------------------------
`,t+=`
`,t+=`message-domain          = dnsauthority ; CAIP-10 spec defines domain as dnsauthority unlike EIP-4361, which uses authority
`,t+=`message-address         = eth-account-id / sol-account-id ; See CAIP-10 for address identifier definition
`,t+=`message-statement       = 1*( reserved / unreserved / " " )
`,t+=`message-uri             = URI
`,t+=`message-version         = "1"
`,t+=`message-chain-id        = eth-chain-id / sol-chain-id
`,t+=`message-nonce           = 8*( ALPHA / DIGIT )
`,t+=`message-issued-at       = date-time
`,t+=`message-expiration-time = date-time
`,t+=`message-not-before      = date-time
`,t+=`message-request-id      = *pchar
`,t+=`message-resources       = *( LF "- " URI )
`,t},e}p();d();var je=(e,t)=>(r,n,a,o,l)=>(r===200&&(l[e]=t(n,a,o,l)),300);var{apgLib:$t}=globalThis;function br(e,t){let r=new $t.parser;r.stats=new $t.stats,r.trace=new $t.trace,r.ast=new $t.ast;let n=$t.utils.charsToString,a=(v,b,S)=>new Date(n(v,b,S)),o=(v,b,S)=>new URL(n(v,b,S));r.ast.callbacks["message-domain"]=je("domain",n),r.ast.callbacks["message-address"]=je("address",n),r.ast.callbacks["message-statement"]=je("statement",n),r.ast.callbacks["message-uri"]=je("uri",o),r.ast.callbacks["message-version"]=je("version",n),r.ast.callbacks["message-chain-id"]=je("chainId",n),r.ast.callbacks["message-nonce"]=je("nonce",n),r.ast.callbacks["message-issued-at"]=je("issuedAt",a),r.ast.callbacks["message-expiration-time"]=je("expirationTime",a),r.ast.callbacks["message-not-before"]=je("notBefore",a),r.ast.callbacks["message-request-id"]=je("requestId",n),r.ast.callbacks["message-resources"]=je("resources",(v,b,S)=>S?n(v,b,S).slice(3).split(`
- `).map(_=>new URL(_)):[]),e==="sign-in-with-x"&&(r.ast.callbacks["eth-account-id"]=r.ast.callbacks["message-address"],r.ast.callbacks["eth-chain-id"]=r.ast.callbacks["message-chain-id"],r.ast.callbacks["sol-account-id"]=r.ast.callbacks["message-address"],r.ast.callbacks["sol-chain-id"]=r.ast.callbacks["message-chain-id"]);let l=null;switch(e){case"sign-in-with-ethereum":l=Hs;break;case"sign-in-with-solana":l=Fs;break;case"sign-in-with-x":l=Ws;break}if(!l)throw new Error(`Could not find grammar for ${e}`);let f=l(),m=r.parse(f,e,t);if(!m.success){let b=m.maxMatched+1,S=t.slice(0,b),_=S.split(`
`),g=_[_.length-1].length-1,A=[S,g>-1?" ".repeat(g)+"^":"^"].join(`
`);return{success:!1,error:new Error(A)}}let u={type:e};return r.ast.translate(u),{success:!0,data:u}}p();d();p();d();var js={ADDRESS_MISMATCH:"address",DOMAIN_MISMATCH:"domain",URI_MISMATCH:"uri",CHAIN_ID_MISMATCH:"chainId",ISSUED_TOO_FAR_IN_THE_PAST:"issuedAt",ISSUED_TOO_FAR_IN_THE_FUTURE:"issuedAt",EXPIRED:"expirationTime",EXPIRES_BEFORE_ISSUANCE:"expirationTime",VALID_AFTER_EXPIRATION:"notBefore"};var Gt=$(st()),_c={eth:{hexToCAIP2:e=>`eip155:${parseInt(e,16)}`,getBufferedGasLimit(e){return"0x"+new St(Xo(new St(e))).toString(16)}},sol:{chainIdToCAIP2:Me,chainIdToCluster:Io,serializeSolanaSignInMessage(e,{expectedChainId:t,expectedAddress:r,expectedURL:n}){let a=e.domain||n.host,o=e.address||r,l=`${a} wants you to sign in with your Solana account:
`;l+=`${o}`,e.statement&&(l+=`

${e.statement}`);let f=[];if(e.uri&&f.push(`URI: ${e.uri}`),e.version&&f.push(`Version: ${e.version}`),e.chainId&&f.push(`Chain ID: ${e.chainId}`),e.nonce&&f.push(`Nonce: ${e.nonce}`),e.issuedAt&&f.push(`Issued At: ${e.issuedAt}`),e.expirationTime&&f.push(`Expiration Time: ${e.expirationTime}`),e.notBefore&&f.push(`Not Before: ${e.notBefore}`),e.requestId&&f.push(`Request ID: ${e.requestId}`),e.resources){f.push("Resources:");for(let b of e.resources)f.push(`- ${b}`)}f.length&&(l+=`

${f.join(`
`)}`);let m=br("sign-in-with-solana",l);if(!m.success)return{type:"fail",data:Gt.default.encode(Buffer.from(l)),error:[{label:"PARSE_ERROR",message:m.error.message}]};let u=1e3*60*10,v=wr(m.data,{expectedAddress:r,expectedURL:n,expectedChainId:t,issuedAtThreshold:u});return v.length?{type:"fail",data:Gt.default.encode(Buffer.from(l)),error:v.map(b=>({label:js[b],message:`siwsErrorMessage_${b}`}))}:{type:"success",data:Gt.default.encode(Buffer.from(l))}},base58Encode:e=>Gt.default.encode(e)},getFaviconURL:To,parseURL:Ro,getOrigin:er,getHostname:Ao,getOriginAndHostname:So};var Js={};Ie(Js,{addTrustedApp:()=>Ic,btc:()=>Xs,deleteTrustedApp:()=>Cc,eth:()=>Ks,getMetaMaskOverrideSetting:()=>Bc,isTrustedApp:()=>Lc,isUserOnboarded:()=>Fc,navigate:()=>qc,onboardQuickly:()=>Hc,requestUserApproval:()=>Uc,requestUserOnboard:()=>Wc,setMetaMaskOverrideSetting:()=>Oc,sol:()=>Gs});p();d();p();d();p();d();var Je=class{constructor(t){this.storage=t,this.target=new EventTarget,this.numListeners={revoked:0},this.unsubscribeFromStorageChanges=null}async get(t){return(await _t(this.storage)).apps[t]??{}}async add(t,r,n){let a=await _t(this.storage),o=a?.apps?.[t]?.[r]??{},l={...a?.apps[t]??{},[r]:{...o,...n}},f={...a.apps,[t]:l},m={...a,apps:f};return await Mr(this.storage,m)}async delete(t,r){let n=await _t(this.storage),a={...n?.apps[t]??{}};delete a[r];let o={...n.apps,[t]:a},l={...n,apps:o};return await Mr(this.storage,l)}dispatchEvent({type:t,data:r}){let n=new Event(t);n.data=r,this.target.dispatchEvent(n)}removeEventListener(t,r){this.target.removeEventListener(t,r),this.numListeners[t]--,Object.values(this.numListeners).reduce((a,o)=>a+o,0)===0&&(this.unsubscribeFromStorageChanges?.(),this.unsubscribeFromStorageChanges=null)}addEventListener(t,r){this.target.addEventListener(t,r),this.numListeners[t]++,Object.values(this.numListeners).reduce((a,o)=>a+o,0)===1&&(this.unsubscribeFromStorageChanges=this.subscribeToStorageChanges())}subscribeToStorageChanges(){let t=null,r=_t(this.storage);return this.storage.subscribe(async n=>{if(n!==Wo)return;let a=await _t(this.storage);if(t??await r){let o=this.getRevokedApps(t??await r,a);for(let l of o)try{this.dispatchEvent({type:"revoked",data:l})}catch(f){Y.captureError(f,"trustedApps")}}t=a})}getRevokedApps(t,r){let n=[],a=Object.entries(t.apps);for(let[o,l]of a){let f=Object.entries(l);for(let[m,u]of f)r.apps[o]?.[m]||n.push({origin:m,accountId:o})}return n}};p();d();var fl=new Error("There is no currently selected account"),Ec=new ye,ue=async()=>{let e=await Ko(de,Ec,await Lo(Ec));if(!e)throw fl;return e};var gl=new ye,yl=new Je(gl),Ic=async(e,t)=>{let r=(await ue()).identifier;await yl.add(r,e,{dappMeta:{appUrl:t.url.origin,title:t.title,imageUrl:t.icon?.toString()},lastConnectedTimestamp:Date.now()})};p();d();var xl=new ye,wl=new Je(xl),Cc=async e=>{let t=(await ue()).identifier;await wl.delete(t,e)};p();d();var bl=new ye,Bc=async()=>await ct(bl);p();d();var Al=new ye,Sl=new Je(Al),Lc=async e=>{let t=(await ue()).identifier;return!!(await Sl.get(t))[e]};p();d();var Ar=$(Ze());p();d();var Pc=$(Ze());var Mc=30,Vs=class{constructor(){this.findPopupIfExists=async()=>{try{Y.addBreadcrumb("NotificationManager","focusPopupIfExists","info");let t=await li();for(let r of t){if(typeof r.id!="number"){Y.addBreadcrumb("NotificationManager","popup has no id","info");continue}if(r.type!=="popup"){Y.addBreadcrumb("NotificationManager","found a non-popup type window","error");continue}let n=!1;if(r.tabs?.length===1){let[a]=r.tabs;if(a.url){let o=new URL(a.url).hostname,l=Pc.default.runtime.id;n=o===l}}if(!n){Y.addBreadcrumb("NotificationManager","popup tab url does not have the extension origin","info");continue}return r.id}return}catch(t){Y.addBreadcrumb("NotificationManager","failed to get open popups","error"),Y.captureError(t instanceof Error?t:new Error("failed to get open popups"),"provider");return}};this.showNotificationPopupAsync=async()=>{Y.addBreadcrumb("NotificationManager","showNotificationPopupAsync","info");let t=await this.findPopupIfExists();t&&await di(t);let r=0,n=0;try{let o=await pi();if(o.top!==void 0&&o.left!==void 0&&o.width!==void 0&&o.height!==void 0)n=o.top,r=o.left+o.width-Wt;else throw new Error("Couldn't get last focused window")}catch(o){Y.addBreadcrumb("NotificationManager","failed to get last focused window","error"),Y.captureError(o instanceof Error?o:new Error("failed to get last focused window"),"provider");let{screenX:l,screenY:f,outerWidth:m}=self;n=Math.max(f,0),r=Math.max(l+(m-Wt),0)}let a;try{a=await Dr({url:"notification.html",type:"popup",width:Wt,height:Fr+Mc,left:r,top:n,focused:!0})}catch{n=0,r=0,a=await Dr({url:"notification.html",type:"popup",width:Wt,height:Fr+Mc,left:r,top:n,focused:!0})}if(!a){let o=new Error("Could not open new popup notification");throw Y.captureError(o,"provider"),o}try{(a.left!==r||a.top!==n)&&a.id&&a.state!=="fullscreen"&&await ci(a.id,r,n)}catch(o){Y.addBreadcrumb("NotificationManager","failed to update window position","error"),Y.captureError(o instanceof Error?o:new Error("failed to update window position"),"provider")}return a}}},Nc=new Vs;var Rl=(e,t,r)=>{if(typeof r!="string")throw new Error("message must be a string");try{let a=at(r);console.log("> Received message from popup:",a);let o=yt[t].response.parse(a);if(o.id===e)return o}catch(a){console.error(a)}throw new Error("RPC response ID does not match RPC request ID")},Dc=(e,t,r)=>async n=>await Rl(t,r,n),vl=async(e,t,r)=>{let n=!1,o=Ar.default.runtime.connect({name:"popup/sidepanel"}),l=Dc(o,e,t);return new Promise((f,m)=>{o.postMessage(r),o.onMessage.addListener(async u=>{o.onMessage.removeListener(l);try{let v=await l(u);return n=!0,o.disconnect(),f(v)}catch(v){return m(v)}}),o.onDisconnect.addListener(()=>{if(!n)return o.onMessage.removeListener(l),s.userRejectedRequest(e)})})},kl=async(e,t,r)=>{let n=await Nc.showNotificationPopupAsync();if(!n)return s.resourceUnavailable(e);let a=n.id;if(typeof a!="number")throw new Error("No notification window id");let o=`notification/${a}`,l=`${sr()}/notification.html`;return await new Promise((f,m)=>{let u=!1,v=b=>{let S=Dc(b,e,t);b.name===o&&b.sender&&b.sender.tab?.url===l&&(b.onMessage.addListener(async g=>{try{let A=await S(g);return u=!0,b.disconnect(),b.onMessage.removeListener(S),f(A)}catch(A){return m(A)}}),b.onDisconnect.addListener(()=>{u||(b.onMessage.removeListener(S),f(s.userRejectedRequest(e))),Ar.default.runtime.onConnect.removeListener(v)}),b.postMessage(r))};Ar.default.runtime.onConnect.addListener(v)})},Uc=async e=>{let[t]=e.params,r=t.tabId,n=t.icon??void 0,o=Ke.isFeatureEnabled("enable-sidepanel-tx-notifications")&&await Ai(),l=bo(t.url),f=Qe({url:l,tabId:r,req:e,icon:n});return o?vl(e.id,e.method,f):kl(e.id,e.method,f)};p();d();var Tl=new ye,Oc=async e=>await Vo(Tl,e);p();d();var qc=()=>{};p();d();var Hc=()=>{throw new Error("This method is unsupported on extension.")};p();d();var Fc=async()=>!0;p();d();var Wc=async()=>{throw new Error("not implemented")};var Ks={};Ie(Ks,{addChain:()=>jc,autoConfirmValidateMessage:()=>Yc,autoConfirmValidateTransaction:()=>Xc,doesSupportEth:()=>Jc,getAccountNonce:()=>Qc,getAccounts:()=>Zc,getEnabledChains:()=>ep,getNetworkId:()=>Mt,getRpcURL:()=>tp,getSelectedAccount:()=>rp,getSupportedNetworks:()=>np,sendTransaction:()=>sp,signAndSendTransaction:()=>op,signMessage:()=>ip,signTypedData:()=>ap,switchChain:()=>cp});p();d();p();d();var jc=async e=>{let t=parseInt(e.chainId.substring(2),16);return!!kt(t)};p();d();p();d();var Vc=$(Ze());var _l=new ye,zs=class extends bi{constructor(){super(_l)}async isTabFocused(t){this.executedChecksAudit.push("IS_TAB_FOCUSED");let n=(await Vc.default.tabs.query({active:!0,lastFocusedWindow:!0}))[0]?.url;return n?new URL(n).hostname===new URL(t).hostname:!1}async isFeatureEnabled(){return this.executedChecksAudit.push("IS_FEATURE_FLAG_ENABLED"),(await Ke.getFeatureFlags())["kill-automatic-approval"]!==!0}async isWalletUnlocked(){return this.executedChecksAudit.push("IS_WALLET_UNLOCKED"),!!Se()}async isDappWhitelistOverridden(){return this.executedChecksAudit.push("IS_DAPP_WHITELIST_OVERRIDDEN"),(await Ke.getFeatureFlags())["enable-automatic-approval-for-all-dapps"]===!0}},ft=new zs;p();d();p();d();var Sr={};p();d();var Kc=[new ye];async function $c(){for(let e of Kc){let t=await e.get("developerMode");if(t)return t.isDeveloperMode}return!1}async function Gc(){for(let e of Kc){let t=await e.get("networkSetting");if(t)return t.setting}return Mo.setting}var Mt=async e=>{if(await $c()){let o=(await Gc()).eip155;if(!o)throw new Error("No evm developer mode chainId set");let l=o.replace("eip155:","");return Ir(l)}let r=Sr[e]??"eip155:1",n=Ee.getEVMNetworkIDValue(r);return Ir(n.toString())};var Yc=async(e,t)=>{let r=await Mt(e.origin);if(!r)return{status:"UNSUPPORTED_NETWORK_ID"};let n=vt(r);if(!Ee.isEVMNetworkID(n))return{status:"UNSUPPORTED_NETWORK_ID"};let a=await ue(),o=await we(a,"eip155"),l=Tt(Xe.language)??"en",f=await be.getDeviceId();return await ft.isEthMessageAutoConfirmable({url:e,networkID:n,data:t,account:a,userAccountAddress:o,accountIdentifier:a.identifier},{locale:l,deviceId:f})};p();d();var Xc=async(e,t)=>{let r=t[0]?.chainId;if(!r)return{status:"UNSUPPORTED_NETWORK_ID"};let n=vt(r);if(!Ee.isEVMNetworkID(n))return{status:"UNSUPPORTED_NETWORK_ID"};let a=await ue(),o=await we(a,"eip155"),l=Tt(Xe.language)??"en",f=await be.getDeviceId();return ft.isEthTransactionAutoConfirmable({url:e,networkID:n,data:t,account:a,userAccountAddress:o,accountIdentifier:a.identifier},{locale:l,deviceId:f})};p();d();var Jc=async()=>(await ue()).addresses.some(t=>t.addressType==="eip155");p();d();var El=new ye,Qc=async(e,t)=>{let r=parseInt(e.replace("0x",""),16),n=kt(r);if(!n)throw new Error(`Unsupported EVM Network ID: ${r}`);return ei(n,t,El)};p();d();var Zc=async()=>{let e=await ue();return[we(e,"eip155").toLowerCase()]};p();d();var Il=new ye,ep=async()=>{let t=(await Il.get("developerMode"))?.isDeveloperMode===!0?"testnets_only":"mainnets_only",r=nr("enable-monad");return rr(r,t)};p();d();var tp=async e=>{let t=tr.parse(`eip155:${parseInt(e,16)}`);if(!Ee.isEVMNetworkID(t))throw new Error(`Invalid EVM NetworkID for chainID: ${e}`);let r=Ee.getRpcProxyUrl(t);if(!r)throw new Error(`No RPC URL found for chainID ${e}`);return r};p();d();var rp=async()=>{let e=await ue();return we(e,"eip155").toLowerCase()};p();d();var np=()=>{let e=nr("enable-monad");return rr(e)};p();d();p();d();var Bl=new ye,Rr=async(e,t)=>{let r=await ue();we(r,"eip155");let n=Xe.t("transactionsAppInteraction"),a=tr.parse(`eip155:${e.chainId?parseInt(e.chainId,16):void 0}`),o=Uo(e.to??"",void 0,void 0,5),l=ko(e.value??"",Ee.getTokenDecimals(a)),f=Ee.getTokenSymbol(a);if(e.nonce===void 0)throw new Error("nonce missing on unsigned transaction");if(e.maxFeePerGas==null||e.maxPriorityFeePerGas==null){let v=(await Yo(a,!0))?.standard;if(v)e.maxFeePerGas==null&&(e.maxFeePerGas=`0x${v.maxFeePerGas.integerValue(St.ROUND_CEIL).toString(16)}`),e.maxPriorityFeePerGas==null&&(e.maxPriorityFeePerGas=`0x${v.maxPriorityFeePerGas.integerValue(St.ROUND_CEIL).toString(16)}`);else throw new Error("Standard transaction speed price information is missing.")}let m={accountSigner:de,accountIdentifier:r.identifier,networkID:a,unsignedTransaction:e,callPayload:void 0,pendingTransactionInput:{ownerAddress:e.from,networkID:a,data:{nonce:e.nonce,hash:"",unsignedTransaction:oo.parse({from:e.from})},type:"dappInteraction",display:{summary:{topLeft:{text:n},topRight:{text:l?`- ${l} ${f}`:""}},detail:{uiRecipient:o}}},storage:Bl};return t?await ti({...m,signature:t}):await ri(m)};var sp=Rr;p();d();var op=Rr;p();d();var ip=async e=>{let t=Si(e),r=await ue();we(r,"eip155");let n=await de.sign(r.identifier,{chainType:"eip155",signingType:"message",message:t});if(n.status==="error")throw new Error(`[${n.type}] ${n.message}`);return n.signature};p();d();var ap=async e=>{let t=await ue();we(t,"eip155");let r=await de.sign(t.identifier,{chainType:"eip155",signingType:"typedData",...e});if(r.status==="error")throw new Error(`[${r.type}] ${r.message}`);return r.signature};p();d();var cp=async(e,t)=>{let r=parseInt(e.substring(2),16),n=kt(r);return n?(Sr[t]=n,!0):!1};var Gs={};Ie(Gs,{addPriorityFee:()=>dp,autoConfirmValidateMessage:()=>lp,autoConfirmValidateTransaction:()=>up,getNetworkId:()=>He,getSelectedAccount:()=>mp,handleSolanaPayTransaction:()=>Ap,handleSolanaPayTransfer:()=>Sp,sendAllTransactions:()=>yp,sendTransaction:()=>fp,signAllTransactions:()=>vr,signMessage:()=>wp,signTransaction:()=>bp});p();d();p();d();p();d();var He=async()=>{let e=await ue(),t=Do(e,"solana").networkID;return Ee.getSolanaNetworkIDValue(t)};var dp=async e=>{let{transaction:t}=$e.deserializeVersionedTransaction(e);t.version==="legacy"&&(t=$e.deserializeWithVersion({transaction:e,type:"legacy"}).transaction);let r=await He(),n=await Zo(t,{connection:pt(Me(r)),calculators:{cost:Qo,budget:new Jo(2e5,2e5)}});return $e.serialize(n).transaction};p();d();var lp=async(e,t)=>{let r=await He();if(r==="localnet")return{status:"UNSUPPORTED_NETWORK_ID"};let n=await ue(),a=await we(n,"solana");return ft.isSolMessageAutoConfirmable({url:e,data:t,networkID:Me(r),account:n,userAccountAddress:a,accountIdentifier:n.identifier??""})};p();d();var up=async(e,t)=>{let r=await He();if(r==="localnet")return{status:"UNSUPPORTED_NETWORK_ID"};let n=await ue(),a=await we(n,"solana"),o=Tt(Xe.language)??"en",l=await be.getDeviceId();return ft.isSolTransactionAutoConfirmable({url:e,networkID:Me(r),data:t,account:n,userAccountAddress:a,accountIdentifier:n.identifier??""},{locale:o,deviceId:l})};p();d();var mp=async()=>{let e=await ue();return we(e,"solana")};p();d();var hp=$(st());var fp=async(e,t)=>{let r=await He(),n=it.VersionedMessage.deserializeMessageVersion(hp.default.decode(Rt(e))),a=$e.deserializeWithVersion({transaction:e,type:n});await ni({connection:pt(Me(r)),signedTx:a.transaction,opts:t})};p();d();var gp=$(st());var yp=async(e,t)=>{let r=await He(),n=pt(Me(r)),a=e.map(o=>{let l=it.VersionedMessage.deserializeMessageVersion(gp.default.decode(Rt(o)));return $e.deserializeWithVersion({transaction:o,type:l}).transaction});return await si({connection:n,signedTxs:a,opts:t})};p();d();var xp=$(st());var wp=async e=>{let t=await ue();we(t,"solana");let r=xp.default.decode(e).toString("utf-8"),n=await de.sign(t.identifier,{chainType:"solana",signingType:"message",message:r});if(n.status!=="success")throw new Error(`[${n.type}] ${n.message}`);return n.signature};p();d();p();d();var $s=$(st());var Ll=new ye,vr=async e=>{let t=await ue();we(t,"solana");let r=we(t,"solana"),n=await He(),a={ownerAddress:r,networkID:Me(n),data:{signature:""},type:"dappInteraction",display:{summary:{topLeft:{text:Xe.t("transactionsPendingAppInteraction")}}}};return(async()=>{let l=[];for(let f=0;f<e.length;f++){let m=e[f],u=it.VersionedMessage.deserializeMessageVersion($s.default.decode(Rt(m))),v=$e.deserializeWithVersion({transaction:m,type:u}),b=await oi({pendingTransactionInput:a,accountIdentifier:t.identifier,feePayer:new le.PublicKey(r),connection:pt(Me(n)),accountSigner:de,transaction:v.transaction,storage:Ll}),S=$e.serialize(b);l.push({transaction:S.transaction,signature:$s.default.encode(lo(b)[0]),version:"version"in b?b.version:"legacy"})}return l})()};var bp=async e=>(await vr([e]))[0];p();d();var Ap=async()=>{};p();d();var Sp=async()=>!1;var Xs={};Ie(Xs,{getSelectedAccount:()=>kr,signMessage:()=>vp,signPSBT:()=>Rp});p();d();p();d();var kr=async()=>{let e=await ue();return Ys(e)},Ys=e=>{let t=e.addresses.filter(a=>a.networkID==="bip122:000000000019d6689c085ae165831e93"||a.networkID==="bip122:000000000933ea01ad0ee984209779ba");if(t.length===0)throw No;if(t.length===1){let[a]=t;if(!a?.publicKey)throw new Error("[btc provider] selected account account does not have a public key");return[{address:a.address,publicKey:Buffer.from(a.publicKey).toString("hex"),addressType:a.addressType,purpose:"payment"},{address:a.address,publicKey:Buffer.from(a.publicKey).toString("hex"),addressType:a.addressType,purpose:"ordinals"}]}let r=t.find(a=>a.addressType==="bip122_p2tr");if(!r?.publicKey)throw new Error("[btc provider] ordinalAddress does not have a public key");let n=t.find(a=>a.addressType==="bip122_p2wpkh");if(!n?.publicKey)throw new Error("[btc provider] paymentAddress does not have a public key");return[{address:n.address,publicKey:Buffer.from(n.publicKey).toString("hex"),addressType:n.addressType,purpose:"payment"},{address:r.address,publicKey:Buffer.from(r.publicKey).toString("hex"),addressType:r.addressType,purpose:"ordinals"}]};p();d();var Rp=async(e,t)=>{let r=await ue(),n=await de.sign(r.identifier,{chainType:"bip122_p2tr",signingType:"transaction",message:Buffer.from(e).toString("hex"),inputsToSign:t.inputsToSign,finalize:t.finalize});if(n.status==="error")throw new Error(`[${n.type}] ${n.message}`);return Buffer.from(n.signature,"hex")};p();d();var vp=async(e,t)=>{let r=await ue(),n=Co("bip122:000000000019d6689c085ae165831e93",e),a=await de.sign(r.identifier,{chainType:n,signingType:"message",message:t});if(a.status==="error")throw new Error(`[btc provider][${a.type}] ${a.message}`);let o=a.message;if(!o)throw new Error("[btc provider] no message on vault response");return{signature:Buffer.from(a.signature,"hex"),signedMessage:o}};var Yt={analytics:Ac,config:Sc,i18n:Rc,logger:vc,rpc:kc,utils:_c,wallet:Js,featureFlags:bc,secrets:{setSharedSecret:()=>{throw new Error("unimplemented")},encryptDeepLinkDappPayload:()=>{throw new Error("unimplemented")},deleteSharedSecret:()=>{throw new Error("unimplemented")},generateSharedSecret:()=>{throw new Error("unimplemented")},generateSession:()=>{throw new Error("unimplemented")},decryptDeepLinkDappPayload:()=>{throw new Error("unimplemented")}}};var Pl=bt.eth.initialize(Yt),Nl=bt.sol.initialize(Yt),Dl=bt.sol_blink.initialize(Yt),Ul=bt.btc.initialize(Yt),kp={...Ul,...Pl,...Nl,...Dl};p();d();var Tr=[],Qs=e=>{Tr.push(e)},Zs=e=>{Tr.splice(Tr.findIndex(t=>{let{origin:r,tabId:n,port:a}=t;return e.origin===r&&e.tabId===n&&a===e.port}),1)},Pt=()=>Tr.map(({origin:e,tabId:t,port:r})=>({origin:e,tabId:t,send:n=>r.postMessage(JSON.stringify(n))}));var Tp=async e=>{if(!e.sender||!e.sender.tab||!e.sender.url||!e.sender.tab.id)throw new Error("sender information is undefined");let t=e.sender.url,r=e.sender.tab.id;Qs({origin:new URL(t).origin,url:t,tabId:r,port:e}),e.onDisconnect.addListener(()=>{Zs({origin:new URL(t).origin,url:t,tabId:r,port:e})});let n=[],a=v=>{if(typeof v=="string")try{let b=Qt.parse(at(v));n.push(b)}catch{}};e.onMessage.addListener(a);let o=await Ri(r),l={type:"BrowserTabRpcRequestSource",url:new URL(t),tabId:r,title:o.title,icon:o.iconUrl?new URL(o.iconUrl):void 0},f=Ol(e,l,n),m=ql(e);Qs({origin:new URL(t).origin,url:t,tabId:r,port:e}),e.onDisconnect.addListener(()=>{Zs({origin:new URL(t).origin,url:t,tabId:r,port:e})});let u=new fi({routes:kp}).init(f,m);return e.onMessage.removeListener(a),u};function Ol(e,t,r=[]){return new ReadableStream({async start(n){for(let a of r)n.enqueue(Ur(a,t));e.onMessage.addListener(async a=>{let o=null;try{o=Qt.parse(at(a))}catch{}if(o)try{n.enqueue(Ur(o,t))}catch(l){Y.captureError(new Error(`Stream enqueue failure (${l})`),"provider")}}),e.onDisconnect.addListener(()=>{n.close()})},async cancel(){e.disconnect()}},new CountQueuingStrategy({highWaterMark:1/0}))}function ql(e){return new WritableStream({write([t,r]){e.postMessage(Qe(r))}})}p();d();var Xt=async({enabledChains:e,metamaskOverrideSetting:t})=>{let r=["solana.js"];if(e.includes("eip155"))switch(t){case"ALWAYS_ASK":{r.push("evmAsk.js");break}case"USE_PHANTOM":{r.push("evmPhantom.js");break}case"USE_METAMASK":{r.push("evmMetamask.js");break}default:wo(t)}e.some(n=>n==="bip122_p2tr"||n==="bip122_p2wpkh")&&r.push("btc.js");try{await chrome.scripting.registerContentScripts([{id:"inpageScripts",matches:["file://*/*","http://*/*","https://*/*"],js:r,runAt:"document_start",allFrames:!0,world:"MAIN"},{id:"contentScript",matches:["file://*/*","http://*/*","https://*/*"],js:["contentScript.js","solanaActionsContentScript.js"],runAt:"document_start",allFrames:!0,world:"ISOLATED"}])}catch{}};async function Jt(){try{let t=(await chrome.scripting.getRegisteredContentScripts()).map(r=>r.id);return chrome.scripting.unregisterContentScripts({ids:t})}catch(e){throw console.error("ERROR REGISTERING",e),new Error("An unexpected error occurred while unregistering dynamic content scripts: "+e.message,{cause:e})}}p();d();var _p=$(qp()),Ep=$(Ze());var Hl=new Ho(be),Nt=new ye,eo=new Je(Nt);async function Fl(){let{isOnboarded:e}=await de.checkVaultIntegrity();e&&Ip()}var Wl=(0,_p.default)(Ip,500);async function Ip(){let e=await ue(),t=await et(),r=await Lr(de,Nt,t);Ke.setSubjectAttributes({rootID:e?.type==="seed"?e.seedIdentifier:void 0}),await Hl.identifyAccount(r,e,t)}async function Cp(){Fl(),Nt.subscribe(e=>{switch(e){case".phantom-labs.account.selectedAccount":{Vl();return}case"developerMode":case"networkSetting":{jl();return}case"metaMaskOverride":{Kl();return}case Bo:{$l();return}}}),eo.addEventListener("revoked",e=>{zl(e.data)})}async function jl(){try{let e=await ue(),t=await eo.get(e.identifier);for(let r of Pt()){let n=r.origin.toLowerCase();if(n in t){let l={jsonrpc:"2.0",method:"phantom_chainChanged",params:{evm:await Mt(n)}};r.send(l)}}}catch(e){Y.captureError(e,"provider")}}async function Vl(){try{await Wl()}catch(a){Y.captureError(a,"provider")}let e=await ue(),t;try{t=we(e,"eip155")}catch{}let r;try{r=we(e,"solana")}catch{}let n;try{n=await kr()}catch{}if(t||r||n){let a=await eo.get(e.identifier);for(let o of Pt()){let l={btc:[]};t&&(l.evm=t),r&&(l.sol=r),n&&(l.btc=n),o.send({jsonrpc:"2.0",method:"phantom_accountChanged",params:o.origin in a?l:null})}}else for(let a of Pt())a.send({jsonrpc:"2.0",method:"phantom_accountChanged",params:null})}async function zl(e){let t=await et(),n=(await Lr(de,Nt,t)).find(m=>m.identifier===e.accountId),a=n?.addresses?.find(m=>m.addressType==="eip155")?.address,o=n?.addresses?.find(m=>m.addressType==="solana")?.address,l=n?Ys(n):void 0;if(!a&&!o&&!l)return;let f={};a&&(f.evm=a),o&&(f.sol=o),l&&(f.btc=l);for(let m of Pt())m.origin.toLowerCase()===e.origin.toLowerCase()&&m.send({jsonrpc:"2.0",method:"phantom_trustRevoked",params:f})}function Kl(){for(let e of Pt())(async()=>{try{await Ep.default.tabs.sendMessage(e.tabId,{jsonrpc:"2.0",method:"phantom_metaMaskOverrideSettingsChanged",params:null})}catch{}})();(async()=>{try{let e=await et(),t=await ct(Nt);await Jt(),await Xt({enabledChains:e.enabledAddressTypes,metamaskOverrideSetting:t})}catch(e){console.error("Could not re-register content scripts",e)}})()}function $l(){(async()=>{try{let e=await et(),t=await ct(Nt);await Jt(),await Xt({enabledChains:e.enabledAddressTypes,metamaskOverrideSetting:t})}catch(e){console.error("Could not re-register content scripts",e)}})()}p();d();function Bp(e){switch(e){case"solana:101":return Pe.SOLANA_MAINNET;case"solana:102":return Pe.SOLANA_TESTNET;case"solana:103":return Pe.SOLANA_DEVNET;default:return Object.values(Pe).find(t=>t===e)?e:null}}var Pp=20*1e3,Ve=new ye,Gl=async()=>{await ii(),await Xl(),Ut.init({provider:mi}),await ui(),Ve.remove("unencryptedSeedAndMnemonic"),Yl(),Cp(),await Jt(),await Xt({enabledChains:(await et().catch(()=>({enabledAddressTypes:_o.getAll()}))).enabledAddressTypes,metamaskOverrideSetting:await ct(Ve).catch(()=>jo.enum.ALWAYS_ASK)})};Gl().catch(e=>{console.error("w error",e)});async function Yl(){if((await De.default.tabs.query({url:De.default.runtime.getURL("onboarding.html")})).length>0)return;let r=await xt.hasPersistedDeviceEncryptionKey(),n=await Ve.get(Or);!r&&!n&&(await Ve.set("firstTimeOnboarding",{isFirstTimeOnboarding:!0}),ai({url:"onboarding.html"}),Ve.set(Or,!0))}function Xl(){De.default.runtime.onInstalled.addListener(Zl),De.default.runtime.onConnect.addListener(tu),De.default.alarms.onAlarm.addListener(ru),De.default.runtime.onMessage.addListener(mo(iu)),De.default.webRequest.onBeforeRequest.addListener(Jl,{urls:["http://*/*","https://*/*"]})}async function Jl(e){return e.tabId===De.default.tabs.TAB_ID_NONE?{}:"documentLifecycle"in e&&e.documentLifecycle==="prerender"?{}:((e.type==="main_frame"||e.type==="sub_frame")&&await Ql(e.url,e.tabId),{})}async function Ql(e,t){let r=new ye,n=await $o(r,be,e);if(!n.result)return;if(n.isSubdomainDomainWhitelisted){let l=await Ve.get("userWhitelistedSubdomains");if(JSON.parse(`${l}`)?.includes(`${n.source}`))return}let a=await Ve.get("userWhitelistedOrigins");JSON.parse(`${a}`)?.includes(`${n.source}`)||(be.capture("phishingBlocklistRedirect",{data:{targetURL:e}}),await De.default.tabs.update(t,{url:`phishing.html?origin=${e}&subdomain=${n.isSubdomainDomainWhitelisted}`}))}function Zl(e){switch(e.reason){case"install":{be&&be.capture("appInstall");break}case"update":{Y.addBreadcrumb("startUp",`Extension updated from ${e.previousVersion} to ${yo}`,"debug");break}default:break}}async function eu(){if(rn())return;let e=await Ve.get("lockTimerMs"),t=Date.now()+(e??Fo);De.default.alarms.create("lockExtension",{when:t})}async function tu(e){if(e.onDisconnect.addListener(eu),(e.sender?.url===De.default.runtime.getURL("popup.html")||e.sender?.url===De.default.runtime.getURL("notification.html"))&&(rn()?(Ht(),Np(to),ro()):await De.default.alarms.clear("lockExtension")),e.name==="contentscript"){if(e.sender&&e.sender.url&&e.sender.tab&&e.sender.tab.id&&(e.sender.frameId===0||e.sender.documentLifecycle==="prerender")){let r=e.postMessage.bind(e);e.postMessage=a=>{r(a)},(await Tp(e)).start()}e.postMessage("phantom#background#initialized"),e.onMessage.addListener(r=>{typeof r=="string"&&r==="phantom#ready_to_setup_streams"&&e.postMessage("phantom#background#initialized")})}}function ru(e){if(e.name.startsWith("deleteStorageValue")){let[t,r]=e.name.split(":");Ve.remove(r)}e.name==="lockExtension"&&(ka(!0),Ht(),ro())}async function ro(){await Ve.set(qr,!0)}async function nu(){await Ve.set(qr,!1)}async function su(e,t){try{let[r,n,a]=await Promise.all([et(),ct(Ve),pu.get()]),o=Xr(t),{hideProvidersArray:l,showMetamaskExplainer:f,dontOverrideWindowEthereum:m}=Gr(o,a);return s.result(e.id,{enabledChains:r.enabledAddressTypes,metamaskOverrideSetting:n,hideProvidersArray:l,showMetamaskExplainer:f,dontOverrideWindowEthereum:m})}catch(r){return Y.captureError(r,"provider"),s.internalError(e.id,r?.message)}}async function ou(e){try{let t=await Ke.getFeatureFlags();if(e.method==="blinksEnabled"){let r=t["enable-experimental-solana-actions"],n=await zo(Ve,!1),a=r===!0&&n===!0;return s.result(e.id,a)}if(e.method==="BlinksV2Config"){let r=await et();return s.result(e.id,{enabled:t["enable-blinks-v2-config"]===!0,supportedBlockchains:r.enabledNetworkIDs.map(Bp).filter(n=>n!=null)})}return s.methodNotFound(e.id)}catch(t){return Y.captureError(t,"provider"),s.internalError(e.id)}}async function iu(e,t){let r=t.url;if(r&&e.method==="providerInjectionOptions")return su(e,r);if(r&&e.method==="blinksEnabled"||e.method==="BlinksV2Config")return ou(e);if(r&&e.method==="blinksLog"){let{event:o,body:l}=e.params;return be.capture(o,{data:l}),s.result(e.id,"success")}if(!r||!r.startsWith(sr()))return Y.captureMessage(`Unauthorized request from origin: ${er(r)}`,"generic"),s.result(e.id,`${e.method} not permitted`);if(!Object.values(hi).includes(e.method))return Y.captureMessage("Unauthorized method","generic"),s.result(e.id,`${e.method} not permitted`);switch(e.method){case"hasOnboarded":return Ra(e);case"resetHasOnboarded":return va(e);case"lockExtension":{let o=Ea(e);return Np(to),ro(),o}case"unlockExtension":{let o=Ta(e);return au().then(l=>{to=l}),nu(),o}case"IsExtensionUnlocked":return _a(e);case"setPassword":return Ia(e);case"verifyPassword":return Ca(e);case"updatePassword":return Ba(e);case"importPrivateKeyAccount":return Zi(e);case"importReadOnlyAccount":return ea(e);case"importSeedAccount":return ta(e);case"importSocialSeedAccount":return ra(e);case"isExistingSeed":return na(e);case"addAccountForSeed":return sa(e);case"addAccountForSeedless":return oa(e);case"addLedgerAccounts":return ia(e);case"exportPrivateKey":return aa(e);case"exportEntropy":return ca(e);case"setAccountIcon":return pa(e);case"deriveAddresses":return da(e);case"removeAccount":return la(e);case"reorderAccount":return ua(e);case"getAllAccounts":return ma(e);case"getAllSeeds":return ha(e);case"getAllSeedlessSeeds":return fa(e);case"sign":return ga(e);case"getAuthenticationPublicKey":return ya(e);case"syncAccounts":return xa(e);case"checkVaultIntegrity":return wa(e);case"logMessage":return La(e);case"downloadLogs":return Ma(e);case"serviceWorkerMutexAcquire":return Aa(e);case"serviceWorkerMutexRelease":return Sa(e)}return s.methodNotFound(e.id)}var to,Lp=(0,Mp.default)(async()=>await De.default.storage.local.set({"last-sw-ping":new Date().getTime()}),Pp);async function au(){return await Lp(),setInterval(Lp,Pp)}async function Np(e){clearInterval(e)}var cu=60*10*1e3,pu=new vo(new ye,cu,()=>Yr().catch(e=>(Y.captureError(e,"provider"),[])),"phantomwallet-dApps-list");export{pu as dAppsListCache};
/*! Bundled license information:

apg-js/dist/apg-lib-bundle.js:
  (*!
   * The buffer module from node.js, for the browser.
   *
   * <AUTHOR> Aboukhadijeh <https://feross.org>
   * @license  MIT
   *)
  (*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> *)
*/
//# sourceMappingURL=serviceWorker.js.map
