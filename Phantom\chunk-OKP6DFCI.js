import{c as yn,k as ht,o as Xt}from"./chunk-WIQ4WVKX.js";import{a as Na}from"./chunk-N7UFQNLW.js";import{a as D}from"./chunk-7X4NV6OJ.js";import{f as M,h as i,i as p,n as s}from"./chunk-3KENBVE7.js";i();s();i();s();var yr=M(D(),1),Ut=M(D(),1);i();s();var xn=M(D(),1),Jt=(0,xn.createContext)({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"});i();s();var vn=M(D(),1),St=(0,vn.createContext)({});i();s();var ot=M(D(),1);i();s();var Pn=M(D(),1),Ct=(0,Pn.createContext)(null);i();s();var ro=M(D(),1);i();s();var At=typeof document<"u";var dt=At?ro.useLayoutEffect:ro.useEffect;i();s();var bn=M(D(),1),no=(0,bn.createContext)({strict:!1});function Vn(t,e,o,r){let{visualElement:n}=(0,ot.useContext)(St),c=(0,ot.useContext)(no),a=(0,ot.useContext)(Ct),l=(0,ot.useContext)(Jt).reducedMotion,u=(0,ot.useRef)();r=r||c.renderer,!u.current&&r&&(u.current=r(t,{visualState:e,parent:n,props:o,presenceContext:a,blockInitialAnimation:a?a.initial===!1:!1,reducedMotionConfig:l}));let m=u.current;return(0,ot.useInsertionEffect)(()=>{m&&m.update(o,a)}),dt(()=>{m&&m.render()}),(0,ot.useEffect)(()=>{m&&m.updateFeatures()}),(self.HandoffAppearAnimations?dt:ot.useEffect)(()=>{m&&m.animationState&&m.animationState.animateChanges()}),m}i();s();var Tn=M(D(),1);i();s();function gt(t){return typeof t=="object"&&Object.prototype.hasOwnProperty.call(t,"current")}function Sn(t,e,o){return(0,Tn.useCallback)(r=>{r&&t.mount&&t.mount(r),e&&(r?e.mount(r):e.unmount()),o&&(typeof o=="function"?o(r):gt(o)&&(o.current=r))},[e])}i();s();var ao=M(D(),1);i();s();i();s();function yt(t){return typeof t=="string"||Array.isArray(t)}i();s();i();s();function wt(t){return typeof t=="object"&&typeof t.start=="function"}i();s();var io=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],Oe=["initial",...io];function jt(t){return wt(t.animate)||Oe.some(e=>yt(t[e]))}function so(t){return!!(jt(t)||t.variants)}function Cn(t,e){if(jt(t)){let{initial:o,animate:r}=t;return{initial:o===!1||yt(o)?o:void 0,animate:yt(r)?r:void 0}}return t.inherit!==!1?e:{}}function wn(t){let{initial:e,animate:o}=Cn(t,(0,ao.useContext)(St));return(0,ao.useMemo)(()=>({initial:e,animate:o}),[An(e),An(o)])}function An(t){return Array.isArray(t)?t.join(" "):t}i();s();i();s();var Mn={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},Nt={};for(let t in Mn)Nt[t]={isEnabled:e=>Mn[t].some(o=>!!e[o])};function Dn(t){for(let e in t)Nt[e]={...Nt[e],...t[e]}}i();s();i();s();var Rn=M(D(),1);function ct(t){let e=(0,Rn.useRef)(null);return e.current===null&&(e.current=t()),e.current}i();s();var Mt={hasAnimatedSinceResize:!0,hasEverUpdated:!1};var Ua=1;function En(){return ct(()=>{if(Mt.hasEverUpdated)return Ua++})}i();s();var Ln=M(D(),1),Qt=(0,Ln.createContext)({});i();s();var Bn=M(D(),1),co=(0,Bn.createContext)({});i();s();var kn=Symbol.for("motionComponentSymbol");function Fn({preloadedFeatures:t,createVisualElement:e,useRender:o,useVisualState:r,Component:n}){t&&Dn(t);function c(l,u){let m,f={...(0,Ut.useContext)(Jt),...l,layoutId:Ga(l)},{isStatic:h}=f,d=wn(l),g=h?void 0:En(),y=r(l,h);if(!h&&At){d.visualElement=Vn(n,y,f,e);let v=(0,Ut.useContext)(co),P=(0,Ut.useContext)(no).strict;d.visualElement&&(m=d.visualElement.loadFeatures(f,P,t,g,v))}return yr.createElement(St.Provider,{value:d},m&&d.visualElement?yr.createElement(m,{visualElement:d.visualElement,...f}):null,o(n,l,g,Sn(y,d.visualElement,u),y,h,d.visualElement))}let a=(0,Ut.forwardRef)(c);return a[kn]=n,a}function Ga({layoutId:t}){let e=(0,Ut.useContext)(Qt).id;return e&&t!==void 0?e+"-"+t:t}i();s();function In(t){function e(r,n={}){return Fn(t(r,n))}if(typeof Proxy>"u")return e;let o=new Map;return new Proxy(e,{get:(r,n)=>(o.has(n)||o.set(n,e(n)),o.get(n))})}i();s();i();s();i();s();var On=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function te(t){return typeof t!="string"||t.includes("-")?!1:!!(On.indexOf(t)>-1||/[A-Z]/.test(t))}i();s();var go=M(D(),1);i();s();var Wn=M(D(),1);i();s();i();s();var ee={};function jn(t){Object.assign(ee,t)}i();s();var oe=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],Y=new Set(oe);function lo(t,{layout:e,layoutId:o}){return Y.has(t)||t.startsWith("origin")||(e||o!==void 0)&&(!!ee[t]||t==="opacity")}i();s();var L=t=>!!(t&&t.getVelocity);i();s();i();s();var $a={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},za=oe.length;function Nn(t,{enableHardwareAcceleration:e=!0,allowTransformNone:o=!0},r,n){let c="";for(let a=0;a<za;a++){let l=oe[a];if(t[l]!==void 0){let u=$a[l]||l;c+=`${u}(${t[l]}) `}}return e&&!t.z&&(c+="translateZ(0)"),c=c.trim(),n?c=n(t,r?"":c):o&&r&&(c="none"),c}i();s();var Un=t=>e=>typeof e=="string"&&e.startsWith(t),uo=Un("--"),mo=Un("var(--");i();s();var Gn=(t,e)=>e&&typeof t=="number"?e.transform(t):t;i();s();i();s();i();s();var Q=(t,e,o)=>Math.min(Math.max(o,t),e);var rt={test:t=>typeof t=="number",parse:parseFloat,transform:t=>t},Dt={...rt,transform:t=>Q(0,1,t)},je={...rt,default:1};i();s();i();s();var Rt=t=>Math.round(t*1e5)/1e5,Et=/(-)?([\d]*\.?[\d])+/g,fo=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,$n=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function xt(t){return typeof t=="string"}var Ne=t=>({test:e=>xt(e)&&e.endsWith(t)&&e.split(" ").length===1,parse:parseFloat,transform:e=>`${e}${t}`}),lt=Ne("deg"),H=Ne("%"),b=Ne("px"),zn=Ne("vh"),Hn=Ne("vw"),xr={...H,parse:t=>H.parse(t)/100,transform:t=>H.transform(t*100)};i();s();var vr={...rt,transform:Math.round};var po={borderWidth:b,borderTopWidth:b,borderRightWidth:b,borderBottomWidth:b,borderLeftWidth:b,borderRadius:b,radius:b,borderTopLeftRadius:b,borderTopRightRadius:b,borderBottomRightRadius:b,borderBottomLeftRadius:b,width:b,maxWidth:b,height:b,maxHeight:b,size:b,top:b,right:b,bottom:b,left:b,padding:b,paddingTop:b,paddingRight:b,paddingBottom:b,paddingLeft:b,margin:b,marginTop:b,marginRight:b,marginBottom:b,marginLeft:b,rotate:lt,rotateX:lt,rotateY:lt,rotateZ:lt,scale:je,scaleX:je,scaleY:je,scaleZ:je,skew:lt,skewX:lt,skewY:lt,distance:b,translateX:b,translateY:b,translateZ:b,x:b,y:b,z:b,perspective:b,transformPerspective:b,opacity:Dt,originX:xr,originY:xr,originZ:b,zIndex:vr,fillOpacity:Dt,strokeOpacity:Dt,numOctaves:vr};function re(t,e,o,r){let{style:n,vars:c,transform:a,transformOrigin:l}=t,u=!1,m=!1,f=!0;for(let h in e){let d=e[h];if(uo(h)){c[h]=d;continue}let g=po[h],y=Gn(d,g);if(Y.has(h)){if(u=!0,a[h]=y,!f)continue;d!==(g.default||0)&&(f=!1)}else h.startsWith("origin")?(m=!0,l[h]=y):n[h]=y}if(e.transform||(u||r?n.transform=Nn(t.transform,o,f,r):n.transform&&(n.transform="none")),m){let{originX:h="50%",originY:d="50%",originZ:g=0}=l;n.transformOrigin=`${h} ${d} ${g}`}}i();s();var ne=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function Pr(t,e,o){for(let r in e)!L(e[r])&&!lo(r,o)&&(t[r]=e[r])}function Ha({transformTemplate:t},e,o){return(0,Wn.useMemo)(()=>{let r=ne();return re(r,e,{enableHardwareAcceleration:!o},t),Object.assign({},r.vars,r.style)},[e])}function Wa(t,e,o){let r=t.style||{},n={};return Pr(n,r,t),Object.assign(n,Ha(t,e,o)),t.transformValues?t.transformValues(n):n}function Kn(t,e,o){let r={},n=Wa(t,e,o);return t.drag&&t.dragListener!==!1&&(r.draggable=!1,n.userSelect=n.WebkitUserSelect=n.WebkitTouchCallout="none",n.touchAction=t.drag===!0?"none":`pan-${t.drag==="x"?"y":"x"}`),t.tabIndex===void 0&&(t.onTap||t.onTapStart||t.whileTap)&&(r.tabIndex=0),r.style=n,r}i();s();i();s();var Ka=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onLayoutAnimationStart","onLayoutAnimationComplete","onLayoutMeasure","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","ignoreStrict","viewport"]);function Ue(t){return t.startsWith("while")||t.startsWith("drag")&&t!=="draggable"||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||Ka.has(t)}var _n=t=>!Ue(t);function _a(t){t&&(_n=e=>e.startsWith("on")?!Ue(e):t(e))}try{_a(Na().default)}catch{}function Yn(t,e,o){let r={};for(let n in t)n==="values"&&typeof t.values=="object"||(_n(n)||o===!0&&Ue(n)||!e&&!Ue(n)||t.draggable&&n.startsWith("onDrag"))&&(r[n]=t[n]);return r}i();s();var Jn=M(D(),1);i();s();i();s();function qn(t,e,o){return typeof t=="string"?t:b.transform(e+o*t)}function Zn(t,e,o){let r=qn(e,t.x,t.width),n=qn(o,t.y,t.height);return`${r} ${n}`}i();s();var Ya={offset:"stroke-dashoffset",array:"stroke-dasharray"},qa={offset:"strokeDashoffset",array:"strokeDasharray"};function Xn(t,e,o=1,r=0,n=!0){t.pathLength=1;let c=n?Ya:qa;t[c.offset]=b.transform(-r);let a=b.transform(e),l=b.transform(o);t[c.array]=`${a} ${l}`}function ie(t,{attrX:e,attrY:o,originX:r,originY:n,pathLength:c,pathSpacing:a=1,pathOffset:l=0,...u},m,f,h){if(re(t,u,m,h),f){t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox);return}t.attrs=t.style,t.style={};let{attrs:d,style:g,dimensions:y}=t;d.transform&&(y&&(g.transform=d.transform),delete d.transform),y&&(r!==void 0||n!==void 0||g.transform)&&(g.transformOrigin=Zn(y,r!==void 0?r:.5,n!==void 0?n:.5)),e!==void 0&&(d.x=e),o!==void 0&&(d.y=o),c!==void 0&&Xn(d,c,a,l,!1)}i();s();var ho=()=>({...ne(),attrs:{}});i();s();var se=t=>typeof t=="string"&&t.toLowerCase()==="svg";function Qn(t,e,o,r){let n=(0,Jn.useMemo)(()=>{let c=ho();return ie(c,e,{enableHardwareAcceleration:!1},se(r),t.transformTemplate),{...c.attrs,style:{...c.style}}},[e]);if(t.style){let c={};Pr(c,t.style,t),n.style={...c,...n.style}}return n}function ti(t=!1){return(o,r,n,c,{latestValues:a},l)=>{let m=(te(o)?Qn:Kn)(r,a,l,o),h={...Yn(r,typeof o=="string",t),...m,ref:c},{children:d}=r,g=(0,go.useMemo)(()=>L(d)?d.get():d,[d]);return n&&(h["data-projection-id"]=n),(0,go.createElement)(o,{...h,children:g})}}i();s();i();s();i();s();var ae=t=>t.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase();i();s();function yo(t,{style:e,vars:o},r,n){Object.assign(t.style,e,n&&n.getProjectionStyles(r));for(let c in o)t.style.setProperty(c,o[c])}i();s();var xo=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function vo(t,e,o,r){yo(t,e,void 0,r);for(let n in e.attrs)t.setAttribute(xo.has(n)?n:ae(n),e.attrs[n])}i();s();i();s();function ce(t,e){let{style:o}=t,r={};for(let n in o)(L(o[n])||e.style&&L(e.style[n])||lo(n,t))&&(r[n]=o[n]);return r}function Po(t,e){let o=ce(t,e);for(let r in t)if(L(t[r])||L(e[r])){let n=r==="x"||r==="y"?"attr"+r.toUpperCase():r;o[n]=t[r]}return o}i();s();var br=M(D(),1);i();s();function le(t,e,o,r={},n={}){return typeof e=="function"&&(e=e(o!==void 0?o:t.custom,r,n)),typeof e=="string"&&(e=t.variants&&t.variants[e]),typeof e=="function"&&(e=e(o!==void 0?o:t.custom,r,n)),e}i();s();i();s();i();s();var Gt=t=>Array.isArray(t);var ei=t=>!!(t&&typeof t=="object"&&t.mix&&t.toValue),oi=t=>Gt(t)?t[t.length-1]||0:t;function ue(t){let e=L(t)?t.get():t;return ei(e)?e.toValue():e}function Za({scrapeMotionValuesFromProps:t,createRenderState:e,onMount:o},r,n,c){let a={latestValues:Xa(r,n,c,t),renderState:e()};return o&&(a.mount=l=>o(r,l,a)),a}var bo=t=>(e,o)=>{let r=(0,br.useContext)(St),n=(0,br.useContext)(Ct),c=()=>Za(t,e,r,n);return o?c():ct(c)};function Xa(t,e,o,r){let n={},c=r(t,{});for(let d in c)n[d]=ue(c[d]);let{initial:a,animate:l}=t,u=jt(t),m=so(t);e&&m&&!u&&t.inherit!==!1&&(a===void 0&&(a=e.initial),l===void 0&&(l=e.animate));let f=o?o.initial===!1:!1;f=f||a===!1;let h=f?l:a;return h&&typeof h!="boolean"&&!wt(h)&&(Array.isArray(h)?h:[h]).forEach(g=>{let y=le(t,g);if(!y)return;let{transitionEnd:v,transition:P,...C}=y;for(let T in C){let x=C[T];if(Array.isArray(x)){let V=f?x.length-1:0;x=x[V]}x!==null&&(n[T]=x)}for(let T in v)n[T]=v[T]}),n}var ri={useVisualState:bo({scrapeMotionValuesFromProps:Po,createRenderState:ho,onMount:(t,e,{renderState:o,latestValues:r})=>{try{o.dimensions=typeof e.getBBox=="function"?e.getBBox():e.getBoundingClientRect()}catch{o.dimensions={x:0,y:0,width:0,height:0}}ie(o,r,{enableHardwareAcceleration:!1},se(e.tagName),t.transformTemplate),vo(e,o)}})};i();s();var ni={useVisualState:bo({scrapeMotionValuesFromProps:ce,createRenderState:ne})};function ii(t,{forwardMotionProps:e=!1},o,r){return{...te(t)?ri:ni,preloadedFeatures:o,useRender:ti(e),createVisualElement:r,Component:t}}i();s();i();s();i();s();i();s();function q(t,e,o,r={passive:!0}){return t.addEventListener(e,o,r),()=>t.removeEventListener(e,o)}i();s();i();s();var Vo=t=>t.pointerType==="mouse"?typeof t.button!="number"||t.button<=0:t.isPrimary!==!1;function $t(t,e="page"){return{point:{x:t[e+"X"],y:t[e+"Y"]}}}var si=t=>e=>Vo(e)&&t(e,$t(e));function Z(t,e,o,r){return q(t,e,si(o),r)}i();s();var Ja=(t,e)=>o=>e(t(o)),X=(...t)=>t.reduce(Ja);i();s();function li(t){let e=null;return()=>{let o=()=>{e=null};return e===null?(e=t,o):!1}}var ai=li("dragHorizontal"),ci=li("dragVertical");function Vr(t){let e=!1;if(t==="y")e=ci();else if(t==="x")e=ai();else{let o=ai(),r=ci();o&&r?e=()=>{o(),r()}:(o&&o(),r&&r())}return e}function To(){let t=Vr(!0);return t?(t(),!1):!0}i();s();var G=class{constructor(e){this.isMounted=!1,this.node=e}update(){}};i();s();i();s();function ui(t){let e=[],o=[],r=0,n=!1,c=!1,a=new WeakSet,l={schedule:(u,m=!1,f=!1)=>{let h=f&&n,d=h?e:o;return m&&a.add(u),d.indexOf(u)===-1&&(d.push(u),h&&n&&(r=e.length)),u},cancel:u=>{let m=o.indexOf(u);m!==-1&&o.splice(m,1),a.delete(u)},process:u=>{if(n){c=!0;return}if(n=!0,[e,o]=[o,e],o.length=0,r=e.length,r)for(let m=0;m<r;m++){let f=e[m];f(u),a.has(f)&&(l.schedule(f),t())}n=!1,c&&(c=!1,l.process(u))}};return l}i();s();var O={delta:0,timestamp:0,isProcessing:!1};var Qa=40,Tr=!0,Ge=!1,$e=["read","update","preRender","render","postRender"],So=$e.reduce((t,e)=>(t[e]=ui(()=>Ge=!0),t),{}),S=$e.reduce((t,e)=>{let o=So[e];return t[e]=(r,n=!1,c=!1)=>(Ge||ec(),o.schedule(r,n,c)),t},{}),W=$e.reduce((t,e)=>(t[e]=So[e].cancel,t),{}),Co=$e.reduce((t,e)=>(t[e]=()=>So[e].process(O),t),{}),tc=t=>So[t].process(O),mi=t=>{Ge=!1,O.delta=Tr?1e3/60:Math.max(Math.min(t-O.timestamp,Qa),1),O.timestamp=t,O.isProcessing=!0,$e.forEach(tc),O.isProcessing=!1,Ge&&(Tr=!1,requestAnimationFrame(mi))},ec=()=>{Ge=!0,Tr=!0,O.isProcessing||requestAnimationFrame(mi)};function fi(t,e){let o="pointer"+(e?"enter":"leave"),r="onHover"+(e?"Start":"End"),n=(c,a)=>{if(c.type==="touch"||To())return;let l=t.getProps();t.animationState&&l.whileHover&&t.animationState.setActive("whileHover",e),l[r]&&S.update(()=>l[r](c,a))};return Z(t.current,o,n,{passive:!t.getProps()[r]})}var Ao=class extends G{mount(){this.unmount=X(fi(this.node,!0),fi(this.node,!1))}unmount(){}};i();s();var wo=class extends G{constructor(){super(...arguments),this.isActive=!1}onFocus(){let e=!1;try{e=this.node.current.matches(":focus-visible")}catch{e=!0}!e||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=X(q(this.node.current,"focus",()=>this.onFocus()),q(this.node.current,"blur",()=>this.onBlur()))}unmount(){}};i();s();i();s();var Sr=(t,e)=>e?t===e?!0:Sr(t,e.parentElement):!1;i();s();var F=t=>t;function Cr(t,e){if(!e)return;let o=new PointerEvent("pointer"+t);e(o,$t(o))}var Mo=class extends G{constructor(){super(...arguments),this.removeStartListeners=F,this.removeEndListeners=F,this.removeAccessibleListeners=F,this.startPointerPress=(e,o)=>{if(this.removeEndListeners(),this.isPressing)return;let r=this.node.getProps(),c=Z(self,"pointerup",(l,u)=>{if(!this.checkPressEnd())return;let{onTap:m,onTapCancel:f}=this.node.getProps();S.update(()=>{Sr(this.node.current,l.target)?m&&m(l,u):f&&f(l,u)})},{passive:!(r.onTap||r.onPointerUp)}),a=Z(self,"pointercancel",(l,u)=>this.cancelPress(l,u),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=X(c,a),this.startPress(e,o)},this.startAccessiblePress=()=>{let e=c=>{if(c.key!=="Enter"||this.isPressing)return;let a=l=>{l.key!=="Enter"||!this.checkPressEnd()||Cr("up",(u,m)=>{let{onTap:f}=this.node.getProps();f&&S.update(()=>f(u,m))})};this.removeEndListeners(),this.removeEndListeners=q(this.node.current,"keyup",a),Cr("down",(l,u)=>{this.startPress(l,u)})},o=q(this.node.current,"keydown",e),r=()=>{this.isPressing&&Cr("cancel",(c,a)=>this.cancelPress(c,a))},n=q(this.node.current,"blur",r);this.removeAccessibleListeners=X(o,n)}}startPress(e,o){this.isPressing=!0;let{onTapStart:r,whileTap:n}=this.node.getProps();n&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&S.update(()=>r(e,o))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!To()}cancelPress(e,o){if(!this.checkPressEnd())return;let{onTapCancel:r}=this.node.getProps();r&&S.update(()=>r(e,o))}mount(){let e=this.node.getProps(),o=Z(this.node.current,"pointerdown",this.startPointerPress,{passive:!(e.onTapStart||e.onPointerStart)}),r=q(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=X(o,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}};i();s();i();s();var wr=new WeakMap,Ar=new WeakMap,oc=t=>{let e=wr.get(t.target);e&&e(t)},rc=t=>{t.forEach(oc)};function nc({root:t,...e}){let o=t||document;Ar.has(o)||Ar.set(o,{});let r=Ar.get(o),n=JSON.stringify(e);return r[n]||(r[n]=new IntersectionObserver(rc,{root:t,...e})),r[n]}function pi(t,e,o){let r=nc(e);return wr.set(t,o),r.observe(t),()=>{wr.delete(t),r.unobserve(t)}}var ic={some:0,all:1},Do=class extends G{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();let{viewport:e={}}=this.node.getProps(),{root:o,margin:r,amount:n="some",once:c}=e,a={root:o?o.current:void 0,rootMargin:r,threshold:typeof n=="number"?n:ic[n]},l=u=>{let{isIntersecting:m}=u;if(this.isInView===m||(this.isInView=m,c&&!m&&this.hasEnteredView))return;m&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",m);let{onViewportEnter:f,onViewportLeave:h}=this.node.getProps(),d=m?f:h;d&&d(u)};return pi(this.node.current,a,l)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;let{props:e,prevProps:o}=this.node;["amount","margin","root"].some(sc(e,o))&&this.startObserver()}unmount(){}};function sc({viewport:t={}},{viewport:e={}}={}){return o=>t[o]!==e[o]}var hi={inView:{Feature:Do},tap:{Feature:Mo},focus:{Feature:wo},hover:{Feature:Ao}};i();s();i();s();i();s();i();s();function Mr(t,e){if(!Array.isArray(e))return!1;let o=e.length;if(o!==t.length)return!1;for(let r=0;r<o;r++)if(e[r]!==t[r])return!1;return!0}i();s();function ac(t){let e={};return t.values.forEach((o,r)=>e[r]=o.get()),e}function cc(t){let e={};return t.values.forEach((o,r)=>e[r]=o.getVelocity()),e}function Lt(t,e,o){let r=t.getProps();return le(r,e,o!==void 0?o:r.custom,ac(t),cc(t))}i();s();i();s();i();s();var lc="framerAppearId",di="data-"+ae(lc);i();s();i();s();var vt=F,B=F;p.NODE_ENV!=="production"&&(vt=(t,e)=>{!t&&typeof console<"u"&&console.warn(e)},B=(t,e)=>{if(!t)throw new Error(e)});i();s();var tt=t=>t*1e3,at=t=>t/1e3;i();s();var gi={current:!1};i();s();i();s();i();s();function yi(t){return!t||Array.isArray(t)||typeof t=="string"&&xi[t]}var ze=([t,e,o,r])=>`cubic-bezier(${t}, ${e}, ${o}, ${r})`,xi={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:ze([0,.65,.55,1]),circOut:ze([.55,0,1,.45]),backIn:ze([.31,.01,.66,-.59]),backOut:ze([.33,1.53,.69,.99])};function vi(t){if(t)return Array.isArray(t)?ze(t):xi[t]}function Pi(t,e,o,{delay:r=0,duration:n,repeat:c=0,repeatType:a="loop",ease:l,times:u}={}){let m={[e]:o};return u&&(m.offset=u),t.animate(m,{delay:r,duration:n,easing:vi(l),fill:"both",iterations:c+1,direction:a==="reverse"?"alternate":"normal"})}i();s();var bi={waapi:()=>Object.hasOwnProperty.call(Element.prototype,"animate")},Dr={},Rr={};for(let t in bi)Rr[t]=()=>(Dr[t]===void 0&&(Dr[t]=bi[t]()),Dr[t]);i();s();function Vi(t,{repeat:e,repeatType:o="loop"}){let r=e&&o!=="loop"&&e%2===1?0:t.length-1;return t[r]}i();s();i();s();i();s();i();s();var me=t=>e=>e<=.5?t(2*e)/2:(2-t(2*(1-e)))/2;i();s();var fe=t=>e=>1-t(1-e);var Ro=t=>t*t,Ti=fe(Ro),Eo=me(Ro);i();s();i();s();i();s();i();s();i();s();var pe=(t,e)=>o=>!!(xt(o)&&$n.test(o)&&o.startsWith(t)||e&&Object.prototype.hasOwnProperty.call(o,e)),Lo=(t,e,o)=>r=>{if(!xt(r))return r;let[n,c,a,l]=r.match(Et);return{[t]:parseFloat(n),[e]:parseFloat(c),[o]:parseFloat(a),alpha:l!==void 0?parseFloat(l):1}};var uc=t=>Q(0,255,t),Er={...rt,transform:t=>Math.round(uc(t))},ut={test:pe("rgb","red"),parse:Lo("red","green","blue"),transform:({red:t,green:e,blue:o,alpha:r=1})=>"rgba("+Er.transform(t)+", "+Er.transform(e)+", "+Er.transform(o)+", "+Rt(Dt.transform(r))+")"};function mc(t){let e="",o="",r="",n="";return t.length>5?(e=t.substring(1,3),o=t.substring(3,5),r=t.substring(5,7),n=t.substring(7,9)):(e=t.substring(1,2),o=t.substring(2,3),r=t.substring(3,4),n=t.substring(4,5),e+=e,o+=o,r+=r,n+=n),{red:parseInt(e,16),green:parseInt(o,16),blue:parseInt(r,16),alpha:n?parseInt(n,16)/255:1}}var He={test:pe("#"),parse:mc,transform:ut.transform};i();s();var Bt={test:pe("hsl","hue"),parse:Lo("hue","saturation","lightness"),transform:({hue:t,saturation:e,lightness:o,alpha:r=1})=>"hsla("+Math.round(t)+", "+H.transform(Rt(e))+", "+H.transform(Rt(o))+", "+Rt(Dt.transform(r))+")"};var j={test:t=>ut.test(t)||He.test(t)||Bt.test(t),parse:t=>ut.test(t)?ut.parse(t):Bt.test(t)?Bt.parse(t):He.parse(t),transform:t=>xt(t)?t:t.hasOwnProperty("red")?ut.transform(t):Bt.transform(t)};i();s();var w=(t,e,o)=>-o*t+o*e+t;i();s();i();s();function Lr(t,e,o){return o<0&&(o+=1),o>1&&(o-=1),o<1/6?t+(e-t)*6*o:o<1/2?e:o<2/3?t+(e-t)*(2/3-o)*6:t}function Si({hue:t,saturation:e,lightness:o,alpha:r}){t/=360,e/=100,o/=100;let n=0,c=0,a=0;if(!e)n=c=a=o;else{let l=o<.5?o*(1+e):o+e-o*e,u=2*o-l;n=Lr(u,l,t+1/3),c=Lr(u,l,t),a=Lr(u,l,t-1/3)}return{red:Math.round(n*255),green:Math.round(c*255),blue:Math.round(a*255),alpha:r}}var Br=(t,e,o)=>{let r=t*t;return Math.sqrt(Math.max(0,o*(e*e-r)+r))},fc=[He,ut,Bt],pc=t=>fc.find(e=>e.test(t));function Ci(t){let e=pc(t);B(!!e,`'${t}' is not an animatable color. Use the equivalent color code instead.`);let o=e.parse(t);return e===Bt&&(o=Si(o)),o}var Bo=(t,e)=>{let o=Ci(t),r=Ci(e),n={...o};return c=>(n.red=Br(o.red,r.red,c),n.green=Br(o.green,r.green,c),n.blue=Br(o.blue,r.blue,c),n.alpha=w(o.alpha,r.alpha,c),ut.transform(n))};i();s();i();s();var Ai="${c}",wi="${n}";function hc(t){var e,o;return isNaN(t)&&xt(t)&&(((e=t.match(Et))===null||e===void 0?void 0:e.length)||0)+(((o=t.match(fo))===null||o===void 0?void 0:o.length)||0)>0}function We(t){typeof t=="number"&&(t=`${t}`);let e=[],o=0,r=0,n=t.match(fo);n&&(o=n.length,t=t.replace(fo,Ai),e.push(...n.map(j.parse)));let c=t.match(Et);return c&&(r=c.length,t=t.replace(Et,wi),e.push(...c.map(rt.parse))),{values:e,numColors:o,numNumbers:r,tokenised:t}}function Mi(t){return We(t).values}function Di(t){let{values:e,numColors:o,tokenised:r}=We(t),n=e.length;return c=>{let a=r;for(let l=0;l<n;l++)a=a.replace(l<o?Ai:wi,l<o?j.transform(c[l]):Rt(c[l]));return a}}var dc=t=>typeof t=="number"?0:t;function gc(t){let e=Mi(t);return Di(t)(e.map(dc))}var K={test:hc,parse:Mi,createTransformer:Di,getAnimatableNone:gc};function Ri(t,e){return typeof t=="number"?o=>w(t,e,o):j.test(t)?Bo(t,e):Fr(t,e)}var kr=(t,e)=>{let o=[...t],r=o.length,n=t.map((c,a)=>Ri(c,e[a]));return c=>{for(let a=0;a<r;a++)o[a]=n[a](c);return o}},Ei=(t,e)=>{let o={...t,...e},r={};for(let n in o)t[n]!==void 0&&e[n]!==void 0&&(r[n]=Ri(t[n],e[n]));return n=>{for(let c in r)o[c]=r[c](n);return o}},Fr=(t,e)=>{let o=K.createTransformer(e),r=We(t),n=We(e);return r.numColors===n.numColors&&r.numNumbers>=n.numNumbers?X(kr(r.values,n.values),o):(vt(!0,`Complex values '${t}' and '${e}' too different to mix. Ensure all colors are of the same type, and that each contains the same quantity of number and color values. Falling back to instant transition.`),a=>`${a>0?e:t}`)};i();s();var Pt=(t,e,o)=>{let r=e-t;return r===0?1:(o-t)/r};var Li=(t,e)=>o=>w(t,e,o);function yc(t){return typeof t=="number"?Li:typeof t=="string"?j.test(t)?Bo:Fr:Array.isArray(t)?kr:typeof t=="object"?Ei:Li}function xc(t,e,o){let r=[],n=o||yc(t[0]),c=t.length-1;for(let a=0;a<c;a++){let l=n(t[a],t[a+1]);if(e){let u=Array.isArray(e)?e[a]:e;l=X(u,l)}r.push(l)}return r}function he(t,e,{clamp:o=!0,ease:r,mixer:n}={}){let c=t.length;if(B(c===e.length,"Both input and output ranges must be the same length"),B(!r||!Array.isArray(r)||r.length===c-1,"Array of easing functions must be of length `input.length - 1`, as it applies to the transitions **between** the defined values."),c===1)return()=>e[0];t[0]>t[c-1]&&(t=[...t].reverse(),e=[...e].reverse());let a=xc(e,r,n),l=a.length,u=m=>{let f=0;if(l>1)for(;f<t.length-2&&!(m<t[f+1]);f++);let h=Pt(t[f],t[f+1],m);return a[f](h)};return o?m=>u(Q(t[0],t[c-1],m)):u}i();s();i();s();function Bi(t,e){let o=t[t.length-1];for(let r=1;r<=e;r++){let n=Pt(0,e,r);t.push(w(o,1,n))}}function ki(t){let e=[0];return Bi(e,t.length-1),e}i();s();function Fi(t,e){return t.map(o=>o*e)}i();s();i();s();var Ii=(t,e,o)=>(((1-3*o+3*e)*t+(3*o-6*e))*t+3*e)*t,vc=1e-7,Pc=12;function bc(t,e,o,r,n){let c,a,l=0;do a=e+(o-e)/2,c=Ii(a,r,n)-t,c>0?o=a:e=a;while(Math.abs(c)>vc&&++l<Pc);return a}function ko(t,e,o,r){if(t===e&&o===r)return F;let n=c=>bc(c,0,1,t,o);return c=>c===0||c===1?c:Ii(n(c),e,r)}i();s();var Ir=t=>1-Math.sin(Math.acos(t)),Ke=fe(Ir),Oi=me(Ke);i();s();var Or=ko(.33,1.53,.69,.99),_e=fe(Or),ji=me(_e);i();s();var Ni=t=>(t*=2)<1?.5*_e(t):.5*(2-Math.pow(2,-10*(t-1)));var Ui={linear:F,easeIn:Ro,easeInOut:Eo,easeOut:Ti,circIn:Ir,circInOut:Oi,circOut:Ke,backIn:_e,backInOut:ji,backOut:Or,anticipate:Ni},jr=t=>{if(Array.isArray(t)){B(t.length===4,"Cubic bezier arrays must contain four numerical values.");let[e,o,r,n]=t;return ko(e,o,r,n)}else if(typeof t=="string")return B(Ui[t]!==void 0,`Invalid easing type '${t}'`),Ui[t];return t},Gi=t=>Array.isArray(t)&&typeof t[0]!="number";function Vc(t,e){return t.map(()=>e||Eo).splice(0,t.length-1)}function Ye({duration:t=300,keyframes:e,times:o,ease:r="easeInOut"}){let n=Gi(r)?r.map(jr):jr(r),c={done:!1,value:e[0]},a=Fi(o&&o.length===e.length?o:ki(e),t),l=he(a,e,{ease:Array.isArray(n)?n:Vc(e,n)});return{calculatedDuration:t,next:u=>(c.value=l(u),c.done=u>=t,c)}}i();s();i();s();i();s();function Fo(t,e){return e?t*(1e3/e):0}var Tc=5;function Io(t,e,o){let r=Math.max(e-Tc,0);return Fo(o-t(r),e-r)}i();s();var Nr=.001,Sc=.01,$i=10,Cc=.05,Ac=1;function zi({duration:t=800,bounce:e=.25,velocity:o=0,mass:r=1}){let n,c;vt(t<=tt($i),"Spring duration must be 10 seconds or less");let a=1-e;a=Q(Cc,Ac,a),t=Q(Sc,$i,at(t)),a<1?(n=m=>{let f=m*a,h=f*t,d=f-o,g=Oo(m,a),y=Math.exp(-h);return Nr-d/g*y},c=m=>{let h=m*a*t,d=h*o+o,g=Math.pow(a,2)*Math.pow(m,2)*t,y=Math.exp(-h),v=Oo(Math.pow(m,2),a);return(-n(m)+Nr>0?-1:1)*((d-g)*y)/v}):(n=m=>{let f=Math.exp(-m*t),h=(m-o)*t+1;return-Nr+f*h},c=m=>{let f=Math.exp(-m*t),h=(o-m)*(t*t);return f*h});let l=5/t,u=Mc(n,c,l);if(t=tt(t),isNaN(u))return{stiffness:100,damping:10,duration:t};{let m=Math.pow(u,2)*r;return{stiffness:m,damping:a*2*Math.sqrt(r*m),duration:t}}}var wc=12;function Mc(t,e,o){let r=o;for(let n=1;n<wc;n++)r=r-t(r)/e(r);return r}function Oo(t,e){return t*Math.sqrt(1-e*e)}var Dc=["duration","bounce"],Rc=["stiffness","damping","mass"];function Hi(t,e){return e.some(o=>t[o]!==void 0)}function Ec(t){let e={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...t};if(!Hi(t,Rc)&&Hi(t,Dc)){let o=zi(t);e={...e,...o,velocity:0,mass:1},e.isResolvedFromDuration=!0}return e}function jo({keyframes:t,restDelta:e,restSpeed:o,...r}){let n=t[0],c=t[t.length-1],a={done:!1,value:n},{stiffness:l,damping:u,mass:m,velocity:f,duration:h,isResolvedFromDuration:d}=Ec(r),g=f?-at(f):0,y=u/(2*Math.sqrt(l*m)),v=c-n,P=at(Math.sqrt(l/m)),C=Math.abs(v)<5;o||(o=C?.01:2),e||(e=C?.005:.5);let T;if(y<1){let x=Oo(P,y);T=V=>{let A=Math.exp(-y*P*V);return c-A*((g+y*P*v)/x*Math.sin(x*V)+v*Math.cos(x*V))}}else if(y===1)T=x=>c-Math.exp(-P*x)*(v+(g+P*v)*x);else{let x=P*Math.sqrt(y*y-1);T=V=>{let A=Math.exp(-y*P*V),E=Math.min(x*V,300);return c-A*((g+y*P*v)*Math.sinh(E)+x*v*Math.cosh(E))/x}}return{calculatedDuration:d&&h||null,next:x=>{let V=T(x);if(d)a.done=x>=h;else{let A=g;x!==0&&(y<1?A=Io(T,x,V):A=0);let E=Math.abs(A)<=o,N=Math.abs(c-V)<=e;a.done=E&&N}return a.value=a.done?c:V,a}}}i();s();function Ur({keyframes:t,velocity:e=0,power:o=.8,timeConstant:r=325,bounceDamping:n=10,bounceStiffness:c=500,modifyTarget:a,min:l,max:u,restDelta:m=.5,restSpeed:f}){let h=t[0],d={done:!1,value:h},g=R=>l!==void 0&&R<l||u!==void 0&&R>u,y=R=>l===void 0?u:u===void 0||Math.abs(l-R)<Math.abs(u-R)?l:u,v=o*e,P=h+v,C=a===void 0?P:a(P);C!==P&&(v=C-h);let T=R=>-v*Math.exp(-R/r),x=R=>C+T(R),V=R=>{let _=T(R),pt=x(R);d.done=Math.abs(_)<=m,d.value=d.done?C:pt},A,E,N=R=>{g(d.value)&&(A=R,E=jo({keyframes:[d.value,y(d.value)],velocity:Io(x,R,d.value),damping:n,stiffness:c,restDelta:m,restSpeed:f}))};return N(0),{calculatedDuration:null,next:R=>{let _=!1;return!E&&A===void 0&&(_=!0,V(R),N(R)),A!==void 0&&R>A?E.next(R-A):(!_&&V(R),d)}}}i();s();var Wi=t=>{let e=({timestamp:o})=>t(o);return{start:()=>S.update(e,!0),stop:()=>W.update(e),now:()=>O.isProcessing?O.timestamp:performance.now()}};var Lc={decay:Ur,inertia:Ur,tween:Ye,keyframes:Ye,spring:jo},Bc=2e4;function kc(t){let e=0,o=50,r=t.next(e);for(;!r.done&&e<Bc;)e+=o,r=t.next(e);return e}function zt({autoplay:t=!0,delay:e=0,driver:o=Wi,keyframes:r,type:n="keyframes",repeat:c=0,repeatDelay:a=0,repeatType:l="loop",onPlay:u,onStop:m,onComplete:f,onUpdate:h,...d}){let g,y,v=()=>{y=new Promise(I=>{g=I})};v();let P,C=Lc[n]||Ye,T;C!==Ye&&typeof r[0]!="number"&&(T=he([0,100],r,{clamp:!1}),r=[0,100]);let x=C({...d,keyframes:r}),V;l==="mirror"&&(V=C({...d,keyframes:[...r].reverse(),velocity:-(d.velocity||0)}));let A="idle",E=null,N=null;x.calculatedDuration===null&&c&&(x.calculatedDuration=kc(x));let{calculatedDuration:R}=x,_=1/0,pt=1/0;R&&(_=R+a,pt=_*(c+1)-a);let U=0,Ie=I=>{if(N===null)return;E!==null?U=E:U=I-N,U=Math.max(U-e,0),A==="finished"&&E===null&&(U=pt);let Tt=U,mn=x;if(c){let gr=U/_,dn=Math.floor(gr),Ot=gr%1;!Ot&&gr>=1&&(Ot=1),Ot===1&&dn--;let gn=dn%2;gn&&(l==="reverse"?(Ot=1-Ot,a&&(Ot-=a/_)):l==="mirror"&&(mn=V)),Tt=(U>=pt?l==="reverse"&&gn?0:1:Q(0,1,Ot))*_}let fn=mn.next(Tt),{value:pn,done:hn}=fn;return h&&h(T?T(pn):pn),R!==null&&(hn=U>=pt),E===null&&(A==="finished"||A==="running"&&hn)&&dr(),fn},dr=()=>{P&&P.stop(),A="finished",f&&f(),g(),v()},Zt=()=>{P||(P=o(Ie));let I=P.now();u&&u(),A="running",E!==null?N=I-E:N||(N=I),E=null,P.start()};return t&&Zt(),{then(I,Tt){return y.then(I,Tt)},get time(){return at(U)},set time(I){I=tt(I),U=I,E!==null||!P?E=I:N=P.now()-I},get state(){return A},play:Zt,pause:()=>{A="paused",E=U},stop:()=>{A!=="idle"&&(A="idle",m&&m(),P&&P.stop(),P=void 0)},sample:I=>(N=0,Ie(I))}}var Fc=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),No=10,Ic=2e4,Oc=(t,e)=>e.type==="spring"||t==="backgroundColor"||!yi(e.ease);function Ki(t,e,{onUpdate:o,onComplete:r,...n}){if(!(Rr.waapi()&&Fc.has(e)&&!n.repeatDelay&&n.repeatType!=="mirror"&&n.damping!==0&&n.type!=="inertia"))return!1;let a,l,u=()=>{l=new Promise(g=>{a=g})};u();let{keyframes:m,duration:f=300,ease:h}=n;if(Oc(e,n)){let g=zt({...n,repeat:0,delay:0}),y={done:!1,value:m[0]},v=[],P=0;for(;!y.done&&P<Ic;)y=g.sample(P),v.push(y.value),P+=No;m=v,f=P-No,h="linear"}let d=Pi(t.owner.current,e,m,{...n,duration:f,ease:h});return d.onfinish=()=>{t.set(Vi(m,n)),S.update(()=>d.cancel()),r&&r(),a(),u()},{then(g,y){return l.then(g,y)},get time(){return at(d.currentTime||0)},set time(g){d.currentTime=tt(g)},play:()=>d.play(),pause:()=>d.pause(),stop:()=>{if(d.playState==="idle")return;let{currentTime:g}=d;if(g){let y=zt({...n,autoplay:!1});t.setWithVelocity(y.sample(g-No).value,y.sample(g).value,No)}S.update(()=>d.cancel())}}}i();s();function _i({keyframes:t,delay:e,onUpdate:o,onComplete:r}){let n=()=>(o&&o(t[t.length-1]),r&&r(),{time:0,play:F,pause:F,stop:F,then:c=>(c(),Promise.resolve())});return e?zt({keyframes:[0,1],duration:e,onComplete:n}):n()}i();s();var jc={type:"spring",stiffness:500,damping:25,restSpeed:10},Nc=t=>({type:"spring",stiffness:550,damping:t===0?2*Math.sqrt(550):30,restSpeed:10}),Uc={type:"keyframes",duration:.8},Gc={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},Yi=(t,{keyframes:e})=>e.length>2?Uc:Y.has(t)?t.startsWith("scale")?Nc(e[1]):jc:Gc;i();s();var qe=(t,e)=>t==="zIndex"?!1:!!(typeof e=="number"||Array.isArray(e)||typeof e=="string"&&K.test(e)&&!e.startsWith("url("));i();s();i();s();i();s();var $c=new Set(["brightness","contrast","saturate","opacity"]);function zc(t){let[e,o]=t.slice(0,-1).split("(");if(e==="drop-shadow")return t;let[r]=o.match(Et)||[];if(!r)return t;let n=o.replace(r,""),c=$c.has(e)?1:0;return r!==o&&(c*=100),e+"("+c+n+")"}var Hc=/([a-z-]*)\(.*?\)/g,Ze={...K,getAnimatableNone:t=>{let e=t.match(Hc);return e?e.map(zc).join(" "):t}};i();s();var Wc={...po,color:j,backgroundColor:j,outlineColor:j,fill:j,stroke:j,borderColor:j,borderTopColor:j,borderRightColor:j,borderBottomColor:j,borderLeftColor:j,filter:Ze,WebkitFilter:Ze},de=t=>Wc[t];function ge(t,e){let o=de(t);return o!==Ze&&(o=K),o.getAnimatableNone?o.getAnimatableNone(e):void 0}i();s();function qi({when:t,delay:e,delayChildren:o,staggerChildren:r,staggerDirection:n,repeat:c,repeatType:a,repeatDelay:l,from:u,elapsed:m,...f}){return!!Object.keys(f).length}function Gr(t){return t===0||typeof t=="string"&&parseFloat(t)===0&&t.indexOf(" ")===-1}function $r(t){return typeof t=="number"?0:ge("",t)}function Uo(t,e){return t[e]||t.default||t}function Zi(t,e,o,r){let n=qe(e,o),c=r.from!==void 0?r.from:t.get();return c==="none"&&n&&typeof o=="string"?c=ge(e,o):Gr(c)&&typeof o=="string"?c=$r(o):!Array.isArray(o)&&Gr(o)&&typeof c=="string"&&(o=$r(c)),Array.isArray(o)?(o[0]===null&&(o[0]=c),o):[c,o]}var ye=(t,e,o,r={})=>n=>{let c=Uo(r,t)||{},a=c.delay||r.delay||0,{elapsed:l=0}=r;l=l-tt(a);let u=Zi(e,t,o,c),m=u[0],f=u[u.length-1],h=qe(t,m),d=qe(t,f);vt(h===d,`You are trying to animate ${t} from "${m}" to "${f}". ${m} is not an animatable value - to enable this animation set ${m} to a value animatable to ${f} via the \`style\` property.`);let g={keyframes:u,velocity:e.getVelocity(),...c,delay:-l,onUpdate:y=>{e.set(y),c.onUpdate&&c.onUpdate(y)},onComplete:()=>{n(),c.onComplete&&c.onComplete()}};if(!h||!d||gi.current||c.type===!1)return _i(g);if(qi(c)||(g={...g,...Yi(t,g)}),g.duration&&(g.duration=tt(g.duration)),g.repeatDelay&&(g.repeatDelay=tt(g.repeatDelay)),e.owner&&e.owner.current instanceof HTMLElement&&!e.owner.getProps().onUpdate){let y=Ki(e,t,g);if(y)return y}return zt(g)};i();s();function Ht(t){return!!(L(t)&&t.add)}i();s();i();s();var Xi=t=>/^\-?\d*\.?\d+$/.test(t);i();s();var Ji=t=>/^0[^.\s]+$/.test(t);i();s();i();s();i();s();function xe(t,e){t.indexOf(e)===-1&&t.push(e)}function ve(t,e){let o=t.indexOf(e);o>-1&&t.splice(o,1)}var kt=class{constructor(){this.subscriptions=[]}add(e){return xe(this.subscriptions,e),()=>ve(this.subscriptions,e)}notify(e,o,r){let n=this.subscriptions.length;if(n)if(n===1)this.subscriptions[0](e,o,r);else for(let c=0;c<n;c++){let a=this.subscriptions[c];a&&a(e,o,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}};i();s();var Qi=new Set;function Ft(t,e,o){t||Qi.has(e)||(console.warn(e),o&&console.warn(o),Qi.add(e))}var Kc=t=>!isNaN(parseFloat(t)),zr=class{constructor(e,o={}){this.version="10.5.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,n=!0)=>{this.prev=this.current,this.current=r;let{delta:c,timestamp:a}=O;this.lastUpdated!==a&&(this.timeDelta=c,this.lastUpdated=a,S.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),n&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>S.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=e,this.canTrackVelocity=Kc(this.current),this.owner=o.owner}onChange(e){return p.NODE_ENV!=="production"&&Ft(!1,'value.onChange(callback) is deprecated. Switch to value.on("change", callback).'),this.on("change",e)}on(e,o){this.events[e]||(this.events[e]=new kt);let r=this.events[e].add(o);return e==="change"?()=>{r(),S.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(let e in this.events)this.events[e].clear()}attach(e,o){this.passiveEffect=e,this.stopPassiveEffect=o}set(e,o=!0){!o||!this.passiveEffect?this.updateAndNotify(e,o):this.passiveEffect(e,this.updateAndNotify)}setWithVelocity(e,o,r){this.set(o),this.prev=e,this.timeDelta=r}jump(e){this.updateAndNotify(e),this.prev=e,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Fo(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(e){return this.stop(),new Promise(o=>{this.hasAnimated=!0,this.animation=e(o),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}};function nt(t,e){return new zr(t,e)}i();s();i();s();i();s();var Go=t=>e=>e.test(t);i();s();var ts={test:t=>t==="auto",parse:t=>t};var Hr=[rt,b,H,lt,Hn,zn,ts],Pe=t=>Hr.find(Go(t));var _c=[...Hr,j,K],es=t=>_c.find(Go(t));function Yc(t,e,o){t.hasValue(e)?t.getValue(e).set(o):t.addValue(e,nt(o))}function os(t,e){let o=Lt(t,e),{transitionEnd:r={},transition:n={},...c}=o?t.makeTargetAnimatable(o,!1):{};c={...c,...r};for(let a in c){let l=oi(c[a]);Yc(t,a,l)}}function rs(t,e,o){var r,n;let c=Object.keys(e).filter(l=>!t.hasValue(l)),a=c.length;if(a)for(let l=0;l<a;l++){let u=c[l],m=e[u],f=null;Array.isArray(m)&&(f=m[0]),f===null&&(f=(n=(r=o[u])!==null&&r!==void 0?r:t.readValue(u))!==null&&n!==void 0?n:e[u]),f!=null&&(typeof f=="string"&&(Xi(f)||Ji(f))?f=parseFloat(f):!es(f)&&K.test(m)&&(f=ge(u,m)),t.addValue(u,nt(f,{owner:t})),o[u]===void 0&&(o[u]=f),f!==null&&t.setBaseTarget(u,f))}}function qc(t,e){return e?(e[t]||e.default||e).from:void 0}function ns(t,e,o){let r={};for(let n in t){let c=qc(n,e);if(c!==void 0)r[n]=c;else{let a=o.getValue(n);a&&(r[n]=a.get())}}return r}function Zc({protectedKeys:t,needsAnimating:e},o){let r=t.hasOwnProperty(o)&&e[o]!==!0;return e[o]=!1,r}function be(t,e,{delay:o=0,transitionOverride:r,type:n}={}){let{transition:c=t.getDefaultTransition(),transitionEnd:a,...l}=t.makeTargetAnimatable(e),u=t.getValue("willChange");r&&(c=r);let m=[],f=n&&t.animationState&&t.animationState.getState()[n];for(let h in l){let d=t.getValue(h),g=l[h];if(!d||g===void 0||f&&Zc(f,h))continue;let y={delay:o,elapsed:0,...c};if(self.HandoffAppearAnimations&&!d.hasAnimated){let P=t.getProps()[di];P&&(y.elapsed=self.HandoffAppearAnimations(P,h,d,S))}d.start(ye(h,d,g,t.shouldReduceMotion&&Y.has(h)?{type:!1}:y));let v=d.animation;Ht(u)&&(u.add(h),v.then(()=>u.remove(h))),m.push(v)}return a&&Promise.all(m).then(()=>{a&&os(t,a)}),m}i();s();function $o(t,e,o={}){let r=Lt(t,e,o.custom),{transition:n=t.getDefaultTransition()||{}}=r||{};o.transitionOverride&&(n=o.transitionOverride);let c=r?()=>Promise.all(be(t,r,o)):()=>Promise.resolve(),a=t.variantChildren&&t.variantChildren.size?(u=0)=>{let{delayChildren:m=0,staggerChildren:f,staggerDirection:h}=n;return Xc(t,e,m+u,f,h,o)}:()=>Promise.resolve(),{when:l}=n;if(l){let[u,m]=l==="beforeChildren"?[c,a]:[a,c];return u().then(()=>m())}else return Promise.all([c(),a(o.delay)])}function Xc(t,e,o=0,r=0,n=1,c){let a=[],l=(t.variantChildren.size-1)*r,u=n===1?(m=0)=>m*r:(m=0)=>l-m*r;return Array.from(t.variantChildren).sort(Jc).forEach((m,f)=>{m.notify("AnimationStart",e),a.push($o(m,e,{...c,delay:o+u(f)}).then(()=>m.notify("AnimationComplete",e)))}),Promise.all(a)}function Jc(t,e){return t.sortNodePosition(e)}function is(t,e,o={}){t.notify("AnimationStart",e);let r;if(Array.isArray(e)){let n=e.map(c=>$o(t,c,o));r=Promise.all(n)}else if(typeof e=="string")r=$o(t,e,o);else{let n=typeof e=="function"?Lt(t,e,o.custom):e;r=Promise.all(be(t,n,o))}return r.then(()=>t.notify("AnimationComplete",e))}var Qc=[...io].reverse(),tl=io.length;function el(t){return e=>Promise.all(e.map(({animation:o,options:r})=>is(t,o,r)))}function ss(t){let e=el(t),o=rl(),r=!0,n=(u,m)=>{let f=Lt(t,m);if(f){let{transition:h,transitionEnd:d,...g}=f;u={...u,...g,...d}}return u};function c(u){e=u(t)}function a(u,m){let f=t.getProps(),h=t.getVariantContext(!0)||{},d=[],g=new Set,y={},v=1/0;for(let C=0;C<tl;C++){let T=Qc[C],x=o[T],V=f[T]!==void 0?f[T]:h[T],A=yt(V),E=T===m?x.isActive:null;E===!1&&(v=C);let N=V===h[T]&&V!==f[T]&&A;if(N&&r&&t.manuallyAnimateOnMount&&(N=!1),x.protectedKeys={...y},!x.isActive&&E===null||!V&&!x.prevProp||wt(V)||typeof V=="boolean")continue;let R=ol(x.prevProp,V),_=R||T===m&&x.isActive&&!N&&A||C>v&&A,pt=Array.isArray(V)?V:[V],U=pt.reduce(n,{});E===!1&&(U={});let{prevResolvedValues:Ie={}}=x,dr={...Ie,...U},Zt=z=>{_=!0,g.delete(z),x.needsAnimating[z]=!0};for(let z in dr){let I=U[z],Tt=Ie[z];y.hasOwnProperty(z)||(I!==Tt?Gt(I)&&Gt(Tt)?!Mr(I,Tt)||R?Zt(z):x.protectedKeys[z]=!0:I!==void 0?Zt(z):g.add(z):I!==void 0&&g.has(z)?Zt(z):x.protectedKeys[z]=!0)}x.prevProp=V,x.prevResolvedValues=U,x.isActive&&(y={...y,...U}),r&&t.blockInitialAnimation&&(_=!1),_&&!N&&d.push(...pt.map(z=>({animation:z,options:{type:T,...u}})))}if(g.size){let C={};g.forEach(T=>{let x=t.getBaseTarget(T);x!==void 0&&(C[T]=x)}),d.push({animation:C})}let P=!!d.length;return r&&f.initial===!1&&!t.manuallyAnimateOnMount&&(P=!1),r=!1,P?e(d):Promise.resolve()}function l(u,m,f){var h;if(o[u].isActive===m)return Promise.resolve();(h=t.variantChildren)===null||h===void 0||h.forEach(g=>{var y;return(y=g.animationState)===null||y===void 0?void 0:y.setActive(u,m)}),o[u].isActive=m;let d=a(f,u);for(let g in o)o[g].protectedKeys={};return d}return{animateChanges:a,setActive:l,setAnimateFunction:c,getState:()=>o}}function ol(t,e){return typeof e=="string"?e!==t:Array.isArray(e)?!Mr(e,t):!1}function Wt(t=!1){return{isActive:t,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function rl(){return{animate:Wt(!0),whileInView:Wt(),whileHover:Wt(),whileTap:Wt(),whileDrag:Wt(),whileFocus:Wt(),exit:Wt()}}var zo=class extends G{constructor(e){super(e),e.animationState||(e.animationState=ss(e))}updateAnimationControlsSubscription(){let{animate:e}=this.node.getProps();this.unmount(),wt(e)&&(this.unmount=e.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){let{animate:e}=this.node.getProps(),{animate:o}=this.node.prevProps||{};e!==o&&this.updateAnimationControlsSubscription()}unmount(){}};i();s();var nl=0,Ho=class extends G{constructor(){super(...arguments),this.id=nl++}update(){if(!this.node.presenceContext)return;let{isPresent:e,onExitComplete:o,custom:r}=this.node.presenceContext,{isPresent:n}=this.node.prevPresenceContext||{};if(!this.node.animationState||e===n)return;let c=this.node.animationState.setActive("exit",!e,{custom:r??this.node.getProps().custom});o&&!e&&c.then(()=>o(this.id))}mount(){let{register:e}=this.node.presenceContext||{};e&&(this.unmount=e(this.id))}unmount(){}};var as={animation:{Feature:zo},exit:{Feature:Ho}};i();s();i();s();i();s();i();s();i();s();var cs=(t,e)=>Math.abs(t-e);function ls(t,e){let o=cs(t.x,e.x),r=cs(t.y,e.y);return Math.sqrt(o**2+r**2)}var Ve=class{constructor(e,o,{transformPagePoint:r}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let m=Kr(this.lastMoveEventInfo,this.history),f=this.startEvent!==null,h=ls(m.offset,{x:0,y:0})>=3;if(!f&&!h)return;let{point:d}=m,{timestamp:g}=O;this.history.push({...d,timestamp:g});let{onStart:y,onMove:v}=this.handlers;f||(y&&y(this.lastMoveEvent,m),this.startEvent=this.lastMoveEvent),v&&v(this.lastMoveEvent,m)},this.handlePointerMove=(m,f)=>{this.lastMoveEvent=m,this.lastMoveEventInfo=Wr(f,this.transformPagePoint),S.update(this.updatePoint,!0)},this.handlePointerUp=(m,f)=>{if(this.end(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;let{onEnd:h,onSessionEnd:d}=this.handlers,g=Kr(m.type==="pointercancel"?this.lastMoveEventInfo:Wr(f,this.transformPagePoint),this.history);this.startEvent&&h&&h(m,g),d&&d(m,g)},!Vo(e))return;this.handlers=o,this.transformPagePoint=r;let n=$t(e),c=Wr(n,this.transformPagePoint),{point:a}=c,{timestamp:l}=O;this.history=[{...a,timestamp:l}];let{onSessionStart:u}=o;u&&u(e,Kr(c,this.history)),this.removeListeners=X(Z(self,"pointermove",this.handlePointerMove),Z(self,"pointerup",this.handlePointerUp),Z(self,"pointercancel",this.handlePointerUp))}updateHandlers(e){this.handlers=e}end(){this.removeListeners&&this.removeListeners(),W.update(this.updatePoint)}};function Wr(t,e){return e?{point:e(t.point)}:t}function us(t,e){return{x:t.x-e.x,y:t.y-e.y}}function Kr({point:t},e){return{point:t,delta:us(t,ms(e)),offset:us(t,il(e)),velocity:sl(e,.1)}}function il(t){return t[0]}function ms(t){return t[t.length-1]}function sl(t,e){if(t.length<2)return{x:0,y:0};let o=t.length-1,r=null,n=ms(t);for(;o>=0&&(r=t[o],!(n.timestamp-r.timestamp>tt(e)));)o--;if(!r)return{x:0,y:0};let c=at(n.timestamp-r.timestamp);if(c===0)return{x:0,y:0};let a={x:(n.x-r.x)/c,y:(n.y-r.y)/c};return a.x===1/0&&(a.x=0),a.y===1/0&&(a.y=0),a}i();s();i();s();function $(t){return t.max-t.min}function Wo(t,e=0,o=.01){return Math.abs(t-e)<=o}function fs(t,e,o,r=.5){t.origin=r,t.originPoint=w(e.min,e.max,t.origin),t.scale=$(o)/$(e),(Wo(t.scale,1,1e-4)||isNaN(t.scale))&&(t.scale=1),t.translate=w(o.min,o.max,t.origin)-t.originPoint,(Wo(t.translate)||isNaN(t.translate))&&(t.translate=0)}function Te(t,e,o,r){fs(t.x,e.x,o.x,r?r.originX:void 0),fs(t.y,e.y,o.y,r?r.originY:void 0)}function ps(t,e,o){t.min=o.min+e.min,t.max=t.min+$(e)}function ds(t,e,o){ps(t.x,e.x,o.x),ps(t.y,e.y,o.y)}function hs(t,e,o){t.min=e.min-o.min,t.max=t.min+$(e)}function Se(t,e,o){hs(t.x,e.x,o.x),hs(t.y,e.y,o.y)}function Ps(t,{min:e,max:o},r){return e!==void 0&&t<e?t=r?w(e,t,r.min):Math.max(t,e):o!==void 0&&t>o&&(t=r?w(o,t,r.max):Math.min(t,o)),t}function gs(t,e,o){return{min:e!==void 0?t.min+e:void 0,max:o!==void 0?t.max+o-(t.max-t.min):void 0}}function bs(t,{top:e,left:o,bottom:r,right:n}){return{x:gs(t.x,o,n),y:gs(t.y,e,r)}}function ys(t,e){let o=e.min-t.min,r=e.max-t.max;return e.max-e.min<t.max-t.min&&([o,r]=[r,o]),{min:o,max:r}}function Vs(t,e){return{x:ys(t.x,e.x),y:ys(t.y,e.y)}}function Ts(t,e){let o=.5,r=$(t),n=$(e);return n>r?o=Pt(e.min,e.max-r,t.min):r>n&&(o=Pt(t.min,t.max-n,e.min)),Q(0,1,o)}function Ss(t,e){let o={};return e.min!==void 0&&(o.min=e.min-t.min),e.max!==void 0&&(o.max=e.max-t.min),o}var Ko=.35;function Cs(t=Ko){return t===!1?t=0:t===!0&&(t=Ko),{x:xs(t,"left","right"),y:xs(t,"top","bottom")}}function xs(t,e,o){return{min:vs(t,e),max:vs(t,o)}}function vs(t,e){return typeof t=="number"?t:t[e]||0}i();s();var As=()=>({translate:0,scale:1,origin:0,originPoint:0}),Ce=()=>({x:As(),y:As()}),ws=()=>({min:0,max:0}),k=()=>({x:ws(),y:ws()});i();s();function it(t){return[t("x"),t("y")]}i();s();i();s();function _o({top:t,left:e,right:o,bottom:r}){return{x:{min:e,max:o},y:{min:t,max:r}}}function Ms({x:t,y:e}){return{top:e.min,right:t.max,bottom:e.max,left:t.min}}function Ds(t,e){if(!e)return t;let o=e({x:t.left,y:t.top}),r=e({x:t.right,y:t.bottom});return{top:o.y,left:o.x,bottom:r.y,right:r.x}}i();s();i();s();function _r(t){return t===void 0||t===1}function Yo({scale:t,scaleX:e,scaleY:o}){return!_r(t)||!_r(e)||!_r(o)}function bt(t){return Yo(t)||Yr(t)||t.z||t.rotate||t.rotateX||t.rotateY}function Yr(t){return Rs(t.x)||Rs(t.y)}function Rs(t){return t&&t!=="0%"}function Xe(t,e,o){let r=t-o,n=e*r;return o+n}function Es(t,e,o,r,n){return n!==void 0&&(t=Xe(t,n,r)),Xe(t,o,r)+e}function qr(t,e=0,o=1,r,n){t.min=Es(t.min,e,o,r,n),t.max=Es(t.max,e,o,r,n)}function Zr(t,{x:e,y:o}){qr(t.x,e.translate,e.scale,e.originPoint),qr(t.y,o.translate,o.scale,o.originPoint)}function ks(t,e,o,r=!1){let n=o.length;if(!n)return;e.x=e.y=1;let c,a;for(let l=0;l<n;l++){c=o[l],a=c.projectionDelta;let u=c.instance;u&&u.style&&u.style.display==="contents"||(r&&c.options.layoutScroll&&c.scroll&&c!==c.root&&Kt(t,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),a&&(e.x*=a.x.scale,e.y*=a.y.scale,Zr(t,a)),r&&bt(c.latestValues)&&Kt(t,c.latestValues))}e.x=Ls(e.x),e.y=Ls(e.y)}function Ls(t){return Number.isInteger(t)||t>1.0000000000001||t<.999999999999?t:1}function mt(t,e){t.min=t.min+e,t.max=t.max+e}function Bs(t,e,[o,r,n]){let c=e[n]!==void 0?e[n]:.5,a=w(t.min,t.max,c);qr(t,e[o],e[r],a,e.scale)}var al=["x","scaleX","originX"],cl=["y","scaleY","originY"];function Kt(t,e){Bs(t.x,e,al),Bs(t.y,e,cl)}function Xr(t,e){return _o(Ds(t.getBoundingClientRect(),e))}function Fs(t,e,o){let r=Xr(t,o),{scroll:n}=e;return n&&(mt(r.x,n.offset.x),mt(r.y,n.offset.y)),r}var ll=new WeakMap,Zo=class{constructor(e){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=k(),this.visualElement=e}start(e,{snapToCursor:o=!1}={}){let{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;let n=u=>{this.stopAnimation(),o&&this.snapToCursor($t(u,"page").point)},c=(u,m)=>{let{drag:f,dragPropagation:h,onDragStart:d}=this.getProps();if(f&&!h&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=Vr(f),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),it(y=>{let v=this.getAxisMotionValue(y).get()||0;if(H.test(v)){let{projection:P}=this.visualElement;if(P&&P.layout){let C=P.layout.layoutBox[y];C&&(v=$(C)*(parseFloat(v)/100))}}this.originPoint[y]=v}),d&&S.update(()=>d(u,m));let{animationState:g}=this.visualElement;g&&g.setActive("whileDrag",!0)},a=(u,m)=>{let{dragPropagation:f,dragDirectionLock:h,onDirectionLock:d,onDrag:g}=this.getProps();if(!f&&!this.openGlobalLock)return;let{offset:y}=m;if(h&&this.currentDirection===null){this.currentDirection=ul(y),this.currentDirection!==null&&d&&d(this.currentDirection);return}this.updateAxis("x",m.point,y),this.updateAxis("y",m.point,y),this.visualElement.render(),g&&g(u,m)},l=(u,m)=>this.stop(u,m);this.panSession=new Ve(e,{onSessionStart:n,onStart:c,onMove:a,onSessionEnd:l},{transformPagePoint:this.visualElement.getTransformPagePoint()})}stop(e,o){let r=this.isDragging;if(this.cancel(),!r)return;let{velocity:n}=o;this.startAnimation(n);let{onDragEnd:c}=this.getProps();c&&S.update(()=>c(e,o))}cancel(){this.isDragging=!1;let{projection:e,animationState:o}=this.visualElement;e&&(e.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;let{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),o&&o.setActive("whileDrag",!1)}updateAxis(e,o,r){let{drag:n}=this.getProps();if(!r||!qo(e,n,this.currentDirection))return;let c=this.getAxisMotionValue(e),a=this.originPoint[e]+r[e];this.constraints&&this.constraints[e]&&(a=Ps(a,this.constraints[e],this.elastic[e])),c.set(a)}resolveConstraints(){let{dragConstraints:e,dragElastic:o}=this.getProps(),{layout:r}=this.visualElement.projection||{},n=this.constraints;e&&gt(e)?this.constraints||(this.constraints=this.resolveRefConstraints()):e&&r?this.constraints=bs(r.layoutBox,e):this.constraints=!1,this.elastic=Cs(o),n!==this.constraints&&r&&this.constraints&&!this.hasMutatedConstraints&&it(c=>{this.getAxisMotionValue(c)&&(this.constraints[c]=Ss(r.layoutBox[c],this.constraints[c]))})}resolveRefConstraints(){let{dragConstraints:e,onMeasureDragConstraints:o}=this.getProps();if(!e||!gt(e))return!1;let r=e.current;B(r!==null,"If `dragConstraints` is set as a React ref, that ref must be passed to another component's `ref` prop.");let{projection:n}=this.visualElement;if(!n||!n.layout)return!1;let c=Fs(r,n.root,this.visualElement.getTransformPagePoint()),a=Vs(n.layout.layoutBox,c);if(o){let l=o(Ms(a));this.hasMutatedConstraints=!!l,l&&(a=_o(l))}return a}startAnimation(e){let{drag:o,dragMomentum:r,dragElastic:n,dragTransition:c,dragSnapToOrigin:a,onDragTransitionEnd:l}=this.getProps(),u=this.constraints||{},m=it(f=>{if(!qo(f,o,this.currentDirection))return;let h=u&&u[f]||{};a&&(h={min:0,max:0});let d=n?200:1e6,g=n?40:1e7,y={type:"inertia",velocity:r?e[f]:0,bounceStiffness:d,bounceDamping:g,timeConstant:750,restDelta:1,restSpeed:10,...c,...h};return this.startAxisValueAnimation(f,y)});return Promise.all(m).then(l)}startAxisValueAnimation(e,o){let r=this.getAxisMotionValue(e);return r.start(ye(e,r,0,o))}stopAnimation(){it(e=>this.getAxisMotionValue(e).stop())}getAxisMotionValue(e){let o="_drag"+e.toUpperCase(),r=this.visualElement.getProps(),n=r[o];return n||this.visualElement.getValue(e,(r.initial?r.initial[e]:void 0)||0)}snapToCursor(e){it(o=>{let{drag:r}=this.getProps();if(!qo(o,r,this.currentDirection))return;let{projection:n}=this.visualElement,c=this.getAxisMotionValue(o);if(n&&n.layout){let{min:a,max:l}=n.layout.layoutBox[o];c.set(e[o]-w(a,l,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;let{drag:e,dragConstraints:o}=this.getProps(),{projection:r}=this.visualElement;if(!gt(o)||!r||!this.constraints)return;this.stopAnimation();let n={x:0,y:0};it(a=>{let l=this.getAxisMotionValue(a);if(l){let u=l.get();n[a]=Ts({min:u,max:u},this.constraints[a])}});let{transformTemplate:c}=this.visualElement.getProps();this.visualElement.current.style.transform=c?c({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),it(a=>{if(!qo(a,e,null))return;let l=this.getAxisMotionValue(a),{min:u,max:m}=this.constraints[a];l.set(w(u,m,n[a]))})}addListeners(){if(!this.visualElement.current)return;ll.set(this.visualElement,this);let e=this.visualElement.current,o=Z(e,"pointerdown",u=>{let{drag:m,dragListener:f=!0}=this.getProps();m&&f&&this.start(u)}),r=()=>{let{dragConstraints:u}=this.getProps();gt(u)&&(this.constraints=this.resolveRefConstraints())},{projection:n}=this.visualElement,c=n.addEventListener("measure",r);n&&!n.layout&&(n.root&&n.root.updateScroll(),n.updateLayout()),r();let a=q(self,"resize",()=>this.scalePositionWithinConstraints()),l=n.addEventListener("didUpdate",({delta:u,hasLayoutChanged:m})=>{this.isDragging&&m&&(it(f=>{let h=this.getAxisMotionValue(f);h&&(this.originPoint[f]+=u[f].translate,h.set(h.get()+u[f].translate))}),this.visualElement.render())});return()=>{a(),o(),c(),l&&l()}}getProps(){let e=this.visualElement.getProps(),{drag:o=!1,dragDirectionLock:r=!1,dragPropagation:n=!1,dragConstraints:c=!1,dragElastic:a=Ko,dragMomentum:l=!0}=e;return{...e,drag:o,dragDirectionLock:r,dragPropagation:n,dragConstraints:c,dragElastic:a,dragMomentum:l}}};function qo(t,e,o){return(e===!0||e===t)&&(o===null||o===t)}function ul(t,e=10){let o=null;return Math.abs(t.y)>e?o="y":Math.abs(t.x)>e&&(o="x"),o}var Xo=class extends G{constructor(e){super(e),this.removeGroupControls=F,this.removeListeners=F,this.controls=new Zo(e)}mount(){let{dragControls:e}=this.node.getProps();e&&(this.removeGroupControls=e.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||F}unmount(){this.removeGroupControls(),this.removeListeners()}};i();s();var Is=t=>(e,o)=>{t&&S.update(()=>t(e,o))},Jo=class extends G{constructor(){super(...arguments),this.removePointerDownListener=F}onPointerDown(e){this.session=new Ve(e,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint()})}createPanHandlers(){let{onPanSessionStart:e,onPanStart:o,onPan:r,onPanEnd:n}=this.node.getProps();return{onSessionStart:Is(e),onStart:Is(o),onMove:r,onEnd:(c,a)=>{delete this.session,n&&S.update(()=>n(c,a))}}}mount(){this.removePointerDownListener=Z(this.node.current,"pointerdown",e=>this.onPointerDown(e))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}};i();s();var Me=M(D(),1);i();s();var Ae=M(D(),1);function Os(){let t=(0,Ae.useContext)(Ct);if(t===null)return[!0,null];let{isPresent:e,onExitComplete:o,register:r}=t,n=(0,Ae.useId)();return(0,Ae.useEffect)(()=>r(n),[]),!e&&o?[!1,()=>o&&o(n)]:[!0]}i();s();function js(t,e){return e.max===e.min?0:t/(e.max-e.min)*100}var we={correct:(t,e)=>{if(!e.target)return t;if(typeof t=="string")if(b.test(t))t=parseFloat(t);else return t;let o=js(t,e.target.x),r=js(t,e.target.y);return`${o}% ${r}%`}};i();s();i();s();var Qr=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function ml(t){let e=Qr.exec(t);if(!e)return[,];let[,o,r]=e;return[o,r]}var fl=4;function Jr(t,e,o=1){B(o<=fl,`Max CSS variable fallback depth detected in property "${t}". This may indicate a circular fallback dependency.`);let[r,n]=ml(t);if(!r)return;let c=self.getComputedStyle(e).getPropertyValue(r);return c?c.trim():mo(n)?Jr(n,e,o+1):n}function Ns(t,{...e},o){let r=t.current;if(!(r instanceof Element))return{target:e,transitionEnd:o};o&&(o={...o}),t.values.forEach(n=>{let c=n.get();if(!mo(c))return;let a=Jr(c,r);a&&n.set(a)});for(let n in e){let c=e[n];if(!mo(c))continue;let a=Jr(c,r);a&&(e[n]=a,o||(o={}),o[n]===void 0&&(o[n]=c))}return{target:e,transitionEnd:o}}var Us="_$css",Gs={correct:(t,{treeScale:e,projectionDelta:o})=>{let r=t,n=t.includes("var("),c=[];n&&(t=t.replace(Qr,g=>(c.push(g),Us)));let a=K.parse(t);if(a.length>5)return r;let l=K.createTransformer(t),u=typeof a[0]!="number"?1:0,m=o.x.scale*e.x,f=o.y.scale*e.y;a[0+u]/=m,a[1+u]/=f;let h=w(m,f,.5);typeof a[2+u]=="number"&&(a[2+u]/=h),typeof a[3+u]=="number"&&(a[3+u]/=h);let d=l(a);if(n){let g=0;d=d.replace(Us,()=>{let y=c[g];return g++,y})}return d}};var tn=class extends Me.default.Component{componentDidMount(){let{visualElement:e,layoutGroup:o,switchLayoutGroup:r,layoutId:n}=this.props,{projection:c}=e;jn(pl),c&&(o.group&&o.group.add(c),r&&r.register&&n&&r.register(c),c.root.didUpdate(),c.addEventListener("animationComplete",()=>{this.safeToRemove()}),c.setOptions({...c.options,onExitComplete:()=>this.safeToRemove()})),Mt.hasEverUpdated=!0}getSnapshotBeforeUpdate(e){let{layoutDependency:o,visualElement:r,drag:n,isPresent:c}=this.props,a=r.projection;return a&&(a.isPresent=c,n||e.layoutDependency!==o||o===void 0?a.willUpdate():this.safeToRemove(),e.isPresent!==c&&(c?a.promote():a.relegate()||S.postRender(()=>{let l=a.getStack();(!l||!l.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){let{projection:e}=this.props.visualElement;e&&(e.root.didUpdate(),!e.currentAnimation&&e.isLead()&&this.safeToRemove())}componentWillUnmount(){let{visualElement:e,layoutGroup:o,switchLayoutGroup:r}=this.props,{projection:n}=e;n&&(n.scheduleCheckAfterUnmount(),o&&o.group&&o.group.remove(n),r&&r.deregister&&r.deregister(n))}safeToRemove(){let{safeToRemove:e}=this.props;e&&e()}render(){return null}};function Qo(t){let[e,o]=Os(),r=(0,Me.useContext)(Qt);return Me.default.createElement(tn,{...t,layoutGroup:r,switchLayoutGroup:(0,Me.useContext)(co),isPresent:e,safeToRemove:o})}var pl={borderRadius:{...we,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:we,borderTopRightRadius:we,borderBottomLeftRadius:we,borderBottomRightRadius:we,boxShadow:Gs};i();s();i();s();i();s();var Ws=["TopLeft","TopRight","BottomLeft","BottomRight"],hl=Ws.length,$s=t=>typeof t=="string"?parseFloat(t):t,zs=t=>typeof t=="number"||b.test(t);function Ks(t,e,o,r,n,c){n?(t.opacity=w(0,o.opacity!==void 0?o.opacity:1,dl(r)),t.opacityExit=w(e.opacity!==void 0?e.opacity:1,0,gl(r))):c&&(t.opacity=w(e.opacity!==void 0?e.opacity:1,o.opacity!==void 0?o.opacity:1,r));for(let a=0;a<hl;a++){let l=`border${Ws[a]}Radius`,u=Hs(e,l),m=Hs(o,l);if(u===void 0&&m===void 0)continue;u||(u=0),m||(m=0),u===0||m===0||zs(u)===zs(m)?(t[l]=Math.max(w($s(u),$s(m),r),0),(H.test(m)||H.test(u))&&(t[l]+="%")):t[l]=m}(e.rotate||o.rotate)&&(t.rotate=w(e.rotate||0,o.rotate||0,r))}function Hs(t,e){return t[e]!==void 0?t[e]:t.borderRadius}var dl=_s(0,.5,Ke),gl=_s(.5,.95,F);function _s(t,e,o){return r=>r<t?0:r>e?1:o(Pt(t,e,r))}i();s();function Ys(t,e){t.min=e.min,t.max=e.max}function et(t,e){Ys(t.x,e.x),Ys(t.y,e.y)}i();s();function qs(t,e,o,r,n){return t-=e,t=Xe(t,1/o,r),n!==void 0&&(t=Xe(t,1/n,r)),t}function yl(t,e=0,o=1,r=.5,n,c=t,a=t){if(H.test(e)&&(e=parseFloat(e),e=w(a.min,a.max,e/100)-a.min),typeof e!="number")return;let l=w(c.min,c.max,r);t===c&&(l-=e),t.min=qs(t.min,e,o,l,n),t.max=qs(t.max,e,o,l,n)}function Zs(t,e,[o,r,n],c,a){yl(t,e[o],e[r],e[n],e.scale,c,a)}var xl=["x","scaleX","originX"],vl=["y","scaleY","originY"];function en(t,e,o,r){Zs(t.x,e,xl,o?o.x:void 0,r?r.x:void 0),Zs(t.y,e,vl,o?o.y:void 0,r?r.y:void 0)}i();s();function Xs(t){return t.translate===0&&t.scale===1}function on(t){return Xs(t.x)&&Xs(t.y)}function tr(t,e){return t.x.min===e.x.min&&t.x.max===e.x.max&&t.y.min===e.y.min&&t.y.max===e.y.max}function rn(t){return $(t.x)/$(t.y)}i();s();var er=class{constructor(){this.members=[]}add(e){xe(this.members,e),e.scheduleRender()}remove(e){if(ve(this.members,e),e===this.prevLead&&(this.prevLead=void 0),e===this.lead){let o=this.members[this.members.length-1];o&&this.promote(o)}}relegate(e){let o=this.members.findIndex(n=>e===n);if(o===0)return!1;let r;for(let n=o;n>=0;n--){let c=this.members[n];if(c.isPresent!==!1){r=c;break}}return r?(this.promote(r),!0):!1}promote(e,o){let r=this.lead;if(e!==r&&(this.prevLead=r,this.lead=e,e.show(),r)){r.instance&&r.scheduleRender(),e.scheduleRender(),e.resumeFrom=r,o&&(e.resumeFrom.preserveOpacity=!0),r.snapshot&&(e.snapshot=r.snapshot,e.snapshot.latestValues=r.animationValues||r.latestValues),e.root&&e.root.isUpdating&&(e.isLayoutDirty=!0);let{crossfade:n}=e.options;n===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(e=>{let{options:o,resumingFrom:r}=e;o.onExitComplete&&o.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(e=>{e.instance&&e.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}};i();s();function nn(t,e,o){let r="",n=t.x.translate/e.x,c=t.y.translate/e.y;if((n||c)&&(r=`translate3d(${n}px, ${c}px, 0) `),(e.x!==1||e.y!==1)&&(r+=`scale(${1/e.x}, ${1/e.y}) `),o){let{rotate:u,rotateX:m,rotateY:f}=o;u&&(r+=`rotate(${u}deg) `),m&&(r+=`rotateX(${m}deg) `),f&&(r+=`rotateY(${f}deg) `)}let a=t.x.scale*e.x,l=t.y.scale*e.y;return(a!==1||l!==1)&&(r+=`scale(${a}, ${l})`),r||"none"}i();s();i();s();var Js=(t,e)=>t.depth-e.depth;var or=class{constructor(){this.children=[],this.isDirty=!1}add(e){xe(this.children,e),this.isDirty=!0}remove(e){ve(this.children,e),this.isDirty=!0}forEach(e){this.isDirty&&this.children.sort(Js),this.isDirty=!1,this.children.forEach(e)}};i();s();function Qs(t,e){let o=performance.now(),r=({timestamp:n})=>{let c=n-o;c>=e&&(W.read(r),t(c-e))};return S.read(r,!0),()=>W.read(r)}i();s();function ta(t){self.MotionDebug&&self.MotionDebug.record(t)}i();s();function rr(t){return t instanceof SVGElement&&t.tagName!=="svg"}i();s();function nr(t,e,o){let r=L(t)?t:nt(t);return r.start(ye("",r,e,o)),r.animation}var ea=["","X","Y","Z"],oa=1e3,Pl=0,_t={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function ir({attachResizeListener:t,defaultParent:e,measureScroll:o,checkIsScrollRoot:r,resetTransform:n}){return class{constructor(a,l={},u=e?.()){this.id=Pl++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.potentialNodes=new Map,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{_t.totalNodes=_t.resolvedTargetDeltas=_t.recalculatedProjection=0,this.nodes.forEach(Tl),this.nodes.forEach(wl),this.nodes.forEach(Ml),this.nodes.forEach(Sl),ta(_t)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.elementId=a,this.latestValues=l,this.root=u?u.root||u:this,this.path=u?[...u.path,u]:[],this.parent=u,this.depth=u?u.depth+1:0,a&&this.root.registerPotentialNode(a,this);for(let m=0;m<this.path.length;m++)this.path[m].shouldResetTransform=!0;this.root===this&&(this.nodes=new or)}addEventListener(a,l){return this.eventHandlers.has(a)||this.eventHandlers.set(a,new kt),this.eventHandlers.get(a).add(l)}notifyListeners(a,...l){let u=this.eventHandlers.get(a);u&&u.notify(...l)}hasListeners(a){return this.eventHandlers.has(a)}registerPotentialNode(a,l){this.potentialNodes.set(a,l)}mount(a,l=!1){if(this.instance)return;this.isSVG=rr(a),this.instance=a;let{layoutId:u,layout:m,visualElement:f}=this.options;if(f&&!f.current&&f.mount(a),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),this.elementId&&this.root.potentialNodes.delete(this.elementId),l&&(m||u)&&(this.isLayoutDirty=!0),t){let h,d=()=>this.root.updateBlockedByResize=!1;t(a,()=>{this.root.updateBlockedByResize=!0,h&&h(),h=Qs(d,250),Mt.hasAnimatedSinceResize&&(Mt.hasAnimatedSinceResize=!1,this.nodes.forEach(na))})}u&&this.root.registerSharedNode(u,this),this.options.animate!==!1&&f&&(u||m)&&this.addEventListener("didUpdate",({delta:h,hasLayoutChanged:d,hasRelativeTargetChanged:g,layout:y})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}let v=this.options.transition||f.getDefaultTransition()||Bl,{onLayoutAnimationStart:P,onLayoutAnimationComplete:C}=f.getProps(),T=!this.targetLayout||!tr(this.targetLayout,y)||g,x=!d&&g;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||x||d&&(T||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(h,x);let V={...Uo(v,"layout"),onPlay:P,onComplete:C};(f.shouldReduceMotion||this.options.layoutRoot)&&(V.delay=0,V.type=!1),this.startAnimation(V)}else!d&&this.animationProgress===0&&na(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=y})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);let a=this.getStack();a&&a.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,W.preRender(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(Dl),this.animationId++)}getTransformTemplate(){let{visualElement:a}=this.options;return a&&a.getProps().transformTemplate}willUpdate(a=!0){if(this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let f=0;f<this.path.length;f++){let h=this.path[f];h.shouldResetTransform=!0,h.updateScroll("snapshot"),h.options.layoutRoot&&h.willUpdate(!1)}let{layoutId:l,layout:u}=this.options;if(l===void 0&&!u)return;let m=this.getTransformTemplate();this.prevTransformTemplateValue=m?m(this.latestValues,""):void 0,this.updateSnapshot(),a&&this.notifyListeners("willUpdate")}didUpdate(){if(this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(ra);return}this.isUpdating&&(this.isUpdating=!1,this.potentialNodes.size&&(this.potentialNodes.forEach(kl),this.potentialNodes.clear()),this.nodes.forEach(Al),this.nodes.forEach(bl),this.nodes.forEach(Vl),this.clearAllSnapshots(),Co.update(),Co.preRender(),Co.render())}clearAllSnapshots(){this.nodes.forEach(Cl),this.sharedNodes.forEach(Rl)}scheduleUpdateProjection(){S.preRender(this.updateProjection,!1,!0)}scheduleCheckAfterUnmount(){S.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let u=0;u<this.path.length;u++)this.path[u].updateScroll();let a=this.layout;this.layout=this.measure(!1),this.layoutCorrected=k(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);let{visualElement:l}=this.options;l&&l.notify("LayoutMeasure",this.layout.layoutBox,a?a.layoutBox:void 0)}updateScroll(a="measure"){let l=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===a&&(l=!1),l&&(this.scroll={animationId:this.root.animationId,phase:a,isRoot:r(this.instance),offset:o(this.instance)})}resetTransform(){if(!n)return;let a=this.isLayoutDirty||this.shouldResetTransform,l=this.projectionDelta&&!on(this.projectionDelta),u=this.getTransformTemplate(),m=u?u(this.latestValues,""):void 0,f=m!==this.prevTransformTemplateValue;a&&(l||bt(this.latestValues)||f)&&(n(this.instance,m),this.shouldResetTransform=!1,this.scheduleRender())}measure(a=!0){let l=this.measurePageBox(),u=this.removeElementScroll(l);return a&&(u=this.removeTransform(u)),Fl(u),{animationId:this.root.animationId,measuredBox:l,layoutBox:u,latestValues:{},source:this.id}}measurePageBox(){let{visualElement:a}=this.options;if(!a)return k();let l=a.measureViewportBox(),{scroll:u}=this.root;return u&&(mt(l.x,u.offset.x),mt(l.y,u.offset.y)),l}removeElementScroll(a){let l=k();et(l,a);for(let u=0;u<this.path.length;u++){let m=this.path[u],{scroll:f,options:h}=m;if(m!==this.root&&f&&h.layoutScroll){if(f.isRoot){et(l,a);let{scroll:d}=this.root;d&&(mt(l.x,-d.offset.x),mt(l.y,-d.offset.y))}mt(l.x,f.offset.x),mt(l.y,f.offset.y)}}return l}applyTransform(a,l=!1){let u=k();et(u,a);for(let m=0;m<this.path.length;m++){let f=this.path[m];!l&&f.options.layoutScroll&&f.scroll&&f!==f.root&&Kt(u,{x:-f.scroll.offset.x,y:-f.scroll.offset.y}),bt(f.latestValues)&&Kt(u,f.latestValues)}return bt(this.latestValues)&&Kt(u,this.latestValues),u}removeTransform(a){let l=k();et(l,a);for(let u=0;u<this.path.length;u++){let m=this.path[u];if(!m.instance||!bt(m.latestValues))continue;Yo(m.latestValues)&&m.updateSnapshot();let f=k(),h=m.measurePageBox();et(f,h),en(l,m.latestValues,m.snapshot?m.snapshot.layoutBox:void 0,f)}return bt(this.latestValues)&&en(l,this.latestValues),l}setTargetDelta(a){this.targetDelta=a,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(a){this.options={...this.options,...a,crossfade:a.crossfade!==void 0?a.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}resolveTargetDelta(a=!1){var l;let u=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=u.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=u.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=u.isSharedProjectionDirty);let m=!!this.resumingFrom||this!==u;if(!(a||m&&this.isSharedProjectionDirty||this.isProjectionDirty||!((l=this.parent)===null||l===void 0)&&l.isProjectionDirty||this.attemptToResolveRelativeTarget))return;let{layout:h,layoutId:d}=this.options;if(!(!this.layout||!(h||d))){if(this.resolvedRelativeTargetAt=O.timestamp,!this.targetDelta&&!this.relativeTarget){let g=this.getClosestProjectingParent();g&&g.layout?(this.relativeParent=g,this.relativeTarget=k(),this.relativeTargetOrigin=k(),Se(this.relativeTargetOrigin,this.layout.layoutBox,g.layout.layoutBox),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=k(),this.targetWithTransforms=k()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.relativeParent.resolvedRelativeTargetAt!==O.timestamp&&this.relativeParent.resolveTargetDelta(!0),ds(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):et(this.target,this.layout.layoutBox),Zr(this.target,this.targetDelta)):et(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;let g=this.getClosestProjectingParent();g&&!!g.resumingFrom==!!this.resumingFrom&&!g.options.layoutScroll&&g.target?(this.relativeParent=g,this.relativeTarget=k(),this.relativeTargetOrigin=k(),Se(this.relativeTargetOrigin,this.target,g.target),et(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}_t.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||Yo(this.parent.latestValues)||Yr(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var a;let l=this.getLead(),u=!!this.resumingFrom||this!==l,m=!0;if((this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty)&&(m=!1),u&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(m=!1),this.resolvedRelativeTargetAt===O.timestamp&&(m=!1),m)return;let{layout:f,layoutId:h}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(f||h))return;et(this.layoutCorrected,this.layout.layoutBox),ks(this.layoutCorrected,this.treeScale,this.path,u);let{target:d}=l;if(!d)return;this.projectionDelta||(this.projectionDelta=Ce(),this.projectionDeltaWithTransform=Ce());let g=this.treeScale.x,y=this.treeScale.y,v=this.projectionTransform;Te(this.projectionDelta,this.layoutCorrected,d,this.latestValues),this.projectionTransform=nn(this.projectionDelta,this.treeScale),(this.projectionTransform!==v||this.treeScale.x!==g||this.treeScale.y!==y)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",d)),_t.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(a=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),a){let l=this.getStack();l&&l.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(a,l=!1){let u=this.snapshot,m=u?u.latestValues:{},f={...this.latestValues},h=Ce();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!l;let d=k(),g=u?u.source:void 0,y=this.layout?this.layout.source:void 0,v=g!==y,P=this.getStack(),C=!P||P.members.length<=1,T=!!(v&&!C&&this.options.crossfade===!0&&!this.path.some(Ll));this.animationProgress=0;let x;this.mixTargetDelta=V=>{let A=V/1e3;ia(h.x,a.x,A),ia(h.y,a.y,A),this.setTargetDelta(h),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Se(d,this.layout.layoutBox,this.relativeParent.layout.layoutBox),El(this.relativeTarget,this.relativeTargetOrigin,d,A),x&&tr(this.relativeTarget,x)&&(this.isProjectionDirty=!1),x||(x=k()),et(x,this.relativeTarget)),v&&(this.animationValues=f,Ks(f,m,this.latestValues,A,T,C)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=A},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(a){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&(W.update(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=S.update(()=>{Mt.hasAnimatedSinceResize=!0,this.currentAnimation=nr(0,oa,{...a,onUpdate:l=>{this.mixTargetDelta(l),a.onUpdate&&a.onUpdate(l)},onComplete:()=>{a.onComplete&&a.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);let a=this.getStack();a&&a.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(oa),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){let a=this.getLead(),{targetWithTransforms:l,target:u,layout:m,latestValues:f}=a;if(!(!l||!u||!m)){if(this!==a&&this.layout&&m&&ca(this.options.animationType,this.layout.layoutBox,m.layoutBox)){u=this.target||k();let h=$(this.layout.layoutBox.x);u.x.min=a.target.x.min,u.x.max=u.x.min+h;let d=$(this.layout.layoutBox.y);u.y.min=a.target.y.min,u.y.max=u.y.min+d}et(l,u),Kt(l,f),Te(this.projectionDeltaWithTransform,this.layoutCorrected,l,f)}}registerSharedNode(a,l){this.sharedNodes.has(a)||this.sharedNodes.set(a,new er),this.sharedNodes.get(a).add(l);let m=l.options.initialPromotionConfig;l.promote({transition:m?m.transition:void 0,preserveFollowOpacity:m&&m.shouldPreserveFollowOpacity?m.shouldPreserveFollowOpacity(l):void 0})}isLead(){let a=this.getStack();return a?a.lead===this:!0}getLead(){var a;let{layoutId:l}=this.options;return l?((a=this.getStack())===null||a===void 0?void 0:a.lead)||this:this}getPrevLead(){var a;let{layoutId:l}=this.options;return l?(a=this.getStack())===null||a===void 0?void 0:a.prevLead:void 0}getStack(){let{layoutId:a}=this.options;if(a)return this.root.sharedNodes.get(a)}promote({needsReset:a,transition:l,preserveFollowOpacity:u}={}){let m=this.getStack();m&&m.promote(this,u),a&&(this.projectionDelta=void 0,this.needsReset=!0),l&&this.setOptions({transition:l})}relegate(){let a=this.getStack();return a?a.relegate(this):!1}resetRotation(){let{visualElement:a}=this.options;if(!a)return;let l=!1,{latestValues:u}=a;if((u.rotate||u.rotateX||u.rotateY||u.rotateZ)&&(l=!0),!l)return;let m={};for(let f=0;f<ea.length;f++){let h="rotate"+ea[f];u[h]&&(m[h]=u[h],a.setStaticValue(h,0))}a.render();for(let f in m)a.setStaticValue(f,m[f]);a.scheduleRender()}getProjectionStyles(a={}){var l,u;let m={};if(!this.instance||this.isSVG)return m;if(this.isVisible)m.visibility="";else return{visibility:"hidden"};let f=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,m.opacity="",m.pointerEvents=ue(a.pointerEvents)||"",m.transform=f?f(this.latestValues,""):"none",m;let h=this.getLead();if(!this.projectionDelta||!this.layout||!h.target){let v={};return this.options.layoutId&&(v.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,v.pointerEvents=ue(a.pointerEvents)||""),this.hasProjected&&!bt(this.latestValues)&&(v.transform=f?f({},""):"none",this.hasProjected=!1),v}let d=h.animationValues||h.latestValues;this.applyTransformsToTarget(),m.transform=nn(this.projectionDeltaWithTransform,this.treeScale,d),f&&(m.transform=f(d,m.transform));let{x:g,y}=this.projectionDelta;m.transformOrigin=`${g.origin*100}% ${y.origin*100}% 0`,h.animationValues?m.opacity=h===this?(u=(l=d.opacity)!==null&&l!==void 0?l:this.latestValues.opacity)!==null&&u!==void 0?u:1:this.preserveOpacity?this.latestValues.opacity:d.opacityExit:m.opacity=h===this?d.opacity!==void 0?d.opacity:"":d.opacityExit!==void 0?d.opacityExit:0;for(let v in ee){if(d[v]===void 0)continue;let{correct:P,applyTo:C}=ee[v],T=m.transform==="none"?d[v]:P(d[v],h);if(C){let x=C.length;for(let V=0;V<x;V++)m[C[V]]=T}else m[v]=T}return this.options.layoutId&&(m.pointerEvents=h===this?ue(a.pointerEvents)||"":"none"),m}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(a=>{var l;return(l=a.currentAnimation)===null||l===void 0?void 0:l.stop()}),this.root.nodes.forEach(ra),this.root.sharedNodes.clear()}}}function bl(t){t.updateLayout()}function Vl(t){var e;let o=((e=t.resumeFrom)===null||e===void 0?void 0:e.snapshot)||t.snapshot;if(t.isLead()&&t.layout&&o&&t.hasListeners("didUpdate")){let{layoutBox:r,measuredBox:n}=t.layout,{animationType:c}=t.options,a=o.source!==t.layout.source;c==="size"?it(h=>{let d=a?o.measuredBox[h]:o.layoutBox[h],g=$(d);d.min=r[h].min,d.max=d.min+g}):ca(c,o.layoutBox,r)&&it(h=>{let d=a?o.measuredBox[h]:o.layoutBox[h],g=$(r[h]);d.max=d.min+g});let l=Ce();Te(l,r,o.layoutBox);let u=Ce();a?Te(u,t.applyTransform(n,!0),o.measuredBox):Te(u,r,o.layoutBox);let m=!on(l),f=!1;if(!t.resumeFrom){let h=t.getClosestProjectingParent();if(h&&!h.resumeFrom){let{snapshot:d,layout:g}=h;if(d&&g){let y=k();Se(y,o.layoutBox,d.layoutBox);let v=k();Se(v,r,g.layoutBox),tr(y,v)||(f=!0),h.options.layoutRoot&&(t.relativeTarget=v,t.relativeTargetOrigin=y,t.relativeParent=h)}}}t.notifyListeners("didUpdate",{layout:r,snapshot:o,delta:u,layoutDelta:l,hasLayoutChanged:m,hasRelativeTargetChanged:f})}else if(t.isLead()){let{onExitComplete:r}=t.options;r&&r()}t.options.transition=void 0}function Tl(t){_t.totalNodes++,t.parent&&(t.isProjecting()||(t.isProjectionDirty=t.parent.isProjectionDirty),t.isSharedProjectionDirty||(t.isSharedProjectionDirty=!!(t.isProjectionDirty||t.parent.isProjectionDirty||t.parent.isSharedProjectionDirty)),t.isTransformDirty||(t.isTransformDirty=t.parent.isTransformDirty))}function Sl(t){t.isProjectionDirty=t.isSharedProjectionDirty=t.isTransformDirty=!1}function Cl(t){t.clearSnapshot()}function ra(t){t.clearMeasurements()}function Al(t){let{visualElement:e}=t.options;e&&e.getProps().onBeforeLayoutMeasure&&e.notify("BeforeLayoutMeasure"),t.resetTransform()}function na(t){t.finishAnimation(),t.targetDelta=t.relativeTarget=t.target=void 0}function wl(t){t.resolveTargetDelta()}function Ml(t){t.calcProjection()}function Dl(t){t.resetRotation()}function Rl(t){t.removeLeadSnapshot()}function ia(t,e,o){t.translate=w(e.translate,0,o),t.scale=w(e.scale,1,o),t.origin=e.origin,t.originPoint=e.originPoint}function sa(t,e,o,r){t.min=w(e.min,o.min,r),t.max=w(e.max,o.max,r)}function El(t,e,o,r){sa(t.x,e.x,o.x,r),sa(t.y,e.y,o.y,r)}function Ll(t){return t.animationValues&&t.animationValues.opacityExit!==void 0}var Bl={duration:.45,ease:[.4,0,.1,1]};function kl(t,e){let o=t.root;for(let c=t.path.length-1;c>=0;c--)if(t.path[c].instance){o=t.path[c];break}let n=(o&&o!==t.root?o.instance:document).querySelector(`[data-projection-id="${e}"]`);n&&t.mount(n,!0)}function aa(t){t.min=Math.round(t.min),t.max=Math.round(t.max)}function Fl(t){aa(t.x),aa(t.y)}function ca(t,e,o){return t==="position"||t==="preserve-aspect"&&!Wo(rn(e),rn(o),.2)}i();s();var la=ir({attachResizeListener:(t,e)=>q(t,"resize",e),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0});var sn={current:void 0},sr=ir({measureScroll:t=>({x:t.scrollLeft,y:t.scrollTop}),defaultParent:()=>{if(!sn.current){let t=new la(0,{});t.mount(self),t.setOptions({layoutScroll:!0}),sn.current=t}return sn.current},resetTransform:(t,e)=>{t.style.transform=e!==void 0?e:"none"},checkIsScrollRoot:t=>self.getComputedStyle(t).position==="fixed"});var ua={pan:{Feature:Jo},drag:{Feature:Xo,ProjectionNode:sr,MeasureLayout:Qo}};i();s();i();s();i();s();i();s();i();s();var Il=new Set(["width","height","top","left","right","bottom","x","y"]),ha=t=>Il.has(t),Ol=t=>Object.keys(t).some(ha),ar=t=>t===rt||t===b,ma=(t,e)=>parseFloat(t.split(", ")[e]),fa=(t,e)=>(o,{transform:r})=>{if(r==="none"||!r)return 0;let n=r.match(/^matrix3d\((.+)\)$/);if(n)return ma(n[1],e);{let c=r.match(/^matrix\((.+)\)$/);return c?ma(c[1],t):0}},jl=new Set(["x","y","z"]),Nl=oe.filter(t=>!jl.has(t));function Ul(t){let e=[];return Nl.forEach(o=>{let r=t.getValue(o);r!==void 0&&(e.push([o,r.get()]),r.set(o.startsWith("scale")?1:0))}),e.length&&t.render(),e}var pa={width:({x:t},{paddingLeft:e="0",paddingRight:o="0"})=>t.max-t.min-parseFloat(e)-parseFloat(o),height:({y:t},{paddingTop:e="0",paddingBottom:o="0"})=>t.max-t.min-parseFloat(e)-parseFloat(o),top:(t,{top:e})=>parseFloat(e),left:(t,{left:e})=>parseFloat(e),bottom:({y:t},{top:e})=>parseFloat(e)+(t.max-t.min),right:({x:t},{left:e})=>parseFloat(e)+(t.max-t.min),x:fa(4,13),y:fa(5,14)},Gl=(t,e,o)=>{let r=e.measureViewportBox(),n=e.current,c=getComputedStyle(n),{display:a}=c,l={};a==="none"&&e.setStaticValue("display",t.display||"block"),o.forEach(m=>{l[m]=pa[m](r,c)}),e.render();let u=e.measureViewportBox();return o.forEach(m=>{let f=e.getValue(m);f&&f.jump(l[m]),t[m]=pa[m](u,c)}),t},$l=(t,e,o={},r={})=>{e={...e},r={...r};let n=Object.keys(e).filter(ha),c=[],a=!1,l=[];if(n.forEach(u=>{let m=t.getValue(u);if(!t.hasValue(u))return;let f=o[u],h=Pe(f),d=e[u],g;if(Gt(d)){let y=d.length,v=d[0]===null?1:0;f=d[v],h=Pe(f);for(let P=v;P<y;P++)g?B(Pe(d[P])===g,"All keyframes must be of the same type"):(g=Pe(d[P]),B(g===h||ar(h)&&ar(g),"Keyframes must be of the same dimension as the current value"))}else g=Pe(d);if(h!==g)if(ar(h)&&ar(g)){let y=m.get();typeof y=="string"&&m.set(parseFloat(y)),typeof d=="string"?e[u]=parseFloat(d):Array.isArray(d)&&g===b&&(e[u]=d.map(parseFloat))}else h?.transform&&g?.transform&&(f===0||d===0)?f===0?m.set(g.transform(f)):e[u]=h.transform(d):(a||(c=Ul(t),a=!0),l.push(u),r[u]=r[u]!==void 0?r[u]:e[u],m.jump(d))}),l.length){let u=l.indexOf("height")>=0?self.pageYOffset:null,m=Gl(e,t,l);return c.length&&c.forEach(([f,h])=>{t.getValue(f).set(h)}),t.render(),At&&u!==null&&self.scrollTo({top:u}),{target:m,transitionEnd:r}}else return{target:e,transitionEnd:r}};function da(t,e,o,r){return Ol(e)?$l(t,e,o,r):{target:e,transitionEnd:r}}var ga=(t,e,o,r)=>{let n=Ns(t,e,r);return e=n.target,r=n.transitionEnd,da(t,e,o,r)};i();s();i();s();i();s();var Yt={current:null},De={current:!1};function cr(){if(De.current=!0,!!At)if(self.matchMedia){let t=self.matchMedia("(prefers-reduced-motion)"),e=()=>Yt.current=t.matches;t.addListener(e),e()}else Yt.current=!1}i();s();function ya(t,e,o){let{willChange:r}=e;for(let n in e){let c=e[n],a=o[n];if(L(c))t.addValue(n,c),Ht(r)&&r.add(n),p.NODE_ENV==="development"&&Ft(c.version==="10.5.0",`Attempting to mix Framer Motion versions ${c.version} with 10.5.0 may not work as expected.`);else if(L(a))t.addValue(n,nt(c,{owner:t})),Ht(r)&&r.remove(n);else if(a!==c)if(t.hasValue(n)){let l=t.getValue(n);!l.hasAnimated&&l.set(c)}else{let l=t.getStaticValue(n);t.addValue(n,nt(l!==void 0?l:c,{owner:t}))}}for(let n in o)e[n]===void 0&&t.removeValue(n);return e}i();s();var It=new WeakMap;var va=Object.keys(Nt),zl=va.length,xa=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],Hl=Oe.length,lr=class{constructor({parent:e,props:o,presenceContext:r,reducedMotionConfig:n,visualState:c},a={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>S.render(this.render,!1,!0);let{latestValues:l,renderState:u}=c;this.latestValues=l,this.baseTarget={...l},this.initialValues=o.initial?{...l}:{},this.renderState=u,this.parent=e,this.props=o,this.presenceContext=r,this.depth=e?e.depth+1:0,this.reducedMotionConfig=n,this.options=a,this.isControllingVariants=jt(o),this.isVariantNode=so(o),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(e&&e.current);let{willChange:m,...f}=this.scrapeMotionValuesFromProps(o,{});for(let h in f){let d=f[h];l[h]!==void 0&&L(d)&&(d.set(l[h],!1),Ht(m)&&m.add(h))}}scrapeMotionValuesFromProps(e,o){return{}}mount(e){this.current=e,It.set(e,this),this.projection&&this.projection.mount(e),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((o,r)=>this.bindToMotionValue(r,o)),De.current||cr(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Yt.current,p.NODE_ENV!=="production"&&Ft(this.shouldReduceMotion!==!0,"You have Reduced Motion enabled on your device. Animations may not appear as expected."),this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){It.delete(this.current),this.projection&&this.projection.unmount(),W.update(this.notifyUpdate),W.render(this.render),this.valueSubscriptions.forEach(e=>e()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(let e in this.events)this.events[e].clear();for(let e in this.features)this.features[e].unmount();this.current=null}bindToMotionValue(e,o){let r=Y.has(e),n=o.on("change",a=>{this.latestValues[e]=a,this.props.onUpdate&&S.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),c=o.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(e,()=>{n(),c()})}sortNodePosition(e){return!this.current||!this.sortInstanceNodePosition||this.type!==e.type?0:this.sortInstanceNodePosition(this.current,e.current)}loadFeatures({children:e,...o},r,n,c,a){let l,u;if(p.NODE_ENV!=="production"&&n&&r){let m="You have rendered a `motion` component within a `LazyMotion` component. This will break tree shaking. Import and render a `m` component instead.";o.ignoreStrict?vt(!1,m):B(!1,m)}for(let m=0;m<zl;m++){let f=va[m],{isEnabled:h,Feature:d,ProjectionNode:g,MeasureLayout:y}=Nt[f];g&&(l=g),h(o)&&(!this.features[f]&&d&&(this.features[f]=new d(this)),y&&(u=y))}if(!this.projection&&l){this.projection=new l(c,this.latestValues,this.parent&&this.parent.projection);let{layoutId:m,layout:f,drag:h,dragConstraints:d,layoutScroll:g,layoutRoot:y}=o;this.projection.setOptions({layoutId:m,layout:f,alwaysMeasureLayout:!!h||d&&gt(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof f=="string"?f:"both",initialPromotionConfig:a,layoutScroll:g,layoutRoot:y})}return u}updateFeatures(){for(let e in this.features){let o=this.features[e];o.isMounted?o.update(this.props,this.prevProps):(o.mount(),o.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):k()}getStaticValue(e){return this.latestValues[e]}setStaticValue(e,o){this.latestValues[e]=o}makeTargetAnimatable(e,o=!0){return this.makeTargetAnimatableFromInstance(e,this.props,o)}update(e,o){(e.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=e,this.prevPresenceContext=this.presenceContext,this.presenceContext=o;for(let r=0;r<xa.length;r++){let n=xa[r];this.propEventSubscriptions[n]&&(this.propEventSubscriptions[n](),delete this.propEventSubscriptions[n]);let c=e["on"+n];c&&(this.propEventSubscriptions[n]=this.on(n,c))}this.prevMotionValues=ya(this,this.scrapeMotionValuesFromProps(e,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(e){return this.props.variants?this.props.variants[e]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(e=!1){if(e)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){let r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}let o={};for(let r=0;r<Hl;r++){let n=Oe[r],c=this.props[n];(yt(c)||c===!1)&&(o[n]=c)}return o}addVariantChild(e){let o=this.getClosestVariantNode();if(o)return o.variantChildren&&o.variantChildren.add(e),()=>o.variantChildren.delete(e)}addValue(e,o){o!==this.values.get(e)&&(this.removeValue(e),this.bindToMotionValue(e,o)),this.values.set(e,o),this.latestValues[e]=o.get()}removeValue(e){this.values.delete(e);let o=this.valueSubscriptions.get(e);o&&(o(),this.valueSubscriptions.delete(e)),delete this.latestValues[e],this.removeValueFromRenderState(e,this.renderState)}hasValue(e){return this.values.has(e)}getValue(e,o){if(this.props.values&&this.props.values[e])return this.props.values[e];let r=this.values.get(e);return r===void 0&&o!==void 0&&(r=nt(o,{owner:this}),this.addValue(e,r)),r}readValue(e){return this.latestValues[e]!==void 0||!this.current?this.latestValues[e]:this.readValueFromInstance(this.current,e,this.options)}setBaseTarget(e,o){this.baseTarget[e]=o}getBaseTarget(e){var o;let{initial:r}=this.props,n=typeof r=="string"||typeof r=="object"?(o=le(this.props,r))===null||o===void 0?void 0:o[e]:void 0;if(r&&n!==void 0)return n;let c=this.getBaseTargetFromProps(this.props,e);return c!==void 0&&!L(c)?c:this.initialValues[e]!==void 0&&n===void 0?void 0:this.baseTarget[e]}on(e,o){return this.events[e]||(this.events[e]=new kt),this.events[e].add(o)}notify(e,...o){this.events[e]&&this.events[e].notify(...o)}};var Re=class extends lr{sortInstanceNodePosition(e,o){return e.compareDocumentPosition(o)&2?1:-1}getBaseTargetFromProps(e,o){return e.style?e.style[o]:void 0}removeValueFromRenderState(e,{vars:o,style:r}){delete o[e],delete r[e]}makeTargetAnimatableFromInstance({transition:e,transitionEnd:o,...r},{transformValues:n},c){let a=ns(r,e||{},this);if(n&&(o&&(o=n(o)),r&&(r=n(r)),a&&(a=n(a))),c){rs(this,r,a);let l=ga(this,r,a,o);o=l.transitionEnd,r=l.target}return{transition:e,transitionEnd:o,...r}}};function Wl(t){return self.getComputedStyle(t)}var Ee=class extends Re{readValueFromInstance(e,o){if(Y.has(o)){let r=de(o);return r&&r.default||0}else{let r=Wl(e),n=(uo(o)?r.getPropertyValue(o):r[o])||0;return typeof n=="string"?n.trim():n}}measureInstanceViewportBox(e,{transformPagePoint:o}){return Xr(e,o)}build(e,o,r,n){re(e,o,r,n.transformTemplate)}scrapeMotionValuesFromProps(e,o){return ce(e,o)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);let{children:e}=this.props;L(e)&&(this.childSubscription=e.on("change",o=>{this.current&&(this.current.textContent=`${o}`)}))}renderInstance(e,o,r,n){yo(e,o,r,n)}};i();s();var Le=class extends Re{constructor(){super(...arguments),this.isSVGTag=!1}getBaseTargetFromProps(e,o){return e[o]}readValueFromInstance(e,o){if(Y.has(o)){let r=de(o);return r&&r.default||0}return o=xo.has(o)?o:ae(o),e.getAttribute(o)}measureInstanceViewportBox(){return k()}scrapeMotionValuesFromProps(e,o){return Po(e,o)}build(e,o,r,n){ie(e,o,r,this.isSVGTag,n.transformTemplate)}renderInstance(e,o,r,n){vo(e,o,r,n)}mount(e){this.isSVGTag=se(e.tagName),super.mount(e)}};var Pa=(t,e)=>te(t)?new Le(e,{enableHardwareAcceleration:!1}):new Ee(e,{enableHardwareAcceleration:!0});i();s();var ba={layout:{ProjectionNode:sr,MeasureLayout:Qo}};var Kl={...as,...hi,...ua,...ba},an=In((t,e)=>ii(t,e,Kl,Pa));i();s();var Vt=M(D(),1),st=M(D(),1);i();s();var Je=M(D(),1);i();s();var Va=M(D(),1);function ur(){let t=(0,Va.useRef)(!1);return dt(()=>(t.current=!0,()=>{t.current=!1}),[]),t}function Ta(){let t=ur(),[e,o]=(0,Je.useState)(0),r=(0,Je.useCallback)(()=>{t.current&&o(e+1)},[e]);return[(0,Je.useCallback)(()=>S.postRender(r),[r]),e]}i();s();var Qe=M(D(),1),to=M(D(),1);i();s();var Be=M(D(),1),qt=M(D(),1),cn=class extends Be.Component{getSnapshotBeforeUpdate(e){let o=this.props.childRef.current;if(o&&e.isPresent&&!this.props.isPresent){let r=this.props.sizeRef.current;r.height=o.offsetHeight||0,r.width=o.offsetWidth||0,r.top=o.offsetTop,r.left=o.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}};function Sa({children:t,isPresent:e}){let o=(0,qt.useId)(),r=(0,qt.useRef)(null),n=(0,qt.useRef)({width:0,height:0,top:0,left:0});return(0,qt.useInsertionEffect)(()=>{let{width:c,height:a,top:l,left:u}=n.current;if(e||!r.current||!c||!a)return;r.current.dataset.motionPopId=o;let m=document.createElement("style");return document.head.appendChild(m),m.sheet&&m.sheet.insertRule(`
          [data-motion-pop-id="${o}"] {
            position: absolute !important;
            width: ${c}px !important;
            height: ${a}px !important;
            top: ${l}px !important;
            left: ${u}px !important;
          }
        `),()=>{document.head.removeChild(m)}},[e]),Be.createElement(cn,{isPresent:e,childRef:r,sizeRef:n},Be.cloneElement(t,{ref:r}))}var mr=({children:t,initial:e,isPresent:o,onExitComplete:r,custom:n,presenceAffectsLayout:c,mode:a})=>{let l=ct(_l),u=(0,to.useId)(),m=(0,to.useMemo)(()=>({id:u,initial:e,isPresent:o,custom:n,onExitComplete:f=>{l.set(f,!0);for(let h of l.values())if(!h)return;r&&r()},register:f=>(l.set(f,!1),()=>l.delete(f))}),c?void 0:[o]);return(0,to.useMemo)(()=>{l.forEach((f,h)=>l.set(h,!1))},[o]),Qe.useEffect(()=>{!o&&!l.size&&r&&r()},[o]),a==="popLayout"&&(t=Qe.createElement(Sa,{isPresent:o},t)),Qe.createElement(Ct.Provider,{value:m},t)};function _l(){return new Map}i();s();var Ca=M(D(),1);function Aa(t){return(0,Ca.useEffect)(()=>()=>t(),[])}var ke=t=>t.key||"";function Yl(t,e){t.forEach(o=>{let r=ke(o);e.set(r,o)})}function ql(t){let e=[];return st.Children.forEach(t,o=>{(0,st.isValidElement)(o)&&e.push(o)}),e}var ln=({children:t,custom:e,initial:o=!0,onExitComplete:r,exitBeforeEnter:n,presenceAffectsLayout:c=!0,mode:a="sync"})=>{B(!n,"Replace exitBeforeEnter with mode='wait'");let[l]=Ta(),u=(0,st.useContext)(Qt).forceRender;u&&(l=u);let m=ur(),f=ql(t),h=f,d=new Set,g=(0,st.useRef)(h),y=(0,st.useRef)(new Map).current,v=(0,st.useRef)(!0);if(dt(()=>{v.current=!1,Yl(f,y),g.current=h}),Aa(()=>{v.current=!0,y.clear(),d.clear()}),v.current)return Vt.createElement(Vt.Fragment,null,h.map(x=>Vt.createElement(mr,{key:ke(x),isPresent:!0,initial:o?void 0:!1,presenceAffectsLayout:c,mode:a},x)));h=[...h];let P=g.current.map(ke),C=f.map(ke),T=P.length;for(let x=0;x<T;x++){let V=P[x];C.indexOf(V)===-1&&d.add(V)}return a==="wait"&&d.size&&(h=[]),d.forEach(x=>{if(C.indexOf(x)!==-1)return;let V=y.get(x);if(!V)return;let A=P.indexOf(x),E=()=>{y.delete(x),d.delete(x);let N=g.current.findIndex(R=>R.key===x);if(g.current.splice(N,1),!d.size){if(g.current=f,m.current===!1)return;l(),r&&r()}};h.splice(A,0,Vt.createElement(mr,{key:ke(V),isPresent:!1,onExitComplete:E,custom:e,presenceAffectsLayout:c,mode:a},V))}),h=h.map(x=>{let V=x.key;return d.has(V)?x:Vt.createElement(mr,{key:ke(x),isPresent:!0,presenceAffectsLayout:c,mode:a},x)}),p.NODE_ENV!=="production"&&a==="wait"&&h.length>1&&console.warn(`You're attempting to animate multiple children within AnimatePresence, but its mode is set to "wait". This will lead to odd visual behaviour.`),Vt.createElement(Vt.Fragment,null,d.size?h:h.map(x=>(0,st.cloneElement)(x)))};i();s();i();s();var Fe=M(D(),1);function un(t){let e=ct(()=>nt(t)),{isStatic:o}=(0,Fe.useContext)(Jt);if(o){let[,r]=(0,Fe.useState)(t);(0,Fe.useEffect)(()=>e.on("change",r),[])}return e}i();s();i();s();var Zl=t=>typeof t=="object"&&t.mix,Xl=t=>Zl(t)?t.mix:void 0;function wa(...t){let e=!Array.isArray(t[0]),o=e?0:-1,r=t[0+o],n=t[1+o],c=t[2+o],a=t[3+o],l=he(n,c,{mixer:Xl(c[0]),...a});return e?l(r):l}i();s();function Ma(t,e){let o=un(e()),r=()=>o.set(e());return r(),dt(()=>{let n=()=>S.update(r,!1,!0),c=t.map(a=>a.on("change",n));return()=>{c.forEach(a=>a()),W.update(r)}}),o}function Jl(t,e,o,r){let n=typeof e=="function"?e:wa(e,o,r);return Array.isArray(t)?Da(t,n):Da([t],([c])=>n(c))}function Da(t,e){let o=ct(()=>[]);return Ma(t,()=>{o.length=0;let r=t.length;for(let n=0;n<r;n++)o[n]=t[n].get();return e(o)})}i();s();function fr(t,e,o){var r;if(typeof t=="string"){let n=document;e&&(B(!!e.current,"Scope provided, but no element detected."),n=e.current),o?((r=o[t])!==null&&r!==void 0||(o[t]=n.querySelectorAll(t)),t=o[t]):t=n.querySelectorAll(t)}else t instanceof Element&&(t=[t]);return Array.from(t||[])}i();s();var Ra=M(D(),1);function Ql(){!De.current&&cr();let[t]=(0,Ra.useState)(Yt.current);return p.NODE_ENV!=="production"&&Ft(t!==!0,"You have Reduced Motion enabled on your device. Animations may not appear as expected."),t}i();s();i();s();var pr=class{constructor(e){this.animations=e.filter(Boolean)}then(e,o){return Promise.all(this.animations).then(e).catch(o)}get time(){return this.animations[0].time}set time(e){for(let o=0;o<this.animations.length;o++)this.animations[o].time=e}runAll(e){this.animations.forEach(o=>o[e]())}play(){this.runAll("play")}pause(){this.runAll("pause")}stop(){this.runAll("stop")}};i();s();function Ea(t){return typeof t=="object"&&!Array.isArray(t)}i();s();function La(t){let e={presenceContext:null,props:{},visualState:{renderState:{transform:{},transformOrigin:{},style:{},vars:{},attrs:{}},latestValues:{}}},o=rr(t)?new Le(e,{enableHardwareAcceleration:!1}):new Ee(e,{enableHardwareAcceleration:!0});o.mount(t),It.set(t,o)}function tu(t,e,o,r){let n=fr(t,r),c=n.length;B(!!c,"No valid element provided.");let a=[];for(let l=0;l<c;l++){let u=n[l];It.has(u)||La(u);let m=It.get(u);a.push(...be(m,{...e,transition:o},{}))}return new pr(a)}var Ba=t=>{function e(o,r,n={}){let c;return Ea(r)?c=tu(o,r,n,t):c=nr(o,r,n),t&&t.animations.push(c),c}return e},eu=Ba();i();s();var hr=M(D(),1);i();s();var ou={any:0,all:1};function ka(t,e,{root:o,margin:r,amount:n="any"}={}){let c=fr(t),a=new WeakMap,l=m=>{m.forEach(f=>{let h=a.get(f.target);if(f.isIntersecting!==!!h)if(f.isIntersecting){let d=e(f);typeof d=="function"?a.set(f.target,d):u.unobserve(f.target)}else h&&(h(f),a.delete(f.target))})},u=new IntersectionObserver(l,{root:o,rootMargin:r,threshold:typeof n=="number"?n:ou[n]});return c.forEach(m=>u.observe(m)),()=>u.disconnect()}function ru(t,{root:e,margin:o,amount:r,once:n=!1}={}){let[c,a]=(0,hr.useState)(!1);return(0,hr.useEffect)(()=>{if(!t.current||n&&c)return;let l=()=>(a(!0),n?void 0:()=>a(!1)),u={root:e&&e.current||void 0,margin:o,amount:r==="some"?"any":r};return ka(t.current,l,u)},[e,t,o,n]),c}i();s();var ft=M(D());var Fa=({width:t,trackColor:e="#181818",spinnerColor:o="#AB9FF2"})=>ft.default.createElement("svg",{width:t,height:t,viewBox:"0 0 26 26"},ft.default.createElement("g",null,ft.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M13 23.413c5.751 0 10.413-4.662 10.413-10.413S18.751 2.587 13 2.587 2.587 7.249 2.587 13 7.249 23.413 13 23.413zm0 2.315c7.03 0 12.727-5.699 12.727-12.728S20.03.273 13 .273C5.97.273.273 5.97.273 13 .273 20.03 5.97 25.728 13 25.728z",fill:e})),ft.default.createElement("g",null,ft.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M16.382 24.125a1.157 1.157 0 01.623-1.513 10.412 10.412 0 005.607-13.617 1.157 1.157 0 112.136-.89 12.726 12.726 0 01-6.853 16.643 1.157 1.157 0 01-1.513-.623z",fill:o})));Fa.defaultProps={width:44};var nu=Xt.div`
  position: ${t=>t.position};
  height: ${t=>t.diameter}px;
  width: ${t=>t.diameter}px;
  animation: rotate 0.5s linear infinite;
  @keyframes rotate {
    100% {
      transform: rotate(360deg);
    }
  }
`,eo=({diameter:t,color:e,trackColor:o,position:r})=>ft.default.createElement(nu,{diameter:t,position:r},ft.default.createElement(Fa,{width:t,spinnerColor:e,trackColor:o}));eo.defaultProps={diameter:44};var iu=Xt.div`
  display: flex;
  align-items: center;
  justify-content: center;
`,Y1=()=>ft.default.createElement(iu,null,ft.default.createElement(eo,null));i();s();var J=M(D());var Ia=Xt.button`
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
  padding: ${t=>t.paddingY}px 0px;
  width: ${t=>t.width};
  height: ${t=>t.height};
  border-radius: ${t=>t.borderRadius};
  font-size: ${t=>t.fontSize}px;
  font-weight: ${t=>t.fontWeight};
  line-height: ${t=>t.lineHeight}px;
  color: white;
  pointer-events: auto;
  border: none;
  outline-color: transparent;
  outline-style: none;
  cursor: ${t=>t.disabled?"auto":"pointer"};
  &:disabled {
    opacity: 0.6;
  }
  ${t=>t.noWrap&&ht`
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `}
  ${t=>t.theme==="primary"?ht`
          background: #ab9ff2;
          color: #222222;
          &:hover:enabled {
            background: #e2dffe;
          }
        `:t.theme==="warning"?ht`
          background: #eb3742;
          color: #222222;
          &:hover:enabled {
            background: #ce232e;
          }
        `:t.theme==="dark"?ht`
          background: #181818;
        `:t.theme==="metamask"?ht`
          background: #f5841f;
          &:hover:enabled {
            background: #d0701a;
          }
        `:t.theme==="link"?ht`
          background: transparent;
          color: #ab9ff2;
          justify-content: flex-start;
          &:hover:enabled {
            color: #ab9ff2;
          }
        `:t.theme==="text"?ht`
          background: transparent;
          padding-left: 4px;
          padding-right: 4px;
          justify-content: flex-start;
          &:hover:enabled {
            background: #444444;
          }
        `:ht`
          background: #333333;
          &:hover:enabled {
            background: #444444;
          }
        `}
`,oo=({children:t,loading:e,to:o,onClick:r,...n})=>o?J.default.createElement(su,{loading:e,to:o,...n},t):J.default.createElement(Ia,{...n,onClick:r},e?J.default.createElement(eo,{diameter:24,position:"absolute"}):t),su=({children:t,loading:e,to:o,...r})=>{let n=yn();if(!o)throw new Error("ButtonWithNavigation requires a 'to' prop");return J.default.createElement(Ia,{...r,onClick:()=>n(o)},e?J.default.createElement(eo,{diameter:24,position:"absolute"}):t)};oo.defaultProps={fontSize:16,fontWeight:600,lineHeight:19,paddingY:14,width:"100%",borderRadius:"6px",theme:"default",type:"button",noWrap:!0};var Oa=Xt.div`
  display: flex;
  flex-direction: ${t=>t.vertical?"column-reverse":"row"};
  width: 100%;
  gap: 10px;
`;var ja={fontSize:14,lineHeight:17,paddingY:10},au=({className:t,primaryText:e,secondaryText:o,onPrimaryClicked:r,onPrimaryHover:n,onSecondaryClicked:c,primaryTheme:a,secondaryTheme:l,primaryDisabled:u,primaryLoading:m,secondaryDisabled:f,buttonPairStyle:h})=>{let d=h==="normal"?{}:ja;return J.default.createElement(Oa,{className:t},J.default.createElement(oo,{theme:l,onClick:c,disabled:f,...d,"data-testid":"secondary-button"},o),J.default.createElement(oo,{type:"submit",theme:a,disabled:u,loading:m,onClick:r,onMouseEnter:n,...d,"data-testid":"primary-button"},e))};au.defaultProps={primaryTheme:"primary",secondaryTheme:"default",primaryDisabled:!1,buttonPairStyle:"normal"};var oR=({buttons:t,buttonStyle:e,className:o,vertical:r})=>{let n=e==="small"?ja:{};return J.default.createElement(Oa,{className:o,vertical:r},J.default.createElement(ln,null,t.map((c,a)=>typeof c.hideButton>"u"?J.default.createElement(oo,{key:c.key??(typeof c.text=="string"&&c.text?c.text:a),type:c.type??"button",theme:c.theme,onClick:c.onClick,disabled:c.disabled,loading:c.loading,className:c.className,"data-testid":c.testID,...n},c.text):c.hideButton?null:J.default.createElement(an.div,{key:c.key??(typeof c.text=="string"&&c.text?c.text:a),initial:{opacity:0,scale:.8,width:0},exit:{opacity:0,width:0},animate:{height:"auto",opacity:1,scale:1,width:"100%"},transition:{ease:"easeInOut",duration:.3}},J.default.createElement(oo,{type:c.type??"button",theme:c.theme,onClick:c.onClick,disabled:c.disabled,loading:c.loading,className:c.className,"data-testid":c.testID,...n},c.text)))))};export{an as a,ln as b,un as c,Jl as d,Ql as e,eu as f,ru as g,eo as h,Y1 as i,oo as j,au as k,oR as l};
//# sourceMappingURL=chunk-OKP6DFCI.js.map
