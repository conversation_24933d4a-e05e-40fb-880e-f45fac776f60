// <PERSON><PERSON><PERSON> tiên đăng ký ts-node để có thể import file TypeScript
require('ts-node').register({
  transpileOnly: true,
  compilerOptions: {
    module: 'CommonJS'
  }
});

// <PERSON><PERSON>y giờ có thể nhập file TypeScript
const globalSetup = require('./global-setup').default;

// Chạy global setup
(async () => {
  console.log('Đang chạy global setup riêng biệt...');
  try {
    await globalSetup({});
    console.log('Hoàn thành global setup.');
  } catch (error) {
    console.error('Lỗi khi chạy global setup:', error);
    process.exit(1);
  }
})();
