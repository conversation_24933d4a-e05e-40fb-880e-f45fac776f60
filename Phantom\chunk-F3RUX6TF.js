import{e as Bi}from"./chunk-V5T43K7V.js";import{$ as yt,$b as Mi,Aa as yi,C as ri,Ca as Si,F as si,Fa as gi,Fb as ki,G as ft,Gb as Ui,Hb as Ri,J as li,K as je,Ka as hi,Kb as <PERSON>,L as Fe,La as Ti,M as ai,N as ci,Pa as Ai,T as ui,Ua as ze,Va as xi,Z as Ct,_ as bt,_a as we,ab as fe,ac as Oi,bb as Jt,cb as $e,ea as pi,fb as eo,gb as wi,hb as Ye,hc as Ni,ia as mi,jb as to,ka as ts,kb as gt,la as di,lb as Ei,lc as Vi,m as We,ma as fi,mb as vi,nb as Ii,nc as Fi,o as Zo,q as Jo,r as ei,rb as <PERSON>,t as dt,u as ti,vb as <PERSON>,w as oi,x as ii,xa as <PERSON>,ya as Ci,z as ni,za as bi}from"./chunk-OUYKWOVO.js";import{a as zo,g as $o,o as Yo}from"./chunk-SLQBAOEK.js";import{$d as Ve,C as Yt,Da as Fo,Fa as lt,Ja as Ho,Ka as _o,L as Uo,Ma as Me,Na as at,O as ee,P as w,Pa as E,Q as k,Qa as Ko,Ra as Oe,T as Se,U as ge,_a as qo,_d as Xo,ab as ct,eb as Go,jb as ut,kb as Qo,nb as Wo,ob as jo,pe as pt,rd as N,te as mt,wc as H,wd as Ne}from"./chunk-MZZEJ42N.js";import{p as Re}from"./chunk-E3NPIRHS.js";import{a as Z,m as L}from"./chunk-56SJOU6P.js";import{V as No,_ as Xt,a as xe,b as R,c as Ro,da as rt,f as Lo,fa as Zt,ha as st,k as Bo,ka as Vo,l as Le,o as Mo,p as Be,r as nt,s as Oo,x as U}from"./chunk-ALUTR72U.js";import{ia as F,ka as O,la as ie,t as it,ta as D}from"./chunk-L3A2KHJO.js";import{a as c}from"./chunk-4P36KWOF.js";import{a as T}from"./chunk-7X4NV6OJ.js";import{f as S,h as l,l as es,m as Buffer,n as a}from"./chunk-3KENBVE7.js";l();a();var $=e=>{if(e!=null&&!(e.constructor===Object&&Object.keys(e).length===0))return e},os=e=>e??[],te=class extends Error{constructor(t){super(t),this.name="CollectiblesError"}},I=c.string().nullish().transform($),Ee=c.number().nullish().transform($),oo=c.union([I,Ee]).transform(e=>typeof e=="number"?e.toString():e),is=c.object({name:I,symbol:I,decimals:Ee,address:I}),io=(b=>(b[b.Uninitialized=0]="Uninitialized",b[b.EditionV1=1]="EditionV1",b[b.MasterEditionV1=2]="MasterEditionV1",b[b.ReservationListV1=3]="ReservationListV1",b[b.MetadataV1=4]="MetadataV1",b[b.ReservationListV2=5]="ReservationListV2",b[b.MasterEditionV2=6]="MasterEditionV2",b[b.EditionMarker=7]="EditionMarker",b[b.UseAuthorityRecord=8]="UseAuthorityRecord",b[b.CollectionAuthorityRecord=9]="CollectionAuthorityRecord",b[b.TokenOwnedEscrow=10]="TokenOwnedEscrow",b[b.TokenRecord=11]="TokenRecord",b[b.MetadataDelegate=12]="MetadataDelegate",b))(io||{}),ht=c.nativeEnum(io),Hi=c.object({price:c.number(),token:is.nullish().transform($)}),ns=Hi.nullish().transform($),rs=c.object({name:I,url:I,isVerified:c.coerce.boolean()}),_i=c.object({id:c.string(),isValidCollectionId:c.coerce.boolean(),name:I,description:I,externalUrl:I,imageUrl:I,isSpam:c.coerce.boolean(),spamStatus:c.nativeEnum(we).optional().default("NOT_VERIFIED"),floorPrice:Hi.nullish().transform($),ownerCount:Ee,totalCount:Ee,tokenCount:Ee,marketplaces:c.array(rs)}),ss=c.object({small:I,medium:I,large:I,blurhash:I}),ls=ss.nullish().transform($),as=c.enum(["image","video","audio","model","other"]),cs=c.object({url:I}),Xe=cs.nullish().transform($),us=c.object({type:as,image:Xe,video:Xe,audio:Xe,model:Xe,other:Xe,previews:ls}),ps=us.nullish().transform($),ms=c.object({trait_type:I,value:I.or(Ee),display_type:I}),ds=c.object({id:c.string(),contract:c.string(),standard:c.nativeEnum(xi)}),no=c.object({compressed:c.boolean(),merkleTree:c.string(),leafIndex:c.number()}),fs=c.object({mint:c.string(),tokenAccount:c.string(),standard:c.nativeEnum(ze),compression:no.nullish().transform($),programId:c.string(),mintExtensions:Bo(Ai).optional()}),Cs=c.object({id:c.string(),url:c.string(),displayName:c.string(),description:c.string(),count:c.number()}),Ki=c.object({hash:c.string(),index:c.number(),distinctRareSats:c.number(),satributes:c.array(Cs),blockNumber:c.number(),value:c.number(),backgroundImageUrl:c.string()}),bs=c.object({firstCreatedTransactionHash:c.string(),firstCreatedInscriptionId:c.string(),inscriptionId:I,inscriptionNumber:oo,outputValue:oo,satNumber:oo,satName:I,satRarity:I,protocolName:I,utxoHash:I,utxoIndex:Ee,createdAt:c.string().nullish().transform($),utxoDetails:Ki.nullish().transform($)}),sc=c.object({firstCreatedTransactionHash:c.string(),firstCreatedInscriptionId:c.string(),utxoDetails:Ki}),ys=c.object({listingPrice:c.string(),listingPriceUiAmount:c.string(),isNative:c.boolean(),url:c.string()}),Ss=c.union([fs,ds,bs]),ro=c.object({id:Ko,chain:Go,name:I,symbol:I,description:I,externalUrl:I,collection:_i,media:ps,uri:I,attributes:c.array(ms).nullish().transform(os),lastSalePrice:ns,balance:I,decimals:I,owner:I,chainData:Ss.optional(),listings:c.record(ys).nullish().transform($)}),gs=_i.extend({items:c.array(ro)}),lc=c.object({items:c.array(gs),isTrimmed:c.boolean()});var hs=c.object({networkID:at}),Tt=hs.extend({type:c.literal("transferSPLToken"),senderAddress:ct,mint:c.string(),amount:c.union([c.instanceof(R),c.string()]),memo:c.string().optional()}),ac=Tt.extend({destination:c.string(),senderAccount:c.string(),decimals:c.number(),references:c.array(c.string()).optional(),programId:c.string()}),cc=Tt.extend({destination:c.string(),compression:no.nullish().transform($).refine(e=>e?.compressed)}),uc=c.object({compression:no.nullish().transform($).refine(e=>e?.compressed),mint:c.string()}),pc=Tt.extend({destination:c.string(),senderAccount:c.string(),standard:c.enum(["ProgrammableNonFungible","ProgrammableNonFungibleEdition"])}),mc=Tt.extend({destination:c.string()}),dc=c.object({root:c.string(),proof:c.array(c.string()),merkleTree:c.string(),dataHash:c.string(),creatorHash:c.string(),leafIndex:c.number(),owner:c.string(),delegate:c.string().optional()}),qi=c.object({networkID:Me}),fc=qi.extend({senderAddress:ct,type:c.literal("transferERC721Evm"),destination:c.string(),amount:c.literal(1),contractAddress:c.string(),tokenID:c.string()}),Cc=qi.extend({senderAddress:ct,type:c.literal("transferERC1155Evm"),destination:c.string(),amount:c.union([c.instanceof(R),c.string()]),contractAddress:c.string(),tokenID:c.string()}),Gi=c.object({networkID:_o}),bc=Gi.extend({type:c.literal("ordinal"),inscriptionId:c.string(),value:c.string(),senderAddress:c.string(),destination:c.string(),amount:c.literal(1),utxoHash:c.string(),utxoIndex:c.number()}),yc=Gi.extend({type:c.literal("raresats"),hash:c.string(),index:c.number(),value:c.string(),senderAddress:c.string(),destination:c.string(),amount:c.literal(1)});l();a();function K(e){return e?.tokenAccount!==void 0}function ne(e){return e?.contract!==void 0}function re(e){return e!==void 0&&"firstCreatedTransactionHash"in e}function At(e){return re(e)&&!!e.utxoDetails}var Qi=e=>e.type==="transferERC721Evm",Wi=e=>e.type==="transferERC1155Evm",ji=e=>"compression"in e&&!!e.compression?.compressed,zi=e=>"standard"in e&&(e.standard==="ProgrammableNonFungible"||e.standard==="ProgrammableNonFungibleEdition"),$i=e=>e.type==="transferSPLToken"&&"destination"in e,Yi=e=>e.type==="ordinal",Xi=e=>e.type==="raresats";l();a();l();a();var so=S(es());l();a();var Zi=new w.PublicKey("NTYeYJ1wr4bpM5xo6zx5En44SvJFAd35zTxxNoERYqd"),vc=new w.PublicKey("M2mx93ekt1fmXSVkTrUL9xVFHkmME8HTUi5Cyc5aF7K"),Ic="https://magiceden.io/terms-of-service.pdf";var He=e=>{for(let t of e.instructions)for(let o of t.keys)if(o.isSigner&&o.pubkey.toBase58()===Zi.toBase58())return!0;return!1},pe=e=>{switch(e){case"mainnet-beta":return 101;case"testnet":return 102;case"devnet":return 103;case"localhost":return 101}},xt=e=>new R(e).multipliedBy(w.LAMPORTS_PER_SOL).toNumber();function wt(e){let t=[2,6],o=ht.safeParse(e);return o.success&&t.includes(o.data)}var lo=so.Buffer.from("metadata"),Ts=so.Buffer.from("edition"),ao=We.toBuffer();function Oc(e){let[t]=w.PublicKey.findProgramAddressSync([lo,ao,e],We);return t}function Ji(e){let[t]=w.PublicKey.findProgramAddressSync([lo,ao,e,Ts],We);return t}function Nc(e){let[t]=w.PublicKey.findProgramAddressSync([lo,ao,e],We);return t}l();a();l();a();var oe=(e,t,o,i)=>{(!t||t>=400&&t<500)&&(D.addBreadcrumb(o,JSON.stringify(i),"info"),D.captureError(e,"collectibles"))};l();a();var Et=e=>{let t=!1;return{collectibles:e.reduce((i,n)=>{let r=ro.safeParse(n);return r.success?i.push(r.data):t=!0,i},[]),containsParseFailure:t}};var As="collectibles",xs=new Error("Failed to fetch collectibles for addresses");async function vt(e){let t="/collectibles/v1";try{let i=(await k.api().post(t,{addresses:e})).data,n=!!i.isTrimmed;if(!i.collectibles)throw xs;let{collectibles:r,containsParseFailure:s}=Et(i.collectibles);return s&&D.captureError(new Error("collectibles failed to parse some items"),"collectibles"),{allCollectibles:r,isTrimmed:n}}catch(o){if(ge(o)){let i=o.response?.status;oe(o,i,As)}throw o}}l();a();var en=new Error("Failed to fetch spl collectible marketplace stats"),co=async(e,t,o)=>{let i=`/solana/nft/v1/mint/${e}/stats`,n=new URLSearchParams;n.set("chainId",t.toString()),o&&n.set("pubkey",o);let r;try{r=await k.api().headers({Accept:"application/json"}).get(`${i}?${n.toString()}`)}catch{throw en}if(!Se(r))throw en;return r.data.stats};l();a();var ws="spl-listings",Es=new Error("Failed to fetch SPL listings"),It=async e=>{let t;try{let o=e.chainId.replace("solana:",""),i=e.address,n=new URLSearchParams;n.append("chainId",o);let r=await k.api().headers({Accept:"application/json"}).get(`/solana/nft/v1/pubkey/${i}/listings?${n.toString()}`);if(t=r.status,!Se(r))throw Es;return r.data.filter(m=>!!m.listings?.magic_eden)}catch(o){let i=o instanceof Error?o:new Error("Non-Error was thrown during fetching of spl listings");throw oe(i,t,ws),i}};var vs=U({seconds:10}),Is=U({seconds:5}),Ps="CollectibleEdition";function Ds(e,t){return O({queryKey:[Ps,{mintAddress:e}],async queryFn(){if(!t)return{};let o=Buffer.from(new w.PublicKey(e).toBytes()),i=Ji(o),{Edition:n}=await Jo();try{let r=await n.fromAccountAddress(t,i),s=ht.safeParse(r.key);if(!s.success)throw new Error(`Invalid MetaplexTokenEdition: ${r.key}`);return{key:s.data,isMasterEdition:wt(r.key)}}catch{return{key:void 0,isMasterEdition:void 0}}}})}var ks="SPLTokenAccountListingStats";function Us(e,t,o){return O({enabled:t!=="",refetchInterval:vs,gcTime:Is,queryKey:[ks,{cluster:e,mint:t,pubkey:o}],async queryFn({queryKey:i}){let[,{mint:n,cluster:r}]=i,s=pe(r);return await co(n,s,o)}})}var Rs=async(e,t,o,i,n=20,r=1e3)=>{try{let s=pe(t);return await Be(async()=>{let m=await co(e,s);if(!m[o])return;let d=(m[o].activities??[]).find(f=>f.signature===i);if(!d)throw new Error("no activity with that signature yet!");return d},()=>!0,n,r)}catch{}},Ls=async(e,t,o,i,n=20,r=1e3)=>{try{let s=pe(o);return await Be(async()=>{let m=await It({address:e,chainId:`solana:${s}`});for(let p of m)if(p.listings?.[i]&&p.asset.mintPubKey===t)throw new Error("still in listings");return m},()=>!0,n,r)}catch{}},Bs=async(e,t,o=20,i=1e3)=>{try{return await Be(async()=>{let{allCollectibles:r}=await vt(e.map(Ct));for(let s of r)if(K(s.chainData)&&s.chainData.mint===t)return s;throw new Error("cant find mint in tokens")},()=>!0,o,i)}catch{}};l();a();var po=S(T());l();a();l();a();var Ms="collectibles",tn=new Error("Failed to fetch collectibles details for addresses");async function on(e){let t="/collectibles/v1/collectibles-details",o;try{let i=await k.api().post(t,{addresses:e});if(o=i.status,!Se(i))throw tn;let n=i.data;if(!n.collectibles)throw tn;let{collectibles:r,containsParseFailure:s}=Et(n.collectibles);return s&&D.captureError(new Error("collectibles failed to parse some items"),"collectibles"),r}catch(i){let n=i instanceof Error?i:new Error("Non-Error was thrown during fetching of collectibles details");throw oe(n,o,Ms),n}}var nn=async e=>{let t=e.find(d=>E.isSolanaNetworkID(d.chainId));if(!t)throw new Error("no solana account");let{chainId:o}=t,i=await Be(()=>It(t),()=>!0),n=i.reduce((d,f)=>({...d,[f.asset.mintPubKey]:f}),{}),r=i.map(d=>({chainId:o,address:d.asset.mintPubKey})),u=Ro(r,100).map(d=>on(d));return(await Promise.all(u)).flat().reduce((d,f)=>{if(!K(f.chainData))return d;let{asset:y,listings:b}=n[f.chainData.mint];return d.push({...f,chainData:{...f.chainData,tokenAccount:y.pubkey},...b?{listings:b}:{}}),d},[])};l();a();l();a();var sn=e=>{let t=[];for(let[o,i]of e){i.sort((r,s)=>{let u=r.name??"",m=s.name??"",p=u.localeCompare(m);if(u.localeCompare(m))return p;let d=rn(r),f=rn(s);return d.localeCompare(f)});let[n]=i;n&&t.push({..."externalUrl"in n?{externalUrl:n.externalUrl}:void 0,...n.collection,items:i})}return t.sort((o,i)=>{if(o.items.length>i.items.length)return-1;if(i.items.length>o.items.length)return 1;let n=o.name??o.id??"",r=i.name??i.id??"";return n.localeCompare(r)})};function rn(e){let t=e.chainData;return K(t)?t.tokenAccount:ne(t)?t.id:(re(t),e.id)}function ln(e){let t=new Map;for(let o of e){let i=o.collection.id,n=t.get(i)??[];n.push(o),t.set(i,n)}return sn(t)}l();a();var an=(e,t)=>{let o=[],i=new Set,n=[...e,...t];for(let r of n)i.has(r.id)||(o.push(r),i.add(r.id));return o};l();a();l();a();var le="collectibles",uo=[le,"collectibleBids"],x={collections:e=>[le,"collections",{addresses:e.map(Ct)}],hiddenCollections:e=>[le,"hiddenCollections",e],unhiddenCollections:e=>[le,"unhiddenCollections",e],visibilityOverrides:e=>[le,"visibilityOverrides",e],pinnedCollections:e=>[le,"pinnedCollections",e],optimisticUpdates:()=>[le,"optimisticUpdates"],collectibleBids:e=>[...uo,e],collectibleMintGasEstimation:e=>[le,"mintNFTGasEstimation",e],collectibleSellSteps:e=>[le,"collectibleSellSteps",e],createMintTransaction:e=>[le,"createMintTransaction",e],transferTransaction:(e,t,o)=>[le,"transferTransaction",{transferArgs:e,transactionUnitCost:t,utxoState:o}]};var Os=20;function _e(e,t){let o=x.optimisticUpdates(),i=e.getQueryData(o)??[];e.setQueryData(o,[...i,t])}async function cn(e,t){let o=x.optimisticUpdates(),i=await e.getQueryState(o);if(i){let n=t??Date.now(),r=i.dataUpdatedAt,s=U({seconds:Os});return n-r>s?(e.removeQueries({queryKey:o}),[]):i?.data??[]}else return[]}function un(e,t){let o=t.reduce((i,n)=>{let{id:r,amount:s}=n,u=new R(s);return i[r]=(i[r]??new R(0)).plus(u),i},{});for(let i of e)i.items=i.items.reduce((n,r)=>{let s=r.id,u=o[s];if(u){let m=new R(r.balance??0);return m.minus(u).isGreaterThan(0)?n.concat({...r,balance:m.minus(u).toString()}):n}else return n.concat(r)},[]);return e=e.filter(i=>i.items.length>0),e}l();a();var ae=(n=>(n[n.Always=1/0]="Always",n[n.Short=U({days:1})]="Short",n[n.Medium=U({days:3})]="Medium",n[n.Long=U({days:7})]="Long",n))(ae||{}),me=(r=>(r[r.Immediate=0]="Immediate",r[r.Short=U({minutes:1})]="Short",r[r.Medium=U({minutes:2.5})]="Medium",r[r.Long=U({minutes:5})]="Long",r[r.Never=1/0]="Never",r))(me||{}),Ze=(u=>(u[u.Shortest=U({seconds:10})]="Shortest",u[u.Shorter=U({seconds:30})]="Shorter",u[u.Short=U({minutes:1})]="Short",u[u.Medium=U({minutes:2.5})]="Medium",u[u.Long=U({minutes:5})]="Long",u[u.Longer=U({minutes:7.5})]="Longer",u[u.Longest=U({minutes:10})]="Longest",u))(Ze||{});var pn=({addresses:e,queryOptions:t={staleTime:me.Short,refetchInterval:Ze.Long},select:o})=>{let i=F(),n=x.collections(e);return O({enabled:e.length!==0&&e.every(r=>!E.isLocalNetworkID(r.networkID)),queryKey:n,gcTime:ae.Medium,staleTime:t.staleTime,refetchInterval:t.refetchInterval,async queryFn({queryKey:[,,{addresses:r}]}){let[s,u]=await Promise.allSettled([nn(r),vt(r)]);if(u.status==="rejected")throw new Error("failed to load unlisted collections");let{allCollectibles:m,isTrimmed:p}=u.status==="fulfilled"?u.value:{allCollectibles:[],isTrimmed:!1},d=s.status==="fulfilled"?s.value:[],f=an(d,m),y=ln(f),b=await cn(i),g=un(y,b);return{items:g,nrOfItems:g.reduce((h,A)=>h+A.items.length,0),isTrimmed:p}},select:o})},ve=e=>pn({...e,select:(0,po.useCallback)(t=>t.items,[])}),Ns=e=>pn({...e,select:(0,po.useCallback)(o=>o.isTrimmed,[])}).data??!1;l();a();l();a();var mo=class extends te{constructor(t){super(t),this.name="GetPinnedCollectionsError"}};async function Pt(e){try{return await e.get("pinnedCollections")}catch{throw new mo("")}}var fo=class extends te{constructor(t){super(t),this.name="SetPinnedCollectionsError"}};async function Co(e,t){try{return await e.set("pinnedCollections",t)}catch{throw new fo("")}}var Dt=e=>{let t=N();return O({enabled:e.length!==0,queryKey:x.pinnedCollections(e),gcTime:ae.Long,staleTime:1/0,refetchInterval:!1,refetchOnMount:!0,async queryFn(){try{return(await Pt(t)??{})[e]||[]}catch(o){return o instanceof Error&&D.captureError(o,"collectibles"),[]}}})};l();a();var mn=S(T());var Vs=({id:e,addresses:t})=>{let{data:o,isSuccess:i}=ve({addresses:t});return(0,mn.useMemo)(()=>{let r={isSuccess:i,data:void 0};for(let s of o??[])for(let u of s.items)if(u.id===e)return{...r,data:u};return r},[e,o,i])};l();a();var W=S(T());l();a();function Y(e){let t=N();return O({gcTime:ae.Long,staleTime:1/0,enabled:!!e,queryKey:x.visibilityOverrides(e??""),async queryFn(){return e?await fe(t,e):new Map}})}l();a();var Pe=S(T());l();a();function Ie(){let e=N(),t=F(),o=H();return ie({async mutationFn({accountId:i,mutations:n}){await $e(e,o,{accountId:i,mutations:n})},async onSuccess(i,{accountId:n}){await t.invalidateQueries({queryKey:x.visibilityOverrides(n)}),await Promise.allSettled([t.invalidateQueries({queryKey:x.hiddenCollections(n)}),t.invalidateQueries({queryKey:x.unhiddenCollections(n)})])}})}async function Ut({collectible:e,visibilityOverrides:t,setVisibilityOverrides:o,accountId:i,desiredVisibility:n,onSuccess:r,onError:s}){if(!t)return;let u=e.collection.id||e.id,m=t.get(u)?.status;try{let p=fn(n,e.collection);await o({accountId:i,mutations:[{id:u,visibility:p}]}),await r({collectible:e,desiredVisibility:n,previousVisibility:m,updatedVisibility:p})}catch(p){await s(p,{collectible:e,desiredVisibility:n})}}async function dn({collection:e,visibilityOverrides:t,setVisibilityOverrides:o,accountId:i,desiredVisibility:n,onSuccess:r,onError:s}){if(!t)return;let u=e.id,m=t.get(u)?.status;try{let p=fn(n,e);await o({accountId:i,mutations:[{id:u,visibility:p}]}),r&&await r({collection:e,desiredVisibility:n,previousVisibility:m,updatedVisibility:p})}catch(p){s&&await s(p,{collection:e,desiredVisibility:n})}}function fn(e,t){let{spamStatus:o}=t;switch(e){case"hidden:reported_spam":if(o==="NOT_VERIFIED"||o==="VERIFIED")return"hidden:reported_spam";if(o==="POSSIBLE_SPAM"||o==="SPAM")return null;throw new Error(`Unexpected spam status: ${o} found while marking spam.`);case"visible:reported_notSpam":if(o==="NOT_VERIFIED"||o==="VERIFIED")return null;if(o==="POSSIBLE_SPAM"||o==="SPAM")return"visible:reported_notSpam";throw new Error(`Unexpected spam status: ${o} found while marking not spam.`);case"hidden":if(o==="NOT_VERIFIED"||o==="VERIFIED")return"hidden";if(o==="POSSIBLE_SPAM"||o==="SPAM")return null;throw new Error(`Unexpected spam status: ${o} found while marking hidden.`);case"visible":if(o==="NOT_VERIFIED"||o==="VERIFIED")return null;if(o==="POSSIBLE_SPAM"||o==="SPAM")return"visible";throw new Error(`Unexpected spam status: ${o} found while marking visible.`)}throw new Error(`Unexpected desired visibility: ${e} found while calculating new visibility.`)}function he(e){return{networkId:e.chain.id,chainId:E.getChainID(e.chain.id),collectionId:e.collection.id,collectibleId:e.id,collectibleName:e.name,collectibleDescription:e.description,collectionName:e.collection.name,collectionDescription:e.collection.description,collectionUrl:e.collection.externalUrl}}function Cn({accountId:e,onError:t,onSuccess:o}){let{data:i}=Y(e),{mutateAsync:n}=Ie();return(0,Pe.useCallback)(async r=>{await Ut({collectible:r,visibilityOverrides:i,setVisibilityOverrides:n,accountId:e,desiredVisibility:"hidden:reported_spam",onSuccess:o,onError:t})},[i,n,e,t,o])}function bn({accountId:e,onError:t,onSuccess:o}){let{data:i}=Y(e),{mutateAsync:n}=Ie();return(0,Pe.useCallback)(async r=>{await Ut({collectible:r,visibilityOverrides:i,setVisibilityOverrides:n,accountId:e,desiredVisibility:"visible:reported_notSpam",onSuccess:o,onError:t})},[i,n,e,t,o])}function yn({accountId:e,onError:t,onSuccess:o}){let{data:i}=Y(e),{mutateAsync:n}=Ie();return(0,Pe.useCallback)(async r=>{await Ut({collectible:r,visibilityOverrides:i,setVisibilityOverrides:n,accountId:e,desiredVisibility:"hidden",onSuccess:o,onError:t})},[i,n,e,t,o])}function Sn({accountId:e,onError:t,onSuccess:o}){let{data:i}=Y(e),{mutateAsync:n}=Ie();return(0,Pe.useCallback)(async r=>{await Ut({collectible:r,visibilityOverrides:i,setVisibilityOverrides:n,accountId:e,desiredVisibility:"visible",onSuccess:o,onError:t})},[i,n,e,t,o])}function gn({accountId:e,onError:t,onSuccess:o}){let{data:i}=Y(e),{mutateAsync:n}=Ie();return(0,Pe.useCallback)(async r=>{await dn({collection:r,visibilityOverrides:i,setVisibilityOverrides:n,accountId:e,desiredVisibility:"hidden",onSuccess:o,onError:t})},[i,n,e,t,o])}function hn({accountId:e,onError:t,onSuccess:o}){let{data:i}=Y(e),{mutateAsync:n}=Ie();return(0,Pe.useCallback)(async r=>{await dn({collection:r,visibilityOverrides:i,setVisibilityOverrides:n,accountId:e,desiredVisibility:"visible",onSuccess:o,onError:t})},[i,n,e,t,o])}l();a();var bo=S(T());var Tn=({toast:e,accountId:t})=>{let{t:o}=L(),i=H(),n=(0,bo.useCallback)(({collectible:s,updatedVisibility:u})=>{u!==null&&i.capture("collectionHidden",{data:he(s)}),e&&e.success(o("collectionHideSuccess"))},[o,e,i]),r=(0,bo.useCallback)((s,u)=>{e&&e.error(o("collectionHideFail"))},[o,e]);return yn({accountId:t,onSuccess:n,onError:r})};l();a();var yo=S(T());var An=({toast:e,accountId:t})=>{let{t:o}=L(),i=H(),n=(0,yo.useCallback)(({collectible:s,updatedVisibility:u})=>{u!==null&&i.capture("collectionUnhidden",{data:he(s)}),e&&e.success(o("collectionUnhideSuccess"))},[o,e,i]),r=(0,yo.useCallback)((s,u)=>{e&&e.error(o("collectionUnhideFail"))},[o,e]);return Sn({accountId:t,onSuccess:n,onError:r})};function Fs({accountId:e,collectible:t,toast:o}){let i=Tn({accountId:e,toast:o}),n=(0,W.useCallback)(()=>{if(t)return i(t)},[i,t]),r=An({accountId:e,toast:o}),s=(0,W.useCallback)(()=>{if(t)return r(t)},[r,t]),{data:u}=Y(e??""),m=(0,W.useMemo)(()=>u?.get(t?.collection.id??t?.id??""),[u,t]),p=(0,W.useMemo)(()=>t?gt(m,{spamStatus:t.collection.spamStatus}):!1,[t,m]),d=(0,W.useCallback)(()=>p?s():n(),[p,n,s]);return(0,W.useMemo)(()=>({isHidden:p,onHideCollectible:n,onUnhideCollectible:s,onToggleHide:d}),[n,d,s,p])}function Hs(e){let{data:t,isPending:o}=Y(e),i=(0,W.useCallback)(s=>{if(!s)return!1;let u=s.collection.id||s.id;return to(t,{spamStatus:s.collection.spamStatus,key:u})},[t]),n=(0,W.useCallback)(s=>{if(!s)return"NOT_VERIFIED";let u=s.collection.id||s.id;return eo(t,{spamStatus:s.collection.spamStatus,key:u})},[t]),r=(0,W.useCallback)(s=>Ye(n(s)),[n]);return(0,W.useMemo)(()=>({getIsHidden:i,getSpamStatus:n,getIsSpam:r,isLoading:o}),[i,n,r,o])}function xn(e){let{data:t,isPending:o}=Y(e),i=(0,W.useCallback)(s=>{if(!s)return!1;let u=s.id;return to(t,{spamStatus:s.spamStatus,key:u})},[t]),n=(0,W.useCallback)(s=>{if(!s)return"NOT_VERIFIED";let u=s.id;return eo(t,{spamStatus:s.spamStatus,key:u})},[t]),r=(0,W.useCallback)(s=>Ye(n(s)),[n]);return(0,W.useMemo)(()=>({getIsHidden:i,getSpamStatus:n,getIsSpam:r,isLoading:o}),[i,n,r,o])}function wn(e){let t=xn(e),o=gn({accountId:e}),i=hn({accountId:e});return(0,W.useMemo)(()=>({...t,hideCollection:o,unhideCollection:i}),[t,o,i])}l();a();var En=S(T());var _s=({id:e,addresses:t})=>{let{data:o}=ve({addresses:t});return(0,En.useMemo)(()=>(o??[]).find(i=>i.id===e),[e,o])};l();a();var vn=S(T()),Rt=(e,t)=>(0,vn.useMemo)(()=>t?e.includes(t):!1,[t,e]);l();a();var Nm=e=>`${e/100}%`,In=(e,t)=>e*t/100/100;l();a();var Rn=S(Xt()),De=S(T());l();a();var Ks=c.object({caip19:c.string(),name:c.string(),symbol:c.string()}),Dn=c.object({bps:c.number(),kind:c.union([c.literal("marketplace"),c.literal("royalty")]),recipient:c.string().optional()}),qs=c.object({amount:c.number(),feeBreakdown:c.array(Dn),netAmount:c.number(),token:Ks,totalFeeBps:c.number()}),Gs=c.object({name:c.string(),logo:c.string(),url:c.string().optional()}),kn=c.object({id:c.string(),expiration:c.string().optional(),price:qs,source:Gs}),Qs=c.object({bids:c.array(kn)}),Pn=new te("Failed to fetch collectible bids."),Ws="collectible-instant-sell-bids",js=async e=>{if(!e.address)throw Pn;try{let t=await k.api().validateStatus(!0).post("/collectibles/v1/bids",{asset:e}),{bids:o}=Qs.parse(t.data);return o}catch(t){if(ge(t)){let o=t.response?.status;oe(t,o,Ws)}throw Pn}},So=({bidAsset:e,isEnabled:t})=>{let o=t&&e.address!==null&&e.chainId!==null;return O({enabled:o,staleTime:me.Short,refetchInterval:Ze.Shortest,queryKey:x.collectibleBids(e),async queryFn(){return js(e)}})},Lt=e=>{let t=e?.chainData,o=e?.chain.id??null,i=ne(t),n=K(t),r=i?{tokenId:t.id}:{},s=n?{contractType:t.standard}:{},u=i?t.contract.toLowerCase():n?t.mint:null;return{chainId:o,address:u,resourceType:"address",...r,...s}};l();a();var zs=S(T());var _=c.enum(["auth","nft-approval","sale","order-signature","refetch","blur-swap","update-proof"]),$s=c.enum(["/execute/auth-signature/v1","/order/v3"]),de=c.enum(["transaction","signature","post","refetch"]),Ys=c.enum(["eip712","eip191"]),Xs=c.object({domain:c.any(),types:c.any(),value:c.any()}),Zs=c.object({message:c.string()}),Js=c.object({from:c.string(),to:c.string(),data:c.string()}),el=c.object({endpoint:$s,body:c.object({id:c.string(),kind:c.string()})}),tl=c.object({action:_,chainId:Me,type:c.literal(de.enum.signature),data:c.object({signatureKind:Ys,signatureData:c.union([Zs,Xs]),postData:el})}),ol=c.object({chainId:Me,action:_,data:Js,type:c.literal(de.enum.transaction)}),il=c.object({chainId:Me,action:_,type:c.literal(de.enum.refetch),data:c.object({resource:c.string()})}),nl=c.union([tl,ol,il]),rl=c.object({action:_,chainId:at,type:c.literal(de.enum.transaction),data:c.object({transaction:c.string()})}),sl=c.union([nl,rl]),ll=c.object({steps:c.array(sl),orderId:c.string().optional()}),al=new te("Failed to fetch collectible sell steps."),cl="collectible-instant-sell-steps",ul=async e=>{try{let t=await k.api().validateStatus(!0).post("/collectibles/v1/sell",e);return ll.parse(t.data)}catch(t){if(ge(t)){let o=t.response?.status;if(oe(t,o,cl),t.response?.data?.code){let{code:i}=t.response.data;throw{code:i}}if(o)throw t}throw al}},go=({sellAsset:e})=>{let t=e.details.orderId!==void 0;return O({enabled:t,queryKey:x.collectibleSellSteps(e),staleTime:1/0,refetchOnWindowFocus:!1,refetchOnReconnect:!1,refetchOnMount:!1,retry:(o,i)=>i.code?!1:o<3,async queryFn(){return ul(e)}})};var Un=(e,t,o)=>{let i=o?.chainData,n=ne(i),r=K(i),s=o?.owner,u=n?s?.toLowerCase():s,m=t?.id,p=t?.source?.name,d=r?{minPrice:t?.price?.netAmount,tokenAccount:i.tokenAccount}:{};return{details:{asset:e,owner:u,orderId:m,marketplace:p,...d}}};l();a();xe.config({EXPONENTIAL_AT:1e3,DECIMAL_PLACES:78});var Ke=(e,t)=>new xe(e).div(rt(t)).toNumber();var pl={steps:[],orderId:""},ml=({collectible:e})=>{let{t}=L(),o=(0,De.useMemo)(()=>Lt(e),[e]),i=(0,De.useMemo)(()=>Ln(e),[e]),{data:n=[],isPending:r,isError:s}=So({bidAsset:o,isEnabled:!i}),u=(0,De.useMemo)(()=>n.length>0?n[0]:void 0,[n]),m=(0,De.useMemo)(()=>Un(o,u,e),[o,u,e]),{data:p=pl,isPending:d,isError:f,refetch:y,error:b}=go({sellAsset:m}),g=(0,De.useMemo)(()=>u&&e?Bn({t,bid:u,collectible:e}):void 0,[t,u,e]),h=(0,De.useMemo)(()=>f?Number.isInteger(b.code):!1,[f,b]);return{bid:u,bidSummary:g,isLoadingBids:r,isErrorBids:s,sellSteps:p,isLoadingSellSteps:d,isErrorSellSteps:f,refetchSellSteps:y,sellStepsError:b,isSellErrorCode:h,isListedOnSolana:i}},dl=({lastSale:e,receiveAmount:t})=>e!==void 0?t-e:t,Ln=e=>{if(!e)return!1;let{listings:t,chainData:o}=e,n=(t?Object.keys(t):[]).length>0,r=o?K(o):!1;return n&&r},fl=({offerAmount:e,collectionFloor:t})=>e-t,Cl=e=>{let t=Math.abs(e)<.001?"0.0000%":"0.00%",o=(0,Rn.default)(e).format(t);return`${e>0?"+":""}${o}`},bl=e=>{if(e){let{price:t,token:o}=e,i=o?.decimals;if(i)return Ke(t,i)}},yl=e=>{if(e){let{price:t,token:o}=e,i=o?.decimals;if(i)return Ke(t,i)}},Sl=e=>{if(!e||!e.previews)return;let{previews:t}=e;if(t.small)return t.small;if(t.medium)return t.medium;if(t.large)return t.large};var Bn=({bid:e,collectible:t,t:o})=>{let{lastSalePrice:i,media:n,name:r,collection:s}=t,{name:u,logo:m}=e.source,{amount:p,netAmount:d,feeBreakdown:f,token:y,totalFeeBps:b}=e.price,{symbol:g,caip19:h}=y,A=0,P=d,j=s.id,M=t.id,G=yl(i),X=g.slice(0,6),v=t.chain.symbol,ue=bl(s.floorPrice),B=ue?fl({offerAmount:p,collectionFloor:ue}):void 0,Q=B?Cl(B):void 0;return{bidTokenSymbol:X,caip19:h,collectionId:j,collectibleId:M,orderId:e.id,collectibleChainId:t.chain.id,collectibleChainSymbol:v,collectionFloor:ue,collectibleImage:Sl(n),collectibleName:r??o("collectiblesUnknownCollectible"),collectionName:s.name??o("collectiblesUnknownCollection"),currencySymbol:e.source.name==="Blur"?v:X,fees:f,floorDifference:B,floorDifferencePercent:Q,lastSale:G,marketplace:u,marketplaceLogoURL:m,offerAmount:p,offerAmountFormatted:st(p),phantomFeePercentage:A,pnl:dl({lastSale:G,receiveAmount:P}),receiveAmount:P,receiveAmountFormatted:st(P),totalFeeBps:b}},gl=({gasFee:e,totalFeeBps:t,offerAmount:o})=>{let i=In(t,o)+e.toNumber();return{raw:i,formatted:st(i)}};l();a();var Mn=new Map([[_.enum["nft-approval"],8e4],[_.enum["blur-swap"],42e3],[_.enum.sale,39e4],["default",42e4]]),Tl=(e,t,o,i)=>{let n=e==="Blur",r=o.filter(h=>h.type==="transaction").map(({action:h})=>({action:h}));if(r.some(({action:h})=>h===_.enum["nft-approval"])||(r=[...r,{action:_.enum["nft-approval"]}]),n){let h=r.some(({action:P})=>P===_.enum.sale),A=r.some(({action:P})=>P===_.enum["blur-swap"]);h||(r=[...r,{action:_.enum.sale}]),A||(r=[...r,{action:_.enum["blur-swap"]}])}let u=r?.reduce((h,{action:A})=>{let P=Mn.get(A)??Mn.get("default");return h+(P??0)},0),m=new R(u),{status:p,data:d}=Ii({gasLimit:m,networkID:t,transactionSpeed:"standard"}),{data:f}=Di(t,d),y=si(d),b=Number(y?.replace(/[^\d.]/g,"")??0),g=i?i.isGreaterThan(b):!1;return{gasEvm:b,gasEstimation:d,gasEstimationStatus:p,ethFeeUI:y,gasEstimationPrice:f,hasEnoughGas:g}};l();a();var Ce=S(T());l();a();var Bt=(e,t)=>{e.invalidateQueries({queryKey:uo}),e.invalidateQueries({predicate:({queryKey:o})=>o.includes("collectibleSellSteps")&&o[2].details.orderId===t?.orderId})};var El=()=>{let{data:e}=Ve();return(0,Ce.useMemo)(()=>{let o=e?.type,i=e?.name??"",n=e?.addresses??[],r=e?.identifier??"";return{isLedger:e?.type==="ledger",accountName:i,accountType:o,chainAddresses:n,accountIdentifier:r}},[e])},vl=({networkID:e,unsignedTransaction:t,pendingActivityRows:o,accountIdentifier:i,accountSigner:n,storage:r,includeCallPayload:s})=>({accountSigner:n,accountIdentifier:i,networkID:e,unsignedTransaction:t,...s&&{callPayload:{from:t.from,to:t.to,data:t.data,value:t.value}},pendingTransactionInput:{ownerAddress:t.from,networkID:e,data:{nonce:"",unsignedTransaction:t,hash:""},type:"sell",display:{summary:o}},storage:r}),Il=async(e,t,o,i,n,r,s)=>{try{if(!o)throw new Error("Gas estimation is required");if(!s)throw new Error("Bid summary is required");let{data:u,chainId:m,action:p}=t,{collectionName:d,marketplace:f,receiveAmount:y,currencySymbol:b,collectibleChainSymbol:g,bidTokenSymbol:h}=s,A=nt(f),P={};p==="nft-approval"&&(P={topRight:{text:""},bottomRight:{text:""},topLeft:{text:e("collectiblesApproveCollection",{collectionName:d})},bottomLeft:{text:A}}),p==="sale"&&(P={topRight:{text:`+${y} ${b}`},topLeft:{text:e("collectiblesSoldCollectible")},bottomRight:{text:`-${d}`},bottomLeft:{text:A}}),p==="blur-swap"&&(P={topRight:{text:`+${y} ${g}`},topLeft:{text:e("transactionsSwapped")},bottomRight:{text:`-${y} ${h}`},bottomLeft:{text:A}});let j=Re.parse({type:"0x2",to:u.to,from:u.from,data:u.data,chainId:E.getEVMNetworkIDValue(m),gas:`0x${o.gasLimit.toString(16)}`,maxFeePerGas:`0x${o.maxFeePerGas.toString(16)}`,maxPriorityFeePerGas:`0x${o.maxPriorityFeePerGas.toString(16)}`}),M=vl({storage:i,accountSigner:n,pendingActivityRows:P,includeCallPayload:!1,networkID:m,unsignedTransaction:j,accountIdentifier:r.accountIdentifier}),G=await mi(M);return{type:t.type,data:G}}catch(u){return{type:t.type,error:u}}},Pl=async(e,t,o)=>{try{let{chainId:i,data:n}=e,{signatureData:r,signatureKind:s,postData:u}=n;if(s==="eip191"&&"message"in r){let m=await t.sign(o.accountIdentifier,{signingType:"message",chainType:"eip155",message:r.message});if(m.status==="success")return await k.api().post("/collectibles/v1/submit-signature",{chainId:i,signature:m.signature,postData:u}),{type:e.type,data:"success"};throw new Error("Execute Sell: Couldn't sign message")}throw new Error("Execute Sell: EIP721 is not yet supported")}catch(i){return{type:e.type,error:i}}},Dl=async(e,t)=>{try{let{resource:o}=e.data;if(o==="/sell"){let{data:i}=await t();return{type:e.type,data:i?.steps}}throw new Error("Execute Sell: Resource is not supported")}catch(o){return{type:e.type,error:o}}},kl=[_.enum.auth,_.enum["nft-approval"],_.enum.sale],Ul="Steps are required",Rl="These steps are not from the same bid",Mt=class extends Error{constructor(){super(...arguments);this.message=Ul}},Ot=class extends Error{constructor(){super(...arguments);this.message=Rl}},Ll=(e,t,o,i,n,r,s)=>{let u=N(),m=H(),p=F(),d=Ne(),[f,y]=(0,Ce.useState)("idle"),[b,g]=(0,Ce.useState)(null),[h,A]=(0,Ce.useState)(null),{t:P}=L(),j=El(),{chainAddresses:M,isLedger:G}=j;(0,Ce.useEffect)(function(){f==="loading"&&Bt(p,n)},[f,p,n]),(0,Ce.useEffect)(function(){f==="success"&&n&&_e(p,{id:n.collectibleId,amount:1})},[f,p,M,n]);let X=()=>{y("idle"),g(null),A(null)},v=s&&n?.receiveAmount?n.receiveAmount*s:void 0,ue=async(B,Q=new Map)=>{let V=B??t;try{if(e&&e!==""&&e!==n?.orderId)throw new Ot;if(!Array.isArray(V)||V.length===0)throw new Mt;y("loading");for await(let z of V){let{action:J}=z,Yr=Q.get(J)==="success",Xr=J===_.enum.sale,Po=J===_.enum.refetch,Zr=kl.some(Ue=>Ue===J);if(Yr)continue;Po||Q.set(J,"loading");let Jr=async Ue=>{switch(Ue.type){case de.enum.transaction:return await Il(P,Ue,o,u,d,j,n);case de.enum.signature:return await Pl(Ue,d,j);case de.enum.refetch:return await Dl(Ue,i)}},{type:Do,data:$t,error:ko}=await Jr(z);if(Zr&&ko)throw ko;if($t&&(Po||Q.set(J,"success"),Do===de.enum.transaction&&Xr&&A($t),Do===de.enum.refetch))return await Mo(500),await ue($t,Q)}y("success"),m.capture("collectibleSellSuccess",{data:{bidSummary:n,usdSaleValue:v}})}catch(z){let J=z?.message??"";(!(z instanceof Mt)||!(z instanceof Ot))&&(D.captureError(z,"collectibles"),m.capture("collectibleSellFailure",{data:{bidSummary:n,usdSaleValue:v,errorMessage:J}})),y("error"),g(z)}};return{executeSellEvmSteps:ue,reset:X,error:b,result:h,status:f,isLedger:G}};l();a();var ce=S(T());var Bl=()=>{let{data:e}=Ve();return(0,ce.useMemo)(()=>{let o=e?.identifier??"",i=e?.type==="ledger",n=e?.addresses??[],r=n.find(qo),s=r?.networkID,u=r?.address??"",m=ci(s),p=ai(m);return{accountIdentifier:o,chainAddresses:n,chainId:s,solanaPublicKey:u,isLedger:i,cluster:m,connection:p}},[e])},Ml=(e,t)=>{let[o,i]=(0,ce.useState)(void 0);return(0,ce.useEffect)(()=>{(async()=>{if(!e)return;let s=e.find(d=>d.type==="transaction")?.data?.transaction;if(!s)return;let u=Yt(s,"base64"),m=await yi(t,[u.message]),p=Uo(m.value);i(p)})()},[e,t]),o},Ol=async(e,t,o,i,n,r)=>{if(!r)throw new Error("Bid summary is required");let{collectionName:s,marketplace:u,receiveAmount:m,currencySymbol:p}=r,d={topRight:{text:`+${m} ${p}`},topLeft:{text:e("collectiblesSoldCollectible")},bottomRight:{text:`-${s}`},bottomLeft:{text:nt(u)}},{data:f,chainId:y,type:b}=t,{transaction:g}=f,h=Yt(g,"base64"),A={ownerAddress:o.solanaPublicKey,networkID:y,data:{signature:""},type:"sell",display:{summary:d}},{accountIdentifier:P,solanaPublicKey:j,connection:M}=o,G=new w.PublicKey(j),X=await gi({accountIdentifier:P,feePayer:G,transaction:h,pendingTransactionInput:A,connection:M,accountSigner:i,storage:n,opts:{skipPreflight:!0}});return{type:b,data:X}},Nl="Steps are required",Vl="These steps are not from the same bid",Nt=class extends Error{constructor(){super(...arguments);this.message=Nl}},Vt=class extends Error{constructor(){super(...arguments);this.message=Vl}},Fl=(e,t,o,i)=>{let n=N(),r=H(),s=F(),u=Ne(),[m,p]=(0,ce.useState)("idle"),[d,f]=(0,ce.useState)(null),[y,b]=(0,ce.useState)(null),{t:g}=L(),h=Bl(),{chainId:A,connection:P,chainAddresses:j,isLedger:M}=h,G=A&&E.isSolanaNetworkID(A)?A:Oe.Solana.Mainnet,X=Ml(t,G);(0,ce.useEffect)(function(){m==="loading"&&Bt(s,o)},[m,s,o]),(0,ce.useEffect)(function(){m==="success"&&o&&_e(s,{id:o.collectibleId,amount:1})},[m,s,j,o]);let v=()=>{p("idle"),f(null),b(null)},ue=i&&o?.receiveAmount?o.receiveAmount*i:void 0;return{executeSellSolanaSteps:async()=>{try{if(e&&e!==o?.orderId)throw new Vt;if(!Array.isArray(t)||t.length===0)throw new Nt;p("loading");for await(let Q of t){let V=await Ol(g,Q,h,u,n,o);b(V.data),await Si({connection:P,signature:V.data})}p("success"),r.capture("collectibleSellSuccess",{data:{bidSummary:o,usdSaleValue:ue}})}catch(Q){let V=Q?.message??"";(!(Q instanceof Nt)||!(Q instanceof Vt))&&(D.captureError(Q,"collectibles"),r.capture("collectibleSellFailure",{data:{bidSummary:o,usdSaleValue:ue,errorMessage:V}})),p("error"),f(Q)}},reset:v,gas:X,error:d,result:y,status:m,isLedger:M}};l();a();var Hl=.01,_l=e=>{let{fungible:t}=Ni({key:Fi(e.collectibleChainId,void 0)}),o=t?.data.balance;return{hasEnoughGas:o?o.isGreaterThan(Hl):!1}};l();a();var Vn=S(T()),Kl=e=>e.reduce((t,o)=>({...t,[o]:1}),{});function ho({allCollections:e=[],pinnedCollectionIds:t=[],getIsHidden:o,getIsSpam:i}){let n=[],r=[],s=[],u=[],m=Kl(t),p={};for(let f of e)i(f)&&o(f)?u.push(f):o(f)?r.push(f):m[f.id]?p[f.id]=f:i(f)?s.push(f):n.push(f);let d=[];for(let f of t)p[f]&&d.push(p[f]);return{collections:[...d,...n,...s],hiddenCollections:r,spamCollections:u}}var ql=({allCollections:e=[],pinnedCollectionIds:t=[],getIsHidden:o,getIsSpam:i})=>(0,Vn.useMemo)(()=>ho({allCollections:e,pinnedCollectionIds:t,getIsHidden:o,getIsSpam:i}),[e,o,i,t]);l();a();var Ft=S(T()),To={location:0,distance:50,threshold:.3},Df={...To,keys:["name"]},kf={...To,keys:["name"]},Uf={...To,keys:["name"]},Je=(e,t)=>{if(t===void 0)return!1;let o=e.toLowerCase();return t.toLowerCase().split(" ").map(i=>i.startsWith(o)||o.startsWith(i)&&t.toLowerCase().includes(o)).includes(!0)};var Fn=(e,t)=>(0,Ft.useMemo)(()=>({collections:e.filter(i=>Je(t,i.name||i.items[0].name))}),[t,e]),Gl=(e,t,o,i)=>{let n=(0,Ft.useMemo)(()=>e.flatMap(r=>r.items),[e]);return(0,Ft.useMemo)(()=>{let r=e.filter(p=>Je(i,p.name)),s=n.filter(p=>Je(i,p.name)),u=t.filter(p=>Je(i,p.name)),m=o.filter(p=>Je(i,p.name));return{collections:r,collectibles:s,hiddenCollections:u,spamCollections:m}},[i,n,e,t,o])};l();a();var Te=S(T());var Ql=U({seconds:10}),Wl=U({seconds:5});function jl({account:e}){let t=e?.identifier??"",o=e?.addresses??[],{data:i=[],isLoading:n}=ve({addresses:o,queryOptions:{staleTime:Wl,refetchInterval:Ql}}),[r,s]=(0,Te.useState)(""),u=No(r)??"",m=(0,Te.useCallback)(M=>s(typeof M=="string"?M:M.currentTarget.value),[s]),{getIsHidden:p,getIsSpam:d,getSpamStatus:f,hideCollection:y,unhideCollection:b,isLoading:g}=wn(t),h=(0,Te.useMemo)(()=>{let{collections:M,hiddenCollections:G,spamCollections:X}=ho({allCollections:i,getIsHidden:p,getIsSpam:d});return[...M,...G,...X]},[i]),A=Fn(h,u),P=(0,Te.useCallback)(({item:M,status:G})=>G==="show"?b(M):y(M),[b,y]);return{viewState:(0,Te.useMemo)(()=>({accountId:t,listItems:u?A.collections:h,searchQuery:r,debouncedSearchQuery:u,handleSearch:m,getIsHidden:p,getIsSpam:d,getSpamStatus:f,onToggleHidden:P}),[t,A.collections,r,m,h,P,p,d,f,u]),viewStateLoading:g||n}}l();a();var Hn={listCollectible:null,listCollectiblePrice:null,editListCollectiblePrice:null,unlistCollectible:null,transactionURL:null},be=mt(e=>({...Hn,reset:()=>e({...Hn}),setListCollectible:t=>e({listCollectible:t}),setListPrice:t=>e({listCollectiblePrice:t}),setEditListPrice:t=>e({editListCollectiblePrice:t}),setUnlistCollectible:t=>e({unlistCollectible:t}),setTransactionURL:t=>e({transactionURL:t})}));l();a();var q=S(ts());var zl="token-account-min-balances",_n=q.struct([q.blob(32,"mint"),q.blob(32,"owner"),q.nu64("amount"),q.blob(93)]),Yf=q.struct([q.blob(44),q.u8("decimals"),q.blob(37)]),$l=q.struct([q.blob(679)]),Yl=q.struct([q.u8("edition"),q.blob(281)]),Xl=async e=>e.getMinimumBalanceForRentExemption(_n.span).then(R),Zl=async e=>R(await e.getMinimumBalanceForRentExemption($l.span)+await e.getMinimumBalanceForRentExemption(Yl.span)+await e.getMinimumBalanceForRentExemption(_n.span));function Jl({tokenEdition:e,connection:t}){return O({queryKey:[zl,{tokenEdition:e}],queryFn:async()=>t?e===null||!wt(e)?Xl(t):Zl(t):R(0)})}l();a();var Kn=S(T()),ea=(e,t,o=!1,i)=>(0,Kn.useMemo)(()=>{if(e)return Ht({collectibleMedia:e,isImageAnimationEnabled:o,previewSize:i??"medium",mediaTypeOverride:t})},[e,t,o,i]),Ht=({collectibleMedia:e,isImageAnimationEnabled:t,previewSize:o,mediaTypeOverride:i})=>{let n=i??e.type;if((n==="image"||n==="other")&&e.previews?.[o]){let s=t?"":`${o==="medium"?"=":"-"}k`;return`${e.previews[o]}${s}`}return e[n]?.url};l();a();var qn=S(T());var ta=!1,oa="small",ia="image",na=e=>(0,qn.useMemo)(()=>Gn(e),[e]),Gn=(e,t=oa)=>{if(!e)return;let{media:o,collection:i}=e,n=ta,r=ia;return o?Ht({collectibleMedia:o,isImageAnimationEnabled:n,previewSize:t,mediaTypeOverride:r}):i.imageUrl};l();a();var Qn=S(T()),Wn=e=>{let{items:t}=e;if(t.length!==0)return t.find(({media:o})=>o?.type==="image")},ra=e=>(0,Qn.useMemo)(()=>{if(e)return Wn(e)??e.items[0]},[e]);l();a();var jn=S(T());var sa=e=>(0,jn.useMemo)(()=>{if(!(!e||e.items.length===0))return zn(e)},[e]),zn=e=>{let{items:t}=e,{length:o}=t;if(o===1){let[{balance:i="",chainData:n}]=t;if(K(n)&&n.standard==="SemiFungible"){let r=parseInt(i,10);return isNaN(r)?1:r}}return o};l();a();l();a();var $n={burnCollectibleTx:void 0,burnCollectibleTxFee:void 0},Yn=e=>({...$n,resetBurnCollectible:()=>e({...$n}),setBurnCollectibleTx:t=>e({burnCollectibleTx:t}),setBurnCollectibleTxFee:t=>e({burnCollectibleTxFee:t})});l();a();var Xn={multichainSellCollectible:void 0},Zn=e=>({...Xn,resetMultichainSellCollectible:()=>e({...Xn}),setMultichainSellCollectible:t=>e({multichainSellCollectible:t})});l();a();var Jn={listCollectible:null,listCollectiblePrice:null,editListCollectiblePrice:null,unlistCollectible:null,transactionURL:null},er=e=>({...Jn,reset:()=>e({...Jn}),setListCollectible:t=>e({listCollectible:t}),setListPrice:t=>e({listCollectiblePrice:t}),setEditListPrice:t=>e({editListCollectiblePrice:t}),setUnlistCollectible:t=>e({unlistCollectible:t}),setTransactionURL:t=>e({transactionURL:t})});l();a();var tr={multichainListCollectible:null,multichainListCollectiblePrice:null,editMultichainListCollectiblePrice:null,unlistMultichainCollectible:null,multichainTransactionURL:null},or=e=>({...tr,reset:()=>e({...tr}),setMultichainListCollectible:t=>e({multichainListCollectible:t}),setMultichainListPrice:t=>e({multichainListCollectiblePrice:t}),setEditMultichainListPrice:t=>e({editMultichainListCollectiblePrice:t}),setUnlistMultichainCollectible:t=>e({unlistMultichainCollectible:t}),setMultichainTransactionURL:t=>e({multichainTransactionURL:t})});var la=mt((e,t)=>({...er(e,t),...Yn(e,t),...or(e,t),...Zn(e,t)}));l();a();l();a();var aa=500,ir=x.collections([])[1],ca=[ir,x.hiddenCollections("")[1],x.unhiddenCollections("")[1],x.pinnedCollections("")[1]],ua=e=>{let t=String(e.queryKey[1]);return t===ir?e.state.data?.nrOfItems<=aa:ca.includes(t)};l();a();var ur=S(T());l();a();l();a();l();a();var nr="#F7931A",pa=5e4,ma="https://help.magiceden.io/en/articles/8450002-mastering-utxo-management-extracting-splitting-sats-on-magic-eden-bitcoin",rr={sendSuccessCondition:"SUBMISSION",displayFeeTooltip:!0,collectibleDetailPrecision:7,onBeforeSend:e=>e.multichainTransaction,collectibleDetailCtaBanner:(e,t)=>{let o=e.chainData;if(At(o)&&t){let i=new R(o.utxoDetails.value??0);if(i.lte(pa))return;let n=Object.values(o.utxoDetails.satributes).reduce((m,{count:p})=>m+p,0),r=Zt(n,E.bitcoin.tokenDecimals),s=Zt(i,E.bitcoin.tokenDecimals),u=Vo(s.minus(r).times(t));return{title:Z.t("collectibleDetailUtxoSplitterCtaTitle"),subtitle:Z.t("collectibleDetailUtxoSplitterCtaSubtitle",{value:u}),iconFill:nr,modal:{title:Z.t("collectibleDetailUtxoSplitterModalCtaTitle"),subtitle:Z.t("collectibleDetailUtxoSplitterModalCtaSubtitle",{value:u}),buttonText:Z.t("collectibleDetailUtxoSplitterModalCtaButton"),uri:ma,iconFill:nr}}}}};l();a();var et={sendSuccessCondition:"CONFIRMATION",displayFeeTooltip:!0,collectibleDetailPrecision:5,collectibleDetailCtaBanner:void 0,onBeforeSend({multichainTransaction:e,gasEstimation:t}){if(!ri(e))throw new Error("MultichainTransaction is not of type EVM");if(!t||!ni(t))throw new Error("No gas estimation found");return pi(e,t)}};l();a();var sr={sendSuccessCondition:"CONFIRMATION",displayFeeTooltip:!0,collectibleDetailPrecision:5,collectibleDetailCtaBanner:void 0,onBeforeSend:({multichainTransaction:e})=>e};var da=new jo({solana:sr,ethereum:et,polygon:et,base:et,monad:et,bitcoin:rr,sui:{}}),ye=Wo(da);l();a();var cr=S(T());l();a();var lr=S(Xt()),ar=({value:e,type:t})=>{if(!e)return"";try{switch(t){case"number":return(0,lr.default)(e).format("0,0.[00]").toString();case"date":return new Date(Number(e)).toLocaleDateString();default:return`${e}`}}catch{return`${e}`}};var xo=e=>(0,cr.useMemo)(()=>{if(!e)return[];let t=e.chainData;return re(t)&&t.utxoDetails?t.utxoDetails.satributes.map(({count:o,displayName:i,description:n,url:r})=>({type:i.toUpperCase(),value:Z.t(fa(o,"collectibleDetailSatsCount_one","collectibleDetailSatsCount_other"),{count:o})??"",modal:{name:i,description:n,iconUrl:r}})):e.attributes.map(({trait_type:o,value:i,display_type:n})=>({type:o,value:ar({value:i,type:n})}))},[e]),fa=(e,t,o)=>e===1?t:o;function Ca(e){let{chain:t,owner:o}=e||{},i=t?.id,{data:n}=pt({address:o,networkID:i});Ri(n);let r=xo(e),s=i?E.getChainID(i):void 0,u=s?Qo(s):void 0,{data:m}=Pi({query:u?{data:u}:void 0});return{ctaBanner:(0,ur.useMemo)(()=>{if(!(!e||!m||!s))return ye.get(s).collectibleDetailCtaBanner?.(e,m.price)},[s,e,m]),traits:r}}l();a();var Rr=S(T());l();a();function ba(e,t,o){let i=t.collectible;if(!i||!t.sendFormValues)throw new Error("State is missing necessary data");let{decimals:n="0"}=i,r=parseFloat(n),s=i.chainData,{mint:u,standard:m,tokenAccount:p,compression:d}=s,{amountAsset:f}=t.sendFormValues,y={networkID:i.chain.id,addressType:"solana",address:i.owner??""},b=Oi(i);switch(m){case"NonFungible":case"NonFungibleEdition":case"ProgrammableNonFungible":case"ProgrammableNonFungibleEdition":return{type:"transferSPLToken",networkID:e,senderAddress:y,destination:o,amount:"1",senderAccount:p,mint:u,decimals:r,standard:m,compression:d,programId:s.programId,transferHookProgramId:b};case"SemiFungible":return{type:"transferSPLToken",networkID:e,senderAddress:y,destination:o,amount:f.isZero()?"1":f.toString(),senderAccount:p,mint:u,decimals:r,standard:m,programId:s.programId,transferHookProgramId:b};default:throw new Error("Unknown fungible token type")}}function ya(e,t,o){let i=t.collectible;if(!i||!t.sendFormValues)throw new Error("State is missing necessary data");let{chainData:n}=i,{id:r,contract:s,standard:u}=n,{amountAsset:m}=t.sendFormValues,p={networkID:i.chain.id,addressType:"eip155",address:i.owner??""};switch(u){case"ERC721":return{type:"transferERC721Evm",networkID:e,senderAddress:p,destination:o,amount:1,contractAddress:s,tokenID:r};case"ERC1155":return{type:"transferERC1155Evm",networkID:e,senderAddress:p,destination:o,amount:m??"1",contractAddress:s,tokenID:r};default:throw new Error("Unknown fungible token type")}}function Sa(e,t,o){let i=t.collectible;if(!i||!i.owner||!t.sendFormValues)throw new Error("State is missing necessary data");let{chainData:n}=i,{firstCreatedInscriptionId:r,utxoDetails:s,outputValue:u,utxoHash:m,utxoIndex:p}=n,d;if(s)d={type:"raresats",networkID:e,hash:s.hash,index:s.index,value:s.value.toString(),senderAddress:i.owner,destination:o,amount:1};else if(m&&p!==void 0)d={type:"ordinal",networkID:e,inscriptionId:r,value:u,amount:1,senderAddress:i.owner,destination:o,utxoHash:m,utxoIndex:p};else throw new Error(`Unable to determine transfer type for collectible: ${i.id}`);return d}function pr(e,t){let o=e?.collectible?.chain.id;if(o){if(E.isBitcoinNetworkID(o))return Sa(o,e,t);if(E.isSolanaNetworkID(o))return ba(o,e,t);if(E.isEVMNetworkID(o))return ya(o,e,t);if(E.isSuiNetworkID(o))throw new Ho("create transfer args");return Le(o)}}l();a();l();a();l();a();var mr=S(lt()),dr=S(lt()),fr=S(Fo()),Cr=({value:e,senderAddress:t,destination:o,utxoState:{safeToSendUtxos:i},feerate:n,utxoHash:r,utxoIndex:s})=>{let u={transactionHash:r,index:s,value:e,isSafeToSend:!1,owner:t},m=[u],p=[{address:o,value:parseFloat(u.value),script:(0,fr.toOutputScript)(o)}],d=bt(ut(Oe.Bitcoin.Mainnet,t),i,p,n,m),f=new dr.Psbt({network:mr.networks.bitcoin});if(d.type==="success"){let{inputs:y,outputs:b}=d;f.addInputs(y.map(g=>yt(g,t,!0))),f.addOutputs(b)}return[f,f.data.inputs.map((y,b)=>({address:t,signingIndexes:[b]})),d.vb]};l();a();var br=S(lt()),yr=S(lt()),Sr=S(Fo()),gr=({hash:e,index:t,value:o,senderAddress:i,destination:n,utxoState:{safeToSendUtxos:r},feerate:s})=>{let u={index:t,transactionHash:e,value:o,isSafeToSend:!1,owner:i},m=[u],p=[{address:n,value:parseFloat(u.value),script:(0,Sr.toOutputScript)(n)}],d=bt(ut(Oe.Bitcoin.Mainnet,i),r,p,s,m),f=new yr.Psbt({network:br.networks.bitcoin});if(d.type==="success"){let{inputs:b,outputs:g}=d;f.addInputs(b.map(h=>yt(h,i,!0))),f.addOutputs(g)}let y=f.data.inputs.map((b,g)=>({address:i,signingIndexes:[g]}));return[f,y,d.vb]};function hr(e,t,o){let i,n,r,s=o,u=Math.round(s?oi(s.btcPerKilobyte).toNumber():ti);if(Yi(e))[i,n,r]=Cr({...e,utxoState:t,feerate:u});else if(Xi(e))[i,n,r]=gr({...e,utxoState:t,feerate:u});else return Le(e);return{networkID:e.networkID,psbt:i,vb:r,psbtChain:void 0,inputsToSign:n}}l();a();l();a();var ga=["function safeTransferFrom(address from, address to, uint256 tokenId)","function transferFrom(address from, address to, uint256 tokenId)"],Tr=new Error("Error populating ERC721 transaction data."),Ar=async e=>{let{sender:t,networkID:o,destination:i,contractAddress:n,tokenID:r,ethereumNetworkID:s}=e,u=ft(o,n,ga,t),m;try{let d=await u.populateTransaction.safeTransferFrom(t,i,new R(r).toString(10));if(d.data)m=d.data;else throw Tr}catch{throw Tr}return Re.parse({type:"0x2",from:t,to:n,chainId:typeof s=="number"?s:E.getEVMNetworkIDValue(s),data:m})};l();a();var ha=["function safeTransferFrom(address from, address to, uint256 id, uint256 amount, bytes data)","function transferFrom(address from, address to, uint256 id, uint256 amount, bytes data)"],xr=new Error("Error populating ERC1155 transaction data."),wr=async e=>{let{sender:t,networkID:o,destination:i,contractAddress:n,tokenID:r,value:s,ethereumNetworkID:u}=e,m=ft(o,n,ha,t),p;try{let f=await m.populateTransaction.safeTransferFrom(t,i,new R(r).toString(10),s.toString(10),[]);if(f.data)p=f.data;else throw xr}catch{throw xr}return Re.parse({type:"0x2",from:t,to:n,chainId:typeof u=="number"?u:E.getEVMNetworkIDValue(u),data:p})};async function Er(e){let t;if(Qi(e)){let{networkID:o,senderAddress:i,destination:n,contractAddress:r,tokenID:s}=e;t=await Ar({sender:i.address,networkID:o,destination:n,contractAddress:r,tokenID:s,value:1,ethereumNetworkID:o})}else if(Wi(e)){let{networkID:o,senderAddress:i,destination:n,contractAddress:r,tokenID:s,amount:u}=e;t=await wr({sender:i.address,networkID:o,destination:n,contractAddress:r,tokenID:s,value:typeof u=="string"?new R(u,10):u,ethereumNetworkID:o})}else return Le(e);return{networkID:e.networkID,unsignedTransaction:t}}l();a();l();a();l();a();var _t=class extends Error{constructor(t){super(t),this.name="ProofError"}};async function vr({networkID:e,mintAddress:t}){let o="/collectibles/v1/proof",i;try{i=await k.api().post(o,{chainId:e,address:t})}catch{let r=new _t(`Failed to fetch proof for ${t} on network ${e}.`);throw D.captureError(r,"collectibles"),r}if(i.status!==200){let r=new _t(`Failed to fetch proof for ${t} on network ${e}.`);throw D.captureError(r,"collectibles"),r}return await i.data}async function Ir(e){let{senderAddress:t,mint:o}=e,i=new ee.Transaction,n=await vr({networkID:t.networkID,mintAddress:o});return i.add(await Zo({...e,...n})),i}l();a();async function Pr({networkID:e,destination:t,mint:o,senderAddress:i}){let{createComputeBudgetIx:n,createOCPTransferIX:r,getOCPMintState:s}=await dt(),u=Fe(e),m=new w.PublicKey(o),p=await s(m,u);if(!p)throw new Error("Mint state not found for OCP transfer");it()&&console.log("This is a Magic Eden Open Creator Protocol Token");let d=new ee.Transaction;d.add(n());let{destinationAddress:f}=await St({solanaConnection:u,destination:new w.PublicKey(t),mint:m,programId:je}),y=new w.PublicKey(i.address),{initAccountIX:b,transferIX:g}=await r({senderAddress:y,destinationAddress:f,mintAddress:m,mintState:p,connection:u});return b&&d.add(b),d.add(g),d}l();a();async function Dr({networkID:e,senderAddress:t,senderAccount:o,destination:i,mint:n}){let r=Fe(e),s=new w.PublicKey(n),{destinationAddress:u,destinationAccount:m}=await St({solanaConnection:r,destination:new w.PublicKey(i),mint:s,programId:je}),{createPNFTTransferIX:p}=await dt(),d=new ee.Transaction,f=new w.PublicKey(t.address),y=new w.PublicKey(o),b=m;if(!b){let[g,h]=await fi(f,u,s,je);d.add(di({account:u,owner:w.SystemProgram.programId})),d.add(g),b=h}return d.add(await p({senderAddress:f,tokenAccount:y,destinationAddress:u,destinationAccount:b,mintAddress:s,connection:r})),d}async function kr(e){let{networkID:t,mint:o}=e,i=Fe(t),n;if(ji(e))n=await Ir(e);else if(zi(e))n=await Dr(e);else if($i(e)&&await ei(new w.PublicKey(o),i))n=await Pr({...e});else if(ii(e))n=await bi({...e,connection:i});else throw new Error("Unsupported transferSPLSolana args");let{memo:r}=e;return r&&n.add(Ci({memoText:r})),n.feePayer=new w.PublicKey(e.senderAddress.address),n=await ui(n,{connection:i}),n.recentBlockhash=(await i.getLatestBlockhash("confirmed")).blockhash,{networkID:e.networkID,transaction:[n]}}async function Ur(e,t,o){if(Ta(e))return kr(e);if(Aa(e))return Er(e);if(xa(e)){if(!t)throw new Error("Missing utxo state for Bitcoin transaction.");return hr(e,t,o)}else throw new Error(`Unsupported transaction type: ${JSON.stringify(e)}`)}var Ta=e=>E.isSolanaNetworkID(e.networkID),Aa=e=>E.isEVMNetworkID(e.networkID),xa=e=>E.isBitcoinNetworkID(e.networkID);var wa="collectible-transfer-tx",Ea=U({seconds:30});function va(e){let{data:t}=Ve(),o=Li(e?.sendFormValues?.recipient,e?.collectible?.chain.id)??"",i=(0,Rr.useMemo)(()=>{if(t)return pr(e,o)},[t,e,o]),n=e?.collectible?.owner,{data:r}=pt({address:n,networkID:i?.networkID}),{data:s}=Ui(r),u=e?.sendFormValues.transactionSpeed??"standard",{data:m}=vi({networkID:i?.networkID,enableFallback:!0}),p=m?m[u]:void 0;return O({staleTime:Ea,enabled:Ia(i,s),queryKey:x.transferTransaction(i,p,s),async queryFn(){if(i)try{return await Ur(i,s,p)}catch(d){if(ge(d)){let f=d.response?.status;oe(d,f,wa,{})}throw d}}})}function Ia(e,t){return!e||E.isBitcoinNetworkID(e.networkID)&&!t?!1:!new xe(e.amount).isNaN()}l();a();var Kt=e=>{let t=N();return O({enabled:e.length!==0,queryKey:x.hiddenCollections(e),gcTime:ae.Long,staleTime:1/0,async queryFn(){try{let o=await fe(t,e);return Array.from(o.values()).filter(i=>gt(i,{spamStatus:"NOT_VERIFIED"})).map(({id:i})=>i)}catch(o){return o instanceof Error&&D.captureError(o,"collectibles"),[]}}})};l();a();l();a();var Lr=S(T());var Pa=e=>{let t=F();return(0,Lr.useCallback)(()=>t.invalidateQueries({queryKey:x.collections(e)}),[t,e])};l();a();var Da=2e3,qt=class extends te{constructor(t){super(t),this.name="UsePinCollectionError"}};function Gt(){let e=N(),t=F();return ie({async mutationFn({collectionId:o,accountId:i,pinned:n}){let r=await Pt(e)??{},s=r[i]??[];if(n){if(Object.values(r).flat().length+1>Da)throw new qt("Pinned limit reached");let m=[...new Set([...s,o])],p={...r,[i]:m};await Co(e,p)}else{let u=s.indexOf(o);if(u===-1)throw new qt("Collection not previously pinned");let m=[...s];m.splice(u,1);let p={...r,[i]:m};await Co(e,p)}},async onSuccess(o,i){await t.invalidateQueries({queryKey:x.pinnedCollections(i.accountId)})}})}l();a();var Br=S(T());var ka=e=>{let t=F();return(0,Br.useCallback)(()=>t.refetchQueries({queryKey:x.collections(e)}),[t,e])};l();a();l();a();var Ua=new Error("Failed to report event for collectible");async function Mr(e){try{let t=await k.api().post("/collectibles/v1/events",{events:e});if(!Se(t))throw new Error}catch{throw Ua}}function Or(){return ie({async mutationFn({event:e}){try{await Mr([e])}catch{}}})}l();a();function Ra(){let{data:e}=Xo(),t=N(),o=F(),i=ki(),n=Ne();return ie({mutationFn:async({multichainTransaction:s,pendingTransactionInput:u,senderAddress:m,optimisticUpdate:p,gasEstimation:d})=>{if(!e)throw new Error("No account identifier found");let{networkID:f}=s,y=ye.get(f).onBeforeSend({multichainTransaction:s,gasEstimation:d})??s,b=await Ti({accountIdentifier:e,accountSigner:n,multichainTransaction:y,networkID:f,pendingTransactionInput:u,senderAddress:m,storage:t,utxoManager:i});return p&&_e(o,p),hi(f,b)},onError(s){s instanceof Error&&D.captureError(s,"collectibles")}})}l();a();var Qt=e=>{let t=N();return O({enabled:e.length!==0,queryKey:x.unhiddenCollections(e),gcTime:ae.Long,staleTime:1/0,async queryFn(){try{let o=await fe(t,e);return Array.from(o.values()).filter(i=>Ei(i,{spamStatus:"NOT_VERIFIED"})).map(({id:i})=>i)}catch(o){return o instanceof Error&&D.captureError(o,"collectibles"),[]}}})};l();a();var La=2e3,wo=class extends Error{constructor(t){super(t),this.name="UseUnhideCollectionError"}};function Wt(){let e=N(),t=F(),o=H();return ie({async mutationFn(i){let{collectionId:n,accountId:r}=i,s=await Jt(e);if(!(await fe(e,r)).has(n)&&s>=La)throw new wo("Too many ids unhidden");await $e(e,o,{accountId:r,mutations:[{id:n,visibility:"visible"}]})},async onSuccess(i,n){await t.invalidateQueries({queryKey:x.unhiddenCollections(n.accountId)}),await t.invalidateQueries({queryKey:x.hiddenCollections(n.accountId)})}})}l();a();var ke=S(T());l();a();var Eo=S(T());var Nr=({toast:e,accountId:t})=>{let{t:o}=L(),i=H(),n=(0,Eo.useCallback)(({collectible:s,previousVisibility:u,updatedVisibility:m})=>{m!==null&&i.capture("collectiblesReportAsNotSpam",{data:he(s)}),e.success(u==="visible"?o("collectiblesNotSpamSuccess"):o("collectiblesNotSpamAndUnhiddenSuccess"))},[o,e,i]),r=(0,Eo.useCallback)((s,u)=>{e.error(o("collectiblesNotSpamFail"))},[o,e]);return bn({accountId:t,onSuccess:n,onError:r})};l();a();var vo=S(T());var Vr=({toast:e,accountId:t})=>{let{t:o}=L(),i=H(),n=(0,vo.useCallback)(({collectible:s,previousVisibility:u,updatedVisibility:m})=>{m!==null&&i.capture("collectiblesReportAsSpam",{data:he(s)}),e.success(u==="hidden"?o("collectiblesSpamSuccess"):o("collectiblesSpamAndHiddenSuccess"))},[o,e,i]),r=(0,vo.useCallback)((s,u)=>{e.error(o("collectiblesSpamFail"))},[o,e]);return Cn({accountId:t,onSuccess:n,onError:r})};function Ba({accountId:e,collectible:t,toast:o}){let i=Vr({collectible:t,accountId:e,toast:o}),n=(0,ke.useCallback)(()=>{if(t)return i(t)},[i,t]),r=Nr({collectible:t,accountId:e,toast:o}),s=(0,ke.useCallback)(()=>{if(t)return r(t)},[r,t]),{data:u}=Y(e??""),m=(0,ke.useMemo)(()=>u?.get(t?.collection.id??t?.id??""),[u,t]),p=(0,ke.useMemo)(()=>t?wi(m,{spamStatus:t.collection.spamStatus}):"NOT_VERIFIED",[t,m]),d=Ye(p);return(0,ke.useMemo)(()=>({isSpam:d,spamStatus:p,onReportSpam:d?void 0:n,onReportNotSpam:d?s:void 0}),[p,d,n,s])}l();a();var Fr=S(Xt()),Hr=S(T());l();a();var jt=(e,t)=>{let o=new xe(e);if(o.isZero())return"0";let i=rt(-t);return o.abs().lt(i)?o.isPositive()?`<${i.toFixed()}`:`-<${i.toFixed()}`:parseFloat(o.toString()).toLocaleString("en-US",{maximumFractionDigits:t})};var Ma=(e,t)=>{let{t:o}=L();return(0,Hr.useMemo)(()=>{let n=[];if(!e)return n;let r=e?.chain.id,s=ye.get(r).collectibleDetailPrecision;e.collection.name&&n.push({label:o("collectibleScreenCollectionLabel"),value:e.collection.name});let u,m;if(e.collection.floorPrice){let{price:f,token:y}=e.collection.floorPrice;if(y&&y.decimals&&y.symbol){let{decimals:b,symbol:g}=y,h=Ke(f,b);n.push({label:o("collectiblesFloorPrice"),value:`${jt(h,s)} ${g}`}),u=h,m=g}}let p,d;if(e.lastSalePrice){let{price:f,token:y}=e.lastSalePrice;if(y&&y.decimals&&(y.symbol||y.name)){let{decimals:b,symbol:g,name:h}=y,A=Ke(f,b);n.push({label:o("collectiblesLastSalePrice"),value:`${jt(A,s)} ${g??h}`}),p=A,d=g}}if(u&&p&&m===d){let f=u-p;n.push({label:o("collectiblesTotalReturn"),value:`${jt(f,s)} ${d}`,color:f>0?t?.positiveColor:f===0?void 0:t?.negativeColor})}return e.collection.ownerCount&&n.push({label:o("collectiblesUniqueHolders"),value:(0,Fr.default)(e.collection.ownerCount).format("0,0")}),e.chain.name&&n.push({label:o("sendFungibleSummaryNetwork"),value:E.getNetworkName(e.chain.id)}),it()&&n.push({label:"Standard",value:Oa(e)}),Mi(e)&&n.push({label:o("collectiblesNonTransferable"),value:o("collectiblesNonTransferableYes")}),n},[e,t?.negativeColor,t?.positiveColor,o])};function Oa(e){let t=e.chainData;if(K(t))return t.standard;if(ne(t))return t.standard;if(re(t))return"Ordinals"}l();a();var Kr=S(T());l();a();var Na=S(T());l();a();var Va=S(T());l();a();var _r=S(T()),zt=(e,t,o,i)=>(0,_r.useMemo)(()=>t.includes(o)?!1:e.includes(o)?!0:!!i,[e,t,o,i]);l();a();var tt=S(T());l();a();var qr=S(T()),Io=({accountId:e,collectionId:t,isPinned:o,pinMessages:i,pinCollection:n,toast:r,sendAnalyticEvent:s,unhideCollection:u,isHidden:m})=>(0,qr.useCallback)(()=>{(async()=>{if(!t||!e){r.error(o?i.unpinFail:i.pinFail);return}try{await n({collectionId:t,accountId:e,pinned:!o}),m&&!o&&await u({collectionId:t,accountId:e}),r.success(o?i.unpinSuccess:i.pinSuccess),s&&s(o?"collectionUnpinned":"collectionPinned")}catch{r.error(o?i.unpinFail:i.pinFail)}})()},[t,e,o,i,n,r,s,u,m]);var Ka=({collectionId:e,isSpam:t,accountId:o,networkId:i,toast:n})=>{let{t:r}=L(),s=H(),{mutateAsync:u}=Wt(),{data:m=[]}=Kt(o||""),{data:p=[]}=Qt(o||""),d=zt(m,p,e??"",!!t),{mutateAsync:f}=Gt(),{data:y=[]}=Dt(o||""),b=Rt(y,e??""),g=(0,tt.useMemo)(()=>({pinSuccess:r("collectionPinSuccess"),pinFail:r("collectionPinFail"),unpinSuccess:r("collectionUnpinSuccess"),unpinFail:r("collectionUnpinFail")}),[r]),h=(0,tt.useCallback)(P=>{i&&s.capture(P,{data:{networkId:i,chainId:E.getChainID(i)}})},[s,i]),A=Io({accountId:o,collectionId:e,isPinned:b,pinMessages:g,pinCollection:f,toast:n,sendAnalyticEvent:h,unhideCollection:u,isHidden:d});return(0,tt.useMemo)(()=>({isPinned:b,onTogglePin:A}),[b,A])};l();a();var ot=S(T());var qa=({networkID:e,isTxError:t,isTxConfirmed:o,isTxSubmitted:i})=>{let n=ye.get(e).sendSuccessCondition,r=(0,ot.useMemo)(()=>t?"ERROR":n==="SUBMISSION"&&i||n==="CONFIRMATION"&&o?"SUCCESS":"LOADING",[o,t,i,n]),s=n==="CONFIRMATION"?"sendStatusConfirmedSuccessTitle":"sendStatusSubmittedSuccessTitle",u=(0,ot.useMemo)(()=>{switch(r){case"LOADING":return Z.t("sendStatusLoadingTitle");case"ERROR":return Z.t("sendStatusErrorTitle");case"SUCCESS":return Z.t(s)}},[r,s]),{isError:m,isSuccess:p}=(0,ot.useMemo)(()=>({isError:r==="ERROR",isSuccess:r==="SUCCESS"}),[r]),{transactionSpeed:d="standard"}=Bi(y=>y.sendFormValues),f=(0,ot.useMemo)(()=>{let y=li.get(e).transactionSpeedDescription(d);return y?Z.t(y):""},[e,d]);return{title:u,isError:m,isSuccess:p,sendSuccessCondition:n,estimatedTime:f}};l();a();var qe=S(T());var Ga=()=>{let e=be(p=>p.listCollectible),t=be(p=>p.editListCollectiblePrice),[o,i]=(0,qe.useState)(),[n,r]=(0,qe.useState)(!1),[s,u]=(0,qe.useState)();return{editList:(0,qe.useCallback)(async(p,d)=>{if(i(void 0),r(!0),!e||!t){i(new Error("missing collectible or price")),r(!1);return}try{let f=new URLSearchParams;f.set("pubKey",d.toBase58()),f.set("mint",e.asset.mintPubKey),f.set("account",e.asset.pubkey),f.set("quotePrice",`${xt(t)}`),f.set("marketplace","magic_eden");let y=pe(p);f.set("chainId",y.toString());let b=`/solana/composer/v1/list?${f.toString()}`,h=(await k.api().retry(3,2e3).get(b)).data,A=ee.Transaction.from(Buffer.from(h.transaction,"base64"));He(A)?u(A):i(new Error("Failed to initialize transaction")),r(!1)}catch(f){console.error(f),i(new Error("Failed to initialize transaction")),r(!1)}},[e,t]),isLoading:n,error:o,transaction:s}};l();a();var Ge=S(T());var Qa=()=>{let e=be(p=>p.listCollectible),t=be(p=>p.listCollectiblePrice),[o,i]=(0,Ge.useState)(),[n,r]=(0,Ge.useState)(!1),[s,u]=(0,Ge.useState)();return{list:(0,Ge.useCallback)(async(p,d)=>{if(i(void 0),r(!0),!e||!t){i(new Error("missing collectible or price")),r(!1);return}try{let f=new URLSearchParams;f.set("pubKey",d.toBase58()),f.set("mint",e.asset.mintPubKey),f.set("account",e.asset.pubkey),f.set("quotePrice",`${xt(t)}`),f.set("marketplace","magic_eden");let y=pe(p);f.set("chainId",y.toString());let b=`/solana/composer/v1/list?${f.toString()}`,h=(await k.api().retry(3,2e3).get(b)).data,A=ee.Transaction.from(Buffer.from(h.transaction,"base64"));He(A)?u(A):i(new Error("Failed to initialize transaction")),r(!1)}catch(f){i(new Error("Failed to initialize transaction")),r(!1),D.captureError(f,"magicEden")}},[e,t]),isLoading:n,error:o,transaction:s}};l();a();var Qe=S(T());var Wa=()=>{let e=be(m=>m.unlistCollectible),[t,o]=(0,Qe.useState)(),[i,n]=(0,Qe.useState)(!1),[r,s]=(0,Qe.useState)();return{unlist:(0,Qe.useCallback)(async(m,p)=>{if(o(void 0),n(!0),!e){o(new Error("missing collectible")),n(!1);return}try{let d=new URLSearchParams;d.set("pubKey",p.toBase58()),d.set("mint",e.asset.mintPubKey),d.set("account",e.asset.pubkey),d.set("marketplace","magic_eden");let f=pe(m);d.set("chainId",f.toString());let y=`/solana/composer/v1/unlist?${d.toString()}`,g=(await k.api().retry(3,2e3).get(y)).data,h=ee.Transaction.from(Buffer.from(g.transaction,"base64"));He(h)?s(h):o(new Error("Failed to initialize transaction")),n(!1)}catch(d){o(new Error("Failed to initialize transaction")),n(!1),D.captureError(d,"magicEden")}},[e]),isLoading:i,error:t,transaction:r}};l();a();var Gr=S(T()),ja=e=>(0,Gr.useMemo)(()=>e.filter(t=>t?.media?.type==="image"),[e]);l();a();var Qr=S(T());var za=(e,t)=>(0,Qr.useMemo)(()=>{if(!e||!t)return;let o=e.chain.id,i=t.explorers[o]??zo.get(o).defaultExplorer,n,r;if(K(e.chainData))r="address",n=e.chainData.mint;else if(ne(e.chainData))if(e.chainData.contract)e.chainData.id?(r="nft",n=[e.chainData.contract,e.chainData.id]):(r="token",n=e.chainData.contract);else return;else if(re(e.chainData))if(At(e.chainData)){let{hash:u,index:m}=e.chainData.utxoDetails;r="transaction",n=`${u}#flow=&vout=${m}`}else return{name:"Ordinals",url:`https://ordinals.com/inscription/${e.chainData.firstCreatedInscriptionId}`};else throw new Error(`Unhandled collectible explorer for chain id: ${e.chain.id}`);if(!n)return;let s=Yo({networkID:o,endpoint:r,explorerType:i,param:n});if(s)return{name:$o[i],url:s}},[e,t]);l();a();var Wr=S(T()),$a=e=>(0,Wr.useMemo)(()=>{let t=new Set;return e.filter(o=>{if(!o?.media?.image?.url)return!1;let{url:i}=o.media.image;return t.has(i)?!1:(t.add(i),!0)})},[e]);l();a();var jr=S(T());var Ya=e=>{let{t}=L();return(0,jr.useMemo)(()=>{let i=[];if(!e)return;let n=e.chainData;if(re(n)){let{outputValue:r,satNumber:s,satName:u,inscriptionId:m,inscriptionNumber:p,protocolName:d,createdAt:f,utxoDetails:y}=n,b=t(m?"collectibleDetailOrdinalInfo":"collectibleDetailRareSatsInfo"),g;return r?g=parseFloat(r).toLocaleString("en-US"):y&&(g=y.value.toLocaleString("en-US")),i.push(g?{label:t("collectibleDetailSatsInUtxo"),value:t("collectibleDetailSatsInUtxoValue",{satsInUtxo:g})??void 0}:null),i.push(s?{label:t("collectibleDetailSatNumber"),value:s}:null),i.push(u?{label:t("collectibleDetailSatName"),value:u}:null),i.push(m?{label:t("collectibleDetailInscriptionId"),value:Oo(m,5,3)}:null),i.push(p?{label:t("collectibleDetailInscriptionNumber"),value:p}:null),i.push(d?{label:t("collectibleDetailStandard"),value:d?.toUpperCase()}:null),i.push(f?{label:t("collectibleDetailCreated"),value:Xa(new Date(f))}:null),{label:b,items:i.filter(Lo)}}},[e,t])};function Xa(e){let t=e.toLocaleDateString(void 0,{month:"numeric",day:"numeric",year:"numeric"}),o=e.toLocaleTimeString();return`${t} ${o}`}l();a();var $r=S(T()),Ae=()=>{},zr=new Map([["send",1],["list",2],["pin",3],["unpin",4],["share",5],["setAsAvatar",6],["saveToLibrary",7],["hide",8],["reportAsSpam",9],["reportAsNotSpam",10],["burnToken",11],["viewOnExplorer",12]]);function Za(e){let{amountToBurn:t=0,explorerName:o,isBurnable:i,isHidden:n,isPinned:r,isSpam:s,onBurnToken:u,onList:m,onReportNotSpam:p,onReportSpam:d,onSaveToLibrary:f,onSend:y,onSetAsAvatar:b,onShare:g,onToggleHide:h,onTogglePin:A,onViewOnExplorer:P,shortcuts:j,shouldDisplaySend:M,shouldDisplayShortcuts:G,shouldDisplayListOnSolana:X}=e,{t:v}=L();return(0,$r.useMemo)(()=>{let B=[];if(M&&B.push({text:v("commandSend"),type:"send",onClick:y??Ae}),X&&B.push({text:v("collectibleDetailList"),type:"list",onClick:m}),f&&B.push({text:v("assetDetailSaveToPhotos"),singleWordAltText:v("commandSave"),type:"saveToLibrary",menuOnly:!0,onClick:f??Ae}),b&&!s&&B.push({text:v("collectibleDetailSetAsAvatar"),singleWordAltText:v("collectibleDetailSetAsAvatarSingleWorkAlt"),type:"setAsAvatar",menuOnly:!0,onClick:b??Ae}),g&&B.push({text:v("collectibleDetailShare"),singleWordAltText:v("commandShare"),type:"share",menuOnly:!0,onClick:g??Ae}),A){let V=v(r?"assetDetailUnpinCollection":"assetDetailPinCollection"),z=v(r?"commandUnpin":"commandPin"),J=r?"unpin":"pin";B.push({text:V,singleWordAltText:z,type:J,menuOnly:!0,onClick:A??Ae})}if(h){let V=v(n?"assetDetailUnhideCollection":"assetDetailHideCollection"),z=v(n?"commandUnhide":"commandHide"),J=n?"unhide":"hide";B.push({text:V,singleWordAltText:z,type:J,menuOnly:!0,onClick:h??Ae})}if(p){let V=v("collectiblesReportAsNotSpam");B.push({text:V,type:"reportAsNotSpam",singleWordAltText:v("collectiblesReportNotSpam"),menuOnly:!0,isDestructive:!0,onClick:p})}if(d){let V=v("collectiblesReportAsSpam");B.push({text:V,type:"reportAsSpam",singleWordAltText:v("commandReport"),menuOnly:!0,isDestructive:!0,onClick:d})}if(i){let V=v(t===1?"collectibleBurnTitleWithCount_one":"collectibleBurnTitleWithCount_other");B.push({text:V,singleWordAltText:v("commandBurn"),type:"burnToken",menuOnly:!0,isDestructive:!0,onClick:u??Ae})}return!!P&&!!o&&B.push({text:v("assetDetailViewOnExplorer",{explorer:o}),singleWordAltText:v("commandView"),type:"viewOnExplorer",menuOnly:!0,onClick:P??Ae}),B.sort((V,z)=>(zr.get(V.type)??100)-(zr.get(z.type)??100)),{actions:Vi(B,4),shortcuts:G?j:[]}},[t,o,i,n,r,s,u,m,p,d,f,y,b,g,h,A,P,j,M,G,X,v])}l();a();export{as as a,Ic as b,wt as c,Oc as d,Ji as e,Nc as f,K as g,ne as h,re as i,At as j,Ds as k,Us as l,Rs as m,Ls as n,Bs as o,ua as p,Ca as q,vr as r,va as s,ve as t,Ns as u,Pa as v,Dt as w,ka as x,Or as y,Ra as z,Vs as A,Fs as B,Hs as C,xn as D,Ba as E,Ma as F,_s as G,Rt as H,Nm as I,In as J,ml as K,gl as L,Tl as M,Ll as N,Vl as O,Fl as P,_l as Q,ql as R,Gl as S,jl as T,Ka as U,qa as V,be as W,Ga as X,Qa as Y,Wa as Z,Jl as _,ea as $,na as aa,ra as ba,sa as ca,ja as da,za as ea,$a as fa,Ya as ga,Za as ha,la as ia};
//# sourceMappingURL=chunk-F3RUX6TF.js.map
