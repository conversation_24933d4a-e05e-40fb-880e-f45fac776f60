import{n as r,o}from"./chunk-WIQ4WVKX.js";import{h as e,n as i}from"./chunk-3KENBVE7.js";e();i();var n=r`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
`,g=o.div`
  display: flex;
  aspect-ratio: ${({aspectRatio:t})=>t};
  flex-direction: ${({flexDirection:t})=>t};
  flex-wrap: ${({flexWrap:t})=>t};
  align-items: ${({align:t})=>t};
  justify-content: ${({justify:t})=>t};
  width: ${({width:t})=>t};
  height: ${({height:t})=>t};
  margin: ${({margin:t})=>t};
  margin-bottom: ${({marginBottom:t})=>t};
  padding: ${({padding:t})=>t};
  border-radius: ${({borderRadius:t})=>t};
  background-color: ${t=>t.backgroundColor||t.theme.skeletonLight};
  animation: ${n} 2s ease-in-out infinite;
`;export{g as a};
//# sourceMappingURL=chunk-QEXGR5WT.js.map
