import{c as p}from"./chunk-KJMFZ7XX.js";import{a as i}from"./chunk-QEXGR5WT.js";import{a}from"./chunk-CCQRCL2K.js";import{n,o as t}from"./chunk-WIQ4WVKX.js";import{a as d}from"./chunk-7X4NV6OJ.js";import{f as c,h as r,n as e}from"./chunk-3KENBVE7.js";r();e();var o=c(d());var f=[1,2,3],m=50,l=350,h=m+l,I=n`
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
`,H=t(a).attrs({align:"center"})`
  height: ${h}px;
`,S=t(i).attrs({width:"100%",height:`${m}px`,margin:"0 0 20px 0",borderRadius:"6px",backgroundColor:"#2D2D2D"})``,L=()=>o.default.createElement(H,null,o.default.createElement(S,null),f.map(s=>o.default.createElement(p,{key:`fungible-token-row-${s}`})));export{L as a};
//# sourceMappingURL=chunk-HNH7ZX4P.js.map
