import{k as s,o as t}from"./chunk-WIQ4WVKX.js";import{a as h}from"./chunk-7X4NV6OJ.js";import{f as d,h as n,n as i}from"./chunk-3KENBVE7.js";n();i();var e=d(h());var F=t.div`
  box-shadow: 0px -4px 6px rgba(0, 0, 0, 0.2);
  background: #222;
  padding: 14px 20px;
  border-top: 1px solid #323232;
  position: absolute;

  left: -16px;
  right: -16px;
  bottom: -10px;

  ${o=>o.removeFooterExpansion&&s`
      left: 0;
      right: 0;
      bottom: 0;
    `}

  ${o=>o.cssOverride}
`,m=t.div`
  height: ${o=>o.height?`${o.height}px`:"auto"};
`,C=e.default.memo(({children:o,removeShadowFooter:a,removeFooterExpansion:r,cssOverride:p})=>{let l=75+(r?0:-10);return e.default.createElement(e.default.Fragment,null,e.default.createElement(F,{removeFooterExpansion:r,cssOverride:p},o),a?null:e.default.createElement(m,{height:l}))});export{C as a};
//# sourceMappingURL=chunk-IVMV7P4T.js.map
