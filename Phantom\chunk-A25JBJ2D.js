import{a as Bt,b as Vt}from"./chunk-Z3VAESXD.js";import{a as Mt}from"./chunk-PUJH7423.js";import{a as fn}from"./chunk-XYFNIIUY.js";import{b as Ut}from"./chunk-O5AAGNHJ.js";import{a as Nt}from"./chunk-MXORZ3WH.js";import{E as Ct,Ma as Ye,S as vt,ga as Et,y as Ft}from"./chunk-JD6NH5K6.js";import{b as Dt}from"./chunk-SIDJ2NRC.js";import{a as xt}from"./chunk-QEXGR5WT.js";import{b as kt}from"./chunk-S24UABH5.js";import{a as ge}from"./chunk-X3ESGVCB.js";import{e as Pt}from"./chunk-DERIAD33.js";import{a as It}from"./chunk-EGXLQXDH.js";import{a as Xe}from"./chunk-CCQRCL2K.js";import{c as wt,h as ve,m as Je}from"./chunk-75L54KUM.js";import{b as jn}from"./chunk-W27Z2YZM.js";import{a as ut,c as pt,d as k,e as ue,n as gn,q as gt,r as ft}from"./chunk-2NGYUYTC.js";import{a as Ve}from"./chunk-4VDZJDFB.js";import{a as yt,b as At,h as Tt,j as ht,k as Qe}from"./chunk-OKP6DFCI.js";import{c as St,ga as bt,o as V,rb as pe}from"./chunk-WIQ4WVKX.js";import{ta as ct}from"./chunk-SD2LXVLD.js";import{o as Ln}from"./chunk-LURFXJDV.js";import{d as y,f as Xn,g as nt,h as tt,j as ot,p as rt,w as st,x as it}from"./chunk-V5T43K7V.js";import{b as qn,c as zn,e as Ae}from"./chunk-MHOQBMVI.js";import{s as Kn,z as Ce}from"./chunk-7ZN4F6J4.js";import{Bc as lt,D as Hn,J as On,L as _n,Ob as je,Qb as Qn,Sa as Ie,Sb as Jn,Ta as pn,Ub as Yn,dc as et,fc as Ze,hc as ee,jc as Pe,oa as Gn,pa as $n,pb as qe,pc as Ue,rb as ze,vc as at,xb as Zn,xc as dt,yc as mt}from"./chunk-OUYKWOVO.js";import{h as $e}from"./chunk-OYGO47TI.js";import{o as Wn}from"./chunk-SLQBAOEK.js";import{$d as Rn,B as xn,Kd as Ge,P as Oe,Pa as v,Ra as Mn,Rb as un,jb as Bn,pe as xe,sb as Un,wc as Vn}from"./chunk-MZZEJ42N.js";import{a as Y,m as we}from"./chunk-56SJOU6P.js";import{S as ln,T as En,Z as Nn,a as kn,b as wn,ea as cn,fa as ke,ka as _e,x as Dn}from"./chunk-ALUTR72U.js";import{Ya as vn,ka as In,oa as Pn,t as Cn,ta as ye}from"./chunk-L3A2KHJO.js";import{a as ce}from"./chunk-7X4NV6OJ.js";import{f as le,h as u,m as Buffer,n as p}from"./chunk-3KENBVE7.js";u();p();u();p();u();p();u();p();var en=le(ce());u();p();u();p();var oo=[Gn.toString(),$n.toString()],Lt=async(t,e)=>{let n=_n(e);try{let o=await n.getParsedAccountInfo(new Oe.PublicKey(t));if(o&&oo.includes(o.value?.owner.toString()??"")&&"mintAuthority"in(o.value?.data).parsed.info)return!0}catch{}return!1};var ro=1*24*60*1e3,so=1*60*1e3,Wt=({address:t,networkID:e})=>In({queryKey:["is-mint-account",t],queryFn:async()=>{if(!v.isSolanaNetworkID(e))throw new Error("Attempting to get mint account for non-Solana network");return Lt(t,e)},enabled:!!t&&v.isSolanaNetworkID(e),gcTime:ro,staleTime:so});u();p();var Kt=le(ce());u();p();var Le=()=>{let{fungibleKey:t,splTokenAccount:e,recipient:n}=y(m=>({fungibleKey:m.fungibleKey,splTokenAccount:m.splTokenAccount,recipient:m.sendFormValues.recipient})),{fungible:o}=ee({key:t,splTokenAccount:e}),{data:a}=xe({address:o?.data.walletAddress,networkID:o?.data.chain.id}),{data:d}=ot(a?{networkID:a.networkID,addressType:a.addressType,address:n}:void 0,o?.type==="SolanaNative"),{data:i=0}=dt(a);return o?.type==="SolanaNative"&&n&&typeof d<"u"&&d<i?i:0};var Rt=(t,e,n)=>{let{fungibleKey:o,splTokenAccount:a}=y(C=>({fungibleKey:C.fungibleKey,splTokenAccount:C.splTokenAccount})),{data:[d]}=vn(["kill-send-simulation"]),i=Le(),c=(0,Kt.useMemo)(()=>{if(!(!t||d))return{sendSessionId:"simulate-send",shouldShowUsdValues:!1,fungibleKey:o,splTokenAccount:a,sendFormValues:{recipient:e,recipientHandle:"",amountAsset:new kn(i),solana:{references:[]}}}},[d,i,t,o,e,a]),{data:m,isLoading:b}=Pe(c),A={type:"transaction",userAccount:t,params:void 0,url:"https://phantom.app",networkID:n};m&&(v.isEVMNetworkID(m.networkID)&&(A.params={transactions:[m.unsignedTransaction]}),v.isSolanaNetworkID(m.networkID)&&(A.params={transactions:m.transaction.map(C=>xn(C)),method:"signAndSendTransaction"}));let{data:T,isLoading:F}=ct(A,{disabled:!m||d,disableRefetch:!0});return{data:T,isLoading:!d&&(b||F)}};var ao=(t,e)=>({address:t,addressType:Bn(e,t),networkID:e}),mo=({senderAddress:t,senderAccounts:e,networkID:n})=>(0,en.useMemo)(()=>t?e.reduce((o,a)=>{let d=a.addresses.filter(i=>i.networkID===n&&i.address!==t);if(n)for(let i of d)o.push({label:a.name,address:i.address,chainID:n});return o},[]):[],[t,e,n]);function lo(t){if(!t)return"";let{warnings:e=[],error:n=""}=t;return n==="UNKNOWN_ERROR"?e[0]?.message:n==="INSUFFICIENT_FUNDS"?Y.t(`sendWarning_${n}`):""}var co=({senderAddress:t,recipient:e,networkID:n})=>{let{data:o,isLoading:a}=Rt(t??"",e,n);return{simulationWarning:lo(o),isSimulationLoading:a}},uo=({recipient:t,networkID:e})=>{let{currentRecipient:n,isStaleRecipient:o,recipientError:a}=je(t,e);return!o&&!a&&n.length>0?n:""},po=({recipient:t,networkID:e})=>{let{data:n=[],isLoading:o}=et({chainAddresses:t?[ao(t,e)]:[]});return{recipientHasZeroBalance:(0,en.useMemo)(()=>n.length===1&&n[0].data.amount==="0",[n]),recipientTokensIsLoading:o}},Sn=({senderAddress:t,senderAccounts:e,networkID:n,recipient:o})=>{let{recentAddresses:a,savedAddresses:d}=Ce(),i=mo({senderAddress:t,senderAccounts:e,networkID:n}),c=uo({recipient:o,networkID:n}),{recipientHasZeroBalance:m,recipientTokensIsLoading:b}=po({recipient:c,networkID:n}),{data:A,isLoading:T}=Wt({address:c,networkID:n}),{simulationWarning:F,isSimulationLoading:C}=co({senderAddress:t??"",recipient:c,networkID:n}),g=!c||b||T&&v.isSolanaNetworkID(n)||C;return{sendAddressWarning:(0,en.useMemo)(()=>g||!t?null:A?Y.t("sendMintAccountWarning"):i.find(S=>S.address===c)?null:F||(a.addresses.find(S=>S.address===o)||d.addresses.find(S=>S.address===o)?null:t===c?Y.t("sendSameAddressWarning"):m?Y.t("sendRecipientZeroBalanceWarning"):Y.t("sendUnknownAddressWarning")),[a,d,o,c,i,t,m,A,F,g]),sendAddressVariant:A?"danger":"warning"}};u();p();var bn=le(ce());var yn=()=>{let t=y(m=>m.analyticsRecipient),e=y(m=>m.fungibleKey),n=y(m=>m.splTokenAccount),{fungible:o}=ee({key:e,splTokenAccount:n}),{data:a=[]}=Ge(),d=(0,bn.useMemo)(()=>a.some(m=>m.addresses.some(b=>b.address===t?.address)),[a,t]),i=o?.data.chain.id??Mn.Solana.Mainnet,{data:c}=xe({address:o?.data.walletAddress,networkID:i});return(0,bn.useMemo)(()=>({assetType:"token",networkId:i,recipient:t,senderAddress:c?.address??"",tokenAddress:o?.data.tokenAddress??"",tokenSymbol:o?.data.symbol??"",tokenType:o?.type,selfSend:d}),[i,t,c?.address,o,d])};var s=le(ce());u();p();var ne=le(ce());var Ht=({networkID:t})=>{let{t:e}=we();return v.isBitcoinNetworkID(t)?ne.default.createElement(bo,null,ne.default.createElement(vt,{alignment:"rightCenter",index:0,content:ne.default.createElement(go,null,ne.default.createElement(fo,null,e("sendFungibleSatProtectionExplainer")," ",ne.default.createElement("span",{onClick:()=>void jn({url:at})},e("commandLearnMore")))),closeOnSecondClick:!1},ne.default.createElement(So,null,ne.default.createElement(bt,{width:16}),ne.default.createElement(Ot,null," ",e("sendFungibleSatProtectionTitle"))))):null},go=V.div`
  padding: 16px;
  width: 256px;
`,fo=V(pe).attrs({size:15,lineHeight:20,color:"#fff"})`
  text-align: left;
  span {
    color: #ab9ff2;
    cursor: pointer;
  }
`,So=V.div`
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 8px;
`,Ot=V(pe).attrs({size:14,color:"#fff",hoverColor:"#ab9ff2"})`
  display: flex;
  align-items: center;
`,bo=V.div`
  padding: 15px;
  background: #2a2a2a;
  border-radius: 9px;
  width: 100%;
  cursor: pointer;
  position: relative;

  &:hover {
    ${Ot} {
      color: #ab9ff2;
    }
    svg {
      fill: #ab9ff2;
      path {
        fill: #ab9ff2;
      }
    }
  }
`;u();p();u();p();u();p();u();p();var fe=le(ce());var An=()=>{let t=y(T=>T.fungibleKey),e=y(T=>T.splTokenAccount),n=y(T=>T.sendFormValues),{fungible:o}=ee({key:t,splTokenAccount:e}),{amountAsset:a}=n,d=ke(a,o?.data.decimals??0),i=Ue(d),c=o?.data.symbol??"",m=(0,fe.useMemo)(()=>o?Ze(o):void 0,[o]),{data:b}=ze({query:m?{data:m}:void 0}),A=_e(Ie(d.toNumber(),b?.usd??0));return fe.default.createElement(k,{direction:"column",alignItems:"center",marginTop:8,marginBottom:32},fe.default.createElement(k,{marginBottom:16},fe.default.createElement(gt,{icon:"Send2",size:64,color:"accentPrimary",backgroundColor:"bgTabBar",shape:"circle"})),fe.default.createElement(ue,{font:"heading1Semibold"},i," ",c),A?fe.default.createElement(ue,{color:"textSecondary",font:"title2"},A):null)};var x=le(ce());u();p();u();p();u();p();u();p();var hn=Nn(Vn,t=>new Tn(t)),Tn=class{#e;constructor(e){this.#e=e}onSendFlowStart({assetType:e,context:n}){this.#e.capture("sendFlowStart",{data:{assetType:e,context:n}})}onSendAssetAttempt(e){this.#e.capture("sendAssetAttempt",{data:{chainId:v.getChainID(e.networkId),...e}})}onSendAssetSuccess(e){this.#e.capture("sendAssetSuccess",{data:{chainId:v.getChainID(e.networkId),...e}})}onSendAssetFailure(e){this.#e.capture("sendAssetFailure",{data:{chainId:v.getChainID(e.networkId),...e}})}};var r=le(ce());var To=V(Xe).attrs({align:"center"})`
  height: 100%;
`,ho=V(Xe).attrs({align:"center",justify:"center"})`
  flex: 1;
  flex-grow: 1;
`,Fo=V(Xe).attrs({align:"center",justify:"center",margin:"0 0 15px 0"})`
  position: relative;
  border-radius: 50%;
  background-color: ${ln("#AB9FF2",.2)};
  box-shadow: 0 0 0 20px ${ln("#AB9FF2",.2)};
`,ko=V(pe).attrs({size:28,lineHeight:33.89,weight:500,margin:"20px 0 10px 0"})``,wo=V(pe).attrs({size:16,lineHeight:19,weight:400,color:"#777777",margin:"0 0 10px 0"})``,De=V(pe).attrs({color:"#FFF",weight:400,size:16})``,xo=V(pe).attrs({size:16,lineHeight:20.8,weight:500,color:"#AB9FF2"})``,Co=t=>{let{t:e}=we(),{networkID:n,assetSymbol:o,logoUri:a,txRecipient:d,uiRecipient:i,uiAmount:c,tokenUSDPrice:m,fungibleKey:b,senderAddress:A,exitSendFlow:T}=t,F=hn(),C=yn(),g=y(),{transactionSpeed:f}=g.sendFormValues,{data:L}=Pe(g),S=mt(n,f,A,o,a,i,c,m,e),{popDetailView:I}=ve(),{closeAllModals:M}=kt(),W=St(),[G,h]=(0,r.useState)(!1),[K,R]=(0,r.useState)(null),{data:Se,status:Ee}=Rn(),H=(0,r.useMemo)(()=>Se?.addresses??[],[Se]),E=Se?.type==="ledger",[te,Te]=(0,r.useState)(!0),[he,oe]=(0,r.useState)(),[P,re]=(0,r.useState)(void 0);nt(b,new wn(c),P?.id??"");let $=Zn("send:fungible",n,E),B=rt(H),q=(0,r.useCallback)(w=>{F.onSendAssetSuccess(C),$(w.id,"confirmed"),B()},[F,C,$,B]),{isSuccess:se,isError:O,error:Z}=Qn(q,P??void 0),{title:nn,isSuccess:be,isError:ie,sendSuccessCondition:We,estimatedTime:Ne}=it({networkID:n,isTxConfirmError:O,isTxSubmissionError:G,isTxConfirmed:se,isTxSubmitted:!!P});(0,r.useEffect)(()=>{let w=P?.networkID?$e(P.networkID):null;!P?.id||!w||w.chainName==="bitcoin"||(se?(ye.addBreadcrumb("fungibles","transactionStatus","info",w),Ae.send.transactionStatus(P.id,{...w,status:{type:"confirmed"}})):ie&&(ye.addBreadcrumb("fungibles","transactionStatus","error",w),Ae.send.transactionStatus(P.id,{...w,status:{type:"error"}})))},[se,ie,P]);let{mutateAsync:Me}=Xn(),{data:Be}=Kn(),{addRecentAddress:ae}=Ce(),de=Ee==="pending",Fe=(0,r.useMemo)(()=>({titleText:nn,retryText:e("commandRetry"),cancelText:e("commandCancel"),closeText:e("commandClose"),viewTransactionText:e("sendStatusViewTransaction")}),[e,nn]),tn=ie?r.default.createElement(r.default.Fragment,null,r.default.createElement(Ve,{i18nKey:"sendFungibleErrorMessageInterpolated",values:{uiAmount:c,assetSymbol:o,uiRecipient:i}},r.default.createElement(ge,null,r.default.createElement(De,null,c," ",o),"failed to send to ",r.default.createElement(De,null,i))),Z&&"data"in Z?r.default.createElement(ge,{margin:"16px 0",justify:"center"},r.default.createElement(Vt,{errorReason:Z.data.errorReason,programId:Z.data.programId,customErrorReason:Z.data.customErrorReason,customErrorCode:Z.data.customErrorCode})):null,K?r.default.createElement(ge,{margin:"16px 0 0 0",justify:"center"},e("sendFungibleSolanaErrorCode",{code:K})):null):be&&We==="CONFIRMATION"?r.default.createElement(Ve,{i18nKey:"sendFungibleConfirmedSuccessMessageInterpolated",values:{uiAmount:c,assetSymbol:o,uiRecipient:i}},r.default.createElement(ge,null,r.default.createElement(De,null,c," ",o),"was successfully sent to ",r.default.createElement(De,null,i))):be&&We==="SUBMISSION"?r.default.createElement(Ve,{i18nKey:"sendStatusEstimatedTransactionTime",values:{time:Ne}},r.default.createElement(ge,null,"Estimated Transaction Time: ",Ne)):r.default.createElement(Ve,{i18nKey:"sendFungibleLoadingMessageInterpolated",values:{uiAmount:c,assetSymbol:o,uiRecipient:i}},r.default.createElement(ge,null,r.default.createElement(De,null,c," ",o),"to ",r.default.createElement(De,null,i))),Ke=(0,r.useCallback)(()=>{if(!P)return;let{id:w}=P,j=Be?.explorers[n],U=Wn({networkID:n,endpoint:"transaction",explorerType:j,param:w});self.open(U)},[n,Be,P]),on=(0,r.useCallback)(()=>{T();let{sendSuccessRedirect:w}=On.get(n);switch(w(qn("kill-base-history"))){case"history":W("/notifications");break;case"home":W("/");break}},[T,W,n]),{data:N}=qe({networkID:n,multichainTransaction:L,transactionSpeed:f,queryOptions:{refetchInterval:!1}}),me=(0,r.useCallback)(async()=>{let w=async()=>{try{if(!L)throw new Error("multichainTransaction is undefined on send status");if(!N)throw new Error("gasEstimation is undefined on send status");ae({address:d,chainID:A.networkID,timestamp:Date.now()});let j=Date.now();F.onSendAssetAttempt(C);let U=await Me({multichainTransaction:L,pendingTransactionInput:S,senderAddress:A,gasEstimation:N}),Re=U.id;$(Re,"initiated",j),$(Re,"received");let X=$e(n),rn=X.chainType==="solana"?"signAndSendTransaction":X.chainType==="eip155"?"eth_sendRawTransaction":"UNKNOWN";ye.addBreadcrumb("fungibles","submittedTransaction","info",{...X,txReceipt:U.id,method:rn}),X.chainType==="solana"?Ae.send.submittedTransaction(U.id,{...X,method:"signAndSendTransaction"}):X.chainType==="eip155"&&Ae.send.submittedTransaction(U.id,{...X,method:"eth_sendRawTransaction"}),re(U)}catch(j){if(j instanceof Error&&$e(n).chainType==="solana"){let[U]=j.message.match(/0x\d+/)||[];typeof U=="string"&&U&&R(U)}h(!0),F.onSendAssetFailure(C),ye.captureError(j,"fungibles")}};E?oe(r.default.createElement(Ut,{ledgerAction:async()=>{await w(),Te(!1)},cancel:M,ledgerApp:Ln(n)})):await w()},[E,L,N,ae,d,A,Me,S,$,n,M,F,C]),z=y(w=>w.retryArgs),Q=(0,r.useCallback)(()=>{z?(re(void 0),h(!1),me()):I()},[me,I,z]);(0,r.useEffect)(()=>{de||(!E||te)&&me()},[de]);let J=(0,r.useCallback)(()=>{!be&&!ie&&g.resetSendSlice()},[be,ie,g]);return wt(J),{i18nStrings:Fe,message:tn,txReceipt:P,isError:ie,isSuccess:be,isLedger:E,needsLedgerApproval:te,ledgerContent:he,onTransactionLinkClick:Ke,onRetry:Q,onCancel:T,onClose:on}},_t=r.default.memo(t=>{let e=Co(t);return r.default.createElement(Io,{...e})}),Io=r.default.memo(t=>{let{i18nStrings:e,message:n,txReceipt:o,isLedger:a,needsLedgerApproval:d,ledgerContent:i,isError:c,isSuccess:m,onTransactionLinkClick:b,onRetry:A,onCancel:T,onClose:F}=t;return a&&d?r.default.createElement(r.default.Fragment,null,i??null):r.default.createElement(To,null,r.default.createElement(ho,null,r.default.createElement(At,{mode:"wait",initial:!0},r.default.createElement(yt.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2}},c?r.default.createElement(fn,{type:"failure"}):m?r.default.createElement(fn,{type:"success"}):r.default.createElement(Fo,null,r.default.createElement(Tt,{diameter:54,color:"#AB9FF2",trackColor:"#181818"})))),r.default.createElement(ko,null,e.titleText),n&&r.default.createElement(wo,null,n),o&&r.default.createElement(xo,{onClick:b},e.viewTransactionText)),c&&r.default.createElement(Qe,{primaryText:e.retryText,onPrimaryClicked:A,secondaryText:e.cancelText,onSecondaryClicked:T}),!c&&!!o&&r.default.createElement(ge,{justify:"center"},r.default.createElement(ht,{onClick:F},e.closeText)))});var Po={accentAlert:"#EB3742",white:"#FFFFFF"},vo=x.default.createElement(xt,{width:"75px",height:"8px",borderRadius:"8px",backgroundColor:"#484848"}),Do=t=>{let{networkID:e,exitSendFlow:n}=t,o=y(h=>h.sendFormValues),a=y(h=>h.setSendFormValues),{popDetailView:d,pushDetailView:i}=ve(),{handleHideModalVisibility:c,handleShowModalVisibility:m}=Ye(),b=(0,x.useCallback)((h,K)=>{i(x.default.createElement(Nt,{onSelectTransactionSpeed:R=>{a({...o,transactionSpeed:R})},selectedTransactionSpeed:h,networkID:e,transactionUnitAmount:K,closeModal:d}))},[e,d,i,o,a]),A=(0,x.useCallback)(h=>{m("txSubmissionCheckFailure",{...h,onButtonClick:()=>{c("txSubmissionCheckFailure")}})},[c,m]),{sender:T,recipientAddress:F,uiRecipient:C,token:g,i18nStrings:f,canSend:L,summaryRows:S,prepareSubmission:I,multichainTransaction:M}=st({networkID:e,valueLoader:vo,fontColors:Po,onTransactionSpeedChange:b,onSubmissionCheckFail:A}),W=(0,x.useMemo)(()=>{let h=M&&Hn(M)?M.transaction.map(K=>{let R=Buffer.from(K.serialize({verifySignatures:!1,requireAllSignatures:!1})).toString("base64");return{label:Y.t("notificationCopyTransactionText"),value:R.slice(0,8),onClick:()=>{It(R),Ft.success(Y.t("notificationTransactionCopied"))}}}):[];return[...S,...h]},[S,M]),G=En((0,x.useCallback)(()=>{if(!I())return;if(!T||!F||!g.key)throw new Error("Sender, recipient or fungible key is undefined");let K={chainID:e,amount:g.amount,symbol:g.symbol,address:g.key};ye.addBreadcrumb("fungibles","approved","info",K),Ae.send.approved(lt(K)),i(x.default.createElement(_t,{networkID:e,senderAddress:T,txRecipient:F,uiRecipient:C,assetSymbol:g.symbol,logoUri:g.logoUri,uiAmount:Number(g.amount),tokenUSDPrice:g.usdPrice,fungibleKey:g.key,exitSendFlow:n}))},[I,e,T,F,g.key,g.symbol,g.logoUri,g.amount,g.usdPrice,i,C,n]),1e3);return{i18nStrings:f,exitSendFlow:n,canSend:L,summaryRows:Cn()?W:S,handleSubmit:G,networkID:e}},Gt=t=>{let e=Do(t);return x.default.createElement(Eo,{...e})},Eo=x.default.memo(({i18nStrings:t,canSend:e,summaryRows:n,handleSubmit:o,exitSendFlow:a,networkID:d})=>x.default.createElement(k,{height:"100%"},x.default.createElement(Je,null,t.title),x.default.createElement(k,{height:"100%"},x.default.createElement(k,{width:"100%",flex:1},x.default.createElement(An,null),x.default.createElement(Dt,{networkID:d,rows:n,borderRadius:"6px 6px 6px 6px",padding:16,fontSize:16})),x.default.createElement(ft,{direction:"row"},x.default.createElement(gn,{onClick:a,theme:"secondary"},t.cancel),x.default.createElement(gn,{disabled:!e,onClick:o,theme:"primary"},t.send)))));var No={form:ut({width:"100%"})},Mo=Dn({seconds:10}),Bo=t=>{let{networkID:e,chainName:n,fungibleKey:o,symbol:a,decimals:d,logoUri:i,tokenAddress:c,type:m}=t,{t:b}=we(),A=(0,s.useMemo)(()=>({pageHeaderText:b("commandSend"),buttonPrimaryText:b("commandNext"),buttonSecondaryText:b("commandCancel"),targetInputButtonText:b("maxInputMax"),memoPlaceholderText:b("sendMemoOptional")}),[b]),T=(0,s.useMemo)(()=>({amountAvailable:(l,_)=>b("sendAssetAmountLabelInterpolated",{amount:l,tokenSymbol:_})}),[b]),{popDetailView:F,pushDetailView:C}=ve(),{handleHideModalVisibility:g}=Ye(),f=y(l=>l.shouldShowUsdValues),L=y(l=>l.setShouldShowUsdValues),S=y(l=>l.sendFormValues),I=y(l=>l.setSendFormValues),M=y(l=>l.resetSendSlice),W=f?"USD":a,G=f?a:"USD",{recipient:h,addressBookRecipient:K,amountAsset:R,amountUsd:Se,solana:{memo:Ee}}=S,H=ke(R,d).toNumber(),E=Se??0,{currentRecipient:te,recipientError:Te,disabledDueToRecipient:he,isStaleRecipient:oe,isRecipientLoading:P}=je(h,e),re=v.getNetworkName(e),$=y(),{fungible:B}=ee({key:o,splTokenAccount:$.splTokenAccount}),{data:q}=xe({address:B?.data.walletAddress,networkID:e}),se=q?.address,O=Le(),Z=ke(O,d).toNumber(),be=(B?.type==="SPL"?B.data.programId:"")===Un,ie=(0,s.useMemo)(()=>{if(!q)return;let l=$.sendFormValues.recipient;return{...$,sendFormValues:{...S,recipient:l||q.address}}},[S,$,q]),{data:We}=Pe(ie),{data:Ne,isPending:Me}=qe({networkID:e,multichainTransaction:We,transactionSpeed:"fastest",queryOptions:{refetchInterval:Mo}}),[Be,ae]=(0,s.useState)(""),[de,Fe]=(0,s.useState)(Pn),tn=(0,s.useCallback)(()=>{M()},[M]);(0,s.useEffect)(()=>{let l=f?E:H;!Be&&l!==0&&ae(l)},[]);let Ke=(0,s.useMemo)(()=>B?Ze(B):void 0,[B]),{data:on}=ze({query:Ke?{data:Ke}:void 0}),N=on?.usd,me=tt(q,B,Ne),z=ke(me??0,d).toNumber(),Q=Ie(z,N??0),J=(0,s.useMemo)(()=>N?`${_e(Ie(H,N))}`:"",[H,N]),w=(0,s.useCallback)(()=>{F(),g("sendFungibleForm"),g("sendFungibleSelect"),M()},[F,g,M]),{getExistingAccount:j,getKnownAddressLabel:U}=Ce(),{data:Re=[]}=Ge(),X=(0,s.useCallback)(l=>{l&&l.preventDefault();let _=m,an=j(h),He=U(h,e),dn=un(q?.address??"",void 0,void 0,5),mn=un(h,an,He,5);zn.capture("sendAsset",{data:{address:c,chain:n,chainId:v.getChainID(e),networkId:e,symbol:a,type:"fungible"}}),C(s.default.createElement(Gt,{networkID:e,assetSymbol:W,logoUri:i,fungibleType:_,fungibleKey:o,networkName:re,uiSender:dn,uiRecipient:mn,txRecipient:te,txAmount:R,uiAmount:f?E:H,tokenUSDPrice:J,exitSendFlow:w}))},[m,j,h,U,e,q?.address,n,a,c,C,W,i,o,re,te,R,f,E,H,J,w]),rn=(0,s.useCallback)(l=>{Fe(_=>({..._,recipientError:void 0})),I({...S,recipient:l,addressBookRecipient:l})},[S,I]),$t=(0,s.useCallback)(()=>{me&&(ae(f?Q:z),I({...S,amountAsset:me,amountUsd:Q}),Fe(l=>({...l,amountError:void 0})))},[f,Q,z,I,S,me]),qt=(0,s.useCallback)(()=>{ae(f?H||"":J==="$0.00"?"":J.substring(1)),L(!f)},[f,H,J,L]),zt=(0,s.useMemo)(()=>{if(!N)return`??? ${G}`;if(f){let l=Ue(pn(E??0,N,2));return E?l.length+(G.length??0)<15?`~${l} ${G}`:`~${l.substring(0,8)}... ${G}`:`0 ${G}`}return E?`~${J}`:"$0.00"},[E,f,G,N,J]),jt=(0,s.useCallback)(l=>{if(ae(l),isNaN(Number(l)))return;let _=parseFloat(l),an=Jn.get(e),He=f?an.minimumSendableCurrencyAmount:Z,dn=f?Q:z,mn=_<He,Yt=_>dn,eo=mn?b("sendFormErrorMinRequiredInterpolated",{minAmount:He,tokenName:W}):Yt?b("sendFormErrorInsufficientBalance"):void 0,no=f?cn(pn(_,N??0,d),d):cn(l,d),to=f?parseFloat(l):Ie(_,N??0);Fe({...de,amountError:eo}),I({...S,amountAsset:no,amountUsd:to})},[e,f,Q,z,Z,b,W,N,d,de,I,S]),Zt=(0,s.useCallback)(l=>{let _=S.solana;_.memo=l,I({...S,solana:_})},[S,I]),Qt=(0,s.useMemo)(()=>Me?"":Ue(f?Q:z),[f,Q,z,Me]),{sendAddressWarning:Fn,sendAddressVariant:sn}=Sn({senderAddress:se,senderAccounts:Re,networkID:e,recipient:h}),Jt=h&&!!Fn&&!Te&&!oe,Xt=(0,s.useMemo)(()=>!!H&&!de.amountError&&!he&&sn!=="danger",[H,de.amountError,he,sn]);return{isGasLoading:Ne===void 0&&Yn(m),i18nStrings:A,i18interpolation:T,networkID:e,senderAddress:se,addressBookRecipient:K,primarySymbol:W,fungibleAssetSymbol:a,canSubmit:Xt,decimals:d,denominationText:zt,logoUri:i,usdPrice:N,inputValue:Be,memoValue:Ee,shouldShowUsdValues:f,sendFormValues:S,sendFormErrors:de,handleAddressSelection:rn,setSendFormValues:I,setSendFormErrors:Fe,setMaxAmount:$t,toggleShouldShowUsdValues:qt,handleAmountChange:jt,handleMemoChange:Zt,handleSubmit:X,exitSendFlow:w,onLeftButtonClick:tn,availableBalance:Qt,isMemoEnabled:be,isStaleRecipient:oe,recipientError:Te,isRecipientLoading:P,sendAddressWarning:Jt?Fn:null,sendAddressVariant:sn}},Uo=t=>{let e=Bo(t);return s.default.createElement(Lo,{...e})},Vo=()=>{let t=y(o=>o.fungibleKey),e=y(o=>o.splTokenAccount),{fungible:n}=ee({key:t,splTokenAccount:e});return n?s.default.createElement(Uo,{networkID:n.data.chain.id,chainName:n.data.chain.name,fungibleKey:n.data.key,symbol:n.data.symbol??"",decimals:n.data.decimals,logoUri:n.data.logoUri??"",tokenAddress:n.data.tokenAddress,amount:n.data.amount,usd:n.data.usd,type:n.type}):null},ba=Vo,Lo=s.default.memo(t=>{let{i18nStrings:e,i18interpolation:n,networkID:o,senderAddress:a,addressBookRecipient:d,primarySymbol:i,fungibleAssetSymbol:c,canSubmit:m,decimals:b,denominationText:A,logoUri:T,usdPrice:F,inputValue:C,memoValue:g,sendFormValues:f,sendFormErrors:L,handleAddressSelection:S,setSendFormValues:I,setSendFormErrors:M,setMaxAmount:W,toggleShouldShowUsdValues:G,handleAmountChange:h,handleMemoChange:K,handleSubmit:R,exitSendFlow:Se,onBlurRecipientInput:Ee,onLeftButtonClick:H,isGasLoading:E,availableBalance:te,isMemoEnabled:Te,isStaleRecipient:he,recipientError:oe,isRecipientLoading:P,sendAddressWarning:re,sendAddressVariant:$}=t,{amountError:B}=L,q=te?s.default.createElement(ue,{font:"caption",color:"textTertiary",children:n.amountAvailable(te,i)}):s.default.createElement("div",null),se=F?s.default.createElement(k,{direction:"row",justifyContent:"space-between",width:"100%"},s.default.createElement(k,{direction:"row",alignItems:"center",cursor:"pointer",color:{base:"textTertiary",hover:"accentPrimary"}},s.default.createElement(ue,{font:"caption",onPress:G,children:A}),s.default.createElement(k,{marginLeft:4},s.default.createElement(pt.Swap2Vertical,{size:12}))),q):s.default.createElement(k,{justifyContent:"flex-end",width:"100%"},q);return s.default.createElement(k,{justifyContent:"space-between",height:"100%"},s.default.createElement(k,null,s.default.createElement(Je,{onLeftButtonClick:H},e.pageHeaderText," ",c),s.default.createElement(k,{marginBottom:20,marginTop:10,marginLeft:"auto",marginRight:"auto"},s.default.createElement(Ct,{image:{type:"fungible",src:T,fallback:i},size:72})),s.default.createElement("form",{onSubmit:R,className:No.form},s.default.createElement(k,{marginBottom:10},s.default.createElement(Bt,{addressBookRecipient:d,networkID:o,senderAddress:a,symbol:i,sendFormValues:f,sendFormErrors:L,recipientError:oe,handleAddressSelection:S,setSendFormValues:I,setSendFormErrors:M,onBlurRecipientInput:Ee,isLoading:P})),!d&&!he&&oe&&s.default.createElement(k,{marginBottom:10},s.default.createElement(ue,{children:oe,color:"accentAlert",font:"body"})),s.default.createElement(k,{marginBottom:10},s.default.createElement(k,{position:"relative"},s.default.createElement(Mt,{name:"amount",value:C,symbol:i,alignSymbol:"right",buttonText:e.targetInputButtonText,width:47,maxLength:20,decimalLimit:b,warning:!!B,onSetTarget:W,onKeyPress:O=>m&&O.key==="Enter"&&O.preventDefault(),onUserInput:h,targetButtonDisabled:E}))),Te&&s.default.createElement(k,{marginBottom:10},s.default.createElement(Pt,{name:"memo",placeholder:e.memoPlaceholderText,value:g,maxLength:240,onKeyDown:O=>m&&O.key==="Enter"&&O.preventDefault(),onChange:O=>{O.target.validity.valid?K(O.target.value):O.preventDefault()}}))),B?s.default.createElement(k,null,s.default.createElement(ue,{children:B,color:"accentAlert",font:"body"})):se,s.default.createElement(k,{marginTop:16},s.default.createElement(Ht,{networkID:o})),re&&s.default.createElement(Et,{message:re,variant:$,actions:[]})),s.default.createElement(Qe,{primaryText:e.buttonPrimaryText,secondaryText:e.buttonSecondaryText,onPrimaryClicked:R,onSecondaryClicked:Se,primaryTheme:m?"primary":"default",primaryDisabled:!m}))});export{Vo as a,ba as b};
//# sourceMappingURL=chunk-A25JBJ2D.js.map
