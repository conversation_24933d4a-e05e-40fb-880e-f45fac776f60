import{B as u,Ma as y}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import{b as p}from"./chunk-W27Z2YZM.js";import"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{k as m}from"./chunk-OKP6DFCI.js";import{ga as c,o as i,y as o}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{Cb as a}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as d}from"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as A}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f,h as l,n as s}from"./chunk-3KENBVE7.js";l();s();var t=f(A());var O=()=>{let{t:e}=d(),{handleHideModalVisibility:n}=y(),r=(0,t.useCallback)(()=>{n("bitcoinAddressTypes")},[n]),b={icon:t.default.createElement(I,{src:"/images/bitcoin/btc-address-types.png"}),headerTitle:e("headerTitleInfo"),bodyTitle:e("bitcoinAddressTypesBodyTitle"),details:[{icon:t.default.createElement(S,null,t.default.createElement(c,{width:20})),title:e("bitcoinAddressTypesFeature1Title"),subtitle:e("bitcoinAddressTypesFeature1Subtitle")},{icon:t.default.createElement(T,null,t.default.createElement(o,{width:28})),title:e("bitcoinAddressTypesFeature2Title"),subtitle:e("bitcoinAddressTypesFeature2Subtitle")},{icon:t.default.createElement(T,null,t.default.createElement(o,{width:28})),title:e("bitcoinAddressTypesFeature3Title"),subtitle:e("bitcoinAddressTypesFeature3Subtitle")}],FooterComponent:()=>t.default.createElement(m,{primaryText:e("interstitialDismissUnderstood"),onPrimaryClicked:r,secondaryText:e("commandLearnMore"),onSecondaryClicked:()=>p({url:a})}),onDismiss:r};return t.default.createElement(u,{...b})},I=i.img`
  display: block;
  width: 200px;
  margin: -15px auto -10px;
`,S=i.div`
  padding-left: 4px;
`,T=i.div`
  transform: translateY(-5px);
`,B=O;export{O as BitcoinAddressTypesInterstitial,B as default};
//# sourceMappingURL=BitcoinAddressTypes-YY5DLY5F.js.map
