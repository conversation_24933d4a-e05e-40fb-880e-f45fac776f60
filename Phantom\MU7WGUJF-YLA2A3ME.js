import{a as c,b as e,c as s,d as u,e as i,f as m,g as d,h as P,j as v,k as h}from"./chunk-KFRTN7AP.js";import{h as l,n as a}from"./chunk-3KENBVE7.js";l();a();var g=t=>{let[r,o]=u({prefix:"TanstackQueryDevtools"}),C=s(),f=c(()=>{let n=r.theme_preference||i;return n!=="system"?n:C()});return e(m.Provider,{value:t,get children(){return e(d,{disabled:!0,localStore:r,setLocalStore:o,get children(){return e(P.Provider,{value:f,get children(){return e(v,{get children(){return e(h,{localStore:r,setLocalStore:o,get onClose(){return t.onClose},showPanelViewOnly:!0})}})}})}})}})},x=g;export{x as default};
//# sourceMappingURL=MU7WGUJF-YLA2A3ME.js.map
