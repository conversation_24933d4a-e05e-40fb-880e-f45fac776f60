// This script uses <PERSON><PERSON> to call the API endpoint to set the 'verified_tokens' key in Redis

const { request } = require('@playwright/test');
const config = require('./configRedis.json');
async function main() {
  // You can customize the value you want to set here
  const key = 'verified_tokens';
  const value = {
    example: "This is a test value for verified_tokens",
    timestamp: new Date().toISOString()
  };

  // The API expects the value as a JSON string
  const apiUrl = `https://api.dex3.fi/dev/redis?type=set&key=verified_tokens`;
  const headers = {
    'x-api-key': config.redis_search['x-api-key']
  };
  const reqContext = await request.newContext();


  try {
    const response = await reqContext.get(apiUrl, {
      headers
    });
    

    if (response.ok()) {
      const result = await response.json();
      const fs = require('fs');
      const path = require('path');
      const outputPath = path.join(__dirname, 'tokenList.json');
      fs.writeFileSync(outputPath, JSON.stringify(result, null, 2), 'utf8');
      console.log(`Successfully set key '${key}' in Redis via API.`);
      console.log(`Saved response to ${outputPath}`);
    } else {
      console.error(`Failed to set key. Status: ${response.status()}`);
      const text = await response.text();
      console.error('Response:', text);
      process.exitCode = 1;
    }
  } catch (error) {
    console.error('Error calling API:', error.message || error);
    process.exitCode = 1;
  } finally {
    await reqContext.dispose();
  }
}

main();

