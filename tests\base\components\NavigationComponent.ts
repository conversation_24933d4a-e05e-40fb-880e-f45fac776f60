import { Page } from '@playwright/test';
import { BasePage } from '../pages/BasePage';
import { DashboardSelectors } from '../../selectors/DashboardSelectors';
import { CommonSelectors } from '../../selectors/CommonSelectors';

/**
 * NavigationComponent - Handles all navigation operations
 * This component encapsulates navigation-specific functionality
 */
export class NavigationComponent extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Navigate to All Tokens tab
   */
  async navigateToAllTokens(): Promise<void> {
    this.logAction('Navigating to All Tokens tab');
    await this.navigateToTokensList('all');
  }

  /**
   * Navigate to New Pairs tab
   */
  async navigateToNewPairs(): Promise<void> {
    this.logAction('Navigating to New Pairs tab');
    await this.navigateToTokensList('new');
  }

  /**
   * Navigate to Trending tab
   */
  async navigateToTrending(): Promise<void> {
    this.logAction('Navigating to Trending tab');
    await this.navigateToTokensList('trending');
  }

  /**
   * Generic method to navigate to token list with specific tab
   */
  async navigateToTokensList(tab: 'all' | 'new' | 'trending' = 'all'): Promise<void> {
    try {
      this.logAction(`Navigating to token list with tab: ${tab}`);
      
      await this.waitForPageLoad();
      
      // Click on Explore link
      await this.clickElement(CommonSelectors.exploreLink, 'Explore Link');
      await this.waitForPageLoad();
      await this.sleep(1000);

      // Select the appropriate tab
      switch (tab) {
        case 'all':
          this.logAction('All tokens tab is selected by default');
          break;
        case 'new':
          await this.clickElement(DashboardSelectors.newPairsTab, 'New Pairs Tab');
          break;
        case 'trending':
          await this.clickElement(DashboardSelectors.trendingTab, 'Trending Tab');
          break;
      }

      await this.waitForPageLoad();
      this.logAction(`Successfully navigated to ${tab} tab`);
    } catch (error) {
      console.error(`Failed to navigate to ${tab} tab: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Verify navigation tabs functionality
   */
  async verifyNavigationTabs(): Promise<void> {
    this.logAction('Verifying navigation tabs functionality');
    
    await this.waitForPageLoad();
    await this.sleep(2000);

    try {
      // Test New Pairs tab
      this.logAction('Testing New Pairs tab');
      await this.clickElement(DashboardSelectors.newPairsTab, 'New Pairs Tab');
      await this.sleep(2000);

      // Test Trending tab
      this.logAction('Testing Trending tab');
      await this.clickElement(DashboardSelectors.trendingTab, 'Trending Tab');
      await this.sleep(2000);

      // Return to All Tokens tab
      this.logAction('Returning to All Tokens tab');
      await this.clickElement(DashboardSelectors.allTokenTab, 'All Tokens Tab');
      await this.sleep(2000);

      this.logAction('Navigation tabs verification completed');
    } catch (error) {
      console.error(`Navigation tabs verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw error;
    }
  }

  /**
   * Navigate to Moon page
   */
  async navigateToMoon(): Promise<void> {
    this.logAction('Navigating to Moon page');
    await this.clickElement(CommonSelectors.moonLink, 'Moon Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate to Top Wallets page
   */
  async navigateToTopWallets(): Promise<void> {
    this.logAction('Navigating to Top Wallets page');
    await this.clickElement(CommonSelectors.topWalletsLink, 'Top Wallets Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate to Copy Trading page
   */
  async navigateToCopyTrading(): Promise<void> {
    this.logAction('Navigating to Copy Trading page');
    await this.clickElement(CommonSelectors.copyTradingLink, 'Copy Trading Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate to Deep Signals page
   */
  async navigateToDeepSignals(): Promise<void> {
    this.logAction('Navigating to Deep Signals page');
    await this.clickElement(CommonSelectors.deepSignalsLink, 'Deep Signals Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate to Following page
   */
  async navigateToFollowing(): Promise<void> {
    this.logAction('Navigating to Following page');
    await this.clickElement(CommonSelectors.followingLink, 'Following Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate to Holdings page
   */
  async navigateToHoldings(): Promise<void> {
    this.logAction('Navigating to Holdings page');
    await this.clickElement(CommonSelectors.holdingLink, 'Holdings Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate to Referral page
   */
  async navigateToReferral(): Promise<void> {
    this.logAction('Navigating to Referral page');
    await this.clickElement(CommonSelectors.referralLink, 'Referral Link');
    await this.waitForPageLoad();
  }

  /**
   * Navigate back to Dashboard from any page
   */
  async navigateToDashboard(): Promise<void> {
    this.logAction('Navigating back to Dashboard');
    await this.uiActions.getDashboardPage();
  }

  /**
   * Verify all main navigation links are visible
   */
  async verifyMainNavigationLinks(): Promise<void> {
    this.logAction('Verifying main navigation links are visible');
    
    const navigationLinks = [
      { selector: CommonSelectors.exploreLink, name: 'Explore Link' },
      { selector: CommonSelectors.moonLink, name: 'Moon Link' },
      { selector: CommonSelectors.topWalletsLink, name: 'Top Wallets Link' },
      { selector: CommonSelectors.copyTradingLink, name: 'Copy Trading Link' },
      { selector: CommonSelectors.deepSignalsLink, name: 'Deep Signals Link' },
      { selector: CommonSelectors.followingLink, name: 'Following Link' },
      { selector: CommonSelectors.holdingLink, name: 'Holdings Link' },
      { selector: CommonSelectors.referralLink, name: 'Referral Link' }
    ];

    for (const link of navigationLinks) {
      await this.verifyElementVisible(link.selector, link.name);
    }
  }

  /**
   * Verify tab-specific elements are visible
   */
  async verifyTabElements(): Promise<void> {
    this.logAction('Verifying tab elements are visible');
    
    const tabElements = [
      { selector: DashboardSelectors.allTokenTab, name: 'All Tokens Tab' },
      { selector: DashboardSelectors.newPairsTab, name: 'New Pairs Tab' },
      { selector: DashboardSelectors.trendingTab, name: 'Trending Tab' }
    ];

    for (const tab of tabElements) {
      await this.verifyElementVisible(tab.selector, tab.name);
    }
  }
} 