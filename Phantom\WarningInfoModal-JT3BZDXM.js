import{a as p}from"./chunk-ROF5SDVA.js";import{a}from"./chunk-IVMV7P4T.js";import{a as i}from"./chunk-VQVTLSDS.js";import{j as c}from"./chunk-OKP6DFCI.js";import{o,qb as e,wa as m}from"./chunk-WIQ4WVKX.js";import{S as l}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-4P36KWOF.js";import{a as h}from"./chunk-7X4NV6OJ.js";import{f as x,h as r,n}from"./chunk-3KENBVE7.js";r();n();var t=x(h());var y=o.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh;
`,C=o.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex-grow: 1;
`,u=o(e).attrs({size:22,color:i.white,weight:"bold"})`
  margin-top: 16px;
  margin-left: 16px;
  margin-right: 16px;
`,v=o(e).attrs({size:16,color:i.grayLight})`
  margin-top: 8px;
  margin-left: 16px;
  margin-right: 16px;
`,w=({title:s,description:f,buttonText:g,onButtonClick:d})=>t.createElement(y,null,t.createElement(C,null,t.createElement(p,{color:l(i.warning,.1),diameter:94},t.createElement(m,{width:54,height:54,circleFill:i.warning})),t.createElement(u,null,s),t.createElement(v,null,f)),t.createElement(a,{removeFooterExpansion:!0},t.createElement(c,{theme:"primary",onClick:d},g))),E=w;export{w as WarningInfoModal,E as default};
//# sourceMappingURL=WarningInfoModal-JT3BZDXM.js.map
