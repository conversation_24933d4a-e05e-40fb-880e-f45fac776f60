import{a as Bt}from"./chunk-5RLYH252.js";import{a as Ze,b as Re,c as lt,d as Y,e as dt,f as ct,g as gt,h as ht,i as yt,j as vt,l as At}from"./chunk-GLVW5MF5.js";import{a as ut,i as bt}from"./chunk-ELBGM5PY.js";import"./chunk-UM364UVK.js";import"./chunk-SYICDMYM.js";import"./chunk-ESXKWKRD.js";import"./chunk-QZG7YQTK.js";import"./chunk-HKFEOKHC.js";import"./chunk-ZNZZRKNQ.js";import"./chunk-2WECCVZD.js";import"./chunk-O5AAGNHJ.js";import"./chunk-MXORZ3WH.js";import"./chunk-5MF3BU53.js";import"./chunk-WE6RAXEH.js";import"./chunk-VHCQKD7Y.js";import{a as ft}from"./chunk-24U56MUI.js";import"./chunk-RLZITNCL.js";import"./chunk-7A6HLO4U.js";import{a as W}from"./chunk-T27XGMXK.js";import"./chunk-AUOG6CT3.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{Ma as U,Na as st,Q as it,d as je,m as Qe}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import{b as et}from"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import{d as ot,g as rt,j as nt}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-YF76YZSL.js";import"./chunk-XJZOYN2T.js";import"./chunk-ZON27MKP.js";import{a as pt,b as mt,c as wt}from"./chunk-JLLUQF3V.js";import{b as xt}from"./chunk-QSVSNR6K.js";import{h as Xe,m as at}from"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import{b as Fe}from"./chunk-W27Z2YZM.js";import{V as Ie,Y as He,d as G,e as X,fa as Oe}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import{a as Pt}from"./chunk-26OZJBRY.js";import"./chunk-XJTFMD4C.js";import{c as Je}from"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import{a as tt}from"./chunk-VQVTLSDS.js";import{a as T,b as I}from"./chunk-OKP6DFCI.js";import{Z as Ke,a as Ve,b as O,c as De,d as $e,e as We,f as K,g as Ue,i as qe,l as Ge,o as P,wa as Ye}from"./chunk-WIQ4WVKX.js";import{a as Wt,c as _e}from"./chunk-AVT3M45V.js";import{Va as Q,fa as Ne,kb as Le}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import{e as $}from"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{a as Ce,c as _}from"./chunk-MHOQBMVI.js";import{a as ke}from"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{f as ue}from"./chunk-7ZN4F6J4.js";import{Bb as ze,O as we,P as Ae,Q as Be,W as Se,X as Te,wb as Me,yb as Ee}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import{a as Ut}from"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as ye,Kd as fe,Pa as pe,Q as oe,Rd as ge,T as re,_d as he,fe as be,ge as ve,kc as me,ne as xe,oe as Pe,vc as de,yc as ce}from"./chunk-MZZEJ42N.js";import{K as ne}from"./chunk-E3NPIRHS.js";import{m as L}from"./chunk-56SJOU6P.js";import{S as x}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{Ya as j,ab as le,ba as D,ka as ie,qa as ae,ta as se,z as te}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as F}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as S,h as a,n as s}from"./chunk-3KENBVE7.js";a();s();var r=S(F()),$t=S(Wt());a();s();a();s();a();s();var o=S(F());a();s();var b=S(F());var St=({expired:e})=>{let{t}=L(),[i,p]=(0,b.useState)(!1),{data:c=[]}=ge("seedless-seeds-only"),{handleShowModalVisibility:d}=U(),f=c.length>0,g=(0,b.useCallback)(()=>{d("seedlessVerifyPinPage")},[d]);(0,b.useEffect)(()=>{e&&p(!1)},[e]);let m=(0,b.useCallback)(()=>{p(!0)},[]),h=(0,b.useCallback)(()=>{p(!0),g()},[g]);return!f||!e||i?null:b.default.createElement(G,{padding:"screen",position:"absolute",insetX:0,zIndex:10,className:it.toastPosition},b.default.createElement(Ie,{onClose:m,icon:"Info",actions:{children:t("seedlessVerifyPinVerifyButtonText"),onPress:h}},b.default.createElement(G,{gap:8},b.default.createElement(X,{font:"captionSemibold",color:"bgWallet",children:t("seedlessVerifyToastPrimaryText")}),b.default.createElement(X,{font:"caption",color:"bgWallet",children:t("seedlessVerifyToastSecondaryText")}))))};a();s();a();s();a();s();a();s();a();s();async function Tt(e){let t=await oe.api().headers({Accept:"application/json"}).get(`/alert/v1?locale=${e}`);if(!re(t))throw new Error("Failed to retrieve Solana network health");return t.data}function Ct(e,t){return ie({queryKey:["solana","health",{locale:e}],refetchInterval:60*2500,enabled:t,async queryFn(){return await Tt(e)}})}function J(e,t){let{data:i}=Ct(e,t);return t?i:void 0}var u=S(F());a();s();var M=S(F());var kt=(0,M.createContext)(null),Ft=()=>{let e=(0,M.useContext)(kt);if(!e)throw new Error("Missing banner context. Make sure you're wrapping your component in a <BannerProvider />");return e},Mt=({children:e})=>{let t=[],i=(m,h)=>{switch(h.type){case"create":return m.concat(h.payload);case"delete":return m.filter(({id:A})=>A!==h.payload.id);case"reset":return t;default:throw new Error("There was an error dispatching a banner action.")}},[p,c]=(0,M.useReducer)(i,t),d=m=>{let{type:h,variant:A,message:l,dismissable:v=!0,icon:k,autohide:w=!0,delay:E=5e3,onClick:V}=m;(!h||!A||!l)&&console.error("You must supply a type, variant and message when creating a Banner.");let H=ne();return c({type:"create",payload:{id:H,type:h,variant:A,message:l,dismissable:v,icon:k,autohide:w,delay:E,onClick:V}}),w&&setTimeout(()=>{f({id:H})},E),H},f=m=>c({type:"delete",payload:{id:m.id}}),g=()=>c({type:"reset"});return M.default.createElement(kt.Provider,{value:{banners:p,createBanner:d,deleteBanner:f,resetBanners:g}},e)};var Et=P.button`
  cursor: ${e=>e.onClick?"pointer":"default"};
  display: flex;
  align-items: center;
  vertical-align: middle;
  overflow: visible;
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  background-color: transparent;
  width: 100%;
  padding: 10px 16px;

  svg {
    fill: #fff;
    margin-right: 8px;
  }
`,qt=P(T.div)`
  position: relative;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: ${e=>{switch(e.variant){case"primary":return x("#AB9FF2",.7);case"success":return x("#21E56F",.7);case"warning":return x("#E5A221",.7);case"danger":return x("#EB3742",.7);default:return x("#E5A221",.7)}}};

  ${Et} {
    &:focus-visible {
      border-color: ${e=>{switch(e.variant){case"primary":return x("#AB9FF2",.7);case"success":return x("#21E56F",.7);case"warning":return x("#E5A221",.7);case"danger":return x("#EB3742",.7);default:return x("#E5A221",.7)}}};
    }
  }
`,jt=P.p`
  color: #ffffff;
  font-size: 14px;
  font-weight: 500;
  line-height: 19px;
  text-align: left;

  svg {
    margin-right: 10px;
  }
`,Qt=P.button`
  cursor: pointer;
  position: absolute;
  right: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  margin: 0;
  padding: 0;
  overflow: visible;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  border-width: 1px;
  border-style: solid;
  border-color: transparent;
  background-color: transparent;

  &:focus,
  &:focus-visible {
    border-color: ${x("#FFFFFF",.3)};
  }

  svg {
    fill: #ffffff;
    margin: 0;
  }
`,Gt=(e,t,i)=>{let{banners:p,createBanner:c,deleteBanner:d}=Ft(),f=p[p.length-1],{handleShowModalVisibility:g}=U(),{showSettingsMenu:m}=nt(),{t:h,i18n:A}=L(),{cluster:l}=e(),k=ve().some(B=>pe.isSolanaNetworkID(B)),w=J(A.language,k),E=t(),V=i();(0,u.useEffect)(()=>{let B=p.find(z=>z.type==="testnet-mode");B&&d({id:B.id}),V?c({type:"testnet-mode",variant:"warning",message:h("featureNotSupportedOnLocalNet"),dismissable:!1,autohide:!1,onClick:()=>m(void 0,u.default.createElement(Y,null))}):E&&c({type:"testnet-mode",variant:"warning",message:h("connectionClusterTestnetMode"),dismissable:!1,autohide:!1,onClick:()=>m(void 0,u.default.createElement(Y,null))})},[V,E,h]),(0,u.useEffect)(()=>{if(!l)return;let B=p.find(z=>z.type==="network-health");if(l==="mainnet-beta"){if(w){let{bannerVariant:z,bannerMessage:q,notificationMessageTitle:R,notificationMessage:ee}=w;!!z&&!!q?q!==B?.message&&c({type:"network-health",variant:z,message:q,dismissable:!1,icon:u.default.createElement(Ye,{width:14,height:14,circleFill:"#FFFFFF",exclamationFill:"transparent"}),autohide:!1,onClick:ee&&R?()=>g("networkHealth",{variant:z,title:R,message:ee}):void 0}):B&&d({id:B.id})}}else B&&d({id:B.id})},[l,w]);let H=(0,u.useCallback)(()=>{f&&d({id:f.id})},[d,f]);return{banner:f,dismissBanner:H}},Xt=u.default.memo(e=>{let{banner:t,dismissBanner:i}=e;return u.default.createElement(I,null,t&&u.default.createElement(qt,{key:"banner",role:"banner","aria-live":t?.autohide?"assertive":"polite","aria-atomic":"true",variant:t.variant,initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{ease:"easeOut",duration:.2}},u.default.createElement(Et,{tabIndex:t.onClick?1:-1,onClick:t.onClick},t.icon,u.default.createElement(jt,null,t.message)),t.dismissable&&u.default.createElement(Qt,{onClick:i},u.default.createElement(Ke,{width:14,fill:"#FFFFFF"}))))}),Kt=()=>{let e=Gt(ze,xe,Pe);return u.default.createElement(Xt,{...e})},zt=()=>u.default.createElement(Kt,null);a();s();var n=S(F());a();s();var Nt=S(Ut()),C=S(F());var Yt=P(T.div)`
  position: absolute;
  top: 0px;
  left: 0;
  width: 0;
  height: 2px;
  background-color: #ab9ff2;
`,Lt=({refs:e,activeRoute:t,onFinishedAnimating:i,isAnimating:p})=>{let[{x:c,width:d},f]=(0,C.useState)({x:0,width:0}),g=(0,C.useCallback)(()=>{e&&e[t]&&e[t].current&&f({x:e[t].current.offsetLeft,width:e[t].current.getBoundingClientRect().width})},[t,e]);return(0,C.useEffect)(()=>{g()},[t,e,g]),(0,C.useEffect)(()=>{let m=(0,Nt.default)(()=>{g()},500);return self.addEventListener("resize",m),()=>{self.removeEventListener("resize",m)}}),C.default.createElement(Yt,{animate:{x:c,width:d},style:{opacity:p?1:0},onAnimationComplete:i,transition:{duration:.4,type:"spring"}})};var _t=.1,It=10,Jt=60,Zt=P.div`
  position: relative;
  height: ${Jt}px;
  display: flex;
`,Rt=P(T.div)`
  flex: 1;
  overflow-x: hidden;
  padding: ${({padding:e})=>typeof e=="number"?e:16}px;
`,eo=P(ut)`
  flex: 1;
  display: flex;
  justify-content: space-around;
  padding: 0px 10px;
`,Ot=n.default.memo(({items:e})=>{let t=O(),i=ot(t),[p,c]=(0,n.useState)(!1),d=(0,n.useMemo)(()=>e.find(l=>Ve({path:`/${l.route}`,end:!0},t.pathname)),[e,t.pathname]),f=d&&d.route,g=(0,n.useMemo)(()=>e.reduce((l,v)=>(l[v.route]=(0,n.createRef)(),l),{}),[e]),m=t.pathname!=i?.pathname&&i?.pathname!=null,h=(0,n.useMemo)(()=>e.map(l=>{let v=n.default.memo(()=>{let k=0;return m&&(k=to(e,t.pathname,i?.pathname??"")?It:-It),n.default.createElement(Rt,{id:"tab-content","data-testid":`tab-content-${l.route}`,initial:{x:k,opacity:0},animate:{x:0,opacity:1},exit:{opacity:0},transition:{duration:_t},padding:l.padding},n.default.createElement(rt,{shouldResetOnAccountChange:!0},l.renderContent()))});return n.default.createElement(K,{key:l.route,path:`/${l.route}`,element:n.default.createElement(v,null)})}),[e,t]),A=(0,n.useCallback)(l=>{c(!0),_.capture("tabPress",{data:{target:l}}),se.addBreadcrumb("generic",`Tab changed to ${l}`,"info")},[]);return n.default.createElement(n.default.Fragment,null,n.default.createElement(I,{mode:"wait",initial:!1},n.default.createElement(Ue,{location:t,key:t.pathname},h,n.default.createElement(K,{key:"redirection",element:n.default.createElement(T.div,{exit:{opacity:0},transition:{duration:_t}},n.default.createElement(We,{to:e[0]?e[0].route:"/"}))}))),n.default.createElement(Zt,null,n.default.createElement(Lt,{refs:g,activeRoute:f,onFinishedAnimating:()=>c(!1),isAnimating:p}),n.default.createElement(eo,{role:"tablist","aria-orientation":"horizontal"},e.map(l=>n.default.createElement(ro,{isActive:f===l.route,key:l.route,item:l,ref:g[l.route],isAnimating:p,onClick:()=>A(l.route)})))),n.default.createElement("div",{"aria-hidden":!0,"data-testid":"current-route","data-location":t.pathname}))},(e,t)=>Ee(e.items.map(i=>i.route),t.items.map(i=>i.route))),to=(e,t,i)=>{let p=e.findIndex(d=>d.route===Ht(t)),c=e.findIndex(d=>d.route===Ht(i));return p>c},Ht=e=>e==="/"?e:e.replace(/^\/+/g,""),oo=P(qe)`
  display: block;
  padding: 15px 0px;
  margin: 0px 12px;
  position: relative;
  width: 100%;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  :hover {
  }
  :after {
    content: "";
    position: absolute;
    top: -1px;
    left: 0;
    height: 2px;
    width: 100%;
    border-radius: 2px;
    ${e=>e.$isActive&&!e.$isAnimating&&"background-color: #AB9FF2;"}
    ${e=>e.$isAnimating&&"background-color: transparent;"}
  }
`,ro=(0,n.forwardRef)(({isActive:e,item:t,isAnimating:i,onClick:p},c)=>n.default.createElement(oo,{"aria-label":t.label,"data-testid":`bottom-tab-nav-button-${t.route}`,$isActive:e,$isAnimating:i,to:t.route,ref:c,onClick:p},t.renderButton({isActive:e})));var no=o.default.lazy(()=>import("./HomeTabPage-6SNOPNNY.js")),io=o.default.lazy(()=>import("./CollectionsPage-5CFTZAXN.js")),ao=o.default.lazy(()=>import("./SwapTabPage-5MR26SMB.js")),so=o.default.lazy(()=>import("./RecentActivity-74VOXUHW.js")),lo=o.default.lazy(()=>import("./ExploreTabPage-SIZXN23V.js")),po=o.default.lazy(()=>import("./HomeHeaderRightButtons-NMSVPO5Z.js")),mo=o.default.lazy(()=>import("./SwapSettingsButton-45IT3L2V.js")),Vt=()=>{let{data:e=[]}=fe();be();let{data:[t]}=j(["frontend-enable-session-start"]),{mutateAsync:i}=ue();W(()=>{lt.onAppSessionStart(e)},e.length>0&&t);let{mutate:p}=Ne();return W(()=>{p()},!0),W(()=>{i()},te),o.default.createElement(o.default.Fragment,null,o.default.createElement(co,null),o.default.createElement(zt,null),o.default.createElement(uo,null),o.default.createElement("div",{id:$}))},co=()=>{let{pathname:e}=O(),t=(0,o.useMemo)(()=>e==="/swap"?o.default.createElement(mo,null):e==="/"?o.default.createElement(po,null):null,[e]);return o.default.createElement(ct,{rightMenuButton:t})},uo=()=>{let{data:e}=he(),{data:t}=ye(),{data:[i]}=j(["enable-pin-verification-timer"]),{data:p}=Qe(i),c=t?.isReadOnly,d=D.isFeatureEnabled("kill-swapper")||c,f=D.isFeatureEnabled("kill-explore"),g=D.isFeatureEnabled("kill-collectibles"),{t:m}=L(),{pathname:h}=O(),A=De(),{closeAllModals:l}=et();Me(),(0,o.useEffect)(()=>{l(),h!=="/"&&A("/")},[e]);let v=(0,o.useCallback)(w=>({isActive:E})=>o.default.createElement(Oe,{animationName:w,isActive:E}),[]),k=(0,o.useMemo)(()=>[{label:m("homeTab"),route:"/",renderButton:v("tabBarHome"),renderContent:()=>o.default.createElement(no,null),padding:0},g?null:{label:m("collectiblesTab"),route:"/collectibles",renderButton:v("tabBarCollectibles"),renderContent:()=>o.default.createElement(io,null)},d?null:{label:m("swapTab"),route:"/swap",padding:0,renderButton:v("tabBarSwapper"),renderContent:()=>o.default.createElement(ao,null)},{label:m("activityTab"),route:"/notifications",renderButton:v("tabBarActivity"),renderContent:()=>o.default.createElement(so,null)},f?null:{label:m("exploreTab"),route:"/explore",renderButton:v("tabBarExplore"),renderContent:()=>o.default.createElement(lo,null),padding:0}].filter(w=>w!==null),[g,f,d,m,v]);return o.default.createElement(o.Suspense,null,o.default.createElement(St,{expired:p?.expired??!1}),o.default.createElement(Ot,{items:k}))};a();s();var N=S(F());a();s();var Z={container:"_1nscgt61 _51gazn1ar _51gazn1b4 _51gaznqm _51gaznvd _51gaznqp _51gaznt1 _51gaznxp _51gazn18w",notification:"_1nscgt63 _51gaznn0 _51gaznny _51gaznpu _51gaznow _51gaznbp _51gazn9a _51gazne4 _51gazn6v _51gaznkx _51gaznk3 _51gaznlr _51gaznj9 _51gaznqg _51gaznqe _51gaznqi _51gaznqc _51gazn2t2 _51gazn2jw _51gazn21k _51gazn2aq _51gazn1k2"};var fo=()=>{let e=bt();if(!e)return null;let t={initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.5}},i={initial:{y:-40,opacity:0},animate:{y:0,opacity:1},exit:{y:40,opacity:0},transition:{duration:.5,ease:[.5,0,.2,1]}};return N.createElement(I,{mode:"wait"},N.createElement(T.div,{className:Z.container,...t},N.createElement(T.div,{className:Z.notification,key:e?.req?.id||"sidepanel",...i},N.createElement(At,null))))},Dt=()=>{let{isSidebarOpen:e}=ft();return e?N.createElement(vt,null,N.createElement(fo,null)):null};ke();ae.init({provider:Pt});_e();Be(new Ce);we((e,t)=>Se(e,t,_));Ae(Te);var go=()=>{(0,r.useEffect)(()=>{_.capture("popupOpen")},[]);let e=(0,r.useCallback)(()=>{Fe({url:"onboarding.html"}),self.close()},[]);return r.default.createElement(r.default.Fragment,null,r.default.createElement($e,{future:{v7_startTransition:!0}},r.default.createElement(Ge,{theme:tt},r.default.createElement(He,null,r.default.createElement(wt,{backgroundColor:"#222222"}),r.default.createElement(Je,null,r.default.createElement(Ze,{withBorder:!0},r.default.createElement(xt,null,r.default.createElement(Xe,null,r.default.createElement(de,{analytics:_},r.default.createElement(Mt,null,r.default.createElement(Le,null,r.default.createElement(le,null,r.default.createElement(me,{authRepository:at},r.default.createElement(ce,{userRepository:mt,claimUsernameSigner:pt},r.default.createElement(je,{seedlessRepository:Bt},r.default.createElement(yt,{openOnboarding:e},r.default.createElement(Q,null,r.default.createElement(gt,null,r.default.createElement(st,null,r.default.createElement(ht,null,r.default.createElement(dt,null,r.default.createElement(Vt,null)))))))),r.default.createElement(Dt,null)))))),r.default.createElement("div",{id:$}),r.default.createElement(Re,null))))))))))},ho=document.getElementById("root"),yo=(0,$t.createRoot)(ho);yo.render(r.default.createElement(go,null));
//# sourceMappingURL=Popup.js.map
