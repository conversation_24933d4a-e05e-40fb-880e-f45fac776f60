import { defineConfig, devices } from '@playwright/test';
import path from 'path';

export default defineConfig({
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 1, // Thêm 1 lần retry ngay cả khi chạy local
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',

  // Thêm global setup và teardown
  globalSetup: require.resolve('./global-setup'),
  globalTeardown: require.resolve('./global-teardown'),

  use: {
    baseURL: 'https://dex3.ai/pump-to-moon',
    trace: 'on-first-retry',

    // Tăng timeout mặc định
    actionTimeout: 30000,
    navigationTimeout: 30000,

    // Chụp màn hình khi test thất bại
    screenshot: 'only-on-failure',

    // Sử dụng storageState đã lưu từ global-setup
    storageState: path.join(__dirname, 'storageState.json'),
  },

  projects: [
    {
      name: 'chromium',
      use: {
        ...devices['Desktop Chrome'],
        // Phantom extension path phải được cấu hình riêng cho mỗi project
        launchOptions: {
          args: [
            `--disable-extensions-except=${process.env.PHANTOM_EXTENSION_PATH || 'C:\\Users\\<USER>\\Downloads\\Phantom'}`,
            `--load-extension=${process.env.PHANTOM_EXTENSION_PATH || 'C:\\Users\\<USER>\\Downloads\\Phantom'}`,
          ],
        }
      },
    },
  ],

  // Cài đặt cho timeouts
  timeout: 60000, // 660 giây cho mỗi test
  expect: {
    timeout: 10000, // 10 giây cho mỗi expect
    toHaveScreenshot: {
      maxDiffPixels: 10,
    },
    toMatchSnapshot: {
      maxDiffPixelRatio: 0.1,
    },
  },
});
