export const StocksSelectors = {
  // Page structure
  pageContainer: '[data-testid="stocks-page"], .stocks-container, main',
  headerTitle: 'h1, [data-testid="page-title"], .page-title',
  loadingSpinner: '[data-testid="loading"], .loading-spinner, .spinner',
  errorMessage: '[data-testid="error"], .error-message, .alert-error',
  
  // Navigation and filters
  stocksNavigation: '[data-testid="stocks-nav"], .stocks-navigation',
  allStocksTab: '[data-testid="all-stocks-tab"], button:has-text("All Stocks"), a[href*="all"]',
  trendingTab: '[data-testid="trending-tab"], button:has-text("Trending"), a[href*="trending"]',
  gainersTab: '[data-testid="gainers-tab"], button:has-text("Gainers"), a[href*="gainers"]',
  losersTab: '[data-testid="losers-tab"], button:has-text("Losers"), a[href*="losers"]',
  
  // Search and filters
  searchInput: '[data-testid="stock-search"], input[placeholder*="Search"], input[placeholder*="stock"]',
  searchButton: '[data-testid="search-button"], button:has-text("Search")',
  filterButton: '[data-testid="filter-button"], button:has-text("Filter")',
  sortDropdown: '[data-testid="sort-dropdown"], select[name="sort"]',
  
  // Stock list
  stocksList: '[data-testid="stocks-list"], .stocks-list, .stock-grid',
  stockItem: '[data-testid="stock-item"], .stock-item, .stock-card',
  stockSymbol: '[data-testid="stock-symbol"], .stock-symbol, .symbol',
  stockName: '[data-testid="stock-name"], .stock-name, .company-name',
  stockPrice: '[data-testid="stock-price"], .stock-price, .price',
  stockChange: '[data-testid="stock-change"], .stock-change, .change',
  stockPercentChange: '[data-testid="stock-percent-change"], .percent-change',
  stockVolume: '[data-testid="stock-volume"], .volume',
  
  // Stock details
  stockDetailModal: '[data-testid="stock-detail-modal"], .stock-detail-modal, .modal',
  stockChart: '[data-testid="stock-chart"], .chart-container, canvas',
  buyButton: '[data-testid="buy-button"], button:has-text("Buy")',
  sellButton: '[data-testid="sell-button"], button:has-text("Sell")',
  addToWatchlistButton: '[data-testid="add-watchlist"], button:has-text("Add to Watchlist")',
  
  // Watchlist
  watchlistSection: '[data-testid="watchlist"], .watchlist-section',
  watchlistItem: '[data-testid="watchlist-item"], .watchlist-item',
  removeFromWatchlistButton: '[data-testid="remove-watchlist"], button:has-text("Remove")',
  
  // Market data
  marketSummary: '[data-testid="market-summary"], .market-summary',
  marketIndex: '[data-testid="market-index"], .market-index',
  marketValue: '[data-testid="market-value"], .market-value',
  
  // Pagination
  pagination: '[data-testid="pagination"], .pagination',
  previousButton: '[data-testid="prev-button"], button:has-text("Previous")',
  nextButton: '[data-testid="next-button"], button:has-text("Next")',
  pageNumber: '[data-testid="page-number"], .page-number',
  
  // Common elements
  refreshButton: '[data-testid="refresh"], button:has-text("Refresh")',
  settingsButton: '[data-testid="settings"], button:has-text("Settings")',
  helpButton: '[data-testid="help"], button:has-text("Help")',
  
  // Dynamic selectors
  stockBySymbol: (symbol: string) => `[data-symbol="${symbol}"], [data-testid="stock-${symbol}"]`,
  stockPriceBySymbol: (symbol: string) => `[data-testid="price-${symbol}"], [data-symbol="${symbol}"] .price`,
  stockChangeBySymbol: (symbol: string) => `[data-testid="change-${symbol}"], [data-symbol="${symbol}"] .change`,
}; 