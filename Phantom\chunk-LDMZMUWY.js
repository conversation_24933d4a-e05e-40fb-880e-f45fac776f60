import{c as $,h as S,n as h}from"./chunk-3KENBVE7.js";var Y=$((w,L)=>{S();h();var A="Expected a function",k=NaN,B="[object Symbol]",F=/^\s+|\s+$/g,R=/^[-+]0x[0-9a-f]+$/i,P=/^0b[01]+$/i,_=/^0o[0-7]+$/i,D=parseInt,G=typeof self=="object"&&self&&self.Object===Object&&self,H=typeof self=="object"&&self&&self.Object===Object&&self,U=G||H||Function("return this")(),X=Object.prototype,q=X.toString,z=Math.max,J=Math.min,j=function(){return U.Date.now()};function K(e,t,i){var a,u,b,c,r,o,s=0,v=!1,l=!1,g=!0;if(typeof e!="function")throw new TypeError(A);t=E(t)||0,T(i)&&(v=!!i.leading,l="maxWait"in i,b=l?z(E(i.maxWait)||0,t):b,g="trailing"in i?!!i.trailing:g);function y(n){var f=a,d=u;return a=u=void 0,s=n,c=e.apply(d,f),c}function C(n){return s=n,r=setTimeout(m,t),v?y(n):c}function M(n){var f=n-o,d=n-s,O=t-f;return l?J(O,b-d):O}function x(n){var f=n-o,d=n-s;return o===void 0||f>=t||f<0||l&&d>=b}function m(){var n=j();if(x(n))return I(n);r=setTimeout(m,M(n))}function I(n){return r=void 0,g&&a?y(n):(a=u=void 0,c)}function N(){r!==void 0&&clearTimeout(r),s=0,a=o=u=r=void 0}function W(){return r===void 0?c:I(j())}function p(){var n=j(),f=x(n);if(a=arguments,u=this,o=n,f){if(r===void 0)return C(o);if(l)return r=setTimeout(m,t),y(o)}return r===void 0&&(r=setTimeout(m,t)),c}return p.cancel=N,p.flush=W,p}function T(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function Q(e){return!!e&&typeof e=="object"}function V(e){return typeof e=="symbol"||Q(e)&&q.call(e)==B}function E(e){if(typeof e=="number")return e;if(V(e))return k;if(T(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=T(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=e.replace(F,"");var i=P.test(e);return i||_.test(e)?D(e.slice(2),i?2:8):R.test(e)?k:+e}L.exports=K});export{Y as a};
//# sourceMappingURL=chunk-LDMZMUWY.js.map
