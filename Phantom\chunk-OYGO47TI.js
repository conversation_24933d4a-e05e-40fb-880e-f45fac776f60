import{Pa as z,Sa as V}from"./chunk-MZZEJ42N.js";import{x as f}from"./chunk-E3NPIRHS.js";import{l as J}from"./chunk-ALUTR72U.js";import{a as i}from"./chunk-4P36KWOF.js";import{a as ht}from"./chunk-7X4NV6OJ.js";import{c as gt,d as yt,f as N,h as c,i as u,n as l}from"./chunk-3KENBVE7.js";var U=gt((H,R)=>{c();l();(function(e,t){"use strict";typeof define=="function"&&define.amd?define(t):typeof R=="object"&&R.exports?R.exports=t():e.log=t()})(H,function(){"use strict";var e=function(){},t="undefined",a=typeof self!==t&&typeof self.navigator!==t&&/Trident\/|MSIE /.test(self.navigator.userAgent),n=["trace","debug","info","warn","error"],s={},o=null;function g(d,y){var r=d[y];if(typeof r.bind=="function")return r.bind(d);try{return Function.prototype.bind.call(r,d)}catch{return function(){return Function.prototype.apply.apply(r,[d,arguments])}}}function S(){console.log&&(console.log.apply?console.log.apply(console,arguments):Function.prototype.apply.apply(console.log,[console,arguments])),console.trace&&console.trace()}function T(d){return d==="debug"&&(d="log"),typeof console===t?!1:d==="trace"&&a?S:console[d]!==void 0?g(console,d):console.log!==void 0?g(console,"log"):e}function P(){for(var d=this.getLevel(),y=0;y<n.length;y++){var r=n[y];this[r]=y<d?e:this.methodFactory(r,d,this.name)}if(this.log=this.debug,typeof console===t&&d<this.levels.SILENT)return"No console available for logging"}function ot(d){return function(){typeof console!==t&&(P.call(this),this[d].apply(this,arguments))}}function ct(d,y,r){return T(d)||ot.apply(this,arguments)}function K(d,y){var r=this,C,k,A,m="loglevel";typeof d=="string"?m+=":"+d:typeof d=="symbol"&&(m=void 0);function dt(p){var h=(n[p]||"silent").toUpperCase();if(!(typeof r===t||!m)){try{r.localStorage[m]=h;return}catch{}try{r.document.cookie=encodeURIComponent(m)+"="+h+";"}catch{}}}function G(){var p;if(!(typeof r===t||!m)){try{p=r.localStorage[m]}catch{}if(typeof p===t)try{var h=r.document.cookie,B=encodeURIComponent(m),Y=h.indexOf(B+"=");Y!==-1&&(p=/^([^;]+)/.exec(h.slice(Y+B.length+1))[1])}catch{}return r.levels[p]===void 0&&(p=void 0),p}}function pt(){if(!(typeof r===t||!m)){try{r.localStorage.removeItem(m)}catch{}try{r.document.cookie=encodeURIComponent(m)+"=; expires=Thu, 01 Jan 1970 00:00:00 UTC"}catch{}}}function _(p){var h=p;if(typeof h=="string"&&r.levels[h.toUpperCase()]!==void 0&&(h=r.levels[h.toUpperCase()]),typeof h=="number"&&h>=0&&h<=r.levels.SILENT)return h;throw new TypeError("log.setLevel() called with invalid level: "+p)}r.name=d,r.levels={TRACE:0,DEBUG:1,INFO:2,WARN:3,ERROR:4,SILENT:5},r.methodFactory=y||ct,r.getLevel=function(){return A??k??C},r.setLevel=function(p,h){return A=_(p),h!==!1&&dt(A),P.call(r)},r.setDefaultLevel=function(p){k=_(p),G()||r.setLevel(p,!1)},r.resetLevel=function(){A=null,pt(),P.call(r)},r.enableAll=function(p){r.setLevel(r.levels.TRACE,p)},r.disableAll=function(p){r.setLevel(r.levels.SILENT,p)},r.rebuild=function(){if(o!==r&&(C=_(o.getLevel())),P.call(r),o===r)for(var p in s)s[p].rebuild()},C=_(o?o.getLevel():"WARN");var W=G();W!=null&&(A=_(W)),P.call(r)}o=new K,o.getLogger=function(y){if(typeof y!="symbol"&&typeof y!="string"||y==="")throw new TypeError("You must supply a name when creating a logger.");var r=s[y];return r||(r=s[y]=new K(y,o.methodFactory)),r};var lt=typeof self!==t?self.log:void 0;return o.noConflict=function(){return typeof self!==t&&self.log===o&&(self.log=lt),o},o.getLoggers=function(){return s},o.default=o,o})});c();l();c();l();var X=mt;function mt(e){var t,a,n;if(e){if(Array.isArray(e)){for(t=[],a=e.length,n=0;n<a;n++){var s=e[n];s!=null&&t.push(s)}return t}if(typeof e=="object"){t={};var o=Object.keys(e);for(a=o.length,n=0;n<a;n++){var g=o[n],S=e[g];S!=null&&(t[g]=S)}return t}}}var tt=N(U());c();l();var ft=i.object({chainType:i.union([i.literal("bip122_p2wpkh"),i.literal("bip122_p2tr")]),chainName:i.literal("bitcoin"),networkId:i.union([i.literal("128"),i.literal("239")])}),Dt=i.object({chainType:i.literal("eip155"),chainName:i.literal("ethereum"),networkId:i.union([i.literal("1"),i.literal("5"),i.literal("11155111")])}),Tt=i.object({chainType:i.literal("eip155"),chainName:i.literal("polygon"),networkId:i.union([i.literal("137"),i.literal("80002")])}),vt=i.object({chainType:i.literal("eip155"),chainName:i.literal("base"),networkId:i.union([i.literal("8453"),i.literal("84532")])}),St=i.object({chainType:i.literal("eip155"),chainName:i.literal("monad"),networkId:i.union([i.literal("143"),i.literal("10143"),i.literal("41454")])}),Q=i.object({chainType:i.literal("solana"),chainName:i.literal("solana"),networkId:i.union([i.literal("mainnet"),i.literal("testnet"),i.literal("devnet"),i.literal("localnet")])}),Z=i.union([ft,Dt,vt,St,Tt,Q]);var L=()=>["t","y","1","true","yes"].some(e=>u.DISABLE_ANALYTICS?.toLowerCase()===e)??!1;function M(e){if(!e)return;let t=e.data??{},a=e.data?.asset??{};try{t=JSON.parse(JSON.stringify(t)),a=JSON.parse(JSON.stringify(a))}catch(s){tt.default.error("Failed to parse analytics payload",s);return}let n={...t,...e.action?{action:e.action}:{},...a&&typeof a=="object"?a:{},...e.type?{type:e.type}:{}};if("chainID"in n&&!("chainType"in n)&&!("chainName"in n)&&!("networkId"in n)){let s=V.safeParse(n.chainID);s.success&&Object.assign(n,et(s.data))}return X(n)}function et(e){let t=z.getChainDefinition(e);return Z.parse({chainType:t.addressTypes[0],chainName:t.id,networkId:t.networks[e].value.toString()})}function At(e){switch(e){case"mainnet-beta":return"solana:101";case"devnet":return"solana:103";case"testnet":return"solana:102";case"localhost":return"solana:localnet";default:return J(e)}}c();l();c();l();var O=u.RUDDERSTACK_DATAPLANE??"https://data.phantom.app",wt=u.RUDDERSTACK_KEY_EXT,It=u.RUDDERSTACK_KEY_WEB;c();l();function b(e,{action:t,data:a}){let n={action:t,data:a};return e.capture("actionApprovedByUser",n)}function x(e,{action:t,data:a}){let n={action:t,data:{...a,chainId:a.chainId??a.networkId}};return e.capture("actionDeniedByUser",n)}function q(e,{action:t,data:a}){let n={type:"transaction",data:{...a,chainId:a.chainId??a.networkId},action:t};return e.capture("transactionSubmitted",n)}function Et(e,{action:t,data:a}){let n={data:{...a,chainId:a.chainId??a.networkId},action:t};return e.capture("transactionStatus",n)}function $(e,t){let a={type:"message",data:{...t,chainId:t.chainId??t.networkId}};return e.capture("transactionSubmitted",a)}var D=class{constructor(t,a){this.approved=t=>{b(this.analytics,{action:this.action,data:{...t,chainId:t.chainId??t.networkId}})};this.denied=async t=>{x(this.analytics,{action:this.action,data:{...t,chainId:t.chainId??t.networkId}})};this.submittedTransaction=async(t,a)=>{a.ids||(a.ids=[t]),q(this.analytics,{action:this.action,data:{...a,chainId:a.chainId??a.networkId}})};this.signedMessage=async t=>{$(this.analytics,{...t,chainId:t.chainId??t.networkId})};this.transactionStatus=async(t,a)=>{a.ids||(a.ids=[t]),Et(this.analytics,{action:this.action,data:{...a,chainId:a.chainId??a.networkId}})};this.analytics=t,this.action=a}};async function at(e,t,a){let{origin:n}=t;switch(e.method){case"eth_sendTransaction":{let[s]=e.params,o=f(s.chainId);return o?.chainType!=="eip155"?{success:!1,error:`Unsupported Ethereum chain ID (${s.chainId}) for method "${e.method}"`}:{success:!0,data:{...o,origin:n,method:e.method,gas:s.gas,maxFeePerGas:s.maxFeePerGas,maxPriorityFeePerGas:s.maxPriorityFeePerGas}}}case"sol_signTransaction":{let s=f(t.chainId);return s?.chainType!=="solana"?{success:!1,error:`Unsupported Solana chain ID (${t.chainId}) for method "${e.method}"`}:{success:!0,data:{...s,origin:n,method:"signTransaction",ids:a}}}case"sol_signAndSendTransaction":{let s=f(t.chainId);return s?.chainType!=="solana"?{success:!1,error:`Unsupported Solana chain ID (${t.chainId}) for method "${e.method}"`}:{success:!0,data:{...s,origin:n,method:"signAndSendTransaction",ids:a}}}case"sol_signAndSendAllTransactions":{let s=f(t.chainId);return s?.chainType!=="solana"?{success:!1,error:`Unsupported Solana chain ID (${t.chainId}) for method "${e.method}"`}:{success:!0,data:{...s,origin:n,method:"signAndSendAllTransactions",ids:a}}}case"sol_signAllTransactions":{let s=f(t.chainId);return s?.chainType!=="solana"?{success:!1,error:`Unsupported Solana chain ID (${t.chainId}) for method "${e.method}"`}:{success:!0,data:{...s,origin:n,method:"signAllTransactions",ids:a}}}}}async function nt(e,t){let{origin:a}=t;switch(e.method){case"eth_sign":case"personal_sign":{let n=t.chainId,s=f(n);return s?.chainType!=="eip155"?{success:!1,error:`Unsupported Ethereum chain ID (${n}) for method "${e.method}"`}:{success:!0,data:{...s,method:e.method,origin:a}}}case"eth_signTypedData":{let[n]=e.params,s=t.chainId;for(let g of n)if(g.name==="chainId"){s=g.value;break}let o=f(s);return o?.chainType!=="eip155"?{success:!1,error:`Unsupported Ethereum chain ID (${s}) for method "${e.method}"`}:{success:!0,data:{...o,method:e.method,origin:a}}}case"eth_signTypedData_v3":{let[n,s]=e.params,o=s.domain.chainId??t.chainId,g=f(o);return g?.chainType!=="eip155"?{success:!1,error:`Unsupported Ethereum chain ID (${o}) for method "${e.method}"`}:{success:!0,data:{...g,method:e.method,origin:a}}}case"eth_signTypedData_v4":{let[n,s]=e.params,o=t.chainId;(typeof s.domain.chainId=="string"||typeof s.domain.chainId=="number")&&(o=s.domain.chainId);let g=f(o);return g?.chainType!=="eip155"?{success:!1,error:`Unsupported Ethereum chain ID (${o}) for method "${e.method}"`}:{success:!0,data:{...g,method:e.method,origin:a}}}case"sol_signIn":case"sol_signMessage":{let n=t.chainId,s=f(n);return s?.chainType!=="solana"?{success:!1,error:`Unsupported Solana chain ID (${n}) for method "${e.method}"`}:{success:!0,data:{...s,method:e.method==="sol_signIn"?"signIn":"signMessage",origin:a}}}}}c();l();c();l();var v=N(U());var Pt=["enable-blocklist-metrics","enable-blocklist-blowfish-logonly","enable-force-upgrade"],w="[Analytics Log]",I=class{constructor(t){this.storage=t}async capture(t,a){v.default.debug(w,"Capture Event:",t,M(a))}async getFeatureFlags(){let{ENABLED_FLAGS:t,featureFlags:a,getFeatureFlagOverrides:n}=await import("./src-C6FNEN3O.js"),s=a.filter(T=>!T.startsWith("kill")&&!Pt.includes(T)),o=Object.fromEntries(s.map(T=>[T,!0])),g=await n(this.storage),S=Object.fromEntries(t.map(T=>[T,!0]));return{...o,...S,...g}}async addUserProperties(t){v.default.debug(w,"User Properties:",JSON.stringify(t))}async identify(t,a){v.default.debug(w,"Identify, accountID:",t," traits:",a)}async getDeviceId(){return"mock-device-id"}setDeviceId(t){v.default.debug(w,`SetDeviceId: ${t}`)}async setAnalyticsOptedOut(t){v.default.debug(w,`SetAnalyticsOptedOut: ${t}`)}async setDisplayLanguage(t){v.default.debug(w,`SetDisplayLanguage: ${t}`)}};var st=()=>O,_t=({storage:e,rudderStackKey:t,createAnalytics:a})=>{if(!t)throw new Error("RUDDERSTACK_KEY_[*] env variables required.");let n=st();if(!n)throw new Error("[*]_RUDDERSTACK_DATAPLANE env variables required.");return L()?new I(e):a(t,n)};c();l();c();l();var bt="burn",rt=e=>new D(e,bt);c();l();var xt="send",it=e=>new D(e,xt);var j={};yt(j,{action:()=>F,createDappAnalytics:()=>Ct,userApprovedAction:()=>Bt,userDeniedAction:()=>Rt});c();l();var F="dapp",Ct=e=>new D(e,F);async function Bt(e,t){return await b(e,{action:F,data:{...t,chainId:t.networkId}})}async function Rt(e,t){return await x(e,{action:F,data:{...t,chainId:t.networkId}})}var Ft=e=>({send:it(e),burn:rt(e)});c();l();async function E(){}var kt=()=>({capture:E,getFeatureFlags:async()=>({}),addUserProperties:E,identify:E,getDeviceId:async()=>"",setDeviceId:E,setAnalyticsOptedOut:E,setDisplayLanguage:E,rudderClient:{}});c();l();c();l();var Nt=N(ht());export{wt as a,q as b,$ as c,D as d,at as e,nt as f,M as g,et as h,At as i,_t as j,j as k,Ft as l,kt as m};
//# sourceMappingURL=chunk-OYGO47TI.js.map
