import { type Page, expect } from "@playwright/test"
import { ENV } from "../base/env"
import { DashboardSelectors } from "../selectors/DashboardSelectors"
import { CommonSelectors } from "../selectors/CommonSelectors"
import UIActions from "../base/actions/UIActions"
import { BrowserContext } from "@playwright/test"
import * as path from 'path'

/**
 * DashboardPage - Mô hình Page Object cho trang Dashboard của Dex3
 * Kết hợp những ưu điểm của cả hai phiên bản trước đó
 */
export class DashboardPage {
  private uiActions: UIActions;
  private processedNotificationPages: Set<string> = new Set();

  constructor(page: Page) {
    this.uiActions = new UIActions(page);
  }

  /**
   * Import ví Phantom bằng private key
   * @returns Promise<void>
   */
  async importWallet(): Promise<void> {
    try {
      console.log("Bắt đầu import ví...");
      const walletName = "Ví Test";
      const password = "12345678";

      await this.uiActions.element(DashboardSelectors.importWalletBtn, "Import Wallet Button").click();
      await this.uiActions.element(DashboardSelectors.importPrivateKeyBtn, "Import Private Key Button").click();
      await this.uiActions.editBox(DashboardSelectors.walletNameInput, "Wallet Name Input").fill(walletName);
      await this.uiActions.editBox(DashboardSelectors.privateKeyInput, "Private Key Input").fill(ENV.PRIVATE_KEY_PHANTOM);
      await this.uiActions.element(DashboardSelectors.importBtn, "Import Button").click();
      await this.uiActions.editBox(DashboardSelectors.passwordInput, "Password Input").fill(password);
      await this.uiActions.editBox(DashboardSelectors.confirmPasswordInput, "Confirm Password Input").fill(password);
      await this.uiActions.element(DashboardSelectors.agreeCheckbox, "Agree Checkbox").click();
      await this.uiActions.element(DashboardSelectors.continueBtn, "Continue Button").click();
      await this.uiActions.element(DashboardSelectors.getStartedBtn, "Get Started Button").click();
      console.log("Ví được import thành công.");
    } catch (error) {
      console.error("Lỗi khi import ví:", error instanceof Error ? error.message : "Lỗi không xác định");
      throw new Error(`Không thể import ví: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
    }
  }

  /**
   * Đăng nhập vào Dex3 và xử lý các popup xác nhận từ Phantom
   * @returns Promise<void>
   */
  async loginDex3(): Promise<void> {
    try {
      console.log("Bắt đầu đăng nhập vào Dex3...");
      await this.uiActions.waitForLoadState();

      // Khởi tạo quá trình đăng nhập với Phantom
      console.log("Click vào icon Phantom để đăng nhập...");
      await this.uiActions.element(DashboardSelectors.phantomBtn, "Phantom Icon").click();

      // Xử lý các popup xác nhận
      await this.handleNotificationPopups();

      // Xác minh trạng thái đăng nhập
      await this.verifyLoginState();
      console.log("Đăng nhập thành công");
    } catch (error) {
      console.error(`Đăng nhập thất bại: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      this.processedNotificationPages.clear();
      throw error;
    }
  }

  /**
   * Xác minh trạng thái đăng nhập bằng cách kiểm tra menu button
   * @returns Promise<void>
   */
  async verifyLoginState(): Promise<void> {
    try {
      console.log("Đang xác minh trạng thái đăng nhập...");
      // Tăng timeout để đảm bảo độ tin cậy
      await this.uiActions.waitForElement(DashboardSelectors.menuBtn, "Menu Button", 30000);
      console.log("Đã tìm thấy menu button, đăng nhập thành công!");
    } catch (error) {
      // Xử lý lỗi chi tiết
      if (error instanceof Error) {
        console.error("Xác minh đăng nhập thất bại:", error.message);
      } else {
        console.error("Xác minh đăng nhập thất bại: Lỗi không xác định");
      }

      // Log thông tin trang hiện tại để debug
      console.log("URL hiện tại:", this.uiActions.getUrl());
      const pageContent = await this.uiActions.content();
      console.log("Một phần nội dung trang hiện tại:", pageContent.substring(0, 200));

      throw new Error(`Không thể xác minh trạng thái đăng nhập: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
    }
  }

  /**
   * Xử lý các popup thông báo từ ví Phantom
   * @param timeout Thời gian chờ tối đa (ms)
   * @returns Promise<void>
   */
  async handleNotificationPopups(timeout = 30000): Promise<void> {
    console.log("Bắt đầu xử lý các popup từ Phantom...");
    // Lưu lại context chính
    const context = this.uiActions.getPage().context();

    try {
      // 1. Xử lý Connect popup trước
      await this.handleConnectPopup(context, timeout);

      // 2. Sau đó xử lý Confirm popup
      await this.handleConfirmPopup(context, timeout);
    } catch (error) {
      console.warn(`Gặp vấn đề khi xử lý popup: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);

      // Kiểm tra xem có đăng nhập thành công không dù gặp lỗi
      try {
        await this.verifyLoginState();
        console.log("Đăng nhập thành công mặc dù xử lý popup có vấn đề");
        return;
      } catch (e) {
        console.error("Xác minh đăng nhập thất bại:", e instanceof Error ? e.message : "Lỗi không xác định");
        throw new Error(`Xử lý popup thất bại: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      }
    }
  }

  /**
   * Xử lý popup Connect từ Phantom
   * @param context Browser context
   * @param timeout Thời gian chờ tối đa (ms)
   * @returns Promise<void>
   * @private
   */
  private async handleConnectPopup(context: BrowserContext, timeout: number): Promise<void> {
    console.log("Đang xử lý popup Connect...");

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        // Tìm tất cả các notification page
        const pages = context.pages();
        const notificationPages = pages.filter(page =>
          !page.isClosed() && page.url().includes("notification")
        );

        if (notificationPages.length === 0) {
          await new Promise(resolve => setTimeout(resolve, 500));
          continue;
        }

        // Xử lý trang đầu tiên tìm thấy
        const page = notificationPages[0];

        // Đưa trang lên phía trước
        await page.bringToFront();

        // Sử dụng selector đã biết chính xác
        const connectButton = page.locator(DashboardSelectors.connectPhantomBtn);

        try {
          // Kiểm tra nút có hiển thị không (với timeout ngắn để không block quá lâu)
          const isVisible = await connectButton.isVisible({ timeout: 3000 }).catch(() => false);

          if (isVisible) {
            console.log(`Tìm thấy nút Connect với selector: ${DashboardSelectors.connectPhantomBtn}`);
            await connectButton.click();
            console.log("Đã click nút Connect thành công");
            await new Promise(resolve => setTimeout(resolve, 1000));
            return;
          }
        } catch (e: any) {
          // Nếu trang đã đóng, coi như thành công
          if (e.toString().includes("has been closed")) {
            console.log("Trang popup đã tự đóng, coi như đã xử lý thành công");
            return;
          }
        }
      } catch (error) {
        // Bỏ qua lỗi và tiếp tục thử
        console.log("Lỗi khi xử lý Connect popup, thử lại...");
      }
    }

    throw new Error(`Không thể xử lý Connect popup trong ${timeout}ms`);
  }

  /**
   * Xử lý popup Confirm từ Phantom
   * @param context Browser context
   * @param timeout Thời gian chờ tối đa (ms)
   * @returns Promise<void>
   * @private
   */
  private async handleConfirmPopup(context: BrowserContext, timeout: number): Promise<void> {
    console.log("Đang xử lý popup Confirm...");

    // Đợi một khoảng thời gian ngắn để đảm bảo popup Connect đã được xử lý hoàn toàn
    // và popup Confirm có thời gian để xuất hiện
    console.log("Đợi 2 giây để đảm bảo popup Confirm có thời gian xuất hiện...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Đăng ký lắng nghe sự kiện trang mới
    let confirmPagePromiseResolved = false;
    const confirmPagePromise = new Promise<Page>((resolve) => {
      const onPageHandler = (page: Page) => {
        if (page.url().includes("notification")) {
          confirmPagePromiseResolved = true;
          context.removeListener('page', onPageHandler);
          resolve(page);
        }
      };
      context.on('page', onPageHandler);

      // Tự động gỡ bỏ listener sau timeout
      setTimeout(() => {
        if (!confirmPagePromiseResolved) {
          context.removeListener('page', onPageHandler);
        }
      }, timeout);
    });

    try {
      // 1. Kiểm tra xem có trang notification mở sẵn không
      const existingPages = context.pages();
      const notificationPages = existingPages.filter(page =>
        !page.isClosed() && page.url().includes("notification")
      );

      let confirmPage: Page | null = null;

      if (notificationPages.length > 0) {
        // Sử dụng trang notification đã mở
        confirmPage = notificationPages[0];
        console.log("Sử dụng trang notification đã mở:", confirmPage.url());
      } else {
        // Đợi trang notification mới xuất hiện với timeout dài hơn
        console.log("Không tìm thấy trang notification hiện có, đợi trang mới...");
        try {
          confirmPage = await Promise.race([
            confirmPagePromise,
            new Promise<never>((_, reject) => {
              setTimeout(() => reject(new Error(`Không tìm thấy Confirm popup trong ${timeout}ms`)), timeout);
            })
          ]);
          console.log("Đã tìm thấy trang notification mới:", confirmPage.url());
        } catch (error) {
          console.log("Không tìm thấy trang notification mới:", error);
          // Thử kiểm tra đăng nhập ngay cả khi không tìm thấy popup
          await this.verifyLoginState().catch(() => {
            console.log("Kiểm tra đăng nhập không thành công, nhưng vẫn tiếp tục");
          });
          return;
        }
      }

      // Đảm bảo confirmPage không null
      if (!confirmPage) {
        console.log("Không tìm thấy trang Confirm popup, kiểm tra đăng nhập...");
        await this.verifyLoginState().catch(() => {
          console.log("Kiểm tra đăng nhập không thành công, nhưng vẫn tiếp tục");
        });
        return;
      }

      // Đưa trang lên phía trước
      try {
        if (!confirmPage.isClosed()) {
          await confirmPage.bringToFront();
        } else {
          console.log("Trang Confirm đã đóng");
          return;
        }
      } catch (e) {
        console.log("Lỗi khi đưa trang Confirm popup lên phía trước:", e instanceof Error ? e.message : "Lỗi không xác định");
        // Tiếp tục, không throw error
      }

      // Chỉ sử dụng selector DashboardSelectors.confirmPhantomBtn
      try {
        if (confirmPage.isClosed()) {
          console.log("Trang Confirm đã đóng");
          return;
        }

        console.log(`Kiểm tra nút Confirm với selector: ${DashboardSelectors.confirmPhantomBtn}`);
        const isVisible = await confirmPage.locator(DashboardSelectors.confirmPhantomBtn).isVisible({ timeout: 3000 }).catch(() => false);

        if (isVisible) {
          console.log(`Tìm thấy nút Confirm với selector: ${DashboardSelectors.confirmPhantomBtn}`);
          await confirmPage.locator(DashboardSelectors.confirmPhantomBtn).click();
          console.log("Đã click nút Confirm thành công");

          // Đợi trang đóng
          try {
            await confirmPage.waitForEvent('close', { timeout: 5000 }).catch(() => {
              console.log("Timeout khi chờ trang đóng, nhưng vẫn tiếp tục");
            });
          } catch (e) {
            // Bỏ qua lỗi
          }
        } else {
          console.log(`Không tìm thấy nút Confirm với selector: ${DashboardSelectors.confirmPhantomBtn}`);
        }
      } catch (e) {
        console.log(`Lỗi khi thao tác với nút Confirm:`, e instanceof Error ? e.message : "Lỗi không xác định");
      }

      // Kiểm tra đăng nhập bất kể tìm thấy nút hay không
      await this.verifyLoginState().catch(() => {
        console.log("Kiểm tra đăng nhập không thành công, nhưng vẫn tiếp tục");
      });

    } catch (error) {
      console.log("Lỗi khi xử lý Confirm popup:", error instanceof Error ? error.message : "Lỗi không xác định");
      // Kiểm tra đăng nhập dù có lỗi
      try {
        await this.verifyLoginState();
        console.log("Đăng nhập đã thành công dù xử lý popup Confirm có vấn đề");
        return;
      } catch (loginError) {
        console.log("Không thể xác minh đăng nhập sau lỗi xử lý popup");
        // Không throw error để cho phép test tiếp tục
      }
    }
}

  /**
   * Chuyển về trang Dashboard sau khi xử lý popup
   * @returns Promise<void>
   */
  async switchToDashboardAfterPopup(): Promise<void> {
    try {
      console.log("Đang chuyển về trang Dashboard sau khi xử lý popup...");
      await this.uiActions.getDashboardPage();
      console.log("Đã chuyển về trang Dashboard thành công");
    } catch (error) {
      console.error("Lỗi khi chuyển về trang Dashboard:", error instanceof Error ? error.message : "Lỗi không xác định");
      // Không throw lỗi để tránh làm gián đoạn luồng test
    }
  }

  /**
   * Kiểm tra URL hiện tại
   * @param expectedUrl URL kỳ vọng (có thể là một phần của URL)
   * @returns Promise<void>
   */
  async checkCurrentUrl(expectedUrl: string): Promise<void> {
    const currentUrl = this.uiActions.getUrl();
    console.log("URL hiện tại:", currentUrl);
    expect(currentUrl).toContain(expectedUrl);
  }

  /**
   * Phương thức tiện ích để chờ đợi một khoảng thời gian cụ thể
   * @param ms Thời gian chờ (ms)
   * @returns Promise<void>
   */
  async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Truy cập trang token list với 3 tab All tokens, New pairs, Trending
   * @param tab Tab cần chọn: "all" (All tokens), "new" (New pairs), "trending" (Trending)
   * @returns Promise<void>
   */
  async navigateToTokensList(tab: "all" | "new" | "trending" = "all"): Promise<void> {
    try {
      console.log(`Truy cập trang token list với tab: ${tab}...`);

      // Đợi trang load hoàn tất trước khi thực hiện thao tác
      await this.uiActions.waitForLoadState();

      // Click vào Explore
      console.log("Click vào Explore...");
      await this.uiActions.element(CommonSelectors.exploreLink, "Explore Link").click();

      // Đợi trang load hoàn tất
      await this.uiActions.waitForLoadState();
      console.log("Đã chuyển đến trang token list thành công");

      // Đợi một chút để đảm bảo UI đã sẵn sàng
      await this.sleep(1000);

      // Chọn tab tương ứng (nếu không phải tab All tokens)
      // Lưu ý: Tab All tokens là mặc định khi click vào Explore nên không cần click lại
      switch (tab) {
        case "all":
          console.log("Tab All tokens đã được chọn mặc định");
          break;
        case "new":
          console.log("Chọn tab New pairs...");
          await this.uiActions.element(DashboardSelectors.newPairsTab, "New pairs tab").click();
          break;
        case "trending":
          console.log("Chọn tab Trending...");
          await this.uiActions.element(DashboardSelectors.trendingTab, "Trending tab").click();
          break;
      }

      // Đợi trang load hoàn tất sau khi chọn tab
      await this.uiActions.waitForLoadState();
      console.log(`Đã chuyển đến tab ${tab} thành công`);
    } catch (error) {
      console.error(`Lỗi khi truy cập trang token list với tab ${tab}:`, error instanceof Error ? error.message : "Lỗi không xác định");
      throw error; // Throw lỗi để test có thể dừng và báo lỗi
    }
  }

  /**
   * Kiểm tra trạng thái của các DEX mặc định trong filter
   * @returns Promise<void>
   */
  async verifyDefaultDexesSelection(): Promise<void> {
    try {
      console.log("Kiểm tra các DEX mặc định...");

      // Đợi trang load hoàn tất
      await this.uiActions.waitForLoadState();

      // Lấy tham chiếu đến trang
      const page = this.uiActions.getPage();

      // Mở bộ lọc DEXes
      await this.uiActions.element(DashboardSelectors.DEXXes, "DEXes Filter").click();
      await this.sleep(1000);

      // Đếm số DEX được kiểm tra
      let checkedDexCount = 0;
      let selectedDexCount = 0;

      // Kiểm tra từng DEX mặc định
      for (const dex of DashboardSelectors.defaultDexes) {
        try {
          // Tìm checkbox của DEX
          const checkbox = page.locator(DashboardSelectors.dexCheckbox(dex));
          const isChecked = await checkbox.isChecked().catch(() => false);
          checkedDexCount++;

          if (isChecked) {
            selectedDexCount++;
            console.log(`- Xác nhận ${dex} được chọn ✅`);
          } else {
            console.log(`- DEX ${dex} không được chọn ❌`);
          }
        } catch (error) {
          console.warn(`- Không thể kiểm tra ${dex}, bỏ qua`);
        }
      }

      // Hiển thị kết quả tổng quan
      console.log(`Đã kiểm tra được ${checkedDexCount}/7 DEX, có ${selectedDexCount} DEX được chọn`);
    } catch (error) {
      console.error("Lỗi khi kiểm tra DEX mặc định:", error instanceof Error ? error.message : "Lỗi không xác định");
      console.log("Tiếp tục test mặc dù gặp lỗi");
    }
  }

  /**
   * Clear tất cả DEX và chọn một DEX cụ thể (phiên bản đơn giản)
   * @param dexName Tên DEX cần chọn
   * @returns Promise<void>
   */
  async selectSingleDex(dexName: string): Promise<void> {
    try {
      console.log(`Chọn DEX: ${dexName}`);

      // Đợi trang load hoàn tất trước khi thực hiện thao tác
      await this.uiActions.waitForLoadState();
      // await this.sleep(1000); // Đợi thêm 1 giây để đảm bảo UI đã sẵn sàng

      // Click vào nút Clear All
      await this.uiActions.element(DashboardSelectors.clearAllDexes, "Clear All Button").click();

      // Đợi UI cập nhật
      await this.uiActions.pauseInSecs(2);

      // Chuyển đổi tên DEX sang định dạng hiển thị
      const displayDexName = this.getDexDisplayName(dexName);
      
      // Chọn DEX cụ thể
      await this.uiActions.element(DashboardSelectors.dexCheckboxLabel(displayDexName), `${dexName} Checkbox`).click();

      // Đợi danh sách token cập nhật
      await this.uiActions.pauseInSecs(2);

      console.log(`Đã chọn DEX ${dexName} thành công`);
    } catch (error) {
      console.error(`Lỗi khi chọn DEX:`, error instanceof Error ? error.message : "Lỗi không xác định");

      // Chụp màn hình khi gặp lỗi
      const screenshotPath = `screenshots/select-dex-error-${Date.now()}.png`;
      await this.uiActions.getPage().screenshot({ path: screenshotPath, fullPage: true });
      console.log(`Đã chụp màn hình lỗi: ${screenshotPath}`);
    }
  }

  /**
   * Chuyển đổi tên DEX từ định dạng trong mảng defaultDexes sang định dạng thực tế
   * @param dexName Tên DEX cần chuyển đổi
   * @returns Tên DEX đã chuyển đổi
   * @private
   */
  private getDexDisplayName(dexName: string): string {
    // Bảng ánh xạ giữa tên DEX trong mảng defaultDexes và giá trị thực tế
    const dexNameMap: Record<string, string> = {
      'pump.fun': 'PumpFun',
      'pumpswap': 'Pumpswap',
      'moonshot': 'Moonshot',
      'raydium': 'Raydium',
      'orca': 'Orca',
      'meteora': 'Meteora',
      'fluxbeam': 'FluxBeam',
      'launchlab': 'LaunchLab'
    };

    // Chuyển đổi tên DEX sang chữ thường để tìm kiếm trong bảng ánh xạ
    const normalizedDexName = dexName.toLowerCase();

    // Trả về tên DEX đã chuyển đổi hoặc giữ nguyên nếu không tìm thấy
    return dexNameMap[normalizedDexName] || dexName;
  }

  /**
   * Kiểm tra các tab điều hướng trong Dashboard
   * @returns Promise<void>
   */
  async verifyNavigationTabs(): Promise<void> {
    await this.uiActions.waitForLoadState('networkidle');
    console.log('Kiểm tra các thành phần điều hướng tab...');
    const page = this.uiActions.getPage();
    await page.waitForTimeout(2000);

    try {
      // Click vào tab New pairs sử dụng uiActions
      console.log('Click vào tab New pairs...');
      await this.uiActions.element(DashboardSelectors.newPairsTab, 'New pairs tab').click();
      await page.waitForTimeout(2000);

      // Click vào tab Trending sử dụng uiActions
      console.log('Click vào tab Trending...');
      await this.uiActions.element(DashboardSelectors.trendingTab, 'Trending tab').click();
      await page.waitForTimeout(2000);

      // Quay lại tab All tokens sử dụng uiActions
      console.log('Quay lại tab All tokens...');
      await this.uiActions.element(DashboardSelectors.allTokenTab, 'All tokens tab').click();
      await page.waitForTimeout(2000);
    } catch (error) {
      console.error('Lỗi khi click vào các tab:', error instanceof Error ? error.message : 'Lỗi không xác định');
    }
    console.log('Kiểm tra các tab điều hướng hoàn tất');
  }

  /**
   * Kiểm tra các bộ lọc trong Dashboard
   * @param screenshotDir Thư mục lưu ảnh chụp màn hình
   * @returns Promise<void>
   */
  async verifyFilterPanelDisplay(screenshotDir: string): Promise<void> {
    console.log('Kiểm tra các thành phần bộ lọc...');
    const page = this.uiActions.getPage();

    // Kiểm tra tất cả các tùy chọn lọc hiển thị
    const dexesFilter = page.locator(DashboardSelectors.DEXXes);
    await expect(dexesFilter).toBeVisible();

    const securityFilter = page.locator(DashboardSelectors.securityFilter);
    await expect(securityFilter).toBeVisible();

    const liquidityFilter = page.locator(DashboardSelectors.liquidityFilter);
    await expect(liquidityFilter).toBeVisible();

    const marketCapFilter = page.locator(DashboardSelectors.marketCapFilter);
    await expect(marketCapFilter).toBeVisible();

    const ageFilter = page.locator(DashboardSelectors.ageFilter);
    await expect(ageFilter).toBeVisible();

    const tokenFilter = page.locator(DashboardSelectors.tokenFilter);
    await expect(tokenFilter).toBeVisible();

    // Click vào bộ lọc DEXes để xem dropdown
    console.log('Click vào bộ lọc DEXes...');
    await dexesFilter.click();
    await page.waitForTimeout(1000);

    // Kiểm tra hộp thoại bộ lọc DEX xuất hiện
    const clearAllButton = page.locator(DashboardSelectors.clearAllDexes);
    await expect(clearAllButton).toBeVisible();

    // Kiểm tra ít nhất một DEX được chọn (cài đặt mặc định)
    await this.verifyDefaultDexesSelection();

    // Chụp ảnh dropdown bộ lọc DEX
    await page.screenshot({ path: path.join(screenshotDir, 'dex-filter-dropdown.png') });

    // Kiểm tra xóa tất cả DEXes
    console.log('Xóa tất cả DEXes...');
    await clearAllButton.click();
    await page.waitForTimeout(1000);

    // Chọn một DEX cụ thể (pump.fun)
    console.log('Chọn DEX pump.fun...');
    await this.selectSingleDex('pump.fun');
    await page.waitForTimeout(2000);

    // Kiểm tra danh sách token đã lọc
    const isCorrectlyFiltered = await this.verifyTokenListByDex('pump.fun', 5);
    expect(isCorrectlyFiltered).toBeTruthy();

    // Đóng bộ lọc DEX bằng cách click bên ngoài
    await page.mouse.click(10, 10);
    await page.waitForTimeout(500);

    console.log('Kiểm tra bộ lọc hoàn tất');
  }

  /**
   * Kiểm tra các tiêu đề cột trong danh sách token
   * @param screenshotDir Thư mục lưu ảnh chụp màn hình
   * @returns Promise<void>
   */
  async verifyTokenListColumnHeaders(screenshotDir: string): Promise<void> {
    console.log('Kiểm tra các tiêu đề cột trong danh sách token...');
    const page = this.uiActions.getPage();

    // Kiểm tra tất cả các tiêu đề cột hiển thị
    for (const header of DashboardSelectors.columnHeaders) {
      const headerElement = page.locator(DashboardSelectors.columnHeader(header)).first();
      await expect(headerElement).toBeVisible({ timeout: 5000 });
      console.log(`✅ Tiêu đề cột "${header}" hiển thị`);
    }

    // Click vào tiêu đề Age để kiểm tra sắp xếp
    console.log('Kiểm tra sắp xếp bằng cách click vào tiêu đề Age...');
    const ageHeader = page.locator(DashboardSelectors.ageHeader);
    await ageHeader.click();
    await page.waitForTimeout(1000);

    // Chụp ảnh sau khi sắp xếp
    await page.screenshot({ path: path.join(screenshotDir, 'age-sorted.png') });

    // Click lần nữa để đảo ngược sắp xếp
    await ageHeader.click();
    await page.waitForTimeout(1000);

    // Chụp ảnh sau khi đảo ngược sắp xếp
    await page.screenshot({ path: path.join(screenshotDir, 'age-reverse-sorted.png') });

    console.log('Kiểm tra các tiêu đề cột hoàn tất');
  }

  /**
   * Kiểm tra nội dung danh sách token
   * @param screenshotDir Thư mục lưu ảnh chụp màn hình
   * @returns Promise<void>
   */
  async verifyTokenListContent(screenshotDir: string): Promise<void> {
    console.log('Kiểm tra nội dung danh sách token...');
    const page = this.uiActions.getPage();

    // Kiểm tra hàng token đầu tiên có đầy đủ các phần tử dự kiến
    const firstTokenRow = page.locator(DashboardSelectors.firstTokenRow);
    await expect(firstTokenRow).toBeVisible();

    // Kiểm tra biểu tượng token
    await expect(firstTokenRow.locator(DashboardSelectors.tokenIcon)).toBeVisible();

    // Kiểm tra tên token
    await expect(firstTokenRow.locator(DashboardSelectors.tokenName)).toBeVisible();

    // Kiểm tra giá token
    await expect(firstTokenRow.locator(DashboardSelectors.tokenPrice)).toBeVisible();

    // Kiểm tra tuổi token
    const ageCell = firstTokenRow.locator(DashboardSelectors.tokenAge);
    await expect(ageCell).toBeVisible();

    // Kiểm tra vốn hóa thị trường token
    const mcapCell = firstTokenRow.locator(DashboardSelectors.tokenMcap);
    await expect(mcapCell).toBeVisible();

    // Chụp ảnh danh sách token
    await page.screenshot({ path: path.join(screenshotDir, 'token-list.png'), fullPage: true });

    console.log('Kiểm tra nội dung danh sách token hoàn tất');
  }

  /**
   * Kiểm tra đầy đủ giao diện Dashboard
   * @param screenshotDir Thư mục lưu ảnh chụp màn hình
   * @returns Promise<void>
   */
  async verifyDashboardUI(screenshotDir: string): Promise<void> {
    console.log('\n==== BẮT ĐẦU KIỂM TRA GIAO DIỆN DASHBOARD ====\n');

    try {
      // 1. Kiểm tra các tab điều hướng
      await this.verifyNavigationTabs();

      // 2. Kiểm tra bộ lọc
      await this.verifyFilterPanelDisplay(screenshotDir);

      // 3. Kiểm tra các tiêu đề cột trong danh sách token
      await this.verifyTokenListColumnHeaders(screenshotDir);

      // 4. Kiểm tra nội dung danh sách token
      await this.verifyTokenListContent(screenshotDir);

      console.log('\n==== KIỂM TRA GIAO DIỆN DASHBOARD HOÀN TẤT THÀNH CÔNG ====\n');
    } catch (error) {
      console.error('\n!!! KIỂM TRA GIAO DIỆN DASHBOARD THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');

      // Chụp ảnh lỗi
      try {
        const errorScreenshotPath = path.join(screenshotDir, `dashboard-ui-error-${Date.now()}.png`);
        await this.uiActions.getPage().screenshot({
          path: errorScreenshotPath,
          fullPage: true
        });
        console.error(`Đã lưu ảnh lỗi: ${errorScreenshotPath}`);
      } catch (screenshotError) {
        console.error("Không thể chụp ảnh lỗi");
      }

      throw error;
    }
  }

  /**
   * Truy cập tab All tokens (mặc định khi click vào Explore)
   * @returns Promise<void>
   */
  async navigateToAllTokens(): Promise<void> {
    try {
      console.log("Truy cập tab All tokens (mặc định)...");
      await this.navigateToTokensList("all");
      console.log("Đã truy cập tab All tokens thành công");
    } catch (error) {
      console.error("Lỗi khi truy cập tab All tokens:", error instanceof Error ? error.message : "Lỗi không xác định");
      throw error;
    }
  }

  /**
   * Truy cập tab New pairs
   * @returns Promise<void>
   */
  async navigateToNewPairs(): Promise<void> {
    try {
      console.log("Truy cập tab New pairs...");
      await this.navigateToTokensList("new");
      console.log("Đã truy cập tab New pairs thành công");
    } catch (error) {
      console.error("Lỗi khi truy cập tab New pairs:", error instanceof Error ? error.message : "Lỗi không xác định");
      throw error;
    }
  }

  /**
   * Truy cập tab Trending
   * @returns Promise<void>
   */
  async navigateToTrending(): Promise<void> {
    try {
      console.log("Truy cập tab Trending...");
      await this.navigateToTokensList("trending");
      console.log("Đã truy cập tab Trending thành công");
    } catch (error) {
      console.error("Lỗi khi truy cập tab Trending:", error instanceof Error ? error.message : "Lỗi không xác định");
      throw error;
    }
  }

  /**
   * Kiểm tra danh sách token có thuộc về DEX cụ thể hay không
   * @param expectedDex Tên DEX cần kiểm tra (ví dụ: "pump.fun", "raydium")
   * @param maxTokensToCheck Số lượng token tối đa cần kiểm tra
   * @returns Promise<boolean> - true nếu tất cả token thuộc DEX kỳ vọng, false nếu ngược lại
   */
  async verifyTokenListByDex(expectedDex: string, maxTokensToCheck: number = 20): Promise<boolean> {
    try {
      // Chuyển đổi tên DEX sang định dạng thực tế
      const expectedDexDisplay = this.getDexDisplayName(expectedDex);
      console.log(`Kiểm tra ${maxTokensToCheck} token đầu tiên từ DEX "${expectedDex}" (hiển thị: "${expectedDexDisplay}")...`);

      const page = this.uiActions.getPage();

      // Selector cho các hàng token và thẻ img DEX
      const tokenRowSelector = 'a.chakra-link.token-virtual-row';
      const dexImgSelector = '.css-jvoq11 img.css-ha03xt';

      // Hover vào hàng đầu tiên để dừng việc append token mới vào list
      const firstRow = page.locator(tokenRowSelector).first();
      console.log("Hover vào hàng đầu tiên để ngăn cập nhật danh sách...");
      await firstRow.hover();

      // Đợi một chút để đảm bảo danh sách ổn định
      await this.sleep(1000);

      // Đếm số lượng token hiển thị
      const totalTokens = await page.locator(tokenRowSelector).count();
      const tokensToCheck = Math.min(maxTokensToCheck, totalTokens);

      if (tokensToCheck === 0) {
        console.log("Không tìm thấy token nào để kiểm tra");
        await page.screenshot({ path: 'screenshots/no-tokens-found.png' });
        return false;
      }

      console.log(`Tìm thấy ${totalTokens} token, sẽ kiểm tra ${tokensToCheck} token`);

      // Danh sách các token không khớp
      let nonMatchingTokens = 0;

      // Kiểm tra từng token
      for (let i = 0; i < tokensToCheck; i++) {
        const tokenElement = page.locator(tokenRowSelector).nth(i);
        const tokenName = await tokenElement.locator('.chakra-text.css-16rit9e').textContent() || `Token ${i+1}`;
        const dexImg = tokenElement.locator(dexImgSelector);
        const actualDex = await dexImg.getAttribute('alt') || 'Không xác định';

        // So sánh với tên DEX đã chuyển đổi
        if (actualDex !== expectedDexDisplay) {
          nonMatchingTokens++;
          console.log(`❌ Token ${i+1} "${tokenName}": DEX là "${actualDex}", không phải "${expectedDexDisplay}"`);
        }
      }

      // Kết quả kiểm tra
      if (nonMatchingTokens === 0) {
        console.log(`✅ Tất cả ${tokensToCheck} token đều từ DEX "${expectedDexDisplay}"`);
        return true;
      } else {
        console.log(`❌ Có ${nonMatchingTokens}/${tokensToCheck} token không phải từ DEX "${expectedDexDisplay}"`);
        await page.screenshot({ path: `screenshots/dex-verification-failed-${Date.now()}.png` });
        return false;
      }
    } catch (error) {
      console.error("Lỗi khi kiểm tra danh sách token:", error instanceof Error ? error.message : "Lỗi không xác định");
      await this.uiActions.getPage().screenshot({ path: `screenshots/dex-verification-error-${Date.now()}.png` });
      return false;
    }
  }
}
