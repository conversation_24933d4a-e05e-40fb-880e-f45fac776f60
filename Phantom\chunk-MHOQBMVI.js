import{b as ft}from"./chunk-GQEPK4C4.js";import{a as zs}from"./chunk-BTKBODVJ.js";import{j as gn}from"./chunk-7ZN4F6J4.js";import{a as wn,g as En,j as Wt,l as bn,m as An}from"./chunk-OYGO47TI.js";import{vc as yn}from"./chunk-MZZEJ42N.js";import{K as pn}from"./chunk-E3NPIRHS.js";import{a as mn}from"./chunk-56SJOU6P.js";import{ca as dn}from"./chunk-ALUTR72U.js";import{G as hn,ba as ct,z as ln}from"./chunk-L3A2KHJO.js";import{a as Ys}from"./chunk-7X4NV6OJ.js";import{c as Hs,f as Kt,h as G,n as K}from"./chunk-3KENBVE7.js";var co=Hs((Nr,uo)=>{"use strict";G();K();var{hasOwnProperty:rt}=Object.prototype,Ie=Dr();Ie.configure=Dr;Ie.stringify=Ie;Ie.default=Ie;Nr.stringify=Ie;Nr.configure=Dr;uo.exports=Ie;var Rf=/[\u0000-\u001f\u0022\u005c\ud800-\udfff]/;function pe(t){return t.length<5e3&&!Rf.test(t)?`"${t}"`:JSON.stringify(t)}function Pr(t,e){if(t.length>200||e)return t.sort(e);for(let r=1;r<t.length;r++){let n=t[r],i=r;for(;i!==0&&t[i-1]>n;)t[i]=t[i-1],i--;t[i]=n}return t}var Of=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(Object.getPrototypeOf(new Int8Array)),Symbol.toStringTag).get;function Fr(t){return Of.call(t)!==void 0&&t.length!==0}function so(t,e,r){t.length<r&&(r=t.length);let n=e===","?"":" ",i=`"0":${n}${t[0]}`;for(let o=1;o<r;o++)i+=`${e}"${o}":${n}${t[o]}`;return i}function If(t){if(rt.call(t,"circularValue")){let e=t.circularValue;if(typeof e=="string")return`"${e}"`;if(e==null)return e;if(e===Error||e===TypeError)return{toString(){throw new TypeError("Converting circular structure to JSON")}};throw new TypeError('The "circularValue" argument must be of type string or the value null or undefined')}return'"[Circular]"'}function Cf(t){let e;if(rt.call(t,"deterministic")&&(e=t.deterministic,typeof e!="boolean"&&typeof e!="function"))throw new TypeError('The "deterministic" argument must be of type boolean or comparator function');return e===void 0?!0:e}function Pf(t,e){let r;if(rt.call(t,e)&&(r=t[e],typeof r!="boolean"))throw new TypeError(`The "${e}" argument must be of type boolean`);return r===void 0?!0:r}function ao(t,e){let r;if(rt.call(t,e)){if(r=t[e],typeof r!="number")throw new TypeError(`The "${e}" argument must be of type number`);if(!Number.isInteger(r))throw new TypeError(`The "${e}" argument must be an integer`);if(r<1)throw new RangeError(`The "${e}" argument must be >= 1`)}return r===void 0?1/0:r}function Oe(t){return t===1?"1 item":`${t} items`}function Ff(t){let e=new Set;for(let r of t)(typeof r=="string"||typeof r=="number")&&e.add(String(r));return e}function Df(t){if(rt.call(t,"strict")){let e=t.strict;if(typeof e!="boolean")throw new TypeError('The "strict" argument must be of type boolean');if(e)return r=>{let n=`Object can not safely be stringified. Received type ${typeof r}`;throw typeof r!="function"&&(n+=` (${r.toString()})`),new Error(n)}}}function Dr(t){t={...t};let e=Df(t);e&&(t.bigint===void 0&&(t.bigint=!1),"circularValue"in t||(t.circularValue=Error));let r=If(t),n=Pf(t,"bigint"),i=Cf(t),o=typeof i=="function"?i:void 0,s=ao(t,"maximumDepth"),u=ao(t,"maximumBreadth");function l(T,c,w,S,A,x){let b=c[T];switch(typeof b=="object"&&b!==null&&typeof b.toJSON=="function"&&(b=b.toJSON(T)),b=S.call(c,T,b),typeof b){case"string":return pe(b);case"object":{if(b===null)return"null";if(w.indexOf(b)!==-1)return r;let R="",v=",",D=x;if(Array.isArray(b)){if(b.length===0)return"[]";if(s<w.length+1)return'"[Array]"';w.push(b),A!==""&&(x+=A,R+=`
${x}`,v=`,
${x}`);let L=Math.min(b.length,u),z=0;for(;z<L-1;z++){let me=l(String(z),b,w,S,A,x);R+=me!==void 0?me:"null",R+=v}let V=l(String(z),b,w,S,A,x);if(R+=V!==void 0?V:"null",b.length-1>u){let me=b.length-u-1;R+=`${v}"... ${Oe(me)} not stringified"`}return A!==""&&(R+=`
${D}`),w.pop(),`[${R}]`}let C=Object.keys(b),U=C.length;if(U===0)return"{}";if(s<w.length+1)return'"[Object]"';let O="",B="";A!==""&&(x+=A,v=`,
${x}`,O=" ");let k=Math.min(U,u);i&&!Fr(b)&&(C=Pr(C,o)),w.push(b);for(let L=0;L<k;L++){let z=C[L],V=l(z,b,w,S,A,x);V!==void 0&&(R+=`${B}${pe(z)}:${O}${V}`,B=v)}if(U>u){let L=U-u;R+=`${B}"...":${O}"${Oe(L)} not stringified"`,B=v}return A!==""&&B.length>1&&(R=`
${x}${R}
${D}`),w.pop(),`{${R}}`}case"number":return isFinite(b)?String(b):e?e(b):"null";case"boolean":return b===!0?"true":"false";case"undefined":return;case"bigint":if(n)return String(b);default:return e?e(b):void 0}}function p(T,c,w,S,A,x){switch(typeof c=="object"&&c!==null&&typeof c.toJSON=="function"&&(c=c.toJSON(T)),typeof c){case"string":return pe(c);case"object":{if(c===null)return"null";if(w.indexOf(c)!==-1)return r;let b=x,R="",v=",";if(Array.isArray(c)){if(c.length===0)return"[]";if(s<w.length+1)return'"[Array]"';w.push(c),A!==""&&(x+=A,R+=`
${x}`,v=`,
${x}`);let U=Math.min(c.length,u),O=0;for(;O<U-1;O++){let k=p(String(O),c[O],w,S,A,x);R+=k!==void 0?k:"null",R+=v}let B=p(String(O),c[O],w,S,A,x);if(R+=B!==void 0?B:"null",c.length-1>u){let k=c.length-u-1;R+=`${v}"... ${Oe(k)} not stringified"`}return A!==""&&(R+=`
${b}`),w.pop(),`[${R}]`}w.push(c);let D="";A!==""&&(x+=A,v=`,
${x}`,D=" ");let C="";for(let U of S){let O=p(U,c[U],w,S,A,x);O!==void 0&&(R+=`${C}${pe(U)}:${D}${O}`,C=v)}return A!==""&&C.length>1&&(R=`
${x}${R}
${b}`),w.pop(),`{${R}}`}case"number":return isFinite(c)?String(c):e?e(c):"null";case"boolean":return c===!0?"true":"false";case"undefined":return;case"bigint":if(n)return String(c);default:return e?e(c):void 0}}function y(T,c,w,S,A){switch(typeof c){case"string":return pe(c);case"object":{if(c===null)return"null";if(typeof c.toJSON=="function"){if(c=c.toJSON(T),typeof c!="object")return y(T,c,w,S,A);if(c===null)return"null"}if(w.indexOf(c)!==-1)return r;let x=A;if(Array.isArray(c)){if(c.length===0)return"[]";if(s<w.length+1)return'"[Array]"';w.push(c),A+=S;let O=`
${A}`,B=`,
${A}`,k=Math.min(c.length,u),L=0;for(;L<k-1;L++){let V=y(String(L),c[L],w,S,A);O+=V!==void 0?V:"null",O+=B}let z=y(String(L),c[L],w,S,A);if(O+=z!==void 0?z:"null",c.length-1>u){let V=c.length-u-1;O+=`${B}"... ${Oe(V)} not stringified"`}return O+=`
${x}`,w.pop(),`[${O}]`}let b=Object.keys(c),R=b.length;if(R===0)return"{}";if(s<w.length+1)return'"[Object]"';A+=S;let v=`,
${A}`,D="",C="",U=Math.min(R,u);Fr(c)&&(D+=so(c,v,u),b=b.slice(c.length),U-=c.length,C=v),i&&(b=Pr(b,o)),w.push(c);for(let O=0;O<U;O++){let B=b[O],k=y(B,c[B],w,S,A);k!==void 0&&(D+=`${C}${pe(B)}: ${k}`,C=v)}if(R>u){let O=R-u;D+=`${C}"...": "${Oe(O)} not stringified"`,C=v}return C!==""&&(D=`
${A}${D}
${x}`),w.pop(),`{${D}}`}case"number":return isFinite(c)?String(c):e?e(c):"null";case"boolean":return c===!0?"true":"false";case"undefined":return;case"bigint":if(n)return String(c);default:return e?e(c):void 0}}function h(T,c,w){switch(typeof c){case"string":return pe(c);case"object":{if(c===null)return"null";if(typeof c.toJSON=="function"){if(c=c.toJSON(T),typeof c!="object")return h(T,c,w);if(c===null)return"null"}if(w.indexOf(c)!==-1)return r;let S="",A=c.length!==void 0;if(A&&Array.isArray(c)){if(c.length===0)return"[]";if(s<w.length+1)return'"[Array]"';w.push(c);let D=Math.min(c.length,u),C=0;for(;C<D-1;C++){let O=h(String(C),c[C],w);S+=O!==void 0?O:"null",S+=","}let U=h(String(C),c[C],w);if(S+=U!==void 0?U:"null",c.length-1>u){let O=c.length-u-1;S+=`,"... ${Oe(O)} not stringified"`}return w.pop(),`[${S}]`}let x=Object.keys(c),b=x.length;if(b===0)return"{}";if(s<w.length+1)return'"[Object]"';let R="",v=Math.min(b,u);A&&Fr(c)&&(S+=so(c,",",u),x=x.slice(c.length),v-=c.length,R=","),i&&(x=Pr(x,o)),w.push(c);for(let D=0;D<v;D++){let C=x[D],U=h(C,c[C],w);U!==void 0&&(S+=`${R}${pe(C)}:${U}`,R=",")}if(b>u){let D=b-u;S+=`${R}"...":"${Oe(D)} not stringified"`}return w.pop(),`{${S}}`}case"number":return isFinite(c)?String(c):e?e(c):"null";case"boolean":return c===!0?"true":"false";case"undefined":return;case"bigint":if(n)return String(c);default:return e?e(c):void 0}}function _(T,c,w){if(arguments.length>1){let S="";if(typeof w=="number"?S=" ".repeat(Math.min(w,10)):typeof w=="string"&&(S=w.slice(0,10)),c!=null){if(typeof c=="function")return l("",{"":T},[],c,S,"");if(Array.isArray(c))return p("",T,[],Ff(c),S,"")}if(S.length!==0)return y("",T,[],S,"")}return h("",T,[])}return _}});G();K();var ie=Kt(zs()),Ne=class{async get(e){try{let n=(await ie.default.storage.local.get(e))[e];return n==null?null:n.expiry?n.value:n}catch(r){let n=`[LocalStorage.get] Error getting key (${e}): ${r}`;throw new Error(n)}}async getAll(e){try{return await ie.default.storage.local.get(e)}catch(r){let n=`[LocalStorage.getAll] Error getting key(s) (${e}): ${r}`;throw new Error(n)}}async remove(e){try{await ie.default.storage.local.remove(e)}catch(r){let n=`[LocalStorage.remove] Error removing key(s) (${e}): ${r}`;throw new Error(n)}}async set(e,r){try{await ie.default.storage.local.set({[e]:r})}catch(n){let i=`[LocalStorage.set] Error setting key (${e}): ${n}`;throw new Error(i)}}async setAll(e){try{await ie.default.storage.local.set(e)}catch(r){let i=`[LocalStorage.setAll] Error setting keys (${Object.keys(e)}): ${r}`;throw new Error(i)}}async clear(){try{await ie.default.storage.local.clear()}catch(e){let r=`[LocalStorage.clear] Error clearing storage: ${e}`;throw new Error(r)}}subscribe(e){let r=n=>{let i=Object.keys(n);for(let o of i)e(o)};return ie.default.storage.onChanged.addListener(r),()=>{ie.default.storage.onChanged.removeListener(r)}}async update(e,r){try{await navigator.locks.request(`storage.${e}`,async n=>{let i=await this.get(e),o=await r(i);await this.set(e,o)})}catch(n){let i=`[LocalStorage.update] Error updating key (${e}): ${n}`;throw new Error(i)}}};G();K();G();K();var Te=typeof self<"u"||typeof self<"u"||typeof self<"u"?self:{};function Qn(){throw new Error("setTimeout has not been defined")}function Zn(){throw new Error("clearTimeout has not been defined")}var le=Qn,he=Zn;typeof Te.setTimeout=="function"&&(le=setTimeout);typeof Te.clearTimeout=="function"&&(he=clearTimeout);function ei(t){if(le===setTimeout)return setTimeout(t,0);if((le===Qn||!le)&&setTimeout)return le=setTimeout,setTimeout(t,0);try{return le(t,0)}catch{try{return le.call(null,t,0)}catch{return le.call(this,t,0)}}}function Vs(t){if(he===clearTimeout)return clearTimeout(t);if((he===Zn||!he)&&clearTimeout)return he=clearTimeout,clearTimeout(t);try{return he(t)}catch{try{return he.call(null,t)}catch{return he.call(this,t)}}}var oe=[],je=!1,Ee,mt=-1;function Js(){!je||!Ee||(je=!1,Ee.length?oe=Ee.concat(oe):mt=-1,oe.length&&ti())}function ti(){if(!je){var t=ei(Js);je=!0;for(var e=oe.length;e;){for(Ee=oe,oe=[];++mt<e;)Ee&&Ee[mt].run();mt=-1,e=oe.length}Ee=null,je=!1,Vs(t)}}function Gs(t){var e=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)e[r-1]=arguments[r];oe.push(new ri(t,e)),oe.length===1&&!je&&ei(ti)}function ri(t,e){this.fun=t,this.array=e}ri.prototype.run=function(){this.fun.apply(null,this.array)};var Ks="browser",Ws="browser",Xs=!0,Qs={},Zs=[],ea="",ta={},ra={},na={};function Re(){}var ia=Re,oa=Re,sa=Re,aa=Re,ua=Re,ca=Re,fa=Re;function la(t){throw new Error("process.binding is not supported")}function ha(){return"/"}function pa(t){throw new Error("process.chdir is not supported")}function da(){return 0}var ve=Te.performance||{},ma=ve.now||ve.mozNow||ve.msNow||ve.oNow||ve.webkitNow||function(){return new Date().getTime()};function ya(t){var e=ma.call(ve)*.001,r=Math.floor(e),n=Math.floor(e%1*1e9);return t&&(r=r-t[0],n=n-t[1],n<0&&(r--,n+=1e9)),[r,n]}var ga=new Date;function wa(){var t=new Date,e=t-ga;return e/1e3}var Sn={nextTick:Gs,title:Ks,browser:Xs,env:Qs,argv:Zs,version:ea,versions:ta,on:ia,addListener:oa,once:sa,off:aa,removeListener:ua,removeAllListeners:ca,emit:fa,binding:la,cwd:ha,chdir:pa,umask:da,hrtime:ya,platform:Ws,release:ra,config:na,uptime:wa};function _e(){return _e=Object.assign?Object.assign.bind():function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(t[n]=r[n])}return t},_e.apply(this,arguments)}function ni(t,e){return function(){return t.apply(e,arguments)}}var{toString:Ea}=Object.prototype,{getPrototypeOf:yr}=Object,It=(t=>e=>{let r=Ea.call(e);return t[r]||(t[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),te=t=>(t=t.toLowerCase(),e=>It(e)===t),Ct=t=>e=>typeof e===t,{isArray:ze}=Array,Ze=Ct("undefined");function ba(t){return t!==null&&!Ze(t)&&t.constructor!==null&&!Ze(t.constructor)&&X(t.constructor.isBuffer)&&t.constructor.isBuffer(t)}var ii=te("ArrayBuffer");function Aa(t){let e;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?e=ArrayBuffer.isView(t):e=t&&t.buffer&&ii(t.buffer),e}var Sa=Ct("string"),X=Ct("function"),oi=Ct("number"),Pt=t=>t!==null&&typeof t=="object",Ta=t=>t===!0||t===!1,yt=t=>{if(It(t)!=="object")return!1;let e=yr(t);return(e===null||e===Object.prototype||Object.getPrototypeOf(e)===null)&&!(Symbol.toStringTag in t)&&!(Symbol.iterator in t)},_a=te("Date"),xa=te("File"),Ra=te("Blob"),Oa=te("FileList"),Ia=t=>Pt(t)&&X(t.pipe),Ca=t=>{let e;return t&&(typeof FormData=="function"&&t instanceof FormData||X(t.append)&&((e=It(t))==="formdata"||e==="object"&&X(t.toString)&&t.toString()==="[object FormData]"))},Pa=te("URLSearchParams"),Fa=t=>t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function et(t,e,{allOwnKeys:r=!1}={}){if(t===null||typeof t>"u")return;let n,i;if(typeof t!="object"&&(t=[t]),ze(t))for(n=0,i=t.length;n<i;n++)e.call(null,t[n],n,t);else{let o=r?Object.getOwnPropertyNames(t):Object.keys(t),s=o.length,u;for(n=0;n<s;n++)u=o[n],e.call(null,t[u],u,t)}}function si(t,e){e=e.toLowerCase();let r=Object.keys(t),n=r.length,i;for(;n-- >0;)if(i=r[n],e===i.toLowerCase())return i;return null}var ai=typeof globalThis<"u"?globalThis:typeof self<"u"||typeof self<"u"?self:Te,ui=t=>!Ze(t)&&t!==ai;function sr(){let{caseless:t}=ui(this)&&this||{},e={},r=(n,i)=>{let o=t&&si(e,i)||i;yt(e[o])&&yt(n)?e[o]=sr(e[o],n):yt(n)?e[o]=sr({},n):ze(n)?e[o]=n.slice():e[o]=n};for(let n=0,i=arguments.length;n<i;n++)arguments[n]&&et(arguments[n],r);return e}var Da=(t,e,r,{allOwnKeys:n}={})=>(et(e,(i,o)=>{r&&X(i)?t[o]=ni(i,r):t[o]=i},{allOwnKeys:n}),t),Na=t=>(t.charCodeAt(0)===65279&&(t=t.slice(1)),t),va=(t,e,r,n)=>{t.prototype=Object.create(e.prototype,n),t.prototype.constructor=t,Object.defineProperty(t,"super",{value:e.prototype}),r&&_e(t.prototype,r)},Ua=(t,e,r,n)=>{let i,o,s,u={};if(e=e||{},t==null)return e;do{for(i=Object.getOwnPropertyNames(t),o=i.length;o-- >0;)s=i[o],(!n||n(s,t,e))&&!u[s]&&(e[s]=t[s],u[s]=!0);t=r!==!1&&yr(t)}while(t&&(!r||r(t,e))&&t!==Object.prototype);return e},Ba=(t,e,r)=>{t=String(t),(r===void 0||r>t.length)&&(r=t.length),r-=e.length;let n=t.indexOf(e,r);return n!==-1&&n===r},ja=t=>{if(!t)return null;if(ze(t))return t;let e=t.length;if(!oi(e))return null;let r=new Array(e);for(;e-- >0;)r[e]=t[e];return r},$a=(t=>e=>t&&e instanceof t)(typeof Uint8Array<"u"&&yr(Uint8Array)),La=(t,e)=>{let n=(t&&t[Symbol.iterator]).call(t),i;for(;(i=n.next())&&!i.done;){let o=i.value;e.call(t,o[0],o[1])}},Ma=(t,e)=>{let r,n=[];for(;(r=t.exec(e))!==null;)n.push(r);return n},ka=te("HTMLFormElement"),qa=t=>t.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,i){return n.toUpperCase()+i}),Tn=(({hasOwnProperty:t})=>(e,r)=>t.call(e,r))(Object.prototype),Ha=te("RegExp"),ci=(t,e)=>{let r=Object.getOwnPropertyDescriptors(t),n={};et(r,(i,o)=>{let s;(s=e(i,o,t))!==!1&&(n[o]=s||i)}),Object.defineProperties(t,n)},Ya=t=>{ci(t,(e,r)=>{if(X(t)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;let n=t[r];if(X(n)){if(e.enumerable=!1,"writable"in e){e.writable=!1;return}e.set||(e.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},za=(t,e)=>{let r={},n=i=>{i.forEach(o=>{r[o]=!0})};return ze(t)?n(t):n(String(t).split(e)),r},Va=()=>{},Ja=(t,e)=>(t=+t,Number.isFinite(t)?t:e),Xt="abcdefghijklmnopqrstuvwxyz",_n="0123456789",fi={DIGIT:_n,ALPHA:Xt,ALPHA_DIGIT:Xt+Xt.toUpperCase()+_n},Ga=(t=16,e=fi.ALPHA_DIGIT)=>{let r="",{length:n}=e;for(;t--;)r+=e[Math.random()*n|0];return r};function Ka(t){return!!(t&&X(t.append)&&t[Symbol.toStringTag]==="FormData"&&t[Symbol.iterator])}var Wa=t=>{let e=new Array(10),r=(n,i)=>{if(Pt(n)){if(e.indexOf(n)>=0)return;if(!("toJSON"in n)){e[i]=n;let o=ze(n)?[]:{};return et(n,(s,u)=>{let l=r(s,i+1);!Ze(l)&&(o[u]=l)}),e[i]=void 0,o}}return n};return r(t,0)},Xa=te("AsyncFunction"),Qa=t=>t&&(Pt(t)||X(t))&&X(t.then)&&X(t.catch),d={isArray:ze,isArrayBuffer:ii,isBuffer:ba,isFormData:Ca,isArrayBufferView:Aa,isString:Sa,isNumber:oi,isBoolean:Ta,isObject:Pt,isPlainObject:yt,isUndefined:Ze,isDate:_a,isFile:xa,isBlob:Ra,isRegExp:Ha,isFunction:X,isStream:Ia,isURLSearchParams:Pa,isTypedArray:$a,isFileList:Oa,forEach:et,merge:sr,extend:Da,trim:Fa,stripBOM:Na,inherits:va,toFlatObject:Ua,kindOf:It,kindOfTest:te,endsWith:Ba,toArray:ja,forEachEntry:La,matchAll:Ma,isHTMLForm:ka,hasOwnProperty:Tn,hasOwnProp:Tn,reduceDescriptors:ci,freezeMethods:Ya,toObjectSet:za,toCamelCase:qa,noop:Va,toFiniteNumber:Ja,findKey:si,global:ai,isContextDefined:ui,ALPHABET:fi,generateString:Ga,isSpecCompliantForm:Ka,toJSONObject:Wa,isAsyncFn:Xa,isThenable:Qa},Q=[],W=[],Za=typeof Uint8Array<"u"?Uint8Array:Array,gr=!1;function li(){gr=!0;for(var t="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",e=0,r=t.length;e<r;++e)Q[e]=t[e],W[t.charCodeAt(e)]=e;W[45]=62,W[95]=63}function eu(t){gr||li();var e,r,n,i,o,s,u=t.length;if(u%4>0)throw new Error("Invalid string. Length must be a multiple of 4");o=t[u-2]==="="?2:t[u-1]==="="?1:0,s=new Za(u*3/4-o),n=o>0?u-4:u;var l=0;for(e=0,r=0;e<n;e+=4,r+=3)i=W[t.charCodeAt(e)]<<18|W[t.charCodeAt(e+1)]<<12|W[t.charCodeAt(e+2)]<<6|W[t.charCodeAt(e+3)],s[l++]=i>>16&255,s[l++]=i>>8&255,s[l++]=i&255;return o===2?(i=W[t.charCodeAt(e)]<<2|W[t.charCodeAt(e+1)]>>4,s[l++]=i&255):o===1&&(i=W[t.charCodeAt(e)]<<10|W[t.charCodeAt(e+1)]<<4|W[t.charCodeAt(e+2)]>>2,s[l++]=i>>8&255,s[l++]=i&255),s}function tu(t){return Q[t>>18&63]+Q[t>>12&63]+Q[t>>6&63]+Q[t&63]}function ru(t,e,r){for(var n,i=[],o=e;o<r;o+=3)n=(t[o]<<16)+(t[o+1]<<8)+t[o+2],i.push(tu(n));return i.join("")}function xn(t){gr||li();for(var e,r=t.length,n=r%3,i="",o=[],s=16383,u=0,l=r-n;u<l;u+=s)o.push(ru(t,u,u+s>l?l:u+s));return n===1?(e=t[r-1],i+=Q[e>>2],i+=Q[e<<4&63],i+="=="):n===2&&(e=(t[r-2]<<8)+t[r-1],i+=Q[e>>10],i+=Q[e>>4&63],i+=Q[e<<2&63],i+="="),o.push(i),o.join("")}function Ft(t,e,r,n,i){var o,s,u=i*8-n-1,l=(1<<u)-1,p=l>>1,y=-7,h=r?i-1:0,_=r?-1:1,T=t[e+h];for(h+=_,o=T&(1<<-y)-1,T>>=-y,y+=u;y>0;o=o*256+t[e+h],h+=_,y-=8);for(s=o&(1<<-y)-1,o>>=-y,y+=n;y>0;s=s*256+t[e+h],h+=_,y-=8);if(o===0)o=1-p;else{if(o===l)return s?NaN:(T?-1:1)*(1/0);s=s+Math.pow(2,n),o=o-p}return(T?-1:1)*s*Math.pow(2,o-n)}function hi(t,e,r,n,i,o){var s,u,l,p=o*8-i-1,y=(1<<p)-1,h=y>>1,_=i===23?Math.pow(2,-24)-Math.pow(2,-77):0,T=n?0:o-1,c=n?1:-1,w=e<0||e===0&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(u=isNaN(e)?1:0,s=y):(s=Math.floor(Math.log(e)/Math.LN2),e*(l=Math.pow(2,-s))<1&&(s--,l*=2),s+h>=1?e+=_/l:e+=_*Math.pow(2,1-h),e*l>=2&&(s++,l/=2),s+h>=y?(u=0,s=y):s+h>=1?(u=(e*l-1)*Math.pow(2,i),s=s+h):(u=e*Math.pow(2,h-1)*Math.pow(2,i),s=0));i>=8;t[r+T]=u&255,T+=c,u/=256,i-=8);for(s=s<<i|u,p+=i;p>0;t[r+T]=s&255,T+=c,s/=256,p-=8);t[r+T-c]|=w*128}var nu={}.toString,pi=Array.isArray||function(t){return nu.call(t)=="[object Array]"};var iu=50;g.TYPED_ARRAY_SUPPORT=Te.TYPED_ARRAY_SUPPORT!==void 0?Te.TYPED_ARRAY_SUPPORT:!0;At();function At(){return g.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function se(t,e){if(At()<e)throw new RangeError("Invalid typed array length");return g.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=g.prototype):(t===null&&(t=new g(e)),t.length=e),t}function g(t,e,r){if(!g.TYPED_ARRAY_SUPPORT&&!(this instanceof g))return new g(t,e,r);if(typeof t=="number"){if(typeof e=="string")throw new Error("If encoding is specified then the first argument must be a string");return wr(this,t)}return di(this,t,e,r)}g.poolSize=8192;g._augment=function(t){return t.__proto__=g.prototype,t};function di(t,e,r,n){if(typeof e=="number")throw new TypeError('"value" argument must not be a number');return typeof ArrayBuffer<"u"&&e instanceof ArrayBuffer?au(t,e,r,n):typeof e=="string"?su(t,e,r):uu(t,e)}g.from=function(t,e,r){return di(null,t,e,r)};g.TYPED_ARRAY_SUPPORT&&(g.prototype.__proto__=Uint8Array.prototype,g.__proto__=Uint8Array,typeof Symbol<"u"&&Symbol.species&&g[Symbol.species]);function mi(t){if(typeof t!="number")throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function ou(t,e,r,n){return mi(e),e<=0?se(t,e):r!==void 0?typeof n=="string"?se(t,e).fill(r,n):se(t,e).fill(r):se(t,e)}g.alloc=function(t,e,r){return ou(null,t,e,r)};function wr(t,e){if(mi(e),t=se(t,e<0?0:Er(e)|0),!g.TYPED_ARRAY_SUPPORT)for(var r=0;r<e;++r)t[r]=0;return t}g.allocUnsafe=function(t){return wr(null,t)};g.allocUnsafeSlow=function(t){return wr(null,t)};function su(t,e,r){if((typeof r!="string"||r==="")&&(r="utf8"),!g.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=yi(e,r)|0;t=se(t,n);var i=t.write(e,r);return i!==n&&(t=t.slice(0,i)),t}function ar(t,e){var r=e.length<0?0:Er(e.length)|0;t=se(t,r);for(var n=0;n<r;n+=1)t[n]=e[n]&255;return t}function au(t,e,r,n){if(e.byteLength,r<0||e.byteLength<r)throw new RangeError("'offset' is out of bounds");if(e.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");return r===void 0&&n===void 0?e=new Uint8Array(e):n===void 0?e=new Uint8Array(e,r):e=new Uint8Array(e,r,n),g.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=g.prototype):t=ar(t,e),t}function uu(t,e){if(ee(e)){var r=Er(e.length)|0;return t=se(t,r),t.length===0||e.copy(t,0,0,r),t}if(e){if(typeof ArrayBuffer<"u"&&e.buffer instanceof ArrayBuffer||"length"in e)return typeof e.length!="number"||Ou(e.length)?se(t,0):ar(t,e);if(e.type==="Buffer"&&pi(e.data))return ar(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function Er(t){if(t>=At())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+At().toString(16)+" bytes");return t|0}g.isBuffer=Ue;function ee(t){return!!(t!=null&&t._isBuffer)}g.compare=function(e,r){if(!ee(e)||!ee(r))throw new TypeError("Arguments must be Buffers");if(e===r)return 0;for(var n=e.length,i=r.length,o=0,s=Math.min(n,i);o<s;++o)if(e[o]!==r[o]){n=e[o],i=r[o];break}return n<i?-1:i<n?1:0};g.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}};g.concat=function(e,r){if(!pi(e))throw new TypeError('"list" argument must be an Array of Buffers');if(e.length===0)return g.alloc(0);var n;if(r===void 0)for(r=0,n=0;n<e.length;++n)r+=e[n].length;var i=g.allocUnsafe(r),o=0;for(n=0;n<e.length;++n){var s=e[n];if(!ee(s))throw new TypeError('"list" argument must be an Array of Buffers');s.copy(i,o),o+=s.length}return i};function yi(t,e){if(ee(t))return t.length;if(typeof ArrayBuffer<"u"&&typeof ArrayBuffer.isView=="function"&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;typeof t!="string"&&(t=""+t);var r=t.length;if(r===0)return 0;for(var n=!1;;)switch(e){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return St(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return r*2;case"hex":return r>>>1;case"base64":return Ti(t).length;default:if(n)return St(t).length;e=(""+e).toLowerCase(),n=!0}}g.byteLength=yi;function cu(t,e,r){var n=!1;if((e===void 0||e<0)&&(e=0),e>this.length||((r===void 0||r>this.length)&&(r=this.length),r<=0)||(r>>>=0,e>>>=0,r<=e))return"";for(t||(t="utf8");;)switch(t){case"hex":return Eu(this,e,r);case"utf8":case"utf-8":return Ei(this,e,r);case"ascii":return gu(this,e,r);case"latin1":case"binary":return wu(this,e,r);case"base64":return mu(this,e,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return bu(this,e,r);default:if(n)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),n=!0}}g.prototype._isBuffer=!0;function be(t,e,r){var n=t[e];t[e]=t[r],t[r]=n}g.prototype.swap16=function(){var e=this.length;if(e%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var r=0;r<e;r+=2)be(this,r,r+1);return this};g.prototype.swap32=function(){var e=this.length;if(e%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var r=0;r<e;r+=4)be(this,r,r+3),be(this,r+1,r+2);return this};g.prototype.swap64=function(){var e=this.length;if(e%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var r=0;r<e;r+=8)be(this,r,r+7),be(this,r+1,r+6),be(this,r+2,r+5),be(this,r+3,r+4);return this};g.prototype.toString=function(){var e=this.length|0;return e===0?"":arguments.length===0?Ei(this,0,e):cu.apply(this,arguments)};g.prototype.equals=function(e){if(!ee(e))throw new TypeError("Argument must be a Buffer");return this===e?!0:g.compare(this,e)===0};g.prototype.inspect=function(){var e="",r=iu;return this.length>0&&(e=this.toString("hex",0,r).match(/.{2}/g).join(" "),this.length>r&&(e+=" ... ")),"<Buffer "+e+">"};g.prototype.compare=function(e,r,n,i,o){if(!ee(e))throw new TypeError("Argument must be a Buffer");if(r===void 0&&(r=0),n===void 0&&(n=e?e.length:0),i===void 0&&(i=0),o===void 0&&(o=this.length),r<0||n>e.length||i<0||o>this.length)throw new RangeError("out of range index");if(i>=o&&r>=n)return 0;if(i>=o)return-1;if(r>=n)return 1;if(r>>>=0,n>>>=0,i>>>=0,o>>>=0,this===e)return 0;for(var s=o-i,u=n-r,l=Math.min(s,u),p=this.slice(i,o),y=e.slice(r,n),h=0;h<l;++h)if(p[h]!==y[h]){s=p[h],u=y[h];break}return s<u?-1:u<s?1:0};function gi(t,e,r,n,i){if(t.length===0)return-1;if(typeof r=="string"?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:t.length-1),r<0&&(r=t.length+r),r>=t.length){if(i)return-1;r=t.length-1}else if(r<0)if(i)r=0;else return-1;if(typeof e=="string"&&(e=g.from(e,n)),ee(e))return e.length===0?-1:Rn(t,e,r,n,i);if(typeof e=="number")return e=e&255,g.TYPED_ARRAY_SUPPORT&&typeof Uint8Array.prototype.indexOf=="function"?i?Uint8Array.prototype.indexOf.call(t,e,r):Uint8Array.prototype.lastIndexOf.call(t,e,r):Rn(t,[e],r,n,i);throw new TypeError("val must be string, number or Buffer")}function Rn(t,e,r,n,i){var o=1,s=t.length,u=e.length;if(n!==void 0&&(n=String(n).toLowerCase(),n==="ucs2"||n==="ucs-2"||n==="utf16le"||n==="utf-16le")){if(t.length<2||e.length<2)return-1;o=2,s/=2,u/=2,r/=2}function l(T,c){return o===1?T[c]:T.readUInt16BE(c*o)}var p;if(i){var y=-1;for(p=r;p<s;p++)if(l(t,p)===l(e,y===-1?0:p-y)){if(y===-1&&(y=p),p-y+1===u)return y*o}else y!==-1&&(p-=p-y),y=-1}else for(r+u>s&&(r=s-u),p=r;p>=0;p--){for(var h=!0,_=0;_<u;_++)if(l(t,p+_)!==l(e,_)){h=!1;break}if(h)return p}return-1}g.prototype.includes=function(e,r,n){return this.indexOf(e,r,n)!==-1};g.prototype.indexOf=function(e,r,n){return gi(this,e,r,n,!0)};g.prototype.lastIndexOf=function(e,r,n){return gi(this,e,r,n,!1)};function fu(t,e,r,n){r=Number(r)||0;var i=t.length-r;n?(n=Number(n),n>i&&(n=i)):n=i;var o=e.length;if(o%2!==0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var s=0;s<n;++s){var u=parseInt(e.substr(s*2,2),16);if(isNaN(u))return s;t[r+s]=u}return s}function lu(t,e,r,n){return vt(St(e,t.length-r),t,r,n)}function wi(t,e,r,n){return vt(xu(e),t,r,n)}function hu(t,e,r,n){return wi(t,e,r,n)}function pu(t,e,r,n){return vt(Ti(e),t,r,n)}function du(t,e,r,n){return vt(Ru(e,t.length-r),t,r,n)}g.prototype.write=function(e,r,n,i){if(r===void 0)i="utf8",n=this.length,r=0;else if(n===void 0&&typeof r=="string")i=r,n=this.length,r=0;else if(isFinite(r))r=r|0,isFinite(n)?(n=n|0,i===void 0&&(i="utf8")):(i=n,n=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var o=this.length-r;if((n===void 0||n>o)&&(n=o),e.length>0&&(n<0||r<0)||r>this.length)throw new RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var s=!1;;)switch(i){case"hex":return fu(this,e,r,n);case"utf8":case"utf-8":return lu(this,e,r,n);case"ascii":return wi(this,e,r,n);case"latin1":case"binary":return hu(this,e,r,n);case"base64":return pu(this,e,r,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return du(this,e,r,n);default:if(s)throw new TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),s=!0}};g.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function mu(t,e,r){return e===0&&r===t.length?xn(t):xn(t.slice(e,r))}function Ei(t,e,r){r=Math.min(t.length,r);for(var n=[],i=e;i<r;){var o=t[i],s=null,u=o>239?4:o>223?3:o>191?2:1;if(i+u<=r){var l,p,y,h;switch(u){case 1:o<128&&(s=o);break;case 2:l=t[i+1],(l&192)===128&&(h=(o&31)<<6|l&63,h>127&&(s=h));break;case 3:l=t[i+1],p=t[i+2],(l&192)===128&&(p&192)===128&&(h=(o&15)<<12|(l&63)<<6|p&63,h>2047&&(h<55296||h>57343)&&(s=h));break;case 4:l=t[i+1],p=t[i+2],y=t[i+3],(l&192)===128&&(p&192)===128&&(y&192)===128&&(h=(o&15)<<18|(l&63)<<12|(p&63)<<6|y&63,h>65535&&h<1114112&&(s=h))}}s===null?(s=65533,u=1):s>65535&&(s-=65536,n.push(s>>>10&1023|55296),s=56320|s&1023),n.push(s),i+=u}return yu(n)}var On=4096;function yu(t){var e=t.length;if(e<=On)return String.fromCharCode.apply(String,t);for(var r="",n=0;n<e;)r+=String.fromCharCode.apply(String,t.slice(n,n+=On));return r}function gu(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]&127);return n}function wu(t,e,r){var n="";r=Math.min(t.length,r);for(var i=e;i<r;++i)n+=String.fromCharCode(t[i]);return n}function Eu(t,e,r){var n=t.length;(!e||e<0)&&(e=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=e;o<r;++o)i+=_u(t[o]);return i}function bu(t,e,r){for(var n=t.slice(e,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+n[o+1]*256);return i}g.prototype.slice=function(e,r){var n=this.length;e=~~e,r=r===void 0?n:~~r,e<0?(e+=n,e<0&&(e=0)):e>n&&(e=n),r<0?(r+=n,r<0&&(r=0)):r>n&&(r=n),r<e&&(r=e);var i;if(g.TYPED_ARRAY_SUPPORT)i=this.subarray(e,r),i.__proto__=g.prototype;else{var o=r-e;i=new g(o,void 0);for(var s=0;s<o;++s)i[s]=this[s+e]}return i};function $(t,e,r){if(t%1!==0||t<0)throw new RangeError("offset is not uint");if(t+e>r)throw new RangeError("Trying to access beyond buffer length")}g.prototype.readUIntLE=function(e,r,n){e=e|0,r=r|0,n||$(e,r,this.length);for(var i=this[e],o=1,s=0;++s<r&&(o*=256);)i+=this[e+s]*o;return i};g.prototype.readUIntBE=function(e,r,n){e=e|0,r=r|0,n||$(e,r,this.length);for(var i=this[e+--r],o=1;r>0&&(o*=256);)i+=this[e+--r]*o;return i};g.prototype.readUInt8=function(e,r){return r||$(e,1,this.length),this[e]};g.prototype.readUInt16LE=function(e,r){return r||$(e,2,this.length),this[e]|this[e+1]<<8};g.prototype.readUInt16BE=function(e,r){return r||$(e,2,this.length),this[e]<<8|this[e+1]};g.prototype.readUInt32LE=function(e,r){return r||$(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+this[e+3]*16777216};g.prototype.readUInt32BE=function(e,r){return r||$(e,4,this.length),this[e]*16777216+(this[e+1]<<16|this[e+2]<<8|this[e+3])};g.prototype.readIntLE=function(e,r,n){e=e|0,r=r|0,n||$(e,r,this.length);for(var i=this[e],o=1,s=0;++s<r&&(o*=256);)i+=this[e+s]*o;return o*=128,i>=o&&(i-=Math.pow(2,8*r)),i};g.prototype.readIntBE=function(e,r,n){e=e|0,r=r|0,n||$(e,r,this.length);for(var i=r,o=1,s=this[e+--i];i>0&&(o*=256);)s+=this[e+--i]*o;return o*=128,s>=o&&(s-=Math.pow(2,8*r)),s};g.prototype.readInt8=function(e,r){return r||$(e,1,this.length),this[e]&128?(255-this[e]+1)*-1:this[e]};g.prototype.readInt16LE=function(e,r){r||$(e,2,this.length);var n=this[e]|this[e+1]<<8;return n&32768?n|4294901760:n};g.prototype.readInt16BE=function(e,r){r||$(e,2,this.length);var n=this[e+1]|this[e]<<8;return n&32768?n|4294901760:n};g.prototype.readInt32LE=function(e,r){return r||$(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24};g.prototype.readInt32BE=function(e,r){return r||$(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]};g.prototype.readFloatLE=function(e,r){return r||$(e,4,this.length),Ft(this,e,!0,23,4)};g.prototype.readFloatBE=function(e,r){return r||$(e,4,this.length),Ft(this,e,!1,23,4)};g.prototype.readDoubleLE=function(e,r){return r||$(e,8,this.length),Ft(this,e,!0,52,8)};g.prototype.readDoubleBE=function(e,r){return r||$(e,8,this.length),Ft(this,e,!1,52,8)};function Y(t,e,r,n,i,o){if(!ee(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(r+n>t.length)throw new RangeError("Index out of range")}g.prototype.writeUIntLE=function(e,r,n,i){if(e=+e,r=r|0,n=n|0,!i){var o=Math.pow(2,8*n)-1;Y(this,e,r,n,o,0)}var s=1,u=0;for(this[r]=e&255;++u<n&&(s*=256);)this[r+u]=e/s&255;return r+n};g.prototype.writeUIntBE=function(e,r,n,i){if(e=+e,r=r|0,n=n|0,!i){var o=Math.pow(2,8*n)-1;Y(this,e,r,n,o,0)}var s=n-1,u=1;for(this[r+s]=e&255;--s>=0&&(u*=256);)this[r+s]=e/u&255;return r+n};g.prototype.writeUInt8=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,1,255,0),g.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[r]=e&255,r+1};function Dt(t,e,r,n){e<0&&(e=65535+e+1);for(var i=0,o=Math.min(t.length-r,2);i<o;++i)t[r+i]=(e&255<<8*(n?i:1-i))>>>(n?i:1-i)*8}g.prototype.writeUInt16LE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,2,65535,0),g.TYPED_ARRAY_SUPPORT?(this[r]=e&255,this[r+1]=e>>>8):Dt(this,e,r,!0),r+2};g.prototype.writeUInt16BE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,2,65535,0),g.TYPED_ARRAY_SUPPORT?(this[r]=e>>>8,this[r+1]=e&255):Dt(this,e,r,!1),r+2};function Nt(t,e,r,n){e<0&&(e=4294967295+e+1);for(var i=0,o=Math.min(t.length-r,4);i<o;++i)t[r+i]=e>>>(n?i:3-i)*8&255}g.prototype.writeUInt32LE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,4,4294967295,0),g.TYPED_ARRAY_SUPPORT?(this[r+3]=e>>>24,this[r+2]=e>>>16,this[r+1]=e>>>8,this[r]=e&255):Nt(this,e,r,!0),r+4};g.prototype.writeUInt32BE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,4,4294967295,0),g.TYPED_ARRAY_SUPPORT?(this[r]=e>>>24,this[r+1]=e>>>16,this[r+2]=e>>>8,this[r+3]=e&255):Nt(this,e,r,!1),r+4};g.prototype.writeIntLE=function(e,r,n,i){if(e=+e,r=r|0,!i){var o=Math.pow(2,8*n-1);Y(this,e,r,n,o-1,-o)}var s=0,u=1,l=0;for(this[r]=e&255;++s<n&&(u*=256);)e<0&&l===0&&this[r+s-1]!==0&&(l=1),this[r+s]=(e/u>>0)-l&255;return r+n};g.prototype.writeIntBE=function(e,r,n,i){if(e=+e,r=r|0,!i){var o=Math.pow(2,8*n-1);Y(this,e,r,n,o-1,-o)}var s=n-1,u=1,l=0;for(this[r+s]=e&255;--s>=0&&(u*=256);)e<0&&l===0&&this[r+s+1]!==0&&(l=1),this[r+s]=(e/u>>0)-l&255;return r+n};g.prototype.writeInt8=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,1,127,-128),g.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[r]=e&255,r+1};g.prototype.writeInt16LE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,2,32767,-32768),g.TYPED_ARRAY_SUPPORT?(this[r]=e&255,this[r+1]=e>>>8):Dt(this,e,r,!0),r+2};g.prototype.writeInt16BE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,2,32767,-32768),g.TYPED_ARRAY_SUPPORT?(this[r]=e>>>8,this[r+1]=e&255):Dt(this,e,r,!1),r+2};g.prototype.writeInt32LE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,4,2147483647,-2147483648),g.TYPED_ARRAY_SUPPORT?(this[r]=e&255,this[r+1]=e>>>8,this[r+2]=e>>>16,this[r+3]=e>>>24):Nt(this,e,r,!0),r+4};g.prototype.writeInt32BE=function(e,r,n){return e=+e,r=r|0,n||Y(this,e,r,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),g.TYPED_ARRAY_SUPPORT?(this[r]=e>>>24,this[r+1]=e>>>16,this[r+2]=e>>>8,this[r+3]=e&255):Nt(this,e,r,!1),r+4};function bi(t,e,r,n,i,o){if(r+n>t.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function Ai(t,e,r,n,i){return i||bi(t,e,r,4),hi(t,e,r,n,23,4),r+4}g.prototype.writeFloatLE=function(e,r,n){return Ai(this,e,r,!0,n)};g.prototype.writeFloatBE=function(e,r,n){return Ai(this,e,r,!1,n)};function Si(t,e,r,n,i){return i||bi(t,e,r,8),hi(t,e,r,n,52,8),r+8}g.prototype.writeDoubleLE=function(e,r,n){return Si(this,e,r,!0,n)};g.prototype.writeDoubleBE=function(e,r,n){return Si(this,e,r,!1,n)};g.prototype.copy=function(e,r,n,i){if(n||(n=0),!i&&i!==0&&(i=this.length),r>=e.length&&(r=e.length),r||(r=0),i>0&&i<n&&(i=n),i===n||e.length===0||this.length===0)return 0;if(r<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(i<0)throw new RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-r<i-n&&(i=e.length-r+n);var o=i-n,s;if(this===e&&n<r&&r<i)for(s=o-1;s>=0;--s)e[s+r]=this[s+n];else if(o<1e3||!g.TYPED_ARRAY_SUPPORT)for(s=0;s<o;++s)e[s+r]=this[s+n];else Uint8Array.prototype.set.call(e,this.subarray(n,n+o),r);return o};g.prototype.fill=function(e,r,n,i){if(typeof e=="string"){if(typeof r=="string"?(i=r,r=0,n=this.length):typeof n=="string"&&(i=n,n=this.length),e.length===1){var o=e.charCodeAt(0);o<256&&(e=o)}if(i!==void 0&&typeof i!="string")throw new TypeError("encoding must be a string");if(typeof i=="string"&&!g.isEncoding(i))throw new TypeError("Unknown encoding: "+i)}else typeof e=="number"&&(e=e&255);if(r<0||this.length<r||this.length<n)throw new RangeError("Out of range index");if(n<=r)return this;r=r>>>0,n=n===void 0?this.length:n>>>0,e||(e=0);var s;if(typeof e=="number")for(s=r;s<n;++s)this[s]=e;else{var u=ee(e)?e:St(new g(e,i).toString()),l=u.length;for(s=0;s<n-r;++s)this[s+r]=u[s%l]}return this};var Au=/[^+\/0-9A-Za-z-_]/g;function Su(t){if(t=Tu(t).replace(Au,""),t.length<2)return"";for(;t.length%4!==0;)t=t+"=";return t}function Tu(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function _u(t){return t<16?"0"+t.toString(16):t.toString(16)}function St(t,e){e=e||1/0;for(var r,n=t.length,i=null,o=[],s=0;s<n;++s){if(r=t.charCodeAt(s),r>55295&&r<57344){if(!i){if(r>56319){(e-=3)>-1&&o.push(239,191,189);continue}else if(s+1===n){(e-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(e-=3)>-1&&o.push(239,191,189),i=r;continue}r=(i-55296<<10|r-56320)+65536}else i&&(e-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((e-=1)<0)break;o.push(r)}else if(r<2048){if((e-=2)<0)break;o.push(r>>6|192,r&63|128)}else if(r<65536){if((e-=3)<0)break;o.push(r>>12|224,r>>6&63|128,r&63|128)}else if(r<1114112){if((e-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,r&63|128)}else throw new Error("Invalid code point")}return o}function xu(t){for(var e=[],r=0;r<t.length;++r)e.push(t.charCodeAt(r)&255);return e}function Ru(t,e){for(var r,n,i,o=[],s=0;s<t.length&&!((e-=2)<0);++s)r=t.charCodeAt(s),n=r>>8,i=r%256,o.push(i),o.push(n);return o}function Ti(t){return eu(Su(t))}function vt(t,e,r,n){for(var i=0;i<n&&!(i+r>=e.length||i>=t.length);++i)e[i+r]=t[i];return i}function Ou(t){return t!==t}function Ue(t){return t!=null&&(!!t._isBuffer||_i(t)||Iu(t))}function _i(t){return!!t.constructor&&typeof t.constructor.isBuffer=="function"&&t.constructor.isBuffer(t)}function Iu(t){return typeof t.readFloatLE=="function"&&typeof t.slice=="function"&&_i(t.slice(0,0))}function I(t,e,r,n,i){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=t,this.name="AxiosError",e&&(this.code=e),r&&(this.config=r),n&&(this.request=n),i&&(this.response=i)}d.inherits(I,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:d.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});var xi=I.prototype,Ri={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(t=>{Ri[t]={value:t}});Object.defineProperties(I,Ri);Object.defineProperty(xi,"isAxiosError",{value:!0});I.from=(t,e,r,n,i,o)=>{let s=Object.create(xi);return d.toFlatObject(t,s,function(l){return l!==Error.prototype},u=>u!=="isAxiosError"),I.call(s,t.message,e,r,n,i),s.cause=t,s.name=t.name,o&&_e(s,o),s};var Cu=null;function ur(t){return d.isPlainObject(t)||d.isArray(t)}function Oi(t){return d.endsWith(t,"[]")?t.slice(0,-2):t}function In(t,e,r){return t?t.concat(e).map(function(i,o){return i=Oi(i),!r&&o?"["+i+"]":i}).join(r?".":""):e}function Pu(t){return d.isArray(t)&&!t.some(ur)}var Fu=d.toFlatObject(d,{},null,function(e){return/^is[A-Z]/.test(e)});function Ut(t,e,r){if(!d.isObject(t))throw new TypeError("target must be an object");e=e||new FormData,r=d.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(w,S){return!d.isUndefined(S[w])});let n=r.metaTokens,i=r.visitor||y,o=r.dots,s=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&d.isSpecCompliantForm(e);if(!d.isFunction(i))throw new TypeError("visitor must be a function");function p(c){if(c===null)return"";if(d.isDate(c))return c.toISOString();if(!l&&d.isBlob(c))throw new I("Blob is not supported. Use a Buffer instead.");return d.isArrayBuffer(c)||d.isTypedArray(c)?l&&typeof Blob=="function"?new Blob([c]):g.from(c):c}function y(c,w,S){let A=c;if(c&&!S&&typeof c=="object"){if(d.endsWith(w,"{}"))w=n?w:w.slice(0,-2),c=JSON.stringify(c);else if(d.isArray(c)&&Pu(c)||(d.isFileList(c)||d.endsWith(w,"[]"))&&(A=d.toArray(c)))return w=Oi(w),A.forEach(function(b,R){!(d.isUndefined(b)||b===null)&&e.append(s===!0?In([w],R,o):s===null?w:w+"[]",p(b))}),!1}return ur(c)?!0:(e.append(In(S,w,o),p(c)),!1)}let h=[],_=_e(Fu,{defaultVisitor:y,convertValue:p,isVisitable:ur});function T(c,w){if(!d.isUndefined(c)){if(h.indexOf(c)!==-1)throw Error("Circular reference detected in "+w.join("."));h.push(c),d.forEach(c,function(A,x){(!(d.isUndefined(A)||A===null)&&i.call(e,A,d.isString(x)?x.trim():x,w,_))===!0&&T(A,w?w.concat(x):[x])}),h.pop()}}if(!d.isObject(t))throw new TypeError("data must be an object");return T(t),e}function Cn(t){let e={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(t).replace(/[!'()~]|%20|%00/g,function(n){return e[n]})}function br(t,e){this._pairs=[],t&&Ut(t,this,e)}var Ii=br.prototype;Ii.append=function(e,r){this._pairs.push([e,r])};Ii.toString=function(e){let r=e?function(n){return e.call(this,n,Cn)}:Cn;return this._pairs.map(function(i){return r(i[0])+"="+r(i[1])},"").join("&")};function Du(t){return encodeURIComponent(t).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Ar(t,e,r){if(!e)return t;let n=r&&r.encode||Du,i=r&&r.serialize,o;if(i?o=i(e,r):o=d.isURLSearchParams(e)?e.toString():new br(e,r).toString(n),o){let s=t.indexOf("#");s!==-1&&(t=t.slice(0,s)),t+=(t.indexOf("?")===-1?"?":"&")+o}return t}var Tt=class{constructor(){this.handlers=[]}use(e,r,n){return this.handlers.push({fulfilled:e,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(e){this.handlers[e]&&(this.handlers[e]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(e){d.forEach(this.handlers,function(n){n!==null&&e(n)})}},Ci={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Nu=typeof URLSearchParams<"u"?URLSearchParams:br,vu=typeof FormData<"u"?FormData:null,Uu=typeof Blob<"u"?Blob:null,Pi={isBrowser:!0,classes:{URLSearchParams:Nu,FormData:vu,Blob:Uu},protocols:["http","https","file","blob","url","data"]},Fi=typeof self<"u"&&typeof document<"u",Bu=(t=>Fi&&["ReactNative","NativeScript","NS"].indexOf(t)<0)(typeof navigator<"u"&&navigator.product),ju=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",$u=Object.freeze({__proto__:null,hasBrowserEnv:Fi,hasStandardBrowserEnv:Bu,hasStandardBrowserWebWorkerEnv:ju}),Z={...$u,...Pi};function Lu(t,e){return Ut(t,new Z.classes.URLSearchParams,_e({visitor:function(r,n,i,o){return Z.isNode&&d.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},e))}function Mu(t){return d.matchAll(/\w+|\[(\w*)]/g,t).map(e=>e[0]==="[]"?"":e[1]||e[0])}function ku(t){let e={},r=Object.keys(t),n,i=r.length,o;for(n=0;n<i;n++)o=r[n],e[o]=t[o];return e}function Di(t){function e(r,n,i,o){let s=r[o++];if(s==="__proto__")return!0;let u=Number.isFinite(+s),l=o>=r.length;return s=!s&&d.isArray(i)?i.length:s,l?(d.hasOwnProp(i,s)?i[s]=[i[s],n]:i[s]=n,!u):((!i[s]||!d.isObject(i[s]))&&(i[s]=[]),e(r,n,i[s],o)&&d.isArray(i[s])&&(i[s]=ku(i[s])),!u)}if(d.isFormData(t)&&d.isFunction(t.entries)){let r={};return d.forEachEntry(t,(n,i)=>{e(Mu(n),i,r,0)}),r}return null}function qu(t,e,r){if(d.isString(t))try{return(e||JSON.parse)(t),d.trim(t)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(t)}var Sr={transitional:Ci,adapter:["xhr","http"],transformRequest:[function(e,r){let n=r.getContentType()||"",i=n.indexOf("application/json")>-1,o=d.isObject(e);if(o&&d.isHTMLForm(e)&&(e=new FormData(e)),d.isFormData(e))return i?JSON.stringify(Di(e)):e;if(d.isArrayBuffer(e)||d.isBuffer(e)||d.isStream(e)||d.isFile(e)||d.isBlob(e))return e;if(d.isArrayBufferView(e))return e.buffer;if(d.isURLSearchParams(e))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),e.toString();let u;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Lu(e,this.formSerializer).toString();if((u=d.isFileList(e))||n.indexOf("multipart/form-data")>-1){let l=this.env&&this.env.FormData;return Ut(u?{"files[]":e}:e,l&&new l,this.formSerializer)}}return o||i?(r.setContentType("application/json",!1),qu(e)):e}],transformResponse:[function(e){let r=this.transitional||Sr.transitional,n=r&&r.forcedJSONParsing,i=this.responseType==="json";if(e&&d.isString(e)&&(n&&!this.responseType||i)){let s=!(r&&r.silentJSONParsing)&&i;try{return JSON.parse(e)}catch(u){if(s)throw u.name==="SyntaxError"?I.from(u,I.ERR_BAD_RESPONSE,this,null,this.response):u}}return e}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Z.classes.FormData,Blob:Z.classes.Blob},validateStatus:function(e){return e>=200&&e<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};d.forEach(["delete","get","head","post","put","patch"],t=>{Sr.headers[t]={}});var Tr=Sr,Hu=d.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Yu=t=>{let e={},r,n,i;return t&&t.split(`
`).forEach(function(s){i=s.indexOf(":"),r=s.substring(0,i).trim().toLowerCase(),n=s.substring(i+1).trim(),!(!r||e[r]&&Hu[r])&&(r==="set-cookie"?e[r]?e[r].push(n):e[r]=[n]:e[r]=e[r]?e[r]+", "+n:n)}),e},Pn=Symbol("internals");function We(t){return t&&String(t).trim().toLowerCase()}function gt(t){return t===!1||t==null?t:d.isArray(t)?t.map(gt):String(t)}function zu(t){let e=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g,n;for(;n=r.exec(t);)e[n[1]]=n[2];return e}var Vu=t=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(t.trim());function Qt(t,e,r,n,i){if(d.isFunction(n))return n.call(this,e,r);if(i&&(e=r),!!d.isString(e)){if(d.isString(n))return e.indexOf(n)!==-1;if(d.isRegExp(n))return n.test(e)}}function Ju(t){return t.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(e,r,n)=>r.toUpperCase()+n)}function Gu(t,e){let r=d.toCamelCase(" "+e);["get","set","has"].forEach(n=>{Object.defineProperty(t,n+r,{value:function(i,o,s){return this[n].call(this,e,i,o,s)},configurable:!0})})}var $e=class{constructor(e){e&&this.set(e)}set(e,r,n){let i=this;function o(u,l,p){let y=We(l);if(!y)throw new Error("header name must be a non-empty string");let h=d.findKey(i,y);(!h||i[h]===void 0||p===!0||p===void 0&&i[h]!==!1)&&(i[h||l]=gt(u))}let s=(u,l)=>d.forEach(u,(p,y)=>o(p,y,l));return d.isPlainObject(e)||e instanceof this.constructor?s(e,r):d.isString(e)&&(e=e.trim())&&!Vu(e)?s(Yu(e),r):e!=null&&o(r,e,n),this}get(e,r){if(e=We(e),e){let n=d.findKey(this,e);if(n){let i=this[n];if(!r)return i;if(r===!0)return zu(i);if(d.isFunction(r))return r.call(this,i,n);if(d.isRegExp(r))return r.exec(i);throw new TypeError("parser must be boolean|regexp|function")}}}has(e,r){if(e=We(e),e){let n=d.findKey(this,e);return!!(n&&this[n]!==void 0&&(!r||Qt(this,this[n],n,r)))}return!1}delete(e,r){let n=this,i=!1;function o(s){if(s=We(s),s){let u=d.findKey(n,s);u&&(!r||Qt(n,n[u],u,r))&&(delete n[u],i=!0)}}return d.isArray(e)?e.forEach(o):o(e),i}clear(e){let r=Object.keys(this),n=r.length,i=!1;for(;n--;){let o=r[n];(!e||Qt(this,this[o],o,e,!0))&&(delete this[o],i=!0)}return i}normalize(e){let r=this,n={};return d.forEach(this,(i,o)=>{let s=d.findKey(n,o);if(s){r[s]=gt(i),delete r[o];return}let u=e?Ju(o):String(o).trim();u!==o&&delete r[o],r[u]=gt(i),n[u]=!0}),this}concat(...e){return this.constructor.concat(this,...e)}toJSON(e){let r=Object.create(null);return d.forEach(this,(n,i)=>{n!=null&&n!==!1&&(r[i]=e&&d.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([e,r])=>e+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(e){return e instanceof this?e:new this(e)}static concat(e,...r){let n=new this(e);return r.forEach(i=>n.set(i)),n}static accessor(e){let n=(this[Pn]=this[Pn]={accessors:{}}).accessors,i=this.prototype;function o(s){let u=We(s);n[u]||(Gu(i,s),n[u]=!0)}return d.isArray(e)?e.forEach(o):o(e),this}};$e.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);d.reduceDescriptors($e.prototype,({value:t},e)=>{let r=e[0].toUpperCase()+e.slice(1);return{get:()=>t,set(n){this[r]=n}}});d.freezeMethods($e);var ae=$e;function Zt(t,e){let r=this||Tr,n=e||r,i=ae.from(n.headers),o=n.data;return d.forEach(t,function(u){o=u.call(r,o,i.normalize(),e?e.status:void 0)}),i.normalize(),o}function Ni(t){return!!(t&&t.__CANCEL__)}function tt(t,e,r){I.call(this,t??"canceled",I.ERR_CANCELED,e,r),this.name="CanceledError"}d.inherits(tt,I,{__CANCEL__:!0});function vi(t,e,r){let n=r.config.validateStatus;!r.status||!n||n(r.status)?t(r):e(new I("Request failed with status code "+r.status,[I.ERR_BAD_REQUEST,I.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}var Ku=Z.hasStandardBrowserEnv?{write(t,e,r,n,i,o){let s=[t+"="+encodeURIComponent(e)];d.isNumber(r)&&s.push("expires="+new Date(r).toGMTString()),d.isString(n)&&s.push("path="+n),d.isString(i)&&s.push("domain="+i),o===!0&&s.push("secure"),document.cookie=s.join("; ")},read(t){let e=document.cookie.match(new RegExp("(^|;\\s*)("+t+")=([^;]*)"));return e?decodeURIComponent(e[3]):null},remove(t){this.write(t,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Wu(t){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(t)}function Xu(t,e){return e?t.replace(/\/?\/$/,"")+"/"+e.replace(/^\/+/,""):t}function _r(t,e){return t&&!Wu(e)?Xu(t,e):e}var Qu=Z.hasStandardBrowserEnv?function(){let e=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a"),n;function i(o){let s=o;return e&&(r.setAttribute("href",s),s=r.href),r.setAttribute("href",s),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:r.pathname.charAt(0)==="/"?r.pathname:"/"+r.pathname}}return n=i(self.location.href),function(s){let u=d.isString(s)?i(s):s;return u.protocol===n.protocol&&u.host===n.host}}():function(){return function(){return!0}}();function Zu(t){let e=/^([-+\w]{1,25})(:?\/\/|:)/.exec(t);return e&&e[1]||""}function ec(t,e){t=t||10;let r=new Array(t),n=new Array(t),i=0,o=0,s;return e=e!==void 0?e:1e3,function(l){let p=Date.now(),y=n[o];s||(s=p),r[i]=l,n[i]=p;let h=o,_=0;for(;h!==i;)_+=r[h++],h=h%t;if(i=(i+1)%t,i===o&&(o=(o+1)%t),p-s<e)return;let T=y&&p-y;return T?Math.round(_*1e3/T):void 0}}function Fn(t,e){let r=0,n=ec(50,250);return i=>{let o=i.loaded,s=i.lengthComputable?i.total:void 0,u=o-r,l=n(u),p=o<=s;r=o;let y={loaded:o,total:s,progress:s?o/s:void 0,bytes:u,rate:l||void 0,estimated:l&&s&&p?(s-o)/l:void 0,event:i};y[e?"download":"upload"]=!0,t(y)}}var tc=typeof XMLHttpRequest<"u",rc=tc&&function(t){return new Promise(function(r,n){let i=t.data,o=ae.from(t.headers).normalize(),{responseType:s,withXSRFToken:u}=t,l;function p(){t.cancelToken&&t.cancelToken.unsubscribe(l),t.signal&&t.signal.removeEventListener("abort",l)}let y;if(d.isFormData(i)){if(Z.hasStandardBrowserEnv||Z.hasStandardBrowserWebWorkerEnv)o.setContentType(!1);else if((y=o.getContentType())!==!1){let[w,...S]=y?y.split(";").map(A=>A.trim()).filter(Boolean):[];o.setContentType([w||"multipart/form-data",...S].join("; "))}}let h=new XMLHttpRequest;if(t.auth){let w=t.auth.username||"",S=t.auth.password?unescape(encodeURIComponent(t.auth.password)):"";o.set("Authorization","Basic "+btoa(w+":"+S))}let _=_r(t.baseURL,t.url);h.open(t.method.toUpperCase(),Ar(_,t.params,t.paramsSerializer),!0),h.timeout=t.timeout;function T(){if(!h)return;let w=ae.from("getAllResponseHeaders"in h&&h.getAllResponseHeaders()),A={data:!s||s==="text"||s==="json"?h.responseText:h.response,status:h.status,statusText:h.statusText,headers:w,config:t,request:h};vi(function(b){r(b),p()},function(b){n(b),p()},A),h=null}if("onloadend"in h?h.onloadend=T:h.onreadystatechange=function(){!h||h.readyState!==4||h.status===0&&!(h.responseURL&&h.responseURL.indexOf("file:")===0)||setTimeout(T)},h.onabort=function(){h&&(n(new I("Request aborted",I.ECONNABORTED,t,h)),h=null)},h.onerror=function(){n(new I("Network Error",I.ERR_NETWORK,t,h)),h=null},h.ontimeout=function(){let S=t.timeout?"timeout of "+t.timeout+"ms exceeded":"timeout exceeded",A=t.transitional||Ci;t.timeoutErrorMessage&&(S=t.timeoutErrorMessage),n(new I(S,A.clarifyTimeoutError?I.ETIMEDOUT:I.ECONNABORTED,t,h)),h=null},Z.hasStandardBrowserEnv&&(u&&d.isFunction(u)&&(u=u(t)),u||u!==!1&&Qu(_))){let w=t.xsrfHeaderName&&t.xsrfCookieName&&Ku.read(t.xsrfCookieName);w&&o.set(t.xsrfHeaderName,w)}i===void 0&&o.setContentType(null),"setRequestHeader"in h&&d.forEach(o.toJSON(),function(S,A){h.setRequestHeader(A,S)}),d.isUndefined(t.withCredentials)||(h.withCredentials=!!t.withCredentials),s&&s!=="json"&&(h.responseType=t.responseType),typeof t.onDownloadProgress=="function"&&h.addEventListener("progress",Fn(t.onDownloadProgress,!0)),typeof t.onUploadProgress=="function"&&h.upload&&h.upload.addEventListener("progress",Fn(t.onUploadProgress)),(t.cancelToken||t.signal)&&(l=w=>{h&&(n(!w||w.type?new tt(null,t,h):w),h.abort(),h=null)},t.cancelToken&&t.cancelToken.subscribe(l),t.signal&&(t.signal.aborted?l():t.signal.addEventListener("abort",l)));let c=Zu(_);if(c&&Z.protocols.indexOf(c)===-1){n(new I("Unsupported protocol "+c+":",I.ERR_BAD_REQUEST,t));return}h.send(i||null)})},cr={http:Cu,xhr:rc};d.forEach(cr,(t,e)=>{if(t){try{Object.defineProperty(t,"name",{value:e})}catch{}Object.defineProperty(t,"adapterName",{value:e})}});var Dn=t=>`- ${t}`,nc=t=>d.isFunction(t)||t===null||t===!1,Ui={getAdapter:t=>{t=d.isArray(t)?t:[t];let{length:e}=t,r,n,i={};for(let o=0;o<e;o++){r=t[o];let s;if(n=r,!nc(r)&&(n=cr[(s=String(r)).toLowerCase()],n===void 0))throw new I(`Unknown adapter '${s}'`);if(n)break;i[s||"#"+o]=n}if(!n){let o=Object.entries(i).map(([u,l])=>`adapter ${u} `+(l===!1?"is not supported by the environment":"is not available in the build")),s=e?o.length>1?`since :
`+o.map(Dn).join(`
`):" "+Dn(o[0]):"as no adapter specified";throw new I("There is no suitable adapter to dispatch the request "+s,"ERR_NOT_SUPPORT")}return n},adapters:cr};function er(t){if(t.cancelToken&&t.cancelToken.throwIfRequested(),t.signal&&t.signal.aborted)throw new tt(null,t)}function Nn(t){return er(t),t.headers=ae.from(t.headers),t.data=Zt.call(t,t.transformRequest),["post","put","patch"].indexOf(t.method)!==-1&&t.headers.setContentType("application/x-www-form-urlencoded",!1),Ui.getAdapter(t.adapter||Tr.adapter)(t).then(function(n){return er(t),n.data=Zt.call(t,t.transformResponse,n),n.headers=ae.from(n.headers),n},function(n){return Ni(n)||(er(t),n&&n.response&&(n.response.data=Zt.call(t,t.transformResponse,n.response),n.response.headers=ae.from(n.response.headers))),Promise.reject(n)})}var vn=t=>t instanceof ae?t.toJSON():t;function Le(t,e){e=e||{};let r={};function n(p,y,h){return d.isPlainObject(p)&&d.isPlainObject(y)?d.merge.call({caseless:h},p,y):d.isPlainObject(y)?d.merge({},y):d.isArray(y)?y.slice():y}function i(p,y,h){if(d.isUndefined(y)){if(!d.isUndefined(p))return n(void 0,p,h)}else return n(p,y,h)}function o(p,y){if(!d.isUndefined(y))return n(void 0,y)}function s(p,y){if(d.isUndefined(y)){if(!d.isUndefined(p))return n(void 0,p)}else return n(void 0,y)}function u(p,y,h){if(h in e)return n(p,y);if(h in t)return n(void 0,p)}let l={url:o,method:o,data:o,baseURL:s,transformRequest:s,transformResponse:s,paramsSerializer:s,timeout:s,timeoutMessage:s,withCredentials:s,withXSRFToken:s,adapter:s,responseType:s,xsrfCookieName:s,xsrfHeaderName:s,onUploadProgress:s,onDownloadProgress:s,decompress:s,maxContentLength:s,maxBodyLength:s,beforeRedirect:s,transport:s,httpAgent:s,httpsAgent:s,cancelToken:s,socketPath:s,responseEncoding:s,validateStatus:u,headers:(p,y)=>i(vn(p),vn(y),!0)};return d.forEach(Object.keys(_e({},t,e)),function(y){let h=l[y]||i,_=h(t[y],e[y],y);d.isUndefined(_)&&h!==u||(r[y]=_)}),r}var Bi="1.6.7",xr={};["object","boolean","number","function","string","symbol"].forEach((t,e)=>{xr[t]=function(n){return typeof n===t||"a"+(e<1?"n ":" ")+t}});var Un={};xr.transitional=function(e,r,n){function i(o,s){return"[Axios v"+Bi+"] Transitional option '"+o+"'"+s+(n?". "+n:"")}return(o,s,u)=>{if(e===!1)throw new I(i(s," has been removed"+(r?" in "+r:"")),I.ERR_DEPRECATED);return r&&!Un[s]&&(Un[s]=!0,console.warn(i(s," has been deprecated since v"+r+" and will be removed in the near future"))),e?e(o,s,u):!0}};function ic(t,e,r){if(typeof t!="object")throw new I("options must be an object",I.ERR_BAD_OPTION_VALUE);let n=Object.keys(t),i=n.length;for(;i-- >0;){let o=n[i],s=e[o];if(s){let u=t[o],l=u===void 0||s(u,o,t);if(l!==!0)throw new I("option "+o+" must be "+l,I.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new I("Unknown option "+o,I.ERR_BAD_OPTION)}}var fr={assertOptions:ic,validators:xr},fe=fr.validators,Me=class{constructor(e){this.defaults=e,this.interceptors={request:new Tt,response:new Tt}}async request(e,r){try{return await this._request(e,r)}catch(n){if(n instanceof Error){let i;Error.captureStackTrace?Error.captureStackTrace(i={}):i=new Error;let o=i.stack?i.stack.replace(/^.+\n/,""):"";n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}throw n}}_request(e,r){typeof e=="string"?(r=r||{},r.url=e):r=e||{},r=Le(this.defaults,r);let{transitional:n,paramsSerializer:i,headers:o}=r;n!==void 0&&fr.assertOptions(n,{silentJSONParsing:fe.transitional(fe.boolean),forcedJSONParsing:fe.transitional(fe.boolean),clarifyTimeoutError:fe.transitional(fe.boolean)},!1),i!=null&&(d.isFunction(i)?r.paramsSerializer={serialize:i}:fr.assertOptions(i,{encode:fe.function,serialize:fe.function},!0)),r.method=(r.method||this.defaults.method||"get").toLowerCase();let s=o&&d.merge(o.common,o[r.method]);o&&d.forEach(["delete","get","head","post","put","patch","common"],c=>{delete o[c]}),r.headers=ae.concat(s,o);let u=[],l=!0;this.interceptors.request.forEach(function(w){typeof w.runWhen=="function"&&w.runWhen(r)===!1||(l=l&&w.synchronous,u.unshift(w.fulfilled,w.rejected))});let p=[];this.interceptors.response.forEach(function(w){p.push(w.fulfilled,w.rejected)});let y,h=0,_;if(!l){let c=[Nn.bind(this),void 0];for(c.unshift.apply(c,u),c.push.apply(c,p),_=c.length,y=Promise.resolve(r);h<_;)y=y.then(c[h++],c[h++]);return y}_=u.length;let T=r;for(h=0;h<_;){let c=u[h++],w=u[h++];try{T=c(T)}catch(S){w.call(this,S);break}}try{y=Nn.call(this,T)}catch(c){return Promise.reject(c)}for(h=0,_=p.length;h<_;)y=y.then(p[h++],p[h++]);return y}getUri(e){e=Le(this.defaults,e);let r=_r(e.baseURL,e.url);return Ar(r,e.params,e.paramsSerializer)}};d.forEach(["delete","get","head","options"],function(e){Me.prototype[e]=function(r,n){return this.request(Le(n||{},{method:e,url:r,data:(n||{}).data}))}});d.forEach(["post","put","patch"],function(e){function r(n){return function(o,s,u){return this.request(Le(u||{},{method:e,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:s}))}}Me.prototype[e]=r(),Me.prototype[e+"Form"]=r(!0)});var wt=Me,lr=class t{constructor(e){if(typeof e!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});let n=this;this.promise.then(i=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](i);n._listeners=null}),this.promise.then=i=>{let o,s=new Promise(u=>{n.subscribe(u),o=u}).then(i);return s.cancel=function(){n.unsubscribe(o)},s},e(function(o,s,u){n.reason||(n.reason=new tt(o,s,u),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(e){if(this.reason){e(this.reason);return}this._listeners?this._listeners.push(e):this._listeners=[e]}unsubscribe(e){if(!this._listeners)return;let r=this._listeners.indexOf(e);r!==-1&&this._listeners.splice(r,1)}static source(){let e;return{token:new t(function(i){e=i}),cancel:e}}},oc=lr;function sc(t){return function(r){return t.apply(null,r)}}function ac(t){return d.isObject(t)&&t.isAxiosError===!0}var hr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(hr).forEach(([t,e])=>{hr[e]=t});var uc=hr;function ji(t){let e=new wt(t),r=ni(wt.prototype.request,e);return d.extend(r,wt.prototype,e,{allOwnKeys:!0}),d.extend(r,e,null,{allOwnKeys:!0}),r.create=function(i){return ji(Le(t,i))},r}var N=ji(Tr);N.Axios=wt;N.CanceledError=tt;N.CancelToken=oc;N.isCancel=Ni;N.VERSION=Bi;N.toFormData=Ut;N.AxiosError=I;N.Cancel=N.CanceledError;N.all=function(e){return Promise.all(e)};N.spread=sc;N.isAxiosError=ac;N.mergeConfig=Le;N.AxiosHeaders=ae;N.formToJSON=t=>Di(d.isHTMLForm(t)?new FormData(t):t);N.getAdapter=Ui.getAdapter;N.HttpStatusCode=uc;N.default=N;var lt=typeof globalThis<"u"?globalThis:typeof self<"u"||typeof self<"u"||typeof self<"u"?self:{};function Ve(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var cc=new Set(["ENOTFOUND","ENETUNREACH","UNABLE_TO_GET_ISSUER_CERT","UNABLE_TO_GET_CRL","UNABLE_TO_DECRYPT_CERT_SIGNATURE","UNABLE_TO_DECRYPT_CRL_SIGNATURE","UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY","CERT_SIGNATURE_FAILURE","CRL_SIGNATURE_FAILURE","CERT_NOT_YET_VALID","CERT_HAS_EXPIRED","CRL_NOT_YET_VALID","CRL_HAS_EXPIRED","ERROR_IN_CERT_NOT_BEFORE_FIELD","ERROR_IN_CERT_NOT_AFTER_FIELD","ERROR_IN_CRL_LAST_UPDATE_FIELD","ERROR_IN_CRL_NEXT_UPDATE_FIELD","OUT_OF_MEM","DEPTH_ZERO_SELF_SIGNED_CERT","SELF_SIGNED_CERT_IN_CHAIN","UNABLE_TO_GET_ISSUER_CERT_LOCALLY","UNABLE_TO_VERIFY_LEAF_SIGNATURE","CERT_CHAIN_TOO_LONG","CERT_REVOKED","INVALID_CA","PATH_LENGTH_EXCEEDED","INVALID_PURPOSE","CERT_UNTRUSTED","CERT_REJECTED","HOSTNAME_MISMATCH"]),fc=t=>!cc.has(t&&t.code),lc=Ve(fc);function Bn(t,e,r,n,i,o,s){try{var u=t[o](s),l=u.value}catch(p){r(p);return}u.done?e(l):Promise.resolve(l).then(n,i)}function $i(t){return function(){var e=this,r=arguments;return new Promise(function(n,i){var o=t.apply(e,r);function s(l){Bn(o,n,i,s,u,"next",l)}function u(l){Bn(o,n,i,s,u,"throw",l)}s(void 0)})}}function jn(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),r.push.apply(r,n)}return r}function tr(t){for(var e=1;e<arguments.length;e++){var r=arguments[e]!=null?arguments[e]:{};e%2?jn(Object(r),!0).forEach(function(n){hc(t,n,r[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):jn(Object(r)).forEach(function(n){Object.defineProperty(t,n,Object.getOwnPropertyDescriptor(r,n))})}return t}function hc(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}var Li="axios-retry";function Mi(t){var e=["ERR_CANCELED","ECONNABORTED"];return!t.response&&!!t.code&&!e.includes(t.code)&&lc(t)}var ki=["get","head","options"],pc=ki.concat(["put","delete"]);function Rr(t){return t.code!=="ECONNABORTED"&&(!t.response||t.response.status>=500&&t.response.status<=599)}function dc(t){return t.config?Rr(t)&&ki.indexOf(t.config.method)!==-1:!1}function qi(t){return t.config?Rr(t)&&pc.indexOf(t.config.method)!==-1:!1}function Hi(t){return Mi(t)||qi(t)}function mc(){return 0}function yc(){var t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0,e=arguments.length>2&&arguments[2]!==void 0?arguments[2]:100,r=Math.pow(2,t)*e,n=r*.2*Math.random();return r+n}var gc={retries:3,retryCondition:Hi,retryDelay:mc,shouldResetTimeout:!1,onRetry:()=>{}};function wc(t,e){return tr(tr(tr({},gc),e),t[Li])}function $n(t,e){var r=wc(t,e);return r.retryCount=r.retryCount||0,t[Li]=r,r}function Ec(t,e){t.defaults.agent===e.agent&&delete e.agent,t.defaults.httpAgent===e.httpAgent&&delete e.httpAgent,t.defaults.httpsAgent===e.httpsAgent&&delete e.httpsAgent}function bc(t,e){return pr.apply(this,arguments)}function pr(){return pr=$i(function*(t,e){var{retries:r,retryCondition:n}=t,i=t.retryCount<r&&n(e);if(typeof i=="object")try{var o=yield i;return o!==!1}catch{return!1}return i}),pr.apply(this,arguments)}function ue(t,e){var r=t.interceptors.request.use(i=>{var o=$n(i,e);return o.lastRequestTime=Date.now(),i}),n=t.interceptors.response.use(null,function(){var i=$i(function*(o){var{config:s}=o;if(!s)return Promise.reject(o);var u=$n(s,e);if(yield bc(u,o)){u.retryCount+=1;var{retryDelay:l,shouldResetTimeout:p,onRetry:y}=u,h=l(u.retryCount,o);if(Ec(t,s),!p&&s.timeout&&u.lastRequestTime){var _=Date.now()-u.lastRequestTime,T=s.timeout-_-h;if(T<=0)return Promise.reject(o);s.timeout=T}return s.transformRequest=[c=>c],yield y(u.retryCount,o,s),new Promise(c=>setTimeout(()=>c(t(s)),h))}return Promise.reject(o)});return function(o){return i.apply(this,arguments)}}());return{requestInterceptorId:r,responseInterceptorId:n}}ue.isNetworkError=Mi;ue.isSafeRequestError=dc;ue.isIdempotentRequestError=qi;ue.isNetworkOrIdempotentRequestError=Hi;ue.exponentialDelay=yc;ue.isRetryableError=Rr;var ke=1e3,qe=ke*60,He=qe*60,xe=He*24,Ac=xe*7,Sc=xe*365.25,Tc=function(t,e){e=e||{};var r=typeof t;if(r==="string"&&t.length>0)return _c(t);if(r==="number"&&isFinite(t))return e.long?Rc(t):xc(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))};function _c(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var r=parseFloat(e[1]),n=(e[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return r*Sc;case"weeks":case"week":case"w":return r*Ac;case"days":case"day":case"d":return r*xe;case"hours":case"hour":case"hrs":case"hr":case"h":return r*He;case"minutes":case"minute":case"mins":case"min":case"m":return r*qe;case"seconds":case"second":case"secs":case"sec":case"s":return r*ke;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}function xc(t){var e=Math.abs(t);return e>=xe?Math.round(t/xe)+"d":e>=He?Math.round(t/He)+"h":e>=qe?Math.round(t/qe)+"m":e>=ke?Math.round(t/ke)+"s":t+"ms"}function Rc(t){var e=Math.abs(t);return e>=xe?ht(t,e,xe,"day"):e>=He?ht(t,e,He,"hour"):e>=qe?ht(t,e,qe,"minute"):e>=ke?ht(t,e,ke,"second"):t+" ms"}function ht(t,e,r,n){var i=e>=r*1.5;return Math.round(t/r)+" "+n+(i?"s":"")}var Oc=Ve(Tc),Ae=256,Et=[],pt;for(;Ae--;)Et[Ae]=(Ae+256).toString(16).substring(1);function Ic(){var t=0,e,r="";if(!pt||Ae+16>256){for(pt=Array(t=256);t--;)pt[t]=256*Math.random()|0;t=Ae=0}for(;t<16;t++)e=pt[Ae+t],t==6?r+=Et[e&15|64]:t==8?r+=Et[e&63|128]:r+=Et[e],t&1&&t>1&&t<11&&(r+="-");return Ae++,r}var Cc="[object String]",Pc=Object.prototype,Fc=Pc.toString,Dc=Array.isArray;function Nc(t){return!!t&&typeof t=="object"}function vc(t){return typeof t=="string"||!Dc(t)&&Nc(t)&&Fc.call(t)==Cc}var Uc=vc,Ln=Ve(Uc),_t={exports:{}};_t.exports;(function(t,e){var r=200,n="__lodash_hash_undefined__",i=9007199254740991,o="[object Arguments]",s="[object Array]",u="[object Boolean]",l="[object Date]",p="[object Error]",y="[object Function]",h="[object GeneratorFunction]",_="[object Map]",T="[object Number]",c="[object Object]",w="[object Promise]",S="[object RegExp]",A="[object Set]",x="[object String]",b="[object Symbol]",R="[object WeakMap]",v="[object ArrayBuffer]",D="[object DataView]",C="[object Float32Array]",U="[object Float64Array]",O="[object Int8Array]",B="[object Int16Array]",k="[object Int32Array]",L="[object Uint8Array]",z="[object Uint8ClampedArray]",V="[object Uint16Array]",me="[object Uint32Array]",Eo=/[\\^$.*+?()[\]{}|]/g,bo=/\w*$/,Ao=/^\[object .+?Constructor\]$/,So=/^(?:0|[1-9]\d*)$/,F={};F[o]=F[s]=F[v]=F[D]=F[u]=F[l]=F[C]=F[U]=F[O]=F[B]=F[k]=F[_]=F[T]=F[c]=F[S]=F[A]=F[x]=F[b]=F[L]=F[z]=F[V]=F[me]=!0,F[p]=F[y]=F[R]=!1;var To=typeof lt=="object"&&lt&&lt.Object===Object&&lt,_o=typeof self=="object"&&self&&self.Object===Object&&self,re=To||_o||Function("return this")(),Lr=e&&!e.nodeType&&e,Mr=Lr&&!0&&t&&!t.nodeType&&t,xo=Mr&&Mr.exports===Lr;function Ro(a,f){return a.set(f[0],f[1]),a}function Oo(a,f){return a.add(f),a}function Io(a,f){for(var m=-1,E=a?a.length:0;++m<E&&f(a[m],m,a)!==!1;);return a}function Co(a,f){for(var m=-1,E=f.length,j=a.length;++m<E;)a[j+m]=f[m];return a}function kr(a,f,m,E){var j=-1,M=a?a.length:0;for(E&&M&&(m=a[++j]);++j<M;)m=f(m,a[j],j,a);return m}function Po(a,f){for(var m=-1,E=Array(a);++m<a;)E[m]=f(m);return E}function Fo(a,f){return a?.[f]}function qr(a){var f=!1;if(a!=null&&typeof a.toString!="function")try{f=!!(a+"")}catch{}return f}function Hr(a){var f=-1,m=Array(a.size);return a.forEach(function(E,j){m[++f]=[j,E]}),m}function $t(a,f){return function(m){return a(f(m))}}function Yr(a){var f=-1,m=Array(a.size);return a.forEach(function(E){m[++f]=E}),m}var Do=Array.prototype,No=Function.prototype,nt=Object.prototype,Lt=re["__core-js_shared__"],zr=function(){var a=/[^.]+$/.exec(Lt&&Lt.keys&&Lt.keys.IE_PROTO||"");return a?"Symbol(src)_1."+a:""}(),Vr=No.toString,ce=nt.hasOwnProperty,it=nt.toString,vo=RegExp("^"+Vr.call(ce).replace(Eo,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Jr=xo?re.Buffer:void 0,Gr=re.Symbol,Kr=re.Uint8Array,Uo=$t(Object.getPrototypeOf,Object),Bo=Object.create,jo=nt.propertyIsEnumerable,$o=Do.splice,Wr=Object.getOwnPropertySymbols,Lo=Jr?Jr.isBuffer:void 0,Mo=$t(Object.keys,Object),Mt=Fe(re,"DataView"),Je=Fe(re,"Map"),kt=Fe(re,"Promise"),qt=Fe(re,"Set"),Ht=Fe(re,"WeakMap"),Ge=Fe(Object,"create"),ko=we(Mt),qo=we(Je),Ho=we(kt),Yo=we(qt),zo=we(Ht),Xr=Gr?Gr.prototype:void 0,Qr=Xr?Xr.valueOf:void 0;function ye(a){var f=-1,m=a?a.length:0;for(this.clear();++f<m;){var E=a[f];this.set(E[0],E[1])}}function Vo(){this.__data__=Ge?Ge(null):{}}function Jo(a){return this.has(a)&&delete this.__data__[a]}function Go(a){var f=this.__data__;if(Ge){var m=f[a];return m===n?void 0:m}return ce.call(f,a)?f[a]:void 0}function Ko(a){var f=this.__data__;return Ge?f[a]!==void 0:ce.call(f,a)}function Wo(a,f){var m=this.__data__;return m[a]=Ge&&f===void 0?n:f,this}ye.prototype.clear=Vo,ye.prototype.delete=Jo,ye.prototype.get=Go,ye.prototype.has=Ko,ye.prototype.set=Wo;function ne(a){var f=-1,m=a?a.length:0;for(this.clear();++f<m;){var E=a[f];this.set(E[0],E[1])}}function Xo(){this.__data__=[]}function Qo(a){var f=this.__data__,m=ot(f,a);if(m<0)return!1;var E=f.length-1;return m==E?f.pop():$o.call(f,m,1),!0}function Zo(a){var f=this.__data__,m=ot(f,a);return m<0?void 0:f[m][1]}function es(a){return ot(this.__data__,a)>-1}function ts(a,f){var m=this.__data__,E=ot(m,a);return E<0?m.push([a,f]):m[E][1]=f,this}ne.prototype.clear=Xo,ne.prototype.delete=Qo,ne.prototype.get=Zo,ne.prototype.has=es,ne.prototype.set=ts;function Ce(a){var f=-1,m=a?a.length:0;for(this.clear();++f<m;){var E=a[f];this.set(E[0],E[1])}}function rs(){this.__data__={hash:new ye,map:new(Je||ne),string:new ye}}function ns(a){return st(this,a).delete(a)}function is(a){return st(this,a).get(a)}function os(a){return st(this,a).has(a)}function ss(a,f){return st(this,a).set(a,f),this}Ce.prototype.clear=rs,Ce.prototype.delete=ns,Ce.prototype.get=is,Ce.prototype.has=os,Ce.prototype.set=ss;function Pe(a){this.__data__=new ne(a)}function as(){this.__data__=new ne}function us(a){return this.__data__.delete(a)}function cs(a){return this.__data__.get(a)}function fs(a){return this.__data__.has(a)}function ls(a,f){var m=this.__data__;if(m instanceof ne){var E=m.__data__;if(!Je||E.length<r-1)return E.push([a,f]),this;m=this.__data__=new Ce(E)}return m.set(a,f),this}Pe.prototype.clear=as,Pe.prototype.delete=us,Pe.prototype.get=cs,Pe.prototype.has=fs,Pe.prototype.set=ls;function hs(a,f){var m=Vt(a)||Bs(a)?Po(a.length,String):[],E=m.length,j=!!E;for(var M in a)(f||ce.call(a,M))&&!(j&&(M=="length"||Ds(M,E)))&&m.push(M);return m}function Zr(a,f,m){var E=a[f];(!(ce.call(a,f)&&nn(E,m))||m===void 0&&!(f in a))&&(a[f]=m)}function ot(a,f){for(var m=a.length;m--;)if(nn(a[m][0],f))return m;return-1}function ps(a,f){return a&&en(f,Jt(f),a)}function Yt(a,f,m,E,j,M,J){var q;if(E&&(q=M?E(a,j,M,J):E(a)),q!==void 0)return q;if(!at(a))return a;var an=Vt(a);if(an){if(q=Cs(a),!f)return Rs(a,q)}else{var De=ge(a),un=De==y||De==h;if($s(a))return Es(a,f);if(De==c||De==o||un&&!M){if(qr(a))return M?a:{};if(q=Ps(un?{}:a),!f)return Os(a,ps(q,a))}else{if(!F[De])return M?a:{};q=Fs(a,De,Yt,f)}}J||(J=new Pe);var cn=J.get(a);if(cn)return cn;if(J.set(a,q),!an)var fn=m?Is(a):Jt(a);return Io(fn||a,function(Gt,ut){fn&&(ut=Gt,Gt=a[ut]),Zr(q,ut,Yt(Gt,f,m,E,ut,a,J))}),q}function ds(a){return at(a)?Bo(a):{}}function ms(a,f,m){var E=f(a);return Vt(a)?E:Co(E,m(a))}function ys(a){return it.call(a)}function gs(a){if(!at(a)||vs(a))return!1;var f=sn(a)||qr(a)?vo:Ao;return f.test(we(a))}function ws(a){if(!rn(a))return Mo(a);var f=[];for(var m in Object(a))ce.call(a,m)&&m!="constructor"&&f.push(m);return f}function Es(a,f){if(f)return a.slice();var m=new a.constructor(a.length);return a.copy(m),m}function zt(a){var f=new a.constructor(a.byteLength);return new Kr(f).set(new Kr(a)),f}function bs(a,f){var m=f?zt(a.buffer):a.buffer;return new a.constructor(m,a.byteOffset,a.byteLength)}function As(a,f,m){var E=f?m(Hr(a),!0):Hr(a);return kr(E,Ro,new a.constructor)}function Ss(a){var f=new a.constructor(a.source,bo.exec(a));return f.lastIndex=a.lastIndex,f}function Ts(a,f,m){var E=f?m(Yr(a),!0):Yr(a);return kr(E,Oo,new a.constructor)}function _s(a){return Qr?Object(Qr.call(a)):{}}function xs(a,f){var m=f?zt(a.buffer):a.buffer;return new a.constructor(m,a.byteOffset,a.length)}function Rs(a,f){var m=-1,E=a.length;for(f||(f=Array(E));++m<E;)f[m]=a[m];return f}function en(a,f,m,E){m||(m={});for(var j=-1,M=f.length;++j<M;){var J=f[j],q=E?E(m[J],a[J],J,m,a):void 0;Zr(m,J,q===void 0?a[J]:q)}return m}function Os(a,f){return en(a,tn(a),f)}function Is(a){return ms(a,Jt,tn)}function st(a,f){var m=a.__data__;return Ns(f)?m[typeof f=="string"?"string":"hash"]:m.map}function Fe(a,f){var m=Fo(a,f);return gs(m)?m:void 0}var tn=Wr?$t(Wr,Object):ks,ge=ys;(Mt&&ge(new Mt(new ArrayBuffer(1)))!=D||Je&&ge(new Je)!=_||kt&&ge(kt.resolve())!=w||qt&&ge(new qt)!=A||Ht&&ge(new Ht)!=R)&&(ge=function(a){var f=it.call(a),m=f==c?a.constructor:void 0,E=m?we(m):void 0;if(E)switch(E){case ko:return D;case qo:return _;case Ho:return w;case Yo:return A;case zo:return R}return f});function Cs(a){var f=a.length,m=a.constructor(f);return f&&typeof a[0]=="string"&&ce.call(a,"index")&&(m.index=a.index,m.input=a.input),m}function Ps(a){return typeof a.constructor=="function"&&!rn(a)?ds(Uo(a)):{}}function Fs(a,f,m,E){var j=a.constructor;switch(f){case v:return zt(a);case u:case l:return new j(+a);case D:return bs(a,E);case C:case U:case O:case B:case k:case L:case z:case V:case me:return xs(a,E);case _:return As(a,E,m);case T:case x:return new j(a);case S:return Ss(a);case A:return Ts(a,E,m);case b:return _s(a)}}function Ds(a,f){return f=f??i,!!f&&(typeof a=="number"||So.test(a))&&a>-1&&a%1==0&&a<f}function Ns(a){var f=typeof a;return f=="string"||f=="number"||f=="symbol"||f=="boolean"?a!=="__proto__":a===null}function vs(a){return!!zr&&zr in a}function rn(a){var f=a&&a.constructor,m=typeof f=="function"&&f.prototype||nt;return a===m}function we(a){if(a!=null){try{return Vr.call(a)}catch{}try{return a+""}catch{}}return""}function Us(a){return Yt(a,!0,!0)}function nn(a,f){return a===f||a!==a&&f!==f}function Bs(a){return js(a)&&ce.call(a,"callee")&&(!jo.call(a,"callee")||it.call(a)==o)}var Vt=Array.isArray;function on(a){return a!=null&&Ls(a.length)&&!sn(a)}function js(a){return Ms(a)&&on(a)}var $s=Lo||qs;function sn(a){var f=at(a)?it.call(a):"";return f==y||f==h}function Ls(a){return typeof a=="number"&&a>-1&&a%1==0&&a<=i}function at(a){var f=typeof a;return!!a&&(f=="object"||f=="function")}function Ms(a){return!!a&&typeof a=="object"}function Jt(a){return on(a)?hs(a):ws(a)}function ks(){return[]}function qs(){return!1}t.exports=Us})(_t,_t.exports);var Bc=_t.exports,jc=Ve(Bc);async function $c(t){let e=Mc(t),r=[Lc(e,t)];t.timeout&&t.timeout>0&&r.push(new Promise(i=>{setTimeout(()=>{let o=t.timeoutErrorMessage?t.timeoutErrorMessage:"timeout of "+t.timeout+"ms exceeded";i(Yi(o,t,"ECONNABORTED",e))},t.timeout)}));let n=await Promise.race(r);return new Promise((i,o)=>{n instanceof Error?o(n):Object.prototype.toString.call(t.settle)==="[object Function]"?t.settle(i,o,n):vi(i,o,n)})}async function Lc(t,e){let r;try{r=await fetch(t)}catch{return Yi("Network Error",e,"ERR_NETWORK",t)}let n={ok:r.ok,status:r.status,statusText:r.statusText,headers:new Headers(r.headers),config:e,request:t};if(r.status>=200&&r.status!==204)switch(e.responseType){case"arraybuffer":n.data=await r.arrayBuffer();break;case"blob":n.data=await r.blob();break;case"json":n.data=await r.json();break;case"formData":n.data=await r.formData();break;default:n.data=await r.text();break}return n}function Mc(t){let e=new Headers(t.headers);if(t.auth){let s=t.auth.username||"",u=t.auth.password?decodeURI(encodeURIComponent(t.auth.password)):"";e.set("Authorization",`Basic ${btoa(s+":"+u)}`)}let r=t.method.toUpperCase(),n={headers:e,method:r};r!=="GET"&&r!=="HEAD"&&(n.body=t.data,d.isFormData(n.body)&&Pi.isStandardBrowserEnv()&&e.delete("Content-Type")),t.mode&&(n.mode=t.mode),t.cache&&(n.cache=t.cache),t.integrity&&(n.integrity=t.integrity),t.redirect&&(n.redirect=t.redirect),t.referrer&&(n.referrer=t.referrer),d.isUndefined(t.withCredentials)||(n.credentials=t.withCredentials?"include":"omit");let i=_r(t.baseURL,t.url),o=Ar(i,t.params,t.paramsSerializer);return new Request(o,n)}function Yi(t,e,r,n,i){if(N.AxiosError&&typeof N.AxiosError=="function")return new N.AxiosError(t,N.AxiosError[r],e,n,i);var o=new Error(t);return kc(o,e,r,n,i)}function kc(t,e,r,n,i){return t.config=e,r&&(t.code=r),t.request=n,t.response=i,t.isAxiosError=!0,t.toJSON=function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:this.config,code:this.code,status:this.response&&this.response.status?this.response.status:null}},t}var{toString:qc}=Object.prototype,Hc=1,Yc=["innerHTML","ownerDocument","style","attributes","nodeValue"];function zc(t){return t!==null&&typeof t=="object"}function Vc(t){return zc(t)&&t.nodeType===Hc&&typeof t.nodeName=="string"&&Yc.every(e=>e in t)}var Jc=function(t){if(t===void 0)return"undefined";if(t===null)return"null";if(Number.isNaN(t))return"nan";if(Array.isArray(t))return"array";switch(qc.call(t)){case"[object Date]":return"date";case"[object RegExp]":return"regexp";case"[object Arguments]":return"arguments";case"[object Error]":return"error"}return t&&Vc(t)?"element":t&&t instanceof Uint8Array&&t.constructor.name==="Buffer"?"buffer":(typeof t.valueOf=="function"&&(t=t.valueOf?.()??Object.prototype.valueOf.apply(t)),typeof t)},rr=Ve(Jc),Gc=function(t,e,r){if(e=e||"and",r=r||", ",t.length<2)return t[0]||"";var n=e.slice(0,2)===r;return n?t.length==2&&(e=e.slice(1)):e=" "+e,t.slice(0,-1).join(r)+e+" "+t[t.length-1]},Kc=Ve(Gc),dr;typeof Object.create=="function"?dr=function(e,r){e.super_=r,e.prototype=Object.create(r.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:dr=function(e,r){e.super_=r;var n=function(){};n.prototype=r.prototype,e.prototype=new n,e.prototype.constructor=e};var Wc=dr;function Se(t,e){var r={seen:[],stylize:Qc};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),zi(e)?r.showHidden=e:e&&sf(r,e),Be(r.showHidden)&&(r.showHidden=!1),Be(r.depth)&&(r.depth=2),Be(r.colors)&&(r.colors=!1),Be(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=Xc),xt(r,t,r.depth)}Se.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]};Se.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};function Xc(t,e){var r=Se.styles[e];return r?"\x1B["+Se.colors[r][0]+"m"+t+"\x1B["+Se.colors[r][1]+"m":t}function Qc(t,e){return t}function Zc(t){var e={};return t.forEach(function(r,n){e[r]=!0}),e}function xt(t,e,r){if(t.customInspect&&e&&Qe(e.inspect)&&e.inspect!==Se&&!(e.constructor&&e.constructor.prototype===e)){var n=e.inspect(r,t);return Ji(n)||(n=xt(t,n,r)),n}var i=ef(t,e);if(i)return i;var o=Object.keys(e),s=Zc(o);if(t.showHidden&&(o=Object.getOwnPropertyNames(e)),bt(e)&&(o.indexOf("message")>=0||o.indexOf("description")>=0))return nr(e);if(o.length===0){if(Qe(e)){var u=e.name?": "+e.name:"";return t.stylize("[Function"+u+"]","special")}if(Xe(e))return t.stylize(RegExp.prototype.toString.call(e),"regexp");if(Rt(e))return t.stylize(Date.prototype.toString.call(e),"date");if(bt(e))return nr(e)}var l="",p=!1,y=["{","}"];if(nf(e)&&(p=!0,y=["[","]"]),Qe(e)){var h=e.name?": "+e.name:"";l=" [Function"+h+"]"}if(Xe(e)&&(l=" "+RegExp.prototype.toString.call(e)),Rt(e)&&(l=" "+Date.prototype.toUTCString.call(e)),bt(e)&&(l=" "+nr(e)),o.length===0&&(!p||e.length==0))return y[0]+l+y[1];if(r<0)return Xe(e)?t.stylize(RegExp.prototype.toString.call(e),"regexp"):t.stylize("[Object]","special");t.seen.push(e);var _;return p?_=tf(t,e,r,s,o):_=o.map(function(T){return mr(t,e,r,s,T,p)}),t.seen.pop(),rf(_,l,y)}function ef(t,e){if(Be(e))return t.stylize("undefined","undefined");if(Ji(e)){var r="'"+JSON.stringify(e).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return t.stylize(r,"string")}if(of(e))return t.stylize(""+e,"number");if(zi(e))return t.stylize(""+e,"boolean");if(Vi(e))return t.stylize("null","null")}function nr(t){return"["+Error.prototype.toString.call(t)+"]"}function tf(t,e,r,n,i){for(var o=[],s=0,u=e.length;s<u;++s)Gi(e,String(s))?o.push(mr(t,e,r,n,String(s),!0)):o.push("");return i.forEach(function(l){l.match(/^\d+$/)||o.push(mr(t,e,r,n,l,!0))}),o}function mr(t,e,r,n,i,o){var s,u,l;if(l=Object.getOwnPropertyDescriptor(e,i)||{value:e[i]},l.get?l.set?u=t.stylize("[Getter/Setter]","special"):u=t.stylize("[Getter]","special"):l.set&&(u=t.stylize("[Setter]","special")),Gi(n,i)||(s="["+i+"]"),u||(t.seen.indexOf(l.value)<0?(Vi(r)?u=xt(t,l.value,null):u=xt(t,l.value,r-1),u.indexOf(`
`)>-1&&(o?u=u.split(`
`).map(function(p){return"  "+p}).join(`
`).substr(2):u=`
`+u.split(`
`).map(function(p){return"   "+p}).join(`
`))):u=t.stylize("[Circular]","special")),Be(s)){if(o&&i.match(/^\d+$/))return u;s=JSON.stringify(""+i),s.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(s=s.substr(1,s.length-2),s=t.stylize(s,"name")):(s=s.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),s=t.stylize(s,"string"))}return s+": "+u}function rf(t,e,r){var n=t.reduce(function(i,o){return o.indexOf(`
`)>=0,i+o.replace(/\u001b\[\d\d?m/g,"").length+1},0);return n>60?r[0]+(e===""?"":e+`
 `)+" "+t.join(`,
  `)+" "+r[1]:r[0]+e+" "+t.join(", ")+" "+r[1]}function nf(t){return Array.isArray(t)}function zi(t){return typeof t=="boolean"}function Vi(t){return t===null}function of(t){return typeof t=="number"}function Ji(t){return typeof t=="string"}function Be(t){return t===void 0}function Xe(t){return Bt(t)&&Or(t)==="[object RegExp]"}function Bt(t){return typeof t=="object"&&t!==null}function Rt(t){return Bt(t)&&Or(t)==="[object Date]"}function bt(t){return Bt(t)&&(Or(t)==="[object Error]"||t instanceof Error)}function Qe(t){return typeof t=="function"}function Mn(t){return t===null||typeof t=="boolean"||typeof t=="number"||typeof t=="string"||typeof t=="symbol"||typeof t>"u"}function Or(t){return Object.prototype.toString.call(t)}function sf(t,e){if(!e||!Bt(e))return t;for(var r=Object.keys(e),n=r.length;n--;)t[r[n]]=e[r[n]];return t}function Gi(t,e){return Object.prototype.hasOwnProperty.call(t,e)}function kn(t,e){if(t===e)return 0;for(var r=t.length,n=e.length,i=0,o=Math.min(r,n);i<o;++i)if(t[i]!==e[i]){r=t[i],n=e[i];break}return r<n?-1:n<r?1:0}var af=Object.prototype.hasOwnProperty,qn=Object.keys||function(t){var e=[];for(var r in t)af.call(t,r)&&e.push(r);return e},Hn=Array.prototype.slice,ir;function Ki(){return typeof ir<"u"?ir:ir=function(){return function(){}.name==="foo"}()}function Yn(t){return Object.prototype.toString.call(t)}function zn(t){return Ue(t)||typeof Te.ArrayBuffer!="function"?!1:typeof ArrayBuffer.isView=="function"?ArrayBuffer.isView(t):t?!!(t instanceof DataView||t.buffer&&t.buffer instanceof ArrayBuffer):!1}function P(t,e){t||H(t,!0,e,"==",Cr)}var uf=/\s*function\s+([^\(\s]*)\s*/;function Wi(t){if(Qe(t)){if(Ki())return t.name;var e=t.toString(),r=e.match(uf);return r&&r[1]}}P.AssertionError=Ir;function Ir(t){this.name="AssertionError",this.actual=t.actual,this.expected=t.expected,this.operator=t.operator,t.message?(this.message=t.message,this.generatedMessage=!1):(this.message=cf(this),this.generatedMessage=!0);var e=t.stackStartFunction||H;if(Error.captureStackTrace)Error.captureStackTrace(this,e);else{var r=new Error;if(r.stack){var n=r.stack,i=Wi(e),o=n.indexOf(`
`+i);if(o>=0){var s=n.indexOf(`
`,o+1);n=n.substring(s+1)}this.stack=n}}}Wc(Ir,Error);function Vn(t,e){return typeof t=="string"?t.length<e?t:t.slice(0,e):t}function Jn(t){if(Ki()||!Qe(t))return Se(t);var e=Wi(t),r=e?": "+e:"";return"[Function"+r+"]"}function cf(t){return Vn(Jn(t.actual),128)+" "+t.operator+" "+Vn(Jn(t.expected),128)}function H(t,e,r,n,i){throw new Ir({message:r,actual:t,expected:e,operator:n,stackStartFunction:i})}P.fail=H;function Cr(t,e){t||H(t,!0,e,"==",Cr)}P.ok=Cr;P.equal=Xi;function Xi(t,e,r){t!=e&&H(t,e,r,"==",Xi)}P.notEqual=Qi;function Qi(t,e,r){t==e&&H(t,e,r,"!=",Qi)}P.deepEqual=Zi;function Zi(t,e,r){Ye(t,e,!1)||H(t,e,r,"deepEqual",Zi)}P.deepStrictEqual=eo;function eo(t,e,r){Ye(t,e,!0)||H(t,e,r,"deepStrictEqual",eo)}function Ye(t,e,r,n){if(t===e)return!0;if(Ue(t)&&Ue(e))return kn(t,e)===0;if(Rt(t)&&Rt(e))return t.getTime()===e.getTime();if(Xe(t)&&Xe(e))return t.source===e.source&&t.global===e.global&&t.multiline===e.multiline&&t.lastIndex===e.lastIndex&&t.ignoreCase===e.ignoreCase;if((t===null||typeof t!="object")&&(e===null||typeof e!="object"))return r?t===e:t==e;if(zn(t)&&zn(e)&&Yn(t)===Yn(e)&&!(t instanceof Float32Array||t instanceof Float64Array))return kn(new Uint8Array(t.buffer),new Uint8Array(e.buffer))===0;if(Ue(t)!==Ue(e))return!1;n=n||{actual:[],expected:[]};var i=n.actual.indexOf(t);return i!==-1&&i===n.expected.indexOf(e)?!0:(n.actual.push(t),n.expected.push(e),ff(t,e,r,n))}function Gn(t){return Object.prototype.toString.call(t)=="[object Arguments]"}function ff(t,e,r,n){if(t==null||e===null||e===void 0)return!1;if(Mn(t)||Mn(e))return t===e;if(r&&Object.getPrototypeOf(t)!==Object.getPrototypeOf(e))return!1;var i=Gn(t),o=Gn(e);if(i&&!o||!i&&o)return!1;if(i)return t=Hn.call(t),e=Hn.call(e),Ye(t,e,r);var s=qn(t),u=qn(e),l,p;if(s.length!==u.length)return!1;for(s.sort(),u.sort(),p=s.length-1;p>=0;p--)if(s[p]!==u[p])return!1;for(p=s.length-1;p>=0;p--)if(l=s[p],!Ye(t[l],e[l],r,n))return!1;return!0}P.notDeepEqual=to;function to(t,e,r){Ye(t,e,!1)&&H(t,e,r,"notDeepEqual",to)}P.notDeepStrictEqual=ro;function ro(t,e,r){Ye(t,e,!0)&&H(t,e,r,"notDeepStrictEqual",ro)}P.strictEqual=no;function no(t,e,r){t!==e&&H(t,e,r,"===",no)}P.notStrictEqual=io;function io(t,e,r){t===e&&H(t,e,r,"!==",io)}function Kn(t,e){if(!t||!e)return!1;if(Object.prototype.toString.call(e)=="[object RegExp]")return e.test(t);try{if(t instanceof e)return!0}catch{}return Error.isPrototypeOf(e)?!1:e.call({},t)===!0}function lf(t){var e;try{t()}catch(r){e=r}return e}function oo(t,e,r,n){var i;if(typeof e!="function")throw new TypeError('"block" argument must be a function');typeof r=="string"&&(n=r,r=null),i=lf(e),n=(r&&r.name?" ("+r.name+").":".")+(n?" "+n:"."),t&&!i&&H(i,r,"Missing expected exception"+n);var o=typeof n=="string",s=!t&&bt(i),u=!t&&i&&!r;if((s&&o&&Kn(i,r)||u)&&H(i,r,"Got unwanted exception"+n),t&&i&&r&&!Kn(i,r)||!t&&i)throw i}P.throws=hf;function hf(t,e,r){oo(!0,t,e,r)}P.doesNotThrow=pf;function pf(t,e,r){oo(!1,t,e,r)}P.ifError=df;function df(t){if(t)throw t}var mf=32768,yf=t=>{P(t.anonymousId||t.userId,'You must pass either an "anonymousId" or a "userId".'),P(t.event,'You must pass an "event".')},gf=t=>{P(t.anonymousId||t.userId,'You must pass either an "anonymousId" or a "userId".'),P(t.groupId,'You must pass a "groupId".')},wf=t=>{P(t.anonymousId||t.userId,'You must pass either an "anonymousId" or a "userId".')},Ef=t=>{P(t.anonymousId||t.userId,'You must pass either an "anonymousId" or a "userId".')},bf=t=>{P(t.anonymousId||t.userId,'You must pass either an "anonymousId" or a "userId".')},Af=t=>{P(t.userId,'You must pass a "userId".'),P(t.previousId,'You must pass a "previousId".')},Wn={anonymousId:["string","number"],category:"string",context:"object",event:"string",groupId:["string","number"],integrations:"object",name:"string",previousId:["string","number"],timestamp:"date",userId:["string","number"],type:"string"},Sf=t=>{P(rr(t)==="object","You must pass a message object.");let e=JSON.stringify(t);P(g.byteLength(e,"utf8")<mf,"Your message must be < 32kb.");for(let r in Wn){let n=t[r];if(!n)continue;let i=Wn[r];rr(i)!=="array"&&(i=[i]);let o=i[0]==="object"?"an":"a";P(i.some(s=>rr(n)===s),`"${r}" must be ${o} ${Kc(i,"or")}.`)}},Tf=(t,e)=>{switch(Sf(t),e=e||t.type,P(e,"You must pass an event type."),e){case"track":return yf(t);case"group":return gf(t);case"identify":return wf(t);case"page":return Ef(t);case"screen":return bf(t);case"alias":return Af(t);default:return P(0,`Invalid event type: "${e}"`)}},or="3.0.6",_f=t=>t&&t.endsWith("/")?t.replace(/\/+$/,""):t,xf=t=>typeof t=="function"&&!!(t.constructor&&t.call&&t.apply),dt=Sn.nextTick.bind(Sn),Xn=()=>{},Ot=class{constructor(e,r,n){if(n=n||{},!e)throw new Error("You must pass your project's write key.");if(!r)throw new Error("You must pass our data plane url.");this.queue=[],this.writeKey=e,this.host=_f(r),this.timeout=n.timeout||void 0,this.flushAt=n.flushAt?Math.max(n.flushAt,1):20,this.flushInterval=n.flushInterval||2e4,this.maxInternalQueueSize=n.maxInternalQueueSize||2e4,this.logLevel=n.logLevel||"info",this.flushOverride=n.flushOverride&&xf(n.flushOverride)?n.flushOverride:void 0,this.flushed=!1,this.axiosInstance=N.create({adapter:$c}),Object.defineProperty(this,"enable",{configurable:!1,writable:!1,enumerable:!0,value:typeof n.enable=="boolean"?n.enable:!0}),this.logger={error(i,...o){this.logLevel!=="off"&&console.error(`${new Date().toISOString()} ["Rudder"] error: ${i}`,...o)},info(i,...o){["silly","debug","info"].includes(this.logLevel)&&console.log(`${new Date().toISOString()} ["Rudder"] info: ${i}`,...o)},debug(i,...o){["silly","debug"].includes(this.logLevel)&&console.debug(`${new Date().toISOString()} ["Rudder"] debug: ${i}`,...o)},silly(i,...o){["silly"].includes(this.logLevel)&&console.info(`${new Date().toISOString()} ["Rudder"] silly: ${i}`,...o)}},ue(this.axiosInstance,{retries:0})}_validate(e,r){try{Tf(e,r)}catch(n){if(n.message==="Your message must be < 32kb."){this.logger.info("Your message must be < 32kb. This is currently surfaced as a warning. Please update your code",e);return}throw n}}identify(e,r){return this._validate(e,"identify"),this.enqueue("identify",e,r),this}group(e,r){return this._validate(e,"group"),this.enqueue("group",e,r),this}track(e,r){return this._validate(e,"track"),this.enqueue("track",e,r),this}page(e,r){return this._validate(e,"page"),this.enqueue("page",e,r),this}screen(e,r){return this._validate(e,"screen"),this.enqueue("screen",e,r),this}alias(e,r){return this._validate(e,"alias"),this.enqueue("alias",e,r),this}enqueue(e,r,n){if(this.queue.length>=this.maxInternalQueueSize){this.logger.error(`not adding events for processing as queue size ${this.queue.length} >= than max configuration ${this.maxInternalQueueSize}`);return}let i=jc(r);if(n=n||Xn,!this.enable)return dt(n);if(e==="identify"&&i.traits&&(i.context||(i.context={}),i.context.traits=i.traits),i={...i},i.type=e,i.context={library:{name:"analytics-service-worker",version:or},...i.context},i.channel="service-worker",i._metadata={serviceWorkerVersion:or,...i._metadata},i.originalTimestamp||(i.originalTimestamp=new Date),i.messageId||(i.messageId=Ic()),i.anonymousId&&!Ln(i.anonymousId)&&(i.anonymousId=JSON.stringify(i.anonymousId)),i.userId&&!Ln(i.userId)&&(i.userId=JSON.stringify(i.userId)),this.queue.push({message:i,callback:n}),!this.flushed){this.flushed=!0,this.flush();return}this.queue.length>=this.flushAt&&(this.logger.debug("flushAt reached, trying flush..."),this.flush()),this.flushInterval&&!this.flushTimer&&(this.logger.debug("no existing flush timer, creating new one"),this.flushTimer=setTimeout(this.flush.bind(this),this.flushInterval))}flush(e){if(this.logger.debug("in flush"),e=e||Xn,!this.enable)return dt(e);if(this.timer&&(this.logger.debug("cancelling existing timer..."),clearTimeout(this.timer),this.timer=null),this.flushTimer&&(this.logger.debug("cancelling existing flushTimer..."),clearTimeout(this.flushTimer),this.flushTimer=null),this.queue.length===0)return this.logger.debug("queue is empty, nothing to flush"),dt(e);let r=this.queue.splice(0,this.flushAt),n=r.map(p=>p.callback),o={batch:r.map(p=>(typeof p.message=="object"&&(p.message.sentAt=new Date),p.message)),sentAt:new Date};this.logger.debug(`batch size is ${r.length}`),this.logger.silly("===data===",o);let s=p=>{n.forEach(y=>{y&&y(p)}),e&&e(p,o)},u={};typeof self>"u"&&(u["user-agent"]=`analytics-service-worker/${or}`,u["Content-Type"]="application/json");let l=typeof this.timeout=="string"?parseInt(Oc(this.timeout),10):this.timeout;if(o.batch.length===0)return this.logger.debug("batch is empty, nothing to flush"),dt(e);if(this.flushOverride)this.flushOverride({host:`${this.host}`,writeKey:this.writeKey,data:o,headers:u,reqTimeout:l,flush:this.flush.bind(this),done:s,isErrorRetryable:this._isErrorRetryable.bind(this)});else{let p={method:"POST",url:`${this.host}`,auth:{username:this.writeKey},data:o,headers:u};l&&(p.timeout=l),this.axiosInstance({...p,"axios-retry":{retries:3,retryCondition:this._isErrorRetryable.bind(this),retryDelay:ue.exponentialDelay}}).then(y=>{this.timer=setTimeout(this.flush.bind(this),this.flushInterval),s()}).catch(y=>{if(this.logger.error(y),this.logger.error(`got error while attempting send for 3 times, dropping ${r.length} events`),this.timer=setTimeout(this.flush.bind(this),this.flushInterval),y.response){let h=new Error(y.response.statusText);return s(h)}s(y)})}}_isErrorRetryable(e){return ue.isNetworkError(e)?!0:e.response?(this.logger.error(`error status: ${e.response.status}`),e.response.status>=500&&e.response.status<=599||e.response.status===429):!1}};var yo=Kt(Ys());G();K();var vr=Kt(co(),1),zf=vr.default.configure;var Ur=vr.default;G();K();var Nf=60*10*1e3,de=new Ne,fo=async()=>de.get("userIdCache")||"",lo=async()=>de.get("userPropsCache")||{},Br=async()=>{let t=await de.get("deviceId");return t||jt(pn())},ho=async()=>{let t=await de.get("rudderstackIdentifyData"),e=t?.timestamp??null;if(!e)return null;let r=Date.now();return e+Nf<r?null:t?.value},po=async t=>(await de.set("userIdCache",t),t),jr=async t=>(await de.set("userPropsCache",t),t),jt=async t=>(await de.set("deviceId",t),t),mo=async t=>(await de.set("rudderstackIdentifyData",{timestamp:Date.now(),value:t}),t);var go=new Ne,vf=(t,e=!0)=>ct.isMultivariateFeatureEnabled(t,e),Uf=dn(vf,{forceRefresh:ln}),dl=t=>Uf(t);var $r=class{constructor(e){this.rudderstackAnalyticsClient=e}#r=null;#n=null;#e={appVersion:hn,displayLanguage:mn.language};#t=null;async rudderstackAnalyticsClientIdentify(e){let r=await ho();(!r||Ur(r)!==Ur(e))&&(await mo(e),this.rudderstackAnalyticsClient.identify(e))}async getFeatureFlags(){return ct.getFeatureFlags()}async addUserProperties(e={}){if(await this.getAnalyticsOptedOutCached())return;let[r,n]=await Promise.all([this.getUserIdCached(),this.getDeviceIdCached()]);ct.setSubjectAttributes(e),this.#e={...this.#e,...e},await jr(this.#e);let i={userId:r,anonymousId:n,context:{traits:this.#e,browser:ft()}};await this.rudderstackAnalyticsClientIdentify(i)}async capture(e,r){if(await this.getAnalyticsOptedOutCached())return;let[n,i,o]=await Promise.all([this.getUserIdCached(),this.getTraitsCached(),this.getDeviceIdCached()]);this.rudderstackAnalyticsClient.track({userId:n,anonymousId:o,event:e,context:{traits:i,browser:ft()},properties:En(r)})}async identify(e,r){if(await this.getAnalyticsOptedOutCached())return;this.#n=e,this.#e={...await this.getTraitsCached(),...r},this.#t=await this.getDeviceIdCached(),await Promise.all([po(this.#n),jr(this.#e),jt(this.#t)]);let n={userId:this.#n,anonymousId:this.#t,context:{traits:this.#e,browser:ft()}};await this.rudderstackAnalyticsClientIdentify(n)}async getAnalyticsOptedOutCached(){return this.#r===null&&(this.#r=await gn(go)),this.#r}async getDeviceId(){return Br()}async getUserIdCached(){return this.#n||await fo()||""}async getTraitsCached(){return{...await lo(),...this.#e}}async getDeviceIdCached(){return this.#t||await Br()}setDeviceId(e){jt(e),this.#t=e}async setAnalyticsOptedOut(e){this.#r=e;let[r,n,i]=await Promise.all([this.getUserIdCached(),this.getTraitsCached(),this.getDeviceIdCached()]),o=e?"analyticsOptedOutByUser":"analyticsOptedInByUser";this.rudderstackAnalyticsClient.track({userId:r,anonymousId:i,event:o,context:{traits:n}})}async setDisplayLanguage(e){this.#e.displayLanguage=e}},Bf=(t,e)=>{let r=new Ot(t,`${e}/v1/batch`);try{return new $r(r)}catch{return console.error("Failed to initialize rudderstack."),An()}},wo=Wt({storage:go,rudderStackKey:wn,createAnalytics:Bf}),ml=({children:t})=>yo.default.createElement(yn,{analytics:wo},t),yl={...bn(wo)};export{Ne as a,dl as b,wo as c,ml as d,yl as e};
/*! Bundled license information:

@rudderstack/analytics-js-service-worker/dist/npm/esm/index.js:
  (*!
   * The buffer module from node.js, for the browser.
   *
   * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
   * @license  MIT
   *)
*/
//# sourceMappingURL=chunk-MHOQBMVI.js.map
