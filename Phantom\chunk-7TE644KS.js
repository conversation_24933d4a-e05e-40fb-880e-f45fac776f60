import{a as m}from"./chunk-7UTGLKC7.js";import{d as c}from"./chunk-QINBGLLG.js";import{a as d,c as l}from"./chunk-2NGYUYTC.js";import{o as a,pa as s,rb as r}from"./chunk-WIQ4WVKX.js";import{a as b}from"./chunk-7X4NV6OJ.js";import{f as u,h as n,n as o}from"./chunk-3KENBVE7.js";n();o();var t=u(b());var W=e=>{let{data:g}=c(e.keybaseUsername),f=e.name??e.keybaseUsername??e.identifier;return t.default.createElement(x,null,t.default.createElement(C,null,t.default.createElement(v,{width:28,iconUrl:e.iconUrl??g}),t.default.createElement(r,{textAlign:"left",weight:600,size:16,noWrap:!0},f),e.website&&t.default.createElement(k,{href:e.website},t.default.createElement(s,null)),e.onInfoClick&&t.default.createElement("div",{className:p.infoContainer,onClick:e.onInfoClick},t.default.createElement(l.Info,{size:16,color:"textTertiary"}))),t.default.createElement(y,null,e.data.map(i=>t.default.createElement(V,{key:i.label},t.default.createElement(r,{textAlign:"left",color:"#777777",size:14,lineHeight:17,noWrap:!0},i.label),i.value))))},p={infoContainer:d({cursor:"pointer"})},x=a.div`
  display: grid;
  grid-template-columns: 100%;
  grid-template-rows: 47px auto;
  align-items: center;
  justify-content: flex-start;
  background: #2a2a2a;
  width: 100%;
  border-radius: 6px;
  margin-top: 12px;
`,C=a.div`
  display: grid;
  gap: 10px;
  grid-template-columns: 33px 1fr auto;
  align-items: center;
  padding: 9px 14px;
  border-bottom: 0.5px solid #474747;
`,k=a.a.attrs({target:"_blank",rel:"noopener noreferrer"})`
  margin-left: auto;
  background: none;
  border: none;
  cursor: pointer;
  padding: 6px;
`,v=a(m)``,y=a.section`
  padding: 14px;
`,V=a.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 36px;
`;export{W as a};
//# sourceMappingURL=chunk-7TE644KS.js.map
