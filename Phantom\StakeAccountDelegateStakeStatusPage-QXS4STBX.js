import{a as M}from"./chunk-R3J4EMVA.js";import{a as f}from"./chunk-43DCCALR.js";import{a as K,b as B,c as F}from"./chunk-O5AAGNHJ.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{b as H,f as _}from"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-W27Z2YZM.js";import"./chunk-H3FFS4GT.js";import{a as z}from"./chunk-4VDZJDFB.js";import"./chunk-GMBAJ6CC.js";import{b as A}from"./chunk-PTZMRZUV.js";import"./chunk-OKP6DFCI.js";import{rb as m}from"./chunk-WIQ4WVKX.js";import{r as O}from"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{M as N,N as v,i as I,j as x}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as E,P as l,Pa as w}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as L}from"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as W}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as J,h as b,n as P}from"./chunk-3KENBVE7.js";b();P();var e=J(W());var X=n=>{let i={stakePubkey:n.stakeAccountPubkey,votePubkey:n.voteAccountPubkey,onClose:n.onClose},{t}=L(),{delegateStake:s,onDeny:a,needsLedgerApproval:u,txHash:r,confirmationStatus:c,isError:k,error:d}=Z(i);if(u)return e.default.createElement(B,{ledgerAction:s,cancel:a});if(k){let o=d,S=o?.message,g=K(o),D=o?.message.includes(x),h=o?.message.includes(I);return g?e.default.createElement(F,{ledgerActionError:o,onRetryClick:s,onCancelClick:a}):D||h?e.default.createElement(M,{onCancelClick:a}):e.default.createElement(A,{icon:"error",title:t("stakeAccountDelegateStakeStakingFailed"),onClose:a,showButton:!!r,iconSize:"large"},e.default.createElement(m,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},S??t("stakeAccountDelegateStakeStakingFailedDescription")),e.default.createElement(f,{txHash:r},t("stakeAccountDelegateStakeViewTransaction")))}return c==="confirmed"||c==="finalized"?e.default.createElement(A,{icon:"success",title:t("stakeAccountDelegateStakeSolStaked"),onClose:n.onClose,iconSize:"large"},e.default.createElement(m,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},e.default.createElement(z,{i18nKey:"stakeAccountDelegateStakeSolStakedDescriptionInterpolated"},"Your SOL will begin earning rewards",e.default.createElement("br",null)," in the next couple days once the stake account becomes active.")),e.default.createElement(f,{txHash:r},t("stakeAccountDelegateStakeViewTransaction"))):e.default.createElement(A,{icon:"loading",title:t("stakeAccountDelegateStakeStakingSol"),onClose:n.onClose,iconSize:"large"},e.default.createElement(m,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},t("stakeAccountDelegateStakeStakingSolDescription")),e.default.createElement(f,{txHash:r},t("stakeAccountDelegateStakeViewTransaction")))},fe=X,Z=({onClose:n,...i})=>{let{data:t,isSuccess:s}=E(),{accountIdentifier:a,isLedgerAccount:u,solanaChainAddress:r,solanaPublicKey:c,connection:k,networkID:d}=(0,e.useMemo)(()=>{let U=t?.identifier??"",y=(t?.addresses??[]).find(G=>w.isSolanaNetworkID(G.networkID)),Y=new l.PublicKey(y?.address??""),j=t?.type==="ledger",T=y?.networkID,q=N(v(T));return{accountIdentifier:U,isLedgerAccount:j,solanaChainAddress:y,solanaPublicKey:Y,connection:q,networkID:T}},[t]),o=H(k),S=(0,e.useCallback)(()=>{o.mutate({...i,accountIdentifier:a,senderAddress:r,stakePubkey:new l.PublicKey(i.stakePubkey),authorizedPubkey:c,votePubkey:new l.PublicKey(i.votePubkey)})},[a,i,o,r,c]),g=O(),D=(0,e.useCallback)(()=>{g.denied({chainType:"solana",chainName:"solana",networkId:w.getSolanaNetworkIDValue(d),type:"delegate"}),n()},[d,n,g]);(0,e.useEffect)(()=>{s&&!u&&S()},[s]);let C=o.data?.id,p=_(k,2e3,C),V=u&&(o.isIdle||o.isPending),Q=p.data?.value?.confirmationStatus;return{delegateStake:S,onDeny:D,needsLedgerApproval:V,txHash:C,confirmationStatus:Q,isError:o.isError||p.isError,error:o.error||p.error}};export{X as StakeAccountDelegateStakeStatusPage,fe as default};
//# sourceMappingURL=StakeAccountDelegateStakeStatusPage-QXS4STBX.js.map
