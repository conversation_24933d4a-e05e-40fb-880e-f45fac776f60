import { Page, BrowserContext } from '@playwright/test';
import { ENV } from './tests/base/env';

/**
 * Helper class for global setup
 * Provides simplified versions of methods from DashboardPage without using test.step()
 */
export class GlobalSetupHelper {
  constructor(private page: Page) {}

  /**
   * Import ví Phantom bằng private key
   */
  async importWallet(): Promise<void> {
    try {
      console.log("Bắt đầu import ví...");
      const walletName = "Ví Test";
      const password = "12345678";

      // Kiểm tra URL hiện tại
      const currentUrl = this.page.url();
      console.log("URL hiện tại:", currentUrl);

      // Kiểm tra xem đang ở trang Phantom không
      if (!currentUrl.startsWith('chrome-extension://')) {
        console.log("Không phải trang Phantom Extension. Bỏ qua bước import.");
        return;
      }

      // Đợi trang load hoàn toàn
      await this.page.waitForLoadState('domcontentloaded');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Kiểm tra xem đã đăng nhập chưa bằng cách tìm kiếm các phần tử chỉ xuất hiện sau khi đăng nhập
      const isLoggedIn = await this.page.locator('text=Assets').isVisible({ timeout: 5000 }).catch(() => false);

      if (isLoggedIn) {
        console.log("Ví đã được import trước đó. Bỏ qua bước import.");
        return;
      }

      console.log("Tiến hành import ví mới...");

      // Sử dụng các selector giống như trong DashboardSelectors
      const importWalletBtn = "//button[text()='I already have a wallet']";
      const importPrivateKeyBtn = "//button//div[text()='Import Private Key']";
      const walletNameInput = "//input[@placeholder='Name']";
      const privateKeyInput = "//textarea[@placeholder='Private key']";
      const importBtn = "//button[text()='Import']";
      const passwordInput = "//input[@name='password']";
      const confirmPasswordInput = "//input[@name='confirmPassword']";
      const agreeCheckbox = "//input[@data-testid='onboarding-form-terms-of-service-checkbox']";
      const continueBtn = "//button[@data-testid='onboarding-form-submit-button']";
      const getStartedBtn = "//button[text()='Get Started']";

      // Chụp màn hình để debug
      console.log("Chụp màn hình trang Phantom trước khi import...");
      await this.page.screenshot({ path: 'screenshots/phantom-before-import.png' });

      // Kiểm tra nút import wallet có hiển thị không
      const importBtnVisible = await this.page.locator(importWalletBtn).isVisible({ timeout: 5000 }).catch(() => false);
      if (!importBtnVisible) {
        console.log(`Nút import wallet không hiển thị. Thử các selector khác...`);

        // Thử các selector khác
        const alternativeSelectors = [
          "//button[contains(text(), 'already have')]",
          "//button[contains(text(), 'Import')]",
          "//button[contains(text(), 'import')]",
          "//button[contains(text(), 'wallet')]",
          "button:has-text('I already have a wallet')",
          "button:has-text('Import')"
        ];

        let found = false;
        for (const selector of alternativeSelectors) {
          const isVisible = await this.page.locator(selector).isVisible({ timeout: 3000 }).catch(() => false);
          if (isVisible) {
            console.log(`Tìm thấy nút import với selector: ${selector}`);
            await this.page.locator(selector).click();
            found = true;
            break;
          }
        }

        if (!found) {
          console.log("Không tìm thấy nút import. Bỏ qua bước import.");
          return;
        }
      } else {
        // Click vào nút import wallet
        console.log(`Clicking import wallet button with selector: ${importWalletBtn}`);
        await this.page.locator(importWalletBtn).click();
      }

      // Đợi trang tiếp theo load
      await this.page.waitForLoadState('domcontentloaded');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Click vào nút Import Private Key
      console.log(`Clicking import private key button with selector: ${importPrivateKeyBtn}`);
      await this.page.locator(importPrivateKeyBtn).click();

      // Điền thông tin ví
      console.log(`Filling wallet name: ${walletName}`);
      await this.page.locator(walletNameInput).fill(walletName);

      console.log(`Filling private key`);
      await this.page.locator(privateKeyInput).fill(ENV.PRIVATE_KEY_PHANTOM);

      console.log(`Clicking import button`);
      await this.page.locator(importBtn).click();

      // Đợi trang mật khẩu load
      await this.page.waitForLoadState('domcontentloaded');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Điền mật khẩu
      console.log(`Filling password`);
      await this.page.locator(passwordInput).fill(password);
      await this.page.locator(confirmPasswordInput).fill(password);

      console.log(`Clicking agree checkbox`);
      await this.page.locator(agreeCheckbox).click();

      console.log(`Clicking continue button`);
      await this.page.locator(continueBtn).click();

      // Đợi trang cuối cùng load
      await this.page.waitForLoadState('domcontentloaded');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Hoàn tất
      console.log(`Clicking get started button`);
      await this.page.locator(getStartedBtn).click();
      console.log("Ví được import thành công.");
    } catch (error) {
      console.error("Lỗi khi import ví:", error instanceof Error ? error.message : "Lỗi không xác định");
      throw new Error(`Không thể import ví: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
    }
  }

  /**
   * Đăng nhập vào Dex3
   */
  async loginDex3(): Promise<void> {
    try {
      console.log("Bắt đầu đăng nhập vào Dex3...");

      // Kiểm tra URL hiện tại
      const currentUrl = this.page.url();
      console.log("URL hiện tại:", currentUrl);

      // Nếu chưa ở trang Dex3, điều hướng đến đó
      if (!currentUrl.includes(ENV.BASE_URL)) {
        console.log(`Điều hướng đến trang Dex3: ${ENV.BASE_URL}`);
        await this.page.goto(ENV.BASE_URL);
      }

      await this.page.waitForLoadState('domcontentloaded');
      await new Promise(resolve => setTimeout(resolve, 3000));

      // Chụp màn hình trang Dex3 trước khi đăng nhập
      await this.page.screenshot({ path: 'screenshots/dex3-before-login.png' });

      // Khởi tạo quá trình đăng nhập với Phantom
      console.log("Click vào icon Phantom để đăng nhập...");

      // Sử dụng selector chính xác cho nút Phantom
      const phantomBtn = "//p[text()='Phantom']";

      try {
        console.log(`Tìm kiếm nút Phantom với selector: ${phantomBtn}`);
        await this.page.waitForSelector(phantomBtn, { timeout: 10000 });
        console.log(`Đã tìm thấy nút Phantom, tiến hành click...`);
        await this.page.locator(phantomBtn).click();
        console.log(`Đã click vào nút Phantom thành công`);
      } catch (error) {
        console.log("Không tìm thấy nút Phantom. Chụp màn hình để debug...");
        await this.page.screenshot({ path: 'screenshots/phantom-button-screen.png' });

        // Thử kiểm tra xem đã đăng nhập chưa
        try {
          await this.verifyLoginState();
          console.log("Có vẻ đã đăng nhập rồi, bỏ qua bước kết nối ví.");
          return;
        } catch (e) {
          // Nếu chưa đăng nhập, báo lỗi
          throw new Error(`Không tìm thấy nút Phantom với selector: ${phantomBtn}`);
        }
      }

      // Xử lý các popup xác nhận
      await this.handleNotificationPopups();

      // Xác minh trạng thái đăng nhập
      await this.verifyLoginState();
      console.log("Đăng nhập thành công");
    } catch (error) {
      console.error(`Đăng nhập thất bại: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      throw error;
    }
  }

  /**
   * Xác minh trạng thái đăng nhập
   */
  async verifyLoginState(): Promise<void> {
    try {
      console.log("Đang xác minh trạng thái đăng nhập...");
      // Tăng timeout để đảm bảo độ tin cậy
      await this.page.waitForSelector("//button[contains(., 'SOL')]", { timeout: 30000 });
      console.log("Đã tìm thấy menu button, đăng nhập thành công!");
    } catch (error) {
      console.error("Xác minh đăng nhập thất bại:", error instanceof Error ? error.message : "Lỗi không xác định");
      throw new Error(`Không thể xác minh trạng thái đăng nhập: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
    }
  }

  /**
   * Xử lý các popup thông báo từ ví Phantom
   */
  async handleNotificationPopups(timeout = 30000): Promise<void> {
    console.log("Bắt đầu xử lý các popup từ Phantom...");
    const context = this.page.context();

    try {
      // Xử lý Connect popup
      await this.handleConnectPopup(context, timeout);

      // Xử lý Confirm popup
      await this.handleConfirmPopup(context, timeout);
    } catch (error) {
      console.warn(`Gặp vấn đề khi xử lý popup: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);

      // Kiểm tra xem có đăng nhập thành công không dù gặp lỗi
      try {
        await this.verifyLoginState();
        console.log("Đăng nhập thành công mặc dù xử lý popup có vấn đề");
        return;
      } catch (e) {
        console.error("Xác minh đăng nhập thất bại:", e instanceof Error ? e.message : "Lỗi không xác định");
        throw new Error(`Xử lý popup thất bại: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      }
    }
  }

  /**
   * Xử lý popup Connect từ Phantom
   */
  private async handleConnectPopup(context: BrowserContext, timeout: number): Promise<void> {
    console.log("Đang xử lý popup Connect...");

    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        // Tìm tất cả các notification page
        const pages = context.pages();
        const notificationPages = pages.filter(page =>
          !page.isClosed() && page.url().includes("notification")
        );

        if (notificationPages.length === 0) {
          await new Promise(resolve => setTimeout(resolve, 500));
          continue;
        }

        // Xử lý trang đầu tiên tìm thấy
        const page = notificationPages[0];

        // Đưa trang lên phía trước
        await page.bringToFront();

        // Sử dụng selector đã biết chính xác
        const connectButton = page.locator('button[type="submit"][data-testid="primary-button"]');

        try {
          // Kiểm tra nút có hiển thị không
          const isVisible = await connectButton.isVisible({ timeout: 3000 }).catch(() => false);

          if (isVisible) {
            console.log(`Tìm thấy nút Connect`);
            await connectButton.click();
            console.log("Đã click nút Connect thành công");
            await new Promise(resolve => setTimeout(resolve, 1000));
            return;
          }
        } catch (e: any) {
          // Nếu trang đã đóng, coi như thành công
          if (e.toString().includes("has been closed")) {
            console.log("Trang popup đã tự đóng, coi như đã xử lý thành công");
            return;
          }
        }
      } catch (error) {
        // Bỏ qua lỗi và tiếp tục thử
        console.log("Lỗi khi xử lý Connect popup, thử lại...");
      }
    }

    throw new Error(`Không thể xử lý Connect popup trong ${timeout}ms`);
  }

  /**
   * Xử lý popup Confirm từ Phantom
   */
  private async handleConfirmPopup(context: BrowserContext, timeout: number): Promise<void> {
    console.log("Đang xử lý popup Confirm...");

    // Đợi một khoảng thời gian ngắn để đảm bảo popup Connect đã được xử lý hoàn toàn
    console.log("Đợi 2 giây để đảm bảo popup Confirm có thời gian xuất hiện...");
    await new Promise(resolve => setTimeout(resolve, 2000));

    try {
      // Kiểm tra xem có trang notification mở sẵn không
      const existingPages = context.pages();
      const notificationPages = existingPages.filter(page =>
        !page.isClosed() && page.url().includes("notification")
      );

      let confirmPage: Page | null = null;

      if (notificationPages.length > 0) {
        // Sử dụng trang notification đã mở
        confirmPage = notificationPages[0];
        console.log("Sử dụng trang notification đã mở:", confirmPage.url());
      } else {
        // Đợi trang notification mới xuất hiện
        console.log("Không tìm thấy trang notification hiện có, đợi trang mới...");

        // Đăng ký lắng nghe sự kiện trang mới
        const confirmPagePromise = new Promise<Page>((resolve, reject) => {
          const timeoutId = setTimeout(() => {
            reject(new Error(`Không tìm thấy Confirm popup trong ${timeout}ms`));
          }, timeout);

          const onPageHandler = (page: Page) => {
            if (page.url().includes("notification")) {
              clearTimeout(timeoutId);
              resolve(page);
            }
          };

          context.on('page', onPageHandler);
        });

        try {
          confirmPage = await confirmPagePromise;
          console.log("Đã tìm thấy trang notification mới:", confirmPage.url());
        } catch (error) {
          console.log("Không tìm thấy trang notification mới:", error);
          // Thử kiểm tra đăng nhập ngay cả khi không tìm thấy popup
          await this.verifyLoginState().catch(() => {
            console.log("Kiểm tra đăng nhập không thành công, nhưng vẫn tiếp tục");
          });
          return;
        }
      }

      // Đảm bảo confirmPage không null
      if (!confirmPage) {
        console.log("Không tìm thấy trang Confirm popup, kiểm tra đăng nhập...");
        await this.verifyLoginState().catch(() => {
          console.log("Kiểm tra đăng nhập không thành công, nhưng vẫn tiếp tục");
        });
        return;
      }

      // Đưa trang lên phía trước
      try {
        if (!confirmPage.isClosed()) {
          await confirmPage.bringToFront();
        } else {
          console.log("Trang Confirm đã đóng");
          return;
        }
      } catch (e) {
        console.log("Lỗi khi đưa trang Confirm popup lên phía trước:", e instanceof Error ? e.message : "Lỗi không xác định");
      }

      // Sử dụng selector button:nth-child(2)
      try {
        if (confirmPage.isClosed()) {
          console.log("Trang Confirm đã đóng");
          return;
        }

        console.log(`Kiểm tra nút Confirm với selector: button:nth-child(2)`);
        const isVisible = await confirmPage.locator('button:nth-child(2)').isVisible({ timeout: 3000 }).catch(() => false);

        if (isVisible) {
          console.log(`Tìm thấy nút Confirm`);
          await confirmPage.locator('button:nth-child(2)').click();
          console.log("Đã click nút Confirm thành công");

          // Đợi trang đóng
          try {
            await confirmPage.waitForEvent('close', { timeout: 5000 }).catch(() => {
              console.log("Timeout khi chờ trang đóng, nhưng vẫn tiếp tục");
            });
          } catch (e) {
            // Bỏ qua lỗi
          }
        } else {
          console.log(`Không tìm thấy nút Confirm`);
        }
      } catch (e) {
        console.log(`Lỗi khi thao tác với nút Confirm:`, e instanceof Error ? e.message : "Lỗi không xác định");
      }

      // Kiểm tra đăng nhập bất kể tìm thấy nút hay không
      await this.verifyLoginState().catch(() => {
        console.log("Kiểm tra đăng nhập không thành công, nhưng vẫn tiếp tục");
      });

    } catch (error) {
      console.log("Lỗi khi xử lý Confirm popup:", error instanceof Error ? error.message : "Lỗi không xác định");
      // Kiểm tra đăng nhập dù có lỗi
      try {
        await this.verifyLoginState();
        console.log("Đăng nhập đã thành công dù xử lý popup Confirm có vấn đề");
        return;
      } catch (loginError) {
        console.log("Không thể xác minh đăng nhập sau lỗi xử lý popup");
      }
    }
  }
}
