import{a as F}from"./chunk-R3J4EMVA.js";import{a as f}from"./chunk-43DCCALR.js";import{a as z,b as v,c as B}from"./chunk-O5AAGNHJ.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{f as H,k as _}from"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-W27Z2YZM.js";import"./chunk-H3FFS4GT.js";import{b as m}from"./chunk-PTZMRZUV.js";import"./chunk-OKP6DFCI.js";import{rb as h}from"./chunk-WIQ4WVKX.js";import{r as O}from"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{M as x,N,i as I,j as P}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as L,P as w,Pa as y}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as b}from"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as J}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as G,h as D,n as E}from"./chunk-3KENBVE7.js";D();E();var t=G(J());var X=r=>{let s={stakePubkey:r.stakeAccountPubkey,amount:r.amount,usdPerSol:r.usdPerSol,onClose:r.onClose},{t:e}=b(),{withdrawStake:c,needsLedgerApproval:d,txHash:n,confirmationStatus:i,isError:u,error:l,onDeny:a}=Y(s);if(d)return t.default.createElement(v,{ledgerAction:c,cancel:a});if(u){let o=l,k=o?.message,S=z(o),A=o?.message.includes(P),C=o?.message.includes(I);return S?t.default.createElement(B,{ledgerActionError:o,onRetryClick:c,onCancelClick:a}):A||C?t.default.createElement(F,{onCancelClick:a}):t.default.createElement(m,{icon:"error",title:e("stakeAccountWithdrawStakeWithdrawalFailed"),onClose:a,showButton:!!n},t.default.createElement(h,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},k??e("stakeAccountWithdrawStakeWithdrawalFailedDescription")),t.default.createElement(f,{txHash:n},e("stakeAccountWithdrawStakeViewTransaction")))}return i==="confirmed"||i==="finalized"?t.default.createElement(m,{icon:"success",title:e("stakeAccountWithdrawStakeSolWithdrawn"),onClose:r.onClose,iconSize:"large"},t.default.createElement(h,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},e("stakeAccountWithdrawStakeSolWithdrawnDescription.part1"),t.default.createElement("br",null)," ",e("stakeAccountWithdrawStakeSolWithdrawnDescription.part2")),t.default.createElement(f,{txHash:n},e("stakeAccountWithdrawStakeViewTransaction"))):t.default.createElement(m,{icon:"loading",title:e("stakeAccountWithdrawStakeWithdrawingSol"),onClose:r.onClose,iconSize:"large"},t.default.createElement(h,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},e("stakeAccountWithdrawStakeWithdrawingSolDescription")),t.default.createElement(f,{txHash:n},e("stakeAccountWithdrawStakeViewTransaction")))},ht=X,Y=({onClose:r,...s})=>{let{data:e,isSuccess:c}=L(),{accountIdentifier:d,isLedgerAccount:n,solanaChainAddress:i,solanaPublicKey:u,connection:l,networkID:a}=(0,t.useMemo)(()=>{let V=e?.identifier??"",p=(e?.addresses??[]).find(q=>y.isSolanaNetworkID(q.networkID)),Q=new w.PublicKey(p?.address??""),U=e?.type==="ledger",T=p?.networkID,j=x(N(T));return{accountIdentifier:V,isLedgerAccount:U,solanaChainAddress:p,solanaPublicKey:Q,connection:j,networkID:T}},[e]),o=_(l),k=(0,t.useCallback)(()=>{o.mutate({...s,accountIdentifier:d,senderAddress:i,stakePubkey:new w.PublicKey(s.stakePubkey),authorizedPubkey:u})},[d,s,i,u,o]),S=O(),A=(0,t.useCallback)(()=>{S.denied({chainType:"solana",chainName:"solana",networkId:y.getSolanaNetworkIDValue(a),type:"withdraw"}),r()},[a,r,S]);(0,t.useEffect)(()=>{if(c){if(!e||!i)throw new Error("Selected account undefined when trying to perform stake transaction.");n||k()}},[c]);let W=o.data?.id,g=H(l,2e3,W),K=n&&(o.isIdle||o.isPending),M=g.data?.value?.confirmationStatus;return{withdrawStake:k,onDeny:A,needsLedgerApproval:K,txHash:W,confirmationStatus:M,isError:o.isError||g.isError,error:o.error||g.error}};export{X as StakeAccountWithdrawStakeStatusPage,ht as default};
//# sourceMappingURL=StakeAccountWithdrawStakeStatusPage-LVUST3RO.js.map
