{"commandAdd": "추가", "commandAccept": "수락", "commandApply": "적용", "commandApprove": "승인", "commandAllow": "허용", "commandBack": "뒤로", "commandBuy": "구입", "commandCancel": "취소", "commandClaim": "클레임", "commandClaimReward": "내 리워드 클레임", "commandClear": "지우기", "commandClose": "닫기", "commandConfirm": "확인", "commandConnect": "연결", "commandContinue": "계속", "commandConvert": "전환", "commandCopy": "복사", "commandCopyAddress": "주소 복사", "commandCopyTokenAddress": "토큰 주소 복사", "commandCreate": "생성", "commandCreateTicket": "티켓 생성", "commandDeny": "거부", "commandDismiss": "닫기", "commandDontAllow": "허용 안 함", "commandDownload": "다운로드", "commandEdit": "편집", "commandEditProfile": "프로필 편집", "commandEnableNow": "지금 활성화", "commandFilter": "필터", "commandFollow": "팔로우", "commandHelp": "도움말", "commandLearnMore": "자세히 알아보기", "commandLearnMore2": "자세히 알아보기", "commandMint": "민팅", "commandMore": "자세히", "commandNext": "다음", "commandNotNow": "나중에", "commandOpen": "열기", "commandOpenSettings": "설정 열기", "commandPaste": "붙여넣기", "commandReceive": "받기", "commandReconnect": "다시 연결", "commandRecordVideo": "동영상 녹화", "commandRequest": "요청", "commandRetry": "다시 시도", "commandReview": "검토", "commandRevoke": "취소", "commandSave": "저장", "commandScanQRCode": "QR 코드 스캔", "commandSelect": "선택", "commandSelectMedia": "미디어 선택", "commandSell": "판매", "commandSend": "보내기", "commandShare": "공유", "commandShowBalance": "잔액 표시", "commandSign": "서명", "commandSignOut": "Sign Out", "commandStake": "스테이킹", "commandMintLST": "JitoSOL 민팅", "commandSwap": "스왑", "commandSwapAgain": "다시 스왑", "commandTakePhoto": "사진 촬영", "commandTryAgain": "다시 시도", "commandViewTransaction": "거래 보기", "commandReportAsNotSpam": "스팸 아님으로 신고", "commandReportAsSpam": "스팸으로 신고", "commandPin": "고정", "commandBlock": "차단", "commandUnblock": "차단 해제", "commandUnstake": "스테이킹 해제", "commandUnpin": "고정 취소", "commandHide": "숨기기", "commandUnhide": "숨기기 취소", "commandBurn": "소각", "commandReport": "신고", "commandView": "보기", "commandProceedAnywayUnsafe": "계속 진행(위험)", "commandUnfollow": "언팔로우", "commandUnwrap": "래핑 해제", "commandConfirmUnsafe": "확인(위험)", "commandYesConfirmUnsafe": "예, 확인(위험)", "commandConfirmAnyway": "그래도 확인", "commandReportIssue": "문제 신고", "commandSearch": "검색", "commandShowMore": "자세히 표시", "commandShowLess": "간단히 표시", "pastParticipleClaimed": "클레임함", "pastParticipleCompleted": "완료함", "pastParticipleCopied": "복사됨", "pastParticipleDone": "완료", "pastParticipleDisabled": "비활성화됨", "pastParticipleRequested": "요청됨", "nounName": "이름", "nounNetwork": "네트워크", "nounNetworkFee": "네트워크 수수료", "nounSymbol": "기호", "nounType": "유형", "nounDescription": "설명", "nounYes": "예", "nounNo": "아니요", "amount": "금액", "limit": "한도", "new": "신규", "gotIt": "확인", "internal": "내부", "reward": "리워드", "seeAll": "모두 보기", "seeLess": "간단히 보기", "viewAll": "모두 보기", "homeTab": "홈", "collectiblesTab": "콜렉터블", "swapTab": "스왑", "activityTab": "활동", "exploreTab": "탐색기", "accountHeaderConnectedInterpolated": "{{origin}}에 연결되었습니다", "accountHeaderConnectedToSite": "이 사이트에 연결되었습니다", "accountHeaderCopyToClipboard": "클립보드에 복사", "accountHeaderNotConnected": "연결되지 않았습니다", "accountHeaderNotConnectedInterpolated": "{{origin}}에 연결되지 않았습니다", "accountHeaderNotConnectedToSite": "이 사이트에 연결되지 않았습니다", "accountWithoutEnoughSolActionButtonCancel": "취소", "accountWithoutEnoughSolPrimaryText": "SOL 부족", "accountWithoutEnoughSolSecondaryText": "이 거래와 관련된 계정에 SOL이 충분하지 않습니다. 이 계정은 귀하 또는 다른 사람의 계정일 수 있습니다. 제출하면 이 거래는 되돌아갑니다.", "accountSwitcher": "계정 전환기", "addAccountHardwareWalletPrimaryText": "하드웨어 월릿 연결", "addAccountHardwareWalletSecondaryText": "Ledger 하드웨어 월릿 사용", "addAccountHardwareWalletSecondaryTextMobile": "{{supportedHardwareWallets}} 월릿 사용", "addAccountSeedVaultWalletPrimaryText": "시드 볼트 연결", "addAccountSeedVaultWalletSecondaryText": "시드 볼트에서 월릿 사용", "addAccountImportSeedPhrasePrimaryText": "복구용 비밀 문구 가져오기", "addAccountImportSeedPhraseSecondaryText": "다른 월릿에서 계정 가져오기", "addAccountImportWalletPrimaryText": "비공개 키 가져오기", "addAccountImportWalletSecondaryText": "싱글 체인 계정 가져오기", "addAccountImportWalletSolanaSecondaryText": "Solana 비공개 키 가져오기", "addAccountLimitReachedText": "Phantom에서 {{accountsCount}} 계정 제한에 도달했습니다. 계정을 더 추가하기 전에 사용하지 않는 계정을 제거하십시오.", "addAccountNoSeedAvailableText": "사용 가능한 시드 문구가 없습니다. 계정을 생성하려면 기존 시드를 가져오십시오.", "addAccountNewWalletPrimaryText": "새 계정 생성", "addAccountNewWalletSecondaryText": "새 월릿 주소 생성", "addAccountNewMultiChainWalletSecondaryText": "새 멀티체인 계정 추가", "addAccountNewSingleChainWalletSecondaryText": "새 계정 추가", "addAccountPrimaryText": "월릿 추가/연결", "addAccountSecretPhraseLabel": "비밀 문구", "addAccountSeedLabel": "시드", "addAccountSeedIDLabel": "시드 ID", "addAccountSecretPhraseDefaultLabel": "비밀 문구 {{number}}", "addAccountPrivateKeyDefaultLabel": "비공개 키 {{number}}", "addAccountZeroAccountsForSeed": "계정 0개", "addAccountShowAccountForSeed": "계정 1개 표시", "addAccountShowAccountsForSeed": "계정 {{numOfAccounts}}개 표시", "addAccountHideAccountForSeed": "계정 1개 숨기기", "addAccountHideAccountsForSeed": "계정 {{numOfAccounts}}개 숨기기", "addAccountSelectSeedDescription": "이 비밀 문구에서 새 계정이 생성됩니다.", "addAccountNumAccountsForSeed": "계정 {{numOfAccounts}}개", "addAccountOneAccountsForSeed": "계정 1개", "addAccountGenerateAccountFromSeed": "계정 생성", "addAccountReadOnly": "주소 보기", "addAccountReadOnlySecondaryText": "공개 월릿 주소 추적", "addAccountSolanaAddress": "Solana 주소", "addAccountEVMAddress": "EVM 주소", "addAccountBitcoinAddress": "Bitcoin 주소", "addAccountCreateSeedTitle": "새 계정 생성", "addAccountCreateSeedExplainer": "월릿에 아직 비밀 문구가 없습니다! 새 월렛을 만들기 위해 복구용 비밀 문구가 생성됩니다. 비밀 문구를 기록하고 보관해 두십시오.", "addAccountSecretPhraseHeader": "내 비밀 문구", "addAccountNoSecretPhrases": "비밀 문구를 사용할 수 없음", "addAccountImportAccountActionButtonImport": "가져오기", "addAccountImportAccountDuplicatePrivateKey": "이 계정은 이미 월릿에 있습니다", "addAccountImportAccountIncorrectFormat": "잘못된 형식", "addAccountImportAccountInvalidPrivateKey": "유효하지 않은 비공개 키", "addAccountImportAccountName": "이름", "addAccountImportAccountPrimaryText": "비공개 키 가져오기", "addAccountImportAccountPrivateKey": "비공개 키", "addAccountImportAccountPublicKey": "주소 또는 도메인", "addAccountImportAccountPrivateKeyRequired": "비공개 키가 필요합니다", "addAccountImportAccountNameRequired": "이름은 필수입니다", "addAccountImportAccountPublicKeyRequired": "공개 주소는 필수입니다", "addAccountImportAccountDuplicateAddress": "이 주소는 이미 월릿에 있습니다", "addAddressAddressAlreadyAdded": "주소가 이미 추가되었습니다", "addAddressAddressAlreadyExists": "주소가 이미 있습니다", "addAddressAddressInvalid": "주소가 유효하지 않습니다", "addAddressAddressIsRequired": "주소가 필요합니다", "addAddressAddressPlaceholder": "주소", "addAddressLabelIsRequired": "라벨이 필요합니다", "addAddressLabelPlaceholder": "라벨", "addAddressPrimaryText": "주소 추가", "addAddressToast": "주소 추가됨", "createAssociatedTokenAccountCostLabelInterpolated": "여기에는 {{solAmountFormatted}} SOL이 소요됩니다", "createAssociatedTokenAccountErrorAccountExists": "이미 이 토큰 계정을 가지고 있습니다", "createAssociatedTokenAccountErrorInsufficientFunds": "자금 부족", "createAssociatedTokenAccountErrorInvalidMint": "유효하지 않은 mint 주소", "createAssociatedTokenAccountErrorInvalidName": "유효하지 않은 이름", "createAssociatedTokenAccountErrorInvalidSymbol": "유효하지 않은 기호", "createAssociatedTokenAccountErrorUnableToCreateMessage": "토큰 계정을 만들 수 없습니다. 다시 시도하십시오.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "토큰 계정을 만들지 못함", "createAssociatedTokenAccountErrorUnableToSendMessage": "거래를 보낼 수 없습니다.", "createAssociatedTokenAccountErrorUnableToSendTitle": "거래를 보내지 못함", "createAssociatedTokenAccountInputPlaceholderMint": "Mint 주소", "createAssociatedTokenAccountInputPlaceholderName": "이름", "createAssociatedTokenAccountInputPlaceholderSymbol": "기호", "createAssociatedTokenAccountLoadingMessage": "토큰 계정을 만드는 중입니다.", "createAssociatedTokenAccountLoadingTitle": "토큰 계정 생성 중", "createAssociatedTokenAccountPageHeader": "토큰 계정 생성", "createAssociatedTokenAccountSuccessMessage": "토큰 계정이 생성되었습니다!", "createAssociatedTokenAccountSuccessTitle": "토큰 계정 생성함", "createAssociatedTokenAccountViewTransaction": "거래 보기", "assetDetailRecentActivity": "최근 활동", "assetDetailStakeSOL": "SOL 스테이킹", "assetDetailUnknownToken": "알 수 없는 토큰", "assetDetailUnwrapAll": "모두 래핑 해제", "assetDetailUnwrappingSOL": "SOL 래핑 해제", "assetDetailUnwrappingSOLFailed": "SOL 래핑 해제 실패", "assetDetailViewOnExplorer": "{{explorer}}에서 보기", "assetDetailViewOnExplorerDefaultExplorer": "탐색기", "assetDetailSaveToPhotos": "사진에 저장", "assetDetailSaveToPhotosToast": "사진에 저장됨", "assetDetailPinCollection": "컬렉션 고정", "assetDetailUnpinCollection": "컬렉션 고정 해제", "assetDetailHideCollection": "컬렉션 숨기기", "assetDetailUnhideCollection": "컬렉션 숨기기 해제", "assetDetailTokenNameLabel": "토큰 이름", "assetDetailNetworkLabel": "네트워크", "assetDetailAddressLabel": "주소", "assetDetailPriceLabel": "가격", "collectibleDetailSetAsAvatar": "아바타로 설정", "collectibleDetailSetAsAvatarSingleWorkAlt": "아바타", "collectibleDetailSetAsAvatarSuccess": "아바타 설정됨", "collectibleDetailShare": "콜렉터블 공유", "assetDetailTokenAddressCopied": "주소 복사됨", "assetDetailStakingLabel": "스테이킹", "assetDetailAboutLabel": "{{fungibleName}} 정보", "assetDetailPriceDetail": "가격 세부 정보", "assetDetailHighlights": "하이라이트", "assetDetailAllTimeReturn": "총 수익률", "assetDetailAverageCost": "평균 비용", "assetDetailPriceHistoryUnavailable": "이 토큰의 가격 내역 없음", "assetDetailPriceHistoryInsufficientData": "이 시간 범위의 가격 내역 없음", "assetDetailPriceDataUnavailable": "가격 데이터 없음", "assetDetailPriceHistoryError": "가격 내역을 가져오는 중에 오류 발생", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1일", "assetDetailTimeFrame24h": "24시간 가격", "assetDetailTimeFrame1W": "1주일", "assetDetailTimeFrame1M": "1개월", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "모두", "sendAssetAmountLabelInterpolated": "{{amount}} {{tokenSymbol}} 사용 가능", "fiatRampQuotes": "견적", "fiatRampNewQuote": "새로운 견적", "assetListSelectToken": "토큰 선택", "assetListSearch": "검색...", "assetListUnknownToken": "알 수 없는 토큰", "buyFlowHealthWarning": "일부 결제 제공자의 트래픽이 높습니다. 입금이 몇 시간 지연될 수 있습니다.", "assetVisibilityUnknownToken": "알 수 없는 토큰", "buyAssetInterpolated": "{{tokenSymbol}} 구입", "buyAssetScreenMaxPurchasePriceInterpolated": "최대 구매 금액은 {{amount}}입니다", "buyAssetScreenMinPurchasePriceInterpolated": "최소 구매 금액은 {{amount}}입니다", "buyNoAssetsAvailable": "Ethereum 또는 Polygon 자산을 사용할 수 없습니다", "buyThirdPartyScreenPaymentMethodSelector": "결제 방법:", "buyThirdPartyScreenPaymentMethod": "결제 수단 선택", "buyThirdPartyScreenChoseQuote": "견적에 유효한 금액 입력", "buyThirdPartyScreenProviders": "제공자", "buyThirdPartyScreenPaymentMethodTitle": "결제 수단", "buyThirdPartyScreenPaymentMethodEmptyState": "해당 지역에서 사용할 수 있는 결제 수단이 없습니다", "buyThirdPartyScreenPaymentMethodFooter": "결제는 네트워크 파트너가 처리합니다. 수수료가 다를 수 있습니다. 일부 결제 수단은 해당 지역에서 사용할 수 없습니다.", "buyThirdPartyScreenProvidersEmptyState": "해당 지역에서 사용할 수 있는 제공자가 없습니다", "buyThirdPartyScreenLoadingQuote": "견적 로드 중...", "buyThirdPartyScreenViewQuote": "견적 보기", "gasEstimationErrorWarning": "이 거래에 대한 수수료를 예상하는 중에 문제가 발생했습니다. 실패할 수도 있습니다.", "gasEstimationCouldNotFetch": "가스 예상치를 가져올 수 없음", "networkFeeCouldNotFetch": "네트워크 수수료를 가져올 수 없음", "nativeTokenBalanceErrorWarning": "이 거래에 대한 토큰 잔액을 가져오는 중에 문제가 발생했습니다. 실패할 수도 있습니다.", "blocklistOriginCommunityDatabaseInterpolated": "이 사이트는 알려진 피싱 웹사이트 및 스캠에 관한 <1>커뮤니티 관리 데이터베이스</1>에 등록된 것으로 표시됩니다. 해당 사이트가 잘못 표시되었다고 생각하시면 <3>문제를 보고</3>해주십시오.", "blocklistOriginDomainIsBlocked": "{{domainName}}이(가) 차단되었습니다!", "blocklistOriginIgnoreWarning": "이 경고를 무시하고 {{domainName}} 연결", "blocklistOriginSiteIsMalicious": "이 웹사이트는 악성이며 사용하기에 안전하지 않다고 판단됩니다.", "blocklistOriginThisDomain": "이 도메인", "blocklistProceedAnyway": "경고를 무시하고 계속하기", "maliciousTransactionWarning": "이 거래는 악성이며 서명하기에 안전하지 않다고 판단됩니다. 귀하와 귀하의 자금을 보호하기 위해 여기에 서명하는 기능을 비활성화했습니다.", "maliciousTransactionWarningIgnoreWarning": "경고를 무시하고 계속하기", "maliciousTransactionWarningTitle": "거래에 플래그가 지정되었습니다!", "maliciousRequestBlockedTitle": "요청 차단됨", "maliciousRequestWarning": "이 웹사이트는 악성으로 플래그가 지정되었습니다. 자금을 훔치거나 거짓 요청을 확인하도록 속이려는 시도를 할 수 있습니다.", "maliciousSignatureRequestBlocked": "귀하의 안전을 위해 Phantom에서 이 요청을 차단했습니다.", "maliciousRequestBlocked": "귀하의 안전을 위해 Phantom에서 이 요청을 차단했습니다.", "maliciousRequestFrictionDescription": "진행하는 것이 안전하지 않으므로 Phantom에서 이 요청을 차단했습니다. 이 대화를 닫아야 합니다.", "maliciousRequestAcknowledge": "이 웹사이트를 사용하면 모든 자금을 잃을 수 있다는 것을 이해합니다.", "maliciousRequestAreYouSure": "계속하시겠습니까?", "siwErrorPopupTitle": "잘못된 서명 요청", "siwParseErrorDescription": "형식이 잘못되어 앱의 서명 요청을 표시할 수 없습니다.", "siwVerificationErrorDescription": "메시지 서명 요청에 1개 이상의 오류가 있습니다. 보안을 위해 올바른 앱을 사용하고 있는지 확인하고 다시 시도하십시오.", "siwErrorPagination": "{{n}}/{{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "경고: 앱의 주소가 서명을 위해 제공된 주소와 일치하지 않습니다.", "siwErrorMessage_DOMAIN_MISMATCH": "경고: 앱의 도메인이 확인을 위해 제공된 도메인과 일치하지 않습니다.", "siwErrorMessage_URI_MISMATCH": "경고: URI 호스트 이름이 도메인과 일치하지 않습니다.", "siwErrorMessage_CHAIN_ID_MISMATCH": "경고: 체인 ID가 확인을 위해 제공된 체인 ID와 일치하지 않습니다.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "경고: 메시지 배포 날짜가 너무 먼 과거입니다.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "경고: 메시지 배포 날짜가 너무 먼 미래입니다.", "siwErrorMessage_EXPIRED": "경고: 메시지가 만료되었습니다.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "경고: 메시지가 배포되기 전에 만료됩니다.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "경고: 메시지가 유효해지기 전에 만료됩니다.", "siwErrorShowErrorDetails": "오류 세부 정보 표시", "siwErrorHideErrorDetails": "오류 세부 정보 숨기기", "siwErrorIgnoreWarning": "경고를 무시하고 계속하기", "siwsTitle": "로그인 요청", "siwsPermissions": "권한", "siwsAgreement": "메시지", "siwsAdvancedDetails": "고급 세부 정보", "siwsAlternateStatement": "{{domain}}에서 Solana 계정으로 로그인하라는 메시지가 표시됩니다.\n{{address}}", "siwsFieldLable_domain": "도메인", "siwsFieldLable_address": "주소", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "버전", "siwsFieldLable_chainId": "체인 ID", "siwsFieldLable_nonce": "논스", "siwsFieldLable_issuedAt": "배포", "siwsFieldLable_expirationTime": "만료 시간", "siwsFieldLable_requestId": "요청 ID", "siwsFieldLable_resources": "리소스", "siwsVerificationErrorDescription": "이 로그인 요청은 유효하지 않습니다. 즉, 사이트가 안전하지 않거나 개발자가 요청을 보낼 때 오류가 발생했음을 의미합니다.", "siwsErrorNumIssues": "문제 {{n}}개", "siwsErrorMessage_CHAIN_ID_MISMATCH": "이 체인 ID는 현재 네트워크와 일치하지 않습니다.", "siwsErrorMessage_DOMAIN_MISMATCH": "이 도메인은 로그인하려는 도메인이 아닙니다.", "siwsErrorMessage_URI_MISMATCH": "이 URI는 로그인하려는 URI가 아닙니다.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "메시지 배포 날짜가 너무 먼 과거입니다.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "메시지 배포 날짜가 너무 먼 미래입니다.", "siwsErrorMessage_EXPIRED": "메시지가 만료되었습니다.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "메시지가 배포되기 전에 만료됩니다.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "메시지가 유효해지기 전에 만료됩니다.", "changeLockTimerPrimaryText": "자동 잠금 타이머", "changeLockTimerSecondaryText": "월릿이 유휴상태가 된 후 잠금상태가 되기까지 얼마나 대기해야 합니까?", "changeLockTimerToast": "자동 잠금 타이머 업데이트됨", "changePasswordConfirmNewPassword": "새 비밀번호 확인", "changePasswordCurrentPassword": "현재 비밀번호", "changePasswordErrorIncorrectCurrentPassword": "잘못된 현재 비밀번호", "changePasswordErrorGeneric": "문제가 발생했습니다. 잠시 후 다시 시도해주십시오", "changePasswordNewPassword": "새 비밀번호", "changePasswordPrimaryText": "비밀번호 변경", "changePasswordToast": "비밀번호 업데이트됨", "collectionsSpamCollections": "스팸 컬렉션", "collectionsHiddenCollections": "숨긴 컬렉션", "collectiblesReportAsSpam": "스팸으로 신고", "collectiblesReportAsSpamAndHide": "스팸으로 신고하고 숨기기", "collectiblesReportAsNotSpam": "스팸 아님으로 신고", "collectiblesReportAsNotSpamAndUnhide": "숨기기 해제하고 스팸 아님으로 신고", "collectiblesReportNotSpam": "스팸 아님", "collectionsManageCollectibles": "콜렉터블 목록 관리", "collectibleDetailDescription": "설명", "collectibleDetailProperties": "자산", "collectibleDetailOrdinalInfo": "서수 정보", "collectibleDetailRareSatsInfo": "Rare Sats 정보", "collectibleDetailSatsInUtxo": "UTXO의 Sat", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Sat 번호", "collectibleDetailSatName": "Sat 이름", "collectibleDetailInscriptionId": "Inscription ID", "collectibleDetailInscriptionNumber": "Inscription 번호", "collectibleDetailStandard": "기준", "collectibleDetailCreated": "생성됨", "collectibleDetailViewOnExplorer": "{{explorer}}에서 보기", "collectibleDetailList": "상장", "collectibleDetailSellNow": "{{amount}} {{symbol}}에 판매", "collectibleDetailUtxoSplitterCtaTitle": "스페어 Bitcoin ​​확보", "collectibleDetailUtxoSplitterCtaSubtitle": "잠금 해제할 {{value}}의 BTC가 있습니다", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "귀하의 자금을 보호하기 위해 당사는 Rare Sats가 포함된 UTXO의 BTC가 전송되는 것을 방지합니다. Magic Eden의 UTXO 스플리터를 사용하여 Rare Sats에서 {{value}}의 BTC를 확보하십시오.", "collectibleDetailUtxoSplitterModalCtaButton": "UTXO 스플리터 사용", "collectibleDetailEasilyAccept": "최고 오퍼 수락", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sat", "collectibleDetailSpamOverlayDescription": "이 콜렉터블은 Phantom에서 스팸으로 분류되어 숨겨졌습니다.", "collectibleDetailSpamOverlayReveal": "콜렉터블 표시", "collectibleBurnTermsOfService": "이 작업은 취소할 수 없음을 이해합니다", "collectibleBurnTitleWithCount_one": "토큰 소각", "collectibleBurnTitleWithCount_other": "토큰 소각", "collectibleBurnDescriptionWithCount_one": "이 작업은 월렛에서 해당 토큰을 영구적으로 폐기하고 제거합니다.", "collectibleBurnDescriptionWithCount_other": "이 작업은 월렛에서 해당 토큰을 영구적으로 폐기하고 제거합니다.", "collectibleBurnTokenWithCount_one": "토큰", "collectibleBurnTokenWithCount_other": "토큰", "collectibleBurnCta": "소각", "collectibleBurnRebate": "리베이트", "collectibleBurnRebateTooltip": "이 토큰 소각을 위해 소량의 SOL이 자동으로 귀하의 월릿에 입금됩니다.", "collectibleBurnNetworkFee": "네트워크 수수료", "collectibleBurnNetworkFeeTooltip": "거래 처리를 위해 Solana 네트워크에서 요구한 금액", "unwrapButtonSwapTo": "{{chainSymbol}}(으)로 스왑", "unwrapButtonWithdrawFrom": "{{chainSymbol}}용 {{withdrawalSource}}에서 인출", "unwrapModalEstimatedTime": "예상 시간", "unwrapModalNetwork": "네트워크", "unwrapModalNetworkFee": "네트워크 수수료", "unwrapModalTitle": "요약", "unsupportedChain": "지원되지 않는 체인", "unsupportedChainDescription": "{{chainName}} 네트워크에서 {{action}} 지원이 되지 않는 것 같습니다.", "networkFeesTooltipLabel": "{{chainName}} 네트워크 수수료", "networkFeesTooltipDescription": "{{chainName}} 수수료는 여러 요인에 따라 다릅니다. 거래를 더 빠르게(더 비싸게) 또는 더 느리게(더 저렴하게) 만들기 위해 사용자 지정할 수 있습니다.", "burnStatusErrorTitleWithCount_one": "토큰 소각 실패", "burnStatusErrorTitleWithCount_other": "토큰 소각 실패", "burnStatusSuccessTitleWithCount_one": "토큰 소각 완료!", "burnStatusSuccessTitleWithCount_other": "토큰 소각 완료!", "burnStatusLoadingTitleWithCount_one": "토큰 소각 중...", "burnStatusLoadingTitleWithCount_other": "토큰 소각 중...", "burnStatusErrorMessageWithCount_one": "이 토큰은 소각할 수 없습니다. 나중에 다시 시도해주십시오.", "burnStatusErrorMessageWithCount_other": "이 토큰은 소각할 수 없습니다. 나중에 다시 시도해주십시오.", "burnStatusSuccessMessageWithCount_one": "이 토큰은 영구적으로 폐기되었으며 {{rebateAmount}} SOL이 귀하의 월릿에 입금되었습니다.", "burnStatusSuccessMessageWithCount_other": "이 토큰은 영구적으로 폐기되었으며 {{rebateAmount}} SOL이 귀하의 월릿에 입금되었습니다.", "burnStatusLoadingMessageWithCount_one": "이 토큰은 영구적으로 폐기되었으며 {{rebateAmount}} SOL이 귀하의 월릿에 입금됩니다.", "burnStatusLoadingMessageWithCount_other": "해당 토큰은 영구적으로 폐기되었으며 {{rebateAmount}} SOL이 귀하의 월릿에 입금됩니다.", "burnStatusViewTransactionText": "거래 보기", "collectibleDisplayLoading": "로드 중...", "collectiblesNoCollectibles": "콜렉터블이 없습니다", "collectiblesPrimaryText": "내 콜렉터블", "collectiblesReceiveCollectible": "콜렉터블 받기", "collectiblesUnknownCollection": "알 수 없는 컬렉션", "collectiblesUnknownCollectible": "알 수 없는 콜렉터블", "collectiblesUniqueHolders": "고유한 홀더", "collectiblesSupply": "공급", "collectiblesUnknownTokens": "알 수 없는 토큰", "collectiblesNrOfListed": "{{ nrOfListed }}개 상장됨", "collectiblesListed": "상장됨", "collectiblesMintCollectible": "콜렉터블 민팅", "collectiblesYouMint": "내가 민팅함", "collectiblesMintCost": "민팅 비용", "collectiblesMintFail": "민팅 실패", "collectiblesMintFailMessage": "콜렉터블을 민팅하는 중에 문제가 발생했습니다. 다시 시도하십시오.", "collectiblesMintCostFree": "무료", "collectiblesMinting": "민팅 중...", "collectiblesMintingMessage": "콜렉터블을 민팅하는 중입니다", "collectiblesMintShareSubject": "확인해 보십시오", "collectiblesMintShareMessage": "@phantom에서 이걸 민팅했습니다!", "collectiblesMintSuccess": "민팅 성공", "collectiblesMintSuccessMessage": "지금 콜렉터블이 민팅되었습니다", "collectiblesMintSuccessQuestMessage": "Phantom Quest의 요구 사항을 충족했습니다. '내 리워드 클레임'을 탭하여 무료 콜렉터블을 받으십시오.", "collectiblesMintRequired": "필요", "collectiblesMintMaxLengthErrorMessage": "최대 길이 초과됨", "collectiblesMintSafelyDismiss": "이 창을 안전하게 닫을 수 있습니다.", "collectiblesTrimmed": "현재 표시할 수 있는 콜렉터블 수 제한에 도달했습니다.", "collectiblesNonTransferable": "양도 불가", "collectiblesNonTransferableYes": "예", "collectiblesSellOfferDetails": "오퍼 세부 정보", "collectiblesSellYouSell": "판매", "collectiblesSellGotIt": "확인", "collectiblesSellYouReceive": "수령", "collectiblesSellOffer": "오퍼", "collectiblesSoldCollectible": "콜렉터블 판매함", "collectiblesSellMarketplace": "마켓플레이스", "collectiblesSellCollectionFloor": "컬렉션 최저 가격", "collectiblesSellDifferenceFromFloor": "최저 가격에서의 차이", "collectiblesSellLastSalePrice": "지난 세일", "collectiblesSellEstimatedFees": "예상 수수료", "collectiblesSellEstimatedProfitAndLoss": "예상 손익", "collectiblesSellViewOnMarketplace": "{{marketplace}}에서 보기", "collectiblesSellCollectionFloorTooltip": "여러 마켓플레이스의 컬렉션에서 가장 낮은 '지금 구입' 가격.", "collectiblesSellProfitLossTooltip": "예상 손익은 마지막 판매 가격과 오퍼 금액에서 수수료를 뺀 금액을 기준으로 계산됩니다.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "로열티({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "마켓플레이스 수수료({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "마켓플레이스 수수료", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}} 네트워크", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "견적에는 {{phantomFeePercentage}} Phantom 수수료가 포함됩니다", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "견적에는 로열티, 네트워크 수수료, 마켓플레이스 수수료 및 {{phantomFeePercentage}} Phantom 수수료가 포함됩니다", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "견적에는 로열티, 네트워크 수수료 및 마켓플레이스 수수료가 포함됩니다", "collectiblesSellTransactionFeeTooltipTitle": "거래 수수료", "collectiblesSellStatusLoadingTitle": "오퍼 수락 중...", "collectiblesSellStatusLoadingIsSellingFor": "판매 가격은", "collectiblesSellStatusSuccessTitle": "{{collectibleName}}을(를) 판매했습니다!", "collectiblesSellStatusSuccessWasSold": "판매가 완료되었습니다:", "collectiblesSellStatusErrorTitle": "문제가 발생했습니다", "collectiblesSellStatusErrorSubtitle": "판매하려는 중에 문제가 발생했습니다:", "collectiblesSellStatusViewTransaction": "거래 보기", "collectiblesSellInsufficientFundsTitle": "자금 부족", "collectiblesSellInsufficientFundsSubtitle": "네트워크 수수료를 지불하기 위한 자금이 부족하기 때문에 이 콜렉터블의 오퍼를 수락할 수 없습니다.", "collectiblesSellRecentlyTransferedNFTTitle": "최근 전송한 항목", "collectiblesSellRecentlyTransferedNFTSubtitle": "전송 후 입찰을 수락하는 데 1시간이 걸립니다.", "collectiblesApproveCollection": "{{collectionName}} 승인함", "collectiblesSellNotAvailableAnymoreTitle": "오퍼 사용 불가", "collectiblesSellNotAvailableAnymoreSubtitle": "이 오퍼는 더 이상 사용할 수 없습니다. 이 입찰을 취소하고 다시 시도하십시오.", "collectiblesSellFlaggedTokenTitle": "콜렉터블에 플래그가 지정되었습니다", "collectiblesSellFlaggedTokenSubtitle": "콜렉터블을 거래할 수 없습니다. 도난되거나 락업 없이 스테이킹되어 신고되는 등 여러 가지 이유가 있을 수 있습니다.", "collectiblesListOnMagicEden": "Magic Eden에서 상장", "collectiblesListPrice": "정가", "collectiblesUseFloor": "최저 가격 사용", "collectiblesFloorPrice": "최저 가격", "collectiblesLastSalePrice": "마지막 판매 가격", "collectiblesTotalReturn": "총 수익", "collectiblesOriginalPurchasePrice": "원래 구매 가격", "collectiblesMagicEdenFee": "Magic Eden 수수료", "collectiblesArtistRoyalties": "아티스트 로열티", "collectiblesListNowButton": "지금 상장", "collectiblesListAnywayButton": "그래도 상장", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "상장 보기", "collectiblesListingViewTransaction": "거래 보기", "collectiblesRemoveListing": "상장 제거", "collectiblesEditListing": "상장 편집", "collectiblesEditListPrice": "정가 편집", "collectiblesListPriceTooltip": "정가는 항목의 판매 가격입니다. 판매자는 일반적으로 정가를 최저 가격 이상으로 설정합니다.", "collectiblesFloorPriceTooltip": "최저 가격은 이 컬렉션의 항목에 대한 최저 활성 정가입니다.", "collectiblesOriginalPurchasePriceTooltip": "원래 이 금액으로 이 항목을 구입했습니다.", "collectiblesPurchasedForSol": "{{lastPurchasePrice}} SOL용으로 구매함", "collectiblesUnableToLoadListings": "상장 항목을 로드할 수 없음", "collectiblesUnableToLoadListingsFrom": "{{marketplace}}에서 상장 항목을 로드할 수 없음", "collectiblesUnableToLoadListingsDescription": "해당 상장 항목과 자산은 안전하지만 현재는 {{marketplace}}에서 로드할 수 없습니다. 나중에 다시 시도하십시오.", "collectiblesBelowFloorPrice": "최저 가격 이하", "collectiblesBelowFloorPriceMessage": "최저 가격 이하 NFT를 상장하시겠습니까?", "collectiblesMinimumListingPrice": "최소 가격은 0.01 SOL입니다", "collectiblesMagicEdenFeeTooltip": "Magic Eden은 완료된 거래에 대해 수수료를 받습니다.", "collectiblesArtistRoyaltiesTooltip": "이 컬렉션을 만든 사람은 판매가 완료될 때마다 로열티 %를 받습니다.", "collectibleScreenCollectionLabel": "컬렉션", "collectibleScreenPhotosPermissionTitle": "사진 권한", "collectibleScreenPhotosPermissionMessage": "사진에 액세스하기 위한 권한이 필요합니다. 설정으로 이동하여 권한을 업데이트하십시오.", "collectibleScreenPhotosPermissionOpenSettings": "설정 열기", "listStatusErrorTitle": "상장 실패", "editListStatusErrorTitle": "업데이트할 수 없음", "removeListStatusErrorTitle": "실패한 상장 제거", "listStatusSuccessTitle": "상장 항목이 생성되었습니다!", "editListingStatusSuccessTitle": "상장 항목이 업데이트되었습니다!", "removeListStatusSuccessTitle": "Magic Eden에서 제거된 상장 항목", "listStatusLoadingTitle": "상장 항목을 만드는 중...", "editListingStatusLoadingTitle": "상장 항목을 업데이트하는 중...", "removeListStatusLoadingTitle": "상장 항목을 제거하는 중...", "listStatusErrorMessage": "{{name}}은(는) Magic Eden에서 상장할 수 없습니다", "removeListStatusErrorMessage": "{{name}}은(는) Magic Eden에서 상장 취소할 수 없습니다", "listStatusSuccessMessage": "이제 {{listCollectiblePrice}} SOL에 대한 {{name}}이(가) Magic Eden에서 상장되었습니다", "editListingStatusSuccessMessage": "이제 {{editListCollectiblePrice}} SOL에 대한 {{name}}이(가) Magic Eden에서 업데이트되었습니다", "removeListStatusSuccessMessage": "{{name}}이(가) Magic Eden에서 제거되었습니다", "listStatusLoadingMessage": "Magic Eden에서 {{listCollectiblePrice}} SOL에 대한 {{name}} 상장 중입니다.", "editListingStatusLoadingMessage": "Magic Eden에서 {{editListCollectiblePrice}} SOL에 대한 {{name}} 업데이트 중입니다.", "removeListStatusLoadingMessage": "Magic Eden에서 {{name}}을(를) 제거하는 중입니다. 시간이 좀 걸릴 수 있습니다.", "listStatusLoadingSafelyDismiss": "이 창을 안전하게 닫을 수 있습니다.", "listStatusViewOnMagicEden": "Magic Eden에서 보기", "listStatusViewOnMarketplace": "{{marketplace}}에서 보기", "listStatusLoadingDismiss": "닫기", "listStatusViewTransaction": "거래 보기", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "하드웨어 월릿을 연결하고, 잠금 해제하십시오. 월릿이 탐지되면 사용할 주소를 선택할 수 있습니다.", "connectHardwareFailedPrimaryText": "연결 실패", "connectHardwareFailedSecondaryText": "하드웨어 월릿을 연결하고, 잠금 해제하십시오. 월릿이 발견되면 사용할 주소를 선택할 수 있습니다.", "connectHardwareFinishPrimaryText": "계정이 추가되었습니다!", "connectHardwareFinishSecondaryText": "이제 Phantom 내에서 Ledger Nano 월릿에 액세스할 수 있습니다. 확장 프로그램으로 돌아가십시오.", "connectHardwareNeedsPermissionPrimaryText": "새 월릿 연결", "connectHardwareNeedsPermissionSecondaryText": "연결 프로세스를 시작하려면 아래 버튼을 클릭하십시오.", "connectHardwareSearchingPrimaryText": "월릿 검색 중...", "connectHardwareSearchingSecondaryText": "하드웨어 월릿을 연결하고, 해당 월릿이 잠금 해제되어 있으며 사용 중인 브라우저에서 권한을 승인했는지 확인하십시오.", "connectHardwarePermissionDeniedPrimary": "권한 거부됨", "connectHardwarePermissionDeniedSecondary": "Phantom 권한을 부여하여 Ledger 장치에 연결", "connectHardwarePermissionUnableToConnect": "연결할 수 없음", "connectHardwarePermissionUnableToConnectDescription": "Ledger 장치에 연결할 수 없습니다. 추가 권한이 필요할 수 있습니다.", "connectHardwareSelectAddressAllAddressesImported": "모든 주소를 가져왔습니다", "connectHardwareSelectAddressDerivationPath": "파생 경로", "connectHardwareSelectAddressSearching": "검색 중...", "connectHardwareSelectAddressSelectWalletAddress": "월릿 주소 선택", "connectHardwareSelectAddressWalletAddress": "월릿 주소", "connectHardwareWaitingForApplicationSecondaryText": "하드웨어 월릿을 연결하고, 잠금 해제하십시오.", "connectHardwareWaitingForPermissionPrimaryText": "권한 필요", "connectHardwareWaitingForPermissionSecondaryText": "하드웨어 월릿을 연결하고, 해당 월릿이 잠금 해제되어 있으며 사용 중인 브라우저에서 권한을 승인했는지 확인하십시오.", "connectHardwareAddAccountButton": "계정 추가", "connectHardwareLedger": "Ledger 연결", "connectHardwareStartConnection": "Ledger 하드웨어 월릿 연결을 시작하려면 아래 버튼을 클릭하십시오", "connectHardwarePairSuccessPrimary": "{{productName}} 연결됨", "connectHardwarePairSuccessSecondary": "{{productName}}을(를) 연결했습니다.", "connectHardwareSelectChains": "연결할 체인 선택", "connectHardwareSearching": "검색 중...", "connectHardwareMakeSureConnected": "하드웨어 월릿을 연결하고 잠금 해제하십시오. 관련 브라우저 권한을 승인하십시오.", "connectHardwareOpenAppDescription": "하드웨어 월릿을 잠금 해제하십시오", "connectHardwareConnecting": "연결 중...", "connectHardwareConnectingDescription": "Ledger 장치에 연결 중입니다.", "connectHardwareConnectingAccounts": "계정 연결 중...", "connectHardwareDiscoveringAccounts": "계정 검색 중...", "connectHardwareDiscoveringAccountsDescription": "사용자 계정의 활동을 찾고 있습니다.", "connectHardwareErrorLedgerLocked": "Ledger가 잠겼습니다", "connectHardwareErrorLedgerLockedDescription": "Ledger 장치가 잠금 해제되어 있는지 확인하고 다시 시도하십시오.", "connectHardwareErrorLedgerGeneric": "문제가 발생했습니다", "connectHardwareErrorLedgerGenericDescription": "계정을 찾을 수 없습니다. Ledger 장치가 잠금 해제되어 있는지 확인하고 다시 시도하십시오.", "connectHardwareErrorLedgerPhantomLocked": "Phantom을 다시 열고 하드웨어를 다시 연결하세요.", "connectHardwareFindingAccountsWithActivity": "{{chainName}} 계정을 찾는 중...", "connectHardwareFindingAccountsWithActivityDualChain": "{{chainName1}} 또는 {{chainName2}} 계정을 찾는 중...", "connectHardwareFoundAccountsWithActivity": "Ledger에서 활동이 있는 계정 {{numOfAccounts}}개를 찾았습니다.", "connectHardwareFoundAccountsWithActivitySingular": "Ledger에서 활동이 있는 계정 1개를 찾았습니다.", "connectHardwareFoundSomeAccounts": "Ledger 장치에서 일부 계정을 찾았습니다.", "connectHardwareViewAccounts": "계정 보기", "connectHardwareConnectAccounts": "계정 연결됨", "connectHardwareSelectAccounts": "계정 선택", "connectHardwareChooseAccountsToConnect": "연결할 월릿 계정을 선택합니다.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}}개 계정 추가됨", "connectHardwareAccountsStepOfSteps": "{{stepNum}}/{{totalSteps}}단계", "connectHardwareMobile": "Ledger 연결", "connectHardwareMobileTitle": "Ledger 하드웨어 월릿 연결", "connectHardwareMobileEnableBluetooth": "Bluetooth 활성화", "connectHardwareMobileEnableBluetoothDescription": "Bluetooth를 사용하여 연결할 수 있는 권한 허용", "connectHardwareMobileEnableBluetoothSettings": "설정으로 이동하여 Phantom이 위치 및 주변 장치 권한을 사용하도록 허용하십시오.", "connectHardwareMobilePairWithDevice": "Ledger 장치와 페어링", "connectHardwareMobilePairWithDeviceDescription": "최상의 신호를 얻으려면 장치를 가까이 두십시오", "connectHardwareMobileConnectAccounts": "계정 연결", "connectHardwareMobileConnectAccountsDescription": "사용했을 수 있는 계정의 활동을 확인할 것입니다", "connectHardwareMobileConnectLedgerDevice": "Ledger 장치 연결", "connectHardwareMobileLookingForDevices": "주변 장치를 찾는 중...", "connectHardwareMobileLookingForDevicesDescription": "Ledger 장치를 연결하고 잠금 해제되었는지 확인하십시오.", "connectHardwareMobileFoundDeviceSingular": "1개 Ledger 장치를 찾았습니다", "connectHardwareMobileFoundDevices": "{{numDevicesFound}}개 Ledger 장치를 찾았습니다", "connectHardwareMobileFoundDevicesDescription": "페어링을 시작하려면 아래에서 Ledger 장치를 선택하십시오.", "connectHardwareMobilePairingWith": "{{deviceName}}과(와) 페어링 중", "connectHardwareMobilePairingWithDescription": "페어링 중에 Ledger 장치의 지침을 따르십시오.", "connectHardwareMobilePairingFailed": "페어링 실패", "connectHardwareMobilePairingFailedDescription": "{{device<PERSON>ame}}과(와) 페어링할 수 없습니다. 장치가 잠금 해제되었는지 확인하십시오.", "connectHardwareMobilePairingSuccessful": "페어링 성공", "connectHardwareMobilePairingSuccessfulDescription": "Ledger 장치를 페어링하고 연결했습니다.", "connectHardwareMobileOpenAppSingleChain": "Ledger에서 {{chainName}} 앱 열기", "connectHardwareMobileOpenAppDualChain": "Ledger에서 {{chainName1}} 또는 {{chainName2}} 앱 열기", "connectHardwareMobileOpenAppDescription": "장치가 잠금 해제되었는지 확인하십시오.", "connectHardwareMobileStillCantFindDevice": "여전히 장치를 찾을 수 없습니까?", "connectHardwareMobileLostConnection": "연결 끊김", "connectHardwareMobileLostConnectionDescription": "{{deviceName}}에 대한 연결이 끊겼습니다. 장치가 잠금 해제되었는지 확인한 후 다시 시도하십시오.", "connectHardwareMobileGenericLedgerDevice": "Ledger 장치", "connectHardwareMobileConnectDeviceSigning": "내 {{deviceName}} 연결", "connectHardwareMobileConnectDeviceSigningDescription": "Ledger 장치를 잠금 해제하고 가까이 두십시오.", "connectHardwareMobileBluetoothDisabled": "Bluetooth가 비활성화되었습니다.", "connectHardwareMobileBluetoothDisabledDescription": "Bluetooth를 활성화하고 Ledger 장치가 잠금 해제되었는지 확인하십시오.", "connectHardwareMobileLearnMore": "자세히 알아보기", "connectHardwareMobileBlindSigningDisabled": "블라인드 서명이 비활성화되었습니다.", "connectHardwareMobileBlindSigningDisabledDescription": "장치에서 블라인드 서명을 활성화했는지 확인하십시오.", "connectHardwareMobileConfirmSingleChain": "하드웨어 월릿에서 거래를 확인해야 합니다. 해당 월릿이 잠금 해제되어 있는지 확인하십시오.", "metamaskExplainerBottomSheetHeader": "이 사이트는 Phantom에서 작동됩니다", "metamaskExplainerBottomSheetSubheader": "계속하려면 월릿 연결 대화 상자에서 MetaMask를 선택하십시오.", "metamaskExplainerBottomSheetDontShowAgain": "다시 표시 안 함", "ledgerStatusNotConnected": "Ledger가 연결되지 않았습니다", "ledgerStatusConnectedInterpolated": "{{productName}}이(가) 연결되었습니다", "connectionClusterInterpolated": "현재 {{cluster}}에 연결되어 있습니다", "connectionClusterTestnetMode": "Testnet 모드에 현재 있습니다", "featureNotSupportedOnLocalNet": "Solana Localnet이 활성화되어 있는 경우 이 기능이 지원되지 않습니다.", "readOnlyAccountBannerWarning": "이 계정을 조회하고 있습니다", "depositAddress": "수신 주소", "depositAddressChainInterpolated": "내 {{chain}} 주소", "depositAssetDepositInterpolated": "{{tokenSymbol}} 받기", "depositAssetSecondaryText": "이 주소는 호환 토큰을 수신하는 데만 사용할 수 있습니다.", "depositAssetTextInterpolated": "이 주소를 사용하여 <1>{{network}}</1>에서 토큰과 콜렉터블을 받으십시오", "depositAssetTransferFromExchange": "거래소에서 전송", "depositAssetShareAddress": "주소 공유", "depositAssetBuyOrDeposit": "구매 또는 전송", "depositAssetBuyOrDepositDesc": "시작하려면 월릿에 이체", "depositAssetTransfer": "전송", "editAddressAddressAlreadyAdded": "주소가 이미 추가되었습니다", "editAddressAddressAlreadyExists": "주소가 이미 있습니다", "editAddressAddressIsRequired": "주소가 필요합니다", "editAddressPrimaryText": "주소 편집", "editAddressRemove": "주소록에서 제거", "editAddressToast": "주소 업데이트됨", "removeSavedAddressToast": "주소 제거됨", "exportSecretErrorGeneric": "문제가 발생했습니다. 잠시 후 다시 시도해주십시오", "exportSecretErrorIncorrectPassword": "잘못된 비밀번호", "exportSecretPassword": "비밀번호", "exportSecretPrivateKey": "비공개 키", "exportSecretSecretPhrase": "비밀 문구", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "복구용 비밀 문구", "exportSecretSelectYourAccount": "계정 선택", "exportSecretShowPrivateKey": "비공개 키 표시", "exportSecretShowSecretRecoveryPhrase": "복구용 비밀 문구 표시", "exportSecretShowSecret": "{{secretNameText}} 표시", "exportSecretWarningPrimaryInterpolated": "{{secretNameText}}을(를) 공유하지 <1>마십시오</1>!", "exportSecretWarningSecondaryInterpolated": "누군가 귀하의 {{secretNameText}}을(를) 알면 귀하의 월릿을 완벽하게 제어할 수 있습니다.", "exportSecretOnlyWay": "{secretNameText}}만이 지갑을 복구할 수 있는 유일한 방법입니다.", "exportSecretDoNotShow": "아무도 {{secretNameText}}을(를) 볼 수 없도록 하십시오.", "exportSecretWillNotShare": "나는 Phantom을 포함하여 어느 누구와도 내 {{secretNameText}}을(를) 공유하지 않을 것입니다.", "exportSecretNeverShare": "다른 사람과 {{secretNameText}}을(를) 공유하지 마십시오.", "exportSecretYourPrivateKey": "내 비공개 키", "exportSecretYourSecretRecoveryPhrase": "내 복구용 비밀 문구", "exportSecretResetPin": "PIN 재설정", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "도움말", "gasUpTo": "최대 {{ amount }}", "timeDescription1hour": "약 1시간", "timeDescription30minutes": "약 30분", "timeDescription10minutes": "약 10분", "timeDescription2minutes": "약 2분", "timeDescription30seconds": "약 30초", "timeDescription15seconds": "약 15초", "timeDescription10seconds": "약 10초", "timeDescription5seconds": "약 5초", "timeDescriptionAbbrev1hour": "1시간", "timeDescriptionAbbrev30minutes": "30분", "timeDescriptionAbbrev10minutes": "10분", "timeDescriptionAbbrev2minutes": "2분", "timeDescriptionAbbrev30seconds": "30초", "timeDescriptionAbbrev15seconds": "15초", "timeDescriptionAbbrev10seconds": "10초", "timeDescriptionAbbrev5seconds": "5초", "gasSlow": "느림", "gasAverage": "평균", "gasFast": "빠름", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "다시 시도", "homeErrorDescription": "자산을 가져오려는 중에 오류가 발생했습니다. 새로 고친 후 다시 시도하십시오", "homeErrorTitle": "자산을 가져오지 못함", "homeManageTokenList": "토큰 상장 관리", "interstitialDismissUnderstood": "이해함", "interstitialBaseWelcomeTitle": "이제 Phantom에서 Base가 지원됩니다!", "interstitialBaseWelcomeItemTitle_1": "토큰 보내기, 받기, 구매", "interstitialBaseWelcomeItemTitle_2": "Base 생태계 살펴보기", "interstitialBaseWelcomeItemTitle_3": "안전 및 보안", "interstitialBaseWelcomeItemDescription_1": "{{paymentMethod}}, 카드 또는 Coinbase를 사용하여 Base에서 USDC 및 ETH를 전송하고 구매하십시오.", "interstitialBaseWelcomeItemDescription_2": "즐겨 사용하는 DeFi 및 NFT 앱에서 Phantom을 사용하십시오.", "interstitialBaseWelcomeItemDescription_3": "Ledger 지원, 스팸 필터링 및 거래 시뮬레이션으로 안전하게 유지하십시오.", "privacyPolicyChangedInterpolated": "개인정보 처리방침이 변경되었습니다. <1>자세히 알아보기</1>", "bitcoinAddressTypesBodyTitle": "Bitcoin 주소 유형", "bitcoinAddressTypesFeature1Title": "Bitcoin 주소 정보", "bitcoinAddressTypesFeature1Subtitle": "Phantom은 각각 고유한 균형을 갖춘 Native Segwit 및 Taproot를 지원합니다. 두 가지 주소 유형 중 하나로 BTC 또는 Ordinals를 보낼 수 있습니다.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Phantom의 기본 BTC 주소입니다. Taproot보다 오래되었지만 모든 월릿 및 거래소와 호환됩니다.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Ordinals 및 BRC-20에 가장 적합하며 최저 수수료입니다. 기본 설정 -> 선호 Bitcoin ​​주소에서 주소를 조정하십시오.", "headerTitleInfo": "정보", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "내 <1>{{addressType}}</1> 주소입니다.", "invalidChecksumTitle": "비밀 문구를 업그레이드했습니다!", "invalidChecksumFeature1ExportPhrase": "새로운 비밀 문구 내보내기", "invalidChecksumFeature1ExportPhraseDescription": "이전 계정의 비공개 키와 함께 새로운 비밀 문구를 백업하십시오.", "invalidChecksumFeature2FundsAreSafe": "귀하의 자금은 안전하게 보호됩니다", "invalidChecksumFeature2FundsAreSafeDescription": "이 업그레이드는 자동화되었습니다. Phantom의 어느 누구도 귀하의 비밀 문구를 알지 못하며 귀하의 자금에 접근할 수 없습니다.", "invalidChecksumFeature3LearnMore": "자세히 알아보기", "invalidChecksumFeature3LearnMoreDescription": "대부분의 월릿과 호환되지 않는 문구가 있습니다. 자세한 내용은 <1>이 도움말 문서</1>를 참조하십시오.", "invalidChecksumBackUpSecretPhrase": "비밀 문구 백업", "migrationFailureTitle": "계정을 마이그레이션하는 중에 문제가 발생했습니다", "migrationFailureFeature1": "비밀 문구 내보내기", "migrationFailureFeature1Description": "비밀 문구를 백업한 후에 온보딩하십시오.", "migrationFailureFeature2": "Phantom에 온보딩", "migrationFailureFeature2Description": "계정을 보려면 Phantom에 다시 온보딩해야 합니다.", "migrationFailureFeature3": "자세히 알아보기", "migrationFailureFeature3Description": "자세히 알아보려면 <1>이 도움말 문서</1>를 확인하십시오.", "migrationFailureContinueToOnboarding": "온보딩 계속하기", "migrationFailureUnableToFetchMnemonic": "비밀 문구를 로드할 수 없습니다", "migrationFailureUnableToFetchMnemonicDescription": "디버그하려면 지원팀에 문의하고 애플리케이션 로그를 다운로드하십시오", "migrationFailureContactSupport": "지원팀에 문의", "ledgerActionConfirm": "Ledger Nano에서 확인", "ledgerActionErrorBlindSignDisabledPrimaryText": "블라인드 서명 비활성화됨", "ledgerActionErrorBlindSignDisabledSecondaryText": "블라인드 서명이 하드웨어 장치에서 활성화되었는지 확인한 후 작업을 다시 시도하십시오", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "작동 중에 하드웨어 장치 연결이 끊겼습니다", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Phantom 확장 프로그램을 닫은 후 작업을 다시 시도하십시오", "ledgerActionErrorDeviceLockedPrimaryText": "하드웨어 장치 잠김", "ledgerActionErrorDeviceLockedSecondaryText": "하드웨어 장치의 잠금을 해제한 후 작업을 다시 시도하십시오", "ledgerActionErrorHeader": "Ledger 작업 오류", "ledgerActionErrorUserRejectionPrimaryText": "사용자가 거래를 거부함", "ledgerActionErrorUserRejectionSecondaryText": "사용자가 하드웨어 장치에서 작업을 거부했습니다", "ledgerActionNeedPermission": "권한 필요", "ledgerActionNeedToConfirm": "하드웨어 월릿에서 거래를 확인해야 합니다. 해당 월릿이 잠금 해제되어 있고 {{chainType}} 앱에 있는지 확인하십시오.", "ledgerActionNeedToConfirmMany": "하드웨어 월릿에서 {{numberOfTransactions}}개 거래를 확인해야 합니다. 해당 월릿이 잠금 해제되어 있고 {{chainType}} 앱에 있는지 확인하십시오.", "ledgerActionNeedToConfirmBlind": "하드웨어 월릿에서 거래를 확인해야 합니다. 해당 월릿이 잠금 해제되어 있고, {{chainType}} 앱에 있고, 블라인드 서명이 활성화되었는지 확인하십시오.", "ledgerActionNeedToConfirmBlindMany": "하드웨어 월릿에서 {{numberOfTransactions}}개 거래를 확인해야 합니다. 해당 월릿이 잠금 해제되어 있고, {{chainType}} 앱에 있고, 블라인드 서명이 활성화되었는지 확인하십시오.", "ledgerActionPleaseConnect": "Ledger Nano를 연결하십시오", "ledgerActionPleaseConnectAndConfirm": "하드웨어 월릿을 연결하고 해당 월릿이 잠금 해제되어 있는지 확인하십시오. 사용 중인 브라우저에서 권한을 승인했는지 확인하십시오.", "maxInputAmount": "금액", "maxInputMax": "최대", "notEnoughSolPrimaryText": "SOL 부족", "notEnoughSolSecondaryText": "이 거래를 위한 월릿의 SOL이 부족합니다. 추가로 입금한 후 다시 시도하십시오.", "insufficientBalancePrimaryText": "{{tokenSymbol}} 부족", "insufficientBalanceSecondaryText": "이 거래를 위한 {{tokenSymbol}}이(가) 월릿에 충분하지 않습니다.", "insufficientBalanceRemaining": "남은 잔액", "insufficientBalanceRequired": "필요한 잔액", "notEnoughSplTokensTitle": "토큰 부족", "notEnoughSplTokensDescription": "이 거래를 위한 토큰이 월릿에 충분하지 않습니다. 제출하면 이 거래는 되돌아갑니다.", "transactionExpiredPrimaryText": "거래 만료됨", "transactionExpiredSecondaryText": "너무 오래 대기하여 거래를 확인할 수 없으며 거래가 만료되었습니다. 제출하면 이 거래는 되돌아갑니다.", "transactionHasWarning": "거래 경고", "tokens": "토큰", "notificationApplicationApprovalPermissionsAddressVerification": "이 주소를 소유하고 있는지 확인", "notificationApplicationApprovalPermissionsTransactionApproval": "거래 승인 요청", "notificationApplicationApprovalPermissionsViewWalletActivity": "월릿 잔액 및 내역 확인", "notificationApplicationApprovalParagraphText": "확인하면 선택한 계정의 잔액과 활동을 이 사이트에서 확인할 수 있습니다.", "notificationApplicationApprovalActionButtonConnect": "연결", "notificationApplicationApprovalActionButtonSignIn": "로그인", "notificationApplicationApprovalAllowApproval": "사이트에서 연결하도록 허용하시겠습니까?", "notificationApplicationApprovalAutoConfirm": "거래 자동 확인", "notificationApplicationApprovalConnectDisclaimer": "신뢰하는 웹사이트만 연결", "notificationApplicationApprovalSignInDisclaimer": "신뢰하는 웹사이트에만 로그인", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "이 웹사이트는 사용하기에 안전하지 않으며 귀하의 자금을 훔치려고 시도할 수 있습니다.", "notificationApplicationApprovalConnectUnknownApp": "알 수 없음", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "앱에 연결할 수 없음", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "이 앱은 {{appNetworkName}}에 연결을 시도하고 있지만 {{phantomNetworkName}}이(가) 선택되었습니다.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "{{networkName}}을(를) 사용하려면 개발자 설정 → Testnet 모드로 이동하십시오.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "알 수 없는 네트워크", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "다른 모바일 앱에 연결은 Ledger에서 현재 지원되지 않습니다.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "비 Ledger 계정으로 전환하거나 인앱 브라우저를 사용하여 다시 시도하십시오.", "notificationSignatureRequestConfirmTransaction": "거래 확인", "notificationSignatureRequestConfirmTransactionCapitalized": "거래 확인", "notificationSignatureRequestConfirmTransactions": "거래 확인", "notificationSignatureRequestConfirmTransactionsCapitalized": "거래 확인", "notificationSignatureRequestSignatureRequest": "서명 요청", "notificationMessageHeader": "메시지", "notificationMessageCopied": "메시지 복사됨", "notificationAutoConfirm": "자동 확인", "notificationAutoConfirmOff": "끄기", "notificationAutoConfirmOn": "켜기", "notificationConfirmFooter": "이 웹사이트를 신뢰하는 경우에만 확인하십시오.", "notificationEstimatedTime": "예상 시간", "notificationPermissionRequestText": "이것은 권한 요청일 뿐입니다. 거래가 즉시 실행되지 않을 수도 있습니다.", "notificationBalanceChangesText": "잔액 변화는 추정치입니다. 관련 금액과 자산은 보장되지 않습니다.", "notificationContractAddress": "계약 주소", "notificationAdvancedDetailsText": "고급", "notificationUnableToSimulateWarningText": "잔액 변화를 현재 추정할 수 없습니다. 나중에 다시 시도하거나 이 사이트를 신뢰할 수 있는지 확인하십시오.", "notificationSignMessageParagraphText": "이 메시지에 서명하면 선택한 계정의 소유권이 있음을 증명합니다.", "notificationSignatureRequestScanFailedDescription": "보안 문제가 있는지 확인하기 위해 메시지를 스캔할 수 없습니다. 조심하면서 계속하십시오.", "notificationFailedToScan": "이 요청의 결과를 시뮬레이션하지 못했습니다.\n확인은 안전하지 않으며 손실로 이어질 수 있습니다.", "notificationScanLoading": "스캔 요청", "notificationTransactionApprovalActionButtonConfirm": "확인", "notificationTransactionApprovalActionButtonBack": "뒤로", "notificationTransactionApprovalEstimatedChanges": "예상 변화", "notificationTransactionApprovalEstimatesBasedOnSimulations": "예상치는 거래 시뮬레이션을 기반으로 측정되었으며 확실한 결과를 보장하지 않습니다", "notificationTransactionApprovalHideAdvancedDetails": "고급 거래 세부 정보 숨기기", "notificationTransactionApprovalNetworkFee": "네트워크 수수료", "notificationTransactionApprovalNetwork": "네트워크", "notificationTransactionApprovalEstimatedTime": "예상 시간", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "자산 소유권에 영향을 미치는 변화가 없습니다", "notificationTransactionApprovalSolanaAmountRequired": "거래 진행을 위해 Solana 네트워크에서 요구한 금액", "notificationTransactionApprovalUnableToSimulate": "시뮬레이션할 수 없습니다. 승인하면 자금 손실이 일어날 수 있으므로 이 웹사이트를 신뢰할 수 있는지 확인하십시오.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "잔액 변경 내역을 가져올 수 없습니다", "notificationTransactionApprovalViewAdvancedDetails": "고급 거래 세부 정보 표시", "notificationTransactionApprovalKnownMalicious": "이 거래는 악성입니다. 서명하면 자금 손실이 발생합니다.", "notificationTransactionApprovalSuspectedMalicious": "이 거래는 악성으로 의심됩니다. 승인하면 자금 손실이 발생할 수 있습니다.", "notificationTransactionApprovalNetworkFeeHighWarning": "네트워크 정체로 인해 네트워크 수수료가 증가되었습니다.", "notificationTransactionERC20ApprovalDescription": "확인하면 이 앱이 아래 한도까지 언제든지 잔액에 액세스할 수 있습니다.", "notificationTransactionERC20ApprovalContractAddress": "계약 주소", "notificationTransactionERC20Unlimited": "무제한", "notificationTransactionERC20ApprovalTitle": "{{tokenSymbol}} 지출 승인", "notificationTransactionERC20RevokeTitle": "{{tokenSymbol}} 지출 취소", "notificationTransactionERC721RevokeTitle": "{{tokenSymbol}} 액세스 취소", "notificationTransactionERC20ApprovalAll": "모든 {{tokenSymbol}}", "notificationIncorrectModeTitle": "잘못된 모드", "notificationIncorrectModeInTestnetTitle": "Testnet 모드에 있습니다", "notificationIncorrectModeNotInTestnetTitle": "Testnet 모드에 있지 않습니다", "notificationIncorrectModeInTestnetDescription": "{{origin}}이(가) mainnet을 사용하려고 하지만, 사용자는 Testnet 모드에 있습니다.", "notificationIncorrectModeNotInTestnetDescription": "{{origin}}이(가) Testnet을 사용하려고 하지만, 사용자는 Testnet 모드에 있지 않습니다.", "notificationIncorrectModeInTestnetProceed": "계속하려면 Testnet 모드를 끄십시오.", "notificationIncorrectModeNotInTestnetProceed": "계속하려면 Testnet 모드를 켜십시오.", "notificationIncorrectEIP712ChainId": "현재 연결된 네트워크용이 아닌 메시지에 서명하지 못하도록 방지했습니다.", "notificationIncorrectEIP712ChainIdDescription": "요청된 메시지는 {{messageChainId}}이고, 사용자는 {{connectedChainId}}에 연결되어 있습니다.", "notificationUnsupportedNetwork": "지원되지 않는 네트워크", "notificationUnsupportedNetworkDescription": "이 웹사이트는 Phantom이 현재 지원하지 않는 네트워크를 사용하려고 합니다.", "notificationUnsupportedNetworkDescriptionInterpolated": "다른 확장 프로그램으로 진행하려면 <1>설정 → 기본 앱 월릿을 끄고 항상 확인을 선택</1>하십시오. 그런 다음 페이지를 새로 고치고 다시 연결하십시오.", "notificationUnsupportedAccount": "지원되지 않는 계정", "notificationUnsupportedAccountDescription": "해당 웹사이트는 이 {{chainType}} 계정이 지원하지 않는 {{targetChainType}}을(를) 사용하려고 합니다.", "notificationUnsupportedAccountDescription2": "호환되는 시드 문구 또는 비공개 키에서 계정으로 전환하고 다시 시도하십시오.", "notificationInvalidTransaction": "잘못된 거래", "notificationInvalidTransactionDescription": "이 앱에서 받은 거래는 잘못된 형식이므로 제출하지 마십시오. 앱 개발자에게 문의하여 이 문제를 보고하십시오.", "notificationCopyTransactionText": "거래 복사", "notificationTransactionCopied": "거래 복사됨", "onboardingImportOptionsPageTitle": "월릿 가져오기", "onboardingImportOptionsPageSubtitle": "비밀 문구, 비공개 키 또는 하드웨어 지갑을 사용하여 기존 지갑을 가져옵니다.", "onboardingImportPrivateKeyPageTitle": "비공개 키 가져오기", "onboardingImportPrivateKeyPageSubtitle": "기존 싱글 체인 월릿 가져오기", "onboardingCreatePassword": "비밀번호 생성", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<1>서비스 약관</1>에 동의합니다", "onboardingCreatePasswordConfirmPasswordPlaceholder": "비밀번호 확인", "onboardingCreatePasswordDescription": "월릿을 잠금 해제하는 데 사용됩니다.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "잘못된 복구용 비밀 문구", "onboardingCreatePasswordPasswordPlaceholder": "비밀번호", "onboardingCreatePasswordPasswordStrengthWeak": "약함", "onboardingCreatePasswordPasswordStrengthMedium": "중간", "onboardingCreatePasswordPasswordStrengthStrong": "강함", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "내 복구용 비밀 문구를 저장했습니다", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "복구용 비밀 문구", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "이 문구는 월릿을 복구하는 유일한 방법입니다. 어느 누구와도 공유하지 마십시오!", "onboardingImportWallet": "월릿 가져오기", "onboardingImportWalletImportExistingWallet": "12개 또는 24개 단어의 복구용 비밀 문구로 기존 월릿을 가져옵니다.", "onboardingImportWalletRestoreWallet": "월릿 복원", "onboardingImportWalletSecretRecoveryPhrase": "복구용 비밀 문구", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "잘못된 복구용 비밀 문구", "onboardingImportWalletIHaveWords": "{{numWords}}개 단어의 복구 문구가 있습니다", "onboardingImportWalletIncorrectOrMisspelledWord": "{{wordIndex}} 단어가 올바르지 않거나 철자가 잘못되었습니다", "onboardingImportWalletIncorrectOrMisspelledWords": "{{wordIndexes}} 단어가 올바르지 않거나 철자가 잘못되었습니다", "onboardingImportWalletScrollDown": "아래로 스크롤", "onboardingImportWalletScrollUp": "위로 스크롤", "onboardingSelectAccountsImportAccounts": "계정 가져오기", "onboardingSelectAccountsImportAccountsDescription": "가져올 월릿 계정을 선택합니다.", "onboardingSelectAccountsImportSelectedAccounts": "선택한 계정 가져오기", "onboardingSelectAccountsFindMoreAccounts": "계정 더 찾기", "onboardingSelectAccountsFindMoreNoneFound": "계정을 찾을 수 없습니다", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}}개 계정 선택됨", "onboardingSelectAccountSelectAllText": "모두 선택", "onboardingAdditionalPermissionsTitle": "Phantom에서 앱 사용", "onboardingAdditionalPermissionsSubtitle": "가장 원활한 앱 경험을 위해 Phantom이 모든 사이트에서 데이터를 읽고 변경할 수 있도록 허용하는 것이 좋습니다.", "interstitialAdditionalPermissionsTitle": "Phantom에서 앱 사용", "interstitialAdditionalPermissionsSubtitle": "중단 없이 앱을 계속 사용하려면 Phantom이 모든 사이트에서 데이터를 읽고 변경하도록 허용하는 것이 좋습니다.", "recentActivityPrimaryText": "최근 활동", "removeAccountActionButtonRemove": "제거", "removeAccountRemoveWallet": "계정 제거", "removeAccountInterpolated": "{{accountName}} 제거", "removeAccountWarningLedger": "Phantom에서 이 월릿을 제거해도 '하드웨어 월릿 연결' 프로세스를 사용하여 다시 추가할 수 있습니다.", "removeAccountWarningSeedVault": "Phantom에서 이 월릿을 제거해도 '시드 볼트 월릿 연결' 프로세스를 사용하여 다시 추가할 수 있습니다.", "removeAccountWarningPrivateKey": "이 월릿을 제거하면 Phantom이 대신 복구해 드릴 수 없습니다. 비공개 키를 백업했는지 확인하십시오.", "removeAccountWarningSeed": "이 월릿을 Phantom에서 제거해도 이 월릿이나 다른 월릿에서 니모닉을 사용하여 다시 가져올 수 있습니다.", "removeAccountWarningReadOnly": "조회 전용 월릿이므로 이 계정을 삭제해도 월릿에 영향을 미치지 않습니다.", "removeSeedPrimaryText": "비밀 문구 {{number}} 제거", "removeSeedSecondaryText": "이 작업을 실행하면 비밀 문구 {{number}}의 기존 계정이 모두 제거됩니다. 기존 비밀 문구를 저장했는지 확인하십시오.", "resetSeedPrimaryText": "새 비밀 문구로 앱 재설정", "resetSeedSecondaryText": "이 작업을 실행하면 기존 계정이 모두 제거되고 새 계정으로 교체됩니다. 기존 비밀 문구와 비공개 키를 백업했는지 확인하십시오.", "resetAppPrimaryText": "앱 재설정 및 초기화", "resetAppSecondaryText": "이렇게 하면 기존 계정 및 데이터가 모두 제거됩니다. 비밀 문구와 비공개 키를 백업했는지 확인하십시오.", "richTransactionsDays": "일", "richTransactionsToday": "오늘", "richTransactionsYesterday": "어제", "richTransactionDetailAccount": "계정", "richTransactionDetailAppInteraction": "앱 상호 작용", "richTransactionDetailAt": "-", "richTransactionDetailBid": "입찰", "richTransactionDetailBidDetails": "입찰 세부 정보", "richTransactionDetailBought": "구입함", "richTransactionDetailBurned": "소각됨", "richTransactionDetailCancelBid": "입찰 취소", "richTransactionDetailCompleted": "완료", "richTransactionDetailConfirmed": "확인 완료", "richTransactionDetailDate": "날짜", "richTransactionDetailFailed": "실패", "richTransactionDetailFrom": "보내는 곳", "richTransactionDetailItem": "항목", "richTransactionDetailListed": "상장됨", "richTransactionDetailListingDetails": "상장 세부 정보", "richTransactionDetailListingPrice": "정가", "richTransactionDetailMarketplace": "마켓플레이스", "richTransactionDetailNetworkFee": "네트워크 수수료", "richTransactionDetailOriginalListingPrice": "원래 정가", "richTransactionDetailPending": "보류 중", "richTransactionDetailPrice": "가격", "richTransactionDetailProvider": "제공자", "richTransactionDetailPurchaseDetails": "구매 세부 정보", "richTransactionDetailRebate": "리베이트", "richTransactionDetailReceived": "수령 완료", "richTransactionDetailSaleDetails": "판매 세부 정보", "richTransactionDetailSent": "전송 완료", "richTransactionDetailSold": "판매함", "richTransactionDetailStaked": "스테이킹 완료", "richTransactionDetailStatus": "상태", "richTransactionDetailSwap": "스왑", "richTransactionDetailSwapDetails": "스왑 세부 정보", "richTransactionDetailTo": "받는 곳", "richTransactionDetailTokenSwap": "토큰 스왑", "richTransactionDetailUnknownNFT": "알 수 없는 NFT", "richTransactionDetailUnlisted": "상장 취소됨", "richTransactionDetailUnstaked": "스테이킹 해제 완료", "richTransactionDetailValidator": "검증자", "richTransactionDetailViewOnExplorer": "{{explorer}}에서 보기", "richTransactionDetailWithdrawStake": "스테이크 인출", "richTransactionDetailYouPaid": "지불함", "richTransactionDetailYouReceived": "수령함", "richTransactionDetailUnwrapDetails": "래핑 해제 세부 정보", "richTransactionDetailTokenUnwrap": "토큰 래핑 해제", "activityItemsRefreshFailed": "최신 거래를 로드하지 못했습니다.", "activityItemsPagingFailed": "이전 거래를 로드하지 못했습니다.", "activityItemsTestnetNotAvailable": "Testnet 거래 내역을 현재 사용할 수 없습니다", "historyUnknownDappName": "알 수 없음", "historyStatusSucceeded": "성공", "historyNetwork": "네트워크", "historyAttemptedAmount": "시도한 금액", "historyAmount": "금액", "sendAddressBookButtonLabel": "주소록", "addressBookSelectAddressBook": "주소록", "sendAddressBookNoAddressesSaved": "저장된 주소가 없습니다", "sendAddressBookRecentlyUsed": "최근 사용", "addressBookSelectRecentlyUsed": "최근 사용", "sendConfirmationLabel": "라벨", "sendConfirmationMessage": "메시지", "sendConfirmationNetworkFee": "네트워크 수수료", "sendConfirmationPrimaryText": "전송 확인", "sendWarning_INSUFFICIENT_FUNDS": "자금이 부족하므로, 제출하면 이 거래가 실패할 가능성이 높습니다.", "sendFungibleSummaryNetwork": "네트워크", "sendFungibleSummaryNetworkFee": "네트워크 수수료", "sendFungibleSummaryEstimatedTime": "예상 시간", "sendFungiblePendingEstimatedTime": "시간 예상", "sendFungibleSummaryEstimatedTimeDescription": "Ethereum 거래 속도는 여러 요인에 따라 다릅니다. \"네트워크 수수료\"를 클릭하면 거래 속도가 빨라집니다.", "sendSummaryBitcoinPendingTxTitle": "전송을 제출할 수 없음", "sendSummaryBitcoinPendingTxDescription": "한 번에 하나의 BTC 전송만 보류할 수 있습니다. 새로운 전송을 제출하려면 완료될 때까지 기다려 주십시오.", "sendFungibleSatProtectionTitle": "Sat 보호를 사용하여 보내기", "sendFungibleSatProtectionExplainer": "Phantom은 귀하의 Ordinals 및 BRC20이 거래 수수료 또는 Bitcoin 전송에 사용되지 않도록 보장합니다.", "sendFungibleTransferFee": "토큰 전송 수수료", "sendFungibleTransferFeeToolTip": "이 토큰의 생성자는 각 전송에 대해 수수료를 받습니다. 이는 Phantom이 부과하거나 징수하는 수수료가 아닙니다.", "sendFungibleInterestBearingPercent": "현재 금리", "sendFungibleNonTransferable": "양도 불가", "sendFungibleNonTransferableToolTip": "이 토큰은 다른 계정으로 전송할 수 없습니다.", "sendFungibleNonTransferableYes": "예", "sendStatusErrorMessageInterpolated": "<1>{{uiRecipient}}</1> 님에게 토큰을 전송하는 중에 오류가 발생했습니다", "sendStatusErrorMessageInsufficientBalance": "거래를 완료하기에 잔액이 부족합니다.", "sendStatusErrorTitle": "전송 불가", "sendStatusLoadingTitle": "전송 중...", "sendStatusSuccessMessageInterpolated": "토큰이 <1>{{uiRecipient}}</1> 님에게 전송되었습니다", "sendStatusSuccessTitle": "전송 완료!", "sendStatusConfirmedSuccessTitle": "전송 완료!", "sendStatusSubmittedSuccessTitle": "거래 제출됨", "sendStatusEstimatedTransactionTime": "예상 거래 시간: {{time}}", "sendStatusViewTransaction": "거래 보기", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> - <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2>이(가) <2>{{uiRecipient}}</2>에게 전송되었습니다", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2>이(가) <2>{{uiRecipient}}</2> 님에게 전송되었습니다", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2>을(를) <2>{{uiRecipient}}</2>에게 전송하지 못했습니다", "sendFungibleSolanaErrorCode": "오류 코드 {{code}}", "sendFormErrorInsufficientBalance": "잔액 부족", "sendFormErrorEmptyAmount": "금액 필요", "sendFormInvalidAddress": "잘못된 {{assetName}} 주소", "sendFormInvalidUsernameOrAddress": "잘못된 사용자 이름 또는 주소", "sendFormErrorInvalidSolanaAddress": "유효하지 않은 Solana 주소", "sendFormErrorInvalidTwitterHandle": "이 Twitter 핸들은 등록되지 않았습니다", "sendFormErrorInvalidDomain": "이 도메인은 등록되지 않았습니다", "sendFormErrorInvalidUsername": "이 사용자 이름은 등록되지 않았습니다", "sendFormErrorMinRequiredInterpolated": "{{tokenName}} 최소 {{minAmount}}개가 필요합니다", "sendRecipientTextareaPlaceholder": "수신자의 SOL 주소", "sendRecipientTextAreaPlaceholder2": "수신자의 {{symbol}} 주소", "sendMemoOptional": "메모(선택사항)", "sendMemo": "메모", "sendOptional": "선택사항", "settings": "설정", "settingsDapps": "dApps", "settingsSelectedAccount": "선택한 계정", "settingsAddressBookNoLabel": "라벨 없음", "settingsAddressBookPrimary": "주소록", "settingsAddressBookRecentlyUsed": "최근 사용", "settingsAddressBookSecondary": "가장 많이 사용하는 주소 관리", "settingsAutoLockTimerPrimary": "자동 잠금 타이머", "settingsAutoLockTimerSecondary": "자동 잠금 타이머 시간 변경", "settingsChangeLanguagePrimary": "언어 변경", "settingsChangeLanguageSecondary": "표시 언어 변경", "settingsChangeNetworkPrimary": "네트워크 변경", "settingsChangeNetworkSecondary": "네트워크 설정 구성", "settingsChangePasswordPrimary": "비밀번호 변경", "settingsChangePasswordSecondary": "잠금 화면 비밀번호 변경", "settingsCompleteBetaSurvey": "베타 설문조사 작성", "settingsDisplayLanguage": "언어 표시", "settingsErrorCannotExportLedgerPrivateKey": "Ledger 비공개 키를 내보낼 수 없습니다", "settingsErrorCannotRemoveAllWallets": "일부 계정을 제거할 수 없습니다", "settingsExportPrivateKey": "비공개 키 표시", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Phantom RPC 네트워크", "settingsTestNetworks": "테스트 네트워크", "settingsUseCustomNetworks": "커스텀 네트워크 사용", "settingsTestnetMode": "Testnet 모드", "settingsTestnetModeDescription": "잔액 및 앱 연결에 적용됩니다.", "settingsWebViewDebugging": "웹 보기 디버깅", "settingsWebViewDebuggingDescription": "인앱 브라우저 웹 보기를 검사하고 디버깅할 수 있습니다.", "settingsTestNetworksInfo": "Testnet 네트워크로 전환은 테스트용으로만 사용됩니다. Testnet Networks의 토큰에는 금전적 가치가 없습니다.", "settingsEmojis": "이모티콘", "settingsNoAddresses": "주소가 없습니다", "settingsAddressBookEmptyHeading": "주소록이 비어 있습니다.", "settingsAddressBookEmptyText": "\"+\" 또는 \"주소 추가\" 버튼을 클릭하여 즐겨찾는 주소를 추가하세요", "settingsEditWallet": "계정 편집", "settingsNoTrustedApps": "신뢰하는 앱이 없습니다", "settingsNoConnections": "아직 연결이 없습니다.", "settingsRemoveWallet": "계정 제거", "settingsResetApp": "앱 재설정", "settingsBlocked": "차단됨", "settingsBlockedAccounts": "차단된 계정", "settingsNoBlockedAccounts": "차단된 계정이 없습니다.", "settingsRemoveSecretPhrase": "비밀 문구 제거", "settingsResetAppWithSecretPhrase": "비밀 문구로 앱 재설정", "settingsResetSecretRecoveryPhrase": "복구용 비밀 문구 재설정", "settingsShowSecretRecoveryPhrase": "복구용 비밀 문구 표시", "settingsShowSecretRecoveryPhraseSecondary": "복구용 문구 표시", "settingsShowSecretRecoveryPhraseTertiary": "비밀 문구 표시", "settingsTrustedAppsAutoConfirmActiveUntil": "{{formattedTimestamp}}까지", "settingsTrustedAppsAutoConfirm": "자동 확인", "settingsTrustedAppsDisclaimer": "신뢰하는 사이트에서만 자동 확인 활성화", "settingsTrustedAppsLastUsed": "{{formattedTimestamp}} 전에 사용함", "settingsTrustedAppsPrimary": "연결된 앱", "settingsTrustedApps": "신뢰하는 앱", "settingsTrustedAppsRevoke": "취소", "settingsTrustedAppsRevokeToast": "{{trustedApp}} 연결됨", "settingsTrustedAppsSecondary": "신뢰하는 애플리케이션 구성", "settingsTrustedAppsToday": "오늘", "settingsTrustedAppsYesterday": "어제", "settingsTrustedAppsLastWeek": "지난주", "settingsTrustedAppsBeforeYesterday": "더 일찍", "settingsTrustedAppsDisconnectAll": "모두 연결 끊기", "settingsTrustedAppsDisconnectAllToast": "모든 앱의 연결 끊김", "settingsTrustedAppsEndAutoConfirmForAll": "모두에 대한 자동 확인 종료", "settingsTrustedAppsEndAutoConfirmForAllToast": "모든 자동 확인 세션 종료됨", "settingsSecurityPrimary": "보안 및 개인 정보", "settingsSecuritySecondary": "보안 설정 업데이트", "settingsActiveNetworks": "활성 네트워크", "settingsActiveNetworksAll": "모두", "settingsActiveNetworksSolana": "Solana 전용", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana는 기본 네트워크이며 항상 켜져 있습니다.", "settingsDeveloperPrimary": "개발자 설정", "settingsAdvanced": "고급 설정", "settingsTransactions": "거래 설정", "settingsAutoConfirm": "자동 확인 설정", "settingsSecurityAnalyticsPrimary": "익명 분석 공유", "settingsSecurityAnalyticsSecondary": "개선할 수 있도록 활성화", "settingsSecurityAnalyticsHelper": "Phantom은 분석 목적으로 귀하의 개인 정보를 사용하지 않습니다", "settingsSuspiciousCollectiblesPrimary": "의심스러운 콜렉터블 숨기기", "settingsSuspiciousCollectiblesSecondary": "플래그가 지정된 콜렉터블을 숨기려면 전환", "settingsPreferredBitcoinAddress": "선호 Bitcoin 주소", "settingsEnabledAddressesUpdated": "표시되는 주소가 업데이트되었습니다!", "settingsEnabledAddresses": "활성화된 주소", "settingsBitcoinPaymentAddressForApps": "앱의 결제 주소", "settingsBitcoinOrdinalsAddressForApps": "앱의 Ordinals 주소", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "위의 두 주소 유형이 모두 활성화되면 Magic Eden과 같은 특정 앱의 경우 귀하의 Native Segwit 주소가 구매 자금에 사용됩니다. 구매한 자산은 귀하의 Taproot 주소로 전송됩니다.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "호환성을 보장하기 위한 Phantom의 기본 Bitcoin 주소입니다.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(기본값)", "settingsPreferredBitcoinAddressTaprootExplainer": "최신 주소 유형으로, 일반적으로 거래 수수료가 더 저렴합니다.", "settingsPreferredExplorers": "선호 탐색기", "settingsPreferredExplorersSecondary": "선호하는 블록체인 탐색기로 변경", "settingsCustomGasControls": "커스텀 가스 컨트롤", "settingsSupportDesk": "지원 데스크", "settingsSubmitATicket": "티켓 제출", "settingsAttachApplicationLogs": "애플리케이션 로그 첨부", "settingsDownloadApplicationLogs": "앱 로그 다운로드", "settingsDownloadApplicationLogsShort": "로그 다운로드", "settingsDownloadApplicationLogsHelper": "Phantom 지원 문제를 해결하는 데 도움이 되는 로컬 데이터, 충돌 보고서, 공개 월릿 주소가 포함되어 있습니다.", "settingsDownloadApplicationLogsWarning": "시드 문구나 비공개 키 같은 중요한 데이터는 포함되지 않습니다.", "settingsWallet": "월릿", "settingsPreferences": "기본 설정", "settingsSecurity": "보안", "settingsDeveloper": "개발자", "settingsSupport": "지원", "settingsWalletShortcutsPrimary": "월릿 단축키 표시", "settingsAppIcon": "앱 아이콘", "settingsAppIconDefault": "기본값", "settingsAppIconLight": "라이트", "settingsAppIconDark": "다크", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "계정", "settingsSearchResultSelected": "선택됨", "settingsSearchResultExport": "내보내기", "settingsSearchResultSeed": "시드", "settingsSearchResultTrusted": "인증됨", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "상태", "settingsSearchResultLogs": "로그", "settingsSearchResultBiometric": "생체 인식", "settingsSearchResultTouch": "터치", "settingsSearchResultFace": "페이스", "settingsSearchResultShortcuts": "단축키", "settingsAllSitesPermissionsTitle": "모든 사이트에서 Phantom에 액세스", "settingsAllSitesPermissionsSubtitle": "확장 프로그램을 클릭하지 않고 Phantom에서 앱을 원활하게 사용할 수 있습니다", "settingsAllSitesPermissionsDisabled": "해당 브라우저는 이 설정 변경을 지원하지 않습니다", "settingsSolanaCopyTransaction": "거래 복사 활성화", "settingsSolanaCopyTransactionDetails": "직렬화된 거래 데이터를 클립보드에 복사", "settingsAutoConfirmHeader": "자동 확인", "refreshWebpageToApplyChanges": "웹 페이지를 새로 고쳐서 변경 사항을 적용하십시오", "settingsExperimentalTitle": "실험적 기능", "settingsExprimentalSolanaActionsSubtitle": "X.com에서 관련 링크가 발견되면 Solana 작업 버튼이 자동으로 확장됩니다", "stakeAccountCardActiveStake": "활성화된 스테이크", "stakeAccountCardBalance": "잔액", "stakeAccountCardRentReserve": "임대 예약", "stakeAccountCardRewards": "마지막 리워드", "stakeAccountCardRewardsTooltip": "가장 최근에 획득한 스테이킹 리워드입니다. 3일마다 리워드를 받습니다.", "stakeAccountCardStakeAccount": "주소", "stakeAccountCardLockup": "락업 기간", "stakeRewardsHistoryTitle": "리워드 내역", "stakeRewardsActivityItemTitle": "리워드", "stakeRewardsHistoryEmptyList": "리워드 없음", "stakeRewardsTime_zero": "오늘", "stakeRewardsTime_one": "어제", "stakeRewardsTime_other": "{{count}}일 전", "stakeRewardsItemsPagingFailed": "이전 리워드를 로드하지 못했습니다.", "stakeAccountCreateAndDelegateErrorStaking": "이 검증자로 스테이킹하는 동안 문제가 발생했습니다. 다시 시도해주십시오.", "stakeAccountCreateAndDelegateSolStaked": "SOL이 스테이킹되었습니다!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "스테이킹 계정이 활성화되면 <1></1> 2~3일 후에 SOL에서 리워드를 획득하기 시작합니다.", "stakeAccountCreateAndDelegateStakingFailed": "스테이킹 실패", "stakeAccountCreateAndDelegateStakingSol": "SOL 스테이킹 중...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "스테이킹 계정을 생성한 다음 SOL을 다음 대상에 위임합니다", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "스테이킹 계정을 생성한 다음 SOL을 {{validatorName}}에 위임합니다", "stakeAccountCreateAndDelegateViewTransaction": "거래 보기", "stakeAccountDeactivateStakeSolUnstaked": "SOL이 스테이킹 해제되었습니다!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "스테이킹 계정이 비활성화되면 <1></1> 2~3일 후에 스테이킹을 인출할 수 있습니다.", "stakeAccountDeactivateStakeSolUnstakedDescription": "스테이킹 계정이 비활성화되면 2~3일 후에 스테이킹을 인출할 수 있습니다.", "stakeAccountDeactivateStakeUnstakingFailed": "스테이킹 해제 실패", "stakeAccountDeactivateStakeUnstakingFailedDescription": "이 검증자로 스테이킹 해제하는 동안 문제가 발생했습니다. 다시 시도해주십시오.", "stakeAccountDeactivateStakeUnstakingSol": "SOL 스테이킹 해제 중...", "stakeAccountDeactivateStakeUnstakingSolDescription": "SOL 스테이킹 해제 프로세스를 시작합니다.", "stakeAccountDeactivateStakeViewTransaction": "거래 보기", "stakeAccountDelegateStakeSolStaked": "SOL이 스테이킹되었습니다!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "스테이킹 계정이 활성화되면 <1></1> 2~3일 후에 SOL에서 리워드를 획득하기 시작합니다.", "stakeAccountDelegateStakeStakingFailed": "스테이킹 실패", "stakeAccountDelegateStakeStakingFailedDescription": "이 검증자로 스테이킹하는 동안 문제가 발생했습니다. 다시 시도해주십시오.", "stakeAccountDelegateStakeStakingSol": "SOL 스테이킹 중...", "stakeAccountDelegateStakeStakingSolDescription": "SOL을 위임 중입니다.", "stakeAccountDelegateStakeViewTransaction": "거래 보기", "stakeAccountListActivationActivating": "활성화 중", "stakeAccountListActivationActive": "활성화", "stakeAccountListActivationInactive": "비활성화", "stakeAccountListActivationDeactivating": "비활성화 중", "stakeAccountListErrorFetching": "스테이킹 계정을 가져올 수 없습니다. 나중에 다시 시도하십시오.", "stakeAccountListNoStakingAccounts": "스테이크 계정 없음", "stakeAccountListReload": "다시 로드", "stakeAccountListViewPrimaryText": "내 스테이크", "stakeAccountListViewStakeSOL": "SOL 스테이킹", "stakeAccountListItemStakeFee": "{{fee}} 수수료", "stakeAccountViewActionButtonRestake": "다시 스테이킹", "stakeAccountViewActionButtonUnstake": "스테이킹 해제", "stakeAccountViewError": "오류", "stakeAccountViewPrimaryText": "내 스테이크", "stakeAccountViewRestake": "다시 스테이킹", "stakeAccountViewSOLCurrentlyStakedInterpolated": "귀하의 SOL은 현재 검증자로 스테이킹되어 있습니다. 이 자금에 액세스하려면 <1></1>스테이킹을 해제해야 합니다. <3>자세히 알아보기</3>", "stakeAccountViewStakeInactive": {"part1": "이 스테이크 계정은 비활성화되어 있습니다. 해당 계정의 스테이크를 인출하거나 위임할 검증자를 찾으십시오.", "part2": "자세히 알아보기"}, "stakeAccountViewStakeNotFound": "이 스테이크 계정을 찾을 수 없습니다.", "stakeAccountViewViewOnExplorer": "{{explorer}}에서 보기", "stakeAccountViewWithdrawStake": "스테이크 인출", "stakeAccountViewWithdrawUnstakedSOL": "스테이킹 해제된 SOL 인출", "stakeAccountInsufficientFunds": "스테이킹 해제하거나 인출할 수 있는 SOL이 충분하지 않습니다.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL 인출 완료!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL이 인출되었습니다.", "part2": "이 스테이크 계정은 몇 분 후 자동으로 제거됩니다."}, "stakeAccountWithdrawStakeViewTransaction": "거래 보기", "stakeAccountWithdrawStakeWithdrawalFailed": "인출 실패", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "이 스테이크 계정에서 인출하는 동안 문제가 발생했습니다. 다시 시도해주십시오.", "stakeAccountWithdrawStakeWithdrawingSol": "SOL 인출 중...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "이 스테이크 계정에서는 SOL을 인출할 수 없습니다.", "startEarningSolAccount": "계정", "startEarningSolAccounts": "계정", "startEarningSolErrorClosePhantom": "여기를 탭하고 다시 시도", "startEarningSolErrorTroubleLoading": "스테이킹 로드 중 문제 발생", "startEarningSolLoading": "로드 중...", "startEarningSolPrimaryText": "SOL 획득 시작", "startEarningSolSearching": "스테이킹 계정 검색 중", "startEarningSolStakeTokens": "토큰 스테이킹 및 리워드 획득", "startEarningSolYourStake": "내 스테이크", "unwrapFungibleTitle": "{{tokenSymbol}}(으)로 스왑", "unwrapFungibleDescription": "{{toToken}}용 {{fromToken}}에서 인출", "unwrapFungibleConfirmSwap": "스왑 확인", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "예상 수수료", "swapFeesFees": "수수료", "swapFeesPhantomFee": "Phantom 수수료", "swapFeesPhantomFeeDisclaimer": "당사는 항상 최고의 유동성 제공자로부터 최상의 가격을 찾습니다. {{feePercentage}} 수수료가 이 견적에 자동으로 포함됩니다.", "swapFeesRate": "가격", "swapFeesRateDisclaimer": "여러 탈중앙화 거래소에서 Jupiter Aggregator가 찾은 최상의 요금입니다.", "swapFeesRateDisclaimerMultichain": "여러 탈중앙화 거래소에서 찾은 최상의 요금입니다.", "swapFeesPriceImpact": "가격 영향", "swapFeesHighPriceImpact": "높은 가격 영향", "swapFeesPriceImpactDisclaimer": "거래 규모에 따른 시장 가격과 예상 가격의 차이입니다.", "swapFeesSlippage": "슬리피지", "swapFeesHighSlippage": "높은 슬리피지 한도", "swapFeesHighSlippageDisclaimer": "가격이 {{slippage}}%보다 더 떨어지면 거래가 실패합니다.", "swapTransferFee": "전송 수수료", "swapTransferFeeDisclaimer": "${{symbol}} 거래에는 Phantom이 아닌 토큰 생성자가 설정한 {{feePercent}}% 전송 수수료가 발생합니다.", "swapTransferFeeDisclaimerMany": "선택한 토큰 거래에는 Phantom이 아닌 토큰 생성자가 설정한 {{feePercent}}% 전송 수수료가 발생합니다.", "swapFeesSlippageDisclaimer": "거래 가격이 제공 견적에서 벗어날 수 있는 금액입니다.", "swapFeesProvider": "제공자", "swapFeesProviderDisclaimer": "거래를 완료하는 데 사용되는 탈중앙화 거래소.", "swapEstimatedTime": "예상 시간", "swapEstimatedTimeShort": "예상 시간", "swapEstimatedTimeDisclaimer": "브리지의 예상 완료 시간은 거래 속도에 영향을 미치는 여러 요소에 따라 다릅니다.", "swapSettingsButtonCommand": "스왑 설정 열기", "swapQuestionRetry": "다시 시도?", "swapUnverifiedTokens": "확인되지 않은 토큰", "swapSectionTitleTokens": "{{section}} 토큰", "swapFlowYouPay": "지불", "swapFlowYouReceive": "수령", "swapFlowActionButtonText": "주문 검토", "swapAssetCardTokenNetwork": "{{network}}의 {{symbol}}", "swapAssetCardMaxButton": "최대", "swapAssetCardSelectTokenAndNetwork": "토큰 및 네트워크 선택", "swapAssetCardBuyTitle": "수령", "swapAssetCardSellTitle": "지불", "swapAssetWarningUnverified": "이 토큰은 확인되지 않았습니다. 신뢰할 수 있는 토큰과만 상호 작용하십시오.", "swapAssetWarningPermanentDelegate": "대리인은 이러한 토큰을 영구적으로 소각하거나 전송할 수 있습니다.", "swapSlippageSettingsTitle": "슬리피지 설정", "swapSlippageSettingsSubtitle": "가격이 슬리피지를 초과하여 변동되면 거래가 실패합니다. 값이 너무 높으면 불리한 거래가 발생합니다.", "swapSlippageSettingsCustom": "커스텀", "swapSlippageSettingsHighSlippageWarning": "해당 거래는 프론트러닝되어 불리한 거래가 발생할 수 있습니다.", "swapSlippageSettingsCustomMinError": "{{minSlippage}}%보다 큰 값을 입력하십시오.", "swapSlippageSettingsCustomMaxError": "{{maxSlippage}}%보다 작은 값을 입력하십시오.", "swapSlippageSettingsCustomInvalidValue": "올바른 값을 입력하십시오.", "swapSlippageSettingsAutoSubtitle": "Phantom은 성공적인 스왑을 위해 최저 슬리피지를 찾습니다.", "swapSlippageSettingsAuto": "자동", "swapSlippageSettingsFixed": "고정", "swapSlippageOptInTitle": "자동 슬리피지", "swapSlippageOptInSubtitle": "Phantom은 성공적인 스왑을 위해 최저 슬리피지 찾습니다. Swapper → 슬리피지 설정에서 언제든지 변경할 수 있습니다.", "swapSlippageOptInEnableOption": "자동 슬리피지 활성화", "swapSlippageOptInRejectOption": "고정 슬리피지로 계속하기", "swapQuoteFeeDisclaimer": "견적에는 {{feePercentage}} Phantom 수수료가 포함됩니다", "swapQuoteMissingContext": "스왑 견적 컨텍스트 누락", "swapQuoteErrorNoQuotes": "견적 없이 스왑 시도 중", "swapQuoteSolanaNetwork": "Solana 네트워크", "swapQuoteNetwork": "네트워크", "swapQuoteOneTimeSerumAccount": "1회용 Serum 계정", "swapQuoteOneTimeTokenAccount": "1회용 토큰 계정", "swapQuoteBridgeFee": "크로스 체인 스왑 수수료", "swapQuoteDestinationNetwork": "대상 네트워크", "swapQuoteLiquidityProvider": "유동성 제공자", "swapReviewFlowActionButtonPrimary": "스왑", "swapReviewFlowPrimaryText": "주문 검토", "swapReviewFlowYouPay": "지불", "swapReviewFlowYouReceive": "수령", "swapReviewInsufficientBalance": "자금 부족", "ugcSwapWarningTitle": "경고", "ugcSwapWarningBody1": "이 토큰은 {{programName}} 토큰 런처에서 거래됩니다.", "ugcSwapWarningBody2": "이러한 토큰의 가치는 변동성이 심하므로, 상당한 금전적 이익이나 손실을 초래할 수 있습니다. 고객님의 책임하에 거래하시기 바랍니다.", "ugcSwapWarningConfirm": "이해함", "bondingCurveProgressLabel": "Bonding Curve 진행률", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Bonding Curve 모델에서 토큰 가격은 곡선의 모양에 따라 결정됩니다. 더 많은 토큰을 구매하면 증가하고, 토큰이 판매되면 감소합니다. 토큰이 품절되면 모든 유동성은 Raydium에 예치되고 소각됩니다.", "ugcFungibleWarningBanner": "이 토큰은 {{programName}}에서 거래되고 있습니다", "ugcCreatedRowLabel": "생성 날짜", "ugcStatusRowLabel": "상태", "ugcStatusRowValue": "졸업", "swapTxConfirmationReceived": "수령 완료!", "swapTxConfirmationSwapFailed": "스왑 실패", "swapTxConfirmationSwapFailedStaleQuota": "견적이 더 이상 유효하지 않습니다. 다시 시도해 주십시오.", "swapTxConfirmationSwapFailedSlippageLimit": "이 스왑에는 슬리피지가 너무 낮습니다. 스왑 화면 상단에서 슬리피지를 늘린 다음 다시 시도하십시오.", "swapTxConfirmationSwapFailedInsufficientBalance": "요청을 완료할 수 없습니다. 거래를 완료하기에 잔액이 충분하지 않습니다.", "swapTxConfirmationSwapFailedEmptyRoute": "이 토큰 쌍의 유동성이 변경되었습니다. 적합한 견적을 찾을 수 없습니다. 다시 시도하거나 토큰 금액을 조정하십시오.", "swapTxConfirmationSwapFailedAcountFrozen": "이 토큰은 생성자에 의해 동결되었습니다. 이 토큰을 보내거나 스왑할 수 없습니다.", "swapTxConfirmationSwapFailedTryAgain": "스왑이 실패했습니다. 다시 시도해주십시오", "swapTxConfirmationSwapFailedUnknownError": "스왑을 완료할 수 없습니다. 귀하의 자금은 영향을 받지 않습니다. 다시 시도하십시오. ", "swapTxConfirmationSwapFailedSimulationTimeout": "스왑을 시뮬레이션할 수 없습니다. 귀하의 자금은 영향을 받지 않습니다. 다시 시도하십시오.", "swapTxConfirmationSwapFailedSimulationUnknownError": "스왑을 완료할 수 없습니다. 귀하의 자금은 영향을 받지 않았습니다. 다시 시도하십시오. ", "swapTxConfirmationSwapFailedInsufficientGas": "계정의 자금 부족으로 인해 거래를 완료할 수 없습니다. 계정에 자금을 추가하고 다시 시도하십시오.", "swapTxConfirmationSwapFailedLedgerReject": "하드웨어 장치에서 사용자가 스왑을 거부했습니다.", "swapTxConfirmationSwapFailedLedgerConnectionError": "장치 연결 오류로 인해 스왑이 거절되었습니다. 다시 시도하십시오.", "swapTxConfirmationSwapFailedLedgerSignError": "장치 서명 오류로 인해 스왑이 거절되었습니다. 다시 시도하십시오.", "swapTxConfirmationSwapFailedLedgerError": "장치 오류로 인해 스왑이 거절되었습니다. 다시 시도하십시오.", "swapTxConfirmationSwappingTokens": "토큰 스왑 중...", "swapTxConfirmationTokens": "토큰", "swapTxConfirmationTokensDeposited": "완료되었습니다. 토큰이 월릿에 입금되었습니다", "swapTxConfirmationTokensDepositedTitle": "완료되었습니다!", "swapTxConfirmationTokensDepositedBody": "토큰이 월릿에 입금되었습니다", "swapTxConfirmationTokensWillBeDeposited": "거래가 완료되면 월릿에 입금됩니다", "swapTxConfirmationViewTransaction": "거래 보기", "swapTxBridgeSubmitting": "거래 제출 중", "swapTxBridgeSubmittingDescription": "{{sellNetwork}}의 {{sellAmount}}을(를) {{buyNetwork}}의 {{buyAmount}}(으)로 스왑합니다", "swapTxBridgeFailed": "거래 제출 실패", "swapTxBridgeFailedDescription": "요청을 완료할 수 없습니다.", "swapTxBridgeSubmitted": "거래 제출됨", "swapTxBridgeSubmittedDescription": "예상 거래 시간: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "이 창을 안전하게 닫을 수 있습니다.", "swapperSwitchTokens": "토큰 전환", "swapperMax": "최대", "swapperTooltipNetwork": "네트워크", "swapperTooltipPrice": "가격", "swapperTooltipAddress": "계약", "swapperTrendingSortBy": "정렬 기준", "swapperTrendingTimeFrame": "기간", "swapperTrendingNetwork": "네트워크", "swapperTrendingRank": "순위", "swapperTrendingTokens": "트렌딩 토큰", "swapperTrendingVolume": "볼륨", "swapperTrendingPrice": "가격", "swapperTrendingPriceChange": "가격 변경", "swapperTrendingMarketCap": "시가총액", "swapperTrendingTimeFrame1h": "1시간", "swapperTrendingTimeFrame24h": "24시간", "swapperTrendingTimeFrame7d": "7일", "swapperTrendingTimeFrame30d": "30일", "swapperTrendingNoTokensFound": "토큰을 찾을 수 없습니다.", "switchToggle": "전환", "termsOfServiceActionButtonAgree": "동의합니다", "termsOfServiceDisclaimerFeesDisabledInterpolated": "<1>'동의합니다'</1>를 클릭하면 Phantom으로 토큰 스왑 시 적용되는 <3>이용약관</3>에 동의하게 됩니다.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "서비스 약관이 변경되었습니다. <1>'동의합니다'</1>를 클릭하면 당사의 새로운 <3>서비스 약관</3>에 동의하게 됩니다.<5></5><6></6>새로운 서비스 약관에는 특정 제품에 적용되는 새로운 <8>수수료 구조</8>가 포함됩니다.", "termsOfServicePrimaryText": "서비스 약관", "tokenRowUnknownToken": "알 수 없는 토큰", "transactionsAppInteraction": "앱 상호 작용", "transactionsFailedAppInteraction": "앱 상호 작용 실패", "transactionsBidOnInterpolated": "{{name}} 입찰", "transactionsBidFailed": "입찰 실패", "transactionsBoughtInterpolated": "{{name}} 구입함", "transactionsBoughtCollectible": "콜렉터블 구입함", "transactionBridgeInitiated": "브리지 시작됨", "transactionBridgeInitiatedFailed": "브리지 시작 실패", "transactionBridgeStatusLink": "LI.FI에서 상태 확인", "transactionsBuyFailed": "구입 실패", "transactionsBurnedSpam": "소각한 스팸", "transactionsBurned": "소각됨", "transactionsUnwrapped": "래핑 해제됨", "transactionsUnwrappedFailed": "래핑 해제 실패", "transactionsCancelBidOnInterpolated": "{{name}} 입찰 취소함", "transactionsCancelBidOnFailed": "입찰 취소 실패", "transactionsError": "오류", "transactionsFailed": "실패", "transactionsSwapped": "스왑함", "transactionsFailedSwap": "스왑 실패", "transactionsFailedBurn": "소각 실패", "transactionsFrom": "송신", "transactionsListedInterpolated": "{{name}} 상장됨", "transactionsListedFailed": "상장 실패", "transactionsNoActivity": "활동 없음", "transactionsReceived": "수령 완료", "transactionsReceivedInterpolated": "{{amount}}개의 SOL 수신", "transactionsSending": "전송 중...", "transactionsPendingCreateListingInterpolated": "{{name}} 생성", "transactionsPendingEditListingInterpolated": "{{name}} 편집", "transactionsPendingSolanaPayTransaction": "Solana Pay 거래 확인 중", "transactionsPendingRemoveListingInterpolated": "{{name}} 상장 취소", "transactionsPendingBurningInterpolated": "{{name}} 소각", "transactionsPendingSending": "전송", "transactionsPendingSwapping": "스왑", "transactionsPendingBridging": "브리징", "transactionsPendingApproving": "승인 중", "transactionsPendingCreatingAndDelegatingStake": "스테이크 생성 및 위임", "transactionsPendingDeactivatingStake": "스테이크 비활성화", "transactionsPendingDelegatingStake": "스테이크 위임", "transactionsPendingWithdrawingStake": "스테이크 인출", "transactionsPendingAppInteraction": "보류 중인 앱 상호 작용", "transactionsPendingBitcoinTransaction": "보류 중인 BTC 거래", "transactionsSent": "전송 완료", "transactionsSendFailed": "전송 실패", "transactionsSwapOn": "{{dappName}}에서 스왑", "transactionsSentInterpolated": "{{amount}}개의 SOL 전송", "transactionsSoldInterpolated": "{{name}} 판매함", "transactionsSoldCollectible": "콜렉터블 판매함", "transactionsSoldFailed": "판매 실패", "transactionsStaked": "스테이킹 완료", "transactionsStakedFailed": "스테이킹 실패", "transactionsSuccess": "성공", "transactionsTo": "수신", "transactionsTokenSwap": "토큰 스왑", "transactionsUnknownAmount": "알 수 없음", "transactionsUnlistedInterpolated": "{{name}} 상장 취소됨", "transactionsUnstaked": "스테이킹 해제 완료", "transactionsUnlistedFailed": "상장 취소 실패", "transactionsDeactivateStake": "스테이킹 비활성화함", "transactionsDeactivateStakeFailed": "스테이킹 비활성화 실패", "transactionsWaitingForConfirmation": "확인 대기 중", "transactionsWithdrawStake": "스테이크 인출", "transactionsWithdrawStakeFailed": "스테이킹 해제 실패", "transactionCancelled": "취소됨", "transactionCancelledFailed": "취소 실패", "transactionApproveToken": "{{tokenSymbol}} 승인함", "transactionApproveTokenFailed": "{{tokenSymbol}} 승인 실패", "transactionApprovalFailed": "승인 실패", "transactionRevokeApproveToken": "{{tokenSymbol}} 취소함", "transactionRevokeApproveTokenFailed": "{{tokenSymbol}} 취소 실패", "transactionRevokeFailed": "취소 실패", "transactionApproveDetailsTitle": "승인 세부 정보", "transactionCancelOrder": "주문 취소", "transactionCancelOrderFailed": "주문 취소 실패", "transactionApproveAppLabel": "앱", "transactionApproveAmountLabel": "금액", "transactionApproveTokenLabel": "토큰", "transactionApproveCollectionLabel": "컬렉션", "transactionApproveAllItems": "모든 항목 승인", "transactionSpendUpTo": "지출 한도", "transactionCancel": "거래 취소", "transactionPrioritizeCancel": "취소 우선 순위 지정", "transactionSpeedUp": "거래 속도 높이기", "transactionCancelHelperText": "원래 거래는 취소되기 전에 완료될 수 있습니다.", "transactionSpeedUplHelperText": "이렇게 하면 네트워크 상태에 따라 거래 속도가 극대화됩니다.", "transactionCancelHelperMobile": "이 거래를 취소하려면 <1>최대 {{amount}}</1>의 비용이 듭니다. 원래 거래는 취소되기 전에 완료될 수 있습니다.", "transactionCancelHelperMobileWithEstimate": "이 거래를 취소하려면 <1>최대 {{amount}}</1>의 비용이 듭니다. 약 {{timeEstimate}} 후 완료됩니다. 원래 거래는 취소되기 전에 완료될 수 있습니다.", "transactionSpeedUpHelperMobile": "이 거래의 속도를 극대화하는 데 드는 비용은 <1>최대 {{amount}}</1>입니다.", "transactionSpeedUpHelperMobileWithEstimate": "이 거래의 속도를 극대화하는 데 드는 비용은 <1>최대 {{amount}}</1>입니다. 약 {{timeEstimate}} 후 완료됩니다.", "transactionEstimatedTime": "예상 시간", "transactionCancelingSend": "보내기 취소 중", "transactionPrioritizingCancel": "취소 우선 순위 지정 중", "transactionCanceling": "취소 중", "transactionReplaceError": "오류가 발생했습니다. 사용자 계정에 수수료가 부과되지 않았습니다. 다시 시도할 수 있습니다.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} 부족", "transactionGasLimitError": "가스 한도를 예상하지 못했습니다.", "transactionGasEstimationError": "가스를 예상하지 못했습니다.", "pendingTransactionCancel": "취소", "pendingTransactionSpeedUp": "속도 높이기", "pendingTransactionStatus": "상태", "pendingTransactionPending": "보류 중", "pendingTransactionPendingInteraction": "보류 중인 상호 작용", "pendingTransactionCancelling": "취소 중", "pendingTransactionDate": "날짜", "pendingTransactionNetworkFee": "네트워크 수수료", "pendingTransactionEstimatedTime": "예상 시간", "pendingTransactionEstimatedTimeHM": "{{hours}}시간 {{minutes}}분", "pendingTransactionEstimatedTimeMS": "{{minutes}}분 {{seconds}}초", "pendingTransactionEstimatedTimeS": "{{seconds}}초", "pendingTransactionsSendingTitle": "{{assetSymbol}} 전송 중", "pendingTransactionsUnknownEstimatedTime": "알 수 없음", "pendingTransactionUnknownApp": "알 수 없는 앱", "permanentDelegateTitle": "위임됨", "permanentDelegateValue": "영구", "permanentDelegateTooltipTitle": "영구 위임", "permanentDelegateTooltipValue": "영구 위임을 통해 다른 계정이 귀하를 대신하여 토큰을 관리할 수 있으며, 여기에는 소각이나 전송이 포함됩니다.", "unlockActionButtonUnlock": "잠금 해제", "unlockEnterPassword": "비밀번호를 입력하십시오", "unlockErrorIncorrectPassword": "잘못된 비밀번호", "unlockErrorSomethingWentWrong": "문제가 발생했습니다. 잠시 후 다시 시도해주십시오", "unlockForgotPassword": "암호 잊음", "unlockPassword": "비밀번호", "forgotPasswordText": "월릿의 12~24개 단어 복구 구문을 입력하여 비밀번호를 재설정할 수 있습니다. Phantom은 사용자의 비밀번호를 복구할 수 없습니다.", "appInfo": "앱 정보", "lastUsed": "마지막 사용", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "하드웨어 계정에서는 사용할 수 없습니다.", "trustedAppAutoConfirmDisclaimer1": "활성화된 동안 Phantom은 사용자에게 알리거나 확인을 요청하지 않고 이 앱의 모든 요청을 확인합니다.", "trustedAppAutoConfirmDisclaimer2": "활성화하면 자금 관련 사기 위험에 처할 수 있습니다. 이 기능은 신뢰할 수 있는 앱에서만 사용하십시오.", "validationUtilsPasswordIsRequired": "비밀번호가 필요합니다", "validationUtilsPasswordLength": "비밀번호는 8자여야 합니다", "validationUtilsPasswordsDontMatch": "비밀번호가 일치하지 않습니다", "validationUtilsPasswordCantBeSame": "이전 비밀번호를 사용할 수 없습니다", "validatorCardEstimatedApy": "예상 APY", "validatorCardCommission": "커미션", "validatorCardTotalStake": "총 스테이크", "validatorCardNumberOfDelegators": "위임자 수", "validatorListChooseAValidator": "검증자 선택", "validatorListErrorFetching": "검증자를 가져올 수 없습니다. 나중에 다시 시도하십시오.", "validatorListNoResults": "결과 없음", "validatorListReload": "다시 로드", "validatorInfoTooltip": "검증자", "validatorInfoTitle": "검증자", "validatorInfoDescription": "검증자에서 SOL을 스테이킹하면 Solana 네트워크의 성능과 보안에 기여하는 동시에 리워드로 SOL을 얻을 수 있습니다.", "validatorApyInfoTooltip": "예상 APY", "validatorApyInfoTitle": "예상 APY", "validatorApyInfoDescription": "검증자에서 SOL을 스테이킹하여 얻는 수익율입니다.", "validatorViewActionButtonStake": "스테이킹", "validatorViewErrorFetching": "검증자를 가져오지 못했습니다.", "validatorViewInsufficientBalance": "잔액 부족", "validatorViewMax": "최대", "validatorViewPrimaryText": "스테이킹 시작", "validatorViewDescriptionInterpolated": "이 검증자로 스테이킹할 <1></1> SOL 금액을 선택하십시오. <3>자세히 알아보기</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "스테이킹하려면 {{amount}} SOL이 필요합니다", "validatorViewValidator": "검증자", "walletMenuItemsAddConnectWallet": "월릿 추가/연결", "walletMenuItemsBridgeAssets": "자산 연결", "walletMenuItemsHelpAndSupport": "도움말 및 지원", "walletMenuItemsLockWallet": "월릿 잠그기", "walletMenuItemsResetSecretPhrase": "비밀 문구 재설정", "walletMenuItemsShowMoreAccounts": "{{count}}개 더보기...", "walletMenuItemsHideAccounts": "계정 숨기기", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "Solana 전용 모드", "disableMultiChainDetail1Header": "Solana에 올인", "disableMultiChainDetail1SecondaryText": "다른 체인을 보지 않고 Solana 계정, 토큰 및 콜렉터블을 관리하십시오.", "disableMultiChainDetail2Header": "언제든지 Multichain으로 돌아가기", "disableMultiChainDetail2SecondaryText": "Multichain을 다시 활성화하면 기존 Ethereum 및 Polygon 잔액이 보존됩니다.", "disableMultiChainButton": "Solana 전용 활성화", "disabledMultiChainHeader": "Solana 전용 활성화됨", "disabledMultiChainText": "언제든지 Multichain을 다시 활성화할 수 있습니다.", "enableMultiChainHeader": "Multichain 활성화", "enabledMultiChainHeader": "Multichain 활성화됨", "enabledMultiChainText": "Ethereum 및 Polygon이 이제 월릿에서 지원됩니다.", "incompatibleAccountHeader": "호환되지 않는 계정", "incompatibleAccountInterpolated": "Solana 전용 모드를 활성화하기 전에 다음 Ethereum 전용 ​​계정을 제거하십시오: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "새로운 기능!", "welcomeToMultiChainPrimaryText": "모든 것을 위한 하나의 월릿", "welcomeToMultiChainDetail1Header": "Ethereum 및 Polygon 지원", "welcomeToMultiChainDetail1SecondaryText": "Solana, Ethereum 및 Polygon의 모든 토큰과 NFT를 한 곳에서 관리하십시오.", "welcomeToMultiChainDetail2Header": "좋아하는 모든 앱 사용", "welcomeToMultiChainDetail2SecondaryText": "여러 체인의 앱을 네트워크 전환 없이 연결할 수 있습니다.", "welcomeToMultiChainDetail3Header": "MetaMask 월릿 가져오기", "welcomeToMultiChainDetail3SecondaryText": "Ethernet 및 Polygon에서 모든 시드 문구를 쉽게 가져올 수 있습니다.", "welcomeToMultiChainIntro": "Phantom Multichain에 오신 것을 환영합니다", "welcomeToMultiChainIntroDesc": "Solana, Ethereum, Polygon의 토큰과 NFT가 모두 한 곳에 있습니다. 모든 것을 위한 하나의 월릿입니다.", "welcomeToMultiChainAccounts": "재설계된 멀티체인 계정", "welcomeToMultiChainAccountsDesc": "멀티체인용으로 재설계된 각 계정에는 이제 해당 ETH 및 Polygon 주소가 있습니다.", "welcomeToMultiChainApps": "어디서나 사용 가능", "welcomeToMultiChainAppsDesc": "Phantom은 Ethereum, Polygon 및 Solana의 모든 앱과 호화됩니다. \"MetaMask에 연결\"을 클릭하면 시작됩니다.", "welcomeToMultiChainImport": "MetaMask에서 즉시 가져오기", "welcomeToMultiChainImportDesc": "MetaMask 또는 Coinbase Wallet 같은 월릿에서 비밀 문구 또는 비공개 키를 가져올 수 있습니다. 모든 것을 한 곳에서 이용할 수 있습니다.", "welcomeToMultiChainImportInterpolated": "MetaMask 또는 Coinbase Wallet 같은 월릿에서 비밀 문구 또는 <0>비공개 키를 가져올 수 있습니다</0>. 모든 것을 한 곳에서 이용할 수 있습니다.", "welcomeToMultiChainTakeTour": "둘러보기", "welcomeToMultiChainSwapperTitle": "Ethereum, Polygon,\nSolana 스왑", "welcomeToMultiChainSwapperDetail1Header": "Ethereum 및 Polygon 지원", "welcomeToMultiChainSwapperDetail1SecondaryText": "이제 월릿 내부에서 쉽게 ERC-20 토큰을 스왑할 수 있습니다.", "welcomeToMultiChainSwapperDetail2Header": "최상의 가격과 완전 저렴한 수수료", "welcomeToMultiChainSwapperDetail2SecondaryText": "100개 이상의 유동성 소스와 스마트 주문 라우팅을 통해 수익을 극대화할 수 있습니다.", "networkErrorTitle": "네트워크 오류", "networkError": "네트워크에 액세스할 수 없습니다. 나중에 다시 시도하세요.", "authenticationUnlockPhantom": "Phantom 잠금 해제", "errorAndOfflineSomethingWentWrong": "문제가 발생했습니다", "errorAndOfflineSomethingWentWrongTryAgain": "다시 시도하십시오.", "errorAndOfflineUnableToFetchAssets": "애셋을 가져올 수 없습니다. 나중에 다시 시도하십시오.", "errorAndOfflineUnableToFetchCollectibles": "콜렉터블을 가져올 수 없습니다. 나중에 다시 시도하십시오.", "errorAndOfflineUnableToFetchSwap": "스왑 정보를 가져올 수 없습니다. 나중에 다시 시도하십시오.", "errorAndOfflineUnableToFetchTransactionHistory": "지금은 거래 내역을 가져올 수 없습니다. 네트워크 연결을 확인하거나 나중에 다시 시도하십시오.", "errorAndOfflineUnableToFetchRewardsHistory": "리워드 내역을 가져올 수 없습니다. 나중에 다시 시도하십시오.", "errorAndOfflineUnableToFetchBlockedUsers": "차단된 사용자를 가져올 수 없습니다. 나중에 다시 시도하십시오.", "networkHealthSheetCloseButtonText": "확인", "swapReviewError": "주문을 검토하는 동안 문제가 발생했습니다. 다시 시도하십시오.", "sendSelectToken": "토큰 선택", "swapBalance": "잔액:", "swapTitle": "토큰 스왑", "swapSelectToken": "토큰 선택", "swapYouPay": "지불", "swapYouReceive": "수령", "aboutPrivacyPolicy": "개인정보 처리방침", "aboutVersion": "버전 {{version}}", "aboutVisitWebsite": "웹사이트 방문", "bottomSheetConnectTitle": "연결", "A11YbottomSheetConnectTitle": "하단 시트 연결", "A11YbottomSheetCommandClose": "하단 시트 거부", "A11YbottomSheetCommandBack": "하단 시트 뒤로", "bottomSheetSignTypedDataTitle": "메시지에 서명", "bottomSheetSignMessageTitle": "메시지에 서명", "bottomSheetSignInTitle": "로그인", "bottomSheetSignInAndConnectTitle": "로그인", "bottomSheetConfirmTransactionTitle": "거래 확인", "bottomSheetConfirmTransactionsTitle": "거래 확인", "bottomSheetSolanaPayTitle": "Solana Pay 요청", "bottomSheetAdvancedTitle": "고급", "bottomSheetReadOnlyAccountTitle": "보기 전용 모드", "bottomSheetTransactionSettingsTitle": "네트워크 수수료", "bottomSheetConnectDescription": "연결하면 선택한 계정의 잔액과 활동을 이 사이트에서 확인할 수 있습니다.", "bottomSheetSignInDescription": "이 메시지에 서명하면 선택한 계정의 소유권이 있음을 증명합니다. 신뢰하는 애플리케이션의 메시지에만 서명하십시오.", "bottomSheetSignInAndConnectDescription": "승인하면 선택한 계정의 잔액과 활동을 이 사이트에서 확인할 수 있습니다.", "bottomSheetConfirmTransactionDescription": "잔액 변화는 추정치입니다. 관련 금액과 자산은 보장되지 않습니다.", "bottomSheetConfirmTransactionsDescription": "잔액 변화는 추정치입니다. 관련 금액과 자산은 보장되지 않습니다.", "bottomSheetSignTypedDataDescription": "이것은 권한 요청일 뿐입니다. 거래가 즉시 실행되지는 않을 수 있습니다.", "bottomSheetSignTypedDataSecondDescription": "잔액 변화는 추정치입니다. 관련 금액과 자산은 보장되지 않습니다.", "bottomSheetSignMessageDescription": "이 메시지에 서명하면 선택한 계정의 소유권이 있음을 증명합니다. 신뢰하는 애플리케이션의 메시지에만 서명하십시오.", "bottomSheetReadOnlyAccountDescription": "보기 전용 모드에서는 이 작업을 수행할 수 없습니다.", "bottomSheetMessageRow": "메시지", "bottomSheetStatementRow": "명세서", "bottomSheetAutoConfirmRow": "자동 확인", "bottomSheetAutoConfirmOff": "끄기", "bottomSheetAutoConfirmOn": "켜기", "bottomSheetAccountRow": "계정", "bottomSheetAdvancedRow": "고급", "bottomSheetContractRow": "계약 주소", "bottomSheetSpenderRow": "소비자 주소", "bottomSheetNetworkRow": "네트워크", "bottomSheetNetworkFeeRow": "네트워크 수수료", "bottomSheetEstimatedTimeRow": "예상 시간", "bottomSheetAccountRowDefaultAccountName": "계정", "bottomSheetConnectRequestDisclaimer": "신뢰하는 웹사이트에만 연결", "bottomSheetSignInRequestDisclaimer": "신뢰하는 웹사이트에만 로그인", "bottomSheetSignatureRequestDisclaimer": "이 웹사이트를 신뢰하는 경우에만 확인하십시오.", "bottomSheetFeaturedTransactionDisclaimer": "다음 단계에서 확인하기 전에 거래 미리 보기가 표시됩니다.", "bottomSheetIgnoreWarning": "경고를 무시하고 계속하기", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "잔액 변화가 없습니다. 주의해서 진행하고 이 사이트를 신뢰하는 경우에만 확인하십시오.", "bottomSheetReadOnlyWarning": "이 주소를 조회만 하고 있습니다. 거래와 메시지에 서명하려면 비밀 문구를 가져와야 합니다.", "bottomSheetWebsiteIsUnsafeWarning": "이 웹사이트는 사용하기에 안전하지 않으며 귀하의 자금을 훔치려고 시도할 수 있습니다.", "bottomSheetViewOnExplorer": "Explorer에서 보기", "bottomSheetTransactionSubmitted": "거래 제출됨", "bottomSheetTransactionPending": "거래 보류 중", "bottomSheetTransactionFailed": "거래 실패", "bottomSheetTransactionSubmittedDescription": "거래가 제출되었습니다. 탐색기에서 해당 거래를 볼 수 있습니다.", "bottomSheetTransactionFailedDescription": "거래가 실패했습니다. 다시 시도하십시오.", "bottomSheetTransactionPendingDescription": "거래를 처리 중입니다...", "transactionsFromInterpolated": "송신: {{from}}", "transactionsFromParagraphInterpolated": "송신: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "오늘", "transactionsToInterpolated": "수신: {{to}}", "transactionsToParagraphInterpolated": "수신: {{to}}", "transactionsYesterday": "어제", "addEditAddressAdd": "주소 추가", "addEditAddressDelete": "주소 삭제", "addEditAddressDeleteTitle": "이 주소를 삭제하시겠습니까?", "addEditAddressSave": "주소 저장", "dAppBrowserComingSoon": "dApp 브라우저 곧 출시!", "dAppBrowserSearchPlaceholder": "사이트, 토큰, URL", "dAppBrowserOpenInNewTab": "새 탭에서 열기", "dAppBrowserSuggested": "제안됨", "dAppBrowserFavorites": "즐겨찾기", "dAppBrowserBookmarks": "북마크", "dAppBrowserBookmarkAdd": "북마크 추가", "dAppBrowserBookmarkRemove": "북마크 제거", "dAppBrowserUsers": "사용자", "dAppBrowserRecents": "최근", "dAppBrowserFavoritesDescription": "즐겨찾기가 여기에 표시됩니다", "dAppBrowserBookmarksDescription": "북마크가 여기에 표시됩니다", "dAppBrowserRecentsDescription": "최근 연결된 dapp이 여기에 표시됩니다", "dAppBrowserEmptyScreenDescription": "URL 입력 또는 웹 검색", "dAppBrowserBlocklistScreenTitle": "{{origin}}이(가) 차단되었습니다! ", "dAppBrowserBlocklistScreenDescription": {"part1": "이 웹사이트는 악성이며 사용하기에 안전하지 않다고 판단됩니다.", "part2": "이 사이트는 알려진 피싱 웹사이트 및 스캠에 관한 커뮤니티 관리 데이터베이스에 등록된 것으로 표시됩니다. 해당 사이트가 잘못 표시되었다고 생각하시면 문제를 보고해주십시오."}, "dAppBrowserLoadFailedScreenTitle": "로드 실패", "dAppBrowserLoadFailedScreenDescription": "이 페이지를 로드하는 중에 오류가 발생했습니다", "dAppBrowserBlocklistScreenIgnoreButton": "경고를 무시하고 계속 표시", "dAppBrowserActionBookmark": "북마크", "dAppBrowserActionRemoveBookmark": "북마크 제거", "dAppBrowserActionRefresh": "새로 고침", "dAppBrowserActionShare": "공유", "dAppBrowserActionCloseTab": "탭 닫기", "dAppBrowserActionEndAutoConfirm": "자동 확인 종료", "dAppBrowserActionDisconnectApp": "앱 연결 끊기", "dAppBrowserActionCloseAllTabs": "모든 탭 닫기", "dAppBrowserNavigationAddressPlaceholder": "URL을 입력하여 검색", "dAppBrowserTabOverviewMore": "자세히", "dAppBrowserTabOverviewAddTab": "탭 추가", "dAppBrowserTabOverviewClose": "닫기", "dAppBrowserCloseTab": "탭 닫기", "dAppBrowserClose": "닫기", "dAppBrowserTabOverviewAddBookmark": "북마크 추가", "dAppBrowserTabOverviewRemoveBookmark": "북마크 제거", "depositAssetListSuggestions": "제안", "depositUndefinedToken": "죄송합니다. 이 토큰을 입금할 수 없습니다", "onboardingImportRecoveryPhraseDetails": "세부 정보", "onboardingCreateRecoveryPhraseVerifyTitle": "복구용 비밀 문구를 작성하셨습니까?", "onboardingCreateRecoveryPhraseVerifySubtitle": "복구용 비밀 문구가 없으면 키 또는 해당 키에 연결된 애셋에 액세스할 수 없게 됩니다.", "onboardingCreateRecoveryPhraseVerifyYes": "예", "onboardingCreateRecoveryPhraseErrorTitle": "오류", "onboardingCreateRecoveryPhraseErrorSubtitle": "계정을 생성하지 못했습니다. 다시 시도하십시오.", "onboardingDoneDescription": "이제 월릿을 완전히 이용할 수 있습니다.", "onboardingDoneGetStarted": "시작하기", "zeroBalanceHeading": "시작!", "zeroBalanceBuyCryptoTitle": "암호화폐 구입", "zeroBalanceBuyCryptoDescription": "직불카드나 신용카드로 첫 번째 암호화폐를 구입하십시오.", "zeroBalanceDepositTitle": "암호화폐 전송", "zeroBalanceDepositDescription": "다른 월릿이나 거래소에서 암호화폐를 입금하십시오.", "onboardingImportAccountsEmptyResult": "계정을 찾을 수 없습니다", "onboardingImportAccountsAccountName": "계정 {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "소셜 계정", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "활동이 있는 {{numberOfWallets}}개 계정을 찾았습니다", "onboardingImportAccountsFoundAccounts_other": "활동이 있는 {{numberOfWallets}}개 계정을 찾았습니다", "onboardingImportAccountsFoundAccountsNoActivity_one": "{{numberOfWallets}}개 계정을 찾았습니다", "onboardingImportAccountsFoundAccountsNoActivity_other": "{{numberOfWallets}}개 계정을 찾았습니다", "onboardingImportRecoveryPhraseLessThanTwelve": "문구의 단어는 12개 이상이어야 합니다.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "문구의 단어는 정확히 12개 또는 24개여야 합니다.", "onboardingImportRecoveryPhraseWrongWord": "잘못된 단어: {{ words }}.", "onboardingProtectTitle": "월릿 보호하기", "onboardingProtectDescription": "생체 인식 보안을 추가하면 본인만 지갑에 액세스할 수 있습니다.", "onboardingProtectButtonHeadlineDevice": "장치", "onboardingProtectButtonHeadlineFaceID": "페이스 ID", "onboardingProtectButtonHeadlineFingerprint": "지문", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "{{ authType }} 인증 사용", "onboardingProtectError": "인증하는 동안 문제가 발생했습니다. 다시 시도하십시오", "onboardingProtectBiometryIosError": "생체 인식 인증은 Phantom에서 구성되지만 시스템 설정에서는 비활성화됩니다. 설정 > Phantom > 페이스 ID 또는 터치 ID를 열어 다시 활성화하십시오.", "onboardingProtectRemoveAuth": "인증 비활성화", "onboardingProtectRemoveAuthDescription": "인증을 비활성화하시겠습니까?", "onboardingWelcomeTitle": "Phantom에 오신 것을 환영합니다", "onboardingWelcomeDescription": "시작하려면 새 월릿을 만들거나 기존 월릿을 가져오십시오.", "onboardingWelcomeCreateWallet": "새 월릿 생성", "onboardingWelcomeAlreadyHaveWallet": "월릿이 이미 있습니다", "onboardingWelcomeConnectSeedVault": "시드 볼트 연결", "onboardingSlide1Title": "사용자가 제어함", "onboardingSlide1Description": "월릿은 생체 인식 액세스, 스캠 감지 및 연중무휴 지원을 통해 보호됩니다.", "onboardingSlide2Title": "NFT를 위한\n베스트 홈", "onboardingSlide2Description": "목록을 관리하고, 스팸을 소각하고, 유용한 푸시 알림으로 최신 정보를 확인하십시오.", "onboardingSlide3Title": "토큰으로 더 많은 작업 수행", "onboardingSlide3Description": "월릿을 나가지 않고도 저장, 스왑, 스테이킹, 전송 및 수령이 가능합니다. ", "onboardingSlide4Title": "최상의 Web3 알아보기", "onboardingSlide4Description": "인앱 브라우저를 사용하여 주요 앱 및 컬렉션을 찾고 연결할 수 있습니다.", "onboardingMultichainSlide5Title": "모든 것을 위한 하나의 월릿", "onboardingMultichainSlide5Description": "사용하기 쉬운 단일 인터페이스에서 Solana, Ethereum 및 Polygon을 모두 이용할 수 있습니다.", "onboardingMultichainSlide5DescriptionWithBitcoin": "사용하기 쉬운 단일 인터페이스에서 Solana, Ethereum, Polygon, Bitcoin을 모두 이용할 수 있습니다.", "requireAuth": "인증 필요", "requireAuthImmediately": "즉시", "availableToSend": "전송 가능", "sendEnterAmount": "계정 입력", "sendEditMemo": "메모 편집", "sendShowLogs": "오류 로그 표시", "sendHideLogs": "오류 로그 숨기기", "sendGoBack": "뒤로", "sendTransactionSuccess": "토큰이 다음으로 전송되었습니다:", "sendInputPlaceholder": "@사용자이름 또는 주소", "sendInputPlaceholderV2": "사용자 이름 또는 주소", "sendPeopleTitle": "피플", "sendDomainTitle": "도메인", "sendFollowing": "팔로잉", "sendRecentlyUsedAddressLabel": "{{formattedTimestamp}} 전에 사용함", "sendRecipientAddress": "수신자의 주소", "sendTokenInterpolated": "{{tokenSymbol}} 보내기", "sendPasteFromClipboard": "클립보드에서 붙여넣기", "sendScanQR": "QR 코드 스캔", "sendTo": "수신:", "sendRecipientZeroBalanceWarning": "이 월릿 주소에는 잔액이 없으며 최근 거래 내역에도 표시되지 않습니다. 주소가 올바른지 확인하십시오.", "sendUnknownAddressWarning": "최근에 상호 작용한 적이 없는 주소입니다. 주의하여 진행하십시오.", "sendSameAddressWarning": "이것은 현재 주소입니다. 송금하면 이체 수수료가 발생하며 다른 잔액 변경은 없습니다.", "sendMintAccountWarning": "민팅 계정 주소입니다. 영구적인 손실이 발생하므로 이 주소로 자금을 보낼 수 없습니다.", "sendCameraAccess": "카메라 액세스", "sendCameraAccessSubtitle": "QR 코드를 스캔하려면 카메라 액세스를 활성화해야 합니다. 지금 설정을 여시겠습니까?", "sendSettings": "설정", "sendOK": "확인", "invalidQRCode": "이 QR 코드는 유효하지 않습니다.", "sendInvalidQRCode": "이 QR 코드는 올바른 주소가 아닙니다", "sendInvalidQRCodeSubtitle": "다시 시도하거나 다른 QR 코드를 사용하십시오.", "sendInvalidQRCodeSplToken": "QR 코드의 잘못된 토큰", "sendInvalidQRCodeSplTokenSubtitle": "이 QR 코드에는 귀하가 소유하지 않거나 당사가 식별할 수 없는 토큰이 포함되어 있습니다.", "sendScanAddressToSend": "자금을 보내려면 {{tokenSymbol}} 주소를 스캔하십시오", "sendScanAddressToSendNoSymbol": "자금을 보낼 주소 스캔", "sendScanAddressToSendCollectible": "콜렉터블을 보내려면 SOL 주소를 스캔하십시오", "sendScanAddressToSendCollectibleMultichain": "콜렉터블을 보낼 주소 스캔", "sendSummary": "요약", "sendUndefinedToken": "죄송합니다. 이 토큰을 전송할 수 없습니다", "sendNoTokens": "사용할 수 있는 토큰이 없습니다", "noBuyOptionsAvailableInCountry": "해당 국가에서 사용 가능한 구매 옵션이 없습니다", "swapAvailableTokenDisclaimer": "네트워크 간 브리징에 사용할 수 있는 토큰 수에는 제한이 있습니다.", "swapCrossSwapNetworkTooltipTitle": "네트워크 간 스왑", "swapCrossSwapNetworkTooltipDescription": "네트워크 간에 스왑하는 경우 최저 가격과 가장 빠른 거래를 위해 사용 가능한 토큰을 사용하는 것이 좋습니다.", "settingsAbout": "Phantom 정보", "settingsShareAppWithFriends": "친구 초대", "settingsConfirm": "예", "settingsMakeSureNoOneIsWatching": "아무도 화면을 보고 있지 않은지 확인하십시오", "settingsManageAccounts": "계정 관리", "settingsPrompt": "계속하시겠습니까?", "settingsSelectAvatar": "아바타 선택", "settingsSelectSecretPhrase": "비밀 문구 선택", "settingsShowPrivateKey": "탭하여 비공개 키 표시", "settingsShowRecoveryPhrase": "탭하여 비밀 문구 표시", "settingsSubmitBetaFeedback": "베타 피드백 제출", "settingsUpdateAccountNameToast": "계정 이름 업데이트됨", "settingsUpdateAvatarToast": "아바타 업데이트됨", "settingsUpdateAvatarToastFailure": "아바타를 업데이트하지 못했습니다!", "settingsWalletAddress": "계정 주소", "settingsWalletAddresses": "계정 주소", "settingsWalletNamePrimary": "계정 이름", "settingsPlaceholderName": "이름", "settingsWalletNameSecondary": "월릿 이름 변경", "settingsYourAccounts": "내 계정", "settingsYourAccountsMultiChain": "멀티 체인", "settingsReportUser": "사용자 신고", "settingsNotifications": "알림", "settingsNotificationPreferences": "알림 기본 설정", "pushNotificationsPreferencesAllowNotifications": "알림 허용", "pushNotificationsPreferencesSentTokens": "토큰 보냄", "pushNotificationsPreferencesSentTokensDescription": "토큰 및 NFT 아웃바운드 전송", "pushNotificationsPreferencesReceivedTokens": "토큰 받음", "pushNotificationsPreferencesReceivedTokensDescription": "토큰 및 NFT 인바운드 전송", "pushNotificationsPreferencesDexSwap": "스왑", "pushNotificationsPreferencesDexSwapDescription": "공인 애플리케이션에서의 스왑", "pushNotificationsPreferencesOtherBalanceChanges": "기타 잔액 변화", "pushNotificationsPreferencesOtherBalanceChangesDescription": "잔액에 영향을 미치는 기타 멀티 토큰 거래", "pushNotificationsPreferencesPhantomMarketing": "Phantom에서 업데이트", "pushNotificationsPreferencesPhantomMarketingDescription": "기능 공지 및 일반 업데이트", "pushNotificationsPreferencesDescription": "이러한 설정은 해당 활성 월렛의 푸시 알림을 제어합니다. 각 월릿에는 고유한 알림 설정이 있습니다. 모든 Phantom 푸시 알림을 끄려면 <1>장치 설정</1>으로 이동하십시오.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "알림 기본 설정을 동기화할 수 없습니다.", "connectSeedVaultConnectSeed": "시드 연결", "connectSeedVaultConnectSeedDescription": "휴대폰의 시드 볼트에 Phantom 연결", "connectSeedVaultSelectAnAccount": "계정 선택", "connectSeedVaultSelectASeed": "시드 선택", "connectSeedVaultSelectASeedDescription": "Phantom에 연결할 시드 선택", "connectSeedVaultSelectAnAccountDescription": "Phantom으로 설정할 계정을 선택하십시오", "connectSeedVaultNoAccountsFound": "계정을 찾을 수 없습니다.", "connectSeedVaultSelectAccounts": "계정 선택", "connectSeedVaultSelectAccountsDescription": "Phantom으로 설정할 계정을 선택하십시오", "connectSeedVaultCompleteSetup": "설정 완료", "connectSeedVaultCompleteSetupDescription": "준비 완료되었습니다! Phantom으로 web3를 탐색하고 시드 볼트를 사용하여 거래를 확인할 수 있습니다.", "connectSeedVaultConnectAnotherSeed": "다른 시드 연결", "connectSeedVaultConnectAllSeedsConnected": "모든 시드 연결됨", "connectSeedVaultNoSeedsConnected": "연결된 시드가 없습니다. 아래 버튼을 탭하여 시드 볼트에서 승인하십시오.", "connectSeedVaultConnectAccount": "계정 연결", "connectSeedVaultLoadMore": "더 로드", "connectSeedVaultNeedPermission": "권한 필요", "connectSeedVaultNeedPermissionDescription": "설정으로 이동하여 Phantom이 시드 볼트 권한을 사용할 수 있도록 허용하십시오.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} 수수료", "stakeAmount": "금액", "stakeAmountBalance": "잔액", "swapTopQuotes": "상위 {{numQuotes}}개 견적", "swapTopQuotesTitle": "상위 견적", "swapProvidersTitle": "제공자", "swapProvidersFee": "{{fee}} 수수료", "swapProvidersTagRecommended": "최고의 수익", "swapProvidersTagFastest": "가장 빠름", "swapProviderEstimatedTimeHM": "{{hours}}시간 {{minutes}}분", "swapProviderEstimatedTimeM": "{{minutes}}분", "swapProviderEstimatedTimeS": "{{seconds}}초", "stakeReview": "검토", "stakeReviewAccount": "계정", "stakeReviewCommissionFee": "커미션 수수료", "stakeReviewConfirm": "확인", "stakeReviewValidator": "검증자", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "스테이크 전환 실패", "convertStakeStatusErrorMessage": "해당 스테이크를 {{poolTokenSymbol}}(으)로 전환할 수 없습니다. 다시 시도하십시오.", "convertStakeStatusLoadingTitle": "{{poolTokenSymbol}}(으)로 전환 중", "convertStakeStatusLoadingMessage": "스테이킹된 {{stakedTokenSymbol}}을(를) {{poolTokenSymbol}}(으)로 전환하는 프로세스를 시작하고 있습니다.", "convertStakeStatusSuccessTitle": "{{poolTokenSymbol}}(으)로 전환 완료!", "convertStakeStatusSuccessMessage": "<1>여기</1>에서 JitoSOL로 추가 리워드를 받으십시오.", "convertStakeStatusConvertMore": "추가로 전환", "convertStakePendingTitle": "{{symbol}}(으)로 스테이크 전환 중", "convertToJitoSOL": "JitoSOL로 전환", "convertToJitoSOLInfoDescription": "SOL을 Jito SOL로 전환하여 리워드를 받고 Jito 생태계에 참여하십시오.", "convertToJitoSOLInfoTitle": "JitoSOL로 전환", "convertStakeBannerTitle": "스테이크를 JitoSOL로 전환하여 리워드를 최대 15%까지 늘리십시오", "convertStakeQuestBannerTitle": "스테이킹된 SOL을 JitoSOL로 전환하고 리워드를 받으십시오. 자세히 알아보십시오.", "liquidStakeConvertInfoTitle": "JitoSOL로 전환", "liquidStakeConvertInfoDescription": "SOL 스테이크를 JitoSOL로 전환하여 리워드를 늘리십시오. <1>자세히 알아보기</1>", "liquidStakeConvertInfoFeature1Title": "Jito으로 스테이킹하는 이유는?", "liquidStakeConvertInfoFeature1Description": "귀하의 스테이크과 함께 증가하는 JitoSOL을 얻으려면 입금하십시오. 추가 수익을 얻으려면 DeFi 프로토콜에서 사용하십시오. 나중에 초기 금액 + 발생 리워드를 위해 JitoSOL을 스왑하십시오.", "liquidStakeConvertInfoFeature2Title": "더 높은 평균 리워드", "liquidStakeConvertInfoFeature2Description": "Jito는 가장 낮은 수수료로 최고의 검증자 사이에 귀하의 SOL을 분산시킵니다. MEV 리워드가 수익을 더욱 늘려줍니다.", "liquidStakeConvertInfoFeature3Title": "Solana 네트워크 지원", "liquidStakeConvertInfoFeature3Description": "리퀴드 스테이킹은 여러 검증자에 스테이크를 분산시키므로 가동 시간이 짧은 검증자로 인한 위험을 줄여 Solana를 보호합니다.", "liquidStakeConvertInfoSecondaryButton": "나중에", "liquidStakeStartStaking": "스테이킹 시작", "liquidStakeReviewOrder": "주문 검토", "convertStakeAccountListPageIneligibleSectionTitle": "부적격 스테이크 계정", "convertStakeAccountIneligibleBottomSheetTitle": "부적격 스테이크 계정", "convertStakeAccountListPageErrorTitle": "스테이크 계정을 가져오지 못함", "convertStakeAccountListPageErrorDescription": "죄송합니다. 문제가 발생하여 스테이크 계정을 가져오지 못했습니다.", "liquidStakeReviewYouPay": "지불", "liquidStakeReviewYouReceive": "수령", "liquidStakeReviewProvider": "제공자", "liquidStakeReviewNetworkFee": "네트워크 수수료", "liquidStakeReviewPageTitle": "확인", "liquidStakeReviewConversionFootnote": "JitoSOL과 교환하여 Solana 토큰을 스테이킹하면 JitoSOL을 약간 적게 받게 됩니다. <1>자세히 알아보기</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Jito의 스테이크 풀은 대부분의 활성 Solana 검증자를 지원합니다. 지원되지 않는 검증자에서 스테이킹된 SOL은 JitoSOL로 전환할 수 없습니다. 또한 새로 스테이킹된 SOL은 JitoSOL 전환이 적용되기까지 최대 2일이 소요됩니다.", "selectAValidator": "검증자 선택", "validatorSelectionListTitle": "검증자 선택", "validatorSelectionListDescription": "SOL을 스테이킹할 검증자를 선택하십시오.", "stakeMethodDescription": "Solana 확장을 돕기 위해 SOL 토큰을 사용하여 이자를 받으십시오. <1>자세히 알아보기</1>", "stakeMethodRecommended": "추천", "stakeMethodEstApy": "예상 APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "리퀴드 스테이킹", "stakeMethodSelectionLiquidStakingDescription": "SOL을 스테이킹하여 더 높은 리워드를 받고, Solana를 확보하고, JitoSOL을 받아 추가 리워드를 얻으십시오.", "stakeMethodSelectionNativeStakingTitle": "네이티브 스테이킹", "stakeMethodSelectionNativeStakingDescription": "SOL을 스테이킹하여 Solana를 확보하는 동시에 리워드를 받으십시오.", "liquidStakeMintStakeSOL": "SOL 스테이킹", "mintJitoSOLInfoPageTitle": "Jito를 사용하는 리퀴드 스테이킹 도입", "mintJitoSOLFeature1Title": "Jito로 스테이킹하는 이유는?", "mintJitoSOLFeature1Description": "귀하의 스테이크과 함께 증가하는 JitoSOL을 얻으려면 입금하십시오. 추가 수익을 얻으려면 DeFi 프로토콜에서 사용하십시오. 초기 금액 + 발생 리워드를 위해 나중에 JitoSOL을 스왑하십시오.", "mintJitoSOLFeature2Title": "더 높은 평균 리워드", "mintJitoSOLFeature2Description": "Jito는 최저 수수료로 최고의 검증자에 귀하의 SOL을 분산시킵니다. MEV 리워드가 수익을 더욱 늘려줍니다.", "mintJitoSOLFeature3Title": "Solana 네트워크 지원", "mintJitoSOLFeature3Description": "리퀴드 스테이킹은 여러 검증자에 스테이크를 분산시키므로 가동 시간이 짧은 검증자로 인한 위험을 줄여 Solana를 보호합니다.", "mintLiquidStakePendingTitle": "리퀴드 스테이크 민팅", "mintStakeStatusErrorTitle": "리퀴드 스테이크 민팅 실패", "mintStakeStatusErrorMessage": "{{poolTokenSymbol}} 리퀴드 스테이크를 민팅할 수 없습니다. 다시 시도하십시오.", "mintStakeStatusSuccessTitle": "{{poolTokenSymbol}} 리퀴드 스테이크 민팅 완료!", "mintStakeStatusLoadingTitle": "{{poolTokenSymbol}} 리퀴드 스테이크 민팅", "mintStakeStatusLoadingMessage": "{{poolTokenSymbol}} 리퀴드 스테이크를 민팅하는 프로세스를 시작하는 중입니다.", "mintLiquidStakeAmountDescription": "Jito로 스테이킹하고 싶은 SOL이 어느 정도인지 선택하십시오.", "mintLiquidStakeAmountProvider": "제공자", "mintLiquidStakeAmountApy": "예상 APY", "mintLiquidStakeAmountBestPrice": "가격", "mintLiquidStakeAmountInsufficientBalance": "잔액 부족", "mintLiquidStakeAmountMinRequired": "스테이킹하려면 {{amount}} {{symbol}}이(가) 필요합니다", "swapTooltipGotIt": "확인", "swapTabInsufficientFunds": "자금 부족", "swapNoAssetsFound": "자산 없음", "swapNoTokensFound": "토큰 찾지 못함", "swapConfirmationTryAgain": "다시 시도", "swapConfirmationGoBack": "뒤로", "swapNoQuotesFound": "견적 찾지 못함", "swapNotProviderFound": "이 토큰 스왑에 대한 제공자를 찾을 수 없습니다. 다른 토큰을 사용해 보십시오.", "swapAvailableOnMainnet": "이 기능은 Mainnet에서만 사용할 수 있습니다", "swapNotAvailableEVM": "EVM 계정에는 아직 스왑을 사용할 수 없습니다", "swapNotAvailableOnSelectedNetwork": "선택한 네트워크에서는 스왑을 사용할 수 없습니다", "singleChainSwapTab": "네트워크 내에서", "crossChainSwapTab": "네트워크 전반에서", "allFilter": "모두", "bridgeRefuelTitle": "Refuel", "bridgeRefuelDescription": "Refuel을 사용하면 브리지 수행 후 거래 비용을 지불할 수 있습니다.", "bridgeRefuelLabelBalance": "내 {{symbol}}", "bridgeRefuelLabelReceive": "수령", "bridgeRefuelLabelFee": "예상 비용", "bridgeRefuelDismiss": "Refuel 없이 계속하기", "bridgeRefuelEnable": "Refuel 활성화", "unwrapWrappedSolError": "래핑 해제 실패", "unwrapWrappedSolLoading": "래핑 해제 중...", "unwrapWrappedSolSuccess": "래핑 해제됨", "unwrapWrappedSolViewTransaction": "거래 보기", "dappApprovePopupSignMessage": "메시지에 서명", "solanaPayFrom": "보내는 곳", "solanaPayMessage": "메시지", "solanaPayNetworkFee": "네트워크 수수료", "solanaPayFree": "무료", "solanaPayPay": "{{item}} 지불", "solanaPayPayNow": "지금 지불", "solanaPaySending": "{{item}} 전송 중", "solanaPayReceiving": "{{item}} 수령 중", "solanaPayMinting": "{{item}} 민팅", "solanaPayTransactionProcessing": "거래가 처리되는 중입니다.\n기다려주십시오.", "solanaPaySent": "전송 완료!", "solanaPayReceived": "수령 완료!", "solanaPayMinted": "민팅 완료!", "solanaPaySentNFT": "NFT 전송 완료!", "solanaPayReceivedNFT": "NFT 수령 완료!", "solanaPayTokensSent": "토큰이 {{to}} 님에게 전송되었습니다", "solanaPayTokensReceived": "{{from}} 님으로부터 새 토큰을 받았습니다", "solanaPayViewTransaction": "거래 보기", "solanaPayTransactionFailed": "거래 실패", "solanaPayConfirm": "확인", "solanaPayTo": "받는 곳", "dappApproveConnectViewAccount": "Solona 계정 보기", "deepLinkInvalidLink": "잘못된 링크", "deepLinkInvalidSplTokenSubtitle": "여기에는 귀하가 소유하지 않거나 당사가 식별할 수 없는 토큰이 포함되어 있습니다.", "walletAvatarShowAllAccounts": "모든 계정 표시", "pushNotificationsGetInstantUpdates": "즉시 업데이트 다운로드", "pushNotificationsEnablePushNotifications": "완료된 전송, 스왑 및 공지에 대한 푸시 알림 활성화", "pushNotificationsEnable": "활성화", "pushNotificationsNotNow": "나중에", "onboardingAgreeToTermsOfServiceInterpolated": "<1>서비스 약관</1>에 동의합니다", "onboardingConfirmSaveSecretRecoveryPhrase": "예, 저장했습니다", "onboardingCreateNewWallet": "새 월릿 생성", "onboardingErrorDuplicateSecretRecoveryPhrase": "이 비밀 문구는 이미 월릿에 있습니다", "onboardingErrorInvalidSecretRecoveryPhrase": "잘못된 복구용 비밀 문구", "onboardingFinished": "모두 완료되었습니다!", "onboardingImportAccounts": "계정 가져오기", "onboardingImportImportingAccounts": "계정을 가져오는 중...", "onboardingImportImportingFindingAccounts": "활동이 있는 계정 찾기", "onboardingImportAccountsLastActive": "{{formattedTimestamp}} 전에 활성 상태", "onboardingImportAccountsNeverUsed": "미사용", "onboardingImportAccountsCreateNew": "새로운 월릿", "onboardingImportAccountsDescription": "가져올 월릿 계정을 선택합니다", "onboardingImportReadOnlyAccountDescription": "보려는 주소나 도메인 이름을 추가하십시오. 보기 전용 액세스 권한만 갖게 되며 거래나 메시지에 서명할 수 없습니다.", "onboardingImportSecretRecoveryPhrase": "비밀 문구 가져오기", "onboardingImportViewAccounts": "계정 보기", "onboardingRestoreExistingWallet": "12개 또는 24개 단어의 복구용 비밀 문구로 기존 월릿을 복원합니다", "onboardingShowUnusedAccounts": "사용하지 않는 계정 표시", "onboardingShowMoreAccounts": "추가 계정 표시", "onboardingHideUnusedAccounts": "사용하지 않는 계정 숨기기", "onboardingSecretRecoveryPhrase": "복구용 비밀 문구", "onboardingSelectAccounts": "계정 선택", "onboardingStoreSecretRecoveryPhraseReminder": "이는 유일한 계정 복구 방법이니 안전한 곳에 저장해두시기 바랍니다.", "useTokenMetasForMintsUnknownName": "알 수 없음", "timeUnitMinute": "분", "timeUnitMinutes": "분", "timeUnitHour": "시간", "timeUnitHours": "시간", "espNFTListWithPrice": "{{dAppName}}에서 {{priceAmount}} {{priceTokenSymbol}}에 {{NFTDisplayName}}을(를) 상장했습니다", "espNFTListWithPriceWithoutDApp": "{{priceAmount}} {{priceTokenSymbol}}에 {{NFTDisplayName}}을(를) 상장했습니다", "espNFTListWithoutPrice": "{{dAppName}}에서 판매용으로 {{NFTDisplayName}}을(를) 상장했습니다", "espNFTListWithoutPriceWithoutDApp": "판매용으로 {{NFTDisplayName}}을(를) 상장했습니다", "espNFTChangeListPriceWithPrice": "{{dAppName}}에서 {{NFTDisplayName}}에 대한 상장을 {{priceAmount}} {{priceTokenSymbol}}(으)로 업데이트했습니다", "espNFTChangeListPriceWithPriceWithoutDApp": "{{NFTDisplayName}}에 대한 상장을 {{priceAmount}} {{priceTokenSymbol}}(으)로 업데이트했습니다", "espNFTChangeListPriceWithoutPrice": "{{dAppName}}에서 {{NFTDisplayName}}에 대한 상장을 업데이트했습니다", "espNFTChangeListPriceWithoutPriceWithoutDApp": "{{NFTDisplayName}}에 대한 상장을 업데이트했습니다", "espNFTBidBidderWithPrice": "{{dAppName}}에서 {{NFTDisplayName}}을(를) 위해 {{priceAmount}} {{priceTokenSymbol}} 입찰을 했습니다", "espNFTBidBidderWithPriceWithoutDApp": "{{NFTDisplayName}}을(를) 위해 {{priceAmount}} {{priceTokenSymbol}} 입찰을 했습니다", "espNFTBidBidderWithoutPrice": "{{dAppName}}에서 {{NFTDisplayName}}에 대한 입찰을 했습니다", "espNFTBidBidderWithoutPriceWithoutDApp": "{{NFTDisplayName}}에 대한 입찰을 했습니다", "espNFTBidListerWithPrice": "{{dAppName}}에서 {{NFTDisplayName}}을(를) 위한 {{priceAmount}} {{priceTokenSymbol}}의 신규 입찰", "espNFTBidListerWithPriceWithoutDApp": "{{NFTDisplayName}}을(를) 위한 {{priceAmount}} {{priceTokenSymbol}}의 신규 입찰", "espNFTBidListerWithoutPrice": "{{dAppName}}에서 {{NFTDisplayName}}에 대한 신규 입찰", "espNFTBidListerWithoutPriceWithoutDApp": "{{NFTDisplayName}}의 신규 입찰", "espNFTCancelBidWithPrice": "{{dAppName}}에서 {{NFTDisplayName}}을(를) 위한 {{priceAmount}} {{priceTokenSymbol}}의 입찰을 취소했습니다", "espNFTCancelBidWithPriceWithoutDApp": "{{NFTDisplayName}}을(를) 위한 {{priceAmount}} {{priceTokenSymbol}}의 입찰을 취소했습니다", "espNFTCancelBidWithoutPrice": "{{dAppName}}에서 {{NFTDisplayName}}에 대한 입찰을 취소했습니다", "espNFTCancelBidWithoutPriceWithoutDApp": "{{NFTDisplayName}}에 대한 입찰을 취소했습니다", "espNFTUnlist": "{{dAppName}}에서 {{NFTDisplayName}}을(를) 상장 취소했습니다", "espNFTUnlistWithoutDApp": "{{NFTDisplayName}}을(를) 상장 취소했습니다.", "espNFTBuyBuyerWithPrice": "{{dAppName}}에서 {{priceAmount}} {{priceTokenSymbol}}에 {{NFTDisplayName}}을(를) 구입했습니다", "espNFTBuyBuyerWithPriceWithoutDApp": "{{priceAmount}} {{priceTokenSymbol}}에 {{NFTDisplayName}}을(를) 구입했습니다", "espNFTBuyBuyerWithoutPrice": "{{dAppName}}에서 {{NFTDisplayName}}을(를) 구입했습니다", "espNFTBuyBuyerWithoutPriceWithoutDApp": "{{NFTDisplayName}}을(를) 구입했습니다", "espNFTBuySellerWithPrice": "{{dAppName}}에서 {{priceAmount}} {{priceTokenSymbol}}에 {{NFTDisplayName}}을(를) 판매했습니다", "espNFTBuySellerWithPriceWithoutDApp": "{{priceAmount}} {{priceTokenSymbol}}에 {{NFTDisplayName}}을(를) 판매했습니다", "espNFTBuySellerWithoutPrice": "{{dAppName}}에서 {{NFTDisplayName}}을(를) 판매했습니다", "espNFTBuySellerWithoutPriceWithoutDApp": "{{NFTDisplayName}}을(를) 판매했습니다", "espDEXSwap": "{{dAppName}}에서 {{downTokensTextFragment}}을(를) {{upTokensTextFragment}}(으)로 교환했습니다", "espDEXDepositLPWithPoolDisplay": "{{dAppName}}의 {{poolDisplayName}} 유동성 풀에 {{downTokensTextFragment}}을(를) 넣었습니다", "espDEXDepositLPWithoutPoolDisplay": "{{dAppName}}에서 {{downTokensTextFragment}}을(를) {{upTokensTextFragment}}(으)로 교환했습니다", "espDEXWithdrawLPWithPoolDisplay": "{{dAppName}}의 {{poolDisplayName}} 유동성 풀에서 {{upTokensTextFragment}}을(를) 인출했습니다", "espDEXWithdrawLPWithoutPoolDisplay": "{{dAppName}}에서 {{downTokensTextFragment}}을(를) {{upTokensTextFragment}}(으)로 교환했습니다", "espGenericTokenSend": "{{downTokensTextFragment}}을(를) 보냈습니다", "espGenericTokenReceive": "{{upTokensTextFragment}}을(를) 받았습니다", "espGenericTransactionBalanceChange": "{{downTokensTextFragment}}을(를) {{upTokensTextFragment}}(으)로 교환했습니다", "espUnknown": "알 수 없음", "espUnknownNFT": "알 수 없는 NFT", "espTextFragmentAnd": "및", "externalLinkWarningTitle": "Phantom을 나가려고 합니다.", "externalLinkWarningDescription": "{{url}}을(를) 여십시오. 상호 작용하기 전에 이 소스를 신뢰할 수 있는지 확인하십시오.", "shortcutsWarningDescription": "{{url}}에서 제공하는 단축키입니다. 상호 작용하기 전에 이 소스를 신뢰할 수 있는지 확인하십시오.", "lowTpsBanner": "Solana에서 네트워크 정체가 발생하고 있습니다", "lowTpsMessageTitle": "Solana 네트워크 정체", "lowTpsMessage": "높은 Solana 정체로 인해 거래가 실패하거나 지연될 수 있습니다. 실패한 거래를 다시 시도하십시오.", "solanaSlow": "Solana 네트워크가 비정상적으로 느립니다", "solanaNetworkTemporarilyDown": "Solana 네트워크가 일시적으로 다운되었습니다", "waitForNetworkRestart": "네트워크가 다시 시작될 때까지 기다리십시오. 귀하의 자금에는 영향을 주지 않습니다.", "exploreCollectionsCarouselTitle": "인기", "exploreDropsCarouselTitle": "신규", "exploreSortFloor": "최저 가격", "exploreSortListed": "상장됨", "exploreSortVolume": "볼륨", "exploreFetchErrorSubtitle": "나중에 다시 시도하십시오.", "exploreFetchErrorTitle": "가져오지 못했습니다.", "exploreTopCollectionsTitle": "상위 NFT 컬렉션", "exploreTopListLess": "간단히", "exploreTopListMore": "자세히", "exploreSeeMore": "자세히 보기", "exploreTrendingTokens": "트렌딩 토큰", "exploreVolumeTokens": "최고 볼륨", "explorePriceChangeTokens": "최고 상승 종목", "explorePriceTokens": "가격별 토큰", "exploreMarketCapTokens": "상위 토큰", "exploreTrendingSites": "트렌딩 사이트", "exploreTopSites": "상위 사이트", "exploreTrendingCollections": "트렌딩 컬렉션", "exploreTopCollections": "상위 컬렉션", "collectiblesSearchCollectionsSection": "컬렉션", "collectiblesSearchItemsSection": "항목", "collectiblesSearchNrOfItems": "{{ nrOfItems }}개 항목", "collectiblesSearchPlaceholderText": "콜렉터블 검색", "collectionPinSuccess": "컬렉션 고정됨", "collectionPinFail": "컬렉션 고정 실패", "collectionUnpinSuccess": "컬렉션 고정 해제됨", "collectionUnpinFail": "컬렉션 고정 해제 실패", "collectionHideSuccess": "컬렉션 숨김", "collectionHideFail": "컬렉션 숨기기 실패", "collectionUnhideSuccess": "컬렉션 숨기기 해제됨", "collectionUnhideFail": "컬렉션 숨기기 해제 실패", "collectiblesSpamSuccess": "스팸으로 신고함", "collectiblesSpamFail": "스팸으로 신고 실패", "collectiblesSpamAndHiddenSuccess": "스팸으로 신고하고 숨김", "collectiblesNotSpamSuccess": "스팸 아님으로 신고함", "collectiblesNotSpamFail": "스팸 아님으로 신고 실패", "collectiblesNotSpamAndUnhiddenSuccess": "스팸 아님으로 신고하고 숨기기 해제함", "tokenPageSpamWarning": "이 토큰은 확인되지 않았습니다. 신뢰할 수 있는 토큰과만 상호 작용하십시오.", "tokenSpamWarning": "이 토큰은 Phantom에서 스팸으로 분류되어 숨겨졌습니다.", "collectibleSpamWarning": "이 콜렉터블은 Phantom에서 스팸으로 분류되어 숨겨졌습니다.", "collectionSpamWarning": "이러한 콜렉터블은 Phantom에서 스팸으로 분류되어 숨겨졌습니다.", "emojiNoResults": "이모티콘 찾지 못함", "emojiSearchResults": "검색 결과", "emojiSuggested": "제안됨", "emojiSmileys": "스마일리 & 피플", "emojiAnimals": "동물 & 자연", "emojiFood": "음식 & 음료", "emojiTravel": "여행 & 명소", "emojiActivities": "활동", "emojiObjects": "물건", "emojiSymbols": "기호", "emojiFlags": "플래그", "whichExtensionToConnectWith": "어떤 확장 프로그램을 연결하시겠습니까?", "configureInSettings": "설정 → 기본 앱 월릿에서 구성할 수 있습니다.", "continueWith": "계속하기:", "useMetaMask": "MetaMask 사용", "usePhantom": "Phantom 사용", "alwaysAsk": "항상 확인", "dontAskMeAgain": "다시 표시 안 함", "selectWalletSettingDescriptionLine1": "일부 앱은 Phantom으로 연결하는 옵션을 제공하지 않을 수 있습니다.", "selectWalletSettingDescriptionLinePhantom": "해결 방법으로, MetaMask가 연결되면 대신 Phantom이 항상 열립니다.", "selectWalletSettingDescriptionLineAlwaysAsk": "해결 방법으로, MetaMask로 연결할 때 대신 Phantom을 사용할 것인지 묻습니다.", "selectWalletSettingDescriptionLineMetaMask": "MetaMask를 기본값을 설정하면 이러한 DApp이 Phantom에 연결되지 않습니다.", "metaMaskOverride": "기본 앱 월릿", "metaMaskOverrideSettingDescriptionLine1": "Phantom 사용 옵션을 제공하지 않는 웹사이트에 연결하는 데 사용됩니다.", "refreshAndReconnectToast": "변경 사항을 적용하려면 새로 고친 후 다시 연결하십시오", "autoConfirmUnavailable": "사용 불가", "autoConfirmReasonDappNotWhitelisted": "원래 계약이 이 앱의 허용 목록에 없으므로 사용할 수 없습니다.", "autoConfirmReasonSessionNotActive": "활성 상태의 자동 확인 세션이 없으므로 사용할 수 없습니다. 아래에서 활성화하십시오.", "autoConfirmReasonRateLimited": "사용 중인 dapp에서 보내는 요청이 너무 많아 사용할 수 없습니다.", "autoConfirmReasonUnsupportedNetwork": "자동 확인이 아직 이 네트워크를 지원하지 않기 때문에 사용할 수 없습니다.", "autoConfirmReasonSimulationFailed": "당사에서 보안을 보장할 수 없으므로 사용할 수 없습니다.", "autoConfirmReasonTabNotFocused": "자동 확인을 시도하려는 도메인의 탭이 활성 상태가 아니므로 사용할 수 없습니다.", "autoConfirmReasonNotUnlocked": "월릿이 잠금 해제되지 않아 사용할 수 없습니다.", "rpcErrorUnauthorizedWrongAccount": "주소의 거래가 선택한 계정 주소와 일치하지 않습니다.", "rpcErrorUnauthorizedUnknownSource": "RPC 요청 소스를 확인할 수 없습니다.", "transactionsDisabledTitle": "거래 비활성화됨", "transactionsDisabledMessage": "해당 주소는 Phantom을 사용하여 거래할 수 없습니다", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "활성", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL을 클립보드에 복사했습니다.", "notEnoughSolScanTransactionWarning": "계정의 SOL이 부족하기 때문에 이 거래가 실패할 수 있습니다. 계정에 SOL을 더 추가한 후 다시 시도하십시오.", "transactionRevertedWarning": "이 거래는 시뮬레이션 중에 되돌아갔습니다. 제출하면 자금이 손실될 수 있습니다.", "slippageToleranceExceeded": "이 거래는 시뮬레이션 중에 되돌아갔습니다. 슬리피지 한도가 초과되었습니다.", "simulationWarningKnownMalicious": "이 계정은 악성 계정인 것 같습니다. 승인하면 자금 손실이 발생할 수 있습니다.", "simulationWarningPoisonedAddress": "이 주소는 최근에 자금을 보낸 주소와 의심스러울 정도로 유사합니다. 이 주소가 올바른지 확인하고 스캠으로 인한 자금 손실을 방지하십시오.", "simulationWarningInteractingWithAccountWithoutActivity": "이전 활동이 없는 계정입니다. 존재하지 않는 계정으로 자금을 보내면 자금 손실이 발생할 수 있습니다.", "quests": "퀘스트", "questsClaimInProgress": "클레임 중", "questsVerifyingCompletion": "퀘스트 완료 확인 중", "questsClaimError": "리워드 클레임 중에 오류 발생", "questsClaimErrorDescription": "리워드를 클레임하는 중에 오류가 발생했습니다. 나중에 다시 시도하세요.", "questsBadgeMobileOnly": "모바일 전용", "questsBadgeExtensionOnly": "확장 전용", "questsExplainerSheetButtonLabel": "확인", "questsNoQuestsAvailable": "사용 가능한 퀘스트가 없습니다", "questsNoQuestsAvailableDescription": "현재 사용 가능한 퀘스트가 없습니다. 새로운 퀘스트가 추가되면 즉시 알려드리겠습니다.", "exploreLearn": "알아보기", "exploreSites": "사이트", "exploreTokens": "토큰", "exploreQuests": "퀘스트", "exploreCollections": "컬렉션", "exploreFilterByall_networks": "모든 네트워크", "exploreSortByrank": "트렌딩", "exploreSortBytrending": "트렌딩", "exploreSortByprice": "가격", "exploreSortByprice-change": "가격 변경", "exploreSortBytop": "상위", "exploreSortByvolume": "볼륨", "exploreSortBygainers": "게이너", "exploreSortBylosers": "루저", "exploreSortBymarket-cap": "시가총액", "exploreSortBymarket_cap": "시가총액", "exploreTimeFrame1h": "1시간", "exploreTimeFrame24h": "24시간", "exploreTimeFrame7d": "7일", "exploreTimeFrame30d": "30일", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "콜렉터블", "exploreCategoryMarketplace": "마켓플레이스", "exploreCategoryGaming": "게임", "exploreCategoryBridges": "브리지", "exploreCategoryOther": "기타", "exploreCategorySocial": "소셜", "exploreCategoryCommunity": "커뮤니티", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "스테이킹", "exploreCategoryArt": "아트", "exploreCategoryTools": "도구", "exploreCategoryDeveloperTools": "개발자 도구", "exploreCategoryHackathon": "해커톤", "exploreCategoryNFTStaking": "NFT 스테이킹", "exploreCategoryExplorer": "탐색기", "exploreCategoryInscriptions": "Inscription", "exploreCategoryBridge": "브리지", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Airdrop 체커", "exploreCategoryPoints": "포인트", "exploreCategoryQuests": "퀘스트", "exploreCategoryShop": "숍", "exploreCategoryProtocol": "프로토콜", "exploreCategoryNamingService": "네이밍 서비스", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "포트폴리오 트래커", "exploreCategoryFitness": "피트니스", "exploreCategoryDePIN": "DePIN", "exploreVolume": "볼륨", "exploreFloor": "최저 가격", "exploreCap": "시가총액", "exploreToken": "토큰", "explorePrice": "가격", "explore24hVolume": "24시간 볼륨", "exploreErrorButtonText": "다시 시도", "exploreErrorDescription": "탐색 콘텐츠를 로드하려는 중에 오류가 발생했습니다. 새로 고친 후 다시 시도하십시오", "exploreErrorTitle": "탐색 콘텐츠를 로드하지 못했습니다", "exploreNetworkError": "네트워크 오류가 발생했습니다. 나중에 다시 시도하십시오.", "exploreTokensLegalDisclaimer": "토큰 목록은 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 및 Jupiter를 포함한 다양한 제3자 제공자가 제공하는 시장 데이터를 사용하여 생성됩니다. 성과는 이전 24시간 기간을 기준으로 합니다. 과거 성과는 미래 성과를 나타내지 않습니다.", "swapperTokensLegalDisclaimer": "트렌딩 토큰 목록은 <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> 및 Jupiter를 포함한 다양한 제3자 제공자의 시장 데이터를 사용하여 생성되며, 명시된 기간 동안 Swapper를 통해 Phantom 사용자가 스왑하는 인기 토큰을 기반으로 합니다. 과거 성과는 미래 성과를 나타내지 않습니다.", "exploreLearnErrorTitle": "학습 콘텐츠를 로드하지 못했습니다", "exploreLearnErrorDescription": "학습 콘텐츠를 로드하려는 중에 오류가 발생했습니다. 새로 고친 후 다시 시도하십시오", "exploreShowMore": "자세히 표시", "exploreShowLess": "간단히 표시", "exploreVisitSite": "사이트 방문", "dappBrowserSearchScreenVisitSite": "사이트 방문", "dappBrowserSearchScreenSearchWithGoogle": "Google로 검색", "dappBrowserSearchScreenSearchLinkYouCopied": "복사한 링크", "dappBrowserExtSearchPlaceholder": "사이트 및 토큰 검색", "dappBrowserSearchNoAppsTokens": "앱 또는 토큰 없음", "dappBrowserTabsLimitExceededScreenTitle": "이전 탭을 닫으시겠습니까?", "dappBrowserTabsLimitExceededScreenDescription": "{{tabsCount}}개 탭이 열려 있습니다. 더 열려면 일부 탭을 닫아야 합니다.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "모든 탭 닫기", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: 이 도메인은 존재하지 않습니다", "dappBrowserTabErrorHttp": "차단되었습니다. HTTPS를 사용하십시오.", "dappBrowserTabError401Unauthorized": "401 승인 안 됨", "dappBrowserTabError501UnhandledRequest": "501 처리되지 않은 요청", "dappBrowserTabErrorTimeout": "시간 초과: 서버가 응답하는 데 너무 오래 걸렸습니다", "dappBrowserTabErrorInvalidResponse": "잘못된 응답", "dappBrowserTabErrorEmptyResponse": "빈 응답", "dappBrowserTabErrorGeneric": "오류 발생함", "localizedErrorUnknownError": "문제가 발생했습니다. 잠시 후 다시 시도해 주십시오.", "localizedErrorUnsupportedCountry": "죄송합니다. 해당 국가는 현재 지원되지 않습니다.", "localizedErrorTokensNotLoading": "해당 토큰을 로드하는 동안 문제가 발생했습니다. 다시 시도해 주십시오.", "localizedErrorSwapperNoQuotes": "지원되지 않는 쌍, 유동성 부족, 금액 부족으로 인해 스왑을 수행할 수 없습니다. 토큰이나 금액을 조정해 보십시오.", "localizedErrorSwapperRefuelNoQuotes": "견적을 찾지 못했습니다. 다른 토큰이나 금액을 사용하거나 충전을 비활성화해 보십시오.", "localizedErrorInsufficientSellAmount": "토큰 금액이 너무 적습니다. 크로스 체인을 스왑하려면 금액을 늘리십시오.", "localizedErrorCrossChainUnavailable": "현재는 크로스 체인 스왑을 사용할 수 없습니다. 나중에 다시 시도하십시오.", "localizedErrorTokenNotTradable": "선택한 코인 중 하나를 거래할 수 없습니다. 다른 토큰을 선택하십시오.", "localizedErrorCollectibleLocked": "토큰 계정이 잠겨 있습니다.", "localizedErrorCollectibleListed": "토큰 계정이 나열되어 있습니다.", "spamActivityAction": "숨겨진 항목 보기", "spamActivityTitle": "숨겨진 활동", "spamActivityWarning": "이 거래는 Phantom에서 스팸으로 분류되어 숨겨졌습니다.", "appAuthenticationFailed": "인증 실패", "appAuthenticationFailedDescription": "인증 시도 문제가 발생했습니다. 다시 시도해 주십시오.", "partialErrorBalanceChainName": "{{chainName}} 잔액을 업데이트하는 데 문제가 있습니다. 귀하의 자금은 안전합니다.", "partialErrorGeneric": "네트워크를 업데이트하는 데 문제가 있습니다. 일부 토큰 잔액과 가격이 최신 상태가 아닐 수 있습니다. 귀하의 자금은 안전합니다.", "partialErrorTokenDetail": "토큰 잔액을 업데이트하는 데 문제가 있습니다. 귀하의 자금은 안전합니다.", "partialErrorTokenPrices": "토큰 가격을 업데이트하는 데 문제가 있습니다. 귀하의 자금은 안전합니다.", "partialErrorTokensTrimmed": "포트폴리오의 일부 토큰을 표시하는 데 문제가 있습니다. 귀하의 자금은 안전합니다.", "publicFungibleDetailAbout": "정보", "publicFungibleDetailYourBalance": "내 잔액", "publicFungibleDetailInfo": "정보", "publicFungibleDetailShowMore": "자세히 표시", "publicFungibleDetailShowLess": "간단히 표시", "publicFungibleDetailPerformance": "24시간 성과", "publicFungibleDetailSecurity": "보안", "publicFungibleDetailMarketCap": "시가총액", "publicFungibleDetailTotalSupply": "총 공급량", "publicFungibleDetailCirculatingSupply": "유통량", "publicFungibleDetailMaxSupply": "최대 공급량", "publicFungibleDetailHolders": "보유자", "publicFungibleDetailVolume": "볼륨", "publicFungibleDetailTrades": "거래", "publicFungibleDetailTraders": "트레이더", "publicFungibleDetailUniqueWallets": "고유한 월릿", "publicFungibleDetailTop10Holders": "톱 10 보유자", "publicFungibleDetailTop10HoldersTooltip": "토큰의 톱 10 보유자가 가진 현재 총 공급량의 퍼센트를 나타냅니다. 가격이 얼마나 쉽게 조작될 수 있는지를 보여주는 척도입니다.", "publicFungibleDetailMintable": "민팅 가능", "publicFungibleDetailMintableTooltip": "토큰이 민팅 가능한 경우 계약 소유자는 토큰 공급을 늘릴 수 있습니다.", "publicFungibleDetailMutableInfo": "변경 가능 정보", "publicFungibleDetailMutableInfoTooltip": "이름, 로고, 웹사이트 주소 등 토큰 정보가 변경 가능한 경우 계약 소유자가 변경할 수 있습니다.", "publicFungibleDetailOwnershipRenounced": "소유권 포기함", "publicFungibleDetailOwnershipRenouncedTooltip": "토큰 소유권을 포기하면 아무도 추가 토큰 민팅 등의 기능을 실행할 수 없습니다.", "publicFungibleDetailUpdateAuthority": "권한 업데이트", "publicFungibleDetailUpdateAuthorityTooltip": "업데이트 권한은 토큰이 변경 가능한 경우 정보를 변경할 수 있는 월릿 주소입니다.", "publicFungibleDetailFreezeAuthority": "동결 기관", "publicFungibleDetailFreezeAuthorityTooltip": "동결 기관은 자금의 이체를 막을 수 있는 지갑 주소입니다.", "publicFungibleUnverifiedToken": "이 토큰은 확인되지 않았습니다. 신뢰할 수 있는 토큰과만 상호 작용하십시오.", "publicFungibleDetailSwap": "{{tokenSymbol}} 스왑", "publicFungibleDetailSwapDescription": "Phantom 앱에서 {{tokenSymbol}} 스왑", "publicFungibleDetailLinkCopied": "클립보드에 복사됨", "publicFungibleDetailContract": "계약", "publicFungibleDetailMint": "민팅", "unifiedTokenDetailTransactionActivity": "활동", "unifiedTokenDetailSeeMoreTransactionActivity": "자세히 보기", "unifiedTokenDetailTransactionActivityError": "최근 활동을 로드하지 못했습니다", "additionalNetworksTitle": "추가 네트워크", "copyAddressRowAdditionalNetworks": "추가 네트워크", "copyAddressRowAdditionalNetworksHeader": "다음 네트워크는 Ethereum과 동일한 주소를 사용합니다.", "copyAddressRowAdditionalNetworksDescription": "이러한 네트워크에서 자산을 보내고 받는 데 Ethereum 주소를 안전하게 사용할 수 있습니다.", "cpeUnknownError": "알 수 없는 오류", "cpeUnknownInstructionError": "알 수 없는 지침 오류", "cpeAccountFrozen": "계정이 동결되었습니다", "cpeAssetFrozen": "자산이 동결되었습니다", "cpeInsufficientFunds": "자금 부족", "cpeInvalidAuthority": "유효하지 않은 기관", "cpeBalanceBelowRent": "임대 면제 기준액 미만 잔액", "cpeNotApprovedForConfidentialTransfers": "기밀 이체용으로 승인되지 않은 계정", "cpeNotAcceptingDepositsOrTransfers": "입금 또는 이체를 수락하지 않는 계정", "cpeNoMemoButRequired": "이전 지침에는 수신자가 송금을 받는 데 필요한 메모가 없습니다", "cpeTransferDisabledForMint": "이 민팅에 대해 이체가 비활성화되었습니다", "cpeDepositAmountExceedsLimit": "입금액이 최대 한도를 초과합니다", "cpeInsufficientFundsForRent": "임대를 위한 잔액 부족", "reportIssueScreenTitle": "문제 신고", "publicFungibleReportIssuePrompt": "{{tokenName}}에 대해 신고할 문제는 무엇인가요?", "publicFungibleReportIssueIncorrectInformation": "잘못된 정보", "publicFungibleReportIssuePriceStale": "가격이 업데이트되지 않습니다", "publicFungibleReportIssuePriceMissing": "가격이 누락되었습니다", "publicFungibleReportIssuePerformanceIncorrect": "24시간 성과가 잘못되었습니다", "publicFungibleReportIssueLinkBroken": "소셜 링크에 액세스할 수 없습니다", "publicFungibleDetailErrorLoading": "토큰 데이터 없음", "reportUserPrompt": "@{{username}}에 대해 신고할 문제는 무엇인가요?", "reportUserOptionAbuseAndHarrassmentTitle": "학대 및 괴롭힘", "reportUserOptionAbuseAndHarrassmentDescription": "표적화된 괴롭힘, 괴롭힘 조장, 폭력적인 위협, 증오적인 콘텐츠 및 레퍼런스", "reportUserOptionPrivacyAndImpersonationTitle": "개인 정보 보호 및 가장", "reportUserOptionPrivacyAndImpersonationDescription": "다른 사람인 것처럼 가장하여, 개인 정보를 공유하거나 공개하겠다고 위협하는 행위", "reportUserOptionSpamTitle": "스팸", "reportUserOptionSpamDescription": "가짜 계정, 스캠, 악성 링크", "reportUserSuccess": "사용자 신고가 제출되었습니다.", "settingsClaimUsernameTitle": "사용자 이름 만들기", "settingsClaimUsernameDescription": "월릿처럼 고유한 ID입니다", "settingsClaimUsernameValueProp1": "단순화된 ID", "settingsClaimUsernameValueProp1Description": "길고 복잡한 주소 대신 사용자 친화적인 ID를 사용할 수 있습니다", "settingsClaimUsernameValueProp2": "더욱 빠르고 간편", "settingsClaimUsernameValueProp2Description": "쉽게 암호화폐를 보내고 받고, 월릿에 로그인하고, 친구들과 연결할 수 있습니다", "settingsClaimUsernameValueProp3": "동기화 상태 유지", "settingsClaimUsernameValueProp3Description": "계정을 사용자 이름에 연결하면 모든 장치에서 동기화됩니다", "settingsClaimUsernameHelperText": "Phantom 계정의 고유한 이름", "settingsClaimUsernameValidationDefault": "이 사용자 이름은 나중에 변경할 수 없습니다", "settingsClaimUsernameValidationAvailable": "사용자 이름 사용 가능", "settingsClaimUsernameValidationUnavailable": "사용자 이름 사용 불가", "settingsClaimUsernameValidationServerError": "사용자 이름이 사용 가능한지 확인할 수 없습니다. 나중에 다시 시도하십시오.", "settingsClaimUsernameValidationErrorLine1": "유효하지 않은 사용자 이름입니다.", "settingsClaimUsernameValidationErrorLine2": "사용자 이름은 {{minChar}} ~ {{maxChar}}자 사이여야 하며 문자와 숫자만 포함할 수 있습니다.", "settingsClaimUsernameValidationLoading": "이 사용자 이름을 사용할 수 있는지 확인하는 중…", "settingsClaimUsernameSaveAndContinue": "저장하고 계속하기", "settingsClaimUsernameChooseAvatarTitle": "아바타 선택", "settingsClaimUsernameAnonymousAuthTitle": "익명 인증", "settingsClaimUsernameAnonymousAuthDescription": "서명을 사용하여 익명으로 Phantom 계정에 로그인합니다", "settingsClaimUsernameAnonymousAuthBadge": "작동 방식 알아보기", "settingsClaimUsernameLinkWalletsTitle": "월릿 링크하기", "settingsClaimUsernameLinkWalletsDescription": "사용자 이름으로 다른 장치에 표시되는 월릿을 선택합니다", "settingsClaimUsernameLinkWalletsBadge": "공개 조회 불가능", "settingsClaimUsernameConnectAccountsTitle": "계정 연결", "settingsClaimUsernameConnectAccountsHelperText": "각 체인 주소는 사용자 이름과 연결됩니다. 나중에 변경할 수 있습니다.", "settingsClaimUsernameContinue": "계속", "settingsClaimUsernameCreateUsername": "사용자 이름 만들기", "settingsClaimUsernameCreating": "사용자 이름을 만드는 중...", "settingsClaimUsernameSuccess": "사용자 이름이 생성되었습니다!", "settingsClaimUsernameError": "사용자 이름을 만드는 중에 오류가 발생했습니다", "settingsClaimUsernameTryAgain": "다시 시도", "settingsClaimUsernameSuccessHelperText": "이제 모든 Phantom 월릿에서 새로운 사용자 이름을 사용할 수 있습니다", "settingsClaimUsernameSettingsSyncedTitle": "동기화된 설정", "settingsClaimUsernameSettingsSyncedHelperText": "길고 복잡한 주소 대신 사용자 친화적인 ID를 사용할 수 있습니다", "settingsClaimUsernameSendToUsernameTitle": "사용자 이름으로 보내기", "settingsClaimUsernameSendToUsernameHelperText": "쉽게 암호화폐를 보내고 받고, 월릿에 로그인하고, 친구들과 연결할 수 있습니다", "settingsClaimUsernameManageAddressesTitle": "공개 주소", "settingsClaimUsernameManageAddressesHelperText": "사용자 이름으로 전송된 모든 토큰 또는 콜렉터블이 해당 주소로 전송됩니다.", "settingsClaimUsernameManageAddressesBadge": "공개 조회 가능", "settingsClaimUsernameEditAddressesTitle": "공개 주소 관리", "settingsClaimUsernameEditAddressesHelperText": "사용자 이름으로 전송된 모든 토큰 또는 콜렉터블이 해당 주소로 전송됩니다. 체인당 하나의 주소를 선택하십시오.", "settingsClaimUsernameEditAddressesError": "네트워크당 하나의 주소만 허용됩니다.", "settingsClaimUsernameEditAddressesEditAddress": "주소 편집", "settingsClaimUsernameNoAddressesSaved": "저장된 공개 주소가 없습니다", "settingsClaimUsernameSave": "저장", "settingsClaimUsernameDone": "완료", "settingsClaimUsernameWatching": "조회 중", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}}개 계정", "settingsClaimUsernameNoOfAccountsSingular": "계정 1개", "settingsClaimUsernameEmptyAccounts": "계정 없음", "settingsClaimUsernameSettingTitle": "내 @사용자이름 생성", "settingsClaimUsernameSettingDescription": "월릿의 고유한 ID입니다", "settingsManageUserProfileAbout": "정보", "settingsManageUserProfileAboutUsername": "사용자 이름", "settingsManageUserProfileAboutBio": "자기소개", "settingsManageUserProfileTitle": "프로필 관리", "settingsManageUserProfileManage": "관리", "settingsManageUserProfileAuthFactors": "인증 요소", "settingsManageUserProfileAuthFactorsDescription": "Phantom 계정에 로그인할 수 있는 시드 문구나 비공개 키를 선택합니다.", "settingsManageUserProfileUpdateAuthFactorsToast": "인증 요소가 업데이트되었습니다!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "인증 요소를 업데이트하지 못했습니다!", "settingsManageUserProfileBiography": "자기소개 편집", "settingsManageUserProfileBiographyDescription": "프로필에 간단한 자기소개를 추가하십시오", "settingsManageUserProfileUpdateBiographyToast": "자기소개가 업데이트되었습니다!", "settingsManageUserProfileUpdateBiographyToastFailure": "문제가 발생했습니다. 다시 시도하십시오", "settingsManageUserProfileBiographyNoUrlMessage": "자기소개에서 모든 URL을 제거하십시오", "settingsManageUserProfileLinkedWallets": "링크된 월릿", "settingsManageUserProfileLinkedWalletsDescription": "Phantom 계정에 로그인할 때 다른 장치에 표시되는 월릿을 선택합니다.", "settingsManageUserProfileUpdateLinkedWalletsToast": "링크된 월릿이 업데이트되었습니다!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "링크된 월릿을 업데이트하지 못했습니다!", "settingsManageUserProfilePrivacy": "개인 정보", "settingsManageUserProfileUpdatePrivacyStateToast": "개인 정보 보호가 업데이트되었습니다!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "개인 정보 보호를 업데이트하지 못했습니다!", "settingsManageUserProfilePublicAddresses": "공개 주소", "settingsManageUserProfileUpdatePublicAddressToast": "공개 주소가 업데이트되었습니다!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "공개 주소를 업데이트하지 못했습니다!", "settingsManageUserProfilePrivacyStatePublic": "공개", "settingsManageUserProfilePrivacyStatePublicDescription": "내 프로필과 공개 주소를 누구나 보고 검색할 수 있습니다", "settingsManageUserProfilePrivacyStatePrivate": "비공개", "settingsManageUserProfilePrivacyStatePrivateDescription": "내 프로필은 누구나 검색할 수 있지만 다른 사용자들이 내 프로필과 공개 주소를 보려면 권한을 요청해야 합니다", "settingsManageUserProfilePrivacyStateInvisible": "표시 안 됨", "settingsManageUserProfilePrivacyStateInvisibleDescription": "내 프로필과 공개 주소가 어디에서나 숨겨지고 검색 가능하지 않습니다", "settingsDownloadPhantom": "Phantom 다운로드", "settingsLogOut": "로그아웃", "seedlessAddAWalletPrimaryText": "월릿 추가", "seedlessAddAWalletSecondaryText": "기존 월릿 로그인 또는 가져오기 ", "seedlessAddSeedlessWalletPrimaryText": "시드 없는 월릿 추가", "seedlessAddSeedlessWalletSecondaryText": "내 Apple ID, Google 또는 이메일 사용", "seedlessCreateNewWalletPrimaryText": "새 월릿 생성?", "seedlessCreateNewWalletSecondaryText": "이 이메일에는 월릿이 없습니다. 월릿을 만드시겠습니까?", "seedlessCreateNewWalletButtonText": "월릿 생성", "seedlessCreateNewWalletNoBundlePrimaryText": "월릿을 찾을 수 없습니다", "seedlessCreateNewWalletNoBundleSecondaryText": "이 이메일에는 월릿이 없습니다", "seedlessCreateNewWalletNoBundleButtonText": "뒤로", "seedlessEmailOptionsPrimaryText": "내 이메일 선택", "seedlessEmailOptionsSecondaryText": "Apple 또는 Google 계정으로 월릿 추가 ", "seedlessEmailOptionsButtonText": "이메일로 계속하기", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Apple ID로 월릿 만들기", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Google 이메일로 월릿 만들기", "seedlessAlreadyExistsPrimaryText": "계정이 이미 존재함", "seedlessAlreadyExistsSecondaryText": "이 이메일에는 생성된 월릿이 이미 있습니다. 대신 로그인하시겠습니까?", "seedlessSignUpWithAppleButtonText": "Apple로 등록하기", "seedlessContinueWithAppleButtonText": "Apple로 계속하기", "seedlessSignUpWithGoogleButtonText": "Google로 등록하기", "seedlessContinueWithGoogleButtonText": "Google로 계속하기", "seedlessCreateAPinPrimaryText": "PIN 생성", "seedlessCreateAPinSecondaryText": "이는 모든 장치에서 월릿을 보호하는 데 사용됩니다. <1>복구 가능하지 않습니다.</1>", "seedlessContinueText": "계속하기", "seedlessConfirmPinPrimaryText": "PIN 확인", "seedlessConfirmPinSecondaryText": "이 PIN을 잊어버리면 새 장치에서 월릿을 복구할 수 없습니다.", "seedlessConfirmPinButtonText": "PIN 생성", "seedlessConfirmPinError": "잘못된 PIN입니다. 다시 시도해 주세요", "seedlessAccountsImportedPrimaryText": "계정 가져옴", "seedlessAccountsImportedSecondaryText": "월릿에 자동으로 이러한 계정을 가져옵니다", "seedlessPreviouslyImportedTag": "이전에 가져옴", "seedlessEnterPinPrimaryText": "PIN 입력", "seedlessEnterPinInvalidPinError": "잘못된 PIN을 입력했습니다. 4자리 숫자만 허용됩니다.", "seedlessEnterPinNumTriesLeft": "남은 시도 횟수는 {{numTries}}회입니다.", "seedlessEnterPinCooldown": "{{minutesLeft}}:{{secondsLeft}} 후에 다시 시도하십시오", "seedlessEnterPinIncorrectLength": "PIN은 정확히 4자리여야 합니다", "seedlessEnterPinMatch": "PIN이 일치합니다", "seedlessDoneText": "완료", "seedlessEnterPinToSign": "이 거래에 서명하려면 PIN을 입력하십시오", "seedlessSigning": "서명", "seedlessCreateSeed": "시드 문구 월릿 만들기", "seedlessImportOptions": "기타 가져오기 옵션", "seedlessImportPrimaryText": "가져오기 옵션", "seedlessImportSecondaryText": "시드 문구, 비공개 키 또는 하드웨어 월릿을 사용하여 기존 월릿을 가져옵니다", "seedlessImportSeedPhrase": "시드 문구 가져오기", "seedlessImportPrivateKey": "비공개 키 가져오기", "seedlessConnectHardwareWallet": "하드웨어 월릿 연결", "seedlessTryAgain": "다시 시도", "seedlessCreatingWalletPrimaryText": "월릿 생성", "seedlessCreatingWalletSecondaryText": "소셜 월릿 추가", "seedlessLoadingWalletPrimaryText": "월릿 로드", "seedlessLoadingWalletSecondaryText": "링크된 월릿 가져오기 및 조회", "seedlessLoadingWalletErrorPrimaryText": "월릿을 로드하지 못함", "seedlessCreatingWalletErrorPrimaryText": "월릿을 만들지 못함", "seedlessErrorSecondaryText": "다시 시도하십시오", "seedlessAuthAlreadyExistsErrorText": "제공된 이메일은 이미 다른 Phantom 계정에 속해 있습니다", "seedlessAuthUnknownErrorText": "알 수 없는 오류가 발생했습니다. 나중에 다시 시도하십시오.", "seedlessAuthUnknownErrorTextRefresh": "알 수 없는 오류가 발생했습니다. 나중에 다시 시도하십시오. 페이지를 새로 고쳐 다시 시도하십시오.", "seedlessAuthErrorCloseWindow": "창 닫기", "seedlessWalletExistsErrorPrimaryText": "장치에 소셜 월릿이 이미 있습니다", "seedlessWalletExistsErrorSecondaryText": "뒤로 돌아가거나 이 화면을 닫으십시오", "seedlessValueProp1PrimaryText": "원활한 설정", "seedlessValueProp1SecondaryText": "Google 또는 Apple 계정을 사용하여 월릿을 만들고 손쉽게 Web3 탐색을 시작하세요", "seedlessValueProp2PrimaryText": "개선된 보안", "seedlessValueProp2SecondaryText": "사용자 월릿은 안전하게 저장되고 여러 요소에서 분산됩니다", "seedlessValueProp3PrimaryText": "간편한 복구", "seedlessValueProp3SecondaryText": "Google 또는 Apple 계정과 4자리 PIN으로 월릿 액세스를 복구합니다", "seedlessLoggingIn": "로그인 중...", "seedlessSignUpOrLogin": "등록 또는 로그인", "seedlessContinueByEnteringYourEmail": "이메일을 입력하여 계속하십시오", "seedless": "시드 없음", "seed": "시드 문구", "seedlessVerifyPinPrimaryText": "PIN 확인", "seedlessVerifyPinSecondaryText": "계속하려면 PIN 번호를 입력하십시오", "seedlessVerifyPinVerifyButtonText": "확인", "seedlessVerifyPinForgotButtonText": "잊어버리셨습니까?", "seedlessPinConfirmButtonText": "확인", "seedlessVerifyToastPrimaryText": "내 PIN 확인", "seedlessVerifyToastSecondaryText": "잊어버리지 않도록 때때로 PIN 확인을 요청드릴 것입니다. 잊어버리면 월릿을 복구할 수 없습니다.", "seedlessVerifyToastSuccessText": "PIN 번호가 확인되었습니다!", "seedlessForgotPinPrimaryText": "다른 장치를 사용하여 PIN 재설정", "seedlessForgotPinSecondaryText": "보안을 위해, 로그인한 다른 장치에서만 PIN을 재설정할 수 있습니다", "seedlessForgotPinInstruction1PrimaryText": "다른 장치 열기", "seedlessForgotPinInstruction1SecondaryText": "이메일로 Phantom 계정에 로그인한 다른 장치로 이동하십시오", "seedlessForgotPinInstruction2PrimaryText": "설정으로 이동", "seedlessForgotPinInstruction2SecondaryText": "설정에서 \"보안 및 개인 정보\"를 선택하고 \"PIN 재설정\"을 선택하십시오", "seedlessForgotPinInstruction3PrimaryText": "새로운 PIN 설정", "seedlessForgotPinInstruction3SecondaryText": "새로운 PIN을 설정하면 이제 이 장치에서 월릿에 로그인할 수 있습니다", "seedlessForgotPinButtonText": "해당 단계를 완료했습니다", "seedlessResetPinPrimaryText": "PIN 재설정", "seedlessResetPinSecondaryText": "기억할 수 있는 새로운 PIN을 입력하십시오. 이 PIN은 모든 장치에서 월릿을 보호하는 데 사용됩니다", "seedlessResetPinSuccessText": "PIN 번호가 업데이트되었습니다!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "월릿을 만들면 <1>서비스 약관</1> 및 <5>개인정보 처리방침</5>에 동의하는 것입니다.", "pageNotFound": "페이지를 찾을 수 없습니다", "pageNotFoundDescription": "죄송하지만, 이 페이지는 존재하지 않거나 이동되었습니다.", "webTokenPagesLegalDisclaimer": "가격 정보는 정보 참조용으로만 제공되며 금융 조언이 아닙니다. 시장 데이터는 제3자가 제공하며 Phantom은 정보의 정확성에 대해 어떠한 진술도 하지 않습니다.", "signUpOrLogin": "등록 또는 로그인", "portalOnboardingAgreeToTermsOfServiceInterpolated": "계정을 만들면 <1>서비스 약관</1> 및 <5>개인정보 처리방침</5>에 동의하는 것입니다.", "feedNoActivity": "아직 활동 없음", "followRequests": "팔로우 요청", "following": "팔로잉", "followers": "팔로워", "follower": "팔로워", "joined": "참여함", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "팔로워 없음", "noFollowing": "팔로잉 없음", "noUsersFound": "사용자를 찾을 수 없음", "viewProfile": "프로필 보기", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}