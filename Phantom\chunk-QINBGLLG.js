import{c as wo,f as To,h as Ae,i as he,j as be,k as Po,l as xo,n as I,o as at,q as Co,r as St,s as b,u as Vt,v as Xt,x as Lo}from"./chunk-HRJWTAGT.js";import{n as bo,v as vo}from"./chunk-V5T43K7V.js";import{Bb as $,Ca as mt,Cb as ft,Fa as go,Ja as Bt,L as le,Qb as Se,R as me,S as fe,T as z,fc as kt,hc as H,ka as So,nc as ho,oa as Rt,pb as yo,pc as Kt,qa as Oe,qb as Ao,wa as We,xc as ge,zc as ye}from"./chunk-OUYKWOVO.js";import{d as ke}from"./chunk-OYGO47TI.js";import{$d as Nt,G as io,L as de,M as so,N as Et,O as R,P as S,Pa as T,Pb as fo,Q as nt,Ra as Ut,T as rt,Wa as mo,Xa as Ft,pe as V,rd as Jt,v as ao,wc as pe,wd as ko}from"./chunk-MZZEJ42N.js";import{a as w,m as lt}from"./chunk-56SJOU6P.js";import{$ as Tt,a as O,aa as uo,b as F,ea as po,fa as pt,ga as lo,x as qt}from"./chunk-ALUTR72U.js";import{Ya as co,ia as K,j as ro,ka as D,la as j,q as qn,ta as W}from"./chunk-L3A2KHJO.js";import{a as ue}from"./chunk-4P36KWOF.js";import{a as E}from"./chunk-7X4NV6OJ.js";import{c as En,f as L,h as s,m as Buffer,n as c}from"./chunk-3KENBVE7.js";var ln=En(h=>{"use strict";s();c();var Fr=h&&h.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(h,"__esModule",{value:!0});h.map=h.array=h.rustEnum=h.str=h.vecU8=h.tagged=h.vec=h.bool=h.option=h.publicKey=h.i256=h.u256=h.i128=h.u128=h.i64=h.u64=h.struct=h.f64=h.f32=h.i32=h.u32=h.i16=h.u16=h.i8=h.u8=void 0;var M=So(),Nr=ao(),Rr=Fr(ro()),ct=So();Object.defineProperty(h,"u8",{enumerable:!0,get:function(){return ct.u8}});Object.defineProperty(h,"i8",{enumerable:!0,get:function(){return ct.s8}});Object.defineProperty(h,"u16",{enumerable:!0,get:function(){return ct.u16}});Object.defineProperty(h,"i16",{enumerable:!0,get:function(){return ct.s16}});Object.defineProperty(h,"u32",{enumerable:!0,get:function(){return ct.u32}});Object.defineProperty(h,"i32",{enumerable:!0,get:function(){return ct.s32}});Object.defineProperty(h,"f32",{enumerable:!0,get:function(){return ct.f32}});Object.defineProperty(h,"f64",{enumerable:!0,get:function(){return ct.f64}});Object.defineProperty(h,"struct",{enumerable:!0,get:function(){return ct.struct}});var ht=class extends M.Layout{constructor(e,o,n){super(e,n),this.blob=(0,M.blob)(e),this.signed=o}decode(e,o=0){let n=new Rr.default(this.blob.decode(e,o),10,"le");return this.signed?n.fromTwos(this.span*8).clone():n}encode(e,o,n=0){return this.signed&&(e=e.toTwos(this.span*8)),this.blob.encode(e.toArrayLike(Buffer,"le",this.span),o,n)}};function dn(t){return new ht(8,!1,t)}h.u64=dn;function Br(t){return new ht(8,!0,t)}h.i64=Br;function Kr(t){return new ht(16,!1,t)}h.u128=Kr;function Vr(t){return new ht(16,!0,t)}h.i128=Vr;function _r(t){return new ht(32,!1,t)}h.u256=_r;function Or(t){return new ht(32,!0,t)}h.i256=Or;var it=class extends M.Layout{constructor(e,o,n,r){super(e.span,r),this.layout=e,this.decoder=o,this.encoder=n}decode(e,o){return this.decoder(this.layout.decode(e,o))}encode(e,o,n){return this.layout.encode(this.encoder(e),o,n)}getSpan(e,o){return this.layout.getSpan(e,o)}};function Wr(t){return new it((0,M.blob)(32),e=>new Nr.PublicKey(e),e=>e.toBuffer(),t)}h.publicKey=Wr;var Je=class extends M.Layout{constructor(e,o){super(-1,o),this.layout=e,this.discriminator=(0,M.u8)()}encode(e,o,n=0){return e==null?this.discriminator.encode(0,o,n):(this.discriminator.encode(1,o,n),this.layout.encode(e,o,n+1)+1)}decode(e,o=0){let n=this.discriminator.decode(e,o);if(n===0)return null;if(n===1)return this.layout.decode(e,o+1);throw new Error("Invalid option "+this.property)}getSpan(e,o=0){let n=this.discriminator.decode(e,o);if(n===0)return 1;if(n===1)return this.layout.getSpan(e,o+1)+1;throw new Error("Invalid option "+this.property)}};function Qr(t,e){return new Je(t,e)}h.option=Qr;function Gr(t){return new it((0,M.u8)(),Hr,jr,t)}h.bool=Gr;function Hr(t){if(t===0)return!1;if(t===1)return!0;throw new Error("Invalid bool: "+t)}function jr(t){return t?1:0}function zr(t,e){let o=(0,M.u32)("length"),n=(0,M.struct)([o,(0,M.seq)(t,(0,M.offset)(o,-o.span),"values")]);return new it(n,({values:r})=>r,r=>({values:r}),e)}h.vec=zr;function $r(t,e,o){let n=(0,M.struct)([dn("tag"),e.replicate("data")]);function r({tag:a,data:u}){if(!a.eq(t))throw new Error("Invalid tag, expected: "+t.toString("hex")+", got: "+a.toString("hex"));return u}return new it(n,r,a=>({tag:t,data:a}),o)}h.tagged=$r;function pn(t){let e=(0,M.u32)("length"),o=(0,M.struct)([e,(0,M.blob)((0,M.offset)(e,-e.span),"data")]);return new it(o,({data:n})=>n,n=>({data:n}),t)}h.vecU8=pn;function Yr(t){return new it(pn(),e=>e.toString("utf-8"),e=>Buffer.from(e,"utf-8"),t)}h.str=Yr;function Jr(t,e,o){let n=(0,M.union)(o??(0,M.u8)(),e);return t.forEach((r,a)=>n.addVariant(a,r,r.property)),n}h.rustEnum=Jr;function Xr(t,e,o){let n=(0,M.struct)([(0,M.seq)(t,e,"values")]);return new it(n,({values:r})=>r,r=>({values:r}),o)}h.array=Xr;var Xe=class extends M.Layout{constructor(e,o,n){super(e.span+o.span,n),this.keyLayout=e,this.valueLayout=o}decode(e,o){o=o||0;let n=this.keyLayout.decode(e,o),r=this.valueLayout.decode(e,o+this.keyLayout.getSpan(e,o));return[n,r]}encode(e,o,n){n=n||0;let r=this.keyLayout.encode(e[0],o,n),a=this.valueLayout.encode(e[1],o,n+r);return r+a}getSpan(e,o){return this.keyLayout.getSpan(e,o)+this.valueLayout.getSpan(e,o)}};function Zr(t,e,o){let n=(0,M.u32)("length"),r=(0,M.struct)([n,(0,M.seq)(new Xe(t,e),(0,M.offset)(n,-n.span),"values")]);return new it(r,({values:a})=>new Map(a),a=>({values:Array.from(a.entries())}),o)}h.map=Zr});s();c();s();c();var ve=L(ao(),1);function Qe(t,e,o,n,r=Rt,a=Oe){return Un(t,e,o,n,Buffer.from([1]),r,a)}function Un(t,e,o,n,r,a=Rt,u=Oe){let i=[{pubkey:t,isSigner:!0,isWritable:!0},{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:o,isSigner:!1,isWritable:!1},{pubkey:n,isSigner:!1,isWritable:!1},{pubkey:ve.SystemProgram.programId,isSigner:!1,isWritable:!1},{pubkey:a,isSigner:!1,isWritable:!1}];return new ve.TransactionInstruction({keys:i,programId:u,data:r})}s();c();var gt=new Error("Missing sender address for staking fungibles."),Io=new Error("Missing signature from waiting for staking transaction confirmation."),Do=new Error("PublicKey is required");var Pt=class extends Error{constructor(e){super(e)}};function Fn({stakePubkey:t,stakeAuthorityPubkey:e,authorizedPubkey:o,transaction:n=new R.Transaction}){return e.equals(o)||n.add(S.StakeProgram.authorize({authorizedPubkey:o,newAuthorizedPubkey:o,stakeAuthorizationType:S.StakeAuthorizationLayout.Staker,stakePubkey:t})),n.add(S.StakeProgram.deactivate({stakePubkey:t,authorizedPubkey:o})),n}var Nn=t=>{let{t:e}=lt(),o=K(),n=St(),{mutateAsync:r}=ft(t);return j({mutationFn:async({accountIdentifier:i,senderAddress:d,stakePubkey:p,stakeAuthorityPubkey:m,authorizedPubkey:l})=>{if(!d)throw gt;let{networkID:k,address:f}=d,y=Fn({stakePubkey:p,stakeAuthorityPubkey:m,authorizedPubkey:l}),g=await z(y,{connection:t}),x={ownerAddress:f??"",networkID:k,data:{signature:""},type:"deactivateState",display:{summary:{topLeft:{text:e("transactionsPendingDeactivatingStake")}}}};n.approved({chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(k),type:"undelegate"});let C=await r({accountIdentifier:i,feePayer:new S.PublicKey(f),transaction:g,pendingTransactionInput:x});return n.submittedTransaction(C,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(k),method:"signAndSendTransaction"}),n.deactivateStake(),{networkID:k,id:C??""}},onSuccess:i=>{mt({connection:t,signature:i.id}).then(()=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"confirmed"}}),at(o)()}).catch(d=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"error"}}),W.captureError(d,"staking")})}})};s();c();function Ge({stakePubkey:t,authorizedPubkey:e,votePubkey:o}){return S.StakeProgram.delegate({stakePubkey:t,authorizedPubkey:e,votePubkey:o})}var Rn=t=>{let{t:e}=lt(),o=K(),n=St(),{mutateAsync:r}=ft(t);return j({mutationFn:async({accountIdentifier:i,senderAddress:d,stakePubkey:p,authorizedPubkey:m,votePubkey:l})=>{if(!d)throw gt;let{networkID:k,address:f}=d,y=Ge({stakePubkey:p,authorizedPubkey:m,votePubkey:l}),g=await z(y,{connection:t}),x={ownerAddress:f??"",networkID:k,data:{signature:""},type:"delegateStake",display:{summary:{topLeft:{text:e("transactionsPendingDelegatingStake")}}}};n.approved({chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(k),type:"delegate"});let C=await r({accountIdentifier:i,feePayer:new S.PublicKey(f),transaction:g,pendingTransactionInput:x});return n.submittedTransaction(C,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(k),method:"signAndSendTransaction"}),{networkID:k,id:C??""}},onSuccess:i=>{mt({connection:t,signature:i.id}).then(()=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"confirmed"}}),at(o)()}).catch(d=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"error"}}),W.captureError(d,"staking")})}})};s();c();s();c();var Mo=({numSignatures:t,lamportsPerSignature:e,lamportBalance:o,rentExemptionMinimum:n})=>{let r=O(e).multipliedBy(t),a=o.minus(r);return O.max(a.minus(n),0)};var Bn=({balance:t,rentExemptionMinimum:e})=>{let o=Mo({numSignatures:11,lamportsPerSignature:Bt,lamportBalance:O(t),rentExemptionMinimum:so(e)});return parseFloat(de(o).toFixed(9))};s();c();s();c();var Kn="https://keybase.io/_/api/1.0/user/pic_url.json",Te=async t=>{let e=await nt.get(`${Kn}?username=${encodeURIComponent(t)}`);if(!rt(e)||e.data.status?.code!==0)throw new Error("Couldn't fetch keybase user avatar");return e.data.pic_url};var Vn=t=>{let e=I.keybaseUserAvatar(t);return D({enabled:!!t,queryKey:e,async queryFn(){if(!t)throw new Error("Keybase username is required");return Te(t)},placeholderData:"",staleTime:1/0})};s();c();s();c();var Eo=new Error("Failed to fetch recommended staking validators.");async function qo(){try{let t=await nt.api().get("/staking/v1/recommended");if(!rt(t))throw Eo;let e=t.data;return Po.parse(e).validators}catch{throw Eo}}var _n=()=>{let t=I.recommendedValidators();return D({enabled:!0,queryKey:t,gcTime:Vt.Long,staleTime:1/0,async queryFn(){return qo()}})};s();c();var On=async(t,e)=>await t.getSignatureStatuses([e],{searchTransactionHistory:!0});function Wn(t,e,o){let n=K(),r=I.signatureStatus(o),a=async()=>{if(!o)throw Io;let p=await On(t,o);return{...p,value:p.value[0]}},i=n.getQueryData(r)?.value?.confirmationStatus;return D({queryKey:r,queryFn:a,enabled:!!o&&i!=="finalized",refetchInterval:e,staleTime:1/0})}s();c();var Qn=t=>async()=>await t.getMinimumBalanceForRentExemption(S.StakeProgram.space);function Gn(t){let e=I.stakeAccountMinBalance(),o=Qn(t);return D({queryKey:e,queryFn:o,placeholderData:0,staleTime:1/0})}s();c();var Hn=async(t,e,o,n="confirmed")=>await t.getInflationReward(e,o,n),jn=async(t,e)=>{let o=[];e.ownerPublicKey&&o.push({memcmp:{offset:44,bytes:e.ownerPublicKey}}),e.voteAccountPublicKey&&o.push({dataSize:200},{memcmp:{offset:124,bytes:e.voteAccountPublicKey}});let n=await t.getParsedProgramAccounts(S.StakeProgram.programId,{filters:o}),r=[];if(e.fetchInflationRewards)try{r=await Hn(t,n.map(({pubkey:a})=>a))}catch(a){let u=a;!u.message.includes("RPC request timed out")&&W.captureError(u,"staking")}return n.map((a,u)=>({pubkey:a.pubkey.toString(),lamports:a.account.lamports,inflationReward:r[u]?.amount,...a.account.data.parsed}))},zn=(t,e)=>I.stakeAccounts(t,e),$n=(t,e)=>()=>jn(t,{ownerPublicKey:e,fetchInflationRewards:!0});function He(t,e){let o=zn(e,t.rpcEndpoint),n=$n(t,e);return D({queryKey:o,queryFn:n,placeholderData:[],staleTime:1/0})}s();c();s();c();s();c();s();c();function Pe(t,e){for(let o of e)if(o.epoch===t)return o;return null}var Uo=.09;function Yn(t,e,o){if(t.activationEpoch===t.deactivationEpoch)return{effective:BigInt(0),activating:BigInt(0)};if(e===t.activationEpoch)return{effective:BigInt(0),activating:t.stake};if(e<t.activationEpoch)return{effective:BigInt(0),activating:BigInt(0)};let n=t.activationEpoch,r=Pe(n,o);if(r!==null){let a=BigInt(0);for(;r!==null;){n++;let u=t.stake-a,i=Number(u)/Number(r.activating),d=Number(r.effective)*Uo,p=BigInt(Math.max(1,Math.round(i*d)));if(a+=p,a>=t.stake){a=t.stake;break}if(n>=e||n>=t.deactivationEpoch)break;r=Pe(n,o)}return{effective:a,activating:t.stake-a}}else return{effective:t.stake,activating:BigInt(0)}}function Fo(t,e,o){let{effective:n,activating:r}=Yn(t,e,o);if(e<t.deactivationEpoch)return{effective:n,activating:r,deactivating:BigInt(0)};if(e==t.deactivationEpoch)return{effective:n,activating:BigInt(0),deactivating:n};let a=t.deactivationEpoch,u=Pe(a,o);if(u!==null){let i=n;for(;u!==null&&(a++,u.deactivating!==BigInt(0));){let d=Number(i)/Number(u.deactivating),p=Number(u.effective)*Uo,m=BigInt(Math.max(1,Math.round(d*p)));if(i-=m,i<=0){i=BigInt(0);break}if(a>=e)break;u=Pe(a,o)}return{effective:i,deactivating:i,activating:BigInt(0)}}else return{effective:BigInt(0),activating:BigInt(0),deactivating:BigInt(0)}}s();c();var No=function(t){if(t.value===null||t.value.data instanceof Buffer)throw new Error("Account not found");let e=[];return t.value.data.parsed.info.forEach(o=>{e.push({epoch:BigInt(o.epoch),effective:BigInt(o.stakeHistory.effective),activating:BigInt(o.stakeHistory.activating),deactivating:BigInt(o.stakeHistory.deactivating)})}),e},Ro=function(t){let e=BigInt(0);if(t.value===null||t.value.data instanceof Buffer)throw new Error("Account not found");return t.value.data.parsed.type==="delegated"&&(e=BigInt(1)),{discriminant:e,meta:{rentExemptReserve:BigInt(t.value.data.parsed.info.meta.rentExemptReserve),authorized:{staker:t.value.data.parsed.info.meta.authorized.staker,withdrawer:t.value.data.parsed.info.meta.authorized.withdrawer},lockup:{unixTimestamp:BigInt(t.value.data.parsed.info.meta.lockup.unixTimestamp),epoch:BigInt(t.value.data.parsed.info.meta.lockup.epoch),custodian:t.value.data.parsed.info.meta.lockup.custodian}},stake:{delegation:{voterPubkey:t.value.data.parsed.info.stake.delegation.voterPubkey,stake:BigInt(t.value.data.parsed.info.stake.delegation.stake),activationEpoch:BigInt(t.value.data.parsed.info.stake.delegation.activationEpoch),deactivationEpoch:BigInt(t.value.data.parsed.info.stake.delegation.deactivationEpoch)},creditsObserved:BigInt(t.value.data.parsed.info.stake.creditsObserved)}}};async function te(t,e){let o=new S.PublicKey("SysvarStakeHistory1111111111111111111111111"),n=await t.getParsedAccountInfo(e);if(n===null||n.value===null)throw new Error("Account not found");let r=await t.getParsedAccountInfo(o);if(r===null)throw new Error("StakeHistory not found");let a=No(r),u=await t.getEpochInfo(),i=Ro(n),{effective:d,activating:p,deactivating:m}=Fo(i.stake.delegation,BigInt(u.epoch),a),l;m>0?l="deactivating":p>0?l="activating":d>0?l="active":l="inactive";let k=BigInt(n.value.lamports)-d-i.meta.rentExemptReserve;return{state:l,active:Number(d),inactive:Number(k)}}function Jn(t,e){let o=I.stakeActivationData(e);return D({queryKey:o,queryFn:async()=>{if(!e)throw Do;return await te(t,new S.PublicKey(e))},enabled:!!e,staleTime:1/0})}s();c();s();c();var Bo=new Error("Failed to fetch staking APY");async function Ko(){try{let t=await nt.api().get("/staking/v1/apy");if(!rt(t))throw Bo;let e=t.data;return be.parse(e).apy}catch{throw Bo}}var Xn=()=>{let t=I.stakeApy();return D({enabled:!0,queryKey:t,gcTime:Vt.Short,staleTime:1/0,async queryFn(){return Ko()}})};s();c();function Zn({stakePubkey:t,authorizedPubkey:e,amount:o,transaction:n=new R.Transaction}){return n.add(S.StakeProgram.withdraw({stakePubkey:t,authorizedPubkey:e,toPubkey:e,lamports:o})),n}var tr=t=>{let{t:e}=lt(),o=K(),n=St(),{mutateAsync:r}=ft(t);return j({mutationFn:async({accountIdentifier:i,senderAddress:d,stakePubkey:p,authorizedPubkey:m,amount:l})=>{if(!d)throw gt;let{networkID:k,address:f}=d,y=Zn({stakePubkey:p,authorizedPubkey:m,amount:l}),g=await z(y,{connection:t}),x={ownerAddress:f??"",networkID:k,data:{signature:""},type:"withdrawStake",display:{summary:{topLeft:{text:e("transactionsPendingWithdrawingStake")}}}};n.approved({chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(k),type:"withdraw"});let C=await r({accountIdentifier:i,feePayer:new S.PublicKey(f),transaction:g,pendingTransactionInput:x});return n.submittedTransaction(C,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(k),method:"signAndSendTransaction"}),{networkID:k,id:C??""}},onSuccess:i=>{mt({connection:t,signature:i.id}).then(()=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"confirmed"}}),at(o)()}).catch(d=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"error"}}),W.captureError(d,"staking")})}})};s();c();function er({ownerPublicKey:t,lamports:e,transaction:o=new R.Transaction}){let n=S.Keypair.generate();return o.add(S.StakeProgram.createAccount({fromPubkey:t,stakePubkey:n.publicKey,authorized:new S.Authorized(t,t),lamports:e})),[o,n]}function or({lamports:t,votePubkey:e,authorizedPubkey:o}){let[n,r]=er({ownerPublicKey:o,lamports:t});return n.add(Ge({stakePubkey:r.publicKey,authorizedPubkey:o,votePubkey:e})),[n,r]}var nr=t=>{let{t:e}=lt(),o=K(),n=St(),{mutateAsync:r}=ft(t);return j({mutationFn:async({accountIdentifier:i,senderAddress:d,lamports:p,votePubkey:m,authorizedPubkey:l,usdPerSol:k})=>{if(!d)throw gt;let{networkID:f,address:y}=d,[g,x]=or({accountIdentifier:i,senderAddress:d,lamports:p,votePubkey:m,authorizedPubkey:l}),C=await z(g,{connection:t}),P={ownerAddress:y??"",networkID:f,data:{signature:""},type:"createAccountAndDelegateStake",display:{summary:{topLeft:{text:e("transactionsPendingCreatingAndDelegatingStake")}}}};n.approved({chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(f),type:"delegate"});let N=await r({accountIdentifier:i,feePayer:new S.PublicKey(y),transaction:C,pendingTransactionInput:P,opts:{additionalSignerAccounts:[x]}});n.submittedTransaction(N,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(f),method:"signAndSendTransaction"});let J=de(p),X=k?J.times(k):void 0;return n.createStakeAccountAndDelegateStake({sol:J.toNumber(),usd:X?.toNumber(),voteAccountPubkey:m}),{networkID:f,id:N??""}},onSuccess:i=>{mt({connection:t,signature:i.id}).then(()=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"confirmed"}}),at(o)()}).catch(d=>{n.transactionStatus(i.id,{chainType:"solana",chainName:"solana",networkId:T.getSolanaNetworkIDValue(i.networkID),status:{type:"error"}}),W.captureError(d,"staking")})}})};s();c();var ee=L(E());s();c();var Vo=L(E());var rr=t=>{let e=[];return t.ownerPublicKey&&e.push({memcmp:{offset:44,bytes:t.ownerPublicKey}}),t.voteAccountPublicKey&&e.push({dataSize:200},{memcmp:{offset:124,bytes:t.voteAccountPublicKey}}),e},_o=async(t,e,o)=>{let n=rr(e),r=await t.getParsedProgramAccounts(S.StakeProgram.programId,{filters:n}),u=(await Promise.allSettled(r.map(p=>te(t,p.pubkey)))).map(p=>{if(p.status==="fulfilled")return p.value.state}),d=(await Promise.allSettled(r.map(p=>{let m=p.account.data.parsed.info?.stake.delegation.voter,l=o.get(m),k=l?.info?.keybaseUsername,f=l?.info?.iconUrl;return f?new Promise(y=>y(f)):k?Te(k):new Promise(y=>y(null))}))).map(p=>{if(p.status==="fulfilled"&&p.value!==null)return p.value});return r.map((p,m)=>({pubkey:p.pubkey.toString(),lamports:p.account.lamports,activationState:u[m],imageUri:d[m],...p.account.data.parsed}))},Oo=(t,e)=>I.convertStakeAccountList(t,e),ar=(t,e,o)=>()=>_o(t,{ownerPublicKey:e},o);function Wo(t,e,o,n=!0){let r=Oo(e,t.rpcEndpoint),a=ar(t,e,o);return D({queryKey:r,queryFn:a,enabled:!!e&&n,staleTime:1/0})}var Qo=(t,e,o,n)=>{let r=K();return(0,Vo.useCallback)(async()=>{n&&await r.prefetchQuery({queryKey:Oo(e,t.rpcEndpoint),queryFn:()=>_o(t,{ownerPublicKey:e},o)})},[r,e,t,o,n])};s();c();var Y=L(E()),xe=null,ir=t=>{if(!xe&&t){let e="liquidStake";xe=new ke(t,e)}if(!xe)throw new Error("liquidStakeAnalytics was not properly configured");return xe},tt=()=>{let t=pe(),e=ir(t),o=(0,Y.useCallback)((f,y)=>{t.capture("liquidStakingConvertAccountClicked",{data:{stake:{validatorName:f,stakeAccountBalance:y}}})},[t]),n=(0,Y.useCallback)(f=>{t.capture("liquidStakingConvertAccountListSeen",{data:{stake:{stakeAccounts:f}}})},[t]),r=(0,Y.useCallback)(()=>{t.capture("liquidStakingConvertBannerClicked")},[t]),a=(0,Y.useCallback)(()=>{t.capture("liquidStakingConvertBannerSeen")},[t]),u=(0,Y.useCallback)(()=>{t.capture("liquidStakingConvertModalContinue")},[t]),i=(0,Y.useCallback)(()=>{t.capture("liquidStakingConvertModalNotNow")},[t]),d=(0,Y.useCallback)(({provider:f,convertDetails:y,amount:g,fromAmount:x,toAmount:C,fromAmountUSD:P,toAmountUSD:N})=>{t.capture("liquidStakingConvertApprovedByUser",{data:{stake:{stakePoolAddress:f?.stakePoolAddress,token:f?.stakePoolTokenMetadata.metadata.name,fromAmount:x,toAmount:C,fromAmountUSD:P,toAmountUSD:N,apy:f?.apy,convertRatio:f?.convertRatio,caip19:f?.stakePoolTokenMetadata.caip19,validatorName:y?.validatorName,stakeAccountBalance:g}}})},[t]),p=(0,Y.useCallback)(f=>{e.submittedTransaction(f,{chainType:"solana",chainName:"solana",networkId:"mainnet",method:"signAndSendTransaction"})},[e]),m=(0,Y.useCallback)(({amount:f,convertDetails:y,provider:g})=>{t.capture("liquidStakingConvertSuccess",{data:{stake:{apy:g?.apy,convertRatio:g?.convertRatio,caip19:g?.stakePoolTokenMetadata.caip19,stakePoolAddress:g?.stakePoolAddress,token:g?.stakePoolTokenMetadata.metadata.name,totalAmount:f,validatorName:y?.validatorName}}})},[t]),l=(0,Y.useCallback)(({amount:f,convertDetails:y})=>{t.capture("liquidStakingConvertFailed",{data:{stake:{validatorName:y?.validatorName,stakeAccountBalance:f}}})},[t]),k=(0,Y.useCallback)(()=>{t.capture("liquidStakingConvertMoreClicked")},[t]);return{trackConvertStakeAccountClicked:o,trackConvertStakeAccountListSeen:n,trackConvertBannerClicked:r,trackConvertBannerSeen:a,trackConvertModalContinue:u,trackConvertModalNotNow:i,trackConvertApprovedByUser:d,trackConvertFailed:l,trackConvertSubmitted:p,trackConvertSuccess:m,trackConvertMoreClicked:k}};var sr=t=>{let{connection:e}=$(),{data:o}=V("solana"),n=b(m=>m.setSkipDismissRouting),{trackConvertModalContinue:r,trackConvertModalNotNow:a}=tt(),{results:u,isFetched:i}=Xt(e),d=(0,ee.useMemo)(()=>new Map(u.map(m=>[m.voteAccountPubkey,m])),[u]),p=Qo(e,o?.address??"",d,i);return(0,ee.useEffect)(()=>{p()},[p]),(0,ee.useEffect)(()=>{n(t)},[t,n]),{trackConvertModalContinue:r,trackConvertModalNotNow:a}};s();c();var Wt=L(E());s();c();var Ot=L(E());var oe=t=>{let e=t?.validators,o=(0,Ot.useMemo)(()=>e?new Map(e.map(P=>[P.vote_account,P])):new Map,[e]),{connection:n}=$(),{data:r}=V("solana"),{results:a,isFetched:u,isError:i}=Xt(n),d=(0,Ot.useMemo)(()=>new Map(a.map(P=>[P.voteAccountPubkey,P])),[a]),{data:p,isLoading:m,isFetching:l,error:k,refetch:f}=Wo(n,r?.address??"",d,u||i),y=m||l,g=(0,Ot.useMemo)(()=>y||!p?[]:p.map(P=>cr(P,d,o)).sort((P,N)=>P.lamports>N.lamports?-1:1),[y,p,d,o]),x=(0,Ot.useMemo)(()=>g.filter(P=>P.eligible),[g]),C=(0,Ot.useMemo)(()=>g.filter(P=>!P.eligible),[g]);return{hasConvertibleStakeAccounts:x.length>0,eligibleStakeAccounts:x,inelegibleStakeAccounts:C,isLoadingList:y,error:k,refetch:f}},cr=(t,e,o)=>{let n=Lo(t,o),r=ur(t,e),a=t.lamports;return{...t,amount:a,eligible:n,title:r,imageUri:t.imageUri}},ur=(t,e)=>{let o="";if(t.type==="delegated"){let{voter:n}=t.info.stake.delegation,r=e.get(n),a=r?.info?.keybaseUsername;o=r?.info?.name??a??fo(n)}else t.type==="initialized"&&(o=t.pubkey);return o};s();c();s();c();var ne=L(E());s();c();s();c();var Go=new Error("Failed to fetch Average Validator APY");async function Ho(){let t=await nt.api().get("/staking/v1/avg-validator-apy");if(!rt(t))throw Go;let e=be.safeParse(t.data);if(e.success)return e.data.apy;throw Go}var jo=()=>{let t=I.averageValidatorApy();return D({enabled:!0,queryKey:t,gcTime:qt({hours:1}),staleTime:1/0,async queryFn(){return Ho()}})};s();c();s();c();var zo=new Error("Failed to fetch liquid staking supported tokens"),dr=new Error("Missing supported tokens in response");async function $o(){try{let t=await nt.api().get("/liquid-staking/v1/supported-tokens");if(!rt(t))throw zo;let e=xo.safeParse(t.data.supportedTokens);if(!e.success)throw dr;return e.data}catch{throw zo}}var Ce=()=>{let t=I.liquidStakingSupportedTokens();return D({queryKey:t,gcTime:qt({hours:1}),staleTime:1/0,async queryFn(){return await $o()}})};var Le=t=>{let e=b(f=>f.setProvider),o=b(f=>f.setFungibleToStake),n=b(f=>f.setSkipDismissRouting),r=(0,ne.useMemo)(()=>t?Ft(kt(t)):"",[t]),{data:a,isPending:u}=jo(),i=a?a/100:null,{data:d={},isPending:p}=Ce(),m=(0,ne.useMemo)(()=>d[r]??[],[r,d]),l=m?m[0]:void 0,k=(0,ne.useCallback)(f=>{l&&t&&(o(t),e(l),n(f))},[t,l,o,e,n]);return{isLoading:p||u,isSupported:m.length>0,providers:m,initLiquidStake:k,defaultProvider:l,avgValidatorApy:i}};var xt=t=>{let e=wo.get(t);return Le(e)};var pr=t=>{let e=b(g=>g.setConvertStakeAccount),o=b(g=>g.setAmount),n=b(g=>g.fungibleToStake),r=b(g=>g.provider),{initLiquidStake:a}=xt(T.solana.mainnetID);(0,Wt.useEffect)(()=>{a()},[a]);let{trackConvertStakeAccountClicked:u,trackConvertStakeAccountListSeen:i}=tt(),{error:d,refetch:p,isLoadingList:m,eligibleStakeAccounts:l,inelegibleStakeAccounts:k}=oe(r);(0,Wt.useEffect)(()=>{m||i([...l,...k].map(g=>({title:g.title,amount:g.amount,eligible:g.eligible})))},[i,l,k,m]);let f=(0,Wt.useCallback)(g=>{let x=g?.info?.stake?.delegation?.voter??"",C=`${g?.lamports??"0"}`,P=g?.title??"";e({validatorVote:x,stakeAccount:g.pubkey,validatorName:P,eligibleAccountsCount:l.length}),o(C),u(P,C),t()},[u,l.length,t,e,o]),y=(0,Wt.useCallback)(()=>{p()},[p]);return{isLoading:m,error:d,eligibleStakeAccounts:l,inelegibleStakeAccounts:k,onReload:y,onSelectStakeAccount:f,tokenSymbol:n?.data.symbol??"",tokenDecimals:n?.data.decimals??0}};s();c();var _=L(E());s();c();function Ie(t){return t instanceof Pt?t:new Pt("UnknownError")}s();c();var Yo=L(E());var De=({txReceipt:t,isTxError:e,poolTokenFungible:o,invalidateQueries:n,dismissLiquidStaking:r})=>{let a=b(p=>p.resetStaking),u=b(p=>p.setNavigateTo),i=b(p=>p.setSkipDismissRouting),d=b(p=>p.skipDismissRouting);return(0,Yo.useCallback)(()=>{if(a(),!t||e)return r();if(n(),d)return i(void 0),r();u(o?{screen:"FungibleDetail",fungible:o}:{screen:"Activity"}),r()},[t,e,o,n,r,u,a,d,i])};s();c();var Me=L(E());s();c();var Jo=({connection:t})=>{let e=Jt(),o=ko();return j({mutationFn:async({accountIdentifier:r,ownerAddress:a,pendingActivityRow:u,activityType:i,transactionData:{networkID:d,transaction:p,signers:m}})=>{if(T.isSolanaNetworkID(d)){await io(p,t);let l={ownerAddress:a,networkID:d,data:{signature:""},type:i,display:{summary:u}};return{id:await go({accountIdentifier:r,feePayer:new S.PublicKey(a),connection:t,accountSigner:o,transaction:p,pendingTransactionInput:l,storage:e,opts:{additionalSignerAccounts:m}}),networkID:d}}throw new Error(`Unsupported network for liquid staking: ${d}`)}})};function Ee({isLoadingPoolToken:t,pendingActivityRow:e,onTransactionSubmitted:o,onTransactionFailed:n}){let{connection:r}=$(),a=b(g=>g.transactionData),u=b(g=>g.fungibleToStake),{data:i,isLoading:d}=Nt(),{mutateAsync:p}=Jo({connection:r}),l=(0,Me.useMemo)(()=>(i?.addresses??[]).find(g=>g.networkID===u?.data.chain.id),[u?.data.chain.id,i?.addresses])?.address??"",k=i?.identifier??"";return{executeLiquidStake:(0,Me.useCallback)(async()=>{try{if(!a)throw new Pt("MissingTransactionData");let g=await p({accountIdentifier:k,ownerAddress:l,pendingActivityRow:e,activityType:"convertStake",transactionData:a});o(g)}catch(g){n(g)}},[k,l,e,a,p,o,n]),isReadyToExecute:!d&&!t}}s();c();var Xo=L(E());var qe=()=>{let t=K();return(0,Xo.useCallback)(()=>at(t)(),[t])};s();c();var Ue=L(E());s();c();function Zo({key:t,queryKey:e,defaultValue:o=null}){let n=Jt();return D({enabled:!0,queryKey:e,gcTime:Vt.Long,staleTime:1/0,async queryFn(){try{return await n.get(t)}catch(r){return r instanceof Error&&W.captureError(r,"staking"),o}}})}function tn({key:t,queryKey:e}){let o=Jt(),n=K();return j({async mutationFn(r){try{return await o.set(t,r)}catch(a){a instanceof Error&&W.captureError(a,"staking");return}},async onMutate(r){await n.cancelQueries({queryKey:e});let a=n.getQueryData(e);return n.setQueryData(e,r),{previousValue:a}},onError(r,a,u){n.setQueryData(e,u?.previousValue)}})}var mr=ue.object({convert:ue.boolean(),mint:ue.boolean()}),je={convert:!1,mint:!1},fr=JSON.stringify(je),Qt=()=>{let{data:t}=Zo({key:"featureUsed",queryKey:I.featureUsed(),defaultValue:fr}),e=(0,Ue.useMemo)(()=>{if(!t)return je;let r=mr.safeParse(JSON.parse(t));return r.success?r.data:je},[t]),{mutateAsync:o}=tn({key:"featureUsed",queryKey:I.featureUsed()}),n=(0,Ue.useCallback)(async r=>{let a={...e,...r};await o(JSON.stringify(a))},[o,e]);return{usageState:e,setUsageState:n}};var Sr="https://www.jito.network/defi/";function gr({stakedTokenSymbol:t,poolTokenSymbol:e,txHash:o,txError:n,confirmationStatus:r,eligibleAccountsCount:a,onDismiss:u,onConvertMore:i}){let d={title:w.t("commandClose"),onPress:u},p={title:w.t("convertStakeStatusConvertMore"),onPress:i},m={txHash:o,txHashTitle:w.t("commandViewTransaction")};return n?{type:"error",title:w.t("convertStakeStatusErrorTitle"),message:w.t("convertStakeStatusErrorMessage",{poolTokenSymbol:e}),...m,primaryButton:d}:r==="confirmed"?{type:"success",title:w.t("convertStakeStatusSuccessTitle",{poolTokenSymbol:e}),message:"",...m,...a>1?{primaryButton:p,secondaryButton:d}:{primaryButton:d}}:{type:"loading",title:w.t("convertStakeStatusLoadingTitle",{poolTokenSymbol:e}),message:w.t("convertStakeStatusLoadingMessage",{stakedTokenSymbol:t,poolTokenSymbol:e}),...m,primaryButton:d}}var yr=({dismissConvertFlow:t,goToConvertAccountList:e})=>{let o=qe(),[n,r]=(0,_.useState)("loading"),[a,u]=(0,_.useState)(),[i,d]=(0,_.useState)(),{trackConvertSubmitted:p,trackConvertSuccess:m,trackConvertFailed:l,trackConvertMoreClicked:k}=tt(),{usageState:f,setUsageState:y}=Qt(),g=b(U=>U.fungibleToStake),x=b(U=>U.provider),C=b(U=>U.convert),P=b(U=>U.amount),[N]=(0,_.useState)(C?.eligibleAccountsCount??0),J=b(U=>U.setConvertStakeAccount),X=b(U=>U.setTransactionData),ut=g?.data.symbol??"",dt=x?.stakePoolTokenMetadata.metadata.symbol??"",Ht=x?.stakePoolTokenMetadata.metadata.address??"",{data:vt}=Nt(),Lt=vt?.type==="ledger",jt=(0,_.useMemo)(()=>(vt?.addresses??[]).find(U=>U.networkID===g?.data.chain.id),[g?.data.chain.id,vt?.addresses])?.addressType??"solana",{fungible:zt,isLoadingTokens:$t}=H({key:Ht}),It=(0,_.useCallback)(U=>{p(U.id),u(U),f.convert||y({convert:!0})},[y,p,f.convert]),wt=(0,_.useCallback)(U=>{l({amount:P,convertDetails:C});let Mn=Ie(U);d(Mn)},[l,P,C]),B=(0,_.useMemo)(()=>({topLeft:{text:w.t("convertStakePendingTitle",{symbol:dt})}}),[dt]),{executeLiquidStake:st,isReadyToExecute:Dt}=Ee({isLoadingPoolToken:$t,pendingActivityRow:B,onTransactionSubmitted:It,onTransactionFailed:wt}),q=(0,_.useCallback)(()=>{m({amount:P,convertDetails:C,provider:x}),J(void 0),X(void 0),o()},[P,m,J,X,o,x,C]),{isSuccess:Mt,isError:Yt,error:ce}=Se(q,a);(0,_.useEffect)(()=>{Mt?r("confirmed"):Yt&&(r("error"),d(ce),l({amount:P,convertDetails:C}))},[P,C,Mt,Yt,l,ce,a]);let oo=De({txReceipt:a,isTxError:Yt,poolTokenFungible:zt,invalidateQueries:o,dismissLiquidStaking:t}),no=(0,_.useCallback)(()=>{k(),e()},[k,e]),Dn=(0,_.useMemo)(()=>gr({stakedTokenSymbol:ut,poolTokenSymbol:dt,txHash:a?.id,txError:i,confirmationStatus:n,eligibleAccountsCount:N,onDismiss:oo,onConvertMore:no}),[ut,dt,a?.id,i,n,N,oo,no]);return{addressType:jt,isLedger:Lt,isReadyToExecute:Dt,statusPageProps:Dn,txError:i,executeLiquidStake:st,learnMoreLink:Sr}};s();c();var Gt=L(E()),Fe=null,Ar=t=>{if(!Fe&&t){let e="liquidStake";Fe=new ke(t,e)}if(!Fe)throw new Error("liquidStakeAnalytics was not properly configured");return Fe},re=()=>{let t=pe(),e=Ar(t),o=(0,Gt.useCallback)(()=>{t.capture("liquidStakingMintModalContinueClicked")},[t]),n=(0,Gt.useCallback)(({provider:i,payAmount:d,receiveAmount:p,payAmountUSD:m,receiveAmountUSD:l,payAssetTitle:k,providerName:f,receiveAssetTitle:y})=>{t.capture("liquidStakingMintApprovedByUser",{data:{stake:{stakePoolAddress:i?.stakePoolAddress,token:i?.stakePoolTokenMetadata.metadata.name,apy:i?.apy,convertRatio:i?.convertRatio,caip19:i?.stakePoolTokenMetadata.caip19,payAmount:d,receiveAmount:p,payAmountUSD:m,receiveAmountUSD:l,payAssetTitle:k,receiveAssetTitle:y,providerName:f}}})},[t]),r=(0,Gt.useCallback)(i=>{e.submittedTransaction(i,{chainType:"solana",chainName:"solana",networkId:"mainnet",method:"signAndSendTransaction"})},[e]),a=(0,Gt.useCallback)(({provider:i,amount:d})=>{t.capture("liquidStakingMintSuccess",{data:{stake:{apy:i?.apy,convertRatio:i?.convertRatio,caip19:i?.stakePoolTokenMetadata.caip19,stakePoolAddress:i?.stakePoolAddress,token:i?.stakePoolTokenMetadata.metadata.name,totalAmount:d}}})},[t]),u=(0,Gt.useCallback)(i=>{t.capture("liquidStakingMintFailed",{data:{stake:{amount:i}}})},[t]);return{trackMintModalContinue:o,trackMintSubmitted:r,trackMintSuccess:a,trackMintFailed:u,trackMintApprovedByUser:n}};s();c();var et=L(E());s();c();var hr=2,en=({networkID:t,poolTokenMintAddress:e})=>{let o=T.getAddressType(t),{data:n}=V(t),{fungible:r}=H({key:e}),a=ho(t,o),{fungible:u}=H({key:a}),i=F(u?.data.amount??""),{data:d=0}=ge(n),p=F(he).plus(F(Bt).multipliedBy(Ae)),m=F(d),l=F(d),k=F(hr).plus(p),f=r?F(k).plus(m):F(l).plus(k).plus(m),y=F(i).minus(f),g=F.max(y,0);return{minAmount:f,maxAmount:g,balance:i}};var br=({goBack:t,goToReviewPage:e,showInfoPage:o})=>{let n=b(B=>B.fungibleToStake),r=b(B=>B.provider),a=b(B=>B.amount),u=b(B=>B.setAmount),i=n?.data.symbol??"",d=n?.data.chain.id??T.solana.mainnetID,p=n?.data.decimals??0,m=r?.stakePoolTokenMetadata.metadata.address??"",[l,k]=(0,et.useState)(a?lo(a,p):""),{data:f}=V("solana"),{initLiquidStake:y}=xt(T.solana.mainnetID);(0,et.useEffect)(()=>{y()},[y]),vo(f,"STAKE_FUNGIBLE");let{minAmount:g,maxAmount:x,balance:C}=en({networkID:d,poolTokenMintAddress:m}),P=pt(x,p),N=pt(g,p),J=pt(C,p),X=po(l,p).toString(),ut=F(X),dt=ut.isLessThan(g),Ht=ut.isGreaterThan(x),vt=ut.isFinite(),Lt=X&&dt?w.t("mintLiquidStakeAmountMinRequired",{symbol:i,amount:N}):X&&Ht?w.t("mintLiquidStakeAmountInsufficientBalance"):void 0,se=vt&&!Lt,jt=(0,et.useMemo)(()=>{if(!r)return;let B=Tt(Number.parseFloat(r.apy)),{address:st,name:Dt,symbol:q,logoURI:Mt}=r.stakePoolTokenMetadata.metadata,Yt=F(1).dividedBy(F(r.convertRatio)),ce=`1 ${i} \u2248 ${Kt(Yt)} ${q}`;return{identifier:st,apy:B,name:Dt,symbol:q,rate:ce,logoURI:Mt}},[r,i]),zt=(0,et.useCallback)(()=>{k(String(P))},[P]),$t=(0,et.useCallback)(B=>{let st=uo(B);Co(st,p??9)&&k(st)},[p]),It=(0,et.useCallback)(()=>{u(X),e()},[X,e,u]),wt=(0,et.useCallback)(()=>{u(""),t()},[u,t]);return{amount:l,amountError:Lt,balance:J,symbol:i,poolToken:jt,canSubmit:se,onBack:wt,onContinue:It,onChangeAmount:$t,onSetMax:zt,onInfoPress:o}};s();c();var G=L(E());var vr="https://www.jito.network/defi/";function wr({poolTokenSymbol:t,txHash:e,txError:o,confirmationStatus:n,onDismiss:r}){let a={title:w.t("commandClose"),onPress:r},u={txHash:e,txHashTitle:w.t("commandViewTransaction")};return o?{type:"error",title:w.t("mintStakeStatusErrorTitle"),message:w.t("mintStakeStatusErrorMessage",{poolTokenSymbol:t}),...u,primaryButton:a}:n==="confirmed"?{type:"success",title:w.t("mintStakeStatusSuccessTitle",{poolTokenSymbol:t}),message:"",...u,primaryButton:a}:{type:"loading",title:w.t("mintStakeStatusLoadingTitle",{poolTokenSymbol:t}),message:w.t("mintStakeStatusLoadingMessage",{poolTokenSymbol:t}),...u,primaryButton:a}}var Tr=t=>{let e=qe(),[o,n]=(0,G.useState)("loading"),[r,a]=(0,G.useState)(),[u,i]=(0,G.useState)(),{trackMintSubmitted:d,trackMintSuccess:p,trackMintFailed:m}=re(),{usageState:l,setUsageState:k}=Qt(),f=b(q=>q.fungibleToStake),y=b(q=>q.provider),g=b(q=>q.setTransactionData),x=b(q=>q.amount),C=y?.stakePoolTokenMetadata.metadata.symbol??"",P=y?.stakePoolTokenMetadata.metadata.address??"",{data:N}=Nt(),J=N?.type==="ledger",ut=(0,G.useMemo)(()=>(N?.addresses??[]).find(q=>q.networkID===f?.data.chain.id),[f?.data.chain.id,N?.addresses])?.addressType??"solana",{fungible:dt,isLoadingTokens:Ht}=H({key:P}),vt=(0,G.useCallback)(q=>{d(q.id),a(q),l.mint||k({mint:!0})},[k,d,l.mint]),Lt=(0,G.useCallback)(q=>{m(x);let Mt=Ie(q);i(Mt)},[m,x]),se=(0,G.useMemo)(()=>({topLeft:{text:w.t("mintLiquidStakePendingTitle",{symbol:C})}}),[C]),{executeLiquidStake:jt,isReadyToExecute:zt}=Ee({isLoadingPoolToken:Ht,pendingActivityRow:se,onTransactionSubmitted:vt,onTransactionFailed:Lt}),$t=(0,G.useCallback)(()=>{p({amount:x,provider:y}),g(void 0),e()},[p,x,y,g,e]),{isSuccess:It,isError:wt,error:B}=Se($t,r);(0,G.useEffect)(()=>{It?n("confirmed"):wt&&(n("error"),i(B),m(x))},[It,m,x,wt,B,r]);let st=De({txReceipt:r,isTxError:wt,poolTokenFungible:dt,invalidateQueries:e,dismissLiquidStaking:t}),Dt=(0,G.useMemo)(()=>wr({poolTokenSymbol:C,txHash:r?.id,txError:u,confirmationStatus:o,onDismiss:st}),[C,r?.id,u,o,st]);return{addressType:ut,isLedger:J,isReadyToExecute:zt,statusPageProps:Dt,learnMoreLink:vr,txError:u,executeLiquidStake:jt}};s();c();var on=L(E());var Pr=({onLiquidStake:t,onNativeStake:e})=>{let o=To,{defaultProvider:n,avgValidatorApy:r}=xt(Ut.Solana.Mainnet);return{options:(0,on.useMemo)(()=>[{type:"liquid",isRecommended:!0,title:w.t("stakeMethodSelectionLiquidStakingTitle"),description:w.t("stakeMethodSelectionLiquidStakingDescription"),apy:n?w.t("stakeMethodEstApy",{apy:Tt(parseFloat(n.apy))}):null,onClick:t},{type:"native",isRecommended:!1,title:w.t("stakeMethodSelectionNativeStakingTitle"),description:w.t("stakeMethodSelectionNativeStakingDescription"),apy:r?w.t("stakeMethodEstApy",{apy:Tt(r)}):null,onClick:e}],[r,n,t,e]),learnMoreLink:o}};s();c();s();c();var Pn=L(E());s();c();s();c();s();c();s();c();s();c();s();c();s();c();function xr(t,...e){if(!(t instanceof Uint8Array))throw new Error("Expected Uint8Array");if(e.length>0&&!e.includes(t.length))throw new Error(`Expected Uint8Array of length ${e}, not of length=${t.length}`)}function ze(t,e=!0){if(t.destroyed)throw new Error("Hash instance has been destroyed");if(e&&t.finished)throw new Error("Hash#digest() has already been called")}function nn(t,e){xr(t);let o=e.outputLen;if(t.length<o)throw new Error(`digestInto() expects output buffer of length at least ${o}`)}s();c();var Cr=t=>t instanceof Uint8Array;var Re=t=>new DataView(t.buffer,t.byteOffset,t.byteLength),ot=(t,e)=>t<<32-e|t>>>e,Lr=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!Lr)throw new Error("Non little-endian hardware is not supported");function Ir(t){if(typeof t!="string")throw new Error(`utf8ToBytes expected string, got ${typeof t}`);return new Uint8Array(new TextEncoder().encode(t))}function $e(t){if(typeof t=="string"&&(t=Ir(t)),!Cr(t))throw new Error(`expected Uint8Array, got ${typeof t}`);return t}var Ne=class{clone(){return this._cloneInto()}},el={}.toString;function rn(t){let e=n=>t().update($e(n)).digest(),o=t();return e.outputLen=o.outputLen,e.blockLen=o.blockLen,e.create=()=>t(),e}function Dr(t,e,o,n){if(typeof t.setBigUint64=="function")return t.setBigUint64(e,o,n);let r=BigInt(32),a=BigInt(4294967295),u=Number(o>>r&a),i=Number(o&a),d=n?4:0,p=n?0:4;t.setUint32(e+d,u,n),t.setUint32(e+p,i,n)}var Be=class extends Ne{constructor(e,o,n,r){super(),this.blockLen=e,this.outputLen=o,this.padOffset=n,this.isLE=r,this.finished=!1,this.length=0,this.pos=0,this.destroyed=!1,this.buffer=new Uint8Array(e),this.view=Re(this.buffer)}update(e){ze(this);let{view:o,buffer:n,blockLen:r}=this;e=$e(e);let a=e.length;for(let u=0;u<a;){let i=Math.min(r-this.pos,a-u);if(i===r){let d=Re(e);for(;r<=a-u;u+=r)this.process(d,u);continue}n.set(e.subarray(u,u+i),this.pos),this.pos+=i,u+=i,this.pos===r&&(this.process(o,0),this.pos=0)}return this.length+=e.length,this.roundClean(),this}digestInto(e){ze(this),nn(e,this),this.finished=!0;let{buffer:o,view:n,blockLen:r,isLE:a}=this,{pos:u}=this;o[u++]=128,this.buffer.subarray(u).fill(0),this.padOffset>r-u&&(this.process(n,0),u=0);for(let l=u;l<r;l++)o[l]=0;Dr(n,r-8,BigInt(this.length*8),a),this.process(n,0);let i=Re(e),d=this.outputLen;if(d%4)throw new Error("_sha2: outputLen should be aligned to 32bit");let p=d/4,m=this.get();if(p>m.length)throw new Error("_sha2: outputLen bigger than state");for(let l=0;l<p;l++)i.setUint32(4*l,m[l],a)}digest(){let{buffer:e,outputLen:o}=this;this.digestInto(e);let n=e.slice(0,o);return this.destroy(),n}_cloneInto(e){e||(e=new this.constructor),e.set(...this.get());let{blockLen:o,buffer:n,length:r,finished:a,destroyed:u,pos:i}=this;return e.length=r,e.pos=i,e.finished=a,e.destroyed=u,r%o&&e.buffer.set(n),e}};var Mr=(t,e,o)=>t&e^~t&o,Er=(t,e,o)=>t&e^t&o^e&o,qr=new Uint32Array([1116352408,1899447441,3049323471,3921009573,961987163,1508970993,2453635748,2870763221,3624381080,310598401,607225278,1426881987,1925078388,2162078206,2614888103,3248222580,3835390401,4022224774,264347078,604807628,770255983,1249150122,1555081692,1996064986,2554220882,2821834349,2952996808,3210313671,3336571891,3584528711,113926993,338241895,666307205,773529912,1294757372,1396182291,1695183700,1986661051,2177026350,2456956037,2730485921,2820302411,3259730800,3345764771,3516065817,3600352804,4094571909,275423344,430227734,506948616,659060556,883997877,958139571,1322822218,1537002063,1747873779,1955562222,2024104815,2227730452,2361852424,2428436474,2756734187,3204031479,3329325298]),yt=new Uint32Array([1779033703,3144134277,1013904242,2773480762,1359893119,2600822924,528734635,1541459225]),At=new Uint32Array(64),Ye=class extends Be{constructor(){super(64,32,8,!1),this.A=yt[0]|0,this.B=yt[1]|0,this.C=yt[2]|0,this.D=yt[3]|0,this.E=yt[4]|0,this.F=yt[5]|0,this.G=yt[6]|0,this.H=yt[7]|0}get(){let{A:e,B:o,C:n,D:r,E:a,F:u,G:i,H:d}=this;return[e,o,n,r,a,u,i,d]}set(e,o,n,r,a,u,i,d){this.A=e|0,this.B=o|0,this.C=n|0,this.D=r|0,this.E=a|0,this.F=u|0,this.G=i|0,this.H=d|0}process(e,o){for(let l=0;l<16;l++,o+=4)At[l]=e.getUint32(o,!1);for(let l=16;l<64;l++){let k=At[l-15],f=At[l-2],y=ot(k,7)^ot(k,18)^k>>>3,g=ot(f,17)^ot(f,19)^f>>>10;At[l]=g+At[l-7]+y+At[l-16]|0}let{A:n,B:r,C:a,D:u,E:i,F:d,G:p,H:m}=this;for(let l=0;l<64;l++){let k=ot(i,6)^ot(i,11)^ot(i,25),f=m+k+Mr(i,d,p)+qr[l]+At[l]|0,g=(ot(n,2)^ot(n,13)^ot(n,22))+Er(n,r,a)|0;m=p,p=d,d=i,i=u+f|0,u=a,a=r,r=n,n=f+g|0}n=n+this.A|0,r=r+this.B|0,a=a+this.C|0,u=u+this.D|0,i=i+this.E|0,d=d+this.F|0,p=p+this.G|0,m=m+this.H|0,this.set(n,r,a,u,i,d,p,m)}roundClean(){At.fill(0)}destroy(){this.set(0,0,0,0,0,0,0,0),this.buffer.fill(0)}};var an=rn(()=>new Ye);var sn=L(qn()),Ur=[69,107,109,106,121,122,120,100,105,53,52,51,110,99,76,56,54,116,99,97,67,65,100,117,108,79,119,121,110,86,53,68,118,54,86,67,109,73,84,117,118,48,51,118,53,101,98,76,80,98,48,88,101,57,111,108,82,71,75,72,85,71,119,68,117,116,111,117,68,117,77,55,49,88,65,106,55,97,111,74,102,77,78,106,56,119,61,61],Ke=t=>{let e=`${String.fromCharCode(...Ur)}||${t}`,o=an(Buffer.from(e));return sn.default.encode(o)};s();c();var fn=L(ro());s();c();var Ct=new S.PublicKey("SPoo1Ku8WFXoNDMHPsrGSTSG1Y47rzgn41SLUNakuHy");s();c();var cn=Object.freeze({DepositStake:{index:9,layout:Et.struct([Et.u8("instruction")])},DepositSol:{index:14,layout:Et.struct([Et.u8("instruction"),Et.ns64("lamports")])}});function un(t,e){let o=t.layout.span,n=Buffer.alloc(o),r=Object.assign({instruction:t.index},e);return t.layout.encode(r,n),n}var ae=class{static depositStake(e){let{stakePool:o,validatorList:n,depositAuthority:r,withdrawAuthority:a,depositStake:u,validatorStake:i,reserveStake:d,destinationPoolAccount:p,managerFeeAccount:m,referralPoolAccount:l,poolMint:k,referralAccount:f}=e,y=cn.DepositStake,g=un(y),x=[{pubkey:o,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!1,isWritable:!0},{pubkey:r,isSigner:!1,isWritable:!1},{pubkey:a,isSigner:!1,isWritable:!1},{pubkey:u,isSigner:!1,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:d,isSigner:!1,isWritable:!0},{pubkey:p,isSigner:!1,isWritable:!0},{pubkey:m,isSigner:!1,isWritable:!0},{pubkey:l,isSigner:!1,isWritable:!0},{pubkey:k,isSigner:!1,isWritable:!0},{pubkey:S.SYSVAR_CLOCK_PUBKEY,isSigner:!1,isWritable:!1},{pubkey:S.SYSVAR_STAKE_HISTORY_PUBKEY,isSigner:!1,isWritable:!1},{pubkey:Rt,isSigner:!1,isWritable:!1},{pubkey:S.StakeProgram.programId,isSigner:!1,isWritable:!1}];return f&&x.push({pubkey:f,isSigner:!1,isWritable:!1}),new R.TransactionInstruction({programId:Ct,keys:x,data:g})}static depositSol(e){let{stakePool:o,withdrawAuthority:n,depositAuthority:r,reserveStake:a,fundingAccount:u,destinationPoolAccount:i,managerFeeAccount:d,referralPoolAccount:p,poolMint:m,lamports:l,referralAccount:k}=e,f=cn.DepositSol,y=un(f,{lamports:l}),g=[{pubkey:o,isSigner:!1,isWritable:!0},{pubkey:n,isSigner:!1,isWritable:!1},{pubkey:a,isSigner:!1,isWritable:!0},{pubkey:u,isSigner:!0,isWritable:!0},{pubkey:i,isSigner:!1,isWritable:!0},{pubkey:d,isSigner:!1,isWritable:!0},{pubkey:p,isSigner:!1,isWritable:!0},{pubkey:m,isSigner:!1,isWritable:!0},{pubkey:S.SystemProgram.programId,isSigner:!1,isWritable:!1},{pubkey:Rt,isSigner:!1,isWritable:!1}];return r&&g.push({pubkey:r,isSigner:!0,isWritable:!1}),k&&g.push({pubkey:k,isSigner:!1,isWritable:!1}),new R.TransactionInstruction({programId:Ct,keys:g,data:y})}};s();c();var v=L(ln()),bt=[(0,v.u64)("denominator"),(0,v.u64)("numerator")],mn=(0,v.struct)([(0,v.u8)("accountType"),(0,v.publicKey)("manager"),(0,v.publicKey)("staker"),(0,v.publicKey)("stakeDepositAuthority"),(0,v.u8)("stakeWithdrawBumpSeed"),(0,v.publicKey)("validatorList"),(0,v.publicKey)("reserveStake"),(0,v.publicKey)("poolMint"),(0,v.publicKey)("managerFeeAccount"),(0,v.publicKey)("tokenProgramId"),(0,v.u64)("totalLamports"),(0,v.u64)("poolTokenSupply"),(0,v.u64)("lastUpdateEpoch"),(0,v.struct)([(0,v.u64)("unixTimestamp"),(0,v.u64)("epoch"),(0,v.publicKey)("custodian")],"lockup"),(0,v.struct)(bt,"epochFee"),(0,v.option)((0,v.struct)(bt),"nextEpochFee"),(0,v.option)((0,v.publicKey)(),"preferredDepositValidatorVoteAddress"),(0,v.option)((0,v.publicKey)(),"preferredWithdrawValidatorVoteAddress"),(0,v.struct)(bt,"stakeDepositFee"),(0,v.struct)(bt,"stakeWithdrawalFee"),(0,v.option)((0,v.struct)(bt),"nextStakeWithdrawalFee"),(0,v.u8)("stakeReferralFee"),(0,v.option)((0,v.publicKey)(),"solDepositAuthority"),(0,v.struct)(bt,"solDepositFee"),(0,v.u8)("solReferralFee"),(0,v.option)((0,v.publicKey)(),"solWithdrawAuthority"),(0,v.struct)(bt,"solWithdrawalFee"),(0,v.option)((0,v.struct)(bt),"nextSolWithdrawalFee"),(0,v.u64)("lastEpochPoolTokenSupply"),(0,v.u64)("lastEpochTotalLamports")]);async function kn(t,e){let o=await t.getAccountInfo(e);if(!o)throw new Error("Invalid stake pool account");return{pubkey:e,account:{data:mn.decode(o.data),executable:o.executable,lamports:o.lamports,owner:o.owner}}}async function Sn(t,e){let[o]=await S.PublicKey.findProgramAddress([e.toBuffer(),Buffer.from("withdraw")],t);return o}async function ta(t,e,o,n){let[r]=await S.PublicKey.findProgramAddress([e.toBuffer(),o.toBuffer(),n?new fn.default(n).toArrayLike(Buffer,"le",4):Buffer.alloc(0)],t);return r}function ea(t){if(typeof t=="number")return Math.abs(t)/S.LAMPORTS_PER_SOL;if(typeof t=="bigint")return Math.abs(Number(t))/S.LAMPORTS_PER_SOL;let e=1;t.isNeg()&&(e=-1);let n=t.abs().toString(10).padStart(10,"0"),r=n.length-9,a=n.slice(0,r)+"."+n.slice(r);return e*parseFloat(a)}async function gn(t,e,o,n,r,a,u){let i=await kn(t,e),d=await Sn(Ct,e),p=await ta(Ct,n,e),m=[],l=[],k=i.account.data.poolMint;if(!a){let f=We(k,o);m.push(Qe(o,f,o,k)),a=f}return m.push(...S.StakeProgram.authorize({stakePubkey:r,authorizedPubkey:o,newAuthorizedPubkey:i.account.data.stakeDepositAuthority,stakeAuthorizationType:S.StakeAuthorizationLayout.Staker}).instructions),m.push(...S.StakeProgram.authorize({stakePubkey:r,authorizedPubkey:o,newAuthorizedPubkey:i.account.data.stakeDepositAuthority,stakeAuthorizationType:S.StakeAuthorizationLayout.Withdrawer}).instructions),m.push(ae.depositStake({stakePool:e,validatorList:i.account.data.validatorList,depositAuthority:i.account.data.stakeDepositAuthority,reserveStake:i.account.data.reserveStake,managerFeeAccount:i.account.data.managerFeeAccount,referralPoolAccount:a,destinationPoolAccount:a,withdrawAuthority:d,depositStake:r,validatorStake:p,poolMint:k,referralAccount:u})),{instructions:m,signers:l}}async function yn(t,e,o,n,r,a,u,i){let d=await t.getBalance(o,"confirmed");if(d<n)throw new Error(`Not enough SOL to deposit into pool. Maximum deposit amount is ${ea(d)} SOL.`);let m=(await kn(t,e)).account.data,l=new S.Keypair,k=[l],f=[];if(f.push(S.SystemProgram.transfer({fromPubkey:o,toPubkey:l.publicKey,lamports:n})),!r){let g=We(m.poolMint,o);f.push(Qe(o,g,o,m.poolMint)),r=g}let y=await Sn(Ct,e);return f.push(ae.depositSol({stakePool:e,reserveStake:m.reserveStake,fundingAccount:l.publicKey,destinationPoolAccount:r,managerFeeAccount:m.managerFeeAccount,referralPoolAccount:u??r,poolMint:m.poolMint,lamports:n,withdrawAuthority:y,depositAuthority:i,referralAccount:a})),{instructions:f,signers:k}}var Ze=async({connection:t,stakePoolAddress:e,ownerAddress:o,validatorVoteAddress:n,stakeAccountAddress:r,poolTokenAccountAddress:a})=>{let u=new S.PublicKey(e),i=new S.PublicKey(o),d=new S.PublicKey(n),p=new S.PublicKey(r),m=a?new S.PublicKey(a):void 0,l=new S.PublicKey(Ke(o)),{instructions:k,signers:f}=await gn(t,u,i,d,p,m,l),y=(await t.getLatestBlockhash("confirmed")).blockhash,g=new R.VersionedTransaction(new R.TransactionMessage({instructions:k,payerKey:i,recentBlockhash:y}).compileToV0Message());return{transaction:await z(g,{connection:le(Ut.Solana.Mainnet),calculators:{cost:fe,budget:new me(2e5,2e5)}}),signers:f}};s();c();var to=async({connection:t,stakePoolAddress:e,ownerAddress:o,amount:n,poolTokenAccountAddress:r})=>{let a=new S.PublicKey(e),u=new S.PublicKey(o),i=r?new S.PublicKey(r):void 0,d=new S.PublicKey(Ke(o)),{instructions:p,signers:m}=await yn(t,a,u,n,i,d),l=(await t.getLatestBlockhash("confirmed")).blockhash,k=new R.VersionedTransaction(new R.TransactionMessage({instructions:p,payerKey:u,recentBlockhash:l}).compileToV0Message());return{transaction:await z(k,{connection:le(Ut.Solana.Mainnet),calculators:{cost:fe,budget:new me(2e5,2e5)}}),signers:m}};var An=t=>{let e=I.getConvertStakeTransaction({stakePoolAddress:t.stakePoolAddress,ownerAddress:t.ownerAddress,validatorVoteAddress:t.validatorVoteAddress,stakeAccountAddress:t.stakeAccountAddress,poolTokenAccountAddress:t.poolTokenAccountAddress}),o=!!t.stakePoolAddress&&!!t.ownerAddress&&!!t.validatorVoteAddress&&!!t.stakeAccountAddress;return D({enabled:o,queryKey:e,staleTime:1/0,async queryFn(){if(T.isSolanaNetworkID(t.networkId)){let{transaction:n,signers:r}=await Ze(t);return{networkID:t.networkId,transaction:n,signers:r}}throw new Error(`Unsupported networkId: ${t.networkId}`)}})};s();c();var Tn=L(E());s();c();var hn=(t,e)=>{let o=[],n=t?kt(t):void 0,r=mo(e?.stakePoolTokenMetadata.caip19??"");return n&&r&&(o.push(n),o.push(r)),{query:{data:o}}};s();c();var eo=L(E()),oa=(t,e)=>{if(e===void 0)return;let o=kt(t),n=Ft(o);return e[n]},na=(t,e)=>{if(!e)return F(0);let o=new F(e.convertRatio);return pt(t,e.stakePoolTokenMetadata.metadata.decimals).div(o)},ra=(t,e,o)=>{if(!t)return null;let n=pt(e,t.data.decimals),r=oa(t,o),a=r?.usd?n.multipliedBy(r.usd):null;return{amount:Kt(n),amountUsd:a?a.toNumber():null,border:!1,logoUri:t.data.logoUri??null,network:{id:t.data.chain.id,name:t.data.chain.name,symbol:t.data.chain.symbol,imageUrl:t.data.chain.imageUrl},symbol:t.data.symbol,tokenAddress:t.data.tokenAddress,tokenType:t.type??"SolanaNative",title:w.t("liquidStakeReviewYouPay")}},aa=(t,e,o,n)=>{if(!e||!t)return null;let r=na(o,e),a=n?n[e.stakePoolTokenMetadata.caip19]:void 0,u=a?.usd?r.multipliedBy(a.usd):null;return{amount:Kt(r),amountUsd:u?u.toNumber():null,border:!1,network:{id:e.stakePoolTokenMetadata.metadata.chainId??"solana:101",name:t.data.chain.name,symbol:t.data.chain.symbol,imageUrl:t.data.chain.imageUrl},logoUri:e.stakePoolTokenMetadata.metadata.logoURI??null,symbol:e.stakePoolTokenMetadata.metadata.symbol,title:w.t("liquidStakeReviewYouReceive"),tokenAddress:e.stakePoolTokenMetadata.metadata.address,tokenType:"SPL"}},bn=({process:t,fungibleToStake:e,amount:o,provider:n,selectedChainAddress:r,transactionFees:a,isLoading:u,canSubmit:i,priceMap:d,networkFeeErrorMsg:p})=>{let m=(0,eo.useMemo)(()=>ra(e,o,d),[e,o,d]),l=(0,eo.useMemo)(()=>aa(e,n,o,d),[e,n,o,d]),k=n?.name??"",f=r?.address??"",y=n?.apy!==void 0?Tt(Number.parseFloat(n.apy)):"",g=`${Kt(pt(a,e?.data.decimals??0))} ${e?.data.symbol}`,x=w.t("stakeReviewAccount"),C=w.t("liquidStakeReviewProvider"),P=w.t("stakeReviewAPY"),N=w.t("liquidStakeReviewNetworkFee"),J=w.t(t==="convert"?"commandConvert":"commandStake");return{process:t,payAsset:m,receiveAsset:l,account:f,providerName:k,apy:y,networkFee:g,accountLabelText:x,providerLabelText:C,apyLabelText:P,networkFeeLabelText:N,primaryButtonText:J,isLoading:u,canSubmit:i,networkFeeErrorMsg:p}};s();c();var vn=L(E());var ia=qt({seconds:10}),wn=({hasPoolTokenAccount:t,chainAddress:e,transactionData:o})=>{let{data:n,isPending:r,isFetched:a,isSuccess:u}=yo({networkID:o?.networkID??T.solana.mainnetID,multichainTransaction:ca(o),queryOptions:{refetchInterval:ia}}),{data:i=0}=ge(e),d=O(he).plus(O(Bt).multipliedBy(Ae)),p=t?O(d):O(i).plus(d),{hasSufficientFunds:m,isLoading:l,nativeTokenSymbol:k}=bo(o?.networkID??T.solana.mainnetID,e?.addressType,n,p),f=ua({hasSufficientFunds:m,hasGasEstimationFailed:a&&!u,nativeTokenSymbol:k}),y=sa(n),g=p.plus(y),x=r||l;return(0,vn.useMemo)(()=>({isLoading:x,hasSufficientFunds:m,transactionFees:g,networkFeeErrorMsg:f,nativeTokenSymbol:k}),[x,m,g,f,k])},sa=t=>t?T.isSolanaNetworkID(t.networkID)?new O(t.value):new O(0):new O(0),ca=t=>{if(t&&T.isSolanaNetworkID(t.networkID)){let{networkID:e,transaction:o}=t;return{networkID:e,transaction:[o]}}},ua=({hasSufficientFunds:t,hasGasEstimationFailed:e,nativeTokenSymbol:o})=>e?w.t("transactionGasEstimationError"):t==="insufficient"?w.t("transactionNotEnoughNative",{nativeTokenSymbol:o}):void 0;var Ve=({process:t,transactionData:e,isLoadingTransactionData:o})=>{let n=b(P=>P.provider),r=b(P=>P.fungibleToStake),a=b(P=>P.amount),{data:u}=V("solana"),{fungible:i,isLoadingTokens:d}=H({key:n?.stakePoolTokenMetadata.metadata.address||""}),{isLoading:p,hasSufficientFunds:m,transactionFees:l,networkFeeErrorMsg:k}=wn({hasPoolTokenAccount:!!i,chainAddress:u,transactionData:e}),f=(0,Tn.useMemo)(()=>hn(r,n),[r,n]),{data:y}=Ao(f),g=o||p||d;return bn({process:t,fungibleToStake:r,amount:a,provider:n,selectedChainAddress:u,transactionFees:l,isLoading:g,canSubmit:!g&&!!e&&m!=="insufficient"&&!k,priceMap:y,networkFeeErrorMsg:k})};var da=t=>{let{data:e}=V("solana"),{trackConvertApprovedByUser:o}=tt(),n=b(y=>y.convert),r=b(y=>y.provider),a=b(y=>y.amount),u=b(y=>y.setTransactionData),{connection:i}=$(),{fungible:d}=H({key:r?.stakePoolTokenMetadata.metadata.address||""}),p=d?ye(d):void 0,{data:m,isLoading:l}=An({connection:i,networkId:r?.chainId??T.solana.mainnetID,stakePoolAddress:r?.stakePoolAddress??"",ownerAddress:e?.address??"",validatorVoteAddress:n?.validatorVote??"",stakeAccountAddress:n?.stakeAccount??"",poolTokenAccountAddress:p}),k=Ve({process:"convert",transactionData:m,isLoadingTransactionData:l}),f=(0,Pn.useCallback)(()=>{o({provider:r,convertDetails:n,amount:a,fromAmount:k.payAsset?.amount,toAmount:k.receiveAsset?.amount,fromAmountUSD:k.payAsset?.amountUsd,toAmountUSD:k.receiveAsset?.amountUsd}),u(m),t()},[t,u,m,o,n,r,k,a]);return{...k,onPrimaryButtonPress:f}};s();c();var _e=L(E());var pa=({fungible:t,goToConvertStake:e})=>{let{connection:o}=$(),{data:n}=V("solana"),{isLoading:r}=He(o,n?.address??""),{trackConvertBannerClicked:a,trackConvertBannerSeen:u}=tt(),{data:[i]}=co(["enable-jito-banner-deeplink-to-quests"]),{isLoading:d,isSupported:p,initLiquidStake:m,defaultProvider:l}=Le(t),{hasConvertibleStakeAccounts:k}=oe(l),y=!(r||d)&&p&&k,g=(0,_e.useCallback)(()=>{if(!y)throw new Error("Not eligible to convert stake");a(),m(),e()},[y,a,m,e]);return(0,_e.useEffect)(()=>{y&&u()},[y]),{title:i?w.t("convertStakeQuestBannerTitle"):w.t("convertStakeBannerTitle"),canConvertStake:y,onPress:g}};s();c();var Cn=L(E());s();c();var xn=t=>{let e=I.getMintTokenTransaction({stakePoolAddress:t.stakePoolAddress,ownerAddress:t.ownerAddress,amount:t.amount,poolTokenAccountAddress:t.poolTokenAccountAddress}),o=!!t.stakePoolAddress&&!!t.ownerAddress&&!!t.amount;return D({enabled:o,queryKey:e,staleTime:1/0,async queryFn(){if(T.isSolanaNetworkID(t.networkId))try{let{transaction:n,signers:r}=await to(t);return{networkID:t.networkId,transaction:n,signers:r}}catch(n){throw W.captureError(n,"staking"),n}throw new Error(`Unsupported networkId: ${t.networkId}`)}})};var la=t=>{let e=b(f=>f.provider),o=b(f=>f.amount),n=b(f=>f.setTransactionData),{data:r}=V("solana"),{connection:a}=$(),{fungible:u}=H({key:e?.stakePoolTokenMetadata.metadata.address||""}),i=u?ye(u):void 0,{trackMintApprovedByUser:d}=re(),{data:p,isPending:m}=xn({amount:O(o).toNumber(),connection:a,networkId:e?.chainId??T.solana.mainnetID,stakePoolAddress:e?.stakePoolAddress??"",ownerAddress:r?.address??"",poolTokenAccountAddress:i}),l=Ve({process:"mint",transactionData:p,isLoadingTransactionData:m}),k=(0,Cn.useCallback)(()=>{d({provider:e,payAmount:l.payAsset?.amount,receiveAmount:l.receiveAsset?.amount,payAmountUSD:l.payAsset?.amountUsd,receiveAmountUSD:l.receiveAsset?.amountUsd,payAssetTitle:l.payAsset?.title,receiveAssetTitle:l.receiveAsset?.title,providerName:l.providerName}),n(p),t()},[d,e,l.payAsset?.amount,l.payAsset?.amountUsd,l.payAsset?.title,l.receiveAsset?.amount,l.receiveAsset?.amountUsd,l.receiveAsset?.title,l.providerName,n,p,t]);return{...l,onPrimaryButtonPress:k}};s();c();var Ln=L(E());function ma(t){return typeof t=="string"?t:t?Ft(kt(t)):""}var fa=t=>{let e=ma(t),{data:o={}}=Ce();return(0,Ln.useMemo)(()=>!!Object.values(o).flat().find(n=>n.stakePoolTokenMetadata.caip19===e),[e,o])};s();c();var In=L(E());var ka=({method:t,goToInfoPage:e,goToInitFlowPage:o})=>{let{usageState:n}=Qt();return(0,In.useCallback)(()=>{n[t]?o():e()},[e,o,t,n])};s();c();export{Nn as a,Rn as b,Bn as c,Vn as d,_n as e,Wn as f,Gn as g,He as h,Jn as i,Xn as j,tr as k,nr as l,tt as m,sr as n,oe as o,xt as p,pr as q,da as r,yr as s,pa as t,re as u,br as v,la as w,Tr as x,fa as y,ka as z,Pr as A};
/*! Bundled license information:

@noble/hashes/esm/utils.js:
  (*! noble-hashes - MIT License (c) 2022 Paul Miller (paulmillr.com) *)
*/
//# sourceMappingURL=chunk-QINBGLLG.js.map
