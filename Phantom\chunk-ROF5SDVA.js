import{o as a}from"./chunk-WIQ4WVKX.js";import{S as i}from"./chunk-ALUTR72U.js";import{h as e,n as o}from"./chunk-3KENBVE7.js";e();o();var t=a.div`
  height: ${r=>r.diameter}px;
  width: ${r=>r.diameter}px;
  margin: ${r=>r.margin};
  background-color: ${r=>r.alpha&&r.color?i(r.color,r.alpha):r.color};
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  opacity: ${r=>r.opacity};
  overflow: hidden;
  ${r=>r.includeDarkBoxShadow?"box-shadow: inset 0px 0px 4px rgba(0, 0, 0, 0.25);":""}
`;t.defaultProps={color:"#333333",diameter:24,opacity:1};export{t as a};
//# sourceMappingURL=chunk-ROF5SDVA.js.map
