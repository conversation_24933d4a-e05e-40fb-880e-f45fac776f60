import{a as z0}from"./chunk-RJRYVRTS.js";import{a as j0,b as O0,c as U0}from"./chunk-O5AAGNHJ.js";import{a as $0}from"./chunk-VHCQKD7Y.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{d as N0,e as M0}from"./chunk-CCUXU2GU.js";import{h as _0}from"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import{a as W0,e as H0}from"./chunk-P5LBFEHG.js";import{b as i0}from"./chunk-S24UABH5.js";import"./chunk-X3ESGVCB.js";import"./chunk-SHAEZV7V.js";import{a as j}from"./chunk-CCQRCL2K.js";import{h as t0}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-W27Z2YZM.js";import"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import{j as I0,k as G0}from"./chunk-OKP6DFCI.js";import{Ba as v0,c as L0,o as r,rb as b}from"./chunk-WIQ4WVKX.js";import{$ as P0,_ as V0,ia as h,k as D0,x as w0,y as F0}from"./chunk-F3RUX6TF.js";import"./chunk-LURFXJDV.js";import{p as S0,v as A0}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import{c as G,e as N}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{r as u0}from"./chunk-7ZN4F6J4.js";import{Bb as T0,Cb as E0,Ja as C0,M as b0,N as g0,Qb as B0}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import{h as _}from"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as f0,L as o0,P as I,Pa as d0,Ra as y0,pe as h0}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as Z}from"./chunk-56SJOU6P.js";import{b as k0}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{ta as p0}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as c0}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as m0,h as X,n as Y}from"./chunk-3KENBVE7.js";X();Y();var t=m0(c0());X();Y();var i=m0(c0());var R0={v:"4.8.0",meta:{g:"LottieFiles AE 1.1.0",a:"",k:"",d:"",tc:""},fr:60,ip:0,op:241,w:250,h:250,nm:"Ico-Burn-09",ddd:0,assets:[{id:"comp_0",layers:[{ddd:0,ind:1,ty:3,nm:"NULL ",parent:4,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:5,s:[91]},{t:25,s:[0]}],ix:10},p:{a:0,k:[0,0,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:5,op:61,st:0,bm:0},{ddd:0,ind:2,ty:4,nm:"x",parent:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:5,s:[-71]},{t:25,s:[0]}],ix:10},p:{s:!0,x:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.402],y:[.555]},t:5,s:[50.202]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:13,s:[45.713]},{i:{x:[.59],y:[.851]},o:{x:[.333],y:[0]},t:21.924,s:[53.853]},{i:{x:[.341],y:[1]},o:{x:[.312],y:[-.641]},t:32.924,s:[48.513]},{t:60,s:[50.837]}],ix:3},y:{a:0,k:49.988,ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[101.065,101.065,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.12,y:1},o:{x:.001,y:.88},t:5,s:[{i:[[0,0],[0,0],[1.225,-1.225],[0,0],[1.224,1.226],[0,0],[0,0],[1.225,1.216],[0,0],[-1.227,1.227],[0,0],[0,0],[-1.224,1.224],[0,0],[-1.224,-1.224],[0,0],[0,0],[-1.223,-1.231],[0,0],[1.221,-1.221]],o:[[0,0],[1.226,1.223],[0,0],[-1.225,1.225],[0,0],[0,0],[-1.221,1.221],[0,0],[-1.231,-1.223],[0,0],[0,0],[-1.224,-1.224],[0,0],[1.224,-1.224],[0,0],[0,0],[1.227,-1.227],[0,0],[1.216,1.225],[0,0]],v:[[7.866,-.893],[11.468,3.218],[11.47,7.652],[7.106,12.016],[2.672,12.014],[-.892,7.866],[-4.434,10.848],[-8.858,10.856],[-13.218,6.527],[-13.226,2.087],[-9.689,-.891],[-12.737,-4.482],[-12.737,-8.914],[-8.371,-13.28],[-3.939,-13.28],[-.891,-9.689],[2.491,-12.586],[6.931,-12.578],[11.26,-8.218],[11.252,-3.794]],c:!0}]},{t:25,s:[{i:[[0,0],[0,0],[1.225,-1.225],[0,0],[1.224,1.226],[0,0],[0,0],[1.225,1.216],[0,0],[-1.227,1.227],[0,0],[0,0],[-1.224,1.224],[0,0],[-1.224,-1.224],[0,0],[0,0],[-1.223,-1.231],[0,0],[1.221,-1.221]],o:[[0,0],[1.226,1.223],[0,0],[-1.225,1.225],[0,0],[0,0],[-1.221,1.221],[0,0],[-1.231,-1.223],[0,0],[0,0],[-1.224,-1.224],[0,0],[1.224,-1.224],[0,0],[0,0],[1.227,-1.227],[0,0],[1.216,1.225],[0,0]],v:[[7.866,-.893],[26.303,17.546],[26.305,21.98],[21.941,26.344],[17.507,26.342],[-.892,7.866],[-19.181,26.154],[-23.605,26.162],[-27.965,21.833],[-27.973,17.393],[-9.689,-.891],[-27.976,-19.178],[-27.976,-23.61],[-23.61,-27.976],[-19.178,-27.976],[-.891,-9.689],[17.393,-27.973],[21.833,-27.965],[26.162,-23.605],[26.154,-19.181]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.921568632126,.215686276555,.258823543787,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:5,op:61,st:0,bm:0},{ddd:0,ind:3,ty:4,nm:"loader",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[150.182,138.496,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[90,90,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.12,.12],y:[1,1]},o:{x:[.001,.001],y:[.88,.88]},t:-40,s:[4,4]},{i:{x:[.833,.833],y:[1,1]},o:{x:[.167,.167],y:[0,0]},t:0,s:[48,48]},{t:4,s:[35.5,35.5]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:1,k:[{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:0,s:[25.31]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:4,s:[39.91]},{i:{x:[.8],y:[.5]},o:{x:[.7],y:[0]},t:28,s:[25.31]},{t:58,s:[74.876]}],ix:1},e:{a:1,k:[{i:{x:[.676],y:[.815]},o:{x:[.348],y:[.188]},t:0,s:[24.979]},{i:{x:[.659],y:[.793]},o:{x:[.331],y:[.217]},t:2,s:[40.449]},{i:{x:[.382],y:[1]},o:{x:[.169],y:[.86]},t:4,s:[53.971]},{t:30,s:[75.021]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:4,s:[32.2]},{t:60,s:[483]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[.921568632126,.215686276555,.258823543787,1],ix:3},o:{a:0,k:100,ix:4},w:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:1,s:[5]},{t:4,s:[10]}],ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:0,op:5,st:0,bm:0},{ddd:0,ind:4,ty:4,nm:"bg",sr:1,ks:{o:{a:0,k:10,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:150,ix:3},y:{a:0,k:150,ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.487,.487,.833],y:[1,1,1]},o:{x:[.3,.3,.167],y:[.356,.356,0]},t:0,s:[100,100,100]},{i:{x:[.309,.309,.833],y:[1,1,1]},o:{x:[.498,.498,.167],y:[0,0,0]},t:2,s:[128.634,128.634,100]},{i:{x:[.55,.55,.833],y:[1,1,1]},o:{x:[.386,.386,.167],y:[-.01,-.01,0]},t:8,s:[93.131,93.131,100]},{i:{x:[.373,.373,.833],y:[1,1,1]},o:{x:[.18,.181,0],y:[0,0,0]},t:18,s:[104.691,104.691,100]},{i:{x:[.442,.442,.833],y:[1,1,1]},o:{x:[.146,.146,0],y:[0,0,0]},t:37,s:[98.292,98.292,100]},{t:60,s:[100,100,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[82,82],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[1,.239215686917,.239215686917,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:61,st:0,bm:0}]},{id:"comp_1",layers:[{ddd:0,ind:1,ty:4,nm:"dot",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:18.5,ix:3},y:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:0,s:[102.5]},{t:20,s:[40.5]}],ix:4}},a:{a:0,k:[32.5,-192.5,0],ix:1},s:{a:1,k:[{i:{x:[.8,.8,.8],y:[.5,.5,1]},o:{x:[.7,.7,.7],y:[0,0,0]},t:10,s:[100,100,100]},{t:20,s:[0,0,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[37,37],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.12939453125,.898010253906,.436859130859,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[32.5,-192.5],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:28,st:-150,bm:0}]},{id:"comp_2",layers:[{ddd:0,ind:1,ty:3,nm:"NULL CONTROL ",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[40.131,41.937,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:2,ty:4,nm:"Shape Layer 1",parent:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:1,k:[{i:{x:.833,y:.743},o:{x:.12,y:.846},t:0,s:[23.744,69.938,0],to:[2.513,-4.357,0],ti:[-12.423,1.284,0]},{i:{x:.833,y:.833},o:{x:.167,y:.111},t:17,s:[40.68,52.173,0],to:[24.037,-2.484,0],ti:[8.971,10.018,0]},{t:29,s:[50.994,22.688,0]}],ix:2},a:{a:0,k:[30.875,-36.375,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:19,s:[100,100,100]},{t:29,s:[0,0,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[9.75,9.75],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.886274516582,.254901975393,.243137255311,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[30.875,-36.375],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:-31,bm:0}]}],layers:[{ddd:0,ind:1,ty:0,nm:"Ico-Fail-01",refId:"comp_0",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:125,ix:3},y:{a:0,k:125,ix:4}},a:{a:0,k:[150,150,0],ix:1},s:{a:0,k:[96.187,96.187,100],ix:6}},ao:0,w:300,h:300,ip:181,op:242,st:181,bm:0},{ddd:0,ind:2,ty:0,nm:"burst-green-01",refId:"comp_1",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:45,ix:10},p:{a:0,k:[156.211,104.711,0],ix:2},a:{a:0,k:[18.5,75,0],ix:1},s:{a:0,k:[40,40,100],ix:6}},ao:0,w:37,h:150,ip:126,op:154,st:126,bm:0},{ddd:0,ind:3,ty:4,nm:"check",parent:6,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:126,s:[91]},{t:146,s:[0]}],ix:10},p:{a:0,k:[-1.804,2.431,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.12,y:1},o:{x:.001,y:.88},t:126,s:[{i:[[3.277,-3.251],[0,0],[0,0],[3.095,-3.251],[0,0],[-3.094,-3.07],[0,0],[-3.277,3.251],[0,0],[3.277,3.251],[0,0]],o:[[0,0],[0,0],[-3.277,-3.251],[0,0],[-3.094,3.251],[0,0],[3.277,3.251],[0,0],[3.277,-3.07],[0,0],[-3.277,-3.251]],v:[[14.745,-17.571],[-16.181,12.789],[-43.121,-13.94],[-54.771,-13.94],[-64.237,-4.729],[-64.237,6.829],[-22.006,48.729],[-10.174,48.729],[35.861,3.199],[35.861,-8.36],[26.577,-17.571]],c:!0}]},{t:146,s:[{i:[[3.277,-3.251],[0,0],[0,0],[3.095,-3.251],[0,0],[-3.094,-3.07],[0,0],[-3.277,3.251],[0,0],[3.277,3.251],[0,0]],o:[[0,0],[0,0],[-3.277,-3.251],[0,0],[-3.094,3.251],[0,0],[3.277,3.251],[0,0],[3.277,-3.07],[0,0],[-3.277,-3.251]],v:[[55.904,-58.729],[-16.181,12.789],[-43.121,-13.94],[-54.771,-13.94],[-64.237,-4.729],[-64.237,6.829],[-22.006,48.729],[-10.174,48.729],[77.02,-37.96],[77.02,-49.519],[67.736,-58.729]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.129411756992,.898039221764,.436862826347,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[43.733,43.733],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Vector",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:126,op:181,st:102,bm:0},{ddd:0,ind:4,ty:4,nm:"loader 3",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[125.182,113.496,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[90,90,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.12,.12],y:[1,1]},o:{x:[.001,.001],y:[.88,.88]},t:81,s:[4,4]},{i:{x:[.833,.833],y:[1,1]},o:{x:[.167,.167],y:[0,0]},t:121,s:[48,48]},{t:125,s:[35.5,35.5]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:1,k:[{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:121,s:[25.31]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:125,s:[39.91]},{i:{x:[.8],y:[.5]},o:{x:[.7],y:[0]},t:149,s:[25.31]},{t:179,s:[74.876]}],ix:1},e:{a:1,k:[{i:{x:[.676],y:[.815]},o:{x:[.348],y:[.188]},t:121,s:[24.979]},{i:{x:[.659],y:[.793]},o:{x:[.331],y:[.217]},t:123,s:[40.449]},{i:{x:[.382],y:[1]},o:{x:[.169],y:[.86]},t:125,s:[53.971]},{t:151,s:[75.021]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:121,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:125,s:[32.2]},{t:181,s:[483]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[.116485595703,.808227539063,.391937255859,1],ix:3},o:{a:0,k:100,ix:4},w:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:122,s:[5]},{t:125,s:[10]}],ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:121,op:126,st:121,bm:0},{ddd:0,ind:5,ty:4,nm:"bg - outer 2",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:123,s:[10]},{t:143,s:[0]}],ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:125,ix:3},y:{a:0,k:125,ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[70,70,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.166,.166],y:[1,1]},o:{x:[.099,.099],y:[.892,.892]},t:121,s:[25,25]},{t:153,s:[141,141]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.129411756992,.898039221764,.436862826347,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:121,op:144,st:121,bm:0},{ddd:0,ind:6,ty:4,nm:"bg 2",sr:1,ks:{o:{a:0,k:10,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:125,ix:3},y:{a:0,k:125,ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.487,.487,.833],y:[1,1,1]},o:{x:[.3,.3,.167],y:[.356,.356,0]},t:121,s:[100,100,100]},{i:{x:[.309,.309,.833],y:[1,1,1]},o:{x:[.498,.498,.167],y:[0,0,0]},t:123,s:[128.634,128.634,100]},{i:{x:[.55,.55,.833],y:[1,1,1]},o:{x:[.386,.386,.167],y:[-.01,-.01,0]},t:129,s:[93.131,93.131,100]},{i:{x:[.373,.373,.833],y:[1,1,1]},o:{x:[.18,.181,0],y:[0,0,0]},t:139,s:[104.691,104.691,100]},{i:{x:[.442,.442,.833],y:[1,1,1]},o:{x:[.146,.146,0],y:[0,0,0]},t:158,s:[98.292,98.292,100]},{t:181,s:[100,100,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[82,82],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.129411756992,.898039221764,.436862826347,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:121,op:181,st:121,bm:0},{ddd:0,ind:7,ty:0,nm:"fire-spark-1",refId:"comp_2",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[138,96,0],ix:2},a:{a:0,k:[40,40,0],ix:1},s:{a:0,k:[-55.75,55.75,100],ix:6}},ao:0,w:80,h:80,ip:68,op:98,st:68,bm:0},{ddd:0,ind:8,ty:0,nm:"fire-spark-1",refId:"comp_2",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[147,105.5,0],ix:2},a:{a:0,k:[40,40,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:80,h:80,ip:65,op:95,st:65,bm:0},{ddd:0,ind:9,ty:0,nm:"fire-spark-1",refId:"comp_2",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[97,107.5,0],ix:2},a:{a:0,k:[40,40,0],ix:1},s:{a:0,k:[-72.35,72.35,100],ix:6}},ao:0,w:80,h:80,ip:86,op:116,st:86,bm:0},{ddd:0,ind:10,ty:4,nm:"fire 3",parent:11,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[0,66.043,0],ix:2},a:{a:0,k:[0,44.091,0],ix:1},s:{a:1,k:[{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:8,s:[100,100,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:23,s:[117.268,117.268,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:38,s:[100,100,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:53,s:[117.268,117.268,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:68,s:[100,100,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:83,s:[117.268,117.268,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:98,s:[100,100,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:113,s:[117.268,117.268,100]},{t:128,s:[100,100,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[2.606,-2.568],[7.813,-7.813],[0,-5.18],[-6.894,-6.894],[-13.424,0],[-4.987,4.905],[0,12.054],[3.16,2.907],[2.312,-2.064],[.782,-.661],[-.343,1.529],[0,3.887],[3.753,4.54],[0,0]],o:[[0,0],[-14.892,14.892],[0,7.685],[5.957,5.957],[13.424,0],[6.608,-6.499],[0,-12.928],[-1.775,-1.633],[-2.312,2.064],[-.782,.661],[.343,-1.529],[0,-3.887],[0,0],[-2.331,-2.82]],v:[[-4.285,-42.297],[-23.265,-23.851],[-37.596,10.163],[-26.2,33.904],[-.128,44.091],[25.512,34.508],[37.598,7.66],[30.088,-15.563],[23.552,-16.467],[18.627,-12.07],[17.019,-13.335],[18.46,-19.241],[12.433,-32.714],[4.899,-41.828]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.886274516582,.254901975393,.243137255311,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:121,st:0,bm:0},{ddd:0,ind:11,ty:4,nm:"fire 2",parent:12,sr:1,ks:{o:{a:0,k:50,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[0,88.182,0],ix:2},a:{a:0,k:[0,66.043,0],ix:1},s:{a:1,k:[{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:2,s:[100,100,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:17,s:[117.268,117.268,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:32,s:[100,100,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:47,s:[117.268,117.268,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:62,s:[100,100,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:77,s:[117.268,117.268,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:92,s:[100,100,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:107,s:[117.268,117.268,100]},{t:122,s:[100,100,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[3.904,-3.847],[11.703,-11.703],[0,-7.759],[-10.326,-10.326],[-20.108,0],[-7.47,7.347],[0,18.055],[4.733,4.354],[3.463,-3.092],[1.172,-.99],[-.514,2.29],[0,5.822],[5.621,6.8],[0,0]],o:[[0,0],[-22.306,22.306],[0,11.512],[8.923,8.923],[20.108,0],[9.898,-9.735],[0,-19.365],[-2.658,-2.445],[-3.463,3.092],[-1.172,.99],[.514,-2.29],[0,-5.822],[0,0],[-3.492,-4.224]],v:[[-6.419,-63.355],[-34.848,-35.726],[-56.314,15.223],[-39.244,50.784],[-.192,66.043],[38.214,51.689],[56.318,11.473],[45.068,-23.312],[35.277,-24.666],[27.901,-18.079],[25.493,-19.974],[27.651,-28.821],[18.623,-49.002],[7.338,-62.653]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.886274516582,.254901975393,.243137255311,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:121,st:0,bm:0},{ddd:0,ind:12,ty:4,nm:"fire 1",parent:13,sr:1,ks:{o:{a:0,k:15,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[0,19.99,0],ix:2},a:{a:0,k:[0,88.182,0],ix:1},s:{a:1,k:[{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:-2,s:[22.668,22.668,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:13,s:[26.868,26.868,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:28,s:[22.668,22.668,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:43,s:[26.868,26.868,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:58,s:[22.668,22.668,100]},{i:{x:[.833,.833,.833],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:73,s:[26.868,26.868,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:88,s:[22.668,22.668,100]},{i:{x:[.49,.49,.49],y:[1,1,1]},o:{x:[.51,.51,.51],y:[0,0,0]},t:103,s:[26.868,26.868,100]},{t:118,s:[22.668,22.668,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[5.212,-5.137],[15.626,-15.626],[0,-10.36],[-13.788,-13.788],[-26.849,0],[-9.975,9.81],[0,24.107],[6.32,5.814],[4.624,-4.129],[1.564,-1.322],[-.687,3.058],[0,7.774],[7.506,9.08],[0,0]],o:[[0,0],[-29.784,29.784],[0,15.371],[11.914,11.914],[26.849,0],[13.216,-12.998],[0,-25.857],[-3.549,-3.265],[-4.624,4.129],[-1.564,1.322],[.687,-3.058],[0,-7.774],[0,0],[-4.662,-5.64]],v:[[-8.571,-84.593],[-46.53,-47.702],[-75.191,20.326],[-52.4,67.808],[-.256,88.182],[51.024,69.017],[75.197,15.319],[60.175,-31.127],[47.103,-32.934],[37.254,-24.139],[34.039,-26.67],[36.92,-38.482],[24.866,-65.428],[9.798,-83.656]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[.886274516582,.254901975393,.243137255311,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:121,st:0,bm:0},{ddd:0,ind:13,ty:3,nm:"null-fire",parent:19,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[0,5,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.4,.4,.4],y:[1,1,1]},o:{x:[.12,.12,.12],y:[.556,.556,0]},t:5,s:[49,49,100]},{t:25,s:[180,180,100]}],ix:6}},ao:0,ip:5,op:121,st:0,bm:0},{ddd:0,ind:14,ty:3,nm:"NULL ",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[125,125,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:121,st:0,bm:0},{ddd:0,ind:15,ty:4,nm:"loader 2",parent:16,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[0,0,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[79.3,79.3],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:1,k:[{i:{x:[.8],y:[.5]},o:{x:[.7],y:[0]},t:88,s:[25.31]},{t:118,s:[74.876]}],ix:1},e:{a:1,k:[{i:{x:[.3],y:[1]},o:{x:[.2],y:[.5]},t:60,s:[24.979]},{t:90,s:[75.021]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:60,s:[0]},{t:120,s:[483]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[.921539306641,.215667724609,.258819580078,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:5,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:60,op:121,st:60,bm:0},{ddd:0,ind:16,ty:4,nm:"ring 2",parent:19,sr:1,ks:{o:{a:0,k:10,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[.182,.183,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.51,.51,.5],y:[.933,.933,1]},o:{x:[.494,.494,.5],y:[.045,.045,0]},t:60,s:[90,90,100]},{i:{x:[.507,.507,.5],y:[.919,.919,1]},o:{x:[.509,.509,.5],y:[-.07,-.07,0]},t:90,s:[100,100,100]},{t:120,s:[90,90,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[79.3,79.3],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"st",c:{a:0,k:[.921539306641,.215667724609,.258819580078,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:5,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:60,op:121,st:60,bm:0},{ddd:0,ind:17,ty:4,nm:"loader",parent:18,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[0,0,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.12,.12],y:[1,1]},o:{x:[.001,.001],y:[.88,.88]},t:3,s:[4,4]},{t:43,s:[79.333,79.333]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:1,k:[{i:{x:[.8],y:[.5]},o:{x:[.7],y:[0]},t:28,s:[25.31]},{t:58,s:[74.876]}],ix:1},e:{a:1,k:[{i:{x:[.3],y:[1]},o:{x:[.2],y:[.5]},t:0,s:[24.979]},{t:30,s:[75.021]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[0]},{t:60,s:[483]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[.921539306641,.215667724609,.258819580078,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:5,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:0,op:60,st:0,bm:0},{ddd:0,ind:18,ty:4,nm:"ring",parent:19,sr:1,ks:{o:{a:0,k:10,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[.182,.183,0],ix:2},a:{a:0,k:[0,0,0],ix:1},s:{a:1,k:[{i:{x:[.51,.51,.5],y:[.933,.933,1]},o:{x:[.494,.494,.5],y:[.045,.045,0]},t:0,s:[90,90,100]},{i:{x:[.507,.507,.5],y:[.919,.919,1]},o:{x:[.509,.509,.5],y:[-.07,-.07,0]},t:30,s:[100,100,100]},{t:60,s:[90,90,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.12,.12],y:[1,1]},o:{x:[.001,.001],y:[.88,.88]},t:3,s:[4,4]},{t:43,s:[79.333,79.333]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"st",c:{a:0,k:[.921539306641,.215667724609,.258819580078,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:5,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:100,ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1}],ip:3,op:60,st:0,bm:0},{ddd:0,ind:19,ty:3,nm:"bg",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:125,ix:3},y:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:0,s:[73]},{t:8,s:[125]}],ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:121,st:0,bm:0},{ddd:0,ind:20,ty:4,nm:"bg - outer",sr:1,ks:{o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:0,s:[20]},{t:32,s:[0]}],ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:125,ix:3},y:{a:1,k:[{i:{x:[.12],y:[1]},o:{x:[.001],y:[.88]},t:0,s:[73]},{t:8,s:[125]}],ix:4}},a:{a:0,k:[0,0,0],ix:1},s:{a:0,k:[70,70,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.166,.166],y:[1,1]},o:{x:[.099,.099],y:[.892,.892]},t:0,s:[25,25]},{t:32,s:[141,141]}],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[.921539306641,.215667724609,.258819580078,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[200,200],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 2",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:32,st:0,bm:0}],markers:[]};var e1=r(j).attrs({justify:"flex-end"})`
  height: 100%;
`,o1=r(j).attrs({align:"center",justify:"center"})`
  flex: 1;
  bottom: 55px;
  position: relative;
`,a1=r.div`
  width: 172px;
  height: 172px;
  position: relative;
  top: 48px;
`,r1=r(b).attrs({size:28,weight:500,color:"#FFFFFF",lineHeight:39.21})`
  margin-top: 24px;
`,n1=r(b).attrs({size:16,weight:400,color:"#999999",lineHeight:20.8})`
  margin-top: 8px;
  margin-left: 16px;
  margin-right: 16px;
  margin-bottom: 22px;
  span {
    color: #999999;
    font-weight: bold;
  }
`,s1=r(b).attrs({size:16,lineHeight:20.8,weight:500,color:"#AB9FF2"})`
  opacity: ${n=>n.disabled?.3:1};
`;var l1=[{segment:[0,59],loop:!1,autoAdvance:!0},{segment:[60,120],loop:!0,autoAdvance:!1},{identifier:"success",segment:[121,180],loop:!1,autoAdvance:!1},{identifier:"error",segment:[181,240],loop:!1,autoAdvance:!1}],x1=(n,l,c,S,d,k,g)=>{let{t:s}=Z(),{hideCollectibleBurnModal:y}=i0(),{popDetailView:x}=t0(),[C,u]=(0,i.useState)("needApproval"),[F,T]=(0,i.useState)(),{data:f}=f0(),{accountIdentifier:E,multiChainAddresses:M,networkID:p,isLedger:A,connection:O,owner:B}=(0,i.useMemo)(()=>{let a=f?.identifier??"",w=f?.type==="ledger",v=f?.addresses??[],l0=v.find(Z0=>d0.isSolanaNetworkID(Z0.networkID)),x0=l0?.networkID,Q0=new I.PublicKey(l0?.address??""),X0=g0(x0),Y0=b0(X0);return{accountIdentifier:a,multiChainAddresses:v,networkID:x0,isLedger:w,owner:Q0,connection:Y0}},[f]),[V,P]=(0,i.useState)("loading"),W=V==="error",U=V==="success",L=W?s("burnStatusErrorTitleWithCount",{count:l}):U?s("burnStatusSuccessTitleWithCount",{count:l}):s("burnStatusLoadingTitleWithCount",{count:l}),z=W?s("burnStatusErrorMessageWithCount",{count:l}):U?s("burnStatusSuccessMessageWithCount",{count:l,rebateAmount:k}):s("burnStatusLoadingMessageWithCount",{count:l,rebateAmount:k}),$=s("commandClose"),m=s("burnStatusViewTransactionText"),[o,e0]=(0,i.useState)(void 0),{mutateAsync:R}=E0(O),{mutateAsync:K}=F0(),D=h(a=>a.burnCollectibleTx),q=h(a=>a.resetBurnCollectible),J=(0,i.useCallback)(async()=>{if(!(!O||!D||!E||!B))try{let a={ownerAddress:B.toString(),networkID:p,data:{signature:""},type:"burn",display:{summary:{topLeft:{text:`${s("transactionsPendingBurningInterpolated",{name:g??s("tokenRowUnknownToken")})}`}}}},w=await R({accountIdentifier:E,feePayer:B,transaction:D,pendingTransactionInput:a}),v=o?.networkID?_(o.networkID):null;v?.chainName==="solana"&&N.burn.submittedTransaction(w,{...v,method:"signAndSendTransaction"}),await K({event:{chainId:p,address:n,event:"userBurned"}}),e0({networkID:p,id:w})}catch(a){let w="Failed to burn collectible";a.message&&(w=`Failed to burn collectible: ${a.message}`),p0.captureError(new Error(w),"collectibles"),P("error"),j0(a)&&T(i.default.createElement(U0,{ledgerActionError:a,onRetryClick:()=>{x()},onCancelClick:y}))}},[E,p,g,O,y,B,x,R,s,D,K,n,o?.networkID]);(0,i.useEffect)(()=>{if(D)try{A?T(i.default.createElement(O0,{ledgerAction:J,cancel:y})):J()}catch(a){console.error(a),P("error")}},[D,A]);let e=L0(),H=(0,i.useCallback)(()=>{q(),y(),e("/notifications")},[y,q,e]),Q=S0(M),a0=w0(M),q0=(0,i.useCallback)(()=>{Promise.all([a0(),Q()]),G.capture("burnCollectibleConfirmationSuccess",{data:{mint:n}}),A&&(u("confirmed"),H())},[H,A,n,Q,a0]);(0,i.useEffect)(()=>{o&&u("mining")},[o]);let{isSuccess:r0,isError:n0}=B0(q0,o??void 0);(0,i.useEffect)(()=>{o&&u("mining")},[o]),(0,i.useEffect)(()=>{let a=o?.networkID?_(o.networkID):null;r0?(a?.chainName==="solana"&&o?.id&&N.burn.transactionStatus(o.id,{...a,status:{type:"confirmed"}}),P("success")):n0&&(a?.chainName==="solana"&&o?.id&&N.burn.transactionStatus(o.id,{...a,status:{type:"error"}}),P("error"))},[r0,n0,o]);let{data:s0}=u0(o),J0=(0,i.useCallback)(()=>{self.open(s0)},[s0]);return{status:V,title:L,message:z,closeText:$,viewTransactionText:m,txHash:o?.id,isLedger:A,step:C,ledgerContent:F,onTransactionLinkClick:J0,onClose:H}},K0=i.default.memo(({mintAddress:n,amountToBurn:l,tokenStandard:c,collectionPubKey:S,tokenEdition:d,collectibleName:k,rebateAmount:g})=>{let s=x1(n,l,c,S,d,g,k);return i.default.createElement(m1,{...s})}),m1=i.default.memo(n=>{let{status:l,title:c,message:S,closeText:d,viewTransactionText:k,txHash:g,isLedger:s,step:y,ledgerContent:x,onTransactionLinkClick:C,onClose:u}=n,F=(0,i.useRef)(null);(0,i.useEffect)(()=>{switch(l){case"success":case"error":F.current?.setStage(l);break}},[l]);let T=null;return s&&y==="needApproval"?T=x:T=i.default.createElement(e1,null,i.default.createElement(o1,null,i.default.createElement(a1,null,i.default.createElement($0,{stages:l1,stageRef:F,path:R0,autoplay:!1,loop:!1})),i.default.createElement(r1,null,c),i.default.createElement(n1,null,S),i.default.createElement(s1,{disabled:!g,onClick:C},k)),i.default.createElement(I0,{onClick:u},d)),i.default.createElement(i.default.Fragment,null,T)});var c1=r.div`
  display: flex;
  position: relative;
  margin-top: 7.4px;
`,k1=r(b).attrs({size:28,weight:500,color:"#FFFFFF",lineHeight:39.21})`
  margin-top: 10px;
`,p1=r(b).attrs({size:14,weight:400,color:"#999999",lineHeight:20.8})`
  margin-top: 8px;
  margin-bottom: 8px;

  span {
    color: #999999;
    font-weight: bold;
  }
`,d1=r.div`
  padding: 16px;
  background: rgba(235, 55, 66, 0.1);
  width: 100%;
  border-radius: 6px;
  margin-top: 16px;
  margin-bottom: 16px;
`,y1=r.div`
  border-radius: 6px;
  padding: 13px 14px;
  background-color: #2a2a2a;
  width: 100%;
`,u1=r.div`
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  width: 100%;
  padding: 5px 0;
`,f1=r(b).attrs(n=>({size:14,color:n.color||"#FFFFFF",lineHeight:17,paddingLeft:8}))``,h1=r.div`
  background-color: #222;
  border-radius: 50%;
  right: -7%;
  bottom: -7%;
  height: 40%;
  width: 40%;
  display: flex;
  position: absolute;
`,b1=r.div`
  background: #eb3742;
  border-radius: 50%;
  border: 4px solid transparent;
  background-clip: content-box;
  display: flex;
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
`,g1=()=>t.default.createElement(h1,null,t.default.createElement(b1,null,t.default.createElement(v0,null))),C1=r(H0)`
  flex: 1;
`,T1=r(f1)`
  flex: 1;
  white-space: nowrap;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
`,E1=t.default.memo(({mintPubkey:n,collectionPubKey:l,tokenStandard:c,amount:S,name:d,compression:k,media:g,programId:s})=>{let y=P0(g,"image",!1,"medium"),x=S?parseInt(S,10):1,C=(0,t.useRef)(0),{connection:u}=T0(),F=D0(n,u).data,T=o0(C0),{data:f}=h0("solana"),E=f?.networkID??y0.Solana.Mainnet,M=(0,t.useMemo)(()=>new I.PublicKey(f?.address??""),[f?.address]);A0(f,"BURN_COLLECTIBLE");let p=F?.key??null,{data:A=0}=V0({tokenEdition:p,connection:u}),B=!!k?.compressed?k0(0):o0(A),V=h(e=>e.setBurnCollectibleTx),P=h(e=>e.burnCollectibleTx),W=h(e=>e.setBurnCollectibleTxFee),U=h(e=>e.burnCollectibleTxFee),L=h(e=>e.resetBurnCollectible);(0,t.useEffect)(()=>{C.current=C.current+1,L(),(async e=>{let{burnTxWithPriority:H,networkFeeUiAmount:Q}=await _0({mintAddress:n,owner:M,amountToBurn:x,isProgrammable:c==="ProgrammableNonFungible"||c==="ProgrammableNonFungibleEdition",collectionPubKey:l,tokenEdition:p,chainId:E,compression:k,programId:s});e===C.current&&(V(H),W(Q??""))})(C.current)},[x,u,V,W,n,M,p,L,E,c,l,k,s]);let{hideCollectibleBurnModal:z}=i0(),{pushDetailView:$}=t0(),{t:m}=Z(),[o,e0]=(0,t.useState)(!1),R=o&&P,K=[{label:m("collectibleBurnTokenWithCount",{count:x}),value:x===1?`${d}`:`${d} (${x})`},{label:m("collectibleBurnRebate"),value:`+${B} SOL`,tooltipContent:m("collectibleBurnRebateTooltip"),color:"#21E56F"},{label:m("collectibleBurnNetworkFee"),value:U||`${T} SOL`,tooltipContent:m("collectibleBurnNetworkFeeTooltip")}],D=(0,t.useCallback)(()=>{G.capture("collectibleBurnCTAClick");let e=_(E);e&&N.burn.approved(e),$(t.default.createElement(K0,{mintAddress:n,amountToBurn:x,tokenStandard:c,collectionPubKey:l,tokenEdition:p,rebateAmount:B.toNumber(),collectibleName:d}))},[$,n,x,l,p,d,E,c,B]),q=(0,t.useCallback)(()=>{G.capture("collectibleBurnCancelClick"),L(),z()},[z,L]),J=(0,t.useCallback)(()=>{G.capture("collectibleBurnTermsClick"),e0(!o)},[o]);return t.default.createElement(j,{align:"center"},t.default.createElement(c1,null,t.default.createElement(z0,{width:98,height:98,uri:y??"",borderRadius:"6px"}),t.default.createElement(g1,null)),t.default.createElement(k1,null,m("collectibleBurnTitleWithCount",{count:x})),t.default.createElement(p1,null,m("collectibleBurnDescriptionWithCount",{count:x})),t.default.createElement(y1,null,K.map(e=>t.default.createElement(u1,{key:e.label},t.default.createElement(C1,{tooltipAlignment:"topLeft",iconSize:12,lineHeight:17,fontWeight:500,info:e.tooltipContent?t.default.createElement(W0,null,e.tooltipContent):null},e.label),t.default.createElement(T1,{color:e.color},e.value)))),t.default.createElement(d1,{onClick:J},t.default.createElement(N0,null,t.default.createElement(M0,{"data-testid":"collectible-burn-terms-of-service-checkbox",checked:o,color:"#eb3742"}),t.default.createElement(b,{size:14,lineHeight:21,weight:500,color:"#eb3742"},m("collectibleBurnTermsOfService")))),t.default.createElement(G0,{primaryText:m("collectibleBurnCta"),primaryTheme:"warning",primaryDisabled:!R,onPrimaryClicked:D,secondaryText:m("commandCancel"),onSecondaryClicked:q}))}),Ii=E1;export{d1 as AlertIsland,E1 as CollectibleBurnPage,Ii as default};
//# sourceMappingURL=CollectibleBurnPage-VEBLLQEN.js.map
