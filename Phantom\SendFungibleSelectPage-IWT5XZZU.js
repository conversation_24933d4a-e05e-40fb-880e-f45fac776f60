import{a as K}from"./chunk-HNH7ZX4P.js";import{a as $}from"./chunk-A25JBJ2D.js";import"./chunk-Z3VAESXD.js";import"./chunk-PUJH7423.js";import"./chunk-XYFNIIUY.js";import"./chunk-O5AAGNHJ.js";import"./chunk-MXORZ3WH.js";import"./chunk-AUOG6CT3.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{Ma as N,U,V as W}from"./chunk-JD6NH5K6.js";import{g as B,h as Q,k as G}from"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{g as H}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import{h as z}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import{a as V}from"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{D as x}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{j as A}from"./chunk-OKP6DFCI.js";import{o as b}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import{d as h}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as D}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{gc as P,tc as _}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{Pa as w}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as O}from"./chunk-56SJOU6P.js";import{V as M,X as E}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as k}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as L,h as p,n as S}from"./chunk-3KENBVE7.js";p();S();var e=L(k());p();S();var g=L(k());var Z=g.default.memo(t=>{let{data:o,index:s,style:r}=t,{listItems:n,moveToSendForm:l,visibilityOverrides:a}=o,d=n[s],{key:u}=d.data;return g.default.createElement("div",{key:u,style:{...r,top:`${parseFloat(r.top)+20}px`}},g.default.createElement(U,{...W(d,a),onClick:()=>l(d),showCurrencyValues:!1}))},Q);var j=74,J=10,R=j+J,X=b.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
`,Y=b.div`
  position: relative;
  width: 100%;
`,ee=()=>{let{fungibles:t,isLoadingTokens:o,visibilityOverrides:s}=P({showHiddenMints:!0}),r=(0,e.useMemo)(()=>t.filter(i=>i.type!=="CompressedSPL"),[t]),[n,l]=(0,e.useState)(""),a=M(n)??"",d=E(r,a,_),{pushDetailView:u}=z(),{handleHideModalVisibility:f}=N(),y=h(i=>i.setSendFungibleKey),v=h(i=>i.resetSendSlice),{t:c}=O(),F=(0,e.useMemo)(()=>({close:c("commandClose"),search:c("assetListSearch"),unknownToken:c("assetDetailUnknownToken")}),[c]),I=(0,e.useCallback)(i=>l(i.currentTarget.value),[l]),T=(0,e.useCallback)(()=>{f("sendFungibleSelect"),v()},[f,v]),C=(0,e.useCallback)(i=>{let{data:m}=i;D.capture("selectSendAsset",{data:{address:m.tokenAddress,chain:m.chain.name,chainId:w.getChainID(m.chain.id),symbol:m.symbol??"",networkId:m.chain.id,type:"fungible"}}),y(i.data.key,i.type==="SPL"?i.data.splTokenAccountPubkey:void 0),u(e.default.createElement($,null))},[y,u]);return{data:(0,e.useMemo)(()=>({listItems:d,i18nStrings:F,searchQuery:n,handleSearch:I,handleCloseModal:T,moveToSendForm:C,visibilityOverrides:s}),[d,F,n,I,T,C,s]),loading:o}},te=e.default.memo(t=>{let o=(0,e.useRef)(null);return(0,e.useEffect)(()=>{setTimeout(()=>o.current?.focus(),200)},[]),e.default.createElement(e.default.Fragment,null,e.default.createElement(Y,null,e.default.createElement(H,{ref:o,tabIndex:0,placeholder:t.i18nStrings.search,maxLength:50,onChange:t.handleSearch,value:t.searchQuery})),e.default.createElement(G,null,e.default.createElement(x,null,({height:s,width:r})=>e.default.createElement(B,{width:r,height:s,itemData:{listItems:t.listItems,i18nStrings:t.i18nStrings,moveToSendForm:t.moveToSendForm,visibilityOverrides:t.visibilityOverrides},itemKey:(n,l)=>{let a=l.listItems[n];return`${a.data.key}-${a.data.chain.id}-${n}`},itemSize:R,itemCount:t.listItems.length},n=>e.default.createElement(Z,{...n})))))}),ie=()=>{let{data:t,loading:o}=ee();return e.default.createElement(X,null,o?e.default.createElement(K,null):e.default.createElement(te,{...t}),e.default.createElement(V,null,e.default.createElement(A,{onClick:t.handleCloseModal},t.i18nStrings.close)))},Oe=ie;export{ie as SendFungibleSelectPage,Oe as default};
//# sourceMappingURL=SendFungibleSelectPage-IWT5XZZU.js.map
