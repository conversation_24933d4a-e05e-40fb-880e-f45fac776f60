import{a as ke}from"./chunk-LUKK5BMR.js";import"./chunk-K5EEWGKQ.js";import{a as xt}from"./chunk-TQNG3Z6X.js";import{a as At,b as Dt}from"./chunk-Z3VAESXD.js";import{a as kt}from"./chunk-PUJH7423.js";import{a as Ne}from"./chunk-XYFNIIUY.js";import{b as Et}from"./chunk-O5AAGNHJ.js";import{a as Ft}from"./chunk-MXORZ3WH.js";import"./chunk-AUOG6CT3.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{Ma as Ae,W as yt}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import{b as ht}from"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import{c as Fe,d as Tt,i as wt}from"./chunk-AHRYSG4W.js";import{a as Ct}from"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import{b as gt}from"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as W}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as B}from"./chunk-CCQRCL2K.js";import{h as re,m as xe}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{a as ut}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import{a as we}from"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{a as St,b as Te,h as ft,j as bt,k as ce}from"./chunk-OKP6DFCI.js";import{c as pt,o as u,rb as G}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import{V as dt,s as lt,v as ct,z as mt}from"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import{o as je}from"./chunk-LURFXJDV.js";import{e as P,n as rt,p as it,q as st,v as at}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{b as Ye,c as fe,e as ne}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{s as Ke,z as ye}from"./chunk-7ZN4F6J4.js";import{Eb as Xe,Ib as et,J as We,Lb as tt,Qb as ot,Sb as nt,mb as Ze,ob as Je,pb as he,vb as Qe}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import{h as Ie}from"./chunk-OYGO47TI.js";import{o as $e}from"./chunk-SLQBAOEK.js";import{$d as _e,Pa as Se,Rb as ze,ib as Ge,pe as qe}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as le,n as Oe}from"./chunk-56SJOU6P.js";import{S as ve,T as Le,V as He,b as te,ha as Ue}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{oa as Re,ta as oe}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as Ce}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as ge,h as _,n as q}from"./chunk-3KENBVE7.js";_();q();var t=ge(Ce());_();q();var Pe=ge(Ce());var Me=({children:s})=>Pe.default.createElement(W,null,Pe.default.createElement(G,{color:"#EB3742",size:16,textAlign:"left"},s));_();q();var c=ge(Ce());_();q();var e=ge(Ce());var Lt=u(B).attrs({align:"center"})`
  height: 100%;
`,Ht=u(B).attrs({align:"center",justify:"center"})`
  flex: 1;
  flex-grow: 1;
`,Ut=u(B).attrs({align:"center",justify:"center",margin:"0 0 15px 0"})`
  position: relative;
  border-radius: 50%;
  background-color: ${ve("#AB9FF2",.2)};
  box-shadow: 0 0 0 20px ${ve("#AB9FF2",.2)};
`,Ot=u(G).attrs({size:28,lineHeight:33.89,weight:500,margin:"20px 0 10px 0"})``,Gt=u(G).attrs({size:16,lineHeight:19,weight:400,color:"#777777",margin:"0 0 10px 0"})``,vt=u(G).attrs({color:"#FFF",weight:400,size:16})``,zt=u(G).attrs({size:16,lineHeight:20.8,weight:500,color:"#AB9FF2"})``,jt=s=>{let{networkID:o,name:a,txRecipient:T,uiRecipient:m,multichainTransaction:l,senderAddress:r,exitSendFlow:d}=s,[f,C]=(0,e.useState)(!1),{popDetailView:i}=re(),{closeAllModals:n}=gt(),b=pt(),{data:y,status:F}=_e(),M=(0,e.useMemo)(()=>y?.addresses??[],[y]),{collectible:D,sendFormValues:V}=P(w=>w),{amountAsset:h,transactionSpeed:R}=V,L=D?.id??"",I=(0,e.useMemo)(()=>h.isZero()?new te(1):h,[h]),p=y?.type==="ledger",[k,x]=(0,e.useState)(!0),[me,ie]=(0,e.useState)(),[g,Y]=(0,e.useState)(void 0),A=it(M),H=ct(M),de=(0,e.useCallback)(()=>{let w=r.networkID,pe={chainType:r.addressType,chainID:w};fe.capture("sendAssetSuccess",{data:pe}),A(),H()},[r.addressType,r.networkID,A,H]),{isSuccess:Z,isError:U,error:v}=ot(de,g??void 0),z=f||U;(0,e.useEffect)(()=>{let w=g?.networkID?Ie(g.networkID):null;!g?.id||!w||w.chainName==="bitcoin"||(Z?(oe.addBreadcrumb("collectibles","transactionStatus","info",w),ne.send.transactionStatus(g.id,{...w,status:{type:"confirmed"}})):z&&(oe.addBreadcrumb("collectibles","transactionStatus","error",w),ne.send.transactionStatus(g.id,{...w,status:{type:"error"}})))},[Z,z,g]);let{mutateAsync:N}=mt(),{data:J}=Ke(),{addRecentAddress:Q}=ye(),O=F==="pending",{t:E}=le(),{title:X,isSuccess:$,isError:se,sendSuccessCondition:K,estimatedTime:S}=dt({networkID:o,isTxError:z,isTxConfirmed:Z,isTxSubmitted:!!g}),j=(0,e.useMemo)(()=>({titleText:X,retryText:E("commandRetry"),cancelText:E("commandCancel"),closeText:E("commandClose"),viewTransactionText:E("sendStatusViewTransaction")}),[E,X]),Ee=se?e.default.createElement(e.default.Fragment,null,e.default.createElement(we,{i18nKey:"sendStatusErrorMessageInterpolated",values:{uiRecipient:m}},"There was an error attempting to send tokens to ",e.default.createElement(vt,null,m)),v&&"data"in v?e.default.createElement("div",{className:ut({marginY:16})},e.default.createElement(Dt,{errorReason:v.data.errorReason,programId:v.data.programId,customErrorReason:v.data.customErrorReason,customErrorCode:v.data.customErrorCode})):null):$&&K==="CONFIRMATION"?e.default.createElement(we,{i18nKey:"sendStatusSuccessMessageInterpolated",values:{uiRecipient:m}},"Your tokens were successfully sent to ",e.default.createElement(vt,null,m)):$&&K==="SUBMISSION"?e.default.createElement(we,{i18nKey:"sendStatusEstimatedTransactionTime",values:{time:S}},"Estimated Transaction Time: ",S):void 0,De=(0,e.useCallback)(()=>{if(!g)return;let{id:w}=g,ue=J?.explorers[o],pe=$e({networkID:o,endpoint:"transaction",explorerType:ue,param:w});self.open(pe)},[o,J,g]),be=(0,e.useCallback)(()=>{d();let{sendSuccessRedirect:w}=We.get(o);switch(w(Ye("kill-base-history"))){case"history":b("/notifications");break;case"home":b("/");break}},[d,b,o]),{data:Ve}=he({networkID:o,multichainTransaction:l,transactionSpeed:R,queryOptions:{refetchInterval:!1}}),Bt=(0,e.useCallback)(async()=>{let w={id:L,amount:I.toNumber()},ue=async()=>{let pe=Tt(o,R,r,a,m,I.toNumber(),E);try{Q({address:T,chainID:r.networkID,timestamp:Date.now()});let ae=await N({multichainTransaction:l,pendingTransactionInput:pe,senderAddress:r,optimisticUpdate:w,gasEstimation:Ve}),ee=Ie(o),Mt=ee.chainType==="solana"?"signAndSendTransaction":ee.chainType==="eip155"?"eth_sendRawTransaction":"UNKNOWN";oe.addBreadcrumb("collectibles","submittedTransaction","info",{...ee,txReceipt:ae.id,method:Mt}),ee.chainType==="solana"?ne.send.submittedTransaction(ae.id,{...ee,method:"signAndSendTransaction"}):ee.chainType==="eip155"&&ne.send.submittedTransaction(ae.id,{...ee,method:"eth_sendRawTransaction"}),Y(ae)}catch(ae){C(!0),oe.captureError(ae,"collectibles")}};p?ie(e.default.createElement(Et,{ledgerAction:async()=>{await ue(),x(!1)},cancel:n,ledgerApp:je(o)})):await ue()},[L,I,p,o,R,r,a,m,E,Q,T,N,l,Ve,n]);return(0,e.useEffect)(()=>{O||(!p||k)&&Bt()},[O]),{i18nStrings:j,message:Ee,txReceipt:g,isSuccess:$,isError:se,isLedger:p,needsLedgerApproval:k,ledgerContent:me,onTransactionLinkClick:De,onRetry:i,onCancel:d,onClose:be}},$t=e.default.memo(s=>{let{i18nStrings:o,message:a,txReceipt:T,isSuccess:m,isError:l,isLedger:r,needsLedgerApproval:d,ledgerContent:f,onTransactionLinkClick:C,onRetry:i,onCancel:n,onClose:b}=s;return r&&d?e.default.createElement(e.default.Fragment,null,f??null):e.default.createElement(Lt,null,e.default.createElement(Ht,null,e.default.createElement(Te,{mode:"wait",initial:!0},e.default.createElement(St.div,{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2}},l?e.default.createElement(Ne,{type:"failure"}):m?e.default.createElement(Ne,{type:"success"}):e.default.createElement(Ut,null,e.default.createElement(ft,{diameter:54,color:"#AB9FF2",trackColor:"#181818"})))),e.default.createElement(Ot,null,o.titleText),a&&e.default.createElement(Gt,null,a),T&&e.default.createElement(zt,{onClick:C},o.viewTransactionText)),l&&e.default.createElement(ce,{primaryText:o.retryText,onPrimaryClicked:i,secondaryText:o.cancelText,onSecondaryClicked:n}),!l&&!!T&&e.default.createElement(W,{justify:"center"},e.default.createElement(bt,{onClick:b},o.closeText)))}),It=s=>{let o=jt(s);return e.default.createElement($t,{...o})};var Kt=u(B).attrs({align:"center"})`
  height: 100%;
`,_t=u(B).attrs({align:"center",justify:"space-between"})`
  height: 100%;
`,qt=u(B).attrs({align:"center",paddingTop:"25px"})`
  border-radius: 6px;
`,Wt=u(G).attrs({size:38,weight:500,lineHeight:46})`
  max-width: 100%;
  margin-top: 18px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
`,Yt=u(W).attrs({justify:"center"})`
  max-width: 275px;
`,Nt=u(G).attrs({color:"#777",size:16})`
  word-break: break-all;
`,Zt=(s,o)=>{let{status:a,data:T}=Ze({networkID:s}),m=P(n=>n.sendFormValues),l=P(n=>n.setSendFormValues),r=(0,c.useCallback)(n=>{l({...m,transactionSpeed:n})},[m,l]),{popDetailView:d,pushDetailView:f}=re(),C,{transactionSpeed:i}=m;return a!=="error"&&T&&i&&(C=()=>f(c.default.createElement(Ft,{onSelectTransactionSpeed:r,selectedTransactionSpeed:i,networkID:s,transactionUnitAmount:o,closeModal:d}))),{transactionSpeed:i,openTransactionSettings:C||void 0}},Jt=c.default.memo(s=>{let{i18nStrings:o,media:a,collectibleChainData:T,displayAmountAndName:m,uiRecipient:l,txRecipient:r,primaryDisabled:d,handleSubmit:f,exitSendFlow:C,summaryRows:i}=s;return c.default.createElement(Kt,null,c.default.createElement(xe,null,o.pageHeaderText),c.default.createElement(_t,null,c.default.createElement(qt,null,c.default.createElement(ke,{media:a,collectibleChainData:T,width:100,height:100}),c.default.createElement(Wt,null,m),c.default.createElement(Yt,null,c.default.createElement(xt,{delayedChildren:c.default.createElement(yt,{alignment:"bottomCenter",copyString:r},c.default.createElement(Nt,null,`to ${l}`))},c.default.createElement(Nt,null,`to ${l}`)))),c.default.createElement(ht,{rows:i,borderRadius:"6px 6px 6px 6px",padding:16,fontSize:16}),c.default.createElement(Te,{mode:"wait",initial:!1},c.default.createElement(ce,{primaryText:o.sendText,secondaryText:o.cancelText,primaryDisabled:d,onPrimaryClicked:f,onSecondaryClicked:C}))))}),Qt=s=>{let{txRecipient:o,networkID:a,media:T,assetSymbol:m,networkName:l,uiRecipient:r,uiAmount:d,exitSendFlow:f}=s,{pushDetailView:C}=re(),{t:i}=le(),{collectible:n,sendFormValues:b,shouldRequireAmount:y,setSendFormValues:F}=P(j=>j),M=n?.chainData,{data:D}=qe({address:n?.owner,networkID:a});at(D,"SEND_COLLECTIBLE");let V=Se.getNetworkName(a),h=(0,c.useMemo)(()=>({transactionsTo:i("transactionsTo"),pageHeaderText:i("sendConfirmationPrimaryText"),networkText:i("sendFungibleSummaryNetwork"),networkFeeText:i("sendFungibleSummaryNetworkFee"),estimatedTimeText:i("sendFungibleSummaryEstimatedTime"),sendText:i("commandSend"),cancelText:i("commandCancel"),networkFeeTextDescription:i("networkFeesTooltipDescription",{chainName:V})}),[i,V]),L=(0,c.useMemo)(()=>n?.chainData?Fe(n?.chainData):!1,[n?.chainData])?`${Ue(d)} ${m||n?.name}`:`${n?.name}`,I=(0,c.useMemo)(()=>nt.get(a).displayFeeTooltip,[a]),p=n?.chainData,{data:k,error:x}=lt({collectible:n,sendFormValues:b,shouldRequireAmount:y}),{data:me,isError:ie}=Je(k),{transactionSpeed:g,openTransactionSettings:Y}=Zt(a,me),{data:A,isFetched:H,isSuccess:de}=he({networkID:a,multichainTransaction:k,transactionSpeed:g,queryOptions:{refetchInterval:!1}}),{data:Z}=Qe(n?.chain.id,A),{hasSufficientFunds:U,isLoading:v,nativeTokenSymbol:z}=rt(a,D?.addressType,A,void 0),N=tt({networkID:a,gasEstimation:A,gasEstimationPrice:Z}),J=H&&!de,Q=(0,c.useMemo)(()=>!!A&&!v&&(U==="sufficient"||U==="unknown")&&!!N,[A,U,v,N]),O=(0,c.useMemo)(()=>ie?i("transactionGasLimitError"):J?i("transactionGasEstimationError"):U==="insufficient"?i("transactionNotEnoughNative",{nativeTokenSymbol:z}):x instanceof Oe?x.message:"",[ie,J,U,x,i,z]),E=Xe(D),{handleHideModalVisibility:X,handleShowModalVisibility:$}=Ae(),se=Le((0,c.useCallback)(()=>{if(!(!k||!D)){if(E){fe.capture("showCollectibleTxSubmissionFailureModal"),$("txSubmissionCheckFailure",{...E,onButtonClick:()=>{X("txSubmissionCheckFailure")}});return}if(F({...b,multichainTransaction:k}),p){let j=wt({chainID:a,amount:d,symbol:m,collectible:p});oe.addBreadcrumb("collectibles","approved","info",{chainId:a,...j.asset}),ne.send.approved(j)}fe.capture("sendAsset",{data:{chainId:Se.getChainID(a),networkId:a,symbol:m,type:"collectible"}}),C(c.default.createElement(It,{networkID:a,name:m,displayAmountAndName:L,txRecipient:o,uiRecipient:r,multichainTransaction:k,exitSendFlow:f,senderAddress:D}))}},[k,D,E,F,b,p,m,a,C,L,o,r,f,d,$,X]),1e3),K=st(A,i),S=[{label:h.transactionsTo,value:r},{label:h.networkText,value:l,color:"#FFF"},{label:h.networkFeeText,onClick:Y,value:N.length>0?N:O?null:c.default.createElement(Ct,{width:"75px",height:"8px",borderRadius:"8px",backgroundColor:"#484848"}),color:O?"#EB3742":"#FFF",leftSubtext:O||K?.leftSubtext,leftSubtextColor:O?"#EB3742":K?.leftSubtextColor,rightSubtext:K?.rightSubtext,tooltipContent:I?h.networkFeeTextDescription:void 0}];return{i18nStrings:h,media:T,collectibleChainData:M,displayAmountAndName:L,uiRecipient:r,txRecipient:o,primaryDisabled:!Q,summaryRows:S,handleSubmit:se,exitSendFlow:f}},Pt=s=>{let o=Qt(s);return c.default.createElement(Jt,{...o})};var Xt=u(B).attrs({justify:"space-between"})`
  height: 100%;
  overflow-y: scroll;
`,eo=u(B).attrs({align:"center"})`
  margin-bottom: 10px;
`,to=u(ce)`
  width: 100%;
  position: absolute;
  bottom: 0;
`,oo=u.form`
  width: 100%;
`,no=u.section`
  position: relative;
  > * {
    margin-top: 10px;
  }
  margin-bottom: 10px;
`,ro=u.div`
  position: relative;
`,io=()=>{let{t:s}=le(),o=(0,t.useMemo)(()=>({buttonPrimaryText:s("commandNext"),buttonSecondaryText:s("commandCancel"),targetInputButtonText:s("maxInputMax")}),[s]),{popDetailView:a,pushDetailView:T}=re(),{handleHideModalVisibility:m}=Ae(),l=P(S=>S.collectible),r=P(S=>S.sendFormValues),d=P(S=>S.setSendFormValues),f=P(S=>S.resetSendSlice),[C,i]=(0,t.useState)(!1),[n,b]=(0,t.useState)(Re),y=l?.chain?.id,F=l?.chainData,M=l?.owner,D=l?.name??"",V=l?.symbol??"",h=l?.balance??"",R=l?.media,L=l?.chainData,I=l?.chain?.name??"",{recipient:p,addressBookRecipient:k,amountAsset:x}=r,me=He(p,500),{data:ie,error:g}=et(me,y),Y=ie?.address,{recipientError:A,amountError:H}=n,{getExistingAccount:de,getKnownAddressLabel:Z}=ye(),U=de(p),v=y?Z(p,y):void 0,z=(0,t.useMemo)(()=>ze(p,U,v,5),[U,p,v]),N=x.isNaN()?"":x.toString(),J=(0,t.useCallback)(S=>d({...r,amountAsset:new te(S)}),[r,d]);(0,t.useEffect)(()=>{J(C?"":"1")},[C]),(0,t.useLayoutEffect)(()=>{F&&Fe(F)&&i(!0)},[F]);let Q=(0,t.useCallback)(()=>{a(),m("sendCollectibleForm"),f()},[a,m,f]),O=(0,t.useCallback)(()=>{h&&(H&&b({...n,amountError:void 0}),d({...r,amountAsset:new te(h)}))},[H,h,n,r,d]),E=(0,t.useCallback)(S=>{H&&b({...n,amountError:void 0}),d({...r,amountAsset:new te(S)})},[H,r,n,d,b]),X=(0,t.useCallback)(()=>{if(!l||!y||!F)return;let S=Ge(Y??p,y),j=g||(S?"":s("sendFormInvalidAddress",{assetName:Se.getChainName(y)})),Ee=x.gt(0)?void 0:s("sendFormErrorEmptyAmount"),De=x.gt(new te(h))?s("sendFormErrorInsufficientBalance"):void 0,be=Ee||De;if(j||be){b({recipientError:j,amountError:be});return}T(t.default.createElement(Pt,{networkID:y,media:R,assetSymbol:V,networkName:I,uiRecipient:z,txRecipient:Y??p,uiAmount:N,txAmount:x,exitSendFlow:Q}))},[l,y,F,x,N,Y,p,g,h,R,V,I,z,s,T,Q]),$=(0,t.useCallback)(S=>{A&&b({...n,recipientError:void 0}),d({...r,recipient:S,addressBookRecipient:S})},[A,n,r,d]),se=(0,t.useCallback)(()=>{m("sendCollectibleForm"),f()},[m,f]),K=(0,t.useMemo)(()=>!!p&&!x.isNaN()&&!x.isZero()&&!n.recipientError&&!n.amountError,[p,x,n.amountError,n.recipientError]);return{i18nStrings:o,networkID:y,senderAddress:M,assetSymbol:V,addressBookRecipient:k,canSubmit:K,media:R,collectibleChainData:L,name:D,isSemiFungible:C,amountAssetUiAmount:N,sendFormValues:r,sendFormErrors:n,recipientError:A,amountError:H,handleAddressSelection:$,setSendFormValues:d,setSendFormErrors:b,setMaxAmount:O,handleAmountChange:E,onButtonPrimaryClick:X,onButtonSecondaryClick:se}},so=()=>{let s=io();return t.default.createElement(ao,{...s})},sr=so,ao=t.default.memo(s=>{let{i18nStrings:o,networkID:a,senderAddress:T,addressBookRecipient:m,assetSymbol:l,canSubmit:r,media:d,collectibleChainData:f,name:C,isSemiFungible:i,amountAssetUiAmount:n,sendFormValues:b,sendFormErrors:y,recipientError:F,amountError:M,handleAddressSelection:D,setSendFormValues:V,setSendFormErrors:h,setMaxAmount:R,handleAmountChange:L,onButtonPrimaryClick:I,onButtonSecondaryClick:p}=s;return t.default.createElement(Xt,null,t.default.createElement(eo,null,t.default.createElement(xe,null,C),t.default.createElement(W,{justify:"center",margin:"0 0 10px 0"},t.default.createElement(ke,{media:d,width:i?160:220,height:i?160:220,collectibleChainData:f})),t.default.createElement(oo,{onSubmit:I},t.default.createElement(no,null,t.default.createElement(At,{addressBookRecipient:m,networkID:a,senderAddress:T,symbol:l,sendFormValues:b,sendFormErrors:y,recipientError:F,handleAddressSelection:D,setSendFormValues:V,setSendFormErrors:h}),F&&t.default.createElement(Me,null,F),i?t.default.createElement(t.default.Fragment,null,t.default.createElement(ro,null,t.default.createElement(kt,{name:"amount",value:n||"",symbol:"",alignSymbol:"right",buttonText:o.targetInputButtonText,width:47,maxLength:20,decimalLimit:0,warning:!!M,onSetTarget:R,onKeyPress:k=>r&&k.key==="Enter"&&k.preventDefault(),onUserInput:L})),M&&t.default.createElement(Me,null,M)):null))),t.default.createElement(to,{primaryText:o.buttonPrimaryText,secondaryText:o.buttonSecondaryText,onPrimaryClicked:I,onSecondaryClicked:p,primaryTheme:r?"primary":"default",primaryDisabled:!r}))});export{so as SendCollectibleFormPage,sr as default};
//# sourceMappingURL=SendCollectibleFormPage-NYQSE6RB.js.map
