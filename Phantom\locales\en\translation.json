{"commandAdd": "Add", "commandAccept": "Accept", "commandApply": "Apply", "commandApprove": "Approve", "commandAllow": "Allow", "commandBack": "Back", "commandBuy": "Buy", "commandCancel": "Cancel", "commandClaim": "<PERSON><PERSON><PERSON>", "commandClaimReward": "Claim your reward", "commandClear": "Clear", "commandClose": "Close", "commandConfirm": "Confirm", "commandConnect": "Connect", "commandContinue": "Continue", "commandConvert": "Convert", "commandCopy": "Copy", "commandCopyAddress": "Copy address", "commandCopyTokenAddress": "Copy token address", "commandCreate": "Create", "commandCreateTicket": "Create Ticket", "commandDeny": "<PERSON><PERSON>", "commandDismiss": "<PERSON><PERSON><PERSON>", "commandDontAllow": "Don't Allow", "commandDownload": "Download", "commandEdit": "Edit", "commandEditProfile": "Edit Profile", "commandEnableNow": "Enable Now", "commandFilter": "Filter", "commandFollow": "Follow", "commandHelp": "Help", "commandLearnMore": "Learn more", "commandLearnMore2": "Learn More", "commandMint": "Mint", "commandMore": "More", "commandNext": "Next", "commandNotNow": "Not Now", "commandOpen": "Open", "commandOpenSettings": "Open Settings", "commandPaste": "Paste", "commandReceive": "Receive", "commandReconnect": "Reconnect", "commandRecordVideo": "Record Video", "commandRequest": "Request", "commandRetry": "Retry", "commandReview": "Review", "commandRevoke": "Revoke", "commandSave": "Save", "commandScanQRCode": "Scan QR Code", "commandSelect": "Select", "commandSelectMedia": "Select Media", "commandSell": "<PERSON>ll", "commandSend": "Send", "commandShare": "Share", "commandShowBalance": "Show Balance", "commandSign": "Sign", "commandSignOut": "Sign Out", "commandStake": "Stake", "commandMintLST": "Mint JitoSOL", "commandSwap": "<PERSON><PERSON><PERSON>", "commandSwapAgain": "Swap Again", "commandTakePhoto": "Take Photo", "commandTryAgain": "Try Again", "commandViewTransaction": "View Transaction", "commandReportAsNotSpam": "Report as not spam", "commandReportAsSpam": "Report as spam", "commandPin": "<PERSON>n", "commandBlock": "Block", "commandUnblock": "Unblock", "commandUnstake": "Unstake", "commandUnpin": "Unpin", "commandHide": "<PERSON>de", "commandUnhide": "Unhide", "commandBurn": "Burn", "commandReport": "Report", "commandView": "View", "commandProceedAnywayUnsafe": "Proceed anyway (unsafe)", "commandUnfollow": "Unfollow", "commandUnwrap": "Unwrap", "commandConfirmUnsafe": "Confirm (unsafe)", "commandYesConfirmUnsafe": "Yes, confirm (unsafe)", "commandConfirmAnyway": "Confirm anyway", "commandReportIssue": "Report an Issue", "commandSearch": "Search", "commandShowMore": "Show more", "commandShowLess": "Show less", "pastParticipleClaimed": "Claimed", "pastParticipleCompleted": "Completed", "pastParticipleCopied": "<PERSON>pied", "pastParticipleDone": "Done", "pastParticipleDisabled": "Disabled", "pastParticipleRequested": "Requested", "nounName": "Name", "nounNetwork": "Network", "nounNetworkFee": "Network Fee", "nounSymbol": "Symbol", "nounType": "Type", "nounDescription": "Description", "nounYes": "Yes", "nounNo": "No", "amount": "Amount", "limit": "Limit", "new": "New", "gotIt": "Got it", "internal": "Internal", "reward": "<PERSON><PERSON>", "seeAll": "See all", "seeLess": "See less", "viewAll": "View all", "homeTab": "Home", "collectiblesTab": "Collectibles", "swapTab": "<PERSON><PERSON><PERSON>", "activityTab": "Activity", "exploreTab": "Explore", "accountHeaderConnectedInterpolated": "You are connected to {{origin}}", "accountHeaderConnectedToSite": "You are connected to this site", "accountHeaderCopyToClipboard": "Copy to clipboard", "accountHeaderNotConnected": "You are not connected to", "accountHeaderNotConnectedInterpolated": "You are not connected to {{origin}}", "accountHeaderNotConnectedToSite": "You are not connected to this site", "accountWithoutEnoughSolActionButtonCancel": "Cancel", "accountWithoutEnoughSolPrimaryText": "Not enough SOL", "accountWithoutEnoughSolSecondaryText": "An account involved in this transaction does not have enough SOL. The account may be yours or someone else’s. This transaction will revert if submitted.", "accountSwitcher": "Account <PERSON><PERSON><PERSON>", "addAccountHardwareWalletPrimaryText": "Connect Hardware Wallet", "addAccountHardwareWalletSecondaryText": "Use your Ledger hardware wallet", "addAccountHardwareWalletSecondaryTextMobile": "Use your {{supportedHardwareWallets}} wallet", "addAccountSeedVaultWalletPrimaryText": "Connect Seed Vault", "addAccountSeedVaultWalletSecondaryText": "Use a wallet from Seed Vault", "addAccountImportSeedPhrasePrimaryText": "Import Secret Recovery Phrase", "addAccountImportSeedPhraseSecondaryText": "Import accounts from another wallet", "addAccountImportWalletPrimaryText": "Import Private Key", "addAccountImportWalletSecondaryText": "Import a single-chain account", "addAccountImportWalletSolanaSecondaryText": "Import a Solana private key", "addAccountLimitReachedText": "You have reached the {{accountsCount}} account limit in Phantom. Please remove unused accounts before adding additional ones.", "addAccountNoSeedAvailableText": "You have no seed phrase available. Please import an existing seed to generate an account.", "addAccountNewWalletPrimaryText": "Create New Account", "addAccountNewWalletSecondaryText": "Generate a new wallet address", "addAccountNewMultiChainWalletSecondaryText": "Add a new multi-chain account", "addAccountNewSingleChainWalletSecondaryText": "Add a new account", "addAccountPrimaryText": "Add / Connect Wallet", "addAccountSecretPhraseLabel": "Secret Phrase", "addAccountSeedLabel": "Seed", "addAccountSeedIDLabel": "Seed ID", "addAccountSecretPhraseDefaultLabel": "Secret Phrase {{number}}", "addAccountPrivateKeyDefaultLabel": "Private Key {{number}}", "addAccountZeroAccountsForSeed": "0 accounts", "addAccountShowAccountForSeed": "Show 1 account", "addAccountShowAccountsForSeed": "Show {{numOfAccounts}} accounts", "addAccountHideAccountForSeed": "Hide 1 account", "addAccountHideAccountsForSeed": "Hide {{numOfAccounts}} accounts", "addAccountSelectSeedDescription": "Your new account will be generated from this Secret Phrase", "addAccountNumAccountsForSeed": "{{numOfAccounts}} accounts", "addAccountOneAccountsForSeed": "1 account", "addAccountGenerateAccountFromSeed": "Create Account", "addAccountReadOnly": "Watch Address", "addAccountReadOnlySecondaryText": "Track any public wallet address", "addAccountSolanaAddress": "Solana Address", "addAccountEVMAddress": "EVM Address", "addAccountBitcoinAddress": "Bitcoin Address", "addAccountCreateSeedTitle": "Create a new account", "addAccountCreateSeedExplainer": "Your wallet doesn't have a secret phrase yet! To create a new wallet, we'll generate you a recovery phrase. Write this down and keep it to yourself.", "addAccountSecretPhraseHeader": "Your Secret Phrase", "addAccountNoSecretPhrases": "No Secret Phrases Available", "addAccountImportAccountActionButtonImport": "Import", "addAccountImportAccountDuplicatePrivateKey": "This account already exists in your wallet", "addAccountImportAccountIncorrectFormat": "Incorrect format", "addAccountImportAccountInvalidPrivateKey": "Invalid Private Key", "addAccountImportAccountName": "Name", "addAccountImportAccountPrimaryText": "Import Private Key", "addAccountImportAccountPrivateKey": "Private key", "addAccountImportAccountPublicKey": "Address or Domain", "addAccountImportAccountPrivateKeyRequired": "Private key is required", "addAccountImportAccountNameRequired": "Name is required", "addAccountImportAccountPublicKeyRequired": "Public address is required", "addAccountImportAccountDuplicateAddress": "This address already exists in your wallet", "addAddressAddressAlreadyAdded": "Address is already added", "addAddressAddressAlreadyExists": "Address already exists", "addAddressAddressInvalid": "Address is not valid", "addAddressAddressIsRequired": "Address is required", "addAddressAddressPlaceholder": "Address", "addAddressLabelIsRequired": "Label is required", "addAddressLabelPlaceholder": "Label", "addAddressPrimaryText": "Add Address", "addAddressToast": "Address added", "createAssociatedTokenAccountCostLabelInterpolated": "This will cost {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "You already have this token account", "createAssociatedTokenAccountErrorInsufficientFunds": "Insufficient funds", "createAssociatedTokenAccountErrorInvalidMint": "Invalid mint address", "createAssociatedTokenAccountErrorInvalidName": "Invalid name", "createAssociatedTokenAccountErrorInvalidSymbol": "Invalid symbol", "createAssociatedTokenAccountErrorUnableToCreateMessage": "We were unable to create your token account. Please try again.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Failed to create account", "createAssociatedTokenAccountErrorUnableToSendMessage": "We were unable to send your transaction.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Failed to send transaction", "createAssociatedTokenAccountInputPlaceholderMint": "Mint Address", "createAssociatedTokenAccountInputPlaceholderName": "Name", "createAssociatedTokenAccountInputPlaceholderSymbol": "Symbol", "createAssociatedTokenAccountLoadingMessage": "We're creating your token account.", "createAssociatedTokenAccountLoadingTitle": "Creating token account", "createAssociatedTokenAccountPageHeader": "Create Token Account", "createAssociatedTokenAccountSuccessMessage": "Your token account was successfully created!", "createAssociatedTokenAccountSuccessTitle": "Created token account", "createAssociatedTokenAccountViewTransaction": "View transaction", "assetDetailRecentActivity": "Recent Activity", "assetDetailStakeSOL": "Stake SOL", "assetDetailUnknownToken": "Unknown To<PERSON>", "assetDetailUnwrapAll": "Unwrap All", "assetDetailUnwrappingSOL": "Unwrapping SOL", "assetDetailUnwrappingSOLFailed": "Unwrapping SOL failed", "assetDetailViewOnExplorer": "View on {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorer", "assetDetailSaveToPhotos": "Save to Photos", "assetDetailSaveToPhotosToast": "Saved to Photos", "assetDetailPinCollection": "Pin Collection", "assetDetailUnpinCollection": "Unpin Collection", "assetDetailHideCollection": "Hide Collection", "assetDetailUnhideCollection": "Unhide Collection", "assetDetailTokenNameLabel": "Token Name", "assetDetailNetworkLabel": "Network", "assetDetailAddressLabel": "Address", "assetDetailPriceLabel": "Price", "collectibleDetailSetAsAvatar": "Set as <PERSON><PERSON>", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar set", "collectibleDetailShare": "Share Collectible", "assetDetailTokenAddressCopied": "Address copied", "assetDetailStakingLabel": "Staking", "assetDetailAboutLabel": "About {{fungibleName}}", "assetDetailPriceDetail": "Price Detail", "assetDetailHighlights": "Highlights", "assetDetailAllTimeReturn": "All Time Return", "assetDetailAverageCost": "Average Cost", "assetDetailPriceHistoryUnavailable": "Price history unavailable for this token", "assetDetailPriceHistoryInsufficientData": "Price history unavailable for this time range", "assetDetailPriceDataUnavailable": "Price data unavailable", "assetDetailPriceHistoryError": "Error fetching price history", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1D", "assetDetailTimeFrame24h": "24h Price", "assetDetailTimeFrame1W": "1W", "assetDetailTimeFrame1M": "1M", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "ALL", "sendAssetAmountLabelInterpolated": "Available {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Quotes", "fiatRampNewQuote": "New quote", "assetListSelectToken": "Select Token", "assetListSearch": "Search...", "assetListUnknownToken": "Unknown To<PERSON>", "buyFlowHealthWarning": "Some of our payment providers are experiencing high traffic. Deposits may be delayed by several hours.", "assetVisibilityUnknownToken": "Unknown To<PERSON>", "buyAssetInterpolated": "Buy {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "Maximum purchase is {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Minimum purchase is {{amount}}", "buyNoAssetsAvailable": "No Ethereum or Polygon assets available", "buyThirdPartyScreenPaymentMethodSelector": "Pay with", "buyThirdPartyScreenPaymentMethod": "Choose payment method", "buyThirdPartyScreenChoseQuote": "Enter valid amount for quote", "buyThirdPartyScreenProviders": "Providers", "buyThirdPartyScreenPaymentMethodTitle": "Payment Methods", "buyThirdPartyScreenPaymentMethodEmptyState": "No payment methods available in your region", "buyThirdPartyScreenPaymentMethodFooter": "Payments are powered by network partners. Fees may vary. Some payment methods are unavailable in your region.", "buyThirdPartyScreenProvidersEmptyState": "No providers available in your region", "buyThirdPartyScreenLoadingQuote": "Loading quote...", "buyThirdPartyScreenViewQuote": "View quote", "gasEstimationErrorWarning": "There was a problem estimating the fee for this transaction. It may fail.", "gasEstimationCouldNotFetch": "Couldn't fetch gas estimation", "networkFeeCouldNotFetch": "Couldn't fetch network fee", "nativeTokenBalanceErrorWarning": "There was a problem getting your token balance for this transaction. It may fail.", "blocklistOriginCommunityDatabaseInterpolated": "This site has been flagged as part of a <1>community-maintained database</1> of known phishing websites and scams. If you believe the site has been flagged in error, <3>please file an issue</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} is blocked!", "blocklistOriginIgnoreWarning": "Ignore this warning, take me to {{domainName}} anyway.", "blocklistOriginSiteIsMalicious": "<PERSON> believes this website is malicious and unsafe to use.", "blocklistOriginThisDomain": "this domain", "blocklistProceedAnyway": "Ignore warning, proceed anyway", "maliciousTransactionWarning": "Phantom believes this transaction is malicious and unsafe to sign. We have disabled the ability to sign it in order to protect you and your funds.", "maliciousTransactionWarningIgnoreWarning": "Ignore warning, proceed anyway", "maliciousTransactionWarningTitle": "Transaction flagged!", "maliciousRequestBlockedTitle": "Request blocked", "maliciousRequestWarning": "This website has been flagged as malicious. It may be trying to steal your funds or trick you into confirming a deceptive request.", "maliciousSignatureRequestBlocked": "For your safety, Phantom has blocked this request.", "maliciousRequestBlocked": "For your safety, Phantom has blocked this request.", "maliciousRequestFrictionDescription": "Proceeding is unsafe, so Phantom blocked this request. You should close this dialogue.", "maliciousRequestAcknowledge": "I understand that I could lose all of my funds by using this website.", "maliciousRequestAreYouSure": "Are you sure?", "siwErrorPopupTitle": "Invalid Signature Request", "siwParseErrorDescription": "The app's signature request cannot be shown due to invalid formatting.", "siwVerificationErrorDescription": "There were 1 or more error(s) with the message signature request. For your security, please ensure you are using the correct app and try again.", "siwErrorPagination": "{{n}} of {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Warning: app's address does not match the provided address for signing.", "siwErrorMessage_DOMAIN_MISMATCH": "Warning: app's domain does not match provided domain for verification.", "siwErrorMessage_URI_MISMATCH": "Warning: URI hostname does not match the domain.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Warning: chain ID does not match the provided chain ID for verification.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Warning: message issuance date is too far in the past.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Warning: message issuance date is too far in the future.", "siwErrorMessage_EXPIRED": "Warning: message has expired.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Warning: message expires before issuance.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Warning: message will expire before it becomes valid.", "siwErrorShowErrorDetails": "Show error details", "siwErrorHideErrorDetails": "Hide error details", "siwErrorIgnoreWarning": "Ignore warning, proceed anyway", "siwsTitle": "Sign In Request", "siwsPermissions": "Permissions", "siwsAgreement": "Message", "siwsAdvancedDetails": "Advanced Details", "siwsAlternateStatement": "{{domain}} wants you to sign in with your Solana account:\n{{address}}", "siwsFieldLable_domain": "Domain", "siwsFieldLable_address": "Address", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Version", "siwsFieldLable_chainId": "Chain ID", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "Issued At", "siwsFieldLable_expirationTime": "Expires At", "siwsFieldLable_requestId": "Request ID", "siwsFieldLable_resources": "Resources", "siwsVerificationErrorDescription": "This sign-in request is invalid. This either means the site is unsafe, or its developer made an error when sending the request.", "siwsErrorNumIssues": "{{n}} issues", "siwsErrorMessage_CHAIN_ID_MISMATCH": "This chain ID does not match the network you are on.", "siwsErrorMessage_DOMAIN_MISMATCH": "This domain is not the one you are signing into.", "siwsErrorMessage_URI_MISMATCH": "This URI is not the one you are signing into.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Message issuance date is too far in the past.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Message issuance date is too far in the future.", "siwsErrorMessage_EXPIRED": "Message has expired.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Message expires before issuance.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Message will expire before it becomes valid.", "changeLockTimerPrimaryText": "Auto-Lock Timer", "changeLockTimerSecondaryText": "How long should we wait to lock your wallet after it has been idle?", "changeLockTimerToast": "Auto-lock timer updated", "changePasswordConfirmNewPassword": "Confirm new password", "changePasswordCurrentPassword": "Current password", "changePasswordErrorIncorrectCurrentPassword": "Incorrect current password", "changePasswordErrorGeneric": "Something went wrong, please try again later", "changePasswordNewPassword": "New password", "changePasswordPrimaryText": "Change password", "changePasswordToast": "Password updated", "collectionsSpamCollections": "Spam Collections", "collectionsHiddenCollections": "Hidden Collections", "collectiblesReportAsSpam": "Report as Spam", "collectiblesReportAsSpamAndHide": "Report as <PERSON><PERSON> and <PERSON><PERSON>", "collectiblesReportAsNotSpam": "Report as Not Spam", "collectiblesReportAsNotSpamAndUnhide": "Unhide and report not spam", "collectiblesReportNotSpam": "Not Spam", "collectionsManageCollectibles": "Manage collectible list", "collectibleDetailDescription": "Description", "collectibleDetailProperties": "Properties", "collectibleDetailOrdinalInfo": "Ordinal Info", "collectibleDetailRareSatsInfo": "Rare Sats Info", "collectibleDetailSatsInUtxo": "Sats in UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Sat Number", "collectibleDetailSatName": "Sat Name", "collectibleDetailInscriptionId": "Inscription ID", "collectibleDetailInscriptionNumber": "Inscription Number", "collectibleDetailStandard": "Standard", "collectibleDetailCreated": "Created", "collectibleDetailViewOnExplorer": "View on {{explorer}}", "collectibleDetailList": "List", "collectibleDetailSellNow": "Sell for {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Free up spare Bitcoin", "collectibleDetailUtxoSplitterCtaSubtitle": "You have {{value}} of BTC to unlock", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "To protect your funds, we prevent BTC in UTXOs with Rare Sats from being sent. Use Magic Eden's UTXO splitter to free up {{value}} of BTC from your Rare Sats.", "collectibleDetailUtxoSplitterModalCtaButton": "Use UTXO Splitter", "collectibleDetailEasilyAccept": "Accept the highest offer", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "This collectible was hidden because <PERSON> believes it is spam.", "collectibleDetailSpamOverlayReveal": "Show Collectible", "collectibleBurnTermsOfService": "I understand this cannot be undone", "collectibleBurnTitleWithCount_one": "Burn <PERSON>", "collectibleBurnTitleWithCount_other": "<PERSON>", "collectibleBurnDescriptionWithCount_one": "This action will permanently destroy and remove this token from your wallet.", "collectibleBurnDescriptionWithCount_other": "This action will permanently destroy and remove these tokens from your wallet.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Tokens", "collectibleBurnCta": "Burn", "collectibleBurnRebate": "Rebate", "collectibleBurnRebateTooltip": "A small amount of SOL will be automatically deposited into your wallet for burning this token.", "collectibleBurnNetworkFee": "Network Fee", "collectibleBurnNetworkFeeTooltip": "Amount required by the Solana network to process the transaction", "unwrapButtonSwapTo": "Swap to {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Withdraw from {{withdrawalSource}} for {{chainSymbol}}", "unwrapModalEstimatedTime": "Estimated Time", "unwrapModalNetwork": "Network", "unwrapModalNetworkFee": "Network Fee", "unwrapModalTitle": "Summary", "unsupportedChain": "Unsupported Chain", "unsupportedChainDescription": "Looks like we don't support {{action}} for the {{chainName}} network.", "networkFeesTooltipLabel": "{{chainName}} Network Fees", "networkFeesTooltipDescription": "{{chainName}} fees vary based on several factors. You can customize them to make your transaction faster (more expensive) or slower (cheaper).", "burnStatusErrorTitleWithCount_one": "<PERSON>ken failed to burn", "burnStatusErrorTitleWithCount_other": "Tokens failed to burn", "burnStatusSuccessTitleWithCount_one": "Token burned!", "burnStatusSuccessTitleWithCount_other": "Tokens burned!", "burnStatusLoadingTitleWithCount_one": "Burning token...", "burnStatusLoadingTitleWithCount_other": "Burning tokens...", "burnStatusErrorMessageWithCount_one": "This token could not be burned. Please try again later.", "burnStatusErrorMessageWithCount_other": "These tokens could not be burned. Please try again later.", "burnStatusSuccessMessageWithCount_one": "This token has been permanently destroyed and {{rebateAmount}} SOL has been deposited into your wallet.", "burnStatusSuccessMessageWithCount_other": "These tokens have been permanently destroyed and {{rebateAmount}} SOL has been deposited into your wallet.", "burnStatusLoadingMessageWithCount_one": "This token is being permanently destroyed and {{rebateAmount}} SOL will be deposited into your wallet.", "burnStatusLoadingMessageWithCount_other": "These tokens are being permanently destroyed and {{rebateAmount}} SOL will be deposited into your wallet.", "burnStatusViewTransactionText": "View transaction", "collectibleDisplayLoading": "Loading...", "collectiblesNoCollectibles": "No collectibles", "collectiblesPrimaryText": "Your Collectibles", "collectiblesReceiveCollectible": "Receive Collectible", "collectiblesUnknownCollection": "Unknown Collection", "collectiblesUnknownCollectible": "Unknown Collectible", "collectiblesUniqueHolders": "Unique Holders", "collectiblesSupply": "Supply", "collectiblesUnknownTokens": "Unknown To<PERSON>s", "collectiblesNrOfListed": "{{ nrOfListed }} Listed", "collectiblesListed": "Listed", "collectiblesMintCollectible": "Mint Collectible", "collectiblesYouMint": "You Mint", "collectiblesMintCost": "Mint cost", "collectiblesMintFail": "Mint failed", "collectiblesMintFailMessage": "There was an issue minting your collectible. Please try again.", "collectiblesMintCostFree": "Free", "collectiblesMinting": "Minting...", "collectiblesMintingMessage": "Your collectible is being minted", "collectiblesMintShareSubject": "Check this out", "collectiblesMintShareMessage": "I minted this on @phantom!", "collectiblesMintSuccess": "Mint successful", "collectiblesMintSuccessMessage": "Your collectible is now minted", "collectiblesMintSuccessQuestMessage": "You've fulfilled the requirements for a Phantom Quest. Tap Claim your reward to get your free collectible.", "collectiblesMintRequired": "Required", "collectiblesMintMaxLengthErrorMessage": "Max length exceeded", "collectiblesMintSafelyDismiss": "You can safely dismiss this window.", "collectiblesTrimmed": "We’ve reached the limit for the number of collectibles that can be displayed right now.", "collectiblesNonTransferable": "Non Transferable", "collectiblesNonTransferableYes": "Yes", "collectiblesSellOfferDetails": "Offer Details", "collectiblesSellYouSell": "You Sell", "collectiblesSellGotIt": "Got it", "collectiblesSellYouReceive": "You Receive", "collectiblesSellOffer": "Offer", "collectiblesSoldCollectible": "Sold Collectible", "collectiblesSellMarketplace": "Marketplace", "collectiblesSellCollectionFloor": "Collection Floor", "collectiblesSellDifferenceFromFloor": "Difference from floor", "collectiblesSellLastSalePrice": "Last Sale", "collectiblesSellEstimatedFees": "Estimated Fees", "collectiblesSellEstimatedProfitAndLoss": "Estimated Profit/Loss", "collectiblesSellViewOnMarketplace": "View on {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "The lowest 'Buy Now' price in the collection across multiple marketplaces.", "collectiblesSellProfitLossTooltip": "The estimated Profit/Loss is calculated based on the last sale price and the offer amount less fees.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Royalties ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Marketplace Fee ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Marketplace Fee", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}} Network", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Quote includes a {{phantomFeePercentage}} Phantom fee", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Quote includes Royalties, Network Fee, Marketplace Fee, and a {{phantomFeePercentage}} Phantom fee", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "Quote includes Royalties, Network Fee, and a Marketplace Fee", "collectiblesSellTransactionFeeTooltipTitle": "Transaction Fee", "collectiblesSellStatusLoadingTitle": "Accepting Offer...", "collectiblesSellStatusLoadingIsSellingFor": "is selling for", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} Sold!", "collectiblesSellStatusSuccessWasSold": "was successfully sold for", "collectiblesSellStatusErrorTitle": "Something Went Wrong", "collectiblesSellStatusErrorSubtitle": "There was an issue attempting to sell", "collectiblesSellStatusViewTransaction": "View Transaction", "collectiblesSellInsufficientFundsTitle": "Insufficient funds", "collectiblesSellInsufficientFundsSubtitle": "We were unable to accept an offer on this collectible because there were insufficient funds to pay the network fee.", "collectiblesSellRecentlyTransferedNFTTitle": "Recently transferred", "collectiblesSellRecentlyTransferedNFTSubtitle": "You must wait 1 hour to accept bids after a transfer.", "collectiblesApproveCollection": "Approved {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "Offer not available", "collectiblesSellNotAvailableAnymoreSubtitle": "The offer is not available anymore. Cancel this bid and try again", "collectiblesSellFlaggedTokenTitle": "Collectible is flagged", "collectiblesSellFlaggedTokenSubtitle": "The collectible is not tradeable, it could be for multiple reasons like reported as stolen or staked without lockup", "collectiblesListOnMagicEden": "List on Magic Eden", "collectiblesListPrice": "List Price", "collectiblesUseFloor": "Use Floor", "collectiblesFloorPrice": "Floor Price", "collectiblesLastSalePrice": "Last Sale Price", "collectiblesTotalReturn": "Total Return", "collectiblesOriginalPurchasePrice": "Original Purchase Price", "collectiblesMagicEdenFee": "Magic Eden Fee", "collectiblesArtistRoyalties": "Artist Royalties", "collectiblesListNowButton": "List Now", "collectiblesListAnywayButton": "List Anyway", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "View Listing", "collectiblesListingViewTransaction": "View Transaction", "collectiblesRemoveListing": "Remove Listing", "collectiblesEditListing": "Edit Listing", "collectiblesEditListPrice": "Edit List Price", "collectiblesListPriceTooltip": "List Price is the sale price for an item. Sellers typically set the List Price to be at or above the Floor Price.", "collectiblesFloorPriceTooltip": "Floor Price is the lowest active List Price for an item in this collection.", "collectiblesOriginalPurchasePriceTooltip": "You originally purchased this item for this amount.", "collectiblesPurchasedForSol": "Purchased for {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Unable to load listings", "collectiblesUnableToLoadListingsFrom": "Unable to load listings from {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "Your listings and assets are safe but we were unable to load them from {{marketplace}} at this time. Please try again later.", "collectiblesBelowFloorPrice": "Below Floor Price", "collectiblesBelowFloorPriceMessage": "Are you sure you want to list your NFT below the floor price?", "collectiblesMinimumListingPrice": "Minimum price is 0.01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden takes a fee on completed transactions.", "collectiblesArtistRoyaltiesTooltip": "The creator of this collection receives a royalty % from each completed sale.", "collectibleScreenCollectionLabel": "Collection", "collectibleScreenPhotosPermissionTitle": "Photos Permission", "collectibleScreenPhotosPermissionMessage": "We need your permission to access your photos. Please go to Settings and update your permissions.", "collectibleScreenPhotosPermissionOpenSettings": "Open Settings", "listStatusErrorTitle": "Listing Failed", "editListStatusErrorTitle": "Unable to update", "removeListStatusErrorTitle": "Remove Listing Failed", "listStatusSuccessTitle": "Listing Created!", "editListingStatusSuccessTitle": "Updated Listing!", "removeListStatusSuccessTitle": "Listing removed from Magic Eden", "listStatusLoadingTitle": "Creating Listing...", "editListingStatusLoadingTitle": "Updating Listing...", "removeListStatusLoadingTitle": "Removing Listing...", "listStatusErrorMessage": "{{name}} could not be listed on Magic Eden", "removeListStatusErrorMessage": "{{name}} could not be unlisted on Magic Eden", "listStatusSuccessMessage": "{{name}} is now listed on Magic Eden for {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "{{name}} is now updated on Magic Eden for {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} was successfully removed from Magic Eden", "listStatusLoadingMessage": "Listing {{name}} on Magic Eden for {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Updating {{name}} on Magic Eden for {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Removing {{name}} from Magic Eden. This might take a while.", "listStatusLoadingSafelyDismiss": "You can safely dismiss this window.", "listStatusViewOnMagicEden": "View on Magic Eden", "listStatusViewOnMarketplace": "View on {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON><PERSON>", "listStatusViewTransaction": "View Transaction", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Connect your hardware wallet and make sure it is unlocked. Once we’ve detected it you can choose which address you’d like to use.", "connectHardwareFailedPrimaryText": "Connection failed", "connectHardwareFailedSecondaryText": "Please connect your hardware wallet and make sure it is unlocked. Once we discover it you can choose which address to use.", "connectHardwareFinishPrimaryText": "Account Added!", "connectHardwareFinishSecondaryText": "You can now access your Ledger Nano wallet from within Phantom. Please return to the extension.", "connectHardwareNeedsPermissionPrimaryText": "Connect a new wallet", "connectHardwareNeedsPermissionSecondaryText": "Click the button below to start the connection process.", "connectHardwareSearchingPrimaryText": "Searching for wallet...", "connectHardwareSearchingSecondaryText": "Connect your hardware wallet, make sure it is unlocked, and that you have approved permissions in your browser.", "connectHardwarePermissionDeniedPrimary": "Permission denied", "connectHardwarePermissionDeniedSecondary": "Grant <PERSON> permission to connect to your Ledger device", "connectHardwarePermissionUnableToConnect": "Unable to connect", "connectHardwarePermissionUnableToConnectDescription": "We were unable to connect to your Ledger device. We may need more permissions.", "connectHardwareSelectAddressAllAddressesImported": "All addresses imported", "connectHardwareSelectAddressDerivationPath": "Derivation path", "connectHardwareSelectAddressSearching": "Searching...", "connectHardwareSelectAddressSelectWalletAddress": "Select wallet address", "connectHardwareSelectAddressWalletAddress": "Wallet address", "connectHardwareWaitingForApplicationSecondaryText": "Please connect your hardware wallet and make sure it is unlocked.", "connectHardwareWaitingForPermissionPrimaryText": "Need permission", "connectHardwareWaitingForPermissionSecondaryText": "Connect your hardware wallet, make sure it is unlocked, and that you have approved permissions in your browser.", "connectHardwareAddAccountButton": "Add Account", "connectHardwareLedger": "Connect your Ledger", "connectHardwareStartConnection": "Click the button below to start connecting your Ledger hardware wallet", "connectHardwarePairSuccessPrimary": "{{productName}} connected", "connectHardwarePairSuccessSecondary": "You have successfully connected your {{productName}}.", "connectHardwareSelectChains": "Select chains to connect", "connectHardwareSearching": "Searching...", "connectHardwareMakeSureConnected": "Connect and unlock your hardware wallet. Please approve relevant browser permissions.", "connectHardwareOpenAppDescription": "Please unlock your hardware wallet", "connectHardwareConnecting": "Connecting...", "connectHardwareConnectingDescription": "We're connecting to your Ledger device.", "connectHardwareConnectingAccounts": "Connecting your accounts...", "connectHardwareDiscoveringAccounts": "Searching for accounts...", "connectHardwareDiscoveringAccountsDescription": "We're looking for activity in your accounts.", "connectHardwareErrorLedgerLocked": "<PERSON>ger is locked", "connectHardwareErrorLedgerLockedDescription": "Make sure your Ledger device is unlocked, then try again.", "connectHardwareErrorLedgerGeneric": "Something went wrong", "connectHardwareErrorLedgerGenericDescription": "Unable to find accounts. Make sure your Ledger device is unlocked, then try again.", "connectHardwareErrorLedgerPhantomLocked": "Please re-open <PERSON> and try to connect your hardware again.", "connectHardwareFindingAccountsWithActivity": "Finding {{chainName}} accounts...", "connectHardwareFindingAccountsWithActivityDualChain": "Finding {{chainName1}} or {{chainName2}} accounts...", "connectHardwareFoundAccountsWithActivity": "We found {{numOfAccounts}} accounts with activity on your Ledger.", "connectHardwareFoundAccountsWithActivitySingular": "We found 1 account with activity on your Ledger.", "connectHardwareFoundSomeAccounts": "We found some accounts on your Ledger device.", "connectHardwareViewAccounts": "View Accounts", "connectHardwareConnectAccounts": "Accounts connected", "connectHardwareSelectAccounts": "Select Accounts", "connectHardwareChooseAccountsToConnect": "Choose wallet accounts to connect.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} Accounts added", "connectHardwareAccountsStepOfSteps": "Step {{stepNum}} of {{totalSteps}}", "connectHardwareMobile": "Connect Ledger", "connectHardwareMobileTitle": "Connect your Ledger hardware wallet", "connectHardwareMobileEnableBluetooth": "Enable Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Allow permission to use Bluetooth to connect", "connectHardwareMobileEnableBluetoothSettings": "Go to Settings to allow Phantom to use Location and Nearby Devices permissions.", "connectHardwareMobilePairWithDevice": "Pair with your Ledger device", "connectHardwareMobilePairWithDeviceDescription": "Keep your device nearby to get the best signal", "connectHardwareMobileConnectAccounts": "Connect accounts", "connectHardwareMobileConnectAccountsDescription": "We'll look for activity in any accounts you might have used", "connectHardwareMobileConnectLedgerDevice": "Connect your Ledger device", "connectHardwareMobileLookingForDevices": "Looking for devices nearby...", "connectHardwareMobileLookingForDevicesDescription": "Please connect your Ledger device and make sure it is unlocked.", "connectHardwareMobileFoundDeviceSingular": "We found 1 Ledger device", "connectHardwareMobileFoundDevices": "We found {{numDevicesFound}} Ledger devices", "connectHardwareMobileFoundDevicesDescription": "Select a Ledger device below to start pairing.", "connectHardwareMobilePairingWith": "Pairing with {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Follow instructions on your Ledger device while pairing.", "connectHardwareMobilePairingFailed": "Pairing unsuccessful", "connectHardwareMobilePairingFailedDescription": "Unable to pair with {{deviceName}}. Make sure your device is unlocked.", "connectHardwareMobilePairingSuccessful": "Pairing successful", "connectHardwareMobilePairingSuccessfulDescription": "You have successfully paired and connected your Ledger device.", "connectHardwareMobileOpenAppSingleChain": "Open the {{chainName}} app on your Ledger", "connectHardwareMobileOpenAppDualChain": "Open the {{chainName1}} or {{chainName2}} app on your Ledger", "connectHardwareMobileOpenAppDescription": "Make sure your device is unlocked.", "connectHardwareMobileStillCantFindDevice": "Still can't find your device?", "connectHardwareMobileLostConnection": "Lost connection", "connectHardwareMobileLostConnectionDescription": "We lost connection to {{deviceName}}. Make sure your device is unlocked, then retry.", "connectHardwareMobileGenericLedgerDevice": "Ledger device", "connectHardwareMobileConnectDeviceSigning": "Connect your {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Unlock your Ledger device and keep it nearby.", "connectHardwareMobileBluetoothDisabled": "Bluetooth is disabled", "connectHardwareMobileBluetoothDisabledDescription": "Please enable your Bluetooth and make sure your Ledger device is unlocked.", "connectHardwareMobileLearnMore": "Learn More", "connectHardwareMobileBlindSigningDisabled": "Blind Signing is disabled", "connectHardwareMobileBlindSigningDisabledDescription": "Make sure blind signing is enabled on your device.", "connectHardwareMobileConfirmSingleChain": "You need to confirm the transaction on your hardware wallet. Make sure it is unlocked.", "metamaskExplainerBottomSheetHeader": "This site works with Phantom", "metamaskExplainerBottomSheetSubheader": "Select MetaMask from the connect wallet dialog to proceed.", "metamaskExplainerBottomSheetDontShowAgain": "Don't show again", "ledgerStatusNotConnected": "Ledger is not connected", "ledgerStatusConnectedInterpolated": "{{productName}} is connected", "connectionClusterInterpolated": "You are currently on {{cluster}}", "connectionClusterTestnetMode": "You are currently in Testnet Mode", "featureNotSupportedOnLocalNet": "This feature is not supported when Solana Localnet is enabled.", "readOnlyAccountBannerWarning": "You are watching this account", "depositAddress": "Receive Address", "depositAddressChainInterpolated": "Your {{chain}} Address", "depositAssetDepositInterpolated": "Receive {{tokenSymbol}}", "depositAssetSecondaryText": "This address can only be used to receive compatible tokens.", "depositAssetTextInterpolated": "Use this address to receive tokens and collectibles on <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Transfer from exchange", "depositAssetShareAddress": "Share address", "depositAssetBuyOrDeposit": "Buy or Transfer", "depositAssetBuyOrDepositDesc": "Fund your wallet to get started", "depositAssetTransfer": "Transfer", "editAddressAddressAlreadyAdded": "Address is already added", "editAddressAddressAlreadyExists": "Address already exists", "editAddressAddressIsRequired": "Address is required", "editAddressPrimaryText": "Edit Address", "editAddressRemove": "Remove from Address Book", "editAddressToast": "Address updated", "removeSavedAddressToast": "Address removed", "exportSecretErrorGeneric": "Something went wrong, please try again later", "exportSecretErrorIncorrectPassword": "Incorrect password", "exportSecretPassword": "Password", "exportSecretPrivateKey": "private key", "exportSecretSecretPhrase": "secret phrase", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "secret recovery phrase", "exportSecretSelectYourAccount": "Select your account", "exportSecretShowPrivateKey": "Show Private Key", "exportSecretShowSecretRecoveryPhrase": "Show secret recovery phrase", "exportSecretShowSecret": "Show {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "Do <1>not</1> share your {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "If someone has your {{secretNameText}} they will have full control of your wallet.", "exportSecretOnlyWay": "Your {{secretNameText}} is the only way to recover your wallet", "exportSecretDoNotShow": "Do not let anyone see your {{secretNameText}}", "exportSecretWillNotShare": "I will not share my {{secretNameText}} with anyone, including <PERSON>.", "exportSecretNeverShare": "Never share your {{secretNameText}} with anyone", "exportSecretYourPrivateKey": "Your Private Key", "exportSecretYourSecretRecoveryPhrase": "Your secret recovery phrase", "exportSecretResetPin": "Reset your PIN", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "Help", "gasUpTo": "Up to {{ amount }}", "timeDescription1hour": "About 1 hour", "timeDescription30minutes": "About 30 minutes", "timeDescription10minutes": "About 10 minutes", "timeDescription2minutes": "About 2 minutes", "timeDescription30seconds": "About 30 seconds", "timeDescription15seconds": "About 15 seconds", "timeDescription10seconds": "About 10 seconds", "timeDescription5seconds": "About 5 seconds", "timeDescriptionAbbrev1hour": "1hr", "timeDescriptionAbbrev30minutes": "30min", "timeDescriptionAbbrev10minutes": "10min", "timeDescriptionAbbrev2minutes": "2min", "timeDescriptionAbbrev30seconds": "30s", "timeDescriptionAbbrev15seconds": "15s", "timeDescriptionAbbrev10seconds": "10s", "timeDescriptionAbbrev5seconds": "5s", "gasSlow": "Slow", "gasAverage": "Average", "gasFast": "Fast", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "Try Again", "homeErrorDescription": "There was an error attempting to retrieve your assets. Please refresh and try again", "homeErrorTitle": "Failed to get assets", "homeManageTokenList": "Manage token list", "interstitialDismissUnderstood": "Understood", "interstitialBaseWelcomeTitle": "Phantom now supports Base!", "interstitialBaseWelcomeItemTitle_1": "Send, receive, and buy tokens", "interstitialBaseWelcomeItemTitle_2": "Explore the Base ecosystem", "interstitialBaseWelcomeItemTitle_3": "Safe and secure", "interstitialBaseWelcomeItemDescription_1": "Transfer and buy USDC and ETH on Base using {{paymentMethod}}, cards, or Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Use Phantom with all your favorite DeFi and NFT apps.", "interstitialBaseWelcomeItemDescription_3": "Stay safe with Ledger support, spam filtering, and transaction simulation.", "privacyPolicyChangedInterpolated": "Our Privacy Policy has changed. <1>Learn More</1>", "bitcoinAddressTypesBodyTitle": "Bitcoin address types", "bitcoinAddressTypesFeature1Title": "About Bitcoin addresses", "bitcoinAddressTypesFeature1Subtitle": "Phantom supports Native Segwit and Taproot, each with its own balance. You can send BTC or Ordinals with either address type.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "The default BTC address in Phantom. Older than Taproot but compatible with all wallets and exchanges.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Best for Ordinals and BRC-20s, with the cheapest fees. Adjust addresses in Preferences -> Preferred Bitcoin Address.", "headerTitleInfo": "Info", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "This is your <1>{{addressType}}</1> address.", "invalidChecksumTitle": "We've upgraded your secret phrase!", "invalidChecksumFeature1ExportPhrase": "Export your new Secret Phrase", "invalidChecksumFeature1ExportPhraseDescription": "Please back up your new secret phrase along with the private keys of your old accounts.", "invalidChecksumFeature2FundsAreSafe": "Your funds are safe and secure", "invalidChecksumFeature2FundsAreSafeDescription": "This upgrade was automated. No one at Phantom knows your secret phrase or has access to your funds.", "invalidChecksumFeature3LearnMore": "Learn more", "invalidChecksumFeature3LearnMoreDescription": "You had a phrase that was incompatible with most wallets. Read <1>this help article</1> to learn more about this.", "invalidChecksumBackUpSecretPhrase": "Back up secret phrase", "migrationFailureTitle": "Something went wrong migrating your account", "migrationFailureFeature1": "Export your secret phrase", "migrationFailureFeature1Description": "Please back up your secret phrase before onboarding.", "migrationFailureFeature2": "Onboard to Phantom", "migrationFailureFeature2Description": "You will need to re-onboard onto Phantom to view your account.", "migrationFailureFeature3": "Learn more", "migrationFailureFeature3Description": "Read <1>this help article</1> to learn more about this.", "migrationFailureContinueToOnboarding": "Continue to onboarding", "migrationFailureUnableToFetchMnemonic": "We were unable to load your secret phrase", "migrationFailureUnableToFetchMnemonicDescription": "Please contact support and download application logs to debug", "migrationFailureContactSupport": "Contact Support", "ledgerActionConfirm": "Confirm on your Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Blind sign disabled", "ledgerActionErrorBlindSignDisabledSecondaryText": "Please make sure blind sign is enabled on your hardware device and then retry the action", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Hardware device disconnected during operation", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Please close the Phantom extension and then retry the action", "ledgerActionErrorDeviceLockedPrimaryText": "Hardware device locked", "ledgerActionErrorDeviceLockedSecondaryText": "Please unlock your hardware device and retry the action", "ledgerActionErrorHeader": "Ledger Action Error", "ledgerActionErrorUserRejectionPrimaryText": "User rejected transaction", "ledgerActionErrorUserRejectionSecondaryText": "The action was rejected on the hardware device by the user", "ledgerActionNeedPermission": "Need permission", "ledgerActionNeedToConfirm": "You need to confirm the transaction on your hardware wallet. Make sure it is unlocked, on the {{chainType}} app.", "ledgerActionNeedToConfirmMany": "You will need to confirm {{numberOfTransactions}} transactions on your hardware wallet. Make sure it is unlocked, on the {{chainType}} app.", "ledgerActionNeedToConfirmBlind": "You need to confirm the transaction on your hardware wallet. Make sure it is unlocked, on the {{chainType}} app, and blind signing is enabled.", "ledgerActionNeedToConfirmBlindMany": "You will need to confirm {{numberOfTransactions}} transactions on your hardware wallet. Make sure it is unlocked, on the {{chainType}} app, and blind signing is enabled.", "ledgerActionPleaseConnect": "Please connect your Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "Please connect your hardware wallet and make sure it is unlocked. Make sure you have approved permissions in your browser.", "maxInputAmount": "Amount", "maxInputMax": "Max", "notEnoughSolPrimaryText": "Not enough SOL", "notEnoughSolSecondaryText": "You don’t have enough SOL in your wallet for this transaction. Please deposit more and try again.", "insufficientBalancePrimaryText": "Not enough {{tokenSymbol}}", "insufficientBalanceSecondaryText": "You don’t have enough {{tokenSymbol}} in your wallet for this transaction.", "insufficientBalanceRemaining": "Remaining", "insufficientBalanceRequired": "Required", "notEnoughSplTokensTitle": "Not enough tokens", "notEnoughSplTokensDescription": "You don’t have enough tokens in your wallet for this transaction. This transaction will revert if submitted.", "transactionExpiredPrimaryText": "Transaction expired", "transactionExpiredSecondaryText": "You waited too long to confirm the transaction and it expired. This transaction will revert if submitted.", "transactionHasWarning": "Transaction warning", "tokens": "tokens", "notificationApplicationApprovalPermissionsAddressVerification": "Verify you own this address", "notificationApplicationApprovalPermissionsTransactionApproval": "Request approval for transactions", "notificationApplicationApprovalPermissionsViewWalletActivity": "View your wallet balance & activity", "notificationApplicationApprovalParagraphText": "Confirming will allow this site to view balances and activity for the selected account.", "notificationApplicationApprovalActionButtonConnect": "Connect", "notificationApplicationApprovalActionButtonSignIn": "Sign In", "notificationApplicationApprovalAllowApproval": "Allow site to connect?", "notificationApplicationApprovalAutoConfirm": "Auto-Confirm transactions", "notificationApplicationApprovalConnectDisclaimer": "Only connect to websites you trust", "notificationApplicationApprovalSignInDisclaimer": "Only sign in to websites you trust", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "This website is unsafe to use and may attempt to steal your funds.", "notificationApplicationApprovalConnectUnknownApp": "Unknown", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Unable to connect to app", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "This app is trying to connect to {{appNetworkName}}, but {{phantomNetworkName}} is selected.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "To use {{networkName}}, go to Developer Settings → Testnet Mode.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Unknown Network", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Connecting to other mobile apps is not currently supported by Ledger.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Please switch to a non-Ledger account or use the in-app browser and try again.", "notificationSignatureRequestConfirmTransaction": "Confirm transaction", "notificationSignatureRequestConfirmTransactionCapitalized": "Confirm Transaction", "notificationSignatureRequestConfirmTransactions": "Confirm transactions", "notificationSignatureRequestConfirmTransactionsCapitalized": "Confirm Transactions", "notificationSignatureRequestSignatureRequest": "Signature Request", "notificationMessageHeader": "Message", "notificationMessageCopied": "Message copied", "notificationAutoConfirm": "Auto-Confirm", "notificationAutoConfirmOff": "Off", "notificationAutoConfirmOn": "On", "notificationConfirmFooter": "Only confirm if you trust this website.", "notificationEstimatedTime": "Estimated Time", "notificationPermissionRequestText": "This is a permission request only. The transaction may not execute immediately.", "notificationBalanceChangesText": "Balance changes are estimated. Amounts and assets involved are not guaranteed.", "notificationContractAddress": "Contract Address", "notificationAdvancedDetailsText": "Advanced", "notificationUnableToSimulateWarningText": "We are currently unable to estimate balance changes. You can try again later, or confirm if you trust this site.", "notificationSignMessageParagraphText": "Signing this message will prove you have ownership of the selected account.", "notificationSignatureRequestScanFailedDescription": "Unable to scan message for security issues. Please proceed with caution.", "notificationFailedToScan": "Failed to simulate the results of this request.\nConfirming is unsafe and may lead to losses.", "notificationScanLoading": "Scanning Request", "notificationTransactionApprovalActionButtonConfirm": "Confirm", "notificationTransactionApprovalActionButtonBack": "Back", "notificationTransactionApprovalEstimatedChanges": "Estimated Changes", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Estimates are based on transaction simulations and are not a guarantee", "notificationTransactionApprovalHideAdvancedDetails": "Hide advanced transaction details", "notificationTransactionApprovalNetworkFee": "Network Fee", "notificationTransactionApprovalNetwork": "Network", "notificationTransactionApprovalEstimatedTime": "Estimated time", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "No changes impacting asset ownership found", "notificationTransactionApprovalSolanaAmountRequired": "Amount required by the Solana network to process the transaction", "notificationTransactionApprovalUnableToSimulate": "Unable to simulate. Make sure you trust this website since approving can lead to loss of funds.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Unable to fetch balance changes", "notificationTransactionApprovalViewAdvancedDetails": "View advanced transaction details", "notificationTransactionApprovalKnownMalicious": "This transaction is malicious. Signing will lead to loss of funds.", "notificationTransactionApprovalSuspectedMalicious": "We suspect this transaction is malicious. Approving may lead to loss of funds.", "notificationTransactionApprovalNetworkFeeHighWarning": "Network fees are elevated due to network congestion.", "notificationTransactionERC20ApprovalDescription": "Confirming will allow this app to access your balance at any time, up to the limit below.", "notificationTransactionERC20ApprovalContractAddress": "Contract Address", "notificationTransactionERC20Unlimited": "unlimited", "notificationTransactionERC20ApprovalTitle": "Approve {{tokenSymbol}} spending", "notificationTransactionERC20RevokeTitle": "Revoke {{tokenSymbol}} spending", "notificationTransactionERC721RevokeTitle": "Revoke {{tokenSymbol}} access", "notificationTransactionERC20ApprovalAll": "All of your {{tokenSymbol}}", "notificationIncorrectModeTitle": "Incorrect mode", "notificationIncorrectModeInTestnetTitle": "You are in Testnet mode", "notificationIncorrectModeNotInTestnetTitle": "You are not in Testnet mode", "notificationIncorrectModeInTestnetDescription": "{{origin}} is trying to use a mainnet, but you are in Testnet mode", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} is trying to use a testnet, but you are not in Testnet mode", "notificationIncorrectModeInTestnetProceed": "To proceed, turn off Testnet mode.", "notificationIncorrectModeNotInTestnetProceed": "To proceed, turn on Testnet mode.", "notificationIncorrectEIP712ChainId": "We prevented you from signing a message that was not intended for the network you're currently connected to", "notificationIncorrectEIP712ChainIdDescription": "Message requested {{message<PERSON>hainId}}, you're connected to {{connectedChainId}}", "notificationUnsupportedNetwork": "Unsupported network", "notificationUnsupportedNetworkDescription": "This website is trying to use a network that Phantom does not currently support.", "notificationUnsupportedNetworkDescriptionInterpolated": "To proceed with a different extension, turn off <1><PERSON><PERSON><PERSON> → Default App Wallet, and select Always Ask</1>. Then refresh the page and reconnect.", "notificationUnsupportedAccount": "Unsupported account", "notificationUnsupportedAccountDescription": "This website is trying to use {{targetChainType}}, which this {{chainType}} account doesn't support.", "notificationUnsupportedAccountDescription2": "Switch to an account from a compatible seed phrase or private key and try again.", "notificationInvalidTransaction": "Invalid transaction", "notificationInvalidTransactionDescription": "The transaction received from this app is malformed and should not be submitted. Please contact the developer of this app to report this issue to them.", "notificationCopyTransactionText": "Copy transaction", "notificationTransactionCopied": "Transaction copied", "onboardingImportOptionsPageTitle": "Import a wallet", "onboardingImportOptionsPageSubtitle": "Import an existing wallet with your secret phrase, private key, or hardware wallet.", "onboardingImportPrivateKeyPageTitle": "Import a Private Key", "onboardingImportPrivateKeyPageSubtitle": "Import an existing single-chain wallet", "onboardingCreatePassword": "Create a password", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "I agree to the <1>Terms of Service</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Confirm Password", "onboardingCreatePasswordDescription": "You will use this to unlock your wallet.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Invalid secret recovery phrase", "onboardingCreatePasswordPasswordPlaceholder": "Password", "onboardingCreatePasswordPasswordStrengthWeak": "Weak", "onboardingCreatePasswordPasswordStrengthMedium": "Medium", "onboardingCreatePasswordPasswordStrengthStrong": "Strong", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "I saved my Secret Recovery Phrase", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Secret Recovery Phrase", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "This phrase is the ONLY way to recover your wallet. Do NOT share it with anyone!", "onboardingImportWallet": "Import Wallet", "onboardingImportWalletImportExistingWallet": "Import an existing wallet with your 12 or 24-word secret recovery phrase.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "Secret Recovery Phrase", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Invalid Secret Recovery Phrase", "onboardingImportWalletIHaveWords": "I have a {{numWords}}-word recovery phrase", "onboardingImportWalletIncorrectOrMisspelledWord": "Word {{wordIndex}} is incorrect or misspelled", "onboardingImportWalletIncorrectOrMisspelledWords": "Words {{wordIndexes}} are incorrect or misspelled", "onboardingImportWalletScrollDown": "Scroll down", "onboardingImportWalletScrollUp": "Scroll up", "onboardingSelectAccountsImportAccounts": "Import Accounts", "onboardingSelectAccountsImportAccountsDescription": "Choose wallet accounts to import.", "onboardingSelectAccountsImportSelectedAccounts": "Import Selected Accounts", "onboardingSelectAccountsFindMoreAccounts": "Find more accounts", "onboardingSelectAccountsFindMoreNoneFound": "No accounts found", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} accounts selected", "onboardingSelectAccountSelectAllText": "Select All", "onboardingAdditionalPermissionsTitle": "Use apps with Phantom", "onboardingAdditionalPermissionsSubtitle": "For the most seamless app experience, we recommend allowing Phantom to read and change data on all sites.", "interstitialAdditionalPermissionsTitle": "Use apps with Phantom", "interstitialAdditionalPermissionsSubtitle": "To continue your experience using apps without interruption, we recommend allowing Phantom to read and change data on all sites.", "recentActivityPrimaryText": "Recent Activity", "removeAccountActionButtonRemove": "Remove", "removeAccountRemoveWallet": "Remove account", "removeAccountInterpolated": "Remove {{accountName}}", "removeAccountWarningLedger": "Even though you are removing this wallet from Phantom, you will be able to re-add it using the \"Connect Hardware Wallet\" flow.", "removeAccountWarningSeedVault": "Even though you are removing this wallet from Phantom, you will be able to re-add it using the \"Connect Seed Vault Wallet\" flow.", "removeAccountWarningPrivateKey": "Once you remove this wallet, Phantom won’t be able to recover it for you. Make sure you have your private key backed up.", "removeAccountWarningSeed": "Even though you are removing this wallet from Phantom, you will be able to re-derive it using your mnemonic in this or another wallet.", "removeAccountWarningReadOnly": "Deleting this account will not affect your wallet, as it is a watch-only wallet.", "removeSeedPrimaryText": "Removing Secret Phrase {{number}}", "removeSeedSecondaryText": "This will remove all existing accounts in Secret Phrase {{number}}. Make sure you have your existing secret phrase saved.", "resetSeedPrimaryText": "Reset app with new secret phrase", "resetSeedSecondaryText": "This will remove all existing accounts and replace them with new ones. Make sure you have your existing secret phrase and private keys backed up.", "resetAppPrimaryText": "Reset & wipe app", "resetAppSecondaryText": "This will remove all existing accounts and data. Make sure you have your secret phrase and private keys backed up.", "richTransactionsDays": "days", "richTransactionsToday": "Today", "richTransactionsYesterday": "Yesterday", "richTransactionDetailAccount": "Account", "richTransactionDetailAppInteraction": "App Interaction", "richTransactionDetailAt": "at", "richTransactionDetailBid": "Bid", "richTransactionDetailBidDetails": "<PERSON><PERSON>", "richTransactionDetailBought": "Bought", "richTransactionDetailBurned": "Burned", "richTransactionDetailCancelBid": "Cancel Bid", "richTransactionDetailCompleted": "Completed", "richTransactionDetailConfirmed": "Confirmed", "richTransactionDetailDate": "Date", "richTransactionDetailFailed": "Failed", "richTransactionDetailFrom": "From", "richTransactionDetailItem": "<PERSON><PERSON>", "richTransactionDetailListed": "Listed", "richTransactionDetailListingDetails": "Listing Details", "richTransactionDetailListingPrice": "Listing Price", "richTransactionDetailMarketplace": "Marketplace", "richTransactionDetailNetworkFee": "Network Fee", "richTransactionDetailOriginalListingPrice": "Original Listing Price", "richTransactionDetailPending": "Pending", "richTransactionDetailPrice": "Price", "richTransactionDetailProvider": "Provider", "richTransactionDetailPurchaseDetails": "Purchase Details", "richTransactionDetailRebate": "Rebate", "richTransactionDetailReceived": "Received", "richTransactionDetailSaleDetails": "Sale Details", "richTransactionDetailSent": "<PERSON><PERSON>", "richTransactionDetailSold": "Sold", "richTransactionDetailStaked": "Staked", "richTransactionDetailStatus": "Status", "richTransactionDetailSwap": "<PERSON><PERSON><PERSON>", "richTransactionDetailSwapDetails": "Swap Details", "richTransactionDetailTo": "To", "richTransactionDetailTokenSwap": "To<PERSON>", "richTransactionDetailUnknownNFT": "Unknown NFT", "richTransactionDetailUnlisted": "Unlisted", "richTransactionDetailUnstaked": "Unstaked", "richTransactionDetailValidator": "Validator", "richTransactionDetailViewOnExplorer": "View on {{explorer}}", "richTransactionDetailWithdrawStake": "Withdraw Stake", "richTransactionDetailYouPaid": "<PERSON> Paid", "richTransactionDetailYouReceived": "You Received", "richTransactionDetailUnwrapDetails": "Unwrap Details", "richTransactionDetailTokenUnwrap": "<PERSON><PERSON>w<PERSON>", "activityItemsRefreshFailed": "Failed to load newer transactions.", "activityItemsPagingFailed": "Failed to load older transactions.", "activityItemsTestnetNotAvailable": "Testnet transaction history not available at this time", "historyUnknownDappName": "Unknown", "historyStatusSucceeded": "Succeeded", "historyNetwork": "Network", "historyAttemptedAmount": "Attempted amount", "historyAmount": "Amount", "sendAddressBookButtonLabel": "Address Book", "addressBookSelectAddressBook": "Address Book", "sendAddressBookNoAddressesSaved": "No addresses saved", "sendAddressBookRecentlyUsed": "Recently Used", "addressBookSelectRecentlyUsed": "Recently Used", "sendConfirmationLabel": "Label", "sendConfirmationMessage": "Message", "sendConfirmationNetworkFee": "Network Fee", "sendConfirmationPrimaryText": "Confirm Send", "sendWarning_INSUFFICIENT_FUNDS": "Insufficient funds, this transaction will likey fail if submitted.", "sendFungibleSummaryNetwork": "Network", "sendFungibleSummaryNetworkFee": "Network fee", "sendFungibleSummaryEstimatedTime": "Estimated time", "sendFungiblePendingEstimatedTime": "Time Estimates", "sendFungibleSummaryEstimatedTimeDescription": "Ethereum transaction speeds vary based on several factors. You can speed them up by clicking on “Network Fee”.", "sendSummaryBitcoinPendingTxTitle": "Couldn't submit transfer", "sendSummaryBitcoinPendingTxDescription": "You can only have one BTC transfer pending at a time. Please wait until it's completed to submit a new transfer.", "sendFungibleSatProtectionTitle": "Sending with Sat Protection", "sendFungibleSatProtectionExplainer": "Phantom ensures that your Ordinals and BRC20s will not be used for transaction fees or Bitcoin transfers.", "sendFungibleTransferFee": "Token transfer fee", "sendFungibleTransferFeeToolTip": "The creator of this token receives a fee on each transfer. This is not a fee charged or collected by Phantom.", "sendFungibleInterestBearingPercent": "Current Interest Rate", "sendFungibleNonTransferable": "Non Transferable", "sendFungibleNonTransferableToolTip": "This token cannot be transferred to another account.", "sendFungibleNonTransferableYes": "Yes", "sendStatusErrorMessageInterpolated": "There was an error attempting to send tokens to <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "You don't have sufficient balance to complete the transaction.", "sendStatusErrorTitle": "Unable to send", "sendStatusLoadingTitle": "Sending...", "sendStatusSuccessMessageInterpolated": "Your tokens were successfully sent to <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Sent!", "sendStatusConfirmedSuccessTitle": "Sent!", "sendStatusSubmittedSuccessTitle": "Transaction Submitted", "sendStatusEstimatedTransactionTime": "Estimated Transaction Time: {{time}}", "sendStatusViewTransaction": "View transaction", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> to <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> was successfully sent to <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> was successfully sent to <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> failed to send to <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "Error Code {{code}}", "sendFormErrorInsufficientBalance": "Insufficient balance", "sendFormErrorEmptyAmount": "Amount required", "sendFormInvalidAddress": "Invalid {{assetName}} address", "sendFormInvalidUsernameOrAddress": "Invalid username or address", "sendFormErrorInvalidSolanaAddress": "Invalid <PERSON> address", "sendFormErrorInvalidTwitterHandle": "This Twitter handle is not registered", "sendFormErrorInvalidDomain": "This domain is not registered", "sendFormErrorInvalidUsername": "This username is not registered", "sendFormErrorMinRequiredInterpolated": "At least {{minAmount}} {{tokenName}} required", "sendRecipientTextareaPlaceholder": "Recipient's SOL address", "sendRecipientTextAreaPlaceholder2": "Recipient's {{symbol}} address", "sendMemoOptional": "Memo (optional)", "sendMemo": "Memo", "sendOptional": "optional", "settings": "Settings", "settingsDapps": "dApps", "settingsSelectedAccount": "Selected account", "settingsAddressBookNoLabel": "No Label", "settingsAddressBookPrimary": "Address Book", "settingsAddressBookRecentlyUsed": "Recently Used", "settingsAddressBookSecondary": "Manage commonly used addresses", "settingsAutoLockTimerPrimary": "Auto-Lock Timer", "settingsAutoLockTimerSecondary": "Change your auto-lock timer duration", "settingsChangeLanguagePrimary": "Change Language", "settingsChangeLanguageSecondary": "Change the display language", "settingsChangeNetworkPrimary": "Change Network", "settingsChangeNetworkSecondary": "Configure your network settings", "settingsChangePasswordPrimary": "Change Password", "settingsChangePasswordSecondary": "Change your lock screen password", "settingsCompleteBetaSurvey": "Complete Beta Survey", "settingsDisplayLanguage": "Display Language", "settingsErrorCannotExportLedgerPrivateKey": "Cannot export Ledger private key", "settingsErrorCannotRemoveAllWallets": "Cannot remove all accounts", "settingsExportPrivateKey": "Show Private Key", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Phantom RPC Network", "settingsTestNetworks": "Test Networks", "settingsUseCustomNetworks": "Use Custom Networks", "settingsTestnetMode": "Testnet Mode", "settingsTestnetModeDescription": "Applies to balances and app connections.", "settingsWebViewDebugging": "Web View Debugging", "settingsWebViewDebuggingDescription": "Allows you to inspect and debug the in-app browser web views.", "settingsTestNetworksInfo": "Switching to any Testnet network is meant for test purposes only. Please be aware that tokens on the Testnet Networks don’t hold any monetary value.", "settingsEmojis": "Emojis", "settingsNoAddresses": "No addresses", "settingsAddressBookEmptyHeading": "Your Address Book is empty", "settingsAddressBookEmptyText": "Click the “+” or “Add Address” buttons to add your favorite addresses", "settingsEditWallet": "Edit Account", "settingsNoTrustedApps": "No trusted apps", "settingsNoConnections": "No connections yet.", "settingsRemoveWallet": "Remove Account", "settingsResetApp": "Reset App", "settingsBlocked": "Blocked", "settingsBlockedAccounts": "Blocked Accounts", "settingsNoBlockedAccounts": "No blocked accounts.", "settingsRemoveSecretPhrase": "Remove Secret Phrase", "settingsResetAppWithSecretPhrase": "Reset App with <PERSON> Phrase", "settingsResetSecretRecoveryPhrase": "Reset Secret Recovery Phrase", "settingsShowSecretRecoveryPhrase": "Show Secret Recovery Phrase", "settingsShowSecretRecoveryPhraseSecondary": "Show Recovery Phrase", "settingsShowSecretRecoveryPhraseTertiary": "Show Secret Phrase", "settingsTrustedAppsAutoConfirmActiveUntil": "Until {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Auto-Confirm", "settingsTrustedAppsDisclaimer": "Only enable auto-confirm on trusted sites", "settingsTrustedAppsLastUsed": "Used {{formattedTimestamp}} ago", "settingsTrustedAppsPrimary": "Connected Apps", "settingsTrustedApps": "Trusted Apps", "settingsTrustedAppsRevoke": "Revoke", "settingsTrustedAppsRevokeToast": "{{trustedApp}} disconnected", "settingsTrustedAppsSecondary": "Configure your trusted applications", "settingsTrustedAppsToday": "Today", "settingsTrustedAppsYesterday": "Yesterday", "settingsTrustedAppsLastWeek": "Last Week", "settingsTrustedAppsBeforeYesterday": "Earlier", "settingsTrustedAppsDisconnectAll": "Disconnect from all", "settingsTrustedAppsDisconnectAllToast": "All apps disconnected", "settingsTrustedAppsEndAutoConfirmForAll": "End auto-confirm for all", "settingsTrustedAppsEndAutoConfirmForAllToast": "All auto-confirm sessions ended", "settingsSecurityPrimary": "Security & Privacy", "settingsSecuritySecondary": "Update your security settings", "settingsActiveNetworks": "Active Networks", "settingsActiveNetworksAll": "All", "settingsActiveNetworksSolana": "Solana Only", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana is the default network and always remains on.", "settingsDeveloperPrimary": "Developer Settings", "settingsAdvanced": "Advanced Settings", "settingsTransactions": "Transaction Settings", "settingsAutoConfirm": "Auto-Confirm settings", "settingsSecurityAnalyticsPrimary": "Share Anonymous Analytics", "settingsSecurityAnalyticsSecondary": "Enable to help us improve", "settingsSecurityAnalyticsHelper": "Phantom does not use your personal information for analytics purposes", "settingsSuspiciousCollectiblesPrimary": "Hide Suspicious Collectibles", "settingsSuspiciousCollectiblesSecondary": "Toggle to hide flagged collectibles", "settingsPreferredBitcoinAddress": "Preferred Bitcoin Address", "settingsEnabledAddressesUpdated": "Visible addresses updated!", "settingsEnabledAddresses": "Enabled Addresses", "settingsBitcoinPaymentAddressForApps": "Payments Address for Apps", "settingsBitcoinOrdinalsAddressForApps": "Ordinals Address for Apps", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "When both address types above are enabled, for certain apps like Magic Eden, your Native Segwit address will be used to fund purchases. Purchased assets will be received in your Taproot address.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "The default Bitcoin address in Phantom to ensure compatibility.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(<PERSON><PERSON><PERSON>)", "settingsPreferredBitcoinAddressTaprootExplainer": "The most modern address type, usually with cheaper transaction fees.", "settingsPreferredExplorers": "Preferred Explorer", "settingsPreferredExplorersSecondary": "Change to your preferred blockchain explorer", "settingsCustomGasControls": "Custom Gas Controls", "settingsSupportDesk": "Support Desk", "settingsSubmitATicket": "Submit a Ticket", "settingsAttachApplicationLogs": "Attach Application Logs", "settingsDownloadApplicationLogs": "Download App Logs", "settingsDownloadApplicationLogsShort": "Download Logs", "settingsDownloadApplicationLogsHelper": "Contains local data, crash reports and public wallet addresses to help resolve Phantom Support issues", "settingsDownloadApplicationLogsWarning": "No sensitive data like seed phrases or private keys are included.", "settingsWallet": "Wallet", "settingsPreferences": "Preferences", "settingsSecurity": "Security", "settingsDeveloper": "Developer", "settingsSupport": "Support", "settingsWalletShortcutsPrimary": "Show Wallet Shortcuts", "settingsAppIcon": "App Icon", "settingsAppIconDefault": "<PERSON><PERSON><PERSON>", "settingsAppIconLight": "Light", "settingsAppIconDark": "Dark", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "Account", "settingsSearchResultSelected": "Selected", "settingsSearchResultExport": "Export", "settingsSearchResultSeed": "Seed", "settingsSearchResultTrusted": "Trusted", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "State", "settingsSearchResultLogs": "Logs", "settingsSearchResultBiometric": "Biometric", "settingsSearchResultTouch": "Touch", "settingsSearchResultFace": "Face", "settingsSearchResultShortcuts": "Shortcuts", "settingsAllSitesPermissionsTitle": "Access Phantom on all sites", "settingsAllSitesPermissionsSubtitle": "Allows you to use apps seamlessly with Phantom without clicking on the extension", "settingsAllSitesPermissionsDisabled": "Your browser does not support changing this setting", "settingsSolanaCopyTransaction": "Enable Copy Transaction", "settingsSolanaCopyTransactionDetails": "Copy serialized transaction data to clipboard", "settingsAutoConfirmHeader": "Auto-Confirm", "refreshWebpageToApplyChanges": "Refresh web page to apply changes", "settingsExperimentalTitle": "Experimental Features", "settingsExprimentalSolanaActionsSubtitle": "Automatically expand Solana Action buttons when relevant links are detected on X.com", "stakeAccountCardActiveStake": "Active Stake", "stakeAccountCardBalance": "Balance", "stakeAccountCardRentReserve": "Rent Reserve", "stakeAccountCardRewards": "Last Reward", "stakeAccountCardRewardsTooltip": "This is the most recent reward you earned for staking. You are rewarded every 3 days.", "stakeAccountCardStakeAccount": "Address", "stakeAccountCardLockup": "Lockup Until", "stakeRewardsHistoryTitle": "Rewards History", "stakeRewardsActivityItemTitle": "Rewards", "stakeRewardsHistoryEmptyList": "No rewards", "stakeRewardsTime_zero": "Today", "stakeRewardsTime_one": "Yesterday", "stakeRewardsTime_other": "{{count}} days ago", "stakeRewardsItemsPagingFailed": "Failed to load older rewards.", "stakeAccountCreateAndDelegateErrorStaking": "There was a problem staking to this validator. Please try again.", "stakeAccountCreateAndDelegateSolStaked": "SOL Staked!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Your SOL will begin earning rewards <1></1> in the next couple days once the stake account becomes active.", "stakeAccountCreateAndDelegateStakingFailed": "Staking Failed", "stakeAccountCreateAndDelegateStakingSol": "Staking SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "We’re creating a staking account, then delegating your SOL to", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "We’re creating a staking account, then delegating your SOL to {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "View Transaction", "stakeAccountDeactivateStakeSolUnstaked": "SOL Unstaked!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "You will be able to withdraw your stake <1></1> in the next couple days once the stake account becomes inactive.", "stakeAccountDeactivateStakeSolUnstakedDescription": "You will be able to withdraw your stake in the next couple days once the stake account becomes inactive.", "stakeAccountDeactivateStakeUnstakingFailed": "Unstaking Failed", "stakeAccountDeactivateStakeUnstakingFailedDescription": "There was a problem unstaking from this validator. Please try again.", "stakeAccountDeactivateStakeUnstakingSol": "Unstaking SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "We are starting the process to unstake your SOL.", "stakeAccountDeactivateStakeViewTransaction": "View Transaction", "stakeAccountDelegateStakeSolStaked": "SOL Staked!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Your SOL will begin earning rewards <1></1> in the next couple days once the stake account becomes active.", "stakeAccountDelegateStakeStakingFailed": "Staking Failed", "stakeAccountDelegateStakeStakingFailedDescription": "There was a problem staking to this validator. Please try again.", "stakeAccountDelegateStakeStakingSol": "Staking SOL...", "stakeAccountDelegateStakeStakingSolDescription": "We’re delegating your SOL.", "stakeAccountDelegateStakeViewTransaction": "View Transaction", "stakeAccountListActivationActivating": "Activating", "stakeAccountListActivationActive": "Active", "stakeAccountListActivationInactive": "Inactive", "stakeAccountListActivationDeactivating": "Deactivating", "stakeAccountListErrorFetching": "We were unable to fetch stake accounts. Please try again later.", "stakeAccountListNoStakingAccounts": "No Staking Accounts", "stakeAccountListReload": "Reload", "stakeAccountListViewPrimaryText": "Your Stake", "stakeAccountListViewStakeSOL": "Stake SOL", "stakeAccountListItemStakeFee": "{{fee}} fee", "stakeAccountViewActionButtonRestake": "Restake", "stakeAccountViewActionButtonUnstake": "Unstake", "stakeAccountViewError": "Error", "stakeAccountViewPrimaryText": "Your Stake", "stakeAccountViewRestake": "Restake", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Your SOL is currently staked with a validator. You’ll need to unstake to <1></1>access these funds. <3>Learn more</3>", "stakeAccountViewStakeInactive": {"part1": "This stake account is inactive. Consider withdrawing its stake or finding a validator to delegate to.", "part2": "Learn more"}, "stakeAccountViewStakeNotFound": "This stake account could not be found.", "stakeAccountViewViewOnExplorer": "View on {{explorer}}", "stakeAccountViewWithdrawStake": "Withdraw Stake", "stakeAccountViewWithdrawUnstakedSOL": "Withdraw Unstaked SOL", "stakeAccountInsufficientFunds": "Not enough available SOL to unstake or withdraw.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL Withdrawn!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Your SOL has been withdrawn.", "part2": "This stake account will automatically be removed within the next few minutes."}, "stakeAccountWithdrawStakeViewTransaction": "View Transaction", "stakeAccountWithdrawStakeWithdrawalFailed": "Withdrawal Failed", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "There was a problem withdrawing from this stake account. Please try again.", "stakeAccountWithdrawStakeWithdrawingSol": "Withdrawing SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "We are withdrawing your SOL from this stake account.", "startEarningSolAccount": "account", "startEarningSolAccounts": "accounts", "startEarningSolErrorClosePhantom": "Tap here and try again", "startEarningSolErrorTroubleLoading": "Trouble loading stake", "startEarningSolLoading": "Loading...", "startEarningSolPrimaryText": "Start earning SOL", "startEarningSolSearching": "Searching for staking accounts", "startEarningSolStakeTokens": "Stake tokens and earn rewards", "startEarningSolYourStake": "Your stake", "unwrapFungibleTitle": "Swap to {{tokenSymbol}}", "unwrapFungibleDescription": "Withdraw from {{fromToken}} for {{toToken}}", "unwrapFungibleConfirmSwap": "Confirm Swap", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Estimated Fees", "swapFeesFees": "Fees", "swapFeesPhantomFee": "Phantom Fee", "swapFeesPhantomFeeDisclaimer": "We always find the best possible price from the top liquidity providers. A fee of {{feePercentage}} is automatically factored into this quote.", "swapFeesRate": "Price", "swapFeesRateDisclaimer": "The best rate found by Jupiter Aggregator across multiple decentralized exchanges.", "swapFeesRateDisclaimerMultichain": "The best rate found across multiple decentralized exchanges.", "swapFeesPriceImpact": "Price Impact", "swapFeesHighPriceImpact": "High Price Impact", "swapFeesPriceImpactDisclaimer": "The difference between the market price and estimated price based on your trade size.", "swapFeesSlippage": "Slippage", "swapFeesHighSlippage": "High Slippage Tolerance", "swapFeesHighSlippageDisclaimer": "Your transaction will fail if the price changes unfavorably more than {{slippage}}%.", "swapTransferFee": "Transfer Fee", "swapTransferFeeDisclaimer": "Trading ${{symbol}} incurs a {{feePercent}}% transfer fee set by the token creator, not <PERSON>.", "swapTransferFeeDisclaimerMany": "Trading the selected tokens incurs a {{feePercent}}% fee set by the token creators, not Phantom.", "swapFeesSlippageDisclaimer": "Amount that the price of your trade can deviate from the quote provided.", "swapFeesProvider": "Provider", "swapFeesProviderDisclaimer": "The decentralized exchange used to complete your trade.", "swapEstimatedTime": "Estimated Time", "swapEstimatedTimeShort": "Est. time", "swapEstimatedTimeDisclaimer": "Estimated completion time for the bridge will vary depending on several factors that affect transaction speeds.", "swapSettingsButtonCommand": "Open Swap Settings", "swapQuestionRetry": "Retry?", "swapUnverifiedTokens": "Unverified Tokens", "swapSectionTitleTokens": "{{section}} Tokens", "swapFlowYouPay": "You Pay", "swapFlowYouReceive": "You Receive", "swapFlowActionButtonText": "Review Order", "swapAssetCardTokenNetwork": "{{symbol}} on {{network}}", "swapAssetCardMaxButton": "Max", "swapAssetCardSelectTokenAndNetwork": "Select Token and Network", "swapAssetCardBuyTitle": "You Receive", "swapAssetCardSellTitle": "You Pay", "swapAssetWarningUnverified": "This token is unverified. Only interact with tokens you trust.", "swapAssetWarningPermanentDelegate": "A delegate can permanently burn or transfer these tokens.", "swapSlippageSettingsTitle": "Slippage Settings", "swapSlippageSettingsSubtitle": "Your transaction will fail if the price changes more than the slippage. Too high of a value will result in an unfavorable trade.", "swapSlippageSettingsCustom": "Custom", "swapSlippageSettingsHighSlippageWarning": "Your transaction may be frontrun and result in an unfavorable trade.", "swapSlippageSettingsCustomMinError": "Please enter a value greater than {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "Please enter a value less than {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "Please enter a valid value.", "swapSlippageSettingsAutoSubtitle": "Phantom will find the lowest slippage for a successful swap.", "swapSlippageSettingsAuto": "Auto", "swapSlippageSettingsFixed": "Fixed", "swapSlippageOptInTitle": "Auto Slippage", "swapSlippageOptInSubtitle": "Phantom will find the lowest slippage for a successful swap. You can change this anytime in Swapper → Slippage Settings.", "swapSlippageOptInEnableOption": "Enable Auto Slippage", "swapSlippageOptInRejectOption": "Continue with Fixed Slippage", "swapQuoteFeeDisclaimer": "Quote includes a {{feePercentage}} Phantom fee", "swapQuoteMissingContext": "Missing swap quote context", "swapQuoteErrorNoQuotes": "Trying to swap with no quotes", "swapQuoteSolanaNetwork": "Solana network", "swapQuoteNetwork": "Network", "swapQuoteOneTimeSerumAccount": "One-time Serum account", "swapQuoteOneTimeTokenAccount": "One-time token account", "swapQuoteBridgeFee": "Cross Chain Swap Fee", "swapQuoteDestinationNetwork": "Destination Network", "swapQuoteLiquidityProvider": "Liquidity Provider", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON><PERSON>", "swapReviewFlowPrimaryText": "Review Order", "swapReviewFlowYouPay": "You Pay", "swapReviewFlowYouReceive": "You Receive", "swapReviewInsufficientBalance": "Insufficient Funds", "ugcSwapWarningTitle": "Warning", "ugcSwapWarningBody1": "This token is trading on the token launcher {{programName}}.", "ugcSwapWarningBody2": "The value of these tokens can fluctuate wildly, leading to substantial financial gains or losses. Please trade at your own risk.", "ugcSwapWarningConfirm": "I understand", "bondingCurveProgressLabel": "Bonding Curve Progress", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "In a bonding curve model, token prices are determined by the curve's shape, increasing as more tokens are bought and decreasing as tokens are sold. When the tokens are sold out, all the liquidity will be deposited into Raydium and burned.", "ugcFungibleWarningBanner": "This token is trading on {{programName}}", "ugcCreatedRowLabel": "Created On", "ugcStatusRowLabel": "Status", "ugcStatusRowValue": "Graduated", "swapTxConfirmationReceived": "Received!", "swapTxConfirmationSwapFailed": "Swap failed", "swapTxConfirmationSwapFailedStaleQuota": "The quote is no longer valid. Please try again.", "swapTxConfirmationSwapFailedSlippageLimit": "Your slippage is too low for this swap. Please increase your slippage at the top of the Swap screen and try again.", "swapTxConfirmationSwapFailedInsufficientBalance": "We were unable to complete the request. You don't have sufficient balance to complete the transaction.", "swapTxConfirmationSwapFailedEmptyRoute": "The liquidity for this token pair has changed. We were unable to find a suitable quote. Please try again or adjust the token amounts.", "swapTxConfirmationSwapFailedAcountFrozen": "This token has been frozen by its creator. You cannot send or swap this token.", "swapTxConfirmationSwapFailedTryAgain": "The swap has failed, please try again", "swapTxConfirmationSwapFailedUnknownError": "We were unable to complete the swap. None of your funds have been affected. Please try again. ", "swapTxConfirmationSwapFailedSimulationTimeout": "We were unable to simulate the swap. None of your funds have been affected. Please try again.", "swapTxConfirmationSwapFailedSimulationUnknownError": "We were unable to complete the swap. None of your funds have been affected. Please try again. ", "swapTxConfirmationSwapFailedInsufficientGas": "Your account does not have enough funds to complete the transaction. Please, add more funds to your account and try again.", "swapTxConfirmationSwapFailedLedgerReject": "The swap was rejected by the user on the hardware device.", "swapTxConfirmationSwapFailedLedgerConnectionError": "The swap was declined due to a device connection error. Please try again.", "swapTxConfirmationSwapFailedLedgerSignError": "The swap was declined due to a device sign error. Please try again.", "swapTxConfirmationSwapFailedLedgerError": "The swap was declined due to a device error. Please try again.", "swapTxConfirmationSwappingTokens": "Swapping tokens...", "swapTxConfirmationTokens": "Tokens", "swapTxConfirmationTokensDeposited": "It's done! Tokens have been deposited into your wallet", "swapTxConfirmationTokensDepositedTitle": "It's done!", "swapTxConfirmationTokensDepositedBody": "Tokens have been deposited into your wallet", "swapTxConfirmationTokensWillBeDeposited": "will be deposited into your wallet once the transaction is complete", "swapTxConfirmationViewTransaction": "View Transaction", "swapTxBridgeSubmitting": "Submitting Transaction", "swapTxBridgeSubmittingDescription": "Swapping {{sellAmount}} on {{sellNetwork}} for {{buyAmount}} on {{buyNetwork}}", "swapTxBridgeFailed": "Transaction Failed to Submit", "swapTxBridgeFailedDescription": "We were unable to complete the request.", "swapTxBridgeSubmitted": "Transaction Submitted", "swapTxBridgeSubmittedDescription": "Estimated Transaction Time: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "You can safely dismiss this window.", "swapperSwitchTokens": "Switch tokens", "swapperMax": "Max", "swapperTooltipNetwork": "Network", "swapperTooltipPrice": "Price", "swapperTooltipAddress": "Contract", "swapperTrendingSortBy": "Sort By", "swapperTrendingTimeFrame": "Time Frame", "swapperTrendingNetwork": "Network", "swapperTrendingRank": "Rank", "swapperTrendingTokens": "Trending Tokens", "swapperTrendingVolume": "Volume", "swapperTrendingPrice": "Price", "swapperTrendingPriceChange": "Price Change", "swapperTrendingMarketCap": "Market Cap", "swapperTrendingTimeFrame1h": "1h", "swapperTrendingTimeFrame24h": "24h", "swapperTrendingTimeFrame7d": "7d", "swapperTrendingTimeFrame30d": "30d", "swapperTrendingNoTokensFound": "No tokens found.", "switchToggle": "Toggle", "termsOfServiceActionButtonAgree": "I Agree", "termsOfServiceDisclaimerFeesDisabledInterpolated": "By clicking <1>\"I Agree\"</1> you accept the <3>Terms and Conditions</3> of swapping tokens with Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "We have revised our Terms of Service. By clicking <1>\"I Agree\"</1> you agree to our new <3>Terms of Service</3>.<5></5><6></6>Our new Terms of Service include a new <8>fee structure</8> for certain products.", "termsOfServicePrimaryText": "Terms of Service", "tokenRowUnknownToken": "Unknown To<PERSON>", "transactionsAppInteraction": "App interaction", "transactionsFailedAppInteraction": "Failed app interaction", "transactionsBidOnInterpolated": "Bid on {{name}}", "transactionsBidFailed": "Bid failed", "transactionsBoughtInterpolated": "Bought {{name}}", "transactionsBoughtCollectible": "Bought Collectible", "transactionBridgeInitiated": "Bridge Initiated", "transactionBridgeInitiatedFailed": "Bridge Initiation Failed", "transactionBridgeStatusLink": "Check Status on LI.FI", "transactionsBuyFailed": "Buy failed", "transactionsBurnedSpam": "Burned spam", "transactionsBurned": "Burned", "transactionsUnwrapped": "Unwrapped", "transactionsUnwrappedFailed": "Unwrap failed", "transactionsCancelBidOnInterpolated": "Cancelled bid on {{name}}", "transactionsCancelBidOnFailed": "Failed to cancel bid", "transactionsError": "Error", "transactionsFailed": "Failed", "transactionsSwapped": "Swapped", "transactionsFailedSwap": "Swap failed", "transactionsFailedBurn": "Burn failed", "transactionsFrom": "From", "transactionsListedInterpolated": "Listed {{name}}", "transactionsListedFailed": "Failed to list", "transactionsNoActivity": "No activity", "transactionsReceived": "Received", "transactionsReceivedInterpolated": "Received {{amount}} SOL", "transactionsSending": "Sending...", "transactionsPendingCreateListingInterpolated": "Creating {{name}}", "transactionsPendingEditListingInterpolated": "Editing {{name}}", "transactionsPendingSolanaPayTransaction": "Confirming Solana Pay Transaction", "transactionsPendingRemoveListingInterpolated": "Unlisting {{name}}", "transactionsPendingBurningInterpolated": "Burning {{name}}", "transactionsPendingSending": "Sending", "transactionsPendingSwapping": "Swapping", "transactionsPendingBridging": "Bridging", "transactionsPendingApproving": "Approving", "transactionsPendingCreatingAndDelegatingStake": "Creating and delegating stake", "transactionsPendingDeactivatingStake": "Deactivating stake", "transactionsPendingDelegatingStake": "Delegating stake", "transactionsPendingWithdrawingStake": "Withdrawing stake", "transactionsPendingAppInteraction": "Pending app interaction", "transactionsPendingBitcoinTransaction": "Pending BTC transaction", "transactionsSent": "<PERSON><PERSON>", "transactionsSendFailed": "Send failed", "transactionsSwapOn": "Swap on {{dappName}}", "transactionsSentInterpolated": "Sent {{amount}} SOL", "transactionsSoldInterpolated": "Sold {{name}}", "transactionsSoldCollectible": "Sold Collectible", "transactionsSoldFailed": "Sale failed", "transactionsStaked": "Staked", "transactionsStakedFailed": "Stake failed", "transactionsSuccess": "Success", "transactionsTo": "To", "transactionsTokenSwap": "To<PERSON>", "transactionsUnknownAmount": "Unknown", "transactionsUnlistedInterpolated": "Unlisted {{name}}", "transactionsUnstaked": "Unstaked", "transactionsUnlistedFailed": "Failed to unlist", "transactionsDeactivateStake": "Deactivated stake", "transactionsDeactivateStakeFailed": "Failed to deactivate stake", "transactionsWaitingForConfirmation": "Waiting for confirmation", "transactionsWithdrawStake": "Withdraw Stake", "transactionsWithdrawStakeFailed": "Unstake failed", "transactionCancelled": "Cancelled", "transactionCancelledFailed": "Failed to cancel", "transactionApproveToken": "Approved {{tokenSymbol}}", "transactionApproveTokenFailed": "Failed to approve {{tokenSymbol}}", "transactionApprovalFailed": "Approval failed", "transactionRevokeApproveToken": "Revoked {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "Failed to revoke {{tokenSymbol}}", "transactionRevokeFailed": "Revoke failed", "transactionApproveDetailsTitle": "Approval Details", "transactionCancelOrder": "Cancel order", "transactionCancelOrderFailed": "Cancel order failed", "transactionApproveAppLabel": "App", "transactionApproveAmountLabel": "Amount", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "Collection", "transactionApproveAllItems": "Approve all items", "transactionSpendUpTo": "Spend up to", "transactionCancel": "Cancel Transaction", "transactionPrioritizeCancel": "Prioritize Cancellation", "transactionSpeedUp": "Speed Up Transaction", "transactionCancelHelperText": "The original transaction may complete before it is cancelled.", "transactionSpeedUplHelperText": "This will maximize the speed of your transaction based on network conditions.", "transactionCancelHelperMobile": "It will cost <1>up to {{amount}}</1> to attempt to cancel this transaction. The original transaction may complete before it is cancelled.", "transactionCancelHelperMobileWithEstimate": "It will cost <1>up to {{amount}}</1> to attempt to cancel this transaction. It should complete in about {{timeEstimate}}. The original transaction may complete before it is cancelled.", "transactionSpeedUpHelperMobile": "It will cost <1>up to {{amount}}</1> to maximize the speed of this transaction.", "transactionSpeedUpHelperMobileWithEstimate": "It will cost <1>up to {{amount}}</1> to maximize the speed of this transaction. It should complete in about {{timeEstimate}}.", "transactionEstimatedTime": "Estimated time", "transactionCancelingSend": "Cancelling send", "transactionPrioritizingCancel": "Prioritizing cancellation", "transactionCanceling": "Cancelling", "transactionReplaceError": "An error ocurred. No fees have been charged to your account.  You may try again.", "transactionNotEnoughNative": "Not enough {{nativeTokenSymbol}}", "transactionGasLimitError": "Failed to estimate gas limit", "transactionGasEstimationError": "Failed to estimate gas", "pendingTransactionCancel": "Cancel", "pendingTransactionSpeedUp": "Speed up", "pendingTransactionStatus": "Status", "pendingTransactionPending": "Pending", "pendingTransactionPendingInteraction": "Pending Interaction", "pendingTransactionCancelling": "Cancelling", "pendingTransactionDate": "Date", "pendingTransactionNetworkFee": "Network fee", "pendingTransactionEstimatedTime": "Estimated time", "pendingTransactionEstimatedTimeHM": "{{hours}}h {{minutes}}m", "pendingTransactionEstimatedTimeMS": "{{minutes}}m {{seconds}}s", "pendingTransactionEstimatedTimeS": "{{seconds}}s", "pendingTransactionsSendingTitle": "Sending {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Unknown", "pendingTransactionUnknownApp": "Unknown App", "permanentDelegateTitle": "Delegated", "permanentDelegateValue": "Permanent", "permanentDelegateTooltipTitle": "Permanent Delegation", "permanentDelegateTooltipValue": "Permanent Delegate allows for another account to manage tokens on your behalf, this includes burning or transferring.", "unlockActionButtonUnlock": "Unlock", "unlockEnterPassword": "Enter your password", "unlockErrorIncorrectPassword": "Incorrect password", "unlockErrorSomethingWentWrong": "Something went wrong, please try again later", "unlockForgotPassword": "Forgot password", "unlockPassword": "Password", "forgotPasswordText": "You can reset your password by entering your wallet's 12-24 word recovery phrase. Phantom cannot recover your password for you.", "appInfo": "App Info", "lastUsed": "Last Used", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Not available with hardware accounts.", "trustedAppAutoConfirmDisclaimer1": "While active, <PERSON> will confirm all requests from this app without notifying you or asking for confirmation.", "trustedAppAutoConfirmDisclaimer2": "Enabling may put your funds at risk of fraud. Only use this feature with apps you trust.", "validationUtilsPasswordIsRequired": "Password is required", "validationUtilsPasswordLength": "Password must be 8 characters long", "validationUtilsPasswordsDontMatch": "Passwords don't match", "validationUtilsPasswordCantBeSame": "You can't use your old password", "validatorCardEstimatedApy": "Estimated APY", "validatorCardCommission": "Commission", "validatorCardTotalStake": "Total Stake", "validatorCardNumberOfDelegators": "# of Delegators", "validatorListChooseAValidator": "Choose a Validator", "validatorListErrorFetching": "We were unable to fetch validators. Please try again later.", "validatorListNoResults": "No Results", "validatorListReload": "Reload", "validatorInfoTooltip": "Validator", "validatorInfoTitle": "Validators", "validatorInfoDescription": "By staking your SOL on a validator you contribute to the performance and safety of the Solana network, all while earning SOL in return.", "validatorApyInfoTooltip": "Est. APY", "validatorApyInfoTitle": "Estimated APY", "validatorApyInfoDescription": "This is the rate of return you earn for staking your SOL on the validator.", "validatorViewActionButtonStake": "Stake", "validatorViewErrorFetching": "Could not fetch validators.", "validatorViewInsufficientBalance": "Insufficient balance", "validatorViewMax": "Max", "validatorViewPrimaryText": "Start Staking", "validatorViewDescriptionInterpolated": "Choose how much SOL you'd like to <1></1> stake with this validator. <3>Learn more</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL required to stake", "validatorViewValidator": "Validator", "walletMenuItemsAddConnectWallet": "Add / Connect Wallet", "walletMenuItemsBridgeAssets": "Bridge Assets", "walletMenuItemsHelpAndSupport": "Help & Support", "walletMenuItemsLockWallet": "Lock Wallet", "walletMenuItemsResetSecretPhrase": "Reset Secret Phrase", "walletMenuItemsShowMoreAccounts": "Show {{count}} more...", "walletMenuItemsHideAccounts": "Hide accounts", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "Solana-only mode", "disableMultiChainDetail1Header": "Go all-in on <PERSON><PERSON>", "disableMultiChainDetail1SecondaryText": "Manage your Solana Accounts, tokens, and collectibles without seeing other chains.", "disableMultiChainDetail2Header": "Return to Multichain at any time", "disableMultiChainDetail2SecondaryText": "Your existing Ethereum and Polygon balances will be preserved when you re-enable Multichain.", "disableMultiChainButton": "Enable <PERSON>-Only", "disabledMultiChainHeader": "Solana-only Enabled", "disabledMultiChainText": "You can re-enable multichain anytime.", "enableMultiChainHeader": "Enable Multichain", "enabledMultiChainHeader": "Multichain Enabled", "enabledMultiChainText": "Ethereum and Polygon are now supported in your wallet.", "incompatibleAccountHeader": "Incompatible Account", "incompatibleAccountInterpolated": "Please remove these Ethereum-only accounts before enabling Solana-only mode: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "What's New!", "welcomeToMultiChainPrimaryText": "One Wallet for Everything", "welcomeToMultiChainDetail1Header": "Ethereum and Polygon support", "welcomeToMultiChainDetail1SecondaryText": "All your tokens and NFTs from Solana, Ethereum, and Polygon in one place.", "welcomeToMultiChainDetail2Header": "Use all the apps you love", "welcomeToMultiChainDetail2SecondaryText": "Connect to apps on multiple chains without switching networks.", "welcomeToMultiChainDetail3Header": "Import your MetaMask wallet", "welcomeToMultiChainDetail3SecondaryText": "Easily import all your seed phrases across Ethereum and Polygon.", "welcomeToMultiChainIntro": "Welcome to Phantom Multichain", "welcomeToMultiChainIntroDesc": "All your tokens and NFTs from Solana, Ethereum, and Polygon in one place. Your one wallet for everything.", "welcomeToMultiChainAccounts": "Multichain Accounts redesigned", "welcomeToMultiChainAccountsDesc": "Redesigned for multichain, each account now has corresponding ETH and Polygon addresses.", "welcomeToMultiChainApps": "Works Everywhere", "welcomeToMultiChainAppsDesc": "Phantom is compatible with every app on Ethereum, Polygon, and Solana. Click “Connect to MetaMask” and you’re ready to go.", "welcomeToMultiChainImport": "Import from MetaMask, instantly", "welcomeToMultiChainImportDesc": "Import your Secret Phrases or Private Keys from wallets like MetaMask or Coinbase Wallet. All in one place.", "welcomeToMultiChainImportInterpolated": "<0>Import your Secret Phrases</0> or Private Keys from wallets like MetaMask or Coinbase Wallet. All in one place.", "welcomeToMultiChainTakeTour": "Take the tour", "welcomeToMultiChainSwapperTitle": "Swap on Ethereum,\nPolygon, & Solana", "welcomeToMultiChainSwapperDetail1Header": "Ethereum and Polygon support", "welcomeToMultiChainSwapperDetail1SecondaryText": "Now you can easily swap ERC-20 tokens from inside your wallet.", "welcomeToMultiChainSwapperDetail2Header": "Best Prices and Super Low Fees", "welcomeToMultiChainSwapperDetail2SecondaryText": "100+ liquidity sources and smart order routing for maximum returns.", "networkErrorTitle": "Network Error", "networkError": "Unfortunately we can't access the network. Please try again later.", "authenticationUnlockPhantom": "Unlock Phantom", "errorAndOfflineSomethingWentWrong": "Something went wrong", "errorAndOfflineSomethingWentWrongTryAgain": "Please try again.", "errorAndOfflineUnableToFetchAssets": "We were unable to fetch assets. Please try again later.", "errorAndOfflineUnableToFetchCollectibles": "We were unable to fetch collectibles. Please try again later.", "errorAndOfflineUnableToFetchSwap": "We were unable to fetch swap info. Please try again later.", "errorAndOfflineUnableToFetchTransactionHistory": "We're unable to get your transaction history right now. Check your network connection, or try again later.", "errorAndOfflineUnableToFetchRewardsHistory": "We were unable to fetch rewards history. Please try again later.", "errorAndOfflineUnableToFetchBlockedUsers": "We were unable to fetch blocked users. Please try again later.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Something went wrong while reviewing your order, please try again.", "sendSelectToken": "Select Token", "swapBalance": "Balance:", "swapTitle": "S<PERSON>p <PERSON>", "swapSelectToken": "Select Token", "swapYouPay": "You Pay", "swapYouReceive": "You Receive", "aboutPrivacyPolicy": "Privacy Policy", "aboutVersion": "Version {{version}}", "aboutVisitWebsite": "Visit Website", "bottomSheetConnectTitle": "Connect", "A11YbottomSheetConnectTitle": "Bottom Sheet Connect", "A11YbottomSheetCommandClose": "Bottom Sheet Decline", "A11YbottomSheetCommandBack": "Bottom Sheet Back", "bottomSheetSignTypedDataTitle": "Sign message", "bottomSheetSignMessageTitle": "Sign message", "bottomSheetSignInTitle": "Sign in", "bottomSheetSignInAndConnectTitle": "Sign in", "bottomSheetConfirmTransactionTitle": "Confirm transaction", "bottomSheetConfirmTransactionsTitle": "Confirm transactions", "bottomSheetSolanaPayTitle": "Solana Pay Request", "bottomSheetAdvancedTitle": "Advanced", "bottomSheetReadOnlyAccountTitle": "View-Only Mode", "bottomSheetTransactionSettingsTitle": "Network Fee", "bottomSheetConnectDescription": "Connecting will allow this site to view balances and activity for the selected account.", "bottomSheetSignInDescription": "Signing this message will prove you have ownership of the selected account. Only sign messages from applications you trust.", "bottomSheetSignInAndConnectDescription": "Approving will allow this site to view balances and activity for the selected account.", "bottomSheetConfirmTransactionDescription": "Balance changes are estimated. Amounts and assets involved are not guaranteed.", "bottomSheetConfirmTransactionsDescription": "Balance changes are estimated. Amounts and assets involved are not guaranteed.", "bottomSheetSignTypedDataDescription": "This is a permission request only. The transaction may not execute immediately.", "bottomSheetSignTypedDataSecondDescription": "Balance changes are estimated. Amounts and assets involved are not guaranteed.", "bottomSheetSignMessageDescription": "Signing this message will prove you have ownership of the selected account. Only sign messages from applications you trust.", "bottomSheetReadOnlyAccountDescription": "Unable to perform this action in view-only mode.", "bottomSheetMessageRow": "Message", "bottomSheetStatementRow": "Statement", "bottomSheetAutoConfirmRow": "Auto-Confirm", "bottomSheetAutoConfirmOff": "Off", "bottomSheetAutoConfirmOn": "On", "bottomSheetAccountRow": "Account", "bottomSheetAdvancedRow": "Advanced", "bottomSheetContractRow": "Contract Address", "bottomSheetSpenderRow": "Spender Address", "bottomSheetNetworkRow": "Network", "bottomSheetNetworkFeeRow": "Network Fee", "bottomSheetEstimatedTimeRow": "Estimated Time", "bottomSheetAccountRowDefaultAccountName": "Account", "bottomSheetConnectRequestDisclaimer": "Only connect to websites you trust", "bottomSheetSignInRequestDisclaimer": "Only sign in to websites you trust", "bottomSheetSignatureRequestDisclaimer": "Only confirm if you trust this website.", "bottomSheetFeaturedTransactionDisclaimer": "You will see a preview of the transaction before you confirm in the next step.", "bottomSheetIgnoreWarning": "Ignore warning, proceed anyway", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "No balance changes found. Please proceed with caution and only confirm if you trust this site.", "bottomSheetReadOnlyWarning": "You are only watching this address. You will need to import in order to sign transactions and messages.", "bottomSheetWebsiteIsUnsafeWarning": "This website is unsafe to use and may attempt to steal your funds.", "bottomSheetViewOnExplorer": "View on Explorer", "bottomSheetTransactionSubmitted": "Transaction Submitted", "bottomSheetTransactionPending": "Transaction Pending", "bottomSheetTransactionFailed": "Transaction Failed", "bottomSheetTransactionSubmittedDescription": "Your transaction has been submitted. You can view it on the explorer.", "bottomSheetTransactionFailedDescription": "Your transaction failed. Please try again.", "bottomSheetTransactionPendingDescription": "The transaction is being processed...", "transactionsFromInterpolated": "From: {{from}}", "transactionsFromParagraphInterpolated": "From {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "Today", "transactionsToInterpolated": "To: {{to}}", "transactionsToParagraphInterpolated": "To {{to}}", "transactionsYesterday": "Yesterday", "addEditAddressAdd": "Add address", "addEditAddressDelete": "Delete address", "addEditAddressDeleteTitle": "Are you sure you want to delete this address?", "addEditAddressSave": "Save address", "dAppBrowserComingSoon": "dApp Browser coming soon!", "dAppBrowserSearchPlaceholder": "Sites, tokens, URL", "dAppBrowserOpenInNewTab": "Open in new tab", "dAppBrowserSuggested": "Suggested", "dAppBrowserFavorites": "Favorites", "dAppBrowserBookmarks": "Bookmarks", "dAppBrowserBookmarkAdd": "Add Bookmark", "dAppBrowserBookmarkRemove": "Remove Bookmark", "dAppBrowserUsers": "Users", "dAppBrowserRecents": "Recents", "dAppBrowserFavoritesDescription": "Your favorites will be shown here", "dAppBrowserBookmarksDescription": "Your bookmarks will be shown here", "dAppBrowserRecentsDescription": "Recently connected dapps will appear here", "dAppBrowserEmptyScreenDescription": "Type a URL or search the web", "dAppBrowserBlocklistScreenTitle": "{{origin}} is blocked! ", "dAppBrowserBlocklistScreenDescription": {"part1": "<PERSON> believes this website is malicious and unsafe to use.", "part2": "This site has been flagged as part of a community-maintained database of known phishing websites and scams. If you believe the site has been flagged in error, please file an issue."}, "dAppBrowserLoadFailedScreenTitle": "Failed to load", "dAppBrowserLoadFailedScreenDescription": "There was an error loading this page", "dAppBrowserBlocklistScreenIgnoreButton": "Ignore warning, show anyway", "dAppBrowserActionBookmark": "Bookmark", "dAppBrowserActionRemoveBookmark": "Remove bookmark", "dAppBrowserActionRefresh": "Refresh", "dAppBrowserActionShare": "Share", "dAppBrowserActionCloseTab": "Close tab", "dAppBrowserActionEndAutoConfirm": "End Auto-Confirm", "dAppBrowserActionDisconnectApp": "Disconnect app", "dAppBrowserActionCloseAllTabs": "Close all tabs", "dAppBrowserNavigationAddressPlaceholder": "Type a URL to search", "dAppBrowserTabOverviewMore": "More", "dAppBrowserTabOverviewAddTab": "Add <PERSON>", "dAppBrowserTabOverviewClose": "Close", "dAppBrowserCloseTab": "Close Tab", "dAppBrowserClose": "Close", "dAppBrowserTabOverviewAddBookmark": "Add Bookmark", "dAppBrowserTabOverviewRemoveBookmark": "Remove Bookmark", "depositAssetListSuggestions": "Suggestions", "depositUndefinedToken": "Sorry, can't deposit this token", "onboardingImportRecoveryPhraseDetails": "Details", "onboardingCreateRecoveryPhraseVerifyTitle": "Written the Secret Recovery Phrase down?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Without the secret recovery phrase you will not be able to access your key or any assets associated with it.", "onboardingCreateRecoveryPhraseVerifyYes": "Yes", "onboardingCreateRecoveryPhraseErrorTitle": "Error", "onboardingCreateRecoveryPhraseErrorSubtitle": "We were unsuccessful in generating an account, please try again.", "onboardingDoneDescription": "You can now fully enjoy your wallet.", "onboardingDoneGetStarted": "Get Started", "zeroBalanceHeading": "Let's get started!", "zeroBalanceBuyCryptoTitle": "Buy Crypto", "zeroBalanceBuyCryptoDescription": "Buy your first crypto with a debit or credit card.", "zeroBalanceDepositTitle": "Transfer Crypto", "zeroBalanceDepositDescription": "Deposit crypto from another wallet or exchange.", "onboardingImportAccountsEmptyResult": "No accounts found", "onboardingImportAccountsAccountName": "Account {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Social Account", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "We found {{numberOfWallets}} account with activity", "onboardingImportAccountsFoundAccounts_other": "We found {{numberOfWallets}} accounts with activity", "onboardingImportAccountsFoundAccountsNoActivity_one": "We found {{numberOfWallets}} account", "onboardingImportAccountsFoundAccountsNoActivity_other": "We found {{numberOfWallets}} accounts", "onboardingImportRecoveryPhraseLessThanTwelve": "Phrase needs to be at least 12 words.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Phrase needs to be exactly 12 or 24 words.", "onboardingImportRecoveryPhraseWrongWord": "Incorrect words: {{ words }}.", "onboardingProtectTitle": "Protect your wallet", "onboardingProtectDescription": "Adding biometric security will ensure that you are the only one that can access your wallet.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Fingerprint", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Use {{ authType }} Authentication", "onboardingProtectError": "Something went wrong while authenticating, please try again", "onboardingProtectBiometryIosError": "Biometric authentication is configured in Phantom but disabled in System Settings. Please open Settings > Phantom > Face ID or Touch ID to re-enable.", "onboardingProtectRemoveAuth": "Disable authentication", "onboardingProtectRemoveAuthDescription": "Are you sure you want to disable authentication?", "onboardingWelcomeTitle": "Welcome to Phantom", "onboardingWelcomeDescription": "To get started, create a new wallet or import an existing one.", "onboardingWelcomeCreateWallet": "Create a new wallet", "onboardingWelcomeAlreadyHaveWallet": "I already have a wallet", "onboardingWelcomeConnectSeedVault": "Connect Seed Vault", "onboardingSlide1Title": "Controlled by you", "onboardingSlide1Description": "Your wallet is secured with biometrics access, scam detection and 24/7 support.", "onboardingSlide2Title": "The best home for\nyour NFTs", "onboardingSlide2Description": "Manage listings, burn spam, and stay updated with helpful push notifications.", "onboardingSlide3Title": "Do more with your tokens", "onboardingSlide3Description": "Store, swap, stake, send, and receive — without ever leaving your wallet. ", "onboardingSlide4Title": "Discover the best of Web3", "onboardingSlide4Description": "Find and connect to leading apps and collections with the in-app browser.", "onboardingMultichainSlide5Title": "One wallet for everything", "onboardingMultichainSlide5Description": "Experience all of Solana, Ethereum, and Polygon in a single user-friendly interface.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Experience all of Solana, Ethereum, Polygon and Bitcoin in a single user-friendly interface.", "requireAuth": "Require authentication", "requireAuthImmediately": "Immediately", "availableToSend": "Available To Send", "sendEnterAmount": "Enter Amount", "sendEditMemo": "Edit Memo", "sendShowLogs": "Show Error Logs", "sendHideLogs": "<PERSON>de Error <PERSON>", "sendGoBack": "Go Back", "sendTransactionSuccess": "Your tokens were successfully sent to", "sendInputPlaceholder": "@username or address", "sendInputPlaceholderV2": "username or address", "sendPeopleTitle": "People", "sendDomainTitle": "Domains", "sendFollowing": "Following", "sendRecentlyUsedAddressLabel": "Used {{formattedTimestamp}} ago", "sendRecipientAddress": "Recipient's address", "sendTokenInterpolated": "Send {{tokenSymbol}}", "sendPasteFromClipboard": "Paste from clipboard", "sendScanQR": "Scan QR Code", "sendTo": "To:", "sendRecipientZeroBalanceWarning": "This wallet address has no balance and doesn't appear in your recent transaction history. Please ensure the address is correct.", "sendUnknownAddressWarning": "This is not an address you've interacted with recently. Please proceed with caution.", "sendSameAddressWarning": "This is your current address. Sending will incur transfer fees with no other balance changes.", "sendMintAccountWarning": "This is a mint account address. You are unable to send funds to this address as it will result in permanent loss.", "sendCameraAccess": "Camera Access", "sendCameraAccessSubtitle": "To scan a QR code, camera access needs to be enabled. Would you like to open Settings now?", "sendSettings": "Settings", "sendOK": "OK", "invalidQRCode": "This QR code is not valid.", "sendInvalidQRCode": "This QR code is not a valid address", "sendInvalidQRCodeSubtitle": "Try again or with another QR code.", "sendInvalidQRCodeSplToken": "Invalid token in QR code", "sendInvalidQRCodeSplTokenSubtitle": "This QR code contains a token that you don't own or we can't identify it.", "sendScanAddressToSend": "Scan {{tokenSymbol}} address to send funds", "sendScanAddressToSendNoSymbol": "Scan address to send funds", "sendScanAddressToSendCollectible": "Scan SOL address to send collectible", "sendScanAddressToSendCollectibleMultichain": "Scan address to send collectible", "sendSummary": "Summary", "sendUndefinedToken": "Sorry, can't send this token", "sendNoTokens": "No tokens available", "noBuyOptionsAvailableInCountry": "No Buy options available in your country", "swapAvailableTokenDisclaimer": "A limited number of tokens are available for bridging between Networks", "swapCrossSwapNetworkTooltipTitle": "Swapping Across Networks", "swapCrossSwapNetworkTooltipDescription": "When swapping across Networks it is recommended to use the available tokens for the lowest price and fastest transactions.", "settingsAbout": "About Phantom", "settingsShareAppWithFriends": "Invite your friends", "settingsConfirm": "Yes", "settingsMakeSureNoOneIsWatching": "Make sure no one is watching your screen", "settingsManageAccounts": "Manage Accounts", "settingsPrompt": "Are you sure you want to continue?", "settingsSelectAvatar": "Select Avatar", "settingsSelectSecretPhrase": "Select Secret Phrase", "settingsShowPrivateKey": "Tap to reveal your private key", "settingsShowRecoveryPhrase": "Tap to reveal your secret phrase", "settingsSubmitBetaFeedback": "Submit Beta Feedback", "settingsUpdateAccountNameToast": "Account name updated", "settingsUpdateAvatarToast": "Avatar updated", "settingsUpdateAvatarToastFailure": "Failed to update Avatar!", "settingsWalletAddress": "Account Address", "settingsWalletAddresses": "Account Addresses", "settingsWalletNamePrimary": "Account Name", "settingsPlaceholderName": "Name", "settingsWalletNameSecondary": "Change your wallet's name", "settingsYourAccounts": "Your Accounts", "settingsYourAccountsMultiChain": "Multi-chain", "settingsReportUser": "Report User", "settingsNotifications": "Notifications", "settingsNotificationPreferences": "Notification Preferences", "pushNotificationsPreferencesAllowNotifications": "Allow Notifications", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON>", "pushNotificationsPreferencesSentTokensDescription": "Outbound transfers of tokens and NFTs", "pushNotificationsPreferencesReceivedTokens": "Received <PERSON>s", "pushNotificationsPreferencesReceivedTokensDescription": "Inbound transfers of tokens and NFTs", "pushNotificationsPreferencesDexSwap": "Swaps", "pushNotificationsPreferencesDexSwapDescription": "Swaps on recognized applications", "pushNotificationsPreferencesOtherBalanceChanges": "Other Balance Changes", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Other multi-token transactions that impact your balance", "pushNotificationsPreferencesPhantomMarketing": "Updates From Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Feature announcements and general updates", "pushNotificationsPreferencesDescription": "These settings control push notifications for this active wallet. Each wallet has their own notification settings. To turn off all Phantom push notifications, go to your <1>device settings</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Unable to sync notification preferences.", "connectSeedVaultConnectSeed": "Connect a Seed", "connectSeedVaultConnectSeedDescription": "Connect Phantom to the Seed Vault on your phone", "connectSeedVaultSelectAnAccount": "Select an account", "connectSeedVaultSelectASeed": "Select a Seed", "connectSeedVaultSelectASeedDescription": "Choose which seed you'd like to connect to Phantom", "connectSeedVaultSelectAnAccountDescription": "Choose which account you'd like to set up with Phantom", "connectSeedVaultNoAccountsFound": "No accounts found.", "connectSeedVaultSelectAccounts": "Select accounts", "connectSeedVaultSelectAccountsDescription": "Choose which accounts you'd like to set up with Phantom", "connectSeedVaultCompleteSetup": "Complete setup", "connectSeedVaultCompleteSetupDescription": "You're all set! Explore web3 with Phantom and use your Seed Vault to confirm transactions", "connectSeedVaultConnectAnotherSeed": "Connect another Seed", "connectSeedVaultConnectAllSeedsConnected": "All seeds connected", "connectSeedVaultNoSeedsConnected": "No seeds connected. Tap the button below to authorize from the Seed Vault.", "connectSeedVaultConnectAccount": "Connect account", "connectSeedVaultLoadMore": "Load More", "connectSeedVaultNeedPermission": "Need permission", "connectSeedVaultNeedPermissionDescription": "Go to Settings to allow Phantom to use Seed Vault permissions.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} fee", "stakeAmount": "Amount", "stakeAmountBalance": "Balance", "swapTopQuotes": "Top {{numQuotes}} Quotes", "swapTopQuotesTitle": "Top Quotes", "swapProvidersTitle": "Providers", "swapProvidersFee": "{{fee}} fee", "swapProvidersTagRecommended": "Best Return", "swapProvidersTagFastest": "Fastest", "swapProviderEstimatedTimeHM": "{{hours}}h {{minutes}}m", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}}s", "stakeReview": "Review", "stakeReviewAccount": "Account", "stakeReviewCommissionFee": "Commission Fee", "stakeReviewConfirm": "Confirm", "stakeReviewValidator": "Validator", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Stake Conversion Failed", "convertStakeStatusErrorMessage": "Your stake could not be converted to {{poolTokenSymbol}}. Please try again.", "convertStakeStatusLoadingTitle": "Converting to {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "We are starting the process to convert your staked {{stakedTokenSymbol}} to {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Conversion to {{poolTokenSymbol}} complete!", "convertStakeStatusSuccessMessage": "Earn additional rewards with your JitoSOL <1>here.</1>", "convertStakeStatusConvertMore": "Convert More", "convertStakePendingTitle": "Converting stake to {{symbol}}", "convertToJitoSOL": "Convert to JitoSOL", "convertToJitoSOLInfoDescription": "Convert your SOL to Jito SOL to earn rewards and participate in the Jito ecosystem.", "convertToJitoSOLInfoTitle": "Convert to JitoSOL", "convertStakeBannerTitle": "Convert your stake to JitoSOL to boost rewards by up to 15%", "convertStakeQuestBannerTitle": "Convert staked SOL to JitoSOL and earn rewards. Learn more.", "liquidStakeConvertInfoTitle": "Convert to JitoSOL", "liquidStakeConvertInfoDescription": "Boost your rewards by converting your SOL stake to JitoSOL. <1>Learn more</1>", "liquidStakeConvertInfoFeature1Title": "Why stake with <PERSON><PERSON>?", "liquidStakeConvertInfoFeature1Description": "Deposit to get JitoSOL, which grows with your stake. Use it in DeFi protocols for extra earnings. Swap your JitoSOL later for your initial amount + accrued rewards", "liquidStakeConvertInfoFeature2Title": "Higher average rewards", "liquidStakeConvertInfoFeature2Description": "Jito spreads your SOL among the best validators with the lowest fees. MEV rewards further boost your earnings.", "liquidStakeConvertInfoFeature3Title": "Support the Solana network", "liquidStakeConvertInfoFeature3Description": "Liquid staking secures Solana by spreading stake across multiple validators, reducing risk from low uptime validators.", "liquidStakeConvertInfoSecondaryButton": "Not Now", "liquidStakeStartStaking": "Start Staking", "liquidStakeReviewOrder": "Review Order", "convertStakeAccountListPageIneligibleSectionTitle": "Ineligible Stake Accounts", "convertStakeAccountIneligibleBottomSheetTitle": "Ineligible Stake Accounts", "convertStakeAccountListPageErrorTitle": "Failed to fetch stake accounts", "convertStakeAccountListPageErrorDescription": "Sorry, something went wrong and we were unable to fetch the stake accounts", "liquidStakeReviewYouPay": "You Pay", "liquidStakeReviewYouReceive": "You Receive", "liquidStakeReviewProvider": "Provider", "liquidStakeReviewNetworkFee": "Network Fee", "liquidStakeReviewPageTitle": "Confirmation", "liquidStakeReviewConversionFootnote": "When you stake Solana tokens in exchange for JitoSOL you'll receive a slightly lesser amount of JitoSOL. <1>Learn more</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Jito's stake pool supports most active Solana validators. You will not be able to convert staked SOL from unsupported validators to JitoSOL. Additionally, newly staked SOL takes ~2 days before being eligible for JitoSOL conversion.", "selectAValidator": "Select a Validator", "validatorSelectionListTitle": "Select a Validator", "validatorSelectionListDescription": "Choose a validator to stake your SOL with.", "stakeMethodDescription": "Earn interest by using your SOL tokens to help Solana scale. <1>Learn more</1>", "stakeMethodRecommended": "Recommended", "stakeMethodEstApy": "Est. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Liquid Staking", "stakeMethodSelectionLiquidStakingDescription": "Stake SOL to earn higher rewards, help secure Solana & receive JitoSOL to earn additional rewards.", "stakeMethodSelectionNativeStakingTitle": "Native Staking", "stakeMethodSelectionNativeStakingDescription": "Stake SOL to receive rewards while helping secure Solana.", "liquidStakeMintStakeSOL": "Stake SOL", "mintJitoSOLInfoPageTitle": "Introducing Liquid Staking with <PERSON><PERSON>", "mintJitoSOLFeature1Title": "Why stake with <PERSON><PERSON>?", "mintJitoSOLFeature1Description": "Deposit to get JitoSOL, which grows with your stake. Use it in DeFi protocols for extra earnings. Swap your JitoSOL later for your initial amount + accrued rewards", "mintJitoSOLFeature2Title": "Higher average rewards", "mintJitoSOLFeature2Description": "Jito spreads your SOL among the best validators with the lowest fees. MEV rewards further boost your earnings.", "mintJitoSOLFeature3Title": "Support the Solana network", "mintJitoSOLFeature3Description": "Liquid staking secures Solana by spreading stake across multiple validators, reducing risk from low uptime validators.", "mintLiquidStakePendingTitle": "Minting liquid stake", "mintStakeStatusErrorTitle": "Minting Liquid Stake Failed", "mintStakeStatusErrorMessage": "Your {{poolTokenSymbol}} liquid stake could not be minted. Please try again.", "mintStakeStatusSuccessTitle": "Minting {{poolTokenSymbol}} liquid stake complete!", "mintStakeStatusLoadingTitle": "Minting {{poolTokenSymbol}} liquid stake", "mintStakeStatusLoadingMessage": "We are starting the process to mint your {{poolTokenSymbol}} liquid stake.", "mintLiquidStakeAmountDescription": "Choose how much SOL you'd like to stake with <PERSON><PERSON>", "mintLiquidStakeAmountProvider": "Provider", "mintLiquidStakeAmountApy": "Est. APY", "mintLiquidStakeAmountBestPrice": "Price", "mintLiquidStakeAmountInsufficientBalance": "Insufficient balance", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} required to stake", "swapTooltipGotIt": "Got it", "swapTabInsufficientFunds": "Insufficient funds", "swapNoAssetsFound": "No Assets", "swapNoTokensFound": "No tokens found", "swapConfirmationTryAgain": "Try again", "swapConfirmationGoBack": "Go back", "swapNoQuotesFound": "No quotes found", "swapNotProviderFound": "We were unable to find a provider for this token swap. Try a different token.", "swapAvailableOnMainnet": "This feature is only available on Mainnet", "swapNotAvailableEVM": "Swaps are not yet available for EVM accounts", "swapNotAvailableOnSelectedNetwork": "Swaps are not available on the selected network", "singleChainSwapTab": "In Network", "crossChainSwapTab": "Across Networks", "allFilter": "All", "bridgeRefuelTitle": "Refuel", "bridgeRefuelDescription": "Refuel ensures you can pay for transactions after you bridge.", "bridgeRefuelLabelBalance": "Your {{symbol}}", "bridgeRefuelLabelReceive": "You Receive", "bridgeRefuelLabelFee": "Estimated Cost", "bridgeRefuelDismiss": "Continue without Refuel", "bridgeRefuelEnable": "Enable Refuel", "unwrapWrappedSolError": "Unwrapping failed", "unwrapWrappedSolLoading": "Unwrapping...", "unwrapWrappedSolSuccess": "Unwrapped", "unwrapWrappedSolViewTransaction": "View Transaction", "dappApprovePopupSignMessage": "Sign Message", "solanaPayFrom": "From", "solanaPayMessage": "Message", "solanaPayNetworkFee": "Network Fee", "solanaPayFree": "Free", "solanaPayPay": "Pay {{item}}", "solanaPayPayNow": "Pay Now", "solanaPaySending": "Sending {{item}}", "solanaPayReceiving": "Receiving {{item}}", "solanaPayMinting": "Minting {{item}}", "solanaPayTransactionProcessing": "Your transaction is processing,\nplease wait.", "solanaPaySent": "Sent!", "solanaPayReceived": "Received!", "solanaPayMinted": "Minted!", "solanaPaySentNFT": "Sent NFT!", "solanaPayReceivedNFT": "Received NFT!", "solanaPayTokensSent": "Your tokens were successfully sent to {{to}}", "solanaPayTokensReceived": "You received new tokens from {{from}}", "solanaPayViewTransaction": "View transaction", "solanaPayTransactionFailed": "Transaction Failed", "solanaPayConfirm": "Confirm", "solanaPayTo": "to", "dappApproveConnectViewAccount": "View your Solana account", "deepLinkInvalidLink": "Invalid link", "deepLinkInvalidSplTokenSubtitle": "This contains a token that you don't own or we can't identify it.", "walletAvatarShowAllAccounts": "Show all accounts", "pushNotificationsGetInstantUpdates": "Get instant updates", "pushNotificationsEnablePushNotifications": "Enable push notifications about completed transfers, swaps and announcements", "pushNotificationsEnable": "Enable", "pushNotificationsNotNow": "Not now", "onboardingAgreeToTermsOfServiceInterpolated": "I agree to the <1>Terms of Service</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, I saved it somewhere", "onboardingCreateNewWallet": "Create New Wallet", "onboardingErrorDuplicateSecretRecoveryPhrase": "This secret phrase already exists in your wallet", "onboardingErrorInvalidSecretRecoveryPhrase": "Invalid secret recovery phrase", "onboardingFinished": "You're all done!", "onboardingImportAccounts": "Import Accounts", "onboardingImportImportingAccounts": "Importing Accounts...", "onboardingImportImportingFindingAccounts": "Finding accounts with activity", "onboardingImportAccountsLastActive": "Active {{formattedTimestamp}} ago", "onboardingImportAccountsNeverUsed": "Never Used", "onboardingImportAccountsCreateNew": "New wallet", "onboardingImportAccountsDescription": "Choose wallet accounts to import", "onboardingImportReadOnlyAccountDescription": "Add an address or domain name you would like to watch. You'll have view-only access and won't be able to sign transactions or messages.", "onboardingImportSecretRecoveryPhrase": "Import Secret Phrase", "onboardingImportViewAccounts": "View Accounts", "onboardingRestoreExistingWallet": "Restore an existing wallet with your 12 or 24-word secret recovery phrase", "onboardingShowUnusedAccounts": "Show Unused Accounts", "onboardingShowMoreAccounts": "Show More Accounts", "onboardingHideUnusedAccounts": "Hide Unused Accounts", "onboardingSecretRecoveryPhrase": "Secret Recovery Phrase", "onboardingSelectAccounts": "Select Your Accounts", "onboardingStoreSecretRecoveryPhraseReminder": "This is the only way you will be able to recover your account. Please store it somewhere safe!", "useTokenMetasForMintsUnknownName": "Unknown", "timeUnitMinute": "minute", "timeUnitMinutes": "minutes", "timeUnitHour": "hour", "timeUnitHours": "hours", "espNFTListWithPrice": "You listed {{NFTDisplayName}} for {{priceAmount}} {{priceTokenSymbol}} on {{dAppName}}", "espNFTListWithPriceWithoutDApp": "You listed {{NFTDisplayName}} for {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "You listed {{NFTDisplayName}} for sale on {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "You listed {{NFTDisplayName}} for sale", "espNFTChangeListPriceWithPrice": "You updated the listing for {{NFTDisplayName}} to {{priceAmount}} {{priceTokenSymbol}} on {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "You updated the listing for {{NFTDisplayName}} to {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "You updated the listing for {{NFTDisplayName}} on {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "You updated the listing for {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "You bid {{priceAmount}} {{priceTokenSymbol}} for {{NFTDisplayName}} on {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "You bid {{priceAmount}} {{priceTokenSymbol}} for {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "You placed a bid for {{NFTDisplayName}} on {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "You placed a bid for {{NFTDisplayName}}", "espNFTBidListerWithPrice": "New bid of {{priceAmount}} {{priceTokenSymbol}} for {{NFTDisplayName}} on {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "New bid of {{priceAmount}} {{priceTokenSymbol}} for {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "New bid for {{NFTDisplayName}} on {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "New bid for {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "You cancelled your bid of {{priceAmount}} {{priceTokenSymbol}} for {{NFTDisplayName}} on {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "You cancelled your bid of {{priceAmount}} {{priceTokenSymbol}} for {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "You cancelled your bid for {{NFTDisplayName}} on {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "You cancelled your bid for {{NFTDisplayName}}", "espNFTUnlist": "You unlisted {{NFTDisplayName}} on {{dAppName}}", "espNFTUnlistWithoutDApp": "You unlisted {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "You bought {{NFTDisplayName}} for {{priceAmount}} {{priceTokenSymbol}} on {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "You bought {{NFTDisplayName}} for {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "You bought {{NFTDisplayName}} on {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "You bought {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "You sold {{NFTDisplayName}} for {{priceAmount}} {{priceTokenSymbol}} on {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "You sold {{NFTDisplayName}} for {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "You sold {{NFTDisplayName}} on {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "You sold {{NFTDisplayName}}", "espDEXSwap": "You exchanged {{downTokensTextFragment}} for {{upTokensTextFragment}} on {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "You deposited {{downTokensTextFragment}} into the {{poolDisplayName}} liquidity pool on {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "You exchanged {{downTokensTextFragment}} for {{upTokensTextFragment}} on {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "You withdrew {{upTokensTextFragment}} from the {{poolDisplayName}} liquidity pool on {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "You exchanged {{downTokensTextFragment}} for {{upTokensTextFragment}} on {{dAppName}}", "espGenericTokenSend": "You sent {{downTokensTextFragment}}", "espGenericTokenReceive": "You received {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "You exchanged {{downTokensTextFragment}} for {{upTokensTextFragment}}", "espUnknown": "UNKNOWN", "espUnknownNFT": "unknown NFT", "espTextFragmentAnd": "and", "externalLinkWarningTitle": "You are about to leave Phantom", "externalLinkWarningDescription": "And open {{url}}. Make sure you trust this source before interacting with it.", "shortcutsWarningDescription": "Shortcuts provided by {{url}}. Make sure you trust this source before interacting with it.", "lowTpsBanner": "Solana is experiencing network congestion", "lowTpsMessageTitle": "Solana network congestion", "lowTpsMessage": "Due to high Solana congestion, your transactions may fail or be delayed. Please retry failed transactions.", "solanaSlow": "Solana network is unusually slow", "solanaNetworkTemporarilyDown": "The Solana network is temporarily down", "waitForNetworkRestart": "Please wait for the network to restart. Your funds are not affected.", "exploreCollectionsCarouselTitle": "What's Popular", "exploreDropsCarouselTitle": "What's New", "exploreSortFloor": "Floor", "exploreSortListed": "Listed", "exploreSortVolume": "Volume", "exploreFetchErrorSubtitle": "Please try again later.", "exploreFetchErrorTitle": "Failed to fetch.", "exploreTopCollectionsTitle": "Top NFT Collections", "exploreTopListLess": "Less", "exploreTopListMore": "More", "exploreSeeMore": "See More", "exploreTrendingTokens": "Trending Tokens", "exploreVolumeTokens": "Highest Volume", "explorePriceChangeTokens": "Biggest Gainers", "explorePriceTokens": "Tokens by Price", "exploreMarketCapTokens": "Top Tokens", "exploreTrendingSites": "Trending Sites", "exploreTopSites": "Top Sites", "exploreTrendingCollections": "Trending Collections", "exploreTopCollections": "Top Collections", "collectiblesSearchCollectionsSection": "Collections", "collectiblesSearchItemsSection": "Items", "collectiblesSearchNrOfItems": "{{ nrOfItems }} Items", "collectiblesSearchPlaceholderText": "Search your collectibles", "collectionPinSuccess": "Collection pinned", "collectionPinFail": "Collection failed to pin", "collectionUnpinSuccess": "Collection unpinned", "collectionUnpinFail": "Collection failed to unpin", "collectionHideSuccess": "Collection hidden", "collectionHideFail": "Collection failed to hide", "collectionUnhideSuccess": "Collection unhidden", "collectionUnhideFail": "Collection failed to unhide", "collectiblesSpamSuccess": "Reported as spam", "collectiblesSpamFail": "Reporting as spam failed", "collectiblesSpamAndHiddenSuccess": "Reported as spam and hidden", "collectiblesNotSpamSuccess": "Reported as not spam", "collectiblesNotSpamFail": "Reporting as not spam failed", "collectiblesNotSpamAndUnhiddenSuccess": "Reported as not spam and unhidden", "tokenPageSpamWarning": "This token is unverified. Only interact with tokens you trust.", "tokenSpamWarning": "This token was hidden because <PERSON> believes it is spam.", "collectibleSpamWarning": "This collectible was hidden because <PERSON> believes it is spam.", "collectionSpamWarning": "These collectibles were hidden because <PERSON> believes they are spam.", "emojiNoResults": "No emoji found", "emojiSearchResults": "Search Results", "emojiSuggested": "Suggested", "emojiSmileys": "Smileys & People", "emojiAnimals": "Animals & Nature", "emojiFood": "Food & Drink", "emojiTravel": "Travel & Places", "emojiActivities": "Activities", "emojiObjects": "Objects", "emojiSymbols": "Symbols", "emojiFlags": "Flags", "whichExtensionToConnectWith": "Which extension do you want to connect with?", "configureInSettings": "Configurable in Settings → Default App Wallet.", "continueWith": "Continue with", "useMetaMask": "Use MetaMask", "usePhantom": "Use Phantom", "alwaysAsk": "Always Ask", "dontAskMeAgain": "Don't ask me again", "selectWalletSettingDescriptionLine1": "Some apps may not offer an option to connect with Phantom.", "selectWalletSettingDescriptionLinePhantom": "As a work-around, connecting with MetaMask will always open Phantom instead.", "selectWalletSettingDescriptionLineAlwaysAsk": "As a work-around, when you connect with MetaMask, we will ask you if you want to use Phantom instead.", "selectWalletSettingDescriptionLineMetaMask": "Setting MetaMask as default will disable those dapps from connecting to Phantom.", "metaMaskOverride": "<PERSON><PERSON><PERSON>", "metaMaskOverrideSettingDescriptionLine1": "For connecting to websites that don’t offer an option to use Phantom.", "refreshAndReconnectToast": "Refresh and reconnect to apply your changes", "autoConfirmUnavailable": "Unavailable", "autoConfirmReasonDappNotWhitelisted": "Unavailable because the contract it came from is not on our allowlist for this app.", "autoConfirmReasonSessionNotActive": "Unavailable because there is no auto-confirm session active. Please enable it below.", "autoConfirmReasonRateLimited": "Unavailable because the dapp you're using is sending too many requests.", "autoConfirmReasonUnsupportedNetwork": "Unavailable because auto-confirm doesn't support this network yet.", "autoConfirmReasonSimulationFailed": "Unavailable because we couldn't guarantee security.", "autoConfirmReasonTabNotFocused": "Unavailable because the tab of domain you're trying to auto-confirm on is not active.", "autoConfirmReasonNotUnlocked": "Unavailable because the wallet was not unlocked.", "rpcErrorUnauthorizedWrongAccount": "Transaction from address does not match the selected account address.", "rpcErrorUnauthorizedUnknownSource": "The RPC request source could not be determined.", "transactionsDisabledTitle": "Transactions disabled", "transactionsDisabledMessage": "Your address is unable to transact using Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Active", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL copied to clipboard", "notEnoughSolScanTransactionWarning": "This transaction may fail due to insufficient SOL in your account. Please add more SOL to your account and try again.", "transactionRevertedWarning": "This transaction reverted during simulation. Funds may be lost if submitted.", "slippageToleranceExceeded": "This transaction reverted during simulation. Slippage tolerance exceeded.", "simulationWarningKnownMalicious": "We believe this account is malicious. Approving may lead to loss of funds.", "simulationWarningPoisonedAddress": "This address is suspiciously similar to an address you recently sent funds to. Please confirm this is the correct address to prevent losing funds to a scam.", "simulationWarningInteractingWithAccountWithoutActivity": "This is an account without any previous activity. Sending funds to a non-existent account can lead to loss of funds", "quests": "Quests", "questsClaimInProgress": "Claiming in progress", "questsVerifyingCompletion": "Verifying completion for quest", "questsClaimError": "Error claiming reward", "questsClaimErrorDescription": "There was an error claiming your reward. Please try again later.", "questsBadgeMobileOnly": "Mobile Only", "questsBadgeExtensionOnly": "Extension Only", "questsExplainerSheetButtonLabel": "Got it", "questsNoQuestsAvailable": "No quests available", "questsNoQuestsAvailableDescription": "There are currently no quests available. We’ll notify you as soon as new ones are added.", "exploreLearn": "Learn", "exploreSites": "Sites", "exploreTokens": "Tokens", "exploreQuests": "Quests", "exploreCollections": "Collections", "exploreFilterByall_networks": "All Networks", "exploreSortByrank": "Trending", "exploreSortBytrending": "Trending", "exploreSortByprice": "Price", "exploreSortByprice-change": "Price Change", "exploreSortBytop": "Top", "exploreSortByvolume": "Volume", "exploreSortBygainers": "Gaine<PERSON>", "exploreSortBylosers": "Losers", "exploreSortBymarket-cap": "Market Cap", "exploreSortBymarket_cap": "Market Cap", "exploreTimeFrame1h": "1h", "exploreTimeFrame24h": "24h", "exploreTimeFrame7d": "7d", "exploreTimeFrame30d": "30d", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Collectibles", "exploreCategoryMarketplace": "Marketplace", "exploreCategoryGaming": "Gaming", "exploreCategoryBridges": "Bridges", "exploreCategoryOther": "Other", "exploreCategorySocial": "Social", "exploreCategoryCommunity": "Community", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Staking", "exploreCategoryArt": "Art", "exploreCategoryTools": "Tools", "exploreCategoryDeveloperTools": "Developer Tools", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "NFT Staking", "exploreCategoryExplorer": "Explorer", "exploreCategoryInscriptions": "Inscriptions", "exploreCategoryBridge": "Bridge", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Airdrop Checker", "exploreCategoryPoints": "Points", "exploreCategoryQuests": "Quests", "exploreCategoryShop": "Shop", "exploreCategoryProtocol": "Protocol", "exploreCategoryNamingService": "Naming Service", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Portfolio Tracker", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volume", "exploreFloor": "Floor", "exploreCap": "Market Cap", "exploreToken": "Token", "explorePrice": "Price", "explore24hVolume": "24h Volume", "exploreErrorButtonText": "Try Again", "exploreErrorDescription": "There was an error attempting to load the explore content. Please refresh and try again", "exploreErrorTitle": "Failed to load explore content", "exploreNetworkError": "There was a network error. Please try again later.", "exploreTokensLegalDisclaimer": "Token lists are generated using market data provided by various third party providers including CoinGecko, Birdeye and Jupiter. Performance is based on the prior 24 hour period. Past performance is not indicative of future performance.", "swapperTokensLegalDisclaimer": "Trending token lists are generated using market data from various third party providers including CoinGecko, Birdeye and Jupiter and based on popular tokens swapped by Phantom users via the Swapper over the stated time period. Past performance is not indicative of future performance.", "exploreLearnErrorTitle": "Failed to load learn content", "exploreLearnErrorDescription": "There was an error attempting to load the learn content. Please refresh and try again", "exploreShowMore": "Show more", "exploreShowLess": "Show less", "exploreVisitSite": "Visit Site", "dappBrowserSearchScreenVisitSite": "Visit site", "dappBrowserSearchScreenSearchWithGoogle": "Search with Google", "dappBrowserSearchScreenSearchLinkYouCopied": "Link You Copied", "dappBrowserExtSearchPlaceholder": "Search for sites, tokens", "dappBrowserSearchNoAppsTokens": "No apps or tokens found", "dappBrowserTabsLimitExceededScreenTitle": "Close Older Tabs?", "dappBrowserTabsLimitExceededScreenDescription": "You have {{tabsCount}} tabs open. To open more, you will need to close some tabs.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "Close All Tabs", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: This domain does not exist", "dappBrowserTabErrorHttp": "Blocked, please use HTTPS", "dappBrowserTabError401Unauthorized": "401 Not authorized", "dappBrowserTabError501UnhandledRequest": "501 Unhandled request", "dappBrowserTabErrorTimeout": "TIMEOUT: Server took too long to respond", "dappBrowserTabErrorInvalidResponse": "Invalid response", "dappBrowserTabErrorEmptyResponse": "Empty response", "dappBrowserTabErrorGeneric": "An error occurred", "localizedErrorUnknownError": "Something went wrong, please try again later.", "localizedErrorUnsupportedCountry": "We are sorry, your country is not currently supported.", "localizedErrorTokensNotLoading": "There was a problem loading your tokens. Please try again.", "localizedErrorSwapperNoQuotes": "No swaps available due to unsupported pair, low liquidity, or low amount. Try adjusting the token or amount.", "localizedErrorSwapperRefuelNoQuotes": "No quotes found. Try a different token, amount, or disable refuel.", "localizedErrorInsufficientSellAmount": "Token amount too low. Increase the value to swap Cross-Chain.", "localizedErrorCrossChainUnavailable": "Cross-chain swaps currently unavailable, please try again later.", "localizedErrorTokenNotTradable": "One of the selected tokens is not tradable. Please select a different token.", "localizedErrorCollectibleLocked": "Token account is locked.", "localizedErrorCollectibleListed": "Token account is listed.", "spamActivityAction": "View hidden items", "spamActivityTitle": "Hidden Activity", "spamActivityWarning": "This transaction was hidden because Phantom believes it may be spam.", "appAuthenticationFailed": "Failed to authenticate", "appAuthenticationFailedDescription": "There was an issue with your authentication attempt. Please try again.", "partialErrorBalanceChainName": "We are having trouble updating your {{chainName}} balances. Your funds are safe.", "partialErrorGeneric": "We are having trouble updating networks, some of your token balances and prices may be out of date. Your funds are safe.", "partialErrorTokenDetail": "We are having trouble updating your token balance. Your funds are safe.", "partialErrorTokenPrices": "We are having trouble updating your token prices. Your funds are safe.", "partialErrorTokensTrimmed": "We are having trouble displaying all of the tokens in your portfolio. Your funds are safe.", "publicFungibleDetailAbout": "About", "publicFungibleDetailYourBalance": "Your Balance", "publicFungibleDetailInfo": "Info", "publicFungibleDetailShowMore": "Show More", "publicFungibleDetailShowLess": "Show Less", "publicFungibleDetailPerformance": "24h Performance", "publicFungibleDetailSecurity": "Security", "publicFungibleDetailMarketCap": "Market Cap", "publicFungibleDetailTotalSupply": "Total Supply", "publicFungibleDetailCirculatingSupply": "Circulating Supply", "publicFungibleDetailMaxSupply": "Max Supply", "publicFungibleDetailHolders": "Holders", "publicFungibleDetailVolume": "Volume", "publicFungibleDetailTrades": "Trades", "publicFungibleDetailTraders": "Traders", "publicFungibleDetailUniqueWallets": "Unique Wallets", "publicFungibleDetailTop10Holders": "Top 10 Holders", "publicFungibleDetailTop10HoldersTooltip": "Indicates the percent of the current total supply held by the top 10 holders of the token. It's a measure of how easily the price can be manipulated.", "publicFungibleDetailMintable": "Mintable", "publicFungibleDetailMintableTooltip": "Token supply may be increased by the contract owner if a token is mintable.", "publicFungibleDetailMutableInfo": "Mutable Info", "publicFungibleDetailMutableInfoTooltip": "If token info such as name, logo, and website address is mutable it can be changed by the contract owner.", "publicFungibleDetailOwnershipRenounced": "Ownership Renounced", "publicFungibleDetailOwnershipRenouncedTooltip": "If token ownership is renounced, no one can execute functions such as mint more tokens.", "publicFungibleDetailUpdateAuthority": "Update Authority", "publicFungibleDetailUpdateAuthorityTooltip": "The update authority is the wallet address that can change information if a token is mutable.", "publicFungibleDetailFreezeAuthority": "Freeze Authority", "publicFungibleDetailFreezeAuthorityTooltip": "The freeze authority is the wallet address that can prevent funds from being transferred.", "publicFungibleUnverifiedToken": "This token is unverified. Only interact with tokens you trust.", "publicFungibleDetailSwap": "Swap {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Swap {{tokenSymbol}} with the Phantom app", "publicFungibleDetailLinkCopied": "Copied to clipboard", "publicFungibleDetailContract": "Contract", "publicFungibleDetailMint": "Mint", "unifiedTokenDetailTransactionActivity": "Activity", "unifiedTokenDetailSeeMoreTransactionActivity": "See More", "unifiedTokenDetailTransactionActivityError": "Failed to load recent activity", "additionalNetworksTitle": "Additional Networks", "copyAddressRowAdditionalNetworks": "Additional Networks", "copyAddressRowAdditionalNetworksHeader": "The following networks use the same address as Ethereum:", "copyAddressRowAdditionalNetworksDescription": "You can safely use your Ethereum address to send and receive assets on any of these networks.", "cpeUnknownError": "Unknown error", "cpeUnknownInstructionError": "Unknown instruction error", "cpeAccountFrozen": "Account is frozen", "cpeAssetFrozen": "Asset is frozen", "cpeInsufficientFunds": "Insufficient funds", "cpeInvalidAuthority": "Invalid authority", "cpeBalanceBelowRent": "Balance below rent-exempt threshold", "cpeNotApprovedForConfidentialTransfers": "Account not approved for confidential transfers", "cpeNotAcceptingDepositsOrTransfers": "Account not accepting deposits or transfers", "cpeNoMemoButRequired": "No memo in previous instruction; required for recipient to receive a transfer", "cpeTransferDisabledForMint": "Transfer is disabled for this mint", "cpeDepositAmountExceedsLimit": "Deposit amount exceeds maximum limit", "cpeInsufficientFundsForRent": "Insufficient funds for rent", "reportIssueScreenTitle": "Report an Issue", "publicFungibleReportIssuePrompt": "What issue do you want to report about {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "Incorrect Information", "publicFungibleReportIssuePriceStale": "Price is not updating", "publicFungibleReportIssuePriceMissing": "Price is missing", "publicFungibleReportIssuePerformanceIncorrect": "24h Performance is incorrect", "publicFungibleReportIssueLinkBroken": "Social links not reachable", "publicFungibleDetailErrorLoading": "Token data unavailable", "reportUserPrompt": "What issue do you want to report about @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "Abuse and Harassment", "reportUserOptionAbuseAndHarrassmentDescription": "Targeted harassment, inciting harassment, violent threats, hateful content and references", "reportUserOptionPrivacyAndImpersonationTitle": "Privacy and Impersonation", "reportUserOptionPrivacyAndImpersonationDescription": "Sharing or threatening to expose private information, pretending to be someone else", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "Fake account, scams, malicious links", "reportUserSuccess": "User Report Submitted.", "settingsClaimUsernameTitle": "Create <PERSON>rna<PERSON>", "settingsClaimUsernameDescription": "A unique identity as unique as your wallet", "settingsClaimUsernameValueProp1": "Simplified Identity", "settingsClaimUsernameValueProp1Description": "Say goodbye to long complex addresses and hello to a user-friendly identity", "settingsClaimUsernameValueProp2": "Fast<PERSON> & Easier", "settingsClaimUsernameValueProp2Description": "Easily send and receive crypto, login to your wallet, and connect with friends", "settingsClaimUsernameValueProp3": "Stay in Sync", "settingsClaimUsernameValueProp3Description": "Connect any account to your username and it will sync across all your devices", "settingsClaimUsernameHelperText": "Your unique name for your Phantom Account", "settingsClaimUsernameValidationDefault": "This username cannot be changed later", "settingsClaimUsernameValidationAvailable": "Username available", "settingsClaimUsernameValidationUnavailable": "Username unavailable", "settingsClaimUsernameValidationServerError": "Unable to check if username is available, please try again later", "settingsClaimUsernameValidationErrorLine1": "Invalid username.", "settingsClaimUsernameValidationErrorLine2": "Usernames must be between {{minChar}} and {{maxChar}} characters long and can only contain letters and numbers.", "settingsClaimUsernameValidationLoading": "Checking whether this username is available...", "settingsClaimUsernameSaveAndContinue": "Save & Continue", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON>", "settingsClaimUsernameAnonymousAuthTitle": "Anonymous Auth", "settingsClaimUsernameAnonymousAuthDescription": "Sign into your Phantom Account anonymously with a signature", "settingsClaimUsernameAnonymousAuthBadge": "Learn how this works", "settingsClaimUsernameLinkWalletsTitle": "Link your wallets", "settingsClaimUsernameLinkWalletsDescription": "Choose the wallets that show up on other devices with your username", "settingsClaimUsernameLinkWalletsBadge": "Not Publicly Viewable", "settingsClaimUsernameConnectAccountsTitle": "Connect Accounts", "settingsClaimUsernameConnectAccountsHelperText": "Each chain address will be connected with your username. You can change these later.", "settingsClaimUsernameContinue": "Continue", "settingsClaimUsernameCreateUsername": "Create <PERSON>rna<PERSON>", "settingsClaimUsernameCreating": "Creating username...", "settingsClaimUsernameSuccess": "Username Created!", "settingsClaimUsernameError": "We encountered an error creating your username", "settingsClaimUsernameTryAgain": "Try Again", "settingsClaimUsernameSuccessHelperText": "You can now use your new username in all of your Phantom wallets", "settingsClaimUsernameSettingsSyncedTitle": "Synced Settings", "settingsClaimUsernameSettingsSyncedHelperText": "Say goodbye to long complex addresses and hello to a user-friendly identity", "settingsClaimUsernameSendToUsernameTitle": "Send to Username", "settingsClaimUsernameSendToUsernameHelperText": "Easily send and receive crypto, login to your wallet, and connect with friends", "settingsClaimUsernameManageAddressesTitle": "Public Addresses", "settingsClaimUsernameManageAddressesHelperText": "Any tokens or collectibles sent to your username will send to these addresses", "settingsClaimUsernameManageAddressesBadge": "Publicly Viewable", "settingsClaimUsernameEditAddressesTitle": "Manage Public Addresses", "settingsClaimUsernameEditAddressesHelperText": "Any tokens or collectibles sent to your username will send to these addresses. Select one address per chain.", "settingsClaimUsernameEditAddressesError": "Only one address per network is allowed.", "settingsClaimUsernameEditAddressesEditAddress": "Edit Addresses", "settingsClaimUsernameNoAddressesSaved": "No public addresses saved", "settingsClaimUsernameSave": "Save", "settingsClaimUsernameDone": "Done", "settingsClaimUsernameWatching": "Watching", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} Account(s)", "settingsClaimUsernameNoOfAccountsSingular": "1 Account", "settingsClaimUsernameEmptyAccounts": "No Account(s)", "settingsClaimUsernameSettingTitle": "Create your @username", "settingsClaimUsernameSettingDescription": "A unique identity for your wallet", "settingsManageUserProfileAbout": "About", "settingsManageUserProfileAboutUsername": "Username", "settingsManageUserProfileAboutBio": "Bio", "settingsManageUserProfileTitle": "Manage Profile", "settingsManageUserProfileManage": "Manage", "settingsManageUserProfileAuthFactors": "Auth Factors", "settingsManageUserProfileAuthFactorsDescription": "Choose which seed phrases or private keys can login to your Phantom Account.", "settingsManageUserProfileUpdateAuthFactorsToast": "Auth factors updated!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Failed to update auth factors!", "settingsManageUserProfileBiography": "Edit <PERSON>io", "settingsManageUserProfileBiographyDescription": "Add a short bio to your profile", "settingsManageUserProfileUpdateBiographyToast": "Bio Updated!", "settingsManageUserProfileUpdateBiographyToastFailure": "Something Happened. Try Again", "settingsManageUserProfileBiographyNoUrlMessage": "Please remove any URLs from your bio", "settingsManageUserProfileLinkedWallets": "Linked Wallets", "settingsManageUserProfileLinkedWalletsDescription": "Choose the wallets that show up on other devices when logging into your Phantom Account.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Linked wallets updated!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Failed to update linked wallets!", "settingsManageUserProfilePrivacy": "Privacy", "settingsManageUserProfileUpdatePrivacyStateToast": "Privacy updated!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Failed to update privacy!", "settingsManageUserProfilePublicAddresses": "Public Addresses", "settingsManageUserProfileUpdatePublicAddressToast": "Public address updated!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Failed to update public address!", "settingsManageUserProfilePrivacyStatePublic": "Public", "settingsManageUserProfilePrivacyStatePublicDescription": "Your profile and public addresses are visible and searchable by anyone", "settingsManageUserProfilePrivacyStatePrivate": "Private", "settingsManageUserProfilePrivacyStatePrivateDescription": "Your profile is searchable by anyone but others must request permission to view your profile and public addresses", "settingsManageUserProfilePrivacyStateInvisible": "Invisible", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Your profile and public addresses are hidden and undiscoverable everywhere", "settingsDownloadPhantom": "Download Phantom", "settingsLogOut": "Log Out", "seedlessAddAWalletPrimaryText": "Add a Wallet", "seedlessAddAWalletSecondaryText": "Login or import an existing wallet ", "seedlessAddSeedlessWalletPrimaryText": "Add Seedless Wallet", "seedlessAddSeedlessWalletSecondaryText": "Use your Apple ID, Google or Email", "seedlessCreateNewWalletPrimaryText": "Create New Wallet?", "seedlessCreateNewWalletSecondaryText": "This email doesn't have a wallet, would you like to create one?", "seedlessCreateNewWalletButtonText": "Create Wallet", "seedlessCreateNewWalletNoBundlePrimaryText": "Wallet not found", "seedlessCreateNewWalletNoBundleSecondaryText": "This email doesn't have a wallet", "seedlessCreateNewWalletNoBundleButtonText": "Go back", "seedlessEmailOptionsPrimaryText": "Select Your Email", "seedlessEmailOptionsSecondaryText": "Add a wallet with your Apple or Google account ", "seedlessEmailOptionsButtonText": "Continue with <PERSON>ail", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Create wallet with your Apple ID", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Create wallet with your Google email", "seedlessAlreadyExistsPrimaryText": "Account Already Exists", "seedlessAlreadyExistsSecondaryText": "This email already has a wallet created, would you like to login instead?", "seedlessSignUpWithAppleButtonText": "Sign up with Apple", "seedlessContinueWithAppleButtonText": "Continue with Apple", "seedlessSignUpWithGoogleButtonText": "Sign up with Google", "seedlessContinueWithGoogleButtonText": "Continue with Google", "seedlessCreateAPinPrimaryText": "Create a PIN", "seedlessCreateAPinSecondaryText": "This is used to secure your wallet on all your devices. <1>This cannot be recovered.</1>", "seedlessContinueText": "Continue", "seedlessConfirmPinPrimaryText": "Confirm your PIN", "seedlessConfirmPinSecondaryText": "If you forget this PIN, you will not be able to recover your wallet on a new device.", "seedlessConfirmPinButtonText": "Create PIN", "seedlessConfirmPinError": "Incorrect PIN. Please try again", "seedlessAccountsImportedPrimaryText": "Accounts Imported", "seedlessAccountsImportedSecondaryText": "These accounts will be automatically imported in your wallet", "seedlessPreviouslyImportedTag": "Previously imported", "seedlessEnterPinPrimaryText": "Enter your PIN", "seedlessEnterPinInvalidPinError": "Incorrect PIN entered. Only 4 digit numbers are allowed", "seedlessEnterPinNumTriesLeft": "{{numTries}} attempts remaining.", "seedlessEnterPinCooldown": "Try again in {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN must be exactly 4 digits", "seedlessEnterPinMatch": "PINs match", "seedlessDoneText": "Done", "seedlessEnterPinToSign": "Enter your PIN to sign this transaction", "seedlessSigning": "Signing", "seedlessCreateSeed": "Create a seed phrase wallet", "seedlessImportOptions": "Other import options", "seedlessImportPrimaryText": "Import Options", "seedlessImportSecondaryText": "Import an existing wallet with your seed phrase, private key or hardware wallet", "seedlessImportSeedPhrase": "Import Seed Phrase", "seedlessImportPrivateKey": "Import Private Key", "seedlessConnectHardwareWallet": "Connect Hardware Wallet", "seedlessTryAgain": "Try again", "seedlessCreatingWalletPrimaryText": "Creating wallet", "seedlessCreatingWalletSecondaryText": "Adding a social wallet", "seedlessLoadingWalletPrimaryText": "Loading wallet", "seedlessLoadingWalletSecondaryText": "Importing and watching your linked wallets", "seedlessLoadingWalletErrorPrimaryText": "Failed to load wallet", "seedlessCreatingWalletErrorPrimaryText": "Failed to create wallet", "seedlessErrorSecondaryText": "Please try again", "seedlessAuthAlreadyExistsErrorText": "The email provided already belongs to a different Phantom account", "seedlessAuthUnknownErrorText": "An unknown error occurred, please try again later", "seedlessAuthUnknownErrorTextRefresh": "An unknown error occurred, please try again later. Refresh the page to try again.", "seedlessAuthErrorCloseWindow": "Close Window", "seedlessWalletExistsErrorPrimaryText": "A social wallet already exists on your device", "seedlessWalletExistsErrorSecondaryText": "Please go back or close this screen", "seedlessValueProp1PrimaryText": "Seamless setup", "seedlessValueProp1SecondaryText": "Create a wallet using a Google or Apple account and start exploring web3 with ease", "seedlessValueProp2PrimaryText": "Enhanced security", "seedlessValueProp2SecondaryText": "Your wallet is stored securely and decentralized across multiple factors", "seedlessValueProp3PrimaryText": "Easy recovery", "seedlessValueProp3SecondaryText": "Recover access to your wallet with your Google or Apple account and a 4-digit PIN", "seedlessLoggingIn": "Logging in...", "seedlessSignUpOrLogin": "Sign Up or Log In", "seedlessContinueByEnteringYourEmail": "Continue by entering in your email", "seedless": "Seedless", "seed": "Seed Phrases", "seedlessVerifyPinPrimaryText": "Verify PIN", "seedlessVerifyPinSecondaryText": "Please enter your PIN number to continue", "seedlessVerifyPinVerifyButtonText": "Verify", "seedlessVerifyPinForgotButtonText": "Forgot?", "seedlessPinConfirmButtonText": "Confirm", "seedlessVerifyToastPrimaryText": "Verify Your PIN", "seedlessVerifyToastSecondaryText": "We'll occasionally ask you to verify your PIN so that you remember it. If you forget, you will not be able to recover your wallet.", "seedlessVerifyToastSuccessText": "Your PIN number is verified!", "seedlessForgotPinPrimaryText": "Reset PIN using another device", "seedlessForgotPinSecondaryText": "For security, you can only reset your PIN in other devices where you are logged in", "seedlessForgotPinInstruction1PrimaryText": "Open Other Device", "seedlessForgotPinInstruction1SecondaryText": "Go to another device where your Phantom account is signed in with your email", "seedlessForgotPinInstruction2PrimaryText": "Go to Settings", "seedlessForgotPinInstruction2SecondaryText": "In Settings, select “Security & Privacy” and then “Reset PIN”", "seedlessForgotPinInstruction3PrimaryText": "Set Your New PIN", "seedlessForgotPinInstruction3SecondaryText": "Once you've set your new PIN, you can now login to your wallet on this device", "seedlessForgotPinButtonText": "I've done these steps", "seedlessResetPinPrimaryText": "Reset PIN", "seedlessResetPinSecondaryText": "Enter a new PIN that you will remember. This is used to secure your wallet on all your devices", "seedlessResetPinSuccessText": "Your PIN number is updated!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "By creating a wallet, you agree to our <1>Terms of Service</1> and <5>Privacy Policy</5>", "pageNotFound": "Page not found", "pageNotFoundDescription": "We haven't ghosted you! This page doesn't exist, or has been moved.", "webTokenPagesLegalDisclaimer": "Pricing information is provided for informational purposes only and is not financial advice. Market data is provided by third parties and Phantom makes no representation as to the accuracy of the information.", "signUpOrLogin": "Signup or login", "portalOnboardingAgreeToTermsOfServiceInterpolated": "By creating an account, you agree to our <1>Terms of Service</1> and <5>Privacy Policy</5>", "feedNoActivity": "No activity yet", "followRequests": "Follow Requests", "following": "Following", "followers": "Followers", "follower": "Follower", "joined": "Joined", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "No Followers", "noFollowing": "No Following", "noUsersFound": "No Users Found", "viewProfile": "View Profile", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}