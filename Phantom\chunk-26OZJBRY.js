import{a as i}from"./chunk-XJTFMD4C.js";import{b as l,d as g,e as c}from"./chunk-AVT3M45V.js";import{c as n}from"./chunk-UCBZOSRF.js";import{a as R}from"./chunk-BTKBODVJ.js";import{wa as m}from"./chunk-L3A2KHJO.js";import{f as L,h as t,n as s}from"./chunk-3KENBVE7.js";t();s();t();s();var p=L(R());var d=async(o,e)=>{let a=i("logMessage",[o,e]),r=await n(p.default.runtime,a);if("error"in r)throw new Error(r.error.message);return r.result},u=async()=>{let o=i("downloadLogs",null),e=await n(p.default.runtime,o);if("error"in e)throw new Error(e.error.message);return e.result};var P={...c,downloadLog:async o=>{if(!g){console.error(m);return}await u();let e=l.getRequests();await g.downloadLog(o,e)},write:(o,e,a,r)=>{d(o,{featureTag:e,severity:a,data:r})}};export{P as a};
//# sourceMappingURL=chunk-26OZJBRY.js.map
