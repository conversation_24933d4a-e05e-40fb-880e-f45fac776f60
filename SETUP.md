# 🚀 Setup Guide - Hướng dẫn cài đặt chi tiết

Hướng dẫn từng bước để setup môi trường test cho dự án Dex3-Automation_Playwright.

## 📋 Checklist trước khi bắt đầu

- [ ] Node.js >= 18.0.0 đã được cài đặt
- [ ] npm >= 8.0.0 đã được cài đặt  
- [ ] Chrome browser đã được cài đặt
- [ ] Git đã được cài đặt
- [ ] C<PERSON> quyền admin để cài đặt packages

## 🔧 Bước 1: Cài đặt Node.js và npm

### Windows:
1. Tải Node.js từ https://nodejs.org/
2. Chạy installer và follow hướng dẫn
3. Mở Command Prompt và verify:
```cmd
node --version
npm --version
```

### Mac:
```bash
# Sử dụng Homebrew
brew install node

# Hoặc tải từ nodejs.org
```

### Linux:
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install nodejs npm

# CentOS/RHEL
sudo yum install nodejs npm
```

## 📦 Bước 2: Clone và cài đặt dependencies

```bash
# Clone repository
git clone https://gitlab.com/platform126/automation-testing/dex3.git
cd dex3

# Cài đặt dependencies
npm install

# Cài đặt Playwright browsers
npx playwright install
npx playwright install-deps
```

## 🦊 Bước 3: Chuẩn bị Phantom Wallet Extension

### Cách 1: Tải từ Chrome Web Store (Khuyến nghị)

1. Mở Chrome và vào Chrome Web Store
2. Tìm "Phantom" wallet extension
3. Click "Add to Chrome" để cài đặt
4. Tìm đường dẫn extension:
   - Vào `chrome://extensions/`
   - Bật "Developer mode"
   - Tìm Phantom extension và note ID
   - Đường dẫn thường là: `C:\Users\<USER>\AppData\Local\Google\Chrome\User Data\Default\Extensions\[extension-id]`

### Cách 2: Sử dụng extension có sẵn trong dự án

Dự án đã có thư mục `Phantom/` chứa extension files. Sử dụng đường dẫn:
```
[project-root]/Phantom
```

## ⚙️ Bước 4: Cấu hình Environment

### 1. Tạo file env.ts

Tạo file `tests/base/env.ts` với nội dung cơ bản:

### 2. Chỉnh sửa file env.ts

Mở file `tests/base/env.ts` và cập nhật:

```typescript
export const ENV = {
  BASE_URL: 'https://dex3.ai',

  // Cập nhật đường dẫn Phantom extension
  PHANTOM_EXTENSION_PATH: 'C:\\path\\to\\your\\phantom\\extension',

  // Cập nhật thông tin ví test
  WALLET_SEED_PHRASE: 'your test wallet seed phrase here',
  WALLET_PASSWORD: 'your_test_password',

  // Các cài đặt khác...
};
```

### 3. Tạo test wallet

⚠️ **QUAN TRỌNG**: Chỉ sử dụng ví test, không sử dụng ví thật!

1. Tạo ví mới trên Phantom
2. Backup seed phrase
3. Nạp một ít SOL test từ faucet (nếu cần)
4. Cập nhật thông tin vào `env.ts`

## 🧪 Bước 5: Chạy setup lần đầu

### 1. Chạy global setup

```bash
npm run setup
```

Lệnh này sẽ:
- Khởi tạo browser với Phantom extension
- Import ví Phantom
- Đăng nhập vào Dex3
- Lưu trạng thái đăng nhập

### 2. Verify setup thành công

Kiểm tra các file sau được tạo:
- [ ] `storageState.json` - Trạng thái browser
- [ ] `login-state.json` - Thông tin đăng nhập
- [ ] `screenshots/` - Thư mục screenshots

### 3. Test setup

```bash
# Chạy một test đơn giản để verify
npm run test:dashboard
```

## 🔍 Bước 6: Verify cài đặt

### Chạy tất cả tests

```bash
npm test
```

### Kiểm tra từng module

```bash
# Test dashboard
npm run test:dashboard

# Test search
npm run test:search

# Test filter
npm run test:filter

# Test buy/sell (cần ví có SOL)
npm run test:buysell
```

## 🐛 Troubleshooting Setup

### Lỗi: "Phantom extension not found"

**Giải pháp:**
1. Kiểm tra đường dẫn trong `env.ts`
2. Đảm bảo extension folder tồn tại
3. Thử sử dụng đường dẫn tuyệt đối

```typescript
// Windows
PHANTOM_EXTENSION_PATH: 'C:\\Users\\<USER>\\Downloads\\Phantom'

// Mac/Linux  
PHANTOM_EXTENSION_PATH: '/Users/<USER>/Downloads/Phantom'
```

### Lỗi: "Cannot import wallet"

**Giải pháp:**
1. Kiểm tra seed phrase đúng format (12 hoặc 24 từ)
2. Đảm bảo không có ký tự đặc biệt
3. Thử chạy lại setup:

```bash
npm run teardown
npm run setup
```

### Lỗi: "Test timeout"

**Giải pháp:**
1. Kiểm tra network connection
2. Tăng timeout trong `playwright.config.ts`
3. Chạy với headed mode để debug:

```bash
npm run test:headed
```

### Lỗi: "Permission denied"

**Giải pháp:**
1. Chạy terminal với quyền admin
2. Kiểm tra quyền truy cập thư mục
3. Thử cài đặt global packages:

```bash
npm install -g playwright
```

## 📝 Cấu hình nâng cao

### 1. Cấu hình CI/CD

File `.github/workflows/playwright.yml` đã được cấu hình sẵn cho GitHub Actions.

### 2. Cấu hình multiple environments

Tạo nhiều file env cho các môi trường khác nhau:
- `env.dev.ts`
- `env.staging.ts`
- `env.prod.ts`

### 3. Cấu hình parallel testing

Trong `playwright.config.ts`:
```typescript
workers: process.env.CI ? 1 : 4, // Chạy 4 workers parallel
```

## ✅ Hoàn thành setup

Sau khi hoàn thành tất cả bước trên, bạn có thể:

1. ✅ Chạy tất cả tests: `npm test`
2. ✅ Chạy tests riêng lẻ: `npm run test:dashboard`
3. ✅ Xem reports: `npm run report`
4. ✅ Debug tests: `npm run test:headed`

**🎉 Chúc mừng! Setup hoàn tất. Happy testing! 🚀**
