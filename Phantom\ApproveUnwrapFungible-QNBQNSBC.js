import{a as te}from"./chunk-MXORZ3WH.js";import{E as R,Ma as ne}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import{b as ee}from"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import{a as _}from"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as T}from"./chunk-CCQRCL2K.js";import{h as Y,m as Z}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{b as Q,k as W}from"./chunk-OKP6DFCI.js";import{c as K,o as w,rb as X}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import{b as $,n as q,y as J}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{F as B,mb as L,ob as z,pb as O,vb as j}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as V,Pa as b,pe as I}from"./chunk-MZZEJ42N.js";import{p as N}from"./chunk-E3NPIRHS.js";import{m as H}from"./chunk-56SJOU6P.js";import{b as M}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as Se}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as fe,h as P,n as G}from"./chunk-3KENBVE7.js";P();G();var e=fe(Se());var Te=w(T).attrs({align:"center"})`
  height: 100%;
`,ye=w(T).attrs({align:"center",justify:"space-between"})`
  height: 100%;
`,be=w(T).attrs({align:"center",paddingTop:"25px"})`
  border-radius: 6px;
`,he=w(X).attrs({size:38,weight:500,lineHeight:46})`
  margin-top: 18px;
`,Fe=(o,t)=>{let{status:s,data:i}=L({networkID:o}),[n,c]=(0,e.useState)("standard"),{popDetailView:a,pushDetailView:m}=Y(),g;return s!=="error"&&i&&n&&t&&(g=()=>{m(e.default.createElement(te,{onSelectTransactionSpeed:c,selectedTransactionSpeed:n,networkID:o,transactionUnitAmount:t,closeModal:a}))}),{transactionSpeed:n,openGasSettings:g}},Ee=o=>{let{t}=H(),s=K(),{handleHideModalVisibility:i}=ne(),{chain:n,tokenAddress:c,balance:a,symbol:m,logoUri:g,walletAddress:oe}=o.data,r=n.id,h=b.getNetworkName(r),{data:ae}=V(),re=ae?.addresses??[],{data:d}=I({networkID:r,address:oe}),u=(0,e.useMemo)(()=>({pageHeaderText:t("unwrapFungibleConfirmSwap"),swapTitle:t("unwrapFungibleConfirmSwapTitle",{fromToken:m,toToken:n.symbol}),networkFeeText:t("sendFungibleSummaryNetworkFee"),networkFeeTextDescription:t("networkFeesTooltipDescription",{chainName:h}),confirmSwap:t("commandSwap"),cancelSwap:t("commandClose")}),[n.symbol,h,m,t]),{data:f,isPending:ie}=$({amount:a,caip19:{chainId:r,resourceType:"address",address:c??""},owner:d?.address??""}),p=(0,e.useMemo)(()=>f&&d?N.parse({to:f.to,from:d.address??"",data:f.data}):void 0,[f,d]),F=(0,e.useMemo)(()=>p?{type:"eip155",networkID:r,unsignedTransaction:p}:void 0,[r,p]),{data:se}=z(F),{transactionSpeed:me,openGasSettings:E}=Fe(r,se),{data:l,isFetched:U,isError:k,isSuccess:pe}=O({networkID:r,transactionSpeed:me,multichainTransaction:F,queryOptions:{refetchInterval:!1}}),{data:x}=j(r,l),ce=x?.gasEstimationPriceUSD?t("gasUpTo",{amount:x?.gasEstimationPriceUSD}):void 0,de=B(l),y=ce??de??"",{hasSufficientFunds:v,nativeTokenSymbol:A}=q(r,d?.addressType,l,new M(a??"0",16)),D=U&&!pe,S=(0,e.useMemo)(()=>k?t("transactionGasLimitError"):D?t("transactionGasEstimationError"):v==="insufficient"?t("transactionNotEnoughNative",{nativeTokenSymbol:A}):"",[k,D,v,t,A]),ue=(0,e.useMemo)(()=>[{label:t("commandSend"),value:`+${a} ${n.symbol}`,color:"#21E56F"},{label:t("commandReceive"),value:`-${a} ${m}`,color:"#EB3742"},{label:u.networkFeeText,onClick:E,value:y.length>0?y:e.default.createElement(_,{width:"75px",height:"8px",borderRadius:"8px",backgroundColor:"#484848"}),color:S?"#EB3742":"#FFF",leftSubtext:S,subtextColor:"#EB3742",tooltipContent:u.networkFeeTextDescription}],[a,n.symbol,u.networkFeeText,u.networkFeeTextDescription,y,S,E,m,t]),{mutateAsync:C,isPending:le}=J(re),we=(0,e.useCallback)(()=>{C({fungible:o,unsignedUnwrapTx:p,gasEstimation:l}),i("approveUnwrapFungible"),s("/notifications")},[o,l,i,s,p,C]),ge=(0,e.useCallback)(()=>{i("approveUnwrapFungible")},[i]);return{isLoading:ie||!U,isSwapDisabled:le||!p||!!S,logoUri:g,summaryRows:ue,i18nStrings:u,onConfirmSwap:we,onDenySwap:ge}},Ue=({fungible:o})=>{if(b.isEthereumNetworkID(o.data.chain.id)){let t=Ee(o);return e.default.createElement(ke,{...t})}throw new Error(`Unwrap fungible not supported for chain: ${o.data.chain.id}`)},et=Ue,ke=e.default.memo(({isLoading:o,isSwapDisabled:t,logoUri:s,summaryRows:i,i18nStrings:n,onConfirmSwap:c,onDenySwap:a})=>e.default.createElement(Te,null,e.default.createElement(Z,null,n.pageHeaderText),e.default.createElement(ye,null,e.default.createElement(be,null,e.default.createElement(R,{image:{type:"icon",preset:"swap"},size:64,badge:{src:s??""}}),e.default.createElement(he,null,n.swapTitle)),e.default.createElement(ee,{rows:i,borderRadius:"6px 6px 6px 6px",padding:16,fontSize:16}),e.default.createElement(Q,{mode:"wait",initial:!1},e.default.createElement(W,{primaryText:n.confirmSwap,secondaryText:n.cancelSwap,primaryDisabled:o||t,onPrimaryClicked:c,onSecondaryClicked:a})))));export{Ue as ApproveUnwrapFungible,et as default};
//# sourceMappingURL=ApproveUnwrapFungible-QNBQNSBC.js.map
