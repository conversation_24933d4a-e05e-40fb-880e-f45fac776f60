{"commandAdd": "<PERSON><PERSON>", "commandAccept": "Kabul Et", "commandApply": "<PERSON><PERSON><PERSON><PERSON>", "commandApprove": "<PERSON><PERSON><PERSON>", "commandAllow": "<PERSON><PERSON> V<PERSON>", "commandBack": "<PERSON><PERSON>", "commandBuy": "Satın Al", "commandCancel": "İptal et", "commandClaim": "Al", "commandClaimReward": "Ödülünüzü alın", "commandClear": "<PERSON><PERSON><PERSON>", "commandClose": "Ka<PERSON><PERSON>", "commandConfirm": "<PERSON><PERSON><PERSON>", "commandConnect": "Bağla", "commandContinue": "<PERSON><PERSON> et", "commandConvert": "Dönüş<PERSON>ür", "commandCopy": "Kopyala", "commandCopyAddress": "<PERSON><PERSON><PERSON>", "commandCopyTokenAddress": "<PERSON><PERSON> adresini k<PERSON>", "commandCreate": "Oluştur", "commandCreateTicket": "<PERSON><PERSON>t o<PERSON>ş<PERSON>", "commandDeny": "<PERSON><PERSON>", "commandDismiss": "Ka<PERSON><PERSON>", "commandDontAllow": "<PERSON>zin Verme", "commandDownload": "<PERSON><PERSON><PERSON>", "commandEdit": "<PERSON><PERSON><PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON>", "commandEnableNow": "<PERSON><PERSON>", "commandFilter": "Filtrele", "commandFollow": "Ta<PERSON><PERSON> et", "commandHelp": "Yardım", "commandLearnMore": "<PERSON><PERSON> fazla bilgi", "commandLearnMore2": "<PERSON><PERSON>", "commandMint": "Mint et", "commandMore": "<PERSON><PERSON> fazla", "commandNext": "<PERSON><PERSON><PERSON>", "commandNotNow": "<PERSON><PERSON><PERSON>", "commandOpen": "Aç", "commandOpenSettings": "Ayarlar'ı aç", "commandPaste": "Yapıştır", "commandReceive": "Al", "commandReconnect": "<PERSON><PERSON><PERSON> b<PERSON>", "commandRecordVideo": "Video kaydet", "commandRequest": "İstek gönder", "commandRetry": "<PERSON><PERSON><PERSON> dene", "commandReview": "<PERSON><PERSON><PERSON>", "commandRevoke": "İptal et", "commandSave": "<PERSON><PERSON>", "commandScanQRCode": "QR Kodu tarayın", "commandSelect": "Seç", "commandSelectMedia": "<PERSON><PERSON><PERSON>", "commandSell": "Sat", "commandSend": "<PERSON><PERSON><PERSON>", "commandShare": "Paylaş", "commandShowBalance": "Bakiyeyi göster", "commandSign": "İmzala", "commandSignOut": "Sign Out", "commandStake": "Stake et", "commandMintLST": "JitoSOL mint'le", "commandSwap": "Ta<PERSON>", "commandSwapAgain": "<PERSON><PERSON><PERSON>", "commandTakePhoto": "Foto<PERSON><PERSON><PERSON>", "commandTryAgain": "<PERSON><PERSON><PERSON>", "commandViewTransaction": "İşlemi görü<PERSON>üle", "commandReportAsNotSpam": "<PERSON><PERSON> de<PERSON> o<PERSON> bildir", "commandReportAsSpam": "<PERSON>m olarak bildir", "commandPin": "<PERSON><PERSON><PERSON>", "commandBlock": "<PERSON><PERSON><PERSON>", "commandUnblock": "<PERSON><PERSON><PERSON> ka<PERSON>", "commandUnstake": "Stake'i kaldır", "commandUnpin": "Sabitlemeyi kaldır", "commandHide": "<PERSON><PERSON><PERSON>", "commandUnhide": "<PERSON><PERSON><PERSON>", "commandBurn": "Yak", "commandReport": "<PERSON><PERSON><PERSON>", "commandView": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandProceedAnywayUnsafe": "<PERSON><PERSON> (gü<PERSON><PERSON> değ<PERSON>)", "commandUnfollow": "<PERSON><PERSON><PERSON><PERSON>", "commandUnwrap": "Kaydır", "commandConfirmUnsafe": "<PERSON><PERSON><PERSON> (güvenli değil)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, onayla (güvenli değil)", "commandConfirmAnyway": "<PERSON><PERSON>", "commandReportIssue": "<PERSON><PERSON> bi<PERSON>", "commandSearch": "Ara", "commandShowMore": "<PERSON><PERSON> faz<PERSON>", "commandShowLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "pastParticipleClaimed": "Alındı", "pastParticipleCompleted": "Tamamlandı", "pastParticipleCopied": "Kopyalandı", "pastParticipleDone": "<PERSON><PERSON>", "pastParticipleDisabled": "<PERSON><PERSON> dı<PERSON>ı", "pastParticipleRequested": "İstek gönderildi", "nounName": "İsim", "nounNetwork": "Ağ", "nounNetworkFee": "<PERSON><PERSON>", "nounSymbol": "Sembol", "nounType": "<PERSON><PERSON><PERSON>", "nounDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "nounYes": "<PERSON><PERSON>", "nounNo": "Hay<PERSON><PERSON>", "amount": "<PERSON><PERSON><PERSON>", "limit": "Limit", "new": "<PERSON><PERSON>", "gotIt": "<PERSON><PERSON><PERSON><PERSON>", "internal": "<PERSON><PERSON><PERSON>", "reward": "<PERSON><PERSON><PERSON><PERSON>", "seeAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> bak", "seeLess": "<PERSON><PERSON> a<PERSON>ına bak", "viewAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "homeTab": "<PERSON><PERSON>", "collectiblesTab": "Koleksi<PERSON>luk<PERSON>", "swapTab": "Ta<PERSON>", "activityTab": "Aktivite", "exploreTab": "Keşfet", "accountHeaderConnectedInterpolated": "{{origin}} adres<PERSON> b<PERSON>", "accountHeaderConnectedToSite": "Bu siteye bağlısınız", "accountHeaderCopyToClipboard": "Panoya kopyalandı", "accountHeaderNotConnected": "Şuna bağlı değilsiniz:", "accountHeaderNotConnectedInterpolated": "Şuna bağlı değilsiniz: {{origin}}", "accountHeaderNotConnectedToSite": "Bu siteye bağlı değilsiniz", "accountWithoutEnoughSolActionButtonCancel": "İptal et", "accountWithoutEnoughSolPrimaryText": "Yeterli SOL yok", "accountWithoutEnoughSolSecondaryText": "Bu işleme dahil olan hesapta yeterli SOL yok. Hesap sizin veya başka birinin olabilir. Gönderilmesi durumunda bu işlem geri dö<PERSON>k.", "accountSwitcher": "Hesap Geçiş Aracı", "addAccountHardwareWalletPrimaryText": "Donanım Cüzdanı bağlayın", "addAccountHardwareWalletSecondaryText": "Ledger donanım cüzdanınızı kullanın", "addAccountHardwareWalletSecondaryTextMobile": "{{supportedHardwareWallets}} cüzdanınızı kullanın", "addAccountSeedVaultWalletPrimaryText": "<PERSON><PERSON> Ka<PERSON>ı<PERSON>lanın", "addAccountSeedVaultWalletSecondaryText": "<PERSON><PERSON>ından bir cüzdan kullanın", "addAccountImportSeedPhrasePrimaryText": "<PERSON><PERSON><PERSON> Kurtarma Tümceciğini içe aktarın", "addAccountImportSeedPhraseSecondaryText": "Başka bir cüzdandaki hesapları içe aktarın", "addAccountImportWalletPrimaryText": "Özel Anahtarı içe aktarın", "addAccountImportWalletSecondaryText": "Tek zincirli bir hesabı içe aktarın", "addAccountImportWalletSolanaSecondaryText": "Bir Solana özel anahtarını içe aktar", "addAccountLimitReachedText": "Phantom'da {{accountsCount}} hesap limitine ulaştınız. Lütfen ek hesap eklemeden önce kullanılmayan hesapları kaldırın.", "addAccountNoSeedAvailableText": "Tohum ifadeniz bulunmuyor. Hesap oluşturmak için lütfen mevcut bir tohumu içe aktarın.", "addAccountNewWalletPrimaryText": "<PERSON><PERSON>", "addAccountNewWalletSecondaryText": "<PERSON>ni bir cüzdan adresi oluşturun", "addAccountNewMultiChainWalletSecondaryText": "Yeni bir çok zincirli hesap e<PERSON>in", "addAccountNewSingleChainWalletSecondaryText": "<PERSON><PERSON> he<PERSON>p ekle", "addAccountPrimaryText": "Cüzdan Ekleyin / Bağlayın", "addAccountSecretPhraseLabel": "<PERSON><PERSON><PERSON>", "addAccountSeedLabel": "<PERSON><PERSON><PERSON><PERSON>", "addAccountSeedIDLabel": "<PERSON><PERSON><PERSON><PERSON>", "addAccountSecretPhraseDefaultLabel": "<PERSON><PERSON><PERSON> {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON> {{number}}", "addAccountZeroAccountsForSeed": "0 hesap", "addAccountShowAccountForSeed": "1 hesap göster", "addAccountShowAccountsForSeed": "{{numOfAccounts}} he<PERSON>p g<PERSON><PERSON>", "addAccountHideAccountForSeed": "1 hesap gizle", "addAccountHideAccountsForSeed": "{{numOfAccounts}} hesap gizle", "addAccountSelectSeedDescription": "<PERSON>ni hesa<PERSON>ınız bu Gizli Tümcecikten oluşturulacak", "addAccountNumAccountsForSeed": "{{numOfAccounts}} hesap", "addAccountOneAccountsForSeed": "1 hesap", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON><PERSON>", "addAccountReadOnly": "<PERSON><PERSON><PERSON>", "addAccountReadOnlySecondaryText": "Herkese açık cüzdan adreslerini takip edin", "addAccountSolanaAddress": "<PERSON><PERSON>", "addAccountEVMAddress": "EVM Adresi", "addAccountBitcoinAddress": "Bitcoin Adresi", "addAccountCreateSeedTitle": "<PERSON><PERSON> bir he<PERSON><PERSON>", "addAccountCreateSeedExplainer": "Henüz cüzdanınızda gizli tümcecik yok! Yeni bir cüzdan oluşturmak için size bir kurtarma tümceciği oluşturacağız. Bunu yazın ve kendinize saklayın.", "addAccountSecretPhraseHeader": "G<PERSON><PERSON> Tümceciğiniz", "addAccountNoSecretPhrases": "Gizli Tümcecik Mevcut Değil", "addAccountImportAccountActionButtonImport": "İçe aktarın", "addAccountImportAccountDuplicatePrivateKey": "Bu hesap cüzdanınızda zaten mevcut", "addAccountImportAccountIncorrectFormat": "Yanlış format", "addAccountImportAccountInvalidPrivateKey": "Geçersiz <PERSON>", "addAccountImportAccountName": "İsim", "addAccountImportAccountPrimaryText": "Özel Anahtarı İçe Aktarın", "addAccountImportAccountPrivateKey": "<PERSON><PERSON>", "addAccountImportAccountPublicKey": "Adres veya Etki Alanı", "addAccountImportAccountPrivateKeyRequired": "<PERSON><PERSON> an<PERSON> gere<PERSON>", "addAccountImportAccountNameRequired": "Ad gere<PERSON>", "addAccountImportAccountPublicKeyRequired": "Herkese açık adres gerekli", "addAccountImportAccountDuplicateAddress": "Bu adres cüzdanınızda zaten mevcut", "addAddressAddressAlreadyAdded": "<PERSON><PERSON> zaten eklendi", "addAddressAddressAlreadyExists": "<PERSON><PERSON> zaten mevcut", "addAddressAddressInvalid": "<PERSON><PERSON> g<PERSON><PERSON><PERSON>", "addAddressAddressIsRequired": "<PERSON><PERSON>", "addAddressAddressPlaceholder": "<PERSON><PERSON>", "addAddressLabelIsRequired": "Etiket gerekli", "addAddressLabelPlaceholder": "Etiket", "addAddressPrimaryText": "<PERSON><PERSON>", "addAddressToast": "<PERSON><PERSON>", "createAssociatedTokenAccountCostLabelInterpolated": "<PERSON><PERSON><PERSON>: {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Bu token hesabına zaten sa<PERSON>z", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON><PERSON><PERSON> fon", "createAssociatedTokenAccountErrorInvalidMint": "Geçersiz mint adresi", "createAssociatedTokenAccountErrorInvalidName": "Geçersiz ad", "createAssociatedTokenAccountErrorInvalidSymbol": "Geçersiz sembol", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Token hesabınız oluşturulamadı. Lütfen tekrar deneyin.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountErrorUnableToSendMessage": "İşleminiz gönderilemedi.", "createAssociatedTokenAccountErrorUnableToSendTitle": "İşlem gönderilemedi", "createAssociatedTokenAccountInputPlaceholderMint": "Mint Adresi", "createAssociatedTokenAccountInputPlaceholderName": "İsim", "createAssociatedTokenAccountInputPlaceholderSymbol": "Sembol", "createAssociatedTokenAccountLoadingMessage": "Token hesabınızı oluşturuyoruz.", "createAssociatedTokenAccountLoadingTitle": "Token hesabı oluşturuluyor", "createAssociatedTokenAccountPageHeader": "Token Hesabı oluştur", "createAssociatedTokenAccountSuccessMessage": "Token hesabınız başarıyla oluşturuldu!", "createAssociatedTokenAccountSuccessTitle": "Token hesabı oluşturuldu", "createAssociatedTokenAccountViewTransaction": "İşlemi görü<PERSON>üle", "assetDetailRecentActivity": "Son Aktivite", "assetDetailStakeSOL": "SOL stake edin", "assetDetailUnknownToken": "Bilinmeyen Token", "assetDetailUnwrapAll": "Tümünün Sargısını Aç", "assetDetailUnwrappingSOL": "SOL kaydırılıyor", "assetDetailUnwrappingSOLFailed": "SOL kaydırması başarısız", "assetDetailViewOnExplorer": "{{explorer}}'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assetDetailViewOnExplorerDefaultExplorer": "<PERSON><PERSON><PERSON>", "assetDetailSaveToPhotos": "Fotoğraflar'a kaydet", "assetDetailSaveToPhotosToast": "Fotoğraf<PERSON><PERSON>", "assetDetailPinCollection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "assetDetailUnpinCollection": "Koleksiyonun sabitlemesini kaldır", "assetDetailHideCollection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> gizle", "assetDetailUnhideCollection": "Koleksi<PERSON><PERSON>", "assetDetailTokenNameLabel": "Token Adı", "assetDetailNetworkLabel": "Ağ", "assetDetailAddressLabel": "<PERSON><PERSON>", "assetDetailPriceLabel": "<PERSON><PERSON><PERSON>", "collectibleDetailSetAsAvatar": "Ava<PERSON> o<PERSON> a<PERSON>", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar seti", "collectibleDetailShare": "Koleksiyon<PERSON><PERSON><PERSON>", "assetDetailTokenAddressCopied": "<PERSON><PERSON> k<PERSON>alandı", "assetDetailStakingLabel": "Stake etme", "assetDetailAboutLabel": "{{fungibleName}} hakkında", "assetDetailPriceDetail": "<PERSON><PERSON><PERSON>", "assetDetailHighlights": "<PERSON><PERSON>", "assetDetailAllTimeReturn": "<PERSON><PERSON><PERSON>", "assetDetailAverageCost": "Ortalama Maliyet", "assetDetailPriceHistoryUnavailable": "Bu token için fiyat geçmişi kullanılamıyor", "assetDetailPriceHistoryInsufficientData": "Bu zaman aralığı için fiyat geçmişi mevcut değil", "assetDetailPriceDataUnavailable": "Fiyat verisi mevcut değil", "assetDetailPriceHistoryError": "Fiyat geçmişi getirilemedi", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1 G", "assetDetailTimeFrame24h": "24 sa Fiyatı", "assetDetailTimeFrame1W": "1 H", "assetDetailTimeFrame1M": "1 A", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "TÜMÜ", "sendAssetAmountLabelInterpolated": "Kullanılabilir {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fiatRampNewQuote": "<PERSON><PERSON>", "assetListSelectToken": "<PERSON><PERSON> seçin", "assetListSearch": "Ara...", "assetListUnknownToken": "Bilinmeyen Token", "buyFlowHealthWarning": "Bazı ödeme sağlayıcılarımız yoğun trafik yaşıyor. Yatırma işlemleri birkaç saat gecikebilir.", "assetVisibilityUnknownToken": "Bilinmeyen Token", "buyAssetInterpolated": "{{tokenSymbol}} Satın Al", "buyAssetScreenMaxPurchasePriceInterpolated": "<PERSON><PERSON><PERSON><PERSON> satın alma i<PERSON>i {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "<PERSON><PERSON><PERSON><PERSON> satın alma i<PERSON>i {{amount}}", "buyNoAssetsAvailable": "Ethereum veya Polygon varlıkları kullanılamıyor", "buyThirdPartyScreenPaymentMethodSelector": "Ödeme aracı:", "buyThirdPartyScreenPaymentMethod": "<PERSON><PERSON><PERSON> y<PERSON>", "buyThirdPartyScreenChoseQuote": "<PERSON><PERSON>t tek<PERSON><PERSON> i<PERSON> ge<PERSON>erli bir tutar girin", "buyThirdPartyScreenProviders": "Sağlayıcılar", "buyThirdPartyScreenPaymentMethodTitle": "<PERSON><PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodEmptyState": "Bölgenizde kullanılabilir ödeme yöntemi bulunmuyor", "buyThirdPartyScreenPaymentMethodFooter": "Ödemeler ağ iş ortakları tarafından sağlanır. Ücretler değişiklik gösterebilir. Bazı ödeme yöntemleri bölgenizde kullanılamıyor.", "buyThirdPartyScreenProvidersEmptyState": "Bölgenizde kullanılabilir sağlayıcı bulunmuyor", "buyThirdPartyScreenLoadingQuote": "<PERSON>yat te<PERSON> yü<PERSON>niyor...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON><PERSON><PERSON>", "gasEstimationErrorWarning": "Bu işlem için ücret tahmin edilirken bir sorun yaşandı. Başarısız olabilir.", "gasEstimationCouldNotFetch": "<PERSON><PERSON> tahm<PERSON>", "networkFeeCouldNotFetch": "<PERSON><PERSON> ücreti getiri<PERSON>edi", "nativeTokenBalanceErrorWarning": "Bu işlem için token bakiyeniz alınırken bir sorun yaşandı. Başarısız olabilir.", "blocklistOriginCommunityDatabaseInterpolated": "Bu site, bilinen oltalama web siteleri ve dolandırıcılıkları içeren <1>topluluk tarafından oluşturulmuş veri tabanının</1> bir parçası olarak işaretlenmiş. Bu sitenin hatayla işaretlendiğini düşünüyorsanız <3>lütfen sorunu bildirin</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} engellendi!", "blocklistOriginIgnoreWarning": "Bu uyarıyı yok say, beni yine de {{domainName}} adresine g<PERSON>ü<PERSON>.", "blocklistOriginSiteIsMalicious": "Phantom, bu web sitesinin kötü niyetli olduğuna ve kullanım için güvenli olmadığına inanıyor.", "blocklistOriginThisDomain": "bu alan adı", "blocklistProceedAnyway": "Uyarıyı yok say, yine de ilerle", "maliciousTransactionWarning": "Phantom, bu i<PERSON><PERSON><PERSON> kötü niyetli olduğuna ve giriş yapmak için güvenli olmadığına inanıyor. Sizi ve fonlarınızı korumak için bunun imzalanabilirliğini devre dışı bıraktık.", "maliciousTransactionWarningIgnoreWarning": "Uyarıyı yok say, yine de ilerle", "maliciousTransactionWarningTitle": "İşlem işaretlendi!", "maliciousRequestBlockedTitle": "İstek engellendi", "maliciousRequestWarning": "Bu web sitesi kötü niyetli olarak işaretlenmiş. Fonlarınızı çalmaya veya yanıltıcı bir isteği onaylamanız için sizi kandırmaya çalışıyor olabilir.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> için <PERSON>, bu <PERSON><PERSON> engelledi.", "maliciousRequestBlocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> için <PERSON>, bu <PERSON><PERSON> engelledi.", "maliciousRequestFrictionDescription": "İlerlemek güven<PERSON>, dolayısıyla Phantom bu talebi engelledi. Bu diyaloğu kapatmalısınız.", "maliciousRequestAcknowledge": "Bu web sitesini kullanarak bütün fonlarımı kaybedebileceğimi anlıyorum.", "maliciousRequestAreYouSure": "Emin misiniz?", "siwErrorPopupTitle": "Geçersiz İmza İsteği", "siwParseErrorDescription": "Uygulamanın imza talebi geçersiz format nedeniyle gösterilemiyor.", "siwVerificationErrorDescription": "Mesaj imza talebiyle ilgili 1 veya daha fazla hata oluştu. Güvenliğiniz için lütfen doğru uygulamayı kullandığınızdan emin olun ve tekrar deneyin.", "siwErrorPagination": "{{n}} / {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Uyarı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> <PERSON><PERSON><PERSON> ad<PERSON> uyuşmuyor.", "siwErrorMessage_DOMAIN_MISMATCH": "Uyarı: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> alan ad<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> sağlanan alan adıyla eşleşmiyor.", "siwErrorMessage_URI_MISMATCH": "Uyarı: <PERSON><PERSON>, al<PERSON> ad<PERSON>.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Uyarı: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>n zincir kimliğiyle eşleşmiyor.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Uyarı: <PERSON>j <PERSON>turma tarihi geçmişte çok eski bir tarih.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Uyarı: <PERSON><PERSON> tarihi gelecekte çok ileri bir tarih.", "siwErrorMessage_EXPIRED": "Uyarı: <PERSON><PERSON><PERSON><PERSON> sü<PERSON>i doldu.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Uyarı: Mesajın süresi oluşturulmadan önce doluyor.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Uyarı: <PERSON><PERSON><PERSON>n süresi geçerli olmadan doluyor.", "siwErrorShowErrorDetails": "Hata bilgilerini g<PERSON>", "siwErrorHideErrorDetails": "Hata bilgi<PERSON>ini gizle", "siwErrorIgnoreWarning": "Uyarıyı yok say, yine de ilerle", "siwsTitle": "<PERSON><PERSON><PERSON> İsteği", "siwsPermissions": "<PERSON><PERSON><PERSON>", "siwsAgreement": "<PERSON><PERSON>", "siwsAdvancedDetails": "Gelişmiş Ayrıntılar", "siwsAlternateStatement": "{{domain}}, <PERSON><PERSON>ızla oturum açmanızı istiyor:\n{{address}}", "siwsFieldLable_domain": "E<PERSON><PERSON>", "siwsFieldLable_address": "<PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Versiyon", "siwsFieldLable_chainId": "<PERSON><PERSON><PERSON><PERSON>", "siwsFieldLable_nonce": "Tel Seferlik Anahtar", "siwsFieldLable_issuedAt": "Hazırlanma Zamanı:", "siwsFieldLable_expirationTime": "Son <PERSON><PERSON>:", "siwsFieldLable_requestId": "İstek Kimliği", "siwsFieldLable_resources": "<PERSON><PERSON><PERSON><PERSON>", "siwsVerificationErrorDescription": "Bu giriş isteği geçersiz. Bu, sitenin güvenli olmadığı ya da geliştiricisinin isteği gönderirken bir hata yaptığı anlamına gelir.", "siwsErrorNumIssues": "{{n}} sorun", "siwsErrorMessage_CHAIN_ID_MISMATCH": "<PERSON><PERSON> <PERSON> k<PERSON>, bulunduğunuz ağla eşleşmiyor.", "siwsErrorMessage_DOMAIN_MISMATCH": "<PERSON>u etki alanı, giri<PERSON>ınız etki alanı değ<PERSON>.", "siwsErrorMessage_URI_MISMATCH": "<PERSON>u URI, g<PERSON><PERSON>ptığınız URI değil.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Mesaj oluşturma tarihi geçmişte çok eski bir tarih.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "<PERSON>j <PERSON>şturma tarihi gelecekte çok uzak bir tarih.", "siwsErrorMessage_EXPIRED": "<PERSON><PERSON><PERSON>n s<PERSON>i doldu.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Mesajın süresi oluşturulmadan önce doluyor.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Mesajın süresi geçerli olmadan dolacak.", "changeLockTimerPrimaryText": "Otomatik Kilitleme Sayacı", "changeLockTimerSecondaryText": "Cüzdanın<PERSON>z boştayken onu kilitlemek için ne kadar beklemeliyiz?", "changeLockTimerToast": "Otomatik kilitleme sayacı gü<PERSON>llendi", "changePasswordConfirmNewPassword": "<PERSON><PERSON>", "changePasswordCurrentPassword": "<PERSON><PERSON><PERSON>", "changePasswordErrorIncorrectCurrentPassword": "Mevcut şifre ya<PERSON>ı<PERSON>", "changePasswordErrorGeneric": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ters gitti, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "changePasswordNewPassword": "<PERSON><PERSON>", "changePasswordPrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "changePasswordToast": "<PERSON><PERSON><PERSON>", "collectionsSpamCollections": "Spam <PERSON>ı", "collectionsHiddenCollections": "Gizlenen <PERSON>", "collectiblesReportAsSpam": "<PERSON>m olarak bildir", "collectiblesReportAsSpamAndHide": "<PERSON><PERSON> o<PERSON>ildir ve Gizle", "collectiblesReportAsNotSpam": "<PERSON><PERSON> o<PERSON> bildir", "collectiblesReportAsNotSpamAndUnhide": "Gizlemeyi kaldır ve spam olmadığını bildir", "collectiblesReportNotSpam": "<PERSON><PERSON>", "collectionsManageCollectibles": "Koleksiyonluk listesini yönetin", "collectibleDetailDescription": "<PERSON><PERSON>ı<PERSON><PERSON>", "collectibleDetailProperties": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailOrdinalInfo": "Ordinal Bilgileri", "collectibleDetailRareSatsInfo": "<PERSON><PERSON>", "collectibleDetailSatsInUtxo": "UTXO'da sat'lar", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sat", "collectibleDetailSatNumber": "Sat Numarası", "collectibleDetailSatName": "Sat Adı", "collectibleDetailInscriptionId": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailInscriptionNumber": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailStandard": "<PERSON><PERSON>", "collectibleDetailCreated": "Oluşturulma:", "collectibleDetailViewOnExplorer": "{{explorer}}'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailList": "Listele", "collectibleDetailSellNow": "{{amount}} {{symbol}} <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sat", "collectibleDetailUtxoSplitterCtaTitle": "<PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailUtxoSplitterCtaSubtitle": "Kilidini açabileceğiniz {{value}} BTC'niz var", "collectibleDetailUtxoSplitterModalCtaTitle": "<PERSON><PERSON>", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Fonlarınızı korumak için Nadir Sat'lı UTXO'lerdeki BTC'nin gönderilmesini engelliyoruz. Nadir Sat'larınızdan {{value}} BTC boşaltmak için Magic Eden'ın UTXO ayırıcısını kullanın.", "collectibleDetailUtxoSplitterModalCtaButton": "UTXO Ayırıcı kullanın", "collectibleDetailEasilyAccept": "En yüksek teklifi kabul et", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sat", "collectibleDetailSpamOverlayDescription": "<PERSON><PERSON>, Phantom spam <PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> giz<PERSON>.", "collectibleDetailSpamOverlayReveal": "Koleksiyonlu<PERSON><PERSON> gö<PERSON>", "collectibleBurnTermsOfService": "Bu i<PERSON><PERSON>in geri alınamayacağını anlıyorum", "collectibleBurnTitleWithCount_one": "Token'ı yak", "collectibleBurnTitleWithCount_other": "Token'ları yak", "collectibleBurnDescriptionWithCount_one": "Bu eylem bu token'ı cüzdanınızdan kalıcı olarak yok eder ve kaldırır.", "collectibleBurnDescriptionWithCount_other": "Bu eylem bu token'ı cüzdanınızdan kalıcı olarak yok eder ve kaldırır.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "To<PERSON>'lar", "collectibleBurnCta": "Yak", "collectibleBurnRebate": "<PERSON><PERSON> yap", "collectibleBurnRebateTooltip": "Bu token'ı yaktığınız için cüzdanınıza az miktarda SOL otomatik olarak yatırılır.", "collectibleBurnNetworkFee": "<PERSON><PERSON>", "collectibleBurnNetworkFeeTooltip": "Solana ağının işlemi işlemek için gerektirdiği ücret", "unwrapButtonSwapTo": "{{chainSymbol}} ile takas et", "unwrapButtonWithdrawFrom": "{{chainSymbol}} i<PERSON><PERSON>: {{withdrawalSource}}", "unwrapModalEstimatedTime": "<PERSON><PERSON><PERSON>", "unwrapModalNetwork": "Ağ", "unwrapModalNetworkFee": "<PERSON><PERSON>", "unwrapModalTitle": "Özet", "unsupportedChain": "Desteklenmeyen Zincir", "unsupportedChainDescription": "Görünüşe göre {{chainName}} ağı için {{action}} eylemini desteklemiyoruz.", "networkFeesTooltipLabel": "{{chainName}} <PERSON><PERSON>", "networkFeesTooltipDescription": "{{chainName}} ücretleri birkaç faktöre göre değişiklik göstermektedir. Bunları işleminizi hızlandırmak (daha pahalı) veya yavaşlatmak (daha ucuz) için özelleştirebilirsiniz.", "burnStatusErrorTitleWithCount_one": "Token yakılamadı", "burnStatusErrorTitleWithCount_other": "Token'lar ya<PERSON>ı", "burnStatusSuccessTitleWithCount_one": "Token yakıldı!", "burnStatusSuccessTitleWithCount_other": "Token'lar yakıldı!", "burnStatusLoadingTitleWithCount_one": "Token yakılıyor...", "burnStatusLoadingTitleWithCount_other": "Token'lar yakılıyor...", "burnStatusErrorMessageWithCount_one": "Bu token yakılamadı. Lütfen daha sonra tekrar deneyin.", "burnStatusErrorMessageWithCount_other": "Bu <PERSON>'lar yakı<PERSON>. Lütfen daha sonra tekrar deneyin.", "burnStatusSuccessMessageWithCount_one": "Bu token kalıcı olarak yok edildi ve {{rebateAmount}} SOL cüzdanınıza yatırıldı.", "burnStatusSuccessMessageWithCount_other": "Bu token'lar kalıcı olarak yok edildi ve {{rebateAmount}} SOL cüzdanınıza yatırıldı.", "burnStatusLoadingMessageWithCount_one": "Bu token kalıcı olarak yok edildi ve {{rebateAmount}} SOL cüzdanınıza yatırılacak.", "burnStatusLoadingMessageWithCount_other": "Bu token'lar kalıcı olarak yok ediliyor ve {{rebateAmount}} SOL cüzdanınıza yatırıldı.", "burnStatusViewTransactionText": "İşlemi görü<PERSON>üle", "collectibleDisplayLoading": "Yükleniyor...", "collectiblesNoCollectibles": "Koleksiyonluk yok", "collectiblesPrimaryText": "Koleksiyonluklarınız", "collectiblesReceiveCollectible": "Koleksiyon<PERSON><PERSON> alın", "collectiblesUnknownCollection": "Bilinmeyen Koleksiyon", "collectiblesUnknownCollectible": "Bilinmeyen Koleksiyonluk", "collectiblesUniqueHolders": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSupply": "<PERSON><PERSON><PERSON>", "collectiblesUnknownTokens": "Bilinmeyen To<PERSON>'lar", "collectiblesNrOfListed": "{{ nrOfListed }} Listelendi", "collectiblesListed": "Listelendi", "collectiblesMintCollectible": "Koleksiyonluk Mint", "collectiblesYouMint": "Mint ediyorsunuz", "collectiblesMintCost": "Mint etme <PERSON>ti", "collectiblesMintFail": "Mint başarısız", "collectiblesMintFailMessage": "Koleksiyonluğunuz mint edilirken bir sorun oluştu. Lütfen tekrar deneyin.", "collectiblesMintCostFree": "Ücretsiz", "collectiblesMinting": "Mint ediliyor...", "collectiblesMintingMessage": "Koleksiyonluğunuz mint ediliyor", "collectiblesMintShareSubject": "Şuna göz atın", "collectiblesMintShareMessage": "Bunu @phantom üzerinde mint ettim!", "collectiblesMintSuccess": "Mint başarılı", "collectiblesMintSuccessMessage": "Koleksiyonluğunuz mint edildi", "collectiblesMintSuccessQuestMessage": "Phantom Quest için gereklilikleri yerine getirdiniz. Ücretsiz koleksiyonluğunuzu edinmek için Ödülünüzü alın seçeneğine dokunun.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMintMaxLengthErrorMessage": "Maks. uzunluk aşıldı", "collectiblesMintSafelyDismiss": "<PERSON><PERSON> pencer<PERSON>i gü<PERSON> kapa<PERSON>bilirsiniz.", "collectiblesTrimmed": "Şu anda gösterilebilecek koleksiyonluk sayısı limitine ulaştık.", "collectiblesNonTransferable": "Transfer edilemez", "collectiblesNonTransferableYes": "<PERSON><PERSON>", "collectiblesSellOfferDetails": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSellYouSell": "Sattığınız", "collectiblesSellGotIt": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSellYouReceive": "Aldığınız", "collectiblesSellOffer": "<PERSON><PERSON><PERSON><PERSON> et", "collectiblesSoldCollectible": "Satılan Koleksiyonluk", "collectiblesSellMarketplace": "<PERSON><PERSON>", "collectiblesSellCollectionFloor": "Koleksiyon Katı", "collectiblesSellDifferenceFromFloor": "<PERSON><PERSON> fark", "collectiblesSellLastSalePrice": "<PERSON>", "collectiblesSellEstimatedFees": "<PERSON><PERSON><PERSON>", "collectiblesSellEstimatedProfitAndLoss": "<PERSON><PERSON><PERSON>/Zarar", "collectiblesSellViewOnMarketplace": "{{marketplace}} üzerind<PERSON> görü<PERSON>in", "collectiblesSellCollectionFloorTooltip": "Birden fazla pazar yerinde koleksiyondaki en düşük \"Hemen Satın Al\" fiyatı.", "collectiblesSellProfitLossTooltip": "<PERSON><PERSON><PERSON>/<PERSON><PERSON><PERSON>, son in<PERSON><PERSON><PERSON> fiyat ve teklif fiyatı eksi ücretlere göre hesaplanır.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "<PERSON><PERSON> ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "<PERSON><PERSON> ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "<PERSON><PERSON>", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}} Ağ<PERSON>", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "<PERSON><PERSON><PERSON><PERSON>, {{phantomFeePercentage}} Phantom ü<PERSON><PERSON><PERSON>erir", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON> ve {{phantomFeePercentage}} Phantom ücretini de içerir", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "<PERSON><PERSON><PERSON><PERSON>; <PERSON><PERSON>, Ağ Ücretini ve Pazar Yeri Ücretini içerir", "collectiblesSellTransactionFeeTooltipTitle": "İşlem Ücreti", "collectiblesSellStatusLoadingTitle": "<PERSON><PERSON><PERSON><PERSON> kabul ediliyor...", "collectiblesSellStatusLoadingIsSellingFor": "<PERSON>u kadara satıyor:", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} Satıldı!", "collectiblesSellStatusSuccessWasSold": "şu kadara başar<PERSON><PERSON> satıldı:", "collectiblesSellStatusErrorTitle": "Bir <PERSON>ler Ters Gitti", "collectiblesSellStatusErrorSubtitle": "Satma denem<PERSON> sı<PERSON>ında bir hata oluş<PERSON>", "collectiblesSellStatusViewTransaction": "İşlemi Görüntüle", "collectiblesSellInsufficientFundsTitle": "<PERSON><PERSON><PERSON> fon", "collectiblesSellInsufficientFundsSubtitle": "Ağ ücretini ödemek için yetersiz fon olduğundan bu koleksiyonluktaki bir teklifi kabul edemedik.", "collectiblesSellRecentlyTransferedNFTTitle": "Kısa süre önce transfer edildi", "collectiblesSellRecentlyTransferedNFTSubtitle": "Transfer sonrasında teklifleri kabul etmek için 1 saat beklemelisiniz.", "collectiblesApproveCollection": "Onaylandı {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSellNotAvailableAnymoreSubtitle": "Teklif artık kullanılamıyor. Bu teklifi iptal edip tekrar deneyin", "collectiblesSellFlaggedTokenTitle": "Koleksiyonluklar işaretlenmiş", "collectiblesSellFlaggedTokenSubtitle": "Koleksiyonluk takas edilemiyor, çalınmış olarak bildirilmiş olması veya kilitlenmeden stake edilmiş olması gibi bunun birden fazla nedeni olabilir", "collectiblesListOnMagicEden": "Magic Eden'da listele", "collectiblesListPrice": "Liste Fiyatı", "collectiblesUseFloor": "Tabanı Kullan", "collectiblesFloorPrice": "Taban Fiyatı", "collectiblesLastSalePrice": "Son Satış Fiyatı", "collectiblesTotalReturn": "Toplam Getiri", "collectiblesOriginalPurchasePrice": "Orijinal Satın Alma Fiyatı", "collectiblesMagicEdenFee": "<PERSON>", "collectiblesArtistRoyalties": "Sanatçı Telif Ücretleri", "collectiblesListNowButton": "<PERSON><PERSON>", "collectiblesListAnywayButton": "<PERSON><PERSON>", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "Listelemeyi <PERSON>", "collectiblesListingViewTransaction": "İşlemi Görüntüle", "collectiblesRemoveListing": "Listelemeyi kaldır", "collectiblesEditListing": "Listelemeyi <PERSON>", "collectiblesEditListPrice": "Liste Fiyatını düzenle", "collectiblesListPriceTooltip": "Liste Fiyatı, bir <PERSON><PERSON>in satış fiyatıdır. Satıcılar genelde Liste Fiyatını, Taban Fiyat veya üzeri olarak belirlerler.", "collectiblesFloorPriceTooltip": "<PERSON><PERSON>, bu koleksiyondaki bir öge için en düşük aktif Liste Fiyatıdır.", "collectiblesOriginalPurchasePriceTooltip": "Bu ögeyi bu fiyattan satın almıştınız.", "collectiblesPurchasedForSol": "{{lastPurchasePrice}} SOL karşılığında satın alındı", "collectiblesUnableToLoadListings": "<PERSON>elemeler y<PERSON>miyor", "collectiblesUnableToLoadListingsFrom": "{{marketplace}} üzerindeki listelemeler yüklenemiyor", "collectiblesUnableToLoadListingsDescription": "Listelemeleriniz ve varlıklarınız <PERSON>ü<PERSON>de, ancak şu anda onları {{marketplace}} üzerinden yükleyemiyoruz. Lütfen daha sonra tekrar deneyin.", "collectiblesBelowFloorPrice": "Taban Fiyatın <PERSON>", "collectiblesBelowFloorPriceMessage": "NFT'nizi taban fiyatın altında listelemek istediğinizden emin misiniz?", "collectiblesMinimumListingPrice": "Minimum fiyat 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden, ta<PERSON><PERSON><PERSON><PERSON> i<PERSON> için ü<PERSON>t alır.", "collectiblesArtistRoyaltiesTooltip": "<PERSON><PERSON> koleksi<PERSON>u o<PERSON> kişi, ta<PERSON><PERSON><PERSON>n her satış için % ücret alır.", "collectibleScreenCollectionLabel": "Koleksiyon", "collectibleScreenPhotosPermissionTitle": "Fotoğraf <PERSON>", "collectibleScreenPhotosPermissionMessage": "Fotoğraflarınıza erişmek için izninize ihtiyacımız var. Lütfen Ayarlar'a gidin ve izinlerinizi güncelleyin.", "collectibleScreenPhotosPermissionOpenSettings": "Ayarlar'ı aç", "listStatusErrorTitle": "Listeleme Başarısız", "editListStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "removeListStatusErrorTitle": "<PERSON>eleme Kaldırılamadı", "listStatusSuccessTitle": "Listeleme Oluşturuldu!", "editListingStatusSuccessTitle": "Listeleme <PERSON>llendi!", "removeListStatusSuccessTitle": "Listeleme Magic Eden'dan kaldırıldı", "listStatusLoadingTitle": "<PERSON><PERSON>me oluşturuluyor...", "editListingStatusLoadingTitle": "<PERSON><PERSON><PERSON> g<PERSON>...", "removeListStatusLoadingTitle": "Listeleme kaldırılıyor...", "listStatusErrorMessage": "{{name}} Magic Eden'da listelenemiyor", "removeListStatusErrorMessage": "{{name}} listelemesi Magic Eden'dan kaldır<PERSON>ıyor", "listStatusSuccessMessage": "{{name}}, {{listCollectiblePrice}} SOL karşılığında Magic Eden'da listelendi", "editListingStatusSuccessMessage": "{{name}}, {{editListCollectiblePrice}} SOL karşılığında Magic Eden'da güncellendi", "removeListStatusSuccessMessage": "{{name}} Magic Eden'dan ba<PERSON><PERSON><PERSON><PERSON> kaldırıldı", "listStatusLoadingMessage": "{{name}}, {{listCollectiblePrice}} SOL karşılığında Magic Eden'da listeleniyor.", "editListingStatusLoadingMessage": "{{name}}, {{editListCollectiblePrice}} SOL karşılığında Magic Eden'da güncelleniyor.", "removeListStatusLoadingMessage": "{{name}}, Magic Eden'dan kaldırılıyor. Bu biraz zaman alabilir.", "listStatusLoadingSafelyDismiss": "<PERSON><PERSON> pencer<PERSON>i gü<PERSON> kapa<PERSON>bilirsiniz.", "listStatusViewOnMagicEden": "Magic Eden'da g<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "listStatusViewOnMarketplace": "{{marketplace}} üzerind<PERSON> görü<PERSON>in", "listStatusLoadingDismiss": "Artık <PERSON>", "listStatusViewTransaction": "İşlemi Görüntüle", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Donanım cüzdanınıza bağlanın ve kilidinin açık olduğundan emin olun. Cüzdanı tespit ettiğimizde hangi adresi kullanmak istediğinizi seçebilirsiniz.", "connectHardwareFailedPrimaryText": "Bağlantı başarısız", "connectHardwareFailedSecondaryText": "Lütfen donanım cüzdanınıza bağlanın ve kilidinin açık olduğundan emin olun. Cüzdanı keşfettiğimizde hangi adresi kullanmak istediğinizi seçebilirsiniz.", "connectHardwareFinishPrimaryText": "<PERSON><PERSON><PERSON>!", "connectHardwareFinishSecondaryText": "Artık Phantom dahilinde Ledger Nano cüzdanınıza erişebilirsiniz. Lütfen uzantıya geri dönün.", "connectHardwareNeedsPermissionPrimaryText": "<PERSON>ni bir cüzdana bağlanın", "connectHardwareNeedsPermissionSecondaryText": "Bağlantı işlemini başlatmak için aşağıdaki düğmeye tıklayın.", "connectHardwareSearchingPrimaryText": "Cüzdan aranıyor...", "connectHardwareSearchingSecondaryText": "Donanım cüzdanınıza ba<PERSON>, kilidinin açık olduğundan ve tarayıcınızdan izinleri onayladığınızdan emin olun.", "connectHardwarePermissionDeniedPrimary": "<PERSON>zin reddedildi", "connectHardwarePermissionDeniedSecondary": "Phantom'ın Ledger cihazınıza bağlanmasına izin verin", "connectHardwarePermissionUnableToConnect": "Bağlanamıyor", "connectHardwarePermissionUnableToConnectDescription": "Ledger cihazınıza bağlanamadık. Daha fazla izne ihtiyacımız olabilir.", "connectHardwareSelectAddressAllAddressesImported": "Bütün ad<PERSON>ler içe aktarıldı", "connectHardwareSelectAddressDerivationPath": "<PERSON><PERSON><PERSON><PERSON> yolu", "connectHardwareSelectAddressSearching": "Aranıyor...", "connectHardwareSelectAddressSelectWalletAddress": "Cüzdan adresi se<PERSON>", "connectHardwareSelectAddressWalletAddress": "Cüzdan adresi", "connectHardwareWaitingForApplicationSecondaryText": "Lütfen donanım cüzdanınıza bağlanın ve kilidinin açık olduğundan emin olun.", "connectHardwareWaitingForPermissionPrimaryText": "<PERSON><PERSON> gere<PERSON>", "connectHardwareWaitingForPermissionSecondaryText": "Donanım cüzdanınıza ba<PERSON>, kilidinin açık olduğundan ve tarayıcınızdan izinleri onayladığınızdan emin olun.", "connectHardwareAddAccountButton": "<PERSON><PERSON><PERSON>", "connectHardwareLedger": "Ledger'ınızı bağlayın", "connectHardwareStartConnection": "Ledger donanım cüzdanınızı bağlamaya başlamak için aşağıdaki düğmeye tıklayın", "connectHardwarePairSuccessPrimary": "{{productName}} bağlı", "connectHardwarePairSuccessSecondary": "{{productName}} adlı ürününüzü başarıyla bağladınız.", "connectHardwareSelectChains": "Bağlanacak zincirleri seçin", "connectHardwareSearching": "Aranıyor...", "connectHardwareMakeSureConnected": "Donanım cüzdanınızı bağlayın ve kilidini açın. Lütfen ilgili tarayıcı izinlerini onaylayın.", "connectHardwareOpenAppDescription": "Lütfen donanım cüzdanınızın kilidini açın", "connectHardwareConnecting": "Bağlanıyor...", "connectHardwareConnectingDescription": "Ledger cihazınıza bağlanıyoruz.", "connectHardwareConnectingAccounts": "Hesaplarınız bağlanıyor...", "connectHardwareDiscoveringAccounts": "Hesaplar aranıyor...", "connectHardwareDiscoveringAccountsDescription": "Hesaplarınızda aktivite arıyoruz.", "connectHardwareErrorLedgerLocked": "<PERSON><PERSON> kilitli", "connectHardwareErrorLedgerLockedDescription": "Ledger cihazınızın kilidinin açık olduğundan emin olup tekrar deneyin.", "connectHardwareErrorLedgerGeneric": "<PERSON><PERSON> ters gitti", "connectHardwareErrorLedgerGenericDescription": "Hesaplar bulunamıyor. Ledger cihazınızın kilidinin açık olduğundan emin olup tekrar deneyin.", "connectHardwareErrorLedgerPhantomLocked": "Lütfen Phantom'ı tekrar açın ve donanımınıza tekrar bağlanmayı deneyin.", "connectHardwareFindingAccountsWithActivity": "{{chainName}} hesapları bulunuyor...", "connectHardwareFindingAccountsWithActivityDualChain": "{{chainName1}} veya {{chainName2}} hesapları bulunuyor...", "connectHardwareFoundAccountsWithActivity": "Ledger'ınızda aktivite olan {{numOfAccounts}} he<PERSON>p bulduk.", "connectHardwareFoundAccountsWithActivitySingular": "Ledger'ınızda aktivite olan 1 hesap bulduk.", "connectHardwareFoundSomeAccounts": "Ledger cihazınızda birkaç hesap bulduk.", "connectHardwareViewAccounts": "Hesapları Görüntüle", "connectHardwareConnectAccounts": "Hesaplar bağlandı", "connectHardwareSelectAccounts": "Hesapları seçin", "connectHardwareChooseAccountsToConnect": "Bağlanacak cüzdan hesaplarını seçin.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} Hesap eklendi", "connectHardwareAccountsStepOfSteps": "{{stepNum}}/{{totalSteps}}. adım", "connectHardwareMobile": "<PERSON><PERSON> ba<PERSON><PERSON>", "connectHardwareMobileTitle": "Ledger don<PERSON>ım cüzdanınızı bağlayın", "connectHardwareMobileEnableBluetooth": "Bluetooth'u etkinleş<PERSON>rin", "connectHardwareMobileEnableBluetoothDescription": "Bağlanmak için Bluetooth'u kullanma izni verin", "connectHardwareMobileEnableBluetoothSettings": "Phantom'ın Konum ve Yakınlardaki Cihazlar izinlerini kullanmasına izin vermek için Ayarlar'a gidin.", "connectHardwareMobilePairWithDevice": "Ledger cihazınızla eşleştirin", "connectHardwareMobilePairWithDeviceDescription": "En iyi sinyali almak için cihazınızı yakınlarda tutun", "connectHardwareMobileConnectAccounts": "Hesaplayı bağla", "connectHardwareMobileConnectAccountsDescription": "Kullanmış olabileceğiniz hesaplarda aktivite arayacağız", "connectHardwareMobileConnectLedgerDevice": "Ledger cihazınızı bağlayın", "connectHardwareMobileLookingForDevices": "Yakınlardaki cihazlar aranıyor...", "connectHardwareMobileLookingForDevicesDescription": "Lütfen Ledger cihazınızı bağlayın ve kilidinin açık olduğundan emin olun.", "connectHardwareMobileFoundDeviceSingular": "1 Ledger cihazı bulduk", "connectHardwareMobileFoundDevices": "{{numDevicesFound}} Ledger cihazı bulduk", "connectHardwareMobileFoundDevicesDescription": "Eşleştirmeye başlamak için aşağıdan bir Ledger cihazı seçin.", "connectHardwareMobilePairingWith": "{{deviceName}} ile eşleştiriliyor", "connectHardwareMobilePairingWithDescription": "Eşleştirirken Ledger cihazınızdaki talimatları izleyin.", "connectHardwareMobilePairingFailed": "Eşleştirme başarısız", "connectHardwareMobilePairingFailedDescription": "{{deviceName}} ile eşleştirilemedi. Cihazının kilidinin açık olduğundan emin olun.", "connectHardwareMobilePairingSuccessful": "Eşleştirme başarılı", "connectHardwareMobilePairingSuccessfulDescription": "Ledger cihazınızı başarıyla eşleştirdiniz ve bağladınız.", "connectHardwareMobileOpenAppSingleChain": "Ledger'ınızda {{chainName}} uygulamasını açın", "connectHardwareMobileOpenAppDualChain": "Ledger'ınızda {{chainName1}} veya {{chainName2}} uygulamasını açın", "connectHardwareMobileOpenAppDescription": "Cihazınızın kilidinin açık olduğundan emin olun.", "connectHardwareMobileStillCantFindDevice": "Cihazınızı hâlâ bulamıyor musunuz?", "connectHardwareMobileLostConnection": "Kesik bağlantı", "connectHardwareMobileLostConnectionDescription": "{{deviceName}} ile ba<PERSON>lantı kesildi. Cihazının kilidinin açık olduğundan emin olup tekrar deneyin.", "connectHardwareMobileGenericLedgerDevice": "Ledger cihazı", "connectHardwareMobileConnectDeviceSigning": "{{deviceName}} adlı cihazınızı bağlayın", "connectHardwareMobileConnectDeviceSigningDescription": "Ledger cihazınızın kilidini açın ve yakınlarda tutun.", "connectHardwareMobileBluetoothDisabled": "Bluetooth devre dışı", "connectHardwareMobileBluetoothDisabledDescription": "Lütfen Bluetooth'unuzu etkinleştirip Ledger cihazınızın kilidinin açık olduğundan emin olun.", "connectHardwareMobileLearnMore": "<PERSON><PERSON>", "connectHardwareMobileBlindSigningDisabled": "<PERSON><PERSON><PERSON> de<PERSON> dışı", "connectHardwareMobileBlindSigningDisabledDescription": "<PERSON><PERSON><PERSON>n cihazınızda etkin olduğundan emin olun.", "connectHardwareMobileConfirmSingleChain": "Donanım cüzdanınızda işlemi onaylamanız gerekiyor. Kilidinin açık olduğundan emin olun.", "metamaskExplainerBottomSheetHeader": "Bu site Phantom ile çalışıyor", "metamaskExplainerBottomSheetSubheader": "İlerlemek için cüzdan bağla diyaloğundan MetaMask'i seçin.", "metamaskExplainerBottomSheetDontShowAgain": "<PERSON><PERSON><PERSON>", "ledgerStatusNotConnected": "Ledger bağ<PERSON><PERSON> değil", "ledgerStatusConnectedInterpolated": "{{productName}} bağlı", "connectionClusterInterpolated": "<PERSON>u anda {{cluster}} üzerindesiniz", "connectionClusterTestnetMode": "Şu anda Testnet Modundasınız", "featureNotSupportedOnLocalNet": "<PERSON><PERSON>, Solana Localnet etkinken desteklenmiyor.", "readOnlyAccountBannerWarning": "Bu hesabı izliyorsunuz", "depositAddress": "Alıcı Adresi", "depositAddressChainInterpolated": "{{chain}} Ad<PERSON><PERSON>z", "depositAssetDepositInterpolated": "{{tokenSymbol}} al", "depositAssetSecondaryText": "Bu adres uyumlu token'ları almak için kullanılabilir.", "depositAssetTextInterpolated": "<1>{{network}}</1> üzerinde token ve koleksiyonluk almak için bu adresi kullanın.", "depositAssetTransferFromExchange": "Borsadan transfer et", "depositAssetShareAddress": "<PERSON><PERSON><PERSON>", "depositAssetBuyOrDeposit": "Satın al veya Transfer et", "depositAssetBuyOrDepositDesc": "Başlamak için cüzdanınıza fon ekleyin", "depositAssetTransfer": "Tranfer et", "editAddressAddressAlreadyAdded": "<PERSON><PERSON> zaten eklendi", "editAddressAddressAlreadyExists": "<PERSON><PERSON> zaten mevcut", "editAddressAddressIsRequired": "<PERSON><PERSON>", "editAddressPrimaryText": "<PERSON><PERSON><PERSON>", "editAddressRemove": "<PERSON><PERSON> kaldır", "editAddressToast": "<PERSON><PERSON>", "removeSavedAddressToast": "<PERSON><PERSON> kaldırıldı", "exportSecretErrorGeneric": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ters gitti, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "exportSecretErrorIncorrectPassword": "Yanlış şifre", "exportSecretPassword": "Şifre", "exportSecretPrivateKey": "<PERSON><PERSON> anahtar", "exportSecretSecretPhrase": "g<PERSON><PERSON>", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "gizli kurtarma tümceciği", "exportSecretSelectYourAccount": "Hesabınızı seçin", "exportSecretShowPrivateKey": "Özel Anahtarı göster", "exportSecretShowSecretRecoveryPhrase": "Gizli kurtarma tümceciğini göster", "exportSecretShowSecret": "{{secretNameText}} bil<PERSON><PERSON> g<PERSON>", "exportSecretWarningPrimaryInterpolated": "{{secretNameText}} bilginizi <1>paylaşmayın</1>!", "exportSecretWarningSecondaryInterpolated": "Biri {{secretNameText}} bilginize sahip olursa cüzdanınızın tam kontrolüne sahip olur.", "exportSecretOnlyWay": "{{secretNameText}}, cüzdanınızı kurtarmanın tek yoludur", "exportSecretDoNotShow": "Kimsenin {{secretNameText}} bilginizi görmesine izin vermeyin", "exportSecretWillNotShare": "{{secretNameText}} bilgimi Phantom da<PERSON> kimseyle <PERSON>şmayacağım.", "exportSecretNeverShare": "{{secretNameText}} bilginizi asla kimseyle <PERSON>ın", "exportSecretYourPrivateKey": "<PERSON>zel <PERSON>", "exportSecretYourSecretRecoveryPhrase": "Gizli kurtarma tümceciğiniz", "exportSecretResetPin": "PIN'inizi sıfırlayın", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "Yardım", "gasUpTo": "En fazla {{ amount }}", "timeDescription1hour": "Yaklaşık 1 saat", "timeDescription30minutes": "Yaklaşık 30 dakika", "timeDescription10minutes": "Yaklaşık 10 dakika", "timeDescription2minutes": "Yaklaşık 2 dakika", "timeDescription30seconds": "Yaklaşık 30 saniye", "timeDescription15seconds": "Yaklaşık 15 saniye", "timeDescription10seconds": "Yaklaşık 10 saniye", "timeDescription5seconds": "Yaklaşık 5 saniye", "timeDescriptionAbbrev1hour": "1 sa", "timeDescriptionAbbrev30minutes": "30 dk", "timeDescriptionAbbrev10minutes": "10 dk", "timeDescriptionAbbrev2minutes": "2 dk", "timeDescriptionAbbrev30seconds": "30 sn", "timeDescriptionAbbrev15seconds": "15 sn", "timeDescriptionAbbrev10seconds": "10 sn", "timeDescriptionAbbrev5seconds": "5 sn", "gasSlow": "Yavaş", "gasAverage": "Ortalama", "gasFast": "Hızlı", "satsPerVirtualByte": "{{satsPerVirtualByte}} sat/vB", "satsAmount": "{{sats}} sat", "homeErrorButtonText": "<PERSON><PERSON><PERSON>", "homeErrorDescription": "Varlıklarınızı getirmeye çalışırken bir hata oluştu. Lütfen yenileyip tekrar deneyin", "homeErrorTitle": "Varlıklar alınamadı", "homeManageTokenList": "To<PERSON> listesini <PERSON>", "interstitialDismissUnderstood": "Anlaşıldı", "interstitialBaseWelcomeTitle": "Phantom artık Base'i destekliyor!", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON> g<PERSON>, alın ve satın alın", "interstitialBaseWelcomeItemTitle_2": "Base ekosistemini keşfedin", "interstitialBaseWelcomeItemTitle_3": "G<PERSON><PERSON><PERSON> ve emniyetli", "interstitialBaseWelcomeItemDescription_1": "Base'te {{paymentMethod}}, kart veya Coinbase'i kullanarak USDC ve ETH transfer edin ve satın alın.", "interstitialBaseWelcomeItemDescription_2": "Phantom'ı tüm favori DeFi ve NFT uygulamalarınızla kullanın.", "interstitialBaseWelcomeItemDescription_3": "<PERSON><PERSON>, spam filtreleme ve işlem simülasyonu ile güvende kalın.", "privacyPolicyChangedInterpolated": "Gizlilik Politikamız değişti. <1>Daha Fazla Bilgi Edinin</1>", "bitcoinAddressTypesBodyTitle": "Bitcoin adresi türleri", "bitcoinAddressTypesFeature1Title": "Bitcoin adresleri hakkında", "bitcoinAddressTypesFeature1Subtitle": "Phantom, Native Seg<PERSON>t ve <PERSON><PERSON><PERSON>'u destekler ve her biri kendi bakiyesine sahiptir. <PERSON><PERSON> adres tü<PERSON>yle de BTC veya Ordinal gönderebilirsiniz.", "bitcoinAddressTypesFeature2Title": "<PERSON><PERSON>", "bitcoinAddressTypesFeature2Subtitle": "Phantom'da<PERSON> varsayılan BTC adresi. Taproot'tan es<PERSON>, ancak tüm cüzdan ve borsalarla uyumludur.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Ordinal'ler ve BRC-20'ler için en iyisi ve en düşük ücretlere sahip. Tercihler -> Tercih Edilen Bitcoin Adresleri adımından adresleri ayarlayın.", "headerTitleInfo": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Bu, <1>{{addressType}}</1> adresinizdir.", "invalidChecksumTitle": "Gizli tümceciğinizi yükselttik!", "invalidChecksumFeature1ExportPhrase": "Yeni Gizli Tümceciğinizi dışa aktarın", "invalidChecksumFeature1ExportPhraseDescription": "Lütfen eski hesaplarınızın özel anahtarlarıyla birlikte yeni gizli tümceciğinizi yedekleyin.", "invalidChecksumFeature2FundsAreSafe": "Fonlarınız güvende ve emniyette", "invalidChecksumFeature2FundsAreSafeDescription": "Bu yükseltme otomatik olarak gerçekleştirildi. Phantom'da kimse gizli tümceciğinizi bilmez ve fonlarınıza erişemez.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON> fazla bilgi", "invalidChecksumFeature3LearnMoreDescription": "Çoğu cüzdanla uyumsuz bir tümceciğiniz vardı. Bu konuda daha fazla bilgi almak için <1>bu yardım makal<PERSON>ni</1> okuyun.", "invalidChecksumBackUpSecretPhrase": "<PERSON><PERSON><PERSON> tü<PERSON><PERSON><PERSON><PERSON>", "migrationFailureTitle": "Hesabı<PERSON><PERSON>z ta<PERSON>ınırken bir şeyler ters gitti", "migrationFailureFeature1": "Gizli tümceciğinizi dışa aktarın", "migrationFailureFeature1Description": "Lütfen kaydolmadan önce gizli tümceciğinizi yedek<PERSON>in.", "migrationFailureFeature2": "Phantom'a kaydolun", "migrationFailureFeature2Description": "Hesabınızı görüntülemek için Phantom'a yeniden kayıt olmanız gerekiyor.", "migrationFailureFeature3": "<PERSON>ha fazla bilgi alın", "migrationFailureFeature3Description": "Bu konuda daha fazla bilgi almak için <1>bu <PERSON><PERSON><PERSON> ma<PERSON></1> okuyun.", "migrationFailureContinueToOnboarding": "<PERSON><PERSON><PERSON><PERSON> devam edin", "migrationFailureUnableToFetchMnemonic": "<PERSON><PERSON><PERSON> tümce<PERSON>ğ<PERSON>z yü<PERSON>i", "migrationFailureUnableToFetchMnemonicDescription": "Lütfen destek ekibiyle iletişime geçin ve hata ayıklamak için uygulama günlüklerini indirin", "migrationFailureContactSupport": "Destekle iletişime geçin", "ledgerActionConfirm": "Ledger <PERSON><PERSON><PERSON>", "ledgerActionErrorBlindSignDisabledPrimaryText": "<PERSON><PERSON><PERSON> de<PERSON> dışı", "ledgerActionErrorBlindSignDisabledSecondaryText": "Lütfen donanım cihazınızda kör imzanızın etkin olduğundan emin olun ve ardından eylemi tekrar deneyin", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Donanım cihazının bağlantısı operasyon sırasında kesildi", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Lütfen Phantom eklentisini kapatın ve ardından eylemi tekrar deneyin", "ledgerActionErrorDeviceLockedPrimaryText": "Donanım cihazı kilitlendi", "ledgerActionErrorDeviceLockedSecondaryText": "Lütfen donanım cihazınızın kilidini açın ve eylemi tekrar deneyin", "ledgerActionErrorHeader": "<PERSON><PERSON>t Defteri E<PERSON>m Hat<PERSON>ı", "ledgerActionErrorUserRejectionPrimaryText": "Kullanıcı işlemi reddetti", "ledgerActionErrorUserRejectionSecondaryText": "<PERSON><PERSON><PERSON> kullanıcı tarafından donanım cihazında reddedildi", "ledgerActionNeedPermission": "<PERSON><PERSON> gere<PERSON>", "ledgerActionNeedToConfirm": "Donanım cüzdanınızda işlemi onaylamanız gerekiyor. {{chainType}} uygulamasında kilidinin açık olduğundan emin olun.", "ledgerActionNeedToConfirmMany": "Donanım cüzdanınızda {{numberOfTransactions}} işlemi onaylamanız gerekiyor. {{chainType}} uygulamasında kilidinin açık olduğundan emin olun.", "ledgerActionNeedToConfirmBlind": "Donanım cüzdanınızda işlemi onaylamanız gerekiyor. {{chainType}} uygulamasında kilidinin açık olduğundan ve kör imzalamanın etkin olduğundan emin olun.", "ledgerActionNeedToConfirmBlindMany": "Donanım cüzdanınızda {{numberOfTransactions}} işlemi onaylamanız gerekiyor. {{chainType}} uygulamasında kilidinin açık olduğundan ve kör imzalamanın etkin olduğundan emin olun.", "ledgerActionPleaseConnect": "Lütfen Ledger Nano'nuzu bağlayın", "ledgerActionPleaseConnectAndConfirm": "Lütfen donanım cüzdanınızı bağlayın ve kilidinin açıldığından emin olun. Tarayıcınızda izinleri onayladığınızdan emin olun.", "maxInputAmount": "<PERSON><PERSON><PERSON>", "maxInputMax": "Ma<PERSON>.", "notEnoughSolPrimaryText": "Yeterli SOL yok", "notEnoughSolSecondaryText": "Bu işlem için cüzdanınızda yeterince SOL yok. Lütfen daha fazla yatırın ve tekrar deneyin.", "insufficientBalancePrimaryText": "Yetersiz {{tokenSymbol}}", "insufficientBalanceSecondaryText": "Bu işlem için c<PERSON> yet<PERSON> {{tokenSymbol}} yok.", "insufficientBalanceRemaining": "<PERSON><PERSON>", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "Yeterli token yok", "notEnoughSplTokensDescription": "Bu işlem için cüzdanınızda yeterince token yok. Gönderilmesi durumunda bu işlem geri dönecek.", "transactionExpiredPrimaryText": "İş<PERSON>in süresi doldu", "transactionExpiredSecondaryText": "Bu işlemi onaylamak için çok uzun süre be<PERSON>iniz ve süresi doldu. Gönderilmesi durumunda bu işlem geri dönecek.", "transactionHasWarning": "İşlem uyarısı", "tokens": "token'lar", "notificationApplicationApprovalPermissionsAddressVerification": "Bu adrese sahip olduğunuzu doğrulayın", "notificationApplicationApprovalPermissionsTransactionApproval": "İşlemler için onay istemek istiyor", "notificationApplicationApprovalPermissionsViewWalletActivity": "Cüzdan bakiyenizi ve aktivitenizi görünt<PERSON>lemek istiyor", "notificationApplicationApprovalParagraphText": "<PERSON><PERSON><PERSON>, bu <PERSON>nin seçili hesa<PERSON> için baki<PERSON>leri ve aktiviteyi görüntülemesine izin verir.", "notificationApplicationApprovalActionButtonConnect": "Bağlan", "notificationApplicationApprovalActionButtonSignIn": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "<PERSON>nin bağlanmasına izin verilsin mi?", "notificationApplicationApprovalAutoConfirm": "İşlemleri otomatik onayla", "notificationApplicationApprovalConnectDisclaimer": "Yalnızca güvendiğiniz web sitelerine bağlanın", "notificationApplicationApprovalSignInDisclaimer": "Yalnızca güvendiğiniz web sitelerine giriş yapın", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Bu web sitesini kullanmak güvenli değil ve site, fonlarınızı çalmaya çalışabilir.", "notificationApplicationApprovalConnectUnknownApp": "Bilinmeyen", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Uygulama bağlanamıyor", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Bu uygulama {{appNetworkName}} ad<PERSON>ı ağa bağlanmaya çalışıyor, ancak {{phantomNetworkName}} seçili.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "{{networkName}} adlı ağı kullanmak için Geliştirici Ayarları → Testnet Moduna gidin.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Bilinmeyen Ağ", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Diğer mobil uygulamalara bağlanmak şu anda Ledger tarafından desteklenmiyor.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Lütfen Ledger olmayan bir hesaba geçin veya uygulama içi tarayıcıyı kullanarak tekrar deneyin.", "notificationSignatureRequestConfirmTransaction": "İşlem onayla", "notificationSignatureRequestConfirmTransactionCapitalized": "İşlemi <PERSON>", "notificationSignatureRequestConfirmTransactions": "İşlemleri onayla", "notificationSignatureRequestConfirmTransactionsCapitalized": "İşlemleri onayla", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON><PERSON>", "notificationMessageHeader": "<PERSON><PERSON>", "notificationMessageCopied": "Mesaj k<PERSON>alandı", "notificationAutoConfirm": "Otomatik <PERSON>", "notificationAutoConfirmOff": "<PERSON><PERSON><PERSON>", "notificationAutoConfirmOn": "Açık", "notificationConfirmFooter": "Yalnızca bu web sitesine güveniyorsanız onaylayın.", "notificationEstimatedTime": "<PERSON><PERSON><PERSON>", "notificationPermissionRequestText": "Bu yalnızca bir izin talebidir. İşlem hemen yürütülmeyebilir.", "notificationBalanceChangesText": "Bakiye değişiklikleri tahminîdir. İlgili tutar ve varlıklar garantili değildir.", "notificationContractAddress": "Sözleşme Adresi", "notificationAdvancedDetailsText": "Gelişmiş", "notificationUnableToSimulateWarningText": "<PERSON><PERSON> anda bakiye değişikliklerini tahmin edemiyoruz. <PERSON>ha sonra deneyebilir ya da bu siteye güveniyorsanız onaylayabilirsiniz.", "notificationSignMessageParagraphText": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, seç<PERSON> hesaba sahip olduğunuzu kanıtlar.", "notificationSignatureRequestScanFailedDescription": "<PERSON><PERSON>, güvenlik sorunları için taranamıyor. Lütfen dikkatle ilerleyin.", "notificationFailedToScan": "Bu talebin sonuçları simüle edilemedi.\nOnaylamak güvenli değil ve kayıplara neden olabilir.", "notificationScanLoading": "<PERSON><PERSON>", "notificationTransactionApprovalActionButtonConfirm": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonBack": "<PERSON><PERSON>", "notificationTransactionApprovalEstimatedChanges": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Ta<PERSON><PERSON>ler işlem simülasyonlarına dayalıdır ve garanti de<PERSON>dir", "notificationTransactionApprovalHideAdvancedDetails": "Gelişmiş işlem bilgilerini gizle", "notificationTransactionApprovalNetworkFee": "<PERSON><PERSON>", "notificationTransactionApprovalNetwork": "Ağ", "notificationTransactionApprovalEstimatedTime": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Varlık mülkiyetini etkileyen değişiklik bulunamadı", "notificationTransactionApprovalSolanaAmountRequired": "Solana ağının işlemi işlemek için gerektirdiği ücret", "notificationTransactionApprovalUnableToSimulate": "Simüle edilemiyor. Onaylaman fon kaybına neden olabileceğinden bu web sitesine güvenebileceğinizden emin olun.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Bakiye değişiklikleri getirilemedi", "notificationTransactionApprovalViewAdvancedDetails": "Gelişmiş işlem bilgilerini görüntüle", "notificationTransactionApprovalKnownMalicious": "Bu işlem kötü niyetli. İmzalamak, fonların kaybına neden olur.", "notificationTransactionApprovalSuspectedMalicious": "Bu işlemin kötü niyetli olduğundan şüpheleniyoruz. İşlemi onaylamak fonların kaybına neden olabilir.", "notificationTransactionApprovalNetworkFeeHighWarning": "Ağ ücretleri ağ tıkanıklığı nedeniyle daha yüksek.", "notificationTransactionERC20ApprovalDescription": "Onaylamak bu uygulamanın aşağıdaki limite kadar herhangi bir zamanda bakiyenize erişmesine izin verir.", "notificationTransactionERC20ApprovalContractAddress": "Sözleşme Adresi", "notificationTransactionERC20Unlimited": "sınırsız", "notificationTransactionERC20ApprovalTitle": "{{tokenSymbol}} harcamayı onaylayın", "notificationTransactionERC20RevokeTitle": "{{tokenSymbol}} harcamayı geri alın", "notificationTransactionERC721RevokeTitle": "{{tokenSymbol}} er<PERSON><PERSON><PERSON><PERSON> geri alın", "notificationTransactionERC20ApprovalAll": "Size ait tüm {{tokenSymbol}}", "notificationIncorrectModeTitle": "Yanlış mod", "notificationIncorrectModeInTestnetTitle": "Testnet modundasınız", "notificationIncorrectModeNotInTestnetTitle": "Testnet modunda değilsiniz", "notificationIncorrectModeInTestnetDescription": "{{origin}} mainnet kullanmaya çalışıyor, ancak siz Testnet modundasınız", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} Testnet kullanmaya çalışıyor, ancak siz Testnet modunda değilsiniz", "notificationIncorrectModeInTestnetProceed": "İlerlemek için Testnet modunu kapatın.", "notificationIncorrectModeNotInTestnetProceed": "İlerlemek için Testnet modunu açın.", "notificationIncorrectEIP712ChainId": "<PERSON><PERSON> anda bağlı olduğunuz ağa yönelik olmayan bir mesajı imzalamanızı önledik", "notificationIncorrectEIP712ChainIdDescription": "<PERSON><PERSON> talep edildi {{messageChainId}}, <PERSON><PERSON> bağlısınız: {{connectedChainId}}", "notificationUnsupportedNetwork": "Desteklenmeyen ağ", "notificationUnsupportedNetworkDescription": "Bu web sitesi, Phantom'ın şu anda desteklemediği bir ağı kullanmaya çalışıyor.", "notificationUnsupportedNetworkDescriptionInterpolated": "Farklı bir uzantıyla ilerlemek için <1>Ayarlar → Varsayılan Uygulama Cüzdanını kapatın ve Her Zaman Sor'u seçin</1>. Ardından sayfayı yenileyin ve tekrar bağlanın.", "notificationUnsupportedAccount": "Desteklenmeyen hesap", "notificationUnsupportedAccountDescription": "Bu web sitesi, bu {{chainType}} hesabının desteklemediği {{targetChainType}} ağını kullanmaya çalışıyor.", "notificationUnsupportedAccountDescription2": "Uyumlu bir tohum ifadesinden bir hesaba geçiş yapıp tekrar deneyin.", "notificationInvalidTransaction": "Geçersiz işlem", "notificationInvalidTransactionDescription": "Bu uygulamadan alınan işlem kusurlu ve gönderilmemeli. Lütfen bu sorunu bildirmek için bu uygulamanın geliştiricisiyle iletişime geçin.", "notificationCopyTransactionText": "İşlemi kopyala", "notificationTransactionCopied": "İşlem kopyalandı", "onboardingImportOptionsPageTitle": "Cüzdan içe aktarın", "onboardingImportOptionsPageSubtitle": "<PERSON><PERSON><PERSON>, <PERSON>zel anahtar veya donanım cüzdanıyla mevcut bir cüzdanı içe aktarın.", "onboardingImportPrivateKeyPageTitle": "<PERSON><PERSON>htar içe aktarın", "onboardingImportPrivateKeyPageSubtitle": "Mevcut bir tek zincirli cüzdan içe aktarın", "onboardingCreatePassword": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<1>Hizmet Koşullarını</1> kabul ediyorum", "onboardingCreatePasswordConfirmPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordDescription": "Cüzdanınızın kilidini açmak için bunu kullanacaksınız.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Geçersiz gizli kurtarma tümceciği", "onboardingCreatePasswordPasswordPlaceholder": "Şifre", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthMedium": "Orta", "onboardingCreatePasswordPasswordStrengthStrong": "Güçlü", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> Tümceciğimi ka<PERSON>ettim", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> Tümceciği", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Bu tümcecik cüzdanınızı kurtarmanın TEK yolu. Bunu kimseyle PAYLAŞMAYIN!", "onboardingImportWallet": "Cüzdanı İçe Aktar", "onboardingImportWalletImportExistingWallet": "12 veya 24 kelimelik gizli kurtarma tümceciğinizle mevcut bir cüzdanı içe aktarın.", "onboardingImportWalletRestoreWallet": "Cüzdanı Geri <PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> Tümceciği", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Geçersiz Gizli Kurtarma Tümceciği", "onboardingImportWalletIHaveWords": "{{numWords}} kelimelik kurtarma ifadem var", "onboardingImportWalletIncorrectOrMisspelledWord": "{{wordIndex}} kelimesi doğru değil veya yanlış yazılmış", "onboardingImportWalletIncorrectOrMisspelledWords": "{{wordIndexes}} kelimeleri doğru değil veya yanlış yazılmış", "onboardingImportWalletScrollDown": "Aşağı kaydır", "onboardingImportWalletScrollUp": "Yukarı kaydır", "onboardingSelectAccountsImportAccounts": "Hesapları İçe Aktar", "onboardingSelectAccountsImportAccountsDescription": "İçe aktarılacak cüzdan hesaplarını seçin.", "onboardingSelectAccountsImportSelectedAccounts": "Seçili Hesapları İçe Aktar", "onboardingSelectAccountsFindMoreAccounts": "<PERSON><PERSON> fazla hesap bulun", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON><PERSON> b<PERSON>", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} hesap se<PERSON><PERSON><PERSON>", "onboardingSelectAccountSelectAllText": "Tümünü Seç", "onboardingAdditionalPermissionsTitle": "Uygulamaları Phantom'la kullan", "onboardingAdditionalPermissionsSubtitle": "En sorunsuz uygulama deneyimi için Phantom'ın tüm sitelerdeki verileri okumasına ve değiştirmesine izin verilmesini öneririz.", "interstitialAdditionalPermissionsTitle": "Uygulamaları Phantom'la kullan", "interstitialAdditionalPermissionsSubtitle": "Uygulamaları kullanma deneyiminize kesintisiz bir şekilde devam etmek için Phantom'ın tüm sitelerdeki verileri okumasına ve değiştirmesine izin verilmesini öneririz.", "recentActivityPrimaryText": "Son Aktivite", "removeAccountActionButtonRemove": "Kaldır", "removeAccountRemoveWallet": "Hesabı kaldır", "removeAccountInterpolated": "{{accountName}} ad<PERSON>ı hesabı kaldır", "removeAccountWarningLedger": "Bu cüzdanı Phantom'dan kald<PERSON> bile, \"Donanım Cüzdanı Bağla\" akışını kullanarak tekrar ekleyebilirsiniz.", "removeAccountWarningSeedVault": "Bu cüzdanı Phantom'dan kald<PERSON> bile, \"Tohum Kasası Cüzdanı Bağla\" akışını kullanarak tekrar ekleyebilirsiniz.", "removeAccountWarningPrivateKey": "Bu cüzdanı kaldırdığınızda Phantom onu sizin için kurtaramaz. <PERSON>zel anahtarınızın yedeği olduğundan emin olun.", "removeAccountWarningSeed": "Bu cüzdanı Phantom'dan kald<PERSON> bile, bu veya başka bir cüzdandaki hatırlatıcı ipucunuzu kullanarak tekrar elde edebilirsiniz.", "removeAccountWarningReadOnly": "Bu yalnızca izlenen bir cüzdan olduğundan, hesa<PERSON><PERSON> silmek cüzdanınızı etkilemez.", "removeSeedPrimaryText": "<PERSON><PERSON><PERSON> Tümcecik {{number}} kaldırılıyor", "removeSeedSecondaryText": "<PERSON><PERSON>, <PERSON><PERSON><PERSON> Tümcecik {{number}} dahilindeki tüm mevcut hesapları kaldırır. Mevcut gizli tümceciğinizin kaydedildiğinden emin olun.", "resetSeedPrimaryText": "Uygulamayı yeni gizli tümcecikle sıfırlayın", "resetSeedSecondaryText": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, mevcut tüm hesapları kaldırır ve onları yenileriyle değiştirir. Mevcut gizli tümceciğinizin ve özel anahtarlarınızın yedeklendiğinden emin olun.", "resetAppPrimaryText": "Uygulamayı sıfırlayıp temizleyin", "resetAppSecondaryText": "<PERSON><PERSON>, mevcut tüm hesap ve verileri kaldırır. <PERSON><PERSON><PERSON> tümceciğinizi ve özel anahtarınızı yedeklediğinizden emin olun.", "richTransactionsDays": "g<PERSON>n", "richTransactionsToday": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionsYesterday": "<PERSON><PERSON><PERSON>", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "Uygulama <PERSON>i", "richTransactionDetailAt": "saat", "richTransactionDetailBid": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailBidDetails": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailBought": "Satın Aldı", "richTransactionDetailBurned": "Yakıldı", "richTransactionDetailCancelBid": "Teklifi <PERSON> Et", "richTransactionDetailCompleted": "Tamamlandı", "richTransactionDetailConfirmed": "Onaylandı", "richTransactionDetailDate": "<PERSON><PERSON><PERSON>", "richTransactionDetailFailed": "Başarısız", "richTransactionDetailFrom": "<PERSON><PERSON>", "richTransactionDetailItem": "Öge", "richTransactionDetailListed": "<PERSON><PERSON><PERSON>", "richTransactionDetailListingDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailListingPrice": "Listeleme Fiyatı", "richTransactionDetailMarketplace": "<PERSON><PERSON>", "richTransactionDetailNetworkFee": "<PERSON><PERSON>", "richTransactionDetailOriginalListingPrice": "<PERSON><PERSON><PERSON> Listeleme Fiyatı", "richTransactionDetailPending": "Bekliyor", "richTransactionDetailPrice": "<PERSON><PERSON><PERSON>", "richTransactionDetailProvider": "Sağlayıcı", "richTransactionDetailPurchaseDetails": "Saıtn Alma Bilgileri", "richTransactionDetailRebate": "<PERSON><PERSON> yap", "richTransactionDetailReceived": "Alındı", "richTransactionDetailSaleDetails": "Satış Bilgileri", "richTransactionDetailSent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailSold": "Satıldı", "richTransactionDetailStaked": "<PERSON><PERSON>", "richTransactionDetailStatus": "Durum", "richTransactionDetailSwap": "Ta<PERSON>", "richTransactionDetailSwapDetails": "<PERSON><PERSON>", "richTransactionDetailTo": "<PERSON><PERSON>", "richTransactionDetailTokenSwap": "Token Takası", "richTransactionDetailUnknownNFT": "Bilinmeyen NFT", "richTransactionDetailUnlisted": "Listeden Kaldırıldı", "richTransactionDetailUnstaked": "Stake Kaldırıldı", "richTransactionDetailValidator": "Doğrulayıcı", "richTransactionDetailViewOnExplorer": "{{explorer}}'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailWithdrawStake": "<PERSON><PERSON>'<PERSON>ek", "richTransactionDetailYouPaid": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailYouReceived": "Aldığınız", "richTransactionDetailUnwrapDetails": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailTokenUnwrap": "Token Kaydırması", "activityItemsRefreshFailed": "<PERSON><PERSON> yeni <PERSON> yükle<PERSON>medi.", "activityItemsPagingFailed": "<PERSON><PERSON> es<PERSON> işlemler yüklenemedi.", "activityItemsTestnetNotAvailable": "Testnet işlem geçmişi şu anda kullanılamıyor", "historyUnknownDappName": "Bilinmeyen", "historyStatusSucceeded": "Başarılı", "historyNetwork": "Ağ", "historyAttemptedAmount": "<PERSON><PERSON><PERSON> miktar", "historyAmount": "<PERSON><PERSON><PERSON>", "sendAddressBookButtonLabel": "<PERSON><PERSON>", "addressBookSelectAddressBook": "<PERSON><PERSON>", "sendAddressBookNoAddressesSaved": "Kayıtlı adres yok", "sendAddressBookRecentlyUsed": "<PERSON>", "addressBookSelectRecentlyUsed": "<PERSON>", "sendConfirmationLabel": "Etiket", "sendConfirmationMessage": "<PERSON><PERSON>", "sendConfirmationNetworkFee": "<PERSON><PERSON>", "sendConfirmationPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendWarning_INSUFFICIENT_FUNDS": "<PERSON><PERSON><PERSON> fon, bu işlem gönderilirse muhtemelen başarısız olacak.", "sendFungibleSummaryNetwork": "Ağ", "sendFungibleSummaryNetworkFee": "<PERSON><PERSON>", "sendFungibleSummaryEstimatedTime": "<PERSON><PERSON><PERSON>", "sendFungiblePendingEstimatedTime": "<PERSON><PERSON><PERSON>", "sendFungibleSummaryEstimatedTimeDescription": "Ethereum işlem hızları birkaç faktöre dayalı olarak değişiklik gösterir. Bunları \"Ağ Ücreti\" seçeneğine tıklayarak hızlandırabilirsiniz.", "sendSummaryBitcoinPendingTxTitle": "Transfer gönderilemedi", "sendSummaryBitcoinPendingTxDescription": "Tek seferde sadece bir BTC transferiniz olabilir. Yeni bir transfer göndermek için lütfen tamamlanana kadar be<PERSON>.", "sendFungibleSatProtectionTitle": "Sat Korumasıyla gönderiliyor", "sendFungibleSatProtectionExplainer": "Phantom, Ordinal'lerinizin ve BRC20'lerinizin işlem ücretleri veya Bitcoin transferleri için kullanılmayacağını garanti eder.", "sendFungibleTransferFee": "Token transfer ücreti", "sendFungibleTransferFeeToolTip": "Bu token'ın <PERSON>, her bir transferde bir ücret alır. <PERSON><PERSON>, Phantom tarafından alınan veya toplanan bir ücret değildir.", "sendFungibleInterestBearingPercent": "Mevcut Faiz Oranı", "sendFungibleNonTransferable": "Transfer edilemez", "sendFungibleNonTransferableToolTip": "Bu token başka bir hesaba transfer edilemez.", "sendFungibleNonTransferableYes": "<PERSON><PERSON>", "sendStatusErrorMessageInterpolated": "<1>{{uiRecipient}}</1> adlı kişiye token gönderme işlemi denemesinde bir hata oluştu", "sendStatusErrorMessageInsufficientBalance": "İşlemi tamamlamak için bakiyeniz yetersiz.", "sendStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sendStatusLoadingTitle": "Gönderiliyor...", "sendStatusSuccessMessageInterpolated": "Token'larınız <1>{{uiRecipient}}</1> adlı kişiye başarıyla gönderildi", "sendStatusSuccessTitle": "Gönderildi!", "sendStatusConfirmedSuccessTitle": "Gönderildi!", "sendStatusSubmittedSuccessTitle": "İşlem Gönderildi", "sendStatusEstimatedTransactionTime": "<PERSON><PERSON><PERSON>üresi: {{time}}", "sendStatusViewTransaction": "İşlemi görü<PERSON>üle", "sendFungibleLoadingMessageInterpolated": "<2>{{uiRecipient}}</2> ad<PERSON><PERSON> al<PERSON>ı<PERSON> <2>{{uiAmount}} {{assetSymbol}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> başarıyla <2>{{uiRecipient}}</2> adlı alıcıya gönderildi", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> başarıyla <2>{{uiRecipient}}</2> adlı alıcıya gönderildi", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2>, <2>{{uiRecipient}}</2> adlı alıcıya gönderilemedi", "sendFungibleSolanaErrorCode": "<PERSON><PERSON> {{code}}", "sendFormErrorInsufficientBalance": "<PERSON><PERSON><PERSON>", "sendFormErrorEmptyAmount": "<PERSON><PERSON><PERSON><PERSON> miktar", "sendFormInvalidAddress": "Geçersiz {{assetName}} adresi", "sendFormInvalidUsernameOrAddress": "Geçersiz kullanıcı adı veya adres", "sendFormErrorInvalidSolanaAddress": "Geçersiz Solana adresi", "sendFormErrorInvalidTwitterHandle": "Bu Twitter tanıtıcısı kayıtlı değil", "sendFormErrorInvalidDomain": "<PERSON>u alan adı kayıtlı değ<PERSON>", "sendFormErrorInvalidUsername": "Bu kullanıcı adı kayıtlı değil", "sendFormErrorMinRequiredInterpolated": "En az {{minAmount}} {{tokenName}} gerekli", "sendRecipientTextareaPlaceholder": "Alıcının SOL adresi", "sendRecipientTextAreaPlaceholder2": "<PERSON><PERSON><PERSON><PERSON>n<PERSON>n {{symbol}} adresi", "sendMemoOptional": "Memo (isteğe bağlı)", "sendMemo": "Memo", "sendOptional": "iste<PERSON>e bağlı", "settings": "<PERSON><PERSON><PERSON>", "settingsDapps": "dApps", "settingsSelectedAccount": "<PERSON><PERSON><PERSON>", "settingsAddressBookNoLabel": "Etiket Yok", "settingsAddressBookPrimary": "<PERSON><PERSON>", "settingsAddressBookRecentlyUsed": "<PERSON>", "settingsAddressBookSecondary": "En sık kullanılan adresleri yönetin", "settingsAutoLockTimerPrimary": "Otomatik Kilitleme Sayacı", "settingsAutoLockTimerSecondary": "Otomatik kilitleme sayacı sürenizi değiştirin", "settingsChangeLanguagePrimary": "<PERSON><PERSON>", "settingsChangeLanguageSecondary": "<PERSON><PERSON><PERSON>", "settingsChangeNetworkPrimary": "<PERSON><PERSON><PERSON> değiştirin", "settingsChangeNetworkSecondary": "<PERSON>ğ <PERSON>larınızı yapılandırın", "settingsChangePasswordPrimary": "<PERSON><PERSON><PERSON><PERSON>", "settingsChangePasswordSecondary": "Kilit ekranı şifrenizi değiştirin", "settingsCompleteBetaSurvey": "Tam <PERSON>", "settingsDisplayLanguage": "<PERSON><PERSON><PERSON>", "settingsErrorCannotExportLedgerPrivateKey": "Kayıt Defteri özel anahtarı dışa aktarılamıyor", "settingsErrorCannotRemoveAllWallets": "<PERSON><PERSON>m hesaplar kaldırılamıyor", "settingsExportPrivateKey": "Özel Anahtarı göster", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "<PERSON><PERSON>", "settingsNetworkPhantomRPC": "Phantom RPC Ağı", "settingsTestNetworks": "Test Ağları", "settingsUseCustomNetworks": "Özel Ağları Kullan", "settingsTestnetMode": "Testnet Modu", "settingsTestnetModeDescription": "Bakiyeler ve uygulama bağlantıları için geçerlidir.", "settingsWebViewDebugging": "Web Görüntüleme Hata Ayıklaması", "settingsWebViewDebuggingDescription": "Uygulama içi tarayıcı web görüntülemelerini denetlemenize ve hata ayıklamanıza izin verir.", "settingsTestNetworksInfo": "Herhangi bir Testnet ağına geçmek yalnızca test amaçlıdır. Testnet Ağlarındaki token'ların parasal bir değeri olmadığını lütfen dikkate alın.", "settingsEmojis": "<PERSON><PERSON><PERSON>'ler", "settingsNoAddresses": "<PERSON><PERSON> yok", "settingsAddressBookEmptyHeading": "<PERSON><PERSON> bo<PERSON>", "settingsAddressBookEmptyText": "Favori adreslerinizi eklemek için \"+\" veya \"<PERSON><PERSON>\" düğ<PERSON><PERSON><PERSON> tıklayın", "settingsEditWallet": "Hesabı düzenle", "settingsNoTrustedApps": "<PERSON><PERSON><PERSON><PERSON><PERSON> u<PERSON> yok", "settingsNoConnections": "Henüz bağlantı yok.", "settingsRemoveWallet": "Hesabı kaldır", "settingsResetApp": "Uygulamayı sıfırlayın", "settingsBlocked": "<PERSON>gel<PERSON>di", "settingsBlockedAccounts": "Engellenen He<PERSON>lar", "settingsNoBlockedAccounts": "Engellenen hesap yok.", "settingsRemoveSecretPhrase": "Gizli Tümceciği kaldırın", "settingsResetAppWithSecretPhrase": "Gizli Tümcecikle Uygulamayı sıfırlayın", "settingsResetSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> Kurtarma Tümceciğini sıfırla", "settingsShowSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> Tümceciğini göster", "settingsShowSecretRecoveryPhraseSecondary": "<PERSON><PERSON> Tümceciğini göster", "settingsShowSecretRecoveryPhraseTertiary": "<PERSON><PERSON><PERSON> Tümceciği göster", "settingsTrustedAppsAutoConfirmActiveUntil": "<PERSON><PERSON> zamana kadar: {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Otomatik <PERSON>", "settingsTrustedAppsDisclaimer": "Yalnızca güvenilir sitelerde otomatik onayla", "settingsTrustedAppsLastUsed": "{{formattedTimestamp}} <PERSON>nce kullanıldı", "settingsTrustedAppsPrimary": "Bağlı Uygulamalar", "settingsTrustedApps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsRevoke": "İptal et", "settingsTrustedAppsRevokeToast": "{{trustedApp}} bağlantısı kesildi", "settingsTrustedAppsSecondary": "Güvenilir uygulamalarınızı yapılandırın", "settingsTrustedAppsToday": "<PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsYesterday": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsLastWeek": "Geçen Hafta", "settingsTrustedAppsBeforeYesterday": "<PERSON><PERSON>", "settingsTrustedAppsDisconnectAll": "Tümüyle bağlantıyı kes", "settingsTrustedAppsDisconnectAllToast": "Tüm uygulamaların bağlantısı kesildi", "settingsTrustedAppsEndAutoConfirmForAll": "Tümü için otomatik onaylamayı sonlandır", "settingsTrustedAppsEndAutoConfirmForAllToast": "Tüm otomatik onaylama oturumları sona erdi", "settingsSecurityPrimary": "Emniyet ve Güvenlik", "settingsSecuritySecondary": "Güvenlik ayarlarınızı güncelleyin", "settingsActiveNetworks": "<PERSON><PERSON><PERSON>", "settingsActiveNetworksAll": "Tümü", "settingsActiveNetworksSolana": "Yalnızca Solana", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "<PERSON><PERSON>, var<PERSON><PERSON><PERSON> ağdır ve her zaman açık kalır.", "settingsDeveloperPrimary": "Geliştirici <PERSON>ı", "settingsAdvanced": "Gelişmiş <PERSON>", "settingsTransactions": "İşlem Ayarları", "settingsAutoConfirm": "Otomatik Onayla ayarları", "settingsSecurityAnalyticsPrimary": "<PERSON><PERSON><PERSON>", "settingsSecurityAnalyticsSecondary": "İyileşmemize yardım etmek için etkinleştirin", "settingsSecurityAnalyticsHelper": "Phantom, analiz amaçları için kişisel bilgilerinizi kullanmaz", "settingsSuspiciousCollectiblesPrimary": "Şüpheli Koleksiyonlukları gizleyin", "settingsSuspiciousCollectiblesSecondary": "Bayraklı koleksiyonlukları gizlemek için geçiş yapın", "settingsPreferredBitcoinAddress": "Tercih edilen Bitcoin Adresi", "settingsEnabledAddressesUpdated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>ler güncellendi!", "settingsEnabledAddresses": "<PERSON><PERSON><PERSON>", "settingsBitcoinPaymentAddressForApps": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsBitcoinOrdinalsAddressForApps": "Uygulamalar için Or<PERSON>l Adresi", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Yukarıda iki adres türü de etkin olduğunda Magic Eden gibi belirli uygulamalar için satın alma işlemlerini fonlamak için Yerel Segwit adresiniz kullanılır. Satın alınan varlıklar Taproot adresinize alınır.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Uygunluğu sağlamak için Phantom'daki varsayılan Bitcoin adresi.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Varsayılan)", "settingsPreferredBitcoinAddressTaprootExplainer": "En modern adres türü, genelde daha ucuz işlem ücretleriyle.", "settingsPreferredExplorers": "<PERSON><PERSON><PERSON>", "settingsPreferredExplorersSecondary": "<PERSON><PERSON><PERSON> ettiğiniz blok zinciri explorer'<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsCustomGasControls": "Özel Gas Kontrolleri", "settingsSupportDesk": "Destek Masası", "settingsSubmitATicket": "<PERSON><PERSON><PERSON>", "settingsAttachApplicationLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsDownloadApplicationLogs": "Uygulam<PERSON> indir", "settingsDownloadApplicationLogsShort": "Günlükleri indir", "settingsDownloadApplicationLogsHelper": "Phantom Destek sorunlarını çözmeye yardımcı olmak için yerel verileri, çökme raporlarını ve herkese açık cüzdan adreslerini içerir", "settingsDownloadApplicationLogsWarning": "<PERSON><PERSON> if<PERSON>leri veya özel anahtarlar gibi hassas veriler d<PERSON>.", "settingsWallet": "Cüzdan", "settingsPreferences": "<PERSON><PERSON><PERSON><PERSON>", "settingsSecurity": "Güvenlik", "settingsDeveloper": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSupport": "Destek", "settingsWalletShortcutsPrimary": "Cüzdan Kısayollarını göster", "settingsAppIcon": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsAppIconDefault": "Varsayılan", "settingsAppIconLight": "Açık", "settingsAppIconDark": "<PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON><PERSON>", "settingsSearchResultSelected": "Seçildi", "settingsSearchResultExport": "Dışa aktar", "settingsSearchResultSeed": "<PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultTrusted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Durum", "settingsSearchResultLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultBiometric": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultTouch": "<PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultFace": "<PERSON><PERSON>z", "settingsSearchResultShortcuts": "K<PERSON><PERSON>ollar", "settingsAllSitesPermissionsTitle": "Phantom'a tüm sitelerde eriş", "settingsAllSitesPermissionsSubtitle": "Uygulamaları Phantom'la uzantıya tıklamaya gerek kalmadan sorunsuz bir şekilde kullanabilmenizi sağlar", "settingsAllSitesPermissionsDisabled": "Tarayıcınız bu ayarı değiştirmeyi desteklemiyor", "settingsSolanaCopyTransaction": "İşlem Kopyalamayı etkinleştir", "settingsSolanaCopyTransactionDetails": "Serileştirilmiş işlem verilerini panoya kopyala", "settingsAutoConfirmHeader": "Otomatik <PERSON>", "refreshWebpageToApplyChanges": "Değişiklikleri uygulamak için web sayfasını yenileyin", "settingsExperimentalTitle": "<PERSON><PERSON>sel <PERSON>", "settingsExprimentalSolanaActionsSubtitle": "X.com adresinde ilgili bağlantılar tespit edildiğinde Solana Eylem düğmelerini otomatik olarak genişlet", "stakeAccountCardActiveStake": "Akt<PERSON>", "stakeAccountCardBalance": "Bakiye", "stakeAccountCardRentReserve": "<PERSON>", "stakeAccountCardRewards": "<PERSON>", "stakeAccountCardRewardsTooltip": "<PERSON><PERSON>, stake ettiğiniz için kazandığınız en son ödül. 3 günde bir ödül alırsınız.", "stakeAccountCardStakeAccount": "<PERSON><PERSON>", "stakeAccountCardLockup": "<PERSON><PERSON>:", "stakeRewardsHistoryTitle": "Ödül Geçmişi", "stakeRewardsActivityItemTitle": "<PERSON><PERSON><PERSON><PERSON>", "stakeRewardsHistoryEmptyList": "Ödül yok", "stakeRewardsTime_zero": "<PERSON><PERSON><PERSON><PERSON>", "stakeRewardsTime_one": "<PERSON><PERSON><PERSON>", "stakeRewardsTime_other": "{{count}} gün <PERSON><PERSON>", "stakeRewardsItemsPagingFailed": "<PERSON><PERSON> <PERSON>.", "stakeAccountCreateAndDelegateErrorStaking": "Bu doğrulayıcıya stake edilirken bir sorun oluştu. Lütfen tekrar deneyin.", "stakeAccountCreateAndDelegateSolStaked": "SOL Stake Edildi!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Stake hesabı aktif olduğunda birkaç gün içinde <1></1> SOL'ünüz ödül kazanmaya başlayacak.", "stakeAccountCreateAndDelegateStakingFailed": "<PERSON><PERSON>", "stakeAccountCreateAndDelegateStakingSol": "SOL stake ediliyor...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Stake hesabı oluşturuyoruz, ardından SOL'ünüz şuraya delege edilecek:", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Stake hesabı oluşturuyoruz, ardından SOL'ünüz şuraya delege edilecek: {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "İşlemi Görüntüle", "stakeAccountDeactivateStakeSolUnstaked": "SOL Stake'i Kaldırıldı!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Stake hesabınız aktif olduğunda birkaç gün içinde <1></1> stake'inizi çekebileceksiniz.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Stake hesabınız etkin olmadığında sonraki birkaç gün içinde stake'inizi çekebileceksiniz.", "stakeAccountDeactivateStakeUnstakingFailed": "Stake Kaldırma Başarısız", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Bu doğrulayıcıdan stake kaldırılırken bir sorun oluştu. Lütfen tekrar deneyin.", "stakeAccountDeactivateStakeUnstakingSol": "SOL stake'i kaldırılıyor...", "stakeAccountDeactivateStakeUnstakingSolDescription": "SOL'ünüzün stake'ini kaldırmak için işleme başlıyoruz.", "stakeAccountDeactivateStakeViewTransaction": "İşlemi Görüntüle", "stakeAccountDelegateStakeSolStaked": "SOL Stake Edildi!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Stake hesabı aktif olduğunda birkaç gün içinde <1></1> SOL'ünüz ödül kazanmaya başlayacak.", "stakeAccountDelegateStakeStakingFailed": "<PERSON><PERSON>", "stakeAccountDelegateStakeStakingFailedDescription": "Bu onaylayıcıya stake edilirken bir sorun oluştu. Lütfen tekrar deneyin.", "stakeAccountDelegateStakeStakingSol": "SOL stake ediliyor...", "stakeAccountDelegateStakeStakingSolDescription": "SOL'ünüzü delege ediyoruz.", "stakeAccountDelegateStakeViewTransaction": "İşlemi Görüntüle", "stakeAccountListActivationActivating": "Etkinleştiriliyor", "stakeAccountListActivationActive": "<PERSON><PERSON><PERSON>", "stakeAccountListActivationInactive": "<PERSON><PERSON><PERSON>", "stakeAccountListActivationDeactivating": "Etkisizleştiriliyor", "stakeAccountListErrorFetching": "Stake hesapları getirilemedi. Lütfen daha sonra tekrar deneyin.", "stakeAccountListNoStakingAccounts": "Stake Hesabı Yok", "stakeAccountListReload": "<PERSON><PERSON><PERSON>", "stakeAccountListViewPrimaryText": "Stake'iniz", "stakeAccountListViewStakeSOL": "SOL stake et", "stakeAccountListItemStakeFee": "{{fee}} ücret", "stakeAccountViewActionButtonRestake": "Yeniden stake et", "stakeAccountViewActionButtonUnstake": "Stake'i kaldır", "stakeAccountViewError": "<PERSON><PERSON>", "stakeAccountViewPrimaryText": "Stake'iniz", "stakeAccountViewRestake": "Yeniden stake et", "stakeAccountViewSOLCurrentlyStakedInterpolated": "SOL'ünüz şu anda bir doğrulayıcıyla stake edilmiş durumda. Bu fonlara erişmek için <1></1>stake'i kaldırmanız gerekiyor. <3>Daha fazla bilgi</3>", "stakeAccountViewStakeInactive": {"part1": "Bu stake hesabı etkin değil. Stake'inizi çekebilir veya delege edilecek bir doğrulayıcı bulabilirsiniz.", "part2": "<PERSON><PERSON> fazla bilgi"}, "stakeAccountViewStakeNotFound": "Bu stake hesabı bulunamadı.", "stakeAccountViewViewOnExplorer": "{{explorer}}'<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewWithdrawStake": "<PERSON><PERSON>'<PERSON>ek", "stakeAccountViewWithdrawUnstakedSOL": "Stake Edilmeyen SOL'ü çek", "stakeAccountInsufficientFunds": "Hisseleri geri stake'lemek veya çekmek için yeterli SOL mevcut değil.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL Çekildi!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL'ünüz çekildi.", "part2": "Bu stake hesabı birkaç dakika içinde otomatik olarak kaldırılacak."}, "stakeAccountWithdrawStakeViewTransaction": "İşlemi Görüntüle", "stakeAccountWithdrawStakeWithdrawalFailed": "Çekme İşlemi Tamamlandı", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Bu stake hesabından çekme işlemi yapılırken bir sorun oluştu. Lütfen tekrar deneyin.", "stakeAccountWithdrawStakeWithdrawingSol": "SOL çekiliyor...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Bu stake hesabınızdan SOL'ünüzü çekiyoruz.", "startEarningSolAccount": "hesap", "startEarningSolAccounts": "hesaplar", "startEarningSolErrorClosePhantom": "<PERSON><PERSON><PERSON> doku<PERSON>p tekrar den<PERSON>in.", "startEarningSolErrorTroubleLoading": "Stake yüklenirken sorun oluştu", "startEarningSolLoading": "Yükleniyor...", "startEarningSolPrimaryText": "SOL kazanmaya başlayın", "startEarningSolSearching": "Stake etme hesapları aranıyor", "startEarningSolStakeTokens": "Token stake edin ve ödüller kazanın", "startEarningSolYourStake": "Stake'iniz", "unwrapFungibleTitle": "{{tokenSymbol}} ile takas et", "unwrapFungibleDescription": "{{toToken}} i<PERSON><PERSON>: {{fromToken}}", "unwrapFungibleConfirmSwap": "Takas<PERSON> onayla", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "<PERSON><PERSON><PERSON>", "swapFeesFees": "<PERSON><PERSON><PERSON>", "swapFeesPhantomFee": "Phantom Ücreti", "swapFeesPhantomFeeDisclaimer": "Her zaman en iyi likidite sağlayıcılarından olası en iyi fiyatı buluruz. {{feePercentage}} oranında bir ücret otomatik olarak bu teklife eklenir.", "swapFeesRate": "<PERSON><PERSON><PERSON>", "swapFeesRateDisclaimer": "Birden çok merkezsizleştirilmiş borsada Jupiter Aggregator tarafından bulunan en uygun fiyat.", "swapFeesRateDisclaimerMultichain": "Birden çok merkezsizleştirilmiş borsadaki en uygun fiyat.", "swapFeesPriceImpact": "<PERSON><PERSON>t <PERSON>", "swapFeesHighPriceImpact": "Yüksek Fiyat Etkisi", "swapFeesPriceImpactDisclaimer": "İşlem hacminize dayalı olarak piyasa fiyatı ve tahmini fiyat arasındaki fark.", "swapFeesSlippage": "<PERSON><PERSON>", "swapFeesHighSlippage": "Yüksek Kayma Toleransı", "swapFeesHighSlippageDisclaimer": "İşleminiz fiyat %{{slippage}} oranından daha fazla aleyhte değişirse başarısız olacak.", "swapTransferFee": "Transfer Ücreti", "swapTransferFeeDisclaimer": "${{symbol}} <PERSON><PERSON><PERSON><PERSON><PERSON>, Phantom tarafından <PERSON>, token oluşturucusu tarafından belirlenen %{{feePercent}} transfer ücretine tabidir.", "swapTransferFeeDisclaimerMany": "Seçilen token'ların alım satımı Phantom tarafından değil, token yaratıcıları tarafından belirlenen bir %{{feePercent}} ücrete tabidir.", "swapFeesSlippageDisclaimer": "İşleminizin fiyatının sağlanan tekliften sapabileceği miktar.", "swapFeesProvider": "Sağlayıcı", "swapFeesProviderDisclaimer": "İşleminizi tamamlamak için kullanılan merkezsizleştirilmiş borsa.", "swapEstimatedTime": "<PERSON><PERSON><PERSON><PERSON>", "swapEstimatedTimeShort": "<PERSON><PERSON><PERSON> s<PERSON>re", "swapEstimatedTimeDisclaimer": "Köprü i<PERSON>in tahmin<PERSON> tama<PERSON>ü<PERSON>, <PERSON>şlem hızlarını etkileyen çeşitli faktörlere dayalı olarak farklılık gösterir.", "swapSettingsButtonCommand": "Takas Ayarlarını aç", "swapQuestionRetry": "Te<PERSON>r denensin mi?", "swapUnverifiedTokens": "Doğrulanmamış Token'lar", "swapSectionTitleTokens": "{{section}} Token", "swapFlowYouPay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapFlowYouReceive": "Aldığınız", "swapFlowActionButtonText": "Sipar<PERSON><PERSON><PERSON>", "swapAssetCardTokenNetwork": "{{network}} üzerinde {{symbol}}", "swapAssetCardMaxButton": "Ma<PERSON>.", "swapAssetCardSelectTokenAndNetwork": "Token ve Ağ seçin", "swapAssetCardBuyTitle": "Alacağınız:", "swapAssetCardSellTitle": "Ödeyeceğiniz:", "swapAssetWarningUnverified": "Bu token doğrulanmamış. Yalnızca güvendiğiniz token'larla etkileşim kurun.", "swapAssetWarningPermanentDelegate": "<PERSON><PERSON> delege, bu jetonları kalıcı olarak yakabilir veya transfer edebilir.", "swapSlippageSettingsTitle": "<PERSON><PERSON>", "swapSlippageSettingsSubtitle": "<PERSON><PERSON><PERSON>, kaymadan daha fazla değişirse işleminiz başarısız olacak. Çok yüksek bir <PERSON>, ale<PERSON><PERSON>e işlemle sonuçlanır.", "swapSlippageSettingsCustom": "<PERSON><PERSON>", "swapSlippageSettingsHighSlippageWarning": "İşleminiz frontrun olabilir ve aleyhte bir işlemle sonuçlanabilir.", "swapSlippageSettingsCustomMinError": "Lütfen %{{minSlippage}} değerinden daha büyük bir değer girin.", "swapSlippageSettingsCustomMaxError": "Lütfen %{{maxSlippage}} değerinden daha küçük bir değer girin.", "swapSlippageSettingsCustomInvalidValue": "Lütfen geçerli bir değer girin.", "swapSlippageSettingsAutoSubtitle": "Phantom, başarıl<PERSON> bir takas için en düşük kaymayı bulur.", "swapSlippageSettingsAuto": "Otomatik", "swapSlippageSettingsFixed": "Sabit", "swapSlippageOptInTitle": "Otomatik <PERSON>", "swapSlippageOptInSubtitle": "Phantom, başarılı bir takas için en düşük kaymayı bulacak. <PERSON><PERSON><PERSON> dilediğiniz zaman Swapper → <PERSON><PERSON> A<PERSON>larından değiştirebilirsiniz.", "swapSlippageOptInEnableOption": "Otomatik Kaymayı etkinleştir", "swapSlippageOptInRejectOption": "<PERSON><PERSON> et", "swapQuoteFeeDisclaimer": "<PERSON><PERSON><PERSON><PERSON>, {{feePercentage}} Phantom ücretini de içerir", "swapQuoteMissingContext": "Takas teklif bağlamı eksik", "swapQuoteErrorNoQuotes": "Teklifsiz takas yapılmaya çalışılınıyor", "swapQuoteSolanaNetwork": "Solana ağı", "swapQuoteNetwork": "Ağ", "swapQuoteOneTimeSerumAccount": "Tek seferlik Serum hesabı", "swapQuoteOneTimeTokenAccount": "Tek seferlik token hesabı", "swapQuoteBridgeFee": "Çapraz Zincir <PERSON>", "swapQuoteDestinationNetwork": "Destinasyon Ağ", "swapQuoteLiquidityProvider": "Likidite Sağlayıcı", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON> et", "swapReviewFlowPrimaryText": "Sipar<PERSON><PERSON><PERSON>", "swapReviewFlowYouPay": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapReviewFlowYouReceive": "Aldığınız", "swapReviewInsufficientBalance": "<PERSON><PERSON><PERSON>", "ugcSwapWarningTitle": "Uyarı", "ugcSwapWarningBody1": "Bu token {{programName}} token launcher üzerinde işlem görüyor.", "ugcSwapWarningBody2": "Bu token'ların değeri büyük ölçüde dalgalanabilir ve önemli finansal kazançlara veya kayıplara yol açabilir. Lütfen riski kendinize ait olmak üzere işlem yapın.", "ugcSwapWarningConfirm": "Anlıyorum", "bondingCurveProgressLabel": "Bonding <PERSON><PERSON><PERSON>", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Bonding curve modelinde, token fiyatları eğrinin şekline göre belirlenir. Daha fazla token satın alındıkça artar ve token satıldık<PERSON> azalır. Token'lar sat<PERSON>, tüm likidite Raydium'a yatırılır ve yakılır.", "ugcFungibleWarningBanner": "Bu token {{programName}} üzerinde işlem görüyor", "ugcCreatedRowLabel": "Oluşturulma Tarihi:", "ugcStatusRowLabel": "Durum", "ugcStatusRowValue": "Mezun edildi", "swapTxConfirmationReceived": "Alındı!", "swapTxConfirmationSwapFailed": "Takas başarısız", "swapTxConfirmationSwapFailedStaleQuota": "Teklif artık geçerli değil. Lütfen tekrar deneyin.", "swapTxConfirmationSwapFailedSlippageLimit": "Kaymanız bu takas için fazla düşük. Lütfen Takas ekranının en üstünden kaymanızı artırıp tekrar deneyin.", "swapTxConfirmationSwapFailedInsufficientBalance": "Talebi tamamlayamadık. İşlemi tamamlamak için yeterli bakiyeniz yok.", "swapTxConfirmationSwapFailedEmptyRoute": "Bu token paritesi için likidite değişti. Uygun bir teklif bulunamadı. Lütfen tekrar deneyin veya token miktarlarını değiştirin.", "swapTxConfirmationSwapFailedAcountFrozen": "Bu token, yaratıcısı tarafından dondurulmuş. Bu token'ı gönderemez veya takas edemezsiniz.", "swapTxConfirmationSwapFailedTryAgain": "<PERSON>kas ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, lütfen tekrar deneyin", "swapTxConfirmationSwapFailedUnknownError": "Takası tamamlayamadık. Fonlarınızın hiçbir kısmı etkilenmedi. Lütfen tekrar deneyin. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Takası simüle edemedik. Fonlarınızın hiçbir kısmı etkilenmedi. Lütfen tekrar deneyin.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Takası tamamlayamadık. Fonlarınızın hiçbir kısmı etkilenmedi. Lütfen tekrar deneyin. ", "swapTxConfirmationSwapFailedInsufficientGas": "İşlemi tamamlamak için hesabınızda yeterli fon yok. Lütfen hesabınıza daha fazla fon ekleyip tekrar deneyin.", "swapTxConfirmationSwapFailedLedgerReject": "<PERSON><PERSON>, donanım cihazındaki kullanıcı tarafından reddedildi.", "swapTxConfirmationSwapFailedLedgerConnectionError": "<PERSON><PERSON>, cihaz bağlantı hatası nedeniyle reddedildi. Lütfen tekrar deneyin.", "swapTxConfirmationSwapFailedLedgerSignError": "<PERSON><PERSON>, cihaz imza hatası nedeniyle reddedildi. Lütfen tekrar deneyin.", "swapTxConfirmationSwapFailedLedgerError": "<PERSON><PERSON>, cihaz hatası nedeniyle reddedildi. Lütfen tekrar deneyin.", "swapTxConfirmationSwappingTokens": "Token'lar takas ediliyor...", "swapTxConfirmationTokens": "To<PERSON>'lar", "swapTxConfirmationTokensDeposited": "Bitti! Token'lar cüzdanınıza yatırıldı", "swapTxConfirmationTokensDepositedTitle": "<PERSON><PERSON>!", "swapTxConfirmationTokensDepositedBody": "Token'lar cü<PERSON>ınıza yatırıldı", "swapTxConfirmationTokensWillBeDeposited": "işlem tamamlandığında cüzdanınıza yatırılacak", "swapTxConfirmationViewTransaction": "İşlemi Görüntüle", "swapTxBridgeSubmitting": "İşlem gönderiliyor", "swapTxBridgeSubmittingDescription": "{{buyNetwork}} üzerinde {{buyAmount}} karşılığında {{sellNetwork}} üzerinde {{sellAmount}} takas ediliyor", "swapTxBridgeFailed": "İşlem Gönderilemedi", "swapTxBridgeFailedDescription": "İstek tamamlanamadı.", "swapTxBridgeSubmitted": "İşlem Gönderildi", "swapTxBridgeSubmittedDescription": "<PERSON><PERSON><PERSON> Süresi: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON><PERSON> pencer<PERSON>i gü<PERSON> kapa<PERSON>bilirsiniz.", "swapperSwitchTokens": "Token'ları <PERSON>r", "swapperMax": "Ma<PERSON>.", "swapperTooltipNetwork": "Ağ", "swapperTooltipPrice": "<PERSON><PERSON><PERSON>", "swapperTooltipAddress": "S<PERSON>zleşme", "swapperTrendingSortBy": "Sıralama ölçütü", "swapperTrendingTimeFrame": "Zaman Çerçevesi", "swapperTrendingNetwork": "Ağ", "swapperTrendingRank": "<PERSON><PERSON><PERSON>", "swapperTrendingTokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swapperTrendingVolume": "Hacim", "swapperTrendingPrice": "<PERSON><PERSON><PERSON>", "swapperTrendingPriceChange": "<PERSON><PERSON><PERSON>", "swapperTrendingMarketCap": "<PERSON><PERSON><PERSON>", "swapperTrendingTimeFrame1h": "1 sa", "swapperTrendingTimeFrame24h": "24 sa", "swapperTrendingTimeFrame7d": "7 g", "swapperTrendingTimeFrame30d": "30 g", "swapperTrendingNoTokensFound": "Token bulunamadı.", "switchToggle": "Geçiş Yap", "termsOfServiceActionButtonAgree": "Kabul Ediyorum", "termsOfServiceDisclaimerFeesDisabledInterpolated": "<1>\"Kabul Ediyorum\"</1> <PERSON><PERSON><PERSON><PERSON><PERSON> tıklayarak Phantom'la token takas etme <3>Hüküm ve Koşullarını</3> kabul etmiş olursunuz.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Hizmet Koşullarımızı revize ettik. <1>\"Kabul Ediyorum\"</1> d<PERSON><PERSON><PERSON>ine tıklayarak yeni <3>Hizmet Koşullarımızı</3> kabul etmiş olursunuz.<5></5><6></6>Yeni Hizmet Koşullarımız belirli ürünler için yeni bir <8>ücret yapısı</8> içerir.", "termsOfServicePrimaryText": "Hizmet Koşulları", "tokenRowUnknownToken": "Bilinmeyen Token", "transactionsAppInteraction": "<PERSON>y<PERSON><PERSON><PERSON>", "transactionsFailedAppInteraction": "Uygulama etkileşimi başarısız", "transactionsBidOnInterpolated": "{{name}} i<PERSON><PERSON> tek<PERSON>f ver", "transactionsBidFailed": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON>", "transactionsBoughtInterpolated": "{{name}} satın aldı", "transactionsBoughtCollectible": "Koleksiyonluk alındı", "transactionBridgeInitiated": "Köprü Başlatıldı", "transactionBridgeInitiatedFailed": "Köprü Başlatma İşlemi Başarısız", "transactionBridgeStatusLink": "LI.FI'da Durumu kontrol edin", "transactionsBuyFailed": "Satın alma başarısız", "transactionsBurnedSpam": "Yakılmış spam", "transactionsBurned": "Yakıldı", "transactionsUnwrapped": "Kaydırıldı", "transactionsUnwrappedFailed": "<PERSON><PERSON><PERSON><PERSON> başarıs<PERSON><PERSON>", "transactionsCancelBidOnInterpolated": "{{name}} üzerine teklif iptal edildi", "transactionsCancelBidOnFailed": "<PERSON><PERSON><PERSON><PERSON> i<PERSON> edile<PERSON>i", "transactionsError": "<PERSON><PERSON>", "transactionsFailed": "Başarısız", "transactionsSwapped": "<PERSON><PERSON> edildi", "transactionsFailedSwap": "Takas başarısız", "transactionsFailedBurn": "<PERSON><PERSON><PERSON> başar<PERSON>s<PERSON><PERSON>", "transactionsFrom": "<PERSON><PERSON>", "transactionsListedInterpolated": "{{name}} listeledi", "transactionsListedFailed": "Listelenemedi", "transactionsNoActivity": "Aktivite yok", "transactionsReceived": "Alındı", "transactionsReceivedInterpolated": "{{amount}} SOL alındı", "transactionsSending": "Gönderiliyor...", "transactionsPendingCreateListingInterpolated": "{{name}} oluşturuluyor", "transactionsPendingEditListingInterpolated": "{{name}} düzenleniyor", "transactionsPendingSolanaPayTransaction": "Solana Ödeme İşlemi Onaylanıyor", "transactionsPendingRemoveListingInterpolated": "{{name}} listed<PERSON>", "transactionsPendingBurningInterpolated": "{{name}} yakılı<PERSON>r", "transactionsPendingSending": "G<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsPendingSwapping": "<PERSON><PERSON> edili<PERSON>r", "transactionsPendingBridging": "K<PERSON><PERSON>r<PERSON><PERSON><PERSON>", "transactionsPendingApproving": "Onaylanıyor", "transactionsPendingCreatingAndDelegatingStake": "Stake oluşturuluyor ve delege ediliyor", "transactionsPendingDeactivatingStake": "Stake etkisizleştiriliyor", "transactionsPendingDelegatingStake": "Stake delege ediliyor", "transactionsPendingWithdrawingStake": "Stake çek<PERSON>", "transactionsPendingAppInteraction": "<PERSON><PERSON><PERSON> u<PERSON>gu<PERSON> et<PERSON>i", "transactionsPendingBitcoinTransaction": "BTC işlemi beklemede", "transactionsSent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsSendFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsSwapOn": "{{dappName}} üzerinde takas edin", "transactionsSentInterpolated": "{{amount}} SOL gönderildi", "transactionsSoldInterpolated": "{{name}} sattı", "transactionsSoldCollectible": "Koleksiyonluk satıldı", "transactionsSoldFailed": "Satış başarısız", "transactionsStaked": "<PERSON><PERSON>", "transactionsStakedFailed": "Stake başarısız", "transactionsSuccess": "Başarılı", "transactionsTo": "<PERSON><PERSON>", "transactionsTokenSwap": "Token Takası", "transactionsUnknownAmount": "Bilinmeyen", "transactionsUnlistedInterpolated": "{{name}} listed<PERSON>", "transactionsUnstaked": "Stake Kaldırıldı", "transactionsUnlistedFailed": "<PERSON>en kaldırılamadı", "transactionsDeactivateStake": "Stake etkisizleştirildi", "transactionsDeactivateStakeFailed": "Stake etkisizleştirmesi başarısız", "transactionsWaitingForConfirmation": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsWithdrawStake": "<PERSON><PERSON>'<PERSON>ek", "transactionsWithdrawStakeFailed": "Stake kaldırma başarısız", "transactionCancelled": "İptal Edildi", "transactionCancelledFailed": "İptal edilemedi", "transactionApproveToken": "Onaylandı {{tokenSymbol}}", "transactionApproveTokenFailed": "Onaylanamadı {{tokenSymbol}}", "transactionApprovalFailed": "<PERSON><PERSON> başarı<PERSON>ı<PERSON>", "transactionRevokeApproveToken": "{{tokenSymbol}} geri <PERSON><PERSON><PERSON>", "transactionRevokeApproveTokenFailed": "{{tokenSymbol}} geri <PERSON><PERSON><PERSON>", "transactionRevokeFailed": "<PERSON><PERSON> <PERSON> ba<PERSON>ı<PERSON><PERSON>", "transactionApproveDetailsTitle": "<PERSON><PERSON>", "transactionCancelOrder": "Tek<PERSON>fi iptal et", "transactionCancelOrderFailed": "<PERSON><PERSON> ed<PERSON><PERSON>", "transactionApproveAppLabel": "Uygulama", "transactionApproveAmountLabel": "<PERSON><PERSON><PERSON>", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "Koleksiyon", "transactionApproveAllItems": "<PERSON><PERSON><PERSON>", "transactionSpendUpTo": "<PERSON><PERSON> tutara kadar ha<PERSON>ın:", "transactionCancel": "İşlemi iptal et", "transactionPrioritizeCancel": "İptal İşlemini Önceliklendir", "transactionSpeedUp": "İşlemi Hızlandır", "transactionCancelHelperText": "Asıl işlem iptal edilmeden önce tamamlanabilir.", "transactionSpeedUplHelperText": "B<PERSON>, ağ koşullarına dayalı olarak işleminizin hızını en üst seviyeye çıkarır.", "transactionCancelHelperMobile": "Bu işlemi iptal etmeyi denemenin ücreti <1>en fazla {{amount}}</1> olacak. Asıl işlem iptal edilmeden önce tamamlanabilir.", "transactionCancelHelperMobileWithEstimate": "Bu işlemi iptal etme denemesinin ücreti <1>en fazla {{amount}}</1> olacak. Yaklaşık {{timeEstimate}} içinde tamamlanabilir. As<PERSON>l işlem, iptal edilmeden önce tamamlanabilir.", "transactionSpeedUpHelperMobile": "Bu <PERSON><PERSON><PERSON>in hızını en üst seviyeye çıkarmanın ücreti <1>en fazla {{amount}}</1> olacak.", "transactionSpeedUpHelperMobileWithEstimate": "Bu i<PERSON><PERSON>in hızını en üst seviyeye çıkarmanın ücreti <1>en fazla {{amount}}</1> olacak. Yaklaşık {{timeEstimate}} içinde tamamlanır.", "transactionEstimatedTime": "<PERSON><PERSON><PERSON>", "transactionCancelingSend": "<PERSON><PERSON><PERSON>me iptal ediliyor", "transactionPrioritizingCancel": "İptal işlemi önceliklendiriliyor", "transactionCanceling": "İptal ediliyor", "transactionReplaceError": "Bir hata oluştu. Hesabınıza hiçbir ücret yansıtılmadı. Tekrar deneyebilirsiniz.", "transactionNotEnoughNative": "Yetersiz {{nativeTokenSymbol}}", "transactionGasLimitError": "Gaz limiti tahmin edile<PERSON>i", "transactionGasEstimationError": "<PERSON>az tahmin ed<PERSON><PERSON>i", "pendingTransactionCancel": "İptal et", "pendingTransactionSpeedUp": "Hızlandır", "pendingTransactionStatus": "Durum", "pendingTransactionPending": "Bekliyor", "pendingTransactionPendingInteraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingTransactionCancelling": "İptal ediliyor", "pendingTransactionDate": "<PERSON><PERSON><PERSON>", "pendingTransactionNetworkFee": "<PERSON><PERSON>", "pendingTransactionEstimatedTime": "<PERSON><PERSON><PERSON>", "pendingTransactionEstimatedTimeHM": "{{hours}} sa {{minutes}} dk", "pendingTransactionEstimatedTimeMS": "{{minutes}} dk {{seconds}} sn", "pendingTransactionEstimatedTimeS": "{{seconds}} sn", "pendingTransactionsSendingTitle": "{{assetSymbol}} gönderiliyor", "pendingTransactionsUnknownEstimatedTime": "Bilinmeyen", "pendingTransactionUnknownApp": "Bilinmeyen Uygulama", "permanentDelegateTitle": "Devredilmiş", "permanentDelegateValue": "Kalıcı", "permanentDelegateTooltipTitle": "Kalıcı Devretme", "permanentDelegateTooltipValue": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kendi adı<PERSON> başka bir hesabın token'ları yönetebilmesini sağlar ve bu, yakmayı veya transfer etmeyi içerir.", "unlockActionButtonUnlock": "<PERSON><PERSON><PERSON>", "unlockEnterPassword": "Şifrenizi girin", "unlockErrorIncorrectPassword": "Yanlış şifre", "unlockErrorSomethingWentWrong": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ters gitti, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "unlockForgotPassword": "<PERSON><PERSON><PERSON><PERSON> unuttum", "unlockPassword": "Şifre", "forgotPasswordText": "Cüzdanınızın 12-24 kelimelik kurtarma ifadesini girerek şifrenizi sıfırlayabilirsiniz. Phantom sizin için şifrenizi kurtaramaz.", "appInfo": "<PERSON>y<PERSON><PERSON><PERSON>", "lastUsed": "<PERSON>", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Donanım hesaplarıyla kullanılamıyor.", "trustedAppAutoConfirmDisclaimer1": "Aktif <PERSON> Phantom bu uygulamadan tüm istekleri size bildirim göndermeden ve sizden onay istemeden onaylar.", "trustedAppAutoConfirmDisclaimer2": "Etkinleştirmek, fonlarınızı dolandırıcılık riskine sokabilir. Bu özelliği yalnızca güvendiğiniz uygulamalarla kullanın.", "validationUtilsPasswordIsRequired": "<PERSON><PERSON><PERSON>", "validationUtilsPasswordLength": "Şifre 8 karakter uzunluğunda olmalı", "validationUtilsPasswordsDontMatch": "<PERSON><PERSON><PERSON><PERSON> eşleşmiyor", "validationUtilsPasswordCantBeSame": "Eski şifrenizi kullanamazsınız", "validatorCardEstimatedApy": "<PERSON><PERSON><PERSON>Y", "validatorCardCommission": "Komisyon", "validatorCardTotalStake": "Toplam Stake", "validatorCardNumberOfDelegators": "Delegatör <PERSON>ı", "validatorListChooseAValidator": "Doğrulayıcı seçin", "validatorListErrorFetching": "Doğrulayıcılar getirilemedi. Lütfen daha sonra tekrar deneyin.", "validatorListNoResults": "<PERSON><PERSON><PERSON>", "validatorListReload": "<PERSON><PERSON><PERSON>", "validatorInfoTooltip": "Doğrulayıcı", "validatorInfoTitle": "Doğrulayı<PERSON>ılar", "validatorInfoDescription": "SOL'unuzu bir doğrulayıcıda stake ederek Solana ağının performansı ve güvenliğine katkıda bulunur ve karşılığında SOL kazanırsınız.", "validatorApyInfoTooltip": "Tah. APY", "validatorApyInfoTitle": "<PERSON><PERSON><PERSON>Y", "validatorApyInfoDescription": "B<PERSON>, doğrulayıcıda SOL'unuzu stake etme karşılığında kazandığınız getiri oranıdır.", "validatorViewActionButtonStake": "Stake", "validatorViewErrorFetching": "Doğrulayıcı getirilemedi.", "validatorViewInsufficientBalance": "<PERSON><PERSON><PERSON>", "validatorViewMax": "Ma<PERSON>.", "validatorViewPrimaryText": "Stake Etmeye Başlayın", "validatorViewDescriptionInterpolated": "Bu doğrulayıcıya kaç SOL <1></1> stake etmek istediğinizi seçin. <3>Daha fazla bilgi edinin</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "Stake etmek için {{amount}} SOL gerekli", "validatorViewValidator": "Doğrulayıcı", "walletMenuItemsAddConnectWallet": "Cüzdan Ekleyin / Bağlayın", "walletMenuItemsBridgeAssets": "Köprü Varlıkları", "walletMenuItemsHelpAndSupport": "Yardım ve Destek", "walletMenuItemsLockWallet": "Cüzdanı Kilitle", "walletMenuItemsResetSecretPhrase": "Gizli Tümceciği Sıfırla", "walletMenuItemsShowMoreAccounts": "{{count}} tane daha g<PERSON>...", "walletMenuItemsHideAccounts": "Hesapları gizle", "toggleMultiChainHeader": "Çoklu Zincir", "disableMultiChainHeader": "Yalnızca Solana modu", "disableMultiChainDetail1Header": "<PERSON><PERSON><PERSON><PERSON> her <PERSON><PERSON> ya<PERSON>ın", "disableMultiChainDetail1SecondaryText": "Solana <PERSON>ınızı, token'ların<PERSON>zı ve koleksiyonluklarınızı diğer zincirleri görmeden yönetin.", "disableMultiChainDetail2Header": "Dilediğiniz zaman Çoklu Zincire dönün", "disableMultiChainDetail2SecondaryText": "Mevcut Ethereum ve Polygon bakiyeleriniz, Çoklu Zinciri yeniden etkinleştirdiğinizde korunur.", "disableMultiChainButton": "Yalnızca Solana'yı etkinleştir", "disabledMultiChainHeader": "Yalnızca Solana Etkinleştirildi", "disabledMultiChainText": "Çoklu zinciri dilediğiniz zaman yeniden etkinleştirebilirsiniz.", "enableMultiChainHeader": "Çoklu Zinciri etkin<PERSON>tir", "enabledMultiChainHeader": "Çoklu Zincir <PERSON>", "enabledMultiChainText": "Ethereum ve Polygon artık cüzdanınızda destekleniyor.", "incompatibleAccountHeader": "Uyumsuz Hesap", "incompatibleAccountInterpolated": "Yalnızca Solana modunu etkinleştirmeden önce bu yalnızca Ethereum hesaplarını lütfen kaldırın: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "<PERSON><PERSON><PERSON><PERSON>!", "welcomeToMultiChainPrimaryText": "Her Şey için Tek Cüzdan", "welcomeToMultiChainDetail1Header": "Ethereum ve Polygon desteği", "welcomeToMultiChainDetail1SecondaryText": "Solana, Ethereum ve Polygon'dan tüm token'larınız ve NFT'leriniz tek bir yerde.", "welcomeToMultiChainDetail2Header": "Sevdiğiniz tüm uygulamaları kullanın", "welcomeToMultiChainDetail2SecondaryText": "<PERSON><PERSON>tirmeden birden fazla zincirde uygulama bağlayın.", "welcomeToMultiChainDetail3Header": "MetaMask cüzdanınızı içe aktarın", "welcomeToMultiChainDetail3SecondaryText": "Ethereum ve Polygon'da tüm tohum ifadelerinizi kolayca içe aktarın.", "welcomeToMultiChainIntro": "Phantom Multichain'e ho<PERSON> geldiniz", "welcomeToMultiChainIntroDesc": "Solana, Ethereum ve Polygon'dan tüm token'larınız ve NFT'leriniz tek bir yerde. Her şey için tek cüzdanınız.", "welcomeToMultiChainAccounts": "Çoklu Zincir Hesapları yeniden tasarlandı", "welcomeToMultiChainAccountsDesc": "Çoklu zincir için ye<PERSON> ta<PERSON>, artık her bir hesap, ilgili ETH ve Polygon adreslerine sahip.", "welcomeToMultiChainApps": "Her Yerde Çalışır", "welcomeToMultiChainAppsDesc": "Phantom; Ethereum, Polygon ve Sol<PERSON>'daki her uygulamayla uyumludur. \"MetaMask'e bağlan\" dü<PERSON><PERSON>ine tıkladığınızda başlamaya hazırsınız.", "welcomeToMultiChainImport": "MetaMask'ten anında içe aktarın", "welcomeToMultiChainImportDesc": "MetaMask veya Coinbase Wallet gibi cüzdanlardan Gizli İfadelerinizi veya Özel Anahtarlarınızı içe aktarın. Tümü tek bir yerde.", "welcomeToMultiChainImportInterpolated": "MetaMask veya Coinbase Wallet gibi cüzdanlardan <0>Gizli İfadelerinizi veya Özel Anahtarlarınızı içe aktarın</0>. Tümü tek bir yerde.", "welcomeToMultiChainTakeTour": "<PERSON><PERSON>", "welcomeToMultiChainSwapperTitle": "Ethereum, Polygon\nve Solana'da takas yapın", "welcomeToMultiChainSwapperDetail1Header": "Ethereum ve Polygon desteği", "welcomeToMultiChainSwapperDetail1SecondaryText": "Artık ERC-20 token'larını cüzdanınızın içinden kolayca takas edebilirsiniz.", "welcomeToMultiChainSwapperDetail2Header": "En İyi Fiyatlar ve Süper Düşük Ücretler", "welcomeToMultiChainSwapperDetail2SecondaryText": "100+ likidite kaynağı ve maksimum getiri karşılığında akıllı emir yönlendirme.", "networkErrorTitle": "<PERSON><PERSON>", "networkError": "Ne yazık ki ağa erişemiyoruz. Lütfen daha sonra tekrar deneyin.", "authenticationUnlockPhantom": "Phantom'ın kilidini aç", "errorAndOfflineSomethingWentWrong": "<PERSON><PERSON> ters gitti", "errorAndOfflineSomethingWentWrongTryAgain": "Lütfen tekrar deneyin.", "errorAndOfflineUnableToFetchAssets": "Varlıklar getirilemedi. Lütfen daha sonra tekrar deneyin.", "errorAndOfflineUnableToFetchCollectibles": "Koleksiyonluklar getirilemedi. Lütfen daha sonra tekrar deneyin.", "errorAndOfflineUnableToFetchSwap": "Takas bilgileri getirilemedi. Lütfen daha sonra tekrar deneyin.", "errorAndOfflineUnableToFetchTransactionHistory": "<PERSON><PERSON> anda işlem geçmişinizi getiremiyoruz. <PERSON>ğ bağlantınızı kontrol edin veya daha sonra tekrar deneyin.", "errorAndOfflineUnableToFetchRewardsHistory": "Ödül geçmişi getirilemedi. Lütfen daha sonra tekrar deneyin.", "errorAndOfflineUnableToFetchBlockedUsers": "Engellenen kullanıcılar getirilemedi. Lütfen daha sonra tekrar deneyin.", "networkHealthSheetCloseButtonText": "<PERSON><PERSON>", "swapReviewError": "Siparişiniz incelenirken bir şeyler ters gitti, lü<PERSON><PERSON> tekrar deneyin.", "sendSelectToken": "<PERSON><PERSON> seçin", "swapBalance": "Bakiye:", "swapTitle": "Token'lar<PERSON>", "swapSelectToken": "<PERSON><PERSON> seçin", "swapYouPay": "Ödeyeceğiniz", "swapYouReceive": "Alacağınız", "aboutPrivacyPolicy": "Gizlilik Politikası", "aboutVersion": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{version}}", "aboutVisitWebsite": "Web Sitesini Ziyaret <PERSON>t", "bottomSheetConnectTitle": "Bağla", "A11YbottomSheetConnectTitle": "Alt Sayfa Bağla", "A11YbottomSheetCommandClose": "Alt Sayfa Reddet", "A11YbottomSheetCommandBack": "Alt Sayfa Geri", "bottomSheetSignTypedDataTitle": "İmza mesa<PERSON>ı", "bottomSheetSignMessageTitle": "İmza mesa<PERSON>ı", "bottomSheetSignInTitle": "<PERSON><PERSON><PERSON> yap", "bottomSheetSignInAndConnectTitle": "Oturum aç", "bottomSheetConfirmTransactionTitle": "İşlem onayla", "bottomSheetConfirmTransactionsTitle": "İşlemleri onaylayın", "bottomSheetSolanaPayTitle": "Solana Pay İsteği", "bottomSheetAdvancedTitle": "Gelişmiş", "bottomSheetReadOnlyAccountTitle": "Yalnızca Görüntüleme Modu", "bottomSheetTransactionSettingsTitle": "<PERSON><PERSON>", "bottomSheetConnectDescription": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON>, bu <PERSON>nin seçili he<PERSON> için baki<PERSON>leri ve aktiviteyi görüntülemesine izin verir.", "bottomSheetSignInDescription": "Bu mesajı imzal<PERSON>, se<PERSON><PERSON> hesabın sahibi olduğunuzu kanıtlar. Yalnızca güvendiğiniz uygulamalardan gelen mesajları imzalayın.", "bottomSheetSignInAndConnectDescription": "<PERSON><PERSON><PERSON><PERSON>, bu <PERSON>nin seçili hesap için baki<PERSON>leri ve etkinliği görüntülemesine izin verir.", "bottomSheetConfirmTransactionDescription": "Bakiye değişiklikleri tahminîdir. İlgili tutar ve varlıklar garantili değildir.", "bottomSheetConfirmTransactionsDescription": "Bakiye değişiklikleri tahminîdir. İlgili tutar ve varlıklar garantili değildir.", "bottomSheetSignTypedDataDescription": "Bu yalnızca bir izin talebidir. İşlem hemen yürütülmeyebilir.", "bottomSheetSignTypedDataSecondDescription": "Bakiye değişiklikleri tahminîdir. İlgili tutar ve varlıklar garantili değildir.", "bottomSheetSignMessageDescription": "Bu mesajı imzal<PERSON>, seçili hesaba sahip olduğunuzu kanıtlar. Yalnızca güvendiğiniz uygulamalardan gelen mesajları imzalayın.", "bottomSheetReadOnlyAccountDescription": "Bu eylem yalnızca görüntüleme modunda gerçekleştirilemiyor.", "bottomSheetMessageRow": "<PERSON><PERSON>", "bottomSheetStatementRow": "<PERSON><PERSON>", "bottomSheetAutoConfirmRow": "Otomatik <PERSON>", "bottomSheetAutoConfirmOff": "<PERSON><PERSON><PERSON>", "bottomSheetAutoConfirmOn": "Açık", "bottomSheetAccountRow": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedRow": "Gelişmiş", "bottomSheetContractRow": "Sözleşme Adresi", "bottomSheetSpenderRow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bottomSheetNetworkRow": "Ağ", "bottomSheetNetworkFeeRow": "<PERSON><PERSON>", "bottomSheetEstimatedTimeRow": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Yalnızca güvendiğiniz web sitelerine bağlanın", "bottomSheetSignInRequestDisclaimer": "Yalnızca güvendiğiniz web sitelerine giriş yapın", "bottomSheetSignatureRequestDisclaimer": "Yalnızca bu web sitesine güveniyorsanız onaylayın.", "bottomSheetFeaturedTransactionDisclaimer": "Bir sonraki adımda onaylamadan önce işlemin bir ön izlemesini göreceksiniz.", "bottomSheetIgnoreWarning": "Uyarıyı yok say, yine de ilerle", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Bakiye değişikliği bulunamadı. Lütfen dikkatli bir şekilde ilerleyin ve yalnızca bu siteye güveniyorsanız onaylayın.", "bottomSheetReadOnlyWarning": "Bu adresi yalnızca izliyorsunuz. İşlemleri ve mesajları imzalamak için içe aktarmanız gerekecek.", "bottomSheetWebsiteIsUnsafeWarning": "Bu web sitesini kullanmak güvenli değil ve site, fonlarınızı çalmaya çalışabilir.", "bottomSheetViewOnExplorer": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bottomSheetTransactionSubmitted": "İşlem Gönderildi", "bottomSheetTransactionPending": "İşlem Beklemede", "bottomSheetTransactionFailed": "İşlem Başarısız", "bottomSheetTransactionSubmittedDescription": "İşleminiz gönderildi. İşlemi gezginde görüntüleyebilirsiniz.", "bottomSheetTransactionFailedDescription": "İşleminiz başarısız oldu. Lütfen tekrar deneyin.", "bottomSheetTransactionPendingDescription": "İşlem işleniyor...", "transactionsFromInterpolated": "Kimden: {{from}}", "transactionsFromParagraphInterpolated": "Kimden: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON><PERSON><PERSON>", "transactionsToInterpolated": "<PERSON><PERSON>: {{to}}", "transactionsToParagraphInterpolated": "<PERSON><PERSON>: {{to}}", "transactionsYesterday": "<PERSON><PERSON><PERSON>", "addEditAddressAdd": "<PERSON><PERSON>", "addEditAddressDelete": "<PERSON><PERSON><PERSON> sil", "addEditAddressDeleteTitle": "Bu adresi silmek istediğinizden emin misiniz?", "addEditAddressSave": "<PERSON><PERSON><PERSON>", "dAppBrowserComingSoon": "dApp Tarayıcı çok yakında çıkıyor!", "dAppBrowserSearchPlaceholder": "<PERSON><PERSON>, token'lar, URL", "dAppBrowserOpenInNewTab": "<PERSON><PERSON> sek<PERSON> aç", "dAppBrowserSuggested": "Önerilen", "dAppBrowserFavorites": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserBookmarks": "<PERSON><PERSON>", "dAppBrowserBookmarkAdd": "<PERSON><PERSON> <PERSON>", "dAppBrowserBookmarkRemove": "<PERSON><PERSON> <PERSON> kaldı<PERSON>", "dAppBrowserUsers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dAppBrowserRecents": "<PERSON><PERSON><PERSON>", "dAppBrowserFavoritesDescription": "Favorileriniz burada gösterilir", "dAppBrowserBookmarksDescription": "<PERSON><PERSON> <PERSON><PERSON><PERSON> burada g<PERSON>", "dAppBrowserRecentsDescription": "<PERSON><PERSON><PERSON><PERSON> zamanda ba<PERSON>lanan dapp'lar burada gö<PERSON>ü<PERSON><PERSON>r", "dAppBrowserEmptyScreenDescription": "URL girin veya web'de arama yapın", "dAppBrowserBlocklistScreenTitle": "{{origin}} engel<PERSON><PERSON>! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom, bu web sitesinin kötü niyetli olduğuna ve kullanım için güvenli olmadığına inanıyor.", "part2": "Bu site, bilinen oltalama web siteleri ve dolandırıcılıkları içeren topluluk tarafından oluşturulmuş bir veri tabanının bir parçası olarak işaretlenmiş. Sitenin yanlışlıkla işaretlendiğini düşünüyorsanız lütfen sorunu bildirin."}, "dAppBrowserLoadFailedScreenTitle": "<PERSON><PERSON><PERSON>nemed<PERSON>", "dAppBrowserLoadFailedScreenDescription": "<PERSON>u <PERSON><PERSON> yüklenir<PERSON> bir hata o<PERSON>ş<PERSON>", "dAppBrowserBlocklistScreenIgnoreButton": "Uyarıyı yok say, yine de <PERSON>", "dAppBrowserActionBookmark": "<PERSON><PERSON>", "dAppBrowserActionRemoveBookmark": "<PERSON><PERSON> imini kaldır", "dAppBrowserActionRefresh": "<PERSON><PERSON><PERSON>", "dAppBrowserActionShare": "Paylaş", "dAppBrowserActionCloseTab": "Sekmeyi kapat", "dAppBrowserActionEndAutoConfirm": "Otomatik Onaylamayı sonlandır", "dAppBrowserActionDisconnectApp": "Uygulamanın bağlantısını kes", "dAppBrowserActionCloseAllTabs": "<PERSON><PERSON><PERSON> se<PERSON> kapat", "dAppBrowserNavigationAddressPlaceholder": "Aramak için bir URL girin", "dAppBrowserTabOverviewMore": "<PERSON><PERSON> fazla", "dAppBrowserTabOverviewAddTab": "<PERSON><PERSON><PERSON> e<PERSON>", "dAppBrowserTabOverviewClose": "Ka<PERSON><PERSON>", "dAppBrowserCloseTab": "Sekmeyi kapat", "dAppBrowserClose": "Ka<PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "<PERSON><PERSON> <PERSON>", "dAppBrowserTabOverviewRemoveBookmark": "<PERSON><PERSON> <PERSON> kaldı<PERSON>", "depositAssetListSuggestions": "<PERSON><PERSON><PERSON>", "depositUndefinedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu <PERSON> yatır<PERSON>lamıyor", "onboardingImportRecoveryPhraseDetails": "Ayrıntılar", "onboardingCreateRecoveryPhraseVerifyTitle": "<PERSON><PERSON><PERSON> Kurtarma Tümceciğini not aldınız mı?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Gizli kurtarma tümceciği olmadan anahtarınıza veya anahtarınızla ilişkili varlıklara erişemezsiniz.", "onboardingCreateRecoveryPhraseVerifyYes": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON>sap oluşturma işlemi başarı<PERSON>ız, lütfen tekrar deney<PERSON>.", "onboardingDoneDescription": "Artık cüzdanınızın tam anlamıyla keyfini çıkarabilirsiniz.", "onboardingDoneGetStarted": "Başlayın", "zeroBalanceHeading": "<PERSON>i ba<PERSON>alım!", "zeroBalanceBuyCryptoTitle": "<PERSON><PERSON><PERSON>ı<PERSON> alın", "zeroBalanceBuyCryptoDescription": "Banka veya kredi kartıyla ilk kriptonuzu satın alın.", "zeroBalanceDepositTitle": "Kripto transfer edin", "zeroBalanceDepositDescription": "Başka bir cüzdan veya borsadan kripto yatırın.", "onboardingImportAccountsEmptyResult": "<PERSON><PERSON><PERSON> b<PERSON>", "onboardingImportAccountsAccountName": "Hesap {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON>", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Aktivite olan {{numberOfWallets}} hesap bulduk", "onboardingImportAccountsFoundAccounts_other": "Aktivite olan {{numberOfWallets}} hesap bulduk", "onboardingImportAccountsFoundAccountsNoActivity_one": "{{numberOfWallets}} hesap bulduk", "onboardingImportAccountsFoundAccountsNoActivity_other": "{{numberOfWallets}} hesap bulduk", "onboardingImportRecoveryPhraseLessThanTwelve": "Tümceciğin en az 12 kelime olması gerekiyor.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Tümceciğin tam olarak 12 veya 24 kelime olması gerekiyor.", "onboardingImportRecoveryPhraseWrongWord": "<PERSON><PERSON><PERSON>ş kelimeler: {{ words }}.", "onboardingProtectTitle": "Cüzdanınızı koruyun", "onboardingProtectDescription": "Biyometrik güvenlik eklemek, cüzdanınıza sadece sizin erişebilmenizi sağlar.", "onboardingProtectButtonHeadlineDevice": "Cihaz", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Parmak izi", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "{{ authType }} Kimlik Doğrulamasını Kullan", "onboardingProtectError": "Kimliğiniz doğrulanırken bir şeyler ters gitti, lütfen tekrar deneyin", "onboardingProtectBiometryIosError": "Biyometrik kimlik doğrulama Phantom'da yapılandırılmış ancak Sistem Ayarlarında devre dışı. Yeniden etkinleştirmek için lütfen Ayarlar > Phantom > Face ID veya Touch ID'yi açın.", "onboardingProtectRemoveAuth": "Kimlik doğrulamayı devre dışı bırak", "onboardingProtectRemoveAuthDescription": "Kimlik doğrulamayı devre dışı bırakmak istediğinizden emin misin?", "onboardingWelcomeTitle": "Phantom'a ho<PERSON> geldiniz", "onboardingWelcomeDescription": "Başlamak için yeni bir cüzdan oluşturun veya mevcut bir cüzdanı içe aktarın.", "onboardingWelcomeCreateWallet": "<PERSON>ni bir cüzdan oluşturun", "onboardingWelcomeAlreadyHaveWallet": "Zaten bir cüzdanım var", "onboardingWelcomeConnectSeedVault": "<PERSON><PERSON> Ka<PERSON>ı<PERSON>lanın", "onboardingSlide1Title": "Sizin tarafınızdan kontrol edilir", "onboardingSlide1Description": "Cüzdanınız biyometrik <PERSON>, dolandırıcılık tespiti ve 7/24 destek ile güvendedir.", "onboardingSlide2Title": "NFT'leriniz i<PERSON>in\nen iyi ev", "onboardingSlide2Description": "Listelemel<PERSON>ö<PERSON>, spam yakın ve faydalı anlık bildirimlerle haberdar kalın.", "onboardingSlide3Title": "Token'ları<PERSON><PERSON><PERSON><PERSON> daha fazlasını yapın", "onboardingSlide3Description": "Cüzdanınızdan ayrılmadan sa<PERSON>ın, takas edin, g<PERSON><PERSON><PERSON> ve alın. ", "onboardingSlide4Title": "Web3'ün en iyisini keşfedin", "onboardingSlide4Description": "Uygulama içi tarayıcıyla önde gelen uygulama ve koleksiyonlar bulun ve bunlara bağlanın.", "onboardingMultichainSlide5Title": "Her şey için bir cüzdan", "onboardingMultichainSlide5Description": "Solana, Ethereum ve Polygon'un tümünü kullanıcı dostu tek bir arayüzde deneyimleyin.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Solana, Ethereum, Polygon ve Bitcoin'in tümünü kullanıcı dostu tek bir arayüzde deneyimleyin.", "requireAuth": "Kimlik doğrulamayı gerekli kıl", "requireAuthImmediately": "Anında", "availableToSend": "Göndermek İçin <PERSON>", "sendEnterAmount": "<PERSON><PERSON><PERSON> girin", "sendEditMemo": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendShowLogs": "Hata Kayıtlarını göster", "sendHideLogs": "Hata Kayıtlarını gizle", "sendGoBack": "<PERSON><PERSON>", "sendTransactionSuccess": "Token'ları<PERSON><PERSON>z başarıyla şuraya gönderildi:", "sendInputPlaceholder": "@kullanıcıadı veya adres", "sendInputPlaceholderV2": "kullanıcı adı veya adres", "sendPeopleTitle": "İnsanlar", "sendDomainTitle": "<PERSON><PERSON><PERSON>", "sendFollowing": "Takip ediliyor", "sendRecentlyUsedAddressLabel": "{{formattedTimestamp}} <PERSON>nce kullanıldı", "sendRecipientAddress": "Alıcı adresi", "sendTokenInterpolated": "{{tokenSymbol}} g<PERSON><PERSON>", "sendPasteFromClipboard": "<PERSON><PERSON>n <PERSON>ı<PERSON>ı<PERSON>", "sendScanQR": "QR Kodu tarayın", "sendTo": "<PERSON><PERSON>:", "sendRecipientZeroBalanceWarning": "Bu cüzdan adresinde bakiye yok ve son iş<PERSON> geçmişinizde görünmüyor. Lütfen adresin doğru olduğundan emin olun.", "sendUnknownAddressWarning": "<PERSON><PERSON>, son <PERSON><PERSON><PERSON><PERSON> et<PERSON><PERSON><PERSON><PERSON> bir ad<PERSON>. Lütfen dikkatle yaklaşın.", "sendSameAddressWarning": "<PERSON><PERSON>, mevcut adresiniz. <PERSON><PERSON><PERSON><PERSON>, ba<PERSON><PERSON> bakiye değişikliği olmadan transfer ücretlerine tabi olur.", "sendMintAccountWarning": "Bu bir mini hesap adresi. Kalıcı kayıpla sonuçlanacağından bu adrese fon gönderemezsiniz.", "sendCameraAccess": "<PERSON><PERSON><PERSON>", "sendCameraAccessSubtitle": "QR kod taramak için kamera erişiminin etkinleştirilmesi gerekiyor. <PERSON>men <PERSON>'ı açmak ister misiniz?", "sendSettings": "<PERSON><PERSON><PERSON>", "sendOK": "<PERSON><PERSON>", "invalidQRCode": "Bu QR kodu geçersiz.", "sendInvalidQRCode": "Bu QR kodu geçerli bir adres <PERSON>ğil", "sendInvalidQRCodeSubtitle": "<PERSON><PERSON><PERSON> deneyin ya da farklı bir QR koduyla deneyin.", "sendInvalidQRCodeSplToken": "QR kodunda geçersiz token", "sendInvalidQRCodeSplTokenSubtitle": "Bu QR kod, sahip olmadığınız veya tanımlayamadığımız bir token içeriyor.", "sendScanAddressToSend": "Fon göndermek için {{tokenSymbol}} ad<PERSON><PERSON> ta<PERSON>n", "sendScanAddressToSendNoSymbol": "Fon göndermek için ad<PERSON> ta<PERSON>ın", "sendScanAddressToSendCollectible": "Koleksiyonluk göndermek için SOL adresi tarayın", "sendScanAddressToSendCollectibleMultichain": "Koleksiyonluk göndermek için adresi tarayın", "sendSummary": "Özet", "sendUndefinedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu <PERSON> gönder<PERSON>mi<PERSON>", "sendNoTokens": "Kullanılabilir token yok", "noBuyOptionsAvailableInCountry": "Ülkenizde kullanılabilir Satın Al seçeneği bulunmuyor", "swapAvailableTokenDisclaimer": "Ağlar arasında köprü kurmak için sınırlı sayıda token mevcuttur", "swapCrossSwapNetworkTooltipTitle": "<PERSON><PERSON><PERSON>", "swapCrossSwapNetworkTooltipDescription": "Ağlar arasında takas yaparken mevcut token'ların en düşük fiyatlı ve en hızlı işlemler için kullanılması önerilir.", "settingsAbout": "Phantom hakkında", "settingsShareAppWithFriends": "Arkadaşlarınızı davet edin", "settingsConfirm": "<PERSON><PERSON>", "settingsMakeSureNoOneIsWatching": "<PERSON><PERSON><PERSON> bakmadığından emin olun", "settingsManageAccounts": "Hesapları Yönet", "settingsPrompt": "<PERSON><PERSON> etmek istediğinizden emin misin?", "settingsSelectAvatar": "Avatar seçme", "settingsSelectSecretPhrase": "Gizli Tümceciği seçin", "settingsShowPrivateKey": "<PERSON>zel anahtarınızın gösterilmesi için dokunun", "settingsShowRecoveryPhrase": "Gizli tümeciğinizin gösterilmesi için dokunun", "settingsSubmitBetaFeedback": "Beta Geri Bildirimi Gönderin", "settingsUpdateAccountNameToast": "<PERSON><PERSON><PERSON> adı <PERSON>", "settingsUpdateAvatarToast": "Avatar <PERSON>", "settingsUpdateAvatarToastFailure": "Avatar güncellenemedi!", "settingsWalletAddress": "<PERSON><PERSON><PERSON>", "settingsWalletAddresses": "<PERSON><PERSON><PERSON>", "settingsWalletNamePrimary": "<PERSON><PERSON><PERSON>", "settingsPlaceholderName": "İsim", "settingsWalletNameSecondary": "Cüzdan adınızı değiştirin", "settingsYourAccounts": "Hesaplarınız", "settingsYourAccountsMultiChain": "Çoklu zincir", "settingsReportUser": "Kullanıcıyı bildir", "settingsNotifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsNotificationPreferences": "<PERSON><PERSON><PERSON><PERSON>", "pushNotificationsPreferencesAllowNotifications": "Bildirimlere İzin Ver", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>'lar", "pushNotificationsPreferencesSentTokensDescription": "Giden token ve NFT transferleri", "pushNotificationsPreferencesReceivedTokens": "<PERSON><PERSON><PERSON>", "pushNotificationsPreferencesReceivedTokensDescription": "Gelen token ve NFT transferleri", "pushNotificationsPreferencesDexSwap": "Takaslar", "pushNotificationsPreferencesDexSwapDescription": "Tanınan uygulamalardaki takaslar", "pushNotificationsPreferencesOtherBalanceChanges": "Diğer Bakiye Değişiklikleri", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Bakiyenizi etkileyen diğer çok token'lı işlemler", "pushNotificationsPreferencesPhantomMarketing": "<PERSON><PERSON><PERSON><PERSON>", "pushNotificationsPreferencesPhantomMarketingDescription": "Özellik duyuruları ve genel güncellemeler", "pushNotificationsPreferencesDescription": "Bu ayarlar bu aktif cüzdan için anlık bildirimleri kontrol eder. Her bir cüzdanın kendi bildirim ayarları vardır. Tüm Phantom anlık bildirimlerini kapatmak için <1>cihaz ayarlarınıza</1> gidin.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Bildirim tercihleri senkronize edilemiyor.", "connectSeedVaultConnectSeed": "<PERSON><PERSON> b<PERSON>", "connectSeedVaultConnectSeedDescription": "Phantom'ı telefonunuzdaki Tohum Kasasına bağlayın", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeed": "<PERSON><PERSON>", "connectSeedVaultSelectASeedDescription": "Phantom'a ba<PERSON><PERSON><PERSON> isted<PERSON><PERSON><PERSON>z <PERSON>", "connectSeedVaultSelectAnAccountDescription": "Phantom'a hangi hesabı kurmak istediğinizi seçin", "connectSeedVaultNoAccountsFound": "<PERSON><PERSON><PERSON> b<PERSON>.", "connectSeedVaultSelectAccounts": "Hesapları seçin", "connectSeedVaultSelectAccountsDescription": "Phantom'a hangi hesapları kurmak istediğinizi seçin", "connectSeedVaultCompleteSetup": "<PERSON><PERSON><PERSON><PERSON> tama<PERSON>la", "connectSeedVaultCompleteSetupDescription": "Her şey hazır! Phantom'la web3'ü keşfedin ve işlemlerinizi onaylamak için Tohum Kasanızı kullanın", "connectSeedVaultConnectAnotherSeed": "Başka bir tohum ba<PERSON>la", "connectSeedVaultConnectAllSeedsConnected": "<PERSON><PERSON><PERSON> bağlı", "connectSeedVaultNoSeedsConnected": "<PERSON><PERSON> bağlanmadı. <PERSON><PERSON> Ka<PERSON>sından izin vermek için aşağıdaki düğmeye dokunun.", "connectSeedVaultConnectAccount": "<PERSON><PERSON><PERSON> b<PERSON>", "connectSeedVaultLoadMore": "<PERSON><PERSON>", "connectSeedVaultNeedPermission": "<PERSON><PERSON> gere<PERSON>", "connectSeedVaultNeedPermissionDescription": "Phantom'ın Tohum Kasası izinlerini kullanmasına izin vermek için Ayarlar'a gidin.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} ücret", "stakeAmount": "<PERSON><PERSON><PERSON>", "stakeAmountBalance": "Bakiye", "swapTopQuotes": "<PERSON> {{numQuotes}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "swapTopQuotesTitle": "En İyi <PERSON>", "swapProvidersTitle": "Sağlayıcılar", "swapProvidersFee": "{{fee}} ücret", "swapProvidersTagRecommended": "En İyi Dönüş", "swapProvidersTagFastest": "En Hızlı", "swapProviderEstimatedTimeHM": "{{hours}} sa {{minutes}} dk", "swapProviderEstimatedTimeM": "{{minutes}} dk", "swapProviderEstimatedTimeS": "{{seconds}} sn", "stakeReview": "<PERSON><PERSON><PERSON>", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "Komis<PERSON>", "stakeReviewConfirm": "<PERSON><PERSON><PERSON>", "stakeReviewValidator": "Doğrulayıcı", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Stake Dönüştürme Başarısız", "convertStakeStatusErrorMessage": "Stake'iniz {{poolTokenSymbol}} adlı token'a dönüştürülemedi. Lütfen tekrar deneyin.", "convertStakeStatusLoadingTitle": "{{poolTokenSymbol}} adlı token'a dönüştürülüyor", "convertStakeStatusLoadingMessage": "Stake ettiğiniz {{stakedTokenSymbol}} adlı token'ı {{poolTokenSymbol}} adlı token'a dönüştürme işlemini başlatıyoruz.", "convertStakeStatusSuccessTitle": "{{poolTokenSymbol}} adlı token'a dönüştürme işlemi tamamlandı!", "convertStakeStatusSuccessMessage": "<1>Buradan</1> JitoSOL'ünüzle ek ödüller kazanın.", "convertStakeStatusConvertMore": "<PERSON><PERSON> fazla dö<PERSON><PERSON>", "convertStakePendingTitle": "Stake, {{symbol}} adlı token'a dönüştürülüyor", "convertToJitoSOL": "JitoSOL'e dönüştür", "convertToJitoSOLInfoDescription": "Ö<PERSON><PERSON>l kazanmak ve Jito ekosistemine katılmak için SOL'ünüzü Jito SOL'e dönüştürün.", "convertToJitoSOLInfoTitle": "JitoSOL'e dönüştürün", "convertStakeBannerTitle": "Ödülleri %15 oranında artırmak için stake'inizi JitoSOL'e dönüştürün", "convertStakeQuestBannerTitle": "Bahisli SOL'u JitoSOL'e dönüştürün ve ödüller kazanın. Daha fazla bilgi edinin.", "liquidStakeConvertInfoTitle": "JitoSOL'e dönüştür", "liquidStakeConvertInfoDescription": "SOL stake'inizi JitoSOL'e dönüştürerek ödüllerinizi artırın. <1>Daha fazla bilgi edinin</1>", "liquidStakeConvertInfoFeature1Title": "<PERSON><PERSON>la stake etmelisiniz?", "liquidStakeConvertInfoFeature1Description": "Stake'inizle büyüyen JitoSOL kazanmak için para yatırın. Ekstra kazançlar için bunu DeFi protokollerinde kullanın. Başlangıç tutarınız + tahakkuk eden ödülleriniz için daha sonra JitoSOL'ünüzü takas edin", "liquidStakeConvertInfoFeature2Title": "<PERSON><PERSON> y<PERSON><PERSON> ortalama ödülleri", "liquidStakeConvertInfoFeature2Description": "Jito, en düşük ücretlerle SOL'ünüzü en iyi doğrulayıcılara yayar. MEV ödülleri kazançlarınızı daha da artırır.", "liquidStakeConvertInfoFeature3Title": "Solana ağını destekleyin", "liquidStakeConvertInfoFeature3Description": "Likit stake'leme, stake'i birden fazla doğrulayıcıya yayıp düşük çalışma zamanlı doğrulayıcılardan gelen riski azaltarak Solana'yı güvenceye alır.", "liquidStakeConvertInfoSecondaryButton": "<PERSON><PERSON><PERSON>", "liquidStakeStartStaking": "Stake Etmeye başlayın", "liquidStakeReviewOrder": "<PERSON><PERSON>", "convertStakeAccountListPageIneligibleSectionTitle": "Uygun Olmayan Stake Hesapları", "convertStakeAccountIneligibleBottomSheetTitle": "Uygun Olmayan Stake Hesapları", "convertStakeAccountListPageErrorTitle": "Stake hesapları getirilemedi", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir <PERSON><PERSON>ler ters gitti ve stake hesapları getirilemiyor", "liquidStakeReviewYouPay": "Ödeyeceğiniz:", "liquidStakeReviewYouReceive": "Alacağınız:", "liquidStakeReviewProvider": "Sağlayıcı", "liquidStakeReviewNetworkFee": "<PERSON><PERSON>", "liquidStakeReviewPageTitle": "<PERSON><PERSON>", "liquidStakeReviewConversionFootnote": "JitoSOL karşılığında Solana token'ı stake ettiğinizde biraz daha az miktarda JitoSOL alırsınız. <1>Daha fazla bilgi edinin</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Jito'nun stake havuzu, en aktif Solana doğrulayıcılarını destekler. Desteklenmeyen doğrulayıcılardan JitoSOL'e stake edilmiş SOL dönüştüremeyeceksiniz. Ek olarak, yeni stake edilen SOL'ün JitoSOL dönüştürmeye uygun olması ~2 gün sürer.", "selectAValidator": "Doğrulayıcı seç", "validatorSelectionListTitle": "Doğrulayıcı seç", "validatorSelectionListDescription": "SOL'ünüzü stake'lemek için bir doğrulayıcı seçin.", "stakeMethodDescription": "Solana'nın ölçeklenmesine yardım etmek için SOL token'larınızı kullanarak faiz kazanın. <1>Daha fazla bilgi edinin</1>", "stakeMethodRecommended": "Önerilen", "stakeMethodEstApy": "Tah. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Likit Stake Etme", "stakeMethodSelectionLiquidStakingDescription": "Daha yü<PERSON><PERSON> ödüller kazanmak, Sol<PERSON>'<PERSON><PERSON><PERSON><PERSON><PERSON>e almaya yardımcı olmak ve ek ödüller kazanmak üzere JitoSOL almak için SOL stake edin.", "stakeMethodSelectionNativeStakingTitle": "<PERSON><PERSON> Etme", "stakeMethodSelectionNativeStakingDescription": "Solana'yı güvenceye almaya yardımcı olurken ödül almak için SOL stake edin.", "liquidStakeMintStakeSOL": "SOL stake et", "mintJitoSOLInfoPageTitle": "Karşınızda Jito ile Likit Stake Etme", "mintJitoSOLFeature1Title": "<PERSON><PERSON>la stake etmelisiniz?", "mintJitoSOLFeature1Description": "Stake'inizle büyüyen JitoSOL kazanmak için para yatırın. Ekstra kazançlar için bunu DeFi protokollerinde kullanın. Başlangıç tutarınız + tahakkuk eden ödülleriniz için daha sonra JitoSOL'ünüzü takas edin", "mintJitoSOLFeature2Title": "<PERSON><PERSON> y<PERSON><PERSON> ortalama ödülleri", "mintJitoSOLFeature2Description": "Jito, en düşük ücretlerle SOL'ünüzü en iyi doğrulayıcılara yayar. MEV ödülleri kazançlarınızı daha da artırır.", "mintJitoSOLFeature3Title": "Solana ağını destekleyin", "mintJitoSOLFeature3Description": "Likit stake'leme, stake'i birden fazla doğrulayıcıya yayıp düşük çalışma zamanlı doğrulayıcılardan gelen riski azaltarak Solana'yı güvenceye alır.", "mintLiquidStakePendingTitle": "Likit stake mint'leme", "mintStakeStatusErrorTitle": "Likit Stake Mint'leme Başarısız", "mintStakeStatusErrorMessage": "{{poolTokenSymbol}} likit stake'iniz mint'lenemiyor. Lütfen tekrar deneyin.", "mintStakeStatusSuccessTitle": "{{poolTokenSymbol}} likit skate mint'leme ta<PERSON>!", "mintStakeStatusLoadingTitle": "{{poolTokenSymbol}} likit skate mint'leme", "mintStakeStatusLoadingMessage": "{{poolTokenSymbol}} likit stake'inizi mint'lemek için işleme başlıyoruz.", "mintLiquidStakeAmountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> ne kadar stake yapmak istediğinizi se<PERSON>in", "mintLiquidStakeAmountProvider": "Sağlayıcı", "mintLiquidStakeAmountApy": "Tah. APY", "mintLiquidStakeAmountBestPrice": "<PERSON><PERSON><PERSON>", "mintLiquidStakeAmountInsufficientBalance": "<PERSON><PERSON><PERSON>", "mintLiquidStakeAmountMinRequired": "Stake etmek için {{amount}} {{symbol}} gerekli", "swapTooltipGotIt": "<PERSON><PERSON><PERSON><PERSON>", "swapTabInsufficientFunds": "<PERSON><PERSON><PERSON> fon", "swapNoAssetsFound": "Varlık yok", "swapNoTokensFound": "Token bulunamadı", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON> dene", "swapConfirmationGoBack": "<PERSON><PERSON> git", "swapNoQuotesFound": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "swapNotProviderFound": "Bu token takası için sağlayıcı bulunamadı. Farklı bir token deneyin.", "swapAvailableOnMainnet": "Bu özellik yalnızca Mainnet'te mevcut", "swapNotAvailableEVM": "Takaslar EVM hesapları için henüz kullanılamıyor", "swapNotAvailableOnSelectedNetwork": "<PERSON><PERSON><PERSON>, seçili ağda mevcut değil", "singleChainSwapTab": "Ağda", "crossChainSwapTab": "<PERSON><PERSON><PERSON> arasında", "allFilter": "Tümü", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON><PERSON> yap", "bridgeRefuelDescription": "<PERSON><PERSON><PERSON><PERSON> yap, k<PERSON>pr<PERSON><PERSON>e yaptıktan sonra işlemler için ödeme yapabilmenizi sağlar.", "bridgeRefuelLabelBalance": "{{symbol}} bakiyeniz", "bridgeRefuelLabelReceive": "Alacağınız", "bridgeRefuelLabelFee": "<PERSON><PERSON><PERSON><PERSON>", "bridgeRefuelDismiss": "<PERSON><PERSON>kleme <PERSON> k<PERSON> devam et", "bridgeRefuelEnable": "<PERSON><PERSON><PERSON><PERSON> Yap'ı etkinleştir", "unwrapWrappedSolError": "<PERSON><PERSON><PERSON><PERSON> başarıs<PERSON><PERSON>", "unwrapWrappedSolLoading": "Kaydırılıyor...", "unwrapWrappedSolSuccess": "Kaydırıldı", "unwrapWrappedSolViewTransaction": "İşlemi Görüntüle", "dappApprovePopupSignMessage": "İmza <PERSON>", "solanaPayFrom": "<PERSON><PERSON>", "solanaPayMessage": "<PERSON><PERSON>", "solanaPayNetworkFee": "<PERSON><PERSON>", "solanaPayFree": "Ücretsiz", "solanaPayPay": "{{item}} öde", "solanaPayPayNow": "<PERSON><PERSON>", "solanaPaySending": "{{item}} g<PERSON><PERSON><PERSON><PERSON><PERSON>", "solanaPayReceiving": "{{item}} alınıyor", "solanaPayMinting": "{{item}} mint ediliyor", "solanaPayTransactionProcessing": "İşleminiz işleniyor,\n<PERSON><PERSON><PERSON><PERSON> be<PERSON>.", "solanaPaySent": "Gönderildi!", "solanaPayReceived": "Alındı!", "solanaPayMinted": "Mint edildi!", "solanaPaySentNFT": "NFT gönderildi!", "solanaPayReceivedNFT": "NFT alındı!", "solanaPayTokensSent": "Token'ların<PERSON>z {{to}} adlı kişiye başarıyla gönderildi", "solanaPayTokensReceived": "{{from}} adlı kişiden token aldınız", "solanaPayViewTransaction": "İşlemi görü<PERSON>üle", "solanaPayTransactionFailed": "İşlem Başarısız", "solanaPayConfirm": "<PERSON><PERSON><PERSON>", "solanaPayTo": ">", "dappApproveConnectViewAccount": "Solana hesabınızı görü<PERSON>in", "deepLinkInvalidLink": "Geçersiz bağlantı", "deepLinkInvalidSplTokenSubtitle": "<PERSON><PERSON>, sa<PERSON> olmadığınız veya tanımlayamadığımız bir token içeriyor.", "walletAvatarShowAllAccounts": "<PERSON><PERSON><PERSON> he<PERSON>ları gö<PERSON>", "pushNotificationsGetInstantUpdates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>n", "pushNotificationsEnablePushNotifications": "<PERSON><PERSON><PERSON><PERSON>, takaslar ve duyurular hakkında anlık bildirimleri etkinleştirin", "pushNotificationsEnable": "Etkinleştir", "pushNotificationsNotNow": "<PERSON><PERSON><PERSON>", "onboardingAgreeToTermsOfServiceInterpolated": "<1>Hizmet Koşullarını</1> kabul ediyorum", "onboardingConfirmSaveSecretRecoveryPhrase": "<PERSON><PERSON>, bir yere kaydettim", "onboardingCreateNewWallet": "Yeni Cüzdan Oluştur", "onboardingErrorDuplicateSecretRecoveryPhrase": "<PERSON><PERSON> gizli ifade, cüzdanınızda zaten mevcut", "onboardingErrorInvalidSecretRecoveryPhrase": "Geçersiz gizli kurtarma tümceciği", "onboardingFinished": "<PERSON><PERSON>i tamam!", "onboardingImportAccounts": "Hesapları İçe Aktar", "onboardingImportImportingAccounts": "Hesaplar içe aktarılıyor...", "onboardingImportImportingFindingAccounts": "Aktivite olan hesaplar bulunuyor", "onboardingImportAccountsLastActive": "{{formattedTimestamp}} önce aktif", "onboardingImportAccountsNeverUsed": "<PERSON><PERSON>", "onboardingImportAccountsCreateNew": "<PERSON><PERSON>", "onboardingImportAccountsDescription": "İçe aktarılacak cüzdan hesaplarını seçin", "onboardingImportReadOnlyAccountDescription": "İzlemek istediğiniz bir adres veya etki alanı ekleyin. Yalnızca görüntüleme erişiminiz o<PERSON>k, işlemleri veya mesajları imzalayamayacaksınız.", "onboardingImportSecretRecoveryPhrase": "Gizli Tümceciği içe aktarın", "onboardingImportViewAccounts": "Hesapları Görüntüle", "onboardingRestoreExistingWallet": "12 veya 24 kelimelik gizli kurtarma tümceciğinizle mevcut bir cüzdanı geri yü<PERSON>in", "onboardingShowUnusedAccounts": "Kullanılmayan Hesapları Göster", "onboardingShowMoreAccounts": "<PERSON><PERSON>", "onboardingHideUnusedAccounts": "Kullanılmayan Hesapları Gizle", "onboardingSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> Tümceciği", "onboardingSelectAccounts": "Hesaplarınızı Seçin", "onboardingStoreSecretRecoveryPhraseReminder": "Yalnızca bu ş<PERSON>ilde he<PERSON>bınızı kurtarabileceksiniz. Lütfen güvenli bir yerde saklayın!", "useTokenMetasForMintsUnknownName": "Bilinmeyen", "timeUnitMinute": "<PERSON><PERSON><PERSON>", "timeUnitMinutes": "<PERSON><PERSON><PERSON>", "timeUnitHour": "saat", "timeUnitHours": "saat", "espNFTListWithPrice": "{{dAppName}} adlı uygulamada {{priceAmount}} {{priceTokenSymbol}} karşılığında {{NFTDisplayName}} listelediniz", "espNFTListWithPriceWithoutDApp": "{{priceAmount}} {{priceTokenSymbol}} karşılığında {{NFTDisplayName}} listelediniz", "espNFTListWithoutPrice": "{{dAppName}} adlı uygulamada satış için {{NFTDisplayName}} listelediniz", "espNFTListWithoutPriceWithoutDApp": "Satış için {{NFTDisplayName}} listelediniz", "espNFTChangeListPriceWithPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} listelemesini {{priceAmount}} {{priceTokenSymbol}} olarak güncellediniz", "espNFTChangeListPriceWithPriceWithoutDApp": "{{NFTDisplayName}} listelemesini {{priceAmount}} {{priceTokenSymbol}} olarak güncellediniz", "espNFTChangeListPriceWithoutPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} listelemesini güncellediniz", "espNFTChangeListPriceWithoutPriceWithoutDApp": "{{NFTDisplayName}} listele<PERSON>ini güncellediniz", "espNFTBidBidderWithPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} için {{priceAmount}} {{priceTokenSymbol}} tek<PERSON>f verdi<PERSON>z", "espNFTBidBidderWithPriceWithoutDApp": "{{NFTDisplayName}} için {{priceAmount}} {{priceTokenSymbol}} teklif verdi<PERSON>z", "espNFTBidBidderWithoutPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} i<PERSON><PERSON> teklif verdi<PERSON>z", "espNFTBidBidderWithoutPriceWithoutDApp": "{{NFTDisplayName}} i<PERSON><PERSON> tek<PERSON>f verdi<PERSON>", "espNFTBidListerWithPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} için {{priceAmount}} {{priceTokenSymbol}} değerinde yeni teklif", "espNFTBidListerWithPriceWithoutDApp": "{{NFTDisplayName}} için {{priceAmount}} {{priceTokenSymbol}} de<PERSON>erinde yeni teklif", "espNFTBidListerWithoutPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} i<PERSON>in yeni teklif", "espNFTBidListerWithoutPriceWithoutDApp": "{{NFTDisplayName}} i<PERSON><PERSON> yeni tek<PERSON>f", "espNFTCancelBidWithPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} için {{priceAmount}} {{priceTokenSymbol}} değerindeki teklifinizi iptal ettiniz", "espNFTCancelBidWithPriceWithoutDApp": "{{NFTDisplayName}} için {{priceAmount}} {{priceTokenSymbol}} değerindeki teklifinizi iptal ettiniz", "espNFTCancelBidWithoutPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} için teklifinizi iptal ettiniz", "espNFTCancelBidWithoutPriceWithoutDApp": "{{NFTDisplayName}} için teklifinizi iptal ettiniz", "espNFTUnlist": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} listelemesini kaldırdınız", "espNFTUnlistWithoutDApp": "{{NFTDisplayName}} tarafınızdan listeden çıkarıldı", "espNFTBuyBuyerWithPrice": "{{dAppName}} adlı uygulamada {{priceAmount}} {{priceTokenSymbol}} karşılığında {{NFTDisplayName}} sat<PERSON>n al<PERSON>", "espNFTBuyBuyerWithPriceWithoutDApp": "{{priceTokenSymbol}} {{priceAmount}} karşılığında {{NFTDisplayName}} satın <PERSON>", "espNFTBuyBuyerWithoutPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} satın aldınız", "espNFTBuyBuyerWithoutPriceWithoutDApp": "{{NFTDisplayName}} sat<PERSON>n al<PERSON>", "espNFTBuySellerWithPrice": "{{dAppName}} adlı uygulamada {{priceAmount}} {{priceTokenSymbol}} karşılığında {{NFTDisplayName}} sattınız", "espNFTBuySellerWithPriceWithoutDApp": "{{priceAmount}} {{priceTokenSymbol}} karşılığında {{NFTDisplayName}} sattınız", "espNFTBuySellerWithoutPrice": "{{dAppName}} adlı uygulamada {{NFTDisplayName}} sattınız", "espNFTBuySellerWithoutPriceWithoutDApp": "{{NFTDisplayName}} sattınız", "espDEXSwap": "{{dAppName}} adlı uygulamada {{upTokensTextFragment}} karşılığında {{downTokensTextFragment}} bozdurdunuz", "espDEXDepositLPWithPoolDisplay": "{{dAppName}} adlı uygulamada {{poolDisplayName}} likidite havuzuna {{downTokensTextFragment}} yat<PERSON>rdınız", "espDEXDepositLPWithoutPoolDisplay": "{{dAppName}} adlı uygulamada {{upTokensTextFragment}} karşılığında {{downTokensTextFragment}} bozdurdunuz", "espDEXWithdrawLPWithPoolDisplay": "{{dAppName}} adlı uygulamada {{poolDisplayName}} likidite havuzundan {{upTokensTextFragment}} çektiniz", "espDEXWithdrawLPWithoutPoolDisplay": "{{dAppName}} adlı uygulamada {{upTokensTextFragment}} karşılığında {{downTokensTextFragment}} bozdurdunuz", "espGenericTokenSend": "{{downTokensTextFragment}} g<PERSON><PERSON><PERSON><PERSON>", "espGenericTokenReceive": "{{upTokensTextFragment}} aldınız", "espGenericTransactionBalanceChange": "{{upTokensTextFragment}} karşılığında {{downTokensTextFragment}} bozdurdunuz", "espUnknown": "BİLİNMEYEN", "espUnknownNFT": "bilinmeyen NFT", "espTextFragmentAnd": "ve", "externalLinkWarningTitle": "<PERSON>'dan <PERSON>k üzeresiniz", "externalLinkWarningDescription": "Ve {{url}} adresini açın. Bu kaynakla iletişime geçmeden önce kaynağa güvendiğinizden emin olun.", "shortcutsWarningDescription": "{{url}} tarafından sağlanan kısayollar. Etkileşime geçmeden önce bu kaynağa güvendiğinizden emin olun.", "lowTpsBanner": "Solana ağ tıkanıklığı yaşıyor", "lowTpsMessageTitle": "Solana ağ tıkanıklığı", "lowTpsMessage": "Yüksek Solana tıkanıklığı nedeniyle işlemleriniz başarısız olabilir veya gecikebilir. Lütfen başarısız işlemleri tekrar deneyin.", "solanaSlow": "Solana ağı alışılmadık derecede yavaş", "solanaNetworkTemporarilyDown": "Solana ağı geçici olarak kullanılamıyor", "waitForNetworkRestart": "Lütfen ağın yeniden başlamasını bekleyin. Fonlarınız bundan etkilenmeyecek.", "exploreCollectionsCarouselTitle": "<PERSON><PERSON><PERSON>?", "exploreDropsCarouselTitle": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortFloor": "Taban", "exploreSortListed": "Listelendi", "exploreSortVolume": "Hacim", "exploreFetchErrorSubtitle": "<PERSON><PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "exploreFetchErrorTitle": "Getirilemedi.", "exploreTopCollectionsTitle": "En İyi NFT Koleksiyonları", "exploreTopListLess": "<PERSON><PERSON> az", "exploreTopListMore": "<PERSON><PERSON>", "exploreSeeMore": "Daha Fazlasına Bak", "exploreTrendingTokens": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreVolumeTokens": "En Yüksek Hacim", "explorePriceChangeTokens": "En Çok Kazananlar", "explorePriceTokens": "<PERSON><PERSON><PERSON> g<PERSON>r", "exploreMarketCapTokens": "En İyi <PERSON>", "exploreTrendingSites": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreTopSites": "<PERSON> İyi <PERSON>ler", "exploreTrendingCollections": "Revaçtaki <PERSON>", "exploreTopCollections": "En İyi Koleksiyonlar", "collectiblesSearchCollectionsSection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchItemsSection": "<PERSON><PERSON><PERSON>", "collectiblesSearchNrOfItems": "{{ nrOfItems }} öge", "collectiblesSearchPlaceholderText": "Koleksiyonluklarınızı arayın", "collectionPinSuccess": "Koleks<PERSON><PERSON>", "collectionPinFail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectionUnpinSuccess": "Koleksiyonun sabitlemesi kaldırıldı", "collectionUnpinFail": "Koleksiyonun sabit<PERSON>esi kaldırılamadı", "collectionHideSuccess": "Koleksiyon gizlendi", "collectionHideFail": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectionUnhideSuccess": "Koleksiyon gösteriliyor", "collectionUnhideFail": "Koleksiyon gösterilemiyor", "collectiblesSpamSuccess": "<PERSON><PERSON> o<PERSON>i", "collectiblesSpamFail": "<PERSON><PERSON> o<PERSON>", "collectiblesSpamAndHiddenSuccess": "Spam olarak bildirildi ve gizlendi", "collectiblesNotSpamSuccess": "Spam olmadığı bildirildi", "collectiblesNotSpamFail": "Spam olmadığı bildirilemedi", "collectiblesNotSpamAndUnhiddenSuccess": "Spam olmadığı bildirildi ve gizlemesi kaldırıldı", "tokenPageSpamWarning": "Bu token doğrulanmamış. Yalnızca güvendiğiniz token'larla etkileşim kurun.", "tokenSpamWarning": "<PERSON>u token, Phantom spam old<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON> giz<PERSON>.", "collectibleSpamWarning": "<PERSON><PERSON>, Phantom spam <PERSON><PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> giz<PERSON>.", "collectionSpamWarning": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>, Phantom spam oldukların<PERSON> düşündüğü i<PERSON><PERSON> giz<PERSON>.", "emojiNoResults": "<PERSON><PERSON>ji b<PERSON>", "emojiSearchResults": "<PERSON>ma <PERSON>", "emojiSuggested": "Önerilen", "emojiSmileys": "<PERSON><PERSON><PERSON> ve İnsanlar", "emojiAnimals": "<PERSON><PERSON><PERSON>", "emojiFood": "Yiyecek ve İçecek", "emojiTravel": "<PERSON><PERSON><PERSON> <PERSON>", "emojiActivities": "Aktiviteler", "emojiObjects": "<PERSON><PERSON><PERSON><PERSON>", "emojiSymbols": "<PERSON><PERSON><PERSON>", "emojiFlags": "Bayraklar", "whichExtensionToConnectWith": "Hangi uzantıyla bağlantı kurmak istiyorsunuz?", "configureInSettings": "Ayarlar → Varsayılan Uygulama Cüzdanı adımından yapılandırılabilir.", "continueWith": "<PERSON><PERSON><PERSON><PERSON> devam edin:", "useMetaMask": "MetaMask'i kullanın", "usePhantom": "Phantom'<PERSON>n kull<PERSON>ın", "alwaysAsk": "Her Zaman Sor", "dontAskMeAgain": "<PERSON><PERSON><PERSON>", "selectWalletSettingDescriptionLine1": "Bazı uygulamalar Phantom ile bağlanma seçeneği sunmayabilir.", "selectWalletSettingDescriptionLinePhantom": "<PERSON>ernatif o<PERSON>ak MetaM<PERSON>'le bağlanmak her zaman onun yerine Phantom'ı açar.", "selectWalletSettingDescriptionLineAlwaysAsk": "<PERSON><PERSON><PERSON><PERSON>, MetaMask'le bağlandığınızda size onun yerine Phantom'ı kullanmak isteyip istemediğinizi sorarız.", "selectWalletSettingDescriptionLineMetaMask": "MetaMask'i varsayılan olarak ayarlamak, bu uygulamaların Phantom'a bağlanmasını devre dışı bırakır.", "metaMaskOverride": "Varsayılan Uygulama Cüzdanı", "metaMaskOverrideSettingDescriptionLine1": "Phantom'ı kullanma seçeneği sunmayan web sitelerine bağlanmak için.", "refreshAndReconnectToast": "Değişikliklerinizi uygulamak için yenileyin ve yeniden bağlanın", "autoConfirmUnavailable": "Kullanılamıyor", "autoConfirmReasonDappNotWhitelisted": "Kullanılamıyor çünkü geldiği sözleşme bu uygulama için izin listemizde değil.", "autoConfirmReasonSessionNotActive": "Kullanılamıyor çünkü otomatik onay oturumu etkin değil. Lütfen aşağıdan etkinleştirin.", "autoConfirmReasonRateLimited": "Kullanılamıyor çünkü kullandığınız dapp çok fazla istek gönderiyor.", "autoConfirmReasonUnsupportedNetwork": "Kullanılamıyor çünkü otomatik onaylama henüz bu ağı desteklemiyor.", "autoConfirmReasonSimulationFailed": "Kullanılamıyor çünkü güvenliği garanti edemiyoruz.", "autoConfirmReasonTabNotFocused": "Kullanılamıyor çünkü otomatik onaylamaya çalıştığınız etki alanı sekmesi etkin değil.", "autoConfirmReasonNotUnlocked": "Kullanılamıyor <PERSON>ü<PERSON>ü cüzdanın kilidi açılmadı.", "rpcErrorUnauthorizedWrongAccount": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>.", "rpcErrorUnauthorizedUnknownSource": "RPC talebi kaynağı belirlenemedi.", "transactionsDisabledTitle": "İşlemler devre dışı bırakıldı", "transactionsDisabledMessage": "Adresiniz Phantom kullanarak işlem yapamıyor", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "<PERSON><PERSON><PERSON>", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL panoya kopyalandı", "notEnoughSolScanTransactionWarning": "Bu işlem, hesabınızda yetersiz SOL olması nedeniyle başarısız olabilir. Lütfen hesabınıza daha fazla SOL ekleyip tekrar deneyin.", "transactionRevertedWarning": "<PERSON><PERSON> <PERSON><PERSON>, si<PERSON><PERSON><PERSON>yon sırasında geri döndür<PERSON>ld<PERSON>. Gönderilirse fonlar kaybedilebilir.", "slippageToleranceExceeded": "Bu işlem simülasyon sırasında geri çevrildi. Kayma toleransı aşıldı.", "simulationWarningKnownMalicious": "<PERSON>u hesabın kötü niyetli olduğunu düşünüyoruz. <PERSON><PERSON>lamak, fon kaybına yol açabilir.", "simulationWarningPoisonedAddress": "<PERSON><PERSON> <PERSON><PERSON>, yakın zamanda fon gönderdiğiniz bir adrese şüpheli bir şekilde benziyor. Dolandırıcılık nedeniyle para kaybetmemek için lütfen bunun doğru adres olduğunu teyit edin.", "simulationWarningInteractingWithAccountWithoutActivity": "<PERSON><PERSON>, daha <PERSON>nce herhangi bir aktivitesi olmayan bir hesap. Var olmayan bir hesaba fon göndermek fon kaybına yol açabilir", "quests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questsClaimInProgress": "<PERSON><PERSON><PERSON> devam ediyor", "questsVerifyingCompletion": "Görev tamamlama doğrulanıyor", "questsClaimError": "<PERSON><PERSON><PERSON><PERSON> hata", "questsClaimErrorDescription": "Ödülünüzü alırken bir hata oluştu. Lütfen daha sonra tekrar deneyin.", "questsBadgeMobileOnly": "Yalnızca Mobil", "questsBadgeExtensionOnly": "Yalnızca <PERSON>", "questsExplainerSheetButtonLabel": "<PERSON><PERSON><PERSON><PERSON>", "questsNoQuestsAvailable": "Kullanılabilir görev yok", "questsNoQuestsAvailableDescription": "Ş<PERSON> anda kullanılabilir görev bulunmuyor. Yenileri eklenir eklenmez sizi bilgilendireceğiz.", "exploreLearn": "<PERSON><PERSON><PERSON>", "exploreSites": "<PERSON><PERSON>", "exploreTokens": "To<PERSON>'lar", "exploreQuests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCollections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreFilterByall_networks": "<PERSON><PERSON><PERSON>", "exploreSortByrank": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreSortBytrending": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreSortByprice": "<PERSON><PERSON><PERSON>", "exploreSortByprice-change": "<PERSON><PERSON><PERSON>", "exploreSortBytop": "En İyi", "exploreSortByvolume": "Hacim", "exploreSortBygainers": "Kazananlar", "exploreSortBylosers": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBymarket-cap": "<PERSON><PERSON><PERSON>", "exploreSortBymarket_cap": "<PERSON><PERSON><PERSON>", "exploreTimeFrame1h": "1 sa", "exploreTimeFrame24h": "24 sa", "exploreTimeFrame7d": "7 g", "exploreTimeFrame30d": "30 g", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Koleksi<PERSON>luk<PERSON>", "exploreCategoryMarketplace": "<PERSON><PERSON>", "exploreCategoryGaming": "<PERSON><PERSON>", "exploreCategoryBridges": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryOther": "<PERSON><PERSON><PERSON>", "exploreCategorySocial": "<PERSON><PERSON><PERSON>", "exploreCategoryCommunity": "Topluluk", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Stake etme", "exploreCategoryArt": "Sanat", "exploreCategoryTools": "Araçlar", "exploreCategoryDeveloperTools": "Geliştirici Araçları", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "NFT Stake Etme", "exploreCategoryExplorer": "<PERSON><PERSON><PERSON>", "exploreCategoryInscriptions": "Yazıtlar", "exploreCategoryBridge": "Köprü", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Airdrop Denetçisi", "exploreCategoryPoints": "<PERSON><PERSON><PERSON>", "exploreCategoryQuests": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryShop": "Mağaza", "exploreCategoryProtocol": "Protokol", "exploreCategoryNamingService": "Adlandırma <PERSON>", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Portföy Takipçisi", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Hacim", "exploreFloor": "Taban", "exploreCap": "<PERSON><PERSON><PERSON>", "exploreToken": "Token", "explorePrice": "<PERSON><PERSON><PERSON>", "explore24hVolume": "24 sa Hacim", "exploreErrorButtonText": "<PERSON><PERSON><PERSON>", "exploreErrorDescription": "Keşfet içeriğini yüklemeye çalışırken bir hata oluştu. Lütfen yenileyip tekrar deneyin", "exploreErrorTitle": "Keşfet içeriği yüklenemedi", "exploreNetworkError": "<PERSON><PERSON> hatası yaşandı. Lütfen daha sonra tekrar deneyin.", "exploreTokensLegalDisclaimer": "To<PERSON> <PERSON><PERSON><PERSON>, Birdeye ve Jupiter dâhil olmak üzere çeşitli üçüncü taraf sağlayıcılar tarafından sağlanan piyasa verileri kullanılarak oluşturulur. Performans, önceki 24 saatlik döneme dayalıdır. Geçmiş performans, gelecekteki performansın göstergesi değildir.", "swapperTokensLegalDisclaimer": "Trend token list<PERSON><PERSON>, Birdeye ve Jupiter gibi çeşitli üçüncü taraf sağ<PERSON>ıcılardan alınan piyasa verileri kullanılarak ve Phantom kullanıcıları tarafından belirtilen süre boyunca Swapper aracılığıyla takas edilen popüler token'lara dayalı olarak oluşturulur. Geçmiş performans, gelecekteki performansın göstergesi değildir.", "exploreLearnErrorTitle": "Öğrenme içeriği yüklenemedi", "exploreLearnErrorDescription": "Öğrenme içeriğini yüklemeye çalışırken bir hata oluştu. Lütfen yenileyip tekrar deneyin", "exploreShowMore": "<PERSON><PERSON> faz<PERSON>", "exploreShowLess": "<PERSON><PERSON> a<PERSON> g<PERSON>", "exploreVisitSite": "<PERSON><PERSON> z<PERSON> et", "dappBrowserSearchScreenVisitSite": "<PERSON><PERSON> z<PERSON> et", "dappBrowserSearchScreenSearchWithGoogle": "Google ile ara", "dappBrowserSearchScreenSearchLinkYouCopied": "Kopyaladığınız Bağlantı", "dappBrowserExtSearchPlaceholder": "Site arayın, token", "dappBrowserSearchNoAppsTokens": "Uygulama veya token bulunamadı", "dappBrowserTabsLimitExceededScreenTitle": "Daha Eski Sekmeler kapatılsın mı?", "dappBrowserTabsLimitExceededScreenDescription": "Açık {{tabsCount}} sekmeniz var. Daha fazla açmak için birkaç sekmeyi kapatmanız gerekiyor.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON><PERSON> ka<PERSON>t", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: <PERSON><PERSON> etki alanı mevcut değil", "dappBrowserTabErrorHttp": "Engellendi, lütfen HTTPS kullanın", "dappBrowserTabError401Unauthorized": "401 İzin verilmiyor", "dappBrowserTabError501UnhandledRequest": "501 Ele alınmamış talep", "dappBrowserTabErrorTimeout": "ZAMAN AŞIMI: <PERSON><PERSON><PERSON><PERSON> yanıt vermesi çok uzun sürdü", "dappBrowserTabErrorInvalidResponse": "Geçersiz yanıt", "dappBrowserTabErrorEmptyResponse": "Boş yanıt", "dappBrowserTabErrorGeneric": "<PERSON><PERSON>", "localizedErrorUnknownError": "<PERSON><PERSON> <PERSON><PERSON> ters gitti, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "localizedErrorUnsupportedCountry": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>u anda ülk<PERSON>z desteklenmiyor.", "localizedErrorTokensNotLoading": "Token'ları<PERSON><PERSON>z yüklenirken bir sorun yaşandı. Lütfen tekrar deneyin.", "localizedErrorSwapperNoQuotes": "Desteklenmeyen parite, düşük likidite veya düşük miktar nedeniyle takas yapılamıyor. Token veya miktarı değiştirmeyi deneyin.", "localizedErrorSwapperRefuelNoQuotes": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>adı. Farklı bir token veya miktar deneyin ya da yeniden yüklemeyi devre dışı bırakın.", "localizedErrorInsufficientSellAmount": "Token miktarı çok düşük. Cross-Chain takas etmek için değeri artırın.", "localizedErrorCrossChainUnavailable": "<PERSON><PERSON><PERSON><PERSON>r a<PERSON>ı ta<PERSON> şu anda k<PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin.", "localizedErrorTokenNotTradable": "Seçili token'lardan bi<PERSON>yle işlem yapılamıyor. Lütfen farklı bir token seçin.", "localizedErrorCollectibleLocked": "Token hesabı kilitli.", "localizedErrorCollectibleListed": "Token hesabı listelendi.", "spamActivityAction": "Gizlenen ögeleri <PERSON>", "spamActivityTitle": "Gizlenen Aktivite", "spamActivityWarning": "<PERSON><PERSON> <PERSON>, Phantom spam o<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> düş<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> g<PERSON>.", "appAuthenticationFailed": "Kimlik doğrulanamadı", "appAuthenticationFailedDescription": "Kimlik doğrulama girişiminizle ilgili bir sorun oluştu. Lütfen tekrar deneyin.", "partialErrorBalanceChainName": "{{chainName}} bakiyenizi güncellemekte sorun yaşıyoruz. Fonlarınız güvende.", "partialErrorGeneric": "Ağları güncellemekte sorun yaşıyoruz, bazı token bakiyeleri ve fiyatlar güncel olmayabilir. Fonlarınız güvende.", "partialErrorTokenDetail": "Token bakiyenizi güncellemekte sorun yaşıyoruz. Fonlarınız güvende.", "partialErrorTokenPrices": "Token fiyatlarınızı güncellemekte sorun yaşıyoruz. Fonlarınız güvende.", "partialErrorTokensTrimmed": "Portföyünüzdeki tüm token'ları göstermekte sorun yaşıyoruz. Fonlarınız güvende.", "publicFungibleDetailAbout": "Hakkında", "publicFungibleDetailYourBalance": "Bakiyeniz", "publicFungibleDetailInfo": "<PERSON><PERSON><PERSON>", "publicFungibleDetailShowMore": "<PERSON><PERSON>", "publicFungibleDetailShowLess": "<PERSON><PERSON>", "publicFungibleDetailPerformance": "24 sa Performans", "publicFungibleDetailSecurity": "Güvenlik", "publicFungibleDetailMarketCap": "<PERSON><PERSON><PERSON>", "publicFungibleDetailTotalSupply": "<PERSON><PERSON> Tedarik", "publicFungibleDetailCirculatingSupply": "Dolaşımdaki Tedarik", "publicFungibleDetailMaxSupply": "<PERSON><PERSON><PERSON>", "publicFungibleDetailHolders": "Hamiller", "publicFungibleDetailVolume": "Hacim", "publicFungibleDetailTrades": "İşlem Sayısı", "publicFungibleDetailTraders": "İşlem Yapan Sayısı", "publicFungibleDetailUniqueWallets": "Benzersiz Cüzdan Sayısı", "publicFungibleDetailTop10Holders": "En İyi 10 Hamil", "publicFungibleDetailTop10HoldersTooltip": "Token'ın en iyi 10 hamili tarafından tutulan mevcut toplam tedarik yüzdesini belirtir. Bu, fiyatın ne kadar kolay bir şekilde manipüle edilebileceğinin bir ölçeğidir.", "publicFungibleDetailMintable": "Mint Edilebilir", "publicFungibleDetailMintableTooltip": "Token tedariki, token mint edilebilirse sözleşme sahibi tarafından artırılabilir.", "publicFungibleDetailMutableInfo": "Sessize Alınabilir Bilgi", "publicFungibleDetailMutableInfoTooltip": "Ad, logo ve web sitesi adresi gibi token bilgileri sessize alınabiliyorsa sözleşme sahibi tarafından değiştirilebilir.", "publicFungibleDetailOwnershipRenounced": "Mülkiyetten Feragat Edildi", "publicFungibleDetailOwnershipRenouncedTooltip": "Token sahipliğinden feragat edilirse daha fazla token mint etmek gibi işlevleri kimse gerçekleştiremez.", "publicFungibleDetailUpdateAuthority": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailUpdateAuthorityTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, token sessize alınabiliyorsa bilgileri değiştirebilen cüzdan adresidir.", "publicFungibleDetailFreezeAuthority": "<PERSON><PERSON><PERSON>", "publicFungibleDetailFreezeAuthorityTooltip": "<PERSON><PERSON><PERSON>, fonların transfer edilmesini önleyebilen cüzdan adresidir.", "publicFungibleUnverifiedToken": "Bu token doğrulanmamış. Yalnızca güvendiğiniz token'larla etkileşim kurun.", "publicFungibleDetailSwap": "{{tokenSymbol}} takas et", "publicFungibleDetailSwapDescription": "Phantom uygulaması ile {{tokenSymbol}} takas edin", "publicFungibleDetailLinkCopied": "Panoya kopyalandı", "publicFungibleDetailContract": "S<PERSON>zleşme", "publicFungibleDetailMint": "Mint et", "unifiedTokenDetailTransactionActivity": "Aktivite", "unifiedTokenDetailSeeMoreTransactionActivity": "Daha Fazlasına Bak", "unifiedTokenDetailTransactionActivityError": "Son aktivite y<PERSON><PERSON><PERSON>i", "additionalNetworksTitle": "Ek Ağlar", "copyAddressRowAdditionalNetworks": "Ek Ağlar", "copyAddressRowAdditionalNetworksHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Ethereum'la aynı adresi kullanıyor:", "copyAddressRowAdditionalNetworksDescription": "Bu ağlardan herhangi birisinde varlık göndermek ve almak için Ethereum adresinizi güvenli bir şekilde kullanabilirsiniz.", "cpeUnknownError": "Bilinmeyen hata", "cpeUnknownInstructionError": "Bilinmeyen talimat hatası", "cpeAccountFrozen": "<PERSON><PERSON><PERSON>", "cpeAssetFrozen": "Varlık donduruldu", "cpeInsufficientFunds": "<PERSON><PERSON><PERSON> fon", "cpeInvalidAuthority": "Geçersiz otorite", "cpeBalanceBelowRent": "Bakiye, kiradan muaflık eşiğinin altında", "cpeNotApprovedForConfidentialTransfers": "Hesap gizli <PERSON>ler i<PERSON><PERSON>", "cpeNotAcceptingDepositsOrTransfers": "Hesap para yatırma işlemlerini veya transferleri kabul etmiyor", "cpeNoMemoButRequired": "Önceki talimatta not yok; alıcının transfer alabilmesi i<PERSON>in gere<PERSON>", "cpeTransferDisabledForMint": "Transfer, bu mint için devre dışı", "cpeDepositAmountExceedsLimit": "Para yatırma miktar<PERSON>, maksim<PERSON> limiti aşıyor", "cpeInsufficientFundsForRent": "<PERSON><PERSON><PERSON> fon", "reportIssueScreenTitle": "<PERSON><PERSON> bi<PERSON>", "publicFungibleReportIssuePrompt": "{{tokenName}} hakkında hangi sorunu bildirmek istiyorsunuz?", "publicFungibleReportIssueIncorrectInformation": "Yanlış Bilgi", "publicFungibleReportIssuePriceStale": "<PERSON><PERSON><PERSON> g<PERSON>", "publicFungibleReportIssuePriceMissing": "Fiyat e<PERSON>", "publicFungibleReportIssuePerformanceIncorrect": "24 sa Performans yanlış", "publicFungibleReportIssueLinkBroken": "So<PERSON>al hesap ba<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>şılamıyor", "publicFungibleDetailErrorLoading": "Token verileri kullanılamıyor", "reportUserPrompt": "@{{username}} hakkında hangi sorunu bildirmek istiyorsunuz?", "reportUserOptionAbuseAndHarrassmentTitle": "İstismar ve Taciz", "reportUserOptionAbuseAndHarrassmentDescription": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> içeren <PERSON>, ne<PERSON>ret içerikli içerikler ve referanslar", "reportUserOptionPrivacyAndImpersonationTitle": "Gizlilik ve Başkasının Kimliğine Bürünme", "reportUserOptionPrivacyAndImpersonationDescription": "Özel bilgileri paylaşmak veya ifşa etmekle tehdit etme, başka biri gibi davranma", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, köt<PERSON> niyetli bağlantılar", "reportUserSuccess": "Kullanıcı Raporu Gönderildi.", "settingsClaimUsernameTitle": "Kullanıcı Adı oluştur", "settingsClaimUsernameDescription": "Cüzdanınız kadar benzersiz bir benzersiz kimlik", "settingsClaimUsernameValueProp1": "Basitleş<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameValueProp1Description": "Uzun ve kompleks adreslere veda edin ve kullanıcı dostu bir kimliğe merhaba deyin", "settingsClaimUsernameValueProp2": "<PERSON>ha <PERSON>ı<PERSON>lı ve <PERSON>", "settingsClaimUsernameValueProp2Description": "Kolayca kripto gönderin ve alın, cüzdanınıza giriş yapın ve arkadaşlarınızla bağlantı kurun", "settingsClaimUsernameValueProp3": "Senkronize kalın", "settingsClaimUsernameValueProp3Description": "Herhangi bir hesabı kullanıcı adınıza bağlayın ve tüm cihazlarınızda senkronize olur", "settingsClaimUsernameHelperText": "Phantom <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON><PERSON> ben<PERSON> adınız", "settingsClaimUsernameValidationDefault": "Bu kullanıcı adı daha sonra değiştirilemez", "settingsClaimUsernameValidationAvailable": "Kullanıcı adı kullanılabilir", "settingsClaimUsernameValidationUnavailable": "Kullanıcı adı kullanılamıyor", "settingsClaimUsernameValidationServerError": "Kullanıcı adının uygun olup olmadığı kontrol edilemiyor, lütfen daha sonra tekrar deneyin", "settingsClaimUsernameValidationErrorLine1": "Geçersiz kullanıcı ad.", "settingsClaimUsernameValidationErrorLine2": "Kullanı<PERSON><PERSON> adları {{minChar}} ila {{maxChar}} karakter arasında olmalıdır ve yalnızca harf ve rakam içerebilir.", "settingsClaimUsernameValidationLoading": "Bu kullanıcı adının kullanılabilir olup olmadığı kontrol ediliyor...", "settingsClaimUsernameSaveAndContinue": "<PERSON><PERSON> ve <PERSON>", "settingsClaimUsernameChooseAvatarTitle": "Ava<PERSON>", "settingsClaimUsernameAnonymousAuthTitle": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameAnonymousAuthDescription": "İmzayla Phantom Hesabınızda anonim olarak oturum açın", "settingsClaimUsernameAnonymousAuthBadge": "Bunun nasıl işlediğini öğrenin", "settingsClaimUsernameLinkWalletsTitle": "Cüzdanlarınızı bağlayın", "settingsClaimUsernameLinkWalletsDescription": "Kullanıcı adınızla diğer cihazlarda görünen cüzdanları seçin", "settingsClaimUsernameLinkWalletsBadge": "Herkese Açık Şekilde <PERSON>ü<PERSON>z", "settingsClaimUsernameConnectAccountsTitle": "Hesapları bağlayın", "settingsClaimUsernameConnectAccountsHelperText": "Her bir zincir adresi kullanıcı adınıza bağlanır. Bunları daha sonra değiştirebilirsiniz.", "settingsClaimUsernameContinue": "<PERSON><PERSON> et", "settingsClaimUsernameCreateUsername": "Kullanıcı Adı oluştur", "settingsClaimUsernameCreating": "Kullanıcı adı oluşturuluyor...", "settingsClaimUsernameSuccess": "Kullanıcı Adı Oluşturuldu!", "settingsClaimUsernameError": "Kullanıcı adınızı oluştururken bir hatayla karşılaştık", "settingsClaimUsernameTryAgain": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameSuccessHelperText": "Artık tüm Phantom cüzdanlarınızda yeni kullanıcı adınızı kullanabilirsiniz", "settingsClaimUsernameSettingsSyncedTitle": "Senk<PERSON><PERSON>", "settingsClaimUsernameSettingsSyncedHelperText": "Uzun ve kompleks adreslere veda edin ve kullanıcı dostu bir kimliğe merhaba deyin", "settingsClaimUsernameSendToUsernameTitle": "Kullanıcı Adı<PERSON>", "settingsClaimUsernameSendToUsernameHelperText": "Kolayca kripto gönderin ve alın, cüzdanınıza giriş yapın ve arkadaşlarınızla bağlantı kurun", "settingsClaimUsernameManageAddressesTitle": "Herkese Açık Adresler", "settingsClaimUsernameManageAddressesHelperText": "Kullanıcı adınıza gönderilen token veya koleksiyonluklar bu adreslere gönderilir", "settingsClaimUsernameManageAddressesBadge": "Herkese Açık Şekilde Görüntülenebilir", "settingsClaimUsernameEditAddressesTitle": "Herkese Açık Adresleri yönet", "settingsClaimUsernameEditAddressesHelperText": "Kullanıcı adınıza gönderilen token veya koleksiyonluklar bu adreslere gönderilir. Zincir başına bir adres seçin.", "settingsClaimUsernameEditAddressesError": "<PERSON><PERSON> ba<PERSON><PERSON>na sadece bir adrese izin verilir.", "settingsClaimUsernameEditAddressesEditAddress": "Adresleri düzenle", "settingsClaimUsernameNoAddressesSaved": "Kayıtlı herkese açık adres yok", "settingsClaimUsernameSave": "<PERSON><PERSON>", "settingsClaimUsernameDone": "<PERSON><PERSON>", "settingsClaimUsernameWatching": "İzleniyor", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} Hesap", "settingsClaimUsernameNoOfAccountsSingular": "1 Hesap", "settingsClaimUsernameEmptyAccounts": "Hesap yok", "settingsClaimUsernameSettingTitle": "@kullanıcıadı oluşturun", "settingsClaimUsernameSettingDescription": "Cüzdanınız için ben<PERSON>iz bir kimlik", "settingsManageUserProfileAbout": "Hakkında", "settingsManageUserProfileAboutUsername": "Kullanıcı adı", "settingsManageUserProfileAboutBio": "Bio", "settingsManageUserProfileTitle": "<PERSON><PERSON>", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "<PERSON><PERSON>", "settingsManageUserProfileAuthFactorsDescription": "Phantom <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> hangi tohum ifadelerinin veya özel anahtarların giriş yapabileceğini seçin.", "settingsManageUserProfileUpdateAuthFactorsToast": "İzin faktörleri güncellendi!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "İzin faktörleri güncellenemedi!", "settingsManageUserProfileBiography": "<PERSON><PERSON>'<PERSON><PERSON> d<PERSON>", "settingsManageUserProfileBiographyDescription": "Profilinize kısa bir bio ekleyin", "settingsManageUserProfileUpdateBiographyToast": "Bio Güncellendi!", "settingsManageUserProfileUpdateBiographyToastFailure": "Bir <PERSON><PERSON>. <PERSON><PERSON><PERSON>", "settingsManageUserProfileBiographyNoUrlMessage": "Lütfen bio'nuzdan URL'leri kaldırın", "settingsManageUserProfileLinkedWallets": "Bağlı Cüzdanlar", "settingsManageUserProfileLinkedWalletsDescription": "Phantom <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> giriş yaparken diğer cihazlarda görünen cüzdanları seçin.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Bağlı cüzdanlar güncellendi!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Bağlı cüzdanlar güncellenemedi!", "settingsManageUserProfilePrivacy": "Gizlilik", "settingsManageUserProfileUpdatePrivacyStateToast": "Gizlilik güncellendi!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Gizlilik güncellenemedi!", "settingsManageUserProfilePublicAddresses": "Herkese Açık Adresler", "settingsManageUserProfileUpdatePublicAddressToast": "Herkese açık adres güncellendi!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Herkese açık adres güncellenemedi!", "settingsManageUserProfilePrivacyStatePublic": "Herkese açık", "settingsManageUserProfilePrivacyStatePublicDescription": "Profiliniz ve herkese açık adresleriniz herkes tarafından görülebilir ve aranabilir", "settingsManageUserProfilePrivacyStatePrivate": "<PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePrivateDescription": "Profiliniz herkes tarafından aranabilir ancak başkalarının profilinizi ve herkese açık adreslerinizi görüntülemek için izin istemeleri gerekir", "settingsManageUserProfilePrivacyStateInvisible": "Görünmez", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Profiliniz ve herkese açık adresleriniz gizlidir ve her yerde keşfedilemez", "settingsDownloadPhantom": "Phantom'ı indir", "settingsLogOut": "Çıkış Yap", "seedlessAddAWalletPrimaryText": "<PERSON><PERSON><PERSON><PERSON> ekle", "seedlessAddAWalletSecondaryText": "<PERSON><PERSON><PERSON> yapın veya mevcut bir cüzdanı içe aktarın ", "seedlessAddSeedlessWalletPrimaryText": "Tohum<PERSON>z <PERSON>ü<PERSON>", "seedlessAddSeedlessWalletSecondaryText": "Apple Kimliği, Google veya E-postanızı kullanın", "seedlessCreateNewWalletPrimaryText": "Yeni Cüzdan oluşturulsun mu?", "seedlessCreateNewWalletSecondaryText": "Bu e-postanın cüzdanı yok, cüzdan oluşturmak ister misiniz?", "seedlessCreateNewWalletButtonText": "Cüzdan oluştur", "seedlessCreateNewWalletNoBundlePrimaryText": "Cüzdan bulunamadı", "seedlessCreateNewWalletNoBundleSecondaryText": "Bu e-postanın cüzdanı yok", "seedlessCreateNewWalletNoBundleButtonText": "<PERSON><PERSON>", "seedlessEmailOptionsPrimaryText": "E-postanızı Seçin", "seedlessEmailOptionsSecondaryText": "Apple veya Google hesabınızla bir cüzdan ekleyin ", "seedlessEmailOptionsButtonText": "E-post<PERSON>la devam et", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Apple kimliğinizle cüzdan oluşturun", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Google e-postanızla cüzdan oluşturun", "seedlessAlreadyExistsPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessAlreadyExistsSecondaryText": "Bu e-postada zaten oluşturulmuş bir cüzdan var, bunun yerine giriş yapmak ister misiniz?", "seedlessSignUpWithAppleButtonText": "Apple ile kaydol", "seedlessContinueWithAppleButtonText": "Apple ile devam et", "seedlessSignUpWithGoogleButtonText": "Google ile kaydol", "seedlessContinueWithGoogleButtonText": "Google ile devam et", "seedlessCreateAPinPrimaryText": "PIN oluşturun", "seedlessCreateAPinSecondaryText": "Bu, cüzdanınızın tüm cihazlarınızda güvenli olmasını sağlamak için kullanılır. <1>Bu, kurtarılamaz.</1>", "seedlessContinueText": "<PERSON><PERSON> et", "seedlessConfirmPinPrimaryText": "PIN'inizi onaylayın", "seedlessConfirmPinSecondaryText": "Bu PIN kodunu unutursanız cüzdanınızı yeni bir cihazda kurtaramazsınız.", "seedlessConfirmPinButtonText": "PIN oluştur", "seedlessConfirmPinError": "Yanlış PIN. Lütfen tekrar deneyin", "seedlessAccountsImportedPrimaryText": "İçe Aktarılan Hesaplar", "seedlessAccountsImportedSecondaryText": "Bu hesaplar otomatik olarak cüzdanınıza aktarılır", "seedlessPreviouslyImportedTag": "Daha ö<PERSON>den içe aktarılan", "seedlessEnterPinPrimaryText": "PIN'inizi girin", "seedlessEnterPinInvalidPinError": "Yanlış PIN girildi. Yalnızca 4 haneli sayılara izin veriliyor", "seedlessEnterPinNumTriesLeft": "{{numTries}} deneme kaldı.", "seedlessEnterPinCooldown": "{{minutesLeft}}:{{secondsLeft}} i<PERSON><PERSON>e tekrar deneyin", "seedlessEnterPinIncorrectLength": "PIN tam olarak 4 haneli olmalıdır", "seedlessEnterPinMatch": "PIN'ler <PERSON>", "seedlessDoneText": "<PERSON><PERSON>", "seedlessEnterPinToSign": "Bu işlemi imzalamak için PIN'inizi girin", "seedlessSigning": "İmzalanıyor", "seedlessCreateSeed": "Tohum tümcecikli cüzdan oluşturun", "seedlessImportOptions": "Diğer içe aktarma seçenekleri", "seedlessImportPrimaryText": "İçe Aktarma Seçenekleri", "seedlessImportSecondaryText": "<PERSON><PERSON>, <PERSON>zel anahtar veya donanım cüzdanınızla mevcut bir cüzdanı içe aktarın", "seedlessImportSeedPhrase": "Tohum Tümceciğini içe aktarın", "seedlessImportPrivateKey": "Özel Anahtarı içe aktarın", "seedlessConnectHardwareWallet": "Donanım Cüzdanı bağlayın", "seedlessTryAgain": "<PERSON><PERSON><PERSON>", "seedlessCreatingWalletPrimaryText": "Cüzdan oluşturuluyor", "seedlessCreatingWalletSecondaryText": "So<PERSON>al cüzdan ekleniyor", "seedlessLoadingWalletPrimaryText": "Cüzdan yükleniyor", "seedlessLoadingWalletSecondaryText": "Bağlı cüzdanlarınız içe aktarılıyor ve izleniyor", "seedlessLoadingWalletErrorPrimaryText": "Cüzdan yüklenemedi", "seedlessCreatingWalletErrorPrimaryText": "Cüzdan oluşturulamadı", "seedlessErrorSecondaryText": "Lütfen tekrar deneyin", "seedlessAuthAlreadyExistsErrorText": "Sağlanan e-posta zaten farklı bir Phantom hesabına ait", "seedlessAuthUnknownErrorText": "Bilinmeyen bir hata <PERSON>, l<PERSON><PERSON><PERSON> daha sonra tekrar deneyin", "seedlessAuthUnknownErrorTextRefresh": "Bilinmeyen bir hata <PERSON>, lütfen daha sonra tekrar deneyin. Tekrar denemek için sayfa<PERSON>ı yeni<PERSON>.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON><PERSON> kapat", "seedlessWalletExistsErrorPrimaryText": "Cihazınızda zaten sosyal bir cüzdan mevcut", "seedlessWalletExistsErrorSecondaryText": "Lütfen geri gidin veya bu ekranı kapatın", "seedlessValueProp1PrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessValueProp1SecondaryText": "Google veya Apple hesabı kullanarak bir cüzdan oluşturun ve web3'yi kolayca keşfetmeye başlayın", "seedlessValueProp2PrimaryText": "Gelişmiş güvenlik", "seedlessValueProp2SecondaryText": "Cüzdanınız güvenli bir şekilde saklanır ve birden fazla faktörde merkezi de<PERSON>", "seedlessValueProp3PrimaryText": "<PERSON><PERSON>", "seedlessValueProp3SecondaryText": "Google veya Apple hesabınızı ve 4 haneli bir PIN'i kullanarak cüzdanınıza erişimi kurtarın", "seedlessLoggingIn": "<PERSON><PERSON><PERSON> yapılıyor...", "seedlessSignUpOrLogin": "Kay<PERSON>lun veya Giriş <PERSON>ı<PERSON>", "seedlessContinueByEnteringYourEmail": "E-postanızı girerek devam edin", "seedless": "Tohumsuz", "seed": "<PERSON><PERSON>", "seedlessVerifyPinPrimaryText": "PIN'i doğrula", "seedlessVerifyPinSecondaryText": "Devam etmek için lütfen PIN numaranızı girin", "seedlessVerifyPinVerifyButtonText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessVerifyPinForgotButtonText": "Unuttunuz mu?", "seedlessPinConfirmButtonText": "<PERSON><PERSON><PERSON>", "seedlessVerifyToastPrimaryText": "PIN'inizi doğrulayın", "seedlessVerifyToastSecondaryText": "Hatırlamanız için zaman zaman PIN kodunuzu doğrulamanızı isteriz. Unutursanız cüzdanınızı kurtarmanız mümkün olmaz.", "seedlessVerifyToastSuccessText": "PIN numaranız doğrulandı!", "seedlessForgotPinPrimaryText": "Başka bir cihaz kullanarak PIN'i sıfırlayın", "seedlessForgotPinSecondaryText": "<PERSON><PERSON><PERSON><PERSON>, PIN'inizi sadece giriş yaptı<PERSON>ınız diğer cihazlarda sıfırlayabilirsiniz", "seedlessForgotPinInstruction1PrimaryText": "Diğer Cihazı aç", "seedlessForgotPinInstruction1SecondaryText": "Phantom hesabınızın e-postanızla oturum açtığı başka bir cihaza gidin", "seedlessForgotPinInstruction2PrimaryText": "<PERSON><PERSON><PERSON>'a gidin", "seedlessForgotPinInstruction2SecondaryText": "Ayar<PERSON><PERSON><PERSON><PERSON> \"Güvenlik ve Gizlilik\" ve ardından \"PIN'i sıfırla\" seçeneğini belirleyin", "seedlessForgotPinInstruction3PrimaryText": "Yeni PIN'inizi beli<PERSON>", "seedlessForgotPinInstruction3SecondaryText": "Yeni PIN'inizi belirledikten sonra artık bu cihazda cüzdanınıza giriş yapabilirsiniz", "seedlessForgotPinButtonText": "<PERSON>u adımları tamamladım", "seedlessResetPinPrimaryText": "PIN'i sıfırla", "seedlessResetPinSecondaryText": "Hatırlayacağınız yeni bir PIN girin. Bu, tüm cihazlarınızda cüzdanınızı güvenceye almak için kullanılır", "seedlessResetPinSuccessText": "PIN numaranız güncellendi!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Cüzdan oluşturarak <1>Hizmet Koşullarımızı</1> ve <5>Gizlilik Politikamızı</5> kabul etmiş olursunuz", "pageNotFound": "Sayfa bulunamadı", "pageNotFoundDescription": "Sizi terk etmedik! Bu sayfa mevcut değil veya taşınmış.", "webTokenPagesLegalDisclaimer": "Fiyatlandırma bilgileri yalnızca bilgilendirme amaçlıdır ve finansal tavsiye niteliğinde değildir. Piyasa verileri üçüncü taraflarca sağlanır ve Phantom bu bilgilerin doğruluğu konusunda herhangi bir beyanda bulunmaz.", "signUpOrLogin": "<PERSON><PERSON><PERSON>n veya giriş yapın", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Hesap oluşturarak <1>Hizmet Koşullarımızı</1> ve <5>Gizlilik Politikamızı</5> kabul etmiş olursunuz", "feedNoActivity": "Henüz aktivite yok", "followRequests": "Takip <PERSON>", "following": "Takip ediliyor", "followers": "Takipçiler", "follower": "Takipçi", "joined": "Katıldı", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "Takipçi Yok", "noFollowing": "Takip Ed<PERSON>", "noUsersFound": "Kullanıcı Bulunamadı", "viewProfile": "<PERSON><PERSON>", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}