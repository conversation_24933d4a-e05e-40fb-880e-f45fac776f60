import{Ma as p}from"./chunk-JD6NH5K6.js";import{wc as c}from"./chunk-MZZEJ42N.js";import{a as u}from"./chunk-7X4NV6OJ.js";import{f as d,h as s,n as r}from"./chunk-3KENBVE7.js";s();r();var e=d(u());var S=()=>{let{handleShowModalVisibility:o}=p(),n=c(),f=(0,e.useCallback)(a=>{let t=a.networkId,i={balance:a.balance,required:a.required};n.capture("showInsufficientBalanceModal",{data:{context:"swapper",networkId:t,token:i}}),o("insufficientBalance",{networkId:t,token:i})},[o,n]),l=(0,e.useCallback)(()=>{o("swapReview")},[o]),m=(0,e.useCallback)(()=>{o("swapTermsOfService")},[o]),w=(0,e.useCallback)(()=>{o("swapConfirmation")},[o]);return{goToInsufficientBalance:f,goToSwapReview:l,goToSwapTermsOfService:m,goToConfirmation:w}};export{S as a};
//# sourceMappingURL=chunk-4QLXYFPC.js.map
