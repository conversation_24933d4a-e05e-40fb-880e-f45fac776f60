import type { Page } from "@playwright/test"
import { expect } from "@playwright/test"
import UIActions from "../base/actions/UIActions"
import { DashboardPage } from "./DashboardPage"
import { TokenDetailSelectors } from "../selectors/TokenDetailSelectors"
import * as path from 'path'

export class TokenDetailPage {
  private uiActions: UIActions
  private dashboardPage: DashboardPage
  private page: Page

  constructor(page: Page) {
    this.uiActions = new UIActions(page)
    this.dashboardPage = new DashboardPage(page)
    this.page = page
  }

  /**
   * Truy cập trang chi tiết token theo URL
   * @param tokenUrl URL của token cần truy cập
   */
  async navigateToTokenPage(tokenUrl: string): Promise<void> {
    await this.uiActions.goto(tokenUrl, "Token Page")
    await this.uiActions.waitForLoadState()
  }

  /**
   * Chọn tab Market để mua/bán theo giá thị trường
   */
  async selectMarketTab(): Promise<void> {
    await this.uiActions.element(TokenDetailSelectors.marketTab, "Market Tab").click()
  }

  /**
   * Chọn tab Limit để đặt lệnh giới hạn
   */
  async selectLimitTab(): Promise<void> {
    await this.uiActions.element(TokenDetailSelectors.limitTab, "Limit Tab").click()
  }

  /**
   * Chọn tab Buy để mua token
   */
  async selectBuyTab(): Promise<void> {
    await this.uiActions.element(TokenDetailSelectors.buyTab, "Buy Tab").click()
  }

  /**
   * Chọn tab Sell để bán token
   */
  async selectSellTab(): Promise<void> {
    await this.uiActions.element(TokenDetailSelectors.sellTab, "Sell Tab").click()
  }

  /**
   * Nhập số lượng token cần mua/bán
   * @param amount Số lượng token cần mua/bán
   */
  async enterAmount(amount: string): Promise<void> {
    await this.uiActions.editBox(TokenDetailSelectors.amountInput, "Amount Input").fill(amount)
  }

  /**
   * Nhập giá trị slippage (độ trượt giá)
   * @param slippage Giá trị slippage (%)
   */
  async enterSlippage(slippage: string): Promise<void> {
    // Đảm bảo cài đặt nâng cao đã được mở
    await this.ensureAdvancedSettingsOpen()

    // Nhập giá trị slippage
    await this.uiActions.editBox(TokenDetailSelectors.slippageInput, "Slippage Input").fill(slippage)
  }

  /**
   * Nhập giá trị priority fee
   * @param fee Giá trị priority fee
   */
  async enterPriorityFee(fee: string): Promise<void> {
    // Đảm bảo cài đặt nâng cao đã được mở
    await this.ensureAdvancedSettingsOpen()

    // Nhập giá trị priority fee
    await this.uiActions.editBox(TokenDetailSelectors.priorityFeeInput, "Priority Fee Input").fill(fee)
  }

  /**
   * Đảm bảo rằng phần cài đặt nâng cao đã được mở
   * @private
   */
  private async ensureAdvancedSettingsOpen(): Promise<void> {
    // Kiểm tra xem phần cài đặt nâng cao đã được mở chưa
    const isSettingsAreaVisible = await this.page
      .locator(`${TokenDetailSelectors.advancedSettingsArea} ${TokenDetailSelectors.slippageInput}`)
      .isVisible()
      .catch(() => false);

    if (!isSettingsAreaVisible) {
      // Nếu chưa mở, mở cài đặt nâng cao
      await this.openAdvancedSettings();
    }
  }

  /**
   * Click nút Buy để thực hiện giao dịch mua
   * @param tokenSymbol Tùy chọn - symbol của token, nếu cung cấp sẽ dùng selector chính xác hơn
   */
  async clickBuyButton(tokenSymbol?: string): Promise<void> {
    console.log(">>> ATTEMPTING TO CLICK BUY BUTTON <<<");

    try {
      // Thử lấy symbol token từ trang nếu không được cung cấp
      if (!tokenSymbol) {
        try {
          // Đầu tiên thử lấy từ tiêu đề tab - phương pháp đáng tin cậy hơn
          tokenSymbol = await this.getTokenSymbolFromTitle();
          console.log(`- Token symbol from page title: ${tokenSymbol}`);
        } catch (e) {
          // Nếu không thành công, thử phương pháp thay thế
          try {
            tokenSymbol = await this.getTokenSymbol();
            console.log(`- Token symbol from page content: ${tokenSymbol}`);
          } catch (err) {
            console.log("- Could not detect token symbol, using default selectors");
          }
        }
      }

      // Tạo danh sách các selector để thử, sắp xếp theo thứ tự ưu tiên
      const selectors = [];

      // Ưu tiên selector theo symbol cụ thể nếu có
      if (tokenSymbol) {
        if (tokenSymbol.toUpperCase() === "PEPE") {
          selectors.push({ name: "buyPepeButton", selector: TokenDetailSelectors.buyPepeButton });
        }
        selectors.push({ name: `buyTokenWithIcon(${tokenSymbol})`, selector: TokenDetailSelectors.buyTokenWithIcon(tokenSymbol) });
        selectors.push({ name: `buyTokenButton(${tokenSymbol})`, selector: TokenDetailSelectors.buyTokenButton(tokenSymbol) });
      }

      // Thêm các selector còn lại
      selectors.push({ name: "buyLightningButton", selector: TokenDetailSelectors.buyLightningButton });
      selectors.push({ name: "buyActionButton", selector: TokenDetailSelectors.buyActionButton });
      selectors.push({ name: "finalBuyButton", selector: TokenDetailSelectors.finalBuyButton });
      selectors.push({ name: "defaultBuyButton", selector: TokenDetailSelectors.buyButton });

      // Thực hiện click với strategy thử lần lượt theo thứ tự ưu tiên
      let clicked = false;

      for (const {name, selector} of selectors) {
        if (clicked) break;

        try {
          const isVisible = await this.page.locator(selector).isVisible({ timeout: 2000 }).catch(() => false);

          if (isVisible) {
            console.log(`- CLICKING button using selector: ${name}`);
            await this.page.locator(selector).click({ timeout: 5000, force: true });
            console.log(`- CLICK SUCCESSFUL with selector: ${name}`);
            clicked = true;
            await this.page.waitForTimeout(1000);
            break;
          }
        } catch (error) {
          console.error(`- ERROR clicking with ${name}:`, error instanceof Error ? error.message : "Unknown error");
        }
      }

      if (!clicked) {
        throw new Error("Could not click any Buy button");
      }
    } catch (error) {
      console.error("ERROR CLICKING BUY BUTTON:", error instanceof Error ? error.message : "Unknown error");
      throw new Error(`Failed to click Buy button: ${error instanceof Error ? error.message : "Unknown error"}`);
    }

    console.log(">>> BUY BUTTON CLICKED SUCCESSFULLY <<<");
  }

  /**
   * Click nút Sell để thực hiện giao dịch bán
   * @param tokenSymbol Tùy chọn - symbol của token, nếu cung cấp sẽ dùng selector chính xác hơn
   */
  async clickSellButton(tokenSymbol?: string): Promise<void> {
    console.log(">>> ATTEMPTING TO CLICK SELL BUTTON <<<");

    try {
      // Thử lấy symbol token từ trang nếu không được cung cấp
      if (!tokenSymbol) {
        try {
          // Đầu tiên thử lấy từ tiêu đề tab - phương pháp đáng tin cậy hơn
          tokenSymbol = await this.getTokenSymbolFromTitle();
          console.log(`- Token symbol from page title: ${tokenSymbol}`);
        } catch (e) {
          // Bỏ qua nếu không lấy được token symbol
          console.log("- Could not detect token symbol, using default selectors");
        }
      }

      // Tạo danh sách các selector để thử, sắp xếp theo thứ tự ưu tiên
      const selectors = [];

      // Ưu tiên selector theo symbol cụ thể nếu có
      if (tokenSymbol) {
        if (tokenSymbol.toUpperCase() === "PEPE") {
          selectors.push({ name: "sellPepeButton", selector: TokenDetailSelectors.sellPepeButton });
        }
        selectors.push({ name: `sellTokenWithIcon(${tokenSymbol})`, selector: TokenDetailSelectors.sellTokenWithIcon(tokenSymbol) });
        selectors.push({ name: `sellTokenButton(${tokenSymbol})`, selector: TokenDetailSelectors.sellTokenButton(tokenSymbol) });
      }

      // Thêm các selector còn lại
      selectors.push({ name: "sellLightningButton", selector: TokenDetailSelectors.sellLightningButton });
      selectors.push({ name: "sellActionButton", selector: TokenDetailSelectors.sellActionButton });
      selectors.push({ name: "finalSellButton", selector: TokenDetailSelectors.finalSellButton });
      selectors.push({ name: "positionSellButton", selector: TokenDetailSelectors.positionSellButton });
      selectors.push({ name: "anySellButton", selector: TokenDetailSelectors.anySellButton });
      selectors.push({ name: "defaultSellButton", selector: TokenDetailSelectors.sellButton });

      // Thực hiện click với strategy thử lần lượt theo thứ tự ưu tiên
      let clicked = false;

      for (const {name, selector} of selectors) {
        if (clicked) break;

        try {
          const isVisible = await this.page.locator(selector).isVisible({ timeout: 2000 }).catch(() => false);

          if (isVisible) {
            console.log(`- CLICKING button using selector: ${name}`);
            await this.page.locator(selector).click({ timeout: 5000, force: true });
            console.log(`- CLICK SUCCESSFUL with selector: ${name}`);
            clicked = true;
            await this.page.waitForTimeout(1000);
            break;
          }
        } catch (error) {
          console.error(`- ERROR clicking with ${name}:`, error instanceof Error ? error.message : "Unknown error");
        }
      }

      if (!clicked) {
        throw new Error("Could not click any Sell button");
      }
    } catch (error) {
      console.error("ERROR CLICKING SELL BUTTON:", error instanceof Error ? error.message : "Unknown error");
      throw new Error(`Failed to click Sell button: ${error instanceof Error ? error.message : "Unknown error"}`);
    }

    console.log(">>> SELL BUTTON CLICKED SUCCESSFULLY <<<");
  }

  /**
   * Xác minh thông báo giao dịch thành công
   */
  async verifyTransactionResult(): Promise<boolean> {
    console.log("=== VERIFYING TRANSACTION RESULT ===");

    try {
      // Lặp trong 10 lần, mỗi lần chờ 1 giây, tổng cộng 10 giây để tìm thông báo
      for (let attempt = 1; attempt <= 10; attempt++) {
        console.log(`- Checking for transaction messages (attempt ${attempt}/10)...`);

        // Tập trung kiểm tra thông báo "wallets traded successfully" trước tiên
        const walletsTradedMessage = 'text=/.*wallets traded successfully.*/, div:has-text("wallets traded successfully"), div.chakra-alert:has-text("wallets traded successfully")';
        try {
          const isWalletsTradedVisible = await this.page.locator(walletsTradedMessage).isVisible({ timeout: 1000 }).catch(() => false);
          if (isWalletsTradedVisible) {
            const message = await this.page.locator(walletsTradedMessage).textContent().catch(() => "Unknown message");
            console.log(`  ✅ WALLETS TRADED SUCCESSFULLY MESSAGE FOUND: "${message}"`);

            // Lưu HTML hiện tại để có thêm thông tin
            const html = await this.page.content();
            console.log("- Success message found, continuing...");

            // Nếu có nút Close, click để đóng thông báo
            try {
              const closeButton = await this.page.locator(TokenDetailSelectors.closeSuccessButton).isVisible({ timeout: 1000 });
              if (closeButton) {
                console.log("  - Closing success message");
                await this.page.locator(TokenDetailSelectors.closeSuccessButton).click().catch(() => {});
              }
            } catch (e) {
              // Bỏ qua lỗi khi click nút close
            }

            return true;
          }
        } catch (error) {
          // Bỏ qua lỗi và tiếp tục kiểm tra
        }

        // Kiểm tra các thông báo thành công khác
        console.log("- Checking for other success messages:");
        const successSelectors = [
          TokenDetailSelectors.successMessage,
          TokenDetailSelectors.walletsTradedSuccessMessage,
          TokenDetailSelectors.tradingSuccessToast,
          'div.chakra-alert:has-text("Success")',
          'text="Transaction successful"',
          'text=/.*Success.*/',
          'div:has-text("Success")',
          'div:has-text("Successful")',
          'div.chakra-toast:has-text("Success")',
          'div[role="alert"][data-status="success"]',
          '[data-status="success"]'
        ];

        for (const selector of successSelectors) {
          try {
            const isVisible = await this.page.locator(selector).isVisible({ timeout: 1000 }).catch(() => false);
            if (isVisible) {
              const message = await this.page.locator(selector).textContent().catch(() => "Unknown message");
              console.log(`  ✅ SUCCESS MESSAGE FOUND: "${message}" (Selector: ${selector})`);

              // Lưu HTML hiện tại để có thêm thông tin
              const html = await this.page.content();
              console.log("- Success message found, continuing...");

              return true;
            }
          } catch (error) {
            // Bỏ qua lỗi và tiếp tục kiểm tra selector tiếp theo
          }
        }

        // Kiểm tra các thông báo lỗi
        console.log("- Checking for error messages:");
        const errorSelectors = [
          'div.chakra-alert.chakra-alert--status-error',
          'text=/.*Rejected.*/',
          'text=/.*Cancelled.*/',
          'text=/.*Error.*/',
          'text=/.*Failed.*/',
          'div:has-text("Error")',
          'div:has-text("Failed")',
          'div[role="alert"][data-status="error"]'
        ];

        for (const selector of errorSelectors) {
          try {
            const isVisible = await this.page.locator(selector).isVisible({ timeout: 1000 }).catch(() => false);
            if (isVisible) {
              const message = await this.page.locator(selector).textContent().catch(() => "Unknown error");
              console.log(`  ❌ ERROR MESSAGE FOUND: "${message}" (Selector: ${selector})`);

              // Lưu HTML hiện tại để có thêm thông tin
              const html = await this.page.content();
              console.log("- Error message found, stopping verification");

              return false;
            }
          } catch (error) {
            // Bỏ qua lỗi và tiếp tục kiểm tra selector tiếp theo
          }
        }

        // Nếu không tìm thấy message, chờ thêm 1 giây và thử lại
        if (attempt < 10) {
          console.log(`  - No message found, waiting 1 more second (attempt ${attempt}/10)...`);
          await this.page.waitForTimeout(1000);
        }
      }

      // Kiểm tra DOM để tìm chỉ dẫn bổ sung
      const bodyText = await this.page.locator('body').textContent();
      if (bodyText && (
          bodyText.includes('wallets traded successfully') ||
          bodyText.includes('traded successfully') ||
          bodyText.includes('success') ||
          bodyText.includes('Success')
        )) {
        console.log("✅ Found success keywords in page content, transaction was likely successful");
        return true;
      }

      // Nếu đã thử hết 10 lần mà không tìm thấy thông báo rõ ràng
      console.log("- No definitive success or error message found after 10 attempts");
      console.log("- Final check: Examining DOM for any toast messages or alerts...");

      // Tìm kiếm tất cả các toast/alert trên trang và lưu lại nội dung
      const toastElements = await this.page.locator('div[role="alert"], .chakra-toast, .chakra-alert').all();
      if (toastElements.length > 0) {
        console.log(`- Found ${toastElements.length} alert/toast elements:`);
        for (let i = 0; i < toastElements.length; i++) {
          const text = await toastElements[i].textContent();
          console.log(`  [${i+1}] ${text}`);
        }
      }

      console.log("- WARNING: Transaction result could not be definitively verified");
      console.log("- Capturing full page screenshot and HTML for analysis");
      await this.page.screenshot({ path: 'transaction-final-full-page.png', fullPage: true });

      // Lưu toàn bộ HTML để phân tích
      const finalHtml = await this.page.content();
      console.log("- Transaction verification inconclusive, continuing with test");

      // Trả về false để báo rằng không thể xác minh kết quả
      return false;
    } catch (error) {
      console.error(`Error verifying transaction result: ${error instanceof Error ? error.message : "Unknown error"}`);
      await this.page.screenshot({ path: 'transaction-verify-error.png' });
      return false;
    }
  }

  /**
   * Lấy thông báo giao dịch thành công cụ thể về "wallets traded successfully"
   * @returns Message hoặc null nếu không tìm thấy
   */
  async getTransactionSuccessMessage(): Promise<string | null> {
    console.log("Checking for transaction success message...");

    try {
      // Danh sách các selector để tìm thông báo thành công
      const successMessageSelectors = [
        // Ưu tiên thông báo "wallets traded successfully"
        'text=/.*wallets traded successfully.*/',
        'div:has-text("wallets traded successfully")',
        'div.chakra-alert:has-text("wallets traded successfully")',

        // Các thông báo thành công khác
        TokenDetailSelectors.successMessage,
        TokenDetailSelectors.walletsTradedSuccessMessage,
        TokenDetailSelectors.tradingSuccessToast
      ];

      // Kiểm tra và chờ đợi thông báo xuất hiện (tối đa 10 giây)
      for (let i = 0; i < 10; i++) {
        console.log(`- Checking for success message (attempt ${i+1}/10)...`);

        // Kiểm tra từng selector
        for (const selector of successMessageSelectors) {
          try {
            const isVisible = await this.page.locator(selector).isVisible({ timeout: 1000 }).catch(() => false);

            if (isVisible) {
              const messageText = await this.page.locator(selector).textContent();
              console.log(`✅ SUCCESS MESSAGE FOUND: "${messageText}"`);
              return messageText || "Success message found (no text content)";
            }
          } catch (error) {
            // Bỏ qua lỗi và tiếp tục
          }
        }

        // Nếu không tìm thấy, đợi 1 giây và thử lại
        if (i < 9) {
          console.log(`- No success message found yet, waiting 1 more second...`);
          await this.page.waitForTimeout(1000);
        }
      }

      // Nếu không tìm thấy sau khi thử tất cả các attempt
      console.log("❌ No success message found after 10 seconds");

      // Kiểm tra DOM để tìm chỉ dẫn
      const bodyText = await this.page.locator('body').textContent();
      if (bodyText && bodyText.includes('wallets traded successfully')) {
        console.log("✅ Found 'wallets traded successfully' in page content");
        return "wallets traded successfully (in page content)";
      }

      return null;
    } catch (error) {
      console.error(`Error getting transaction success message: ${error instanceof Error ? error.message : "Unknown error"}`);
      return null;
    }
  }

  /**
   * Lấy giá hiện tại của token
   * @returns Giá hiện tại của token
   */
  async getTokenPrice(): Promise<string> {
    console.log("Getting token price...")
    const price = await this.uiActions.element(TokenDetailSelectors.tokenPrice, "Token Price").getTextContent()
    console.log(`Token price: ${price}`)
    return price || ""
  }

  /**
   * Lấy ký hiệu (symbol) của token
   * @returns Ký hiệu của token
   */
  async getTokenSymbol(): Promise<string> {
    console.log("Getting token symbol...")
    const symbol = await this.uiActions.element(TokenDetailSelectors.tokenSymbol, "Token Symbol").getTextContent()
    console.log(`Token symbol: ${symbol}`)
    return symbol || ""
  }

  /**
   * Lấy tên đầy đủ của token
   * @returns Tên đầy đủ của token
   */
  async getTokenName(): Promise<string> {
    console.log("Getting token name...")
    const name = await this.uiActions.element(TokenDetailSelectors.tokenName, "Token Name").getTextContent()
    console.log(`Token name: ${name}`)
    return name || ""
  }

  /**
   * Lấy thông tin thanh khoản (liquidity) của token
   * @returns Giá trị thanh khoản
   */
  async getTokenLiquidity(): Promise<string> {
    console.log("Getting token liquidity...")
    const liquidity = await this.uiActions
      .element(TokenDetailSelectors.tokenLiquidity, "Token Liquidity")
      .getTextContent()
    console.log(`Token liquidity: ${liquidity}`)
    return liquidity || ""
  }

  /**
   * Lấy thông tin khối lượng giao dịch (volume) của token
   * @returns Giá trị khối lượng giao dịch
   */
  async getTokenVolume(): Promise<string> {
    console.log("Getting token volume...")
    const volume = await this.uiActions.element(TokenDetailSelectors.tokenVolume, "Token Volume").getTextContent()
    console.log(`Token volume: ${volume}`)
    return volume || ""
  }

  /**
   * Thêm token vào danh sách theo dõi (watchlist)
   */
  async addToWatchlist(): Promise<void> {
    console.log("Adding token to watchlist...")
    await this.uiActions.element(TokenDetailSelectors.addToWatchlistButton, "Add to Watchlist Button").click()
    console.log("Token added to watchlist.")
  }

  /**
   * Mở cài đặt nâng cao (slippage và priority fee)
   */
  async openAdvancedSettings(): Promise<void> {
    // Kiểm tra xem có mở rộng chưa
    const isExpanded = await this.page
      .locator(TokenDetailSelectors.advancedSettingsButton)
      .getAttribute('aria-expanded')
      .then(value => value === 'true')
      .catch(() => false);

    if (!isExpanded) {
      // Click vào nút mở rộng chỉ khi nó chưa được mở
      await this.uiActions.element(TokenDetailSelectors.advancedSettingsButton, "Advanced Settings Button").click();

      // Đợi một chút để animation hoàn thành
      await this.page.waitForTimeout(1000);

      // Xác minh rằng đã mở rộng thành công
      const expandedAfterClick = await this.page
        .locator(TokenDetailSelectors.advancedSettingsButton)
        .getAttribute('aria-expanded')
        .then(value => value === 'true')
        .catch(() => false);

      if (!expandedAfterClick) {
        // Thử lại nếu không mở được
        await this.uiActions.element(TokenDetailSelectors.advancedSettingsButton, "Advanced Settings Button").click();
        await this.page.waitForTimeout(1000);
      }
    }
  }

  /**
   * Thực hiện toàn bộ luồng mua token
   * @param tokenUrl URL của token cần mua
   * @param amount Số lượng token cần mua
   * @param slippage Giá trị slippage (%)
   * @param priorityFee Giá trị priority fee
   * @param tokenSymbol Tùy chọn - symbol của token, nếu cung cấp sẽ dùng để tìm nút Buy chính xác
   */
  async buyToken(tokenUrl: string, amount: string, slippage: string, priorityFee: string, tokenSymbol?: string): Promise<void> {
    console.log(`\n======== STARTING BUY TOKEN FLOW ========`);

    // Truy cập trang token
    await this.navigateToTokenPage(tokenUrl);

    // Thứ tự đúng:
    // 1. Chọn tab Buy
    // 2. Chọn tab Market/Limit
    // 3. Nhập số lượng
    // 4. Mở rộng để cài đặt slippage và priority fee
    // 5. Click nút Buy

    // 1. Chọn tab Buy
    await this.selectBuyTab();

    // 2. Chọn tab Market (mặc định thường đã là market)
    await this.selectMarketTab();

    // 3. Nhập số lượng token cần mua
    await this.enterAmount(amount);

    // 4. Mở rộng cài đặt nâng cao
    await this.openAdvancedSettings();

    // Nhập các thông số giao dịch nâng cao
    await this.enterSlippage(slippage);
    await this.enterPriorityFee(priorityFee);

    // Thử lấy token symbol nếu chưa được cung cấp
    if (!tokenSymbol) {
      // Thử lấy từ tiêu đề trang
      try {
        tokenSymbol = await this.getTokenSymbolFromTitle();
      } catch (e) {
        // Nếu không thành công, thử lấy từ nội dung trang
        try {
          tokenSymbol = await this.getTokenSymbol();
        } catch (e) {
          // Bỏ qua nếu không lấy được token symbol
        }
      }
    }

    // 5. Thực hiện giao dịch với token symbol (nếu có)
    await this.clickBuyButton(tokenSymbol);

    // Chờ một chút để trang xử lý request
    await this.page.waitForTimeout(3000);

    // 7. Xác minh kết quả giao dịch
    await this.verifyTransactionResult();

    // Chụp màn hình sau khi mua thành công
    const screenshotDir = path.join(process.cwd(), 'screenshots');
    await this.page.screenshot({ path: path.join(screenshotDir, 'buy-token-success.png') });

    console.log(`\n======== BUY TOKEN FLOW COMPLETED ========\n`)
  }

  /**
   * Lấy token symbol từ tiêu đề tab của trình duyệt
   * @returns Promise<string> - Symbol của token
   */
  async getTokenSymbolFromTitle(): Promise<string> {
    try {
      const pageTitle = await this.page.title();
      console.log(`Page title: ${pageTitle}`);

      // Tìm token symbol theo các pattern phổ biến
      // Pattern 1: "TokenSymbol $price - TokenName on Exchange" (ví dụ: "PEPE $0.0631 - Pepe on Solana")
      const pattern1 = /^([A-Z0-9]+)\s+\$/i;
      const match1 = pageTitle.match(pattern1);

      if (match1 && match1[1]) {
        console.log(`Found token symbol in title (pattern 1): ${match1[1]}`);
        return match1[1];
      }

      // Pattern 2: "TokenName (TokenSymbol)" (ví dụ: "Pepe (PEPE)")
      const pattern2 = /\(([A-Z0-9]+)\)/i;
      const match2 = pageTitle.match(pattern2);

      if (match2 && match2[1]) {
        console.log(`Found token symbol in title (pattern 2): ${match2[1]}`);
        return match2[1];
      }

      // Pattern 3: "TokenSymbol/USD on Exchange" (ví dụ: "PEPE/USD on Raydium")
      const pattern3 = /^([A-Z0-9]+)\/USD/i;
      const match3 = pageTitle.match(pattern3);

      if (match3 && match3[1]) {
        console.log(`Found token symbol in title (pattern 3): ${match3[1]}`);
        return match3[1];
      }

      // Nếu không tìm thấy, dựa vào nội dung trang
      console.log("Could not extract token symbol from title, falling back to page content method");
      return await this.getTokenSymbol();
    } catch (error) {
      console.error("Error getting token symbol from title:", error);
      // Fallback to other method
      return await this.getTokenSymbol();
    }
  }

  /**
   * Lấy số dư token hiện tại
   * @param tokenSymbol Symbol của token (tùy chọn), nếu cung cấp sẽ dùng để tìm chính xác hơn
   * @returns Promise<number> - Số dư token hoặc 0 nếu không tìm thấy
   */
  async getTokenBalance(tokenSymbol?: string): Promise<number> {
    console.log("Getting token balance...");

    try {
      // Danh sách các selector để tìm số dư token
      const tokenBalanceSelectors = [
        // Selector mới cho button menu chứa số lượng token
        '.chakra-menu__menu-button .css-8279j6 .css-lemfo4',
        'button[class*="chakra-menu__menu-button"] .css-lemfo4',
        '.css-8279j6 .css-lemfo4',

        // Selector tiêu chuẩn cho token balance
        TokenDetailSelectors.tokenBalance,
        TokenDetailSelectors.tokenBalanceAlternative,

        // Selector theo symbol nếu có
        tokenSymbol ? TokenDetailSelectors.tokenBalanceBySymbol(tokenSymbol) : null,

        // Các selector mẫu khác
        '.css-eep4we div:has(> span:has-text("Available")) > div',
        '.css-ku2d8x',
        'div.css-ku2d8x',
        'div:has(> span:has-text("Available")) + div',
        'div:has(> span:has-text("Balance")) + div',
        'div[data-testid="token-balance"]',
        '[aria-label="Amount"] + div:has(span)'
      ].filter(selector => selector !== null); // Loại bỏ các giá trị null

      let balanceText = null;
      let usedSelector = '';

      // Thử lần lượt các selector
      for (const selector of tokenBalanceSelectors) {
        try {
          const isVisible = await this.page.locator(selector).isVisible({ timeout: 2000 }).catch(() => false);
          if (isVisible) {
            balanceText = await this.page.locator(selector).textContent();
            console.log(`- Found balance text with selector "${selector}": ${balanceText}`);
            usedSelector = selector;
            break;
          }
        } catch (e) {
          // Bỏ qua lỗi và thử selector tiếp theo
        }
      }

      // Nếu vẫn không tìm thấy, thử tìm theo thuộc tính aria-expanded
      if (!balanceText) {
        try {
          // Tìm button có aria-expanded và chứa hình ảnh token
          const menuButton = this.page.locator('button[aria-expanded][aria-haspopup="menu"]').first();
          const isVisible = await menuButton.isVisible({ timeout: 2000 });

          if (isVisible) {
            // Lấy text từ toàn bộ button
            balanceText = await menuButton.innerText();
            console.log(`- Found menu button with text: ${balanceText}`);
            usedSelector = 'button[aria-expanded][aria-haspopup="menu"]';
          }
        } catch (e) {
          // Bỏ qua nếu không tìm thấy
        }
      }

      if (balanceText) {
        // Trích xuất số từ text (loại bỏ các ký tự không phải số và dấu chấm)
        const balanceMatch = balanceText.match(/([0-9,]*[.])?[0-9,]+/);
        if (balanceMatch && balanceMatch[0]) {
          // Loại bỏ dấu phẩy ngăn cách hàng nghìn và chuyển thành số
          const balance = parseFloat(balanceMatch[0].replace(/,/g, ''));
          console.log(`- Extracted token balance: ${balance} using selector: ${usedSelector}`);
          return balance;
        }
      }

      console.log(`- Could not determine token balance`);
      return 0;
    } catch (error) {
      console.error(`Error getting token balance: ${error instanceof Error ? error.message : "Unknown error"}`);
      return 0;
    }
  }

  /**
   * Thực hiện toàn bộ luồng bán token
   * @param tokenUrl URL của token cần bán
   * @param amount Số lượng token cần bán, có thể là % như "25%", "50%", "75%", "100%" hoặc giá trị cụ thể
   * @param slippage Giá trị slippage (%)
   * @param priorityFee Giá trị priority fee
   * @param tokenSymbol Tùy chọn - symbol của token, nếu cung cấp sẽ dùng để tìm nút Sell chính xác
   */
  async sellToken(tokenUrl: string, amount: string, slippage: string, priorityFee: string, tokenSymbol?: string): Promise<void> {
    console.log(`\n======== STARTING SELL TOKEN FLOW ========`);

    // 1. Chọn tab Sell
    await this.selectSellTab();

    // 2. Chọn tab Market (mặc định thường đã là market)
    await this.selectMarketTab();

    // 3. Lấy số lượng token hiện có, tính % và nhập giá trị vào ô amount

    // Đợi một chút để trang tải số lượng token
    await this.page.waitForTimeout(2000);

    // Kiểm tra xem amount có phải là % không
    const percentMatch = amount.match(/^(\d+)%$/);
    let amountToSell = '';

    if (percentMatch) {
      // Nếu amount là dạng %, lấy giá trị % (25%, 50%, 75%, 100%)
      const percentValue = parseInt(percentMatch[1]);

      // Lấy số lượng token hiện có từ UI bằng các selector khác nhau
      const tokenBalance = await this.getTokenBalance(tokenSymbol);

      if (tokenBalance > 0) {
        // Tính số lượng token cần bán dựa trên % và chỉ lấy đến 2 chữ số thập phân
        amountToSell = (tokenBalance * percentValue / 100).toFixed(2);
      } else {
        amountToSell = amount;
      }
    } else {
      // Nếu amount không phải dạng %, sử dụng giá trị trực tiếp
      amountToSell = amount;
    }

    // Nhập số lượng token cần bán vào ô input
    await this.enterAmount(amountToSell);

    // 4. Mở rộng cài đặt nâng cao
    await this.openAdvancedSettings();

    // Nhập các thông số giao dịch nâng cao
    await this.enterSlippage(slippage);
    await this.enterPriorityFee(priorityFee);

    // Thử lấy token symbol nếu chưa được cung cấp
    if (!tokenSymbol) {
      try {
        tokenSymbol = await this.getTokenSymbolFromTitle();
      } catch (e) {
        // Bỏ qua nếu không lấy được token symbol
      }
    }

    // 5. Thực hiện giao dịch với token symbol (nếu có)
    await this.clickSellButton(tokenSymbol);

    // 6. Chờ một khoảng thời gian để trang xử lý request và giao dịch hoàn tất
    console.log("Waiting for transaction completion...");
    await this.page.waitForTimeout(5000);

    // 7. Xác minh kết quả giao dịch
    await this.verifyTransactionResult();

    // Chụp màn hình sau khi bán thành công
    const screenshotDir = path.join(process.cwd(), 'screenshots');
    await this.page.screenshot({ path: path.join(screenshotDir, 'sell-token-success.png') });

    console.log(`\n======== SELL TOKEN FLOW COMPLETED ========\n`)
  }

  /**
   * Kiểm tra khu vực giao dịch (transaction section) trên trang token detail
   * @returns Promise<boolean> - true nếu khu vực giao dịch hiển thị
   */
  async verifyTransactionSection(): Promise<boolean> {
    console.log("Kiểm tra khu vực giao dịch dưới biểu đồ...");
    const transactionSection = this.page.locator(TokenDetailSelectors.transactionSection);

    const isTransactionSectionVisible = await transactionSection.isVisible().catch(() => false);
    if (isTransactionSectionVisible) {
      console.log("✅ Khu vực giao dịch được hiển thị");
    } else {
      console.log("❌ Không tìm thấy khu vực giao dịch");
    }

    return isTransactionSectionVisible;
  }

  /**
   * Kiểm tra các tab trong khu vực giao dịch
   * @returns Promise<{visibleTabCount: number, visibleTabs: string[]}> - Số lượng tab hiển thị và danh sách tên tab
   */
  async verifyTransactionTabs(): Promise<{visibleTabCount: number, visibleTabs: string[]}> {
    console.log("Kiểm tra các tab trong khu vực giao dịch...");

    // Danh sách các tab có thể có
    const expectedTabs = [
      { name: "Transactions", selector: TokenDetailSelectors.transactionTabs.transactions },
      { name: "Positions", selector: TokenDetailSelectors.transactionTabs.positions },
      { name: "Open Orders", selector: TokenDetailSelectors.transactionTabs.openOrders },
      { name: "Top traders", selector: TokenDetailSelectors.transactionTabs.topTraders },
      { name: "Holders", selector: TokenDetailSelectors.transactionTabs.holders },
      { name: "Bubblemaps", selector: TokenDetailSelectors.transactionTabs.bubblemaps },
      { name: "Trade logs", selector: TokenDetailSelectors.transactionTabs.tradeLogs }
    ];

    // Đếm số tab hiển thị
    let visibleTabCount = 0;
    let visibleTabs: string[] = [];

    for (const tab of expectedTabs) {
      const isVisible = await this.page.locator(tab.selector).isVisible().catch(() => false);
      if (isVisible) {
        visibleTabCount++;
        visibleTabs.push(tab.name);
        console.log(`✅ Tab "${tab.name}" được hiển thị`);
      }
    }

    console.log(`Tổng số: ${visibleTabCount} tab được hiển thị: ${visibleTabs.join(", ")}`);
    return { visibleTabCount, visibleTabs };
  }

  /**
   * Kiểm tra các subtab trong tab Transactions
   * @returns Promise<{visibleSubtabCount: number, visibleSubtabs: string[]}> - Số lượng subtab hiển thị và danh sách tên subtab
   */
  async verifyTransactionSubtabs(): Promise<{visibleSubtabCount: number, visibleSubtabs: string[]}> {
    console.log("Kiểm tra các subtab trong tab Transactions...");

    // Danh sách các subtab có thể có
    const transactionSubtabs = [
      { name: "All txns", selector: TokenDetailSelectors.transactionSubtabs.allTxns },
      { name: "Whale", selector: TokenDetailSelectors.transactionSubtabs.whale },
      { name: "Smart", selector: TokenDetailSelectors.transactionSubtabs.smart },
      { name: "KOL", selector: TokenDetailSelectors.transactionSubtabs.kol },
      { name: "Dev", selector: TokenDetailSelectors.transactionSubtabs.dev },
      { name: "Sniper", selector: TokenDetailSelectors.transactionSubtabs.sniper },
      { name: "Insider", selector: TokenDetailSelectors.transactionSubtabs.insider },
      { name: "Following", selector: TokenDetailSelectors.transactionSubtabs.following },
      { name: "My wallets", selector: TokenDetailSelectors.transactionSubtabs.myWallets }
    ];

    let visibleSubtabCount = 0;
    let visibleSubtabs: string[] = [];

    for (const subtab of transactionSubtabs) {
      const isVisible = await this.page.locator(subtab.selector).isVisible().catch(() => false);
      if (isVisible) {
        visibleSubtabCount++;
        visibleSubtabs.push(subtab.name);
        console.log(`✅ Subtab "${subtab.name}" được hiển thị`);
      }
    }

    console.log(`Tổng số: ${visibleSubtabCount} subtab được hiển thị: ${visibleSubtabs.join(", ")}`);
    return { visibleSubtabCount, visibleSubtabs };
  }

  /**
   * Đếm số lượng giao dịch hiển thị trong bảng
   * @returns Promise<number> - Số lượng giao dịch
   */
  async countTransactions(): Promise<number> {
    console.log("Đếm số giao dịch hiển thị...");
    const transactionRows = this.page.locator(TokenDetailSelectors.transactionRows);
    const count = await transactionRows.count();
    console.log(`Số giao dịch hiển thị: ${count}`);
    return count;
  }

  /**
   * Scroll để tải thêm giao dịch
   * @param waitTime Thời gian chờ sau khi scroll (ms)
   * @returns Promise<void>
   */
  async scrollToLoadMoreTransactions(waitTime: number = 5000): Promise<void> {
    console.log("Scroll khu vực giao dịch để tải thêm...");

    // Tìm phần tử cuối cùng và scroll đến đó
    const transactionRows = this.page.locator(TokenDetailSelectors.transactionRows);
    const count = await transactionRows.count();

    if (count > 0) {
      const lastRow = transactionRows.last();
      await lastRow.scrollIntoViewIfNeeded();

      // Scroll xuống một chút nữa để kích hoạt tải thêm
      await this.page.mouse.wheel(0, 500);

      // Đợi để tải thêm dữ liệu
      console.log(`Đợi ${waitTime/1000} giây để tải thêm giao dịch...`);
      await this.page.waitForTimeout(waitTime);
    } else {
      console.log("Không có giao dịch nào để scroll");
    }
  }

  /**
   * Phân tích loại giao dịch (Buy/Sell) từ các hàng giao dịch
   * @param maxRows Số lượng hàng tối đa cần phân tích
   * @returns Promise<string[]> - Mảng các loại giao dịch
   */
  async analyzeTransactionTypes(maxRows: number = 5): Promise<string[]> {
    console.log("Phân tích loại giao dịch...");
    const transactionRows = this.page.locator(TokenDetailSelectors.transactionRows);
    const count = await transactionRows.count();
    const transactionTypes: string[] = [];

    for (let i = 0; i < Math.min(count, maxRows); i++) {
      const row = transactionRows.nth(i);
      const rowText = await row.textContent();

      // Kiểm tra loại giao dịch
      if (rowText?.includes("Buy")) {
        transactionTypes.push("Buy");
      } else if (rowText?.includes("Sell")) {
        transactionTypes.push("Sell");
      } else {
        transactionTypes.push("Unknown");
      }
    }

    console.log(`Các loại giao dịch hiển thị: ${transactionTypes.join(", ")}`);
    return transactionTypes;
  }

  /**
   * Kiểm tra đầy đủ khu vực giao dịch, bao gồm scroll để tải thêm
   * @param screenshotDir Thư mục lưu ảnh chụp màn hình
   * @returns Promise<void>
   */
  async verifyTransactionSectionWithScroll(screenshotDir: string): Promise<void> {
    console.log("\n==== BẮT ĐẦU KIỂM TRA TRANSACTION SECTION ====\n");

    // 1. Kiểm tra khu vực giao dịch
    const isTransactionSectionVisible = await this.verifyTransactionSection();
    expect(isTransactionSectionVisible).toBeTruthy();

    // 2. Kiểm tra các tab
    const { visibleTabCount } = await this.verifyTransactionTabs();
    expect(visibleTabCount).toBeGreaterThan(0);

    // 3. Kiểm tra các subtab
    await this.verifyTransactionSubtabs();

    // 4. Đếm số giao dịch trước khi scroll
    const initialTransactionCount = await this.countTransactions();
    expect(initialTransactionCount).toBeGreaterThan(0);

    // Chụp ảnh khu vực giao dịch trước khi scroll
    await this.page.screenshot({
      path: path.join(screenshotDir, 'transactions-before-scroll.png'),
      clip: { x: 0, y: 400, width: 1280, height: 400 }
    });

    // 5. Scroll để tải thêm giao dịch
    await this.scrollToLoadMoreTransactions(5000);

    // Scroll lần thứ hai để tải thêm nữa
    await this.page.mouse.wheel(0, 500);
    await this.page.waitForTimeout(3000);

    // 6. Đếm số giao dịch sau khi scroll
    const updatedTransactionCount = await this.countTransactions();

    // Chụp ảnh khu vực giao dịch sau khi scroll
    await this.page.screenshot({
      path: path.join(screenshotDir, 'transactions-after-scroll.png'),
      clip: { x: 0, y: 400, width: 1280, height: 400 }
    });

    // Kiểm tra xem có tải thêm giao dịch không
    if (updatedTransactionCount > initialTransactionCount) {
      console.log(`✅ Đã tải thêm ${updatedTransactionCount - initialTransactionCount} giao dịch sau khi scroll`);
    } else {
      console.log("⚠️ Không tải thêm được giao dịch sau khi scroll, có thể đã hiển thị tất cả");
    }

    console.log("\n==== KẾT THÚC KIỂM TRA TRANSACTION SECTION ====\n");
  }

  /**
   * Lấy thông tin thay đổi giá trong 24h (cố định)
   * @returns Promise<string> - Giá trị thay đổi 24h (ví dụ: "+5.67%", "-2.34%")
   */
  async get24hPercentageChange(): Promise<string> {
    console.log("Lấy thông tin thay đổi giá 24h cố định...");
    try {
      // Selector cho phần hiển thị % thay đổi giá 24h cố định bên dưới giá token
      const percentageText = await this.page
        .locator('//div[contains(@class, "css-1uyggyb")] | //div[contains(@class, "css-1pof48l")]//div[contains(@class, "css-1q1iys5")]')
        .textContent();

      const result = percentageText ? percentageText.trim() : "";
      console.log(`Thay đổi giá 24h cố định: ${result}`);
      return result;
    } catch (error) {
      console.error(`Lỗi khi lấy thông tin thay đổi giá 24h: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return "";
    }
  }

  /**
   * Kiểm tra xem token đã được thêm vào danh sách theo dõi chưa
   * @returns True nếu token đã được thêm vào danh sách theo dõi, false nếu chưa
   */
  async isTokenInWatchlist(): Promise<boolean> {
    try {
      return await this.page
        .locator(TokenDetailSelectors.removeFromWatchlistButton)
        .isVisible()
        .catch(() => false);
    } catch (error) {
      console.error("Lỗi khi kiểm tra token trong danh sách theo dõi:", error);
      return false;
    }
  }

  /**
   * Chuyển đổi trạng thái theo dõi token (thêm nếu chưa có, xóa nếu đã có)
   * @returns Trạng thái mới của danh sách theo dõi (true nếu đã thêm, false nếu đã xóa)
   */
  async toggleWatchlist(): Promise<boolean> {
    const isInWatchlist = await this.isTokenInWatchlist();

    try {
      if (isInWatchlist) {
        console.log("Token đã có trong danh sách theo dõi, đang xóa...");
        await this.page.locator(TokenDetailSelectors.removeFromWatchlistButton).click();
      } else {
        console.log("Token chưa có trong danh sách theo dõi, đang thêm...");
        await this.page.locator(TokenDetailSelectors.addToWatchlistButton).click();
      }

      // Đợi hoàn tất thao tác
      await this.page.waitForTimeout(1000);

      // Kiểm tra trạng thái mới
      const newState = await this.isTokenInWatchlist();
      console.log(`Trạng thái danh sách theo dõi hiện tại: ${newState ? "đã thêm" : "chưa thêm"}`);
      return newState;
    } catch (error) {
      console.error("Lỗi khi chuyển đổi trạng thái danh sách theo dõi:", error);
      return isInWatchlist; // Trả về trạng thái ban đầu vì thao tác thất bại
    }
  }

  /**
   * Kiểm tra các bộ chọn khoảng thời gian (24H, 7D, 30D, v.v.)
   * @returns Promise<boolean> - true nếu các bộ chọn khoảng thời gian hiển thị
   */
  async areTimePeriodSelectorsVisible(): Promise<boolean> {
    console.log("Kiểm tra các bộ chọn khoảng thời gian...");
    try {
      const periods = ["5m", "1h", "6h", "3m", "1m", "5d", "1d", "24H", "7D", "30D", "90D", "1Y", "ALL"];

      for (const period of periods) {
        const isVisible = await this.page
          .locator(`//button[contains(text(), "${period}")]`)
          .isVisible()
          .catch(() => false);

        if (isVisible) {
          console.log(`Tìm thấy bộ chọn khoảng thời gian: ${period}`);
          return true;
        }
      }

      console.log("Không tìm thấy bộ chọn khoảng thời gian");
      return false;
    } catch (error) {
      console.error(`Lỗi khi kiểm tra bộ chọn khoảng thời gian: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return false;
    }
  }

  /**
   * Chọn một khoảng thời gian cụ thể
   * @param period Khoảng thời gian cần chọn (ví dụ: "5m", "1h", v.v.)
   */
  async selectTimePeriod(period: string): Promise<void> {
    console.log(`Chọn khoảng thời gian: ${period}`);

    try {
      const selector = `//button[contains(text(), "${period}")]`;
      const isVisible = await this.page.locator(selector).isVisible().catch(() => false);

      if (!isVisible) {
        console.warn(`Bộ chọn khoảng thời gian cho "${period}" không hiển thị`);
        return;
      }

      await this.page.locator(selector).click();
      console.log(`Đã chọn khoảng thời gian: ${period}`);

      // Đợi biểu đồ cập nhật
      await this.page.waitForTimeout(1000);
    } catch (error) {
      console.error(`Lỗi khi chọn khoảng thời gian "${period}":`, error instanceof Error ? error.message : "Lỗi không xác định");
    }
  }

  /**
   * Lấy tất cả các thông số trong phần header
   * @returns Object containing all header metrics
   */
  async getHeaderMetrics(): Promise<{
    marketCap: string;
    liquidity: string;
    holders: string;
    dev: string;
    insiders: string;
    top10: string;
    snipers: string;
    security: string;
    percentageChange: string;
    symbol: string;
    name: string;
    price: string;
  }> {
    console.log("Lấy các thông số header metrics...");

    const metrics: Record<string, string> = {};
    try {
      // Lấy các thông số cơ bản
      metrics.symbol = await this.getTokenSymbol();
      metrics.name = await this.getTokenName();
      metrics.price = await this.getTokenPrice();
      metrics.percentageChange = await this.get24hPercentageChange();

      // Lấy các thông số metrics dựa trên selectors
      metrics.marketCap = await this.page.locator('//div[text()="MCap"]/following-sibling::p//div[contains(@class, "css-lemfo4")]').textContent() || '';
      metrics.liquidity = await this.page.locator('//div[text()="Liquidity"]/following-sibling::p//div[contains(@class, "css-lemfo4")]').textContent() || '';
      metrics.holders = await this.page.locator('//div[text()="Holders"]/following-sibling::p//div[contains(@class, "css-lemfo4")]').textContent() || '';
      metrics.dev = await this.page.locator('//div[text()="Dev"]/following-sibling::p//div[contains(@class, "css-lemfo4")]').textContent() || '';
      metrics.insiders = await this.page.locator('//div[text()="Insiders"]/following-sibling::p//div[contains(@class, "css-lemfo4")]').textContent() || '';
      metrics.top10 = await this.page.locator('//div[text()="Top 10"]/following-sibling::p//div[contains(@class, "css-lemfo4")]').textContent() || '';
      metrics.snipers = await this.page.locator('//div[text()="Snipers"]/following-sibling::p//div[contains(@class, "css-1v0aycn")]//p').textContent() || '';
      metrics.security = await this.page.locator('//div[text()="Security"]/following-sibling::p//div[contains(@class, "css-k9f27k")]//p').textContent() || '';

      console.log("Header metrics:", JSON.stringify(metrics, null, 2));
      return metrics as any;
    } catch (error) {
      console.error("Lỗi khi lấy thông số header:", error instanceof Error ? error.message : "Lỗi không xác định");
      // Return empty values as a fallback
      return {
        marketCap: "", liquidity: "", holders: "", dev: "",
        insiders: "", top10: "", snipers: "", security: "", percentageChange: "",
        symbol: "", name: "", price: ""
      };
    }
  }



  /**
   * Kiểm tra đầy đủ thông tin header của token
   * @param screenshotDir Thư mục lưu ảnh chụp màn hình
   * @returns Promise<void>
   */
  async verifyTokenHeaderInfo(screenshotDir: string): Promise<void> {
    console.log('\n=== BẮT ĐẦU KIỂM TRA THÔNG TIN HEADER TOKEN ===\n');

    try {
      // 1. Chụp ảnh phần header để tham khảo
      await this.page.screenshot({
        path: path.join(screenshotDir, 'token-header.png'),
        clip: { x: 0, y: 0, width: 1280, height: 150 }
      });

      // 2. Kiểm tra symbol token được hiển thị
      const tokenSymbol = await this.getTokenSymbol();
      console.log(`Token Symbol: ${tokenSymbol}`);
      expect(tokenSymbol).not.toBe('');
      expect(tokenSymbol.length).toBeGreaterThan(0);

      // 3. Kiểm tra tên token được hiển thị
      const tokenName = await this.getTokenName();
      console.log(`Token Name: ${tokenName}`);
      expect(tokenName).not.toBe('');

      // 4. Kiểm tra giá token được hiển thị
      const tokenPrice = await this.getTokenPrice();
      console.log(`Token Price: ${tokenPrice}`);
      expect(tokenPrice).not.toBe('');

      // 5. Kiểm tra thanh khoản token được hiển thị
      const tokenLiquidity = await this.getTokenLiquidity();
      console.log(`Token Liquidity: ${tokenLiquidity}`);

      // 6. Kiểm tra khối lượng giao dịch token được hiển thị
      const tokenVolume = await this.getTokenVolume();
      console.log(`Token Volume: ${tokenVolume}`);

      // 7. Lấy tất cả các thông số header
      const headerMetrics = await this.getHeaderMetrics();
      console.log("Header Metrics:", headerMetrics);

      // Kiểm tra ít nhất một số thông số quan trọng có mặt
      const criticalMetrics = ['marketCap', 'liquidity', 'holders'];
      let criticalMetricsFound = 0;

      for (const metric of criticalMetrics) {
        if (headerMetrics[metric as keyof typeof headerMetrics]?.length > 0) {
          criticalMetricsFound++;
        }
      }

      console.log(`Tìm thấy ${criticalMetricsFound}/${criticalMetrics.length} thông số quan trọng`);
      expect(criticalMetricsFound).toBeGreaterThan(0);

      // 8. Kiểm tra thay đổi phần trăm 24h cố định được hiển thị
      const percentageChange = await this.get24hPercentageChange();
      console.log(`Thay đổi phần trăm 24h cố định: ${percentageChange}`);

      // 9. Kiểm tra bộ chọn khoảng thời gian cho biểu đồ
      const areTimePeriodSelectorsVisible = await this.areTimePeriodSelectorsVisible();
      console.log(`Bộ chọn khoảng thời gian cho biểu đồ hiển thị: ${areTimePeriodSelectorsVisible}`);
      console.log("Lưu ý: Phần trăm thay đổi giá chỉ hiển thị cố định cho khoảng thời gian 24h, không thay đổi theo các khoảng thời gian khác");

      // 10. Chụp ảnh toàn bộ trang để tham khảo
      await this.page.screenshot({
        path: path.join(screenshotDir, 'token-detail-full.png'),
        fullPage: true
      });

      console.log('\n=== KIỂM TRA THÔNG TIN HEADER TOKEN HOÀN TẤT THÀNH CÔNG ===\n');
    } catch (error) {
      console.error('\n!!! KIỂM TRA THÔNG TIN HEADER TOKEN THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');

      // Chụp ảnh lỗi
      try {
        const errorScreenshotPath = path.join(screenshotDir, `token-header-error-${Date.now()}.png`);
        await this.page.screenshot({
          path: errorScreenshotPath,
          fullPage: true
        });
        console.error(`Đã lưu ảnh lỗi: ${errorScreenshotPath}`);
      } catch (screenshotError) {
        console.error("Không thể chụp ảnh lỗi");
      }

      throw error;
    }
  }
}
