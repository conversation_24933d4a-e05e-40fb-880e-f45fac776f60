import{a as d}from"./chunk-7X4NV6OJ.js";import{f as l,h as s,n as a}from"./chunk-3KENBVE7.js";s();a();var t=l(d()),i=n=>{let e=document.createElement("textarea");e.value=n,e.setAttribute("readonly",""),e.style.position="absolute",e.style.left="-9999px",document.body.appendChild(e);let o=document.getSelection().rangeCount>0?document.getSelection().getRangeAt(0):!1;e.select();let c=document.execCommand("copy");return document.body.removeChild(e),o&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(o)),c},u=n=>{let[e,o]=(0,t.useState)(!1),c=(0,t.useCallback)(()=>{e||o(i(n))},[n,e]);return(0,t.useEffect)(()=>()=>o(!1),[n]),[e,c,o]};export{i as a,u as b};
//# sourceMappingURL=chunk-EGXLQXDH.js.map
