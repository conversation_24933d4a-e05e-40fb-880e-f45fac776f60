import{p as fm}from"./chunk-F3RUX6TF.js";import{t as Sm}from"./chunk-HRJWTAGT.js";import{e as Oa}from"./chunk-LURFXJDV.js";import{p as mm,u as dm}from"./chunk-V5T43K7V.js";import{a as gm,d as ym}from"./chunk-QALJXKGR.js";import{a as om}from"./chunk-MHOQBMVI.js";import{D as ys,E as _l,F as Ql,G as Ol,H as Ml,m as Nl,s as Bl}from"./chunk-7ZN4F6J4.js";import{Aa as Zl,B as $l,Bb as nr,Ca as Ss,Fa as em,Ga as ho,Ha as tm,Ia as rm,L as zl,Qb as cm,Sa as Fe,Sb as Dr,T as jl,Ta as tt,Tb as Nr,Ub as Le,Vb as um,Wa as sm,Wb as Br,Y as Ma,Z as Wl,Za as im,bc as lm,e as Yh,f as ql,fa as Yl,fc as q,g as ft,ga as Xl,gc as Ct,hc as rt,i as Gl,ia as Jl,j as Vl,mb as ws,nb as am,nc as Ao,pc as sr,qb as gt,rb as Me,rc as qa,tb as pm,vb as fn,xc as hs,y as Kl,z as Hl}from"./chunk-OUYKWOVO.js";import{d as nm,h as dn}from"./chunk-OYGO47TI.js";import{o as Dl,u as Te}from"./chunk-SLQBAOEK.js";import{$a as xl,$d as te,C as lo,Cc as dt,Dd as fs,G as ol,Ga as us,Kb as ls,Kd as Ll,P as ln,Pa as F,Pb as ms,Q as U,Qa as xt,Ra as W,Rb as Qa,Sa as or,Sb as mn,T as se,Td as Ul,Va as ze,Wa as Il,Xa as oe,Ya as kr,ac as Oe,ba as Tl,bc as Cl,ca as Pr,cb as Et,cc as wo,ec as Rl,ge as gs,kb as je,nb as yo,nc as Ge,ob as So,pd as kl,pe as vt,rd as ee,ta as Fl,te as Be,ub as El,wc as $,wd as ds,yb as vl,zc as Pl}from"./chunk-MZZEJ42N.js";import{K as ns,p as uo,v as rl}from"./chunk-E3NPIRHS.js";import{a as D,m as rr,n as Re}from"./chunk-56SJOU6P.js";import{$ as wl,U as fo,V as yl,Y as Sl,Z as go,_ as Wh,a as K,aa as tr,b as N,ba as mt,da as _a,ea as hl,fa as Z,ga as Al,ha as bl,ia as Ce,ja as ps,ka as cs,o as dl,p as fl,q as is,t as gl,v as as,w as It,x as L}from"./chunk-ALUTR72U.js";import{Ca as ul,Ea as ll,G as le,I as Ae,J as sl,K as lt,La as ml,Ya as ae,ba as Qe,da as il,ea as al,fa as ss,ga as pl,ia as G,ka as B,la as ie,ma as er,na as cl,sa as mo,t as os,ta as Q,v as Zt,z as nl}from"./chunk-L3A2KHJO.js";import{a as l}from"./chunk-4P36KWOF.js";import{a as R}from"./chunk-7X4NV6OJ.js";import{c as $e,f as C,h as i,l as jh,m as Buffer,n as a}from"./chunk-3KENBVE7.js";var bm=$e((Ik,Am)=>{i();a();(function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t={rotl:function(r,o){return r<<o|r>>>32-o},rotr:function(r,o){return r<<32-o|r>>>o},endian:function(r){if(r.constructor==Number)return t.rotl(r,8)&16711935|t.rotl(r,24)&**********;for(var o=0;o<r.length;o++)r[o]=t.endian(r[o]);return r},randomBytes:function(r){for(var o=[];r>0;r--)o.push(Math.floor(Math.random()*256));return o},bytesToWords:function(r){for(var o=[],n=0,s=0;n<r.length;n++,s+=8)o[s>>>5]|=r[n]<<24-s%32;return o},wordsToBytes:function(r){for(var o=[],n=0;n<r.length*32;n+=8)o.push(r[n>>>5]>>>24-n%32&255);return o},bytesToHex:function(r){for(var o=[],n=0;n<r.length;n++)o.push((r[n]>>>4).toString(16)),o.push((r[n]&15).toString(16));return o.join("")},hexToBytes:function(r){for(var o=[],n=0;n<r.length;n+=2)o.push(parseInt(r.substr(n,2),16));return o},bytesToBase64:function(r){for(var o=[],n=0;n<r.length;n+=3)for(var s=r[n]<<16|r[n+1]<<8|r[n+2],p=0;p<4;p++)n*8+p*6<=r.length*8?o.push(e.charAt(s>>>6*(3-p)&63)):o.push("=");return o.join("")},base64ToBytes:function(r){r=r.replace(/[^A-Z0-9+\/]/ig,"");for(var o=[],n=0,s=0;n<r.length;s=++n%4)s!=0&&o.push((e.indexOf(r.charAt(n-1))&Math.pow(2,-2*s+8)-1)<<s*2|e.indexOf(r.charAt(n))>>>6-s*2);return o}};Am.exports=t})()});var Ha=$e((vk,Tm)=>{i();a();var Ka={utf8:{stringToBytes:function(e){return Ka.bin.stringToBytes(unescape(encodeURIComponent(e)))},bytesToString:function(e){return decodeURIComponent(escape(Ka.bin.bytesToString(e)))}},bin:{stringToBytes:function(e){for(var t=[],r=0;r<e.length;r++)t.push(e.charCodeAt(r)&255);return t},bytesToString:function(e){for(var t=[],r=0;r<e.length;r++)t.push(String.fromCharCode(e[r]));return t.join("")}}};Tm.exports=Ka});var Im=$e((Pk,Fm)=>{i();a();(function(){var e=bm(),t=Ha().utf8,r=Ha().bin,o=function(s){s.constructor==String?s=t.stringToBytes(s):typeof Buffer<"u"&&typeof Buffer.isBuffer=="function"&&Buffer.isBuffer(s)?s=Array.prototype.slice.call(s,0):Array.isArray(s)||(s=s.toString());var p=e.bytesToWords(s),c=s.length*8,u=[],m=1732584193,f=-271733879,g=-1732584194,S=271733878,w=-1009589776;p[c>>5]|=128<<24-c%32,p[(c+64>>>9<<4)+15]=c;for(var y=0;y<p.length;y+=16){for(var h=m,A=f,T=g,I=S,v=w,x=0;x<80;x++){if(x<16)u[x]=p[y+x];else{var E=u[x-3]^u[x-8]^u[x-14]^u[x-16];u[x]=E<<1|E>>>31}var P=(m<<5|m>>>27)+w+(u[x]>>>0)+(x<20?(f&g|~f&S)+1518500249:x<40?(f^g^S)+**********:x<60?(f&g|f&S|g&S)-**********:(f^g^S)-899497514);w=S,S=g,g=f<<30|f>>>2,f=m,m=P}m+=h,f+=A,g+=T,S+=I,w+=v}return[m,f,g,S,w]},n=function(s,p){var c=e.wordsToBytes(o(s));return p&&p.asBytes?c:p&&p.asString?r.bytesToString(c):e.bytesToHex(c)};n._blocksize=16,n._digestsize=20,Fm.exports=n})()});var vm=$e((_k,eA)=>{eA.exports={name:"@phantom-labs/blocklist",version:"0.13.0",main:"index.js",types:"index.d.ts",repository:"**************:phantom-labs/blocklist.git",author:"Jordan Leigh <<EMAIL>>",license:"MIT",scripts:{build:"node build.js"},dependencies:{"js-yaml":"^4.1.0",sha3:"^2.1.4"}}});var Cm=$e((Qk,tA)=>{tA.exports={blocklist:["phantomweb.app","aurory.app","solvision.io","staratlas.art","starsatlas.com","sollet.cc","raydlum.io","aurorynft.com","sneks.gg","solletweb.io","i-sollet.com","fancyfrenchienft.art","solanawebwallet.online","phahtom.com","server-syncwallet.com","staratias.app","raydium.network","grapesnetwork.me","staratias.art","soistarter.org","audius-nft.top","aurory.me","degenapes.app","phantom-app.online","phantomwallet.net","dapps-node.com","phantom-app.link","solanaoutage.com","walletconnectdapps.net","staratlas.cx","web-phantom.app","0120tt.com"],whitelist:[],fuzzylist:[],contentHash:"830ea1c9a833213eabc17990adf053081f503b1203ca9a2c711db7199e657293"}});var Rm=$e(ja=>{i();a();var{version:rA}=vm(),oA=Cm(),nA=()=>rA,sA=()=>oA;ja.getVersion=nA;ja.getBlocklist=sA});var km=$e(To=>{"use strict";i();a();To.__esModule=!0;To.distance=To.closest=void 0;var Rt=new Uint32Array(65536),iA=function(e,t){for(var r=e.length,o=t.length,n=1<<r-1,s=-1,p=0,c=r,u=r;u--;)Rt[e.charCodeAt(u)]|=1<<u;for(u=0;u<o;u++){var m=Rt[t.charCodeAt(u)],f=m|p;m|=(m&s)+s^s,p|=~(m|s),s&=m,p&n&&c++,s&n&&c--,p=p<<1|1,s=s<<1|~(f|p),p&=f}for(u=r;u--;)Rt[e.charCodeAt(u)]=0;return c},aA=function(e,t){for(var r=t.length,o=e.length,n=[],s=[],p=Math.ceil(r/32),c=Math.ceil(o/32),u=0;u<p;u++)s[u]=-1,n[u]=0;for(var m=0;m<c-1;m++){for(var f=0,g=-1,S=m*32,w=Math.min(32,o)+S,y=S;y<w;y++)Rt[e.charCodeAt(y)]|=1<<y;for(var u=0;u<r;u++){var h=Rt[t.charCodeAt(u)],A=s[u/32|0]>>>u&1,T=n[u/32|0]>>>u&1,I=h|f,v=((h|T)&g)+g^g|h|T,x=f|~(v|g),E=g&v;x>>>31^A&&(s[u/32|0]^=1<<u),E>>>31^T&&(n[u/32|0]^=1<<u),x=x<<1|A,E=E<<1|T,g=E|~(I|x),f=x&I}for(var y=S;y<w;y++)Rt[e.charCodeAt(y)]=0}for(var P=0,k=-1,H=m*32,j=Math.min(32,o-H)+H,y=H;y<j;y++)Rt[e.charCodeAt(y)]|=1<<y;for(var M=o,u=0;u<r;u++){var h=Rt[t.charCodeAt(u)],A=s[u/32|0]>>>u&1,T=n[u/32|0]>>>u&1,I=h|P,v=((h|T)&k)+k^k|h|T,x=P|~(v|k),E=k&v;M+=x>>>o-1&1,M-=E>>>o-1&1,x>>>31^A&&(s[u/32|0]^=1<<u),E>>>31^T&&(n[u/32|0]^=1<<u),x=x<<1|A,E=E<<1|T,k=E|~(I|x),P=x&I}for(var y=H;y<j;y++)Rt[e.charCodeAt(y)]=0;return M},Pm=function(e,t){if(e.length<t.length){var r=t;t=e,e=r}return t.length===0?e.length:e.length<=32?iA(e,t):aA(e,t)};To.distance=Pm;var pA=function(e,t){for(var r=1/0,o=0,n=0;n<t.length;n++){var s=Pm(e,t[n]);s<r&&(r=s,o=n)}return t[o]};To.closest=pA});var Dm=$e((Wa,gn)=>{i();a();(function(){"use strict";var e;try{e=typeof Intl<"u"&&typeof Intl.Collator<"u"?Intl.Collator("generic",{sensitivity:"base"}):null}catch{console.log("Collator could not be initialized and wouldn't be used")}var t=km(),r=[],o=[],n={get:function(s,p,c){var u=c&&e&&c.useCollator;if(u){var m=s.length,f=p.length;if(m===0)return f;if(f===0)return m;var g,S,w,y,h;for(w=0;w<f;++w)r[w]=w,o[w]=p.charCodeAt(w);r[f]=f;var A;for(w=0;w<m;++w){for(S=w+1,y=0;y<f;++y)g=S,A=e.compare(s.charAt(w),String.fromCharCode(o[y]))===0,S=r[y]+(A?0:1),h=g+1,S>h&&(S=h),h=r[y+1]+1,S>h&&(S=h),r[y]=g;r[y]=S}return S}return t.distance(s,p)}};typeof define<"u"&&define!==null&&define.amd?define(function(){return n}):typeof gn<"u"&&gn!==null&&typeof Wa<"u"&&gn.exports===Wa?gn.exports=n:(typeof self<"u"&&typeof self.postMessage=="function"&&typeof self.importScripts=="function"||typeof self<"u"&&self!==null)&&(self.Levenshtein=n)})()});var sf=$e((bn,Eo)=>{i();a();var kA=200,Mp="__lodash_hash_undefined__",_s=1,Ud=2,_d=9007199254740991,Ps="[object Arguments]",Np="[object Array]",DA="[object AsyncFunction]",Qd="[object Boolean]",Od="[object Date]",Md="[object Error]",qd="[object Function]",NA="[object GeneratorFunction]",ks="[object Map]",Gd="[object Number]",BA="[object Null]",xo="[object Object]",xd="[object Promise]",LA="[object Proxy]",Vd="[object RegExp]",Ds="[object Set]",Kd="[object String]",UA="[object Symbol]",_A="[object Undefined]",Bp="[object WeakMap]",Hd="[object ArrayBuffer]",Ns="[object DataView]",QA="[object Float32Array]",OA="[object Float64Array]",MA="[object Int8Array]",qA="[object Int16Array]",GA="[object Int32Array]",VA="[object Uint8Array]",KA="[object Uint8ClampedArray]",HA="[object Uint16Array]",$A="[object Uint32Array]",zA=/[\\^$.*+?()[\]{}|]/g,jA=/^\[object .+?Constructor\]$/,WA=/^(?:0|[1-9]\d*)$/,me={};me[QA]=me[OA]=me[MA]=me[qA]=me[GA]=me[VA]=me[KA]=me[HA]=me[$A]=!0;me[Ps]=me[Np]=me[Hd]=me[Qd]=me[Ns]=me[Od]=me[Md]=me[qd]=me[ks]=me[Gd]=me[xo]=me[Vd]=me[Ds]=me[Kd]=me[Bp]=!1;var $d=typeof self=="object"&&self&&self.Object===Object&&self,YA=typeof self=="object"&&self&&self.Object===Object&&self,kt=$d||YA||Function("return this")(),zd=typeof bn=="object"&&bn&&!bn.nodeType&&bn,Ed=zd&&typeof Eo=="object"&&Eo&&!Eo.nodeType&&Eo,jd=Ed&&Ed.exports===zd,Pp=jd&&$d.process,vd=function(){try{return Pp&&Pp.binding&&Pp.binding("util")}catch{}}(),Cd=vd&&vd.isTypedArray;function XA(e,t){for(var r=-1,o=e==null?0:e.length,n=0,s=[];++r<o;){var p=e[r];t(p,r,e)&&(s[n++]=p)}return s}function JA(e,t){for(var r=-1,o=t.length,n=e.length;++r<o;)e[n+r]=t[r];return e}function ZA(e,t){for(var r=-1,o=e==null?0:e.length;++r<o;)if(t(e[r],r,e))return!0;return!1}function eb(e,t){for(var r=-1,o=Array(e);++r<e;)o[r]=t(r);return o}function tb(e){return function(t){return e(t)}}function rb(e,t){return e.has(t)}function ob(e,t){return e?.[t]}function nb(e){var t=-1,r=Array(e.size);return e.forEach(function(o,n){r[++t]=[n,o]}),r}function sb(e,t){return function(r){return e(t(r))}}function ib(e){var t=-1,r=Array(e.size);return e.forEach(function(o){r[++t]=o}),r}var ab=Array.prototype,pb=Function.prototype,Qs=Object.prototype,kp=kt["__core-js_shared__"],Wd=pb.toString,wt=Qs.hasOwnProperty,Rd=function(){var e=/[^.]+$/.exec(kp&&kp.keys&&kp.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Yd=Qs.toString,cb=RegExp("^"+Wd.call(wt).replace(zA,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),Pd=jd?kt.Buffer:void 0,Bs=kt.Symbol,kd=kt.Uint8Array,Xd=Qs.propertyIsEnumerable,ub=ab.splice,Lr=Bs?Bs.toStringTag:void 0,Dd=Object.getOwnPropertySymbols,lb=Pd?Pd.isBuffer:void 0,mb=sb(Object.keys,Object),Lp=vo(kt,"DataView"),Tn=vo(kt,"Map"),Up=vo(kt,"Promise"),_p=vo(kt,"Set"),Qp=vo(kt,"WeakMap"),Fn=vo(Object,"create"),db=Qr(Lp),fb=Qr(Tn),gb=Qr(Up),yb=Qr(_p),Sb=Qr(Qp),Nd=Bs?Bs.prototype:void 0,Dp=Nd?Nd.valueOf:void 0;function Ur(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function wb(){this.__data__=Fn?Fn(null):{},this.size=0}function hb(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function Ab(e){var t=this.__data__;if(Fn){var r=t[e];return r===Mp?void 0:r}return wt.call(t,e)?t[e]:void 0}function bb(e){var t=this.__data__;return Fn?t[e]!==void 0:wt.call(t,e)}function Tb(e,t){var r=this.__data__;return this.size+=this.has(e)?0:1,r[e]=Fn&&t===void 0?Mp:t,this}Ur.prototype.clear=wb;Ur.prototype.delete=hb;Ur.prototype.get=Ab;Ur.prototype.has=bb;Ur.prototype.set=Tb;function Dt(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function Fb(){this.__data__=[],this.size=0}function Ib(e){var t=this.__data__,r=Os(t,e);if(r<0)return!1;var o=t.length-1;return r==o?t.pop():ub.call(t,r,1),--this.size,!0}function xb(e){var t=this.__data__,r=Os(t,e);return r<0?void 0:t[r][1]}function Eb(e){return Os(this.__data__,e)>-1}function vb(e,t){var r=this.__data__,o=Os(r,e);return o<0?(++this.size,r.push([e,t])):r[o][1]=t,this}Dt.prototype.clear=Fb;Dt.prototype.delete=Ib;Dt.prototype.get=xb;Dt.prototype.has=Eb;Dt.prototype.set=vb;function _r(e){var t=-1,r=e==null?0:e.length;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function Cb(){this.size=0,this.__data__={hash:new Ur,map:new(Tn||Dt),string:new Ur}}function Rb(e){var t=Ms(this,e).delete(e);return this.size-=t?1:0,t}function Pb(e){return Ms(this,e).get(e)}function kb(e){return Ms(this,e).has(e)}function Db(e,t){var r=Ms(this,e),o=r.size;return r.set(e,t),this.size+=r.size==o?0:1,this}_r.prototype.clear=Cb;_r.prototype.delete=Rb;_r.prototype.get=Pb;_r.prototype.has=kb;_r.prototype.set=Db;function Ls(e){var t=-1,r=e==null?0:e.length;for(this.__data__=new _r;++t<r;)this.add(e[t])}function Nb(e){return this.__data__.set(e,Mp),this}function Bb(e){return this.__data__.has(e)}Ls.prototype.add=Ls.prototype.push=Nb;Ls.prototype.has=Bb;function pr(e){var t=this.__data__=new Dt(e);this.size=t.size}function Lb(){this.__data__=new Dt,this.size=0}function Ub(e){var t=this.__data__,r=t.delete(e);return this.size=t.size,r}function _b(e){return this.__data__.get(e)}function Qb(e){return this.__data__.has(e)}function Ob(e,t){var r=this.__data__;if(r instanceof Dt){var o=r.__data__;if(!Tn||o.length<kA-1)return o.push([e,t]),this.size=++r.size,this;r=this.__data__=new _r(o)}return r.set(e,t),this.size=r.size,this}pr.prototype.clear=Lb;pr.prototype.delete=Ub;pr.prototype.get=_b;pr.prototype.has=Qb;pr.prototype.set=Ob;function Mb(e,t){var r=Us(e),o=!r&&tT(e),n=!r&&!o&&Op(e),s=!r&&!o&&!n&&nf(e),p=r||o||n||s,c=p?eb(e.length,String):[],u=c.length;for(var m in e)(t||wt.call(e,m))&&!(p&&(m=="length"||n&&(m=="offset"||m=="parent")||s&&(m=="buffer"||m=="byteLength"||m=="byteOffset")||Yb(m,u)))&&c.push(m);return c}function Os(e,t){for(var r=e.length;r--;)if(ef(e[r][0],t))return r;return-1}function qb(e,t,r){var o=t(e);return Us(e)?o:JA(o,r(e))}function xn(e){return e==null?e===void 0?_A:BA:Lr&&Lr in Object(e)?jb(e):eT(e)}function Bd(e){return In(e)&&xn(e)==Ps}function Jd(e,t,r,o,n){return e===t?!0:e==null||t==null||!In(e)&&!In(t)?e!==e&&t!==t:Gb(e,t,r,o,Jd,n)}function Gb(e,t,r,o,n,s){var p=Us(e),c=Us(t),u=p?Np:ar(e),m=c?Np:ar(t);u=u==Ps?xo:u,m=m==Ps?xo:m;var f=u==xo,g=m==xo,S=u==m;if(S&&Op(e)){if(!Op(t))return!1;p=!0,f=!1}if(S&&!f)return s||(s=new pr),p||nf(e)?Zd(e,t,r,o,n,s):$b(e,t,u,r,o,n,s);if(!(r&_s)){var w=f&&wt.call(e,"__wrapped__"),y=g&&wt.call(t,"__wrapped__");if(w||y){var h=w?e.value():e,A=y?t.value():t;return s||(s=new pr),n(h,A,r,o,s)}}return S?(s||(s=new pr),zb(e,t,r,o,n,s)):!1}function Vb(e){if(!of(e)||Jb(e))return!1;var t=tf(e)?cb:jA;return t.test(Qr(e))}function Kb(e){return In(e)&&rf(e.length)&&!!me[xn(e)]}function Hb(e){if(!Zb(e))return mb(e);var t=[];for(var r in Object(e))wt.call(e,r)&&r!="constructor"&&t.push(r);return t}function Zd(e,t,r,o,n,s){var p=r&_s,c=e.length,u=t.length;if(c!=u&&!(p&&u>c))return!1;var m=s.get(e);if(m&&s.get(t))return m==t;var f=-1,g=!0,S=r&Ud?new Ls:void 0;for(s.set(e,t),s.set(t,e);++f<c;){var w=e[f],y=t[f];if(o)var h=p?o(y,w,f,t,e,s):o(w,y,f,e,t,s);if(h!==void 0){if(h)continue;g=!1;break}if(S){if(!ZA(t,function(A,T){if(!rb(S,T)&&(w===A||n(w,A,r,o,s)))return S.push(T)})){g=!1;break}}else if(!(w===y||n(w,y,r,o,s))){g=!1;break}}return s.delete(e),s.delete(t),g}function $b(e,t,r,o,n,s,p){switch(r){case Ns:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Hd:return!(e.byteLength!=t.byteLength||!s(new kd(e),new kd(t)));case Qd:case Od:case Gd:return ef(+e,+t);case Md:return e.name==t.name&&e.message==t.message;case Vd:case Kd:return e==t+"";case ks:var c=nb;case Ds:var u=o&_s;if(c||(c=ib),e.size!=t.size&&!u)return!1;var m=p.get(e);if(m)return m==t;o|=Ud,p.set(e,t);var f=Zd(c(e),c(t),o,n,s,p);return p.delete(e),f;case UA:if(Dp)return Dp.call(e)==Dp.call(t)}return!1}function zb(e,t,r,o,n,s){var p=r&_s,c=Ld(e),u=c.length,m=Ld(t),f=m.length;if(u!=f&&!p)return!1;for(var g=u;g--;){var S=c[g];if(!(p?S in t:wt.call(t,S)))return!1}var w=s.get(e);if(w&&s.get(t))return w==t;var y=!0;s.set(e,t),s.set(t,e);for(var h=p;++g<u;){S=c[g];var A=e[S],T=t[S];if(o)var I=p?o(T,A,S,t,e,s):o(A,T,S,e,t,s);if(!(I===void 0?A===T||n(A,T,r,o,s):I)){y=!1;break}h||(h=S=="constructor")}if(y&&!h){var v=e.constructor,x=t.constructor;v!=x&&"constructor"in e&&"constructor"in t&&!(typeof v=="function"&&v instanceof v&&typeof x=="function"&&x instanceof x)&&(y=!1)}return s.delete(e),s.delete(t),y}function Ld(e){return qb(e,nT,Wb)}function Ms(e,t){var r=e.__data__;return Xb(t)?r[typeof t=="string"?"string":"hash"]:r.map}function vo(e,t){var r=ob(e,t);return Vb(r)?r:void 0}function jb(e){var t=wt.call(e,Lr),r=e[Lr];try{e[Lr]=void 0;var o=!0}catch{}var n=Yd.call(e);return o&&(t?e[Lr]=r:delete e[Lr]),n}var Wb=Dd?function(e){return e==null?[]:(e=Object(e),XA(Dd(e),function(t){return Xd.call(e,t)}))}:sT,ar=xn;(Lp&&ar(new Lp(new ArrayBuffer(1)))!=Ns||Tn&&ar(new Tn)!=ks||Up&&ar(Up.resolve())!=xd||_p&&ar(new _p)!=Ds||Qp&&ar(new Qp)!=Bp)&&(ar=function(e){var t=xn(e),r=t==xo?e.constructor:void 0,o=r?Qr(r):"";if(o)switch(o){case db:return Ns;case fb:return ks;case gb:return xd;case yb:return Ds;case Sb:return Bp}return t});function Yb(e,t){return t=t??_d,!!t&&(typeof e=="number"||WA.test(e))&&e>-1&&e%1==0&&e<t}function Xb(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function Jb(e){return!!Rd&&Rd in e}function Zb(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||Qs;return e===r}function eT(e){return Yd.call(e)}function Qr(e){if(e!=null){try{return Wd.call(e)}catch{}try{return e+""}catch{}}return""}function ef(e,t){return e===t||e!==e&&t!==t}var tT=Bd(function(){return arguments}())?Bd:function(e){return In(e)&&wt.call(e,"callee")&&!Xd.call(e,"callee")},Us=Array.isArray;function rT(e){return e!=null&&rf(e.length)&&!tf(e)}var Op=lb||iT;function oT(e,t){return Jd(e,t)}function tf(e){if(!of(e))return!1;var t=xn(e);return t==qd||t==NA||t==DA||t==LA}function rf(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=_d}function of(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function In(e){return e!=null&&typeof e=="object"}var nf=Cd?tb(Cd):Kb;function nT(e){return rT(e)?Mb(e):Hb(e)}function sT(){return[]}function iT(){return!1}Eo.exports=oT});var Mf=$e((En,Ro)=>{i();a();var yf=200,aT="Expected a function",Yp="__lodash_hash_undefined__",$s=1,ko=2,Xp=1/0,Sf=9007199254740991,qs="[object Arguments]",Vp="[object Array]",wf="[object Boolean]",hf="[object Date]",Af="[object Error]",bf="[object Function]",pT="[object GeneratorFunction]",Gs="[object Map]",Tf="[object Number]",Co="[object Object]",af="[object Promise]",Ff="[object RegExp]",Vs="[object Set]",If="[object String]",xf="[object Symbol]",Kp="[object WeakMap]",Ef="[object ArrayBuffer]",Ks="[object DataView]",cT="[object Float32Array]",uT="[object Float64Array]",lT="[object Int8Array]",mT="[object Int16Array]",dT="[object Int32Array]",fT="[object Uint8Array]",gT="[object Uint8ClampedArray]",yT="[object Uint16Array]",ST="[object Uint32Array]",wT=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,hT=/^\w*$/,AT=/^\./,bT=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,TT=/[\\^$.*+?()[\]{}|]/g,FT=/\\(\\)?/g,IT=/^\[object .+?Constructor\]$/,xT=/^(?:0|[1-9]\d*)$/,de={};de[cT]=de[uT]=de[lT]=de[mT]=de[dT]=de[fT]=de[gT]=de[yT]=de[ST]=!0;de[qs]=de[Vp]=de[Ef]=de[wf]=de[Ks]=de[hf]=de[Af]=de[bf]=de[Gs]=de[Tf]=de[Co]=de[Ff]=de[Vs]=de[If]=de[Kp]=!1;var vf=typeof self=="object"&&self&&self.Object===Object&&self,ET=typeof self=="object"&&self&&self.Object===Object&&self,ur=vf||ET||Function("return this")(),Cf=typeof En=="object"&&En&&!En.nodeType&&En,pf=Cf&&typeof Ro=="object"&&Ro&&!Ro.nodeType&&Ro,vT=pf&&pf.exports===Cf,cf=vT&&vf.process,uf=function(){try{return cf&&cf.binding("util")}catch{}}(),lf=uf&&uf.isTypedArray;function CT(e,t){var r=e?e.length:0;return!!r&&DT(e,t,0)>-1}function RT(e,t,r){for(var o=-1,n=e?e.length:0;++o<n;)if(r(t,e[o]))return!0;return!1}function PT(e,t){for(var r=-1,o=e?e.length:0;++r<o;)if(t(e[r],r,e))return!0;return!1}function kT(e,t,r,o){for(var n=e.length,s=r+(o?1:-1);o?s--:++s<n;)if(t(e[s],s,e))return s;return-1}function DT(e,t,r){if(t!==t)return kT(e,NT,r);for(var o=r-1,n=e.length;++o<n;)if(e[o]===t)return o;return-1}function NT(e){return e!==e}function BT(e){return function(t){return t?.[e]}}function LT(e,t){for(var r=-1,o=Array(e);++r<e;)o[r]=t(r);return o}function UT(e){return function(t){return e(t)}}function _T(e,t){return e.has(t)}function QT(e,t){return e?.[t]}function Hp(e){var t=!1;if(e!=null&&typeof e.toString!="function")try{t=!!(e+"")}catch{}return t}function OT(e){var t=-1,r=Array(e.size);return e.forEach(function(o,n){r[++t]=[n,o]}),r}function MT(e,t){return function(r){return e(t(r))}}function Jp(e){var t=-1,r=Array(e.size);return e.forEach(function(o){r[++t]=o}),r}var qT=Array.prototype,GT=Function.prototype,zs=Object.prototype,qp=ur["__core-js_shared__"],mf=function(){var e=/[^.]+$/.exec(qp&&qp.keys&&qp.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Rf=GT.toString,Bt=zs.hasOwnProperty,Do=zs.toString,VT=RegExp("^"+Rf.call(Bt).replace(TT,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),df=ur.Symbol,ff=ur.Uint8Array,KT=zs.propertyIsEnumerable,HT=qT.splice,$T=MT(Object.keys,Object),$p=No(ur,"DataView"),vn=No(ur,"Map"),zp=No(ur,"Promise"),Po=No(ur,"Set"),jp=No(ur,"WeakMap"),Cn=No(Object,"create"),zT=qr($p),jT=qr(vn),WT=qr(zp),YT=qr(Po),XT=qr(jp),Hs=df?df.prototype:void 0,Gp=Hs?Hs.valueOf:void 0,gf=Hs?Hs.toString:void 0;function Or(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function JT(){this.__data__=Cn?Cn(null):{}}function ZT(e){return this.has(e)&&delete this.__data__[e]}function eF(e){var t=this.__data__;if(Cn){var r=t[e];return r===Yp?void 0:r}return Bt.call(t,e)?t[e]:void 0}function tF(e){var t=this.__data__;return Cn?t[e]!==void 0:Bt.call(t,e)}function rF(e,t){var r=this.__data__;return r[e]=Cn&&t===void 0?Yp:t,this}Or.prototype.clear=JT;Or.prototype.delete=ZT;Or.prototype.get=eF;Or.prototype.has=tF;Or.prototype.set=rF;function Lt(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function oF(){this.__data__=[]}function nF(e){var t=this.__data__,r=js(t,e);if(r<0)return!1;var o=t.length-1;return r==o?t.pop():HT.call(t,r,1),!0}function sF(e){var t=this.__data__,r=js(t,e);return r<0?void 0:t[r][1]}function iF(e){return js(this.__data__,e)>-1}function aF(e,t){var r=this.__data__,o=js(r,e);return o<0?r.push([e,t]):r[o][1]=t,this}Lt.prototype.clear=oF;Lt.prototype.delete=nF;Lt.prototype.get=sF;Lt.prototype.has=iF;Lt.prototype.set=aF;function Ut(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function pF(){this.__data__={hash:new Or,map:new(vn||Lt),string:new Or}}function cF(e){return Ws(this,e).delete(e)}function uF(e){return Ws(this,e).get(e)}function lF(e){return Ws(this,e).has(e)}function mF(e,t){return Ws(this,e).set(e,t),this}Ut.prototype.clear=pF;Ut.prototype.delete=cF;Ut.prototype.get=uF;Ut.prototype.has=lF;Ut.prototype.set=mF;function Rn(e){var t=-1,r=e?e.length:0;for(this.__data__=new Ut;++t<r;)this.add(e[t])}function dF(e){return this.__data__.set(e,Yp),this}function fF(e){return this.__data__.has(e)}Rn.prototype.add=Rn.prototype.push=dF;Rn.prototype.has=fF;function Nt(e){this.__data__=new Lt(e)}function gF(){this.__data__=new Lt}function yF(e){return this.__data__.delete(e)}function SF(e){return this.__data__.get(e)}function wF(e){return this.__data__.has(e)}function hF(e,t){var r=this.__data__;if(r instanceof Lt){var o=r.__data__;if(!vn||o.length<yf-1)return o.push([e,t]),this;r=this.__data__=new Ut(o)}return r.set(e,t),this}Nt.prototype.clear=gF;Nt.prototype.delete=yF;Nt.prototype.get=SF;Nt.prototype.has=wF;Nt.prototype.set=hF;function AF(e,t){var r=Mr(e)||_f(e)?LT(e.length,String):[],o=r.length,n=!!o;for(var s in e)(t||Bt.call(e,s))&&!(n&&(s=="length"||Nf(s,o)))&&r.push(s);return r}function js(e,t){for(var r=e.length;r--;)if(Uf(e[r][0],t))return r;return-1}function Pf(e,t){t=Ys(t,e)?[t]:kf(t);for(var r=0,o=t.length;e!=null&&r<o;)e=e[Xs(t[r++])];return r&&r==o?e:void 0}function bF(e){return Do.call(e)}function TF(e,t){return e!=null&&t in Object(e)}function Zp(e,t,r,o,n){return e===t?!0:e==null||t==null||!Js(e)&&!Zs(t)?e!==e&&t!==t:FF(e,t,Zp,r,o,n)}function FF(e,t,r,o,n,s){var p=Mr(e),c=Mr(t),u=Vp,m=Vp;p||(u=cr(e),u=u==qs?Co:u),c||(m=cr(t),m=m==qs?Co:m);var f=u==Co&&!Hp(e),g=m==Co&&!Hp(t),S=u==m;if(S&&!f)return s||(s=new Nt),p||HF(e)?Df(e,t,r,o,n,s):LF(e,t,u,r,o,n,s);if(!(n&ko)){var w=f&&Bt.call(e,"__wrapped__"),y=g&&Bt.call(t,"__wrapped__");if(w||y){var h=w?e.value():e,A=y?t.value():t;return s||(s=new Nt),r(h,A,o,n,s)}}return S?(s||(s=new Nt),UF(e,t,r,o,n,s)):!1}function IF(e,t,r,o){var n=r.length,s=n,p=!o;if(e==null)return!s;for(e=Object(e);n--;){var c=r[n];if(p&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++n<s;){c=r[n];var u=c[0],m=e[u],f=c[1];if(p&&c[2]){if(m===void 0&&!(u in e))return!1}else{var g=new Nt;if(o)var S=o(m,f,u,e,t,g);if(!(S===void 0?Zp(f,m,o,$s|ko,g):S))return!1}}return!0}function xF(e){if(!Js(e)||MF(e))return!1;var t=Of(e)||Hp(e)?VT:IT;return t.test(qr(e))}function EF(e){return Zs(e)&&tc(e.length)&&!!de[Do.call(e)]}function vF(e){return typeof e=="function"?e:e==null?WF:typeof e=="object"?Mr(e)?PF(e[0],e[1]):RF(e):XF(e)}function CF(e){if(!qF(e))return $T(e);var t=[];for(var r in Object(e))Bt.call(e,r)&&r!="constructor"&&t.push(r);return t}function RF(e){var t=_F(e);return t.length==1&&t[0][2]?Lf(t[0][0],t[0][1]):function(r){return r===e||IF(r,e,t)}}function PF(e,t){return Ys(e)&&Bf(t)?Lf(Xs(e),t):function(r){var o=zF(r,e);return o===void 0&&o===t?jF(r,e):Zp(t,o,void 0,$s|ko)}}function kF(e){return function(t){return Pf(t,e)}}function DF(e){if(typeof e=="string")return e;if(rc(e))return gf?gf.call(e):"";var t=e+"";return t=="0"&&1/e==-Xp?"-0":t}function NF(e,t,r){var o=-1,n=CT,s=e.length,p=!0,c=[],u=c;if(r)p=!1,n=RT;else if(s>=yf){var m=t?null:BF(e);if(m)return Jp(m);p=!1,n=_T,u=new Rn}else u=t?[]:c;e:for(;++o<s;){var f=e[o],g=t?t(f):f;if(f=r||f!==0?f:0,p&&g===g){for(var S=u.length;S--;)if(u[S]===g)continue e;t&&u.push(g),c.push(f)}else n(u,g,r)||(u!==c&&u.push(g),c.push(f))}return c}function kf(e){return Mr(e)?e:GF(e)}var BF=Po&&1/Jp(new Po([,-0]))[1]==Xp?function(e){return new Po(e)}:YF;function Df(e,t,r,o,n,s){var p=n&ko,c=e.length,u=t.length;if(c!=u&&!(p&&u>c))return!1;var m=s.get(e);if(m&&s.get(t))return m==t;var f=-1,g=!0,S=n&$s?new Rn:void 0;for(s.set(e,t),s.set(t,e);++f<c;){var w=e[f],y=t[f];if(o)var h=p?o(y,w,f,t,e,s):o(w,y,f,e,t,s);if(h!==void 0){if(h)continue;g=!1;break}if(S){if(!PT(t,function(A,T){if(!S.has(T)&&(w===A||r(w,A,o,n,s)))return S.add(T)})){g=!1;break}}else if(!(w===y||r(w,y,o,n,s))){g=!1;break}}return s.delete(e),s.delete(t),g}function LF(e,t,r,o,n,s,p){switch(r){case Ks:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Ef:return!(e.byteLength!=t.byteLength||!o(new ff(e),new ff(t)));case wf:case hf:case Tf:return Uf(+e,+t);case Af:return e.name==t.name&&e.message==t.message;case Ff:case If:return e==t+"";case Gs:var c=OT;case Vs:var u=s&ko;if(c||(c=Jp),e.size!=t.size&&!u)return!1;var m=p.get(e);if(m)return m==t;s|=$s,p.set(e,t);var f=Df(c(e),c(t),o,n,s,p);return p.delete(e),f;case xf:if(Gp)return Gp.call(e)==Gp.call(t)}return!1}function UF(e,t,r,o,n,s){var p=n&ko,c=Wp(e),u=c.length,m=Wp(t),f=m.length;if(u!=f&&!p)return!1;for(var g=u;g--;){var S=c[g];if(!(p?S in t:Bt.call(t,S)))return!1}var w=s.get(e);if(w&&s.get(t))return w==t;var y=!0;s.set(e,t),s.set(t,e);for(var h=p;++g<u;){S=c[g];var A=e[S],T=t[S];if(o)var I=p?o(T,A,S,t,e,s):o(A,T,S,e,t,s);if(!(I===void 0?A===T||r(A,T,o,n,s):I)){y=!1;break}h||(h=S=="constructor")}if(y&&!h){var v=e.constructor,x=t.constructor;v!=x&&"constructor"in e&&"constructor"in t&&!(typeof v=="function"&&v instanceof v&&typeof x=="function"&&x instanceof x)&&(y=!1)}return s.delete(e),s.delete(t),y}function Ws(e,t){var r=e.__data__;return OF(t)?r[typeof t=="string"?"string":"hash"]:r.map}function _F(e){for(var t=Wp(e),r=t.length;r--;){var o=t[r],n=e[o];t[r]=[o,n,Bf(n)]}return t}function No(e,t){var r=QT(e,t);return xF(r)?r:void 0}var cr=bF;($p&&cr(new $p(new ArrayBuffer(1)))!=Ks||vn&&cr(new vn)!=Gs||zp&&cr(zp.resolve())!=af||Po&&cr(new Po)!=Vs||jp&&cr(new jp)!=Kp)&&(cr=function(e){var t=Do.call(e),r=t==Co?e.constructor:void 0,o=r?qr(r):void 0;if(o)switch(o){case zT:return Ks;case jT:return Gs;case WT:return af;case YT:return Vs;case XT:return Kp}return t});function QF(e,t,r){t=Ys(t,e)?[t]:kf(t);for(var o,n=-1,p=t.length;++n<p;){var s=Xs(t[n]);if(!(o=e!=null&&r(e,s)))break;e=e[s]}if(o)return o;var p=e?e.length:0;return!!p&&tc(p)&&Nf(s,p)&&(Mr(e)||_f(e))}function Nf(e,t){return t=t??Sf,!!t&&(typeof e=="number"||xT.test(e))&&e>-1&&e%1==0&&e<t}function Ys(e,t){if(Mr(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||rc(e)?!0:hT.test(e)||!wT.test(e)||t!=null&&e in Object(t)}function OF(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function MF(e){return!!mf&&mf in e}function qF(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||zs;return e===r}function Bf(e){return e===e&&!Js(e)}function Lf(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var GF=ec(function(e){e=$F(e);var t=[];return AT.test(e)&&t.push(""),e.replace(bT,function(r,o,n,s){t.push(n?s.replace(FT,"$1"):o||r)}),t});function Xs(e){if(typeof e=="string"||rc(e))return e;var t=e+"";return t=="0"&&1/e==-Xp?"-0":t}function qr(e){if(e!=null){try{return Rf.call(e)}catch{}try{return e+""}catch{}}return""}function VF(e,t){return e&&e.length?NF(e,vF(t,2)):[]}function ec(e,t){if(typeof e!="function"||t&&typeof t!="function")throw new TypeError(aT);var r=function(){var o=arguments,n=t?t.apply(this,o):o[0],s=r.cache;if(s.has(n))return s.get(n);var p=e.apply(this,o);return r.cache=s.set(n,p),p};return r.cache=new(ec.Cache||Ut),r}ec.Cache=Ut;function Uf(e,t){return e===t||e!==e&&t!==t}function _f(e){return KF(e)&&Bt.call(e,"callee")&&(!KT.call(e,"callee")||Do.call(e)==qs)}var Mr=Array.isArray;function Qf(e){return e!=null&&tc(e.length)&&!Of(e)}function KF(e){return Zs(e)&&Qf(e)}function Of(e){var t=Js(e)?Do.call(e):"";return t==bf||t==pT}function tc(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Sf}function Js(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function Zs(e){return!!e&&typeof e=="object"}function rc(e){return typeof e=="symbol"||Zs(e)&&Do.call(e)==xf}var HF=lf?UT(lf):EF;function $F(e){return e==null?"":DF(e)}function zF(e,t,r){var o=e==null?void 0:Pf(e,t);return o===void 0?r:o}function jF(e,t){return e!=null&&QF(e,t,TF)}function Wp(e){return Qf(e)?AF(e):CF(e)}function WF(e){return e}function YF(){}function XF(e){return Ys(e)?BT(Xs(e)):kF(e)}Ro.exports=VF});var Lg=$e((kn,Qo)=>{i();a();var kI=200,DI="Expected a function",Fc="__lodash_hash_undefined__",ci=1,Oo=2,mg=1/0,dg=9007199254740991,ri="[object Arguments]",yc="[object Array]",fg="[object Boolean]",gg="[object Date]",yg="[object Error]",Sg="[object Function]",NI="[object GeneratorFunction]",oi="[object Map]",wg="[object Number]",_o="[object Object]",og="[object Promise]",hg="[object RegExp]",ni="[object Set]",Ag="[object String]",bg="[object Symbol]",Sc="[object WeakMap]",Tg="[object ArrayBuffer]",si="[object DataView]",BI="[object Float32Array]",LI="[object Float64Array]",UI="[object Int8Array]",_I="[object Int16Array]",QI="[object Int32Array]",OI="[object Uint8Array]",MI="[object Uint8ClampedArray]",qI="[object Uint16Array]",GI="[object Uint32Array]",VI=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,KI=/^\w*$/,HI=/^\./,$I=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,zI=/[\\^$.*+?()[\]{}|]/g,jI=/\\(\\)?/g,WI=/^\[object .+?Constructor\]$/,YI=/^(?:0|[1-9]\d*)$/,fe={};fe[BI]=fe[LI]=fe[UI]=fe[_I]=fe[QI]=fe[OI]=fe[MI]=fe[qI]=fe[GI]=!0;fe[ri]=fe[yc]=fe[Tg]=fe[fg]=fe[si]=fe[gg]=fe[yg]=fe[Sg]=fe[oi]=fe[wg]=fe[_o]=fe[hg]=fe[ni]=fe[Ag]=fe[Sc]=!1;var Fg=typeof self=="object"&&self&&self.Object===Object&&self,XI=typeof self=="object"&&self&&self.Object===Object&&self,fr=Fg||XI||Function("return this")(),Ig=typeof kn=="object"&&kn&&!kn.nodeType&&kn,ng=Ig&&typeof Qo=="object"&&Qo&&!Qo.nodeType&&Qo,JI=ng&&ng.exports===Ig,sg=JI&&Fg.process,ig=function(){try{return sg&&sg.binding("util")}catch{}}(),ag=ig&&ig.isTypedArray;function ZI(e,t,r,o){for(var n=-1,s=e?e.length:0;++n<s;){var p=e[n];t(o,p,r(p),e)}return o}function ex(e,t){for(var r=-1,o=e?e.length:0;++r<o;)if(t(e[r],r,e))return!0;return!1}function tx(e){return function(t){return t?.[e]}}function rx(e,t){for(var r=-1,o=Array(e);++r<e;)o[r]=t(r);return o}function ox(e){return function(t){return e(t)}}function nx(e,t){return e?.[t]}function wc(e){var t=!1;if(e!=null&&typeof e.toString!="function")try{t=!!(e+"")}catch{}return t}function sx(e){var t=-1,r=Array(e.size);return e.forEach(function(o,n){r[++t]=[n,o]}),r}function ix(e,t){return function(r){return e(t(r))}}function ax(e){var t=-1,r=Array(e.size);return e.forEach(function(o){r[++t]=o}),r}var px=Array.prototype,cx=Function.prototype,ui=Object.prototype,fc=fr["__core-js_shared__"],pg=function(){var e=/[^.]+$/.exec(fc&&fc.keys&&fc.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),xg=cx.toString,Mt=ui.hasOwnProperty,Mo=ui.toString,ux=RegExp("^"+xg.call(Mt).replace(zI,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),cg=fr.Symbol,ug=fr.Uint8Array,lx=ui.propertyIsEnumerable,mx=px.splice,dx=ix(Object.keys,Object),hc=qo(fr,"DataView"),Dn=qo(fr,"Map"),Ac=qo(fr,"Promise"),bc=qo(fr,"Set"),Tc=qo(fr,"WeakMap"),Nn=qo(Object,"create"),fx=$r(hc),gx=$r(Dn),yx=$r(Ac),Sx=$r(bc),wx=$r(Tc),ii=cg?cg.prototype:void 0,gc=ii?ii.valueOf:void 0,lg=ii?ii.toString:void 0;function Hr(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function hx(){this.__data__=Nn?Nn(null):{}}function Ax(e){return this.has(e)&&delete this.__data__[e]}function bx(e){var t=this.__data__;if(Nn){var r=t[e];return r===Fc?void 0:r}return Mt.call(t,e)?t[e]:void 0}function Tx(e){var t=this.__data__;return Nn?t[e]!==void 0:Mt.call(t,e)}function Fx(e,t){var r=this.__data__;return r[e]=Nn&&t===void 0?Fc:t,this}Hr.prototype.clear=hx;Hr.prototype.delete=Ax;Hr.prototype.get=bx;Hr.prototype.has=Tx;Hr.prototype.set=Fx;function qt(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function Ix(){this.__data__=[]}function xx(e){var t=this.__data__,r=li(t,e);if(r<0)return!1;var o=t.length-1;return r==o?t.pop():mx.call(t,r,1),!0}function Ex(e){var t=this.__data__,r=li(t,e);return r<0?void 0:t[r][1]}function vx(e){return li(this.__data__,e)>-1}function Cx(e,t){var r=this.__data__,o=li(r,e);return o<0?r.push([e,t]):r[o][1]=t,this}qt.prototype.clear=Ix;qt.prototype.delete=xx;qt.prototype.get=Ex;qt.prototype.has=vx;qt.prototype.set=Cx;function Gt(e){var t=-1,r=e?e.length:0;for(this.clear();++t<r;){var o=e[t];this.set(o[0],o[1])}}function Rx(){this.__data__={hash:new Hr,map:new(Dn||qt),string:new Hr}}function Px(e){return mi(this,e).delete(e)}function kx(e){return mi(this,e).get(e)}function Dx(e){return mi(this,e).has(e)}function Nx(e,t){return mi(this,e).set(e,t),this}Gt.prototype.clear=Rx;Gt.prototype.delete=Px;Gt.prototype.get=kx;Gt.prototype.has=Dx;Gt.prototype.set=Nx;function ai(e){var t=-1,r=e?e.length:0;for(this.__data__=new Gt;++t<r;)this.add(e[t])}function Bx(e){return this.__data__.set(e,Fc),this}function Lx(e){return this.__data__.has(e)}ai.prototype.add=ai.prototype.push=Bx;ai.prototype.has=Lx;function Ot(e){this.__data__=new qt(e)}function Ux(){this.__data__=new qt}function _x(e){return this.__data__.delete(e)}function Qx(e){return this.__data__.get(e)}function Ox(e){return this.__data__.has(e)}function Mx(e,t){var r=this.__data__;if(r instanceof qt){var o=r.__data__;if(!Dn||o.length<kI-1)return o.push([e,t]),this;r=this.__data__=new Gt(o)}return r.set(e,t),this}Ot.prototype.clear=Ux;Ot.prototype.delete=_x;Ot.prototype.get=Qx;Ot.prototype.has=Ox;Ot.prototype.set=Mx;function qx(e,t){var r=dr(e)||Ng(e)?rx(e.length,String):[],o=r.length,n=!!o;for(var s in e)(t||Mt.call(e,s))&&!(n&&(s=="length"||Rg(s,o)))&&r.push(s);return r}function li(e,t){for(var r=e.length;r--;)if(Dg(e[r][0],t))return r;return-1}function Gx(e,t,r,o){return Vx(e,function(n,s,p){t(o,n,r(n),p)}),o}var Vx=sE(Hx),Kx=iE();function Hx(e,t){return e&&Kx(e,t,pi)}function Eg(e,t){t=di(t,e)?[t]:vg(t);for(var r=0,o=t.length;e!=null&&r<o;)e=e[fi(t[r++])];return r&&r==o?e:void 0}function $x(e){return Mo.call(e)}function zx(e,t){return e!=null&&t in Object(e)}function Ic(e,t,r,o,n){return e===t?!0:e==null||t==null||!gi(e)&&!yi(t)?e!==e&&t!==t:jx(e,t,Ic,r,o,n)}function jx(e,t,r,o,n,s){var p=dr(e),c=dr(t),u=yc,m=yc;p||(u=mr(e),u=u==ri?_o:u),c||(m=mr(t),m=m==ri?_o:m);var f=u==_o&&!wc(e),g=m==_o&&!wc(t),S=u==m;if(S&&!f)return s||(s=new Ot),p||SE(e)?Cg(e,t,r,o,n,s):aE(e,t,u,r,o,n,s);if(!(n&Oo)){var w=f&&Mt.call(e,"__wrapped__"),y=g&&Mt.call(t,"__wrapped__");if(w||y){var h=w?e.value():e,A=y?t.value():t;return s||(s=new Ot),r(h,A,o,n,s)}}return S?(s||(s=new Ot),pE(e,t,r,o,n,s)):!1}function Wx(e,t,r,o){var n=r.length,s=n,p=!o;if(e==null)return!s;for(e=Object(e);n--;){var c=r[n];if(p&&c[2]?c[1]!==e[c[0]]:!(c[0]in e))return!1}for(;++n<s;){c=r[n];var u=c[0],m=e[u],f=c[1];if(p&&c[2]){if(m===void 0&&!(u in e))return!1}else{var g=new Ot;if(o)var S=o(m,f,u,e,t,g);if(!(S===void 0?Ic(f,m,o,ci|Oo,g):S))return!1}}return!0}function Yx(e){if(!gi(e)||mE(e))return!1;var t=Bg(e)||wc(e)?ux:WI;return t.test($r(e))}function Xx(e){return yi(e)&&vc(e.length)&&!!fe[Mo.call(e)]}function Jx(e){return typeof e=="function"?e:e==null?bE:typeof e=="object"?dr(e)?tE(e[0],e[1]):eE(e):TE(e)}function Zx(e){if(!dE(e))return dx(e);var t=[];for(var r in Object(e))Mt.call(e,r)&&r!="constructor"&&t.push(r);return t}function eE(e){var t=cE(e);return t.length==1&&t[0][2]?kg(t[0][0],t[0][1]):function(r){return r===e||Wx(r,e,t)}}function tE(e,t){return di(e)&&Pg(t)?kg(fi(e),t):function(r){var o=hE(r,e);return o===void 0&&o===t?AE(r,e):Ic(t,o,void 0,ci|Oo)}}function rE(e){return function(t){return Eg(t,e)}}function oE(e){if(typeof e=="string")return e;if(Cc(e))return lg?lg.call(e):"";var t=e+"";return t=="0"&&1/e==-mg?"-0":t}function vg(e){return dr(e)?e:fE(e)}function nE(e,t){return function(r,o){var n=dr(r)?ZI:Gx,s=t?t():{};return n(r,e,Jx(o,2),s)}}function sE(e,t){return function(r,o){if(r==null)return r;if(!Ec(r))return e(r,o);for(var n=r.length,s=t?n:-1,p=Object(r);(t?s--:++s<n)&&o(p[s],s,p)!==!1;);return r}}function iE(e){return function(t,r,o){for(var n=-1,s=Object(t),p=o(t),c=p.length;c--;){var u=p[e?c:++n];if(r(s[u],u,s)===!1)break}return t}}function Cg(e,t,r,o,n,s){var p=n&Oo,c=e.length,u=t.length;if(c!=u&&!(p&&u>c))return!1;var m=s.get(e);if(m&&s.get(t))return m==t;var f=-1,g=!0,S=n&ci?new ai:void 0;for(s.set(e,t),s.set(t,e);++f<c;){var w=e[f],y=t[f];if(o)var h=p?o(y,w,f,t,e,s):o(w,y,f,e,t,s);if(h!==void 0){if(h)continue;g=!1;break}if(S){if(!ex(t,function(A,T){if(!S.has(T)&&(w===A||r(w,A,o,n,s)))return S.add(T)})){g=!1;break}}else if(!(w===y||r(w,y,o,n,s))){g=!1;break}}return s.delete(e),s.delete(t),g}function aE(e,t,r,o,n,s,p){switch(r){case si:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case Tg:return!(e.byteLength!=t.byteLength||!o(new ug(e),new ug(t)));case fg:case gg:case wg:return Dg(+e,+t);case yg:return e.name==t.name&&e.message==t.message;case hg:case Ag:return e==t+"";case oi:var c=sx;case ni:var u=s&Oo;if(c||(c=ax),e.size!=t.size&&!u)return!1;var m=p.get(e);if(m)return m==t;s|=ci,p.set(e,t);var f=Cg(c(e),c(t),o,n,s,p);return p.delete(e),f;case bg:if(gc)return gc.call(e)==gc.call(t)}return!1}function pE(e,t,r,o,n,s){var p=n&Oo,c=pi(e),u=c.length,m=pi(t),f=m.length;if(u!=f&&!p)return!1;for(var g=u;g--;){var S=c[g];if(!(p?S in t:Mt.call(t,S)))return!1}var w=s.get(e);if(w&&s.get(t))return w==t;var y=!0;s.set(e,t),s.set(t,e);for(var h=p;++g<u;){S=c[g];var A=e[S],T=t[S];if(o)var I=p?o(T,A,S,t,e,s):o(A,T,S,e,t,s);if(!(I===void 0?A===T||r(A,T,o,n,s):I)){y=!1;break}h||(h=S=="constructor")}if(y&&!h){var v=e.constructor,x=t.constructor;v!=x&&"constructor"in e&&"constructor"in t&&!(typeof v=="function"&&v instanceof v&&typeof x=="function"&&x instanceof x)&&(y=!1)}return s.delete(e),s.delete(t),y}function mi(e,t){var r=e.__data__;return lE(t)?r[typeof t=="string"?"string":"hash"]:r.map}function cE(e){for(var t=pi(e),r=t.length;r--;){var o=t[r],n=e[o];t[r]=[o,n,Pg(n)]}return t}function qo(e,t){var r=nx(e,t);return Yx(r)?r:void 0}var mr=$x;(hc&&mr(new hc(new ArrayBuffer(1)))!=si||Dn&&mr(new Dn)!=oi||Ac&&mr(Ac.resolve())!=og||bc&&mr(new bc)!=ni||Tc&&mr(new Tc)!=Sc)&&(mr=function(e){var t=Mo.call(e),r=t==_o?e.constructor:void 0,o=r?$r(r):void 0;if(o)switch(o){case fx:return si;case gx:return oi;case yx:return og;case Sx:return ni;case wx:return Sc}return t});function uE(e,t,r){t=di(t,e)?[t]:vg(t);for(var o,n=-1,p=t.length;++n<p;){var s=fi(t[n]);if(!(o=e!=null&&r(e,s)))break;e=e[s]}if(o)return o;var p=e?e.length:0;return!!p&&vc(p)&&Rg(s,p)&&(dr(e)||Ng(e))}function Rg(e,t){return t=t??dg,!!t&&(typeof e=="number"||YI.test(e))&&e>-1&&e%1==0&&e<t}function di(e,t){if(dr(e))return!1;var r=typeof e;return r=="number"||r=="symbol"||r=="boolean"||e==null||Cc(e)?!0:KI.test(e)||!VI.test(e)||t!=null&&e in Object(t)}function lE(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function mE(e){return!!pg&&pg in e}function dE(e){var t=e&&e.constructor,r=typeof t=="function"&&t.prototype||ui;return e===r}function Pg(e){return e===e&&!gi(e)}function kg(e,t){return function(r){return r==null?!1:r[e]===t&&(t!==void 0||e in Object(r))}}var fE=xc(function(e){e=wE(e);var t=[];return HI.test(e)&&t.push(""),e.replace($I,function(r,o,n,s){t.push(n?s.replace(jI,"$1"):o||r)}),t});function fi(e){if(typeof e=="string"||Cc(e))return e;var t=e+"";return t=="0"&&1/e==-mg?"-0":t}function $r(e){if(e!=null){try{return xg.call(e)}catch{}try{return e+""}catch{}}return""}var gE=nE(function(e,t,r){e[r]=t});function xc(e,t){if(typeof e!="function"||t&&typeof t!="function")throw new TypeError(DI);var r=function(){var o=arguments,n=t?t.apply(this,o):o[0],s=r.cache;if(s.has(n))return s.get(n);var p=e.apply(this,o);return r.cache=s.set(n,p),p};return r.cache=new(xc.Cache||Gt),r}xc.Cache=Gt;function Dg(e,t){return e===t||e!==e&&t!==t}function Ng(e){return yE(e)&&Mt.call(e,"callee")&&(!lx.call(e,"callee")||Mo.call(e)==ri)}var dr=Array.isArray;function Ec(e){return e!=null&&vc(e.length)&&!Bg(e)}function yE(e){return yi(e)&&Ec(e)}function Bg(e){var t=gi(e)?Mo.call(e):"";return t==Sg||t==NI}function vc(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=dg}function gi(e){var t=typeof e;return!!e&&(t=="object"||t=="function")}function yi(e){return!!e&&typeof e=="object"}function Cc(e){return typeof e=="symbol"||yi(e)&&Mo.call(e)==bg}var SE=ag?ox(ag):Xx;function wE(e){return e==null?"":oE(e)}function hE(e,t,r){var o=e==null?void 0:Eg(e,t);return o===void 0?r:o}function AE(e,t){return e!=null&&uE(e,t,zx)}function pi(e){return Ec(e)?qx(e):Zx(e)}function bE(e){return e}function TE(e){return di(e)?tx(fi(e)):rE(e)}Qo.exports=gE});var mu=$e(lu=>{"use strict";i();a();Object.defineProperty(lu,"__esModule",{value:!0});var IS=R();function bC(e){var t=IS.useRef();return IS.useEffect(function(){t.current=e}),t.current}lu.default=bC});i();a();i();a();i();a();var Xh=l.object({balance:l.boolean(),history:l.boolean()}),lk=l.record(l.string(),Xh),As=class extends Error{constructor(t){super(t),this.name="SetTermsAcknowledgementError"}};var wm=async(e,t)=>{try{return await e.set("termsHaveBeenAcknowledged2",t)}catch{throw new As("Error Setting Terms Acknowledgement")}};async function Jh(e){return(await e.get("onboardedAt"))?.onboardedAt}async function hm(e,t=Date.now()){t&&!await Jh(e)?await e.set("onboardedAt",{onboardedAt:t}):t||await e.remove("onboardedAt")}i();a();i();a();var xm=C(Yh(),1),Em=C(Im(),1),$a=C(jh(),1),bo=(e=>(e.BLOCK="BLOCK",e.NONE="NONE",e))(bo||{});function Zh(e,t){for(let r=0;r<e.k;r++){let o=(0,Em.default)(`${e.salt}_${t}_${r}`,{asBytes:!0}),s=$a.Buffer.from(o).subarray(0,4).readUInt32BE(0)%e.bits,p=Math.floor(s/24),c=$a.Buffer.from(e.bitVector.slice(p*4,p*4+4),"base64"),u=s%24,m=Math.floor(u/8),f=u%8;if(!(c[m]&1<<7-f))return!1}return!0}var Nk=typeof self<"u"&&self.fetch?self.fetch:xm.default;function za(e,t,r,o){let s=new URL(o).hostname.toLowerCase().split(".");for(let p=0;p<s.length-1;p++){let c=s.slice(p).join(".");if(t.includes(c)||Zh(e,c)&&!r.includes(c))return"BLOCK"}return"NONE"}var bs=C(Rm());var _m=C(Dm());i();a();var Nm="https://blocklist.phantom.app/multichain.json",Ya="https://blowfish-blocklist-proxy.phantom.app",cA="https://github.com/phantom-labs/blocklist",Xa=(e=3)=>({retry:e,retryDelay:t=>Math.min(1e3*2**t,3e4)}),yt={SLIPPAGE_EXCEEDED:"SLIPPAGE_EXCEEDED",INSUFFICIENT_FUNDS:"INSUFFICIENT_FUNDS",INSUFFICIENT_GAS:"INSUFFICIENT_GAS",UNKNOWN_ERROR:"UNKNOWN_ERROR",SIMULATION_FAILED:"SIMULATION_FAILED",TRANSACTION_EXPIRED:"TRANSACTION_EXPIRED",ACCOUNT_FROZEN:"ACCOUNT_FROZEN",SIMULATION_TIMEOUT:"SIMULATION_TIMEOUT",STALE_QUOTA:"STALE_QUOTA"};function uA(e){return{1:"1-critical-alert",2:"2-alert",3:"3-critical-error",4:"4-error",5:"5-information"}[e]}var mA=3,tp=6e5,dA=6e4,oD=l.object({type:l.union([l.literal("all"),l.literal("blocklist"),l.literal("whitelist"),l.literal("error"),l.literal("fuzzy"),l.literal("blowfishBloomFilter")]),result:l.boolean(),source:l.optional(l.string()),match:l.string().optional(),isSubdomainDomainWhitelisted:l.boolean().optional()}),nD=l.object({localHash:l.any(),cursor:l.any()}),rp=l.object({bitVector:l.string(),k:l.number(),hash:l.string(),bits:l.number(),salt:l.string()}),Qm=l.union([l.object({bloomFilter:rp,bloomFilterHash:l.string(),recentlyAdded:l.array(l.string()),recentlyRemoved:l.array(l.string()),nextCursor:l.string()}),l.object({bloomFilter:rp.optional(),bloomFilterHash:l.optional(l.string()),recentlyAdded:l.array(l.string()),recentlyRemoved:l.array(l.string()),nextCursor:l.string()})]),Om=l.object({bloomFilter:rp,lastUpdated:l.number()}).nullish(),Mm=l.object({recentlyAdded:l.array(l.string()),recentlyRemoved:l.array(l.string()),lastUpdated:l.number().optional()}).nullish(),yn=class{constructor(t,r,o,n){this.blocklist=Ja(t),this.whitelist=Ja(r),this.fuzzylist=Ja(o),this.tolerance=n||mA}check(t,r){if(!t)return{type:"all",result:!1,isSubdomainDomainWhitelisted:!1};let o=Ts(t),n=op(o);if(gA(n,this.whitelist)){let u=new URL(t).hostname;if(Za(op(u),this.blocklist)){let f={type:"blocklist",result:!0,source:u,isSubdomainDomainWhitelisted:!0};return r.capture("blockedDomain",{data:f}),f}else return{type:"whitelist",result:!1,source:u,isSubdomainDomainWhitelisted:!0}}if(Za(n,this.whitelist))return{type:"whitelist",result:!1,source:o,isSubdomainDomainWhitelisted:!1};if(Za(n,this.blocklist)){let u={type:"blocklist",result:!0,source:o,isSubdomainDomainWhitelisted:!1};return r.capture("blockedDomain",{data:u}),u}if(this.tolerance>0){let u=Bm(n);u=u.replace("www.","");let m=this.fuzzylist.find(f=>{let g=Bm(f);return _m.default.get(u,g)<=this.tolerance});if(m)return{type:"fuzzy",result:!0,match:fA(m),source:o,isSubdomainDomainWhitelisted:!1}}return{type:"all",result:!1,source:o,isSubdomainDomainWhitelisted:!1}}},Ts=e=>{let t=new URL(e).hostname.split(".");return t.slice(0).slice(-(t.length===4?3:2)).join(".")},Ja=e=>e.map(op),op=e=>e.split(".").reverse(),fA=e=>e.slice().reverse().join("."),Bm=e=>e.slice(1).reverse().join("."),Za=(e,t)=>t.some(r=>r.length>e.length?!1:r.every((o,n)=>e[n]===o)),gA=(e,t)=>t.some(r=>r[0]===e[0]&&r[1]===e[1]&&r[2]==="*"),yA=async e=>{let t=(0,bs.getBlocklist)(),r=await e.get("cachedBlocklistDiff"),o=null;try{o=JSON.parse(`${r}`)}catch(n){Q.captureError(new Error(`Error parsing blocklist diff: ${n?.message}`),"generic")}if(o&&o.expiresAt&&o.expiresAt>Date.now())return new yn(t.blocklist.concat(o.blocklist),t.whitelist.concat(o.whitelist),t.fuzzylist.concat(o.fuzzylist));{let n={},s=(0,bs.getVersion)();o&&(n.lastContentHash=`${o.contentHash}`),s&&(n.localBlocklistVersion=`${s}`);let p=await U.headers(n).get(Nm);if(p.status===200){let c=p.data;return c.expiresAt=Date.now()+tp,e.set("cachedBlocklistDiff",JSON.stringify(c)),new yn(t.blocklist.concat(c.blocklist),t.whitelist.concat(c.whitelist),t.fuzzylist.concat(c.fuzzylist))}else{if(p.status===304)return o.expiresAt=Date.now()+tp,e.set("cachedBlocklistDiff",JSON.stringify(o)),new yn(t.blocklist.concat(o.blocklist),t.whitelist.concat(o.whitelist),t.fuzzylist.concat(o.fuzzylist));throw new Error("Invalid response from blocklist")}}},np=async(e,t,r)=>{let o,n;try{let p=await is(t.getFeatureFlags(),1e3);o=!!p["kill-blowfish-blocklist-cursor"],n=!!p["kill-previous-blocklist"]}catch{o=!1,n=!1}let s;try{if(n)return o?Um(r,e,t):Lm(r,e,t);{s=await yA(e);let p=s.check(r,t);return p.type==="all"?o?Um(r,e,t):Lm(r,e,t):p}}catch{return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1}}},Lm=async(e,t,r)=>{if(!e)return{type:"all",result:!1,isSubdomainDomainWhitelisted:!1};let o=Ts(e),n=await t.getAll(["cachedBloomFilter","cachedBloomFilterHash","cachedScanDataRecents","cachedScanDataCursor"]),s=n.cachedBloomFilter??null,p=n.cachedBloomFilterHash??null,c=n.cachedScanDataCursor??null,u=n.cachedScanDataRecents??null,m=null,f={recentlyAdded:[],recentlyRemoved:[],lastUpdated:0},g=null;if(s){let w=Om.safeParse(s);w.success?m=w.data:await ep(t)}if(u){let w=Mm.safeParse(u);w.success?f=w.data:await ep(t)}if(c){let y=l.string().safeParse(c);y.success?g=y.data:await ep(t)}if(!f||f.lastUpdated==null||Date.now()>f.lastUpdated+dA){let w={localHash:p,cursor:g},y=await U.post(Ya,w);if(y.status!==200)return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1};let h=y.data,A,T=Qm.safeParse(h);if(T.success)A=T.data;else return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1};let I=[],v=[],x=typeof p=="string"&&A.bloomFilterHash?p!==A.bloomFilterHash:!1;f!=null&&!x&&(I=f.recentlyAdded,v=f.recentlyRemoved),I=I.concat(A.recentlyAdded),v=v.concat(A.recentlyRemoved),I=I.filter(P=>A.recentlyRemoved.indexOf(P)<0),v=v.filter(P=>A.recentlyAdded.indexOf(P)<0);let E={recentlyAdded:I,recentlyRemoved:v,lastUpdated:Date.now()};if(await t.set("cachedScanDataRecents",E),f=E,A.nextCursor!==g&&await t.set("cachedScanDataCursor",A.nextCursor),A.bloomFilter!=null){let P={bloomFilter:A.bloomFilter,lastUpdated:Date.now()};await Promise.all([t.set("cachedBloomFilter",P),t.set("cachedBloomFilterHash",A.bloomFilterHash)]),m=P}}if(!m)return{type:"all",result:!1,isSubdomainDomainWhitelisted:!1};let S=za(m.bloomFilter,f.recentlyAdded,f.recentlyRemoved,e);if(S===bo.BLOCK){let w={result:!0,source:o,type:"blowfishBloomFilter"};return r.capture("blockedDomain",{data:w}),w}else if(S===bo.NONE)return{result:!1,type:"all",isSubdomainDomainWhitelisted:!1};return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1}};async function ep(e){await e.remove(["cachedBloomFilter","cachedBloomFilterHash","cachedScanDataRecents","cachedScanDataCursor"])}var Um=async(e,t,r)=>{if(!e)return{type:"all",result:!1,isSubdomainDomainWhitelisted:!1};let o=Ts(e),n=await t.getAll(["cachedBloomFilter","cachedBloomFilterHash","cachedScanDataRecents"]),s=n.cachedBloomFilter??null,p=n.cachedBloomFilterHash??null,c=n.cachedScanDataRecents??null,u=null,m={recentlyAdded:[],recentlyRemoved:[],lastUpdated:0};if(s){let g=Om.safeParse(s);g.success?u=g.data:await t.remove(["cachedBloomFilter","cachedBloomFilterHash","cachedScanDataRecents"])}if(c){let g=Mm.safeParse(c);g.success?m=g.data:await t.remove(["cachedBloomFilter","cachedBloomFilterHash","cachedScanDataRecents"])}if(!m||m.lastUpdated==null||Date.now()>m.lastUpdated+tp){let g={localHash:p},S=await U.post(Ya,g);if(S.status!==200)return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1};let w=S.data,y,h=Qm.safeParse(w);if(h.success)y=h.data;else return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1};let A={recentlyAdded:y.recentlyAdded,recentlyRemoved:y.recentlyRemoved,lastUpdated:Date.now()};if(await t.set("cachedScanDataRecents",A),m=A,y.bloomFilter!=null){let T={bloomFilter:y.bloomFilter,lastUpdated:Date.now()};await Promise.all([t.set("cachedBloomFilter",T),t.set("cachedBloomFilterHash",y.bloomFilterHash)]),u=T}}if(!u)return{type:"all",result:!1,isSubdomainDomainWhitelisted:!1};let f=za(u.bloomFilter,m.recentlyAdded,m.recentlyRemoved,e);if(f===bo.BLOCK){let g={result:!0,source:o,type:"blowfishBloomFilter"};return r.capture("blockedDomain",{data:g}),g}else if(f===bo.NONE)return{result:!1,type:"all",isSubdomainDomainWhitelisted:!1};return{result:!1,type:"error",isSubdomainDomainWhitelisted:!1}};i();a();i();a();var SA=e=>e/1e8;i();a();i();a();var Fs={GREEN:"accentSuccess",RED:"accentAlert",WHITE:"textPrimary"};function hA(e){switch(e){case"PLUS":return Fs.GREEN;case"MINUS":return Fs.RED;case"EQUAL":return Fs.WHITE;default:return Fs.WHITE}}function AA(e){let t=e?.advancedDetails&&"safeguard"in e.advancedDetails?e?.advancedDetails?.safeguard:void 0;if(t?.recommended&&!t?.shouldBundle&&!t.error&&!t.verifyError&&t?.transactions&&t.transactions.length>0)return t.transactions}function qm(e,t){if(e.params&&"safeguard"in e.params&&e.params.safeguard&&t?.advancedDetails&&"safeguard"in t.advancedDetails&&t.advancedDetails.safeguard?.transactions){let r=t.advancedDetails.safeguard;if(r.error)return r.error;let o=e.params.transactions,n=r.transactions;if(n){let s;try{rm(o,n)}catch(p){p instanceof ho?s=p:p instanceof Error?s=ho.createUnknownError(p.message):s=ho.createUnknownError("Non error type",p)}if(s)return t.advancedDetails.safeguard.verifyError=s,s}}}function Gm(e){if(e.advancedDetails&&"requestId"in e.advancedDetails)return e.advancedDetails.requestId}i();a();i();a();var sp=async(e,t,r)=>{let o=`/simulation/v1?language=${e}`,n=await U.api().post(o,{...r,chainId:r.networkID,appVersion:le,platform:Ae,deviceId:t});if(!se(n))throw new Error("Failed to scan message");return n.data},Sn=async(e,t,r)=>{let o=`/simulation/v1?language=${e}`,n={...r.params,...r.params&&"safeguard"in r.params?{safeguard:{...r.params.safeguard,lighthouseProgramId:tm}}:{}},s=await U.api().post(o,{...r,chainId:r.networkID,params:n,metadata:r.metadata,appVersion:le,platform:Ae,deviceId:t});if(!se(s))throw new Error("Failed to scan transaction");let p=s.data,c=qm(r,p);if(c instanceof ho){let u=Gm(p);Q.captureMessage(`Verify error ${u??""}: ${c.message}`,"safeguard")}return p};var UD=l.union([l.literal("signTransaction"),l.literal("signAllTransactions"),l.literal("signAndSendTransaction"),l.literal("signAndSendAllTransactions")]);i();a();var Vm=l.enum(["Native","Fungible","SemiFungible","NonFungible","Unknown"]),Pt=l.object({id:l.string(),displayName:l.string().optional(),logoURI:l.union([l.string(),l.null()]).optional(),tokenType:Vm,decimals:l.number().optional(),symbol:l.string().optional(),isSpam:l.boolean().optional()}),Km=l.object({id:xt,displayName:l.string(),logoURI:l.union([l.string(),l.null()]).optional()}),Is=l.object({token:Pt,amount:l.string(),to:xt,from:xt}),ip=l.literal("UNCLASSIFIED_APP_INTERACTION"),ap=l.literal("TOKEN_SEND"),pp=l.literal("TOKEN_BURN"),cp=l.literal("TOKEN_SWAP"),up=l.literal("TOKEN_UNWRAP"),lp=l.literal("WITHDRAW_STAKE"),mp=l.literal("DEACTIVATE_STAKE"),dp=l.literal("STAKE"),fp=l.literal("COLLECTIBLE_LIST"),gp=l.literal("COLLECTIBLE_BUY_ITEM"),yp=l.literal("COLLECTIBLE_CANCEL_BID"),Sp=l.literal("COLLECTIBLE_BID_ITEM"),wp=l.literal("COLLECTIBLE_UNLIST"),hp=l.literal("COLLECTIBLE_SELL_ITEM"),Ap=l.literal("CANCEL_TX"),bp=l.literal("TOKEN_APPROVAL"),Tp=l.literal("CANCEL_ORDER"),Fp=l.literal("COLLECTION_APPROVAL"),Ip=l.literal("BRIDGE_INIT"),bA=l.union([ip,ap,pp,cp,up,lp,mp,dp,fp,gp,yp,Sp,wp,hp,Ap,bp,Tp,Fp,Ip]),Hm=l.object({transactionType:ip,balanceChanges:l.array(Is),dapp:l.union([ft,l.null()]).optional()}),$m=l.object({transactionType:ap,balanceChanges:l.array(Is).nonempty(),dapp:l.union([ft,l.null()]).optional()}),zm=l.object({transactionType:pp,dapp:l.union([ft,l.null()]).optional(),token:Pt,amount:l.string()}),jm=l.object({transactionType:cp,dapp:l.union([ft,l.null()]).optional(),receiveToken:Pt,sendToken:Pt,receiveAmount:l.string(),sendAmount:l.string()}),Wm=l.object({transactionType:up,token:Pt,amount:l.string()}),Ym=l.object({transactionType:lp,stakeAccount:xt,amount:l.string()}),Xm=l.object({transactionType:mp,stakeAccount:xt,amount:l.string()}),Jm=l.object({transactionType:dp,stakeAccount:xt,amount:l.string(),validatorName:l.string().optional(),validatorWebsite:l.string().optional(),keybaseUsername:l.string().optional()}),Zm=l.object({transactionType:Ap}),Fo=l.object({dapp:l.union([ft,l.null()]),itemAmount:l.string().optional(),item:Pt,forAmount:l.string(),forAsset:Pt,listingUrl:l.string().optional()}),ed=Fo.merge(l.object({transactionType:fp})),td=Fo.merge(l.object({transactionType:gp})),rd=Fo.merge(l.object({transactionType:yp})),od=Fo.merge(l.object({transactionType:Sp})),nd=Fo.merge(l.object({transactionType:wp})),sd=Fo.merge(l.object({transactionType:hp})),id=l.object({transactionType:bp,dapp:l.union([ft,l.null()]).optional(),token:Pt,value:l.string().optional(),isApproved:l.boolean()}),ad=l.object({transactionType:Tp,dapp:l.union([ft,l.null()]).optional(),orderId:l.string()}),pd=l.object({transactionType:Fp,collection:Km,dapp:l.union([ft,l.null()]).optional(),isApproved:l.boolean()}),cd=l.object({transactionType:Ip,dapp:l.union([ft,l.null()]).optional(),balanceChanges:l.array(Is).nonempty(),explorerUrl:l.string().optional()}),ud=l.enum(["success","failed"]),ld=l.object({networkFee:l.string().optional(),networkFeePayer:l.string().optional(),status:ud,errorMessage:l.union([l.string(),l.null()]),blockNumber:l.string(),chainId:or,transactionId:l.string()}),xs=l.object({timestamp:l.number(),owner:xt,id:xt,interactionData:l.union([Hm,$m,zm,jm,Wm,Ym,Xm,Jm,ed,td,rd,od,nd,sd,Zm,id,ad,pd,cd]),chainMeta:ld}),qe=e=>{let o=e.split("/")[1]?.split(":");return o&&o[1]};i();a();var xp=C(R());var TA=["So11111111111111111111111111111111111111112","501"],FA=(e,t,r,o,n)=>{let{interactionData:s,chainMeta:p}=e,{transactionType:c}=s,u=p.status==="failed",m=p.chainId,f=F.getTokenSymbol(m),g=F.getTokenDecimals(m),S=t("historyUnknownDappName"),w=xp.default.createElement(r,{activityItem:e,size:"small",isDetailViewOrSvg:!1});switch(c){case"TOKEN_SEND":{let h=s.balanceChanges[0],{token:{symbol:A,displayName:T,tokenType:I,decimals:v},amount:x}=h,E=md(h,n),P=wn(I),k=I==="NonFungible",H="";if(!u&&v!==void 0&&!k){let j=E?"+":"-";P?H=`${j}${Ce(x,v)}`:H=`${j}${Ce(x,v)}${A?` ${It(A,6)}`:""}`}if(E){let j=qe(h.from),M=Qa(j,void 0,o(j,m));return{topLeft:{text:`${t("transactionsReceived")} ${P?T||t("richTransactionDetailUnknownNFT"):""}`},topRight:{text:`${H}`,color:"accentSuccess"},bottomLeft:{text:t("transactionsFromParagraphInterpolated",{from:M,defaultValue:`From ${M}`})},start:w}}else{let j=qe(h.to),M=Qa(j,void 0,o(j,m));return{topLeft:{text:u?t("transactionsSendFailed"):`${t("transactionsSent")} ${P?T||t("richTransactionDetailUnknownNFT"):""}`},topRight:{text:`${H}`,color:"textPrimary"},bottomLeft:{text:t("transactionsToParagraphInterpolated",{to:M,defaultValue:`To ${M}`})},start:w}}}case"TOKEN_BURN":{let{amount:y,token:h}=s,A="";return h.displayName?y==="1"?A=h.displayName:A=`${h.displayName} (${y})`:A=t("richTransactionDetailUnknownNFT"),{topLeft:{text:u?`${t("transactionsFailedBurn")}`:`${t("transactionsBurned")}`},bottomLeft:{text:A},start:w}}case"TOKEN_UNWRAP":{let{amount:y,token:h}=s;return{topLeft:{text:u?`${t("transactionsUnwrappedFailed")}`:`${t("transactionsUnwrapped")}`},topRight:{text:u?"":`${Ce(y,h.decimals??0)} ${h.symbol}`,color:"textPrimary"},start:w}}case"TOKEN_SWAP":{let{receiveAmount:y,receiveToken:h,sendAmount:A,sendToken:T,dapp:I}=s,v=I?I.displayName:t("historyUnknownDappName"),x=mn(qe(T.id)||""),E=mn(qe(h.id)||""),P="";!u&&h.decimals!==void 0&&(P=`+${Ce(y,h.decimals)} ${It(h.symbol||E,6)}`);let k="";return!u&&T.decimals!==void 0&&(k=`-${Ce(A,T.decimals)} ${It(T.symbol||x,6)}`),{topLeft:{text:t(u?"transactionsFailedSwap":"transactionsSwapped")},topRight:{text:P,color:"accentSuccess"},bottomLeft:{text:v},bottomRight:{text:k,color:"textPrimary"},start:w}}case"BRIDGE_INIT":{let y=s.dapp?s.dapp.displayName:S,A=s.balanceChanges[0],{token:{symbol:T,decimals:I},amount:v}=A,x=T?` ${It(T,6)}`:"";if(!u&&I!==void 0){let E=`-${Ce(v,I)}${x}`;return{topLeft:{text:u?t("transactionBridgeInitiatedFailed"):`${t("transactionBridgeInitiated")}`},bottomLeft:{text:y},topRight:{text:`${E}`,color:"textPrimary"},start:w}}return{topLeft:{text:u?t("transactionBridgeInitiatedFailed"):`${t("transactionBridgeInitiated")}`},bottomLeft:{text:y},start:w}}case"WITHDRAW_STAKE":{let{amount:y,stakeAccount:h}=s,A=qe(h),T=F.getTokenSymbol(m);return{topLeft:{text:t(u?"transactionsWithdrawStakeFailed":"transactionsWithdrawStake")},topRight:{text:u?"":`${Ce(y,g)} ${It(T,6)}`,color:"textPrimary"},bottomLeft:{text:ms(A,4)},start:w}}case"DEACTIVATE_STAKE":{let{stakeAccount:y}=s,h=qe(y);return{topLeft:{text:t(u?"transactionsDeactivateStakeFailed":"transactionsDeactivateStake")},bottomLeft:{text:ms(h,4)},start:w}}case"STAKE":{let{amount:y,stakeAccount:h,validatorName:A}=s,T=qe(h);return{topLeft:{text:t(u?"transactionsStakedFailed":"transactionsStaked")},topRight:{text:u?"":`${Ce(y,g)} ${It(f,6)}`,color:"textPrimary"},bottomLeft:{text:A||ms(T,4)},start:w}}case"COLLECTIBLE_LIST":{let{dapp:y,item:h}=s,A=y?y.displayName:t("historyUnknownDappName"),T=h.displayName||t("richTransactionDetailUnknownNFT");return{topLeft:{text:u?t("transactionsListedFailed"):t("transactionsListedInterpolated",{name:T})},bottomLeft:{text:A},start:w}}case"COLLECTIBLE_BUY_ITEM":{let{dapp:y,itemAmount:h,item:A,forAsset:T,forAmount:I}=s,v=y?y.displayName:t("historyUnknownDappName"),x=T.decimals!==void 0?`-${Ce(I,T.decimals)} ${It(T.symbol||"",6)}`:t("transactionsUnknownAmount"),E=A.symbol||mn(qe(A.id)),P=h&&A.tokenType==="SemiFungible"?`+${h} ${E}`:`+${E}`;return{topLeft:{text:t(u?"transactionsBuyFailed":"transactionsBoughtCollectible")},topRight:{text:u?"":P,color:"accentSuccess"},bottomRight:{text:u?"":`${x}`,color:"textPrimary"},bottomLeft:{text:v},start:w}}case"COLLECTIBLE_CANCEL_BID":{let{dapp:y,item:h}=s,A=y?y.displayName:t("historyUnknownDappName"),T=h.displayName||t("richTransactionDetailUnknownNFT");return{topLeft:{text:u?t("transactionsCancelBidOnFailed"):t("transactionsCancelBidOnInterpolated",{name:T})},bottomLeft:{text:A},start:w}}case"COLLECTIBLE_BID_ITEM":{let{dapp:y,item:h}=s,A=y?y.displayName:t("historyUnknownDappName"),T=h.displayName||t("richTransactionDetailUnknownNFT");return{topLeft:{text:u?t("transactionsBidFailed"):t("transactionsBidOnInterpolated",{name:T})},bottomLeft:{text:A},start:w}}case"COLLECTIBLE_UNLIST":{let{dapp:y,item:h}=s,A=y?y.displayName:t("historyUnknownDappName"),T=h.displayName||t("richTransactionDetailUnknownNFT");return{topLeft:{text:u?t("transactionsUnlistedFailed"):t("transactionsUnlistedInterpolated",{name:T})},bottomLeft:{text:A},start:w}}case"COLLECTIBLE_SELL_ITEM":{let{dapp:y,itemAmount:h,item:A,forAsset:T,forAmount:I}=s,v=y?y.displayName:t("historyUnknownDappName"),x=T.decimals!==void 0?`+${Ce(I,T.decimals)} ${It(T.symbol||"",6)}`:t("transactionsUnknownAmount"),E=A.symbol||mn(qe(A.id)),P=h&&A.tokenType==="SemiFungible"?`-${h} ${E}`:`-${E}`;return{topLeft:{text:t(u?"transactionsSoldFailed":"transactionsSoldCollectible")},topRight:{text:u?"":`${x}`,color:"accentSuccess"},bottomRight:{text:u?"":P,color:"textPrimary"},bottomLeft:{text:v},start:w}}case"CANCEL_TX":return{topLeft:{text:t(u?"transactionCancelledFailed":"transactionCancelled")},start:w};case"TOKEN_APPROVAL":{let{dapp:y,token:h,isApproved:A}=s,T=wn(h.tokenType)?h.displayName:h.symbol,I;return u?A?wn(h.tokenType)?I=t("transactionApproveTokenFailed",{tokenSymbol:T}):I=t("transactionApprovalFailed"):wn(h.tokenType)?I=t("transactionRevokeApproveTokenFailed",{tokenSymbol:T}):I=t("transactionRevokeFailed"):A?I=t("transactionApproveToken",{tokenSymbol:T}):I=t("transactionRevokeApproveToken",{tokenSymbol:T}),{topLeft:{text:I},bottomLeft:{text:y?y.displayName:S,color:"textTertiary"},start:w}}case"COLLECTION_APPROVAL":{let{collection:y,isApproved:h,dapp:A}=s,T=y.displayName,I;return u?h?I=t("transactionApproveTokenFailed",{tokenSymbol:T}):I=t("transactionRevokeApproveTokenFailed",{tokenSymbol:T}):h?I=t("transactionApproveToken",{tokenSymbol:T}):I=t("transactionRevokeApproveToken",{tokenSymbol:T}),{topLeft:{text:I},bottomLeft:{text:A?A.displayName:S,color:"textTertiary"},start:w}}case"CANCEL_ORDER":{let{dapp:y}=s;return{topLeft:{text:t(u?"transactionCancelOrderFailed":"transactionCancelOrder")},bottomLeft:{text:y?.displayName||t("historyUnknownDappName"),color:"textTertiary"},start:w}}case"UNCLASSIFIED_APP_INTERACTION":{let{dapp:y}=s,h=y?y.displayName:S;return{topLeft:{text:t(u?"transactionsFailedAppInteraction":"transactionsAppInteraction")},bottomLeft:{text:h},start:w}}}},IA=(e,t)=>{let{display:r,type:o}=e,{topLeft:n,topRight:s,bottomLeft:p,bottomRight:c}=r.summary,u=xp.default.createElement(t,{logoUri:r.summary.logoUri,pendingTransactionType:o,size:"small"});return{topLeft:n,topRight:s,bottomLeft:p,bottomRight:c,start:u}},Ep=(e,t,r)=>{let o=(e??[]).map(Wl);if(t){let n=o.find(s=>s.chainId===t)?.address;if(n){let s={chainId:t,address:n};r&&(s.filters=[{type:"token",value:r}]),o=[s]}}return o},wn=e=>{switch(e){case"SemiFungible":case"NonFungible":return!0;default:return!1}},md=(e,t)=>{let r=qe(e.to),o=qe(e.from),n=t.some(p=>p.address.toLowerCase()===r.toLowerCase()),s=t.some(p=>p.address.toLowerCase()===o.toLowerCase());return n&&!s},vp=e=>{switch(e?.type){case"BRC20":return e.data.firstCreatedInscriptionId;case"ERC20":return e.data.contractAddress;case"SPL":return e.data.splTokenAccountPubkey;case"CompressedSPL":return e.data.mintAddress;default:return}},xA=(e,t)=>{let r=qe(e.id);return TA.includes(r)&&(e.tokenType="Native"),e.tokenType==="Native"?{chainId:t,slip44:F.getSlip44(t),resourceType:"nativeToken"}:{chainId:t,address:r,resourceType:"address"}};i();a();i();a();i();a();var ir={showTimeToMineRowInPendingTransactionDetail:!1,showFeeRowInPendingTransactionDetail:!0};var dd={...ir};i();a();var fd={showTimeToMineRowInPendingTransactionDetail:!1,showFeeRowInPendingTransactionDetail:!1};i();a();var gd={...ir,showTimeToMineRowInPendingTransactionDetail:!0};i();a();var yd={...ir};i();a();var Sd={...ir};i();a();var wd={showTimeToMineRowInPendingTransactionDetail:!1,showFeeRowInPendingTransactionDetail:!1};var EA=new So({solana:wd,ethereum:gd,polygon:Sd,monad:yd,base:dd,bitcoin:fd,sui:{}}),hd=yo(EA);i();a();i();a();i();a();var Id=C(R());i();a();var Td=C(R());i();a();var vA=10,CA="/history/v2",Io=class extends Error{constructor(){super(...arguments);this.message="406: Cache bust!"}},hn=class extends Error{constructor(){super(...arguments);this.message="Server response malformed."}},vs=class extends Error{},Cs=class extends Error{},Cp=l.object({next:l.union([l.null(),l.string()]),results:l.array(xs)}),Ad=async e=>{let t=await U.api().retry(3,2e3).post(`${CA}`,e);if(t.status===406)throw new Io;if(t.status>=400&&t.status<500)throw new Cs(`FetchActivityItem: Status code: ${t.status}`);if(t.status>=500)throw new vs(`FetchActivityItem: Status code: ${t.status}`);return t.data},bd=e=>!Array.isArray(e.results)||typeof e.next!="string"&&e.next!==null,Rs=async e=>{let t=await Ad(e);if(bd(t))throw new hn;for(;t.results?.length<vA&&t.next;){if(bd(t))throw new hn;let r=await Ad({next:t.next,isSpam:e.isSpam});t.next=r.next,t.results=[...t.results,...r.results]}try{return Cp.parse(t)}catch(r){let o=r,n=[],s=o.issues;for(let u=0;u<s.length;++u){let m=s[u].path;if(m[0]!=="results")throw Q.captureError(o,"history"),o;let f=m[1];if(typeof f!="number")throw Q.captureError(o,"history"),o;n.push(f)}n.sort(function(u,m){return m-u});let p=n.map(u=>t.results[u]).map(u=>u.interactionData?.transactionType||""),c=[];for(let u=0;u<n.length;++u){let m=n[u],f=t.results[m],g={timestamp:f?.timestamp,owner:f?.owner,id:f?.id,chainMeta:f?.chainMeta,interactionData:{transactionType:"UNCLASSIFIED_APP_INTERACTION",balanceChanges:[]}};try{xs.parse(g),t.results[m]=g,c.push(m)}catch{}}for(let u=0;u<n.length;++u){let m=n[u];c.indexOf(m)===-1&&t.results.splice(m,1)}n.length&&console.error(`Removed history items, ${p.join(",")}`);try{return Cp.parse(t)}catch(u){let m=u;throw Q.captureError(m,"history"),m}}};var An="fetch-infinite-activity-items",Fd=({accounts:e,isSpam:t})=>[An,e,t],RA=(e,t)=>{e.removeQueries({queryKey:t})},Rp=({accounts:e,isSpam:t})=>{let r=G();(0,Td.useEffect)(()=>()=>{r.cancelQueries({queryKey:[An],exact:!1})},[r]);let o=Fd({accounts:e,isSpam:t});return er({queryKey:o,queryFn:async({pageParam:p})=>{try{let c;return p&&p!==""?c=await Rs({next:p,isSpam:t}):c=await Rs({accounts:e||[],isSpam:t}),c}catch(c){c instanceof Io&&RA(r,o),Q.captureError(c,"history")}},getNextPageParam:p=>p?.next,staleTime:1/0,retry:!1,enabled:!!e?.length&&e.every(p=>!F.isLocalNetworkID(p.chainId)),initialPageParam:""})};var PA=({fungible:e,isSpam:t=!1})=>{let{data:r=vl,status:o}=Ul(),n=r.isDeveloperMode,s=vp(e),{data:p}=vt({address:e?.data.walletAddress,networkID:e?.data.chain.id}),c=(0,Id.useMemo)(()=>Ep(p?[p]:[],e?.data.chain.id,s),[p,e?.data.chain.id,s]),u=Rp({accounts:c,isSpam:t}),m=u.isPending||o==="pending";return{activityItemsData:u,isLoadingHistory:m,isDeveloperMode:n}};i();a();var ei=C(R());i();a();var zf=C(sf()),jf=C(Mf());var JF=l.object({domain:l.string(),url:l.string(),name:l.string(),category:l.string(),imageUrl:l.string()}),Wf=l.object({uuid:l.string(),data:l.array(JF)}),ZF=l.object({chainId:or,address:l.string(),name:l.string(),symbol:l.string(),logoUrl:l.string(),marketCap:l.number(),price:l.number(),priceChange:l.number(),volume:l.number(),volumeChange:l.number()}),Yf=l.object({items:l.array(ZF)}),eI=l.object({paymentTokenId:l.string(),name:l.string(),symbol:l.string(),decimals:l.number()}),tI=l.object({verified:l.boolean(),collectionUrl:l.string(),marketplaceId:l.string(),marketplaceName:l.string(),marketplaceCollectionId:l.string()}),rI=l.object({marketplaceId:l.string(),paymentToken:eI,value:l.string()}),oI=l.object({id:l.string(),name:l.string(),volume:l.string().optional(),volumePercentChange:l.number(),marketplacePages:l.array(tI),floorPrices:l.array(rI),imageUrl:l.string()}),Xf=l.object({uuid:l.string(),data:l.array(oI)}),nI=["tokens","sites","collections","quests","learn"],Gr=l.enum(["shortcuts","carousel",...nI]),sI=l.object({type:l.literal(Gr.enum.shortcuts)}),iI=l.object({type:l.literal(Gr.enum.carousel)}),Pn="all_networks",Bo=l.union([or,l.literal(Pn)]),aI=["asc","desc"],pI=l.enum(aI),cI=["rank","volume","price","price-change","market-cap"],qf=l.enum(cI),uI=["1h","24h","7d","30d"],Gf=l.enum(uI),lI=l.object({type:l.literal(Gr.enum.tokens),sortBy:l.array(qf).refine(e=>new Set(e).size===e.length),sortByDefault:qf,sortDirectionDefault:pI,network:l.array(Bo).refine(e=>new Set(e).size===e.length),networkDefault:Bo,timeFrame:l.array(Gf).refine(e=>new Set(e).size===e.length),timeFrameDefault:Gf,rankAlgo:l.string(),limit:l.number(),listLimit:l.number()}),mI=["top","rank"],Vf=l.enum(mI),dI=["24h","7d","30d"],Kf=l.enum(dI),fI=l.object({type:l.literal(Gr.enum.sites),sortBy:l.array(Vf).refine(e=>new Set(e).size===e.length),sortByDefault:Vf,network:l.array(Bo).refine(e=>new Set(e).size===e.length),networkDefault:Bo,timeFrame:l.array(Kf).refine(e=>new Set(e).size===e.length),timeFrameDefault:Kf,rankAlgo:l.string(),limit:l.number(),listLimit:l.number()}),gI=["top","rank"],Hf=l.enum(gI),yI=["24h","7d","30d"],$f=l.enum(yI),SI=l.object({type:l.literal(Gr.enum.collections),sortBy:l.array(Hf).refine(e=>new Set(e).size===e.length),sortByDefault:Hf,network:l.array(Bo).refine(e=>new Set(e).size===e.length),networkDefault:Bo,timeFrame:l.array($f).refine(e=>new Set(e).size===e.length),timeFrameDefault:$f,rankAlgo:l.string(),limit:l.number(),listLimit:l.number()}),wI=l.object({type:l.literal(Gr.enum.learn),limit:l.number()}),hI=l.object({type:l.literal(Gr.enum.quests),limit:l.number()}),AI=l.discriminatedUnion("type",[sI,iI,lI,fI,SI,wI,hI]),oc=l.object({sections:l.array(AI).refine(e=>(0,zf.default)(e,(0,jf.default)(e,"type")))}),NB=l.object({uuid:l.string(),data:l.array(rl)});var bI=oc.parse({sections:[{type:"shortcuts"},{type:"carousel"},{type:"tokens",sortBy:["rank","volume","price","price-change","market-cap"],sortByDefault:"rank",sortDirectionDefault:"asc",network:["solana:101","eip155:1","eip155:137","all_networks"],networkDefault:"solana:101",timeFrame:["1h","24h","7d","30d"],timeFrameDefault:"24h",rankAlgo:"default",limit:3,listLimit:100},{type:"sites",sortBy:["top","rank"],sortByDefault:"top",network:["solana:101","eip155:1","eip155:137","bip122:000000000019d6689c085ae165831e93","all_networks"],networkDefault:"solana:101",timeFrame:["24h","7d","30d"],timeFrameDefault:"24h",rankAlgo:"default",limit:3,listLimit:50},{type:"collections",sortBy:["rank","top"],sortByDefault:"rank",network:["solana:101","eip155:1","eip155:137","bip122:000000000019d6689c085ae165831e93","all_networks"],networkDefault:"solana:101",timeFrame:["24h","7d","30d"],timeFrameDefault:"24h",rankAlgo:"default",limit:3,listLimit:50},{type:"learn",limit:3},{type:"quests",limit:3}]}),nc=()=>{let e=Qe.getMultivariateAssignment("explore-config"),{data:t}=ym();return(0,ei.useMemo)(()=>{let r;try{r=oc.parse(JSON.parse(e??""))}catch{r=bI}return t?r:{...r,sections:r.sections.filter(o=>o.type!=="quests")}},[e,t])},sc=()=>{let e=nc();return(0,ei.useMemo)(()=>{let t=[];for(let r of e.sections)(r.type==="collections"||r.type==="tokens"||r.type==="sites"||r.type==="learn"||r.type==="quests")&&t.push(r);return t},[e.sections])},ic=e=>{let t=nc();return(0,ei.useMemo)(()=>t.sections.find(o=>o.type===e)??null,[t.sections,e])};i();a();i();a();i();a();i();a();var _t=L({days:1}),Qt=L({seconds:5});i();a();i();a();var ot="@phantom/phantom-network",ke={actor(e){return["actor",`${ot}:actor`,{profileId:e}]},actorAndFollowers(e){return["user",`${ot}:actor-and-followers`,{profileId:e}]},actorAndFollowing(e){return["user",`${ot}:actor-and-following`,{profileId:e}]},userProfileByUsernameOrUserProfileId(e){return["user",`${ot}:user-profile-by-username-or-user-profile-id`,{usernameOrUserProfileId:e}]},followUser(e,t){return["user",`${ot}:follow-user`,{currentId:e,targetId:t}]},inbox(e){return["user",`${ot}:inbox`,{profileId:e}]},searchForActorBase(){return["actor",`${ot}:search`]},searchForActor(e,t){return["actor",`${ot}:search`,{query:e,enableSorting:t}]},followRequests(e){return["user",`${ot}:follow-requests`,{profileId:e}]},blocking(e){return["user",`${ot}:blocking`,{profileId:e}]},pendingFollowRequest(e,t){return["user",`${ot}:pending-follow-request`,{profileId:e,targetId:t}]}};i();a();i();a();var nt=e=>{let t=Zt();return Cl.parse(`${t}/v1/actors/${e}`)};i();a();i();a();function lr(e){let t=e.match(/\/v1\/actors\/([^/]+)/)?.[1];return t?Oe.parse(t):void 0}i();a();function Se(e){return lr(e)??Oe.parse(e)}var Lo=(e,t)=>{let r=Se(t.id);r&&e.setQueryData(ke.actor(r),t)};function ac({query:e,enabled:t,enableSorting:r=!1}){let o=G(),{data:n}=Ge(),{data:s}=dt(),p=n?.accessToken,c=s?.id?nt(s.id):void 0,u=e?.startsWith("@")?e.slice(1).trim():e.trim(),m=Pl(),f=ke.searchForActor(u,r),g=async()=>{if(!u)return{actors:[]};let{actors:y}=await m.searchForActor(u,p);for(let A of y)Lo(o,A);let h=y.map(A=>{let T=!1,I=!1;return c&&(typeof A.followers=="string"?I=!1:I=(A.followers.current?.orderedItems||[]).find(x=>x.includes(c))!==void 0,typeof A.following=="string"?T=!1:T=(A.following.current?.orderedItems||[]).find(x=>x.includes(c))!==void 0),{followsYou:T,followedByYou:I,...A}});return r&&h.sort((A,T)=>A.followedByYou?-1:T.followedByYou?1:0),{actors:h}},S=t&&!!p;return{...B({enabled:S,queryKey:f,queryFn:g,gcTime:_t,staleTime:Qt}),enabled:S}}i();a();i();a();i();a();i();a();var TI=l.object({orderedItems:wo.array(),next:l.string().optional()}),FI=l.object({orderedItems:wo.array(),next:l.string().optional()});var II=l.object({orderedItems:wo.array(),next:l.string().optional()}),xI=l.object({orderedItems:wo.array(),next:l.string().optional()});i();a();i();a();i();a();i();a();i();a();i();a();i();a();var Vr=go($,e=>new pc(e)),pc=class{#e;constructor(t){this.#e=t}profileFollowRequestAcceptedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileFollowRequestAcceptedByUser",{data:{targetId:t,targetProfileId:o,actorId:r,actorProfileId:n,type:"followRequestAccepted"}})}profileFollowRequestDeniedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileFollowRequestDeniedByUser",{data:{targetId:t,targetProfileId:o,actorId:r,actorProfileId:n,type:"followRequestDenied"}})}profileUnfollowedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileUnfollowedByUser",{data:{targetId:t,targetProfileId:o,actorId:r,actorProfileId:n,type:"unfollow"}})}profileFollowedByUser(t,r,o){let n=Se(t),s=Se(r);this.#e.capture("profileFollowedByUser",{data:{targetId:t,targetProfileId:n,actorId:r,actorProfileId:s,type:o?"followRequest":"follow"}})}profileReportedByUser(t,r,o){let n=Se(t),s=Se(r);this.#e.capture("profileReportedByUser",{data:{targetId:t,targetProfileId:n,actorId:r,actorProfileId:s,type:"report",...o}})}profileUnblockedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileUnblockedByUser",{data:{targetId:t,targetProfileId:o,actorId:r,actorProfileId:n,type:"unblock"}})}profileBlockedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileBlockedByUser",{data:{targetId:t,targetProfileId:o,actorId:r,actorProfileId:n,type:"block"}})}profileViewedByUser(t,r,o){let n=Se(t),s=Se(r);this.#e.capture("profileViewedByUser",{data:{targetId:r,targetProfileId:s,actorId:t,actorProfileId:n,isFollowing:o,type:"view"}})}profileFollowersViewedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileFollowersViewedByUser",{data:{targetId:r,targetProfileId:n,actorId:t,actorProfileId:o,type:"viewFollowers"}})}profileFollowingViewedByUser(t,r){let o=Se(t),n=Se(r);this.#e.capture("profileFollowingViewedByUser",{data:{targetId:r,targetProfileId:n,actorId:t,actorProfileId:o,type:"viewFollowing"}})}};i();a();i();a();i();a();i();a();i();a();i();a();i();a();i();a();i();a();i();a();i();a();i();a();i();a();var ti="en-US",T1=new Intl.NumberFormat(ti,{maximumFractionDigits:0}),F1=new Intl.NumberFormat(ti,{notation:"compact",compactDisplay:"short",maximumFractionDigits:1,minimumFractionDigits:1}),I1=new Intl.NumberFormat(ti,{notation:"compact",compactDisplay:"short",maximumFractionDigits:0,minimumFractionDigits:0}),x1=new Intl.NumberFormat(ti,{notation:"compact",compactDisplay:"short",maximumFractionDigits:1,minimumFractionDigits:1});i();a();i();a();i();a();i();a();i();a();i();a();i();a();var CI=l.object({type:l.literal("fungible"),data:sm}),eg=l.object({results:l.array(l.any()).transform(e=>e.reduce((t,r)=>{let o=CI.safeParse(r);return o.success&&t.push(o.data),t},[])),prevCursor:l.string().nullish().default(null),nextCursor:l.string().nullish().default(null),hasMore:l.boolean()});async function tg(e){let{query:t,networkIds:r,searchContext:o,supportedNetworkIds:n}=e,s=await U.api().headers({"Content-Type":"application/json"}).get("/search/v1",{params:{query:t.trim(),chainIds:r.join(","),platform:Ae,pageSize:"50",searchTypes:"fungible",searchContext:o,...n&&{supportedNetworkIds:n.join(",")}}});if(!se(s))throw new Re({key:"UnknownError"});let p=eg.safeParse(s.data);if(p.success)return p.data;throw new Re({key:"UnknownError"})}i();a();var lc=e=>{let t=e.data,r=qa(t)??void 0;return{type:t.type,data:{...t.data,tokenAddress:qa(t)??void 0,key:r}}};i();a();var RI="search",rg={searchResults:(e,t,r,o)=>[RI,{query:e,networkIds:t,supportedNetworkIds:o,chainAddresses:r}]};i();a();var mc=(s=>(s[s.Always=1/0]="Always",s[s.Short=L({minutes:5})]="Short",s[s.Medium=L({minutes:30})]="Medium",s[s.Long=L({hours:1})]="Long",s[s.Never=0]="Never",s))(mc||{}),dc=(n=>(n[n.Immediate=0]="Immediate",n[n.Short=L({minutes:5})]="Short",n[n.Medium=L({minutes:30})]="Medium",n[n.Long=L({hours:1})]="Long",n))(dc||{});var PI=async({query:e,networkIds:t,searchContext:r,supportedNetworkIds:o})=>(await tg({query:e,networkIds:t,searchContext:r,supportedNetworkIds:o})).results.map(s=>lc(s)),Kr=({query:e,networkIds:t,enableQuery:r=!0,keepPreviousData:o=!1,searchContext:n,supportedNetworkIds:s,chainAddresses:p})=>{let c=rg.searchResults(e,t,p,s);return B({queryKey:c,gcTime:mc.Short,staleTime:dc.Short,enabled:r,placeholderData:o?al:void 0,queryFn:()=>PI({query:e,networkIds:t,searchContext:n,supportedNetworkIds:s})})};var Lc=C(Lg()),xe=C(R());i();a();i();a();i();a();var Ug={filterEnabled:e=>e,tokenSearchEnabled:e=>e};i();a();var _g={filterEnabled:()=>!0,tokenSearchEnabled:()=>!0};i();a();var Qg={filterEnabled:()=>!0,tokenSearchEnabled:()=>!0};i();a();var Og={filterEnabled:()=>!0,tokenSearchEnabled:()=>!0};i();a();var Mg={filterEnabled:()=>!0,tokenSearchEnabled:()=>!0};i();a();var qg={filterEnabled:()=>!0,tokenSearchEnabled:()=>!0};var FE=new So({solana:qg,ethereum:Qg,polygon:Mg,base:Ug,bitcoin:_g,monad:Og,sui:{}}),Rc=yo(FE);i();a();var Gg={fields:["domain","name","category","tags"],storeFields:["domain","name","category","tags","imageUrl"],searchOptions:{boost:{name:2},prefix:!0,fuzzy:e=>e.length>3?.2:!1}};i();a();var Si=e=>["explore-dapp",e];var Pc=e=>e.addresses.map(t=>F.getMainnetNetworkID(t.networkID))??[];function Vg(e){try{return Dr.get(e).nonNativeFungibleTokenType}catch{return"SolanaNative"}}i();a();i();a();var ht="@phantom/explore",Ie={RecentlyViewedSearchItems:[ht,"RecentlyViewedSearchItems"],sitesV2({sortBy:e,timeFrame:t,networkIDs:r,limit:o,locale:n,rankAlgo:s}){return[ht,"sitesV2",le,n,{sortBy:e,timeFrame:t,networkIDs:r,rankAlgo:s,limit:o}]},recommendedSites(e,t){return[ht,"sites","recommended",le,t,e]},tokensV2({sortBy:e,sortDirection:t,timeFrame:r,networkIDs:o,locale:n,limit:s,rankAlgo:p}){return[ht,"tokensV2",n,{sortBy:e,sortDirection:t,timeFrame:r,networkIDs:o,rankAlgo:p,limit:s}]},collectionsV2({sortBy:e,timeFrame:t,networkIDs:r,limit:o,locale:n,rankAlgo:s}){return[ht,"collectionsV2",le,n,{sortBy:e,timeFrame:t,networkIDs:r,rankAlgo:s,limit:o}]},featuredTransactions(e,t){return[ht,"transactions","featured",le,t,e]},learn(e,t){return[ht,"learn",le,t,e]}};var IE=10,Hg="RecentlyViewedSearchItems";async function xE(e){return await e.get(Hg)??[]}async function $g(e,t){await e.set(Hg,t)}function kc(e){return B({queryKey:Ie.RecentlyViewedSearchItems,queryFn:async()=>xE(e),staleTime:0,enabled:!0})}function EE(e){let t=G();return ie({mutationFn:async o=>{let n=Kg(o),p=(t.getQueryData(Ie.RecentlyViewedSearchItems)??[]).filter(c=>Kg(c)!==n);p.unshift(o),p=p.slice(0,IE),await $g(e,p),t.setQueryData(Ie.RecentlyViewedSearchItems,p),await t.refetchQueries({queryKey:Ie.RecentlyViewedSearchItems})}})}function vE(e){let t=G();return ie({mutationFn:async()=>{await $g(e,[]),t.setQueryData(Ie.RecentlyViewedSearchItems,[]),await t.refetchQueries({queryKey:Ie.RecentlyViewedSearchItems})}})}function Kg(e){return e.type==="token"?oe(q(e.data)):e.data.id}i();a();var CE=e=>{let t={isRecommended:"true"};return e.networkIds&&(t.chainIds=e.networkIds.join(",")),`/dapps/v1?${new URLSearchParams(t).toString()}`},RE=async e=>{let t=await U.api().get(CE(e));if(t.status===500)throw new Error("Error fetching recommended dapps from registry");return(await t.data).data},Dc=e=>{let t=G();return B({queryKey:["dapps","recommended",e],queryFn:async()=>{try{let o=await RE(e);return ss.batch(()=>{for(let n of o??[]){let s=Si(n.domain),p=t.getQueryState(s);t.setQueryData(s,()=>n??p?.data??null)}}),o}catch(o){throw Q.captureError(o,"dappRegistry"),o}},gcTime:L({days:1}),staleTime:L({days:1})})};i();a();i();a();var zg=async({sortBy:e,sortDirection:t,timeFrame:r,networkIDs:o,locale:n,limit:s,rankAlgo:p})=>{let c=new URLSearchParams({timeFrame:r,sortBy:e,sortDirection:t,limit:s.toString(),rankAlgo:p,platform:Ae,locale:n,appVersion:le});o.forEach(f=>{c.append("chainIds[]",f)});let u=await U.api().get("/explore/v2/trending-tokens",{params:c});if(!se(u))throw new Error("Failed to fetch tokens");let m=Yf.safeParse(u.data);if(!m.success)throw new Error("Failed to parse tokens");return m.data};i();a();var Go=(e,t)=>{let r=fs(),n=sc().find(p=>p.type===t),s=[];return n&&n?.type!=="learn"&&n?.type!=="quests"?n.network.forEach(p=>{p!==Pn&&r.includes(p)&&s.push(p)}):s=r,e!==Pn&&!s.includes(e)?[]:e===Pn?s:[e]};var PE=L({minutes:1}),kE=L({minutes:1}),DE=100,Nc=({sortBy:e,sortDirection:t,timeFrame:r,network:o,limit:n,rankAlgo:s,enableQuery:p=!0})=>{let{data:c}=te(),u=Go(o,"tokens"),m=DE,f=Te(D.language)??"en";return B({queryKey:Ie.tokensV2({sortBy:e,sortDirection:t,timeFrame:r,networkIDs:u,locale:f,limit:m,rankAlgo:s}),enabled:!!c&&p,async queryFn(){if(!c)throw new Error("No account found");return zg({sortBy:e,sortDirection:t,timeFrame:r,networkIDs:u,locale:f,limit:m,rankAlgo:s})},gcTime:kE,staleTime:PE,retry:3,select:g=>n>=m?g:{...g,items:g.items.slice(0,n)}})};var Bn=3,BE=15,Bc=50,jg={VERIFIED:1,NOT_VERIFIED:2,POSSIBLE_SPAM:3,SPAM:4},LE=(e,t=100)=>{let[r,o]=(0,xe.useState)(!1);return(0,xe.useEffect)(()=>{let n;return e?n=setTimeout(()=>o(!0),t):o(!1),()=>clearTimeout(n)},[e,t]),r},UE=e=>{let t=Vg(e.chainId),r=F.getChainDefinition(e.chainId),o={name:e.name,symbol:e.symbol,logoUri:e.logoUrl,chain:{id:e.chainId,symbol:r.tokenSymbol,name:r.name,imageUrl:r.iconUrl}};switch(t){case"SPL":return{type:t,data:{...o,mintAddress:e.address}};case"ERC20":return{type:t,data:{...o,contractAddress:e.address}};case"BRC20":return{type:t,data:{...o,firstCreatedInscriptionId:e.address}};default:return null}},_E=e=>e.reduce((t,r)=>{let o=UE(r);return o&&t.push(o),t},[]),QE=({searchQuery:e,networkIds:t,minSearchResults:r,excludePlaceholderTokens:o=!1,extraDapps:n=[]})=>{let{data:[s,p]}=ae(["enable-social-profiles","kill-base-explore-search"]),[c,u]=(0,xe.useState)(!1),[m,f]=(0,xe.useState)(!1),[g,S]=(0,xe.useState)(!1),w=t.filter(_e=>Rc.get(_e).tokenSearchEnabled(!p));(0,xe.useEffect)(()=>{u(!1),f(!1),S(!1)},[e]);let{data:y=[],isPending:h}=Kr({query:e,networkIds:w,searchContext:"explore",enableQuery:!!e}),A=ic("tokens"),{data:T}=Nc({sortBy:A?.sortByDefault??"rank",sortDirection:A?.sortDirectionDefault??"asc",timeFrame:A?.timeFrameDefault??"24h",network:A?.networkDefault??"solana:101",limit:10,rankAlgo:A?.rankAlgo??"default",enableQuery:!e&&!o}),I=(0,xe.useMemo)(()=>y.sort((_e,un)=>jg[_e.data.spamStatus??"NOT_VERIFIED"]-jg[un.data.spamStatus??"NOT_VERIFIED"]),[y]),v=(0,xe.useMemo)(()=>T?.items?_E(T.items):[],[T?.items]),x=r??(e.length===0?BE:Bn),E=(0,xe.useMemo)(()=>I.slice(0,m?Bc:x),[m,I,x]),P=(0,xe.useMemo)(()=>[...I.map(_e=>oe(q(_e))),...v.map(_e=>oe(q(_e)))],[I,v]),{data:k={},isPending:H}=gt({query:{data:P},enabled:P.length>0}),{data:j=[],isPending:M}=Dc({networkIds:w}),X=(0,xe.useMemo)(()=>{let _e=[...j,...n],un=(0,Lc.default)(_e,zh=>as(zh.domain));return Object.values(un)},[j,n]),Y=as(e),ye=Sl(X,Y,Gg),z=(0,Lc.default)(ye,_e=>as(_e.domain)),J=Object.values(z),ce=J.slice(0,c?Bc:Bn),{data:be,isPending:_,enabled:ct}=ac({query:e,enabled:s}),Xt=s?ct&&_:!1,O=!!e,ve=M||h||Xt,rs=O&&!ve,re=ee(),{data:He}=kc(re),{actorsAll:ut,actors:Jt}=(0,xe.useMemo)(()=>{let _e=be?.actors??[],un=_e.slice(0,g?Bc:Bn);return{actorsAll:_e,actors:un}},[g,be?.actors]),cn=(0,xe.useMemo)(()=>({actors:Jt,dapps:j,searchedDapps:ce,renderShowMoreDappsButton:O&&J.length>Bn,isLoading:ve,tokens:E,trendingTokens:v,renderShowMoreTokensButton:O&&I.length>x,renderShowMoreUsersButton:O&&ut.length>Bn,priceMap:k,isPriceMapLoading:H,showSearchResults:rs,showMoreTokens:m,setShowMoreTokens:f,showMoreDapps:c,setShowMoreDapps:u,showMoreUsers:g,setShowMoreUsers:S,recentlyViewedSearchItems:He}),[Jt,j,ce,O,J.length,ve,E,v,I.length,x,ut.length,k,H,rs,m,c,g,He]),[tl,Hh]=(0,xe.useState)(null),$h=LE(ve);return(0,xe.useEffect)(()=>{cn.isLoading||Hh(e?cn:null)},[cn.isLoading,e]),{...cn.isLoading&&tl?tl:cn,showSpinner:$h&&O}};i();a();i();a();var OE=e=>`/dapps/v1?domains=${e.join(",")}`,ME=async e=>{let t=await U.api().get(OE(e));if(t.status===500)throw new Error("Error fetching recommended dapps from registry");return t.data.data},qE=e=>{let t=G(),r=e!=null&&Array.isArray(e)&&e.length>0&&e.filter(n=>n!=null&&n!="").length>0;return B({queryKey:["dapps",e],enabled:r,queryFn:async()=>{try{let n=await ME(e);return ss.batch(()=>{for(let s of n??[]){let p=Si(s.domain),c=t.getQueryState(p);t.setQueryData(p,()=>s??c?.data??null)}}),n}catch(n){throw Q.captureError(n,"dappRegistry"),n}},gcTime:L({days:1}),staleTime:L({days:1})})};i();a();var Yg=C(R());i();a();var Wg=async(e,t,r,o)=>{let n=r?.addresses.map(Et)??[],s=r?.type==="seed"?r.seedIdentifier:null,p=r?.identifier??"",c=new URLSearchParams({accountId:p,appVersion:le,deviceId:o,limit:"6",locale:t,platform:Ae,timeframe:"24h"});e.forEach(f=>{c.append("chainIds[]",f)}),s&&c.append("rootId",s),n.forEach(f=>{c.append("selectedAccountAddresses[]",f.toString())});let u=await U.api().get("/explore/v1/featured-dapps",{params:c});if(!se(u))throw new Error("Failed to fetch sites");return await u.data};var GE=L({minutes:10}),VE=L({minutes:10}),KE=()=>{let{data:e}=te(),t=(0,Yg.useMemo)(()=>e?Pc(e):[],[e]),r=Te(D.language)??"en",o=$(),n=async()=>{if(!e)throw new Error("No account found");let s=await o.getDeviceId();return Wg(t,r,e,s)};return B({queryKey:Ie.recommendedSites(t,r),enabled:!!e,queryFn:n,gcTime:GE,staleTime:VE})};i();a();var HE=C(R());i();a();var sG=L({minutes:10}),iG=L({minutes:10});i();a();i();a();var $E={rank:"trending",top:"top"},Xg=async({sortBy:e,timeFrame:t,networkIDs:r,limit:o,locale:n,account:s,deviceId:p,rankAlgo:c})=>{let u=s?.addresses.map(Et)??[],m=s?.type==="seed"?s.seedIdentifier:null,f=s?.identifier??"",g=new URLSearchParams({accountId:f,appVersion:le,deviceId:p,limit:o.toString(),locale:n,platform:Ae,rankAlgo:c,rankBy:$E[e],timeframe:t});r.forEach(y=>{g.append("chainIds[]",y)}),m&&g.append("rootId",m),u.forEach(y=>{g.append("selectedAccountAddresses[]",y.toString())});let S=await U.api().get("/explore/v1/trending-dapps",{params:g});if(!se(S))throw new Error("Failed to fetch sites");let w=Wf.safeParse(S.data);if(!w.success)throw new Error("Failed to parse sites");return w.data};var zE=L({minutes:10}),jE=L({minutes:10}),WE=({sortBy:e,timeFrame:t,network:r,limit:o,rankAlgo:n})=>{let{data:s}=te(),p=Go(r,"sites"),c=Te(D.language)??"en",u=$();return B({queryKey:Ie.sitesV2({sortBy:e,timeFrame:t,networkIDs:p,locale:c,limit:o,rankAlgo:n}),enabled:!!s,async queryFn(){if(!s)throw new Error("No account found");let m=await u.getDeviceId();return Xg({sortBy:e,timeFrame:t,networkIDs:p,locale:c,account:s,deviceId:m,limit:o,rankAlgo:n})},gcTime:jE,staleTime:zE,retry:3})};i();a();i();a();var YE={rank:"trending",top:"volume"},XE=e=>e.floorPrices.length>0&&e.marketplacePages.length>0,Jg=async({sortBy:e,timeFrame:t,networkIDs:r,limit:o,locale:n,account:s,deviceId:p,rankAlgo:c})=>{let u=s?.addresses.map(Et)??[],m=s?.type==="seed"?s.seedIdentifier:null,f=s?.identifier??"",g=new URLSearchParams({accountId:f,appVersion:le,deviceId:p,limit:o.toString(),locale:n,platform:Ae,rankAlgo:c,rankBy:YE[e],timeframe:t});r.forEach(y=>{g.append("chainIds[]",y.toString())}),m&&g.append("rootId",m),u.forEach(y=>{g.append("selectedAccountAddresses[]",y.toString())});let S=await U.api().get("/explore/v1/trending-collections",{params:g});if(!se(S))throw new Error("Failed to fetch collections");let w=Xf.safeParse(S.data);if(!w.success)throw new Error("Failed to parse collections");return{...w.data,data:w.data.data.filter(XE)}};var JE=L({minutes:10}),ZE=L({minutes:10}),ev=({sortBy:e,timeFrame:t,network:r,limit:o,rankAlgo:n})=>{let{data:s}=te(),p=Go(r,"collections"),c=Te(D.language)??"en",u=$();return B({queryKey:Ie.collectionsV2({sortBy:e,timeFrame:t,networkIDs:p,locale:c,limit:o,rankAlgo:n}),enabled:!!s,async queryFn(){if(!s)throw new Error("No account found");let m=await u.getDeviceId();return Jg({sortBy:e,timeFrame:t,networkIDs:p,locale:c,account:s,deviceId:m,limit:o,rankAlgo:n})},gcTime:ZE,staleTime:JE,retry:3})};i();a();i();a();var Zg=async(e,t)=>{let r=new URLSearchParams({appVersion:le,locale:t,platform:Ae});e.forEach(s=>{r.append("chainIds[]",s)});let o=await U.api().get("/explore/v1/learn-grid",{params:r});if(!se(o))throw new Error("Failed to fetch learn grid");return o.data};var tv=L({minutes:10}),rv=L({minutes:10}),ov=()=>{let e=fs(),t=Te(D.language)??"en";return B({queryKey:Ie.learn(e,t),enabled:e.length>0,async queryFn(){return Zg(e,t)},gcTime:rv,staleTime:tv})};i();a();i();a();var DV={location:0,distance:50,threshold:.2,shouldSort:!0,keys:[{name:"token.name",weight:.4},{name:"token.symbol",weight:.4},{name:"token.caip19.chainId",weight:.2}]};var ey=30,NV=6,BV=29850,LV=5,ty=["100","500","1000"];i();a();i();a();i();a();var nv=l.object({name:l.string().nullish().default(null),symbol:l.string().nullish().default(null),decimals:l.number(),caip19:ze,logoURI:l.string().nullish().default(null)}),sv=l.object({token:nv,quickSelectDenominations:l.array(l.string()).optional(),addressType:Tl.optional()}),GV=l.object({groups:l.array(l.object({groupName:l.string(),tokens:l.array(sv)}))}),ry=(s=>(s.CARD="CARD",s.MOBILE_WALLET="MOBILE_WALLET",s.BANK_TRANSFER="BANK_TRANSFER",s.EXCHANGE="EXCHANGE",s.LOCAL="LOCAL",s))(ry||{}),Uc=l.object({id:l.string(),type:l.nativeEnum(ry),name:l.string(),estimatedArrival:l.string(),assets:l.object({icon:l.string(),roundIcon:l.string()}),description:l.string().optional()}),VV=l.object({paymentMethods:l.array(Uc)}),oy=l.object({id:l.string(),name:l.string(),tag:l.string(),logo:l.string(),quote:l.object({displaySourceAmount:l.string(),displayDestinationAmount:l.string(),displayFee:l.string()}).optional()}),KV=l.object({providers:l.array(oy),paymentMethod:Uc,token:ze}),HV=l.object({provider:oy,paymentMethod:Uc}),$V=l.object({url:l.string()}),At=class extends Error{constructor(t){super(t),this.name="FiatRampError"}};var ny=new At("Failed to get FiatRampTokens"),sy=async(e,t,r)=>{try{let o=await U.api().headers(e).bearer(r).post("/fiat_ramp/v2/onramp/tokens",t);if(!se(o))throw ny;return o.data}catch{throw ny}};i();a();var hi=e=>e.sort((t,r)=>F.compareNetworkID(t.token.caip19.chainId,r.token.caip19.chainId));i();a();var Ai="@phantom/fiat-ramp",bi=`${Ai}:fungibles`,iv=`${Ai}:payment-methods`,av=`${Ai}:providers`,pv=`${Ai}:recommended-quote`,gr={fiatRampTokens(e,t,r){return[bi,e,t,r]},fiatRampPaymentMethods(e,t,r){return[iv,e,t,r]},fiatRampProviders(e,t,r){return[av,e,t,r]},fiatRampRecommendedQuote(e,t,r){return[pv,e,t,r]}};var cv=L({days:7}),uv=L({hours:1}),_c=(e,t,r,o=!0,n)=>{let{data:s}=te(),p=Array.from(new Set(s?.addresses?.map(w=>w.networkID))),c=Array.from(new Set(s?.addresses?.map(w=>w.addressType))),u={chainIDs:p},m={"x-client-platform":e,"x-client-app-version":t,"x-client-locale":r},{data:f}=Ge(),g=f?.userID,S=f?.accessToken;return B({queryKey:gr.fiatRampTokens(m,u,g),queryFn:()=>sy(m,u,S),gcTime:cv,staleTime:uv,enabled:o&&p.length>0,select:w=>{let y={...w,groups:w.groups.map(h=>({...h,tokens:hi(h.tokens.filter(A=>A.addressType?c.includes(A.addressType):!0))}))};return n?n(y):y}})};i();a();i();a();var iy=e=>{let t=e?.groups.flatMap(r=>r.tokens)??[];return hi(t)};var Qc=(e,t,r,o=!0)=>_c(e,t,r,o,iy);i();a();i();a();i();a();var ay=new At("Failed to get FiatRampPaymentMethods"),py=async(e,t,r)=>{try{let o=await U.api().headers(e).bearer(r).post("/fiat_ramp/v2/onramp/payment_methods",t);if(!se(o))throw ay;return o.data}catch{throw ay}};var lv=L({days:7}),mv=L({hours:1}),dv=(e,t,r,o)=>{let n={"x-client-platform":e,"x-client-app-version":t,"x-client-locale":r},{data:s}=Ge(),p=s?.userID,c=s?.accessToken;return B({queryKey:gr.fiatRampPaymentMethods(n,o,p),queryFn:()=>py(n,o,c),gcTime:lv,staleTime:mv})};i();a();i();a();var cy=new At("Failed to get FiatRampProviders"),uy=async(e,t,r)=>{try{let o=await U.api().headers(e).bearer(r).post("/fiat_ramp/v2/onramp/providers",t);if(!se(o))throw cy;return o.data}catch{throw cy}};var fv=L({seconds:ey}),gv=L({hours:1}),yv=(e,t,r,o)=>{let n={"x-client-platform":e,"x-client-app-version":t,"x-client-locale":r},{data:s}=Ge(),p=s?.userID,c=s?.accessToken;return B({queryKey:gr.fiatRampProviders(n,o,p),queryFn:()=>uy(n,o,c),gcTime:gv,staleTime:fv})};i();a();i();a();var ly=new At("Failed to get FiatRampRecommendedQuote"),my=async(e,t,r)=>{try{let o=await U.api().headers(e).bearer(r).post("/fiat_ramp/v2/onramp/recommended_quote",t);if(!se(o))throw ly;return o.data}catch{throw ly}};var Sv=L({days:7}),wv=L({hours:1}),hv=(e,t,r,o,n=!0)=>{let s={"x-client-platform":e,"x-client-app-version":t,"x-client-locale":r},{data:p}=Ge(),c=p?.userID,u=p?.accessToken;return B({queryKey:gr.fiatRampRecommendedQuote(s,o,c),queryFn:()=>my(s,o,u),gcTime:Sv,staleTime:wv,enabled:n})};i();a();var Av=(e,t,r,o)=>{let{data:n}=te(),{data:s=[]}=Qc(e,t,r),p=s.find(u=>oe(u.token.caip19)===o),c=n?.addresses.find(u=>p?.addressType?u.addressType===p.addressType&&u.networkID===p?.token.caip19.chainId:u.networkID===p?.token.caip19.chainId)?.address??"";return p?.token.caip19.chainId&&F.isEVMNetworkID(p.token.caip19.chainId)&&(c=c.toLowerCase()),{tokenToBuy:p,destinationAddress:c,quickSelectDenominations:p?.quickSelectDenominations||ty}};i();a();i();a();i();a();i();a();i();a();var Ti=e=>{let t=[];for(let r in e.errors)Array.isArray(e.errors[r])&&t.push(e.errors[r].join(", "));return t.join(", ")};var fy=async({locale:e})=>{let t;try{let r=JSON.stringify({platform:Ae,appVersion:le,locale:e}),o=await U.api().headers({"Content-Type":"application/json"}).post(({apiEnv:s})=>`/banners/force_upgrade/${s}/v1`,r);if(o.status===400){let s=await o.data,p=Ti(s);throw new Error(p)}else if(o.status<200||o.status>299)throw new Error(`status code: ${o.status}, body: ${o.statusText}`);let n=o.data;return n?{...n}:dy}catch(r){return t===400&&r instanceof Error&&Q.captureError(r,"actionBanner"),dy}},dy={forceUpgradeStatus:"noop"};var gy=async({locale:e})=>({...await fy({locale:e})});i();a();var Un="@phantom/interstitials",yr=`${Un}:action-banners`,yy=`${Un}:force-upgrade`,Ye={actionBanners:(e,t)=>[yr,{accountIdentifier:e,accessToken:t}],hasSeenInterstitial:e=>[`${Un}:has-seen-interstitial`,{feature:e}],appLaunchTimes:()=>[`${Un}:app-launch-times`],lastOnboardedAt:()=>[`${Un}:last-onboarded-at`]};var bv=()=>B({queryKey:[yy],queryFn:async()=>{let t=Te(D.language)??"en";return gy({locale:t})},enabled:!!le,staleTime:L({minutes:5})});i();a();i();a();i();a();i();a();var Oc=(y=>(y.Buy="Buy",y.Collectibles="Collectibles",y.ConnectHardwareWallet="Connect Hardware Wallet",y.ConvertToJito="Convert to Jito",y.Explore="Explore",y.Quests="Quests",y.MintCollectible="Mint Collectible",y.SocialLogin="Social Login",y.SettingsClaimUsername="Settings: Claim Username",y.SettingsImportSeedPhrase="Settings: Import Seed Phrase",y.SettingsNotificationPreferences="Settings: Notification Preferences",y.SettingsSecurityAndPrivacy="Settings: Security & Privacy",y.Swapper="Swapper",y.ExternalLink="External Link",y))(Oc||{}),Fi=class extends Error{constructor(){super(...arguments);this.message="Failed to set dismissed action banners to storage"}};var Tv={Multichain:2,MultichainSwapper:1,InvalidChecksum:1,AdditionalPermissions:1},Fv={Multichain:3,MultichainSwapper:1,InvalidChecksum:1,AdditionalPermissions:1},Mc=e=>lt?Tv[e]:sl?Fv[e]:-1;var qc=1,zr=".phantom-labs.settings.interstitials",Vo=class{static async hasSeen(t,r){let o=await r.get(zr);if(!o)return!1;let n=Mc(t);return(o.interstitialsSeen[t]??0)>=n}static async setHasSeen(t,r){let o=await r.get(zr),n=Mc(t);await r.set(zr,{version:qc,interstitialsSeen:{...!!o&&o.interstitialsSeen,[t]:n}})}static async resetHasSeen(t,r){let o=await r.get(zr);if(o)return r.set(zr,{version:qc,interstitialsSeen:{...o.interstitialsSeen,[t]:void 0}})}static async resetAllHasSeen(t){if(await t.get(zr))return t.set(zr,{version:qc,interstitialsSeen:{}})}};var Iv=e=>{let t=ee(),r=Ye.hasSeenInterstitial(e);return B({queryKey:r,queryFn:async()=>await Vo.hasSeen(e,t)})};i();a();i();a();var Sy=async e=>{await e.invalidateQueries({queryKey:[yr]})};El(Sy);var wy=async(e,t,r)=>{await e.setQueryData(Ye.hasSeenInterstitial(t),r)};var xv=e=>{let t=ee(),r=G();return ie({mutationFn:async()=>{await Vo.setHasSeen(e,t),await wy(r,e,!0)}})};i();a();i();a();i();a();var Gc=async e=>await e.get("dismissedActionBanners")??[],hy=async(e,t)=>{try{let r=await Gc(e);return r.includes(t)?void 0:e.set("dismissedActionBanners",[...r,t])}catch{throw new Fi}};i();a();i();a();var by=async({accountID:e,deviceId:t,locale:r,rootId:o,selectedAccountAddresses:n,addresses:s,accessToken:p})=>{let c;try{let u={accountID:e,deviceId:t,locale:r,platform:Ae,rootId:o,selectedAccountAddresses:n,version:le,addresses:s},m=await U.api().headers({"Content-Type":"application/json"}).bearer(p).post(({apiEnv:g})=>`/banners/get_banners/${g}/v2`,u);if(m.status===400){let g=await m.data,S=Ti(g);throw new Error(S)}else if(m.status<200||m.status>299)throw new Error(`status code: ${m.status}, body: ${await m.statusText}`);let f=m.data;return f?{...f,banners:f.banners??[]}:Ay}catch(u){return c===400&&u instanceof Error&&Q.captureError(u,"actionBanner"),Ay}},Ay={id:void 0,banners:[]};i();a();i();a();var Vc=e=>e?Object.values(Oc).includes(e):!1;i();a();var Ty=e=>{switch(e){case"Bottom Sheet":return lt;case"Modal":return!0;default:return!1}};var Fy=e=>{let{bannerType:t,destinationType:r}=e;switch(t){case"Deep Link":return Vc(r);case"Bottom Sheet":case"Modal":return Ty(t)&&Vc(r);default:return!1}};var Iy=async({accountID:e,deviceId:t,dismissedBanners:r,locale:o,rootId:n,selectedAccountAddresses:s,addresses:p,accessToken:c})=>{let u=await by({accountID:e,deviceId:t,locale:o,rootId:n,selectedAccountAddresses:s,addresses:p,accessToken:c});return{...u,banners:u.banners.filter(m=>Fy(m)&&!r.includes(m.id))}};var vv=L({minutes:1}),Cv=()=>{let e=$(),t=ee(),{data:r,isLoading:o}=Ge(),{data:n}=Ll(kl),{data:s}=te(),p=async()=>{let c=n?.flatMap(w=>w.addresses.map(Et))??[],u=s?.type!=="readOnly"?s?.addresses.map(Et)??[]:[],m=s?.type==="seed"?s.seedIdentifier:null,f=await Gc(t),g=Te(D.language)??"en",S=await e.getDeviceId();return Iy({accountID:s?.identifier??"",deviceId:S,dismissedBanners:f,locale:g,rootId:m,selectedAccountAddresses:u,addresses:c,accessToken:r?.accessToken})};return B({queryKey:Ye.actionBanners(s?.identifier,r?.accessToken),queryFn:p,enabled:!o&&!!s?.identifier,staleTime:vv})};i();a();i();a();var Rv=()=>{let e=ee(),t=G();return ie({mutationFn:async({actionBannerId:o})=>{hy(e,o),t.setQueriesData({queryKey:[yr]},n=>{if(!n)return;let s=n.banners.filter(p=>p.id!==o);return{...n,banners:s}})}})};i();a();i();a();var Pv=()=>{let e=ee(),t=G();return ie({mutationFn:async()=>{let r=Date.now(),n=[...(await e.get("appLaunchTimes")||[]).slice(-1),r];return e.set("appLaunchTimes",n)},onSuccess:()=>{t.invalidateQueries({queryKey:Ye.appLaunchTimes()})}})};i();a();var kv=()=>{let e=ee(),t=G();return ie({mutationFn:()=>{let r=Date.now();return e.set("lastOnboardedAt",r)},onSuccess:()=>{t.invalidateQueries({queryKey:Ye.lastOnboardedAt()})}})};i();a();i();a();var ge={buyFungible:void 0,buyUsdValue:void 0,sellFungible:void 0,sellUsdValue:void 0,sellAmount:"",uiSellAmountCryptoInput:"",amountType:"sell",quoteResponse:void 0,selectedProviderIndex:0,gasEstimation:void 0,isFetchingGasEstimation:!1,maxGasEstimation:void 0,refuelEnabled:!1,hasNoRoutes:!1,isFetchingQuote:!1,error:null,searchQuery:"",numSearchResults:0,primaryCurrency:"crypto",uiSellAmountLocalFiatInput:"",uiBuyAmountCryptoInput:"",uiBuyAmountLocalFiatInput:"",canInputFiatSellAmount:!1,swapFilter:void 0},b=Be(e=>({...ge,resetQuote:()=>e({quoteResponse:ge.quoteResponse,isFetchingQuote:ge.isFetchingQuote,hasNoRoutes:ge.hasNoRoutes,selectedProviderIndex:ge.selectedProviderIndex,error:ge.error,uiSellAmountLocalFiatInput:ge.uiSellAmountLocalFiatInput}),resetSwapper:()=>e({quoteResponse:ge.quoteResponse,gasEstimation:ge.gasEstimation,isFetchingGasEstimation:ge.isFetchingGasEstimation,maxGasEstimation:ge.maxGasEstimation,error:ge.error,sellAmount:"",uiSellAmountCryptoInput:"",uiBuyAmountCryptoInput:ge.uiBuyAmountCryptoInput,swapFilter:ge.swapFilter}),setBuyFungible:t=>e({buyFungible:t}),setBuyUsdValue:t=>e({buyUsdValue:t}),setHasNoRoutes:t=>e({hasNoRoutes:t}),setIsFetchingQuote:t=>e({isFetchingQuote:t}),setQuoteResponse:t=>e({quoteResponse:t,hasNoRoutes:!1}),setSelectedProviderIndex:t=>e({selectedProviderIndex:t}),setGasEstimation:t=>e({gasEstimation:t}),setIsFetchingGasEstimation:t=>e({isFetchingGasEstimation:t}),setMaxGasEstimation:t=>e({maxGasEstimation:t}),setRefuelEnabled:t=>e({refuelEnabled:t}),setSellAmount:t=>e({sellAmount:t}),setAmountType:t=>e({amountType:t}),setSellUsdValue:t=>e({sellUsdValue:t}),setSellFungible:t=>e({sellFungible:t}),setUiSellAmountCryptoInput:t=>e({uiSellAmountCryptoInput:t}),setError:t=>e({error:t}),setSearchQuery:t=>e({searchQuery:t}),setNumSearchResults:t=>e({numSearchResults:t}),setPrimaryCurrency:t=>e({primaryCurrency:t}),setUiSellAmountLocalFiatInput:t=>e({uiSellAmountLocalFiatInput:t}),setUiBuyAmountCryptoInput:t=>e({uiBuyAmountCryptoInput:t}),setUiBuyAmountLocalFiatInput:t=>e({uiBuyAmountLocalFiatInput:t}),setCanInputFiatSellAmount:t=>e({canInputFiatSellAmount:t}),setSwapFilter:t=>e({swapFilter:t}),switchTokensForSell:()=>{e(t=>{let r=t.sellFungible??t.buyFungible,o=t.buyFungible??t.sellFungible,n=t.uiBuyAmountCryptoInput,s=t.uiBuyAmountLocalFiatInput;return{sellFungible:o,buyFungible:r,uiSellAmountCryptoInput:n,uiSellAmountLocalFiatInput:s,uiBuyAmountCryptoInput:ge.uiBuyAmountCryptoInput,uiBuyAmountLocalFiatInput:ge.uiBuyAmountLocalFiatInput,sellAmount:n,swapFilter:void 0}})},switchTokensForBuy:()=>{e(t=>{let r=t.sellFungible??t.buyFungible,o=t.buyFungible??t.sellFungible,n=t.uiSellAmountCryptoInput,s=t.uiSellAmountLocalFiatInput;return{sellFungible:o,buyFungible:r,uiSellAmountCryptoInput:ge.uiSellAmountCryptoInput,uiSellAmountLocalFiatInput:ge.uiSellAmountLocalFiatInput,uiBuyAmountCryptoInput:n,uiBuyAmountLocalFiatInput:s,sellAmount:n,swapFilter:void 0}})},resetUiAmounts:()=>e({uiSellAmountCryptoInput:ge.uiSellAmountCryptoInput,uiSellAmountLocalFiatInput:ge.uiSellAmountLocalFiatInput,uiBuyAmountCryptoInput:ge.uiBuyAmountCryptoInput,uiBuyAmountLocalFiatInput:ge.uiBuyAmountLocalFiatInput,sellAmount:ge.sellAmount})}));i();a();var xy=C(R());function V(){let e=b(c=>c.quoteResponse),t=b(c=>c.hasNoRoutes),r=b(c=>c.selectedProviderIndex),o=b(c=>c.isFetchingQuote),n=(0,xy.useMemo)(()=>e?e.quotes:[],[e]),s=n.length>0?n[r]:void 0;return{bestBuyAmount:n[0]?.buyAmount??"",currentQuote:s,hasNoRoutes:t,quoteResponse:e,quotes:n,selectedProviderIndex:r,isFetchingQuote:o}}i();a();var vy=C(R());i();a();i();a();var Hc=(n=>(n[n.Always=1/0]="Always",n[n.Short=L({days:1})]="Short",n[n.Medium=L({days:3})]="Medium",n[n.Long=L({days:7})]="Long",n))(Hc||{}),Ey=(s=>(s[s.Immediate=0]="Immediate",s[s.Short=L({minutes:1})]="Short",s[s.Medium=L({minutes:2.5})]="Medium",s[s.Long=L({minutes:60})]="Long",s[s.Never=1/0]="Never",s))(Ey||{}),_n=(o=>(o[o.Short=L({seconds:20})]="Short",o[o.Medium=L({minutes:2.5})]="Medium",o[o.Long=L({minutes:5})]="Long",o))(_n||{});var Dv=l.object({swapper:l.number(),review:l.number().nullish().default(null)}),Ii={swapper:_n.Short,review:null},Sr=()=>{let e=Qe.getMultivariateAssignment("swapper-poll-interval-json");if(!e)return Ii;try{let t=JSON.parse(e);return Dv.parse(t)}catch{return Ii}};var st=()=>{let e=Qn(n=>n.intervalMs),t=Qn(n=>n.state),r=Qn(n=>n.setState),o=(0,vy.useCallback)(n=>{r("resumed",n??Cy())},[r]);return{state:t,pause:()=>r("paused"),resume:o,intervalMs:e}},jr=e=>{Qn.getState().setState("resumed",e??Cy())},On=()=>{Qn.getState().setState("paused")},Cy=()=>Sr().swapper,Qn=Be(e=>({state:"resumed",intervalMs:Ii.swapper,setState(t,r){e(r?{state:t,intervalMs:r}:{state:t})}}));i();a();var jy=C(R());i();a();i();a();i();a();i();a();var Nv=e=>{let t=ee(),r=$(),{data:o,isPending:n}=B({gcTime:0,queryKey:["originIsBlocklisted",e],queryFn:async()=>{let{result:s}=await np(t,r,e);return s}});return e===void 0?{isLoading:!1,originIsBlocklisted:!1}:{isLoading:n,originIsBlocklisted:o??!1}};i();a();var $c=C(R());i();a();var Mn=class{constructor(t){this.onScannedMessageByUser=t=>{this.#e.capture("simulation",{data:{simulation:{...t,type:"message",status:"success"}}})};this.onFailedScannedMessageByUser=t=>{this.#e.capture("simulation",{data:{simulation:{...t,type:"message",status:"error"}}})};this.onScannedTransactionByUser=t=>{this.#e.capture("simulation",{data:{simulation:{...t,type:"transaction",status:"success"}}})};this.onFailedScannedTransactionByUser=t=>{this.#e.capture("simulation",{data:{simulation:{...t,type:"transaction",status:"error"}}})};this.#e=t}#e};function Bv(e,t){let{data:r}=te(),{params:o,networkID:n}=e,[s,p]=(0,$c.useState)(null),c=["scan-message",{url:e.url,message:o.message}],u=r?.addresses.find(y=>y.networkID===n)?.address,m=Te(D.language)??"en",f=$(),g=new Mn(f),S=Py(),w=B({queryKey:c,queryFn:async()=>{try{let y=await sp(m,await f.getDeviceId(),{...e,userAccount:u});return p(null),g.onScannedMessageByUser({networkId:n,warnings:y.warnings.map(h=>h.severity),shouldBlock:!!y.block,errorCode:y.error??""}),y}catch(y){p(y),g.onFailedScannedMessageByUser({networkId:n,url:e.url}),Q.addBreadcrumb("simulation",`networkID: ${n}, url: ${e.url}, type: message`,"info"),Q.captureError(w.error,"simulation")}},enabled:!!u&&!!e.url&&!!o.message&&!t?.disabled,refetchInterval:t?.disableRefetch?!1:S,staleTime:1e3*10,...Xa()});return{isFetched:w.isFetched,isFetching:w.isFetching,data:w.data,isLoading:w.isPending&&!t?.disabled,isError:w.isError||!!s,error:w.error||s}}function qn(e,t){let{params:r}=e,[o,n]=(0,$c.useState)(null),s=$(),p=["scan-transaction",e.networkID,e.url,r?r.transactions??r.transaction:""],c=Py(),u=!1;F.isEVMNetworkID(e.networkID)||F.isSolanaNetworkID(e.networkID)?u=!!r&&r.transactions.length>0&&!!e.userAccount:F.isBitcoinNetworkID(e.networkID)&&(u=!!r&&!!r.transaction&&!!r.userAddresses&&r.userAddresses.length>0);let m=Te(D.language)??"en",f=new Mn(s),g=B({queryKey:p,queryFn:async()=>{try{let S=await Sn(m,await s.getDeviceId(),e);return f.onScannedTransactionByUser({networkId:e.networkID,warnings:S.warnings.map(w=>w.severity),shouldBlock:!!S.block,errorCode:S.error??""}),n(null),t?.onSettled?.(S,void 0),S}catch(S){f.onFailedScannedTransactionByUser({networkId:e.networkID,url:e.url}),Q.addBreadcrumb("simulation",`networkID: ${e.networkID}, url: ${e.url}, type: transaction`,"info"),n(S),t?.onSettled?.(void 0,S),Q.captureError(S,"simulation")}},enabled:u&&!t?.disabled,refetchInterval:t?.disableRefetch?!1:c,staleTime:1e3*10,...Xa()});return{isFetched:g.isFetched,isFetching:g.isFetching,data:g.data,isLoading:g.isPending&&!t?.disabled,isError:g.isError||!!o,error:g.error||o}}var Ry=5e3;function Py(){let e=Qe.getMultivariateAssignment("transactions-simulation-interval-ms");if(!e)return Ry;let t=parseInt(e,10);return Number.isNaN(t)?Ry:t}var Kn=C(R());i();a();i();a();i();a();var Ny=l.object({name:l.string(),message:l.string()}),By=l.object({chainId:or,name:l.string(),symbol:l.string(),decimals:l.number(),logoURI:l.optional(l.string()),includesBridge:l.optional(l.boolean())}),Lv=By.extend({address:l.string(),resourceType:l.literal("address")}),Uv=By.extend({slip44:l.string(),resourceType:l.literal("nativeToken")}),_v=l.union([Lv,Uv]),Qv=l.enum(["network","phantom"]),Ov=l.object({name:l.string(),percentage:l.number(),amount:l.number(),type:Qv}),Mv=l.object({name:l.string(),proportion:l.string()}),Ly=l.object({sellAmount:l.string(),buyAmount:l.string()}),Uy=Ly.extend({priceImpact:l.number(),sources:l.array(Mv),fees:l.array(Ov)}),zc=Uy.extend({transactionData:l.array(l.string()),slippageTolerance:l.optional(l.number())}),jc=Uy.extend({allowanceTarget:l.string(),exchangeAddress:l.string(),gas:l.number(),value:l.string(),transactionData:l.string(),slippageTolerance:l.optional(l.number())}),ky=l.object({name:l.string(),amount:l.string(),token:ze,percentage:l.string(),included:l.boolean(),amountUSD:l.optional(l.string())}),qv=l.object({name:l.string(),key:l.string(),logoURI:l.string()}),_y=l.object({name:l.string(),symbol:l.string()}),Gv=l.object({transactionData:l.string(),buyToken:ze,sellToken:ze,nonIncludedNonGasFees:l.string(),feeCosts:l.array(ky),chainId:or,tool:qv,value:l.optional(l.string()),allowanceTarget:l.optional(l.string()),approvalMetadata:l.optional(_y),approvalExactAmount:l.optional(l.string()),exchangeAddress:l.optional(l.string()),gasCosts:l.optional(l.array(l.number())),includedFeeCosts:l.optional(l.array(ky))}),Wc=Ly.extend({executionDuration:l.number(),tags:l.array(l.string()),steps:l.array(Gv),refuelAmount:l.optional(l.string())}),Vv=l.union([zc,jc,Wc]),Yc=l.enum(["solana","eip155","xchain"]),Xc=l.object({type:Yc,taker:ze,buyToken:ze,sellToken:ze,slippageTolerance:l.number(),simulationTolerance:l.optional(l.number()),gasBuffer:l.optional(l.number()),quotes:l.array(Vv)}),Kv=l.enum(["swap-approval","swap","bridge-approval","bridge"]),Hv=l.enum(["swap","bridge"]),Qy=l.object({sellToken:ze,buyToken:ze,approvalMetadata:l.optional(_y)}),$v=l.object({type:l.literal("solana"),transactions:l.array(l.string()),transactionTypes:l.array(Hv),transactionPairs:l.optional(l.array(Qy))}),zv=l.object({type:l.literal("evm"),transactions:l.array(uo),transactionTypes:l.array(Kv),transactionPairs:l.optional(l.array(Qy))}),Qz=l.union([$v,zv]);var Jc=l.object({name:l.string(),amount:l.string()}).strict(),xi=l.object({name:l.string(),fee:l.string(),amount:l.string(),time:l.object({text:l.string(),isFast:l.boolean()}),logoURI:l.string()}),Oz=l.union([Jc,xi]);var Dy=l.object({token:_v,owner:l.string()}),Oy=l.object({sell:Dy,buy:Dy});function pe(e){return Wc.safeParse(e).success}function Wr(e){return zc.safeParse(e).success}function Yr(e){return jc.safeParse(e).success}function hr(e){return Wr(e)||Yr(e)}i();a();var Ar="swapper";var My=[Ar,"quotes"],ue={quotesScope:()=>My,quotes:({sellTokenCaip19:e,buyTokenCaip19:t,taker:r,amountValue:o,amountType:n,slippageTolerance:s,refuelEnabled:p,takerDestination:c,enableAutoSlippage:u})=>[...My,{sellToken:e,buyToken:t,taker:r,takerDestination:c,amountValue:o,amountType:n,slippageTolerance:s,refuelEnabled:p,enableAutoSlippage:u}],transactions:e=>[Ar,"transactions",e],slippageTolerance:()=>[Ar,"slippageTolerance"],lastSubmittedPairs:()=>[Ar,"lastSubmittedPairs"],lastSelectedTokens:()=>[Ar,"lastSelectedTokens"],slippageSettings:()=>[Ar,"slippageSettings"],hasViewedAutoSlippageOptIn:()=>[Ar,"hasViewedAutoSlippageOptIn"],optimisticUpdates:()=>[Ar,"optimisticUpdates"]};var qy=async({callerAddress:e,contractAddress:t,networkId:r,sellAmount:o,allowanceTarget:n,approvalExactAmount:s,exchangeAddress:p,value:c,transactionData:u,type:m})=>{let f=[],g=[];if(t&&(await Yl({callerAddress:e,spender:n,networkId:r,contractAddress:t})).lt(s??o.toString())){let y=await Xl({callerAddress:e,spender:n,networkId:r,contractAddress:t,exactAmount:s});f.push(y),g.push(`${m}-approval`)}let S=uo.parse({type:"0x2",from:e,data:u,chainId:F.getEVMNetworkIDValue(r),to:p,...c==="0"?{}:{value:`0x${new N(c).toString(16)}`}});return f.push(S),g.push(m),{transactions:f,transactionTypes:g}},jv=async({networkId:e,callerAddress:t,quote:r})=>{let{sellAmount:o,steps:n}=r,s=[],p=[],c=[];for(let u of n){let{allowanceTarget:m,exchangeAddress:f,value:g,transactionData:S,sellToken:w,buyToken:y,approvalMetadata:h}=u,A=w.resourceType==="address"?w.address:null,T=F.compareNetworkID(w.chainId,y.chainId)===0?"swap":"bridge";if(!m||!f||!S||!g)throw new Error("Missing bridge step data for EVM bridge quote.");let I=await qy({callerAddress:t,contractAddress:A,networkId:e,sellAmount:o,allowanceTarget:m,approvalExactAmount:u.approvalExactAmount,exchangeAddress:f,value:g,transactionData:S,type:T});s.push(...I.transactions),p.push(...I.transactionTypes),c.push({sellToken:w,buyToken:y,approvalMetadata:h}),I.transactionTypes.length==2&&I.transactionTypes.find(v=>v.includes("approval"))&&c.push({sellToken:w,buyToken:y,approvalMetadata:h})}return{type:"evm",transactions:s,transactionTypes:p,transactionPairs:c}},Wv=async({quote:e,connection:t})=>{let r=[],o=[],n=[];for(let s of e.steps){let{transactionData:p,sellToken:c,buyToken:u}=s,m=F.compareNetworkID(c.chainId,u.chainId)===0?"swap":"bridge",f=await Gy(p,t);r.push(Ma.serialize(f).transaction),o.push(m),n.push({sellToken:c,buyToken:u})}return{type:"solana",transactions:r,transactionTypes:o,transactionPairs:n}},Yv=async({networkId:e,callerAddress:t,quote:r,sellFungible:o})=>{let n=o?.data.tokenAddress??"",{allowanceTarget:s,exchangeAddress:p,sellAmount:c,value:u}=r;return{type:"evm",...await qy({callerAddress:t,contractAddress:n,networkId:e,sellAmount:c,allowanceTarget:s,exchangeAddress:p,value:u,transactionData:r.transactionData,type:"swap"})}},Gy=async(e,t)=>{let r=lo(e,"bs58");return await jl(r,{connection:t,transactionType:"Swap"})},Zc=async({quote:e,connection:t})=>{let r=e.transactionData.map(p=>Gy(p,t)),n=(await Promise.all(r)).map(p=>Ma.serialize(p).transaction);return{type:"solana",transactionTypes:n.map(()=>"swap"),transactions:n}},br=({callerAddress:e,sellFungible:t,quote:r})=>{let{connection:o}=nr(),n=ue.transactions(r);return B({queryKey:n,enabled:!!r&&!!t,gcTime:0,staleTime:0,refetchInterval:_n.Short,async queryFn(){try{if(!r)throw new Error("Missing quote");if(!t)throw new Error("Missing sell token");let s=t.data.chain.id;if(hr(r)){if(F.isEVMNetworkID(s)&&Yr(r))return await Yv({networkId:s,callerAddress:e,quote:r,sellFungible:t});if(F.isSolanaNetworkID(s)&&Wr(r))return Zc({quote:r,connection:o});throw new Error(`Single network swap quote not supported for network ID ${s}`)}if(pe(r)){if(F.isEVMNetworkID(s))return await jv({networkId:s,callerAddress:e,quote:r});if(F.isSolanaNetworkID(s))return await Wv({quote:r,connection:o});throw new Error(`Bridge quote not supported for network ID ${s}`)}throw new Error("Swap quote type not supported")}catch(s){throw s instanceof Error&&Q.captureError(s,"swapper"),s}}})};i();a();i();a();i();a();i();a();i();a();i();a();i();a();var Ei=e=>e.steps.map(t=>t.tool);var Xe=e=>e?pe(e)?Ei(e):e?.sources??[]:[];i();a();function Ue(e){let t=e.reduce((r,o)=>r.add(o.name),new Set);return Array.from(t).join(" + ")}var Xv=10,Jv=Be(e=>({transitions:[],addTransition:(t,r)=>e(o=>{let n=Zv(r,t);return n?{transitions:[n,...o.transitions].slice(0,Xv)}:o})}));function Vy(e,t,r){Jv.getState().addTransition(t,r)}function Zv(e,t){switch(t.type){case"idle":return;case"fetchingQuote":return;case"quote":return{sell:bt(t.response,t.sell,"sell"),buy:bt(t.response,t.buy,"buy"),sellAmount:t.quote.sellAmount,buyAmount:t.quote.buyAmount,provider:Gn(t.quote),slippage:t.response.slippageTolerance,stateType:t.type,eventType:e.type,timestamp:Date.now()};case"simulationRequestErrored":return{sell:bt(t.response,t.sell,"sell"),buy:bt(t.response,t.buy,"buy"),sellAmount:t.quote.sellAmount,buyAmount:t.quote.buyAmount,provider:Gn(t.quote),slippage:t.response.slippageTolerance,stateType:t.type,eventType:e.type,error:t.error,quoteTransactionData:vi(t.quote),timestamp:Date.now()};case"simulated":return;case"simulationFailed":return{sell:bt(t.response,t.sell,"sell"),buy:bt(t.response,t.buy,"buy"),sellAmount:t.quote.sellAmount,buyAmount:t.quote.buyAmount,provider:Gn(t.quote),slippage:t.response.slippageTolerance,simulationError:t.simulationError,stateType:t.type,eventType:e.type,simulationWarning:t.scannedTransactionResult.warnings,quoteTransactionData:vi(t.quote),simulationHumanReadableError:t.scannedTransactionResult.simulationError?.humanReadableError,timestamp:Date.now()};case"transactionSubmissionFailed":return{sell:bt(t.response,t.sell,"sell"),buy:bt(t.response,t.buy,"buy"),sellAmount:t.quote.sellAmount,buyAmount:t.quote.buyAmount,provider:Gn(t.quote),slippage:t.response.slippageTolerance,error:t.error,stateType:t.type,eventType:e.type,quoteTransactionData:vi(t.quote),timestamp:Date.now()};case"transactionNotConfirmed":return{sell:bt(t.response,t.sell,"sell"),buy:bt(t.response,t.buy,"buy"),sellAmount:t.quote.sellAmount,buyAmount:t.quote.buyAmount,provider:Gn(t.quote),slippage:t.response.slippageTolerance,stateType:t.type,eventType:e.type,quoteTransactionData:vi(t.quote),timestamp:Date.now()};default:return}}var Gn=e=>{let t=Xe(e);return Ue(t)},bt=(e,t,r)=>{switch(r){case"sell":return{caip19:e.sellToken,symbol:t.data.symbol??void 0};case"buy":return{caip19:e.buyToken,symbol:t.data.symbol??void 0}}},vi=e=>{if(!pe(e))return e.transactionData};var Ri=class{onStateChange(t,r,o){Vy(r,o,t)}};i();a();var Pi=class{constructor(t){this.listeners=t}transition(t,r){try{let o=this.detectNextState(t,r);return this.listeners.forEach(n=>n.onStateChange(r,t,o)),o}catch{return t}}detectNextState(t,r){switch(r.type){case"quoteRequestStarted":return{type:"fetchingQuote",buy:r.buy,sell:r.sell,amount:r.amount};case"quoteResponseReceived":switch(t.type){case"fetchingQuote":case"quote":case"simulated":case"simulationRequestErrored":case"transactionSubmissionFailed":case"transactionNotConfirmed":let o=r.response.quotes[r.quoteIndex];if(!o)throw new Error("Quote index out of bounds");return{type:"quote",response:r.response,buy:t.buy,sell:t.sell,quote:o};default:throw new Error("Can't transition from this state")}case"quoteSelected":switch(t.type){case"quote":let o=t.response.quotes[r.quoteIndex];if(!o)throw new Error("Quote index out of bounds");return{type:"quote",quote:o,buy:t.buy,sell:t.sell,response:t.response};default:throw new Error("Can only transition to QuoteChanged from Quote")}case"simulationResponseReceived":switch(t.type){case"quote":return r.scannedTransactionResult.error?{type:"simulationFailed",quote:t.quote,buy:t.buy,sell:t.sell,response:t.response,scannedTransactionResult:r.scannedTransactionResult,simulationError:r.scannedTransactionResult.error}:{type:"simulated",quote:t.quote,buy:t.buy,sell:t.sell,response:t.response,scannedTransactionResult:r.scannedTransactionResult};default:throw new Error("Can only transition to Simulated from Simulating")}case"simulationRequestError":switch(t.type){case"quote":return{type:"simulationRequestErrored",buy:t.buy,sell:t.sell,quote:t.quote,response:t.response,error:r.error};default:throw new Error("Can only transition to SimulationRequestErrored from Simulating")}case"transactionSubmissionFailed":switch(t.type){case"simulated":case"simulationFailed":return{type:"transactionSubmissionFailed",quote:t.quote,buy:t.buy,sell:t.sell,response:t.response,scannedTransactionResult:t.scannedTransactionResult,error:r.error};default:throw new Error("Can only transition to SwapSubmitFailed from SubmittingSwap")}case"transactionConfirmationFailed":switch(t.type){case"simulated":return{type:"transactionNotConfirmed",quote:t.quote,buy:t.buy,sell:t.sell,txReceipt:r.txReceipt,response:t.response,scannedTransactionResult:t.scannedTransactionResult,error:r.error};default:throw new Error("Can only transition to TransactionNotConfirmed from ConfirmingTransaction")}}}};var eC=new Pi(new Set([new Ri])),Ky=Be(e=>({state:{type:"idle"},transition:t=>{e(r=>{let o=r.state;return{state:eC.transition(o,t)}})}}));var Vt=()=>{let e=Ky(r=>r.state),t=Ky(r=>r.transition);return{state:e,transition:t}};i();a();var tC="swap-priority-fee",rC=async(e,t,r)=>{let o=lo(e,"bs58");return await ol(o,r),{networkFeeEstimation:await Zl(t,[o.message])}};function Hy({swapTransactions:e,chainId:t,solanaConnection:r}){let o=[tC,e],n=async()=>{if(!e)throw new Error("No swap transactions");if(e.type!=="solana")throw new Error("Trying estimate network fee for non-solana txs");if(!t)throw new Error("Trying to execute swap with no chain id.");try{let p=e.transactions.map(m=>rC(m,t,r));return(await Promise.all(p)).map(m=>m.networkFeeEstimation.value)}catch{return[0]}},s=!!e&&e.transactions.length>0&&e.type==="solana"&&!!t;return B({queryKey:o,queryFn:n,enabled:s})}i();a();var oC=e=>{switch(e){case W.Ethereum.Mainnet:case W.Ethereum.Sepolia:return 8e5;case W.Polygon.Mainnet:case W.Polygon.Amoy:return 5e5;case W.Base.Mainnet:case W.Base.Sepolia:return 5e5;default:return 0}},nC=(e,t)=>e.steps.flatMap(o=>o.gasCosts??[oC(t)]),sC=1.3,$y={estimateGas:({evmGasEstimation:e,currentQuote:t,scanResult:r,networkID:o,includeGasBuffer:n,gasBuffer:s})=>{let{maxFeePerGas:p,maxPriorityFeePerGas:c}=e,u=Yr(t)?[t.gas]:nC(t,o),m=r?.advancedDetails?.gas;return r?.advancedDetails?.gasLimit>0&&(u=m),u.map(g=>({maxFeePerGas:p,maxPriorityFeePerGas:c,networkID:o,gasLimit:new K(g).multipliedBy(n?s??sC:1).integerValue(K.ROUND_CEIL)}))}};i();a();var zy={estimateGas:({solanaNetworkFees:e,networkID:t})=>e.map(r=>({networkID:t,value:r}))};var aC=2e4,eu=({includeGasBuffer:e=!1}={})=>{let{currentQuote:t,quoteResponse:r}=V(),o=b(E=>E.sellFungible),n=o?.data.chain.id??W.Solana.Mainnet,{data:s}=vt(o?{address:o.data.walletAddress,networkID:o.data.chain.id}:void 0),p=s?.address??"",{data:c}=am({networkID:F.isEVMNetworkID(n)?n:W.Ethereum.Mainnet,transactionSpeed:"standard",gasLimit:new N(1),refetchInterval:aC}),{data:u,isFetching:m}=br({callerAddress:p,sellFungible:o,quote:t}),f={type:"transaction",userAccount:p,params:void 0,url:"https://phantom.app",networkID:n};u?.type==="evm"&&F.isEVMNetworkID(n)&&(f.params={transactions:u.transactions});let{transition:g}=Vt(),S=(0,Kn.useCallback)((E,P)=>{P&&g({type:"simulationRequestError",error:P}),E&&g({type:"simulationResponseReceived",scannedTransactionResult:E})},[g]),{data:w,isFetching:y}=qn(f,{disabled:F.isSolanaNetworkID(n),onSettled:S}),{connection:h}=nr(),{data:A,isFetching:T}=Hy({swapTransactions:u,chainId:F.isSolanaNetworkID(n)?n:W.Solana.Mainnet,solanaConnection:h}),I=b(E=>E.isFetchingQuote),v=(0,Kn.useMemo)(()=>{if(!(I||!t)){if(F.isEVMNetworkID(n)&&(Yr(t)||pe(t))&&c)return $y.estimateGas({evmGasEstimation:c,currentQuote:t,scanResult:w,includeGasBuffer:e,networkID:n,gasBuffer:r?.gasBuffer});if(F.isSolanaNetworkID(n)&&A!==void 0)return zy.estimateGas({solanaNetworkFees:A,networkID:n})}},[I,t,n,c,A,w,e,r?.gasBuffer]),x=T||m||y;return(0,Kn.useMemo)(()=>({gasEstimation:v,isFetching:x,isError:c===void 0}),[v,x,c])};function pC(){let e=b(c=>c.setGasEstimation),t=b(c=>c.setIsFetchingGasEstimation),r=b(c=>c.setMaxGasEstimation),{state:o}=st(),{gasEstimation:n,isFetching:s}=eu(),{gasEstimation:p}=eu({includeGasBuffer:!0});(0,jy.useEffect)(()=>{if(o==="paused"){t(!1);return}t(s),e(n),r(p)},[o,e,r,t,n,p,s])}i();a();i();a();var uS=C(R());i();a();var Wy=(e,t,r)=>!(!e||t&&!r);i();a();var Ni=C(R());i();a();i();a();i();a();i();a();var tu=5,ru=5,Yy={id:F.solana.mainnetID,imageUrl:F.solana.iconUrl,name:F.solana.name,symbol:F.solana.tokenSymbol},ki={type:"SolanaNative",data:{amount:"0",balance:new N(0),chain:Yy,decimals:9,key:"SolanaNative",logoUri:"https://cdn.jsdelivr.net/gh/solana-labs/token-list@main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",name:"Solana",symbol:"SOL",tokenAddress:void 0,walletAddress:"",spamStatus:"VERIFIED"}},ou={type:"SPL",data:{amount:"0",balance:new N(0),chain:Yy,decimals:6,key:"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",logoUri:"https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v/logo.png",mintAddress:"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",name:"USD Coin",splTokenAccountPubkey:"EAF7dH8Jy43Xgp68PhyybKhfKxk46NFRkWN4PMCLqMfS",symbol:"USDC",tokenAddress:"EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",walletAddress:"",programId:"TokenkegQfeZyiNwAJbNbGKPFXCWuBvf9Ss623VQ5DA",spamStatus:"VERIFIED"}},Xy={type:"PolygonNative",data:{chain:{id:F.polygon.mainnetID,name:F.polygon.name,symbol:F.polygon.tokenSymbol,imageUrl:F.polygon.iconUrl},walletAddress:"",decimals:18,amount:"72172806352057410682",logoUri:"https://wallet-asset.matic.network/img/tokens/matic.svg",name:"Polygon",symbol:F.polygon.tokenSymbol,balance:new N(0),key:"PolygonNative",usd:79.**************,price:1.095,usd_24h_change:-.*****************,tokenAddress:void 0,spamStatus:"VERIFIED"}},Jy={type:"BaseNative",data:{chain:{id:F.base.mainnetID,name:F.base.name,symbol:F.base.tokenSymbol,imageUrl:F.base.iconUrl},walletAddress:"",decimals:18,amount:"72172806352057410682",logoUri:"https://cdn.jsdelivr.net/gh/trustwallet/assets@master/blockchains/ethereum/assets/******************************************/logo.png",name:"Base",symbol:F.base.tokenSymbol,balance:new N(0),key:"BaseNative",usd:79.**************,price:1.095,usd_24h_change:-.*****************,tokenAddress:void 0,spamStatus:"VERIFIED"}},Zy={type:"ERC20",data:{amount:"0",balance:new N(0),decimals:6,key:"******************************************",logoUri:"https://wallet-asset.matic.network/img/tokens/usdc.svg",name:"USD Coin",symbol:"USDC",tokenAddress:"******************************************",walletAddress:"",chain:{id:F.polygon.mainnetID,imageUrl:F.polygon.iconUrl,name:F.polygon.name,symbol:F.polygon.tokenSymbol},contractAddress:"******************************************",spamStatus:"VERIFIED"}},eS={type:"ERC20",data:{amount:"0",balance:new N(0),decimals:6,key:"******************************************",logoUri:"https://wallet-asset.matic.network/img/tokens/usdc.svg",name:"USD Coin",symbol:"USDC",tokenAddress:"******************************************",walletAddress:"",chain:{id:F.base.mainnetID,imageUrl:F.base.iconUrl,name:F.base.name,symbol:F.base.tokenSymbol},contractAddress:"******************************************",spamStatus:"VERIFIED"}},tS={type:"EthereumNative",data:{chain:{id:F.ethereum.mainnetID,name:F.ethereum.name,symbol:F.ethereum.tokenSymbol,imageUrl:F.ethereum.iconUrl},walletAddress:"",decimals:18,amount:"45824381294194610",logoUri:"https://cdn.jsdelivr.net/gh/trustwallet/assets@master/blockchains/ethereum/assets/******************************************/logo.png",name:"Ethereum",symbol:"ETH",balance:new N(0),key:"EthereumNative",usd:85.14215868842653,price:1858.01,usd_24h_change:.0009391170983548136,tokenAddress:void 0,spamStatus:"VERIFIED"}},rS={type:"ERC20",data:{chain:{id:F.ethereum.mainnetID,name:F.ethereum.name,symbol:F.ethereum.tokenSymbol,imageUrl:F.ethereum.iconUrl},walletAddress:"",contractAddress:"******************************************",decimals:6,amount:"1881092",logoUri:"https://assets.coingecko.com/coins/images/6319/large/USD_Coin_icon.png?1547042389",name:"USD Coin",symbol:"USDC",balance:new N(0),key:"******************************************",tokenAddress:"******************************************",usd:1.8807026139560001,price:.999793,usd_24h_change:-.0005475429199477411,spamStatus:"VERIFIED"}},Hn=ou,$n=ki;var Di="https://help.phantom.app/hc/en-us/articles/5985106844435",oS="https://help.phantom.app/hc/en-us/articles/29763090277139";var Xr=18;var Ko=500,Tr={amount:new N(0),uiAmount:"",pct:0};i();a();var Ho={};var nS={...Ho,bridgeMinimumUsdAmounts:0,swapDefaultFee:new N(6*10**16),bridgeDefaultFee:new N(6*10**16),averageNetworkFee:new N(15e-5*10**18),nativeFungible:Jy,usdcFungible:eS};i();a();var sS={...Ho,bridgeMinimumUsdAmounts:20,swapDefaultFee:new N(2.5*10**16),bridgeDefaultFee:new N(3.75*10**16),averageNetworkFee:new N(0),nativeFungible:tS,usdcFungible:rS};i();a();var iS={...Ho,bridgeMinimumUsdAmounts:2,swapDefaultFee:new N(6*10**16),bridgeDefaultFee:new N(6*10**16),averageNetworkFee:new N(.0015*10**18),nativeFungible:Xy,usdcFungible:Zy};i();a();var aS={bridgeMinimumUsdAmounts:1,swapDefaultFee:new N(3*10**6),bridgeDefaultFee:new N(30*10**6),averageNetworkFee:new N(15e3),nativeFungible:ki,usdcFungible:ou};var x4=["solana","ethereum","polygon","base"],cC=new So({solana:aS,ethereum:sS,polygon:iS,base:nS}),we=yo(cC);i();a();var he=(e,t)=>!!e&&!!t&&F.compareNetworkID(e,t)!==0;i();a();var cS=C(R());i();a();var pS=C(R());function Fr(){let{data:[e]}=ae(["enable-base-swapper"]);return(0,pS.useMemo)(()=>we.supportedChains.filter(t=>t!=="base"||e),[e])}var Kt=()=>{let{data:e}=te(),t=Fr().map(r=>F.getMainnetNetworkID(r));return(0,cS.useMemo)(()=>uC(e,t),[e,t])},uC=(e,t)=>[...new Set((e?.addresses??[]).map(r=>r.networkID).filter(r=>t.includes(r)))];var Bi=()=>{let[e,t]=(0,Ni.useState)(!1),r=b(u=>u.sellFungible),o=b(u=>u.buyFungible),n=o?.data.chain.id,s=he(r?.data.chain.id,o?.data.chain.id),p=Kt(),{fungible:c}=rt({key:n?Ao(n,void 0):void 0});return(0,Ni.useEffect)(()=>{if(!c){t(!1);return}if(p.includes(c.data.chain.id)){let u=we.get(c.data.chain.id),m=s?u.bridgeDefaultFee:u.swapDefaultFee,f=new N(c.data.amount??0).gte(m);t(s&&!f)}},[c,s,t,p]),e};var Jr=()=>{let{currentQuote:e}=V(),t=$(),r=Bi(),n=b(A=>A.buyFungible)?.data.chain.id,{fungible:s}=rt({key:n?Ao(n,void 0):void 0}),p=n?F.getTokenSymbol(n):"",c=s?.data.decimals??0,u=n?F.getChainID(n):void 0,m=b(A=>A.refuelEnabled),f=b(A=>A.setRefuelEnabled),g=(0,uS.useCallback)(A=>{t.capture("swapperRefuelToggle",{data:{value:A}}),f(A)},[f,t]),S=pe(e)?e.refuelAmount:void 0,w=S?`${Ce(S??0,c)} ${p}`:void 0,y=Ce(s?.data.amount??0,c),h=Wy(r,m,S);return{label:D.t("bridgeRefuelTitle"),refuelSupported:r,refuelEnabled:m,setRefuelEnabled:g,refuelAmount:S,uiRefuelAmount:w,showRefuelOption:h,buyNativeToken:{chain:u,symbol:p,balance:y,decimals:c}}};var lC=()=>{let{isFetchingQuote:e}=V(),{refuelEnabled:t,setRefuelEnabled:r,refuelAmount:o,uiRefuelAmount:n,buyNativeToken:{symbol:s,balance:p,decimals:c,chain:u}}=Jr(),m=u?je(u):void 0,{data:f}=Me({query:m?{data:m}:void 0}),g="";if(f?.usd&&o&&c){let S=Fe(Z(new K(o),c).toNumber(),f.usd);g=cs(S)}return{symbol:s,balance:p,refuelAmount:o,uiRefuelAmount:n,estimatedCost:g,refuelEnabled:t,setRefuelEnabled:r,isFetchingQuote:e}};i();a();var gS=C(R());i();a();var mC="swap",lS=e=>new nm(e,mC),Li=go($,e=>lS(e));i();a();i();a();var mS=C(R());i();a();function it({key:e,queryKey:t,defaultValue:r=null}){let o=ee();return B({enabled:!0,queryKey:t,gcTime:Hc.Long,staleTime:1/0,async queryFn(){try{return await o.get(e)}catch(n){return n instanceof Error&&Q.captureError(n,"swapper"),r}}})}function Tt({key:e,queryKey:t}){let r=ee(),o=G();return ie({async mutationFn(n){try{return await r.set(e,n)}catch(s){s instanceof Error&&Q.captureError(s,"swapper");return}},async onMutate(n){await o.cancelQueries({queryKey:t});let s=o.getQueryData(t);return o.setQueryData(t,n),{previousValue:s}},onError(n,s,p){o.setQueryData(t,p?.previousValue)}})}var dC={type:"fixed"},De=()=>{let{slippageSetting:e,isFetched:t,save:r}=fC();return{isFetched:t,selectedSetting:e,setSlippageSetting:r}},fC=()=>{let{data:e,isFetched:t}=it({key:"slippageSettings",queryKey:ue.slippageSettings()}),{mutateAsync:r}=Tt({key:"slippageSettings",queryKey:ue.slippageSettings()}),o=(0,mS.useCallback)(async n=>{await r(n)},[r]);return{isFetched:t,slippageSetting:e??dC,save:o}};function Ir(e,t,r){if(!r)return;if(t==="fixed")return r.slippageTolerance;let o=r.quotes[e];if(o)return pe(o)?r.slippageTolerance:hr(o)?o.slippageTolerance:r.slippageTolerance}i();a();var Ui=(e,t,r)=>{if(r)return Z(e,t).times(K(r)).toNumber()},dS=(e,t)=>t&&e?t>e?t:e:t??e;function fS({buyAssetUsdValue:e,buyToken:t,sellAssetUsdValue:r,sellToken:o,buyAmount:n,sellAmount:s,phantomFeeUiAmount:p,priceImpact:c,provider:u,slippageTolerance:m,slippageType:f,sellSwapFilter:g}){if(t&&o){let S=Ui(n,t.data.decimals,e),w=Ui(s,o.data.decimals,r),y=dS(S,w);return{networkId:o.data.chain.id,chainId:F.getChainID(o.data.chain.id),toNetworkId:t.data.chain.id,toChainId:F.getChainID(t.data.chain.id),isBridge:o.data.chain.id!==t.data.chain.id,marketName:`${o.data.symbol}/${t.data.symbol}`,tradeDirection:"sell",swapSource:u,priceImpact:c,fromAssetSymbol:o.data.symbol??"",fromAssetAddress:o.data.tokenAddress,fromAssetAmount:Z(s,o.data.decimals),fromAssetUsdPrice:r,fromAssetUsdVolume:w,toAssetSymbol:t.data.symbol??"",toAssetAddress:t.data.tokenAddress,toAssetAmount:Z(n,t.data.decimals),toAssetUsdPrice:e,toAssetUsdVolume:S,totalUsdVolume:y,feeUsd:p,slippageTolerance:m,slippageType:f,sellSwapFilter:g}}return null}function nu({buyAssetUsdValue:e,buyToken:t,sellAssetUsdValue:r,sellToken:o,quote:n,slippageTolerance:s,slippageType:p,sellSwapFilter:c}){if(t&&o){let{buyAmount:u,sellAmount:m}=n,f="priceImpact"in n?n.priceImpact:0,g="sources"in n?Ue(n.sources):"",S=("fees"in n?n.fees.find(A=>A.type==="phantom")?.amount.toString():"")??"",w=Ui(u,t.data.decimals,e),y=Ui(m,o.data.decimals,r),h=dS(w,y);return{chainId:o.data.chain.id??t.data.chain.id,swap:{marketName:`${o.data.symbol}/${t.data.symbol}`,tradeDirection:"sell",swapSource:g,priceImpact:f,fromAssetSymbol:o.data.symbol??"",fromAssetAddress:o.data.tokenAddress??"",fromAssetAmount:Z(m,o.data.decimals),fromAssetUsdPrice:r??0,fromAssetUsdVolume:y??0,toAssetSymbol:t.data.symbol??"",toAssetAddress:t.data.tokenAddress??"",toAssetAmount:Z(u,t.data.decimals),toAssetUsdPrice:e,toAssetUsdVolume:w,totalUsdVolume:h??0,feeUsd:Number(S),slippageTolerance:s,slippageType:p,sellSwapFilter:c}}}return null}i();a();var _i=C(R());i();a();var su=e=>{let r={chainId:e.data.chain.id,name:e.data.name??"",symbol:e.data.symbol??"",decimals:e.data.decimals,logoURI:e.data.logoUri??""};return Le(e.type)?{resourceType:"nativeToken",slip44:e.data.key,...r}:{resourceType:"address",address:e.data.key,...r}};i();a();function gC(e){let{mainnetID:t,iconUrl:r,name:o,tokenSymbol:n}=F[e];return{id:t,imageUrl:r,name:o,symbol:n}}var iu=e=>e.resourceType==="address"?e.address:e.slip44,au=(e,t)=>{let r={amount:"0",balance:new N(0),decimals:e.decimals,key:iu(e),logoUri:e.logoURI,mintAddress:iu(e),name:e.name,splTokenAccountPubkey:"",symbol:e.symbol,tokenAddress:iu(e),walletAddress:t,spamStatus:"NOT_VERIFIED"},o=gC(F.getChainID(e.chainId)),{nativeFungibleTokenType:n,nonNativeFungibleTokenType:s}=Dr.get(e.chainId),p=e.resourceType==="nativeToken"?n:s,c=yC(e);return{type:p,data:{chain:o,...r,...c}}};function yC(e){if(e.resourceType==="address"){if(F.isEVMNetworkID(e.chainId))return{contractAddress:e.address};if(F.isSolanaNetworkID(e.chainId))return{mintAddress:e.address}}return{}}function SC(e){let t=Oy.safeParse(JSON.parse(e));if(!t.success)return null;let{sell:r,buy:o}=t.data,n=au(r.token,r.owner),s=au(o.token,o.owner);return!n||!s?null:{sellFungible:n,buyFungible:s}}function wC(e,t){let r=su(e),o=su(t);return!r||!o?"":JSON.stringify({sell:{token:r,owner:e.data.walletAddress},buy:{token:o,owner:t.data.walletAddress}})}var xr=()=>{let{data:e,isFetching:t}=it({key:"lastSubmittedPairs",queryKey:ue.lastSubmittedPairs()}),r=(0,_i.useMemo)(()=>e?SC(e):null,[e]),{mutateAsync:o}=Tt({key:"lastSubmittedPairs",queryKey:ue.lastSubmittedPairs()}),n=(0,_i.useCallback)(async(s,p)=>{await o(wC(s,p))},[o]);return{pairs:r,isLoading:t,storePairs:n}};var jn=({goToConfirmation:e})=>{let t=b(h=>h.selectedProviderIndex),r=b(h=>h.sellUsdValue),o=b(h=>h.buyUsdValue),{currentQuote:n,quoteResponse:s}=V(),p=b(h=>h.sellFungible),c=b(h=>h.buyFungible),u=b(h=>h.swapFilter),{selectedSetting:m}=De(),{storePairs:f}=xr(),g=$(),S=Li(),{pause:w}=st();return(0,gS.useCallback)(()=>{if(!n||!p||!c)return;let h=Ir(t,m.type,s);g.capture("swapperSwap");let A=nu({buyToken:c,sellToken:p,sellAssetUsdValue:r,buyAssetUsdValue:o,quote:n,slippageTolerance:h,slippageType:m.type,sellSwapFilter:u});A&&(Q.addBreadcrumb("swapper","approved","info",mo(A.swap)),S.approved(A)),w(),e(),F.compareNetworkID(p.data.chain.id,c.data.chain.id)===0&&f(p,c)},[n,p,c,t,m.type,s,g,r,o,w,e,S,f,u])};i();a();var pu=C(R());i();a();var Qi=(e,t)=>{if(t===void 0)return;let r=q(e),o=oe(r);return t[o]};i();a();var yS=C(Wh()),Oi=(e,t)=>{let r=(0,yS.default)(e),o=r.value();return Math.abs(o)>0&&Math.abs(o)<1e-5?`< ${o<0?"-":""}0.00001`:t>=12?r.format("0,0.[0]"):t>=9?r.format("0,0.[000]"):r.format("0,0.[00000]")};var Mi={network:{id:F.solana.mainnetID,name:F.solana.name,symbol:F.solana.tokenSymbol,imageUrl:F.solana.iconUrl},tokenType:"SolanaNative",symbol:"",logoUri:"",tokenAddress:"",amount:"",amountUsd:0};function hC({goToConfirmation:e}){let t=b(f=>f.buyFungible),r=b(f=>f.sellFungible),o=b(f=>f.setBuyUsdValue),n=b(f=>f.setSellUsdValue),s=(0,pu.useMemo)(()=>{let f=[];return t&&r&&(f.push(q(t)),f.push(q(r))),{query:{data:f}}},[t,r]),{data:p}=gt(s),{currentQuote:c,quoteResponse:u}=V(),m=jn({goToConfirmation:e});return(0,pu.useMemo)(()=>{if(!u||!c||!t||!r)return{buyToken:Mi,sellToken:Mi,onSwap:m};let f=t?.data,g=r?.data;if(!f||!g)return{buyToken:Mi,sellToken:Mi,onSwap:m};let S="",w=null,y="",h=null,{sellAmount:A,buyAmount:T}=c,I=Z(T,f.decimals),v=Z(A,g.decimals),x=N(v),E=String(I).split(".")[0].length,P=v.toString().split(".")[0].length,k=f.symbol?.length,H=g.symbol?.length,j=E&&k?E+k:0,M=P&&H?P+H:0;y=`${Oi(x,M)} ${g.symbol}`,S=`${Oi(I,j)} ${f.symbol}`;let X=Qi(t,p),Y=Qi(r,p);return X?.usd&&(w=I.multipliedBy(X.usd).toNumber(),o(X.usd)),Y?.usd&&(h=x.multipliedBy(Y.usd).toNumber(),n(Y.usd)),{buyToken:{network:f.chain,tokenType:t.type,symbol:f.symbol,logoUri:f.logoUri,tokenAddress:f.tokenAddress,amount:S,amountUsd:w},sellToken:{network:g.chain,tokenType:r.type,symbol:g.symbol,logoUri:g.logoUri,tokenAddress:g.tokenAddress,amount:y,amountUsd:h},onSwap:m}},[u,c,t,r,p,m,o,n])}i();a();var Je=C(R());i();a();var SS=(e,t)=>e.length===0?$n:t.includes(e[0])?we.get(e[0]).nativeFungible??$n:$n,wS=e=>we.get(e).usdcFungible,Wn=(e,t)=>{let r=Nr(t);return e.find(o=>{if(Le(r)&&r===o.type)return!0;if(t.resourceType==="address"){let n=um(o);return kr(t)?gl(t.address,n):t.address===n}})},qi=(e,t,r)=>{let o=Wn(r,e);if(!o){if(Le(Nr(e)))o=we.get(e.chainId).nativeFungible;else if(t&&t.chainId===e.chainId){let n=Le(Nr(t)),{usdcFungible:s,nativeFungible:p}=we.get(t.chainId);o=n?s:p}}return o};i();a();var AS=C(R());i();a();var uu=C(R());i();a();function eo(e,t){return e.data.key===t.data.key}i();a();var hS=C(R());var cu=()=>{let e=Kt();return(0,hS.useCallback)(t=>!!t&&e.includes(t),[e])};function to({enablePrices:e,enableSorting:t}){let{fungibles:r,isLoadingTokens:o,tokensError:n,refetch:s}=Ct({enablePrices:e,enableSorting:t}),p=cu(),c=r.filter(f=>p(f.data.chain.id)&&f.type!=="CompressedSPL"),u=b(f=>f.sellFungible),m=(0,uu.useMemo)(()=>{if(u){let f=c.findIndex(S=>eo(S,u)),g=c[f];g&&g.data.amount!==u.data.amount&&(c[f]=u)}return c.filter(f=>f.data.amount!=="0")},[c,u]);return(0,uu.useMemo)(()=>({assets:m,isLoading:o,error:n,refetchTokens:s}),[m,o,n,s])}var Gi=()=>{let e=gs(),t=b(s=>s.sellFungible),{pairs:r}=xr(),{data:[o]}=ae(["enable-swapper-token-fetching-optimization"]),{assets:n}=to({enablePrices:!o,enableSorting:!o});return(0,AS.useMemo)(()=>{let s=t?wS(t.data.chain.id):Hn,u=((r?e.includes(r.buyFungible.data.chain.id):!1)?r?.buyFungible:void 0)??s;return u?Wn(n,q(u))??u:void 0},[t,r,e,n])};i();a();var bS=C(R());var Er=()=>{let e=gs(),t=Kt(),{pairs:r}=xr(),{data:[o]}=ae(["enable-swapper-token-fetching-optimization"]),{assets:n}=to({enablePrices:!o,enableSorting:!o});return(0,bS.useMemo)(()=>{let s=SS(e,t),p=r?q(r?.sellFungible):void 0,c=r?q(r?.buyFungible):void 0;return((p?e.includes(p.chainId):!1)&&p&&c?qi(p,c,n):void 0)??s},[e,r,n,t])};var TS=e=>{if(e)try{return Il(e)}catch{return}},FS=e=>Br(e)??[];function AC({paramsSellFungible:e,paramsSellAmount:t,paramsBuyFungible:r,navRef:o}){let{isLoading:n}=xr(),s=(0,Je.useMemo)(()=>TS(e),[e]),p=(0,Je.useMemo)(()=>TS(r),[r]),c=t,[u,m]=(0,Je.useState)(!1),[f,g]=(0,Je.useState)(!1),{data:S}=te(),{data:[w]}=ae(["enable-swapper-token-fetching-optimization"]),{assets:y,isLoading:h}=to({enablePrices:!w,enableSorting:!w}),{data:A,isPending:T}=Kr({query:p?.resourceType==="address"?p.address:p?.slip44??"",enableQuery:!!p,networkIds:[p?.chainId??F.solana.mainnetID],searchContext:"swapper"}),I=(0,Je.useMemo)(()=>{let z=[];return s&&z.push(...FS(s)),p&&z.push(...FS(p)),z},[s,p]),{fungibles:v,isLoadingTokens:x,isRefetchingTokens:E,tokensError:P}=Ct({keys:I,enablePrices:!w,enableSorting:!w}),k=Er(),H=Gi(),j=b(z=>z.setBuyFungible),M=b(z=>z.setSellFungible),X=b(z=>z.setUiSellAmountCryptoInput),Y=b(z=>z.setSellAmount);return(0,Je.useEffect)(()=>{M(void 0),m(!1),j(void 0),g(!1)},[s,p,M,j,S?.identifier,n,o]),(0,Je.useEffect)(()=>{if(u)return;let z;if(s){if(h)return;z=qi(s,p,y)}else if(!s&&p){let{usdcFungible:J,nativeFungible:ce}=we.get(p.chainId);z=Le(Nr(p))?J:ce}else z=k;m(!0),M(z)},[s,p,k,y,M,h,u]),(0,Je.useEffect)(()=>{if(f)return;let z;if(p){if(T)return;if(z=Wn(A??[],p),!z){let{usdcFungible:J,nativeFungible:ce}=we.get(p.chainId);z=Le(Nr(p))?ce:J}}else z=H;if(!x&&!E&&!P&&v&&z){let J=oe(q(z)),ce=v.find(be=>oe(q(be))===J);ce&&(z=ce)}g(!0),j(z)},[p,A,T,j,f,H,v,x,E,P]),(0,Je.useEffect)(()=>{c?(Y(c),X(c)):(Y(""),X(""))},[c,o,X,Y]),{isLoading:(!!s||!!p)&&(!f||!u)||n}}i();a();var Ze=C(R()),ES=C(mu());i();a();var xS=({sellToken:e,buyToken:t,taker:r,takerDestination:o,amount:n,slippageTolerance:s,refuelEnabled:p,pollInterval:c,pollerState:u,enableAutoSlippage:m,getSwapQuotesStrategy:f})=>{let g=$(),S=e?q(e):void 0,w=t?q(t):void 0,y=e?lm(e,n.value).toString():n.value,h=!!n.value&&!!t&&!!e&&u==="resumed",A=ue.quotes({sellTokenCaip19:S,buyTokenCaip19:w,taker:r,amountValue:y,amountType:n.type,refuelEnabled:p,takerDestination:o,slippageTolerance:s,enableAutoSlippage:m});return B({queryKey:A,enabled:h,gcTime:0,staleTime:0,refetchInterval:c,async queryFn({signal:T}){if(!n||!w||!S||!t||!e)throw new Error("missing necessary args");let I=await f.fetch({sellTokenCaip19:S,buyTokenCaip19:w,taker:r,takerDestination:o,sellAmount:y,exactOut:n.type==="buy",slippageTolerance:s,refuelEnabled:p,signal:T,enableAutoSlippage:m,sell:e,buy:t,amount:{value:y,type:n.type}});return g.capture("swapperQuotesResult",{data:{numQuotes:I.quotes.length,sellToken:e,fromNetworkId:S.chainId,toNetworkId:w.chainId}}),I}})};i();a();var Vi=()=>{let e=b(g=>g.sellFungible),t=b(g=>g.buyFungible),r=b(g=>g.sellAmount),o=e?q(e):null,{data:n}=Me({query:o?{data:o}:void 0}),s=e?.data.chain.id,p=t?.data.chain.id,c=n?.usd;if(!s||!p||!r||!c||F.compareNetworkID(s,p)===0)return!0;let u=Fe(Number(r),c),m=we.get(s).bridgeMinimumUsdAmounts,f=we.get(p).bridgeMinimumUsdAmounts;return m<=u&&f<=u};i();a();var ro=()=>it({key:"slippageTolerance",queryKey:ue.slippageTolerance()}),Ki=()=>Tt({key:"slippageTolerance",queryKey:ue.slippageTolerance()});function TC({skipInitialBuyFungible:e,getSwapQuotesStrategy:t}){let r=$(),o=b(O=>O.sellAmount),n=b(O=>O.amountType),p=b(O=>O.buyFungible)??(e?void 0:Hn),c=b(O=>O.sellFungible),u=b(O=>O.refuelEnabled),m=b(O=>O.setRefuelEnabled),{state:f,intervalMs:g}=st(),S=b(O=>O.resetQuote),w=b(O=>O.setHasNoRoutes),y=b(O=>O.setError),h=b(O=>O.setIsFetchingQuote),A=b(O=>O.setQuoteResponse),T=b(O=>O.setSelectedProviderIndex),{data:[I]}=ae(["enable-auto-slippage"]),{data:v}=ro(),{mutateAsync:x}=Ki(),{selectedSetting:E}=De(),P=I&&E.type==="auto",k=Bi();(0,Ze.useEffect)(()=>m(k),[k,p,m]);let H=Vi(),j=(0,ES.default)(H),M=(0,Ze.useMemo)(()=>{let O=n==="sell"?{amount:o,fungible:c}:{amount:o,fungible:p};return new N(O.amount).isGreaterThan(0)&&O.fungible?hl(O.amount,O.fungible.data.decimals).toString():(S(),"")},[n,p,S,o,c]),X=he(c?.data?.chain.id,p?.data.chain.id);(0,Ze.useEffect)(()=>{X&&r.capture("swapperRefuelDefault",{data:{default:k}})},[k,r,X]);let{data:Y}=vt(c?.data?.chain.id),{data:ye}=vt(p?.data.chain.id),z=(0,Ze.useMemo)(()=>Y?{chainId:Y.networkID,address:Y.address,resourceType:"address"}:{},[Y]),J=(0,Ze.useMemo)(()=>{if(X)return{chainId:ye?.networkID,address:ye?.address,resourceType:"address"}},[X,ye?.address,ye?.networkID]),{data:ce,error:be,isLoading:_}=xS({sellToken:c,buyToken:p,taker:z,takerDestination:J,amount:{value:H?M:"",type:n},slippageTolerance:P?void 0:v??void 0,refuelEnabled:u,enableAutoSlippage:P,pollInterval:g,pollerState:f,getSwapQuotesStrategy:t}),ct=(0,Ze.useCallback)(O=>{y(null),T(0),h(!1),w(!1),A(O);let ve=Ir(0,E.type,O);ve&&v!==ve&&x(ve)},[y,w,h,A,T,x,v,E.type]),Xt=(0,Ze.useCallback)(O=>{S(),h(!1),w(!0),y(O)},[S,y,w,h]);(0,Ze.useEffect)(()=>{f!=="paused"&&(_?h(!0):ce?ct(ce):be&&Xt(be))},[be,ce,_,ct,Xt,h,f,H]),(0,Ze.useEffect)(()=>{H?j===!1&&H&&(y(null),w(!1)):(y(new Re({key:"InsufficientSellAmount"})),w(!0))},[H,j,y,w])}i();a();var Hi=C(R());i();a();i();a();i();a();var FC=e=>e.isGreaterThanOrEqualTo(1)?"0,0.[00]":"0,0.[00000]",$o=e=>{let t=K(e);return bl(e,FC(t))};function Ht(e,t,r){if(t.lte(0))return"";let o=oe({chainId:e,resourceType:"nativeToken",slip44:F.getSlip44(e)}),n=F.getTokenDecimals(e),s=F.getTokenSymbol(e);if(!o||!n||!s)return"";let p=t.div(_a(n)),c=r[o];if(!c?.usd)return`${$o(p.toString())} ${s}`;let u=p.times(c.usd).toNumber(),m=cs(u,{minimumAmount:1e-6,decimalFormatSmallNumbers:"0.[00000]"});return m!=="$NaN"?m:""}i();a();var oo=e=>{let t=Math.floor(e/3600),r=Math.floor((e-t*3600)/60),o=e%60;if(t>0)return D.t("swapProviderEstimatedTimeHM",{hours:t,minutes:r});if(r>0){let n=o>0?r+1:r;return D.t("swapProviderEstimatedTimeM",{minutes:n})}return D.t("swapProviderEstimatedTimeS",{seconds:o})};i();a();var zo=e=>e.steps.reduce((t,r)=>t.plus(new N(r.nonIncludedNonGasFees)),new N(0));var IC=5*60,vS=e=>{if(pe(e))return Ue(Ei(e));let{sources:t}=e;return Ue(t)},CS=({quotes:e,buyTokenNetworkId:t,buyTokenDecimals:r,buyTokenSymbol:o})=>e.map(n=>{let{buyAmount:s}=n,p=Dr.get(t).formDisplayPrecision;return{name:vS(n),amount:`${sr(Z(s,r),p)} ${o}`}}),RS=({quotes:e,buyTokenNetworkId:t,buyTokenDecimals:r,buyTokenSymbol:o,networkIDForFees:n,networkFee:s,priceMap:p,t:c})=>e.filter(pe).map(u=>{let{buyAmount:m,executionDuration:f,steps:g}=u,w=zo(u).plus(s),y=g.find(A=>!!A.tool.logoURI),h=Dr.get(t).formDisplayPrecision;return{name:vS(u),fee:n&&p?c("swapProvidersFee",{fee:Ht(n,w,p)}):"",amount:`${sr(Z(m,r),h)} ${o}`,time:{text:oo(f),isFast:f<=IC},logoURI:y?.tool.logoURI??""}});i();a();var xC={aggregate(e){return e.reduce((t,r)=>({...t,gasLimit:t.gasLimit.plus(r.gasLimit)}),{networkID:F.ethereum.mainnetID,gasLimit:new N(0),maxFeePerGas:e[0].maxFeePerGas,maxPriorityFeePerGas:e[0].maxPriorityFeePerGas})}},EC={aggregate(e){return e.reduce((t,r)=>({...t,value:t.value+r.value}),{networkID:F.solana.mainnetID,value:0})}},Yn=e=>{if(!(!e||e.length===0)){if(Hl(e[0]))return xC.aggregate(e);if(Kl(e[0]))return EC.aggregate(e)}};function vC(){let{t:e}=rr(),{quoteResponse:t,isFetchingQuote:r,quotes:o,selectedProviderIndex:n}=V(),s=b(v=>v.setSelectedProviderIndex),p=b(v=>v.maxGasEstimation),c=b(v=>v.sellFungible),u=b(v=>v.buyFungible),m=c?.data.chain.id,{data:f}=fn(m,Yn(p)),S=Fr().map(v=>je(v)),{data:w={}}=gt({query:{data:S}}),y=t?.type==="xchain",h=(0,Hi.useMemo)(()=>u?y?RS({quotes:o,buyTokenNetworkId:u.data.chain.id,buyTokenDecimals:u.data.decimals,buyTokenSymbol:u.data.symbol,networkIDForFees:m,networkFee:f?.gasEstimationAmount??new N(0),priceMap:w,t:e}):CS({quotes:o,buyTokenNetworkId:u.data.chain.id,buyTokenDecimals:u.data.decimals,buyTokenSymbol:u.data.symbol}):[],[u,y,o,e,m,f?.gasEstimationAmount,w]),A=$(),{transition:T}=Vt(),I=(0,Hi.useCallback)(v=>{let x=h[v];A.capture("swapperProviderSelected",{data:{provider:x.name}}),s(v),T({type:"quoteSelected",quoteIndex:v})},[s,h,A,T]);return{rows:h,selectedProviderIndex:n,isLoadingProviders:r,setSelectedProviderIndex:I}}i();a();var PS=C(R());var $t=()=>{let{resume:e}=st(),t=b(r=>r.resetSwapper);return(0,PS.useCallback)(()=>{e(),t()},[t,e])};i();a();var ji=C(R());i();a();var DS=C(R());i();a();var kS=C(R());var $i=()=>{let e=G();return{cancelQuotes:(0,kS.useCallback)(async()=>{await e.cancelQueries({queryKey:ue.quotesScope()}),await dl(1)},[e])}};i();a();function zi(e){return{chain:"solana",type:"fungible",address:e.type==="SPL"?e.data.mintAddress:void 0,symbol:e.data.symbol??""}}function du(){let e=$(),t=b(n=>n.setBuyFungible),r=b(n=>n.buyFungible),{cancelQuotes:o}=$i();return(0,DS.useCallback)(async n=>{if(r&&eo(n,r))return;await o();let s=zi(n);e.capture("swapperUpdateToAsset",{data:s}),t(n)},[e,r,o,t])}i();a();i();a();var CC=l.enum(["pumpFun","moonshot"]),RC=l.object({type:CC,programId:l.string(),name:l.string(),iconUrl:l.string()}),PC=l.object({programs:l.array(RC)}),NS=e=>{try{return PC.parse(e)}catch{return}};var fu=[],zt=()=>{try{let e=Qe.getMultivariateAssignment("enabled-ugc-programs");if(!e)return fu;let t=JSON.parse(e);return NS(t)?.programs??fu}catch{return fu}};i();a();var BS=e=>{gu.getState().setAsset(e)},gu=Be(e=>({asset:void 0,setAsset(t){e({asset:t})}}));var kC=({onClose:e})=>{let t=du(),r=gu(s=>s.asset),o=(0,ji.useMemo)(()=>zt().find(p=>p.programId===r?.data?.ugcTokenMeta?.programId),[r]),n=(0,ji.useCallback)(async()=>{r&&(await t(r),e())},[t,r,e]);return{asset:r,program:o,handleConfirmPress:n}};i();a();var DC=()=>{let{review:e}=Sr();e?jr(e):On()};i();a();i();a();var yu=()=>{let e=G(),t=b();return{clearQuotes:async()=>{t.resetQuote(),await e.invalidateQueries({queryKey:ue.quotesScope()})}}};i();a();i();a();var BC=({networkID:e,unsignedTransaction:t,pendingActivityRows:r,accountIdentifier:o,accountSigner:n,storage:s,includeCallPayload:p})=>({accountSigner:n,accountIdentifier:o,networkID:e,unsignedTransaction:t,...p&&{callPayload:{from:t.from,to:t.to,data:t.data,value:t.value}},pendingTransactionInput:{ownerAddress:t.from,networkID:e,data:{nonce:"",unsignedTransaction:t,hash:""},type:"swap",display:{summary:r}},storage:s}),LS=async({transactions:e,transactionTypes:t,networkID:r,gasEstimation:o,pendingApprovalActivityRows:n,pendingSwapActivityRows:s,accountIdentifier:p,accountSigner:c,storage:u})=>{let m=!0,f="";for(let[g,S]of e.entries()){let w=o[g],y={gas:`0x${w.gasLimit.toString(16)}`,maxFeePerGas:`0x${w.maxFeePerGas.toString(16)}`,maxPriorityFeePerGas:`0x${w.maxPriorityFeePerGas.toString(16)}`},h=uo.parse({...S,...y}),T=t[g].includes("approval")?n:s,I=BC({networkID:r,accountIdentifier:p,pendingActivityRows:T.shift(),accountSigner:c,storage:u,unsignedTransaction:h,includeCallPayload:m});try{f="",f=await Jl(I)}catch(v){return{signature:f,error:v instanceof Error?v:new Error("Failed to process EVM swap transaction")}}m&&(m=!1)}return{signature:f}};i();a();var LC=({networkID:e,transaction:t,feePayer:r,connection:o,pendingActivityRows:n,accountIdentifier:s,accountSigner:p,storage:c})=>({accountSigner:p,accountIdentifier:s,transaction:t,feePayer:r,connection:o,opts:{skipPreflight:!0},pendingTransactionInput:{ownerAddress:r.toBase58(),networkID:e,data:{signature:""},type:"swap",display:{summary:n}},storage:c}),Wi=async({callerAddress:e,connection:t,networkID:r,transactions:o,pendingSwapActivityRows:n,accountIdentifier:s,accountSigner:p,storage:c})=>{let u=new ln.PublicKey(e),m="";for(let[f,g]of o.entries()){let S=lo(g,"bs58"),w=LC({networkID:r,transaction:S,feePayer:u,connection:t,pendingActivityRows:n[f],accountIdentifier:s,accountSigner:p,storage:c});try{m="",m=await em(w),f!==o.length-1&&await Ss({connection:t,signature:m})}catch(y){return{signature:m??"",error:y instanceof Error?y:new Error("Failed to process solana swap transaction")}}}return{signature:m??""}};var wu=({accountIdentifier:e,networkID:t})=>{let r=ee(),o=ds();return ie({mutationFn:async s=>{let{swapTransactions:p,type:c}=s;if(p.type==="evm"&&c==="eip155"){let{gasEstimation:u,pendingApprovalActivityRows:m,pendingSwapActivityRows:f}=s,{signature:g,error:S}=await Q.startSpan({name:"Process EVM Swap Txns"},()=>LS({transactions:p.transactions,transactionTypes:p.transactionTypes,networkID:t,gasEstimation:u,pendingSwapActivityRows:f,pendingApprovalActivityRows:m,accountIdentifier:e,accountSigner:o,storage:r}));return{networkID:t,chainType:c,id:g,error:S}}else if(p.type==="solana"&&c==="solana"){let{callerAddress:u,connection:m,pendingSwapActivityRows:f}=s;if(!m)throw new Error("missing connection");let{signature:g,error:S}=await Q.startSpan({name:"Process Solana Swap Txns"},()=>Wi({callerAddress:u,connection:m,networkID:t,transactions:p.transactions,pendingSwapActivityRows:f,accountIdentifier:e,accountSigner:o,storage:r}));return{networkID:t,chainType:c,id:g,error:S}}throw new Error("unsupported")}})};i();a();i();a();i();a();i();a();i();a();i();a();var UC="/currency_exchange/v1/rates",US=async()=>{let e=await U.api().get(`${UC}?from=USD&currencies=${Object.keys(ys).join(",")}`);if(!se(e))throw new Error("Failed to fetch exchange rates");return await e.data};var _C=L({hours:2}),hu=e=>B({queryKey:Ql.currencyExchangeRates(),queryFn:US,enabled:!!e,staleTime:_C,select(t){return e?t?.rates[e]:1}});i();a();var jo=C(R());var Ne=()=>{let{data:[e]}=ae(["enable-multi-currency"]),{data:t}=Ml(e),{data:r}=hu(t),o=(0,jo.useMemo)(()=>e&&t&&r?ys[t].symbol:_l.symbol,[e,t,r]),n=(0,jo.useCallback)(p=>!e||!t||!r?p:K(p).div(K(r)).toNumber(),[e,t,r]),s=(0,jo.useCallback)(p=>!e||!t||!r?p:K(p).times(K(r)).toNumber(),[e,t,r]);return(0,jo.useMemo)(()=>({symbol:o,toUsd:n,fromUsd:s}),[o,n,s])};var Yo=C(R());i();a();var _S=C(R());function QS(){let e=b(n=>n.setUiBuyAmountCryptoInput),t=b(n=>n.setUiBuyAmountLocalFiatInput),r=b(n=>n.primaryCurrency),{fromUsd:o}=Ne();return(0,_S.useCallback)(({amount:n,decimals:s,usdPrice:p})=>{if(r==="crypto")if(n===void 0)e("");else{let c=Z(n,s).toString(),u=mt(c);e(u)}else if(r==="fiat"&&p)if(n===void 0)t("");else{let c=Z(n,s).toString(),u=Fe(Number(c),p),m=o(u);t(m.toString())}},[o,r,t,e])}i();a();var OS=C(R());function MS(){let e=b(c=>c.setAmountType),t=b(c=>c.setSellAmount),r=b(c=>c.sellAmount),o=b(c=>c.setUiBuyAmountCryptoInput),n=$(),[s,p]=fo(r,t,Ko);return(0,OS.useCallback)(async({fungibleValue:c,shouldUpdateQuote:u,decimals:m})=>{let f=tr(c.substring(0,Xr)),g=mt(f,m);e("buy"),o(g),await n.capture("swapperChangeReceiveCryptoAmount",{data:{amount:g}}),u&&p(g)},[n,e,o,p])}i();a();var qS=C(R());function Yi(e){return(0,qS.useMemo)(()=>e===void 0?void 0:q(e),[e])}i();a();var Wo=C(R());var GS=()=>{let e=b(A=>A.primaryCurrency),t=b(A=>A.sellFungible),r=b(A=>A.buyFungible),o=b(A=>A.uiSellAmountLocalFiatInput),n=b(A=>A.uiBuyAmountLocalFiatInput),s=b(A=>A.amountType),p=b(A=>A.setUiSellAmountCryptoInput),c=b(A=>A.setUiBuyAmountCryptoInput),u=b(A=>A.switchTokensForBuy),m=b(A=>A.switchTokensForSell),{data:f}=Me({query:t?{data:q(t)}:void 0}),{data:g}=Me({query:r?{data:q(r)}:void 0}),{toUsd:S}=Ne(),w=(0,Wo.useMemo)(()=>({localFiatValue:o,decimals:t?.data.decimals??0,usdPrice:f?.usd}),[t?.data.decimals,o,f?.usd]),y=(0,Wo.useMemo)(()=>({localFiatValue:n,decimals:r?.data.decimals??0,usdPrice:g?.usd}),[r?.data.decimals,g?.usd,n]),h=(0,Wo.useCallback)(({localFiatValue:A,decimals:T,usdPrice:I})=>{let v=A?"":sr(0,T);if(I&&A){let x=S(A);v=tt(Number(x),I,T).toString()}return v},[S]);return(0,Wo.useCallback)(()=>{if(e==="fiat"){let A=h(w);p(A);let T=h(y);c(T)}s==="sell"?m():u()},[s,y,e,w,c,p,u,m,h])};var OC={chainId:W.Solana.Mainnet,slip44:"501",resourceType:"nativeToken"};function Au(){let e=b(E=>E.buyFungible),t=Yi(e),r=b(E=>E.sellFungible),o=b(E=>E.primaryCurrency),n=b(E=>E.uiBuyAmountCryptoInput),s=b(E=>E.uiBuyAmountLocalFiatInput),p=b(E=>E.amountType),c=b(E=>E.setAmountType),u=b(E=>E.setSellAmount),m=b(E=>E.uiSellAmountCryptoInput),f=b(E=>E.uiSellAmountLocalFiatInput),g=e?.data.decimals??0,{currentQuote:S}=V(),w=e?q(e):OC,{data:y}=Me({query:{data:w}}),h=GS(),A=QS(),T=MS(),{toUsd:I}=Ne();(0,Yo.useEffect)(()=>{p==="sell"&&t&&A({amount:S?.buyAmount,decimals:g,usdPrice:y?.usd})},[p,t,S?.buyAmount,g,y?.usd,A]),(0,Yo.useEffect)(()=>{if(he(r?.data.chain.id,e?.data.chain.id)&&p==="buy"){if(c("sell"),o==="crypto")u(m);else if(y?.usd){let E=I(f),P=tt(E,y.usd,g);u(P.toString())}}},[p,e,g,y,o,r,c,u,I,m,f]);let v=(0,Yo.useCallback)(E=>{T({fungibleValue:E,decimals:g,shouldUpdateQuote:!0})},[g,T]),x=(0,Yo.useCallback)(()=>{h()},[h]);return{cryptoAmountInputValue:n,handleCryptoAmountInputChange:v,localFiatAmountInputValue:s,handleSwitchTokensClick:x}}i();a();var pt=C(R());i();a();var MC=3,qC=new N(356190),GC=e=>we.get(e).averageNetworkFee.times(MC);function Xi({sellFungible:e,totalFees:t,transactionSpeedsToUnitCost:r,minRequiredBalance:o,isBridge:n=!1}){let{decimals:s,amount:p,chain:c}=e.data,u=new N(p),m=Le(e.type),{source:f,destination:g}=o,w=VC({isNativeToken:m,network:c,totalFees:t,isBridge:n,transactionSpeedsToUnitCost:r}).plus(g),y=m?KC(u,w,f):u.toString();return Al(y,s)}function VC({isNativeToken:e,network:t,totalFees:r,isBridge:o,transactionSpeedsToUnitCost:n}){if(!e)return r;let s=we.get(t.id),p=o?s.bridgeDefaultFee:s.swapDefaultFee,c=n?.standard;return $l(c)&&(p=qC.multipliedBy(c.maxFeePerGas.plus(c.maxPriorityFeePerGas))),p.plus(GC(t.id))}function KC(e,t,r){let o=r.plus(t);return e.gt(o)?e.minus(o).toString():e.toString()}i();a();var VS=C(R());function KS(){let e=b(m=>m.primaryCurrency),t=b(m=>m.setUiSellAmountLocalFiatInput),r=b(m=>m.setUiBuyAmountLocalFiatInput),o=b(m=>m.setAmountType),n=b(m=>m.setPrimaryCurrency),s=b(m=>m.setUiSellAmountCryptoInput),p=b(m=>m.setSellAmount),c=b(m=>m.setUiBuyAmountCryptoInput),{fromUsd:u}=Ne();return(0,VS.useCallback)(({buyAmountFromQuote:m,buyFungibleDecimals:f,buyFungibleUsdPrice:g,sellAmountFromQuote:S,sellFungibleUsdPrice:w})=>{if(e==="crypto"){if(o("sell"),w&&S){let y=Fe(Number(S),w),h=u(y);t(h.toString()),p(S.toString())}if(g&&m){let y=Z(m,f).toString(),h=Fe(Number(y),g),A=u(h);r(A.toString())}n("fiat")}if(e==="fiat"){if(S&&s(S),g&&m){let y=Z(m,f).toString();c(y)}n("crypto")}},[u,e,p,o,c,r,n,s,t])}i();a();var HS=C(R());function $S(){let e=b(n=>n.primaryCurrency),t=b(n=>n.setUiSellAmountCryptoInput),r=b(n=>n.setUiSellAmountLocalFiatInput),{fromUsd:o}=Ne();return(0,HS.useCallback)(({amount:n,decimals:s,usdPrice:p})=>{if(e==="crypto")if(n===void 0)t("0");else{let c=Z(n,s).toString();t(c)}else if(e==="fiat"&&p)if(n===void 0)r("0");else{let c=Z(n,s).toString(),u=Fe(Number(c),p),m=o(u);r(m.toString())}},[o,e,t,r])}i();a();var zS=C(R());function jS(){let e=b(p=>p.setAmountType),t=b(p=>p.setUiSellAmountCryptoInput),r=b(p=>p.setSellAmount),o=b(p=>p.sellAmount),[n,s]=fo(o,r,Ko);return(0,zS.useCallback)(({fungibleValue:p,decimals:c,shouldUpdateQuote:u})=>{let m=tr(p.substring(0,Xr)),f=mt(m,c);e("sell"),t(f),u&&s(f)},[e,t,s])}i();a();var WS=C(R());function YS(){let e=b(c=>c.setAmountType),t=b(c=>c.setSellAmount),r=b(c=>c.sellAmount),o=b(c=>c.setUiSellAmountLocalFiatInput),{toUsd:n}=Ne(),[s,p]=fo(r,t,Ko);return(0,WS.useCallback)(({localFiatValue:c,shouldUpdateQuote:u,decimals:m,priceData:f})=>{let g=tr(c.substring(0,Xr)),S=mt(g,2);if(e("sell"),o(S),u&&f?.usd){let w=n(c),y=tt(w,f.usd,m);p(y.toString())}},[e,o,p,n])}i();a();var uw=C(R());i();a();var XS=C(R());var HC={"Allbridge's fee":"destinationNetwork","Allbridge Liquidity Fee":"liquidityProvider"},JS=()=>{let{currentQuote:e}=V(),{destinationNetworkFee:t,liquidityFee:r}=(0,XS.useMemo)(()=>{function o(s){return s?{amount:new K(s.amount??0),uiAmount:`$${s.amountUSD??""}`,pct:parseFloat(s.percentage??"0")}:Tr}let n=new Map([]);return e&&pe(e)&&(n=e.steps.flatMap(s=>s.includedFeeCosts).reduce((s,p)=>{if(!p)return s;let c=HC[p.name];return c&&s.set(c,p),s},n)),{destinationNetworkFee:o(n.get("destinationNetwork")),liquidityFee:o(n.get("liquidityProvider"))}},[e]);return{destinationNetworkFee:t,liquidityFee:r}};i();a();var ZS=C(R());var $C={calculate({currentQuote:e,gasEstimationPriceData:t}){return{amount:(e&&t?.gasEstimationAmount)??new K(0),uiAmount:(e&&t?.gasEstimationPriceUSD)??"",pct:0}}},zC={calculate({currentQuote:e,gasEstimationPriceData:t,maxGasEstimationPriceData:r}){return{amount:(e&&t?.gasEstimationAmount)??new K(0),uiAmount:(e&&t?.gasEstimationPriceUSD)??"",maxAmount:(e&&r?.gasEstimationAmount)??new K(0),maxUiAmount:(e&&r?.gasEstimationPriceUSD)??"",pct:0}}},ew=()=>{let{currentQuote:e}=V(),t=b(m=>m.sellFungible),r=b(m=>m.gasEstimation),o=b(m=>m.maxGasEstimation),n=t?.data.chain.id??W.Solana.Mainnet,{data:s,isFetching:p}=fn(n,Yn(r)),{data:c}=fn(n,Yn(o));return{networkFee:(0,ZS.useMemo)(()=>F.isEVMNetworkID(n)?zC.calculate({currentQuote:e,gasEstimationPriceData:s,maxGasEstimationPriceData:c}):$C.calculate({currentQuote:e,gasEstimationPriceData:s}),[e,s,c,n]),isFetchingGasEstimationPrice:p}};i();a();var tw=C(R());var jC={calculate(){return Tr}},WC={calculate({currentQuote:e,networkID:t,priceMap:r}){let o=zo(e);return{amount:o,uiAmount:Ht(t,o,r),pct:0}}},rw=({priceMap:e})=>{let{currentQuote:t}=V(),o=b(s=>s.sellFungible)?.data.chain.id??W.Solana.Mainnet;return{nonIncludedFees:(0,tw.useMemo)(()=>t&&pe(t)?WC.calculate({currentQuote:t,networkID:o,priceMap:e}):jC.calculate(),[o,t,e])}};i();a();var ow=C(R());var YC={calculate(e){let t=(e?.fees??[]).find(r=>r.type==="phantom");return{amount:new K(t?.amount??0),uiAmount:"",pct:t?.percentage??0}}},XC={calculate(e){let t=e.steps.flatMap(r=>r.feeCosts).find(r=>r.name==="phantom");return{amount:new K(t?.amount??0),uiAmount:"",pct:parseFloat(t?.percentage??"0")}}},nw=()=>{let{currentQuote:e}=V();return{phantomFee:(0,ow.useMemo)(()=>hr(e)?YC.calculate(e):pe(e)?XC.calculate(e):Tr,[e])}};i();a();var sw=C(R());var JC={calculate({currentQuote:e,networkID:t,priceMap:r}){let o=(e?.fees??[]).find(n=>n.name==="Token account deposits");return{amount:new K(o?.amount??0),uiAmount:o?.amount?Ht(t,new K(o.amount),r):"",pct:o?.percentage??0}}},ZC={calculate(){return Tr}},iw=({priceMap:e})=>{let{currentQuote:t}=V(),o=b(s=>s.sellFungible)?.data.chain.id??W.Solana.Mainnet;return{tokenAccountFee:(0,sw.useMemo)(()=>hr(t)?JC.calculate({currentQuote:t,networkID:o,priceMap:e}):ZC.calculate(),[t,o,e])}};i();a();var aw=C(R());var pw={calculate:({tokenAccountFee:e,networkFee:t,nonIncludedFees:r,networkID:o,priceMap:n})=>{let s=e.amount.plus(t.amount).plus(r.amount);return{amount:s,uiAmount:Ht(o,s,n),pct:0}}},eR={calculate:({tokenAccountFee:e,networkFee:t,nonIncludedFees:r,networkID:o,priceMap:n})=>{let s=pw.calculate({tokenAccountFee:e,networkFee:t,nonIncludedFees:r,networkID:o,priceMap:n}),p=e.amount.plus(t.maxAmount??0).plus(r.amount);return{...s,maxAmount:p,maxUiAmount:Ht(o,p,n)}}},cw=({tokenAccountFee:e,networkFee:t,nonIncludedFees:r,priceMap:o})=>{let s=b(c=>c.sellFungible)?.data.chain.id??W.Solana.Mainnet;return{totalFees:(0,aw.useMemo)(()=>F.isEVMNetworkID(s)?eR.calculate({tokenAccountFee:e,networkFee:t,nonIncludedFees:r,networkID:s,priceMap:o}):pw.calculate({tokenAccountFee:e,networkFee:t,nonIncludedFees:r,networkID:s,priceMap:o}),[t,s,r,o,e])}};var at=()=>{let e=b(S=>S.isFetchingGasEstimation),t=Fr(),{data:r={}}=gt({query:{data:t.map(S=>je(S))}}),{networkFee:o,isFetchingGasEstimationPrice:n}=ew(),s=e||n,{phantomFee:p}=nw(),{tokenAccountFee:c}=iw({priceMap:r}),{nonIncludedFees:u}=rw({priceMap:r}),{totalFees:m}=cw({tokenAccountFee:c,networkFee:o,nonIncludedFees:u,priceMap:r}),{destinationNetworkFee:f,liquidityFee:g}=JS();return(0,uw.useMemo)(()=>({phantomFee:p,networkFee:o,tokenAccountFee:c,totalFees:m,nonIncludedFees:u,destinationNetworkFee:f,liquidityFee:g,isFetchingFees:s}),[p,o,c,m,u,f,g,s])};i();a();var tR=20;function Ji(e,t){let r=ue.optimisticUpdates(),o=e.getQueryData(r)??[];e.setQueryData(r,[...o.filter(n=>n.id!==t.id),t])}function lw(e,t){let r=ue.optimisticUpdates(),o=e.getQueryState(r);if(o){let n=t??Date.now(),s=o.dataUpdatedAt,p=L({seconds:tR});return n-s>p?(e.removeQueries({queryKey:r}),[]):o?.data??[]}else return[]}function Xo(e){return`${e.type}-${e.data.key}`}i();a();var bu=C(R());var rR=.05,oR=({sellNetworkId:e,buyNetworkId:t,destinationMinRequiredBalance:r,priceMap:o})=>{if(!e||!t||!o)return new K(0);let n=oe(je(F.getChainID(e))),s=oe(je(F.getChainID(t))),p=o[n]?.usd,c=o[s]?.usd;if(!p||!c)return new K(0);let u=Z(r.toNumber(),F.getTokenDecimals(t)),m=Fe(u.toNumber(),c),f=tt(m,p,F.getTokenDecimals(e));return new K(f).times(1+rR)},Jo=()=>{let e=Gi(),t=Er(),r=b(h=>h.sellFungible)??t,o=b(h=>h.buyFungible)??e,n=r.data.chain.id,s=o?.data.chain.id,p=s??n,c=he(n,s),{fungible:u}=rt({key:o?.data.key}),{data:m}=te(),f=(0,bu.useMemo)(()=>m?.addresses.find(h=>h.networkID===p),[m,p]),S=Fr().map(h=>je(h)),{data:w}=gt({query:{data:S},queryOptions:{refetchInterval:!1}}),{data:y=0}=hs(f);return(0,bu.useMemo)(()=>{if(u)return new K(0);let h=new K(y);return c?oR({sellNetworkId:n,buyNetworkId:s,destinationMinRequiredBalance:h,priceMap:w}):h},[u,y,c,n,s,w])};i();a();var Tu=C(R());var Zo=()=>{let e=Er(),r=(b(p=>p.sellFungible)??e).data.chain.id,{data:o}=te(),n=(0,Tu.useMemo)(()=>o?.addresses.find(p=>p.networkID===r),[o,r]),{data:s=0}=hs(n);return(0,Tu.useMemo)(()=>new K(s),[s])};function Fu(){let e=G(),t=Er(),r=b(re=>re.sellFungible)??t,o=b(re=>re.buyFungible),n=b(re=>re.primaryCurrency),s=b(re=>re.isFetchingQuote),p=b(re=>re.sellAmount),c=b(re=>re.uiSellAmountCryptoInput),u=b(re=>re.setSellFungible),m=b(re=>re.uiSellAmountLocalFiatInput),f=b(re=>re.amountType),g=b(re=>re.setSwapFilter),S=Yi(r),w=r?.data.key,{fungible:y}=rt({key:w}),h=r?.data.chain.id,A=o?.data.chain.id,{totalFees:T}=at(),{data:I}=ws({networkID:h}),v=he(h,A),x=Zo(),E=Jo(),P=r?.data.amount,k=r?r.data.decimals:0,H=(0,pt.useMemo)(()=>Xi({sellFungible:r,totalFees:T.amount,transactionSpeedsToUnitCost:F.isEVMNetworkID(r.data.chain.id)?I:void 0,isBridge:v,minRequiredBalance:{source:x,destination:E}}),[r,T.amount,I,v,E,x]),j=(0,pt.useMemo)(()=>f==="buy"?new K(c).isLessThanOrEqualTo(new K(H)):p===""?!0:new K(p).isLessThanOrEqualTo(new K(H)),[f,H,p,c]),{currentQuote:M}=V(),{data:X}=Me({query:{data:q(r)}}),Y=X?.usd,{data:ye}=Me({query:o?{data:q(o)}:void 0}),z=KS(),J=jS(),ce=YS(),be=$S(),_=(0,pt.useCallback)(()=>{let re=M?.sellAmount?Z(M?.sellAmount,k).toString():p,He=M?.buyAmount,ut=o?.data.decimals??0,Jt=ye?.usd;z({buyAmountFromQuote:He,buyFungibleDecimals:ut,buyFungibleUsdPrice:Jt,sellAmountFromQuote:re,sellFungibleUsdPrice:Y})},[M?.sellAmount,M?.buyAmount,k,p,o?.data.decimals,ye?.usd,z,Y]),ct=(0,pt.useCallback)(re=>{J({fungibleValue:re,decimals:k,shouldUpdateQuote:!0})},[k,J]),Xt=(0,pt.useCallback)(re=>{ce({priceData:X,localFiatValue:re,decimals:k,shouldUpdateQuote:!0})},[k,X,ce]),{fromUsd:O}=Ne(),ve=(0,pt.useCallback)(re=>{g(re);let He=K(H).multipliedBy(re);if(n==="crypto")J({fungibleValue:He.toString(),decimals:k,shouldUpdateQuote:!0});else if(Y){let ut=Fe(Number(He),Y),Jt=O(ut).toString();ce({priceData:X,localFiatValue:Jt,decimals:k,shouldUpdateQuote:!0})}},[g,H,n,Y,J,k,ce,X,O]);(0,pt.useEffect)(()=>{let re=lw(e);if(y){let He={...y},ut=re.find(Jt=>Jt.id===Xo(y));ut&&y.data.amount!==ut.amount&&(He.data={...He.data,amount:ut.amount}),He.data.amount!==P&&u(He)}},[y,P,u,e]),(0,pt.useEffect)(()=>{f==="buy"&&S&&be({amount:M?.sellAmount,decimals:k,usdPrice:Y})},[f,M?.sellAmount,k,X?.usd,S,Y,be]);let rs=Vi();return{maxSellAmount:H,sellFungible:r,notEnoughAssets:!j,uiSellAmountCryptoInput:c,uiSellAmountLocalFiatInput:m,hasMinimumSellAmount:rs,isFetchingQuote:s,handleToggleCurrencyClick:_,handleFiatAmountInputChange:Xt,handleCryptoAmountInputChange:ct,handleSwapFilterClick:ve}}i();a();var et=C(R());i();a();var nR=50,mw=({includeOwnedAssets:e,sellAsset:t,ownedAssets:r,searchAssets:o})=>{if(!e||!t)return o;let n=g=>oe(q(g)),s=n(t),p=t.data.chain.id,c=r.filter(g=>n(g)!==s&&F.compareNetworkID(p,g.data.chain.id)===0&&g.type!=="CompressedSPL").slice(0,nR-1),u=c.map(g=>[n(g),g]),m=new Map(u),f=o.filter(g=>!m.has(n(g)));return[...c,...f]};function sR({enableQuery:e=!0}={}){let{sellFungible:t,searchQuery:r,setSearchQuery:o,setNumSearchResults:n}=b(),s=yl(r,150),{fungibles:p,isLoadingTokens:c,isRefetchingTokens:u,tokensError:m}=Ct(),f=Kt(),[g,S]=(0,et.useState)(""),{data:w}=te(),y=(0,et.useMemo)(()=>w?.addresses??[],[w]),{data:h,isSuccess:A,isFetching:T,error:I}=Kr({query:s,networkIds:[t?.data.chain.id??F.solana.mainnetID],enableQuery:e,keepPreviousData:!0,searchContext:"swapper",supportedNetworkIds:f,chainAddresses:y}),v=!A&&T,x=(0,et.useMemo)(()=>p.map(M=>[oe(q(M)),M]),[p]),E=(0,et.useMemo)(()=>{let M=ll(x.map(([Y])=>Y).join("~")),X=ml(M);return ul(X)},[x]),P=(0,et.useMemo)(()=>new Map(x),[E]),k=(0,et.useMemo)(()=>{if(!h||h.length===0)return[];S(ns());let M=h.reduce((X,Y)=>{let ye="";if(Le(Y.type)){if(t&&Le(t.type)&&Y.data.chain.id===t.data.chain.id)return X;ye=oe(je(F.getChainID(Y.data.chain.id)))}else{if(t&&Y.data.tokenAddress===t.data.tokenAddress)return X;ye=oe(q(Y))}let z,J=P.get(ye);return J&&J.data.balance?z=J:z=Y,z&&X.push(z),X},[]);return n(M.length),M},[P,h,t,n]),H=$();(0,et.useEffect)(()=>{A&&k.length===0&&H.capture("swapperNullSearchResult",{data:{searchQuery:s}})},[A,k,s,H]);let j=(0,et.useMemo)(()=>mw({includeOwnedAssets:!s,sellAsset:t,ownedAssets:p,searchAssets:k}),[s,p,k,t]);return(0,et.useMemo)(()=>({assets:j,initialFilterKey:t?.data.chain.id,isLoading:c||v,isFetching:c||v||u,error:m??I,searchRef:g,setSearchQuery:o}),[j,c,v,u,m,I,t,g,o])}i();a();var Ee=C(R()),fw=C(mu());i();a();var Zi="https://help.phantom.app/hc/en-us/articles/27085326202515";var no=[.3,.5,1],iR=no.map(e=>`${e}%`),gw=[...iR,"custom"],yw=gw.length-1,aR=5,pR=2,Iu=.1,xu=30,cR=1,dw=(e,t)=>{if(!e)return;let r=parseFloat(e);if(isNaN(r))return{message:t("swapSlippageSettingsCustomInvalidValue"),severity:"critical"};if(r>xu)return{message:t("swapSlippageSettingsCustomMaxError",{maxSlippage:xu}),severity:"critical"};if(r>cR)return{message:t("swapSlippageSettingsHighSlippageWarning"),severity:"warning"};if(r<Iu)return{message:t("swapSlippageSettingsCustomMinError",{minSlippage:Iu}),severity:"critical"}},uR=e=>e&&no.indexOf(e)!==-1?no.indexOf(e):e?yw:0,lR=e=>e&&no.indexOf(e)===-1?`${e}`:"",mR=({onDismiss:e})=>{let{t}=rr(),r=$(),{data:o,isFetched:n}=ro(),{mutateAsync:s}=Ki(),{selectedSetting:p,isFetched:c,setSlippageSetting:u}=De(),[m,f]=(0,Ee.useState)(p.type),g=(0,fw.default)(c),[S,w]=(0,Ee.useState)(!1),[y,h]=(0,Ee.useState)(0),[A,T]=(0,Ee.useState)(),[I,v]=(0,Ee.useState)(""),[x,E]=(0,Ee.useState)(),P=m==="auto",k=y===yw,H=P?!1:x?.severity==="critical"||k&&!I;(0,Ee.useEffect)(()=>{g!==c&&m!==p.type&&f(p.type)},[g,c,p.type,m]);let j=(0,Ee.useCallback)(J=>{y!==J&&(E(void 0),h(J),no[J]?(T(no[J]),v("")):T(void 0))},[y]),M=(0,Ee.useCallback)(J=>{let ce=tr(J.substring(0,aR)),be=mt(ce,pR),_=parseFloat(be);if(isNaN(_)){E(void 0),v("");return}v(be),E(dw(be,t))},[t]),X=(0,Ee.useCallback)(()=>{if(P)r.capture("slippageChanged",{data:{slippageType:"auto"}});else if(k){let J=parseFloat(I??"");s(J),r.capture("slippageChanged",{data:{slippageValue:J,slippageType:"custom"}}),T(void 0)}else A&&(s(A),r.capture("slippageChanged",{data:{slippageValue:A,slippageType:"preset"}}),v(""));u({type:m}),e()},[P,u,m,e,k,A,I,s,r]),Y=(0,Ee.useCallback)(()=>{let J=uR(o),ce=lR(o);h(J),T(no[J]),v(ce),E(dw(ce,t)),f(p.type)},[o,t,p.type]),ye=(0,Ee.useCallback)(()=>{f(m==="auto"?"fixed":"auto")},[m,f]),z=(0,Ee.useMemo)(()=>({title:t("swapSlippageSettingsTitle"),subtitle:t("swapSlippageSettingsSubtitle",{minSlippage:Iu,maxSlippage:xu}),custom:t("swapSlippageSettingsCustom"),auto:t("swapSlippageSettingsAuto"),autoSubtitle:t("swapSlippageSettingsAutoSubtitle"),learnMore:t("commandLearnMore"),button:t("commandConfirm")}),[t]);return(0,Ee.useEffect)(()=>{!S&&n&&(Y(),w(!0))},[n,S,Y]),{options:gw,selectedIndex:y,customSlippageValue:I,i18nStrings:z,error:x,submitDisabled:H,isAutoEnabled:P,autoLearnMoreUrl:Zi,onConfirm:X,onDismiss:e,onSelectOption:j,onChangeCustomSlippage:M,onResetToDefault:Y,onToggleAutoSlippage:ye}};i();a();var so=C(R());i();a();i();a();var Sw=()=>[{chainId:"solana:101",address:"2b1kV6DkPAnxd5ixfnxCpjxmKwqjjaYmCZfHsFu24GXo",resourceType:"address"}];var ww=e=>{let t=[];return fR(e)&&!gR(e)&&t.push({level:"warn",titleKey:"swapAssetWarningPermanentDelegate"}),(e.data.spamStatus==="NOT_VERIFIED"||e.data.spamStatus==="SPAM"||e.data.spamStatus==="POSSIBLE_SPAM")&&t.push({level:"info",titleKey:"swapAssetWarningUnverified"}),t};function fR(e){let t=e.data;return t?.mintExtensions?t.mintExtensions.some(r=>r.extension==="permanentDelegate"):!1}function gR(e){let t=q(e),r=Sw();return t.resourceType==="address"?r.some(o=>o.resourceType===t.resourceType&&o.address===t.address&&o.chainId===t.chainId):!1}i();a();var Aw=C(R());i();a();function hw(){return[new Ru,new Eu,new Cu,new vu]}var Eu=class{isEditable({sellAsset:t}){return!!t&&F.isSolanaNetworkID(t.data.chain.id)}},vu=class{isEditable({currencyDenomination:t}){return t==="crypto"}},Cu=class{isEditable({sellAsset:t,buyAsset:r}){return!he(t?.data.chain.id,r?.data.chain.id)}},Ru=class{isEditable(){return Qe.isFeatureEnabled("enable-swapper-exact-out")}};var Pu=({sellAsset:e,buyAsset:t,currencyDenomination:r})=>{let o=(0,Aw.useMemo)(()=>hw(),[]);return t?o.every(n=>n.isEditable({sellAsset:e,buyAsset:t,currencyDenomination:r})):!1};i();a();var Tw=C(R());i();a();var ku=class{isSupported(t,r,o,n,s){switch(t){case"sell":return!!(o&&s);case"buy":return!1}}},Du=class{isSupported(t){return t!=="buy"}};function bw(){return[new ku,new Du]}var ea=(e,t,r,o,n)=>(0,Tw.useMemo)(()=>bw(),[]).every(p=>p.isSupported(e,t,r,o,n));i();a();var Nu=class{format(t,r){return`${sr(t)} ${r}`}},Bu=class{format(t,r){return`${ps(t,{compact:!0})} ${r}`}},Lu=class{format(t){return ps(t,{compact:!0})}};function Qu(e){switch(e){case"full":return new Nu;case"compact":return new Bu;case"compactNoSymbol":return new Lu}}var Uu=class{format(t,r,o){return`${sr(tt(Number(t),o,r.data?.decimals??0))} ${r.data.symbol??""}`}},ta=class{format(t,r,o){return ps(tt(Number(t),o,r.data?.decimals??0),{compact:!0})}},_u=class{constructor(t){this.noSymbolDisplayStrategy=t}format(t,r,o){return`${this.noSymbolDisplayStrategy.format(t,r,o)} ${r.data?.symbol??""}`}};function Ou(e){switch(e){case"full":return new Uu;case"compact":return new _u(new ta);case"compactNoSymbol":return new ta}}var yR=lt?[.25,.5,1]:[.5,1],Fw=lt?Qu("full"):Qu("compactNoSymbol"),Iw=lt?Ou("full"):Ou("compact"),SR={chainId:W.Solana.Mainnet,slip44:"501",resourceType:"nativeToken"},wR=()=>{let t=b(O=>O.amountType)==="sell"?"sell":"buy",r=b(O=>O.primaryCurrency),{symbol:o,toUsd:n}=Ne(),s=r==="fiat"?o:void 0,p=b(O=>O.swapFilter),c=b(O=>O.setSwapFilter),u=Nl(),{handleCryptoAmountInputChange:m,handleFiatAmountInputChange:f,sellFungible:g,uiSellAmountCryptoInput:S,isFetchingQuote:w,notEnoughAssets:y,hasMinimumSellAmount:h,handleToggleCurrencyClick:A,uiSellAmountLocalFiatInput:T,handleSwapFilterClick:I,maxSellAmount:v}=Fu(),x=b(O=>O.buyFungible),E=x?q(x):SR,P=vw(E),k=vw(q(g)),H=ea("sell",g,k,x,P),j=(0,so.useMemo)(()=>({fungible:g,amount:r==="crypto"?S:T,secondaryCryptoSubtitle:r==="fiat"?xw(n(T),g,Iw,k):void 0,balance:Ew(g,Fw,u,v),isAmountLoading:w&&t!=="sell",isSecondaryCryptoSubtitleLoading:w,isWarning:y||!h,isAmountEditable:!0,supportsCurrencyDenominationSwitching:H,amountPrefix:s,filters:yR,selectedFilter:p,warnings:[],usdAmount:k?Fe(Number(S),k):0}),[g,r,S,T,k,u,v,w,t,y,h,H,s,p,n]),{handleSwitchTokensClick:M,cryptoAmountInputValue:X,localFiatAmountInputValue:Y,handleCryptoAmountInputChange:ye}=Au(),z=ea("buy",g,k,x,P),J=Pu({sellAsset:g,buyAsset:x,currencyDenomination:r}),ce=(0,so.useMemo)(()=>{if(x)return{fungible:x,perspective:"buy",balance:Ew(x,Fw,u),amount:r==="crypto"?X:Y,secondaryCryptoSubtitle:r==="fiat"?xw(n(Y),x,Iw,P):void 0,isAmountLoading:w&&t!=="buy",isSecondaryCryptoSubtitleLoading:w,isWarning:!1,isAmountEditable:J,supportsCurrencyDenominationSwitching:z,amountPrefix:s,filters:[],selectedFilter:void 0,warnings:ww(x),usdAmount:P?Fe(Number(X),P):0}},[x,u,r,X,Y,P,w,t,J,z,s,n]),be=(0,so.useCallback)((O,ve)=>{switch(c(void 0),O){case"sell":r==="crypto"?m(ve):f(ve);break;case"buy":if(r==="fiat")return;ye(ve);break}},[c,r,m,f,ye]),_=$(),ct=(0,so.useCallback)(async()=>{t==="sell"&&(A(),await _.capture("swapperToggleQuoteCurrency",{data:{value:r==="fiat"?"native":"fiat"}}))},[t,_,r,A]),Xt=(0,so.useCallback)((O,ve)=>{ve==="sell"&&I(O)},[I]);return{switchPerspective:M,sell:j,buy:ce,setAmount:be,switchCurrencyDenomination:ct,setFilter:Xt,currencyDenomination:r}};function xw(e,t,r,o){let n=t.data?.symbol??"",s=Number(e);return o?r.format(s,t,o):`0 ${n}`}function Ew(e,t,r,o){let n=o?K(o):e.data?.balance;if(!(!n||r))return t.format(n,e.data?.symbol??"")}var vw=e=>{let{data:t}=Me({query:{data:e}});return t?.usd};i();a();var hR=["lastSubmittedPairs","slippageTolerance"],Mu=e=>{let t=String(e.queryKey[1]);return hR.includes(t)};i();a();var Cw=C(R());function qu(e){let{t}=rr();return(0,Cw.useMemo)(()=>{let{networkFee:r,phantomFee:o,tokenAccountFee:n,nonIncludedFees:s,destinationNetworkFee:p,liquidityFee:c}=e;return{networkFee:{label:t("swapQuoteNetwork"),value:r.uiAmount,maxValue:r.maxUiAmount},bridgeFee:{label:t("swapQuoteBridgeFee"),value:s.uiAmount,maxValue:s.maxUiAmount},tokenAccountFee:{label:t("swapQuoteOneTimeTokenAccount"),value:n.uiAmount},phantomFeePercent:{label:o.pct?t("swapQuoteFeeDisclaimer",{feePercentage:wl(o.pct)}):"",value:o.pct?`${o.pct}`:""},destinationNetworkFee:{label:t("swapQuoteDestinationNetwork"),value:p.uiAmount},liquidityProviderFee:{label:t("swapQuoteLiquidityProvider"),value:c.uiAmount}}},[e,t])}i();a();var Rw=C(R());function AR(){let e=$(),t=b(g=>g.buyFungible),r=b(g=>g.sellFungible),o=b(g=>g.setBuyFungible),n=b(g=>g.setSellFungible),s=b(g=>g.setSellAmount),p=b(g=>g.setUiSellAmountCryptoInput),c=b(g=>g.resetQuote),u=b(g=>g.setSwapFilter),m=r?.data.amount,{cancelQuotes:f}=$i();return(0,Rw.useCallback)(async g=>{if(r&&eo(g,r)){g&&g.data.amount!==m&&(n(g),u(void 0));return}await f();let S=zi(g);e.capture("swapperUpdateFromAsset",{data:S}),n(g),u(void 0),t&&r&&eo(g,t)&&o(r),g&&r?.data.chain.id!==g.data.chain.id&&(c(),o(void 0),s(""),p(""))},[e,t,f,m,c,r,o,s,n,u,p])}i();a();var Uw=C(R());i();a();var vr=C(R());i();a();i();a();i();a();var ra=class{sections(t){let r=zt().map(s=>s.programId),o=t.reduce((s,p)=>{if(p.data.spamStatus==="VERIFIED")return s.verified.push(p),s;if(p.type!=="SPL"||!p.data.ugcTokenMeta||p.data.ugcTokenMeta.status==="graduated"||!r.includes(p.data.ugcTokenMeta.programId))return s.unverified.push(p),s;let c=p.data.ugcTokenMeta.programId,u=s[c]??[];return u.push(p),s[c]=u,s},{verified:[],unverified:[]}),n=zt().map(s=>{let p=o[s.programId]??[];return{id:s.programId,header:{title:D.t("swapSectionTitleTokens",{section:s.name}),iconUrl:s.iconUrl},data:p,behavior:"collapsible"}});return[{id:"verified",data:o.verified,behavior:"static"},{id:"unverified",header:{title:D.t("swapUnverifiedTokens")},data:o.unverified,behavior:"collapsible"},...n]}};i();a();var oa=class{sections(t){let r=t.filter(n=>n.data.spamStatus==="VERIFIED"),o=t.filter(n=>n.data.spamStatus!=="VERIFIED");return[{id:"verified",data:r,behavior:"static"},{id:"unverified",header:{title:D.t("swapUnverifiedTokens")},data:o,behavior:"static"}]}};function kw(e){return e?new ra:new oa}i();a();var Nw=C(R());i();a();var na=C(R());var Dw=()=>{let{data:e}=it({key:"lastSelectedTokens",queryKey:ue.lastSelectedTokens()}),t=(0,na.useMemo)(()=>e?JSON.parse(e):{},[e]),{mutateAsync:r}=Tt({key:"lastSelectedTokens",queryKey:ue.lastSelectedTokens()}),o=(0,na.useCallback)(async n=>{let s={...t,[n]:!0};await r(JSON.stringify(s))},[t,r]);return{lastSelectedTokens:t,storeSelectedToken:o}};i();a();var sa=class{constructor(t,r){this.lastSelectedTokens=t;this.storeSelectedToken=r}intercept(t){let r=oe(q(t));return this.lastSelectedTokens[r]||this.storeSelectedToken(r),"continue"}};i();a();i();a();var ia=class{constructor(t){this.lastSelectedTokens=t}intercept(t,r){switch(t.type){case"SPL":{let o=oe(q(t)),n=this.lastSelectedTokens[o];return t.data.ugcTokenMeta?.status==="preDeploy"&&!n?(BS(t),r("ugcWarning"),"interrupt"):"continue"}default:return"continue"}}};function FR({enableUgcSwaps:e,lastSelectedTokens:t,storeSelectedToken:r}){let o=[];return e&&o.push(new sa(t,r),new ia(t)),o}function Bw(e){let{lastSelectedTokens:t,storeSelectedToken:r}=Dw();return(0,Nw.useMemo)(()=>FR({enableUgcSwaps:e,lastSelectedTokens:t,storeSelectedToken:r}),[e,t,r])}var Lw=(0,vr.createContext)(void 0),IR=({children:e})=>{let{data:[t]}=ae(["enable-ugc-swaps"]),r=(0,vr.useMemo)(()=>kw(t),[t]),o=Bw(t);return vr.default.createElement(Lw.Provider,{value:{buyAssetSectionAdapter:r,interceptors:o}},e)},Xn=()=>{let e=(0,vr.useContext)(Lw);if(!e)throw new Error("useSwapAssetSelect must be used within a SwapAssetSelectProvider");return e};var xR=()=>{let{interceptors:e}=Xn();return{executeInterceptors:(0,Uw.useCallback)((r,o)=>{for(let n of e)if(n.intercept(r,o)==="interrupt")return"interrupt";return"continue"},[e])}};i();a();var io=C(R());var _w=3;function Gu(e){return{id:e.id,data:e.data,...e.header?{header:e.header}:{}}}var ER=e=>{let[t,r]=(0,io.useState)({}),o=(0,io.useCallback)((p,c)=>r(u=>({...u,[p]:c})),[]),{buyAssetSectionAdapter:n}=Xn(),s=(0,io.useMemo)(()=>n.sections(e),[n,e]);return(0,io.useMemo)(()=>s.map(p=>{switch(p.behavior){case"static":return Gu(p);case"collapsible":{if(p.data.length<=_w)return Gu(p);let c=t[p.id],u=c?p.data:p.data.slice(0,_w),m=()=>o(p.id,!c),f=Gu(p);return{...f,header:{...f.header,button:{label:c?D.t("commandShowLess"):D.t("commandShowMore"),onClick:m}},data:u}}}}),[s,t,o])};i();a();var vR=()=>{let e=ee(),t=G();return ie({mutationFn:async o=>{await wm(e,o),t.setQueryData(["termsHaveBeenAcknowledged2"],o)}})};i();a();i();a();var Qw=async e=>!!await e.get("termsHaveBeenAcknowledged2");var Vu=()=>{let e=ee(),t=async()=>!!await Qw(e);return B({queryKey:["termsHaveBeenAcknowledged2"],queryFn:t})};i();a();i();a();var kR=C(R());i();a();var Ow=go($,e=>new Ku(e)),Ku=class{constructor(t){this.onOpen=()=>this.#e.capture("connectHardwareOpen");this.onSelectedChains=t=>{let r=new Set(t),o=[];for(let s of F.getAllChainIDs())r.has(s)&&o.push(s);let n={numChains:o.length,selectedChains:o.map(s=>s.charAt(0).toUpperCase()+s.slice(1)).join(", ")};this.#e.capture("connectHardwareSelectedChains",{data:n})};this.onDerivedAccounts=t=>{let r={};for(let[o,n]of Object.entries(t.derivedAddresses)){if(!CR(o))continue;let s={numberOfDerivedAccounts:n.addresses.length};n.duration!==void 0&&(s.derivationDuration=n.duration),r[o]=s}this.#e.capture("connectHardwareAccountsDerived",{data:{...r}})};this.onDiscoveredAccounts=t=>{let{discoveredAccounts:r}=t,o={};for(let n of r){if(n.status==="undiscovered")continue;let s=n.accounts.reduce((p,{derivationPathType:c,hasAccountActivity:u})=>{let m=Pr.getAddressType(c);return p[m]={derivationPathType:c,hasAccountActivity:u},p},{});for(let{hasAccountActivity:p,derivationPathType:c}of Object.values(s)){if(!p)continue;let u=o[c]??0;o[c]=u+1}}this.#e.capture("connectHardwareAccountsDiscovered",{data:{numberOfActiveAccounts:o}})};this.onConnectedAccounts=t=>{let r={};for(let o of t.accounts){let n=[...o.addresses,...o.publicKeys].reduce((s,p)=>{let c=Pr.getAddressType(p.pathType);return s[c]=p.pathType,s},{});for(let s of Object.values(n)){let p=r[s]??0;r[s]=p+1}}this.#e.capture("connectHardwareAccountsConnected",{data:{numberOfAccountsImported:t.accounts.length,numberOfAccountsImportedByDerivationPath:r}})};this.#e=t}#e};function CR(e){return typeof e!="string"?!1:Object.keys(Oa).includes(e)}i();a();i();a();var jse=["firstTimeOnboarding","isResettingApp"];i();a();i();a();var aa="@phantom/onboarding",jt={firstTimeOnboarding:[`${aa}:first-time-onboarding`],isResettingApp:[`${aa}:is-resetting-app`],deriveLedgerAccounts:[`${aa}:derive-ledger-accounts`],discoverLedgerAccounts:[`${aa}:discover-ledger-accounts`]};i();a();i();a();i();a();i();a();var DR="/discover/v1",NR={Accept:"application/json"},BR=e=>{if(!e||!Array.isArray(e.accounts))return!1;for(let t of e.accounts)if(!(typeof t.address=="string"&&typeof t.amount=="string"&&(!t.lastActivityTimestamp||typeof t.lastActivityTimestamp=="number")&&t.chain&&typeof t.chain.id=="string"&&new Set(F.getAllNetworkIDs()).has(t.chain.id)&&typeof t.chain.name=="string"&&typeof t.chain.symbol=="string"&&typeof t.chain.imageUrl=="string"))return!1;return!0},LR=e=>!!e&&typeof e.message=="string",Hu=async e=>{if(!e.accounts.length)throw new Error("Must send at least 1 account for discovery");let t=await U.api().headers(NR).post(DR,e),r=t.data;if(!se(t))throw LR(r)?new Error(r.message):new Error(`Unable to discover accounts with args: ${JSON.stringify(e)}`);if(BR(r))return r;throw new Error(`Invalid discover accounts response: ${JSON.stringify(r)}`)};var UR="fetch-multi-chain-accounts-by-seed",_R=()=>["bitcoinTaproot","bitcoinNativeSegwit"],QR=()=>["bip44Change","bip44","deprecated"],OR=()=>["bip44Ethereum","bip44EthereumSecondary","bip44EthereumTertiary"],MR=(e,t)=>_R().map(r=>{let o={pathType:r,index:e},{publicKey:n}=t.derivePathType(r,e),s=Pr.getAddressType(r),p=us({publicKey:n,addressType:s});return{chainType:s,chainId:W.Bitcoin.Mainnet,address:p,publicKey:Buffer.from(n).toString("hex"),pathParams:o}}),qR=(e,t)=>OR().map(r=>{let o={pathType:r,index:e},{publicKey:n}=t.derivePathType(r,e),s=Pr.getAddressType(r),p=us({publicKey:n,addressType:"eip155"});return{chainType:s,pathType:r,address:p,publicKey:Buffer.from(n).toString("hex"),pathParams:o}}),GR=(e,t)=>QR().map(r=>{let o={pathType:r,index:e},n=t.derivePathType(r,e),s;switch(r){case"bip44Root":case"bip44":case"bip44Change":s=n.publicKey;break;case"deprecated":s=Fl(n.secretKey).publicKey;break}let p=us({publicKey:s,addressType:"solana"});return{chainType:"solana",chainId:W.Solana.Mainnet,address:p,publicKey:Buffer.from(s).toString("hex"),pathParams:o}}),VR=1e4,Mw=async(e,t=!0)=>{let r=Object.assign({},...e);try{let o=Object.values(r).map(({account:g})=>({address:g.address,chainId:g.chainId})),n;t?n=(await fl(async()=>is(Hu({accounts:o}),VR),()=>!0,2)).accounts:n=(await Hu({accounts:o})).accounts;let s={};for(let g of n){let{account:S}=r[`${g.chain.id}-${g.address}`],w=S.publicKey,y=S.chainType,h=ls(S.pathParams),A={...g,publicKey:w,chainType:y,derivationPathType:S.pathParams.pathType,hasAccountActivity:!!g.lastActivityTimestamp||parseFloat(g.amount)>0};s[h]=[...s[h]||[],A]}Object.keys(s).forEach(g=>{s[g]=s[g].sort((S,w)=>F.compareNetworkID(S.chain.id,w.chain.id))});let p=Object.values(s),c=p.filter(g=>g.some(S=>S.hasAccountActivity)),u=!!c.length;return(u?c:p).map(g=>{let S=g[0];return{status:"discovered",seedIndex:r[`${S.chain.id}-${S.address}`].account.pathParams.index??-1,isSelectedByDefault:u,accounts:g}})}catch{let o=Object.values(r).map(({account:c})=>c),n={};for(let c of o){let{account:u}=r[`${c.chainId}-${c.address}`],m=ls(u.pathParams),f={...c,chain:{id:c.chainId,name:F.getNetworkName(c.chainId),symbol:F.getTokenSymbol(c.chainId)},chainType:Pr.getAddressType(u.pathParams.pathType),derivationPathType:u.pathParams.pathType,hasAccountActivity:!1};n[m]=[...n[m]||[],f]}return Object.keys(n).forEach(c=>{n[c]=n[c].sort((u,m)=>F.compareNetworkID(u.chain.id,m.chain.id))}),Object.values(n).map(c=>{let u=c[0];return{status:"undiscovered",seedIndex:r[`${u.chain.id}-${u.address}`].account.pathParams.index??-1,accounts:c,isSelectedByDefault:!1}})}},KR=async({hdWallet:e,startIndex:t,endIndex:r,enabledAddressTypes:o,enabledChains:n})=>{let s=[];for(let p=t;p<=r;++p){let c={};if(o.includes("bip122_p2wpkh"))for(let u of MR(p,e))c[`${W.Bitcoin.Mainnet}-${u.address}`]={account:u};if(o.includes("eip155")){let u=qR(p,e),m=n.filter(f=>F.isEVMNetworkID(F[f].mainnetID));for(let f of m){let g=F[f];for(let{address:S,publicKey:w,chainType:y,pathParams:h}of u)c[`${g.mainnetID}-${S}`]={account:{chainType:y,chainId:g.mainnetID,address:S,publicKey:w,pathParams:h}}}}if(o.includes("solana"))for(let u of GR(p,e))c[`${u.chainId}-${u.address}`]={account:u};s.push(c)}return Mw(s)};function Vie({batchSize:e,hdWallet:t,enabledAddressTypes:r,enabledChains:o}){let n=!!t&&r.length!=0;return er({enabled:n,queryKey:[UR,e],queryFn:async({pageParam:u=0})=>{try{if(e<1)throw new Error(`Need positive batchSize: ${e}`);if(!t)throw new Error("Missing HDWallet");let m=[],f=[],g=u===0,S=u,w=!0;for(;w;){let y=S,h=S+e-1,T=(await KR({hdWallet:t,startIndex:y,endIndex:h,enabledAddressTypes:r,enabledChains:o})).flat(1);S+=e,w=!1;for(let I of T)m.push(I),(I.status==="undiscovered"||I.isSelectedByDefault)&&f.push(I),g&&I.status==="discovered"&&I.isSelectedByDefault&&(w=!0)}if(g&&f.length===0&&m.length>1){let y=m[0],h=y.accounts.map(T=>({...T,hasAccountActivity:!1})),A={status:"undiscovered",seedIndex:y.seedIndex,accounts:h,isSelectedByDefault:!1};f.push(A)}return f}catch(m){throw m instanceof Error&&Q.captureError(m,"account"),m}},getNextPageParam:(u,m)=>{let f=m.length,g=f?m[0]:[],S=g.length?HR(g[g.length-1].seedIndex,e):0;return f===1?S:S+e*(f-1)},gcTime:0,staleTime:0,initialPageParam:0})}var HR=(e,t)=>Math.ceil(e/t)*t+t;i();a();i();a();var $R=()=>{let e=ee(),t=async()=>{let o=await e.get("firstTimeOnboarding");return o===null?!0:o.isFirstTimeOnboarding},{data:r}=B({queryKey:jt.firstTimeOnboarding,queryFn:t});return r},zR=()=>{let e=ee(),t=G();return ie({mutationFn:async o=>{await e.set("firstTimeOnboarding",{isFirstTimeOnboarding:o}),hm(e,o?void 0:Date.now()),t.setQueryData(jt.firstTimeOnboarding,o)}})};i();a();var jR=()=>{let e=ee(),t=async()=>{let o=await e.get("isResettingApp");return o===null?{isResettingApp:!1,isFirstTimeResettingApp:!1}:o},{data:r}=B({queryKey:jt.isResettingApp,queryFn:t});return r},WR=()=>{let e=ee(),t=G();return ie({mutationFn:async o=>{let s=(await e.get("isResettingApp"))?.isFirstTimeResettingApp??!1;await e.set("isResettingApp",{isResettingApp:o,isFirstTimeResettingApp:s}),t.setQueryData(jt.isResettingApp,{isResettingApp:o,isFirstTimeResettingApp:s})}})},YR=()=>{let e=ee(),t=G();return ie({mutationFn:async o=>{let s=(await e.get("isResettingApp"))?.isResettingApp??!1;await e.set("isResettingApp",{isResettingApp:s,isFirstTimeResettingApp:o}),t.setQueryData(jt.isResettingApp,{isResettingApp:s,isFirstTimeResettingApp:o})}})};i();a();i();a();var Cr=C(R());i();a();i();a();i();a();var XR=["SwapperNoQuotes","UnsupportedCountry","UnknownError"],qw=(e,t,r,o)=>{e instanceof Re&&XR.includes(e.code)||(!t||t>=400&&t<500)&&(Q.addBreadcrumb(r,JSON.stringify(o),"info"),Q.captureError(e,"swapper"))};i();a();var Gw=async(e,t,r=void 0)=>{if(se(e))return Promise.resolve();let o;try{o=e.data}catch{throw new Re({key:r})}let n=Ny.safeParse(o);throw n.success?(Q.addBreadcrumb(t,JSON.stringify(n.data),"info"),new Re({key:n.data.name})):new Re({key:r})};i();a();var Vw=(e,t)=>{let r=new Re({key:"SwapperNoQuotes"});if(Yc.safeParse(e?.type).success===!1)throw new Error("Invalid input");let o=Xc.safeParse(e);if(!o.success)throw r;if(o.data.quotes.length===0)throw t?new Re({key:"SwapperRefuelNoQuotes"}):r;return o.data};var Kw="swap-quotes";async function pa({sellTokenCaip19:e,buyTokenCaip19:t,taker:r,takerDestination:o,exactOut:n,sellAmount:s,slippageTolerance:p,refuelEnabled:c,signal:u,enableAutoSlippage:m}){let f;try{kr(r)&&(r.address=r.address.toLowerCase()),o&&kr(o)&&(o.address=o.address.toLowerCase()),kr(t)&&(t.address=t.address.toLowerCase()),kr(e)&&(e.address=e.address.toLowerCase());let g="/swap/v2/quotes",S={sellToken:e,buyToken:t,taker:r,takerDestination:o,exactOut:n,sellAmount:s,slippageTolerance:p,autoSlippage:m,...c?{refuel:1,ignoreRefuelFailures:!0}:{}},w=await U.api().post(g,S,{signal:u});return f=w.status,await Gw(w,Kw,"SwapperNoQuotes"),Vw(w.data,c)}catch(g){let S=g instanceof Re?g:new Re;throw qw(S,f,Kw,{sellToken:e,buyToken:t,sellAmount:s,slippageTolerance:p}),S}}var ca=class{getQuotes(t){return pa({sellTokenCaip19:t.sell,buyTokenCaip19:t.buy,taker:t.taker,takerDestination:t.takerDestination,sellAmount:t.amount,slippageTolerance:t.slippageTolerance,refuelEnabled:t.refuelEnabled,signal:t.signal,exactOut:t.exactOutEnabled,enableAutoSlippage:t.autoSlippageEnabled})}};i();a();i();a();i();a();var zw=C(R());i();a();var Hw=C(R());function ua(e){return(0,Hw.useMemo)(()=>Ue(e),[e])}function jw({sender:e}){let{t}=rr(),{currentQuote:r}=V(),o=b(g=>g.buyFungible),n=b(g=>g.sellFungible),s=ua(Xe(r)),{data:p}=br({callerAddress:e,quote:r,sellFungible:n}),c=p?.transactionPairs?.flatMap(g=>[...Br(g.sellToken)??[],...Br(g.buyToken)??[]])??[],u=[...new Set(c)],{fungibles:m}=Ct({keys:u});return{pendingActivityRows:(0,zw.useMemo)(()=>$u({swapTransactions:p,sellFungible:n,buyFungible:o,fungibles:m,t,currentQuote:r,provider:s}),[p,n,o,m,t,r,s])}}function $u({swapTransactions:e,sellFungible:t,buyFungible:r,fungibles:o,currentQuote:n,provider:s,t:p}){let c={pendingSwapActivityRows:[],pendingApprovalActivityRows:[]};return e?e.type==="evm"?e.transactionTypes.reduce((u,m,f)=>{let g=t,S=r,w,y=e.transactionPairs;if(y&&y[f]){let h=y[f];g=la(h.sellToken,o)??t,S=la(h.buyToken,o)??r,w=h.approvalMetadata}if(m.includes("approval")){let h=g?.data.symbol??"";u.pendingApprovalActivityRows.push({topLeft:w?{text:`${p("transactionsPendingApproving")} ${w.symbol}`}:{text:`${p("transactionsPendingApproving")} ${h}`}})}else g&&S&&u.pendingSwapActivityRows.push($w({sellFungible:g,buyFungible:S,sellAmount:n?.sellAmount,buyAmount:n?.buyAmount,provider:s,isBridge:m.includes("bridge"),t:p}));return u},c):e.transactionTypes.reduce((u,m,f)=>{let g=t,S=r,w=e.transactionPairs;if(w&&w[f]){let y=w[f];g=la(y.sellToken,o)??t,S=la(y.buyToken,o)??r}return g&&S&&u.pendingSwapActivityRows.push($w({sellFungible:g,buyFungible:S,sellAmount:n?.sellAmount,buyAmount:n?.buyAmount,provider:s,isBridge:m.includes("bridge"),t:p})),u},c):c}var $w=({sellFungible:e,buyFungible:t,sellAmount:r,buyAmount:o,provider:n,isBridge:s,t:p})=>{let c=e.data.symbol,u=t.data.symbol,m=Z(r??0,e.data.decimals??0),f=Z(o??0,t.data.decimals??0);return{topLeft:{text:`${p(s?"transactionsPendingBridging":"transactionsPendingSwapping")} on ${n}`},topRight:{text:`${f} ${u}`,color:"accentSuccess"},bottomLeft:{text:`${c} \u2192 ${u}`},bottomRight:{text:`${m} ${c}`}}},la=(e,t)=>t.find(r=>(Br(e)??[]).includes(r.data.key));var ma=class{constructor(t,r,o,n,s,p){this.executingQuote=t;this.takerAddress=r;this.networkId=o;this.quote=n;this.executionId=s;this.connection=p}async createTransaction(){return Zc({quote:this.quote,connection:this.connection})}async simulate(t,r,o){return await Sn(r,o,{type:"transaction",userAccount:this.takerAddress,params:{transactions:t.transactions,method:"signAndSendTransaction"},url:"https://phantom.app",networkID:this.networkId})}async swap(t,r,o,n){let s=this.parsePendingActivity(this.executingQuote,r),{signature:p,error:c}=await Wi({callerAddress:this.takerAddress,connection:this.connection,networkID:this.networkId,transactions:r.transactions,pendingSwapActivityRows:s.pendingSwapActivityRows,accountIdentifier:t,accountSigner:o,storage:n});if(c)throw c;return{networkID:this.networkId,id:p}}async waitForConfirmation(t){let r=zl(t.networkID);await Ss({connection:r,signature:t.id})}parsePendingActivity(t,r){let o=Ue(Xe(t.quote));return $u({swapTransactions:r,sellFungible:t.sell,buyFungible:t.buy,fungibles:[],currentQuote:t.quote,provider:o,t:D.t})}};var da=class{create(t,r,o,n){if(Wr(t.quote))return new ma(t,r,o,t.quote,ns(),n);throw new Error("Unsupported SwapQuote}")}};i();a();i();a();i();a();i();a();i();a();var Ww=(f=>(f.SlippageExceeded="SLIPPAGE_EXCEEDED",f.InsufficientFunds="INSUFFICIENT_FUNDS",f.InsufficientGas="INSUFFICIENT_GAS",f.UnknownError="UNKNOWN_ERROR",f.SimulationFailed="SIMULATION_FAILED",f.TransactionExpired="TRANSACTION_EXPIRED",f.AccountFrozen="ACCOUNT_FROZEN",f.SimulationTimeout="SIMULATION_TIMEOUT",f.StaleQuota="STALE_QUOTA",f.StaleQuote="STALE_QUOTE",f))(Ww||{}),Jn=class e extends Error{constructor(r,o,n){super(r);this.scannedTransactionResult=o;this.type=n;o&&(this.detailMessage=o.simulationError?.humanReadableError??(o.warnings.length>0?o.warnings[0].message:void 0)),this.type=n}static fromError(r){return r instanceof e?r:new e(r.message)}static fromScannedTransactionResult(r,o){return(s=>Object.values(Ww).includes(s))(r)?new e(r,o,r):new e(r,o)}};i();a();var en=class e extends Error{constructor(r,o){super(r);this.type=o}static fromError(r){return new e(r.message)}static fromType(r){return new e(r,r)}};var fa=class{constructor(t,r,o){this.swapCommand=t;this.send=r;this.executingQuote=o;this.takerAddress=t.takerAddress,this.networkId=t.networkId,this.quote=t.quote,this.executionId=t.executionId}createTransaction(){return this.swapCommand.createTransaction()}async simulate(t,r,o){try{this.send({type:"simulationRequestStarted",executionId:this.executionId,executingQuote:this.executingQuote});let n=await this.swapCommand.simulate(t,r,o);if(n.error){let s=Jn.fromScannedTransactionResult(n.error,n);throw this.send({type:"simulationFailed",error:s,executionId:this.executionId}),s}return this.send({type:"simulationPassed",scannedTransactionResult:n,executionId:this.executionId}),n}catch(n){throw this.send({type:"simulationFailed",error:Jn.fromError(n),executionId:this.executionId}),n}}async swap(t,r,o,n){this.send({type:"transactionSubmissionStarted",executionId:this.executionId});try{let s=await this.swapCommand.swap(t,r,o,n);if(!s.id)throw en.fromType("noSignature");return this.send({type:"transactionSubmissionSuccess",txReceipt:s,executionId:this.executionId}),s}catch(s){let p=s instanceof en?s:en.fromError(s);throw this.send({type:"transactionSubmissionFailed",error:p,executionId:this.executionId}),p}}async waitForConfirmation(t){this.send({type:"transactionConfirmationStarted",executionId:this.executionId,txReceipt:t});try{let r=await this.swapCommand.waitForConfirmation(t);return this.send({type:"transactionConfirmed",executionId:this.executionId,txReceipt:t}),r}catch(r){throw this.send({type:"transactionConfirmationFailed",error:r,txReceipt:t,executionId:this.executionId}),r}}};i();a();i();a();var ju=class extends Error{constructor(t,r){super(`Can't transition to ${r} from ${t.type}`)}},ga=class{constructor(){this.state=new Map;this.listeners=new Set}send(t){t.type==="simulationRequestStarted"&&this.state.set(t.executionId,{type:"idle",executingQuote:t.executingQuote});let r=this.state.get(t.executionId);if(!r)throw new Error(`No previous state found for execution id: ${t.executionId}`);let o=r;try{return o=this.detectNextState(r,t),o.type==="transactionConfirmed"?this.state.delete(t.executionId):this.state.set(t.executionId,o),o}catch{return r}finally{this.notifyListeners(t,r,o,this.state)}}notifyListeners(t,r,o,n){try{this.listeners.forEach(s=>s.onStateChange(t,r,o,n))}catch{}}getState(){return this.state}addListener(t){this.listeners.add(t)}removeListener(t){this.listeners.delete(t)}detectNextState(t,r){switch(r.type){case"simulationRequestStarted":return Wt(t,"idle"),{type:"simulating",executingQuote:t.executingQuote};case"simulationPassed":return Wt(t,"simulating"),{type:"simulated",scannedTransactionResult:r.scannedTransactionResult,executingQuote:t.executingQuote};case"simulationFailed":return Wt(t,"simulating"),{type:"simulationFailed",error:r.error,executingQuote:t.executingQuote};case"transactionSubmissionStarted":return Wt(t,"simulated"),{type:"submittingTransaction",executingQuote:t.executingQuote,scannedTransactionResult:t.scannedTransactionResult};case"transactionSubmissionFailed":return Wt(t,"submittingTransaction"),{type:"transactionSubmissionFailed",error:r.error,executingQuote:t.executingQuote,scannedTransactionResult:t.scannedTransactionResult};case"transactionSubmissionSuccess":return Wt(t,"submittingTransaction"),{type:"transactionSubmitted",txReceipt:r.txReceipt,executingQuote:t.executingQuote,scannedTransactionResult:t.scannedTransactionResult};case"transactionConfirmationStarted":return Wt(t,"transactionSubmitted"),{type:"confirmingTransaction",txReceipt:t.txReceipt,scannedTransactionResult:t.scannedTransactionResult,executingQuote:t.executingQuote};case"transactionConfirmationFailed":return Wt(t,"transactionSubmitted"),{type:"transactionNotConfirmed",txReceipt:t.txReceipt,scannedTransactionResult:t.scannedTransactionResult,error:r.error,executingQuote:t.executingQuote};case"transactionConfirmed":return Wt(t,"confirmingTransaction"),{type:"transactionConfirmed",txReceipt:t.txReceipt,scannedTransactionResult:t.scannedTransactionResult,executingQuote:t.executingQuote}}}};function Wt(e,t){if(e.type!==t)throw new ju(e,t)}i();a();function Yw(e){return e.type==="quote"}i();a();i();a();var JR={"Could not find any routes":"noRoute"},ya=class e extends Error{constructor(t,r){super(r??t),this.type=t}static fromMessage(t){let r=t?JR[t]??"undefinedError":"undefinedError";return new e(r,t)}};var tn=class extends Error{constructor(t,r){super(`Can't transition to ${r} from ${t.type}`)}},Sa=class{constructor(t){this.listeners=new Set;this.state=t??{type:"idle"}}send(t){let r=this.state;try{return r=this.detectNextState(this.state,t),this.state=r,r}catch{return this.state}finally{this.notifyListeners(t,this.state,r)}}getState(){return this.state}addListener(t){this.listeners.add(t)}removeListener(t){this.listeners.delete(t)}notifyListeners(t,r,o){try{this.listeners.forEach(n=>n.onStateChange(t,r,o))}catch{}}detectNextState(t,r){switch(r.type){case"setPair":return{type:"pair",buy:r.buy,sell:r.sell,amount:r.amount};case"quoteRequestStarted":switch(t.type){case"pair":case"quote":case"quoteError":return{type:"fetchingQuote",buy:t.buy,sell:t.sell,amount:r.amount,slippageType:r.slippageType};default:throw new tn(t,r.type)}case"quoteRequestError":if(t.type!=="fetchingQuote")throw new tn(t,r.type);return{type:"quoteError",buy:t.buy,sell:t.sell,amount:t.amount,error:r.error,slippageType:t.slippageType};case"quoteResponseReceived":if(t.type!=="fetchingQuote")throw new tn(t,r.type);let o=r.response.quotes[0];return o?{type:"quote",response:r.response,buy:t.buy,sell:t.sell,quote:o,amount:t.amount,slippageType:t.slippageType}:{type:"quoteError",buy:t.buy,sell:t.sell,amount:t.amount,error:new ya("noRoute"),slippageType:t.slippageType};case"userSelectedQuote":if(t.type!=="quote")throw new tn(t,r.type);return{type:"quote",quote:r.quote,buy:t.buy,sell:t.sell,response:t.response,amount:t.amount,slippageType:t.slippageType}}}};var wa=class{constructor(t,r,o){this.apiClient=t;this.swapCommandFactory=r;this.stateMachine=new Sa;this.executeSwapStateMachine=new ga;this.account=o}setPair(t,r,o){this.send({type:"setPair",buy:t,sell:r,amount:o})}setAccount(t){this.account=t}async fetchQuotes({amount:t,buy:r,sell:o,slippageTolerance:n,autoSlippageEnabled:s,refuelEnabled:p,signal:c}){let u=q(o),m=q(r),f=t.perspective==="buy",g=new K(t.amount).shiftedBy(t.perspective==="buy"?r.data.decimals:o.data.decimals).toString();this.send({type:"quoteRequestStarted",amount:t,slippageType:s?"auto":"fixed"});let S=this.checkChainAddress(o.data.chain.id),w={chainId:S.networkID,resourceType:"address",address:S.address},y;if(he(o.data.chain.id,r.data.chain.id)){let h=this.checkChainAddress(r.data.chain.id);y={chainId:h.networkID,resourceType:"address",address:h.address}}try{let h=await this.apiClient.getQuotes({sell:u,buy:m,amount:g,taker:w,takerDestination:y,slippageTolerance:n??0,autoSlippageEnabled:s,exactOutEnabled:f,refuelEnabled:p??!1,signal:c});return this.send({type:"quoteResponseReceived",response:h}),h}catch(h){throw this.send({type:"quoteRequestError",error:h}),h}}async swap(t,r,o,n,s){let p=this.getState();if(!Yw(p))throw new Error(`Can't swap from ${p.type}`);let c=this.checkAccount(),u=this.checkChainAddress(p.sell.data.chain.id),m={account:c,buy:p.buy,sell:p.sell,amount:p.amount,quote:p.quote,response:p.response,slippageType:p.slippageType},f=this.swapCommandFactory.create(m,u.address,u.networkID,s),g=new fa(f,y=>this.executeSwapStateMachine.send(y),m),S=await g.createTransaction();await g.simulate(S,o,n);let w=await g.swap(c.identifier,S,t,r);await g.waitForConfirmation(w)}selectQuote(t){this.send({type:"userSelectedQuote",quote:t})}getState(){return this.stateMachine.getState()}subscribe(t){return this.stateMachine.addListener(t),()=>this.unsubscribe(t)}unsubscribe(t){this.stateMachine.removeListener(t)}getExecuteState(){return this.executeSwapStateMachine.getState()}subscribeExecution(t){return this.executeSwapStateMachine.addListener(t),()=>this.unsubscribeExecution(t)}unsubscribeExecution(t){this.executeSwapStateMachine.removeListener(t)}send(t){return this.stateMachine.send(t)}checkAccount(){if(!this.account)throw new Error("Must set a MultiChainAccount");return this.account}checkChainAddress(t){let o=this.checkAccount().addresses.find(n=>n.networkID===t);if(!o)throw new Error(`No address found for chain ${t}`);return xl(o)&&(o.address=o.address.toLowerCase()),o}};var Jw=(0,Cr.createContext)(null);function sue({children:e}){let{data:t}=te();return(0,Cr.useEffect)(()=>{t&&Xw.setAccount(t)},[t]),Cr.default.createElement(Jw.Provider,{value:Xw},e)}function ha(){let e=(0,Cr.useContext)(Jw);if(!e)throw new Error("useSwapper must be used within a SwapperProvider");return e}var Xw=tP();function tP(){return new wa(new ca,new da)}i();a();var Ft=C(R());i();a();i();a();i();a();i();a();var oP=3e3,Zn=Be(e=>({toast:void 0,visible:!1,currentTimer:null,hideToast(){e(t=>(t.currentTimer&&clearTimeout(t.currentTimer),{visible:!1,currentTimer:null}))},removeToast(){e(t=>(t.currentTimer&&clearTimeout(t.currentTimer),{toast:void 0,visible:!1,currentTimer:null}))},success(t){e(r=>{r.currentTimer&&clearTimeout(r.currentTimer);let o=setTimeout(()=>{e({visible:!1,currentTimer:null})},oP);return{toast:{type:"Success",...t},visible:!0,currentTimer:o}})},failed(t){e(r=>(r.currentTimer&&clearTimeout(r.currentTimer),{toast:{type:"Failed",...t},visible:!0,currentTimer:null}))},progress(t){e(r=>(r.currentTimer&&clearTimeout(r.currentTimer),{toast:{type:"Progress",...t},visible:!0,currentTimer:null}))}}));var Aa=class{onStateChange(t,r,o,n){let{sell:s,buy:p}=o.executingQuote;switch(t.type){case"simulationRequestStarted":nP(`Swapping ${s.data.symbol} -> ${p.data.symbol}`);break;case"transactionConfirmed":sP(`Swapped ${s.data.symbol} -> ${p.data.symbol}`);break;case"simulationFailed":Wu(`Swap failed ${s.data.symbol} -> ${p.data.symbol}`,t.error.detailMessage??"");break;case"transactionSubmissionFailed":Wu(`Swap failed ${s.data.symbol} -> ${p.data.symbol}`,t.error.message??"");break;case"transactionConfirmationFailed":Wu(`Swap failed ${s.data.symbol} -> ${p.data.symbol}`,t.error.message??"");break}}};function nP(e){Zn.getState().progress({message:e,onPress:()=>{}})}function sP(e){Zn.getState().success({message:e,onPress:()=>{}})}function Wu(e,t){Zn.getState().failed({title:e,message:t,onPress:()=>{},buttonText:"Retry"})}i();a();i();a();var ba=class{constructor(t){this.queryClient=t}onStateChange(t,r,o,n){let{sell:s,quote:p,account:c}=o.executingQuote;if(t.type==="transactionConfirmed"){let u=new N(s.data.amount),m=new N(p.sellAmount),f=u.minus(m).toString();Ji(this.queryClient,{id:Xo(s),amount:f}),this.queryClient.invalidateQueries({queryKey:im.tokens(c.addresses)})}}};var Ta=class{constructor(t){this.toastListener=t}intercept(t){let{swapper:r,connection:o,storage:n,vaultProxy:s,locale:p,analytics:c,resetSwapper:u,queryClient:m}=t,f=r.getState();if(f.type!=="quote")return"continue";let{quote:g}=f;return Wr(g)?((async()=>{let S=await c.getDeviceId(),w=new ba(m);try{u(),r.subscribeExecution(w),r.subscribeExecution(this.toastListener),await r.swap(s,n,p,S,o)}catch{}finally{r.unsubscribeExecution(w),r.unsubscribeExecution(this.toastListener)}})(),"interrupt"):"continue"}};i();a();i();a();var Fa=class{intercept({hasViewedAutoSlippageOptIn:t,selectedSetting:r,setHasViewedAutoSlippageOptIn:o,showOverlay:n}){return!t&&r!=="auto"?(n("autoSlippageOptIn"),o(!0),"interrupt"):"continue"}};i();a();var Ia=class{intercept(){let{review:t}=Sr();return t?jr(t):On(),"continue"}};function Zw(e,t,r){let o=[];return e&&o.push(new Fa),r&&o.push(new Ta(new Aa)),t||o.push(new Ia),o}i();a();var xa=class{async fetch(t){return await pa(t)}};i();a();var Ea=class{constructor(t){this.swapper=t}async fetch(t){let r=aP(t.amount,t.sell,t.buy);return this.swapper.setPair(t.buy,t.sell,r),await this.swapper.fetchQuotes({buy:t.buy,sell:t.sell,amount:r,slippageTolerance:t.slippageTolerance,autoSlippageEnabled:t.enableAutoSlippage,refuelEnabled:t.refuelEnabled})}};function aP(e,t,r){let o=e.type==="sell"?t:r;return{amount:new K(e.value).shiftedBy(-o.data.decimals).toString(),perspective:e.type}}var eh=(0,Ft.createContext)(void 0),pP=({children:e})=>{let{data:[t,r,o]}=ae(["enable-auto-slippage","enable-swapper-skip-review","enable-swap-toasts"]),n=(0,Ft.useMemo)(()=>t?["auto","fixed"]:["fixed"],[t]),s=(0,Ft.useMemo)(()=>Zw(t,r,o),[t,r,o]),p=nl,c=r,u=ha(),m=(0,Ft.useMemo)(()=>o&&lt?new Ea(u):new xa,[u,o]);return Ft.default.createElement(eh.Provider,{value:{slippageSettingTypes:n,reviewInterceptors:s,inspectorEnabled:p,showStickyCTAButton:c,getSwapQuotesStrategy:m}},e)},Yu=()=>{let e=(0,Ft.useContext)(eh);if(!e)throw new Error("useSwapTab must be used within a SwapTabProvider");return e};i();a();var es=()=>{let{selectedSetting:e}=De(),t=b(s=>s.selectedProviderIndex),r=b(s=>s.quoteResponse),o=Ir(t,e.type,r),{data:n}=ro();return o??n??void 0};i();a();var cP="https://help.phantom.app/hc/en-us/articles/33745013398675";i();a();var uP=e=>xi.safeParse(e).success;i();a();var Rr=C(R());i();a();i();a();function th(){return Sr().review!==null?1:0}var rh=(0,Rr.createContext)(void 0),mP=({children:e})=>{let t=(0,Rr.useMemo)(()=>th(),[]);return Rr.default.createElement(rh.Provider,{value:{infoRowDisplayStrategy:t}},e)},dP=()=>{let e=(0,Rr.useContext)(rh);if(!e)throw new Error("useSwapReview must be used within a SwapReviewProvider");return e};i();a();var Ke=C(R());i();a();i();a();var ao=C(R());i();a();i();a();var va=e=>e&&pe(e)?e.executionDuration:0;var oh=e=>{let t=va(e);return oo(t)};i();a();var nh=({networkID:e,txReceipt:t,preferredExplorers:r})=>{let o=t?.id??"";if(!o)return;let n=r?.explorers[e];return Dl({networkID:e,endpoint:"transaction",explorerType:n,param:o})};i();a();i();a();var ne=class extends Error{constructor(t,r){super(t),this.code=t,this.detailMessage=r}};function sh(e,t){if(t){let r=fP(e),o=yP(e,t),n=SP(e,t);return{notEnoughSol:t.message==="NotEnoughSol",txErrorTitle:r,txErrorMessage:o,txErrorHelpButtonLink:n}}return{notEnoughSol:!1,txErrorTitle:"",txErrorMessage:"",txErrorHelpButtonLink:""}}var fP=e=>e?D.t("swapTxBridgeFailed"):D.t("swapTxConfirmationSwapFailed"),gP=e=>{switch(e.message){case"LedgerConnectionError":return D.t("swapTxConfirmationSwapFailedLedgerConnectionError");case"LedgerRejectAction":return D.t("swapTxConfirmationSwapFailedLedgerReject");case"LedgerUnknownSignError":return D.t("swapTxConfirmationSwapFailedLedgerSignError");case"LedgerUnknownError":return D.t("swapTxConfirmationSwapFailedLedgerError");default:return null}},yP=(e,t)=>{let r=gP(t);if(r!==null)return r;if(e)switch(t.message){case"slippageToleranceExceeded":return D.t("swapTxConfirmationSwapFailedSlippageLimit");default:return D.t("swapTxBridgeFailedDescription")}else switch(t.message){case"INSUFFICIENT_FUNDS":return D.t("swapTxConfirmationSwapFailedInsufficientBalance");case"slippageToleranceExceeded":return D.t("swapTxConfirmationSwapFailedSlippageLimit");case"EmptyRoute":return D.t("swapTxConfirmationSwapFailedEmptyRoute");case"AccountFrozen":return D.t("swapTxConfirmationSwapFailedAcountFrozen");case"SIMULATION_TIMEOUT":return D.t("swapTxConfirmationSwapFailedSimulationTimeout");case"INSUFFICIENT_GAS":return D.t("swapTxConfirmationSwapFailedInsufficientGas");case"Scan results are not valid":return D.t("swapTxConfirmationSwapFailedUnknownError");case"STALE_QUOTA":return D.t("swapTxConfirmationSwapFailedStaleQuota");case"SimulationUnknownError":return D.t("swapTxConfirmationSwapFailedSimulationUnknownError");default:return D.t("swapTxConfirmationSwapFailedTryAgain")}},SP=(e,t)=>e?Di:t.message==="AccountFrozen"?oS:Di;i();a();var ih=({sellAmount:e,sellFungible:t,buyAmount:r,buyFungible:o,txStatus:n,txReceipt:s,swapTransactions:p,isReadyToExecute:c,uiEstimatedTime:u,isBridge:m,isLedger:f,notEnoughSol:g,txError:S,txErrorTitle:w,txErrorMessage:y,explorerUrl:h,txErrorHelpButtonLink:A,addressType:T})=>{let I=Z(e,t?.data.decimals??0).toNumber(),v=Z(r,o?.data.decimals??0).toNumber(),x=n==="success",E=n==="error",P=!!s,k=p?.transactions.length||0;return{sellAsset:{amount:I,symbol:t?.data.symbol??"",networkID:t?.data.chain.id??W.Solana.Mainnet},buyAsset:{amount:v,symbol:o?.data.symbol??"",networkID:o?.data.chain.id??W.Solana.Mainnet},estimatedTime:u,isBridge:m,isSuccess:x,isFailure:E,isLedger:f,isClosable:P,notEnoughSol:g,txError:S,txErrorTitle:w,txErrorMessage:S?.detailMessage?`${y}

${S?.detailMessage}`:y,txLink:h??"",txErrorHelpButtonLink:A,numberOfTransactions:k,isReadyToExecute:c,addressType:T}};var Xu=({sellFungible:e,txReceipt:t,swapTransactions:r,isBridge:o,txError:n,txStatus:s,isReadyToExecute:p,isLedger:c})=>{let u=e?.data.chain.id??W.Solana.Mainnet,{notEnoughSol:m,txErrorTitle:f,txErrorMessage:g,txErrorHelpButtonLink:S}=(0,ao.useMemo)(()=>sh(o,n),[o,n]),w=(0,ao.useMemo)(()=>F.getAddressType(u),[u]),{currentQuote:y}=V(),{buyAmount:h,sellAmount:A}=y??{buyAmount:"0",sellAmount:"0"},T=b(P=>P.buyFungible),{data:I}=Bl(),v=(0,ao.useMemo)(()=>nh({txReceipt:t,networkID:u,preferredExplorers:I}),[t,u,I]),x=(0,ao.useMemo)(()=>oh(y),[y]);return(0,ao.useCallback)(()=>ih({sellFungible:e,txReceipt:t,addressType:w,swapTransactions:r,isBridge:o,txError:n,sellAmount:A,buyAmount:h,buyFungible:T,txStatus:s,isReadyToExecute:p,uiEstimatedTime:x,isLedger:c,notEnoughSol:m,txErrorTitle:f,txErrorMessage:g,explorerUrl:v,txErrorHelpButtonLink:S}),[e,t,w,r,o,n,A,h,T,s,p,x,c,m,f,g,v,S])()};i();a();var wP="slippageToleranceExceeded",hP="INSUFFICIENT_FUNDS",AP=Vl,bP="insufficient lamports",TP="lower balance than rent-exempt minimum",FP=Gl,IP="Transaction confirmation failed",xP=["Ledger App not open","Ledger not connected","Unable to connect to Ledger","Unable to connect to Ledger device","Need permission to connect to Ledger"],EP="Ledger user rejected action",vP="Ledger Sign Error";function CP(e){try{return Number(e.split('"Custom":')[1].split("}")[0])}catch{return null}}function RP(e){switch(CP(e.message)){case 301:return"slippageToleranceExceeded";case 6e3:return"EmptyRoute";case 6001:return"slippageToleranceExceeded";case 6002:return"InvalidCalculation";case 6003:return"MissingPlatformFeeAccount";case 6004:return"InvalidSlippage";case 6005:return"NotEnoughPercent";case 6006:return"InvalidInputIndex";case 6007:return"InvalidOutputIndex";case 6008:return"NotEnoughAccountKeys";case 6009:return"NonZeroMinimumOutAmountNotSupported";case 6010:return"InvalidRoutePlan";case 6011:return"InvalidReferralAuthority";case 6012:return"LedgerTokenAccountDoesNotMatch";case 6013:return"InvalidTokenLedger";case 6014:return"IncorrectTokenProgramID";case 6015:return"TokenProgramNotProvided";case 6016:return"SwapNotSupported";case 6017:return"ExactOutAmountNotMatched";case 6018:return"SourceAndDstinationMintCannotBeTheSame";default:return null}}function PP(e){return xP.some(r=>e.message.includes(r))?"LedgerConnectionError":e.message.includes(EP)?"LedgerRejectAction":e.message.includes(vP)?"LedgerUnknownSignError":e.message.toLowerCase().includes("ledger")?"LedgerUnknownError":null}function rn(e){if(e===void 0)return new ne("UnknownError");if(e instanceof ne)return e;let t=kP(e);if(t.message.includes(hP))return new ne("INSUFFICIENT_FUNDS");if(DP(t.message))return new ne("slippageToleranceExceeded");if(t.message==="Scan results are not valid")return new ne("Scan results are not valid");if([AP,bP,FP,TP].some(u=>t.message.includes(u)))return new ne("NotEnoughSol");let s=RP(t);if(s!==null)return new ne(s);if(t.message.includes(IP))return new ne("Transaction confirmation failed");let c=PP(t);return c!==null?new ne(c):new ne("UnknownError")}function kP(e){return typeof e=="string"?new Error(e):typeof e=="object"&&"logs"in e&&e.logs?.length?new Error(e.logs.join(`
`)):e}function DP(e){return e.includes(wP)}i();a();var nn=C(R());i();a();var on=e=>e?pe(e)?void 0:e.priceImpact:0;i();a();function ah({buyAssetUsdValue:e,buyToken:t,sellAssetUsdValue:r,sellToken:o,buyAmount:n,sellAmount:s,phantomFeeUiAmount:p,priceImpact:c,provider:u,slippageTolerance:m,transactionId:f,slippageType:g,sellSwapFilter:S}){let w=fS({buyAssetUsdValue:e,buyToken:t,sellAssetUsdValue:r,sellToken:o,buyAmount:n,sellAmount:s,phantomFeeUiAmount:p,priceImpact:c,provider:u,slippageTolerance:m,slippageType:g,sellSwapFilter:S});return w?{data:{...w,id:f}}:{}}i();a();var ph=({txReceipt:e,networkID:t,amountType:r,swapAnalytics:o})=>{let n=dn(t);Q.addBreadcrumb("swapper","submittedTransaction","info",{...n,txReceipt:e.id,method:n.chainType==="solana"?"signAndSendTransaction":n.chainType==="eip155"?"eth_sendRawTransaction":"UNKNOWN"}),n.chainType==="solana"?o.submittedTransaction(e.id,{...n,method:"signAndSendTransaction",swapType:r==="sell"?"exactIn":"exactOut"}):n.chainType==="eip155"&&o.submittedTransaction(e.id,{...n,method:"eth_sendRawTransaction",swapType:r==="sell"?"exactIn":"exactOut"})};var po=()=>{let e=$(),t=Li(),{currentQuote:r,quoteResponse:o}=V(),n=b(E=>E.buyFungible),s=b(E=>E.sellFungible),p=b(E=>E.buyUsdValue),c=b(E=>E.sellUsdValue),u=b(E=>E.selectedProviderIndex),m=b(E=>E.swapFilter),{selectedSetting:f}=De(),g=b.getState().amountType,S=s?.data.chain.id??W.Solana.Mainnet,w=at(),{transition:y}=Vt(),h=(0,nn.useCallback)(E=>{let P=Ir(u,f.type,o);return ah({buyToken:n,sellToken:s,buyAmount:r?.buyAmount.toString()??"",sellAmount:r?.sellAmount.toString()??"",phantomFeeUiAmount:w.phantomFee.uiAmount,priceImpact:on(r),provider:Ue(Xe(r)),buyAssetUsdValue:p,sellAssetUsdValue:c,slippageTolerance:P,transactionId:E??"",slippageType:f.type,sellSwapFilter:m})},[n,s,r,w.phantomFee.uiAmount,p,c,o,u,f.type,m]),A=(0,nn.useCallback)(E=>{ph({txReceipt:E,networkID:S,amountType:g,swapAnalytics:t})},[S,g,t]),T=(0,nn.useCallback)((E,P)=>{y({type:"transactionSubmissionFailed",error:E}),E.message!=="Scan results are not valid"&&E.message!=="slippageToleranceExceeded"&&Q.captureError(E,"swapper");let k=h(P);e.capture("swapperSwapFailure",{data:{...k.data,error:E.message}})},[e,h,y]),I=()=>{e.capture("swapperSwapFailureNoSignature")},v=(0,nn.useCallback)(E=>{let P=h(E.id);e.capture("swapperSwapSuccess",P);let k=dn(E.networkID);Q.addBreadcrumb("swapper","transactionStatus","info",{...k,txReceipt:E.id}),(k.chainType==="eip155"||k.chainType==="solana")&&t.transactionStatus(E.id,{...k,status:{type:"confirmed"}})},[e,h,t]),x=(0,nn.useCallback)((E,P)=>{let k=E?.networkID?dn(E.networkID):null;(k?.chainType==="eip155"||k?.chainType==="solana")&&E?.id&&(Q.addBreadcrumb("swapper","transactionStatus","error",{...k,txReceipt:E.id}),t.transactionStatus(E.id,{...k,status:{type:"error"}}),y({type:"transactionConfirmationFailed",error:P,txReceipt:E}))},[t,y]);return{logTransactionSubmitted:A,logTransactionSubmissionError:T,logSwapFailureNoSignature:I,logTransactionConfirmed:v,logTransactionConfirmationFailed:x}};i();a();var Sh=C(R());i();a();i();a();i();a();i();a();var ch=3;function uh(){try{let e=Qe.getMultivariateAssignment("swapper-high-slippage-threshold"),t=Number(e);return!e||isNaN(t)?ch:t}catch{return ch}}var lh=({currentQuote:e,quoteResponse:t,gasEstimation:r,swapTransactions:o,scanResults:n,isSwapSimulationDisabled:s,slippageTolerance:p,enableGasEstimationOptimization:c})=>{if(!e)throw new ne("Trying to execute swap with no current quote");if(!t)throw new ne("Trying to execute swap with no quote response");if((c?F.isEVMNetworkID(t.sellToken.chainId):!0)&&(!r||r?.length===0))throw new ne("No gas estimation available");if(!o)throw new ne("No swap transactions available");if(n.state!=="valid"&&(Q.addBreadcrumb("swapper","swapSimulationNotValid","info",mo({scanResults:n.state})),!s)){let m=["Scan results are not valid","AccountFrozen","INSUFFICIENT_FUNDS","INSUFFICIENT_GAS","slippageToleranceExceeded","SIMULATION_TIMEOUT","MissingArgument","exceedsSimulationTolerance","STALE_QUOTA","SimulationUnknownError"];if(BP(n.state)&&m.includes(n.state.code)){if(NP(n.state,p))return;throw n.state}else throw new ne("Scan results are not valid")}};function NP(e,t){return e.code==="slippageToleranceExceeded"&&t&&t>=uh()}function BP(e){return e instanceof ne}var LP={swap:{solana:"executeSolanaSwap",evm:"executeEvmSwap"},bridge:{solana:"executeSolanaBridge",evm:"executeEvmBridge"}},UP=(e,t)=>LP[e?"bridge":"swap"][t??"solana"],mh=async({isBridge:e,addressType:t,isSwapSimulationDisabled:r,sender:o,currentQuote:n,quoteResponse:s,gasEstimation:p,swapTransactions:c,scanResults:u,pendingActivityRows:m,connection:f,enableGasEstimationOptimization:g,logSwapFailureNoSignature:S,logTransactionSubmitted:w,onTransactionReceiptReceived:y,onTransactionSubmissionError:h,resetToInitialState:A,slippageTolerance:T,swapFungibles:I})=>{await Q.startSpan({name:UP(e,c?.type)},async()=>{A();let v;try{lh({currentQuote:n,quoteResponse:s,gasEstimation:p,swapTransactions:c,scanResults:u,isSwapSimulationDisabled:r,slippageTolerance:T,enableGasEstimationOptimization:g});let x=await I({type:t,callerAddress:o,swapTransactions:c,pendingSwapActivityRows:m.pendingSwapActivityRows,pendingApprovalActivityRows:m.pendingApprovalActivityRows,gasEstimation:p,connection:f});if(y(x),x.error)throw x.id||S(),v=x.id,x.error;w(x)}catch(x){let E=rn(x);h(E,v)}})};i();a();var Pa=C(R());i();a();var fh=.01,Ra="swap-validation",dh=e=>{if(e.resourceType==="nativeToken"){if(["60","966"].includes(e.slip44))return"0xeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeeee"}else if(e.resourceType==="address")return e.address??"";return""},gh=({scanResult:e,scanResultError:t,quote:r,sellToken:o,buyToken:n,simulationTolerance:s=fh})=>{if(t)return"valid";if(!r||!o||!n||!s)return Q.addBreadcrumb(Ra,`Missing necessary args: ${JSON.stringify({quote:!!r,scanResult:!!e,sellToken:o,buyToken:n,simulationTolerance:s})}`,"info"),new ne("MissingArgument");if(e?.error===yt.SIMULATION_FAILED)return"valid";if(e?.error){if(Q.addBreadcrumb(Ra,`Simulation error: ${JSON.stringify({error:e.error})}`,"info"),e.error===yt.SLIPPAGE_EXCEEDED)return new ne("slippageToleranceExceeded");if(e.error===yt.INSUFFICIENT_FUNDS)return new ne("INSUFFICIENT_FUNDS");if(e.error===yt.INSUFFICIENT_GAS)return new ne("INSUFFICIENT_GAS");if(e.error===yt.ACCOUNT_FROZEN)return new ne("AccountFrozen");if(e.error===yt.SIMULATION_TIMEOUT)return new ne("SIMULATION_TIMEOUT");if(e.error===yt.STALE_QUOTA)return new ne("STALE_QUOTA");if(e.error===yt.UNKNOWN_ERROR){let f=QP(e);return new ne("SimulationUnknownError",f)}return new ne("Scan results are not valid")}if(!e)return Q.addBreadcrumb(Ra,`Missing necessary args: ${JSON.stringify({quote:!!r,scanResult:!1,sellToken:o,buyToken:n,simulationTolerance:s})}`,"info"),new ne("MissingArgument");let p=e?.advancedDetails,{isSimulationToleranceExceeded:c,buyDiff:u,sellDiff:m}=_P(p,n,o,r,s);return c&&p?.networkId===W.Ethereum.Mainnet?(Q.addBreadcrumb(Ra,`Diff is larger than simulationTolerance: ${JSON.stringify({sellToken:o,buyToken:n,buyDiff:u.toString(),sellDiff:m.toString(),simulationTolerance:s})}`,"info"),new ne("exceedsSimulationTolerance")):"valid"},_P=(e,t,r,o,n=fh)=>{let s=e?.tokenChange||[],p=new N(s.find(y=>y.address===dh(t))?.value??"0"),c=new N(s.find(y=>y.address===dh(r))?.value??"0").abs(),u=r.resourceType==="nativeToken",m=new N(o.buyAmount),f=new N(o.sellAmount),g=p.minus(m).dividedBy(m).absoluteValue(),S=c.minus(f);if(u&&pe(o)){let y=zo(o);S=S.minus(y)}return S=S.dividedBy(f).absoluteValue(),{isSimulationToleranceExceeded:!((pe(o)||g.lt(n))&&S.lt(n)),buyDiff:g,sellDiff:S}};function QP(e){return e.simulationError?e.simulationError.humanReadableError:e.warnings[0]?.message}var yh=({swapTransactions:e})=>{let t=b(T=>T.sellFungible),r=b(T=>T.buyFungible),o=t?.data.chain.id??W.Solana.Mainnet,{data:n}=vt(t?{address:t.data.walletAddress,networkID:t.data.chain.id}:void 0),{selectedSetting:s}=De(),p=n?.address??"",{currentQuote:c,quoteResponse:u}=V(),m=Ue(Xe(c)),f=he(t?.data.chain.id,r?.data.chain.id),g=u?.buyToken?oe(u?.buyToken):void 0,S=u?.sellToken?oe(u?.sellToken):void 0,w={type:"transaction",userAccount:p,params:void 0,url:"https://phantom.app",networkID:o,context:f?"bridge":"swap",provider:m,slippageType:s.type,metadata:{swapper:{quoteOutputAmount:c?.buyAmount,quoteOutputToken:g,quoteInputAmount:c?.sellAmount,quoteInputToken:S}}};e?.type==="evm"&&F.isEVMNetworkID(o)&&(w.params={transactions:e.transactions}),e?.type==="solana"&&F.isSolanaNetworkID(o)&&(w.params={transactions:e.transactions,method:"signAndSendTransaction",simulatorConfig:{decodeAccounts:!1}});let{transition:y}=Vt(),h=(0,Pa.useCallback)((T,I)=>{I&&y({type:"simulationRequestError",error:I}),T&&y({type:"simulationResponseReceived",scannedTransactionResult:T})},[y]),A=qn(w,{disableRefetch:!0,onSettled:h});return(0,Pa.useMemo)(()=>{if(A.isLoading)return{isLoading:!0,state:"loading"};let T=gh({scanResult:A.data,scanResultError:A.isError,quote:c,sellToken:u?.sellToken,buyToken:u?.buyToken,simulationTolerance:u?.simulationTolerance});return{isLoading:A.isLoading,state:T}},[A,c,u])};function wh({isBridge:e,addressType:t,isSwapSimulationDisabled:r,onTransactionReceiptReceived:o,onTransactionSubmissionError:n,resetToInitialState:s,sender:p}){let{logTransactionSubmitted:c,logSwapFailureNoSignature:u}=po(),{connection:m}=nr(),{data:f}=te(),{data:[g]}=ae(["enable-swapper-gas-estimation-optimization"]),{currentQuote:S,quoteResponse:w}=V(),y=es(),h=b(X=>X.maxGasEstimation),A=b(X=>X.sellFungible),T=A?.data.chain.id??W.Solana.Mainnet,{data:I,isSuccess:v}=br({callerAddress:p,quote:S,sellFungible:A}),x=yh({swapTransactions:I}),{pendingActivityRows:E}=jw({sender:p}),{mutateAsync:P}=wu({accountIdentifier:f?.identifier??"",networkID:T}),[k,H]=(0,Sh.useState)(!1),j=async()=>(H(!0),mh({isBridge:e,addressType:t,isSwapSimulationDisabled:r,sender:p,currentQuote:S,quoteResponse:w,gasEstimation:h,swapTransactions:I,scanResults:x,pendingActivityRows:E,connection:m,slippageTolerance:y,enableGasEstimationOptimization:g,logSwapFailureNoSignature:u,logTransactionSubmitted:c,swapFungibles:P,onTransactionSubmissionError:n,onTransactionReceiptReceived:o,resetToInitialState:s}));return{isReadyToExecute:k||(!x.isLoading||r)&&v,executeSwap:j}}i();a();var ka=C(R());var hh=({goToSwapTab:e,goToSwapReview:t,goToActivityTab:r})=>{let{resume:o}=st(),{clearQuotes:n}=yu(),s=$t(),p=(0,ka.useCallback)(()=>{o(),n(),e()},[e,o,n]),c=(0,ka.useCallback)(()=>{o(),t()},[t,o]),u=(0,ka.useCallback)(()=>{s(),r()},[r,s]);return{onClose:p,onRetry:c,onSwapSuccess:u}};i();a();function Ah(){let{data:[e]}=ae(["kill-swapper-simulation"]);return e}i();a();var sn=C(R());var bh=({onTransactionConfirmationSuccess:e,onTransactionConfirmationError:t,txReceipt:r})=>{let o=G(),{data:n}=te(),s=(0,sn.useMemo)(()=>n?.addresses??[],[n]),p=mm(s),c=$t(),{logTransactionConfirmed:u,logTransactionConfirmationFailed:m}=po(),f=b(T=>T.sellFungible),{currentQuote:g}=V(),{sellAmount:S}=g??{sellAmount:"0"},w=(0,sn.useCallback)(async T=>{if(u(T),c(),f){let I=new N(f.data.amount),v=new N(S),x=I.minus(v).toString();Ji(o,{id:Xo(f),amount:x})}p()},[u,c,S,f,p,o]),{isSuccess:y,isError:h,error:A}=cm(w,r);(0,sn.useEffect)(()=>{if(y)e();else if(h){let T=rn(A);t(T),m(r,T)}},[y,h,A,r,m,e,t])};function Th({isLedger:e,goToSwapTab:t,goToSwapReview:r,goToActivityTab:o}){let{currentQuote:n}=V(),s=b(_=>_.buyFungible),p=b(_=>_.sellFungible),c=$t(),u=Ah(),{logTransactionSubmissionError:m}=po(),[f]=(0,Ke.useState)(he(p?.data.chain.id,s?.data.chain.id)),g=p?.data.chain.id??W.Solana.Mainnet,{data:S}=te(),w=(0,Ke.useMemo)(()=>S?.addresses??[],[S]),y=(0,Ke.useMemo)(()=>w.find(_=>_.networkID===p?.data.chain.id)?.address??"",[w,p]),h=(0,Ke.useMemo)(()=>F.getAddressType(g),[g]),{data:A}=br({callerAddress:y,quote:n,sellFungible:p}),[T,I]=(0,Ke.useState)(),[v,x]=(0,Ke.useState)("loading"),[E,P]=(0,Ke.useState)(),k=()=>{x("loading"),P(void 0),I(void 0)},H=_=>{I(_)},j=(0,Ke.useCallback)((_,ct)=>{x("error"),P(_),m(_,ct)},[m]),M=(0,Ke.useCallback)(()=>{x("success")},[]),X=(0,Ke.useCallback)(_=>{x("error"),P(rn(_))},[]);bh({onTransactionConfirmationSuccess:M,onTransactionConfirmationError:X,txReceipt:v==="error"?void 0:T});let{executeSwap:Y,isReadyToExecute:ye}=wh({isBridge:f,addressType:h,isSwapSimulationDisabled:u,onTransactionSubmissionError:j,onTransactionReceiptReceived:H,resetToInitialState:k,sender:y}),{onClose:z,onRetry:J,onSwapSuccess:ce}=hh({goToSwapTab:t,goToSwapReview:r,goToActivityTab:o});return{...Xu({sellFungible:p,txReceipt:T,swapTransactions:A,isBridge:f,txStatus:v,isReadyToExecute:ye,isLedger:e,txError:E}),executeSwap:Y,resetSwapper:c,onClose:z,onRetry:J,onSwapSuccess:ce}}i();a();i();a();i();a();var xh=C(R());i();a();var Fh=C(R());var OP={status:!1,timestamp:""},Ih=()=>{let{data:e}=it({key:"hasViewedAutoSlippageOptIn",queryKey:ue.hasViewedAutoSlippageOptIn()}),{mutateAsync:t}=Tt({key:"hasViewedAutoSlippageOptIn",queryKey:ue.hasViewedAutoSlippageOptIn()}),r=(0,Fh.useCallback)(o=>void t({status:o,timestamp:new Date().toISOString()}),[t]);return{viewedOptInState:e??OP,setViewedOptInState:r}};var Ju=()=>{let{viewedOptInState:e,setViewedOptInState:t}=Ih(),{selectedSetting:r}=De(),{reviewInterceptors:o}=Yu(),n=ha(),{connection:s}=nr(),p=ee(),c=ds(),u=D.language,m=$(),f=$t(),g=G();return{executeInterceptors:(0,xh.useCallback)(w=>{for(let y of o)if(y.intercept({hasViewedAutoSlippageOptIn:e.status,selectedSetting:r.type,setHasViewedAutoSlippageOptIn:t,showOverlay:w,swapper:n,connection:s,storage:p,vaultProxy:c,locale:u,analytics:m,resetSwapper:f,queryClient:g})==="interrupt")return"interrupt";return"continue"},[o,e.status,r.type,t,n,s,p,c,u,m,f,g])}};i();a();var _ge=Be(e=>({quoteSpan:void 0,previousBuyAmount:void 0,previousSellAmount:void 0,setQuoteSpan(t){e({quoteSpan:t})},setPreviousBuyAmount(t){e({previousBuyAmount:t})},setPreviousSellAmount(t){e({previousSellAmount:t})}}));i();a();var Vge=Be(e=>({initialLoadSpan:void 0,setInitialLoadSpan(t){e({initialLoadSpan:t})}}));i();a();var co=C(R());var MP=l.object({programId:l.string(),complete:l.boolean(),progress:l.string()}),qP=l.object({bondingCurves:l.record(l.string(),MP)});function GP(e){let t=Eh(e),o=G().getQueryData(t.queryKey),{data:n,isFetching:s,isError:p}=B({...t,initialData:o,staleTime:0}),c=(0,co.useMemo)(()=>zt(),[]);return(0,co.useMemo)(()=>{let u=o===n;if(s&&!u)return{type:"Loading"};if(p)return{type:"Error"};let m=qP.safeParse(n);if(!m.success)return{type:"Error"};let f=m.data.bondingCurves[e];if(!f)return{type:"NoBondingCurve"};let g=c.find(w=>w.programId===f.programId);if(!g)return{type:"NoBondingCurve"};let S=HP(f.progress);return S?f.complete?{type:"Graduated",ugcProgram:g}:{type:u&&s?"BondingCurveRefreshing":"BondingCurve",progressPercentage:S,ugcProgram:g}:{type:"Error"}},[o,e,n,p,s,c])}var VP=e=>{let t=G(),r=Eh(e),o=Qe.isFeatureEnabled("enable-ugc-swaps");return(0,co.useCallback)(async()=>{o&&await t.refetchQueries({queryKey:r.queryKey,type:"active"})},[t,r.queryKey,o])},Eh=e=>{let t=(0,co.useMemo)(()=>zt(),[]),r=(0,co.useMemo)(()=>t.map(n=>n.programId),[t]);return{queryKey:["bonding-curve",e,r],queryFn:()=>KP(e,r)}},KP=async(e,t)=>(await U.api().post("/ugc/v1/bonding-curve",{tokens:[e],programIds:t})).data,HP=e=>{let t=Number(e);if(!isNaN(t))return Math.round(t*100)};i();a();var zP=C(R());var oye=["simulationRequestErrored","simulationFailed","transactionSubmissionFailed","transactionNotConfirmed"];i();a();var Da=C(R());var jP=["enable","reject"],WP=({continueSwap:e,dismiss:t})=>{let r=$(),{setSlippageSetting:o}=De(),n=(0,Da.useCallback)(p=>{switch(t(),p){case"enable":r.capture("autoSlippageOptIn",{data:{step:"enabledAutoSlippage"}}),o({type:"auto"}),jr();break;case"reject":r.capture("autoSlippageOptIn",{data:{step:"rejectedAutoSlippage"}}),o({type:"fixed"}),e();break}},[e,t,o,r]),s=(0,Da.useMemo)(()=>jP.map(p=>({label:D.t(p==="enable"?"swapSlippageOptInEnableOption":"swapSlippageOptInRejectOption"),variant:p==="enable"?"primary":"secondary",onPress:()=>n(p)})),[n]);return{title:D.t("swapSlippageOptInTitle"),description:D.t("swapSlippageOptInSubtitle"),learnMoreText:D.t("commandLearnMore"),learnMoreUrl:Zi,options:s}};i();a();var Ch=C(R());var YP=({goToAssetSelect:e})=>{let{setSearchQuery:t}=b();return(0,Ch.useCallback)(r=>{r==="buy"&&t(""),e(r)},[e,t])};i();a();i();a();i();a();var an=C(R());i();a();var Zu=C(R());i();a();var kh=C(R());i();a();function Rh(e){if(e===void 0)return"#999";let t=parseFloat(e.toFixed(2));switch(!0){case t>=5:return"#EB3742";case(t>=1&&e<5):return"#FFDC62";case(e>=0&&e<1):return"#21E56F";default:return"#777777"}}i();a();function Ph(e){if(e===void 0)return"-";switch(!0){case e>=1e3:return">1000%";case(e>=.01&&e<1e3):return`${e.toFixed(2)}%`;case(e>0&&e<.01):return"<0.01%";case e===0:return"0%";default:return"-"}}function Na(e){return(0,kh.useMemo)(()=>{let t="",r="";if(e===void 0)return;let o=e*100;return t=Rh(o),r=Ph(o),{color:t,text:r,percentage:o}},[e])}i();a();var Ba=({priceImpact:e,slippage:t,displayRefuelRow:r,transferFees:o,shouldShowFees:n,shouldShowPriceImpact:s,shouldShowSlippage:p})=>{let c=[];return e&&e.percentage>tu&&c.push("highPriceImpact"),t&&t.percentage>=ru&&c.push("highSlippage"),(o?.totalPercentFees??0)>0&&c.push("transferFees"),c.push("providerPressable"),c.push("exchangeRate"),n&&c.push("fees"),s&&c.push("priceImpact"),p&&c.push("slippage"),r&&c.push("refuel"),c};i();a();function La(e){let t="",r="";if(e!==void 0)return t=XP(e),r=JP(e),{color:t,text:r,percentage:e}}function XP(e){let t=parseFloat(e.toFixed(2));switch(!0){case t>2:return"#EB3742";case(t>=1&&e<=2):return"#FFDC62";default:return"#777777"}}function JP(e){switch(!0){case e>=1e3:return">1000%";case(e>=.01&&e<1e3):return`${e.toFixed(2)}%`;case(e>0&&e<.01):return"<0.01%";case e===0:return"0%";default:return"-"}}i();a();var Ua=e=>e.reduce((t,r)=>{if(!r||r.type!=="SPL")return t;let o;return r.type==="SPL"&&(o=ZP(r)),o?{fees:[...t.fees,o],totalPercentFees:t.totalPercentFees+o.percentFee}:t},{fees:[],totalPercentFees:0}),ZP=e=>{if(e.type!=="SPL")return;let r=(e.data.mintExtensions??[]).find(o=>o.extension==="transferFeeConfig");if(r)return{caip19:oe(q(e)),fungibleKey:e.data.key,symbol:e.data.symbol??"",name:e.data.name??"",percentFee:r.state.newerTransferFee.transferFeeBasisPoints/100}};var Dh=()=>{let{currentQuote:e,quoteResponse:t}=V(),r=Na(on(e)),o=(0,Zu.useMemo)(()=>La(t?.slippageTolerance),[t?.slippageTolerance]),{showRefuelOption:n,refuelAmount:s}=Jr(),p=n&&!!s,c=b(S=>S.sellFungible),u=b(S=>S.buyFungible),m=(0,Zu.useMemo)(()=>Ua([c,u]),[c,u]),{data:[f]}=ae(["enable-swapper-skip-review"]);return{logSwapHomeState:()=>{let S=Ba({priceImpact:r,slippage:o,displayRefuelRow:p,transferFees:m,shouldShowFees:f,shouldShowPriceImpact:!!r&&f,shouldShowSlippage:!!o&&f}),w={slippage:JSON.stringify(o),priceImpact:JSON.stringify(r),rows:S};Q.addBreadcrumb("swapper","swap-home","info",mo(w))}}};i();a();var Bh=C(R());i();a();function Nh({sellFungible:e,totalFees:t,minRequiredBalance:r,currentQuote:o}){let n=t.maxAmount??t.amount,s=e?.type?Le(e?.type):!1,{source:p,destination:c}=r;if(s){let u=new K(o?.sellAmount??0);return n.plus(u)}else{let u=p.plus(c);return n.plus(u)}}var Lh=()=>{let{currentQuote:e}=V(),{totalFees:t}=at(),r=b(p=>p.sellFungible),o=Zo(),n=Jo();return(0,Bh.useMemo)(()=>Nh({sellFungible:r,totalFees:t,minRequiredBalance:{source:o,destination:n},currentQuote:e}),[r,t,o,n,e])};i();a();function Uh({sellFungible:e,sellNativeFungible:t,totalNativeRequirement:r}){let o=e?.data.chain.id,n=t?Ce(t.data.amount,t.data.decimals):"-",s=t?Ce(r,t.data.decimals):"-";return{networkId:o,balance:n,required:s}}i();a();function _h({quoteResponse:e,hasInsufficientBalance:t,insufficientBalanceArgs:r,isTermsAcknowledged:o,shouldSkipSwapReview:n,logSwapHomeState:s,goToInsufficientBalance:p,goToSwapTermsOfService:c,goToSwapReview:u,onSwap:m,executeInterceptors:f}){if(e)t?p(r):(s(),o?f()==="continue"&&(n?m():u()):c());else return}var el=({goToSwapTermsOfService:e,goToSwapReview:t,goToInsufficientBalance:r,goToConfirmation:o,showOverlay:n})=>{let{data:[s]}=ae(["enable-swapper-skip-review"]),{quoteResponse:p}=V(),c=b(v=>v.sellFungible),u=c?.data.chain.id,{data:m}=Vu(),{logSwapHomeState:f}=Dh(),{fungible:g}=rt({key:u?Ao(u,void 0):void 0}),S=Lh(),w=(0,an.useMemo)(()=>new K(g?.data.amount??0).lt(S),[g,S]),y=(0,an.useMemo)(()=>Uh({sellFungible:c,sellNativeFungible:g,totalNativeRequirement:S}),[c,g,S]),h=jn({goToConfirmation:o}),{executeInterceptors:A}=Ju(),T=(0,an.useCallback)(()=>A(n),[A,n]);return{onClick:(0,an.useCallback)(()=>_h({quoteResponse:p,hasInsufficientBalance:w,insufficientBalanceArgs:y,isTermsAcknowledged:m,shouldSkipSwapReview:s,logSwapHomeState:f,goToInsufficientBalance:r,goToSwapTermsOfService:e,goToSwapReview:t,onSwap:h,executeInterceptors:T}),[p,w,y,m,s,f,r,e,t,h,T])}};i();a();i();a();i();a();var Qh=({sellFungible:e,totalFees:t,transactionSpeedsToUnitCost:r,currentQuote:o,isBridge:n,minRequiredBalance:s})=>{let p=e?Xi({sellFungible:e,totalFees:t.amount,transactionSpeedsToUnitCost:F.isEVMNetworkID(e.data.chain.id)?r:void 0,isBridge:n,minRequiredBalance:s}):"0",c=e?.data.decimals;return o?.sellAmount?new N(Z(o.sellAmount,c??0)).isLessThanOrEqualTo(new N(p)):!0};var Oh=({sellFungible:e,isFetchingQuote:t,quoteResponse:r,currentQuote:o,gasEstimation:n,maxGasEstimation:s,hasNoRoutes:p,totalFees:c,transactionSpeedsToUnitCost:u,isBridge:m,minRequiredBalance:f,enableGasEstimationOptimization:g,shouldSkipSwapReview:S})=>{let w=Qh({sellFungible:e,totalFees:c,transactionSpeedsToUnitCost:u,currentQuote:o,isBridge:m,minRequiredBalance:f});switch(tk({quoteResponse:r,sellFungible:e,currentQuote:o,hasNoRoutes:p,hasEnoughAssets:w,isFetchingQuote:t,gasEstimation:n,maxGasEstimation:s,enableGasEstimationOptimization:g,shouldSkipSwapReview:S})){case"notSwappable":return{title:D.t("swapReviewFlowActionButtonPrimary"),type:"secondary",disabled:!0};case"swappable":return{title:D.t("swapReviewFlowActionButtonPrimary"),type:"primary",disabled:!1};case"reviewable":return{title:D.t("swapFlowActionButtonText"),type:"primary",disabled:!1};case"notReviewable":return{title:D.t("swapFlowActionButtonText"),type:"secondary",disabled:!0};case"insufficientBalance":return{title:D.t("swapReviewInsufficientBalance"),type:"alert",disabled:!0};case"noRoutes":return{title:D.t("swapNoQuotesFound"),type:"secondary",disabled:!0}}},ek=({sellFungible:e,enableGasEstimationOptimization:t})=>{let r=e?.data.chain.id??W.Solana.Mainnet;return t?F.isEVMNetworkID(r):!0};function tk({quoteResponse:e,sellFungible:t,currentQuote:r,hasNoRoutes:o,hasEnoughAssets:n,isFetchingQuote:s,gasEstimation:p,maxGasEstimation:c,enableGasEstimationOptimization:u,shouldSkipSwapReview:m}){if(!n)return"insufficientBalance";if(o)return"noRoutes";let f=ek({sellFungible:t,enableGasEstimationOptimization:u}),g=(p??[]).length>0&&(c??[]).length>0;return!e||!t||!r||f&&!g||s?m?"notSwappable":"notReviewable":m?"swappable":"reviewable"}function rk({goToInsufficientBalance:e,goToSwapReview:t,goToSwapTermsOfService:r,goToConfirmation:o,showOverlay:n}){let{currentQuote:s,isFetchingQuote:p,quoteResponse:c}=V(),{data:[u,m]}=ae(["enable-swapper-gas-estimation-optimization","enable-swapper-skip-review"]),f=b(M=>M.hasNoRoutes),g=b(M=>M.sellFungible),S=g?.data.chain.id,w=b(M=>M.gasEstimation),y=b(M=>M.maxGasEstimation),A=b(M=>M.buyFungible)?.data.chain.id,T=he(S,A),{totalFees:I}=at(),{data:v}=ws({networkID:S}),x=Zo(),E=Jo(),{title:P,type:k,disabled:H}=Oh({sellFungible:g,currentQuote:s,quoteResponse:c,isFetchingQuote:p,isBridge:T,gasEstimation:w,maxGasEstimation:y,hasNoRoutes:f,totalFees:I,transactionSpeedsToUnitCost:v,minRequiredBalance:{source:x,destination:E},enableGasEstimationOptimization:u,shouldSkipSwapReview:m}),{onClick:j}=el({goToInsufficientBalance:e,goToSwapReview:t,goToSwapTermsOfService:r,goToConfirmation:o,showOverlay:n});return{disabled:H,title:P,type:k,onClick:j}}i();a();i();a();var ts=C(R());i();a();var pn=C(R());var ok=N(1);function Mh(e,t,r){let o=$(),[n,s]=(0,pn.useState)(!1),p=(0,pn.useCallback)(async()=>{await o.capture("swapperToggleBestPriceRatio",{data:{unitCurrency:n?"buy token":"sell token"}}),s(!n)},[o,n]),c=b(m=>m.sellFungible),u=b(m=>m.buyFungible);return(0,pn.useMemo)(()=>{let m=!1,f="";if(t&&r){let g=u?.data,S=c?.data;if(g&&S){let{buyAmount:w,sellAmount:y}=r,h=Z(w,g.decimals),A=Z(y,S.decimals),T=h.dividedBy(A),I=`1 ${S.symbol} \u2248 ${$o(T)} ${g.symbol}`,v=`1 ${g.symbol} \u2248 ${$o(ok.dividedBy(T))} ${S.symbol}`;m=new N(r.buyAmount).isEqualTo(e),f=n?v:I}}return{isBestRate:m,rate:f,flip:p}},[e,r,n,t,c,u,p])}i();a();var qh=({buyNetwork:e,sellNetwork:t,executionDuration:r,priceImpact:o,slippage:n,displayRefuelRow:s})=>{let p=[];return p.push("provider"),p.push("exchangeRate"),e&&t&&e!==t&&p.push("network"),p.push("fees"),r>0&&p.push("executionDuration"),o&&p.push("priceImpact"),n&&p.push("slippage"),s&&p.push("refuel"),p};function nk({isSwapReview:e,showProviders:t}){let{bestBuyAmount:r,currentQuote:o,hasNoRoutes:n,quoteResponse:s,quotes:p,isFetchingQuote:c}=V(),u=Mh(r,s,o),m=Na(on(o)),f=es(),g=(0,ts.useMemo)(()=>La(f),[f]),S=ua(Xe(o)),w=va(o),y=oo(w),[h]=(0,ts.useState)(y),A=s?.sellToken?.chainId?F.getChainName(s.sellToken.chainId):void 0,T=s?.buyToken?.chainId?F.getChainName(s.buyToken.chainId):void 0,I=at(),v=qu(I),{showRefuelOption:x,refuelAmount:E}=Jr(),P=b(z=>z.sellFungible),k=b(z=>z.buyFungible),H=(0,ts.useMemo)(()=>Ua([P,k]),[P,k]),{data:[j]}=ae(["enable-swapper-skip-review"]),M=x&&(!e||!!E),Y={exchangeRate:u,priceImpact:m,slippage:g,provider:S,fees:I,executionDuration:w,uiEstimatedTime:h,isFetchingQuote:c,sellNetwork:A,buyNetwork:T,feeBreakdown:v,selectProviderEnabled:!e&&!!S,isSwapReview:e,showProviders:t,transferFees:H},ye=e?qh({exchangeRate:u,priceImpact:m,slippage:g,displayRefuelRow:M,buyNetwork:T,sellNetwork:A,executionDuration:w}):Ba({priceImpact:m,slippage:g,displayRefuelRow:M,transferFees:H,shouldShowFees:j,shouldShowPriceImpact:!!m&&j,shouldShowSlippage:!!g&&j});return{exchangeRate:u,hasNoRoutes:n,priceImpact:m,slippage:g,provider:S,fees:I,nrOfQuotes:p.length,executionDuration:w,isFetchingQuote:c,sellNetwork:A,buyNetwork:T,rows:ye,sharedRowProps:Y}}i();a();var Kh=C(R());i();a();var Yt=()=>!0,Gh={tokens:Yt,prices:pm,collectibles:fm,currency:Ol,staking:Sm,swapper:Mu,transferFungibleLoggingContext:Yt,user:Rl,[ql]:Yt,[yr]:Yt,[bi]:Yt,[dm]:Yt,[An]:Yt,[gm]:Yt,[ht]:Yt,"test-debug-query":os},Vh=e=>{if(e.queryKey.length===0||e.state.status!=="success")return!1;let t=String(e.queryKey[0]);return Gh[t]?Gh[t](e):!1};var sk=new pl({defaultOptions:{mutations:{retry:!1,networkMode:"offlineFirst"},queries:{retry:!1,staleTime:1/0,gcTime:1/0,refetchOnWindowFocus:!1,networkMode:"offlineFirst"}}}),ik=il({underlyingStorage:new om,throttleMs:1e3,enableDebugLogs:os()}),vAe=e=>Kh.default.createElement(cl,{client:sk,persistOptions:{persister:ik,dehydrateOptions:{shouldDehydrateQuery:Vh},buster:ak,maxAge:1/0}},e.children),ak="24.26.0";i();a();var pk=(_=>(_.HasOnboarded="hasOnboarded",_.ResetHasOnboarded="resetHasOnboarded",_.LockExtension="lockExtension",_.UnlockExtension="unlockExtension",_.IsExtensionUnlocked="IsExtensionUnlocked",_.SetPassword="setPassword",_.VerifyPassword="verifyPassword",_.UpdatePassword="updatePassword",_.ImportPrivateKeyAccount="importPrivateKeyAccount",_.ImportReadOnlyAccount="importReadOnlyAccount",_.ImportSeedAccount="importSeedAccount",_.ImportSeedlessAccount="importSocialSeedAccount",_.IsExistingSeed="isExistingSeed",_.AddAccountForSeed="addAccountForSeed",_.AddAccountForSeedless="addAccountForSeedless",_.AddLedgerAccounts="addLedgerAccounts",_.DeriveAddresses="deriveAddresses",_.ExportPrivateKey="exportPrivateKey",_.ExportEntropy="exportEntropy",_.SetAccountIcon="setAccountIcon",_.RemoveAccount="removeAccount",_.ReorderAccount="reorderAccount",_.GetAllAccounts="getAllAccounts",_.GetAllSeeds="getAllSeeds",_.GetAllSeedless="getAllSeedlessSeeds",_.GetAuthenticationPublicKey="getAuthenticationPublicKey",_.Sign="sign",_.SyncAccounts="syncAccounts",_.CheckVaultIntegrity="checkVaultIntegrity",_.LogMessage="logMessage",_.DownloadLogs="downloadLogs",_.ProviderInjectionOptions="providerInjectionOptions",_.ServiceWorkerMutexAcquire="serviceWorkerMutexAcquire",_.ServiceWorkerMutexRelease="serviceWorkerMutexRelease",_))(pk||{});export{Mw as a,Vie as b,jse as c,Jh as d,SA as e,bm as f,Ha as g,cA as h,yt as i,uA as j,Ts as k,np as l,hA as m,AA as n,sp as o,Sn as p,qe as q,Rp as r,FA as s,IA as t,Ep as u,wn as v,md as w,vp as x,xA as y,PA as z,hd as A,qE as B,KE as C,sf as D,Pn as E,nc as F,sc as G,ic as H,EE as I,vE as J,Nc as K,QE as L,WE as M,ev as N,ov as O,At as P,dv as Q,DV as R,ey as S,NV as T,BV as U,LV as V,ty as W,yv as X,hv as Y,_c as Z,Qc as _,Av as $,Cv as aa,Rv as ba,bv as ca,zr as da,Iv as ea,Pv as fa,Sy as ga,xv as ha,kv as ia,b as ja,yu as ka,hu as la,V as ma,Kt as na,cu as oa,to as pa,st as qa,Nv as ra,Bv as sa,qn as ta,pC as ua,Jr as va,lC as wa,jn as xa,hC as ya,AC as za,sR as Aa,mR as Ba,TC as Ca,wR as Da,vC as Ea,qu as Fa,$t as Ga,du as Ha,AR as Ia,IR as Ja,xR as Ka,kC as La,ER as Ma,DC as Na,$R as Oa,zR as Pa,jR as Qa,WR as Ra,YR as Sa,vR as Ta,Vu as Ua,sue as Va,pP as Wa,Yu as Xa,rk as Ya,es as Za,cP as _a,uP as $a,mP as ab,dP as bb,GP as cb,VP as db,Th as eb,nk as fb,WP as gb,YP as hb,Vh as ib,sk as jb,vAe as kb,pk as lb};
//# sourceMappingURL=chunk-SD2LXVLD.js.map
