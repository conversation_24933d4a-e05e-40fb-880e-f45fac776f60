async function callDex3API() {
    const url = "https://api.dex3.ai/pool/holders/v2";
    
    // <PERSON>ers từ curl command
    const headers = {
        "accept": "application/json",
        "accept-language": "en-US,en;q=0.9,vi;q=0.8",
        "access-control-allow-origin": "*",
        "clienttimestamp": "1755700469433",
        "content-type": "application/json",
        "dnt": "1",
        "origin": "https://dex3.ai",
        "priority": "u=1, i",
        "referer": "https://dex3.ai/",
        "sec-ch-ua": '"Not;A=Brand";v="99", "Google Chrome";v="139", "Chromium";v="139"',
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": '"Windows"',
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        // Cookie sẽ được browser tự động gửi nếu cùng domain
        "Cookie": "AWSALB=Vc7URdWebg2Xz2abQ4Bjgv6Ii0Kv67EsiDphgygA4jSSjmMsJswz1FOm1c4CAaJiuc9rvyZ0dI9hpcjd5s0vWHQ6tCefNubq8FSzxKK2F5Plup8mmM+vkKib9Bzq; AWSALBCORS=Vc7URdWebg2Xz2abQ4Bjgv6Ii0Kv67EsiDphgygA4jSSjmMsJswz1FOm1c4CAaJiuc9rvyZ0dI9hpcjd5s0vWHQ6tCefNubq8FSzxKK2F5Plup8mmM+vkKib9Bzq"
    };
    
    // Data từ curl command
    const data = {
        "address": "5kMoxf8b3ciAwD8ah9SBUWQH2aUdo8n2U1Su4mcANuos",
        "tag": "all"
    };
    
    try {
        console.log("Đang gửi request...");
        console.log("URL:", url);
        console.log("Data:", JSON.stringify(data, null, 2));
        
        const response = await fetch(url, {
            method: 'POST',
            headers: headers,
            body: JSON.stringify(data)
        });
        
        console.log("Status:", response.status);
        console.log("Status Text:", response.statusText);
        
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const responseData = await response.json();
        
        console.log("Response Headers:");
        for (let [key, value] of response.headers.entries()) {
            console.log(`${key}: ${value}`);
        }
        
        console.log("\n=== RESPONSE DATA ===");
        console.log(JSON.stringify(responseData, null, 2));
        
        return responseData;
        
    } catch (error) {
        console.error("Lỗi khi gọi API:", error);
        
        if (error.name === 'TypeError' && error.message.includes('CORS')) {
            console.log("\n⚠️ Lưu ý: Có thể gặp lỗi CORS khi chạy từ browser.");
            console.log("Hãy thử chạy từ Node.js hoặc sử dụng proxy/extension để bypass CORS.");
        }
        
        throw error;
    }
}

// Chạy function
callDex3API()
    .then(data => {
        console.log("✅ API call thành công!");
    })
    .catch(error => {
        console.log("❌ API call thất bại!");
    });

// Nếu muốn chạy trong Node.js, uncomment và cài đặt node-fetch:
// npm install node-fetch

/*
// Phiên bản cho Node.js
const fetch = require('node-fetch');

async function callDex3APINodeJS() {
    // Same code as above but with node-fetch
    // ...
}
*/