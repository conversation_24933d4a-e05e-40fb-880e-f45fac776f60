import{a as o}from"./chunk-ROF5SDVA.js";import{a,b as c}from"./chunk-OKP6DFCI.js";import{B as l,G as u,M as d,o as t,wa as p}from"./chunk-WIQ4WVKX.js";import{a as C}from"./chunk-7X4NV6OJ.js";import{f as h,h as m,n as s}from"./chunk-3KENBVE7.js";m();s();var i=h(C());var w=t.div`
  position: relative;
`,I=t(a.div)`
  width: ${n=>n.width}px;
  height: ${n=>n.width}px;
`,F=t(a.div)`
  top: 0;
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  justify-content: center;
  align-items: center;
`;var P=({type:n,iconWidth:r,defaultIcon:f,backgroundWidth:e=94})=>{let y=()=>{switch(n){case"default":return f??i.default.createElement(d,{width:r??30});case"warning":return i.default.createElement(p,{width:40,height:40,circleFill:"#FFDC62",exclamationFill:"#00000000"});case"failure":return i.default.createElement(l,{width:r??30});case"success":return i.default.createElement(u,{height:"100%",width:r??40,fill:"#21E56F"})}};return i.default.createElement(w,null,i.default.createElement(c,{mode:"wait",initial:!1},i.default.createElement(I,{width:e,key:n,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2}},(()=>{switch(n){case"default":return i.default.createElement(o,{diameter:e,color:"#181818",includeDarkBoxShadow:!0});case"warning":return i.default.createElement(o,{diameter:e,color:"#FFDC62",opacity:.1});case"failure":return i.default.createElement(o,{diameter:e,color:"#EB3742",opacity:.1});case"success":return i.default.createElement(o,{diameter:e,color:"#21E56F",opacity:.1})}})())),i.default.createElement(c,{mode:"wait",initial:!0},i.default.createElement(F,{key:n,initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},transition:{duration:.4,bounce:.4,type:"spring"}},y())))};export{P as a};
//# sourceMappingURL=chunk-XYFNIIUY.js.map
