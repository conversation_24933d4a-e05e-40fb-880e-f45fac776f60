import{b as g}from"./chunk-S24UABH5.js";import{a as i}from"./chunk-CCQRCL2K.js";import{a as p}from"./chunk-ROF5SDVA.js";import{j as c}from"./chunk-OKP6DFCI.js";import{o as n,rb as t,va as a}from"./chunk-WIQ4WVKX.js";import{m}from"./chunk-56SJOU6P.js";import{S as s}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-4P36KWOF.js";import{a as d}from"./chunk-7X4NV6OJ.js";import{f,h as e,n as l}from"./chunk-3KENBVE7.js";e();l();var o=f(d());var u=n(i).attrs({align:"center",justify:"space-between"})`
  height: 100%;
`,x=n(i).attrs({align:"center",justify:"center"})`
  height: 100%;
`,h=n(p)`
  svg {
    fill: #e5a221;
  }
`,L=()=>{let{t:r}=m(),{hideListingsErrorModal:C}=g();return o.default.createElement(u,null,o.default.createElement(x,null,o.default.createElement(i,{align:"center",margin:"0 0 20px 0"},o.default.createElement(h,{color:s("#E5A221",.1),diameter:94},o.default.createElement(a,{fill:"#E5A221"}))),o.default.createElement(i,{align:"center",margin:"0 0 10px 0"},o.default.createElement(t,{size:26,weight:500,lineHeight:34},r("collectiblesUnableToLoadListings"))),o.default.createElement(i,{align:"center",padding:"0 20px"},o.default.createElement(t,{size:16,color:"#777777",lineHeight:22},r("collectiblesUnableToLoadListingsDescription",{marketplace:"Magic Eden"})))),o.default.createElement(c,{onClick:C},r("commandClose")))},z=L;export{L as ListingsErrorPage,z as default};
//# sourceMappingURL=ListingsErrorPage-46T3R6RC.js.map
