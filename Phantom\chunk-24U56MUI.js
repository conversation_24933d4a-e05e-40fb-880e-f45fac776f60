import{c as s}from"./chunk-XJZOYN2T.js";import{Ya as o}from"./chunk-L3A2KHJO.js";import{a as l}from"./chunk-7X4NV6OJ.js";import{f as d,h as t,n as a}from"./chunk-3KENBVE7.js";t();a();var e=d(l());function m(){let{data:[r]}=o(["enable-sidepanel-tx-notifications"]),[n,p]=(0,e.useState)(!1);return(0,e.useEffect)(()=>{async function f(){let i=await s();i!=n&&p(i)}f()},[]),{isSidebarOpen:r&&n}}export{m as a};
//# sourceMappingURL=chunk-24U56MUI.js.map
