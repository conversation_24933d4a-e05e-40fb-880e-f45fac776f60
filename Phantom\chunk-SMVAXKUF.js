import{b as L}from"./chunk-W27Z2YZM.js";import{a as o}from"./chunk-XJTFMD4C.js";import{c as Q,da as T,jb as te}from"./chunk-SD2LXVLD.js";import{a as p,b as l,c as d}from"./chunk-UCBZOSRF.js";import{a as se,g as ne}from"./chunk-MNXYIK2W.js";import{a as M,c as P}from"./chunk-MHOQBMVI.js";import{a as E}from"./chunk-BTKBODVJ.js";import{A as re}from"./chunk-7ZN4F6J4.js";import{t as Z}from"./chunk-SLQBAOEK.js";import{Ib as N,Mc as z,Vb as U,Vc as Y,Wb as K,Xc as j,Zc as H,hc as W,he as ee,ic as J,jc as G,mc as $,vd as X}from"./chunk-MZZEJ42N.js";import{a as w}from"./chunk-56SJOU6P.js";import{A as q,Qa as k,pa as _,q as me,qa as F,ta as g}from"./chunk-L3A2KHJO.js";import{a as D}from"./chunk-7X4NV6OJ.js";import{f as y,h as c,m as Buffer,n as u}from"./chunk-3KENBVE7.js";c();u();var S=y(me()),oe=y(D()),a=y(E());var Ae=async(t,e,r)=>{let s=e.fold(f=>Buffer.from(f.buffer).toString("base64")),n=o("importPrivateKeyAccount",[t,s,r]),i=await g.startSpan({name:"importPrivateKeyAccount"},()=>d(a.default.runtime,n));if("error"in i)throw new Error(i.error.message);if(typeof i.result=="string")return JSON.parse(i.result);throw new Error("importPrivateKeyAccount: Invalid response from background script")},He=async(t,e,r)=>{let s=t.getEntropy(),n=o("importSeedAccount",[S.default.encode(s),e,r]);s.fill(0);let i=await g.startSpan({name:"importSeedAccount"},()=>d(a.default.runtime,n));if("error"in i)throw new Error(i.error.message);if(typeof i.result=="string")return JSON.parse(i.result);throw new Error("importS*edAccount: Invalid response from background script")},Xe=async(t,e,r)=>{let s=t.getEntropy(),n=o("importSocialSeedAccount",[S.default.encode(s),e,r]);s.fill(0);let i=await g.startSpan({name:"importSeedlessAccount"},()=>d(a.default.runtime,n));if("error"in i)throw new Error(i.error.message);if(typeof i.result=="string")return JSON.parse(i.result);throw new Error("importS*edlessAccount: Invalid response from background script")},Qe=async t=>{let e=t.getEntropy(),r=o("isExistingSeed",S.default.encode(e));e.fill(0);let s=await g.startSpan({name:"isExistingSeed"},()=>d(a.default.runtime,r));if("error"in s)throw new Error(s.error.message);if(typeof s.result=="boolean"||s.result===null)return s.result;throw new Error("isExistingS*ed: Invalid response from background script")},he=async t=>{let e=o("addAccountForSeed",[t]),r=await g.startSpan({name:"addAccountForSeed"},async()=>l(await a.default.runtime.sendMessage(p(e))));if("error"in r)throw new Error(r.error.message);if(typeof r.result=="string")return JSON.parse(r.result);throw new Error("addAccountForS*ed: Invalid response from background script")},we=async t=>{let e=o("addAccountForSeedless",[t]),r=await g.startSpan({name:"addAccountForSeedless"},async()=>l(await a.default.runtime.sendMessage(p(e))));if("error"in r)throw new Error(r.error.message);if(typeof r.result=="string")return JSON.parse(r.result);throw new Error("addAccountForS*ed: Invalid response from background script")},Se=async(t,e)=>{let r=o("addLedgerAccounts",[t,e]),s=await g.startSpan({name:"connectLedgerAccounts"},async()=>l(await a.default.runtime.sendMessage(p(r))));if("error"in s)throw new Error(s.error.message);if(typeof s.result=="string")return JSON.parse(s.result);throw new Error("connectLedgerAccounts: Invalid response from background script")},Be=async t=>{let e=o("importReadOnlyAccount",t),r=await g.startSpan({name:"addReadonlyAccount"},async()=>l(await a.default.runtime.sendMessage(p(e))));if("error"in r)throw new Error(r.error.message);if(typeof r.result=="string")return JSON.parse(r.result);throw new Error("addReadonlyAccount: Invalid response from background script")},Ze=async(t,e,r)=>{let s=o("exportPrivateKey",[t,e,r]),n=await g.startSpan({name:"exportPrivateKey"},async()=>l(await a.default.runtime.sendMessage(p(s))));if("error"in n)throw new Error(n.error.message);if(typeof n.result=="string")return k.from(Buffer.from(n.result,"hex"));throw new Error("exportPrivateKey: Invalid response from background script")},er=async(t,e)=>{let r=o("exportEntropy",[t,e]),s=await g.startSpan({name:"exportEntropy"},async()=>l(await a.default.runtime.sendMessage(p(r))));if("error"in s)throw new Error(s.error.message);if(typeof s.result=="string")return k.from(Buffer.from(s.result,"hex"));throw new Error("exportEntropy: Invalid response from background script")},Re=async()=>{let t=o("deriveAddresses",void 0),e=await d(a.default.runtime,t);if("error"in e)throw new Error(e.error.message);return e.result},Ee=async t=>{let e=o("removeAccount",t),r=await d(a.default.runtime,e);if("error"in r)throw new Error(r.error.message);return r.result},Me=async(t,e)=>{let r=o("reorderAccount",{identifier:t,toIndex:e}),s=await d(a.default.runtime,r);if("error"in s)throw new Error(s.error.message);return s.result},Pe=async t=>{let e=o("syncAccounts",{syncedAccounts:t}),r=l(await a.default.runtime.sendMessage(p(e)));if("error"in r)throw new Error(r.error.message);return r.result},xe=async()=>{let t=o("getAllAccounts",void 0),e=l(await a.default.runtime.sendMessage(p(t)));if("error"in e)throw new Error(e.error.message);if(typeof e.result=="string"){let r=JSON.parse(e.result);return Y({accounts:r}).accounts}throw new Error("fetchAllAccounts: Invalid response from background script")},Ie=async()=>{let t=o("getAllSeeds",void 0),e=l(await a.default.runtime.sendMessage(p(t)));if("error"in e)throw new Error(e.error.message);if(typeof e.result=="string")return JSON.parse(e.result);throw new Error("fetchAllSe*dMetas: Invalid response from background script")},be=async()=>{let t=o("getAllSeedlessSeeds",void 0),e=l(await a.default.runtime.sendMessage(p(t)));if("error"in e)throw new Error(e.error.message);if(typeof e.result=="string")return JSON.parse(e.result);throw new Error("fetchAllSe*dlessMetas: Invalid response from background script")},ve=async t=>{let e=o("getAuthenticationPublicKey",{secretIdentifier:t}),r=l(await a.default.runtime.sendMessage(p(e)));if("error"in r)throw new Error(r.error.message);if(typeof r.result=="string")return JSON.parse(r.result);throw new Error("getAuthenticationPublicK*y: Invalid response from background script")},ke=async()=>{let t=o("checkVaultIntegrity",void 0),e=l(await a.default.runtime.sendMessage(p(t)));if("error"in e)throw new Error(e.error.message);return e.result},Le=async(t,e)=>{let r=H.parse(e);"chainType"in r&&r.chainType==="solana"&&r.signingType==="transaction"&&typeof r.message!="string"&&(r.message=S.default.encode(r.message));let s=o("sign",{accountIdentifier:t,params:r}),n=l(await a.default.runtime.sendMessage(p(s)));if("error"in n)throw new Error(n.error.message);if(typeof n.result=="string")return l(n.result);throw new Error("sign: Invalid response from background script")},ae={addAccountForSeed:he,addReadonlyAccount:Be,importPrivateKeyAccount:Ae,connectLedgerAccounts:Se,fetchAllAccounts:xe,fetchAllSeedMetas:Ie,fetchAllSeedlessMetas:be,connectSolanaSeedVaultAccounts:()=>{throw new Error("Solana Seed Vault is not supported on browser extension.")},deriveAddresses:Re,removeAccount:async t=>{if(!await Ee(t))throw new Error("Error while removing account")},reorderAccount:async(t,e)=>{if(!await Me(t,e))throw new Error("Error while reordering account")},sign:Le,getAuthenticationPublicKey:ve,syncAccounts:Pe,addAccountForSeedless:we,checkVaultIntegrity:ke},ie=()=>ae;function rr({children:t}){return oe.default.createElement(X,{vault:ae},t)}c();u();c();u();var O=class{constructor(e){this.onBannerSeen=e=>{this.#e.capture("actionBannerSeenByUser",{data:{banner:e}})};this.onBannerClick=e=>{this.#e.capture("actionBannerClickedByUser",{data:{banner:e}})};this.onBannerDismiss=e=>{this.#e.capture("actionBannerDismissedByUser",{data:{banner:e}})};this.onInterstitialSeen=e=>{this.#e.capture("actionBannerInterstitialSeenByUser",{data:{banner:e}})};this.onInterstitialPrimaryClick=e=>{this.#e.capture("actionBannerInterstitialPrimaryButtonClickedByUser",{data:{banner:e}})};this.onInterstitialSecondaryClick=e=>{this.#e.capture("actionBannerInterstitialSecondaryButtonClickedByUser",{data:{banner:e}})};this.onInterstitialDismiss=e=>{this.#e.capture("actionBannerInterstitialDismissedByUser",{data:{banner:e}})};this.#e=e}#e};c();u();var Oe=({id:t,humanReadableId:e,description:r,bannerType:s})=>({id:t,humanReadableId:e,description:r,type:s}),Ve=({id:t,humanReadableId:e,description:r,bannerType:s,interstitial:n})=>{let{title:i,primaryButtonText:f,secondaryButtonText:A}=n,h;if(s==="Modal")for(let[I,R]of Object.values(n.lineItems).entries())h={...h??{},[`lineItem${I+1}`]:{title:R.title,description:R.description}};return{id:t,humanReadableId:e,description:r,type:s,interstitial:{title:i,body:h,primaryButtonText:f??w.t("commandContinue"),secondaryButtonText:A??w.t("commandDismiss")}}};c();u();var le=y(E());c();u();var V=y(E());var Ce=async()=>{let t=o("serviceWorkerMutexAcquire",void 0),e=l(await V.default.runtime.sendMessage(p(t)));if("error"in e)throw new Error(e.error.message);return()=>{let r=o("serviceWorkerMutexRelease",void 0);V.default.runtime.sendMessage(p(r))}},ce=async t=>{let e=await Ce();try{return await t()}finally{e()}};c();u();var ue=y(E()),pe=async(t,e,r)=>{let s=await ue.default.identity.launchWebAuthFlow({interactive:r,url:e});if(!G(t,s))return J(t,s);throw new Error("Error fetching auth code")};var C=ie(),De={getClientID:$,redirectURL:le.default.identity.getRedirectURL(),fetchAuthorizationCode:(t,e,r)=>pe(t,e,r)},qe={storage:new M,signer:{sign:(t,e)=>U(C,t,e),getAuthenticationPublicKey:t=>C.getAuthenticationPublicKey(t),getAllSecretIdentifiers:()=>K(C)},authConfig:De,queryClient:te,runExclusive:ce},_e=W(qe);_e.subscribe("userID",t=>{P.addUserProperties({authUserId:t})});c();u();var B=y(D());c();u();var x=class{async get(e){let r=null;try{if(r=self.localStorage.getItem(e),typeof r!="string")return null;try{return JSON.parse(r)}catch{return console.warn(`[DeprecatedLocalStorage.get] Error parsing JSON for key "${e}"`),r}}catch(s){let n=`[DeprecatedLocalStorage.get] Error getting key (${e}): ${s}`;throw new Error(n)}}async getAll(e){try{let r={};e=e||Object.keys(self.localStorage);for(let s of e)r[s]=await this.get(s);return r}catch(r){let s=`[DeprecatedLocalStorage.getAll] Error getting key(s) (${e}): ${r}`;throw new Error(s)}}async remove(e){let r=`[DeprecatedLocalStorage.remove] Error removing key(s) (${e}): Method not implemented`;throw new Error(r)}async set(e,r){let s=`[DeprecatedLocalStorage.set] Error setting key (${e}): Method not implemented`;throw new Error(s)}async setAll(e){let s=`[DeprecatedLocalStorage.setAll] Error setting key (${Object.keys(e)}): Method not implemented`;throw new Error(s)}subscribe(e){return console.warn("[DeprecatedLocalStorage.subscribe] Method not implemented"),()=>{}}async update(e){let r=`[DeprecatedLocalStorage.update] Error updating key(s) (${e}): Method not implemented`;throw new Error(r)}};var Fe=new M,Ne=new x,Ue=[...N,...Q,T,j,"i18nextLng"],gt=()=>{let{data:t,isFetching:e}=ee(),{data:r,isFetching:s}=re(),{data:n,isFetching:i}=_([{key:"Local Storage",storage:Fe},{key:"Deprecated Local Storage",storage:Ne}],Ue),f=z(),A=(0,B.useCallback)(async()=>{let{version:b}=chrome.runtime.getManifest(),v=await P.getDeviceId(),de=await f();return{"App Version":b,"Platform Information":se,"Display Language":Z(w.language),"Phantom Auth":de,"Device Id":v,...t,...r,...n}},[t,r,n,f]),h=e||s||i,I=(0,B.useCallback)(async b=>{b?.preventDefault();let v=await A();await F.downloadLog(v)},[A]),R=(0,B.useCallback)(()=>{L({url:ne})},[]),ge=(0,B.useCallback)(()=>{L({url:q})},[]);return{appState:A,isFetching:h,goCreateTicket:R,goSupportDesk:ge,downloadLogs:I}};export{He as a,Xe as b,Qe as c,Ze as d,er as e,be as f,ie as g,rr as h,O as i,Oe as j,Ve as k,gt as l,_e as m};
//# sourceMappingURL=chunk-SMVAXKUF.js.map
