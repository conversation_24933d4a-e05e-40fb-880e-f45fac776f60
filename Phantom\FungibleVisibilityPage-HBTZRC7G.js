import{a as ei}from"./chunk-HNH7ZX4P.js";import{a as ti}from"./chunk-2WECCVZD.js";import{E as J,Ma as ni}from"./chunk-JD6NH5K6.js";import{g as Y,k as R}from"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as j}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import{k as ii}from"./chunk-IWGMKDQE.js";import{g as X}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as Z}from"./chunk-CCQRCL2K.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import{a as K}from"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{D as Q,a as B,b as $}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{j as q}from"./chunk-OKP6DFCI.js";import{D as G,o as r,rb as M}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import{c as z}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{db as H,ec as P,gc as N,ib as _,tc as U}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as D,Pb as W}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as E}from"./chunk-56SJOU6P.js";import{V as C,X as w,ha as L}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as x}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as V,h as I,n as F}from"./chunk-3KENBVE7.js";I();F();var i=V(x());I();F();var t=V(x());var mi=r(j).attrs({align:"center",padding:"10px"})`
  background-color: #2a2a2a;
  border-radius: 6px;
  height: 74px;
  gap: 10px;
`,ui=r(Z)`
  flex: 1;
  min-width: 0;
  text-align: left;
  align-items: normal;
`,di=r(M).attrs({size:16,weight:600,lineHeight:19,noWrap:!0,maxWidth:"210px",textAlign:"left"})``,ci=r(M).attrs({color:"#777777",size:14,lineHeight:17,noWrap:!0})`
  text-align: left;
  margin-top: 5px;
`,si=t.default.memo(e=>{let{data:n,index:a,style:m}=e,{listItems:l,visibilityOverrides:o,accountId:u,setVisibilityOverrides:b,i18nStrings:p}=n,g=l[a],{chain:y,key:s,balance:f,name:h,logoUri:S,symbol:d,tokenAddress:c,walletAddress:k,spamStatus:v}=g.data,O=d??c,ri=h??p.unknownToken,T=_(o,{key:s,spamStatus:v}),li=H(o,{key:s,spamStatus:v})==="POSSIBLE_SPAM",A=d?`${L(f)} ${d}`:c?W(c):void 0;return t.default.createElement("div",{key:s,style:{...m,top:`${parseFloat(m.top)+20}px`}},t.default.createElement(mi,null,t.default.createElement(J,{tokenType:g.type,chainMeta:y,image:{type:"fungible",src:S,fallback:O},size:48}),t.default.createElement(ui,null,t.default.createElement(ii,{networkID:y.id,walletAddress:k},t.default.createElement(t.default.Fragment,null,t.default.createElement(di,null,ri),li?t.default.createElement(G,{className:B({marginLeft:4,minHeight:16,minWidth:16}),fill:$.colors.legacy.accentWarning,height:16,width:16}):null)),A?t.default.createElement(ci,null,A):null),t.default.createElement(ti,{id:s,checked:!T,onChange:()=>{s&&b({accountId:u,mutations:[{fungibleKey:s,isHidden:!T}]})}})))});var pi=74,gi=10,bi=pi+gi,oi=r.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  flex: 1;
`,yi=r.div`
  position: relative;
  width: 100%;
`,fi=()=>{let{handleHideModalVisibility:e}=ni(),{t:n}=E(),a=(0,i.useMemo)(()=>({close:n("commandClose"),search:n("assetListSearch"),unknownToken:n("assetDetailUnknownToken")}),[n]),{data:m,isPending:l}=D(),o=m?.identifier??"",{fungibles:u,isLoadingTokens:b}=N({showHiddenMints:!0}),[p,g]=(0,i.useState)(""),y=C(p)??"",s=w(u,y,U),{data:f,isPending:h}=P(o),{mutateAsync:S}=z(),d=(0,i.useCallback)(O=>g(O.currentTarget.value),[g]),c=(0,i.useCallback)(()=>e("fungibleVisibility"),[e]),k=(0,i.useMemo)(()=>({accountId:o,listItems:s,i18nStrings:a,searchQuery:p,visibilityOverrides:f,setVisibilityOverrides:S,handleSearch:d,handleCloseModal:c}),[o,s,a,p,f,S,d,c]),v=(0,i.useMemo)(()=>l||b||h,[l,h,b]);return{data:k,loading:v}},hi=i.default.memo(e=>{let n=(0,i.useRef)(null);return(0,i.useEffect)(()=>{setTimeout(()=>n.current?.focus(),200)},[]),i.default.createElement(oi,null,i.default.createElement(yi,null,i.default.createElement(X,{ref:n,tabIndex:0,placeholder:e.i18nStrings.search,maxLength:50,onChange:e.handleSearch,value:e.searchQuery})),i.default.createElement(R,null,i.default.createElement(Q,null,({height:a,width:m})=>i.default.createElement(Y,{width:m,height:a,itemData:{listItems:e.listItems,accountId:e.accountId,visibilityOverrides:e.visibilityOverrides,setVisibilityOverrides:e.setVisibilityOverrides,i18nStrings:e.i18nStrings},itemKey:(l,o)=>{let u=o.listItems[l];return`${u.data.key}-${u.data.chain.id}-${l}`},itemSize:bi,itemCount:e.listItems.length},si))))}),Si=()=>{let{data:e,loading:n}=fi();return i.default.createElement(oi,null,n?i.default.createElement(ei,null):i.default.createElement(hi,{...e}),i.default.createElement(K,null,i.default.createElement(q,{onClick:e.handleCloseModal},e.i18nStrings.close)))},Xi=Si;export{Si as FungibleVisibilityPage,Xi as default};
//# sourceMappingURL=FungibleVisibilityPage-HBTZRC7G.js.map
