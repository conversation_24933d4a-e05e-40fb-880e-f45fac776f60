import { Page } from '@playwright/test';
import { BasePage } from '../pages/BasePage';
import { DashboardSelectors } from '../../selectors/DashboardSelectors';

/**
 * FilterComponent - Handles all filtering operations on the Dashboard
 * This component encapsulates filter-specific functionality
 */
export class FilterComponent extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Open DEX filter dropdown
   */
  async openDexFilter(): Promise<void> {
    this.logAction('Opening DEX filter dropdown');
    await this.clickElement(DashboardSelectors.DEXXes, 'DEX Filter');
    await this.sleep(1000);
  }

  /**
   * Clear all selected DEXes
   */
  async clearAllDexes(): Promise<void> {
    this.logAction('Clearing all selected DEXes');
    await this.clickElement(DashboardSelectors.clearAllDexes, 'Clear All DEXes Button');
    await this.sleep(2000);
  }

  /**
   * Select a specific DEX by name
   */
  async selectDex(dexName: string): Promise<void> {
    this.logAction(`Selecting DEX: ${dexName}`);
    const displayName = this.getDexDisplayName(dexName);
    await this.clickElement(
      DashboardSelectors.dexCheckboxLabel(displayName), 
      `${dexName} Checkbox`
    );
    await this.sleep(2000);
  }

  /**
   * Select only one DEX (clear all others first)
   */
  async selectSingleDex(dexName: string): Promise<void> {
    try {
      this.logAction(`Selecting single DEX: ${dexName}`);
      
      await this.waitForPageLoad();
      await this.openDexFilter();
      await this.clearAllDexes();
      await this.selectDex(dexName);
      
      this.logAction(`Successfully selected single DEX: ${dexName}`);
    } catch (error) {
      console.error(`Failed to select DEX: ${error instanceof Error ? error.message : 'Unknown error'}`);
      await this.takeScreenshot(`select-dex-error-${dexName}`);
      throw error;
    }
  }

  /**
   * Verify which DEXes are currently selected
   */
  async verifySelectedDexes(): Promise<string[]> {
    this.logAction('Verifying selected DEXes');
    
    await this.openDexFilter();
    const selectedDexes: string[] = [];
    
    for (const dex of DashboardSelectors.defaultDexes) {
      try {
        const checkbox = this.page.locator(DashboardSelectors.dexCheckbox(dex));
        const isChecked = await checkbox.isChecked().catch(() => false);
        
        if (isChecked) {
          selectedDexes.push(dex);
          console.log(`✅ DEX ${dex} is selected`);
        } else {
          console.log(`❌ DEX ${dex} is not selected`);
        }
      } catch (error) {
        console.warn(`Could not check DEX ${dex}: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
    
    console.log(`Total selected DEXes: ${selectedDexes.length}/7`);
    return selectedDexes;
  }

  /**
   * Open security filter
   */
  async openSecurityFilter(): Promise<void> {
    this.logAction('Opening security filter');
    await this.clickElement(DashboardSelectors.securityFilter, 'Security Filter');
    await this.sleep(1000);
  }

  /**
   * Open liquidity filter
   */
  async openLiquidityFilter(): Promise<void> {
    this.logAction('Opening liquidity filter');
    await this.clickElement(DashboardSelectors.liquidityFilter, 'Liquidity Filter');
    await this.sleep(1000);
  }

  /**
   * Open market cap filter
   */
  async openMarketCapFilter(): Promise<void> {
    this.logAction('Opening market cap filter');
    await this.clickElement(DashboardSelectors.marketCapFilter, 'Market Cap Filter');
    await this.sleep(1000);
  }

  /**
   * Open age filter
   */
  async openAgeFilter(): Promise<void> {
    this.logAction('Opening age filter');
    await this.clickElement(DashboardSelectors.ageFilter, 'Age Filter');
    await this.sleep(1000);
  }

  /**
   * Open token filter
   */
  async openTokenFilter(): Promise<void> {
    this.logAction('Opening token filter');
    await this.clickElement(DashboardSelectors.tokenFilter, 'Token Filter');
    await this.sleep(1000);
  }

  /**
   * Verify all filters are visible
   */
  async verifyAllFiltersVisible(): Promise<void> {
    this.logAction('Verifying all filters are visible');
    
    const filters = [
      { selector: DashboardSelectors.DEXXes, name: 'DEX Filter' },
      { selector: DashboardSelectors.securityFilter, name: 'Security Filter' },
      { selector: DashboardSelectors.liquidityFilter, name: 'Liquidity Filter' },
      { selector: DashboardSelectors.marketCapFilter, name: 'Market Cap Filter' },
      { selector: DashboardSelectors.ageFilter, name: 'Age Filter' },
      { selector: DashboardSelectors.tokenFilter, name: 'Token Filter' }
    ];

    for (const filter of filters) {
      await this.verifyElementVisible(filter.selector, filter.name);
    }
  }

  /**
   * Close filter dropdown by clicking outside
   */
  async closeFilterDropdown(): Promise<void> {
    this.logAction('Closing filter dropdown');
    await this.page.mouse.click(10, 10);
    await this.sleep(500);
  }

  /**
   * Convert DEX name to display format
   */
  private getDexDisplayName(dexName: string): string {
    const dexNameMap: Record<string, string> = {
      'pump.fun': 'PumpFun',
      'pumpswap': 'Pumpswap',
      'moonshot': 'Moonshot',
      'raydium': 'Raydium',
      'orca': 'Orca',
      'meteora': 'Meteora',
      'fluxbeam': 'FluxBeam',
      'launchlab': 'LaunchLab'
    };

    const normalizedDexName = dexName.toLowerCase();
    return dexNameMap[normalizedDexName] || dexName;
  }

  /**
   * Verify token list is filtered by specific DEX
   */
  async verifyTokenListFilteredByDex(expectedDex: string, maxTokensToCheck: number = 20): Promise<boolean> {
    try {
      const expectedDexDisplay = this.getDexDisplayName(expectedDex);
      this.logAction(`Verifying first ${maxTokensToCheck} tokens are from DEX: ${expectedDex} (display: ${expectedDexDisplay})`);

      const tokenRowSelector = 'a.chakra-link.token-virtual-row';
      const dexImgSelector = '.css-jvoq11 img.css-ha03xt';

      // Hover on first row to stabilize the list
      const firstRow = this.page.locator(tokenRowSelector).first();
      await firstRow.hover();
      await this.sleep(1000);

      const totalTokens = await this.page.locator(tokenRowSelector).count();
      const tokensToCheck = Math.min(maxTokensToCheck, totalTokens);

      if (tokensToCheck === 0) {
        console.log("No tokens found to verify");
        await this.takeScreenshot('no-tokens-found');
        return false;
      }

      console.log(`Found ${totalTokens} tokens, checking ${tokensToCheck} tokens`);

      let nonMatchingTokens = 0;

      for (let i = 0; i < tokensToCheck; i++) {
        const tokenElement = this.page.locator(tokenRowSelector).nth(i);
        const tokenName = await tokenElement.locator('.chakra-text.css-16rit9e').textContent() || `Token ${i+1}`;
        const dexImg = tokenElement.locator(dexImgSelector);
        const actualDex = await dexImg.getAttribute('alt') || 'Unknown';

        if (actualDex !== expectedDexDisplay) {
          nonMatchingTokens++;
          console.log(`❌ Token ${i+1} "${tokenName}": DEX is "${actualDex}", not "${expectedDexDisplay}"`);
        }
      }

      if (nonMatchingTokens === 0) {
        console.log(`✅ All ${tokensToCheck} tokens are from DEX "${expectedDexDisplay}"`);
        return true;
      } else {
        console.log(`❌ ${nonMatchingTokens}/${tokensToCheck} tokens are not from DEX "${expectedDexDisplay}"`);
        await this.takeScreenshot(`dex-verification-failed-${expectedDex}`);
        return false;
      }
    } catch (error) {
      console.error(`Error verifying token list: ${error instanceof Error ? error.message : 'Unknown error'}`);
      await this.takeScreenshot(`dex-verification-error-${expectedDex}`);
      return false;
    }
  }
} 