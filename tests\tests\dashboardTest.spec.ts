import { test, expect } from '../base/TestBase';
import { ENV } from '../base/env';
import { DashboardSelectors } from '../selectors/DashboardSelectors';
import * as path from 'path';

test.describe('Dashboard tests', () => {
  const screenshotDir = path.join(process.cwd(), 'screenshots');

  test('Kiểm tra trang Dashboard sau khi đăng nhập', async ({ dashboardPage }) => {
    // Không cần đăng nhập nữa vì đã được thiết lập từ fixtures

    // Kiểm tra xem đã đăng nhập thành công chưa
    await dashboardPage.verifyLoginState();

    // Kiểm tra URL hiện tại
    await dashboardPage.checkCurrentUrl(ENV.BASE_URL);

    // Thực hiện các tác vụ khác trên trang Dashboard
    console.log('Test trang Dashboard hoàn thành thành công.');
  });

  test('Kiểm tra các thành phần giao diện Dashboard', async ({ dashboardPage }) => {
    try {
      // Truy cập tab All tokens
      await dashboardPage.navigateToAllTokens();

      // Sử dụng phương thức tổng hợp để kiểm tra giao diện Dashboard
      await dashboardPage.verifyDashboardUI(screenshotDir);
    } catch (error) {
      console.error('\n!!! KIỂM TRA GIAO DIỆN DASHBOARD THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');
      throw error;
    }
  });
});