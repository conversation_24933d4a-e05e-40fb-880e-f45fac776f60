const { request } = require('@playwright/test');
const fs = require('fs');
const path = require('path');
const tokenList = require('./apiListStock.js');
// Load config
const config = require('./configRedis.json');
const redisConfig = config.redis_search;

// Lấy tokenList từ kết quả của file apiListStock.js
// Giả sử file apiListStock.js đã xuất ra một mảng các token (ví dụ: module.exports = stockList;

async function main() {
  // tokenList được import từ ./apiListStock.js, giả sử là mảng các address stock
  if (!Array.isArray(tokenList) || tokenList.length === 0) {
    console.error('tokenList is empty or not an array.');
    process.exitCode = 1;
    return;
  }

  const apiUrl = 'https://api.dex3.ai/dev/redisearch/token';
  const headers = {
    'x-api-key': redisConfig['x-api-key'],
    'Content-Type': 'application/json',
  };
  const reqContext = await request.newContext();

  let data = [];
  try {
    // API này nhận POST, không phải GET
    const response = await reqContext.post(apiUrl, {
      headers,
      data: { tokens: tokenList }
    });

    if (!response.ok()) {
      console.error('API request failed:', response.status());
      console.error('Response:', await response.text());
      process.exitCode = 1;
      return;
    }

    data = await response.json();

    // Đảm bảo data là mảng
    const stockList = Array.isArray(data) ? data : [data];

    // Tổng marketCap top stock
    const stockMarketCap = stockList.reduce((sum, item) => {
      if (item && !isNaN(Number(item.marketCap))) {
        return sum + Number(item.marketCap);
      }
      return sum;
    }, 0);

    // Tổng volume top stock
    const stockVolume = stockList.reduce((sum, item) => {
      if (item && !isNaN(Number(item.volume24h))) {
        return sum + Number(item.volume24h);
      }
      return sum;
    }, 0);

    // Màn hình stock
    console.log('Tổng stock:', stockList.length);
    console.log('Tổng marketCap stock:', stockMarketCap);
    console.log('Tổng volume stock:', stockVolume);

    // In ra data chi tiết nếu cần
    // console.log(data);

  } catch (error) {
    console.error('Error during API requests:', error.message || error);
    process.exitCode = 1;
  } finally {
    await reqContext.dispose();
  }
}




main();