import{h as m}from"./chunk-DERIAD33.js";import{j as l}from"./chunk-OKP6DFCI.js";import{o,rb as e}from"./chunk-WIQ4WVKX.js";import{m as a}from"./chunk-56SJOU6P.js";import{a as T}from"./chunk-7X4NV6OJ.js";import{f as b,h as r,n as p}from"./chunk-3KENBVE7.js";r();p();var t=b(T());var v=o.div`
  position: relative;
  width: 100%;
`,y=o.div`
  position: absolute;
  right: 12px;
  top: calc(50% - 27px / 2);
  display: flex;
  align-items: center;
`,B=o(e)`
  margin-right: ${n=>`calc(120px - (${n.textLength}px * 5))`};
`,S=o(e)`
  margin-right: 10px;
`,w=o(l)`
  height: 27px;
`,C=o.div`
  position: relative;
  width: 100%;
  padding: 0px 8px;
  overflow: hidden;
  text-overflow: ellipsis;
`,N=({symbol:n,alignSymbol:g,buttonText:s,width:d,borderRadius:u,onSetTarget:x,targetButtonDisabled:h,...i})=>{let{t:c}=a(),f=i.value.toString().length;return t.default.createElement(v,null,t.default.createElement(m,{placeholder:c("maxInputAmount"),borderRadius:u,...i}),t.default.createElement(y,null,g==="left"?t.default.createElement(B,{size:16,textLength:f,color:"#777777"},n):t.default.createElement(S,{size:16,color:"#777777"},n),t.default.createElement(w,{disabled:h,fontSize:13,width:`${d}px`,borderRadius:"100px",paddingY:4,onClick:x},t.default.createElement(C,null,s))))};export{N as a};
//# sourceMappingURL=chunk-PUJH7423.js.map
