import { test, expect } from '../base/TestBase';
import { ENV } from '../base/env';

test.describe('Search tests', () => {
  // Sử dụng fixture searchPage từ TestBase

  test('Kiểm tra popup tìm kiếm và các phần Trending Tokens, Top Wallets', async ({ page, searchPage }) => {
    try {
      console.log('1. Điều hướng đến trang chủ Dex3');
      await searchPage.navigateToDex3();

      console.log('2. Mở popup tìm kiếm');
      await searchPage.openSearchPopup();

      console.log('3. Kiểm tra popup hiển thị');
      const isPopupVisible = await searchPage.isSearchPopupVisible();
      expect(isPopupVisible).toBeTruthy();

      console.log('4. Kiểm tra phần Trending Tokens');
      const hasTrendingTokens = await searchPage.hasTrendingTokensSection();
      expect(hasTrendingTokens).toBeTruthy();

      console.log('5. Kiểm tra phần Top Wallets');
      const hasTopWallets = await searchPage.hasTopWalletsSection();
      expect(hasTopWallets).toBeTruthy();

      // 6. Chụp ảnh popup tìm kiếm
      await page.screenshot({ path: 'screenshots/search-popup.png' });

      // 7. Đóng popup tìm kiếm
      console.log('7. Đóng popup tìm kiếm');
      await searchPage.closeSearchPopup();

      console.log('\n==== KẾT THÚC TEST KIỂM TRA POPUP TÌM KIẾM ====\n');
    } catch (error) {
      console.error('\n!!! TEST KIỂM TRA POPUP TÌM KIẾM THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');

      // Chụp ảnh khi gặp lỗi
      await page.screenshot({ path: 'screenshots/search-popup-error.png' });
      throw error;
    }
  });

  test('Tìm kiếm token với từ khóa ', async ({ page, searchPage }) => {
    try {
      console.log('1. Điều hướng đến trang chủ Dex3');
      await searchPage.navigateToDex3();

      console.log('2. Mở popup tìm kiếm');
      await searchPage.openSearchPopup();

      console.log('3. Kiểm tra popup hiển thị');
      const isPopupVisible = await searchPage.isSearchPopupVisible();
      expect(isPopupVisible).toBeTruthy();

      console.log('4. Tìm kiếm với từ khóa');
      await searchPage.search('TRUMP');

      console.log('6. Kiểm tra kết quả tìm kiếm');
      const resultsCount = await searchPage.getSearchResultsCount();
      console.log(`Số lượng kết quả tìm kiếm: ${resultsCount}`);

      // Nên có ít nhất một kết quả khi tìm SOL
      expect(resultsCount).toBeGreaterThan(0);

      // 7. Chụp ảnh kết quả tìm kiếm
      await page.screenshot({ path: 'screenshots/search-results.png' });

      // 8. Click vào token đầu tiên trong kết quả tìm kiếm
      console.log('8. Click vào token đầu tiên trong kết quả tìm kiếm');
      await searchPage.clickFirstSearchResult();

      // 9. Đợi trang token detail tải xong
      console.log('9. Đợi trang token detail tải xong');
      await page.waitForLoadState('networkidle');

      // 10. Chụp ảnh trang token detail
      console.log('10. Chụp ảnh trang token detail');
      await page.screenshot({ path: 'screenshots/token-detail.png' });

      // 11. Kiểm tra URL có chứa "/solana/" (phần của token detail)
      const currentUrl = page.url();
      console.log(`URL hiện tại: ${currentUrl}`);
      expect(currentUrl).toContain('/solana/');
      console.log('URL chứa "/solana/", đã chuyển hướng đến trang token detail thành công');

      console.log('\n==== KẾT THÚC TEST TÌM KIẾM VỚI TỪ KHÓA ====\n');
    } catch (error) {
      console.error('\n!!! TEST TÌM KIẾM VỚI TỪ KHÓA THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');

      // Chụp ảnh khi gặp lỗi
      await page.screenshot({ path: 'screenshots/search-keyword-error.png' });
      throw error;
    }
  });

  test('Tìm kiếm với từ khóa không có kết quả', async ({ page, searchPage }) => {
    try {
      console.log('\n==== BẮT ĐẦU TEST TÌM KIẾM KHÔNG CÓ KẾT QUẢ ====\n');

      // Từ khóa ngẫu nhiên không có kết quả
      const randomKeyword = 'xyzabcdefghijklmnopqrstuvwxyz123456789';

      // 1. Điều hướng đến trang chủ Dex3
      console.log('1. Điều hướng đến trang chủ Dex3');
      await searchPage.navigateToDex3();

      // 2. Mở popup tìm kiếm
      console.log('2. Mở popup tìm kiếm');
      await searchPage.openSearchPopup();

      // 3. Kiểm tra popup hiển thị
      console.log('3. Kiểm tra popup hiển thị');
      const isPopupVisible = await searchPage.isSearchPopupVisible();
      expect(isPopupVisible).toBeTruthy();

      // 4. Tìm kiếm với từ khóa ngẫu nhiên
      console.log(`4. Tìm kiếm với từ khóa không có kết quả: "${randomKeyword}"`);
      await searchPage.search(randomKeyword);
      
      // 6. Kiểm tra thông báo không tìm thấy kết quả
      console.log('6. Kiểm tra thông báo không tìm thấy kết quả');
      const hasNoResults = await searchPage.hasNoResultsMessage();
      expect(hasNoResults).toBeTruthy();

      // 7. Chụp ảnh kết quả tìm kiếm
      await page.screenshot({ path: 'screenshots/search-no-results.png' });

      // 8. Đóng popup tìm kiếm
      console.log('8. Đóng popup tìm kiếm');
      await searchPage.closeSearchPopup();

      console.log('\n==== KẾT THÚC TEST TÌM KIẾM KHÔNG CÓ KẾT QUẢ ====\n');
    } catch (error) {
      console.error('\n!!! TEST TÌM KIẾM KHÔNG CÓ KẾT QUẢ THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');

      // Chụp ảnh khi gặp lỗi
      await page.screenshot({ path: 'screenshots/search-no-results-error.png' });
      throw error;
    }
  });
});
