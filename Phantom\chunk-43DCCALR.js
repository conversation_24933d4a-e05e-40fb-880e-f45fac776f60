import{k as n,o as m,rb as d}from"./chunk-WIQ4WVKX.js";import{r as p}from"./chunk-7ZN4F6J4.js";import{qe as l}from"./chunk-MZZEJ42N.js";import{S as s}from"./chunk-ALUTR72U.js";import{a as h}from"./chunk-7X4NV6OJ.js";import{f as T,h as a,n as c}from"./chunk-3KENBVE7.js";a();c();var t=T(h());var v=o=>{let{txHash:e}=o,{data:i}=l("solana"),f=e&&i?{id:e,networkID:i}:void 0,{data:r}=p(f),x=(0,t.useCallback)(()=>{r&&self.open(r)},[r]);return t.default.createElement(w,{opacity:e?1:0,onClick:x},o.children)},w=m(d).attrs({size:16,weight:500,color:"#AB9FF2"})`
  margin-top: 18px;
  text-decoration: none;
  ${o=>o.opacity===0?n`
          pointer-events: none;
        `:n`
          &:hover {
            cursor: pointer;
            color: ${s("#e2dffe",.5)};
          }
        `}
  }
`;export{v as a};
//# sourceMappingURL=chunk-43DCCALR.js.map
