import{B as f,Ma as s}from"./chunk-JD6NH5K6.js";import{b as u}from"./chunk-W27Z2YZM.js";import{k as F}from"./chunk-OKP6DFCI.js";import{ca as y}from"./chunk-SD2LXVLD.js";import{c as i}from"./chunk-MHOQBMVI.js";import{m}from"./chunk-56SJOU6P.js";import{Ya as d}from"./chunk-L3A2KHJO.js";import{a as T}from"./chunk-7X4NV6OJ.js";import{f as B,h as c,n as p}from"./chunk-3KENBVE7.js";c();p();var r=B(T());var j=()=>{let{data:[t]}=d(["enable-force-upgrade"]);return t?r.default.createElement(x,null):null},x=()=>{let{handleShowModalVisibility:t}=s(),{data:e,isSuccess:a}=y(),o=a&&e?.forceUpgradeStatus==="upgrade";return(0,r.useEffect)(()=>{o&&t("forceUpgrade",e)},[e,t,o]),null},P=({interstitial:t,minVersionRequired:e})=>{let{t:a}=m(),{handleHideModalVisibility:o}=s(),{imageUrl:g,title:U,description:h,primaryButtonText:S,primaryButtonDestination:n}=t||{},C=(0,r.useCallback)(()=>{u({url:n}),i.capture("forceUpgradeInterstitialPrimaryButtonClickedByUser",{data:{minVersionRequired:e}})},[e,n]),l=(0,r.useCallback)(()=>{o("forceUpgrade"),i.capture("forceUpgradeInterstitialDismissedByUser",{data:{minVersionRequired:e}})},[o,e]),b={headerTitle:a("exploreDropsCarouselTitle"),alignBody:"center",icon:g,bodyTitle:U,bodyDescription:h,FooterComponent:()=>r.default.createElement(F,{primaryText:S,onPrimaryClicked:C,secondaryText:a("commandDismiss"),onSecondaryClicked:l}),onDismiss:l};return r.default.createElement(f,{...b})},z=P;export{j as a,P as b,z as c};
//# sourceMappingURL=chunk-ANFAK3NI.js.map
