import { chromium, FullConfig } from '@playwright/test';
import { GlobalSetupHelper } from './GlobalSetupHelper';
import { ENV } from './tests/base/env';
import * as fs from 'fs';
import * as path from 'path';

/**
 * Global setup để thiết lập môi trường chạy test
 * - Import và đăng nhập ví Phantom
 * - Đăng nhập vào Dex3
 * - Lưu trạng thái đăng nhập để tái sử dụng
 */
async function globalSetup(config: FullConfig): Promise<void> {
  console.log('=== Bắt đầu global setup ===');

  // Kiểm tra nếu đã có trạng thái đăng nhập và trạng thái đó còn hợp lệ
  if (await isValidLoginState()) {
    console.log('Trạng thái đăng nhập đã tồn tại và còn hợp lệ. Bỏ qua bước setup.');
    return;
  }

  console.log('Trạng thái đăng nhập không tồn tại hoặc đã hết hạn. Tiến hành setup mới...');

  const userDataDir = path.join(__dirname, 'user-data-dir');
  ensureDirectoryExists(userDataDir);

  // Khởi tạo browser trong persistent context để lưu trạng thái extension
  const browser = await chromium.launchPersistentContext(userDataDir, {
    headless: false,
    args: [
      `--disable-extensions-except=${ENV.PHANTOM_EXTENSION_PATH}`,
      `--load-extension=${ENV.PHANTOM_EXTENSION_PATH}`,
    ],
  });

  console.log('Browser đã được khởi tạo thành công.');

  try {
    // Đợi để extension load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Lấy tất cả trang đang mở
    let pages = browser.pages();
    console.log(`Hiện có ${pages.length} trang đang mở.`);

    let mainPage = pages.length > 0 ? pages[0] : await browser.newPage();

    // 1. Import Phantom Wallet
    console.log('Bắt đầu import ví Phantom...');
    // Chuyển tới trang phantom extension
    const page = await switchToPhantomPage(browser, mainPage);
    const setupHelper = new GlobalSetupHelper(page);
    await setupHelper.importWallet();
    console.log('Import ví Phantom thành công.');

    // 2. Đăng nhập vào Dex3
    console.log('Bắt đầu đăng nhập vào Dex3...');
    // Mở trang Dex3 trong tab mới
    mainPage = await browser.newPage();
    await mainPage.goto(ENV.BASE_URL);

    // Đợi trang load
    await mainPage.waitForLoadState('domcontentloaded');
    await mainPage.waitForTimeout(3000);

    // Thực hiện đăng nhập
    const dex3SetupHelper = new GlobalSetupHelper(mainPage);
    await dex3SetupHelper.loginDex3();

    // Xác minh đăng nhập thành công
    await dex3SetupHelper.verifyLoginState();
    console.log('Đăng nhập vào Dex3 thành công.');

    // Lưu thông tin về trạng thái đăng nhập hiện tại
    saveLoginState();

    // 3. Lưu trạng thái storageState cho trang web (không bao gồm extension)
    await mainPage.context().storageState({ path: path.join(__dirname, 'storageState.json') });
    console.log('Đã lưu trạng thái storage cho trang web.');

  } catch (error) {
    console.error('Lỗi trong global setup:', error);
    throw error;
  } finally {
    // Đóng browser
    await browser.close();
    console.log('=== Kết thúc global setup ===');
  }
}

/**
 * Chuyển đến trang Phantom Extension
 */
async function switchToPhantomPage(browser: any, currentPage: any): Promise<any> {
  console.log('Đang chuyển đến trang Phantom Extension...');

  // Kiểm tra các trang hiện có để tìm trang Phantom
  const pages = browser.pages();
  const phantomPage = pages.find((page: any) =>
    page.url().startsWith('chrome-extension://') && page.url().includes('/onboarding')
  );

  if (phantomPage) {
    console.log('Đã tìm thấy trang Phantom:', phantomPage.url());
    await phantomPage.bringToFront();
    return phantomPage;
  }

  // Nếu không tìm thấy, mở trang Phantom Extension trực tiếp
  console.log('Không tìm thấy trang Phantom, mở trực tiếp...');

  try {
    // Không thể sử dụng đường dẫn trực tiếp, cần tìm ID của extension
    console.log('Tìm ID của extension Phantom...');

    // Đợi một chút để extension load
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Mở trang mới và kiểm tra các extension đã cài đặt
    const extensionsPage = await browser.newPage();
    await extensionsPage.goto('chrome://extensions/');
    console.log('Mở trang extensions để kiểm tra các extension đã cài đặt');

    // Đợi trang load
    await extensionsPage.waitForLoadState('domcontentloaded');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Chụp màn hình trang extensions để debug
    await extensionsPage.screenshot({ path: 'screenshots/extensions-page.png' });

    // Đóng trang extensions
    await extensionsPage.close();

    // Thử tìm bất kỳ trang extension nào đã mở
    const anyExtensionPage = pages.find((page: any) => page.url().startsWith('chrome-extension://'));
    if (anyExtensionPage) {
      const url = anyExtensionPage.url();
      const matches = url.match(/chrome-extension:\/\/([^/]+)/);
      if (matches && matches[1]) {
        console.log('Tìm thấy extension ID từ trang đã mở:', matches[1]);
        const newPhantomPage = await browser.newPage();
        await newPhantomPage.goto(`chrome-extension://${matches[1]}/onboarding.html`);
        return newPhantomPage;
      }
    }

    // Nếu không tìm thấy, thử mở trang mới và điều hướng đến trang chính của Dex3
    console.log('Không tìm thấy extension ID, mở trang Dex3 để thử kích hoạt extension...');
    const newPage = await browser.newPage();
    await newPage.goto(ENV.BASE_URL);
    await newPage.waitForLoadState('domcontentloaded');
    await new Promise(resolve => setTimeout(resolve, 3000));

    // Chụp màn hình trang Dex3
    await newPage.screenshot({ path: 'screenshots/dex3-page.png' });

    // Kiểm tra lại các trang đã mở
    const updatedPages = browser.pages();
    const phantomPageAfterDex3 = updatedPages.find((page: any) =>
      page.url().startsWith('chrome-extension://') && page.url().includes('/onboarding')
    );

    if (phantomPageAfterDex3) {
      console.log('Tìm thấy trang Phantom sau khi mở Dex3:', phantomPageAfterDex3.url());
      await phantomPageAfterDex3.bringToFront();
      return phantomPageAfterDex3;
    }

    // Nếu vẫn không tìm thấy, trả về trang Dex3
    console.log('Không tìm thấy trang Phantom, sử dụng trang Dex3...');
    return newPage;
  } catch (error) {
    console.error('Lỗi khi mở trang Phantom:', error);
  }

  // Nếu vẫn không tìm thấy, trả về trang hiện tại
  console.log('Không thể mở trang Phantom, sử dụng trang hiện tại...');
  return currentPage;
}

/**
 * Lưu thông tin về trạng thái đăng nhập hiện tại
 */
function saveLoginState() {
  const loginInfo = {
    timestamp: Date.now(),
    expiresIn: 24 * 60 * 60 * 1000, // Hết hạn sau 24 giờ
  };

  fs.writeFileSync(
    path.join(__dirname, 'login-state.json'),
    JSON.stringify(loginInfo, null, 2)
  );

  console.log('Đã lưu thông tin trạng thái đăng nhập.');
}

/**
 * Kiểm tra xem trạng thái đăng nhập hiện tại có hợp lệ không
 */
async function isValidLoginState(): Promise<boolean> {
  const loginStatePath = path.join(__dirname, 'login-state.json');
  const storageStatePath = path.join(__dirname, 'storageState.json');

  // Kiểm tra nếu cả hai file đều tồn tại
  if (!fs.existsSync(loginStatePath) || !fs.existsSync(storageStatePath)) {
    return false;
  }

  try {
    // Đọc thông tin trạng thái đăng nhập
    const loginInfo = JSON.parse(fs.readFileSync(loginStatePath, 'utf8'));

    // Kiểm tra tính hợp lệ của thời gian
    const currentTime = Date.now();
    const expirationTime = loginInfo.timestamp + loginInfo.expiresIn;

    return currentTime < expirationTime;
  } catch (error) {
    console.error('Lỗi khi kiểm tra trạng thái đăng nhập:', error);
    return false;
  }
}

/**
 * Đảm bảo thư mục tồn tại
 */
function ensureDirectoryExists(dirPath: string) {
  if (!fs.existsSync(dirPath)) {
    fs.mkdirSync(dirPath, { recursive: true });
  }
}

export default globalSetup;
