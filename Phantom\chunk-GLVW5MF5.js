import{b as t1,c as Rt,d as Gi,e as Qe,f as no,g as o1,h as r1,i as n1,j as Fe}from"./chunk-ELBGM5PY.js";import{a as a3}from"./chunk-UM364UVK.js";import{G as $u,a as Uu,b as Vu,c as Hu,d as _u,e as Gu,f as Ru,g as zu}from"./chunk-SYICDMYM.js";import{a as Lu,b as Ou,c as <PERSON>}from"./chunk-HKFEOKHC.js";import{a as Hi}from"./chunk-ZNZZRKNQ.js";import{a as ja}from"./chunk-2WECCVZD.js";import{a as Nu,b as xo,c as Bu}from"./chunk-O5AAGNHJ.js";import{a as Pu}from"./chunk-MXORZ3WH.js";import{d as ie,f as mm,r as um,t as Ea,v as hm,x as km}from"./chunk-5MF3BU53.js";import{a as qu,b as _i,c as ju,d as <PERSON>,e as <PERSON>,f as Ju}from"./chunk-WE6RAXEH.js";import{a as e1}from"./chunk-VHCQKD7Y.js";import{b as Qu,d as Zu}from"./chunk-RLZITNCL.js";import{a as ou}from"./chunk-T27XGMXK.js";import{a as Mu,b as As,c as Fu}from"./chunk-Q3HNV7GN.js";import{a as Vi}from"./chunk-XJWRT6N6.js";import{A as ro,Aa as vu,Ba as Cu,C as au,D as Ha,E as yt,Ea as Tu,Fa as Du,Ga as Eu,Ha as qa,I as cu,J as lu,K as _l,L as mu,M as uu,Ma as Er,N as fu,Na as Rl,P as Oi,R as Ir,S as Vn,W as wu,b as Am,f as wm,h as bm,i as vm,j as Cm,k as Tm,l as Im,r as Jm,s as Mt,t as Xm,ta as Ss,u as Eo,ua as $e,v as Qm,va as Dr,w as We,wa as bu,x as Zm,xa as Ui,y as Ft,ya as za,za as $a}from"./chunk-JD6NH5K6.js";import{n as Wi}from"./chunk-KJMFZ7XX.js";import{d as gu,e as xu}from"./chunk-CCUXU2GU.js";import{a as Cr}from"./chunk-QEXGR5WT.js";import{a as eu,b as tu}from"./chunk-S24UABH5.js";import{a as xt}from"./chunk-X3ESGVCB.js";import{b as go,c as Gl}from"./chunk-SHAEZV7V.js";import{a as po,b as K,c as _a,d as zo,e as uo,f as hu,g as Tr,h as Ga,i as fo,j as Ra,k as ku,l as Su,m as Au}from"./chunk-IWGMKDQE.js";import{b as mo,d as Li,g as pu}from"./chunk-DERIAD33.js";import{a as du}from"./chunk-EGXLQXDH.js";import{a as Nt}from"./chunk-CCQRCL2K.js";import{g as Bi,h as U,i as su,j as Gt,k as Va,l as vr,n as O}from"./chunk-75L54KUM.js";import{a as yu}from"./chunk-ROF5SDVA.js";import{a as rt}from"./chunk-IVMV7P4T.js";import{a as Da}from"./chunk-YF76YZSL.js";import{a as Xu,b as Ka,c as a1}from"./chunk-JLLUQF3V.js";import{b as i1}from"./chunk-QSVSNR6K.js";import{d as Dm,e as Ma,f as Em,g as _t,h as Pm,l as Iu,m as er}from"./chunk-SMVAXKUF.js";import{z as nu}from"./chunk-HPOS2V3B.js";import{a as ru,b as Ni}from"./chunk-XYJX6G2K.js";import{b as Ar}from"./chunk-W27Z2YZM.js";import{J as wr,M as ks,N as gm,U as z,V as xm,a as _e,b as Ht,c as J,d as M,e as R,f as oo,n as he,p as Ii,q as kn,r as Zo,t as Ul,v as fm}from"./chunk-2NGYUYTC.js";import{a as Ro}from"./chunk-4VDZJDFB.js";import{a as s1}from"./chunk-26OZJBRY.js";import{c as Hl}from"./chunk-GMBAJ6CC.js";import{a as Ym}from"./chunk-PTZMRZUV.js";import{a as iu}from"./chunk-VQVTLSDS.js";import{a as Et,b as Sn,h as Pt,j as oe,k as gt,l as An}from"./chunk-OKP6DFCI.js";import{$ as _m,A as Lm,G as Om,Ga as Fi,Ha as Wa,I as Wm,K as Ba,Ka as Km,L as Um,O as br,U as Ei,W as Vm,_ as Hm,aa as Gm,ba as Rm,ca as zm,d as Sm,da as La,ea as Pi,fa as Mi,ga as $m,ha as qm,ja as jm,l as Vl,lb as Ua,o as f,qa as Oa,r as Mm,rb as E,s as Fm,t as Nm,w as Fa,y as Bm,z as Na}from"./chunk-WIQ4WVKX.js";import{a as o3,c as pm}from"./chunk-AVT3M45V.js";import{B as am,D as cm,e as Il,ga as Ia,i as xn,ib as lm,j as va,jb as de,kb as dm,m as Ca,n as rm,ra as Ci,sa as Ti,ta as hn}from"./chunk-SD2LXVLD.js";import{b as Si,c as Ai,d as ap,g as cp,h as lp,j as jr,n as dp}from"./chunk-LURFXJDV.js";import{n as Ol}from"./chunk-V5T43K7V.js";import{b as Di,c as Pa,e as ym}from"./chunk-MNXYIK2W.js";import{a as Yr,c as W}from"./chunk-MHOQBMVI.js";import{a as nm}from"./chunk-GQEPK4C4.js";import{a as A0}from"./chunk-BTKBODVJ.js";import{B as Yp,C as Jp,D as Xp,H as Qp,I as Zp,a as ha,c as wp,d as bp,g as vp,h as Cp,i as Tp,n as Ip,s as ka,t as Dp,x as Ep,y as Pp,z as Sr}from"./chunk-7ZN4F6J4.js";import{Bb as vi,Cb as im,Ea as om,H as em,Ib as Ta,J as Dl,Jb as sm,Lb as Nl,Nb as Bl,U as Kr,V as El,Y as tm,hc as Ll,mb as Pl,pb as Ml,pc as Wl,vb as Fl,yb as yn}from"./chunk-OUYKWOVO.js";import{a as i3}from"./chunk-WFPABEAU.js";import{a as Sp,b as Ap}from"./chunk-THLBAMDB.js";import{a as s3}from"./chunk-LDMZMUWY.js";import{a as t3}from"./chunk-OXFZHPMY.js";import{k as ft}from"./chunk-OYGO47TI.js";import{A as hp,C as vl,F as ya,G as kp,a as pp,b as wi,g as mp,j as up,n as fp,o as ga,q as gp,s as xp,t as xa,v as yp,y as bi}from"./chunk-SLQBAOEK.js";import{$d as ue,Ab as H0,Ad as _o,Bd as yi,C as ta,Cc as ca,Cd as hi,Dc as la,Dd as ki,Ec as X0,Ed as ma,Fa as r3,Fc as xi,Fd as ua,Gc as Q0,Hc as Z0,Hd as fa,Ic as qr,Id as Mp,J as m0,Jc as un,Jd as Sa,Kc as wl,Kd as te,L as ul,Lb as _0,Lc as ep,Ld as kr,Ma as kl,Md as Fp,Nc as hr,O as u0,Ob as G0,Od as Np,P as $r,Pa as j,Pb as be,Pd as Bp,Q as S0,Qb as R0,Qd as Lp,Ra as Ne,Rc as bl,Rd as Do,Sc as tp,Sd as Op,Td as Wp,Uc as n3,Ud as Up,V as ys,Vd as Aa,Wd as Vp,Yd as Hp,Zd as _p,_d as Go,bb as na,be as Gp,ca as Un,ce as Rp,db as ia,de as wa,ee as zp,fc as z0,gc as $0,ge as $p,ib as sa,ie as Cl,je as qp,kc as q0,ke as ba,le as to,me as jp,ne as fn,oc as aa,od as op,pe as Tl,rc as j0,rd as rp,sc as K0,sd as da,td as np,te as gn,ud as ip,ue as Vt,vb as U0,vc as Y0,wb as Sl,wc as hs,xa as O0,xb as mn,xd as pa,xe as Kp,ya as W0,yb as V0,yc as J0,yd as sp,zb as Al}from"./chunk-MZZEJ42N.js";import{J as Oe,w as Ho,x as ut}from"./chunk-E3NPIRHS.js";import{a as yr,b as ra,m as S}from"./chunk-56SJOU6P.js";import{B as xs,E as P0,H as M0,K as gl,M as F0,N as N0,O as B0,S as dn,V as pn,a as fi,ja as xl,ka as yl,la as L0,ma as hl,o as E0,v as ee}from"./chunk-ALUTR72U.js";import{A as gi,B as f0,C as g0,E as x0,I as y0,L as h0,M as k0,P as ln,Va as C0,Wa as T0,Ya as He,Za as I0,ab as D0,ha as w0,ka as xr,la as b0,q as ea,qa as v0,s as gr,t as fl,ta as $,v as cn,z as oa}from"./chunk-L3A2KHJO.js";import{a as C}from"./chunk-7X4NV6OJ.js";import{f as A,h as d,m as Buffer,n as p}from"./chunk-3KENBVE7.js";d();p();var q=A(C());d();p();var c1=f.div`
  background-color: #222222;
  min-width: ${Di}px;
  min-height: ${Pa}px;
  height: 100vh;
  width: 100vw;
  ${e=>e.withBorder&&!k0?"border: 1px solid #323232;":""}
  display: flex;
  flex-direction: column;
  position: relative;
  overflow: hidden;
  ${h0?"border-radius: 8px;":""}
`;d();p();var Ko=A(C());d();p();var ne=A(C());var l1={v:"4.8.0",meta:{g:"LottieFiles AE 3.3.6",a:"",k:"",d:"",tc:""},fr:15,ip:0,op:170,w:1e3,h:1e3,nm:"lock-seq-01",ddd:1,assets:[{id:"comp_0",layers:[{ddd:0,ind:1,ty:3,nm:"MASTER 3",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:1,k:[{i:{x:[.838],y:[.308]},o:{x:[.333],y:[0]},t:8,s:[0]},{i:{x:[.228],y:[.771]},o:{x:[.31],y:[.594]},t:12,s:[-51.6]},{t:20,s:[-380.6]}],ix:10},p:{a:0,k:[500,362,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:20,st:0,bm:0},{ddd:0,ind:2,ty:3,nm:"MASTER 2",parent:1,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:1,k:[{i:{x:[.838],y:[.411]},o:{x:[.333],y:[0]},t:8,s:[0]},{t:12,s:[-270.8]}],ix:10},p:{a:0,k:[50,188,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:20,st:0,bm:0},{ddd:0,ind:3,ty:3,nm:"MASTER 1",parent:2,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:51.923,ix:3},y:{a:0,k:40.385,ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,ip:0,op:20,st:0,bm:0},{ddd:0,ind:4,ty:4,nm:"trail-center-2",parent:9,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:-252.044,ix:10},p:{s:!0,x:{a:0,k:3.16,ix:3},y:{a:0,k:-119.549,ix:4}},a:{a:0,k:[216,-182,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[340,340],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:1,k:[{i:{x:[.833],y:[.862]},o:{x:[.41],y:[.23]},t:13,s:[59]},{i:{x:[.969],y:[.281]},o:{x:[.167],y:[.231]},t:17,s:[20.351]},{t:20,s:[3]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[136]},{t:20,s:[-175]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,.992126464844,.972534179688,1],ix:3},o:{a:0,k:100,ix:4},w:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[40.8]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:14,s:[56.6]},{t:20,s:[41.5]}],ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[224.5,-182],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:14,op:20,st:-8,bm:0},{ddd:0,ind:5,ty:4,nm:"trail-center-1",parent:9,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:-53.044,ix:10},p:{s:!0,x:{a:0,k:109.814,ix:3},y:{a:0,k:-340.912,ix:4}},a:{a:0,k:[216,-182,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[340,340],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[31]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:14,s:[22]},{t:16,s:[3]}],ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[136]},{t:20,s:[-66]}],ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,.992126464844,.972534179688,1],ix:3},o:{a:0,k:100,ix:4},w:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[40.8]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:14,s:[119.8]},{t:20,s:[41.5]}],ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[224.5,-182],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:13,op:19,st:-8,bm:0},{ddd:0,ind:6,ty:4,nm:"trail-1",parent:9,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:-53.044,ix:10},p:{s:!0,x:{a:0,k:-.883,ix:3},y:{a:0,k:-125.596,ix:4}},a:{a:0,k:[216,-182,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[340,340],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[13]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:14,s:[22]},{t:16,s:[3]}],ix:2},o:{a:0,k:0,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,.992126464844,.972534179688,1],ix:3},o:{a:0,k:100,ix:4},w:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:13,s:[203.8]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:14,s:[119.8]},{t:16,s:[68.5]}],ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[224.5,-182],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:3,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:13,op:17,st:-8,bm:0},{ddd:0,ind:7,ty:4,nm:"main shape 3",parent:3,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.066,.066],y:[1,1]},o:{x:[.31,.31],y:[.668,.668]},t:12,s:[283,283]},{t:20,s:[56,56]}],ix:2},p:{a:0,k:[-130.6,-235.3],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 2",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-128.6,-260.3],ix:2},a:{a:0,k:[-128.6,-260.3],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:8,s:[0],h:1},{t:12,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"circle",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.333,y:0},t:-7.5,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:-6,s:[{i:[[17.24,-.077],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[-.62,42.719]],o:[[-20.297,.091],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[.108,-7.469]],v:[[-50.814,-319.717],[-102.975,-293.657],[-99.574,-302.684],[-112.356,-316.561],[-148.367,-293.657],[-146.79,-298.417],[-156.507,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-35.359,-298.356]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:-3.5,s:[{i:[[9.502,-1.336],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.03,2.817],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:0,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.833,y:.833},o:{x:.333,y:0},t:8,s:[{i:[[5.864,7.595],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[-14.24,40.79]],o:[[-12.667,-16.408],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[5.656,-16.2]],v:[[-38.577,-321.197],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-38.913,-289.018]],c:!0}]},{t:12,s:[{i:[[8.311,16.93],[9.103,-13.949],[.474,2.11],[9.226,.026],[7.219,-2.93],[4.548,-3.094],[5.656,-7.73],[.501,-9.792],[-31.485,.645],[-6.8,12.305]],o:[[-4.199,-8.554],[1.107,-3.086],[-1.347,-5.931],[-10.46,-.03],[-4.609,1.871],[-3.84,2.612],[-4.479,6.122],[-1.395,27.241],[34.82,-.713],[8.215,-14.866]],v:[[-79.058,-297.807],[-102.975,-293.657],[-103.687,-304.084],[-121.892,-318.257],[-144.24,-313.362],[-159.178,-304.49],[-177.357,-285.704],[-189.096,-254],[-143.74,-202.714],[-80.68,-241.25]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:8,s:[100],h:1},{t:12,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"ghost",np:2,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:11,op:17,st:-1,bm:0},{ddd:0,ind:8,ty:4,nm:"eyes mask 2",parent:3,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:0,k:154.191,ix:3},y:{a:1,k:[{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:-6,s:[-29.508]},{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:-4,s:[-44.85]},{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:0,s:[-29.508]},{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:2,s:[-44.85]},{i:{x:[.667],y:[1]},o:{x:[.5],y:[0]},t:5,s:[-29.508]},{i:{x:[.843],y:[.759]},o:{x:[.333],y:[0]},t:8,s:[-44.85]},{i:{x:[.667],y:[1]},o:{x:[.371],y:[.439]},t:10,s:[-29.508]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:12,s:[-9.507]},{t:14,s:[-29.508]}],ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:-7,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-6,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-4,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:13,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:14,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:16,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:19,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:21,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:-7,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-6,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-4,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:13,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:14,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:16,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:19,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:21,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"st",c:{a:0,k:[0,0,0,0],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:2,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:46,ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:-7,s:[26]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:-5,s:[-70.05]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:-2,s:[23.375]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:9,s:[12]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:12,s:[12]},{t:15,s:[60]}],ix:3},m:1,ix:4,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:-5,s:[0],h:1},{t:-4,s:[100],h:1},{t:15,s:[100],h:1},{t:16,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes closed",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:-7,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-6,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-4,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:13,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:14,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:16,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:19,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:21,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:-7,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-6,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:-4,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:13,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:14,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:16,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:19,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:21,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:-5,s:[100],h:1},{t:-4,s:[0],h:1},{t:15,s:[0],h:1},{t:16,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes open",np:2,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:8,op:11,st:20.5,bm:0},{ddd:0,ind:9,ty:4,nm:"main shape 2",parent:3,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:1,k:[{i:{x:[.066,.066],y:[1,1]},o:{x:[.31,.31],y:[.668,.668]},t:12,s:[283,283]},{t:20,s:[56,56]}],ix:2},p:{a:0,k:[-130.6,-235.3],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 2",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-128.6,-260.3],ix:2},a:{a:0,k:[-128.6,-260.3],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:8,s:[0],h:1},{t:12,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"circle",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.333,y:0},t:-7.5,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:-6,s:[{i:[[17.24,-.077],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[-.62,42.719]],o:[[-20.297,.091],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[.108,-7.469]],v:[[-50.814,-319.717],[-102.975,-293.657],[-99.574,-302.684],[-112.356,-316.561],[-148.367,-293.657],[-146.79,-298.417],[-156.507,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-35.359,-298.356]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:-3.5,s:[{i:[[9.502,-1.336],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.03,2.817],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:0,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.833,y:.833},o:{x:.333,y:0},t:8,s:[{i:[[5.864,7.595],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[-14.24,40.79]],o:[[-12.667,-16.408],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[5.656,-16.2]],v:[[-38.577,-321.197],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-38.913,-289.018]],c:!0}]},{t:12,s:[{i:[[8.311,16.93],[9.103,-13.949],[.474,2.11],[9.226,.026],[7.219,-2.93],[4.548,-3.094],[5.656,-7.73],[.501,-9.792],[-31.485,.645],[-6.8,12.305]],o:[[-4.199,-8.554],[1.107,-3.086],[-1.347,-5.931],[-10.46,-.03],[-4.609,1.871],[-3.84,2.612],[-4.479,6.122],[-1.395,27.241],[34.82,-.713],[8.215,-14.866]],v:[[-79.058,-297.807],[-102.975,-293.657],[-103.687,-304.084],[-121.892,-318.257],[-144.24,-313.362],[-159.178,-304.49],[-177.357,-285.704],[-189.096,-254],[-143.74,-202.714],[-80.68,-241.25]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:8,s:[100],h:1},{t:12,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"ghost",np:2,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:8,op:11,st:-1,bm:0},{ddd:0,ind:10,ty:4,nm:"eyes mask",parent:3,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:0,k:154.191,ix:3},y:{a:1,k:[{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:2,s:[-29.508]},{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:4,s:[-44.85]},{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:8,s:[-29.508]},{i:{x:[.5],y:[1]},o:{x:[.5],y:[0]},t:10,s:[-44.85]},{i:{x:[.667],y:[1]},o:{x:[.5],y:[0]},t:13,s:[-29.508]},{i:{x:[.843],y:[.759]},o:{x:[.333],y:[0]},t:16,s:[-44.85]},{i:{x:[.667],y:[1]},o:{x:[.371],y:[.439]},t:18,s:[-29.508]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:20,s:[-9.507]},{t:22,s:[-29.508]}],ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:2,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:4,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:22,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:27,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:2,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:4,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:22,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:27,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"st",c:{a:0,k:[0,0,0,0],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:2,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:46,ix:2},o:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:1,s:[26]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:3,s:[-70.05]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:6,s:[23.375]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:17,s:[12]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:20,s:[12]},{t:23,s:[60]}],ix:3},m:1,ix:4,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:3,s:[0],h:1},{t:4,s:[100],h:1},{t:23,s:[100],h:1},{t:24,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes closed",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:2,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:4,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:22,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:27,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:2,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:4,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:22,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:27,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:3,s:[100],h:1},{t:4,s:[0],h:1},{t:23,s:[0],h:1},{t:24,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes open",np:2,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:0,op:8,st:28.5,bm:0},{ddd:0,ind:11,ty:4,nm:"main shape",parent:3,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0},ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:8,st:7,bm:0}]},{id:"comp_1",layers:[{ddd:0,ind:1,ty:3,nm:"MASTER 2",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[500,500,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:2,ty:3,nm:"MASTER 1",parent:1,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:1,k:[{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:0,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:1,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:2,s:[0]},{i:{x:[.833],y:[1.154]},o:{x:[.167],y:[0]},t:3,s:[0]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.083]},t:4,s:[0]},{i:{x:[.833],y:[1.083]},o:{x:[.167],y:[.167]},t:5,s:[-1.85]},{i:{x:[.833],y:[.892]},o:{x:[.167],y:[.042]},t:6,s:[-3.7]},{i:{x:[.833],y:[.925]},o:{x:[.167],y:[.362]},t:7,s:[0]},{i:{x:[.833],y:[.478]},o:{x:[.167],y:[-.726]},t:8,s:[1.106]},{i:{x:[.833],y:[.849]},o:{x:[.167],y:[.099]},t:9,s:[.992]},{i:{x:[.833],y:[.888]},o:{x:[.167],y:[.186]},t:10,s:[.393]},{i:{x:[.833],y:[.951]},o:{x:[.167],y:[.326]},t:11,s:[-.093]},{i:{x:[.833],y:[.751]},o:{x:[.167],y:[-.119]},t:12,s:[-.26]},{i:{x:[.833],y:[.861]},o:{x:[.167],y:[.125]},t:13,s:[-.192]},{i:{x:[.833],y:[.898]},o:{x:[.167],y:[.207]},t:14,s:[-.055]},{i:{x:[.833],y:[1.006]},o:{x:[.167],y:[.449]},t:15,s:[.037]},{i:{x:[.833],y:[.807]},o:{x:[.167],y:[.006]},t:16,s:[.058]},{i:{x:[.833],y:[.87]},o:{x:[.167],y:[.147]},t:17,s:[.035]},{i:{x:[.833],y:[.909]},o:{x:[.167],y:[.233]},t:18,s:[.006]},{i:{x:[.833],y:[1.268]},o:{x:[.167],y:[1.032]},t:19,s:[-.011]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.064]},t:20,s:[-.012]},{i:{x:[.833],y:[.917]},o:{x:[.167],y:[.166]},t:21,s:[-.006]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[.001]},t:22,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:23,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:24,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:25,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:26,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:27,s:[0]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:28,s:[0]},{t:29,s:[0]}],ix:10},p:{s:!0,x:{a:1,k:[{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:0,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:1,s:[51.923]},{i:{x:[.833],y:[.167]},o:{x:[.167],y:[0]},t:2,s:[51.923]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.083]},t:3,s:[51.923]},{i:{x:[.833],y:[1.083]},o:{x:[.167],y:[.167]},t:4,s:[61.923]},{i:{x:[.833],y:[.892]},o:{x:[.167],y:[.042]},t:5,s:[71.923]},{i:{x:[.833],y:[.925]},o:{x:[.167],y:[.362]},t:6,s:[51.923]},{i:{x:[.833],y:[.478]},o:{x:[.167],y:[-.726]},t:7,s:[45.945]},{i:{x:[.833],y:[.849]},o:{x:[.167],y:[.099]},t:8,s:[46.561]},{i:{x:[.833],y:[.888]},o:{x:[.167],y:[.186]},t:9,s:[49.799]},{i:{x:[.833],y:[.951]},o:{x:[.167],y:[.326]},t:10,s:[52.427]},{i:{x:[.833],y:[.751]},o:{x:[.167],y:[-.119]},t:11,s:[53.33]},{i:{x:[.833],y:[.861]},o:{x:[.167],y:[.125]},t:12,s:[52.958]},{i:{x:[.833],y:[.898]},o:{x:[.167],y:[.207]},t:13,s:[52.22]},{i:{x:[.833],y:[1.006]},o:{x:[.167],y:[.449]},t:14,s:[51.724]},{i:{x:[.833],y:[.807]},o:{x:[.167],y:[.006]},t:15,s:[51.611]},{i:{x:[.833],y:[.87]},o:{x:[.167],y:[.147]},t:16,s:[51.733]},{i:{x:[.833],y:[.909]},o:{x:[.167],y:[.233]},t:17,s:[51.892]},{i:{x:[.833],y:[1.268]},o:{x:[.167],y:[1.032]},t:18,s:[51.981]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.064]},t:19,s:[51.989]},{i:{x:[.833],y:[.917]},o:{x:[.167],y:[.166]},t:20,s:[51.956]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[-.003]},t:21,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:22,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:23,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:24,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:25,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:26,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:27,s:[51.923]},{i:{x:[.833],y:[1]},o:{x:[.167],y:[0]},t:28,s:[51.923]},{t:29,s:[51.923]}],ix:3},y:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:20,s:[40.385]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:22,s:[50]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:23,s:[40.385]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:24,s:[36.627]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:25,s:[37.014]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:26,s:[39.049]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:27,s:[40.701]},{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:28,s:[41.269]},{t:29,s:[40.385]}],ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:3,ty:4,nm:"eyes mask",parent:2,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:7,s:[154.376]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:11,s:[173.376]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:15,s:[154.376]},{i:{x:[.667],y:[1]},o:{x:[.167],y:[0]},t:18,s:[173.376]},{t:20,s:[154.376]}],ix:3},y:{a:1,k:[{t:1,s:[-29.5],h:1},{t:2,s:[-9.508],h:1},{t:20,s:[-29.5],h:1}],ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:4,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:5,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:18,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:20,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:23,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.01,y:1},o:{x:.167,y:0},t:26,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.99,y:0},t:27,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}],h:1}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:4,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:5,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:18,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:20,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:23,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.01,y:1},o:{x:.167,y:0},t:26,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.99,y:0},t:27,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}],h:1}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.177,99.785],ix:2},a:{a:0,k:[228.177,99.785],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:6,s:[100],h:1},{t:7,s:[0],h:1},{t:18,s:[0],h:1},{t:19,s:[100],h:1},{t:22,s:[100],h:1},{t:23,s:[0],h:1},{t:24,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"open",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:4,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:5,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:7,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:18,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:20,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:23,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.01,y:1},o:{x:.167,y:0},t:26,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.99,y:0},t:27,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}],h:1}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:4,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:5,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:7,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:18,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:20,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:21,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:23,s:[{i:[[0,11.836],[-9.767,-.02],[-3.827,12.146],[9.528,0]],o:[[2.934,9.755],[6.787,.014],[0,11.836],[-9.767,0]],v:[[31.8,-37.728],[47.945,-19.599],[64.435,-37.728],[48.237,-17.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:24,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.01,y:1},o:{x:.167,y:0},t:26,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.99,y:0},t:27,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{t:29,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}],h:1}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"st",c:{a:0,k:[0,0,0,0],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:2,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:41,ix:2},o:{a:1,k:[{i:{x:[0],y:[1]},o:{x:[.001],y:[0]},t:5,s:[-148]},{t:9,s:[-180]}],ix:3},m:1,ix:4,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"tr",p:{a:0,k:[228.177,99.785],ix:2},a:{a:0,k:[228.177,99.785],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:1,k:[{t:6,s:[1],h:1},{t:7,s:[100],h:1},{t:18,s:[100],h:1},{t:19,s:[1],h:1},{t:22,s:[1],h:1},{t:23,s:[100],h:1},{t:24,s:[1],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"closed",np:4,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:28.5,bm:0},{ddd:0,ind:4,ty:4,nm:"main shape",parent:2,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.333,y:0},t:4,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:5.5,s:[{i:[[17.24,-.077],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[-.62,42.719]],o:[[-20.297,.091],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[.108,-7.469]],v:[[-50.814,-319.717],[-102.975,-293.657],[-99.574,-302.684],[-112.356,-316.561],[-148.367,-293.657],[-146.79,-298.417],[-156.507,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-35.359,-298.356]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:8,s:[{i:[[9.502,-1.336],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.03,2.817],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:11.5,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:21,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:29,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:33.5,s:[{i:[[17.24,-.077],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[-.62,42.719]],o:[[-20.297,.091],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[.108,-7.469]],v:[[-50.814,-319.717],[-102.975,-293.657],[-99.574,-302.684],[-112.356,-316.561],[-148.367,-293.657],[-146.79,-298.417],[-156.507,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-35.359,-298.356]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:36,s:[{i:[[9.502,-1.336],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.03,2.817],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:39.5,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{t:47.5,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:7,bm:0}]},{id:"comp_2",layers:[{ddd:0,ind:1,ty:3,nm:"MASTER 3",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[500,475,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:2,ty:3,nm:"MASTER 2",parent:1,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:50,ix:3},y:{a:1,k:[{i:{x:[.492],y:[1]},o:{x:[.171],y:[.655]},t:0,s:[74.658]},{i:{x:[.769],y:[.51]},o:{x:[.533],y:[0]},t:9,s:[-112.092]},{i:{x:[.859],y:[-53.071]},o:{x:[.143],y:[-55.189]},t:17,s:[74.658]},{i:{x:[.488],y:[1]},o:{x:[.156],y:[0]},t:25,s:[74.658]},{t:29,s:[74.658]}],ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:3,ty:3,nm:"MASTER 1",parent:2,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:51.923,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:0,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:7,s:[50]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:16,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:23,s:[50]},{t:29,s:[40.385]}],ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:0,s:[96.154,96.154,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:1,s:[96.154,58.854,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:2,s:[96.154,72.83,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:3,s:[96.154,96.154,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:4,s:[96.154,101.291,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:5,s:[96.154,103.946,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:6,s:[96.154,104.554,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:7,s:[96.154,102.105,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:8,s:[96.154,99.283,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:9,s:[96.154,96.154,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:10,s:[96.154,97.003,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:11,s:[96.154,98.225,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:12,s:[96.154,99.538,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:13,s:[96.154,100.838,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:14,s:[96.154,102.102,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:15,s:[96.154,103.336,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:16,s:[96.154,104.554,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:17,s:[96.154,84.654,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:18,s:[96.154,83.598,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:19,s:[96.154,85.19,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:20,s:[96.154,87.429,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:21,s:[96.154,89.579,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:22,s:[96.154,91.472,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:23,s:[96.154,93.15,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:24,s:[96.154,94.693,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:25,s:[96.154,96.154,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:26,s:[96.154,96.334,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:27,s:[96.154,96.323,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:28,s:[96.154,96.265,100]},{t:29,s:[96.154,96.213,100]}],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:4,ty:0,nm:"cloud-puff-01-white",parent:2,refId:"comp_3",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:-80,ix:10},p:{s:!0,x:{a:0,k:-275.999,ix:3},y:{a:0,k:234.686,ix:4}},a:{a:0,k:[960,540,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,w:1920,h:1080,ip:18,op:28.5,st:18,bm:0},{ddd:0,ind:5,ty:0,nm:"cloud-puff-01-white",parent:2,refId:"comp_3",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:-80,ix:10},p:{s:!0,x:{a:0,k:-267.298,ix:3},y:{a:0,k:197.854,ix:4}},a:{a:0,k:[960,540,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,w:1920,h:1080,ip:17,op:27.5,st:17,bm:0},{ddd:0,ind:6,ty:4,nm:"eyes mask",parent:3,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:0,k:154.376,ix:3},y:{a:0,k:-29.5,ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:2,s:[0],h:1},{t:6,s:[100],h:1},{t:8,s:[0],h:1},{t:10,s:[100],h:1},{t:12,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:2,s:[0],h:1},{t:6,s:[100],h:1},{t:8,s:[0],h:1},{t:10,s:[100],h:1},{t:12,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.637,84.65],ix:2},a:{a:0,k:[228.637,84.65],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes-closed",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:0,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:3,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:9,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:2,s:[100],h:1},{t:6,s:[0],h:1},{t:8,s:[100],h:1},{t:10,s:[0],h:1},{t:12,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.167,y:0},t:0,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:3,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:9,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:2,s:[100],h:1},{t:6,s:[0],h:1},{t:8,s:[100],h:1},{t:10,s:[0],h:1},{t:12,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes open",np:2,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:28.5,bm:0},{ddd:0,ind:7,ty:4,nm:"main shape",parent:3,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.167,y:0},t:0,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:8,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:17,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:24,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{t:29,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:7,bm:0}]},{id:"comp_3",layers:[{ddd:0,ind:1,ty:0,nm:"puff-01-white",refId:"comp_4",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:-54,ix:10},p:{a:0,k:[966.461,520.618,0],ix:2},a:{a:0,k:[150,34,0],ix:1},s:{a:0,k:[68,68,100],ix:6}},ao:0,w:300,h:68,ip:-.5,op:5,st:-.5,bm:0}]},{id:"comp_4",layers:[{ddd:0,ind:1,ty:4,nm:"puff",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{a:1,k:[{i:{x:.29,y:1},o:{x:.07,y:.8},t:0,s:[150,34,0],to:[11.667,0,0],ti:[-11.667,0,0]},{t:5,s:[220,34,0]}],ix:2},a:{a:0,k:[230.455,-200.826,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:0,s:[100,100,100]},{t:5,s:[50,50,100]}],ix:6}},ao:0,shapes:[{ty:"gr",it:[{d:1,ty:"el",s:{a:0,k:[66.942,66.942],ix:2},p:{a:0,k:[0,0],ix:3},nm:"Ellipse Path 1",mn:"ADBE Vector Shape - Ellipse",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156922817,.972549080849,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[230.455,-200.826],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Ellipse 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:5.5,st:-105.5,bm:0}]},{id:"comp_5",layers:[{ddd:0,ind:1,ty:3,nm:"MASTER 2",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[500,500,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:2,ty:3,nm:"MASTER 1",parent:1,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:51.923,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:0,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:7,s:[50]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:16,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:23,s:[50]},{t:29,s:[40.385]}],ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:3,ty:4,nm:"eyes mask",parent:2,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:0,k:154.376,ix:3},y:{a:0,k:-29.5,ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:-3,s:[100],h:1},{t:-1,s:[0],h:1},{t:6,s:[100],h:1},{t:8,s:[0],h:1},{t:10,s:[100],h:1},{t:12,s:[0],h:1},{t:33,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:-3,s:[100],h:1},{t:-1,s:[0],h:1},{t:6,s:[100],h:1},{t:8,s:[0],h:1},{t:10,s:[100],h:1},{t:12,s:[0],h:1},{t:33,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.637,84.65],ix:2},a:{a:0,k:[228.637,84.65],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes-closed",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.42,y:0},t:-3,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:-2,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:0,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.833,y:1},o:{x:.167,y:0},t:9,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:30,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:31,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:33,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:-3,s:[0],h:1},{t:-1,s:[100],h:1},{t:6,s:[0],h:1},{t:8,s:[100],h:1},{t:10,s:[0],h:1},{t:12,s:[100],h:1},{t:32,s:[100],h:1},{t:33,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.42,y:0},t:-3,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:-2,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:0,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{i:{x:.833,y:1},o:{x:.167,y:0},t:9,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:30,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.58,y:1},o:{x:.42,y:0},t:31,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:33,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:-3,s:[0],h:1},{t:-1,s:[100],h:1},{t:6,s:[0],h:1},{t:8,s:[100],h:1},{t:10,s:[0],h:1},{t:12,s:[100],h:1},{t:32,s:[100],h:1},{t:33,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes open",np:2,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:0,op:31,st:28.5,bm:0},{ddd:0,ind:4,ty:4,nm:"main shape",parent:2,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.167,y:0},t:0,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:8,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:17,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:24,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{t:29,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:31,st:7,bm:0}]},{id:"comp_6",layers:[{ddd:0,ind:1,ty:3,nm:"MASTER 2",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[500,500,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:2,ty:3,nm:"MASTER 1",parent:1,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:51.923,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:0,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:7,s:[50]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:16,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:23,s:[50]},{i:{x:[.833],y:[1]},o:{x:[.333],y:[0]},t:29,s:[40.385]},{t:30,s:[40.385]}],ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:1,k:[{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:0,s:[96.154,96.154,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:1,s:[96.154,58.854,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:2,s:[96.154,72.83,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:3,s:[96.154,96.154,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:4,s:[96.154,98.491,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:5,s:[96.154,98.346,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:6,s:[96.154,97.594,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:7,s:[96.154,96.927,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:8,s:[96.154,96.5,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:9,s:[96.154,96.274,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:10,s:[96.154,96.176,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:11,s:[96.154,96.143,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:12,s:[96.154,96.137,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:13,s:[96.154,96.141,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:14,s:[96.154,96.147,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:15,s:[96.154,96.15,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:16,s:[96.154,96.152,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:17,s:[96.154,96.153,100]},{i:{x:[.833,.833,.833],y:[.833,.833,.833]},o:{x:[.167,.167,.167],y:[.167,.167,.167]},t:18,s:[96.154,96.154,100]},{t:30,s:[96.154,96.154,100]}],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:3,ty:4,nm:"eyes mask",parent:2,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:0,k:154.376,ix:3},y:{a:1,k:[{t:0,s:[-40.175],h:1},{t:3,s:[-29.5],h:1}],ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:0,s:[100],h:1},{t:2,s:[0],h:1},{t:6,s:[100],h:1},{t:8,s:[0],h:1},{t:10,s:[100],h:1},{t:12,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:0,s:[100],h:1},{t:2,s:[0],h:1},{t:6,s:[100],h:1},{t:8,s:[0],h:1},{t:10,s:[100],h:1},{t:12,s:[0],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.637,84.65],ix:2},a:{a:0,k:[228.637,84.65],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes-closed",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.42,y:0},t:0,s:[{i:[[2.954,-4.121],[-10.506,-.186],[-2.022,-4.891],[11.77,.083]],o:[[2.07,-6.444],[12.167,.216],[-2.367,-2.979],[-9.767,-.069]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.755,-49.604]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:3,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:9,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.724,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:0,s:[0],h:1},{t:2,s:[100],h:1},{t:6,s:[0],h:1},{t:8,s:[100],h:1},{t:10,s:[0],h:1},{t:12,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-r",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.58,y:1},o:{x:.42,y:0},t:0,s:[{i:[[2.218,-2.747],[-9.607,.144],[-1.21,-8.431],[9.528,0]],o:[[1.472,-5.48],[9.454,-.142],[-2.48,-6.044],[-9.767,0]],v:[[31.8,-37.728],[47.874,-52.379],[64.435,-37.728],[47.96,-49.536]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:1,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-45.228],[48.237,-65.349],[64.435,-45.228],[48.237,-24.87]],c:!0}]},{i:{x:.58,y:1},o:{x:.167,y:0},t:3,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]},{i:{x:.833,y:1},o:{x:.42,y:0},t:7,s:[{i:[[0,14.108],[-9.767,0],[0,-14.108],[9.528,0]],o:[[0,-14.108],[9.528,0],[0,14.108],[-9.767,0]],v:[[31.8,-42.856],[48.237,-66.839],[64.435,-42.856],[48.237,-18.59]],c:!0}]},{t:9,s:[{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[31.8,-42.728],[48.237,-62.849],[64.435,-42.728],[48.237,-22.37]],c:!0}]}],ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[48.118,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:1,k:[{t:0,s:[0],h:1},{t:2,s:[100],h:1},{t:6,s:[0],h:1},{t:8,s:[100],h:1},{t:10,s:[0],h:1},{t:12,s:[100],h:1}],ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[228.176,80.503],ix:2},a:{a:0,k:[228.176,80.503],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eyes open",np:2,cix:2,bm:0,ix:3,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:28.5,bm:0},{ddd:0,ind:4,ty:4,nm:"main shape",parent:2,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.333,y:0},t:0,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:8,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:17,s:[{i:[[9.595,0],[9.103,-13.949],[.222,2.955],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-.451,-6.007],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.445,-305.286],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:24,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{t:29,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:7,bm:0}]},{id:"comp_7",layers:[{ddd:0,ind:1,ty:3,nm:"MASTER 2",sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{a:0,k:[500,500,0],ix:2},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:0,ind:2,ty:3,nm:"MASTER 1",parent:1,sr:1,ks:{o:{a:0,k:0,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:51.923,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:0,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:7,s:[50]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:16,s:[40.385]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:23,s:[50]},{t:29,s:[40.385]}],ix:4}},a:{a:0,k:[50,50,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,ip:0,op:30,st:0,bm:0},{ddd:1,ind:3,ty:0,nm:"cloud-puff-01-white",parent:1,refId:"comp_3",sr:1,ks:{o:{a:0,k:100,ix:11},rx:{a:0,k:0,ix:8},ry:{a:0,k:0,ix:9},rz:{a:0,k:0,ix:10},or:{a:0,k:[0,0,0],ix:7},p:{s:!0,x:{a:1,k:[{i:{x:[.833],y:[.833]},o:{x:[.167],y:[.167]},t:15,s:[274.962]},{t:16,s:[299.001]}],ix:3},y:{a:0,k:28.107,ix:4},z:{a:0,k:0,ix:5}},a:{a:0,k:[960,540,0],ix:1},s:{a:0,k:[96.154,96.154,96.154],ix:6}},ao:0,w:1920,h:1080,ip:14,op:24.5,st:14,bm:0},{ddd:0,ind:4,ty:0,nm:"cloud-puff-01-white",parent:1,refId:"comp_3",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:307.702,ix:3},y:{a:0,k:-8.725,ix:4}},a:{a:0,k:[960,540,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,w:1920,h:1080,ip:11,op:21.5,st:11,bm:0},{ddd:0,ind:5,ty:4,nm:"gooey-2",parent:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:274.645,ix:3},y:{a:0,k:6.306,ix:4}},a:{a:0,k:[257.131,-73.942,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:14,s:[{i:[[5.75,13.5],[-6,-32.75],[-20.02,31.244],[-17.75,12],[-.388,14.3],[3.773,26.567]],o:[[-15.359,-36.06],[4.103,22.398],[16.5,-25.75],[11.347,-7.671],[.245,-9.02],[-6,-42.25]],v:[[245.75,-125.5],[211.75,-66.5],[253,10.75],[281.75,-64.5],[301.255,-94.23],[276.5,-142.5]],c:!0}]},{t:15,s:[{i:[[3.5,23.75],[-6,-32.75],[-1.489,37.078],[1.865,26.478],[-.314,30.525],[.75,29]],o:[[-5.714,-38.776],[4.103,22.398],[2.75,-68.5],[-1.25,-17.75],[.245,-23.77],[-1.056,-40.837]],v:[[244.5,-124.5],[211.75,-66.5],[250.5,-27],[272.5,-65.25],[304.255,-105.98],[275.25,-141]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Shape 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:14,op:16,st:3,bm:0},{ddd:0,ind:6,ty:4,nm:"gooey-1",parent:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:299.165,ix:3},y:{a:0,k:-30.713,ix:4}},a:{a:0,k:[257.131,-73.942,0],ix:1},s:{a:0,k:[96.154,96.154,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.833,y:.833},o:{x:.167,y:.167},t:11,s:[{i:[[5.75,13.5],[-6,-32.75],[-4.88,36.786],[-18.545,-3.091],[-.388,14.3],[21.836,-15.597]],o:[[-15.359,-36.06],[4.103,22.398],[6.5,-49],[13.511,2.252],[.245,-9.02],[-29.75,21.25]],v:[[245.75,-125.5],[211.75,-66.5],[250.5,-27],[286,-67.5],[309.255,-91.98],[273.5,-108.5]],c:!0}]},{t:12,s:[{i:[[3.5,23.75],[-6,-32.75],[-1.489,37.078],[-18.101,-5.081],[.495,22.73],[16.258,-21.348]],o:[[-5.714,-38.776],[4.103,22.398],[2.75,-68.5],[14.25,4],[-.196,-9.021],[-24.75,32.5]],v:[[244.5,-124.5],[211.75,-66.5],[250.5,-27],[284,-87.25],[313.255,-105.73],[278.75,-110.75]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Shape 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:11,op:13,st:0,bm:0},{ddd:0,ind:7,ty:4,nm:"eyes mask",parent:2,td:1,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:359.934,ix:10},p:{s:!0,x:{a:0,k:154.376,ix:3},y:{a:1,k:[{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:0,s:[-40.175]},{i:{x:[.667],y:[1]},o:{x:[.333],y:[0]},t:13,s:[-18.842]},{t:29,s:[-40.175]}],ix:4}},a:{a:0,k:[228.174,84.73,0],ix:1},s:{a:0,k:[183.04,183.04,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[253.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l-new 3",np:1,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"gr",it:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:0,k:{i:[[0,11.836],[-9.767,0],[0,-11.836],[9.528,0]],o:[[0,-11.836],[9.528,0],[0,11.836],[-9.767,0]],v:[[-18.687,-42.807],[-2.25,-62.928],[13.948,-42.807],[-2.25,-22.449]],c:!0},ix:2},nm:"Path 2",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"tm",s:{a:0,k:0,ix:1},e:{a:0,k:42.3,ix:2},o:{a:0,k:193,ix:3},m:1,ix:2,nm:"Trim Paths 1",mn:"ADBE Vector Filter - Trim",hd:!1},{ty:"st",c:{a:0,k:[1,1,1,1],ix:3},o:{a:0,k:100,ix:4},w:{a:0,k:7,ix:5},lc:2,lj:1,ml:4,bm:0,nm:"Stroke 1",mn:"ADBE Vector Graphic - Stroke",hd:!1},{ty:"tr",p:{a:0,k:[0,0],ix:2},a:{a:0,k:[0,0],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:4,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1},{ty:"tr",p:{a:0,k:[202.624,84.73],ix:2},a:{a:0,k:[-2.882,-42.609],ix:1},s:{a:0,k:[100,100],ix:3},r:{a:0,k:-.035,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"eye-l-new 2",np:1,cix:2,bm:0,ix:2,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:28.5,bm:0},{ddd:0,ind:8,ty:4,nm:"main shape",parent:2,tt:2,sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:539.999,ix:10},p:{a:0,k:[48,60,0],ix:2},a:{a:0,k:[-125.008,-243.563,0],ix:1},s:{a:0,k:[104,104,100],ix:6}},ao:0,shapes:[{ty:"gr",it:[{ind:0,ty:"sh",ix:1,ks:{a:1,k:[{i:{x:.667,y:1},o:{x:.333,y:0},t:1,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:8,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{i:{x:.667,y:1},o:{x:.333,y:0},t:17,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]},{i:{x:.833,y:1},o:{x:.333,y:0},t:24,s:[{i:[[9.595,0],[9.103,-13.949],[.621,1.847],[9.226,0],[7.012,-11.974],[.53,1.49],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[-1.764,-5.246],[-12.67,0],[.492,-1.728],[-1.619,-4.551],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-61.124,-325.205],[-102.975,-293.657],[-104.44,-304.483],[-117.956,-316.688],[-148.367,-293.657],[-149.697,-300.324],[-160.149,-307.676],[-198.557,-239.466],[-154.518,-192.805],[-42.303,-304.096]],c:!0}]},{t:29,s:[{i:[[9.595,0],[9.103,-13.949],[0,2.963],[9.226,0],[7.012,-11.974],[0,1.481],[6.519,0],[0,-31.848],[-31.491,0],[0,43.699]],o:[[-20.297,0],[1.107,-3.086],[0,-8.147],[-12.67,0],[.492,-1.728],[0,-5.678],[-20.543,0],[0,24.812],[55.356,0],[0,-17.159]],v:[[-58.321,-325.259],[-102.975,-293.657],[-101.253,-302.792],[-115.153,-316.742],[-148.367,-293.657],[-147.629,-298.472],[-157.347,-307.73],[-198.557,-239.466],[-154.518,-192.805],[-39.5,-304.15]],c:!0}]}],ix:2},nm:"Path 1",mn:"ADBE Vector Shape - Group",hd:!1},{ty:"fl",c:{a:0,k:[1,.992156863213,.972549021244,1],ix:4},o:{a:0,k:100,ix:5},r:1,bm:0,nm:"Fill 1",mn:"ADBE Vector Graphic - Fill",hd:!1},{ty:"tr",p:{a:0,k:[-117.081,-147.979],ix:2},a:{a:0,k:[-125,-234],ix:1},s:{a:0,k:[343,343],ix:3},r:{a:0,k:0,ix:6},o:{a:0,k:100,ix:7},sk:{a:0,k:0,ix:4},sa:{a:0,k:0,ix:5},nm:"Transform"}],nm:"Group 1",np:2,cix:2,bm:0,ix:1,mn:"ADBE Vector Group",hd:!1}],ip:0,op:30,st:7,bm:0}]}],layers:[{ddd:0,ind:1,ty:0,nm:"unlock-4",refId:"comp_0",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:500,ix:3},y:{a:0,k:500,ix:4}},a:{a:0,k:[500,500,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:1e3,h:1e3,ip:150,op:170,st:150,bm:0},{ddd:0,ind:2,ty:0,nm:"incorrect",refId:"comp_1",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:500,ix:3},y:{a:0,k:500,ix:4}},a:{a:0,k:[500,500,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:1e3,h:1e3,ip:120,op:150,st:120,bm:0},{ddd:0,ind:3,ty:0,nm:"jump",refId:"comp_2",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:500,ix:3},y:{a:0,k:500,ix:4}},a:{a:0,k:[500,500,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:1e3,h:1e3,ip:90,op:120,st:90,bm:10},{ddd:0,ind:4,ty:0,nm:"awake",refId:"comp_5",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:500,ix:3},y:{a:0,k:500,ix:4}},a:{a:0,k:[500,500,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:1e3,h:1e3,ip:60,op:90,st:60,bm:0},{ddd:0,ind:5,ty:0,nm:"wake-up",refId:"comp_6",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:500,ix:3},y:{a:0,k:500,ix:4}},a:{a:0,k:[500,500,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:1e3,h:1e3,ip:30,op:60,st:30,bm:0},{ddd:0,ind:6,ty:0,nm:"idle",refId:"comp_7",sr:1,ks:{o:{a:0,k:100,ix:11},r:{a:0,k:0,ix:10},p:{s:!0,x:{a:0,k:500,ix:3},y:{a:0,k:500,ix:4}},a:{a:0,k:[500,500,0],ix:1},s:{a:0,k:[100,100,100],ix:6}},ao:0,w:1e3,h:1e3,ip:0,op:30,st:0,bm:0}],markers:[]};d();p();var io=A(C());var Ya=()=>{let{t:e}=S(),t=(0,io.useCallback)(()=>{W.capture("walletMenuHelpCenter"),Ar({url:gi})},[]),o=(0,io.useCallback)(()=>{Ar({url:"onboarding.html?restore=true"})},[]);return io.default.createElement(io.default.Fragment,null,io.default.createElement(O,null,e("unlockForgotPassword")),io.default.createElement(zo,{size:"large"},io.default.createElement("div",null),io.default.createElement(uo,null,io.default.createElement(Wm,null),io.default.createElement(E,{size:26,weight:500,lineHeight:31},e("unlockForgotPassword")),io.default.createElement(E,{size:16,color:"#777777",lineHeight:21},e("forgotPasswordText")),io.default.createElement(hu,{margin:"8px 0 0",onClick:t},e("commandLearnMore"))),io.default.createElement(oe,{onClick:o,theme:"primary"},e("walletMenuItemsResetSecretPhrase"))))};d();p();var zl=A(C()),d1=({children:e,shouldAnimate:t})=>zl.default.createElement(Et.div,{animate:t?{x:-5}:{x:0},transition:{type:"spring",stiffness:400,damping:7,mass:1},style:{width:"100%"}},e),p1=({children:e})=>zl.default.createElement(Et.div,{whileTap:{scale:.95},transition:{type:"spring",stiffness:500,damping:30,mass:2},style:{width:"100%"}},e);var l3=f(Va)`
  ${e=>e.showBorder?"":"border-bottom: none;"}
`,d3=f.form`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
`,p3=f.div`
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
`,m3=f(mo.WithWarning)`
  position: relative;
  z-index: 10;
`,u3=f.div`
  display: flex;
  flex: 1;
  position: relative;
  min-height: 252px;
  width: 250px;
  margin-bottom: -20px;
`,f3=f.div`
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 62%;
  height: 50%;
`,g3=f(E).attrs({size:24,weight:500,lineHeight:34,margin:"0 0 20px"})``,x3=f(E).attrs({size:16,lineHeight:19,weight:500,color:"#666666"})`
  margin-top: 16px;
  text-decoration: none;
  &:hover {
    color: #999999;
  }
`,m1=ne.default.memo(({onUnlock:e})=>{let{t}=S(),{pushDetailView:o}=U(),{showSettingsMenu:r}=Gt(),{control:n,formState:{errors:i},handleSubmit:s,setError:a,setFocus:c}=to(),[l,m]=ne.default.useState(!0),[u,g]=ne.default.useState(!1),[x,y]=ne.default.useState(!1),b=(0,ne.useRef)(null),h=(0,ne.useCallback)(()=>{b.current?.setStageNow("wake-up")},[]),w=(0,ne.useCallback)(()=>{b.current?.setStageNow("jump")},[]),T=(0,ne.useCallback)(()=>{b.current?.setStageNow("incorrect"),y(!0),setTimeout(()=>{y(!1)},75)},[]),v=(0,ne.useCallback)(()=>{b.current?.setStageNow("unlock")},[]),I=(0,ne.useCallback)(async({password:F})=>{try{if(g(!0),!await e(F))throw new Error("Incorrect password");v(),g(!1),m(!1),W.capture("unlockWallet")}catch(P){P.message&&P.message.includes("Incorrect password")?(a("password",{type:"manual"}),T()):($.captureError(new Error(`\u{1F6AB} Unexpected Error when Unlocking Wallet: ${P.message}`),"account"),a("password",{type:"manual",message:t("unlockErrorSomethingWentWrong")})),g(!1)}},[T,v,e,a,t]),N=()=>{W.capture("forgotPassword"),$.addBreadcrumb("startUp","\u{1F300} Password reset initiated","debug"),r(),o(ne.default.createElement(Ya,null))},B=()=>{W.capture("unlockWalletBodyClicked"),c("password")},{downloadLogs:V,goSupportDesk:L}=Iu();return ne.default.createElement(Gi,null,ne.default.createElement(l3,{justifyContent:"space-between",showBorder:l},l?ne.default.createElement(ne.default.Fragment,null,ne.default.createElement("div",{style:{width:ru*2}})," ",ne.default.createElement(Nm,{width:94}),ne.default.createElement(nu,{items:[{label:t("settingsSupportDesk"),key:t("settingsSupportDesk"),onClick:()=>{W.capture("walletMenuHelpCenter"),L()}},{label:t("settingsDownloadApplicationLogs"),key:t("settingsDownloadApplicationLogs"),onClick:()=>{W.capture("walletMenuHelpCenter"),V()}}]},ne.default.createElement(Ni,null,ne.default.createElement(Hm,null)))):null),ne.default.createElement(Rt,{onClick:B},ne.default.createElement(d3,{id:"unlock-form",onSubmit:s(I)},ne.default.createElement(p3,null,ne.default.createElement(u3,null,ne.default.createElement(e1,{stages:h3,stageRef:b,path:l1,autoplay:!1,loop:!1}),ne.default.createElement(f3,{onClick:w})),l?ne.default.createElement(ne.default.Fragment,null,ne.default.createElement(g3,null,t("unlockEnterPassword")),ne.default.createElement(d1,{shouldAnimate:x},ne.default.createElement(y3,{onDirty:h,control:n,warning:!!i.password,warningMessage:i.password?.message})),ne.default.createElement(x3,{onClick:N},t("unlockForgotPassword"))):null))),ne.default.createElement(Qe,{plain:!0},l?ne.default.createElement(p1,null,ne.default.createElement(oe,{"data-testid":"unlock-form-submit-button",form:"unlock-form",type:"submit",theme:"primary",disabled:u,loading:u,height:"47px"},t("unlockActionButtonUnlock"))):null))}),y3=ne.default.memo(({control:e,warning:t,warningMessage:o,onDirty:r})=>{let{t:n}=S(),{field:i,fieldState:{isDirty:s}}=qp({name:"password",control:e,defaultValue:"",rules:{required:!0}}),a=(0,ne.useCallback)(c=>{s||r(),i.onChange(c)},[i,s,r]);return ne.default.createElement(m3,{"data-testid":"unlock-form-password-input",name:i.name,autoFocus:!0,onBlur:i.onBlur,onChange:a,placeholder:n("unlockPassword"),type:"password",value:i.value,ref:i.ref,warning:t,warningMessage:o})}),h3=[{identifier:"idle",segment:[0,29],loop:!0,autoAdvance:!1},{identifier:"wake-up",segment:[30,59],loop:!0,autoAdvance:!1,nextIdentifier:"awake"},{identifier:"awake",segment:[60,89],loop:!0,autoAdvance:!1},{identifier:"jump",segment:[90,119],loop:!0,autoAdvance:!1,nextIdentifier:"awake",shouldNotInterrupt:!0},{identifier:"incorrect",segment:[120,149],loop:!0,autoAdvance:!1,nextIdentifier:"awake"},{identifier:"unlock",segment:[150,169],loop:!1,autoAdvance:!1}];d();p();var u1=A(A0());d();p();function ws(e){$.addBreadcrumb("startUp",e,"debug")}function k3(){return{onLockState(e){switch(e){case"locked":ws("\u{1F512} Extension locked");break;case"migration-failure":ws("\u274C Migration failure");break;case"onboarding":ws("\u{1F5DD}\uFE0F Onboarding incomplete");break;case"unlocked":{ws("\u{1F513} Extension unlocked");break}case"unknown":break}},onUnlockSettled(e,t){ws(`${e?"\u{1F513} Unlocked":"\u{1F512} Locked"}. Migration: ${t}`)}}}var Ja=k3();var Xa=gn((e,t)=>({isConnected:!1,state:"unknown",async refresh(){return t().isConnected||(u1.default.runtime.connect(),e({isConnected:!0})),await _l()?await uu()?e({state:"unlocked"}):e({state:"locked"}):e({state:"onboarding"})},async unlock(o){if(!await _l())return e({state:"onboarding"}),!1;let{isUnlocked:n,migrationResult:i}=await mu(o);return Ja.onUnlockSettled(n,i),i==="failure"?(e({state:"migration-failure"}),!0):n?(e({state:"unlocked"}),!0):(e({state:"locked"}),!1)},async lock(){let o=await fu();return o&&e({state:"locked"}),o}}));d();p();var jo=A(C());d();p();var Hn=A(C());var f1=({children:e,isOpen:t,onClose:o})=>Hn.default.createElement(Sn,null,t&&Hn.default.createElement(Hn.default.Fragment,null,Hn.default.createElement(A3,{onClick:o,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{ease:"linear",duration:.25}}),Hn.default.createElement(S3,{"aria-modal":"true",initial:{y:self.innerHeight},animate:{y:0},exit:{y:self.innerHeight},transition:{ease:"easeOut",duration:.25}},Hn.default.createElement(w3,null,e)))),S3=f(Et.div)`
  position: absolute;
  z-index: 50;
`,A3=f(Et.div)`
  width: 100%;
  height: 100%;
  margin: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(1px);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 50;
`,w3=f.div`
  display: flex;
  flex-direction: column;
  min-width: ${Di}px;
  min-height: ${Pa}px;
  height: 100vh;
  width: 100vw;
  background: #222222;
  padding: 0 16px 16px;
`;d();p();var ae=A(C());d();p();var nt=A(C());d();p();var ke=A(C());d();p();var pe=A(C());var wn=pe.default.memo(({accountIdentifier:e})=>{let{t}=S(),{popDetailView:o}=U(),{data:r}=te(),{data:n}=kr(e||""),{mutate:i}=Rp(),s=r?.findIndex(u=>u.identifier===e)||r?.length||0,a=Vt(u=>u.editableAccountMetadata),c=Vt(u=>u.setEditableAccountMetadata),l=(0,pe.useCallback)(u=>{c&&c({...a,icon:u})},[a,c]),m=(0,pe.useCallback)(u=>{n?(W.capture("accountAvatarModified",{data:{type:u.type}}),i({identifier:n.identifier,icon:u}),yn(n?.icon,u)||We.success(t("settingsUpdateAvatarToast"))):l(u),o()},[l,n,o,i,t]);return pe.default.createElement($l,{accountIndex:s,accountName:n?.name||a?.name||"",accountIdentifier:e,currentIcon:n?.icon||a?.icon,onPrimaryClicked:m})}),$l=pe.default.memo(({accountIndex:e,accountName:t,accountIdentifier:o,currentIcon:r,onPrimaryClicked:n,onSecondaryClicked:i})=>{let{t:s}=S(),{scrollContainerRef:a,scrollPosition:c}=Hu(),{mutate:l}=Tp(),{data:m}=Cp(),u=m?m.skinTone:up.skinTone,[g,x]=(0,pe.useState)(!1),[y,b]=(0,pe.useState)(0),[h,w]=(0,pe.useState)(r??mn),T=(0,pe.useCallback)(()=>{x(!0)},[]),v=(0,pe.useCallback)(()=>{x(!1)},[]),I=(0,pe.useCallback)(F=>{w({unicode:F,imageUrl:void 0,type:"emoji"})},[]),N=(0,pe.useCallback)(F=>{w({unicode:void 0,imageUrl:F,type:"image"})},[]),B=(0,pe.useCallback)(F=>{l({skinTone:F}),v()},[v,l]),V=(0,pe.useCallback)(()=>{w(r??mn)},[r]),L=(0,pe.useMemo)(()=>{let F=[s("settingsEmojis")];return o&&F.push(s("collectiblesTab")),F},[s,o]);return pe.default.createElement(pe.default.Fragment,null,pe.default.createElement(O,{icon:pe.default.createElement(br,{fill:"#777"})},s("settingsSelectAvatar")),pe.default.createElement(Uu,{tabs:L,selectedIndex:y,setSelectedIndex:b},pe.default.createElement(M,{direction:"row",alignItems:"center"},y===0&&pe.default.createElement(Ru,{currentSkinTone:u,isVisible:g,onClose:v,onClick:T,onSelect:B}),pe.default.createElement($e,{accountIndex:e,accountName:t,accountIcon:h,size:"small"}))),pe.default.createElement(M,{flex:1,direction:"row",overflow:"auto",paddingTop:16,paddingBottom:20,ref:a},pe.default.createElement(Vu,{tabs:L,selectedIndex:y},pe.default.createElement(Gu,{onEmojiSelect:I,containerRef:a,scrollPosition:c,skinTone:u,previewIcon:h}),o&&pe.default.createElement(_u,{accountIdentifier:o,onCollectibleSelect:N,previewIcon:h}))),pe.default.createElement(rt,null,pe.default.createElement(gt,{primaryText:s("commandSave"),secondaryText:s("commandClear"),onPrimaryClicked:()=>n(h),onSecondaryClicked:i??V})))});d();p();var zt=A(C());var b3=f(E).attrs({size:16,lineHeight:19,weight:500})`
  padding-left: 8px;
`,x1=zt.default.memo(({account:e,onCloseClick:t})=>{let{t:o}=S(),{popDetailView:r,pushDetailViewCallback:n}=U(),{data:i}=te(),s=i?.indexOf(e)??0,{addresses:a,name:c}=e,l=(0,zt.useMemo)(()=>a.length>0?a.map(m=>{let u=n(zt.default.createElement(Wu,{address:m.address,networkID:m.networkID,headerType:"settings",onCloseClick:r}));return zt.default.createElement("li",null,zt.default.createElement(Lu,{key:`${m.networkID}-${m.addressType}`,chainAddress:m,onQRClick:u}))}):[],[a,r,n]);return zt.default.createElement(zt.default.Fragment,null,zt.default.createElement(O,{shouldWrap:!1},zt.default.createElement($e,{accountIndex:s,accountName:c,size:"xsmall"}),zt.default.createElement(b3,null,c)),zt.default.createElement(M,{paddingY:"screen",overflow:"auto"},zt.default.createElement(M,{element:"ul",gap:"list"},l)),zt.default.createElement(rt,null,zt.default.createElement(oe,{onClick:t},o("commandClose"))))});d();p();var $t=A(C());var y1=$t.default.memo(({accountIdentifier:e,accountName:t})=>{let{t:o}=S(),{popDetailView:r,pushDetailView:n}=U(),{formState:{errors:i},register:s,getValues:a,handleSubmit:c}=to(),{data:l}=te(),{data:m}=kr(e),{mutate:u}=Hp(),g=l?.indexOf(m),x=Vt(v=>v.editableAccountMetadata),y=Vt(v=>v.setEditableAccountMetadata),b=(0,$t.useCallback)(()=>{y({...x,name:a("name")}),n($t.default.createElement(wn,{accountIdentifier:e}))},[e,x,a,n,y]),h=Object.keys(i).length===0,w={required:!0},T=(0,$t.useCallback)(async({name:v})=>{m?.identifier&&(u({identifier:m?.identifier,name:v}),r(),W.capture("changeAccountName",{data:{accountIdentifier:m?.identifier}}),y(void 0),t!==v&&We.success(o("settingsUpdateAccountNameToast")))},[m?.identifier,t,r,u,y,o]);return $t.default.createElement(fo,{onSubmit:c(T)},$t.default.createElement(O,{shouldWrap:!0},o("settingsWalletNamePrimary")),$t.default.createElement(zo,{size:"large"},$t.default.createElement(uo,null,$t.default.createElement(Vi,{icon:$t.default.createElement(Ui,null,$t.default.createElement(Dr,{accountIndex:g||0,accountName:t,accountIcon:m?.icon,onClick:b})),primaryText:o("settingsWalletNamePrimary")}),$t.default.createElement(Tr,null,$t.default.createElement(mo,{autoFocus:!0,placeholder:o("settingsPlaceholderName"),maxLength:wi,defaultValue:x?.name||t,...s("name",w)}))),$t.default.createElement(gt,{primaryText:o("commandSave"),secondaryText:o("commandCancel"),onSecondaryClicked:r,primaryDisabled:!h})))});d();p();var yo=A(C());d();p();var bs=A(C());var bn=({type:e})=>{let{t}=S(),o=e==="privateKey"?t("exportSecretPrivateKey"):t("exportSecretSecretPhrase");return bs.default.createElement(v3,{"data-testid":`warning-island-${e}`},bs.default.createElement(au,{color:"#EB3742",title:bs.default.createElement(Ro,{i18nKey:"exportSecretWarningPrimaryInterpolated",values:{secretNameText:o}},"Do ",bs.default.createElement("u",null,"not")," share your ",o,"!"),description:t("exportSecretWarningSecondaryInterpolated",{secretNameText:o})}))},v3=f.div`
  margin-bottom: 20px;
`;var zi=yo.default.memo(({password:e,accountIdentifier:t,chainAddress:o})=>{let{t:r}=S(),{popDetailView:n}=U(),[i,s]=yo.default.useState("");(0,yo.useEffect)(()=>{t&&o?(async()=>{try{let c=o.networkID,l=j.getNetworkDefinition(c).value,m=o.addressType,g=(await e.foldAsync(x=>Dm(Buffer.from(x).toString("utf-8"),t,m))).fold(x=>O0({privateKey:x,networkIDValue:l,addressType:m}));s(g),$.addBreadcrumb("account",`\u2705 Exported Private Key for Account: ${t}`,"info"),W.capture("exportPrivateKey",{data:{chainType:m,chainId:c}})}catch{throw $.addBreadcrumb("account",`\u{1F6AB} Failed to Export Private Key for Account: "${t}", networkID: "${o.networkID}", address: "${o.address}"`,"error"),new Error("Could not get private key")}})():$.addBreadcrumb("account",`\u{1F6AB} Failed to Export Private Key: "${t}", chainAddress: "${JSON.stringify(o)}"`,"error")},[t,o,e]);let a=()=>{n(),n(),n()};return yo.default.createElement(yo.default.Fragment,null,yo.default.createElement(O,{shouldWrap:!0},r("exportSecretYourPrivateKey")),yo.default.createElement(K,null,yo.default.createElement(uo,null,yo.default.createElement(bn,{type:"privateKey"}),i?yo.default.createElement(Ou,{value:i}):yo.default.createElement(Cr,{height:"138px",width:"100%",backgroundColor:"#4D4D4D",borderRadius:"8px"}))),yo.default.createElement(oe,{onClick:a},r("pastParticipleDone")))});d();p();var Po=A(C());d();p();var _n=A(C());var Qa=_n.default.memo(({indexes:e,wordlist:t})=>_n.default.createElement(I3,null,e.length&&t.length?e.map((o,r)=>_n.default.createElement(C3,{key:r},_n.default.createElement(D3,null,`${r+1}.`),_n.default.createElement(E3,{"data-testid":`secret-recovery-phrase-word-input-${r}`,readOnly:!0,autoComplete:"off",autoCorrect:"off",spellCheck:!1,value:t[o],pattern:"[A-Za-z\\s]+"}))):Array.from({length:e?e.length:12},(o,r)=>_n.default.createElement(T3,{key:r})))),C3=f(xt).attrs({align:"center",justify:"center"})`
  color: #fff;
  background: #181818;
  border: 1px solid #2f2f2f;
  border-radius: 6px;
  box-shadow: 0 0 4px 0 rgba(0, 0, 0, 0.25);
  align-self: center;
  height: 41px;
`,T3=f(Cr).attrs({backgroundColor:"#484848",borderRadius:"6px",height:"41px"})``,I3=f.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  row-gap: 10px;
  column-gap: 4.5px;
  width: 100%;
`,D3=f(E).attrs({size:14,weight:400,color:"#999999"})`
  padding-left: 8px;
  padding-right: 2px;
`,E3=f.input`
  color: #fff;
  background: none;
  border: none;
  width: 100%;
  font-size: 14px;
  font-weight: 500;
  padding: 0;
`;var Za=({accountIdentifier:e,password:t})=>{let{t:o}=S(),{popDetailView:r}=U(),[{indexes:n,wordlist:i},s]=(0,Po.useState)({indexes:[],wordlist:[]}),{data:a}=kr(e);return(0,Po.useEffect)(()=>{(async()=>{if((a?.type==="seed"||a?.type==="seedless")&&a?.seedIdentifier)try{let c=await t.foldAsync(g=>Ma(Buffer.from(g).toString("utf-8"),a.seedIdentifier));if(!c)throw new Error("Could not get mnemonic");let l=await Da(),m=c.fold(l.fromEntropy),u=Array.from(await m.getIndexes());s({indexes:u,wordlist:l.wordlist}),$.addBreadcrumb("account",`\u2705 Exported S*ed for Account: ${e}`,"info"),W.capture("exportMnemonic")}catch{throw $.addBreadcrumb("account",`\u{1F6AB} Failed to Export S*ed for Account: "${e}", account: "${JSON.stringify(a)}"`,"error"),new Error("Could not get mnemonic")}else $.addBreadcrumb("account",`\u{1F6AB} Failed to Export S*ed: Account: "${JSON.stringify(a)}"`,"error")})()},[a,e,t]),Po.default.createElement(Po.default.Fragment,null,Po.default.createElement(O,{shouldWrap:!0},o("exportSecretYourSecretRecoveryPhrase")),Po.default.createElement(K,null,Po.default.createElement(uo,null,Po.default.createElement(bn,{type:"mnemonic"}),Po.default.createElement(Qa,{indexes:n,wordlist:i}))),Po.default.createElement(oe,{onClick:()=>{r(),r()}},o("pastParticipleDone")))};d();p();var Mo=A(C());var h1=Mo.default.memo(({account:e})=>{let{t}=S(),{popDetailView:o}=U(),{hideSettingsMenu:r}=Gt(),{mutate:n}=Vp(),i=t("removeAccountInterpolated",{accountName:e.name}),s=gp(e.type),a=(0,Mo.useCallback)(c=>{c.preventDefault(),n({identifier:e.identifier}),r()},[e.identifier,r,n]);return e?Mo.default.createElement(fo,{onSubmit:a},Mo.default.createElement(O,null,t("settingsRemoveWallet")),Mo.default.createElement(zo,{size:"large"},Mo.default.createElement("div",null),Mo.default.createElement(uo,null,Mo.default.createElement(Um,null),Mo.default.createElement(E,{size:28,weight:500,lineHeight:34},i),Mo.default.createElement(E,{size:16,color:"#777777",lineHeight:19,maxWidth:"312px"},s)),Mo.default.createElement(gt,{primaryText:t("removeAccountActionButtonRemove"),secondaryText:t("commandCancel"),primaryTheme:"warning",onSecondaryClicked:o}))):null});d();p();var qe=A(C());var ec=qe.default.memo(({password:e,accountIdentifier:t})=>{let{t:o}=S(),{popDetailView:r,pushDetailView:n}=U(),{data:i}=kr(t),s=(0,qe.useCallback)(l=>{n(qe.default.createElement(zi,{password:e,chainAddress:l,accountIdentifier:t}))},[t,e,n]),a=(0,qe.useMemo)(()=>i?i.addresses.map((l,m)=>qe.default.createElement(P3,{dataTestId:`select-private-key-${l.networkID}`,key:`${l.networkID}-${l.address}-${m}`,chainAddress:l,onClick:s})):[],[i,s]),c=()=>{r(),r()};return qe.default.createElement(qe.default.Fragment,null,qe.default.createElement(O,{shouldWrap:!0},o("exportSecretYourPrivateKey")),qe.default.createElement(K,null,qe.default.createElement(uo,null,qe.default.createElement(bn,{type:"privateKey"}),a)),qe.default.createElement(oe,{onClick:c},o("commandCancel")))}),P3=({dataTestId:e,chainAddress:t,onClick:o})=>{let r=j.getNetworkName(t.networkID);return qe.default.createElement(M3,{onClick:()=>o(t),"data-testid":e},qe.default.createElement(po,{networkID:t.networkID,size:48}),qe.default.createElement(Nt,{margin:"0 0 0 8px"},qe.default.createElement(ku,{networkID:t.networkID,walletAddress:t.address},qe.default.createElement(E,{size:16,weight:600,lineHeight:19,noWrap:!0,margin:"0 0 1px"},r)),qe.default.createElement(E,{size:14,weight:400,lineHeight:18,color:"#999999"},be(t.address,4))))},M3=f.div`
  background: #2a2a2a;
  border-radius: 6px;
  padding-top: 15px;
  padding-bottom: 15px;
  padding-left: 10px;
  padding-right: 15px;
  display: flex;
  margin-bottom: 10px;
  align-items: center;
  width: 100%;
  cursor: pointer;
  &:hover {
    background: #333333;
  }
`;var F3=f(E).attrs({size:14,weight:500,lineHeight:17,whiteSpace:"nowrap",color:"#777"})``,N3=(e,t=[])=>{if(!e)return{canExportPrivateKey:!1,canExportSeed:!1,seedIndex:0};switch(e.type){case"seed":case"seedless":return{canExportPrivateKey:!0,canExportSeed:!0,seedIndex:Math.max(t.findIndex(o=>o.identifier===e.seedIdentifier),0)};case"privateKey":return{canExportPrivateKey:!0,canExportSeed:!1,seedIndex:0};case"ledger":case"seedVault":case"readOnly":return{canExportPrivateKey:!1,canExportSeed:!1,seedIndex:0}}},$i=ke.default.memo(({accountIdentifier:e})=>{let{t}=S(),{popDetailView:o,pushDetailView:r,pushDetailViewCallback:n}=U(),{data:i=[]}=Do("all"),{data:s}=te(),{data:a}=kr(e),c=s?.indexOf(a),l=a?.name||"",m=a?.addresses??[],[u]=m,{canExportPrivateKey:g,canExportSeed:x,seedIndex:y}=(0,ke.useMemo)(()=>N3(a),[a]),b=x&&i.length>1,h=!!s&&s.length>1,w=a?.type!=="readOnly",T=(0,ke.useCallback)(P=>{let H=a?.addresses||[];if(H.length===1){let[G]=H;r(ke.default.createElement(zi,{password:P,chainAddress:G,accountIdentifier:e}))}else r(ke.default.createElement(ec,{password:P,accountIdentifier:e}))},[a,r,e]),v=(0,ke.useCallback)(P=>{r(ke.default.createElement(Za,{accountIdentifier:e,password:P}))},[r,e]),I=n(ke.default.createElement(Oi,{type:"privateKey",onSuccess:T})),N=n(ke.default.createElement(Oi,{type:"mnemonic",onSuccess:v})),B=n(ke.default.createElement(wn,{accountIdentifier:e})),V=(0,ke.useMemo)(()=>{let P=m.length===1?{topLeft:{text:t("settingsWalletAddress")},topRight:{text:m.length.toString()},end:ke.default.createElement(wu,{copyString:u.address,alignment:"bottomCenter"},ke.default.createElement(F3,null,be(u.address,4))),type:"base"}:{topLeft:{text:t("settingsWalletAddresses")},topRight:{text:m.length.toString()},type:"drawer",onClick:a?n(ke.default.createElement(x1,{account:a,onCloseClick:o})):()=>{}};return[{topLeft:{text:t("settingsWalletNamePrimary")},topRight:{text:l},type:"drawer",onClick:n(ke.default.createElement(y1,{accountIdentifier:e,accountName:l}))},P]},[m.length,u,a,l,e,t,n,o]),L=(0,ke.useMemo)(()=>{let P=[];return x&&(b&&P.push({topLeft:{text:t("addAccountSecretPhraseLabel")},topRight:{text:t("addAccountSecretPhraseDefaultLabel",{number:y+1})},type:"base"}),P.push({topLeft:{text:t("settingsShowSecretRecoveryPhraseTertiary")},type:"drawer",onClick:N})),g&&P.push({topLeft:{text:t("settingsExportPrivateKey")},type:"drawer",onClick:I}),P},[x,g,b,t,N,y,I]),F=[{topLeft:{text:t("settingsRemoveWallet"),color:"accentAlert"},onClick:a?n(ke.default.createElement(h1,{account:a})):void 0}];return ke.default.createElement(ke.default.Fragment,null,ke.default.createElement(O,null,t("settingsEditWallet")),ke.default.createElement(za,null,w?ke.default.createElement(Dr,{accountIndex:c||0,accountName:l,accountIcon:a?.icon,onClick:B}):ke.default.createElement(bu,{accountIndex:c||0,accountName:l,accountIcon:a?.icon,onClick:B})),ke.default.createElement(M,{gap:"section"},ke.default.createElement(z,{rows:V}),ke.default.createElement(z,{rows:L}),h&&ke.default.createElement(z,{rows:F})))});d();p();d();p();d();p();d();p();var Jr=A(C());d();p();var B3=y0==="extension"?"onClick":"onPress",Fo=e=>({[B3]:e});var ql=({navigateToPublicAddresses:e,toastController:t})=>{let{t:o}=S(),r=hr(),n=wl(),{PRIVACY_STATE_DISPLAY_METADATA:i}=tc(),{mutateAsync:s}=xi();return Jr.default.createElement(M,{direction:"column",width:"100%",gap:16},Jr.default.createElement(z,{rows:["public","private","invisible"].map(a=>{let{icon:c,title:l,description:m}=i[a],u=c;return{type:"radio",id:`privacy-state--${a}`,active:n===a,alignStartNodeItems:"ascender",start:Jr.default.createElement(u,{size:18,color:"accentPrimary"}),topLeft:{text:l},bottomLeft:{component:()=>Jr.default.createElement(R,{align:"left",font:"label",color:"textSecondary",children:m})},...Fo(async()=>{try{await s({privacyState:a}),t.success(o("settingsManageUserProfileUpdatePrivacyStateToast")),r.onPrivacyStateUpdated(a)}catch(g){$.captureError(g,"account"),t.error(o("settingsManageUserProfileUpdatePrivacyStateToastFailure"))}})}})}),Jr.default.createElement(z,{rows:[{type:"drawer",disabled:n==="invisible",start:Jr.default.createElement(J.Search,{size:18,color:"accentPrimary"}),topLeft:{text:o("settingsClaimUsernameManageAddressesTitle")},topRight:n==="invisible"?{text:o("pastParticipleDisabled")}:void 0,...Fo(e)}]}))},tc=()=>{let{t:e}=S(),t=wl(),o=(0,Jr.useMemo)(()=>({public:{title:e("settingsManageUserProfilePrivacyStatePublic"),icon:J.Globe,description:e("settingsManageUserProfilePrivacyStatePublicDescription")},private:{title:e("settingsManageUserProfilePrivacyStatePrivate"),icon:J.Lock,description:e("settingsManageUserProfilePrivacyStatePrivateDescription")},invisible:{title:e("settingsManageUserProfilePrivacyStateInvisible"),icon:J.EyeOff,description:e("settingsManageUserProfilePrivacyStateInvisibleDescription")},hidden:{title:e("settingsManageUserProfilePrivacyStatePublic"),icon:J.Globe,description:e("settingsManageUserProfilePrivacyStatePublicDescription")}}),[e]);return{currentPrivacyState:o[t],PRIVACY_STATE_DISPLAY_METADATA:o}};d();p();var vs=A(cm()),Fr=A(C());var jl=({AccountAvatar:e,toastController:t})=>{let{t:o}=S(),r=hr(),n=ki(),{data:i=[]}=te(),s=ep(),{mutateAsync:a,isPending:c}=xi(),l=(0,Fr.useMemo)(()=>n.map(m=>{let u=j.getChainID(m),x=i.filter(y=>y.addresses.some(b=>(0,vs.default)(b.networkID,m))).map(y=>{let b=i.findIndex(T=>(0,vs.default)(T.identifier,y.identifier)),h=y.addresses.find(T=>(0,vs.default)(T.networkID,m))?.address;if(!h)throw new Error(`Unable to find address for Chain (${m}) in account: ${y.identifier}`);let w=y.addresses.some(T=>(0,vs.default)(s?.[m],T.address));return{start:Fr.default.createElement(e,{size:"small",accountIcon:y.icon,accountIndex:b,accountName:y.name}),topLeft:{text:y.name,font:"body"},bottomLeft:{text:be(h||"")},active:w,disabled:c,type:"radio",...Fo(async()=>{try{let T={...s,[m]:h};await a({addresses:T}),t.success(o("settingsManageUserProfileUpdatePublicAddressToast")),r.onPublicAddressesUpdated(Object.entries(T).filter(([v,I])=>!!I).map(([v])=>v))}catch(T){$.captureError(T,"account"),t.error(o("settingsManageUserProfileUpdatePublicAddressToastFailure"))}})}});return x.length===1&&x[0]?.type==="check"&&x[0]?.active&&(x[0].disabled=!0),Fr.default.createElement(M,{key:m,direction:"column",gap:16},Fr.default.createElement(M,{direction:"row",gap:12,alignItems:"center",justifyContent:"flex-start"},Fr.default.createElement(po,{networkID:m,size:24}),Fr.default.createElement(R,{font:"captionSemibold",color:"textPrimary",children:o(j.getChainName(u))})),Fr.default.createElement(z,{rows:x}))}),[e,i,n,c,r,a,s,o,t]);return Fr.default.createElement(M,{direction:"column",width:"100%",gap:24},l)};d();p();var Kl=A(cm()),qi=A(C());var Yl=({AccountAvatar:e,toastController:t})=>{let{t:o}=S(),r=hr(),{data:n=[]}=te(),{data:i}=la(),{mutateAsync:s}=Q0(),{mutateAsync:a}=Z0(),c=(0,qi.useMemo)(()=>n.filter(l=>G0(l)).map(l=>{let m=n.findIndex(g=>(0,Kl.default)(g.identifier,l.identifier)),u=i?.some(g=>(0,Kl.default)(g.accountHash,l.identifier))||!1;return{type:"check",active:u,start:qi.default.createElement(e,{size:"small",accountIcon:l.icon,accountIndex:m,accountName:l.name}),topLeft:{text:l.name,font:"body"},...Fo(async()=>{try{let g=i?.length??0;u?(await a(l.identifier),g--):(await s(l.identifier),g++),t.success(o("settingsManageUserProfileUpdateLinkedWalletsToast")),r.onLinkedWalletsUpdated(g,n.length)}catch(g){$.captureError(g,"account"),t.error(o("settingsManageUserProfileUpdateLinkedWalletsToastFailure"))}})}}),[e,n,r,a,i,o,t,s]);return qi.default.createElement(M,{width:"100%"},qi.default.createElement(z,{rows:c}))};d();p();var Gn=A(C()),Jl=({getAuthFactorRow:e,toastController:t})=>{let{t:o}=S(),r=hr(),{data:n=[]}=te(),{data:i=[]}=Do("all"),{data:s}=aa(),a=Object.values(s||{}).filter(h=>h?.isExistingAuthFactor).length,{mutateAsync:c}=j0(),{mutateAsync:l}=K0(),[m,u]=(0,Gn.useMemo)(()=>{let h=Object.keys(s||{}),w=new Set,T=new Set;for(let v of n)v.type==="seed"||v.type==="seedless"?v.seedIdentifier&&h.includes(v.seedIdentifier)&&w.add(v.seedIdentifier):v.type==="privateKey"&&v.privateKeyIdentifier&&h.includes(v.privateKeyIdentifier)&&T.add(v.privateKeyIdentifier);return[[...w],[...T]]},[n,s]),g=(0,Gn.useCallback)(async h=>{try{let w=a;s?.[h]?.isExistingAuthFactor?(await l({authenticationPublicKey:s[h]?.authenticationPublicKey}),w--):(await c({secretIdentifier:h}),w++),t.success(o("settingsManageUserProfileUpdateAuthFactorsToast")),r.onAuthFactorsUpdated(w,m.length+u.length)}catch(w){$.captureError(w,"account"),t.error(o("settingsManageUserProfileUpdateAuthFactorsToastFailure"))}},[c,s,r,l,o,t,a,u.length,m.length]),x=m.map(h=>{let w=bl(n,h).length,T=s?.[h]?.isExistingAuthFactor??!1,v=T&&a===1,I=i.findIndex(N=>N.identifier===h);return e({accountTypePrefix:o("addAccountSecretPhraseDefaultLabel",{number:I+1}),numberOfAccountsText:w===1?o("settingsClaimUsernameNoOfAccountsSingular"):w>0?o("settingsClaimUsernameNoOfAccounts",{noOfAccounts:w}):o("settingsClaimUsernameEmptyAccounts"),secretId:h,isActive:T,accounts:n,onSelect:g,disabled:v})}),y=u.map((h,w)=>{let T=bl(n,h).length,v=s?.[h]?.isExistingAuthFactor??!1,I=v&&a===1;return e({accountTypePrefix:o("addAccountPrivateKeyDefaultLabel",{number:w+1}),numberOfAccountsText:T?o("settingsClaimUsernameNoOfAccounts",{noOfAccounts:T}):o("settingsClaimUsernameEmptyAccounts"),secretId:h,isActive:v,accounts:n,onSelect:g,disabled:I})}),b=[...x,...y];return Gn.default.createElement(M,{width:"100%"},Gn.default.createElement(z,{rows:b}))};d();p();var tr=A(C());var Xl=({navigateToAuthFactors:e,navigateToEditBiography:t,navigateToSyncedAccounts:o,navigateToPrivacyState:r,navigateToPublicAddresses:n})=>{let{t:i}=S(),s=qr(),{data:a,isPending:c}=ca(),l=a?.biography,{data:m,isPending:u}=aa(),g=u?void 0:Object.values(m||{}).filter(I=>I?.isExistingAuthFactor)?.length?.toString(),{data:x,isPending:y}=la(),b=y?void 0:x?.length?.toString()||"0",{currentPrivacyState:h}=tc(),w=h?.icon,{data:[T]}=He(["enable-social-profiles"]),v=[{topLeft:{text:i("settingsManageUserProfileAboutUsername")},topRight:s&&{text:`@${s}`}}];return T&&t&&v.push({type:"drawer",topLeft:tr.default.createElement(M,{flex:1,width:96},tr.default.createElement(R,{font:"caption",children:i("settingsManageUserProfileAboutBio")})),topRight:{text:l??""},disabled:c,...Fo(t)}),tr.default.createElement(M,{gap:24,width:"100%"},tr.default.createElement(M,{gap:"section",width:"100%"},tr.default.createElement(R,{font:"bodySemibold",color:"textSecondary",children:i("settingsManageUserProfileAbout")}),tr.default.createElement(z,{rows:v})),tr.default.createElement(M,{gap:"section",width:"100%"},tr.default.createElement(R,{font:"bodySemibold",color:"textSecondary",children:i("settingsManageUserProfileManage")}),tr.default.createElement(z,{rows:[{type:"drawer",topLeft:{text:i("settingsManageUserProfileAuthFactors")},topRight:g&&{text:g},disabled:u||!g,...Fo(e)},{type:"drawer",topLeft:{text:i("settingsManageUserProfileLinkedWallets")},topRight:b&&{text:b},disabled:y,...Fo(o)},T?{type:"drawer",topLeft:{text:i("settingsManageUserProfilePrivacy")},topRight:!c&&{text:h.title,before:tr.default.createElement(w,{size:16,color:"textSecondary"})},disabled:c,...Fo(r)}:{type:"drawer",topLeft:{text:i("settingsClaimUsernameManageAddressesTitle")},...Fo(n)}]})))};var $o=A(C());d();p();var Rn=A(C());var k1=()=>{let{t:e}=S();return Rn.default.createElement(Rn.default.Fragment,null,Rn.default.createElement(O,null,e("settingsManageUserProfileAuthFactors")),Rn.default.createElement(M,{gap:24,paddingTop:20,direction:"column",alignItems:"center",overflow:"auto"},Rn.default.createElement(R,{align:"center",font:"caption",color:"textSecondary",children:e("settingsManageUserProfileAuthFactorsDescription")}),Rn.default.createElement(Jl,{getAuthFactorRow:$u,toastController:Ft})))};d();p();var Xr=A(C());var S1=()=>{let{t:e}=S();return Xr.default.createElement(Xr.default.Fragment,null,Xr.default.createElement(O,null,e("settingsManageUserProfileLinkedWallets")),Xr.default.createElement(M,{gap:8,paddingTop:20,direction:"column",alignItems:"center",overflow:"auto"},Xr.default.createElement(R,{align:"center",font:"caption",color:"textSecondary",children:e("settingsManageUserProfileLinkedWalletsDescription")}),Xr.default.createElement(M,{marginBottom:16},Xr.default.createElement(zu,{leftIcon:"eye-off",text:e("settingsClaimUsernameLinkWalletsBadge"),onClick:()=>Ar({url:z0})})),Xr.default.createElement(Yl,{AccountAvatar:$e,toastController:Ft})))};d();p();var zn=A(C());d();p();var ji=A(C());var oc=()=>{let{t:e}=S();return ji.default.createElement(ji.default.Fragment,null,ji.default.createElement(O,null,e("settingsManageUserProfilePublicAddresses")),ji.default.createElement(M,{paddingTop:20,overflow:"auto"},ji.default.createElement(jl,{AccountAvatar:$e,toastController:Ft})))};var A1=()=>{let{t:e}=S(),{pushDetailViewCallback:t}=U();return zn.default.createElement(zn.default.Fragment,null,zn.default.createElement(O,null,e("settingsManageUserProfilePrivacy")),zn.default.createElement(M,{marginTop:20,overflow:"auto"},zn.default.createElement(ql,{navigateToPublicAddresses:t(zn.default.createElement(oc,null)),toastController:Ft})))};d();p();var rc=A(C());var w1=()=>{let{t:e}=S(),{popDetailView:t}=U(),o=hr(),r=qr(),n=un(),{data:i=""}=Go(),{mutateAsync:s}=xi(),a=(0,rc.useCallback)(async c=>{try{await s({icon:c.imageUrl||c.unicode||null}),Ft.success(e("settingsUpdateAvatarToast")),o.onAvatarUpdated(c.type==="image"?"collectible":c.type==="emoji"?"emoji":"default")}catch(l){$.captureError(l,"account"),Ft.error(e("settingsUpdateAvatarToastFailure"))}t()},[o,s,t,e]);return rc.default.createElement($l,{accountIndex:0,accountName:r||"",accountIdentifier:i,currentIcon:n,onPrimaryClicked:a})};var b1=()=>{let{t:e}=S(),{pushDetailViewCallback:t}=U(),o=qr(),r=un();return $o.default.createElement($o.default.Fragment,null,$o.default.createElement(O,null,e("settingsManageUserProfileTitle")),$o.default.createElement(za,null,$o.default.createElement(Dr,{accountIndex:0,accountName:`@${o}`,accountIcon:r,onClick:t($o.default.createElement(w1,null))})),$o.default.createElement(Xl,{navigateToAuthFactors:t($o.default.createElement(k1,null)),navigateToSyncedAccounts:t($o.default.createElement(S1,null)),navigateToPrivacyState:t($o.default.createElement(A1,null)),navigateToPublicAddresses:t($o.default.createElement(oc,null))}))};d();p();var so=A(C()),v1=({width:e=36,height:t=36})=>so.createElement("svg",{xmlns:"http://www.w3.org/2000/svg",width:e,height:t,fill:"none"},so.createElement("path",{fill:"#AB9FF2",d:"M35.99 17.998a5.993 5.993 0 0 0-1.957-4.434 2.134 2.134 0 0 1 .004-3.136 5.984 5.984 0 0 0 1.914-5.111C35.648 2.566 33.425.342 30.673.039a5.985 5.985 0 0 0-5.111 1.915 2.131 2.131 0 0 1-3.136.003A5.987 5.987 0 0 0 17.992 0a5.987 5.987 0 0 0-4.435 1.957 2.134 2.134 0 0 1-3.135-.003A5.975 5.975 0 0 0 5.317.042C2.566.345.342 2.57.039 5.32a5.985 5.985 0 0 0 1.915 5.112c.914.836.92 2.298.003 3.135A5.987 5.987 0 0 0 0 18.002c0 1.758.755 3.337 1.957 4.434a2.134 2.134 0 0 1-.003 3.136 5.984 5.984 0 0 0-1.915 5.111c.303 2.751 2.527 4.975 5.278 5.278a5.984 5.984 0 0 0 5.111-1.914 2.131 2.131 0 0 1 3.136-.004A5.987 5.987 0 0 0 17.998 36a5.987 5.987 0 0 0 4.435-1.957 2.134 2.134 0 0 1 3.135.004 5.985 5.985 0 0 0 5.112 1.914c2.751-.303 4.975-2.527 5.278-5.278a5.984 5.984 0 0 0-1.915-5.111 2.131 2.131 0 0 1-.003-3.136 5.987 5.987 0 0 0 1.957-4.434l-.007-.004Z"}),so.createElement("rect",{width:23.698,height:23.698,x:6.04,y:5.935,fill:"#3C315B",rx:4}),so.createElement("mask",{id:"a",width:24,height:25,x:6,y:5,maskUnits:"userSpaceOnUse",style:{maskType:"alpha"}},so.createElement("rect",{width:23.698,height:23.698,x:6.04,y:5.936,fill:"#AB9FF2",rx:4})),so.createElement("g",{mask:"url(#a)"},so.createElement("path",{fill:"#FFFDF8",d:"M17.006 15.197c-9.211 0-19.14 11.213-19.14 18.458 0 2.844 1.536 3.5 3.132 3.5 3.378 0 5.916-2.927 7.43-5.24a4.494 4.494 0 0 0-.286 1.515c0 1.35.778 2.312 2.313 2.312 2.109 0 4.36-1.841 5.527-3.826a2.909 2.909 0 0 0-.123.797c0 .942.532 1.535 1.617 1.535 3.419 0 6.857-6.037 6.857-11.316 0-4.114-2.088-7.735-7.327-7.735Z"}),so.createElement("path",{fill:"#FFFDF8",d:"M10.364 9.22c1.616.758 2.305.655 3.786-.612.127-.108.311.04.233.186-.902 1.728-.85 2.421.25 3.827.1.127-.038.3-.183.23-1.616-.756-2.304-.653-3.786.614-.127.108-.312-.039-.234-.186.903-1.727.85-2.421-.25-3.827a.151.151 0 0 1 .184-.23V9.22ZM26.078 17.774c.878.068 1.178-.1 1.641-.943.04-.072.15-.036.14.046-.115.955.03 1.266.783 1.723.068.042.035.145-.044.139-.878-.068-1.178.1-1.642.943-.04.072-.15.037-.14-.045.116-.955-.03-1.266-.783-1.724-.068-.04-.034-.144.045-.138ZM21.561 9.02c.659-.3.808-.539.806-1.329 0-.067.094-.085.12-.023.295.733.523.899 1.246.929.065.003.082.09.023.117-.659.3-.808.54-.806 1.33 0 .067-.094.085-.12.023-.295-.733-.523-.899-1.246-.93-.065-.002-.082-.09-.022-.116ZM22.273 13.629c.487.19.687.148 1.096-.251.035-.034.092.005.072.05-.231.522-.203.725.146 1.115.031.036-.006.088-.05.071-.487-.19-.686-.148-1.096.251-.035.034-.092-.005-.072-.05.232-.522.203-.724-.145-1.115-.032-.035.005-.088.049-.07v-.001Z",opacity:.1}),so.createElement("path",{fill:"#FFFDF8",d:"M12.63 21.758c.224-.768.932-1.291 1.702-1.204.866.098 1.485.936 1.378 1.871"}),so.createElement("path",{stroke:"#3C315B",strokeLinecap:"round",strokeMiterlimit:10,d:"M12.63 21.758c.224-.768.932-1.291 1.702-1.204.866.098 1.485.936 1.378 1.871"}),so.createElement("path",{fill:"#FFFDF8",d:"M17.348 21.758c.225-.768.933-1.29 1.702-1.204.867.099 1.485.936 1.378 1.872"}),so.createElement("path",{stroke:"#3C315B",strokeLinecap:"round",strokeMiterlimit:10,d:"M17.348 21.758c.225-.768.933-1.29 1.702-1.204.867.099 1.485.936 1.378 1.872"})),so.createElement("rect",{width:30,height:11.78,x:5.235,y:1.26,fill:"#fff",rx:4.2}),so.createElement("path",{fill:"#3C315B",d:"M12.966 10.986c-2.346 0-3.96-1.433-3.96-3.762v-.005c0-2.21 1.552-3.717 3.789-3.717 2.158 0 3.581 1.336 3.581 3.392V6.9c0 1.38-.632 2.236-1.568 2.236-.537 0-.958-.263-1.16-.672l-.053-.097h-.04c-.237.519-.663.791-1.213.791-.892 0-1.507-.76-1.507-1.924v-.005c0-1.08.641-1.841 1.503-1.841.496 0 .896.25 1.077.663h.035v-.562h1.01v2.013c0 .487.163.756.431.756.347 0 .571-.493.571-1.306v-.004c0-1.635-1.032-2.659-2.68-2.659-1.701 0-2.861 1.182-2.861 2.94v.005c0 1.88 1.23 2.948 3.138 2.948.527 0 1.05-.079 1.485-.228v.79a4.927 4.927 0 0 1-1.578.242Zm-.312-2.734c.409 0 .677-.373.677-.989V7.25c0-.597-.268-.962-.677-.962-.409 0-.663.36-.663.971v.004c0 .624.255.99.663.99Zm5.247 3.2c-.25 0-.505-.031-.654-.058v-.953c.092.017.241.044.43.044.383 0 .593-.11.708-.431l.057-.154-1.666-4.71h1.411l.993 3.682h.035l.998-3.683h1.353L19.95 9.984c-.369 1.111-1.002 1.467-2.048 1.467Zm6.109-1.455c-1.433 0-2.334-.919-2.334-2.452v-.01c0-1.515.923-2.443 2.33-2.443 1.41 0 2.337.919 2.337 2.444v.009c0 1.538-.91 2.452-2.333 2.452Zm.004-1.007c.624 0 1.024-.527 1.024-1.445v-.01c0-.909-.409-1.436-1.033-1.436-.62 0-1.024.527-1.024 1.437v.009c0 .922.4 1.445 1.033 1.445Zm4.597 1.007c-1.037 0-1.626-.664-1.626-1.767V5.19h1.283v2.772c0 .615.29.971.866.971.575 0 .94-.422.94-1.037V5.19h1.283V9.9h-1.283v-.778h-.026c-.242.54-.725.875-1.437.875Z"}));var T1=()=>{let{t:e}=S(),{pushDetailViewCallback:t}=U(),{handleShowModalVisibility:o}=Er(),r=un(),{data:[n]}=He(["enable-manage-user-profile"]),{data:i,isFetched:s}=ca(),a=s&&!i?.username,c=i?.username,{data:l=[]}=te(),{data:m}=ue(),{data:u=""}=Go(),g=m?.name??"",x=l.findIndex(T=>T.identifier===u)??0,y=!fn(),{data:b}=Mp(m?.identifier,y),h=b?.value,w=y?h:void 0;return n&&c?nt.default.createElement(oo,{start:nt.default.createElement(C1,null,nt.default.createElement($e,{testID:"settings-header-manage-profile",size:"medium",accountIndex:x,accountName:`@${c}`,accountIcon:r})),end:nt.default.createElement(J.ChevronRight,{color:"textTertiary",size:18}),topLeft:{text:`@${c}`,font:"bodySemibold"},onClick:t(nt.default.createElement(b1,null))}):nt.default.createElement(nt.default.Fragment,null,nt.default.createElement(Sn,null,a&&nt.default.createElement(Et.div,{key:"claim-username",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"}},nt.default.createElement(oo,{start:nt.default.createElement(v1,null),topLeft:{text:e("settingsClaimUsernameSettingTitle"),font:"bodyMedium"},bottomLeft:{text:e("settingsClaimUsernameSettingDescription")},end:nt.default.createElement(J.ChevronRight,{color:"textTertiary",size:18}),onClick:()=>o("claimUsername")}))),nt.default.createElement(oo,{start:nt.default.createElement(C1,null,nt.default.createElement($e,{size:"medium",accountIndex:x,accountName:g,accountIcon:m?.icon})),end:nt.default.createElement(J.ChevronRight,{color:"textTertiary",size:18}),topLeft:{text:g,font:"bodySemibold"},bottomLeft:w?nt.default.createElement(Ir,{value:w,font:"caption"}):void 0,onClick:t(nt.default.createElement($i,{accountIdentifier:u}))}))},C1=f.div`
  ${Ss} {
    background: ${Ht.colors.legacy.textPrimary};
    p {
      color: ${Ht.colors.legacy.bgButton};
    }
  }
`;d();p();var Ql=A(C());var nc=({value:e,onChange:t})=>{let{t:o}=S();return Ql.default.createElement(L3,null,Ql.default.createElement(pu,{placeholder:o("assetListSearch"),value:e,onChange:r=>{"value"in r.target&&typeof r.target.value=="string"&&t(r.target.value)},showClearIcon:!!e,onClear:()=>{t("")}}))},L3=f.div`
  padding: 14px 0 14px;
  background: #222;
  position: relative;
  border-bottom: none;

  &:after {
    content: "";
    position: absolute;
    bottom: 0;
    left: -20px;
    width: calc(100% + 40px);
    border-bottom: 1px solid #323232; // #474747
  }
`;d();p();var Vc=A(C());d();p();var ht=A(C());var D1=({description:e,rightNode:t,icon:o,title:r,secondaryTitle:n,type:i,onClick:s,testID:a,enabled:c=!0,stackTitleDescription:l,loading:m,...u})=>c?ht.default.createElement(V3,{onClick:s,"data-testid":a,style:l?{height:"unset"}:void 0,...u,...s&&{role:"button"}},O3({type:i,title:r,secondaryTitle:n,icon:o,maxWidth:t===null?void 0:"260px",description:e,stackTitleDescription:l}),W3(t,e,l,m)):ht.default.createElement(ht.default.Fragment,null),O3=e=>{let{icon:t,type:o,maxWidth:r,title:n,secondaryTitle:i,stackTitleDescription:s,description:a}=e,c=s?H3:Nt;return t?ht.default.createElement(P1,null,ht.default.createElement(U3,null,t),ht.default.createElement(c,null,ht.default.createElement(I1,{color:o==="alert"?"#EB3742":"#FFFFFF",maxWidth:r},n),s&&a&&ht.default.createElement(E1,null,a)),i&&i!=="string"&&ht.default.createElement(ht.default.Fragment,null,i)):ht.default.createElement(I1,{color:o==="alert"?"#EB3742":"#FFFFFF",maxWidth:r},n)},W3=(e,t,o,r)=>{if(e||e===null)return e;let n=r?ht.default.createElement(Pt,{diameter:20}):ht.default.createElement(Fa,{fill:"#777",height:12});return t&&!o?ht.default.createElement(P1,null,ht.default.createElement(E1,null,t),ht.default.createElement(Zl,null,n)):ht.default.createElement(Zl,null,n)},I1=f(E).attrs({size:16,weight:500,lineHeight:19,noWrap:!0})``,E1=f(E).attrs({size:14,weight:500,lineHeight:17,whiteSpace:"nowrap",color:"#777"})``,Zl=f.div`
  margin-left: 12px;
`,U3=f.div`
  width: 24px;
  height: 24px;
  margin-right: 8px;
  display: flex;
  justify-content: center;
  align-items: center;
`,V3=f.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #2a2a2a;
  border-radius: 12px;
  padding: 14px 15px;
  height: 47px;
  width: 100%;
  margin-bottom: 10px;

  &:last-of-type {
    margin-bottom: 0;
  }

  ${e=>e.onClick?`
  &:hover {
    background: #333;
    cursor: pointer;
    ${Zl} {
      path {
        fill: #fff;
      }
    }
  }`:""}
`,P1=f(xt)`
  flex-shrink: 1;
  width: auto;
  align-self: center;
`,H3=f(Nt)`
  gap: 4px;
  margin-left: 4px;
`;d();p();var Q=A(C());d();p();var or=A(C());var _3=_e({display:"flex",flexDirection:"column",gap:8,alignItems:"center",marginBottom:16}),G3=_e({marginBottom:8}),ic=()=>{let{t:e}=S(),{version:t}=chrome.runtime.getManifest(),o=(0,or.useMemo)(()=>[{topLeft:{text:e("termsOfServicePrimaryText")},type:"link",onClick:()=>self.open(g0,"_blank")},{topLeft:{text:e("aboutPrivacyPolicy")},type:"link",onClick:()=>self.open(x0,"_blank")},{topLeft:{text:e("aboutVisitWebsite")},type:"link",onClick:()=>self.open(f0,"_blank")}],[e]);return or.default.createElement(or.default.Fragment,null,or.default.createElement(O,null,e("settingsAbout")),or.default.createElement(K,null,or.default.createElement("div",{className:_3},or.default.createElement(Fm,{width:200}),or.default.createElement(R,{className:G3,font:"captionSemibold",color:"textSecondary"},e("aboutVersion",{version:t}))),or.default.createElement(z,{rows:o})))};d();p();d();p();var D=A(C()),Cn=A(t3());d();p();var je=A(C());function F1(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,je.useMemo)(()=>r=>{t.forEach(n=>n(r))},t)}var Cs=typeof self<"u"&&typeof self.document<"u"&&typeof self.document.createElement<"u";function $n(e){let t=Object.prototype.toString.call(e);return t==="[object Window]"||t==="[object global]"}function sc(e){return"nodeType"in e}function qt(e){var t,o;return e?$n(e)?e:sc(e)&&(t=(o=e.ownerDocument)==null?void 0:o.defaultView)!=null?t:self:self}function ac(e){let{Document:t}=qt(e);return e instanceof t}function Ki(e){return $n(e)?!1:e instanceof qt(e).HTMLElement}function N1(e){return e instanceof qt(e).SVGElement}function qn(e){return e?$n(e)?e.document:sc(e)?ac(e)?e:Ki(e)?e.ownerDocument:document:document:document}var ho=Cs?je.useLayoutEffect:je.useEffect;function Ts(e){let t=(0,je.useRef)(e);return ho(()=>{t.current=e}),(0,je.useCallback)(function(){for(var o=arguments.length,r=new Array(o),n=0;n<o;n++)r[n]=arguments[n];return t.current==null?void 0:t.current(...r)},[])}function B1(){let e=(0,je.useRef)(null),t=(0,je.useCallback)((r,n)=>{e.current=setInterval(r,n)},[]),o=(0,je.useCallback)(()=>{e.current!==null&&(clearInterval(e.current),e.current=null)},[]);return[t,o]}function Yi(e,t){t===void 0&&(t=[e]);let o=(0,je.useRef)(e);return ho(()=>{o.current!==e&&(o.current=e)},t),o}function Ji(e,t){let o=(0,je.useRef)();return(0,je.useMemo)(()=>{let r=e(o.current);return o.current=r,r},[...t])}function Is(e){let t=Ts(e),o=(0,je.useRef)(null),r=(0,je.useCallback)(n=>{n!==o.current&&t?.(n,o.current),o.current=n},[]);return[o,r]}function Ds(e){let t=(0,je.useRef)();return(0,je.useEffect)(()=>{t.current=e},[e]),t.current}var ed={};function jn(e,t){return(0,je.useMemo)(()=>{if(t)return t;let o=ed[e]==null?0:ed[e]+1;return ed[e]=o,e+"-"+o},[e,t])}function L1(e){return function(t){for(var o=arguments.length,r=new Array(o>1?o-1:0),n=1;n<o;n++)r[n-1]=arguments[n];return r.reduce((i,s)=>{let a=Object.entries(s);for(let[c,l]of a){let m=i[c];m!=null&&(i[c]=m+e*l)}return i},{...t})}}var Kn=L1(1),Xi=L1(-1);function R3(e){return"clientX"in e&&"clientY"in e}function Qi(e){if(!e)return!1;let{KeyboardEvent:t}=qt(e.target);return t&&e instanceof t}function z3(e){if(!e)return!1;let{TouchEvent:t}=qt(e.target);return t&&e instanceof t}function Es(e){if(z3(e)){if(e.touches&&e.touches.length){let{clientX:t,clientY:o}=e.touches[0];return{x:t,y:o}}else if(e.changedTouches&&e.changedTouches.length){let{clientX:t,clientY:o}=e.changedTouches[0];return{x:t,y:o}}}return R3(e)?{x:e.clientX,y:e.clientY}:null}var rr=Object.freeze({Translate:{toString(e){if(!e)return;let{x:t,y:o}=e;return"translate3d("+(t?Math.round(t):0)+"px, "+(o?Math.round(o):0)+"px, 0)"}},Scale:{toString(e){if(!e)return;let{scaleX:t,scaleY:o}=e;return"scaleX("+t+") scaleY("+o+")"}},Transform:{toString(e){if(e)return[rr.Translate.toString(e),rr.Scale.toString(e)].join(" ")}},Transition:{toString(e){let{property:t,duration:o,easing:r}=e;return t+" "+o+"ms "+r}}}),M1="a,frame,iframe,input:not([type=hidden]):not(:disabled),select:not(:disabled),textarea:not(:disabled),button:not(:disabled),*[tabindex]";function O1(e){return e.matches(M1)?e:e.querySelector(M1)}d();p();var Yn=A(C()),$3={display:"none"};function W1(e){let{id:t,value:o}=e;return Yn.default.createElement("div",{id:t,style:$3},o)}var q3={position:"fixed",width:1,height:1,margin:-1,border:0,padding:0,overflow:"hidden",clip:"rect(0 0 0 0)",clipPath:"inset(100%)",whiteSpace:"nowrap"};function U1(e){let{id:t,announcement:o}=e;return Yn.default.createElement("div",{id:t,style:q3,role:"status","aria-live":"assertive","aria-atomic":!0},o)}function V1(){let[e,t]=(0,Yn.useState)("");return{announce:(0,Yn.useCallback)(r=>{r!=null&&t(r)},[]),announcement:e}}var X1=(0,D.createContext)(null);function j3(e){let t=(0,D.useContext)(X1);(0,D.useEffect)(()=>{if(!t)throw new Error("useDndMonitor must be used within a children of <DndContext>");return t(e)},[e,t])}function K3(){let[e]=(0,D.useState)(()=>new Set),t=(0,D.useCallback)(r=>(e.add(r),()=>e.delete(r)),[e]);return[(0,D.useCallback)(r=>{let{type:n,event:i}=r;e.forEach(s=>{var a;return(a=s[n])==null?void 0:a.call(s,i)})},[e]),t]}var Y3={draggable:`
    To pick up a draggable item, press the space bar.
    While dragging, use the arrow keys to move the item.
    Press space again to drop the item in its new position, or press escape to cancel.
  `},J3={onDragStart(e){let{active:t}=e;return"Picked up draggable item "+t.id+"."},onDragOver(e){let{active:t,over:o}=e;return o?"Draggable item "+t.id+" was moved over droppable area "+o.id+".":"Draggable item "+t.id+" is no longer over a droppable area."},onDragEnd(e){let{active:t,over:o}=e;return o?"Draggable item "+t.id+" was dropped over droppable area "+o.id:"Draggable item "+t.id+" was dropped."},onDragCancel(e){let{active:t}=e;return"Dragging was cancelled. Draggable item "+t.id+" was dropped."}};function X3(e){let{announcements:t=J3,container:o,hiddenTextDescribedById:r,screenReaderInstructions:n=Y3}=e,{announce:i,announcement:s}=V1(),a=jn("DndLiveRegion"),[c,l]=(0,D.useState)(!1);if((0,D.useEffect)(()=>{l(!0)},[]),j3((0,D.useMemo)(()=>({onDragStart(u){let{active:g}=u;i(t.onDragStart({active:g}))},onDragMove(u){let{active:g,over:x}=u;t.onDragMove&&i(t.onDragMove({active:g,over:x}))},onDragOver(u){let{active:g,over:x}=u;i(t.onDragOver({active:g,over:x}))},onDragEnd(u){let{active:g,over:x}=u;i(t.onDragEnd({active:g,over:x}))},onDragCancel(u){let{active:g,over:x}=u;i(t.onDragCancel({active:g,over:x}))}}),[i,t])),!c)return null;let m=D.default.createElement(D.default.Fragment,null,D.default.createElement(W1,{id:r,value:n.draggable}),D.default.createElement(U1,{id:a,announcement:s}));return o?(0,Cn.createPortal)(m,o):m}var it;(function(e){e.DragStart="dragStart",e.DragMove="dragMove",e.DragEnd="dragEnd",e.DragCancel="dragCancel",e.DragOver="dragOver",e.RegisterDroppable="registerDroppable",e.SetDroppableDisabled="setDroppableDisabled",e.UnregisterDroppable="unregisterDroppable"})(it||(it={}));function lc(){}function Q1(e,t){return(0,D.useMemo)(()=>({sensor:e,options:t??{}}),[e,t])}function Z1(){for(var e=arguments.length,t=new Array(e),o=0;o<e;o++)t[o]=arguments[o];return(0,D.useMemo)(()=>[...t].filter(r=>r!=null),[...t])}var ir=Object.freeze({x:0,y:0});function Q3(e,t){return Math.sqrt(Math.pow(e.x-t.x,2)+Math.pow(e.y-t.y,2))}function Z3(e,t){let o=Es(e);if(!o)return"0 0";let r={x:(o.x-t.left)/t.width*100,y:(o.y-t.top)/t.height*100};return r.x+"% "+r.y+"%"}function ex(e,t){let{data:{value:o}}=e,{data:{value:r}}=t;return o-r}function tx(e,t){let{data:{value:o}}=e,{data:{value:r}}=t;return r-o}function ef(e,t){if(!e||e.length===0)return null;let[o]=e;return t?o[t]:o}function H1(e,t,o){return t===void 0&&(t=e.left),o===void 0&&(o=e.top),{x:t+e.width*.5,y:o+e.height*.5}}var tf=e=>{let{collisionRect:t,droppableRects:o,droppableContainers:r}=e,n=H1(t,t.left,t.top),i=[];for(let s of r){let{id:a}=s,c=o.get(a);if(c){let l=Q3(H1(c),n);i.push({id:a,data:{droppableContainer:s,value:l}})}}return i.sort(ex)};function ox(e,t){let o=Math.max(t.top,e.top),r=Math.max(t.left,e.left),n=Math.min(t.left+t.width,e.left+e.width),i=Math.min(t.top+t.height,e.top+e.height),s=n-r,a=i-o;if(r<n&&o<i){let c=t.width*t.height,l=e.width*e.height,m=s*a,u=m/(c+l-m);return Number(u.toFixed(4))}return 0}var rx=e=>{let{collisionRect:t,droppableRects:o,droppableContainers:r}=e,n=[];for(let i of r){let{id:s}=i,a=o.get(s);if(a){let c=ox(a,t);c>0&&n.push({id:s,data:{droppableContainer:i,value:c}})}}return n.sort(tx)};function nx(e,t,o){return{...e,scaleX:t&&o?t.width/o.width:1,scaleY:t&&o?t.height/o.height:1}}function of(e,t){return e&&t?{x:e.left-t.left,y:e.top-t.top}:ir}function ix(e){return function(o){for(var r=arguments.length,n=new Array(r>1?r-1:0),i=1;i<r;i++)n[i-1]=arguments[i];return n.reduce((s,a)=>({...s,top:s.top+e*a.y,bottom:s.bottom+e*a.y,left:s.left+e*a.x,right:s.right+e*a.x}),{...o})}}var sx=ix(1);function rf(e){if(e.startsWith("matrix3d(")){let t=e.slice(9,-1).split(/, /);return{x:+t[12],y:+t[13],scaleX:+t[0],scaleY:+t[5]}}else if(e.startsWith("matrix(")){let t=e.slice(7,-1).split(/, /);return{x:+t[4],y:+t[5],scaleX:+t[0],scaleY:+t[3]}}return null}function ax(e,t,o){let r=rf(t);if(!r)return e;let{scaleX:n,scaleY:i,x:s,y:a}=r,c=e.left-s-(1-n)*parseFloat(o),l=e.top-a-(1-i)*parseFloat(o.slice(o.indexOf(" ")+1)),m=n?e.width/n:e.width,u=i?e.height/i:e.height;return{width:m,height:u,top:l,right:c+m,bottom:l+u,left:c}}var cx={ignoreTransform:!1};function Qn(e,t){t===void 0&&(t=cx);let o=e.getBoundingClientRect();if(t.ignoreTransform){let{transform:l,transformOrigin:m}=qt(e).getComputedStyle(e);l&&(o=ax(o,l,m))}let{top:r,left:n,width:i,height:s,bottom:a,right:c}=o;return{top:r,left:n,width:i,height:s,bottom:a,right:c}}function _1(e){return Qn(e,{ignoreTransform:!0})}function lx(e){let t=e.innerWidth,o=e.innerHeight;return{top:0,left:0,right:t,bottom:o,width:t,height:o}}function dx(e,t){return t===void 0&&(t=qt(e).getComputedStyle(e)),t.position==="fixed"}function px(e,t){t===void 0&&(t=qt(e).getComputedStyle(e));let o=/(auto|scroll|overlay)/;return["overflow","overflowX","overflowY"].some(n=>{let i=t[n];return typeof i=="string"?o.test(i):!1})}function mc(e,t){let o=[];function r(n){if(t!=null&&o.length>=t||!n)return o;if(ac(n)&&n.scrollingElement!=null&&!o.includes(n.scrollingElement))return o.push(n.scrollingElement),o;if(!Ki(n)||N1(n)||o.includes(n))return o;let i=qt(e).getComputedStyle(n);return n!==e&&px(n,i)&&o.push(n),dx(n,i)?o:r(n.parentNode)}return e?r(e):o}function nf(e){let[t]=mc(e,1);return t??null}function td(e){return!Cs||!e?null:$n(e)?e:sc(e)?ac(e)||e===qn(e).scrollingElement?self:Ki(e)?e:null:null}function sf(e){return $n(e)?e.scrollX:e.scrollLeft}function af(e){return $n(e)?e.scrollY:e.scrollTop}function id(e){return{x:sf(e),y:af(e)}}var kt;(function(e){e[e.Forward=1]="Forward",e[e.Backward=-1]="Backward"})(kt||(kt={}));function cf(e){return!Cs||!e?!1:e===document.scrollingElement}function lf(e){let t={x:0,y:0},o=cf(e)?{height:self.innerHeight,width:self.innerWidth}:{height:e.clientHeight,width:e.clientWidth},r={x:e.scrollWidth-o.width,y:e.scrollHeight-o.height},n=e.scrollTop<=t.y,i=e.scrollLeft<=t.x,s=e.scrollTop>=r.y,a=e.scrollLeft>=r.x;return{isTop:n,isLeft:i,isBottom:s,isRight:a,maxScroll:r,minScroll:t}}var mx={x:.2,y:.2};function ux(e,t,o,r,n){let{top:i,left:s,right:a,bottom:c}=o;r===void 0&&(r=10),n===void 0&&(n=mx);let{isTop:l,isBottom:m,isLeft:u,isRight:g}=lf(e),x={x:0,y:0},y={x:0,y:0},b={height:t.height*n.y,width:t.width*n.x};return!l&&i<=t.top+b.height?(x.y=kt.Backward,y.y=r*Math.abs((t.top+b.height-i)/b.height)):!m&&c>=t.bottom-b.height&&(x.y=kt.Forward,y.y=r*Math.abs((t.bottom-b.height-c)/b.height)),!g&&a>=t.right-b.width?(x.x=kt.Forward,y.x=r*Math.abs((t.right-b.width-a)/b.width)):!u&&s<=t.left+b.width&&(x.x=kt.Backward,y.x=r*Math.abs((t.left+b.width-s)/b.width)),{direction:x,speed:y}}function fx(e){if(e===document.scrollingElement){let{innerWidth:i,innerHeight:s}=self;return{top:0,left:0,right:i,bottom:s,width:i,height:s}}let{top:t,left:o,right:r,bottom:n}=e.getBoundingClientRect();return{top:t,left:o,right:r,bottom:n,width:e.clientWidth,height:e.clientHeight}}function df(e){return e.reduce((t,o)=>Kn(t,id(o)),ir)}function gx(e){return e.reduce((t,o)=>t+sf(o),0)}function xx(e){return e.reduce((t,o)=>t+af(o),0)}function pf(e,t){if(t===void 0&&(t=Qn),!e)return;let{top:o,left:r,bottom:n,right:i}=t(e);nf(e)&&(n<=0||i<=0||o>=self.innerHeight||r>=self.innerWidth)&&e.scrollIntoView({block:"center",inline:"center"})}var yx=[["x",["left","right"],gx],["y",["top","bottom"],xx]],Ms=class{constructor(t,o){this.rect=void 0,this.width=void 0,this.height=void 0,this.top=void 0,this.bottom=void 0,this.right=void 0,this.left=void 0;let r=mc(o),n=df(r);this.rect={...t},this.width=t.width,this.height=t.height;for(let[i,s,a]of yx)for(let c of s)Object.defineProperty(this,c,{get:()=>{let l=a(r),m=n[i]-l;return this.rect[c]+m},enumerable:!0});Object.defineProperty(this,"rect",{enumerable:!1})}},Jn=class{constructor(t){this.target=void 0,this.listeners=[],this.removeAll=()=>{this.listeners.forEach(o=>{var r;return(r=this.target)==null?void 0:r.removeEventListener(...o)})},this.target=t}add(t,o,r){var n;(n=this.target)==null||n.addEventListener(t,o,r),this.listeners.push([t,o,r])}};function hx(e){let{EventTarget:t}=qt(e);return e instanceof t?e:qn(e)}function od(e,t){let o=Math.abs(e.x),r=Math.abs(e.y);return typeof t=="number"?Math.sqrt(o**2+r**2)>t:"x"in t&&"y"in t?o>t.x&&r>t.y:"x"in t?o>t.x:"y"in t?r>t.y:!1}var nr;(function(e){e.Click="click",e.DragStart="dragstart",e.Keydown="keydown",e.ContextMenu="contextmenu",e.Resize="resize",e.SelectionChange="selectionchange",e.VisibilityChange="visibilitychange"})(nr||(nr={}));function G1(e){e.preventDefault()}function kx(e){e.stopPropagation()}var ge;(function(e){e.Space="Space",e.Down="ArrowDown",e.Right="ArrowRight",e.Left="ArrowLeft",e.Up="ArrowUp",e.Esc="Escape",e.Enter="Enter"})(ge||(ge={}));var mf={start:[ge.Space,ge.Enter],cancel:[ge.Esc],end:[ge.Space,ge.Enter]},Sx=(e,t)=>{let{currentCoordinates:o}=t;switch(e.code){case ge.Right:return{...o,x:o.x+25};case ge.Left:return{...o,x:o.x-25};case ge.Down:return{...o,y:o.y+25};case ge.Up:return{...o,y:o.y-25}}},dc=class{constructor(t){this.props=void 0,this.autoScrollEnabled=!1,this.referenceCoordinates=void 0,this.listeners=void 0,this.windowListeners=void 0,this.props=t;let{event:{target:o}}=t;this.props=t,this.listeners=new Jn(qn(o)),this.windowListeners=new Jn(qt(o)),this.handleKeyDown=this.handleKeyDown.bind(this),this.handleCancel=this.handleCancel.bind(this),this.attach()}attach(){this.handleStart(),this.windowListeners.add(nr.Resize,this.handleCancel),this.windowListeners.add(nr.VisibilityChange,this.handleCancel),setTimeout(()=>this.listeners.add(nr.Keydown,this.handleKeyDown))}handleStart(){let{activeNode:t,onStart:o}=this.props,r=t.node.current;r&&pf(r),o(ir)}handleKeyDown(t){if(Qi(t)){let{active:o,context:r,options:n}=this.props,{keyboardCodes:i=mf,coordinateGetter:s=Sx,scrollBehavior:a="smooth"}=n,{code:c}=t;if(i.end.includes(c)){this.handleEnd(t);return}if(i.cancel.includes(c)){this.handleCancel(t);return}let{collisionRect:l}=r.current,m=l?{x:l.left,y:l.top}:ir;this.referenceCoordinates||(this.referenceCoordinates=m);let u=s(t,{active:o,context:r.current,currentCoordinates:m});if(u){let g=Xi(u,m),x={x:0,y:0},{scrollableAncestors:y}=r.current;for(let b of y){let h=t.code,{isTop:w,isRight:T,isLeft:v,isBottom:I,maxScroll:N,minScroll:B}=lf(b),V=fx(b),L={x:Math.min(h===ge.Right?V.right-V.width/2:V.right,Math.max(h===ge.Right?V.left:V.left+V.width/2,u.x)),y:Math.min(h===ge.Down?V.bottom-V.height/2:V.bottom,Math.max(h===ge.Down?V.top:V.top+V.height/2,u.y))},F=h===ge.Right&&!T||h===ge.Left&&!v,P=h===ge.Down&&!I||h===ge.Up&&!w;if(F&&L.x!==u.x){let H=b.scrollLeft+g.x,G=h===ge.Right&&H<=N.x||h===ge.Left&&H>=B.x;if(G&&!g.y){b.scrollTo({left:H,behavior:a});return}G?x.x=b.scrollLeft-H:x.x=h===ge.Right?b.scrollLeft-N.x:b.scrollLeft-B.x,x.x&&b.scrollBy({left:-x.x,behavior:a});break}else if(P&&L.y!==u.y){let H=b.scrollTop+g.y,G=h===ge.Down&&H<=N.y||h===ge.Up&&H>=B.y;if(G&&!g.x){b.scrollTo({top:H,behavior:a});return}G?x.y=b.scrollTop-H:x.y=h===ge.Down?b.scrollTop-N.y:b.scrollTop-B.y,x.y&&b.scrollBy({top:-x.y,behavior:a});break}}this.handleMove(t,Kn(Xi(u,this.referenceCoordinates),x))}}}handleMove(t,o){let{onMove:r}=this.props;t.preventDefault(),r(o)}handleEnd(t){let{onEnd:o}=this.props;t.preventDefault(),this.detach(),o()}handleCancel(t){let{onCancel:o}=this.props;t.preventDefault(),this.detach(),o()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll()}};dc.activators=[{eventName:"onKeyDown",handler:(e,t,o)=>{let{keyboardCodes:r=mf,onActivation:n}=t,{active:i}=o,{code:s}=e.nativeEvent;if(r.start.includes(s)){let a=i.activatorNode.current;return a&&e.target!==a?!1:(e.preventDefault(),n?.({event:e.nativeEvent}),!0)}return!1}}];function R1(e){return!!(e&&"distance"in e)}function z1(e){return!!(e&&"delay"in e)}var Fs=class{constructor(t,o,r){var n;r===void 0&&(r=hx(t.event.target)),this.props=void 0,this.events=void 0,this.autoScrollEnabled=!0,this.document=void 0,this.activated=!1,this.initialCoordinates=void 0,this.timeoutId=null,this.listeners=void 0,this.documentListeners=void 0,this.windowListeners=void 0,this.props=t,this.events=o;let{event:i}=t,{target:s}=i;this.props=t,this.events=o,this.document=qn(s),this.documentListeners=new Jn(this.document),this.listeners=new Jn(r),this.windowListeners=new Jn(qt(s)),this.initialCoordinates=(n=Es(i))!=null?n:ir,this.handleStart=this.handleStart.bind(this),this.handleMove=this.handleMove.bind(this),this.handleEnd=this.handleEnd.bind(this),this.handleCancel=this.handleCancel.bind(this),this.handleKeydown=this.handleKeydown.bind(this),this.removeTextSelection=this.removeTextSelection.bind(this),this.attach()}attach(){let{events:t,props:{options:{activationConstraint:o}}}=this;if(this.listeners.add(t.move.name,this.handleMove,{passive:!1}),this.listeners.add(t.end.name,this.handleEnd),this.windowListeners.add(nr.DragStart,G1),this.windowListeners.add(nr.VisibilityChange,this.handleCancel),this.windowListeners.add(nr.ContextMenu,G1),this.documentListeners.add(nr.Keydown,this.handleKeydown),o){if(R1(o))return;if(z1(o)){this.timeoutId=setTimeout(this.handleStart,o.delay);return}}this.handleStart()}detach(){this.listeners.removeAll(),this.windowListeners.removeAll(),setTimeout(this.documentListeners.removeAll,50),this.timeoutId!==null&&(clearTimeout(this.timeoutId),this.timeoutId=null)}handleStart(){let{initialCoordinates:t}=this,{onStart:o}=this.props;t&&(this.activated=!0,this.documentListeners.add(nr.Click,kx,{capture:!0}),this.removeTextSelection(),this.documentListeners.add(nr.SelectionChange,this.removeTextSelection),o(t))}handleMove(t){var o;let{activated:r,initialCoordinates:n,props:i}=this,{onMove:s,options:{activationConstraint:a}}=i;if(!n)return;let c=(o=Es(t))!=null?o:ir,l=Xi(n,c);if(!r&&a){if(z1(a))return od(l,a.tolerance)?this.handleCancel():void 0;if(R1(a))return a.tolerance!=null&&od(l,a.tolerance)?this.handleCancel():od(l,a.distance)?this.handleStart():void 0}t.cancelable&&t.preventDefault(),s(c)}handleEnd(){let{onEnd:t}=this.props;this.detach(),t()}handleCancel(){let{onCancel:t}=this.props;this.detach(),t()}handleKeydown(t){t.code===ge.Esc&&this.handleCancel()}removeTextSelection(){var t;(t=this.document.getSelection())==null||t.removeAllRanges()}},Ax={move:{name:"pointermove"},end:{name:"pointerup"}},Zi=class extends Fs{constructor(t){let{event:o}=t,r=qn(o.target);super(t,Ax,r)}};Zi.activators=[{eventName:"onPointerDown",handler:(e,t)=>{let{nativeEvent:o}=e,{onActivation:r}=t;return!o.isPrimary||o.button!==0?!1:(r?.({event:o}),!0)}}];var wx={move:{name:"mousemove"},end:{name:"mouseup"}},sd;(function(e){e[e.RightClick=2]="RightClick"})(sd||(sd={}));var ad=class extends Fs{constructor(t){super(t,wx,qn(t.event.target))}};ad.activators=[{eventName:"onMouseDown",handler:(e,t)=>{let{nativeEvent:o}=e,{onActivation:r}=t;return o.button===sd.RightClick?!1:(r?.({event:o}),!0)}}];var rd={move:{name:"touchmove"},end:{name:"touchend"}},cd=class extends Fs{constructor(t){super(t,rd)}static setup(){return self.addEventListener(rd.move.name,t,{capture:!1,passive:!1}),function(){self.removeEventListener(rd.move.name,t)};function t(){}}};cd.activators=[{eventName:"onTouchStart",handler:(e,t)=>{let{nativeEvent:o}=e,{onActivation:r}=t,{touches:n}=o;return n.length>1?!1:(r?.({event:o}),!0)}}];var Ps;(function(e){e[e.Pointer=0]="Pointer",e[e.DraggableRect=1]="DraggableRect"})(Ps||(Ps={}));var pc;(function(e){e[e.TreeOrder=0]="TreeOrder",e[e.ReversedTreeOrder=1]="ReversedTreeOrder"})(pc||(pc={}));function bx(e){let{acceleration:t,activator:o=Ps.Pointer,canScroll:r,draggingRect:n,enabled:i,interval:s=5,order:a=pc.TreeOrder,pointerCoordinates:c,scrollableAncestors:l,scrollableAncestorRects:m,delta:u,threshold:g}=e,x=Cx({delta:u,disabled:!i}),[y,b]=B1(),h=(0,D.useRef)({x:0,y:0}),w=(0,D.useRef)({x:0,y:0}),T=(0,D.useMemo)(()=>{switch(o){case Ps.Pointer:return c?{top:c.y,bottom:c.y,left:c.x,right:c.x}:null;case Ps.DraggableRect:return n}},[o,n,c]),v=(0,D.useRef)(null),I=(0,D.useCallback)(()=>{let B=v.current;if(!B)return;let V=h.current.x*w.current.x,L=h.current.y*w.current.y;B.scrollBy(V,L)},[]),N=(0,D.useMemo)(()=>a===pc.TreeOrder?[...l].reverse():l,[a,l]);(0,D.useEffect)(()=>{if(!i||!l.length||!T){b();return}for(let B of N){if(r?.(B)===!1)continue;let V=l.indexOf(B),L=m[V];if(!L)continue;let{direction:F,speed:P}=ux(B,L,T,t,g);for(let H of["x","y"])x[H][F[H]]||(P[H]=0,F[H]=0);if(P.x>0||P.y>0){b(),v.current=B,y(I,s),h.current=P,w.current=F;return}}h.current={x:0,y:0},w.current={x:0,y:0},b()},[t,I,r,b,i,s,JSON.stringify(T),JSON.stringify(x),y,l,N,m,JSON.stringify(g)])}var vx={x:{[kt.Backward]:!1,[kt.Forward]:!1},y:{[kt.Backward]:!1,[kt.Forward]:!1}};function Cx(e){let{delta:t,disabled:o}=e,r=Ds(t);return Ji(n=>{if(o||!r||!n)return vx;let i={x:Math.sign(t.x-r.x),y:Math.sign(t.y-r.y)};return{x:{[kt.Backward]:n.x[kt.Backward]||i.x===-1,[kt.Forward]:n.x[kt.Forward]||i.x===1},y:{[kt.Backward]:n.y[kt.Backward]||i.y===-1,[kt.Forward]:n.y[kt.Forward]||i.y===1}}},[o,t,r])}function Tx(e,t){let o=t!==null?e.get(t):void 0,r=o?o.node.current:null;return Ji(n=>{var i;return t===null?null:(i=r??n)!=null?i:null},[r,t])}function Ix(e,t){return(0,D.useMemo)(()=>e.reduce((o,r)=>{let{sensor:n}=r,i=n.activators.map(s=>({eventName:s.eventName,handler:t(s.handler,r)}));return[...o,...i]},[]),[e,t])}var Ns;(function(e){e[e.Always=0]="Always",e[e.BeforeDragging=1]="BeforeDragging",e[e.WhileDragging=2]="WhileDragging"})(Ns||(Ns={}));var ld;(function(e){e.Optimized="optimized"})(ld||(ld={}));var $1=new Map;function Dx(e,t){let{dragging:o,dependencies:r,config:n}=t,[i,s]=(0,D.useState)(null),{frequency:a,measure:c,strategy:l}=n,m=(0,D.useRef)(e),u=h(),g=Yi(u),x=(0,D.useCallback)(function(w){w===void 0&&(w=[]),!g.current&&s(T=>T===null?w:T.concat(w.filter(v=>!T.includes(v))))},[g]),y=(0,D.useRef)(null),b=Ji(w=>{if(u&&!o)return $1;if(!w||w===$1||m.current!==e||i!=null){let T=new Map;for(let v of e){if(!v)continue;if(i&&i.length>0&&!i.includes(v.id)&&v.rect.current){T.set(v.id,v.rect.current);continue}let I=v.node.current,N=I?new Ms(c(I),I):null;v.rect.current=N,N&&T.set(v.id,N)}return T}return w},[e,i,o,u,c]);return(0,D.useEffect)(()=>{m.current=e},[e]),(0,D.useEffect)(()=>{u||x()},[o,u]),(0,D.useEffect)(()=>{i&&i.length>0&&s(null)},[JSON.stringify(i)]),(0,D.useEffect)(()=>{u||typeof a!="number"||y.current!==null||(y.current=setTimeout(()=>{x(),y.current=null},a))},[a,u,x,...r]),{droppableRects:b,measureDroppableContainers:x,measuringScheduled:i!=null};function h(){switch(l){case Ns.Always:return!1;case Ns.BeforeDragging:return o;default:return!o}}}function dd(e,t){return Ji(o=>e?o||(typeof t=="function"?t(e):e):null,[t,e])}function Ex(e,t){return dd(e,t)}function Px(e){let{callback:t,disabled:o}=e,r=Ts(t),n=(0,D.useMemo)(()=>{if(o||typeof self>"u"||typeof self.MutationObserver>"u")return;let{MutationObserver:i}=self;return new i(r)},[r,o]);return(0,D.useEffect)(()=>()=>n?.disconnect(),[n]),n}function uc(e){let{callback:t,disabled:o}=e,r=Ts(t),n=(0,D.useMemo)(()=>{if(o||typeof self>"u"||typeof self.ResizeObserver>"u")return;let{ResizeObserver:i}=self;return new i(r)},[o]);return(0,D.useEffect)(()=>()=>n?.disconnect(),[n]),n}function Mx(e){return new Ms(Qn(e),e)}function q1(e,t,o){t===void 0&&(t=Mx);let[r,n]=(0,D.useReducer)(a,null),i=Px({callback(c){if(e)for(let l of c){let{type:m,target:u}=l;if(m==="childList"&&u instanceof HTMLElement&&u.contains(e)){n();break}}}}),s=uc({callback:n});return ho(()=>{n(),e?(s?.observe(e),i?.observe(document.body,{childList:!0,subtree:!0})):(s?.disconnect(),i?.disconnect())},[e]),r;function a(c){if(!e)return null;if(e.isConnected===!1){var l;return(l=c??o)!=null?l:null}let m=t(e);return JSON.stringify(c)===JSON.stringify(m)?c:m}}function Fx(e){let t=dd(e);return of(e,t)}var j1=[];function Nx(e){let t=(0,D.useRef)(e),o=Ji(r=>e?r&&r!==j1&&e&&t.current&&e.parentNode===t.current.parentNode?r:mc(e):j1,[e]);return(0,D.useEffect)(()=>{t.current=e},[e]),o}function Bx(e){let[t,o]=(0,D.useState)(null),r=(0,D.useRef)(e),n=(0,D.useCallback)(i=>{let s=td(i.target);s&&o(a=>a?(a.set(s,id(s)),new Map(a)):null)},[]);return(0,D.useEffect)(()=>{let i=r.current;if(e!==i){s(i);let a=e.map(c=>{let l=td(c);return l?(l.addEventListener("scroll",n,{passive:!0}),[l,id(l)]):null}).filter(c=>c!=null);o(a.length?new Map(a):null),r.current=e}return()=>{s(e),s(i)};function s(a){a.forEach(c=>{let l=td(c);l?.removeEventListener("scroll",n)})}},[n,e]),(0,D.useMemo)(()=>e.length?t?Array.from(t.values()).reduce((i,s)=>Kn(i,s),ir):df(e):ir,[e,t])}function K1(e,t){t===void 0&&(t=[]);let o=(0,D.useRef)(null);return(0,D.useEffect)(()=>{o.current=null},t),(0,D.useEffect)(()=>{let r=e!==ir;r&&!o.current&&(o.current=e),!r&&o.current&&(o.current=null)},[e]),o.current?Xi(e,o.current):ir}function Lx(e){(0,D.useEffect)(()=>{if(!Cs)return;let t=e.map(o=>{let{sensor:r}=o;return r.setup==null?void 0:r.setup()});return()=>{for(let o of t)o?.()}},e.map(t=>{let{sensor:o}=t;return o}))}function Ox(e,t){return(0,D.useMemo)(()=>e.reduce((o,r)=>{let{eventName:n,handler:i}=r;return o[n]=s=>{i(s,t)},o},{}),[e,t])}function uf(e){return(0,D.useMemo)(()=>e?lx(e):null,[e])}var nd=[];function Wx(e,t){t===void 0&&(t=Qn);let[o]=e,r=uf(o?qt(o):null),[n,i]=(0,D.useReducer)(a,nd),s=uc({callback:i});return e.length>0&&n===nd&&i(),ho(()=>{e.length?e.forEach(c=>s?.observe(c)):(s?.disconnect(),i())},[e]),n;function a(){return e.length?e.map(c=>cf(c)?r:new Ms(t(c),c)):nd}}function ff(e){if(!e)return null;if(e.children.length>1)return e;let t=e.children[0];return Ki(t)?t:e}function Ux(e){let{measure:t}=e,[o,r]=(0,D.useState)(null),n=(0,D.useCallback)(l=>{for(let{target:m}of l)if(Ki(m)){r(u=>{let g=t(m);return u?{...u,width:g.width,height:g.height}:g});break}},[t]),i=uc({callback:n}),s=(0,D.useCallback)(l=>{let m=ff(l);i?.disconnect(),m&&i?.observe(m),r(m?t(m):null)},[t,i]),[a,c]=Is(s);return(0,D.useMemo)(()=>({nodeRef:a,rect:o,setRef:c}),[o,a,c])}var Vx=[{sensor:Zi,options:{}},{sensor:dc,options:{}}],Hx={current:{}},cc={draggable:{measure:_1},droppable:{measure:_1,strategy:Ns.WhileDragging,frequency:ld.Optimized},dragOverlay:{measure:Qn}},Xn=class extends Map{get(t){var o;return t!=null&&(o=super.get(t))!=null?o:void 0}toArray(){return Array.from(this.values())}getEnabled(){return this.toArray().filter(t=>{let{disabled:o}=t;return!o})}getNodeFor(t){var o,r;return(o=(r=this.get(t))==null?void 0:r.node.current)!=null?o:void 0}},_x={activatorEvent:null,active:null,activeNode:null,activeNodeRect:null,collisions:null,containerNodeRect:null,draggableNodes:new Map,droppableRects:new Map,droppableContainers:new Xn,over:null,dragOverlay:{nodeRef:{current:null},rect:null,setRef:lc},scrollableAncestors:[],scrollableAncestorRects:[],measuringConfiguration:cc,measureDroppableContainers:lc,windowRect:null,measuringScheduled:!1},gf={activatorEvent:null,activators:[],active:null,activeNodeRect:null,ariaDescribedById:{draggable:""},dispatch:lc,draggableNodes:new Map,over:null,measureDroppableContainers:lc},Bs=(0,D.createContext)(gf),xf=(0,D.createContext)(_x);function Gx(){return{draggable:{active:null,initialCoordinates:{x:0,y:0},nodes:new Map,translate:{x:0,y:0}},droppable:{containers:new Xn}}}function Rx(e,t){switch(t.type){case it.DragStart:return{...e,draggable:{...e.draggable,initialCoordinates:t.initialCoordinates,active:t.active}};case it.DragMove:return e.draggable.active?{...e,draggable:{...e.draggable,translate:{x:t.coordinates.x-e.draggable.initialCoordinates.x,y:t.coordinates.y-e.draggable.initialCoordinates.y}}}:e;case it.DragEnd:case it.DragCancel:return{...e,draggable:{...e.draggable,active:null,initialCoordinates:{x:0,y:0},translate:{x:0,y:0}}};case it.RegisterDroppable:{let{element:o}=t,{id:r}=o,n=new Xn(e.droppable.containers);return n.set(r,o),{...e,droppable:{...e.droppable,containers:n}}}case it.SetDroppableDisabled:{let{id:o,key:r,disabled:n}=t,i=e.droppable.containers.get(o);if(!i||r!==i.key)return e;let s=new Xn(e.droppable.containers);return s.set(o,{...i,disabled:n}),{...e,droppable:{...e.droppable,containers:s}}}case it.UnregisterDroppable:{let{id:o,key:r}=t,n=e.droppable.containers.get(o);if(!n||r!==n.key)return e;let i=new Xn(e.droppable.containers);return i.delete(o),{...e,droppable:{...e.droppable,containers:i}}}default:return e}}function zx(e){let{disabled:t}=e,{active:o,activatorEvent:r,draggableNodes:n}=(0,D.useContext)(Bs),i=Ds(r),s=Ds(o?.id);return(0,D.useEffect)(()=>{if(!t&&!r&&i&&s!=null){if(!Qi(i)||document.activeElement===i.target)return;let a=n.get(s);if(!a)return;let{activatorNode:c,node:l}=a;if(!c.current&&!l.current)return;requestAnimationFrame(()=>{for(let m of[c.current,l.current]){if(!m)continue;let u=O1(m);if(u){u.focus();break}}})}},[r,t,n,s,i]),null}function yf(e,t){let{transform:o,...r}=t;return e!=null&&e.length?e.reduce((n,i)=>i({transform:n,...r}),o):o}function $x(e){return(0,D.useMemo)(()=>({draggable:{...cc.draggable,...e?.draggable},droppable:{...cc.droppable,...e?.droppable},dragOverlay:{...cc.dragOverlay,...e?.dragOverlay}}),[e?.draggable,e?.droppable,e?.dragOverlay])}function qx(e){let{activeNode:t,measure:o,initialRect:r,config:n=!0}=e,i=(0,D.useRef)(!1),{x:s,y:a}=typeof n=="boolean"?{x:n,y:n}:n;ho(()=>{if(!s&&!a||!t){i.current=!1;return}if(i.current||!r)return;let l=t?.node.current;if(!l||l.isConnected===!1)return;let m=o(l),u=of(m,r);if(s||(u.x=0),a||(u.y=0),i.current=!0,Math.abs(u.x)>0||Math.abs(u.y)>0){let g=nf(l);g&&g.scrollBy({top:u.y,left:u.x})}},[t,s,a,r,o])}var fc=(0,D.createContext)({...ir,scaleX:1,scaleY:1}),vn;(function(e){e[e.Uninitialized=0]="Uninitialized",e[e.Initializing=1]="Initializing",e[e.Initialized=2]="Initialized"})(vn||(vn={}));var hf=(0,D.memo)(function(t){var o,r,n,i;let{id:s,accessibility:a,autoScroll:c=!0,children:l,sensors:m=Vx,collisionDetection:u=rx,measuring:g,modifiers:x,...y}=t,b=(0,D.useReducer)(Rx,void 0,Gx),[h,w]=b,[T,v]=K3(),[I,N]=(0,D.useState)(vn.Uninitialized),B=I===vn.Initialized,{draggable:{active:V,nodes:L,translate:F},droppable:{containers:P}}=h,H=V?L.get(V):null,G=(0,D.useRef)({initial:null,translated:null}),X=(0,D.useMemo)(()=>{var Dt;return V!=null?{id:V,data:(Dt=H?.data)!=null?Dt:Hx,rect:G}:null},[V,H]),re=(0,D.useRef)(null),[Pe,Zt]=(0,D.useState)(null),[Me,lo]=(0,D.useState)(null),Xe=Yi(y,Object.values(y)),wo=jn("DndDescribedBy",s),bo=(0,D.useMemo)(()=>P.getEnabled(),[P]),Le=$x(g),{droppableRects:It,measureDroppableContainers:mt,measuringScheduled:eo}=Dx(bo,{dragging:B,dependencies:[F.x,F.y],config:Le.droppable}),ot=Tx(L,V),Wo=(0,D.useMemo)(()=>Me?Es(Me):null,[Me]),vo=e3(),ze=Ex(ot,Le.draggable.measure);qx({activeNode:V?L.get(V):null,config:vo.layoutShiftCompensation,initialRect:ze,measure:Le.draggable.measure});let le=q1(ot,Le.draggable.measure,ze),mr=q1(ot?ot.parentElement:null),Co=(0,D.useRef)({activatorEvent:null,active:null,activeNode:ot,collisionRect:null,collisions:null,droppableRects:It,draggableNodes:L,draggingNode:null,draggingNodeRect:null,droppableContainers:P,over:null,scrollableAncestors:[],scrollAdjustedTranslate:null}),Gr=P.getNodeFor((o=Co.current.over)==null?void 0:o.id),Uo=Ux({measure:Le.dragOverlay.measure}),nn=(r=Uo.nodeRef.current)!=null?r:ot,Rr=B?(n=Uo.rect)!=null?n:le:null,cs=!!(Uo.nodeRef.current&&Uo.rect),ls=Fx(cs?null:le),mi=uf(nn?qt(nn):null),ur=Nx(B?Gr??ot:null),Ln=Wx(ur),On=yf(x,{transform:{x:F.x-ls.x,y:F.y-ls.y,scaleX:1,scaleY:1},activatorEvent:Me,active:X,activeNodeRect:le,containerNodeRect:mr,draggingNodeRect:Rr,over:Co.current.over,overlayNodeRect:Uo.rect,scrollableAncestors:ur,scrollableAncestorRects:Ln,windowRect:mi}),ds=Wo?Kn(Wo,F):null,ps=Bx(ur),pl=K1(ps),Xs=K1(ps,[le]),sn=Kn(On,pl),zr=Rr?sx(Rr,On):null,Wn=X&&zr?u({active:X,collisionRect:zr,droppableRects:It,droppableContainers:bo,pointerCoordinates:ds}):null,Te=ef(Wn,"id"),[Ve,c0]=(0,D.useState)(null),Y2=cs?On:Kn(On,Xs),J2=nx(Y2,(i=Ve?.rect)!=null?i:null,le),l0=(0,D.useCallback)((Dt,To)=>{let{sensor:Io,options:an}=To;if(re.current==null)return;let Vo=L.get(re.current);if(!Vo)return;let Xo=Dt.nativeEvent,fr=new Io({active:re.current,activeNode:Vo,event:Xo,options:an,context:Co,onStart(Qo){let ms=re.current;if(ms==null)return;let us=L.get(ms);if(!us)return;let{onDragStart:Qs}=Xe.current,Zs={active:{id:ms,data:us.data,rect:G}};(0,Cn.unstable_batchedUpdates)(()=>{Qs?.(Zs),N(vn.Initializing),w({type:it.DragStart,initialCoordinates:Qo,active:ms}),T({type:"onDragStart",event:Zs})})},onMove(Qo){w({type:it.DragMove,coordinates:Qo})},onEnd:ui(it.DragEnd),onCancel:ui(it.DragCancel)});(0,Cn.unstable_batchedUpdates)(()=>{Zt(fr),lo(Dt.nativeEvent)});function ui(Qo){return async function(){let{active:us,collisions:Qs,over:Zs,scrollAdjustedTranslate:p0}=Co.current,fs=null;if(us&&p0){let{cancelDrop:gs}=Xe.current;fs={activatorEvent:Xo,active:us,collisions:Qs,delta:p0,over:Zs},Qo===it.DragEnd&&typeof gs=="function"&&await Promise.resolve(gs(fs))&&(Qo=it.DragCancel)}re.current=null,(0,Cn.unstable_batchedUpdates)(()=>{w({type:Qo}),N(vn.Uninitialized),c0(null),Zt(null),lo(null);let gs=Qo===it.DragEnd?"onDragEnd":"onDragCancel";if(fs){let ml=Xe.current[gs];ml?.(fs),T({type:gs,event:fs})}})}}},[L]),X2=(0,D.useCallback)((Dt,To)=>(Io,an)=>{let Vo=Io.nativeEvent,Xo=L.get(an);if(re.current!==null||!Xo||Vo.dndKit||Vo.defaultPrevented)return;let fr={active:Xo};Dt(Io,To.options,fr)===!0&&(Vo.dndKit={capturedBy:To.sensor},re.current=an,l0(Io,To))},[L,l0]),d0=Ix(m,X2);Lx(m),ho(()=>{le&&I===vn.Initializing&&N(vn.Initialized)},[le,I]),(0,D.useEffect)(()=>{let{onDragMove:Dt}=Xe.current,{active:To,activatorEvent:Io,collisions:an,over:Vo}=Co.current;if(!To||!Io)return;let Xo={active:To,activatorEvent:Io,collisions:an,delta:{x:sn.x,y:sn.y},over:Vo};(0,Cn.unstable_batchedUpdates)(()=>{Dt?.(Xo),T({type:"onDragMove",event:Xo})})},[sn.x,sn.y]),(0,D.useEffect)(()=>{let{active:Dt,activatorEvent:To,collisions:Io,droppableContainers:an,scrollAdjustedTranslate:Vo}=Co.current;if(!Dt||re.current==null||!To||!Vo)return;let{onDragOver:Xo}=Xe.current,fr=an.get(Te),ui=fr&&fr.rect.current?{id:fr.id,rect:fr.rect.current,data:fr.data,disabled:fr.disabled}:null,Qo={active:Dt,activatorEvent:To,collisions:Io,delta:{x:Vo.x,y:Vo.y},over:ui};(0,Cn.unstable_batchedUpdates)(()=>{c0(ui),Xo?.(Qo),T({type:"onDragOver",event:Qo})})},[Te]),ho(()=>{Co.current={activatorEvent:Me,active:X,activeNode:ot,collisionRect:zr,collisions:Wn,droppableRects:It,draggableNodes:L,draggingNode:nn,draggingNodeRect:Rr,droppableContainers:P,over:Ve,scrollableAncestors:ur,scrollAdjustedTranslate:sn},G.current={initial:Rr,translated:zr}},[X,ot,Wn,zr,L,nn,Rr,It,P,Ve,ur,sn]),bx({...vo,delta:F,draggingRect:zr,pointerCoordinates:ds,scrollableAncestors:ur,scrollableAncestorRects:Ln});let Q2=(0,D.useMemo)(()=>({active:X,activeNode:ot,activeNodeRect:le,activatorEvent:Me,collisions:Wn,containerNodeRect:mr,dragOverlay:Uo,draggableNodes:L,droppableContainers:P,droppableRects:It,over:Ve,measureDroppableContainers:mt,scrollableAncestors:ur,scrollableAncestorRects:Ln,measuringConfiguration:Le,measuringScheduled:eo,windowRect:mi}),[X,ot,le,Me,Wn,mr,Uo,L,P,It,Ve,mt,ur,Ln,Le,eo,mi]),Z2=(0,D.useMemo)(()=>({activatorEvent:Me,activators:d0,active:X,activeNodeRect:le,ariaDescribedById:{draggable:wo},dispatch:w,draggableNodes:L,over:Ve,measureDroppableContainers:mt}),[Me,d0,X,le,w,wo,L,Ve,mt]);return D.default.createElement(X1.Provider,{value:v},D.default.createElement(Bs.Provider,{value:Z2},D.default.createElement(xf.Provider,{value:Q2},D.default.createElement(fc.Provider,{value:J2},l)),D.default.createElement(zx,{disabled:a?.restoreFocus===!1})),D.default.createElement(X3,{...a,hiddenTextDescribedById:wo}));function e3(){let Dt=Pe?.autoScrollEnabled===!1,To=typeof c=="object"?c.enabled===!1:c===!1,Io=B&&!Dt&&!To;return typeof c=="object"?{...c,enabled:Io}:{enabled:Io}}}),jx=(0,D.createContext)(null),Y1="button",Kx="Droppable";function kf(e){let{id:t,data:o,disabled:r=!1,attributes:n}=e,i=jn(Kx),{activators:s,activatorEvent:a,active:c,activeNodeRect:l,ariaDescribedById:m,draggableNodes:u,over:g}=(0,D.useContext)(Bs),{role:x=Y1,roleDescription:y="draggable",tabIndex:b=0}=n??{},h=c?.id===t,w=(0,D.useContext)(h?fc:jx),[T,v]=Is(),[I,N]=Is(),B=Ox(s,t),V=Yi(o);ho(()=>(u.set(t,{id:t,key:i,node:T,activatorNode:I,data:V}),()=>{let F=u.get(t);F&&F.key===i&&u.delete(t)}),[u,t]);let L=(0,D.useMemo)(()=>({role:x,tabIndex:b,"aria-disabled":r,"aria-pressed":h&&x===Y1?!0:void 0,"aria-roledescription":y,"aria-describedby":m.draggable}),[r,x,b,h,y,m.draggable]);return{active:c,activatorEvent:a,activeNodeRect:l,attributes:L,isDragging:h,listeners:r?void 0:B,node:T,over:g,setNodeRef:v,setActivatorNodeRef:N,transform:w}}function pd(){return(0,D.useContext)(xf)}var Yx="Droppable",Jx={timeout:25};function Sf(e){let{data:t,disabled:o=!1,id:r,resizeObserverConfig:n}=e,i=jn(Yx),{active:s,dispatch:a,over:c,measureDroppableContainers:l}=(0,D.useContext)(Bs),m=(0,D.useRef)({disabled:o}),u=(0,D.useRef)(!1),g=(0,D.useRef)(null),x=(0,D.useRef)(null),{disabled:y,updateMeasurementsFor:b,timeout:h}={...Jx,...n},w=Yi(b??r),T=(0,D.useCallback)(()=>{if(!u.current){u.current=!0;return}x.current!=null&&clearTimeout(x.current),x.current=setTimeout(()=>{l(Array.isArray(w.current)?w.current:[w.current]),x.current=null},h)},[h]),v=uc({callback:T,disabled:y||!s}),I=(0,D.useCallback)((L,F)=>{v&&(F&&(v.unobserve(F),u.current=!1),L&&v.observe(L))},[v]),[N,B]=Is(I),V=Yi(t);return(0,D.useEffect)(()=>{!v||!N.current||(v.disconnect(),u.current=!1,v.observe(N.current))},[N,v]),ho(()=>(a({type:it.RegisterDroppable,element:{id:r,key:i,disabled:o,node:N,rect:g,data:V}}),()=>a({type:it.UnregisterDroppable,key:i,id:r})),[r]),(0,D.useEffect)(()=>{o!==m.current.disabled&&(a({type:it.SetDroppableDisabled,id:r,key:i,disabled:o}),m.current.disabled=o)},[r,i,o,a]),{active:s,rect:g,isOver:c?.id===r,node:N,over:c,setNodeRef:B}}function Xx(e){let{animation:t,children:o}=e,[r,n]=(0,D.useState)(null),[i,s]=(0,D.useState)(null),a=Ds(o);return!o&&!r&&a&&n(a),ho(()=>{if(!i)return;let c=r?.key,l=r?.props.id;if(c==null||l==null){n(null);return}Promise.resolve(t(l,i)).then(()=>{n(null)})},[t,r,i]),D.default.createElement(D.default.Fragment,null,o,r?(0,D.cloneElement)(r,{ref:s}):null)}var Qx={x:0,y:0,scaleX:1,scaleY:1};function Zx(e){let{children:t}=e;return D.default.createElement(Bs.Provider,{value:gf},D.default.createElement(fc.Provider,{value:Qx},t))}var ey={position:"fixed",touchAction:"none"},ty=e=>Qi(e)?"transform 250ms ease":void 0,oy=(0,D.forwardRef)((e,t)=>{let{as:o,activatorEvent:r,adjustScale:n,children:i,className:s,rect:a,style:c,transform:l,transition:m=ty}=e;if(!a)return null;let u=n?l:{...l,scaleX:1,scaleY:1},g={...ey,width:a.width,height:a.height,top:a.top,left:a.left,transform:rr.Transform.toString(u),transformOrigin:n&&r?Z3(r,a):void 0,transition:typeof m=="function"?m(r):m,...c};return D.default.createElement(o,{className:s,style:g,ref:t},i)}),ry=e=>t=>{let{active:o,dragOverlay:r}=t,n={},{styles:i,className:s}=e;if(i!=null&&i.active)for(let[a,c]of Object.entries(i.active))c!==void 0&&(n[a]=o.node.style.getPropertyValue(a),o.node.style.setProperty(a,c));if(i!=null&&i.dragOverlay)for(let[a,c]of Object.entries(i.dragOverlay))c!==void 0&&r.node.style.setProperty(a,c);return s!=null&&s.active&&o.node.classList.add(s.active),s!=null&&s.dragOverlay&&r.node.classList.add(s.dragOverlay),function(){for(let[c,l]of Object.entries(n))o.node.style.setProperty(c,l);s!=null&&s.active&&o.node.classList.remove(s.active)}},ny=e=>{let{transform:{initial:t,final:o}}=e;return[{transform:rr.Transform.toString(t)},{transform:rr.Transform.toString(o)}]},iy={duration:250,easing:"ease",keyframes:ny,sideEffects:ry({styles:{active:{opacity:"0"}}})};function sy(e){let{config:t,draggableNodes:o,droppableContainers:r,measuringConfiguration:n}=e;return Ts((i,s)=>{if(t===null)return;let a=o.get(i);if(!a)return;let c=a.node.current;if(!c)return;let l=ff(s);if(!l)return;let{transform:m}=qt(s).getComputedStyle(s),u=rf(m);if(!u)return;let g=typeof t=="function"?t:ay(t);return pf(c,n.draggable.measure),g({active:{id:i,data:a.data,node:c,rect:n.draggable.measure(c)},draggableNodes:o,dragOverlay:{node:s,rect:n.dragOverlay.measure(l)},droppableContainers:r,measuringConfiguration:n,transform:u})})}function ay(e){let{duration:t,easing:o,sideEffects:r,keyframes:n}={...iy,...e};return i=>{let{active:s,dragOverlay:a,transform:c,...l}=i;if(!t)return;let m={x:a.rect.left-s.rect.left,y:a.rect.top-s.rect.top},u={scaleX:c.scaleX!==1?s.rect.width*c.scaleX/a.rect.width:1,scaleY:c.scaleY!==1?s.rect.height*c.scaleY/a.rect.height:1},g={x:c.x-m.x,y:c.y-m.y,...u},x=n({...l,active:s,dragOverlay:a,transform:{initial:c,final:g}}),[y]=x,b=x[x.length-1];if(JSON.stringify(y)===JSON.stringify(b))return;let h=r?.({active:s,dragOverlay:a,...l}),w=a.node.animate(x,{duration:t,easing:o,fill:"forwards"});return new Promise(T=>{w.onfinish=()=>{h?.(),T()}})}}var J1=0;function cy(e){return(0,D.useMemo)(()=>{if(e!=null)return J1++,J1},[e])}var Af=D.default.memo(e=>{let{adjustScale:t=!1,children:o,dropAnimation:r,style:n,transition:i,modifiers:s,wrapperElement:a="div",className:c,zIndex:l=999}=e,{activatorEvent:m,active:u,activeNodeRect:g,containerNodeRect:x,draggableNodes:y,droppableContainers:b,dragOverlay:h,over:w,measuringConfiguration:T,scrollableAncestors:v,scrollableAncestorRects:I,windowRect:N}=pd(),B=(0,D.useContext)(fc),V=cy(u?.id),L=yf(s,{activatorEvent:m,active:u,activeNodeRect:g,containerNodeRect:x,draggingNodeRect:h.rect,over:w,overlayNodeRect:h.rect,scrollableAncestors:v,scrollableAncestorRects:I,transform:B,windowRect:N}),F=dd(g),P=sy({config:r,draggableNodes:y,droppableContainers:b,measuringConfiguration:T}),H=F?h.setRef:void 0;return D.default.createElement(Zx,null,D.default.createElement(Xx,{animation:P},u&&V?D.default.createElement(oy,{key:V,id:u.id,ref:H,as:a,activatorEvent:m,adjustScale:t,className:c,transition:i,rect:F,style:{zIndex:l,...n},transform:L},o):null))});d();p();var Ke=A(C());function yc(e,t,o){let r=e.slice();return r.splice(o<0?r.length+o:o,0,r.splice(t,1)[0]),r}function ly(e,t){return e.reduce((o,r,n)=>{let i=t.get(r);return i&&(o[n]=i),o},Array(e.length))}function gc(e){return e!==null&&e>=0}function dy(e,t){if(e===t)return!0;if(e.length!==t.length)return!1;for(let o=0;o<e.length;o++)if(e[o]!==t[o])return!1;return!0}function py(e){return typeof e=="boolean"?{draggable:e,droppable:e}:e}var wf=e=>{let{rects:t,activeIndex:o,overIndex:r,index:n}=e,i=yc(t,r,o),s=t[n],a=i[n];return!a||!s?null:{x:a.left-s.left,y:a.top-s.top,scaleX:a.width/s.width,scaleY:a.height/s.height}};var xc={scaleX:1,scaleY:1},bf=e=>{var t;let{activeIndex:o,activeNodeRect:r,index:n,rects:i,overIndex:s}=e,a=(t=i[o])!=null?t:r;if(!a)return null;if(n===o){let l=i[s];return l?{x:0,y:o<s?l.top+l.height-(a.top+a.height):l.top-a.top,...xc}:null}let c=my(i,n,o);return n>o&&n<=s?{x:0,y:-a.height-c,...xc}:n<o&&n>=s?{x:0,y:a.height+c,...xc}:{x:0,y:0,...xc}};function my(e,t,o){let r=e[t],n=e[t-1],i=e[t+1];return r?o<t?n?r.top-(n.top+n.height):i?i.top-(r.top+r.height):0:i?i.top-(r.top+r.height):n?r.top-(n.top+n.height):0:0}var vf="Sortable",Cf=Ke.default.createContext({activeIndex:-1,containerId:vf,disableTransforms:!1,items:[],overIndex:-1,useDragOverlay:!1,sortedRects:[],strategy:wf,disabled:{draggable:!1,droppable:!1}});function Tf(e){let{children:t,id:o,items:r,strategy:n=wf,disabled:i=!1}=e,{active:s,dragOverlay:a,droppableRects:c,over:l,measureDroppableContainers:m}=pd(),u=jn(vf,o),g=a.rect!==null,x=(0,Ke.useMemo)(()=>r.map(B=>typeof B=="object"&&"id"in B?B.id:B),[r]),y=s!=null,b=s?x.indexOf(s.id):-1,h=l?x.indexOf(l.id):-1,w=(0,Ke.useRef)(x),T=!dy(x,w.current),v=h!==-1&&b===-1||T,I=py(i);ho(()=>{T&&y&&m(x)},[T,x,y,m]),(0,Ke.useEffect)(()=>{w.current=x},[x]);let N=(0,Ke.useMemo)(()=>({activeIndex:b,containerId:u,disabled:I,disableTransforms:v,items:x,overIndex:h,useDragOverlay:g,sortedRects:ly(x,c),strategy:n}),[b,u,I.draggable,I.droppable,v,x,h,c,g,n]);return Ke.default.createElement(Cf.Provider,{value:N},t)}var uy=e=>{let{id:t,items:o,activeIndex:r,overIndex:n}=e;return yc(o,r,n).indexOf(t)},fy=e=>{let{containerId:t,isSorting:o,wasDragging:r,index:n,items:i,newIndex:s,previousItems:a,previousContainerId:c,transition:l}=e;return!l||!r||a!==i&&n===s?!1:o?!0:s!==n&&t===c},gy={duration:200,easing:"ease"},If="transform",xy=rr.Transition.toString({property:If,duration:0,easing:"linear"}),yy={roleDescription:"sortable"};function hy(e){let{disabled:t,index:o,node:r,rect:n}=e,[i,s]=(0,Ke.useState)(null),a=(0,Ke.useRef)(o);return ho(()=>{if(!t&&o!==a.current&&r.current){let c=n.current;if(c){let l=Qn(r.current,{ignoreTransform:!0}),m={x:c.left-l.left,y:c.top-l.top,scaleX:c.width/l.width,scaleY:c.height/l.height};(m.x||m.y)&&s(m)}}o!==a.current&&(a.current=o)},[t,o,r,n]),(0,Ke.useEffect)(()=>{i&&s(null)},[i]),i}function Df(e){let{animateLayoutChanges:t=fy,attributes:o,disabled:r,data:n,getNewIndex:i=uy,id:s,strategy:a,resizeObserverConfig:c,transition:l=gy}=e,{items:m,containerId:u,activeIndex:g,disabled:x,disableTransforms:y,sortedRects:b,overIndex:h,useDragOverlay:w,strategy:T}=(0,Ke.useContext)(Cf),v=ky(r,x),I=m.indexOf(s),N=(0,Ke.useMemo)(()=>({sortable:{containerId:u,index:I,items:m},...n}),[u,n,I,m]),B=(0,Ke.useMemo)(()=>m.slice(m.indexOf(s)),[m,s]),{rect:V,node:L,isOver:F,setNodeRef:P}=Sf({id:s,data:N,disabled:v.droppable,resizeObserverConfig:{updateMeasurementsFor:B,...c}}),{active:H,activatorEvent:G,activeNodeRect:X,attributes:re,setNodeRef:Pe,listeners:Zt,isDragging:Me,over:lo,setActivatorNodeRef:Xe,transform:wo}=kf({id:s,data:N,attributes:{...yy,...o},disabled:v.draggable}),bo=F1(P,Pe),Le=!!H,It=Le&&!y&&gc(g)&&gc(h),mt=!w&&Me,eo=mt&&It?wo:null,Wo=It?eo??(a??T)({rects:b,activeNodeRect:X,activeIndex:g,overIndex:h,index:I}):null,vo=gc(g)&&gc(h)?i({id:s,items:m,activeIndex:g,overIndex:h}):I,ze=H?.id,le=(0,Ke.useRef)({activeId:ze,items:m,newIndex:vo,containerId:u}),mr=m!==le.current.items,Co=t({active:H,containerId:u,isDragging:Me,isSorting:Le,id:s,index:I,items:m,newIndex:le.current.newIndex,previousItems:le.current.items,previousContainerId:le.current.containerId,transition:l,wasDragging:le.current.activeId!=null}),Gr=hy({disabled:!Co,index:I,node:L,rect:V});return(0,Ke.useEffect)(()=>{Le&&le.current.newIndex!==vo&&(le.current.newIndex=vo),u!==le.current.containerId&&(le.current.containerId=u),m!==le.current.items&&(le.current.items=m)},[Le,vo,u,m]),(0,Ke.useEffect)(()=>{if(ze===le.current.activeId)return;if(ze&&!le.current.activeId){le.current.activeId=ze;return}let nn=setTimeout(()=>{le.current.activeId=ze},50);return()=>clearTimeout(nn)},[ze]),{active:H,activeIndex:g,attributes:re,data:N,rect:V,index:I,newIndex:vo,items:m,isOver:F,isSorting:Le,isDragging:Me,listeners:Zt,node:L,overIndex:h,over:lo,setNodeRef:bo,setActivatorNodeRef:Xe,setDroppableNodeRef:P,setDraggableNodeRef:Pe,transform:Gr??Wo,transition:Uo()};function Uo(){if(Gr||mr&&le.current.newIndex===I)return xy;if(!(mt&&!Qi(G)||!l)&&(Le||Co))return rr.Transition.toString({...l,property:If})}}function ky(e,t){var o,r;return typeof e=="boolean"?{draggable:e,droppable:!1}:{draggable:(o=e?.draggable)!=null?o:t.draggable,droppable:(r=e?.droppable)!=null?r:t.droppable}}var zv=[ge.Down,ge.Right,ge.Up,ge.Left];var Ae=A(C());d();p();var Nr=A(C());var Sy=f.div(e=>`
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 8px 10px 12px;
  border-radius: 12px;
  height: 52px;
  margin-bottom: 10px;
  cursor: ${e.isDragged?"grabbing":"pointer"};
  background: ${e.isDragged?"#AB9FF2":"#2a2a2a"};
  color: ${e.isDragged?"#2a2a2a":"#fff"};
  box-shadow: ${e.isDragged?"0px 4px 16px rgba(0, 0, 0, 0.5)":"#0px"};
  border: 1px solid transparent;

  &:hover {
    border: 1px solid ${e.isDragged?"transparent":"#777"};
  }

  rect {
    &:hover {
      cursor: ${e.isDragged?"grabbing":"grab"};
      fill: rgba(255, 255, 255, 0.05);
    }
  }
`),Ay=f.div`
  color: #fff;
`,wy=f(E).attrs({size:16,weight:600,lineHeight:19,maxWidth:"140px",noWrap:!0})`
  color: currentColor;
`,by=f.div`
  display: flex;
  align-items: center;
`,md=Nr.default.memo(({accountBalance:e,accountIcon:t,accountIndex:o,accountName:r,isDragged:n,onClick:i,setNodeRef:s,style:a,...c})=>Nr.default.createElement(Sy,{isDragged:n,ref:s,style:a,...c,onClick:i},Nr.default.createElement(Ay,null,Nr.default.createElement($e,{size:"small",accountIcon:t,accountIndex:o,accountName:r})),Nr.default.createElement(xt,null,Nr.default.createElement(wy,{margin:"0 0 0 12px"},r)),Nr.default.createElement(by,null,e?Nr.default.createElement(Ir,{className:_e({marginRight:6}),value:e,font:"bodyMedium",color:"textTertiary"}):null,Nr.default.createElement(qm,null))));d();p();var Tn=A(C());d();p();var st=A(C());d();p();var Be=A(C());var Ef=()=>{let{t:e}=S(),{hideSettingsMenu:t}=Gt(),{popDetailView:o,pushDetailView:r}=U(),{formState:{errors:n},register:i,getValues:s,handleSubmit:a}=to(),{data:c=[]}=Do("seeds-only"),{data:l=[]}=Do("seedless-seeds-only"),{data:m=[]}=Do("all"),{data:u=[]}=te(),{mutate:g}=Np(),{mutate:x}=Lp(),[y,b]=(0,Be.useState)(),h=(0,Be.useMemo)(()=>u.length,[u]),w=Vt(P=>P.editableAccountMetadata),T=Vt(P=>P.setEditableAccountMetadata),v=w?.name||e("onboardingImportAccountsAccountName",{walletIndex:h+1}),I=(0,Be.useCallback)(()=>{o()},[o]),N=(0,Be.useCallback)(()=>{r(Be.default.createElement($a,{navigationCallback:I}))},[r,I]),B=(0,Be.useCallback)(()=>{let P=s("name");T({...w,name:P}),r(Be.default.createElement(wn,null))},[s,w,T,r]);(0,Be.useEffect)(()=>{c.length>0?b(w?.seedIdentifier||c[0].identifier):l.length>0&&b(w?.seedIdentifier||l[0].identifier)},[c,w,b,l]);let V=Object.keys(n).length===0,L={required:!0},F=(0,Be.useCallback)(async({name:P})=>{if(!y){$.captureError(new Error("Failed to add new se*d account with no se*d selected"),"account");return}l.some(G=>G.identifier===y)?x({name:P||v,icon:w?.icon,socialSeedIdentifier:y}):g({name:P||v,icon:w?.icon,seedIdentifier:y}),W.capture("addSeedAccount",{data:{walletIndex:u.length}}),t(),T(void 0)},[u,g,x,v,w?.icon,t,y,T,l]);return Be.default.createElement(fo,{onSubmit:a(F)},Be.default.createElement(O,null,e("addAccountGenerateAccountFromSeed")),Be.default.createElement(zo,{size:"large"},Be.default.createElement(Ni,null,Be.default.createElement(Ui,null,Be.default.createElement(Dr,{accountIndex:h,accountName:v,accountIcon:w?.icon,onClick:B}))),Be.default.createElement(Tr,null,Be.default.createElement(mo.WithWarning,{placeholder:e("addAccountImportAccountName"),defaultValue:v,warning:n.name,warningMessage:n.name?.message,autoComplete:"off",maxLength:wi,...i("name",L)}),m.length>1&&Be.default.createElement(vu,{accounts:u,seedMetas:m,onClick:N,onChange:P=>b(P),selectedSeedIdentifier:y})),Be.default.createElement(gt,{primaryText:e("commandCreate"),secondaryText:e("commandCancel"),onSecondaryClicked:o,primaryDisabled:!V})))};d();p();var ce=A(C());var Pf=()=>{let{t:e}=S(),{pushDetailView:t}=U(),o=(0,ce.useCallback)(()=>{t(ce.default.createElement(vy,null))},[t]);return ce.default.createElement(ce.default.Fragment,null,ce.default.createElement(O,null,e("addAccountGenerateAccountFromSeed")),ce.default.createElement(Iy,null,ce.default.createElement(Ty,null,ce.default.createElement(J.FilePlus,{color:"gray",size:"50%"})),ce.default.createElement(Dy,null,e("addAccountNewWalletPrimaryText")),ce.default.createElement(Ey,null,e("addAccountCreateSeedExplainer"))),ce.default.createElement(rt,null,ce.default.createElement(oe,{theme:"primary","data-testid":"add-address-menu-item",onClick:o},e("commandContinue"))))},vy=()=>{let{t:e}=S(),{hideSettingsMenu:t}=Gt(),[{indexes:o,wordlist:r},n]=(0,ce.useState)({indexes:[],wordlist:[]}),[i,s]=(0,ce.useState)(),{handleImportSeed:a}=qu("seed"),c=_o(),{data:l}=te(),m=(0,ce.useCallback)(async()=>{if(!i)return;let b=0,h=l?.length||0;await a(i,[{derivationIndex:b,derivationPathTypes:H0(c)}],e("onboardingImportAccountsAccountName",{walletIndex:h+b+1}),h),t()},[l?.length,c,a,t,i,e]),u=(0,ce.useCallback)(async()=>{let b=await Da(),h=b.fromSentenceLength(12),w=Array.from(await h.getIndexes());n({indexes:w,wordlist:b.wordlist}),s(h)},[]);(0,ce.useEffect)(()=>{u()},[u]);let[g,x]=(0,ce.useState)(!1),y=()=>{x(!g)};return ce.default.createElement(ce.default.Fragment,null,ce.default.createElement(O,{shouldWrap:!0},e("exportSecretYourSecretRecoveryPhrase")),ce.default.createElement(K,null,ce.default.createElement(uo,null,ce.default.createElement(bn,{type:"mnemonic"}),ce.default.createElement(Qa,{indexes:o,wordlist:r}),ce.default.createElement(Cy,{key:"onboarding-form-saved-secret-recovery-phrase-checkbox"},ce.default.createElement(xu,{"data-testid":"onboarding-form-saved-secret-recovery-phrase-checkbox",checked:g,onChange:y}),ce.default.createElement(E,{size:16,lineHeight:19,color:"#999"},ce.default.createElement(Ro,{i18nKey:"onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase"}))))),ce.default.createElement(rt,null,ce.default.createElement(oe,{theme:"primary",onClick:m,disabled:!g},e("commandCreate"))))},Cy=f(gu)`
  margin-top: 16px;
`,Ty=f(yu).attrs({color:"#181818",diameter:88,includeDarkBoxShadow:!0})`
  margin-bottom: 16px;
`,Iy=f(Nt).attrs({align:"center",justify:"center",flex:1})``,Dy=f(E).attrs({size:17,lineHeight:22,weight:500,margin:"0 0 8px"})``,Ey=f(E).attrs({size:15,lineHeight:21,weight:500,color:"#777",margin:"0 0 17px"})``;d();p();var No=A(C());var Mf=()=>{let{t:e}=S(),{hideSettingsMenu:t}=Gt(),{pushDetailView:o}=U(),{data:r=[]}=te(),{mutate:n}=Up(),i=Vt(x=>x.editableAccountMetadata),s=Vt(x=>x.setEditableAccountMetadata),a=Kp({importPrivateKeyCallback:x=>{n(x),s(void 0),t()},existingAccounts:r,editableAccountMetadata:i,analytics:W}),{formOnSubmitHandler:c,canSubmit:l,getValues:m,errors:u}=a,g=()=>{let x=m("name"),y=m("privateKey"),b=m("networkID"),h=m("addressType"),w={...i,name:x,networkID:b,addressType:h};if(u.privateKey)s(w);else try{let T=b?j.getNetworkDefinition(b).value:j.getNetworkDefinition(Ne.Solana.Mainnet).value,v=W0({privateKey:y,addressType:h,networkIDValue:T});s({...w,secrets:v})}catch{s(w),$.addBreadcrumb("account","Failed to cache private key for AvatarPicker transition => defaulting to remaining metadata","info")}o(No.default.createElement(wn,null))};return No.default.createElement("section",null,No.default.createElement(O,null,e("addAccountImportAccountPrimaryText")),No.default.createElement(fo,{onSubmit:c},No.default.createElement(_a,null,No.default.createElement(zo,{size:"large"},No.default.createElement(Ni,null,No.default.createElement(Ui,null,No.default.createElement(Dr,{accountIndex:0,accountName:i?.name||e("addAccountImportAccountPrivateKey"),accountIcon:i?.icon,onClick:g}))),No.default.createElement(Ku,{...a}))),No.default.createElement(rt,null,No.default.createElement(oe,{type:"submit",theme:l?"primary":"default",disabled:!l},e("addAccountImportAccountActionButtonImport")))))};d();p();var Ff=A(s3()),St=A(C());var Nf=()=>{let{t:e}=S(),{hideSettingsMenu:t}=Gt(),{data:o=[]}=te(),{mutate:r}=Bp(),n=_o(),{mutateAsync:i}=pa(),s=Vt(N=>N.editableAccountMetadata),{Controller:a,canSubmit:c,control:l,errors:m,nameValidations:u,address:g,addressValidations:x,handleSubmit:y,onSubmit:b,register:h,trigger:w}=jp({accounts:o,enabledAddressTypes:n,enableAddressTypes:i,editableAccountMetadata:{...s,icon:{type:"read-only-default"}},addReadOnlyAccount:r,fetchDomainOwner:sm}),T=()=>{t()},v=ki(),I=s?.networkID||v[0];return St.default.createElement(fo,{onSubmit:y(N=>b(N,T))},St.default.createElement(O,null,e("addAccountReadOnly")),St.default.createElement(_a,null,St.default.createElement(zo,{size:"large"},St.default.createElement(Py,null,St.default.createElement(My,null,St.default.createElement(Ei,{width:44,height:44,fill:"#FFF"}))),St.default.createElement(E,{size:16,color:"#777777",lineHeight:19,maxWidth:"312px",margin:"24px 0px 0px 0px"},e("onboardingImportReadOnlyAccountDescription")),St.default.createElement(Tr,null,St.default.createElement(a,{name:"networkID",control:l,defaultValue:I,render:({field:{onChange:N,value:B}})=>v.length===1?St.default.createElement(St.default.Fragment,null):St.default.createElement(_i,{onChange:V=>{N(V),g&&w("address")},value:B})}),St.default.createElement(mo.WithWarning,{placeholder:e("addAccountImportAccountName"),defaultValue:s?.name,warning:!!m.name,warningMessage:m.name?.message,autoComplete:"off",maxLength:wi,...h("name",u)}),St.default.createElement(Li.WithWarning,{placeholder:e("addAccountImportAccountPublicKey"),defaultValue:"",warning:!!m.address,warningMessage:m.address?.message,autoComplete:"off",...h("address",x),onChange:(0,Ff.default)(()=>w("address"),500)})))),St.default.createElement(rt,null,St.default.createElement(oe,{type:"submit",theme:c?"primary":"default",disabled:!c},e("addAccountImportAccountActionButtonImport"))))},Py=f.div`
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #181818;
  border-radius: 88px;
  min-width: 88px;
  width: 88px;
  height: 88px;
  margin-left: -8px;
`,My=f.div`
  opacity: 0.3;
`;d();p();var Bf=A(C()),Lf=()=>{let{t:e}=S();return Bf.default.createElement(R,{font:"caption"},e("addAccountLimitReachedText",{accountsCount:Sl}))},Of=()=>{let{data:e=[]}=te();return e.length>Sl};d();p();var ud=A(C());var Wf=({onClick:e,disabled:t})=>{let{t:o}=S(),r=ma();return ud.default.createElement(oo,{"data-testid":"add-account-create-new-wallet-button",topLeft:{text:o("addAccountNewWalletPrimaryText"),font:"bodyMedium"},bottomLeft:{text:o(r?"addAccountNewSingleChainWalletSecondaryText":"addAccountNewMultiChainWalletSecondaryText")},start:ud.default.createElement(kn,{backgroundColor:"borderPrimary",color:"textPrimary",icon:"Plus",shape:"circle",size:32}),onClick:e,disabled:t})};d();p();var fd=A(C());var Uf=({onClick:e,disabled:t})=>{let{t:o}=S();return fd.default.createElement(oo,{topLeft:{text:o("addAccountReadOnly"),font:"bodyMedium"},bottomLeft:{text:o("addAccountReadOnlySecondaryText")},start:fd.default.createElement(kn,{backgroundColor:"borderPrimary",color:"textPrimary",icon:"Eye",shape:"circle",size:32}),onClick:e,disabled:t})};var Vf=()=>{let{pushDetailView:e}=U(),t=Of(),o=Vt(g=>g.setEditableAccountMetadata),{data:r=[]}=Do("seedless-seeds-only"),n=r.length>0,{data:i=[]}=Do("seeds-only"),s=i.length>0,a=(0,st.useCallback)(()=>{e(!s&&!n?st.default.createElement(Pf,null):st.default.createElement(Ef,null))},[e,s,n]),c=(0,st.useCallback)(()=>Ar({url:"connect_hardware.html"}),[]),l=(0,st.useCallback)(()=>Ar({url:"onboarding.html?append=true"}),[]),m=(0,st.useCallback)(()=>{o(void 0),e(st.default.createElement(Mf,null))},[e,o]),u=(0,st.useCallback)(()=>e(st.default.createElement(Nf,null)),[e]);return st.default.createElement(M,{gap:"section"},t&&st.default.createElement(Lf,null),st.default.createElement(M,{gap:"list"},st.default.createElement(Wf,{onClick:a,disabled:t}),st.default.createElement(Yu,{onClick:c,disabled:t}),st.default.createElement(Ju,{onClick:l,disabled:t}),st.default.createElement(ju,{onClick:m,disabled:t}),st.default.createElement(Uf,{onClick:u,disabled:t})))};var hc=()=>{let{t:e}=S(),{popDetailView:t,detailViewStackLength:o}=U(),{hideSettingsMenu:r}=Gt();return Tn.default.createElement(Tn.default.Fragment,null,Tn.default.createElement(O,null,e("addAccountPrimaryText")),Tn.default.createElement(K,null,Tn.default.createElement(Vf,null)),Tn.default.createElement(rt,null,Tn.default.createElement(oe,{onClick:o>0?t:r},e("commandClose"))))};var Fy=10,es=()=>{let{t:e}=S(),{pushDetailView:t,pushDetailViewCallback:o}=U(),{data:r=[]}=te(),{mutate:n}=_p(),i=!fn(),{data:s}=Sa(),a=Z1(Q1(Zi,{activationConstraint:{distance:Fy}})),c=(0,Ae.useMemo)(()=>r.map(T=>({account:T,id:T.identifier})),[r]),[l,m]=Ae.default.useState(c),[u,g]=(0,Ae.useState)(null),x=o(Ae.default.createElement(hc,null)),y=(0,Ae.useCallback)((T,v)=>{T.stopPropagation(),t(Ae.default.createElement($i,{accountIdentifier:v}))},[t]),b=(0,Ae.useCallback)(T=>{let{active:v}=T,I=l.findIndex(N=>N.id===v.id);g(I)},[l]),h=(0,Ae.useCallback)(T=>{let{active:v,over:I}=T;if(v.id!==I.id){let N=l.findIndex(L=>L.id===I.id),B=l.findIndex(L=>L.id===v.id),V=yc(l,B,N);n({identifier:l[B].account.identifier,toIndex:N}),m(V),W.capture("accountOrderModified")}g(null)},[l,n]),w=(0,Ae.useMemo)(()=>{if(u===null)return null;let T=l[u];return Ae.default.createElement(md,{accountIndex:u,accountName:T.account.name,accountIcon:T.account.icon,isDragged:!0})},[u,l]);return Ae.default.createElement(Ae.default.Fragment,null,Ae.default.createElement(O,{icon:Ae.default.createElement(br,{fill:"#777"}),onIconClick:x},e("settingsManageAccounts")),Ae.default.createElement(K,{"data-testid":"sortable-account-container"},Ae.default.createElement(hf,{sensors:a,collisionDetection:tf,onDragStart:b,onDragEnd:h},Ae.default.createElement(Tf,{items:l,strategy:bf},l.map(({account:T,id:v},I)=>Ae.default.createElement(Ny,{key:v,id:v,index:I,account:T,isBalanceVisible:i,accountBalance:s?.[T.identifier]?.balance,openEditAccount:y}))),Ae.default.createElement(Af,null,w))),Ae.default.createElement(rt,null,Ae.default.createElement(oe,{"data-testid":"add-account-menu-item",onClick:x},e("addAccountPrimaryText"))))},Ny=Ae.default.memo(({id:e,account:t,index:o,isBalanceVisible:r,accountBalance:n,openEditAccount:i})=>{let{attributes:s,isDragging:a,listeners:c,transform:l,transition:m,setNodeRef:u}=Df({id:e}),g={transform:rr.Transform.toString(l),transition:m,opacity:a?.5:1},x=(0,Ae.useCallback)(y=>{i(y,t.identifier)},[t.identifier,i]);return Ae.default.createElement(md,{"data-testid":`manage-accounts-sortable-${t.name}`,accountIndex:o,accountBalance:r?n?.value:void 0,accountName:t.name,accountIcon:t.icon,isDragged:!1,onClick:x,setNodeRef:u,style:g,...s,...c})});d();p();var Lt=A(C());var ts=({debugMode:e=!1})=>{let{t}=S(),o=yi(),r=hi(),{mutateAsync:n}=sp(),{mutateAsync:i}=ip(),{data:s}=te(),[a,c]=(0,Lt.useState)([]);(0,Lt.useEffect)(()=>{let g=new Set(o),x=r.map(y=>({id:y,isEnabled:g.has(y)}));c(x)},[r,o]);let l=(0,Lt.useCallback)(g=>s?s.reduce((x,y)=>(y.addresses.every(b=>g.id===j.getChainID(b.networkID))&&x.push(y),x),[]).length>0:!1,[s]),m=(0,Lt.useCallback)(g=>{if(g.isEnabled&&l(g))return;let x=[g.id];o.length>1&&g.isEnabled&&(i({chainIDs:x}),W.capture("settingsChainSelectorToggledOffByUser",{data:{toggle:{chain:g.id}}})),g.isEnabled||(n({chainIDs:x}),W.capture("settingsChainSelectorToggledOnByUser",{data:{toggle:{chain:g.id}}}))},[i,l,n,o.length]),u=(0,Lt.useMemo)(()=>a.map(g=>{let x=`toggle-${g.id}`,y=!e&&g.id==="solana",b=h=>y?!0:h.isEnabled&&l(h);return{id:x,key:x,start:Lt.default.createElement(po,{networkID:j.getNetworkIDs(g.id)[0],size:24}),topLeft:{text:j[g.id].name,after:j.getDefaultChainEnablement(g.id)?void 0:Lt.default.createElement(Ii,{size:"small",children:"BETA"})},active:g.isEnabled,disabled:b(g),type:"toggle",onClick:()=>m(g)}}),[a,e,l,m]);return Lt.default.createElement(Lt.default.Fragment,null,Lt.default.createElement(O,null,t("settingsActiveNetworks")),Lt.default.createElement(K,null,Lt.default.createElement(z,{rows:u})))};d();p();var Se=A(C());d();p();var At=A(C());d();p();var Ie=A(C());var kc=Ie.default.memo(({recentAddress:e})=>{let{t}=S(),{popDetailView:o}=U(),{formState:{errors:r},control:n,register:i,handleSubmit:s,trigger:a,watch:c}=to({defaultValues:{address:e?e.address:"",networkID:e?e.chainID:Ne.Solana.Mainnet,label:""}}),l=_o(),{isExistingAccountAddress:m,isSavedAddress:u,addSavedAddress:g}=Sr(),x=c("address"),y=c("networkID"),[b,h]=(0,Ie.useState)(""),w=pn(b,500),{data:T,error:v,isFetching:I}=Ta(w,y),N=T?.address,B=(0,Ie.useCallback)(P=>m(P)?t("addAddressAddressAlreadyExists"):u(P,y)||u(b,y)?t("addAddressAddressAlreadyAdded"):!0,[y,b,m,u,t]),V=(0,Ie.useCallback)(({address:P,networkID:H,label:G})=>{g({address:P,chainID:H,label:G}),W.capture("addAddressSubmitted",{data:{networkId:H,chainId:j.getChainID(H)}}),o(),We.success(t("addAddressToast"))},[g,o,t]);(0,Ie.useEffect)(()=>{x&&Kr(x)&&h(x)},[x,y]),(0,Ie.useEffect)(()=>{(N||v)&&a("address")},[N,v,a]);let L=(0,Ie.useMemo)(()=>({required:{value:!0,message:t("addAddressAddressIsRequired")},validate:async P=>{let H=Kr(P);if(H&&v)return v;let G=H&&N?N:P;return sa(G,y)?B(G):t("addAddressAddressInvalid")}}),[y,v,N,B,t]),F={required:{value:!0,message:t("addAddressLabelIsRequired")}};return Ie.default.createElement(fo,{onSubmit:s(V)},Ie.default.createElement(O,null,t("addAddressPrimaryText")),Ie.default.createElement(K,null,Ie.default.createElement(Vi,{icon:Ie.default.createElement(Ga,null,Ie.default.createElement(br,{fill:"#2d2d2d",width:38})),primaryText:t("addAddressPrimaryText")}),Ie.default.createElement(Tr,null,Ie.default.createElement(ba,{name:"networkID",control:n,render:({field:{onChange:P,value:H}})=>l.length===1?Ie.default.createElement(Ie.default.Fragment,null):Ie.default.createElement(_i,{onChange:P,value:H})}),Ie.default.createElement(mo.WithWarning,{placeholder:t("addAddressLabelPlaceholder"),type:"text",warning:!!r.label,warningMessage:r.label?.message,autoComplete:"off",...i("label",F)}),Ie.default.createElement(Li.WithWarning,{placeholder:t("addAddressAddressPlaceholder"),warning:!!r.address,warningMessage:r.address?.message,autoComplete:"off",spellCheck:"false",...i("address",L)}))),Ie.default.createElement(gt,{primaryDisabled:I,primaryLoading:I,primaryText:t("commandAdd"),secondaryText:t("commandCancel"),onPrimaryClicked:s(V),onSecondaryClicked:o}))});d();p();var we=A(C());var _f=we.default.memo(({savedAddress:e})=>{let{t}=S(),{popDetailView:o}=U(),{formState:{errors:r},control:n,register:i,handleSubmit:s,trigger:a,watch:c}=to({defaultValues:{address:e.address,chainID:e.chainID,label:e.label}}),l=_o(),{isExistingAccountAddress:m,isSavedAddress:u,editSavedAddress:g,removeSavedAddress:x}=Sr(),y=c("address"),b=c("chainID"),[h,w]=(0,we.useState)(""),T=pn(h,500),{data:v,error:I,isFetching:N}=Ta(T,b),B=v?.address,V=(0,we.useCallback)(G=>{if(![G,h].includes(e.address)){if(m(G))return t("addAddressAddressAlreadyExists");if(u(G,b)||u(h,b))return t("editAddressAddressAlreadyAdded")}return!0},[b,h,m,u,e.address,t]),L=(0,we.useCallback)(({address:G,chainID:X,label:re})=>{let Pe={address:G,chainID:X,label:re};g(e,Pe),W.capture("editAddressSubmitted"),o(),yn(e,Pe)||We.success(t("editAddressToast"))},[g,o,e,t]),F=(0,we.useCallback)(()=>{x(e),o(),We.success(t("removeSavedAddressToast"))},[o,x,e,t]);(0,we.useEffect)(()=>{y&&Kr(y)&&w(y)},[y,b]),(0,we.useEffect)(()=>{(B||I)&&a("address")},[B,I,a]);let P=(0,we.useMemo)(()=>({required:{value:!0,message:t("addAddressAddressIsRequired")},validate:G=>{let X=Kr(G);if(X&&I)return I;let re=X&&B?B:G;return sa(re,b)?V(re):t("addAddressAddressInvalid")}}),[b,I,B,V,t]),H={required:{value:!0,message:t("addAddressLabelIsRequired")}};return we.default.createElement(fo,{onSubmit:s(L)},we.default.createElement(O,null,t("editAddressPrimaryText")),we.default.createElement(K,null,we.default.createElement(Vi,{icon:we.default.createElement(Ga,null,we.default.createElement(Ba,{fill:"#2d2d2d",width:38})),primaryText:t("editAddressPrimaryText")}),we.default.createElement(Tr,null,we.default.createElement(ba,{name:"chainID",control:n,defaultValue:e.chainID,render:({field:{onChange:G,value:X}})=>l.length===1?we.default.createElement(we.default.Fragment,null):we.default.createElement(_i,{onChange:G,value:X})}),we.default.createElement(mo.WithWarning,{defaultValue:e.label,type:"text",warning:!!r.label,warningMessage:r.label?.message,autoComplete:"off",...i("label",H)}),we.default.createElement(Li.WithWarning,{defaultValue:e.address,warning:!!r.address,warningMessage:r.address?.message,autoComplete:"off",spellCheck:"false",...i("address",P)})),we.default.createElement(By,{onClick:F},t("editAddressRemove"))),we.default.createElement(gt,{primaryDisabled:N,primaryLoading:N,primaryText:t("commandSave"),secondaryText:t("commandCancel"),onPrimaryClicked:s(L),onSecondaryClicked:o}))}),By=f(E).attrs({size:16,weight:500,lineHeight:19,color:"#666666"})`
  text-decoration: none;
  margin-bottom: 30px;
  &:hover {
    color: #999999;
  }
`;var Ly=_e({position:"relative"}),Oy=_e({borderRadius:"full",size:40,display:"flex",justifyContent:"center",alignItems:"center",backgroundColor:"accentPrimary"}),Wy=_e({position:"absolute",bottom:"-2",right:"-2"}),Gf=At.default.memo(({recentAddress:e})=>{let{address:t}=e,{t:o}=S(),{pushDetailView:r}=U(),n=Kr(t),i=(0,At.useCallback)(()=>{r(At.default.createElement(kc,{recentAddress:e}))},[e,r]);return At.default.createElement(oo,{onClick:i,topLeft:{text:o("settingsAddressBookNoLabel"),color:"textSecondary"},bottomLeft:n?t:be(t,4),end:At.default.createElement(kn,{backgroundColor:"accentPrimary",color:"textPrimary",icon:"Plus",size:32})})}),Rf=At.default.memo(({isSingleChainEnabled:e,savedAddress:t})=>{let{address:o,chainID:r,label:n}=t,{pushDetailView:i}=U(),s=Kr(o),a=(0,At.useCallback)(()=>{i(At.default.createElement(_f,{savedAddress:t}))},[t,i]);return At.default.createElement(oo,{onClick:a,start:At.default.createElement("div",{className:Ly},At.default.createElement(Uy,{label:n}),e?null:At.default.createElement("div",{className:Wy},At.default.createElement(po,{networkID:r,size:16,borderColor:"bgRow"}))),topLeft:{text:n,after:s&&At.default.createElement(Au,{networkID:r,address:o})},bottomLeft:s?o:be(o,4)})}),Uy=({label:e})=>{if(!e)return null;let t=e.charAt(0).toUpperCase();return At.default.createElement("div",{className:Oy},At.default.createElement(R,{font:"captionSemibold",color:"bgWallet"},t))};var Vy=_e({width:"100%"}),Hy=_e({marginTop:10,marginBottom:14}),Sc=()=>{let{t:e}=S(),{pushDetailView:t}=U(),{recentAddresses:o,savedAddresses:r}=Sr(),n=o.addresses,i=r.addresses,s=ma(),a=(0,Se.useCallback)(()=>{t(Se.default.createElement(kc,{recentAddress:void 0}))},[t]);return(0,Se.useEffect)(()=>{W.capture("addressBookOpen")},[]),i.length===0&&n.length===0?Se.default.createElement(_y,{openAddAddress:a}):Se.default.createElement(Se.default.Fragment,null,Se.default.createElement(zf,{openAddAddress:a}),Se.default.createElement(K,null,Se.default.createElement(M,{gap:"list"},i.length>0&&i.map(c=>Se.default.createElement(Rf,{key:c.address,savedAddress:c,isSingleChainEnabled:s}))),n.length>0&&Se.default.createElement(Se.default.Fragment,null,Se.default.createElement(R,{font:"bodyMedium",children:e("settingsAddressBookRecentlyUsed"),className:Hy}),Se.default.createElement(M,{gap:"list"},n.map((c,l)=>Se.default.createElement(Gf,{key:`${c.address}-${l}`,recentAddress:c}))))))},zf=({openAddAddress:e})=>{let{t}=S();return Se.default.createElement(O,{icon:Se.default.createElement(br,{fill:"#777"}),onIconClick:e},t("settingsAddressBookPrimary"))},_y=({openAddAddress:e})=>{let{t}=S();return Se.default.createElement(Se.default.Fragment,null,Se.default.createElement(zf,{openAddAddress:e}),Se.default.createElement(M,{gap:"section",alignItems:"center",justifyContent:"center",height:"100%"},Se.default.createElement(kn,{backgroundColor:"bgArea",color:"bgTabBar",icon:"Plus",shape:"circle",size:64}),Se.default.createElement(M,{gap:"list",alignItems:"center",justifyContent:"center",textAlign:"center"},Se.default.createElement(R,{font:"bodySemibold",children:t("settingsAddressBookEmptyHeading")}),Se.default.createElement(R,{font:"caption",color:"textSecondary",children:t("settingsAddressBookEmptyText")}))),Se.default.createElement(rt,null,Se.default.createElement(he,{"data-testid":"add-address-menu-item",className:Vy,onClick:e},t("addAddressPrimaryText"))))};d();p();var jt=A(C());d();p();var $f="enableSolanaCopyTransaction";function Ac(){let e=rp(),t=["settings","solana","enableCopyTransaction"],o=async()=>!!await e.get($f),n=b0({mutationFn:async s=>{await e.set($f,s)},onSuccess:()=>{de.invalidateQueries({queryKey:t})}}),i=xr({queryKey:t,queryFn:o});return{data:i.data,isLoading:i.isPending,mutate:n.mutate}}d();p();var Bo=A(C());var qf=Bo.default.memo(({chainType:e})=>{let{t}=S(),{popDetailView:o}=U(),{data:r}=Aa(),{mutate:n}=wa(),i=r?.setting[e],s=(0,Bo.useCallback)(l=>{if(r){let m={...r,setting:{...r.setting,[e]:l}};n({networkSetting:m}),o()}},[e,r,o,n]),{data:a}=da(),c=(0,Bo.useMemo)(()=>(a?.enabledNetworkIDs.filter(m=>!j.isMainnetNetworkID(m)&&j.getAddressTypes(m).includes(e))??[]).map(m=>({topLeft:{text:j.getNetworkName(m)},active:i===m,type:"check",onClick:()=>s(m)})),[e,a?.enabledNetworkIDs,i,s]);return Bo.default.createElement(Bo.default.Fragment,null,Bo.default.createElement(O,null,t("settingsTestNetworks")),Bo.default.createElement(K,null,Bo.default.createElement(M,{gap:"section"},Bo.default.createElement(z,{rows:c}),Bo.default.createElement(R,{color:"textTertiary",font:"caption",children:t("settingsTestNetworksInfo")}))))});var wc=()=>{let{t:e}=S(),{pushDetailViewCallback:t}=U(),o=_o(),{data:r=V0}=Wp(),{data:n=Al}=Aa(),{mutate:i}=Gp(),{mutate:s}=wa(),{data:a,mutate:c}=Ac(),l=r.isDeveloperMode,m=(0,jt.useCallback)(()=>{if(r){let w={...r,isDeveloperMode:!l};i({developerMode:w}),l&&s({networkSetting:Al})}},[r,l,i,s]),{data:u}=da(),g=(0,jt.useMemo)(()=>u?.enabledNetworkIDs.filter(w=>!j.isMainnetNetworkID(w))??[],[u]),x=(0,jt.useMemo)(()=>!!u?.enabledNetworkIDs.filter(w=>w==="solana:101"),[u]),y=(0,jt.useMemo)(()=>{let w=[];return w.push({id:"toggleTestNetwork",topLeft:{text:e("settingsTestnetMode")},bottomLeft:{text:e("settingsTestnetModeDescription"),truncate:null},type:"toggle",active:l,onClick:m}),l&&n&&o.forEach(T=>{let v=g.find(I=>I===n.setting[T]);if(v){let I=Gy(T);w.push({start:jt.default.createElement(po,{networkID:I,size:32}),topLeft:{text:Un.getDisplayName(T)},topRight:{text:j.getNetworkName(v)},type:"drawer",onClick:t(jt.default.createElement(qf,{chainType:T}))})}}),w},[o,l,n,t,e,g,m]),b=(0,jt.useCallback)(()=>{c(!a)},[a,c]),h=(0,jt.useMemo)(()=>typeof a>"u"||!x?[]:[{type:"toggle",id:"solana-copy-transaction",active:a,onClick:b,topLeft:{text:e("settingsSolanaCopyTransaction")},bottomLeft:{text:e("settingsSolanaCopyTransactionDetails"),truncate:null}}],[x,a,b,e]);return jt.default.createElement(jt.default.Fragment,null,jt.default.createElement(O,null,e("settingsDeveloperPrimary")),jt.default.createElement(K,null,jt.default.createElement(z,{rows:[...y,...h]})))},Gy=e=>{let t;switch(e){case"bip122_p2tr":case"bip122_p2wpkh":case"bip122_p2sh":case"bip122_p2pkh":t=Ne.Bitcoin.Mainnet;break;case"eip155":t=Ne.Ethereum.Mainnet;break;case"solana":t=Ne.Solana.Mainnet;break;case"bip44_ed25519":t=Ne.Sui.Mainnet;break}return t};d();p();var jf=A(i3()),ko=A(C());var Ry="my-custom-url.com",bc=()=>{let{popDetailView:e}=U(),[t,o]=(0,ko.useState)(cn()),{mutateAsync:r}=vp(),n=(0,ko.useMemo)(()=>(0,jf.default)(a=>{r(a),de.clear()},500,{leading:!0,trailing:!0}),[r]),i=(0,ko.useCallback)(a=>{o(a),n(a)},[n]),s=(0,ko.useMemo)(()=>{let a=Object.entries(gr).map(([l,m])=>({topLeft:{text:l},topRight:{text:m.replace("https://","")},active:t===m,type:"check",onClick:()=>{i(m),e()}})),c={topLeft:{text:"Custom",after:ko.default.createElement(mo,{onChange:l=>i(l.currentTarget.value),value:t,placeholder:Ry,autoFocus:!1,type:"text"})},active:!Object.values(gr).includes(t),type:"check",onClick:()=>null};return[...a,c]},[t,e,i]);return ko.default.createElement(ko.default.Fragment,null,ko.default.createElement(O,null,"API Environment"),ko.default.createElement(K,null,ko.default.createElement(z,{rows:s})))};d();p();var Ot=A(C());d();p();var gd=async e=>{await U0(e),await Promise.all([Fp,Yp,Ia].map(t=>t(e)))};var vc=()=>{let{data:e}=He(ln),t=I0(),o=(0,Ot.useCallback)(async(m,u)=>{switch(await T0(new Yr,m,!u),await t(),m){case"enable-monad":{gd(de);break}default:break}},[t]),r=(0,Ot.useCallback)(async()=>{await C0(new Yr,{}),await t(),gd(de)},[t]),n=(0,Ot.useMemo)(()=>e.map((m,u)=>({topLeft:ln[u],type:"toggle",active:m,id:ln[u],onClick:()=>o(ln[u],m)})).sort((m,u)=>m.topLeft.localeCompare(u.topLeft)),[e,o]),[i,s]=(0,Ot.useState)(""),a=pn(i,200),c=!!a&&a.length>0,l=(0,Ot.useMemo)(()=>n.filter(m=>m.topLeft.includes(a)),[n,a]);return Ot.default.createElement(Ot.default.Fragment,null,Ot.default.createElement(O,null,"Feature Flags"),Ot.default.createElement(nc,{value:i,onChange:s}),Ot.default.createElement(K,null,Ot.default.createElement(M,{justifyContent:"center",alignItems:"center",paddingBottom:"screen"},Ot.default.createElement(he,{theme:"primary",onClick:r},"Reset Overrides")),Ot.default.createElement(z,{rows:c?l:n})))};d();p();var Kt=A(C());var zy=["Shape","IconSVGContainer"];function $y(e){let[t]=e;return!zy.includes(t)}var Cc=()=>{let[e,t]=(0,Kt.useState)(""),o=(0,Kt.useMemo)(()=>new RegExp(e.trim(),"i"),[e]);return Kt.default.createElement(Kt.default.Fragment,null,Kt.default.createElement(O,null,"Icon Gallery"),Kt.default.createElement(K,null,Kt.default.createElement("div",{style:{display:"flex",gap:"16px",alignItems:"center",marginBottom:"24px"}},Kt.default.createElement("label",{htmlFor:"search"},"Search:"),Kt.default.createElement("input",{id:"search",type:"text",onChange:r=>t(r.target.value),autoComplete:"off",value:e,style:{backgroundColor:"#181818",border:"1px solid #2f2f2f",borderRadius:"6px",padding:"14px",width:"100%"}})),Kt.default.createElement("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(80px, 1fr))",gap:"32px",justifyContent:"center",alignItems:"center"}},Object.entries(J).filter($y).filter(([r])=>o.test(r)).map(([r,n])=>Kt.default.createElement("div",{key:r,style:{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",cursor:"pointer"},role:"button",onClick:()=>du(r)},Kt.default.createElement("header",{style:{marginBottom:"8px",fontSize:"0.8rem"}},r),Kt.default.createElement(n,{color:"accentPrimary",size:36}))))))};d();p();var ao=A(C());d();p();var me=A(C());var Kf=e=>encodeURI(`https://jwt.io/#debugger-io?token=${e}`),os=["test-key-based-auth.access-token"],jy=()=>xr({queryKey:os,queryFn:async()=>{let e=await er.getUser();if(!e)return"logged-out";let{accessToken:t,userID:o}=e;return console.log("[Phantom Auth] \u2705 Successfully authorized:",Kf(t)),console.log("[Phantom Auth]   userID:",o),{accessToken:t,userID:o}}}),Yf=()=>{let{data:e,status:t}=jy(),{mutateAsync:o,isPending:r}=X0(),[n,i]=(0,me.useState)(!1),[s,a]=(0,me.useState)(!1),c=(0,me.useMemo)(()=>{let l=!1,m,u,g=()=>{};switch(t){case"pending":m="Authorizing \u{1F7E1}",u=me.default.createElement(Pt,{diameter:16});break;case"success":e==="logged-out"?m="Logged Out \u26AA\uFE0F":(m="Authorized \u{1F7E2}",l=!0,g=()=>self.open(Kf(e.accessToken),"_blank"));break;case"error":m="Error \u{1F534}";break;default:throw new Error("Unknown status")}let x=new URL($0());return[{topLeft:"Environment",topRight:x.hostname.match(/^.+?(?=\.phantom)/)?.toString()??""},{topLeft:"Domain",topRight:x.hostname.match(/(?=phantom\.).+$/)?.toString()??""},{topLeft:"Status",topRight:m},{topLeft:"Subject ID",topRight:!!e&&e!=="logged-out"&&be(e.userID)},{topLeft:"View JWT",...l?{type:"link",onClick:g}:{end:u}}]},[e,t]);return r?me.default.createElement(me.default.Fragment,null,me.default.createElement(O,null,"Auth"),me.default.createElement(K,null,me.default.createElement(Pt,null))):me.default.createElement(me.default.Fragment,null,me.default.createElement(O,null,"Auth"),me.default.createElement(K,null,me.default.createElement(M,{gap:"section"},me.default.createElement(z,{rows:c}),t==="success"&&e!=="logged-out"&&me.default.createElement(me.default.Fragment,null,me.default.createElement(he,{onClick:async()=>{await er.logOut(),await de.resetQueries({queryKey:os})}},"Log out"),me.default.createElement(M,{marginTop:32},me.default.createElement(he,{onClick:()=>{i(!n)}},n?"Hide danger zone":"Show danger zone")),n&&me.default.createElement(me.default.Fragment,null,me.default.createElement(R,{font:"heading3",color:"accentAlert",marginTop:"screen"},"Danger Zone"),me.default.createElement(he,{color:"accentAlert",theme:"destructive",onClick:async()=>{console.log("[Phantom Auth] Deleting synced accounts..."),await Ka.deleteSyncedAccounts(),console.log("[Phantom Auth] \u2705 Finished deleting synced accounts!")}},"Delete Synced Accounts"),me.default.createElement(he,{type:"reset",theme:"destructive",onClick:async()=>{console.log("[Phantom Auth] Deleting everything..."),await o(),console.log("[Phantom Auth] \u2705 Finished deleting everything!"),await de.resetQueries({queryKey:os})}},"Delete Profile and Log Out"))),t==="error"&&me.default.createElement(he,{onClick:async()=>{await de.refetchQueries({queryKey:os})}},"Try again"),t!=="pending"&&e==="logged-out"&&me.default.createElement(me.default.Fragment,null,me.default.createElement(he,{disabled:s,onClick:async()=>{try{a(!0),await er.logIn({type:"key"}),await de.refetchQueries({queryKey:os})}catch(l){console.error("Unable to log in with key-based auth. Reason:",l)}finally{a(!1)}}},"Log in with Key"),me.default.createElement(he,{disabled:s,onClick:async()=>{try{a(!0),await er.logIn({type:"email",prompt:"login",provider:"google"}),await de.refetchQueries({queryKey:os})}catch(l){console.error("Unable to log in with email-based auth. Reason:",l)}finally{a(!1)}}},"Log in with Google")))))};d();p();var at=A(C());function Ky(){let e=w0(de,{shouldDehydrateQuery:lm});return new Blob([JSON.stringify(e)]).size}var Yy=new Yr;async function Jy(){let e=await Yy.get("REACT_QUERY_OFFLINE_CACHE");return e?new Blob([e]).size:0}var Tc="Loading...",Jf=()=>{let[e,t]=(0,at.useState)(Tc),[o,r]=(0,at.useState)(Tc),n=(0,at.useCallback)(async()=>{t(Tc),r(Tc),await E0(1e3);let c=await Jy();t(xl(Ky()/1024)+" KB"),r(xl(c/1024)+" KB")},[]);(0,at.useEffect)(()=>{n()},[n]);let i=(0,at.useCallback)(()=>{de.clear(),We.success("Cleared!",{position:"bottom-center"}),n()},[n]),s=(0,at.useCallback)(()=>{de.setQueryData(["test-debug-query",Date.now()],{fakeData:Buffer.from(new Uint8Array(250*1e3)).toString("hex")}),We.success("Added",{position:"bottom-center"}),n()},[n]);return at.default.createElement(at.default.Fragment,null,at.default.createElement(O,null,"Internal Cache"),at.default.createElement(K,null,at.default.createElement(M,{gap:"section"},at.default.createElement(z,{rows:[{topLeft:"In-memory size",topRight:e},{topLeft:"Persisted cache size",topRight:o}]}),at.default.createElement(R,{font:"caption",color:"textSecondary"},"Clear the React Query persisted information using the button below. This won't clear any cached images."),at.default.createElement(he,{onClick:i},"Clear Cache"),at.default.createElement(he,{onClick:s},"Add 500 KB to cache"))))};d();p();var se=A(C());var Qf=()=>{let{data:e=!1}=Mu(),{data:t=ap,fetchStatus:o,isSuccess:r,isError:n}=Fu(!0,fl()),i=(0,se.useMemo)(()=>{let u;switch(t.status){case"connected":u="Connected \u{1F7E2}";break;case"needs-permission":u="Needs Permission \u{1F7E0}";break;case"reconnecting":u="Reconnecting \u{1F7E0}";break;case"waiting-for-approval":u="Waiting for Approval \u{1F7E0}";break;case"not-connected":u="Not Connected \u26AA\uFE0F";break}let g,x,y;return t.status==="connected"&&t.selectedApp?(g=dp(t.selectedApp.app),x=t.selectedApp.version,"blindSigningEnabled"in t.selectedApp?y=t.selectedApp.blindSigningEnabled?"\u2705":"\u274C":y="\u{1F7E0}"):(g="None",x="",y=""),[{topLeft:{text:"Connection Status"},topRight:{text:e?u:"Ledger Not Supported \u274C"},type:"base"},{topLeft:{text:"Fetch Status"},topRight:{text:r?"Done":n?"Error \u274C":"Idle"},end:o==="fetching"?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"},{topLeft:{text:"App"},topRight:{text:g},type:"base"},{topLeft:{text:"App Version"},topRight:{text:x},type:"base"},{topLeft:{text:"Blind Signing Enabled"},topRight:{text:y},type:"base"}]},[o,n,e,r,t]),s=xd(t,"solana"),a=xd(t,"eip155"),c=xd(t,"bip122_p2wpkh"),l=Qy(t),m=rh();return se.default.createElement(se.default.Fragment,null,se.default.createElement(O,null,"Ledger"),se.default.createElement(K,null,se.default.createElement(M,{gap:"section"},se.default.createElement(z,{rows:i}),se.default.createElement(nh,{"data-testid":"clear address cache",onClick:async()=>{await cp(de),await lp(de)}},"Clear address cache"),se.default.createElement(Os,null,"Solana Addresses"),se.default.createElement(z,{rows:s}),se.default.createElement(Os,null,"Ethereum Addresses"),se.default.createElement(z,{rows:a}),se.default.createElement(Os,null,"Bitcoin Segwit Addresses"),se.default.createElement(z,{rows:c}),se.default.createElement(Os,null,"Connect Ledger Account"),se.default.createElement(z,{rows:l}),se.default.createElement(Os,null,"Sign Solana"),se.default.createElement(z,{rows:m}))))},xd=(e,t)=>{let o=(0,se.useMemo)(()=>{let a=[];t==="solana"?a.push({pathType:"bip44Root"}):t==="eip155"&&a.push({pathType:"bip44RootEthereum"});for(let c=0;c<4;++c)a.push({pathType:Un[t].defaultDerivationPathType,index:c});return a},[t]),{data:r={},fetchStatus:n,isSuccess:i,isError:s}=As(e,o,fl(),!0);return(0,se.useMemo)(()=>{let a={topLeft:{text:"Fetch Status"},topRight:{text:i?"Done":s?"Error \u274C":"Idle"},end:n==="fetching"?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"},c=o.map(l=>{let m=ys(l),u=r[m];return{topLeft:{text:m},topRight:{text:u?be(u.address):""},end:!u&&n==="fetching"?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"}});return[a,...c]},[r,n,s,i,o])},yd={pathType:"bitcoinTaproot",index:0},hd={pathType:"bip44Change",index:0},kd={pathType:"bip44Ethereum",index:0},Qy=e=>{let{data:t,isError:o,fetchStatus:r}=As(e,[yd],!0,!0),{data:n,isError:i,fetchStatus:s}=As(e,[hd],!0,!0),{data:a,isError:c,fetchStatus:l}=As(e,[kd],!0,!0),m=(0,se.useMemo)(()=>t?t[ys(yd)]:void 0,[t]),u=(0,se.useMemo)(()=>n?n[ys(hd)]:void 0,[n]),g=(0,se.useMemo)(()=>a?a[ys(kd)]:void 0,[a]),{mutateAsync:x,isPending:y}=Op(),b=(0,se.useCallback)(async()=>{if(!u&&!g)return;let h={accounts:[{name:"Ledger",derivationIndex:0,addresses:[],publicKeys:[],accountIndex:0}]};u&&h.accounts[0].addresses.push({pathType:hd.pathType,value:u.address}),g&&h.accounts[0].addresses.push({pathType:kd.pathType,value:g.address}),m&&h.accounts[0].publicKeys.push({pathType:yd.pathType,value:m.publicKey}),await x(h)},[u,g,m,x]);return(0,se.useMemo)(()=>[{topLeft:{text:"Mutation Status"},topRight:{text:o||i||c?"Error fetching addresses":m||u||g?"Ready":"Need an address"},end:y?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"},{topLeft:{text:"Bitcoin Public Key"},topRight:{text:m?be(m.publicKey):""},end:r==="fetching"?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"},{topLeft:{text:"Solana Address"},topRight:{text:u?be(u.address):""},end:s==="fetching"?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"},{topLeft:{text:"Ethereum Address"},topRight:{text:g?be(g.address):""},end:l==="fetching"?se.default.createElement(Pt,{diameter:22}):void 0,type:"base"},{topLeft:{text:"Add Ledger Account"},onClick:b,disabled:!u&&!g,type:"drawer"}],[r,m,b,g,l,o,c,y,i,u,s])},Zy=async(e,t)=>{let o=new u0.Transaction().add($r.SystemProgram.transfer({fromPubkey:e,toPubkey:e,lamports:26}));o.feePayer=e;let r=o;return r.recentBlockhash=(await t.getLatestBlockhash()).blockhash,o},eh=()=>{let{data:e}=Go(),{data:t}=Tl("solana"),{connection:o}=vi(),{mutateAsync:r}=im(o),n=(0,se.useMemo)(()=>{if(t)return{ownerAddress:t.address,networkID:t.networkID,data:{signature:""},type:"send",display:{summary:{topLeft:{text:"Sending Transaction from Ledger"}}}}},[t]);return(0,se.useCallback)(async()=>{if(console.log("Sign Solana Transaction"),!e||!t||!n){console.log("No valid solana account, can't sign.");return}let i=new $r.PublicKey(t.address),s=await r({accountIdentifier:e,feePayer:i,transaction:await Zy(i,o),pendingTransactionInput:n});console.log("\u2705 Signed! Transaction:"),console.log(`https://solscan.io/tx/${s}`)},[n,e,r,t,o])},th=_t(),Xf="To avoid digital dognappers, sign below to authenticate with CryptoCorgis.",oh=()=>{let{data:e}=ue();return(0,se.useCallback)(async()=>{if(!e){console.log("No valid solana account, can't sign.");return}console.log(`Signing message: "${Xf}"`);let t=await th.sign(e.identifier,{chainType:"solana",signingType:"message",message:Xf});t.status==="success"?console.log("\u2705 Signed! Signature:",t.signature):console.error(t.message)},[e])},rh=()=>{let e=eh(),t=oh();return(0,se.useMemo)(()=>[{topLeft:{text:"Sign Transaction"},type:"drawer",onClick:e},{topLeft:{text:"Sign Message"},type:"drawer",onClick:t}],[e,t])},Os=f(E).attrs({size:16,weight:500,lineHeight:22,noWrap:!0})`
  margin-bottom: 8px;
`,nh=f(oe)`
  margin-bottom: 8px;
`;d();p();var Z=A(C());var eg=e=>encodeURI(`https://jwt.io/#debugger-io?token=${e}`),Sd=["test-key-seedless-recovery.access-token"],ih=()=>xr({queryKey:Sd,queryFn:async()=>{let e=await er.getUser();if(!e)return"logged-out";let{accessToken:t,userID:o}=e;return console.log("[Phantom Auth] \u2705 Successfully authorized:",eg(t)),console.log("[Phantom Auth]   userID:",o),{accessToken:t,userID:o}}}),Zf=f(oe)`
  margin-top: 8px;
  padding: 16px;
  margin-bottom: 8px;
`,sh=e=>{let{register:t,handleSubmit:o}=to(),{mutate:r,status:n}=bm(),i=n==="pending"?"\u23F3":n==="success"?"Recover \u2705":n==="error"?"Recover \u274C":"Recover";return Z.default.createElement("form",{onSubmit:o(async s=>{r(s.pin)})},Z.default.createElement(vr,null,"Recover"),Z.default.createElement(vr,null,"PIN"),Z.default.createElement(Ws,{placeholder:"Enter 4 digit PIN",autoComplete:"off",...t("pin")}),Z.default.createElement(oe,{disabled:n==="pending",type:"submit"},i))},ah=e=>{let{register:t,handleSubmit:o}=to(),{mutate:r,status:n}=wm(),i=n==="pending"?"\u23F3":n==="success"?"Export \u2705":n==="error"?"Export \u274C":"Export";return Z.default.createElement("form",{onSubmit:o(async s=>{r(s.pin)})},Z.default.createElement(vr,null,"Backup"),Z.default.createElement(vr,null,"PIN"),Z.default.createElement(Ws,{placeholder:"Enter 4 digit PIN",autoComplete:"off",...t("pin")}),Z.default.createElement(oe,{disabled:n==="pending",type:"submit"},i))},ch=e=>{let{register:t,handleSubmit:o}=to(),{mutateAsync:r,status:n}=vm(),{refetch:i}=Im(),[s,a]=(0,Z.useState)({isInvalidPin:!1,triesLeft:void 0,pin:""}),[c,l]=(0,Z.useState)(0),m=x=>x instanceof Am&&x.reasonCode===0,u=n==="pending"?"\u23F3":n==="success"?"Verify \u2705":n==="error"?"Verify \u274C":"Verify",g=()=>{let x=s.triesLeft+" tries left.";if(c>0){let y=Math.ceil(c/1e3);x=x+" Try again in "+y+" seconds"}return x};return Z.default.createElement(Z.default.Fragment,null,Z.default.createElement("form",{onSubmit:o(async x=>{try{await r(x.pin),a(y=>({...y,isInvalidPin:!1,triesLeft:void 0,pin:""}))}catch(y){if(m(y)){let h=await i();h.data!==void 0&&h.data>0&&l(h.data),a(w=>({...w,isInvalidPin:!0,triesLeft:y.guessesRemaining,pin:""}))}}})},Z.default.createElement(vr,null,"Verify PIN"),Z.default.createElement(vr,null,"PIN"),Z.default.createElement(Ws,{placeholder:"Enter 4 digit PIN",autoComplete:"off",...t("pin")}),Z.default.createElement(oe,{disabled:n==="pending",type:"submit"},u)),s.isInvalidPin&&Z.default.createElement(vr,null,g()))},lh=e=>{let{register:t,handleSubmit:o}=to(),{mutateAsync:r,status:n}=Tm(),i=n==="pending"?"\u23F3":n==="success"?"Reset \u2705":n==="error"?"Reset \u274C":"Reset";return Z.default.createElement("form",{onSubmit:o(async s=>{let a=await Em();if(a.length<1){console.log("[Phantom Auth]   Vault has no seedless accounts to reset bundle with");return}let c=await Promise.all(a.map(async function(m){let u=m.identifier;return await Ma(s.password,u)}));console.log("[Phantom Auth]   Found "+c.length+"seedless entropies to reset bundle with");let l=await r({pin:s.pin,seedlessEntropies:c})})},Z.default.createElement(vr,null,"Reset PIN"),Z.default.createElement(vr,null,"PIN"),Z.default.createElement(Ws,{placeholder:"Enter password",autoComplete:"off",type:"password",...t("password")}),Z.default.createElement(Ws,{placeholder:"Enter 4 digit PIN",autoComplete:"off",...t("pin")}),Z.default.createElement(oe,{disabled:n==="pending",type:"submit"},i))},dh=e=>{let{mutate:t,data:o,status:r}=Cm();return Z.default.createElement(oe,{type:"submit",disabled:r==="pending",onClick:()=>t()},r==="pending"?"\u23F3":r==="success"&&o?"DELETE BUNDLE \u2705":r==="error"||r==="success"&&!o?"DELETE BUNDLE \u274C":"DELETE BUNDLE")},tg=()=>{let{data:e,status:t}=ih(),[o,r]=(0,Z.useState)(!1),n=(0,Z.useMemo)(()=>{let i,s,a;switch(t){case"pending":i="Authorizing \u{1F7E1}",s=Z.default.createElement(Pt,{diameter:16});break;case"success":e==="logged-out"?(i="Logged Out \u26AA\uFE0F",s=void 0):(i="Authorized \u{1F7E2}",s=Z.default.createElement(Oa,{height:16,width:16}),a=()=>self.open(eg(e.accessToken),"_blank"));break;case"error":i="Error \u{1F534}";break;default:throw new Error("Unknown status")}return[{topLeft:{text:"Auth Status"},topRight:{text:i},type:"base"},{topLeft:{text:"Subject ID"},topRight:{text:e&&e!=="logged-out"?be(e.userID):""},type:"base"}]},[e,t]);return Z.default.createElement(Z.default.Fragment,null,Z.default.createElement(O,null,"Debug Seedless"),Z.default.createElement(K,null,Z.default.createElement(z,{rows:n}),t==="success"&&e!=="logged-out"&&Z.default.createElement(Z.default.Fragment,null,Z.default.createElement(ah,null),Z.default.createElement(sh,null),Z.default.createElement(dh,null),Z.default.createElement(ch,null),Z.default.createElement(lh,null),Z.default.createElement(Zf,{onClick:async()=>{await er.logOut(),await de.resetQueries({queryKey:Sd})}},"Log out")),t==="success"&&e==="logged-out"&&Z.default.createElement(Zf,{disabled:o,loading:o,height:"47px",onClick:async()=>{try{r(!0),await er.logIn({type:"email",prompt:"login",provider:"google"}),await de.refetchQueries({queryKey:Sd})}catch(i){console.error("Unable to log in with email-based auth. Reason:",i)}finally{r(!1)}}},"Login with Google")))},Ws=f.input`
  width: 100%;
  padding: 14px;
  background: #181818;
  border-width: 1px;
  border-style: solid;
  border-color: ${e=>e.warning?"#EB3742":"#2f2f2f"};
  border-radius: ${e=>e.borderRadius?e.borderRadius:"6px"};
  color: white;
  font-size: ${e=>e.fontSize};
  line-height: 19px;
  margin-top: 16px;
  margin-bottom: 16px;
  &::placeholder {
    color: #666666;
  }
  &:focus {
    outline: 0;
  }
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  &[type="number"] {
    -moz-appearance: textfield;
  }
  ::selection {
    background: #ab9ff2;
  }
  ::-moz-selection {
    background: #ab9ff2;
  }
`;d();p();var ct=A(C());var og=()=>{let{data:e}=Qp(!0),{mutateAsync:t}=Zp(),o=(0,ct.useMemo)(()=>Object.values(Xp).map(r=>{let n=e===r.value;return{topLeft:`${r.name} (${r.symbol})`,type:"radio",active:n,onClick:()=>{r.value!==e&&t(r.value)}}}),[e,t]);return ct.default.createElement(ct.default.Fragment,null,ct.default.createElement(O,null,"Multi-Currency Price"),ct.default.createElement(K,null,ct.default.createElement(z,{rows:o}),ct.default.createElement(Us,null,ct.default.createElement(Ir,{value:1})),ct.default.createElement(Us,null,ct.default.createElement(Ir,{value:1e3})),ct.default.createElement(Us,null,ct.default.createElement(Ir,{value:1e4})),ct.default.createElement(Us,null,ct.default.createElement(Ir,{value:1e5})),ct.default.createElement(Us,null,ct.default.createElement(Ir,{value:1e6}))))},Us=({children:e})=>ct.default.createElement("div",{style:{marginBottom:10}},e);var Ic=()=>{let{pushDetailViewCallback:e}=U(),{handleShowModalVisibility:t}=Er(),r=[{topLeft:"API Environment",topRight:Object.keys(gr).find(n=>gr[n]===cn())||"Custom",type:"drawer",onClick:e(ao.default.createElement(bc,null))},{topLeft:"Chain Selector",type:"drawer",onClick:e(ao.default.createElement(ts,{debugMode:!0}))},{topLeft:"Feature Flags",type:"drawer",onClick:e(ao.default.createElement(vc,null))},{topLeft:"Ledger",type:"drawer",onClick:e(ao.default.createElement(Qf,null))},{topLeft:"Auth",type:"drawer",onClick:e(ao.default.createElement(Yf,null))},{topLeft:"Claim Username",type:"drawer",onClick:()=>t("claimUsername")},{topLeft:"Seedless",type:"drawer",onClick:e(ao.default.createElement(tg,null))},{topLeft:"Internal cache",type:"drawer",onClick:e(ao.default.createElement(Jf,null))},{topLeft:"Icons",type:"drawer",onClick:e(ao.default.createElement(Cc,null))},{topLeft:"Multi-currency",type:"drawer",onClick:e(ao.default.createElement(og,null))}];return ao.default.createElement(ao.default.Fragment,null,ao.default.createElement(O,null,"Internal Settings"),ao.default.createElement(K,null,ao.default.createElement(z,{rows:r})))};d();p();var Lr=A(C());var Dc=()=>{let{t:e}=S(),{popDetailView:t}=U(),[o,r]=(0,Lr.useState)(yr.language),n=(0,Lr.useMemo)(()=>fp.map(i=>{let s=xp(o,i.value);return{topLeft:{text:i.displayValue},type:"check",active:s,disabled:s,onClick:()=>{yr.changeLanguage(i.value,()=>{W.addUserProperties({displayLanguage:yr.language}),Ia(de)}),r(i.value),W.capture("displayLanguageChanged",{data:{targetLanguage:i.value}}),t()}}}),[o,t]);return Lr.default.createElement(Lr.default.Fragment,null,Lr.default.createElement(O,null,e("settingsDisplayLanguage")),Lr.default.createElement(K,null,Lr.default.createElement(z,{rows:n})))};d();p();var sr=A(C());var Ec=()=>{let{t:e}=S(),{data:t}=wp(),{mutate:o}=bp(),r=(0,sr.useMemo)(()=>[ha.enum.USE_PHANTOM,ha.enum.USE_METAMASK,ha.enum.ALWAYS_ASK].map(n=>({topLeft:{text:{ALWAYS_ASK:e("alwaysAsk"),USE_PHANTOM:"Phantom",USE_METAMASK:"MetaMask"}[n]},type:"check",active:t===n,id:`metamask-override--${n}`,onClick:()=>{o(n),t!==n&&Ft.success(e("refreshAndReconnectToast"))}})),[t,o,e]);return sr.default.createElement(sr.default.Fragment,null,sr.default.createElement(O,null,e("metaMaskOverride")),sr.default.createElement(K,null,sr.default.createElement(M,{gap:"section"},sr.default.createElement(z,{rows:r}),sr.default.createElement(R,{font:"caption",color:"textSecondary"},e("metaMaskOverrideSettingDescriptionLine1")),t&&sr.default.createElement(R,{font:"caption",color:"textSecondary"},e({ALWAYS_ASK:"selectWalletSettingDescriptionLineAlwaysAsk",USE_PHANTOM:"selectWalletSettingDescriptionLinePhantom",USE_METAMASK:"selectWalletSettingDescriptionLineMetaMask"}[t])))))};d();p();var qo=A(C());d();p();var Yt=A(C());var Pc=()=>{let{t:e}=S(),{handleShowModalVisibility:t}=Er(),o=fn(),r=_o(),{mutateAsync:n}=pa(),{mutateAsync:i}=np(),{availableBitcoinAddressTypes:s,enabledBitcoinAddressTypes:a,handleAddressTypeEnablementChange:c,PREFERRED_BTC_ADDRESS_CONTENT:l}=Jp({isTestnetMode:o,enabledAddressTypes:r,enableAddressTypes:n,disableAddressTypes:i}),{data:m}=te(),u=(0,Yt.useMemo)(()=>m?m.reduce((x,y)=>(y.addresses.every(b=>j.isBitcoinNetworkID(b.networkID))&&x.push(y),x),[]):[],[m]),g=(0,Yt.useMemo)(()=>{let x=[];return s.forEach(y=>{if(y in l){let{title:b,secondaryTitle:h,subtitle:w}=l[y],T=r.includes(y),v=u.some(I=>I.addresses.some(N=>N.addressType===y));x.push({id:`toggle-address-type-${y}`,topLeft:{text:b,after:h?Yt.default.createElement(Ii,{children:h,size:"small"}):void 0},bottomLeft:{component:()=>Yt.default.createElement(R,{align:"left",font:"label",color:"textSecondary",children:w})},active:T,disabled:v,type:"toggle",onClick:()=>{v||(c(y,T),Ft.success(e("settingsEnabledAddressesUpdated")))}})}}),x},[l,s,r,c,u,e]);return Yt.default.createElement(Yt.default.Fragment,null,Yt.default.createElement(O,null,e("settingsPreferredBitcoinAddress")),Yt.default.createElement(K,null,Yt.default.createElement(mh,{onClick:()=>t("bitcoinAddressTypes")},Yt.default.createElement(rg,null,e("settingsEnabledAddresses")),Yt.default.createElement(ng,null,Yt.default.createElement($m,{width:16,fill:"#999"}))),Yt.default.createElement(z,{rows:g}),a.length>1&&Yt.default.createElement(ph,null,e("settingsPreferredBitcoinAddressConnectToAppsExplainer"))))},rg=f(E).attrs({size:16,lineHeight:19,weight:500,margin:"0 0 0 8px",color:"#999"})``,ng=f.div`
  transform: translate(4px, 1px);
  cursor: pointer;
`,ph=f.p`
  line-height: 20px;
  margin-top: 10px;
  margin-bottom: 10px;
  color: #999;
`,mh=f(xt).attrs({alignItems:"center",justifyContent:"flex-start"})`
  margin-top: 0;
  margin-bottom: 8px;
  cursor: pointer;
  &:hover {
    ${rg} {
      color: #ab9ff2;
    }
    ${ng} {
      svg {
        path {
          fill: #ab9ff2;
        }
      }
    }
  }
`;d();p();var So=A(C());var Mc=()=>{let{t:e}=S(),t=ki(),{data:o,isPending:r}=ka(),{mutate:n}=Dp(),i=(0,So.useCallback)((a,c)=>{if(o){let{explorers:l}=o,m={[a]:c};j.isSolanaNetworkID(a)&&(m={[Ne.Solana.Mainnet]:c,[Ne.Solana.Testnet]:c,[Ne.Solana.Devnet]:c}),n({preferredExplorers:{...o,explorers:{...l,...m}}})}},[o,n]),s=(0,So.useMemo)(()=>o?t.map(a=>{let c=j.getChainID(a),l=pp.get(c).getExplorerUrl(a),m=Object.keys(l).map(u=>{let g=u,x=o.explorers[a]===g;return{topLeft:{text:mp[g]},active:x,type:"check",onClick:()=>i(a,g)}});return So.default.createElement(uh,{key:a},So.default.createElement(fh,null,So.default.createElement(po,{networkID:a,size:24}),So.default.createElement(gh,null,e(j.getChainName(c)))),So.default.createElement(z,{rows:m}))}):[],[t,i,o,e]);return r||!o?null:So.default.createElement(So.default.Fragment,null,So.default.createElement(O,null,e("settingsPreferredExplorers")),So.default.createElement(K,null,s))},uh=f.div`
  margin-bottom: 20px;
`,fh=f(xt)`
  margin-bottom: 10px;
`,gh=f(E).attrs({size:16,lineHeight:19,weight:500,margin:"0 0 0 8px"})``;var Fc=()=>{let{t:e}=S(),{pushDetailViewCallback:t}=U(),[o]=fa(["bitcoin"]),r=ua(),n=(0,qo.useMemo)(()=>[{id:"settings-item-display-language",topLeft:{text:e("settingsDisplayLanguage")},topRight:{text:xa(yr.language)},type:"drawer",onClick:t(qo.default.createElement(Dc,null))},{id:"settings-item-metamask-override",topLeft:{text:e("metaMaskOverride")},hidden:!r,type:"drawer",onClick:t(qo.default.createElement(Ec,null))},{id:"settings-item-preferred-explorers",topLeft:{text:e("settingsPreferredExplorers")},type:"drawer",onClick:t(qo.default.createElement(Mc,null))},{id:"settings-item-preferred-bitcoin-address",topLeft:{text:e("settingsPreferredBitcoinAddress")},hidden:!o,type:"drawer",onClick:t(qo.default.createElement(Pc,null))}],[o,r,e,t]);return qo.default.createElement(qo.default.Fragment,null,qo.default.createElement(O,null,e("settingsPreferences")),qo.default.createElement(K,null,qo.default.createElement(z,{rows:n})))};d();p();var Dd=A(n3()),Ze=A(C());d();p();var Or=A(C());d();p();var Zn=A(C());var ig=({title:e,imageUrl:t,subtitle:o,onClick:r})=>Zn.default.createElement(xh,{onClick:r,"data-testid":"trusted_apps_row-button"},Zn.default.createElement(kh,{image:{type:"dapp",src:t},size:Ha.small}),Zn.default.createElement(Nt,null,Zn.default.createElement(yh,null,e),o&&Zn.default.createElement(hh,null,o)),Zn.default.createElement(Fa,{height:12})),xh=f(oe).attrs({borderRadius:"0"})`
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #2a2a2a;
  padding: 12px 16px 12px 12px;
  height: 64px;
  width: 100%;

  &:last-of-type {
    margin-bottom: 0;
  }

  svg > path {
    fill: #999999;
  }
`,yh=f(E).attrs({size:16,weight:600,lineHeight:19,maxWidth:"180px",noWrap:!0})`
  margin-bottom: 2px;
`,hh=f(E).attrs({size:14,weight:600,lineHeight:18,maxWidth:"180px",noWrap:!0,color:"#999"})``,kh=f(yt)`
  margin-right: 10px;
`;d();p();var Wc=A(C());d();p();d();p();d();p();d();p();var wh=A(C());d();p();var Sh=e=>[`auto-confirm:${e}--enabled`];async function Ah(e){let t=await S0.api().post("/auto-confirm/v1/dapp",{domain:e}),{data:o}=t;return!o?.supportedChainIds||!Array.isArray(o.supportedChainIds)?[]:o.supportedChainIds}function Nc(e){return xr({queryKey:Sh(ee(e)),gcTime:3e5,async queryFn(){return await Ah(ee(e))}})}d();p();var ar=A(C()),ag=A(a3());d();p();var sg={settings:void 0,origin:void 0,accountIdentifier:void 0},Ad=gn(e=>({...sg,setSettingsForDomain:(t,o,r)=>e({accountIdentifier:t,origin:o,settings:r}),reset:()=>e(sg)}));var ei=({origin:e,autoSave:t=!1})=>{let{data:o}=ue(),r=o?o.identifier:"",{get:{data:n},set:{mutateAsync:i}}=ya(r,e),s=(0,ag.default)(n),a=$p(),{data:c=[],isLoading:l}=Nc(e),{data:[m,u]}=He(["kill-automatic-approval","enable-automatic-approval-for-all-dapps"]),g=c.length>0,x=!m&&(u||g),{shadowSettings:y,setShadowSettings:b,reset:h,origin:w,accountIdentifier:T}=Ad(G=>({shadowSettings:G.settings,setShadowSettings:G.setSettingsForDomain,origin:G.origin,accountIdentifier:G.accountIdentifier,reset:G.reset}));ar.default.useEffect(()=>{(e!=w||r!=T)&&h()},[e,w,h,r,T]),ar.default.useEffect(()=>{x&&n!=null&&(n!=null&&y==null&&b(r,e,n),n!=null&&t&&!yn(n,y)&&!yn(n,s)&&b(r,e,n))},[n,b,y,e,r,x,t,w,T,s]);let v=ar.default.useMemo(()=>_0(o?.type),[o]),I=ar.default.useMemo(()=>u?a:a.filter(G=>c.includes(G)),[a,c,u]),N=ar.default.useCallback(G=>G?!!y?.networks?.[G]:I.some(X=>!!y?.networks?.[X]),[I,y]),B=um(),V=ar.default.useCallback(G=>{if(!x||v)return;let X=JSON.parse(JSON.stringify(y));if(X?.networks&&X?.networks?.[G]==null)X.networks[G]=!0;else if(X?.networks)X.networks[G]=!X.networks[G];else return!1;X.sessionStartTime=Date.now(),X.maxSessionDuration=vl,b(r,e,X),t&&i(X);let re=X.networks[G]??!1;return B.onAutoConfirmSettingsChanged({origin:e,networkID:G,enabled:re,sessionStartTime:re?X?.sessionStartTime:void 0,maxSessionDuration:re?X?.maxSessionDuration:void 0,view:t?"TRUSTED_APP":"NOTIFICATION"}),re},[x,v,y,b,r,e,t,B,i]),L=ar.default.useCallback(()=>{if(!x||y==null||v)return;let G=!N(),X=JSON.parse(JSON.stringify(y));for(let re of I)X.networks[re]=G;return X.sessionStartTime=Date.now(),X.maxSessionDuration=vl,b(r,e,JSON.parse(JSON.stringify(X))),t&&i(X),B.onAutoConfirmSettingsChanged({origin:e,networkID:"all",enabled:G,sessionStartTime:G?X?.sessionStartTime:void 0,maxSessionDuration:G?X?.maxSessionDuration:void 0,view:t?"TRUSTED_APP":"NOTIFICATION"}),G},[x,y,v,N,b,r,e,t,B,I,i]),F=ar.default.useCallback(async()=>{!x||y==null||v||await i(y)},[y,i,x,v]),P=ar.default.useMemo(()=>y?.sessionStartTime!=null?new Date(y?.sessionStartTime+(y?.maxSessionDuration??0)*1e3):null,[y?.sessionStartTime,y?.maxSessionDuration]),H=l;return ar.default.useMemo(()=>({available:x&&!v,isHardwareAccount:v,toggle:V,networkIDs:I,isActive:N,untilDate:P,settings:y,toggleAll:L,save:F,resetShadowSettings:h,loading:H,isAutoConfirmIsTouched:!yn(y,n)}),[x,v,V,I,N,P,y,L,F,h,H,n])};var ve=A(C());d();p();var Vs=f.section`
  border-radius: 6px;
  margin-bottom: 10px;
  background: #2a2a2a;
`,Hs=f.div`
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 1px;

  & + & {
    border-top: 1px solid #222222;
  }

  /* a card's first level childs are always rows! */
  & > div + div {
    border-top: 1px solid #222222;
  }
`,ti=f.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1px;
  padding: 14px 16px;
  cursor: ${e=>e.onClick?"pointer":"inherit"};

  > * {
    display: flex;
    align-items: center;
  }
`;d();p();var Bc=A(C()),cg=A(C());var bh="xsmall",vh=f.span`
  margin-left: 8px;
`,Lc=({dappUrl:e})=>{let{data:t}=te(),{data:o}=Go(),{accountIndex:r,accountIcon:n,accountName:i}=(0,cg.useMemo)(()=>{if(t){let a=Math.max(0,t.findIndex(l=>l.identifier===o)),c=t[a];if(c)return{accountIndex:a,accountIcon:c.icon,accountName:c.name}}return{accountIndex:0,accountIcon:mn,accountName:""}},[t,o]),s=B0({url:e,format:"hostname"})??"";return Bc.createElement(O,null,Bc.createElement($e,{accountIcon:n,accountIndex:r,accountName:i,size:bh}),Bc.createElement(vh,null,s))};d();p();var fe=A(C());d();p();var bd=A(C());d();p();var Qr=A(C());var Ch=f.div`
  display: flex;
  background-color: ${e=>e.color};
  padding: 16px;
  width: 100%;
  border-width: 1px;
  border-style: solid;
  border-color: ${e=>e.color};
  border-radius: 16px;
  gap: 8px;
`,Th=f.div`
  padding: 3px;
`,Ih=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left",wordBreak:"break-word"})``,wd=Ht.colors.legacy.bgRow,wt=({message:e,variant:t,...o})=>{let r,n,i;switch(t){case 1:r=Ht.colors.legacy.accentAlert,n=Ht.colors.legacy.bgWallet,i=Qr.createElement(J.AlertOctagon,{color:"bgWallet",size:18});break;case 2:r=Ht.colors.legacy.accentWarning,n=Ht.colors.legacy.bgWallet,i=Qr.createElement(J.AlertTriangle,{color:"bgWallet",size:18});break;case 3:r=wd,n=Ht.colors.legacy.accentAlert,i=Qr.createElement(J.AlertCircle,{color:"accentAlert",size:18});break;case 4:r=wd,n=Ht.colors.legacy.accentWarning,i=Qr.createElement(J.AlertCircle,{color:"accentWarning",size:18});break;case 5:r=wd,n=Ht.colors.legacy.textPrimary,i=Qr.createElement(J.Info,{color:"textPrimary",size:18});break;default:throw new Error(`Unknown variant: ${t}`)}return Qr.createElement(Ch,{color:r,...o},Qr.createElement(Th,null,i),Qr.createElement(Ih,{color:n,padding:"4px 0","data-testid":"approval-warning-text",className:`variant-${t}`},e))};var lg=({reason:e})=>{let t=Ea(e);return t==null?null:bd.createElement(wt,{message:bd.createElement(Ro,{i18nKey:t}),variant:4})};var Eh=f(E).attrs({size:14,weight:600,lineHeight:17,color:"#999999",textAlign:"left"})`
  padding: 0 4px;
  margin-bottom: 8px;
`,Ph=f.div`
  margin-top: 16px;
`,vd=f(E)`
  font-size: 16px;
  font-weight: 600;
  line-height: 21px;
  color: #ffffff;
`,Cd=f(Nt)`
  align-items: flex-start;
`,Td=f(Nt)`
  align-items: flex-end;
  width: auto;
`,dg=f(E)`
  font-weight: 500;
  font-size: 13px;
  line-height: 16px;
  color: #999999;
  margin-top: 2px;
`,pg=f.div`
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 0 4px;
  margin-top: 8px;
`,Id=f(E)`
  font-weight: 400;
  font-size: 14px;
  line-height: 17px;
  color: #999999;
  text-align: left;
`,Oc=({dappUrl:e,autoConfirmStatusCode:t,networkID:o,autoSave:r=!1})=>{let{t:n}=S(),i=F0(e)??"",{isActive:s,isHardwareAccount:a,untilDate:c,networkIDs:l,toggle:m,settings:u,toggleAll:g}=ei({origin:i,autoSave:r}),x=fe.useCallback(h=>async()=>{m(h)},[m]),y=()=>fe.createElement(Vs,{style:{marginTop:8,marginBottom:4}},fe.createElement(Hs,null,fe.createElement(ti,null,fe.createElement(Cd,null,fe.createElement(vd,null,n("settingsTrustedAppDetailsAutoConfirmActiveTitle")),c!=null&&s()?fe.createElement(dg,null,n("settingsTrustedAppsAutoConfirmActiveUntil",{formattedTimestamp:xs(c).toLowerCase()})):null),fe.createElement(Td,null,fe.createElement(ja,{id:"toggle-active",checked:s(),onChange:g}))),l.map(h=>fe.createElement(ti,{key:`automatic-approval--${h}`},fe.createElement(Cd,null,fe.createElement(vd,null,j.getNetworkName(h))),fe.createElement(Td,null,fe.createElement(ja,{id:`toggle-${h.replace(":","--")}`,checked:!!u?.networks?.[h],onChange:x(h)})))))),b=()=>o==null?null:fe.createElement(Vs,{style:{marginTop:8,marginBottom:4}},fe.createElement(Hs,null,fe.createElement(ti,{key:`automatic-approval--${o}`},fe.createElement(Cd,null,fe.createElement(vd,null,j.getNetworkName(o)),c!=null&&s(o)?fe.createElement(dg,null,"Until ",xs(c)):null),fe.createElement(Td,null,fe.createElement(ja,{id:`toggle-${o.replace(":","--")}`,checked:!!u?.networks?.[o],onChange:x(o)})))));return fe.createElement(Ph,null,fe.createElement(Eh,null,n("settingsAutoConfirmHeader")),t!=null&&t!="DISABLED"&&t!="OK"&&fe.createElement(lg,{reason:t}),o==null?y():b(),a?fe.createElement(pg,null,fe.createElement(Id,null,n("trustedAppAutoConfirmDisabledHardwareAccount"))):fe.createElement(pg,null,fe.createElement(Id,null,n("trustedAppAutoConfirmDisclaimer1")),fe.createElement(Id,null,n("trustedAppAutoConfirmDisclaimer2"))))};var mg=({trustedApp:e,onDisconnect:t,dappUrl:o})=>{let{t:r}=S(),{available:n,loading:i}=ei({origin:new URL(o).origin}),s=ee(e.dappMeta.appUrl);return ve.createElement(ve.Fragment,null,ve.createElement(Lc,{dappUrl:e.dappMeta.appUrl}),ve.createElement(Wi,{isLoading:i},ve.createElement(K,null,ve.createElement(Mh,null,ve.createElement(Wh,{image:{type:"dapp",src:e.dappMeta.imageUrl},size:Ha.medium}),ve.createElement(Nh,null,ve.createElement(Bh,null,e.dappMeta.title))),ve.createElement(Fh,null,ve.createElement(Oh,null,r("appInfo")),ve.createElement(Vs,{style:{marginTop:8}},ve.createElement(Hs,null,e.lastConnectedTimestamp&&ve.createElement(ti,null,ve.createElement(E,{size:14,lineHeight:17,textAlign:"left",color:"#999",style:{gap:10}},r("lastUsed")),ve.createElement(E,{size:14,weight:500,lineHeight:17,whiteSpace:"normal",wordBreak:"break-word","data-testid":"networkName"},P0(e.lastConnectedTimestamp??0))),ve.createElement(ti,null,ve.createElement(E,{size:14,lineHeight:17,textAlign:"left",color:"#999",style:{gap:10}},r("url")),ve.createElement(_h,null,ve.createElement(Vn,{alignment:"bottomRight",index:0,content:ve.createElement(Lh,null,s)},ve.createElement(Vh,null,ve.createElement(Uh,{href:e.dappMeta.appUrl},s)))))))),n&&ve.createElement(Oc,{dappUrl:o,autoSave:!0}),ve.createElement(Hh,{onClick:t,"data-testid":"trusted-apps-revoke-button"},"Disconnect"))))},Mh=f.div`
  display: flex;
  flex-direction: row;
  align-items: center;
`,Fh=f.div`
  margin-top: 16px;
`,Nh=f.div`
  overflow: hidden;
`,Bh=f(E).attrs({size:28,color:"#FFFFFF",lineHeight:34,weight:600,fontFamily:"Inter"})`
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: left;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  max-height: 68px;
`,Lh=f.div`
  max-width: 240px;
  padding: 14px;
  font-size: 14px;
  line-height: 17px;
`,Oh=f(E).attrs({size:14,weight:600,lineHeight:17,color:"#999",textAlign:"left"})`
  padding: 0 4px;
`,Wh=f(yt)`
  margin-right: 12px;
`,Uh=f.a.attrs({target:"_blank",rel:"noopener noreferrer"})`
  color: #ab9ff2;
  text-decoration: none;
  cursor: pointer;
  font-size: 14px;
  line-height: 17px;
  font-weight: 400;

  text-overflow: ellipsis;
  white-space: nowrap;
  margin-left: 32px;
`,Vh=f.div`
  overflow: hidden;
  text-overflow: ellipsis;
  color: #ab9ff2;
`,Hh=f(oe)`
  background: none;
  color: #eb3742;
  text-align: right;
  background: #2a2a2a;
  border-radius: 6px;
  justify-content: flex-start;
  padding: 14px 16px;
  margin-top: 24px;
`,_h=f.div`
  overflow: hidden;

  & > div {
    overflow: hidden;
  }

  & > div > div {
    overflow: hidden;
  }
`;var ug=({trustedApp:e,onDisconnect:t})=>{let{data:o}=ue(),r=o?o.identifier:"",{remove:{mutateAsync:n}}=ya(r,new URL(e.dappMeta.appUrl).origin),i=Wc.useCallback(async()=>{await n(),t()},[n,t]);return Wc.createElement(mg,{trustedApp:e,onDisconnect:i,dappUrl:e.dappMeta.appUrl})};var fg=({title:e,trustedApps:t,withAutoConfirm:o,onRemoveTrustedApplication:r})=>{let{pushDetailView:n,popDetailView:i}=U(),{t:s}=S(),a=Or.useCallback((l,m)=>()=>{r(new URL(l).origin),i(),We.dismiss(),We.success(s("settingsTrustedAppsRevokeToast",{trustedApp:m}))},[r,i,s]),c=Or.useCallback((l,m)=>()=>{n(Or.createElement(ug,{trustedApp:l,onDisconnect:a(l.dappMeta.appUrl,m)}))},[a,n]);return Or.createElement(Rh,{key:e},Or.createElement(Gh,null,s(e)),Or.createElement(zh,null,t.map(l=>{let m=ee(l.dappMeta.appUrl),u=Ap(l,!!o);return Or.createElement("li",{key:`${l.dappMeta.appUrl}`},Or.createElement(ig,{imageUrl:l.dappMeta.imageUrl,title:m,subtitle:u?s("settingsTrustedAppsAutoConfirmActiveUntil",{formattedTimestamp:u}):void 0,timestamp:l.lastConnectedTimestamp,onClick:c(l,m)}))})))},Gh=f(E).attrs({size:14,weight:600,lineHeight:17,color:"#777777",textAlign:"left"})`
  padding: 0 4px;
  margin-bottom: 8px;
`,Rh=f.li`
  margin-bottom: 16px;
`,zh=f.ol`
  border-radius: 6px;
  overflow: hidden;

  li {
    list-style: none;

    & + li {
      margin-top: 1px;
    }
  }
`;var $h=f(E)`
  font-weight: 500;
  font-size: 16px;
  line-height: 19px;
  color: #999999;
`,qh=f.div`
  height: 100%;
  font-weight: 500;
  font-size: 18px;
  line-height: 22px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
`,jh=({accountIdentifier:e,trustedAppsByAccount:t})=>{let{t:o}=S(),{data:[r]}=He(["kill-automatic-approval"]);return(0,Ze.useMemo)(()=>{let n=Sp({accountIdentifier:e,autoConfirmEnabled:!r,trustedAppsByAccount:t});if(!n)return[];let{autoConfirmEnabledApps:i,timeBucketedApps:s}=n;return[{category:o("settingsTrustedAppsAutoConfirm"),trustedApps:i,withAutoConfirm:!0},{category:o("settingsTrustedAppsToday"),trustedApps:s.Today},{category:o("settingsTrustedAppsYesterday"),trustedApps:s.Yesterday},{category:o("settingsTrustedAppsLastWeek"),trustedApps:s["Last Week"]},{category:o("settingsTrustedAppsBeforeYesterday"),trustedApps:s.Earlier}].filter(a=>(a?.trustedApps??[]).length>0)},[e,t,r,o])},Kh=()=>{let{t:e}=S(),{data:[t]}=He(["kill-automatic-approval"]),o=(0,Ze.useMemo)(()=>({textPrimary:e("settingsTrustedAppsPrimary"),noConnections:e("settingsNoConnections")}),[e]),{data:r,isPending:n}=ue(),{data:i,isPending:s}=bi(),{mutate:a}=hp(),c=r?r.identifier:"",{removeAll:{mutate:l}}=kp(c),m=i?i.apps[c]:null,u=jh({accountIdentifier:c,trustedAppsByAccount:m}),g=(0,Ze.useCallback)(h=>{if(i){let w=(0,Dd.default)(i);delete w.apps[c][h],a({trustedApps:w})}We.success(e("settingsTrustedAppsRevokeToast",{trustedApp:ee(h)}))},[c,i,a,e]),x=(0,Ze.useCallback)(()=>{if(i){let h=(0,Dd.default)(i);h.apps[c]={},a({trustedApps:h})}We.success(e("settingsTrustedAppsDisconnectAllToast"))},[c,i,a,e]),y=(0,Ze.useCallback)(async()=>{await l(),We.success(e("settingsTrustedAppsEndAutoConfirmForAllToast"))},[l,e]);return{data:(0,Ze.useMemo)(()=>({trustedAppsByCategory:u,i18nStrings:o,removeTrustedApplication:g,onDisconnectFromAll:x,onEndAutoConfirmForAll:t?void 0:y}),[u,o,g,x,t,y]),isLoadingSelectedAccount:n,isLoadingTrustedApps:s}},Uc=()=>{let{data:e,isLoadingSelectedAccount:t,isLoadingTrustedApps:o}=Kh();return o||t?null:Ze.default.createElement(Yh,{...e})},Yh=Ze.default.memo(e=>{let{trustedAppsByCategory:t,i18nStrings:o,removeTrustedApplication:r,onDisconnectFromAll:n,onEndAutoConfirmForAll:i}=e,{t:s}=S(),a=[];return n&&a.push({key:"disconnect-all",label:s("settingsTrustedAppsDisconnectAll"),variant:"warning",onClick:n}),i&&a.push({key:"end-auto-confirm-all",label:s("settingsTrustedAppsEndAutoConfirmForAll"),variant:"warning",onClick:i}),Ze.default.createElement(Ze.default.Fragment,null,Ze.default.createElement(O,{items:a,icon:Ze.default.createElement(jm,{"data-testid":"trusted-apps-more-icon"})},o.textPrimary),Ze.default.createElement(K,null,t.length>0?Ze.default.createElement("ol",null,t.map(({category:c,trustedApps:l=[],withAutoConfirm:m})=>Ze.default.createElement(fg,{key:c,title:c,trustedApps:l,withAutoConfirm:m,onRemoveTrustedApplication:r}))):Ze.default.createElement(qh,null,Ze.default.createElement($h,null,o.noConnections))))});var gg=()=>{let{t:e}=S(),{pushDetailView:t,pushDetailViewCallback:o}=U(),{data:r=[]}=Do("all"),{data:n=[]}=te(),{data:i}=ue(),s=i?.identifier??"",a=(0,Q.useMemo)(()=>{if(!n||r.length===0)return!1;let v=n.filter(I=>I.type==="seed"||I.type==="seedless");return r.length>1||n.length>v.length},[n,r.length]),c=yi(),l=hi(),m=c?.length<l.length?c?.length:e("settingsActiveNetworksAll"),[u]=fa(["bitcoin"]),g=ua(),{savedAddresses:x}=Sr(),y=x&&x.addresses.length>0?x.addresses.length:"",{data:b}=bi(),h=(0,Q.useMemo)(()=>{let v=b?.apps;if(v&&Object.keys(v)?.length===0)return"";let I=v?.[s],N=Object.keys(I??{}).length;return N?String(N):""},[b?.apps,s]),{data:w}=Ip();return(0,Q.useMemo)(()=>{let v=[];n?.forEach((L,F)=>{v.push({title:L.name,description:`${e("settingsManageAccounts")} > ${e("commandEdit")}`,icon:Q.default.createElement($e,{size:"small",accountIcon:L.icon,accountIndex:F+1,accountName:L.name}),onClick:o(Q.default.createElement($i,{accountIdentifier:L.identifier})),tags:[e("settingsSearchResultAccount"),e("commandEdit"),i?.identifier===L.identifier?e("settingsSearchResultSelected"):""]})}),i&&(i.type==="privateKey"||i.type==="seed"||i.type==="seedVault")&&(v.push({title:e("exportSecretYourPrivateKey"),description:i.name,icon:Q.default.createElement(Pi,{width:28,fill:"#AB9FF2"}),onClick:o(Q.default.createElement(Oi,{type:"privateKey",onSuccess:L=>{let F=i?.addresses||[];if(F.length===1){let[P]=F;t(Q.default.createElement(zi,{password:L,chainAddress:P,accountIdentifier:i.identifier}))}else t(Q.default.createElement(ec,{password:L,accountIdentifier:i.identifier}))}})),tags:[e("settingsSearchResultExport"),e("exportSecretPrivateKey")]}),i?.type==="seed"&&v.push({title:e("exportSecretYourSecretRecoveryPhrase"),description:i.name,icon:Q.default.createElement(Pi,{width:28,fill:"#AB9FF2"}),onClick:o(Q.default.createElement(Oi,{type:"mnemonic",onSuccess:L=>{t(Q.default.createElement(Za,{accountIdentifier:s,password:L}))}})),tags:[e("settingsSearchResultExport"),e("settingsSearchResultSeed")]}));let I=[{icon:Q.default.createElement(Bm,{width:24,fill:"#AB9FF2"}),title:e("settingsManageAccounts"),onClick:o(Q.default.createElement(es,null)),description:String(n?.length||"")},{icon:Q.default.createElement(Mi,{width:24,fill:"#AB9FF2"}),title:e("settingsPreferences"),onClick:o(Q.default.createElement(Fc,null))},{icon:Q.default.createElement(Pi,{width:28,fill:"#AB9FF2"}),title:e("settingsSecurityPrimary"),onClick:o(Q.default.createElement(qa,null))},{icon:Q.default.createElement(La,{width:24,fill:"#AB9FF2"}),title:e("settingsActiveNetworks"),onClick:o(Q.default.createElement(ts,null)),description:String(m)},{icon:Q.default.createElement(zm,{width:24,fill:"#AB9FF2"}),title:e("settingsAddressBookPrimary"),onClick:o(Q.default.createElement(Sc,null)),description:String(y)},{icon:Q.default.createElement(Rm,{width:24,fill:"#AB9FF2"}),title:e("settingsTrustedAppsPrimary"),onClick:o(Q.default.createElement(Uc,null)),description:String(h)},{icon:Q.default.createElement(Gm,{width:24,fill:"#AB9FF2"}),title:e("settingsDeveloperPrimary"),onClick:o(Q.default.createElement(wc,null))},{icon:Q.default.createElement(_m,{fill:"#AB9FF2",width:24}),title:e("walletMenuItemsHelpAndSupport"),onClick:()=>self.open(gi),rightNode:Q.default.createElement(Oa,{height:12,width:12})},{icon:Q.default.createElement(Mm,{color:"#AB9FF2",size:"20px"}),title:e("settingsAbout"),onClick:o(Q.default.createElement(ic,null))}],N=[{icon:Q.default.createElement(Mi,{width:24,fill:"#AB9FF2"}),title:e("settingsDisplayLanguage"),description:xa(yr.language),onClick:o(Q.default.createElement(Dc,null)),tags:[e("settingsPreferences")]},{icon:Q.default.createElement(Mi,{width:24,fill:"#AB9FF2"}),title:e("settingsPreferredExplorers"),onClick:o(Q.default.createElement(Mc,null)),tags:[e("settingsPreferences")]}];u&&N.push({icon:Q.default.createElement(Mi,{width:24,fill:"#AB9FF2"}),title:e("settingsPreferredBitcoinAddress"),onClick:o(Q.default.createElement(Pc,null)),tags:[e("settingsPreferences")]}),g&&N.push({icon:Q.default.createElement(Mi,{width:24,fill:"#AB9FF2"}),title:e("metaMaskOverride"),onClick:o(Q.default.createElement(Ec,null)),tags:[e("settingsPreferences")]});let B=[{icon:Q.default.createElement(Pi,{width:28,fill:"#AB9FF2"}),title:e("settingsChangePasswordPrimary"),onClick:o(Q.default.createElement(Tu,null)),tags:[e("settingsSecurityPrimary")]},{icon:Q.default.createElement(Pi,{width:28,fill:"#AB9FF2"}),title:e("settingsAutoLockTimerPrimary"),description:M0(e,w),onClick:o(Q.default.createElement(Cu,null)),tags:[e("settingsSecurityPrimary")]},{icon:Q.default.createElement(La,{width:24,fill:"#AB9FF2"}),title:e("settingsDownloadApplicationLogs"),onClick:o(Q.default.createElement(Du,null)),tags:[e("settingsSecurityPrimary")]}];a&&B.push({title:e("settingsRemoveSecretPhrase"),type:"alert",onClick:o(Q.default.createElement($a,{navigationCallback:L=>t(Q.default.createElement(Eu,{seedIdentifier:L}))})),tags:[e("settingsSecurityPrimary")]});let V=[{icon:Q.default.createElement(Ei,{width:22,height:18,fill:"#AB9FF2"}),title:"Internal Settings",onClick:o(Q.default.createElement(Ic,null)),description:"Debug"},{icon:Q.default.createElement(La,{width:24,height:18,fill:"#AB9FF2"}),title:"API Environment",description:Object.keys(gr).find(L=>gr[L]===cn())||"Custom",onClick:o(Q.default.createElement(bc,null))},{icon:Q.default.createElement(Ei,{width:22,height:18,fill:"#AB9FF2"}),title:"Feature Flags",onClick:o(Q.default.createElement(vc,null))},{icon:Q.default.createElement(Ei,{width:22,height:18,fill:"#AB9FF2"}),title:"Icons Gallery",onClick:o(Q.default.createElement(Cc,null))}];return[...v,...I,...N,...B,...oa?V:[]]},[n,h,m,u,g,w,t,o,y,i,s,a,e])};var xg=({searchQuery:e})=>{let t=gg(),o=yp(t,e);return Vc.default.createElement(Vc.default.Fragment,null,o.map(({title:r,description:n,icon:i,type:s,onClick:a,rightNode:c},l)=>Vc.default.createElement(D1,{key:`settings-search-item-${l}`,title:r,description:n,icon:i,type:s,onClick:a,rightNode:c,stackTitleDescription:!0})))};d();p();var cr=A(C());var yg=()=>{let{data:e}=Ep(),{mutate:t}=Pp(),o=(0,cr.useCallback)(()=>{if(e==null)return;let n=!e;t({enabled:n}),We.success(ra("refreshWebpageToApplyChanges")),W.capture("solanaActionsSettingsChanged",{data:{enabled:n}})},[e,t]),r=[{id:"toggle-0",topLeft:{text:"Solana Actions on X.com"},type:"toggle",active:e===!0,onClick:o}];return cr.default.createElement(cr.default.Fragment,null,cr.default.createElement(O,null,ra("settingsExperimentalTitle")),cr.default.createElement(K,null,cr.default.createElement(M,{gap:"list"},cr.default.createElement(z,{rows:r}),cr.default.createElement(M,{paddingX:4},cr.default.createElement(R,{font:"caption",children:ra("settingsExprimentalSolanaActionsSubtitle"),color:"textSecondary"})))))};var Hc=()=>{let{t:e}=S(),[t,o]=(0,ae.useState)(""),r=pn(t,200),n=!!r&&r.length>0,i=Xa(c=>c.lock),s=()=>{W.capture("walletMenuLockWallet"),i()},{settingsRows:a}=Jh();return ae.default.createElement(ae.default.Fragment,null,ae.default.createElement(O,null,e("settings")),ae.default.createElement(nc,{value:t,onChange:o}),ae.default.createElement(K,null,n?ae.default.createElement(xg,{searchQuery:r}):ae.default.createElement(M,{gap:"section"},ae.default.createElement(T1,null),a.map((c,l)=>ae.default.createElement(z,{key:l,rows:c})))),ae.default.createElement(rt,null,ae.default.createElement(oe,{"data-testid":"lock-menu-item",onClick:s},e("walletMenuItemsLockWallet"))))},Jh=()=>{let{t:e}=S(),{pushDetailViewCallback:t}=U(),{data:o=[]}=te(),{data:r=""}=Go(),n=yi(),i=hi(),s=n?.length<i.length?n?.length:e("settingsActiveNetworksAll"),{savedAddresses:a}=Sr(),c=a&&a.addresses.length>0?a.addresses.length:"",{data:l}=bi(),m=(0,ae.useMemo)(()=>{let h=l?.apps;if(h&&Object.keys(h)?.length===0)return"";let w=h?.[r],T=Object.keys(w??{}).length;return T?String(T):""},[l?.apps,r]),{data:u}=He(ln),g="enable-experimental-solana-actions",x=ln.findIndex(h=>h===g),y=u[x];return{settingsRows:(0,ae.useMemo)(()=>{let h={color:"accentPrimary",size:16};return[[{id:"settings-item-manage-accounts",start:ae.default.createElement(J.WalletClosed,{...h}),topLeft:{text:e("settingsManageAccounts")},topRight:{text:String(o?.length||"")},type:"drawer",onClick:t(ae.default.createElement(es,null))},{id:"settings-item-preferences",start:ae.default.createElement(J.Sliders,{...h}),topLeft:{text:e("settingsPreferences")},type:"drawer",onClick:t(ae.default.createElement(Fc,null))},{id:"settings-item-security-and-privacy",start:ae.default.createElement(J.Shield,{...h}),topLeft:{text:e("settingsSecurityPrimary")},type:"drawer",onClick:t(ae.default.createElement(qa,null))}],[{start:ae.default.createElement(J.Globe,{...h}),topLeft:{text:e("settingsActiveNetworks")},topRight:{text:s},type:"drawer",id:"settings-item-active-networks",onClick:t(ae.default.createElement(ts,null))},{id:"settings-item-address-book",start:ae.default.createElement(J.Smile,{...h}),topLeft:{text:e("settingsAddressBookPrimary")},topRight:{text:String(c)},type:"drawer",onClick:t(ae.default.createElement(Sc,null))},{id:"settings-item-trusted-apps",start:ae.default.createElement(J.Layers,{...h}),topLeft:{text:e("settingsTrustedAppsPrimary")},topRight:{text:String(m)},type:"drawer",onClick:t(ae.default.createElement(Uc,null))}],[{id:"settings-item-developer-settings",start:ae.default.createElement(J.Terminal,{...h}),topLeft:{text:e("settingsDeveloperPrimary")},type:"drawer",onClick:t(ae.default.createElement(wc,null))},{start:ae.default.createElement(J.Eye,{...h}),topLeft:{text:"Internal Settings"},topRight:{text:"Debug"},hidden:!oa,type:"drawer",onClick:t(ae.default.createElement(Ic,null))},{id:"settings-item-experimental-features",start:ae.default.createElement(J.Cpu,{...h}),topLeft:{text:e("settingsExperimentalTitle")},hidden:!y,type:"drawer",onClick:t(ae.default.createElement(yg,null))}],[{id:"settings-item-help-and-support",start:ae.default.createElement(J.HelpCircle,{...h}),topLeft:{text:e("walletMenuItemsHelpAndSupport")},type:"link",onClick:()=>self.open(gi)},{id:"settings-item-about",start:ae.default.createElement(J.LogoStroke,{...h}),topLeft:{text:e("settingsAbout")},type:"drawer",onClick:t(ae.default.createElement(ic,null))}]]},[e,o?.length,s,c,m,y,t])}};var hg=jo.default.memo(({children:e,singleView:t})=>{let[o,r]=(0,jo.useState)(void 0),[n,i]=(0,jo.useState)(!1),s=(0,jo.useCallback)((c,l)=>{r(l??void 0),i(!0)},[]),a=(0,jo.useCallback)(()=>{o&&r(void 0),i(!1)},[o]);return jo.default.createElement(su.Provider,{value:{isOpen:n,showSettingsMenu:s,hideSettingsMenu:a}},e,jo.default.createElement(f1,{isOpen:n,onClose:a},t||jo.default.createElement(Bi,null,o||jo.default.createElement(Hc,null))))});var Xh=new Yr,kg=Ko.default.memo(e=>{let{openOnboarding:t}=e,{state:o,refresh:r,unlock:n}=Xa();return(0,Ko.useEffect)(()=>{o==="unknown"&&r(),o==="onboarding"&&t(),Ja.onLockState(o)},[o,r,t]),(0,Ko.useEffect)(()=>Xh.subscribe(async i=>{i===mm&&await r()}),[r]),Ko.default.createElement(Sn,null,o==="locked"&&Ko.default.createElement(Qh,{key:"locked",exit:{opacity:0},transition:{ease:"easeOut",duration:.5,delay:.5}},Ko.default.createElement(hg,{singleView:Ko.default.createElement(Ya,null)},Ko.default.createElement(m1,{onUnlock:n}))),o==="unlocked"&&Ko.default.createElement(Ko.default.Fragment,{key:"unlocked"},e.children))}),Qh=f(Et.div)`
  height: 100vh;
  z-index: 9999;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  width: 100%;
`;d();p();d();p();d();p();var ri=A(C());d();p();var lt=A(C());d();p();var In=A(C()),Dn=e=>{let{type:t,description:o,domain:r}=e,{t:n}=S(),{originIsBlocklisted:i}=Ci(e.domain),s;"iconUrl"in e&&e.iconUrl&&(s=e.iconUrl),!s&&e.domain&&(s=L0(e.domain)),i&&(s=void 0);function a(l){switch(l){case"APPROVE_TRANSACTION":return"notificationSignatureRequestConfirmTransactionCapitalized";case"APPROVE_TRANSACTIONS":return"notificationSignatureRequestConfirmTransactionsCapitalized";case"SIGN_MESSAGE":return"dappApprovePopupSignMessage";case"CONNECT":return"notificationApplicationApprovalActionButtonConnect";case"SIGN_IN":return"notificationApplicationApprovalActionButtonSignIn"}}let c="textSecondary";return i&&(c="accentAlert"),In.createElement(M,{gap:8},In.createElement(M,{direction:"row",gap:8,alignItems:"center"},In.createElement(yt,{image:{type:"dapp",src:s},size:56}),In.createElement(M,{gap:6},In.createElement(R,{color:"textPrimary",font:"heading3"},n(a(t))),r&&In.createElement(R,{font:"labelMedium",color:c},ee(r??"")))),o&&(typeof o=="string"?In.createElement(R,{font:"caption",color:"textSecondary"},o):o))};function _s(e){let{t}=S(),{domain:o,type:r,onClose:n,icon:i}=e,s=r==="APPROVE_TRANSACTION"||r==="APPROVE_TRANSACTIONS"?t("notificationBalanceChangesText"):void 0,a=[lt.default.createElement(he,{key:"secondary-first",theme:"secondary",onClick:n,"data-testid":"btn-cancel"},t("commandCancel")),lt.default.createElement(he,{key:"secondary-second",theme:"secondary",onClick:()=>!0,"data-testid":"btn-confirm",disabled:!0},t("commandConfirm"))],c=Array.from({length:3}).map(()=>({start:lt.default.createElement(gm,{height:24,width:24}),topLeft:lt.default.createElement(ks,{font:"body",width:80}),topRight:lt.default.createElement(ks,{font:"body",width:80})}));return lt.default.createElement(lt.default.Fragment,null,lt.default.createElement(M,{flex:1,overflow:"auto",padding:"screen",minHeight:0,height:"100%",gap:"section",testID:"scan-loading"},lt.default.createElement(Dn,{domain:o,type:r,iconUrl:i,description:s||lt.default.createElement(lt.default.Fragment,null,lt.default.createElement(ks,{font:"body",width:"90%"}),lt.default.createElement(ks,{font:"body",width:"70%"}))}),lt.default.createElement(z,{rows:c})),lt.default.createElement(M,{padding:"screen"},lt.default.createElement(Zo,null,lt.default.createElement(lt.default.Fragment,null,a))))}d();p();var Ue=A(C());d();p();var Sg=A(C()),rs=e=>{let{t}=S(),o=j.getChainName(e),r="solana";switch(o){case"Solana":r="solana";break;case"Ethereum":r="ethereum";break;case"Polygon":r="polygon";break;case"Base":r="base";break;case"Bitcoin":r="bitcoin";break}return{topLeft:{text:t("notificationTransactionApprovalNetwork"),font:"label"},topRight:{before:Sg.default.createElement(yt,{image:{type:"network",preset:r},size:16}),text:o}}};d();p();var Nd=A(C());d();p();var wg=A(C(),1),bg=A(C(),1),Zr=A(C(),1);var oi=A(C(),1);var Fd=A(C(),1),Zh=e=>typeof e=="function",Pd=(e,t)=>Zh(e)?e(t):e,e8=(()=>{let e=0;return()=>(++e).toString()})(),t8=(()=>{let e;return()=>{if(e===void 0&&typeof self<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),o8=20,_c=new Map,r8=1e3,Ag=e=>{if(_c.has(e))return;let t=setTimeout(()=>{_c.delete(e),Gc({type:4,toastId:e})},r8);_c.set(e,t)},n8=e=>{let t=_c.get(e);t&&clearTimeout(t)},Md=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,o8)};case 1:return t.toast.id&&n8(t.toast.id),{...e,toasts:e.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:o}=t;return e.toasts.find(i=>i.id===o.id)?Md(e,{type:1,toast:o}):Md(e,{type:0,toast:o});case 3:let{toastId:r}=t;return r?Ag(r):e.toasts.forEach(i=>{Ag(i.id)}),{...e,toasts:e.toasts.map(i=>i.id===r||r===void 0?{...i,visible:!1}:i)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},i8=[],Ed={toasts:[],pausedAt:void 0},Gc=e=>{Ed=Md(Ed,e),i8.forEach(t=>{t(Ed)})};var s8=(e,t="blank",o)=>({createdAt:Date.now(),visible:!0,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...o,id:o?.id||e8()}),Gs=e=>(t,o)=>{let r=s8(t,e,o);return Gc({type:2,toast:r}),r.id},co=(e,t)=>Gs("blank")(e,t);co.error=Gs("error");co.success=Gs("success");co.loading=Gs("loading");co.custom=Gs("custom");co.dismiss=e=>{Gc({type:3,toastId:e})};co.remove=e=>Gc({type:4,toastId:e});co.promise=(e,t,o)=>{let r=co.loading(t.loading,{...o,...o?.loading});return e.then(n=>(co.success(Pd(t.success,n),{id:r,...o,...o?.success}),n)).catch(n=>{co.error(Pd(t.error,n),{id:r,...o,...o?.error})}),e};var a8=Mt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,c8=Mt`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,l8=Mt`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,d8=Eo("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${a8} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${c8} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${l8} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,p8=Mt`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,m8=Eo("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${p8} 1s linear infinite;
`,u8=Mt`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,f8=Mt`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,g8=Eo("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${u8} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${f8} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,x8=Eo("div")`
  position: absolute;
`,y8=Eo("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,h8=Mt`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,k8=Eo("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${h8} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,S8=({toast:e})=>{let{icon:t,type:o,iconTheme:r}=e;return t!==void 0?typeof t=="string"?oi.createElement(k8,null,t):t:o==="blank"?null:oi.createElement(y8,null,oi.createElement(m8,{...r}),o!=="loading"&&oi.createElement(x8,null,o==="error"?oi.createElement(d8,{...r}):oi.createElement(g8,{...r})))},A8=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,w8=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,b8="0%{opacity:0;} 100%{opacity:1;}",v8="0%{opacity:1;} 100%{opacity:0;}",C8=Eo("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,T8=Eo("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,I8=(e,t)=>{let o=e.includes("top")?1:-1,[r,n]=t8()?[b8,v8]:[A8(o),w8(o)];return{animation:t?`${Mt(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Mt(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},LU=Zr.memo(({toast:e,position:t,style:o,children:r})=>{let n=e.height?I8(e.position||t||"top-center",e.visible):{opacity:0},i=Zr.createElement(S8,{toast:e}),s=Zr.createElement(T8,{...e.ariaProps},Pd(e.message,e));return Zr.createElement(C8,{className:e.className,style:{...n,...o,...e.style}},typeof r=="function"?r({icon:i,message:s}):Zr.createElement(Zr.Fragment,null,i,s))});Xm(Fd.createElement);var WU=Jm`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`;var Rc=Nd.default.memo(({message:e,isJson:t,header:o})=>{let{t:r}=S();return Nd.default.createElement(z,{rows:[{type:"collapsible",topLeft:{text:o},bottomLeft:{text:e},allowCopy:t,onCopied:()=>{co.success(r("notificationMessageCopied"))}}]})}),Bd=e=>{let t=e,o=!1;try{let r=JSON.parse(e);t=JSON.stringify(r,null,2),o=!0}catch{}return{formattedMessage:t,isJson:o}};var Ld=e=>{let{t}=S(),o=hs(),{origin:r,message:n,networkID:i,scanWarnings:s,confirmApproval:a,denyApproval:c,scanFailed:l,icon:m}=e,{formattedMessage:u,isJson:g}=Bd(n),x=(0,Ue.useCallback)(async()=>{s?.length&&e.advancedDetails&&e.advancedDetails.errorSignInWithSolana&&await o.capture("userIgnoredSignInWithMessageError",{data:{type:e.advancedDetails.errorSignInWithSolana?.type,format:e.advancedDetails.errorSignInWithSolana?.format,origin:r,error:e.advancedDetails.errorSignInWithSolana?.error}}),a?.()},[s?.length,e.advancedDetails,a,o,r]),y=rs(i),b=(s||[]).some(w=>w.severity===1),h=[Ue.default.createElement(he,{key:"secondary",theme:"secondary",onClick:c,"data-testid":"secondary-button"},t(a?"commandCancel":"commandClose"))];return!b&&a&&h.push(Ue.default.createElement(he,{key:"primary",theme:l?"destructive":"primary",onClick:a,"data-testid":"primary-button"},t("commandConfirm"))),b&&h.push(Ue.default.createElement(he,{theme:"destructive",onClick:x},t("commandConfirmUnsafe"))),Ue.default.createElement(Ue.default.Fragment,null,Ue.default.createElement(M,{justifyContent:"space-between",flex:1,overflow:"auto",padding:"screen",minHeight:0,height:"100%",gap:"section",testID:"solana-sign-message"},Ue.default.createElement(Dn,{type:"SIGN_MESSAGE",domain:ee(r),iconUrl:m,description:t("notificationSignMessageParagraphText")}),(s?.length||l)&&Ue.default.createElement(M,{gap:8},s&&s?.length>0&&Ue.default.createElement(Ue.default.Fragment,null,s.map((w,T)=>Ue.default.createElement(wr,{level:va(w.severity),key:`simulation-warning-${T}`},w.message))),l&&Ue.default.createElement(wr,{level:"1-critical-alert",testID:"simulation-failed"},t("notificationFailedToScan"))),Ue.default.createElement(M,{gap:8},Ue.default.createElement(M,{testID:"message",asChild:!0},Ue.default.createElement(Rc,{header:t("notificationMessageHeader"),message:u,isJson:g})),Ue.default.createElement(z,{rows:[y]})),Ue.default.createElement(R,{align:"center",marginTop:"auto",font:"caption",color:"textSecondary"},t("notificationConfirmFooter"))),Ue.default.createElement(Zo,{direction:"row",type:"solid"},Ue.default.createElement(Ue.default.Fragment,null,h)))};var D8=ri.memo(({icon:e,origin:t,message:o,display:r,confirmApproval:n,denyApproval:i,networkID:s})=>{let a=ri.useMemo(()=>{switch(r){case"hex":return`0x${Buffer.from(o).toString("hex")}`;case"utf8":default:return new TextDecoder().decode(o)}},[r,o]),{isError:c,data:l,isLoading:m}=Ti({networkID:s??"solana:101",type:"message",url:t,params:{message:Buffer.from(o).toString("utf8")}});return m?ri.createElement(_s,{type:"SIGN_MESSAGE",icon:e,domain:t,onClose:i}):ri.createElement(Ld,{icon:e,origin:t,message:a,networkID:s,confirmApproval:n,denyApproval:i,scanFailed:c,scanIsLoading:m,scanWarnings:l?.warnings||[],advancedDetails:l?.advancedDetails})});d();p();var Yo=A(C()),zc=({header:e,children:t})=>{let o=!!e,[r,n]=(0,Yo.useState)(!o),i=(0,Yo.useCallback)(()=>{n(!r)},[r]);return Yo.default.createElement(Yo.default.Fragment,null,e&&Yo.default.createElement(M,{testID:"toggle-table",onPress:i,paddingTop:4,paddingBottom:4,paddingRight:4,direction:"row",alignItems:"center"},Yo.default.createElement(M,{marginRight:6,width:16,height:16},r?Yo.default.createElement(J.TriangleDownFill,{color:"textSecondary"}):Yo.default.createElement(J.TriangleRightFill,{color:"textSecondary"})),Yo.default.createElement(R,{font:"captionSemibold",color:"textSecondary"},e)),r&&t)};d();p();var E8=f.div`
  flex: none;
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;

  > * {
    margin-top: 27px;
  }
`,P8=f.div`
  flex: 1;
  overflow: auto;
  padding: 0px 16px;
`,M8=f.div`
  position: fixed;
  display: flex;
  flex-direction: column;
  height: 100%;
  width: 100%;
  left: 0;
  bottom: 0;
  background: #222222;
`,F8=f.div`
  ${e=>!e.plain&&`
    background-color: ${e.theme?.footer?.backgroundColor??"#2b2b2b"};
    border-top: ${e.theme?.footer?.borderTop??"1px solid #323232"};
    box-shadow: ${e.theme?.footer?.boxShadow??"0px -6px 10px rgba(0, 0, 0, 0.25)"};
  `}
  flex: none;
  padding: 14px 20px;
`,N8=f.div`
  padding: 20px;
  height: 100%;
`;d();p();var ni=(e,t,o,r,n="#999999",i,s,a)=>[{topLeft:e("bottomSheetNetworkFeeRow"),...a?{onClick:a,type:"drawer"}:{type:"base"},topRight:{text:o&&!r?"Loading...":t,color:n},...i&&{bottomLeft:{text:i,color:s??"accentAlert"}}}],$c=(e,t)=>t?[{topLeft:e("bottomSheetEstimatedTimeRow"),topRight:t}]:[];d();p();var Ud=A(C());d();p();d();p();var V8=A(C(),1);d();p();var $s=A(C(),1);d();p();d();p();d();p();d();p();d();p();var Tg=A(C(),1);d();p();var qs=(0,Tg.createContext)();d();p();var js=A(C(),1);d();p();var Ig=A(C(),1);d();p();d();p();var Dg=A(C(),1);d();p();var z8=A(C(),1);d();p();var _8=A(C(),1);d();p();var Ce=A(C());d();p();var Wt=A(C()),Hd=({rows:e})=>{let t=(0,Wt.useMemo)(()=>e.map((o,r)=>o.type==="AssetChange"?Eg({change:o}):o.type==="MessageOnly"?Vd({change:o}):Vd({change:o})),[e]);return Wt.default.createElement(z,{rows:t})},Eg=({change:e})=>{let t=e.asset.type==="native"||e.asset.type==="unknown"?"fungible":e.asset.type,o=e.changeText||e.fallbackMessage,r=Ca(e.changeSign),n=e.changeSign==="EQUAL",i=e.name===(e?.changeText??e.fallbackMessage),s=n||i;return{topLeft:{before:e.image?Wt.default.createElement(Wt.default.Fragment,null,Wt.default.createElement(yt,{image:{type:t,src:e.image},size:24}),Wt.default.createElement("div",{style:{display:"none"},"data-testid":"estimated-change-image"})):Wt.default.createElement(M,{backgroundColor:e.changeSign==="PLUS"?"accentSuccess":e.changeSign==="MINUS"?"accentAlert":"textSecondary",size:24,flex:"none",borderRadius:12,justifyContent:"center",alignItems:"center"},e.changeSign==="PLUS"&&Wt.default.createElement(J.Plus,{"data-testid":"estimated-changes-icon-receive",size:16,color:"bgWallet"}),e.changeSign==="MINUS"&&Wt.default.createElement(J.Send,{"data-testid":"estimated-changes-icon-send",size:16,color:"bgWallet"}),n&&Wt.default.createElement(J.Check,{"data-testid":"estimated-changes-icon-check",size:16,color:"bgWallet"})),after:!s&&Wt.default.createElement(R,{whiteSpace:"pre-line",wordBreak:"break-word",font:"caption",color:"textPrimary"},e.name)},topRight:Wt.default.createElement(R,{whiteSpace:"pre-line",wordBreak:"break-word",font:"caption",color:r,align:s?"left":"right"},o)}},Vd=({change:e})=>{let t="fungible",o;return e.image&&(o=Wt.default.createElement(yt,{image:{type:t,src:e.image},size:24})),!e.image&&e.changeType==="approval"&&(o=Wt.default.createElement(M,{backgroundColor:"textSecondary",size:24,flex:"none",borderRadius:12,justifyContent:"center",alignItems:"center"},Wt.default.createElement(J.Check,{size:16,color:"bgWallet"}))),{topLeft:{before:o,text:e.message||e.fallbackMessage}}};var en=Ce.default.memo(({advancedDetails:e,confirmApproval:t,denyApproval:o,hasSimulationFailed:r,numTransactions:n,domain:i,rows:s=[],networkFeeRows:a=[],isLoading:c,isErrorNetworkFee:l,isErrorNativeTokenBalance:m,simulationResults:u,simulationWarnings:g,icon:x,networkID:y,showFriction:b,showConfirmAnyway:h})=>{let{t:w}=S(),T=rs(y),v=g.length>0||l||m||r,I=g.some(F=>F.severity===1),N=u.length===0,B=!r&&!(N&&v),[V,L]=(0,Ce.useMemo)(()=>{let F="row",P=[Ce.default.createElement(he,{key:"secondary-button",testID:"secondary-button",onClick:o},w(t?"commandCancel":"commandClose"))];return!b&&!h&&t&&P.push(Ce.default.createElement(he,{key:"primary-button",testID:"primary-button",theme:I||r?"destructive":"primary",disabled:c,onClick:t},w("commandConfirm"))),b&&t&&(P.push(Ce.default.createElement(M,{height:24},Ce.default.createElement(R,{key:"friction-confirm-button",onPress:t,color:["textSecondary","accentAlert"]},w("commandConfirmUnsafe")))),F="column"),h&&t&&!b&&(P.push(Ce.default.createElement(M,null,Ce.default.createElement(R,{key:"confirm-anyway-button",onPress:t,color:["textSecondary","accentAlert"]},w("commandConfirmAnyway")))),F="column"),[P,F]},[t,o,I,r,b,h,c,w]);return Ce.default.createElement(Ce.default.Fragment,null,Ce.default.createElement(M,{justifyContent:"space-between",flex:1,overflow:"auto",padding:"screen",minHeight:0,height:"100%",gap:"section",testID:"approve-transaction"},Ce.default.createElement(Dn,{domain:i,type:n&&n>1?"APPROVE_TRANSACTIONS":"APPROVE_TRANSACTION",iconUrl:x,description:w("notificationBalanceChangesText")}),v&&Ce.default.createElement(M,{gap:8,testID:"warning-container"},g.map((F,P)=>Ce.default.createElement(wr,{level:va(F.severity),key:`simulation-warning-${P}`,testID:"approval-warning-text"},F.message)),r&&Ce.default.createElement(wr,{level:"1-critical-alert",testID:"simulation-failed"},w("notificationFailedToScan")),m&&Ce.default.createElement(wr,{testID:"approval-warning-text",level:"3-critical-error"},w("nativeTokenBalanceErrorWarning")),l&&Ce.default.createElement(wr,{testID:"approval-warning-text",level:"3-critical-error"},w("gasEstimationErrorWarning"))),Ce.default.createElement(M,{gap:8},B&&Ce.default.createElement(Hd,{rows:u}),Ce.default.createElement(z,{rows:[T,...a,...s]}),e&&Ce.default.createElement(zc,{header:w("notificationAdvancedDetailsText")},e)),Ce.default.createElement(R,{align:"center",marginTop:"auto",font:"caption",color:"textSecondary"},w("notificationConfirmFooter"))),Ce.default.createElement(Zo,{direction:L,type:"solid"},Ce.default.createElement(Ce.default.Fragment,null,V)))});d();p();var j8=A(C());d();p();var Y8=A(C());d();p();var Ye=A(C());d();p();var _d=A(C()),Pg=e=>{let t=document.createElement("textarea");t.value=e,t.setAttribute("readonly",""),t.style.position="absolute",t.style.left="-9999px",document.body.appendChild(t);let o=document.getSelection().rangeCount>0?document.getSelection().getRangeAt(0):!1;t.select();let r=document.execCommand("copy");return document.body.removeChild(t),o&&(document.getSelection().removeAllRanges(),document.getSelection().addRange(o)),r};d();p();var Gd={copyTransactionButton:"_12hl3fw2 _51gazn33h _51gazn18w _51gazn1c4 _51gazn1ar _51gazngj _51gazn1gq",transactionValue:"_12hl3fw3",toastWrapper:"_12hl3fw4"};var tn=({networkID:e,scanResult:t,copyTransactions:o})=>{let{t:r}=S(),n=(0,Ye.useMemo)(()=>(o||[]).map((i,s)=>Ye.default.createElement("div",{className:Gd.copyTransactionButton,role:"button","aria-label":r("commandCopy"),key:`copy-transaction-${s}`,onClick:()=>{Pg(i),co.success(yr.t("notificationTransactionCopied"),{id:"copy-transaction-toast",duration:3e3})}},Ye.default.createElement("span",{className:Gd.transactionValue},i.slice(0,20)),Ye.default.createElement(J.Copy,{size:16}))),[r,o]);return Ye.default.createElement(M,null,n?.map((i,s)=>Ye.default.createElement(M,{marginBottom:8,key:`copy-advanced-row-${s}`},Ye.default.createElement(oo,{topLeft:{text:r("notificationCopyTransactionText"),font:"caption"},topRight:i}))),t&&Ye.default.createElement(J8,{selectedAddresses:void 0,scanResult:t,networkID:e}))},J8=({selectedAddresses:e,scanResult:t,networkID:o})=>{let{data:r}=ka(),{t:n}=S(),i=(0,Ye.useCallback)(a=>{if(!r)return"";let c=r.explorers[o];return ga({endpoint:"address",explorerType:c,networkID:o,param:a})},[r,o]),s=(0,Ye.useMemo)(()=>{if(!t)return[];if(j.isEVMNetworkID(o)||j.isSolanaNetworkID(o))return t.advancedDetails?.advancedRows?t.advancedDetails.advancedRows.map(c=>({title:c.title,rowItems:c.items.map(l=>l.isAddress?{type:"link",topLeft:l.title,topRight:R0(l.value),onClick:()=>()=>self.open(i(l.value),"_blank")}:l.value.length>50?{type:"collapsible",topLeft:{text:l.title},bottomLeft:{text:l.value},allowCopy:!0,onCopied:()=>{co.success(n("notificationMessageCopied"))}}:{topLeft:l.title,topRight:l.value})})):[];if(j.isBitcoinNetworkID(o)){let{inputs:a,outputs:c}=t?.advancedDetails??{};return!a||!c?[]:[...a.map((m,u)=>({title:`Input #${u+1}`,rowItems:[{topLeft:"address",topRight:(e?.includes(m.address)?"You \xB7 ":"")+be(m.address),type:"link",onClick:()=>self.open(`https://mempool.space/address/${m.address}`,"_blank")},{topLeft:"value",topRight:`${Il(m.amount)} BTC`}]})),...c.map((m,u)=>({title:`Output #${u+1}`,rowItems:[{topLeft:"address",topRight:(e?.includes(m.address)?"You \xB7 ":"")+be(m.address),type:"link",onClick:()=>self.open(`https://mempool.space/address/${m.address}`,"_blank")},{topLeft:"value",topRight:`${Il(m.amount)} BTC`}]}))]}return[]},[i,o,t,e,n]);return Ye.default.createElement(Ye.default.Fragment,null,s.map((a,c)=>Ye.default.createElement(X8,{index:c,title:a.title,rowItems:a.rowItems,key:`advanced-detail-table-${c}`})))},X8=({title:e,rowItems:t,index:o})=>Ye.default.createElement(Ye.default.Fragment,null,Ye.default.createElement(M,{padding:4},Ye.default.createElement(R,{font:"captionMedium",color:"textSecondary"},e)),Ye.default.createElement(z,{key:`instruction-table-${o}`,rows:t}));d();p();var Rd=A(C());d();p();var Mg=A(C());d();p();var e7=A(ea()),Bg=A(C());d();p();var zd=A(C());d();p();var AR=gn(e=>({transactionSpeed:"standard",setTransactionSpeed:t=>e({transactionSpeed:t})}));d();p();var Je=A(C());var XR=Je.default.memo(({headerText:e,primaryText:t,onPress:o,presetViewStates:r,onCancel:n})=>Je.default.createElement(Je.default.Fragment,null,Je.default.createElement(M,{direction:"column",gap:8,justifyContent:"space-between",height:"100%"},Je.default.createElement(M,null,Je.default.createElement(M,{direction:"row",gap:8,alignItems:"center",padding:8},Je.default.createElement(fm,{backgroundColor:["bgWallet","black"],label:"Back",color:"textPrimary",icon:"ArrowLeft",onClick:n,shape:"circle",size:28}),Je.default.createElement(M,{direction:"row",justifyContent:"center"},Je.default.createElement(R,{color:"textPrimary",font:"body"},e))),Je.default.createElement(M,{padding:16,paddingBottom:32},Je.default.createElement(M,{borderRadius:6,overflow:"hidden"},r.map((i,s)=>Je.default.createElement(r7,{key:`preset-${s}`,onClick:i.onClick,title:i.title,description:i.description,selected:i.selected,borderTop:s!==0}))))),Je.default.createElement(Zo,{direction:"column",type:"solid"},Je.default.createElement(he,{theme:"primary",onClick:o},t))))),r7=({selected:e,title:t,description:o,onClick:r,borderTop:n})=>Je.default.createElement("div",{onClick:r,className:_e({display:"flex",backgroundColor:e?"brandPrimary":"bgRow",padding:16,cursor:"pointer",alignItems:"center",borderTopColor:n?"bgWallet":void 0,borderTopWidth:n?1:void 0,borderTopStyle:n?"solid":void 0})},Je.default.createElement(M,null,Je.default.createElement(R,{color:e?"bgRow":"white",marginBottom:6,font:"body"},t),Je.default.createElement(R,{color:e?"bgRow":"textSecondary",font:"micro1"},o||Je.default.createElement("span",null,"\xA0"))));d();p();var Og=A(C());var Lg=class extends Og.default.Component{constructor(t){super(t),this.state={error:null}}static getDerivedStateFromError(t){return t instanceof Error?{error:t}:typeof t=="string"?{error:new Error(t)}:{error:new Error}}componentDidCatch(t,o){t instanceof Error&&$.captureError(t,"generic")}render(){return this.state.error?typeof this.props.fallback=="function"?this.props.fallback(this.state.error):this.props.fallback:this.props.children}};var Jt=A(C());d();p();var ii=A(C());var n7=f(Et.div)`
  padding: 0 16px 16px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
`,i7=f.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: ${e=>e.settingsContainerHeight??"100%"};
`,En=(e,t)=>{let{pushDetailView:o}=U(),{isActive:r,available:n,untilDate:i,save:s,settings:a,isAutoConfirmIsTouched:c,loading:l}=ei({origin:new URL(e).origin});return{open:ii.default.useCallback(u=>{o(ii.default.createElement(n7,null,ii.default.createElement(Lc,{dappUrl:e}),ii.default.createElement(Wi,{isLoading:l},ii.default.createElement(i7,null,ii.default.createElement(Oc,{dappUrl:e,autoConfirmStatusCode:u,networkID:t})))))},[o,e,t,l]),isActive:r,untilDate:i,save:s,settings:a,isAutoConfirmIsTouched:c,available:n}};d();p();var qc=A(C());function Pn({block:e,warnings:t,isLoadingSimulation:o}){let r=t?.some(a=>a.severity===1),n="loading";!o&&e?n="block":o||(n="main");let[i,s]=(0,qc.useState)(n);return(0,qc.useEffect)(()=>{s(o?"loading":e?"block":"main")},[e,o]),{showFrictionInterstitial:!!r,initialScreen:n,screen:i,setScreen:s}}d();p();d();p();d();p();d();p();var jc=gn(e=>({transactionSpeed:"standard",setTransactionSpeed:t=>e({transactionSpeed:t})}));var $d=A(C());var s7=f(Et.div)`
  padding: 0 16px 16px 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
`,Wg=(e,t)=>{let{status:o,data:r}=Pl({networkID:e,enableFallback:!0}),n=jc(l=>l.transactionSpeed),i=jc(l=>l.setTransactionSpeed),{popDetailView:s,pushDetailView:a}=U(),c;return o!=="error"&&r&&n&&t&&(c=()=>{a($d.default.createElement(s7,null,$d.default.createElement(Pu,{onSelectTransactionSpeed:i,selectedTransactionSpeed:n,networkID:e,transactionUnitAmount:t,closeModal:s})))}),{transactionSpeed:n,openGasSettings:c}};d();p();var Kc=A(C());async function a7(e){try{let t=e.networkID.replace("eip155:",""),o=ut(e.networkID.replace("eip155:",""));if(o?.chainType!=="eip155")throw new Error(`Failed to capture transaction signature event analytics: Unsupported Ethereum Network ID "${t}"`);return await ft.userApprovedAction(W,{...o,origin:e.origin,chainId:o.networkId,method:"eth_sendTransaction"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (approve)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserApprove: ${typeof t}`),"provider")}}async function c7(e){try{let t=e.networkID.replace("eip155:",""),o=ut(t);if(o?.chainType!=="eip155")throw new Error(`Failed to capture transaction signature event analytics: Unsupported Ethereum Network ID "${t}"`);return await ft.userDeniedAction(W,{...o,origin:e.origin,chainId:o.networkId,method:"eth_sendTransaction"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (deny)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserDeny: ${typeof t}`),"provider")}}function l7(e,t){return(o,r)=>{let{networkID:n,origin:i}=r,s=(0,Kc.useRef)(!1);return(0,Kc.useCallback)(async a=>(j.isEVMNetworkID(n)&&!s.current&&("error"in a?await t({networkID:n,origin:i}):await e({networkID:n,origin:i}),s.current=!0),o(a)),[o,n,i])}}var Ug=l7(a7,c7);d();p();var Ut=A(C());var d7=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})`
  margin-top: 16px;
  margin-bottom: 8px;
`,p7=f.div`
  margin-top: 16px;
`,m7=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  padding: 16px 0 8px;
`,u7=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,f7=f(Rt)`
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  padding: 16px 16px;
`,g7=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,x7=f(E).attrs({size:28,weight:600,lineHeight:32,textAlign:"left",letterSpacing:"-2%"})``,y7=f.div`
  color: ${go};
  font-size: 13px;
  font-family: Inter;
  font-weight: 500;
  line-height: 16px;
`,Wr=({origin:e,onConfirm:t,onClose:o,warningMessage:r})=>{let{t:n}=S();return Ut.default.useEffect(()=>{W.capture("blockScreenSeenByUser",{data:{warningMessage:r,url:e}})},[e,r]),Ut.default.createElement(Ut.default.Fragment,null,Ut.default.createElement(f7,{"data-testid":"blocklist-connect-request"},Ut.default.createElement("div",null,Ut.default.createElement(g7,null,Ut.default.createElement(Ua,{width:36,height:36}),Ut.default.createElement(x7,null,n("maliciousRequestBlockedTitle")),Ut.default.createElement(y7,null,ee(e))),r&&Ut.default.createElement(p7,null,Ut.default.createElement(wt,{message:r,variant:1})),Ut.default.createElement(d7,{color:"#FFFFFF"},n("maliciousRequestBlocked")))),Ut.default.createElement(Qe,null,Ut.default.createElement(oe,{onClick:o},n("commandClose")),Ut.default.createElement(m7,null,Ut.default.createElement(u7,{hoverColor:go,onClick:t,color:"#777777"},n("commandProceedAnywayUnsafe")))))};d();p();var bt=A(C());d();p();var si=A(C());var Vg=({setRiskAcknowledged:e})=>{let{t}=S(),[o,r]=si.useState(!1),n=()=>{r(s=>!s)};si.useEffect(()=>{e(o)},[o,e]);let i=_e({textAlign:"left"});return si.createElement(h7,{onClick:n,"data-testid":"acknowledge--button"},si.createElement(Ul,{checked:o,onChange:n,label:{text:t("maliciousRequestAcknowledge"),color:"accentAlert",className:i},variant:{shape:"square",theme:"alert"}}))},h7=f.button`
  background-color: ${dn(go,.1)};
  border: none;
  border-radius: 8px;
  padding: 16px;
  display: flex;
  flex-direction: row;
  width: 100%;
  cursor: pointer;
`;var k7=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})`
  margin-top: 16px;
  margin-bottom: 8px;
`,S7=f.div`
  margin-top: 16px;
`,A7=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,w7=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  margin-bottom: 8px;
`,b7=f(Rt)`
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
  padding: 16px 16px;
`,v7=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,C7=f(Qe)`
  display: flex;
  flex-direction: column;
  gap: 16px;
`,T7=f(E).attrs({size:28,weight:600,lineHeight:32,textAlign:"left",letterSpacing:-.56})``,I7=f.div`
  color: #999999;
  font-size: 13px;
  font-family: Inter;
  font-weight: 500;
  line-height: 16px;
`,Mn=({origin:e,onConfirm:t,onClose:o,onBack:r,warningMessage:n})=>{let[i,s]=bt.default.useState(!1),{t:a}=S();return bt.default.createElement(bt.default.Fragment,null,bt.default.createElement(b7,{"data-testid":"friction-notification"},bt.default.createElement("div",null,bt.default.createElement(v7,null,bt.default.createElement(Ua,{width:36,height:36}),bt.default.createElement(T7,null,a("maliciousRequestAreYouSure")),bt.default.createElement(I7,null,ee(e))),n&&bt.default.createElement(S7,null,bt.default.createElement(wt,{message:n,variant:1})),bt.default.createElement(k7,{color:"#FFFFFF"},a("maliciousRequestFrictionDescription")))),bt.default.createElement(C7,{plain:!0},bt.default.createElement(Vg,{setRiskAcknowledged:s}),bt.default.createElement(An,{buttons:[{text:a("commandBack"),onClick:r},{text:a("commandClose"),onClick:o}]}),bt.default.createElement(w7,null,bt.default.createElement(A7,{onClick:i?t:void 0,color:i?go:dn(go,.5)},a("commandYesConfirmUnsafe")))))};d();p();var ai=A(C());d();p();var ns=f.div`
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 16px;
`,Hg=f(yt)`
  margin-right: 4px;
`,_g=f(E)`
  &:hover {
    color: #ab9ff2;
  }
`,Gg=f.div`
  margin-left: 8px;
`;var is=({origin:e,autoConfirmStatusCode:t,networkID:o})=>{let{t:r}=S(),{open:n,isActive:i,untilDate:s,available:a}=En(e,o),c=()=>{n(t)},l=t!=null&&t!=="DISABLED"&&t!=="OK",u=ai.createElement(ns,null,i(o)&&s?ai.createElement(R,{color:"textSecondary",font:"body"},r("settingsTrustedAppsAutoConfirmActiveUntil",{formattedTimestamp:xs(s).toLowerCase()})):ai.createElement(R,{color:"textSecondary",font:"body","data-testid":"networkName"},r("notificationAutoConfirmOff")),ai.createElement(J.ChevronRight,{size:20,color:"textSecondary"}));return{label:r("notificationAutoConfirm"),value:u,onClick:c,description:l?ai.createElement(R,{color:"textSecondary",font:"caption"},ai.createElement(Ro,{i18nKey:Ea(t)})):void 0,showAutoConfirmRow:a&&(i(o)||!i(o)&&!l)}};d();p();var et=A(C());d();p();var lr=A(C());var D7=f.div`
  color: ${({color:e})=>e};
  font-size: 13px;
  font-family: Inter;
  font-weight: 500;
  margin-top: 6px;
  line-height: 16px;
`,E7=f(E).attrs({size:22,weight:600,lineHeight:24,textAlign:"left"})``,P7=f(E).attrs({size:15,color:"#999999",textAlign:"left",lineHeight:20})``,Ao=e=>{let{title:t,type:o,description:r,domain:n}=e,{t:i}=S(),{originIsBlocklisted:s}=Ci(e.domain),{data:a}=am([n||N0(e.iconUrl)?.hostname||""]),c=lr.useMemo(()=>{let u=a?a.find(g=>g.domain===n):void 0;if(n&&u?.imageUrl)return u.imageUrl;if(e.iconUrl)return e.iconUrl},[n,s,e.iconUrl,a]);function l(u){switch(u){case"APPROVE_TRANSACTION":return"notificationSignatureRequestConfirmTransactionCapitalized";case"APPROVE_TRANSACTIONS":return"notificationSignatureRequestConfirmTransactionsCapitalized";case"SIGN_MESSAGE":return"dappApprovePopupSignMessage";case"CONNECT":return"notificationApplicationApprovalActionButtonConnect";case"SIGN_IN":return"notificationApplicationApprovalActionButtonSignIn"}}let m="#999999";return s&&(m=go),lr.createElement(Nt,{margin:"16px 0 8px 0"},lr.createElement(xt,null,lr.createElement(yt,{image:{type:"dapp",src:c},size:56}),lr.createElement(Nt,{margin:"0 0 0 8px"},lr.createElement(E7,null,t||i(l(o))),n&&lr.createElement(D7,{color:m},ee(n??"")))),r&&lr.createElement(xt,{margin:"8px 0 0 0"},typeof r=="string"?lr.createElement(P7,null,r):r))};function Ur({domain:e,type:t,onClose:o,icon:r}){let{t:n}=S(),i=t==="APPROVE_TRANSACTION"||t==="APPROVE_TRANSACTIONS"?n("notificationBalanceChangesText"):void 0;return et.default.createElement(et.default.Fragment,null,et.default.createElement(Rt,{"data-testid":"scan-loading"},et.default.createElement(M7,null,et.default.createElement(Ao,{domain:e,type:t,iconUrl:r,description:i||et.default.createElement(O7,null,et.default.createElement(zg,{width:270}),et.default.createElement(zg,{width:320}))}),et.default.createElement(F7,null,et.default.createElement(qd,null),et.default.createElement(qd,null),et.default.createElement(qd,null)))),et.default.createElement(Qe,null,et.default.createElement(An,{buttons:[{text:n("commandCancel"),onClick:o,testID:"btn-cancel"},{text:n("commandConfirm"),onClick:()=>!0,disabled:!0,testID:"btn-confirm"}]})))}function qd(){return et.default.createElement(N7,null,et.default.createElement(B7,null,et.default.createElement(L7,null),et.default.createElement(Rg,null)),et.default.createElement(Rg,null))}var M7=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding-bottom: 32px;
`,F7=f.div`
  display: flex;
  flex-direction: column;
  border-radius: 16px;
  overflow: hidden;
  gap: 1px;
`,N7=f.div`
  display: flex;
  flex-direction: row;
  padding: 16px;
  background: #2c2d30;
  justify-content: space-between;
`,B7=f.div`
  display: flex;
  flex-direction: row;
  gap: 8px;
`,L7=f(Cr)`
  width: 24px;
  height: 24px;
  border-radius: 100%;
`,Rg=f(Cr)`
  width: 78px;
  height: 24px;
  border-radius: 16px;
`,O7=f.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: center;
  gap: 8px;
`,zg=f(Cr)`
  width: ${e=>e.width}px;
  height: 16px;
  background: ${e=>e.theme.skeletonLight};
  border-radius: 16px;
`;var W7=({transaction:e,maxFeePerGas:t="0x0",maxPriorityFeePerGas:o="0x0",postOutgoingBackgroundResponse:r,requestId:n})=>{let{data:i}=ue(),[s,a]=(0,Jt.useMemo)(()=>[i?.identifier??"",i?.type==="ledger"],[i]);return{ledgerSignTransaction:(0,Jt.useCallback)(async()=>{let l=_t(),u=await(async()=>(e.maxFeePerGas=t,e.maxPriorityFeePerGas=o,jr(de,Ai),l.sign(s,{chainType:"eip155",signingType:"transaction",message:em(e)}).finally(()=>jr(de,Si))))(),g;switch(u.status){case"success":g={jsonrpc:"2.0",id:n,result:{type:"send",maxFeePerGas:t,maxPriorityFeePerGas:o,signature:u.signature}};break;case"error":g={jsonrpc:"2.0",id:n,error:ie.userRejectedRequest(n).error};break}r(g)},[s,t,o,r,e,n]),isLedgerAccount:a}},U7=(e,t,o,r)=>{let{data:n,isPending:i,isError:s}=Ml({networkID:e,multichainTransaction:t,transactionSpeed:o,queryOptions:{refetchInterval:gl}}),{data:a,isPending:c}=Fl(e,n),l=o?Dl.get(e).transactionSpeedDescription(o,!0):void 0,m=l?r(l):"";return{networkFee:Nl({networkID:t.networkID,gasEstimation:n,gasEstimationPrice:a}),isLoadingNetworkFee:i||c,gasEstimation:n,isErrorGasEstimation:s,estimatedTime:m}},$g=e=>{let t=ee(e.url.origin),{url:o,requestId:r,icon:n}=e,i=o.origin,s=kl.parse(`eip155:${e.transaction.chainId?parseInt(e.transaction.chainId,16):void 0}`),{t:a}=S(),{data:c,isError:l,isFetched:m,isLoading:u}=hn({networkID:s,url:o.href,userAccount:e.transaction.from,params:{transactions:[e.transaction],method:"eth_sendTransaction"},type:"transaction"}),g=c?.error===xn.INSUFFICIENT_GAS||c?.error===xn.INSUFFICIENT_FUNDS,{screen:x,setScreen:y,showFrictionInterstitial:b}=Pn({block:c?.block,isLoadingSimulation:u&&!m,warnings:c?.warnings}),h=Fe(),w=Ug(h,{networkID:s,origin:i}),T=(0,Jt.useCallback)(()=>{w({jsonrpc:"2.0",id:r,error:ie.userRejectedRequest(r).error})},[r,w]),v=(0,Jt.useMemo)(()=>typeof e.transaction.gas=="string"?new fi(e.transaction.gas,16):void 0,[e.transaction.gas]),{transactionSpeed:I,openGasSettings:N}=Wg(s,v?{gasLimit:v}:void 0),B={networkID:s,unsignedTransaction:e.transaction},{networkFee:V,isLoadingNetworkFee:L,isErrorGasEstimation:F,gasEstimation:P,estimatedTime:H}=U7(s,B,I,a),{hasSufficientFunds:G,nativeTokenSymbol:X,isLoading:re,isError:Pe}=Ol(s,"eip155",P,void 0),{maxFeePerGas:Zt,maxPriorityFeePerGas:Me}=El(P||{gasLimit:new fi(0),maxFeePerGas:new fi(0),maxPriorityFeePerGas:new fi(0),networkID:s}),{ledgerSignTransaction:lo,isLedgerAccount:Xe}=W7({transaction:e.transaction,maxFeePerGas:Zt,maxPriorityFeePerGas:Me,postOutgoingBackgroundResponse:w,requestId:r}),{pushDetailView:wo}=U(),{save:bo,isAutoConfirmIsTouched:Le}=En(e.url.origin),It=(0,Jt.useCallback)(async()=>{Le&&await bo(),Xe?wo(Jt.default.createElement(no,null,Jt.default.createElement(xo,{ledgerApp:"EVM",ledgerAction:lo,cancel:T}))):w({jsonrpc:"2.0",id:r,result:{type:"signAndSend",maxFeePerGas:Zt||"0x0",maxPriorityFeePerGas:Me||"0x0"}})},[Le,bo,Xe,wo,lo,T,w,r,Zt,Me]),mt=(0,Jt.useCallback)(()=>{W.capture("userIgnoredKnownMaliciousWarning",{data:{origin:i}}),y("main")},[i,y]),eo=is({origin:e.url.origin,networkID:e.transaction.chainId?ia(e.transaction.chainId):void 0,autoConfirmStatusCode:e.autoConfirmStatusCode}),ot={topLeft:eo.label,topRight:eo.value,bottomLeft:eo.description,onClick:eo.onClick};if(x==="block")return Jt.default.createElement(Wr,{warningMessage:c?.block?.message??"",origin:i,onConfirm:mt,onClose:T});let Wo={networkID:s,unsignedTransaction:e.transaction};return x==="loading"?Jt.default.createElement(Ur,{type:"APPROVE_TRANSACTION",icon:n,domain:t,onClose:T}):x==="friction"?Jt.default.createElement(Mn,{onClose:T,onConfirm:It,onBack:()=>y("main"),origin:i,warningMessage:c?.warnings?.[0]?.message}):Jt.default.createElement(en,{advancedDetails:Jt.default.createElement(tn,{copyTransactions:[],scanResult:c,networkID:s}),confirmApproval:b?()=>y("friction"):It,denyApproval:T,hasSimulationFailed:!!l,numTransactions:1,domain:t,rows:eo.showAutoConfirmRow?[ot]:[],simulationResults:c?.expectedChanges??[],simulationWarnings:c?.warnings??[],icon:n,networkID:Wo.networkID,showFriction:b,showConfirmAnyway:!b&&g,isErrorNativeTokenBalance:Pe,isErrorNetworkFee:F,isLoading:L&&!F||re,networkFeeRows:[...ni(a,V,L,F,G==="insufficient"?"accentAlert":"textSecondary",G==="insufficient"?a("transactionNotEnoughNative",{nativeTokenSymbol:X}):F?a("gasEstimationCouldNotFetch"):void 0,"accentAlert",N),...$c(a,H)]})};d();p();var Xt=A(C());d();p();var Yc=A(C());async function V7(e){try{let t=e.networkID.replace("eip155:",""),o=ut(e.networkID.replace("eip155:",""));if(o?.chainType!=="eip155")throw new Error(`Failed to capture message signature event analytics: Unsupported Ethereum Network ID "${t}"`);return await ft.userApprovedAction(W,{...o,origin:e.origin,method:e.method})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (approve)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserApprove: ${typeof t}`),"provider")}}async function H7(e){try{let t=e.networkID.replace("eip155:",""),o=ut(t);if(o?.chainType!=="eip155")throw new Error(`Failed to capture message signature event analytics: Unsupported Ethereum Network ID "${t}"`);return await ft.userDeniedAction(W,{...o,origin:e.origin,method:e.method})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (deny)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserDeny: ${typeof t}`),"provider")}}function _7(e,t){return(o,r)=>{let{networkID:n,origin:i,originalMethod:s}=r,a=(0,Yc.useRef)(!1);return(0,Yc.useCallback)(async c=>{if(j.isEVMNetworkID(n)&&!a.current){let l=s;"error"in c?await t({networkID:n,origin:i,method:l}):await e({networkID:n,origin:i,method:l}),a.current=!0}return o(c)},[o,n,i,s])}}var qg=_7(V7,H7);d();p();var ye=A(C());d();p();var xe=A(C());var G7=f.div`
  background: #2a2a2a;
  border-radius: 16px;
  display: flex;
  flex-direction: column;
`,Kd=f.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  gap: 8px;
`,Kg=f.div`
  width: ${e=>e.size}px;
  height: ${e=>e.size}px;
  overflow: hidden;
  flex: none;
`,Yg=f.div`
  flex: 2;
  display: flex;
  align-items: center;
  gap: 8px;
`,R7=f.div`
  flex-shrink: 1;
`,jg=f.div`
  border-bottom: 1px solid #222222;
  border-bottom-width: 1px;
  margin: 0 -16px;
`,Jg=({rows:e})=>xe.default.createElement(G7,null,xe.default.createElement("div",null,e.map((t,o)=>t.type==="AssetChange"?xe.default.createElement(xe.default.Fragment,{key:`change-row-${o}`},xe.default.createElement(z7,{key:`expected-change-${o}`,change:t}),o!==e.length-1&&e.length>1&&xe.default.createElement(jg,null)):t.type==="MessageOnly"?xe.default.createElement(xe.default.Fragment,{key:`change-row-${o}`},xe.default.createElement($7,{key:`expected-change-${o}`,change:t}),o!==e.length-1&&e.length>1&&xe.default.createElement(jg,null)):xe.default.createElement(Kd,{key:`expected-change-${o}`},xe.default.createElement(R,{whiteSpace:"pre-line",wordBreak:"break-word",marginBottom:8,font:"caption",color:"textPrimary"},t.fallbackMessage))))),z7=({change:e})=>{let t=e.asset.type==="native"||e.asset.type==="unknown"?"fungible":e.asset.type,o=e.changeText||e.fallbackMessage,r=Ca(e.changeSign),n=e.changeSign==="EQUAL",i=e.name===(e?.changeText??e.fallbackMessage),s=n||i;return xe.default.createElement(Kd,null,xe.default.createElement(Yg,null,e.image?xe.default.createElement(Kg,{size:24,"data-testid":"estimated-change-image"},xe.default.createElement(yt,{image:{type:t,src:e.image},size:24})):xe.default.createElement(M,{backgroundColor:e.changeSign==="PLUS"?"accentSuccess":e.changeSign==="MINUS"?"accentAlert":"textSecondary",size:24,flex:"none",borderRadius:12,justifyContent:"center",alignItems:"center"},e.changeSign==="PLUS"&&xe.default.createElement(J.Plus,{"data-testid":"estimated-changes-icon-receive",size:16,color:"bgWallet"}),e.changeSign==="MINUS"&&xe.default.createElement(J.Send,{"data-testid":"estimated-changes-icon-send",size:16,color:"bgWallet"}),n&&xe.default.createElement(J.Check,{"data-testid":"estimated-changes-icon-check",size:16,color:"bgWallet"})),!s&&xe.default.createElement(R,{whiteSpace:"pre-line",wordBreak:"break-word",font:"caption",color:"textPrimary"},e.name)),xe.default.createElement(R7,null,xe.default.createElement(R,{whiteSpace:"pre-line",wordBreak:"break-word",font:"caption",color:r,align:s?"left":"right"},o)))},$7=({change:e})=>xe.default.createElement(Kd,null,xe.default.createElement(Yg,null,e.image&&xe.default.createElement(Kg,{size:24},xe.default.createElement(yt,{image:{type:"fungible",src:e.image},size:24})),!e.image&&e.changeType==="approval"&&xe.default.createElement(M,{backgroundColor:"textSecondary",size:24,flex:"none",borderRadius:12,justifyContent:"center",alignItems:"center"},xe.default.createElement(J.Check,{size:16,color:"bgWallet"})),xe.default.createElement(R,{font:"caption",whiteSpace:"pre-line",wordBreak:"break-word",color:"textPrimary"},e.message||e.fallbackMessage)));d();p();var dt=A(C());var q7=f.div`
  margin-right: 4px;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
`,j7=f.div`
  height: 1px;
  background-color: #222222;
`,K7=f.div`
  padding: 4px;
  display: flex;
  align-items: center;
  cursor: pointer;
`,Y7=f.div`
  display: flex;
  flex-direction: column;
`,J7=f.div`
  background: #2a2a2a;
  border-top-left-radius: ${({isFirstRow:e})=>e?"16px":"0px"};
  border-top-right-radius: ${({isFirstRow:e})=>e?"16px":"0px"};
  border-bottom-left-radius: ${({isLastRow:e})=>e?"16px":"0px"};
  border-bottom-right-radius: ${({isLastRow:e})=>e?"16px":"0px"};
  width: 100%;
  padding: 16px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  justify-content: flex-start;
  text-align: left;
`,X7=f.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  gap: 8px;
  cursor: ${({onClick:e})=>e?"pointer":"auto"};
`,dr=({header:e,rows:t=[]})=>{let o=!!e,[r,n]=(0,dt.useState)(!o),i=(0,dt.useCallback)(()=>{n(!r)},[r]);return dt.default.createElement(dt.default.Fragment,null,e&&dt.default.createElement(K7,{"data-testid":"toggle-table",onClick:i},dt.default.createElement(q7,null,r?dt.default.createElement(Wa,{fill:"#999999"}):dt.default.createElement(Fi,{fill:"#999999"})),dt.default.createElement(E,{color:"#999999",textAlign:"left",size:14,weight:600,lineHeight:17},e)),r&&dt.default.createElement(Y7,null,t.map(({label:s,value:a,description:c,onClick:l},m)=>dt.default.createElement(dt.default.Fragment,{key:`table-row-${s}-${m}`},s?dt.default.createElement(J7,{isFirstRow:m===0,isLastRow:m===t.length-1},dt.default.createElement(X7,{onClick:l},dt.default.createElement(E,{size:14,weight:400,lineHeight:17,color:"#ffffff"},s),a),c):a,m!==t.length-1&&t.length>1&&dt.default.createElement(j7,null)))))};d();p();var ci=A(C());var Xg=(e,t)=>{let{t:o}=S(),r=(0,ci.useCallback)(()=>{if(e){let i=ga({networkID:t,endpoint:"address",param:e});self.open(i)}},[e,t]),n=ci.default.createElement(ns,null,ci.default.createElement(_g,{size:14,weight:400,lineHeight:17,color:"#ab9ff2",onClick:r},be(e??"",4)),ci.default.createElement(Gg,null,ci.default.createElement(Km,{fill:"#999999"})));return e?{label:o("notificationContractAddress"),value:n}:{label:"",value:null}};d();p();var Jc=A(C());var Fn=e=>{let{t}=S(),o=j.getChainName(e),r="solana";switch(o){case"Solana":r="solana";break;case"Ethereum":r="ethereum";break;case"Polygon":r="polygon";break;case"Base":r="base";break;case"Bitcoin":r="bitcoin";break}let n=Jc.default.createElement(ns,null,Jc.default.createElement(Hg,{image:{type:"network",preset:r},size:16}),Jc.default.createElement(E,{size:14,weight:400,lineHeight:17,color:"#999999"},o));return{label:t("notificationTransactionApprovalNetwork"),value:n}};d();p();var Jd=A(C());d();p();var De=A(C());var Qg=96,Q7=f.div`
  background: #2a2a2a;
  border-radius: ${({isInTable:e})=>e?"0 0 16px 16px":"16px"};
  overflow: hidden;
  position: relative;
`,Z7=f.div`
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: ${({isOverflow:e})=>e?"pointer":"auto"};
`,e4=f.div`
  position: relative;
  overflow-wrap: break-word;
  max-height: ${({isOpen:e})=>e?"none":`${Qg}px`};

  &:after {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    height: 100%;
    width: 100%;
    background: ${({isOverflow:e})=>e?"linear-gradient(to top, #2a2a2a, transparent)":"none"};
    transition: background 200ms ease;
    display: ${({isOpen:e})=>e?"none":"block"};
  }
`,t4=f.div`
  padding: ${({isOpen:e,isOverflow:t})=>!t||e?"0 16px 16px 16px":"0 16px"};
`,o4=f(E)`
  pre {
    text-align: left;
    font-size: 14px;
    font-weight: 400;
    line-height: 17px;
    color: #999999;
    white-space: pre-wrap;
  }
`,r4=f.div`
  margin-right: 4px;
`,n4=f.div`
  display: flex;
  width: 100%;
`,i4=f.div``,s4=f(Na)`
  &:hover {
    path {
      stroke: #ffffff;
    }
  }
`,Yd=De.default.memo(({message:e,allowCopy:t,isInTable:o=!1,header:r})=>{let{t:n}=S(),i=(0,De.useRef)(null),[s,a]=(0,De.useState)(!1),[c,l]=(0,De.useState)(!1),{copied:m,copy:u}=Hi(e),g=(0,De.useCallback)(()=>{u(),We.success(n("notificationMessageCopied"))},[u,n]),x=(0,De.useCallback)(()=>{a(!s)},[s]);return(0,De.useEffect)(()=>{if(i.current){let y=i.current.scrollHeight>Qg;l(y)}},[e,s]),De.default.createElement(Q7,{isInTable:o},De.default.createElement(Z7,{isOverflow:c,onClick:c?x:void 0},De.default.createElement(n4,null,c&&De.default.createElement(r4,{"data-testid":"toggle"},s?De.default.createElement(Wa,null):De.default.createElement(Fi,null)),De.default.createElement(E,{textAlign:"left",size:14,weight:400,lineHeight:17,wordBreak:"break-word"},r)),t&&De.default.createElement(i4,{onClick:y=>{y.stopPropagation(),g()}},De.default.createElement(s4,{copied:m,fill:"#999999","data-testid":"copy-icon"}))),De.default.createElement(e4,{isOpen:s,isOverflow:c},De.default.createElement(t4,{isOpen:s,isOverflow:c},De.default.createElement("div",{ref:i},De.default.createElement(o4,null,De.default.createElement("pre",null,e))))))});var on=Jd.default.memo(({message:e,isJson:t,isInTable:o=!1,header:r})=>Jd.default.createElement(Yd,{header:r,message:e,allowCopy:t,isInTable:o})),Xc=e=>{let t=e,o=!1;try{let r=JSON.parse(e);t=JSON.stringify(r,null,2),o=!0}catch{}return{formattedMessage:t,isJson:o}};var a4=f.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  overflow: auto;
  padding: 0px 16px;
`,c4=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,l4=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
`,d4=f.div`
  display: flex;
  justify-content: center;
  margin: 16px 0;
`,p4=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  padding: 16px 0 8px;
`,m4=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,u4=f.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  gap: 8px;
`,Qc=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,Zg=e=>{let{t}=S(),{origin:o,message:r,originalMethod:n,advancedDetails:i,icon:s,networkID:a,autoConfirmStatusCode:c,simulationResults:l,scanWarnings:m,scanFailed:u,confirmApproval:g,denyApproval:x}=e,{formattedMessage:y,isJson:b}=Xc(r),h=Fn(a),w=Xg(i?.contractAddress,a),{save:T,isAutoConfirmIsTouched:v}=En(o),I=is({origin:o,autoConfirmStatusCode:c,networkID:a}),N=["eth_signTypedData_v3","eth_signTypedData_v4"].includes(n??""),B=[];w.value&&B.push(w);let V=!!l&&l?.length>0;V&&B.push({value:ye.default.createElement(on,{header:t("notificationMessageHeader"),message:y,isJson:b,isInTable:!0})});let L=async()=>{v&&await T(),g?.()},F=(m||[]).some(H=>H.severity===1),P=[{text:t("commandCancel"),onClick:x,testID:"secondary-button"}];return F||P.push({text:t("commandConfirm"),theme:F?"warning":"primary",onClick:L,testID:"primary-button"}),ye.default.createElement(ye.default.Fragment,null,ye.default.createElement(a4,null,ye.default.createElement("div",null,ye.default.createElement(Ao,{type:"SIGN_MESSAGE",domain:ee(o),iconUrl:s}),ye.default.createElement(l4,null,V?ye.default.createElement(ye.default.Fragment,null,ye.default.createElement(Qc,{color:"#999999"},t("notificationPermissionRequestText")),ye.default.createElement(Qc,{color:"#999999"},t("notificationBalanceChangesText"))):ye.default.createElement(Qc,{color:"#999999"},t("notificationBalanceChangesText"))),ye.default.createElement("div",null,(m?.length||u)&&ye.default.createElement(u4,null,m&&m?.length>0&&ye.default.createElement(ye.default.Fragment,null,m.map((H,G)=>ye.default.createElement(wt,{message:H.message,variant:H.severity,key:`simulation-warning-${G}`}))),u&&ye.default.createElement(wt,{message:t("notificationFailedToScan"),variant:1,"data-testid":"simulation-failed"})),ye.default.createElement(c4,null,ye.default.createElement("div",{"data-testid":"message"},V?l?ye.default.createElement(Jg,{rows:l}):null:ye.default.createElement(on,{header:t("notificationMessageHeader"),message:y,isJson:b})),ye.default.createElement(dr,{rows:[h,...I.showAutoConfirmRow&&N?[I]:[]]}),B.length>0&&ye.default.createElement(dr,{rows:B,header:t("notificationAdvancedDetailsText")})))),ye.default.createElement(d4,null,ye.default.createElement(Qc,{color:"#999999"},t("notificationConfirmFooter")))),ye.default.createElement(Qe,null,ye.default.createElement(An,{buttons:P}),F?ye.default.createElement(p4,null,ye.default.createElement(m4,{hoverColor:go,onClick:L,color:"#777777"},t("commandConfirmUnsafe"))):void 0))};d();p();var Ee=A(C());var f4=f.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  flex: 1;
  overflow: auto;
  padding: 0px 16px;
`,g4=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,x4=f.div`
  margin-bottom: 24px;
`,y4=f.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
  gap: 8px;
`,e2=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,h4=f.div`
  display: flex;
  justify-content: center;
  margin: 16px 0;
`,k4=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  padding: 16px 0 8px;
`,S4=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,ss=e=>{let{t}=S(),{origin:o,message:r,networkID:n,scanWarnings:i,confirmApproval:s,denyApproval:a,scanFailed:c,icon:l}=e,{formattedMessage:m,isJson:u}=Xc(r),g=(0,Ee.useCallback)(async()=>{i?.length&&e.advancedDetails&&e.advancedDetails.errorSignInWithSolana&&await W.capture("userIgnoredSignInWithMessageError",{data:{type:e.advancedDetails.errorSignInWithSolana?.type,format:e.advancedDetails.errorSignInWithSolana?.format,origin:o,error:e.advancedDetails.errorSignInWithSolana?.error}}),s?.()},[i,s,o,e.advancedDetails]),x=Fn(n),y=(i||[]).some(h=>h.severity===1),b=[{text:t(s?"commandCancel":"commandClose"),onClick:a,testID:"secondary-button"}];return!y&&s&&b.push({text:t("commandConfirm"),theme:c?"warning":"primary",onClick:()=>s(),testID:"primary-button"}),Ee.default.createElement(Ee.default.Fragment,null,Ee.default.createElement(f4,null,Ee.default.createElement("div",null,Ee.default.createElement(Ao,{type:"SIGN_MESSAGE",domain:ee(o),iconUrl:l}),Ee.default.createElement(x4,null,Ee.default.createElement(e2,{color:"#999999"},t("notificationSignMessageParagraphText"))),Ee.default.createElement("div",null,(i?.length||c)&&Ee.default.createElement(y4,null,i&&i?.length>0&&Ee.default.createElement(Ee.default.Fragment,null,i.map((h,w)=>Ee.default.createElement(wt,{message:h.message,variant:h.severity,key:`simulation-warning-${w}`}))),c&&Ee.default.createElement(wt,{message:t("notificationFailedToScan"),variant:1,"data-testid":"simulation-failed"})),Ee.default.createElement(g4,null,Ee.default.createElement("div",{"data-testid":"message"},Ee.default.createElement(on,{header:t("notificationMessageHeader"),message:m,isJson:u})),Ee.default.createElement(dr,{rows:[x]})))),Ee.default.createElement(h4,null,Ee.default.createElement(e2,{color:"#999999"},t("notificationConfirmFooter")))),Ee.default.createElement(Qe,null,Ee.default.createElement(An,{buttons:b}),y?Ee.default.createElement(k4,null,Ee.default.createElement(S4,{hoverColor:go,onClick:g,color:"#777777"},t("commandConfirmUnsafe"))):void 0))};var t2=e=>{let{icon:t,url:o,requestId:r,message:n,originalMethod:i,hexChainId:s,autoConfirmStatusCode:a}=e,c=o.origin,l=ia(s),m=(0,Xt.useMemo)(()=>hm(n),[n]),u=Fe(),g=qg(u,{networkID:l,origin:o.origin,originalMethod:i}),{data:x}=ue(),[y,b]=(0,Xt.useMemo)(()=>[x?.identifier??"",x?.type==="ledger",x?.addresses],[x]),h=(0,Xt.useCallback)(()=>{g({jsonrpc:"2.0",id:r,error:ie.userRejectedRequest(r).error}),W.capture("messageSubmissionTransactionBlockedAndClosed",{data:{origin:c}})},[r,c,g]),{pushDetailView:w}=U(),T=(0,Xt.useCallback)(async()=>{let G=_t(),re=await(async()=>(jr(de,Ai),G.sign(y,{chainType:"eip155",signingType:"message",message:Buffer.from(n.substring(2),"hex").toString("utf8")}).finally(()=>jr(de,Si))))(),Pe;switch(re.status){case"success":Pe={jsonrpc:"2.0",id:r,result:{approvalType:"hardware",signature:re.signature}};break;case"error":Pe={jsonrpc:"2.0",id:r,error:ie.userRejectedRequest(r).error};break}g(Pe)},[y,n,g,r]),v=(0,Xt.useCallback)(async()=>{b?w(Xt.default.createElement(no,null,Xt.default.createElement(xo,{ledgerApp:"EVM",ledgerAction:T,cancel:h}))):(W.capture("messageSubmissionTransactionConfirmation",{data:{originalMethod:i}}),g({jsonrpc:"2.0",id:r,result:{approvalType:"user"}}))},[b,i,w,T,h,g,r]),{data:I,isError:N,isLoading:B,isFetched:V}=Ti({networkID:l,url:o.href,type:"message",params:{message:m}},{disabled:i==="personal_sign"}),{screen:L,setScreen:F,showFrictionInterstitial:P}=Pn({block:I?.block,isLoadingSimulation:B&&!V,warnings:I?.warnings}),H=(0,Xt.useCallback)(()=>{W.capture("userIgnoredKnownMaliciousWarning",{data:{origin:c}}),F("main")},[c,F]);return L==="block"?Xt.default.createElement(Wr,{warningMessage:I?.block?.message??"",origin:c,onConfirm:H,onClose:h}):L==="loading"?Xt.default.createElement(Ur,{type:"SIGN_MESSAGE",domain:ee(c),onClose:h,icon:t}):L==="friction"?Xt.default.createElement(Mn,{onClose:h,onConfirm:v,onBack:()=>F("main"),origin:c,warningMessage:I?.warnings?.[0]?.message}):i==="eth_sign"||i==="personal_sign"?Xt.default.createElement(ss,{denyApproval:h,autoConfirmStatusCode:a,message:m,simulationResults:I?.expectedChanges||[],scanWarnings:I?.warnings||[],scanFailed:N,advancedDetails:I?.advancedDetails,confirmApproval:P?()=>F("friction"):v,networkID:l,origin:c,icon:t}):Xt.default.createElement(Zg,{denyApproval:h,autoConfirmStatusCode:a,message:m,simulationResults:I?.expectedChanges||[],scanWarnings:I?.warnings||[],scanFailed:N,advancedDetails:I?.advancedDetails,confirmApproval:P?()=>F("friction"):v,networkID:l,origin:c,icon:t,originalMethod:i})};d();p();var as=A(C());d();p();var li=A(C());d();p();var vt=A(C());var o2=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,A4=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  margin: 16px 0;
`,w4=f.div`
  display: flex;
  flex-direction: column;
`,b4=f.div`
  margin-bottom: 24px;
`,v4=f(Rt)`
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
`,C4=f.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
`,r2=({icon:e,origin:t,confirmApproval:o,denyApproval:r,originIsBlocklisted:n})=>{let{t:i}=S(),{data:s}=ue();return vt.default.createElement(vt.default.Fragment,null,vt.default.createElement(v4,null,vt.default.createElement("div",null,vt.default.createElement(Ao,{type:"CONNECT",domain:ee(t),iconUrl:e}),vt.default.createElement(b4,null,vt.default.createElement(o2,{color:"#999999"},i("notificationApplicationApprovalParagraphText"))),vt.default.createElement(Wi,{isLoading:!1},vt.default.createElement(w4,null,n&&vt.default.createElement(C4,null,vt.default.createElement(wt,{message:i("notificationApplicationApprovalWebsiteIsUnsafeWarning"),variant:1})),vt.default.createElement(dr,{rows:[{label:"Account",value:vt.default.createElement(E,{size:14,weight:400,lineHeight:17,color:"#999999"},s?.name)}]})))),vt.default.createElement(A4,null,vt.default.createElement(o2,{color:"#999999"},i("notificationApplicationApprovalConnectDisclaimer")))),vt.default.createElement(Qe,null,vt.default.createElement(gt,{primaryText:i("notificationApplicationApprovalActionButtonConnect"),primaryTheme:n?"warning":"primary",secondaryText:i("commandCancel"),onPrimaryClicked:o,onSecondaryClicked:r})))};var T4=f.div.attrs({"data-testid":"loading-wrapper"})`
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  align-items: center;
  min-height: 320px;
`,n2=li.default.memo(({isLoading:e,originIsBlocklisted:t,icon:o,origin:r,confirmApproval:n,denyApproval:i})=>{let[s,a]=li.default.useState(!1);return e?li.default.createElement(T4,null,li.default.createElement(Pt,null)):t&&!s?li.default.createElement(Wr,{origin:r,onConfirm:()=>{a(!0)},onClose:i}):li.default.createElement(r2,{origin:r,icon:o,confirmApproval:n,denyApproval:i,originIsBlocklisted:t})});var Zc=as.default.memo(({url:e,icon:t,requestId:o})=>{let r=e.origin,n=Fe(),i=0;o!==null&&(i=o);let s=(0,as.useCallback)(()=>{n({jsonrpc:"2.0",id:i,error:ie.userRejectedRequest(o).error})},[o,i,n]),a=(0,as.useCallback)(()=>{n({jsonrpc:"2.0",id:i,result:null})},[i,n]),{isLoading:c,originIsBlocklisted:l}=Ci(r);return as.default.createElement(n2,{icon:t,isLoading:c,originIsBlocklisted:l,origin:r,confirmApproval:a,denyApproval:s})});var q2=A(o3());d();p();var Lo=A(C());d();p();var Y=A(C());d();p();var Vr=A(C());var el=Vr.default.memo(({chainAddress:e,...t})=>{let{address:o,networkID:r}=e,n=be(o,4),{buttonText:i,copied:s,copy:a}=Hi(o,n),c=Su({networkID:r,address:o}),l=tp(r);return Vr.default.createElement(D4,{...t,copied:s,onClick:a},Vr.default.createElement(xt,null,Vr.default.createElement(po,{networkID:r,size:24}),c,Vr.default.createElement(i2,null,l)),Vr.default.createElement(E4,{copied:s},Vr.default.createElement(I4,null,i),s?Vr.default.createElement(Om,{width:10}):Vr.default.createElement(Na,{width:12})))}),i2=f(E).attrs({textAlign:"left",margin:"0 0 0 8px",size:14,lineHeight:17,weight:500,noWrap:!0,maxWidth:"100px"})``,I4=f(E).attrs({margin:"0 4px 0 12px",size:14,lineHeight:17,weight:500,color:"#777",noWrap:!0,maxWidth:"120px"})``,D4=f(xt)`
  cursor: pointer;
  margin-bottom: 4px;
  &:last-of-type {
    margin-bottom: 0;
  }
  &:hover ${xt}:last-child {
    p {
      color: #ab9ff2;
    }
    svg {
      path {
        ${e=>e.copied?"fill: #AB9FF2;":"stroke: #AB9FF2;"};
      }
    }
  }

  // Hide Bitcoin Address Type Badges normally
  ${Ra} {
    display: none;
  }
  // Show Bitcoin Address Type Badges on hover if applicable
  &:hover {
    ${Ra} {
      display: unset;
      background: ${dn("#FFFFFF",.2)};
    }
    // If ChainName immediately follows TitleBadgeContainer, hide it on hover
    ${Ra} + ${i2} {
      display: none;
    }
  }
`,E4=f(xt)`
  justify-content: flex-end;
  path {
    ${e=>e.copied?"fill: #777;":"stroke: #777;"};
  }
`;d();p();var tt=A(C());var Xd={cursoredPointer:_e({cursor:"pointer"}),tooltipRow:_e({paddingY:6,borderRadius:12,backgroundColor:{hover:"bgRow"}})},s2=({...e})=>tt.default.createElement(oo,{compact:!0,background:!1,className:Xd.tooltipRow,...e}),P4=tt.default.memo(({chainAddress:e,"data-testid":t})=>{let{address:o,networkID:r}=e,n=be(o,4),{buttonText:i,copy:s,copied:a}=Hi(o,n);return tt.default.createElement("div",{"data-testid":t,className:Xd.cursoredPointer,onClick:s},tt.default.createElement(s2,{start:tt.default.createElement(lu,{networkID:r,address:o}),end:tt.default.createElement(cu,{text:i,copied:a})}))}),M4=({account:e})=>{let t=e?.addresses??[],o=Cl(t),r=t.filter(n=>!o.includes(n));return tt.default.createElement(M,null,r.map(n=>tt.default.createElement(P4,{"data-testid":`account-header-chain-${n.networkID}`,key:na.toKey(n),chainAddress:n})))},F4=()=>{let{t:e}=S(),{handleShowModalVisibility:t}=Er();return tt.default.createElement("div",{className:Xd.cursoredPointer,onClick:()=>t("additionalNetworks")},tt.default.createElement(s2,{start:tt.default.createElement(M,{gap:8,alignItems:"center",direction:"row"},tt.default.createElement(J.Info,{size:18}),tt.default.createElement(R,{font:"caption",color:"textPrimary",children:e("copyAddressRowAdditionalNetworks")}))}))},tl=({account:e})=>{let t=e?.addresses??[],o=Cl(t).length>0;return tt.default.createElement(M,null,tt.default.createElement(M4,{account:e}),o&&tt.default.createElement(tt.default.Fragment,null,tt.default.createElement(M,{borderTopColor:"borderSecondary",borderTopWidth:1,borderTopStyle:"solid",marginY:4}),tt.default.createElement(F4,null)))};var ol=8,o0=64,a2=-o0-ol,N4=f(Et.div)`
  position: absolute;
  z-index: 100;
`,B4=f(Et.div)`
  width: 100%;
  height: 100%;
  margin: 0;
  background: rgba(0, 0, 0, 0.2);
  backdrop-filter: blur(1px);
  position: fixed;
  left: 0;
  top: 0;
  z-index: 2;
`,L4=f.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  flex-direction: column;
  position: absolute;
  top: ${ol}px;
  left: ${ol}px;
  width: ${o0}px;
  height: calc(100vh - ${2*ol}px);
  background: #000;
  border-radius: 8px;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  padding: 8px 8px 0px 8px;
`,O4=f.div`
  padding: 4px 12px;
`,W4=f(E).attrs({size:13,weight:500,maxWidth:"240px",noWrap:!0})``,c2=f.div`
  min-width: 270px;
  padding: ${e=>e.padding??0}px;
`,U4=f.div`
  height: 128px;
  padding: 8px 8px 0;
  border-top: 1px solid #333;
`,V4=f.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  height: 100%;
  padding: 8px 0;
  overflow: auto;
  position: relative;
`,Zd=f(E).attrs({textAlign:"left",size:16,lineHeight:19,weight:500,maxWidth:"140px",noWrap:!0})``,e0=f(E).attrs({size:16,lineHeight:19,weight:500,color:"#777"})``,l2=f.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding-bottom: ${e=>e.paddingBottom??0}px;
  padding-top: ${e=>e.paddingTop??0}px;
  padding-left: ${e=>e.paddingLeft??0}px;
  padding-right: ${e=>e.paddingRight??0}px;
  &:hover {
    cursor: ${e=>e.isDisabled?"not-allowed":"pointer"};

    ${Zd}, ${e0} {
      color: #ab9ff2;
    }
  }
`,t0=f(E).attrs({margin:"2px 0 0",size:10,lineHeight:12,weight:600,maxWidth:`${o0-8}px`,noWrap:!0})``,H4=f.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  margin-bottom: 8px;
  cursor: pointer;

  ${Ss} {
    transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    transition-duration: 250ms;
    transition-property: border, background, color;
    background: ${e=>e.isSelected?"#ab9ff2":"#333"};
    border: 2px solid ${e=>e.isSelected?"#ab9ff2":"transparent"};
    color: ${e=>e.isSelected?"#000":"#fff"};

    p {
      color: currentColor;
    }
  }

  ${t0} {
    transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
    transition-duration: 250ms;
    transition-property: color;
    color: ${e=>e.isSelected?"#ab9ff2":"#777"};
  }

  &:hover {
    ${Ss} {
      cursor: ${e=>e.isDisabled?"not-allowed":"pointer"};
      background: #e2dffe;
      border: 2px solid #e2dffe;
      color: #000;
    }

    ${t0} {
      color: #e2dffe;
    }
  }
`,d2=f.div`
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  cursor: pointer;
  transition: fill 200ms ease;
  min-height: 32px;
  min-width: 32px;
  margin-bottom: 8px;

  svg,
  path {
    fill: #777777;
  }

  :hover {
    background: #fff;

    svg,
    path {
      fill: #000;
    }
  }
`,p2=Y.default.memo(({isOpen:e,onClose:t,accountDisabler:o,disableAccountManagement:r})=>{let{closeAllModals:n}=tu(),i=!fn(),{data:s}=te(),{data:a}=Sa(),{data:c}=ue(),{mutate:l}=zp(),m=(0,Y.useRef)(null),u=()=>m.current?.scrollIntoView({block:"nearest"}),g=(0,Y.useCallback)(y=>{n(),l({identifier:y}),t()},[n,t,l]),x=(0,Y.useMemo)(()=>s?s.map((y,b)=>Y.default.createElement(_4,{key:y.identifier,account:y,index:b,isBalanceVisible:i,accountBalance:a?.[y.identifier]?.balance,selectedAccountId:c?.identifier??"",selectedRef:m,setAccount:g,disabled:o?o(y):!1})):null,[s,i,a,c?.identifier,g,o]);return Y.default.createElement(G4,{accounts:x,isOpen:e,disableAccountManagement:r,onClose:t,scrollIntoView:u})}),_4=Y.default.memo(({account:e,index:t,isBalanceVisible:o,accountBalance:r,selectedAccountId:n,selectedRef:i,setAccount:s,disabled:a})=>{let c=(0,Y.useRef)(null),l=e.identifier===n,[m,u]=(0,Y.useState)(!1),g=l||m?"#000":"#FFF",x=(0,Y.useCallback)(()=>{a||(c.current?.close(),s(e.identifier))},[e,a,s]);return Y.default.createElement(Vn,{alignment:"right",tooltipRef:c,index:t,content:Y.default.createElement(R4,{account:e,isBalanceVisible:o,accountBalance:r,disabled:a,onClick:x})},Y.default.createElement(H4,{isSelected:l,isDisabled:a,onMouseEnter:()=>u(!0),onMouseLeave:()=>u(!1),ref:l?i:null,onClick:x,role:"button"},Y.default.createElement($e,{accountIndex:t,accountName:e.name,accountIcon:e.icon,size:"medium",iconColor:g}),Y.default.createElement(t0,null,e.name)))}),G4=Y.default.memo(({accounts:e,isOpen:t,disableAccountManagement:o,onClose:r,scrollIntoView:n})=>{let{showSettingsMenu:i}=Gt(),{t:s}=S(),a=(0,Y.useCallback)(m=>{i(m,Y.default.createElement(Hc,null)),r()},[r,i]),c=(0,Y.useCallback)(m=>{i(m,Y.default.createElement(es,null)),r()},[r,i]),l=(0,Y.useCallback)(m=>{i(m,Y.default.createElement(hc,null)),r()},[r,i]);return(0,Y.useEffect)(()=>{t&&n()},[t]),Y.default.createElement(Sn,{initial:!1},t&&Y.default.createElement(Y.default.Fragment,null,Y.default.createElement(B4,{onClick:r,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{ease:"linear",duration:.125}}),Y.default.createElement(N4,{initial:{x:a2},animate:{x:0},exit:{x:a2},transition:{ease:"easeOut",duration:.125}},Y.default.createElement(L4,{"data-testid":"account-menu"},Y.default.createElement(d2,{onClick:r,role:"button","aria-label":s("commandClose")},Y.default.createElement(Vm,null)),Y.default.createElement(V4,{id:"accounts"},e),!o&&Y.default.createElement(U4,null,Y.default.createElement(Qd,{Icon:Y.default.createElement(br,{width:16}),testId:"sidebar_menu-button-add_account",text:s("addAccountPrimaryText"),onClick:l}),Y.default.createElement(Qd,{Icon:Y.default.createElement(Ba,{width:16}),testId:"sidebar_menu-button-manage_accounts",text:s("settingsManageAccounts"),onClick:c}),Y.default.createElement(Qd,{Icon:Y.default.createElement(Lm,{width:16}),testId:"sidebar_menu-button-settings",text:s("settings"),onClick:a}))))))}),Qd=({Icon:e,testId:t,text:o,onClick:r,ariaLabel:n})=>{let i=(0,Y.useRef)(null);return Y.default.createElement(Vn,{alignment:"rightCenter",tooltipRef:i,index:0,content:Y.default.createElement(O4,null,Y.default.createElement(W4,null,o))},Y.default.createElement(d2,{onClick:r,"data-testid":t,role:"button","aria-label":n??o},e))},R4=({account:e,isBalanceVisible:t,accountBalance:o,disabled:r,onClick:n})=>{let{data:[i]}=He(["enable-consolidated-addresses"]);return i?Y.default.createElement(c2,{padding:4},Y.default.createElement(l2,{isDisabled:r,onClick:n,paddingBottom:8,paddingTop:16,paddingLeft:16,paddingRight:8},Y.default.createElement(Zd,null,e.name),t&&o?Y.default.createElement(e0,null,yl(o.value)):null),Y.default.createElement(tl,{account:e})):Y.default.createElement(c2,{padding:16},Y.default.createElement(l2,{isDisabled:r,onClick:n,paddingBottom:16},Y.default.createElement(Zd,null,e.name),t&&o?Y.default.createElement(e0,null,yl(o.value)):null),e.addresses.map((s,a)=>Y.default.createElement(el,{key:`${s.networkID}-${a}`,chainAddress:s})))};var m2=(0,Lo.createContext)({isOpen:!1,showMenu:hl,hideMenu:hl}),u2=Lo.default.memo(({children:e,accountDisabler:t,disableAccountManagement:o})=>{let[r,n]=(0,Lo.useState)(!1),i=(0,Lo.useCallback)(()=>{n(!0)},[]),s=(0,Lo.useCallback)(()=>{n(!1)},[]);return Lo.default.createElement(m2.Provider,{value:{isOpen:r,showMenu:i,hideMenu:s}},e,Lo.default.createElement(p2,{isOpen:r,onClose:s,accountDisabler:t,disableAccountManagement:o}))}),f2=()=>(0,Lo.useContext)(m2);d();p();var r0=A(C());d();p();var Ge=A(C());d();p();var rl={address:"_51gazn18w _51gazn1ar _51gazn1c1",iconWrapper:"_51gazn18w _51gazn1ar _51gazn1b4",tooltipContent:"mlnwxx3 _51gazn4e _51gazn34 _51gazn5o _51gazn1u",tooltipContentSmallPadding:"mlnwxx5 _51gazn49 _51gazn2z _51gazn5j _51gazn1p"};var z4=14,$4=({account:e,accountName:t})=>{let o=qr(),{data:[r]}=He(["enable-consolidated-addresses"]);return!e||e.addresses.length===0?Ge.default.createElement(Cr,{height:"10px",backgroundColor:"#2D2D2D",borderRadius:"8px",width:"100px",margin:"8px 0 0 8px"}):Ge.default.createElement(Vn,{alignment:"bottomLeft",paddingOffset:o?z4:0,index:0,content:Ge.default.createElement("div",{className:r?rl.tooltipContentSmallPadding:rl.tooltipContent},r?Ge.default.createElement(tl,{account:e}):e.addresses.map(n=>Ge.default.createElement(el,{"data-testid":`account-header-chain-${n.networkID}`,key:na.toKey(n),chainAddress:n}))),closeOnSecondClick:!1},Ge.default.createElement(M,{direction:"row",minWidth:0,alignItems:"center",overflow:"hidden"},Ge.default.createElement(R,{"data-testid":"home-header-account-name",font:"captionSemibold",className:Qu,children:t,color:"textPrimary",truncate:"ellipsis"}),Ge.default.createElement("div",{className:rl.iconWrapper},Ge.default.createElement(J.Copy,{size:14}))))},Ks=Ge.default.memo(({rightMenuButton:e,...t})=>{let{data:o}=te(),{data:r}=Go(),n=qr(),i=un(),{showMenu:s}=f2(),{account:a,accountIndex:c,accountIcon:l,accountName:m,shouldShowTooltip:u}=(0,Ge.useMemo)(()=>{if(o){let g=Math.max(0,o.findIndex(y=>y.identifier===r)),x=o[g];if(x)return{account:x,accountIndex:g,accountIcon:n?i:x.icon?x.icon:mn,accountName:x.name,shouldShowTooltip:x.addresses.length>1}}return{account:null,accountIndex:0,accountIcon:i??mn,accountName:"",shouldShowTooltip:!1}},[o,r,i,n]);return Ge.default.createElement(Va,{...t},Ge.default.createElement(M,{direction:"row",width:"100%",gap:4,alignItems:"center"},Ge.default.createElement($e,{testID:"settings-menu-open-button",accountIcon:l,accountIndex:c,accountName:m,size:"small",onClick:s}),Ge.default.createElement(M,{width:"100%",minWidth:0},n&&Ge.default.createElement(M,{width:"100%",direction:"row",alignItems:"center",overflow:"hidden"},Ge.default.createElement(R,{testID:"home-header-username",truncate:"ellipsis",font:"labelSemibold",color:"textSecondary",className:Zu,children:`@${n}`}),cn()==="https://staging-api.phantom.app"?Ge.default.createElement(Ii,{backgroundColor:"accentPrimary",size:"small"},"STAGING"):null),Ge.default.createElement($4,{account:a??void 0,accountName:m,accountIndex:c,shouldShowTooltip:u})),e?Ge.default.createElement(M,{direction:"row",alignItems:"center"},e):null))});Ks.defaultProps={justifyContent:"space-between"};var g2=e=>r0.createElement(Gi,null,r0.createElement(Ks,null),e.children);d();p();var x2=A(A0());var y2=e=>{let{data:t}=xr({gcTime:0,queryKey:["tabMeta",e],queryFn:async()=>{if(e===void 0)return;let o=await x2.default.tabs.get(e),r=await km(e);return{iconUrl:r?.iconUrl??o?.favIconUrl,title:r?.title??o?.title}}});return t??{}};d();p();var Ys=A(C());var q4=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.8); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,j4=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-20}%,-1px) scale(.8); opacity:0;}
`,K4="0%{opacity:0;} 100%{opacity:1;}",Y4="0%{opacity:1;} 100%{opacity:0;}",J4=(()=>{let e;return()=>{if(e===void 0&&typeof self<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),X4=(e,t)=>{let[r,n]=J4()?[K4,Y4]:[q4(-1),j4(-1)];return{animation:t?`${Mt(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Mt(n)} 0.25s forwards cubic-bezier(.06,.71,.55,1)`}},Q4=_e({width:"100%"}),h2=Ys.default.memo(()=>Ys.default.createElement(Zm,{containerStyle:{bottom:Ht.paddingScreen},toastOptions:{position:"bottom-center",duration:3e3}},e=>{let t=e.height?X4(e.position||"bottom-center",e.visible):{opacity:0};return Ys.default.createElement("div",{className:Q4,style:{...t,maxWidth:Di}},Ys.default.createElement(xm,{children:String(Qm(e.message,e)),type:e.type==="success"||e.type==="error"?e.type:"success",onClose:()=>Ft.dismiss(e.id)}))}));d();p();var k2=A(C());var S2=({children:e})=>{let[t,o,r]=r1();return k2.createElement(o1.Provider,{value:{isConnected:t,lastMessage:o,postMessage:r}},e)};d();p();var _=A(C());var Z4=(0,_.lazy)(()=>import("./ExternalLinkWarning-CYX76CMT.js")),e6=(0,_.lazy)(()=>import("./NotEnoughSol-N7E7QFPN.js")),t6=(0,_.lazy)(()=>import("./CollectibleBurnPage-VEBLLQEN.js")),o6=(0,_.lazy)(()=>import("./CreateListingPage-6YJKXNOP.js")),r6=(0,_.lazy)(()=>import("./EditListingPage-432AEXCS.js")),n6=(0,_.lazy)(()=>import("./ListingsErrorPage-46T3R6RC.js")),i6=(0,_.lazy)(()=>import("./DepositAddressPage-NNV2DW3I.js")),s6=(0,_.lazy)(()=>import("./StakeAccountCreateAndDelegateStatusPage-ODXD7V74.js")),a6=(0,_.lazy)(()=>import("./StakeAccountDelegateStakeStatusPage-QXS4STBX.js")),c6=(0,_.lazy)(()=>import("./StakeAccountDetailPage-OMIOSUOF.js")),l6=(0,_.lazy)(()=>import("./StakeAccountWithdrawStakeStatusPage-LVUST3RO.js")),d6=(0,_.lazy)(()=>import("./StakeAmountPage-VME5XTAB.js")),p6=(0,_.lazy)(()=>import("./ValidatorListPage-DDROFTTF.js")),m6=(0,_.lazy)(()=>import("./StakeAccountDeactivateStakeStatusPage-TXELBZVJ.js")),Jo=({children:e})=>_.default.createElement(Bi,null,_.default.createElement(_.Suspense,null,e)),n0=({children:e})=>{let[t,o]=(0,_.useState)(void 0),[r,n]=(0,_.useState)(!1),[i,s]=(0,_.useState)(void 0),[a,c]=(0,_.useState)(void 0),[l,m]=(0,_.useState)(void 0),[u,g]=(0,_.useState)(!1),[x,y]=(0,_.useState)(),{closeAllModals:b}=Er(),h=Te=>{o(Te)},w=()=>{o(void 0)},T=()=>{W.capture("showNotEnoughSolModal"),n(!0)},v=()=>{n(!1)},I=Te=>{s(Te)},N=()=>{s(void 0)},B=Te=>{W.capture("showCollectibleListingModal"),c(Te)},V=()=>{c(void 0)},L=Te=>{W.capture("showEditCollectibleListingModal"),m(Te)},F=()=>{m(void 0)},P=Te=>{W.capture("showCollectibleBurnModal"),y(Te)},H=()=>{y(void 0)},[G,X]=(0,_.useState)(null),re=G&&_.default.createElement(p6,{...G}),Pe=Te=>{X(Ve=>(Ve||W.capture("showValidatorListModal"),Te))},Zt=()=>{X(()=>null)},[Me,lo]=(0,_.useState)(null),Xe=Me&&_.default.createElement(d6,{...Me}),wo=Te=>{lo(Ve=>(Ve||W.capture("showStakeAmountModal"),Te))},bo=()=>{lo(()=>null)},[Le,It]=(0,_.useState)(null),mt=Le&&_.default.createElement(c6,{...Le}),eo=Te=>{It(Ve=>(Ve||W.capture("showStakeAccountModal"),Te))},ot=()=>{It(()=>null)},[Wo,vo]=(0,_.useState)(null),ze=Wo&&_.default.createElement(s6,{...Wo}),le=Te=>{vo(Ve=>(Ve||W.capture("showStakeAccountCreateAndDelegateStatusModal"),Te))},mr=()=>{vo(()=>null)},[Co,Gr]=(0,_.useState)(null),Uo=Co&&_.default.createElement(m6,{...Co}),nn=Te=>{Gr(Ve=>(Ve||W.capture("showStakeAccountDeactivateStakeStatusModal"),Te))},Rr=()=>{Gr(()=>null)},[cs,ls]=(0,_.useState)(null),mi=cs&&_.default.createElement(a6,{...cs}),ur=Te=>{ls(Ve=>(Ve||W.capture("showStakeAccountDelegateStakeStatusModal"),Te))},Ln=()=>{ls(()=>null)},[On,ds]=(0,_.useState)(null),ps=On&&_.default.createElement(l6,{...On}),pl=Te=>{ds(Ve=>(Ve||W.capture("showStakeAccountWithdrawStakeStatusModal"),Te))},Xs=()=>{ds(()=>null)},sn=()=>{g(!0)},zr=()=>{g(!1)},Wn=()=>{b(),r&&n(!1),N(),zr(),V(),F(),H(),Zt(),bo(),ot(),mr(),Rr(),Ln(),Xs()};return _.default.createElement(eu.Provider,{value:{closeAllModals:Wn,hideNotEnoughSolModal:v,showNotEnoughSolModal:T,showListingsErrorModal:sn,hideListingsErrorModal:zr,showExternalLinkWarningModal:h,hideExternalLinkWarningModal:w,showDepositFungibleModal:I,hideDepositFungibleModal:N,showCollectibleListingModal:B,hideCollectibleListingModal:V,showEditCollectibleListingModal:L,hideEditCollectibleListingModal:F,showCollectibleBurnModal:P,hideCollectibleBurnModal:H,showStakeAccountCreateAndDelegateStatusModal:le,hideStakeAccountCreateAndDelegateStatusModal:mr,showStakeAccountDeactivateStakeStatusModal:nn,hideStakeAccountDeactivateStakeStatusModal:Rr,showStakeAccountDelegateStakeStatusModal:ur,hideStakeAccountDelegateStakeStatusModal:Ln,showStakeAccountDetailModal:eo,hideStakeAccountDetailModal:ot,showStakeAccountWithdrawStakeStatusModal:pl,hideStakeAccountWithdrawStakeStatusModal:Xs,showValidatorListModal:Pe,hideValidatorListModal:Zt,showStakeAmountModal:wo,hideStakeAmountModal:bo}},e,_.default.createElement(ro,{isOpen:r},_.default.createElement(Jo,null,_.default.createElement(e6,{onCancelClick:v}))),_.default.createElement(ro,{isOpen:i!==void 0},_.default.createElement(Jo,null,i!==void 0&&_.default.createElement(i6,{...i}))),_.default.createElement(ro,{isOpen:!!a},_.default.createElement(Jo,null,_.default.createElement(o6,null))),_.default.createElement(ro,{isOpen:!!l},_.default.createElement(Jo,null,_.default.createElement(r6,null))),_.default.createElement(ro,{isOpen:u},_.default.createElement(_.Suspense,null,_.default.createElement(n6,null))),_.default.createElement(ro,{isOpen:!!x},x!==void 0&&_.default.createElement(Jo,null,_.default.createElement(t6,{mintPubkey:x?.chainData?.mint??"",collectionPubKey:x.collection.isValidCollectionId?x.collection.id:null,tokenStandard:(x?.chainData).standard,media:x.media,amount:x.balance,name:x.name??"",compression:x.chainData.compression,programId:(x?.chainData).programId}))),_.default.createElement(ro,{isOpen:!!re},_.default.createElement(Jo,null,re)),_.default.createElement(ro,{isOpen:!!Xe},_.default.createElement(Jo,null,Xe)),_.default.createElement(ro,{isOpen:!!mt},_.default.createElement(Jo,null,mt)),_.default.createElement(ro,{isOpen:!!ze},_.default.createElement(Jo,null,ze)),_.default.createElement(ro,{isOpen:!!Uo},_.default.createElement(Jo,null,Uo)),_.default.createElement(ro,{isOpen:!!mi},_.default.createElement(Jo,null,mi)),_.default.createElement(ro,{isOpen:!!ps},_.default.createElement(Jo,null,ps)),_.default.createElement(ro,{isOpen:t!==void 0},_.default.createElement(_.Suspense,null,t&&_.default.createElement(Z4,{...t}))))};d();p();var rn=A(C());var u6=_t(),f6=e=>{let{url:t,requestId:o,messageBytes:r,icon:n}=e,i=t.origin,{data:s}=ue(),a=s?.identifier??"",{networkID:c,addressType:l}=s?.addresses?.find(w=>j.isBitcoinNetworkID(w.networkID))??{addressType:"bip122_p2wpkh",networkID:Ne.Bitcoin.Mainnet},m=s?.type==="ledger",u=(0,rn.useCallback)(async w=>{let T=await u6.sign(a,{chainType:l,signingType:"message",message:w});if(T.status==="error")throw new Error(`[btc provider][${T.type}] ${T.message}`);let v=T.message;if(!v)throw new Error("[btc sign message notification] no message on vault response");return{type:"send",signature:Buffer.from(T.signature,"hex"),signedMessage:v}},[a,l]),{pushDetailView:g}=U(),x=Fe(),y=(0,rn.useCallback)(()=>{let w=ie.userRejectedRequest(o);x(w)},[o,x]),b=(0,rn.useCallback)(async()=>{try{let w=await u(r);x(ie.result(o,w))}catch(w){let T=ie.internalError(o,w.message);x(T)}},[u,r,o,x]),h=(0,rn.useCallback)(()=>{m?g(rn.default.createElement(no,null,rn.default.createElement(xo,{ledgerApp:"Bitcoin",ledgerAction:b,cancel:y}))):x(ie.result(o,{type:"signAndSend"}))},[y,m,x,g,o,b]);return{networkID:c,message:Buffer.from(r).toString("utf8"),icon:n,origin:i,requestId:o,confirmApproval:h,denyApproval:y}},A2=e=>{let t=f6({...e});return rn.default.createElement(ss,{...t})};d();p();var w2=A(r3()),Oo=A(C());var g6=async({message:e,accountIdentifier:t,addressType:o,inputsToSign:r,finalize:n})=>{let i=await _t().sign(t,{chainType:o,signingType:"transaction",message:Buffer.from(e).toString("hex"),inputsToSign:r,finalize:n});if(i.status==="error")throw new Error(`[btc provider][${i.type}] ${i.message}`);return{type:"send",signature:Buffer.from(i.signature,"hex")}},b2=e=>{let{url:t,requestId:o,message:r,icon:n,inputsToSign:i,finalize:s}=e,a=ee(t.origin),c=t.origin,{data:l}=ue(),m=l?.identifier??"",{networkID:u,addressType:g,address:x}=l?.addresses?.find(re=>j.isBitcoinNetworkID(re.networkID))??{addressType:"bip122_p2wpkh",networkID:Ne.Bitcoin.Mainnet},y=l?.type==="ledger",{pushDetailView:b}=U(),h=Fe(),w=(0,Oo.useCallback)(async()=>{try{let re=await g6({message:Buffer.from(r,"hex"),accountIdentifier:m,addressType:g,inputsToSign:i,finalize:s});h(ie.result(o,re))}catch(re){let Pe=ie.internalError(o,re.message);h(Pe)}},[i,m,g,s,r,o,h]),T=(0,Oo.useCallback)(()=>{let re=ie.userRejectedRequest(o);h(re)},[o,h]),v=(0,Oo.useCallback)(()=>{y?b(Oo.default.createElement(no,null,Oo.default.createElement(xo,{ledgerApp:"Bitcoin",ledgerAction:w,cancel:T}))):h(ie.result(o,{type:"signAndSend"}))},[w,h,o,T,y,b]),I=l?.addresses.map(re=>re.address),{isError:N,data:B,isFetched:V,isLoading:L}=hn({networkID:Ne.Bitcoin.Mainnet,type:"transaction",url:t.href,userAccount:x??"",params:{transaction:r,userAddresses:I??[]}}),{screen:F,setScreen:P,showFrictionInterstitial:H}=Pn({block:B?.block,isLoadingSimulation:L&&!V,warnings:B?.warnings}),G=(0,Oo.useCallback)(()=>{W.capture("userIgnoredKnownMaliciousWarning",{data:{origin:c}}),P("main")},[c,P]),X={networkID:u,psbt:w2.Psbt.fromHex(r),vb:0,inputsToSign:[],psbtChain:void 0};return F==="block"?Oo.default.createElement(Wr,{warningMessage:B?.block?.message||"",origin:c,onConfirm:G,onClose:T}):F==="loading"?Oo.default.createElement(Ur,{type:"APPROVE_TRANSACTION",icon:n,domain:a,onClose:T}):F==="friction"?Oo.default.createElement(Mn,{onClose:T,onConfirm:v,onBack:()=>P("main"),origin:c,warningMessage:B?.warnings?.[0]?.message}):Oo.default.createElement(en,{advancedDetails:Oo.default.createElement(tn,{copyTransactions:[],scanResult:B,networkID:u}),confirmApproval:H?()=>P("friction"):v,denyApproval:T,hasSimulationFailed:N,numTransactions:1,domain:a,simulationResults:B?.expectedChanges??[],simulationWarnings:B?.warnings??[],icon:n,networkID:X.networkID,isErrorNativeTokenBalance:!1,isErrorNetworkFee:!1,isLoading:!1,networkFeeRows:[],showFriction:H})};d();p();var nl=A(C());d();p();var Hr=A(C());function Nn({origin:e,title:t,children:o,onClose:r,...n}){let{t:i}=S();return Hr.default.createElement(x6,{...n},Hr.default.createElement(y6,null,Hr.default.createElement(h6,null,Hr.default.createElement(J.XCircle,{size:36,color:"accentAlert"}),Hr.default.createElement(k6,null,t),Hr.default.createElement(S6,null,e)),Hr.default.createElement(A6,null,o)),Hr.default.createElement(w6,null,Hr.default.createElement(oe,{onClick:r,"data-testid":"button-close"},i("commandClose"))))}var x6=f.div`
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100%;
  width: 100%;
  padding: 16px;
  gap: 16px;
`,y6=f.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
`,h6=f.div`
  display: flex;
  flex-direction: column;
  gap: 8px;
`,k6=f.h1`
  font-size: 28px;
  font-weight: 600;
  line-height: 32px;
  letter-spacing: -2%;
  color: #fff;
`,S6=f.p`
  font-size: 13px;
  line-height: 16px;
  color: #999999;
`,A6=f.p`
  font-size: 16px;
  line-height: 20px;
  color: #fff;
`,w6=f.div`
  display: flex;
  gap: 16px;
`;var v2=f(E)`
  text-align: left;
  font-size: 16px;
  line-height: 20px;
  strong {
    font-weight: 600;
  }
`,C2=({url:e,connectedChainId:t,messageChainId:o})=>{let{t:r}=S(),n=e.origin;return nl.createElement(Nn,{"data-testid":"incorrect-chain_id",origin:n,title:r("notificationIncorrectEIP712ChainId"),onClose:()=>self.close()},nl.createElement(v2,null,r("notificationIncorrectEIP712ChainId")),nl.createElement(v2,{style:{marginTop:16}},r("notificationIncorrectEIP712ChainIdDescription",{messageChainId:parseInt(o,16),connectedChainId:parseInt(t,16)})))};d();p();var il=A(C());var T2=f(E)`
  text-align: left;
  font-size: 16px;
  line-height: 20px;
  strong {
    font-weight: 600;
  }
`,I2=({hostname:e,enabledChains:t})=>{let{t:o}=S(),[r,n,i]=t==="testnet"?[o("notificationIncorrectModeInTestnetTitle"),o("notificationIncorrectModeInTestnetDescription",{origin:e}),o("notificationIncorrectModeInTestnetProceed")]:[o("notificationIncorrectModeNotInTestnetTitle"),o("notificationIncorrectModeNotInTestnetDescription",{origin:e}),o("notificationIncorrectModeNotInTestnetProceed")];return il.createElement(Nn,{origin:e,title:r,onClose:()=>self.close(),"data-testid":"incorrect-mode"},il.createElement(T2,null,n),il.createElement(T2,{style:{marginTop:16}},i))};d();p();var Ct=A(C());var b6=f.div`
  display: flex;
  flex-direction: row;
  align-items: center;

  > * {
    margin: 0px ${e=>e.spacing}px;
    text-align: left;
  }
`,v6=f.div`
  margin-bottom: 24px;
`,C6=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,D2=Ct.default.memo(({icon:e,url:t,requestId:o})=>{let r=t.origin,n=Fe(),i=0;o!==null&&(i=o);let s=(0,Ct.useCallback)(()=>{n({jsonrpc:"2.0",id:i,error:ie.userRejectedRequest(o).error})},[o,i,n]);return Ct.default.createElement(T6,{icon:e,origin:r,denyApproval:s})}),T6=({icon:e,origin:t,denyApproval:o})=>{let{t:r}=S(),n=()=>o(t);return Ct.default.createElement(Gi,null,Ct.default.createElement(Ks,null),Ct.default.createElement(Rt,null,Ct.default.createElement(Ao,{type:"SIGN_MESSAGE",domain:ee(t),iconUrl:e}),Ct.default.createElement(v6,null,Ct.default.createElement(C6,{color:"#999999"},r("notificationSignMessageParagraphText"))),Ct.default.createElement(I6,null,Ct.default.createElement(wt,{message:r("bottomSheetReadOnlyAccountDescription"),variant:2})),Ct.default.createElement(t1,null,Ct.default.createElement(b6,{spacing:4},Ct.default.createElement(E,{size:14,lineHeight:17,color:"#777"},r("bottomSheetReadOnlyWarning"))))),Ct.default.createElement(Qe,null,Ct.default.createElement(oe,{onClick:n,"data-testid":"secondary-button"},r("commandCancel"))))},I6=f.div`
  margin-bottom: 12px;
`;d();p();var E2=A(ea()),Re=A(C());d();p();var sl=A(C());async function D6(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture transaction signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userApprovedAction(W,{...o,origin:e.origin,method:"signAllTransactions"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (approve)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserApprove: ${typeof t}`),"provider")}}async function E6(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture transaction signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userDeniedAction(W,{...o,origin:e.origin,method:"signAllTransactions"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (deny)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserDeny: ${typeof t}`),"provider")}}function P6(e,t){return(o,r)=>{let{networkID:n,origin:i}=r,s=(0,sl.useRef)(!1);return(0,sl.useCallback)(async a=>(n&&j.isSolanaNetworkID(n)&&!s.current&&("error"in a?await t({networkID:n,origin:i}):await e({networkID:n,origin:i}),s.current=!0),o(a)),[o,n,i])}}var i0=P6(D6,E6);d();p();var al=A(C());async function M6(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture transaction signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userApprovedAction(W,{...o,origin:e.origin,method:"signTransaction"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (approve)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserApprove: ${typeof t}`),"provider")}}async function F6(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture transaction signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userDeniedAction(W,{...o,origin:e.origin,method:"signTransaction"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (deny)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserDeny: ${typeof t}`),"provider")}}function N6(e,t){return(o,r)=>{let{networkID:n,origin:i}=r,s=(0,al.useRef)(!1);return(0,al.useCallback)(async a=>(n&&j.isSolanaNetworkID(n)&&!s.current&&("error"in a?await t({networkID:n,origin:i}):await e({networkID:n,origin:i}),s.current=!0),o(a)),[o,n,i])}}var s0=N6(M6,F6);var P2=["signAndSendAllTransactions","signAllTransactions"],B6=()=>{let{data:e}=ue();return(0,Re.useMemo)(()=>{let t=e?.addresses?.find(i=>j.isSolanaNetworkID(i.networkID)),o=t?.address,r=t?t.networkID:void 0,n=e?.type==="ledger";return{accountIdentifier:e?.identifier,owner:o??"",networkID:r,isLedgerAccount:n}},[e])},L6=({transactionPayload:e,requestId:t,origin:o,method:r,networkID:n,owner:i,accountIdentifier:s})=>{let{t:a}=S(),{connection:c}=vi(),{pushDetailView:l,popDetailView:m}=U(),u=Fe(),g=s0(u,{networkID:n,origin:o}),x=Fe(),y=i0(x,{networkID:n,origin:o});return{ledgerSignAllTransactions:(0,Re.useCallback)(async()=>{let h=new Yr;if(!s||!n||!e||Array.isArray(e)&&e.length===0)throw new Error("missing accountIdentifier or chainId or transaction");let w=P2.includes(r);try{let T=_t(),v={ownerAddress:i,networkID:n,data:{signature:""},type:"dappInteraction",display:{summary:{topLeft:{text:a("transactionsPendingAppInteraction")}}}},I=[];for(let B=0;B<e.length;B++){let V=e[B];jr(de,Ai),I.push(await om({accountIdentifier:s,feePayer:new $r.PublicKey(i),connection:c,accountSigner:T,transaction:V,pendingTransactionInput:v,storage:h}).finally(()=>jr(de,Si)))}let N=I.map(B=>({signedTransaction:tm.serialize(B).transaction,signature:E2.default.encode(m0(B)[0]),version:"version"in B?B.version:"legacy"}));w?y(ie.result(t,{type:"send",result:N})):g(ie.result(t,{type:"send",...N[0]}))}catch(T){throw Nu(T)&&l(Re.default.createElement(no,null,Re.default.createElement(Bu,{ledgerActionError:T,onRetryClick:()=>{m()},onCancelClick:()=>{let v=ie.userRejectedRequest(t);w?y(v):g(v)}}))),new Error("error signing with ledger: "+T,{cause:T})}},[s,n,e,r,i,a,c,y,t,g,l,m])}},O6=e=>{let t=e.url.origin,o=ee(t),{t:r}=S(),{data:n}=Ac(),{data:[i],isFetched:s}=He(["enable-safeguard-frontend"]),{accountIdentifier:a,owner:c,networkID:l=Ne.Solana.Mainnet,isLedgerAccount:m}=B6(),{isError:u,data:g,isFetched:x,isLoading:y}=hn({networkID:l,type:"transaction",url:e.url.href,userAccount:c,params:{transactions:e.transactions,method:e.originalMethod,safeguard:{enabled:i}}},{disabled:!s}),b=rm(g),h=b??e.transactions,w=(0,Re.useMemo)(()=>{let ze=[];for(let le of h){let mr=ta(le,"bs58");ze.push(mr)}return ze},[h]),{ledgerSignAllTransactions:T}=L6({transactionPayload:w,requestId:e.requestId,origin:t,method:e.originalMethod,networkID:l,owner:c,accountIdentifier:a}),{pushDetailView:v}=U(),I=Fe(),N=s0(I,{networkID:l,origin:t}),B=Fe(),V=i0(B,{networkID:l,origin:t}),L=(0,Re.useMemo)(()=>P2.includes(e.originalMethod)?V:N,[e.originalMethod,V,N]),{connection:F}=vi(),{data:P,isLoading:H,isError:G}=Bl(F,h),{fungible:X}=Ll({key:"SolanaNative"}),re=(0,Re.useMemo)(()=>{let ze=X?.data.amount??"";return ze?P?BigInt(ze)>P.estimatedFee:!0:!1},[X,P]),Pe=g?.error===xn.INSUFFICIENT_GAS||g?.error===xn.INSUFFICIENT_FUNDS||!re,{screen:Zt,setScreen:Me,showFrictionInterstitial:lo}=Pn({block:g?.block,isLoadingSimulation:y&&!x,warnings:g?.warnings}),Xe=(0,Re.useCallback)(()=>{let ze=ie.userRejectedRequest(e.requestId);L(ze)},[e.requestId,L]),{save:wo,isAutoConfirmIsTouched:bo}=En(e.url.origin),Le=(0,Re.useCallback)(async()=>{bo&&await wo(),m?v(Re.default.createElement(no,null,Re.default.createElement(xo,{ledgerAction:T,cancel:Xe}))):L(ie.result(e.requestId,{type:"signAndSend",overwriteTransactions:b}))},[bo,wo,m,v,T,Xe,L,e.requestId,b]),It=(0,Re.useCallback)(()=>{W.capture("userIgnoredKnownMaliciousWarning",{data:{origin:t}}),Me("main")},[t,Me]),mt=is({origin:e.url.origin,autoConfirmStatusCode:e.autoConfirmStatusCode,networkID:l||void 0}),eo={topLeft:mt.label,topRight:mt.value,bottomLeft:mt.description,onClick:mt.onClick},ot=(0,Re.useMemo)(()=>n?h.map(le=>Buffer.from(ta(le,"bs58").serialize()).toString("base64")):void 0,[n,h]),Wo=(0,Re.useMemo)(()=>P?.highFees?r("notificationTransactionApprovalNetworkFeeHighWarning"):Pe?r("transactionNotEnoughNative",{nativeTokenSymbol:"SOL"}):G?r("networkFeeCouldNotFetch"):void 0,[P,r,Pe,G]);if(Zt==="block")return Re.default.createElement(Wr,{warningMessage:g?.block?.message??"",origin:e.url.origin,onConfirm:It,onClose:Xe});let vo={networkID:l??"solana:101",transaction:w};return Zt==="loading"?Re.default.createElement(Ur,{type:h.length===1?"APPROVE_TRANSACTIONS":"APPROVE_TRANSACTION",icon:e.icon,domain:o,onClose:Xe}):Zt==="friction"?Re.default.createElement(Mn,{onClose:Xe,onConfirm:Le,onBack:()=>Me("main"),origin:e.url.origin,warningMessage:g?.warnings?.[0]?.message}):Re.default.createElement(en,{advancedDetails:Re.default.createElement(tn,{copyTransactions:ot,scanResult:g,networkID:l}),confirmApproval:lo?()=>Me("friction"):Le,denyApproval:Xe,hasSimulationFailed:!!u,numTransactions:w.length,domain:o,rows:mt.showAutoConfirmRow?[eo]:[],simulationResults:g?.expectedChanges??[],simulationWarnings:g?.warnings??[],icon:e.icon,networkID:vo.networkID,showFriction:lo,showConfirmAnyway:!lo&&Pe,isErrorNativeTokenBalance:!1,isErrorNetworkFee:G,isLoading:H,networkFeeRows:[...ni(r,P?`${Wl(ul(P?.estimatedFee))} SOL`:"",H,G,Pe?"accentAlert":"textSecondary",Wo,P?.highFees||Pe||Wo?"accentAlert":"textSecondary")]})},Js=O6;d();p();var O2=A(ea()),pr=A(C());d();p();var cl=A(C());async function W6(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture message signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userApprovedAction(W,{...o,origin:e.origin,method:"signIn"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (approve)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserApprove: ${typeof t}`),"provider")}}async function U6(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture message signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userDeniedAction(W,{...o,origin:e.origin,method:"signIn"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (deny)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserDeny: ${typeof t}`),"provider")}}function V6(e,t){return(o,r)=>{let{networkID:n,origin:i}=r,s=(0,cl.useRef)(!1);return(0,cl.useCallback)(async a=>(n&&j.isSolanaNetworkID(n)&&!s.current&&("error"in a?await t({networkID:n,origin:i}):await e({networkID:n,origin:i}),s.current=!0),o(a)),[o,n,i])}}var M2=V6(W6,U6);d();p();var Tt=A(C());d();p();var pt=A(C());var H6=f.div`
  background: #2a2a2a;
  border-radius: 16px;
`,Yee=f.div`
  border-radius: 16px 16px 0 0;
  padding: 12px 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  gap: 8px;
  align-items: center;
  background: #2a2a2a;
  color: #999999;
`,Jee=f.div`
  padding-top: 0px;
  padding-bottom: 12px;
  padding-left: 18px;
  padding-right: 18px;
  border-top: 1px solid #222222;

  > * {
    margin-top: 12px;
  }
`,Xee=f.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
`,Qee=f.a.attrs({target:"_blank",rel:"noopener noreferrer"})`
  text-decoration: none;
  cursor: pointer;
`,_6=({detailName:e,detailValue:t,errorObject:o})=>{let{t:r}=S(),n=o!==void 0;return pt.default.createElement(R6,{style:{flexDirection:"column"}},pt.default.createElement("div",{style:{display:"flex",justifyContent:"space-between",width:"100%"}},pt.default.createElement($6,{size:14,lineHeight:17,wordBreak:"break-word",textAlign:"left"},r(`siwsFieldLable_${e}`)),pt.default.createElement(z6,{size:14,lineHeight:17,wordBreak:"break-word",textAlign:"right",style:{width:"70%"}},e==="resources"?t.join(`
`):t)),n&&e in o&&pt.default.createElement(E,{color:n&&e in o?Gl:"default",size:12.5,style:{marginTop:8,textAlign:"left"}},r(o[e])))},ll=({signInData:e,errorObject:t})=>{let{t:o}=S(),[r,n]=(0,pt.useState)(!1),i=(0,pt.useCallback)(m=>{n(!r),setTimeout(()=>{!r&&m.target.scrollIntoView({behavior:"smooth",block:"start"})},1)},[r]),s=m=>m.map(([u,g])=>pt.default.createElement(_6,{detailName:u,detailValue:g,errorObject:t})),a=t!==void 0,c=Object.entries(e).filter(([m,u])=>m!=="statement"),l=r?c:c.filter(([m,u])=>t&&m in t);return pt.default.createElement(pt.default.Fragment,null,pt.default.createElement(G6,{isSticky:r,onClick:i,isCollapsible:!0},pt.default.createElement(q6,{isExpanded:r},pt.default.createElement(Fi,{fill:"#999999"})),pt.default.createElement(E,{color:"#999999",textAlign:"left",size:14,weight:600,lineHeight:17},o("notificationAdvancedDetailsText")),a&&pt.default.createElement(E,{color:Gl,textAlign:"left",size:14,weight:400,lineHeight:17},"(",o("siwsErrorNumIssues",{n:Object.keys(t).length}),")")),pt.default.createElement(H6,null,s(l)))},G6=f.div`
  border-radius: 16px 16px 0 0;
  display: flex;
  flex-direction: row;
  gap: 4px;
  transition:
    box-shadow 0.2s ease,
    color 0.2s ease;
  scroll-margin-top: 10px;

  ${({isCollapsible:e})=>e&&`
    cursor: pointer;
    :hover {
      color: #ffffff;
    }
  `}

  ${({isSticky:e})=>e&&`
    box-shadow: 0px 81px 33px rgba(0, 0, 0, 0.02), 0px 46px 27px rgba(0, 0, 0, 0.08), 0px 20px 20px rgba(0, 0, 0, 0.13), 0px 5px 11px rgba(0, 0, 0, 0.15), 0px 0px 0px rgba(0, 0, 0, 0.15);
    position: sticky;
    top: 0;
    z-index: 1000;
  `}
`,R6=f.div`
  padding: 14px 16px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: flex-start;
  border-top: 1px solid #222222;
`,z6=f(E)`
  flex-shrink: 1;
`,$6=f(E)`
  flex-shrink: 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`,q6=f.div`
  cursor: pointer;
  transition: background-color 0.2s ease;

  :hover {
    background-color: #333333;
  }

  svg {
    color: currentColor;
    position: relative;
    fill: currentColor;
    transition: transform 0.1s ease-in-out;
    transform: ${({isExpanded:e})=>e?"rotate(90deg)":"none"};

    * {
      color: currentColor;
    }
  }
`;var j6=f.div`
  margin-bottom: 24px;
`,F2=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,K6=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  margin: 16px 0;
  align-items: flex-end;
`,Y6=f(Rt)`
  display: flex;
  flex-direction: column;
  align-content: space-between;
`,N2=e=>{let{origin:t,signInData:o,icon:r,errorDetails:n,confirmApproval:i,denyApproval:s}=e,{t:a}=S(),c=n.reduce((m,u)=>(m[u.label]=u.message,m),{}),l=Fn(e.networkID);return Tt.default.createElement(Tt.default.Fragment,null,Tt.default.createElement(Y6,null,Tt.default.createElement("div",null,Tt.default.createElement(Ao,{type:"SIGN_IN",domain:ee(t),iconUrl:r}),Tt.default.createElement(j6,null,Tt.default.createElement(F2,{color:"#999999"},a("notificationApplicationApprovalParagraphText"))),Tt.default.createElement(Nt,{margin:"24px 0px 24px 0px ",justify:"center"},Tt.default.createElement(wt,{message:a("siwsVerificationErrorDescription"),variant:3})),Tt.default.createElement("div",{style:{display:"flex",gap:8,flexDirection:"column",marginBottom:8}},Tt.default.createElement(on,{header:a("notificationMessageHeader"),message:e.message,isJson:!1}),Tt.default.createElement(dr,{rows:[l]}),Tt.default.createElement(ll,{signInData:o,errorObject:c}))),Tt.default.createElement(K6,null,Tt.default.createElement(F2,{color:"#999999"},a("notificationApplicationApprovalSignInDisclaimer")))),Tt.default.createElement(Qe,null,Tt.default.createElement(gt,{primaryText:a("commandConfirm"),secondaryText:a("commandCancel"),onPrimaryClicked:i,onSecondaryClicked:s,primaryTheme:"warning"})))};d();p();var Qt=A(C());var J6=f.div`
  margin-bottom: 24px;
`,B2=f(E).attrs({size:14,weight:400,lineHeight:17,textAlign:"left"})``,X6=f.div`
  display: flex;
  flex: 1;
  justify-content: center;
  margin: 16px 0;
  align-items: flex-end;
`,Q6=f(Rt)`
  display: flex;
  flex-direction: column;
  align-content: space-between;
`,L2=e=>{let{origin:t,signInData:o,icon:r,confirmApproval:n,denyApproval:i}=e,{t:s}=S(),a=Fn(e.networkID);return Qt.default.createElement(Qt.default.Fragment,null,Qt.default.createElement(Q6,null,Qt.default.createElement("div",null,Qt.default.createElement(Ao,{type:"SIGN_IN",domain:ee(t),iconUrl:r}),Qt.default.createElement(J6,null,Qt.default.createElement(B2,{color:"#999999"},s("notificationApplicationApprovalParagraphText"))),Qt.default.createElement("div",{style:{display:"flex",gap:8,flexDirection:"column"}},Qt.default.createElement(on,{header:s("notificationMessageHeader"),message:e.message,isJson:!1}),Qt.default.createElement(dr,{rows:[a]}),Qt.default.createElement(ll,{signInData:o}))),Qt.default.createElement(X6,null,Qt.default.createElement(B2,{color:"#999999"},s("notificationApplicationApprovalSignInDisclaimer")))),Qt.default.createElement(Qe,null,Qt.default.createElement(gt,{primaryText:s("commandConfirm"),secondaryText:s("commandCancel"),onPrimaryClicked:n,onSecondaryClicked:i})))};var Z6=_t(),e9=()=>{let{data:e}=ue();return(0,pr.useMemo)(()=>{let t=e?.identifier??"",o=e?.addresses?.find(r=>j.isSolanaNetworkID(r.networkID));return{isLedger:e?.type==="ledger",networkID:o?.networkID,signMessage:async r=>{let n=Buffer.from(r).toString("utf-8"),i=await Z6.sign(t,{chainType:"solana",signingType:"message",message:n});if(i.status==="success")return i.signature;throw new Error(`[${i.type}] ${i.message}`)}}},[e])},t9=e=>{let{isLedger:t,networkID:o,signMessage:r}=e9(),{tabId:n,url:i,requestId:s,signInData:a,message:c,errorDetails:l,icon:m}=e,u=i.origin,g=O2.default.decode(c),x=Fe(),y=M2(x,{networkID:o,origin:u}),{pushDetailView:b}=U(),h=(0,pr.useCallback)(()=>{let v=ie.userRejectedRequest(s);y(v)},[s,y]),w=(0,pr.useCallback)(async()=>{try{let v=await r(g),I=ie.result(s,{type:"send",signature:v});y(I)}catch(v){let I=ie.internalError(s,v.message);y(I)}},[r,g,s,y]),T=(0,pr.useCallback)(()=>{t?b(pr.default.createElement(no,null,pr.default.createElement(xo,{ledgerAction:w,cancel:h}))):y(ie.result(s,{type:"signAndSend"}))},[h,y,t,b,s,w]);return{tabId:n,icon:m,origin:u,signInData:a,message:g.toString("utf8"),errorDetails:l,networkID:o??"solana:101",confirmApproval:T,denyApproval:h}},W2=e=>{let t=t9(e);return"errorDetails"in t&&t.errorDetails?pr.default.createElement(N2,{...t}):pr.default.createElement(L2,{...t})};d();p();var H2=A(ea()),_r=A(C());d();p();var dl=A(C());async function o9(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture message signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userApprovedAction(W,{...o,origin:e.origin,method:"signMessage"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (approve)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserApprove: ${typeof t}`),"provider")}}async function r9(e){try{let t=Ho(e.networkID.replace("solana:",""))??"localnet",o=ut(t);if(o?.chainType!=="solana")throw new Error(`Failed to capture message signature event analytics: Unsupported Solana Network ID "${t}"`);return await ft.userDeniedAction(W,{...o,origin:e.origin,method:"signMessage"})}catch(t){$.addBreadcrumb("analytics","Could not log dapp user action (deny)","error"),t instanceof Error?$.captureError(t,"provider"):$.captureError(new Error(`Non-Error instance thrown in captureUserDeny: ${typeof t}`),"provider")}}function n9(e,t){return(o,r)=>{let{networkID:n,origin:i}=r,s=(0,dl.useRef)(!1);return(0,dl.useCallback)(async a=>(j.isSolanaNetworkID(n)&&!s.current&&("error"in a?await t({networkID:n,origin:i}):await e({networkID:n,origin:i}),s.current=!0),o(a)),[o,n,i])}}var U2=n9(o9,r9);d();p();var di=A(C());var V2=di.memo(({icon:e,origin:t,message:o,display:r,confirmApproval:n,denyApproval:i,networkID:s,isSimulationLoading:a,hasSimulationFailed:c,scanResult:l})=>{let m=di.useMemo(()=>{switch(r){case"hex":return`0x${Buffer.from(o).toString("hex")}`;case"utf8":default:return new TextDecoder().decode(o)}},[r,o]);return a?di.createElement(Ur,{type:"SIGN_MESSAGE",icon:e,domain:t,onClose:i}):di.createElement(ss,{icon:e,origin:t,message:m,networkID:s,confirmApproval:n,denyApproval:i,scanFailed:c,scanIsLoading:a,scanWarnings:l?.warnings||[],advancedDetails:l?.advancedDetails})});var i9=_t(),s9=()=>{let{data:e}=ue();return(0,_r.useMemo)(()=>{let t=e?.identifier??"",{address:o,networkID:r}=e?.addresses?.find(n=>j.isSolanaNetworkID(n.networkID))??{address:"",networkID:Ne.Solana.Mainnet};return{isLedger:e?.type==="ledger",publicKey:o,networkID:r,signMessage:async n=>{let i=Buffer.from(n).toString("utf-8"),s=await i9.sign(t,{chainType:"solana",signingType:"message",message:i});if(s.status==="success")return s.signature;throw new Error(`[${s.type}] ${s.message}`)}}},[e])},a9=e=>{let{url:t,requestId:o,message:r,display:n,tabId:i,networkID:s,icon:a}=e,c=t.origin,l=H2.default.decode(r),m=Fe(),u=U2(m,{networkID:s,origin:c}),{pushDetailView:g}=U(),x=(0,_r.useCallback)(()=>{let v=ie.userRejectedRequest(o);u(v)},[o,u]),y=(0,_r.useCallback)(async()=>{try{let v=await e.signMessage(l),I=ie.result(o,{type:"send",signature:v});u(I)}catch(v){let I=ie.internalError(o,v.message);u(I)}},[e,l,o,u]),b=(0,_r.useCallback)(()=>{e.isLedger?g(_r.default.createElement(no,null,_r.default.createElement(xo,{ledgerAction:y,cancel:x}))):u(ie.result(o,{type:"signAndSend"}))},[x,u,e.isLedger,g,o,y]),{isError:h,data:w,isLoading:T}=Ti({networkID:s??"solana:101",type:"message",url:t.href,params:{message:Buffer.from(l).toString("utf8")}});return{networkID:s,tabId:i,icon:a,origin:c,requestId:o,message:l,display:n,isSimulationLoading:T,hasSimulationFailed:h,scanResult:w,confirmApproval:b,denyApproval:x}},_2=e=>{let t=s9(),o=a9({...e,...t});return _r.default.createElement(V2,{...o})};d();p();var pi=A(C());var G2=f(E)`
  text-align: left;
  font-size: 16px;
  line-height: 20px;
  strong {
    font-weight: 600;
  }
`,a0=({url:e,targetAddressType:t,addressType:o,requestId:r})=>{let{t:n}=S(),i=e.origin,s=Fe(),a=pi.useCallback(()=>{s({jsonrpc:"2.0",id:r,error:ie.userRejectedRequest(r).error})},[r,s]);return pi.createElement(Nn,{origin:i,title:n("notificationUnsupportedAccount"),onClose:()=>{a()}},pi.createElement(G2,null,pi.createElement(Ro,{t:n,i18nKey:"notificationUnsupportedAccountDescription",values:{origin:i,targetChainType:Un.getDisplayName(t),chainType:Un.getDisplayName(o)}})),pi.createElement(G2,{margin:"16px 0 0 0"},n("notificationUnsupportedAccountDescription2")))};d();p();var Bn=A(C());var R2=f(E)`
  text-align: left;
  font-size: 16px;
  line-height: 20px;
  strong {
    font-weight: 600;
  }
`,z2=({url:e,requestId:t})=>{let{t:o}=S(),r=e.origin,n=Fe(),i=Bn.useCallback(()=>{n({jsonrpc:"2.0",id:t,error:ie.userRejectedRequest(t).error})},[t,n]);return Bn.createElement(Nn,{origin:r,title:o("unsupported_network",{defaultValue:"Unsupported network"}),onClose:()=>{i()}},Bn.createElement(R2,null,o("notificationUnsupportedNetworkDescription")),Bn.createElement(R2,{margin:"16px 0 0 0"},Bn.createElement(Ro,{t:o,i18nKey:"notificationUnsupportedNetworkDescriptionInterpolated"},"To proceed with a different extension, turn off",Bn.createElement("strong",null,"Settings \u2192 Default App Wallet, and select Always Ask"),". Then refresh the page and reconnect.")))};d();p();var $2=new op(W);nm();var c9=q.default.lazy(()=>import("./EthSelectWallet-JNTUSPWG.js"));v0.init({provider:s1});pm();var j2=({req:e,url:t,tabId:o,icon:r})=>{let{method:n}=e,{data:i=[]}=te(),{data:[s]}=He(["frontend-enable-session-start"]);return ou(()=>{$2.onAppSessionStart(i)},i.length>0&&s),(0,q.useEffect)(()=>{W.capture("notificationOpen",{data:{method:n}})},[n]),q.default.createElement(q.default.Fragment,null,q.default.createElement(n0,null,q.default.createElement(Rl,null,q.default.createElement(l9,{req:e,url:t,tabId:o,icon:r}))))},l9=q.default.memo(({req:e,url:t,tabId:o,icon:r})=>{let{id:n}=e,i=new URL(t),s=y2(o);r||(r=s.iconUrl);let a=Oe.user_confirmIncorrectMode.request.safeParse(e);if(a.success){let{params:F}=a.data;return q.default.createElement(I2,{hostname:ee(i.origin),enabledChains:F[1]})}let c=Oe.user_confirmEIP712IncorrectChainId.request.safeParse(e);if(c.success){let{params:F}=c.data,{connectedChainId:P,messageChainId:H}=F[1];return q.default.createElement(C2,{url:i,connectedChainId:P,messageChainId:H})}let l=Oe.user_confirmUnsupportedNetwork.request.safeParse(e);if(l.success){let F=l.data.params[1];return q.default.createElement(z2,{url:i,targetNetworkId:F,requestId:n})}let m=Oe.user_confirmUnsupportedAccount.request.safeParse(e);if(m.success&&m.data.params[1]==="ethereum")return q.default.createElement(a0,{targetAddressType:"eip155",addressType:"solana",url:i,requestId:n});if(m.success&&m.data.params[1]==="solana")return q.default.createElement(a0,{targetAddressType:"solana",addressType:"eip155",url:i,requestId:n});if(Oe.user_approveSolConnect.request.safeParse(e).success)return q.default.createElement(Zc,{icon:r,url:i,requestId:n});let g=Oe.user_approveSolSignTransaction.request.safeParse(e);if(g.success){let{params:F}=g.data,{transaction:P,autoConfirmStatusCode:H}=F[1];return q.default.createElement(Js,{tabId:o,icon:r,url:i,requestId:n,transactions:[P],autoConfirmStatusCode:H,originalMethod:"signTransaction"})}let x=Oe.user_approveSolSignAndSendTransaction.request.safeParse(e);if(x.success){let{params:F}=x.data,{transaction:P,autoConfirmStatusCode:H}=F[1];return q.default.createElement(Js,{tabId:o,icon:r,url:i,requestId:n,transactions:[P],autoConfirmStatusCode:H,originalMethod:"signAndSendTransaction"})}let y=Oe.user_approveSolSignAllTransactions.request.safeParse(e);if(y.success){let{params:F}=y.data,{transactions:P,autoConfirmStatusCode:H}=F[1];return q.default.createElement(Js,{tabId:o,icon:r,url:i,requestId:n,transactions:P,autoConfirmStatusCode:H,originalMethod:"signAllTransactions"})}let b=Oe.user_approveSolSignAndSendAllTransactions.request.safeParse(e);if(b.success){let{params:F}=b.data,{transactions:P,autoConfirmStatusCode:H}=F[1];return q.default.createElement(Js,{tabId:o,url:i,icon:r,requestId:n,transactions:P,autoConfirmStatusCode:H,originalMethod:"signAndSendAllTransactions"})}let h=Oe.user_approveSolSignMessage.request.safeParse(e);if(h.success){let{params:F}=h.data,{message:P,display:H}=F[1];return q.default.createElement(_2,{tabId:o,icon:r,url:i,requestId:n,message:P,display:H})}let w=Oe.user_approveSolSignIn.request.safeParse(e);if(w.success){let{params:F}=w.data,{signInData:P,message:H,errorDetails:G}=F[1];return q.default.createElement(W2,{tabId:o,icon:r,url:i,requestId:n,signInData:P,message:H,errorDetails:G})}let T=Oe.user_approveEthRequestAccounts.request.safeParse(e),v=Oe.user_approveWalletRequestPermissions.request.safeParse(e);if(T.success||v.success)return q.default.createElement(Zc,{icon:r,url:i,requestId:n});let I=Oe.user_approveEthSendTransaction.request.safeParse(e);if(I.success){let{params:F}=I.data,{transaction:P,autoConfirmStatusCode:H}=F[1];return q.default.createElement($g,{tabId:o,icon:r,url:i,requestId:n,transaction:P,autoConfirmStatusCode:H})}let N=Oe.user_approveEthSignMessage.request.safeParse(e);if(N.success){let{params:F}=N.data,{message:P,originalMethod:H,chainId:G,autoConfirmStatusCode:X}=F[1];return q.default.createElement(t2,{tabId:o,icon:r,url:i,requestId:n,message:P,originalMethod:H,hexChainId:G,autoConfirmStatusCode:X})}if(Oe.user_approveBtcRequestAccounts.request.safeParse(e).success)return q.default.createElement(Zc,{icon:r,url:i,requestId:n});let V=Oe.user_approveBtcSignPSBT.request.safeParse(e);if(V.success)return q.default.createElement(b2,{icon:r,url:i,requestId:n,message:Buffer.from(V.data.params[1].psbt).toString("hex"),finalize:V.data.params[1].finalize,inputsToSign:V.data.params[1].inputsToSign});let L=Oe.user_approveBtcSignMessage.request.safeParse(e);return L.success?q.default.createElement(A2,{icon:r,url:i,requestId:n,messageBytes:L.data.params[1].message}):null}),K2=e=>{let t=(0,q.useCallback)(()=>{Ar({url:"onboarding.html"}),self.close()},[]),o=n1(),{data:r}=ue(),n=r?.isReadOnly;if(!o)return null;let{url:i,req:s,tabId:a,icon:c}=o,{id:l,method:m}=s,u=Oe.user_selectEthWallet.method.safeParse(m).success,g=Oe.user_approveEthRequestAccounts.method.safeParse(m).success||Oe.user_approveSolConnect.method.safeParse(m).success||Oe.user_approveBtcRequestAccounts.method.safeParse(m).success;return q.default.createElement(q.default.Fragment,null,u?q.default.createElement(q.Suspense,{fallback:q.default.createElement(Ym,{color:"#AB9FF2"})},q.default.createElement(c9,{requestId:l})):q.default.createElement(kg,{openOnboarding:t},q.default.createElement(Hl,{title:g?"Connection Error":"Transaction Error",message:`There was an error attempting to ${g?"connect to the application":"sign the transaction"}. Please try again.`},n&&!g?q.default.createElement(D2,{icon:c,url:new URL(i),requestId:l}):q.default.createElement(Bi,null,e.render({req:s,url:i,tabId:a,icon:c})),q.default.createElement(h2,null))))},d9=()=>q.default.createElement(K2,{render:({req:e,url:t,tabId:o,icon:r})=>{let n;return e.method==="user_approveSolConnect"&&(n="solana"),e.method==="user_approveEthRequestAccounts"&&(n="eip155"),e.method==="user_approveBtcRequestAccounts"&&(n="bip122_p2tr"),q.default.createElement(n0,null,q.default.createElement(Rl,null,q.default.createElement(u2,{accountDisabler:s=>n?s.addresses?.every(a=>a.addressType!==n):!1,disableAccountManagement:!0},q.default.createElement(g2,null,q.default.createElement(j2,{req:e,url:t,tabId:o,icon:r})))))}}),Yre=()=>q.default.createElement(Vl,{theme:{footer:{backgroundColor:"none",borderTop:"1px solid #323232",boxShadow:"0px -4px 6px 0px rgba(0, 0, 0, 0.20)"},detailViewMaxHeight:"80vh",detailViewMinHeight:"320px"}},q.default.createElement(K2,{render:({req:e,url:t,tabId:o,icon:r})=>q.default.createElement(j2,{req:e,url:t,tabId:o,icon:r})})),p9=()=>q.default.createElement(q.default.Fragment,null,q.default.createElement(Sm,{future:{v7_startTransition:!0}},q.default.createElement(Vl,{theme:iu},q.default.createElement(a1,{backgroundColor:"#222222"}),q.default.createElement(Hl,null,q.default.createElement(c1,null,q.default.createElement(i1,null,q.default.createElement(Pm,null,q.default.createElement(dm,null,q.default.createElement(D0,null,q.default.createElement(Y0,{analytics:W},q.default.createElement(q0,{authRepository:er},q.default.createElement(J0,{userRepository:Ka,claimUsernameSigner:Xu},q.default.createElement(S2,null,q.default.createElement(d9,null))))))),q.default.createElement("div",{id:ym})))))))),m9=document.getElementById("root"),u9=(0,q2.createRoot)(m9);u9.render(q.default.createElement(p9,null));export{c1 as a,h2 as b,$2 as c,wc as d,u2 as e,Ks as f,n0 as g,hg as h,kg as i,S2 as j,d9 as k,Yre as l};
//# sourceMappingURL=chunk-GLVW5MF5.js.map
