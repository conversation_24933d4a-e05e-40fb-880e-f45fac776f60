import{o as t,rb as i}from"./chunk-WIQ4WVKX.js";import{Pb as p}from"./chunk-MZZEJ42N.js";import{a as u}from"./chunk-7X4NV6OJ.js";import{f as d,h as n,n as a}from"./chunk-3KENBVE7.js";n();a();var e=d(u());var A=({name:c,publicKey:m,publicKeyLabel:r,hideParens:b})=>{let s=p(m,4),o=b?s:`(${s})`;return e.default.createElement(e.default.Fragment,null,e.default.createElement(y,null,c),r?e.default.createElement(g,null,e.default.createElement(l,null,o),e.default.createElement(P,null,r)):e.default.createElement(l,null,o))},y=t(i).attrs({size:16,weight:500,color:"white",maxWidth:"135px",noWrap:!0})``,l=t(i).attrs({size:16,weight:400,opacity:.5})``,P=t.span`
  opacity: 0.3;
  font-size: 15px;
  margin-right: 5px;
  display: inline-flex;
`,g=t.div`
  text-align: right;
`;export{A as a};
//# sourceMappingURL=chunk-MHQYYZ7C.js.map
