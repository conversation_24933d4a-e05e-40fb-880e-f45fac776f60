import{a as J}from"./chunk-7X4NV6OJ.js";import{a as at,b as Ce,c as Ae,f as K,h as b,n as v}from"./chunk-3KENBVE7.js";var Lt=Ae((oi,Pt)=>{b();v();Pt.exports={area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0}});function W(r){"@babel/helpers - typeof";return W=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},W(r)}var Ge=Ce(()=>{b();v()});function Qe(r,e){if(W(r)!=="object"||r===null)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var n=t.call(r,e||"default");if(W(n)!=="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}var It=Ce(()=>{b();v();Ge()});function Ye(r){var e=Qe(r,"string");return W(e)==="symbol"?e:String(e)}var $t=Ce(()=>{b();v();Ge();It()});var zt=Ae((G,qt)=>{b();v();var Ee=typeof globalThis<"u"&&globalThis||typeof self<"u"&&self||typeof self<"u"&&self,ke=function(){function r(){this.fetch=!1,this.DOMException=Ee.DOMException}return r.prototype=Ee,new r}();(function(r){var e=function(t){var n=typeof r<"u"&&r||typeof self<"u"&&self||typeof n<"u"&&n,i={searchParams:"URLSearchParams"in n,iterable:"Symbol"in n&&"iterator"in Symbol,blob:"FileReader"in n&&"Blob"in n&&function(){try{return new Blob,!0}catch{return!1}}(),formData:"FormData"in n,arrayBuffer:"ArrayBuffer"in n};function s(f){return f&&DataView.prototype.isPrototypeOf(f)}if(i.arrayBuffer)var o=["[object Int8Array]","[object Uint8Array]","[object Uint8ClampedArray]","[object Int16Array]","[object Uint16Array]","[object Int32Array]","[object Uint32Array]","[object Float32Array]","[object Float64Array]"],a=ArrayBuffer.isView||function(f){return f&&o.indexOf(Object.prototype.toString.call(f))>-1};function u(f){if(typeof f!="string"&&(f=String(f)),/[^a-z0-9\-#$%&'*+.^_`|~!]/i.test(f)||f==="")throw new TypeError('Invalid character in header field name: "'+f+'"');return f.toLowerCase()}function l(f){return typeof f!="string"&&(f=String(f)),f}function p(f){var h={next:function(){var L=f.shift();return{done:L===void 0,value:L}}};return i.iterable&&(h[Symbol.iterator]=function(){return h}),h}function d(f){this.map={},f instanceof d?f.forEach(function(h,L){this.append(L,h)},this):Array.isArray(f)?f.forEach(function(h){this.append(h[0],h[1])},this):f&&Object.getOwnPropertyNames(f).forEach(function(h){this.append(h,f[h])},this)}d.prototype.append=function(f,h){f=u(f),h=l(h);var L=this.map[f];this.map[f]=L?L+", "+h:h},d.prototype.delete=function(f){delete this.map[u(f)]},d.prototype.get=function(f){return f=u(f),this.has(f)?this.map[f]:null},d.prototype.has=function(f){return this.map.hasOwnProperty(u(f))},d.prototype.set=function(f,h){this.map[u(f)]=l(h)},d.prototype.forEach=function(f,h){for(var L in this.map)this.map.hasOwnProperty(L)&&f.call(h,this.map[L],L,this)},d.prototype.keys=function(){var f=[];return this.forEach(function(h,L){f.push(L)}),p(f)},d.prototype.values=function(){var f=[];return this.forEach(function(h){f.push(h)}),p(f)},d.prototype.entries=function(){var f=[];return this.forEach(function(h,L){f.push([L,h])}),p(f)},i.iterable&&(d.prototype[Symbol.iterator]=d.prototype.entries);function c(f){if(f.bodyUsed)return Promise.reject(new TypeError("Already read"));f.bodyUsed=!0}function m(f){return new Promise(function(h,L){f.onload=function(){h(f.result)},f.onerror=function(){L(f.error)}})}function g(f){var h=new FileReader,L=m(h);return h.readAsArrayBuffer(f),L}function P(f){var h=new FileReader,L=m(h);return h.readAsText(f),L}function x(f){for(var h=new Uint8Array(f),L=new Array(h.length),R=0;R<h.length;R++)L[R]=String.fromCharCode(h[R]);return L.join("")}function k(f){if(f.slice)return f.slice(0);var h=new Uint8Array(f.byteLength);return h.set(new Uint8Array(f)),h.buffer}function S(){return this.bodyUsed=!1,this._initBody=function(f){this.bodyUsed=this.bodyUsed,this._bodyInit=f,f?typeof f=="string"?this._bodyText=f:i.blob&&Blob.prototype.isPrototypeOf(f)?this._bodyBlob=f:i.formData&&FormData.prototype.isPrototypeOf(f)?this._bodyFormData=f:i.searchParams&&URLSearchParams.prototype.isPrototypeOf(f)?this._bodyText=f.toString():i.arrayBuffer&&i.blob&&s(f)?(this._bodyArrayBuffer=k(f.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer])):i.arrayBuffer&&(ArrayBuffer.prototype.isPrototypeOf(f)||a(f))?this._bodyArrayBuffer=k(f):this._bodyText=f=Object.prototype.toString.call(f):this._bodyText="",this.headers.get("content-type")||(typeof f=="string"?this.headers.set("content-type","text/plain;charset=UTF-8"):this._bodyBlob&&this._bodyBlob.type?this.headers.set("content-type",this._bodyBlob.type):i.searchParams&&URLSearchParams.prototype.isPrototypeOf(f)&&this.headers.set("content-type","application/x-www-form-urlencoded;charset=UTF-8"))},i.blob&&(this.blob=function(){var f=c(this);if(f)return f;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error("could not read FormData body as blob");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){if(this._bodyArrayBuffer){var f=c(this);return f||(ArrayBuffer.isView(this._bodyArrayBuffer)?Promise.resolve(this._bodyArrayBuffer.buffer.slice(this._bodyArrayBuffer.byteOffset,this._bodyArrayBuffer.byteOffset+this._bodyArrayBuffer.byteLength)):Promise.resolve(this._bodyArrayBuffer))}else return this.blob().then(g)}),this.text=function(){var f=c(this);if(f)return f;if(this._bodyBlob)return P(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(x(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error("could not read FormData body as text");return Promise.resolve(this._bodyText)},i.formData&&(this.formData=function(){return this.text().then(y)}),this.json=function(){return this.text().then(JSON.parse)},this}var A=["DELETE","GET","HEAD","OPTIONS","POST","PUT"];function O(f){var h=f.toUpperCase();return A.indexOf(h)>-1?h:f}function w(f,h){if(!(this instanceof w))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');h=h||{};var L=h.body;if(f instanceof w){if(f.bodyUsed)throw new TypeError("Already read");this.url=f.url,this.credentials=f.credentials,h.headers||(this.headers=new d(f.headers)),this.method=f.method,this.mode=f.mode,this.signal=f.signal,!L&&f._bodyInit!=null&&(L=f._bodyInit,f.bodyUsed=!0)}else this.url=String(f);if(this.credentials=h.credentials||this.credentials||"same-origin",(h.headers||!this.headers)&&(this.headers=new d(h.headers)),this.method=O(h.method||this.method||"GET"),this.mode=h.mode||this.mode||null,this.signal=h.signal||this.signal,this.referrer=null,(this.method==="GET"||this.method==="HEAD")&&L)throw new TypeError("Body not allowed for GET or HEAD requests");if(this._initBody(L),(this.method==="GET"||this.method==="HEAD")&&(h.cache==="no-store"||h.cache==="no-cache")){var R=/([?&])_=[^&]*/;if(R.test(this.url))this.url=this.url.replace(R,"$1_="+new Date().getTime());else{var E=/\?/;this.url+=(E.test(this.url)?"&":"?")+"_="+new Date().getTime()}}}w.prototype.clone=function(){return new w(this,{body:this._bodyInit})};function y(f){var h=new FormData;return f.trim().split("&").forEach(function(L){if(L){var R=L.split("="),E=R.shift().replace(/\+/g," "),T=R.join("=").replace(/\+/g," ");h.append(decodeURIComponent(E),decodeURIComponent(T))}}),h}function N(f){var h=new d,L=f.replace(/\r?\n[\t ]+/g," ");return L.split("\r").map(function(R){return R.indexOf(`
`)===0?R.substr(1,R.length):R}).forEach(function(R){var E=R.split(":"),T=E.shift().trim();if(T){var Q=E.join(":").trim();h.append(T,Q)}}),h}S.call(w.prototype);function I(f,h){if(!(this instanceof I))throw new TypeError('Please use the "new" operator, this DOM object constructor cannot be called as a function.');h||(h={}),this.type="default",this.status=h.status===void 0?200:h.status,this.ok=this.status>=200&&this.status<300,this.statusText=h.statusText===void 0?"":""+h.statusText,this.headers=new d(h.headers),this.url=h.url||"",this._initBody(f)}S.call(I.prototype),I.prototype.clone=function(){return new I(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new d(this.headers),url:this.url})},I.error=function(){var f=new I(null,{status:0,statusText:""});return f.type="error",f};var U=[301,302,303,307,308];I.redirect=function(f,h){if(U.indexOf(h)===-1)throw new RangeError("Invalid status code");return new I(null,{status:h,headers:{location:f}})},t.DOMException=n.DOMException;try{new t.DOMException}catch{t.DOMException=function(h,L){this.message=h,this.name=L;var R=Error(h);this.stack=R.stack},t.DOMException.prototype=Object.create(Error.prototype),t.DOMException.prototype.constructor=t.DOMException}function j(f,h){return new Promise(function(L,R){var E=new w(f,h);if(E.signal&&E.signal.aborted)return R(new t.DOMException("Aborted","AbortError"));var T=new XMLHttpRequest;function Q(){T.abort()}T.onload=function(){var $={status:T.status,statusText:T.statusText,headers:N(T.getAllResponseHeaders()||"")};$.url="responseURL"in T?T.responseURL:$.headers.get("X-Request-URL");var V="response"in T?T.response:T.responseText;setTimeout(function(){L(new I(V,$))},0)},T.onerror=function(){setTimeout(function(){R(new TypeError("Network request failed"))},0)},T.ontimeout=function(){setTimeout(function(){R(new TypeError("Network request failed"))},0)},T.onabort=function(){setTimeout(function(){R(new t.DOMException("Aborted","AbortError"))},0)};function B($){try{return $===""&&n.location.href?n.location.href:$}catch{return $}}T.open(E.method,B(E.url),!0),E.credentials==="include"?T.withCredentials=!0:E.credentials==="omit"&&(T.withCredentials=!1),"responseType"in T&&(i.blob?T.responseType="blob":i.arrayBuffer&&E.headers.get("Content-Type")&&E.headers.get("Content-Type").indexOf("application/octet-stream")!==-1&&(T.responseType="arraybuffer")),h&&typeof h.headers=="object"&&!(h.headers instanceof d)?Object.getOwnPropertyNames(h.headers).forEach(function($){T.setRequestHeader($,l(h.headers[$]))}):E.headers.forEach(function($,V){T.setRequestHeader(V,$)}),E.signal&&(E.signal.addEventListener("abort",Q),T.onreadystatechange=function(){T.readyState===4&&E.signal.removeEventListener("abort",Q)}),T.send(typeof E._bodyInit>"u"?null:E._bodyInit)})}return j.polyfill=!0,n.fetch||(n.fetch=j,n.Headers=d,n.Request=w,n.Response=I),t.Headers=d,t.Request=w,t.Response=I,t.fetch=j,t}({})})(ke);ke.fetch.ponyfill=!0;delete ke.fetch.polyfill;var oe=Ee.fetch?Ee:ke;G=oe.fetch;G.default=oe.fetch;G.fetch=oe.fetch;G.Headers=oe.Headers;G.Request=oe.Request;G.Response=oe.Response;qt.exports=G});var Xt=Ae((nt,Jt)=>{b();v();var Ne;typeof fetch=="function"&&(typeof self<"u"&&self.fetch||typeof self<"u"&&self.fetch?Ne=self.fetch:Ne=fetch);typeof at<"u"&&typeof self>"u"&&(de=Ne||zt(),de.default&&(de=de.default),nt.default=de,Jt.exports=nt.default);var de});b();v();var on={type:"logger",log(r){this.output("log",r)},warn(r){this.output("warn",r)},error(r){this.output("error",r)},output(r,e){console&&console[r]&&console[r].apply(console,e)}},Ie=class r{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.init(e,t)}init(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.prefix=t.prefix||"i18next:",this.logger=e||on,this.options=t,this.debug=t.debug}log(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"log","",!0)}warn(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","",!0)}error(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"error","")}deprecate(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return this.forward(t,"warn","WARNING DEPRECATED: ",!0)}forward(e,t,n,i){return i&&!this.debug?null:(typeof e[0]=="string"&&(e[0]=`${n}${this.prefix} ${e[0]}`),this.logger[t](e))}create(e){return new r(this.logger,{prefix:`${this.prefix}:${e}:`,...this.options})}clone(e){return e=e||this.options,e.prefix=e.prefix||this.prefix,new r(this.logger,e)}},_=new Ie,ne=class{constructor(){this.observers={}}on(e,t){return e.split(" ").forEach(n=>{this.observers[n]||(this.observers[n]=new Map);let i=this.observers[n].get(t)||0;this.observers[n].set(t,i+1)}),this}off(e,t){if(this.observers[e]){if(!t){delete this.observers[e];return}this.observers[e].delete(t)}}emit(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),i=1;i<t;i++)n[i-1]=arguments[i];this.observers[e]&&Array.from(this.observers[e].entries()).forEach(o=>{let[a,u]=o;for(let l=0;l<u;l++)a(...n)}),this.observers["*"]&&Array.from(this.observers["*"].entries()).forEach(o=>{let[a,u]=o;for(let l=0;l<u;l++)a.apply(a,[e,...n])})}};function ue(){let r,e,t=new Promise((n,i)=>{r=n,e=i});return t.resolve=r,t.reject=e,t}function ut(r){return r==null?"":""+r}function an(r,e,t){r.forEach(n=>{e[n]&&(t[n]=e[n])})}var un=/###/g;function le(r,e,t){function n(a){return a&&a.indexOf("###")>-1?a.replace(un,"."):a}function i(){return!r||typeof r=="string"}let s=typeof e!="string"?e:e.split("."),o=0;for(;o<s.length-1;){if(i())return{};let a=n(s[o]);!r[a]&&t&&(r[a]=new t),Object.prototype.hasOwnProperty.call(r,a)?r=r[a]:r={},++o}return i()?{}:{obj:r,k:n(s[o])}}function lt(r,e,t){let{obj:n,k:i}=le(r,e,Object);if(n!==void 0||e.length===1){n[i]=t;return}let s=e[e.length-1],o=e.slice(0,e.length-1),a=le(r,o,Object);for(;a.obj===void 0&&o.length;)s=`${o[o.length-1]}.${s}`,o=o.slice(0,o.length-1),a=le(r,o,Object),a&&a.obj&&typeof a.obj[`${a.k}.${s}`]<"u"&&(a.obj=void 0);a.obj[`${a.k}.${s}`]=t}function ln(r,e,t,n){let{obj:i,k:s}=le(r,e,Object);i[s]=i[s]||[],n&&(i[s]=i[s].concat(t)),n||i[s].push(t)}function me(r,e){let{obj:t,k:n}=le(r,e);if(t)return t[n]}function fn(r,e,t){let n=me(r,t);return n!==void 0?n:me(e,t)}function gt(r,e,t){for(let n in e)n!=="__proto__"&&n!=="constructor"&&(n in r?typeof r[n]=="string"||r[n]instanceof String||typeof e[n]=="string"||e[n]instanceof String?t&&(r[n]=e[n]):gt(r[n],e[n],t):r[n]=e[n]);return r}function ee(r){return r.replace(/[\-\[\]\/\{\}\(\)\*\+\?\.\\\^\$\|]/g,"\\$&")}var cn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;","/":"&#x2F;"};function pn(r){return typeof r=="string"?r.replace(/[&<>"'\/]/g,e=>cn[e]):r}var $e=class{constructor(e){this.capacity=e,this.regExpMap=new Map,this.regExpQueue=[]}getRegExp(e){let t=this.regExpMap.get(e);if(t!==void 0)return t;let n=new RegExp(e);return this.regExpQueue.length===this.capacity&&this.regExpMap.delete(this.regExpQueue.shift()),this.regExpMap.set(e,n),this.regExpQueue.push(e),n}},dn=[" ",",","?","!",";"],hn=new $e(20);function gn(r,e,t){e=e||"",t=t||"";let n=dn.filter(o=>e.indexOf(o)<0&&t.indexOf(o)<0);if(n.length===0)return!0;let i=hn.getRegExp(`(${n.map(o=>o==="?"?"\\?":o).join("|")})`),s=!i.test(r);if(!s){let o=r.indexOf(t);o>0&&!i.test(r.substring(0,o))&&(s=!0)}return s}function De(r,e){let t=arguments.length>2&&arguments[2]!==void 0?arguments[2]:".";if(!r)return;if(r[e])return r[e];let n=e.split(t),i=r;for(let s=0;s<n.length;){if(!i||typeof i!="object")return;let o,a="";for(let u=s;u<n.length;++u)if(u!==s&&(a+=t),a+=n[u],o=i[a],o!==void 0){if(["string","number","boolean"].indexOf(typeof o)>-1&&u<n.length-1)continue;s+=u-s+1;break}i=o}return i}function ye(r){return r&&r.indexOf("_")>0?r.replace("_","-"):r}var be=class extends ne{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{ns:["translation"],defaultNS:"translation"};super(),this.data=e||{},this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.options.ignoreJSONStructure===void 0&&(this.options.ignoreJSONStructure=!0)}addNamespaces(e){this.options.ns.indexOf(e)<0&&this.options.ns.push(e)}removeNamespaces(e){let t=this.options.ns.indexOf(e);t>-1&&this.options.ns.splice(t,1)}getResource(e,t,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},s=i.keySeparator!==void 0?i.keySeparator:this.options.keySeparator,o=i.ignoreJSONStructure!==void 0?i.ignoreJSONStructure:this.options.ignoreJSONStructure,a;e.indexOf(".")>-1?a=e.split("."):(a=[e,t],n&&(Array.isArray(n)?a.push(...n):typeof n=="string"&&s?a.push(...n.split(s)):a.push(n)));let u=me(this.data,a);return!u&&!t&&!n&&e.indexOf(".")>-1&&(e=a[0],t=a[1],n=a.slice(2).join(".")),u||!o||typeof n!="string"?u:De(this.data&&this.data[e]&&this.data[e][t],n,s)}addResource(e,t,n,i){let s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{silent:!1},o=s.keySeparator!==void 0?s.keySeparator:this.options.keySeparator,a=[e,t];n&&(a=a.concat(o?n.split(o):n)),e.indexOf(".")>-1&&(a=e.split("."),i=t,t=a[1]),this.addNamespaces(t),lt(this.data,a,i),s.silent||this.emit("added",e,t,n,i)}addResources(e,t,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{silent:!1};for(let s in n)(typeof n[s]=="string"||Object.prototype.toString.apply(n[s])==="[object Array]")&&this.addResource(e,t,s,n[s],{silent:!0});i.silent||this.emit("added",e,t,n)}addResourceBundle(e,t,n,i,s){let o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{silent:!1,skipCopy:!1},a=[e,t];e.indexOf(".")>-1&&(a=e.split("."),i=n,n=t,t=a[1]),this.addNamespaces(t);let u=me(this.data,a)||{};o.skipCopy||(n=JSON.parse(JSON.stringify(n))),i?gt(u,n,s):u={...u,...n},lt(this.data,a,u),o.silent||this.emit("added",e,t,n)}removeResourceBundle(e,t){this.hasResourceBundle(e,t)&&delete this.data[e][t],this.removeNamespaces(t),this.emit("removed",e,t)}hasResourceBundle(e,t){return this.getResource(e,t)!==void 0}getResourceBundle(e,t){return t||(t=this.options.defaultNS),this.options.compatibilityAPI==="v1"?{...this.getResource(e,t)}:this.getResource(e,t)}getDataByLanguage(e){return this.data[e]}hasLanguageSomeTranslations(e){let t=this.getDataByLanguage(e);return!!(t&&Object.keys(t)||[]).find(i=>t[i]&&Object.keys(t[i]).length>0)}toJSON(){return this.data}},mt={processors:{},addPostProcessor(r){this.processors[r.name]=r},handle(r,e,t,n,i){return r.forEach(s=>{this.processors[s]&&(e=this.processors[s].process(e,t,n,i))}),e}},ft={},ve=class r extends ne{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};super(),an(["resourceStore","languageUtils","pluralResolver","interpolator","backendConnector","i18nFormat","utils"],e,this),this.options=t,this.options.keySeparator===void 0&&(this.options.keySeparator="."),this.logger=_.create("translator")}changeLanguage(e){e&&(this.language=e)}exists(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}};if(e==null)return!1;let n=this.resolve(e,t);return n&&n.res!==void 0}extractFromKey(e,t){let n=t.nsSeparator!==void 0?t.nsSeparator:this.options.nsSeparator;n===void 0&&(n=":");let i=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator,s=t.ns||this.options.defaultNS||[],o=n&&e.indexOf(n)>-1,a=!this.options.userDefinedKeySeparator&&!t.keySeparator&&!this.options.userDefinedNsSeparator&&!t.nsSeparator&&!gn(e,n,i);if(o&&!a){let u=e.match(this.interpolator.nestingRegexp);if(u&&u.length>0)return{key:e,namespaces:s};let l=e.split(n);(n!==i||n===i&&this.options.ns.indexOf(l[0])>-1)&&(s=l.shift()),e=l.join(i)}return typeof s=="string"&&(s=[s]),{key:e,namespaces:s}}translate(e,t,n){if(typeof t!="object"&&this.options.overloadTranslationOptionHandler&&(t=this.options.overloadTranslationOptionHandler(arguments)),typeof t=="object"&&(t={...t}),t||(t={}),e==null)return"";Array.isArray(e)||(e=[String(e)]);let i=t.returnDetails!==void 0?t.returnDetails:this.options.returnDetails,s=t.keySeparator!==void 0?t.keySeparator:this.options.keySeparator,{key:o,namespaces:a}=this.extractFromKey(e[e.length-1],t),u=a[a.length-1],l=t.lng||this.language,p=t.appendNamespaceToCIMode||this.options.appendNamespaceToCIMode;if(l&&l.toLowerCase()==="cimode"){if(p){let O=t.nsSeparator||this.options.nsSeparator;return i?{res:`${u}${O}${o}`,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:u,usedParams:this.getUsedParamsDetails(t)}:`${u}${O}${o}`}return i?{res:o,usedKey:o,exactUsedKey:o,usedLng:l,usedNS:u,usedParams:this.getUsedParamsDetails(t)}:o}let d=this.resolve(e,t),c=d&&d.res,m=d&&d.usedKey||o,g=d&&d.exactUsedKey||o,P=Object.prototype.toString.apply(c),x=["[object Number]","[object Function]","[object RegExp]"],k=t.joinArrays!==void 0?t.joinArrays:this.options.joinArrays,S=!this.i18nFormat||this.i18nFormat.handleAsObject;if(S&&c&&(typeof c!="string"&&typeof c!="boolean"&&typeof c!="number")&&x.indexOf(P)<0&&!(typeof k=="string"&&P==="[object Array]")){if(!t.returnObjects&&!this.options.returnObjects){this.options.returnedObjectHandler||this.logger.warn("accessing an object - but returnObjects options is not enabled!");let O=this.options.returnedObjectHandler?this.options.returnedObjectHandler(m,c,{...t,ns:a}):`key '${o} (${this.language})' returned an object instead of string.`;return i?(d.res=O,d.usedParams=this.getUsedParamsDetails(t),d):O}if(s){let O=P==="[object Array]",w=O?[]:{},y=O?g:m;for(let N in c)if(Object.prototype.hasOwnProperty.call(c,N)){let I=`${y}${s}${N}`;w[N]=this.translate(I,{...t,joinArrays:!1,ns:a}),w[N]===I&&(w[N]=c[N])}c=w}}else if(S&&typeof k=="string"&&P==="[object Array]")c=c.join(k),c&&(c=this.extendTranslation(c,e,t,n));else{let O=!1,w=!1,y=t.count!==void 0&&typeof t.count!="string",N=r.hasDefaultValue(t),I=y?this.pluralResolver.getSuffix(l,t.count,t):"",U=t.ordinal&&y?this.pluralResolver.getSuffix(l,t.count,{ordinal:!1}):"",j=y&&!t.ordinal&&t.count===0&&this.pluralResolver.shouldUseIntlApi(),f=j&&t[`defaultValue${this.options.pluralSeparator}zero`]||t[`defaultValue${I}`]||t[`defaultValue${U}`]||t.defaultValue;!this.isValidLookup(c)&&N&&(O=!0,c=f),this.isValidLookup(c)||(w=!0,c=o);let L=(t.missingKeyNoValueFallbackToKey||this.options.missingKeyNoValueFallbackToKey)&&w?void 0:c,R=N&&f!==c&&this.options.updateMissing;if(w||O||R){if(this.logger.log(R?"updateKey":"missingKey",l,u,o,R?f:c),s){let B=this.resolve(o,{...t,keySeparator:!1});B&&B.res&&this.logger.warn("Seems the loaded translations were in flat JSON format instead of nested. Either set keySeparator: false on init or make sure your translations are published in nested format.")}let E=[],T=this.languageUtils.getFallbackCodes(this.options.fallbackLng,t.lng||this.language);if(this.options.saveMissingTo==="fallback"&&T&&T[0])for(let B=0;B<T.length;B++)E.push(T[B]);else this.options.saveMissingTo==="all"?E=this.languageUtils.toResolveHierarchy(t.lng||this.language):E.push(t.lng||this.language);let Q=(B,$,V)=>{let ot=N&&V!==c?V:L;this.options.missingKeyHandler?this.options.missingKeyHandler(B,u,$,ot,R,t):this.backendConnector&&this.backendConnector.saveMissing&&this.backendConnector.saveMissing(B,u,$,ot,R,t),this.emit("missingKey",B,u,$,c)};this.options.saveMissing&&(this.options.saveMissingPlurals&&y?E.forEach(B=>{let $=this.pluralResolver.getSuffixes(B,t);j&&t[`defaultValue${this.options.pluralSeparator}zero`]&&$.indexOf(`${this.options.pluralSeparator}zero`)<0&&$.push(`${this.options.pluralSeparator}zero`),$.forEach(V=>{Q([B],o+V,t[`defaultValue${V}`]||f)})}):Q(E,o,f))}c=this.extendTranslation(c,e,t,d,n),w&&c===o&&this.options.appendNamespaceToMissingKey&&(c=`${u}:${o}`),(w||O)&&this.options.parseMissingKeyHandler&&(this.options.compatibilityAPI!=="v1"?c=this.options.parseMissingKeyHandler(this.options.appendNamespaceToMissingKey?`${u}:${o}`:o,O?c:void 0):c=this.options.parseMissingKeyHandler(c))}return i?(d.res=c,d.usedParams=this.getUsedParamsDetails(t),d):c}extendTranslation(e,t,n,i,s){var o=this;if(this.i18nFormat&&this.i18nFormat.parse)e=this.i18nFormat.parse(e,{...this.options.interpolation.defaultVariables,...n},n.lng||this.language||i.usedLng,i.usedNS,i.usedKey,{resolved:i});else if(!n.skipInterpolation){n.interpolation&&this.interpolator.init({...n,interpolation:{...this.options.interpolation,...n.interpolation}});let l=typeof e=="string"&&(n&&n.interpolation&&n.interpolation.skipOnVariables!==void 0?n.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables),p;if(l){let c=e.match(this.interpolator.nestingRegexp);p=c&&c.length}let d=n.replace&&typeof n.replace!="string"?n.replace:n;if(this.options.interpolation.defaultVariables&&(d={...this.options.interpolation.defaultVariables,...d}),e=this.interpolator.interpolate(e,d,n.lng||this.language,n),l){let c=e.match(this.interpolator.nestingRegexp),m=c&&c.length;p<m&&(n.nest=!1)}!n.lng&&this.options.compatibilityAPI!=="v1"&&i&&i.res&&(n.lng=i.usedLng),n.nest!==!1&&(e=this.interpolator.nest(e,function(){for(var c=arguments.length,m=new Array(c),g=0;g<c;g++)m[g]=arguments[g];return s&&s[0]===m[0]&&!n.context?(o.logger.warn(`It seems you are nesting recursively key: ${m[0]} in key: ${t[0]}`),null):o.translate(...m,t)},n)),n.interpolation&&this.interpolator.reset()}let a=n.postProcess||this.options.postProcess,u=typeof a=="string"?[a]:a;return e!=null&&u&&u.length&&n.applyPostProcessor!==!1&&(e=mt.handle(u,e,t,this.options&&this.options.postProcessPassResolved?{i18nResolved:{...i,usedParams:this.getUsedParamsDetails(n)},...n}:n,this)),e}resolve(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n,i,s,o,a;return typeof e=="string"&&(e=[e]),e.forEach(u=>{if(this.isValidLookup(n))return;let l=this.extractFromKey(u,t),p=l.key;i=p;let d=l.namespaces;this.options.fallbackNS&&(d=d.concat(this.options.fallbackNS));let c=t.count!==void 0&&typeof t.count!="string",m=c&&!t.ordinal&&t.count===0&&this.pluralResolver.shouldUseIntlApi(),g=t.context!==void 0&&(typeof t.context=="string"||typeof t.context=="number")&&t.context!=="",P=t.lngs?t.lngs:this.languageUtils.toResolveHierarchy(t.lng||this.language,t.fallbackLng);d.forEach(x=>{this.isValidLookup(n)||(a=x,!ft[`${P[0]}-${x}`]&&this.utils&&this.utils.hasLoadedNamespace&&!this.utils.hasLoadedNamespace(a)&&(ft[`${P[0]}-${x}`]=!0,this.logger.warn(`key "${i}" for languages "${P.join(", ")}" won't get resolved as namespace "${a}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!")),P.forEach(k=>{if(this.isValidLookup(n))return;o=k;let S=[p];if(this.i18nFormat&&this.i18nFormat.addLookupKeys)this.i18nFormat.addLookupKeys(S,p,k,x,t);else{let O;c&&(O=this.pluralResolver.getSuffix(k,t.count,t));let w=`${this.options.pluralSeparator}zero`,y=`${this.options.pluralSeparator}ordinal${this.options.pluralSeparator}`;if(c&&(S.push(p+O),t.ordinal&&O.indexOf(y)===0&&S.push(p+O.replace(y,this.options.pluralSeparator)),m&&S.push(p+w)),g){let N=`${p}${this.options.contextSeparator}${t.context}`;S.push(N),c&&(S.push(N+O),t.ordinal&&O.indexOf(y)===0&&S.push(N+O.replace(y,this.options.pluralSeparator)),m&&S.push(N+w))}}let A;for(;A=S.pop();)this.isValidLookup(n)||(s=A,n=this.getResource(k,x,A,t))}))})}),{res:n,usedKey:i,exactUsedKey:s,usedLng:o,usedNS:a}}isValidLookup(e){return e!==void 0&&!(!this.options.returnNull&&e===null)&&!(!this.options.returnEmptyString&&e==="")}getResource(e,t,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return this.i18nFormat&&this.i18nFormat.getResource?this.i18nFormat.getResource(e,t,n,i):this.resourceStore.getResource(e,t,n,i)}getUsedParamsDetails(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=["defaultValue","ordinal","context","replace","lng","lngs","fallbackLng","ns","keySeparator","nsSeparator","returnObjects","returnDetails","joinArrays","postProcess","interpolation"],n=e.replace&&typeof e.replace!="string",i=n?e.replace:e;if(n&&typeof e.count<"u"&&(i.count=e.count),this.options.interpolation.defaultVariables&&(i={...this.options.interpolation.defaultVariables,...i}),!n){i={...i};for(let s of t)delete i[s]}return i}static hasDefaultValue(e){let t="defaultValue";for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t===n.substring(0,t.length)&&e[n]!==void 0)return!0;return!1}};function je(r){return r.charAt(0).toUpperCase()+r.slice(1)}var xe=class{constructor(e){this.options=e,this.supportedLngs=this.options.supportedLngs||!1,this.logger=_.create("languageUtils")}getScriptPartFromCode(e){if(e=ye(e),!e||e.indexOf("-")<0)return null;let t=e.split("-");return t.length===2||(t.pop(),t[t.length-1].toLowerCase()==="x")?null:this.formatLanguageCode(t.join("-"))}getLanguagePartFromCode(e){if(e=ye(e),!e||e.indexOf("-")<0)return e;let t=e.split("-");return this.formatLanguageCode(t[0])}formatLanguageCode(e){if(typeof e=="string"&&e.indexOf("-")>-1){let t=["hans","hant","latn","cyrl","cans","mong","arab"],n=e.split("-");return this.options.lowerCaseLng?n=n.map(i=>i.toLowerCase()):n.length===2?(n[0]=n[0].toLowerCase(),n[1]=n[1].toUpperCase(),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=je(n[1].toLowerCase()))):n.length===3&&(n[0]=n[0].toLowerCase(),n[1].length===2&&(n[1]=n[1].toUpperCase()),n[0]!=="sgn"&&n[2].length===2&&(n[2]=n[2].toUpperCase()),t.indexOf(n[1].toLowerCase())>-1&&(n[1]=je(n[1].toLowerCase())),t.indexOf(n[2].toLowerCase())>-1&&(n[2]=je(n[2].toLowerCase()))),n.join("-")}return this.options.cleanCode||this.options.lowerCaseLng?e.toLowerCase():e}isSupportedCode(e){return(this.options.load==="languageOnly"||this.options.nonExplicitSupportedLngs)&&(e=this.getLanguagePartFromCode(e)),!this.supportedLngs||!this.supportedLngs.length||this.supportedLngs.indexOf(e)>-1}getBestMatchFromCodes(e){if(!e)return null;let t;return e.forEach(n=>{if(t)return;let i=this.formatLanguageCode(n);(!this.options.supportedLngs||this.isSupportedCode(i))&&(t=i)}),!t&&this.options.supportedLngs&&e.forEach(n=>{if(t)return;let i=this.getLanguagePartFromCode(n);if(this.isSupportedCode(i))return t=i;t=this.options.supportedLngs.find(s=>{if(s===i)return s;if(!(s.indexOf("-")<0&&i.indexOf("-")<0)&&(s.indexOf("-")>0&&i.indexOf("-")<0&&s.substring(0,s.indexOf("-"))===i||s.indexOf(i)===0&&i.length>1))return s})}),t||(t=this.getFallbackCodes(this.options.fallbackLng)[0]),t}getFallbackCodes(e,t){if(!e)return[];if(typeof e=="function"&&(e=e(t)),typeof e=="string"&&(e=[e]),Object.prototype.toString.apply(e)==="[object Array]")return e;if(!t)return e.default||[];let n=e[t];return n||(n=e[this.getScriptPartFromCode(t)]),n||(n=e[this.formatLanguageCode(t)]),n||(n=e[this.getLanguagePartFromCode(t)]),n||(n=e.default),n||[]}toResolveHierarchy(e,t){let n=this.getFallbackCodes(t||this.options.fallbackLng||[],e),i=[],s=o=>{o&&(this.isSupportedCode(o)?i.push(o):this.logger.warn(`rejecting language code not found in supportedLngs: ${o}`))};return typeof e=="string"&&(e.indexOf("-")>-1||e.indexOf("_")>-1)?(this.options.load!=="languageOnly"&&s(this.formatLanguageCode(e)),this.options.load!=="languageOnly"&&this.options.load!=="currentOnly"&&s(this.getScriptPartFromCode(e)),this.options.load!=="currentOnly"&&s(this.getLanguagePartFromCode(e))):typeof e=="string"&&s(this.formatLanguageCode(e)),n.forEach(o=>{i.indexOf(o)<0&&s(this.formatLanguageCode(o))}),i}},mn=[{lngs:["ach","ak","am","arn","br","fil","gun","ln","mfe","mg","mi","oc","pt","pt-BR","tg","tl","ti","tr","uz","wa"],nr:[1,2],fc:1},{lngs:["af","an","ast","az","bg","bn","ca","da","de","dev","el","en","eo","es","et","eu","fi","fo","fur","fy","gl","gu","ha","hi","hu","hy","ia","it","kk","kn","ku","lb","mai","ml","mn","mr","nah","nap","nb","ne","nl","nn","no","nso","pa","pap","pms","ps","pt-PT","rm","sco","se","si","so","son","sq","sv","sw","ta","te","tk","ur","yo"],nr:[1,2],fc:2},{lngs:["ay","bo","cgg","fa","ht","id","ja","jbo","ka","km","ko","ky","lo","ms","sah","su","th","tt","ug","vi","wo","zh"],nr:[1],fc:3},{lngs:["be","bs","cnr","dz","hr","ru","sr","uk"],nr:[1,2,5],fc:4},{lngs:["ar"],nr:[0,1,2,3,11,100],fc:5},{lngs:["cs","sk"],nr:[1,2,5],fc:6},{lngs:["csb","pl"],nr:[1,2,5],fc:7},{lngs:["cy"],nr:[1,2,3,8],fc:8},{lngs:["fr"],nr:[1,2],fc:9},{lngs:["ga"],nr:[1,2,3,7,11],fc:10},{lngs:["gd"],nr:[1,2,3,20],fc:11},{lngs:["is"],nr:[1,2],fc:12},{lngs:["jv"],nr:[0,1],fc:13},{lngs:["kw"],nr:[1,2,3,4],fc:14},{lngs:["lt"],nr:[1,2,10],fc:15},{lngs:["lv"],nr:[1,2,0],fc:16},{lngs:["mk"],nr:[1,2],fc:17},{lngs:["mnk"],nr:[0,1,2],fc:18},{lngs:["mt"],nr:[1,2,11,20],fc:19},{lngs:["or"],nr:[2,1],fc:2},{lngs:["ro"],nr:[1,2,20],fc:20},{lngs:["sl"],nr:[5,1,2,3],fc:21},{lngs:["he","iw"],nr:[1,2,20,21],fc:22}],yn={1:function(r){return+(r>1)},2:function(r){return+(r!=1)},3:function(r){return 0},4:function(r){return r%10==1&&r%100!=11?0:r%10>=2&&r%10<=4&&(r%100<10||r%100>=20)?1:2},5:function(r){return r==0?0:r==1?1:r==2?2:r%100>=3&&r%100<=10?3:r%100>=11?4:5},6:function(r){return r==1?0:r>=2&&r<=4?1:2},7:function(r){return r==1?0:r%10>=2&&r%10<=4&&(r%100<10||r%100>=20)?1:2},8:function(r){return r==1?0:r==2?1:r!=8&&r!=11?2:3},9:function(r){return+(r>=2)},10:function(r){return r==1?0:r==2?1:r<7?2:r<11?3:4},11:function(r){return r==1||r==11?0:r==2||r==12?1:r>2&&r<20?2:3},12:function(r){return+(r%10!=1||r%100==11)},13:function(r){return+(r!==0)},14:function(r){return r==1?0:r==2?1:r==3?2:3},15:function(r){return r%10==1&&r%100!=11?0:r%10>=2&&(r%100<10||r%100>=20)?1:2},16:function(r){return r%10==1&&r%100!=11?0:r!==0?1:2},17:function(r){return r==1||r%10==1&&r%100!=11?0:1},18:function(r){return r==0?0:r==1?1:2},19:function(r){return r==1?0:r==0||r%100>1&&r%100<11?1:r%100>10&&r%100<20?2:3},20:function(r){return r==1?0:r==0||r%100>0&&r%100<20?1:2},21:function(r){return r%100==1?1:r%100==2?2:r%100==3||r%100==4?3:0},22:function(r){return r==1?0:r==2?1:(r<0||r>10)&&r%10==0?2:3}},bn=["v1","v2","v3"],vn=["v4"],ct={zero:0,one:1,two:2,few:3,many:4,other:5};function xn(){let r={};return mn.forEach(e=>{e.lngs.forEach(t=>{r[t]={numbers:e.nr,plurals:yn[e.fc]}})}),r}var Fe=class{constructor(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};this.languageUtils=e,this.options=t,this.logger=_.create("pluralResolver"),(!this.options.compatibilityJSON||vn.includes(this.options.compatibilityJSON))&&(typeof Intl>"u"||!Intl.PluralRules)&&(this.options.compatibilityJSON="v3",this.logger.error("Your environment seems not to be Intl API compatible, use an Intl.PluralRules polyfill. Will fallback to the compatibilityJSON v3 format handling.")),this.rules=xn()}addRule(e,t){this.rules[e]=t}getRule(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(this.shouldUseIntlApi())try{return new Intl.PluralRules(ye(e==="dev"?"en":e),{type:t.ordinal?"ordinal":"cardinal"})}catch{return}return this.rules[e]||this.rules[this.languageUtils.getLanguagePartFromCode(e)]}needsPlural(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=this.getRule(e,t);return this.shouldUseIntlApi()?n&&n.resolvedOptions().pluralCategories.length>1:n&&n.numbers.length>1}getPluralFormsOfKey(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};return this.getSuffixes(e,n).map(i=>`${t}${i}`)}getSuffixes(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=this.getRule(e,t);return n?this.shouldUseIntlApi()?n.resolvedOptions().pluralCategories.sort((i,s)=>ct[i]-ct[s]).map(i=>`${this.options.prepend}${t.ordinal?`ordinal${this.options.prepend}`:""}${i}`):n.numbers.map(i=>this.getSuffix(e,i,t)):[]}getSuffix(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=this.getRule(e,n);return i?this.shouldUseIntlApi()?`${this.options.prepend}${n.ordinal?`ordinal${this.options.prepend}`:""}${i.select(t)}`:this.getSuffixRetroCompatible(i,t):(this.logger.warn(`no plural rule found for: ${e}`),"")}getSuffixRetroCompatible(e,t){let n=e.noAbs?e.plurals(t):e.plurals(Math.abs(t)),i=e.numbers[n];this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1&&(i===2?i="plural":i===1&&(i=""));let s=()=>this.options.prepend&&i.toString()?this.options.prepend+i.toString():i.toString();return this.options.compatibilityJSON==="v1"?i===1?"":typeof i=="number"?`_plural_${i.toString()}`:s():this.options.compatibilityJSON==="v2"||this.options.simplifyPluralSuffix&&e.numbers.length===2&&e.numbers[0]===1?s():this.options.prepend&&n.toString()?this.options.prepend+n.toString():n.toString()}shouldUseIntlApi(){return!bn.includes(this.options.compatibilityJSON)}};function pt(r,e,t){let n=arguments.length>3&&arguments[3]!==void 0?arguments[3]:".",i=arguments.length>4&&arguments[4]!==void 0?arguments[4]:!0,s=fn(r,e,t);return!s&&i&&typeof t=="string"&&(s=De(r,t,n),s===void 0&&(s=De(e,t,n))),s}var Ue=class{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=_.create("interpolator"),this.options=e,this.format=e.interpolation&&e.interpolation.format||(t=>t),this.init(e)}init(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};e.interpolation||(e.interpolation={escapeValue:!0});let t=e.interpolation;this.escape=t.escape!==void 0?t.escape:pn,this.escapeValue=t.escapeValue!==void 0?t.escapeValue:!0,this.useRawValueToEscape=t.useRawValueToEscape!==void 0?t.useRawValueToEscape:!1,this.prefix=t.prefix?ee(t.prefix):t.prefixEscaped||"{{",this.suffix=t.suffix?ee(t.suffix):t.suffixEscaped||"}}",this.formatSeparator=t.formatSeparator?t.formatSeparator:t.formatSeparator||",",this.unescapePrefix=t.unescapeSuffix?"":t.unescapePrefix||"-",this.unescapeSuffix=this.unescapePrefix?"":t.unescapeSuffix||"",this.nestingPrefix=t.nestingPrefix?ee(t.nestingPrefix):t.nestingPrefixEscaped||ee("$t("),this.nestingSuffix=t.nestingSuffix?ee(t.nestingSuffix):t.nestingSuffixEscaped||ee(")"),this.nestingOptionsSeparator=t.nestingOptionsSeparator?t.nestingOptionsSeparator:t.nestingOptionsSeparator||",",this.maxReplaces=t.maxReplaces?t.maxReplaces:1e3,this.alwaysFormat=t.alwaysFormat!==void 0?t.alwaysFormat:!1,this.resetRegExp()}reset(){this.options&&this.init(this.options)}resetRegExp(){let e=(t,n)=>t&&t.source===n?(t.lastIndex=0,t):new RegExp(n,"g");this.regexp=e(this.regexp,`${this.prefix}(.+?)${this.suffix}`),this.regexpUnescape=e(this.regexpUnescape,`${this.prefix}${this.unescapePrefix}(.+?)${this.unescapeSuffix}${this.suffix}`),this.nestingRegexp=e(this.nestingRegexp,`${this.nestingPrefix}(.+?)${this.nestingSuffix}`)}interpolate(e,t,n,i){let s,o,a,u=this.options&&this.options.interpolation&&this.options.interpolation.defaultVariables||{};function l(g){return g.replace(/\$/g,"$$$$")}let p=g=>{if(g.indexOf(this.formatSeparator)<0){let S=pt(t,u,g,this.options.keySeparator,this.options.ignoreJSONStructure);return this.alwaysFormat?this.format(S,void 0,n,{...i,...t,interpolationkey:g}):S}let P=g.split(this.formatSeparator),x=P.shift().trim(),k=P.join(this.formatSeparator).trim();return this.format(pt(t,u,x,this.options.keySeparator,this.options.ignoreJSONStructure),k,n,{...i,...t,interpolationkey:x})};this.resetRegExp();let d=i&&i.missingInterpolationHandler||this.options.missingInterpolationHandler,c=i&&i.interpolation&&i.interpolation.skipOnVariables!==void 0?i.interpolation.skipOnVariables:this.options.interpolation.skipOnVariables;return[{regex:this.regexpUnescape,safeValue:g=>l(g)},{regex:this.regexp,safeValue:g=>this.escapeValue?l(this.escape(g)):l(g)}].forEach(g=>{for(a=0;s=g.regex.exec(e);){let P=s[1].trim();if(o=p(P),o===void 0)if(typeof d=="function"){let k=d(e,s,i);o=typeof k=="string"?k:""}else if(i&&Object.prototype.hasOwnProperty.call(i,P))o="";else if(c){o=s[0];continue}else this.logger.warn(`missed to pass in variable ${P} for interpolating ${e}`),o="";else typeof o!="string"&&!this.useRawValueToEscape&&(o=ut(o));let x=g.safeValue(o);if(e=e.replace(s[0],x),c?(g.regex.lastIndex+=o.length,g.regex.lastIndex-=s[0].length):g.regex.lastIndex=0,a++,a>=this.maxReplaces)break}}),e}nest(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i,s,o;function a(u,l){let p=this.nestingOptionsSeparator;if(u.indexOf(p)<0)return u;let d=u.split(new RegExp(`${p}[ ]*{`)),c=`{${d[1]}`;u=d[0],c=this.interpolate(c,o);let m=c.match(/'/g),g=c.match(/"/g);(m&&m.length%2===0&&!g||g.length%2!==0)&&(c=c.replace(/'/g,'"'));try{o=JSON.parse(c),l&&(o={...l,...o})}catch(P){return this.logger.warn(`failed parsing options string in nesting for key ${u}`,P),`${u}${p}${c}`}return o.defaultValue&&o.defaultValue.indexOf(this.prefix)>-1&&delete o.defaultValue,u}for(;i=this.nestingRegexp.exec(e);){let u=[];o={...n},o=o.replace&&typeof o.replace!="string"?o.replace:o,o.applyPostProcessor=!1,delete o.defaultValue;let l=!1;if(i[0].indexOf(this.formatSeparator)!==-1&&!/{.*}/.test(i[1])){let p=i[1].split(this.formatSeparator).map(d=>d.trim());i[1]=p.shift(),u=p,l=!0}if(s=t(a.call(this,i[1].trim(),o),o),s&&i[0]===e&&typeof s!="string")return s;typeof s!="string"&&(s=ut(s)),s||(this.logger.warn(`missed to resolve ${i[1]} for nesting ${e}`),s=""),l&&(s=u.reduce((p,d)=>this.format(p,d,n.lng,{...n,interpolationkey:i[1].trim()}),s.trim())),e=e.replace(i[0],s),this.regexp.lastIndex=0}return e}};function wn(r){let e=r.toLowerCase().trim(),t={};if(r.indexOf("(")>-1){let n=r.split("(");e=n[0].toLowerCase().trim();let i=n[1].substring(0,n[1].length-1);e==="currency"&&i.indexOf(":")<0?t.currency||(t.currency=i.trim()):e==="relativetime"&&i.indexOf(":")<0?t.range||(t.range=i.trim()):i.split(";").forEach(o=>{if(!o)return;let[a,...u]=o.split(":"),l=u.join(":").trim().replace(/^'+|'+$/g,"");t[a.trim()]||(t[a.trim()]=l),l==="false"&&(t[a.trim()]=!1),l==="true"&&(t[a.trim()]=!0),isNaN(l)||(t[a.trim()]=parseInt(l,10))})}return{formatName:e,formatOptions:t}}function te(r){let e={};return function(n,i,s){let o=i+JSON.stringify(s),a=e[o];return a||(a=r(ye(i),s),e[o]=a),a(n)}}var Be=class{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{};this.logger=_.create("formatter"),this.options=e,this.formats={number:te((t,n)=>{let i=new Intl.NumberFormat(t,{...n});return s=>i.format(s)}),currency:te((t,n)=>{let i=new Intl.NumberFormat(t,{...n,style:"currency"});return s=>i.format(s)}),datetime:te((t,n)=>{let i=new Intl.DateTimeFormat(t,{...n});return s=>i.format(s)}),relativetime:te((t,n)=>{let i=new Intl.RelativeTimeFormat(t,{...n});return s=>i.format(s,n.range||"day")}),list:te((t,n)=>{let i=new Intl.ListFormat(t,{...n});return s=>i.format(s)})},this.init(e)}init(e){let n=(arguments.length>1&&arguments[1]!==void 0?arguments[1]:{interpolation:{}}).interpolation;this.formatSeparator=n.formatSeparator?n.formatSeparator:n.formatSeparator||","}add(e,t){this.formats[e.toLowerCase().trim()]=t}addCached(e,t){this.formats[e.toLowerCase().trim()]=te(t)}format(e,t,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};return t.split(this.formatSeparator).reduce((a,u)=>{let{formatName:l,formatOptions:p}=wn(u);if(this.formats[l]){let d=a;try{let c=i&&i.formatParams&&i.formatParams[i.interpolationkey]||{},m=c.locale||c.lng||i.locale||i.lng||n;d=this.formats[l](a,m,{...p,...i,...c})}catch(c){this.logger.warn(c)}return d}else this.logger.warn(`there was no format function for ${l}`);return a},e)}};function Sn(r,e){r.pending[e]!==void 0&&(delete r.pending[e],r.pendingCount--)}var Me=class extends ne{constructor(e,t,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{};super(),this.backend=e,this.store=t,this.services=n,this.languageUtils=n.languageUtils,this.options=i,this.logger=_.create("backendConnector"),this.waitingReads=[],this.maxParallelReads=i.maxParallelReads||10,this.readingCalls=0,this.maxRetries=i.maxRetries>=0?i.maxRetries:5,this.retryTimeout=i.retryTimeout>=1?i.retryTimeout:350,this.state={},this.queue=[],this.backend&&this.backend.init&&this.backend.init(n,i.backend,i)}queueLoad(e,t,n,i){let s={},o={},a={},u={};return e.forEach(l=>{let p=!0;t.forEach(d=>{let c=`${l}|${d}`;!n.reload&&this.store.hasResourceBundle(l,d)?this.state[c]=2:this.state[c]<0||(this.state[c]===1?o[c]===void 0&&(o[c]=!0):(this.state[c]=1,p=!1,o[c]===void 0&&(o[c]=!0),s[c]===void 0&&(s[c]=!0),u[d]===void 0&&(u[d]=!0)))}),p||(a[l]=!0)}),(Object.keys(s).length||Object.keys(o).length)&&this.queue.push({pending:o,pendingCount:Object.keys(o).length,loaded:{},errors:[],callback:i}),{toLoad:Object.keys(s),pending:Object.keys(o),toLoadLanguages:Object.keys(a),toLoadNamespaces:Object.keys(u)}}loaded(e,t,n){let i=e.split("|"),s=i[0],o=i[1];t&&this.emit("failedLoading",s,o,t),n&&this.store.addResourceBundle(s,o,n,void 0,void 0,{skipCopy:!0}),this.state[e]=t?-1:2;let a={};this.queue.forEach(u=>{ln(u.loaded,[s],o),Sn(u,e),t&&u.errors.push(t),u.pendingCount===0&&!u.done&&(Object.keys(u.loaded).forEach(l=>{a[l]||(a[l]={});let p=u.loaded[l];p.length&&p.forEach(d=>{a[l][d]===void 0&&(a[l][d]=!0)})}),u.done=!0,u.errors.length?u.callback(u.errors):u.callback())}),this.emit("loaded",a),this.queue=this.queue.filter(u=>!u.done)}read(e,t,n){let i=arguments.length>3&&arguments[3]!==void 0?arguments[3]:0,s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:this.retryTimeout,o=arguments.length>5?arguments[5]:void 0;if(!e.length)return o(null,{});if(this.readingCalls>=this.maxParallelReads){this.waitingReads.push({lng:e,ns:t,fcName:n,tried:i,wait:s,callback:o});return}this.readingCalls++;let a=(l,p)=>{if(this.readingCalls--,this.waitingReads.length>0){let d=this.waitingReads.shift();this.read(d.lng,d.ns,d.fcName,d.tried,d.wait,d.callback)}if(l&&p&&i<this.maxRetries){setTimeout(()=>{this.read.call(this,e,t,n,i+1,s*2,o)},s);return}o(l,p)},u=this.backend[n].bind(this.backend);if(u.length===2){try{let l=u(e,t);l&&typeof l.then=="function"?l.then(p=>a(null,p)).catch(a):a(null,l)}catch(l){a(l)}return}return u(e,t,a)}prepareLoading(e,t){let n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=arguments.length>3?arguments[3]:void 0;if(!this.backend)return this.logger.warn("No backend was added via i18next.use. Will not load resources."),i&&i();typeof e=="string"&&(e=this.languageUtils.toResolveHierarchy(e)),typeof t=="string"&&(t=[t]);let s=this.queueLoad(e,t,n,i);if(!s.toLoad.length)return s.pending.length||i(),null;s.toLoad.forEach(o=>{this.loadOne(o)})}load(e,t,n){this.prepareLoading(e,t,{},n)}reload(e,t,n){this.prepareLoading(e,t,{reload:!0},n)}loadOne(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:"",n=e.split("|"),i=n[0],s=n[1];this.read(i,s,"read",void 0,void 0,(o,a)=>{o&&this.logger.warn(`${t}loading namespace ${s} for language ${i} failed`,o),!o&&a&&this.logger.log(`${t}loaded namespace ${s} for language ${i}`,a),this.loaded(e,o,a)})}saveMissing(e,t,n,i,s){let o=arguments.length>5&&arguments[5]!==void 0?arguments[5]:{},a=arguments.length>6&&arguments[6]!==void 0?arguments[6]:()=>{};if(this.services.utils&&this.services.utils.hasLoadedNamespace&&!this.services.utils.hasLoadedNamespace(t)){this.logger.warn(`did not save key "${n}" as the namespace "${t}" was not yet loaded`,"This means something IS WRONG in your setup. You access the t function before i18next.init / i18next.loadNamespace / i18next.changeLanguage was done. Wait for the callback or Promise to resolve before accessing it!!!");return}if(!(n==null||n==="")){if(this.backend&&this.backend.create){let u={...o,isUpdate:s},l=this.backend.create.bind(this.backend);if(l.length<6)try{let p;l.length===5?p=l(e,t,n,i,u):p=l(e,t,n,i),p&&typeof p.then=="function"?p.then(d=>a(null,d)).catch(a):a(null,p)}catch(p){a(p)}else l(e,t,n,i,a,u)}!e||!e[0]||this.store.addResource(e[0],t,n,i)}}};function dt(){return{debug:!1,initImmediate:!0,ns:["translation"],defaultNS:["translation"],fallbackLng:["dev"],fallbackNS:!1,supportedLngs:!1,nonExplicitSupportedLngs:!1,load:"all",preload:!1,simplifyPluralSuffix:!0,keySeparator:".",nsSeparator:":",pluralSeparator:"_",contextSeparator:"_",partialBundledLanguages:!1,saveMissing:!1,updateMissing:!1,saveMissingTo:"fallback",saveMissingPlurals:!0,missingKeyHandler:!1,missingInterpolationHandler:!1,postProcess:!1,postProcessPassResolved:!1,returnNull:!1,returnEmptyString:!0,returnObjects:!1,joinArrays:!1,returnedObjectHandler:!1,parseMissingKeyHandler:!1,appendNamespaceToMissingKey:!1,appendNamespaceToCIMode:!1,overloadTranslationOptionHandler:function(e){let t={};if(typeof e[1]=="object"&&(t=e[1]),typeof e[1]=="string"&&(t.defaultValue=e[1]),typeof e[2]=="string"&&(t.tDescription=e[2]),typeof e[2]=="object"||typeof e[3]=="object"){let n=e[3]||e[2];Object.keys(n).forEach(i=>{t[i]=n[i]})}return t},interpolation:{escapeValue:!0,format:r=>r,prefix:"{{",suffix:"}}",formatSeparator:",",unescapePrefix:"-",nestingPrefix:"$t(",nestingSuffix:")",nestingOptionsSeparator:",",maxReplaces:1e3,skipOnVariables:!0}}}function ht(r){return typeof r.ns=="string"&&(r.ns=[r.ns]),typeof r.fallbackLng=="string"&&(r.fallbackLng=[r.fallbackLng]),typeof r.fallbackNS=="string"&&(r.fallbackNS=[r.fallbackNS]),r.supportedLngs&&r.supportedLngs.indexOf("cimode")<0&&(r.supportedLngs=r.supportedLngs.concat(["cimode"])),r}function ge(){}function On(r){Object.getOwnPropertyNames(Object.getPrototypeOf(r)).forEach(t=>{typeof r[t]=="function"&&(r[t]=r[t].bind(r))})}var we=class r extends ne{constructor(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;if(super(),this.options=ht(e),this.services={},this.logger=_,this.modules={external:[]},On(this),t&&!this.isInitialized&&!e.isClone){if(!this.options.initImmediate)return this.init(e,t),this;setTimeout(()=>{this.init(e,t)},0)}}init(){var e=this;let t=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},n=arguments.length>1?arguments[1]:void 0;this.isInitializing=!0,typeof t=="function"&&(n=t,t={}),!t.defaultNS&&t.defaultNS!==!1&&t.ns&&(typeof t.ns=="string"?t.defaultNS=t.ns:t.ns.indexOf("translation")<0&&(t.defaultNS=t.ns[0]));let i=dt();this.options={...i,...this.options,...ht(t)},this.options.compatibilityAPI!=="v1"&&(this.options.interpolation={...i.interpolation,...this.options.interpolation}),t.keySeparator!==void 0&&(this.options.userDefinedKeySeparator=t.keySeparator),t.nsSeparator!==void 0&&(this.options.userDefinedNsSeparator=t.nsSeparator);function s(p){return p?typeof p=="function"?new p:p:null}if(!this.options.isClone){this.modules.logger?_.init(s(this.modules.logger),this.options):_.init(null,this.options);let p;this.modules.formatter?p=this.modules.formatter:typeof Intl<"u"&&(p=Be);let d=new xe(this.options);this.store=new be(this.options.resources,this.options);let c=this.services;c.logger=_,c.resourceStore=this.store,c.languageUtils=d,c.pluralResolver=new Fe(d,{prepend:this.options.pluralSeparator,compatibilityJSON:this.options.compatibilityJSON,simplifyPluralSuffix:this.options.simplifyPluralSuffix}),p&&(!this.options.interpolation.format||this.options.interpolation.format===i.interpolation.format)&&(c.formatter=s(p),c.formatter.init(c,this.options),this.options.interpolation.format=c.formatter.format.bind(c.formatter)),c.interpolator=new Ue(this.options),c.utils={hasLoadedNamespace:this.hasLoadedNamespace.bind(this)},c.backendConnector=new Me(s(this.modules.backend),c.resourceStore,c,this.options),c.backendConnector.on("*",function(m){for(var g=arguments.length,P=new Array(g>1?g-1:0),x=1;x<g;x++)P[x-1]=arguments[x];e.emit(m,...P)}),this.modules.languageDetector&&(c.languageDetector=s(this.modules.languageDetector),c.languageDetector.init&&c.languageDetector.init(c,this.options.detection,this.options)),this.modules.i18nFormat&&(c.i18nFormat=s(this.modules.i18nFormat),c.i18nFormat.init&&c.i18nFormat.init(this)),this.translator=new ve(this.services,this.options),this.translator.on("*",function(m){for(var g=arguments.length,P=new Array(g>1?g-1:0),x=1;x<g;x++)P[x-1]=arguments[x];e.emit(m,...P)}),this.modules.external.forEach(m=>{m.init&&m.init(this)})}if(this.format=this.options.interpolation.format,n||(n=ge),this.options.fallbackLng&&!this.services.languageDetector&&!this.options.lng){let p=this.services.languageUtils.getFallbackCodes(this.options.fallbackLng);p.length>0&&p[0]!=="dev"&&(this.options.lng=p[0])}!this.services.languageDetector&&!this.options.lng&&this.logger.warn("init: no languageDetector is used and no lng is defined"),["getResource","hasResourceBundle","getResourceBundle","getDataByLanguage"].forEach(p=>{this[p]=function(){return e.store[p](...arguments)}}),["addResource","addResources","addResourceBundle","removeResourceBundle"].forEach(p=>{this[p]=function(){return e.store[p](...arguments),e}});let u=ue(),l=()=>{let p=(d,c)=>{this.isInitializing=!1,this.isInitialized&&!this.initializedStoreOnce&&this.logger.warn("init: i18next is already initialized. You should call init just once!"),this.isInitialized=!0,this.options.isClone||this.logger.log("initialized",this.options),this.emit("initialized",this.options),u.resolve(c),n(d,c)};if(this.languages&&this.options.compatibilityAPI!=="v1"&&!this.isInitialized)return p(null,this.t.bind(this));this.changeLanguage(this.options.lng,p)};return this.options.resources||!this.options.initImmediate?l():setTimeout(l,0),u}loadResources(e){let n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ge,i=typeof e=="string"?e:this.language;if(typeof e=="function"&&(n=e),!this.options.resources||this.options.partialBundledLanguages){if(i&&i.toLowerCase()==="cimode"&&(!this.options.preload||this.options.preload.length===0))return n();let s=[],o=a=>{if(!a||a==="cimode")return;this.services.languageUtils.toResolveHierarchy(a).forEach(l=>{l!=="cimode"&&s.indexOf(l)<0&&s.push(l)})};i?o(i):this.services.languageUtils.getFallbackCodes(this.options.fallbackLng).forEach(u=>o(u)),this.options.preload&&this.options.preload.forEach(a=>o(a)),this.services.backendConnector.load(s,this.options.ns,a=>{!a&&!this.resolvedLanguage&&this.language&&this.setResolvedLanguage(this.language),n(a)})}else n(null)}reloadResources(e,t,n){let i=ue();return e||(e=this.languages),t||(t=this.options.ns),n||(n=ge),this.services.backendConnector.reload(e,t,s=>{i.resolve(),n(s)}),i}use(e){if(!e)throw new Error("You are passing an undefined module! Please check the object you are passing to i18next.use()");if(!e.type)throw new Error("You are passing a wrong module! Please check the object you are passing to i18next.use()");return e.type==="backend"&&(this.modules.backend=e),(e.type==="logger"||e.log&&e.warn&&e.error)&&(this.modules.logger=e),e.type==="languageDetector"&&(this.modules.languageDetector=e),e.type==="i18nFormat"&&(this.modules.i18nFormat=e),e.type==="postProcessor"&&mt.addPostProcessor(e),e.type==="formatter"&&(this.modules.formatter=e),e.type==="3rdParty"&&this.modules.external.push(e),this}setResolvedLanguage(e){if(!(!e||!this.languages)&&!(["cimode","dev"].indexOf(e)>-1))for(let t=0;t<this.languages.length;t++){let n=this.languages[t];if(!(["cimode","dev"].indexOf(n)>-1)&&this.store.hasLanguageSomeTranslations(n)){this.resolvedLanguage=n;break}}}changeLanguage(e,t){var n=this;this.isLanguageChangingTo=e;let i=ue();this.emit("languageChanging",e);let s=u=>{this.language=u,this.languages=this.services.languageUtils.toResolveHierarchy(u),this.resolvedLanguage=void 0,this.setResolvedLanguage(u)},o=(u,l)=>{l?(s(l),this.translator.changeLanguage(l),this.isLanguageChangingTo=void 0,this.emit("languageChanged",l),this.logger.log("languageChanged",l)):this.isLanguageChangingTo=void 0,i.resolve(function(){return n.t(...arguments)}),t&&t(u,function(){return n.t(...arguments)})},a=u=>{!e&&!u&&this.services.languageDetector&&(u=[]);let l=typeof u=="string"?u:this.services.languageUtils.getBestMatchFromCodes(u);l&&(this.language||s(l),this.translator.language||this.translator.changeLanguage(l),this.services.languageDetector&&this.services.languageDetector.cacheUserLanguage&&this.services.languageDetector.cacheUserLanguage(l)),this.loadResources(l,p=>{o(p,l)})};return!e&&this.services.languageDetector&&!this.services.languageDetector.async?a(this.services.languageDetector.detect()):!e&&this.services.languageDetector&&this.services.languageDetector.async?this.services.languageDetector.detect.length===0?this.services.languageDetector.detect().then(a):this.services.languageDetector.detect(a):a(e),i}getFixedT(e,t,n){var i=this;let s=function(o,a){let u;if(typeof a!="object"){for(var l=arguments.length,p=new Array(l>2?l-2:0),d=2;d<l;d++)p[d-2]=arguments[d];u=i.options.overloadTranslationOptionHandler([o,a].concat(p))}else u={...a};u.lng=u.lng||s.lng,u.lngs=u.lngs||s.lngs,u.ns=u.ns||s.ns,u.keyPrefix=u.keyPrefix||n||s.keyPrefix;let c=i.options.keySeparator||".",m;return u.keyPrefix&&Array.isArray(o)?m=o.map(g=>`${u.keyPrefix}${c}${g}`):m=u.keyPrefix?`${u.keyPrefix}${c}${o}`:o,i.t(m,u)};return typeof e=="string"?s.lng=e:s.lngs=e,s.ns=t,s.keyPrefix=n,s}t(){return this.translator&&this.translator.translate(...arguments)}exists(){return this.translator&&this.translator.exists(...arguments)}setDefaultNamespace(e){this.options.defaultNS=e}hasLoadedNamespace(e){let t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};if(!this.isInitialized)return this.logger.warn("hasLoadedNamespace: i18next was not initialized",this.languages),!1;if(!this.languages||!this.languages.length)return this.logger.warn("hasLoadedNamespace: i18n.languages were undefined or empty",this.languages),!1;let n=t.lng||this.resolvedLanguage||this.languages[0],i=this.options?this.options.fallbackLng:!1,s=this.languages[this.languages.length-1];if(n.toLowerCase()==="cimode")return!0;let o=(a,u)=>{let l=this.services.backendConnector.state[`${a}|${u}`];return l===-1||l===2};if(t.precheck){let a=t.precheck(this,o);if(a!==void 0)return a}return!!(this.hasResourceBundle(n,e)||!this.services.backendConnector.backend||this.options.resources&&!this.options.partialBundledLanguages||o(n,e)&&(!i||o(s,e)))}loadNamespaces(e,t){let n=ue();return this.options.ns?(typeof e=="string"&&(e=[e]),e.forEach(i=>{this.options.ns.indexOf(i)<0&&this.options.ns.push(i)}),this.loadResources(i=>{n.resolve(),t&&t(i)}),n):(t&&t(),Promise.resolve())}loadLanguages(e,t){let n=ue();typeof e=="string"&&(e=[e]);let i=this.options.preload||[],s=e.filter(o=>i.indexOf(o)<0&&this.services.languageUtils.isSupportedCode(o));return s.length?(this.options.preload=i.concat(s),this.loadResources(o=>{n.resolve(),t&&t(o)}),n):(t&&t(),Promise.resolve())}dir(e){if(e||(e=this.resolvedLanguage||(this.languages&&this.languages.length>0?this.languages[0]:this.language)),!e)return"rtl";let t=["ar","shu","sqr","ssh","xaa","yhd","yud","aao","abh","abv","acm","acq","acw","acx","acy","adf","ads","aeb","aec","afb","ajp","apc","apd","arb","arq","ars","ary","arz","auz","avl","ayh","ayl","ayn","ayp","bbz","pga","he","iw","ps","pbt","pbu","pst","prp","prd","ug","ur","ydd","yds","yih","ji","yi","hbo","men","xmn","fa","jpr","peo","pes","prs","dv","sam","ckb"],n=this.services&&this.services.languageUtils||new xe(dt());return t.indexOf(n.getLanguagePartFromCode(e))>-1||e.toLowerCase().indexOf("-arab")>1?"rtl":"ltr"}static createInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0;return new r(e,t)}cloneInstance(){let e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:ge,n=e.forkResourceStore;n&&delete e.forkResourceStore;let i={...this.options,...e,isClone:!0},s=new r(i);return(e.debug!==void 0||e.prefix!==void 0)&&(s.logger=s.logger.clone(e)),["store","services","language"].forEach(a=>{s[a]=this[a]}),s.services={...this.services},s.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},n&&(s.store=new be(this.store.data,i),s.services.resourceStore=s.store),s.translator=new ve(s.services,i),s.translator.on("*",function(a){for(var u=arguments.length,l=new Array(u>1?u-1:0),p=1;p<u;p++)l[p-1]=arguments[p];s.emit(a,...l)}),s.init(i,t),s.translator.options=i,s.translator.backendConnector.services.utils={hasLoadedNamespace:s.hasLoadedNamespace.bind(s)},s}toJSON(){return{options:this.options,store:this.store,language:this.language,languages:this.languages,resolvedLanguage:this.resolvedLanguage}}},D=we.createInstance();D.createInstance=we.createInstance;var hr=D.createInstance,gr=D.dir,mr=D.init,yr=D.loadResources,br=D.reloadResources,vr=D.use,xr=D.changeLanguage,wr=D.getFixedT,Sr=D.t,Or=D.exists,Pr=D.setDefaultNamespace,Lr=D.hasLoadedNamespace,Er=D.loadNamespaces,kr=D.loadLanguages;b();v();var H=K(J(),1);b();v();var vt=K(J(),1);b();v();b();v();var Pn=/&(?:amp|#38|lt|#60|gt|#62|apos|#39|quot|#34|nbsp|#160|copy|#169|reg|#174|hellip|#8230|#x2F|#47);/g,Ln={"&amp;":"&","&#38;":"&","&lt;":"<","&#60;":"<","&gt;":">","&#62;":">","&apos;":"'","&#39;":"'","&quot;":'"',"&#34;":'"',"&nbsp;":" ","&#160;":" ","&copy;":"\xA9","&#169;":"\xA9","&reg;":"\xAE","&#174;":"\xAE","&hellip;":"\u2026","&#8230;":"\u2026","&#x2F;":"/","&#47;":"/"},En=r=>Ln[r],yt=r=>r.replace(Pn,En);var He={bindI18n:"languageChanged",bindI18nStore:"",transEmptyNodeValue:"",transSupportBasicHtmlNodes:!0,transWrapTextNodes:"",transKeepBasicHtmlNodesFor:["br","strong","i","p"],useSuspense:!0,unescape:yt},Se=(r={})=>{He={...He,...r}},re=()=>He;b();v();var bt,Oe=r=>{bt=r},q=()=>bt;b();v();var Ve={type:"3rdParty",init(r){Se(r.options.react),Oe(r)}};var Y=(0,vt.createContext)(),Pe=class{constructor(){this.usedNamespaces={}}addUsedNamespaces(e){e.forEach(t=>{this.usedNamespaces[t]??=!0})}getUsedNamespaces(){return Object.keys(this.usedNamespaces)}};b();v();var fe=(...r)=>{console?.warn&&(M(r[0])&&(r[0]=`react-i18next:: ${r[0]}`),console.warn(...r))},xt={},ie=(...r)=>{M(r[0])&&xt[r[0]]||(M(r[0])&&(xt[r[0]]=new Date),fe(...r))},wt=(r,e)=>()=>{if(r.isInitialized)e();else{let t=()=>{setTimeout(()=>{r.off("initialized",t)},0),e()};r.on("initialized",t)}},Ke=(r,e,t)=>{r.loadNamespaces(e,wt(r,t))},_e=(r,e,t,n)=>{M(t)&&(t=[t]),t.forEach(i=>{r.options.ns.indexOf(i)<0&&r.options.ns.push(i)}),r.loadLanguages(e,wt(r,n))},St=(r,e,t={})=>!e.languages||!e.languages.length?(ie("i18n.languages were undefined or empty",e.languages),!0):e.hasLoadedNamespace(r,{lng:t.lng,precheck:(n,i)=>{if(t.bindI18n?.indexOf("languageChanging")>-1&&n.services.backendConnector.backend&&n.isLanguageChangingTo&&!i(n.isLanguageChangingTo,r))return!1}});var M=r=>typeof r=="string",X=r=>typeof r=="object"&&r!==null;var kn=(r,e)=>{let t=(0,H.useRef)();return(0,H.useEffect)(()=>{t.current=e?t.current:r},[r,e]),t.current},Ot=(r,e,t,n)=>r.getFixedT(e,t,n),Nn=(r,e,t,n)=>(0,H.useCallback)(Ot(r,e,t,n),[r,e,t,n]),Le=(r,e={})=>{let{i18n:t}=e,{i18n:n,defaultNS:i}=(0,H.useContext)(Y)||{},s=t||n||q();if(s&&!s.reportNamespaces&&(s.reportNamespaces=new Pe),!s){ie("You will need to pass in an i18next instance by using initReactI18next");let O=(y,N)=>M(N)?N:X(N)&&M(N.defaultValue)?N.defaultValue:Array.isArray(y)?y[y.length-1]:y,w=[O,{},!1];return w.t=O,w.i18n={},w.ready=!1,w}s.options.react?.wait&&ie("It seems you are still using the old wait option, you may migrate to the new useSuspense behaviour.");let o={...re(),...s.options.react,...e},{useSuspense:a,keyPrefix:u}=o,l=r||i||s.options?.defaultNS;l=M(l)?[l]:l||["translation"],s.reportNamespaces.addUsedNamespaces?.(l);let p=(s.isInitialized||s.initializedStoreOnce)&&l.every(O=>St(O,s,o)),d=Nn(s,e.lng||null,o.nsMode==="fallback"?l:l[0],u),c=()=>d,m=()=>Ot(s,e.lng||null,o.nsMode==="fallback"?l:l[0],u),[g,P]=(0,H.useState)(c),x=l.join();e.lng&&(x=`${e.lng}${x}`);let k=kn(x),S=(0,H.useRef)(!0);(0,H.useEffect)(()=>{let{bindI18n:O,bindI18nStore:w}=o;S.current=!0,!p&&!a&&(e.lng?_e(s,e.lng,l,()=>{S.current&&P(m)}):Ke(s,l,()=>{S.current&&P(m)})),p&&k&&k!==x&&S.current&&P(m);let y=()=>{S.current&&P(m)};return O&&s?.on(O,y),w&&s?.store.on(w,y),()=>{S.current=!1,s&&O?.split(" ").forEach(N=>s.off(N,y)),w&&s&&w.split(" ").forEach(N=>s.store.off(N,y))}},[s,x]),(0,H.useEffect)(()=>{S.current&&p&&P(c)},[s,u,p]);let A=[g,s,p];if(A.t=g,A.i18n=s,A.ready=p,p||!p&&!a)return A;throw new Promise(O=>{e.lng?_e(s,e.lng,l,()=>O()):Ke(s,l,()=>O())})};b();v();b();v();var Rt=K(J(),1);b();v();var F=K(J(),1);b();v();var kt=K(Lt()),Tn=/\s([^'"/\s><]+?)[\s/>]|([^\s=]+)=\s?(".*?"|'.*?')/g;function Et(r){var e={type:"tag",name:"",voidElement:!1,attrs:{},children:[]},t=r.match(/<\/?([^\s]+?)[/\s>]/);if(t&&(e.name=t[1],(kt.default[t[1]]||r.charAt(r.length-2)==="/")&&(e.voidElement=!0),e.name.startsWith("!--"))){var n=r.indexOf("-->");return{type:"comment",comment:n!==-1?r.slice(4,n):""}}for(var i=new RegExp(Tn),s=null;(s=i.exec(r))!==null;)if(s[0].trim())if(s[1]){var o=s[1].trim(),a=[o,""];o.indexOf("=")>-1&&(a=o.split("=")),e.attrs[a[0]]=a[1],i.lastIndex--}else s[2]&&(e.attrs[s[2]]=s[3].trim().substring(1,s[3].length-1));return e}var Rn=/<[a-zA-Z0-9\-\!\/](?:"[^"]*"|'[^']*'|[^'">])*>/g,Cn=/^\s*$/,An=Object.create(null);function Nt(r,e){switch(e.type){case"text":return r+e.content;case"tag":return r+="<"+e.name+(e.attrs?function(t){var n=[];for(var i in t)n.push(i+'="'+t[i]+'"');return n.length?" "+n.join(" "):""}(e.attrs):"")+(e.voidElement?"/>":">"),e.voidElement?r:r+e.children.reduce(Nt,"")+"</"+e.name+">";case"comment":return r+"<!--"+e.comment+"-->"}}var jn={parse:function(r,e){e||(e={}),e.components||(e.components=An);var t,n=[],i=[],s=-1,o=!1;if(r.indexOf("<")!==0){var a=r.indexOf("<");n.push({type:"text",content:a===-1?r:r.substring(0,a)})}return r.replace(Rn,function(u,l){if(o){if(u!=="</"+t.name+">")return;o=!1}var p,d=u.charAt(1)!=="/",c=u.startsWith("<!--"),m=l+u.length,g=r.charAt(m);if(c){var P=Et(u);return s<0?(n.push(P),n):((p=i[s]).children.push(P),n)}if(d&&(s++,(t=Et(u)).type==="tag"&&e.components[t.name]&&(t.type="component",o=!0),t.voidElement||o||!g||g==="<"||t.children.push({type:"text",content:r.slice(m,r.indexOf("<",m))}),s===0&&n.push(t),(p=i[s-1])&&p.children.push(t),i[s]=t),(!d||t.voidElement)&&(s>-1&&(t.voidElement||t.name===u.slice(2,-1))&&(s--,t=s===-1?n:i[s]),!o&&g!=="<"&&g)){p=s===-1?n:i[s].children;var x=r.indexOf("<",m),k=r.slice(m,x===-1?void 0:x);Cn.test(k)&&(k=" "),(x>-1&&s+p.length>=0||k!==" ")&&p.push({type:"text",content:k})}}),n},stringify:function(r){return r.reduce(function(e,t){return e+Nt("",t)},"")}},Tt=jn;var qe=(r,e)=>{if(!r)return!1;let t=r.props?.children??r.children;return e?t.length>0:!!t},ze=r=>{if(!r)return[];let e=r.props?.children??r.children;return r.props?.i18nIsDynamicList?se(e):e},In=r=>Array.isArray(r)&&r.every(F.isValidElement),se=r=>Array.isArray(r)?r:[r],$n=(r,e)=>{let t={...e};return t.props=Object.assign(r.props,e.props),t},Je=(r,e)=>{if(!r)return"";let t="",n=se(r),i=e?.transSupportBasicHtmlNodes?e.transKeepBasicHtmlNodesFor??[]:[];return n.forEach((s,o)=>{if(M(s))t+=`${s}`;else if((0,F.isValidElement)(s)){let{props:a,type:u}=s,l=Object.keys(a).length,p=i.indexOf(u)>-1,d=a.children;if(!d&&p&&!l)t+=`<${u}/>`;else if(!d&&(!p||l)||a.i18nIsDynamicList)t+=`<${o}></${o}>`;else if(p&&l===1&&M(d))t+=`<${u}>${d}</${u}>`;else{let c=Je(d,e);t+=`<${o}>${c}</${o}>`}}else if(s===null)fe("Trans: the passed in value is invalid - seems you passed in a null child.");else if(X(s)){let{format:a,...u}=s,l=Object.keys(u);if(l.length===1){let p=a?`${l[0]}, ${a}`:l[0];t+=`{{${p}}}`}else fe("react-i18next: the passed in object contained more than one variable - the object should look like {{ value, format }} where format is optional.",s)}else fe("Trans: the passed in value is invalid - seems you passed in a variable like {number} - please pass in variables for interpolation as full objects like {{number}}.",s)}),t},Dn=(r,e,t,n,i,s)=>{if(e==="")return[];let o=n.transKeepBasicHtmlNodesFor||[],a=e&&new RegExp(o.map(x=>`<${x}`).join("|")).test(e);if(!r&&!a&&!s)return[e];let u={},l=x=>{se(x).forEach(S=>{M(S)||(qe(S)?l(ze(S)):X(S)&&!(0,F.isValidElement)(S)&&Object.assign(u,S))})};l(r);let p=Tt.parse(`<0>${e}</0>`),d={...u,...i},c=(x,k,S)=>{let A=ze(x),O=g(A,k.children,S);return In(A)&&O.length===0||x.props?.i18nIsDynamicList?A:O},m=(x,k,S,A,O)=>{x.dummy?(x.children=k,S.push((0,F.cloneElement)(x,{key:A},O?void 0:k))):S.push(...F.Children.map([x],w=>{let y={...w.props};return delete y.i18nIsDynamicList,(0,F.createElement)(w.type,{...y,key:A,ref:w.ref},O?null:k)}))},g=(x,k,S)=>{let A=se(x);return se(k).reduce((w,y,N)=>{let I=y.children?.[0]?.content&&t.services.interpolator.interpolate(y.children[0].content,d,t.language);if(y.type==="tag"){let U=A[parseInt(y.name,10)];S.length===1&&!U&&(U=S[0][y.name]),U||(U={});let j=Object.keys(y.attrs).length!==0?$n({props:y.attrs},U):U,f=(0,F.isValidElement)(j),h=f&&qe(y,!0)&&!y.voidElement,L=a&&X(j)&&j.dummy&&!f,R=X(r)&&Object.hasOwnProperty.call(r,y.name);if(M(j)){let E=t.services.interpolator.interpolate(j,d,t.language);w.push(E)}else if(qe(j)||h){let E=c(j,y,S);m(j,E,w,N)}else if(L){let E=g(A,y.children,S);m(j,E,w,N)}else if(Number.isNaN(parseFloat(y.name)))if(R){let E=c(j,y,S);m(j,E,w,N,y.voidElement)}else if(n.transSupportBasicHtmlNodes&&o.indexOf(y.name)>-1)if(y.voidElement)w.push((0,F.createElement)(y.name,{key:`${y.name}-${N}`}));else{let E=g(A,y.children,S);w.push((0,F.createElement)(y.name,{key:`${y.name}-${N}`},E))}else if(y.voidElement)w.push(`<${y.name} />`);else{let E=g(A,y.children,S);w.push(`<${y.name}>${E}</${y.name}>`)}else if(X(j)&&!f){let E=y.children[0]?I:null;E&&w.push(E)}else m(j,I,w,N,y.children.length!==1||!I)}else if(y.type==="text"){let U=n.transWrapTextNodes,j=s?n.unescape(t.services.interpolator.interpolate(y.content,d,t.language)):t.services.interpolator.interpolate(y.content,d,t.language);U?w.push((0,F.createElement)(U,{key:`${y.name}-${N}`},j)):w.push(j)}return w},[])},P=g([{dummy:!0,children:r||[]}],p,se(r||[]));return ze(P[0])};function Xe({children:r,count:e,parent:t,i18nKey:n,context:i,tOptions:s={},values:o,defaults:a,components:u,ns:l,i18n:p,t:d,shouldUnescape:c,...m}){let g=p||q();if(!g)return ie("You will need to pass in an i18next instance by using i18nextReactModule"),r;let P=d||g.t.bind(g)||(f=>f),x={...re(),...g.options?.react},k=l||P.ns||g.options?.defaultNS;k=M(k)?[k]:k||["translation"];let S=Je(r,x),A=a||S||x.transEmptyNodeValue||n,{hashTransKey:O}=x,w=n||(O?O(S||A):S||A);g.options?.interpolation?.defaultVariables&&(o=o&&Object.keys(o).length>0?{...o,...g.options.interpolation.defaultVariables}:{...g.options.interpolation.defaultVariables});let y=o||e!==void 0||!r?s.interpolation:{interpolation:{...s.interpolation,prefix:"#$?",suffix:"?$#"}},N={...s,context:i||s.context,count:e,...o,...y,defaultValue:A,ns:k},I=w?P(w,N):A;u&&Object.keys(u).forEach(f=>{let h=u[f];if(typeof h.type=="function"||!h.props||!h.props.children||I.indexOf(`${f}/>`)<0&&I.indexOf(`${f} />`)<0)return;function L(){return(0,F.createElement)(F.Fragment,null,h)}u[f]=(0,F.createElement)(L)});let U=Dn(u||r,I,g,x,N,c),j=t??x.defaultTransParent;return j?(0,F.createElement)(j,m,U):U}function Ct({children:r,count:e,parent:t,i18nKey:n,context:i,tOptions:s={},values:o,defaults:a,components:u,ns:l,i18n:p,t:d,shouldUnescape:c,...m}){let{i18n:g,defaultNS:P}=(0,Rt.useContext)(Y)||{},x=p||g||q(),k=d||x?.t.bind(x);return Xe({children:r,count:e,parent:t,i18nKey:n,context:i,tOptions:s,values:o,defaults:a,components:u,ns:l||k?.ns||P||x?.options?.defaultNS,i18n:x,t:d,shouldUnescape:c,...m})}b();v();var At=K(J(),1);b();v();b();v();var jt=K(J(),1);b();v();var Hn=K(J(),1);b();v();var Un=K(J(),1);b();v();b();v();b();v();function We(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}b();v();$t();function Dt(r,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,Ye(n.key),n)}}function Ze(r,e,t){return e&&Dt(r.prototype,e),t&&Dt(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}var Ht=[],Vn=Ht.forEach,Kn=Ht.slice;function _n(r){return Vn.call(Kn.call(arguments,1),function(e){if(e)for(var t in e)r[t]===void 0&&(r[t]=e[t])}),r}var Ft=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/,qn=function(e,t,n){var i=n||{};i.path=i.path||"/";var s=encodeURIComponent(t),o=e+"="+s;if(i.maxAge>0){var a=i.maxAge-0;if(isNaN(a))throw new Error("maxAge should be a Number");o+="; Max-Age="+Math.floor(a)}if(i.domain){if(!Ft.test(i.domain))throw new TypeError("option domain is invalid");o+="; Domain="+i.domain}if(i.path){if(!Ft.test(i.path))throw new TypeError("option path is invalid");o+="; Path="+i.path}if(i.expires){if(typeof i.expires.toUTCString!="function")throw new TypeError("option expires is invalid");o+="; Expires="+i.expires.toUTCString()}if(i.httpOnly&&(o+="; HttpOnly"),i.secure&&(o+="; Secure"),i.sameSite){var u=typeof i.sameSite=="string"?i.sameSite.toLowerCase():i.sameSite;switch(u){case!0:o+="; SameSite=Strict";break;case"lax":o+="; SameSite=Lax";break;case"strict":o+="; SameSite=Strict";break;case"none":o+="; SameSite=None";break;default:throw new TypeError("option sameSite is invalid")}}return o},Ut={create:function(e,t,n,i){var s=arguments.length>4&&arguments[4]!==void 0?arguments[4]:{path:"/",sameSite:"strict"};n&&(s.expires=new Date,s.expires.setTime(s.expires.getTime()+n*60*1e3)),i&&(s.domain=i),document.cookie=qn(e,encodeURIComponent(t),s)},read:function(e){for(var t=e+"=",n=document.cookie.split(";"),i=0;i<n.length;i++){for(var s=n[i];s.charAt(0)===" ";)s=s.substring(1,s.length);if(s.indexOf(t)===0)return s.substring(t.length,s.length)}return null},remove:function(e){this.create(e,"",-1)}},zn={name:"cookie",lookup:function(e){var t;if(e.lookupCookie&&typeof document<"u"){var n=Ut.read(e.lookupCookie);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupCookie&&typeof document<"u"&&Ut.create(t.lookupCookie,e,t.cookieMinutes,t.cookieDomain,t.cookieOptions)}},Jn={name:"querystring",lookup:function(e){var t;if(typeof self<"u")for(var n=self.location.search.substring(1),i=n.split("&"),s=0;s<i.length;s++){var o=i[s].indexOf("=");if(o>0){var a=i[s].substring(0,o);a===e.lookupQuerystring&&(t=i[s].substring(o+1))}}return t}},ce=null,Bt=function(){if(ce!==null)return ce;try{ce=self!=="undefined"&&self.localStorage!==null;var e="i18next.translate.boo";self.localStorage.setItem(e,"foo"),self.localStorage.removeItem(e)}catch{ce=!1}return ce},Xn={name:"localStorage",lookup:function(e){var t;if(e.lookupLocalStorage&&Bt()){var n=self.localStorage.getItem(e.lookupLocalStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupLocalStorage&&Bt()&&self.localStorage.setItem(t.lookupLocalStorage,e)}},pe=null,Mt=function(){if(pe!==null)return pe;try{pe=self!=="undefined"&&self.sessionStorage!==null;var e="i18next.translate.boo";self.sessionStorage.setItem(e,"foo"),self.sessionStorage.removeItem(e)}catch{pe=!1}return pe},Wn={name:"sessionStorage",lookup:function(e){var t;if(e.lookupSessionStorage&&Mt()){var n=self.sessionStorage.getItem(e.lookupSessionStorage);n&&(t=n)}return t},cacheUserLanguage:function(e,t){t.lookupSessionStorage&&Mt()&&self.sessionStorage.setItem(t.lookupSessionStorage,e)}},Gn={name:"navigator",lookup:function(e){var t=[];if(typeof navigator<"u"){if(navigator.languages)for(var n=0;n<navigator.languages.length;n++)t.push(navigator.languages[n]);navigator.userLanguage&&t.push(navigator.userLanguage),navigator.language&&t.push(navigator.language)}return t.length>0?t:void 0}},Qn={name:"htmlTag",lookup:function(e){var t,n=e.htmlTag||(typeof document<"u"?document.documentElement:null);return n&&typeof n.getAttribute=="function"&&(t=n.getAttribute("lang")),t}},Yn={name:"path",lookup:function(e){var t;if(typeof self<"u"){var n=self.location.pathname.match(/\/([a-zA-Z-]*)/g);if(n instanceof Array)if(typeof e.lookupFromPathIndex=="number"){if(typeof n[e.lookupFromPathIndex]!="string")return;t=n[e.lookupFromPathIndex].replace("/","")}else t=n[0].replace("/","")}return t}},Zn={name:"subdomain",lookup:function(e){var t;if(typeof self<"u"){var n=self.location.href.match(/(?:http[s]*\:\/\/)*(.*?)\.(?=[^\/]*\..{2,5})/gi);n instanceof Array&&(typeof e.lookupFromSubdomainIndex=="number"?t=n[e.lookupFromSubdomainIndex].replace("http://","").replace("https://","").replace(".",""):t=n[0].replace("http://","").replace("https://","").replace(".",""))}return t}};function er(){return{order:["querystring","cookie","localStorage","sessionStorage","navigator","htmlTag"],lookupQuerystring:"lng",lookupCookie:"i18next",lookupLocalStorage:"i18nextLng",lookupSessionStorage:"i18nextLng",caches:["localStorage"],excludeCacheFor:["cimode"]}}var Vt=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};We(this,r),this.type="languageDetector",this.detectors={},this.init(e,t)}return Ze(r,[{key:"init",value:function(t){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},i=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};this.services=t,this.options=_n(n,this.options||{},er()),this.options.lookupFromUrlIndex&&(this.options.lookupFromPathIndex=this.options.lookupFromUrlIndex),this.i18nOptions=i,this.addDetector(zn),this.addDetector(Jn),this.addDetector(Xn),this.addDetector(Wn),this.addDetector(Gn),this.addDetector(Qn),this.addDetector(Yn),this.addDetector(Zn)}},{key:"addDetector",value:function(t){this.detectors[t.name]=t}},{key:"detect",value:function(t){var n=this;t||(t=this.options.order);var i=[];return t.forEach(function(s){if(n.detectors[s]){var o=n.detectors[s].lookup(n.options);o&&typeof o=="string"&&(o=[o]),o&&(i=i.concat(o))}}),this.services.languageUtils.getBestMatchFromCodes?i:i.length>0?i[0]:null}},{key:"cacheUserLanguage",value:function(t,n){var i=this;n||(n=this.options.caches),n&&(this.options.excludeCacheFor&&this.options.excludeCacheFor.indexOf(t)>-1||n.forEach(function(s){i.detectors[s]&&i.detectors[s].cacheUserLanguage(t,i.options)}))}}]),r}();Vt.type="languageDetector";var tr=Vt;b();v();b();v();function et(r){"@babel/helpers - typeof";return et=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},et(r)}var Kt=[],Fs=Kt.forEach,Us=Kt.slice;function tt(){return typeof XMLHttpRequest=="function"||(typeof XMLHttpRequest>"u"?"undefined":et(XMLHttpRequest))==="object"}function nr(r){return!!r&&typeof r.then=="function"}function _t(r){return nr(r)?r:Promise.resolve(r)}b();v();var Te=K(Xt(),1);function Wt(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable})),t.push.apply(t,n)}return t}function Gt(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?Wt(Object(t),!0).forEach(function(n){rr(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):Wt(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function rr(r,e,t){return e=ir(e),e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function ir(r){var e=sr(r,"string");return Z(e)=="symbol"?e:e+""}function sr(r,e){if(Z(r)!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var n=t.call(r,e||"default");if(Z(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}function Z(r){"@babel/helpers - typeof";return Z=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Z(r)}var z;typeof fetch=="function"&&(typeof self<"u"&&self.fetch||typeof self<"u"&&self.fetch?z=self.fetch:z=fetch);var he;tt()&&(typeof self<"u"&&self.XMLHttpRequest||typeof self<"u"&&self.XMLHttpRequest)&&(he=self.XMLHttpRequest);var Re;typeof ActiveXObject=="function"&&(typeof self<"u"&&self.ActiveXObject||typeof self<"u"&&self.ActiveXObject)&&(Re=self.ActiveXObject);!z&&Te&&!he&&!Re&&(z=Te.default||Te);typeof z!="function"&&(z=void 0);var rt=function(e,t){if(t&&Z(t)==="object"){var n="";for(var i in t)n+="&"+encodeURIComponent(i)+"="+encodeURIComponent(t[i]);if(!n)return e;e=e+(e.indexOf("?")!==-1?"&":"?")+n.slice(1)}return e},Qt=function(e,t,n,i){var s=function(u){if(!u.ok)return n(u.statusText||"Error",{status:u.status});u.text().then(function(l){n(null,{status:u.status,data:l})}).catch(n)};if(i){var o=i(e,t);if(o instanceof Promise){o.then(s).catch(n);return}}typeof fetch=="function"?fetch(e,t).then(s).catch(n):z(e,t).then(s).catch(n)},Yt=!1,or=function(e,t,n,i){e.queryStringParams&&(t=rt(t,e.queryStringParams));var s=Gt({},typeof e.customHeaders=="function"?e.customHeaders():e.customHeaders);typeof self>"u"&&typeof self<"u"&&typeof self.process<"u"&&self.process.versions&&self.process.versions.node&&(s["User-Agent"]="i18next-http-backend (node/".concat(self.process.version,"; ").concat(self.process.platform," ").concat(self.process.arch,")")),n&&(s["Content-Type"]="application/json");var o=typeof e.requestOptions=="function"?e.requestOptions(n):e.requestOptions,a=Gt({method:n?"POST":"GET",body:n?e.stringify(n):void 0,headers:s},Yt?{}:o),u=typeof e.alternateFetch=="function"&&e.alternateFetch.length>=1?e.alternateFetch:void 0;try{Qt(t,a,i,u)}catch(l){if(!o||Object.keys(o).length===0||!l.message||l.message.indexOf("not implemented")<0)return i(l);try{Object.keys(o).forEach(function(p){delete a[p]}),Qt(t,a,i,u),Yt=!0}catch(p){i(p)}}},ar=function(e,t,n,i){n&&Z(n)==="object"&&(n=rt("",n).slice(1)),e.queryStringParams&&(t=rt(t,e.queryStringParams));try{var s;he?s=new he:s=new Re("MSXML2.XMLHTTP.3.0"),s.open(n?"POST":"GET",t,1),e.crossDomain||s.setRequestHeader("X-Requested-With","XMLHttpRequest"),s.withCredentials=!!e.withCredentials,n&&s.setRequestHeader("Content-Type","application/x-www-form-urlencoded"),s.overrideMimeType&&s.overrideMimeType("application/json");var o=e.customHeaders;if(o=typeof o=="function"?o():o,o)for(var a in o)s.setRequestHeader(a,o[a]);s.onreadystatechange=function(){s.readyState>3&&i(s.status>=400?s.statusText:null,{status:s.status,data:s.responseText})},s.send(n)}catch(u){console&&console.log(u)}},ur=function(e,t,n,i){if(typeof n=="function"&&(i=n,n=void 0),i=i||function(){},z&&t.indexOf("file:")!==0)return or(e,t,n,i);if(tt()||typeof ActiveXObject=="function")return ar(e,t,n,i);i(new Error("No fetch and no xhr implementation found!"))},Zt=ur;function ae(r){"@babel/helpers - typeof";return ae=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},ae(r)}function en(r,e){var t=Object.keys(r);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(r);e&&(n=n.filter(function(i){return Object.getOwnPropertyDescriptor(r,i).enumerable})),t.push.apply(t,n)}return t}function it(r){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?en(Object(t),!0).forEach(function(n){nn(r,n,t[n])}):Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(t)):en(Object(t)).forEach(function(n){Object.defineProperty(r,n,Object.getOwnPropertyDescriptor(t,n))})}return r}function lr(r,e){if(!(r instanceof e))throw new TypeError("Cannot call a class as a function")}function tn(r,e){for(var t=0;t<e.length;t++){var n=e[t];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(r,rn(n.key),n)}}function fr(r,e,t){return e&&tn(r.prototype,e),t&&tn(r,t),Object.defineProperty(r,"prototype",{writable:!1}),r}function nn(r,e,t){return e=rn(e),e in r?Object.defineProperty(r,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):r[e]=t,r}function rn(r){var e=cr(r,"string");return ae(e)=="symbol"?e:e+""}function cr(r,e){if(ae(r)!="object"||!r)return r;var t=r[Symbol.toPrimitive];if(t!==void 0){var n=t.call(r,e||"default");if(ae(n)!="object")return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(r)}var pr=function(){return{loadPath:"/locales/{{lng}}/{{ns}}.json",addPath:"/locales/add/{{lng}}/{{ns}}",parse:function(t){return JSON.parse(t)},stringify:JSON.stringify,parsePayload:function(t,n,i){return nn({},n,i||"")},parseLoadPayload:function(t,n){},request:Zt,reloadInterval:typeof self<"u"?!1:60*60*1e3,customHeaders:{},queryStringParams:{},crossDomain:!1,withCredentials:!1,overrideMimeType:!1,requestOptions:{mode:"cors",credentials:"same-origin",cache:"default"}}},sn=function(){function r(e){var t=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};lr(this,r),this.services=e,this.options=t,this.allOptions=n,this.type="backend",this.init(e,t,n)}return fr(r,[{key:"init",value:function(t){var n=this,i=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},s=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{};if(this.services=t,this.options=it(it(it({},pr()),this.options||{}),i),this.allOptions=s,this.services&&this.options.reloadInterval){var o=setInterval(function(){return n.reload()},this.options.reloadInterval);ae(o)==="object"&&typeof o.unref=="function"&&o.unref()}}},{key:"readMulti",value:function(t,n,i){this._readAny(t,t,n,n,i)}},{key:"read",value:function(t,n,i){this._readAny([t],t,[n],n,i)}},{key:"_readAny",value:function(t,n,i,s,o){var a=this,u=this.options.loadPath;typeof this.options.loadPath=="function"&&(u=this.options.loadPath(t,i)),u=_t(u),u.then(function(l){if(!l)return o(null,{});var p=a.services.interpolator.interpolate(l,{lng:t.join("+"),ns:i.join("+")});a.loadUrl(p,o,n,s)})}},{key:"loadUrl",value:function(t,n,i,s){var o=this,a=typeof i=="string"?[i]:i,u=typeof s=="string"?[s]:s,l=this.options.parseLoadPayload(a,u);this.options.request(this.options,t,l,function(p,d){if(d&&(d.status>=500&&d.status<600||!d.status))return n("failed loading "+t+"; status code: "+d.status,!0);if(d&&d.status>=400&&d.status<500)return n("failed loading "+t+"; status code: "+d.status,!1);if(!d&&p&&p.message&&p.message.indexOf("Failed to fetch")>-1)return n("failed loading "+t+": "+p.message,!0);if(p)return n(p,!1);var c,m;try{typeof d.data=="string"?c=o.options.parse(d.data,i,s):c=d.data}catch{m="failed parsing "+t+" to json"}if(m)return n(m,!1);n(null,c)})}},{key:"create",value:function(t,n,i,s,o){var a=this;if(this.options.addPath){typeof t=="string"&&(t=[t]);var u=this.options.parsePayload(n,i,s),l=0,p=[],d=[];t.forEach(function(c){var m=a.options.addPath;typeof a.options.addPath=="function"&&(m=a.options.addPath(c,n));var g=a.services.interpolator.interpolate(m,{lng:c,ns:n});a.options.request(a.options,g,u,function(P,x){l+=1,p.push(P),d.push(x),l===t.length&&typeof o=="function"&&o(p,d)})})}}},{key:"reload",value:function(){var t=this,n=this.services,i=n.backendConnector,s=n.languageUtils,o=n.logger,a=i.language;if(!(a&&a.toLowerCase()==="cimode")){var u=[],l=function(d){var c=s.toResolveHierarchy(d);c.forEach(function(m){u.indexOf(m)<0&&u.push(m)})};l(a),this.allOptions.preload&&this.allOptions.preload.forEach(function(p){return l(p)}),u.forEach(function(p){t.allOptions.ns.forEach(function(d){i.read(p,d,"read",null,null,function(c,m){c&&o.warn("loading namespace ".concat(d," for language ").concat(p," failed"),c),!c&&m&&o.log("loaded namespace ".concat(d," for language ").concat(p),m),i.loaded("".concat(p,"|").concat(d),c,m)})})})}}}])}();sn.type="backend";var dr=sn;b();v();var st=class extends Error{constructor({key:e="UnknownError"}={}){let t=`localizedError${e}`,n=D.exists(t)?D.t(t):D.t("localizedErrorUnknownError");super(n),this.code=e,this.translationKey=t}};export{D as a,Sr as b,We as c,W as d,Ge as e,Ye as f,$t as g,Ze as h,tr as i,dr as j,Ve as k,Ct as l,Le as m,st as n};
//# sourceMappingURL=chunk-56SJOU6P.js.map
