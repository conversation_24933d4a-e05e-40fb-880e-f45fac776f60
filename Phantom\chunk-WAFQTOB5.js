import{L as e,P as l}from"./chunk-MZZEJ42N.js";import{ha as m}from"./chunk-ALUTR72U.js";import{a}from"./chunk-7X4NV6OJ.js";import{f,h as n,n as o}from"./chunk-3KENBVE7.js";n();o();var t=f(a()),u=(r,i)=>m(e(typeof r=="string"?Number(r):r),i),g=r=>typeof r.children>"u"?null:t.default.createElement(t.default.Fragment,null,u(r.children,r.format)," SOL");export{g as a};
//# sourceMappingURL=chunk-WAFQTOB5.js.map
