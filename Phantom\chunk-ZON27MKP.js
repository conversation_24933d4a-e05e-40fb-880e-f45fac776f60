import{h as f,n as _}from"./chunk-3KENBVE7.js";f();_();var y=new Error("timeout while waiting for mutex to become available"),k=new Error("mutex already locked"),v=new Error("request for lock canceled"),p=function(c,e,t,i){function u(r){return r instanceof t?r:new t(function(s){s(r)})}return new(t||(t=Promise))(function(r,s){function l(n){try{o(i.next(n))}catch(a){s(a)}}function h(n){try{o(i.throw(n))}catch(a){s(a)}}function o(n){n.done?r(n.value):u(n.value).then(l,h)}o((i=i.apply(c,e||[])).next())})},d=class{constructor(e,t=v){this._value=e,this._cancelError=t,this._weightedQueues=[],this._weightedWaiters=[]}acquire(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise((t,i)=>{this._weightedQueues[e-1]||(this._weightedQueues[e-1]=[]),this._weightedQueues[e-1].push({resolve:t,reject:i}),this._dispatch()})}runExclusive(e,t=1){return p(this,void 0,void 0,function*(){let[i,u]=yield this.acquire(t);try{return yield e(i)}finally{u()}})}waitForUnlock(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);return new Promise(t=>{this._weightedWaiters[e-1]||(this._weightedWaiters[e-1]=[]),this._weightedWaiters[e-1].push(t),this._dispatch()})}isLocked(){return this._value<=0}getValue(){return this._value}setValue(e){this._value=e,this._dispatch()}release(e=1){if(e<=0)throw new Error(`invalid weight ${e}: must be positive`);this._value+=e,this._dispatch()}cancel(){this._weightedQueues.forEach(e=>e.forEach(t=>t.reject(this._cancelError))),this._weightedQueues=[]}_dispatch(){var e;for(let t=this._value;t>0;t--){let i=(e=this._weightedQueues[t-1])===null||e===void 0?void 0:e.shift();if(!i)continue;let u=this._value,r=t;this._value-=t,t=this._value+1,i.resolve([u,this._newReleaser(r)])}this._drainUnlockWaiters()}_newReleaser(e){let t=!1;return()=>{t||(t=!0,this.release(e))}}_drainUnlockWaiters(){for(let e=this._value;e>0;e--)this._weightedWaiters[e-1]&&(this._weightedWaiters[e-1].forEach(t=>t()),this._weightedWaiters[e-1]=[])}},m=function(c,e,t,i){function u(r){return r instanceof t?r:new t(function(s){s(r)})}return new(t||(t=Promise))(function(r,s){function l(n){try{o(i.next(n))}catch(a){s(a)}}function h(n){try{o(i.throw(n))}catch(a){s(a)}}function o(n){n.done?r(n.value):u(n.value).then(l,h)}o((i=i.apply(c,e||[])).next())})},w=class{constructor(e){this._semaphore=new d(1,e)}acquire(){return m(this,void 0,void 0,function*(){let[,e]=yield this._semaphore.acquire();return e})}runExclusive(e){return this._semaphore.runExclusive(()=>e())}isLocked(){return this._semaphore.isLocked()}waitForUnlock(){return this._semaphore.waitForUnlock()}release(){this._semaphore.isLocked()&&this._semaphore.release()}cancel(){return this._semaphore.cancel()}};export{w as a};
//# sourceMappingURL=chunk-ZON27MKP.js.map
