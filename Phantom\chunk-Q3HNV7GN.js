import{a as S,b as E,f as c,g as _,h as b,i as K,j as v,k,m as d,n as $,p as G,q as W,r as x,s as N,u as q,v as D}from"./chunk-LURFXJDV.js";import{ca as F}from"./chunk-MZZEJ42N.js";import{o as w,q as Q}from"./chunk-ALUTR72U.js";import{ia as L,ka as u}from"./chunk-L3A2KHJO.js";import{a as M}from"./chunk-7X4NV6OJ.js";import{f as I,h as r,n as o}from"./chunk-3KENBVE7.js";r();o();var C=I(M());r();o();var y=(s=!1,e,n)=>{if(e?.status===n.status)return;let t,a="";switch(n.status){case"connected":t="\u{1F7E2} Connected",a=`, selected app: ${n.selectedApp?$(n.selectedApp.app):"None"}`;break;case"needs-permission":t="\u{1F7E0} Needs Permission";break;case"reconnecting":t="\u{1F7E0} Reconnecting";break;case"waiting-for-approval":t="\u{1F7E0} Waiting For Approval";break;case"not-connected":t="\u26AA\uFE0F Not Connected";break}d(s,`${t}${a}`)};r();o();var R=()=>{let s=c.isLedgerSupported();return u({queryKey:s,queryFn:async()=>D().isSupported()})};var U=5,H=500,V=(s=!0,e=!1,n=2e3)=>{let t=L(),[a,h]=(0,C.useState)(n),p=(0,C.useCallback)(()=>{d(e,"disconnect event received from Ledger transport."),K(t)},[e,t]);(0,C.useEffect)(()=>("hid"in self.navigator&&self.navigator.hid.addEventListener("disconnect",p),()=>"hid"in self.navigator&&self.navigator.hid.removeEventListener("disconnect",p)),[p]);let{data:l=!1}=R(),f=s&&l,T=c.ledgerTransportState();return u({enabled:f,queryKey:T,queryFn:async()=>{if(!f)return S;let i=t.getQueryData(T),g,B=!1,A=0;do{try{g=(await Promise.all([Q(N(q(),e,A>0?E:i),n),w(200)]))[0]}catch{g=S}B=!!i?.isConnected&&g.status==="not-connected"&&++A<U,B&&(v(t,E),d(e,"Attempting to reconnect..."),A===1&&y(e,i,E),await w(H))}while(B);return A>0&&(i=E),y(e,i,g),i?.isConnected&&!g.isConnected&&_(t).then(()=>b(t)),h(g.isConnected?3500:n),g},refetchOnMount:!0,refetchInterval:a,initialData:S})};r();o();var X=(s=!0,e=!1)=>{let n=q(),t=L(),a=c.requestLedgerPermission();return u({enabled:s,queryKey:a,queryFn:async()=>{let p;try{d(e,"Requesting permission to connect to Ledger..."),p=await n.request()}catch(f){if(d(e,`Received error trying to request permission: ${f}`),typeof f.message=="string"&&f.message?.includes("device is already open")){d(e,"\u2705 Permission granted, device already open. Connecting to device...");let T=c.ledgerTransportState(),O=await t.getQueryData(T),i;try{i=await N(n,e,O)}catch{}return i?.isConnected?(d(e,`\u2705 Permission granted. Connected to Ledger device: ${i.transport.deviceModel?.productName??"Unknown device"}. Selected app: ${i?.selectedApp?.app??"none"}`),await v(t,i),y(e,void 0,i),{type:"granted",transport:i.transport}):(d(e,"\u274C Unable to connect. Try again and make sure device is unlocked."),{type:"unable-to-connect"})}return d(e,"\u274C Permission denied."),{type:"denied"}}if(!p)return d(e,"\u274C Unable to connect to Ledger device. Permission granted, but no Transport received."),{type:"unable-to-connect"};let l={status:"connected",isConnected:!0,transport:p,selectedApp:await x(p,n.defaultTransportTimeout)};return await v(t,l),y(e,void 0,l),d(e,`\u2705 Permission granted. Connected to Ledger device: ${p.deviceModel?.productName??"Unknown device"}`),{type:"granted",transport:p}},gcTime:0,staleTime:0,refetchOnMount:!0})};r();o();r();o();r();o();var j=(s,e=[],n=!1,t=!1,a=()=>{})=>{let{defaultTransportTimeout:h}=D(),p=L(),l=G(F.getAddressType(e[0].pathType)),f=s.isConnected&&e.length>0&&l===s.selectedApp?.app,T=c.ledgerAddresses(e,t);return u({enabled:f,queryKey:T,queryFn:async()=>{let i=await p.getQueryData(c.ledgerTransportState());if(!i)throw new k;return W({transportTimeout:h,state:i,derivationPathParams:e,enableDebugLogs:n,enableLedgerXPubDerivation:t,onSuccess:a})},retry:!1})};r();o();r();o();r();o();var P=I(M());function J(s,e){return s=Math.ceil(s),e=Math.floor(e),Math.floor(Math.random()*(e-s)+s)}var Y=s=>{let e=[];for(let n=1;n<=s;++n){let t=J(0,3),a;t===0?a=0:t===1?a=-70:a=-100,e.push({name:`Nano X ${n}`,id:`${n}`,rssi:a})}return e.sort((n,t)=>t.rssi-n.rssi)},vt=Y(100);r();o();export{R as a,j as b,V as c,X as d};
//# sourceMappingURL=chunk-Q3HNV7GN.js.map
