{"commandAdd": "<PERSON><PERSON>", "commandAccept": "<PERSON><PERSON><PERSON>", "commandApply": "<PERSON><PERSON>", "commandApprove": "<PERSON><PERSON><PERSON>", "commandAllow": "<PERSON><PERSON><PERSON>", "commandBack": "Laghazi", "commandBuy": "<PERSON><PERSON><PERSON>", "commandCancel": "<PERSON><PERSON><PERSON><PERSON>", "commandClaim": "Zọta", "commandClaimReward": "Zọta ihe nrite gị", "commandClear": "<PERSON><PERSON><PERSON>", "commandClose": "<PERSON><PERSON><PERSON>", "commandConfirm": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>", "commandConnect": "Jikọọ", "commandContinue": "Gaa n'ihu", "commandConvert": "Gbanweta", "commandCopy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandCopyAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "commandCopyTokenAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON> adrees<PERSON> tokin", "commandCreate": "Mepee", "commandCreateTicket": "<PERSON><PERSON><PERSON><PERSON>", "commandDeny": "<PERSON><PERSON><PERSON>", "commandDismiss": "<PERSON><PERSON><PERSON>", "commandDontAllow": "<PERSON>", "commandDownload": "<PERSON><PERSON><PERSON>", "commandEdit": "<PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON> onwe onye", "commandEnableNow": "Gbanye ugbua", "commandFilter": "<PERSON><PERSON><PERSON><PERSON>", "commandFollow": "<PERSON>ro", "commandHelp": "<PERSON><PERSON>ma<PERSON>", "commandLearnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandLearnMore2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandMint": "<PERSON><PERSON><PERSON><PERSON>", "commandMore": "Ọzọ", "commandNext": "Osote", "commandNotNow": "Ọ b<PERSON>gh<PERSON> ugbua", "commandOpen": "Mepee", "commandOpenSettings": "<PERSON><PERSON><PERSON>", "commandPaste": "<PERSON><PERSON>", "commandReceive": "<PERSON><PERSON><PERSON>", "commandReconnect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandRecordVideo": "See Onyonyo", "commandRequest": "Rịọ", "commandRetry": "<PERSON><PERSON><PERSON>", "commandReview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandRevoke": "<PERSON><PERSON><PERSON><PERSON>", "commandSave": "Chekwaa", "commandScanQRCode": "Nyocha Koodu QR", "commandSelect": "<PERSON><PERSON><PERSON><PERSON>", "commandSelectMedia": "<PERSON><PERSON><PERSON><PERSON> ngwa ozi", "commandSell": "<PERSON><PERSON>", "commandSend": "Zipụ", "commandShare": "<PERSON><PERSON>", "commandShowBalance": "<PERSON><PERSON>", "commandSign": "Nye nkwado", "commandSignOut": "Sign Out", "commandStake": "Nkuchi coin", "commandMintLST": "Biputa JitoSOL", "commandSwap": "Mgbanweta", "commandSwapAgain": "<PERSON><PERSON>", "commandTakePhoto": "See foto", "commandTryAgain": "Nwaa Ọzọ", "commandViewTransaction": "<PERSON><PERSON>", "commandReportAsNotSpam": "<PERSON><PERSON><PERSON> dịka nke na-abụghị ihe ezitere ana-ach<PERSON>gh<PERSON>", "commandReportAsSpam": "<PERSON><PERSON><PERSON> dịka nke ezitere ana-a<PERSON><PERSON>gh<PERSON>", "commandPin": "Jigide", "commandBlock": "Gbochie", "commandUnblock": "Gbaghe", "commandUnstake": "<PERSON><PERSON><PERSON> n<PERSON>", "commandUnpin": "<PERSON><PERSON><PERSON>", "commandHide": "Zoo", "commandUnhide": "<PERSON><PERSON><PERSON>", "commandBurn": "<PERSON><PERSON><PERSON>", "commandReport": "Kọọ", "commandView": "<PERSON><PERSON>", "commandProceedAnywayUnsafe": "N'agbanyeghi otú o sina dị, gaa n'ihu (nọ n'ihe ize ndụ)", "commandUnfollow": "Kwụsị iso", "commandUnwrap": "<PERSON><PERSON><PERSON>ụ", "commandConfirmUnsafe": "<PERSON><PERSON><PERSON><PERSON> (nọ n'ihe ize ndụ)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, mee n<PERSON><PERSON><PERSON><PERSON> (nọ n'ihe ize ndụ)", "commandConfirmAnyway": "Kwado n'agban<PERSON>ghị", "commandReportIssue": "<PERSON><PERSON> mk<PERSON>a nsogbu", "commandSearch": "Chọọ", "commandShowMore": "<PERSON><PERSON><PERSON><PERSON>", "commandShowLess": "<PERSON><PERSON> ngosi", "pastParticipleClaimed": "A zọtala", "pastParticipleCompleted": "E mechala", "pastParticipleCopied": "A <PERSON>ọpịala", "pastParticipleDone": "E mechaala", "pastParticipleDisabled": "A gbanyụọla", "pastParticipleRequested": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "nounName": "<PERSON>a", "nounNetwork": "Netwọkụ", "nounNetworkFee": "Ego ụgwọ Nzipụ Ego", "nounSymbol": "<PERSON><PERSON> njirima<PERSON>", "nounType": "Ụdịrị", "nounDescription": "Nkọwa", "nounYes": "Ee", "nounNo": "Mba", "amount": "Ọnụego", "limit": "Njedebe", "new": "Ọhụrụ", "gotIt": "<PERSON><PERSON><PERSON><PERSON> ya", "internal": "N'ime", "reward": "<PERSON>he nrite", "seeAll": "<PERSON><PERSON> ha nile", "seeLess": "<PERSON><PERSON> ngosi", "viewAll": "<PERSON><PERSON><PERSON><PERSON> ha niile", "homeTab": "Ụlọ", "collectiblesTab": "<PERSON><PERSON> onwunwe digitalụ", "swapTab": "Mgbanweta", "activityTab": "<PERSON>he o<PERSON>e", "exploreTab": "<PERSON>be nchọta ihe", "accountHeaderConnectedInterpolated": "Ejik<PERSON>la g<PERSON> na {{origin}}", "accountHeaderConnectedToSite": "Ejikọla gị na saiti a", "accountHeaderCopyToClipboard": "Kọpị<PERSON> na k<PERSON>ụ", "accountHeaderNotConnected": "Ejikoghị gị na", "accountHeaderNotConnectedInterpolated": "<PERSON>jik<PERSON><PERSON><PERSON> gị na {{origin}}", "accountHeaderNotConnectedToSite": "Ejikọghị gị na saịtị a", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "Enweghi SOL zuru ezu", "accountWithoutEnoughSolSecondaryText": "Akaụnt<PERSON> so na nzipụ ego a e nweghị SOL zuru ezu. Akaụntụ ahụ nwere ike ịbụ nke gị maọbụ nke onye ọzọ. Nzipụ ego a ga alaghachị ma ọbụrụ ma ezipụ ya.", "accountSwitcher": "<PERSON><PERSON>", "addAccountHardwareWalletPrimaryText": "Jikọọ Ngwa akpaego ", "addAccountHardwareWalletSecondaryText": "<PERSON><PERSON> ngwa nchekwa akpaego Ledger gị", "addAccountHardwareWalletSecondaryTextMobile": "<PERSON><PERSON> {{supportedHardwareWallets}} akpaego gị", "addAccountSeedVaultWalletPrimaryText": "Jikọọ Ebe Ndebe Akpaego", "addAccountSeedVaultWalletSecondaryText": "Si na Ebe Ndebe Akpaego were akpaego", "addAccountImportSeedPhrasePrimaryText": "<PERSON><PERSON><PERSON>", "addAccountImportSeedPhraseSecondaryText": "Si n'akpaego <PERSON> bubata akaụnt<PERSON> ha", "addAccountImportWalletPrimaryText": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> n<PERSON> m<PERSON>ye", "addAccountImportWalletSecondaryText": "<PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> nwere otu chenụ", "addAccountImportWalletSolanaSecondaryText": "Bụbata nọmba n<PERSON>zo mbanye nke Solana", "addAccountLimitReachedText": "I ruola {{accountsCount}} ngw<PERSON><PERSON> akaụnt<PERSON> na Phantom. <PERSON><PERSON> akaụntụ ndị a na-ejighị eme ihe tupu itinye ndị ọzọ.", "addAccountNoSeedAvailableText": "I nwegh<PERSON> mkp<PERSON><PERSON><PERSON><PERSON> mkp<PERSON><PERSON>. <PERSON><PERSON> buba<PERSON><PERSON> nwetaghachi\nnke dịbu iji wee kepụta aka<PERSON><PERSON>.", "addAccountNewWalletPrimaryText": "<PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "addAccountNewWalletSecondaryText": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON> d<PERSON>", "addAccountNewMultiChainWalletSecondaryText": "Tinye akaụnt<PERSON> nwere ọtụtụ chenụ", "addAccountNewSingleChainWalletSecondaryText": "<PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "addAccountPrimaryText": "Tinye/Jikọọ Akpaego", "addAccountSecretPhraseLabel": "<PERSON><PERSON>", "addAccountSeedLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addAccountSeedIDLabel": "<PERSON><PERSON><PERSON>ra mkp<PERSON><PERSON><PERSON><PERSON>", "addAccountSecretPhraseDefaultLabel": "{{number}} <PERSON><PERSON><PERSON><PERSON><PERSON> ", "addAccountPrivateKeyDefaultLabel": "{{number}} Akara Mbanye Nzuzo", "addAccountZeroAccountsForSeed": "akaụntụ 0", "addAccountShowAccountForSeed": "Gosipụta 1 akaụntụ", "addAccountShowAccountsForSeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{numOfAccounts}}", "addAccountHideAccountForSeed": "Zoo 1 akaụntụ", "addAccountHideAccountsForSeed": "Zoo akaụntụ {{numOfAccounts}}", "addAccountSelectSeedDescription": "A ga-esite na Okwu <PERSON>zuzo a mepụta akaụntụ <PERSON>ị", "addAccountNumAccountsForSeed": "akaụntụ {{numOfAccounts}}", "addAccountOneAccountsForSeed": "1 akaụnt<PERSON>", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON><PERSON>", "addAccountReadOnly": "<PERSON><PERSON>", "addAccountReadOnlySecondaryText": "<PERSON><PERSON><PERSON> ad<PERSON> a<PERSON>", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Adreesị EVM", "addAccountBitcoinAddress": "Adreesị Bitcoin", "addAccountCreateSeedTitle": "<PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "addAccountCreateSeedExplainer": "Akpaego gị enwebeghị okwu nzuzo! Iji mepee akpa<PERSON>, anyị ga-emepụtara gị okwu nnwetaghachị. Detuo ya ma mee ka onye ọzọ ghara ịma.", "addAccountSecretPhraseHeader": "<PERSON><PERSON> Nzuzo Gị", "addAccountNoSecretPhrases": "<PERSON> nwegh<PERSON>wu <PERSON> dị", "addAccountImportAccountActionButtonImport": "Bụbata", "addAccountImportAccountDuplicatePrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON> a dịbụ adị na akpaego gị", "addAccountImportAccountIncorrectFormat": "<PERSON><PERSON> nhazi ya ezighi ezi", "addAccountImportAccountInvalidPrivateKey": "Nọmba Nzuzo <PERSON>ak<PERSON>ghị", "addAccountImportAccountName": "<PERSON>a", "addAccountImportAccountPrimaryText": "Bubata Nọmba N<PERSON>", "addAccountImportAccountPrivateKey": "Nọmba Nzu<PERSON>", "addAccountImportAccountPublicKey": "Adreesị maọbụ Domain", "addAccountImportAccountPrivateKeyRequired": "Achọrọ nọmba n<PERSON>zo m<PERSON>ye", "addAccountImportAccountNameRequired": "A chọrọ aha ", "addAccountImportAccountPublicKeyRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> adrees<PERSON> oha", "addAccountImportAccountDuplicateAddress": "<PERSON><PERSON><PERSON><PERSON> a dịbụ adị n'akpaego gị", "addAddressAddressAlreadyAdded": "<PERSON><PERSON><PERSON><PERSON>", "addAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON>", "addAddressAddressInvalid": "<PERSON><PERSON><PERSON><PERSON>", "addAddressAddressIsRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>ị", "addAddressLabelIsRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> aha ad<PERSON>si a<PERSON>", "addAddressLabelPlaceholder": "<PERSON><PERSON> ad<PERSON><PERSON> a<PERSON>", "addAddressPrimaryText": "<PERSON><PERSON>", "addAddressToast": "<PERSON> <PERSON><PERSON> adrees<PERSON>", "createAssociatedTokenAccountCostLabelInterpolated": "Nke a ga-ewe SOL {{solAmountFormatted}}", "createAssociatedTokenAccountErrorAccountExists": "Ị nwe<PERSON><PERSON> aka<PERSON><PERSON><PERSON> tokin a", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON><PERSON> e<PERSON> ezu", "createAssociatedTokenAccountErrorInvalidMint": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> tokin a dabaghị", "createAssociatedTokenAccountErrorInvalidName": "<PERSON>a a dabagh<PERSON>", "createAssociatedTokenAccountErrorInvalidSymbol": "Akara njirimara a dabaghị", "createAssociatedTokenAccountErrorUnableToCreateMessage": "<PERSON><PERSON> e kepụtanwughị akaụnt<PERSON> tokin gị. <PERSON><PERSON>.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> a gaghị", "createAssociatedTokenAccountErrorUnableToSendMessage": "Anyị e zipụ nwụghị ego gị.", "createAssociatedTokenAccountErrorUnableToSendTitle": "<PERSON>zi<PERSON><PERSON> ego a gaghị", "createAssociatedTokenAccountInputPlaceholderMint": "<PERSON><PERSON><PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderName": "<PERSON>a", "createAssociatedTokenAccountInputPlaceholderSymbol": "<PERSON><PERSON> njirima<PERSON>", "createAssociatedTokenAccountLoadingMessage": "<PERSON><PERSON> n'emepe akan<PERSON>ụ tokin gị.", "createAssociatedTokenAccountLoadingTitle": "A na-emepe aka<PERSON><PERSON><PERSON>kin", "createAssociatedTokenAccountPageHeader": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountSuccessMessage": "<PERSON><PERSON><PERSON> aka<PERSON><PERSON> tokin gị gara nke <PERSON>!", "createAssociatedTokenAccountSuccessTitle": "E mepela aka<PERSON><PERSON><PERSON> tokin", "createAssociatedTokenAccountViewTransaction": "<PERSON><PERSON> ego", "assetDetailRecentActivity": "<PERSON><PERSON>", "assetDetailStakeSOL": "Kuchie SOL ruo nwa oge", "assetDetailUnknownToken": "<PERSON><PERSON> ana-am<PERSON>", "assetDetailUnwrapAll": "<PERSON><PERSON><PERSON>", "assetDetailUnwrappingSOL": "Iwepu SOL", "assetDetailUnwrappingSOLFailed": "Mgbanwe SOL a gaghị", "assetDetailViewOnExplorer": "<PERSON><PERSON> {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Ebenchọta data", "assetDetailSaveToPhotos": "Chekwaa na Foto gasị", "assetDetailSaveToPhotosToast": "Echekwara na Foto gasị", "assetDetailPinCollection": "<PERSON><PERSON><PERSON> nile dị na dịgịtalụ otu ebe", "assetDetailUnpinCollection": "<PERSON><PERSON> njigide E<PERSON>ese nile dị n'dịg<PERSON> n'otu ebe", "assetDetailHideCollection": "Zoo Eserese nile dị na dịgịtalu", "assetDetailUnhideCollection": "<PERSON><PERSON><PERSON> n<PERSON>zo Eserese nile dị na dịgịtalu", "assetDetailTokenNameLabel": "<PERSON><PERSON>", "assetDetailNetworkLabel": "Netwọkụ", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON>ị", "assetDetailPriceLabel": "Ọnụahịa", "collectibleDetailSetAsAvatar": "<PERSON><PERSON> d<PERSON>", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Nchikọta Avatar", "collectibleDetailShare": "<PERSON><PERSON><PERSON> onwunwe <PERSON>", "assetDetailTokenAddressCopied": "A kopịala adreesị", "assetDetailStakingLabel": "Ikuchi coin ruo nwa oge", "assetDetailAboutLabel": "Banyere {{fungibleName}}", "assetDetailPriceDetail": "Nkọwa Ọnụahịa", "assetDetailHighlights": "<PERSON><PERSON> ndị kacha mma", "assetDetailAllTimeReturn": "<PERSON>he mweta nke <PERSON>", "assetDetailAverageCost": "Ọnụ ah<PERSON><PERSON>", "assetDetailPriceHistoryUnavailable": "Ọnụahịa ndị dịbụ na mbụ adịghị maka tokịn a", "assetDetailPriceHistoryInsufficientData": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON> maka oge a", "assetDetailPriceDataUnavailable": "<PERSON><PERSON><PERSON>", "assetDetailPriceHistoryError": "Njehie na nweta ọnụahịa ndị dịbụ na mbụ", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "OtuỤbọchị", "assetDetailTimeFrame24h": "Ọnụ ahịa 24h", "assetDetailTimeFrame1W": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailTimeFrame1M": "<PERSON><PERSON>ọn<PERSON>", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "HA NIILE", "sendAssetAmountLabelInterpolated": "{{amount}} {{tokenSymbol}} Dị", "fiatRampQuotes": "Ọnụego", "fiatRampNewQuote": "Ọnụego <PERSON><PERSON><PERSON><PERSON>ụ", "assetListSelectToken": "<PERSON><PERSON><PERSON><PERSON>", "assetListSearch": "Chọọ...", "assetListUnknownToken": "<PERSON><PERSON> ana-am<PERSON>", "buyFlowHealthWarning": "Ụfọdụ n'ime ndị ọkwụ ụgwọ anyị n'enwe nnukwu ọnụọgụgụ mmadụ na webụsaịtị ha. Ntinye ego nwere ike iwe ọtụtụ awa.", "assetVisibilityUnknownToken": "<PERSON><PERSON> ana-am<PERSON>", "buyAssetInterpolated": "Zụta {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "Ọnụego kachasịnụ a ga azụta ya bụ {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Ọnụego kacha ọn<PERSON>ala a ga azụta ya bụ {{amount}}", "buyNoAssetsAvailable": "E nweghị ihe onwunwe nke Ethereum maọbụ Polygon dịnụ", "buyThirdPartyScreenPaymentMethodSelector": "Kwụọ ụgwọ site n'iji", "buyThirdPartyScreenPaymentMethod": "<PERSON><PERSON><PERSON><PERSON> usoro nkwụnye <PERSON>w<PERSON>", "buyThirdPartyScreenChoseQuote": "Tinye ego ole dabara maka <PERSON>", "buyThirdPartyScreenProviders": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodTitle": "Ụzọ Ịkwụ ụgwọ", "buyThirdPartyScreenPaymentMethodEmptyState": "Enweghị usoro nkwụnye <PERSON>gwọ ọbụla dị maka mpaghara obibi gị", "buyThirdPartyScreenPaymentMethodFooter": "Ọ bụ otu njikọ netwọk na-ahazi akwụmụgwọ. Ego ụgwọ nwere ike ị dị ịche. Ụzọ i ji kwụ ụgwọ ụfọdụ adịghị maka mpaghara ebe ị bi.", "buyThirdPartyScreenProvidersEmptyState": "<PERSON> nwegh<PERSON> eben<PERSON>h<PERSON>a dị maka mpaghara obibi gị", "buyThirdPartyScreenLoadingQuote": "N'ebupụta ọnụegọ...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON>", "gasEstimationErrorWarning": "E nwere nsogbu dị na nhazi ụgwọ a gakwụ maka nzipu ego a. O nwere ike ọ gaghị aga.", "gasEstimationCouldNotFetch": "E nweghị ike nweta ụgwọ a ga-akwụ maka nzipu ego", "networkFeeCouldNotFetch": "E nweghị ike ị nweta ego ụgwọ nzipụ ego", "nativeTokenBalanceErrorWarning": "E nwere nsogbu dị n'ịnweta tokịn gị fọdụrụ maka nzipu ego a. O nwere ike ọ gaghị aga.", "blocklistOriginCommunityDatabaseInterpolated": "Akaala saịtị a dịka otu n'ime <1> database nke otu igwe mmadụ emere ka ọ na arụ ọrụ nke ọma</1> nke amaara dịka weebusait na-eji aghụgh<PERSON> anakọta ihe ọmụma ndị mmadụ nakwa egwu wayo. Ọ bụrụ na ị chere na akara weebusaiti a na njehie, <3> biko tinye akwụkwọ mkpesa</3>.", "blocklistOriginDomainIsBlocked": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{domainName}}!", "blocklistOriginIgnoreWarning": "<PERSON><PERSON><PERSON> aka n'nti a <PERSON>a, kp<PERSON>r<PERSON> m gaa na {{domainName}} n'agban<PERSON>gh<PERSON>.", "blocklistOriginSiteIsMalicious": "Phantom kwenyere na weebusaịtị a ga emebi ihe nakwa ọ dịghị mma iji.", "blocklistOriginThisDomain": "domain a", "blocklistProceedAnyway": "<PERSON><PERSON><PERSON> aka na ntị anya, ga n'ihu na agbanyegh<PERSON>", "maliciousTransactionWarning": "Phantom kwenyer<PERSON> na nzipụ ego coin a bụ nke ana enyeghi ikike nakwa ọ dịghị mma iji. <PERSON><PERSON> egbochiela ikike ya ibanye iji chekwaa gị nakwa ego coin gị.", "maliciousTransactionWarningIgnoreWarning": "<PERSON><PERSON><PERSON> aka n'<PERSON>ti anya, gaa n'ihu n'ag<PERSON><PERSON><PERSON>", "maliciousTransactionWarningTitle": "<PERSON><PERSON> nzipụ ego coin a dịka nke ana-enyo enyo!", "maliciousRequestBlockedTitle": "<PERSON><PERSON><PERSON><PERSON>", "maliciousRequestWarning": "E fetuola webụsaịtị a dịka nke nwere ike imebi ihe. O nwere ike bụrụ na ọ chọrọ izuru ego gị ma ọ bụ duhie gị ka ịkwado arịrịọ <PERSON>.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON>.", "maliciousRequestBlocked": "<PERSON><PERSON>, <PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>.", "maliciousRequestFrictionDescription": "Ịga n'ihu nọ n'ihe ize ndụ, ya mere Phantom a<PERSON><PERSON><PERSON> arịrịọ a. Ị ga-emechi mkpar<PERSON>ta <PERSON>ka a.", "maliciousRequestAcknowledge": "A ghọtara m na ego m nile nwere ike ifunahu m site na-eji weeb<PERSON> a.", "maliciousRequestAreYouSure": "Obi o siri gị ike?", "siwErrorPopupTitle": "<PERSON><PERSON><PERSON> aka <PERSON><PERSON>", "siwParseErrorDescription": "<PERSON><PERSON><PERSON><PERSON> egosi m<PERSON> aka a<PERSON><PERSON> anara n'ihi na nhazi ya amak<PERSON>gh<PERSON>.", "siwVerificationErrorDescription": "E nwere njehie 1 maọbụ karịa dị na ozi mbinye aka anara. <PERSON><PERSON> n<PERSON>kwa gị, biko gbaa mbo hụ na ị na e ji aapụ ziri ezi ma nwaa ọzọ.", "siwErrorPagination": "{{n}} nke {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Ịdọ aka n'nti: adreesị aapụ na adreesị enyere amak<PERSON>ghị maka mbanye.", "siwErrorMessage_DOMAIN_MISMATCH": "Ịdọ aka n'nti: domain nke aapụ na nke enyere amak<PERSON>gh<PERSON> maka eme nkwa<PERSON>ez<PERSON>.", "siwErrorMessage_URI_MISMATCH": "Ịdọ aka n'ntị: Aha URL na domain amakọghị.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Ịdọ aka n'ntị: Chaịnụ ID na Chainụ ID enyere amak<PERSON>ghgị maka eme nkwadoez<PERSON>.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Ịdọ aka n'ntị: deetị enyere ozi dị ezigbo anya n'oge gara aga.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Ịdọ aka n'nti: deetị enyere ozi dị ezigbo anya n'ọdịnịhụ.", "siwErrorMessage_EXPIRED": "Ịdọ aka n'ntị: oge ozi a<PERSON>.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Ịdọ aka n'ntị: oge ozi aga<PERSON> tupu nyefe.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Ịdọ aka n'ntị: oge ozi ga agafe tupu aga-anabata ya.", "siwErrorShowErrorDetails": "<PERSON><PERSON> n<PERSON><PERSON> n<PERSON>e", "siwErrorHideErrorDetails": "Zọọ nkọwa njehie", "siwErrorIgnoreWarning": "<PERSON><PERSON><PERSON> aka n'<PERSON>ti anya, gaa n'ihu n'ag<PERSON><PERSON><PERSON>", "siwsTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "siwsPermissions": "<PERSON><PERSON><PERSON>", "siwsAgreement": "<PERSON><PERSON>", "siwsAdvancedDetails": "Nkọwa e mere n'Ọdịnịhụ", "siwsAlternateStatement": "{{domain}} ch<PERSON><PERSON><PERSON> ka <PERSON> jiri aka<PERSON> gị banye: {{address}}", "siwsFieldLable_domain": "Domain", "siwsFieldLable_address": "<PERSON><PERSON><PERSON>ị", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "<PERSON><PERSON><PERSON>", "siwsFieldLable_chainId": "<PERSON><PERSON><PERSON><PERSON>", "siwsFieldLable_nonce": "Nọmba ana-eji naan<PERSON> otu ug<PERSON>", "siwsFieldLable_issuedAt": "<PERSON><PERSON><PERSON>", "siwsFieldLable_expirationTime": "Ga-akwụsị <PERSON>", "siwsFieldLable_requestId": "Rịọ maka ID", "siwsFieldLable_resources": "<PERSON><PERSON>", "siwsVerificationErrorDescription": "Ar<PERSON><PERSON><PERSON><PERSON> nbanye a amak<PERSON>ghị. Nke a nwere ike <PERSON>p<PERSON>ta na saịtị ahụ adịghị mma, ma<PERSON><PERSON><PERSON> onye mepụtara ya mere njehie mgbe ọ na-eziga arịr<PERSON><PERSON> ahụ.", "siwsErrorNumIssues": "{{n}} nsogbu", "siwsErrorMessage_CHAIN_ID_MISMATCH": "<PERSON><PERSON><PERSON><PERSON> a na netwọk ị nọ na ya agakọghị.", "siwsErrorMessage_DOMAIN_MISMATCH": "Domain a abụghị nke ị na-abanye na ya.", "siwsErrorMessage_URI_MISMATCH": "URL a abụghị nke ị na-abanye na ya.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Deetị enyere ozi dị ezigbo anya n'oge gara aga.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Deetị enyere ozi tere ezigbo anya n'ọdịn<PERSON>.", "siwsErrorMessage_EXPIRED": "<PERSON><PERSON> ozi agefeela.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "<PERSON>ge ozi agafeela tupu enye ya.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Oge ozi ga-agafe tupu aga-anabata ya.", "changeLockTimerPrimaryText": "<PERSON><PERSON><PERSON> Oge Mgbanyu nke Ak<PERSON>aka", "changeLockTimerSecondaryText": "<PERSON><PERSON><PERSON> oge ole ka anyị ga-echere iji kuchie akpaego gị ka ọ nọchara nkiti?", "changeLockTimerToast": "E melitela nhazi oge mgban<PERSON>ụ n'ak<PERSON>aka", "changePasswordConfirmNewPassword": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> passw<PERSON><PERSON><PERSON><PERSON>", "changePasswordCurrentPassword": "Passwọọ<PERSON><PERSON> ugbu a", "changePasswordErrorIncorrectCurrentPassword": "Passwọọdụ ugbu a e<PERSON>hi ezi", "changePasswordErrorGeneric": "E nwere ihe na e<PERSON>hi ezi, biko nwaa ọzọ ma emecha", "changePasswordNewPassword": "Pass<PERSON><PERSON><PERSON><PERSON><PERSON>", "changePasswordPrimaryText": "<PERSON><PERSON><PERSON><PERSON> pass<PERSON><PERSON>", "changePasswordToast": "E melitela passwọọdụ", "collectionsSpamCollections": "Eserese ndị dị na dịgịtalu <PERSON> ana-<PERSON><PERSON><PERSON>", "collectionsHiddenCollections": "Eserese nile dị na dịgịtalu Ezoro ezo", "collectiblesReportAsSpam": "<PERSON><PERSON><PERSON> dịka nke <PERSON>e ana-a<PERSON><PERSON><PERSON>", "collectiblesReportAsSpamAndHide": "<PERSON><PERSON><PERSON> dịka nke <PERSON>e ana-a<PERSON>ọghị ma Zoo", "collectiblesReportAsNotSpam": "<PERSON><PERSON>ọ dịka nke na <PERSON>ụghị ihe Ezitere ana-ach<PERSON>gh<PERSON>", "collectiblesReportAsNotSpamAndUnhide": "<PERSON><PERSON><PERSON> nzuzo ma kọọ na ọ bụghị nke ezitere ana-achọghị", "collectiblesReportNotSpam": "Ọbụghị ihe Ezitere ana-a<PERSON>", "collectionsManageCollectibles": "Jik<PERSON><PERSON> ngosip<PERSON> ihe onwunwe dịg<PERSON> maka orire", "collectibleDetailDescription": "Nkọwa", "collectibleDetailProperties": "<PERSON><PERSON>", "collectibleDetailOrdinalInfo": "<PERSON><PERSON>", "collectibleDetailRareSatsInfo": "<PERSON><PERSON> Rare Sats", "collectibleDetailSatsInUtxo": "Sats na UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Nọmba Sat", "collectibleDetailSatName": "<PERSON><PERSON>", "collectibleDetailInscriptionId": "ID Data etinyere", "collectibleDetailInscriptionNumber": "Nọmba <PERSON>tinyer<PERSON>", "collectibleDetailStandard": "Ụkpụrụ", "collectibleDetailCreated": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailViewOnExplorer": "<PERSON><PERSON> {{explorer}}", "collectibleDetailList": "<PERSON><PERSON><PERSON><PERSON><PERSON> eserese maka orire", "collectibleDetailSellNow": "<PERSON><PERSON> n'ọnụego {{amount}}{{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Nyepụ Bitcoin fọ<PERSON><PERSON><PERSON>", "collectibleDetailUtxoSplitterCtaSubtitle": "Ị nwere {{value}} BTC ịga a<PERSON>poghe", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "<PERSON><PERSON> ch<PERSON> ego gị, anyị na egbochi BTC dị na UTXOs yana Rare Sats ka aghara izipụ ya. <PERSON><PERSON> ihe nkewa Magic Eden UTXP nyepụ {{value}} BTC site na Rare Sats gị.", "collectibleDetailUtxoSplitterModalCtaButton": "<PERSON><PERSON> UTXO", "collectibleDetailEasilyAccept": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "E zoro ihe onwunwe dịg<PERSON>talụ a ezo n'ihi na Phantom chere na ọ bụ nke e zitere ana-achọgh<PERSON>.", "collectibleDetailSpamOverlayReveal": "<PERSON><PERSON> onwunwe <PERSON>", "collectibleBurnTermsOfService": "<PERSON><PERSON><PERSON><PERSON> m na enweghị ike emeghachị nke a", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON><PERSON>", "collectibleBurnTitleWithCount_other": "<PERSON><PERSON><PERSON>", "collectibleBurnDescriptionWithCount_one": "<PERSON>me nke a ga-ebibi ma wepụ kwa tokin a pụọ n'akpaego gị ruo ebighi ebi.", "collectibleBurnDescriptionWithCount_other": "<PERSON>me nke a ga ebibi ma wepụ kwa tokin ndịa pụ<PERSON> n'akpaego gị ruo ebighi ebi.", "collectibleBurnTokenWithCount_one": "<PERSON><PERSON>", "collectibleBurnTokenWithCount_other": "Tokin gasị", "collectibleBurnCta": "<PERSON><PERSON><PERSON>", "collectibleBurnRebate": "<PERSON><PERSON><PERSON>ach<PERSON> ego", "collectibleBurnRebateTooltip": "A ga etinye obere SOL na akpaego gị na mmereonwe maka iwepụ tokin a pụọ na nkesa.", "collectibleBurnNetworkFee": "Ego ụgw<PERSON> Nzipụ ego", "collectibleBurnNetworkFeeTooltip": "Ọnụego netwọkụ Solana chọrọ maka nzipụ ego", "unwrapButtonSwapTo": "<PERSON><PERSON><PERSON><PERSON> {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Si na {{withdrawalSource}} d<PERSON><PERSON><PERSON>, maka {{chainSymbol}}", "unwrapModalEstimatedTime": "<PERSON><PERSON>", "unwrapModalNetwork": "Netwọkụ", "unwrapModalNetworkFee": "Ego ụgwọ Nzipụ Ego", "unwrapModalTitle": "Nchịkọta", "unsupportedChain": "<PERSON><PERSON>", "unsupportedChainDescription": "Ọ dị ka anyị akwadoghị {{action}} maka netwọk {{chainName}}.", "networkFeesTooltipLabel": "Ụgwọ Netwọk {{chainName}}", "networkFeesTooltipDescription": "Ụgwọ {{chainName}} na-a<PERSON><PERSON>o ụkwụ n'ọtụtụ ihe were a gbanwe. Ị nwere ike <PERSON>hazi ha iji mee ka nzipụ ego gị n'aga ngwa ngwa (Nke kacha gala ọnụ) ma ọ bụ ka ọ na-aga nwayọ nwayọ (Nke dị ọnụala).", "burnStatusErrorTitleWithCount_one": "<PERSON><PERSON> e<PERSON> mbibi", "burnStatusErrorTitleWithCount_other": "<PERSON><PERSON> e<PERSON> mbibi", "burnStatusSuccessTitleWithCount_one": "<PERSON><PERSON><PERSON><PERSON><PERSON> tokin!", "burnStatusSuccessTitleWithCount_other": "Ewepụla tokin gasị!", "burnStatusLoadingTitleWithCount_one": "<PERSON><PERSON><PERSON> tokin...", "burnStatusLoadingTitleWithCount_other": "<PERSON><PERSON><PERSON> tokin...", "burnStatusErrorMessageWithCount_one": "<PERSON><PERSON>wu<PERSON><PERSON> ike bibie tokin a. <PERSON><PERSON> nwaa ọzọ ma emecha.", "burnStatusErrorMessageWithCount_other": "<PERSON><PERSON><PERSON><PERSON><PERSON> ike bibie tokin ndịa. <PERSON><PERSON> nwaa <PERSON>zọ ma emechaa.", "burnStatusSuccessMessageWithCount_one": "<PERSON><PERSON><PERSON><PERSON> tokin a ruo ebighi ebi ma tinye kwa {{rebateAmount}} SOL n'ime akpaego gị.", "burnStatusSuccessMessageWithCount_other": "<PERSON><PERSON><PERSON><PERSON> tokin ndịa ruo ebighi ebi ma <PERSON>e kwa {{rebateAmount}} SOL n'ime akpaego gị.", "burnStatusLoadingMessageWithCount_one": "A na-ebibi tokin a ruo ebighi ebi, ma ga-etin<PERSON>kwa {{rebateAmount}} SOL n'ime akpaego gị.", "burnStatusLoadingMessageWithCount_other": "A na-ebibi tokin ndị a ruo ebighi ebi, ma ga-etin<PERSON>kwa {{rebateAmount}} SOL n'ime akpaego gị.", "burnStatusViewTransactionText": "<PERSON><PERSON> ego", "collectibleDisplayLoading": "Na-e<PERSON><PERSON>a...", "collectiblesNoCollectibles": "<PERSON><PERSON><PERSON><PERSON> ihe onwunwe d<PERSON>", "collectiblesPrimaryText": "<PERSON><PERSON> onwu<PERSON>we d<PERSON> gị", "collectiblesReceiveCollectible": "<PERSON><PERSON><PERSON> onwunwe <PERSON>", "collectiblesUnknownCollection": "<PERSON><PERSON> eserese dị na dịg<PERSON>u ana-amagh<PERSON>", "collectiblesUnknownCollectible": "<PERSON><PERSON> eserese dị na dịg<PERSON> an<PERSON>-<PERSON>", "collectiblesUniqueHolders": "Nọmba Ndị nwe tokin", "collectiblesSupply": "Inyefe", "collectiblesUnknownTokens": "<PERSON><PERSON> ana-am<PERSON>", "collectiblesNrOfListed": "{{ nrOfListed }} Egosipụtara eserese maka orire", "collectiblesListed": "<PERSON><PERSON><PERSON><PERSON><PERSON> eserese maka orire", "collectiblesMintCollectible": "Bipụta Ihe onwunwe digitalụ", "collectiblesYouMint": "Ị ga-ebip<PERSON>ta", "collectiblesMintCost": "Ọnụego <PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesMintFail": "<PERSON><PERSON><PERSON><PERSON><PERSON> a gagh<PERSON>", "collectiblesMintFailMessage": "E nwere nsogbu dap<PERSON> n'ibipụta ihe onwunwe digitalụ gị. Biko n<PERSON>a ọzọ.", "collectiblesMintCostFree": "N'efu", "collectiblesMinting": "Na-e<PERSON><PERSON><PERSON><PERSON>...", "collectiblesMintingMessage": "A na-ebipụta ihe onwunwe digitalụ gị", "collectiblesMintShareSubject": "Lelegodi ihe a", "collectiblesMintShareMessage": "<PERSON><PERSON><PERSON><PERSON> m nke a na @phantom!", "collectiblesMintSuccess": "Mbip<PERSON><PERSON> gara nke <PERSON>", "collectiblesMintSuccessMessage": "E bipụtala ihe onwunwe digitalụ gị", "collectiblesMintSuccessQuestMessage": "Ị mezuola ihe ndị achọrọ iji sonyere Ihe omume Phantom. P<PERSON>a <PERSON> ihe nrite gị iji nweta ihe onwunwe digitalụ efu gị.", "collectiblesMintRequired": "A chọrọ", "collectiblesMintMaxLengthErrorMessage": "<PERSON><PERSON><PERSON><PERSON> og<PERSON> ana<PERSON>an<PERSON>", "collectiblesMintSafelyDismiss": "Ị nwere ike imechi windo a n'enweghị nsogbu.", "collectiblesTrimmed": "Anyị eruola na njedebe maka nọmba ihe onwunwe dịg<PERSON> e nwere ike igosip<PERSON>ta ugbu a.", "collectiblesNonTransferable": "Nke a na-Enweghí Ike Inyefe", "collectiblesNonTransferableYes": "Ee", "collectiblesSellOfferDetails": "<PERSON>i banyere ihe orire a", "collectiblesSellYouSell": "Ị ga-ere", "collectiblesSellGotIt": "<PERSON><PERSON><PERSON><PERSON> ya", "collectiblesSellYouReceive": "Ị Ga <PERSON>", "collectiblesSellOffer": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSoldCollectible": "<PERSON><PERSON> onwu<PERSON>we d<PERSON> eregoro", "collectiblesSellMarketplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesSellCollectionFloor": "Ọnụego kacha dị ala", "collectiblesSellDifferenceFromFloor": "Ndịiche dị n'ọnụegọ kacha dị ala", "collectiblesSellLastSalePrice": "Ego ole e rere na mgbe gara a ga", "collectiblesSellEstimatedFees": "Ụgwọ a tụrụ anya ya", "collectiblesSellEstimatedProfitAndLoss": "<PERSON><PERSON>/Ọghọm A tụrụ anya ya", "collectiblesSellViewOnMarketplace": "<PERSON> na {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "Ọnụego '<PERSON><PERSON><PERSON><PERSON> ug<PERSON>' kacha dị ala na ihe eserese dí na digitalụ gazuo ọtụtụ ebe nzụta ahịa.", "collectiblesSellProfitLossTooltip": "A na-ag<PERSON>o <PERSON>ụ n'ego ikpeazụ e rere, nakwa n'ụgwọ kacha ala a kpọsara were agbakọ Uru/<PERSON>gh<PERSON><PERSON> a tụr<PERSON> anya.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "{{royaltiesPercentage}} Ego nrite", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "{{marketplaceFeePercentage}} Ụgwọ <PERSON><PERSON> n<PERSON><PERSON> ah<PERSON>a", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Ego ụgwọ <PERSON>", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Netwọk {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Ọnụego gụnyere {{phantomFeePercentage}} ego a ga-akwụ Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Ọnuego g<PERSON><PERSON><PERSON>, Ụgwọ <PERSON><PERSON><PERSON><PERSON>, Ugw<PERSON> n<PERSON> ah<PERSON>, nakwa {{phantomFeePercentage}} ego a ga-akwụ Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "Ọnụego g<PERSON><PERSON><PERSON>, Ụgwọ <PERSON><PERSON><PERSON><PERSON>, na Ụgwọ Ebe n<PERSON><PERSON>ta ah<PERSON>a", "collectiblesSellTransactionFeeTooltipTitle": "Ụgwọ a ga-akwụ maka nzipụ ego", "collectiblesSellStatusLoadingTitle": "A ka na-anabata ikwe ọnụ ahịa...", "collectiblesSellStatusLoadingIsSellingFor": "na-ere n'ọn<PERSON>", "collectiblesSellStatusSuccessTitle": "E rela {{collectibleName}}!", "collectiblesSellStatusSuccessWasSold": "e rere ya n'ọnụ ego", "collectiblesSellStatusErrorTitle": "E nwere ihe na ezighi ezi", "collectiblesSellStatusErrorSubtitle": "E nwere nsogbu dị<PERSON> chọrọ ire", "collectiblesSellStatusViewTransaction": "<PERSON><PERSON>", "collectiblesSellInsufficientFundsTitle": "<PERSON><PERSON> ezughi ezu", "collectiblesSellInsufficientFundsSubtitle": "Anyị enweghị ike nabata ekwem ọnụ ahịa n'eserese digitalụ a, maka na enweghị ego zuru iji kwụọ ụgwụ netwọk.", "collectiblesSellRecentlyTransferedNFTTitle": "Ezip<PERSON><PERSON><PERSON> n'oge na-adịgh<PERSON> anya", "collectiblesSellRecentlyTransferedNFTSubtitle": "Ị ga echerịrị otu awa iji nabata ekwemọnụ ahịa mgbe ezipụsịrị ego.", "collectiblesApproveCollection": "{{collectionName}} a kwadoro", "collectiblesSellNotAvailableAnymoreTitle": "Ọnụahịa a adịghị ya", "collectiblesSellNotAvailableAnymoreSubtitle": "Ọnụ-ah<PERSON><PERSON> ahụ adịgh<PERSON>z<PERSON> ya ọzọ. Kagbuo ek<PERSON><PERSON><PERSON>nụ ahịa a ma nwaa ọzọ", "collectiblesSellFlaggedTokenTitle": "A kaala ihe onwunwe dịg<PERSON> díka nke a na-enyo enyo", "collectiblesSellFlaggedTokenSubtitle": "Agagh<PERSON> enwe ike ire ihe onwunwe dịg<PERSON><PERSON> ah<PERSON>, nke a nwere ike <PERSON>ụ n'ihi ọtụtụ ihe dịka nke akọrọ na-ezuru ezu maọbụ akụchịrị na-ereghi ere", "collectiblesListOnMagicEden": "<PERSON><PERSON><PERSON><PERSON><PERSON> eserese na Magic Eden maka orire", "collectiblesListPrice": "Ọnụego Akwadoro", "collectiblesUseFloor": "<PERSON><PERSON> Ọnụala", "collectiblesFloorPrice": "Ọnụego kacha Ọnụala", "collectiblesLastSalePrice": "Ọnụego ana Ere ya Ikpeazụ", "collectiblesTotalReturn": "<PERSON><PERSON><PERSON><PERSON> ihe ni<PERSON>", "collectiblesOriginalPurchasePrice": "Ọnụego <PERSON><PERSON><PERSON><PERSON> ya", "collectiblesMagicEdenFee": "<PERSON>go n<PERSON>ụ Magic Eden", "collectiblesArtistRoyalties": "Pa<PERSON>", "collectiblesListNowButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesListAnywayButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> eserese maka orire n'Agbanyeghi", "collectiblesCreateListingTermsOfService": "Site na-ịkpatụ <1>'<PERSON><PERSON><PERSON><PERSON><PERSON> maka orire Ug<PERSON> a\"</1>ị ga ekwenye na Usoro Ọrụ <3>nke Magic Eden</3>", "collectiblesViewListing": "<PERSON><PERSON> eserese maka orire", "collectiblesListingViewTransaction": "<PERSON><PERSON>", "collectiblesRemoveListing": "<PERSON><PERSON><PERSON> eserese maka orire", "collectiblesEditListing": "<PERSON><PERSON> eserese maka orire", "collectiblesEditListPrice": "<PERSON><PERSON> Ọnụego Akwadoro", "collectiblesListPriceTooltip": "Ọnụego Akwadoro bụ onụego edowere ere eserese dịgịtalụ. Ndị na ere eserese na edowekarị Ọnụego Akwadoro ka ọ nọrọ na maọbụ karịa Ọnụego kacha Ọnụala.", "collectiblesFloorPriceTooltip": "Ọnụego kacha Ọnụala bụ Ọnụego Akwadoro kacha nọ n'ọrụ maka eserese dị na dịgịtalu.", "collectiblesOriginalPurchasePriceTooltip": "Ị zụtara ihe eserese dịg<PERSON>ụ a na ọnụegọ a.", "collectiblesPurchasedForSol": "<PERSON><PERSON><PERSON><PERSON> maka {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Enweghi ike e<PERSON> ngos<PERSON> eserese", "collectiblesUnableToLoadListingsFrom": "Enweghi ike e<PERSON> ngosip<PERSON>ta eserese si na {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON> eserese na ihe onwunwe gị nọ na nchekwa mana anyị enweghi ike ebụbata ha site na {{marketplace}} n'oge a.", "collectiblesBelowFloorPrice": "<PERSON><PERSON><PERSON> Ọnụego kacha Ọnụala", "collectiblesBelowFloorPriceMessage": "I ji n'aka na ị chọrọ egosipụta eserese NFT gị na ọnụego na erughi Ọnụego kacha Ọnụala?", "collectiblesMinimumListingPrice": "Ọnụego kacha <PERSON> bụ 0.01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden na ewere ego nzipu ihe mgbe azụchara ihe.", "collectiblesArtistRoyaltiesTooltip": "Onye mep<PERSON>tara ihe eserese a dị na dịgịtalụ ga-enweta pasent ego uru ahịa sitere na ihe ọ bụla o rere.", "collectibleScreenCollectionLabel": "<PERSON>he eserese nile dị na dịg<PERSON>", "collectibleScreenPhotosPermissionTitle": "Nnata ikike Foto gasị", "collectibleScreenPhotosPermissionMessage": "<PERSON><PERSON> chọrọ ikike gị inweta foto gị gasị. <PERSON>iko gaa na <PERSON> ma melite ikike gị.", "collectibleScreenPhotosPermissionOpenSettings": "<PERSON><PERSON><PERSON>", "listStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> nke Ọma", "editListStatusErrorTitle": "Enweghi ike etinye ozi <PERSON>", "removeListStatusErrorTitle": "<PERSON><PERSON><PERSON> eserese nke na <PERSON>ghị nke Ọma", "listStatusSuccessTitle": "Mepụtara <PERSON> esere<PERSON>!", "editListingStatusSuccessTitle": "<PERSON>tinyela oz<PERSON>ụ<PERSON>ụ na Ng<PERSON>ipụta esere<PERSON>!", "removeListStatusSuccessTitle": "Ewepụla ngosipụta eserese na Magic Eden", "listStatusLoadingTitle": "<PERSON>-<PERSON><PERSON><PERSON><PERSON> es<PERSON>...", "editListingStatusLoadingTitle": "Na-etinye ozi <PERSON> na ngosipụta eserese...", "removeListStatusLoadingTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>...", "listStatusErrorMessage": "Enwe<PERSON>i ike egos<PERSON><PERSON><PERSON><PERSON> esere<PERSON> {{name}} na Magic Eden", "removeListStatusErrorMessage": "<PERSON>we<PERSON>i ike etinye ngosip<PERSON>ta eserese {{name}} na Magic Eden", "listStatusSuccessMessage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> esere<PERSON> {{name}} na Magic Eden maka {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> {{name}} na Magic Eden maka {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> {{name}} na Magic Eden gara nke <PERSON>", "listStatusLoadingMessage": "Na-ego<PERSON><PERSON><PERSON><PERSON> esere<PERSON> {{name}} na Magic Eden maka {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Na-et<PERSON>ye oz<PERSON> {{name}} na Magic Eden maka {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Na-ewe<PERSON>ụ {{name}} na Magic Eden. Nke a nwere ike ewe oge.", "listStatusLoadingSafelyDismiss": "Ị nwere ike imechi windo a n'enweghi nsogbu.", "listStatusViewOnMagicEden": "<PERSON><PERSON> na Magic Eden", "listStatusViewOnMarketplace": "<PERSON> na {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON>", "listStatusViewTransaction": "<PERSON><PERSON>", "connectHardwareConnectedPrimaryText": "Akpa Nchekwa Ego USB", "connectHardwareConnectedSecondaryText": "Jikọọ ihe nchekwa akpaego gị ma gbaa mbo hụ na akpochighi ya. Ozugbo any<PERSON> chọp<PERSON> ya, ị nwere ike ihọrọ adreesi nke ị ga-achọ iji.", "connectHardwareFailedPrimaryText": "<PERSON><PERSON><PERSON>", "connectHardwareFailedSecondaryText": "<PERSON>iko jikọọ ihe nchekwa akpaego gị ma gbaa mbo hụ na akpochighi ya. Ozugbo <PERSON><PERSON> chọp<PERSON> ya, <PERSON> nwere ike ihọr<PERSON> adreesi ị ga-eji.", "connectHardwareFinishPrimaryText": "<PERSON>tinyela <PERSON>!", "connectHardwareFinishSecondaryText": "Ị ga enwe ike enweta Akpa Nchekwa Ego USB n'ime Phantom. <PERSON><PERSON> gaghach<PERSON> na ihe nchọgharị intaneti.", "connectHardwareNeedsPermissionPrimaryText": "<PERSON><PERSON><PERSON>", "connectHardwareNeedsPermissionSecondaryText": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> dị n'okpuru ka ibido usoro njiko.", "connectHardwareSearchingPrimaryText": "Na-<PERSON><PERSON><PERSON> a<PERSON>ego...", "connectHardwareSearchingSecondaryText": "<PERSON><PERSON><PERSON> ihe nchekwa a<PERSON> gị, gbaa mbo hụ na akpochigh<PERSON> ya, nakwa na ị nyela ikike na ihe nchọgharị gị.", "connectHardwarePermissionDeniedPrimary": "<PERSON><PERSON><PERSON><PERSON> inye ikike", "connectHardwarePermissionDeniedSecondary": "Nye Phantom ikike ijikọ na-akụrụngwa Ledger gị", "connectHardwarePermissionUnableToConnect": "Njikọ agaghị", "connectHardwarePermissionUnableToConnectDescription": "<PERSON><PERSON> ejikọnwugh<PERSON> n'akụrụngwa Ledger gị. Anyị nwere ike nwe mkpa nweta ikike ndị ọzọ.", "connectHardwareSelectAddressAllAddressesImported": "<PERSON><PERSON><PERSON><PERSON><PERSON> adreesi nile", "connectHardwareSelectAddressDerivationPath": "<PERSON><PERSON> nweta kii", "connectHardwareSelectAddressSearching": "Na-achọ...", "connectHardwareSelectAddressSelectWalletAddress": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON> a<PERSON>", "connectHardwareSelectAddressWalletAddress": "<PERSON><PERSON><PERSON>", "connectHardwareWaitingForApplicationSecondaryText": "<PERSON>iko jikoo ihe nchekwa ak<PERSON>ego gị ma gbaa mbo hụ na akpochịghị ya.", "connectHardwareWaitingForPermissionPrimaryText": "Chọrọ nnata ikike", "connectHardwareWaitingForPermissionSecondaryText": "<PERSON><PERSON><PERSON> ihe nchekwa a<PERSON> gị, gbaa mbo hụ na akpochigh<PERSON> ya, nakwa na ị nyela ikike na ihe nchọgharị gị.", "connectHardwareAddAccountButton": "<PERSON><PERSON>", "connectHardwareLedger": "Jikọọ Ledger gị", "connectHardwareStartConnection": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> dị n'okpuru i ji bido njikọ ngwa akpa nchekwa ego Ledger gị", "connectHardwarePairSuccessPrimary": "{{productName}} e ji<PERSON><PERSON><PERSON>", "connectHardwarePairSuccessSecondary": "Njikọ {{productName}} g<PERSON> gara nke <PERSON>.", "connectHardwareSelectChains": "H<PERSON>r<PERSON> chenụ a ga ejikọ", "connectHardwareSearching": "Na-achọ...", "connectHardwareMakeSureConnected": "Jikọọ ma kpọghe ngwa nchekwa akpaego gị. <PERSON><PERSON> nye igwe nchọgharị ihe ịntanetị ikike ndị kwesịrị.", "connectHardwareOpenAppDescription": "Biko kpọghe ngwa nchekwa akpaego gị", "connectHardwareConnecting": "Ọ na ejikọ...", "connectHardwareConnectingDescription": "<PERSON><PERSON> ejik<PERSON>wala na-akụrụngwa Ledger gị.", "connectHardwareConnectingAccounts": "A na-ejikọ akaụntụ gị ha...", "connectHardwareDiscoveringAccounts": "A na-chọgharị akaụnt<PERSON> ha...", "connectHardwareDiscoveringAccountsDescription": "<PERSON><PERSON> na-achọ ihe omume na-akaụntụ gị ha.", "connectHardwareErrorLedgerLocked": "<PERSON> <PERSON> mkpọchi", "connectHardwareErrorLedgerLockedDescription": "<PERSON><PERSON><PERSON> mbọ hụ na-emere Ledger g<PERSON> mkp<PERSON>, ma nwa<PERSON>.", "connectHardwareErrorLedgerGeneric": "E nwere ihe na ezighi ezi", "connectHardwareErrorLedgerGenericDescription": "A chọtanwughị aka<PERSON><PERSON><PERSON> ọ bụla. Gbaa mbọ hụ na akpọpere Ledger gị akpọpe, ma nwaa <PERSON>z<PERSON>.", "connectHardwareErrorLedgerPhantomLocked": "<PERSON><PERSON>a Phantom ma nwaa njik<PERSON> akụrụngwa g<PERSON>.", "connectHardwareFindingAccountsWithActivity": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aka<PERSON> {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "A na-achọ akaụnt<PERSON> {{chainName1}} ma <PERSON> bụ {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "<PERSON><PERSON> chọ<PERSON> aka<PERSON><PERSON> dị {{numOfAccounts}} nke nwere ihe omume na akpa ego gị.", "connectHardwareFoundAccountsWithActivitySingular": "Anyị hụtara 1 akaụnt<PERSON> nke nwere ihe omume na ngwa Ledger gị.", "connectHardwareFoundSomeAccounts": "<PERSON><PERSON> h<PERSON> akaụntụ na ngwa Ledger gị.", "connectHardwareViewAccounts": "<PERSON><PERSON> aka<PERSON><PERSON><PERSON>i", "connectHardwareConnectAccounts": "<PERSON> jik<PERSON><PERSON> aka<PERSON><PERSON><PERSON> ha", "connectHardwareSelectAccounts": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareChooseAccountsToConnect": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> akpa ego ị ga ejikọ.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} <PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareAccountsStepOfSteps": "Njem {{stepNum}} nke {{totalSteps}}", "connectHardwareMobile": "Jikọ<PERSON> Ledger", "connectHardwareMobileTitle": "Jikọọ akpaego Ledger gị", "connectHardwareMobileEnableBluetooth": "Gbanye igwe ozi blututu", "connectHardwareMobileEnableBluetoothDescription": "Nye ikike i ji were igwe ozi blututu jikọọ", "connectHardwareMobileEnableBluetoothSettings": "Gaa na Ebe Nhazi i ji nye <PERSON>, iji ebe nakwa Akụrụngwa Ndị nọ nso mee ihe.", "connectHardwareMobilePairWithDevice": "Jikọọ ya na akụrụngwa Ledger gị", "connectHardwareMobilePairWithDeviceDescription": "<PERSON><PERSON> a<PERSON> gị nso i ji nweta mgbaama kacha mma", "connectHardwareMobileConnectAccounts": "Jikọọ akaụntụ gasị", "connectHardwareMobileConnectAccountsDescription": "<PERSON>ị ga-achọ ihe omume na-akaụntụ ọ bụla ị nweburu", "connectHardwareMobileConnectLedgerDevice": "Jikọọ akpaego Ledger gị", "connectHardwareMobileLookingForDevices": "A na-achọ akụrụngwa ndị nọ nso...", "connectHardwareMobileLookingForDevicesDescription": "<PERSON><PERSON> jikọọ akụrụngwa Ledger gị ma gba mbọ hụ na emepere ya emepe.", "connectHardwareMobileFoundDeviceSingular": "<PERSON><PERSON> h<PERSON> 1 akpaego Ledger", "connectHardwareMobileFoundDevices": "<PERSON><PERSON> {{numDevicesFound}}", "connectHardwareMobileFoundDevicesDescription": "<PERSON><PERSON><PERSON><PERSON> otu a<PERSON>ego Ledger n'okpuru iji bido njikọ.", "connectHardwareMobilePairingWith": "A na-ejikọ ya na {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Soro ntụziaka dị na akụrụngwa Ledger gị mgbe ị n'eme njikọ.", "connectHardwareMobilePairingFailed": "Njik<PERSON> a gaghị", "connectHardwareMobilePairingFailedDescription": "E nweghị ike i jikọ ya na {{deviceName}}. Gba mbọ hụ na akpọpere akụrụngwa gị akpọpe.", "connectHardwareMobilePairingSuccessful": "Njikọ gara ọfụma", "connectHardwareMobilePairingSuccessfulDescription": "I jik<PERSON><PERSON>a ma jikọkwa akpaego Ledger g<PERSON>.", "connectHardwareMobileOpenAppSingleChain": "<PERSON><PERSON><PERSON> a<PERSON> {{chainName}} na-akpaego Ledger gị", "connectHardwareMobileOpenAppDualChain": "<PERSON><PERSON><PERSON> appụ {{chainName1}} ma<PERSON><PERSON><PERSON> {{chainName2}} n'akpaego Ledger gị", "connectHardwareMobileOpenAppDescription": "Gba mbọ hụ na e mepere akụrụngwa gị e mepe.", "connectHardwareMobileStillCantFindDevice": "Ị ka n'enwe nsogbu <PERSON>ch<PERSON>ta akụr<PERSON>ngwa gị?", "connectHardwareMobileLostConnection": "Njikọ a daala mba", "connectHardwareMobileLostConnectionDescription": "<PERSON><PERSON> a naghịzi enweta njikọ na {{deviceName}}. <PERSON><PERSON> gbaa mbọ hụ na akpọpere akụ<PERSON><PERSON><PERSON><PERSON> gị akpọpe, ma nwaa <PERSON>.", "connectHardwareMobileGenericLedgerDevice": "<PERSON><PERSON><PERSON><PERSON>ụ<PERSON><PERSON> Ledger", "connectHardwareMobileConnectDeviceSigning": "<PERSON><PERSON><PERSON><PERSON> {{deviceName}} gị", "connectHardwareMobileConnectDeviceSigningDescription": "<PERSON><PERSON><PERSON> a<PERSON>ego Ledger gị ma debe ya nso.", "connectHardwareMobileBluetoothDisabled": "A gbanyụrụ <PERSON> a gbanyụ", "connectHardwareMobileBluetoothDisabledDescription": "<PERSON><PERSON> gbanye blututu gị ma gba mbọ hụ na emepere akpaego Ledger gị emepe.", "connectHardwareMobileLearnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileBlindSigningDisabled": "A gbanyụr<PERSON> m<PERSON> aka digitalụ nke e zoro ozi ya a gbanyụ", "connectHardwareMobileBlindSigningDisabledDescription": "Gba mbọ hụ na-agbanyere m<PERSON> aka digitalụ nke e zoro ozi ya, na akụrụng<PERSON> g<PERSON>.", "connectHardwareMobileConfirmSingleChain": "Ị kwesịrị ị kwado nzipụ ego ahụ na ngwa nchekwa akpaego gị. Gba mbọ hụ na akpọpere ya akpọpe.", "metamaskExplainerBottomSheetHeader": "<PERSON><PERSON><PERSON><PERSON> a na Phantom ga-ar<PERSON>", "metamaskExplainerBottomSheetSubheader": "<PERSON> na mkpak<PERSON>r<PERSON><PERSON> jikọọ akpaego were họrọ MetaMask iji gaa n'ihu.", "metamaskExplainerBottomSheetDontShowAgain": "E gosizila <PERSON>", "ledgerStatusNotConnected": "<PERSON> jik<PERSON><PERSON> Ledger", "ledgerStatusConnectedInterpolated": "{{productName}} e<PERSON><PERSON><PERSON><PERSON>", "connectionClusterInterpolated": "Ị nọ na {{cluster}} ugbu a", "connectionClusterTestnetMode": "Ị nọ na Ọnọdụ Testnet ugbua", "featureNotSupportedOnLocalNet": "<PERSON><PERSON><PERSON><PERSON> a agaghị arụ mgbe a gbanyere Solana Localnet.", "readOnlyAccountBannerWarning": "Ị na-el<PERSON> aka<PERSON><PERSON> a", "depositAddress": "<PERSON><PERSON><PERSON>", "depositAddressChainInterpolated": "Adreesị {{chain}} Gị", "depositAssetDepositInterpolated": "Nweta {{tokenSymbol}}", "depositAssetSecondaryText": "A ga eji adreesị a enweta naanị tokin ndị yana ya makọrọ.", "depositAssetTextInterpolated": "Were adreesị a nweta tokin nakwa ihe onwunwe digitalụ na <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Nye site na-ebe mgbanweta", "depositAssetShareAddress": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "depositAssetBuyOrDeposit": "<PERSON><PERSON><PERSON>", "depositAssetBuyOrDepositDesc": "Tinye ego na akpaego gị ka ibido", "depositAssetTransfer": "<PERSON><PERSON>", "editAddressAddressAlreadyAdded": "<PERSON><PERSON><PERSON><PERSON>", "editAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON>", "editAddressAddressIsRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "editAddressPrimaryText": "<PERSON><PERSON>", "editAddressRemove": "Wepụ site na Ebe nchekwa Adreesi", "editAddressToast": "E melitela adreesị", "removeSavedAddressToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> adrees<PERSON>", "exportSecretErrorGeneric": "E nwere ihe na e<PERSON>hi ezi, biko nwaa ọzọ ma emecha", "exportSecretErrorIncorrectPassword": "Passwọọdụ e<PERSON>hi ezi", "exportSecretPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportSecretPrivateKey": "nọmba n<PERSON> m<PERSON>ye", "exportSecretSecretPhrase": "<PERSON><PERSON> n<PERSON><PERSON>", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "okwu nzuzo nnwetaghachị", "exportSecretSelectYourAccount": "<PERSON><PERSON><PERSON><PERSON> akaụnt<PERSON>", "exportSecretShowPrivateKey": "<PERSON><PERSON> akara n<PERSON> m<PERSON>ye", "exportSecretShowSecretRecoveryPhrase": "<PERSON><PERSON> okwu nzuzo nnwetagha<PERSON>", "exportSecretShowSecret": "Gosi {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "Ekekwala <1></1> {{secretNameText}} gị!", "exportSecretWarningSecondaryInterpolated": "Ọ bụru na mmadụ jiri {{secretNameText}} gị ha ga-enwe ikike ichịkwa ak<PERSON>ego gị.", "exportSecretOnlyWay": "{{secretNameText}} g<PERSON> bụ naanị <PERSON> aga esi enwetaghachị akpaego gị", "exportSecretDoNotShow": "<PERSON>kwek<PERSON> ka onye ọ bụla hụ {{secretNameText}} gị", "exportSecretWillNotShare": "A gaghị m enye onye ọ bụla {{secretNameText}}, ya na <PERSON>.", "exportSecretNeverShare": "<PERSON><PERSON>ị<PERSON> onye ọ bụla {{secretNameText}} gị", "exportSecretYourPrivateKey": "Akara Mbanye Nzuzo gị", "exportSecretYourSecretRecoveryPhrase": "<PERSON><PERSON> nzuzo nnwetaghachị gị", "exportSecretResetPin": "Hazigharịa PIN gị", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "<PERSON><PERSON>ma<PERSON>", "gasUpTo": "Ruo {{ amount }}", "timeDescription1hour": "<PERSON><PERSON> d<PERSON> ka awa 1", "timeDescription30minutes": "<PERSON><PERSON> d<PERSON> ka nkeji 30", "timeDescription10minutes": "<PERSON><PERSON> d<PERSON> ka nkeji 10", "timeDescription2minutes": "<PERSON><PERSON> d<PERSON> ka nkeji 2", "timeDescription30seconds": "<PERSON><PERSON> d<PERSON> ka tịnkọm tịnkọm 30", "timeDescription15seconds": "<PERSON><PERSON> dị ka tịnkọm tịnkọm 15", "timeDescription10seconds": "<PERSON><PERSON> d<PERSON> ka tịnkọm tịnkọm 10", "timeDescription5seconds": "<PERSON><PERSON> d<PERSON> ka tinkọm tịnkọm 5", "timeDescriptionAbbrev1hour": "Awa 1", "timeDescriptionAbbrev30minutes": "Nkeji 30", "timeDescriptionAbbrev10minutes": "Nkeji 10", "timeDescriptionAbbrev2minutes": "Nkeji 2", "timeDescriptionAbbrev30seconds": "<PERSON>ịnkọ<PERSON> 30", "timeDescriptionAbbrev15seconds": "tịnkọm 15", "timeDescriptionAbbrev10seconds": "tịnkọm 10", "timeDescriptionAbbrev5seconds": "tịnkọm 5", "gasSlow": "Nwayọọ", "gasAverage": "Obere", "gasFast": "Ng<PERSON> ngwa", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "Nwaa Ọzọ", "homeErrorDescription": "E nwere ihe na-agaghị nke ọma mgbe achọrọ inwetaghachị ihe onwunwe gị. <PERSON><PERSON> buba<PERSON>ị ma nwaa <PERSON>z<PERSON>", "homeErrorTitle": "<PERSON>we<PERSON><PERSON> ike enweta ihe onwunwe", "homeManageTokenList": "Chịk<PERSON><PERSON> n<PERSON><PERSON><PERSON> tokin", "interstitialDismissUnderstood": "<PERSON><PERSON><PERSON><PERSON><PERSON> ya", "interstitialBaseWelcomeTitle": "Phantom na-anabatazi Base ugbua!", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON>, nweta, ma zụta tok<PERSON>n", "interstitialBaseWelcomeItemTitle_2": "Mee nch<PERSON>gharị n'ime Base", "interstitialBaseWelcomeItemTitle_3": "Ọ nọgh<PERSON> n'ihe ize ndụ, o nwekwara nchekwa", "interstitialBaseWelcomeItemDescription_1": "Ziga ma zụta USDC na ETH na Base site n'iji {{paymentMethod}}, kaadị, ma ọ bụ Coinbase.", "interstitialBaseWelcomeItemDescription_2": "<PERSON><PERSON> Phantom mee ihe na aapụ Defi na NFT niile ị kacha jiri eme ihe.", "interstitialBaseWelcomeItemDescription_3": "Nọrọ n'udo site na nkwado Ledger, nwepụ ozi a na-ach<PERSON><PERSON>, nakwa <PERSON><PERSON> nzipụ ego.", "privacyPolicyChangedInterpolated": "<PERSON><PERSON> Nzowe ozi onwe onye anyị agbanwela. <1><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></1>", "bitcoinAddressTypesBodyTitle": "Ụdị adreesị Bitcoin", "bitcoinAddressTypesFeature1Title": "Banyere adreesị Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom na-akwado Native <PERSON><PERSON><PERSON><PERSON>, nke ọbụla nwere ego fọdụrụ na ya. Ị ga-enwe ike izipụ BTC maọbụ Ordinals site na-eji ụdị adreesị ọbụla.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Adreesị BTC na-eji na Phantom. Ọ dị ochie karịa <PERSON> mana ọ na-adaba na akpaego nakwa ebe mgbanweta nile.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "<PERSON><PERSON><PERSON><PERSON> mma maka Ordinals an BRC-20s, nwere ego nzipụ kacha <PERSON>. <PERSON><PERSON> adrees<PERSON> gasị Na otu ị siri wee chọọ-> <PERSON><PERSON><PERSON><PERSON><PERSON>reesị Bitcoin.", "headerTitleInfo": "<PERSON><PERSON>", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Nke a <1>{{addressType}}</1> bụ adreesị gị.", "invalidChecksumTitle": "Anyị a kwalitela okwu nzuzo gị!", "invalidChecksumFeature1ExportPhrase": "<PERSON><PERSON><PERSON>", "invalidChecksumFeature1ExportPhraseDescription": "<PERSON><PERSON> dekwa okwu nzuzo <PERSON>hụrụ gị ya na akara mbanye nzuzo nke akaụntụ ochie gị.", "invalidChecksumFeature2FundsAreSafe": "<PERSON><PERSON> g<PERSON> anọgh<PERSON> n'ihe ize ndụ, o nwekwara nchekwa", "invalidChecksumFeature2FundsAreSafeDescription": "E mere mbugo a n'ụzọ a na-akpaghị aka. O nweghị onye nọ na Phantom ma okwu nzuzo gị ma ọ bụ nwe ikike ị banye n'ego gị.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "invalidChecksumFeature3LearnMoreDescription": "Ị nwere okwu ya na ọtụtụ akpaego n'ejekọghị. Gụọ <1>edemede enyem aka a</1> i ji mụtakwuo maka nke a.", "invalidChecksumBackUpSecretPhrase": "<PERSON><PERSON><PERSON> okwu n<PERSON>zo", "migrationFailureTitle": "O nwere ihe na-aga<PERSON>hi <PERSON>fụma mgbe a na-ebugharị akaụntụ gị", "migrationFailureFeature1": "<PERSON><PERSON><PERSON> okwu n<PERSON>zo gị", "migrationFailureFeature1Description": "<PERSON><PERSON> dekwa okwu nzuzo gị tupu ị debanye.", "migrationFailureFeature2": "Debanye na Phantom", "migrationFailureFeature2Description": "<PERSON> kwesịrị na ị ga-edebagharị na Phantom iji hụ akaụ<PERSON><PERSON> gị.", "migrationFailureFeature3": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "migrationFailureFeature3Description": "Gụọ <1>ozi <PERSON> aka a</1> i<PERSON> m<PERSON> maka nke a.", "migrationFailureContinueToOnboarding": "Gaa n'ihu ruo na nde<PERSON>ye", "migrationFailureUnableToFetchMnemonic": "Anyị e nwenwughị ike bubata okwu nzuzo gị", "migrationFailureUnableToFetchMnemonicDescription": "<PERSON><PERSON> k<PERSON>ọtụrụ enyemaka ma budata ozi igwe aapụ i ji mezie", "migrationFailureContactSupport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledgerActionConfirm": "Me<PERSON> nkwadoeziokwu na Ledger Nano gị", "ledgerActionErrorBlindSignDisabledPrimaryText": "<PERSON><PERSON><PERSON><PERSON> aka d<PERSON><PERSON><PERSON><PERSON> na ezo njirmara ozi", "ledgerActionErrorBlindSignDisabledSecondaryText": "<PERSON><PERSON> gbaa mbo hụ na agbanyere m<PERSON> aka d<PERSON><PERSON><PERSON><PERSON><PERSON> na-ezo n<PERSON>rimara ozi na ihe nchekwa njịarụ gị ma mee ya <PERSON>z<PERSON>", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Ejikọghị ihe nchekwa njịarụ na oge ịrụ ọrụ", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "<PERSON>iko mechie ihe nchọgharị intaneti nke Phantom ma mee ya <PERSON>z<PERSON>", "ledgerActionErrorDeviceLockedPrimaryText": "Akpochịrị ihe nchekwa njịarụ", "ledgerActionErrorDeviceLockedSecondaryText": "<PERSON><PERSON> k<PERSON>ghee ihe nchekwa njịarụ gị ma mee ya <PERSON>z<PERSON>", "ledgerActionErrorHeader": "<PERSON><PERSON><PERSON><PERSON> omume ebe <PERSON> n<PERSON> ego", "ledgerActionErrorUserRejectionPrimaryText": "Onye na-eji ya anaba<PERSON>hị nzipụ ego", "ledgerActionErrorUserRejectionSecondaryText": "Onye na-eji ya anabataghị omume ihe ahụ na ihe nchekwa njị<PERSON><PERSON> ahụ", "ledgerActionNeedPermission": "Chọrọ nnata ikike", "ledgerActionNeedToConfirm": "Ị kwesiri ime nkwadoeziokwu nzipụ ego a na ngwa nchekwa akpaego gị. Gbaa mbọ hụ na akpọchịghị ya na aapụ {{chainType}}.", "ledgerActionNeedToConfirmMany": "Ị kwesiri ime nkwadoez<PERSON><PERSON>wu nzipụ ego {{numberOfTransactions}} na ngwa nchekwa akpaego gị. Gbaa mbọ hụ na-akpọchịghị ya na aapụ {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Ị kwesịrị ime nkwadoeziokwu nzipụ ego ahụ na ngwa nchekwa akpaego gị. Gbaa mbọ hụ na akpọchịghị ya na aapụ {{chainType}}, nakwa na-agbanyere nkwado nkwekọrịta ire ahịa na-ahụghị anya.", "ledgerActionNeedToConfirmBlindMany": "Ị kwesiri ime nkwadoeziokwu nzipụ ego {{numberOfTransactions}} na ngwa nchekwa akpaego gị. Gbaa mbọ hụ na akpọchịghị ya na aapụ {{chainType}}, nakwa na agbanyere nkwado nkwek<PERSON>rịta ire ahịa na-ahụghị anya.", "ledgerActionPleaseConnect": "<PERSON><PERSON> jikoo Akpa Nchekwa Ego USB gị", "ledgerActionPleaseConnectAndConfirm": "Biko jikọọ ngwa nchekwa akpaego gị ma gba mbọ hụ na-akpọpere ya akpọpe. Gba mbọ hụ na ị<PERSON>ela ikike n'igwe nchọta ihe n'ịntanetị gị.", "maxInputAmount": "Ọnụego", "maxInputMax": "<PERSON><PERSON><PERSON><PERSON>", "notEnoughSolPrimaryText": "Enweghi SOL zuru ezu", "notEnoughSolSecondaryText": "I nweghi SOL zuru ezu na akpaego gị maka nzipụ ego a. <PERSON><PERSON>o SOL ma nwaa ọzọ.", "insufficientBalancePrimaryText": "{{tokenSymbol}} e zughị ezu", "insufficientBalanceSecondaryText": "Ị nweghị {{tokenSymbol}} zuru ezu n'ak<PERSON>ego gị maka nzipụ a.", "insufficientBalanceRemaining": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "insufficientBalanceRequired": "A chọrọ", "notEnoughSplTokensTitle": "To<PERSON> ezugh<PERSON> ezu", "notEnoughSplTokensDescription": "Ị nweghị tokin zuru ezu n'akpaego gị maka nzipụ ego a. Nzipụ ego a ga alaghachị azụ ma ọ bụrụ na ezipụ ya.", "transactionExpiredPrimaryText": "<PERSON>ge n<PERSON>pụ ego agafeela", "transactionExpiredSecondaryText": "Ị gburu nnukwu oge n'ịkwado nzipụ ego ahụ, oge wee gafee. Ego a ga alaghachi ma ọ bụrụ na ị ziga ya.", "transactionHasWarning": "A dọm aka-na-ntị maka <PERSON> ego", "tokens": "tokin gasị", "notificationApplicationApprovalPermissionsAddressVerification": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> na ọ bụ gị nwe adreesị a", "notificationApplicationApprovalPermissionsTransactionApproval": "Nata ikike maka nzipụ ego", "notificationApplicationApprovalPermissionsViewWalletActivity": "<PERSON><PERSON> ego foduru na akpaego nakwa ihe omume gị", "notificationApplicationApprovalParagraphText": "<PERSON><PERSON> ga-enye saịtị a ohere ilele ego fọdụrụnụ na ihe ndị na-eme n'akaụ<PERSON><PERSON> ahọr<PERSON>.", "notificationApplicationApprovalActionButtonConnect": "Jikọọ", "notificationApplicationApprovalActionButtonSignIn": "Banye", "notificationApplicationApprovalAllowApproval": "Nye saịtị ohere ijiko?", "notificationApplicationApprovalAutoConfirm": "<PERSON><PERSON><PERSON> ego n'a<PERSON> aka", "notificationApplicationApprovalConnectDisclaimer": "Jikọọ naanị weeb<PERSON>tị ị nwere <PERSON>t<PERSON>was<PERSON>r<PERSON> obi", "notificationApplicationApprovalSignInDisclaimer": "Banye naanị na weebụ<PERSON> tụkwasịr<PERSON> obi", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "<PERSON>eb<PERSON><PERSON><PERSON>tị a nọ n'ihe ize ndụ iji, o nwere ike <PERSON>nwa izu ego gị.", "notificationApplicationApprovalConnectUnknownApp": "<PERSON><PERSON>", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Enweghị ike ijikọ na aapụ", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "<PERSON><PERSON><PERSON> a na-achọ ijikọ {{appNetworkName}}, mana <PERSON><PERSON><PERSON> {{phantomNetworkName}}.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Iji were {{networkName}} mee <PERSON><PERSON>, ga na <PERSON>ye mmep<PERSON> → Ọnọdụ Testnet.\n", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Netwọk A na-amaghị", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Nkwado adịghị maka ijikọ aapụ ekwentị ndị ọzọ nakwa akpaego Ledger n'oge a.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "<PERSON>iko banye na akaụntụ n'enweghị Ledger ma ọ bụ were nchọihe n'ịntanetị ime nke aapụ gị ma nwaa ọzọ.", "notificationSignatureRequestConfirmTransaction": "<PERSON><PERSON><PERSON>", "notificationSignatureRequestConfirmTransactionCapitalized": "<PERSON><PERSON><PERSON>", "notificationSignatureRequestConfirmTransactions": "<PERSON><PERSON><PERSON> n<PERSON> ego", "notificationSignatureRequestConfirmTransactionsCapitalized": "K<PERSON><PERSON>", "notificationSignatureRequestSignatureRequest": "Achọr<PERSON>binyeaka", "notificationMessageHeader": "<PERSON><PERSON>", "notificationMessageCopied": "A kọpịala ozi", "notificationAutoConfirm": "Nkwado n'akpaghị aka", "notificationAutoConfirmOff": "Gbanyụọ", "notificationAutoConfirmOn": "Mgban<PERSON>", "notificationConfirmFooter": "<PERSON><PERSON> <PERSON>k<PERSON><PERSON><PERSON><PERSON> naanị ma ọbụrụ na ị nwere ntụkwasiri na weebụsaịtị a.", "notificationEstimatedTime": "<PERSON><PERSON>", "notificationPermissionRequestText": "Nke a bụ naanị arịrịọ ínara ikike. <PERSON><PERSON><PERSON><PERSON> ego a nwere ike ọ gaghị a ga ozugbo.", "notificationBalanceChangesText": "Mgbanwe n'ego fọd<PERSON><PERSON><PERSON> bụ nke a na-atụ anya ya. Ón<PERSON>ego nakwa ihe onwunwe abụghị nke eji n'aka.", "notificationContractAddress": "<PERSON><PERSON><PERSON><PERSON>", "notificationAdvancedDetailsText": "<PERSON><PERSON> elu", "notificationUnableToSimulateWarningText": "<PERSON>i e nweghị ike ịgbakọ mgbanwe n'ego fọdụrụnụ. Ị nwere ike ịnwale ọzọ ma emecha, ma ọ bụ kwado ma ọ bụrụ na ị nwere ntụkwasịobi na webụsaịtị a.", "notificationSignMessageParagraphText": "Ịkwado ozi a ga-egosi na ọ bụ gị nwe aka<PERSON><PERSON><PERSON> a họrọ.", "notificationSignatureRequestScanFailedDescription": "<PERSON> nyochaghị ma ozi a onwere nsogbu gbasara nchekwa. <PERSON><PERSON> kee nkwụcha ma gaa n'ihu.", "notificationFailedToScan": "O kwegh<PERSON> e<PERSON><PERSON> r<PERSON> arịrịọ a. Na-eme nkwadoeziokwu na ọ dịghị mma ma nwee ike ibute ọghọm.", "notificationScanLoading": "<PERSON><PERSON>", "notificationTransactionApprovalActionButtonConfirm": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonBack": "Laghachi", "notificationTransactionApprovalEstimatedChanges": "Mgbanwe e mere Atụmat<PERSON> ya", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Mgbakọta dabere na eṅomi nzipụ ego na etinyeghi ihe mgbanwe dị na database ma ọ bụghị ihe e ji n'aka", "notificationTransactionApprovalHideAdvancedDetails": "<PERSON><PERSON> n<PERSON>wa nzipụ ego emere n'ihu", "notificationTransactionApprovalNetworkFee": "<PERSON>go <PERSON>gw<PERSON> nzipụ ego", "notificationTransactionApprovalNetwork": "Netwọkụ", "notificationTransactionApprovalEstimatedTime": "<PERSON>ge at<PERSON><PERSON><PERSON>a", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Enweghị mgbanwe na-emetụta inwe ihe onwunwe nke ah<PERSON>", "notificationTransactionApprovalSolanaAmountRequired": "Ọnụego netwọkụ Solana chọrọ maka nzipụ ego", "notificationTransactionApprovalUnableToSimulate": "Enweghughị ike <PERSON>omi. Gbaa mbọ hụ na ị nwere ntụkwasị obi na webụsaịtị a, ebe ọ bụ na-inye ikike nwere ike ibute ego ifu efu.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Enweghi ike enweta mgbanwe n'ego fọd<PERSON><PERSON><PERSON>", "notificationTransactionApprovalViewAdvancedDetails": "<PERSON><PERSON> n<PERSON>ọwa nzipụ ego emere n'ihu", "notificationTransactionApprovalKnownMalicious": "<PERSON>zi<PERSON><PERSON> ego a bụ nke nduhie. <PERSON><PERSON><PERSON> ga ebute nfunahu ego.", "notificationTransactionApprovalSuspectedMalicious": "Anyị na enyo na nzipụ ego a bụ nke ana-enyeghi ikike. Inye ikike nwere ike ebute nfunahu ego.", "notificationTransactionApprovalNetworkFeeHighWarning": "A na-ewelite aka na ego ụgwọ nzipụ coin n'ihi nzịpụ coin karịr<PERSON> ikike netwok.", "notificationTransactionERC20ApprovalDescription": "Ịkwado ga-enye aapụ a ikike ibanye n'ego gị fọdụrụ oge ọ bụla, ruo ogogo dị n'okpuru.", "notificationTransactionERC20ApprovalContractAddress": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionERC20Unlimited": "enwe<PERSON><PERSON> nje<PERSON>be", "notificationTransactionERC20ApprovalTitle": "<PERSON>ye nkwado maka mmefu {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Kagbuo mmefu {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "<PERSON><PERSON><PERSON><PERSON> ohere mweta {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "{{tokenSymbol}} g<PERSON> nile", "notificationIncorrectModeTitle": "Ọnọdụ na-e<PERSON>hi ezi", "notificationIncorrectModeInTestnetTitle": "Ị nọ n'ọnọdụ Testnet", "notificationIncorrectModeNotInTestnetTitle": "Ị nọghị n'ọnọdụ Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} na-achọ i ji mainnet mee ihe, mana ị nọ n'ọnọdụ Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} na-achọ i ji testnet mee ihe, mana ị nọghị n'ọnọdụ Testnet", "notificationIncorrectModeInTestnetProceed": "I ji gaa n'ihu, g<PERSON><PERSON><PERSON><PERSON> ọnọdụ <PERSON>net.", "notificationIncorrectModeNotInTestnetProceed": "I ji gaa n'ihu, g<PERSON><PERSON> <PERSON>n<PERSON>.", "notificationIncorrectEIP712ChainId": "Anyị gbochiri gị ka ị ghara ibinye aka na ozi a na-emebereghi maka netwọkụ nke ị nọ na njiko ya ugbu a", "notificationIncorrectEIP712ChainIdDescription": "<PERSON><PERSON> an<PERSON> {{messageChainId}}, <PERSON> jik<PERSON><PERSON><PERSON> {{connectedChainId}}", "notificationUnsupportedNetwork": "Netwọkụ a na-akwadoghị", "notificationUnsupportedNetworkDescription": "Weebụsaịtị a na anwa iji netwokụ nke Phantom anaghị akwado ugbu a.", "notificationUnsupportedNetworkDescriptionInterpolated": "<PERSON>ji gaa n'ihu na-eji ihe nchọta ihe n'ịntanetị dị iche, gbanyuo <1><PERSON><PERSON><PERSON> →Akpaego <PERSON>apụ ana-ah<PERSON><PERSON><PERSON>, ma họr<PERSON> Jụ<PERSON> Mgbe <PERSON></1>. Mgbe ahụ bugharịa peeji ma jikọgharịa.", "notificationUnsupportedAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON> a na-akwa<PERSON>", "notificationUnsupportedAccountDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a na-anwa iji {{targetChainType}}, nke aka<PERSON> {{chainType}} a anaghị akwado.", "notificationUnsupportedAccountDescription2": "Banye n'akaụntụ site na mkpụrụokwu nnwetaghach<PERSON> da<PERSON>, ma <PERSON> bụ nọmba nzuzo mbanye ma nwaa ọzọ.", "notificationInvalidTransaction": "Nzipụ ego na-ezighi ezi", "notificationInvalidTransactionDescription": "Nzipụ ego anatara site n'aapụ a ezighi ezi, ekwesighi iziga ya. Biko kpọtụrụ onye nrụpụta aapụ iji kọọrọ ha nsogbu a.", "notificationCopyTransactionText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> n<PERSON>", "notificationTransactionCopied": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingImportOptionsPageTitle": "<PERSON><PERSON><PERSON> a<PERSON>", "onboardingImportOptionsPageSubtitle": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> ngwa nchekwa akpaego gị bubata akpaego dịbụ adị.", "onboardingImportPrivateKeyPageTitle": "Bubata Nọmba N<PERSON>", "onboardingImportPrivateKeyPageSubtitle": "<PERSON><PERSON><PERSON> a<PERSON> ebebere maka oha dị<PERSON><PERSON> adị", "onboardingCreatePassword": "<PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON><PERSON><PERSON><PERSON> m na<1> <PERSON><PERSON> Ọrụ</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordDescription": "Ị ga-eji nke a akpọchị akpaego gị.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON> nzuzo nnwetaghachị ezighi ezi", "onboardingCreatePasswordPasswordPlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON><PERSON><PERSON> ike", "onboardingCreatePasswordPasswordStrengthMedium": "<PERSON>i obere ike", "onboardingCreatePasswordPasswordStrengthStrong": "<PERSON><PERSON> e<PERSON> ike", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON> m <PERSON><PERSON> m", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Okwu a bụ NAANỊ ụzọ aga-eji enwetaghachi ak<PERSON>ego gị. ENYEKWALA YA onye ọzọ!", "onboardingImportWallet": "Bụbata <PERSON>", "onboardingImportWalletImportExistingWallet": "<PERSON><PERSON> mk<PERSON><PERSON><PERSON><PERSON> okwu n<PERSON>zo nnwetag<PERSON>ch<PERSON> 12 maọbụ 24 wee bubata akpaego dịb<PERSON> adị.", "onboardingImportWalletRestoreWallet": "<PERSON>we<PERSON><PERSON><PERSON><PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON>zo Nnwetaghachị e<PERSON>hi ezi", "onboardingImportWalletIHaveWords": "E nwere m {{numWords}}- ok<PERSON> n<PERSON><PERSON> nwe<PERSON>", "onboardingImportWalletIncorrectOrMisspelledWord": "<PERSON>kara okwu a {{wordIndex}} ezighị ezi ma ọ bụ ahazighị ya ọf<PERSON>ma", "onboardingImportWalletIncorrectOrMisspelledWords": "<PERSON><PERSON> okwu ndịa {{wordIndexes}} ezighị ezi ma ọ bụ ahazighị ha ọfụma", "onboardingImportWalletScrollDown": "<PERSON><PERSON><PERSON> ala", "onboardingImportWalletScrollUp": "Gbagote elu", "onboardingSelectAccountsImportAccounts": "Bụbata Akaụntụ gasị", "onboardingSelectAccountsImportAccountsDescription": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>.", "onboardingSelectAccountsImportSelectedAccounts": "<PERSON><PERSON><PERSON><PERSON>", "onboardingSelectAccountsFindMoreAccounts": "<PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> nd<PERSON>", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "onboardingSelectAccountsNoOfAccountsSelected": "A h<PERSON><PERSON><PERSON><PERSON> {{numOfAccounts}}", "onboardingSelectAccountSelectAllText": "<PERSON><PERSON><PERSON><PERSON>", "onboardingAdditionalPermissionsTitle": "<PERSON><PERSON> a<PERSON> mee ihe na <PERSON>", "onboardingAdditionalPermissionsSubtitle": "<PERSON><PERSON> <PERSON> zere nsogbu ọ bụla oge eji aapụ eme ihe, anyị kwadoro ịnye Phantom ikike ị gụ<PERSON> nakwa ị<PERSON>banwe ozi na webụsaịtị niile.", "interstitialAdditionalPermissionsTitle": "<PERSON><PERSON> a<PERSON> mee ihe na <PERSON>", "interstitialAdditionalPermissionsSubtitle": "I ji gaa n'ihu iji a<PERSON>ụ mee ihe n'enwe <PERSON>, anyị kwadoro ịnye Phantom ikike ị gụụ nakwa ịgbanwe ozi na webụsaịtị niile.", "recentActivityPrimaryText": "<PERSON><PERSON>", "removeAccountActionButtonRemove": "<PERSON><PERSON><PERSON>", "removeAccountRemoveWallet": "<PERSON><PERSON><PERSON>", "removeAccountInterpolated": "We<PERSON>ụ {{accountName}}", "removeAccountWarningLedger": "N'agbanyeghị na ị na-ewepụ akpaego gị na Phantom, ị ga enwe ike etinyeghachị ya site na-eji \"Njikoo Ihe Nchekwa Akpaego\" na arụsị ọrụ ike.", "removeAccountWarningSeedVault": "N'agbanyeghị na ị na ewepụ akpaego a na Phantom, ị ga enwe ike itinyeghachị ya site na-iji \"Ebe Ndebe Akpaego\" maka <PERSON> nzipụ.", "removeAccountWarningPrivateKey": "Ozugbo iwe<PERSON><PERSON><PERSON><PERSON> a<PERSON> a, <PERSON> agaghị enwe ike enwetaghachịrị gị ya. Gbaa mbo hụ na ị chekwara nọmba nzuzo mbanye gị.", "removeAccountWarningSeed": "N'agbanyeghị na ị na-ewepụ akpaego gị na Phantom, ị ga enwe ike enwetaghachị ya site na eji mnemonic na nke a maọbụ na akpaego ọzọ.", "removeAccountWarningReadOnly": "Iwep<PERSON> akaụnt<PERSON> a agaghị emetụta akpaego gị, n'ihi na ọ bụ akpaego nke aga-elele naan<PERSON>.", "removeSeedPrimaryText": "<PERSON><PERSON><PERSON> {{number}}", "removeSeedSecondaryText": "Nke a ga-ewe<PERSON>ụ akaụntụ nile dị n'ime Okwu Nzuzo nke {{number}}. Gba mbọ hụ na ị chekwara okwu nzụzo nke ị nweburu.", "resetSeedPrimaryText": "Were okwu n<PERSON><PERSON><PERSON> ha<PERSON>a aap<PERSON>", "resetSeedSecondaryText": "Nke a ga ewepụ akaụntụ nile diwara gbo ma were ndị ọhụrụ nọchie ha. Gba mbọ hụ na enwere ebe idokwara okwu nzuzo nakwa akara mgbachi nkeonwe gị.", "resetAppPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma hichaa aap<PERSON>", "resetAppSecondaryText": "Nke a ga-ewepụ akaụntụ na ozi nile dịbụ adị. Gba mbọ hụ na ị dokwara okwu nzuzo nakwa nọmba nzuzo mbanye gị.", "richTransactionsDays": "ụbọchị gasị", "richTransactionsToday": "<PERSON><PERSON>", "richTransactionsYesterday": "Ụnyaahụ", "richTransactionDetailAccount": "<PERSON><PERSON>", "richTransactionDetailAppInteraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailAt": "na", "richTransactionDetailBid": "<PERSON><PERSON>", "richTransactionDetailBidDetails": "Nkọwa ikwe ọnụ", "richTransactionDetailBought": "Zụtara", "richTransactionDetailBurned": "Ebibiela", "richTransactionDetailCancelBid": "Kagbuo i<PERSON>", "richTransactionDetailCompleted": "E mechala", "richTransactionDetailConfirmed": "<PERSON><PERSON>", "richTransactionDetailDate": "<PERSON><PERSON>", "richTransactionDetailFailed": "Ọgaghị", "richTransactionDetailFrom": "Site na", "richTransactionDetailItem": "Eser<PERSON>", "richTransactionDetailListed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eserese maka orire", "richTransactionDetailListingDetails": "Nkọwa <PERSON> es<PERSON>", "richTransactionDetailListingPrice": "Ọnụego Akwadoro", "richTransactionDetailMarketplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailNetworkFee": "<PERSON>go <PERSON>gw<PERSON> nzipụ ego", "richTransactionDetailOriginalListingPrice": "Ọnụego <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ya", "richTransactionDetailPending": "<PERSON><PERSON> ana-<PERSON><PERSON><PERSON><PERSON><PERSON> nk<PERSON>", "richTransactionDetailPrice": "Ọnụahịa", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailPurchaseDetails": "Nkọwa ihe Azụrụ", "richTransactionDetailRebate": "<PERSON><PERSON><PERSON>ach<PERSON> ego", "richTransactionDetailReceived": "Nwetara", "richTransactionDetailSaleDetails": "Nkọwa ihe orire", "richTransactionDetailSent": "Ezigala", "richTransactionDetailSold": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailStaked": "<PERSON><PERSON><PERSON><PERSON> ruo nwa oge", "richTransactionDetailStatus": "Ọnọdụ ya", "richTransactionDetailSwap": "Mgbanweta", "richTransactionDetailSwapDetails": "Nkọwa Mgbanwete ihe onwunwe", "richTransactionDetailTo": "Na", "richTransactionDetailTokenSwap": "<PERSON><PERSON><PERSON><PERSON><PERSON> tokin", "richTransactionDetailUnknownNFT": "NFT ana-amagh<PERSON>", "richTransactionDetailUnlisted": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> eserese", "richTransactionDetailUnstaked": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailValidator": "Onye o mee nkwadoeziokwu", "richTransactionDetailViewOnExplorer": "<PERSON><PERSON> {{explorer}}", "richTransactionDetailWithdrawStake": "<PERSON><PERSON><PERSON> ruo nwa oge", "richTransactionDetailYouPaid": "Ị Kw<PERSON>rụ Ụgwọ", "richTransactionDetailYouReceived": "Ị <PERSON><PERSON><PERSON>", "richTransactionDetailUnwrapDetails": "Nkọwa banyere Mgbanwe", "richTransactionDetailTokenUnwrap": "Mgban<PERSON>", "activityItemsRefreshFailed": "Mbudata nzipu ego nd<PERSON> a gaghị.", "activityItemsPagingFailed": "Mbudata nzipu ego ndị ochie a gaghị.", "activityItemsTestnetNotAvailable": "Ndekọ ozi nzipu ego e mere na Testnet adịghị n'oge a", "historyUnknownDappName": "<PERSON><PERSON>", "historyStatusSucceeded": "Ọ gara nke <PERSON>", "historyNetwork": "Netwọkụ", "historyAttemptedAmount": "Ọnụego anwara", "historyAmount": "Ọnụego", "sendAddressBookButtonLabel": "<PERSON><PERSON>chek<PERSON>", "addressBookSelectAddressBook": "<PERSON><PERSON>chek<PERSON>", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON><PERSON><PERSON> adreesi e<PERSON>", "sendAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON>", "addressBookSelectRecentlyUsed": "<PERSON><PERSON><PERSON>", "sendConfirmationLabel": "<PERSON><PERSON> ad<PERSON><PERSON> a<PERSON>", "sendConfirmationMessage": "<PERSON><PERSON>", "sendConfirmationNetworkFee": "Ego ụgwọ Nzipụ Ego", "sendConfirmationPrimaryText": "<PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON><PERSON>wu Nzipụ", "sendWarning_INSUFFICIENT_FUNDS": "<PERSON><PERSON>, <PERSON><PERSON> ego a nwere ike <PERSON>da ma ọ bụrụ na-eme ya.", "sendFungibleSummaryNetwork": "Netwọkụ", "sendFungibleSummaryNetworkFee": "<PERSON>go <PERSON>gw<PERSON> nzipụ ego", "sendFungibleSummaryEstimatedTime": "<PERSON>ge at<PERSON><PERSON><PERSON>a", "sendFungiblePendingEstimatedTime": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> at<PERSON><PERSON> ya", "sendFungibleSummaryEstimatedTimeDescription": "Ịga ngwa ngwa azụ m ahịa Ethereum na-agbado ụkwụ n'ọtụtụ ihe. Ị nwere ike ime ka ọ ga ngwa ngwa karịa site n'ịpị \"Ụgwọ Netwọk\".", "sendSummaryBitcoinPendingTxTitle": "E nweghị ike i<PERSON>a n<PERSON>pu", "sendSummaryBitcoinPendingTxDescription": "I nwere ike ịnwe naanị otu nzipu BTC n'echere n'otu mgbe. <PERSON>iko chere ka omecha, tupu ime nzipu <PERSON>ụ<PERSON>ụ.", "sendFungibleSatProtectionTitle": "Ji <PERSON> Sat em<PERSON>", "sendFungibleSatProtectionExplainer": "Phantom na-ahụ na a gaghị eji Ordinals na BRC20s gị eme ụgwọ nzipụ ego maọbụ nzipụ Bitcoin.", "sendFungibleTransferFee": "<PERSON><PERSON> tokin", "sendFungibleTransferFeeToolTip": "<PERSON>ye mmep<PERSON> tokin a natara ego na nzipụ ọbụla e mere. Nke a abụghị ego ana-ana ma<PERSON><PERSON>ụ nke Phantom na-anọk<PERSON>ta.", "sendFungibleInterestBearingPercent": "Ọnụego Ọmụrụ nwa Ugbu a", "sendFungibleNonTransferable": "Nke a na-Enweghí Ike Inyefe", "sendFungibleNonTransferableToolTip": "A gaghị enwe ike ị were tokịn a gaa na akaụnt<PERSON>.", "sendFungibleNonTransferableYes": "Ee", "sendStatusErrorMessageInterpolated": "E nwere ihe na-agaghị nke ọma mgbe achọrọ izipụ tokin na <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "I nweghị ego zuru oke fọdụrụnụ iji mezuo nzipụ ego ahụ.", "sendStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON> ike e<PERSON>", "sendStatusLoadingTitle": "Na-ezi<PERSON>ụ...", "sendStatusSuccessMessageInterpolated": "<PERSON><PERSON><PERSON> tokin gị na <1>{{uiRecipient}}</1> gara nke <PERSON>", "sendStatusSuccessTitle": "E zipụla!", "sendStatusConfirmedSuccessTitle": "E zipụla!", "sendStatusSubmittedSuccessTitle": "E zigala Nzipu ego", "sendStatusEstimatedTransactionTime": "<PERSON><PERSON> A tụ<PERSON>ụ anya: {{time}}", "sendStatusViewTransaction": "<PERSON><PERSON> ego", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> to <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<PERSON> <2>{{uiAmount}}{{assetSymbol}}</2> ọfụma gaa na <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<PERSON> zipuru <2>{{uiAmount}}{{assetSymbol}}</2> ọfụma gaa na <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> failed to send to <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON><PERSON><PERSON><PERSON> koodu {{code}}", "sendFormErrorInsufficientBalance": "<PERSON><PERSON> fodurunu e<PERSON>ghi ezu", "sendFormErrorEmptyAmount": "<PERSON>", "sendFormInvalidAddress": "<PERSON><PERSON><PERSON>ị {{assetName}} am<PERSON><PERSON><PERSON><PERSON>", "sendFormInvalidUsernameOrAddress": "<PERSON>a mbanye ma <PERSON> bụ adreesị a dabaghị", "sendFormErrorInvalidSolanaAddress": "<PERSON><PERSON><PERSON>", "sendFormErrorInvalidTwitterHandle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aha<PERSON><PERSON> a", "sendFormErrorInvalidDomain": "Edebanyeghi domain a", "sendFormErrorInvalidUsername": "Ejibeghị aha mbanye a mepee akaụntụ", "sendFormErrorMinRequiredInterpolated": "Opekampe a<PERSON>ọ<PERSON>ọ {{minAmount}} {{tokenName}}", "sendRecipientTextareaPlaceholder": "<PERSON><PERSON><PERSON> onye nweta SOL", "sendRecipientTextAreaPlaceholder2": "Ad<PERSON>sị {{symbol}} nke ndị na anata ya", "sendMemoOptional": "<PERSON><PERSON> (nhọrọ)", "sendMemo": "<PERSON>i <PERSON>", "sendOptional": "nhọrọ", "settings": "<PERSON><PERSON><PERSON>", "settingsDapps": "dApps", "settingsSelectedAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON> a họr<PERSON>", "settingsAddressBookNoLabel": "<PERSON><PERSON><PERSON><PERSON> aha ad<PERSON> a<PERSON>", "settingsAddressBookPrimary": "<PERSON><PERSON>chek<PERSON>", "settingsAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON>", "settingsAddressBookSecondary": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON> an<PERSON>", "settingsAutoLockTimerPrimary": "<PERSON><PERSON><PERSON> Oge Mgbanyu nke Ak<PERSON>aka", "settingsAutoLockTimerSecondary": "Gbanwee ogologo oge nhazi oge mgbanyu nke akpaaka gị", "settingsChangeLanguagePrimary": "<PERSON><PERSON><PERSON><PERSON>", "settingsChangeLanguageSecondary": "<PERSON><PERSON><PERSON><PERSON> ngosi as<PERSON>", "settingsChangeNetworkPrimary": "Gbanwee Netwok", "settingsChangeNetworkSecondary": "Hakọọ nhazi netwok gị", "settingsChangePasswordPrimary": "<PERSON>ban<PERSON><PERSON>", "settingsChangePasswordSecondary": "Gbanwee passw<PERSON><PERSON><PERSON><PERSON> mkpọchị ihuenyo gị", "settingsCompleteBetaSurvey": "<PERSON><PERSON><PERSON> nke <PERSON>", "settingsDisplayLanguage": "<PERSON><PERSON>", "settingsErrorCannotExportLedgerPrivateKey": "Enweghị ike e<PERSON> n<PERSON> n<PERSON>zo mbanye nke ebe <PERSON> nzi<PERSON>", "settingsErrorCannotRemoveAllWallets": "<PERSON><PERSON><PERSON><PERSON> ewepụ<PERSON>ị aka<PERSON><PERSON><PERSON>le", "settingsExportPrivateKey": "<PERSON><PERSON> akara n<PERSON> m<PERSON>ye", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "<PERSON><PERSON><PERSON><PERSON>", "settingsNetworkPhantomRPC": "Netwọkụ nke Phantom RPC", "settingsTestNetworks": "Netwọk ndị eji eme Nnwale", "settingsUseCustomNetworks": "<PERSON><PERSON> nke Ahazịr<PERSON> ahazị", "settingsTestnetMode": "Ọnọdụ Testnet", "settingsTestnetModeDescription": "Gụnyere ma ego ndị fọdụrụ nakwa njikọ aapụ.", "settingsWebViewDebugging": "<PERSON><PERSON><PERSON> na Nlele Weebụ", "settingsWebViewDebuggingDescription": "Na-enye gị ohere inyocha ma dozie njehie na nlele weebụ dị n'ime aapụ ihe nchọgharị.", "settingsTestNetworksInfo": "Ịgbanwe gaa na Testnet netwọk ọ bụla kwesiri ịbụ naanị maka nnwale. Biko mara na tokin ndị nọ na Testnet Netwọk anaghị enwe ihe uru ego ọ bụla.", "settingsEmojis": "<PERSON><PERSON><PERSON>", "settingsNoAddresses": "<PERSON><PERSON><PERSON><PERSON>", "settingsAddressBookEmptyHeading": "Ebe Nchekwa Adreesi gị togboro chakọọ", "settingsAddressBookEmptyText": "<PERSON><PERSON><PERSON> b<PERSON> \"+\" ma<PERSON><PERSON><PERSON> nke \"<PERSON><PERSON>\" iji tinye adreesị kacha amasị gị", "settingsEditWallet": "<PERSON><PERSON>", "settingsNoTrustedApps": "<PERSON><PERSON><PERSON><PERSON> atụkwasịrị obi", "settingsNoConnections": "Enwebegh<PERSON> njiko ugbu a.", "settingsRemoveWallet": "<PERSON><PERSON><PERSON>", "settingsResetApp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsBlocked": "E gbochiela", "settingsBlockedAccounts": "Akaụntụ ndị e gbochirila", "settingsNoBlockedAccounts": "E nweghị akaụnt<PERSON> ndị e gbochiri.", "settingsRemoveSecretPhrase": "<PERSON><PERSON><PERSON>", "settingsResetAppWithSecretPhrase": "<PERSON> okwu n<PERSON>zo ha<PERSON> aap<PERSON>", "settingsResetSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsShowSecretRecoveryPhrase": "<PERSON><PERSON>", "settingsShowSecretRecoveryPhraseSecondary": "<PERSON><PERSON>", "settingsShowSecretRecoveryPhraseTertiary": "<PERSON><PERSON> okwu n<PERSON>zo", "settingsTrustedAppsAutoConfirmActiveUntil": "Ruo {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Nkwado n'akpaghị aka", "settingsTrustedAppsDisclaimer": "Gbanye nkwado-n'<PERSON> aka naan<PERSON> na webụsaịtị ndị ịnwere ntụkwasịob<PERSON> na ha", "settingsTrustedAppsLastUsed": "Jirila {{formattedTimestamp}} n'oge gara aga", "settingsTrustedAppsPrimary": "Aapụ ndị <PERSON>", "settingsTrustedApps": "Aapụ ndị E nwere ntụkwasịobi na ha", "settingsTrustedAppsRevoke": "<PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsRevokeToast": "e<PERSON><PERSON><PERSON><PERSON> {{trustedApp}}", "settingsTrustedAppsSecondary": "Hakọọ ngwaọrụ <PERSON> ntụkwasiri obi", "settingsTrustedAppsToday": "<PERSON><PERSON>", "settingsTrustedAppsYesterday": "Ụnyaahụ", "settingsTrustedAppsLastWeek": "<PERSON><PERSON> ụka gara a ga", "settingsTrustedAppsBeforeYesterday": "<PERSON><PERSON>", "settingsTrustedAppsDisconnectAll": "Wepụ njikọ pụọ na ha nile", "settingsTrustedAppsDisconnectAllToast": "E wepụla n<PERSON>kọ Aapụ nile", "settingsTrustedAppsEndAutoConfirmForAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> nk<PERSON> n'a<PERSON> aka maka ha nile", "settingsTrustedAppsEndAutoConfirmForAllToast": "A kwụsị<PERSON>'a<PERSON> aka nile", "settingsSecurityPrimary": "Nchekwa na Ihe nzuzo", "settingsSecuritySecondary": "Melite nhazi nchekwa gị", "settingsActiveNetworks": "Netwọkụ ndị nọ n'Ọrụ", "settingsActiveNetworksAll": "Ha nile", "settingsActiveNetworksSolana": "<PERSON><PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "<PERSON><PERSON> bụ netwọkụ nke nhọrọ akpaaka ma ga-anọgide na mgbanye mgbe niile.", "settingsDeveloperPrimary": "<PERSON><PERSON><PERSON><PERSON>", "settingsAdvanced": "<PERSON><PERSON><PERSON> elu", "settingsTransactions": "<PERSON><PERSON><PERSON><PERSON>", "settingsAutoConfirm": "N<PERSON>zi <PERSON>kwa<PERSON> a <PERSON>'akpaghị aka", "settingsSecurityAnalyticsPrimary": "Kekọrịta Nyocha mgbanweta data na Ekpugheghị Njirimara", "settingsSecurityAnalyticsSecondary": "<PERSON><PERSON><PERSON> iji nyere any<PERSON> aka em<PERSON><PERSON><PERSON><PERSON>", "settingsSecurityAnalyticsHelper": "Phantom anaghị eji ozi nkeonwe gị eme nyocha", "settingsSuspiciousCollectiblesPrimary": "Zoo Ihe onwunwe dịg<PERSON> ana Enyo Enyo", "settingsSuspiciousCollectiblesSecondary": "<PERSON><PERSON><PERSON> b<PERSON><PERSON><PERSON><PERSON><PERSON> iji zoo ihe onwunwe dị<PERSON><PERSON><PERSON> akara", "settingsPreferredBitcoinAddress": "Adreesị Bitcoin nke <PERSON> họ<PERSON>", "settingsEnabledAddressesUpdated": "<PERSON><PERSON><PERSON><PERSON> ozi <PERSON>h<PERSON>rụ na-adreesi ndị ana-ahụ anya!", "settingsEnabledAddresses": "Adreesị ndị A gban<PERSON>e", "settingsBitcoinPaymentAddressForApps": "Adreesị eji akwụ ụgwọ maka App gasị", "settingsBitcoinOrdinalsAddressForApps": "Adreesị Ordinals maka App gasị", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Mgbe emep<PERSON><PERSON> <PERSON>dị adreesị abụọ dị n'elu, ma<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON> ụdị aapụ dịka <PERSON>, a ga-eji adreesị Native Segwit gị etinye ego eji azụ ihe. A ga-anata ihe onwunwe dịgịtalụ ndị azụrụ azụ na adreesị Taproot gị.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "<PERSON><PERSON><PERSON><PERSON> mburubịa Bitcoin n'ime Phantom iji hụ na ha gakọr<PERSON>.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(<PERSON><PERSON><PERSON><PERSON>a)", "settingsPreferredBitcoinAddressTaprootExplainer": "Ụdị adreesị kachasị dị ọhụ<PERSON>, na-enwekarị ụgwọ nzipụ ego dị ọnụ ala.", "settingsPreferredExplorers": "Ebe nchọta data Ahọ<PERSON>", "settingsPreferredExplorersSecondary": "Gbanwee na-ebe nchọta data nke <PERSON> họr<PERSON>la", "settingsCustomGasControls": "Njikwa Nhazi Ụgwọ ego nzipụ etu esiri chọọ", "settingsSupportDesk": "<PERSON><PERSON>", "settingsSubmitATicket": "Ziga Tiketị", "settingsAttachApplicationLogs": "Gbakwụnye Ozi Aapụ", "settingsDownloadApplicationLogs": "Budata <PERSON> ihe o<PERSON>e <PERSON>", "settingsDownloadApplicationLogsShort": "Bud<PERSON>", "settingsDownloadApplicationLogsHelper": "O nwere ozi ime, ozi <PERSON>, nakwa ad<PERSON><PERSON>, i<PERSON> nye aka n'<PERSON><PERSON><PERSON> nsogbu Enyemaka na Phantom", "settingsDownloadApplicationLogsWarning": "Onweghị ozi dị mkpa dịka mkpụr<PERSON><PERSON><PERSON> mkpọchi ma ọ bụ akara mbanye nzuzo e tinyere.", "settingsWallet": "Akpaego", "settingsPreferences": "<PERSON><PERSON><PERSON>", "settingsSecurity": "Nchekwa", "settingsDeveloper": "<PERSON><PERSON>ụ<PERSON>", "settingsSupport": "<PERSON><PERSON>ma<PERSON>", "settingsWalletShortcutsPrimary": "Gosipụta Ụzọ mkpirisi ịbanye Akpaego", "settingsAppIcon": "Icon Aapụ", "settingsAppIconDefault": "<PERSON><PERSON>", "settingsAppIconLight": "Light\n", "settingsAppIconDark": "Dark", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON>", "settingsSearchResultSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultExport": "<PERSON><PERSON><PERSON>", "settingsSearchResultSeed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultTrusted": "Nke akwadoro", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Ọnọdụ ugbua", "settingsSearchResultLogs": "<PERSON><PERSON>", "settingsSearchResultBiometric": "<PERSON><PERSON><PERSON> mmad<PERSON>", "settingsSearchResultTouch": "<PERSON><PERSON><PERSON> aka", "settingsSearchResultFace": "<PERSON><PERSON><PERSON> <PERSON>", "settingsSearchResultShortcuts": "Ụzọ dị mfe", "settingsAllSitesPermissionsTitle": "Banye Phantom site na saịtị niile", "settingsAllSitesPermissionsSubtitle": "Na-ekwe ka i jiri aapụ mee ihe na Phantom n'enwegh<PERSON> nsogbu <PERSON>, na n'apịghị aka na ihe nchọta ihe", "settingsAllSitesPermissionsDisabled": "Ngwa nchọta ihe n'ịntanetị gị akwadoghị mgbanwe nhazi a", "settingsSolanaCopyTransaction": "Gbanye Nkọpị<PERSON>", "settingsSolanaCopyTransactionDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> ozi nzi<PERSON>ụ ego ahazirila n'usoro n'usoro gaa na klipbọọdụ", "settingsAutoConfirmHeader": "Nkwado n'akpaghị aka", "refreshWebpageToApplyChanges": "<PERSON><PERSON><PERSON><PERSON><PERSON> ibe web<PERSON><PERSON> i ji mee ka mgbanwe dịgide", "settingsExperimentalTitle": "<PERSON><PERSON><PERSON>", "settingsExprimentalSolanaActionsSubtitle": "<PERSON><PERSON><PERSON> bọ<PERSON>ịnụ Ọrụ <PERSON>'a<PERSON>gh<PERSON> aka; oge ach<PERSON><PERSON> adreesị webụsaịtị ndị dị mkpa na X.com", "stakeAccountCardActiveStake": "Nkuchi coin ruo nwa oge nọ n'ọrụ", "stakeAccountCardBalance": "<PERSON><PERSON>", "stakeAccountCardRentReserve": "Ebe Ndebe Mgbazinye Ego Ụgwọ", "stakeAccountCardRewards": "Ihe nrite ikpeazụ", "stakeAccountCardRewardsTooltip": "Nke a bụ ihe nrita ị nwetara nso-nso a site na nkuchi ị mere. A ga n'enye gị ihe nrita kwa ụbọchị atọ ọbụla.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON>ị", "stakeAccountCardLockup": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeRewardsHistoryTitle": "<PERSON><PERSON><PERSON> n<PERSON>", "stakeRewardsActivityItemTitle": "<PERSON>he nrite", "stakeRewardsHistoryEmptyList": "E nweghi ihe nrite", "stakeRewardsTime_zero": "<PERSON><PERSON>", "stakeRewardsTime_one": "Ụnyaahụ", "stakeRewardsTime_other": "{{count}} <PERSON><PERSON><PERSON><PERSON><PERSON> gara aga", "stakeRewardsItemsPagingFailed": "Mbup<PERSON><PERSON> ihe nrita ndị kacha ochie a gaghị.", "stakeAccountCreateAndDelegateErrorStaking": "E nwere nsogbu na-enye o mee nkwadoeziokwu nkuchi ruo nwa oge. <PERSON><PERSON> nwaa ọzọ.", "stakeAccountCreateAndDelegateSolStaked": "Akuchiela SOL ruo nwa oge!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "SOL gị ga-ebido erite onyinye ego<1></1>na mkpụrụ ụbọchị ole na ole ozugbo akaụntụ ahụ akuchiri ruo nwa oge bidoro rụwa ọrụ.", "stakeAccountCreateAndDelegateStakingFailed": "<PERSON><PERSON><PERSON> ruo nwa oge <PERSON> nke <PERSON>", "stakeAccountCreateAndDelegateStakingSol": "Na-akuchi SOL ruo nwa oge...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Anyị na emepụta akaụnt<PERSON> aga akuchi ruo nwa oge, mgbe ah<PERSON> na enyefe SOL gị", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Anyị na emepụta akaụnt<PERSON> aga akuchi ruo nwa oge, mgbe ah<PERSON> na enyefe {{validatorName}} SOL gị", "stakeAccountCreateAndDelegateViewTransaction": "<PERSON><PERSON>", "stakeAccountDeactivateStakeSolUnstaked": "Ewepụla nkuchi SOL!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Ị ga-enwe ike ewepụ nkuchi gị<1></1>na mkpụrụ ụbọchị ole na ole ozugbo akaụntụ akuchiri ruo nwa oge kwusiri ịrụ ọrụ.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Ị ga-enwe ike <PERSON>dọrọ ihe nrịta ọmụrụnwa gị na mkpụrụ ụbọchị ole-na-ole ma ọ bụrụ na akaụntụ ọmụrụnwa kuchie.", "stakeAccountDeactivateStakeUnstakingFailed": "Iwepụ N<PERSON> ruo nwa oge Agagh<PERSON> nke <PERSON>ma", "stakeAccountDeactivateStakeUnstakingFailedDescription": "E nwere nsogbu ewepụ nkuchi ruo nwa oge n'aka onye o mee nkwadoeziokwu a. <PERSON><PERSON> nwaa ọzọ.", "stakeAccountDeactivateStakeUnstakingSol": "Na-ewepụ nkuchi SOL ruo nwa oge...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Anyị na amalite usoro iwepụ nkuchi SOL gị ruo nwa oge.", "stakeAccountDeactivateStakeViewTransaction": "<PERSON><PERSON>", "stakeAccountDelegateStakeSolStaked": "Akuchiela SOL ruo nwa oge!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "SOL ga-ebido erite onyinye ego<1></1>n'mkpụr<PERSON>bọchị ole ozugbo akaụnt<PERSON> akuchiri ruo nwa oge bidoro rụwa ọrụ.", "stakeAccountDelegateStakeStakingFailed": "<PERSON><PERSON><PERSON> ruo nwa oge <PERSON> nke <PERSON>", "stakeAccountDelegateStakeStakingFailedDescription": "E nwere nsogbu na-enye o mee nkwadoeziokwu a nkuchi ruo nwa oge. <PERSON><PERSON> nwaa ọz<PERSON>.", "stakeAccountDelegateStakeStakingSol": "Na-akuchi SOL ruo nwa oge...", "stakeAccountDelegateStakeStakingSolDescription": "Anyị na enyefe SOL gị.", "stakeAccountDelegateStakeViewTransaction": "<PERSON><PERSON>", "stakeAccountListActivationActivating": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountListActivationActive": "Nọ n'ọrụ", "stakeAccountListActivationInactive": "Anọgh<PERSON>'ọ<PERSON>ụ", "stakeAccountListActivationDeactivating": "Na-ag<PERSON><PERSON>ụ", "stakeAccountListErrorFetching": "<PERSON><PERSON> enwetanwughị aka<PERSON><PERSON><PERSON> n<PERSON>chi ego. <PERSON><PERSON> nwaa ọzọ ma emecha.", "stakeAccountListNoStakingAccounts": "E nweghi A<PERSON>chi<PERSON> ruo nwa oge", "stakeAccountListReload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountListViewPrimaryText": "<PERSON><PERSON><PERSON> gị ruo nwa oge", "stakeAccountListViewStakeSOL": "Kuchie SOL ruo nwa oge", "stakeAccountListItemStakeFee": "{{fee}} ego nzipụ", "stakeAccountViewActionButtonRestake": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON><PERSON> n<PERSON>", "stakeAccountViewError": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewPrimaryText": "<PERSON><PERSON><PERSON> gị ruo nwa oge", "stakeAccountViewRestake": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewSOLCurrentlyStakedInterpolated": "<PERSON><PERSON><PERSON>ri SOL gị ugbu a n'aka onye o mee nkwadoeziokwu. Ị kwesiri iwepụ nkuchi <1></1> iji nweta ego ndị a.<3><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></3>", "stakeAccountViewStakeInactive": {"part1": "Akaụntụ a akuchiri anọghị n'ọrụ. Chebara echiche ewepu nkuchi ya maọbụ ịchọta onye o mee nkwadoeziokwu aga-enyefe ya n'aka.", "part2": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, "stakeAccountViewStakeNotFound": "<PERSON><PERSON><PERSON><PERSON> ike <PERSON> akaụntu a <PERSON>chiri.", "stakeAccountViewViewOnExplorer": "<PERSON><PERSON> {{explorer}}", "stakeAccountViewWithdrawStake": "<PERSON><PERSON><PERSON> ruo nwa oge", "stakeAccountViewWithdrawUnstakedSOL": "Wepụ SOL nke ana a<PERSON>ghi", "stakeAccountInsufficientFunds": "E nweghị SOL zuru ezu maka ịwepụ nkuchi ma ọ bụ ị dọrọ.", "stakeAccountWithdrawStakeSolWithdrawn": "Ewepụla SOL!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Ewepụla SOL gị.", "part2": "A ga ewepụ akaụntị a akuchiri n'ime nkeji ole na ole na abịanụ n'ak<PERSON>ghị aka."}, "stakeAccountWithdrawStakeViewTransaction": "<PERSON><PERSON>", "stakeAccountWithdrawStakeWithdrawalFailed": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "E nwere nsogbu na ịdọr<PERSON> tokin na akaụntụ a akuchiri. <PERSON><PERSON> nwaa ọzọ.", "stakeAccountWithdrawStakeWithdrawingSol": "Na-ewepụ SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Anyị na-ewepụ SOL gị na akaụntụ a akuchiri.", "startEarningSolAccount": "<PERSON><PERSON><PERSON><PERSON>", "startEarningSolAccounts": "akaụntụ gasị", "startEarningSolErrorClosePhantom": "<PERSON><PERSON><PERSON><PERSON> ebe a ma nwaa <PERSON>", "startEarningSolErrorTroubleLoading": "Nsogbu na-e<PERSON><PERSON>a ekuchi ruo nwa oge", "startEarningSolLoading": "Na-e<PERSON><PERSON>a...", "startEarningSolPrimaryText": "Bido erite SOL", "startEarningSolSearching": "Na-achọ maka aka<PERSON><PERSON><PERSON>", "startEarningSolStakeTokens": "<PERSON><PERSON>e tokin gasị ma rite onyinye ego", "startEarningSolYourStake": "<PERSON><PERSON><PERSON> gị ruo nwa oge", "unwrapFungibleTitle": "<PERSON><PERSON><PERSON><PERSON> {{tokenSymbol}}", "unwrapFungibleDescription": "Dọrọ site {{fromToken}} maka {{toToken}}", "unwrapFungibleConfirmSwap": "<PERSON><PERSON><PERSON>", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Ụgwọ a tụrụ anya ya", "swapFeesFees": "Ụgwọ", "swapFeesPhantomFee": "<PERSON><PERSON> n<PERSON>", "swapFeesPhantomFeeDisclaimer": "Anyị na achọtakarị ọnụego kacha mma enwere ike enweta site na n'aka ndị achụmnta ego kachasịnụ. A ga etinye ego nzipu {{feePercentage}} na ọnụego a na ụzọ mmereonwe.", "swapFeesRate": "Ọnụahịa", "swapFeesRateDisclaimer": "Ọnụego kacha mma sitere na Nweta ahịa Jupiter na ọtụtụ ebe njiko ndị na-azụ ahịa na ndị na ere ahịa.", "swapFeesRateDisclaimerMultichain": "Ọnụego kacha mma e nwere ike inweta n'ebe ịzụ ahịa crypto nke onye na ibe ya.", "swapFeesPriceImpact": "<PERSON><PERSON><PERSON><PERSON>", "swapFeesHighPriceImpact": "<PERSON><PERSON><PERSON><PERSON> Ugw<PERSON> Dị elu", "swapFeesPriceImpactDisclaimer": "Ọdịịche dị n'etiti ọnụ ahịa na mgbakọta ọnụego dabere na ọnụego eji ere ihe.", "swapFeesSlippage": "Ọdịiche na ọnụego at<PERSON>r<PERSON> anya na ọnụego azụtara ahịa", "swapFeesHighSlippage": "Nnagide Ọdị Iche dị Elu na Ọnụego Atụrụ Anya na Ọnụego Azụtara <PERSON>a", "swapFeesHighSlippageDisclaimer": "Nzipụ ego gị agaghị aga ma ọ bụrụ na ọnụegọ gbanwe n'ụzọ na adịghị gị mma karịa {{slippage}}%.", "swapTransferFee": "Ụgwọ Nzipu ego", "swapTransferFeeDisclaimer": "Ị zụkọrịta ${{symbol}} ga-ebute <PERSON>g<PERSON> nzipụ ego {{feePercent}}%, nke onye nkepụta tokịn ah<PERSON> n'abụghị Phantom hiwere.", "swapTransferFeeDisclaimerMany": "Ị zụkọrịta tokịn ndị ahọrọ ga-ebute ụgwọ dị {{feePercent}} nke ndị nkepụta tokịn ahụ n'abụghị Phantom hiwere.", "swapFeesSlippageDisclaimer": "Ọnụego nke ọnụahịa ị ji azụta ihe nwere ike ịgbanwe na ọnụego enyere.", "swapFeesProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapFeesProviderDisclaimer": "<PERSON><PERSON> njiko ndị na-azụ ahịa na ndị na ere ahịa eji emezu a<PERSON>.", "swapEstimatedTime": "Oge <PERSON> anya", "swapEstimatedTimeShort": "<PERSON>ge at<PERSON><PERSON><PERSON>a", "swapEstimatedTimeDisclaimer": "Oge a tụrụ anya maka <PERSON> njikọ ahụ ga-agbado <PERSON> n'ọtụtụ ihe met<PERSON><PERSON> ngwa ngwa nke nzipu ego were dị iche.", "swapSettingsButtonCommand": "Me<PERSON>e <PERSON> Nhazi M<PERSON>ban<PERSON> ihe onwunwe", "swapQuestionRetry": "Nwaa ọzọ?", "swapUnverifiedTokens": "<PERSON><PERSON> ana-<PERSON><PERSON><PERSON>", "swapSectionTitleTokens": "<PERSON><PERSON><PERSON><PERSON> {{section}}", "swapFlowYouPay": "Ị ga Akwụ", "swapFlowYouReceive": "Ị Ga <PERSON>", "swapFlowActionButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ịtụrụ", "swapAssetCardTokenNetwork": "{{symbol}} na {{network}}", "swapAssetCardMaxButton": "<PERSON><PERSON><PERSON><PERSON>", "swapAssetCardSelectTokenAndNetwork": "H<PERSON>rọ Tokin na Netwọk", "swapAssetCardBuyTitle": "Ị ga <PERSON>weta", "swapAssetCardSellTitle": "Ị ga-akwụ", "swapAssetWarningUnverified": "Enyochabeghị Tok<PERSON> a<PERSON> naanị tokịn ndị ị nwere ntụkwasị obi na ha.", "swapAssetWarningPermanentDelegate": "Onye nnọchite anya nwere ike <PERSON>we<PERSON>ụ kpam kpam ma ọ bụ nyefe tokịn ndịa.", "swapSlippageSettingsTitle": "<PERSON><PERSON><PERSON> Ọdịiche ọnụego atụr<PERSON> anya na ọnụego azụtara ahịa", "swapSlippageSettingsSubtitle": "Nzipu ego a agaghị aga ma ọ bụrụ na-enwe mgbanwe n'ọnụahịa karịa ndịiche na ọnụego atụrụ anya. <PERSON>ru dị nnukwu elu ga-eme ka azụmahịa ahụ bụrụ ọghọm ahịa.", "swapSlippageSettingsCustom": "<PERSON><PERSON>zi etu esiri chọ<PERSON>", "swapSlippageSettingsHighSlippageWarning": "<PERSON>zi<PERSON>ụ ego gị nwere ike ịnọrọ na-nchere wee bute azụmahịa na-agaghị eweta uru.", "swapSlippageSettingsCustomMinError": "<PERSON><PERSON>e <PERSON> {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "<PERSON><PERSON>e ọ<PERSON>er<PERSON> {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "Biko tinye ọnụọgụ ziri ezi.", "swapSlippageSettingsAutoSubtitle": "Phantom ga-ach<PERSON><PERSON> nd<PERSON> n'ọn<PERSON><PERSON> at<PERSON><PERSON><PERSON>, n'ọn<PERSON><PERSON> az<PERSON> ahịa nke kacha na <PERSON>, maka mgbanweta ga-aga nke <PERSON>.", "swapSlippageSettingsAuto": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aka", "swapSlippageSettingsFixed": "A kabirila", "swapSlippageOptInTitle": "Emume <PERSON> atụrụ anya ya na ọnụego azụtara ahịa a na-akpagh<PERSON> aka", "swapSlippageOptInSubtitle": "Phantom ga-achọta ndịiche n'ọnụego atụrụanya ya n'ọnụego azụtara ahịa kacha ala maka ịme mgbanweta ga-aga nke ọma. Ị nwere ike ịgbanwe nke a mgbe ọ bụla na Mgbanweta → <PERSON><PERSON>zi ọdịiche n'ọnụego atụrụ anya n'ọnụego azụtara ahịa.", "swapSlippageOptInEnableOption": "Gbanye Ndịiche n'ọnụego atụrụ anya n'ọnụego orire <PERSON> aka", "swapSlippageOptInRejectOption": "Were Nd<PERSON><PERSON> atụr<PERSON>anya ya na-ọn<PERSON>ego az<PERSON>tara ahịa Akabirila Gaa N'ihu", "swapQuoteFeeDisclaimer": "Ọnụego gụnyere ego nzipu Phantom {{feePercentage}}", "swapQuoteMissingContext": "Ọnụego mgbanweta ihe onwunwe adịghị ya", "swapQuoteErrorNoQuotes": "Na-achọ egbanweta ihe onwunwe na-enweghi ndepụta ọnụego", "swapQuoteSolanaNetwork": "Netwok Solana", "swapQuoteNetwork": "Netwọkụ", "swapQuoteOneTimeSerumAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ana-ego otu ugboro", "swapQuoteOneTimeTokenAccount": "<PERSON><PERSON><PERSON><PERSON><PERSON> tokin ana-eji otu ug<PERSON>", "swapQuoteBridgeFee": "Ọnụego <PERSON><PERSON><PERSON><PERSON>", "swapQuoteDestinationNetwork": "Netwọk Ebe nziga", "swapQuoteLiquidityProvider": "Onye Nre ihe onwunwe", "swapReviewFlowActionButtonPrimary": "Mgbanweta", "swapReviewFlowPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Ịtụrụ", "swapReviewFlowYouPay": "Ị ga Akwụ", "swapReviewFlowYouReceive": "Ị Ga <PERSON>", "swapReviewInsufficientBalance": "<PERSON><PERSON> e<PERSON> ezu", "ugcSwapWarningTitle": "Ịdọ aka na ntị", "ugcSwapWarningBody1": "A na-ere tokịn a n'ebe mbipụta tokịn {{<PERSON><PERSON><PERSON><PERSON><PERSON>}}.", "ugcSwapWarningBody2": "<PERSON>ru tokịn ndịa nwere ike <PERSON> n'ụz<PERSON> dị egwu, nke ga-ebute uru mmụbanye ego ma <PERSON> bụ ọghọm. <PERSON><PERSON> kee nkwụcha dịka <PERSON> n'azụkọrị<PERSON>.", "ugcSwapWarningConfirm": "<PERSON><PERSON><PERSON><PERSON> m", "bondingCurveProgressLabel": "<PERSON><PERSON><PERSON><PERSON><PERSON> a <PERSON>a<PERSON> aka nke orire na <PERSON> tok<PERSON>n", "bondingCurveInfoTitle": "N<PERSON>k<PERSON><PERSON><PERSON><PERSON> a <PERSON>'a<PERSON> aka nke orire na <PERSON> tok<PERSON>n", "bondingCurveInfoDescription": "N'usoro njik<PERSON><PERSON><PERSON><PERSON> a n'a<PERSON> aka nke orire na ọzụzụ tokịn, ọ bụ ụdịdị njikọ ahụ n'ekpebi ọnụ ahịa tọkịn. Ọ na-agbago dịka a n'az<PERSON> tokịn, ma na-agbada dịka a na-ere ha. Mgbe erechara tokịn ahụ,  a ga-etinye ego niile na Raydium ma mee ka ha pụọ na nkesa.", "ugcFungibleWarningBanner": "A na-ere To<PERSON>ịn a na {{programName}}", "ugcCreatedRowLabel": "E keputara Na", "ugcStatusRowLabel": "Ọnọdụ", "ugcStatusRowValue": "Ọgafela", "swapTxConfirmationReceived": "Nwetara!", "swapTxConfirmationSwapFailed": "Mg<PERSON><PERSON><PERSON> a<PERSON>", "swapTxConfirmationSwapFailedStaleQuota": "Ọnụego a adịgh<PERSON>zi ire. <PERSON><PERSON>z<PERSON>.", "swapTxConfirmationSwapFailedSlippageLimit": "Ọdịiche na ọnụego atụrụ anya ya na ọnụego azụtara ahịa gị dị oke ala maka mgbanweta a. <PERSON><PERSON> welita aka na ọdịiche na ọnụego atụrụ anya ya na ọnụego azụtara ahịa gị n'elu Mgbanweta ihuenyo ma nwaa ọzọ.", "swapTxConfirmationSwapFailedInsufficientBalance": "Anyị enweghị ike imezu arịrịọ ahụ. Ị nweghị ego fọdụrụnụ zuru ezu iji mezuo nzipụ ego ahụ.", "swapTxConfirmationSwapFailedEmptyRoute": "Ụzọ nrepụ tokin a agbanweela. Anyị e nweghị ike <PERSON>ch<PERSON>tanwu ọnụego dabaranụ. <PERSON><PERSON> nwaa ọzọ maọbụ gbanwee ọnụego tokin ahụ.", "swapTxConfirmationSwapFailedAcountFrozen": "<PERSON>ye nkepụta tokịn a e jichiela ya. Ị gaghị enwe ike i zipụ ma ọ bụ gbanweta tokịn a.", "swapTxConfirmationSwapFailedTryAgain": "<PERSON>g<PERSON><PERSON><PERSON>he onwu<PERSON> a<PERSON>, biko nwaa <PERSON>", "swapTxConfirmationSwapFailedUnknownError": "Anyị enweghị ike <PERSON> ah<PERSON>. O nweghị ihe met<PERSON>tara ego gị ọ bụla. <PERSON><PERSON> n<PERSON>a <PERSON>. ", "swapTxConfirmationSwapFailedSimulationTimeout": "<PERSON><PERSON> emenwughị oyiri mg<PERSON> ah<PERSON>. O nweghị ihe met<PERSON><PERSON> ego gị ọ bụla. <PERSON><PERSON> n<PERSON>a <PERSON>.", "swapTxConfirmationSwapFailedSimulationUnknownError": "<PERSON>ị enweghughị ike mezuo mg<PERSON>ta ah<PERSON>. Onweghị ego gị nke a metụtara. <PERSON><PERSON> n<PERSON>. ", "swapTxConfirmationSwapFailedInsufficientGas": "<PERSON><PERSON><PERSON><PERSON><PERSON> gị enweghị ego zuru iji mezuo nzipu ego ahụ. <PERSON><PERSON> tinyekwuo ego na akaụntụ gị ma nwaa ọzọ.", "swapTxConfirmationSwapFailedLedgerReject": "<PERSON><PERSON> njiarụ kagburu mgbanweta ahụ na akụrụngwa nke ọzọ.", "swapTxConfirmationSwapFailedLedgerConnectionError": "A jụrụ ime mgbanweta ah<PERSON> n'ihi nsogbu njikọ akụrụngwa. Biko n<PERSON>a ọzọ.", "swapTxConfirmationSwapFailedLedgerSignError": "A jụrụ ime mgbanweta ah<PERSON> n'ihi nsogbu mbanye nke akụrụngwa. <PERSON><PERSON> n<PERSON> ọz<PERSON>.", "swapTxConfirmationSwapFailedLedgerError": "A jụrụ ime mgbanweta ah<PERSON> n'ihi nsogbu ak<PERSON><PERSON><PERSON>. Biko nwaa ọzọ.", "swapTxConfirmationSwappingTokens": "Na-ag<PERSON><PERSON>ta tokin...", "swapTxConfirmationTokens": "Tokin gasị", "swapTxConfirmationTokensDeposited": "Emechala ya! E<PERSON>yela tokin na ak<PERSON>ego gị", "swapTxConfirmationTokensDepositedTitle": "E mechaala!", "swapTxConfirmationTokensDepositedBody": "<PERSON> <PERSON><PERSON> tokin n'ak<PERSON>ego gị", "swapTxConfirmationTokensWillBeDeposited": "ga etinye ya na akpaego gị ozugbo emechara nzipụ egp", "swapTxConfirmationViewTransaction": "<PERSON><PERSON>", "swapTxBridgeSubmitting": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "swapTxBridgeSubmittingDescription": "Mgbanweta {{sellAmount}} na {{sellNetwork}} maka {{buyAmount}} na {{buyNetwork}}", "swapTxBridgeFailed": "Nzipu ego E zigaghị", "swapTxBridgeFailedDescription": "<PERSON><PERSON> enweghị ike mezuo arịr<PERSON><PERSON> ah<PERSON>.", "swapTxBridgeSubmitted": "E zigala Nzipu ego", "swapTxBridgeSubmittedDescription": "<PERSON><PERSON> anya <PERSON> zipu ego: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "Ị nwere ike imechi windo a n'enweghị nsogbu.", "swapperSwitchTokens": "Gban<PERSON>ta <PERSON>", "swapperMax": "<PERSON><PERSON><PERSON><PERSON>", "swapperTooltipNetwork": "Netwọkụ", "swapperTooltipPrice": "Ọnụahịa", "swapperTooltipAddress": "Nkwek<PERSON><PERSON><PERSON><PERSON>", "swapperTrendingSortBy": "Hazie site na", "swapperTrendingTimeFrame": "<PERSON><PERSON>", "swapperTrendingNetwork": "Netwọk", "swapperTrendingRank": "Ọkwa", "swapperTrendingTokens": "<PERSON><PERSON><PERSON><PERSON> nd<PERSON>", "swapperTrendingVolume": "Ọnụọgụgụ ole", "swapperTrendingPrice": "Ọnụahịa", "swapperTrendingPriceChange": "Mgbanwe Ọnụahịa", "swapperTrendingMarketCap": "<PERSON><PERSON>", "swapperTrendingTimeFrame1h": "Awa 1", "swapperTrendingTimeFrame24h": "Awa 24", "swapperTrendingTimeFrame7d": "Ụbọchị 7", "swapperTrendingTimeFrame30d": "Ụbọchị 30", "swapperTrendingNoTokensFound": "O nwegh<PERSON> tok<PERSON>n <PERSON> a h<PERSON>.", "switchToggle": "Bọtinụ mgbanye na mgbanyu", "termsOfServiceActionButtonAgree": "Ekwetere M", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Site na ipi <1>\"Ekwetere M\"</1>ị na ekwete <3><PERSON><PERSON> Ọrụ</3>nke eji <PERSON> mg<PERSON>ta tokin.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "<PERSON>i edezighar<PERSON>ala Iwu Usoro Ọrụ anyị. Site na ipi <1>\"Ekwetere M\"</1>ị na ekweta na <3>Iwu Usoro ọrụ ọhụrụ anyị</3>.<5></5><6></6>Iwu Usoro Ọrụ ọhụrụ anyị gụnyere <8>ndepụta ọnụego</8>maka ụfọdụ ihe.", "termsOfServicePrimaryText": "<PERSON><PERSON> Ọrụ", "tokenRowUnknownToken": "<PERSON><PERSON> ana-am<PERSON>", "transactionsAppInteraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsFailedAppInteraction": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> a <PERSON>h<PERSON>", "transactionsBidOnInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsBidFailed": "<PERSON><PERSON><PERSON> m <PERSON>n<PERSON> a gaghị", "transactionsBoughtInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsBoughtCollectible": "<PERSON><PERSON> onwu<PERSON>we <PERSON>", "transactionBridgeInitiated": "E bidola Njikọ", "transactionBridgeInitiatedFailed": "<PERSON><PERSON><PERSON>", "transactionBridgeStatusLink": "Lelee Ọnọdụ na LI.FI", "transactionsBuyFailed": "<PERSON><PERSON> a gaghị", "transactionsBurnedSpam": "<PERSON><PERSON> ndị ezitere ana-a<PERSON><PERSON><PERSON><PERSON> ewepụrụ", "transactionsBurned": "Ebibiela", "transactionsUnwrapped": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsUnwrappedFailed": "<PERSON><PERSON><PERSON><PERSON> a gaghị", "transactionsCancelBidOnInterpolated": "Nkagbu ekwe m ọnụ na {{name}} ", "transactionsCancelBidOnFailed": "Nkagbu ekwe m <PERSON> aga<PERSON>", "transactionsError": "<PERSON><PERSON><PERSON><PERSON>", "transactionsFailed": "Ọgaghị", "transactionsSwapped": "A gbanwetala", "transactionsFailedSwap": "Mg<PERSON><PERSON><PERSON> a<PERSON>", "transactionsFailedBurn": "<PERSON><PERSON><PERSON> a <PERSON>h<PERSON>", "transactionsFrom": "Site na", "transactionsListedInterpolated": "<PERSON><PERSON><PERSON><PERSON><PERSON>{{name}}", "transactionsListedFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON> a gaghị", "transactionsNoActivity": "<PERSON>we<PERSON>i ihe omume", "transactionsReceived": "Nwetara", "transactionsReceivedInterpolated": "Nwetara {{amount}} SOL", "transactionsSending": "Na-ezi<PERSON>ụ...", "transactionsPendingCreateListingInterpolated": "<PERSON>me<PERSON>ụ<PERSON> {{name}}", "transactionsPendingEditListingInterpolated": "<PERSON><PERSON><PERSON>{{name}}", "transactionsPendingSolanaPayTransaction": "<PERSON><PERSON><PERSON><PERSON>kwu Nzipụ ego Solana Pay", "transactionsPendingRemoveListingInterpolated": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ị maka orire {{name}}", "transactionsPendingBurningInterpolated": "Iwepu {{name}}", "transactionsPendingSending": "<PERSON><PERSON><PERSON>", "transactionsPendingSwapping": "<PERSON>gban<PERSON><PERSON>", "transactionsPendingBridging": "Ijikọ", "transactionsPendingApproving": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsPendingCreatingAndDelegatingStake": "Imepụta na inye nkuchi coin ruo nwa oge", "transactionsPendingDeactivatingStake": "Imechi nkuchi coin ruo nwa oge", "transactionsPendingDelegatingStake": "Inye nkuchi coin ruo nwa oge", "transactionsPendingWithdrawingStake": "Iwepu nkuchi coin ruo nwa oge", "transactionsPendingAppInteraction": "<PERSON><PERSON>k<PERSON><PERSON><PERSON><PERSON> nke ana-emeb<PERSON><PERSON><PERSON> nkwa<PERSON> ya", "transactionsPendingBitcoinTransaction": "Nzipú BTC na-agabeghí", "transactionsSent": "Ezigala", "transactionsSendFailed": "<PERSON><PERSON><PERSON><PERSON> a gaghị", "transactionsSwapOn": "Mgbanwete ihe onwunwe na {{dappName}}", "transactionsSentInterpolated": "Ezigala {{amount}} SOL", "transactionsSoldInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsSoldCollectible": "<PERSON><PERSON> onwunwe ererela", "transactionsSoldFailed": "<PERSON><PERSON> a gaghị", "transactionsStaked": "<PERSON><PERSON><PERSON><PERSON> ruo nwa oge", "transactionsStakedFailed": "<PERSON><PERSON><PERSON><PERSON> a gaghị", "transactionsSuccess": "Ọ gara nke <PERSON>", "transactionsTo": "<PERSON>uo", "transactionsTokenSwap": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "transactionsUnknownAmount": "<PERSON><PERSON>", "transactionsUnlistedInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsUnstaked": "<PERSON><PERSON><PERSON><PERSON>", "transactionsUnlistedFailed": "<PERSON> <PERSON>we<PERSON><PERSON> go<PERSON>", "transactionsDeactivateStake": "Nkụchi e mechirila", "transactionsDeactivateStakeFailed": "Mmechi n<PERSON>ụchi a gaghị", "transactionsWaitingForConfirmation": "Na-eche maka eme nkwa<PERSON>ez<PERSON>wu", "transactionsWithdrawStake": "Wepụ Nkuchi coin ruo nwa oge", "transactionsWithdrawStakeFailed": "Mwepụ nk<PERSON>chi a gaghị", "transactionCancelled": "<PERSON><PERSON><PERSON><PERSON>", "transactionCancelledFailed": "<PERSON>kag<PERSON> a gaghị", "transactionApproveToken": "{{tokenSymbol}} A kwadoro", "transactionApproveTokenFailed": "Gbajọ<PERSON><PERSON> {{tokenSymbol}}", "transactionApprovalFailed": "Nkwado a gaghị", "transactionRevokeApproveToken": "Kagburu {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> kagbu {{tokenSymbol}}", "transactionRevokeFailed": "<PERSON>kag<PERSON> a gaghị", "transactionApproveDetailsTitle": "Nkọwa gbasara Nkwado", "transactionCancelOrder": "<PERSON><PERSON><PERSON><PERSON> a t<PERSON>", "transactionCancelOrderFailed": "Nkag<PERSON> ihe at<PERSON><PERSON><PERSON> a gaghị", "transactionApproveAppLabel": "Aapụ", "transactionApproveAmountLabel": "Ọnụ ego", "transactionApproveTokenLabel": "<PERSON><PERSON>", "transactionApproveCollectionLabel": "<PERSON>he eserese nile dị na dịg<PERSON>", "transactionApproveAllItems": "<PERSON><PERSON><PERSON> ihe niile", "transactionSpendUpTo": "<PERSON><PERSON>o ruo", "transactionCancel": "<PERSON><PERSON><PERSON><PERSON>", "transactionPrioritizeCancel": "Kag<PERSON>o nke ka <PERSON>pa Ibute Ụzọ", "transactionSpeedUp": "<PERSON><PERSON> n<PERSON> ego Osiso", "transactionCancelHelperText": "<PERSON>zi<PERSON>ụ ego nke mbu nwere ike <PERSON>gacha tupu akagbuo ya.", "transactionSpeedUplHelperText": "Nke a ga eme ka ịga osiso nzipụ ego gị gbagotekwuo site na etu ọnọdụ netwọk siri dị.", "transactionCancelHelperMobile": "Ọ ga ewe <1> ihe ruru {{amount}}</1> ịnwa ịkagbu nzipụ ego a. Nzipụ ego nke mbu nwere ike ịgacha tupu akagbuo ya.", "transactionCancelHelperMobileWithEstimate": "Ọ ga-ewe <1> ihe ruru {{amount}}</1> ịnwa nkagbu nzipụ ego a. Ọ ga-emecha n'ihe dịka {{timeEstimate}}. Nzipụ ego nke mbu nwere ike ịgacha tupu akagbuo ya.", "transactionSpeedUpHelperMobile": "Ọ ga-ewe <1>ihe ruru {{amount}}</1> iji mee ka <PERSON>ga ọsịsọ nzipụ ego a gbagotekwuo.", "transactionSpeedUpHelperMobileWithEstimate": "Ọ ga-ewe <1> ihe ruru {{amount}}</1>iji mee ka <PERSON>ga ọsịsọ nzipụ ego a gbagotekwuo. Ọ kwesịrị imecha n'ihe dịka {{timeEstimate}}.", "transactionEstimatedTime": "<PERSON>ge at<PERSON><PERSON><PERSON>a", "transactionCancelingSend": "Ikagbu nzipụ", "transactionPrioritizingCancel": "Ịkagbu nke ka mkpa ibute <PERSON>", "transactionCanceling": "Ikagbu", "transactionReplaceError": "E nwere njehie merenụ. Enwebeghị ego ndị anara n'akaụntụ gị. Ị nwere ike ịnwa ọzọ.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} ezugh<PERSON> ezu", "transactionGasLimitError": "Mgbakọ ụgwọ kacha a ga-akwụ maka i zipụ ego a gaghị", "transactionGasEstimationError": "Mgbakọ ụgwọ a ga-akwụ i zipu ego a gaghị", "pendingTransactionCancel": "<PERSON><PERSON><PERSON><PERSON>", "pendingTransactionSpeedUp": "<PERSON><PERSON> ngwa ngwa", "pendingTransactionStatus": "Ọnọdụ", "pendingTransactionPending": "<PERSON><PERSON> ana-<PERSON><PERSON><PERSON><PERSON><PERSON> nk<PERSON>", "pendingTransactionPendingInteraction": "Mmek<PERSON><PERSON><PERSON><PERSON> nke ana-<PERSON><PERSON><PERSON><PERSON><PERSON> ya", "pendingTransactionCancelling": "Ikagbu", "pendingTransactionDate": "<PERSON><PERSON>", "pendingTransactionNetworkFee": "<PERSON>go <PERSON>gw<PERSON> nzipụ ego", "pendingTransactionEstimatedTime": "<PERSON>ge at<PERSON><PERSON><PERSON>a", "pendingTransactionEstimatedTimeHM": "{{hours}} awa {{minutes}} nkeji", "pendingTransactionEstimatedTimeMS": "{{minutes}} nkeji {{seconds}} nkebi", "pendingTransactionEstimatedTimeS": "{{seconds}} nkebi", "pendingTransactionsSendingTitle": "Izipụ {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "<PERSON><PERSON>", "pendingTransactionUnknownApp": "<PERSON><PERSON><PERSON>", "permanentDelegateTitle": "<PERSON><PERSON>", "permanentDelegateValue": "Na-adigide adigide", "permanentDelegateTooltipTitle": "Nyefe na-adigide adigide", "permanentDelegateTooltipValue": "Nji I<PERSON>ke tokịn n'enye ohere ka akaụntụ ọzọ n'ahazi tokịn n'aha gị, nke a gụnyere mwepụ ma ọ bụ nyefe.", "unlockActionButtonUnlock": "<PERSON><PERSON><PERSON><PERSON>", "unlockEnterPassword": "<PERSON><PERSON> passw<PERSON><PERSON><PERSON><PERSON> gị", "unlockErrorIncorrectPassword": "Passwọọdụ na e<PERSON>hi ezi", "unlockErrorSomethingWentWrong": "E nwere ihe na e<PERSON>hi ezi, biko nwaa ọzọ ma emecha", "unlockForgotPassword": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "unlockPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "forgotPasswordText": "Ị nwere ike <PERSON> passwọọdụ gị site na itinye mkpụrụ<PERSON><PERSON> nzu<PERSON> nwetaghachị akpaego gị 12 ruo 24. Phantom agaghị eweghachitere gị passwọọdụ gị.", "appInfo": "<PERSON><PERSON> nke aap<PERSON>", "lastUsed": "Oge e jiri ya Ikpeazụ", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Ọ dịghị maka aka<PERSON>nt<PERSON> ndị nwere ngwa nchekwa ak<PERSON>ego.", "trustedAppAutoConfirmDisclaimer1": "Mgbe ọ nọ n'ọrụ, Phantom ga-akwa<PERSON> arịrị<PERSON> nile si na aapụ a na-emeghị ka <PERSON>mara ma ọ bụ ịjụ maka nkwado.", "trustedAppAutoConfirmDisclaimer2": "Ịgbanye nwere ike itinye ego gị n'ihe ize ndụ mpụ. <PERSON><PERSON> ihe omume mee ihe naanị na aapụ ndị ịtụkwasịrị obi.", "validationUtilsPasswordIsRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validationUtilsPasswordLength": "Passwọodụ ga ad<PERSON><PERSON><PERSON><PERSON><PERSON> ogologo mkp<PERSON><PERSON><PERSON><PERSON><PERSON> 8", "validationUtilsPasswordsDontMatch": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "validationUtilsPasswordCantBeSame": "Ị gaghị enwe ike eji passwọọdụ ochie gị", "validatorCardEstimatedApy": "APY a na-atụ anya ya", "validatorCardCommission": "Ego", "validatorCardTotalStake": "Ngụkọta nkuchi coin nile ruo nwa oge", "validatorCardNumberOfDelegators": "# nke <PERSON>d<PERSON> nnweta ego pasent n<PERSON><PERSON> aka<PERSON>nt<PERSON>", "validatorListChooseAValidator": "Horo Onye o mee nkwadoeziokwu", "validatorListErrorFetching": "<PERSON><PERSON> enwetenwugh<PERSON> nhazi nkwa<PERSON>ez<PERSON>. <PERSON><PERSON> nwaa ọzọ ma emecha.", "validatorListNoResults": "<PERSON><PERSON><PERSON><PERSON>", "validatorListReload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validatorInfoTooltip": "Onye o mee nkwadoeziokwu", "validatorInfoTitle": "Ndị o mee nkwadoeziokwu", "validatorInfoDescription": "Site n'itinye SOL gị na nhazi nkwado-eziokwu, Ị na akwado ọrụ nakwa nchekwa nke netwọk Solana, na-erita kwa SOL n'otu aka ahụ.", "validatorApyInfoTooltip": "APY E mere at<PERSON><PERSON> ya", "validatorApyInfoTitle": "APY a na-atụ anya ya", "validatorApyInfoDescription": "Nke a bụ ọnụego ị ga-enweta site na ịkụchi SOL gị nwa mgbe na nhazi nkwadoeziokwu.", "validatorViewActionButtonStake": "<PERSON><PERSON><PERSON> ruo nwa oge", "validatorViewErrorFetching": "<PERSON>we<PERSON>i ike enweta ndị o mee nkwadoez<PERSON>kwu.", "validatorViewInsufficientBalance": "<PERSON><PERSON> fọ<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ezughi ezu", "validatorViewMax": "<PERSON><PERSON><PERSON><PERSON>", "validatorViewPrimaryText": "Bido Nkuchi coin ruo nwa oge", "validatorViewDescriptionInterpolated": "Họrọ SOL ole ị ga achọ <1></1>e<PERSON><PERSON> ruo nwa oge n'aka onye o mee nkwadoeziokwu a. <3>Mụtakwụọ</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOl ach<PERSON>r<PERSON> ekuchi ruo nwa oge", "validatorViewValidator": "Onye o mee nkwadoeziokwu", "walletMenuItemsAddConnectWallet": "Tinye/Jikọọ Akpaego", "walletMenuItemsBridgeAssets": "<PERSON><PERSON> n<PERSON>", "walletMenuItemsHelpAndSupport": "Enyemaka na Nkwado", "walletMenuItemsLockWallet": "<PERSON><PERSON><PERSON>", "walletMenuItemsResetSecretPhrase": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "walletMenuItemsShowMoreAccounts": "<PERSON><PERSON><PERSON><PERSON> {{count}}...", "walletMenuItemsHideAccounts": "<PERSON><PERSON>", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "<PERSON><PERSON><PERSON>", "disableMultiChainDetail1Header": "Gbalịa ike g<PERSON> nile na Solana", "disableMultiChainDetail1SecondaryText": "<PERSON><PERSON><PERSON><PERSON>, to<PERSON>, na <PERSON><PERSON> onwunwe dịg<PERSON>ụ gị n'ahụghị chaịnụ ndị <PERSON>zọ.", "disableMultiChainDetail2Header": "Gaghachi na Multichain oge ọ<PERSON>ụla", "disableMultiChainDetail2SecondaryText": "A ga-echekwa ego Ethereum na Polygon gi ka fọdụrụ mgbe ịgbanyegharịrị Multichain.", "disableMultiChainButton": "Gbanye nke Naanị <PERSON>", "disabledMultiChainHeader": "Agbanyela naanị nke <PERSON>", "disabledMultiChainText": "Ị nwere ike <PERSON> multichain oge ọbụla.", "enableMultiChainHeader": "Gbanye Multichain", "enabledMultiChainHeader": "A gbanyere Multichain", "enabledMultiChainText": "E nweziri nkwadozi maka Ethereum na Polygon n'ime akpaego gị ugbua.", "incompatibleAccountHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "incompatibleAccountInterpolated": "<PERSON>iko wepụ naanị akaụntụ Ethereum ndị a tupu ịgbanye naanị ọnọd<PERSON>: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Gịnị mere Ọhụrụ!", "welcomeToMultiChainPrimaryText": "<PERSON><PERSON> a<PERSON> maka ihe nile", "welcomeToMultiChainDetail1Header": "Nkwado Ethereum na Polygon", "welcomeToMultiChainDetail1SecondaryText": "Tokịn gị nile na NFT sitere na Solana, Ethereum na Polygon n'otu ebe.", "welcomeToMultiChainDetail2Header": "<PERSON><PERSON> a<PERSON> nile <PERSON> n'anya mee ihe", "welcomeToMultiChainDetail2SecondaryText": "Jikọọ aapụ na ọtụtụ ụdọ-njikọ n'agbanweghị netwọk.", "welcomeToMultiChainDetail3Header": "Webata akpaego MetaMask gị", "welcomeToMultiChainDetail3SecondaryText": "Webata mkpụrụokwu mkpọchi Ethereum na Polygon gị na mfe.", "welcomeToMultiChainIntro": "Nnọọ n'Phantom nke <PERSON> na Ọtụtụ Ebe", "welcomeToMultiChainIntroDesc": "Tokin nile na NFT gị nile si na Solana, Ethereum, nakwa Polygon n'otu ebe. Otu ak<PERSON>ego gị maka ime ihe nile.", "welcomeToMultiChainAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON> multichain", "welcomeToMultiChainAccountsDesc": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> maka multichain, aka<PERSON><PERSON><PERSON> <PERSON> bụla nwe<PERSON>ri adreesị ETH na Polygon yana ha kwekoro ugbu a.", "welcomeToMultiChainApps": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "welcomeToMultiChainAppsDesc": "Phantom na aapụ nile dị n'Ethereum, Polygon, na Solana <PERSON>. <PERSON><PERSON><PERSON> \"Jikọọ na MetaMask\" <PERSON> wee dị njikere ibido.", "welcomeToMultiChainImport": "Si na MetaMask bubata, ozugbo", "welcomeToMultiChainImportDesc": "Si na akpaego dịka MetaMask maọbụ Akpaego Coinbase bubata Okwu Nzuzo gị maọbụ Nọmba nzuzo mbanye. Ha nile n'otu ebe.", "welcomeToMultiChainImportInterpolated": "<0>Si na akpaego dịka MetaMask maọbụ akpaego Coinbase bubata Okwu Nzuzo gị</0> ma<PERSON><PERSON><PERSON> Nọmba nzuzo mbanye. Ha nile n'otu ebe.", "welcomeToMultiChainTakeTour": "<PERSON><PERSON>a", "welcomeToMultiChainSwapperTitle": "Gbanweta na Ethereum, \nPolygon, na Solana", "welcomeToMultiChainSwapperDetail1Header": "Nkwado Ethereum na Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Ugbu a <PERSON> ga enwe ike <PERSON>ta tokin ERC-20 site n'ime akpaego gị.", "welcomeToMultiChainSwapperDetail2Header": "Ọnụego Kacha <PERSON> ya na Ụgwọ ọkwụkwụ Dị ala nke <PERSON>wu", "welcomeToMultiChainSwapperDetail2SecondaryText": "Ụzọ 100+ e si erepụ nakwa usoro ọgbara ọhụrụ e ji enweta ọnụ ahịa kacha mma maka ịnweta nnukwu uru ahịa.", "networkErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "networkError": "Ọ dị nwute na anyị enweghị ike ịnweta netwok. <PERSON><PERSON> nwaa ọzọ ma emechaa.", "authenticationUnlockPhantom": "Kpoghee Phantom", "errorAndOfflineSomethingWentWrong": "E nwere ihe na ezighi ezi", "errorAndOfflineSomethingWentWrongTryAgain": "<PERSON><PERSON>.", "errorAndOfflineUnableToFetchAssets": "<PERSON><PERSON> enweghị ike enweta ihe onwunwe. <PERSON><PERSON>.", "errorAndOfflineUnableToFetchCollectibles": "<PERSON><PERSON> enweghị ike enweta ihe onwunwe dịg<PERSON>. <PERSON><PERSON>.", "errorAndOfflineUnableToFetchSwap": "Anyị enweghi ike enweta ozi mgbanweta ihe onwunwe. <PERSON><PERSON> nwaa ọzọ ma emechaa.", "errorAndOfflineUnableToFetchTransactionHistory": "<PERSON><PERSON> e nweghí ike inweta ndeko ozi nzipụ ego gị nile ugbu a. <PERSON> njiko netwọkụ gị, ma<PERSON><PERSON><PERSON> nwaa ọzọ ma emechaa.", "errorAndOfflineUnableToFetchRewardsHistory": "<PERSON><PERSON> enwenwughị ike <PERSON>ta ndekọ ihe nrita. <PERSON><PERSON> nwaa ọzọ ma emecha.", "errorAndOfflineUnableToFetchBlockedUsers": "<PERSON>ị enweghị ike nweta ndị e gbochirila. <PERSON><PERSON> nwaa ọzọ ma emecha.", "networkHealthSheetCloseButtonText": "ODỊMMA", "swapReviewError": "E nwere ihe na ezighi ezi mgbe ana-enyochagharị ihe <PERSON>ụ<PERSON>ụ, biko nwaa ọzọ.", "sendSelectToken": "<PERSON><PERSON><PERSON><PERSON>", "swapBalance": "<PERSON><PERSON> f<PERSON>:", "swapTitle": "<PERSON><PERSON><PERSON><PERSON>", "swapSelectToken": "<PERSON><PERSON><PERSON><PERSON>", "swapYouPay": "Ị ga Akwụ", "swapYouReceive": "Ị Ga <PERSON>", "aboutPrivacyPolicy": "<PERSON><PERSON>", "aboutVersion": "V<PERSON><PERSON>{{version}}", "aboutVisitWebsite": "Gaa na weebusaịtị", "bottomSheetConnectTitle": "Jikọọ", "A11YbottomSheetConnectTitle": "Njiko <PERSON> ozi mmapụta ala aap<PERSON>", "A11YbottomSheetCommandClose": "<PERSON><PERSON>ụ <PERSON> ozi mmapụta ala aap<PERSON>", "A11YbottomSheetCommandBack": "Nlaghachi Ihe ozi mmapụta ala aap<PERSON>", "bottomSheetSignTypedDataTitle": "<PERSON><PERSON><PERSON> aka d<PERSON><PERSON><PERSON><PERSON>", "bottomSheetSignMessageTitle": "<PERSON><PERSON><PERSON> aka d<PERSON><PERSON><PERSON><PERSON>", "bottomSheetSignInTitle": "Banye", "bottomSheetSignInAndConnectTitle": "Banye", "bottomSheetConfirmTransactionTitle": "<PERSON><PERSON><PERSON>", "bottomSheetConfirmTransactionsTitle": "K<PERSON><PERSON>", "bottomSheetSolanaPayTitle": "Arịrịọ Akwụm ụgwọ na Solana", "bottomSheetAdvancedTitle": "<PERSON><PERSON> elu", "bottomSheetReadOnlyAccountTitle": "Ọnọdụ nke <PERSON>", "bottomSheetTransactionSettingsTitle": "Ego ụgwọ Nzipụ Ego", "bottomSheetConnectDescription": "<PERSON><PERSON><PERSON> ga<PERSON>enye saịtị a ohere ilele ego fọdụrụnụ na ihe ndị na-eme na akaụnt<PERSON> ahọr<PERSON>.", "bottomSheetSignInDescription": "Ị binye aka na ozi a ga-egosi na ọ bụ gị nwe aka<PERSON><PERSON><PERSON> ndi a họpútara. <PERSON><PERSON> aka  naanị ozi sitere na akụrụngwa ndị ị tụkwasịrị obi.", "bottomSheetSignInAndConnectDescription": "Ị kwado ga-ekwe ka webụsaịtị a hụ ego fọdụrụ nakwa ihe omume maka akaụntụ a họrọ.", "bottomSheetConfirmTransactionDescription": "Mgbanwe n'ego fọd<PERSON><PERSON><PERSON> bụ nke a na-atụ anya ya. Ón<PERSON>ego nakwa ihe onwunwe abụghị nke eji n'aka.", "bottomSheetConfirmTransactionsDescription": "Mgbanwe n'ego fọd<PERSON><PERSON><PERSON> bụ nke a na-atụ anya ya. Ón<PERSON>ego nakwa ihe onwunwe abụghị nke eji n'aka.", "bottomSheetSignTypedDataDescription": "Nke a bụ naanị arịrịọ ínara ikike. <PERSON><PERSON><PERSON><PERSON> ego a nwere ike ọ gaghị a ga ozugbo.", "bottomSheetSignTypedDataSecondDescription": "Mgbanwe n'ego fọd<PERSON><PERSON><PERSON> bụ nke a na-atụ anya ya. Ón<PERSON>ego nakwa ihe onwunwe abụghị nke eji n'aka.", "bottomSheetSignMessageDescription": "Ị kwado ozi a ga-egosi na ọ bụ gị nwe akaụntụ ndị ahọrọ. Kwado naanị ozi sitere na akụrụngwa ndị ị tụkwasịrị obi.", "bottomSheetReadOnlyAccountDescription": "E nweghị ike imenwu ihe a n'ọnọdụ nke naanị nlele.", "bottomSheetMessageRow": "<PERSON><PERSON>", "bottomSheetStatementRow": "Ozi <PERSON>wa", "bottomSheetAutoConfirmRow": "Nkwado n'akpaghị aka", "bottomSheetAutoConfirmOff": "Gbanyụọ", "bottomSheetAutoConfirmOn": "Gbanye", "bottomSheetAccountRow": "<PERSON><PERSON>", "bottomSheetAdvancedRow": "<PERSON><PERSON> elu", "bottomSheetContractRow": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetSpenderRow": "<PERSON><PERSON><PERSON><PERSON> ndi enyere i<PERSON>", "bottomSheetNetworkRow": "Netwọkụ", "bottomSheetNetworkFeeRow": "Ego ụgwọ Nzipụ Ego", "bottomSheetEstimatedTimeRow": "Oge <PERSON> anya ya", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Jikọọ naanị na webụsaịtị ndị ị tụkwasịrị obị", "bottomSheetSignInRequestDisclaimer": "Banye naanị na webụsaịtị ndị ị tụkwasịr<PERSON> obi", "bottomSheetSignatureRequestDisclaimer": "<PERSON><PERSON><PERSON> naanị ma ọ bụrụ na ị nwere ntụkwasịobi na webụsaịtị a.", "bottomSheetFeaturedTransactionDisclaimer": "Ị ga-ah<PERSON> nhụtụrụ nke nzipu ego ahụ tupu ịnye nkwado na nzom ụkwụ nke n'esote.", "bottomSheetIgnoreWarning": "<PERSON><PERSON><PERSON> <PERSON> dọ aka na ntị, gaa n'ihu n'agban<PERSON>gh<PERSON>", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "E nweghị mgbanwe na ego fọdụ<PERSON><PERSON><PERSON><PERSON>. Biko gaa n'ihu na nlezianya ma mee nkwadoeziokwu ma ọbụrụ na ị nwere ntụkwasi obi na saịtị a.", "bottomSheetReadOnlyWarning": "Ị ga elele adreesị a naanị. Ọ ga-adị mkpa ibubata iji nwee ike <PERSON> aka na nzipụ ego nakwa ozi.", "bottomSheetWebsiteIsUnsafeWarning": "<PERSON>eb<PERSON><PERSON><PERSON>tị a nọ n'ihe ize ndụ iji, o nwere ike <PERSON>nwa izu ego gị.", "bottomSheetViewOnExplorer": "<PERSON><PERSON> ya <PERSON>ebe nch<PERSON>ta ihe", "bottomSheetTransactionSubmitted": "E zigala Nzipu ego ahụ", "bottomSheetTransactionPending": "Nzipu ego N'echere", "bottomSheetTransactionFailed": "Nzipu ego A gaghị", "bottomSheetTransactionSubmittedDescription": "E zigala nzipu ego gị. Ị nwere ike <PERSON>hụ ya na-ebe nchọta ihe.", "bottomSheetTransactionFailedDescription": "Nzipu ego gị agagh<PERSON>. <PERSON><PERSON>.", "bottomSheetTransactionPendingDescription": "A na-eme nzipu ego ahụ...", "transactionsFromInterpolated": "Site na:{{from}}", "transactionsFromParagraphInterpolated": "Site {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON>", "transactionsToInterpolated": "Gaa na:{{to}}", "transactionsToParagraphInterpolated": "Ruo {{to}}", "transactionsYesterday": "Ụnyaahụ", "addEditAddressAdd": "<PERSON><PERSON> ad<PERSON>", "addEditAddressDelete": "<PERSON><PERSON><PERSON><PERSON><PERSON> ad<PERSON>", "addEditAddressDeleteTitle": "E ji n'aka na i chọr ọ ehichapụ adreesi a?", "addEditAddressSave": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserComingSoon": "ihe nchọgharị dApp na-abịa n'oge na-adịghị anya!", "dAppBrowserSearchPlaceholder": "<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, ad<PERSON><PERSON> web<PERSON>", "dAppBrowserOpenInNewTab": "<PERSON><PERSON><PERSON> n'ibe nch<PERSON><PERSON><PERSON>", "dAppBrowserSuggested": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> a<PERSON> ya", "dAppBrowserFavorites": "<PERSON><PERSON> kacha amas<PERSON>", "dAppBrowserBookmarks": "Ndekọba <PERSON>ụ<PERSON>", "dAppBrowserBookmarkAdd": "<PERSON><PERSON><PERSON>", "dAppBrowserBookmarkRemove": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserUsers": "<PERSON><PERSON><PERSON>", "dAppBrowserRecents": "Omerenso a", "dAppBrowserFavoritesDescription": "A ga egosi nke kacha amasị gị ebe a", "dAppBrowserBookmarksDescription": "A ga-egosi n<PERSON> webụsaịtị gị niile ebe a", "dAppBrowserRecentsDescription": "Dapp niile e jikọrọ nso nso a ga-egosi ebe a", "dAppBrowserEmptyScreenDescription": "Pịa URL maọbụ ch<PERSON> weeb<PERSON>", "dAppBrowserBlocklistScreenTitle": "<PERSON><PERSON><PERSON><PERSON> {{origin}}! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom kwenyere na weebusaịtị a ga emebi ihe nakwa ọ dịghị mma iji.", "part2": "Akaala saịtị a dịka otu n'ime database nke otu igwe mmadụ emere ka ọ na arụ ọrụ nke ọma nke amaara dịka weebusait na-eji aghụghọ anakọta ihe ọmụma ndị mmadụ nakwa egwu wayo. Ọ bụrụ na ị chere na akara weebusaiti a na njehie, biko tinye akwụkwọ mkpesa."}, "dAppBrowserLoadFailedScreenTitle": "<PERSON><PERSON><PERSON><PERSON> a <PERSON>h<PERSON>", "dAppBrowserLoadFailedScreenDescription": "E nwere ịda mba na mbudata ibe webụsaịtị a", "dAppBrowserBlocklistScreenIgnoreButton": "<PERSON><PERSON><PERSON> aka n'<PERSON><PERSON> anya, go<PERSON> n'ag<PERSON><PERSON><PERSON>", "dAppBrowserActionBookmark": "Ndekọba <PERSON>ụ<PERSON>", "dAppBrowserActionRemoveBookmark": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserActionRefresh": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "dAppBrowserActionShare": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "dAppBrowserActionCloseTab": "<PERSON><PERSON><PERSON>", "dAppBrowserActionEndAutoConfirm": "<PERSON><PERSON><PERSON><PERSON><PERSON> a na-a<PERSON> aka", "dAppBrowserActionDisconnectApp": "We<PERSON>ụ n<PERSON> a<PERSON>ụ", "dAppBrowserActionCloseAllTabs": "<PERSON><PERSON><PERSON>", "dAppBrowserNavigationAddressPlaceholder": "Debanye webụsaị<PERSON>ị i ji ch<PERSON>", "dAppBrowserTabOverviewMore": "Ọzọ", "dAppBrowserTabOverviewAddTab": "<PERSON><PERSON>be n<PERSON>a ihe ịntanetị", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON><PERSON>", "dAppBrowserClose": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewRemoveBookmark": "<PERSON><PERSON><PERSON><PERSON>", "depositAssetListSuggestions": "Arọ gasị", "depositUndefinedToken": "Ya ewutela g<PERSON>, enweghi ike etinye tokin a", "onboardingImportRecoveryPhraseDetails": "Nkọwa", "onboardingCreateRecoveryPhraseVerifyTitle": "<PERSON><PERSON><PERSON><PERSON>nwetaghachị?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Ma i<PERSON><PERSON>i okwu n<PERSON>zo nnwetaghachị gị ị gaghị enwe ike enweta kii gị maọbụ ihe onwunwe ọ bụla jikoro ya.", "onboardingCreateRecoveryPhraseVerifyYes": "Ee", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON> aga<PERSON> n<PERSON>, biko nwaa <PERSON>.", "onboardingDoneDescription": "Ị ga enwe ike inweta ọṅụ zuru ezu ugbu a na akpaego gị.", "onboardingDoneGetStarted": "Bido", "zeroBalanceHeading": "Ka anyị bido!", "zeroBalanceBuyCryptoTitle": "<PERSON>ụ<PERSON> Crypto", "zeroBalanceBuyCryptoDescription": "<PERSON><PERSON> kaadị ndoro ego ma<PERSON><PERSON><PERSON> kaadị njìakwụụgwọ zụta crypto mbu gị.", "zeroBalanceDepositTitle": "Zipụ Cry<PERSON>o", "zeroBalanceDepositDescription": "Tinye crypto site na akpaego ọzọ maọbụ mgbanweta.", "onboardingImportAccountsEmptyResult": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "onboardingImportAccountsAccountName": "<PERSON><PERSON><PERSON><PERSON> {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "{{walletIndex}} <PERSON><PERSON> n<PERSON> m ah<PERSON>a", "onboardingImportAccountsFoundAccounts_one": "<PERSON><PERSON> hụtara {{numberOfWallets}} ihe emere n'akaụ<PERSON>", "onboardingImportAccountsFoundAccounts_other": "<PERSON><PERSON> hụtara {{numberOfWallets}} ihe emere n'akaụ<PERSON> gasị", "onboardingImportAccountsFoundAccountsNoActivity_one": "<PERSON><PERSON> hụtara {{numberOfWallets}} aka<PERSON><PERSON><PERSON>", "onboardingImportAccountsFoundAccountsNoActivity_other": "<PERSON><PERSON> hụtara {{numberOfWallets}} akaụntụ gasị", "onboardingImportRecoveryPhraseLessThanTwelve": "Mkpuru okwu kwesiri idi opekampe okwu 12.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Mkpuru okwu kwesiri idi okwu 12 maọbụ 24 kpomkwem.", "onboardingImportRecoveryPhraseWrongWord": "<PERSON>wu na ezighi ezi: {{ words }}.", "onboardingProtectTitle": "Chekwaa <PERSON>", "onboardingProtectDescription": "<PERSON><PERSON><PERSON> nchekwa na-eji akara mkpịsị aka ga-agba mbo hụ na ọ bụ naanị gị ga-enwe ikike ịba n'akpaego gị.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "<PERSON><PERSON> na eme nkwadoezio<PERSON>", "onboardingProtectButtonHeadlineFingerprint": "Akara mkpịsị aka", "onboardingProtectButtonHeadlinePIN": "JIGIDE", "onboardingProtectButtonSubheadline": "<PERSON><PERSON> {{ authType }} <PERSON><PERSON> n<PERSON>", "onboardingProtectError": "E nwere ihe na ezighi ezi mgbe ana-eme nkwa<PERSON>ez<PERSON>, biko nwaa ọzọ", "onboardingProtectBiometryIosError": "<PERSON><PERSON><PERSON><PERSON><PERSON> ngwa nyocha mkpịsị aka na ihu n'ime Phantom, mana e si n'ime Ebe Nhazi ihe n'ekwentị gbanyụọ ya. <PERSON><PERSON> ga na, <PERSON><PERSON> > Phantom > <PERSON><PERSON><PERSON> ma <PERSON>ụ Nyocha <PERSON>ị aka iji gbanyeghachi ya.", "onboardingProtectRemoveAuth": "Gbanyuo nkwadoez<PERSON>wu", "onboardingProtectRemoveAuthDescription": "E ji n'aka na <PERSON> chọr<PERSON> igbanyu nkwadoeziokwu?", "onboardingWelcomeTitle": "Nnọọ na Phantom", "onboardingWelcomeDescription": "<PERSON><PERSON>, me<PERSON>e a<PERSON><PERSON><PERSON><PERSON> ma<PERSON>ụ bubata nke dịbụ ad<PERSON>.", "onboardingWelcomeCreateWallet": "<PERSON><PERSON><PERSON><PERSON>", "onboardingWelcomeAlreadyHaveWallet": "<PERSON><PERSON><PERSON><PERSON> m r<PERSON><PERSON>", "onboardingWelcomeConnectSeedVault": "Jikọọ Ebe Ndebe Akpaego", "onboardingSlide1Title": "Ọ bụ gị na-ejikwa ya", "onboardingSlide1Description": "<PERSON><PERSON> m<PERSON> mkp<PERSON><PERSON>we, n<PERSON><PERSON><PERSON><PERSON><PERSON> mp<PERSON>wa en<PERSON> 24/7 were chekwaba a<PERSON>pa<PERSON> g<PERSON>.", "onboardingSlide2Title": "Ụlọ kacha mma maka NFT gị", "onboardingSlide2Description": "Hazie etu igosip<PERSON>ta maka orire si aga, we<PERSON><PERSON> nzite tokin ana-achọghị, ma nọrọ na ịnata ozi ọhụrụ site na nziokwa na enye aka nke na abịa n'ak<PERSON>ghị aka.", "onboardingSlide3Title": "Mee ihe ndị ọzọ site na iji tokin gị", "onboardingSlide3Description": "<PERSON><PERSON><PERSON><PERSON>, g<PERSON><PERSON><PERSON>, k<PERSON><PERSON>, z<PERSON><PERSON>, ma nata — n'ahapụghị akpaego gị. ", "onboardingSlide4Title": "Chọpụta Web3 nke kacha mma", "onboardingSlide4Description": "<PERSON><PERSON> <PERSON>he nch<PERSON>gharị dị n'ime aapụ wee chọta ma jikọọ eserese dịgịtalụ na aapụ kacha eme nke <PERSON>ma.", "onboardingMultichainSlide5Title": "<PERSON><PERSON> a<PERSON> maka ihe nile", "onboardingMultichainSlide5Description": "<PERSON><PERSON><PERSON> nile n<PERSON>, Ethereum, na Polygon n'otu ihe njiko ngwa kọmpụta dị mfe ojiji.", "onboardingMultichainSlide5DescriptionWithBitcoin": "<PERSON><PERSON>ta <PERSON>, Ethereum, Polygon na Bitcoin nile n'otu ihe njiko ngwa kọmpụta dị mfe ojiji.", "requireAuth": "Ga-achọ eme nkwadoeziokwu", "requireAuthImmediately": "Ozugbo", "availableToSend": "Ọ d<PERSON>la ma<PERSON>", "sendEnterAmount": "<PERSON><PERSON>", "sendEditMemo": "<PERSON><PERSON>", "sendShowLogs": "<PERSON><PERSON>", "sendHideLogs": "Zoo Ndeko <PERSON>", "sendGoBack": "<PERSON><PERSON><PERSON><PERSON>", "sendTransactionSuccess": "<PERSON>zi<PERSON><PERSON> tokin gị gara nke <PERSON>", "sendInputPlaceholder": "@ahambanye ma ọ bụ adreesị", "sendInputPlaceholderV2": "aha mbanye ma <PERSON> bụ adreesị", "sendPeopleTitle": "<PERSON>dị mmad<PERSON>", "sendDomainTitle": "Ad<PERSON>sị nzipu na nweta ego", "sendFollowing": "N'eso", "sendRecentlyUsedAddressLabel": "<PERSON><PERSON> {{formattedTimestamp}} oge gara aga", "sendRecipientAddress": "<PERSON><PERSON><PERSON> onye nweta", "sendTokenInterpolated": "Zipụ {{tokenSymbol}}", "sendPasteFromClipboard": "Tinye site na klipbọọdụ", "sendScanQR": "See QR Koodu", "sendTo": "Na:", "sendRecipientZeroBalanceWarning": "<PERSON><PERSON><PERSON><PERSON> akpaego a enweghị ego nfọd<PERSON>, ọ dịghị egosikwa na ndekọ nzipụ ego ndị ị mere nso nso a. <PERSON><PERSON> gbaa mbọ hụ na adreesị ahụ ziri ezi.", "sendUnknownAddressWarning": "<PERSON><PERSON> ab<PERSON>ghị adressị i jirila mee ihe nso nso a. <PERSON><PERSON> kee nkwụcha dịka <PERSON> na-aga n'ihu.", "sendSameAddressWarning": "Nke a bụ adreesị gị ugbua. Ịme nzipu ga-akpata akwụ m ụgwọ ego nzipu n'enweghị mgbanwe ọzọ ọbụla n'ego fọdụrụ.", "sendMintAccountWarning": "Nke a bụ aka<PERSON><PERSON><PERSON> adreesị mbiputa. Ị nweghị ike izipu ego gaa na-adreesị a, maka na nke a ga-ebute ego nfunyụ anya.", "sendCameraAccess": "Nweta <PERSON>", "sendCameraAccessSubtitle": "<PERSON>ji see <PERSON><PERSON> kood<PERSON>, ek<PERSON><PERSON> ig<PERSON>ye nweta igwefoto. Ị ga achọ emepe nhazi ugbu a?", "sendSettings": "Nhazi gasị", "sendOK": "ODỊMMA", "invalidQRCode": "<PERSON><PERSON><PERSON> QR a amakọghị.", "sendInvalidQRCode": "<PERSON><PERSON>u QR a abụgh<PERSON> adreesi ana-anabata", "sendInvalidQRCodeSubtitle": "Nwaa <PERSON>z<PERSON> maọbụ jiri koodu QR ọzọ.", "sendInvalidQRCodeSplToken": "<PERSON><PERSON> am<PERSON> na koodu QR", "sendInvalidQRCodeSplTokenSubtitle": "QR koodu a nwere tokin nke na abụghị gị nwe ya maọbụ anyị enweghi ike imata ya.", "sendScanAddressToSend": "See {{tokenSymbol}} ad<PERSON><PERSON> iji zipụ ego", "sendScanAddressToSendNoSymbol": "See adreesị iji zip<PERSON> ego", "sendScanAddressToSendCollectible": "See ad<PERSON><PERSON>l ka izipụ ihe onwunwe dịg<PERSON>", "sendScanAddressToSendCollectibleMultichain": "See adreesị iji zip<PERSON> ihe onwunwe dị<PERSON>", "sendSummary": "Nchịkọta", "sendUndefinedToken": "Ya ewutela g<PERSON>, enweghi ike ezipụ tokin a", "sendNoTokens": "<PERSON><PERSON><PERSON><PERSON> tokin d<PERSON>n<PERSON>", "noBuyOptionsAvailableInCountry": "Enwegh<PERSON> nhọrọ Ịzụta dị na obodo gị", "swapAvailableTokenDisclaimer": "<PERSON>k<PERSON><PERSON> ole-na-ole dị maka <PERSON> ọwara njikọ n'etiti Netwọk", "swapCrossSwapNetworkTooltipTitle": "Mgbanweta Gazuo Netwọk", "swapCrossSwapNetworkTooltipDescription": "Mgbe ị na-eme mgbanweta gazuru Netwọk, a na-adụ ọdụ ka e jiri tokin ndị dị maka mweta ọnụ ego kacha ala nakwa i zipụ ego ọsịsọ.", "settingsAbout": "Banyere Phantom", "settingsShareAppWithFriends": "<PERSON><PERSON><PERSON> nd<PERSON> enyi gị", "settingsConfirm": "Ee", "settingsMakeSureNoOneIsWatching": "Gbaa mbo hụ na enweghi onye na-ele ihuenyo gi anya", "settingsManageAccounts": "Jikwaa Akaụntụ gasị", "settingsPrompt": "E ji n'aka na <PERSON> chọr<PERSON> n'ihu?", "settingsSelectAvatar": "Họrọ Ava<PERSON>", "settingsSelectSecretPhrase": "<PERSON><PERSON><PERSON><PERSON> okwu n<PERSON>zo", "settingsShowPrivateKey": "<PERSON><PERSON><PERSON> aka iji gosi n<PERSON> n<PERSON> mbanye gị", "settingsShowRecoveryPhrase": "<PERSON><PERSON><PERSON> aka iji gosi okwu n<PERSON>zo gị", "settingsSubmitBetaFeedback": "<PERSON><PERSON><PERSON>", "settingsUpdateAccountNameToast": "E melitela aha akaụ<PERSON>", "settingsUpdateAvatarToast": "E melitela Avatar", "settingsUpdateAvatarToastFailure": "Ntinye foto nnọchi ihu onye agaghị!", "settingsWalletAddress": "<PERSON><PERSON><PERSON><PERSON>", "settingsWalletAddresses": "Ad<PERSON>sị <PERSON>", "settingsWalletNamePrimary": "<PERSON><PERSON>", "settingsPlaceholderName": "<PERSON>a", "settingsWalletNameSecondary": "Gbanwee aha ak<PERSON>ego gị", "settingsYourAccounts": "<PERSON><PERSON><PERSON><PERSON>", "settingsYourAccountsMultiChain": "Nke a ga eji ọ<PERSON>ụtụ ugboro", "settingsReportUser": "<PERSON><PERSON><PERSON>ye n<PERSON>", "settingsNotifications": "Nziọkwa gasị", "settingsNotificationPreferences": "Nhọrọ Nziọkwa", "pushNotificationsPreferencesAllowNotifications": "Gbanye Nziọkwa", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON><PERSON>", "pushNotificationsPreferencesSentTokensDescription": "Nzipụ ego tokin na NFT site n'intaneti", "pushNotificationsPreferencesReceivedTokens": "<PERSON><PERSON><PERSON>", "pushNotificationsPreferencesReceivedTokensDescription": "<PERSON><PERSON>ta <PERSON>gwọ ego si na mba ofisi maka tokin na NFT", "pushNotificationsPreferencesDexSwap": "Mgbanweta ihe onwunwe", "pushNotificationsPreferencesDexSwapDescription": "Mgbanwete ihe onwunwe na ngwaọrụ ama-ama", "pushNotificationsPreferencesOtherBalanceChanges": "Mgbanwe <PERSON> fod<PERSON>", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Ọtụtụ nzipụ ego tokin ndị ọzọ metụtara ego g<PERSON> fodurunu", "pushNotificationsPreferencesPhantomMarketing": "<PERSON><PERSON>re na Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Nziokwa ndị egosiri na ozi ọ<PERSON>ụ<PERSON> nile", "pushNotificationsPreferencesDescription": "Nhazi a na achikwa ozi enwetara na ihuenyo njiarụ maka akpaego a nọ n'ọrụ. Akpaego ọ bụla nwere nhazi nziọkwa nke ha. Iji gbanyuo nweta ozi Phantom nile na ihuenyo njiarụ, biko gaa na <1>nhazi njiarụ</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Enweghi ike eme ka nhọrọ nziọkwa rukoo ọrụ.", "connectSeedVaultConnectSeed": "Jikoo <PERSON><PERSON><PERSON>", "connectSeedVaultConnectSeedDescription": "Jikọọ Phantom na Ebe Ndebe Akpaego dị na ekwentị gị", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeed": "<PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeedDescription": "<PERSON><PERSON><PERSON><PERSON> mkp<PERSON><PERSON><PERSON><PERSON><PERSON> nwe<PERSON> nke ọ ga-amasị gị iji jikọọ na Phantom", "connectSeedVaultSelectAnAccountDescription": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> nke <PERSON> ga-achọ <PERSON> na <PERSON>", "connectSeedVaultNoAccountsFound": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON>.", "connectSeedVaultSelectAccounts": "<PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultSelectAccountsDescription": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> nke <PERSON> ga-achọ <PERSON> na <PERSON>", "connectSeedVaultCompleteSetup": "<PERSON><PERSON><PERSON>", "connectSeedVaultCompleteSetupDescription": "Ị dịla njikere! <PERSON><PERSON> mee nchọgharị web3 ma jiri <PERSON>be Ndebe Akpaego gị mee nkwadoeziokwu nke nzipụ ego", "connectSeedVaultConnectAnotherSeed": "Jikọọ Passw<PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultConnectAllSeedsConnected": "<PERSON> jik<PERSON>la okwu nwe<PERSON>chi nile", "connectSeedVaultNoSeedsConnected": "Enweghị passwọ<PERSON><PERSON><PERSON> nwetaghachị ejikọrọ. <PERSON>pa<PERSON>ụ bọtịnụ dị n'okpuru iji nye ikike site na Ebe Ndebe Akpaego.", "connectSeedVaultConnectAccount": "Jik<PERSON><PERSON> aka<PERSON>", "connectSeedVaultLoadMore": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultNeedPermission": "Chọrọ nnata ikike", "connectSeedVaultNeedPermissionDescription": "Gaa na Ebe Nhazi iji nye Phantom ikike iji Ebe Ndebe Akpaego mee ihe.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} ego nzipụ", "stakeAmount": "Ọnụego", "stakeAmountBalance": "<PERSON><PERSON>", "swapTopQuotes": "Ọnụego {{numQuotes}} <PERSON><PERSON><PERSON><PERSON>", "swapTopQuotesTitle": "<PERSON><PERSON> N<PERSON><PERSON>", "swapProvidersTitle": "Ebe-nre-ah<PERSON>a", "swapProvidersFee": "{{fee}} ego nzipụ", "swapProvidersTagRecommended": "Ọmụrụnwa Kacha mma", "swapProvidersTagFastest": "<PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}} awa {{minutes}} nkeji", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}} nkebi", "stakeReview": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "stakeReviewAccount": "<PERSON><PERSON>", "stakeReviewCommissionFee": "Pasent Ego ere eserese", "stakeReviewConfirm": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>", "stakeReviewValidator": "Onye o mee nkwadoeziokwu", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Mgbanweta Nkuchi Coin Agaghị", "convertStakeStatusErrorMessage": "A<PERSON>gh<PERSON> enwe <PERSON> coin gị akuchiri ka ọ ghọọ {{poolTokenSymbol}}. <PERSON><PERSON>.", "convertStakeStatusLoadingTitle": "<PERSON>g<PERSON><PERSON><PERSON> ka <PERSON> bụ<PERSON> {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Anyị na amalite usoro igbanweta {{stakedTokenSymbol}} gị aku<PERSON>ri ka <PERSON> bụr<PERSON> {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "<PERSON>g<PERSON><PERSON><PERSON> ka <PERSON> {{poolTokenSymbol}} emechaala!", "convertStakeStatusSuccessMessage": "Nweta ihe nrite ndị ọzọ site na eji JitoSOL gị <1>ebea.</1>", "convertStakeStatusConvertMore": "Gbanwetakwuo", "convertStakePendingTitle": "Igbanweta coin a<PERSON><PERSON><PERSON> ka obụrụ {{symbol}}", "convertToJitoSOL": "Gbanweta ka <PERSON> b<PERSON>ụ JitoSOL", "convertToJitoSOLInfoDescription": "Gbanweta SOL gị ka ọ bụrụ Jito SOL ka ịnweta ihe nrite wee sonye na ndị otu <PERSON>.", "convertToJitoSOLInfoTitle": "Gbanweta ka <PERSON> b<PERSON>ụ JitoSOL", "convertStakeBannerTitle": "Gbanwe nkụchị k<PERSON>ịn gị gaa JitoSOL i ji bulite ihe nrite ruo 15%", "convertStakeQuestBannerTitle": "Gbanwe SOL akụchirila gaa JitoSOL ma nweta ihe nrite. Mụtakwuo.", "liquidStakeConvertInfoTitle": "Gbanweta ka <PERSON> b<PERSON>ụ JitoSOL", "liquidStakeConvertInfoDescription": "Kwalite ihe nrite gị site na ịgbanweta nkuchi SOL gị ka ọ ghọọ JitoSOL. <1>M<PERSON><PERSON>kwuo</1>", "liquidStakeConvertInfoFeature1Title": "Gịnị mere ị chọrọ eji Jito kuchie coin?", "liquidStakeConvertInfoFeature1Description": "Tinye ego iji nweta JitoSOL, nke na-eso nkuchi gị eto. <PERSON>ri ya na ukpuru DeFi maka iritekwu ego. Gbanweta JitoSOL gị ma emesia maka ọnụego mbu gị + ihe nrite enwetaworo", "liquidStakeConvertInfoFeature2Title": "<PERSON>he nrite <PERSON>kara kacha elu", "liquidStakeConvertInfoFeature2Description": "<PERSON><PERSON> na-<PERSON><PERSON>ego kacha ala ekesa SOL gị n'etiti ndị o mee nkwadoeziokwu kacha mma. Ihe nrite MEV na-eme ka ego ị na-erite na-abawanye.", "liquidStakeConvertInfoFeature3Title": "Nye netwọk Solana nkwado", "liquidStakeConvertInfoFeature3Description": "Nkuchi tokin aga erepụnwu osiso na-echekwa Solana site na ekesa nkuchi tokin ruo n'ọtụtụ ndị o mee nkwadoeziokwu, na-ebelata ihe ize ndụ sitere n'aka ndị o mee nkwadoeziokwu nwere obere ohere ịnọ n'ịntanetị.", "liquidStakeConvertInfoSecondaryButton": "Ọ b<PERSON>gh<PERSON> ugbua", "liquidStakeStartStaking": "Bido Nkụchi kọịn", "liquidStakeReviewOrder": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "convertStakeAccountListPageIneligibleSectionTitle": "Akaụntụ ndị Akuchiri coin ruo nwa oge na Etozughi Etozu", "convertStakeAccountIneligibleBottomSheetTitle": "Akaụntụ ndị Akuchiri coin ruo nwa oge <PERSON>", "convertStakeAccountListPageErrorTitle": "<PERSON> nwetaghị aka<PERSON><PERSON><PERSON> n<PERSON>", "convertStakeAccountListPageErrorDescription": "<PERSON>we ewe<PERSON> g<PERSON>, o nwere ihe n'agaghị ọfụma ma mee n'anyị enwetaghị akaụnt<PERSON> nkụchi k<PERSON>ịn ndị ahụ", "liquidStakeReviewYouPay": "Ị Ga-akwụ", "liquidStakeReviewYouReceive": "Ị Ga <PERSON> nweta", "liquidStakeReviewProvider": "Ebe-nre-ah<PERSON>a", "liquidStakeReviewNetworkFee": "Ego ụgwọ Nzipụ Ego", "liquidStakeReviewPageTitle": "Nkwadoeziokwu", "liquidStakeReviewConversionFootnote": "Mgbe ị kụchiri tokịn <PERSON> ma gbanweta JitoSOL, Ị ga e nweta JitoSol ọnụọgụgụ ya dịtụ obere. <1><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON></1>", "convertStakeAccountIneligibleBottomSheetDescription": "<PERSON>be nkụchi kọịn <PERSON>to n'akwado ngwa omeire Solana nke kacha arụ ọrụ. Ị gaghị enwe ike ịgbanwe SOL akụchirila, site na omeire a n'akwadoghị ga JitoSOL. <PERSON>, SOL akụchiri <PERSON>hụrụ ga-ewe mkpụrụ ụbọchị ~2 tupu o tozue maka mgbanwe na JitoSOL.", "selectAValidator": "Họrọ Onye o mee nkwadoeziokwu", "validatorSelectionListTitle": "Họrọ Onye o mee nkwadoeziokwu", "validatorSelectionListDescription": "H<PERSON><PERSON><PERSON> onye o mee nkwadoeziokwu ka o kuchie SOL gị na.", "stakeMethodDescription": "Nweta ọmụrụnwa site n'iji tokịn SOL gị nyere Solana aka ị gbago. <1>M<PERSON>takwuo</1>", "stakeMethodRecommended": "A kwadoro", "stakeMethodEstApy": "Est. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Nkụ<PERSON><PERSON> k<PERSON>ịn maka nweta tok<PERSON>n", "stakeMethodSelectionLiquidStakingDescription": "Kụchie SOL maka Ị nweta ihe nrita dị elu. <PERSON><PERSON> aka chekwa Solana ka ị nweta JitoSOL i ga-eji nwetakwu ihe nrite ndị ọzọ.", "stakeMethodSelectionNativeStakingTitle": "Nkụchi k<PERSON>ị<PERSON> ma<PERSON> n<PERSON>kwa", "stakeMethodSelectionNativeStakingDescription": "Kụchie SOL i ji nweta ihe nrite dịka <PERSON> na-enye aka n'ịchekwa Solana.", "liquidStakeMintStakeSOL": "Mee n<PERSON>chị SOL", "mintJitoSOLInfoPageTitle": "<PERSON><PERSON><PERSON><PERSON> nkuzi i ji jito mee nkụchị kọịn maka nweta tokịn", "mintJitoSOLFeature1Title": "Ọ gịnị mere ị ga-eji were <PERSON><PERSON> mee nk<PERSON>chi?", "mintJitoSOLFeature1Description": "Tinye ị ji nweta JitoSOL nke na-eso nkụchi gị eto. Were ya mee ihe n'usoro iwu DeFi maka i ritekwu ego. Gbanwe JitoSOL gị ma emecha maka ego mbụ gị + nchịkọta ihe nrite ndị ị nwetarala", "mintJitoSOLFeature2Title": "<PERSON>he nrite <PERSON>kara kacha elu", "mintJitoSOLFeature2Description": "Jito na-ekesa SOL gị nye ndị o mee nkwadoeziokwu nwere ụgwọ kacha dị ala. Ihe nrite MEV na-ebulite kwu ihe ị ga-enweta.", "mintJitoSOLFeature3Title": "Nye netwọk Solana nkwado", "mintJitoSOLFeature3Description": "Ịme nkụchị kọịn maka nweta <PERSON>, n'echekwa Solana, site n'ịkesa nkụchi ruo ọtụtụ ndị o mee nkwa<PERSON>ez<PERSON>, ma wetuo ọghọm si n'aka ndị o mee nkwadoeziokwu n'enwetaghị ọtụtụ mbinye aka.", "mintLiquidStakePendingTitle": "Mbipụta nk<PERSON>chi k<PERSON> maka nweta ọmụr<PERSON> tok<PERSON>n", "mintStakeStatusErrorTitle": "Mbipụta N<PERSON>ụ<PERSON> k<PERSON>ị<PERSON> i ji nweta ọmụr<PERSON>wa tokịn A <PERSON>hị", "mintStakeStatusErrorMessage": "E nweghị ike bipụta {{poolTokenSymbol}} nk<PERSON>chi k<PERSON>ịn i ji nweta ọmụrụnwa tokịn gị. <PERSON><PERSON> nwa<PERSON>.", "mintStakeStatusSuccessTitle": "Mbipụta {{poolTokenSymbol}} nkụchi k<PERSON>ịn i ji nweta ọmụrụnwa tokịn e mezuola!", "mintStakeStatusLoadingTitle": "Mbipụta {{poolTokenSymbol}} nk<PERSON><PERSON> k<PERSON>ịn i ji nweta ọmụrụnwa tokịn", "mintStakeStatusLoadingMessage": "Anyị n'amalite njem usoro i bipụta nkụchi kọịn i ji nweta ọmụrụnwa tokịn {{poolTokenSymbol}} gị.", "mintLiquidStakeAmountDescription": "Họrọ SOL ole ị chọrọ ị ji <PERSON>e", "mintLiquidStakeAmountProvider": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "mintLiquidStakeAmountApy": "Est. APY", "mintLiquidStakeAmountBestPrice": "Ọnụahịa", "mintLiquidStakeAmountInsufficientBalance": "<PERSON><PERSON> f<PERSON><PERSON><PERSON><PERSON>", "mintLiquidStakeAmountMinRequired": "<PERSON> k<PERSON> inwe {{amount}} {{symbol}} i ji mee nk<PERSON><PERSON> k<PERSON>n", "swapTooltipGotIt": "<PERSON><PERSON><PERSON><PERSON> ya", "swapTabInsufficientFunds": "<PERSON><PERSON> ezughi ezu", "swapNoAssetsFound": "<PERSON> nweghị ihe onwunwe", "swapNoTokensFound": "<PERSON> nwegh<PERSON> tokin ah<PERSON>", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON>", "swapConfirmationGoBack": "<PERSON><PERSON><PERSON><PERSON>", "swapNoQuotesFound": "<PERSON><PERSON><PERSON><PERSON>", "swapNotProviderFound": "<PERSON><PERSON> enweghi ike <PERSON> eben<PERSON>h<PERSON>a maka mgbanweta tokin a. <PERSON> to<PERSON>.", "swapAvailableOnMainnet": "<PERSON><PERSON> a dị na naanị Mainnet", "swapNotAvailableEVM": "Mgbanwete ihe onwunwe adịbeghị maka akaụntụ EVM gasị", "swapNotAvailableOnSelectedNetwork": "Mgbanweta ad<PERSON> na netwọk a họrọ", "singleChainSwapTab": "Na Netwọ<PERSON>", "crossChainSwapTab": "Gazuo Netwọk", "allFilter": "<PERSON><PERSON>", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bridgeRefuelDescription": "Ị mejuputa na-eme ka ị nwe ike ịkwụ ụgwọ nzipu ego ka ịmechara njikọ.", "bridgeRefuelLabelBalance": "{{symbol}} <PERSON><PERSON> gị", "bridgeRefuelLabelReceive": "Ị Ga <PERSON>", "bridgeRefuelLabelFee": "Ọnụ ahịa A tụrụ anya", "bridgeRefuelDismiss": "Gaa n'ihu n'emeg<PERSON>ị Mmeju", "bridgeRefuelEnable": "Gbanye Mmejuputa", "unwrapWrappedSolError": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "unwrapWrappedSolLoading": "Na-ewe<PERSON>ụ...", "unwrapWrappedSolSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unwrapWrappedSolViewTransaction": "<PERSON><PERSON>", "dappApprovePopupSignMessage": "<PERSON><PERSON><PERSON> aka d<PERSON><PERSON><PERSON><PERSON>", "solanaPayFrom": "Site na", "solanaPayMessage": "<PERSON><PERSON>", "solanaPayNetworkFee": "<PERSON>go <PERSON>gw<PERSON> nzipụ ego", "solanaPayFree": "N'efu", "solanaPayPay": "<PERSON><PERSON><PERSON><PERSON>w<PERSON> {{item}}", "solanaPayPayNow": "Kwụọ Ugbu a", "solanaPaySending": "Na-eziga {{item}}", "solanaPayReceiving": "Na a nabata {{item}}", "solanaPayMinting": "Na e kepụta {{item}}", "solanaPayTransactionProcessing": "<PERSON>zi<PERSON><PERSON> ego g<PERSON> na-aga, biko chere.", "solanaPaySent": "E zipụla!", "solanaPayReceived": "E nwetala!", "solanaPayMinted": "E kep<PERSON>tala!", "solanaPaySentNFT": "E zigala NFT!", "solanaPayReceivedNFT": "E nwetala NFT!", "solanaPayTokensSent": "<PERSON><PERSON><PERSON> tok<PERSON>n g<PERSON> na {{to}} gara nke <PERSON>", "solanaPayTokensReceived": "Ị nwetere tokịn <PERSON> site n'aka {{from}}", "solanaPayViewTransaction": "<PERSON><PERSON>", "solanaPayTransactionFailed": "Nzipụ ego Agaghị", "solanaPayConfirm": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>", "solanaPayTo": "na", "dappApproveConnectViewAccount": "<PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "deepLinkInvalidLink": "Njikọ amakọghị", "deepLinkInvalidSplTokenSubtitle": "Nke a nwere tokin na abụghị gị nwe maọbụ anyị enweghi imata ya.", "walletAvatarShowAllAccounts": "<PERSON><PERSON> aka<PERSON><PERSON><PERSON>", "pushNotificationsGetInstantUpdates": "Nweta ozi <PERSON> oz<PERSON>bu", "pushNotificationsEnablePushNotifications": "Gbanye nweta ozi na ihuenyo njiarụ banyere nzipụ ego emezuru, mgbanwete coin na nziọkwa gasị", "pushNotificationsEnable": "Gbanye", "pushNotificationsNotNow": "Ọbụghị ugbua", "onboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON><PERSON><PERSON><PERSON> m na<1> <PERSON><PERSON> Ọrụ</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "ỌDỊMMA, e<PERSON><PERSON><PERSON> m ya ebe <PERSON>", "onboardingCreateNewWallet": "<PERSON><PERSON><PERSON><PERSON> Ọhụrụ", "onboardingErrorDuplicateSecretRecoveryPhrase": "<PERSON><PERSON> nzuzo a dịbụ adị n'akpaego gị", "onboardingErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON>zo Nnwetaghachị e<PERSON>hi ezi", "onboardingFinished": "Ị mechaala!", "onboardingImportAccounts": "Bụbata Akaụntụ gasị", "onboardingImportImportingAccounts": "Bụbata Akaụntụ gasị...", "onboardingImportImportingFindingAccounts": "Ịchọta akaụnt<PERSON> nwere ihe omume", "onboardingImportAccountsLastActive": "Nọ n'ọrụ {{formattedTimestamp}} n'oge gara aga", "onboardingImportAccountsNeverUsed": "Ejibeghi M<PERSON>", "onboardingImportAccountsCreateNew": "<PERSON><PERSON><PERSON><PERSON>", "onboardingImportAccountsDescription": "<PERSON><PERSON><PERSON><PERSON>", "onboardingImportReadOnlyAccountDescription": "<PERSON>e adreesị maọbụ aha domain ị ga-achọ ilele. Ị ga-enwe ohere nlele naanị, ị gaghị enwe ike <PERSON> aka na nzipụ ego maọbụ ozi.", "onboardingImportSecretRecoveryPhrase": "<PERSON><PERSON><PERSON>", "onboardingImportViewAccounts": "Lelee Akaụntụ gasị", "onboardingRestoreExistingWallet": "<PERSON><PERSON> mk<PERSON><PERSON><PERSON><PERSON> okwu n<PERSON>zo nnwetaghachị 12 maọbụ 24 wee nwetaghachị akpaego dị<PERSON><PERSON> adị", "onboardingShowUnusedAccounts": "<PERSON><PERSON>", "onboardingShowMoreAccounts": "<PERSON><PERSON><PERSON><PERSON>", "onboardingHideUnusedAccounts": "Zoo Akaụntụ <PERSON>", "onboardingSecretRecoveryPhrase": "<PERSON><PERSON>", "onboardingSelectAccounts": "<PERSON><PERSON><PERSON><PERSON>", "onboardingStoreSecretRecoveryPhraseReminder": "Nke a bụ naanị <PERSON> i ga esi enwetaghachị aka<PERSON><PERSON><PERSON> gị. Biko chekwaa ya ebe dị mma!", "useTokenMetasForMintsUnknownName": "<PERSON><PERSON>", "timeUnitMinute": "n<PERSON><PERSON>", "timeUnitMinutes": "nkeji gasị", "timeUnitHour": "awa", "timeUnitHours": "awa gasị", "espNFTListWithPrice": "Ị gosiputara {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}} na{{dAppName}}", "espNFTListWithPriceWithoutDApp": "Ị gosipụtara {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Ị gosipụtara {{NFTDisplayName}} maka orere na {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Ị gosip<PERSON>tara {{NFTDisplayName}} maka orere", "espNFTChangeListPriceWithPrice": "Ị tinyere ozi <PERSON> maka {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}} na{{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Ị tinyere ozi <PERSON> na ngosipụta maka {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Ị tinyere ozi <PERSON> na ngosiputa maka {{NFTDisplayName}} na {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Ị tinyere ozi <PERSON> na ngosip<PERSON>ta maka {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Ị kwere {{priceAmount}} {{priceTokenSymbol}} maka {{NFTDisplayName}} na {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Ị kwere {{priceAmount}} {{priceTokenSymbol}} maka {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Ị kwere ọnụ maka {{NFTDisplayName}} na {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Ị kwere ọnụ maka {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Ọnụ <PERSON>h<PERSON><PERSON>ụ nke {{priceAmount}} {{priceTokenSymbol}} maka {{NFTDisplayName}} na {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Ọnụ <PERSON>h<PERSON><PERSON>ụ nke {{priceAmount}} {{priceTokenSymbol}} maka {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Ọnụ <PERSON><PERSON><PERSON><PERSON><PERSON> maka {{NFTDisplayName}} na {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Ọnụ <PERSON><PERSON><PERSON><PERSON><PERSON> maka {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Ị kagburu ikwe ọnụ nke {{priceAmount}} {{priceTokenSymbol}} maka {{NFTDisplayName}} na {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Ị kagburu ikwe ọnụ nke {{priceAmount}} {{priceTokenSymbol}} maka {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Ị kagburu ikwe ọnụ gị maka {{NFTDisplayName}} na {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Ị kagburu ikwe ọnụ gị maka {{NFTDisplayName}}", "espNFTUnlist": "Ị gosipụtaghị {{NFTDisplayName}} na {{dAppName}} maka orere", "espNFTUnlistWithoutDApp": "Ị gosipụ<PERSON>hị {{NFTDisplayName}} maka orere", "espNFTBuyBuyerWithPrice": "Ị zụtara {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}} na {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Ị zụtara {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Ị zụtar {{NFTDisplayName}} na {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Ị zụtara {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Ị rere {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}} na {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Ị rere {{NFTDisplayName}} na {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Ị rere {{NFTDisplayName}} na {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Ị rere {{NFTDisplayName}}", "espDEXSwap": "Ị gbanwetere {{downTokensTextFragment}} maka {{upTokensTextFragment}} na {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Ị tinyere {{downTokensTextFragment}} n'ime {{poolDisplayName}} nchịk<PERSON>ta tokin akuchiri na ar<PERSON> onwe ya na {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Ị gbanwetara {{downTokensTextFragment}} maka {{upTokensTextFragment}} na {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Ị wepụrụ {{upTokensTextFragment}} site na {{poolDisplayName}} nchịk<PERSON>ta tokin akuchiri na arụr<PERSON> onwe ya na {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Ị gbanwetara {{downTokensTextFragment}} maka {{upTokensTextFragment}} na {{dAppName}}", "espGenericTokenSend": "Ị zigara {{downTokensTextFragment}}", "espGenericTokenReceive": "Ị nwetara {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Ị gbanwetara {{downTokensTextFragment}} maka {{upTokensTextFragment}}", "espUnknown": "NKE ANA-AMAGHỊ", "espUnknownNFT": "NFT ana-amagh<PERSON>", "espTextFragmentAnd": "na", "externalLinkWarningTitle": "Ị na-achọ ịpụ na Phantom", "externalLinkWarningDescription": "Ma mepee {{url}}. Gbaa mbo hụ na ị nwere ntụkwasị obi na ebe ọ si abịa tupu ejiri ya mee ihe.", "shortcutsWarningDescription": "Ụzọ nkenke {{url}} wetara. Gbaa mbọ hụ na ị nwere ntụkwasị obi na owara a tupu i jiri ya mee ihe.", "lowTpsBanner": "Solana na enwe nzịpụ coin karịrị ikike netwok", "lowTpsMessageTitle": "Nzịpụ coin karịrị ikike netwok Solana", "lowTpsMessage": "<PERSON>'<PERSON>hi ngafe na nọmba nzipụ ego Solana dị oke elu, nzipụ ego gị nwere ike ọ gaghị aga maọbụ gbuo oge. <PERSON><PERSON> nwaa nzipụ ego na-agaghị ọzọ.", "solanaSlow": "Netwok Solana na adịghị aga osiso dịka o kwesiri idi", "solanaNetworkTemporarilyDown": "Netwok Solana anagh<PERSON> aga ugbu a", "waitForNetworkRestart": "Biko chere ka netwok bidogharịa. Ọ gaghi emetụta ego gị.", "exploreCollectionsCarouselTitle": "<PERSON><PERSON> nke na <PERSON>", "exploreDropsCarouselTitle": "Gịnị mere Ọhụrụ", "exploreSortFloor": "<PERSON><PERSON> kacha ala", "exploreSortListed": "<PERSON><PERSON><PERSON><PERSON><PERSON> eserese maka orire", "exploreSortVolume": "Nha", "exploreFetchErrorSubtitle": "<PERSON><PERSON> nwaa ọzọ ma emecha.", "exploreFetchErrorTitle": "<PERSON> nweghị ike <PERSON> nwetanwu.", "exploreTopCollectionsTitle": "Eserese NFT gasị <PERSON>", "exploreTopListLess": "Belata", "exploreTopListMore": "Ọzọ", "exploreSeeMore": "<PERSON><PERSON><PERSON><PERSON><PERSON> nd<PERSON>", "exploreTrendingTokens": "<PERSON><PERSON><PERSON><PERSON> nd<PERSON>", "exploreVolumeTokens": "Ọnụọgụgụ az<PERSON><PERSON><PERSON>a <PERSON> elu", "explorePriceChangeTokens": "<PERSON><PERSON><PERSON> kacha <PERSON>", "explorePriceTokens": "Tokịn site N'ọnụego", "exploreMarketCapTokens": "Tok<PERSON>n ndị <PERSON> elu", "exploreTrendingSites": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>", "exploreTopSites": "<PERSON><PERSON><PERSON><PERSON> ndị dị <PERSON>", "exploreTrendingCollections": "Mkpok<PERSON><PERSON> ihe onwunwe dị<PERSON> ndị <PERSON>wu", "exploreTopCollections": "Mkpokọta eserese dijitalụ ndị dị <PERSON>", "collectiblesSearchCollectionsSection": "<PERSON>he eserese nile dị na dịg<PERSON>", "collectiblesSearchItemsSection": "Eserese gasị", "collectiblesSearchNrOfItems": "Eserese {{ nrOfItems }}", "collectiblesSearchPlaceholderText": "Chọ<PERSON> onwunwe dịg<PERSON> gị", "collectionPinSuccess": "Ejigidela eserese nile dị na dịgịtalu n'otu ebe", "collectionPinFail": "Eserese nile dị na dịgịtalu ekweghi njigide n'otu ebe", "collectionUnpinSuccess": "<PERSON><PERSON><PERSON><PERSON><PERSON> njigide eserese nile dị na dịg<PERSON>talu n'otu ebe", "collectionUnpinFail": "Eserese nile dị na dịgịtalu ekweghi ewepu njigide n'otu ebe", "collectionHideSuccess": "Ezoro Eserese nile dị na dịgịtalu ezo", "collectionHideFail": "Eserese nile dị na dịgịtalu ekweghi ezo", "collectionUnhideSuccess": "Ewep<PERSON><PERSON><PERSON> n<PERSON>zo <PERSON>ese nile dị na dịgịtalu", "collectionUnhideFail": "Eserese nile dị na dịgịtalu ekweghi ezo", "collectiblesSpamSuccess": "<PERSON>k<PERSON><PERSON><PERSON> dịka nke ezitere ana-a<PERSON><PERSON><PERSON>", "collectiblesSpamFail": "<PERSON><PERSON><PERSON><PERSON> dịka nke ezitere ana-achọghị na agaghị", "collectiblesSpamAndHiddenSuccess": "<PERSON><PERSON>ọ dịka nke ezitere ana-achọghị zoro ezo", "collectiblesNotSpamSuccess": "Ak<PERSON><PERSON><PERSON> dịka nke na-abụghị ihe ezitere ana-ach<PERSON>gh<PERSON>", "collectiblesNotSpamFail": "Ịkọ na ọbụghị ihe ezitere ana-achọghị agaghị", "collectiblesNotSpamAndUnhiddenSuccess": "Ak<PERSON>r<PERSON> dịka nke ezitere ana-achọghị ma o zughị ezo", "tokenPageSpamWarning": "<PERSON><PERSON><PERSON><PERSON><PERSON> tokịn a<PERSON> naanị tokịn ndị ị nwere ntụkwasịobi na ha.", "tokenSpamWarning": "E zoro tokin a ezo n'ihi na Phantom chere na ọ bụ nke e zitere ana-achọghị.", "collectibleSpamWarning": "E zoro ihe onwunwe dịg<PERSON>talụ a ezo n'ihi na Phantom chere na ọ bụ nke e zitere ana-achọgh<PERSON>.", "collectionSpamWarning": "E zoro ihe onwunwe dịg<PERSON>talụ ndị a ezo n'ihi na Phantom chere na ọ bụ nke e zitere ana-achọgh<PERSON>.", "emojiNoResults": "<PERSON> nwegh<PERSON> emoji ah<PERSON>", "emojiSearchResults": "Chọọ Rịzọọtụ", "emojiSuggested": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> a<PERSON> ya", "emojiSmileys": "Ịhụ ọchị na Ndị mmadụ", "emojiAnimals": "Ụmụ anụ<PERSON><PERSON> na <PERSON>", "emojiFood": "Nri na Ihe ọnụ<PERSON>ụ", "emojiTravel": "Njem na Ebe gasị", "emojiActivities": "Ihe omume gasị", "emojiObjects": "<PERSON>he gas<PERSON>", "emojiSymbols": "<PERSON><PERSON><PERSON><PERSON>", "emojiFlags": "Flaagị", "whichExtensionToConnectWith": "Ke<PERSON><PERSON> ihe nchọta ihe ịntanetị nke ị chọrọ iji jikọọ?", "configureInSettings": "A ga-ahazili ya n'ime <PERSON> → <PERSON><PERSON><PERSON>", "continueWith": "<PERSON><PERSON> gaa n'ihu", "useMetaMask": "<PERSON><PERSON>", "usePhantom": "<PERSON><PERSON>", "alwaysAsk": "Jụọ Mg<PERSON> bụla", "dontAskMeAgain": "<PERSON><PERSON><PERSON><PERSON><PERSON> m <PERSON>", "selectWalletSettingDescriptionLine1": "Ụfọdụ aapụ nwere ike ha agaghị enye ohere i ji Phantom jikọọ.", "selectWalletSettingDescriptionLinePhantom": "<PERSON><PERSON> ka ihe nn<PERSON><PERSON>a, isi na MetaMask jikọọ ga-emepe Phantom oge ọ bụla.", "selectWalletSettingDescriptionLineAlwaysAsk": "<PERSON><PERSON><PERSON> ihe nn<PERSON><PERSON> anya, mgbe i ji MetaMask mee njik<PERSON>, anyị ga ajụ gị ma <PERSON>r<PERSON> i ji Phantom mee ihe.", "selectWalletSettingDescriptionLineMetaMask": "Ime <PERSON> ka ọ nọrọ na ọnọdụ mburubịa ga-akwụsị daapụ ndị ahụ i jikọ na Phantom.", "metaMaskOverride": "<PERSON><PERSON><PERSON>", "metaMaskOverrideSettingDescriptionLine1": "I ji jikọọ na webụsaịtị na-anaghị enye ohere i ji Phantom mee ihe.", "refreshAndReconnectToast": "<PERSON><PERSON><PERSON><PERSON><PERSON> ma j<PERSON>k<PERSON> i ji tinye mgbanwe gị", "autoConfirmUnavailable": "Ọ dịghị", "autoConfirmReasonDappNotWhitelisted": "Ọ dịghị maka na nkwekọrịta osi na ya pụta adịghị na ndepụta nnabata anyị maka aapụ a.", "autoConfirmReasonSessionNotActive": "Ọ dịghị maka na-enweghị ihe omume nkwado a na-akpaghị aka nọ n'ọrụ. <PERSON><PERSON> gbanye ya n'okpuru.", "autoConfirmReasonRateLimited": "Ọ dịghị maka na dapp ahụ ị ji eme ihe na-ezipu arịrịọ dị ọtụtụ.", "autoConfirmReasonUnsupportedNetwork": "Ọ dịghị maka na nkwado a na-ak<PERSON>ghị aka a naghị akwado netwọk a ugbua.", "autoConfirmReasonSimulationFailed": "Ọ dịghị maka na anyị enweghị ike inye nkwa nchekwa.", "autoConfirmReasonTabNotFocused": "Ọ dịghị maka na taabụ domain ị na-achọ ime nkwado a na-akpaghị aka na ya anaghị arụ ọrụ.", "autoConfirmReasonNotUnlocked": "Ọ dịghị maka na-emepeghị akpaego ahụ emepe.", "rpcErrorUnauthorizedWrongAccount": "<PERSON><PERSON><PERSON><PERSON> ah<PERSON>a sitere na adreesị amakọghị na adreesị akaụnt<PERSON> nke ahọr<PERSON>.", "rpcErrorUnauthorizedUnknownSource": "E nweghị ike <PERSON>wụ ebe arịrịọ RPC ahụ siri bịa.", "transactionsDisabledTitle": "Agbany<PERSON><PERSON><PERSON>", "transactionsDisabledMessage": "<PERSON><PERSON><PERSON><PERSON> ejinwu adreesị gị eme nzipụ ego na Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Nọ n'ọrụ", "settingsTrustedAppDetailsCopiedToClipboardToast": "A kọpịr<PERSON><PERSON> aha webụsaịtị gaa na klipbọọdụ", "notEnoughSolScanTransactionWarning": "<PERSON>zi<PERSON><PERSON> ego a nwere ike <PERSON>da mba maka na SOL dị na-akaụntụ gị e zughị ezu. <PERSON><PERSON>o SOL n'ime akaụntụ gị ma nwaa ọzọ.", "transactionRevertedWarning": "Nzipụ ego a laghachiri azụ n'oge ṅṅomi. Ego nwere ike efu ma ọbụrụ na-ezipụ ya.", "slippageToleranceExceeded": "Nzipụ ego a laghachiri mgbe a na-eme ṅomi. A gafela ofe nnagide ndịiche n'ego ahịa na n'ego orire ya.", "simulationWarningKnownMalicious": "<PERSON><PERSON> kwen<PERSON>e na-aka<PERSON><PERSON> a ga-emebi ihe. Inye ikike nwere ike ịbute ofufu ego.", "simulationWarningPoisonedAddress": "Adreesị a bụ nke a na-enyo enyo na o yiri adreesị ịzigaburu ego n'oge mbụ. <PERSON><PERSON> nyocha ma kwenye na adreesị a bụ nke ziri ezi, i ji gbochie ego ịfụ na wayo.", "simulationWarningInteractingWithAccountWithoutActivity": "Nke a bụ akaụnt<PERSON> na-enweghị ihe omume ọbụla na mbụ. I ziga ego gaa na akaụntụ na-adịghị nwere ike ibute ofufu ego", "quests": "Ọrụ omume", "questsClaimInProgress": "Nzọta na-aga n'ihu", "questsVerifyingCompletion": "Mnyocha mmecha njem ihe omume", "questsClaimError": "<PERSON><PERSON><PERSON> na n<PERSON>ọta ihe nrite", "questsClaimErrorDescription": "E nwere ngahie dap<PERSON> na n<PERSON>ọta ihe nrite gị. <PERSON><PERSON> nwaa ọzọ ma emecha.", "questsBadgeMobileOnly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "questsBadgeExtensionOnly": "<PERSON>he-nchọta-ihe-<PERSON><PERSON><PERSON><PERSON>", "questsExplainerSheetButtonLabel": "<PERSON><PERSON><PERSON><PERSON> ya", "questsNoQuestsAvailable": "<PERSON>we<PERSON><PERSON> njem ihe omume dị", "questsNoQuestsAvailableDescription": "<PERSON> nweghị njem ihe omume dị. <PERSON><PERSON> ga-eme ka ị mara ma ọ bụrụ na-etinye ndị ọhụrụ.", "exploreLearn": "<PERSON><PERSON><PERSON>", "exploreSites": "Saịtị gasị", "exploreTokens": "Tokin gasị", "exploreQuests": "<PERSON><PERSON><PERSON> ihe o<PERSON>e", "exploreCollections": "<PERSON>he eserese nile dị na dịg<PERSON>", "exploreFilterByall_networks": "Netwọkụ Nile", "exploreSortByrank": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBytrending": "<PERSON><PERSON><PERSON><PERSON> ewu", "exploreSortByprice": "Ọnụahịa", "exploreSortByprice-change": "Mgbanwe Ọnụahịa", "exploreSortBytop": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortByvolume": "Nha", "exploreSortBygainers": "Ndị <PERSON><PERSON><PERSON>", "exploreSortBylosers": "Ndị <PERSON>ah<PERSON> g<PERSON>ara", "exploreSortBymarket-cap": "Ọnụahịa <PERSON><PERSON><PERSON><PERSON>", "exploreSortBymarket_cap": "Ọnụego Coin Nile", "exploreTimeFrame1h": "Awa 1", "exploreTimeFrame24h": "Awa 24", "exploreTimeFrame7d": "Ụbọchị 7", "exploreTimeFrame30d": "Ụbọchị 30", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "<PERSON><PERSON> onwu<PERSON>", "exploreCategoryMarketplace": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryGaming": "<PERSON><PERSON>", "exploreCategoryBridges": "Njikọ", "exploreCategoryOther": "Nd<PERSON>", "exploreCategorySocial": "<PERSON><PERSON><PERSON>", "exploreCategoryCommunity": "Ndị o<PERSON>", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Nkuchi coin ruo nwa oge", "exploreCategoryArt": "Nkà <PERSON>", "exploreCategoryTools": "<PERSON><PERSON>", "exploreCategoryDeveloperTools": "Ngwa Onye nrụpụta", "exploreCategoryHackathon": "Asọmpi Nrụkọ Ọrụ", "exploreCategoryNFTStaking": "Nkuchi NFT ruo nwa oge", "exploreCategoryExplorer": "Ebenchọta data", "exploreCategoryInscriptions": "Data etinyere", "exploreCategoryBridge": "Njikọ", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Ngwa Ome Nkwadoeziokwu Airdrop anatara", "exploreCategoryPoints": "Ọnụego nrite", "exploreCategoryQuests": "Ọrụ omume", "exploreCategoryShop": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryProtocol": "Ukpuru", "exploreCategoryNamingService": "Inye Ọrụ enye<PERSON>ka <PERSON>a", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Ngwa Nsochi Nzipụ Ego", "exploreCategoryFitness": "<PERSON><PERSON><PERSON>", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Nha", "exploreFloor": "<PERSON><PERSON> kacha ala", "exploreCap": "Ọnụego Coin Nile", "exploreToken": "<PERSON><PERSON>", "explorePrice": "Ọnụahịa", "explore24hVolume": "Nha 24h", "exploreErrorButtonText": "Nwaa Ọzọ", "exploreErrorDescription": "E nwere ihe na-agaghị nke ọma mgbe achọrọ ibubata ihe dị n'ime ebe nchọgharị ozi. <PERSON><PERSON> buba<PERSON>chị ma nwaa <PERSON>z<PERSON>", "exploreErrorTitle": "O kweghi ebubata ihe dị n'ime ebe nchọgharị ozi", "exploreNetworkError": "E nwere nsogbu dab<PERSON> na netwọkụ. <PERSON><PERSON> nwaa ọzọ ma emechaa.", "exploreTokensLegalDisclaimer": "A na-ekepụta ngosipụta tokịn site n'iji ozi ahịa nke ndị a<PERSON> onye nke atọ gụnyere CoinGecko, Birdeye nakwa Jupiter. Ihe mmepụta na-agbado ụkwụ na awa 24 gara aga. Ihe mmepụta mgbe gara aga a naghị egosipụta ihe mmepụta a ga-enwe n'ọdịnihu.", "swapperTokensLegalDisclaimer": "Tokịn ndị egosipụ<PERSON>la n'ewu ewu bụ ndị a na-ekepụta site n'iji ozi ahịa sitere n'aka ndị az<PERSON>mah<PERSON> onye nke atọ, nke gụ<PERSON>ere CoinGecko, <PERSON><PERSON> nakwa Jupiter, ma na-a<PERSON><PERSON><PERSON>ụ na mgbanweta tokịn ndị ama ama nke ndị n'eji Phantom eme ihe gbanwetara site n'iji Swapper ruo oge ekwuputara. Ihe mmepụta ha na mgbe gara aga a naghị egosipụta ihe mmepụta ha ga-enwe n'ọdịnihu.", "exploreLearnErrorTitle": "O kweghi ebubata ihe dị n'ime ihe ọmụmụ", "exploreLearnErrorDescription": "E nwere ihe na-agaghị nke ọma mgbe achọrọ ibubata ihe dị n'ime ihe ọmụmụ. <PERSON><PERSON> buba<PERSON>chị ma nwaa <PERSON>z<PERSON>", "exploreShowMore": "<PERSON><PERSON><PERSON><PERSON>", "exploreShowLess": "<PERSON><PERSON> ngosi", "exploreVisitSite": "Gaa na saịtị", "dappBrowserSearchScreenVisitSite": "Gaa na saịtị", "dappBrowserSearchScreenSearchWithGoogle": "<PERSON><PERSON> mee nchọgharị", "dappBrowserSearchScreenSearchLinkYouCopied": "Njikọ Ị K<PERSON>pịrị", "dappBrowserExtSearchPlaceholder": "Chọọ <PERSON>ịtị, tokịn", "dappBrowserSearchNoAppsTokens": "A hụghị aapụ ma ọ bụ tokịn ọ bụla", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON><PERSON><PERSON>?", "dappBrowserTabsLimitExceededScreenDescription": "Ị nwere {{tabsCount}} taabụ mepere emepe. Ịji mepee ndị ọzọ, ị kwesiri imeche taabụ ụfọdụ.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON><PERSON>", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: <PERSON><PERSON><PERSON><PERSON> dị otu a adịghị", "dappBrowserTabErrorHttp": "<PERSON><PERSON><PERSON>, biko jiri HTTPS", "dappBrowserTabError401Unauthorized": "401 A kwadoghị", "dappBrowserTabError501UnhandledRequest": "501 Arịrịọ a na-emezughị", "dappBrowserTabErrorTimeout": "OGE AGAFELA: O were kọ<PERSON><PERSON> nnu<PERSON>wu oge ị zaghachi", "dappBrowserTabErrorInvalidResponse": "Nzaghachi adabaghị", "dappBrowserTabErrorEmptyResponse": "Nzaghachi efu", "dappBrowserTabErrorGeneric": "<PERSON><PERSON> ngahi<PERSON>", "localizedErrorUnknownError": "<PERSON> nwere ike na-agh<PERSON> nke <PERSON>, biko nwaa <PERSON> ma<PERSON>em<PERSON>aa.", "localizedErrorUnsupportedCountry": "Ọ dị any<PERSON> n<PERSON>, anagh<PERSON> akwado obodo gị ugbu a.", "localizedErrorTokensNotLoading": "E nwere nsogbu na ibubata tokin gị. <PERSON><PERSON>.", "localizedErrorSwapperNoQuotes": "<PERSON> nweghị mgbanwota dị, n<PERSON><PERSON><PERSON> njik<PERSON> a na-akwadog<PERSON>, ọdịd<PERSON> ala mgbanweta tokịn banye n'ihe onwunwe <PERSON>z<PERSON>, ma <PERSON> bụ ego dị ala. <PERSON><PERSON><PERSON><PERSON>a tokịn ahụ ma ọ bụ ego orire.", "localizedErrorSwapperRefuelNoQuotes": "<PERSON> nweghị ọn<PERSON><PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> g<PERSON> n<PERSON>.", "localizedErrorInsufficientSellAmount": "Ọnụego tokin dị oke ala. Welite ọnụego ahụ iji gbanweta Cross-Chain.", "localizedErrorCrossChainUnavailable": "Ịgbanweta Cross-chain adịghị ugbu a, biko nwaa ọzọ ma emechaa.", "localizedErrorTokenNotTradable": "A gaghị enwe ike irepụ otu n'ime tokịn ndị a họrọ. <PERSON><PERSON> họrọ tokịn dị iche.", "localizedErrorCollectibleLocked": "<PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON>.", "localizedErrorCollectibleListed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> aka<PERSON><PERSON><PERSON> tokin maka orire.", "spamActivityAction": "<PERSON><PERSON><PERSON><PERSON> ihe ndị zoro ezo", "spamActivityTitle": "<PERSON>he o<PERSON>e <PERSON>", "spamActivityWarning": "E zoro nzipu ego a maka na Phantom nwere nkwenye na ọ bụ ihe omume a na-achọghị.", "appAuthenticationFailed": "Ọ kweghi ime nkwa<PERSON>ez<PERSON>wu", "appAuthenticationFailedDescription": "E nwere nsogbu dap<PERSON> na ịnwa ime nkwadoeziokwu gị. <PERSON><PERSON> ọz<PERSON>.", "partialErrorBalanceChainName": "Anyị na-enwe nsogbu imelite ego fọd<PERSON><PERSON><PERSON> na {{chainName}} gị. Ego gị nọ na nchekwa.", "partialErrorGeneric": "<PERSON><PERSON> na-enwe nsogbu imelite <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> ego tokin fọdụrụnụ na ọnụego gị nwere ike o meela ochie. Ego gị nọ na nchekwa.", "partialErrorTokenDetail": "Anyị na-enwe nsogbu imelite ego tokin gị fọdụr<PERSON><PERSON>. Ego gị nọ na nchekwa.", "partialErrorTokenPrices": "Anyị na-enwe nsogbu imelite ọn<PERSON>ego tokin gị fọdụrụnụ. Ego gị nọ na nchekwa.", "partialErrorTokensTrimmed": "<PERSON><PERSON> n'enwe nsogbu n'ịgosip<PERSON>ta tokịn niile dị na nchịkọ ihe onwunwe gị. <PERSON>go gị nwere nchekwa.", "publicFungibleDetailAbout": "Banyere", "publicFungibleDetailYourBalance": "<PERSON><PERSON>", "publicFungibleDetailInfo": "<PERSON><PERSON>", "publicFungibleDetailShowMore": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailShowLess": "<PERSON><PERSON>", "publicFungibleDetailPerformance": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> 24h", "publicFungibleDetailSecurity": "Nchekwa", "publicFungibleDetailMarketCap": "Ọnụego Coin Nile", "publicFungibleDetailTotalSupply": "Ngụk<PERSON><PERSON>", "publicFungibleDetailCirculatingSupply": "Ngụk<PERSON><PERSON> tokin dị adị maka <PERSON>", "publicFungibleDetailMaxSupply": "Ngụkọta Ọnụọgụ Tokin aga <PERSON>", "publicFungibleDetailHolders": "Nd<PERSON> nwe tokin", "publicFungibleDetailVolume": "Nha", "publicFungibleDetailTrades": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailTraders": "<PERSON>dị na<PERSON><PERSON><PERSON><PERSON> ah<PERSON>", "publicFungibleDetailUniqueWallets": "<PERSON>k<PERSON><PERSON>", "publicFungibleDetailTop10Holders": "Ndepụta ndị 10 <PERSON><PERSON> nwe <PERSON>", "publicFungibleDetailTop10HoldersTooltip": "N'egosi<PERSON><PERSON>ta pacent ego tok<PERSON><PERSON> o<PERSON> dị u<PERSON>, nke dị n'aka mmadụ 10 kacha gote ma jigide tokịn ahụ. Ọ bụ ụzọ e si a mata ụdị mfe ọ dị i ji aghụghọ gbanwe ọnụ ahịa.", "publicFungibleDetailMintable": "Mintable", "publicFungibleDetailMintableTooltip": "Onye nwe nkwek<PERSON>r<PERSON>ta nwere ike ị welite nyefe tok<PERSON>n, ma <PERSON> b<PERSON> na e nwere ike ibipụta tokịn ah<PERSON>.", "publicFungibleDetailMutableInfo": "Ozi e nwere ike I<PERSON>banwe", "publicFungibleDetailMutableInfoTooltip": "Ọ bụrụ na e nwere ike ịgbanwe n'ozi ban<PERSON>e to<PERSON>, d<PERSON><PERSON> ah<PERSON>, es<PERSON><PERSON> n<PERSON><PERSON>ra nakwa webụsaịtị. Onye nwe nkwekọrịta ahụ nwere ike ị gbanwe ya.", "publicFungibleDetailOwnershipRenounced": "<PERSON><PERSON><PERSON><PERSON> aka na <PERSON>chikwa", "publicFungibleDetailOwnershipRenouncedTooltip": "Ọ bụrụ na-aj<PERSON> onwunwe tokịn, e nwegh<PERSON> onye ga-enwe ike i mepụta ihe omume, dịka ị bipụta tokịn ọzọ.", "publicFungibleDetailUpdateAuthority": "I<PERSON>ke Itinye <PERSON> Ọhụrụ", "publicFungibleDetailUpdateAuthorityTooltip": "Ikike <PERSON>we bụ adressị akpaego nwere ike <PERSON> oz<PERSON>, ma <PERSON> bụrụ na tokịn bụrụ ihe e nwere ike <PERSON>.", "publicFungibleDetailFreezeAuthority": "<PERSON><PERSON><PERSON>", "publicFungibleDetailFreezeAuthorityTooltip": "Onye ji ikike mkp<PERSON>chị aka<PERSON><PERSON><PERSON> bụ adreesị akpaego nwere ike <PERSON> nzipụ ego.", "publicFungibleUnverifiedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON> tokịn a<PERSON> naanị tokịn ndị ị nwere ntụkwasịobi na ha.", "publicFungibleDetailSwap": "Gbanweta {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "<PERSON><PERSON> Gbanweta {{tokenSymbol}}", "publicFungibleDetailLinkCopied": "A kọpịr<PERSON>la gaa na klipbọ<PERSON>d<PERSON>", "publicFungibleDetailContract": "Nkwek<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailMint": "<PERSON><PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "<PERSON>he o<PERSON>e", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivityError": "Mbudata ihe omume emere nso nso a agaghị", "additionalNetworksTitle": "Netwọk Ndị a<PERSON>", "copyAddressRowAdditionalNetworks": "Netwọk Ndị a<PERSON>", "copyAddressRowAdditionalNetworksHeader": "Netwọk ndị a na-ekek<PERSON>r<PERSON>ta otu adreesị na Etherreum:", "copyAddressRowAdditionalNetworksDescription": "Ị nwere ike iji adreesị Ethereum gị ziga ma nweta ihe onwunwe na netwọk ọbụla n'ime ndị a.", "cpeUnknownError": "<PERSON><PERSON><PERSON><PERSON> a na-amagh<PERSON>", "cpeUnknownInstructionError": "<PERSON><PERSON><PERSON><PERSON> ntụ<PERSON> a na-amagh<PERSON>", "cpeAccountFrozen": "A kpọchịrị aka<PERSON>", "cpeAssetFrozen": "A kpọ<PERSON>ri ihe onwunwe", "cpeInsufficientFunds": "<PERSON><PERSON> e<PERSON> ezu", "cpeInvalidAuthority": "Ikike a dabaghị", "cpeBalanceBelowRent": "<PERSON><PERSON> fọd<PERSON><PERSON><PERSON> erughị ogogo ehiwere maka nwepụ mg<PERSON>ye", "cpeNotApprovedForConfidentialTransfers": "A kwadoghị akaụntụ a maka nzipụ ego nzuzo", "cpeNotAcceptingDepositsOrTransfers": "<PERSON><PERSON><PERSON><PERSON><PERSON> a naghị anabata nkwụnye ma ọ bụ nzipụ ego", "cpeNoMemoButRequired": "<PERSON>i nd<PERSON>iche anọghị na ntụziaka gara aga ekwesịrị iji mee ka onye nweta were nweta nzipu ego", "cpeTransferDisabledForMint": "A gbanyụrụ nzipụ maka mbipụta a", "cpeDepositAmountExceedsLimit": "Ego nkwụnye gafere oke kacha elu", "cpeInsufficientFundsForRent": "<PERSON><PERSON> e<PERSON> ezu maka mg<PERSON>ye", "reportIssueScreenTitle": "<PERSON><PERSON> mk<PERSON>a nsogbu", "publicFungibleReportIssuePrompt": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> maka {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "<PERSON><PERSON> N<PERSON>e<PERSON>", "publicFungibleReportIssuePriceStale": "Ọnụahịa a naghị emelite", "publicFungibleReportIssuePriceMissing": "Ọnụahịa a naghị egosi", "publicFungibleReportIssuePerformanceIncorrect": "<PERSON><PERSON><PERSON><PERSON>ọ<PERSON>ụ 24h ezighi ezi", "publicFungibleReportIssueLinkBroken": "Webụsaịtị i ji sonyere anyị na sosha midia a naghị aga", "publicFungibleDetailErrorLoading": "<PERSON><PERSON>", "reportUserPrompt": "<PERSON><PERSON><PERSON> n<PERSON> chọ<PERSON> maka ya @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "Mmegbu na Mmekpaahụ", "reportUserOptionAbuseAndHarrassmentDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ak<PERSON><PERSON> anya eme, mkpal<PERSON> mm<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON> mer<PERSON> ah<PERSON>, edem<PERSON>e na nkọwa ndị bu akp<PERSON>rọm asị", "reportUserOptionPrivacyAndImpersonationTitle": "Nzowe onwe onye na <PERSON>uzu n<PERSON> onye", "reportUserOptionPrivacyAndImpersonationDescription": "Ị kek<PERSON>r<PERSON><PERSON> ma ọ bụ iyi egwu <PERSON>kpughe ozi onwe onye, aghụghọ na ị bụ onye ọzọ", "reportUserOptionSpamTitle": "<PERSON><PERSON> a na-a<PERSON>", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON><PERSON><PERSON>, m<PERSON><PERSON>, ad<PERSON><PERSON><PERSON> webụsaịtị n'emebi ihe", "reportUserSuccess": "E zigala Mkpesa Onye njiaru.", "settingsClaimUsernameTitle": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameDescription": "Njirimara pụ<PERSON>ụ iche ka ak<PERSON>ego gị", "settingsClaimUsernameValueProp1": "<PERSON><PERSON><PERSON><PERSON> ka <PERSON> mfe", "settingsClaimUsernameValueProp1Description": "Feere ogologo adreesị n'agbanwoju anya aka, ma nabata nji<PERSON>ra dị mfe i ji mee ihe", "settingsClaimUsernameValueProp2": "Ọsịsọ & Ị dị mfe", "settingsClaimUsernameValueProp2Description": "<PERSON><PERSON><PERSON> ma nwetakwa crypto na ọdị mfe, banye na-akpaego gị ma soro ndị enyi kpakọr<PERSON>ta", "settingsClaimUsernameValueProp3": "Nọgide na Njikọrịta", "settingsClaimUsernameValueProp3Description": "Jikọọ aka<PERSON><PERSON><PERSON> n'aha mbanye gị, ọ ga-ejik<PERSON>rịta na-ak<PERSON>r<PERSON>ngwa gị niile", "settingsClaimUsernameHelperText": "<PERSON>a nd<PERSON>iche gị maka A<PERSON>ụntụ Phantom gị", "settingsClaimUsernameValidationDefault": "A gaghị enwe ike <PERSON>we aha mbanye a ma emecha", "settingsClaimUsernameValidationAvailable": "Ewerebeghị aha mbanye a", "settingsClaimUsernameValidationUnavailable": "<PERSON><PERSON><PERSON> aha mbanye a", "settingsClaimUsernameValidationServerError": "E nweghị ike <PERSON> ma e werela aha mbanye, biko nwaa mgbe ọzọ", "settingsClaimUsernameValidationErrorLine1": "<PERSON><PERSON> <PERSON><PERSON><PERSON> ad<PERSON>.", "settingsClaimUsernameValidationErrorLine2": "<PERSON>a mbanye ga-enwe okwu ọnụọgụgụ ha ga-adịrịrị n'agbata {{minChar}} na {{maxChar}} n'ogologo, ma nwe naanị mkpụrụ edemede nakwa ọnụọ<PERSON>ụ<PERSON>.", "settingsClaimUsernameValidationLoading": "Na-e<PERSON>cha ma ewerela aha mbanye a...", "settingsClaimUsernameSaveAndContinue": "Chekwaa & Gaa n'ihu", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON><PERSON><PERSON> akara ngosi", "settingsClaimUsernameAnonymousAuthTitle": "Nkwadoeziokwu E <PERSON>chi<PERSON> n<PERSON>", "settingsClaimUsernameAnonymousAuthDescription": "<PERSON><PERSON> aka ban<PERSON> n'ime Akaụntụ Phantom gị na-ek<PERSON>gh<PERSON>ị njirimara gị", "settingsClaimUsernameAnonymousAuthBadge": "Mụta ka o si arụ <PERSON>", "settingsClaimUsernameLinkWalletsTitle": "Jikọọ akpaego gị", "settingsClaimUsernameLinkWalletsDescription": "<PERSON><PERSON><PERSON><PERSON> akpaego ndị ji aha mbanye egosi na-ak<PERSON>r<PERSON>ngwa gị ndị <PERSON>", "settingsClaimUsernameLinkWalletsBadge": "Ọha A gaghị Ah<PERSON> ya", "settingsClaimUsernameConnectAccountsTitle": "Jik<PERSON><PERSON> aka<PERSON>", "settingsClaimUsernameConnectAccountsHelperText": "A ga e jikọọ adreesị chenụ ọbụla n'aha mbanye gị. Ị nwere ike ị megharị nke a ma emecha.", "settingsClaimUsernameContinue": "Gaa n'ihu", "settingsClaimUsernameCreateUsername": "<PERSON><PERSON><PERSON><PERSON> aha mbanye", "settingsClaimUsernameCreating": "Na-<PERSON><PERSON><PERSON><PERSON>ta aha mbanye...", "settingsClaimUsernameSuccess": "E keputala Aha mbanye!", "settingsClaimUsernameError": "<PERSON><PERSON> zutere ngahie na nkeputa aha mbanye gị", "settingsClaimUsernameTryAgain": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameSuccessHelperText": "Ị nweziri ike i ji aha mbanye ọhụrụ gị na-akpaego Phantom gị niile", "settingsClaimUsernameSettingsSyncedTitle": "<PERSON><PERSON><PERSON> <PERSON>ọ<PERSON>ọ<PERSON>", "settingsClaimUsernameSettingsSyncedHelperText": "Feere ogologo adreesị n'agbanwoju anya aka, ma nabata nji<PERSON>ra dị mfe iji eme ihe", "settingsClaimUsernameSendToUsernameTitle": "Ziga na Aha mbanye", "settingsClaimUsernameSendToUsernameHelperText": "<PERSON><PERSON><PERSON> ma nwetakwa crypto na ọdị mfe, banye na-akpaego gị ma soro ndị enyi kpakọr<PERSON>ta", "settingsClaimUsernameManageAddressesTitle": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameManageAddressesHelperText": "Tok<PERSON>n ma <PERSON> bụ ihe onwunwe dịgitalụ ọb<PERSON> e<PERSON>ara n'aha mbanye gị, ga-aga n'adreesị ndịa", "settingsClaimUsernameManageAddressesBadge": "Ọha <PERSON><PERSON><PERSON><PERSON> ya", "settingsClaimUsernameEditAddressesTitle": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameEditAddressesHelperText": "Tokịn ma ọ bụ ihe onwunwe dịgitalụ ọbụla ezigara n'aha mbanye gị, ga-aga na adreesị ndịa. <PERSON><PERSON><PERSON><PERSON> otu adreesị maka chenụ <PERSON>.", "settingsClaimUsernameEditAddressesError": "<PERSON><PERSON> adreesị naanị ka a na-anabata maka otu netwọk.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON><PERSON>", "settingsClaimUsernameNoAddressesSaved": "Enweghị adreesị <PERSON> e chekwara", "settingsClaimUsernameSave": "Chekwa", "settingsClaimUsernameDone": "O mechala", "settingsClaimUsernameWatching": "Na-<PERSON><PERSON>", "settingsClaimUsernameNoOfAccounts": "<PERSON><PERSON><PERSON><PERSON> {{noOfAccounts}}", "settingsClaimUsernameNoOfAccountsSingular": "Akaụntụ 1", "settingsClaimUsernameEmptyAccounts": "<PERSON> nweghị <PERSON>", "settingsClaimUsernameSettingTitle": "<PERSON><PERSON><PERSON> @ahambanye nke gị", "settingsClaimUsernameSettingDescription": "Njirimara pụ<PERSON>ụ iche maka ak<PERSON>ego gị", "settingsManageUserProfileAbout": "Banyere", "settingsManageUserProfileAboutUsername": "<PERSON><PERSON> m<PERSON>ye", "settingsManageUserProfileAboutBio": "<PERSON><PERSON> onwe onye", "settingsManageUserProfileTitle": "Jikwaa Nkọwa onwe onye", "settingsManageUserProfileManage": "Jikwaa", "settingsManageUserProfileAuthFactors": "Mmewere Nkwadoeziokwu", "settingsManageUserProfileAuthFactorsDescription": "<PERSON><PERSON><PERSON><PERSON> mkpụ<PERSON><PERSON><PERSON><PERSON> nwetaghachị ma ọ bụ akara <PERSON>ban<PERSON> Nzuzo nke ga-abanyenwu na-Akaụntụ Phantom gị.", "settingsManageUserProfileUpdateAuthFactorsToast": "E melitela mmewere nkwadoeziokwu!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Mmelite mmewere nkwadoeziokwu agaghị!", "settingsManageUserProfileBiography": "<PERSON><PERSON> ebe ozi nk<PERSON>wa onwe onye", "settingsManageUserProfileBiographyDescription": "Tinye obere ozi nkọwa onwe na nkọwa onwe onye gị", "settingsManageUserProfileUpdateBiographyToast": "E melitela Ozi ebe nkọwa onwe!", "settingsManageUserProfileUpdateBiographyToastFailure": "O nwere Ihe mere. <PERSON><PERSON>", "settingsManageUserProfileBiographyNoUrlMessage": "<PERSON><PERSON> wepụ akara web<PERSON><PERSON> na-ebe nk<PERSON>wa onwe gị", "settingsManageUserProfileLinkedWallets": "Akpaego ndị <PERSON>", "settingsManageUserProfileLinkedWalletsDescription": "<PERSON><PERSON><PERSON><PERSON> akpaego ndị n'egosi na-akụrụngwa ndị ọzọ oge ị na-abanye na Akaụntụ Phantom.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Emelitela a<PERSON>ego ndị ejikọrọla!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "<PERSON><PERSON><PERSON> a<PERSON> ndị ejikọr<PERSON> agagh<PERSON>!", "settingsManageUserProfilePrivacy": "Nzowe onwe onye", "settingsManageUserProfileUpdatePrivacyStateToast": "E melitela nzowe onwe onye!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "<PERSON><PERSON><PERSON> nzowe onwe onye agaghị!", "settingsManageUserProfilePublicAddresses": "<PERSON><PERSON><PERSON><PERSON> ndị nke Ọha", "settingsManageUserProfileUpdatePublicAddressToast": "Emelitela adreesị <PERSON>!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "<PERSON><PERSON><PERSON> adreesị <PERSON>ha agagh<PERSON>!", "settingsManageUserProfilePrivacyStatePublic": "Ọha", "settingsManageUserProfilePrivacyStatePublicDescription": "Nkọwa onwe onye gị na adreesị <PERSON>ha bụ nke onye ọbụla ga-ahụ ma chọtanwuo", "settingsManageUserProfilePrivacyStatePrivate": "<PERSON><PERSON> onwe", "settingsManageUserProfilePrivacyStatePrivateDescription": "Nkọwa onwe onye gị bụ nke onye ọbụla ga achọ<PERSON>, mana ndị <PERSON>zọ ga-anata ikike tupu ha enwe ike ịhụ nkọwa onwe onye gị nakwa adrees<PERSON>ha", "settingsManageUserProfilePrivacyStateInvisible": "<PERSON><PERSON>", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Nkọwa onwe onye gị na-adrees<PERSON>ha bụ nke e zoro ezo, a gaghị enwekwa ike <PERSON>tanwu ya ebe niile", "settingsDownloadPhantom": "Daụnloduo Phantom", "settingsLogOut": "<PERSON><PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "<PERSON><PERSON>", "seedlessAddAWalletSecondaryText": "<PERSON>ye ma <PERSON> bụ bubata a<PERSON> d<PERSON> ", "seedlessAddSeedlessWalletPrimaryText": "<PERSON>e Akpaego N'enweghị mkpụ<PERSON><PERSON><PERSON> mbanye", "seedlessAddSeedlessWalletSecondaryText": "<PERSON><PERSON>, Google ma ọ bụ Akara ozi ikuku meelụ gị", "seedlessCreateNewWalletPrimaryText": "<PERSON><PERSON><PERSON><PERSON> Ọhụrụ?", "seedlessCreateNewWalletSecondaryText": "Akara ozi ikuku meelụ a enweghị akpaego, Ị ga-achọ ịkepụta otu?", "seedlessCreateNewWalletButtonText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessCreateNewWalletNoBundlePrimaryText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "seedlessCreateNewWalletNoBundleSecondaryText": "Akara ozi ikuku meelụ a enweghị akpaego", "seedlessCreateNewWalletNoBundleButtonText": "<PERSON><PERSON><PERSON>", "seedlessEmailOptionsPrimaryText": "Họrọ Akara ozi ikuku me<PERSON>ụ Gị", "seedlessEmailOptionsSecondaryText": "<PERSON><PERSON> bu akaụntụ Apple ma ọ bụ Google gị ", "seedlessEmailOptionsButtonText": "<PERSON><PERSON> akara ozi ikuku meelụ gaa n'ihu", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "<PERSON><PERSON> Apple gị were keputa akpaego", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "<PERSON><PERSON> akara ozi ikuku meelụ Google gị keputa akpaego", "seedlessAlreadyExistsPrimaryText": "E nwebuolarị <PERSON> a", "seedlessAlreadyExistsSecondaryText": "Akara ozi ikuku a nwere ak<PERSON>ego e keputarala, Ị ga-achọ ịme mbanye?", "seedlessSignUpWithAppleButtonText": "Jiri Apple were debanye aha", "seedlessContinueWithAppleButtonText": "<PERSON><PERSON> gaa n'ihu", "seedlessSignUpWithGoogleButtonText": "Jiri <PERSON> banye", "seedlessContinueWithGoogleButtonText": "Jiri Google gaa n'ihu", "seedlessCreateAPinPrimaryText": "Kepụta PIN", "seedlessCreateAPinSecondaryText": "E ji nke a echekwa akpaego gị na akụrụngwa gị niile. <1>A gaghị eweghachiteli nke a.</1>", "seedlessContinueText": "Gaa n'ihu", "seedlessConfirmPinPrimaryText": "Mee nkwadoeziokwu PIN gị", "seedlessConfirmPinSecondaryText": "Ị chefuo PIN a, Ị gaghị enwe ike <PERSON>we<PERSON>te akpaego gị na akụrụngwa ọhụrụ.", "seedlessConfirmPinButtonText": "Kepụta PIN", "seedlessConfirmPinError": "PIN a ezighi ezi. <PERSON><PERSON>", "seedlessAccountsImportedPrimaryText": "<PERSON> buba<PERSON>", "seedlessAccountsImportedSecondaryText": "A ga-ebubata aka<PERSON><PERSON><PERSON> ndị ahụ n'ime akpaego gị na-akpaghị aka", "seedlessPreviouslyImportedTag": "E bubataburu n'oge gara aga", "seedlessEnterPinPrimaryText": "Tibanye PIN gị", "seedlessEnterPinInvalidPinError": "Ị tinyere PIN n'adabaghị. Na<PERSON><PERSON> akara ọnụọgụgụ dị 4 ka a na-anabata", "seedlessEnterPinNumTriesLeft": "nwale {{numTries}} fọ<PERSON><PERSON><PERSON><PERSON>.", "seedlessEnterPinCooldown": "<PERSON><PERSON><PERSON> ma o ruo {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN ga adịrịrị 4 n'ọnụọgụgụ", "seedlessEnterPinMatch": "PIN ndị <PERSON>", "seedlessDoneText": "O mechala", "seedlessEnterPinToSign": "Tibanye PIN iji nye nzipu ego a nkwado", "seedlessSigning": "N'enye nkwado", "seedlessCreateSeed": "<PERSON><PERSON><PERSON><PERSON> a<PERSON> nwere mkp<PERSON><PERSON><PERSON><PERSON><PERSON> nwe<PERSON>", "seedlessImportOptions": "<PERSON>họr<PERSON> mbubata ndị <PERSON>", "seedlessImportPrimaryText": "Nhọrọ Mbubata", "seedlessImportSecondaryText": "Bubata akpaego dịbu site n'iji mkpụ<PERSON><PERSON><PERSON><PERSON>, akara mbanye nzuzo ma <PERSON> bụ ngwa nchekwa akpaego gị", "seedlessImportSeedPhrase": "<PERSON><PERSON><PERSON>", "seedlessImportPrivateKey": "Bubata Akara Mbanye Nzuzo", "seedlessConnectHardwareWallet": "Jikọọ Ngwa Nchekwa Akpaego", "seedlessTryAgain": "<PERSON><PERSON><PERSON>", "seedlessCreatingWalletPrimaryText": "A na-ekeputa akpaego", "seedlessCreatingWalletSecondaryText": "A n'etinye akpaego soshal", "seedlessLoadingWalletPrimaryText": "A na-ebudata akpaego", "seedlessLoadingWalletSecondaryText": "Mbubata na nlele akaụntụ gị ndị e jikọrọla", "seedlessLoadingWalletErrorPrimaryText": "<PERSON><PERSON><PERSON> a<PERSON> a <PERSON>h<PERSON>", "seedlessCreatingWalletErrorPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON> a<PERSON> a <PERSON>h<PERSON>", "seedlessErrorSecondaryText": "<PERSON><PERSON>", "seedlessAuthAlreadyExistsErrorText": "Akara ozi ikuku meelụ <PERSON>ere, bụ akaụntụ Phantom <PERSON>z<PERSON> nwe ya", "seedlessAuthUnknownErrorText": "<PERSON><PERSON><PERSON><PERSON> a na-am<PERSON><PERSON> ad<PERSON>, biko nwaa <PERSON>z<PERSON> ma emecha", "seedlessAuthUnknownErrorTextRefresh": "<PERSON><PERSON><PERSON><PERSON> a na-am<PERSON><PERSON> da<PERSON>, biko nwaa ọzọ ma emecha. Bugharịa ibe web<PERSON><PERSON><PERSON>t<PERSON> ahụ iji nwaa ọzọ.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON>", "seedlessWalletExistsErrorPrimaryText": "E nwela ak<PERSON>ego soshal dịbu na-akụr<PERSON>ng<PERSON> gị", "seedlessWalletExistsErrorSecondaryText": "<PERSON><PERSON> gaghachi azụ ma ọ bụ mechie ihu ebe a", "seedlessValueProp1PrimaryText": "<PERSON><PERSON><PERSON> n'enwegh<PERSON> nsogbu", "seedlessValueProp1SecondaryText": "Kepụta akpaego site n'iji akaụntụ Google ma <PERSON> bụ <PERSON>, ma bido nchọgharị ihe na web3 n'enweghị nsogbu", "seedlessValueProp2PrimaryText": "Nchekwa e meliterela", "seedlessValueProp2SecondaryText": "E chekwara akpaego gị nke ọma ma kesakwa ikike azụmahịa gazuo ọtụtụ ebe", "seedlessValueProp3PrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON> dị mfe", "seedlessValueProp3SecondaryText": "Nwetaghachi mbanye na-akpaego gị site n'iji akaụntụ Google ma ọ bụ Apple, ya na PIN ọnụọgụgụ ya dị 4", "seedlessLoggingIn": "Na abanye n'ime...", "seedlessSignUpOrLogin": "Debanye Aha ma <PERSON> b<PERSON>", "seedlessContinueByEnteringYourEmail": "Gaa n'ihu site n'itinye akara ozi ikuku meelụ gị", "seedless": "Enweghị mkp<PERSON><PERSON><PERSON><PERSON> mbanye", "seed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "seedlessVerifyPinPrimaryText": "Nye PIN nkwado", "seedlessVerifyPinSecondaryText": "<PERSON><PERSON> tibanye PIN gị iji gaa n'ihu", "seedlessVerifyPinVerifyButtonText": "Nye nkwado", "seedlessVerifyPinForgotButtonText": "Ị chefuru?", "seedlessPinConfirmButtonText": "<PERSON><PERSON> n<PERSON><PERSON><PERSON><PERSON>", "seedlessVerifyToastPrimaryText": "Nyocha ma kwado PIN gị", "seedlessVerifyToastSecondaryText": "Anyị ga-agwa gị ugboro ugboro ka ịnyocha ma kwado PIN gị ka ị wee n'echete ya. Ọ b<PERSON><PERSON><PERSON>, Ị gaghị enwe ike iweghachite akpaego gị.", "seedlessVerifyToastSuccessText": "<PERSON><PERSON>la nkwado maka akara PIN gị!", "seedlessForgotPinPrimaryText": "<PERSON><PERSON><PERSON><PERSON> hazigharịa PIN", "seedlessForgotPinSecondaryText": "<PERSON><PERSON>, Ị ga-enwe i<PERSON>ị PIN gị naanị na-akụrụngwa gị ndị ọzọ mgbe ịbanyere n'ime akpaego gị", "seedlessForgotPinInstruction1PrimaryText": "<PERSON><PERSON><PERSON> nke Ọzọ", "seedlessForgotPinInstruction1SecondaryText": "Gaa na-akụrụngwa ọzọ ebe i ji akara ozi ikuku mail gị banye n'ime akaụntụ Phantom gị", "seedlessForgotPinInstruction2PrimaryText": "Gaa n'ebe Nhazi", "seedlessForgotPinInstruction2SecondaryText": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> \"Nchekwa & Nzowe ozi onwe onye\" ya na \"Hazigharịa PIN\"", "seedlessForgotPinInstruction3PrimaryText": "Tinye PIN Ọhụrụ gị", "seedlessForgotPinInstruction3SecondaryText": "Ọ bụrụ na itinyecha PIN ọhụrụ gị, Ị nweziri ike ugbua <PERSON>banye n'ime akpaego gị na akụrụngwa a", "seedlessForgotPinButtonText": "E meela m ihe njem nd<PERSON>a", "seedlessResetPinPrimaryText": "Dozigharịa PIN", "seedlessResetPinSecondaryText": "Tibanye PIN ọhụrụ ị ga-enwe ike <PERSON>. A ga-eji nke a echekwa akpaego gị na-akụrụngwa gị niile", "seedlessResetPinSuccessText": "Emelitela akara PIN gị!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Site n'ikep<PERSON><PERSON>, ị kwenyere na <1><PERSON><PERSON> Ọrụ</1> na <5><PERSON><PERSON> oz<PERSON> onwe onye</5> anyị", "pageNotFound": "<PERSON><PERSON><PERSON><PERSON> ibe web<PERSON><PERSON> a", "pageNotFoundDescription": "<PERSON><PERSON> ahapụbeghị gị! Ibe webụsaịtị a adịgh<PERSON>, ma <PERSON> bụ n'ewep<PERSON> ya.", "webTokenPagesLegalDisclaimer": "Ozi gbasara ọnụego bụ nke e wepụtara naanị maka mgbasa ozi, ọ bụghị ndụmọdụ gbasara ego. Ozi azụmahịa nke ndị ọzọ na Phantom wepụtara a naghị egosipụta izi ezi nke ozi ahụ.", "signUpOrLogin": "<PERSON><PERSON><PERSON> aha ma <PERSON> bụ banye", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Site n'ịkep<PERSON>ta akaụnt<PERSON>, Ị na-ekwenye na <1>Iwu nke usoro ọrụ\n</1> nakwa <5><PERSON>wu nzowe ozi onwe onye</5> anyị", "feedNoActivity": "<PERSON> nweghebeghị ihe omume <PERSON>", "followRequests": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "following": "N'eso", "followers": "<PERSON><PERSON><PERSON> nso", "follower": "<PERSON><PERSON> nso", "joined": "O sonyela", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "E nweghị ndị n'eso", "noFollowing": "E nweghị nso", "noUsersFound": "<PERSON> nweghị onye njiarụ ahụrụ", "viewProfile": "<PERSON><PERSON><PERSON><PERSON> onwe onye", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}