import{b as Qn}from"./chunk-ALUTR72U.js";import{j as Ox,q as Lx}from"./chunk-L3A2KHJO.js";import{a as e}from"./chunk-4P36KWOF.js";import{c as V,d as l,f as Yt,h as p,l as jx,n}from"./chunk-3KENBVE7.js";var Zn=V(mt=>{"use strict";p();n();Object.defineProperty(mt,"__esModule",{value:!0});mt.version=void 0;mt.version="logger/5.7.0"});var Ce=V(fe=>{"use strict";p();n();Object.defineProperty(fe,"__esModule",{value:!0});fe.Logger=fe.ErrorCode=fe.LogLevel=void 0;var Jn=!1,Xn=!1,lt={debug:1,default:2,info:2,warning:3,error:4,off:5},Yn=lt.default,Hx=Zn(),$t=null;function Fx(){try{var r=[];if(["NFD","NFC","NFKD","NFKC"].forEach(function(t){try{if("test".normalize(t)!=="test")throw new Error("bad normalize")}catch{r.push(t)}}),r.length)throw new Error("missing "+r.join(", "));if("\xE9".normalize("NFD")!=="e\u0301")throw new Error("broken implementation")}catch(t){return t.message}return null}var $n=Fx(),rs;(function(r){r.DEBUG="DEBUG",r.INFO="INFO",r.WARNING="WARNING",r.ERROR="ERROR",r.OFF="OFF"})(rs=fe.LogLevel||(fe.LogLevel={}));var ie;(function(r){r.UNKNOWN_ERROR="UNKNOWN_ERROR",r.NOT_IMPLEMENTED="NOT_IMPLEMENTED",r.UNSUPPORTED_OPERATION="UNSUPPORTED_OPERATION",r.NETWORK_ERROR="NETWORK_ERROR",r.SERVER_ERROR="SERVER_ERROR",r.TIMEOUT="TIMEOUT",r.BUFFER_OVERRUN="BUFFER_OVERRUN",r.NUMERIC_FAULT="NUMERIC_FAULT",r.MISSING_NEW="MISSING_NEW",r.INVALID_ARGUMENT="INVALID_ARGUMENT",r.MISSING_ARGUMENT="MISSING_ARGUMENT",r.UNEXPECTED_ARGUMENT="UNEXPECTED_ARGUMENT",r.CALL_EXCEPTION="CALL_EXCEPTION",r.INSUFFICIENT_FUNDS="INSUFFICIENT_FUNDS",r.NONCE_EXPIRED="NONCE_EXPIRED",r.REPLACEMENT_UNDERPRICED="REPLACEMENT_UNDERPRICED",r.UNPREDICTABLE_GAS_LIMIT="UNPREDICTABLE_GAS_LIMIT",r.TRANSACTION_REPLACED="TRANSACTION_REPLACED",r.ACTION_REJECTED="ACTION_REJECTED"})(ie=fe.ErrorCode||(fe.ErrorCode={}));var es="0123456789abcdef",Kx=function(){function r(t){Object.defineProperty(this,"version",{enumerable:!0,value:t,writable:!1})}return r.prototype._log=function(t,s){var c=t.toLowerCase();lt[c]==null&&this.throwArgumentError("invalid log level name","logLevel",t),!(Yn>lt[c])&&console.log.apply(console,s)},r.prototype.debug=function(){for(var t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];this._log(r.levels.DEBUG,t)},r.prototype.info=function(){for(var t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];this._log(r.levels.INFO,t)},r.prototype.warn=function(){for(var t=[],s=0;s<arguments.length;s++)t[s]=arguments[s];this._log(r.levels.WARNING,t)},r.prototype.makeError=function(t,s,c){if(Xn)return this.makeError("censored error",s,{});s||(s=r.errors.UNKNOWN_ERROR),c||(c={});var y=[];Object.keys(c).forEach(function(T){var J=c[T];try{if(J instanceof Uint8Array){for(var M="",Pe=0;Pe<J.length;Pe++)M+=es[J[Pe]>>4],M+=es[J[Pe]&15];y.push(T+"=Uint8Array(0x"+M+")")}else y.push(T+"="+JSON.stringify(J))}catch{y.push(T+"="+JSON.stringify(c[T].toString()))}}),y.push("code="+s),y.push("version="+this.version);var z=t,_="";switch(s){case ie.NUMERIC_FAULT:{_="NUMERIC_FAULT";var k=t;switch(k){case"overflow":case"underflow":case"division-by-zero":_+="-"+k;break;case"negative-power":case"negative-width":_+="-unsupported";break;case"unbound-bitwise-result":_+="-unbound-result";break}break}case ie.CALL_EXCEPTION:case ie.INSUFFICIENT_FUNDS:case ie.MISSING_NEW:case ie.NONCE_EXPIRED:case ie.REPLACEMENT_UNDERPRICED:case ie.TRANSACTION_REPLACED:case ie.UNPREDICTABLE_GAS_LIMIT:_=s;break}_&&(t+=" [ See: https://links.ethers.org/v5-errors-"+_+" ]"),y.length&&(t+=" ("+y.join(", ")+")");var P=new Error(t);return P.reason=z,P.code=s,Object.keys(c).forEach(function(T){P[T]=c[T]}),P},r.prototype.throwError=function(t,s,c){throw this.makeError(t,s,c)},r.prototype.throwArgumentError=function(t,s,c){return this.throwError(t,r.errors.INVALID_ARGUMENT,{argument:s,value:c})},r.prototype.assert=function(t,s,c,y){t||this.throwError(s,c,y)},r.prototype.assertArgument=function(t,s,c,y){t||this.throwArgumentError(s,c,y)},r.prototype.checkNormalize=function(t){t==null&&(t="platform missing String.prototype.normalize"),$n&&this.throwError("platform missing String.prototype.normalize",r.errors.UNSUPPORTED_OPERATION,{operation:"String.prototype.normalize",form:$n})},r.prototype.checkSafeUint53=function(t,s){typeof t=="number"&&(s==null&&(s="value not safe"),(t<0||t>=9007199254740991)&&this.throwError(s,r.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"out-of-safe-range",value:t}),t%1&&this.throwError(s,r.errors.NUMERIC_FAULT,{operation:"checkSafeInteger",fault:"non-integer",value:t}))},r.prototype.checkArgumentCount=function(t,s,c){c?c=": "+c:c="",t<s&&this.throwError("missing argument"+c,r.errors.MISSING_ARGUMENT,{count:t,expectedCount:s}),t>s&&this.throwError("too many arguments"+c,r.errors.UNEXPECTED_ARGUMENT,{count:t,expectedCount:s})},r.prototype.checkNew=function(t,s){(t===Object||t==null)&&this.throwError("missing new",r.errors.MISSING_NEW,{name:s.name})},r.prototype.checkAbstract=function(t,s){t===s?this.throwError("cannot instantiate abstract class "+JSON.stringify(s.name)+" directly; use a sub-class",r.errors.UNSUPPORTED_OPERATION,{name:t.name,operation:"new"}):(t===Object||t==null)&&this.throwError("missing new",r.errors.MISSING_NEW,{name:s.name})},r.globalLogger=function(){return $t||($t=new r(Hx.version)),$t},r.setCensorship=function(t,s){if(!t&&s&&this.globalLogger().throwError("cannot permanently disable censorship",r.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"}),Jn){if(!t)return;this.globalLogger().throwError("error censorship permanent",r.errors.UNSUPPORTED_OPERATION,{operation:"setCensorship"})}Xn=!!t,Jn=!!s},r.setLogLevel=function(t){var s=lt[t.toLowerCase()];if(s==null){r.globalLogger().warn("invalid log level - "+t);return}Yn=s},r.from=function(t){return new r(t)},r.errors=ie,r.levels=rs,r}();fe.Logger=Kx});var ts=V(yt=>{"use strict";p();n();Object.defineProperty(yt,"__esModule",{value:!0});yt.version=void 0;yt.version="bytes/5.7.0"});var Be=V(C=>{"use strict";p();n();Object.defineProperty(C,"__esModule",{value:!0});C.joinSignature=C.splitSignature=C.hexZeroPad=C.hexStripZeros=C.hexValue=C.hexConcat=C.hexDataSlice=C.hexDataLength=C.hexlify=C.isHexString=C.zeroPad=C.stripZeros=C.concat=C.arrayify=C.isBytes=C.isBytesLike=void 0;var Vx=Ce(),Gx=ts(),L=new Vx.Logger(Gx.version);function ps(r){return!!r.toHexString}function ke(r){return r.slice||(r.slice=function(){var t=Array.prototype.slice.call(arguments);return ke(new Uint8Array(Array.prototype.slice.apply(r,t)))}),r}function ns(r){return se(r)&&!(r.length%2)||dt(r)}C.isBytesLike=ns;function os(r){return typeof r=="number"&&r==r&&r%1===0}function dt(r){if(r==null)return!1;if(r.constructor===Uint8Array)return!0;if(typeof r=="string"||!os(r.length)||r.length<0)return!1;for(var t=0;t<r.length;t++){var s=r[t];if(!os(s)||s<0||s>=256)return!1}return!0}C.isBytes=dt;function Ie(r,t){if(t||(t={}),typeof r=="number"){L.checkSafeUint53(r,"invalid arrayify value");for(var s=[];r;)s.unshift(r&255),r=parseInt(String(r/256));return s.length===0&&s.push(0),ke(new Uint8Array(s))}if(t.allowMissingPrefix&&typeof r=="string"&&r.substring(0,2)!=="0x"&&(r="0x"+r),ps(r)&&(r=r.toHexString()),se(r)){var c=r.substring(2);c.length%2&&(t.hexPad==="left"?c="0"+c:t.hexPad==="right"?c+="0":L.throwArgumentError("hex data is odd-length","value",r));for(var s=[],y=0;y<c.length;y+=2)s.push(parseInt(c.substring(y,y+2),16));return ke(new Uint8Array(s))}return dt(r)?ke(new Uint8Array(r)):L.throwArgumentError("invalid arrayify value","value",r)}C.arrayify=Ie;function ss(r){var t=r.map(function(y){return Ie(y)}),s=t.reduce(function(y,z){return y+z.length},0),c=new Uint8Array(s);return t.reduce(function(y,z){return c.set(z,y),y+z.length},0),ke(c)}C.concat=ss;function Wx(r){var t=Ie(r);if(t.length===0)return t;for(var s=0;s<t.length&&t[s]===0;)s++;return s&&(t=t.slice(s)),t}C.stripZeros=Wx;function as(r,t){r=Ie(r),r.length>t&&L.throwArgumentError("value out of range","value",arguments[0]);var s=new Uint8Array(t);return s.set(r,t-r.length),ke(s)}C.zeroPad=as;function se(r,t){return!(typeof r!="string"||!r.match(/^0x[0-9A-Fa-f]*$/)||t&&r.length!==2+2*t)}C.isHexString=se;var eo="0123456789abcdef";function G(r,t){if(t||(t={}),typeof r=="number"){L.checkSafeUint53(r,"invalid hexlify value");for(var s="";r;)s=eo[r&15]+s,r=Math.floor(r/16);return s.length?(s.length%2&&(s="0"+s),"0x"+s):"0x00"}if(typeof r=="bigint")return r=r.toString(16),r.length%2?"0x0"+r:"0x"+r;if(t.allowMissingPrefix&&typeof r=="string"&&r.substring(0,2)!=="0x"&&(r="0x"+r),ps(r))return r.toHexString();if(se(r))return r.length%2&&(t.hexPad==="left"?r="0x0"+r.substring(2):t.hexPad==="right"?r+="0":L.throwArgumentError("hex data is odd-length","value",r)),r.toLowerCase();if(dt(r)){for(var c="0x",y=0;y<r.length;y++){var z=r[y];c+=eo[(z&240)>>4]+eo[z&15]}return c}return L.throwArgumentError("invalid hexlify value","value",r)}C.hexlify=G;function Qx(r){if(typeof r!="string")r=G(r);else if(!se(r)||r.length%2)return null;return(r.length-2)/2}C.hexDataLength=Qx;function Zx(r,t,s){return typeof r!="string"?r=G(r):(!se(r)||r.length%2)&&L.throwArgumentError("invalid hexData","value",r),t=2+2*t,s!=null?"0x"+r.substring(t,2+2*s):"0x"+r.substring(t)}C.hexDataSlice=Zx;function Jx(r){var t="0x";return r.forEach(function(s){t+=G(s).substring(2)}),t}C.hexConcat=Jx;function Xx(r){var t=is(G(r,{hexPad:"left"}));return t==="0x"?"0x0":t}C.hexValue=Xx;function is(r){typeof r!="string"&&(r=G(r)),se(r)||L.throwArgumentError("invalid hex string","value",r),r=r.substring(2);for(var t=0;t<r.length&&r[t]==="0";)t++;return"0x"+r.substring(t)}C.hexStripZeros=is;function xt(r,t){for(typeof r!="string"?r=G(r):se(r)||L.throwArgumentError("invalid hex string","value",r),r.length>2*t+2&&L.throwArgumentError("value out of range","value",arguments[1]);r.length<2*t+2;)r="0x0"+r.substring(2);return r}C.hexZeroPad=xt;function fs(r){var t={r:"0x",s:"0x",_vs:"0x",recoveryParam:0,v:0,yParityAndS:"0x",compact:"0x"};if(ns(r)){var s=Ie(r);s.length===64?(t.v=27+(s[32]>>7),s[32]&=127,t.r=G(s.slice(0,32)),t.s=G(s.slice(32,64))):s.length===65?(t.r=G(s.slice(0,32)),t.s=G(s.slice(32,64)),t.v=s[64]):L.throwArgumentError("invalid signature string","signature",r),t.v<27&&(t.v===0||t.v===1?t.v+=27:L.throwArgumentError("signature invalid v byte","signature",r)),t.recoveryParam=1-t.v%2,t.recoveryParam&&(s[32]|=128),t._vs=G(s.slice(32,64))}else{if(t.r=r.r,t.s=r.s,t.v=r.v,t.recoveryParam=r.recoveryParam,t._vs=r._vs,t._vs!=null){var c=as(Ie(t._vs),32);t._vs=G(c);var y=c[0]>=128?1:0;t.recoveryParam==null?t.recoveryParam=y:t.recoveryParam!==y&&L.throwArgumentError("signature recoveryParam mismatch _vs","signature",r),c[0]&=127;var z=G(c);t.s==null?t.s=z:t.s!==z&&L.throwArgumentError("signature v mismatch _vs","signature",r)}if(t.recoveryParam==null)t.v==null?L.throwArgumentError("signature missing v and recoveryParam","signature",r):t.v===0||t.v===1?t.recoveryParam=t.v:t.recoveryParam=1-t.v%2;else if(t.v==null)t.v=27+t.recoveryParam;else{var _=t.v===0||t.v===1?t.v:1-t.v%2;t.recoveryParam!==_&&L.throwArgumentError("signature recoveryParam mismatch v","signature",r)}t.r==null||!se(t.r)?L.throwArgumentError("signature missing or invalid r","signature",r):t.r=xt(t.r,32),t.s==null||!se(t.s)?L.throwArgumentError("signature missing or invalid s","signature",r):t.s=xt(t.s,32);var k=Ie(t.s);k[0]>=128&&L.throwArgumentError("signature s out of range","signature",r),t.recoveryParam&&(k[0]|=128);var P=G(k);t._vs&&(se(t._vs)||L.throwArgumentError("signature invalid _vs","signature",r),t._vs=xt(t._vs,32)),t._vs==null?t._vs=P:t._vs!==P&&L.throwArgumentError("signature _vs mismatch v and s","signature",r)}return t.yParityAndS=t._vs,t.compact=t.r+t.yParityAndS.substring(2),t}C.splitSignature=fs;function Yx(r){return r=fs(r),G(ss([r.r,r.s,r.recoveryParam?"0x1c":"0x1b"]))}C.joinSignature=Yx});var ro=V(ht=>{"use strict";p();n();Object.defineProperty(ht,"__esModule",{value:!0});ht.version=void 0;ht.version="bignumber/5.7.0"});var zt=V(te=>{"use strict";p();n();var $x=te&&te.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(te,"__esModule",{value:!0});te._base16To36=te._base36To16=te.BigNumber=te.isBigNumberish=void 0;var ed=$x(Ox()),Ze=ed.default.BN,Me=Be(),Ue=Ce(),rd=ro(),he=new Ue.Logger(rd.version),to={},cs=9007199254740991;function td(r){return r!=null&&(gt.isBigNumber(r)||typeof r=="number"&&r%1===0||typeof r=="string"&&!!r.match(/^-?[0-9]+$/)||(0,Me.isHexString)(r)||typeof r=="bigint"||(0,Me.isBytes)(r))}te.isBigNumberish=td;var us=!1,gt=function(){function r(t,s){t!==to&&he.throwError("cannot call constructor directly; use BigNumber.from",Ue.Logger.errors.UNSUPPORTED_OPERATION,{operation:"new (BigNumber)"}),this._hex=s,this._isBigNumber=!0,Object.freeze(this)}return r.prototype.fromTwos=function(t){return $(w(this).fromTwos(t))},r.prototype.toTwos=function(t){return $(w(this).toTwos(t))},r.prototype.abs=function(){return this._hex[0]==="-"?r.from(this._hex.substring(1)):this},r.prototype.add=function(t){return $(w(this).add(w(t)))},r.prototype.sub=function(t){return $(w(this).sub(w(t)))},r.prototype.div=function(t){var s=r.from(t);return s.isZero()&&re("division-by-zero","div"),$(w(this).div(w(t)))},r.prototype.mul=function(t){return $(w(this).mul(w(t)))},r.prototype.mod=function(t){var s=w(t);return s.isNeg()&&re("division-by-zero","mod"),$(w(this).umod(s))},r.prototype.pow=function(t){var s=w(t);return s.isNeg()&&re("negative-power","pow"),$(w(this).pow(s))},r.prototype.and=function(t){var s=w(t);return(this.isNegative()||s.isNeg())&&re("unbound-bitwise-result","and"),$(w(this).and(s))},r.prototype.or=function(t){var s=w(t);return(this.isNegative()||s.isNeg())&&re("unbound-bitwise-result","or"),$(w(this).or(s))},r.prototype.xor=function(t){var s=w(t);return(this.isNegative()||s.isNeg())&&re("unbound-bitwise-result","xor"),$(w(this).xor(s))},r.prototype.mask=function(t){return(this.isNegative()||t<0)&&re("negative-width","mask"),$(w(this).maskn(t))},r.prototype.shl=function(t){return(this.isNegative()||t<0)&&re("negative-width","shl"),$(w(this).shln(t))},r.prototype.shr=function(t){return(this.isNegative()||t<0)&&re("negative-width","shr"),$(w(this).shrn(t))},r.prototype.eq=function(t){return w(this).eq(w(t))},r.prototype.lt=function(t){return w(this).lt(w(t))},r.prototype.lte=function(t){return w(this).lte(w(t))},r.prototype.gt=function(t){return w(this).gt(w(t))},r.prototype.gte=function(t){return w(this).gte(w(t))},r.prototype.isNegative=function(){return this._hex[0]==="-"},r.prototype.isZero=function(){return w(this).isZero()},r.prototype.toNumber=function(){try{return w(this).toNumber()}catch{re("overflow","toNumber",this.toString())}return null},r.prototype.toBigInt=function(){try{return BigInt(this.toString())}catch{}return he.throwError("this platform does not support BigInt",Ue.Logger.errors.UNSUPPORTED_OPERATION,{value:this.toString()})},r.prototype.toString=function(){return arguments.length>0&&(arguments[0]===10?us||(us=!0,he.warn("BigNumber.toString does not accept any parameters; base-10 is assumed")):arguments[0]===16?he.throwError("BigNumber.toString does not accept any parameters; use bigNumber.toHexString()",Ue.Logger.errors.UNEXPECTED_ARGUMENT,{}):he.throwError("BigNumber.toString does not accept parameters",Ue.Logger.errors.UNEXPECTED_ARGUMENT,{})),w(this).toString(10)},r.prototype.toHexString=function(){return this._hex},r.prototype.toJSON=function(t){return{type:"BigNumber",hex:this.toHexString()}},r.from=function(t){if(t instanceof r)return t;if(typeof t=="string")return t.match(/^-?0x[0-9a-f]+$/i)?new r(to,Je(t)):t.match(/^-?[0-9]+$/)?new r(to,Je(new Ze(t))):he.throwArgumentError("invalid BigNumber string","value",t);if(typeof t=="number")return t%1&&re("underflow","BigNumber.from",t),(t>=cs||t<=-cs)&&re("overflow","BigNumber.from",t),r.from(String(t));var s=t;if(typeof s=="bigint")return r.from(s.toString());if((0,Me.isBytes)(s))return r.from((0,Me.hexlify)(s));if(s)if(s.toHexString){var c=s.toHexString();if(typeof c=="string")return r.from(c)}else{var c=s._hex;if(c==null&&s.type==="BigNumber"&&(c=s.hex),typeof c=="string"&&((0,Me.isHexString)(c)||c[0]==="-"&&(0,Me.isHexString)(c.substring(1))))return r.from(c)}return he.throwArgumentError("invalid BigNumber value","value",t)},r.isBigNumber=function(t){return!!(t&&t._isBigNumber)},r}();te.BigNumber=gt;function Je(r){if(typeof r!="string")return Je(r.toString(16));if(r[0]==="-")return r=r.substring(1),r[0]==="-"&&he.throwArgumentError("invalid hex","value",r),r=Je(r),r==="0x00"?r:"-"+r;if(r.substring(0,2)!=="0x"&&(r="0x"+r),r==="0x")return"0x00";for(r.length%2&&(r="0x0"+r.substring(2));r.length>4&&r.substring(0,4)==="0x00";)r="0x"+r.substring(4);return r}function $(r){return gt.from(Je(r))}function w(r){var t=gt.from(r).toHexString();return t[0]==="-"?new Ze("-"+t.substring(3),16):new Ze(t.substring(2),16)}function re(r,t,s){var c={fault:r,operation:t};return s!=null&&(c.value=s),he.throwError(r,Ue.Logger.errors.NUMERIC_FAULT,c)}function od(r){return new Ze(r,36).toString(16)}te._base36To16=od;function pd(r){return new Ze(r,16).toString(36)}te._base16To36=pd});var ds=V(ue=>{"use strict";p();n();Object.defineProperty(ue,"__esModule",{value:!0});ue.FixedNumber=ue.FixedFormat=ue.parseFixed=ue.formatFixed=void 0;var Rt=Be(),$e=Ce(),nd=ro(),X=new $e.Logger(nd.version),ge=zt(),Xe={},ls=ge.BigNumber.from(0),ys=ge.BigNumber.from(-1);function xs(r,t,s,c){var y={fault:t,operation:s};return c!==void 0&&(y.value=c),X.throwError(r,$e.Logger.errors.NUMERIC_FAULT,y)}var Ye="0";for(;Ye.length<256;)Ye+=Ye;function oo(r){if(typeof r!="number")try{r=ge.BigNumber.from(r).toNumber()}catch{}return typeof r=="number"&&r>=0&&r<=256&&!(r%1)?"1"+Ye.substring(0,r):X.throwArgumentError("invalid decimal size","decimals",r)}function _t(r,t){t==null&&(t=0);var s=oo(t);r=ge.BigNumber.from(r);var c=r.lt(ls);c&&(r=r.mul(ys));for(var y=r.mod(s).toString();y.length<s.length-1;)y="0"+y;y=y.match(/^([0-9]*[1-9]|0)(0*)/)[1];var z=r.div(s).toString();return s.length===1?r=z:r=z+"."+y,c&&(r="-"+r),r}ue.formatFixed=_t;function ce(r,t){t==null&&(t=0);var s=oo(t);(typeof r!="string"||!r.match(/^-?[0-9.]+$/))&&X.throwArgumentError("invalid decimal value","value",r);var c=r.substring(0,1)==="-";c&&(r=r.substring(1)),r==="."&&X.throwArgumentError("missing value","value",r);var y=r.split(".");y.length>2&&X.throwArgumentError("too many decimal points","value",r);var z=y[0],_=y[1];for(z||(z="0"),_||(_="0");_[_.length-1]==="0";)_=_.substring(0,_.length-1);for(_.length>s.length-1&&xs("fractional component exceeds decimals","underflow","parseFixed"),_===""&&(_="0");_.length<s.length-1;)_+="0";var k=ge.BigNumber.from(z),P=ge.BigNumber.from(_),T=k.mul(s).add(P);return c&&(T=T.mul(ys)),T}ue.parseFixed=ce;var bt=function(){function r(t,s,c,y){t!==Xe&&X.throwError("cannot use FixedFormat constructor; use FixedFormat.from",$e.Logger.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.signed=s,this.width=c,this.decimals=y,this.name=(s?"":"u")+"fixed"+String(c)+"x"+String(y),this._multiplier=oo(y),Object.freeze(this)}return r.from=function(t){if(t instanceof r)return t;typeof t=="number"&&(t="fixed128x"+t);var s=!0,c=128,y=18;if(typeof t=="string"){if(t!=="fixed")if(t==="ufixed")s=!1;else{var z=t.match(/^(u?)fixed([0-9]+)x([0-9]+)$/);z||X.throwArgumentError("invalid fixed format","format",t),s=z[1]!=="u",c=parseInt(z[2]),y=parseInt(z[3])}}else if(t){var _=function(k,P,T){return t[k]==null?T:(typeof t[k]!==P&&X.throwArgumentError("invalid fixed format ("+k+" not "+P+")","format."+k,t[k]),t[k])};s=_("signed","boolean",s),c=_("width","number",c),y=_("decimals","number",y)}return c%8&&X.throwArgumentError("invalid fixed format width (not byte aligned)","format.width",c),y>80&&X.throwArgumentError("invalid fixed format (decimals too large)","format.decimals",y),new r(Xe,s,c,y)},r}();ue.FixedFormat=bt;var po=function(){function r(t,s,c,y){t!==Xe&&X.throwError("cannot use FixedNumber constructor; use FixedNumber.from",$e.Logger.errors.UNSUPPORTED_OPERATION,{operation:"new FixedFormat"}),this.format=y,this._hex=s,this._value=c,this._isFixedNumber=!0,Object.freeze(this)}return r.prototype._checkFormat=function(t){this.format.name!==t.format.name&&X.throwArgumentError("incompatible format; use fixedNumber.toFormat","other",t)},r.prototype.addUnsafe=function(t){this._checkFormat(t);var s=ce(this._value,this.format.decimals),c=ce(t._value,t.format.decimals);return r.fromValue(s.add(c),this.format.decimals,this.format)},r.prototype.subUnsafe=function(t){this._checkFormat(t);var s=ce(this._value,this.format.decimals),c=ce(t._value,t.format.decimals);return r.fromValue(s.sub(c),this.format.decimals,this.format)},r.prototype.mulUnsafe=function(t){this._checkFormat(t);var s=ce(this._value,this.format.decimals),c=ce(t._value,t.format.decimals);return r.fromValue(s.mul(c).div(this.format._multiplier),this.format.decimals,this.format)},r.prototype.divUnsafe=function(t){this._checkFormat(t);var s=ce(this._value,this.format.decimals),c=ce(t._value,t.format.decimals);return r.fromValue(s.mul(this.format._multiplier).div(c),this.format.decimals,this.format)},r.prototype.floor=function(){var t=this.toString().split(".");t.length===1&&t.push("0");var s=r.from(t[0],this.format),c=!t[1].match(/^(0*)$/);return this.isNegative()&&c&&(s=s.subUnsafe(ms.toFormat(s.format))),s},r.prototype.ceiling=function(){var t=this.toString().split(".");t.length===1&&t.push("0");var s=r.from(t[0],this.format),c=!t[1].match(/^(0*)$/);return!this.isNegative()&&c&&(s=s.addUnsafe(ms.toFormat(s.format))),s},r.prototype.round=function(t){t==null&&(t=0);var s=this.toString().split(".");if(s.length===1&&s.push("0"),(t<0||t>80||t%1)&&X.throwArgumentError("invalid decimal count","decimals",t),s[1].length<=t)return this;var c=r.from("1"+Ye.substring(0,t),this.format),y=sd.toFormat(this.format);return this.mulUnsafe(c).addUnsafe(y).floor().divUnsafe(c)},r.prototype.isZero=function(){return this._value==="0.0"||this._value==="0"},r.prototype.isNegative=function(){return this._value[0]==="-"},r.prototype.toString=function(){return this._value},r.prototype.toHexString=function(t){if(t==null)return this._hex;t%8&&X.throwArgumentError("invalid byte width","width",t);var s=ge.BigNumber.from(this._hex).fromTwos(this.format.width).toTwos(t).toHexString();return(0,Rt.hexZeroPad)(s,t/8)},r.prototype.toUnsafeFloat=function(){return parseFloat(this.toString())},r.prototype.toFormat=function(t){return r.fromString(this._value,t)},r.fromValue=function(t,s,c){return c==null&&s!=null&&!(0,ge.isBigNumberish)(s)&&(c=s,s=null),s==null&&(s=0),c==null&&(c="fixed"),r.fromString(_t(t,s),bt.from(c))},r.fromString=function(t,s){s==null&&(s="fixed");var c=bt.from(s),y=ce(t,c.decimals);!c.signed&&y.lt(ls)&&xs("unsigned value cannot be negative","overflow","value",t);var z=null;c.signed?z=y.toTwos(c.width).toHexString():(z=y.toHexString(),z=(0,Rt.hexZeroPad)(z,c.width/8));var _=_t(y,c.decimals);return new r(Xe,z,_,c)},r.fromBytes=function(t,s){s==null&&(s="fixed");var c=bt.from(s);if((0,Rt.arrayify)(t).length>c.width/8)throw new Error("overflow");var y=ge.BigNumber.from(t);c.signed&&(y=y.fromTwos(c.width));var z=y.toTwos((c.signed?0:1)+c.width).toHexString(),_=_t(y,c.decimals);return new r(Xe,z,_,c)},r.from=function(t,s){if(typeof t=="string")return r.fromString(t,s);if((0,Rt.isBytes)(t))return r.fromBytes(t,s);try{return r.fromValue(t,0,s)}catch(c){if(c.code!==$e.Logger.errors.INVALID_ARGUMENT)throw c}return X.throwArgumentError("invalid FixedNumber value","value",t)},r.isFixedNumber=function(t){return!!(t&&t._isFixedNumber)},r}();ue.FixedNumber=po;var ms=po.from(1),sd=po.from("0.5")});var gs=V(W=>{"use strict";p();n();Object.defineProperty(W,"__esModule",{value:!0});W._base36To16=W._base16To36=W.parseFixed=W.FixedNumber=W.FixedFormat=W.formatFixed=W.BigNumber=void 0;var ad=zt();Object.defineProperty(W,"BigNumber",{enumerable:!0,get:function(){return ad.BigNumber}});var Et=ds();Object.defineProperty(W,"formatFixed",{enumerable:!0,get:function(){return Et.formatFixed}});Object.defineProperty(W,"FixedFormat",{enumerable:!0,get:function(){return Et.FixedFormat}});Object.defineProperty(W,"FixedNumber",{enumerable:!0,get:function(){return Et.FixedNumber}});Object.defineProperty(W,"parseFixed",{enumerable:!0,get:function(){return Et.parseFixed}});var hs=zt();Object.defineProperty(W,"_base16To36",{enumerable:!0,get:function(){return hs._base16To36}});Object.defineProperty(W,"_base36To16",{enumerable:!0,get:function(){return hs._base36To16}})});var zs=V((wb,Pt)=>{p();n();(function(){"use strict";var r="input is invalid type",t="finalize already called",s=typeof self=="object",c=s?self:{};c.JS_SHA3_NO_WINDOW&&(s=!1);var y=!s&&typeof self=="object",z=!c.JS_SHA3_NO_NODE_JS&&typeof process=="object"&&process.versions&&process.versions.node;(z||y)&&(c=self);var _=!c.JS_SHA3_NO_COMMON_JS&&typeof Pt=="object"&&Pt.exports,k=typeof define=="function"&&define.amd,P=!c.JS_SHA3_NO_ARRAY_BUFFER&&typeof ArrayBuffer<"u",T="0123456789abcdef".split(""),J=[31,7936,2031616,520093696],M=[4,1024,262144,67108864],Pe=[1,256,65536,16777216],j=[6,1536,393216,100663296],ne=[0,8,16,24],Un=[1,0,32898,0,32906,2147483648,2147516416,2147483648,32907,0,2147483649,0,2147516545,2147483648,32777,2147483648,138,0,136,0,2147516425,0,2147483658,0,2147516555,0,139,2147483648,32905,2147483648,32771,2147483648,32770,2147483648,128,2147483648,32778,0,2147483658,2147483648,2147516545,2147483648,32896,2147483648,2147483649,0,2147516424,2147483648],D=[224,256,384,512],Zt=[128,256],jn=["hex","buffer","arrayBuffer","array","digest"],On={128:168,256:136};(c.JS_SHA3_NO_NODE_JS||!Array.isArray)&&(Array.isArray=function(o){return Object.prototype.toString.call(o)==="[object Array]"}),P&&(c.JS_SHA3_NO_ARRAY_BUFFER_IS_VIEW||!ArrayBuffer.isView)&&(ArrayBuffer.isView=function(o){return typeof o=="object"&&o.buffer&&o.buffer.constructor===ArrayBuffer});for(var Ln=function(o,x,d){return function(h){return new H(o,x,o).update(h)[d]()}},Hn=function(o,x,d){return function(h,R){return new H(o,x,R).update(h)[d]()}},Fn=function(o,x,d){return function(h,R,q,I){return de["cshake"+o].update(h,R,q,I)[d]()}},Kn=function(o,x,d){return function(h,R,q,I){return de["kmac"+o].update(h,R,q,I)[d]()}},ur=function(o,x,d,h){for(var R=0;R<jn.length;++R){var q=jn[R];o[q]=x(d,h,q)}return o},Vn=function(o,x){var d=Ln(o,x,"hex");return d.create=function(){return new H(o,x,o)},d.update=function(h){return d.create().update(h)},ur(d,Ln,o,x)},Bx=function(o,x){var d=Hn(o,x,"hex");return d.create=function(h){return new H(o,x,h)},d.update=function(h,R){return d.create(R).update(h)},ur(d,Hn,o,x)},Mx=function(o,x){var d=On[o],h=Fn(o,x,"hex");return h.create=function(R,q,I){return!q&&!I?de["shake"+o].create(R):new H(o,x,R).bytepad([q,I],d)},h.update=function(R,q,I,S){return h.create(q,I,S).update(R)},ur(h,Fn,o,x)},Ux=function(o,x){var d=On[o],h=Kn(o,x,"hex");return h.create=function(R,q,I){return new Xt(o,x,q).bytepad(["KMAC",I],d).bytepad([R],d)},h.update=function(R,q,I,S){return h.create(R,I,S).update(q)},ur(h,Kn,o,x)},Gn=[{name:"keccak",padding:Pe,bits:D,createMethod:Vn},{name:"sha3",padding:j,bits:D,createMethod:Vn},{name:"shake",padding:J,bits:Zt,createMethod:Bx},{name:"cshake",padding:M,bits:Zt,createMethod:Mx},{name:"kmac",padding:M,bits:Zt,createMethod:Ux}],de={},Ge=[],Se=0;Se<Gn.length;++Se)for(var De=Gn[Se],mr=De.bits,We=0;We<mr.length;++We){var Jt=De.name+"_"+mr[We];if(Ge.push(Jt),de[Jt]=De.createMethod(mr[We],De.padding),De.name!=="sha3"){var Wn=De.name+mr[We];Ge.push(Wn),de[Wn]=de[Jt]}}function H(o,x,d){this.blocks=[],this.s=[],this.padding=x,this.outputBits=d,this.reset=!0,this.finalized=!1,this.block=0,this.start=0,this.blockCount=1600-(o<<1)>>5,this.byteCount=this.blockCount<<2,this.outputBlocks=d>>5,this.extraBytes=(d&31)>>3;for(var h=0;h<50;++h)this.s[h]=0}H.prototype.update=function(o){if(this.finalized)throw new Error(t);var x,d=typeof o;if(d!=="string"){if(d==="object"){if(o===null)throw new Error(r);if(P&&o.constructor===ArrayBuffer)o=new Uint8Array(o);else if(!Array.isArray(o)&&(!P||!ArrayBuffer.isView(o)))throw new Error(r)}else throw new Error(r);x=!0}for(var h=this.blocks,R=this.byteCount,q=o.length,I=this.blockCount,S=0,F=this.s,A,O;S<q;){if(this.reset)for(this.reset=!1,h[0]=this.block,A=1;A<I+1;++A)h[A]=0;if(x)for(A=this.start;S<q&&A<R;++S)h[A>>2]|=o[S]<<ne[A++&3];else for(A=this.start;S<q&&A<R;++S)O=o.charCodeAt(S),O<128?h[A>>2]|=O<<ne[A++&3]:O<2048?(h[A>>2]|=(192|O>>6)<<ne[A++&3],h[A>>2]|=(128|O&63)<<ne[A++&3]):O<55296||O>=57344?(h[A>>2]|=(224|O>>12)<<ne[A++&3],h[A>>2]|=(128|O>>6&63)<<ne[A++&3],h[A>>2]|=(128|O&63)<<ne[A++&3]):(O=65536+((O&1023)<<10|o.charCodeAt(++S)&1023),h[A>>2]|=(240|O>>18)<<ne[A++&3],h[A>>2]|=(128|O>>12&63)<<ne[A++&3],h[A>>2]|=(128|O>>6&63)<<ne[A++&3],h[A>>2]|=(128|O&63)<<ne[A++&3]);if(this.lastByteIndex=A,A>=R){for(this.start=A-R,this.block=h[I],A=0;A<I;++A)F[A]^=h[A];Qe(F),this.reset=!0}else this.start=A}return this},H.prototype.encode=function(o,x){var d=o&255,h=1,R=[d];for(o=o>>8,d=o&255;d>0;)R.unshift(d),o=o>>8,d=o&255,++h;return x?R.push(h):R.unshift(h),this.update(R),R.length},H.prototype.encodeString=function(o){var x,d=typeof o;if(d!=="string"){if(d==="object"){if(o===null)throw new Error(r);if(P&&o.constructor===ArrayBuffer)o=new Uint8Array(o);else if(!Array.isArray(o)&&(!P||!ArrayBuffer.isView(o)))throw new Error(r)}else throw new Error(r);x=!0}var h=0,R=o.length;if(x)h=R;else for(var q=0;q<o.length;++q){var I=o.charCodeAt(q);I<128?h+=1:I<2048?h+=2:I<55296||I>=57344?h+=3:(I=65536+((I&1023)<<10|o.charCodeAt(++q)&1023),h+=4)}return h+=this.encode(h*8),this.update(o),h},H.prototype.bytepad=function(o,x){for(var d=this.encode(x),h=0;h<o.length;++h)d+=this.encodeString(o[h]);var R=x-d%x,q=[];return q.length=R,this.update(q),this},H.prototype.finalize=function(){if(!this.finalized){this.finalized=!0;var o=this.blocks,x=this.lastByteIndex,d=this.blockCount,h=this.s;if(o[x>>2]|=this.padding[x&3],this.lastByteIndex===this.byteCount)for(o[0]=o[d],x=1;x<d+1;++x)o[x]=0;for(o[d-1]|=2147483648,x=0;x<d;++x)h[x]^=o[x];Qe(h)}},H.prototype.toString=H.prototype.hex=function(){this.finalize();for(var o=this.blockCount,x=this.s,d=this.outputBlocks,h=this.extraBytes,R=0,q=0,I="",S;q<d;){for(R=0;R<o&&q<d;++R,++q)S=x[R],I+=T[S>>4&15]+T[S&15]+T[S>>12&15]+T[S>>8&15]+T[S>>20&15]+T[S>>16&15]+T[S>>28&15]+T[S>>24&15];q%o===0&&(Qe(x),R=0)}return h&&(S=x[R],I+=T[S>>4&15]+T[S&15],h>1&&(I+=T[S>>12&15]+T[S>>8&15]),h>2&&(I+=T[S>>20&15]+T[S>>16&15])),I},H.prototype.arrayBuffer=function(){this.finalize();var o=this.blockCount,x=this.s,d=this.outputBlocks,h=this.extraBytes,R=0,q=0,I=this.outputBits>>3,S;h?S=new ArrayBuffer(d+1<<2):S=new ArrayBuffer(I);for(var F=new Uint32Array(S);q<d;){for(R=0;R<o&&q<d;++R,++q)F[q]=x[R];q%o===0&&Qe(x)}return h&&(F[R]=x[R],S=S.slice(0,I)),S},H.prototype.buffer=H.prototype.arrayBuffer,H.prototype.digest=H.prototype.array=function(){this.finalize();for(var o=this.blockCount,x=this.s,d=this.outputBlocks,h=this.extraBytes,R=0,q=0,I=[],S,F;q<d;){for(R=0;R<o&&q<d;++R,++q)S=q<<2,F=x[R],I[S]=F&255,I[S+1]=F>>8&255,I[S+2]=F>>16&255,I[S+3]=F>>24&255;q%o===0&&Qe(x)}return h&&(S=q<<2,F=x[R],I[S]=F&255,h>1&&(I[S+1]=F>>8&255),h>2&&(I[S+2]=F>>16&255)),I};function Xt(o,x,d){H.call(this,o,x,d)}Xt.prototype=new H,Xt.prototype.finalize=function(){return this.encode(this.outputBits,!0),H.prototype.finalize.call(this)};var Qe=function(o){var x,d,h,R,q,I,S,F,A,O,lr,yr,xr,dr,hr,gr,zr,Rr,_r,br,Er,Pr,Sr,qr,Ir,vr,Ar,Tr,wr,Nr,Dr,Cr,kr,Br,Mr,Ur,jr,Or,Lr,Hr,Fr,Kr,Vr,Gr,Wr,Qr,Zr,Jr,Xr,Yr,$r,et,rt,tt,ot,pt,nt,st,at,it,ft,ct,ut;for(h=0;h<48;h+=2)R=o[0]^o[10]^o[20]^o[30]^o[40],q=o[1]^o[11]^o[21]^o[31]^o[41],I=o[2]^o[12]^o[22]^o[32]^o[42],S=o[3]^o[13]^o[23]^o[33]^o[43],F=o[4]^o[14]^o[24]^o[34]^o[44],A=o[5]^o[15]^o[25]^o[35]^o[45],O=o[6]^o[16]^o[26]^o[36]^o[46],lr=o[7]^o[17]^o[27]^o[37]^o[47],yr=o[8]^o[18]^o[28]^o[38]^o[48],xr=o[9]^o[19]^o[29]^o[39]^o[49],x=yr^(I<<1|S>>>31),d=xr^(S<<1|I>>>31),o[0]^=x,o[1]^=d,o[10]^=x,o[11]^=d,o[20]^=x,o[21]^=d,o[30]^=x,o[31]^=d,o[40]^=x,o[41]^=d,x=R^(F<<1|A>>>31),d=q^(A<<1|F>>>31),o[2]^=x,o[3]^=d,o[12]^=x,o[13]^=d,o[22]^=x,o[23]^=d,o[32]^=x,o[33]^=d,o[42]^=x,o[43]^=d,x=I^(O<<1|lr>>>31),d=S^(lr<<1|O>>>31),o[4]^=x,o[5]^=d,o[14]^=x,o[15]^=d,o[24]^=x,o[25]^=d,o[34]^=x,o[35]^=d,o[44]^=x,o[45]^=d,x=F^(yr<<1|xr>>>31),d=A^(xr<<1|yr>>>31),o[6]^=x,o[7]^=d,o[16]^=x,o[17]^=d,o[26]^=x,o[27]^=d,o[36]^=x,o[37]^=d,o[46]^=x,o[47]^=d,x=O^(R<<1|q>>>31),d=lr^(q<<1|R>>>31),o[8]^=x,o[9]^=d,o[18]^=x,o[19]^=d,o[28]^=x,o[29]^=d,o[38]^=x,o[39]^=d,o[48]^=x,o[49]^=d,dr=o[0],hr=o[1],Qr=o[11]<<4|o[10]>>>28,Zr=o[10]<<4|o[11]>>>28,Tr=o[20]<<3|o[21]>>>29,wr=o[21]<<3|o[20]>>>29,it=o[31]<<9|o[30]>>>23,ft=o[30]<<9|o[31]>>>23,Kr=o[40]<<18|o[41]>>>14,Vr=o[41]<<18|o[40]>>>14,Br=o[2]<<1|o[3]>>>31,Mr=o[3]<<1|o[2]>>>31,gr=o[13]<<12|o[12]>>>20,zr=o[12]<<12|o[13]>>>20,Jr=o[22]<<10|o[23]>>>22,Xr=o[23]<<10|o[22]>>>22,Nr=o[33]<<13|o[32]>>>19,Dr=o[32]<<13|o[33]>>>19,ct=o[42]<<2|o[43]>>>30,ut=o[43]<<2|o[42]>>>30,tt=o[5]<<30|o[4]>>>2,ot=o[4]<<30|o[5]>>>2,Ur=o[14]<<6|o[15]>>>26,jr=o[15]<<6|o[14]>>>26,Rr=o[25]<<11|o[24]>>>21,_r=o[24]<<11|o[25]>>>21,Yr=o[34]<<15|o[35]>>>17,$r=o[35]<<15|o[34]>>>17,Cr=o[45]<<29|o[44]>>>3,kr=o[44]<<29|o[45]>>>3,qr=o[6]<<28|o[7]>>>4,Ir=o[7]<<28|o[6]>>>4,pt=o[17]<<23|o[16]>>>9,nt=o[16]<<23|o[17]>>>9,Or=o[26]<<25|o[27]>>>7,Lr=o[27]<<25|o[26]>>>7,br=o[36]<<21|o[37]>>>11,Er=o[37]<<21|o[36]>>>11,et=o[47]<<24|o[46]>>>8,rt=o[46]<<24|o[47]>>>8,Gr=o[8]<<27|o[9]>>>5,Wr=o[9]<<27|o[8]>>>5,vr=o[18]<<20|o[19]>>>12,Ar=o[19]<<20|o[18]>>>12,st=o[29]<<7|o[28]>>>25,at=o[28]<<7|o[29]>>>25,Hr=o[38]<<8|o[39]>>>24,Fr=o[39]<<8|o[38]>>>24,Pr=o[48]<<14|o[49]>>>18,Sr=o[49]<<14|o[48]>>>18,o[0]=dr^~gr&Rr,o[1]=hr^~zr&_r,o[10]=qr^~vr&Tr,o[11]=Ir^~Ar&wr,o[20]=Br^~Ur&Or,o[21]=Mr^~jr&Lr,o[30]=Gr^~Qr&Jr,o[31]=Wr^~Zr&Xr,o[40]=tt^~pt&st,o[41]=ot^~nt&at,o[2]=gr^~Rr&br,o[3]=zr^~_r&Er,o[12]=vr^~Tr&Nr,o[13]=Ar^~wr&Dr,o[22]=Ur^~Or&Hr,o[23]=jr^~Lr&Fr,o[32]=Qr^~Jr&Yr,o[33]=Zr^~Xr&$r,o[42]=pt^~st&it,o[43]=nt^~at&ft,o[4]=Rr^~br&Pr,o[5]=_r^~Er&Sr,o[14]=Tr^~Nr&Cr,o[15]=wr^~Dr&kr,o[24]=Or^~Hr&Kr,o[25]=Lr^~Fr&Vr,o[34]=Jr^~Yr&et,o[35]=Xr^~$r&rt,o[44]=st^~it&ct,o[45]=at^~ft&ut,o[6]=br^~Pr&dr,o[7]=Er^~Sr&hr,o[16]=Nr^~Cr&qr,o[17]=Dr^~kr&Ir,o[26]=Hr^~Kr&Br,o[27]=Fr^~Vr&Mr,o[36]=Yr^~et&Gr,o[37]=$r^~rt&Wr,o[46]=it^~ct&tt,o[47]=ft^~ut&ot,o[8]=Pr^~dr&gr,o[9]=Sr^~hr&zr,o[18]=Cr^~qr&vr,o[19]=kr^~Ir&Ar,o[28]=Kr^~Br&Ur,o[29]=Vr^~Mr&jr,o[38]=et^~Gr&Qr,o[39]=rt^~Wr&Zr,o[48]=ct^~tt&pt,o[49]=ut^~ot&nt,o[0]^=Un[h],o[1]^=Un[h+1]};if(_)Pt.exports=de;else{for(Se=0;Se<Ge.length;++Se)c[Ge[Se]]=de[Ge[Se]];k&&define(function(){return de})}})()});var Rs=V(je=>{"use strict";p();n();var id=je&&je.__importDefault||function(r){return r&&r.__esModule?r:{default:r}};Object.defineProperty(je,"__esModule",{value:!0});je.keccak256=void 0;var fd=id(zs()),cd=Be();function ud(r){return"0x"+fd.default.keccak_256((0,cd.arrayify)(r))}je.keccak256=ud});var _s=V(St=>{"use strict";p();n();Object.defineProperty(St,"__esModule",{value:!0});St.version=void 0;St.version="rlp/5.7.0"});var Is=V(Oe=>{"use strict";p();n();Object.defineProperty(Oe,"__esModule",{value:!0});Oe.decode=Oe.encode=void 0;var ve=Be(),ze=Ce(),md=_s(),me=new ze.Logger(md.version);function bs(r){for(var t=[];r;)t.unshift(r&255),r>>=8;return t}function Es(r,t,s){for(var c=0,y=0;y<s;y++)c=c*256+r[t+y];return c}function Ss(r){if(Array.isArray(r)){var t=[];if(r.forEach(function(z){t=t.concat(Ss(z))}),t.length<=55)return t.unshift(192+t.length),t;var s=bs(t.length);return s.unshift(247+s.length),s.concat(t)}(0,ve.isBytesLike)(r)||me.throwArgumentError("RLP object must be BytesLike","object",r);var c=Array.prototype.slice.call((0,ve.arrayify)(r));if(c.length===1&&c[0]<=127)return c;if(c.length<=55)return c.unshift(128+c.length),c;var y=bs(c.length);return y.unshift(183+y.length),y.concat(c)}function ld(r){return(0,ve.hexlify)(Ss(r))}Oe.encode=ld;function Ps(r,t,s,c){for(var y=[];s<t+1+c;){var z=qs(r,s);y.push(z.result),s+=z.consumed,s>t+1+c&&me.throwError("child data too short",ze.Logger.errors.BUFFER_OVERRUN,{})}return{consumed:1+c,result:y}}function qs(r,t){if(r.length===0&&me.throwError("data too short",ze.Logger.errors.BUFFER_OVERRUN,{}),r[t]>=248){var s=r[t]-247;t+1+s>r.length&&me.throwError("data short segment too short",ze.Logger.errors.BUFFER_OVERRUN,{});var c=Es(r,t+1,s);return t+1+s+c>r.length&&me.throwError("data long segment too short",ze.Logger.errors.BUFFER_OVERRUN,{}),Ps(r,t,t+1+s,s+c)}else if(r[t]>=192){var y=r[t]-192;return t+1+y>r.length&&me.throwError("data array too short",ze.Logger.errors.BUFFER_OVERRUN,{}),Ps(r,t,t+1,y)}else if(r[t]>=184){var s=r[t]-183;t+1+s>r.length&&me.throwError("data array too short",ze.Logger.errors.BUFFER_OVERRUN,{});var z=Es(r,t+1,s);t+1+s+z>r.length&&me.throwError("data array too short",ze.Logger.errors.BUFFER_OVERRUN,{});var _=(0,ve.hexlify)(r.slice(t+1+s,t+1+s+z));return{consumed:1+s+z,result:_}}else if(r[t]>=128){var k=r[t]-128;t+1+k>r.length&&me.throwError("data too short",ze.Logger.errors.BUFFER_OVERRUN,{});var _=(0,ve.hexlify)(r.slice(t+1,t+1+k));return{consumed:1+k,result:_}}return{consumed:1,result:(0,ve.hexlify)(r[t])}}function yd(r){var t=(0,ve.arrayify)(r),s=qs(t,0);return s.consumed!==t.length&&me.throwArgumentError("invalid rlp data","data",r),s.result}Oe.decode=yd});var vs=V(qt=>{"use strict";p();n();Object.defineProperty(qt,"__esModule",{value:!0});qt.version=void 0;qt.version="address/5.7.0"});var Ns=V(oe=>{"use strict";p();n();Object.defineProperty(oe,"__esModule",{value:!0});oe.getCreate2Address=oe.getContractAddress=oe.getIcapAddress=oe.isAddress=oe.getAddress=void 0;var Re=Be(),no=gs(),so=Rs(),xd=Is(),dd=Ce(),hd=vs(),qe=new dd.Logger(hd.version);function As(r){(0,Re.isHexString)(r,20)||qe.throwArgumentError("invalid address","address",r),r=r.toLowerCase();for(var t=r.substring(2).split(""),s=new Uint8Array(40),c=0;c<40;c++)s[c]=t[c].charCodeAt(0);for(var y=(0,Re.arrayify)((0,so.keccak256)(s)),c=0;c<40;c+=2)y[c>>1]>>4>=8&&(t[c]=t[c].toUpperCase()),(y[c>>1]&15)>=8&&(t[c+1]=t[c+1].toUpperCase());return"0x"+t.join("")}var gd=9007199254740991;function zd(r){return Math.log10?Math.log10(r):Math.log(r)/Math.LN10}var ao={};for(ae=0;ae<10;ae++)ao[String(ae)]=String(ae);var ae;for(ae=0;ae<26;ae++)ao[String.fromCharCode(65+ae)]=String(10+ae);var ae,Ts=Math.floor(zd(gd));function ws(r){r=r.toUpperCase(),r=r.substring(4)+r.substring(0,2)+"00";for(var t=r.split("").map(function(y){return ao[y]}).join("");t.length>=Ts;){var s=t.substring(0,Ts);t=parseInt(s,10)%97+t.substring(s.length)}for(var c=String(98-parseInt(t,10)%97);c.length<2;)c="0"+c;return c}function Ae(r){var t=null;if(typeof r!="string"&&qe.throwArgumentError("invalid address","address",r),r.match(/^(0x)?[0-9a-fA-F]{40}$/))r.substring(0,2)!=="0x"&&(r="0x"+r),t=As(r),r.match(/([A-F].*[a-f])|([a-f].*[A-F])/)&&t!==r&&qe.throwArgumentError("bad address checksum","address",r);else if(r.match(/^XE[0-9]{2}[0-9A-Za-z]{30,31}$/)){for(r.substring(2,4)!==ws(r)&&qe.throwArgumentError("bad icap checksum","address",r),t=(0,no._base36To16)(r.substring(4));t.length<40;)t="0"+t;t=As("0x"+t)}else qe.throwArgumentError("invalid address","address",r);return t}oe.getAddress=Ae;function Rd(r){try{return Ae(r),!0}catch{}return!1}oe.isAddress=Rd;function _d(r){for(var t=(0,no._base16To36)(Ae(r).substring(2)).toUpperCase();t.length<30;)t="0"+t;return"XE"+ws("XE00"+t)+t}oe.getIcapAddress=_d;function bd(r){var t=null;try{t=Ae(r.from)}catch{qe.throwArgumentError("missing from address","transaction",r)}var s=(0,Re.stripZeros)((0,Re.arrayify)(no.BigNumber.from(r.nonce).toHexString()));return Ae((0,Re.hexDataSlice)((0,so.keccak256)((0,xd.encode)([t,s])),12))}oe.getContractAddress=bd;function Ed(r,t,s){return(0,Re.hexDataLength)(t)!==32&&qe.throwArgumentError("salt must be 32 bytes","salt",t),(0,Re.hexDataLength)(s)!==32&&qe.throwArgumentError("initCodeHash must be 32 bytes","initCodeHash",s),Ae((0,Re.hexDataSlice)((0,so.keccak256)((0,Re.concat)(["0xff",Ae(r),t,s])),12))}oe.getCreate2Address=Ed});var ob=V((MN,fr)=>{p();n();function tb(r){return r&&r.__esModule?r:{default:r}}fr.exports=tb,fr.exports.__esModule=!0,fr.exports.default=fr.exports});var sb=V((ON,Mn)=>{"use strict";p();n();var pb=Object.prototype.hasOwnProperty,Y="~";function cr(){}Object.create&&(cr.prototype=Object.create(null),new cr().__proto__||(Y=!1));function nb(r,t,s){this.fn=r,this.context=t,this.once=s||!1}function kx(r,t,s,c,y){if(typeof s!="function")throw new TypeError("The listener must be a function");var z=new nb(s,c||r,y),_=Y?Y+t:t;return r._events[_]?r._events[_].fn?r._events[_]=[r._events[_],z]:r._events[_].push(z):(r._events[_]=z,r._eventsCount++),r}function Qt(r,t){--r._eventsCount===0?r._events=new cr:delete r._events[t]}function Z(){this._events=new cr,this._eventsCount=0}Z.prototype.eventNames=function(){var t=[],s,c;if(this._eventsCount===0)return t;for(c in s=this._events)pb.call(s,c)&&t.push(Y?c.slice(1):c);return Object.getOwnPropertySymbols?t.concat(Object.getOwnPropertySymbols(s)):t};Z.prototype.listeners=function(t){var s=Y?Y+t:t,c=this._events[s];if(!c)return[];if(c.fn)return[c.fn];for(var y=0,z=c.length,_=new Array(z);y<z;y++)_[y]=c[y].fn;return _};Z.prototype.listenerCount=function(t){var s=Y?Y+t:t,c=this._events[s];return c?c.fn?1:c.length:0};Z.prototype.emit=function(t,s,c,y,z,_){var k=Y?Y+t:t;if(!this._events[k])return!1;var P=this._events[k],T=arguments.length,J,M;if(P.fn){switch(P.once&&this.removeListener(t,P.fn,void 0,!0),T){case 1:return P.fn.call(P.context),!0;case 2:return P.fn.call(P.context,s),!0;case 3:return P.fn.call(P.context,s,c),!0;case 4:return P.fn.call(P.context,s,c,y),!0;case 5:return P.fn.call(P.context,s,c,y,z),!0;case 6:return P.fn.call(P.context,s,c,y,z,_),!0}for(M=1,J=new Array(T-1);M<T;M++)J[M-1]=arguments[M];P.fn.apply(P.context,J)}else{var Pe=P.length,j;for(M=0;M<Pe;M++)switch(P[M].once&&this.removeListener(t,P[M].fn,void 0,!0),T){case 1:P[M].fn.call(P[M].context);break;case 2:P[M].fn.call(P[M].context,s);break;case 3:P[M].fn.call(P[M].context,s,c);break;case 4:P[M].fn.call(P[M].context,s,c,y);break;default:if(!J)for(j=1,J=new Array(T-1);j<T;j++)J[j-1]=arguments[j];P[M].fn.apply(P[M].context,J)}}return!0};Z.prototype.on=function(t,s,c){return kx(this,t,s,c,!1)};Z.prototype.once=function(t,s,c){return kx(this,t,s,c,!0)};Z.prototype.removeListener=function(t,s,c,y){var z=Y?Y+t:t;if(!this._events[z])return this;if(!s)return Qt(this,z),this;var _=this._events[z];if(_.fn)_.fn===s&&(!y||_.once)&&(!c||_.context===c)&&Qt(this,z);else{for(var k=0,P=[],T=_.length;k<T;k++)(_[k].fn!==s||y&&!_[k].once||c&&_[k].context!==c)&&P.push(_[k]);P.length?this._events[z]=P.length===1?P[0]:P:Qt(this,z)}return this};Z.prototype.removeAllListeners=function(t){var s;return t?(s=Y?Y+t:t,this._events[s]&&Qt(this,s)):(this._events=new cr,this._eventsCount=0),this};Z.prototype.off=Z.prototype.removeListener;Z.prototype.addListener=Z.prototype.on;Z.prefixed=Y;Z.EventEmitter=Z;typeof Mn<"u"&&(Mn.exports=Z)});p();n();var Ds=Yt(jx()),Cs=Yt(Ns()),io=Yt(Lx());var B=e.string().min(2,{message:"Must be 2 or more characters long"}).regex(/^0x[0-9A-Fa-f]*$/,{message:"String must be '0x'-prefixed and followed by valid hex characters"}),m=e.number().transform(r=>`0x${r.toString(16)}`).or(B.min(3,{message:"Must be 3 or more characters long (should always have at least one digit - zero is '0x0')."})).refine(r=>r==="0x0"?!0:r[2]!=="0",{message:"Invalid hex quantity: leading zero digits are not allowed."}),N=B.refine(r=>r.length%2===0,{message:"Invalid hex-encoded data: must be even number of digits"}),Le=e.string().transform(r=>{let t=N.safeParse(r);return t.success?t.data:`0x${Ds.Buffer.from(r).toString("hex")}`}),fo=e.union([e.string(),e.number()]).transform((r,t)=>{if(typeof r=="number")return r;let s=r.startsWith("0x")?16:10,c=parseInt(r,s);return Number.isNaN(c)&&t.addIssue({code:e.ZodIssueCode.custom,message:"Could not parse as LenientInteger"}),c}),co=r=>N.refine(t=>t.length===r*2+2,{message:`Invalid byte length. (Expected ${r} bytes)`}),b=B.refine(Cs.isAddress,{message:"Invalid Ethereum address."}),E=co(32),uo=co(256),le=e.object({blockHash:E,address:b,logIndex:m,data:N,removed:e.boolean().optional(),topics:e.array(E),blockNumber:m.nullish().default(null),transactionIndex:m,transactionHash:E}),er=e.object({transactionHash:E,transactionIndex:m,blockHash:E,blockNumber:m,from:b,to:b.nullish().default(null).optional(),root:E.optional(),status:e.literal("0x1").or(e.literal("0x0")).optional(),cumulativeGasUsed:m,gasUsed:m,contractAddress:b.nullish().default(null),logs:e.array(le),logsBloom:uo,effectiveGasPrice:m.optional(),type:m.optional()}),rr=e.object({from:b.optional(),chainId:m.optional(),to:b.optional(),gas:m.optional(),gasPrice:m.optional(),value:m.optional(),data:N.optional(),nonce:m.optional()}),_e=e.object({name:e.string(),type:e.string()}),It=e.array(_e),ks=e.object({chainId:fo.optional(),name:e.string(),verifyingContract:b,version:e.string().optional()}),mo=e.object({type:e.string(),name:e.string(),value:e.string()}).array(),vt=e.object({domain:ks,message:e.record(e.any()),primaryType:e.string(),types:e.object({EIP712Domain:It}).and(e.record(_e.array()))}),lo=e.object({chainId:fo.optional(),name:e.string().optional(),verifyingContract:b.optional(),version:e.string().optional(),salt:e.string().optional()}),At=e.object({domain:lo,message:e.record(e.any()),primaryType:e.string(),types:e.object({EIP712Domain:It}).and(e.record(_e.array()))}),Bs=e.object({name:e.literal("owner"),type:e.literal("address")}),Ms=e.object({name:e.literal("spender"),type:e.literal("address")}),Us=e.object({name:e.literal("value"),type:e.literal("uint256")}),js=e.object({name:e.literal("value"),type:e.literal("uint256")}),Os=e.object({name:e.literal("deadline"),type:e.literal("uint256")}),Ls=e.tuple([_e,_e,_e,_e,_e]).refine(r=>{let t=[Bs,Ms,Us,js,Os],s=new Set(["owner","spender","value","nonce","deadline"]);for(let c of r)for(let y of t){let z=y.safeParse(c);z.success&&s.delete(z.data.name)}return s.size===0}).transform(()=>[{name:"owner",type:"address"},{name:"spender",type:"address"},{name:"value",type:"uint256"},{name:"nonce",type:"uint256"},{name:"deadline",type:"uint256"}]),Hs=e.literal("Permit"),Fs=e.object({EIP712Domain:It,Permit:Ls}),Ks=e.object({owner:b,spender:b,value:m,nonce:m,deadline:m}),Pd=e.object({domain:lo,primaryType:Hs,types:Fs,message:Ks}),yo=(c=>(c.legacy="0x0",c.eip2930="0x1",c.eip1559="0x2",c))(yo||{}),Vs=e.tuple([b,e.array(E)]),Te=e.object({chainId:m.optional(),data:N.optional(),from:b,gas:m.optional(),gasPrice:m.optional(),nonce:m.optional(),to:b.optional(),value:m.optional(),type:e.nativeEnum(yo).optional(),accessList:e.array(Vs).optional(),maxPriorityFeePerGas:m.optional(),maxFeePerGas:m.optional(),gasLimit:m.optional()}).transform(r=>(r.gas==null&&r.gasLimit!=null&&(r.gas=r.gasLimit,delete r.gasLimit),r)).brand("EthUnsignedTransactionObject"),ye=e.object({blockHash:E.nullish(),blockNumber:m.nullish(),from:b,gas:m,gasPrice:m.nullish(),hash:E,input:N,nonce:m,to:b.nullish().default(null),transactionIndex:m.nullish(),value:m,v:m,r:m,s:m}),xo=e.object({address:b.optional(),balance:m,codeHash:E,nonce:m,storageHash:E,accountProof:e.array(B),storageProof:e.array(e.object({key:m,value:m,proof:e.array(B)}))}),U=e.literal("latest").or(e.literal("earliest")).or(e.literal("pending")).or(e.literal("finalized")),be=e.object({number:m.nullish().default(null),hash:E.nullish().default(null),parentHash:E,nonce:co(8).nullish().default(null),sha3Uncles:E,logsBloom:uo.nullish().default(null),transactionsRoot:E,stateRoot:E,receiptsRoot:E,miner:b.nullish().default(null),mixHash:E.optional(),difficulty:m,totalDifficulty:m.nullish().default(null),extraData:N,size:m,gasLimit:m,gasUsed:m,timestamp:m,transactions:e.array(ye).or(e.array(E)),uncles:e.array(E),baseFeePerGas:m.optional()}),tr=e.enum(["CONTINUE_WITH_PHANTOM","CONTINUE_WITH_METAMASK","ALWAYS_USE_PHANTOM","ALWAYS_USE_METAMASK"]),or=e.string().refine(r=>{try{return io.default.decode(r).byteLength===32}catch{return!1}},{message:"String must be a valid solana public key of 32 bytes"}),Gs=e.union([e.literal("bip122_p2tr"),e.literal("bip122_p2wpkh"),e.literal("bip122_p2sh"),e.literal("bip122_p2pkh")]),Ws=e.object({address:e.string(),publicKey:e.string(),addressType:Gs}),we=Ws.and(e.object({purpose:e.union([e.literal("payment"),e.literal("ordinals")])})),Sd=e.string().refine(r=>{try{return io.default.decode(r).byteLength===64}catch{return!1}},{message:"String must be a valid solana address of 64 bytes"}),g=e.string().regex(/^[**********************************************************]*$/),v=e.object({url:e.string().url(),icon:e.string().nullish().default(null),tabId:e.number().optional()}),Qs=e.array(e.any()),Zs=e.unknown().transform((r,t)=>typeof r=="object"&&r!==null?r:(t.addIssue({code:e.ZodIssueCode.custom,message:"Not an object"}),e.NEVER)),He=e.union([e.null(),e.string(),e.number(),e.boolean(),Qs,Zs]),pr=e.literal("2.0"),Fe=e.union([e.string(),e.number(),e.null()]),Js=e.object({jsonrpc:e.literal("2.0"),id:Fe,method:e.string(),params:He.optional()}),qd=e.array(Js),Id=e.object({jsonrpc:e.literal("2.0"),method:e.string(),params:He}),ho=(D=>(D[D.ParseError=-32700]="ParseError",D[D.InternalError=-32603]="InternalError",D[D.InvalidParams=-32602]="InvalidParams",D[D.MethodNotFound=-32601]="MethodNotFound",D[D.InvalidRequest=-32600]="InvalidRequest",D[D.RequestCancelled=-32800]="RequestCancelled",D[D.TransactionRejected=-32003]="TransactionRejected",D[D.ResourceUnavailable=-32002]="ResourceUnavailable",D[D.InvalidInput=-32e3]="InvalidInput",D[D.UserRejectedRequest=4001]="UserRejectedRequest",D[D.Unauthorized=4100]="Unauthorized",D[D.UnsupportedMethod=4200]="UnsupportedMethod",D[D.RateLimited=4290]="RateLimited",D[D.Disconnected=4900]="Disconnected",D[D.ChainDisconnected=4901]="ChainDisconnected",D[D.ExecutionReverted=3]="ExecutionReverted",D))(ho||{});var a=e.object({code:e.nativeEnum(ho).or(e.number()),message:e.string()}),Xs=e.object({error:a}).or(e.object({result:He})),Ys=e.object({jsonrpc:e.literal("2.0"),id:Fe}).and(Xs),vd=e.array(Ys),Ne=e.object({domain:e.string().optional(),address:e.string().optional(),statement:e.string().optional(),uri:e.string().optional(),version:e.string().optional(),chainId:e.string().optional(),nonce:e.string().optional(),issuedAt:e.string().optional(),expirationTime:e.string().optional(),notBefore:e.string().optional(),requestId:e.string().optional(),resources:e.array(e.string()).optional()}),$s=e.literal("mainnet"),ea=e.literal("testnet"),Ad=e.literal("devnet"),Td=e.literal("localnet"),go=e.enum([$s.value,ea.value,Ad.value,Td.value]),wd=e.literal("1"),Nd=e.literal("11155111"),Tt=e.enum([wd.value,Nd.value]),Dd=e.literal("0x1"),Cd=e.literal("0xaa36a7"),wt=e.enum([Dd.value,Cd.value]),kd=e.literal("137"),Bd=e.literal("80002"),Nt=e.enum([kd.value,Bd.value]),Md=e.literal("0x89"),Ud=e.literal("0x13882"),Dt=e.enum([Md.value,Ud.value]),jd=e.literal("8453"),Od=e.literal("84532"),Ct=e.enum([jd.value,Od.value]),Ld=e.literal("0x2105"),Hd=e.literal("0x14a34"),kt=e.enum([Ld.value,Hd.value]),Fd=e.literal("mainnet-beta"),Kd=e.literal("testnet"),Vd=e.literal("devnet"),nr=g,Bt=g,Ke=g,zo=g,sr=g,Mt=e.string().url(),ar=e.string(),Ut=e.enum([Fd.value,Kd.value,Vd.value]),pe=e.object({dapp_encryption_public_key:nr,nonce:Ke,redirect_link:ar,payload:zo}),xe=e.object({nonce:Ke,data:sr}),Gd=e.object({name:e.string(),label:e.string().optional(),required:e.boolean().optional()}),Wd=e.object({message:e.string()}),ra=e.object({href:e.string(),label:e.string(),parameters:e.array(Gd).optional()}),ta=e.object({label:e.string(),url:e.string()}),Ve=e.object({successMessage:e.string().optional(),failureMessage:e.string().optional(),pendingMessage:e.string().optional(),onSuccessAction:ta.optional()}),Ro=e.object({domain:e.string(),name:e.string(),category:e.string(),actionUrl:e.string(),icon:e.string(),title:e.string(),description:e.string(),label:e.string(),disabled:e.boolean().optional(),links:e.object({actions:e.array(ra)}).optional(),error:Wd.optional(),postAction:Ve.optional()});var Hi={};l(Hi,{user_approveBtcRequestAccounts:()=>_o,user_approveBtcSignMessage:()=>Eo,user_approveBtcSignPSBT:()=>bo,user_approveEthRequestAccounts:()=>Po,user_approveEthSendTransaction:()=>qo,user_approveEthSignMessage:()=>Io,user_approveSolConnect:()=>vo,user_approveSolFeaturedAction:()=>Lo,user_approveSolPayTransaction:()=>Oo,user_approveSolSignAllTransactions:()=>Ao,user_approveSolSignAndSendAllTransactions:()=>wo,user_approveSolSignAndSendTransaction:()=>To,user_approveSolSignIn:()=>No,user_approveSolSignMessage:()=>Do,user_approveSolSignTransaction:()=>Co,user_approveWalletRequestPermissions:()=>So,user_confirmEIP712IncorrectChainId:()=>ko,user_confirmIncorrectMode:()=>Bo,user_confirmUnsupportedAccount:()=>Mo,user_confirmUnsupportedNetwork:()=>Uo,user_selectEthWallet:()=>jo,user_solTransactionConfirmation:()=>Ho});p();n();var _o={};l(_o,{error:()=>ua,method:()=>ia,params:()=>fa,request:()=>eh,response:()=>rh,result:()=>ca});p();n();p();n();function i(r,t){return e.object({jsonrpc:pr,id:Fe,method:r,params:t})}function f(r,t){return e.object({jsonrpc:pr,id:Fe}).and(e.object({result:r}).or(e.object({error:t})))}function ee(r,t){return e.object({jsonrpc:pr,method:r,params:t})}function oa(r){return Tt.safeParse(r).success}function pa(r){return wt.safeParse(r).success}function na(r){return Nt.safeParse(r).success}function sa(r){return Dt.safeParse(r).success}function Qd(r){return Ct.safeParse(r).success}function Zd(r){return kt.safeParse(r).success}function aa(r){return go.safeParse(r).success}function Jd(r){switch(r){case"101":return"mainnet";case"102":return"testnet";case"103":return"devnet"}return null}function Xd(r){let t=typeof r=="string"?r.replace(/^(eip155:|solana:)/gm,""):`${r}`;return aa(t)?{chainType:"solana",chainName:"solana",networkId:t}:oa(t)?{chainType:"eip155",chainName:"ethereum",networkId:t}:pa(t)?{chainType:"eip155",chainName:"ethereum",networkId:parseInt(t.substring(2),16).toString()}:na(t)?{chainType:"eip155",chainName:"polygon",networkId:t}:sa(t)?{chainType:"eip155",chainName:"polygon",networkId:parseInt(t.substring(2),16).toString()}:Qd(t)?{chainType:"eip155",chainName:"base",networkId:t}:Zd(t)?{chainType:"eip155",chainName:"base",networkId:parseInt(t.substring(2),16).toString()}:null}function Yd(r){return Tt.or(Nt).or(Ct).parse(parseInt(r.substring(2),16).toString())}function $d(r){return wt.or(Dt).or(kt).parse("0x"+parseInt(r,10).toString(16))}var ia=e.literal("user_approveBtcRequestAccounts"),fa=e.tuple([v]),ca=e.null(),ua=a,eh=i(ia,fa),rh=f(ca,ua);var bo={};l(bo,{error:()=>xa,method:()=>ma,params:()=>la,request:()=>th,response:()=>oh,result:()=>ya});p();n();var ma=e.literal("user_approveBtcSignPSBT"),la=e.tuple([v,e.object({psbt:e.instanceof(Uint8Array),inputsToSign:e.array(e.object({address:e.string(),signingIndexes:e.array(e.number()),sigHash:e.number().optional()})),finalize:e.boolean()})]),ya=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend")}),e.object({type:e.literal("send"),signature:e.instanceof(Uint8Array)})]),xa=a,th=i(ma,la),oh=f(ya,xa);var Eo={};l(Eo,{error:()=>za,method:()=>da,params:()=>ha,request:()=>ph,response:()=>nh,result:()=>ga});p();n();var da=e.literal("user_approveBtcSignMessage"),ha=e.tuple([v,e.object({message:e.instanceof(Uint8Array)})]),ga=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend")}),e.object({type:e.literal("send"),signature:e.instanceof(Uint8Array),signedMessage:e.instanceof(Uint8Array)})]),za=a,ph=i(da,ha),nh=f(ga,za);var Po={};l(Po,{error:()=>Ea,method:()=>Ra,params:()=>_a,request:()=>sh,response:()=>ah,result:()=>ba});p();n();var Ra=e.literal("user_approveEthRequestAccounts"),_a=e.tuple([v]),ba=e.null(),Ea=a,sh=i(Ra,_a),ah=f(ba,Ea);var So={};l(So,{error:()=>Ia,method:()=>Pa,params:()=>Sa,request:()=>ih,response:()=>fh,result:()=>qa});p();n();var Pa=e.literal("user_approveWalletRequestPermissions"),Sa=e.tuple([v]),qa=e.null(),Ia=a,ih=i(Pa,Sa),fh=f(qa,Ia);var qo={};l(qo,{error:()=>wa,method:()=>va,params:()=>Aa,request:()=>ch,response:()=>uh,result:()=>Ta});p();n();p();n();var Q=(j=>(j.OK="OK",j.FeatureKilled="FEATURE_KILLED",j.WalletLocked="WALLET_LOCKED",j.TabNotFocused="TAB_NOT_FOCUSED",j.Disabled="DISABLED",j.SessionExpired="SESSION_EXPIRED",j.RateLimitExceeded="RATE_LIMIT_EXCEEDED",j.SimulationFailed="SIMULATION_FAILED",j.UnsupportedDapp="UNSUPPORTED_DAPP",j.UnsupportedNetworkId="UNSUPPORTED_NETWORK_ID",j.UnsupportedMethod="UNSUPPORTED_METHOD",j.Unimplemented="UNIMPLEMENTED",j.Unknown="UNKNOWN",j))(Q||{});var va=e.literal("user_approveEthSendTransaction"),Aa=e.tuple([v,e.object({transaction:Te,autoConfirmStatusCode:e.nativeEnum(Q)})]),Ta=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend"),maxFeePerGas:m,maxPriorityFeePerGas:m}),e.object({type:e.literal("send"),signature:B,maxFeePerGas:m,maxPriorityFeePerGas:m})]),wa=a,ch=i(va,Aa),uh=f(Ta,wa);var Io={};l(Io,{error:()=>ka,method:()=>Na,params:()=>Da,request:()=>mh,response:()=>lh,result:()=>Ca});p();n();var Na=e.literal("user_approveEthSignMessage"),Da=e.tuple([v,e.object({signer:b,message:N,originalMethod:e.enum(["eth_sign","personal_sign","eth_signTypedData","eth_signTypedData_v3","eth_signTypedData_v4"]),chainId:e.string(),autoConfirmStatusCode:e.nativeEnum(Q)})]),Ca=e.discriminatedUnion("approvalType",[e.object({approvalType:e.literal("user")}),e.object({approvalType:e.literal("hardware"),signature:B})]),ka=a,mh=i(Na,Da),lh=f(Ca,ka);var vo={};l(vo,{error:()=>ja,method:()=>Ba,params:()=>Ma,request:()=>yh,response:()=>xh,result:()=>Ua});p();n();var Ba=e.literal("user_approveSolConnect"),Ma=e.tuple([v]),Ua=e.null(),ja=a,yh=i(Ba,Ma),xh=f(Ua,ja);var Ao={};l(Ao,{error:()=>Fa,method:()=>Oa,params:()=>La,request:()=>dh,response:()=>hh,result:()=>Ha});p();n();var Oa=e.literal("user_approveSolSignAllTransactions"),La=e.tuple([v,e.object({transactions:e.array(g),autoConfirmStatusCode:e.nativeEnum(Q)})]),Ha=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend"),overwriteTransactions:e.array(g).optional()}),e.object({type:e.literal("send"),result:e.array(e.object({signedTransaction:g,signature:g,version:e.union([e.literal("legacy"),e.number()])}))})]),Fa=a,dh=i(Oa,La),hh=f(Ha,Fa);var To={};l(To,{error:()=>Wa,method:()=>Ka,params:()=>Va,request:()=>gh,response:()=>zh,result:()=>Ga});p();n();var Ka=e.literal("user_approveSolSignAndSendTransaction"),Va=e.tuple([v,e.object({transaction:e.string(),autoConfirmStatusCode:e.nativeEnum(Q)})]),Ga=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend"),overwriteTransactions:e.array(g).optional()}),e.object({type:e.literal("send"),signedTransaction:g,signature:g,version:e.union([e.literal("legacy"),e.number()])})]),Wa=a,gh=i(Ka,Va),zh=f(Ga,Wa);var wo={};l(wo,{error:()=>Xa,method:()=>Qa,params:()=>Za,request:()=>Rh,response:()=>_h,result:()=>Ja});p();n();var Qa=e.literal("user_approveSolSignAndSendAllTransactions"),Za=e.tuple([v,e.object({transactions:e.array(g),autoConfirmStatusCode:e.nativeEnum(Q)})]),Ja=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend"),overwriteTransactions:e.array(g).optional()}),e.object({type:e.literal("send"),result:e.array(e.object({signedTransaction:g,signature:g,version:e.union([e.literal("legacy"),e.number()])}))})]),Xa=a,Rh=i(Qa,Za),_h=f(Ja,Xa);var No={};l(No,{error:()=>ri,method:()=>Ya,params:()=>$a,request:()=>bh,response:()=>Eh,result:()=>ei});p();n();var Ya=e.literal("user_approveSolSignIn"),$a=e.tuple([v,e.object({connect:e.boolean(),signInData:Ne,message:g,errorDetails:e.array(e.object({label:e.string(),message:e.string()})).optional()})]),ei=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend")}),e.object({type:e.literal("send"),signature:g})]),ri=a,bh=i(Ya,$a),Eh=f(ei,ri);var Do={};l(Do,{error:()=>ni,method:()=>ti,params:()=>oi,request:()=>Ph,response:()=>Sh,result:()=>pi});p();n();var ti=e.literal("user_approveSolSignMessage"),oi=e.tuple([v,e.object({message:g,display:e.union([e.literal("utf8"),e.literal("hex")]),autoConfirmStatusCode:e.nativeEnum(Q)})]),pi=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend")}),e.object({type:e.literal("send"),signature:g})]),ni=a,Ph=i(ti,oi),Sh=f(pi,ni);var Co={};l(Co,{error:()=>fi,method:()=>si,params:()=>ai,request:()=>qh,response:()=>Ih,result:()=>ii});p();n();var si=e.literal("user_approveSolSignTransaction"),ai=e.tuple([v,e.object({transaction:e.string(),autoConfirmStatusCode:e.nativeEnum(Q)})]),ii=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend"),overwriteTransactions:e.array(g).optional()}),e.object({type:e.literal("send"),signedTransaction:g,signature:g,version:e.union([e.literal("legacy"),e.number()])})]),fi=a,qh=i(si,ai),Ih=f(ii,fi);var ko={};l(ko,{error:()=>li,method:()=>ci,params:()=>ui,request:()=>vh,response:()=>Ah,result:()=>mi});p();n();var ci=e.literal("user_confirmEIP712IncorrectChainId"),ui=e.tuple([v,e.object({connectedChainId:e.string(),messageChainId:e.string()})]),mi=e.null(),li=a,vh=i(ci,ui),Ah=f(mi,li);var Bo={};l(Bo,{error:()=>hi,method:()=>yi,params:()=>xi,request:()=>Th,response:()=>wh,result:()=>di});p();n();var yi=e.literal("user_confirmIncorrectMode"),xi=e.tuple([v,e.enum(["mainnet","testnet"])]),di=e.null(),hi=a,Th=i(yi,xi),wh=f(di,hi);var Mo={};l(Mo,{error:()=>_i,method:()=>gi,params:()=>zi,request:()=>Nh,response:()=>Dh,result:()=>Ri});p();n();var gi=e.literal("user_confirmUnsupportedAccount"),zi=e.tuple([v,e.literal("ethereum").or(e.literal("solana"))]),Ri=e.null(),_i=a,Nh=i(gi,zi),Dh=f(Ri,_i);var Uo={};l(Uo,{error:()=>Si,method:()=>bi,params:()=>Ei,request:()=>Ch,response:()=>kh,result:()=>Pi});p();n();var bi=e.literal("user_confirmUnsupportedNetwork"),Ei=e.tuple([v,e.string()]),Pi=e.null(),Si=a,Ch=i(bi,Ei),kh=f(Pi,Si);var jo={};l(jo,{error:()=>Ai,method:()=>qi,params:()=>Ii,request:()=>Bh,response:()=>Mh,result:()=>vi});p();n();var qi=e.literal("user_selectEthWallet"),Ii=e.tuple([v]),vi=tr,Ai=a,Bh=i(qi,Ii),Mh=f(vi,Ai);var Oo={};l(Oo,{error:()=>Di,method:()=>Ti,params:()=>wi,request:()=>Uh,response:()=>jh,result:()=>Ni});p();n();var Ti=e.literal("user_approveSolPayTransaction"),wi=e.tuple([v,e.object({label:e.string().optional(),transaction:e.string()})]),Ni=e.discriminatedUnion("type",[e.object({type:e.literal("signAndSend")}),e.object({type:e.literal("send"),signedTransaction:g,signature:g,version:e.union([e.literal("legacy"),e.number()])})]),Di=a,Uh=i(Ti,wi),jh=f(Ni,Di);var Lo={};l(Lo,{error:()=>Mi,method:()=>Ci,params:()=>ki,request:()=>Oh,response:()=>Lh,result:()=>Bi});p();n();var Ci=e.literal("user_approveSolFeaturedAction"),ki=e.tuple([v,e.object({featuredTransaction:Ro})]),Bi=e.object({transaction:e.string().optional(),message:e.string().optional()}),Mi=a,Oh=i(Ci,ki),Lh=f(Bi,Mi);var Ho={};l(Ho,{error:()=>Li,method:()=>Ui,params:()=>ji,request:()=>Hh,response:()=>Fh,result:()=>Oi});p();n();var Ui=e.literal("user_solTransactionConfirmation"),ji=e.tuple([v,e.object({signature:g,postAction:Ve.optional()})]),Oi=e.null(),Li=a,Hh=i(Ui,ji),Fh=f(Oi,Li);p();n();var rf={};l(rf,{btc_requestAccounts:()=>Fo,btc_signMessage:()=>Vo,btc_signPSBT:()=>Ko});p();n();var Fo={};l(Fo,{error:()=>Gi,method:()=>Fi,params:()=>Ki,request:()=>Kh,response:()=>Vh,result:()=>Vi});p();n();var Fi=e.literal("btc_requestAccounts"),Ki=e.tuple([]),Vi=e.array(we),Gi=a,Kh=i(Fi,Ki),Vh=f(Vi,Gi);var Ko={};l(Ko,{error:()=>Ji,method:()=>Wi,params:()=>Qi,request:()=>Gh,response:()=>Wh,result:()=>Zi});p();n();var Wi=e.literal("btc_signPSBT"),Qi=e.tuple([e.instanceof(Uint8Array),e.object({inputsToSign:e.array(e.object({address:e.string(),signingIndexes:e.array(e.number()),sigHash:e.number().optional()})),finalize:e.boolean()})]),Zi=e.instanceof(Uint8Array),Ji=a,Gh=i(Wi,Qi),Wh=f(Zi,Ji);var Vo={};l(Vo,{error:()=>ef,method:()=>Xi,params:()=>Yi,request:()=>Qh,response:()=>Zh,result:()=>$i});p();n();var Xi=e.literal("btc_signMessage"),Yi=e.object({address:e.string(),message:e.instanceof(Uint8Array)}),$i=e.object({signature:e.instanceof(Uint8Array),signedMessage:e.instanceof(Uint8Array)}),ef=a,Qh=i(Xi,Yi),Zh=f($i,ef);var zl={};l(zl,{eth_accounts:()=>Go,eth_blockNumber:()=>Wo,eth_call:()=>Qo,eth_cancelPrivateTransaction:()=>Zo,eth_chainId:()=>Jo,eth_estimateGas:()=>Xo,eth_feeHistory:()=>Yo,eth_gasPrice:()=>$o,eth_getBalance:()=>ep,eth_getBlockByHash:()=>rp,eth_getBlockByNumber:()=>tp,eth_getBlockReceipts:()=>op,eth_getBlockTransactionCountByHash:()=>pp,eth_getBlockTransactionCountByNumber:()=>np,eth_getCode:()=>sp,eth_getFilterChanges:()=>ap,eth_getFilterLogs:()=>ip,eth_getLogs:()=>fp,eth_getProof:()=>cp,eth_getStorageAt:()=>up,eth_getTransactionByBlockHashAndIndex:()=>mp,eth_getTransactionByBlockNumberAndIndex:()=>lp,eth_getTransactionByHash:()=>yp,eth_getTransactionCount:()=>xp,eth_getTransactionReceipt:()=>dp,eth_getUncleByBlockHashAndIndex:()=>hp,eth_getUncleByBlockNumberAndIndex:()=>gp,eth_getUncleCountByBlockHash:()=>zp,eth_getUncleCountByBlockNumber:()=>Rp,eth_maxPriorityFeePerGas:()=>_p,eth_newBlockFilter:()=>bp,eth_newFilter:()=>Ep,eth_newPendingTransactionFilter:()=>Pp,eth_protocolVersion:()=>qp,eth_requestAccounts:()=>Ip,eth_sendPrivateTransaction:()=>vp,eth_sendRawTransaction:()=>Ap,eth_sendTransaction:()=>Tp,eth_sign:()=>wp,eth_signTransaction:()=>Np,eth_signTypedData:()=>Dp,eth_signTypedData_v3:()=>Cp,eth_signTypedData_v4:()=>kp,eth_subscribe:()=>Bp,eth_syncing:()=>Mp,eth_uninstallFilter:()=>Up,eth_unsubscribe:()=>jp,net_listening:()=>Op,net_version:()=>Lp,personal_sign:()=>Sp,wallet_addEthereumChain:()=>Hp,wallet_getPermissions:()=>Zp,wallet_requestPermissions:()=>Qp,wallet_selectEthereumProvider:()=>Fp,wallet_switchEthereumChain:()=>Kp,wallet_watchAsset:()=>Vp,web3_clientVersion:()=>Gp,web3_sha3:()=>Wp});p();n();var Go={};l(Go,{error:()=>nf,method:()=>tf,params:()=>of,request:()=>Jh,response:()=>Xh,result:()=>pf});p();n();var tf=e.literal("eth_accounts"),of=e.tuple([]),pf=e.array(b),nf=a,Jh=i(tf,of),Xh=f(pf,nf);var Wo={};l(Wo,{error:()=>cf,method:()=>sf,params:()=>af,request:()=>Yh,response:()=>$h,result:()=>ff});p();n();var sf=e.literal("eth_blockNumber"),af=e.tuple([]),ff=m,cf=a,Yh=i(sf,af),$h=f(ff,cf);var Qo={};l(Qo,{error:()=>yf,method:()=>uf,params:()=>mf,request:()=>eg,response:()=>rg,result:()=>lf});p();n();var uf=e.literal("eth_call"),mf=e.tuple([rr,m.or(U)]),lf=N,yf=a,eg=i(uf,mf),rg=f(lf,yf);var Zo={};l(Zo,{error:()=>gf,method:()=>xf,params:()=>df,request:()=>tg,response:()=>og,result:()=>hf});p();n();var xf=e.literal("eth_cancelPrivateTransaction"),df=e.tuple([e.object({txHash:E})]),hf=e.boolean(),gf=a,tg=i(xf,df),og=f(hf,gf);var Jo={};l(Jo,{error:()=>bf,method:()=>zf,params:()=>Rf,request:()=>pg,response:()=>ng,result:()=>_f});p();n();var zf=e.literal("eth_chainId"),Rf=e.tuple([]),_f=B,bf=a,pg=i(zf,Rf),ng=f(_f,bf);var Xo={};l(Xo,{error:()=>qf,method:()=>Ef,params:()=>Pf,request:()=>sg,response:()=>ag,result:()=>Sf});p();n();var Ef=e.literal("eth_estimateGas"),Pf=e.tuple([rr]),Sf=m,qf=a,sg=i(Ef,Pf),ag=f(Sf,qf);var Yo={};l(Yo,{error:()=>Tf,method:()=>If,params:()=>vf,request:()=>ig,response:()=>fg,result:()=>Af});p();n();var If=e.literal("eth_feeHistory"),vf=e.tuple([e.number(),m.or(U),e.array(e.number()).optional()]),Af=e.object({oldestBlock:e.number(),reward:e.array(e.tuple([m,m])).optional(),baseFeePerGas:e.array(m),gasUsedRatio:e.array(e.number())}),Tf=a,ig=i(If,vf),fg=f(Af,Tf);var $o={};l($o,{error:()=>Cf,method:()=>wf,params:()=>Nf,request:()=>cg,response:()=>ug,result:()=>Df});p();n();var wf=e.literal("eth_gasPrice"),Nf=e.tuple([]),Df=m,Cf=a,cg=i(wf,Nf),ug=f(Df,Cf);var ep={};l(ep,{error:()=>Uf,method:()=>kf,params:()=>Bf,request:()=>mg,response:()=>lg,result:()=>Mf});p();n();var kf=e.literal("eth_getBalance"),Bf=e.tuple([b,m.or(U)]),Mf=m,Uf=a,mg=i(kf,Bf),lg=f(Mf,Uf);var rp={};l(rp,{error:()=>Hf,method:()=>jf,params:()=>Of,request:()=>yg,response:()=>xg,result:()=>Lf});p();n();var jf=e.literal("eth_getBlockByHash"),Of=e.tuple([E,e.boolean()]),Lf=be,Hf=a,yg=i(jf,Of),xg=f(Lf,Hf);var tp={};l(tp,{error:()=>Gf,method:()=>Ff,params:()=>Kf,request:()=>dg,response:()=>hg,result:()=>Vf});p();n();var Ff=e.literal("eth_getBlockByNumber"),Kf=e.tuple([m.or(U),e.boolean()]),Vf=be,Gf=a,dg=i(Ff,Kf),hg=f(Vf,Gf);var op={};l(op,{error:()=>Jf,method:()=>Wf,params:()=>Qf,request:()=>gg,response:()=>zg,result:()=>Zf});p();n();var Wf=e.literal("eth_getBlockReceipts"),Qf=e.tuple([E.or(m).or(U)]),Zf=e.array(er),Jf=a,gg=i(Wf,Qf),zg=f(Zf,Jf);var pp={};l(pp,{error:()=>ec,method:()=>Xf,params:()=>Yf,request:()=>Rg,response:()=>_g,result:()=>$f});p();n();var Xf=e.literal("eth_getBlockTransactionCountByHash"),Yf=e.tuple([E]),$f=m,ec=a,Rg=i(Xf,Yf),_g=f($f,ec);var np={};l(np,{error:()=>pc,method:()=>rc,params:()=>tc,request:()=>bg,response:()=>Eg,result:()=>oc});p();n();var rc=e.literal("eth_getBlockTransactionCountByNumber"),tc=e.tuple([m]),oc=m,pc=a,bg=i(rc,tc),Eg=f(oc,pc);var sp={};l(sp,{error:()=>ic,method:()=>nc,params:()=>sc,request:()=>Pg,response:()=>Sg,result:()=>ac});p();n();var nc=e.literal("eth_getCode"),sc=e.tuple([b,e.union([m,U])]),ac=N,ic=a,Pg=i(nc,sc),Sg=f(ac,ic);var ap={};l(ap,{error:()=>mc,method:()=>fc,params:()=>cc,request:()=>qg,response:()=>Ig,result:()=>uc});p();n();var fc=e.literal("eth_getFilterChanges"),cc=e.tuple([m]),uc=e.array(le),mc=a,qg=i(fc,cc),Ig=f(uc,mc);var ip={};l(ip,{error:()=>dc,method:()=>lc,params:()=>yc,request:()=>vg,response:()=>Ag,result:()=>xc});p();n();var lc=e.literal("eth_getFilterLogs"),yc=e.tuple([m]),xc=e.array(le),dc=a,vg=i(lc,yc),Ag=f(xc,dc);var fp={};l(fp,{error:()=>Rc,method:()=>hc,params:()=>gc,request:()=>Tg,response:()=>wg,result:()=>zc});p();n();var hc=e.literal("eth_getLogs"),gc=e.tuple([e.object({fromBlock:m.or(U).optional(),toBlock:e.string().optional(),address:b.optional(),topics:e.array(E).optional(),blockHash:E.optional()})]),zc=e.array(le),Rc=a,Tg=i(hc,gc),wg=f(zc,Rc);var cp={};l(cp,{error:()=>Pc,method:()=>_c,params:()=>bc,request:()=>Ng,response:()=>Dg,result:()=>Ec});p();n();var _c=e.literal("eth_getProof"),bc=e.tuple([b,e.array(E),m.or(U)]),Ec=xo,Pc=a,Ng=i(_c,bc),Dg=f(Ec,Pc);var up={};l(up,{error:()=>vc,method:()=>Sc,params:()=>qc,request:()=>Cg,response:()=>kg,result:()=>Ic});p();n();var Sc=e.literal("eth_getStorageAt"),qc=e.tuple([b,m,m.or(U)]),Ic=N,vc=a,Cg=i(Sc,qc),kg=f(Ic,vc);var mp={};l(mp,{error:()=>Nc,method:()=>Ac,params:()=>Tc,request:()=>Bg,response:()=>Mg,result:()=>wc});p();n();var Ac=e.literal("eth_getTransactionByBlockHashAndIndex"),Tc=e.tuple([E,m]),wc=ye.nullish().default(null),Nc=a,Bg=i(Ac,Tc),Mg=f(wc,Nc);var lp={};l(lp,{error:()=>Bc,method:()=>Dc,params:()=>Cc,request:()=>Ug,response:()=>jg,result:()=>kc});p();n();var Dc=e.literal("eth_getTransactionByBlockNumberAndIndex"),Cc=e.tuple([e.string(),m]),kc=ye,Bc=a,Ug=i(Dc,Cc),jg=f(kc,Bc);var yp={};l(yp,{error:()=>Oc,method:()=>Mc,params:()=>Uc,request:()=>Og,response:()=>Lg,result:()=>jc});p();n();var Mc=e.literal("eth_getTransactionByHash"),Uc=e.tuple([E]),jc=ye.nullable(),Oc=a,Og=i(Mc,Uc),Lg=f(jc,Oc);var xp={};l(xp,{error:()=>Kc,method:()=>Lc,params:()=>Hc,request:()=>Hg,response:()=>Fg,result:()=>Fc});p();n();var Lc=e.literal("eth_getTransactionCount"),Hc=e.tuple([b,m.or(U)]),Fc=m,Kc=a,Hg=i(Lc,Hc),Fg=f(Fc,Kc);var dp={};l(dp,{error:()=>Qc,method:()=>Vc,params:()=>Gc,request:()=>Kg,response:()=>Vg,result:()=>Wc});p();n();var Vc=e.literal("eth_getTransactionReceipt"),Gc=e.tuple([E]),Wc=er.nullish().default(null),Qc=a,Kg=i(Vc,Gc),Vg=f(Wc,Qc);var hp={};l(hp,{error:()=>Yc,method:()=>Zc,params:()=>Jc,request:()=>Gg,response:()=>Wg,result:()=>Xc});p();n();var Zc=e.literal("eth_getUncleByBlockHashAndIndex"),Jc=e.tuple([m.or(U),m]),Xc=be,Yc=a,Gg=i(Zc,Jc),Wg=f(Xc,Yc);var gp={};l(gp,{error:()=>tu,method:()=>$c,params:()=>eu,request:()=>Qg,response:()=>Zg,result:()=>ru});p();n();var $c=e.literal("eth_getUncleByBlockNumberAndIndex"),eu=e.tuple([m.or(U),m]),ru=be,tu=a,Qg=i($c,eu),Zg=f(ru,tu);var zp={};l(zp,{error:()=>su,method:()=>ou,params:()=>pu,request:()=>Jg,response:()=>Xg,result:()=>nu});p();n();var ou=e.literal("eth_getUncleCountByBlockHash"),pu=e.tuple([E]),nu=m,su=a,Jg=i(ou,pu),Xg=f(nu,su);var Rp={};l(Rp,{error:()=>cu,method:()=>au,params:()=>iu,request:()=>Yg,response:()=>$g,result:()=>fu});p();n();var au=e.literal("eth_getUncleCountByBlockNumber"),iu=e.tuple([m.or(U)]),fu=m,cu=a,Yg=i(au,iu),$g=f(fu,cu);var _p={};l(_p,{error:()=>yu,method:()=>uu,params:()=>mu,request:()=>ez,response:()=>rz,result:()=>lu});p();n();var uu=e.literal("eth_maxPriorityFeePerGas"),mu=e.tuple([]),lu=m,yu=a,ez=i(uu,mu),rz=f(lu,yu);var bp={};l(bp,{error:()=>gu,method:()=>xu,params:()=>du,request:()=>tz,response:()=>oz,result:()=>hu});p();n();var xu=e.literal("eth_newBlockFilter"),du=e.tuple([]),hu=m,gu=a,tz=i(xu,du),oz=f(hu,gu);var Ep={};l(Ep,{error:()=>bu,method:()=>zu,params:()=>Ru,request:()=>pz,response:()=>nz,result:()=>_u});p();n();var zu=e.literal("eth_newFilter"),Ru=e.tuple([e.object({fromBlock:m.optional(),toBlock:m.optional(),address:b.or(e.array(b)).optional(),topics:e.array(N.nullish().default(null).or(e.array(N.nullish().default(null)))).optional()})]),_u=m,bu=a,pz=i(zu,Ru),nz=f(_u,bu);var Pp={};l(Pp,{error:()=>qu,method:()=>Eu,params:()=>Pu,request:()=>sz,response:()=>az,result:()=>Su});p();n();var Eu=e.literal("eth_newPendingTransactionFilter"),Pu=e.tuple([]),Su=m,qu=a,sz=i(Eu,Pu),az=f(Su,qu);var Sp={};l(Sp,{error:()=>Tu,method:()=>Iu,params:()=>vu,request:()=>iz,response:()=>fz,result:()=>Au});p();n();var Iu=e.literal("personal_sign"),vu=e.union([e.tuple([Le,b]),e.tuple([Le,b,e.unknown()])]),Au=N,Tu=a,iz=i(Iu,vu),fz=f(Au,Tu);var qp={};l(qp,{error:()=>Cu,method:()=>wu,params:()=>Nu,request:()=>cz,response:()=>uz,result:()=>Du});p();n();var wu=e.literal("eth_protocolVersion"),Nu=e.tuple([]),Du=e.string(),Cu=a,cz=i(wu,Nu),uz=f(Du,Cu);var Ip={};l(Ip,{error:()=>Uu,method:()=>ku,params:()=>Bu,request:()=>mz,response:()=>lz,result:()=>Mu});p();n();var ku=e.literal("eth_requestAccounts"),Bu=e.tuple([]),Mu=e.array(b),Uu=a,mz=i(ku,Bu),lz=f(Mu,Uu);var vp={};l(vp,{error:()=>Hu,method:()=>ju,params:()=>Ou,request:()=>yz,response:()=>xz,result:()=>Lu});p();n();var ju=e.literal("eth_sendPrivateTransaction"),Ou=e.tuple([e.object({tx:E,maxBlockNumber:m.optional(),preferences:e.object({fast:e.boolean()}).optional()})]),Lu=E,Hu=a,yz=i(ju,Ou),xz=f(Lu,Hu);var Ap={};l(Ap,{error:()=>Gu,method:()=>Fu,params:()=>Ku,request:()=>dz,response:()=>hz,result:()=>Vu});p();n();var Fu=e.literal("eth_sendRawTransaction"),Ku=e.tuple([N]),Vu=E,Gu=a,dz=i(Fu,Ku),hz=f(Vu,Gu);var Tp={};l(Tp,{error:()=>Ju,method:()=>Wu,params:()=>Qu,request:()=>gz,response:()=>zz,result:()=>Zu});p();n();var Wu=e.literal("eth_sendTransaction"),Qu=e.tuple([Te]),Zu=E,Ju=a,gz=i(Wu,Qu),zz=f(Zu,Ju);var wp={};l(wp,{error:()=>em,method:()=>Xu,params:()=>Yu,request:()=>Rz,response:()=>_z,result:()=>$u});p();n();var Xu=e.literal("eth_sign"),Yu=e.tuple([b,Le]),$u=N,em=a,Rz=i(Xu,Yu),_z=f($u,em);var Np={};l(Np,{error:()=>pm,method:()=>rm,params:()=>tm,request:()=>bz,response:()=>Ez,result:()=>om});p();n();var rm=e.literal("eth_signTransaction"),tm=e.tuple([Te]),om=N,pm=a,bz=i(rm,tm),Ez=f(om,pm);var Dp={};l(Dp,{error:()=>im,method:()=>nm,params:()=>sm,request:()=>Pz,response:()=>Sz,result:()=>am});p();n();var nm=e.literal("eth_signTypedData"),sm=e.tuple([mo,b]),am=N,im=a,Pz=i(nm,sm),Sz=f(am,im);var Cp={};l(Cp,{error:()=>mm,method:()=>fm,params:()=>cm,request:()=>qz,response:()=>Iz,result:()=>um});p();n();var fm=e.literal("eth_signTypedData_v3"),cm=e.tuple([b,e.string().transform((r,t)=>{try{let s=JSON.parse(r);return vt.parse(s)}catch(s){return t.addIssue({code:e.ZodIssueCode.custom,message:"Invalid typed data:"+s.message,fatal:!0}),e.NEVER}}).or(vt)]),um=N,mm=a,qz=i(fm,cm),Iz=f(um,mm);var kp={};l(kp,{error:()=>dm,method:()=>lm,params:()=>ym,request:()=>vz,response:()=>Az,result:()=>xm});p();n();var lm=e.literal("eth_signTypedData_v4"),ym=e.tuple([b,e.string().transform((r,t)=>{try{let s=JSON.parse(r);return At.parse(s)}catch(s){return t.addIssue({code:e.ZodIssueCode.custom,message:"Invalid typed data:"+s.message,fatal:!0}),e.NEVER}}).or(At)]),xm=N,dm=a,vz=i(lm,ym),Az=f(xm,dm);var Bp={};l(Bp,{error:()=>Rm,method:()=>hm,params:()=>gm,request:()=>Tz,response:()=>wz,result:()=>zm});p();n();var hm=e.literal("eth_subscribe"),gm=e.any(),zm=e.union([B,e.object({result:ye,subscription:B}),e.object({result:E,subscription:B}),e.object({result:e.object({difficulty:B,extraData:B,gasLimit:B,gasUsed:B,logsBloom:B,miner:b,nonce:B,number:B,parentHash:E,receiptRoot:E,sha3Uncles:E,stateRoot:E,timestamp:B,transactionsRoot:E}),subscription:B}),e.object({result:le,subscription:B})]),Rm=a,Tz=i(hm,gm),wz=f(zm,Rm);var Mp={};l(Mp,{error:()=>Pm,method:()=>_m,params:()=>bm,request:()=>Nz,response:()=>Dz,result:()=>Em});p();n();var _m=e.literal("eth_syncing"),bm=e.tuple([]),Em=e.union([e.object({currentBlock:m,highestBlock:m,startingBlock:m}),e.literal(!1)]),Pm=a,Nz=i(_m,bm),Dz=f(Em,Pm);var Up={};l(Up,{error:()=>vm,method:()=>Sm,params:()=>qm,request:()=>Cz,response:()=>kz,result:()=>Im});p();n();var Sm=e.literal("eth_uninstallFilter"),qm=e.tuple([m]),Im=e.boolean(),vm=a,Cz=i(Sm,qm),kz=f(Im,vm);var jp={};l(jp,{error:()=>Nm,method:()=>Am,params:()=>Tm,request:()=>Bz,response:()=>Mz,result:()=>wm});p();n();var Am=e.literal("eth_unsubscribe"),Tm=e.any(),wm=e.boolean(),Nm=a,Bz=i(Am,Tm),Mz=f(wm,Nm);var Op={};l(Op,{error:()=>Bm,method:()=>Dm,params:()=>Cm,request:()=>Uz,response:()=>jz,result:()=>km});p();n();var Dm=e.literal("net_listening"),Cm=e.tuple([]),km=e.boolean(),Bm=a,Uz=i(Dm,Cm),jz=f(km,Bm);var Lp={};l(Lp,{error:()=>Om,method:()=>Mm,params:()=>Um,request:()=>Oz,response:()=>Lz,result:()=>jm});p();n();var Mm=e.literal("net_version"),Um=e.tuple([]),jm=e.string(),Om=a,Oz=i(Mm,Um),Lz=f(jm,Om);var Hp={};l(Hp,{error:()=>Km,method:()=>Lm,params:()=>Hm,request:()=>Hz,response:()=>Fz,result:()=>Fm});p();n();var Lm=e.literal("wallet_addEthereumChain"),Hm=e.tuple([e.object({chainId:B,chainName:e.string(),nativeCurrency:e.object({name:e.string(),symbol:e.string().refine(r=>{let{length:t}=r;return t>=2&&t<=6},{message:"Value is not a valid symbol."}),decimals:e.number()}),rpcUrls:e.array(e.string()),blockExplorerUrls:e.union([e.tuple([e.string()]),e.null()]).optional(),iconUrls:e.array(e.string()).optional()})]),Fm=e.null(),Km=a,Hz=i(Lm,Hm),Fz=f(Fm,Km);var Fp={};l(Fp,{error:()=>Qm,method:()=>Vm,params:()=>Gm,request:()=>Kz,response:()=>Vz,result:()=>Wm});p();n();var Vm=e.literal("wallet_selectEthereumProvider"),Gm=e.tuple([]),Wm=tr,Qm=a,Kz=i(Vm,Gm),Vz=f(Wm,Qm);var Kp={};l(Kp,{error:()=>Ym,method:()=>Zm,params:()=>Jm,request:()=>Gz,response:()=>Wz,result:()=>Xm});p();n();var Zm=e.literal("wallet_switchEthereumChain"),Jm=e.tuple([e.object({chainId:B})]),Xm=e.null(),Ym=a,Gz=i(Zm,Jm),Wz=f(Xm,Ym);var Vp={};l(Vp,{error:()=>tl,method:()=>$m,params:()=>el,request:()=>Qz,response:()=>Zz,result:()=>rl});p();n();var $m=e.literal("wallet_watchAsset"),el=e.object({type:e.literal("ERC20"),options:e.object({address:b,symbol:e.string(),decimals:e.number(),image:e.string()})}),rl=e.boolean(),tl=a,Qz=i($m,el),Zz=f(rl,tl);var Gp={};l(Gp,{error:()=>sl,method:()=>ol,params:()=>pl,request:()=>Jz,response:()=>Xz,result:()=>nl});p();n();var ol=e.literal("web3_clientVersion"),pl=e.tuple([]),nl=e.string(),sl=a,Jz=i(ol,pl),Xz=f(nl,sl);var Wp={};l(Wp,{error:()=>cl,method:()=>al,params:()=>il,request:()=>Yz,response:()=>$z,result:()=>fl});p();n();var al=e.literal("web3_sha3"),il=e.tuple([N]),fl=N,cl=a,Yz=i(al,il),$z=f(fl,cl);var Qp={};l(Qp,{error:()=>yl,method:()=>ul,params:()=>ml,request:()=>oR,response:()=>pR,result:()=>ll});p();n();var eR=e.record(e.string(),e.any()),rR=e.record(e.string(),eR),tR=e.object({parentCapability:e.string(),date:e.number().optional()}),ul=e.literal("wallet_requestPermissions"),ml=e.tuple([rR]),ll=e.array(tR),yl=a,oR=i(ul,ml),pR=f(ll,yl);var Zp={};l(Zp,{error:()=>gl,method:()=>xl,params:()=>dl,request:()=>aR,response:()=>iR,result:()=>hl});p();n();var nR=e.object({type:e.string(),value:e.any()}),sR=e.object({invoker:e.string().url(),parentCapability:e.string(),caveats:e.array(nR)}),xl=e.literal("wallet_getPermissions"),dl=e.tuple([]),hl=e.array(sR),gl=a,aR=i(xl,dl),iR=f(hl,gl);p();n();p();n();var fR=e.literal("phantom_accountChanged"),cR=e.object({evm:e.optional(b),sol:e.optional(or),btc:e.array(we).optional()}).nullish().default(null),s1=ee(fR,cR);p();n();var mR=e.literal("phantom_metaMaskOverrideSettingsChanged"),lR=e.null(),u1=ee(mR,lR);p();n();var xR=e.literal("phantom_chainChanged"),dR=e.object({evm:m}).nullish().default(null),h1=ee(xR,dR);p();n();var gR=e.literal("phantom_dappIcon"),zR=e.string().nullish().default(null),b1=ee(gR,zR);p();n();var _R=e.literal("phantom_dappMeta"),bR=e.object({title:e.string(),url:e.string(),icons:e.object({href:e.string(),size:e.object({width:e.number(),height:e.number()})}).array()}),I1=ee(_R,bR);p();n();var PR=e.literal("phantom_trustRevoked"),SR=e.object({evm:e.optional(b),sol:e.optional(or),btc:e.array(we).optional()}).nullish().default(null),D1=ee(PR,SR);var gy={};l(gy,{phantom_deep_link_browse:()=>Jp,phantom_deep_link_connect:()=>$p,phantom_deep_link_disconnect:()=>en,phantom_deep_link_fungible:()=>Yp,phantom_deep_link_onboard:()=>un,phantom_deep_link_signAllTransactions:()=>pn,phantom_deep_link_signAndSendAllTransactions:()=>fn,phantom_deep_link_signAndSendTransaction:()=>an,phantom_deep_link_signIn:()=>tn,phantom_deep_link_signMessage:()=>rn,phantom_deep_link_signTransaction:()=>on,phantom_deep_link_swap:()=>Xp,phantom_deep_link_tokens:()=>cn});p();n();var Jp={};l(Jp,{error:()=>El,method:()=>Rl,params:()=>_l,path:()=>TR,request:()=>vR,response:()=>AR,result:()=>bl});p();n();var Rl=e.literal("phantom_deep_link_browse"),_l=e.object({url:e.string(),ref:e.string()}),bl=e.null(),El=a,vR=i(Rl,_l),AR=f(bl,El),TR="browse/:url";var Xp={};l(Xp,{error:()=>Il,method:()=>Pl,params:()=>Sl,request:()=>wR,response:()=>NR,result:()=>ql});p();n();var Pl=e.literal("phantom_deep_link_swap"),Sl=e.object({buy:e.string(),sell:e.string()}),ql=e.null(),Il=a,wR=i(Pl,Sl),NR=f(ql,Il);var Yp={};l(Yp,{error:()=>wl,method:()=>vl,params:()=>Al,request:()=>DR,response:()=>CR,result:()=>Tl});p();n();var vl=e.literal("phantom_deep_link_fungible"),Al=e.object({token:e.string()}),Tl=e.null(),wl=a,DR=i(vl,Al),CR=f(Tl,wl);var $p={};l($p,{error:()=>kl,method:()=>Nl,params:()=>Dl,request:()=>kR,response:()=>BR,responsePayload:()=>MR,result:()=>Cl});p();n();var Nl=e.literal("phantom_deep_link_connect"),Dl=e.object({app_url:Mt,dapp_encryption_public_key:nr,redirect_link:ar,cluster:Ut.optional()}),Cl=e.object({phantom_encryption_public_key:Bt,nonce:Ke,data:sr}),kl=a,kR=i(Nl,Dl),BR=f(Cl,kl),MR=e.object({public_key:g,session:g});var en={};l(en,{error:()=>jl,method:()=>Bl,params:()=>Ml,request:()=>UR,requestPayload:()=>OR,response:()=>jR,result:()=>Ul});p();n();var Bl=e.literal("phantom_deep_link_disconnect"),Ml=pe,Ul=e.null(),jl=a,UR=i(Bl,Ml),jR=f(Ul,jl),OR=e.object({session:g});var rn={};l(rn,{error:()=>Fl,method:()=>Ol,params:()=>Ll,request:()=>LR,requestPayload:()=>FR,response:()=>HR,responsePayload:()=>KR,result:()=>Hl});p();n();var Ol=e.literal("phantom_deep_link_signMessage"),Ll=pe,Hl=xe,Fl=a,LR=i(Ol,Ll),HR=f(Hl,Fl),FR=e.object({session:g,message:g,display:e.union([e.literal("utf8"),e.literal("hex")]).optional()}),KR=e.object({signature:g,publicKey:g});var tn={};l(tn,{error:()=>Wl,method:()=>Kl,params:()=>Vl,request:()=>VR,requestPayload:()=>WR,response:()=>GR,responsePayload:()=>QR,result:()=>Gl});p();n();var Kl=e.literal("phantom_deep_link_signIn"),Vl=e.object({app_url:Mt,dapp_encryption_public_key:nr,redirect_link:ar,cluster:Ut.optional(),payload:zo}),Gl=e.object({phantom_encryption_public_key:Bt,nonce:Ke,data:sr}),Wl=a,VR=i(Kl,Vl),GR=f(Gl,Wl),WR=Ne,QR=e.object({address:g,signedMessage:g,signature:g,session:g});var on={};l(on,{error:()=>Xl,method:()=>Ql,params:()=>Zl,request:()=>ZR,requestPayload:()=>XR,response:()=>JR,responsePayload:()=>YR,result:()=>Jl});p();n();var Ql=e.literal("phantom_deep_link_signTransaction"),Zl=pe,Jl=xe,Xl=a,ZR=i(Ql,Zl),JR=f(Jl,Xl),XR=e.object({session:g,transaction:g}),YR=e.object({transaction:g});var pn={};l(pn,{error:()=>ry,method:()=>Yl,params:()=>$l,request:()=>$R,requestPayload:()=>r_,response:()=>e_,responsePayload:()=>t_,result:()=>ey});p();n();var Yl=e.literal("phantom_deep_link_signAllTransactions"),$l=pe,ey=xe,ry=a,$R=i(Yl,$l),e_=f(ey,ry),r_=e.object({session:g,transactions:e.array(g)}),t_=e.object({transactions:e.array(g)});var an={};l(an,{error:()=>ny,method:()=>ty,params:()=>oy,request:()=>o_,requestPayload:()=>n_,response:()=>p_,responsePayload:()=>s_,result:()=>py});p();n();var sn={};l(sn,{SolanaProviderEvent:()=>nn,SolanaSendOptions:()=>Ee});p();n();var Ee=e.optional(e.object({skipPreflight:e.optional(e.boolean()),preflightCommitment:e.optional(e.union([e.literal("processed"),e.literal("confirmed"),e.literal("finalized"),e.literal("recent"),e.literal("single"),e.literal("singleGossip"),e.literal("root"),e.literal("max")])),maxRetries:e.optional(e.number()),minContextSlot:e.optional(e.number())})),nn=(c=>(c.Connect="connect",c.Disconnect="disconnect",c.AccountChanged="accountChanged",c))(nn||{}),T2=e.nativeEnum(nn);var ty=e.literal("phantom_deep_link_signAndSendTransaction"),oy=pe,py=xe,ny=a,o_=i(ty,oy),p_=f(py,ny),n_=e.object({session:g,transaction:g,sendOptions:Ee.optional()}),s_=e.object({signature:g});var fn={};l(fn,{error:()=>fy,method:()=>sy,params:()=>ay,request:()=>a_,requestPayload:()=>f_,response:()=>i_,responsePayload:()=>c_,result:()=>iy});p();n();var sy=e.literal("phantom_deep_link_signAndSendAllTransactions"),ay=pe,iy=xe,fy=a,a_=i(sy,ay),i_=f(iy,fy),f_=e.object({session:g,transactions:e.array(g),sendOptions:Ee.optional()}),c_=e.object({signatures:e.array(e.union([g,e.null()]))});var cn={};l(cn,{error:()=>ly,method:()=>cy,params:()=>uy,path:()=>l_,request:()=>u_,response:()=>m_,result:()=>my});p();n();var cy=e.literal("phantom_deep_link_tokens"),uy=e.object({chain:e.string(),address:e.string().optional(),referralId:e.string().optional()}),my=e.null(),ly=a,u_=i(cy,uy),m_=f(my,ly),l_="tokens/:chain{/:address}";var un={};l(un,{error:()=>hy,method:()=>yy,params:()=>xy,request:()=>y_,response:()=>x_,result:()=>dy});p();n();var yy=e.literal("phantom_deep_link_onboard"),xy=e.object({value:e.string().optional(),accounts:e.string().optional()}),dy=e.null(),hy=a,y_=i(yy,xy),x_=f(dy,hy);var Qy={};l(Qy,{common:()=>sn,sol_connect:()=>Ot,sol_disconnect:()=>mn,sol_signAllTransactions:()=>ln,sol_signAndSendAllTransactions:()=>Ft,sol_signAndSendTransaction:()=>yn,sol_signIn:()=>dn,sol_signMessage:()=>xn,sol_signTransaction:()=>hn});p();n();var Ot={};l(Ot,{error:()=>_y,method:()=>zy,params:()=>Ry,request:()=>d_,response:()=>h_,result:()=>jt});p();n();var zy=e.literal("sol_connect"),Ry=e.object({onlyIfTrusted:e.optional(e.boolean())}),jt=e.object({publicKey:e.string()}),_y=a,d_=i(zy,Ry),h_=f(jt,_y);var mn={};l(mn,{error:()=>Sy,method:()=>by,params:()=>Ey,request:()=>g_,response:()=>z_,result:()=>Py});p();n();var by=e.literal("sol_disconnect"),Ey=He.optional(),Py=e.null(),Sy=a,g_=i(by,Ey),z_=f(Py,Sy);var ln={};l(ln,{error:()=>Ay,method:()=>qy,params:()=>Iy,request:()=>R_,response:()=>__,result:()=>vy});p();n();var qy=e.literal("sol_signAllTransactions"),Iy=e.object({transactions:e.array(g)}),vy=e.array(e.object({signature:e.string(),transaction:g,version:e.union([e.literal("legacy"),e.number()])})),Ay=a,R_=i(qy,Iy),__=f(vy,Ay);var yn={};l(yn,{error:()=>Dy,method:()=>Ty,params:()=>wy,request:()=>b_,response:()=>E_,result:()=>Ny});p();n();var Ty=e.literal("sol_signAndSendTransaction"),wy=e.object({transaction:g,options:Ee,showConfirmation:e.boolean().optional(),postAction:Ve.optional()}),Ny=e.object({signature:e.string(),publicKey:e.string()}),Dy=a,b_=i(Ty,wy),E_=f(Ny,Dy);var Ft={};l(Ft,{error:()=>ky,method:()=>Cy,params:()=>Lt,request:()=>P_,response:()=>S_,result:()=>Ht});p();n();var Cy=e.literal("sol_signAndSendAllTransactions"),Lt=e.object({transactions:e.array(g),options:Ee}),Ht=e.object({signatures:e.array(e.union([e.string(),e.null()])),publicKey:e.string()}),ky=a,P_=i(Cy,Lt),S_=f(Ht,ky);var xn={};l(xn,{error:()=>jy,method:()=>By,params:()=>My,request:()=>q_,response:()=>I_,result:()=>Uy});p();n();var By=e.literal("sol_signMessage"),My=e.object({message:g,display:e.union([e.literal("utf8"),e.literal("hex")])}),Uy=e.object({signature:e.string(),publicKey:e.string()}),jy=a,q_=i(By,My),I_=f(Uy,jy);var dn={};l(dn,{error:()=>Fy,method:()=>Oy,params:()=>Ly,request:()=>v_,response:()=>A_,result:()=>Hy});p();n();var Oy=e.literal("sol_signIn"),Ly=e.object({signInData:Ne}),Hy=e.object({address:e.string(),signedMessage:e.string(),signature:e.string()}),Fy=a,v_=i(Oy,Ly),A_=f(Hy,Fy);var hn={};l(hn,{error:()=>Wy,method:()=>Ky,params:()=>Vy,request:()=>T_,response:()=>w_,result:()=>Gy});p();n();var Ky=e.literal("sol_signTransaction"),Vy=e.object({transaction:g}),Gy=e.object({signature:e.string(),transaction:g,version:e.union([e.literal("legacy"),e.number()])}),Wy=a,T_=i(Ky,Vy),w_=f(Gy,Wy);var ox={};l(ox,{sol_blink_connect:()=>gn,sol_blink_signAndSendAllTransactions:()=>zn});p();n();var gn={};l(gn,{error:()=>Yy,method:()=>Zy,params:()=>Jy,request:()=>N_,response:()=>D_,result:()=>Xy});p();n();p();n();var Kt=e.object({blinkUrl:e.string().url(),blinkTitle:e.string().optional()});var Zy=e.literal("sol_blink_connect"),Jy=e.object({context:Kt}),Xy=jt,Yy=a,N_=i(Zy,Jy),D_=f(Xy,Yy);var zn={};l(zn,{error:()=>tx,method:()=>$y,params:()=>ex,request:()=>C_,response:()=>k_,result:()=>rx});p();n();var $y=e.literal("sol_blink_signAndSendAllTransactions"),ex=Lt.merge(e.object({context:Kt})),rx=Ht,tx=a,C_=i($y,ex),k_=f(rx,tx);var Px={};l(Px,{sol_mwa_authorize:()=>vn,sol_mwa_reauthorize:()=>An,sol_mwa_sign_and_send_transactions:()=>Nn,sol_mwa_sign_messages:()=>wn,sol_mwa_sign_transactions:()=>Tn});p();n();var vn={};l(vn,{error:()=>ax,method:()=>px,params:()=>nx,request:()=>U_,response:()=>j_,result:()=>sx});p();n();p();n();var Vt=e.object({identityName:e.string().nullish(),identityUri:e.string().nullish(),iconRelativeUri:e.string().nullish()}),B_=e.object({identity:Vt,authorizationScope:e.string()}),Gt=e.object({verifiableIdentity:B_,publicKey:e.string(),payloads:e.array(e.string())}),Rn=e.object({identity:Vt,cluster:e.string().optional()}),_n=e.object({verifiableIdentity:Vt}),bn=e.object({signPayloads:Gt,minContextSlot:e.number()}),En=e.object({signPayloads:Gt}),Pn=e.object({signPayloads:Gt}),mw=e.union([Rn,_n,bn,En,Pn]),Sn=e.union([e.object({type:e.literal("AUTHORIZE_SUCCESS"),publicKey:e.string(),accountLabel:e.string().optional(),walletUriBase:e.string().optional(),scope:e.string().optional()}),e.object({type:e.literal("AUTHORIZE_DECLINE")})]),qn=e.union([e.object({type:e.literal("REAUTHORIZE_SUCCESS")}),e.object({type:e.literal("REAUTHORIZE_DECLINE")})]),ir=e.union([e.object({type:e.literal("SIGN_PAYLOADS_SUCCESS"),signedPayloads:e.array(e.string())}),e.object({type:e.literal("SIGN_PAYLOADS_DECLINE")}),e.object({type:e.literal("SIGN_PAYLOADS_ERROR_INVALID_PAYLOADS"),valid:e.array(e.boolean())}),e.object({type:e.literal("SIGN_PAYLOADS_ERROR_AUTHORIZATION_NOT_VALID")}),e.object({type:e.literal("SIGN_PAYLOADS_ERROR_TOO_MANY_PAYLOADS")})]),In=e.union([e.object({type:e.literal("SIGN_AND_SEND_TRANSACTIONS_SUCCESS"),signedPayloads:e.array(e.string())}),e.object({type:e.literal("SIGN_AND_SEND_TRANSACTIONS_DECLINE")}),e.object({type:e.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_INVALID_PAYLOADS"),valid:e.array(e.boolean())}),e.object({type:e.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_NOT_SUBMITTED"),signatures:e.array(e.string())}),e.object({type:e.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_TOO_MANY_PAYLOADS")}),e.object({type:e.literal("SIGN_AND_SEND_TRANSACTIONS_ERROR_AUTHORIZATION_NOT_VALID")})]),M_=e.union([Sn,qn,ir,In]);var px=e.literal("sol_mwa_authorize"),nx=Rn,sx=Sn,ax=a,U_=i(px,nx),j_=f(sx,ax);var An={};l(An,{error:()=>ux,method:()=>ix,params:()=>fx,request:()=>O_,response:()=>L_,result:()=>cx});p();n();var ix=e.literal("sol_mwa_reauthorize"),fx=_n,cx=qn,ux=a,O_=i(ix,fx),L_=f(cx,ux);var Tn={};l(Tn,{error:()=>xx,method:()=>mx,params:()=>lx,request:()=>H_,response:()=>F_,result:()=>yx});p();n();var mx=e.literal("sol_mwa_sign_transactions"),lx=En,yx=ir,xx=a,H_=i(mx,lx),F_=f(yx,xx);var wn={};l(wn,{error:()=>zx,method:()=>dx,params:()=>hx,request:()=>K_,response:()=>V_,result:()=>gx});p();n();var dx=e.literal("sol_mwa_sign_messages"),hx=Pn,gx=ir,zx=a,K_=i(dx,hx),V_=f(gx,zx);var Nn={};l(Nn,{error:()=>Ex,method:()=>Rx,params:()=>_x,request:()=>G_,response:()=>W_,result:()=>bx});p();n();var Rx=e.literal("sol_mwa_sign_and_send_transactions"),_x=bn,bx=In,Ex=a,G_=i(Rx,_x),W_=f(bx,Ex);var Dx={};l(Dx,{sol_pay_transaction:()=>Cn,sol_pay_transfer:()=>Dn});p();n();var Dn={};l(Dn,{error:()=>vx,method:()=>Sx,params:()=>qx,request:()=>Q_,response:()=>Z_,result:()=>Ix});p();n();var Sx=e.literal("sol_pay_transfer"),qx=e.object({amount:e.instanceof(Qn).optional(),recipient:e.string(),splToken:e.string().optional(),reference:e.array(e.string()).optional(),memo:e.string().optional(),label:e.string().optional(),message:e.string().optional()}),Ix=e.null(),vx=a,Q_=i(Sx,qx),Z_=f(Ix,vx);var Cn={};l(Cn,{error:()=>Nx,method:()=>Ax,params:()=>Tx,request:()=>J_,response:()=>X_,result:()=>wx});p();n();var Ax=e.literal("sol_pay_transaction"),Tx=e.object({link:e.string().url()}),wx=e.null(),Nx=a,J_=i(Ax,Tx),X_=f(wx,Nx);p();n();p();n();var Y_=typeof self.crypto<"u"&&self.crypto.randomUUID&&self.crypto.randomUUID.bind(self.crypto),kn={randomUUID:Y_};p();n();var Wt,$_=new Uint8Array(16);function Bn(){if(!Wt&&(Wt=typeof self.crypto<"u"&&self.crypto.getRandomValues&&self.crypto.getRandomValues.bind(self.crypto),!Wt))throw new Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return Wt($_)}p();n();var K=[];for(let r=0;r<256;++r)K.push((r+256).toString(16).slice(1));function Cx(r,t=0){return K[r[t+0]]+K[r[t+1]]+K[r[t+2]]+K[r[t+3]]+"-"+K[r[t+4]]+K[r[t+5]]+"-"+K[r[t+6]]+K[r[t+7]]+"-"+K[r[t+8]]+K[r[t+9]]+"-"+K[r[t+10]]+K[r[t+11]]+K[r[t+12]]+K[r[t+13]]+K[r[t+14]]+K[r[t+15]]}function eb(r,t,s){if(kn.randomUUID&&!t&&!r)return kn.randomUUID();r=r||{};let c=r.random||(r.rng||Bn)();if(c[6]=c[6]&15|64,c[8]=c[8]&63|128,t){s=s||0;for(let y=0;y<16;++y)t[s+y]=c[y];return t}return Cx(c)}var rb=eb;p();n();export{ob as a,sb as b,Ce as c,Be as d,gs as e,zs as f,Rs as g,Is as h,Ns as i,B as j,b as k,rr as l,mo as m,vt as n,At as o,Te as p,tr as q,or as r,g as s,Js as t,$s as u,Ro as v,Jd as w,Xd as x,Yd as y,$d as z,rf as A,zl as B,gy as C,Qy as D,ox as E,Vt as F,Gt as G,Px as H,Dx as I,Hi as J,rb as K};
/*! Bundled license information:

js-sha3/src/sha3.js:
  (**
   * [js-sha3]{@link https://github.com/emn178/js-sha3}
   *
   * @version 0.8.0
   * <AUTHOR> Yi-Cyuan [<EMAIL>]
   * @copyright Chen, Yi-Cyuan 2015-2018
   * @license MIT
   *)
*/
//# sourceMappingURL=chunk-E3NPIRHS.js.map
