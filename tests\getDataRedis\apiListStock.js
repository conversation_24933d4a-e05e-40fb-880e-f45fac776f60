const { request } = require('@playwright/test');
const config = require('./configRedis.json');

const apiUrl = 'https://api.dex3.fi/v2/token-list/stocks';
const headers = {
    'x-api-key': config.redis_search['x-api-key'],
    'Content-Type': 'application/json',
};

async function StockList() {
    const reqContext = await request.newContext();

    const res = await reqContext.post(apiUrl, { headers });
    const json = await res.json()
    return json.data.map((item) => item.token.address);
}

module.exports = StockList;



