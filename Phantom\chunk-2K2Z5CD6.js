import{a}from"./chunk-O3A2VMJ6.js";import{a as L,b as i,d as l,e as v}from"./chunk-W3ZRPNOX.js";import{z as C}from"./chunk-JD6NH5K6.js";import{a as y}from"./chunk-IVMV7P4T.js";import{a as t,e as w,n as b}from"./chunk-2NGYUYTC.js";import{a as x}from"./chunk-4VDZJDFB.js";import{a as S}from"./chunk-VQVTLSDS.js";import{o as d,qb as h}from"./chunk-WIQ4WVKX.js";import{g}from"./chunk-HRJWTAGT.js";import{Pb as k}from"./chunk-MZZEJ42N.js";import{e as p}from"./chunk-ALUTR72U.js";import{a as $}from"./chunk-7X4NV6OJ.js";import{f as W,h as s,n as f}from"./chunk-3KENBVE7.js";s();f();var o=W($());var e={screen:t({overflow:"auto"}),body:t({display:"flex",flexDirection:"column",justifyContent:"space-between"}),content:t({display:"flex",flexDirection:"column",width:"100%"}),assets:t({backgroundColor:"bgRow",borderRadius:6,width:"100%"}),line:t({backgroundColor:"bgWallet",width:"100%",height:1}),infoCard:t({marginTop:16}),button:t({width:"100%",height:48})},j=d(h).attrs({color:S.grayLight,size:14})`
  text-align: left;
  line-height: normal;
  max-width: 100%;
  margin: 10px 0 16px;
`,E=d.a.attrs({target:"_blank",rel:"noopener noreferrer"})`
  color: ${m=>m.theme.purple};
  text-decoration: none;
  cursor: pointer;
`,G=()=>o.default.createElement(j,null,o.default.createElement(x,{i18nKey:"liquidStakeReviewConversionFootnote"},"When you stake Solana tokens in exchange for JitoSOL you'll receive a slightly lesser amount of JitoSOL.",o.default.createElement(E,{href:g},"Learn more"))),ao=o.default.memo(({process:m,headerTitle:T,onBack:I,onPrimaryButtonPress:N,canSubmit:P,payAsset:r,receiveAsset:n,accountLabelText:V,account:O,providerLabelText:U,providerName:F,apyLabelText:q,apy:B,networkFeeLabelText:_,networkFee:c,isLoading:u,networkFeeErrorMsg:H,primaryButtonText:J})=>{let z=m==="mint",D=[n?o.default.createElement(i,{label:V},o.default.createElement(l,null,o.default.createElement(w,{font:"body",children:k(O,4)}))):null,o.default.createElement(i,{label:U},o.default.createElement(l,null,F)),o.default.createElement(i,{label:q},o.default.createElement(l,null,B)),o.default.createElement(i,{label:_,isLoading:u,error:H},o.default.createElement(l,null,c))];return o.default.createElement("div",{className:e.screen},o.default.createElement(C,{leftButton:{type:"back",onClick:I},titleSize:"regular"},T),o.default.createElement("div",{className:e.body},o.default.createElement("div",{className:e.content},o.default.createElement("div",{className:e.assets},r?o.default.createElement(a,{title:r.title,amount:r.amount+" "+r.symbol,amountUsd:r.amountUsd,logoUri:r.logoUri,symbol:r.symbol,tokenType:r.tokenType,tokenAddress:r.tokenAddress,network:r.network}):null,o.default.createElement("div",{className:e.line}),n?o.default.createElement(a,{title:n.title,amount:n.amount+" "+n.symbol,amountUsd:n.amountUsd,logoUri:n.logoUri,symbol:n.symbol,tokenType:n.tokenType,tokenAddress:n.tokenAddress,network:n.network}):null),o.default.createElement(L,{className:e.infoCard,roundedTop:!0,roundedBottom:!0},p(D,o.default.createElement(v,{gap:1})).map((K,M)=>o.default.createElement("div",{key:`r-${M}`},K))),z?o.default.createElement(G,null):null),o.default.createElement(y,null,o.default.createElement(b,{className:e.button,theme:"primary",disabled:!P||u,onClick:N},J))))});export{ao as a};
//# sourceMappingURL=chunk-2K2Z5CD6.js.map
