import { Page, expect } from '@playwright/test';
import { BasePage } from '../pages/BasePage';
import { DashboardSelectors } from '../../selectors/DashboardSelectors';
import * as path from 'path';

/**
 * TokenListComponent - Handles all token list operations
 * This component encapsulates token list verification and interaction functionality
 */
export class TokenListComponent extends BasePage {
  constructor(page: Page) {
    super(page);
  }

  /**
   * Verify column headers in token list
   */
  async verifyColumnHeaders(screenshotDir: string): Promise<void> {
    this.logAction('Verifying token list column headers');

    // Check all column headers are visible
    for (const header of DashboardSelectors.columnHeaders) {
      const headerElement = this.page.locator(DashboardSelectors.columnHeader(header)).first();
      await expect(headerElement).toBeVisible({ timeout: 5000 });
      this.logAction(`✅ Column header "${header}" is visible`);
    }

    // Test sorting by clicking Age header
    this.logAction('Testing sorting by clicking Age header');
    const ageHeader = this.page.locator(DashboardSelectors.ageHeader);
    await ageHeader.click();
    await this.sleep(1000);

    // Take screenshot after sorting
    await this.page.screenshot({ path: path.join(screenshotDir, 'age-sorted.png') });

    // Click again to reverse sort
    await ageHeader.click();
    await this.sleep(1000);

    // Take screenshot after reverse sorting
    await this.page.screenshot({ path: path.join(screenshotDir, 'age-reverse-sorted.png') });

    this.logAction('Column headers verification completed');
  }

  /**
   * Verify token list content and structure
   */
  async verifyTokenListContent(screenshotDir: string): Promise<void> {
    this.logAction('Verifying token list content');

    // Check first token row has all expected elements
    const firstTokenRow = this.page.locator(DashboardSelectors.firstTokenRow);
    await expect(firstTokenRow).toBeVisible();

    // Verify token icon
    await expect(firstTokenRow.locator(DashboardSelectors.tokenIcon)).toBeVisible();

    // Verify token name
    await expect(firstTokenRow.locator(DashboardSelectors.tokenName)).toBeVisible();

    // Verify token price
    await expect(firstTokenRow.locator(DashboardSelectors.tokenPrice)).toBeVisible();

    // Verify token age
    const ageCell = firstTokenRow.locator(DashboardSelectors.tokenAge);
    await expect(ageCell).toBeVisible();

    // Verify market cap
    const mcapCell = firstTokenRow.locator(DashboardSelectors.tokenMcap);
    await expect(mcapCell).toBeVisible();

    // Take screenshot of token list
    await this.page.screenshot({ path: path.join(screenshotDir, 'token-list.png'), fullPage: true });

    this.logAction('Token list content verification completed');
  }

  /**
   * Get token count in current list
   */
  async getTokenCount(): Promise<number> {
    const tokenRowSelector = 'a.chakra-link.token-virtual-row';
    return await this.page.locator(tokenRowSelector).count();
  }

  /**
   * Get token information by index
   */
  async getTokenInfo(index: number): Promise<{
    name: string;
    symbol: string;
    price: string;
    dex: string;
  }> {
    const tokenRowSelector = 'a.chakra-link.token-virtual-row';
    const tokenElement = this.page.locator(tokenRowSelector).nth(index);

    const name = await tokenElement.locator('.chakra-text.css-16rit9e').textContent() || '';
    const dexImg = tokenElement.locator('.css-jvoq11 img.css-ha03xt');
    const dex = await dexImg.getAttribute('alt') || 'Unknown';
    
    // Get additional info if needed
    const price = await tokenElement.locator(DashboardSelectors.tokenPrice).textContent() || '';
    
    return {
      name: name.trim(),
      symbol: '', // Could be extracted if needed
      price: price.trim(),
      dex: dex
    };
  }

  /**
   * Click on a token by index
   */
  async clickToken(index: number): Promise<void> {
    const tokenRowSelector = 'a.chakra-link.token-virtual-row';
    const tokenElement = this.page.locator(tokenRowSelector).nth(index);
    await tokenElement.click();
    await this.waitForPageLoad();
  }

  /**
   * Search for a specific token by name
   */
  async findTokenByName(tokenName: string, maxTokensToCheck: number = 50): Promise<number | null> {
    this.logAction(`Searching for token: ${tokenName}`);
    
    const tokenRowSelector = 'a.chakra-link.token-virtual-row';
    const totalTokens = await this.page.locator(tokenRowSelector).count();
    const tokensToCheck = Math.min(maxTokensToCheck, totalTokens);

    for (let i = 0; i < tokensToCheck; i++) {
      const tokenInfo = await this.getTokenInfo(i);
      if (tokenInfo.name.toLowerCase().includes(tokenName.toLowerCase())) {
        this.logAction(`Found token "${tokenName}" at index ${i}`);
        return i;
      }
    }

    this.logAction(`Token "${tokenName}" not found in first ${tokensToCheck} tokens`);
    return null;
  }

  /**
   * Verify token list is properly loaded
   */
  async verifyTokenListLoaded(minTokenCount: number = 5): Promise<void> {
    this.logAction(`Verifying token list has at least ${minTokenCount} tokens`);
    
    // Wait for tokens to load
    await this.sleep(2000);
    
    const tokenCount = await this.getTokenCount();
    
    if (tokenCount < minTokenCount) {
      throw new Error(`Expected at least ${minTokenCount} tokens, but found ${tokenCount}`);
    }
    
    this.logAction(`✅ Token list loaded with ${tokenCount} tokens`);
  }

  /**
   * Verify tokens are from specific DEX
   */
  async verifyTokensFromDex(expectedDex: string, maxTokensToCheck: number = 20): Promise<boolean> {
    try {
      this.logAction(`Verifying ${maxTokensToCheck} tokens are from DEX: ${expectedDex}`);

      const tokenRowSelector = 'a.chakra-link.token-virtual-row';
      
      // Hover on first row to stabilize the list
      const firstRow = this.page.locator(tokenRowSelector).first();
      await firstRow.hover();
      await this.sleep(1000);

      const totalTokens = await this.getTokenCount();
      const tokensToCheck = Math.min(maxTokensToCheck, totalTokens);

      if (tokensToCheck === 0) {
        this.logAction('No tokens found to verify');
        await this.takeScreenshot('no-tokens-found');
        return false;
      }

      this.logAction(`Found ${totalTokens} tokens, checking ${tokensToCheck} tokens`);

      let nonMatchingTokens = 0;

      for (let i = 0; i < tokensToCheck; i++) {
        const tokenInfo = await this.getTokenInfo(i);
        
        if (tokenInfo.dex !== expectedDex) {
          nonMatchingTokens++;
          this.logAction(`❌ Token ${i+1} "${tokenInfo.name}": DEX is "${tokenInfo.dex}", not "${expectedDex}"`);
        }
      }

      if (nonMatchingTokens === 0) {
        this.logAction(`✅ All ${tokensToCheck} tokens are from DEX "${expectedDex}"`);
        return true;
      } else {
        this.logAction(`❌ ${nonMatchingTokens}/${tokensToCheck} tokens are not from DEX "${expectedDex}"`);
        await this.takeScreenshot(`dex-verification-failed-${expectedDex}`);
        return false;
      }
    } catch (error) {
      console.error(`Error verifying tokens from DEX: ${error instanceof Error ? error.message : 'Unknown error'}`);
      await this.takeScreenshot(`dex-verification-error-${expectedDex}`);
      return false;
    }
  }

  /**
   * Wait for token list to update after filter change
   */
  async waitForTokenListUpdate(timeout: number = 10000): Promise<void> {
    this.logAction('Waiting for token list to update');
    
    // Wait for any loading indicators to disappear
    await this.sleep(2000);
    
    // Verify list has loaded
    await this.verifyTokenListLoaded();
    
    this.logAction('Token list update completed');
  }

  /**
   * Get all visible token names
   */
  async getAllTokenNames(maxTokens: number = 20): Promise<string[]> {
    const tokenNames: string[] = [];
    const tokenCount = Math.min(await this.getTokenCount(), maxTokens);
    
    for (let i = 0; i < tokenCount; i++) {
      const tokenInfo = await this.getTokenInfo(i);
      tokenNames.push(tokenInfo.name);
    }
    
    return tokenNames;
  }

  /**
   * Verify token list UI components
   */
  async verifyTokenListUI(screenshotDir: string): Promise<void> {
    this.logAction('Starting comprehensive token list UI verification');

    try {
      // 1. Verify column headers
      await this.verifyColumnHeaders(screenshotDir);

      // 2. Verify token list content
      await this.verifyTokenListContent(screenshotDir);

      // 3. Verify list is properly loaded
      await this.verifyTokenListLoaded();

      this.logAction('Token list UI verification completed successfully');
    } catch (error) {
      console.error(`Token list UI verification failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      await this.takeScreenshot(`token-list-ui-error`);
      throw error;
    }
  }

  /**
   * Take screenshot of current token list state
   */
  async takeTokenListScreenshot(filename: string): Promise<void> {
    await this.takeScreenshot(`token-list-${filename}`);
  }
} 