import{D as E}from"./chunk-SLQBAOEK.js";import{Ra as a}from"./chunk-MZZEJ42N.js";import{A as w,C as T}from"./chunk-ALUTR72U.js";import{h as e,n as t}from"./chunk-3KENBVE7.js";e();t();e();t();e();t();var $=new Set(["trustedApplicationsByOwner","trustedApplicationsByOwnerBrowser","trustedApplicationsByOwnerMobile"]),q="userTrustedApps";e();t();e();t();var de="userExplorerPreferenceV2";e();t();var he=new Error("Failed to get cluster from storage");var Oe={"mainnet-beta":a.Solana.Mainnet,devnet:a.Solana.Devnet,testnet:a.Solana.Testnet,localhost:a.Solana.Localnet};e();t();e();t();var Ke="isAnalyticsOptedOut";e();t();e();t();e();t();e();t();var h=n=>{let{accountIdentifier:m,autoConfirmEnabled:f=!1,trustedAppsByAccount:A}=n;if(!m||!A)return;let M=Object.values(A),S=[],y=[];f?(M.forEach(o=>{o.autoConfirm&&E(o.autoConfirm)?S.push(o):y.push(o)}),S.sort((o,d)=>(d.autoConfirm?.sessionStartTime??0)-(o.autoConfirm?.sessionStartTime??0))):y=M;let C=w(y.map(o=>{let d=o.lastConnectedTimestamp??0;return{timestamp:d,data:{...o,lastConnectedTimestamp:d}}}));return{autoConfirmEnabledApps:S,timeBucketedApps:C}};e();t();var O=(n,m)=>{if(!m||n.autoConfirm?.sessionStartTime===void 0||n.autoConfirm?.maxSessionDuration===void 0)return;let f=new Date(n.autoConfirm?.sessionStartTime+n.autoConfirm?.maxSessionDuration*1e3);return T(f).format("h:mm A")};export{h as a,O as b};
//# sourceMappingURL=chunk-THLBAMDB.js.map
