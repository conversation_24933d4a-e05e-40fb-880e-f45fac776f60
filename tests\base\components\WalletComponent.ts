import { <PERSON>, BrowserContext } from '@playwright/test';
import { BasePage } from '../pages/BasePage';
import { DashboardSelectors } from '../../selectors/DashboardSelectors';
import { ENV } from '../env';

/**
 * WalletComponent - Handles all wallet-related operations
 * This component encapsulates wallet import, login, and popup handling functionality
 */
export class WalletComponent extends BasePage {
  private processedNotificationPages: Set<string> = new Set();

  constructor(page: Page) {
    super(page);
  }

  /**
   * Import Phantom wallet using private key
   */
  async importWallet(): Promise<void> {
    try {
      this.logAction('Starting wallet import process');
      const walletName = "Test Wallet";
      const password = "12345678";

      await this.clickElement(DashboardSelectors.importWalletBtn, "Import Wallet Button");
      await this.clickElement(DashboardSelectors.importPrivateKeyBtn, "Import Private Key Button");
      await this.fillInput(DashboardSelectors.walletNameInput, walletName, "Wallet Name Input");
      await this.fillInput(DashboardSelectors.privateKeyInput, ENV.PRIVATE_KEY_PHANTOM, "Private Key Input");
      await this.clickElement(DashboardSelectors.importBtn, "Import Button");
      await this.fillInput(DashboardSelectors.passwordInput, password, "Password Input");
      await this.fillInput(DashboardSelectors.confirmPasswordInput, password, "Confirm Password Input");
      await this.clickElement(DashboardSelectors.agreeCheckbox, "Agree Checkbox");
      await this.clickElement(DashboardSelectors.continueBtn, "Continue Button");
      await this.clickElement(DashboardSelectors.getStartedBtn, "Get Started Button");
      
      this.logAction('Wallet imported successfully');
    } catch (error) {
      console.error(`Wallet import failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      throw new Error(`Unable to import wallet: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Login to Dex3 using Phantom wallet
   */
  async loginDex3(): Promise<void> {
    try {
      this.logAction('Starting Dex3 login process');
      await this.waitForPageLoad();

      // Initiate login with Phantom
      this.logAction('Clicking Phantom icon to login');
      await this.clickElement(DashboardSelectors.phantomBtn, "Phantom Icon");

      // Handle notification popups
      await this.handleNotificationPopups();

      // Verify login state
      await this.verifyLoginState();
      this.logAction('Login successful');
    } catch (error) {
      console.error(`Login failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      this.processedNotificationPages.clear();
      throw error;
    }
  }

  /**
   * Verify login state by checking for menu button
   */
  async verifyLoginState(): Promise<void> {
    try {
      this.logAction('Verifying login state');
      await this.waitForElement(DashboardSelectors.menuBtn, "Menu Button", 30000);
      this.logAction('Menu button found, login verified successfully');
    } catch (error) {
      if (error instanceof Error) {
        console.error('Login verification failed:', error.message);
      } else {
        console.error('Login verification failed: Unknown error');
      }

      // Log current page info for debugging
      console.log('Current URL:', this.getCurrentUrl());
      const pageContent = await this.uiActions.content();
      console.log('Page content sample:', pageContent.substring(0, 200));

      throw new Error(`Unable to verify login state: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle notification popups from Phantom wallet
   */
  async handleNotificationPopups(timeout = 30000): Promise<void> {
    this.logAction('Starting to handle Phantom notification popups');
    const context = this.page.context();

    try {
      // Handle Connect popup first
      await this.handleConnectPopup(context, timeout);

      // Then handle Confirm popup
      await this.handleConfirmPopup(context, timeout);
    } catch (error) {
      console.warn(`Issue handling popup: ${error instanceof Error ? error.message : 'Unknown error'}`);

      // Check if login succeeded despite popup issues
      try {
        await this.verifyLoginState();
        this.logAction('Login successful despite popup handling issues');
        return;
      } catch (e) {
        console.error('Login verification failed:', e instanceof Error ? e.message : 'Unknown error');
        throw new Error(`Popup handling failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    }
  }

  /**
   * Handle Connect popup from Phantom
   */
  private async handleConnectPopup(context: BrowserContext, timeout: number): Promise<void> {
    this.logAction('Handling Connect popup');
    const startTime = Date.now();

    while (Date.now() - startTime < timeout) {
      try {
        // Find all notification pages
        const pages = context.pages();
        const notificationPages = pages.filter((page: any) =>
          !page.isClosed() && page.url().includes("notification")
        );

        if (notificationPages.length === 0) {
          await this.sleep(500);
          continue;
        }

        // Process the first page found
        const page = notificationPages[0];
        await page.bringToFront();

        const connectButton = page.locator(DashboardSelectors.connectPhantomBtn);

        try {
          const isVisible = await connectButton.isVisible({ timeout: 3000 }).catch(() => false);

          if (isVisible) {
            this.logAction(`Found Connect button with selector: ${DashboardSelectors.connectPhantomBtn}`);
            await connectButton.click();
            this.logAction('Successfully clicked Connect button');
            await this.sleep(1000);
            return;
          }
        } catch (e: any) {
          // If page closed, consider it successful
          if (e.toString().includes("has been closed")) {
            this.logAction('Popup page closed automatically, considered successful');
            return;
          }
        }
      } catch (error) {
        // Ignore errors and continue trying
        console.log('Error handling Connect popup, retrying...');
      }
    }

    throw new Error(`Unable to handle Connect popup within ${timeout}ms`);
  }

  /**
   * Handle Confirm popup from Phantom
   */
  private async handleConfirmPopup(context: BrowserContext, timeout: number): Promise<void> {
    this.logAction('Handling Confirm popup');

    // Wait briefly to ensure Connect popup is fully processed
    this.logAction('Waiting 2 seconds for Confirm popup to appear');
    await this.sleep(2000);

    // Register listener for new pages
    let confirmPagePromiseResolved = false;
    const confirmPagePromise = new Promise<Page>((resolve) => {
      const onPageHandler = (page: Page) => {
        if (page.url().includes("notification")) {
          confirmPagePromiseResolved = true;
          context.removeListener('page', onPageHandler);
          resolve(page);
        }
      };
      context.on('page', onPageHandler);

      // Auto-remove listener after timeout
      setTimeout(() => {
        if (!confirmPagePromiseResolved) {
          context.removeListener('page', onPageHandler);
        }
      }, timeout);
    });

    try {
      // Check for existing notification pages
      const existingPages = context.pages();
      const notificationPages = existingPages.filter((page: any) =>
        !page.isClosed() && page.url().includes("notification")
      );

      let confirmPage: Page | null = null;

      if (notificationPages.length > 0) {
        confirmPage = notificationPages[0];
        this.logAction(`Using existing notification page: ${confirmPage.url()}`);
      } else {
        this.logAction('No existing notification page found, waiting for new one');
        try {
          confirmPage = await Promise.race([
            confirmPagePromise,
            new Promise<never>((_, reject) => {
              setTimeout(() => reject(new Error(`No Confirm popup found within ${timeout}ms`)), timeout);
            })
          ]);
          this.logAction(`Found new notification page: ${confirmPage.url()}`);
        } catch (error) {
          this.logAction(`No new notification page found: ${error}`);
          await this.verifyLoginState().catch(() => {
            this.logAction('Login check unsuccessful, but continuing');
          });
          return;
        }
      }

      if (!confirmPage) {
        this.logAction('No Confirm popup page found, checking login');
        await this.verifyLoginState().catch(() => {
          this.logAction('Login check unsuccessful, but continuing');
        });
        return;
      }

      // Bring page to front
      try {
        if (!confirmPage.isClosed()) {
          await confirmPage.bringToFront();
        } else {
          this.logAction('Confirm page already closed');
          return;
        }
      } catch (e) {
        this.logAction(`Error bringing Confirm popup to front: ${e instanceof Error ? e.message : 'Unknown error'}`);
      }

      // Use the defined selector for Confirm button
      try {
        if (confirmPage.isClosed()) {
          this.logAction('Confirm page already closed');
          return;
        }

        this.logAction(`Checking for Confirm button with selector: ${DashboardSelectors.confirmPhantomBtn}`);
        const isVisible = await confirmPage.locator(DashboardSelectors.confirmPhantomBtn).isVisible({ timeout: 3000 }).catch(() => false);

        if (isVisible) {
          this.logAction(`Found Confirm button with selector: ${DashboardSelectors.confirmPhantomBtn}`);
          await confirmPage.locator(DashboardSelectors.confirmPhantomBtn).click();
          this.logAction('Successfully clicked Confirm button');

          // Wait for page to close
          try {
            await confirmPage.waitForEvent('close', { timeout: 5000 }).catch(() => {
              this.logAction('Timeout waiting for page to close, but continuing');
            });
          } catch (e) {
            // Ignore errors
          }
        } else {
          this.logAction(`Confirm button not found with selector: ${DashboardSelectors.confirmPhantomBtn}`);
        }
      } catch (e) {
        this.logAction(`Error interacting with Confirm button: ${e instanceof Error ? e.message : 'Unknown error'}`);
      }

      // Verify login regardless of button interaction
      await this.verifyLoginState().catch(() => {
        this.logAction('Login check unsuccessful, but continuing');
      });

    } catch (error) {
      this.logAction(`Error handling Confirm popup: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Try to verify login despite errors
      try {
        await this.verifyLoginState();
        this.logAction('Login successful despite Confirm popup handling issues');
        return;
      } catch (loginError) {
        this.logAction('Unable to verify login after popup error');
        // Don't throw error to allow test to continue
      }
    }
  }

  /**
   * Switch back to Dashboard page after popup handling
   */
  async switchToDashboardAfterPopup(): Promise<void> {
    try {
      this.logAction('Switching back to Dashboard after popup handling');
      await this.uiActions.getDashboardPage();
      this.logAction('Successfully switched back to Dashboard');
    } catch (error) {
      console.error(`Error switching to Dashboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
      // Don't throw error to avoid disrupting test flow
    }
  }

  /**
   * Clear processed notification pages cache
   */
  clearNotificationCache(): void {
    this.processedNotificationPages.clear();
  }
} 