import{a as ye}from"./chunk-RJRYVRTS.js";import{a as Bt}from"./chunk-XYFNIIUY.js";import{a as ge,b as fe,c as Ce}from"./chunk-O5AAGNHJ.js";import{b as ht}from"./chunk-SIDJ2NRC.js";import{j as ot}from"./chunk-AHRYSG4W.js";import{b as V}from"./chunk-S24UABH5.js";import{a as pt}from"./chunk-X3ESGVCB.js";import{a as m}from"./chunk-CCQRCL2K.js";import{h as q,m as xt}from"./chunk-75L54KUM.js";import{a as ue}from"./chunk-ROF5SDVA.js";import{a as pe}from"./chunk-4VDZJDFB.js";import{a as ce,b as me,h as de,j as z,k as et}from"./chunk-OKP6DFCI.js";import{c as re,o as i,rb as B,va as ae,xa as le}from"./chunk-WIQ4WVKX.js";import{W as A,X as ne,Y as se,b as Rt,m as te,n as ee,o as oe,x as ie}from"./chunk-F3RUX6TF.js";import{v as wt}from"./chunk-V5T43K7V.js";import{c as J}from"./chunk-MHOQBMVI.js";import{s as Qt}from"./chunk-7ZN4F6J4.js";import{Bb as bt,Ca as Yt,Cb as Zt,Ja as St,M as Xt,N as tt}from"./chunk-OUYKWOVO.js";import{h as Lt,i as Tt}from"./chunk-OYGO47TI.js";import{o as Jt}from"./chunk-SLQBAOEK.js";import{$d as R,L as Ct,P as O,Pa as Z,pe as yt}from"./chunk-MZZEJ42N.js";import{m as E}from"./chunk-56SJOU6P.js";import{S as dt}from"./chunk-ALUTR72U.js";import{ta as Dt}from"./chunk-L3A2KHJO.js";import{a as G}from"./chunk-7X4NV6OJ.js";import{f as W,h as b,n as w}from"./chunk-3KENBVE7.js";b();w();var $=W(G());var Ke=i(m)`
  background: #2a2a2a;
  padding: 12px 12px;
  border-radius: ${e=>e.borderRadius?e.borderRadius:"6px"};
`,Oe=i.div`
  margin-top: 10px;
`,kt=$.default.memo(({uri:e,collectibleName:t,collectionName:o,borderRadius:n})=>$.default.createElement(Ke,{borderRadius:n},$.default.createElement(pt,null,$.default.createElement(m,null,$.default.createElement(le,null),$.default.createElement(Oe,null,$.default.createElement(B,{weight:500,size:16,color:"#FFF",textAlign:"left"},t),$.default.createElement(B,{weight:400,size:16,color:"#999",textAlign:"left"},o))),e&&$.default.createElement(ye,{uri:e,width:75,height:75,borderRadius:"6px"}))));b();w();var C=W(G());b();w();var it=W(G());b();w();var r=W(G());b();w();var l=W(G());var Pt=e=>{let{collectible:t,action:o,actionType:n,actionError:p,transaction:a,errorTitle:S,successTitle:s,loadingTitle:u,errorMessage:c,successMessage:f,loadingMessage:F,marketplaceUrl:L,marketplace:N,onClose:D}=e,T=ot(),v=t.asset.name,h=re(),{closeAllModals:M}=V(),{popDetailView:H}=q(),{t:d}=E(),x=(0,l.useMemo)(()=>({viewTransactionText:d("listStatusViewTransaction"),retryText:d("commandRetry"),cancelText:d("commandCancel"),viewOnMEText:d("listStatusViewOnMagicEden"),dismissText:d("listStatusLoadingDismiss")}),[d]),{viewTransactionText:K,retryText:st,cancelText:rt,viewOnMEText:at,dismissText:At}=x,[ut,X]=(0,l.useState)("loading"),vt=ut==="error",he=ut==="loading",Mt=ut==="success",[k,ke]=(0,l.useState)(void 0),[Kt,Ot]=(0,l.useState)("needApproval"),[Pe,Ht]=(0,l.useState)(),{data:gt}=R(),{accountIdentifier:_t,chainAddresses:It,networkID:P,solanaPublicKey:ft,isLedger:lt,cluster:Vt,connection:$t}=(0,l.useMemo)(()=>{let g=gt?.identifier??"",_=gt?.type==="ledger",U=gt?.addresses??[],Y=U.find(Be=>Z.isSolanaNetworkID(Be.networkID)),j=Y?.networkID,Ne=Y?.address??"",qt=tt(j),De=Xt(qt);return{accountIdentifier:g,chainAddresses:U,networkID:j,solanaPublicKey:Ne,isLedger:_,cluster:qt,connection:De}},[gt]),Ut=A(g=>g.setTransactionURL),{mutateAsync:zt}=Zt($t),ct,mt;switch(n){case"list":ct="createListing",mt=d("transactionsPendingCreateListingInterpolated",{name:v??d("tokenRowUnknownToken")});break;case"edit":ct="editListing",mt=d("transactionsPendingEditListingInterpolated",{name:v??d("tokenRowUnknownToken")});break;case"remove":ct="removeListing",mt=d("transactionsPendingRemoveListingInterpolated",{name:v??d("tokenRowUnknownToken")});break}let Ee=vt?S:Mt?s:u,Ae=vt?c:Mt?f:F,ve=(0,l.useCallback)(()=>{L&&self.open(L)},[L]),{data:Ft}=Qt(),Me=(0,l.useCallback)(()=>{if(!P||!Ft||!k)return;let g=Ft.explorers[P],_=Jt({networkID:P,endpoint:"transaction",explorerType:g,param:k});Ut(_),self.open(_)},[P,Ft,Ut,k]),jt=(0,l.useCallback)(()=>{D(),h("/notifications")},[h,D]),Wt=(0,l.useCallback)(async()=>{if(!(!a||!P))try{let g={ownerAddress:ft,networkID:P,data:{signature:""},type:ct,display:{summary:{topLeft:{text:mt}}}},_=await zt({accountIdentifier:_t,feePayer:new O.PublicKey(ft),transaction:a,pendingTransactionInput:g}),U=Lt(P);U.chainType==="solana"&&T.submittedTransaction(_,{...U,method:"signAndSendTransaction"}),ke(_)}catch(g){Dt.captureError(g,"collectibles"),X("error"),ge(g)&&Ht(l.default.createElement(Ce,{ledgerActionError:g,onRetryClick:()=>{H()},onCancelClick:M}))}},[_t,P,M,mt,ct,H,zt,ft,a,T]);(0,l.useEffect)(()=>{a&&(lt?Ht(l.default.createElement(fe,{ledgerAction:Wt,cancel:M})):Wt())},[a]);let Gt=ie(It);(0,l.useEffect)(()=>{k&&(async()=>{try{Ot("mining"),await Yt({connection:$t,signature:k}),await te(t.asset.mintPubKey,Vt,N,k),n==="remove"&&(await ee(ft,t.asset.mintPubKey,Vt,N),await oe(It,t.asset.mintPubKey)),await Gt(),X("success"),Ie(n),lt&&(Ot("confirmed"),jt())}catch{X("error"),Nt(n)}})()},[k,It,Gt]);let Ie=(0,l.useCallback)(g=>{let U={list:"listCollectibleConfirmationSuccess",edit:"editCollectibleConfirmationSuccess",remove:"unlistCollectibleConfirmationSuccess"}[g],{mintPubKey:Y}=t.asset;if(J.capture(U,{data:{mint:Y}}),P&&k){let j=Lt(P);j.chainType==="solana"&&T.transactionStatus(k,{...j,status:{type:"confirmed"}})}},[t.asset,k,P,T]),Nt=(0,l.useCallback)(g=>{let U={list:"listCollectibleConfirmationError",edit:"editCollectibleConfirmationError",remove:"unlistCollectibleConfirmationError"}[g],{mintPubKey:Y}=t.asset;if(J.capture(U,{data:{mint:Y}}),P&&k){let j=Lt(P);j.chainType==="solana"&&T.transactionStatus(k,{...j,status:{type:"error"}})}},[t.asset,k,P,T]);(0,l.useEffect)(()=>{p&&X("error")},[p]);let Fe=(0,l.useCallback)(async()=>{try{X("loading"),await o()}catch(g){Dt.captureError(g,"collectibles"),X("error"),Nt(n)}},[o,Nt,n]);return(0,l.useEffect)(()=>{(!lt||lt&&Kt==="needApproval")&&Fe()},[]),{viewTransactionText:K,cancelText:rt,retryText:st,viewOnMEText:at,dismissText:At,status:ut,isError:vt,isLoading:he,isSuccess:Mt,title:Ee,message:Ae,txHash:k,isLedger:lt,step:Kt,ledgerContent:Pe,onTransactionLinkClick:Me,onMELinkClick:ve,onRetry:H,onCancel:H,onDismiss:jt}};var Et=r.default.memo(e=>{let{viewTransactionText:t,cancelText:o,retryText:n,viewOnMEText:p,dismissText:a,status:S,isError:s,isLoading:u,isSuccess:c,title:f,message:F,txHash:L,isLedger:N,step:D,ledgerContent:T,onTransactionLinkClick:v,onMELinkClick:h,onRetry:M,onCancel:H,onDismiss:d}=e,{t:x}=E(),K=null;return N&&D==="needApproval"?K=T:K=r.default.createElement(He,null,r.default.createElement(_e,null,r.default.createElement(me,{mode:"wait",initial:!0},r.default.createElement(ce.div,{key:S,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2}},s?r.default.createElement(Bt,{type:"failure"}):c?r.default.createElement(Bt,{type:"success"}):r.default.createElement(Ve,null,r.default.createElement(de,{diameter:54,color:"#AB9FF2",trackColor:"#181818"})))),r.default.createElement($e,null,f),F&&r.default.createElement(Ue,null,`${F} ${u&&L?x("listStatusLoadingSafelyDismiss"):""}`),L&&r.default.createElement(ze,{onClick:v},t)),s?r.default.createElement(et,{primaryText:n,onPrimaryClicked:M,secondaryText:o,onSecondaryClicked:H}):u&&L&&f!==x("removeListStatusLoadingTitle")?r.default.createElement(m,null,r.default.createElement(Le,{theme:"primary",disabled:!0},p),r.default.createElement(z,{onClick:d},a)):u&&L&&f===x("removeListStatusLoadingTitle")?r.default.createElement(pt,null,r.default.createElement(z,{onClick:d},a)):c&&f!==x("removeListStatusSuccessTitle")?r.default.createElement(m,null,r.default.createElement(Le,{theme:"primary",onClick:h},p),r.default.createElement(z,{onClick:d},a)):c&&f===x("removeListStatusSuccessTitle")?r.default.createElement(pt,null,r.default.createElement(z,{onClick:d},a)):null),r.default.createElement(r.default.Fragment,null,K)}),He=i(m).attrs({justify:"space-between",align:"center"})`
  height: 100%;
`,_e=i(m).attrs({align:"center",justify:"center"})`
  flex: 1;
  flex-grow: 1;
`,Ve=i(m).attrs({align:"center",justify:"center",margin:"0 0 15px 0"})`
  position: relative;
  border-radius: 50%;
  background-color: ${dt("#AB9FF2",.2)};
  box-shadow: 0 0 0 20px ${dt("#AB9FF2",.2)};
`,$e=i(B).attrs({size:28,lineHeight:33.89,weight:500,margin:"20px 0 10px 0"})``,Ue=i(B).attrs({size:16,lineHeight:19,weight:400,color:"#777777",margin:"0 0 10px 0"})`
  padding: 0px 20px;
`,ze=i(B).attrs({size:16,lineHeight:20.8,weight:500,color:"#AB9FF2"})``,Le=i(z)`
  margin-bottom: 10px;
`;var Te=()=>{let e=A(t=>t.listCollectible);return e?it.default.createElement(je,{listCollectible:e}):null},je=({listCollectible:e})=>{let{closeAllModals:t}=V(),{data:o}=R(),{solanaPublicKey:n,cluster:p}=(0,it.useMemo)(()=>{let x=o?.addresses.find(at=>Z.isSolanaNetworkID(at.networkID)),K=x?.networkID,st=new O.PublicKey(x?.address??""),rt=tt(K);return{chainId:K,solanaPublicKey:st,cluster:rt}},[o]),a=A(x=>x.listCollectiblePrice),S="magic_eden",s=`https://magiceden.io/item-details/${e.asset.mintPubKey}`,u=e.asset.name??null,{t:c}=E(),f=(0,it.useMemo)(()=>({errorTitle:c("listStatusErrorTitle"),successTitle:c("listStatusSuccessTitle"),loadingTitle:c("listStatusLoadingTitle"),errorMessage:c("listStatusErrorMessage",{name:u}),successMessage:c("listStatusSuccessMessage",{name:u,listCollectiblePrice:a}),loadingMessage:c("listStatusLoadingMessage",{name:u,listCollectiblePrice:a})}),[c,u,a]),{errorTitle:F,successTitle:L,loadingTitle:N,errorMessage:D,successMessage:T,loadingMessage:v}=f,{list:h,error:M,transaction:H}=se(),d=Pt({collectible:e,action:()=>h(p,n),actionType:"list",actionError:M,transaction:H,errorTitle:F,successTitle:L,loadingTitle:N,errorMessage:D,successMessage:T,loadingMessage:v,marketplaceUrl:s,marketplace:S,onClose:t});return e?it.default.createElement(Et,{...d}):null};var Ge=i(m).attrs({justify:"space-between"})`
  height: 100%;
`,qe=i(m).attrs({align:"center"})``,Je=i(et)`
  width: 100%;
`,Qe=i.div`
  width: 100%;
  overflow: hidden;
`,Xe=i(B).attrs({size:12,lineHeight:18,weight:400,textAlign:"center",color:"#999999",wordBreak:"break-word"})`
  padding: 0 48px;
  width: 100%;
`,Ye=i.div`
  border-bottom: 1px solid #222222;
  border-bottom-width: 1px;
`,be=({artistRoyalties:e,marketplaceFee:t})=>{let{t:o}=E(),n=ot(),{pushDetailView:p}=q(),{hideCollectibleListingModal:a}=V(),S=(0,C.useCallback)(()=>{a()},[a]),s=A(M=>M.listCollectible);if(!s)return null;let u=A(M=>M.listCollectiblePrice),{data:c}=yt("solana");wt(c,"CREATE_LISTING");let{cluster:f}=bt(),F=Ct(St),L=s.asset.collectibleImage,N=s.asset.name,D=s.asset.collectionName??null,T=(0,C.useCallback)(()=>{J.capture("listCollectibleCreateListing",{data:{mint:s.asset.mintPubKey}}),n.approved({chainId:Tt(f),type:"create",list:{type:"create",tokenId:s.asset.mintPubKey}}),u&&p(C.default.createElement(Te,null))},[s?.asset.mintPubKey,u,p,f,n]),v=()=>{self.open(Rt,"_blank")},h=[{label:o("collectiblesListPrice"),value:`${u} SOL`,color:"#21E56F",tooltipContent:o("collectiblesListPriceTooltip")},{label:o("collectiblesMagicEdenFee"),value:t===null?"\u2014":`${t}%`,tooltipContent:o("collectiblesMagicEdenFeeTooltip",{marketplaceFee:t})},{label:o("collectiblesArtistRoyalties"),value:e===null?"\u2014":`${e}%`,tooltipContent:o("collectiblesArtistRoyaltiesTooltip")},{label:o("sendConfirmationNetworkFee"),value:`${F} SOL`}];return C.default.createElement(Ge,null,C.default.createElement(qe,null,C.default.createElement(xt,null,o("collectiblesListOnMagicEden")),C.default.createElement(kt,{uri:L,collectibleName:N,collectionName:D,borderRadius:"6px 6px 0px 0px"}),C.default.createElement(Ye,null),C.default.createElement(Qe,null,C.default.createElement(ht,{rows:h,borderRadius:"0px 0px 6px 6px"}))),C.default.createElement(Xe,null,C.default.createElement(pe,{i18nKey:"collectiblesCreateListingTermsOfService"},"By tapping ",C.default.createElement("span",{style:{color:"#999999"}},'"List Now"')," you agree to Magic Eden's",C.default.createElement("label",{onClick:v,style:{color:"#8A81F7",cursor:"pointer"}},"Terms of Service"))),C.default.createElement(Je,{primaryText:o("collectiblesListNowButton"),secondaryText:o("commandCancel"),onPrimaryClicked:T,onSecondaryClicked:S}))};b();w();var I=W(G());b();w();var nt=W(G());var we=()=>{let e=A(t=>t.listCollectible);return e?nt.default.createElement(Ze,{listCollectible:e}):null},Ze=({listCollectible:e})=>{let{t}=E(),{closeAllModals:o}=V(),{data:n}=R(),{solanaPublicKey:p,cluster:a}=(0,nt.useMemo)(()=>{let K=(n?.addresses??[]).find(At=>Z.isSolanaNetworkID(At.networkID)),st=K?.networkID,rt=new O.PublicKey(K?.address??""),at=tt(st);return{solanaPublicKey:rt,cluster:at}},[n]),S=A(x=>x.editListCollectiblePrice),s="magic_eden",u=`https://magiceden.io/item-details/${e.asset.mintPubKey}`,c=e.asset.name,f=(0,nt.useMemo)(()=>({errorTitle:t("editListStatusErrorTitle"),successTitle:t("editListingStatusSuccessTitle"),loadingTitle:t("editListingStatusLoadingTitle"),errorMessage:t("listStatusErrorMessage",{name:c}),successMessage:t("editListingStatusSuccessMessage",{name:c,editListCollectiblePrice:S}),loadingMessage:t("editListingStatusLoadingMessage",{name:c,editListCollectiblePrice:S})}),[t,c,S]),{errorTitle:F,successTitle:L,loadingTitle:N,errorMessage:D,successMessage:T,loadingMessage:v}=f,{editList:h,error:M,transaction:H}=ne(),d=Pt({collectible:e,action:()=>h(a,p),actionType:"edit",actionError:M,transaction:H,errorTitle:F,successTitle:L,loadingTitle:N,errorMessage:D,successMessage:T,loadingMessage:v,marketplaceUrl:u,marketplace:s,onClose:o});return nt.default.createElement(Et,{...d})};var Re=i(m).attrs({justify:"space-between"})`
  height: 100%;
`,to=i(m).attrs({align:"center"})``,eo=i(et)`
  width: 100%;
`,oo=i.div`
  width: 100%;
  overflow: hidden;
`,io=i.div`
  border-bottom: 1px solid #222222;
  border-bottom-width: 1px;
`,xe=({artistRoyalties:e,marketplaceFee:t})=>{let{t:o}=E(),n=ot(),{pushDetailView:p}=q(),{hideEditCollectibleListingModal:a}=V(),S=(0,I.useCallback)(()=>{a()},[a]),s=A(h=>h.listCollectible);if(!s)return null;let u=A(h=>h.editListCollectiblePrice),{data:c}=yt("solana");wt(c,"EDIT_LISTING");let{cluster:f}=bt(),F=Ct(St),L=s.asset.collectibleImage,N=s.asset.name,D=s.asset.collectionName??null,T=(0,I.useCallback)(()=>{J.capture("listCollectibleEditListing",{data:{mint:s.asset.mintPubKey}}),n.approved({chainId:Tt(f),type:"update",list:{type:"update",tokenId:s.asset.mintPubKey}}),p(I.default.createElement(we,null))},[s.asset.mintPubKey,p,f,n]),v=[{label:o("collectiblesListPrice"),value:`${u} SOL`,color:"#21E56F"},{label:o("collectiblesMagicEdenFee"),value:t===null?"\u2014":`${t}%`},{label:o("collectiblesArtistRoyalties"),value:e===null?"\u2014":`${e}%`},{label:o("sendConfirmationNetworkFee"),value:`${F} SOL`}];return I.default.createElement(Re,null,I.default.createElement(to,null,I.default.createElement(xt,null,o("collectiblesEditListing")),I.default.createElement(kt,{uri:L,collectibleName:N,collectionName:D,borderRadius:"6px 6px 0px 0px"}),I.default.createElement(io,null),I.default.createElement(oo,null,I.default.createElement(ht,{rows:v,borderRadius:"0px 0px 6px 6px"}))),I.default.createElement(eo,{primaryText:o("collectiblesListNowButton"),secondaryText:o("commandCancel"),onPrimaryClicked:T,onSecondaryClicked:S}))};b();w();var y=W(G());var no=i(m).attrs({align:"center",justify:"space-between"})`
  height: 100%;
`,so=i(m).attrs({align:"center",justify:"center"})`
  height: 100%;
`,ro=i(ue)`
  svg {
    fill: #e5a221;
  }
`,ao=i(z)`
  margin-bottom: 10px;
`,_n=({artistRoyalties:e,marketplaceFee:t,isEditing:o=!1})=>{let{t:n}=E(),{pushDetailView:p,popDetailView:a}=q(),S=o?"EditListing":"CreateListing",s=(0,y.useCallback)(()=>{p(S==="EditListing"?y.default.createElement(xe,{artistRoyalties:e,marketplaceFee:t}):y.default.createElement(be,{artistRoyalties:e,marketplaceFee:t}))},[e,t,S,p]);return y.default.createElement(no,null,y.default.createElement(so,null,y.default.createElement(m,{align:"center",margin:"0 0 20px 0"},y.default.createElement(ro,{color:dt("#E5A221",.1),diameter:94},y.default.createElement(ae,{fill:"#E5A221"}))),y.default.createElement(m,{align:"center",margin:"0 0 10px 0"},y.default.createElement(B,{size:26,weight:500,lineHeight:34},n("collectiblesBelowFloorPrice"))),y.default.createElement(m,{align:"center",padding:"0 20px"},y.default.createElement(B,{size:16,color:"#777777",lineHeight:22},n("collectiblesBelowFloorPriceMessage")))),y.default.createElement(m,null,y.default.createElement(ao,{theme:"primary",onClick:s},n("collectiblesListAnywayButton")),y.default.createElement(z,{onClick:a},n("commandCancel"))))};export{kt as a,Pt as b,Et as c,be as d,xe as e,_n as f};
//# sourceMappingURL=chunk-S7TXLC7D.js.map
