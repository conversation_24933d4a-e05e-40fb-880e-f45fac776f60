import { type Page, expect } from "@playwright/test"
import UIActions from "../base/actions/UIActions"
import { CommonSelectors } from "../selectors/CommonSelectors"
import { SearchSelectorsXPath } from "../selectors/SearchSelectors"
import { ENV } from "../base/env"

/**
 * SearchPage - M<PERSON> hình Page Object cho chức năng tìm kiếm của Dex3
 */
export class SearchPage {
  private uiActions: UIActions;

  constructor(page: Page) {
    this.uiActions = new UIActions(page);
  }

  /**
   * Điều hướng đến trang chủ Dex3
   * @returns Promise<void>
   */
  async navigateToDex3(): Promise<void> {
    try {
      console.log(`Điều hướng đến trang chủ Dex3: ${ENV.BASE_URL}`);
      await this.uiActions.goto(ENV.BASE_URL, "Dex3 Homepage");
      await this.uiActions.waitForLoadState();
      console.log("Đã điều hướng đến trang chủ Dex3 thành công");
    } catch (error) {
      console.error(`Không thể điều hướng đến trang chủ Dex3: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      throw error;
    }
  }

  /**
   * Mở popup tìm kiếm
   * @returns Promise<void>
   */
  async openSearchPopup(): Promise<void> {
    try {
      console.log("Mở popup tìm kiếm...");
      await this.uiActions.element(CommonSelectors.searchButton, "Search Button").click();
      await this.uiActions.waitForElement(SearchSelectorsXPath.searchPopup, "Search Popup", 10000);
      console.log("Đã mở popup tìm kiếm thành công");
    } catch (error) {
      console.error(`Không thể mở popup tìm kiếm: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      throw error;
    }
  }

  /**
   * Kiểm tra xem popup tìm kiếm có hiển thị không
   * @returns Promise<boolean>
   */
  async isSearchPopupVisible(): Promise<boolean> {
    try {
      const page = this.uiActions.getPage();
      const isVisible = await page.locator(SearchSelectorsXPath.searchPopup).isVisible();
      console.log(`Popup tìm kiếm ${isVisible ? "đang hiển thị" : "không hiển thị"}`);
      return isVisible;
    } catch (error) {
      console.error(`Lỗi khi kiểm tra popup tìm kiếm: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return false;
    }
  }

  /**
   * Nhập từ khóa tìm kiếm
   * @param keyword Từ khóa tìm kiếm
   * @returns Promise<void>
   */
  async search(keyword: string): Promise<void> {
    try {
      console.log(`Tìm kiếm với từ khóa: "${keyword}"`);
      await this.uiActions.editBox(SearchSelectorsXPath.searchInput, "Search Input").fill(keyword);
      // Đợi một chút để kết quả tìm kiếm hiển thị
      await this.uiActions.pauseInSecs(1);
      console.log("Đã nhập từ khóa tìm kiếm");
    } catch (error) {
      console.error(`Không thể nhập từ khóa tìm kiếm: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      throw error;
    }
  }

  /**
   * Đóng popup tìm kiếm bằng cách click ra ngoài popup
   * @returns Promise<void>
   */
  async closeSearchPopup(): Promise<void> {
    try {
      console.log("Đóng popup tìm kiếm bằng cách click ra ngoài popup...");

      // Kiểm tra xem popup có hiển thị không
      const page = this.uiActions.getPage();
      const isVisible = await page.locator(SearchSelectorsXPath.searchPopup).isVisible();

      if (isVisible) {
        // Click vào một vị trí ngoài popup (góc trái trên của trang)
        await page.mouse.click(10, 10);

        // Đợi popup biến mất
        await page.waitForSelector(SearchSelectorsXPath.searchPopup, { state: 'hidden', timeout: 5000 });
        console.log("Đã đóng popup tìm kiếm bằng cách click ra ngoài");
      } else {
        console.log("Popup tìm kiếm không hiển thị, không cần đóng");
      }
    } catch (error) {
      console.error(`Không thể đóng popup tìm kiếm: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);

      // Nếu không thể đóng bằng cách click ra ngoài, thử dùng nút đóng
      try {
        console.log("Thử đóng bằng nút đóng...");
        await this.uiActions.element(SearchSelectorsXPath.searchCloseButton, "Close Button").click();
        await this.uiActions.getPage().waitForSelector(SearchSelectorsXPath.searchPopup, { state: 'hidden', timeout: 5000 });
        console.log("Đã đóng popup tìm kiếm bằng nút đóng");
      } catch (secondError) {
        console.error(`Không thể đóng popup tìm kiếm bằng cả hai cách: ${secondError instanceof Error ? secondError.message : "Lỗi không xác định"}`);
        throw secondError;
      }
    }
  }

  /**
   * Lấy số lượng kết quả tìm kiếm
   * @returns Promise<number>
   */
  async getSearchResultsCount(): Promise<number> {
    try {
      const page = this.uiActions.getPage();

      try {
        await page.waitForSelector(SearchSelectorsXPath.searchResults, { 
          state: 'visible', 
          timeout: 10000
        });
      } catch (error) {
        console.log("Timeout khi đợi kếết quả tìm kiếm xuất hiện");
      }

      const count = await page.locator(SearchSelectorsXPath.searchResults).count();
      console.log(`Số lượng kết quả tìm kiếm: ${count}`);
      return count;
    } catch (error) {
      console.error(`Lỗi khi đếm kết quả tìm kiếm: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return 0;
    }
  }

  /**
   * Kiểm tra xem có phần Trending Tokens và hiển thị đúng 3 token không
   * @returns Promise<boolean>
   */
  async hasTrendingTokensSection(): Promise<boolean> {
    try {
      const page = this.uiActions.getPage();

      // Đợi popup tìm kiếm load hoàn tất
      console.log("Đợi popup tìm kiếm load hoàn tất...");
      await page.waitForSelector(SearchSelectorsXPath.searchPopup, { state: 'visible', timeout: 10000 });
      await page.waitForLoadState('domcontentloaded', { timeout: 5000 }).catch(() => {
        console.log("Timeout khi đợi networkidle, nhưng vẫn tiếp tục");
      });

      // Kiểm tra xem phần Trending Tokens có hiển thị không
      const isSectionVisible = await page.locator(SearchSelectorsXPath.trendingTokensSection).isVisible();
      if (!isSectionVisible) {
        console.log(`Phần Trending Tokens không hiển thị`);
        return false;
      }
      
      // Đợi ít nhất một token trending xuất hiện (với timeout 5s)
      console.log("Đợi các token trending xuất hiện...");
      try {
        await page.waitForSelector(SearchSelectorsXPath.trendingTokenItems, { 
          state: 'visible', 
          timeout: 5000 
        });
      } catch (error) {
        console.log("Timeout khi đợi token trending xuất hiện, nhưng vẫn tiếp tục");
      }

      // Đếm số lượng token trong phần Trending Tokens
      const tokenCount = await page.locator(SearchSelectorsXPath.trendingTokenItems).count();

      // Kiểm tra xem có đúng 3 token không
      const hasExactlyThreeTokens = tokenCount === 3;

      console.log(`Phần Trending Tokens có hiển thị với ${tokenCount} token. ${hasExactlyThreeTokens ? 'Đúng 3 token' : `${tokenCount < 3 ? 'Chưa đủ 3 token' : 'Nhiều hơn 3 token'}`}`);

      // Lấy tên của các token để hiển thị trong log
      if (tokenCount > 0) {
        const tokenElements = page.locator(SearchSelectorsXPath.trendingTokenItems);
        for (let i = 0; i < Math.min(tokenCount, 5); i++) { // Chỉ hiển thị tối đa 5 token để tránh log quá dài
          const tokenElement = tokenElements.nth(i);
          const tokenName = await tokenElement.locator('p').first().textContent() || 'Unknown';
          console.log(`  - Token ${i+1}: ${tokenName}`);
        }
      }

      return hasExactlyThreeTokens;
    } catch (error) {
      console.error(`Lỗi khi kiểm tra phần Trending Tokens: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return false;
    }
  }

  /**
   * Kiểm tra xem có phần Top Wallets và có ít nhất 1 wallet hiển thị không
   * @returns Promise<boolean>
   */
  async hasTopWalletsSection(): Promise<boolean> {
    try {
      const page = this.uiActions.getPage();

      // Kiểm tra xem phần Top Wallets có hiển thị không
      const isSectionVisible = await page.locator(SearchSelectorsXPath.topWalletsSection).isVisible();
      if (!isSectionVisible) {
        console.log(`Phần Top Wallets không hiển thị`);
        return false;
      }

      // Đợi một chút để các wallet được tải
      await page.waitForTimeout(1000);

      // Đếm số lượng wallet trong phần Top Wallets
      const walletCount = await page.locator(SearchSelectorsXPath.topWalletItems).count();

      // Kiểm tra xem có ít nhất 1 wallet không
      const hasAtLeastOneWallet = walletCount >= 1;

      console.log(`Phần Top Wallets có hiển thị với ${walletCount} wallet. ${hasAtLeastOneWallet ? 'Có ít nhất 1 wallet' : 'Không có wallet nào'}`);

      // Lấy thông tin của các wallet để hiển thị trong log
      if (walletCount > 0) {
        const walletElements = page.locator(SearchSelectorsXPath.topWalletItems);
        for (let i = 0; i < Math.min(walletCount, 3); i++) { // Chỉ hiển thị tối đa 3 wallet để tránh log quá dài
          const walletElement = walletElements.nth(i);
          const walletName = await walletElement.locator('p').first().textContent() || 'Unknown';
          console.log(`  - Wallet ${i+1}: ${walletName}`);
        }
      }

      return hasAtLeastOneWallet;
    } catch (error) {
      console.error(`Lỗi khi kiểm tra phần Top Wallets: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return false;
    }
  }

  /**
   * Kiểm tra xem có thông báo không tìm thấy kết quả không
   * @returns Promise<boolean>
   */
  async hasNoResultsMessage(): Promise<boolean> {
    try {
      const page = this.uiActions.getPage();
      await page.waitForSelector(SearchSelectorsXPath.noDataMessage, { 
        state: 'visible', 
        timeout: 10000 
      });
      const isVisible = await page.locator(SearchSelectorsXPath.noDataMessage).isVisible();
      console.log(`Thông báo không tìm thấy kết quả ${isVisible ? "có hiển thị" : "không hiển thị"}`);
      return isVisible;
    } catch (error) {
      console.error(`Lỗi khi kiểm tra thông báo không tìm thấy kết quả: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      return false;
    }
  }

  /**
   * Click vào kết quả tìm kiếm đầu tiên
   * @returns Promise<void>
   */
  async clickFirstSearchResult(): Promise<void> {
    try {
      console.log("Click vào kết quả tìm kiếm đầu tiên...");
      const page = this.uiActions.getPage();
      await page.locator(SearchSelectorsXPath.searchResults).first().click();
      await this.uiActions.waitForLoadState();
      console.log("Đã click vào kết quả tìm kiếm đầu tiên");
    } catch (error) {
      console.error(`Không thể click vào kết quả tìm kiếm đầu tiên: ${error instanceof Error ? error.message : "Lỗi không xác định"}`);
      throw error;
    }
  }
}
