import { test as base, expect, Browser, BrowserContext, Page } from '@playwright/test';
import { DashboardPage } from '../pages/DashboardPage';
import { TokenDetailPage } from '../pages/TokenDetailPage';
import { SearchPage } from '../pages/SearchPage';
import { ENV } from './env';
import * as path from 'path';

/**
 * Định nghĩa các trạng thái fixture được sử dụng trong test
 */
type TestFixtures = {
  context: BrowserContext;
  page: Page;
  dashboardPage: DashboardPage;
  tokenDetailPage: TokenDetailPage;
  searchPage: SearchPage;
};

/**
 * Mở rộng lớp test của Playwright để cung cấp các fixture tùy chỉnh
 * - context: BrowserContext đã được khởi tạo với extension Phantom
 * - page: Trang hiện tại
 * - dashboardPage: Instance của DashboardPage đã khởi tạo
 */
export const test = base.extend<TestFixtures>({
  // Tạo context với Phantom extension đã được thiết lập
  context: async ({ browser }, use) => {
    console.log('Khởi tạo context với Phantom extension...');

    // Sử dụng lại trạng thái đăng nhập từ global setup
    const storageStatePath = path.join(__dirname, '../../storageState.json');

    // Tạo một context mới với extension Phantom
    const context = await browser.newContext({
      storageState: storageStatePath,
      viewport: { width: 1280, height: 720 }
    });

    // Áp dụng các cài đặt chung cho tất cả các trang
    context.setDefaultTimeout(60000); // Tăng thời gian chờ lên 60 giây

    await use(context);
    await context.close();
    console.log('Đã đóng context');
  },

  // Tạo một trang mới từ context đã thiết lập
  page: async ({ context }, use) => {
    console.log('Tạo trang mới...');
    const page = await context.newPage();
    await use(page);
  },

  // Tạo và khởi tạo DashboardPage
  dashboardPage: async ({ page }, use) => {
    console.log('Khởi tạo DashboardPage...');
    const dashboardPage = new DashboardPage(page);

    // Chuyển đến trang Dex3
    await page.goto(ENV.BASE_URL);
    await page.waitForLoadState('domcontentloaded');
    // Kiểm tra nếu chưa đăng nhập, thì thực hiện đăng nhập
    const isLoggedIn = await checkIfLoggedIn(page);
    if (!isLoggedIn) {
      console.log('Chưa đăng nhập, tiến hành đăng nhập...');
      await dashboardPage.loginDex3();
    } else {
      console.log('Đã đăng nhập, tiếp tục test...');
    }

    await use(dashboardPage);
  },

  // Tạo và khởi tạo TokenDetailPage
  tokenDetailPage: async ({ page }, use) => {
    console.log('Khởi tạo TokenDetailPage...');
    const tokenDetailPage = new TokenDetailPage(page);
    await use(tokenDetailPage);
  },

  // Tạo và khởi tạo SearchPage
  searchPage: async ({ page }, use) => {
    console.log('Khởi tạo SearchPage...');
    const searchPage = new SearchPage(page);
    await use(searchPage);
  },
});

/**
 * Kiểm tra xem đã đăng nhập vào Dex3 chưa
 */
async function checkIfLoggedIn(page: Page): Promise<boolean> {
  try {
    // Đợi trang load hoàn tất
    await page.waitForLoadState('networkidle');
    // Kiểm tra nút menu (SOL) có hiển thị không
    const menuButton = page.locator("//button[contains(., 'SOL')]");
    await page.waitForSelector("//button[contains(., 'SOL')]", { timeout: 10000 });
    await page.waitForTimeout(2000);
    return await menuButton.isVisible({ timeout: 5000 });
  } catch (error) {
    return false;
  }
}

// Re-export expect để sử dụng trong test
export { expect };
