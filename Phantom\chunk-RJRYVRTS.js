import{a}from"./chunk-AHRYSG4W.js";import{a as d}from"./chunk-QEXGR5WT.js";import{ia as l,o}from"./chunk-WIQ4WVKX.js";import{b as t}from"./chunk-MNXYIK2W.js";import{a as u}from"./chunk-7X4NV6OJ.js";import{f as p,h as n,n as m}from"./chunk-3KENBVE7.js";n();m();var e=p(u());var b=o.div`
  border-radius: 8px;
  flex: 1;
  align-items: center;
  justify-content: center;
  padding: 8px;
`,c=o(a)`
  max-width: 100%;
  max-height: 100%;
`,I=o.div`
  flex-basis: 75px;
`,y=e.default.memo(s=>{let{uri:g,width:r,height:i,borderRadius:h}=s;return e.default.createElement(e.default.Fragment,null,e.default.createElement(c,{src:g,fallback:e.default.createElement(b,null,e.default.createElement(l,null)),width:r||t,height:i||t,loader:e.default.createElement(I,null,e.default.createElement(d,{width:"75px",height:"75px",borderRadius:"8px",backgroundColor:"#434343"})),style:{borderRadius:h,...r?{width:r}:null,...i?{height:i}:null},hidden:status==="loading"}))});export{y as a};
//# sourceMappingURL=chunk-RJRYVRTS.js.map
