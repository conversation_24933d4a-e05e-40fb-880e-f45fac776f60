import{Ma as g}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as h}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as i}from"./chunk-CCQRCL2K.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{j as c}from"./chunk-OKP6DFCI.js";import{o as r,rb as o,va as m}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as l}from"./chunk-56SJOU6P.js";import{S as n}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as w}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as p,h as a,n as s}from"./chunk-3KENBVE7.js";a();s();var t=p(w());var C=r(i).attrs({align:"center",justify:"space-between"})`
  height: 100%;
`,d=r(i).attrs({align:"center",justify:"center"})`
  height: 100%;
`,k=r.div`
  width: 94px;
  height: 94px;
  position: relative;
  border-radius: 100%;
  background-color: ${e=>{switch(e.variant){case"warning":return n("#E5A221",.1);case"danger":return n("#EB3742",.1);default:return n("#E5A221",.1)}}};
`,x=r(h).attrs({align:"center",justify:"center"})`
  height: 100%;
`,H=r(o).attrs({size:28,weight:500,margin:"30px 0 10px 0",lineHeight:33})``,v=r(o).attrs({size:16,weight:400,color:"#999999",margin:"0 30px"})``,T=t.default.memo(e=>{let{t:f}=l(),{handleHideModalVisibility:u}=g();return t.default.createElement(C,null,t.default.createElement(d,null,t.default.createElement(k,{variant:e.variant},t.default.createElement(x,null,t.default.createElement(m,{fill:e.variant==="danger"?"#EB3742":"#E5A221"}))),t.default.createElement(H,null,e.title),t.default.createElement(v,null,e.message)),t.default.createElement(c,{onClick:()=>u("networkHealth")},f("commandClose")))}),O=T;export{T as NetworkHealth,O as default};
//# sourceMappingURL=NetworkHealth-RS54C5MZ.js.map
