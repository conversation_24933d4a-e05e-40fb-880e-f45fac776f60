import { chromium, type BrowserContext, type Page } from "@playwright/test"
import { DashboardPage } from "../pages/DashboardPage"
import { ENV } from "./env"

export class BaseTests {
  private browser!: BrowserContext
  // Thay đổi từ protected sang public
  public _page!: Page
  protected dashboardPage!: DashboardPage
  private browserReady: boolean = false

  // Đ<PERSON><PERSON> bảo getter là public
  public get page(): Page {
    return this._page
  }

  async setUp(): Promise<void> {
    // Kiểm tra extension path có tồn tại
    console.log("Using Phantom extension path:", ENV.PHANTOM_EXTENSION_PATH)

    this.browser = await chromium.launchPersistentContext("", {
      headless: false,
      devtools: false,
      args: [
        `--disable-extensions-except=${ENV.PHANTOM_EXTENSION_PATH}`,
        `--load-extension=${ENV.PHANTOM_EXTENSION_PATH}`,
      ],
    })
    this.browserReady = true;

    // Đ<PERSON><PERSON> một chút để đảm bảo extension được load đầy đủ
    console.log("Waiting for extension to fully load...")
    await this.sleep(3000)

    const pages = this.browser.pages()
    console.log(`Found ${pages.length} pages after browser initialization`)

    // Chỉ khởi tạo trang, nhưng không điều hướng đến dex3.ai
    this._page = pages.length > 0 ? pages[0] : await this.browser.newPage()
    console.log("Browser setup completed. Will navigate to DEX3 later after wallet import.")
  }

  // Phương thức mới để mở trang DEX3
  async openDex3Page(): Promise<void> {
    if (!this.browserReady || !this.browser) {
      throw new Error("Browser context is not available. Did you call setUp() first?")
    }

    try {
      // Lấy danh sách trang hiện tại
      const currentPages = this.browser.pages()

      // Kiểm tra xem trang DEX3 đã tồn tại chưa
      const existingDex3Page = currentPages.find(page =>
        page.url().includes("dex3.ai") || page.url().includes("cashdrop.click")
      )

      if (existingDex3Page) {
        console.log("Found existing DEX3 page:", existingDex3Page.url())
        this._page = existingDex3Page
        await this._page.bringToFront()
        return
      }

      // Nếu trang hiện tại đang ở extension, tạo trang mới
      if (this._page && this._page.url().startsWith("chrome-extension://")) {
        console.log("Current page is extension. Creating a new page for DEX3.")
        this._page = await this.browser.newPage()
      }

      console.log("Navigating to:", ENV.BASE_URL)
      await this._page.goto(ENV.BASE_URL, { waitUntil: 'domcontentloaded' })

      // Thay thế waitForLoadState vì đang gây timeout
      console.log("Waiting for page to be ready...")
      try {
        // Thay vì đợi networkidle (có thể không bao giờ kết thúc), chỉ đợi một khoảng thời gian
        await this.sleep(5000)
        console.log("Page loaded. Current URL:", this._page.url())
      } catch (error) {
        console.error("Error during page load:", error)
        // Tiếp tục dù có lỗi
        console.log("Continuing despite load error. Current URL:", this._page.url())
      }
    } catch (error) {
      console.error("Error opening DEX3 page:", error)
      throw error
    }
  }

  async switchToPhantom(): Promise<Page> {
    if (!this.browserReady || !this.browser) {
      throw new Error("Browser context is not available. Did you call setUp() first?")
    }

    console.log("Waiting for Phantom Wallet page to open...")
    try {
      // Cleanup trước khi thử để đảm bảo không có trang nào cản trở
      await this.cleanupBeforeRetry();

      // Kiểm tra xem đã có trang Phantom onboarding chưa
      const existingPages = this.browser.pages();
      const phantomPage = existingPages.find(page =>
        page.url().startsWith("chrome-extension://") && page.url().includes("/onboarding")
      );

      if (phantomPage) {
        console.log("Using existing Phantom Wallet page:", phantomPage.url());
        this._page = phantomPage;
        await this._page.bringToFront();

        // Đợi thêm để đảm bảo trang đã load hoàn toàn
        await this._page.waitForLoadState('domcontentloaded', { timeout: 10000 }).catch(e => {
          console.log("Error waiting for page load state:", e);
        });

        console.log("Phantom page ready");
        return this._page;
      }

      console.log("Current browser context has", this.browser.pages().length, "pages");
      console.log("Current pages:", this.browser.pages().map(p => p.url()).join(", "));

      // Đảm bảo trang hiện tại được focus
      await this._page.bringToFront();

      // Không tìm thấy trang Phantom hiện có, đợi trang mới
      console.log("Waiting for new Phantom Wallet page to open...");
      const newPhantomPage = await this.browser.waitForEvent("page", { timeout: 30000 });
      console.log("New page detected:", newPhantomPage.url());

      if (newPhantomPage.url().startsWith("chrome-extension://") && newPhantomPage.url().includes("/onboarding")) {
        console.log("Phantom Wallet onboarding page detected:", newPhantomPage.url());
        this._page = newPhantomPage;
        await this._page.bringToFront(); // Chuyển sang tab Phantom Wallet

        // Đợi thêm để đảm bảo trang đã load hoàn toàn
        await newPhantomPage.waitForLoadState('domcontentloaded', { timeout: 10000 }).catch(e => {
          console.log("Error waiting for page load state:", e);
        });

        console.log("Phantom page fully loaded");
        return this._page;
      }

      console.log("Page detected but not matching Phantom criteria:", newPhantomPage.url());
      console.log("Page URL needs to start with chrome-extension:// and include /onboarding");
      throw new Error("Phantom Wallet onboarding page not found.");
    } catch (error) {
      if (error instanceof Error) {
        console.error("Error while switching to Phantom Wallet:", error.message);
      } else {
        console.error("An unknown error occurred while switching to Phantom Wallet:", error);
      }

      // Log thông tin về extension path để debug
      console.log("Extension path being used:", ENV.PHANTOM_EXTENSION_PATH);
      console.log("Current pages after error:", this.browser.pages().map(p => p.url()).join(", "));

      throw new Error("Phantom Wallet page not found.");
    }
  }

  // Phương thức để dọn dẹp trước khi thử lại
  async cleanupBeforeRetry(): Promise<void> {
    if (!this.browserReady || !this.browser) {
      console.log("Browser context not available during cleanup");
      return;
    }

    console.log("Cleaning up before retry...");
    try {
      const pages = this.browser.pages();

      // Kiểm tra xem đã có Phantom onboarding page chưa
      const phantomOnboardingPage = pages.find(page =>
        page.url().startsWith("chrome-extension://") && page.url().includes("/onboarding")
      );

      if (phantomOnboardingPage) {
        console.log("Found existing Phantom onboarding page:", phantomOnboardingPage.url());
        console.log("Will use this page instead of waiting for a new one");

        try {
          // Focus vào Phantom page
          await phantomOnboardingPage.bringToFront();
          this._page = phantomOnboardingPage;
          console.log("Successfully switched to Phantom page");

          // Không đóng các trang khác vì có thể vẫn cần dùng
          return;
        } catch (error) {
          console.log("Error switching to Phantom page:", error);
          // Tiếp tục với flow bình thường nếu không thể switch
        }
      }

      // Nếu không có Phantom page, đóng các trang không phải trang chính
      let mainPageFound = false;

      for (const page of pages) {
        try {
          if (page.isClosed()) continue;

          const url = page.url();
          // Giữ lại trang chính (dex3.ai hoặc cashdrop.click)
          if (url.includes("dex3.ai") || url.includes("cashdrop.click")) {
            mainPageFound = true;
            this._page = page; // Đặt trang chính làm trang hiện tại
            console.log("Using main page:", url);
            continue;
          }

          // Đóng các trang khác
          if (page !== this._page) {
            console.log("Closing page:", url);
            await page.close();
          }
        } catch (e) {
          console.log("Error handling page during cleanup:", e);
        }
      }

      // Nếu không tìm thấy trang chính, tạo mới
      if (!mainPageFound || this._page.isClosed()) {
        console.log("No main page found, creating a new one");
        this._page = await this.browser.newPage();
        await this._page.goto(ENV.BASE_URL, { waitUntil: 'domcontentloaded' });
        console.log("Created new main page:", this._page.url());
      }
    } catch (error) {
      console.log("Error during cleanup:", error);
      // Không throw error ở đây để tránh làm hỏng test flow
    }

    // Chờ một chút sau khi đóng
    await this.sleep(1000);
    console.log("Cleanup completed.");
  }

  async switchToDashboardPage(): Promise<DashboardPage> {
    if (!this.browserReady || !this.browser) {
      throw new Error("Browser context is not available. Did you call setUp() first?")
    }

    console.log("Switching to Dashboard page...")
    const dashboardPage = this.browser.pages().find((page) =>
      ["cashdrop.click", "dex3.ai"].some(domain => page.url().includes(domain))
    );

    if (!dashboardPage) {
      console.log("Dashboard page not found. Opening a new one...")
      if (this._page && this._page.url().startsWith("chrome-extension://")) {
        // Nếu đang ở trang extension, tạo trang mới
        this._page = await this.browser.newPage();
      }
      await this.openDex3Page();
    } else {
      this._page = dashboardPage;
      console.log("Reusing existing Dashboard page:", this._page.url());
      // Refresh trang để đảm bảo trạng thái mới nhất
      await this._page.reload({ waitUntil: 'domcontentloaded' });
      await this.sleep(3000);
    }

    this.dashboardPage = new DashboardPage(this._page);
    return this.dashboardPage;
  }

  public getDashboardPage(): DashboardPage {
    if (!this.dashboardPage) {
      if (!this._page) {
        throw new Error("Page is not initialized. Did you call setUp()?")
      }
      this.dashboardPage = new DashboardPage(this._page)
    }
    return this.dashboardPage
  }

  // Thêm phương thức mới để lấy page hiện tại
  public getCurrentPage(): Page {
    if (!this._page) {
      throw new Error("Page is not initialized. Did you call setUp()?")
    }
    return this._page
  }

  async tearDown(): Promise<void> {
    console.log("Tearing down test environment...");
    try {
      // Thêm độ trễ ngắn trước khi đóng - đôi khi cần thiết để các tác vụ xử lý bất đồng bộ hoàn tất
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (this._page && !this._page.isClosed()) {
        console.log("Closing page:", this._page.url());
        await this._page.close().catch(e => console.error("Error closing page:", e));
      } else {
        console.log("Page already closed or not initialized");
      }

      if (this.browserReady && this.browser) {
        console.log("Closing browser context...");
        await this.browser.close().catch(e => console.error("Error closing browser context:", e));
        this.browserReady = false;
      } else {
        console.log("Browser context already closed or not initialized");
      }
    } catch (error) {
      console.error("Error during teardown:", error);
    }
    console.log("Test environment torn down.");
  }

  async sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }
}

