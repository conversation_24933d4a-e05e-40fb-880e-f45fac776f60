import{k as T}from"./chunk-7ZN4F6J4.js";import{u as R}from"./chunk-SLQBAOEK.js";import{$d as u,Kd as A,Q as y,T as Q,cb as h}from"./chunk-MZZEJ42N.js";import{a as x}from"./chunk-56SJOU6P.js";import{l as L}from"./chunk-ALUTR72U.js";import{G as k,I as M,J as U,ba as D,ia as B,ka as g,la as F,ta as S}from"./chunk-L3A2KHJO.js";import{h as s,n as r}from"./chunk-3KENBVE7.js";s();r();s();r();var m="@phantom/quests",z=`${m}:quests`,w={quests(t){return[`${m}:quests`,t]},questStatus(t){return[`${m}:questStatus`,t]},showQuests(t,e,n,o,a){return[`${m}:showQuests`,t,e,n,o,a]}};s();r();var E=class extends Error{constructor(e){super(e),this.name="ClaimQuestRewardError"}},C=class extends Error{constructor(e){super(e),this.name="ClaimQuestRewardBadRequestError"}},_=class extends Error{constructor(e){super(e),this.name="CheckQuestStatusError"}};s();r();var I=t=>{let e=new Set,n=[];for(let o of t)switch(o.type){case"seed":case"seedless":{e.has(o.seedIdentifier)||(e.add(o.seedIdentifier),n.push({type:"ROOT_ID",id:o.seedIdentifier}));break}case"privateKey":case"ledger":case"seedVault":{e.has(o.identifier)||(e.add(o.identifier),n.push({type:"ACCOUNT_ID",id:o.identifier}));break}case"readOnly":break;default:L(o)}return n};var K=new E("Claiming Reward Failed"),W=()=>{let t=B(),{data:e}=u(),{data:n=[]}=A(),o=R(x.language)??"en";return F({mutationFn:async({questId:c,networkIds:d})=>{try{let p=d.flat(),q=e?.addresses.find(f=>p.includes(f.networkID)),X=e?.addresses.map(h)??[],j=I(n),H=y.api().post("/quests/v1/claim_reward",{questId:c,locale:o,identifiers:j,selectedAccountAddresses:X,walletAddress:{chainId:q?.networkID,resourceType:"address",address:q?.address}}),[O]=await Promise.all([H,new Promise(f=>setTimeout(f,2e3))]);if(!Q(O)){let f=O.data;throw new C(JSON.stringify(f))}let b=O.data;if(b.status!=="reward_granted")throw K;return b}catch(p){throw p instanceof C&&S.captureError(p,"quest"),K}},onSuccess:()=>{t.invalidateQueries({queryKey:[`${m}:quests`]})}})};s();r();s();r();s();r();var P=async t=>{try{let e=await y.api().post("/quests/v1",t);if(!Q(e))throw new Error("Failed to fetch quests");return e.data.quests}catch(e){return S.addBreadcrumb("quest",`Error when fetching quests: ${e?.message??"Error without message"}`,"info"),[]}};s();r();s();r();s();r();var V=Date.now()/1e3;var Y=t=>V>t.expirationUnixTimeInSeconds;s();r();var $=(t,e)=>{let n=e?.addresses.map(o=>o.networkID)??[];for(let o of Object.values(t.networkIds)){if(Array.isArray(o))return o.every(a=>n.includes(a));if(n.includes(o))return!0}return!1};var J=(t,e)=>t.filter(n=>!Y(n)&&$(n,e));var N=()=>{let{data:t}=u(),{data:e=[]}=A(),{data:n,isLoading:o}=T(),a=R(x.language)??"en",l=t?.addresses.map(h)??[],c=I(e),d={appVersion:k,locale:a,platform:M,identifiers:c,selectedAccountAddresses:l,isOptedOut:!!n},p=()=>P(d);return g({queryKey:w.quests(d),enabled:e.length>0&&t&&t.type!=="readOnly"&&!o,queryFn:p,select:q=>J(q,t),refetchOnMount:U?"always":!0})};var Z=()=>{let{data:t,isLoading:e}=u(),{data:n,isLoading:o}=T(),a=t?.isReadOnly,l=D.isFeatureEnabled("kill-quests"),{error:c}=N(),d=()=>!l&&!n&&!a&&!c;return g({queryKey:w.showQuests(t?.addresses[0]?.address??"",!!n,!!a,l,c),enabled:!e&&!o,queryFn:d})};s();r();s();r();var Tt=new _("Checking quest status failed");s();r();export{z as a,W as b,N as c,Z as d};
//# sourceMappingURL=chunk-QALJXKGR.js.map
