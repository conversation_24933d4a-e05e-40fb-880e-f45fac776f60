{"commandAdd": "<PERSON><PERSON>.", "commandAccept": "Aceitar", "commandApply": "Aplicar", "commandApprove": "<PERSON><PERSON><PERSON>", "commandAllow": "<PERSON><PERSON><PERSON>", "commandBack": "Voltar", "commandBuy": "Compra", "commandCancel": "<PERSON><PERSON><PERSON>", "commandClaim": "Resgatar", "commandClaimReward": "Resgate a sua recompensa", "commandClear": "Limpar", "commandClose": "<PERSON><PERSON><PERSON>", "commandConfirm": "Confirmar", "commandConnect": "Ligar", "commandContinue": "<PERSON><PERSON><PERSON><PERSON>", "commandConvert": "Converter", "commandCopy": "Copiar", "commandCopyAddress": "<PERSON><PERSON><PERSON>", "commandCopyTokenAddress": "Copiar endereço do token", "commandCreate": "<PERSON><PERSON><PERSON>", "commandCreateTicket": "Criar ticket", "commandDeny": "<PERSON><PERSON><PERSON>", "commandDismiss": "Dispensar", "commandDontAllow": "Não permitir", "commandDownload": "<PERSON><PERSON><PERSON><PERSON>", "commandEdit": "<PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON> perfil", "commandEnableNow": "Ativar agora", "commandFilter": "Filtrar", "commandFollow": "<PERSON><PERSON><PERSON>", "commandHelp": "<PERSON><PERSON><PERSON>", "commandLearnMore": "<PERSON>ber mais", "commandLearnMore2": "<PERSON>ber mais", "commandMint": "<PERSON><PERSON><PERSON>", "commandMore": "<PERSON><PERSON>", "commandNext": "Se<PERSON><PERSON>", "commandNotNow": "<PERSON><PERSON><PERSON> n<PERSON>", "commandOpen": "Abrir", "commandOpenSettings": "A<PERSON>r definiç<PERSON>es", "commandPaste": "Colar", "commandReceive": "<PERSON><PERSON><PERSON>", "commandReconnect": "Voltar a ligar", "commandRecordVideo": "Gravar vídeo", "commandRequest": "Pedido", "commandRetry": "<PERSON><PERSON>r", "commandReview": "<PERSON><PERSON><PERSON>", "commandRevoke": "<PERSON><PERSON><PERSON>", "commandSave": "Guardar", "commandScanQRCode": "Digitalizar código QR", "commandSelect": "Selecionar", "commandSelectMedia": "Selecionar suporte", "commandSell": "Vender", "commandSend": "Enviar", "commandShare": "Partilhar", "commandShowBalance": "<PERSON>rar saldo", "commandSign": "<PERSON><PERSON><PERSON>", "commandSignOut": "Sign Out", "commandStake": "Stake", "commandMintLST": "Cunhar JitoSOL", "commandSwap": "Trocar", "commandSwapAgain": "Trocar outra vez", "commandTakePhoto": "Tirar foto", "commandTryAgain": "Tentar novamente", "commandViewTransaction": "Ver transação", "commandReportAsNotSpam": "Comunicar como não sendo spam", "commandReportAsSpam": "Comunicar como spam", "commandPin": "Afixar", "commandBlock": "Bloquear", "commandUnblock": "Desb<PERSON>que<PERSON>", "commandUnstake": "Tirar staking", "commandUnpin": "Desafixar", "commandHide": "Ocultar", "commandUnhide": "Mostrar", "commandBurn": "Queimar", "commandReport": "Comunicar", "commandView": "<PERSON>er", "commandProceedAnywayUnsafe": "Continuar na mesma (não seguro)", "commandUnfollow": "<PERSON><PERSON><PERSON>", "commandUnwrap": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "commandConfirmUnsafe": "Confirmar (não seguro)", "commandYesConfirmUnsafe": "<PERSON>m, confirmar (não seguro)", "commandConfirmAnyway": "Confirmar na mesma", "commandReportIssue": "Comunicar um problema", "commandSearch": "Procurar", "commandShowMore": "<PERSON><PERSON> mais", "commandShowLess": "<PERSON><PERSON> menos", "pastParticipleClaimed": "Resgatado", "pastParticipleCompleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pastParticipleCopied": "Copiado", "pastParticipleDone": "<PERSON><PERSON>", "pastParticipleDisabled": "Desativado", "pastParticipleRequested": "Pedido", "nounName": "Nome", "nounNetwork": "Rede", "nounNetworkFee": "Taxa da rede", "nounSymbol": "Símbolo", "nounType": "Tipo", "nounDescription": "Descrição", "nounYes": "<PERSON>m", "nounNo": "Não", "amount": "Mont<PERSON>", "limit": "Limite", "new": "Novo", "gotIt": "OK", "internal": "Interno", "reward": "Recompensa", "seeAll": "Ver tudo", "seeLess": "<PERSON>er menos", "viewAll": "Ver tudo", "homeTab": "Início", "collectiblesTab": "Colecionáveis", "swapTab": "Trocar", "activityTab": "Atividade", "exploreTab": "Explorador", "accountHeaderConnectedInterpolated": "Tem uma ligação a {{origin}}", "accountHeaderConnectedToSite": "Tem uma ligação a este website", "accountHeaderCopyToClipboard": "Copiar para área de transferência", "accountHeaderNotConnected": "Não tem uma ligação a", "accountHeaderNotConnectedInterpolated": "Não tem uma ligação a {{origin}}", "accountHeaderNotConnectedToSite": "Não tem uma ligação a este website", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "SOL insuficientes", "accountWithoutEnoughSolSecondaryText": "Uma conta envolvida nesta transação não tem SOL suficientes. A conta pode ser sua ou de outra pessoa. Esta transação será revertida se enviada.", "accountSwitcher": "Comutador de conta", "addAccountHardwareWalletPrimaryText": "Ligar carteira de hardware", "addAccountHardwareWalletSecondaryText": "Usar a carteira de harware do seu Ledger", "addAccountHardwareWalletSecondaryTextMobile": "Utilize a sua carteira {{supportedHardwareWallets}}", "addAccountSeedVaultWalletPrimaryText": "Ligar cofre de sementes", "addAccountSeedVaultWalletSecondaryText": "Usar uma carteira do cofre de sementes", "addAccountImportSeedPhrasePrimaryText": "Importar frase secreta de recuperação", "addAccountImportSeedPhraseSecondaryText": "Importar contas de outra carteira", "addAccountImportWalletPrimaryText": "Importar chave privada", "addAccountImportWalletSecondaryText": "Importar uma conta de cadeia única", "addAccountImportWalletSolanaSecondaryText": "Importar uma chave privada de Solana", "addAccountLimitReachedText": "Atingiu o limite de conta {{accountsCount}} na Phantom. Retire contas não utilizadas antes de adicionar contas novas.", "addAccountNoSeedAvailableText": "Não tem nenhuma frase semente disponível. Importe uma semente existente para gerar uma conta.", "addAccountNewWalletPrimaryText": "Criar conta nova", "addAccountNewWalletSecondaryText": "Gerar um novo endereço de carteira", "addAccountNewMultiChainWalletSecondaryText": "Adicionar uma nova conta de múltiplas cadeias", "addAccountNewSingleChainWalletSecondaryText": "Adicionar uma conta nova", "addAccountPrimaryText": "Adicionar/ligar carteira", "addAccountSecretPhraseLabel": "Frase secreta", "addAccountSeedLabel": "Semente", "addAccountSeedIDLabel": "ID de semente", "addAccountSecretPhraseDefaultLabel": "Frase secreta {{number}}", "addAccountPrivateKeyDefaultLabel": "Chave privada {{number}}", "addAccountZeroAccountsForSeed": "0 contas", "addAccountShowAccountForSeed": "Mostrar 1 conta", "addAccountShowAccountsForSeed": "Mostrar {{numOfAccounts}} contas", "addAccountHideAccountForSeed": "Ocultar 1 conta", "addAccountHideAccountsForSeed": "Ocultar {{numOfAccounts}} contas", "addAccountSelectSeedDescription": "A sua conta nova será gerada a partir desta frase secreta", "addAccountNumAccountsForSeed": "{{numOfAccounts}} contas", "addAccountOneAccountsForSeed": "1 conta", "addAccountGenerateAccountFromSeed": "C<PERSON><PERSON> conta", "addAccountReadOnly": "<PERSON>er endere<PERSON>o", "addAccountReadOnlySecondaryText": "<PERSON><PERSON><PERSON> qualquer endereço de carteira público", "addAccountSolanaAddress": "Endereço Solana", "addAccountEVMAddress": "Endereço EVM", "addAccountBitcoinAddress": "Endereço Bitcoin", "addAccountCreateSeedTitle": "Criar uma conta nova", "addAccountCreateSeedExplainer": "A sua carteira ainda não tem uma frase secreta! Para criar uma carteira nova, iremos gerar-lhe uma frase de recuperação. Anote a frase e não a revele a ninguém.", "addAccountSecretPhraseHeader": "A sua frase secreta", "addAccountNoSecretPhrases": "Não existem frases secretas disponíveis", "addAccountImportAccountActionButtonImport": "Importar", "addAccountImportAccountDuplicatePrivateKey": "Esta conta já existe na sua carteira", "addAccountImportAccountIncorrectFormat": "Formato incorreto", "addAccountImportAccountInvalidPrivateKey": "Chave privada inválida", "addAccountImportAccountName": "Nome", "addAccountImportAccountPrimaryText": "Importar chave privada", "addAccountImportAccountPrivateKey": "Chave privada", "addAccountImportAccountPublicKey": "Endereço ou domínio", "addAccountImportAccountPrivateKeyRequired": "É necessária a chave privada", "addAccountImportAccountNameRequired": "É necessário o nome", "addAccountImportAccountPublicKeyRequired": "É necessário um endereço público", "addAccountImportAccountDuplicateAddress": "Este endereço já existe na sua carteira", "addAddressAddressAlreadyAdded": "O endereço já foi adicionado", "addAddressAddressAlreadyExists": "O endereço já existe", "addAddressAddressInvalid": "O endereço não é válido", "addAddressAddressIsRequired": "É necessário o endereço", "addAddressAddressPlaceholder": "Endereço", "addAddressLabelIsRequired": "É necessária a etiqueta", "addAddressLabelPlaceholder": "Etiqueta", "addAddressPrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "addAddressToast": "Endereço adicionado", "createAssociatedTokenAccountCostLabelInterpolated": "<PERSON>to irá custar {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Já tem esta conta de token", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON><PERSON> insuficiente", "createAssociatedTokenAccountErrorInvalidMint": "Endereço de cunhagem inválido", "createAssociatedTokenAccountErrorInvalidName": "Nome inválido", "createAssociatedTokenAccountErrorInvalidSymbol": "Símbolo inválido", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Não nos foi possível criar a sua conta de token. Tente outra vez.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Falha ao criar a conta", "createAssociatedTokenAccountErrorUnableToSendMessage": "Não nos foi possível enviar a sua transação.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Falha ao enviar a transação", "createAssociatedTokenAccountInputPlaceholderMint": "Endereço de cunhagem", "createAssociatedTokenAccountInputPlaceholderName": "Nome", "createAssociatedTokenAccountInputPlaceholderSymbol": "Símbolo", "createAssociatedTokenAccountLoadingMessage": "Estamos a criar a sua conta de token.", "createAssociatedTokenAccountLoadingTitle": "A criar conta de token", "createAssociatedTokenAccountPageHeader": "Criar conta de token", "createAssociatedTokenAccountSuccessMessage": "A sua conta de token foi criada com sucesso!", "createAssociatedTokenAccountSuccessTitle": "Conta de token criada", "createAssociatedTokenAccountViewTransaction": "Ver transação", "assetDetailRecentActivity": "Atividade recente", "assetDetailStakeSOL": "Staking de SOL", "assetDetailUnknownToken": "Token desconhecido", "assetDetailUnwrapAll": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tudo", "assetDetailUnwrappingSOL": "A remover wrapping de SOL", "assetDetailUnwrappingSOLFailed": "Falha de remoção de wrapping de SOL", "assetDetailViewOnExplorer": "Ver em {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorador", "assetDetailSaveToPhotos": "Guardar nas fotos", "assetDetailSaveToPhotosToast": "Guardado nas fotos", "assetDetailPinCollection": "Afixar <PERSON>", "assetDetailUnpinCollection": "Desafixar coleção", "assetDetailHideCollection": "Ocultar co<PERSON>ção", "assetDetailUnhideCollection": "Mostrar coleção", "assetDetailTokenNameLabel": "Nome do token", "assetDetailNetworkLabel": "Rede", "assetDetailAddressLabel": "Endereço", "assetDetailPriceLabel": "Preço", "collectibleDetailSetAsAvatar": "Definir como avatar", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar definido", "collectibleDetailShare": "<PERSON><PERSON><PERSON>", "assetDetailTokenAddressCopied": "Endereço copiado", "assetDetailStakingLabel": "Staking", "assetDetailAboutLabel": "Sobre {{fungibleName}}", "assetDetailPriceDetail": "Dados do preço", "assetDetailHighlights": "Destaques", "assetDetailAllTimeReturn": "Retorno total", "assetDetailAverageCost": "<PERSON><PERSON>o m<PERSON>", "assetDetailPriceHistoryUnavailable": "Histórico de preço não disponível para este token", "assetDetailPriceHistoryInsufficientData": "Histórico de preço não disponível para este intervalo de tempo", "assetDetailPriceDataUnavailable": "Dados de preços não disponíveis", "assetDetailPriceHistoryError": "Erro ao obter histórico de preço", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1D", "assetDetailTimeFrame24h": "Preço de 24h", "assetDetailTimeFrame1W": "1S", "assetDetailTimeFrame1M": "1M", "assetDetailTimeFrameYTD": "SOMA", "assetDetailTimeFrameAll": "TUDO", "sendAssetAmountLabelInterpolated": "{{amount}} {{tokenSymbol}} disponível", "fiatRampQuotes": "Orçamentos", "fiatRampNewQuote": "Novo orçamento", "assetListSelectToken": "Selecionar token", "assetListSearch": "Procurar...", "assetListUnknownToken": "Token desconhecido", "buyFlowHealthWarning": "Alguns dos fornecedores de pagamento estão a registar um tráfego elevado. Os depósitos podem ter um atraso de várias horas.", "assetVisibilityUnknownToken": "Token desconhecido", "buyAssetInterpolated": "Comprar {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "A compra máxima é de {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "A compra mínima é de {{amount}}", "buyNoAssetsAvailable": "Não existem ativos Ethereum ou Polygon disponíveis", "buyThirdPartyScreenPaymentMethodSelector": "Pagar com", "buyThirdPartyScreenPaymentMethod": "Escolher o método de pagamento", "buyThirdPartyScreenChoseQuote": "Introduzir montante válido para orçamento", "buyThirdPartyScreenProviders": "Fornecedores", "buyThirdPartyScreenPaymentMethodTitle": "Métodos de pagamento", "buyThirdPartyScreenPaymentMethodEmptyState": "Não existem métodos de pagamento disponíveis na sua região", "buyThirdPartyScreenPaymentMethodFooter": "Os pagamentos são operados por parceiros da rede. As taxas podem variar. Alguns métodos de pagamento não estão disponíveis na sua região.", "buyThirdPartyScreenProvidersEmptyState": "Não existem fornecedores disponíveis na sua região", "buyThirdPartyScreenLoadingQuote": "A carregar orçamento...", "buyThirdPartyScreenViewQuote": "Ver orçamento", "gasEstimationErrorWarning": "Ocorreu um problema a calcular a taxa para esta transação. Esta poderá falhar.", "gasEstimationCouldNotFetch": "Não foi possivel obter estimativa de gasolina", "networkFeeCouldNotFetch": "Não foi possível obter taxa da rede", "nativeTokenBalanceErrorWarning": "Ocorreu um problema a obter o saldo do seu token para esta transação. Esta poderá falhar.", "blocklistOriginCommunityDatabaseInterpolated": "Este website foi assinalado como parte de uma <1>base de dados mantida pela comunidade</1> de burlas e websites de phishing conhecidos. Se acredita que o website foi assinalado erradamente, <3>apresente uma reclamação</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} está bloqueado!", "blocklistOriginIgnoreWarning": "Ignorar este aviso, ir para {{domainName}} na mesma.", "blocklistOriginSiteIsMalicious": "A Phantom acredita que este website é malicioso e que a sua utilização não é segura.", "blocklistOriginThisDomain": "este domínio", "blocklistProceedAnyway": "Ignorar aviso, avançar na mesma", "maliciousTransactionWarning": "A Phantom acredita que esta transação é maliciosa e que a sua assinatura não é segura. Desativámos a capacidade de assinatura para proteção do utilizador e dos seus fundos.", "maliciousTransactionWarningIgnoreWarning": "Ignorar aviso, avançar na mesma", "maliciousTransactionWarningTitle": "Transação assinalada!", "maliciousRequestBlockedTitle": "Pedido bloqueado", "maliciousRequestWarning": "Este website foi assinalado como malicioso. O mesmo pode estar a tentar roubar os seus fundos ou a tentar que confirme um pedido enganador.", "maliciousSignatureRequestBlocked": "Para sua segurança, a Phantom bloqueou este pedido.", "maliciousRequestBlocked": "Para sua segurança, a Phantom bloqueou este pedido.", "maliciousRequestFrictionDescription": "Continuar não é seguro, por isso a Phantom bloqueou este pedido. Deve fechar esta caixa de diálogo.", "maliciousRequestAcknowledge": "Compreendo que posso perder todos os meus fundos ao utilizar este website.", "maliciousRequestAreYouSure": "Tem a certeza?", "siwErrorPopupTitle": "Pedido de assinatura inválido", "siwParseErrorDescription": "O pedido de assinatura da aplicação não pode ser exibido devido a uma formatação inválida.", "siwVerificationErrorDescription": "Ocorreu um ou mais erros com o pedido de assinatura da mensagem. Para sua segurança certifique-se de que está a usar a aplicação correta e tente novamente.", "siwErrorPagination": "{{n}} de {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Advertência: o endereço da aplicação não corresponde ao endereço fornecido para assinatura.", "siwErrorMessage_DOMAIN_MISMATCH": "Advertência: o domínio da aplicação não corresponde ao domínio fornecido para verificação.", "siwErrorMessage_URI_MISMATCH": "Advertência: o nome de anfitrião URI não corresponde ao domínio.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Advertência: a ID de cadeia não corresponde à ID de cadeia fornecida para verificação.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Advertência: a data de emissão da mensagem é demasiado atrás no passado.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Advertência: a data de emissão da mensagem é demasiado à frente no futuro.", "siwErrorMessage_EXPIRED": "Advertência: a mensagem expirou.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Advertência: a mensagem expira antes da emissão.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Advertência: a mensagem irá expirar antes de se tornar válida.", "siwErrorShowErrorDetails": "<PERSON><PERSON> de<PERSON><PERSON> de erro", "siwErrorHideErrorDetails": "<PERSON><PERSON><PERSON><PERSON> de<PERSON><PERSON> de erro", "siwErrorIgnoreWarning": "Ignorar aviso, avançar na mesma", "siwsTitle": "Pedido de início de sessão", "siwsPermissions": "Autorizações", "siwsAgreement": "Mensagem", "siwsAdvancedDetails": "<PERSON><PERSON><PERSON>", "siwsAlternateStatement": "{{domain}} quer que inicie a sessão com a sua conta Solana:\n{{address}}", "siwsFieldLable_domain": "<PERSON><PERSON><PERSON>", "siwsFieldLable_address": "Endereço", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Vers<PERSON>", "siwsFieldLable_chainId": "ID de cadeia", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "Emitido em", "siwsFieldLable_expirationTime": "Expira em", "siwsFieldLable_requestId": "ID de pedido", "siwsFieldLable_resources": "Recursos", "siwsVerificationErrorDescription": "Este pedido de início de sessão é inválido. Isto significa que o website não é seguro, ou que o programador cometeu um erro quando enviou o pedido.", "siwsErrorNumIssues": "{{n}} problemas", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Esta ID de cadeia não corresponde à rede em que se encontra.", "siwsErrorMessage_DOMAIN_MISMATCH": "Este domínio não é aquele em que está a iniciar a sessão.", "siwsErrorMessage_URI_MISMATCH": "Este URI não é aquele em que está a iniciar a sessão.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "A data de emissão da mensagem é demasiado atrás no passado.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "A data de emissão da mensagem é demasiado à frente no futuro.", "siwsErrorMessage_EXPIRED": "A mensagem expirou.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "A mensagem expira antes da emissão.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "A mensagem irá expirar antes de se tornar válida.", "changeLockTimerPrimaryText": "Temporizador de bloqueio auto", "changeLockTimerSecondaryText": "Quanto tempo devemos aguardar antes de bloquear a sua carteira depois de estar inativa?", "changeLockTimerToast": "Temporizador de bloqueio auto atualizado", "changePasswordConfirmNewPassword": "Confirmar nova palavra-passe", "changePasswordCurrentPassword": "Palavra-passe atual", "changePasswordErrorIncorrectCurrentPassword": "Palavra-passe atual incorreta", "changePasswordErrorGeneric": "Ocorreu um erro, tente outra vez mais tarde", "changePasswordNewPassword": "Nova palavra-passe", "changePasswordPrimaryText": "Alterar palavra-passe", "changePasswordToast": "Palavra-passe atualizada", "collectionsSpamCollections": "Coleções de spam", "collectionsHiddenCollections": "Coleções ocultas", "collectiblesReportAsSpam": "Comunicar como spam", "collectiblesReportAsSpamAndHide": "Comunicar como spam e ocultar", "collectiblesReportAsNotSpam": "Comunicar como não sendo spam", "collectiblesReportAsNotSpamAndUnhide": "Mostrar e comunicar como não sendo spam", "collectiblesReportNotSpam": "Não é spam", "collectionsManageCollectibles": "G<PERSON>r lista de colecionáveis", "collectibleDetailDescription": "Descrição", "collectibleDetailProperties": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailOrdinalInfo": "Informação ordinal", "collectibleDetailRareSatsInfo": "Informação sobre sats raros", "collectibleDetailSatsInUtxo": "Sats em UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Número <PERSON>", "collectibleDetailSatName": "Nome Sat", "collectibleDetailInscriptionId": "ID de inscrição", "collectibleDetailInscriptionNumber": "Número de inscrição", "collectibleDetailStandard": "Standard", "collectibleDetailCreated": "<PERSON><PERSON><PERSON>", "collectibleDetailViewOnExplorer": "Ver em {{explorer}}", "collectibleDetailList": "Lista", "collectibleDetailSellNow": "Vender por {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Disponibiliza Bitcoins extra", "collectibleDetailUtxoSplitterCtaSubtitle": "Tem {{value}} BTC para desbloquear", "collectibleDetailUtxoSplitterModalCtaTitle": "Sats raros", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Para proteger os seus fundos, impedimos que BTC em UTXO com sats raros sejam enviados. Utilize o divisor de UTXO do Magic Eden para disponibilizar {{value}} BTC a partir dos seus sats raros.", "collectibleDetailUtxoSplitterModalCtaButton": "Utilizar divisor de UTXO", "collectibleDetailEasilyAccept": "Aceitar a oferta mais alta", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "Este colecionável estava oculto porque a Phantom acredita que é corrreio indesejado.", "collectibleDetailSpamOverlayReveal": "Mostrar co<PERSON>vel", "collectibleBurnTermsOfService": "Compreendo que esta ação não pode ser anulada", "collectibleBurnTitleWithCount_one": "Queimar token", "collectibleBurnTitleWithCount_other": "Queimar tokens", "collectibleBurnDescriptionWithCount_one": "Esta ação irá destruir e remover de forma permanente este token da sua carteira.", "collectibleBurnDescriptionWithCount_other": "Esta ação irá destruir e remover de forma permanente estes tokens da sua carteira.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Tokens", "collectibleBurnCta": "Queimar", "collectibleBurnRebate": "Desconto", "collectibleBurnRebateTooltip": "Uma pequena quantidade de SOL será depositada automaticamente na sua carteira com a queima deste token.", "collectibleBurnNetworkFee": "Taxa da rede", "collectibleBurnNetworkFeeTooltip": "Montante necessário pela rede Solana para processar a transação", "unwrapButtonSwapTo": "Trocar para {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Levantar de {{withdrawalSource}} para {{chainSymbol}}", "unwrapModalEstimatedTime": "Tempo estimado", "unwrapModalNetwork": "Rede", "unwrapModalNetworkFee": "Taxa da rede", "unwrapModalTitle": "Resumo", "unsupportedChain": "Cadeia não suportada", "unsupportedChainDescription": "Aparentemente não suportamos {{action}} da rede {{chainName}}.", "networkFeesTooltipLabel": "Taxas de rede {{chainName}}", "networkFeesTooltipDescription": "As taxas {{chainName}} variam com base em vários fatores. Pode personalizá-las para tornar a sua transação mais rápida (mais dispendiosa) ou mais lenta (mais barata).", "burnStatusErrorTitleWithCount_one": "O token não foi queimado", "burnStatusErrorTitleWithCount_other": "Os tokens não foram queimados", "burnStatusSuccessTitleWithCount_one": "Token queimado!", "burnStatusSuccessTitleWithCount_other": "Tokens queimados!", "burnStatusLoadingTitleWithCount_one": "A queimar o token...", "burnStatusLoadingTitleWithCount_other": "A queimar os tokens...", "burnStatusErrorMessageWithCount_one": "Não foi possível queimar este token. Tente outra vez mais tarde.", "burnStatusErrorMessageWithCount_other": "Não foi possível queimar estes tokens. Tente outra vez mais tarde.", "burnStatusSuccessMessageWithCount_one": "Este token foi destruído de forma permanente e foi realizado um depósito de {{rebateAmount}} SOL na sua carteira.", "burnStatusSuccessMessageWithCount_other": "Estes tokens foram destruídos de forma permanente e foi realizado um depósito de {{rebateAmount}} SOL na sua carteira.", "burnStatusLoadingMessageWithCount_one": "Este token está a ser destruído de forma permanente e um depósito de {{rebateAmount}} SOL será realizado na sua carteira.", "burnStatusLoadingMessageWithCount_other": "Estes tokens estão a ser destruídos de forma permanente e um depósito de {{rebateAmount}} SOL será realizado na sua carteira.", "burnStatusViewTransactionText": "Ver transação", "collectibleDisplayLoading": "A carregar...", "collectiblesNoCollectibles": "<PERSON><PERSON><PERSON>", "collectiblesPrimaryText": "Os seus colecionáveis", "collectiblesReceiveCollectible": "Re<PERSON>ber <PERSON>", "collectiblesUnknownCollection": "Colecionável desconhecido", "collectiblesUnknownCollectible": "Colecionável desconhecido", "collectiblesUniqueHolders": "Proprietários únicos", "collectiblesSupply": "<PERSON><PERSON><PERSON>", "collectiblesUnknownTokens": "Tokens desconhecidos", "collectiblesNrOfListed": "{{ nrOfListed }} listado", "collectiblesListed": "Listado", "collectiblesMintCollectible": "<PERSON><PERSON><PERSON>", "collectiblesYouMint": "<PERSON><PERSON><PERSON><PERSON> por si", "collectiblesMintCost": "<PERSON>usto de cunh<PERSON>m", "collectiblesMintFail": "<PERSON><PERSON><PERSON> de <PERSON>nh<PERSON>", "collectiblesMintFailMessage": "Ocorreu um problema ao cunhar o seu celecionável. Tente outra vez.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMinting": "A cunhar...", "collectiblesMintingMessage": "O seu colecionável está a ser cunhado", "collectiblesMintShareSubject": "Consultar", "collectiblesMintShareMessage": "<PERSON><PERSON><PERSON><PERSON> isto na @phantom!", "collectiblesMintSuccess": "Cunhagem com sucesso", "collectiblesMintSuccessMessage": "O seu colecionável já foi cunhado", "collectiblesMintSuccessQuestMessage": "Cumpriu os requisitos de uma Missão Phantom. Toque em Resgatar a sua recompensa para obter o seu colecionável gratuito.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesMintMaxLengthErrorMessage": "Comprimento máx. excedido", "collectiblesMintSafelyDismiss": "Pode dispensar esta janela em segurança.", "collectiblesTrimmed": "Atingimos o limite do número de colecionáveis que podem ser exibidos neste momento.", "collectiblesNonTransferable": "Não transferível", "collectiblesNonTransferableYes": "<PERSON>m", "collectiblesSellOfferDetails": "<PERSON><PERSON> of<PERSON>", "collectiblesSellYouSell": "<PERSON><PERSON><PERSON>", "collectiblesSellGotIt": "OK", "collectiblesSellYouReceive": "Recebe", "collectiblesSellOffer": "Oferecer", "collectiblesSoldCollectible": "Cole<PERSON><PERSON><PERSON> vendido", "collectiblesSellMarketplace": "<PERSON><PERSON><PERSON>", "collectiblesSellCollectionFloor": "<PERSON>iar de coleção", "collectiblesSellDifferenceFromFloor": "Diferença de piso", "collectiblesSellLastSalePrice": "Última venda", "collectiblesSellEstimatedFees": "Taxas estimadas", "collectiblesSellEstimatedProfitAndLoss": "<PERSON>ro est<PERSON>/Perda", "collectiblesSellViewOnMarketplace": "Ver em {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "O preço \"Comprar agora\" mais baixo da coleção em múltiplos mercados.", "collectiblesSellProfitLossTooltip": "O lucro/perda estimado é calculado com base no último preço de venda e no montante da oferta menos as taxas.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Royalties ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Taxa de mercado ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Taxa de mercado", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Rede {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "O orçamento inclui uma taxa Phantom de {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "O orçamento inclui royalties, taxa de rede, taxa de mercado, e uma taxa Phantom de {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "O orçamento inclui royalties, taxa de rede, e uma tarifa de mercado", "collectiblesSellTransactionFeeTooltipTitle": "Taxa de transação", "collectiblesSellStatusLoadingTitle": "A aceitar a oferta...", "collectiblesSellStatusLoadingIsSellingFor": "está a ser vendido por", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} vendido(a)!", "collectiblesSellStatusSuccessWasSold": "foi vendido com êxito por", "collectiblesSellStatusErrorTitle": "Algo correu mal", "collectiblesSellStatusErrorSubtitle": "Ocorreu um erro ao tentar vender", "collectiblesSellStatusViewTransaction": "Ver transação", "collectiblesSellInsufficientFundsTitle": "Fundos insuficientes", "collectiblesSellInsufficientFundsSubtitle": "Não foi possível aceitar uma oferta para este colecionável porque não havia fundos suficientes para pagar a taxa de rede.", "collectiblesSellRecentlyTransferedNFTTitle": "Transferido recentemente", "collectiblesSellRecentlyTransferedNFTSubtitle": "Tem de aguardar uma hora para aceitar ofertas após uma transferência.", "collectiblesApproveCollection": "Aprovado {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "Oferta não disponível", "collectiblesSellNotAvailableAnymoreSubtitle": "A oferta já não está disponível. Cancele esta oferta e tente outra vez.", "collectiblesSellFlaggedTokenTitle": "O colecionável foi assinalado", "collectiblesSellFlaggedTokenSubtitle": "O colecionável não pode ser comercializado. <PERSON>to pode dever-se a vários motivos como, por exemplo, ter sido comunicado como roubado ou alvo de stake sem bloqueio", "collectiblesListOnMagicEden": "Listado no Magic Eden", "collectiblesListPrice": "Preço de lista", "collectiblesUseFloor": "<PERSON><PERSON> limiar", "collectiblesFloorPrice": "Preço de limiar", "collectiblesLastSalePrice": "Último preço de promoção", "collectiblesTotalReturn": "Retorno total", "collectiblesOriginalPurchasePrice": "Preço de compra original", "collectiblesMagicEdenFee": "Taxa de Magic Eden", "collectiblesArtistRoyalties": "Direitos de artista", "collectiblesListNowButton": "Listar agora", "collectiblesListAnywayButton": "Listar na mesma", "collectiblesCreateListingTermsOfService": "Ao tocar em <1>\"Listar agora\"</1> ace<PERSON> os <3>Termos de Serviço</3> de Magic Eden", "collectiblesViewListing": "Ver listagem", "collectiblesListingViewTransaction": "Ver transação", "collectiblesRemoveListing": "Remover listagem", "collectiblesEditListing": "Editar listagem", "collectiblesEditListPrice": "Editar preço de lista", "collectiblesListPriceTooltip": "O preço de lista é o preço de venda de um item. Os vendedores geralmente definem o preço de lista com igual ou superior ao preço de limiar.", "collectiblesFloorPriceTooltip": "O preço de limiar é o preço de lista mais baixo de um item nesta coleção.", "collectiblesOriginalPurchasePriceTooltip": "Originalmente comprou este item por este valor.", "collectiblesPurchasedForSol": "Comprado por {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Não é possível carregar as listagens", "collectiblesUnableToLoadListingsFrom": "Não é possível carregar as listagens de {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "As suas listagens e ativos estão seguros mas não os conseguimos carregar a partir de {{marketplace}} neste momento. Tente novamente mais tarde.", "collectiblesBelowFloorPrice": "Abaixo do preço de limiar", "collectiblesBelowFloorPriceMessage": "Tem a certeza que pretende listar o seu NFT abaixo do preço de limiar?", "collectiblesMinimumListingPrice": "O preço mínimo é de 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "O Magic Eden cobra uma taxa em relação a transações concluídas.", "collectiblesArtistRoyaltiesTooltip": "O criador desta coleção recebe uma % de comissão por cada venda concluída.", "collectibleScreenCollectionLabel": "Coleção", "collectibleScreenPhotosPermissionTitle": "Autorização para aceder a fotos", "collectibleScreenPhotosPermissionMessage": "Necessitamos da sua autorização para aceder às suas fotos. Entre nas definições e atualize as suas autorizações.", "collectibleScreenPhotosPermissionOpenSettings": "A<PERSON>r definiç<PERSON>es", "listStatusErrorTitle": "<PERSON><PERSON><PERSON> de <PERSON>m", "editListStatusErrorTitle": "Não foi possível atualizar", "removeListStatusErrorTitle": "Falha de remoção de listagem", "listStatusSuccessTitle": "Listagem criada!", "editListingStatusSuccessTitle": "Listagem atualizada!", "removeListStatusSuccessTitle": "Listagem removida do Magic Eden", "listStatusLoadingTitle": "A criar listagem...", "editListingStatusLoadingTitle": "A atualizar listagem...", "removeListStatusLoadingTitle": "A remover listagem...", "listStatusErrorMessage": "Não foi possível listar {{name}} no Magic Eden", "removeListStatusErrorMessage": "<PERSON>ão foi possível remover {{name}} da lista no Magic Eden", "listStatusSuccessMessage": "{{name}} está agora listado em Magic Eden por {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "{{name}} está agora atualizado em Magic Eden por {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} foi removido com sucesso do Magic Eden", "listStatusLoadingMessage": "Listagem {{name}} em Magic Eden de {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "A atualizar {{name}} em <PERSON> Eden de {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "A remover {{name}} do Magic Eden. Esta ação pode demorar algum tempo.", "listStatusLoadingSafelyDismiss": "Pode dispensar esta janela em segurança.", "listStatusViewOnMagicEden": "Ver no Magic Eden", "listStatusViewOnMarketplace": "Ver em {{marketplace}}", "listStatusLoadingDismiss": "Dispensar", "listStatusViewTransaction": "Ver transação", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Ligue a sua carteira de hardware e certifique-se de que está desbloqueada. Assim que a tivermos detetado pode escolher qual o endereço que pretende usar.", "connectHardwareFailedPrimaryText": "Falha de ligação", "connectHardwareFailedSecondaryText": "Ligue a sua carteira de hardware e certifique-se de que está desbloqueada. Assim que a descobrirmos pode escolher qual o endereço que pretende usar.", "connectHardwareFinishPrimaryText": "Conta adicionada!", "connectHardwareFinishSecondaryText": "Pode agora aceder à sua carteira Ledger Nano a partir de dentro de Phantom. Regresse à extensão.", "connectHardwareNeedsPermissionPrimaryText": "Ligar uma carteira nova", "connectHardwareNeedsPermissionSecondaryText": "Clique no botão a seguir para iniciar o processo de ligação.", "connectHardwareSearchingPrimaryText": "A procurar a carteira...", "connectHardwareSearchingSecondaryText": "Ligue a sua carteira de hardware, certifique-se de que está desbloqueada, e de que tem permissões aprovadas no seu browser.", "connectHardwarePermissionDeniedPrimary": "Autorização negada", "connectHardwarePermissionDeniedSecondary": "Dar autorização à Phantom para se ligar ao seu dispositivo Ledger", "connectHardwarePermissionUnableToConnect": "Não é possível realizar a ligação", "connectHardwarePermissionUnableToConnectDescription": "Não foi possível realizar a ligação ao seu dispositivo Ledger. Podemos precisar de mais autorizações.", "connectHardwareSelectAddressAllAddressesImported": "Todos os endereços importados", "connectHardwareSelectAddressDerivationPath": "Caminho de derivação", "connectHardwareSelectAddressSearching": "A procurar...", "connectHardwareSelectAddressSelectWalletAddress": "Selecionar endereço da carteira", "connectHardwareSelectAddressWalletAddress": "Endereço da carteira", "connectHardwareWaitingForApplicationSecondaryText": "Ligue a sua carteira de hardware e certifique-se de está desbloqueada.", "connectHardwareWaitingForPermissionPrimaryText": "É preciso autorização", "connectHardwareWaitingForPermissionSecondaryText": "Ligue a sua carteira de hardware, certifique-se de que está desbloqueada, e de que tem permissões aprovadas no seu browser.", "connectHardwareAddAccountButton": "Adicionar conta", "connectHardwareLedger": "Ligar o seu Ledger", "connectHardwareStartConnection": "Clique no botão a seguir para iniciar a ligação da carteira de hardware do seu Ledger", "connectHardwarePairSuccessPrimary": "{{productName}} ligado", "connectHardwarePairSuccessSecondary": "Realizou a ligação com sucesso do seu {{productName}}.", "connectHardwareSelectChains": "Selecionar cadeias a ligar", "connectHardwareSearching": "A procurar...", "connectHardwareMakeSureConnected": "Ligue e desbloqueie a sua carteira de hardware. Aprove as autorizações relevantes do browser.", "connectHardwareOpenAppDescription": "Desbloqueie a sua carteira de hardware", "connectHardwareConnecting": "A ligar...", "connectHardwareConnectingDescription": "Estamos a realizar a ligação ao seu dispositivo Ledger.", "connectHardwareConnectingAccounts": "A ligar as suas contas...", "connectHardwareDiscoveringAccounts": "A procurar contas...", "connectHardwareDiscoveringAccountsDescription": "Estamos a procurar atividade nas suas contas.", "connectHardwareErrorLedgerLocked": "O Ledger está bloqueado", "connectHardwareErrorLedgerLockedDescription": "Certifique-se de que o seu dispositivo Ledger está desbloqueado, e tente outra vez.", "connectHardwareErrorLedgerGeneric": "Algo correu mal", "connectHardwareErrorLedgerGenericDescription": "Não foi possível encontrar contas. Certifique-se que o seu dispositivo Ledger está desbloqueado, e tente outra vez.", "connectHardwareErrorLedgerPhantomLocked": "Volte a abrir a Phantom e tente ligar o seu hardware outra vez.", "connectHardwareFindingAccountsWithActivity": "A procurar contas {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "A encontrar contas {{chainName1}} ou {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Encontrámos {{numOfAccounts}} contas com atividade no seu Ledger.", "connectHardwareFoundAccountsWithActivitySingular": "Encontrámos 1 conta com atividade no seu Ledger.", "connectHardwareFoundSomeAccounts": "Encontrámos algumas contas no seu dispositivo Ledger.", "connectHardwareViewAccounts": "Ver contas", "connectHardwareConnectAccounts": "Contas ligadas", "connectHardwareSelectAccounts": "Selecionar contas", "connectHardwareChooseAccountsToConnect": "Escolher contas de carteiras a ligar.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} contas adicionadas", "connectHardwareAccountsStepOfSteps": "Passo {{stepNum}} de {{totalSteps}}", "connectHardwareMobile": "Ligar Led<PERSON>", "connectHardwareMobileTitle": "Ligar a carteira de hardware do seu Ledger", "connectHardwareMobileEnableBluetooth": "Ativar Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Dar autorização para usar Bluetooth para ligação", "connectHardwareMobileEnableBluetoothSettings": "Entre nas Definições para permitir à Phantom usar as autorizações de Dispositivos próximos e Localização.", "connectHardwareMobilePairWithDevice": "Emparelhar com o seu dispositivo Ledger", "connectHardwareMobilePairWithDeviceDescription": "Mantenha o seu dispositivo por perto para obter o melhor sinal", "connectHardwareMobileConnectAccounts": "Ligar contas", "connectHardwareMobileConnectAccountsDescription": "Vamos procurar atividade em quaisquer contas que possa ter usado", "connectHardwareMobileConnectLedgerDevice": "Ligar o seu dispositivo Ledger", "connectHardwareMobileLookingForDevices": "A procurar dispositivos próximos...", "connectHardwareMobileLookingForDevicesDescription": "Ligue o seu dispositivo Ledger e certifique-se de que está desbloqueado.", "connectHardwareMobileFoundDeviceSingular": "Encontrámos 1 dispositivo Ledger", "connectHardwareMobileFoundDevices": "Encontrámos {{numDevicesFound}} dispositivos Ledger", "connectHardwareMobileFoundDevicesDescription": "Selecione um dispositivo Ledger abaixo para começar a emparelhar.", "connectHardwareMobilePairingWith": "Emparelhamento com {{deviceName}}", "connectHardwareMobilePairingWithDescription": "<PERSON><PERSON> as instruções no seu dispositivo Ledger durante o emparelhamento.", "connectHardwareMobilePairingFailed": "Emparelhamento sem sucesso", "connectHardwareMobilePairingFailedDescription": "Não é possível emparelhar com {{deviceName}}. Certifique-se de que o seu dispositivo está desbloqueado.", "connectHardwareMobilePairingSuccessful": "Emparelhamento com sucesso", "connectHardwareMobilePairingSuccessfulDescription": "Emparelhou e realizou a ligação com sucesso ao seu dispositivo Ledger.", "connectHardwareMobileOpenAppSingleChain": "Abrir a aplicação {{chainName}} no seu Ledger", "connectHardwareMobileOpenAppDualChain": "Abrir a aplicação {{chainName1}} ou {{chainName2}} no seu Ledger", "connectHardwareMobileOpenAppDescription": "Certifique-se de que o seu dispositivo está desbloqueado.", "connectHardwareMobileStillCantFindDevice": "Ainda não encontrou o seu dispositivo?", "connectHardwareMobileLostConnection": "Ligação perdida", "connectHardwareMobileLostConnectionDescription": "Perdemos a ligação a {{deviceName}}. Certifique-se de que o seu dispositivo está desbloqueado, e depois tente outra vez.", "connectHardwareMobileGenericLedgerDevice": "Dispositivo Ledger", "connectHardwareMobileConnectDeviceSigning": "Ligue o seu {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Desbloqueie o seu dispositivo Ledger e mantenha-o por perto.", "connectHardwareMobileBluetoothDisabled": "Bluetooth está desativado", "connectHardwareMobileBluetoothDisabledDescription": "Ative o seu Bluetooth e certifique-se de que o seu dispositivo Ledger está desbloqueado.", "connectHardwareMobileLearnMore": "<PERSON>ber mais", "connectHardwareMobileBlindSigningDisabled": "Assinatura cega desativada", "connectHardwareMobileBlindSigningDisabledDescription": "Certifique-se de que a assinatura cega está ativa no seu dispositivo.", "connectHardwareMobileConfirmSingleChain": "Tem de confirmar a transação na sua carteira de hardware. Certifique-se de que está desbloqueada.", "metamaskExplainerBottomSheetHeader": "Este website funciona com a Phantom", "metamaskExplainerBottomSheetSubheader": "Selecione MetaMask na caixa de diálogo da carteira de ligação para avançar.", "metamaskExplainerBottomSheetDontShowAgain": "Não mostrar novamente", "ledgerStatusNotConnected": "O Ledger não está ligado", "ledgerStatusConnectedInterpolated": "{{productName}} está ligado", "connectionClusterInterpolated": "Está atualmente em {{cluster}}", "connectionClusterTestnetMode": "Está atualmente no modo Testnet", "featureNotSupportedOnLocalNet": "Esta funcionalidade não é suportada quando a rede local Solana está ativada.", "readOnlyAccountBannerWarning": "Está a visualizar esta conta", "depositAddress": "Endereço de receção", "depositAddressChainInterpolated": "O seu endereço de {{chain}}", "depositAssetDepositInterpolated": "Receber {{tokenSymbol}}", "depositAssetSecondaryText": "Este endereço só pode ser usado para receber tokens compatíveis.", "depositAssetTextInterpolated": "Utilize este endereço para receber tokens e artigos colecionáveis em <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Transferência de câmbio", "depositAssetShareAddress": "<PERSON><PERSON><PERSON>", "depositAssetBuyOrDeposit": "<PERSON><PERSON><PERSON> ou transferir", "depositAssetBuyOrDepositDesc": "Coloque saldo na sua carteira para começar", "depositAssetTransfer": "Transferir", "editAddressAddressAlreadyAdded": "O endereço já foi adicionado", "editAddressAddressAlreadyExists": "O endereço já existe", "editAddressAddressIsRequired": "É necessário o endereço", "editAddressPrimaryText": "<PERSON><PERSON>", "editAddressRemove": "Remover do livro de endereços", "editAddressToast": "Endereço atualizado", "removeSavedAddressToast": "Endereço removido", "exportSecretErrorGeneric": "Ocorreu um erro, tente outra vez mais tarde", "exportSecretErrorIncorrectPassword": "Palavra-passe incorreta", "exportSecretPassword": "Palavra-passe", "exportSecretPrivateKey": "chave privada", "exportSecretSecretPhrase": "frase secreta", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "frase secreta de recuperação", "exportSecretSelectYourAccount": "Selecione a sua conta", "exportSecretShowPrivateKey": "Mostrar chave privada", "exportSecretShowSecretRecoveryPhrase": "Mostrar frase secreta de recuperação", "exportSecretShowSecret": "Mostrar {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "<1><PERSON><PERSON></1> partilhe o seu {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "Se alguém tiver o seu {{secretNameText}} então terá controlo total sobre a sua carteira.", "exportSecretOnlyWay": "O seu {{secretNameText}} é a única forma de recuperar a sua carteira", "exportSecretDoNotShow": "Não deixe ninguém ver o seu {{secretNameText}}", "exportSecretWillNotShare": "<PERSON>ão irei partilhar {{secretNameText}} com ninguém, incluindo a Phantom.", "exportSecretNeverShare": "Nunca partilhe o seu {{secretNameText}} com ninguém", "exportSecretYourPrivateKey": "A sua chave privada", "exportSecretYourSecretRecoveryPhrase": "A sua frase secreta de recuperação", "exportSecretResetPin": "Reponha o seu PIN", "fullPageHeaderBeta": "beta!", "fullPageHeaderHelp": "<PERSON><PERSON><PERSON>", "gasUpTo": "Até {{ amount }}", "timeDescription1hour": "Cerca de 1 hora", "timeDescription30minutes": "Cerca de 30 minutos", "timeDescription10minutes": "Cerca de 10 minutos", "timeDescription2minutes": "Cerca de 2 minutos", "timeDescription30seconds": "Cerca de 30 segundos", "timeDescription15seconds": "Cerca de 15 segundos", "timeDescription10seconds": "Cerca de 10 segundos", "timeDescription5seconds": "Cerca de 5 segundos", "timeDescriptionAbbrev1hour": "1 h", "timeDescriptionAbbrev30minutes": "30 min", "timeDescriptionAbbrev10minutes": "10 min", "timeDescriptionAbbrev2minutes": "2 min", "timeDescriptionAbbrev30seconds": "30 s", "timeDescriptionAbbrev15seconds": "15 s", "timeDescriptionAbbrev10seconds": "10 s", "timeDescriptionAbbrev5seconds": "5 s", "gasSlow": "<PERSON><PERSON>", "gasAverage": "Normal", "gasFast": "<PERSON><PERSON><PERSON><PERSON>", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "Tentar novamente", "homeErrorDescription": "Ocorreu um erro ao tentar recuperar os seus ativos. Realize a atualização e tente outra vez.", "homeErrorTitle": "Falha ao tentar obter os ativos", "homeManageTokenList": "G<PERSON>r lista de tokens", "interstitialDismissUnderstood": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "interstitialBaseWelcomeTitle": "A Phantom agora suporta Base!", "interstitialBaseWelcomeItemTitle_1": "Enviar, receber, e comprar tokens", "interstitialBaseWelcomeItemTitle_2": "Explorar o ecossistema Base", "interstitialBaseWelcomeItemTitle_3": "Seguro e protegido", "interstitialBaseWelcomeItemDescription_1": "Transferir e comprar USDC e ETH em Base utilizando {{paymentMethod}}, cartões, ou Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Utilize a Phantom com todas as suas aplicações DeFi e NFT favoritas.", "interstitialBaseWelcomeItemDescription_3": "Mantenha-se seguro com suporte Ledger, filtro de spam, e simulação de transações.", "privacyPolicyChangedInterpolated": "A nossa Política de Privacidade mudou. <1><PERSON><PERSON> ma<PERSON></1>", "bitcoinAddressTypesBodyTitle": "Tipos de endereço Bitcoin", "bitcoinAddressTypesFeature1Title": "Sobre os endereços Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "A Phantom suporta Native Seg<PERSON><PERSON> e Taproot, cada um com o seu próprio saldo. Pode enviar BTC ou Ordinals com qualquer um dos tipos de endereço.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "O endereço BTC predefinido na Phantom. Mais antigo que Taproot mas compatível com todas as carteiras e câmbios.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Melhor para Ordinals e BRC-20, com as tarifas mais baratas. Ajuste os endereços em Preferências -> Endereço Bitcoin preferido.", "headerTitleInfo": "Info.", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Este é o seu endereço <1>{{addressType}}</1>.", "invalidChecksumTitle": "Realizámos o upgrade da sua frase secreta!", "invalidChecksumFeature1ExportPhrase": "Exportar a sua nova frase secreta", "invalidChecksumFeature1ExportPhraseDescription": "Guarde uma cópia de segurança da sua nova frase secreta com as chaves privadas das suas contas antigas.", "invalidChecksumFeature2FundsAreSafe": "Os seus fundos estão seguros e protegidos", "invalidChecksumFeature2FundsAreSafeDescription": "Este upgrade foi automatizado. Ninguém na Phantom conhece a sua frase secreta ou tem acesso aos seus fundos.", "invalidChecksumFeature3LearnMore": "<PERSON>ber mais", "invalidChecksumFeature3LearnMoreDescription": "Tinha uma frase que era incompatível com a maioria das carteiras. Leia <1>este artigo de ajuda</1> para saber mais sobre isto.", "invalidChecksumBackUpSecretPhrase": "Cópia de segurança da frase secreta", "migrationFailureTitle": "Algo correu mal na migração da sua conta", "migrationFailureFeature1": "Exportar a sua frase secreta", "migrationFailureFeature1Description": "Guarde uma cópia de segurança da sua frase secreta antes de iniciar a integração.", "migrationFailureFeature2": "Integração na Phantom", "migrationFailureFeature2Description": "Terá de realizar a re-integração na Phantom para ver a sua conta.", "migrationFailureFeature3": "<PERSON>ber mais", "migrationFailureFeature3Description": "<PERSON><PERSON> <1>este artigo de ajuda</1> para saber mais sobre isto.", "migrationFailureContinueToOnboarding": "Avançar para a re-integração", "migrationFailureUnableToFetchMnemonic": "Não nos foi possível carregar a sua frase secreta", "migrationFailureUnableToFetchMnemonicDescription": "Contacte a assistência e descarregue os registos da aplicação para realizar a depuração", "migrationFailureContactSupport": "Contactar a assistência", "ledgerActionConfirm": "Confirmar no seu Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Assinatura oculta desativada", "ledgerActionErrorBlindSignDisabledSecondaryText": "Certifique-se de que a assinatura oculta está ativada no seu dispositivo e tente a ação outra vez", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Dispositivo desligado durante a operação", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Feche a extensão Phantom e volte a tentar a ação", "ledgerActionErrorDeviceLockedPrimaryText": "Dispositivo bloqueado", "ledgerActionErrorDeviceLockedSecondaryText": "Debloqueie o dispositivo e volte a tentar a ação", "ledgerActionErrorHeader": "Erro de ação do Ledger", "ledgerActionErrorUserRejectionPrimaryText": "Transação rejeitada pelo utilizador", "ledgerActionErrorUserRejectionSecondaryText": "A ação foi rejeitada no dispositivo pelo utilizador", "ledgerActionNeedPermission": "É preciso autorização", "ledgerActionNeedToConfirm": "Tem de confirmar a transação na sua carteira de hardware. Certifique-se de que está desbloqueada e na aplicação {{chainType}}.", "ledgerActionNeedToConfirmMany": "Tem de confirmar {{numberOfTransactions}} transações na sua carteira de hardware. Certifique-se de que está desbloqueada e na aplicação {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Tem de confirmar a transação na sua carteira de hardware. Certifique-se de que está desbloqueada, na aplicação {{chainType}}, e que tem a assinatura cega ativada.", "ledgerActionNeedToConfirmBlindMany": "Tem de confirmar {{numberOfTransactions}} transações na sua carteira de hardware. Certifique-se de que está desbloqueada, na aplicação {{chainType}}, e que tem a assinatura cega ativada.", "ledgerActionPleaseConnect": "Ligue o seu Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "Ligue a sua carteira de hardware e certifique-se de que está desbloqueada. Certifique-se de que aprovou as permissões no seu browser.", "maxInputAmount": "Mont<PERSON>", "maxInputMax": "Máx.", "notEnoughSolPrimaryText": "SOL insuficientes", "notEnoughSolSecondaryText": "Não tem SOL suficientes na sua carteira para esta transação. Deposite mais e tente outra vez.", "insufficientBalancePrimaryText": "{{tokenSymbol}} insuficientes", "insufficientBalanceSecondaryText": "Não tem {{tokenSymbol}} suficientes na sua carteira para esta transação.", "insufficientBalanceRemaining": "Restante", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "Não tem tokens suficientes", "notEnoughSplTokensDescription": "Não tem tokens suficientes na sua carteira para esta transação. Esta transação será revertida se enviada.", "transactionExpiredPrimaryText": "Transação expirada", "transactionExpiredSecondaryText": "Esperou demasiado tempo para confirmar a transação e esta expirou. Esta transação será revertida se enviada.", "transactionHasWarning": "Aviso da transação", "tokens": "tokens", "notificationApplicationApprovalPermissionsAddressVerification": "Verifique se é dono deste endereço", "notificationApplicationApprovalPermissionsTransactionApproval": "Pedir aprovação para transações", "notificationApplicationApprovalPermissionsViewWalletActivity": "Ver a sua atividade e saldo da conta", "notificationApplicationApprovalParagraphText": "A confirmação permitirá que este website veja saldos e atividade para a conta selecionada.", "notificationApplicationApprovalActionButtonConnect": "Ligar", "notificationApplicationApprovalActionButtonSignIn": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "Permitir a ligação do website?", "notificationApplicationApprovalAutoConfirm": "Confirmação automática das transações", "notificationApplicationApprovalConnectDisclaimer": "Ligar apenas a websites de confiança", "notificationApplicationApprovalSignInDisclaimer": "Iniciar a sessão apenas em websites de confiança", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "A utilização deste website não é segura e o mesmo poderá tentar roubar os seus fundos.", "notificationApplicationApprovalConnectUnknownApp": "Desconhecido", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Não é possível realizar a ligação à aplicação", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Esta aplicação está a tentar ligar a {{appNetworkName}}, mas {{phantomNetworkName}} está selecionado.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Para usar {{networkName}}, entre em Definições do programador → Modo Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Rede desconhecida", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Ligar a outras apps móveis não é atualmente suportado pelo Ledger.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Mude para uma conta não Ledger ou use o browser da app e tente novamente.", "notificationSignatureRequestConfirmTransaction": "Confirmar trans<PERSON>", "notificationSignatureRequestConfirmTransactionCapitalized": "Confirmar trans<PERSON>", "notificationSignatureRequestConfirmTransactions": "Confirmar <PERSON>", "notificationSignatureRequestConfirmTransactionsCapitalized": "Confirmar <PERSON>", "notificationSignatureRequestSignatureRequest": "Pedido de assinatura", "notificationMessageHeader": "Mensagem", "notificationMessageCopied": "Mensagem copiada", "notificationAutoConfirm": "Confirmação automática", "notificationAutoConfirmOff": "<PERSON><PERSON><PERSON>", "notificationAutoConfirmOn": "<PERSON>g", "notificationConfirmFooter": "Confirme apenas se confiar neste website.", "notificationEstimatedTime": "Tempo estimado", "notificationPermissionRequestText": "Trata-se apenas de um pedido de autorização. A transação poderá não ser executada imediatamente.", "notificationBalanceChangesText": "As alterações de saldo são estimadas. Os montantes e ativos envolvidos não são garantidos.", "notificationContractAddress": "Endereço de contrato", "notificationAdvancedDetailsText": "Avançado", "notificationUnableToSimulateWarningText": "Estamos atualmente a calcular as alterações do saldo. Pode tentar mais tarde, ou confirmar se confiar neste website.", "notificationSignMessageParagraphText": "Assinar esta mensagem irá provar que é proprietário da conta selecionada.", "notificationSignatureRequestScanFailedDescription": "Não foi possível ler mensagem em relação a questões de segurança. Avance com cuidado.", "notificationFailedToScan": "Falha ao simular os resultados deste pedido.\nA confirmação não é segura e pode conduzir a perdas.", "notificationScanLoading": "Pedido de digitalização", "notificationTransactionApprovalActionButtonConfirm": "Confirmar", "notificationTransactionApprovalActionButtonBack": "Voltar", "notificationTransactionApprovalEstimatedChanges": "Alterações estimadas", "notificationTransactionApprovalEstimatesBasedOnSimulations": "As estimativas são baseadas em simulações de transações e não são uma garantia", "notificationTransactionApprovalHideAdvancedDetails": "Ocultar detalhes avançados de transações", "notificationTransactionApprovalNetworkFee": "Taxa da rede", "notificationTransactionApprovalNetwork": "Rede", "notificationTransactionApprovalEstimatedTime": "Tempo estimado", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Não foram encontradas alterações que afetem a propriedade do ativo", "notificationTransactionApprovalSolanaAmountRequired": "Montante necessário pela rede Solana para processar a transação", "notificationTransactionApprovalUnableToSimulate": "Não é possível simular. Certifique-se de que confia neste website uma vez que a aprovação pode resultar numa perda de fundos.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Não é possível obter alterações de saldo", "notificationTransactionApprovalViewAdvancedDetails": "Ver detalhes avançados de transações", "notificationTransactionApprovalKnownMalicious": "Esta transação é maliciosa. A assinatura irá levar à perda de fundos.", "notificationTransactionApprovalSuspectedMalicious": "Suspeitamos que esta transação é maliciosa. A aprovação poderá levar à perda de fundos.", "notificationTransactionApprovalNetworkFeeHighWarning": "As taxas de rede são elevadas devido a congestão da rede.", "notificationTransactionERC20ApprovalDescription": "Esta confirmação permitirá a esta aplicação aceder ao seu saldo em qualquer altura, até ao limite indicado abaixo.", "notificationTransactionERC20ApprovalContractAddress": "Endereço de contrato", "notificationTransactionERC20Unlimited": "ilimitado", "notificationTransactionERC20ApprovalTitle": "<PERSON><PERSON><PERSON> gasto de {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "<PERSON><PERSON><PERSON> gasto de {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "<PERSON><PERSON><PERSON> acesso a {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Todos os seus {{tokenSymbol}}", "notificationIncorrectModeTitle": "Modo incorreto", "notificationIncorrectModeInTestnetTitle": "Está no modo Testnet", "notificationIncorrectModeNotInTestnetTitle": "Não está no modo Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} está a tentar usar uma Mainnet, mas está no modo Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} está a tentar usar uma rede de teste, mas não está no modo Testnet", "notificationIncorrectModeInTestnetProceed": "Para continuar, desligue o modo Testnet.", "notificationIncorrectModeNotInTestnetProceed": "Para continuar, ligue o modo Testnet.", "notificationIncorrectEIP712ChainId": "Impedimos que assinasse uma mensagem que não se destinava à rede na qual está atualmente ligado", "notificationIncorrectEIP712ChainIdDescription": "Mensagem pedida {{messageChainId}}, está ligado a {{connectedChainId}}", "notificationUnsupportedNetwork": "Rede não suportada", "notificationUnsupportedNetworkDescription": "Este website está a tentar usar uma rede atualmente não suportada pela Phantom.", "notificationUnsupportedNetworkDescriptionInterpolated": "Para continuar com uma extensão diferente, desligue <1>Definições → Carteira predefinida da aplicação, e selecione Perguntar sempre</1>. Em seguida, atualize a página e volte a ligar.", "notificationUnsupportedAccount": "Conta não suportada", "notificationUnsupportedAccountDescription": "Este website está a tentar usar {{targetChainType}}, que não é suportado por esta conta {{chainType}}.", "notificationUnsupportedAccountDescription2": "Mude para uma conta de chave privada ou de frase semente compatível e tente outra vez.", "notificationInvalidTransaction": "Transação inválida", "notificationInvalidTransactionDescription": "A transação recebida desta aplicação está formada incorretamente e não deve ser enviada. Contacte o programador desta aplicação para lhe comunicar o problema.", "notificationCopyTransactionText": "Copiar transação", "notificationTransactionCopied": "Transação copiada", "onboardingImportOptionsPageTitle": "Importar uma carteira", "onboardingImportOptionsPageSubtitle": "Importar uma carteira existente com a sua frase secreta, chave privada, ou carteira de hardware.", "onboardingImportPrivateKeyPageTitle": "Importar uma chave privada", "onboardingImportPrivateKeyPageSubtitle": "Importar uma carteira de cadeia única existente", "onboardingCreatePassword": "Criar uma palavra-passe", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<PERSON>ito o<PERSON> <1>termos de serviço</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Confirmar palavra-passe", "onboardingCreatePasswordDescription": "<PERSON><PERSON><PERSON> utiliz<PERSON>-la para desbloquear a sua carteira.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Frase secreta de recuperação inválida", "onboardingCreatePasswordPasswordPlaceholder": "Palavra-passe", "onboardingCreatePasswordPasswordStrengthWeak": "Fraca", "onboardingCreatePasswordPasswordStrengthMedium": "Média", "onboardingCreatePasswordPasswordStrengthStrong": "Forte", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Guardei a minha frase secreta de recuperação", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Frase secreta de recuperação", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Esta frase é a ÚNICA forma de recuperar a sua carteira. NÃO a partilhe com ninguém!", "onboardingImportWallet": "Importar carteira", "onboardingImportWalletImportExistingWallet": "Importar uma carteira existente com a sua frase secreta de recuperação de 12 ou 24 palavras.", "onboardingImportWalletRestoreWallet": "Restaurar a carteira", "onboardingImportWalletSecretRecoveryPhrase": "Frase secreta de recuperação", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Frase secreta de recuperação inválida", "onboardingImportWalletIHaveWords": "Tenho uma frase de recuperação de {{numWords}} palavra(s)", "onboardingImportWalletIncorrectOrMisspelledWord": "A palavra {{wordIndex}} está incorreta ou mal escrita", "onboardingImportWalletIncorrectOrMisspelledWords": "As palavras {{wordIndexes}} estão incorretas ou mal escritas", "onboardingImportWalletScrollDown": "Navegar para baixo", "onboardingImportWalletScrollUp": "Navegar para cima", "onboardingSelectAccountsImportAccounts": "Importar contas", "onboardingSelectAccountsImportAccountsDescription": "Escolher contas de carteiras a importar.", "onboardingSelectAccountsImportSelectedAccounts": "Importar contas selecionadas", "onboardingSelectAccountsFindMoreAccounts": "Encontrar mais contas", "onboardingSelectAccountsFindMoreNoneFound": "Nenhuma conta encontrada", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} contas selecionadas", "onboardingSelectAccountSelectAllText": "Selecionar tudo", "onboardingAdditionalPermissionsTitle": "Utilizar aplicações com a Phantom", "onboardingAdditionalPermissionsSubtitle": "Para uma utilização dinâmica de aplicações, recomendamos permitir que a Phantom leia e realize alterações de dados em todos os locais.", "interstitialAdditionalPermissionsTitle": "Utilizar aplicações com a Phantom", "interstitialAdditionalPermissionsSubtitle": "Para continuar a sua experiência utilizando aplicações sem interrupção, recomendamos que permita que a Phantom leia e altere dados em todos os locais.", "recentActivityPrimaryText": "Atividade recente", "removeAccountActionButtonRemove": "Remover", "removeAccountRemoveWallet": "Remover conta", "removeAccountInterpolated": "Remover {{accountName}}", "removeAccountWarningLedger": "Apesar de estar a remover esta carteira da Phantom, poderá voltar a adicioná-la usando o fluxo \"Ligar a carteira de hardware\".", "removeAccountWarningSeedVault": "Apesar de estar a remover esta carteira da Phantom, poderá voltar a adicioná-la usando o fluxo \"Ligar a carteira do cofre de sementes\".", "removeAccountWarningPrivateKey": "Assim que remover esta carteira, a Phantom não irá poder recuperá-la se mudar de ideias. Certifique-se de que tem uma cópia de segurança da sua chave privada.", "removeAccountWarningSeed": "Apesar de estar a remover esta carteira da Phantom, poderá voltar a rederivá-la usando a sua mnemónica nesta ou em outra carteira.", "removeAccountWarningReadOnly": "Eliminar esta conta não irá afetar a sua carteira, uma vez que esta é uma carteira apenas de visualização.", "removeSeedPrimaryText": "A remover a frase secreta {{number}}", "removeSeedSecondaryText": "<PERSON><PERSON> ir<PERSON> remover todas as contas existentes na frase secreta {{number}}. Certifique-se de que tem a sua frase secreta existente guardada.", "resetSeedPrimaryText": "Repor aplicação com nova frase secreta", "resetSeedSecondaryText": "<PERSON><PERSON> irá remover todas as contas existentes e substitui-las por novas. Certifique-se de que tem cópias de segurança da sua frase secreta e das chaves privadas.", "resetAppPrimaryText": "Repor e limpar aplicação", "resetAppSecondaryText": "<PERSON><PERSON> irá remover todas as contas e dados existentes. Certifique-se de que tem cópias de segurança da sua frase secreta e das chaves privadas.", "richTransactionsDays": "dias", "richTransactionsToday": "Hoje", "richTransactionsYesterday": "Ontem", "richTransactionDetailAccount": "Conta", "richTransactionDetailAppInteraction": "Interação com aplicação", "richTransactionDetailAt": "em", "richTransactionDetailBid": "Licitação", "richTransactionDetailBidDetails": "Dados de licitação", "richTransactionDetailBought": "Comprou", "richTransactionDetailBurned": "Que<PERSON>do", "richTransactionDetailCancelBid": "Cancelar licit<PERSON>", "richTransactionDetailCompleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailConfirmed": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailDate": "Data", "richTransactionDetailFailed": "Fal<PERSON>", "richTransactionDetailFrom": "De", "richTransactionDetailItem": "<PERSON><PERSON>", "richTransactionDetailListed": "Listado", "richTransactionDetailListingDetails": "Dados de listagem", "richTransactionDetailListingPrice": "Preço de listagem", "richTransactionDetailMarketplace": "<PERSON><PERSON><PERSON>", "richTransactionDetailNetworkFee": "Taxa da rede", "richTransactionDetailOriginalListingPrice": "Preço de listagem original", "richTransactionDetailPending": "Pendente", "richTransactionDetailPrice": "Preço", "richTransactionDetailProvider": "Fornecedor", "richTransactionDetailPurchaseDetails": "Dados de compra", "richTransactionDetailRebate": "Desconto", "richTransactionDetailReceived": "Recebido", "richTransactionDetailSaleDetails": "<PERSON><PERSON><PERSON> de venda", "richTransactionDetailSent": "Enviado", "richTransactionDetailSold": "<PERSON><PERSON><PERSON>", "richTransactionDetailStaked": "Staked", "richTransactionDetailStatus": "Estado", "richTransactionDetailSwap": "Trocar", "richTransactionDetailSwapDetails": "Detalhes da troca", "richTransactionDetailTo": "Para", "richTransactionDetailTokenSwap": "Trocar token", "richTransactionDetailUnknownNFT": "NFT desconhecido", "richTransactionDetailUnlisted": "Remo<PERSON><PERSON> da lista", "richTransactionDetailUnstaked": "Staked removido", "richTransactionDetailValidator": "Validador", "richTransactionDetailViewOnExplorer": "Ver em {{explorer}}", "richTransactionDetailWithdrawStake": "Levantar staking", "richTransactionDetailYouPaid": "Pago<PERSON>", "richTransactionDetailYouReceived": "Recebeu", "richTransactionDetailUnwrapDetails": "Dados de remoção de wrapping", "richTransactionDetailTokenUnwrap": "Unwrapping de token", "activityItemsRefreshFailed": "Falha ao carregar transações mais recentes.", "activityItemsPagingFailed": "Falha ao carregar transações mais antigas.", "activityItemsTestnetNotAvailable": "Histórico de transações Testnet não disponível neste momento", "historyUnknownDappName": "Desconhecido", "historyStatusSucceeded": "Com sucesso", "historyNetwork": "Rede", "historyAttemptedAmount": "Montante da tentativa", "historyAmount": "Mont<PERSON>", "sendAddressBookButtonLabel": "Livro de endereços", "addressBookSelectAddressBook": "Livro de endereços", "sendAddressBookNoAddressesSaved": "Nenhum endereço gravado", "sendAddressBookRecentlyUsed": "Usado recentemente", "addressBookSelectRecentlyUsed": "Usado recentemente", "sendConfirmationLabel": "Etiqueta", "sendConfirmationMessage": "Mensagem", "sendConfirmationNetworkFee": "Taxa da rede", "sendConfirmationPrimaryText": "Confirmar envio", "sendWarning_INSUFFICIENT_FUNDS": "Fundos insuficientes, esta transação irá provavelmente falhar se for submetida.", "sendFungibleSummaryNetwork": "Rede", "sendFungibleSummaryNetworkFee": "Taxa da rede", "sendFungibleSummaryEstimatedTime": "Tempo estimado", "sendFungiblePendingEstimatedTime": "Estimativa de tempo", "sendFungibleSummaryEstimatedTimeDescription": "As velocidades de transações Ethereum variam com base em vários fatores. Pode acelerá-las clicando em “Taxa de rede”.", "sendSummaryBitcoinPendingTxTitle": "Não foi possível enviar a transferência", "sendSummaryBitcoinPendingTxDescription": "Só pode ter uma transferência BTC pendente de cada vez. Aguarde até que esteja concluída para enviar uma nova transferência.", "sendFungibleSatProtectionTitle": "Envio com proteção Sat", "sendFungibleSatProtectionExplainer": "A Phantom assegura que Ordinal e BRC20 não serão usados para taxas de transação ou transferências Bitcoin.", "sendFungibleTransferFee": "Taxa de transferência de token", "sendFungibleTransferFeeToolTip": "O criador deste token recebe uma taxa de cada transferência. Não é uma taxa recolhida ou cobrada pela Phantom.", "sendFungibleInterestBearingPercent": "Taxa de juro atual", "sendFungibleNonTransferable": "Não transferível", "sendFungibleNonTransferableToolTip": "Este token não pode ser transferido para outra conta.", "sendFungibleNonTransferableYes": "<PERSON>m", "sendStatusErrorMessageInterpolated": "Ocorreu um erro a tentar enviar tokens para <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "Não tem saldo suficiente para completar a transação.", "sendStatusErrorTitle": "Não foi possível enviar", "sendStatusLoadingTitle": "A enviar...", "sendStatusSuccessMessageInterpolated": "Os seus tokens foram enviados com sucesso para <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Enviado!", "sendStatusConfirmedSuccessTitle": "Enviado!", "sendStatusSubmittedSuccessTitle": "Transação enviada", "sendStatusEstimatedTransactionTime": "Tempo previsto da transação: {{time}}", "sendStatusViewTransaction": "Ver transação", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> a <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> foi enviado com sucesso para <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> foi enviado com sucesso para <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> não foi enviado para <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON><PERSON><PERSON> de er<PERSON> {{code}}", "sendFormErrorInsufficientBalance": "<PERSON><PERSON> insuficiente", "sendFormErrorEmptyAmount": "Número necess<PERSON>", "sendFormInvalidAddress": "Endereço {{assetName}} inválido", "sendFormInvalidUsernameOrAddress": "Nome de utilizador ou endereço inválido", "sendFormErrorInvalidSolanaAddress": "Endereço Solana inválido", "sendFormErrorInvalidTwitterHandle": "Este atributo Twitter não está registado", "sendFormErrorInvalidDomain": "Este domínio não está registado", "sendFormErrorInvalidUsername": "Este nome de utilizador não está registado", "sendFormErrorMinRequiredInterpolated": "<PERSON><PERSON> menos {{minAmount}} {{tokenName}} necessário(s)", "sendRecipientTextareaPlaceholder": "Endereço SOL do destinatário", "sendRecipientTextAreaPlaceholder2": "Endereço {{symbol}} do destinatário", "sendMemoOptional": "Memo (opcional)", "sendMemo": "<PERSON><PERSON>and<PERSON>", "sendOptional": "opcional", "settings": "Definições", "settingsDapps": "dApps", "settingsSelectedAccount": "Conta selecionada", "settingsAddressBookNoLabel": "Sem etiqueta", "settingsAddressBookPrimary": "Livro de endereços", "settingsAddressBookRecentlyUsed": "Usado recentemente", "settingsAddressBookSecondary": "<PERSON><PERSON><PERSON> endereços usados recentemente", "settingsAutoLockTimerPrimary": "Temporizador de bloqueio auto", "settingsAutoLockTimerSecondary": "Alterar a duração do seu temporizador de bloqueio automático", "settingsChangeLanguagePrimary": "Alterar idioma", "settingsChangeLanguageSecondary": "Alterar o idioma de visualização", "settingsChangeNetworkPrimary": "Alterar a rede", "settingsChangeNetworkSecondary": "Configurar as suas definições de rede", "settingsChangePasswordPrimary": "Alterar palavra-passe", "settingsChangePasswordSecondary": "Alterar a sua palavra-passe de bloqueio de ecrã", "settingsCompleteBetaSurvey": "Completar inquérito beta", "settingsDisplayLanguage": "Idioma de visualização", "settingsErrorCannotExportLedgerPrivateKey": "Não é possível exportar a chave privada do Ledger", "settingsErrorCannotRemoveAllWallets": "Não é possível remover todas as contas", "settingsExportPrivateKey": "Mostrar chave privada", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Rede RPC Phantom", "settingsTestNetworks": "Redes de teste", "settingsUseCustomNetworks": "Utilizar redes personalizadas", "settingsTestnetMode": "Modo Testnet", "settingsTestnetModeDescription": "Aplicável a saldos e ligações de aplicação.", "settingsWebViewDebugging": "Depuração de vista Web", "settingsWebViewDebuggingDescription": "Permite-lhe inspecionar e depurar as vistas Web do navegador na aplicação.", "settingsTestNetworksInfo": "Mudar para qualquer rede de teste destina-se apenas a efeitos de teste. Tenha em atenção que tokens nas redes de teste não têm qualquer valor monetário.", "settingsEmojis": "Emojis", "settingsNoAddresses": "Nenhum endereço", "settingsAddressBookEmptyHeading": "O seu livro de endereços está vazio", "settingsAddressBookEmptyText": "Clique nos botões “+” ou “Adicionar endereço” para adicionar os seus endereços favoritos", "settingsEditWallet": "<PERSON>ar conta", "settingsNoTrustedApps": "Nenhuma aplicação de confiança", "settingsNoConnections": "Ainda não existem ligações.", "settingsRemoveWallet": "Remover conta", "settingsResetApp": "Repor aplicação", "settingsBlocked": "Bloqueado", "settingsBlockedAccounts": "Contas bloqueadas", "settingsNoBlockedAccounts": "Nenhuma conta bloqueada.", "settingsRemoveSecretPhrase": "Remover frase secreta", "settingsResetAppWithSecretPhrase": "Repor aplicação com frase secreta", "settingsResetSecretRecoveryPhrase": "Repor frase secreta de recuperação", "settingsShowSecretRecoveryPhrase": "Mostrar frase secreta de recuperação", "settingsShowSecretRecoveryPhraseSecondary": "Mostrar frase de recuperação", "settingsShowSecretRecoveryPhraseTertiary": "Mostrar frase de secreta", "settingsTrustedAppsAutoConfirmActiveUntil": "Até {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Confirmação automática", "settingsTrustedAppsDisclaimer": "Ativar a confirmação automática só em websites de confiança", "settingsTrustedAppsLastUsed": "Utilizado há {{formattedTimestamp}} atrás", "settingsTrustedAppsPrimary": "Aplicações ligadas", "settingsTrustedApps": "Aplicações de confiança", "settingsTrustedAppsRevoke": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsRevokeToast": "{{trustedApp}} desligada", "settingsTrustedAppsSecondary": "Configurar as suas aplicações de confiança", "settingsTrustedAppsToday": "Hoje", "settingsTrustedAppsYesterday": "Ontem", "settingsTrustedAppsLastWeek": "Semana passada", "settingsTrustedAppsBeforeYesterday": "<PERSON><PERSON>", "settingsTrustedAppsDisconnectAll": "<PERSON><PERSON><PERSON> de todos", "settingsTrustedAppsDisconnectAllToast": "Todas as aplicações desligadas", "settingsTrustedAppsEndAutoConfirmForAll": "Terminar confirmação automática para todos", "settingsTrustedAppsEndAutoConfirmForAllToast": "<PERSON>rminadas todas as sessões de confirmação automática", "settingsSecurityPrimary": "Segurança e privacidade", "settingsSecuritySecondary": "Atualizar as suas definições de segurança", "settingsActiveNetworks": "Redes ativas", "settingsActiveNetworksAll": "<PERSON><PERSON>", "settingsActiveNetworksSolana": "A<PERSON>as <PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana é a rede predefinida e está sempre ativa.", "settingsDeveloperPrimary": "Definições do programador", "settingsAdvanced": "Definições avançadas", "settingsTransactions": "Definições da transação", "settingsAutoConfirm": "Definições de confirmação automática", "settingsSecurityAnalyticsPrimary": "<PERSON><PERSON><PERSON> an<PERSON> anón<PERSON>", "settingsSecurityAnalyticsSecondary": "<PERSON><PERSON><PERSON>-nos a mel<PERSON>r", "settingsSecurityAnalyticsHelper": "A Phantom não usa a sua informação pessoal para efeitos de análise", "settingsSuspiciousCollectiblesPrimary": "Ocultar colecionáveis suspeitos", "settingsSuspiciousCollectiblesSecondary": "Alternar para ocultar colecionáveis assinalados", "settingsPreferredBitcoinAddress": "Endereço de bitcoin preferido", "settingsEnabledAddressesUpdated": "Endereços visíveis atualizados!", "settingsEnabledAddresses": "Endereços ativados", "settingsBitcoinPaymentAddressForApps": "Endereço de pagamentos para aplicações", "settingsBitcoinOrdinalsAddressForApps": "Endereço Ordinals para aplicações", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Quando os dois tipos de endereço acima estão ativados, para determinadas aplicações como Magic Eden, o seu endereço Native Segwit será usado para financiar as compras. Os ativos comprados serão recebidos no seu endereço Taproot.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "O endereço de bitcoin predefinido na Phantom para assegurar a compatibillidade.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Predefinido)", "settingsPreferredBitcoinAddressTaprootExplainer": "O tipo de endereço mais moderno, geralmente com taxas de transação mais baixas.", "settingsPreferredExplorers": "Explorador preferido", "settingsPreferredExplorersSecondary": "Mudar para o seu explorador de blockchain preferido", "settingsCustomGasControls": "Controlos de gasolina personalizados", "settingsSupportDesk": "Balcão de apoio", "settingsSubmitATicket": "Enviar um pedido", "settingsAttachApplicationLogs": "Anexar registos da aplicação", "settingsDownloadApplicationLogs": "Descarregar registos da aplicação", "settingsDownloadApplicationLogsShort": "<PERSON><PERSON><PERSON><PERSON> registos", "settingsDownloadApplicationLogsHelper": "Contém dados locais, relatórios de falhas e endereços de carteiras públicas para ajudar a Assistência da Phantom a resolver problemas", "settingsDownloadApplicationLogsWarning": "Não estão incluídos dados sensíveis como, por exemplo, frases semente ou chaves privadas.", "settingsWallet": "<PERSON><PERSON><PERSON>", "settingsPreferences": "Preferências", "settingsSecurity": "Segurança", "settingsDeveloper": "Programador", "settingsSupport": "Suporte", "settingsWalletShortcutsPrimary": "Mostrar atal<PERSON> da carteira", "settingsAppIcon": "Ícone da app", "settingsAppIconDefault": "Predefinido", "settingsAppIconLight": "<PERSON><PERSON><PERSON>", "settingsAppIconDark": "Escuro", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "Conta", "settingsSearchResultSelected": "Selecionado", "settingsSearchResultExport": "Exportar", "settingsSearchResultSeed": "Semente", "settingsSearchResultTrusted": "De confiança", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Estado", "settingsSearchResultLogs": "Registos", "settingsSearchResultBiometric": "Biométrico", "settingsSearchResultTouch": "<PERSON><PERSON><PERSON>", "settingsSearchResultFace": "Rosto", "settingsSearchResultShortcuts": "Atalhos", "settingsAllSitesPermissionsTitle": "Aceder a Phantom em todos os locais", "settingsAllSitesPermissionsSubtitle": "Permite-lhe usar aplicações de forma dinâmica com a Phantom sem ter de clicar na extensão", "settingsAllSitesPermissionsDisabled": "O seu navegador não suporta a alteração desta definição", "settingsSolanaCopyTransaction": "Ativar cópia de transação", "settingsSolanaCopyTransactionDetails": "Copiar dados de transação em série para a área de transferência", "settingsAutoConfirmHeader": "Confirmação automática", "refreshWebpageToApplyChanges": "Atualize a página Web para aplicar as alterações", "settingsExperimentalTitle": "Funcionalidades experimentais", "settingsExprimentalSolanaActionsSubtitle": "Expanda automaticamente os botões de ação Solana quando forem detetadas ligações relevantes em X.com", "stakeAccountCardActiveStake": "Staking ativo", "stakeAccountCardBalance": "<PERSON><PERSON>", "stakeAccountCardRentReserve": "Reserva de renda", "stakeAccountCardRewards": "Última recompensa", "stakeAccountCardRewardsTooltip": "Esta é a recompensa mais recente que ganhou com o staking. Recebe a recompensa a cada 3 dias.", "stakeAccountCardStakeAccount": "Endereço", "stakeAccountCardLockup": "Bloquear até", "stakeRewardsHistoryTitle": "Histórico de recompensas", "stakeRewardsActivityItemTitle": "Recompensas", "stakeRewardsHistoryEmptyList": "<PERSON>enhum<PERSON> recompensa", "stakeRewardsTime_zero": "Hoje", "stakeRewardsTime_one": "Ontem", "stakeRewardsTime_other": "{{count}} dias atrás", "stakeRewardsItemsPagingFailed": "Falha ao carregar recompensas mais antigas.", "stakeAccountCreateAndDelegateErrorStaking": "Ocorreu um problema com o staking para este validador. Tente outra vez.", "stakeAccountCreateAndDelegateSolStaked": "SOL a ganhar renda!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Os seus SOL vão começar a ganhar recompensas <1></1> nos próximos dias assim que a conta de staking ficar ativa.", "stakeAccountCreateAndDelegateStakingFailed": "<PERSON><PERSON><PERSON> de staking", "stakeAccountCreateAndDelegateStakingSol": "A fazer staking dos SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Estamos a criar uma conta de staking e, em seguida, iremos delegar os seus SOL a", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Estamos a criar uma conta de staking e, em seguida, iremos delegar os seus SOL a {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Ver transação", "stakeAccountDeactivateStakeSolUnstaked": "SOL sem staking!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Poderá levantar o valor do staking <1></1> nos próximos dias assim que a conta de staking ficar ativa.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Poderá levantar o valor do staking nos próximos dias assim que a conta de staking ficar inativa.", "stakeAccountDeactivateStakeUnstakingFailed": "Falha de remoção de staking", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Ocorreu um problema com a remoção do staking deste validador. Tente outra vez.", "stakeAccountDeactivateStakeUnstakingSol": "A remover staking de SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Estamos a iniciar o processo para remover o staking dos seus SOL.", "stakeAccountDeactivateStakeViewTransaction": "Ver transação", "stakeAccountDelegateStakeSolStaked": "SOL a ganhar renda!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Os seus SOL vão começar a ganhar recompensas <1></1> nos próximos dias assim que a conta de staking ficar ativa.", "stakeAccountDelegateStakeStakingFailed": "<PERSON><PERSON><PERSON> de staking", "stakeAccountDelegateStakeStakingFailedDescription": "Ocorreu um problema com o staking para este validador. Tente outra vez.", "stakeAccountDelegateStakeStakingSol": "A fazer staking de SOL...", "stakeAccountDelegateStakeStakingSolDescription": "Estamos a delegar os seus SOL.", "stakeAccountDelegateStakeViewTransaction": "Ver transação", "stakeAccountListActivationActivating": "A ativar", "stakeAccountListActivationActive": "Ativo", "stakeAccountListActivationInactive": "Inativo", "stakeAccountListActivationDeactivating": "A desativar", "stakeAccountListErrorFetching": "<PERSON>ão conseguimos obter as contas de staking. Tente outra vez mais tarde.", "stakeAccountListNoStakingAccounts": "Não existem contas de staking", "stakeAccountListReload": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountListViewPrimaryText": "O seu staking", "stakeAccountListViewStakeSOL": "Staking dos SOL", "stakeAccountListItemStakeFee": "Taxa de {{fee}}", "stakeAccountViewActionButtonRestake": "Restaking", "stakeAccountViewActionButtonUnstake": "Tirar staking", "stakeAccountViewError": "Erro", "stakeAccountViewPrimaryText": "O seu staking", "stakeAccountViewRestake": "Restaking", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Os seus SOL estão atualmente em staking com o validador. Terá de remover o staking para <1></1>aceder a estes fundos. <3><PERSON>ber mais</3>", "stakeAccountViewStakeInactive": {"part1": "Esta conta de staking está inativa. Considere levantar o staking ou encontrar um validador a quem delegar.", "part2": "<PERSON>ber mais"}, "stakeAccountViewStakeNotFound": "Não foi possível encontrar esta conta de staking.", "stakeAccountViewViewOnExplorer": "Ver em {{explorer}}", "stakeAccountViewWithdrawStake": "Levantar staking", "stakeAccountViewWithdrawUnstakedSOL": "Levantar SOL sem staking", "stakeAccountInsufficientFunds": "Não existem SOL suficientes disponíveis para cancelar stake ou fazer levantamento.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL levantados!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Os seus SOL foram levantados.", "part2": "Esta conta de staking será removida automaticamente nos próximos minutos."}, "stakeAccountWithdrawStakeViewTransaction": "Ver transação", "stakeAccountWithdrawStakeWithdrawalFailed": "Falha de levantamento", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Ocorreu um problema com o levantamento desta conta de staking. Tente outra vez.", "stakeAccountWithdrawStakeWithdrawingSol": "A levantar os SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Estamos a levantar os seus SOL desta conta de staking.", "startEarningSolAccount": "conta", "startEarningSolAccounts": "contas", "startEarningSolErrorClosePhantom": "Toque aqui e tente outra vez", "startEarningSolErrorTroubleLoading": "Problemas de carregamento de staking", "startEarningSolLoading": "A carregar...", "startEarningSolPrimaryText": "Começar a ganhar SOL", "startEarningSolSearching": "A procurar contas de staking", "startEarningSolStakeTokens": "Staking de tokens e ganhar recompensas", "startEarningSolYourStake": "O seu staking", "unwrapFungibleTitle": "Trocar para {{tokenSymbol}}", "unwrapFungibleDescription": "Levantar de {{fromToken}} para {{toToken}}", "unwrapFungibleConfirmSwap": "Confirmar troca", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Taxas estimadas", "swapFeesFees": "Taxas", "swapFeesPhantomFee": "Taxa Phantom", "swapFeesPhantomFeeDisclaimer": "Encontramos sempre o melhor preço possível junto dos principais fornecedores de liquidez. Uma taxa de {{feePercentage}} é automaticamente incluída neste orçamento.", "swapFeesRate": "Preço", "swapFeesRateDisclaimer": "O melhor preço encontrado pelo Jupiter Aggregator em múltiplos câmbios descentralizados.", "swapFeesRateDisclaimerMultichain": "O melhor preço encontrado em múltiplos câmbios descentralizados.", "swapFeesPriceImpact": "Impacto do preço", "swapFeesHighPriceImpact": "Impacto de preço mais alto", "swapFeesPriceImpactDisclaimer": "A diferença entre o preço de mercado e o preço calculado com base na dimensão da transação.", "swapFeesSlippage": "Deslizamento", "swapFeesHighSlippage": "Elevada tolerância de deslizamento", "swapFeesHighSlippageDisclaimer": "A sua transação irá falhar se o preço se alterar de forma desfavorável mais do que {{slippage}}%.", "swapTransferFee": "Taxa de transferência", "swapTransferFeeDisclaimer": "Negociar ${{symbol}} incorre uma taxa de transferência de {{feePercent}}% definida pelo criador do token, não pela Phantom.", "swapTransferFeeDisclaimerMany": "Negociar os tokens selecionados incorre uma taxa de transferência de {{feePercent}}% definida pelos criadores do token, não pela Phantom.", "swapFeesSlippageDisclaimer": "Valor que o preço da sua negociação pode desviar em relação ao orçamento fornecido.", "swapFeesProvider": "Fornecedor", "swapFeesProviderDisclaimer": "O câmbio descentralizado usado para completar a sua transação.", "swapEstimatedTime": "Tempo estimado", "swapEstimatedTimeShort": "Tempo previsto", "swapEstimatedTimeDisclaimer": "O tempo estimado de conclusão da ponte irá variar consoante vários fatores que afetam as velocidades das transações.", "swapSettingsButtonCommand": "Abrir definições de troca", "swapQuestionRetry": "Repetir?", "swapUnverifiedTokens": "Tokens não verificados", "swapSectionTitleTokens": "Tokens de {{section}}", "swapFlowYouPay": "Paga", "swapFlowYouReceive": "Recebe", "swapFlowActionButtonText": "Rever ordem", "swapAssetCardTokenNetwork": "{{symbol}} em {{network}}", "swapAssetCardMaxButton": "Máx.", "swapAssetCardSelectTokenAndNetwork": "Selecionar token e rede", "swapAssetCardBuyTitle": "Recebe", "swapAssetCardSellTitle": "Paga", "swapAssetWarningUnverified": "Este token não está verificado. Interaja apenas com tokens em que confia.", "swapAssetWarningPermanentDelegate": "Um delegado pode queimar ou transferir automaticamente estes tokens.", "swapSlippageSettingsTitle": "Definições de deslizamento", "swapSlippageSettingsSubtitle": "A sua transação irá falhar se o preço mudar mais do que o deslizamento. Um valor demasiado alto irá resultar num negócio desfavorável.", "swapSlippageSettingsCustom": "Personalizar", "swapSlippageSettingsHighSlippageWarning": "A sua transação pode ser alvo de frontrunning e resultar num negócio desfavorável.", "swapSlippageSettingsCustomMinError": "Introduza um valor superior a {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "Introduza um valor inferior a {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "Introduza um valor válido.", "swapSlippageSettingsAutoSubtitle": "A Phantom irá encontrar o deslizamento mais baixo para uma troca com sucesso.", "swapSlippageSettingsAuto": "Autom.", "swapSlippageSettingsFixed": "Fixo", "swapSlippageOptInTitle": "Deslizamento automático", "swapSlippageOptInSubtitle": "A Phantom irá encontrar o deslizamento mais baixo para uma troca com sucesso. Pode alterar isto em qualquer altura em Substituidor → Definições de deslizamento.", "swapSlippageOptInEnableOption": "Ativar deslizamento automático", "swapSlippageOptInRejectOption": "Continuar com deslizamento fixo", "swapQuoteFeeDisclaimer": "O orçamento inclui uma taxa Phantom de {{feePercentage}}", "swapQuoteMissingContext": "Contexto de dados de substituição em falta", "swapQuoteErrorNoQuotes": "Tentativa de substituição sem dados", "swapQuoteSolanaNetwork": "Rede Solana", "swapQuoteNetwork": "Rede", "swapQuoteOneTimeSerumAccount": "Conta Serum única", "swapQuoteOneTimeTokenAccount": "Conta de token único", "swapQuoteBridgeFee": "Taxa de troca de cadeia cruzada", "swapQuoteDestinationNetwork": "Rede de destino", "swapQuoteLiquidityProvider": "Fornecedor de liquidez", "swapReviewFlowActionButtonPrimary": "Substituir", "swapReviewFlowPrimaryText": "Rever ordem", "swapReviewFlowYouPay": "Paga", "swapReviewFlowYouReceive": "Recebe", "swapReviewInsufficientBalance": "Fundos insuficientes", "ugcSwapWarningTitle": "Aviso", "ugcSwapWarningBody1": "Este token está a ser negociado no lançador de tokens {{programName}}.", "ugcSwapWarningBody2": "O valor destes tokens pode flutuar muito, conduzindo a ganhos ou perdas substanciais. Negoceie segundo a sua própria conta e risco.", "ugcSwapWarningConfirm": "<PERSON><PERSON><PERSON><PERSON>", "bondingCurveProgressLabel": "Progresso da bonding curve", "bondingCurveInfoTitle": "Bonding curve", "bondingCurveInfoDescription": "Num modelo de bonding curve, os preços dos token são determinados pela forma da curva, aumentando à medida que mais tokens são comprados e diminuindo à medida que os tokens são vendidos. Quando os tokens são esgotados, toda a liquidez será depositada na Raydium e queimada.", "ugcFungibleWarningBanner": "Este token está a ser negociado em {{programName}}", "ugcCreatedRowLabel": "C<PERSON><PERSON> em", "ugcStatusRowLabel": "Estado", "ugcStatusRowValue": "<PERSON><PERSON><PERSON><PERSON>", "swapTxConfirmationReceived": "Recebido!", "swapTxConfirmationSwapFailed": "Falha de substituição", "swapTxConfirmationSwapFailedStaleQuota": "O orçamento já não é válido. Tente outra vez.", "swapTxConfirmationSwapFailedSlippageLimit": "O deslizamento é demasiado baixo para esta troca. Aumente o seu deslizamento na parte superior do ecrã de troca e tente outra vez.", "swapTxConfirmationSwapFailedInsufficientBalance": "Não nos foi possível completar o pedido. Não tem saldo suficiente para completar a transação.", "swapTxConfirmationSwapFailedEmptyRoute": "A liquidez deste par de token foi alterada. Não foi possível encontrar um orçamento adequado. Tente outra vez ou ajuste os valores do token.", "swapTxConfirmationSwapFailedAcountFrozen": "Este token foi congelado pelo seu criador. Não pode enviar ou trocar este token.", "swapTxConfirmationSwapFailedTryAgain": "A substituição falhou, tente outra vez", "swapTxConfirmationSwapFailedUnknownError": "Não nos foi possível completar a troca. Nenhum dos seus fundos foi afetado. Tente outra vez. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Não nos foi possível simular a troca. Nenhum dos seus fundos foi afetado. Tente outra vez.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Não nos foi possível completar a troca. Nenhum dos seus fundos foi afetado. Tente outra vez. ", "swapTxConfirmationSwapFailedInsufficientGas": "A sua conta não tem fundos suficientes para completar a transação. Adicione mais fundos à sua conta e tente outra vez.", "swapTxConfirmationSwapFailedLedgerReject": "A troca foi rejeitada pelo utilizador no dispositivo de hardware.", "swapTxConfirmationSwapFailedLedgerConnectionError": "A troca foi rejeitada devido a um erro de ligação do dispositivo. Tente outra vez.", "swapTxConfirmationSwapFailedLedgerSignError": "A troca foi rejeitada devido a um erro de assinatura do dispositivo. Tente outra vez.", "swapTxConfirmationSwapFailedLedgerError": "A troca foi rejeitada devido a um erro do dispositivo. Tente outra vez.", "swapTxConfirmationSwappingTokens": "A substituir tokens...", "swapTxConfirmationTokens": "Tokens", "swapTxConfirmationTokensDeposited": "Feito! Os tokens foram depositados na sua carteira.", "swapTxConfirmationTokensDepositedTitle": "<PERSON><PERSON>!", "swapTxConfirmationTokensDepositedBody": "Os tokens foram depositados na sua carteira", "swapTxConfirmationTokensWillBeDeposited": "serão depositados na sua carteira depois da transação estar concluída", "swapTxConfirmationViewTransaction": "Ver transação", "swapTxBridgeSubmitting": "A enviar transação", "swapTxBridgeSubmittingDescription": "A trocar {{sellAmount}} em {{sellNetwork}} por {{buyAmount}} em {{buyNetwork}}", "swapTxBridgeFailed": "A transação não foi enviada", "swapTxBridgeFailedDescription": "Não nos foi possível completar o pedido.", "swapTxBridgeSubmitted": "Transação enviada", "swapTxBridgeSubmittedDescription": "Tempo previsto da transação: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "Pode dispensar esta janela em segurança.", "swapperSwitchTokens": "Mudar tokens", "swapperMax": "Máx.", "swapperTooltipNetwork": "Rede", "swapperTooltipPrice": "Preço", "swapperTooltipAddress": "Contrato", "swapperTrendingSortBy": "Ordenar por", "swapperTrendingTimeFrame": "Intervalo temporal", "swapperTrendingNetwork": "Rede", "swapperTrendingRank": "Nível", "swapperTrendingTokens": "Tokens populares", "swapperTrendingVolume": "Volume", "swapperTrendingPrice": "Preço", "swapperTrendingPriceChange": "Alteração do preço", "swapperTrendingMarketCap": "Limite de mercado", "swapperTrendingTimeFrame1h": "1 h", "swapperTrendingTimeFrame24h": "24 h", "swapperTrendingTimeFrame7d": "7 d", "swapperTrendingTimeFrame30d": "30 d", "swapperTrendingNoTokensFound": "Nenhum token encontrado.", "switchToggle": "Alternar", "termsOfServiceActionButtonAgree": "<PERSON><PERSON>", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Ao clicar em <1>\"Aceito\"</1> está a aceitar os <3>termos e condições</3> de substituição de tokens com a Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Revemos os nossos termos de serviço. Ao clicar em <1>\"Aceito\"</1> está a aceitar os nossos novos <3>termos de serviço</3>.<5></5><6></6>Os nossos termos de serviço incluem uma nova <8>estrutura de taxas</8> para determinados produtos.", "termsOfServicePrimaryText": "Termos de serviço", "tokenRowUnknownToken": "Token desconhecido", "transactionsAppInteraction": "Interação com aplicação", "transactionsFailedAppInteraction": "Falha de interação com aplicação", "transactionsBidOnInterpolated": "Licitar em {{name}}", "transactionsBidFailed": "Falha de licitação", "transactionsBoughtInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsBoughtCollectible": "Compra de colecionável", "transactionBridgeInitiated": "Ponte iniciada", "transactionBridgeInitiatedFailed": "Falha da iniciação da ponte", "transactionBridgeStatusLink": "Verificar estado em LI.FI", "transactionsBuyFailed": "<PERSON><PERSON><PERSON> de compra", "transactionsBurnedSpam": "<PERSON>m queimado", "transactionsBurned": "Que<PERSON>do", "transactionsUnwrapped": "Wrapping removido", "transactionsUnwrappedFailed": "Falha ao remover wrapping", "transactionsCancelBidOnInterpolated": "Licitação cancelada em {{name}}", "transactionsCancelBidOnFailed": "Falha ao cancelar licitação", "transactionsError": "Erro", "transactionsFailed": "Fal<PERSON>", "transactionsSwapped": "Trocado", "transactionsFailedSwap": "Falha de troca", "transactionsFailedBurn": "Falha ao queimar", "transactionsFrom": "De", "transactionsListedInterpolated": "Listado {{name}}", "transactionsListedFailed": "<PERSON><PERSON><PERSON> de <PERSON>m", "transactionsNoActivity": "<PERSON>enhuma atividade", "transactionsReceived": "Recebido", "transactionsReceivedInterpolated": "Recebeu {{amount}} SOL", "transactionsSending": "A enviar...", "transactionsPendingCreateListingInterpolated": "A criar {{name}}", "transactionsPendingEditListingInterpolated": "A editar {{name}}", "transactionsPendingSolanaPayTransaction": "A confirmar transação de pagamento com Solana", "transactionsPendingRemoveListingInterpolated": "A remover da lista {{name}}", "transactionsPendingBurningInterpolated": "A queimar {{name}}", "transactionsPendingSending": "A enviar", "transactionsPendingSwapping": "A trocar", "transactionsPendingBridging": "<PERSON><PERSON>", "transactionsPendingApproving": "A aprovar", "transactionsPendingCreatingAndDelegatingStake": "Criar e delegar stake", "transactionsPendingDeactivatingStake": "Desativar stake", "transactionsPendingDelegatingStake": "Delegar stake", "transactionsPendingWithdrawingStake": "Levantar stake", "transactionsPendingAppInteraction": "Interação de aplicação pendente", "transactionsPendingBitcoinTransaction": "Transação BTC pendente", "transactionsSent": "Enviado", "transactionsSendFailed": "<PERSON><PERSON><PERSON> <PERSON>", "transactionsSwapOn": "Substituir em {{dappName}}", "transactionsSentInterpolated": "Enviados {{amount}} SOL", "transactionsSoldInterpolated": "<PERSON>end<PERSON> {{name}}", "transactionsSoldCollectible": "Cole<PERSON><PERSON><PERSON> vendido", "transactionsSoldFailed": "<PERSON><PERSON><PERSON> de venda", "transactionsStaked": "Staked", "transactionsStakedFailed": "<PERSON><PERSON><PERSON> de staking", "transactionsSuccess": "Sucesso", "transactionsTo": "Para", "transactionsTokenSwap": "Trocar token", "transactionsUnknownAmount": "Desconhecido", "transactionsUnlistedInterpolated": "<PERSON><PERSON><PERSON><PERSON> da <PERSON>a {{name}}", "transactionsUnstaked": "Tirar stake", "transactionsUnlistedFailed": "Falha de remoção de lista", "transactionsDeactivateStake": "Staking desativado", "transactionsDeactivateStakeFailed": "Falha de desativação de staking", "transactionsWaitingForConfirmation": "A aguardar confirmação", "transactionsWithdrawStake": "Levantar staking", "transactionsWithdrawStakeFailed": "Falha de remoção de staking", "transactionCancelled": "Cancelado", "transactionCancelledFailed": "<PERSON>alha ao cancelar", "transactionApproveToken": "Aprovado {{tokenSymbol}}", "transactionApproveTokenFailed": "Falha na aprovação de {{tokenSymbol}}", "transactionApprovalFailed": "Falha de aprovação", "transactionRevokeApproveToken": "Revogado {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "Falha na revogação de {{tokenSymbol}}", "transactionRevokeFailed": "Falha de revogação", "transactionApproveDetailsTitle": "Dados de aprovação", "transactionCancelOrder": "Cancelar ordem", "transactionCancelOrderFailed": "Falha de cancelamento de ordem", "transactionApproveAppLabel": "App", "transactionApproveAmountLabel": "Mont<PERSON>", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "Coleção", "transactionApproveAllItems": "<PERSON>ovar todos os itens", "transactionSpendUpTo": "Gastar até", "transactionCancel": "Cancelar transação", "transactionPrioritizeCancel": "Priorizar cancelamento", "transactionSpeedUp": "Acelerar transação", "transactionCancelHelperText": "A transação original pode ser concluída antes de ser cancelada.", "transactionSpeedUplHelperText": "Isto irá maximizar a velocidade da sua transação com base nas condições da rede.", "transactionCancelHelperMobile": "<PERSON><PERSON><PERSON> custar <1>até {{amount}}</1> para tentar cancelar esta transação. A transação original pode estar concluída antes de ser cancelada.", "transactionCancelHelperMobileWithEstimate": "<PERSON><PERSON><PERSON> custar <1>até {{amount}}</1> para tentar cancelar esta transação. A mesma deve estar concluída dentro de {{timeEstimate}}. A transação original pode estar concluída antes de ser cancelada.", "transactionSpeedUpHelperMobile": "<PERSON><PERSON><PERSON> custar <1>até {{amount}}</1> para maximizar a velocidade desta transação.", "transactionSpeedUpHelperMobileWithEstimate": "<PERSON><PERSON><PERSON> custar <1>até {{amount}}</1> para maximizar a velocidade desta transação. A mesma deve estar concluída dentro de {{timeEstimate}}.", "transactionEstimatedTime": "Tempo estimado", "transactionCancelingSend": "A cancelar envio", "transactionPrioritizingCancel": "A priorizar cancelamento", "transactionCanceling": "A cancelar", "transactionReplaceError": "Ocorreu um erro. Não foi cobrada nenhuma taxa à sua conta. Pode tentar outra vez.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} insuficientes", "transactionGasLimitError": "Falha de estimativa de limite de gasolina", "transactionGasEstimationError": "Falha de estimativa de gasolina", "pendingTransactionCancel": "<PERSON><PERSON><PERSON>", "pendingTransactionSpeedUp": "<PERSON><PERSON><PERSON>", "pendingTransactionStatus": "Estado", "pendingTransactionPending": "Pendente", "pendingTransactionPendingInteraction": "Interação pendente", "pendingTransactionCancelling": "A cancelar", "pendingTransactionDate": "Data", "pendingTransactionNetworkFee": "Taxa da rede", "pendingTransactionEstimatedTime": "Tempo estimado", "pendingTransactionEstimatedTimeHM": "{{hours}} h {{minutes}} m", "pendingTransactionEstimatedTimeMS": "{{minutes}} m {{seconds}} s", "pendingTransactionEstimatedTimeS": "{{seconds}} s", "pendingTransactionsSendingTitle": "A enviar {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Desconhecido", "pendingTransactionUnknownApp": "Aplicação desconhecida", "permanentDelegateTitle": "Delegado", "permanentDelegateValue": "Permanente", "permanentDelegateTooltipTitle": "Delegação permanente", "permanentDelegateTooltipValue": "Delegado permanente permite que outra conta realize a gestão de tokens em seu nome, isto inclui queimar ou transferir os tokens.", "unlockActionButtonUnlock": "Desb<PERSON>que<PERSON>", "unlockEnterPassword": "Introduzir a palavra-passe", "unlockErrorIncorrectPassword": "Palavra-passe incorreta", "unlockErrorSomethingWentWrong": "Ocorreu um erro, tente outra vez mais tarde", "unlockForgotPassword": "Esqueceu a palavra-passe", "unlockPassword": "Palavra-passe", "forgotPasswordText": "Pode repor a sua palavra-passe introduzindo a frase de recuperação de 12-24 palavras da sua carteira. A Phantom não pode repor a sua palavra-passe por si.", "appInfo": "Info. aplicação", "lastUsed": "Última utilização", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Não disponível com contas de hardware.", "trustedAppAutoConfirmDisclaimer1": "<PERSON><PERSON><PERSON> at<PERSON>, a Phantom irá confirmar todos os pedidos desta aplicação sem enviar-lhe qualquer notificação ou pedir-lhe qualquer confirmação.", "trustedAppAutoConfirmDisclaimer2": "Ativar a opção pode colocar os seus fundos em risco de fraude. Utilize esta funcionalidade apenas com aplicações em que confie.", "validationUtilsPasswordIsRequired": "A palavra-passe é necessária", "validationUtilsPasswordLength": "Tem de ter 8 caracteres de comprimento", "validationUtilsPasswordsDontMatch": "As palavras-passe não coincidem", "validationUtilsPasswordCantBeSame": "<PERSON>ão pode usar a sua palavra-passe anterior", "validatorCardEstimatedApy": "Estimativa APY", "validatorCardCommission": "Comissão", "validatorCardTotalStake": "Staking total", "validatorCardNumberOfDelegators": "N.º de delegadores", "validatorListChooseAValidator": "Escolher um validador", "validatorListErrorFetching": "Não conseguimos obter os validadores. Tente outra vez mais tarde.", "validatorListNoResults": "<PERSON><PERSON>hum resultado", "validatorListReload": "<PERSON><PERSON><PERSON><PERSON>", "validatorInfoTooltip": "Validador", "validatorInfoTitle": "Validadores", "validatorInfoDescription": "Ao realizar o staking de SOL num validador está a contribuir para o desempenho e segurança da rede Solana, ao mesmo tempo que também ganha SOL.", "validatorApyInfoTooltip": "Est. APY", "validatorApyInfoTitle": "Estimativa APY", "validatorApyInfoDescription": "Esta é a taxa de retorno que ganha por staking de SOL no validador.", "validatorViewActionButtonStake": "Staking", "validatorViewErrorFetching": "Não foi possível recuperar validadores.", "validatorViewInsufficientBalance": "<PERSON><PERSON> insuficiente", "validatorViewMax": "Máx.", "validatorViewPrimaryText": "Iniciar staking", "validatorViewDescriptionInterpolated": "Escolha quantos SOL quer usar no <1></1> staking com este validador. <3>Saber mais</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "São necessários {{amount}} SOL para staking", "validatorViewValidator": "Validador", "walletMenuItemsAddConnectWallet": "Adicionar/ligar carteira", "walletMenuItemsBridgeAssets": "Associar ativos", "walletMenuItemsHelpAndSupport": "Ajuda e suporte", "walletMenuItemsLockWallet": "Bloquear carteira", "walletMenuItemsResetSecretPhrase": "Repor frase secreta", "walletMenuItemsShowMoreAccounts": "<PERSON>rar mais {{count}}...", "walletMenuItemsHideAccounts": "Ocultar contas", "toggleMultiChainHeader": "Multicadeia", "disableMultiChainHeader": "<PERSON>do apen<PERSON>", "disableMultiChainDetail1Header": "Exclusivamente com Solana", "disableMultiChainDetail1SecondaryText": "Realize a gestão das suas contas Solana, de tokens, e de colecionáveis sem ver outras cadeias.", "disableMultiChainDetail2Header": "Voltar a multicadeia em qualquer altura", "disableMultiChainDetail2SecondaryText": "Os saldos Ethereum e Polygon existentes serão preservados quando reativar a multicadeia.", "disableMultiChainButton": "Ativar apenas Solana", "disabledMultiChainHeader": "Apenas Solana ativado", "disabledMultiChainText": "Pode reativar a multicadeia em qualquer altura.", "enableMultiChainHeader": "Ativar multicadeia", "enabledMultiChainHeader": "Multicadeia ativada", "enabledMultiChainText": "Ethereum e Polygon são agora suportados na sua carteira.", "incompatibleAccountHeader": "Conta incompatível", "incompatibleAccountInterpolated": "Remova estas contas apenas de Ethereum antes de ativar o modo apenas de Solana: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "As novidades!", "welcomeToMultiChainPrimaryText": "<PERSON>a carteira para tudo", "welcomeToMultiChainDetail1Header": "Suporte para Ethereum e Polygon", "welcomeToMultiChainDetail1SecondaryText": "Todos os seus tokens e NFT de Solana, Ethereum, e Polygon num único local.", "welcomeToMultiChainDetail2Header": "Utilize todas as aplicações que adora", "welcomeToMultiChainDetail2SecondaryText": "Ligue-se a aplicações em cadeias múltiplas sem trocar de redes.", "welcomeToMultiChainDetail3Header": "Importe a sua carteira MetaMask", "welcomeToMultiChainDetail3SecondaryText": "Importe facilmente todas as suas frases semente em Ethereum e Polygon.", "welcomeToMultiChainIntro": "<PERSON><PERSON><PERSON>v<PERSON><PERSON> a Phantom Multichain", "welcomeToMultiChainIntroDesc": "Todos os tokens e NFt de Solana, Ethereum, e Polygon num único lugar. A sua carteira única para tudo.", "welcomeToMultiChainAccounts": "Contas multicadeia redesenhadas", "welcomeToMultiChainAccountsDesc": "Redesenhadas para cadeia múltipla, cada conta tem agora endereços ETH e Polygon correspondentes.", "welcomeToMultiChainApps": "Funciona em qualquer local", "welcomeToMultiChainAppsDesc": "A Phantom é compatível com cada aplicação em Ethereum, Polygon, e Solana. Clique em \"Ligar a MetaMask\" e está preparado a começar.", "welcomeToMultiChainImport": "Importar de MetaMask, imediatamente", "welcomeToMultiChainImportDesc": "Importe as suas frases secretas ou chaves privadas de carteiras como MetaMask ou Coinbase Wallet. Tudo num único local.", "welcomeToMultiChainImportInterpolated": "<0><PERSON><PERSON>rte as suas frases secretas</0> ou chaves privadas de carteiras como MetaMask ou Coinbase Wallet. Tudo num único local.", "welcomeToMultiChainTakeTour": "Realize a visita", "welcomeToMultiChainSwapperTitle": "Trocar Ethereum,\nPolygon, e Solana", "welcomeToMultiChainSwapperDetail1Header": "Suporte para Ethereum e Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Agora pode facilmente trocar tokens ERC-20 a partir do interior da sua carteira.", "welcomeToMultiChainSwapperDetail2Header": "Melhores preços e taxas superbaixas", "welcomeToMultiChainSwapperDetail2SecondaryText": "Mais de 100 fontes de liquidez e encaminhamento de pedidos inteligente para retornos máximos.", "networkErrorTitle": "Erro de rede", "networkError": "Infelizmente não conseguimos aceder à rede. Tente outra vez mais tarde.", "authenticationUnlockPhantom": "Desbloquear Phantom", "errorAndOfflineSomethingWentWrong": "Algo correu mal", "errorAndOfflineSomethingWentWrongTryAgain": "Tente outra vez.", "errorAndOfflineUnableToFetchAssets": "Não conseguimos obter os ativos. Tente outra vez mais tarde.", "errorAndOfflineUnableToFetchCollectibles": "Não conseguimos obter colecionáveis. Tente outra vez mais tarde.", "errorAndOfflineUnableToFetchSwap": "Não conseguimos obter informação sobre troca. Tente outra vez mais tarde.", "errorAndOfflineUnableToFetchTransactionHistory": "Não nos é possível obter o seu histórico de transações agora. Verfique a sua ligação de rede, ou tente outra vez mais tarde.", "errorAndOfflineUnableToFetchRewardsHistory": "Não conseguimos obter o histórico de recompensas. Tente outra vez mais tarde.", "errorAndOfflineUnableToFetchBlockedUsers": "Não conseguimos obter os utilizadores bloqueados. Tente outra vez mais tarde.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Ocorreu um erro ao rever a sua ordem, tente outra vez.", "sendSelectToken": "Selecionar token", "swapBalance": "Saldo:", "swapTitle": "Substituir tokens", "swapSelectToken": "Selecionar token", "swapYouPay": "Paga", "swapYouReceive": "Recebe", "aboutPrivacyPolicy": "Política de Privacidade", "aboutVersion": "<PERSON><PERSON><PERSON> {{version}}", "aboutVisitWebsite": "Visitar website", "bottomSheetConnectTitle": "Ligar", "A11YbottomSheetConnectTitle": "Ligação de folha inferior", "A11YbottomSheetCommandClose": "Rejeição de folha inferior", "A11YbottomSheetCommandBack": "Verso de folha inferior", "bottomSheetSignTypedDataTitle": "Mensagem de assinatura", "bottomSheetSignMessageTitle": "Mensagem de assinatura", "bottomSheetSignInTitle": "<PERSON><PERSON><PERSON>", "bottomSheetSignInAndConnectTitle": "<PERSON><PERSON><PERSON>", "bottomSheetConfirmTransactionTitle": "Confirmar trans<PERSON>", "bottomSheetConfirmTransactionsTitle": "Confirmar <PERSON>", "bottomSheetSolanaPayTitle": "Pedido de pagamento com Solana", "bottomSheetAdvancedTitle": "Avançado", "bottomSheetReadOnlyAccountTitle": "Modo apenas de visualização", "bottomSheetTransactionSettingsTitle": "Taxa da rede", "bottomSheetConnectDescription": "A ligação permitirá que este website veja saldos e atividade para a conta selecionada.", "bottomSheetSignInDescription": "Assinar esta mensagem irá provar que é proprietário da conta selecionada. Assine apenas mensagens de aplicações em que confia.", "bottomSheetSignInAndConnectDescription": "A aprovação permitirá que este website veja saldos e atividade para a conta selecionada.", "bottomSheetConfirmTransactionDescription": "As alterações de saldo são estimadas. Os montantes e ativos envolvidos não são garantidos.", "bottomSheetConfirmTransactionsDescription": "As alterações de saldo são estimadas. Os montantes e ativos envolvidos não são garantidos.", "bottomSheetSignTypedDataDescription": "Trata-se apenas de um pedido de autorização. A transação poderá não ser executada imediatamente.", "bottomSheetSignTypedDataSecondDescription": "As alterações de saldo são estimadas. Os montantes e ativos envolvidos não são garantidos.", "bottomSheetSignMessageDescription": "Assinar esta mensagem irá provar que é proprietário da conta selecionada. Assine apenas mensagens de aplicações em que confia.", "bottomSheetReadOnlyAccountDescription": "Não é possível realizar esta ação em modo apenas de visualização.", "bottomSheetMessageRow": "Mensagem", "bottomSheetStatementRow": "Declaração", "bottomSheetAutoConfirmRow": "Confirmação automática", "bottomSheetAutoConfirmOff": "<PERSON><PERSON><PERSON>", "bottomSheetAutoConfirmOn": "<PERSON>g", "bottomSheetAccountRow": "Conta", "bottomSheetAdvancedRow": "Avançado", "bottomSheetContractRow": "Endereço do contrato", "bottomSheetSpenderRow": "Endereço de gastador", "bottomSheetNetworkRow": "Rede", "bottomSheetNetworkFeeRow": "Taxa da rede", "bottomSheetEstimatedTimeRow": "Tempo estimado", "bottomSheetAccountRowDefaultAccountName": "Conta", "bottomSheetConnectRequestDisclaimer": "Ligar apenas a websites de confiança", "bottomSheetSignInRequestDisclaimer": "Iniciar a sessão apenas em websites de confiança", "bottomSheetSignatureRequestDisclaimer": "Confirme apenas se confiar neste website.", "bottomSheetFeaturedTransactionDisclaimer": "Irá ver uma pré-visualização da transação antes da confirmação no passo seguinte.", "bottomSheetIgnoreWarning": "Ignorar aviso, avançar na mesma", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Não foram encontradas alterações de saldo. Avance com cuidado e confirme apenas se confiar neste website.", "bottomSheetReadOnlyWarning": "Está apenas a visualizar este endereço. Terá de importar para poder assinar transações e mensagens.", "bottomSheetWebsiteIsUnsafeWarning": "A utilização deste website não é segura e o mesmo poderá tentar roubar os seus fundos.", "bottomSheetViewOnExplorer": "Ver no Explorer", "bottomSheetTransactionSubmitted": "Transação enviada", "bottomSheetTransactionPending": "Transação pendente", "bottomSheetTransactionFailed": "Falha da transação", "bottomSheetTransactionSubmittedDescription": "A sua transação foi enviada. Pode vê-la no explorador.", "bottomSheetTransactionFailedDescription": "A sua transação falhou. Tente outra vez.", "bottomSheetTransactionPendingDescription": "A transação está a ser processada...", "transactionsFromInterpolated": "De: {{from}}", "transactionsFromParagraphInterpolated": "De {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "Hoje", "transactionsToInterpolated": "Para: {{to}}", "transactionsToParagraphInterpolated": "Para {{to}}", "transactionsYesterday": "Ontem", "addEditAddressAdd": "<PERSON><PERSON><PERSON><PERSON>", "addEditAddressDelete": "Eliminar endereço", "addEditAddressDeleteTitle": "Tem a certeza que quer eliminar este endereço?", "addEditAddressSave": "Guardar endereço", "dAppBrowserComingSoon": "Browser dApp disponível brevemente!", "dAppBrowserSearchPlaceholder": "Websites, tokens, URL", "dAppBrowserOpenInNewTab": "Abrir num separador novo", "dAppBrowserSuggested": "Sugestão", "dAppBrowserFavorites": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserBookmarks": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserBookmarkAdd": "Adici<PERSON><PERSON> favorito", "dAppBrowserBookmarkRemove": "Remover favorito", "dAppBrowserUsers": "Utilizadores", "dAppBrowserRecents": "<PERSON><PERSON>", "dAppBrowserFavoritesDescription": "Os seus favoritos serão exibidos aqui", "dAppBrowserBookmarksDescription": "Os seus favoritos serão exibidos aqui", "dAppBrowserRecentsDescription": "As dapps ligadas recentemente serão exibidas aqui", "dAppBrowserEmptyScreenDescription": "Digitar um URL ou pesquisar a Internet", "dAppBrowserBlocklistScreenTitle": "{{origin}} está bloqueado! ", "dAppBrowserBlocklistScreenDescription": {"part1": "A Phantom acredita que este website é malicioso e que a sua utilização não é segura.", "part2": "Este website foi assinalado como parte de uma base de dados mantida pela comunidade de burlas e websites de phishing conhecidos. Se acredita que o website foi assinalado erradamente, apresente uma reclamação."}, "dAppBrowserLoadFailedScreenTitle": "Falha ao carregar", "dAppBrowserLoadFailedScreenDescription": "Ocorreu um erro ao carregar esta página", "dAppBrowserBlocklistScreenIgnoreButton": "Ignorar aviso, mostrar na mesma", "dAppBrowserActionBookmark": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserActionRemoveBookmark": "Remover favorito", "dAppBrowserActionRefresh": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserActionShare": "Partilhar", "dAppBrowserActionCloseTab": "<PERSON><PERSON><PERSON>", "dAppBrowserActionEndAutoConfirm": "Terminar confirmação automática", "dAppBrowserActionDisconnectApp": "Desligar aplicação", "dAppBrowserActionCloseAllTabs": "<PERSON><PERSON><PERSON> todos os separadores", "dAppBrowserNavigationAddressPlaceholder": "Digite um URL a procurar", "dAppBrowserTabOverviewMore": "<PERSON><PERSON>", "dAppBrowserTabOverviewAddTab": "Adic. separador", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON><PERSON>", "dAppBrowserClose": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "Adici<PERSON><PERSON> favorito", "dAppBrowserTabOverviewRemoveBookmark": "Remover favorito", "depositAssetListSuggestions": "Sugestões", "depositUndefinedToken": "Lamento, não é possível depositar este token", "onboardingImportRecoveryPhraseDetails": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseVerifyTitle": "Apontou a sua frase secreta de recuperação?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Sem a frase secreta de recuperação não poderá aceder à sua chave ou a quaisquer ativos associados à mesma.", "onboardingCreateRecoveryPhraseVerifyYes": "<PERSON>m", "onboardingCreateRecoveryPhraseErrorTitle": "Erro", "onboardingCreateRecoveryPhraseErrorSubtitle": "Não tivemos sucesso em gerar uma conta, tente novamente.", "onboardingDoneDescription": "Pode agora desfrutar plenamente da sua carteira.", "onboardingDoneGetStarted": "<PERSON><PERSON><PERSON>", "zeroBalanceHeading": "Vamos começar!", "zeroBalanceBuyCryptoTitle": "Comprar criptomoeda", "zeroBalanceBuyCryptoDescription": "Compre a sua primeira criptomoeda com um cartão de débito ou de crédito.", "zeroBalanceDepositTitle": "Transferir criptomoeda", "zeroBalanceDepositDescription": "Depositar criptomoeda de outra carteira ou câmbio.", "onboardingImportAccountsEmptyResult": "Nenhuma conta encontrada", "onboardingImportAccountsAccountName": "Conta {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Conta social", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Encontrámos {{numberOfWallets}} conta com atividade", "onboardingImportAccountsFoundAccounts_other": "Encontrámos {{numberOfWallets}} contas com atividade", "onboardingImportAccountsFoundAccountsNoActivity_one": "Encontrámos {{numberOfWallets}} conta", "onboardingImportAccountsFoundAccountsNoActivity_other": "Encontrámos {{numberOfWallets}} contas", "onboardingImportRecoveryPhraseLessThanTwelve": "A frase tem de ter pelo menos 12 palavras.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "A frase tem de ter exatamente 12 ou 24 palavras.", "onboardingImportRecoveryPhraseWrongWord": "Palavras incorretas: {{ words }}.", "onboardingProtectTitle": "Proteja a sua carteira", "onboardingProtectDescription": "Adicionar segurança biométrica irá assegurar que é a única pessoa que pode aceder à sua carteira.", "onboardingProtectButtonHeadlineDevice": "Dispositivo", "onboardingProtectButtonHeadlineFaceID": "ID facial", "onboardingProtectButtonHeadlineFingerprint": "Impressão digital", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Utilizar autenticação {{ authType }}", "onboardingProtectError": "Ocorreu um erro ao realizar a autenticação, tente outra vez", "onboardingProtectBiometryIosError": "A autenticação biométrica está configurada no Phantom mas desativada nas definições do sistema. Abra Definições > Phantom > ID de rosto ou ID tátil para voltar a ativar.", "onboardingProtectRemoveAuth": "Desativar a autenticação", "onboardingProtectRemoveAuthDescription": "Tem a certeza que pretende desativar a autenticação?", "onboardingWelcomeTitle": "Boas-vindas à Phantom", "onboardingWelcomeDescription": "Para começar, crie uma nova carteira ou importe uma carteira existente.", "onboardingWelcomeCreateWallet": "Criar uma nova carteira", "onboardingWelcomeAlreadyHaveWallet": "<PERSON><PERSON> tenho uma carteira", "onboardingWelcomeConnectSeedVault": "Ligar cofre de sementes", "onboardingSlide1Title": "Controlado por si", "onboardingSlide1Description": "A sua carteira está segura com acesso biométrico, deteção de fraude e assistência 24 horas por dia, 7 dias por semana.", "onboardingSlide2Title": "A melhor casa para\nos seus NFT", "onboardingSlide2Description": "Realize a gestão de listas, queime spam, e mantenha-se a par das novidades com úteis notificações push.", "onboardingSlide3Title": "Faça mais com os seus tokens", "onboardingSlide3Description": "Guarde, troque, use stakes, envie, e receba — sem nunca ter de sair da sua carteira. ", "onboardingSlide4Title": "Descubra o melhor de Web3", "onboardingSlide4Description": "Encontre e ligue-se a aplicações líderes e a coleções com o navegador da aplicação.", "onboardingMultichainSlide5Title": "<PERSON>a carteira para tudo", "onboardingMultichainSlide5Description": "Viva toda a experiência Solana, Ethereum, e Polygon num único interface de fácil utilização.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Viva toda a experiência Solana, Ethereum, Polygon e Bitcoin num único interface de fácil utilização.", "requireAuth": "<PERSON><PERSON>r auten<PERSON>", "requireAuthImmediately": "Imediatamente", "availableToSend": "Disponível para envio", "sendEnterAmount": "<PERSON><PERSON><PERSON><PERSON><PERSON> montante", "sendEditMemo": "<PERSON>ar memorando", "sendShowLogs": "Mostrar registos de erro", "sendHideLogs": "Ocultar registos de erro", "sendGoBack": "Voltar", "sendTransactionSuccess": "Os seus tokens foram enviados com êxito para", "sendInputPlaceholder": "@nomedeutilizador ou endereço", "sendInputPlaceholderV2": "nome de utilizador ou endereço", "sendPeopleTitle": "<PERSON><PERSON><PERSON><PERSON>", "sendDomainTitle": "<PERSON><PERSON><PERSON>", "sendFollowing": "A seguir", "sendRecentlyUsedAddressLabel": "Utilizado há {{formattedTimestamp}} atrás", "sendRecipientAddress": "Endereço do destinatário", "sendTokenInterpolated": "Enviar {{tokenSymbol}}", "sendPasteFromClipboard": "Colar a partir da área de transferência", "sendScanQR": "Digitalizar código QR", "sendTo": "Para:", "sendRecipientZeroBalanceWarning": "Este endereço de carteira não tem saldo e não surge no seu histórico recente de transações. Certifique-se de que o endereço está correto.", "sendUnknownAddressWarning": "Este não é um endereço com o qual tenha interagido recentemente. Avance com precaução.", "sendSameAddressWarning": "Este é o seu endereço atual. O envio irá incorrer taxas de transferência sem quaisquer outras alterações de saldo.", "sendMintAccountWarning": "Este é um endereço de conta de cunhagem. Não pode enviar fundos para este endereço uma vez que isso irá resultar numa perda permanente.", "sendCameraAccess": "Acesso à câmara", "sendCameraAccessSubtitle": "Para ler um código QR, é preciso ter o acesso à câmara ativado. Pre<PERSON>e a<PERSON>r as Definiç<PERSON>es agora?", "sendSettings": "Definições", "sendOK": "OK", "invalidQRCode": "Este código QR não é válido.", "sendInvalidQRCode": "Este código QR não é um endereço válido", "sendInvalidQRCodeSubtitle": "Tente novamente ou com outro código QR.", "sendInvalidQRCodeSplToken": "Token inválido no código QR", "sendInvalidQRCodeSplTokenSubtitle": "Este código QR contém um token que não tem ou que não conseguimos identificar.", "sendScanAddressToSend": "Ler o endereço {{tokenSymbol}} para enviar fundos", "sendScanAddressToSendNoSymbol": "Ler endereço para enviar fundos", "sendScanAddressToSendCollectible": "Ler endereço SOL para enviar colecionável", "sendScanAddressToSendCollectibleMultichain": "Ler endereço para enviar colecionável", "sendSummary": "Resumo", "sendUndefinedToken": "Lament<PERSON>s, não é possível enviar este token", "sendNoTokens": "Não existem tokens disponíveis", "noBuyOptionsAvailableInCountry": "Não existem opções de compra disponíveis no seu país", "swapAvailableTokenDisclaimer": "Existe um número limitado de tokens disponível para a ligação em ponte entre redes", "swapCrossSwapNetworkTooltipTitle": "Trocas entre redes", "swapCrossSwapNetworkTooltipDescription": "Ao realizar trocas entre redes é recomendada a utilização dos tokens disponíveis para o preço mais baixo e transações mais rápidas.", "settingsAbout": "Sobre a Phantom", "settingsShareAppWithFriends": "Convidar os seus amigos", "settingsConfirm": "<PERSON>m", "settingsMakeSureNoOneIsWatching": "Certifique-se de que ninguém está a ver o seu ecrã", "settingsManageAccounts": "<PERSON><PERSON><PERSON> con<PERSON>", "settingsPrompt": "Tem a certeza que quer continuar?", "settingsSelectAvatar": "Selecionar avatar", "settingsSelectSecretPhrase": "Selecionar frase secreta", "settingsShowPrivateKey": "Toque para revelar a sua chave privada", "settingsShowRecoveryPhrase": "Toque para revelar a sua frase secreta", "settingsSubmitBetaFeedback": "Enviar feedback beta", "settingsUpdateAccountNameToast": "Nome da conta atualizado", "settingsUpdateAvatarToast": "Avatar atualizado", "settingsUpdateAvatarToastFailure": "Falha ao atualizar o avatar!", "settingsWalletAddress": "Endereço da conta", "settingsWalletAddresses": "Endereços da conta", "settingsWalletNamePrimary": "Nome da conta", "settingsPlaceholderName": "Nome", "settingsWalletNameSecondary": "Alterar o nome da sua carteira", "settingsYourAccounts": "As suas contas", "settingsYourAccountsMultiChain": "Multi-cadeia", "settingsReportUser": "Reportar utilizador", "settingsNotifications": "Notificações", "settingsNotificationPreferences": "Preferências de notificação", "pushNotificationsPreferencesAllowNotifications": "Permitir <PERSON>", "pushNotificationsPreferencesSentTokens": "Tokens enviados", "pushNotificationsPreferencesSentTokensDescription": "Transferências de saída de tokens e NFT", "pushNotificationsPreferencesReceivedTokens": "Tokens recebidos", "pushNotificationsPreferencesReceivedTokensDescription": "Transferências de entrada de tokens e NFT", "pushNotificationsPreferencesDexSwap": "Trocas", "pushNotificationsPreferencesDexSwapDescription": "Trocas em aplicações reconhecidas", "pushNotificationsPreferencesOtherBalanceChanges": "Outras alterações de saldo", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Outras transações multi-token que afetam o seu saldo", "pushNotificationsPreferencesPhantomMarketing": "Atualizações Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Destaque de anúncios e atualizações gerais", "pushNotificationsPreferencesDescription": "Estas definições controlam as notificações push desta carteira ativa. Cada carteira tem as suas próprias definições de notificação. Para desligar todas as notificações push Phantom, entre nas <1>definições do dispositivo</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Não foi possível sincronizar as preferências de notificação.", "connectSeedVaultConnectSeed": "Ligar uma semente", "connectSeedVaultConnectSeedDescription": "Ligar Phantom ao cofre de sementes no seu telefone", "connectSeedVaultSelectAnAccount": "Selecionar uma conta", "connectSeedVaultSelectASeed": "Selecionar uma semente", "connectSeedVaultSelectASeedDescription": "Escolha qual a semente que pretende ligar à Phantom", "connectSeedVaultSelectAnAccountDescription": "Escolha qual a conta que quer configurar com a Phantom", "connectSeedVaultNoAccountsFound": "Nenhuma conta encontrada.", "connectSeedVaultSelectAccounts": "Selecionar contas", "connectSeedVaultSelectAccountsDescription": "<PERSON><PERSON><PERSON><PERSON> quais as contas que quer configurar com a Phantom", "connectSeedVaultCompleteSetup": "Completar configuração", "connectSeedVaultCompleteSetupDescription": "Tudo pronto! Explore web3 com a Phantom e use o cofre de sementes para confirmar as transações.", "connectSeedVaultConnectAnotherSeed": "Ligar outra semente", "connectSeedVaultConnectAllSeedsConnected": "<PERSON><PERSON> as sementes ligadas", "connectSeedVaultNoSeedsConnected": "Nenhuma semente ligada. Toque no botão em baixo para dar autorização a partir do cofre de sementes.", "connectSeedVaultConnectAccount": "Ligar conta", "connectSeedVaultLoadMore": "<PERSON><PERSON><PERSON> mais", "connectSeedVaultNeedPermission": "É preciso autorização", "connectSeedVaultNeedPermissionDescription": "<PERSON>r para as definições para permitir à Phantom usar as autorizações do cofre de sementes.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "Taxa de {{fee}}", "stakeAmount": "Mont<PERSON>", "stakeAmountBalance": "<PERSON><PERSON>", "swapTopQuotes": "{{numQuotes}} orçamentos principais", "swapTopQuotesTitle": "Cotações principais", "swapProvidersTitle": "Fornecedores", "swapProvidersFee": "Taxa de {{fee}}", "swapProvidersTagRecommended": "<PERSON><PERSON> retorno", "swapProvidersTagFastest": "<PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}} h {{minutes}} m", "swapProviderEstimatedTimeM": "{{minutes}} m", "swapProviderEstimatedTimeS": "{{seconds}} s", "stakeReview": "<PERSON><PERSON><PERSON>", "stakeReviewAccount": "Conta", "stakeReviewCommissionFee": "Comissão", "stakeReviewConfirm": "Confirmar", "stakeReviewValidator": "Validador", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "A conversão do stake falhou", "convertStakeStatusErrorMessage": "Não foi possível converter o seu stake em {{poolTokenSymbol}}. Tente outra vez.", "convertStakeStatusLoadingTitle": "A converter para {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Estamos a iniciar o processo para converter o seu stake de {{stakedTokenSymbol}} para {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Conversão para {{poolTokenSymbol}} concluída!", "convertStakeStatusSuccessMessage": "Ganhe recompensas adicionais com os seus JitoSOL <1>aqui.</1>", "convertStakeStatusConvertMore": "Converter mais", "convertStakePendingTitle": "A converter stake para {{symbol}}", "convertToJitoSOL": "Converter para JitoSOL", "convertToJitoSOLInfoDescription": "Converta os seus SOL para Jito SOL para ganhar recompensas e participar no ecossistema Jito.", "convertToJitoSOLInfoTitle": "Converter para JitoSOL", "convertStakeBannerTitle": "Converta o seu stake para JitoSOL para reforçar as recompensas em até 15%", "convertStakeQuestBannerTitle": "Converta SOL em stake para JitoSOL e ganhe recompensas. Saiba mais.", "liquidStakeConvertInfoTitle": "Converter para JitoSOL", "liquidStakeConvertInfoDescription": "Aumente as suas recompensas convertendo o seu stake de SOL para JitoSOL. <1><PERSON>ber mais</1>", "liquidStakeConvertInfoFeature1Title": "Porque realizar o stake com Jito?", "liquidStakeConvertInfoFeature1Description": "Deposite para obter Jito SOL, que cresce com o seu stake. Utilize com protocolos DeFi para receitas extra. Mude os Jito SOL posteriormente para o seu montante inicial + as recompensas adicionais", "liquidStakeConvertInfoFeature2Title": "Recompensas médias mais altas", "liquidStakeConvertInfoFeature2Description": "Jito expande os seus SOL entre os melhores validadores com as taxas mais baixas. As recompensas MEV aumentam ainda mais os seus ganhos.", "liquidStakeConvertInfoFeature3Title": "Suportar a rede Solana", "liquidStakeConvertInfoFeature3Description": "O stake líquido torna segura a rede Solana distribuindo o stake por diversos validadores, reduzindo o risco de validadores de baixo tempo de funcionamento.", "liquidStakeConvertInfoSecondaryButton": "<PERSON><PERSON><PERSON> n<PERSON>", "liquidStakeStartStaking": "Iniciar staking", "liquidStakeReviewOrder": "Rever ordem", "convertStakeAccountListPageIneligibleSectionTitle": "Contas de staking não elegíveis", "convertStakeAccountIneligibleBottomSheetTitle": "Contas de staking não elegíveis", "convertStakeAccountListPageErrorTitle": "Falha ao tentar encontrar contas de staking", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON><PERSON><PERSON>, algo correu mal e não foi possível encontrar as contas de staking", "liquidStakeReviewYouPay": "Paga", "liquidStakeReviewYouReceive": "Recebe", "liquidStakeReviewProvider": "Fornecedor", "liquidStakeReviewNetworkFee": "Taxa da rede", "liquidStakeReviewPageTitle": "Confirmação", "liquidStakeReviewConversionFootnote": "Quando realiza o staking com tokens Solana em troca de JitoSOL, irá receber uma quantia ligeiramente menor de JitoSOL. <1><PERSON><PERSON> mais</1>", "convertStakeAccountIneligibleBottomSheetDescription": "O conjunto de staking de Jito suporta a maioria dos validadores Solana ativos. Não poderá converter SOL alvo de staking de validadores não suportados para JitoSOL. Adicionalmente, SOL de staking novos demoram ~2 dias antes de serem elegíveis para conversão para JitoSOL.", "selectAValidator": "Selecionar um validador", "validatorSelectionListTitle": "Selecionar um validador", "validatorSelectionListDescription": "Escolher um validador com o qual realizar o stake com SOL.", "stakeMethodDescription": "Ganhe juros utilizando os seus tokens SOL para ajudar na escala de Solana obtidos. <1>Saiba mais</1>", "stakeMethodRecommended": "Recomendado", "stakeMethodEstApy": "Est. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "<PERSON><PERSON> l<PERSON>", "stakeMethodSelectionLiquidStakingDescription": "Realize o staking de SOL para obter recompensas mais altas, ajudar a obter mais Solana e receber JitoSOL para ganhar recompensas adicionais.", "stakeMethodSelectionNativeStakingTitle": "Stake nativo", "stakeMethodSelectionNativeStakingDescription": "Realize o staking de SOL para receber recompensas ao mesmo tempo que obtém Solana.", "liquidStakeMintStakeSOL": "Staking de SOL", "mintJitoSOLInfoPageTitle": "Introdução de stake líquido com Jito", "mintJitoSOLFeature1Title": "Porque realizar o stake com Jito?", "mintJitoSOLFeature1Description": "Deposite para obter JitoSOL, que cresce com o seu stake. Utilize com protocolos DeFi para receitas extra. Mude os JitoSOL posteriormente para o seu montante inicial + as recompensas adicionais", "mintJitoSOLFeature2Title": "Recompensas médias mais altas", "mintJitoSOLFeature2Description": "Jito expande os seus SOL entre os melhores validadores com as taxas mais baixas. As recompensas MEV aumentam ainda mais os seus ganhos.", "mintJitoSOLFeature3Title": "Suportar a rede Solana", "mintJitoSOLFeature3Description": "O stake líquido torna segura a rede Solana distribuindo o stake por diversos validadores, reduzindo o risco de validadores de baixo tempo de funcionamento.", "mintLiquidStakePendingTitle": "<PERSON><PERSON><PERSON><PERSON> de staking líquido", "mintStakeStatusErrorTitle": "Falha de cunhagem de staking líquido", "mintStakeStatusErrorMessage": "Não foi possível cunhar o seu staking líquido {{poolTokenSymbol}}. Tente outra vez.", "mintStakeStatusSuccessTitle": "<PERSON><PERSON><PERSON><PERSON> de staking líquido {{poolTokenSymbol}} concluída!", "mintStakeStatusLoadingTitle": "A cunhar staking líquido {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "Estamos a iniciar o processo para cunhar o seu staking líquido {{poolTokenSymbol}}.", "mintLiquidStakeAmountDescription": "Escolha quanto SOL pretende usar no staking com Jito", "mintLiquidStakeAmountProvider": "Fornecedor", "mintLiquidStakeAmountApy": "Est. APY", "mintLiquidStakeAmountBestPrice": "Preço", "mintLiquidStakeAmountInsufficientBalance": "<PERSON><PERSON> insuficiente", "mintLiquidStakeAmountMinRequired": "são necessários {{amount}} {{symbol}} para staking", "swapTooltipGotIt": "OK", "swapTabInsufficientFunds": "<PERSON><PERSON> insuficiente", "swapNoAssetsFound": "<PERSON><PERSON> bens", "swapNoTokensFound": "Nenhum token encontrado", "swapConfirmationTryAgain": "Tentar novamente", "swapConfirmationGoBack": "Voltar", "swapNoQuotesFound": "Nenhum orçamento encontrado", "swapNotProviderFound": "Não conseguimos encontrar um fornecedor para esta troca de tokens. Tente um token diferente.", "swapAvailableOnMainnet": "Esta funcionalidade apenas está disponível na Mainnet", "swapNotAvailableEVM": "As trocas para contas EVM ainda não estão disponíveis", "swapNotAvailableOnSelectedNetwork": "Não estão disponíveis trocas na rede selecionada", "singleChainSwapTab": "Na rede", "crossChainSwapTab": "Em várias redes", "allFilter": "<PERSON><PERSON>", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON><PERSON>", "bridgeRefuelDescription": "Reabastecer assegura que pode pagar as transações depois de realizar a transposição.", "bridgeRefuelLabelBalance": "O seu {{symbol}}", "bridgeRefuelLabelReceive": "Recebe", "bridgeRefuelLabelFee": "<PERSON>usto estimado", "bridgeRefuelDismiss": "Continuar sem reabastecer", "bridgeRefuelEnable": "Ativar reabaste<PERSON>mento", "unwrapWrappedSolError": "Falha de remoção de wrapping", "unwrapWrappedSolLoading": "A remover wrapping...", "unwrapWrappedSolSuccess": "Wrapping removido", "unwrapWrappedSolViewTransaction": "Ver transação", "dappApprovePopupSignMessage": "Mensagem de assinatura", "solanaPayFrom": "De", "solanaPayMessage": "Mensagem", "solanaPayNetworkFee": "Taxa da rede", "solanaPayFree": "<PERSON><PERSON><PERSON><PERSON>", "solanaPayPay": "Pagar {{item}}", "solanaPayPayNow": "Pagar agora", "solanaPaySending": "A enviar {{item}}", "solanaPayReceiving": "A receber {{item}}", "solanaPayMinting": "A cunhar {{item}}", "solanaPayTransactionProcessing": "A sua transação está a ser processada,\naguarde.", "solanaPaySent": "Enviado!", "solanaPayReceived": "Recebido!", "solanaPayMinted": "<PERSON>unhado!", "solanaPaySentNFT": "NFT enviado!", "solanaPayReceivedNFT": "NFT recebido!", "solanaPayTokensSent": "Os seus tokens foram enviados com sucesso para {{to}}", "solanaPayTokensReceived": "Recebeu novos tokens de {{from}}", "solanaPayViewTransaction": "Ver transação", "solanaPayTransactionFailed": "Falha da transação", "solanaPayConfirm": "Confirmar", "solanaPayTo": "para", "dappApproveConnectViewAccount": "Ver a sua conta Solana", "deepLinkInvalidLink": "Ligação inválida", "deepLinkInvalidSplTokenSubtitle": "Contém um token que não tem ou que não conseguimos identificar.", "walletAvatarShowAllAccounts": "<PERSON><PERSON> todas as contas", "pushNotificationsGetInstantUpdates": "Obter atualizações imediatas", "pushNotificationsEnablePushNotifications": "Ativar notificações push sobre transferências concluídas, trocas e anúncios", "pushNotificationsEnable": "Ativar", "pushNotificationsNotNow": "<PERSON><PERSON><PERSON> n<PERSON>", "onboardingAgreeToTermsOfServiceInterpolated": "<PERSON>ito o<PERSON> <1>termos de serviço</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, já a guardei", "onboardingCreateNewWallet": "C<PERSON>r uma carteira nova", "onboardingErrorDuplicateSecretRecoveryPhrase": "Esta frase secreta já existe na sua carteira", "onboardingErrorInvalidSecretRecoveryPhrase": "Frase secreta de recuperação inválida", "onboardingFinished": "Está despachado!", "onboardingImportAccounts": "Importar contas", "onboardingImportImportingAccounts": "A importar contas...", "onboardingImportImportingFindingAccounts": "Encontrar contas com atividade", "onboardingImportAccountsLastActive": "Ativo há {{formattedTimestamp}} atrás", "onboardingImportAccountsNeverUsed": "Nunca utilizado", "onboardingImportAccountsCreateNew": "Carteira nova", "onboardingImportAccountsDescription": "Escolher contas de carteiras a importar", "onboardingImportReadOnlyAccountDescription": "Adicione um endereço ou nome de domínio que pretende visualizar. Terá acesso apenas de visualização e não poderá assinar transações ou mensagens.", "onboardingImportSecretRecoveryPhrase": "Importar frase secreta", "onboardingImportViewAccounts": "Ver contas", "onboardingRestoreExistingWallet": "Restaurar uma carteira existente com a sua frase secreta de recuperação de 12 ou 24 palavras", "onboardingShowUnusedAccounts": "Mostrar contas não utilizadas", "onboardingShowMoreAccounts": "<PERSON>rar mais contas", "onboardingHideUnusedAccounts": "Ocultar contas não utilizadas", "onboardingSecretRecoveryPhrase": "Frase secreta de recuperação", "onboardingSelectAccounts": "Se<PERSON><PERSON>e as suas contas", "onboardingStoreSecretRecoveryPhraseReminder": "Esta é a única forma de conseguir recuperar a sua conta. Guarde-a num lugar seguro!", "useTokenMetasForMintsUnknownName": "Desconhecido", "timeUnitMinute": "minuto", "timeUnitMinutes": "minutos", "timeUnitHour": "hora", "timeUnitHours": "horas", "espNFTListWithPrice": "Listou {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}} em {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Listou {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Listou {{NFTDisplayName}} para venda em {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Listou {{NFTDisplayName}} para venda", "espNFTChangeListPriceWithPrice": "Atualizou a listagem de {{NFTDisplayName}} a {{priceAmount}} {{priceTokenSymbol}} em {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Atualizou a listagem de {{NFTDisplayName}} a {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "At<PERSON><PERSON>ou a listagem de {{NFTDisplayName}} em {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Atualizou a listagem de {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Licitou {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}} em {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Licitou {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Realizou uma licitação a {{NFTDisplayName}} em {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Realizou uma licitação a {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Nova licitação de {{priceAmount}} {{priceTokenSymbol}} para {{NFTDisplayName}} em {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Nova licitação de {{priceAmount}} {{priceTokenSymbol}} para {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Nova licitação para {{NFTDisplayName}} em {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Nova licitação para {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Can<PERSON>ou a sua licitação de {{priceAmount}} {{priceTokenSymbol}} para {{NFTDisplayName}} em {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Cancelou a sua licitação de {{priceAmount}} {{priceTokenSymbol}} para {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "<PERSON><PERSON>ou a sua licitação para {{NFTDisplayName}} em {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Cancelou a sua licitação para {{NFTDisplayName}}", "espNFTUnlist": "Removeu da lista {{NFTDisplayName}} em {{dAppName}}", "espNFTUnlistWithoutDApp": "Removeu da lista {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Comprou {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}} em {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Comprou {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Comprou {{NFTDisplayName}} em {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Comprou {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Vendeu {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}} em {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Vendeu {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Vendeu {{NFTDisplayName}} em {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Vendeu {{NFTDisplayName}}", "espDEXSwap": "Trocou {{downTokensTextFragment}} por {{upTokensTextFragment}} em {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Depositou {{downTokensTextFragment}} no conjunto de liquidez {{poolDisplayName}} em {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Trocou {{downTokensTextFragment}} por {{upTokensTextFragment}} em {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Levantou {{upTokensTextFragment}} do conjunto de liquidez {{poolDisplayName}} em {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Trocou {{downTokensTextFragment}} por {{upTokensTextFragment}} em {{dAppName}}", "espGenericTokenSend": "Enviou {{downTokensTextFragment}}", "espGenericTokenReceive": "Recebeu {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Trocou {{downTokensTextFragment}} por {{upTokensTextFragment}}", "espUnknown": "DESCONHECIDO", "espUnknownNFT": "NFT desconhecido", "espTextFragmentAnd": "e", "externalLinkWarningTitle": "<PERSON><PERSON><PERSON> prestes a sair da <PERSON>", "externalLinkWarningDescription": "E abrir {{url}}. Certifique-se de que confia nesta fonte antes de interagir com ela.", "shortcutsWarningDescription": "Atalhos fornecidos por {{url}}. Certifique-se de que confia nesta fonte antes de interagir com ela.", "lowTpsBanner": "Está a ocorrer uma congestão da rede Solana", "lowTpsMessageTitle": "Congestão da rede Solana", "lowTpsMessage": "Devido à congestão elevada da rede Solana, as suas transações podem falhar ou registar atrasos. Volte a tentar as transações falhadas.", "solanaSlow": "A rede Solana está invulgarmente lenta", "solanaNetworkTemporarilyDown": "A rede Solana está temporariamente desligada", "waitForNetworkRestart": "Aguarde que a rede reinicie. O seus fundos não são afetados.", "exploreCollectionsCarouselTitle": "Mais popular", "exploreDropsCarouselTitle": "As novidades", "exploreSortFloor": "<PERSON><PERSON>", "exploreSortListed": "Listado", "exploreSortVolume": "Volume", "exploreFetchErrorSubtitle": "Tente outra vez mais tarde.", "exploreFetchErrorTitle": "Falha na recuperação.", "exploreTopCollectionsTitle": "Principais coleções NFT", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "<PERSON><PERSON>", "exploreSeeMore": "Ver mais", "exploreTrendingTokens": "Tokens populares", "exploreVolumeTokens": "Volume mais alto", "explorePriceChangeTokens": "<PERSON><PERSON>", "explorePriceTokens": "Tokens por preço", "exploreMarketCapTokens": "Tokens principais", "exploreTrendingSites": "Sítios de tendência", "exploreTopSites": "<PERSON><PERSON><PERSON><PERSON> princi<PERSON>", "exploreTrendingCollections": "Coleções em tendência", "exploreTopCollections": "Coleções principais", "collectiblesSearchCollectionsSection": "Coleções", "collectiblesSearchItemsSection": "<PERSON><PERSON>", "collectiblesSearchNrOfItems": "{{ nrOfItems }} itens", "collectiblesSearchPlaceholderText": "<PERSON><PERSON><PERSON><PERSON> as suas coleções", "collectionPinSuccess": "Coleção afixada", "collectionPinFail": "Falha ao afixar coleção", "collectionUnpinSuccess": "Coleção desafixada", "collectionUnpinFail": "Falha ao desafixar coleção", "collectionHideSuccess": "Coleção ocultada", "collectionHideFail": "Falha ao ocultar coleção", "collectionUnhideSuccess": "Coleção mostrada", "collectionUnhideFail": "Falha ao mostrar coleção", "collectiblesSpamSuccess": "Comunicado como spam", "collectiblesSpamFail": "Falha de comunicação como spam", "collectiblesSpamAndHiddenSuccess": "Comunicado como spam e oculto", "collectiblesNotSpamSuccess": "Comunicado como não sendo spam", "collectiblesNotSpamFail": "Falha de comunicação como não sendo spam", "collectiblesNotSpamAndUnhiddenSuccess": "Mostrado e comunicado como não sendo spam", "tokenPageSpamWarning": "Este token não está verificado. Interaja apenas com tokens em que confia.", "tokenSpamWarning": "Este token estava oculto porque a Phantom acredita que é spam.", "collectibleSpamWarning": "Este colecionável estava oculto porque a Phantom acredita que é spam.", "collectionSpamWarning": "<PERSON>stes colecionáveis foram ocultos porque a Phantom acredita que são spam.", "emojiNoResults": "Nenhum emoji encontrado", "emojiSearchResults": "Resultados da pesquisa", "emojiSuggested": "Sugestão", "emojiSmileys": "Caras sorridentes e pessoas", "emojiAnimals": "Animais e natureza", "emojiFood": "Comida e bebida", "emojiTravel": "Viagens e locais", "emojiActivities": "Atividades", "emojiObjects": "<PERSON><PERSON><PERSON><PERSON>", "emojiSymbols": "<PERSON><PERSON><PERSON><PERSON>", "emojiFlags": "Bandeiras", "whichExtensionToConnectWith": "Qual a extensão a que pretende realizar a ligação?", "configureInSettings": "Configurável em Definições → Carteira de aplicação predefinida.", "continueWith": "Continuar com", "useMetaMask": "<PERSON><PERSON><PERSON><PERSON>", "usePhantom": "Utilizar Phantom", "alwaysAsk": "Pergun<PERSON> sempre", "dontAskMeAgain": "Não perguntar novamente", "selectWalletSettingDescriptionLine1": "Algumas aplicações podem não oferecer uma opção de ligação à Phantom.", "selectWalletSettingDescriptionLinePhantom": "Como alternativa, realizar a ligação com MetaMask irá sempre abrir a Phantom.", "selectWalletSettingDescriptionLineAlwaysAsk": "Como alternativa, ao realizar a ligação com MetaMask, iremos perguntar-lhe se prefere antes usar a Phantom.", "selectWalletSettingDescriptionLineMetaMask": "Definir MetaMask como predefinição irá desativar a ligação dessas dapps à Phantom.", "metaMaskOverride": "Carteira predefinida da aplicação", "metaMaskOverrideSettingDescriptionLine1": "Para a ligação a websites que não oferecem a opção de usar a Phantom.", "refreshAndReconnectToast": "Atualize e volte a ligar para aplicar as suas alterações", "autoConfirmUnavailable": "Indisponível", "autoConfirmReasonDappNotWhitelisted": "Não disponível porque o contrato de onde provém não consta da nossa lista de permissões para esta aplicação.", "autoConfirmReasonSessionNotActive": "Não disponível porque não existe uma sessão de confirmação automática ativa. Ative-a abaixo.", "autoConfirmReasonRateLimited": "Não disponível porque a dapp que está a utilizar está a enviar demasiados pedidos.", "autoConfirmReasonUnsupportedNetwork": "Não disponível porque a confirmação automática ainda não suporta esta rede.", "autoConfirmReasonSimulationFailed": "Não disponível porque não podemos garantir a segurança.", "autoConfirmReasonTabNotFocused": "Não disponível porque o separador do domínio que está a tentar confirmar automaticamente não está ativo.", "autoConfirmReasonNotUnlocked": "Não disponível porque a carteira não foi desbloqueada.", "rpcErrorUnauthorizedWrongAccount": "A transação do endereço não corresponde ao endereço de conta selecionado.", "rpcErrorUnauthorizedUnknownSource": "Não foi possível determinar a fonte de pedido RPC.", "transactionsDisabledTitle": "Transações desativadas", "transactionsDisabledMessage": "O seu endereço não permite realizar transações com a Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Ativo", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL copiado para a área de transferência", "notEnoughSolScanTransactionWarning": "Esta transação pode falhar devido a SOL insuficientes na sua conta. Adicione mais SOL na sua conta e tente outra vez.", "transactionRevertedWarning": "Esta transação foi revertida durante a simulação. Poderá perder os fundos se estes forem enviados.", "slippageToleranceExceeded": "Esta transação foi revertida durante a simulação. Tolerância de deslizamento excedida.", "simulationWarningKnownMalicious": "Acreditamos que esta transação é maliciosa. A aprovação poderá levar à perda de fundos.", "simulationWarningPoisonedAddress": "Este endereço é muito idêntico a um endereço para o qual enviou recentemente fundos. Confirme se este é o endereço correto para evitar perder fundos com uma fraude.", "simulationWarningInteractingWithAccountWithoutActivity": "Esta é uma conta sem qualquer atividade anterior. Enviar fundos para uma conta não existente pode levar à perda de fundos", "quests": "Pedidos", "questsClaimInProgress": "Resgate em curso", "questsVerifyingCompletion": "A verificar conclusão de pedido", "questsClaimError": "Erro de resgate de recompensa", "questsClaimErrorDescription": "Ocorreu um erro ao resgatar a recompensa. Tente outra vez mais tarde.", "questsBadgeMobileOnly": "Apenas telemóvel", "questsBadgeExtensionOnly": "Apenas extensão", "questsExplainerSheetButtonLabel": "OK", "questsNoQuestsAvailable": "Não existem missões disponíveis", "questsNoQuestsAvailableDescription": "Não existem missões atualmente disponíveis. Iremos avisá-lo assim que forem adicionadas missões novas.", "exploreLearn": "Aprender", "exploreSites": "<PERSON><PERSON><PERSON><PERSON>", "exploreTokens": "Tokens", "exploreQuests": "<PERSON><PERSON><PERSON>", "exploreCollections": "Coleções", "exploreFilterByall_networks": "<PERSON><PERSON> as redes", "exploreSortByrank": "Tendências", "exploreSortBytrending": "Tendências", "exploreSortByprice": "Preço", "exploreSortByprice-change": "Alteração do preço", "exploreSortBytop": "Topo", "exploreSortByvolume": "Volume", "exploreSortBygainers": "Ganhos", "exploreSortBylosers": "<PERSON><PERSON>", "exploreSortBymarket-cap": "Limite de mercado", "exploreSortBymarket_cap": "Limite de mercado", "exploreTimeFrame1h": "1 h", "exploreTimeFrame24h": "24 h", "exploreTimeFrame7d": "7 d", "exploreTimeFrame30d": "30 d", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Colecionáveis", "exploreCategoryMarketplace": "<PERSON><PERSON><PERSON>", "exploreCategoryGaming": "Jogos", "exploreCategoryBridges": "<PERSON><PERSON>", "exploreCategoryOther": "Outro", "exploreCategorySocial": "Social", "exploreCategoryCommunity": "Comunidade", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Staking", "exploreCategoryArt": "Arte", "exploreCategoryTools": "Ferramentas", "exploreCategoryDeveloperTools": "Ferramentas de programação", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Staking NFT", "exploreCategoryExplorer": "Explorador", "exploreCategoryInscriptions": "Inscrições", "exploreCategoryBridge": "<PERSON><PERSON>", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Verificador Airdrop", "exploreCategoryPoints": "Pontos", "exploreCategoryQuests": "<PERSON><PERSON><PERSON>", "exploreCategoryShop": "<PERSON><PERSON>", "exploreCategoryProtocol": "Protocolo", "exploreCategoryNamingService": "Serviço de designação", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "<PERSON><PERSON><PERSON><PERSON>ó<PERSON>", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volume", "exploreFloor": "<PERSON><PERSON>", "exploreCap": "Limite de mercado", "exploreToken": "Token", "explorePrice": "Preço", "explore24hVolume": "Volume de 24h", "exploreErrorButtonText": "Tentar novamente", "exploreErrorDescription": "Ocorreu um erro ao tentar carregar o conteúdo de exploração. Realize a atualização e tente outra vez", "exploreErrorTitle": "Falha ao carregar o conteúdo de exploração", "exploreNetworkError": "Houve um erro da rede. Tente outra vez mais tarde.", "exploreTokensLegalDisclaimer": "As listas de tokens são geradas utilizando dados de mercado fornecidos por vários fornecedores terceiros incluindo CoinGecko, Birdeye e Jupiter. O desempenho é baseado no período de 24 horas anteriores. O desempenho passado não é indicativo do desempenho futuro.", "swapperTokensLegalDisclaimer": "As listas de tokens em destaque são geradas utilizando dados de mercado fornecidos por vários fornecedores terceiros incluindo CoinGecko, Birdeye e Jupiter e baseadas em tokens populares trocados por utilizadores Phantom através do Substituidor durante o período de tempo indicado. O desempenho passado não é indicativo do desempenho futuro.", "exploreLearnErrorTitle": "Falha ao carregar o conteúdo de aprendizagem", "exploreLearnErrorDescription": "Ocorreu um erro ao tentar carregar o conteúdo de aprendizagem. Realize a atualização e tente outra vez", "exploreShowMore": "<PERSON><PERSON> mais", "exploreShowLess": "<PERSON><PERSON> menos", "exploreVisitSite": "Visitar sítio", "dappBrowserSearchScreenVisitSite": "Visitar sítio", "dappBrowserSearchScreenSearchWithGoogle": "Pesquisar com Google", "dappBrowserSearchScreenSearchLinkYouCopied": "Ligação que copiou", "dappBrowserExtSearchPlaceholder": "Procure por websites, tokens", "dappBrowserSearchNoAppsTokens": "Não foram encontrados tokens ou aplicações", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON><PERSON>r separadores mais antigos?", "dappBrowserTabsLimitExceededScreenDescription": "Tem {{tabsCount}} separadores abertos. Para abrir mais, ter<PERSON> de fechar alguns separadores.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON><PERSON> todos os separadores", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: este domínio não existe", "dappBrowserTabErrorHttp": "Bloqueado, utilize HTTPS", "dappBrowserTabError401Unauthorized": "401 Não autorizado", "dappBrowserTabError501UnhandledRequest": "501 Pedido não tratado", "dappBrowserTabErrorTimeout": "TIMEOUT: o servidor demorou demasiado tempo a responder", "dappBrowserTabErrorInvalidResponse": "Resposta inválida", "dappBrowserTabErrorEmptyResponse": "Resposta vazia", "dappBrowserTabErrorGeneric": "Ocorreu um erro", "localizedErrorUnknownError": "Ocorreu um erro, tente outra vez mais tarde.", "localizedErrorUnsupportedCountry": "Lamentamos, o seu país não é atualmente suportado.", "localizedErrorTokensNotLoading": "Ocorreu um problema a carregar os seus tokens. Tente outra vez.", "localizedErrorSwapperNoQuotes": "Nenhuma troca disponível devido a par não suportado, baixa liquidez, ou baixo montante. Experimente ajustar o token ou o montante.", "localizedErrorSwapperRefuelNoQuotes": "Nenhum orçamento encontrado. Experimente uma quantia ou um token diferente ou desative a recarga.", "localizedErrorInsufficientSellAmount": "Número de tokens muito baixo. Aumente o valor para trocar a cadeia cruzada.", "localizedErrorCrossChainUnavailable": "As trocas de cadeia cruzada não estão atualmente disponíveis, tente outra vez mais tarde.", "localizedErrorTokenNotTradable": "Um dos tokens selecionados não é negociável. Selecione um token diferente.", "localizedErrorCollectibleLocked": "A conta do token está bloqueada.", "localizedErrorCollectibleListed": "A conta do token está listada.", "spamActivityAction": "Ver itens ocultos", "spamActivityTitle": "Atividade oculta", "spamActivityWarning": "Esta transação estava oculta porque a Phantom acredita que pode ser corrreio indesejado.", "appAuthenticationFailed": "Falha de autenticação", "appAuthenticationFailedDescription": "Ocorreu um problema com a sua tentativa de autenticação. Tente outra vez.", "partialErrorBalanceChainName": "Estamos a ter problemas em atualizar os seus saldos {{chainName}}. Os seus fundos estão a salvo.", "partialErrorGeneric": "Estamos a ter problemas em atualizar as redes, alguns dos seus preços e saldos de tokens podem estar desatualizados. Os seus fundos estão a salvo.", "partialErrorTokenDetail": "Estamos a ter problemas em atualizar o seu saldo de token. Os seus fundos estão a salvo.", "partialErrorTokenPrices": "Estamos a ter problemas em atualizar os seus preços de token. Os seus fundos estão a salvo.", "partialErrorTokensTrimmed": "Estamos a ter algumas dificuldades em exibir todos os tokens no seu portfólio. Os seus fundos estão seguros.", "publicFungibleDetailAbout": "Sobre", "publicFungibleDetailYourBalance": "O seu saldo", "publicFungibleDetailInfo": "Info.", "publicFungibleDetailShowMore": "<PERSON><PERSON> mais", "publicFungibleDetailShowLess": "<PERSON><PERSON> menos", "publicFungibleDetailPerformance": "Desempen<PERSON> de 24h", "publicFungibleDetailSecurity": "Segurança", "publicFungibleDetailMarketCap": "Limite de mercado", "publicFungibleDetailTotalSupply": "Fornecimento total", "publicFungibleDetailCirculatingSupply": "Fornecimento em circulação", "publicFungibleDetailMaxSupply": "Fornecimento máx.", "publicFungibleDetailHolders": "Detentores", "publicFungibleDetailVolume": "Volume", "publicFungibleDetailTrades": "Transações", "publicFungibleDetailTraders": "Comerciantes", "publicFungibleDetailUniqueWallets": "Carteiras únicas", "publicFungibleDetailTop10Holders": "10 detentores principais", "publicFungibleDetailTop10HoldersTooltip": "Indica a percentagem do fornecimento total atual na posse dos 10 principais proprietários do token. É uma medida do nível de facilidade de manipulação do preço.", "publicFungibleDetailMintable": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailMintableTooltip": "O fornecimento do token pode ser aumentado pelo proprietário do contrato se um token for cunhável.", "publicFungibleDetailMutableInfo": "Informação mutável", "publicFungibleDetailMutableInfoTooltip": "Se a informação do token como, por exemplo, nome, logótipo, e endereço do website for mutável esta pode ser alterada pelo proprietário do contacto.", "publicFungibleDetailOwnershipRenounced": "<PERSON><PERSON><PERSON>", "publicFungibleDetailOwnershipRenouncedTooltip": "Se ocorrer a ren<PERSON><PERSON> do token, ninguém pode executar funções como, por exemplo, cunhar mais tokens.", "publicFungibleDetailUpdateAuthority": "Atualizar autoridade", "publicFungibleDetailUpdateAuthorityTooltip": "A autoridade de atualização é o endereço da carteira que pode alterar informação se um token for mutável.", "publicFungibleDetailFreezeAuthority": "Congelar autoridade", "publicFungibleDetailFreezeAuthorityTooltip": "A autoridade de congelar é o endereço da carteira que pode impedir a transferência de fundos.", "publicFungibleUnverifiedToken": "Este token não está verificado. Realize interações apenas com tokens em que confie.", "publicFungibleDetailSwap": "Trocar {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Trocar {{tokenSymbol}} com a aplicação Phantom", "publicFungibleDetailLinkCopied": "Copiado para a área de transferência", "publicFungibleDetailContract": "Contrato", "publicFungibleDetailMint": "<PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "Atividade", "unifiedTokenDetailSeeMoreTransactionActivity": "Ver mais", "unifiedTokenDetailTransactionActivityError": "Falha ao carregar a atividade recente", "additionalNetworksTitle": "Redes adicionais", "copyAddressRowAdditionalNetworks": "Redes adicionais", "copyAddressRowAdditionalNetworksHeader": "As redes seguintes utilizam o mesmo endereço que em Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Pode utilizar o seu endereço Ethereum em segurança para enviar e receber ativos em qualquer uma destas redes.", "cpeUnknownError": "<PERSON><PERSON>conhe<PERSON>", "cpeUnknownInstructionError": "Erro de instrução desconhecida", "cpeAccountFrozen": "A conta está congelada", "cpeAssetFrozen": "O ativo está congelado", "cpeInsufficientFunds": "Fundos insuficientes", "cpeInvalidAuthority": "Autoridade inválida", "cpeBalanceBelowRent": "Saldo abaixo do limiar de insenção de aluguer", "cpeNotApprovedForConfidentialTransfers": "Conta não aprovada para transferências privadas", "cpeNotAcceptingDepositsOrTransfers": "A conta não aceita depósitos ou transferências", "cpeNoMemoButRequired": "Nenhum memorando na instrução anterior; necessário para que o destinatário receba uma transferência", "cpeTransferDisabledForMint": "A transferência está desativada para esta cunhagem", "cpeDepositAmountExceedsLimit": "O montante do depósito excede o limite máximo", "cpeInsufficientFundsForRent": "Fundos insuficientes para o aluguer", "reportIssueScreenTitle": "Comunicar um problema", "publicFungibleReportIssuePrompt": "Qual o problema que pretende comunicar sobre {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "Informação incorreta", "publicFungibleReportIssuePriceStale": "O preço não está a ser atualizado", "publicFungibleReportIssuePriceMissing": "O preço está em falta", "publicFungibleReportIssuePerformanceIncorrect": "O desempenho 24h está incorreto", "publicFungibleReportIssueLinkBroken": "Ligações sociais não alcançáveis", "publicFungibleDetailErrorLoading": "Dados do token não disponíveis", "reportUserPrompt": "Qual o problema que pretende comunicar sobre @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "Abuso e assédio", "reportUserOptionAbuseAndHarrassmentDescription": "Assédio específico, incitação a assédio, ameaças violentas, referências e conteúdo de ódio", "reportUserOptionPrivacyAndImpersonationTitle": "Privacidade e usurpação de identidade", "reportUserOptionPrivacyAndImpersonationDescription": "Partilhar ou ameaçar a exposição de informação privada, fingir ser uma outra pessoa", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "Conta falsa, fraudes, ligações maliciosas", "reportUserSuccess": "Comunicação de utilizador enviada.", "settingsClaimUsernameTitle": "Criar nome de utilizador", "settingsClaimUsernameDescription": "Uma identidade única tão única como a sua carteira", "settingsClaimUsernameValueProp1": "Identidade simplificada", "settingsClaimUsernameValueProp1Description": "Diga adeus a endereços complexos e longos e diga olá a uma identidade amiga do utilizador", "settingsClaimUsernameValueProp2": "<PERSON><PERSON> r<PERSON> e mais fácil", "settingsClaimUsernameValueProp2Description": "Envie e receba cripto mais facilmente, inicie a sessão na sua carteira, e ligue-se aos seus amigos", "settingsClaimUsernameValueProp3": "Mantenha-se sincronizado", "settingsClaimUsernameValueProp3Description": "Ligue qualquer conta ao seu nome de utilizador e este irá sincronizar-se em todos os seus dispositivos", "settingsClaimUsernameHelperText": "O seu nome único para a sua conta Phantom", "settingsClaimUsernameValidationDefault": "Este nome de utilizador não poderá ser alterado posteriormente", "settingsClaimUsernameValidationAvailable": "Nome de utilizador disponível", "settingsClaimUsernameValidationUnavailable": "Nome de utilizador não disponível", "settingsClaimUsernameValidationServerError": "Não é possível verificar se o nome do utilizador está disponível, tente outra vez mais tarde", "settingsClaimUsernameValidationErrorLine1": "Nome de utilizador inválido.", "settingsClaimUsernameValidationErrorLine2": "Os nomes de utilizador têm de ter entre {{minChar}} e {{maxChar}} caracteres de comprimento e só podem conter letras e números.", "settingsClaimUsernameValidationLoading": "A verificar se este nome de utilizador está disponível...", "settingsClaimUsernameSaveAndContinue": "Guardar e continuar", "settingsClaimUsernameChooseAvatarTitle": "E<PERSON><PERSON>her avatar", "settingsClaimUsernameAnonymousAuthTitle": "Aut. anónima", "settingsClaimUsernameAnonymousAuthDescription": "Inicie a sessão na sua Conta Phantom de forma anónima com uma assinatura", "settingsClaimUsernameAnonymousAuthBadge": "Saiba como funciona", "settingsClaimUsernameLinkWalletsTitle": "<PERSON><PERSON><PERSON><PERSON> as suas carteiras", "settingsClaimUsernameLinkWalletsDescription": "<PERSON><PERSON><PERSON><PERSON> as carteiras que surgem em outros dispositivos com o seu nome de utilizador", "settingsClaimUsernameLinkWalletsBadge": "Não pode ser visto publicamente", "settingsClaimUsernameConnectAccountsTitle": "Ligar contas", "settingsClaimUsernameConnectAccountsHelperText": "Cada endereço de cadeia será ligado ao seu nome de utilizador. Pode alterá-los posteriormente.", "settingsClaimUsernameContinue": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameCreateUsername": "Criar nome de utilizador", "settingsClaimUsernameCreating": "Criar nome de utilizador...", "settingsClaimUsernameSuccess": "Nome de utilizador criado!", "settingsClaimUsernameError": "Encontrámos um erro ao criar o seu nome de utilizador", "settingsClaimUsernameTryAgain": "Tentar novamente", "settingsClaimUsernameSuccessHelperText": "Pode agora utilizar o seu novo nome de utilizador em todas as suas carteiras Phantom", "settingsClaimUsernameSettingsSyncedTitle": "Definições sincronizadas", "settingsClaimUsernameSettingsSyncedHelperText": "Diga adeus a endereços complexos e longos e diga olá a uma identidade amiga do utilizador", "settingsClaimUsernameSendToUsernameTitle": "Enviar para nome de utilizador", "settingsClaimUsernameSendToUsernameHelperText": "Envie e receba cripto mais facilmente, inicie a sessão na sua carteira, e ligue-se aos seus amigos", "settingsClaimUsernameManageAddressesTitle": "Endereços públicos", "settingsClaimUsernameManageAddressesHelperText": "Quaisquer tokens ou colecionáveis enviados para o seu nome de utilizador enviarão para estes endereços", "settingsClaimUsernameManageAddressesBadge": "Pode ser visto publicamente", "settingsClaimUsernameEditAddressesTitle": "<PERSON><PERSON><PERSON> end<PERSON> públic<PERSON>", "settingsClaimUsernameEditAddressesHelperText": "Quaisquer tokens ou colecionáveis enviados para o seu nome de utilizador enviarão para estes endereços. Selecione um endereço por cadeia.", "settingsClaimUsernameEditAddressesError": "Só é permitido um endereço por rede.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON><PERSON>", "settingsClaimUsernameNoAddressesSaved": "Nenhum endereço público gravado", "settingsClaimUsernameSave": "Guardar", "settingsClaimUsernameDone": "<PERSON><PERSON>", "settingsClaimUsernameWatching": "A observar", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} conta(s)", "settingsClaimUsernameNoOfAccountsSingular": "1 conta", "settingsClaimUsernameEmptyAccounts": "<PERSON><PERSON><PERSON><PERSON> conta", "settingsClaimUsernameSettingTitle": "Crie o seu @nomedeutilizador", "settingsClaimUsernameSettingDescription": "Uma identidade única para a sua carteira", "settingsManageUserProfileAbout": "Sobre", "settingsManageUserProfileAboutUsername": "Nome de utilizador", "settingsManageUserProfileAboutBio": "Bio", "settingsManageUserProfileTitle": "<PERSON><PERSON><PERSON>l", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "Fatores de autenticação", "settingsManageUserProfileAuthFactorsDescription": "<PERSON><PERSON><PERSON><PERSON> quais as frases semente ou chaves privadas podem iniciar a sessão na sua Conta Phantom.", "settingsManageUserProfileUpdateAuthFactorsToast": "Fatores de autenticação atualizados!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Falha ao atualizar fatores de autenticação!", "settingsManageUserProfileBiography": "Editar biografia", "settingsManageUserProfileBiographyDescription": "Adicionar uma breve biografia ao seu perfil", "settingsManageUserProfileUpdateBiographyToast": "Biografia atualizada!", "settingsManageUserProfileUpdateBiographyToastFailure": "Ocorreu algo. Tente outra vez", "settingsManageUserProfileBiographyNoUrlMessage": "Retire quaisquer URL da sua biografia", "settingsManageUserProfileLinkedWallets": "Carteiras ligadas", "settingsManageUserProfileLinkedWalletsDescription": "<PERSON><PERSON><PERSON><PERSON> as carteiras que surgem em outros dispositivos ao iniciar a sessão na sua Conta Phantom.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Carteiras ligadas atualizadas!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Falha ao atualizar as carteiras ligadas!", "settingsManageUserProfilePrivacy": "Privacidade", "settingsManageUserProfileUpdatePrivacyStateToast": "Privacidade atualizada!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Falha ao atualizar a privacidade!", "settingsManageUserProfilePublicAddresses": "Endereços públicos", "settingsManageUserProfileUpdatePublicAddressToast": "Endereço público atualizado!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Falha ao atualizar o endereço público!", "settingsManageUserProfilePrivacyStatePublic": "Público", "settingsManageUserProfilePrivacyStatePublicDescription": "O seu perfil e os seus endereços públicos podem ser vistos e pesquisados por qualquer pessoa", "settingsManageUserProfilePrivacyStatePrivate": "Privado", "settingsManageUserProfilePrivacyStatePrivateDescription": "O seu perfil pode ser pesquisado por qualquer pessoa mas outros têm de pedir autorização para ver o seu perfil e os seus endereços públicos", "settingsManageUserProfilePrivacyStateInvisible": "Invisível", "settingsManageUserProfilePrivacyStateInvisibleDescription": "O seu perfil e os seus endereços públicos estão ocultos e não podem ser descobertos em parte alguma", "settingsDownloadPhantom": "<PERSON><PERSON><PERSON><PERSON> a Phantom", "settingsLogOut": "<PERSON><PERSON><PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "Adicionar uma carteira", "seedlessAddAWalletSecondaryText": "Iniciar a sessão ou importar uma carteira existente ", "seedlessAddSeedlessWalletPrimaryText": "Adicionar carteira sem semente", "seedlessAddSeedlessWalletSecondaryText": "Utilize a sua Apple ID, Google ou e-mail", "seedlessCreateNewWalletPrimaryText": "Criar uma carteira nova?", "seedlessCreateNewWalletSecondaryText": "Este e-mail não tem uma carteira, pretende criar uma?", "seedlessCreateNewWalletButtonText": "<PERSON><PERSON><PERSON>", "seedlessCreateNewWalletNoBundlePrimaryText": "Carteira não encontrada", "seedlessCreateNewWalletNoBundleSecondaryText": "Este e-mail não tem uma carteira", "seedlessCreateNewWalletNoBundleButtonText": "Voltar", "seedlessEmailOptionsPrimaryText": "Selecione o seu e-mail", "seedlessEmailOptionsSecondaryText": "Adicione uma carteira com a sua conta Google ou Apple ", "seedlessEmailOptionsButtonText": "Continuar com o e-mail", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Carteira criada com a sua ID Apple", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Carteira criada com o seu e-mail Google", "seedlessAlreadyExistsPrimaryText": "A conta já existe", "seedlessAlreadyExistsSecondaryText": "Este e-mail já tem uma carteira criada, pretende em vez disso iniciar a sessão?", "seedlessSignUpWithAppleButtonText": "Registar-se com a Apple", "seedlessContinueWithAppleButtonText": "Continuar com a Apple", "seedlessSignUpWithGoogleButtonText": "Registar-se com o Google", "seedlessContinueWithGoogleButtonText": "Continuar com o Google", "seedlessCreateAPinPrimaryText": "<PERSON>riar um PIN", "seedlessCreateAPinSecondaryText": "Isto é utilizado para tornar segura a sua carteira em todos os seus dispositivos. <1>Isto não pode ser recuperado.</1>", "seedlessContinueText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessConfirmPinPrimaryText": "Confirme o seu PIN", "seedlessConfirmPinSecondaryText": "Se esquecer o seu PIN, não poderá recuperar a sua carteira num novo dispositivo.", "seedlessConfirmPinButtonText": "Criar PIN", "seedlessConfirmPinError": "PIN incorreto. Tente outra vez", "seedlessAccountsImportedPrimaryText": "Contas importadas", "seedlessAccountsImportedSecondaryText": "Estas contas serão importadas automaticamente na sua carteira", "seedlessPreviouslyImportedTag": "Importado anteriormente", "seedlessEnterPinPrimaryText": "Introduza o seu PIN", "seedlessEnterPinInvalidPinError": "PIN incorreto introduzido. Só são permitidos números de 4 dígitos", "seedlessEnterPinNumTriesLeft": "{{numTries}} tentativas restantes.", "seedlessEnterPinCooldown": "Tente outra vez dentro de {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "O PIN tem de ter exatamente 4 dígitos", "seedlessEnterPinMatch": "Os PIN são idênticos", "seedlessDoneText": "<PERSON><PERSON>", "seedlessEnterPinToSign": "Introduza o seu PIN para assinar esta transação", "seedlessSigning": "A assinar", "seedlessCreateSeed": "Crie uma carteira de frase semente", "seedlessImportOptions": "Outras opções de importação", "seedlessImportPrimaryText": "Opções de importação", "seedlessImportSecondaryText": "Importe uma carteira existente com a sua frase semente, chave privada, ou carteira de hardware", "seedlessImportSeedPhrase": "Importar frase semente", "seedlessImportPrivateKey": "Importar chave privada", "seedlessConnectHardwareWallet": "Ligar carteira de hardware", "seedlessTryAgain": "Tentar novamente", "seedlessCreatingWalletPrimaryText": "A criar carteira", "seedlessCreatingWalletSecondaryText": "A adicionar uma carteira social", "seedlessLoadingWalletPrimaryText": "A carregar carteira", "seedlessLoadingWalletSecondaryText": "A importar e a observar as suas carteiras associadas", "seedlessLoadingWalletErrorPrimaryText": "Falha ao carregar a carteira", "seedlessCreatingWalletErrorPrimaryText": "Falha ao criar a carteira", "seedlessErrorSecondaryText": "Tente outra vez", "seedlessAuthAlreadyExistsErrorText": "O endereço de e-mail fornecido já pertence a uma conta Phantom diferente", "seedlessAuthUnknownErrorText": "Ocorreu um erro desconhecido, tente outra vez mais tarde", "seedlessAuthUnknownErrorTextRefresh": "Ocorreu um erro desconhecido, tente outra vez mais tarde. Atualize a página para tentar outra vez.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON>", "seedlessWalletExistsErrorPrimaryText": "Já existe uma carteira social no seu dispositivo", "seedlessWalletExistsErrorSecondaryText": "Retroceda ou feche este ecrã", "seedlessValueProp1PrimaryText": "Configuração sem problemas", "seedlessValueProp1SecondaryText": "Crie uma carteira utilizando uma conta Google ou Apple e comece a explorar web3 com facilidade", "seedlessValueProp2PrimaryText": "Segurança reforçada", "seedlessValueProp2SecondaryText": "A sua carteira está guardada de forma segura e descentralizada ao longo de múltiplos fatores", "seedlessValueProp3PrimaryText": "Recuperação fácil", "seedlessValueProp3SecondaryText": "Recupere o acesso à sua carteira com a sua conta Google ou Apple e um PIN de 4 dígitos", "seedlessLoggingIn": "A iniciar a sessão...", "seedlessSignUpOrLogin": "Registo ou início de sessão", "seedlessContinueByEnteringYourEmail": "Continue introduzindo o seu e-mail", "seedless": "Sem semente", "seed": "Frases semente", "seedlessVerifyPinPrimaryText": "Verificar PIN", "seedlessVerifyPinSecondaryText": "Introduza o seu número de PIN para continuar", "seedlessVerifyPinVerifyButtonText": "Verificar", "seedlessVerifyPinForgotButtonText": "Esqueceu?", "seedlessPinConfirmButtonText": "Confirmar", "seedlessVerifyToastPrimaryText": "Verifique o seu PIN", "seedlessVerifyToastSecondaryText": "Iremos ocasionalmente pedir-lhe o seu número de PIN pelo que deve lembrar-se dele. Caso se esqueça, não poderá recuperar a sua carteira.", "seedlessVerifyToastSuccessText": "O seu número de PIN está verificado!", "seedlessForgotPinPrimaryText": "Repor PIN utilizando outro dispositivo", "seedlessForgotPinSecondaryText": "Por uma questão de segurança, só pode repor o seu PIN em outros dispositivos onde tem a sessão iniciada", "seedlessForgotPinInstruction1PrimaryText": "Abrir outro dispositivo", "seedlessForgotPinInstruction1SecondaryText": "Ir para outro dispositivo em que a sua conta Phantom esteja iniciada com o seu e-mail", "seedlessForgotPinInstruction2PrimaryText": "<PERSON><PERSON> <PERSON> as definiç<PERSON><PERSON>", "seedlessForgotPinInstruction2SecondaryText": "Nas Definições, selecione “Segurança e privacidade” e, em seguida, “Repor PIN”", "seedlessForgotPinInstruction3PrimaryText": "Defina o seu novo PIN", "seedlessForgotPinInstruction3SecondaryText": "Assim que definir o seu novo PIN, pode iniciar a sessão na sua carteira neste dispositivo", "seedlessForgotPinButtonText": "Já realizei estes passos", "seedlessResetPinPrimaryText": "Repor PIN", "seedlessResetPinSecondaryText": "Introduza um novo PIN de que se consiga lembrar. Este será usado para tornar a sua carteira segura em todos os seus dispositivos", "seedlessResetPinSuccessText": "O número do seu PIN está atualizado!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "<PERSON>o criar uma carteira, aceita os nossos <1>Termos de Serviço</1> e <5>Política de Privacidade</5>", "pageNotFound": "Página não encontrada", "pageNotFoundDescription": "Não o abandonámos! Esta página não existe, ou foi movida.", "webTokenPagesLegalDisclaimer": "A informação sobre preços é fornecida apenas para efeitos informativos e não se trata de aconselhamento financeiro. Os dados sobre o mercado são fornecidos por terceiros e a Phantom não se responsabilize pela exatidão da informação.", "signUpOrLogin": "Registo ou início de sessão", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Ao criar uma conta, aceita os nossos <1>Termos de Serviço</1> e <5>Política de Privacidade</5>", "feedNoActivity": "<PERSON>da nenhuma atividade", "followRequests": "<PERSON><PERSON><PERSON> ped<PERSON>", "following": "A seguir", "followers": "<PERSON><PERSON><PERSON><PERSON>", "follower": "<PERSON><PERSON><PERSON><PERSON>", "joined": "Adesão", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON><PERSON><PERSON>", "noFollowing": "A não seguir ninguém", "noUsersFound": "Nenhum utilizador encontrado", "viewProfile": "Ver perfil", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}