{"commandAdd": "<PERSON><PERSON><PERSON><PERSON>", "commandAccept": "<PERSON><PERSON><PERSON>", "commandApply": "<PERSON><PERSON>", "commandApprove": "<PERSON><PERSON>", "commandAllow": "<PERSON> phép", "commandBack": "Quay lại", "commandBuy": "<PERSON><PERSON>", "commandCancel": "<PERSON><PERSON><PERSON>", "commandClaim": "Nhậ<PERSON>", "commandClaimReward": "Nhận phần thưởng của bạn", "commandClear": "Xóa", "commandClose": "Đ<PERSON><PERSON>", "commandConfirm": "<PERSON><PERSON><PERSON>", "commandConnect": "<PERSON><PERSON><PERSON>", "commandContinue": "<PERSON><PERSON><PERSON><PERSON>", "commandConvert": "<PERSON><PERSON><PERSON><PERSON> đổi", "commandCopy": "Sao chép", "commandCopyAddress": "<PERSON>o ch<PERSON>p địa chỉ", "commandCopyTokenAddress": "<PERSON><PERSON> chép địa chỉ token", "commandCreate": "Tạo", "commandCreateTicket": "<PERSON><PERSON><PERSON>", "commandDeny": "<PERSON><PERSON> chối", "commandDismiss": "Đ<PERSON><PERSON>", "commandDontAllow": "<PERSON><PERSON><PERSON><PERSON> cho phép", "commandDownload": "<PERSON><PERSON><PERSON> về", "commandEdit": "<PERSON><PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON><PERSON> h<PERSON> sơ", "commandEnableNow": "<PERSON><PERSON><PERSON> ngay", "commandFilter": "<PERSON><PERSON><PERSON>", "commandFollow": "<PERSON>", "commandHelp": "<PERSON><PERSON><PERSON> g<PERSON>", "commandLearnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "commandLearnMore2": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "commandMint": "<PERSON><PERSON><PERSON>", "commandMore": "<PERSON><PERSON><PERSON><PERSON>", "commandNext": "<PERSON><PERSON><PERSON><PERSON> theo", "commandNotNow": "Để sau", "commandOpen": "Mở", "commandOpenSettings": "Mở Cài đặt", "commandPaste": "Dán", "commandReceive": "Nhậ<PERSON>", "commandReconnect": "<PERSON><PERSON><PERSON><PERSON> lại", "commandRecordVideo": "Quay video", "commandRequest": "<PERSON><PERSON><PERSON> c<PERSON>", "commandRetry": "<PERSON><PERSON><PERSON> lại", "commandReview": "<PERSON><PERSON>", "commandRevoke": "<PERSON><PERSON> h<PERSON>", "commandSave": "<PERSON><PERSON><PERSON>", "commandScanQRCode": "Quét mã QR", "commandSelect": "<PERSON><PERSON><PERSON>", "commandSelectMedia": "<PERSON><PERSON><PERSON> nội dung đa ph<PERSON>ơng tiện", "commandSell": "Bán", "commandSend": "<PERSON><PERSON><PERSON>", "commandShare": "<PERSON><PERSON> sẻ", "commandShowBalance": "<PERSON><PERSON><PERSON> thị số dư", "commandSign": "<PERSON><PERSON>", "commandSignOut": "Sign Out", "commandStake": "<PERSON><PERSON><PERSON>", "commandMintLST": "Mint JitoSOL", "commandSwap": "<PERSON><PERSON>", "commandSwapAgain": "<PERSON><PERSON> đ<PERSON>i lại", "commandTakePhoto": "<PERSON><PERSON><PERSON>", "commandTryAgain": "<PERSON><PERSON><PERSON> lại", "commandViewTransaction": "<PERSON>em giao d<PERSON>ch", "commandReportAsNotSpam": "Báo cáo là không phải rác", "commandReportAsSpam": "Báo cáo là rác", "commandPin": "<PERSON><PERSON>", "commandBlock": "Chặn", "commandUnblock": "Bỏ chặn", "commandUnstake": "<PERSON><PERSON><PERSON>", "commandUnpin": "Bỏ ghim", "commandHide": "Ẩn", "commandUnhide": "<PERSON><PERSON><PERSON> thị", "commandBurn": "<PERSON><PERSON><PERSON>", "commandReport": "Báo cáo", "commandView": "Xem", "commandProceedAnywayUnsafe": "Vẫn tiếp tục (khô<PERSON> an toàn)", "commandUnfollow": "Bỏ theo dõi", "commandUnwrap": "Mở bọc", "commandConfirmUnsafe": "<PERSON><PERSON><PERSON> (không an toàn)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, x<PERSON>c <PERSON> (không an toàn)", "commandConfirmAnyway": "Vẫn xác nhận", "commandReportIssue": "<PERSON><PERSON>o c<PERSON>o một vấn đề", "commandSearch": "<PERSON><PERSON><PERSON>", "commandShowMore": "<PERSON><PERSON><PERSON> thị thêm", "commandShowLess": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t", "pastParticipleClaimed": "Đã nhận", "pastParticipleCompleted": "<PERSON><PERSON> hoàn thành", "pastParticipleCopied": "Đã sao chép", "pastParticipleDone": "<PERSON><PERSON>", "pastParticipleDisabled": "Đã tắt", "pastParticipleRequested": "<PERSON><PERSON> yêu cầu", "nounName": "<PERSON><PERSON><PERSON>", "nounNetwork": "Mạng l<PERSON>", "nounNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "nounSymbol": "<PERSON><PERSON><PERSON><PERSON>", "nounType": "<PERSON><PERSON><PERSON>", "nounDescription": "<PERSON><PERSON>", "nounYes": "<PERSON><PERSON>", "nounNo": "K<PERSON>ô<PERSON>", "amount": "<PERSON><PERSON> tiền", "limit": "<PERSON><PERSON><PERSON><PERSON> hạn", "new": "<PERSON><PERSON><PERSON>", "gotIt": "<PERSON><PERSON> hiểu", "internal": "<PERSON><PERSON><PERSON> bộ", "reward": "Phần thưởng", "seeAll": "<PERSON><PERSON> t<PERSON>t cả", "seeLess": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t", "viewAll": "<PERSON><PERSON> t<PERSON>t cả", "homeTab": "Trang chủ", "collectiblesTab": "<PERSON><PERSON><PERSON><PERSON><PERSON> tầm", "swapTab": "<PERSON><PERSON>", "activityTab": "<PERSON><PERSON><PERSON> đ<PERSON>", "exploreTab": "Khám phá", "accountHeaderConnectedInterpolated": "Bạn đã đư<PERSON>c kết nối với {{origin}}", "accountHeaderConnectedToSite": "Bạn đã đư<PERSON>c kết nối với website này", "accountHeaderCopyToClipboard": "Sao chép vào bộ nhớ tạm", "accountHeaderNotConnected": "<PERSON>ạn ch<PERSON><PERSON> đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "accountHeaderNotConnectedInterpolated": "<PERSON><PERSON>n ch<PERSON><PERSON> đ<PERSON><PERSON><PERSON> kết nối với {{origin}}", "accountHeaderNotConnectedToSite": "Bạn chưa đư<PERSON> kết nối với website này", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "Không đủ SOL", "accountWithoutEnoughSolSecondaryText": "Một tài khoản tham gia vào giao dịch này không có đủ SOL. Tài khoản này có thể là của bạn hay của một người khác. Giao dịch này sẽ hoàn tác nếu được gửi đi.", "accountSwitcher": "<PERSON><PERSON><PERSON><PERSON> tà<PERSON>n", "addAccountHardwareWalletPrimaryText": "<PERSON><PERSON><PERSON> n<PERSON>i ví phần cứng", "addAccountHardwareWalletSecondaryText": "Sử dụng ví phần cứng Ledger của bạn", "addAccountHardwareWalletSecondaryTextMobile": "Sử dụng ví {{supportedHardwareWallets}} c<PERSON><PERSON> bạn", "addAccountSeedVaultWalletPrimaryText": "<PERSON><PERSON><PERSON> n<PERSON> vớ<PERSON>", "addAccountSeedVaultWalletSecondaryText": "Sử dụng một ví từ Seed <PERSON>ault", "addAccountImportSeedPhrasePrimaryText": "<PERSON><PERSON><PERSON><PERSON> cụm từ khôi phục bí mật", "addAccountImportSeedPhraseSecondaryText": "<PERSON><PERSON><PERSON><PERSON> tài k<PERSON>n từ một ví khác", "addAccountImportWalletPrimaryText": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON> riêng", "addAccountImportWalletSecondaryText": "<PERSON><PERSON><PERSON><PERSON> một tài k<PERSON>ản đơn chuỗi", "addAccountImportWalletSolanaSecondaryText": "<PERSON><PERSON><PERSON><PERSON> một khóa riêng tư <PERSON>", "addAccountLimitReachedText": "Bạn đã đạt giới hạn tài khoản là {{accountsCount}} trong Phantom. Vui lòng xóa các tài khoản không sử dụng trư<PERSON><PERSON> khi bổ sung thêm tài khoản.", "addAccountNoSeedAvailableText": "Bạ<PERSON> không có cụm từ khôi phục nào. <PERSON><PERSON> lòng nhập một seed hiện có để tạo một tài kho<PERSON>n.", "addAccountNewWalletPrimaryText": "<PERSON><PERSON><PERSON> tài k<PERSON>n mới", "addAccountNewWalletSecondaryText": "Tạo địa chỉ ví mới", "addAccountNewMultiChainWalletSecondaryText": "<PERSON>h<PERSON><PERSON> một tài khoản đa chuỗi mới", "addAccountNewSingleChainWalletSecondaryText": "<PERSON><PERSON><PERSON><PERSON> một tài k<PERSON>ản mới", "addAccountPrimaryText": "Thêm / Kết nối ví", "addAccountSecretPhraseLabel": "<PERSON><PERSON><PERSON> từ bí mật", "addAccountSeedLabel": "Seed", "addAccountSeedIDLabel": "Seed ID", "addAccountSecretPhraseDefaultLabel": "<PERSON><PERSON><PERSON> từ bí mật {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON><PERSON><PERSON> riêng {{number}}", "addAccountZeroAccountsForSeed": "0 tài k<PERSON>", "addAccountShowAccountForSeed": "Hiển thị 1 tà<PERSON> k<PERSON>n", "addAccountShowAccountsForSeed": "<PERSON><PERSON><PERSON> thị {{numOfAccounts}} t<PERSON><PERSON> k<PERSON>n", "addAccountHideAccountForSeed": "Ẩn 1 tài k<PERSON>n", "addAccountHideAccountsForSeed": "Ẩn {{numOfAccounts}} t<PERSON><PERSON>n", "addAccountSelectSeedDescription": "Tài khoản mới của bạn sẽ được tạo từ Cụm từ bí mật này", "addAccountNumAccountsForSeed": "{{numOfAccounts}} t<PERSON><PERSON>", "addAccountOneAccountsForSeed": "1 tài k<PERSON>n", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON><PERSON> t<PERSON>", "addAccountReadOnly": "<PERSON> đ<PERSON> chỉ", "addAccountReadOnlySecondaryText": "<PERSON> mọi địa chỉ ví công khai", "addAccountSolanaAddress": "Địa chỉ Solana", "addAccountEVMAddress": "Địa chỉ EVM", "addAccountBitcoinAddress": "Địa chỉ Bitcoin", "addAccountCreateSeedTitle": "Tạo một tài k<PERSON>n mới", "addAccountCreateSeedExplainer": "Ví của bạn chưa có một cụm từ bí mật! Để tạo một ví mới, chúng tôi sẽ tạo cho bạn một cụm từ khôi phục. Hãy viết cụm từ này ra giấy và giữ nó ở chỗ bí mật.", "addAccountSecretPhraseHeader": "<PERSON><PERSON><PERSON> từ bí mật của bạn", "addAccountNoSecretPhrases": "<PERSON><PERSON><PERSON><PERSON> có cụm từ bí mật nào khả dụng", "addAccountImportAccountActionButtonImport": "<PERSON><PERSON><PERSON><PERSON>", "addAccountImportAccountDuplicatePrivateKey": "Tài khoản này đã tồn tại trong ví của bạn rồi", "addAccountImportAccountIncorrectFormat": "Định dạng sai", "addAccountImportAccountInvalidPrivateKey": "<PERSON><PERSON><PERSON><PERSON> riêng tư không hợp lệ", "addAccountImportAccountName": "<PERSON><PERSON><PERSON>", "addAccountImportAccountPrimaryText": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON> riêng", "addAccountImportAccountPrivateKey": "<PERSON><PERSON><PERSON> r<PERSON>", "addAccountImportAccountPublicKey": "Địa chỉ hoặc Tên miền", "addAccountImportAccountPrivateKeyRequired": "<PERSON><PERSON><PERSON> có khóa riêng", "addAccountImportAccountNameRequired": "<PERSON><PERSON><PERSON> có tên", "addAccountImportAccountPublicKeyRequired": "Địa chỉ công khai là bắt buộc", "addAccountImportAccountDuplicateAddress": "Địa chỉ này đã tồn tại trong ví của bạn rồi", "addAddressAddressAlreadyAdded": "Địa chỉ đã được thêm vào", "addAddressAddressAlreadyExists": "Địa chỉ đã tồn tại", "addAddressAddressInvalid": "Địa chỉ không hợp lệ", "addAddressAddressIsRequired": "<PERSON>ần có địa chỉ", "addAddressAddressPlaceholder": "Địa chỉ", "addAddressLabelIsRequired": "<PERSON><PERSON><PERSON> c<PERSON> n<PERSON>n", "addAddressLabelPlaceholder": "<PERSON><PERSON>ã<PERSON>", "addAddressPrimaryText": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "addAddressToast": "Địa chỉ đã đư<PERSON><PERSON> thêm", "createAssociatedTokenAccountCostLabelInterpolated": "<PERSON><PERSON><PERSON> này sẽ tốn {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Bạn đã có tài khoản token này rồi", "createAssociatedTokenAccountErrorInsufficientFunds": "Số tiền không đủ", "createAssociatedTokenAccountErrorInvalidMint": "Địa chỉ đú<PERSON> không hợp lệ", "createAssociatedTokenAccountErrorInvalidName": "<PERSON><PERSON><PERSON> kh<PERSON>ng h<PERSON> lệ", "createAssociatedTokenAccountErrorInvalidSymbol": "<PERSON><PERSON> hi<PERSON> kh<PERSON>ng h<PERSON>p lệ", "createAssociatedTokenAccountErrorUnableToCreateMessage": "<PERSON><PERSON>g tôi đã không thể tạo tài khoản token của bạn. <PERSON><PERSON> lòng thử lại.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "<PERSON><PERSON><PERSON><PERSON> thể tạo tài k<PERSON>n", "createAssociatedTokenAccountErrorUnableToSendMessage": "<PERSON><PERSON>g tôi không thể gửi giao dịch của bạn.", "createAssociatedTokenAccountErrorUnableToSendTitle": "<PERSON><PERSON><PERSON><PERSON> thể gửi giao dịch", "createAssociatedTokenAccountInputPlaceholderMint": "Đ<PERSON>a chỉ đúc tiền", "createAssociatedTokenAccountInputPlaceholderName": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderSymbol": "<PERSON><PERSON><PERSON><PERSON>", "createAssociatedTokenAccountLoadingMessage": "<PERSON><PERSON>g tôi đang tạo tài k<PERSON>n token của bạn.", "createAssociatedTokenAccountLoadingTitle": "<PERSON><PERSON><PERSON> t<PERSON> token", "createAssociatedTokenAccountPageHeader": "<PERSON><PERSON><PERSON> t<PERSON> token", "createAssociatedTokenAccountSuccessMessage": "T<PERSON><PERSON> k<PERSON>n token của bạn đã đư<PERSON>c tạo thành công!", "createAssociatedTokenAccountSuccessTitle": "<PERSON><PERSON><PERSON> token đã đ<PERSON><PERSON><PERSON> tạo", "createAssociatedTokenAccountViewTransaction": "<PERSON>em giao d<PERSON>ch", "assetDetailRecentActivity": "<PERSON><PERSON><PERSON> động gần đây", "assetDetailStakeSOL": "Cọc SOL", "assetDetailUnknownToken": "<PERSON><PERSON> chưa x<PERSON>c <PERSON>", "assetDetailUnwrapAll": "Tháo mở toàn bộ", "assetDetailUnwrappingSOL": "Đang mở bọc SOL", "assetDetailUnwrappingSOLFailed": "Mở bọc SOL không thành công", "assetDetailViewOnExplorer": "<PERSON><PERSON> tr<PERSON> {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>m phá", "assetDetailSaveToPhotos": "<PERSON><PERSON><PERSON> vào Ảnh", "assetDetailSaveToPhotosToast": "<PERSON>ã lưu vào Ảnh", "assetDetailPinCollection": "<PERSON><PERSON> s<PERSON>u tập", "assetDetailUnpinCollection": "Bỏ g<PERSON> sưu tập", "assetDetailHideCollection": "Ẩn <PERSON><PERSON> sưu tập", "assetDetailUnhideCollection": "Bỏ <PERSON><PERSON> <PERSON> sưu tập", "assetDetailTokenNameLabel": "Tên token", "assetDetailNetworkLabel": "Mạng l<PERSON>", "assetDetailAddressLabel": "Địa chỉ", "assetDetailPriceLabel": "Giá", "collectibleDetailSetAsAvatar": "Đặt làm <PERSON>nh đạ<PERSON> di<PERSON>n", "collectibleDetailSetAsAvatarSingleWorkAlt": "Ảnh đại diện", "collectibleDetailSetAsAvatarSuccess": "<PERSON>ình đại diện đã được đặt", "collectibleDetailShare": "<PERSON><PERSON> sẻ Vật sưu tầm", "assetDetailTokenAddressCopied": "Địa chỉ đã được sao chép", "assetDetailStakingLabel": "Đặt cọc", "assetDetailAboutLabel": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u về {{fungibleName}}", "assetDetailPriceDetail": "Chi tiết giá", "assetDetailHighlights": "<PERSON><PERSON><PERSON> b<PERSON>t", "assetDetailAllTimeReturn": "<PERSON><PERSON><PERSON> nhuận toàn thời gian", "assetDetailAverageCost": "Chi phí trung bình", "assetDetailPriceHistoryUnavailable": "<PERSON><PERSON><PERSON> sử giá không khả dụng cho token này", "assetDetailPriceHistoryInsufficientData": "<PERSON><PERSON><PERSON> sử gi<PERSON> không khả dụng cho khoảng thời gian này", "assetDetailPriceDataUnavailable": "<PERSON><PERSON> liệu về giá không có sẵn", "assetDetailPriceHistoryError": "Lỗi truy xu<PERSON>t lịch sử giá", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1D", "assetDetailTimeFrame24h": "Giá 24 giờ", "assetDetailTimeFrame1W": "1W", "assetDetailTimeFrame1M": "1M", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "TẤT CẢ", "sendAssetAmountLabelInterpolated": "Có sẵn {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Báo giá", "fiatRampNewQuote": "Báo giá mới", "assetListSelectToken": "Ch<PERSON>n token", "assetListSearch": "<PERSON><PERSON><PERSON> k<PERSON>...", "assetListUnknownToken": "<PERSON><PERSON> chưa x<PERSON>c <PERSON>", "buyFlowHealthWarning": "Một số nhà cung cấp dịch vụ thanh toán của chúng tôi đang có mật độ giao dịch lớn. Vi<PERSON><PERSON> nộp tiền có thể bị trễ vài tiếng.", "assetVisibilityUnknownToken": "<PERSON><PERSON> chưa x<PERSON>c <PERSON>", "buyAssetInterpolated": "Mua {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "<PERSON><PERSON> tiền mua tối đa là {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "<PERSON><PERSON> tiền mua tối thiểu là {{amount}}", "buyNoAssetsAvailable": "K<PERSON><PERSON>ng có tài sản Ethereum hoặc Polygon nào", "buyThirdPartyScreenPaymentMethodSelector": "Thanh toán bằng", "buyThirdPartyScreenPaymentMethod": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON><PERSON> thức thanh toán", "buyThirdPartyScreenChoseQuote": "<PERSON><PERSON><PERSON>n số tiền hợp lệ để báo giá", "buyThirdPartyScreenProviders": "<PERSON><PERSON><PERSON> cung cấp", "buyThirdPartyScreenPaymentMethodTitle": "<PERSON><PERSON><PERSON> p<PERSON><PERSON><PERSON> thức thanh toán", "buyThirdPartyScreenPaymentMethodEmptyState": "<PERSON><PERSON><PERSON><PERSON> có phư<PERSON><PERSON> thức thanh toán nào khả dụng cho khu vực của bạn", "buyThirdPartyScreenPaymentMethodFooter": "<PERSON>h toán được cung cấp bởi các đối tác mạng lưới. <PERSON>í có thể khác nhau. Một số phương thức thanh toán không khả dụng trong khu vực của bạn.", "buyThirdPartyScreenProvidersEmptyState": "<PERSON>h<PERSON>ng có nhà cung cấp nào khả dụng cho khu vực của bạn", "buyThirdPartyScreenLoadingQuote": "<PERSON><PERSON> tải báo giá...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON> b<PERSON>o giá", "gasEstimationErrorWarning": "<PERSON><PERSON> xảy ra lỗi khi ước tính phí cho giao dịch này. Giao dịch có thể sẽ không thành công.", "gasEstimationCouldNotFetch": "<PERSON><PERSON><PERSON><PERSON> thể truy xuất ư<PERSON><PERSON> t<PERSON>h năng lượng", "networkFeeCouldNotFetch": "<PERSON><PERSON><PERSON><PERSON> thể truy xuất phí mạng lưới", "nativeTokenBalanceErrorWarning": "<PERSON><PERSON> xảy ra lỗi khi truy xuất số dư token của bạn cho giao dịch này. Giao dịch có thể sẽ không thành công.", "blocklistOriginCommunityDatabaseInterpolated": "Website này được đánh dấu là một phần của <1>cơ sở dữ liệu được duy trì bởi cộng đồng</1> gồm nhiều website phishing và những màn lừa đảo có tiếng. Nếu bạn tin website này bị đánh dấu sai, <3>hãy gửi báo một sự cố</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} đã bị chặn!", "blocklistOriginIgnoreWarning": "Bỏ qua cảnh b<PERSON><PERSON>, cứ đưa tôi đến {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom tin rằng website này độc hại và không an toàn để dùng.", "blocklistOriginThisDomain": "tên mi<PERSON>n n<PERSON>y", "blocklistProceedAnyway": "Bỏ qua cảnh b<PERSON>o, c<PERSON> tiế<PERSON> tục", "maliciousTransactionWarning": "Phantom tin rằng giao dịch này là độc hại và không an toàn để ký. Chúng tôi đã tắt chức năng ký giao dịch này nhằm bảo vệ bạn và tiền của bạn.", "maliciousTransactionWarningIgnoreWarning": "Bỏ qua cảnh b<PERSON>o, c<PERSON> tiế<PERSON> tục", "maliciousTransactionWarningTitle": "G<PERSON>o dịch đã đư<PERSON><PERSON> đánh dấu!", "maliciousRequestBlockedTitle": "<PERSON><PERSON><PERSON> cầu đã bị chặn", "maliciousRequestWarning": "Website này đã bị đánh dấu là độc hại. N<PERSON> có thể đang cố đánh cắp tiền của bạn hoặc lừa bạn xác nhận một yêu cầu giả.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON> sự an toàn của bạn, <PERSON> đã chặn yêu cầu này.", "maliciousRequestBlocked": "<PERSON><PERSON> sự an toàn của bạn, <PERSON> đã chặn yêu cầu này.", "maliciousRequestFrictionDescription": "<PERSON><PERSON><PERSON><PERSON> tiếp tục là không an toàn, vậy nên Phantom đã chặn yêu cầu này. Bạn nên đóng hộp thoại này.", "maliciousRequestAcknowledge": "Tôi hiểu rằng tôi có thể mất toàn bộ tiền của mình khi truy cập website này.", "maliciousRequestAreYouSure": "Bạn có chắc không?", "siwErrorPopupTitle": "<PERSON><PERSON><PERSON> c<PERSON>u chữ ký không hợp lệ", "siwParseErrorDescription": "<PERSON><PERSON><PERSON> cầu chữ ký của ứng dụng không thể được hiển thị do sai định dạng.", "siwVerificationErrorDescription": "Có 1 hoặc nhiều lỗi với yêu cầu chữ ký của tin nhắn. <PERSON><PERSON> bả<PERSON> m<PERSON>, h<PERSON><PERSON> chắc chắn rằng bạn đang sử dụng đúng ứng dụng và thử lại.", "siwErrorPagination": "{{n}} / {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Cảnh báo: địa chỉ của ứng dụng không khớp với địa chỉ được cung cấp để ký.", "siwErrorMessage_DOMAIN_MISMATCH": "Cảnh báo: tên miền của ứng dụng không khớp với tên miền đư<PERSON>c cung cấp để xác minh.", "siwErrorMessage_URI_MISMATCH": "Cảnh báo: tên máy chủ URI không khớp với tên miền.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Cảnh báo: ID chuỗi không khớp với ID chuỗi được cung cấp để xác minh.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Cảnh báo: ng<PERSON>y phát hành tin nhắn ở quá xa trong quá khứ.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Cảnh báo: ng<PERSON>y phát hành tin nhắn ở quá xa trong tương lai.", "siwErrorMessage_EXPIRED": "Cảnh báo: tin nhắn đã hết hạn.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Cảnh báo: tin nhắn hết hạn trư<PERSON><PERSON> khi phát hành.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Cảnh báo: tin nhắn sẽ hết hạn trước khi nó có hiệu lực.", "siwErrorShowErrorDetails": "<PERSON><PERSON><PERSON> thị chi tiết lỗi", "siwErrorHideErrorDetails": "Ẩn chi tiết lỗi", "siwErrorIgnoreWarning": "Bỏ qua cảnh b<PERSON>o, c<PERSON> tiế<PERSON> tục", "siwsTitle": "<PERSON><PERSON><PERSON> c<PERSON>u đ<PERSON>ng <PERSON>h<PERSON>p", "siwsPermissions": "<PERSON><PERSON><PERSON><PERSON>", "siwsAgreement": "<PERSON>", "siwsAdvancedDetails": "<PERSON> tiết nâng cao", "siwsAlternateStatement": "{{domain}} muốn bạn ký bằng tài khoản <PERSON> của bạn:\n{{address}}", "siwsFieldLable_domain": "<PERSON><PERSON><PERSON>", "siwsFieldLable_address": "Địa chỉ", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "<PERSON><PERSON><PERSON>", "siwsFieldLable_chainId": "ID chuỗi", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "<PERSON><PERSON><PERSON>", "siwsFieldLable_expirationTime": "<PERSON><PERSON><PERSON> h<PERSON> l<PERSON>c", "siwsFieldLable_requestId": "<PERSON> yêu c<PERSON>u", "siwsFieldLable_resources": "<PERSON><PERSON><PERSON>", "siwsVerificationErrorDescription": "<PERSON><PERSON><PERSON> cầu đăng nhập này không hợp lệ. Điều này có nghĩa website này không an toàn, hoặc nhà phát triển đã mắc lỗi khi gửi yêu cầu này.", "siwsErrorNumIssues": "{{n}} vấn đề", "siwsErrorMessage_CHAIN_ID_MISMATCH": "ID chuỗi này không khớp với mạng lưới bạn đang tham gia.", "siwsErrorMessage_DOMAIN_MISMATCH": "Tên miền này không phải tên miền bạn đang đăng nhập vào.", "siwsErrorMessage_URI_MISMATCH": "URI này không phải URI bạn đang đăng nhập vào.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "<PERSON><PERSON><PERSON> phát hành tin nhắn ở quá xa trong quá khứ.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "<PERSON><PERSON><PERSON> phát hành tin nhắn ở quá xa trong tương lai.", "siwsErrorMessage_EXPIRED": "<PERSON> nhắn đã hết hạn.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "<PERSON> nhắn hết hạn trư<PERSON>c khi phát hành.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "<PERSON> nhắn sẽ hết hạn trước khi nó có hiệu lực.", "changeLockTimerPrimaryText": "<PERSON>ộ định giờ khóa tự động", "changeLockTimerSecondaryText": "Ta phải chờ bao lâu để khóa ví sau khi nó ở trạng thái nhàn rỗi?", "changeLockTimerToast": "<PERSON>hờ<PERSON> gian tự động khóa đã đư<PERSON><PERSON> cập nhật", "changePasswordConfirmNewPassword": "<PERSON><PERSON><PERSON> nh<PERSON>n mật khẩu mới", "changePasswordCurrentPassword": "<PERSON><PERSON><PERSON><PERSON> hiện tại", "changePasswordErrorIncorrectCurrentPassword": "<PERSON><PERSON><PERSON> <PERSON><PERSON>u hiện tại sai", "changePasswordErrorGeneric": "<PERSON><PERSON> sự cố, h<PERSON><PERSON> thử lại sau", "changePasswordNewPassword": "<PERSON><PERSON><PERSON> mới", "changePasswordPrimaryText": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "changePasswordToast": "<PERSON><PERSON><PERSON> khẩu đã đư<PERSON><PERSON> cập nhật", "collectionsSpamCollections": "<PERSON><PERSON><PERSON> bộ s<PERSON><PERSON> tập r<PERSON>c", "collectionsHiddenCollections": "<PERSON><PERSON><PERSON> bộ s<PERSON><PERSON> tập bị <PERSON>n", "collectiblesReportAsSpam": "Báo cáo là Rác", "collectiblesReportAsSpamAndHide": "Báo cáo là Rác và Ẩn", "collectiblesReportAsNotSpam": "Báo cáo là Không phải rác", "collectiblesReportAsNotSpamAndUnhide": "Bỏ ẩn và báo cáo không phải rác", "collectiblesReportNotSpam": "Không ph<PERSON>i rác", "collectionsManageCollectibles": "<PERSON><PERSON><PERSON><PERSON> lý danh sách vật sưu tầm", "collectibleDetailDescription": "<PERSON><PERSON><PERSON>", "collectibleDetailProperties": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailOrdinalInfo": "Thông tin Ordinal", "collectibleDetailRareSatsInfo": "Thông tin về Rare Sat", "collectibleDetailSatsInUtxo": "Sat trong UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sat", "collectibleDetailSatNumber": "Số Sat", "collectibleDetailSatName": "<PERSON><PERSON><PERSON>", "collectibleDetailInscriptionId": "Thông tin khắc ghi", "collectibleDetailInscriptionNumber": "Số khắc ghi", "collectibleDetailStandard": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailCreated": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "collectibleDetailViewOnExplorer": "<PERSON><PERSON> tr<PERSON> {{explorer}}", "collectibleDetailList": "<PERSON><PERSON>", "collectibleDetailSellNow": "<PERSON><PERSON> với giá {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Giải phóng Bitcoin thừa", "collectibleDetailUtxoSplitterCtaSubtitle": "Bạn có {{value}} tính theo BTC cần mở khóa", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sat", "collectibleDetailUtxoSplitterModalCtaSubtitle": "<PERSON><PERSON> bảo vệ vốn của bạn, chúng tôi đã ngăn BTC trong các UTXO với Rare Sat được gửi đi. H<PERSON>y dùng bộ chia UTXO của Magic Eden để giải phóng {{value}} tính theo BTC từ các Rare Sat của bạn.", "collectibleDetailUtxoSplitterModalCtaButton": "Sử dụng bộ chia UTXO", "collectibleDetailEasilyAccept": "<PERSON><PERSON><PERSON> nhận đề nghị cao nhất", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sat", "collectibleDetailSpamOverlayDescription": "<PERSON><PERSON>t sưu tầm này đã bị ẩn bởi Phantom tin rằng nó là vật phẩm rác.", "collectibleDetailSpamOverlayReveal": "<PERSON><PERSON><PERSON> thị <PERSON> s<PERSON>u tầm", "collectibleBurnTermsOfService": "<PERSON><PERSON><PERSON> hiểu vi<PERSON><PERSON> này không thể được hoàn tác", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON><PERSON> ch<PERSON> token", "collectibleBurnTitleWithCount_other": "<PERSON><PERSON><PERSON> ch<PERSON> token", "collectibleBurnDescriptionWithCount_one": "Hành động này sẽ phá hủy vĩnh viễn và xóa token này khỏi ví của bạn.", "collectibleBurnDescriptionWithCount_other": "Hành động này sẽ phá hủy vĩnh viễn và xóa những token này khỏi ví của bạn.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Token", "collectibleBurnCta": "<PERSON><PERSON><PERSON>", "collectibleBurnRebate": "<PERSON><PERSON><PERSON>", "collectibleBurnRebateTooltip": "Một lượng nhỏ SOL sẽ tự động được gửi vào ví của bạn để đốt cháy token này.", "collectibleBurnNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "collectibleBurnNetworkFeeTooltip": "<PERSON><PERSON><PERSON><PERSON> tiền b<PERSON><PERSON> buộc bởi mạng lưới Solana để xử lý giao dịch", "unwrapButtonSwapTo": "<PERSON><PERSON> đổi thành {{chainSymbol}}", "unwrapButtonWithdrawFrom": "<PERSON><PERSON><PERSON> từ {{withdrawalSource}} cho {{chainSymbol}}", "unwrapModalEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "unwrapModalNetwork": "Mạng l<PERSON>", "unwrapModalNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "unwrapModalTitle": "<PERSON><PERSON><PERSON>", "unsupportedChain": "Chuỗi được hỗ trợ", "unsupportedChainDescription": "<PERSON><PERSON> vẻ chúng tôi không hỗ trợ {{action}} cho mạng lưới {{chainName}}.", "networkFeesTooltipLabel": "<PERSON><PERSON> mạng lưới {{chainName}}", "networkFeesTooltipDescription": "Phí {{chainName}} phụ thuộc vào một vài yếu tố. Bạn có thể tùy chỉnh chúng để thực hiện các giao dị<PERSON> hơn (đ<PERSON><PERSON> hơn) hoặc chậm hơn (rẻ hơn).", "burnStatusErrorTitleWithCount_one": "To<PERSON> không thể được đốt cháy", "burnStatusErrorTitleWithCount_other": "To<PERSON> không thể được đốt cháy", "burnStatusSuccessTitleWithCount_one": "Token đã đư<PERSON><PERSON> đốt chá<PERSON>!", "burnStatusSuccessTitleWithCount_other": "Token đã đư<PERSON><PERSON> đốt chá<PERSON>!", "burnStatusLoadingTitleWithCount_one": "<PERSON><PERSON> đốt cháy token...", "burnStatusLoadingTitleWithCount_other": "<PERSON><PERSON> đốt cháy token...", "burnStatusErrorMessageWithCount_one": "Token này không thể được đốt cháy. <PERSON><PERSON> lòng thử lại sau.", "burnStatusErrorMessageWithCount_other": "Những token này không thể được đốt cháy. <PERSON><PERSON> lòng thử lại sau.", "burnStatusSuccessMessageWithCount_one": "Token này đã bị phá hủy vĩnh viễn và {{rebateAmount}} SOL đã được gửi vào ví của bạn.", "burnStatusSuccessMessageWithCount_other": "Những token này đã bị phá hủy vĩnh viễn và {{rebateAmount}} SOL đã được gửi vào ví của bạn.", "burnStatusLoadingMessageWithCount_one": "Token này đang bị phá hủy vĩnh viễn và {{rebateAmount}} SOL sẽ được gửi vào ví của bạn.", "burnStatusLoadingMessageWithCount_other": "Những token này đang bị phá hủy vĩnh viễn và {{rebateAmount}} SOL sẽ được gửi vào ví của bạn.", "burnStatusViewTransactionText": "<PERSON>em giao d<PERSON>ch", "collectibleDisplayLoading": "<PERSON><PERSON> tả<PERSON>...", "collectiblesNoCollectibles": "<PERSON><PERSON><PERSON><PERSON> có vật s<PERSON>u tầm", "collectiblesPrimaryText": "<PERSON><PERSON><PERSON> s<PERSON>u tầm của bạn", "collectiblesReceiveCollectible": "<PERSON><PERSON><PERSON><PERSON> vật s<PERSON><PERSON> tầm", "collectiblesUnknownCollection": "<PERSON><PERSON> sưu tập không x<PERSON>c đ<PERSON>nh", "collectiblesUnknownCollectible": "<PERSON><PERSON><PERSON> s<PERSON>u tầm không x<PERSON>c đ<PERSON>nh", "collectiblesUniqueHolders": "<PERSON><PERSON><PERSON><PERSON> nắm gi<PERSON> kh<PERSON>c nhau", "collectiblesSupply": "<PERSON><PERSON><PERSON><PERSON> cung", "collectiblesUnknownTokens": "<PERSON><PERSON> không x<PERSON>c đ<PERSON>", "collectiblesNrOfListed": "{{ nrOfListed }} đ<PERSON> niêm yết", "collectiblesListed": "<PERSON><PERSON> niêm y<PERSON>t", "collectiblesMintCollectible": "<PERSON><PERSON><PERSON> s<PERSON> tầm", "collectiblesYouMint": "<PERSON>ạ<PERSON>", "collectiblesMintCost": "<PERSON> phí đúc", "collectiblesMintFail": "<PERSON><PERSON><PERSON> không thành công", "collectiblesMintFailMessage": "<PERSON><PERSON> xảy ra lỗi khi đúc vật sưu tầm của bạn. <PERSON><PERSON> lòng thử lại.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON> phí", "collectiblesMinting": "<PERSON><PERSON> đúc...", "collectiblesMintingMessage": "<PERSON><PERSON><PERSON> sưu tầm của bạn đang đư<PERSON><PERSON> đúc", "collectiblesMintShareSubject": "<PERSON><PERSON><PERSON> xem cái này", "collectiblesMintShareMessage": "<PERSON><PERSON><PERSON> đ<PERSON>c đ<PERSON> cái này trên @phantom!", "collectiblesMintSuccess": "<PERSON><PERSON><PERSON> thành công", "collectiblesMintSuccessMessage": "<PERSON><PERSON><PERSON> sưu tầm của bạn đang đư<PERSON><PERSON> đúc", "collectiblesMintSuccessQuestMessage": "Bạn đã đáp ứng các yêu cầu cho một Nhiệm vụ Phantom. Hãy chạm vào <PERSON>hận phần thưởng của bạn để nhận vật sưu tầm miễn phí của bạn.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON> c<PERSON>", "collectiblesMintMaxLengthErrorMessage": "<PERSON><PERSON> vượt quá độ dài tối đa", "collectiblesMintSafelyDismiss": "<PERSON><PERSON>n có thể đóng cửa sổ này một cách an toàn.", "collectiblesTrimmed": "<PERSON>úng ta đã đạt giới hạn về số vật sưu tầm có thể được hiển thị tại thời điểm này.", "collectiblesNonTransferable": "<PERSON><PERSON><PERSON><PERSON> chuyển đ<PERSON>", "collectiblesNonTransferableYes": "<PERSON><PERSON>", "collectiblesSellOfferDetails": "<PERSON> tiết đề nghị", "collectiblesSellYouSell": "<PERSON><PERSON><PERSON> b<PERSON>", "collectiblesSellGotIt": "<PERSON><PERSON> hiểu", "collectiblesSellYouReceive": "Bạn <PERSON>n", "collectiblesSellOffer": "<PERSON><PERSON> nghị", "collectiblesSoldCollectible": "<PERSON><PERSON><PERSON> s<PERSON>u tầm đã bán", "collectiblesSellMarketplace": "<PERSON>ợ điện tử", "collectiblesSellCollectionFloor": "<PERSON><PERSON><PERSON> sàn cho bộ sưu tập", "collectiblesSellDifferenceFromFloor": "<PERSON><PERSON><PERSON> l<PERSON>ch với gi<PERSON> sàn", "collectiblesSellLastSalePrice": "<PERSON><PERSON><PERSON> b<PERSON> cuối cùng", "collectiblesSellEstimatedFees": "<PERSON><PERSON> <PERSON>", "collectiblesSellEstimatedProfitAndLoss": "Lời/lỗ ước tính", "collectiblesSellViewOnMarketplace": "<PERSON><PERSON> trên {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> ng<PERSON>' thấp nhất trong bộ sưu tập trên nhiều chợ điện tử.", "collectiblesSellProfitLossTooltip": "<PERSON><PERSON><PERSON>/Lỗ ước tính được tính dựa trên giá bán gần nhất và số tiền đề nghị trừ đi phí.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "<PERSON><PERSON> bản quyền ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "<PERSON><PERSON> chợ điện tử ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "<PERSON><PERSON>", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "<PERSON><PERSON>ng l<PERSON> {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "<PERSON><PERSON><PERSON> gi<PERSON> bao gồm {{phantomFeePercentage}} phí Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "<PERSON><PERSON><PERSON> gi<PERSON> bao gồm <PERSON>uy<PERSON>, <PERSON><PERSON> mạ<PERSON>, <PERSON><PERSON> chợ điện tử, và {{phantomFeePercentage}} phí Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "<PERSON><PERSON><PERSON> gi<PERSON> bao gồm <PERSON> b<PERSON> quyề<PERSON>, <PERSON><PERSON> mạ<PERSON>, và một <PERSON><PERSON> chợ điện tử", "collectiblesSellTransactionFeeTooltipTitle": "<PERSON><PERSON> giao d<PERSON>ch", "collectiblesSellStatusLoadingTitle": "<PERSON><PERSON> chấp nhận đề nghị...", "collectiblesSellStatusLoadingIsSellingFor": "đang bán với giá", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} đã đ<PERSON><PERSON> bán!", "collectiblesSellStatusSuccessWasSold": "đ<PERSON> đ<PERSON><PERSON><PERSON> bán thành công với giá", "collectiblesSellStatusErrorTitle": "Đ<PERSON> xảy ra lỗi", "collectiblesSellStatusErrorSubtitle": "<PERSON><PERSON> xảy ra lỗi khi cố gắng bán", "collectiblesSellStatusViewTransaction": "<PERSON>em giao d<PERSON>ch", "collectiblesSellInsufficientFundsTitle": "Số tiền không đủ", "collectiblesSellInsufficientFundsSubtitle": "<PERSON>úng tôi đã không thể chấp nhận một đề nghị cho vật sưu tầm này bởi không có đủ tiền để trả phí mạng lưới.", "collectiblesSellRecentlyTransferedNFTTitle": "<PERSON><PERSON> chuyển gần đây", "collectiblesSellRecentlyTransferedNFTSubtitle": "Bạn phải chờ 1 giờ để chấp nhận các đấu giá sau một giao dịch chuyển.", "collectiblesApproveCollection": "{{collectionName}} đ<PERSON> đ<PERSON> phê du<PERSON>t", "collectiblesSellNotAvailableAnymoreTitle": "Ưu đãi không khả dụng", "collectiblesSellNotAvailableAnymoreSubtitle": "Ưu đãi này không còn khả dụng nữa. Hãy hủy đấu giá này và thử lại", "collectiblesSellFlaggedTokenTitle": "<PERSON><PERSON><PERSON> sưu tầm đ<PERSON><PERSON><PERSON> đ<PERSON>h dấu", "collectiblesSellFlaggedTokenSubtitle": "V<PERSON>t sưu tầm không thể được trao đổi, điề<PERSON> này có thể vì nhiều lý do, chẳng hạn như bị báo cáo là bị mất hoặc được đặt cọc mà không khóa", "collectiblesListOnMagicEden": "<PERSON><PERSON><PERSON> y<PERSON>t trên Magic Eden", "collectiblesListPrice": "<PERSON><PERSON><PERSON>", "collectiblesUseFloor": "Sử dụng sàn", "collectiblesFloorPrice": "<PERSON><PERSON><PERSON>", "collectiblesLastSalePrice": "<PERSON><PERSON><PERSON>n mãi cuối cùng", "collectiblesTotalReturn": "<PERSON><PERSON><PERSON> lợi n<PERSON>n", "collectiblesOriginalPurchasePrice": "<PERSON><PERSON><PERSON> mua ban đầu", "collectiblesMagicEdenFee": "Phí Magic Eden", "collectiblesArtistRoyalties": "<PERSON><PERSON> bản quyền cho nghệ sĩ", "collectiblesListNowButton": "<PERSON><PERSON><PERSON> ngay", "collectiblesListAnywayButton": "Vẫn niêm yết", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "<PERSON><PERSON>", "collectiblesListingViewTransaction": "<PERSON>em giao d<PERSON>ch", "collectiblesRemoveListing": "<PERSON><PERSON><PERSON>", "collectiblesEditListing": "<PERSON><PERSON><PERSON>", "collectiblesEditListPrice": "<PERSON><PERSON><PERSON> gi<PERSON> ni<PERSON>m y<PERSON>t", "collectiblesListPriceTooltip": "<PERSON><PERSON><PERSON> niêm yết là giá bán cho một mục. <PERSON><PERSON><PERSON><PERSON> bán thường đặt Giá niêm yết bằng hoặc cao hơn Gi<PERSON> sàn.", "collectiblesFloorPriceTooltip": "<PERSON><PERSON><PERSON> sàn là Giá niêm yết thấp nhất có hiệu lực cho một mục trong bộ sưu tập này.", "collectiblesOriginalPurchasePriceTooltip": "<PERSON><PERSON><PERSON> ban đầu mà bạn đã chi để mua mục này.", "collectiblesPurchasedForSol": "Đ<PERSON> mua với giá {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách niêm yết", "collectiblesUnableToLoadListingsFrom": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách niêm yết từ {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "<PERSON><PERSON> sách niêm yết và các tái ản của bạn vẫn an toàn, nhưng hiện tại chúng tôi không thể tải chúng từ {{marketplace}}. <PERSON><PERSON> lòng thử lại sau.", "collectiblesBelowFloorPrice": "<PERSON><PERSON><PERSON><PERSON> h<PERSON>n gi<PERSON> sàn", "collectiblesBelowFloorPriceMessage": "Bạn có chắc bạn muốn niêm yết NFT của mình thấp hơn giá sàn?", "collectiblesMinimumListingPrice": "<PERSON><PERSON><PERSON> tối thiểu là 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden nhận một mức phí cho các giao dịch đ<PERSON><PERSON><PERSON> hoàn thành.", "collectiblesArtistRoyaltiesTooltip": "<PERSON><PERSON><PERSON> gi<PERSON> của bộ sưu tập này nhận một mức % phí bản quyền từ mỗi giao dịch bán đư<PERSON>c hoàn thành.", "collectibleScreenCollectionLabel": "<PERSON><PERSON> s<PERSON>u tập", "collectibleScreenPhotosPermissionTitle": "<PERSON><PERSON><PERSON><PERSON> t<PERSON><PERSON> cậ<PERSON>", "collectibleScreenPhotosPermissionMessage": "<PERSON><PERSON><PERSON> tôi cần bạn cấp quyền để truy cập các ảnh của bạn. Vui lòng vào Cài đặt và cập nhật các quyền của bạn.", "collectibleScreenPhotosPermissionOpenSettings": "Mở Cài đặt", "listStatusErrorTitle": "<PERSON><PERSON><PERSON> yết đã thất bại", "editListStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật", "removeListStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể x<PERSON>a <PERSON> y<PERSON>", "listStatusSuccessTitle": "<PERSON><PERSON><PERSON> yết đã đư<PERSON>c tạo!", "editListingStatusSuccessTitle": "<PERSON><PERSON><PERSON> yết đã đư<PERSON><PERSON> cập nhật!", "removeListStatusSuccessTitle": "<PERSON><PERSON><PERSON> y<PERSON>t bị xóa khỏi Magic Eden", "listStatusLoadingTitle": "<PERSON><PERSON> t<PERSON> y<PERSON>...", "editListingStatusLoadingTitle": "<PERSON><PERSON> cập nh<PERSON>t <PERSON> yết...", "removeListStatusLoadingTitle": "<PERSON><PERSON> x<PERSON> y<PERSON>...", "listStatusErrorMessage": "{{name}} không thể được niêm yết trên Magic Eden", "removeListStatusErrorMessage": "{{name}} khô<PERSON> thể bị bỏ niêm yết trên Magic Eden", "listStatusSuccessMessage": "{{name}} hi<PERSON><PERSON> đ<PERSON><PERSON><PERSON> niêm yết trên Magic Eden với giá {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "{{name}} hi<PERSON><PERSON> đ<PERSON> cập nhật trên <PERSON> với giá {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} đã được xóa thành công khỏi Magic Eden", "listStatusLoadingMessage": "<PERSON><PERSON><PERSON> {{name}} trên Magic Eden cho {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "<PERSON><PERSON><PERSON> nh<PERSON> {{name}} trên Magic Eden cho {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "<PERSON><PERSON> xóa {{name}} từ Magic Eden. Vi<PERSON><PERSON> này có thể cần một chút thời gian.", "listStatusLoadingSafelyDismiss": "<PERSON><PERSON>n có thể đóng cửa sổ này một cách an toàn.", "listStatusViewOnMagicEden": "<PERSON><PERSON> trên <PERSON>", "listStatusViewOnMarketplace": "<PERSON><PERSON> trên {{marketplace}}", "listStatusLoadingDismiss": "Đ<PERSON><PERSON>", "listStatusViewTransaction": "<PERSON>em giao d<PERSON>ch", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Kết nối ví cứng của bạn và đảm bảo nó được mở khóa. <PERSON>u khi chúng tôi đã phát hiện đượ<PERSON> nó, bạn có thể chọn địa chỉ mà mình muốn sử dụng.", "connectHardwareFailedPrimaryText": "<PERSON><PERSON><PERSON> n<PERSON>i thất bại", "connectHardwareFailedSecondaryText": "<PERSON><PERSON> lòng kết nối ví cứng của bạn và đảm bảo nó được mở khóa. Sau khi chúng tôi đã khám phá được nó, bạn có thể chọn địa chỉ mà mình muốn sử dụng.", "connectHardwareFinishPrimaryText": "Tà<PERSON> khoản đã đư<PERSON>c thêm!", "connectHardwareFinishSecondaryText": "<PERSON><PERSON><PERSON> đây bạn có thể truy cập ví Ledger <PERSON> của mình từ trong Phantom. Vui lòng quay lại phần mở rộng.", "connectHardwareNeedsPermissionPrimaryText": "<PERSON><PERSON><PERSON> n<PERSON>i một ví mới", "connectHardwareNeedsPermissionSecondaryText": "<PERSON><PERSON><PERSON>n nút dưới đây để bắt đầu quy trình kết nối.", "connectHardwareSearchingPrimaryText": "<PERSON><PERSON> tìm ví...", "connectHardwareSearchingSecondaryText": "<PERSON>ết nối ví cứng của bạn, bả<PERSON> đảm ví được mở khóa, và rằng bạn đã phê duyệt các quyền truy cập trong trình duyệt của mình.", "connectHardwarePermissionDeniedPrimary": "<PERSON><PERSON><PERSON>n đã bị từ chối", "connectHardwarePermissionDeniedSecondary": "<PERSON><PERSON><PERSON> quyền để Phantom có thể kết nối với thiết bị Ledger của bạn", "connectHardwarePermissionUnableToConnect": "<PERSON><PERSON><PERSON><PERSON> thể kết nối", "connectHardwarePermissionUnableToConnectDescription": "<PERSON>úng tôi không thể kết nối với thiết bị <PERSON>ger của bạn. Chúng tôi có thể cần thêm quyền.", "connectHardwareSelectAddressAllAddressesImported": "<PERSON><PERSON><PERSON> địa chỉ đều được nhập", "connectHardwareSelectAddressDerivationPath": "Đường dẫn phái sinh", "connectHardwareSelectAddressSearching": "<PERSON><PERSON> tìm kiếm...", "connectHardwareSelectAddressSelectWalletAddress": "<PERSON><PERSON><PERSON> địa chỉ ví", "connectHardwareSelectAddressWalletAddress": "Địa chỉ ví", "connectHardwareWaitingForApplicationSecondaryText": "<PERSON>ui lòng kết nối ví cứng của bạn và đảm bảo ví được mở khóa.", "connectHardwareWaitingForPermissionPrimaryText": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> quyền", "connectHardwareWaitingForPermissionSecondaryText": "<PERSON>ết nối ví cứng của bạn, bả<PERSON> đảm ví được mở khóa, và rằng bạn đã phê duyệt các quyền truy cập trong trình duyệt của mình.", "connectHardwareAddAccountButton": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>", "connectHardwareLedger": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> của bạn", "connectHardwareStartConnection": "<PERSON><PERSON><PERSON>n nút dưới đây để bắt đầu kết nối ví cứng <PERSON>ger của bạn", "connectHardwarePairSuccessPrimary": "{{productName}} <PERSON><PERSON><PERSON><PERSON> k<PERSON>i", "connectHardwarePairSuccessSecondary": "Bạn đã kết nối thành công {{productName}} của mình.", "connectHardwareSelectChains": "<PERSON><PERSON><PERSON> các chuỗi để kết nối", "connectHardwareSearching": "<PERSON><PERSON> tìm kiếm...", "connectHardwareMakeSureConnected": "Kết nối và mở khóa ví cứng của bạn. Vui lòng phê duyệt các quyền liên quan của trình duyệt.", "connectHardwareOpenAppDescription": "<PERSON><PERSON> lòng mở khóa ví cứng của bạn", "connectHardwareConnecting": "<PERSON><PERSON> kết nối...", "connectHardwareConnectingDescription": "<PERSON><PERSON>g tôi đang kết nối với thiết bị <PERSON> của bạn.", "connectHardwareConnectingAccounts": "<PERSON><PERSON> kết n<PERSON>i các tài kho<PERSON>n của bạn...", "connectHardwareDiscoveringAccounts": "<PERSON><PERSON> tìm tà<PERSON>...", "connectHardwareDiscoveringAccountsDescription": "<PERSON><PERSON>g tôi đang tìm kiếm các hoạt động trong tài khoản của bạn.", "connectHardwareErrorLedgerLocked": "Ledger đ<PERSON> bị kh<PERSON>a", "connectHardwareErrorLedgerLockedDescription": "<PERSON><PERSON><PERSON> đ<PERSON> b<PERSON><PERSON> thiết bị <PERSON>ger của bạn được mở khóa, sau đó thử lại.", "connectHardwareErrorLedgerGeneric": "Đã có lỗi xảy ra", "connectHardwareErrorLedgerGenericDescription": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài kho<PERSON>n. <PERSON><PERSON><PERSON> đảm bảo thiết bị Ledger của bạn được mở khóa, và sau đó thử lại.", "connectHardwareErrorLedgerPhantomLocked": "<PERSON><PERSON> lòng mở lại Phantom và thử kết nối lại với phần cứng của bạn.", "connectHardwareFindingAccountsWithActivity": "<PERSON><PERSON> tìm tài <PERSON> {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "<PERSON><PERSON> tìm các tà<PERSON> {{chainName1}} hoặc {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "<PERSON>úng tôi đã tìm thấy {{numOfAccounts}} tài kho<PERSON>n có hoạt động trên Ledger của bạn.", "connectHardwareFoundAccountsWithActivitySingular": "<PERSON><PERSON>g tôi đã tìm thấy 1 tài khoản có hoạt động trên Ledger của bạn.", "connectHardwareFoundSomeAccounts": "<PERSON><PERSON>g tôi đã tìm thấy một số tài khoản trên thiết bị <PERSON> của bạn.", "connectHardwareViewAccounts": "<PERSON><PERSON> t<PERSON>", "connectHardwareConnectAccounts": "<PERSON><PERSON><PERSON> khoản đã đ<PERSON><PERSON><PERSON> kết nối", "connectHardwareSelectAccounts": "<PERSON><PERSON><PERSON> tà<PERSON>", "connectHardwareChooseAccountsToConnect": "<PERSON><PERSON><PERSON> tài khoản ví để kết nối.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} tài kho<PERSON>n đã đư<PERSON><PERSON> thêm", "connectHardwareAccountsStepOfSteps": "Bước {{stepNum}} / {{totalSteps}}", "connectHardwareMobile": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileTitle": "<PERSON><PERSON><PERSON> n<PERSON>i ví phần cứng <PERSON> của bạn", "connectHardwareMobileEnableBluetooth": "<PERSON><PERSON><PERSON>", "connectHardwareMobileEnableBluetoothDescription": "<PERSON> phép sử dụng Bluetooth để kết nối", "connectHardwareMobileEnableBluetoothSettings": "<PERSON><PERSON><PERSON> đặt để cho phép Phantom sử dụng các quyền Vị trí và Thiết bị gần đây.", "connectHardwareMobilePairWithDevice": "<PERSON><PERSON><PERSON><PERSON> đôi với thiết bị <PERSON> của bạn", "connectHardwareMobilePairWithDeviceDescription": "<PERSON><PERSON><PERSON> thiết bị của bạn ở gần để có tín hiệu tốt nhất", "connectHardwareMobileConnectAccounts": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>c tài <PERSON>n", "connectHardwareMobileConnectAccountsDescription": "<PERSON><PERSON>g tôi sẽ tìm hoạt động trong mọi tài khoản mà bạn có thể đã sử dụng", "connectHardwareMobileConnectLedgerDevice": "<PERSON><PERSON><PERSON> n<PERSON>i thi<PERSON>t bị <PERSON> của bạn", "connectHardwareMobileLookingForDevices": "<PERSON><PERSON> tìm các thiết bị gần đây...", "connectHardwareMobileLookingForDevicesDescription": "<PERSON><PERSON> lòng kết nối thiết bị Ledger của bạn và đảm bảo ví được mở khóa.", "connectHardwareMobileFoundDeviceSingular": "<PERSON><PERSON>g tôi đã tìm thấy 1 thiế<PERSON> b<PERSON>", "connectHardwareMobileFoundDevices": "<PERSON><PERSON>g tôi đã tìm thấy {{numDevicesFound}} thi<PERSON><PERSON> b<PERSON>", "connectHardwareMobileFoundDevicesDescription": "<PERSON><PERSON><PERSON> một thiết bị Ledger ở dưới để bắt đầu gh<PERSON>p đôi.", "connectHardwareMobilePairingWith": "<PERSON><PERSON><PERSON><PERSON> đôi với {{deviceName}}", "connectHardwareMobilePairingWithDescription": "<PERSON><PERSON><PERSON> theo hư<PERSON> dẫn trên thiết bị <PERSON> của bạn trong khi ghép đôi.", "connectHardwareMobilePairingFailed": "<PERSON><PERSON><PERSON><PERSON> đôi không thành công", "connectHardwareMobilePairingFailedDescription": "<PERSON><PERSON><PERSON><PERSON> thể ghép đôi với {{deviceName}}. <PERSON><PERSON><PERSON> đảm bảo thiết bị của bạn được mở khóa.", "connectHardwareMobilePairingSuccessful": "<PERSON><PERSON><PERSON><PERSON> đôi thành công", "connectHardwareMobilePairingSuccessfulDescription": "Bạn đã ghép đôi thành công và kết nối thiết bị Ledger của mình.", "connectHardwareMobileOpenAppSingleChain": "Mở ứng dụng {{chainName}} trên <PERSON> c<PERSON>a bạn", "connectHardwareMobileOpenAppDualChain": "Mở ứng dụng {{chainName1}} hoặc {{chainName2}} trên <PERSON> của bạn", "connectHardwareMobileOpenAppDescription": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> thiết bị của bạn được mở khóa.", "connectHardwareMobileStillCantFindDevice": "Vẫn không thể tìm thấy thiết bị của bạn?", "connectHardwareMobileLostConnection": "<PERSON><PERSON> mất kết n<PERSON>i", "connectHardwareMobileLostConnectionDescription": "<PERSON><PERSON>g tôi đã mất kết nối với {{deviceName}}. <PERSON><PERSON><PERSON> đảm bảo thiết bị của bạn được mở khóa, sau đó thử lại.", "connectHardwareMobileGenericLedgerDevice": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>", "connectHardwareMobileConnectDeviceSigning": "<PERSON><PERSON><PERSON> {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Mở khóa thiết bị Ledger của bạn và giữ nó ở gần.", "connectHardwareMobileBluetoothDisabled": "Bluetooth đã bị vô hiệu", "connectHardwareMobileBluetoothDisabledDescription": "<PERSON><PERSON> lòng bật Bluetooth của bạn và đảm bảo thiết bị Ledger của bạn được mở khóa.", "connectHardwareMobileLearnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "connectHardwareMobileBlindSigningDisabled": "<PERSON><PERSON> khống đã bị vô hiệu", "connectHardwareMobileBlindSigningDisabledDescription": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> ký khống đ<PERSON><PERSON><PERSON> bật trên thiết bị của bạn.", "connectHardwareMobileConfirmSingleChain": "Bạn cần xác nhận giao dịch trên ví cứng của mình. <PERSON><PERSON><PERSON> bảo đảm là nó được mở khóa.", "metamaskExplainerBottomSheetHeader": "Website này làm việc với Phantom", "metamaskExplainerBottomSheetSubheader": "<PERSON><PERSON><PERSON> từ hộp thoại kết nối ví để tiếp tục.", "metamaskExplainerBottomSheetDontShowAgain": "<PERSON><PERSON><PERSON><PERSON> hiển thị lại", "ledgerStatusNotConnected": "Ledger kh<PERSON>ng đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "ledgerStatusConnectedInterpolated": "{{productName}} <PERSON><PERSON><PERSON><PERSON> k<PERSON>i", "connectionClusterInterpolated": "Bạn hiện ở {{cluster}}", "connectionClusterTestnetMode": "Bạn đang ở trong Chế độ Testnet", "featureNotSupportedOnLocalNet": "<PERSON><PERSON><PERSON> năng này không được hỗ trợ khi Solana Localnet được bật.", "readOnlyAccountBannerWarning": "Bạn đang theo dõi tài kho<PERSON>n này", "depositAddress": "Địa chỉ nhận", "depositAddressChainInterpolated": "Địa chỉ {{chain}} củ<PERSON> bạn", "depositAssetDepositInterpolated": "Nhận {{tokenSymbol}}", "depositAssetSecondaryText": "Đ<PERSON>a chỉ này chỉ có thể được dùng để nhận các token tương thích.", "depositAssetTextInterpolated": "Sử dụng địa chỉ này để nhận các token và vật sưu tầm trên <1>{{network}}</1>.", "depositAssetTransferFromExchange": "<PERSON><PERSON><PERSON><PERSON> từ sàn giao dịch", "depositAssetShareAddress": "Chia sẻ địa chỉ", "depositAssetBuyOrDeposit": "<PERSON><PERSON> hoặc <PERSON>ể<PERSON> k<PERSON>n", "depositAssetBuyOrDepositDesc": "<PERSON><PERSON><PERSON> tiền vào ví của bạn để bắt đầu", "depositAssetTransfer": "<PERSON><PERSON><PERSON><PERSON>", "editAddressAddressAlreadyAdded": "Địa chỉ đã được thêm vào", "editAddressAddressAlreadyExists": "Địa chỉ đã tồn tại", "editAddressAddressIsRequired": "<PERSON>ần có địa chỉ", "editAddressPrimaryText": "<PERSON><PERSON><PERSON> địa chỉ", "editAddressRemove": "Xoá bỏ khỏi Sổ địa chỉ", "editAddressToast": "Địa chỉ đã đư<PERSON><PERSON> cập nhật", "removeSavedAddressToast": "Địa chỉ đã bị xóa", "exportSecretErrorGeneric": "<PERSON><PERSON> sự cố, h<PERSON><PERSON> thử lại sau", "exportSecretErrorIncorrectPassword": "<PERSON><PERSON><PERSON> sai", "exportSecretPassword": "<PERSON><PERSON><PERSON>", "exportSecretPrivateKey": "c<PERSON><PERSON> b<PERSON>n", "exportSecretSecretPhrase": "c<PERSON><PERSON> từ bí mật", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "cụm từ khôi phục bí mật", "exportSecretSelectYourAccount": "<PERSON><PERSON><PERSON> tài k<PERSON>n của bạn", "exportSecretShowPrivateKey": "<PERSON><PERSON><PERSON> thị <PERSON> riêng tư", "exportSecretShowSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> thị cụm từ khôi phục bí mật", "exportSecretShowSecret": "<PERSON><PERSON><PERSON> thị {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "<1><PERSON><PERSON><PERSON></1> chia sẻ {{secretNameText}} của bạn!", "exportSecretWarningSecondaryInterpolated": "<PERSON>ếu ai đó có được {{secretNameText}} củ<PERSON> bạn, họ sẽ toàn quyền kiểm soát ví của bạn.", "exportSecretOnlyWay": "{{secretNameText}} là cách duy nhất để khôi phục ví của bạn", "exportSecretDoNotShow": "<PERSON><PERSON>ng để ai thấy {{secretNameText}} c<PERSON><PERSON> bạn", "exportSecretWillNotShare": "<PERSON><PERSON><PERSON> sẽ không chia sẻ {{secretNameText}} của mình với bất cứ ai, bao g<PERSON><PERSON>.", "exportSecretNeverShare": "<PERSON><PERSON><PERSON> bao giờ chia sẻ {{secretNameText}} của bạn với bất kỳ ai", "exportSecretYourPrivateKey": "<PERSON><PERSON><PERSON><PERSON> riêng tư của bạn", "exportSecretYourSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> từ khôi phục bí mật của bạn", "exportSecretResetPin": "Đặt lại PIN của bạn", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "<PERSON><PERSON><PERSON> g<PERSON>", "gasUpTo": "<PERSON><PERSON><PERSON> đ<PERSON>n {{ amount }}", "timeDescription1hour": "Khoảng 1 tiếng", "timeDescription30minutes": "Khoảng 30 phút", "timeDescription10minutes": "Khoảng 10 phút", "timeDescription2minutes": "Khoảng 2 phút", "timeDescription30seconds": "Khoảng 30 giây", "timeDescription15seconds": "Khoảng 15 giây", "timeDescription10seconds": "Khoảng 10 giây", "timeDescription5seconds": "Khoảng 5 giây", "timeDescriptionAbbrev1hour": "1 tiếng", "timeDescriptionAbbrev30minutes": "30 phút", "timeDescriptionAbbrev10minutes": "10 phút", "timeDescriptionAbbrev2minutes": "2 phút", "timeDescriptionAbbrev30seconds": "30s", "timeDescriptionAbbrev15seconds": "15s", "timeDescriptionAbbrev10seconds": "10s", "timeDescriptionAbbrev5seconds": "5s", "gasSlow": "<PERSON><PERSON><PERSON>", "gasAverage": "<PERSON>rung bình", "gasFast": "<PERSON><PERSON><PERSON>", "satsPerVirtualByte": "{{satsPerVirtualByte}} sat/vB", "satsAmount": "{{sats}} sat", "homeErrorButtonText": "<PERSON><PERSON><PERSON> lại", "homeErrorDescription": "<PERSON>ã có lỗi xảy ra khi cố gắng truy xuất các tài sản của bạn. Vui lòng làm mới và thử lại", "homeErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể truy xuất tài sản", "homeManageTokenList": "<PERSON><PERSON><PERSON><PERSON> lí danh sách <PERSON>", "interstitialDismissUnderstood": "<PERSON><PERSON> hiểu", "interstitialBaseWelcomeTitle": "Phantom hiện đã hỗ trợ Base!", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON><PERSON>, n<PERSON><PERSON><PERSON>, và mua <PERSON>", "interstitialBaseWelcomeItemTitle_2": "K<PERSON>á<PERSON> phá hệ sinh thái Base", "interstitialBaseWelcomeItemTitle_3": "An toàn và bảo mật", "interstitialBaseWelcomeItemDescription_1": "Chuyển khoản và mua USDC và ETH trên Base bằng {{paymentMethod}}, thẻ, hoặc Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Sử dụng Phantom v<PERSON><PERSON> tất cả các ứng dụng DeFi và NFT ưa thích của bạn.", "interstitialBaseWelcomeItemDescription_3": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> an toàn với hỗ tr<PERSON>, l<PERSON><PERSON> th<PERSON>, và gi<PERSON> lập giao d<PERSON>.", "privacyPolicyChangedInterpolated": "<PERSON><PERSON><PERSON> s<PERSON>ch quyền riêng tư của chúng tôi đã thay đổi. <1>Tìm hiểu thêm</1>", "bitcoinAddressTypesBodyTitle": "<PERSON><PERSON>c lo<PERSON>i địa chỉ Bitcoin", "bitcoinAddressTypesFeature1Title": "Thông tin về địa chỉ Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom hỗ trợ Native Se<PERSON><PERSON><PERSON> và <PERSON>, cả hai đều có số dư riêng. Bạn có thể gửi BTC hoặc Ordinal với một trong hai loại địa chỉ này.", "bitcoinAddressTypesFeature2Title": "<PERSON><PERSON><PERSON><PERSON> tự nhiên", "bitcoinAddressTypesFeature2Subtitle": "Địa chỉ BTC mặc định trong Phantom. <PERSON><PERSON> hơn Taproot nhưng tương thích với tất cả các ví và sàn giao dịch.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Tốt nhất cho Ordinal và BRC-20, với phí thấp nhất. Điều chỉnh các địa chỉ trong Tùy chọn -> Địa chỉ Bitcoin ưu tiên.", "headerTitleInfo": "Thông tin", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "<PERSON><PERSON><PERSON> là địa chỉ <1>{{addressType}}</1> củ<PERSON> bạn.", "invalidChecksumTitle": "<PERSON>úng tôi đã nâng cấp cụm từ bí mật của bạn!", "invalidChecksumFeature1ExportPhrase": "<PERSON><PERSON><PERSON> từ bí mật mới của bạn", "invalidChecksumFeature1ExportPhraseDescription": "<PERSON>ui lòng sao lưu cụm từ bí mật mới của bạn cùng với các khóa riêng tư của tài khoản cũ của bạn.", "invalidChecksumFeature2FundsAreSafe": "Ti<PERSON>n của bạn đang được an toàn và bảo mật", "invalidChecksumFeature2FundsAreSafeDescription": "<PERSON><PERSON><PERSON><PERSON> nâng cấp được thực hiện tự động. Không có ai tại Phantom biết cụm từ bí mật hay có quyền truy cập tiền của bạn.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "invalidChecksumFeature3LearnMoreDescription": "Bạn có một cụm từ không tương thích với hầu hết các ví. <PERSON><PERSON><PERSON> đọc <1>bài viết trợ gi<PERSON><PERSON> này</1> để tìm hiểu thêm.", "invalidChecksumBackUpSecretPhrase": "<PERSON><PERSON> <PERSON><PERSON><PERSON> c<PERSON>m từ bí mật", "migrationFailureTitle": "<PERSON><PERSON> xảy ra lỗi khi di chuyển tài khoản của bạn", "migrationFailureFeature1": "<PERSON><PERSON><PERSON> cụm từ bí mật của bạn", "migrationFailureFeature1Description": "<PERSON><PERSON> lòng sao lưu cụm từ bí mật của bạn trư<PERSON>c khi tham gia.", "migrationFailureFeature2": "<PERSON>ham gia <PERSON>", "migrationFailureFeature2Description": "Bạn sẽ cần tham gia lại Phantom để xem tài khoản của mình.", "migrationFailureFeature3": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "migrationFailureFeature3Description": "Đ<PERSON><PERSON> <1>b<PERSON><PERSON> viết hướng dẫn này</1> để tìm hiểu thêm.", "migrationFailureContinueToOnboarding": "<PERSON><PERSON><PERSON><PERSON> tục tham gia", "migrationFailureUnableToFetchMnemonic": "<PERSON><PERSON>g tôi đã không thể tải cụm từ bí mật của bạn", "migrationFailureUnableToFetchMnemonicDescription": "<PERSON><PERSON> lòng liên hệ bộ phận hỗ trợ và tải về nhật ký ứng dụng để gỡ lỗi", "migrationFailureContactSupport": "<PERSON><PERSON><PERSON> hệ với bộ phận Hỗ trợ", "ledgerActionConfirm": "<PERSON><PERSON><PERSON>n trên Ledger <PERSON> b<PERSON>n", "ledgerActionErrorBlindSignDisabledPrimaryText": "<PERSON><PERSON> khống đã bị vô hiệu", "ledgerActionErrorBlindSignDisabledSecondaryText": "<PERSON><PERSON> lòng đảm bảo ký khống đư<PERSON><PERSON> bật trên thiết bị của bạn và thử lại", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "<PERSON><PERSON><PERSON><PERSON> bị phần cứng bị ngắt kết nối trong quá trình hoạt động", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "<PERSON><PERSON> lòng đóng phần mở rộng Phantom và thử lại", "ledgerActionErrorDeviceLockedPrimaryText": "<PERSON><PERSON><PERSON><PERSON> bị đã bị kh<PERSON>a", "ledgerActionErrorDeviceLockedSecondaryText": "<PERSON><PERSON> lòng mở khóa thiết bị của bạn và thử lại", "ledgerActionErrorHeader": "Lỗi hành động với sổ cái", "ledgerActionErrorUserRejectionPrimaryText": "Ngư<PERSON>i dùng đã từ chối giao dịch", "ledgerActionErrorUserRejectionSecondaryText": "<PERSON><PERSON><PERSON> động này bị người dùng từ chối trên thiết bị", "ledgerActionNeedPermission": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> quyền", "ledgerActionNeedToConfirm": "Bạn cần xác nhận giao dịch trên ví cứng của mình. <PERSON><PERSON><PERSON> bảo đảm là ví được mở khóa trên ứng dụng {{chainType}}.", "ledgerActionNeedToConfirmMany": "Bạn cần xác nhận {{numberOfTransactions}} giao dịch trên ví cứng của mình. <PERSON><PERSON><PERSON> bảo đảm là ví được mở khóa trên ứng dụng {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Bạn cần xác nhận giao dịch trên ví cứng của mình. <PERSON><PERSON><PERSON> bảo đảm là ví được mở khóa, và chức năng ký khống (blind signing) được bật trên ứng dụng {{chainType}}.", "ledgerActionNeedToConfirmBlindMany": "Bạn cần xác nhận {{numberOfTransactions}} giao dịch trên ví cứng của mình. <PERSON><PERSON><PERSON> bảo đảm là ví được mở khóa, và chức năng ký khống (blind signing) được bật trên ứng dụng {{chainType}}.", "ledgerActionPleaseConnect": "<PERSON><PERSON> lòng kết nối Ledger <PERSON> c<PERSON> b<PERSON>n", "ledgerActionPleaseConnectAndConfirm": "<PERSON><PERSON> lòng kết nối ví cứng và bảo đảm nó được mở khóa. Bảo đảm bạn đã phê chuẩn các quyền trong trình duyệt của bạn.", "maxInputAmount": "<PERSON><PERSON><PERSON><PERSON>", "maxInputMax": "<PERSON><PERSON><PERSON> đa", "notEnoughSolPrimaryText": "Không đủ SOL", "notEnoughSolSecondaryText": "Bạn không có đủ SOL trong ví cho giao dịch này. Vui lòng gửi thêm tiền và thử lại.", "insufficientBalancePrimaryText": "<PERSON><PERSON><PERSON><PERSON> đủ {{tokenSymbol}}", "insufficientBalanceSecondaryText": "Bạn không có đủ {{tokenSymbol}} trong ví cho giao dịch này.", "insufficientBalanceRemaining": "<PERSON>òn lại", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "Không đủ token", "notEnoughSplTokensDescription": "Bạn không có đủ token trong ví của mình cho giao dịch này. Giao dịch này sẽ hoàn tác nếu được gửi đi.", "transactionExpiredPrimaryText": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đ<PERSON> hết hạn", "transactionExpiredSecondaryText": "Bạn đã chờ quá lâu để xác nhận giao dịch và nó đã hết hạn. Giao dịch này sẽ hoàn tác nếu được gửi đi.", "transactionHasWarning": "<PERSON><PERSON><PERSON> b<PERSON>o giao d<PERSON>ch", "tokens": "token", "notificationApplicationApprovalPermissionsAddressVerification": "<PERSON><PERSON><PERSON> <PERSON>h rằng bạn sở hữu địa chỉ này", "notificationApplicationApprovalPermissionsTransactionApproval": "<PERSON><PERSON><PERSON> c<PERSON>u phê chuẩn cho các giao dịch", "notificationApplicationApprovalPermissionsViewWalletActivity": "Xem s<PERSON> dư & hoạt động của ví", "notificationApplicationApprovalParagraphText": "Việc xác nhận sẽ cho phép website này xem số dư và hoạt động cho tài khoản đư<PERSON><PERSON> chọn.", "notificationApplicationApprovalActionButtonConnect": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalActionButtonSignIn": "<PERSON><PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "Cho phép website kết nối?", "notificationApplicationApprovalAutoConfirm": "<PERSON><PERSON> động xác nhận các giao dịch", "notificationApplicationApprovalConnectDisclaimer": "Chỉ kết nối đến những website bạn tin tưởng", "notificationApplicationApprovalSignInDisclaimer": "Chỉ đăng nhập vào những website bạn tin tưởng", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Website này không an toàn cho việc sử dụng và có thể tìm cách đánh cắp tiền của bạn.", "notificationApplicationApprovalConnectUnknownApp": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "<PERSON><PERSON><PERSON><PERSON> thể kết nối với ứng dụng", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Ứng dụng này đang cố gắng kết nối với {{appNetworkName}}, nhưng {{phantomNetworkName}} đã đ<PERSON><PERSON><PERSON> chọn.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "<PERSON><PERSON> sử dụng {{networkName}}, vui lòng vào <PERSON> đặt nhà phát triển → <PERSON>ế độ Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "<PERSON><PERSON>ng kh<PERSON>ng x<PERSON>c đ<PERSON>nh", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "<PERSON><PERSON><PERSON><PERSON> kết nối với các ứng dụng di động khác hiện không được hỗ trợ bởi Ledger.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "<PERSON><PERSON> lò<PERSON> chuy<PERSON> sang một tài khoản không phải Ledger hoặc sử dụng trình duyệt trong ứng dụng và thử lại.", "notificationSignatureRequestConfirmTransaction": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "notificationSignatureRequestConfirmTransactionCapitalized": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "notificationSignatureRequestConfirmTransactions": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "notificationSignatureRequestConfirmTransactionsCapitalized": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON><PERSON> c<PERSON>u chữ kí", "notificationMessageHeader": "<PERSON>", "notificationMessageCopied": "Tin nhắn đã đư<PERSON><PERSON> sao chép", "notificationAutoConfirm": "<PERSON><PERSON> động x<PERSON>c n<PERSON>n", "notificationAutoConfirmOff": "Tắt", "notificationAutoConfirmOn": "<PERSON><PERSON><PERSON>", "notificationConfirmFooter": "Chỉ xác nhận nếu bạn tin tưởng website này.", "notificationEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "notificationPermissionRequestText": "Đ<PERSON>y chỉ là một yêu cầu về quyền truy cập. <PERSON><PERSON><PERSON> dịch có thể không đư<PERSON><PERSON> thực thi ngay lập tức.", "notificationBalanceChangesText": "<PERSON><PERSON><PERSON> thay đổi về số dư đều là ước tính. Số tiền và tài sản liên quan không đư<PERSON><PERSON> đả<PERSON> b<PERSON>.", "notificationContractAddress": "Đ<PERSON>a chỉ hợp đồng", "notificationAdvancedDetailsText": "<PERSON><PERSON><PERSON> cao", "notificationUnableToSimulateWarningText": "<PERSON><PERSON>g tôi hiện không thể xác nhận thay đổi vê số dư. Bạn có thể thử lại sau, hoặc xác nhận nếu bạn tin tưởng website này.", "notificationSignMessageParagraphText": "Việ<PERSON> ký vào tin nhắn này sẽ chứng tỏ bạn có quyền sở hữu tài khoản đã chọn.", "notificationSignatureRequestScanFailedDescription": "<PERSON><PERSON><PERSON><PERSON> thể quét tin nhắn để phát hiện các vấn đề về bảo mật. <PERSON><PERSON><PERSON> tiếp tục một cách thận trọng.", "notificationFailedToScan": "<PERSON>h<PERSON>ng thể giả lập các kết quả của yêu cầu này.\nViệc xác nhận là không an toàn và có thể dẫn đến thua lỗ.", "notificationScanLoading": "<PERSON><PERSON><PERSON> c<PERSON> quét", "notificationTransactionApprovalActionButtonConfirm": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonBack": "Quay lại", "notificationTransactionApprovalEstimatedChanges": "<PERSON><PERSON><PERSON> thay đổi đư<PERSON><PERSON>h", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Phần ước tính dựa trên những mô phỏng giao dịch và không phải phần bả<PERSON> đảm", "notificationTransactionApprovalHideAdvancedDetails": "Ẩn các chi tiết giao dịch nâng cao", "notificationTransactionApprovalNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "notificationTransactionApprovalNetwork": "Mạng l<PERSON>", "notificationTransactionApprovalEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thay đổi nào ảnh hưởng đến quyền sở hữu tài sản", "notificationTransactionApprovalSolanaAmountRequired": "<PERSON><PERSON><PERSON><PERSON> tiền b<PERSON><PERSON> buộc bởi mạng lưới Solana để xử lí giao dịch", "notificationTransactionApprovalUnableToSimulate": "Không thể mô phỏng. Vui lòng đảm bảo bạn tin tưởng website này bởi việc phê duyệt có thể dẫn đến mất tiền.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "<PERSON><PERSON><PERSON><PERSON> thể tìm đ<PERSON><PERSON><PERSON> những thay đổi số dư", "notificationTransactionApprovalViewAdvancedDetails": "<PERSON>em các chi tiết giao dịch nâng cao", "notificationTransactionApprovalKnownMalicious": "<PERSON><PERSON><PERSON> là một giao dịch độc hại. <PERSON><PERSON><PERSON><PERSON> ký cho giao dịch này sẽ khiến bạn mất tiền.", "notificationTransactionApprovalSuspectedMalicious": "<PERSON><PERSON><PERSON> tôi nghi đây là một giao dịch độc hại. Vi<PERSON><PERSON> phê duyệt cho giao dịch này sẽ khiến bạn mất tiền.", "notificationTransactionApprovalNetworkFeeHighWarning": "<PERSON><PERSON> mạng lưới bị tăng do nghẽn mạng.", "notificationTransactionERC20ApprovalDescription": "<PERSON><PERSON><PERSON><PERSON> xác nhận sẽ cho phép ứng dụng truy cập số dư của bạn bất cứ lúc nào, đến giới hạn dưới đây.", "notificationTransactionERC20ApprovalContractAddress": "Đ<PERSON>a chỉ hợp đồng", "notificationTransactionERC20Unlimited": "kh<PERSON>ng gi<PERSON>i hạn", "notificationTransactionERC20ApprovalTitle": "<PERSON><PERSON>t chi tiêu {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "<PERSON>hu hồi chi tiêu {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "<PERSON><PERSON> hồi quyền truy cập {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Tất cả {{tokenSymbol}} c<PERSON><PERSON> bạn", "notificationIncorrectModeTitle": "<PERSON> chế độ", "notificationIncorrectModeInTestnetTitle": "Bạn đang ở chế độ Testnet", "notificationIncorrectModeNotInTestnetTitle": "Bạn đang không ở chế độ Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} đang cố sử dụng một mainnet, nhưng bạn đang ở chế độ Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} đang cố sử dụng một testnet, nhưng bạn đang không ở chế độ Testnet", "notificationIncorrectModeInTestnetProceed": "<PERSON><PERSON> tiế<PERSON>, h<PERSON><PERSON> tắt chế độ <PERSON>.", "notificationIncorrectModeNotInTestnetProceed": "<PERSON><PERSON> tiế<PERSON>, <PERSON><PERSON><PERSON> bật chế độ <PERSON>.", "notificationIncorrectEIP712ChainId": "<PERSON>úng tôi đã ngăn bạn ký vào một tin nhắn không dành cho mạng mà bạn đang kết nối đến", "notificationIncorrectEIP712ChainIdDescription": "<PERSON> nhắn này yêu cầu {{messageChainId}}, bạn đang kết nối đến {{connectedChainId}}", "notificationUnsupportedNetwork": "Mạng lưới không được hỗ trợ", "notificationUnsupportedNetworkDescription": "Website này đang cố sử dụng một mạng lưới mà Phantom hiện không hỗ trợ.", "notificationUnsupportedNetworkDescriptionInterpolated": "<PERSON><PERSON> tiếp tục với một phần mở rộng khác, hã<PERSON> tắt <1><PERSON>ài đặt → Ứng dụng ví mặc định, và chọn <PERSON> hỏi</1>. Sau đó làm mới trang và kết nối lại.", "notificationUnsupportedAccount": "<PERSON><PERSON><PERSON> k<PERSON>n không được hỗ trợ", "notificationUnsupportedAccountDescription": "Website này đang cố sử dụng {{targetChainType}}, mà tài khoản {{chainType}} này không hỗ trợ.", "notificationUnsupportedAccountDescription2": "<PERSON><PERSON><PERSON><PERSON> sang một tài khoản từ một cụm khôi phục hoặc khóa riêng tư tương thích và thử lại.", "notificationInvalidTransaction": "<PERSON><PERSON><PERSON><PERSON> k<PERSON>ng h<PERSON>p l<PERSON>", "notificationInvalidTransactionDescription": "Giao dịch đã nhận từ ứng dụng này là không hợp lệ và không nên được gửi đi. Vui lòng liên lạc với nhà phát triển của ứng dụng để báo cáo vấn đề này với họ.", "notificationCopyTransactionText": "<PERSON>o chép giao d<PERSON>ch", "notificationTransactionCopied": "<PERSON><PERSON>o d<PERSON>ch đã đ<PERSON><PERSON><PERSON> sao chép", "onboardingImportOptionsPageTitle": "<PERSON><PERSON><PERSON><PERSON> một ví", "onboardingImportOptionsPageSubtitle": "<PERSON><PERSON><PERSON><PERSON> một ví hiện có bằng cụm từ bí mật, kh<PERSON><PERSON> r<PERSON>, hoặc ví phần cứng của bạn.", "onboardingImportPrivateKeyPageTitle": "<PERSON><PERSON><PERSON><PERSON> một khóa riêng", "onboardingImportPrivateKeyPageSubtitle": "<PERSON><PERSON><PERSON><PERSON> một ví đơn chuỗi hiện có", "onboardingCreatePassword": "<PERSON><PERSON><PERSON> mật k<PERSON>u", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON><PERSON> đồng ý với <1><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>n dịch vụ</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "<PERSON><PERSON><PERSON>n mật kh<PERSON>u", "onboardingCreatePasswordDescription": "Bạn sẽ dùng cái này để mở khóa ví của mình.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON> hợp lệ đối với cụm từ khôi phục bí mật", "onboardingCreatePasswordPasswordPlaceholder": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthMedium": "<PERSON>rung bình", "onboardingCreatePasswordPasswordStrengthStrong": "Mạnh", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Tôi đã lưu <PERSON> từ khôi phục bí mật của mình", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> từ khôi phục bí mật", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Cụm từ này là cách DUY NHẤT để khôi phục ví của bạn. KHÔNG chia sẻ cụm từ này với bất cứ ai!", "onboardingImportWallet": "<PERSON><PERSON><PERSON><PERSON>", "onboardingImportWalletImportExistingWallet": "<PERSON><PERSON><PERSON><PERSON> một ví có sẵn với Cụm từ khôi phục bí mật dài 12 hay 24 chữ.", "onboardingImportWalletRestoreWallet": "Khôi p<PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> từ khôi phục bí mật", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> từ khôi phục bí mật không hợp lệ", "onboardingImportWalletIHaveWords": "<PERSON><PERSON><PERSON> có một cụm từ phục hồi gồm {{numWords}} từ", "onboardingImportWalletIncorrectOrMisspelledWord": "Từ {{wordIndex}} không chính xác hoặc bị viết sai chính tả", "onboardingImportWalletIncorrectOrMisspelledWords": "<PERSON><PERSON><PERSON> từ {{wordIndexes}} không chính xác hoặc bị viết sai chính tả", "onboardingImportWalletScrollDown": "<PERSON><PERSON><PERSON><PERSON> xu<PERSON>ng", "onboardingImportWalletScrollUp": "<PERSON><PERSON><PERSON><PERSON> lên", "onboardingSelectAccountsImportAccounts": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "onboardingSelectAccountsImportAccountsDescription": "<PERSON><PERSON><PERSON> tài k<PERSON>n ví để nhập.", "onboardingSelectAccountsImportSelectedAccounts": "<PERSON><PERSON><PERSON><PERSON> các tài k<PERSON>n đã chọn", "onboardingSelectAccountsFindMoreAccounts": "<PERSON><PERSON><PERSON> thêm tài <PERSON>n", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài kho<PERSON>n nào", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} tà<PERSON> kho<PERSON>n đã đ<PERSON><PERSON><PERSON> chọn", "onboardingSelectAccountSelectAllText": "<PERSON><PERSON><PERSON> tất cả", "onboardingAdditionalPermissionsTitle": "Sử dụng ứng dụng với Phantom", "onboardingAdditionalPermissionsSubtitle": "<PERSON><PERSON> có trải nghiệm sử dụng ứng dụng liền mạch nh<PERSON>t, chúng tôi khuyên bạn cho phép Phantom đọc và thay đổi dữ liệu trên tất cả các trang web.", "interstitialAdditionalPermissionsTitle": "Sử dụng ứng dụng với Phantom", "interstitialAdditionalPermissionsSubtitle": "<PERSON><PERSON> tiếp tục trải nghiệm sử dụng các ứng dụng mà không bị gi<PERSON>, chúng tôi khuyên bạn nên cho phép Phantom đọc và thay đổi dữ liệu trên tất cả các trang web.", "recentActivityPrimaryText": "<PERSON><PERSON><PERSON> động gần đây", "removeAccountActionButtonRemove": "Xoá bỏ", "removeAccountRemoveWallet": "<PERSON><PERSON><PERSON> t<PERSON>", "removeAccountInterpolated": "Xóa {{accountName}}", "removeAccountWarningLedger": "Cho dù bạn xóa bỏ ví này khỏi Phantom, bạn vẫn có thể thêm nó vào lại bằng luồng \"Kết nối ví phần cứng\".", "removeAccountWarningSeedVault": "Cho dù bạn xóa bỏ ví này khỏi Phantom, bạn vẫn có thể thêm nó vào lại bằng luồng \"Kết nối với Seed Vault Wallet\".", "removeAccountWarningPrivateKey": "<PERSON><PERSON><PERSON> khi bạn xóa bỏ ví này, <PERSON> sẽ không thể nào khôi phục nó cho bạn. <PERSON><PERSON><PERSON> bảo đảm bạn đã sao lưu khóa riêng.", "removeAccountWarningSeed": "Cho dù bạn xóa bỏ ví này khỏi Phantom, bạn vẫn có thể lấy lại nó bằng lời gợi nhắc ở ví này hay ví khác.", "removeAccountWarningReadOnly": "Việc xóa tài khoản này sẽ không ảnh hưởng đến ví của bạn, bởi tài khoản này là ví chỉ được theo dõi.", "removeSeedPrimaryText": "<PERSON><PERSON><PERSON> c<PERSON>m từ bí mật {{number}}", "removeSeedSecondaryText": "<PERSON><PERSON> tác này sẽ xóa tất cả các tài khoản hiện có trong Cụm từ bí mật {{number}}. <PERSON><PERSON><PERSON> bảo đảm bạn đã lưu cụm từ bí mật hiện tại.", "resetSeedPrimaryText": "Đặt lại ứng dụng bằng cụm từ bí mật mới", "resetSeedSecondaryText": "<PERSON><PERSON> tác này sẽ xóa bỏ tất cả các tài khoản hiện có và thế chúng bằng tài khoản mới. H<PERSON>y bảo đảm bạn đã sao lưu cụm từ bí mật và khóa riêng.", "resetAppPrimaryText": "Đặt lại & x<PERSON>a hoàn toàn ứng dụng", "resetAppSecondaryText": "<PERSON><PERSON> tác này sẽ xóa tất cả các tài khoản và dữ liệu hiện có. H<PERSON>y bảo đảm bạn đã sao lưu cụm từ bí mật và khóa riêng của mình.", "richTransactionsDays": "ng<PERSON>y", "richTransactionsToday": "<PERSON><PERSON><PERSON> nay", "richTransactionsYesterday": "<PERSON><PERSON><PERSON> qua", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "<PERSON><PERSON><PERSON><PERSON> tác <PERSON>ng dụng", "richTransactionDetailAt": "l<PERSON><PERSON>", "richTransactionDetailBid": "Chào mua", "richTransactionDetailBidDetails": "<PERSON> tiết chào mua", "richTransactionDetailBought": "Đã mua", "richTransactionDetailBurned": "<PERSON><PERSON> đốt ch<PERSON>y", "richTransactionDetailCancelBid": "<PERSON><PERSON><PERSON> ch<PERSON>o mua", "richTransactionDetailCompleted": "<PERSON><PERSON> hoàn thành", "richTransactionDetailConfirmed": "<PERSON><PERSON> x<PERSON>c <PERSON>n", "richTransactionDetailDate": "<PERSON><PERSON><PERSON>", "richTransactionDetailFailed": "<PERSON><PERSON> thất bại", "richTransactionDetailFrom": "Từ", "richTransactionDetailItem": "<PERSON><PERSON><PERSON>", "richTransactionDetailListed": "<PERSON><PERSON> niêm y<PERSON>t", "richTransactionDetailListingDetails": "<PERSON> tiết niêm yết", "richTransactionDetailListingPrice": "<PERSON><PERSON><PERSON>", "richTransactionDetailMarketplace": "<PERSON>ợ điện tử", "richTransactionDetailNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "richTransactionDetailOriginalListingPrice": "<PERSON><PERSON><PERSON> ni<PERSON> yết ban đầu", "richTransactionDetailPending": "<PERSON><PERSON> chờ du<PERSON>", "richTransactionDetailPrice": "Giá", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON> cung cấp", "richTransactionDetailPurchaseDetails": "<PERSON> tiết đơn hàng", "richTransactionDetailRebate": "<PERSON><PERSON><PERSON>", "richTransactionDetailReceived": "Đã nhận", "richTransactionDetailSaleDetails": "<PERSON> tiết giao dịch", "richTransactionDetailSent": "Đ<PERSON> gửi", "richTransactionDetailSold": "<PERSON><PERSON> bán", "richTransactionDetailStaked": "Đã đặt cọc", "richTransactionDetailStatus": "<PERSON><PERSON><PERSON><PERSON> thái", "richTransactionDetailSwap": "<PERSON><PERSON>", "richTransactionDetailSwapDetails": "<PERSON><PERSON> đổi chi tiết", "richTransactionDetailTo": "<PERSON><PERSON><PERSON>", "richTransactionDetailTokenSwap": "<PERSON><PERSON> đ<PERSON>i token", "richTransactionDetailUnknownNFT": "NFT không x<PERSON>c đ<PERSON>", "richTransactionDetailUnlisted": "<PERSON><PERSON> bỏ niêm yết", "richTransactionDetailUnstaked": "<PERSON><PERSON> hủy c<PERSON>c", "richTransactionDetailValidator": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c th<PERSON>c", "richTransactionDetailViewOnExplorer": "<PERSON><PERSON> tr<PERSON> {{explorer}}", "richTransactionDetailWithdrawStake": "<PERSON><PERSON><PERSON>", "richTransactionDetailYouPaid": "Bạn đã thanh toán", "richTransactionDetailYouReceived": "Bạn đã nhận", "richTransactionDetailUnwrapDetails": "<PERSON> tiết mở bọc", "richTransactionDetailTokenUnwrap": "Mở bọc token", "activityItemsRefreshFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải các giao dịch mới hơn.", "activityItemsPagingFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải các giao dịch cũ hơn.", "activityItemsTestnetNotAvailable": "<PERSON><PERSON><PERSON> sử giao dịch <PERSON>net tạm thời không khả dụng", "historyUnknownDappName": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "historyStatusSucceeded": "<PERSON><PERSON> thành công", "historyNetwork": "Mạng l<PERSON>", "historyAttemptedAmount": "<PERSON><PERSON> tiền của giao dịch thất bại", "historyAmount": "<PERSON><PERSON> tiền", "sendAddressBookButtonLabel": "Sổ địa chỉ", "addressBookSelectAddressBook": "Sổ địa chỉ", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON>a có địa chỉ nào đư<PERSON><PERSON> lưu", "sendAddressBookRecentlyUsed": "Sử dụng gần đây", "addressBookSelectRecentlyUsed": "Sử dụng gần đây", "sendConfirmationLabel": "<PERSON><PERSON>ã<PERSON>", "sendConfirmationMessage": "<PERSON>", "sendConfirmationNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "sendConfirmationPrimaryText": "<PERSON><PERSON><PERSON>", "sendWarning_INSUFFICIENT_FUNDS": "<PERSON>hông đủ tiền, giao d<PERSON><PERSON> này nhiều khả năng sẽ không thành công nếu được gửi đi.", "sendFungibleSummaryNetwork": "Mạng l<PERSON>", "sendFungibleSummaryNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "sendFungibleSummaryEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "sendFungiblePendingEstimatedTime": "Ước t<PERSON>h thời gian", "sendFungibleSummaryEstimatedTimeDescription": "Tốc độ giao dịch Ethereum phụ thuộc vào một vài yếu tố. Bạn có thể tăng tốc chúng bằng cách nhấn vào “Phí mạng lưới”.", "sendSummaryBitcoinPendingTxTitle": "<PERSON><PERSON><PERSON><PERSON> thể gửi yêu cầu chuyển kho<PERSON>n", "sendSummaryBitcoinPendingTxDescription": "Bạn chỉ có thể có một yêu cầu chuyển khoản BTC đang chờ duyệt tại một thời điểm. <PERSON><PERSON> lòng chờ nó hoàn tất trước khi gửi đi một yêu cầu mới.", "sendFungibleSatProtectionTitle": "Gửi với <PERSON> vệ Sat", "sendFungibleSatProtectionExplainer": "Phantom đảm bảo rằng các Ordinal và BRC20 của bạn sẽ không được sử dụng cho phí giao dịch hay chuyển khoản Bitcoin.", "sendFungibleTransferFee": "<PERSON><PERSON> chuy<PERSON>n token", "sendFungibleTransferFeeToolTip": "<PERSON><PERSON>à tạo token này sẽ nhận được một khoản phí cho mỗi giao dịch chuyển. <PERSON>í này không được đề ra hay được thu bởi Phantom.", "sendFungibleInterestBearingPercent": "<PERSON><PERSON><PERSON> su<PERSON>t hiện tại", "sendFungibleNonTransferable": "<PERSON><PERSON><PERSON><PERSON> chuyển đ<PERSON>", "sendFungibleNonTransferableToolTip": "Token này không thể được chuyển kho<PERSON>n sang một tài khoản khác.", "sendFungibleNonTransferableYes": "<PERSON><PERSON>", "sendStatusErrorMessageInterpolated": "<PERSON><PERSON> lỗi khi tìm cách gửi token đến <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "<PERSON><PERSON><PERSON> không có đủ số dư để hoàn tất giao dịch.", "sendStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON> g<PERSON><PERSON>", "sendStatusLoadingTitle": "<PERSON><PERSON> g<PERSON>...", "sendStatusSuccessMessageInterpolated": "Token của bạn đã đư<PERSON>c gửi đến <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "<PERSON><PERSON> gửi!", "sendStatusConfirmedSuccessTitle": "<PERSON><PERSON> gửi!", "sendStatusSubmittedSuccessTitle": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> g<PERSON>", "sendStatusEstimatedTransactionTime": "Th<PERSON>i gian giao d<PERSON>ch <PERSON> t<PERSON>h: {{time}}", "sendStatusViewTransaction": "<PERSON>em giao d<PERSON>ch", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> cho <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> đ<PERSON> đư<PERSON><PERSON> g<PERSON>i đến <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> đ<PERSON> đư<PERSON><PERSON> g<PERSON>i đến <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> không thể được gửi đến <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "Mã lỗi {{code}}", "sendFormErrorInsufficientBalance": "Số dư không đủ", "sendFormErrorEmptyAmount": "<PERSON><PERSON> tiền cần thiết", "sendFormInvalidAddress": "<PERSON><PERSON><PERSON> chỉ {{assetName}} kh<PERSON>ng hợp lệ", "sendFormInvalidUsernameOrAddress": "<PERSON><PERSON><PERSON> người dùng hoặc địa chỉ không hợp lệ", "sendFormErrorInvalidSolanaAddress": "Địa chỉ <PERSON><PERSON> không hợp lệ", "sendFormErrorInvalidTwitterHandle": "<PERSON>ên tà<PERSON> Twitter này chưa đư<PERSON><PERSON> đăng ký", "sendFormErrorInvalidDomain": "<PERSON>ên miền này chưa đư<PERSON><PERSON> đăng ký", "sendFormErrorInvalidUsername": "<PERSON><PERSON><PERSON> ngư<PERSON>i dùng này chưa đư<PERSON><PERSON> đăng ký", "sendFormErrorMinRequiredInterpolated": "<PERSON><PERSON><PERSON> c<PERSON>t nhất {{minAmount}} {{tokenName}}", "sendRecipientTextareaPlaceholder": "Địa chỉ SOL của người nhận", "sendRecipientTextAreaPlaceholder2": "Địa chỉ {{symbol}} của người nhận", "sendMemoOptional": "<PERSON><PERSON><PERSON>hi <PERSON> (t<PERSON><PERSON> ch<PERSON>)", "sendMemo": "Ghi nhớ", "sendOptional": "t<PERSON><PERSON>n", "settings": "Cài đặt", "settingsDapps": "dApp", "settingsSelectedAccount": "<PERSON><PERSON><PERSON> k<PERSON>n đã chọn", "settingsAddressBookNoLabel": "<PERSON><PERSON><PERSON><PERSON> nh<PERSON>n", "settingsAddressBookPrimary": "Sổ địa chỉ", "settingsAddressBookRecentlyUsed": "Sử dụng gần đây", "settingsAddressBookSecondary": "<PERSON><PERSON><PERSON><PERSON> lí những địa chỉ thường dùng", "settingsAutoLockTimerPrimary": "<PERSON>ộ định giờ khóa tự động", "settingsAutoLockTimerSecondary": "<PERSON><PERSON>i thời lượ<PERSON> của bộ định giờ khóa tự động", "settingsChangeLanguagePrimary": "<PERSON><PERSON><PERSON> ngôn ngữ", "settingsChangeLanguageSecondary": "<PERSON><PERSON><PERSON> ngôn ngữ hiển thị", "settingsChangeNetworkPrimary": "Đổi mạng lư<PERSON>i", "settingsChangeNetworkSecondary": "<PERSON><PERSON><PERSON> hình phần thiết lập mạng của bạn", "settingsChangePasswordPrimary": "<PERSON><PERSON><PERSON> mật kh<PERSON>u", "settingsChangePasswordSecondary": "<PERSON><PERSON><PERSON> mật khẩu màn hình khóa", "settingsCompleteBetaSurvey": "<PERSON><PERSON><PERSON> thành k<PERSON>o s<PERSON>t beta", "settingsDisplayLanguage": "<PERSON><PERSON><PERSON> thị ngôn ngữ", "settingsErrorCannotExportLedgerPrivateKey": "<PERSON><PERSON><PERSON><PERSON> thể xuất khóa riêng Ledger", "settingsErrorCannotRemoveAllWallets": "<PERSON><PERSON><PERSON><PERSON> thể xóa bỏ tất cả các tài kho<PERSON>n", "settingsExportPrivateKey": "<PERSON><PERSON><PERSON> thị <PERSON> riêng tư", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Mạng lưới RPC Phantom", "settingsTestNetworks": "<PERSON><PERSON><PERSON><PERSON> thử nghiệm", "settingsUseCustomNetworks": "Sử dụng các mạng lưới tùy chỉnh", "settingsTestnetMode": "<PERSON><PERSON> độ <PERSON>net", "settingsTestnetModeDescription": "<PERSON><PERSON> dụng cho số dư và kết nối ứng dụng.", "settingsWebViewDebugging": "Gỡ lỗi chế độ xem web", "settingsWebViewDebuggingDescription": "<PERSON> phép bạn kiểm tra và gỡ lỗi cho chế độ xem web của trình duyệt trong ứng dụng.", "settingsTestNetworksInfo": "<PERSON>i<PERSON><PERSON> chuyển đổi sang bất kỳ mạng lưới Testnet nào chỉ nhằm mục đích thử nghiệm. <PERSON><PERSON><PERSON> lưu ý rằng các token trên Mạng lưới Testnet không có giá trị tiền tệ.", "settingsEmojis": "<PERSON><PERSON><PERSON>", "settingsNoAddresses": "<PERSON><PERSON><PERSON><PERSON> có địa chỉ", "settingsAddressBookEmptyHeading": "<PERSON><PERSON> bạ của bạn đang trống", "settingsAddressBookEmptyText": "<PERSON><PERSON><PERSON>n nút “+” hoặc “Thêm địa chỉ” để thêm các địa chỉ ưa thích của bạn", "settingsEditWallet": "<PERSON><PERSON><PERSON> t<PERSON><PERSON>", "settingsNoTrustedApps": "<PERSON><PERSON><PERSON>ng có ứng dụng đáng tin cậy", "settingsNoConnections": "<PERSON><PERSON><PERSON> c<PERSON> kết nối nào.", "settingsRemoveWallet": "<PERSON><PERSON><PERSON> t<PERSON>", "settingsResetApp": "Đặt lại ứng dụng", "settingsBlocked": "Đã chặn", "settingsBlockedAccounts": "<PERSON><PERSON><PERSON>n bị chặn", "settingsNoBlockedAccounts": "<PERSON><PERSON><PERSON><PERSON> có tài kho<PERSON>n bị chặn nào.", "settingsRemoveSecretPhrase": "<PERSON><PERSON><PERSON> c<PERSON>m từ bí mật", "settingsResetAppWithSecretPhrase": "Đặt lại ứng dụng bằng cụm từ bí mật", "settingsResetSecretRecoveryPhrase": "Đặt lại <PERSON> từ khôi phục bí mật", "settingsShowSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> thị <PERSON>m từ khôi phục bí mật", "settingsShowSecretRecoveryPhraseSecondary": "<PERSON><PERSON><PERSON> thị cụm từ khôi phục", "settingsShowSecretRecoveryPhraseTertiary": "<PERSON><PERSON><PERSON> thị cụm từ bí mật", "settingsTrustedAppsAutoConfirmActiveUntil": "Đến {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "<PERSON><PERSON> động x<PERSON>c n<PERSON>n", "settingsTrustedAppsDisclaimer": "Chỉ bật chức năng tự động xác nhận đối với dữ liệu đáng tin cậy", "settingsTrustedAppsLastUsed": "<PERSON><PERSON><PERSON><PERSON> sử dụng {{formattedTimestamp}} tr<PERSON><PERSON><PERSON>", "settingsTrustedAppsPrimary": "<PERSON><PERSON><PERSON>ng dụng đ<PERSON><PERSON><PERSON> kết n<PERSON>i", "settingsTrustedApps": "Ứng dụng đáng tin cậy", "settingsTrustedAppsRevoke": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsRevokeToast": "{{trustedApp}} đ<PERSON> bị ng<PERSON>t kết nối", "settingsTrustedAppsSecondary": "<PERSON><PERSON><PERSON> hình các <PERSON>ng dụng đáng tin cậy", "settingsTrustedAppsToday": "<PERSON><PERSON><PERSON> nay", "settingsTrustedAppsYesterday": "<PERSON><PERSON><PERSON> qua", "settingsTrustedAppsLastWeek": "<PERSON><PERSON><PERSON> tr<PERSON>", "settingsTrustedAppsBeforeYesterday": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsDisconnectAll": "<PERSON><PERSON><PERSON> kết nối khỏi tất cả", "settingsTrustedAppsDisconnectAllToast": "Tất cả các ứng dụng đã đư<PERSON>c ngắt kết nối", "settingsTrustedAppsEndAutoConfirmForAll": "<PERSON><PERSON><PERSON> thúc tự động xác nhận cho tất cả", "settingsTrustedAppsEndAutoConfirmForAllToast": "Tất cả các phiên tự động xác nhận đều đã kết thúc", "settingsSecurityPrimary": "<PERSON>ả<PERSON> & <PERSON><PERSON><PERSON><PERSON> riêng tư", "settingsSecuritySecondary": "<PERSON><PERSON><PERSON> nhật cài đặt bảo mật của bạn", "settingsActiveNetworks": "<PERSON><PERSON><PERSON> mạng đang hoạt động", "settingsActiveNetworksAll": "<PERSON><PERSON><PERSON> c<PERSON>", "settingsActiveNetworksSolana": "Chỉ Solana", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana là mạng lưới mặc định và luôn đ<PERSON><PERSON><PERSON> bật.", "settingsDeveloperPrimary": "Cài đặt cho Nhà phát triển", "settingsAdvanced": "Cài đặt nâng cao", "settingsTransactions": "Cài đặt giao dịch", "settingsAutoConfirm": "<PERSON>ài đặt Tự động xác nhận", "settingsSecurityAnalyticsPrimary": "Chia sẻ thông tin phân tích ẩn danh", "settingsSecurityAnalyticsSecondary": "<PERSON> phép chúng tôi cải thiện", "settingsSecurityAnalyticsHelper": "Phantom không sử dụng thông tin cá nhân của bạn vì mục đích phân tích", "settingsSuspiciousCollectiblesPrimary": "Ẩn các <PERSON><PERSON> sưu tầm đáng ngờ", "settingsSuspiciousCollectiblesSecondary": "<PERSON>ật để <PERSON>n các vật sưu tầm đã bị đánh dấu", "settingsPreferredBitcoinAddress": "Địa chỉ Bitcoin ưu tiên", "settingsEnabledAddressesUpdated": "<PERSON><PERSON> cập nhật các địa chỉ hiển thị!", "settingsEnabledAddresses": "<PERSON><PERSON><PERSON> địa chỉ được cho phép", "settingsBitcoinPaymentAddressForApps": "Đ<PERSON><PERSON> chỉ thanh toán cho ứng dụng", "settingsBitcoinOrdinalsAddressForApps": "Địa chỉ Ordinal cho ứng dụng", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "<PERSON><PERSON> cả hai loại địa chỉ ở trên đều đượ<PERSON> bật, đối với một số ứng dụng nhất định như Magic Eden, địa chỉ Native Segwit của bạn sẽ được sử dụng để thanh toán cho các đơn hàng. Các tài sản đã mua sẽ được nhận vào địa chỉ Taproot của bạn.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Địa chỉ Bitcoin mặc định trong Phantom để đảm bả<PERSON> t<PERSON>h tương thích.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Mặc định)", "settingsPreferredBitcoinAddressTaprootExplainer": "<PERSON><PERSON><PERSON> địa chỉ hiện đại nhất, thư<PERSON><PERSON> với phí giao dịch rẻ hơn.", "settingsPreferredExplorers": "<PERSON><PERSON><PERSON><PERSON> tiên", "settingsPreferredExplorersSecondary": "<PERSON><PERSON><PERSON><PERSON> sang trình duyệt chuỗi khối ưu tiên của bạn", "settingsCustomGasControls": "<PERSON><PERSON><PERSON> so<PERSON>t năng lượng tùy chỉnh", "settingsSupportDesk": "<PERSON>ộ phận hỗ trợ", "settingsSubmitATicket": "<PERSON><PERSON><PERSON> m<PERSON>t <PERSON>", "settingsAttachApplicationLogs": "<PERSON><PERSON><PERSON> k<PERSON> k<PERSON> dụng", "settingsDownloadApplicationLogs": "<PERSON><PERSON><PERSON> về nhật ký <PERSON>ng dụng", "settingsDownloadApplicationLogsShort": "<PERSON><PERSON><PERSON> về <PERSON>hậ<PERSON> ký", "settingsDownloadApplicationLogsHelper": "Chứa dữ liệu cục bộ, báo cáo lỗi treo và địa chỉ ví công khai để giúp giải quyết các vấn đề về Phantom", "settingsDownloadApplicationLogsWarning": "<PERSON><PERSON><PERSON><PERSON> có dữ liệu nh<PERSON>y cảm nào, chẳng hạn như cụm từ khôi phục hoặc khóa riêng tư được bao gồm.", "settingsWallet": "Ví", "settingsPreferences": "<PERSON><PERSON><PERSON>", "settingsSecurity": "<PERSON><PERSON><PERSON>", "settingsDeveloper": "<PERSON><PERSON><PERSON> ph<PERSON>t triển", "settingsSupport": "Hỗ trợ", "settingsWalletShortcutsPrimary": "<PERSON><PERSON><PERSON> thị lối tắt <PERSON>í", "settingsAppIcon": "<PERSON><PERSON><PERSON><PERSON> tư<PERSON>ng dụng", "settingsAppIconDefault": "Mặc định", "settingsAppIconLight": "<PERSON><PERSON><PERSON>", "settingsAppIconDark": "<PERSON><PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON><PERSON>", "settingsSearchResultSelected": "<PERSON><PERSON> ch<PERSON>n", "settingsSearchResultExport": "<PERSON><PERSON><PERSON>", "settingsSearchResultSeed": "Seed", "settingsSearchResultTrusted": "<PERSON><PERSON><PERSON><PERSON> tin cậy", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "<PERSON><PERSON><PERSON><PERSON> thái", "settingsSearchResultLogs": "<PERSON><PERSON><PERSON><PERSON> ký", "settingsSearchResultBiometric": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> h<PERSON>c", "settingsSearchResultTouch": "<PERSON><PERSON><PERSON>", "settingsSearchResultFace": "Gương mặt", "settingsSearchResultShortcuts": "<PERSON><PERSON><PERSON>", "settingsAllSitesPermissionsTitle": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> <PERSON> trên tất cả các trang web", "settingsAllSitesPermissionsSubtitle": "<PERSON> phép bạn sử dụng ứng dụng liền mạch với Phantom mà không cần nhấn vào phần mở rộng", "settingsAllSitesPermissionsDisabled": "Trì<PERSON> du<PERSON>t của bạn không hỗ trợ việc thay đổi cài đặt này", "settingsSolanaCopyTransaction": "<PERSON><PERSON><PERSON> ch<PERSON> giao d<PERSON>ch", "settingsSolanaCopyTransactionDetails": "<PERSON><PERSON> ch<PERSON>p sê-ri dữ liệu giao dịch vào bộ nhớ tạm", "settingsAutoConfirmHeader": "<PERSON><PERSON> động x<PERSON>c n<PERSON>n", "refreshWebpageToApplyChanges": "<PERSON><PERSON><PERSON> mới trang web để áp dụng các thay đổi", "settingsExperimentalTitle": "<PERSON><PERSON><PERSON> t<PERSON> năng thử nghiệm", "settingsExprimentalSolanaActionsSubtitle": "Tự động mở rộng các nút Solana Action khi phát hiện các liên kết liên quan trên X.com", "stakeAccountCardActiveStake": "<PERSON><PERSON><PERSON> đang hoạt động", "stakeAccountCardBalance": "Số dư", "stakeAccountCardRentReserve": "<PERSON><PERSON><PERSON> vi<PERSON><PERSON> thuê", "stakeAccountCardRewards": "Phần thưởng gần nhất", "stakeAccountCardRewardsTooltip": "<PERSON><PERSON><PERSON> là phần thưởng gần nhất mà bạn có được nhờ đặt cọc. Bạn sẽ được thưởng sau mỗi 3 ngày.", "stakeAccountCardStakeAccount": "Địa chỉ", "stakeAccountCardLockup": "<PERSON><PERSON><PERSON><PERSON> cho đến", "stakeRewardsHistoryTitle": "<PERSON><PERSON><PERSON> s<PERSON> thưởng", "stakeRewardsActivityItemTitle": "Phần thưởng", "stakeRewardsHistoryEmptyList": "<PERSON><PERSON><PERSON><PERSON> có phần thưởng nào", "stakeRewardsTime_zero": "<PERSON><PERSON><PERSON> nay", "stakeRewardsTime_one": "<PERSON><PERSON><PERSON> qua", "stakeRewardsTime_other": "{{count}} ng<PERSON><PERSON> tr<PERSON>", "stakeRewardsItemsPagingFailed": "<PERSON><PERSON><PERSON><PERSON> thể tải các phần thưởng cũ hơn.", "stakeAccountCreateAndDelegateErrorStaking": "<PERSON><PERSON> vấn đề khi đặt cọc đối với người xác thực này. <PERSON><PERSON> lòng thử lại.", "stakeAccountCreateAndDelegateSolStaked": "SOL đã cọc!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "SOL của bạn sẽ bắt đầu kiếm được phần thưởng <1></1> sau mấy ngày nữa sau khi tài khoản cọc sang trạng thái hoạt động.", "stakeAccountCreateAndDelegateStakingFailed": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "stakeAccountCreateAndDelegateStakingSol": "<PERSON><PERSON> cọc SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "<PERSON><PERSON>g tôi sẽ tạo một tài kho<PERSON>n cọc, sau đ<PERSON> <PERSON><PERSON> thác SOL của bạn cho", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "<PERSON><PERSON>g tôi sẽ tạo một tài kho<PERSON>n cọc, sau đ<PERSON> <PERSON> thác SOL của bạn cho {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "<PERSON>em giao d<PERSON>ch", "stakeAccountDeactivateStakeSolUnstaked": "SOL hủy cọc!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Bạn sẽ có thể rút cọc của mình <1></1> sau mấy ngày nữa sau khi tài kho<PERSON>n cọc sang trạng thái ngừng hoạt động.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Bạn sẽ có thể rút cọc của mình sau mấy ngày nữa sau khi tài khoản cọc sang trạng thái ngừng hoạt động.", "stakeAccountDeactivateStakeUnstakingFailed": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> thất b<PERSON>i", "stakeAccountDeactivateStakeUnstakingFailedDescription": "<PERSON><PERSON> vấn đề khi hủy cọc đối với người xác thực này. <PERSON><PERSON> lòng thử lại.", "stakeAccountDeactivateStakeUnstakingSol": "<PERSON><PERSON> h<PERSON> c<PERSON>c SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "<PERSON><PERSON><PERSON> tôi đang bắt đầu tiến trình hủy cọc SOL của bạn.", "stakeAccountDeactivateStakeViewTransaction": "<PERSON>em giao d<PERSON>ch", "stakeAccountDelegateStakeSolStaked": "SOL đã cọc!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "SOL của bạn sẽ bắt đầu kiếm được phần thưởng <1></1> sau mấy ngày nữa sau khi tài khoản cọc sang trạng thái hoạt động.", "stakeAccountDelegateStakeStakingFailed": "<PERSON><PERSON><PERSON> thất b<PERSON>i", "stakeAccountDelegateStakeStakingFailedDescription": "<PERSON><PERSON> vấn đề khi đặt cọc đối với người xác thực này. <PERSON><PERSON> lòng thử lại.", "stakeAccountDelegateStakeStakingSol": "<PERSON><PERSON> cọc SOL...", "stakeAccountDelegateStakeStakingSolDescription": "<PERSON><PERSON>g tôi đang <PERSON>y thác SOL của bạn.", "stakeAccountDelegateStakeViewTransaction": "<PERSON>em giao d<PERSON>ch", "stakeAccountListActivationActivating": "<PERSON><PERSON> k<PERSON>", "stakeAccountListActivationActive": "<PERSON><PERSON><PERSON> đ<PERSON>", "stakeAccountListActivationInactive": "Ngưng hoạt động", "stakeAccountListActivationDeactivating": "<PERSON><PERSON><PERSON> k<PERSON>", "stakeAccountListErrorFetching": "<PERSON><PERSON><PERSON> tôi không thể truy xuất tài khoản đặt cọc. <PERSON><PERSON> lòng thử lại sau.", "stakeAccountListNoStakingAccounts": "<PERSON><PERSON><PERSON><PERSON> có tài kho<PERSON>n cọc", "stakeAccountListReload": "<PERSON><PERSON><PERSON> l<PERSON>i", "stakeAccountListViewPrimaryText": "<PERSON><PERSON><PERSON> c<PERSON> b<PERSON>n", "stakeAccountListViewStakeSOL": "Cọc SOL", "stakeAccountListItemStakeFee": "Phí {{fee}}", "stakeAccountViewActionButtonRestake": "<PERSON><PERSON><PERSON>", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON><PERSON>", "stakeAccountViewError": "Lỗi", "stakeAccountViewPrimaryText": "<PERSON><PERSON><PERSON> c<PERSON> b<PERSON>n", "stakeAccountViewRestake": "<PERSON><PERSON><PERSON>", "stakeAccountViewSOLCurrentlyStakedInterpolated": "SOL của bạn hiện đang được cọc với một người xác thực. Bạn sẽ cần hủy cọc để <3></3>truy cập khoản tiền này.<1>Tìm hiểu thêm</1>", "stakeAccountViewStakeInactive": {"part1": "<PERSON><PERSON><PERSON>n cọc này đã ngưng hoạt động. Hãy xem xét việc rút cọc hoặc tìm một người xác thực để ủy thác.", "part2": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm"}, "stakeAccountViewStakeNotFound": "<PERSON><PERSON><PERSON><PERSON> thể tìm thấy tài kho<PERSON>n cọc này.", "stakeAccountViewViewOnExplorer": "<PERSON><PERSON> tr<PERSON> {{explorer}}", "stakeAccountViewWithdrawStake": "<PERSON><PERSON><PERSON>", "stakeAccountViewWithdrawUnstakedSOL": "Rút SOL đã hủy c<PERSON>c", "stakeAccountInsufficientFunds": "Không đủ SOL khả dụng để hủy cọc hoặc rút tiền.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL được rút ra!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL của bạn đã đư<PERSON>c rút ra.", "part2": "<PERSON><PERSON><PERSON> cọc này sẽ tự động bị xóa bỏ trong vòng vài phút nữa."}, "stakeAccountWithdrawStakeViewTransaction": "<PERSON>em giao d<PERSON>ch", "stakeAccountWithdrawStakeWithdrawalFailed": "<PERSON><PERSON><PERSON> không thành", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "<PERSON><PERSON> vấn đề khi rút khỏi tài khoản cọc này. <PERSON><PERSON> lòng thử lại.", "stakeAccountWithdrawStakeWithdrawingSol": "Đang rút SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "<PERSON><PERSON>g tôi đang rút SOL của bạn khỏi tài khoản cọc này.", "startEarningSolAccount": "t<PERSON><PERSON>", "startEarningSolAccounts": "t<PERSON><PERSON>", "startEarningSolErrorClosePhantom": "Chạm vào đây và thử lại", "startEarningSolErrorTroubleLoading": "Gặp rắc r<PERSON>i khi tải cọc", "startEarningSolLoading": "<PERSON><PERSON> tả<PERSON>...", "startEarningSolPrimaryText": "Bắt đ<PERSON><PERSON> k<PERSON>m SOL", "startEarningSolSearching": "<PERSON><PERSON> tìm kiếm những tài khoản đặt cọc", "startEarningSolStakeTokens": "Cọc token và kiếm phần thưởng", "startEarningSolYourStake": "<PERSON><PERSON><PERSON> c<PERSON> b<PERSON>n", "unwrapFungibleTitle": "<PERSON><PERSON> đổi thành {{tokenSymbol}}", "unwrapFungibleDescription": "<PERSON><PERSON><PERSON> từ {{fromToken}} cho {{toToken}}", "unwrapFungibleConfirmSwap": "<PERSON><PERSON><PERSON>n <PERSON> đ<PERSON>", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "<PERSON><PERSON> <PERSON>", "swapFeesFees": "Phí", "swapFeesPhantomFee": "Phí Phantom", "swapFeesPhantomFeeDisclaimer": "<PERSON><PERSON>g tôi luôn tìm được mức giá tốt nhất có thể từ các nhà cung cấp thanh khoản hàng đầu. Một mức phí {{feePercentage}} sẽ tự động được tính vào báo giá này.", "swapFeesRate": "Giá", "swapFeesRateDisclaimer": "<PERSON><PERSON><PERSON> tốt nhất được tìm thấy bởi Jupiter Aggregator qua nhiều sàn giao dịch phi tập trung.", "swapFeesRateDisclaimerMultichain": "<PERSON><PERSON><PERSON> tốt nhất đ<PERSON><PERSON><PERSON> tìm thấy qua nhiều sàn giao dịch phi tập trung.", "swapFeesPriceImpact": "<PERSON><PERSON><PERSON> động giá", "swapFeesHighPriceImpact": "<PERSON><PERSON><PERSON> động lớn đến giá", "swapFeesPriceImpactDisclaimer": "<PERSON><PERSON><PERSON> chênh giữa giá thị trường và giá ước tính dựa trên quy mô giao dịch của bạn.", "swapFeesSlippage": "<PERSON><PERSON><PERSON><PERSON><PERSON> gi<PERSON>", "swapFeesHighSlippage": "Dung sai tr<PERSON><PERSON>t giá cao", "swapFeesHighSlippageDisclaimer": "<PERSON><PERSON><PERSON> d<PERSON>ch của bạn sẽ thất bại nếu giá thay đổi theo hướng bất lợi hơn {{slippage}}%.", "swapTransferFee": "<PERSON><PERSON> ch<PERSON> k<PERSON>n", "swapTransferFeeDisclaimer": "Việc giao dịch ${{symbol}} sẽ phát sinh phí chuyển khoản là {{feePercent}}%, phí này do nhà tạo token quy định, không phải Phantom.", "swapTransferFeeDisclaimerMany": "Việc giao dịch các token đã chọn sẽ phát sinh phí là {{feePercent}}%, phí này do nhà tạo token quy định, không phải Phantom.", "swapFeesSlippageDisclaimer": "<PERSON><PERSON> tiền chênh lệch giữa giá giao dịch so với báo giá đư<PERSON><PERSON> cung cấp.", "swapFeesProvider": "<PERSON><PERSON><PERSON> cung cấp", "swapFeesProviderDisclaimer": "Sàn giao dịch phi tập trung đã đư<PERSON><PERSON> sử dụng để hoàn tất giao dịch của bạn.", "swapEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "swapEstimatedTimeShort": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "swapEstimatedTimeDisclaimer": "Thời gian hoàn tất ước tính cho cầu nối sẽ thay đổi tùy thuộc vào nhiều yếu tố ảnh hưởng đến tốc độ giao dịch.", "swapSettingsButtonCommand": "Mở Cài đặt <PERSON><PERSON> đổi", "swapQuestionRetry": "Thử lại?", "swapUnverifiedTokens": "Token chưa đ<PERSON><PERSON><PERSON> x<PERSON>c <PERSON>h", "swapSectionTitleTokens": "<PERSON><PERSON><PERSON> token {{section}}", "swapFlowYouPay": "<PERSON><PERSON> to<PERSON> của bạn", "swapFlowYouReceive": "Bạn <PERSON>n", "swapFlowActionButtonText": "<PERSON><PERSON> lại đơn hàng", "swapAssetCardTokenNetwork": "{{symbol}} trên {{network}}", "swapAssetCardMaxButton": "<PERSON><PERSON><PERSON> đa", "swapAssetCardSelectTokenAndNetwork": "Chọn To<PERSON> và Mạng lưới", "swapAssetCardBuyTitle": "Bạn nh<PERSON>n về", "swapAssetCardSellTitle": "Bạn thanh toán", "swapAssetWarningUnverified": "Token này chưa được xác minh. Hãy chỉ tương tác với các token mà bạn tin tưởng.", "swapAssetWarningPermanentDelegate": "Một người được ủy quyền có thể vĩnh viễn đốt cháy hoặc chuyển nhượng các token này.", "swapSlippageSettingsTitle": "Cài đặt Trượt giá", "swapSlippageSettingsSubtitle": "Giao dịch của bạn sẽ không thành công nếu giá thay đổi nhiều hơn mức trượt giá. Gi<PERSON> trị quá cao sẽ dẫn đến một giao dịch bất lợi.", "swapSlippageSettingsCustom": "<PERSON><PERSON><PERSON> chỉnh", "swapSlippageSettingsHighSlippageWarning": "<PERSON>iao dịch của bạn có thể bị đón đầu và dẫn đến một giao dịch bất lợi.", "swapSlippageSettingsCustomMinError": "<PERSON><PERSON> lòng điền một giá trị cao hơn {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "<PERSON><PERSON> lòng điền một giá trị thấp hơn {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "<PERSON><PERSON> lòng điền một giá trị hợp lệ.", "swapSlippageSettingsAutoSubtitle": "Phantom sẽ tìm mức trư<PERSON>t giá thấp nhất để có một giao dịch hoán đổi thành công.", "swapSlippageSettingsAuto": "<PERSON><PERSON> động", "swapSlippageSettingsFixed": "<PERSON><PERSON>", "swapSlippageOptInTitle": "Tự động trư<PERSON><PERSON> giá", "swapSlippageOptInSubtitle": "Phantom sẽ tìm mức trượt giá thấp nhất để hoán đổi thành công. Bạn có thể thay đổi điều này bất cứ lúc nào trong Trình hoán đổi → Cài đặt trượt giá.", "swapSlippageOptInEnableOption": "<PERSON><PERSON>t Tự động trư<PERSON>t giá", "swapSlippageOptInRejectOption": "<PERSON><PERSON><PERSON><PERSON> tục với <PERSON> giá cố định", "swapQuoteFeeDisclaimer": "<PERSON><PERSON><PERSON> gi<PERSON> bao gồm {{feePercentage}} phí Phantom", "swapQuoteMissingContext": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> cảnh báo giá giao dịch", "swapQuoteErrorNoQuotes": "<PERSON><PERSON><PERSON> cách giao dịch mà không cần báo giá", "swapQuoteSolanaNetwork": "Mạng lư<PERSON>", "swapQuoteNetwork": "Mạng l<PERSON>", "swapQuoteOneTimeSerumAccount": "<PERSON><PERSON><PERSON>n Serum dùng một lần", "swapQuoteOneTimeTokenAccount": "<PERSON><PERSON><PERSON> token một lần", "swapQuoteBridgeFee": "<PERSON><PERSON> hoán đổi chuỗi chéo", "swapQuoteDestinationNetwork": "<PERSON><PERSON><PERSON> lư<PERSON>i điểm đến", "swapQuoteLiquidityProvider": "<PERSON><PERSON><PERSON> cung cấp thanh k<PERSON>n", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON>", "swapReviewFlowPrimaryText": "<PERSON><PERSON> lại đơn hàng", "swapReviewFlowYouPay": "<PERSON><PERSON> to<PERSON> của bạn", "swapReviewFlowYouReceive": "Bạn <PERSON>n", "swapReviewInsufficientBalance": "<PERSON><PERSON><PERSON><PERSON> đủ tiền", "ugcSwapWarningTitle": "<PERSON><PERSON><PERSON> b<PERSON>o", "ugcSwapWarningBody1": "Token này đang đư<PERSON><PERSON> giao dịch trên trình triển khai token {{programName}}.", "ugcSwapWarningBody2": "G<PERSON><PERSON> trị của các token này có thể dao động rất lớn, dẫn đến lợi nhuận hoặc thua lỗ tài chính đáng kể. Bạn chịu mọi rủi ro khi giao dịch.", "ugcSwapWarningConfirm": "<PERSON><PERSON><PERSON>", "bondingCurveProgressLabel": "<PERSON>iến đ<PERSON> Bonding Curve", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Trong một mô hình <PERSON> Curve, gi<PERSON> token được xác định bởi hình dạng của đường cong, tăng lên khi các token được mua và giảm khi các token được bán. Khi các token được bán hết, toàn bộ thanh khoản sẽ được nộp vào Raydium và bị đốt cháy.", "ugcFungibleWarningBanner": "Token này đang giao dịch trên {{programName}}", "ugcCreatedRowLabel": "<PERSON><PERSON><PERSON>", "ugcStatusRowLabel": "<PERSON><PERSON><PERSON><PERSON> thái", "ugcStatusRowValue": "<PERSON><PERSON> tốt ng<PERSON>", "swapTxConfirmationReceived": "Đ<PERSON> nhận!", "swapTxConfirmationSwapFailed": "<PERSON><PERSON> đổi không thành", "swapTxConfirmationSwapFailedStaleQuota": "B<PERSON>o giá này không còn hiệu lực. <PERSON><PERSON> lòng thử lại.", "swapTxConfirmationSwapFailedSlippageLimit": "<PERSON><PERSON><PERSON> trượt giá của bạn quá thấp cho giao dịch hoán đổi này. <PERSON><PERSON> lòng tăng mức trượt giá ở đầu màn hình Hoán đổi và thử lại.", "swapTxConfirmationSwapFailedInsufficientBalance": "<PERSON><PERSON>g tôi đã không thể hoàn tất đư<PERSON><PERSON> yêu cầu này. Bạn không có đủ số dư để hoàn tất giao dịch.", "swapTxConfirmationSwapFailedEmptyRoute": "<PERSON><PERSON> kho<PERSON>n cho cặp token này đã thay đổi. <PERSON><PERSON>g tôi không thể tìm được một báo giá phù hợp. <PERSON><PERSON> lòng thử lại hoặc điều chỉnh số token.", "swapTxConfirmationSwapFailedAcountFrozen": "Token này đã được đóng băng bởi tác giả của nó. Bạn không thể gửi hoặc hoán đổi token này.", "swapTxConfirmationSwapFailedTryAgain": "<PERSON><PERSON> đổi này thất b<PERSON>i, vui lòng thử lại", "swapTxConfirmationSwapFailedUnknownError": "<PERSON>úng tôi đã không thể hoàn tất việc hoán đổi. Tiền của bạn hoàn toàn không bị ảnh hưởng. <PERSON><PERSON> lòng thử lại. ", "swapTxConfirmationSwapFailedSimulationTimeout": "<PERSON><PERSON>g tôi đã không thể giả lập việc hoán đổi. Tiền của bạn hoàn toàn không bị ảnh hưởng. <PERSON><PERSON> lòng thử lại.", "swapTxConfirmationSwapFailedSimulationUnknownError": "<PERSON>úng tôi đã không thể hoàn tất việc hoán đổi. Tiền của bạn hoàn toàn không bị ảnh hưởng. <PERSON><PERSON> lòng thử lại. ", "swapTxConfirmationSwapFailedInsufficientGas": "<PERSON><PERSON><PERSON> khoản của bạn không có đủ tiền để hoàn tất giao dịch. <PERSON>ui lòng thêm tiền vào tài khoản của bạn và thử lại.", "swapTxConfirmationSwapFailedLedgerReject": "<PERSON><PERSON><PERSON><PERSON> hoán đổi đã bị từ chối bởi người dùng trên thiết bị phần cứng.", "swapTxConfirmationSwapFailedLedgerConnectionError": "<PERSON><PERSON><PERSON><PERSON> hoán đổi đã bị từ chối do lỗi kết nối thiết bị. <PERSON><PERSON> lòng thử lại.", "swapTxConfirmationSwapFailedLedgerSignError": "<PERSON><PERSON><PERSON><PERSON> hoán đổi đã bị từ chối do lỗi đăng nhập thiết bị. <PERSON><PERSON> lòng thử lại.", "swapTxConfirmationSwapFailedLedgerError": "<PERSON><PERSON><PERSON><PERSON> hoán đổi đã bị từ chối do lỗi thiết bị. <PERSON><PERSON> lòng thử lại.", "swapTxConfirmationSwappingTokens": "<PERSON><PERSON> hoán đổi các token...", "swapTxConfirmationTokens": "Token", "swapTxConfirmationTokensDeposited": "Xong! Token đã đư<PERSON><PERSON> gửi vào ví của bạn", "swapTxConfirmationTokensDepositedTitle": "Xong!", "swapTxConfirmationTokensDepositedBody": "Token đã đ<PERSON><PERSON><PERSON> gửi vào ví của bạn", "swapTxConfirmationTokensWillBeDeposited": "sẽ được gửi vào ví của bạn sau khi hoàn thành giao dịch", "swapTxConfirmationViewTransaction": "<PERSON>em giao d<PERSON>ch", "swapTxBridgeSubmitting": "<PERSON><PERSON> g<PERSON>i giao d<PERSON>ch", "swapTxBridgeSubmittingDescription": "<PERSON><PERSON> hoán đổi {{sellAmount}} trên {{sellNetwork}} đ<PERSON> lấy {{buyAmount}} trên {{buyNetwork}}", "swapTxBridgeFailed": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> g<PERSON>i đi", "swapTxBridgeFailedDescription": "<PERSON><PERSON>g tôi đã không thể hoàn tất yêu cầu này.", "swapTxBridgeSubmitted": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> g<PERSON>", "swapTxBridgeSubmittedDescription": "Th<PERSON>i gian giao dịch <PERSON> tính: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON><PERSON>n có thể đóng cửa sổ này một cách an toàn.", "swapperSwitchTokens": "Đổi token", "swapperMax": "<PERSON><PERSON><PERSON> đa", "swapperTooltipNetwork": "Mạng l<PERSON>", "swapperTooltipPrice": "Giá", "swapperTooltipAddress": "<PERSON><PERSON><PERSON>", "swapperTrendingSortBy": "<PERSON><PERSON><PERSON> xếp theo", "swapperTrendingTimeFrame": "<PERSON><PERSON><PERSON> thời gian", "swapperTrendingNetwork": "Mạng l<PERSON>", "swapperTrendingRank": "<PERSON><PERSON><PERSON> h<PERSON>", "swapperTrendingTokens": "Token xu h<PERSON>ng", "swapperTrendingVolume": "<PERSON><PERSON><PERSON><PERSON>", "swapperTrendingPrice": "Giá", "swapperTrendingPriceChange": "Thay đổi giá", "swapperTrendingMarketCap": "<PERSON><PERSON><PERSON> hóa thị trường", "swapperTrendingTimeFrame1h": "1h", "swapperTrendingTimeFrame24h": "24h", "swapperTrendingTimeFrame7d": "7ng", "swapperTrendingTimeFrame30d": "30ng", "swapperTrendingNoTokensFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token nào.", "switchToggle": "<PERSON><PERSON><PERSON>", "termsOfServiceActionButtonAgree": "<PERSON><PERSON><PERSON> đồng <PERSON>", "termsOfServiceDisclaimerFeesDisabledInterpolated": "<PERSON><PERSON><PERSON><PERSON> vào <1>\"Tôi đồng ý\"</1>, bạn chấp thuận <3><PERSON><PERSON><PERSON><PERSON> khoản và điều kiện </3> cho việc trao đổi token với Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Chúng tôi đã sửa Điều khoản dịch vụ. <PERSON><PERSON> nhấp vào <6>\"Tôi đồng ý\"</6>, bạn đồng ý với <3>Điều khoản dịch vụ</3> mới của chúng tôi.<5></5><0></0>Điều khoản dịch vụ mới của chúng tôi bao gồm <8>cơ cấu chi phí</8> mới cho một số sản phẩm nhất định.", "termsOfServicePrimaryText": "<PERSON><PERSON><PERSON><PERSON> d<PERSON><PERSON> vụ", "tokenRowUnknownToken": "<PERSON><PERSON> chưa x<PERSON>c <PERSON>", "transactionsAppInteraction": "<PERSON><PERSON><PERSON><PERSON> tác <PERSON>ng dụng", "transactionsFailedAppInteraction": "<PERSON><PERSON><PERSON><PERSON> tác ứng dụng không thành công", "transactionsBidOnInterpolated": "Chào mua cho {{name}}", "transactionsBidFailed": "<PERSON><PERSON><PERSON> gi<PERSON> không thành công", "transactionsBoughtInterpolated": "<PERSON><PERSON> mua {{name}}", "transactionsBoughtCollectible": "<PERSON><PERSON><PERSON> s<PERSON>u tầm đã mua", "transactionBridgeInitiated": "<PERSON><PERSON><PERSON> nối đã đư<PERSON><PERSON> b<PERSON><PERSON> đầu", "transactionBridgeInitiatedFailed": "<PERSON><PERSON><PERSON> đầu C<PERSON>u nối không thành công", "transactionBridgeStatusLink": "<PERSON><PERSON>m tra trạng thái trên LI.FI", "transactionsBuyFailed": "<PERSON><PERSON> không thành công", "transactionsBurnedSpam": "<PERSON><PERSON><PERSON> đ<PERSON> đốt", "transactionsBurned": "<PERSON><PERSON> đốt ch<PERSON>y", "transactionsUnwrapped": "<PERSON><PERSON> mở bọc", "transactionsUnwrappedFailed": "Mở bọc không thành công", "transactionsCancelBidOnInterpolated": "<PERSON><PERSON> hủy đấu giá cho {{name}}", "transactionsCancelBidOnFailed": "<PERSON><PERSON><PERSON> đấu giá không thành công", "transactionsError": "Lỗi", "transactionsFailed": "<PERSON><PERSON> thất bại", "transactionsSwapped": "<PERSON><PERSON> ho<PERSON> đổi", "transactionsFailedSwap": "<PERSON><PERSON> đổi không thành công", "transactionsFailedBurn": "<PERSON><PERSON><PERSON> không thành công", "transactionsFrom": "Từ", "transactionsListedInterpolated": "<PERSON><PERSON> niêm yết {{name}}", "transactionsListedFailed": "<PERSON><PERSON><PERSON> yết không thành công", "transactionsNoActivity": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "transactionsReceived": "Đã nhận", "transactionsReceivedInterpolated": "<PERSON><PERSON> nhận {{amount}} SOL", "transactionsSending": "<PERSON><PERSON> g<PERSON>...", "transactionsPendingCreateListingInterpolated": "Tạo {{name}}", "transactionsPendingEditListingInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsPendingSolanaPayTransaction": "<PERSON><PERSON><PERSON>n giao d<PERSON><PERSON>", "transactionsPendingRemoveListingInterpolated": "Bỏ niêm yết {{name}}", "transactionsPendingBurningInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsPendingSending": "<PERSON><PERSON><PERSON>", "transactionsPendingSwapping": "<PERSON><PERSON>", "transactionsPendingBridging": "<PERSON><PERSON><PERSON>", "transactionsPendingApproving": "<PERSON><PERSON>", "transactionsPendingCreatingAndDelegatingStake": "<PERSON><PERSON><PERSON> và <PERSON>y quyền đặt cọc", "transactionsPendingDeactivatingStake": "<PERSON>ang bỏ kích ho<PERSON>t đặt cọc", "transactionsPendingDelegatingStake": "<PERSON><PERSON> <PERSON><PERSON> quyền đặt cọc", "transactionsPendingWithdrawingStake": "<PERSON><PERSON> rút lại đặt cọc", "transactionsPendingAppInteraction": "<PERSON><PERSON> chờ ứng dụng tương tác", "transactionsPendingBitcoinTransaction": "<PERSON><PERSON><PERSON> d<PERSON>ch BTC đang chờ duyệt", "transactionsSent": "Đ<PERSON> gửi", "transactionsSendFailed": "<PERSON><PERSON><PERSON> không thành công", "transactionsSwapOn": "<PERSON><PERSON> đổi trên {{dappName}}", "transactionsSentInterpolated": "<PERSON><PERSON> gửi {{amount}} SOL", "transactionsSoldInterpolated": "<PERSON><PERSON> bán {{name}}", "transactionsSoldCollectible": "<PERSON><PERSON><PERSON> s<PERSON>u tầm đã bán", "transactionsSoldFailed": "<PERSON><PERSON> không thành công", "transactionsStaked": "Đã đặt cọc", "transactionsStakedFailed": "Đặt cọc không thành công", "transactionsSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "transactionsTo": "<PERSON><PERSON><PERSON>", "transactionsTokenSwap": "<PERSON><PERSON> đ<PERSON>i token", "transactionsUnknownAmount": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "transactionsUnlistedInterpolated": "<PERSON><PERSON> bỏ niêm yết {{name}}", "transactionsUnstaked": "<PERSON><PERSON> hủy c<PERSON>c", "transactionsUnlistedFailed": "Bỏ niêm yết không thành công", "transactionsDeactivateStake": "Đặt cọc đã được bỏ kích hoạt", "transactionsDeactivateStakeFailed": "Bỏ kích hoạt đặt cọc không thành công", "transactionsWaitingForConfirmation": "<PERSON><PERSON> chờ x<PERSON>c <PERSON>n", "transactionsWithdrawStake": "<PERSON><PERSON><PERSON>", "transactionsWithdrawStakeFailed": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> không thành công", "transactionCancelled": "<PERSON><PERSON> hủy", "transactionCancelledFailed": "<PERSON><PERSON><PERSON> không thành công", "transactionApproveToken": "{{tokenSymbol}} đ<PERSON> đ<PERSON><PERSON><PERSON> phê <PERSON>", "transactionApproveTokenFailed": "<PERSON><PERSON><PERSON><PERSON> thể phê duyệt cho {{tokenSymbol}}", "transactionApprovalFailed": "<PERSON><PERSON> không thành công", "transactionRevokeApproveToken": "{{tokenSymbol}} đ<PERSON> bị hủy phê du<PERSON>t", "transactionRevokeApproveTokenFailed": "<PERSON><PERSON><PERSON><PERSON> thể hủy phê duyệt {{tokenSymbol}}", "transactionRevokeFailed": "<PERSON><PERSON> hồi không thành công", "transactionApproveDetailsTitle": "<PERSON> tiết phê du<PERSON>t", "transactionCancelOrder": "<PERSON><PERSON><PERSON>", "transactionCancelOrderFailed": "<PERSON><PERSON><PERSON> l<PERSON>nh không thành công", "transactionApproveAppLabel": "Ứng dụng", "transactionApproveAmountLabel": "<PERSON><PERSON> tiền", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "<PERSON><PERSON> s<PERSON>u tập", "transactionApproveAllItems": "<PERSON><PERSON> tất cả các mục", "transactionSpendUpTo": "<PERSON> tiêu tối đa", "transactionCancel": "<PERSON><PERSON><PERSON> giao d<PERSON>ch", "transactionPrioritizeCancel": "Ưu tiên việ<PERSON> hủy", "transactionSpeedUp": "<PERSON><PERSON><PERSON> tốc giao d<PERSON>ch", "transactionCancelHelperText": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> gốc có thể hoàn tất tr<PERSON><PERSON><PERSON> khi nó bị hủy.", "transactionSpeedUplHelperText": "<PERSON><PERSON><PERSON>u này sẽ tối đa hóa tốc độ giao dịch của bạn dựa trên điều kiện mạng lưới.", "transactionCancelHelperMobile": "Sẽ cần <1>tối đa {{amount}}</1> để nỗ lực hủy giao dịch này. Giao dịch gốc có thể hoàn tất trước khi nó bị hủy.", "transactionCancelHelperMobileWithEstimate": "Sẽ cần <1>tối đa {{amount}}</1> để nỗ lực hủy giao dịch này. Việc này sẽ được hoàn tất trong khoảng {{timeEstimate}}. Giao dịch gốc có thể hoàn tất trước khi nó bị hủy.", "transactionSpeedUpHelperMobile": "Sẽ cần <1>đ<PERSON>n {{amount}}</1> để tối đa hóa tốc độ của giao dịch này.", "transactionSpeedUpHelperMobileWithEstimate": "Sẽ cần <1>đ<PERSON><PERSON> {{amount}}</1> để tối đa hóa tốc độ của giao dịch này. Việc này sẽ hoàn tất trong khoảng {{timeEstimate}}.", "transactionEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "transactionCancelingSend": "<PERSON><PERSON> h<PERSON>y vi<PERSON><PERSON> g<PERSON>i đi", "transactionPrioritizingCancel": "Ưu tiên việ<PERSON> hủy", "transactionCanceling": "<PERSON><PERSON>", "transactionReplaceError": "Đã xảy ra lỗi. <PERSON><PERSON><PERSON><PERSON> có phí nào bị tính cho tài khoản của bạn. Bạn có thể thử lại.", "transactionNotEnoughNative": "<PERSON><PERSON><PERSON>ng đủ {{nativeTokenSymbol}}", "transactionGasLimitError": "<PERSON><PERSON><PERSON><PERSON> thể ước tính giới hạn năng lượng", "transactionGasEstimationError": "<PERSON><PERSON><PERSON><PERSON> thể ước tính năng lượng", "pendingTransactionCancel": "<PERSON><PERSON><PERSON>", "pendingTransactionSpeedUp": "<PERSON><PERSON><PERSON> tốc", "pendingTransactionStatus": "<PERSON><PERSON><PERSON><PERSON> thái", "pendingTransactionPending": "<PERSON><PERSON> chờ du<PERSON>", "pendingTransactionPendingInteraction": "<PERSON><PERSON> chờ tương tác", "pendingTransactionCancelling": "<PERSON><PERSON>", "pendingTransactionDate": "<PERSON><PERSON><PERSON>", "pendingTransactionNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "pendingTransactionEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "pendingTransactionEstimatedTimeHM": "{{hours}}g {{minutes}}ph", "pendingTransactionEstimatedTimeMS": "{{minutes}}ph {{seconds}}s", "pendingTransactionEstimatedTimeS": "{{seconds}}s", "pendingTransactionsSendingTitle": "<PERSON><PERSON> g<PERSON>i {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "pendingTransactionUnknownApp": "Ứng dụng không xác định", "permanentDelegateTitle": "<PERSON><PERSON> <PERSON><PERSON> quyền", "permanentDelegateValue": "<PERSON><PERSON><PERSON> vi<PERSON>n", "permanentDelegateTooltipTitle": "Ủy quyền vĩnh viễn", "permanentDelegateTooltipValue": "Ủy quyền vĩnh viễn cho phép một tài khoản khác quản lý các token thay cho bạn, điều này bao gồm đốt cháy hoặc chuyển khoản.", "unlockActionButtonUnlock": "Mở khóa", "unlockEnterPassword": "<PERSON><PERSON><PERSON><PERSON> mật kh<PERSON>u", "unlockErrorIncorrectPassword": "<PERSON><PERSON><PERSON> sai", "unlockErrorSomethingWentWrong": "<PERSON><PERSON> sự cố, h<PERSON><PERSON> thử lại sau", "unlockForgotPassword": "<PERSON><PERSON><PERSON><PERSON> mật k<PERSON>u", "unlockPassword": "<PERSON><PERSON><PERSON>", "forgotPasswordText": "Bạn có thể đặt lại mật khẩu của mình bằng cách nhập cụm từ khôi phục dài 12-24 từ cho ví của mình. Phantom không thể khôi phục mật khẩu này cho bạn.", "appInfo": "Thông tin ứng dụng", "lastUsed": "Sử dụng gần đây", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "<PERSON><PERSON><PERSON><PERSON> khả dụng với các tài khoản phần cứng.", "trustedAppAutoConfirmDisclaimer1": "Trong thời gian <PERSON> độ<PERSON>, <PERSON> sẽ xác nhận tất cả các yêu cầu từ ứng dụng này mà không thông báo cho bạn hoặc hỏi xác nhận.", "trustedAppAutoConfirmDisclaimer2": "<PERSON><PERSON><PERSON><PERSON> kích hoạt có thể khiến tiền vốn của bạn có nguy cơ bị lừa đảo. Chỉ sử dụng tính năng này với các ứng dụng mà bạn tin tưởng.", "validationUtilsPasswordIsRequired": "<PERSON><PERSON><PERSON> c<PERSON> mật kh<PERSON>u", "validationUtilsPasswordLength": "<PERSON><PERSON>t kh<PERSON>u phải dài 8 kí tự", "validationUtilsPasswordsDontMatch": "<PERSON><PERSON><PERSON> kh<PERSON>u không khớp", "validationUtilsPasswordCantBeSame": "<PERSON><PERSON><PERSON> không thể dùng mật khẩu cũ của mình", "validatorCardEstimatedApy": "APY ước t<PERSON>", "validatorCardCommission": "<PERSON><PERSON> hoa hồng", "validatorCardTotalStake": "<PERSON><PERSON><PERSON> c<PERSON>c", "validatorCardNumberOfDelegators": "Số ngườ<PERSON>c", "validatorListChooseAValidator": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> x<PERSON>c thực", "validatorListErrorFetching": "<PERSON><PERSON><PERSON> tôi không thể truy xuất đơn vị xác thực. <PERSON><PERSON> lòng thử lại sau.", "validatorListNoResults": "<PERSON><PERSON><PERSON><PERSON> kết quả", "validatorListReload": "<PERSON><PERSON><PERSON> l<PERSON>i", "validatorInfoTooltip": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c th<PERSON>c", "validatorInfoTitle": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c th<PERSON>c", "validatorInfoDescription": "Thông qua việc đặt cọc SOL trên một đơn vị xác thực, bạn sẽ đóng góp vào hiệu năng và sự an toàn của mạng lướ<PERSON>, đồng thời kiếm được SOL.", "validatorApyInfoTooltip": "APY ước t<PERSON>", "validatorApyInfoTitle": "APY ước t<PERSON>", "validatorApyInfoDescription": "<PERSON><PERSON><PERSON> là tỷ lệ lợi nhuận bạn kiếm được khi đặt cọc SOL của mình trên đơn vị xác thực.", "validatorViewActionButtonStake": "<PERSON><PERSON><PERSON>", "validatorViewErrorFetching": "<PERSON><PERSON><PERSON><PERSON> thể tìm đư<PERSON> người xác thực.", "validatorViewInsufficientBalance": "Số dư không đủ", "validatorViewMax": "<PERSON><PERSON><PERSON> đa", "validatorViewPrimaryText": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> c<PERSON>c", "validatorViewDescriptionInterpolated": "<PERSON><PERSON><PERSON> bao nhiêu SOL bạn muốn để <1></1> cọc với người xác thực này. <3>Tìm hiểu thêm</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "Cần đặt cọc {{amount}} SOL", "validatorViewValidator": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c th<PERSON>c", "walletMenuItemsAddConnectWallet": "Thêm / Kết nối ví", "walletMenuItemsBridgeAssets": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> tài sản", "walletMenuItemsHelpAndSupport": "Trợ giúp & hỗ trợ", "walletMenuItemsLockWallet": "Khoá ví", "walletMenuItemsResetSecretPhrase": "Đặt lại <PERSON> từ bí mật", "walletMenuItemsShowMoreAccounts": "<PERSON><PERSON><PERSON> thêm {{count}}...", "walletMenuItemsHideAccounts": "Ẩn tài k<PERSON>n", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "<PERSON>ế độ chỉ Solana", "disableMultiChainDetail1Header": "Đặt tất cả vào <PERSON>", "disableMultiChainDetail1SecondaryText": "<PERSON><PERSON><PERSON><PERSON> lý các tài k<PERSON>, token, và vật sưu tầm của bạn mà không cần vào các chuỗi khác.", "disableMultiChainDetail2Header": "Trở lại Multichain b<PERSON><PERSON> c<PERSON> lúc nào", "disableMultiChainDetail2SecondaryText": "Số dư Ethereum và Polygon hiện tại của bạn sẽ được giữ nguyên khi bạn bật lại Multichain.", "disableMultiChainButton": "<PERSON><PERSON>t chế độ chỉ Solana", "disabledMultiChainHeader": "Chế độ chỉ Solana đã đượ<PERSON> bật", "disabledMultiChainText": "<PERSON><PERSON><PERSON> có thể bật lại <PERSON>in bất cứ lúc nào.", "enableMultiChainHeader": "<PERSON><PERSON>t Multichain", "enabledMultiChainHeader": "Multichain đã đ<PERSON><PERSON><PERSON> bật", "enabledMultiChainText": "Ethereum và Polygon hiện được hỗ trợ trong ví của bạn.", "incompatibleAccountHeader": "<PERSON><PERSON><PERSON> k<PERSON>n không tư<PERSON>ng thích", "incompatibleAccountInterpolated": "<PERSON>ui lòng xóa các tài kho<PERSON>n chỉ Ethereum này trước khi kích hoạt chế độ chỉ Solana: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Có gì mới!", "welcomeToMultiChainPrimaryText": "Một ví cho tất cả", "welcomeToMultiChainDetail1Header": "Hỗ trợ Ethereum và Polygon", "welcomeToMultiChainDetail1SecondaryText": "Tất cả các token và NFT của bạn từ <PERSON>, Ethereum, và Polygon ở cùng một nơi.", "welcomeToMultiChainDetail2Header": "Sử dụng tất cả các ứng dụng mà bạn thích", "welcomeToMultiChainDetail2SecondaryText": "<PERSON><PERSON>t nối với các ứng dụng trên nhiều chuỗi mà không cần chuyển mạng lưới.", "welcomeToMultiChainDetail3Header": "Nhập ví MetaMask của bạn", "welcomeToMultiChainDetail3SecondaryText": "<PERSON><PERSON> dàng nhập tất cả các cụm từ khôi phục của bạn trên Ethereum và Polygon.", "welcomeToMultiChainIntro": "<PERSON><PERSON><PERSON> mừng bạn đến với Phantom Multichain", "welcomeToMultiChainIntroDesc": "Tất cả các token và NFT của bạn từ Solana, Ethereum, và Polygon đều ở cùng một nơi. Một ví cho tất cả mọi thứ.", "welcomeToMultiChainAccounts": "<PERSON><PERSON><PERSON> k<PERSON><PERSON>n Multichain đã đ<PERSON><PERSON><PERSON> thiết kế lại", "welcomeToMultiChainAccountsDesc": "<PERSON><PERSON><PERSON><PERSON> thiết kế lại cho <PERSON>, hiện mỗi tài khoản đều có các địa chỉ ETH và Polygon tương ứng.", "welcomeToMultiChainApps": "<PERSON><PERSON><PERSON> động ở mọi nơi", "welcomeToMultiChainAppsDesc": "Phantom tương thích với mọi ứng dụng trên Ethereum, Polygon, và Solana. <PERSON>hấn vào “Kết nối với MetaMask” và bạn đã sẵn sàng.", "welcomeToMultiChainImport": "<PERSON><PERSON><PERSON><PERSON> tức thì từ MetaMask", "welcomeToMultiChainImportDesc": "<PERSON><PERSON><PERSON><PERSON> từ bí mật hoặc Khóa riêng của bạn từ các ví như Ví MetaMask hoặc Coinbase. Tất cả ở cùng một nơi.", "welcomeToMultiChainImportInterpolated": "<0><PERSON><PERSON><PERSON><PERSON> từ bí mật</0> hoặc Khóa riêng của bạn từ các ví như Ví MetaMask hoặc Coinbase. Tất cả ở cùng một nơi.", "welcomeToMultiChainTakeTour": "Xem video hướng dẫn", "welcomeToMultiChainSwapperTitle": "<PERSON><PERSON> đổi trên Ethereum,\nPolygon, & Solana", "welcomeToMultiChainSwapperDetail1Header": "Hỗ trợ Ethereum và Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "<PERSON><PERSON><PERSON> đây bạn có thể dễ dàng hoán đổi các token ERC-20 từ trong ví của mình.", "welcomeToMultiChainSwapperDetail2Header": "<PERSON><PERSON><PERSON> tốt nhất và phí siêu thấp", "welcomeToMultiChainSwapperDetail2SecondaryText": "Hơn 100 nguồn thanh khoản và định tuyến lệnh thông minh cho lợi nhuận tối đa.", "networkErrorTitle": "Lỗi mạng", "networkError": "<PERSON><PERSON><PERSON> t<PERSON><PERSON><PERSON> chúng tôi không thể truy cập mạng. <PERSON><PERSON> lòng thử lại sau.", "authenticationUnlockPhantom": "Mở khóa Phantom", "errorAndOfflineSomethingWentWrong": "Đã có lỗi xảy ra", "errorAndOfflineSomethingWentWrongTryAgain": "<PERSON><PERSON> lòng thử lại.", "errorAndOfflineUnableToFetchAssets": "<PERSON><PERSON><PERSON> tôi không thể truy xuất các tài sản. <PERSON><PERSON> lòng thử lại sau.", "errorAndOfflineUnableToFetchCollectibles": "<PERSON><PERSON><PERSON> tôi không thể truy xuất các vật phẩm sưu tập. <PERSON><PERSON> lòng thử lại sau.", "errorAndOfflineUnableToFetchSwap": "<PERSON><PERSON>g tôi không thể truy xuất thông tin hoán đổi. <PERSON><PERSON> lòng thử lại sau.", "errorAndOfflineUnableToFetchTransactionHistory": "<PERSON><PERSON>g tôi không thể truy xuất lịch sử giao dịch của bạn lúc này. <PERSON><PERSON>y kiểm tra kết nối mạng của bạn hoặc thử lại sau.", "errorAndOfflineUnableToFetchRewardsHistory": "<PERSON><PERSON><PERSON> tôi không thể truy xu<PERSON>t lịch sử phần thưởng. <PERSON><PERSON> lòng thử lại sau.", "errorAndOfflineUnableToFetchBlockedUsers": "<PERSON><PERSON>g tôi không thể truy xuất những người dùng bị chặn. <PERSON><PERSON> lòng thử lại sau.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "<PERSON><PERSON> có lỗi xảy ra khi xem lại lệnh của bạn, vui lòng thử lại.", "sendSelectToken": "Ch<PERSON>n token", "swapBalance": "Số dư:", "swapTitle": "<PERSON><PERSON> đ<PERSON>i token", "swapSelectToken": "Ch<PERSON>n token", "swapYouPay": "Bạn thanh toán", "swapYouReceive": "Bạn nh<PERSON>n về", "aboutPrivacyPolicy": "<PERSON><PERSON><PERSON> s<PERSON>ch quyền riêng tư", "aboutVersion": "<PERSON><PERSON><PERSON> b<PERSON> {{version}}", "aboutVisitWebsite": "Truy c<PERSON>p website", "bottomSheetConnectTitle": "<PERSON><PERSON><PERSON>", "A11YbottomSheetConnectTitle": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> cùng kết nối", "A11YbottomSheetCommandClose": "<PERSON><PERSON><PERSON> dư<PERSON>i cùng từ chối", "A11YbottomSheetCommandBack": "Bảng dưới cùng quay lại", "bottomSheetSignTypedDataTitle": "<PERSON><PERSON> tên vào tin nh<PERSON>n", "bottomSheetSignMessageTitle": "<PERSON><PERSON> tên vào tin nh<PERSON>n", "bottomSheetSignInTitle": "<PERSON><PERSON><PERSON>", "bottomSheetSignInAndConnectTitle": "<PERSON><PERSON><PERSON>", "bottomSheetConfirmTransactionTitle": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "bottomSheetConfirmTransactionsTitle": "<PERSON><PERSON><PERSON>n giao d<PERSON>ch", "bottomSheetSolanaPayTitle": "<PERSON><PERSON><PERSON> c<PERSON><PERSON>", "bottomSheetAdvancedTitle": "<PERSON><PERSON><PERSON> cao", "bottomSheetReadOnlyAccountTitle": "<PERSON>ế độ chỉ xem", "bottomSheetTransactionSettingsTitle": "<PERSON><PERSON> mạng l<PERSON>", "bottomSheetConnectDescription": "Việc kết nối sẽ cho phép website này xem số dư và hoạt động cho tài khoản đư<PERSON><PERSON> chọn.", "bottomSheetSignInDescription": "Việc ký vào tin nhắn này sẽ chứng minh bạn có quyền sở hữu tài khoản đã chọn. Hãy chỉ ký vào các tin nhắn từ những ứng dụng mà bạn tin tưởng.", "bottomSheetSignInAndConnectDescription": "Việc phê duyệt sẽ cho phép website này xem số dư và hoạt động cho tài khoản đư<PERSON><PERSON> chọn.", "bottomSheetConfirmTransactionDescription": "<PERSON><PERSON><PERSON> thay đổi về số dư đều là ước tính. Số tiền và tài sản liên quan không đư<PERSON><PERSON> đả<PERSON> b<PERSON>.", "bottomSheetConfirmTransactionsDescription": "<PERSON><PERSON><PERSON> thay đổi về số dư đều là ước tính. Số tiền và tài sản liên quan không đư<PERSON><PERSON> đả<PERSON> b<PERSON>.", "bottomSheetSignTypedDataDescription": "Đ<PERSON>y chỉ là một yêu cầu về quyền truy cập. <PERSON><PERSON><PERSON> dịch có thể không đư<PERSON><PERSON> thực thi ngay lập tức.", "bottomSheetSignTypedDataSecondDescription": "<PERSON><PERSON><PERSON> thay đổi về số dư đều là ước tính. Số tiền và tài sản liên quan không đư<PERSON><PERSON> đả<PERSON> b<PERSON>.", "bottomSheetSignMessageDescription": "Việc ký vào tin nhắn này sẽ chứng minh bạn có quyền sở hữu tài khoản đã chọn. Hãy chỉ ký vào các tin nhắn từ những ứng dụng mà bạn tin tưởng.", "bottomSheetReadOnlyAccountDescription": "<PERSON><PERSON><PERSON><PERSON> thể thực hiện hành động này trong chế độ chỉ xem.", "bottomSheetMessageRow": "<PERSON>", "bottomSheetStatementRow": "<PERSON><PERSON><PERSON><PERSON> bố", "bottomSheetAutoConfirmRow": "<PERSON><PERSON> động x<PERSON>c n<PERSON>n", "bottomSheetAutoConfirmOff": "Tắt", "bottomSheetAutoConfirmOn": "<PERSON><PERSON><PERSON>", "bottomSheetAccountRow": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedRow": "<PERSON><PERSON><PERSON> cao", "bottomSheetContractRow": "Đ<PERSON>a chỉ hợp đồng", "bottomSheetSpenderRow": "<PERSON><PERSON><PERSON> chỉ của người chi tiêu", "bottomSheetNetworkRow": "Mạng l<PERSON>", "bottomSheetNetworkFeeRow": "<PERSON><PERSON> mạng l<PERSON>", "bottomSheetEstimatedTimeRow": "<PERSON><PERSON><PERSON><PERSON> gian <PERSON> t<PERSON>h", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Chỉ kết nối đến những website bạn tin tưởng", "bottomSheetSignInRequestDisclaimer": "Chỉ đăng nhập vào những website bạn tin tưởng", "bottomSheetSignatureRequestDisclaimer": "Chỉ xác nhận nếu bạn tin tưởng website này.", "bottomSheetFeaturedTransactionDisclaimer": "Bạn sẽ thấy một bản xem trước của giao dịch trước khi bạn xác nhận ở bước tiếp theo.", "bottomSheetIgnoreWarning": "Bỏ qua cảnh b<PERSON>o, c<PERSON> tiế<PERSON> tục", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "<PERSON><PERSON><PERSON><PERSON> tìm thấy thay đổi số dư nào. <PERSON><PERSON><PERSON> thận trọng và chỉ xác nhận nếu bạn tin tưởng website này.", "bottomSheetReadOnlyWarning": "Bạn chỉ đang theo dõi địa chỉ này. Bạn sẽ cần nhập cụm từ bí mật để ký cho các giao dịch và tin nhắn.", "bottomSheetWebsiteIsUnsafeWarning": "Website này không an toàn cho việc sử dụng và có thể tìm cách đánh cắp tiền của bạn.", "bottomSheetViewOnExplorer": "<PERSON><PERSON> trê<PERSON>", "bottomSheetTransactionSubmitted": "<PERSON><PERSON><PERSON> d<PERSON>ch đã đ<PERSON><PERSON><PERSON> g<PERSON>", "bottomSheetTransactionPending": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đang chờ xử lý", "bottomSheetTransactionFailed": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> không thành công", "bottomSheetTransactionSubmittedDescription": "<PERSON><PERSON><PERSON> dịch của bạn đã đư<PERSON>c gửi đi. Bạn có thể xem giao dịch này trên trình du<PERSON>.", "bottomSheetTransactionFailedDescription": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> không thành công. <PERSON><PERSON> lòng thử lại.", "bottomSheetTransactionPendingDescription": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> này đang đư<PERSON>c xử lý...", "transactionsFromInterpolated": "Từ: {{from}}", "transactionsFromParagraphInterpolated": "Từ {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON><PERSON> nay", "transactionsToInterpolated": "Đến: {{to}}", "transactionsToParagraphInterpolated": "<PERSON><PERSON>n {{to}}", "transactionsYesterday": "<PERSON><PERSON><PERSON> qua", "addEditAddressAdd": "<PERSON><PERSON><PERSON><PERSON> địa chỉ", "addEditAddressDelete": "<PERSON><PERSON><PERSON> địa chỉ", "addEditAddressDeleteTitle": "Bạn có chắc bạn muốn xóa địa chỉ này?", "addEditAddressSave": "<PERSON><PERSON><PERSON> chỉ", "dAppBrowserComingSoon": "<PERSON><PERSON><PERSON> có <PERSON> du<PERSON> dApp!", "dAppBrowserSearchPlaceholder": "Trang web, token, URL", "dAppBrowserOpenInNewTab": "Mở trong tab mới", "dAppBrowserSuggested": "<PERSON><PERSON> đề xuất", "dAppBrowserFavorites": "Ưa thích", "dAppBrowserBookmarks": "<PERSON><PERSON><PERSON> trang", "dAppBrowserBookmarkAdd": "<PERSON><PERSON><PERSON><PERSON>rang", "dAppBrowserBookmarkRemove": "<PERSON><PERSON><PERSON> t<PERSON>", "dAppBrowserUsers": "<PERSON><PERSON><PERSON><PERSON> dùng", "dAppBrowserRecents": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserFavoritesDescription": "<PERSON><PERSON><PERSON> mục ưa thích của bạn sẽ được hiển thị tại đây", "dAppBrowserBookmarksDescription": "<PERSON><PERSON><PERSON> dấu trang của bạn sẽ được hiển thị tại đây", "dAppBrowserRecentsDescription": "<PERSON><PERSON><PERSON> dapp kết nối gần đây sẽ được hiển thị ở đây", "dAppBrowserEmptyScreenDescription": "Nhập URL hoặc tìm trên web", "dAppBrowserBlocklistScreenTitle": "{{origin}} đã bị chặn! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom tin rằng website này độc hại và không an toàn cho việc sử dụng.", "part2": "Website này được đánh dấu là một phần của cơ sở dữ liệu các website phishing và lừa đảo có tiếng được duy trì bởi cộng đồng. Nếu bạn tin website này bị đánh dấu nhầm, hãy gử<PERSON> báo cáo sự cố."}, "dAppBrowserLoadFailedScreenTitle": "<PERSON><PERSON><PERSON><PERSON> thể tải", "dAppBrowserLoadFailedScreenDescription": "<PERSON><PERSON> xảy ra lỗi khi tải trang này", "dAppBrowserBlocklistScreenIgnoreButton": "Bỏ qua cảnh b<PERSON><PERSON>, c<PERSON> hiển thị", "dAppBrowserActionBookmark": "<PERSON><PERSON><PERSON> trang", "dAppBrowserActionRemoveBookmark": "<PERSON><PERSON><PERSON> d<PERSON>u trang", "dAppBrowserActionRefresh": "<PERSON><PERSON><PERSON>", "dAppBrowserActionShare": "<PERSON><PERSON> sẻ", "dAppBrowserActionCloseTab": "Đóng tab", "dAppBrowserActionEndAutoConfirm": "<PERSON><PERSON><PERSON> th<PERSON>c <PERSON> động xác nhận", "dAppBrowserActionDisconnectApp": "<PERSON><PERSON><PERSON> kết n<PERSON>i <PERSON>ng dụng", "dAppBrowserActionCloseAllTabs": "<PERSON><PERSON><PERSON> tất cả các tab", "dAppBrowserNavigationAddressPlaceholder": "<PERSON><PERSON><PERSON><PERSON> một URL để tìm kiếm", "dAppBrowserTabOverviewMore": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddTab": "Thêm tab", "dAppBrowserTabOverviewClose": "Đ<PERSON><PERSON>", "dAppBrowserCloseTab": "Đóng tab", "dAppBrowserClose": "Đ<PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "<PERSON><PERSON><PERSON><PERSON>rang", "dAppBrowserTabOverviewRemoveBookmark": "<PERSON><PERSON><PERSON> t<PERSON>", "depositAssetListSuggestions": "<PERSON><PERSON> xuất", "depositUndefinedToken": "<PERSON><PERSON><PERSON>, kh<PERSON><PERSON> thể nộp token này", "onboardingImportRecoveryPhraseDetails": "<PERSON> ti<PERSON>", "onboardingCreateRecoveryPhraseVerifyTitle": "Bạn đã viết C<PERSON>m từ khôi phục bí mật ra giấy?", "onboardingCreateRecoveryPhraseVerifySubtitle": "<PERSON><PERSON><PERSON> không có <PERSON>m từ khôi phục bí mật, bạn sẽ không thể truy cập khóa của mình hay bất kỳ tài liệu nào liên quan đến nó.", "onboardingCreateRecoveryPhraseVerifyYes": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorTitle": "Lỗi", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON><PERSON>g tôi đã không thể tạo một tài k<PERSON>, vui lòng thử lại.", "onboardingDoneDescription": "<PERSON><PERSON><PERSON> đây bạn có thể tận hưởng tất cả chức năng của ví mình.", "onboardingDoneGetStarted": "<PERSON><PERSON><PERSON> đ<PERSON>u", "zeroBalanceHeading": "<PERSON><PERSON><PERSON> cùng bắt đầu!", "zeroBalanceBuyCryptoTitle": "<PERSON><PERSON> tiền mã hóa", "zeroBalanceBuyCryptoDescription": "<PERSON>a đồng tiền mã hóa đầu tiên của bạn bằng thẻ ghi nợ hay tín dụng.", "zeroBalanceDepositTitle": "Chuyển tiền mã hóa", "zeroBalanceDepositDescription": "<PERSON><PERSON><PERSON> tiền mã hóa từ một ví hay sàn giao dị<PERSON>.", "onboardingImportAccountsEmptyResult": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài kho<PERSON>n nào", "onboardingImportAccountsAccountName": "<PERSON><PERSON><PERSON> {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON> k<PERSON>n xã hội", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "<PERSON>úng tôi đã tìm thấy {{numberOfWallets}} tài khoản có hoạt động", "onboardingImportAccountsFoundAccounts_other": "<PERSON>úng tôi đã tìm thấy {{numberOfWallets}} tài khoản có hoạt động", "onboardingImportAccountsFoundAccountsNoActivity_one": "<PERSON><PERSON>g tôi đã tìm thấy {{numberOfWallets}} tài k<PERSON>n", "onboardingImportAccountsFoundAccountsNoActivity_other": "<PERSON><PERSON>g tôi đã tìm thấy {{numberOfWallets}} tài k<PERSON>n", "onboardingImportRecoveryPhraseLessThanTwelve": "<PERSON><PERSON><PERSON> từ này cần dài tối thiểu 12 từ.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "<PERSON><PERSON><PERSON> từ này cần dài ch<PERSON>h xác 12 hoặc 24 từ.", "onboardingImportRecoveryPhraseWrongWord": "Từ sai: {{ words }}.", "onboardingProtectTitle": "Bảo vệ ví của bạn", "onboardingProtectDescription": "<PERSON><PERSON><PERSON><PERSON> thêm bảo mật sinh trắc học sẽ đảm bảo bạn là người duy nhất có thể truy cập ví của bạn.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON><PERSON><PERSON> bị", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Vân tay", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "S<PERSON> dụng xác thực {{ authType }}", "onboardingProtectError": "<PERSON><PERSON> có lỗi xảy ra khi xác thực, vui lòng thử lại", "onboardingProtectBiometryIosError": "Vi<PERSON><PERSON> xác thực sinh trắc học được cấu hình trong Phantom, nhưng bị vô hiệu trong Cài đặt hệ thống. <PERSON><PERSON> lòng mở Cài đặt > Phantom > Face ID hoặc Touch ID để bật lại.", "onboardingProtectRemoveAuth": "<PERSON><PERSON> hi<PERSON>u x<PERSON>c thực", "onboardingProtectRemoveAuthDescription": "Bạn có chắc bạn muốn vô hiệu việc xác thực?", "onboardingWelcomeTitle": "<PERSON><PERSON><PERSON> mừng bạn đến với Phantom", "onboardingWelcomeDescription": "<PERSON><PERSON> bắt đầu, h<PERSON><PERSON> tạo một ví mới hoặc nhập một ví sẵn có.", "onboardingWelcomeCreateWallet": "Tạo một ví mới", "onboardingWelcomeAlreadyHaveWallet": "Tôi đã có ví rồi", "onboardingWelcomeConnectSeedVault": "<PERSON><PERSON><PERSON> n<PERSON> vớ<PERSON>", "onboardingSlide1Title": "<PERSON><PERSON><PERSON><PERSON> kiểm soát bởi bạn", "onboardingSlide1Description": "<PERSON><PERSON> của bạn được bảo mật với quyền truy cập bằng sinh trắc học, ph<PERSON>t hiện lừa đảo cùng hỗ trợ 24/7.", "onboardingSlide2Title": "<PERSON><PERSON><PERSON> nhà tốt nhất cho\ncác NFT của bạn", "onboardingSlide2Description": "<PERSON><PERSON><PERSON><PERSON> lý danh sách niêm yế<PERSON>, đố<PERSON> chá<PERSON>, và lu<PERSON>n cập nhật với những thông báo đẩy hữu <PERSON>ch.", "onboardingSlide3Title": "<PERSON><PERSON><PERSON> hơn với các <PERSON> của bạn", "onboardingSlide3Description": "<PERSON><PERSON><PERSON>, <PERSON><PERSON> đ<PERSON>, đặt cọ<PERSON>, g<PERSON><PERSON>, và nhận — mà không cần rời khỏi ví của bạn. ", "onboardingSlide4Title": "<PERSON><PERSON><PERSON><PERSON> phá những điều tốt nhất của Web3", "onboardingSlide4Description": "Tìm và kết nối với các ứng dụng và bộ sưu tập hàng đầu với trình duyệt trong ứng dụng.", "onboardingMultichainSlide5Title": "Một ví cho tất cả", "onboardingMultichainSlide5Description": "<PERSON><PERSON><PERSON><PERSON> nghiệm tất cả <PERSON>, Ethereum, và Polygon trong một giao diện thân thiện với người dùng duy nhất.", "onboardingMultichainSlide5DescriptionWithBitcoin": "<PERSON><PERSON>ả<PERSON> nghiệm tất cả <PERSON>, Ethereum, Polygon, và Bitcoin trong một giao diện thân thiện với người dùng duy nhất.", "requireAuth": "<PERSON><PERSON><PERSON> x<PERSON> thực", "requireAuthImmediately": "<PERSON><PERSON> l<PERSON> t<PERSON>c", "availableToSend": "<PERSON><PERSON> thể gửi", "sendEnterAmount": "<PERSON><PERSON><PERSON><PERSON> số tiền", "sendEditMemo": "<PERSON><PERSON><PERSON> nh<PERSON>", "sendShowLogs": "<PERSON><PERSON><PERSON> thị nhật ký lỗi", "sendHideLogs": "Ẩn nhật ký lỗi", "sendGoBack": "Quay lại", "sendTransactionSuccess": "Token của bạn đã đư<PERSON><PERSON> gửi thành công cho", "sendInputPlaceholder": "@tên-người-dùng hoặc địa chỉ", "sendInputPlaceholderV2": "tên người dùng hoặc địa chỉ", "sendPeopleTitle": "<PERSON><PERSON><PERSON>", "sendDomainTitle": "<PERSON><PERSON><PERSON>", "sendFollowing": "<PERSON><PERSON> the<PERSON> d<PERSON>i", "sendRecentlyUsedAddressLabel": "<PERSON><PERSON><PERSON><PERSON> sử dụng {{formattedTimestamp}} tr<PERSON><PERSON><PERSON>", "sendRecipientAddress": "<PERSON><PERSON><PERSON> chỉ của người nhận", "sendTokenInterpolated": "Gửi {{tokenSymbol}}", "sendPasteFromClipboard": "<PERSON><PERSON> từ bộ nhớ tạm", "sendScanQR": "<PERSON>uét Mã QR", "sendTo": "Đến:", "sendRecipientZeroBalanceWarning": "Địa chỉ ví này không có số dư và không xuất hiện trong lịch sử giao dịch gần đây của bạn. Vui lòng đảm bảo rằng địa chỉ này là chính xác.", "sendUnknownAddressWarning": "<PERSON><PERSON><PERSON> không phải là một địa chỉ mà bạn đã tương tác gần đây. <PERSON><PERSON> lòng thận trọng khi tiếp tục.", "sendSameAddressWarning": "<PERSON><PERSON><PERSON> là địa chỉ hiện tại của bạn. Việc gửi sẽ phát sinh phí chuyển khoản và không có thay đổi nào khác về số dư.", "sendMintAccountWarning": "<PERSON><PERSON>y là một địa chỉ tài khoản đúc. Bạn không thể gửi tiền đến tài khoản này bởi việc này sẽ gây mất tiền vĩnh viễn.", "sendCameraAccess": "<PERSON><PERSON><PERSON> c<PERSON> camera", "sendCameraAccessSubtitle": "<PERSON><PERSON> quét một mã QR, quyền tru<PERSON> cập camera cần đ<PERSON><PERSON><PERSON> bật. Bạn có muốn mở Cài đặt ngay?", "sendSettings": "Cài đặt", "sendOK": "OK", "invalidQRCode": "Mã QR này không hợp lệ.", "sendInvalidQRCode": "Mã QR này không phải là một địa chỉ hợp lệ", "sendInvalidQRCodeSubtitle": "Thử lại hoặc với một mã QR khác.", "sendInvalidQRCodeSplToken": "Token không hợp lệ trong mã QR", "sendInvalidQRCodeSplTokenSubtitle": "Mã này chứa một token mà bạn không sở hữu hoặc chúng tôi không thể xác định nó.", "sendScanAddressToSend": "Q<PERSON>t địa chỉ {{tokenSymbol}} để gửi tiền", "sendScanAddressToSendNoSymbol": "<PERSON><PERSON>t địa chỉ để gửi tiền", "sendScanAddressToSendCollectible": "<PERSON><PERSON>t địa chỉ SOL để gửi vật phẩm sưu tập", "sendScanAddressToSendCollectibleMultichain": "<PERSON><PERSON>t địa chỉ để gửi vật phẩm sưu tập", "sendSummary": "<PERSON><PERSON><PERSON>", "sendUndefinedToken": "Xin lỗi, không thể gửi token này", "sendNoTokens": "<PERSON><PERSON><PERSON><PERSON> có token nào khả dụng", "noBuyOptionsAvailableInCountry": "<PERSON><PERSON><PERSON><PERSON> có tùy chọn Mua ở quốc gia của bạn", "swapAvailableTokenDisclaimer": "<PERSON><PERSON> giới hạn về số token khả dụng để bắc cầu gi<PERSON>a các <PERSON> lư<PERSON>i", "swapCrossSwapNetworkTooltipTitle": "<PERSON><PERSON> đổi gi<PERSON>a các mạng lư<PERSON>i", "swapCrossSwapNetworkTooltipDescription": "<PERSON><PERSON> hoán đổi gi<PERSON>a các <PERSON>, bạn nên sử dụng các token khả dụng để có giá thấp nhất và thời gian giao dịch n<PERSON>h nhất.", "settingsAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON><PERSON> về <PERSON>", "settingsShareAppWithFriends": "<PERSON><PERSON><PERSON> bạn bè của bạn", "settingsConfirm": "<PERSON><PERSON>", "settingsMakeSureNoOneIsWatching": "<PERSON><PERSON><PERSON> b<PERSON>o không ai nhìn thấy màn hình của bạn", "settingsManageAccounts": "<PERSON><PERSON><PERSON><PERSON> lý tài <PERSON>n", "settingsPrompt": "Bạn có chắc bạn muốn tiếp tục?", "settingsSelectAvatar": "<PERSON><PERSON><PERSON> đ<PERSON>n", "settingsSelectSecretPhrase": "<PERSON><PERSON><PERSON> c<PERSON>m từ bí mật", "settingsShowPrivateKey": "<PERSON><PERSON><PERSON> để hiển thị khóa riêng tư của bạn", "settingsShowRecoveryPhrase": "<PERSON>ạm để hiển thị cụm từ bí mật của bạn", "settingsSubmitBetaFeedback": "<PERSON><PERSON><PERSON> p<PERSON><PERSON>n hồi beta", "settingsUpdateAccountNameToast": "<PERSON>ên tài khoản đã đ<PERSON><PERSON><PERSON> cập nh<PERSON>t", "settingsUpdateAvatarToast": "<PERSON><PERSON><PERSON> đại diện đã đư<PERSON><PERSON> cập nhật", "settingsUpdateAvatarToastFailure": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật Hình đại diện!", "settingsWalletAddress": "Địa chỉ tài k<PERSON>n", "settingsWalletAddresses": "Địa chỉ tài k<PERSON>n", "settingsWalletNamePrimary": "<PERSON><PERSON><PERSON> tà<PERSON>", "settingsPlaceholderName": "<PERSON><PERSON><PERSON>", "settingsWalletNameSecondary": "<PERSON><PERSON>i tên ví của bạn", "settingsYourAccounts": "<PERSON><PERSON><PERSON> c<PERSON>a bạn", "settingsYourAccountsMultiChain": "<PERSON>a chuỗi", "settingsReportUser": "<PERSON><PERSON>o cáo ng<PERSON>ời dùng", "settingsNotifications": "<PERSON><PERSON><PERSON><PERSON> báo", "settingsNotificationPreferences": "<PERSON><PERSON><PERSON> chọn thông báo", "pushNotificationsPreferencesAllowNotifications": "<PERSON> phép thông báo", "pushNotificationsPreferencesSentTokens": "Token đã gửi", "pushNotificationsPreferencesSentTokensDescription": "<PERSON>ác giao d<PERSON>ch chuyển token và NFT ra ngoài", "pushNotificationsPreferencesReceivedTokens": "To<PERSON> đã nhận", "pushNotificationsPreferencesReceivedTokensDescription": "Các giao d<PERSON>ch nhận vào token và NFT", "pushNotificationsPreferencesDexSwap": "<PERSON><PERSON>", "pushNotificationsPreferencesDexSwapDescription": "<PERSON><PERSON> đổi trên các ứng dụng đư<PERSON>c công nhận", "pushNotificationsPreferencesOtherBalanceChanges": "<PERSON><PERSON><PERSON> thay đổi số dư kh<PERSON>c", "pushNotificationsPreferencesOtherBalanceChangesDescription": "<PERSON><PERSON><PERSON> giao dịch nhiều token khác ảnh hưởng đến số dư của bạn", "pushNotificationsPreferencesPhantomMarketing": "<PERSON><PERSON><PERSON> nhật từ <PERSON>", "pushNotificationsPreferencesPhantomMarketingDescription": "<PERSON><PERSON><PERSON> thông tin công bố tính năng và thông tin cập nhật chung", "pushNotificationsPreferencesDescription": "<PERSON><PERSON><PERSON> cài đặt này kiểm soát thông báo đẩy cho ví đang hoạt động này. Mỗi ví đều có cài đặt thông báo riêng của chúng. <PERSON><PERSON> tắt tất cả thông báo đẩy của <PERSON>, hãy truy cập <1>cài đặt thiết bị</1> của bạn.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "<PERSON><PERSON><PERSON><PERSON> thể đồng bộ tùy chọn thông báo.", "connectSeedVaultConnectSeed": "<PERSON><PERSON><PERSON> n<PERSON> m<PERSON>t <PERSON>d", "connectSeedVaultConnectSeedDescription": "<PERSON><PERSON>t nối Phantom v<PERSON><PERSON> Seed <PERSON>ault trên điện tho<PERSON>i của bạn", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON><PERSON> một tài <PERSON>n", "connectSeedVaultSelectASeed": "<PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeedDescription": "<PERSON><PERSON><PERSON> Seed mà bạn muốn kết nối với Phantom", "connectSeedVaultSelectAnAccountDescription": "<PERSON><PERSON>n tài khoản mà bạn muốn thiết lập với <PERSON>", "connectSeedVaultNoAccountsFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy tài k<PERSON>n nào.", "connectSeedVaultSelectAccounts": "<PERSON><PERSON><PERSON> tà<PERSON>", "connectSeedVaultSelectAccountsDescription": "<PERSON><PERSON><PERSON> các tài khoản mà bạn muốn thiết lập với <PERSON>", "connectSeedVaultCompleteSetup": "<PERSON><PERSON><PERSON> tất vi<PERSON><PERSON> thiết lập", "connectSeedVaultCompleteSetupDescription": "Bạn đã sẵn sàng! H<PERSON>y khám phá web3 với Phantom và sử dụng Seed Vault để xác nhận các giao dịch", "connectSeedVaultConnectAnotherSeed": "<PERSON><PERSON><PERSON> n<PERSON><PERSON> một <PERSON>", "connectSeedVaultConnectAllSeedsConnected": "Tất cả các Seed đều đã đư<PERSON><PERSON> kết nối", "connectSeedVaultNoSeedsConnected": "<PERSON><PERSON><PERSON><PERSON> có seed nà<PERSON> đư<PERSON>c kết nối. <PERSON>ạm vào nút dưới đây để xác thực từ Seed Vault.", "connectSeedVaultConnectAccount": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n", "connectSeedVaultLoadMore": "<PERSON><PERSON><PERSON>ê<PERSON>", "connectSeedVaultNeedPermission": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> quyền", "connectSeedVaultNeedPermissionDescription": "<PERSON><PERSON><PERSON> đặt để cho phép Phantom sử dụng quyền truy cập Seed <PERSON>.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "Phí {{fee}}", "stakeAmount": "<PERSON><PERSON> tiền", "stakeAmountBalance": "Số dư", "swapTopQuotes": "Top {{numQuotes}} báo giá", "swapTopQuotesTitle": "<PERSON><PERSON><PERSON> giá hàng đầu", "swapProvidersTitle": "<PERSON><PERSON><PERSON> cung cấp", "swapProvidersFee": "Phí {{fee}}", "swapProvidersTagRecommended": "<PERSON><PERSON><PERSON> nhu<PERSON>n tốt nhất", "swapProvidersTagFastest": "<PERSON><PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}}g {{minutes}}ph", "swapProviderEstimatedTimeM": "{{minutes}}ph", "swapProviderEstimatedTimeS": "{{seconds}}s", "stakeReview": "<PERSON><PERSON>", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "<PERSON><PERSON> hoa hồng", "stakeReviewConfirm": "<PERSON><PERSON><PERSON>", "stakeReviewValidator": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>c th<PERSON>c", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Chuyển đổi đặt cọc không thành công", "convertStakeStatusErrorMessage": "Đặt cọc của bạn không thể được chuyển đổi sang {{poolTokenSymbol}}. <PERSON><PERSON> lòng thử lại.", "convertStakeStatusLoadingTitle": "<PERSON><PERSON> chuy<PERSON>n đ<PERSON><PERSON> sang {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "<PERSON><PERSON>g tôi đang bắt đầu quy trình chuyển đổi {{stakedTokenSymbol}} đã đặt cọc của bạn sang {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "<PERSON>y<PERSON><PERSON> đ<PERSON><PERSON> sang {{poolTokenSymbol}} đã hoàn tất!", "convertStakeStatusSuccessMessage": "<PERSON><PERSON><PERSON><PERSON> thêm phần thưởng với JitoSOL của bạn <1>tại đây.</1>", "convertStakeStatusConvertMore": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>i thêm n<PERSON>a", "convertStakePendingTitle": "<PERSON><PERSON> chuyển đổi đặt cọc sang {{symbol}}", "convertToJitoSOL": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> sang JitoSOL", "convertToJitoSOLInfoDescription": "Chuyển đổi SOL củ<PERSON> bạn sang Jito SOL để nhận phần thưởng và tham gia hệ sinh thái Jito.", "convertToJitoSOLInfoTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> sang JitoSOL", "convertStakeBannerTitle": "Chuyển đổi đặt cọc của bạn sang JitoSOL để tăng đến 15% phần thưởng", "convertStakeQuestBannerTitle": "Chuyển đổi SOL đã đặt c<PERSON><PERSON> sang JitoSOL và nhận phần thưởng. Tìm hiểu thêm.", "liquidStakeConvertInfoTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> sang JitoSOL", "liquidStakeConvertInfoDescription": "Tăng cường phần thưởng của bạn bằng cách chuyển đổi đặt cọc SOL của bạn sang JitoSOL. <1>Tìm hiểu thêm</1>", "liquidStakeConvertInfoFeature1Title": "Vì sao đặt cọc với Jito?", "liquidStakeConvertInfoFeature1Description": "<PERSON><PERSON><PERSON> tiền để nhận JitoSOL, vốn sẽ tăng lên theo đặt cọc của bạn. Sử dụng nó trong các giao thức DeFi để tăng thu nhập. <PERSON><PERSON> đổi JitoSOL của bạn trong tương lai để nhận số tiền ban đầu + phần thưởng tích lũy", "liquidStakeConvertInfoFeature2Title": "Phần thưởng trung bình cao hơn", "liquidStakeConvertInfoFeature2Description": "Jito sẽ phân bổ SOL của bạn cho những đơn vị xác thực tốt nhất có phí thấp nhất. <PERSON><PERSON><PERSON> phần thưởng MEV sẽ tăng hơn nữa thu nhập của bạn.", "liquidStakeConvertInfoFeature3Title": "Hỗ trợ mạng lưới <PERSON>ana", "liquidStakeConvertInfoFeature3Description": "Đặt cọc thanh khoản tăng tính đảm bảo cho <PERSON> bằng cách phân bổ đặt cọc cho nhiều đơn vị xác thực khác <PERSON>u, gi<PERSON><PERSON> rủi ro từ những đơn vị xác thực có thời gian hoạt động thấp.", "liquidStakeConvertInfoSecondaryButton": "Để sau", "liquidStakeStartStaking": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> c<PERSON>c", "liquidStakeReviewOrder": "<PERSON><PERSON> lại đơn hàng", "convertStakeAccountListPageIneligibleSectionTitle": "<PERSON><PERSON><PERSON> k<PERSON>n đặt cọc không đủ tiêu chuẩn", "convertStakeAccountIneligibleBottomSheetTitle": "<PERSON><PERSON><PERSON> k<PERSON>n đặt cọc không đủ tiêu chuẩn", "convertStakeAccountListPageErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể truy xuất tài khoản đặt cọc", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON><PERSON> ti<PERSON><PERSON>, đã xảy ra lỗi và chúng tôi đã không thể truy xuất các tài kho<PERSON>n đặt cọc", "liquidStakeReviewYouPay": "Bạn thanh toán", "liquidStakeReviewYouReceive": "Bạn nh<PERSON>n về", "liquidStakeReviewProvider": "<PERSON><PERSON><PERSON> cung cấp", "liquidStakeReviewNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "liquidStakeReviewPageTitle": "<PERSON><PERSON><PERSON>", "liquidStakeReviewConversionFootnote": "<PERSON><PERSON> bạn đặt cọc các token Solana để đổi lấy JitoSOL, bạn sẽ nhận về một lượng JitoSOL gần tương đương. <1>Tìm hiểu thêm</1>", "convertStakeAccountIneligibleBottomSheetDescription": "<PERSON><PERSON><PERSON> đặt cọc của Jito hỗ trợ hầu hết các đơn vị xác thực Solana đang hoạt động. Bạn sẽ không thể chuyển đổi SOL đã đặt cọc từ các đơn vị xác thực không được hỗ trợ sang JitoSOL. <PERSON><PERSON><PERSON><PERSON> ra, SOL mới đặt cọc sẽ cần ~2 ngày trước khi đủ tiêu chuẩn để chuyển đổi JitoSOL.", "selectAValidator": "<PERSON><PERSON><PERSON> một đơn vị xác thực", "validatorSelectionListTitle": "<PERSON><PERSON><PERSON> một đơn vị xác thực", "validatorSelectionListDescription": "<PERSON><PERSON><PERSON> một đơn vị xác thực để đặt cọc SOL của bạn.", "stakeMethodDescription": "Nhận lãi suất bằng cách sử dụng các token SOL của bạn để giúp <PERSON> mở rộng. <1>Tìm hiểu thêm</1>", "stakeMethodRecommended": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON> xuất", "stakeMethodEstApy": "APY ước t<PERSON>h: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Đặt cọc thanh k<PERSON>n", "stakeMethodSelectionLiquidStakingDescription": "Đặt cọc SOL để nhận thưởng cao hơn, g<PERSON><PERSON><PERSON> bả<PERSON> mậ<PERSON> & nhận JitoSOL để nhận thêm phần thưởng.", "stakeMethodSelectionNativeStakingTitle": "Đặt cọc tự nhiên", "stakeMethodSelectionNativeStakingDescription": "Đặt cọc SOL để nhận phần thưởng, đồng thời gi<PERSON><PERSON> bả<PERSON> mật <PERSON>.", "liquidStakeMintStakeSOL": "Đặt c<PERSON>c <PERSON>", "mintJitoSOLInfoPageTitle": "<PERSON><PERSON><PERSON>i thiệu Đặt cọc thanh kho<PERSON>n với <PERSON>to", "mintJitoSOLFeature1Title": "Vì sao đặt cọc với Jito?", "mintJitoSOLFeature1Description": "<PERSON><PERSON><PERSON> tiền để nhận JitoSOL, vốn sẽ tăng lên theo đặt cọc của bạn. Sử dụng nó trong các giao thức DeFi để tăng thu nhập. <PERSON><PERSON> đổi JitoSOL của bạn trong tương lai để nhận số tiền ban đầu + phần thưởng tích lũy", "mintJitoSOLFeature2Title": "Phần thưởng trung bình cao hơn", "mintJitoSOLFeature2Description": "Jito sẽ phân bổ SOL của bạn cho những đơn vị xác thực tốt nhất có phí thấp nhất. <PERSON><PERSON><PERSON> phần thưởng MEV sẽ tăng hơn nữa thu nhập của bạn.", "mintJitoSOLFeature3Title": "Hỗ trợ mạng lưới <PERSON>ana", "mintJitoSOLFeature3Description": "Đặt cọc thanh khoản tăng tính đảm bảo cho <PERSON> bằng cách phân bổ đặt cọc cho nhiều đơn vị xác thực khác <PERSON>u, gi<PERSON><PERSON> rủi ro từ những đơn vị xác thực có thời gian hoạt động thấp.", "mintLiquidStakePendingTitle": "<PERSON><PERSON><PERSON>", "mintStakeStatusErrorTitle": "<PERSON><PERSON><PERSON> c<PERSON>c thanh kho<PERSON>n không thành công", "mintStakeStatusErrorMessage": "<PERSON><PERSON><PERSON> k<PERSON>n {{poolTokenSymbol}} củ<PERSON> bạn không thể được đúc. <PERSON><PERSON> lòng thử lại.", "mintStakeStatusSuccessTitle": "<PERSON><PERSON><PERSON> c<PERSON> {{poolTokenSymbol}} đã hoàn tất!", "mintStakeStatusLoadingTitle": "<PERSON><PERSON><PERSON> {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "<PERSON><PERSON>g tôi đang bắt đầu quy trình đúc cọc than<PERSON>n {{poolTokenSymbol}} của bạn.", "mintLiquidStakeAmountDescription": "<PERSON><PERSON><PERSON> chọn lượng SOL bạn muốn đặt cọc cùng <PERSON>to", "mintLiquidStakeAmountProvider": "<PERSON><PERSON><PERSON> cung cấp", "mintLiquidStakeAmountApy": "APY ước t<PERSON>", "mintLiquidStakeAmountBestPrice": "Giá", "mintLiquidStakeAmountInsufficientBalance": "Số dư không đủ", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} cần thiết để đặt cọc", "swapTooltipGotIt": "<PERSON><PERSON> hiểu", "swapTabInsufficientFunds": "Số tiền không đủ", "swapNoAssetsFound": "<PERSON><PERSON><PERSON><PERSON> có tài sản", "swapNoTokensFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy token nào", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON> lại", "swapConfirmationGoBack": "Quay lại", "swapNoQuotesFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy báo giá nào", "swapNotProviderFound": "<PERSON><PERSON>g tôi không thể tìm thấy một nhà cung cấp cho hoán đổi token này. H<PERSON>y thử một token khác.", "swapAvailableOnMainnet": "<PERSON><PERSON><PERSON> năng này chỉ khả dụng trên Mainnet", "swapNotAvailableEVM": "<PERSON><PERSON><PERSON> ho<PERSON> đổi chưa khả dụng cho tài khoản EVM", "swapNotAvailableOnSelectedNetwork": "<PERSON><PERSON><PERSON><PERSON> thể hoán đổi trên mạng lưới đã chọn", "singleChainSwapTab": "Trong Mạng lưới", "crossChainSwapTab": "<PERSON>ua các mạng", "allFilter": "<PERSON><PERSON><PERSON> c<PERSON>", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON> l<PERSON>", "bridgeRefuelDescription": "<PERSON><PERSON><PERSON> nhiên liệu đảm bảo bạn có thể thanh toán cho các giao dịch sau khi bạn bắt đầu cầu nối.", "bridgeRefuelLabelBalance": "{{symbol}} c<PERSON><PERSON> b<PERSON>n", "bridgeRefuelLabelReceive": "Bạn nh<PERSON>n về", "bridgeRefuelLabelFee": "Chi phí <PERSON><PERSON> t<PERSON>h", "bridgeRefuelDismiss": "<PERSON><PERSON><PERSON><PERSON> tụ<PERSON> vớ<PERSON> n<PERSON>ê<PERSON> li<PERSON>u", "bridgeRefuelEnable": "<PERSON><PERSON><PERSON> l<PERSON>", "unwrapWrappedSolError": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>i khi mở bọc", "unwrapWrappedSolLoading": "<PERSON><PERSON> mở bọc...", "unwrapWrappedSolSuccess": "<PERSON><PERSON> mở bọc", "unwrapWrappedSolViewTransaction": "<PERSON>em giao d<PERSON>ch", "dappApprovePopupSignMessage": "<PERSON><PERSON> tên vào tin nh<PERSON>n", "solanaPayFrom": "Từ", "solanaPayMessage": "<PERSON>", "solanaPayNetworkFee": "<PERSON><PERSON> mạng l<PERSON>", "solanaPayFree": "<PERSON><PERSON><PERSON> phí", "solanaPayPay": "<PERSON>h toán {{item}}", "solanaPayPayNow": "<PERSON><PERSON> to<PERSON> ngay", "solanaPaySending": "<PERSON><PERSON> g<PERSON>i {{item}}", "solanaPayReceiving": "<PERSON><PERSON> {{item}}", "solanaPayMinting": "<PERSON><PERSON> đ<PERSON> {{item}}", "solanaPayTransactionProcessing": "<PERSON><PERSON><PERSON><PERSON> của bạn đang đư<PERSON> xử lý,\nvui lòng chờ.", "solanaPaySent": "<PERSON><PERSON> gửi!", "solanaPayReceived": "Đ<PERSON> nhận!", "solanaPayMinted": "Đã đúc!", "solanaPaySentNFT": "Đã gửi NFT!", "solanaPayReceivedNFT": "Đã nhận NFT!", "solanaPayTokensSent": "Token của bạn đã được gửi đến {{to}}", "solanaPayTokensReceived": "Bạn đã nhận các token mới từ {{from}}", "solanaPayViewTransaction": "<PERSON>em giao d<PERSON>ch", "solanaPayTransactionFailed": "<PERSON><PERSON><PERSON> d<PERSON>ch đã thất bại", "solanaPayConfirm": "<PERSON><PERSON><PERSON>", "solanaPayTo": "<PERSON><PERSON><PERSON>", "dappApproveConnectViewAccount": "<PERSON><PERSON> tài k<PERSON>n <PERSON> của bạn", "deepLinkInvalidLink": "<PERSON><PERSON><PERSON> kết không h<PERSON>p lệ", "deepLinkInvalidSplTokenSubtitle": "Mã này chứa một token mà bạn không sở hữu hoặc chúng tôi không thể xác định nó.", "walletAvatarShowAllAccounts": "<PERSON><PERSON><PERSON> thị tất cả tài k<PERSON>n", "pushNotificationsGetInstantUpdates": "<PERSON><PERSON><PERSON><PERSON> cập nhật tức thì", "pushNotificationsEnablePushNotifications": "<PERSON><PERSON><PERSON> thông báo đ<PERSON>y về các giao dị<PERSON>, ho<PERSON> đổi đã hoàn tất và các công bố", "pushNotificationsEnable": "<PERSON><PERSON><PERSON>", "pushNotificationsNotNow": "Để sau", "onboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON><PERSON> đồng ý với <1><PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON>n dịch vụ</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, tôi đã lưu nó ở đâu đó", "onboardingCreateNewWallet": "Tạo ví mới", "onboardingErrorDuplicateSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> từ bí mật này đã tồn tại trong ví của bạn rồi", "onboardingErrorInvalidSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON> hợp lệ đối với cụm từ khôi phục bí mật", "onboardingFinished": "Bạn đã xong hết!", "onboardingImportAccounts": "<PERSON><PERSON><PERSON><PERSON> c<PERSON>", "onboardingImportImportingAccounts": "<PERSON><PERSON> c<PERSON> tà<PERSON>...", "onboardingImportImportingFindingAccounts": "<PERSON><PERSON> tìm các tài k<PERSON>n có hoạt động", "onboardingImportAccountsLastActive": "<PERSON><PERSON><PERSON> động {{formattedTimestamp}} trước", "onboardingImportAccountsNeverUsed": "Chưa sử dụng bao giờ", "onboardingImportAccountsCreateNew": "<PERSON><PERSON> mới", "onboardingImportAccountsDescription": "<PERSON><PERSON><PERSON> tài kho<PERSON>n ví để nhập", "onboardingImportReadOnlyAccountDescription": "Thêm một địa chỉ hoặc tên miền mà bạn muốn theo dõi. Bạn sẽ có quyền truy cập chỉ xem, và không thể ký cho các giao dịch hay tin nhắn.", "onboardingImportSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON> từ bí mật", "onboardingImportViewAccounts": "<PERSON><PERSON> t<PERSON>", "onboardingRestoreExistingWallet": "Khô<PERSON> phục một ví có sẵn với Cụm từ khôi phục bí mật dài 12 hay 24 chữ", "onboardingShowUnusedAccounts": "<PERSON><PERSON><PERSON> thị các tài khoản chưa sử dụng", "onboardingShowMoreAccounts": "<PERSON><PERSON><PERSON> thị các tài k<PERSON>n khác", "onboardingHideUnusedAccounts": "Ẩn các tài k<PERSON>n chưa sử dụng", "onboardingSecretRecoveryPhrase": "<PERSON><PERSON><PERSON> từ khôi phục bí mật", "onboardingSelectAccounts": "<PERSON><PERSON><PERSON> tài k<PERSON>n của bạn", "onboardingStoreSecretRecoveryPhraseReminder": "<PERSON><PERSON><PERSON> là cách duy nhất để có thể khôi phục tài khoản của bạn. H<PERSON>y lưu giữ nó ở nơi an toàn!", "useTokenMetasForMintsUnknownName": "<PERSON><PERSON><PERSON><PERSON> x<PERSON>", "timeUnitMinute": "<PERSON><PERSON><PERSON><PERSON>", "timeUnitMinutes": "<PERSON><PERSON><PERSON><PERSON>", "timeUnitHour": "giờ", "timeUnitHours": "giờ", "espNFTListWithPrice": "Bạn đã niêm yết {{NFTDisplayName}} với giá {{priceAmount}} {{priceTokenSymbol}} trên {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Bạn đã niêm yết {{NFTDisplayName}} với giá {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Bạn đã niêm yết {{NFTDisplayName}} để bán trên {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Bạn đã niêm yết {{NFTDisplayName}} để bán", "espNFTChangeListPriceWithPrice": "<PERSON><PERSON><PERSON> đã cập nhật niêm yết cho {{NFTDisplayName}} thành {{priceAmount}} {{priceTokenSymbol}} trên {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "<PERSON><PERSON><PERSON> đã cập nhật niêm yết cho {{NFTDisplayName}} thành {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "<PERSON><PERSON><PERSON> đã cập nhật niêm yết cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "<PERSON><PERSON><PERSON> đã cập nhật niêm yết cho {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Bạn đã chào giá {{priceAmount}} {{priceTokenSymbol}} cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Bạn đã chào giá {{priceAmount}} {{priceTokenSymbol}} cho {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Bạn đã đặt một chào mua cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Bạn đã đặt một chào mua cho {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Chào giá mới {{priceAmount}} {{priceTokenSymbol}} cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Chào giá mới {{priceAmount}} {{priceTokenSymbol}} cho {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Chào mua mới cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Chào mua mới cho {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Bạn đã hủy chào giá {{priceAmount}} {{priceTokenSymbol}} cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Bạn đã hủy chào giá {{priceAmount}} {{priceTokenSymbol}} cho {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Bạn đã hủy chào mua cho {{NFTDisplayName}} trên {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Bạn đã hủy chào mua cho {{NFTDisplayName}}", "espNFTUnlist": "Bạn đã bỏ niêm yết {{NFTDisplayName}} trên {{dAppName}}", "espNFTUnlistWithoutDApp": "Bạn đã bỏ niêm yết cho {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Bạn đã mua {{NFTDisplayName}} với giá {{priceAmount}} {{priceTokenSymbol}} trên {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Bạn đã mua {{NFTDisplayName}} với giá {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Bạn đã mua {{NFTDisplayName}} trên {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Bạn đã mua {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "B<PERSON>n đã bán {{NFTDisplayName}} với giá {{priceAmount}} {{priceTokenSymbol}} trên {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "<PERSON><PERSON>n đã bán {{NFTDisplayName}} với giá {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "B<PERSON>n đã bán {{NFTDisplayName}} trên {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "<PERSON><PERSON><PERSON> đã bán {{NFTDisplayName}}", "espDEXSwap": "Bạn đã đổi {{downTokensTextFragment}} lấy {{upTokensTextFragment}} trên {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Bạn đã nộp {{downTokensTextFragment}} vào quỹ thanh kho<PERSON>n {{poolDisplayName}} trên {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Bạn đã đổi {{downTokensTextFragment}} lấy {{upTokensTextFragment}} trên {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Bạn đã rút {{upTokensTextFragment}} khỏi quỹ thanh khoản {{poolDisplayName}} trên {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Bạn đã đổi {{downTokensTextFragment}} lấy {{upTokensTextFragment}} trên {{dAppName}}", "espGenericTokenSend": "Bạn đã gửi {{downTokensTextFragment}}", "espGenericTokenReceive": "Bạn đã nhận {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Bạn đã đổi {{downTokensTextFragment}} lấy {{upTokensTextFragment}}", "espUnknown": "KHÔNG XÁC ĐỊNH", "espUnknownNFT": "NFT không x<PERSON>c đ<PERSON>", "espTextFragmentAnd": "và", "externalLinkWarningTitle": "Bạn sắp rời khỏi Phantom", "externalLinkWarningDescription": "Và mở {{url}}. <PERSON><PERSON><PERSON> đảm bảo bạn tin tưởng nguồn này trước khi tương tác với nó.", "shortcutsWarningDescription": "<PERSON><PERSON><PERSON> tắt được cung cấp bởi {{url}}. <PERSON><PERSON><PERSON> đảm bảo bạn tin tưởng nguồn này trước khi tương tác với nó.", "lowTpsBanner": "Solana đang gặp vấn đề về nghẽn mạng", "lowTpsMessageTitle": "Nghẽn mạng <PERSON>", "lowTpsMessage": "Bởi việc nghẽn mạng <PERSON>, các giao dịch của bạn có thể thất bại hoặc bị trễ. <PERSON><PERSON> lòng thử lại các giao dịch thất bại.", "solanaSlow": "Mạng Sol<PERSON> đang bị chậm một cách bất thường", "solanaNetworkTemporarilyDown": "Mạng lưới <PERSON>ana đang tạm thời ngoại tuyến", "waitForNetworkRestart": "<PERSON><PERSON> lòng chờ mạng lưới này khởi động lại. Tiền vốn của bạn sẽ không bị ảnh hưởng.", "exploreCollectionsCarouselTitle": "<PERSON><PERSON> s<PERSON>u tập phổ biến", "exploreDropsCarouselTitle": "<PERSON>ó gì mới", "exploreSortFloor": "Sàn", "exploreSortListed": "<PERSON><PERSON> niêm y<PERSON>t", "exploreSortVolume": "<PERSON><PERSON><PERSON><PERSON>", "exploreFetchErrorSubtitle": "<PERSON><PERSON> lòng thử lại sau.", "exploreFetchErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể truy xuất.", "exploreTopCollectionsTitle": "<PERSON><PERSON><PERSON> bộ sưu tập N<PERSON> hàng đầu", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "<PERSON><PERSON><PERSON><PERSON>", "exploreSeeMore": "<PERSON><PERSON>", "exploreTrendingTokens": "Token xu h<PERSON>ng", "exploreVolumeTokens": "<PERSON><PERSON><PERSON><PERSON> lư<PERSON> lớn nhất", "explorePriceChangeTokens": "<PERSON><PERSON> tăng mạnh nhất", "explorePriceTokens": "<PERSON><PERSON> s<PERSON>ch token theo giá", "exploreMarketCapTokens": "<PERSON><PERSON><PERSON> <PERSON> hàng đầu", "exploreTrendingSites": "<PERSON><PERSON><PERSON> trang <PERSON> xu h<PERSON>ng", "exploreTopSites": "<PERSON><PERSON><PERSON> trang <PERSON> hàng đầu", "exploreTrendingCollections": "<PERSON><PERSON><PERSON> bộ sưu tập xu hướng", "exploreTopCollections": "<PERSON><PERSON><PERSON> bộ sưu tập hàng đầu", "collectiblesSearchCollectionsSection": "<PERSON><PERSON> s<PERSON>u tập", "collectiblesSearchItemsSection": "<PERSON><PERSON><PERSON>", "collectiblesSearchNrOfItems": "{{ nrOfItems }} mục", "collectiblesSearchPlaceholderText": "T<PERSON><PERSON> trong các vật sưu tầm của bạn", "collectionPinSuccess": "<PERSON><PERSON> sưu tập đã đ<PERSON><PERSON><PERSON> ghim", "collectionPinFail": "<PERSON><PERSON> sưu tập không thể đư<PERSON>c ghim", "collectionUnpinSuccess": "<PERSON><PERSON> sưu tập đã được bỏ ghim", "collectionUnpinFail": "<PERSON><PERSON> sưu tập không thể được bỏ ghim", "collectionHideSuccess": "<PERSON><PERSON> sưu tập đã bị <PERSON>n", "collectionHideFail": "<PERSON><PERSON> sưu tập không thể bị ẩn", "collectionUnhideSuccess": "<PERSON><PERSON> sưu tập đã được bỏ ẩn", "collectionUnhideFail": "<PERSON><PERSON> sưu tập không thể được bỏ ẩn", "collectiblesSpamSuccess": "Đã báo cáo là rác", "collectiblesSpamFail": "Báo cáo là rác không thành công", "collectiblesSpamAndHiddenSuccess": "Báo cáo là rác và ẩn", "collectiblesNotSpamSuccess": "Đã báo cáo là không phải rác", "collectiblesNotSpamFail": "<PERSON>hông thành công trong việ<PERSON> báo cáo là không phải rác", "collectiblesNotSpamAndUnhiddenSuccess": "<PERSON>ã báo cáo là không phải rác và được bỏ ẩn", "tokenPageSpamWarning": "Token này chưa được xác minh. Hãy chỉ tương tác với các token mà bạn tin tưởng.", "tokenSpamWarning": "Token này đã bị ẩn bởi Phantom tin rằng nó là vật phẩm rác.", "collectibleSpamWarning": "<PERSON><PERSON>t sưu tầm này đã bị ẩn bởi Phantom tin rằng nó là vật phẩm rác.", "collectionSpamWarning": "<PERSON><PERSON><PERSON> vật sưu tầm này đã bị ẩn bởi Phantom tin rằng chúng là vật phẩm rác.", "emojiNoResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy emoji nào", "emojiSearchResults": "<PERSON><PERSON><PERSON> kết quả", "emojiSuggested": "<PERSON><PERSON> đề xuất", "emojiSmileys": "Mặt cư<PERSON><PERSON> & <PERSON><PERSON><PERSON> người", "emojiAnimals": "Động vật & <PERSON><PERSON><PERSON><PERSON>", "emojiFood": "<PERSON><PERSON><PERSON><PERSON> p<PERSON> & <PERSON><PERSON>ng", "emojiTravel": "<PERSON> & <PERSON><PERSON><PERSON>", "emojiActivities": "<PERSON><PERSON><PERSON> đ<PERSON>", "emojiObjects": "<PERSON><PERSON> vật", "emojiSymbols": "<PERSON><PERSON><PERSON><PERSON>", "emojiFlags": "Cờ", "whichExtensionToConnectWith": "Bạn muốn kết nối với phần mở rộng nào?", "configureInSettings": "<PERSON><PERSON> thể cấu hình trong Cài đặt → Ứng dụng ví mặc định.", "continueWith": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> v<PERSON>i", "useMetaMask": "Sử dụng <PERSON>", "usePhantom": "Sử dụng Phantom", "alwaysAsk": "Luôn hỏi", "dontAskMeAgain": "Không hỏi lại", "selectWalletSettingDescriptionLine1": "Một số ứng dụng có thể không đề xuất một tùy chọn để kết nối với Phantom.", "selectWalletSettingDescriptionLinePhantom": "<PERSON><PERSON> k<PERSON>h<PERSON>, c<PERSON><PERSON> kết nối với MetaMask sẽ luôn mở ra Phantom.", "selectWalletSettingDescriptionLineAlwaysAsk": "<PERSON><PERSON> khắ<PERSON>h<PERSON>, khi bạn kết nối với <PERSON>, chúng tôi sẽ hỏi liệu bạn có muốn dùng Phantom thay thế không.", "selectWalletSettingDescriptionLineMetaMask": "Việc đặt MetaMask làm tùy chọn mặc định sẽ vô hiệu các dapps đó khỏi kết nối với Phantom.", "metaMaskOverride": "Ứng dụng ví mặc định", "metaMaskOverrideSettingDescriptionLine1": "<PERSON><PERSON> kết nối với các website không đề xuất một tùy chọn sử dụng Phantom.", "refreshAndReconnectToast": "<PERSON><PERSON><PERSON> mới và kết nối lại để áp dụng các thay đổi của bạn", "autoConfirmUnavailable": "<PERSON><PERSON><PERSON><PERSON> khả dụng", "autoConfirmReasonDappNotWhitelisted": "Không khả dụng bởi hợp đồng cung cấp nó không nằm trong danh sách cho phép của chúng tôi cho ứng dụng này.", "autoConfirmReasonSessionNotActive": "Không khả dụng bởi không có phiên tự động xác nhận nào đang hoạt động. <PERSON><PERSON> lòng bật nó dưới đây.", "autoConfirmReasonRateLimited": "<PERSON>hông khả dụng bởi dapp bạn sử dụng đang gửi quá nhiều yêu cầu.", "autoConfirmReasonUnsupportedNetwork": "<PERSON>hông khả dụng bởi tự động xác nhận chưa hỗ trợ mạng lưới này.", "autoConfirmReasonSimulationFailed": "<PERSON>hông khả dụng bởi chúng tôi không thể đảm bảo sự bảo mật.", "autoConfirmReasonTabNotFocused": "<PERSON>hông khả dụng bởi tab của tên miền bạn đang cố xác nhận tự động không hoạt động.", "autoConfirmReasonNotUnlocked": "Không khả dụng bởi ví chưa được mở khóa.", "rpcErrorUnauthorizedWrongAccount": "<PERSON><PERSON><PERSON> d<PERSON>ch từ địa chỉ này không khớp với địa chỉ tài khoản đã chọn.", "rpcErrorUnauthorizedUnknownSource": "<PERSON><PERSON><PERSON><PERSON> thể xác định nguồn yêu cầu RPC.", "transactionsDisabledTitle": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> đã bị vô hiệu", "transactionsDisabledMessage": "<PERSON><PERSON>a chỉ của bạn không thể được giao dịch bằng Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "<PERSON><PERSON><PERSON> đ<PERSON>", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL đã được sao chép vào bộ nhớ tạm", "notEnoughSolScanTransactionWarning": "<PERSON><PERSON>o dịch này có thể sẽ không thành công bởi không có đủ SOL trong tài khoản của bạn. <PERSON><PERSON> lòng bổ sung thêm SOL vào tài khoản của bạn và thử lại.", "transactionRevertedWarning": "<PERSON><PERSON><PERSON> dịch này đã không thành công trong quá trình giả lập. Ti<PERSON><PERSON> có thể sẽ bị mất nếu được gửi đi.", "slippageToleranceExceeded": "<PERSON><PERSON><PERSON> d<PERSON><PERSON> này đã bị hoàn tác trong quá trình giả lập. <PERSON><PERSON> vư<PERSON><PERSON> quá mức dung sai trượt giá.", "simulationWarningKnownMalicious": "<PERSON><PERSON>g tôi tin rằng đây là một tài khoản độc hại. Việc phê duyệt cho giao dịch này sẽ khiến bạn mất tiền.", "simulationWarningPoisonedAddress": "Địa chỉ này gần giống một cách khả nghi với một địa chỉ mà bạn đã gửi tiền đến gần đây. Vui lòng xác nhận rằng đây là địa chỉ chính xác để tránh mất tiền do lừa đảo.", "simulationWarningInteractingWithAccountWithoutActivity": "<PERSON><PERSON>y là một địa chỉ chưa có bất kỳ hoạt động nào trước đây. Việc gửi tiền đến một tài khoản không tồn tại có thể làm mất tiền", "quests": "Nhiệm vụ", "questsClaimInProgress": "<PERSON><PERSON>", "questsVerifyingCompletion": "<PERSON><PERSON> x<PERSON>c minh hoàn thành nhi<PERSON>m vụ", "questsClaimError": "Lỗi khi nhận thưởng", "questsClaimErrorDescription": "<PERSON><PERSON> xảy ra lỗi khi nhận phần thưởng của bạn. <PERSON><PERSON> lòng thử lại sau.", "questsBadgeMobileOnly": "Chỉ di động", "questsBadgeExtensionOnly": "Chỉ mở rộng", "questsExplainerSheetButtonLabel": "<PERSON><PERSON> hiểu", "questsNoQuestsAvailable": "<PERSON><PERSON><PERSON><PERSON> có nhiệm vụ nào khả dụng", "questsNoQuestsAvailableDescription": "<PERSON><PERSON><PERSON> không có nhiệm vụ nào khả dụng. <PERSON>úng tôi sẽ thông báo ngay cho bạn khi các nhiệm vụ mới được bổ sung.", "exploreLearn": "<PERSON><PERSON><PERSON> hi<PERSON>u thêm", "exploreSites": "Website", "exploreTokens": "Token", "exploreQuests": "Nhiệm vụ", "exploreCollections": "<PERSON><PERSON> s<PERSON>u tập", "exploreFilterByall_networks": "<PERSON><PERSON><PERSON> cả các mạng lư<PERSON>i", "exploreSortByrank": "<PERSON>", "exploreSortBytrending": "<PERSON>", "exploreSortByprice": "Giá", "exploreSortByprice-change": "Thay đổi giá", "exploreSortBytop": "<PERSON><PERSON><PERSON>", "exploreSortByvolume": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBygainers": "Tăng giá", "exploreSortBylosers": "G<PERSON>ảm giá", "exploreSortBymarket-cap": "<PERSON><PERSON><PERSON> hóa thị trường", "exploreSortBymarket_cap": "<PERSON><PERSON><PERSON> hóa thị trường", "exploreTimeFrame1h": "1h", "exploreTimeFrame24h": "24h", "exploreTimeFrame7d": "7ng", "exploreTimeFrame30d": "30ng", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "<PERSON><PERSON><PERSON><PERSON><PERSON> tầm", "exploreCategoryMarketplace": "<PERSON>ợ điện tử", "exploreCategoryGaming": "Game", "exploreCategoryBridges": "<PERSON><PERSON><PERSON>", "exploreCategoryOther": "K<PERSON><PERSON><PERSON>", "exploreCategorySocial": "<PERSON><PERSON> h<PERSON>i", "exploreCategoryCommunity": "<PERSON><PERSON>ng đồng", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Đặt cọc", "exploreCategoryArt": "<PERSON><PERSON><PERSON>", "exploreCategoryTools": "<PERSON><PERSON><PERSON> cụ", "exploreCategoryDeveloperTools": "<PERSON><PERSON>ng cụ nhà phát triển", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Đặt cọc NFT", "exploreCategoryExplorer": "<PERSON><PERSON><PERSON><PERSON> kh<PERSON>m phá", "exploreCategoryInscriptions": "Khắc ghi", "exploreCategoryBridge": "<PERSON><PERSON><PERSON>", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "<PERSON><PERSON><PERSON>", "exploreCategoryPoints": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryQuests": "Nhiệm vụ", "exploreCategoryShop": "<PERSON><PERSON><PERSON> h<PERSON>", "exploreCategoryProtocol": "<PERSON><PERSON><PERSON> th<PERSON>", "exploreCategoryNamingService": "<PERSON><PERSON><PERSON> vụ đặt tên", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "<PERSON><PERSON><PERSON><PERSON> theo dõi danh mục", "exploreCategoryFitness": "<PERSON><PERSON><PERSON> chất", "exploreCategoryDePIN": "DePIN", "exploreVolume": "<PERSON><PERSON><PERSON><PERSON>", "exploreFloor": "Sàn", "exploreCap": "<PERSON><PERSON><PERSON> hóa thị trường", "exploreToken": "Token", "explorePrice": "Giá", "explore24hVolume": "<PERSON><PERSON><PERSON><PERSON> 24h", "exploreErrorButtonText": "<PERSON><PERSON><PERSON> lại", "exploreErrorDescription": "<PERSON><PERSON> có lỗi xảy ra khi cố gắng tải nội dung khám phá. Vui lòng làm mới và thử lại", "exploreErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể tải nội dung khám phá", "exploreNetworkError": "<PERSON><PERSON> xảy ra lỗi mạng. <PERSON><PERSON> lòng thử lại sau.", "exploreTokensLegalDisclaimer": "<PERSON>h sách token được tạo bằng dữ liệu thị trường được cung cấp bởi nhiều bên thứ ba khác nhau bao gồ<PERSON>, Birdeye và Jupiter. Hiệu suất được tính dựa trên khoảng thời gian 24 tiếng trước đó. Hiệu suất trong quá khứ không chỉ báo hiệu suất trong tương lai.", "swapperTokensLegalDisclaimer": "<PERSON><PERSON> sách token xu hướng được tạo bằng dữ liệu thị trường từ nhiều bên thứ ba khác nhau bao gồ<PERSON>, Birdeye và Jupiter và được tính dựa trên các token phổ biến được hoán đổi bởi người dùng Phantom qua Swapper trong khoảng thời gian đã nêu. Hiệu suất trong quá khứ không chỉ báo hiệu suất trong tương lai.", "exploreLearnErrorTitle": "<PERSON><PERSON><PERSON><PERSON> thể tải nội dung học tập", "exploreLearnErrorDescription": "<PERSON><PERSON> có lỗi xảy ra khi cố gắng tải nội dung học tập. Vui lòng làm mới và thử lại", "exploreShowMore": "<PERSON><PERSON><PERSON> thị thêm", "exploreShowLess": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t", "exploreVisitSite": "<PERSON><PERSON><PERSON> c<PERSON>p trang web", "dappBrowserSearchScreenVisitSite": "Truy c<PERSON>p website", "dappBrowserSearchScreenSearchWithGoogle": "Tìm với Google", "dappBrowserSearchScreenSearchLinkYouCopied": "<PERSON><PERSON><PERSON> kết bạn đã sao chép", "dappBrowserExtSearchPlaceholder": "T<PERSON><PERSON> ki<PERSON>m c<PERSON>c trang web, token", "dappBrowserSearchNoAppsTokens": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ứng dụng hay token nào", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON><PERSON><PERSON> các tab cũ hơn?", "dappBrowserTabsLimitExceededScreenDescription": "Bạn có {{tabsCount}} tab đang mở. Đ<PERSON> mở thêm, bạn sẽ cần đóng một số tab.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON><PERSON> tất cả các tab", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: <PERSON><PERSON><PERSON> miền này không tồn tại", "dappBrowserTabErrorHttp": "<PERSON><PERSON> bị chặn, vui lòng sử dụng HTTPS", "dappBrowserTabError401Unauthorized": "401 <PERSON>hông đ<PERSON><PERSON><PERSON> quyền", "dappBrowserTabError501UnhandledRequest": "501 Yêu cầu không đ<PERSON><PERSON><PERSON> xử lý", "dappBrowserTabErrorTimeout": "HẾT THỜI GIAN CHỜ: <PERSON><PERSON><PERSON> chủ không hồi đáp trong thời gian dài", "dappBrowserTabErrorInvalidResponse": "<PERSON><PERSON><PERSON> đ<PERSON> kh<PERSON>ng hợp lệ", "dappBrowserTabErrorEmptyResponse": "<PERSON><PERSON><PERSON> đ<PERSON>p rỗng", "dappBrowserTabErrorGeneric": "Đ<PERSON> xảy ra lỗi", "localizedErrorUnknownError": "<PERSON><PERSON> xảy ra lỗi, vui lòng thử lại sau.", "localizedErrorUnsupportedCountry": "<PERSON><PERSON><PERSON> tôi rất ti<PERSON>, quốc gia của bạn hiện không được hỗ trợ.", "localizedErrorTokensNotLoading": "<PERSON><PERSON> xảy ra lỗi khi tải các token của bạn. <PERSON><PERSON> lòng thử lại.", "localizedErrorSwapperNoQuotes": "<PERSON>hông thể hoán đổi do cặp không được hỗ trợ, <PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> thấp, hoặc số tiền thấp. <PERSON><PERSON><PERSON> thử điều chỉnh token hoặc số tiền.", "localizedErrorSwapperRefuelNoQuotes": "<PERSON><PERSON><PERSON><PERSON> tìm thấy báo giá nào. <PERSON><PERSON><PERSON> thử một token, số tiền kh<PERSON>, hoặc tắt nạp nhiên liệu.", "localizedErrorInsufficientSellAmount": "<PERSON>ố tiền token quá thấp. <PERSON><PERSON><PERSON> tăng giá trị để hoán đổi Chuỗi chéo.", "localizedErrorCrossChainUnavailable": "<PERSON><PERSON><PERSON> năng hoán đổi chuỗi chéo hiện không khả dụng, vui lòng thử lại sau.", "localizedErrorTokenNotTradable": "Một token đã chọn không thể được giao dịch. <PERSON><PERSON> lòng chọn một token khác.", "localizedErrorCollectibleLocked": "<PERSON><PERSON><PERSON> token bị kh<PERSON>a.", "localizedErrorCollectibleListed": "<PERSON><PERSON><PERSON> token đã đ<PERSON><PERSON><PERSON> niêm yết.", "spamActivityAction": "<PERSON><PERSON> c<PERSON> m<PERSON> b<PERSON>", "spamActivityTitle": "<PERSON><PERSON><PERSON> đ<PERSON> b<PERSON>", "spamActivityWarning": "Giao dịch này đã bị ẩn bởi Phantom tin rằng nó là giao dịch rác.", "appAuthenticationFailed": "<PERSON><PERSON><PERSON><PERSON> thể xác thực", "appAuthenticationFailedDescription": "<PERSON><PERSON> xảy ra vấn đề với nỗ lực xác thực của bạn. <PERSON><PERSON> lòng thử lại.", "partialErrorBalanceChainName": "<PERSON><PERSON>g tôi đang gặp vấn đề khi cập nhật số dư {{chainName}} của bạn. Tiền của bạn vẫn an toàn.", "partialErrorGeneric": "<PERSON>úng tôi đang gặp vấn đề khi cập nhật mạng lưới, một số thông tin về số dư và giá cả token của bạn có thể đang bị lỗi thời. Tiền của bạn vẫn an toàn.", "partialErrorTokenDetail": "<PERSON><PERSON>g tôi đang gặp vấn đề khi cập nhật số dư token của bạn. Tiền của bạn vẫn an toàn.", "partialErrorTokenPrices": "<PERSON><PERSON>g tôi đang gặp vấn đề khi cập nhật giá token của bạn. Tiền của bạn vẫn an toàn.", "partialErrorTokensTrimmed": "<PERSON>úng tôi đang gặp vấn đề với việc hiển thị tất cả các token trong danh mục của bạn. Tiền của bạn vẫn an toàn.", "publicFungibleDetailAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "publicFungibleDetailYourBalance": "Số dư của bạn", "publicFungibleDetailInfo": "Thông tin", "publicFungibleDetailShowMore": "<PERSON><PERSON><PERSON> thị thêm", "publicFungibleDetailShowLess": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>t", "publicFungibleDetailPerformance": "<PERSON><PERSON><PERSON> quả 24 giờ", "publicFungibleDetailSecurity": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMarketCap": "<PERSON><PERSON><PERSON> hóa thị trường", "publicFungibleDetailTotalSupply": "<PERSON><PERSON>ng lượng cung", "publicFungibleDetailCirculatingSupply": "<PERSON><PERSON><PERSON><PERSON> cung đang lưu hành", "publicFungibleDetailMaxSupply": "<PERSON><PERSON><PERSON><PERSON> cung tối đa", "publicFungibleDetailHolders": "<PERSON><PERSON><PERSON><PERSON> n<PERSON> giữ", "publicFungibleDetailVolume": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailTrades": "<PERSON><PERSON><PERSON>", "publicFungibleDetailTraders": "<PERSON>hà giao d<PERSON>ch", "publicFungibleDetailUniqueWallets": "Ví k<PERSON>c nhau", "publicFungibleDetailTop10Holders": "Top 10 người nắm giữ", "publicFungibleDetailTop10HoldersTooltip": "<PERSON><PERSON><PERSON><PERSON> thị phần trăm tổng lượng cung hiện được nắm giữ bởi 10 người nắm giữ nhiều nhất cho token này. <PERSON><PERSON><PERSON> là một thước đo về mức độ dễ thao túng giá cả.", "publicFungibleDetailMintable": "<PERSON><PERSON> thể đúc", "publicFungibleDetailMintableTooltip": "Nguồn cung token có thể được tăng thêm bởi chủ hợp đồng nếu token đó có thể đúc được.", "publicFungibleDetailMutableInfo": "Thông tin có thể thay đổi", "publicFungibleDetailMutableInfoTooltip": "<PERSON><PERSON><PERSON> thông tin token chẳng hạn như tên, logo, và địa chỉ trang web là thay đổi được, nó có thể được thay đổi bởi chủ hợp đồng.", "publicFungibleDetailOwnershipRenounced": "<PERSON><PERSON> từ bỏ quyền sở hữu", "publicFungibleDetailOwnershipRenouncedTooltip": "<PERSON><PERSON><PERSON> quyền sở hữu token bị thu hồi, không ai có thể thực thi các chức năng như đúc thêm token.", "publicFungibleDetailUpdateAuthority": "<PERSON><PERSON><PERSON> nh<PERSON><PERSON> quyền hạn", "publicFungibleDetailUpdateAuthorityTooltip": "<PERSON><PERSON><PERSON><PERSON> cập nhật là địa chỉ ví có thể thay đổi thông tin nếu thông tin token là thay đổi được.", "publicFungibleDetailFreezeAuthority": "<PERSON><PERSON><PERSON><PERSON> đ<PERSON>g b<PERSON>ng", "publicFungibleDetailFreezeAuthorityTooltip": "Quyền đóng băng là địa chỉ ví có thể ngăn tiền khỏi bị chuyển đi.", "publicFungibleUnverifiedToken": "Token này chưa được xác minh. Hãy chỉ tương tác với các token mà bạn tin tưởng.", "publicFungibleDetailSwap": "<PERSON><PERSON> đ<PERSON> {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "<PERSON><PERSON> đổi {{tokenSymbol}} v<PERSON>i <PERSON>ng dụng Phantom", "publicFungibleDetailLinkCopied": "Đã sao chép vào bộ nhớ tạm", "publicFungibleDetailContract": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMint": "<PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "<PERSON><PERSON><PERSON> đ<PERSON>", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON><PERSON>", "unifiedTokenDetailTransactionActivityError": "<PERSON><PERSON><PERSON><PERSON> thể tải hoạt động gần đây", "additionalNetworksTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON> sung", "copyAddressRowAdditionalNetworks": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON> sung", "copyAddressRowAdditionalNetworksHeader": "<PERSON><PERSON><PERSON> mạng lưới sau đây sử dụng cùng địa chỉ với Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Bạn có thể sử dụng địa chỉ Ethereum của mình một cách an toàn để gửi và nhận tài sản trên bất kỳ mạng lưới nào.", "cpeUnknownError": "Lỗi không xác định", "cpeUnknownInstructionError": "Lỗi hướng dẫn không xác định", "cpeAccountFrozen": "<PERSON><PERSON><PERSON> k<PERSON>n đã bị đóng băng", "cpeAssetFrozen": "<PERSON><PERSON><PERSON> sản bị đóng băng", "cpeInsufficientFunds": "<PERSON><PERSON><PERSON><PERSON> đủ tiền", "cpeInvalidAuthority": "<PERSON><PERSON><PERSON><PERSON> không hợp lệ", "cpeBalanceBelowRent": "<PERSON><PERSON> dư ở dưới ngưỡng miễn thuê", "cpeNotApprovedForConfidentialTransfers": "<PERSON><PERSON><PERSON> kho<PERSON>n không đư<PERSON><PERSON> phê duyệt để chuyển đi bí mật", "cpeNotAcceptingDepositsOrTransfers": "<PERSON><PERSON><PERSON> khoản không chấp nhận tiền nộp hoặc chuyển khoản", "cpeNoMemoButRequired": "<PERSON><PERSON><PERSON><PERSON> có bản ghi nhớ trong hướng dẫn trước; vốn là cần thiết để người nhận có thể nhận tiền chuyển khoản", "cpeTransferDisabledForMint": "Chuyển đi bị tắt cho lần đ<PERSON>c này", "cpeDepositAmountExceedsLimit": "<PERSON><PERSON> tiền nộp đã vư<PERSON>t quá giới hạn tối đa", "cpeInsufficientFundsForRent": "<PERSON><PERSON>ông đủ tiền để thuê", "reportIssueScreenTitle": "<PERSON><PERSON>o c<PERSON>o một vấn đề", "publicFungibleReportIssuePrompt": "Bạn muốn báo cáo vấn đề nào về {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "Thông tin không chính xác", "publicFungibleReportIssuePriceStale": "<PERSON><PERSON><PERSON> kh<PERSON>ng đ<PERSON><PERSON><PERSON> cậ<PERSON> nh<PERSON>t", "publicFungibleReportIssuePriceMissing": "<PERSON><PERSON><PERSON> bị thi<PERSON>u", "publicFungibleReportIssuePerformanceIncorrect": "<PERSON>ệu quả 24 gi<PERSON> không ch<PERSON>h xác", "publicFungibleReportIssueLinkBroken": "<PERSON><PERSON><PERSON> kết mạng xã hội không truy cập đ<PERSON>", "publicFungibleDetailErrorLoading": "<PERSON><PERSON> liệu về token không có sẵn", "reportUserPrompt": "Bạn muốn báo cáo vấn đề nào về @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "<PERSON><PERSON><PERSON> d<PERSON> và quấy rối", "reportUserOptionAbuseAndHarrassmentDescription": "<PERSON>u<PERSON><PERSON> rối có mụ<PERSON> t<PERSON>, kích động qu<PERSON><PERSON> rối, đe d<PERSON><PERSON> b<PERSON><PERSON> l<PERSON>, n<PERSON><PERSON> dung nhắc đến sự thù địch", "reportUserOptionPrivacyAndImpersonationTitle": "<PERSON><PERSON><PERSON><PERSON> riêng tư và mạo danh", "reportUserOptionPrivacyAndImpersonationDescription": "Chia sẻ hoặc đe dọa tiết lộ thông tin riêng tư, gi<PERSON> vờ là một người khác", "reportUserOptionSpamTitle": "<PERSON><PERSON><PERSON>", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON>, l<PERSON><PERSON> đ<PERSON><PERSON>, li<PERSON><PERSON> kế<PERSON> đ<PERSON><PERSON> hại", "reportUserSuccess": "<PERSON><PERSON><PERSON> cáo người dùng đã đư<PERSON>c gửi đi.", "settingsClaimUsernameTitle": "<PERSON><PERSON><PERSON> ng<PERSON> dùng", "settingsClaimUsernameDescription": "<PERSON><PERSON><PERSON> tên duy nhất như ví của bạn", "settingsClaimUsernameValueProp1": "<PERSON><PERSON> t<PERSON>h đơn g<PERSON>n", "settingsClaimUsernameValueProp1Description": "<PERSON>ào tạm biệt các địa chỉ dài dòng phức tạp và làm quen với một danh tính thân thiện với người dùng", "settingsClaimUsernameValueProp2": "<PERSON><PERSON><PERSON> & <PERSON><PERSON> dàng hơn", "settingsClaimUsernameValueProp2Description": "<PERSON><PERSON> dàng gửi và nhận tiền mã hóa, đă<PERSON> nhập vào ví của bạn, và kết nối với bạn bè", "settingsClaimUsernameValueProp3": "<PERSON><PERSON> tr<PERSON> bộ", "settingsClaimUsernameValueProp3Description": "Kết nối bất kỳ tài khoản nào với tên người dùng của bạn và nó sẽ được đồng bộ trên tất cả các thiết bị của bạn", "settingsClaimUsernameHelperText": "<PERSON><PERSON><PERSON> riêng cho <PERSON><PERSON>n Phantom c<PERSON><PERSON> bạn", "settingsClaimUsernameValidationDefault": "Tên người dùng này không thể được thay đổi sau này", "settingsClaimUsernameValidationAvailable": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i dùng này là khả dụng", "settingsClaimUsernameValidationUnavailable": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i dùng này không khả dụng", "settingsClaimUsernameValidationServerError": "<PERSON>h<PERSON>ng thể kiểm tra rằng tên người dùng này có khả dụng hay không, vui lòng thử lại sau", "settingsClaimUsernameValidationErrorLine1": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON> dùng không hợp lệ.", "settingsClaimUsernameValidationErrorLine2": "Tên người dùng phải dài từ {{minChar}} đến {{maxChar}} ký tự và chỉ có thể chứa các chữ cái và số.", "settingsClaimUsernameValidationLoading": "<PERSON><PERSON><PERSON> tra liệu tên người dùng này có khả dụng hay không...", "settingsClaimUsernameSaveAndContinue": "Lư<PERSON> & t<PERSON><PERSON><PERSON> tục", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON><PERSON> hình đạ<PERSON> di<PERSON>n", "settingsClaimUsernameAnonymousAuthTitle": "<PERSON><PERSON><PERSON> th<PERSON><PERSON> danh", "settingsClaimUsernameAnonymousAuthDescription": "<PERSON><PERSON><PERSON> nh<PERSON>p v<PERSON><PERSON> Phantom của bạn một cách ẩn danh với một chữ ký", "settingsClaimUsernameAnonymousAuthBadge": "<PERSON><PERSON><PERSON> hi<PERSON><PERSON> c<PERSON>ch thức hoạt động", "settingsClaimUsernameLinkWalletsTitle": "<PERSON>ên kết với ví của bạn", "settingsClaimUsernameLinkWalletsDescription": "<PERSON><PERSON><PERSON> các ví được hiển thị trên các thiết bị khác với tên người dùng của bạn", "settingsClaimUsernameLinkWalletsBadge": "<PERSON><PERSON><PERSON><PERSON> hiển thị công khai", "settingsClaimUsernameConnectAccountsTitle": "<PERSON><PERSON><PERSON> n<PERSON>i tài <PERSON>n", "settingsClaimUsernameConnectAccountsHelperText": "Mỗi địa chỉ chuỗi đều sẽ được kết nối với tên người dùng của bạn. Bạn có thể thay đổi các địa chỉ này sau.", "settingsClaimUsernameContinue": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameCreateUsername": "<PERSON><PERSON><PERSON> ng<PERSON> dùng", "settingsClaimUsernameCreating": "<PERSON><PERSON> tạo tên người dùng...", "settingsClaimUsernameSuccess": "Tên người dùng đã đư<PERSON>c tạo!", "settingsClaimUsernameError": "<PERSON>úng tôi đã gặp phải lỗi khi tạo tên người dùng của bạn", "settingsClaimUsernameTryAgain": "<PERSON><PERSON><PERSON> lại", "settingsClaimUsernameSuccessHelperText": "<PERSON><PERSON><PERSON> đây bạn có thể sử dụng tên người dùng mới của mình trong tất cả các ví Phantom của bạn", "settingsClaimUsernameSettingsSyncedTitle": "Cài đặt được đồng bộ", "settingsClaimUsernameSettingsSyncedHelperText": "<PERSON>ào tạm biệt các địa chỉ dài dòng phức tạp và làm quen với một danh tính thân thiện với người dùng", "settingsClaimUsernameSendToUsernameTitle": "<PERSON><PERSON><PERSON> cho Tên ng<PERSON>ời dùng", "settingsClaimUsernameSendToUsernameHelperText": "<PERSON><PERSON> dàng gửi và nhận tiền mã hóa, đă<PERSON> nhập vào ví của bạn, và kết nối với bạn bè", "settingsClaimUsernameManageAddressesTitle": "Địa chỉ công khai", "settingsClaimUsernameManageAddressesHelperText": "Mọi token hoặc vật sưu tầm được gửi đến tên người dùng của bạn đều sẽ được gửi đến các địa chỉ này", "settingsClaimUsernameManageAddressesBadge": "<PERSON><PERSON><PERSON> thị công khai", "settingsClaimUsernameEditAddressesTitle": "<PERSON><PERSON><PERSON><PERSON> lý địa chỉ công khai", "settingsClaimUsernameEditAddressesHelperText": "Mọi token hoặc vật sưu tầm được gửi đến tên người dùng của bạn đều sẽ được gửi đến các địa chỉ này. Chọn một địa chỉ cho mỗi chuỗi.", "settingsClaimUsernameEditAddressesError": "Chỉ một địa chỉ cho mỗi mạng lưới là được cho phép.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON><PERSON><PERSON> địa chỉ", "settingsClaimUsernameNoAddressesSaved": "<PERSON>ưa có địa chỉ công khai nào đư<PERSON><PERSON> lưu", "settingsClaimUsernameSave": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameDone": "<PERSON><PERSON>", "settingsClaimUsernameWatching": "<PERSON><PERSON> the<PERSON> d<PERSON>i", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} t<PERSON><PERSON>", "settingsClaimUsernameNoOfAccountsSingular": "1 Tài khoản", "settingsClaimUsernameEmptyAccounts": "<PERSON><PERSON><PERSON><PERSON> có tài kho<PERSON>n nào", "settingsClaimUsernameSettingTitle": "Tạo @tên-người-dùng của riêng bạn", "settingsClaimUsernameSettingDescription": "<PERSON><PERSON><PERSON> tên duy nhất cho ví của bạn", "settingsManageUserProfileAbout": "<PERSON><PERSON><PERSON><PERSON> thi<PERSON>u", "settingsManageUserProfileAboutUsername": "<PERSON><PERSON><PERSON> dùng", "settingsManageUserProfileAboutBio": "<PERSON><PERSON><PERSON><PERSON> sử", "settingsManageUserProfileTitle": "<PERSON><PERSON><PERSON><PERSON> lý hồ sơ", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON><PERSON> lý", "settingsManageUserProfileAuthFactors": "<PERSON><PERSON><PERSON> tố xác thực", "settingsManageUserProfileAuthFactorsDescription": "<PERSON><PERSON><PERSON> cụm từ hạt giống hoặc khóa riêng nào có thể đăng nhập vào T<PERSON><PERSON>n Phantom củ<PERSON> bạn.", "settingsManageUserProfileUpdateAuthFactorsToast": "Yếu tố xác thực đã được cập nhật!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật yếu tố xác thực!", "settingsManageUserProfileBiography": "<PERSON><PERSON><PERSON> sử", "settingsManageUserProfileBiographyDescription": "<PERSON><PERSON><PERSON><PERSON> một tiểu sử ngắn vào hồ sơ của bạn", "settingsManageUserProfileUpdateBiographyToast": "Tiểu sử đã đư<PERSON>c cập nhật!", "settingsManageUserProfileUpdateBiographyToastFailure": "<PERSON><PERSON> xảy ra một vấn đề nào đó. <PERSON><PERSON><PERSON> thử lại", "settingsManageUserProfileBiographyNoUrlMessage": "<PERSON><PERSON> lòng xóa tất cả URL khỏi tiểu sử của bạn", "settingsManageUserProfileLinkedWallets": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> liên kết", "settingsManageUserProfileLinkedWalletsDescription": "<PERSON><PERSON><PERSON> các ví hiển thị trên những thiết bị khác khi đăng nhập và<PERSON> T<PERSON><PERSON> k<PERSON>n Phantom củ<PERSON> bạn.", "settingsManageUserProfileUpdateLinkedWalletsToast": "<PERSON><PERSON> cập nhật các ví được liên kết!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "<PERSON><PERSON><PERSON>ng thể cập nhật các ví đã liên kết!", "settingsManageUserProfilePrivacy": "<PERSON><PERSON><PERSON><PERSON> riêng tư", "settingsManageUserProfileUpdatePrivacyStateToast": "<PERSON>uyền riêng tư đã đư<PERSON><PERSON> cập nhật!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "<PERSON><PERSON><PERSON><PERSON> thể cập nhật quyền riêng tư!", "settingsManageUserProfilePublicAddresses": "Địa chỉ công khai", "settingsManageUserProfileUpdatePublicAddressToast": "Địa chỉ công khai đã được cập nhật!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "<PERSON>hông thể cập nhật địa chỉ công khai!", "settingsManageUserProfilePrivacyStatePublic": "<PERSON><PERSON><PERSON> khai", "settingsManageUserProfilePrivacyStatePublicDescription": "<PERSON><PERSON> sơ và các địa chỉ công khai của bạn được hiển thị và có thể được tìm thấy bởi bất cứ ai", "settingsManageUserProfilePrivacyStatePrivate": "<PERSON><PERSON><PERSON><PERSON> tư", "settingsManageUserProfilePrivacyStatePrivateDescription": "<PERSON><PERSON> sơ của bạn có thể được tìm thấy bởi bất cứ ai, nhưng những người khác phải yêu cầu quyền để xem hồ sơ và các địa chỉ công khai của bạn", "settingsManageUserProfilePrivacyStateInvisible": "<PERSON><PERSON>", "settingsManageUserProfilePrivacyStateInvisibleDescription": "<PERSON><PERSON> sơ và các địa chỉ công khai của bạn được ẩn và không thể được tìm thấy ở bất cứ đâu", "settingsDownloadPhantom": "<PERSON><PERSON><PERSON> về <PERSON>", "settingsLogOut": "<PERSON><PERSON><PERSON> xu<PERSON>", "seedlessAddAWalletPrimaryText": "<PERSON><PERSON><PERSON><PERSON> một ví", "seedlessAddAWalletSecondaryText": "<PERSON><PERSON><PERSON> nhập hoặc nhập một ví hiện có ", "seedlessAddSeedlessWalletPrimaryText": "Thê<PERSON> ví không hạt giống", "seedlessAddSeedlessWalletSecondaryText": "Sử dụng Apple ID, Google hoặc Email của bạn", "seedlessCreateNewWalletPrimaryText": "Tạo ví mới?", "seedlessCreateNewWalletSecondaryText": "Email này không có ví nào, bạn có muốn tạo một ví không?", "seedlessCreateNewWalletButtonText": "Tạo ví", "seedlessCreateNewWalletNoBundlePrimaryText": "<PERSON><PERSON><PERSON><PERSON> tìm thấy ví", "seedlessCreateNewWalletNoBundleSecondaryText": "Email này không có ví nào", "seedlessCreateNewWalletNoBundleButtonText": "Quay lại", "seedlessEmailOptionsPrimaryText": "<PERSON><PERSON>n email c<PERSON>a bạn", "seedlessEmailOptionsSecondaryText": "Thê<PERSON> một ví với tài k<PERSON>n Apple hoặc Google của bạn ", "seedlessEmailOptionsButtonText": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với email", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Tạo ví với Apple ID của bạn", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Tạo ví với tài <PERSON> Google của bạn", "seedlessAlreadyExistsPrimaryText": "<PERSON><PERSON><PERSON> khoản đã tồn tại rồi", "seedlessAlreadyExistsSecondaryText": "Email này đã có một ví rồi, bạn có muốn đăng nhập không?", "seedlessSignUpWithAppleButtonText": "Đăng ký với Apple", "seedlessContinueWithAppleButtonText": "<PERSON><PERSON><PERSON><PERSON> t<PERSON> với <PERSON>", "seedlessSignUpWithGoogleButtonText": "Đăng ký với Google", "seedlessContinueWithGoogleButtonText": "T<PERSON><PERSON>p t<PERSON> v<PERSON>i Google", "seedlessCreateAPinPrimaryText": "Tạo PIN", "seedlessCreateAPinSecondaryText": "Mã này được sử dụng để bảo mật cho ví của bạn trên tất cả các thiết bị của bạn. <1>Mã này không thể được phụ<PERSON> hồ<PERSON>.</1>", "seedlessContinueText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessConfirmPinPrimaryText": "<PERSON><PERSON><PERSON> n<PERSON>n mã PIN của bạn", "seedlessConfirmPinSecondaryText": "Nếu bạn quên mã P<PERSON>, bạn sẽ không thể khôi phục ví của mình trên một thiết bị mới.", "seedlessConfirmPinButtonText": "Tạo PIN", "seedlessConfirmPinError": "Mã PIN không chính xác. <PERSON><PERSON> lòng thử lại", "seedlessAccountsImportedPrimaryText": "<PERSON><PERSON><PERSON> tài k<PERSON>n đã đ<PERSON><PERSON><PERSON> nhập", "seedlessAccountsImportedSecondaryText": "<PERSON><PERSON><PERSON> tài k<PERSON>n này sẽ tự động được nhập vào ví của bạn", "seedlessPreviouslyImportedTag": "<PERSON><PERSON> nhập tr<PERSON><PERSON><PERSON> đ<PERSON>y", "seedlessEnterPinPrimaryText": "Điền PIN của bạn", "seedlessEnterPinInvalidPinError": "Điền sai mã PIN. Chỉ đư<PERSON><PERSON> phép điền 4 ký tự số", "seedlessEnterPinNumTriesLeft": "Còn {{numTries}} l<PERSON><PERSON><PERSON> thử.", "seedlessEnterPinCooldown": "Thử lại sau {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN phải gồm đ<PERSON>g 4 ký tự số", "seedlessEnterPinMatch": "PIN khớp nhau", "seedlessDoneText": "<PERSON><PERSON>", "seedlessEnterPinToSign": "Điền mã PIN của bạn để ký kết giao dịch này", "seedlessSigning": "<PERSON><PERSON> k<PERSON>", "seedlessCreateSeed": "<PERSON><PERSON><PERSON> một cụm từ hạt giống cho ví", "seedlessImportOptions": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>n nh<PERSON> kh<PERSON>c", "seedlessImportPrimaryText": "<PERSON><PERSON><PERSON> t<PERSON> ch<PERSON>p", "seedlessImportSecondaryText": "<PERSON><PERSON><PERSON><PERSON> một ví hiện có bằng cụm từ hạt giống, kh<PERSON><PERSON> riê<PERSON>, hoặc ví phần cứng của bạn", "seedlessImportSeedPhrase": "<PERSON><PERSON><PERSON><PERSON> bằng cụm từ hạt giống", "seedlessImportPrivateKey": "Nhập bằng khóa riêng", "seedlessConnectHardwareWallet": "<PERSON><PERSON><PERSON> n<PERSON>i ví phần cứng", "seedlessTryAgain": "<PERSON><PERSON><PERSON> lại", "seedlessCreatingWalletPrimaryText": "Tạo ví", "seedlessCreatingWalletSecondaryText": "<PERSON>h<PERSON><PERSON> một ví xã hội", "seedlessLoadingWalletPrimaryText": "Tải ví", "seedlessLoadingWalletSecondaryText": "<PERSON>hậ<PERSON> và xem các ví đư<PERSON><PERSON> liên kết của bạn", "seedlessLoadingWalletErrorPrimaryText": "<PERSON><PERSON><PERSON><PERSON> thể tải ví", "seedlessCreatingWalletErrorPrimaryText": "<PERSON><PERSON><PERSON><PERSON> thể tạo ví", "seedlessErrorSecondaryText": "<PERSON><PERSON> lòng thử lại", "seedlessAuthAlreadyExistsErrorText": "<PERSON><PERSON> cung cấp thuộc về một tài k<PERSON> k<PERSON>c", "seedlessAuthUnknownErrorText": "<PERSON><PERSON> x<PERSON>y ra lỗi, vui lòng thử lại sau", "seedlessAuthUnknownErrorTextRefresh": "<PERSON><PERSON> xảy ra lỗi không x<PERSON>c đ<PERSON>nh, vui lòng thử lại sau. <PERSON><PERSON><PERSON> làm mới trang để thử lại.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON> c<PERSON>a s<PERSON>", "seedlessWalletExistsErrorPrimaryText": "Một ví xã hội đã tồn tại trên thiết bị của bạn rồi", "seedlessWalletExistsErrorSecondaryText": "<PERSON>ui lòng quay lại hoặc đóng màn hình này", "seedlessValueProp1PrimaryText": "<PERSON><PERSON><PERSON><PERSON> lậ<PERSON> li<PERSON>n mạch", "seedlessValueProp1SecondaryText": "Tạo một ví bằng tài k<PERSON>n Google hoặc Apple và bắt đầu khám phá web3 một cách dễ dàng", "seedlessValueProp2PrimaryText": "<PERSON><PERSON><PERSON> c<PERSON><PERSON><PERSON> b<PERSON><PERSON> mật", "seedlessValueProp2SecondaryText": "<PERSON><PERSON> của bạn đ<PERSON><PERSON><PERSON> lưu trữ bảo mật và phi tập trung qua nhiều yếu tố khác nhau", "seedlessValueProp3PrimaryText": "<PERSON><PERSON> kh<PERSON>i phục", "seedlessValueProp3SecondaryText": "<PERSON><PERSON><PERSON><PERSON> phục quyền truy cập ví của bạn với tài kho<PERSON>n Google hoặc Apple và một PIN gồm 4 chữ số", "seedlessLoggingIn": "<PERSON><PERSON> đăng nhập...", "seedlessSignUpOrLogin": "<PERSON><PERSON>ng ký hoặc đăng nhập", "seedlessContinueByEnteringYourEmail": "<PERSON><PERSON><PERSON><PERSON> tục bằng cách điền email của bạn", "seedless": "<PERSON><PERSON><PERSON><PERSON> hạt g<PERSON>", "seed": "<PERSON><PERSON><PERSON> từ hạt giống", "seedlessVerifyPinPrimaryText": "Xác minh PIN", "seedlessVerifyPinSecondaryText": "<PERSON>ui lòng điền mã PIN của bạn để tiếp tục", "seedlessVerifyPinVerifyButtonText": "<PERSON><PERSON><PERSON>", "seedlessVerifyPinForgotButtonText": "Bạn đã quên?", "seedlessPinConfirmButtonText": "<PERSON><PERSON><PERSON>", "seedlessVerifyToastPrimaryText": "<PERSON><PERSON><PERSON> <PERSON>h PIN của bạn", "seedlessVerifyToastSecondaryText": "Thỉnh thoảng chúng tôi sẽ yêu cầu bạn xác minh mã PIN của mình để bạn nhớ nó. Nếu bạn quên, bạn sẽ không thể phục hồi ví của mình.", "seedlessVerifyToastSuccessText": "Mã PIN của bạn đã đư<PERSON>c xác minh!", "seedlessForgotPinPrimaryText": "Đặt lại mã PIN bằng một thiết bị khác", "seedlessForgotPinSecondaryText": "<PERSON>ì lý do bả<PERSON> mật, bạn chỉ có thể đặt lại mã PIN của mình trong các thiết bị khác mà bạn đã đăng nhập", "seedlessForgotPinInstruction1PrimaryText": "Mở thi<PERSON>t bị kh<PERSON>c", "seedlessForgotPinInstruction1SecondaryText": "<PERSON><PERSON><PERSON> một thiết bị khác đã đăng nhập tà<PERSON> Phantom bằng email của bạn", "seedlessForgotPinInstruction2PrimaryText": "Vào Cài đặt", "seedlessForgotPinInstruction2SecondaryText": "Trong Cài đặt, ch<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> m<PERSON> & <PERSON><PERSON><PERSON>n riêng tư” và chọn “Đặt lại PIN”", "seedlessForgotPinInstruction3PrimaryText": "Đặt PIN mới của bạn", "seedlessForgotPinInstruction3SecondaryText": "<PERSON>u khi bạn đã đặt PIN mới, bạn sẽ có thể đăng nhập vào ví của mình trên thiết bị này", "seedlessForgotPinButtonText": "<PERSON><PERSON><PERSON> đã thực hiện các b<PERSON><PERSON> này", "seedlessResetPinPrimaryText": "Đặt lại PIN", "seedlessResetPinSecondaryText": "Điền một mã PIN mà bạn sẽ nhớ. Mã này được sử dụng để bảo mật ví trên tất cả các thiết bị của bạn", "seedlessResetPinSuccessText": "Mã PIN của bạn đã đư<PERSON>c cập nhật!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Thông qua việc tạo ví, bạn đồng ý với <1><PERSON><PERSON><PERSON><PERSON> khoản dịch vụ</1> và <5><PERSON><PERSON><PERSON> sách quyền riêng tư</5> c<PERSON><PERSON> chúng tôi", "pageNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy trang", "pageNotFoundDescription": "<PERSON>úng tôi không dẫn bạn đi lạc lối! Trang này không tồn tại, hoặc đã bị di chuyển rồi.", "webTokenPagesLegalDisclaimer": "Thông tin giá chỉ được cung cấp vì mục đích thông tin và không phải là một lời khuyên tài chính. Dữ liệu thị trường được cung cấp bởi các bên thứ ba và Phantom không đảm bảo độ chính xác của thông tin này.", "signUpOrLogin": "<PERSON><PERSON>ng ký hoặc đăng nhập", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Thông qua việc tạo tà<PERSON>, bạn đồng ý với <1><PERSON><PERSON><PERSON><PERSON> khoản dịch vụ</1> và <5><PERSON><PERSON><PERSON> sách quyền riêng tư</5> củ<PERSON> chúng tôi", "feedNoActivity": "Chưa có hoạt động nào", "followRequests": "<PERSON><PERSON><PERSON> y<PERSON>u c<PERSON>u theo dõi", "following": "<PERSON><PERSON> the<PERSON> d<PERSON>i", "followers": "<PERSON><PERSON><PERSON><PERSON> theo dõi", "follower": "<PERSON><PERSON><PERSON><PERSON> theo dõi", "joined": "Đ<PERSON> tham gia", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON><PERSON><PERSON><PERSON> có người theo dõi nào", "noFollowing": "<PERSON><PERSON><PERSON><PERSON> theo dõi ai", "noUsersFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy người dùng nào", "viewProfile": "<PERSON><PERSON> h<PERSON> s<PERSON>", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}