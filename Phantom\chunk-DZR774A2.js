import{c as i}from"./chunk-MHOQBMVI.js";import{h as o,n as t}from"./chunk-3KENBVE7.js";o();t();o();t();o();t();var r=class{constructor(e){this.onExploreTabClickedByUser=e=>{this.#e.capture("onExploreTabClickedByUser",{data:{explore:e}})};this.onExploreTabTitlesDragged=()=>{this.#e.capture("onExploreTabTitlesDragged")};this.onExploreFilterChangedByUser=e=>{this.#e.capture("onExploreFilterChangedByUser",{data:{explore:e}})};this.onExploreListShownToUser=e=>{this.#e.capture("onExploreListShownToUser",{data:{explore:e}})};this.onExploreCarouselShownToUser=e=>{this.#e.capture("onExploreCarouselShownToUser",{data:{explore:e}})};this.onExploreListItemClickedByUser=e=>{let{context:l,...a}=e;this.#e.capture("onExploreListItemClickedByUser",{data:{explore:{context:l||"explore",...a}}})};this.onExploreCarouselItemClickedByUser=e=>{this.#e.capture("onExploreCarouselItemClickedByUser",{data:{explore:e}})};this.onExploreLearnItemClickedByUser=e=>{this.#e.capture("onExploreLearnItemClickedByUser",{data:{explore:e}})};this.onExploreCarouselDragged=e=>{this.#e.capture("onExploreCarouselDragged",{data:{explore:e}})};this.searchedByUser=e=>{this.#e.capture("searchedByUser",{data:{search:e}})};this.searchItemClickedByUser=e=>{this.#e.capture("searchItemClickedByUser",{data:{search:e}})};this.onExploreSectionClickedByUser=e=>{this.#e.capture("onExploreSectionClickedByUser",{data:{explore:e}})};this.onExploreShortcutClickedByUser=e=>{this.#e.capture("onExploreShortcutClickedByUser",{data:{explore:e}})};this.#e=e}#e};var N=new r(i);export{N as a};
//# sourceMappingURL=chunk-DZR774A2.js.map
