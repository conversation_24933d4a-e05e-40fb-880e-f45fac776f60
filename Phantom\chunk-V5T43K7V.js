import{k as dt,s as De,z as mn}from"./chunk-7ZN4F6J4.js";import{$a as vn,$b as zn,A as ft,Bb as _n,Eb as Hn,Fa as En,Fb as Wn,Gb as Tt,Hb as Kn,J as bn,Ja as Rn,<PERSON> as Un,L as Sn,La as Dn,Lb as Qn,Ma as ve,Na as Ke,Oa as gt,Qa as In,Sa as Nn,Sb as Be,Tb as Pt,U as Tn,Ub as j,Wb as At,Xa as Pn,Xb as Ft,Ya as yt,Yb as ht,Za as Q,Zb as qn,_ as An,_b as $n,aa as Fn,ba as hn,cc as Yn,db as Bn,ec as vt,fc as wt,gc as pe,hb as bt,hc as Ae,ia as wn,jc as Bt,kc as Oe,lc as jn,mb as St,mc as kt,nc as Xn,oa as We,ob as On,oc as Ct,pb as Ln,pc as Le,qa as kn,qc as Jn,ra as Pe,rb as Mn,sb as Vn,ta as Cn,ua as xn,v as fn,vb as Gn,w as gn,y as mt,z as yn}from"./chunk-OUYKWOVO.js";import{a as Ee,g as Re,o as Ue}from"./chunk-SLQBAOEK.js";import{$d as ae,La as ln,Na as ct,O as tn,P as J,Pa as H,Pb as ce,Q as ge,Rb as dn,T as nn,U as rt,Ua as cn,Uc as er,Wa as Se,Xa as pt,_a as He,ca as un,e as Jo,jb as pn,pe as Ie,rd as Te,te as Ne,v as Zo,wc as re,wd as xe}from"./chunk-MZZEJ42N.js";import{K as on,p as en}from"./chunk-E3NPIRHS.js";import{a as u,m as $,n as lt}from"./chunk-56SJOU6P.js";import{$ as ut,M as at,a as O,b as ot,da as sn,f as it,fa as an,ka as Ce,l as st,ma as Nt,s as rn,x as be}from"./chunk-ALUTR72U.js";import{Ya as ke,ia as se,ka as ne,la as ye,ta as we}from"./chunk-L3A2KHJO.js";import{a as Y}from"./chunk-4P36KWOF.js";import{a as V}from"./chunk-7X4NV6OJ.js";import{f as I,h as s,m as Buffer,n as a}from"./chunk-3KENBVE7.js";s();a();var tr=Y.object({data:Y.string(),from:Y.string(),to:Y.string()}),nr=Y.object({transaction:tr});async function or({caip19:e,amount:t,owner:n}){let o=await ge.api().post("/tokens/v1/unwrap",{owner:n,caip19:e,amount:t.toNumber()}),r=nr.safeParse(o.data);if(!r.success){let{error:i}=r;throw we.captureError(i,"fungibles"),new yt(`Failed to get unwrap transaction: ${i.message}`)}return r.data.transaction}var rr=({caip19:e,amount:t,owner:n})=>ne({gcTime:ve.Medium,staleTime:1/0,enabled:n!==""&&t.isGreaterThan(0),queryKey:Q.unwrapTransaction({caip19:e,amount:t,owner:n}),async queryFn(){return await or({caip19:e,amount:t,owner:n})}});s();a();function Ot(){let e=Te(),t=re(),n=se();return ye({async mutationFn({accountId:o,mutations:r}){await vn(e,t,{accountId:o,mutations:r})},async onSuccess(o,r){await n.invalidateQueries({queryKey:Q.hiddenMints(r.accountId)})}})}s();a();var Lt=I(er());s();a();var xt={recipient:"",recipientHandle:"",addressBookRecipient:void 0,amountAsset:new O(0),amountUsd:0,transactionSpeed:"standard",solana:{references:[]},multichainTransaction:void 0};var eo={sendSessionId:"",fungibleKey:void 0,splTokenAccount:void 0,analyticsRecipient:void 0,sendFormValues:xt,shouldShowUsdValues:!1,retryArgs:void 0},q=Ne((e,t)=>({...(0,Lt.default)(eo),resetSendSlice:()=>{let n=(0,Lt.default)(eo);n.sendSessionId=on(),e(n)},setAnalyticsRecipient:n=>{e({analyticsRecipient:n})},setSendFungibleKey:(n,o)=>{e({fungibleKey:n,splTokenAccount:o})},setSendFormValues:n=>{e({sendFormValues:n})},setInputAmount:n=>{let o=t();e({sendFormValues:{...o.sendFormValues,inputAmount:n}})},setShouldShowUsdValues:n=>{e({shouldShowUsdValues:n})},setRetryArgs:n=>{e({retryArgs:n})}}));s();a();var to={collectible:void 0,sendFormValues:xt,shouldRequireAmount:!1},no=Ne(e=>({...to,resetSendSlice:()=>{e({...to})},setSendCollectible:t=>{e({collectible:t})},setSendFormValues:t=>{e({sendFormValues:t})},setShouldRequireAmount:t=>{e({shouldRequireAmount:t})}}));s();a();s();a();var ir={query:""},sr=Ne(e=>({...ir,setQuery:t=>{e({query:t})}}));s();a();var ur=I(V());s();a();s();a();var lr=I(V());s();a();s();a();s();a();var Et=I(Jo(),1),ro=I(Zo(),1);var oo=(0,Et.struct)([(0,Et.u8)("instruction")]);function io(e,t,n,o=[],r=We){let i=xn([{pubkey:e,isSigner:!1,isWritable:!0},{pubkey:t,isSigner:!1,isWritable:!0}],n,o),l=Buffer.alloc(oo.span);return oo.encode({instruction:Cn.CloseAccount},l),new ro.TransactionInstruction({keys:i,programId:r,data:l})}var Mt=new O(0),Vt=(e,t,n,o=!0)=>{let{data:r,isLoading:i}=Tt(e),l=q(b=>b.sendFormValues.recipient);if(!t)return Mt;let{data:c,type:p}=t,m=new O(c.balance).multipliedBy(sn(c.decimals));if(!j(p)||!n)return m;let d=new O(0);if(mt(n))d=m.minus(new O(n.value));else if(yn(n)){let{gasLimit:b,maxFeePerGas:g,l1Fee:S=new O(0)}=n,w=b.times(g).plus(S);d=m.minus(w)}else if(ft(n)){if(!e||i)return;let b=e.addressType,g=r?.safeToSendUtxos??[],S=gn(n.btcPerKilobyte),w=l&&ln.safeParse(l).success?l:"bc1p",k=[{value:g.reduce((U,h)=>U.plus(new O(h.value)),new O(0)).toNumber(),address:w}],P=An(b,g,k,S.toNumber(),[]);if(P.type==="error")d=new O(P.maxAmount).integerValue(O.ROUND_DOWN);else{let{inputs:U,fee:h}=P;d=U.reduce((L,E)=>L.plus(new O(E.value)),new O(0)).minus(h).integerValue(O.ROUND_DOWN)}}else return st(n);return d.isGreaterThan(Mt)?d:o?Mt:d};s();a();var Gt=I(V());s();a();var so=(e,t,n)=>{let o=Xn(e,t),{fungibles:r,isLoadingTokens:i,isErrorTokens:l,isErrorPrices:c}=pe({...n,keys:o?[o]:[]});return{isError:c||l,fungible:r.length===1?r[0]:void 0,isLoading:i}};var _t=(e,t,n,o=new O(0))=>{let{fungible:r,isLoading:i,isError:l}=so(e,t),c="unknown",p="",m=(0,Gt.useMemo)(()=>{let b=r?.data.walletAddress;if(!b)return;let g=pn(e,b);return{networkID:e,address:b,addressType:g}},[e,r?.data.walletAddress]),d=Vt(m,r,n,!1);return!l&&r&&d&&(d.gte(o)?c="sufficient":c="insufficient",p=r.data.symbol||""),(0,Gt.useMemo)(()=>({hasSufficientFunds:c,nativeTokenSymbol:p,isLoading:i,isError:l}),[c,p,i,l])};s();a();var co=I(V());s();a();var Ht=I(V());s();a();var ao=({chainAddresses:e=[]})=>{let t=Q.tokensErrorCount(e);return ne({queryKey:t,initialData:0})};s();a();var cr=({errors:e})=>e;function uo({chainAddresses:e,queryOptions:t}){return Yn({select:cr,chainAddresses:e,queryOptions:t})}var lo=3,pr=1,Me=({networkID:e,chainAddresses:t=[]})=>{let{data:n=[]}=uo({chainAddresses:t}),{data:o=0}=ao({chainAddresses:t}),r=o>=lo,{data:i=0}=Vn(),l=i>=lo,c=n.length===pr&&n.some(d=>d.code==="TokensTrimmed"),{t:p}=$(),m=(0,Ht.useMemo)(()=>{if(r)return p("partialErrorGeneric");let d=n.filter(g=>e?g.details.chainId===e:!0),b=d.length+(l?1:0);return b?b>1?p("partialErrorGeneric"):l?p("partialErrorTokenPrices"):c?p("partialErrorTokensTrimmed"):e?p("partialErrorTokenDetail"):p("partialErrorBalanceChainName",{chainName:H.getChainName(d[0].details.chainId)}):""},[r,n,l,e,c,p]);return(0,Ht.useMemo)(()=>({partialErrorMessage:m,shouldShowPartialError:m!==""}),[m])};function dr(e){let{onTappingBuy:t,onTappingReceive:n,onTappingSend:o,onTappingSwap:r,account:i}=e,{shouldShowPartialError:l,partialErrorMessage:c}=Me({chainAddresses:i?.addresses});return{ctaActions:mr({onTappingBuy:t,onTappingReceive:n,onTappingSend:o,onTappingSwap:r}),shouldShowPartialError:l,partialErrorMessage:c}}function mr(e){let{t}=$();return(0,co.useMemo)(()=>{let o=[];return o.push({text:t("commandSend"),type:"send",onClick:e.onTappingSend}),o.push({text:t("commandSwap"),type:"swap",onClick:e.onTappingSwap}),o.push({text:t("commandReceive"),type:"receive",onClick:e.onTappingReceive}),o.push({text:t("commandBuy"),type:"buy",onClick:e.onTappingBuy}),o.sort((r,i)=>(Oe.get(r.type)??100)-(Oe.get(i.type)??100)),{primary:o,more:[]}},[t,e.onTappingSend,e.onTappingSwap,e.onTappingReceive,e.onTappingBuy])}s();a();s();a();var qe=(r=>(r.FAIL_SEVERE="FAIL_SEVERE",r.FAIL_HIGH="FAIL_HIGH",r.NO_ACTION="NO_ACTION",r.DATA_NOT_FOUND="DATA_NOT_FOUND",r))(qe||{}),po=Y.object({action:Y.nativeEnum(qe),address:cn,expirationUnixtimeInSec:Y.number().nullish().default(null)}),Wt=Y.object({addressScreenResults:Y.array(po)}),Qe=class extends Error{constructor(t){super(t),this.name="WalletScreenError"}};s();a();var To=I(V());s();a();s();a();var gr="@phantom/wallet-screening",mo=`${gr}:wallet-screen-action`,fo={walletScreenAction(e){return[mo,e]}};s();a();var go=new Error("Failed to fetch wallet screen action.");async function yo(e){try{let t=await ge.api().post("/wallet_screen/v1",e);if(!nn(t))throw go;let n=t.data;return Wt.parse(n)}catch{throw go}}var bo=be({days:7}),So=e=>{let t=e!==void 0&&H.isMainnetNetworkID(e.networkID),n=[];if(e!==void 0){let c=H.isEVMNetworkID(e.networkID)?e.address.toLowerCase():e.address;n=[{chainId:e.networkID,address:c,resourceType:"address"}]}let o={addresses:n},r=se(),i=fo.walletScreenAction(o),l=[i[0]];return ne({enabled:t,queryKey:i,gcTime:bo,staleTime:bo,refetchInterval:!1,refetchOnMount:!1,async queryFn(){if(yr(r,l))return"FAIL_SEVERE";let p=await yo(o);return br(p)}})},yr=(e,t)=>{let n=e.getQueriesData({queryKey:t,exact:!1}),o=!1;for(let[r,i]of n){let l=i;if(l&&l==="FAIL_SEVERE"){o=!0;break}}return o},br=e=>{let t=e.addressScreenResults;return t.some(n=>n.action==="FAIL_SEVERE")?"FAIL_SEVERE":t.some(n=>n.action==="FAIL_HIGH")?"FAIL_HIGH":t.some(n=>n.action==="NO_ACTION")?"NO_ACTION":"DATA_NOT_FOUND"};var Kt=(e,t)=>{let n=re(),{data:o}=So(e);switch((0,To.useEffect)(()=>{o&&e&&n.capture("walletScreened",{data:{walletScreen:{action:o.toString(),surface:t},chainId:H.getChainID(e.networkID),networkId:e.networkID}})},[o,e,t,n]),o){case"FAIL_SEVERE":case"FAIL_HIGH":throw new Qe(`Transaction disabled: ${t}`);case"NO_ACTION":case"DATA_NOT_FOUND":return}};s();a();s();a();s();a();var Ao=I(V());var Tr=({networkID:e,unsignedTransaction:t,pendingActivityRows:n,accountIdentifier:o,accountSigner:r,storage:i,includeCallPayload:l})=>({accountSigner:r,accountIdentifier:o,networkID:e,unsignedTransaction:t,...l&&{callPayload:{from:t.from,to:t.to,data:t.data,value:t.value}},pendingTransactionInput:{ownerAddress:t.from,networkID:e,data:{nonce:"",unsignedTransaction:t,hash:""},type:"swap",display:{summary:n}},storage:i}),Ar=async({t:e,fungible:t,unsignedUnwrapTx:n,gasEstimation:o,storage:r,accountMetadata:i,accountSigner:l})=>{if(!n)throw new Error("Unsigned unwrap transaction is required");if(!o)throw new Error("Gas estimation is required");let{chain:c,balance:p}=t.data;if(!H.isEthereumNetworkID(c.id))throw new Error(`Chain is not supported: ${c.id}`);let m=c.id,d={topRight:{text:`+${p} ${c.symbol}`},topLeft:{text:e("transactionsPendingSwapping")},bottomRight:{text:`-${p} ${t.data.symbol}`},bottomLeft:{text:`${t.data.name}`}},b=en.parse({type:"0x2",chainId:H.getEVMNetworkIDValue(m),gas:`0x${o.gasLimit.toString(16)}`,maxFeePerGas:`0x${o.maxFeePerGas.toString(16)}`,maxPriorityFeePerGas:`0x${o.maxPriorityFeePerGas.toString(16)}`,...n}),g=Tr({storage:r,accountSigner:l,pendingActivityRows:d,includeCallPayload:!1,networkID:m,unsignedTransaction:b,accountIdentifier:i.accountIdentifier});return wn(g)},Fr=()=>{let{data:e}=ae();return(0,Ao.useMemo)(()=>{let n=e?.type,o=e?.name??"",r=e?.identifier??"";return{accountName:o,accountType:n,accountIdentifier:r}},[e])},hr=e=>{let t=Te(),n=se(),{t:o}=$(),r=Fr(),i=xe();return ye({async mutationFn({fungible:l,unsignedUnwrapTx:c,gasEstimation:p}){return await Ar({t:o,fungible:l,unsignedUnwrapTx:c,gasEstimation:p,accountMetadata:r,storage:t,accountSigner:i})},async onSuccess(){await n.invalidateQueries({queryKey:Q.tokens(e)})}})};s();a();s();a();s();a();var Fo=()=>{let{setRetryArgs:e}=q();return t=>{if(t instanceof hn){let{successfulTxIds:n}=t,[o,r]=n;e({type:"BRC20",commitTxId:o,revealTxId:r})}}};function wr(){let e=Te(),{data:t}=ae({select:l=>l.identifier}),n=Fo(),o=Wn(),r=xe();return ye({mutationFn:async({multichainTransaction:l,pendingTransactionInput:c,senderAddress:p,gasEstimation:m})=>{if(!t)throw new Error("No account identifier found");let{networkID:d}=l,b=Be.get(d).onBeforeSend({multichainTransaction:l,gasEstimation:m})??l,g=await Dn({accountIdentifier:t,accountSigner:r,multichainTransaction:b,networkID:d,pendingTransactionInput:c,senderAddress:p,storage:e,utxoManager:o});return Un(d,g)},onError(l){l instanceof Error&&(we.captureError(l,"fungibles"),l instanceof Fn&&n(l))}})}s();a();var ho=I(V());var kr=(e,t,n)=>{let o=se(),{fungibles:r}=pe({keys:[e]}),i=r.length===1?r[0]:void 0;(0,ho.useEffect)(()=>{if(!i)return;let{type:l,data:c}=i,{balance:p,decimals:m,chain:d}=c,{id:b}=d,g=j(l)===!0;if(!n||!p||g||p.isEqualTo(t))return;let S=Q.transferFungibleLoggingContext(e),w={transactionId:n,decimals:m,chainId:b,isNativeToken:g,previousBalance:p,fungibleKey:e};o.setQueryData(S,w)},[n])};s();a();var ko=I(V());s();a();function wo(e,t,n){return J.PublicKey.findProgramAddressSync([e.toBuffer(),n.toBuffer(),t.toBuffer()],kn)[0]}var Cr=new yt("Unable to unwrap SOL. Wrapped SOL Token account does not exist.");function Qt(){let{connection:e}=_n(),{data:t}=ae(),{solanaChainAddress:n,accountIdentifier:o}=(0,ko.useMemo)(()=>({solanaChainAddress:t?.addresses.find(He),accountIdentifier:t?.identifier??""}),[t]),r=Te(),i=xe(),{t:l}=$();return ye({mutationFn:async()=>{if(!n)return Promise.reject("solanaChainAddress not loaded");let{networkID:c,address:p}=n,m=new J.PublicKey(p),d=wo(m,Pe,We);if(!await e.getAccountInfo(d))throw Cr;let g=io(d,m,m,[],We),S=new tn.Transaction().add(g),w={ownerAddress:p,networkID:c,data:{signature:""},type:"unwrapSOL",display:{summary:{topLeft:{text:l("assetDetailUnwrappingSOL")}}}},C=await En({accountIdentifier:o,accountSigner:i,connection:e,feePayer:new J.PublicKey(p),pendingTransactionInput:w,storage:r,transaction:S,useSubmissionService:!1});return{networkID:c,id:C}}})}s();a();var Co=be({minutes:1}),xr=(e,t=!0)=>{let n=Q.solanaBalance(e);return ne({enabled:!!e&&t&&He(e),gcTime:Co,staleTime:Co,queryKey:n,async queryFn(){if(!e)throw new Error("No chain address provided");if(He(e))return Sn(e.networkID).getBalance(new J.PublicKey(e.address));throw new Error(`SOL balance not implemented for ${e.networkID}`)}})};s();a();function qt({balance:e,fungibleTokenType:t,chainAddress:n,isSendEnabledForTokenType:o,isSplNonTransferable:r}){let{data:i}=Tt(n),l;switch(t){case"BRC20":case"BitcoinNative":l=!!i;break;default:l=!0}return e.isGreaterThan(0)&&o&&l&&!r}s();a();var Eo=I(V());s();a();var xo=I(V());var $t=e=>{let{data:t}=De();return(0,xo.useMemo)(()=>{if(!e||!t)return;let n=e.data.chain.id,o=t.explorers[n]??Ee.get(n).defaultExplorer,r,i;if(j(e.type))i="address",r=e.data.walletAddress;else switch(e.type){case"ERC20":{i="address",r=e.data.contractAddress;break}case"SPL":{i="address",r=(e.data.splTokenAccountPubkey||e.data.tokenAddress)??"";break}case"CompressedSPL":{i="address",r=e.data.tokenAddress??"";break}case"BRC20":return{explorerName:"Unisat",explorerUrl:`https://unisat.io/brc20?q=${e.data.walletAddress}`}}let l=Ue({networkID:n,endpoint:i,explorerType:o,param:r});return{explorerName:Re[o],explorerUrl:l}},[e,t])};var Er=[{text:"commandReceive",type:"receive"},{text:"commandSend",type:"send"},{text:"commandSwap",type:"swap"},{text:"assetDetailStakeSOL",singleWordAltText:"commandStake",type:"stakeSol",menuOnly:!0},{text:"commandMintLST",singleWordAltText:"commandMintLST",type:"mintLST",menuOnly:!0},{text:"assetDetailUnwrapAll",singleWordAltText:"commandUnwrap",type:"unwrapWrappedSol",menuOnly:!0},{text:"commandStake",singleWordAltText:"commandStake",type:"stake",menuOnly:!0},{text:"commandUnstake",singleWordAltText:"commandUnstake",type:"unstake",menuOnly:!0},{text:"commandReportAsSpam",singleWordAltText:"commandReport",type:"reportAsSpam",isDestructive:!0,menuOnly:!0},{text:"commandReportAsNotSpam",singleWordAltText:"collectiblesReportNotSpam",type:"reportAsNotSpam",isDestructive:!0,menuOnly:!0},{text:"",singleWordAltText:"commandView",type:"viewOnExplorer",menuOnly:!0}];function Rr(e,t){return Er.reduce((n,o)=>{let r=e[o.type];if(r===void 0)return n;if((typeof r.enabled>"u"?!!r.onClick:r.enabled)&&r.onClick){let l=r.text?r.text:t(o.text);return[...n,{...o,onClick:r.onClick,text:l,...o.singleWordAltText?{singleWordAltText:t(o.singleWordAltText)}:{}}]}return n},[])}function Ur(e){let{balance:t,canSwap:n,chainAddress:o,fee:r,fungible:i,isReadOnlyAccount:l,isSplNonTransferable:c,killBrc20Sends:p,onDepositPress:m,onMarkAsSpam:d,onMarkNotSpam:b,onSendPress:g,onStakeSolPress:S,onSwapPress:w,onUnwrapWrappedSolPress:C,onNativeMintLSTPress:k,onStakeLSTPress:P,onUnstakeLSTPress:U,onViewOnExplorerPress:h,type:y}=e,{t:L}=$(),E=Ie("solana"),F=$t(i),v=y==="BRC20"?!p:!0,N=qt({balance:t,fungibleTokenType:y,chainAddress:o,isSendEnabledForTokenType:v,isSplNonTransferable:c}),G=y==="SolanaNative",D=!l&&G,K=y==="SPL"&&i?.data.tokenAddress===Pe.toBase58(),{fungible:ee}=Ae({key:"SolanaNative"}),T=ot(ee?.data?.amount??"0"),M=K&&!!i.data.tokenAddress&&E!==void 0&&T.isGreaterThan(r),te=(0,Eo.useMemo)(()=>{let _=i?.type==="CompressedSPL",B=Rr({receive:{enabled:!0,onClick:m},viewOnExplorer:{enabled:!0,onClick:h,text:L("assetDetailViewOnExplorer",{explorer:F?.explorerName})},send:{enabled:N&&!_,onClick:g},swap:{enabled:n&&!_,onClick:w},stakeSol:{enabled:D,onClick:S},mintLST:{onClick:k},unwrapWrappedSol:{enabled:M,onClick:C},stake:{onClick:P},unstake:{onClick:U},reportAsSpam:{enabled:i?.type&&!j(i.type),onClick:d},reportAsNotSpam:{enabled:i?.type&&!j(i.type),onClick:b}},L);return B.sort((A,R)=>(Oe.get(A.type)??100)-(Oe.get(R.type)??100)),jn(B,4)},[L,m,N,n,D,M,d,b,F?.explorerName,h,i?.type,g,w,S,C,P,U,k]);return{canStakeSol:D,canUnwrapWrappedSol:M,ctaActions:te}}s();a();var Ve=I(V());s();a();s();a();var Dr=Y.object({data:Pn}),Ir="TokenHoldingsNotLoading";async function Ro(e){let t=Se(e.caip19),n=new URLSearchParams;n.append("walletAddress",e.walletAddress);let o=t.resourceType==="nativeToken"?t.slip44:t.address,r=await ge.api().timeout(be({seconds:10})).get(`/tokens/v1/${t.chainId}/${t.resourceType}/${o}/holdings`,{params:n});if(r.status!==200)throw new lt({key:Ir});let i=await r.data;return Dr.parse(i)}var Nr="get-fungible-holdings",Pr=e=>({gcTime:ve.Short,staleTime:Ke.Long,enabled:!!e,queryKey:e?Q.fungibleHoldings(e):[],async queryFn(){if(!e)throw new Error("No args provided for getFungibleHoldings");try{return(await Ro(e)).data}catch(t){if(rt(t)){let n=t.response?.status;gt(t,n,Nr,{})}throw t}}});function Uo(e){return ne(Pr(e))}function vr(e){if(e&&H.isBitcoinNetworkID(e.networkID))return`${un.getDisplayName(e.addressType)} (${rn(e.address,5,3)})`}function Br({symbol:e,name:t,chainName:n,addressString:o,mintAddress:r,contractAddress:i,transferFeePercent:l,interestBearingPercent:c,isNonTransferable:p,isPermanentDelegated:m,copyToClipboard:d}){return[{label:u.t("assetDetailTokenNameLabel"),value:e?`${t} (${e})`:t},n?{label:u.t("assetDetailNetworkLabel"),value:n}:void 0,o?{label:u.t("assetDetailAddressLabel"),value:o}:void 0,r?{label:u.t("publicFungibleDetailMint"),value:ce(r,4),onPress:()=>{d(r)}}:void 0,i?{label:u.t("publicFungibleDetailContract"),value:ce(i,4),onPress:()=>{d(i)}}:void 0,l?{label:u.t("sendFungibleTransferFee"),value:`${l}%`,tooltipContent:u.t("sendFungibleTransferFeeToolTip")}:void 0,c?{label:u.t("sendFungibleInterestBearingPercent"),value:`${c}%`}:void 0,p?{label:u.t("sendFungibleNonTransferable"),value:u.t("sendFungibleNonTransferableYes")}:void 0,m?{label:u.t("permanentDelegateTitle"),value:u.t("permanentDelegateValue"),tooltipContent:u.t("permanentDelegateTooltipValue")}:void 0].filter(it)}function Do({fungibleKey:e,account:t,copyToClipboard:n}){let{fungible:o}=Ae({key:e}),{chainName:r,symbol:i,name:l,walletAddress:c,networkID:p,transferFeePercent:m,interestBearingPercent:d,isNonTransferable:b,isPermanentDelegated:g}=(0,Ve.useMemo)(()=>{let E=o?.data;if(!E)return{};let{symbol:F,name:v,chain:N,walletAddress:G}=E,D=ht(o),K=qn(o),ee=zn(o),T=!!$n(o);return{symbol:F??null,name:v??u.t("assetDetailUnknownToken"),walletAddress:G,chainName:N.name,networkID:N.id,transferFeePercent:D,interestBearingPercent:K,isNonTransferable:ee,isPermanentDelegated:T}},[o]),{data:S}=Ie({address:c,networkID:p});Kn(S);let w=(0,Ve.useMemo)(()=>vr(S),[S]),C=(0,Ve.useMemo)(()=>o&&o.type==="SPL"?o.data.mintAddress:void 0,[o]),k=(0,Ve.useMemo)(()=>o&&o.type==="ERC20"?o.data.contractAddress:void 0,[o]),{partialErrorMessage:P,shouldShowPartialError:U}=Me({chainAddresses:t?.addresses,networkID:p}),{data:[h]}=ke(["enable-holdings"]),y=h&&o?pt(wt(o)):void 0,L=Uo(y&&c?{caip19:y,walletAddress:c}:void 0);return(0,Ve.useMemo)(()=>({summaryItems:Br({symbol:i,name:l,chainName:r,addressString:w,mintAddress:C,contractAddress:k,transferFeePercent:m,interestBearingPercent:d,isNonTransferable:b,isPermanentDelegated:g,copyToClipboard:n}),partialErrorMessage:P,shouldShowPartialError:U,holdingsQueryResult:L,isHoldingsEnabled:h}),[n,i,l,r,w,C,k,m,d,b,P,U,g,L,h])}s();a();var Io=I(V());var Or=e=>{let t=se();return(0,Io.useCallback)(()=>t.invalidateQueries({queryKey:Q.tokens(e)}),[t,e])};s();a();var No=I(V()),zt=(e,t)=>(0,No.useMemo)(()=>{if(e)if(ft(e)){let n=fn(e).toNumber().toLocaleString("en-US");return{leftSubtext:void 0,leftSubtextColor:void 0,rightSubtext:t("satsAmount",{sats:n.toString()})}}else return mt(e)&&e.highFees?{leftSubtext:t("notificationTransactionApprovalNetworkFeeHighWarning"),leftSubtextColor:"#ffdc62",rightSubtext:""}:void 0},[e,t]);s();a();s();a();s();a();s();a();var Lr="TokenDetailNotLoading";async function Yt(e){let t=Se(e.caip19),n=t.resourceType==="nativeToken"?t.slip44:t.address,o=await ge.api().timeout(be({seconds:10})).get(`/tokens/v1/${t.chainId}/${t.resourceType}/${n}`);if(o.status!==200)throw new lt({key:Lr});let r=await o.data;return In.parse(r.data),r}s();a();var Po={fungibleDetail({caip19:e}){return["fungibleDetail",{caip19:e}]}};var Mr="get-fungible-detail",vo=e=>({gcTime:ve.Short,staleTime:Ke.Long,queryKey:Po.fungibleDetail({caip19:e.caip19}),async queryFn(){try{return(await Yt(e)).data}catch(t){if(rt(t)){let n=t.response?.status;gt(t,n,Mr,{})}throw t}}});function ze(e){return ne(vo(e))}var Z=I(V());function Gr(e,t){if(!e)return;let n=[];X(n,u.t("nounSymbol"),e.symbol),X(n,u.t("nounNetwork"),e.chain.name),X(n,ct.safeParse(e.chain.id).success?u.t("publicFungibleDetailMint"):u.t("publicFungibleDetailContract"),t);let o=Oo(e.marketCap);X(n,u.t("publicFungibleDetailMarketCap"),o);let r=Ye(e.totalSupply);X(n,u.t("publicFungibleDetailTotalSupply"),r);let i=Ye(e.circulatingSupply);X(n,u.t("publicFungibleDetailCirculatingSupply"),i);let l=Qr(e.maxSupply);X(n,u.t("publicFungibleDetailMaxSupply"),l);let c=Ye(e.holders);return X(n,u.t("publicFungibleDetailHolders"),c),n}function _r(e){if(!e)return;let t=[],n=Oo(e.volume24hUSD),o=Ut(e.volume24hUSDChangePercentage);Xt(t,u.t("publicFungibleDetailVolume"),n,o);let r=Ye(e.trades24h),i=Ut(e.trades24hChangePercentage);Xt(t,u.t("publicFungibleDetailTrades"),r,i);let l=Ye(e.uniqueWallets24h),c=Ut(e.uniqueWallets24hChangePercentage);return Xt(t,u.t("publicFungibleDetailTraders"),l,c),t.length>0?t:void 0}function Hr(e,t){if(!e)return;let n=[],o=Ut(e.top10HoldersPercent);X(n,u.t("publicFungibleDetailTop10Holders"),o,u.t("publicFungibleDetailTop10HoldersTooltip"),t);let r=jt(e.mintable);X(n,u.t("publicFungibleDetailMintable"),r,u.t("publicFungibleDetailMintableTooltip"),t);let i=jt(e.mutableMetadata);X(n,u.t("publicFungibleDetailMutableInfo"),i,u.t("publicFungibleDetailMutableInfoTooltip"),t);let l=jt(e.ownershipRenounced);X(n,u.t("publicFungibleDetailOwnershipRenounced"),l,u.t("publicFungibleDetailOwnershipRenouncedTooltip"),t);let c=Bo(e.updateAuthority);X(n,u.t("publicFungibleDetailUpdateAuthority"),c,u.t("publicFungibleDetailUpdateAuthorityTooltip"),t);let p=Bo(e.freezeAuthority);return X(n,u.t("publicFungibleDetailFreezeAuthority"),p,u.t("publicFungibleDetailFreezeAuthorityTooltip"),t),n.length>0?n:void 0}function Wr(e){let{fungibleAnalytics:t,caip19:n,canBuy:o,canSwap:r,entryPoint:i,fungibleKey:l,navigateToBalance:c,openBuy:p,openReportIssue:m,openLink:d,openSwapper:b,showSummaryTooltip:g,title:S,onShare:w,copyAddress:C,includeUgcItemTypes:k,refetchBondingCurve:P}=e,{t:U}=$(),h=re(),y=(0,Z.useMemo)(()=>Se(n),[n]),{data:L}=ae(),E=L?.isReadOnly??!1,{data:F,refetch:v,isError:N}=ze({caip19:n}),G=$r(l,y),{fungibles:D,isLoadingTokens:K,refetch:ee}=pe({keys:G}),T=D?.[0],M=qr(F,T,S),te=(0,Z.useRef)(!1);(0,Z.useEffect)(()=>{!te.current&&!K&&(te.current=!0,t.onPublicFungiblePageOpen({caip19:n,uiContext:{name:i},hasBalance:!!T}))},[t,n,T,i,K]);let _=(0,Z.useCallback)(()=>{Promise.all([v(),ee(),P()])},[v,ee,P]),B=(0,Z.useCallback)(()=>{if(!T)return;let x=T.data.chain.id,le=M??U("assetDetailUnknownToken");h.capture("assetDetailClick",{data:{address:T.data.tokenAddress,chain:x,chainId:x,isNativeOfType:x,networkId:x,type:"fungible",spamStatus:T.data.spamStatus,name:le}}),c({chainName:T.data.chain.name,fungibleKey:T.data.key,title:M,networkID:T.data.chain.id,symbol:T.data.symbol??"",tokenAddress:T.data.tokenAddress,type:T.type,walletAddress:T.data.walletAddress})},[M,T,c,h,U]),A=[];k&&A.push({type:"ugcWarningBanner",data:{caip19:n,spamStatus:F?.spamStatus}}),!k&&F&&F.spamStatus!=="VERIFIED"&&A.push({type:"unverifiedBanner"}),A.push({type:"priceHistory",data:{caip19:n}});let R=Kr({caip19:n,canBuy:o,canSwap:r,fungible:T,fungibleCaip19:y,openBuy:p,openReportIssue:m,openLink:d,openSwapper:b,title:M,onShare:w,isReadOnly:E}),de=R.primary.length>0,me=!E&&R.primary.length===0&&R.more.length>0;de&&A.push({type:"cta",data:{actions:R}}),T&&A.push({type:"sectionHeader",data:u.t("publicFungibleDetailYourBalance")},{type:"balance",data:{fungible:T,onPress:B}}),N&&A.push({type:"error",data:void 0}),F?.description&&A.push({type:"sectionHeader",data:u.t("publicFungibleDetailAbout")},{type:"about",data:F.description});let ue=Gr(F,C);ue&&(A.push({type:"sectionHeader",data:u.t("publicFungibleDetailInfo")}),k?A.push({type:"tableWithBondingCurveProgress",data:{caip19:n,rows:ue,key:"info"}}):A.push({type:"table",data:{key:"info",rows:ue}}));let fe=_r(F);fe&&A.push({type:"sectionHeader",data:u.t("publicFungibleDetailPerformance")},{type:"table",data:{key:"performance",rows:fe}});let oe=Hr(F,g);oe&&A.push({type:"sectionHeader",data:u.t("publicFungibleDetailSecurity")},{type:"table",data:{key:"security",rows:oe}});let Ge=(0,Z.useCallback)(x=>{h.capture("onPublicFungibleLinkOpen",{data:{url:x}})},[h]);return F?.links&&A.push({type:"links",data:{links:F.links,trackAction:Ge}}),{title:M,caip19:n,items:A,actions:me?R:void 0,showMoreInHeader:me,isLoading:K,refetch:_}}function Kr(e){let{caip19:t,canBuy:n,canSwap:o,fungible:r,fungibleCaip19:i,openBuy:l,openReportIssue:c,openLink:p,openSwapper:m,title:d,onShare:b,isReadOnly:g}=e,{t:S}=$(),{data:w}=dt(),{data:[C]}=ke(["prefer-phantom-dot-com"]),k=o(i.chainId),P=(0,Z.useCallback)(()=>{t&&m({buyFungibleCaip19:t})},[t,m]),{data:U}=De(),h=i.resourceType==="address",y=(0,Z.useMemo)(()=>{if(!h||!U)return;let N=i.chainId,G=U.explorers[N]??Ee.get(N).defaultExplorer,D=Ue({networkID:N,endpoint:"address",explorerType:G,param:i.address});return{explorerName:Re[G],explorerUrl:D}},[h,i,U]),L=(0,Z.useCallback)(()=>{y?.explorerUrl&&p(y?.explorerUrl)},[y?.explorerUrl,p]),E={caip19:t,hasBalance:!!r,title:d},F=[];k&&!g&&F.push({type:"swap",text:S("commandSwap"),onClick:P,typeSpecificMetadata:E}),n&&!g&&F.push({type:"buy",text:S("commandBuy"),onClick:l??Nt,typeSpecificMetadata:E});let v=Ct.getUrlFromFungible({caip19:i,isAnalyticsOptedOut:!!w,useDotCom:C});return v&&F.push({type:"share",text:S("commandShare"),onClick:()=>b(v),typeSpecificMetadata:{...E,shareUrl:v}}),F.push({type:"reportAsSpam",text:S("commandReportIssue"),onClick:c??Nt,menuOnly:!0,typeSpecificMetadata:E}),h&&F.push({type:"viewOnExplorer",text:S("assetDetailViewOnExplorer",{explorer:y?.explorerName}),singleWordAltText:S("commandView"),onClick:L,menuOnly:!0,typeSpecificMetadata:{...E,origin:at(y?.explorerUrl)}}),kt(F,3)}var Bo=e=>e?ce(e):void 0,Ut=e=>e?ut(Ft(e)):void 0,Ye=e=>e?Le(e):void 0,Oo=e=>e?Ce(e,{compact:!0}):void 0,Qr=e=>e===null?"\u221E":e===void 0?void 0:Le(e),jt=e=>e==null?void 0:e?u.t("nounYes"):u.t("nounNo");function X(e,t,n,o,r){n&&e.push({label:t,value:n,tooltipContent:o,handleTooltip:r?()=>{r(t,o??"")}:void 0})}function Xt(e,t,n,o,r,i){!n||!o||e.push({label:t,value:n,change:o,tooltipContent:r,handleTooltip:i?()=>{i(t,r??"")}:void 0})}var qr=(e,t,n)=>e?.name??t?.data.name??n??"",$r=(e,t)=>e?[e]:t?At(t):void 0;s();a();var z=I(V());function zr(e,{showSummaryTooltip:t,copyAddress:n}){if(!e)return;let o=[];W(o,u.t("nounSymbol"),e.symbol),W(o,u.t("nounNetwork"),e.chain.name),W(o,ct.safeParse(e.chain.id).success?u.t("publicFungibleDetailMint"):u.t("publicFungibleDetailContract"),n);let r=_o(e.marketCap);W(o,u.t("publicFungibleDetailMarketCap"),r);let i=Xe(e.totalSupply);W(o,u.t("publicFungibleDetailTotalSupply"),i);let l=Xe(e.circulatingSupply);W(o,u.t("publicFungibleDetailCirculatingSupply"),l);let c=mi(e.maxSupply);W(o,u.t("publicFungibleDetailMaxSupply"),c);let p=Xe(e.holders);W(o,u.t("publicFungibleDetailHolders"),p);let m=di(e.mintExtensions?.find(Jr)?.state.newerTransferFee.transferFeeBasisPoints);return W(o,u.t("sendFungibleTransferFee"),m,u.t("sendFungibleTransferFeeToolTip"),t),o}function Yr(e){if(!e)return;let t=[],n=_o(e.volume24hUSD),o=je(e.volume24hUSDChangePercentage);Jt(t,u.t("publicFungibleDetailVolume"),n,o);let r=Xe(e.trades24h),i=je(e.trades24hChangePercentage);Jt(t,u.t("publicFungibleDetailTrades"),r,i);let l=Xe(e.uniqueWallets24h),c=je(e.uniqueWallets24hChangePercentage);return Jt(t,u.t("publicFungibleDetailTraders"),l,c),t.length>0?t:void 0}function jr(e,t){if(!e)return;let n=[],o=je(e.top10HoldersPercent);W(n,u.t("publicFungibleDetailTop10Holders"),o,u.t("publicFungibleDetailTop10HoldersTooltip"),t);let r=Dt(e.mintable);W(n,u.t("publicFungibleDetailMintable"),r,u.t("publicFungibleDetailMintableTooltip"),t);let i=Dt(e.mutableMetadata);W(n,u.t("publicFungibleDetailMutableInfo"),i,u.t("publicFungibleDetailMutableInfoTooltip"),t);let l=Dt(e.ownershipRenounced);W(n,u.t("publicFungibleDetailOwnershipRenounced"),l,u.t("publicFungibleDetailOwnershipRenouncedTooltip"),t);let c=Mo(e.updateAuthority);W(n,u.t("publicFungibleDetailUpdateAuthority"),c,u.t("publicFungibleDetailUpdateAuthorityTooltip"),t);let p=Mo(e.freezeAuthority);W(n,u.t("publicFungibleDetailFreezeAuthority"),p,u.t("publicFungibleDetailFreezeAuthorityTooltip"),t);let m=Vo(e.mintExtensions?.find(Xr)?.state.delegate);W(n,u.t("permanentDelegateTitle"),m,u.t("permanentDelegateTooltipValue"),t);let d=Vo(e.mintExtensions?.find(Zr));return W(n,u.t("sendFungibleNonTransferable"),d,u.t("sendFungibleNonTransferableToolTip"),t),n.length>0?n:void 0}function Xr(e){return e.extension==="permanentDelegate"}function Jr(e){return e.extension==="transferFeeConfig"}function Zr(e){return e.extension==="nonTransferable"}function ei(e){let{fungibleAnalytics:t,caip19:n,canBuy:o,canSwap:r,entryPoint:i,fungibleKey:l,openBuy:c,openLink:p,openSwapper:m,showSummaryTooltip:d,title:b,onShare:g,onSend:S,onReceive:w,copyAddress:C,includeUgcItemTypes:k,refetchBondingCurve:P,onUnwrapWrappedSol:U,onStakeSol:h,onNativeMintLSTPress:y,onStakeLSTPress:L,onUnstakeLSTPress:E}=e,F=re(),v=(0,z.useMemo)(()=>Se(n),[n]),{data:N}=ae(),G=N?.isReadOnly??!1,{data:D,refetch:K,isError:ee}=ze({caip19:n}),T=gi(l,v),{fungibles:M,isLoadingTokens:te,refetch:_}=pe({keys:T}),B=pi(M),A=fi(D,B,b),R=ii({fungible:B,fungibleDetail:D,fungibleCaip19:v}),de=ci({fungible:R,accountId:N?.identifier}),{onMarkNotSpamAsync:me}=Go({fungible:R,accountId:N?.identifier,fungibleCaip19:v}),{partialErrorMessage:ue,shouldShowPartialError:fe}=Me({chainAddresses:N?.addresses,networkID:v.chainId}),oe=(0,z.useRef)(!1);(0,z.useEffect)(()=>{!oe.current&&!te&&(oe.current=!0,t.onPublicFungiblePageOpen({caip19:n,uiContext:{name:i},hasBalance:!!R?.data.balance.gt(0)}))},[t,n,R,i,te]);let Ge=(0,z.useCallback)(()=>{Promise.all([K(),_(),P()])},[K,_,P]),x=[];fe&&x.push({type:"partialError",data:ue}),ni(x,{fungible:R,derivedSpamStatus:de,onMarkNotSpamAsync:me,includeUgcItemTypes:k,caip19:n,hasFungibleDetail:!!D}),x.push({type:"priceHistory",data:{caip19:n}});let le=ri({caip19:n,canBuy:o,canSwap:r,fungible:R,fungibleCaip19:v,openBuy:c,openLink:p,openSwapper:m,onSend:S,onReceive:w,title:A,onShare:g,isReadOnly:G,derivedSpamStatus:de,accountId:N?.identifier,onUnwrapWrappedSol:U,onStakeSol:h,onNativeMintLSTPress:y,onStakeLSTPress:L,onUnstakeLSTPress:E}),Fe=le.primary.length>0,Ze=!G&&le.primary.length===0&&le.more.length>0;Fe&&x.push({type:"cta",data:{actions:le}}),B&&(x.push({type:"balance",data:{status:"success",fungible:B,holdings:void 0}}),!G&&B.type==="SolanaNative"&&(x.push({type:"sectionHeader",data:u.t("assetDetailStakingLabel")}),x.push({type:"staking",data:B}))),ee&&x.push({type:"error",data:void 0}),D?.description&&x.push({type:"sectionHeader",data:u.t("publicFungibleDetailAbout")},{type:"about",data:D.description.trim()});let he=zr(D,{copyAddress:C,showSummaryTooltip:d});he&&(x.push({type:"sectionHeader",data:u.t("publicFungibleDetailInfo")}),k?x.push({type:"tableWithBondingCurveProgress",data:{caip19:n,rows:he,key:"info"}}):x.push({type:"table",data:{key:"info",rows:he}}));let et=Yr(D);et&&x.push({type:"sectionHeader",data:u.t("publicFungibleDetailPerformance")},{type:"table",data:{key:"performance",rows:et}});let tt=jr(D,d);tt&&x.push({type:"sectionHeader",data:u.t("publicFungibleDetailSecurity")},{type:"table",data:{key:"security",rows:tt}});let nt=(0,z.useCallback)(It=>{F.capture("onPublicFungibleLinkOpen",{data:{url:It}})},[F]);return D?.links&&x.push({type:"links",data:{links:D.links,trackAction:nt}}),B&&B.data.balance.gt(0)&&de!=="POSSIBLE_SPAM"&&x.push({type:"transactionHistory",data:{fungible:B,caip19:n,fungibleKey:(T??[]).find(Boolean)}}),{title:A,caip19:n,fungible:B,items:x,actions:Ze?le:void 0,showMoreInHeader:Ze,isLoading:te||!R,refetch:Ge}}var ti=[{type:"receive",text:"commandReceive"},{type:"send",text:"commandSend"},{type:"swap",text:"commandSwap"},{type:"buy",text:"commandBuy"},{type:"share",text:"commandShare"},{text:"assetDetailStakeSOL",singleWordAltText:"commandStake",type:"stakeSol",menuOnly:!0},{text:"commandMintLST",singleWordAltText:"commandMintLST",type:"mintLST",menuOnly:!0},{text:"assetDetailUnwrapAll",singleWordAltText:"commandUnwrap",type:"unwrapWrappedSol",menuOnly:!0},{text:"commandStake",singleWordAltText:"commandStake",type:"stake",menuOnly:!0},{text:"commandUnstake",singleWordAltText:"commandUnstake",type:"unstake",menuOnly:!0},{text:"commandReportAsSpam",type:"reportAsSpam",isDestructive:!0,menuOnly:!0},{text:"commandReportAsNotSpam",type:"reportAsNotSpam",isDestructive:!0,menuOnly:!0},{text:"",singleWordAltText:"commandView",type:"viewOnExplorer",menuOnly:!0}];function ni(e,{fungible:t,derivedSpamStatus:n,onMarkNotSpamAsync:o,includeUgcItemTypes:r,caip19:i,hasFungibleDetail:l}){t&&n&&n==="POSSIBLE_SPAM"?e.push({type:"spamTreatment",data:{fungible:t,spamStatus:n,markAsNotSpam:o}}):r?e.push({type:"ugcWarningBanner",data:{caip19:i,spamStatus:n}}):!r&&l&&n!=="VERIFIED"&&e.push({type:"unverifiedBanner"})}function oi(e,t){return ti.reduce((n,o)=>{let r=e[o.type];return r===void 0?n:!!("enabled"in r?r.enabled:r.onClick)&&r.onClick?[...n,{...o,onClick:r.onClick,text:r.text?r.text:t(o.text),..."singleWordAltText"in o&&{singleWordAltText:t(o.singleWordAltText)},..."typeSpecificMetadata"in r&&{typeSpecificMetadata:r.typeSpecificMetadata}}]:n},[])}function ri(e){let{accountId:t,caip19:n,canBuy:o,canSwap:r,fungible:i,fungibleCaip19:l,openBuy:c,openLink:p,openSwapper:m,onReceive:d,onSend:b,title:g,onShare:S,onUnwrapWrappedSol:w,onStakeSol:C,derivedSpamStatus:k,onNativeMintLSTPress:P,onStakeLSTPress:U,onUnstakeLSTPress:h,isReadOnly:y}=e,{t:L}=$(),{data:E}=dt(),{data:[F]}=ke(["prefer-phantom-dot-com"]),{onMarkAsSpam:v,onMarkNotSpam:N}=Go({fungible:i,accountId:t,fungibleCaip19:l}),G=Lo(d,i),D=Lo(b,i),K=i?.type==="CompressedSPL",ee=r(l.chainId)&&k&&!bt(k)&&!K,T=(0,z.useCallback)(()=>{n&&m({buyFungibleCaip19:n})},[n,m]),M=ui({walletAddress:i?.data.walletAddress,fungibleCaip19:l}),te=(0,z.useCallback)(()=>{M?.explorerUrl&&p(M?.explorerUrl)},[M?.explorerUrl,p]),_=!!i&&i.data.balance.gt(0),B=j(i?.type),A={caip19:n,hasBalance:_,title:g},R=Ct.getUrlFromFungible({caip19:l,isAnalyticsOptedOut:!!E,useDotCom:F}),me=i&&i.type==="SPL"&&i?.data.tokenAddress===Pe.toBase58()&&!y&&i.data.balance.isGreaterThan(O(Rn).dividedBy(J.LAMPORTS_PER_SOL)),ue=li(),fe=oi({receive:{enabled:!y&&(_||B),onClick:G,typeSpecificMetadata:A},send:{enabled:!y&&_&&!K,onClick:D,typeSpecificMetadata:A},swap:{enabled:!y&&ee,onClick:T,typeSpecificMetadata:A},buy:{enabled:!y&&o,onClick:c,typeSpecificMetadata:A},unwrapWrappedSol:{enabled:me,onClick:w?()=>w(ue):void 0},stakeSol:{enabled:!y&&i?.type==="SolanaNative",onClick:C,typeSpecificMetadata:A},mintLST:{enabled:!y&&_,onClick:P},stake:{enabled:!y&&_,onClick:U},unstake:{enabled:!y&&_,onClick:h},share:{onClick:R?()=>S(R):void 0,typeSpecificMetadata:A},reportAsSpam:{enabled:k&&!bt(k),onClick:v,typeSpecificMetadata:A},reportAsNotSpam:{enabled:k&&bt(k),onClick:N,typeSpecificMetadata:A},viewOnExplorer:{enabled:!!M,onClick:te,text:L("assetDetailViewOnExplorer",{explorer:M?.explorerName}),typeSpecificMetadata:{...A,origin:at(M?.explorerUrl)}}},L);return kt(fe,4)}function ii({fungible:e,fungibleDetail:t,fungibleCaip19:n}){if(e)return e;if(!t)return;let o="address"in n?si(n,t):ai(n,t);if(o)return o;we.captureError(new Error(`Fungible type could not be derived from ${JSON.stringify(n)}`),"fungibles")}function si(e,t){let n=Pt(e),o=t.address??e.address,r={amount:"0",balance:O(0),chain:t.chain,decimals:t.decimals,logoUri:t.logoURI??t.logoUri,name:t.name??"Unknown",symbol:t.symbol??"Unknown",key:o,walletAddress:"",spamStatus:t.spamStatus};if("SPL"===n)return{type:"SPL",data:{...r,mintAddress:o,programId:"",splTokenAccountPubkey:""}};if("ERC20"===n)return{type:"ERC20",data:{...r,contractAddress:o}};if("BRC20"===n)return{type:"BRC20",data:{...r,firstCreatedInscriptionId:o}}}function ai(e,t){let n=Pt(e),o={amount:"0",balance:O(0),chain:t.chain,decimals:t.decimals,logoUri:t.logoURI??t.logoUri,name:t.name??"Unknown",symbol:t.symbol??"Unknown",key:n,walletAddress:"",spamStatus:t.spamStatus};switch(e.slip44){case"501":return{type:"SolanaNative",data:o};case"0":return{type:"BitcoinNative",data:o};case"60":return{type:"EthereumNative",data:o};case"8453":return{type:"BaseNative",data:o};case"143":return{type:"MonadNative",data:o};case"966":return{type:"PolygonNative",data:o};case"784":return;default:{let r=new Error("Missing native token implementation when creating fake a Fungible");throw st(e.slip44,r),r}}}function ui({walletAddress:e,fungibleCaip19:t}){let{data:n}=De(),o=t.resourceType==="address"?t.address:e;if(!o||!n)return;let r=t.chainId,i=n.explorers[r]??Ee.get(r).defaultExplorer,l=Ue({networkID:r,endpoint:"address",explorerType:i,param:o});return{explorerName:Re[i],explorerUrl:l}}function Lo(e,t){return(0,z.useMemo)(()=>{if(e)return()=>{t&&e(t)}},[e,t])}function li(){let{mutateAsync:e}=Qt();return e}function ci({fungible:e,accountId:t}){let{data:n}=vt(t??"");return e?Bn(n,{key:e.data.key,spamStatus:e?.data.spamStatus??"NOT_VERIFIED"}):void 0}function Go({fungible:e,accountId:t,fungibleCaip19:n}){let o=re(),{mutateAsync:r}=Ot(),i=(0,z.useCallback)(async()=>{!e||!t||(o.capture("fungiblesReportAsNotSpam",{data:{caip19:pt(n)}}),await r({accountId:t,mutations:[{fungibleKey:e.data.key,visibility:"visible:reported_notSpam"}]}))},[t,e,r,o,n]),l=(0,z.useCallback)(()=>{i()},[i]);return{onMarkAsSpam:(0,z.useMemo)(()=>{if(e&&j(e.type))return;let p=Jn({analytics:o,fungible:e,accountId:t,setVisibilityOverrides:r});if(p)return()=>{p()}},[t,e,r,o]),onMarkNotSpam:l,onMarkNotSpamAsync:i}}var pi=e=>e&&e.length>0?e.reduce((t,n)=>!t||t.data.balance.lt(n.data.balance)?n:t,void 0):void 0,Mo=e=>e?ce(e):void 0,di=e=>e?je(Ft(e)):void 0,je=e=>e?ut(e/100):void 0,Xe=e=>e?Le(e):void 0,_o=e=>e?Ce(e,{compact:!0}):void 0,mi=e=>e===null?"\u221E":e===void 0?void 0:Le(e),Dt=e=>e==null?void 0:e?u.t("nounYes"):u.t("nounNo"),Vo=e=>e==null?void 0:Dt(!!e);function W(e,t,n,o,r){n&&e.push({label:t,value:n,tooltipContent:o,handleTooltip:r?()=>{r(t,o??"")}:void 0})}function Jt(e,t,n,o,r,i){!n||!o||e.push({label:t,value:n,change:o,tooltipContent:r,handleTooltip:i?()=>{i(t,r??"")}:void 0})}var fi=(e,t,n)=>e?.name||t?.data.name||n||"",gi=(e,t)=>e?[e]:t?At(t):void 0;s();a();var ie=I(V());s();a();var Ho=I(V()),Wo=({networkID:e,transactionUnitAmount:t,onTransactionSpeedChange:n})=>{let{status:o,data:r}=St({networkID:e}),{transactionSpeed:i}=q(c=>c.sendFormValues),l=(0,Ho.useMemo)(()=>{if(o!=="error"&&r&&i)return()=>n(i,t)},[n,i,t,r,o]);return{transactionSpeed:i,openTransactionSettings:l}};var yi={accentAlert:"#EB3742",white:"#FFFFFF"};var bi=({networkID:e,recipient:t,uiRecipient:n,displayUiRecipient:o,networkName:r,networkFee:i,networkFeeErrorMsg:l,networkFeeRow:c,displayFeeTooltip:p,transferFeePercent:m,solanaMetadata:d,valueLoader:b,fontColors:g,openTransactionSettings:S,onNetworkFeeDescriptionPress:w,onTransferFeePress:C})=>(0,ie.useMemo)(()=>{let k=o?n:t;return[H.isSolanaNetworkID(e)&&d.label?{label:u.t("sendConfirmationLabel"),value:d.label}:void 0,H.isSolanaNetworkID(e)&&d.message?{label:u.t("sendConfirmationMessage"),value:d.message}:void 0,{label:u.t("transactionsTo"),value:k,isAddress:!Tn(k),type:"address"},{label:u.t("sendFungibleSummaryNetwork"),value:r},{label:u.t("sendFungibleSummaryNetworkFee"),value:i.length>0?i:l?null:b,color:l?g.accentAlert:g.white,leftSubtext:l||void 0,leftSubtextColor:g.accentAlert,rightSubtext:c?.rightSubtext,tooltipContent:p?u.t("networkFeesTooltipDescription",{chainName:r})||"":void 0,onClick:p?S:void 0,onPress:p?S:void 0,handleTooltip:p?w:void 0},m?{label:u.t("sendFungibleTransferFee"),value:`${m}%`,tooltipContent:u.t("sendFungibleTransferFeeToolTip")||"",handleTooltip:C}:void 0,d.memo?{label:u.t("sendMemo"),value:d.memo.replace(/(\r\n|\n|\r)/gm," ")}:void 0].filter(it)},[p,o,g.accentAlert,g.white,i,l,c?.rightSubtext,e,r,w,C,S,t,d.label,d.memo,d.message,m,n,b]),Si=({hasSufficientFunds:e,isTransactionUnitError:t,hasGasEstimationFailed:n,nativeTokenSymbol:o})=>t?u.t("transactionGasLimitError"):n?u.t("transactionGasEstimationError"):e==="insufficient"?u.t("transactionNotEnoughNative",{nativeTokenSymbol:o}):"",Ti=e=>{let{networkID:t,displayUiRecipient:n=!0,valueLoader:o,fontColors:r=yi,onTransactionSpeedChange:i,onNetworkFeeDescriptionPress:l,onTransferFeePress:c,onSubmissionCheckFail:p}=e,m=re(),d=H.getNetworkName(t),b=q(),g=q(_e=>_e.sendFormValues),S=q(_e=>_e.setSendFormValues),{amountAsset:w,recipient:C,recipientHandle:k,solana:P}=g,U=k||C,h=q(_e=>_e.fungibleKey),{fungible:y}=Ae({key:h,splTokenAccount:b.splTokenAccount}),L=y?y.data.tokenAddress:void 0,E=y?.data.symbol??(L?ce(L,4):u.t("tokenRowUnknownToken")),F=y?.data.logoUri??"",v=an(w,y?.data.decimals??0),N=v.toString(),G=(0,ie.useMemo)(()=>y?wt(y):void 0,[y]),{data:D}=Mn({query:G?{data:G}:void 0}),K=Ce(Nn(v.toNumber(),D?.usd??0)),ee=(0,ie.useMemo)(()=>({key:h,amount:N,symbol:E||"",logoUri:F,usdPrice:K}),[h,F,E,K,N]),{data:T}=Ie({address:y?.data.walletAddress,networkID:t}),{getExistingAccount:M,getKnownAddressLabel:te}=mn(),_=M(C),B=te(C,t),A=(0,ie.useMemo)(()=>dn(C,_,B,4),[_,C,B]);U=B?C:U,Kt(T,"SEND_FUNGIBLE");let{data:R}=Bt(b),{data:de,isError:me}=On(R),{transactionSpeed:ue,openTransactionSettings:fe}=Wo({networkID:t,transactionUnitAmount:de,onTransactionSpeedChange:i}),{data:oe,isFetched:Ge,isSuccess:x}=Ln({networkID:t,multichainTransaction:R,transactionSpeed:ue,queryOptions:{refetchInterval:!1}}),{data:le}=Gn(y?.data.chain.id,oe),Fe=Hn(T),Ze=y&&j(y.type)?w:new ot(0),{hasSufficientFunds:he,isLoading:et,nativeTokenSymbol:tt}=_t(t,T?.addressType,oe,Ze),nt=Qn({networkID:t,gasEstimation:oe,gasEstimationPrice:le}),It=zt(oe,u.t),Ko=ht(y),{data:Zt}=St({networkID:t}),Qo=(0,ie.useMemo)(()=>Be.get(t).displayFeeTooltip&&Object.keys(Zt??{}).length>1,[Zt,t]);(0,ie.useEffect)(()=>{m.capture("sendAssetSummary",{data:{type:"fungible",symbol:E??"",networkId:t,chainId:H.getChainID(t)}})},[t,E,m]);let qo=Ge&&!x,$o=!!oe&&!et&&he!=="insufficient"&&!!nt,zo=Si({hasSufficientFunds:he,isTransactionUnitError:me,hasGasEstimationFailed:qo,nativeTokenSymbol:tt}),Yo=(0,ie.useCallback)(()=>!R||!T||!h?!1:Fe?(m.capture("showFungibleTxSubmissionFailureModal"),p(Fe),!1):(S({...g,multichainTransaction:R}),!0),[R,T,h,Fe,S,g,m,p]),jo=bi({networkID:t,recipient:U,uiRecipient:A,networkName:d,networkFee:nt,networkFeeErrorMsg:zo,networkFeeRow:It,transferFeePercent:Ko,solanaMetadata:P,displayFeeTooltip:Qo,valueLoader:o,fontColors:r,displayUiRecipient:n,openTransactionSettings:fe,onNetworkFeeDescriptionPress:l,onTransferFeePress:c}),Xo=(0,ie.useMemo)(()=>({title:u.t("sendConfirmationPrimaryText"),send:u.t("commandSend"),cancel:u.t("commandCancel")}),[]);return{sender:T,recipientAddress:C,uiRecipient:A,token:ee,networkName:d,canSend:$o,txSubmissionCheckFailureModal:Fe,summaryRows:jo,i18nStrings:Xo,multichainTransaction:R,prepareSubmission:Yo}};s();a();var Je=I(V());var Ai=({networkID:e,isTxConfirmError:t,isTxSubmissionError:n,isTxConfirmed:o,isTxSubmitted:r})=>{let i=Be.get(e).sendSuccessCondition,l=(0,Je.useMemo)(()=>{switch(i){case"SUBMISSION":return n?"ERROR":r?"SUCCESS":"LOADING";case"CONFIRMATION":return n||t?"ERROR":o?"SUCCESS":"LOADING"}},[t,o,n,r,i]),c=i==="CONFIRMATION"?"sendStatusConfirmedSuccessTitle":"sendStatusSubmittedSuccessTitle",p=(0,Je.useMemo)(()=>{switch(l){case"LOADING":return u.t("sendStatusLoadingTitle");case"ERROR":return u.t("sendStatusErrorTitle");case"SUCCESS":return u.t(c)}},[l,c]),{isError:m,isSuccess:d}=(0,Je.useMemo)(()=>({isError:l==="ERROR",isSuccess:l==="SUCCESS"}),[l]),{transactionSpeed:b="standard"}=q(S=>S.sendFormValues),g=(0,Je.useMemo)(()=>{let S=bn.get(e).transactionSpeedDescription(b);return S?u.t(S):""},[e,b]);return{title:p,isError:m,isSuccess:d,sendSuccessCondition:i,estimatedTime:g}};s();a();var Fi=I(V());s();a();export{io as a,rr as b,Ot as c,q as d,no as e,wr as f,kr as g,Vt as h,Qt as i,xr as j,$t as k,Ur as l,Do as m,_t as n,dr as o,Or as p,zt as q,Wr as r,ei as s,Qe as t,mo as u,Kt as v,Ti as w,Ai as x,hr as y};
//# sourceMappingURL=chunk-V5T43K7V.js.map
