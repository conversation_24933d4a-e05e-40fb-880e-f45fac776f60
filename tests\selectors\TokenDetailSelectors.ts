export class TokenDetailSelectors {
  // Buy/Sell buttons
  static buyButton = 'button:has-text("Buy")';
  static sellButton = 'button:has-text("Sell")';

  // Buy/Sell tabs
  static buyTab = 'button.chakra-button:has-text("Buy")';
  static sellTab = 'button.chakra-button:has-text("Sell")';

  // Market/Limit tabs
  static marketTab = 'p:has-text("Market")';
  static limitTab = 'p:has-text("Limit")';

  // Amount input
  static amountInput = 'input.none-border-input';

  // Percentage buttons
  static percentageButtons = '.css-19mruwt p';
  static maxAmountButton = 'div.css-izrwdw p, p:has-text("100 %"), p.chakra-text.css-5g61yk:has-text("100 %")';
  static percent75Button = 'p:has-text("75 %"), p.chakra-text.css-5g61yk:has-text("75 %")';
  static percent50Button = 'p:has-text("50 %"), p.chakra-text.css-5g61yk:has-text("50 %")';
  static percent25Button = 'p:has-text("25 %"), p.chakra-text.css-5g61yk:has-text("25 %")';

  // Slippage settings
  static slippageInput = 'input[placeholder="0.00%"]';
  static priorityFeeInput = 'input[placeholder="Fee"]';
  static mevProtectionToggle = '.css-6x69oj';
  static tipInput = 'input[placeholder="0"]';

  // Transaction success
  static successMessage = 'text="Transaction successful", text="wallets traded successfully", div:has-text("wallets traded successfully")';
  static closeSuccessButton = 'button:has-text("Close"), button[aria-label="Close"]';
  static walletsTradedSuccessMessage = 'text=/.*wallets traded successfully.*/';
  static tradingSuccessToast = 'div.chakra-alert:has-text("traded successfully")';

  // Thông tin cơ bản token
  static tokenContainer = '//div[@id="token-info-id"]';
  static tokenIcon = '//div[contains(@class, "css-1ahtk00")]/img';
  static tokenDexIcon = '//span[contains(@class, "css-1pf2w37")]/img';
  static tokenName = '//div[@id="base-token-name"]/span[contains(@class, "chakra-text")]';
  static tokenSymbol = '//p[@id="symbol-el"]';

  // Thông tin giá và biến động
  static tokenPrice = '//div[contains(@class, "css-1q1iys5")]/p[contains(@class, "chakra-text")]';
  static percentageChange = '//div[contains(@class, "css-1uyggyb")] | //div[contains(@class, "css-1pof48l")]//div[contains(@class, "css-1q1iys5")]';
  static timeframe = '//div[contains(@class, "css-1pof48l")]/p[contains(@class, "chakra-text")]';

  // Các metrics trong header
  static marketCap = '//div[contains(@class, "css-mtpvyb")]//div[contains(@class, "css-lemfo4")]';
  static liquidity = '//div[contains(@class, "css-1mx63gp")]//div[contains(@class, "css-lemfo4")]';
  static holders = '//div[contains(@class, "css-149i3s9")]//div[contains(@class, "css-lemfo4")]';
  static dev = '//div[contains(@class, "css-19ktzvf")]//div[contains(@class, "css-lemfo4")]';
  static insiders = '(//div[contains(@class, "css-149i3s9")]//div[contains(@class, "css-lemfo4")])[2]';
  static top10 = '//div[contains(@class, "css-vli8vr")]//div[contains(@class, "css-lemfo4")]';
  static snipers = '//div[contains(@class, "css-zxozyj")]//div[contains(@class, "css-1v0aycn")]//p[contains(@class, "css-t4khrt")]';
  static security = '//div[contains(@class, "css-zxozyj")]//div[contains(@class, "css-k9f27k")]//p[contains(@class, "css-xi606m")]';

  // Buttons và links
  static contractCopyButton = '//div[contains(@class, "css-1k7htcp")]//div[contains(@class, "css-1ud0ogq")]';
  static linkButton = '//div[contains(@class, "css-1k7htcp")]//div[contains(@class, "css-xbykej")]';
  static socialLinks = '//div[contains(@class, "css-nn803e")]//a[contains(@class, "chakra-link")]';

  // Thông tin khác
  static tokenAge = '(//p[contains(@class, "css-1gepi3p")])[2]';
  static progressBar = '//div[contains(@class, "css-aafb3j")]//div[contains(@class, "css-1kfjbl4")]';

  // Legacy selectors (giữ lại để tương thích ngược)
  static tokenLiquidity = '.token-liquidity, div[data-testid="token-liquidity"]';
  static tokenVolume = '.token-volume, div[data-testid="token-volume"]';

  // Controls
  static addToWatchlistButton = '//button[contains(text(), "Add to Watchlist") or @aria-label="Add to watchlist"]';
  static removeFromWatchlistButton = '//button[contains(text(), "Remove from Watchlist") or @aria-label="Remove from watchlist"]';
  static advancedSettingsButton = '//button[contains(@class, "chakra-accordion__button") or starts-with(@id, "accordion-button")]';
  static advancedSettingsArea = '//div[contains(@class, "chakra-accordion__item")]';

  // Buy/Sell action button
  static actionButton = 'button:has-text("Buy")';
  static buyActionButton = 'button.chakra-button.css-17anub0:has(p:has-text("Buy")), button.chakra-button:has(p:has-text("Buy"))';
  static sellActionButton = 'button.chakra-button.css-17anub0:has(p:has-text("Sell")), button.chakra-button:has(p:has-text("Sell"))';
  static buyTokenButton = (tokenSymbol: string) => `button.chakra-button:has(p:has-text("Buy ${tokenSymbol}"))`;
  static sellTokenButton = (tokenSymbol: string) => `button.chakra-button:has(p:has-text("Sell ${tokenSymbol}"))`;

  // Buy button với lightning icon
  static buyLightningButton = 'button.chakra-button:has(svg path[d*="L8.00001 2.44668"])';
  static buyPepeButton = 'button.chakra-button.css-17anub0:has(p:has-text("Buy PEPE"))';
  static buyTokenWithIcon = (tokenSymbol: string) => `button.chakra-button:has(span.chakra-button__icon):has(p:has-text("Buy ${tokenSymbol}"))`;
  static finalBuyButton = 'button.chakra-button.css-17anub0';

  // Sell button với icon
  static sellLightningButton = 'button.chakra-button:has(svg path[d*="L8.00001 2.44668"]):has-text("Sell")';
  static sellPepeButton = 'button.chakra-button.css-dayaey:has-text("Sell PEPE")';
  static sellTokenWithIcon = (tokenSymbol: string) => `button.chakra-button:has(span.chakra-button__icon):has-text("Sell ${tokenSymbol}")`;
  static finalSellButton = 'button.chakra-button.css-dayaey';

  // Sell position-based - selector mới thêm
  static positionSellButton = 'div[role="tabpanel"] button.chakra-button:has-text("Sell")';
  static sellPanelButton = 'div[role="tabpanel"]:has(button:has-text("Sell")) button:has-text("Sell")';
  static sellTypedButton = 'button[type="button"].chakra-button:has-text("Sell")';
  static anySellButton = 'button:has(svg path[d*="L8.00001 2.44668"]):has-text("Sell")';

  // Token balance
  static tokenBalance = 'div[role="button"] p'; // Selector chung cho số dư token
  static tokenBalanceAlternative = '.chakra-text:has-text("SOL")'; // Selector thay thế dựa trên text
  static tokenBalanceBySymbol = (symbol: string) => `.chakra-text:has-text("${symbol}")`; // Selector theo symbol token

  // Token header metrics
  static marketCap = 'div:has-text("MCap"), .chakra-text:has-text("MCap")';
  static liquidity = 'div:has-text("Liquidity"), .chakra-text:has-text("Liquidity")';
  static holders = 'div:has-text("Holders"), .chakra-text:has-text("Holders")';
  static dev = 'div:has-text("Dev"), .chakra-text:has-text("Dev")';
  static insiders = 'div:has-text("Insiders"), .chakra-text:has-text("Insiders")';
  static top10 = 'div:has-text("Top 10"), .chakra-text:has-text("Top 10")';
  static snipers = 'div:has-text("Snipers"), .chakra-text:has-text("Snipers")';
  static security = 'div:has-text("Security"), .chakra-text:has-text("Security")';

  // Token 24h percentage change
  static percentageChange = '.chakra-text:has-text("%")';

  // Time period selectors
  static timePeriodSelector = (period: string) => `//button[contains(text(), "${period}")]`;
  static timePeriod5m = '//button[contains(text(), "5m")]';
  static timePeriod1h = '//button[contains(text(), "1h")]';
  static timePeriod6h = '//button[contains(text(), "6h")]';
  static timePeriod3m = '//button[contains(text(), "3m")]';
  static timePeriod1m = '//button[contains(text(), "1m")]';
  static timePeriod5d = '//button[contains(text(), "5d")]';
  static timePeriod1d = '//button[contains(text(), "1d")]';
  static timePeriod24h = '//button[contains(text(), "24H")]';
  static timePeriod7d = '//button[contains(text(), "7D")]';
  static timePeriod30d = '//button[contains(text(), "30D")]';
  static timePeriod90d = '//button[contains(text(), "90D")]';
  static timePeriod1y = '//button[contains(text(), "1Y")]';
  static timePeriodAll = '//button[contains(text(), "ALL")]';

  // Transaction section
  static transactionSection = '//div[contains(@class, "css-1lekzkb")] | //section[.//table] | //div[.//table[contains(@class, "chakra-table")]]';
  static transactionRows = '//table/tbody/tr | //div[@role="row"] | //tr[contains(@class, "css-0")]';

  // Transaction tabs
  static transactionTabs = {
    transactions: '//button[contains(text(), "Transactions")]',
    positions: '//button[contains(text(), "Positions")]',
    openOrders: '//button[contains(text(), "Open Orders")]',
    topTraders: '//button[contains(text(), "Top traders")]',
    holders: '//button[contains(text(), "Holders")]',
    bubblemaps: '//button[contains(text(), "Bubblemaps")]',
    tradeLogs: '//button[contains(text(), "Trade logs")]'
  };

  // Transaction subtabs
  static transactionSubtabs = {
    allTxns: '//button[contains(text(), "All txns")]',
    whale: '//button[contains(text(), "Whale")]',
    smart: '//button[contains(text(), "Smart")]',
    kol: '//button[contains(text(), "KOL")]',
    dev: '//button[contains(text(), "Dev")]',
    sniper: '//button[contains(text(), "Sniper")]',
    insider: '//button[contains(text(), "Insider")]',
    following: '//button[contains(text(), "Following")]',
    myWallets: '//button[contains(text(), "My wallets")]'
  };
}

