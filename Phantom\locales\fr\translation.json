{"commandAdd": "Ajouter", "commandAccept": "Accepter", "commandApply": "Appliquer", "commandApprove": "Approuver", "commandAllow": "Autoriser", "commandBack": "Retour", "commandBuy": "<PERSON><PERSON><PERSON>", "commandCancel": "Annuler", "commandClaim": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandClaimReward": "Récupérez votre récompense", "commandClear": "<PERSON><PERSON><PERSON><PERSON>", "commandClose": "<PERSON><PERSON><PERSON>", "commandConfirm": "Confirmer", "commandConnect": "Connecter", "commandContinue": "<PERSON><PERSON><PERSON>", "commandConvert": "Convertir", "commandCopy": "<PERSON><PERSON><PERSON>", "commandCopyAddress": "Co<PERSON>r l'adresse", "commandCopyTokenAddress": "Co<PERSON>r l'adresse du jeton", "commandCreate": "<PERSON><PERSON><PERSON>", "commandCreateTicket": "<PERSON><PERSON><PERSON> un ticket", "commandDeny": "Refuser", "commandDismiss": "<PERSON><PERSON><PERSON>", "commandDontAllow": "Ne pas autoriser", "commandDownload": "Télécharger", "commandEdit": "Modifier", "commandEditProfile": "Modifier le profil", "commandEnableNow": "Activer maintenant", "commandFilter": "<PERSON><PERSON><PERSON>", "commandFollow": "Suivre", "commandHelp": "Aide", "commandLearnMore": "En savoir plus", "commandLearnMore2": "Plus d'informations", "commandMint": "Mint", "commandMore": "Plus", "commandNext": "Suivant", "commandNotNow": "Pas maintenant", "commandOpen": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandOpenSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "commandPaste": "<PERSON><PERSON>", "commandReceive": "Recevoir", "commandReconnect": "Reconnecter", "commandRecordVideo": "Enregistrer une vidéo", "commandRequest": "<PERSON><PERSON><PERSON>", "commandRetry": "<PERSON><PERSON><PERSON><PERSON>", "commandReview": "Vérifier", "commandRevoke": "Révoquer", "commandSave": "Enregistrer", "commandScanQRCode": "Scanner un code QR", "commandSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSelectMedia": "Sélectionner un fichier multimédia", "commandSell": "<PERSON><PERSON><PERSON>", "commandSend": "Envoyer", "commandShare": "Partager", "commandShowBalance": "<PERSON><PERSON><PERSON><PERSON> le solde", "commandSign": "Signer", "commandSignOut": "Sign Out", "commandStake": "<PERSON><PERSON><PERSON>", "commandMintLST": "<PERSON><PERSON>", "commandSwap": "<PERSON><PERSON><PERSON>", "commandSwapAgain": "Échanger à nouveau", "commandTakePhoto": "<PERSON><PERSON><PERSON> une photo", "commandTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "commandViewTransaction": "Voir la transaction", "commandReportAsNotSpam": "Signaler comme non spam", "commandReportAsSpam": "Signaler comme spam", "commandPin": "<PERSON><PERSON><PERSON>", "commandBlock": "Bloquer", "commandUnblock": "Débloquer", "commandUnstake": "<PERSON><PERSON><PERSON> l'enjeu", "commandUnpin": "<PERSON><PERSON><PERSON><PERSON>", "commandHide": "Masquer", "commandUnhide": "<PERSON><PERSON><PERSON><PERSON>", "commandBurn": "<PERSON><PERSON><PERSON><PERSON>", "commandReport": "Signaler", "commandView": "Voir", "commandProceedAnywayUnsafe": "Continuer quand même (non sécurisé)", "commandUnfollow": "Ne plus suivre", "commandUnwrap": "<PERSON><PERSON><PERSON><PERSON>", "commandConfirmUnsafe": "Confirmer (non sécurisé)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, confirmer (non sécurisé)", "commandConfirmAnyway": "Confirmer quand même", "commandReportIssue": "Signaler un problème", "commandSearch": "<PERSON><PERSON><PERSON>", "commandShowMore": "Afficher plus", "commandShowLess": "Affiche<PERSON> moins", "pastParticipleClaimed": "Récupérée", "pastParticipleCompleted": "Terminée", "pastParticipleCopied": "<PERSON><PERSON><PERSON>", "pastParticipleDone": "<PERSON><PERSON><PERSON><PERSON>", "pastParticipleDisabled": "Désactivé", "pastParticipleRequested": "<PERSON><PERSON><PERSON>", "nounName": "Nom", "nounNetwork": "<PERSON><PERSON><PERSON>", "nounNetworkFee": "Frais du réseau", "nounSymbol": "Symbole", "nounType": "Type", "nounDescription": "Description", "nounYes": "O<PERSON>", "nounNo": "Non", "amount": "<PERSON><PERSON>", "limit": "Limite", "new": "Nouveau", "gotIt": "<PERSON><PERSON><PERSON>", "internal": "Interne", "reward": "Récompense", "seeAll": "Tout voir", "seeLess": "Voir moins", "viewAll": "<PERSON><PERSON> afficher", "homeTab": "Accueil", "collectiblesTab": "Objets à collectionner", "swapTab": "<PERSON><PERSON><PERSON>", "activityTab": "Activité", "exploreTab": "Explorateur", "accountHeaderConnectedInterpolated": "Vous êtes connecté à {{origin}}", "accountHeaderConnectedToSite": "Vous êtes connecté à ce site", "accountHeaderCopyToClipboard": "Copier dans le presse-papier", "accountHeaderNotConnected": "Vous n'êtes pas connecté à", "accountHeaderNotConnectedInterpolated": "Vous n'êtes pas connecté à {{origin}}", "accountHeaderNotConnectedToSite": "Vous n'êtes pas connecté à ce site", "accountWithoutEnoughSolActionButtonCancel": "Annuler", "accountWithoutEnoughSolPrimaryText": "Pas assez de SOL", "accountWithoutEnoughSolSecondaryText": "Un compte impliqué dans cette transaction n'a pas assez de SOL. Le compte peut être le vôtre ou celui de quelqu'un d'autre. Cette transaction sera annulée si elle est envoyée.", "accountSwitcher": "Changeur de compte", "addAccountHardwareWalletPrimaryText": "Connecter un portefeuille matériel", "addAccountHardwareWalletSecondaryText": "Utilisez votre portefeuille matériel Ledger", "addAccountHardwareWalletSecondaryTextMobile": "Utilisez votre portefeuille {{supportedHardwareWallets}}", "addAccountSeedVaultWalletPrimaryText": "Connexion au coffre à codes de sauvegarde", "addAccountSeedVaultWalletSecondaryText": "Utilisez un portefeuille du Coffre à codes de sauvegarde", "addAccountImportSeedPhrasePrimaryText": "Importer une Phrase de récupération secrète", "addAccountImportSeedPhraseSecondaryText": "Importer des comptes depuis un autre portefeuille", "addAccountImportWalletPrimaryText": "Importer une clé privée", "addAccountImportWalletSecondaryText": "Importer un compte à chaîne unique", "addAccountImportWalletSolanaSecondaryText": "Importer une clé privée Solana", "addAccountLimitReachedText": "Vous avez atteint la limite de {{accountsCount}} comptes dans Phantom. Veuillez supprimer les comptes inutilisés avant d'en ajouter d'autres.", "addAccountNoSeedAvailableText": "Vous n'avez pas de phrases de sauvegarde disponible. Veuillez en importer une existante pour générer un compte.", "addAccountNewWalletPrimaryText": "<PERSON><PERSON>er un nouveau compte", "addAccountNewWalletSecondaryText": "Gén<PERSON>rer une nouvelle adresse de portefeuille", "addAccountNewMultiChainWalletSecondaryText": "Ajouter un compte à chaînes multiples", "addAccountNewSingleChainWalletSecondaryText": "Ajouter un nouveau compte", "addAccountPrimaryText": "Ajouter/connecter un portefeuille", "addAccountSecretPhraseLabel": "Phrase secrète", "addAccountSeedLabel": "Code de sauvegarde", "addAccountSeedIDLabel": "ID du code de sauvegarde", "addAccountSecretPhraseDefaultLabel": "Phrase secrète {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON>lé privée {{number}}", "addAccountZeroAccountsForSeed": "0 comptes", "addAccountShowAccountForSeed": "Afficher 1 compte", "addAccountShowAccountsForSeed": "Afficher {{numOfAccounts}} comptes", "addAccountHideAccountForSeed": "Masquer 1 compte", "addAccountHideAccountsForSeed": "Masquer {{numOfAccounts}} comptes", "addAccountSelectSeedDescription": "Votre nouveau compte sera généré à partir de cette Phrase secrète", "addAccountNumAccountsForSeed": "{{numOfAccounts}} comptes", "addAccountOneAccountsForSeed": "1 compte", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON><PERSON> un compte", "addAccountReadOnly": "Surveiller une adresse", "addAccountReadOnlySecondaryText": "Suivez n'importe quelle adresse de portefeuille publique", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Adresse EVM", "addAccountBitcoinAddress": "Adresse Bitcoin", "addAccountCreateSeedTitle": "<PERSON><PERSON>er un nouveau compte", "addAccountCreateSeedExplainer": "Votre portefeuille n'a pas encore de phrase secrète ! Pour créer un nouveau portefeuille, nous allons vous générer une phrase de récupération. Notez-la et gardez-la pour vous.", "addAccountSecretPhraseHeader": "Votre phrase secrète", "addAccountNoSecretPhrases": "Aucunes Phrases secrètes disponibles", "addAccountImportAccountActionButtonImport": "Importer", "addAccountImportAccountDuplicatePrivateKey": "Ce compte existe déjà dans votre portefeuille", "addAccountImportAccountIncorrectFormat": "Format incorrect", "addAccountImportAccountInvalidPrivateKey": "Clé privée non valide", "addAccountImportAccountName": "Nom", "addAccountImportAccountPrimaryText": "Importer une clé privée", "addAccountImportAccountPrivateKey": "Clé privée", "addAccountImportAccountPublicKey": "Adresse ou domaine", "addAccountImportAccountPrivateKeyRequired": "La clé privée est obligatoire", "addAccountImportAccountNameRequired": "Le nom est obligatoire", "addAccountImportAccountPublicKeyRequired": "L'adresse publique est requise", "addAccountImportAccountDuplicateAddress": "Cette adresse existe déjà dans votre portefeuille", "addAddressAddressAlreadyAdded": "L'adresse a déjà été ajoutée", "addAddressAddressAlreadyExists": "L'adresse existe déjà", "addAddressAddressInvalid": "L'adresse n'est pas valide", "addAddressAddressIsRequired": "L'adresse est obligatoire", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "Une étiquette est obligatoire", "addAddressLabelPlaceholder": "Étiquette", "addAddressPrimaryText": "Ajouter une adresse", "addAddressToast": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountCostLabelInterpolated": "<PERSON><PERSON> {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Vous possédez déjà ce compte de jeton", "createAssociatedTokenAccountErrorInsufficientFunds": "Fonds insuffisants", "createAssociatedTokenAccountErrorInvalidMint": "Adresse mint non valide", "createAssociatedTokenAccountErrorInvalidName": "Nom non valide", "createAssociatedTokenAccountErrorInvalidSymbol": "Symbole non valide", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Nous n'avons pas pu créer votre compte de jeton. Veuillez réessayer.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Impossible de créer le compte", "createAssociatedTokenAccountErrorUnableToSendMessage": "Nous n'avons pas pu envoyer votre transaction.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Échec de l'envoi de la transaction", "createAssociatedTokenAccountInputPlaceholderMint": "Adresse mint", "createAssociatedTokenAccountInputPlaceholderName": "Nom", "createAssociatedTokenAccountInputPlaceholderSymbol": "Symbole", "createAssociatedTokenAccountLoadingMessage": "Nous créons votre compte de jeton.", "createAssociatedTokenAccountLoadingTitle": "Création du compte de jeton", "createAssociatedTokenAccountPageHeader": "<PERSON><PERSON><PERSON> un compte de jeton", "createAssociatedTokenAccountSuccessMessage": "Votre compte de jeton a bien été créé !", "createAssociatedTokenAccountSuccessTitle": "<PERSON><PERSON>e de <PERSON>on c<PERSON>", "createAssociatedTokenAccountViewTransaction": "Voir la transaction", "assetDetailRecentActivity": "Activité récente", "assetDetailStakeSOL": "Mettre des SOL en jeu", "assetDetailUnknownToken": "Jet<PERSON> inconnu", "assetDetailUnwrapAll": "<PERSON><PERSON>", "assetDetailUnwrappingSOL": "Déballage du SOL", "assetDetailUnwrappingSOLFailed": "Échec du déballage SOL", "assetDetailViewOnExplorer": "Voir dans {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorateur", "assetDetailSaveToPhotos": "Enregistrer dans les photos", "assetDetailSaveToPhotosToast": "Enregistr<PERSON> dans les photos", "assetDetailPinCollection": "É<PERSON>ler la collection", "assetDetailUnpinCollection": "Détacher la collection", "assetDetailHideCollection": "Masquer la collection", "assetDetailUnhideCollection": "<PERSON><PERSON> la collection", "assetDetailTokenNameLabel": "Nom du jeton", "assetDetailNetworkLabel": "<PERSON><PERSON><PERSON>", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "Prix", "collectibleDetailSetAsAvatar": "Définir comme avatar", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Ensemble avatar", "collectibleDetailShare": "Partager un objet de collection", "assetDetailTokenAddressCopied": "<PERSON><PERSON><PERSON> copi<PERSON>", "assetDetailStakingLabel": "<PERSON><PERSON><PERSON>", "assetDetailAboutLabel": "À propos de {{fungibleName}}", "assetDetailPriceDetail": "Détail du prix", "assetDetailHighlights": "Points forts", "assetDetailAllTimeReturn": "Retour à tout moment", "assetDetailAverageCost": "<PERSON><PERSON><PERSON> moyen", "assetDetailPriceHistoryUnavailable": "Historique des prix indisponible pour ce jeton", "assetDetailPriceHistoryInsufficientData": "Historique des prix indisponible pour cette période", "assetDetailPriceDataUnavailable": "Données sur les prix indisponibles", "assetDetailPriceHistoryError": "Erreur lors de la récupération de l'historique des prix", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1J", "assetDetailTimeFrame24h": "Prix 24h", "assetDetailTimeFrame1W": "1S", "assetDetailTimeFrame1M": "1M", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "TOUT", "sendAssetAmountLabelInterpolated": "Disponible : {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Citations", "fiatRampNewQuote": "Nouvelle citation", "assetListSelectToken": "Sélectionner un jeton", "assetListSearch": "<PERSON><PERSON><PERSON>…", "assetListUnknownToken": "Jet<PERSON> inconnu", "buyFlowHealthWarning": "Certains de nos prestataires de paiement connaissent un trafic important. Les dépôts peuvent être retardés de plusieurs heures.", "assetVisibilityUnknownToken": "Jet<PERSON> inconnu", "buyAssetInterpolated": "Acheter {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "L'achat maximum est de {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "L'achat minimum est de {{amount}}", "buyNoAssetsAvailable": "Aucun actif Ethereum ou Polygon disponible", "buyThirdPartyScreenPaymentMethodSelector": "Payer avec", "buyThirdPartyScreenPaymentMethod": "<PERSON><PERSON> le moyen de paiement", "buyThirdPartyScreenChoseQuote": "Saisissez un montant valide pour le devis", "buyThirdPartyScreenProviders": "Fournisseurs", "buyThirdPartyScreenPaymentMethodTitle": "Moyens de paiement", "buyThirdPartyScreenPaymentMethodEmptyState": "Aucun moyen de paiement disponible dans votre région", "buyThirdPartyScreenPaymentMethodFooter": "Les paiements sont effectués par des partenaires du réseau. Les frais peuvent varier. Certains modes de paiement ne sont pas disponibles dans votre région.", "buyThirdPartyScreenProvidersEmptyState": "Aucun fournisseur disponible dans votre région", "buyThirdPartyScreenLoadingQuote": "Chargement du devis...", "buyThirdPartyScreenViewQuote": "Voir le devis", "gasEstimationErrorWarning": "Un problème est survenu lors de l'estimation des frais pour cette transaction. Un échec est possible.", "gasEstimationCouldNotFetch": "Impossible de récupérer l'estimation de l'essence", "networkFeeCouldNotFetch": "Impossible de récupérer les frais de réseau", "nativeTokenBalanceErrorWarning": "Un problème est survenu lors de l'obtention de votre solde de jetons pour cette transaction. Un échec est possible.", "blocklistOriginCommunityDatabaseInterpolated": "Ce site a été signalé dans le cadre d'une <1>base de données communautaire</1> de sites d'hameçonnage et d'arnaques connus. Si vous pensez que le site a été signalé par erreur, <3>vous pouvez faire remonter un problème</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} est bloqué !", "blocklistOriginIgnoreWarning": "Ignorer cet avertissement, emmenez-moi quand même sur {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom pense que ce site Web est malveillant et dangereux.", "blocklistOriginThisDomain": "ce domaine", "blocklistProceedAnyway": "Ignorer l'avertissement, continuer quand même", "maliciousTransactionWarning": "Phantom pense que cette transaction est malveillante et non sécurisée. Nous avons désactivé la possibilité de la signer afin de vous protéger, vous et vos fonds.", "maliciousTransactionWarningIgnoreWarning": "Ignorer l'avertissement, continuer quand même", "maliciousTransactionWarningTitle": "Transaction signalée !", "maliciousRequestBlockedTitle": "<PERSON><PERSON><PERSON> blo<PERSON>", "maliciousRequestWarning": "Ce site web a été marqué comme malveillant. Il pourrait tenter de vous voler vos fonds et de vous pousser à confirmer une demande trompeuse.", "maliciousSignatureRequestBlocked": "<PERSON>r souc<PERSON> de <PERSON>, <PERSON> a bloqué cette requête.", "maliciousRequestBlocked": "<PERSON>r souc<PERSON> de <PERSON>, <PERSON> a bloqué cette requête.", "maliciousRequestFrictionDescription": "Continuer n'est pas sûr, c'est pourquoi Phantom a bloqué cette demande. Vous devriez fermer cette boîte de dialogue.", "maliciousRequestAcknowledge": "Je comprends que je peux perdre tous mes fonds si j'utilise ce site Web.", "maliciousRequestAreYouSure": "Confirmer ?", "siwErrorPopupTitle": "Demande de signature non valide", "siwParseErrorDescription": "La demande de signature de l'application ne peut être affichée en raison d'un formatage non valide.", "siwVerificationErrorDescription": "La demande de signature de message comptait 1 ou plusieurs erreur(s). Pour votre sécurité, veuillez vous assurer que vous utilisez la bonne application et réessayez.", "siwErrorPagination": "{{n}} sur {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Avertissement : l'adresse de l'application ne correspond pas à l'adresse fournie pour la signature.", "siwErrorMessage_DOMAIN_MISMATCH": "Attention : le domaine de l'application ne correspond pas au domaine fourni pour la vérification.", "siwErrorMessage_URI_MISMATCH": "Avertissement : Le nom d'hôte URI ne correspond pas au domaine.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Avertissement : l'ID de la chaîne ne correspond pas à l'ID de la chaîne fournie pour la vérification.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Attention : la date d'émission du message est trop éloignée dans le passé.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Attention : la date d'émission du message est trop éloignée dans le futur.", "siwErrorMessage_EXPIRED": "Avertissement : le message a expiré.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Avertissement : le message expire avant d'être délivré.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Avertissement : le message expirera avant d'être valide.", "siwErrorShowErrorDetails": "Afficher les détails de l'erreur", "siwErrorHideErrorDetails": "Masquer les détails de l'erreur", "siwErrorIgnoreWarning": "Ignorer l'avertissement, continuer quand même", "siwsTitle": "Demande de connexion", "siwsPermissions": "Permissions", "siwsAgreement": "Message", "siwsAdvancedDetails": "<PERSON><PERSON><PERSON> a<PERSON>", "siwsAlternateStatement": "{{domain}} veut que vous vous connectiez avec votre compte Solana :\n{{address}}", "siwsFieldLable_domain": "Domaine", "siwsFieldLable_address": "<PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Version", "siwsFieldLable_chainId": "ID de chaîne", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "É<PERSON>", "siwsFieldLable_expirationTime": "Expire à", "siwsFieldLable_requestId": "ID de la demande", "siwsFieldLable_resources": "Ressources", "siwsVerificationErrorDescription": "Cette demande de connexion n'est pas valide. Cela signifie que le site n'est pas sécurisé ou que son développeur a fait une erreur lors de l'envoi de la demande.", "siwsErrorNumIssues": "{{n}} problèmes", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Cet ID de chaîne ne correspond pas au réseau que vous utilisez.", "siwsErrorMessage_DOMAIN_MISMATCH": "Ce domaine n'est pas celui auquel vous êtes connecté.", "siwsErrorMessage_URI_MISMATCH": "Cet URI n'est pas celui auquel vous êtes connecté.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "La date d'émission du message est trop éloignée dans le passé.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "La date d'émission du message est trop éloignée dans le futur.", "siwsErrorMessage_EXPIRED": "Le message a expiré.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Le message expire avant son émission.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Le message expirera avant d'être valide.", "changeLockTimerPrimaryText": "Minuteur de verrouillage automatique", "changeLockTimerSecondaryText": "Combien de temps faut-il attendre pour verrouiller votre portefeuille après qu'il ait été inactif ?", "changeLockTimerToast": "Minuteur de verrouillage automatique mis à jour", "changePasswordConfirmNewPassword": "Confirmer le nouveau mot de passe", "changePasswordCurrentPassword": "Mot de passe actuel", "changePasswordErrorIncorrectCurrentPassword": "Mot de passe actuel incorrect", "changePasswordErrorGeneric": "Il y a eu un problème, veuil<PERSON>z réessayer plus tard", "changePasswordNewPassword": "Nouveau mot de passe", "changePasswordPrimaryText": "Modifier le mot de passe", "changePasswordToast": "Mot de passe mis à jour", "collectionsSpamCollections": "Collections de spam", "collectionsHiddenCollections": "Collections masquées", "collectiblesReportAsSpam": "Signaler comme spam", "collectiblesReportAsSpamAndHide": "Signaler comme spam et masquer", "collectiblesReportAsNotSpam": "Signaler comme non spam", "collectiblesReportAsNotSpamAndUnhide": "Afficher et signaler comme n'étant pas du spam", "collectiblesReportNotSpam": "Pas un spam", "collectionsManageCollectibles": "<PERSON><PERSON><PERSON> la liste des objets à collection", "collectibleDetailDescription": "Description", "collectibleDetailProperties": "Propriétés", "collectibleDetailOrdinalInfo": "Informations ordinales", "collectibleDetailRareSatsInfo": "Infos Rare Sats", "collectibleDetailSatsInUtxo": "Sats dans UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "<PERSON>um<PERSON><PERSON>", "collectibleDetailSatName": "Nom Sat", "collectibleDetailInscriptionId": "ID d'inscription", "collectibleDetailInscriptionNumber": "N<PERSON><PERSON><PERSON>'inscription", "collectibleDetailStandard": "Norme", "collectibleDetailCreated": "Création", "collectibleDetailViewOnExplorer": "Voir dans {{explorer}}", "collectibleDetailList": "Liste", "collectibleDetailSellNow": "Vendez pour {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Libérer les bitcoins disponibles", "collectibleDetailUtxoSplitterCtaSubtitle": "Vous avez {{value}} en BTC à débloquer", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Pour protéger vos fonds, nous empêchons les BTC dans des UTXO avec des Rare Sats d'être envoyés. Utilisez le partageur d'UTXO de Magic Eden pour libérer {{value}} en BTC de vos Rare Sats.", "collectibleDetailUtxoSplitterModalCtaButton": "Utiliser le partageur d'UTXO", "collectibleDetailEasilyAccept": "Accepter la meilleure offre", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "Cet objet de collection a été masqué, car <PERSON> pense qu'il s'agit d'un spam.", "collectibleDetailSpamOverlayReveal": "Afficher un objet de collection", "collectibleBurnTermsOfService": "Je comprends que cette action ne peut pas être annulée", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON><PERSON><PERSON> le jeton", "collectibleBurnTitleWithCount_other": "Brûler les jetons", "collectibleBurnDescriptionWithCount_one": "Cette action détruira définitivement et supprimera ce jeton de votre portefeuille.", "collectibleBurnDescriptionWithCount_other": "Cette action détruira définitivement et supprimera ces jetons de votre portefeuille.", "collectibleBurnTokenWithCount_one": "<PERSON><PERSON>", "collectibleBurnTokenWithCount_other": "Jet<PERSON>", "collectibleBurnCta": "<PERSON><PERSON><PERSON><PERSON>", "collectibleBurnRebate": "Remboursement", "collectibleBurnRebateTooltip": "Un petit montant de SOL sera automatiquement déposé dans votre portefeuille pour avoir brûlé ce jeton.", "collectibleBurnNetworkFee": "Frais du réseau", "collectibleBurnNetworkFeeTooltip": "Montant requis par le réseau Solana pour traiter la transaction", "unwrapButtonSwapTo": "<PERSON>changer en {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Retirer depuis {{withdrawalSource}} pour {{chainSymbol}}", "unwrapModalEstimatedTime": "Temps estimé", "unwrapModalNetwork": "<PERSON><PERSON><PERSON>", "unwrapModalNetworkFee": "Frais du réseau", "unwrapModalTitle": "Résumé", "unsupportedChain": "Chaîne non prise en charge", "unsupportedChainDescription": "On dirait que nous ne prenons pas en charge {{action}} pour le réseau {{chainName}}.", "networkFeesTooltipLabel": "Fr<PERSON> de réseau {{chainName}}", "networkFeesTooltipDescription": "Les frais {{chainName}} sont basés sur plusieurs facteurs. V<PERSON> pouvez les personnaliser pour accélérer (frais augmentés) ou ralentir (frais réduits) votre transaction.", "burnStatusErrorTitleWithCount_one": "Le jeton n'a pas pu être brûlé", "burnStatusErrorTitleWithCount_other": "Les jetons n'ont pas pu être brûlés", "burnStatusSuccessTitleWithCount_one": "Jeton brûlé !", "burnStatusSuccessTitleWithCount_other": "Jetons brûlés !", "burnStatusLoadingTitleWithCount_one": "Brûlage de jeton…", "burnStatusLoadingTitleWithCount_other": "Brûlage de jetons…", "burnStatusErrorMessageWithCount_one": "Ce jeton n'a pas pu être brûlé. Veuillez réessayer plus tard.", "burnStatusErrorMessageWithCount_other": "Ces jetons n'ont pas pu être brûlés. Veuillez réessayer plus tard.", "burnStatusSuccessMessageWithCount_one": "Ce jeton a été définitivement détruit et {{rebateAmount}} SOL a été déposé dans votre portefeuille.", "burnStatusSuccessMessageWithCount_other": "Ces jetons ont été définitivement détruits et {{rebateAmount}} SOL a été déposé dans votre portefeuille.", "burnStatusLoadingMessageWithCount_one": "Ce jeton est en cours de destruction définitive et {{rebateAmount}} SOL sera déposé dans votre portefeuille.", "burnStatusLoadingMessageWithCount_other": "Ces jetons sont en cours de destruction définitive et {{rebateAmount}} SOL sera déposé dans votre portefeuille.", "burnStatusViewTransactionText": "Voir la transaction", "collectibleDisplayLoading": "Chargement…", "collectiblesNoCollectibles": "Aucun objet à collectionner", "collectiblesPrimaryText": "Vos objets à collectionner", "collectiblesReceiveCollectible": "Recevoir un objet à collectionner", "collectiblesUnknownCollection": "Collection inconnue", "collectiblesUnknownCollectible": "Objet à collectionner inconnu", "collectiblesUniqueHolders": "Proprié<PERSON><PERSON> uniques", "collectiblesSupply": "Approvisionnement", "collectiblesUnknownTokens": "Jetons inconnus", "collectiblesNrOfListed": "{{ nrOfListed }} listé(s)", "collectiblesListed": "Listé", "collectiblesMintCollectible": "Objet à collectionner mint", "collectiblesYouMint": "Votre mint", "collectiblesMintCost": "Coût du mint", "collectiblesMintFail": "Mint échoué", "collectiblesMintFailMessage": "Une erreur est survenue lors du minting de votre objet à collectionner. Veuillez réessayer.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "Minting...", "collectiblesMintingMessage": "Votre objet à collectionner est en cours de mint", "collectiblesMintShareSubject": "Regardez ça", "collectiblesMintShareMessage": "J'ai mint ça sur @phantom !", "collectiblesMintSuccess": "Mint réussi", "collectiblesMintSuccessMessage": "Votre objet à collectionner est maintenant mint", "collectiblesMintSuccessQuestMessage": "Vous avez rempli les exigences d'une quête Phantom. Appuyez sur Récupérez votre récompense pour obtenir votre objet à collectionner gratuit.", "collectiblesMintRequired": "Requis", "collectiblesMintMaxLengthErrorMessage": "Longueur maximale dépassée", "collectiblesMintSafelyDismiss": "Vous pouvez sans crainte fermer cette fenêtre.", "collectiblesTrimmed": "Nous avons atteint la limite du nombre d'objets à collectionner pouvant être affichés pour le moment.", "collectiblesNonTransferable": "Non transférable", "collectiblesNonTransferableYes": "O<PERSON>", "collectiblesSellOfferDetails": "Détails de l'offre", "collectiblesSellYouSell": "Vous vendez à", "collectiblesSellGotIt": "J'ai compris", "collectiblesSellYouReceive": "<PERSON><PERSON> rece<PERSON>", "collectiblesSellOffer": "Proposer", "collectiblesSoldCollectible": "Objet à collectionné vendu", "collectiblesSellMarketplace": "<PERSON><PERSON>", "collectiblesSellCollectionFloor": "Plancher de collection", "collectiblesSellDifferenceFromFloor": "<PERSON><PERSON><PERSON><PERSON><PERSON> de plancher", "collectiblesSellLastSalePrice": "Dernière promotion", "collectiblesSellEstimatedFees": "Frais estimés", "collectiblesSellEstimatedProfitAndLoss": "Profits/pertes estimés", "collectiblesSellViewOnMarketplace": "Voir dans {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "Le prix « Acheter maintenant » le plus bas de la collection sur plusieurs marchés.", "collectiblesSellProfitLossTooltip": "L'estimation du profit/perte est calculée en fonction du dernier prix de vente et du montant de l'offre moins les frais.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Redevances ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Fr<PERSON> du marché ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Frais du marché", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "<PERSON><PERSON><PERSON> {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Le devis comprend une commission Phantom de {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Le devis comprend les redevances, les frais de réseau, les frais de marché et une commission Phantom de {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "Le devis comprend les redevances, les frais de réseau et les frais de place de marché", "collectiblesSellTransactionFeeTooltipTitle": "Frais de transaction", "collectiblesSellStatusLoadingTitle": "Acceptation de l'offre…", "collectiblesSellStatusLoadingIsSellingFor": "vend pour", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} vendu !", "collectiblesSellStatusSuccessWasSold": "a bien vendu pour", "collectiblesSellStatusErrorTitle": "Un problème est survenu", "collectiblesSellStatusErrorSubtitle": "Un problème est survenu lors de la tentative de vente", "collectiblesSellStatusViewTransaction": "Voir la transaction", "collectiblesSellInsufficientFundsTitle": "Fonds insuffisants", "collectiblesSellInsufficientFundsSubtitle": "Nous n'avons pas pu accepter une offre sur cet objet à collectionner parce que les fonds n'étaient pas suffisants pour payer les frais de réseau.", "collectiblesSellRecentlyTransferedNFTTitle": "<PERSON><PERSON> récent", "collectiblesSellRecentlyTransferedNFTSubtitle": "Vous devez attendre 1 heure pour accepter les enchères après un transfert.", "collectiblesApproveCollection": "{{collectionName}} approuvé", "collectiblesSellNotAvailableAnymoreTitle": "Offre indisponible", "collectiblesSellNotAvailableAnymoreSubtitle": "L'offre n'est plus disponible. Annulez cette enchère et réessayez", "collectiblesSellFlaggedTokenTitle": "L'objet de collection est signalé", "collectiblesSellFlaggedTokenSubtitle": "L'objet de collection n'est pas négociable. Plusieurs raisons à cela sont possibles, comme avoir été déclaré volé ou avoir été placé en enjeu sans être verrouillé", "collectiblesListOnMagicEden": "Liste sur Magic Eden", "collectiblesListPrice": "Prix de liste", "collectiblesUseFloor": "Util<PERSON> le seuil", "collectiblesFloorPrice": "Prix plancher", "collectiblesLastSalePrice": "Dernier prix de vente", "collectiblesTotalReturn": "Rendement total", "collectiblesOriginalPurchasePrice": "Prix d'achat d'origine", "collectiblesMagicEdenFee": "Frais de Magic Eden", "collectiblesArtistRoyalties": "Redevances d'artiste", "collectiblesListNowButton": "Lister maintenant", "collectiblesListAnywayButton": "Lister quand même", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "Afficher le listing", "collectiblesListingViewTransaction": "Voir la transaction", "collectiblesRemoveListing": "Supprimer le listing", "collectiblesEditListing": "Modifier le listing", "collectiblesEditListPrice": "Modifier le prix du listage", "collectiblesListPriceTooltip": "Le Prix de liste est le prix de vente d'un article. Les vendeurs règlent généralement le Prix de liste pour qu'il soit supérieur ou égal au prix plancher.", "collectiblesFloorPriceTooltip": "Le Prix plancher est le Prix de liste actif le plus bas pour un article de cette collection.", "collectiblesOriginalPurchasePriceTooltip": "Vous avez initialement acheté cet article pour ce montant.", "collectiblesPurchasedForSol": "Acheté pour {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "Impossible de charger les objets à collectionner listés", "collectiblesUnableToLoadListingsFrom": "Impossible de charger les listings de {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "Vos listings et vos actifs sont en sécurité, mais nous n'avons pas pu les charger à partir du {{marketplace}} pour le moment. Veuillez réessayer plus tard.", "collectiblesBelowFloorPrice": "En dessous du prix plancher", "collectiblesBelowFloorPriceMessage": "Voulez-vous vraiment lister votre NFT en dessous du prix plancher ?", "collectiblesMinimumListingPrice": "Le prix minimum est de 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden prélève une commission sur les transactions effectuées.", "collectiblesArtistRoyaltiesTooltip": "Le créateur de cette collection reçoit un % de redevance sur chaque vente réalisée.", "collectibleScreenCollectionLabel": "Collection", "collectibleScreenPhotosPermissionTitle": "Permission des photos", "collectibleScreenPhotosPermissionMessage": "Nous avons besoin de votre autorisation pour accéder à vos photos. Veuillez aller dans Paramètres et mettre à jour vos permissions.", "collectibleScreenPhotosPermissionOpenSettings": "<PERSON><PERSON><PERSON><PERSON><PERSON> les paramètres", "listStatusErrorTitle": "Échec du listing", "editListStatusErrorTitle": "Mise à jour impossible", "removeListStatusErrorTitle": "Échec de la suppression du listing", "listStatusSuccessTitle": "Listing créé !", "editListingStatusSuccessTitle": "Listing mis à jour !", "removeListStatusSuccessTitle": "Listing supprimé de Magic Eden", "listStatusLoadingTitle": "Création d'un listing...", "editListingStatusLoadingTitle": "Mise à jour du listing...", "removeListStatusLoadingTitle": "Suppression du listing...", "listStatusErrorMessage": "{{name}} n'a pas pu être listé(e) sur Magic Eden", "removeListStatusErrorMessage": "{{name}} n'a pas pu être délisté(e) sur Magic Eden", "listStatusSuccessMessage": "{{name}} est maintenant listé sur Magic Eden pour {{listCollectiblePrice}}. SOL", "editListingStatusSuccessMessage": "{{name}} est maintenant mis à jour sur Magic Eden pour {{editListCollectiblePrice}}. SOL", "removeListStatusSuccessMessage": "{{name}} a bien été supprimé de Magic Eden", "listStatusLoadingMessage": "Listing de {{name}} sur Magic Eden pour {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Mise à jour de {{name}} sur Magic Eden pour {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Suppression de {{name}} de Magic Eden. Cela peut prendre un certain temps.", "listStatusLoadingSafelyDismiss": "Vous pouvez sans crainte fermer cette fenêtre.", "listStatusViewOnMagicEden": "Afficher sur Magic Eden", "listStatusViewOnMarketplace": "Voir dans {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON><PERSON>", "listStatusViewTransaction": "Voir la transaction", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Connectez votre portefeuille matériel et assurez-vous qu'il est déverrouillé. Une fois que nous l'aurons détecté vous pourrez choisir l'adresse que vous souhaitez utiliser.", "connectHardwareFailedPrimaryText": "Échec de la connexion", "connectHardwareFailedSecondaryText": "Veuillez connecter votre portefeuille matériel et assurez-vous qu'il est déverrouillé. Une fois que nous l'aurons découvert, vous pourrez choisir quelle adresse utiliser.", "connectHardwareFinishPrimaryText": "Compte a<PERSON> !", "connectHardwareFinishSecondaryText": "Vous pouvez maintenant accéder à votre portefeuille Ledger Nano depuis Phantom. Veuillez retourner à l'extension.", "connectHardwareNeedsPermissionPrimaryText": "Connecter un nouveau portefeuille", "connectHardwareNeedsPermissionSecondaryText": "Cliquez sur le bouton ci-dessous pour terminer le processus de connexion.", "connectHardwareSearchingPrimaryText": "Recherche d'un portefeuille…", "connectHardwareSearchingSecondaryText": "Connectez votre portefeuille matériel et assurez-vous qu'il est déverrouillé et que vous avez donné les autorisations dans votre navigateur.", "connectHardwarePermissionDeniedPrimary": "Permission refusée", "connectHardwarePermissionDeniedSecondary": "Accordez à Phantom la permission de se connecter à votre appareil Ledger", "connectHardwarePermissionUnableToConnect": "Connexion impossible", "connectHardwarePermissionUnableToConnectDescription": "Nous n'avons pas pu nous connecter à votre appareil Led<PERSON>. Il se peut que nous ayons besoin de plus de permissions.", "connectHardwareSelectAddressAllAddressesImported": "Toutes les adresses importées", "connectHardwareSelectAddressDerivationPath": "Chemin de dérivation", "connectHardwareSelectAddressSearching": "Recherche…", "connectHardwareSelectAddressSelectWalletAddress": "Sélectionner l'adresse du portefeuille", "connectHardwareSelectAddressWalletAddress": "Adresse du portefeuille", "connectHardwareWaitingForApplicationSecondaryText": "Veuillez connecter votre portefeuille matériel et assurez-vous qu'il est déverrouillé.", "connectHardwareWaitingForPermissionPrimaryText": "Permission requise", "connectHardwareWaitingForPermissionSecondaryText": "Connectez votre portefeuille matériel et assurez-vous qu'il est déverrouillé et que vous avez donné les autorisations dans votre navigateur.", "connectHardwareAddAccountButton": "Ajouter un compte", "connectHardwareLedger": "Connectez votre Led<PERSON>", "connectHardwareStartConnection": "Cliquez sur le bouton ci-dessous pour commencer la connexion de votre portefeuille matériel Ledger", "connectHardwarePairSuccessPrimary": "{{productName}} connecté", "connectHardwarePairSuccessSecondary": "<PERSON><PERSON> avez r<PERSON>i à connecter votre {{productName}}.", "connectHardwareSelectChains": "Sélectionnez les chaînes à connecter", "connectHardwareSearching": "Recherche…", "connectHardwareMakeSureConnected": "Connectez et déverrouillez votre portefeuille matériel. Veuillez approuver les permissions de navigateur pertinentes.", "connectHardwareOpenAppDescription": "Veuillez déverrouiller votre portefeuille matériel", "connectHardwareConnecting": "Connexion en cours…", "connectHardwareConnectingDescription": "Nous sommes connectés à votre appareil Ledger.", "connectHardwareConnectingAccounts": "Connexion de vos comptes…", "connectHardwareDiscoveringAccounts": "Recherche de comptes…", "connectHardwareDiscoveringAccountsDescription": "Nous cherchons de l'activité sur vos comptes.", "connectHardwareErrorLedgerLocked": "Le Ledger est connecté", "connectHardwareErrorLedgerLockedDescription": "Assurez-vous que votre appareil Ledger est déverrouillé, puis réessayez.", "connectHardwareErrorLedgerGeneric": "Un problème est survenu", "connectHardwareErrorLedgerGenericDescription": "Impossible de trouver des comptes. Assurez-vous que votre appareil <PERSON> est déverrouillé, puis réessayez.", "connectHardwareErrorLedgerPhantomLocked": "Veuillez rouvrir Phantom et réessayer de connecter votre matériel.", "connectHardwareFindingAccountsWithActivity": "Recherche de comptes {{chainName}}…", "connectHardwareFindingAccountsWithActivityDualChain": "Recherche de comptes {{chainName1}} ou {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Nous avons trouvé {{numOfAccounts}} comptes avec activité sur votre Ledge.", "connectHardwareFoundAccountsWithActivitySingular": "Nous avons trouvé 1 compte avec activité sur votre Ledge.", "connectHardwareFoundSomeAccounts": "Nous avons trouvé des comptes sur votre appareil <PERSON>.", "connectHardwareViewAccounts": "Affiche<PERSON> les comptes", "connectHardwareConnectAccounts": "Comptes connectés", "connectHardwareSelectAccounts": "Sélectionner des comptes", "connectHardwareChooseAccountsToConnect": "Choisir les comptes de portefeuilles à connecter.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} comptes ajou<PERSON>s", "connectHardwareAccountsStepOfSteps": "Étape {{stepNum}} sur {{totalSteps}}", "connectHardwareMobile": "Connecter un Ledger", "connectHardwareMobileTitle": "Connectez votre portefeuille matériel Ledger", "connectHardwareMobileEnableBluetooth": "<PERSON><PERSON> <PERSON>", "connectHardwareMobileEnableBluetoothDescription": "Autorisez l'utilisation de Bluetooth pour la connexion", "connectHardwareMobileEnableBluetoothSettings": "Allez dans Paramètres pour autoriser Phantom à utiliser les permissions Localisation et Appareils proches.", "connectHardwareMobilePairWithDevice": "Appariez à votre appareil Ledger", "connectHardwareMobilePairWithDeviceDescription": "Gardez votre appareil à proximité pour profiter du meilleur signal", "connectHardwareMobileConnectAccounts": "Connecter des comptes", "connectHardwareMobileConnectAccountsDescription": "Nous allons chercher une activité dans un compte que vous pourriez avoir utilisé", "connectHardwareMobileConnectLedgerDevice": "Connectez votre appareil <PERSON>", "connectHardwareMobileLookingForDevices": "À la recherche d'appareils proches...", "connectHardwareMobileLookingForDevicesDescription": "Veuillez connecter votre appareil <PERSON> et vous assurez qu'il est déverrouillé.", "connectHardwareMobileFoundDeviceSingular": "Nous avons trouvé un appareil Ledger", "connectHardwareMobileFoundDevices": "Nous avons trouvé {{numDevicesFound}} appareils <PERSON><PERSON>", "connectHardwareMobileFoundDevicesDescription": "Sélectionnez un appareil Ledger ci-dessous pour commencer à apparier.", "connectHardwareMobilePairingWith": "Appariement avec {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Su<PERSON>z les instructions sur votre appareil Ledger pendant l'appariement.", "connectHardwareMobilePairingFailed": "Apparie<PERSON> r<PERSON>i", "connectHardwareMobilePairingFailedDescription": "Impossible d'apparier avec {{deviceName}}. Veuillez vous assurer que l'appareil est déverrouillé.", "connectHardwareMobilePairingSuccessful": "Apparie<PERSON> r<PERSON>i", "connectHardwareMobilePairingSuccessfulDescription": "Vous avez réussi à apparier et connecter votre appareil <PERSON>.", "connectHardwareMobileOpenAppSingleChain": "Ouvrez l'application {{chainName}} sur votre Ledger", "connectHardwareMobileOpenAppDualChain": "Ouvrez l'application {{chainName1}} ou {{chainName2}} sur votre Ledger", "connectHardwareMobileOpenAppDescription": "Assurez-vous que votre appareil est débloqué.", "connectHardwareMobileStillCantFindDevice": "Vous ne pouvez toujours pas trouver votre appareil ?", "connectHardwareMobileLostConnection": "Connexion perdue", "connectHardwareMobileLostConnectionDescription": "Nous avons perdu la connexion avec {{deviceName}}. Veuillez vous assurer que votre appareil est débloqué, puis réessayez.", "connectHardwareMobileGenericLedgerDevice": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobileConnectDeviceSigning": "Connectez votre {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Déverrouillez votre appareil Ledger et gardez-le à proximité.", "connectHardwareMobileBluetoothDisabled": "Le bluetooth est désactivé", "connectHardwareMobileBluetoothDisabledDescription": "Veuillez activer votre bluetooth et vous assurer que votre appareil Ledger est débloqué.", "connectHardwareMobileLearnMore": "Plus d'informations", "connectHardwareMobileBlindSigningDisabled": "La Signature aveugle est désactivée", "connectHardwareMobileBlindSigningDisabledDescription": "Assurez-vous que la signature en aveugle est activée sur votre appareil.", "connectHardwareMobileConfirmSingleChain": "V<PERSON> devez confirmer la transaction sur votre portefeuille matériel. Assurez-vous qu'il est déverrouillé.", "metamaskExplainerBottomSheetHeader": "Ce site fonctionne avec Phantom", "metamaskExplainerBottomSheetSubheader": "Sélectionnez MetaMask dans la boîte de dialogue de portefeuille connecté pour continuer.", "metamaskExplainerBottomSheetDontShowAgain": "Ne plus afficher", "ledgerStatusNotConnected": "Le Ledger n'est pas connecté", "ledgerStatusConnectedInterpolated": "{{productName}} est connecté", "connectionClusterInterpolated": "Vous êtes actuellement sur {{cluster}}", "connectionClusterTestnetMode": "Vous êtes actuellement en mode Testnet", "featureNotSupportedOnLocalNet": "Cette fonctionnalité n'est pas prise en charge lorsque Solana Localnet est activé.", "readOnlyAccountBannerWarning": "Vous surveillez ce compte", "depositAddress": "<PERSON><PERSON><PERSON>", "depositAddressChainInterpolated": "Votre adresse {{chain}}", "depositAssetDepositInterpolated": "Recevoir {{tokenSymbol}}", "depositAssetSecondaryText": "Cette adresse ne peut être utilisée que pour recevoir des jetons compatibles.", "depositAssetTextInterpolated": "Utilisez cette adresse pour recevoir des jetons et des objets à collectionner sur <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Transfert de la bourse", "depositAssetShareAddress": "Partager l'adresse", "depositAssetBuyOrDeposit": "<PERSON><PERSON><PERSON> ou Transf<PERSON>rer", "depositAssetBuyOrDepositDesc": "Alimentez votre portefeuille pour commencer", "depositAssetTransfer": "<PERSON><PERSON><PERSON><PERSON>", "editAddressAddressAlreadyAdded": "L'adresse a déjà été ajoutée", "editAddressAddressAlreadyExists": "L'adresse existe déjà", "editAddressAddressIsRequired": "L'adresse est obligatoire", "editAddressPrimaryText": "Modifier l'adresse", "editAddressRemove": "Supprimer du carnet d'adresse", "editAddressToast": "<PERSON>resse mise à jour", "removeSavedAddressToast": "<PERSON>resse supprimée", "exportSecretErrorGeneric": "Il y a eu un problème, veuil<PERSON>z réessayer plus tard", "exportSecretErrorIncorrectPassword": "Mot de passe incorrect", "exportSecretPassword": "Mot de passe", "exportSecretPrivateKey": "clé privée", "exportSecretSecretPhrase": "phrase secrète", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "phrase de récupération secrète", "exportSecretSelectYourAccount": "Sélectionnez votre compte", "exportSecretShowPrivateKey": "Afficher la clé privée", "exportSecretShowSecretRecoveryPhrase": "Afficher la phrase de récupération secrète", "exportSecretShowSecret": "Afficher {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "Ne partagez <1>pas</1> votre {{secretNameText}} !", "exportSecretWarningSecondaryInterpolated": "Si quelqu'un possède votre {{secretNameText}}, il aura un contrôle total sur votre portefeuille.", "exportSecretOnlyWay": "Votre {{secretNameText}} est le seul moyen de récupérer votre portefeuille", "exportSecretDoNotShow": "Ne laissez personne voir votre {{secretNameText}}", "exportSecretWillNotShare": "Je ne partagerai pas ma {{secretNameText}} avec qui que ce soit, y compris Phantom.", "exportSecretNeverShare": "Ne partagez jamais votre {{secretNameText}} avec quiconque", "exportSecretYourPrivateKey": "Votre clé privée", "exportSecretYourSecretRecoveryPhrase": "Votre phrase de récupération secrète", "exportSecretResetPin": "Réinitialisez votre PIN", "fullPageHeaderBeta": "<PERSON><PERSON><PERSON> !", "fullPageHeaderHelp": "Aide", "gasUpTo": "Jusqu'à {{ amount }}", "timeDescription1hour": "Environ 1 heure", "timeDescription30minutes": "Environ 30 minutes", "timeDescription10minutes": "Environ 10 minutes", "timeDescription2minutes": "Environ 2 minutes", "timeDescription30seconds": "Environ 30 secondes", "timeDescription15seconds": "Environ 15 secondes", "timeDescription10seconds": "Environ 10 secondes", "timeDescription5seconds": "Environ 5 secondes", "timeDescriptionAbbrev1hour": "1 h", "timeDescriptionAbbrev30minutes": "30 min", "timeDescriptionAbbrev10minutes": "10 min", "timeDescriptionAbbrev2minutes": "2 min", "timeDescriptionAbbrev30seconds": "30 s", "timeDescriptionAbbrev15seconds": "15 s", "timeDescriptionAbbrev10seconds": "10 s", "timeDescriptionAbbrev5seconds": "5 s", "gasSlow": "<PERSON><PERSON>", "gasAverage": "<PERSON><PERSON><PERSON>", "gasFast": "Rapide", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "<PERSON><PERSON><PERSON><PERSON>", "homeErrorDescription": "Une erreur est survenue lors de la tentative de récupération de vos actifs. Veuillez actualiser et réessayer", "homeErrorTitle": "Échec de la récupération des actifs", "homeManageTokenList": "G<PERSON>rer la liste des jetons", "interstitialDismissUnderstood": "<PERSON><PERSON><PERSON>", "interstitialBaseWelcomeTitle": "Phantom prend maintenant en charge Base !", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON><PERSON>, recevez et achetez des jetons", "interstitialBaseWelcomeItemTitle_2": "Explorez l'écosystème Base", "interstitialBaseWelcomeItemTitle_3": "Sûr et sécurisé", "interstitialBaseWelcomeItemDescription_1": "Transférez et achetez des USDC et des ETH sur Base en utilisant {{paymentMethod}}, des cartes ou Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Utilisez Phantom avec toutes vos applications DeFi et NFT préférées.", "interstitialBaseWelcomeItemDescription_3": "Restez en sécurité grâce à la prise en charge de Ledger, au filtrage des spams et à la simulation des transactions.", "privacyPolicyChangedInterpolated": "Notre Politique de confidentialité a été modifiée. <1>Plus d'informations</1>", "bitcoinAddressTypesBodyTitle": "Types d'adresses Bitcoin", "bitcoinAddressTypesFeature1Title": "À propos des adresses Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom prend en charge Native Seg<PERSON><PERSON> et Ta<PERSON>root, chacun avec son propre solde. <PERSON><PERSON> pouvez envoyer des BTC ou des Ordinals avec l'un ou l'autre type d'adresse.", "bitcoinAddressTypesFeature2Title": "<PERSON><PERSON><PERSON><PERSON> natif", "bitcoinAddressTypesFeature2Subtitle": "L'adresse BTC par défaut dans Phantom. Plus ancienne que Taproot, mais compatible avec tous les portefeuilles et toutes les bourses d'échange.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Meilleur pour les Ordinals et les BRC-20, avec les frais les moins élevés. Ajustez les adresses dans Préférences -> Adresse Bitcoin préférée.", "headerTitleInfo": "Informations", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Voici votre adresse <1>{{addressType}}</1>.", "invalidChecksumTitle": "Nous avons amélioré votre phrase secrète !", "invalidChecksumFeature1ExportPhrase": "Exportez votre nouvelle phrase secrète", "invalidChecksumFeature1ExportPhraseDescription": "Veuillez sauvegarder votre nouvelle phrase secrète avec les clés privées de vos vieux comptes.", "invalidChecksumFeature2FundsAreSafe": "Vos fonds sont en sécurité", "invalidChecksumFeature2FundsAreSafeDescription": "Cette mise à niveau a été automatisée. Personne chez <PERSON> ne connaît votre phrase secrète ou n'a accès à vos fonds.", "invalidChecksumFeature3LearnMore": "En savoir plus", "invalidChecksumFeature3LearnMoreDescription": "Votre phrase était incompatible avec la plupart des portefeuilles. Lisez <1>cet article d'aide</1> pour en savoir plus.", "invalidChecksumBackUpSecretPhrase": "Sauvegardez la phrase secrète", "migrationFailureTitle": "Un problème est survenu lors de la migration de votre compte", "migrationFailureFeature1": "Exportez votre phrase secrète", "migrationFailureFeature1Description": "Veuillez sauvegarder votre phrase secrète avant l'intégration.", "migrationFailureFeature2": "Intégration à Phantom", "migrationFailureFeature2Description": "Vous allez devoir effectuer une nouvelle intégration à Phantom pour voir votre compte.", "migrationFailureFeature3": "En savoir plus", "migrationFailureFeature3Description": "Lisez <1>cet article d'aide</1> pour obtenir plus d'informations.", "migrationFailureContinueToOnboarding": "Continuer vers l'intégration", "migrationFailureUnableToFetchMnemonic": "Nous n'avons pas pu charger votre phrase secrète", "migrationFailureUnableToFetchMnemonicDescription": "Veuillez contacter l'assistance et télécharger les journaux de l'application pour déboguer", "migrationFailureContactSupport": "Contacter l'assistance", "ledgerActionConfirm": "Confirmez sur votre Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Signature aveugle désactivée", "ledgerActionErrorBlindSignDisabledSecondaryText": "Merci de vous assurer que la signature aveugle est activée sur votre périphérique, puis réessayez l'action", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Périphérique matériel déconnecté pendant l'opération", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Merci de fermer l'extension Phantom et de réessayer l'action", "ledgerActionErrorDeviceLockedPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ledgerActionErrorDeviceLockedSecondaryText": "<PERSON><PERSON><PERSON> <PERSON>rouiller votre appareil et de réessayer l'action", "ledgerActionErrorHeader": "Erreur d'action Ledger", "ledgerActionErrorUserRejectionPrimaryText": "L'utilisateur a rejeté la transaction", "ledgerActionErrorUserRejectionSecondaryText": "L'action a été rejetée sur le périphérique matériel par l'utilisateur", "ledgerActionNeedPermission": "Permission requise", "ledgerActionNeedToConfirm": "V<PERSON> devez confirmer la transaction sur votre portefeuille matériel. Assurez-vous qu'il est déverrouillé et sur l'application {{chainType}}.", "ledgerActionNeedToConfirmMany": "Vous devrez confirmer {{numberOfTransactions}} transactions sur votre portefeuille matériel. Assurez-vous qu'il est déverrouillé et sur l'application {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Vous devez confirmer la transaction sur votre portefeuille matériel. Assurez-vous qu'il est déverrouillé, sur l'application {{chainType}} et que la signature aveugle est activée.", "ledgerActionNeedToConfirmBlindMany": "Vous devrez confirmer {{numberOfTransactions}} transactions sur votre portefeuille matériel. Assurez-vous qu'il est déverrouillé, sur l'application {{chainType}}, et que la signature aveugle est activée.", "ledgerActionPleaseConnect": "Veuillez connecter votre Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "Veuillez connecter votre portefeuille matériel et assurez-vous qu'il est déverrouillé. Assurez-vous que vous avez les autorisations approuvées dans votre navigateur.", "maxInputAmount": "<PERSON><PERSON>", "maxInputMax": "Max", "notEnoughSolPrimaryText": "Pas assez de SOL", "notEnoughSolSecondaryText": "Vous n'avez pas assez de SOL dans votre portefeuille cette transaction. Veuillez en déposer davantage et réessayer.", "insufficientBalancePrimaryText": "{{tokenSymbol}} insuffisant", "insufficientBalanceSecondaryText": "Vous n'avez pas assez de {{tokenSymbol}} dans votre portefeuille pour cette transaction.", "insufficientBalanceRemaining": "Restant", "insufficientBalanceRequired": "Requis", "notEnoughSplTokensTitle": "Jetons insuffisants", "notEnoughSplTokensDescription": "Vous n'avez pas assez de jetons dans votre portefeuille pour cette transaction. Cette transaction sera annulée si elle est envoyée.", "transactionExpiredPrimaryText": "Transaction expirée", "transactionExpiredSecondaryText": "Vous avez attendu trop longtemps pour confirmer la transaction et elle a expiré. Cette transaction sera rétablie si elle est envoyée.", "transactionHasWarning": "Avertissement transaction", "tokens": "jetons", "notificationApplicationApprovalPermissionsAddressVerification": "Vérifiez votre propre adresse", "notificationApplicationApprovalPermissionsTransactionApproval": "Demander l'approbation de transactions", "notificationApplicationApprovalPermissionsViewWalletActivity": "Voir le solde et l'activité de votre portefeuille", "notificationApplicationApprovalParagraphText": "Confirmer permettra à ce site de voir les soldes et les activités pour le compte sélectionné.", "notificationApplicationApprovalActionButtonConnect": "Connecter", "notificationApplicationApprovalActionButtonSignIn": "Connexion", "notificationApplicationApprovalAllowApproval": "Autoriser le site à se connecter ?", "notificationApplicationApprovalAutoConfirm": "Confirmer les transactions automatiquement", "notificationApplicationApprovalConnectDisclaimer": "Se connecter uniquement aux sites Web auxquels vous faites confiance", "notificationApplicationApprovalSignInDisclaimer": "Ne vous connectez qu'aux sites web auxquels vous faites confiance", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Ce site n'est pas sûr et peut tenter de voler vos fonds.", "notificationApplicationApprovalConnectUnknownApp": "Inconnu", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Connexion à l'application impossible", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Cette application essaie de se connecter à {{appNetworkName}}, mais {{phantomNetworkName}} est connecté.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Pour utiliser {{networkName}}, allez dans Paramètres pour développeurs → Mode Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "<PERSON><PERSON><PERSON> inconnu", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "La connexion à d'autres applications pour mobiles n'est actuellement pas prise en charge par Ledger.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Veu<PERSON>z basculer sur un compte non Ledger ou utiliser le navigateur de l'application, et réessayer.", "notificationSignatureRequestConfirmTransaction": "Confirmer la transaction", "notificationSignatureRequestConfirmTransactionCapitalized": "Confirmer la transaction", "notificationSignatureRequestConfirmTransactions": "Confirmer les transactions", "notificationSignatureRequestConfirmTransactionsCapitalized": "Confirmer les transactions", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON><PERSON> de <PERSON>", "notificationMessageHeader": "Message", "notificationMessageCopied": "Message copié", "notificationAutoConfirm": "Confirmation automatique", "notificationAutoConfirmOff": "Désactivée", "notificationAutoConfirmOn": "Activée", "notificationConfirmFooter": "Confirmez uniquement si vous avez confiance en ce site web.", "notificationEstimatedTime": "Temps estimé", "notificationPermissionRequestText": "Il s'agit uniquement d'une permission demande uniquement. La transaction ne peut pas être exécutée immédiatement.", "notificationBalanceChangesText": "Les modifications de solde sont estimées. Les montants et actifs impliqués ne sont pas garantis.", "notificationContractAddress": "<PERSON><PERSON><PERSON> de contrat", "notificationAdvancedDetailsText": "<PERSON><PERSON><PERSON>", "notificationUnableToSimulateWarningText": "Nous ne pouvons pas estimer les modifications du solde à l'heure actuelle. Vous pouvez réessayer plus tard ou confirmer que vous avez confiance en ce site.", "notificationSignMessageParagraphText": "Signer ce message prouvera que vous êtes propriétaire du compte sélectionné.", "notificationSignatureRequestScanFailedDescription": "Impossible de scanner le message pour des raisons de sécurité. Veuillez faire attention.", "notificationFailedToScan": "Échec de la simulation des résultats de cette demande.\nLa confirmation n'est pas sûre et peut entraîner des pertes.", "notificationScanLoading": "<PERSON><PERSON> de <PERSON> demande", "notificationTransactionApprovalActionButtonConfirm": "Confirmer", "notificationTransactionApprovalActionButtonBack": "Retour", "notificationTransactionApprovalEstimatedChanges": "Estimation des modifications", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Les estimations sont basées sur des simulations de transactions et ne constituent pas une garantie", "notificationTransactionApprovalHideAdvancedDetails": "Masquer les informations avancées sur les transactions", "notificationTransactionApprovalNetworkFee": "Frais du réseau", "notificationTransactionApprovalNetwork": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatedTime": "Temps estimé", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Aucune modification ayant un impact sur la propriété des actifs n'a été constatée", "notificationTransactionApprovalSolanaAmountRequired": "Montant requis par le réseau Solana pour traiter la transaction", "notificationTransactionApprovalUnableToSimulate": "Simulation impossible. Assurez-vous de faire confiance à ce site web, car l'approbation peut entraîner la perte de fonds.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Impossible de récupérer les changements de solde", "notificationTransactionApprovalViewAdvancedDetails": "Voir les informations avancées sur les transactions", "notificationTransactionApprovalKnownMalicious": "Cette transaction est malveillante. La signer entraînera une perte de fonds.", "notificationTransactionApprovalSuspectedMalicious": "Nous soupçonnons cette transaction d'être malveillante. L'approuver peut entraîner une perte de fonds.", "notificationTransactionApprovalNetworkFeeHighWarning": "Les frais de réseau sont élevés en raison de sa congestion.", "notificationTransactionERC20ApprovalDescription": "Confirmer permettra à cette application d'accéder à votre solde à tout moment, jusqu'à la limite indiquée ci-dessous.", "notificationTransactionERC20ApprovalContractAddress": "<PERSON><PERSON><PERSON> de contrat", "notificationTransactionERC20Unlimited": "illimité", "notificationTransactionERC20ApprovalTitle": "Approuver la dépense de {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Révoquer la dépense de {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Révoquer un accès à {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Tous vos {{tokenSymbol}}", "notificationIncorrectModeTitle": "Mode incorrect", "notificationIncorrectModeInTestnetTitle": "Vous êtes en mode Testnet", "notificationIncorrectModeNotInTestnetTitle": "Vous n'êtes pas en mode Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} essaie d'utiliser un mainnet, mais vous êtes en mode Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} essaie d'utiliser un testnet, mais vous n'êtes pas en mode Testnet", "notificationIncorrectModeInTestnetProceed": "Pour continuer, d<PERSON><PERSON>z le mode Testnet.", "notificationIncorrectModeNotInTestnetProceed": "Pour continuer, activez le mode Testnet.", "notificationIncorrectEIP712ChainId": "Nous vous avons empêché de signer un message qui n'était pas destiné au réseau auquel vous êtes actuellement connecté", "notificationIncorrectEIP712ChainIdDescription": "Message demandé {{messageChainId}}, vous êtes connecté à {{connectedChainId}}", "notificationUnsupportedNetwork": "R<PERSON>eau non pris en charge", "notificationUnsupportedNetworkDescription": "Ce site web tente d'utiliser un réseau que Phantom ne prend pas en charge à l'heure actuelle.", "notificationUnsupportedNetworkDescriptionInterpolated": "Pour procéder avec une autre extension, désactivez <1>Paramètres → Portefeuille d'application par défaut, et sélectionnez Toujours demander</1>. Actualisez ensuite la page et reconnectez-vous.", "notificationUnsupportedAccount": "Compte non pris en charge", "notificationUnsupportedAccountDescription": "Ce site web tente d'utiliser {{targetChainType}}, que ce compte {{chainType}} ne prend pas en charge.", "notificationUnsupportedAccountDescription2": "Basculez sur un compte depuis une phrase d'initialisation ou une clé privée compatible et réessayez.", "notificationInvalidTransaction": "Transaction non valide", "notificationInvalidTransactionDescription": "La transaction reçue de cette application est malformée et ne doit pas être envoyée. Veuillez contacter le développeur de cette application pour lui signaler ce problème.", "notificationCopyTransactionText": "Copier la transaction", "notificationTransactionCopied": "Transaction copiée", "onboardingImportOptionsPageTitle": "Importer un portefeuille", "onboardingImportOptionsPageSubtitle": "Importez un portefeuille existant avec votre phrase secrète, votre clé privée ou votre portefeuille matériel.", "onboardingImportPrivateKeyPageTitle": "Importer une clé privée", "onboardingImportPrivateKeyPageSubtitle": "Importer un portefeuille à chaîne unique existant", "onboardingCreatePassword": "<PERSON><PERSON><PERSON> un mot de passe", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "J'accepte les <1>Conditions d'utilisation</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Confirmer le mot de passe", "onboardingCreatePasswordDescription": "Vous l'utiliserez pour déverrouiller votre portefeuille.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Phrase de récupération secrète invalide", "onboardingCreatePasswordPasswordPlaceholder": "Mot de passe", "onboardingCreatePasswordPasswordStrengthWeak": "Faible", "onboardingCreatePasswordPasswordStrengthMedium": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthStrong": "Forte", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "J'ai enregistré ma Phrase de récupération secrète", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Phrase de récupération secrète", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Cette phrase est le SEUL moyen de récupérer votre portefeuille. Ne la partagez PAS avec qui que ce soit !", "onboardingImportWallet": "Importer un Portefeuille", "onboardingImportWalletImportExistingWallet": "Importez un portefeuille existant avec votre phrase secrète de récupération de 12 ou 24 mots.", "onboardingImportWalletRestoreWallet": "Restaurer un Portefeuille", "onboardingImportWalletSecretRecoveryPhrase": "Phrase de récupération secrète", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Phrase de récupération secrète non valide", "onboardingImportWalletIHaveWords": "J'ai une phrase de récupération de {{numWords}} mots", "onboardingImportWalletIncorrectOrMisspelledWord": "Le mot {{wordIndex}} est incorrect ou mal orthographié", "onboardingImportWalletIncorrectOrMisspelledWords": "Les mots {{wordIndexes}} sont incorrects ou mal orthographiés", "onboardingImportWalletScrollDown": "Faites défiler vers le bas", "onboardingImportWalletScrollUp": "Faites défiler vers le haut", "onboardingSelectAccountsImportAccounts": "Importer des comptes", "onboardingSelectAccountsImportAccountsDescription": "Choisir les comptes de portefeuilles à importer.", "onboardingSelectAccountsImportSelectedAccounts": "Importer les comptes sélectionnés", "onboardingSelectAccountsFindMoreAccounts": "Trouvez plus de comptes", "onboardingSelectAccountsFindMoreNoneFound": "Aucun compte trouvé", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} comptes sélectionnés", "onboardingSelectAccountSelectAllText": "<PERSON><PERSON>", "onboardingAdditionalPermissionsTitle": "Utiliser des applications avec Phantom", "onboardingAdditionalPermissionsSubtitle": "Pour que l'application soit la plus transparente possible, nous vous recommandons d'autoriser Phantom à lire et à modifier les données sur tous les sites.", "interstitialAdditionalPermissionsTitle": "Utiliser des applications avec Phantom", "interstitialAdditionalPermissionsSubtitle": "Pour continuer à utiliser les applications sans interruption, nous vous recommandons d'autoriser Phantom à lire et à modifier les données sur tous les sites.", "recentActivityPrimaryText": "Activité récente", "removeAccountActionButtonRemove": "<PERSON><PERSON><PERSON><PERSON>", "removeAccountRemoveWallet": "Supprimer le compte", "removeAccountInterpolated": "Supprimer {{accountName}}", "removeAccountWarningLedger": "Même si vous supprimez ce portefeuille de Phantom, vous pourrez le réinsérer en utilisant le flux « Connecter un portefeuille matériel ».", "removeAccountWarningSeedVault": "Même si vous supprimez ce portefeuille de Phantom, vous pourrez le réinsérer en utilisant le flux « Connecter un portefeuille coffre à codes de sauvegarde ».", "removeAccountWarningPrivateKey": "Une fois que vous avez supprimé ce portefeuille, <PERSON> ne pourra pas le récupérer pour vous. Assurez-vous d'avoir sauvegardé votre clé privée.", "removeAccountWarningSeed": "Même si vous supprimez ce portefeuille de Phantom, vous pourrez le récupérer en utilisant votre mnémonique dans ce portefeuille ou un autre.", "removeAccountWarningReadOnly": "Supprimer ce compte n'affectera pas votre portefeuille, étant donné que c'est un portefeuille en lecture seule.", "removeSeedPrimaryText": "Suppression de la Phrase secrète {{number}}", "removeSeedSecondaryText": "<PERSON>la supprimera tous les portefeuilles existants dans la Phrase secrète {{number}}. Assurez-vous d'avoir sauvegardé votre phrase secrète.", "resetSeedPrimaryText": "Réinitialiser l'application avec une nouvelle phrase secrète", "resetSeedSecondaryText": "Cela supprimera tous les comptes existants et les remplacera par de nouveaux. Assurez-vous que vous avez sauvegardé votre phrase secrète et vos clés privées existantes.", "resetAppPrimaryText": "Réinitialiser et effacer l'application", "resetAppSecondaryText": "Cette opération supprimera tous les comptes et toutes les données existants. Veillez à sauvegarder votre phrase secrète et vos clés privées.", "richTransactionsDays": "jours", "richTransactionsToday": "<PERSON><PERSON><PERSON>'hui", "richTransactionsYesterday": "<PERSON>er", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "Interaction application", "richTransactionDetailAt": "à", "richTransactionDetailBid": "<PERSON><PERSON><PERSON>", "richTransactionDetailBidDetails": "<PERSON>é<PERSON> de l'enchère", "richTransactionDetailBought": "<PERSON><PERSON><PERSON>", "richTransactionDetailBurned": "B<PERSON><PERSON><PERSON>", "richTransactionDetailCancelBid": "Annuler l'enchère", "richTransactionDetailCompleted": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailConfirmed": "Confirmée", "richTransactionDetailDate": "Date", "richTransactionDetailFailed": "Échec", "richTransactionDetailFrom": "De", "richTransactionDetailItem": "Article", "richTransactionDetailListed": "Listé", "richTransactionDetailListingDetails": "<PERSON>é<PERSON> du listage", "richTransactionDetailListingPrice": "Prix du listage", "richTransactionDetailMarketplace": "<PERSON><PERSON>", "richTransactionDetailNetworkFee": "Frais du réseau", "richTransactionDetailOriginalListingPrice": "Prix d'origine du listage", "richTransactionDetailPending": "En attente", "richTransactionDetailPrice": "Prix", "richTransactionDetailProvider": "Fournisseur", "richTransactionDetailPurchaseDetails": "<PERSON>é<PERSON> de l'achat", "richTransactionDetailRebate": "Remboursement", "richTransactionDetailReceived": "Reçue", "richTransactionDetailSaleDetails": "<PERSON><PERSON><PERSON> de la vente", "richTransactionDetailSent": "Envoyée", "richTransactionDetailSold": "Vendu", "richTransactionDetailStaked": "<PERSON><PERSON><PERSON> p<PERSON>", "richTransactionDetailStatus": "Statut", "richTransactionDetailSwap": "<PERSON><PERSON><PERSON>", "richTransactionDetailSwapDetails": "Détails de l'échange", "richTransactionDetailTo": "À", "richTransactionDetailTokenSwap": "É<PERSON><PERSON> de <PERSON>on", "richTransactionDetailUnknownNFT": "NFT inconnu", "richTransactionDetailUnlisted": "Dé-list<PERSON>", "richTransactionDetailUnstaked": "<PERSON><PERSON><PERSON> reti<PERSON>", "richTransactionDetailValidator": "Validateur", "richTransactionDetailViewOnExplorer": "Voir dans {{explorer}}", "richTransactionDetailWithdrawStake": "<PERSON><PERSON><PERSON> l'enjeu", "richTransactionDetailYouPaid": "Vous avez payé", "richTransactionDetailYouReceived": "<PERSON><PERSON> avez reçu", "richTransactionDetailUnwrapDetails": "Détails du déballage", "richTransactionDetailTokenUnwrap": "Déballage de jeton", "activityItemsRefreshFailed": "Échec du chargement de transactions plus récentes.", "activityItemsPagingFailed": "Échec du chargement de transactions plus anciennes.", "activityItemsTestnetNotAvailable": "L'historique des transactions de Testnet n'est pas disponible pour le moment", "historyUnknownDappName": "Inconnu", "historyStatusSucceeded": "Réussite", "historyNetwork": "<PERSON><PERSON><PERSON>", "historyAttemptedAmount": "<PERSON><PERSON> tenté", "historyAmount": "<PERSON><PERSON>", "sendAddressBookButtonLabel": "Carnet d'adresses", "addressBookSelectAddressBook": "Carnet d'adresses", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON>ne adresse enregistrée", "sendAddressBookRecentlyUsed": "Utilisées récemment", "addressBookSelectRecentlyUsed": "Utilisées récemment", "sendConfirmationLabel": "Étiquette", "sendConfirmationMessage": "Message", "sendConfirmationNetworkFee": "Frais du réseau", "sendConfirmationPrimaryText": "Confirmer l'envoi", "sendWarning_INSUFFICIENT_FUNDS": "Fonds insuffisants, cette transaction échouera probablement si elle est envoyée.", "sendFungibleSummaryNetwork": "<PERSON><PERSON><PERSON>", "sendFungibleSummaryNetworkFee": "Frais du réseau", "sendFungibleSummaryEstimatedTime": "Temps estimé", "sendFungiblePendingEstimatedTime": "Estimations de temps", "sendFungibleSummaryEstimatedTimeDescription": "La vitesse des transactions Ethereum varie en fonction de plusieurs facteurs. V<PERSON> pouvez les accélérer en cliquant sur « Frais de réseau ».", "sendSummaryBitcoinPendingTxTitle": "Impossible d'envoyer le virement", "sendSummaryBitcoinPendingTxDescription": "Vous ne pouvez avoir qu'un virement BTC en attente à la fois. Veuillez attendre qu'il soit traité avant d'en envoyer un nouveau.", "sendFungibleSatProtectionTitle": "Envoi d'une protection Sat", "sendFungibleSatProtectionExplainer": "Phantom garantit que vos Ordinals et BRC20 ne seront pas utilisés pour des frais de transaction ou des transferts de bitcoins.", "sendFungibleTransferFee": "Frais de transfert du jeton", "sendFungibleTransferFeeToolTip": "Le créateur de ce jeton perçoit une commission sur chaque transfert. Il ne s'agit pas de frais facturés ou perçus par Phantom.", "sendFungibleInterestBearingPercent": "Taux d'intérêt en vigueur", "sendFungibleNonTransferable": "Non transférable", "sendFungibleNonTransferableToolTip": "Ce jeton ne peut pas être transféré sur un autre compte.", "sendFungibleNonTransferableYes": "O<PERSON>", "sendStatusErrorMessageInterpolated": "Une erreur est survenue lors de la tentative d'envoi de jetons à <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "Votre solde est insuffisant pour pouvoir finaliser la transaction.", "sendStatusErrorTitle": "Envoi impossible", "sendStatusLoadingTitle": "Envoi…", "sendStatusSuccessMessageInterpolated": "Vos jetons ont bien été envoyés à <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Envoyé !", "sendStatusConfirmedSuccessTitle": "Envoyé !", "sendStatusSubmittedSuccessTitle": "Transaction envoyée", "sendStatusEstimatedTransactionTime": "<PERSON><PERSON>e estimée de la transaction : {{time}}", "sendStatusViewTransaction": "Voir la transaction", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> à <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> a bien été envoyé à <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> a bien été envoyé à <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> n'a pas pu être envoyé à <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "Code d'erreur : {{code}}", "sendFormErrorInsufficientBalance": "Solde insuffisant", "sendFormErrorEmptyAmount": "Quantité requise", "sendFormInvalidAddress": "Adresse {{assetName}} non valide", "sendFormInvalidUsernameOrAddress": "Nom d'utilisateur ou adresse non valide", "sendFormErrorInvalidSolanaAddress": "<PERSON><PERSON><PERSON> invalide", "sendFormErrorInvalidTwitterHandle": "Cet identifiant Twitter n'est pas inscrit", "sendFormErrorInvalidDomain": "Ce domaine n'est pas inscrit", "sendFormErrorInvalidUsername": "Ce nom d'utilisateur n'est pas inscrit", "sendFormErrorMinRequiredInterpolated": "Au moins {{tokenName}} {{minAmount}} requis", "sendRecipientTextareaPlaceholder": "Adresse SOL du destinataire", "sendRecipientTextAreaPlaceholder2": "<PERSON><PERSON><PERSON> {{symbol}} du des<PERSON><PERSON>", "sendMemoOptional": "<PERSON><PERSON><PERSON> (facultatif)", "sendMemo": "Mémo ", "sendOptional": "facultatif", "settings": "Réglages", "settingsDapps": "dApps", "settingsSelectedAccount": "Co<PERSON>e <PERSON>", "settingsAddressBookNoLabel": "Aucune éti<PERSON>", "settingsAddressBookPrimary": "Carnet d'adresses", "settingsAddressBookRecentlyUsed": "Utilisées récemment", "settingsAddressBookSecondary": "<PERSON><PERSON><PERSON> les adresses utilisées régulièrement", "settingsAutoLockTimerPrimary": "Minuteur de verrouillage automatique", "settingsAutoLockTimerSecondary": "Modifiez la durée de votre minuteur de verrouillage automatique", "settingsChangeLanguagePrimary": "Changer la langue", "settingsChangeLanguageSecondary": "Modifiez la langue d'affichage", "settingsChangeNetworkPrimary": "Modifier le réseau", "settingsChangeNetworkSecondary": "Modifiez vos paramètres réseau", "settingsChangePasswordPrimary": "Modifier le mot de passe", "settingsChangePasswordSecondary": "Modifiez le mot de passe de votre écran de déverrouillage", "settingsCompleteBetaSurvey": "Remplissez l'enquête sur la bêta", "settingsDisplayLanguage": "Langue d'affichage", "settingsErrorCannotExportLedgerPrivateKey": "Impossible d'exporter la clé privée Ledger", "settingsErrorCannotRemoveAllWallets": "Impossible de supprimer tous les comptes", "settingsExportPrivateKey": "Afficher la clé privée", "settingsNetworkMainnetBeta": "<PERSON><PERSON><PERSON>", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "R<PERSON>eau RPC Phantom", "settingsTestNetworks": "Réseaux test", "settingsUseCustomNetworks": "Utiliser des réseaux personnalisés", "settingsTestnetMode": "Mode Testnet", "settingsTestnetModeDescription": "S'applique aux soldes et connexion d'application.", "settingsWebViewDebugging": "Débogage de la vue web", "settingsWebViewDebuggingDescription": "Vous autorise à inspecter et à déboguer les vues web du navigateur de l'application.", "settingsTestNetworksInfo": "Le passage à un réseau Testnet est uniquement destiné à des fins de test. Veuillez noter que les jetons des réseaux Testnet n'ont aucune valeur monétaire.", "settingsEmojis": "Emojis", "settingsNoAddresses": "<PERSON><PERSON><PERSON> adresse", "settingsAddressBookEmptyHeading": "Votre Carnet d'adresses est vide", "settingsAddressBookEmptyText": "Cliquez sur les boutons « + » ou « Ajouter une adresse » pour ajouter vos adresses favorites", "settingsEditWallet": "Modifier le compte", "settingsNoTrustedApps": "Aucune application de confiance", "settingsNoConnections": "Pas encore de connexion.", "settingsRemoveWallet": "Supprimer le compte", "settingsResetApp": "Réinitialiser l'application", "settingsBlocked": "<PERSON><PERSON><PERSON><PERSON>", "settingsBlockedAccounts": "<PERSON><PERSON><PERSON> blo<PERSON>", "settingsNoBlockedAccounts": "Pas de compte bloqué.", "settingsRemoveSecretPhrase": "Supprimer la Phrase secrète", "settingsResetAppWithSecretPhrase": "Réinitialiser l'application avec la Phrase secrète", "settingsResetSecretRecoveryPhrase": "Réinitialiser la phrase de récupération secrète", "settingsShowSecretRecoveryPhrase": "Afficher la phrase de récupération", "settingsShowSecretRecoveryPhraseSecondary": "Afficher la Phrase de récupération", "settingsShowSecretRecoveryPhraseTertiary": "Afficher la Phrase secrète", "settingsTrustedAppsAutoConfirmActiveUntil": "Jusq<PERSON>'à {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Confirmation automatique", "settingsTrustedAppsDisclaimer": "N'activer la confirmation automatique que sur les sites de confiance", "settingsTrustedAppsLastUsed": "Utilisée il y a {{formattedTimestamp}}", "settingsTrustedAppsPrimary": "Applications connectées", "settingsTrustedApps": "Applications de confiance", "settingsTrustedAppsRevoke": "Révoquer", "settingsTrustedAppsRevokeToast": "{{trustedApp}} déconnecté", "settingsTrustedAppsSecondary": "Configurez vos applications de confiance", "settingsTrustedAppsToday": "<PERSON><PERSON><PERSON>'hui", "settingsTrustedAppsYesterday": "<PERSON>er", "settingsTrustedAppsLastWeek": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsBeforeYesterday": "Plus tôt", "settingsTrustedAppsDisconnectAll": "Déconnexion de tout", "settingsTrustedAppsDisconnectAllToast": "Toutes les applications connectées", "settingsTrustedAppsEndAutoConfirmForAll": "Terminer la confirmation automatique pour tout", "settingsTrustedAppsEndAutoConfirmForAllToast": "Toutes les sessions en confirmation automatique ont été fermées", "settingsSecurityPrimary": "Sécurité et confidentialité", "settingsSecuritySecondary": "Mettez à jour vos paramètres de sécurité", "settingsActiveNetworks": "Réseaux actifs", "settingsActiveNetworksAll": "Tous", "settingsActiveNetworksSolana": "Solana uniquement", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana est le réseau par défaut et reste toujours activé.", "settingsDeveloperPrimary": "Paramètres pour développeurs", "settingsAdvanced": "Paramètres avancés", "settingsTransactions": "Paramètres de la transaction", "settingsAutoConfirm": "Paramètres de la confirmation automatique", "settingsSecurityAnalyticsPrimary": "Partager des données analytiques anonymes", "settingsSecurityAnalyticsSecondary": "Activer pour nous aider à nous améliorer", "settingsSecurityAnalyticsHelper": "Phantom n'utilise pas vos informations personnelles à des fins analytiques", "settingsSuspiciousCollectiblesPrimary": "Masquer les objets à collectionner suspects", "settingsSuspiciousCollectiblesSecondary": "Basculer pour masquer les objets à collectionner signalés", "settingsPreferredBitcoinAddress": "<PERSON>resse Bitcoin préférée", "settingsEnabledAddressesUpdated": "Adresses visibles mises à jour !", "settingsEnabledAddresses": "Adresses activées", "settingsBitcoinPaymentAddressForApps": "Adresse de paiement pour les applications", "settingsBitcoinOrdinalsAddressForApps": "Adresse Ordinals pour les applications", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Lorsque les deux types d'adresses ci-dessus sont activés, pour certaines applications comme Magic Eden, votre adresse Segwit native sera utilisée pour financer les achats. Les actifs achetés seront reçus dans votre adresse <PERSON>.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "L'adresse Bitcoin par défaut dans Phantom pour assurer une compatibilité.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(<PERSON><PERSON><PERSON>)", "settingsPreferredBitcoinAddressTaprootExplainer": "Le type d'adresse le plus moderne, dont les frais de transaction sont généralement moins élevés.", "settingsPreferredExplorers": "Explorateur préféré", "settingsPreferredExplorersSecondary": "Changez pour votre explorateur de blockchain préféré", "settingsCustomGasControls": "Contrôles de l'essence personnalisés", "settingsSupportDesk": "Service d'assistance", "settingsSubmitATicket": "Envoyer un ticket", "settingsAttachApplicationLogs": "Jo<PERSON>re les journaux d'application", "settingsDownloadApplicationLogs": "Télécharger les journaux d'application", "settingsDownloadApplicationLogsShort": "Télécharger les journaux", "settingsDownloadApplicationLogsHelper": "Contient des données locales, des rapports de plantage et des adresses de portefeuilles publics pour aider à résoudre les problèmes de l'Assistance Phantom", "settingsDownloadApplicationLogsWarning": "Aucunes données sensibles, comme les phrases de sauvegarde ou les clés privées, ne sont incluses.", "settingsWallet": "Portefeuille", "settingsPreferences": "Préférences", "settingsSecurity": "Sécurité", "settingsDeveloper": "Développeur", "settingsSupport": "Assistance", "settingsWalletShortcutsPrimary": "Afficher les Raccourcis de portefeuille", "settingsAppIcon": "Icône de l'application", "settingsAppIconDefault": "<PERSON><PERSON> <PERSON><PERSON>", "settingsAppIconLight": "<PERSON>", "settingsAppIconDark": "Sombre", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON><PERSON>", "settingsSearchResultSelected": "Sélectionné", "settingsSearchResultExport": "Exporter", "settingsSearchResultSeed": "Code de sauvegarde", "settingsSearchResultTrusted": "De confiance", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "État", "settingsSearchResultLogs": "<PERSON><PERSON><PERSON>", "settingsSearchResultBiometric": "Biométrique", "settingsSearchResultTouch": "Tactil", "settingsSearchResultFace": "Faciale", "settingsSearchResultShortcuts": "<PERSON><PERSON><PERSON><PERSON>", "settingsAllSitesPermissionsTitle": "Accéder à Phantom sur tous les sites", "settingsAllSitesPermissionsSubtitle": "Permet d'utiliser des applications de manière transparente avec Phantom, sans cliquer sur l'extension", "settingsAllSitesPermissionsDisabled": "Votre navigateur ne permet pas de modifier ce paramètre", "settingsSolanaCopyTransaction": "Activer Co<PERSON>r la <PERSON>", "settingsSolanaCopyTransactionDetails": "Co<PERSON>r les données de la transaction séquentielles dans le presse-papiers", "settingsAutoConfirmHeader": "Confirmation automatique", "refreshWebpageToApplyChanges": "Actualiser la page web pour appliquer les modifications", "settingsExperimentalTitle": "Fonctionnalités expérimentales", "settingsExprimentalSolanaActionsSubtitle": "Développer automatiquement les boutons d'action Solana lorsque des liens pertinents sont détectés sur X.com", "stakeAccountCardActiveStake": "Enjeu actif", "stakeAccountCardBalance": "Solde", "stakeAccountCardRentReserve": "Réserve de location", "stakeAccountCardRewards": "<PERSON><PERSON><PERSON> ré<PERSON>pen<PERSON>", "stakeAccountCardRewardsTooltip": "Il s'agit de la récompense la plus récente que vous avez gagnée pour votre enjeu. Vous recevez une récompense tous les 3 jours.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "Verrouiller jusqu'au", "stakeRewardsHistoryTitle": "Historique des récompenses", "stakeRewardsActivityItemTitle": "Récompenses", "stakeRewardsHistoryEmptyList": "<PERSON><PERSON>ne ré<PERSON>", "stakeRewardsTime_zero": "<PERSON><PERSON><PERSON>'hui", "stakeRewardsTime_one": "<PERSON>er", "stakeRewardsTime_other": "Il y a {{count}} jours", "stakeRewardsItemsPagingFailed": "Échec du chargement des anciennes récompenses.", "stakeAccountCreateAndDelegateErrorStaking": "Il y a eu un problème lors de la mise en jeu sur ce validateur. Veuillez réessayer.", "stakeAccountCreateAndDelegateSolStaked": "SOL mis en jeu !", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Vos SOL commenceront à rapporter des récompenses <1></1> dans les prochains jours une fois le compte d'enjeu actif.", "stakeAccountCreateAndDelegateStakingFailed": "Échec de la mise en jeu", "stakeAccountCreateAndDelegateStakingSol": "Mise en jeu des SOL…", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Nous créons un compte d'enjeu, puis nous déléguons vos SOL à", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Nous créons un compte d'enjeu, puis nous déléguons vos SOL à {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Voir la transaction", "stakeAccountDeactivateStakeSolUnstaked": "SOL retirés de l'enjeu !", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Vous pourrez retirer votre enjeu <1></1> dans les prochains jours une fois le compte d'enjeu inactif.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Vous pourrez retirer votre enjeu dans les prochains jours une fois le compte d'enjeu inactif.", "stakeAccountDeactivateStakeUnstakingFailed": "Échec du retrait de l'enjeu", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Il y a eu un problème lors du retrait de l'enjeu de ce validateur. Veuillez réessayer.", "stakeAccountDeactivateStakeUnstakingSol": "Retrait de l'enjeu des SOL…", "stakeAccountDeactivateStakeUnstakingSolDescription": "Nous commençons le processus du retrait de l'enjeu de vos SOL.", "stakeAccountDeactivateStakeViewTransaction": "Voir la transaction", "stakeAccountDelegateStakeSolStaked": "SOL mis en jeu !", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Vos SOL commenceront à gagner des récompenses <1></1> dans les prochains jours une fois le compte d'enjeu actif.", "stakeAccountDelegateStakeStakingFailed": "Échec de la mise en jeu", "stakeAccountDelegateStakeStakingFailedDescription": "Il y a eu un problème lors de la mise en jeu sur ce validateur. Veuillez réessayer.", "stakeAccountDelegateStakeStakingSol": "Mise en jeu des SOL…", "stakeAccountDelegateStakeStakingSolDescription": "Nos déléguons vos SOL.", "stakeAccountDelegateStakeViewTransaction": "Voir la transaction", "stakeAccountListActivationActivating": "Activation", "stakeAccountListActivationActive": "Actif", "stakeAccountListActivationInactive": "Inactif", "stakeAccountListActivationDeactivating": "Désactivation", "stakeAccountListErrorFetching": "Nous n'avons pas pu récupérer les comptes d'enjeu. Veuillez réessayer plus tard.", "stakeAccountListNoStakingAccounts": "Aucun compte d'enjeu", "stakeAccountListReload": "Recharger", "stakeAccountListViewPrimaryText": "<PERSON><PERSON><PERSON> enjeu", "stakeAccountListViewStakeSOL": "Mettre des SOL en jeu", "stakeAccountListItemStakeFee": "{{fee}} de frais", "stakeAccountViewActionButtonRestake": "Remettre en jeu", "stakeAccountViewActionButtonUnstake": "<PERSON><PERSON><PERSON> l'enjeu", "stakeAccountViewError": "<PERSON><PERSON><PERSON>", "stakeAccountViewPrimaryText": "<PERSON><PERSON><PERSON> enjeu", "stakeAccountViewRestake": "Remettre en jeu", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Vos SOL sont actuellement mis en jeu avec un validateur. V<PERSON> devrez retirer l'enjeu pour <1></1>accéder à ces fonds. <3>En savoir plus</3>", "stakeAccountViewStakeInactive": {"part1": "Ce compte d'enjeu est inactif. Pensez à retirer son enjeu ou à trouver un validateur à qui le déléguer.", "part2": "En savoir plus"}, "stakeAccountViewStakeNotFound": "Compte d'enjeu introuvable.", "stakeAccountViewViewOnExplorer": "Voir dans {{explorer}}", "stakeAccountViewWithdrawStake": "<PERSON><PERSON><PERSON> l'enjeu", "stakeAccountViewWithdrawUnstakedSOL": "Retirer les SOL non mis en jeu", "stakeAccountInsufficientFunds": "SOL disponibles insuffisants pour débloquer ou retirer des fonds.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL retirés !", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Vos SOL ont été retirés.", "part2": "Ce compte d'enjeu sera automatiquement supprimé dans les prochaines minutes."}, "stakeAccountWithdrawStakeViewTransaction": "Voir la transaction", "stakeAccountWithdrawStakeWithdrawalFailed": "Échec du retrait", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Il y a eu un problème lors du retrait de ce compte d'enjeu. Veuillez réessayer.", "stakeAccountWithdrawStakeWithdrawingSol": "Retrait des SOL…", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Nous retirons vos SOL de ce compte d'enjeu.", "startEarningSolAccount": "compte", "startEarningSolAccounts": "comptes", "startEarningSolErrorClosePhantom": "<PERSON><PERSON><PERSON>ez ici et réessayez", "startEarningSolErrorTroubleLoading": "Problème de chargement de l'enjeu", "startEarningSolLoading": "Chargement…", "startEarningSolPrimaryText": "Commencez à gagner des SOL", "startEarningSolSearching": "Recherche de comptes d'enjeu", "startEarningSolStakeTokens": "Partagez des jetons et gagnez des récompenses", "startEarningSolYourStake": "<PERSON><PERSON><PERSON> enjeu", "unwrapFungibleTitle": "<PERSON><PERSON><PERSON> en {{tokenSymbol}}", "unwrapFungibleDescription": "Re<PERSON>rer depuis {{fromToken}} pour {{toToken}}", "unwrapFungibleConfirmSwap": "Confirmer l'échange", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Frais estimés", "swapFeesFees": "<PERSON><PERSON>", "swapFeesPhantomFee": "Frais de Phantom", "swapFeesPhantomFeeDisclaimer": "Nous trouvons toujours le meilleur prix possible auprès des meilleurs fournisseurs de liquidités. Des frais de {{feePercentage}} sont automatiquement pris en compte dans cette cotation.", "swapFeesRate": "Prix", "swapFeesRateDisclaimer": "Le meilleur taux trouvé par Jupiter Aggregator à travers plusieurs échanges décentralisés.", "swapFeesRateDisclaimerMultichain": "Le meilleur taux trouvé à travers plusieurs échanges décentralisés.", "swapFeesPriceImpact": "Impact sur le prix", "swapFeesHighPriceImpact": "Fort impact sur le prix", "swapFeesPriceImpactDisclaimer": "La différence entre le prix du marché et le prix estimé en fonction de la taille de votre transaction.", "swapFeesSlippage": "Glissement", "swapFeesHighSlippage": "Tolérance au glissement élevée", "swapFeesHighSlippageDisclaimer": "Votre transaction échouera si le prix change désavantageusement plus que {{slippage}} %.", "swapTransferFee": "Frais de transfer<PERSON>", "swapTransferFeeDisclaimer": "Échanger des ${{symbol}} comprend {{feePercent}} % de frais de virement établis par le créateur du jeton, pas Phantom.", "swapTransferFeeDisclaimerMany": "Échanger les jetons sélectionnés comprend {{feePercent}} % de frais établis par les créateurs des jetons, pas Phantom.", "swapFeesSlippageDisclaimer": "Montant pour lequel le prix de votre transaction peut s'écarter du devis fourni.", "swapFeesProvider": "Fournisseur", "swapFeesProviderDisclaimer": "L'échange décentralisé utilisé pour effectuer votre transaction.", "swapEstimatedTime": "Temps estimé", "swapEstimatedTimeShort": "Temps estimé", "swapEstimatedTimeDisclaimer": "L'estimation du temps d'achèvement du pont variera en fonction de plusieurs facteurs qui affectent la vitesse des transactions.", "swapSettingsButtonCommand": "<PERSON><PERSON><PERSON><PERSON>r les paramètres d'échange", "swapQuestionRetry": "Réessayer ?", "swapUnverifiedTokens": "Jetons non vérifiés", "swapSectionTitleTokens": "{{section}} jetons", "swapFlowYouPay": "Vous payez", "swapFlowYouReceive": "<PERSON><PERSON> rece<PERSON>", "swapFlowActionButtonText": "Vérifier la transaction", "swapAssetCardTokenNetwork": "{{symbol}} sur {{network}}", "swapAssetCardMaxButton": "Max", "swapAssetCardSelectTokenAndNetwork": "Sélectionnez un jeton et un réseau", "swapAssetCardBuyTitle": "<PERSON><PERSON> rece<PERSON>", "swapAssetCardSellTitle": "Vous payez", "swapAssetWarningUnverified": "Ce jeton n'a pas été vérifié. N'interagissez qu'avec des jetons en lesquels vous avez confiance.", "swapAssetWarningPermanentDelegate": "Un délégué peut brûler ou transférer ces jetons de manière permanente.", "swapSlippageSettingsTitle": "Paramètres des glissements", "swapSlippageSettingsSubtitle": "Votre transaction échouera si la variance du prix dépasse le glissement. Une valeur trop élevée entraînera une transaction défavorable.", "swapSlippageSettingsCustom": "<PERSON><PERSON><PERSON><PERSON>", "swapSlippageSettingsHighSlippageWarning": "Votre transaction peut passer en première ligne et donner lieu à un échange défavorable.", "swapSlippageSettingsCustomMinError": "Veuillez saisir une valeur supérieure à {{minSlippage}} %.", "swapSlippageSettingsCustomMaxError": "Veuillez saisir une valeur inférieure à {{maxSlippage}} %.", "swapSlippageSettingsCustomInvalidValue": "Veuillez saisir une valeur valide.", "swapSlippageSettingsAutoSubtitle": "Phantom trouvera le glissement le plus faible pour un échange réussi.", "swapSlippageSettingsAuto": "Auto", "swapSlippageSettingsFixed": "Fixe", "swapSlippageOptInTitle": "Glissement automatique", "swapSlippageOptInSubtitle": "Phantom trouvera le taux de glissement le plus bas pour un échange réussi. Vous pouvez modifier ce paramètre à tout moment dans Swapper → Paramètres de glissement.", "swapSlippageOptInEnableOption": "Activer le glissement automatique", "swapSlippageOptInRejectOption": "Continuer avec un glissement fixe", "swapQuoteFeeDisclaimer": "Le devis inclut une commission Phantom de {{feePercentage}}", "swapQuoteMissingContext": "Contexte de devis d'échange manquant", "swapQuoteErrorNoQuotes": "Tentative d'échange sans devis", "swapQuoteSolanaNetwork": "<PERSON><PERSON><PERSON>", "swapQuoteNetwork": "<PERSON><PERSON><PERSON>", "swapQuoteOneTimeSerumAccount": "Compte Serum à usage unique", "swapQuoteOneTimeTokenAccount": "Compte de jeton à usage unique", "swapQuoteBridgeFee": "Frais d'échange de chaîne croisée", "swapQuoteDestinationNetwork": "Réseau de destination", "swapQuoteLiquidityProvider": "Fournisseur de liquidité", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON><PERSON>", "swapReviewFlowPrimaryText": "Vérifier la commande", "swapReviewFlowYouPay": "Vous payez", "swapReviewFlowYouReceive": "<PERSON><PERSON> rece<PERSON>", "swapReviewInsufficientBalance": "Fonds insuffisants", "ugcSwapWarningTitle": "Avertissement", "ugcSwapWarningBody1": "Ce jeton s'échange sur le lanceur de jetons {{programName}}.", "ugcSwapWarningBody2": "La valeur de ces jetons peut fluctuer fortement, entraînant des gains ou des pertes financières considérables. Les transactions se font à vos risques et périls.", "ugcSwapWarningConfirm": "Je comprends", "bondingCurveProgressLabel": "Progression de la Bonding Curve", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Dans un modèle en bonding curve, les prix des jetons sont déterminés par la forme de la courbe, fluctuant en fonction des achats et des ventes. Lorsque les jetons sont vendus, toutes les liquidités sont déposées dans Raydium et brûlées.", "ugcFungibleWarningBanner": "Ce jeton est échangé sur {{programName}}", "ugcCreatedRowLabel": "Création le", "ugcStatusRowLabel": "Statut", "ugcStatusRowValue": "Graduée", "swapTxConfirmationReceived": "<PERSON><PERSON>u !", "swapTxConfirmationSwapFailed": "Échec de l'échange", "swapTxConfirmationSwapFailedStaleQuota": "Le devis n'est plus valide. Veuillez réessayer.", "swapTxConfirmationSwapFailedSlippageLimit": "Votre glissement est trop faible pour cet échange. Veuillez augmenter votre glissement en haut de l'écran d'Échange et réessayer.", "swapTxConfirmationSwapFailedInsufficientBalance": "Nous n'avons pas pu compléter la demande. Vous ne disposez pas d'un solde suffisant pour effectuer la transaction.", "swapTxConfirmationSwapFailedEmptyRoute": "La liquidité de cette paire de jetons a changé. Nous n'avons pas pu trouver de devis approprié. Veuillez réessayer ou ajuster les quantités de jetons.", "swapTxConfirmationSwapFailedAcountFrozen": "Ce jeton a été gelé par son créateur. Vous ne pouvez pas envoyer ou échanger ce jeton.", "swapTxConfirmationSwapFailedTryAgain": "Échec de l'échange, ve<PERSON><PERSON><PERSON> rées<PERSON>er", "swapTxConfirmationSwapFailedUnknownError": "Nous n'avons pas pu finaliser l'échange. Vos fonds n'ont pas été affectés. Veuillez réessayer. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Nous n'avons pas pu simuler l'échange. Vos fonds n'ont pas été affectés. Veuillez réessayer.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Nous n'avons pas pu finaliser l'échange. Vos fonds n'ont pas été affectés. Veuillez réessayer. ", "swapTxConfirmationSwapFailedInsufficientGas": "Votre compte ne dispose pas d'assez de fonds pour finaliser cette transaction. Veuillez ajouter plus de fonds à votre compte et réessayer.", "swapTxConfirmationSwapFailedLedgerReject": "L'échange a été refusé par l'utilisateur sur le périphérique matériel.", "swapTxConfirmationSwapFailedLedgerConnectionError": "L'échange a été refusé en raison d'une erreur de connexion de l'appareil. Veuillez réessayer.", "swapTxConfirmationSwapFailedLedgerSignError": "L'échange a été refusé en raison d'une erreur de signature sur l'appareil. Veuillez réessayer.", "swapTxConfirmationSwapFailedLedgerError": "L'échange a été refusé en raison d'une erreur d'appareil. Veuillez réessayer.", "swapTxConfirmationSwappingTokens": "Échange des jetons…", "swapTxConfirmationTokens": "Jet<PERSON>", "swapTxConfirmationTokensDeposited": "C'est fait ! Les jetons ont été déposés sur votre portefeuille", "swapTxConfirmationTokensDepositedTitle": "C'est fait !", "swapTxConfirmationTokensDepositedBody": "Les jetons ont été déposés sur votre portefeuille", "swapTxConfirmationTokensWillBeDeposited": "seront déposés sur votre portefeuille une fois la transaction terminée", "swapTxConfirmationViewTransaction": "Voir la transaction", "swapTxBridgeSubmitting": "Envoi de la transaction", "swapTxBridgeSubmittingDescription": "Échange de {{sellAmount}} sur {{sellNetwork}} contre {{buyAmount}} sur {{buyNetwork}}", "swapTxBridgeFailed": "Échec de l'envoi de la transaction", "swapTxBridgeFailedDescription": "Nous n'avons pas pu terminer la requête.", "swapTxBridgeSubmitted": "Transaction envoyée", "swapTxBridgeSubmittedDescription": "Durée estimée de la transaction : {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "Vous pouvez sans crainte fermer cette fenêtre.", "swapperSwitchTokens": "Changer les jetons", "swapperMax": "Max", "swapperTooltipNetwork": "<PERSON><PERSON><PERSON>", "swapperTooltipPrice": "Prix", "swapperTooltipAddress": "Contrat", "swapperTrendingSortBy": "Trier par", "swapperTrendingTimeFrame": "Période", "swapperTrendingNetwork": "<PERSON><PERSON><PERSON>", "swapperTrendingRank": "<PERSON>ng", "swapperTrendingTokens": "Jetons tendance", "swapperTrendingVolume": "Volume", "swapperTrendingPrice": "Prix", "swapperTrendingPriceChange": "Modification du prix", "swapperTrendingMarketCap": "Plafond du march<PERSON>", "swapperTrendingTimeFrame1h": "1 h", "swapperTrendingTimeFrame24h": "24 h", "swapperTrendingTimeFrame7d": "7 j", "swapperTrendingTimeFrame30d": "30 j", "swapperTrendingNoTokensFound": "Aucun jeton trouvé.", "switchToggle": "Basculer", "termsOfServiceActionButtonAgree": "J'accepte", "termsOfServiceDisclaimerFeesDisabledInterpolated": "En cliquant sur <1>« J'accepte »</1>, vous acceptez les <3>Conditions générales</3> de l'échange de jetons avec Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Nous avons révisé nos Conditions d'utilisation. En cliquant sur <1>« J'accepte »</1>, vous acceptez nos nouvelles <3>Conditions d'utilisation</3>.<5></5><6></6>Nos nouvelles conditions d'utilisation incluent une nouvelle <8>structure de frais</8> pour certains produits.", "termsOfServicePrimaryText": "Conditions d'utilisation", "tokenRowUnknownToken": "Jet<PERSON> inconnu", "transactionsAppInteraction": "Interaction application", "transactionsFailedAppInteraction": "Échec de l'intéraction application", "transactionsBidOnInterpolated": "Enchère sur {{name}}", "transactionsBidFailed": "Échec de l'enchère", "transactionsBoughtInterpolated": "<PERSON><PERSON><PERSON> de {{name}}", "transactionsBoughtCollectible": "Objet à collectionner acheté", "transactionBridgeInitiated": "Pont initié", "transactionBridgeInitiatedFailed": "Échec de l'initiation du pont", "transactionBridgeStatusLink": "Vérifiez le statut sur LI.FI", "transactionsBuyFailed": "Échec de l'achat", "transactionsBurnedSpam": "Spam brûlé", "transactionsBurned": "B<PERSON><PERSON><PERSON>", "transactionsUnwrapped": "Déballé", "transactionsUnwrappedFailed": "Échec du déballage", "transactionsCancelBidOnInterpolated": "Enchère annulée sur {{name}}", "transactionsCancelBidOnFailed": "Échec de l'annulation de l'enchère", "transactionsError": "<PERSON><PERSON><PERSON>", "transactionsFailed": "Échec", "transactionsSwapped": "Échangées", "transactionsFailedSwap": "Échec de l'échange", "transactionsFailedBurn": "Échec du brûlage", "transactionsFrom": "De", "transactionsListedInterpolated": "Listage de {{name}}", "transactionsListedFailed": "Échec du listage", "transactionsNoActivity": "Aucune activité", "transactionsReceived": "<PERSON><PERSON><PERSON> ", "transactionsReceivedInterpolated": "{{amount}} SOL reçus", "transactionsSending": "Envoi…", "transactionsPendingCreateListingInterpolated": "Création de {{name}}", "transactionsPendingEditListingInterpolated": "Modification de {{name}}", "transactionsPendingSolanaPayTransaction": "Confirmation de la transaction Solana Pay", "transactionsPendingRemoveListingInterpolated": "Dé-listage de {{name}}", "transactionsPendingBurningInterpolated": "<PERSON><PERSON><PERSON><PERSON> de {{name}}", "transactionsPendingSending": "Envoi", "transactionsPendingSwapping": "Échange", "transactionsPendingBridging": "Ponts", "transactionsPendingApproving": "En approbation", "transactionsPendingCreatingAndDelegatingStake": "Création et délégation d'un enjeu", "transactionsPendingDeactivatingStake": "Désactivation d'un enjeu", "transactionsPendingDelegatingStake": "Délégation d'un enjeu", "transactionsPendingWithdrawingStake": "Retrait d'un enjeu", "transactionsPendingAppInteraction": "En attente d'une interaction avec l'application", "transactionsPendingBitcoinTransaction": "En attente d'une transaction BTC", "transactionsSent": "<PERSON><PERSON><PERSON>", "transactionsSendFailed": "Échec de l'envoi", "transactionsSwapOn": "<PERSON><PERSON><PERSON> sur {{dappName}}", "transactionsSentInterpolated": "{{amount}} SOL envoyés", "transactionsSoldInterpolated": "Vente de {{name}}", "transactionsSoldCollectible": "Objet à collectionné vendu", "transactionsSoldFailed": "Échec de la vente", "transactionsStaked": "<PERSON><PERSON><PERSON> p<PERSON>", "transactionsStakedFailed": "Échec de l'enjeu", "transactionsSuccess": "Su<PERSON>ès", "transactionsTo": "À", "transactionsTokenSwap": "É<PERSON><PERSON> de <PERSON>on", "transactionsUnknownAmount": "Inconnu", "transactionsUnlistedInterpolated": "Dé-listage de {{name}}", "transactionsUnstaked": "<PERSON><PERSON><PERSON> reti<PERSON>", "transactionsUnlistedFailed": "Échec du dé-listage", "transactionsDeactivateStake": "<PERSON><PERSON><PERSON><PERSON>", "transactionsDeactivateStakeFailed": "Échec de la désactivation de l'enjeu", "transactionsWaitingForConfirmation": "En attente de confirmation", "transactionsWithdrawStake": "<PERSON><PERSON><PERSON> l'enjeu", "transactionsWithdrawStakeFailed": "Échec du retrait de l'enjeu", "transactionCancelled": "<PERSON><PERSON><PERSON>", "transactionCancelledFailed": "Échec de l'annulation", "transactionApproveToken": "{{tokenSymbol}} approuvé", "transactionApproveTokenFailed": "Échec de l'approbation de {{tokenSymbol}}", "transactionApprovalFailed": "Échec de l'approbation", "transactionRevokeApproveToken": "{{tokenSymbol}} révoqué", "transactionRevokeApproveTokenFailed": "Échec de la révocation de {{tokenSymbol}}", "transactionRevokeFailed": "Échec de la révocation", "transactionApproveDetailsTitle": "Détails de l'approbation", "transactionCancelOrder": "Annuler la commande", "transactionCancelOrderFailed": "Échec de l'annulation de la commande", "transactionApproveAppLabel": "Application", "transactionApproveAmountLabel": "<PERSON><PERSON>", "transactionApproveTokenLabel": "<PERSON><PERSON>", "transactionApproveCollectionLabel": "Collection", "transactionApproveAllItems": "Approuver tous les éléments", "transactionSpendUpTo": "<PERSON><PERSON><PERSON><PERSON> jusqu'à", "transactionCancel": "Annuler la transaction", "transactionPrioritizeCancel": "Donner la priorité à l'annulation", "transactionSpeedUp": "Accélérer la transaction", "transactionCancelHelperText": "La transaction d'origine peut se terminer avant d'être annulée.", "transactionSpeedUplHelperText": "<PERSON><PERSON> per<PERSON>ra de maximiser la vitesse de votre transaction en fonction des conditions du réseau.", "transactionCancelHelperMobile": "Tenter d'annuler cette transaction coûtera <1>jusqu'à {{amount}}</1>. La transaction d'origine peut se terminer avant d'être annulée.", "transactionCancelHelperMobileWithEstimate": "Tenter d'annuler cette transaction coûtera <1>jusqu'à {{amount}}</1>. Elle devrait se terminer dans environ {{timeEstimate}}. La transaction d'origine peut se terminer avant l'annulation.", "transactionSpeedUpHelperMobile": "Il en coûtera <1>jusqu'à {{amount}}</1> pour maximiser la vitesse de cette transaction.", "transactionSpeedUpHelperMobileWithEstimate": "Il en coûtera <1>jusqu'à {{amount}}</1> pour maximiser la vitesse de cette transaction. Elle devrait se terminer dans environ {{timeEstimate}}.", "transactionEstimatedTime": "Temps estimé", "transactionCancelingSend": "Annulation de l'envoi", "transactionPrioritizingCancel": "Priorité donnée à l'annulation", "transactionCanceling": "Annulation", "transactionReplaceError": "Une erreur est survenue. Aucun frais ne sera débité de votre compte. Vous pouvez réessayer.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} insuffisant", "transactionGasLimitError": "Échec de l'estimation de la limite d'essence", "transactionGasEstimationError": "Échec de l'estimation de l'essence", "pendingTransactionCancel": "Annuler", "pendingTransactionSpeedUp": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "pendingTransactionStatus": "Statut", "pendingTransactionPending": "En attente", "pendingTransactionPendingInteraction": "En attente d'une interaction", "pendingTransactionCancelling": "Annulation", "pendingTransactionDate": "Date", "pendingTransactionNetworkFee": "Frais du réseau", "pendingTransactionEstimatedTime": "Temps estimé", "pendingTransactionEstimatedTimeHM": "{{hours}} h {{minutes}} m", "pendingTransactionEstimatedTimeMS": "{{minutes}} m {{seconds}} s", "pendingTransactionEstimatedTimeS": "{{seconds}} s", "pendingTransactionsSendingTitle": "Envoi de {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Inconnu", "pendingTransactionUnknownApp": "Application inconnue", "permanentDelegateTitle": "Délégu<PERSON>", "permanentDelegateValue": "Permanent", "permanentDelegateTooltipTitle": "Délégation permanente", "permanentDelegateTooltipValue": "Délégué permanent permet à un autre compte de gérer les jetons pour vous, ce qui comprend la possibilité de brûler et de transférer.", "unlockActionButtonUnlock": "Débloquer", "unlockEnterPassword": "Saisissez votre mot de passe", "unlockErrorIncorrectPassword": "Mot de passe incorrect", "unlockErrorSomethingWentWrong": "Un problème est survenu, veuil<PERSON>z réessayer plus tard", "unlockForgotPassword": "Mot de passe oublié", "unlockPassword": "Mot de passe", "forgotPasswordText": "Vous pouvez réinitialiser votre mot de passe en saisissant la phrase de récupération de 12 à 24 mots de votre portefeuille. Phantom ne peut pas récupérer votre mot de passe pour vous.", "appInfo": "Informations sur l'application", "lastUsed": "Dernière utilisation", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "Indisponible avec les comptes matériels.", "trustedAppAutoConfirmDisclaimer1": "Quand cette option est active, Phantom confirmera toutes les demandes de cette application sans vous en informer ou vous demander confirmation.", "trustedAppAutoConfirmDisclaimer2": "Activer cette option met vos fonds en danger de fraude. N'utilisez cette fonctionnalité qu'avec les applications de confiance.", "validationUtilsPasswordIsRequired": "Mot de passe requis", "validationUtilsPasswordLength": "Le mot de passe doit faire plus de 8 caractères", "validationUtilsPasswordsDontMatch": "Les mots de passe ne correspondent pas", "validationUtilsPasswordCantBeSame": "Vous ne pouvez pas utiliser votre ancien mot de passe", "validatorCardEstimatedApy": "APY estimé", "validatorCardCommission": "Commission", "validatorCardTotalStake": "Enjeu total", "validatorCardNumberOfDelegators": "# de délégateurs", "validatorListChooseAValidator": "Choisissez un validateur", "validatorListErrorFetching": "Nous n'avons pas pu récupérer les validateurs. Veuillez réessayer plus tard.", "validatorListNoResults": "Aucun résultat", "validatorListReload": "Recharger", "validatorInfoTooltip": "Validateur", "validatorInfoTitle": "Validateurs", "validatorInfoDescription": "En empilant vos SOL sur un validateur, vous contribuez à la performance et à la sécurité du réseau Solana, et, ce faisant, vous recevez un petit pourcentage des récompenses du réseau.", "validatorApyInfoTooltip": "APY est.", "validatorApyInfoTitle": "APY estimé", "validatorApyInfoDescription": "Il s'agit du taux de rendement que vous obtenez en misant votre SOL sur le validateur.", "validatorViewActionButtonStake": "<PERSON><PERSON><PERSON>", "validatorViewErrorFetching": "Impossible de récupérer les validateurs.", "validatorViewInsufficientBalance": "Solde insuffisant", "validatorViewMax": "Max", "validatorViewPrimaryText": "Commencer à mettre en jeu", "validatorViewDescriptionInterpolated": "Choisissez combien de SOL vous souhaitez <1></1> mettre en jeu avec ce validateur. <3>En savoir plus</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL requis pour mettre en jeu", "validatorViewValidator": "Validateur", "walletMenuItemsAddConnectWallet": "Ajouter/connecter un portefeuille", "walletMenuItemsBridgeAssets": "Déplacer les actifs", "walletMenuItemsHelpAndSupport": "Aide et assistance", "walletMenuItemsLockWallet": "Verrouiller le portefeuille", "walletMenuItemsResetSecretPhrase": "Réinitialiser la phrase secrète", "walletMenuItemsShowMoreAccounts": "Afficher {{count}} autre(s)…", "walletMenuItemsHideAccounts": "Masquer les comptes", "toggleMultiChainHeader": "Multichaîne", "disableMultiChainHeader": "Mode Solana uniquement", "disableMultiChainDetail1Header": "Foncez sur Solana", "disableMultiChainDetail1SecondaryText": "<PERSON><PERSON><PERSON> vos objets à collectionner, jetons et comptes Solana sans voir les autres chaînes.", "disableMultiChainDetail2Header": "Repassez en Multichaîne à tout moment", "disableMultiChainDetail2SecondaryText": "Vos soldes Ethereum et Polygon existants seront préservés quand vous ré<PERSON>rez Multichaîne.", "disableMultiChainButton": "Activer <PERSON> uniquement", "disabledMultiChainHeader": "Solana uniquement activé", "disabledMultiChainText": "V<PERSON> pouvez ré<PERSON>r Multichaîne à tout moment.", "enableMultiChainHeader": "<PERSON><PERSON>", "enabledMultiChainHeader": "Multichaîne activé", "enabledMultiChainText": "Ethereum et Polygon sont maintenant pris en charge dans votre portefeuille.", "incompatibleAccountHeader": "Compte incompatible", "incompatibleAccountInterpolated": "Veuillez supprimer ces comptes réservés à Ethereum avant d'activer le mode Solana uniquement : <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Quoi de neuf !", "welcomeToMultiChainPrimaryText": "Un portefeuille pour tout", "welcomeToMultiChainDetail1Header": "Prise en charge Ethereum et Polygon", "welcomeToMultiChainDetail1SecondaryText": "Tous vos tokens et NFT de Solana, Ethereum et Polygon au même endroit.", "welcomeToMultiChainDetail2Header": "Utilisez les applications que vous aimez", "welcomeToMultiChainDetail2SecondaryText": "Connectez-vous à des applications sur plusieurs chaînes sans changer de réseau.", "welcomeToMultiChainDetail3Header": "Importez votre portefeuille MetaMask", "welcomeToMultiChainDetail3SecondaryText": "Importez facilement toutes vos phrases de sauvegarde à travers Ethereum et Polygon.", "welcomeToMultiChainIntro": "Bienvenue sur Phantom Multichain", "welcomeToMultiChainIntroDesc": "Tous vos jetons et NFT de Solana, Ethereum et Polygon au même endroit. Votre portefeuille unique pour tout.", "welcomeToMultiChainAccounts": "Refonte des comptes multichaînes", "welcomeToMultiChainAccountsDesc": "<PERSON><PERSON><PERSON> de la multichaîne, chaque compte a maintenant des adresses ETH et Polygon correspondantes.", "welcomeToMultiChainApps": "Fonctionne partout", "welcomeToMultiChainAppsDesc": "Phantom est compatible avec toutes les applications sous Ethereum, Polygon et Solana. Cliquez sur « Connexion à MetaMask » et c'est bon.", "welcomeToMultiChainImport": "Importez de MetaMask, instantanément", "welcomeToMultiChainImportDesc": "Importez vos phrases secrètes ou vos clés privées à partir de portefeuilles comme MetaMask ou Coinbase Wallet. Tout au même endroit.", "welcomeToMultiChainImportInterpolated": "<0>Importez vos phrases secrètes</0> ou vos clés privées à partir de portefeuilles tels que MetaMask ou Coinbase Wallet. Tout au même endroit.", "welcomeToMultiChainTakeTour": "Faire la visite", "welcomeToMultiChainSwapperTitle": "Échanger sur Ethereum,\nPolygon et Solana", "welcomeToMultiChainSwapperDetail1Header": "Prise en charge Ethereum et Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Vous pouvez maintenant échanger facilement les jetons ERC-20 depuis votre portefeuille.", "welcomeToMultiChainSwapperDetail2Header": "Meilleurs prix et frais très bas", "welcomeToMultiChainSwapperDetail2SecondaryText": "Plus de 100 sources de liquidités et un acheminement intelligent des commandes pour un rendement maximal.", "networkErrorTitle": "<PERSON><PERSON><PERSON>", "networkError": "Nous ne pouvons malheureusement pas accéder au réseau. Veuillez réessayer plus tard.", "authenticationUnlockPhantom": "Déverrouiller Phantom", "errorAndOfflineSomethingWentWrong": "Un problème est survenu", "errorAndOfflineSomethingWentWrongTryAgain": "Veuillez réessayer.", "errorAndOfflineUnableToFetchAssets": "Nous n'avons pas pu récupérer les actifs. Veuillez réessayer plus tard.", "errorAndOfflineUnableToFetchCollectibles": "Nous n'avons pas pu récupérer les objets à collectionner. Veuillez réessayer plus tard.", "errorAndOfflineUnableToFetchSwap": "Nous n'avons pas pu récupérer les informations d'échange. Veuillez réessayer plus tard.", "errorAndOfflineUnableToFetchTransactionHistory": "Nous ne sommes pas en mesure d'obtenir l'historique de vos transactions pour le moment. Vérifiez votre connexion réseau ou réessayez plus tard.", "errorAndOfflineUnableToFetchRewardsHistory": "Nous n'avons pas pu récupérer l'historique des récompenses. Veuillez réessayer plus tard.", "errorAndOfflineUnableToFetchBlockedUsers": "Nous n'avons pas pu récupérer les utilisateurs bloqués. Veuillez réessayer plus tard.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Un problème est survenu lors de la vérification de votre commande, veuil<PERSON><PERSON> réessayer.", "sendSelectToken": "Sélectionner un jeton", "swapBalance": "Solde :", "swapTitle": "Échanger les jetons", "swapSelectToken": "Sélectionner un jeton", "swapYouPay": "Vous payez", "swapYouReceive": "<PERSON><PERSON> rece<PERSON>", "aboutPrivacyPolicy": "Politique de confidentialité", "aboutVersion": "Version {{version}}", "aboutVisitWebsite": "Visitez le site Web", "bottomSheetConnectTitle": "Connecter", "A11YbottomSheetConnectTitle": "Feuille du bas Connexion", "A11YbottomSheetCommandClose": "<PERSON><PERSON><PERSON> du bas Refuser", "A11YbottomSheetCommandBack": "<PERSON><PERSON><PERSON> du bas Retour", "bottomSheetSignTypedDataTitle": "Signer le message", "bottomSheetSignMessageTitle": "Signer le message", "bottomSheetSignInTitle": "Connexion", "bottomSheetSignInAndConnectTitle": "Connexion", "bottomSheetConfirmTransactionTitle": "Confirmer la transaction", "bottomSheetConfirmTransactionsTitle": "Confirmer les transactions", "bottomSheetSolanaPayTitle": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedTitle": "<PERSON><PERSON><PERSON>", "bottomSheetReadOnlyAccountTitle": "Mode lecture seule", "bottomSheetTransactionSettingsTitle": "Frais du réseau", "bottomSheetConnectDescription": "Connecter permettra à ce site de voir les soldes et les activités pour le compte sélectionné.", "bottomSheetSignInDescription": "Signer ce message prouvera que vous êtes propriétaire du compte sélectionné. Ne signez que les messages provenant d'applications auxquelles vous faites confiance.", "bottomSheetSignInAndConnectDescription": "Approuver permettra à ce site de voir les soldes et les activités pour le compte sélectionné.", "bottomSheetConfirmTransactionDescription": "Les modifications de solde sont estimées. Les montants et actifs impliqués ne sont pas garantis.", "bottomSheetConfirmTransactionsDescription": "Les modifications de solde sont estimées. Les montants et actifs impliqués ne sont pas garantis.", "bottomSheetSignTypedDataDescription": "Il s'agit uniquement d'une permission demande uniquement. La transaction ne peut pas être exécutée immédiatement.", "bottomSheetSignTypedDataSecondDescription": "Les modifications de solde sont estimées. Les montants et actifs impliqués ne sont pas garantis.", "bottomSheetSignMessageDescription": "Signer ce message prouvera que vous êtes propriétaire du compte sélectionné. Ne signez que les messages provenant d'applications auxquelles vous faites confiance.", "bottomSheetReadOnlyAccountDescription": "Impossible d'effectuer cette action en lecture seule.", "bottomSheetMessageRow": "Message", "bottomSheetStatementRow": "Déclaration", "bottomSheetAutoConfirmRow": "Confirmation automatique", "bottomSheetAutoConfirmOff": "Désactivée", "bottomSheetAutoConfirmOn": "Activée", "bottomSheetAccountRow": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedRow": "<PERSON><PERSON><PERSON>", "bottomSheetContractRow": "<PERSON><PERSON><PERSON> de contrat", "bottomSheetSpenderRow": "<PERSON>ress<PERSON> de l'utilisateur qui dépense", "bottomSheetNetworkRow": "<PERSON><PERSON><PERSON>", "bottomSheetNetworkFeeRow": "Frais du réseau", "bottomSheetEstimatedTimeRow": "Temps estimé", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Ne vous connectez qu'aux sites web auxquels vous faites confiance", "bottomSheetSignInRequestDisclaimer": "Ne vous identifiez que sur les sites web auxquels vous faites confiance", "bottomSheetSignatureRequestDisclaimer": "Confirmez uniquement si vous avez confiance en ce site web.", "bottomSheetFeaturedTransactionDisclaimer": "Un aperçu de la transaction vous sera présenter avant de passer à l'étape suivante qui vous permettra de la confirmer.", "bottomSheetIgnoreWarning": "Ignorer l'avertissement, continuer quand même", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "Aucune modification de solde n'a été trouvée. Veuillez procéder avec prudence et ne confirmer que si vous avez confiance en ce site.", "bottomSheetReadOnlyWarning": "Vous êtes en lecture seule sur cette adresse. Vous devrez importer la phrase secrète pour pouvoir signer des transactions et des messages.", "bottomSheetWebsiteIsUnsafeWarning": "Ce site n'est pas sûr et peut tenter de voler vos fonds.", "bottomSheetViewOnExplorer": "Voir dans l'explorateur", "bottomSheetTransactionSubmitted": "Transaction envoyée", "bottomSheetTransactionPending": "Transaction en attente", "bottomSheetTransactionFailed": "Échec de la transaction", "bottomSheetTransactionSubmittedDescription": "Votre transaction a été envoyée. Vous pouvez la consulter dans l'explorateur.", "bottomSheetTransactionFailedDescription": "Votre transaction a échoué. Veuillez réessayer.", "bottomSheetTransactionPendingDescription": "La transaction est en cours de traitement...", "transactionsFromInterpolated": "Depuis : {{from}}", "transactionsFromParagraphInterpolated": "Depuis {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON><PERSON>'hui", "transactionsToInterpolated": "Vers : {{to}}", "transactionsToParagraphInterpolated": "Vers {{to}}", "transactionsYesterday": "<PERSON>er", "addEditAddressAdd": "Ajouter une adresse", "addEditAddressDelete": "Supprimer une adresse", "addEditAddressDeleteTitle": "Voulez-vous vraiment supprimer cette adresse ?", "addEditAddressSave": "Enregistrer l'adresse", "dAppBrowserComingSoon": "Le navigateur dApp sera bientôt disponible !", "dAppBrowserSearchPlaceholder": "Sites, jetons, URL", "dAppBrowserOpenInNewTab": "<PERSON><PERSON><PERSON><PERSON>r dans un nouvel onglet", "dAppBrowserSuggested": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserFavorites": "<PERSON><PERSON><PERSON>", "dAppBrowserBookmarks": "Signets", "dAppBrowserBookmarkAdd": "Ajouter un signet", "dAppBrowserBookmarkRemove": "Supprimer le signet", "dAppBrowserUsers": "Utilisateurs", "dAppBrowserRecents": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserFavoritesDescription": "Vos favoris apparaissent ici", "dAppBrowserBookmarksDescription": "Vos signets apparaissent ici", "dAppBrowserRecentsDescription": "Les dapps récemment connectées apparaîtront ici", "dAppBrowserEmptyScreenDescription": "Saisissez une URL ou faites une recherche sur le Web", "dAppBrowserBlocklistScreenTitle": "{{origin}} est bloqué ! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom pense que ce site Web est malveillant et dangereux.", "part2": "Ce site a été signalé dans le cadre d'une base de données communautaire de sites d'hameçonnage et d'arnaques connus. Si vous pensez que le site a été signalé par erreur, vous pouvez faire remonter un problème."}, "dAppBrowserLoadFailedScreenTitle": "Échec du chargement", "dAppBrowserLoadFailedScreenDescription": "Une erreur est survenue lors du chargement de cette page", "dAppBrowserBlocklistScreenIgnoreButton": "Ignorer l'avertissement, afficher quand même", "dAppBrowserActionBookmark": "Signet", "dAppBrowserActionRemoveBookmark": "Supprimer le signet", "dAppBrowserActionRefresh": "Actualiser", "dAppBrowserActionShare": "Partager", "dAppBrowserActionCloseTab": "<PERSON><PERSON><PERSON> l'on<PERSON>t", "dAppBrowserActionEndAutoConfirm": "Terminer la confirmation automatique", "dAppBrowserActionDisconnectApp": "Déconnecter l'application", "dAppBrowserActionCloseAllTabs": "Fermer tous les onglets", "dAppBrowserNavigationAddressPlaceholder": "Saisissez une URL pour rechercher", "dAppBrowserTabOverviewMore": "Plus", "dAppBrowserTabOverviewAddTab": "Ajouter un onglet", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON><PERSON> l'on<PERSON>t", "dAppBrowserClose": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "Ajouter un signet", "dAppBrowserTabOverviewRemoveBookmark": "Supprimer le signet", "depositAssetListSuggestions": "Suggestions", "depositUndefinedToken": "<PERSON><PERSON><PERSON><PERSON>, impossible d'envoyer ce jeton", "onboardingImportRecoveryPhraseDetails": "Détails", "onboardingCreateRecoveryPhraseVerifyTitle": "Vous avez écrit la phrase de récupération secrète ?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Sans phrase de récupération secrète, vous ne pourrez plus accéder à votre clé ou tout actif qui lui est associé.", "onboardingCreateRecoveryPhraseVerifyYes": "O<PERSON>", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "Nous n'avons pas réussi à générer un compte, veuil<PERSON><PERSON> réessayer.", "onboardingDoneDescription": "Vous pouvez maintenant pleinement profiter de votre portefeuille.", "onboardingDoneGetStarted": "<PERSON><PERSON><PERSON><PERSON>", "zeroBalanceHeading": "Commençons !", "zeroBalanceBuyCryptoTitle": "Acheter des cryptos", "zeroBalanceBuyCryptoDescription": "Achetez vos premiers cryptos avec une carte de débit ou de crédit.", "zeroBalanceDepositTitle": "Transférer des cryptos", "zeroBalanceDepositDescription": "Déposez des cryptos à partir d'un autre portefeuille ou d'une bourse d'échange.", "onboardingImportAccountsEmptyResult": "Aucun compte trouvé", "onboardingImportAccountsAccountName": "Compte {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Compte social", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Nous avons trouvé {{numberOfWallets}} compte avec de l'activité", "onboardingImportAccountsFoundAccounts_other": "Nous avons trouvé {{numberOfWallets}} comptes avec de l'activité", "onboardingImportAccountsFoundAccountsNoActivity_one": "Nous avons trouvé {{numberOfWallets}} compte", "onboardingImportAccountsFoundAccountsNoActivity_other": "Nous avons trouvé {{numberOfWallets}} comptes", "onboardingImportRecoveryPhraseLessThanTwelve": "La phrase doit contenir au moins 12 mots.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "La phrase doit contenir exactement 12 ou 24 mots.", "onboardingImportRecoveryPhraseWrongWord": "Mots incorrects : {{ words }}.", "onboardingProtectTitle": "Protéger votre portefeuille", "onboardingProtectDescription": "L'ajout d'une sécurité biométrique garantira que vous êtes la seule personne à pouvoir accéder à votre portefeuille.", "onboardingProtectButtonHeadlineDevice": "Appareil", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Empreinte digitale", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Utiliser l'authentification {{ authType }}", "onboardingProtectError": "Un problème est survenu lors de l'authentification, veuil<PERSON><PERSON> réessayer", "onboardingProtectBiometryIosError": "L'authentification biométrique est configurée dans Phantom, mais désactivée dans les paramètres système. Veuillez ouvrir Paramètres > Phantom > Face ID ou Touch ID pour la réactiver.", "onboardingProtectRemoveAuth": "Désactiver l'authentification", "onboardingProtectRemoveAuthDescription": "Voulez-vous vraiment désactiver l'authentification ?", "onboardingWelcomeTitle": "Bienvenue sur Phantom", "onboardingWelcomeDescription": "Pour commencer, créez un nouveau portefeuille ou importez un portefeuille existant.", "onboardingWelcomeCreateWallet": "Créer un nouveau portefeuille", "onboardingWelcomeAlreadyHaveWallet": "J'ai déjà un portefeuille", "onboardingWelcomeConnectSeedVault": "Connexion au coffre à codes de sauvegarde", "onboardingSlide1Title": "Contrôlé par vous", "onboardingSlide1Description": "Votre portefeuille est sécurisé grâce à l'accès biométrique, à la détection des fraudes et à l'assistance 24 h / 24, 7 j / 7.", "onboardingSlide2Title": "La meilleure maison pour\nvos NFT", "onboardingSlide2Description": "<PERSON><PERSON><PERSON> les listages, brûlez les spams et restez à jour grâce à des notifications push utiles.", "onboardingSlide3Title": "Faites-en davantage avec vos jetons", "onboardingSlide3Description": "<PERSON><PERSON>, <PERSON><PERSON><PERSON>, mette<PERSON> en jeu, envoyez et recevez — sans jamais quitter votre portefeuille. ", "onboardingSlide4Title": "Découvrez le meilleur de Web3", "onboardingSlide4Description": "Trouvez et connectez-vous aux collections et applications de pointe grâce au navigateur intégré à l'application.", "onboardingMultichainSlide5Title": "Un portefeuille pour tout", "onboardingMultichainSlide5Description": "Faites l'expérience de l'intégralité de Solana, Ethereum et Polygon dans une interface conviviale.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Faites l'expérience de l'intégralité de Solana, Polygon et Bitcoin dans une interface conviviale.", "requireAuth": "Exiger l'authentification", "requireAuthImmediately": "Immédiatement", "availableToSend": "Disponible pour envoyer", "sendEnterAmount": "<PERSON><PERSON> un montant", "sendEditMemo": "Modifier le mémo", "sendShowLogs": "Afficher les journaux d'erreur", "sendHideLogs": "Masquer les journaux d'erreur", "sendGoBack": "Retour", "sendTransactionSuccess": "Vos jetons ont bien été envoyés à", "sendInputPlaceholder": "@nomdutilisateur ou adresse", "sendInputPlaceholderV2": "nom d'utilisateur ou adresse", "sendPeopleTitle": "<PERSON><PERSON>", "sendDomainTitle": "Domaines", "sendFollowing": "Abonnements", "sendRecentlyUsedAddressLabel": "Utilisée il y a {{formattedTimestamp}}", "sendRecipientAddress": "<PERSON><PERSON><PERSON> <PERSON> destinataire", "sendTokenInterpolated": "Envoyer {{tokenSymbol}}", "sendPasteFromClipboard": "Coller depuis le presse-papiers", "sendScanQR": "Scanner un code QR", "sendTo": "À :", "sendRecipientZeroBalanceWarning": "Cette adresse de portefeuille n'a pas de solde et n'apparaît pas dans l'historique de vos transactions récentes. Veuillez vous assurer que l'adresse est correcte.", "sendUnknownAddressWarning": "Il ne s'agit pas d'une adresse avec laquelle vous avez eu des contacts récemment. Veuillez continuer avec prudence.", "sendSameAddressWarning": "Voici votre adresse actuelle. L'envoi entraînera des frais de virement sans autre modification de solde.", "sendMintAccountWarning": "Voici l'adresse d'un compte mint. Vous ne pouvez pas envoyer de fonds à cette adresse, car cela entraînerait une perte définitive.", "sendCameraAccess": "Accès à la caméra", "sendCameraAccessSubtitle": "Pour scanner un code QR, l'accès à la caméra doit être activé. Souhaitez-vous ouvrir les réglages maintenant ?", "sendSettings": "Réglages", "sendOK": "OK", "invalidQRCode": "Ce code QR n'est pas valide.", "sendInvalidQRCode": "Ce code QR n'est pas une adresse valide", "sendInvalidQRCodeSubtitle": "Essayez à nouveau ou avec un autre code QR.", "sendInvalidQRCodeSplToken": "Jeton invalide dans le code QR", "sendInvalidQRCodeSplTokenSubtitle": "Ce code QR contient un jeton que vous ne possédez pas ou que nous ne pouvons pas identifier.", "sendScanAddressToSend": "<PERSON><PERSON>z l'adresse de {{tokenSymbol}} pour envoyer des fonds", "sendScanAddressToSendNoSymbol": "Scannez l'adresse pour envoyer des fonds", "sendScanAddressToSendCollectible": "Scanner une adresse SOL pou envoyer un objet à collectionner", "sendScanAddressToSendCollectibleMultichain": "<PERSON><PERSON>z une adresse pour envoyer un objet à collectionner", "sendSummary": "Résumé", "sendUndefinedToken": "<PERSON><PERSON><PERSON><PERSON>, impossible d'envoyer ce jeton", "sendNoTokens": "Aucun jeton disponible", "noBuyOptionsAvailableInCountry": "Aucune option d'achat disponible dans votre pays", "swapAvailableTokenDisclaimer": "Un nombre limité de jetons est disponible pour passer d'un réseau à l'autre", "swapCrossSwapNetworkTooltipTitle": "Échanges entre réseaux", "swapCrossSwapNetworkTooltipDescription": "Lorsque vous échangez entre réseaux, il est recommandé d'utiliser les jetons disponibles pour obtenir le prix le plus bas et les transactions les plus rapides.", "settingsAbout": "À propos de Phantom", "settingsShareAppWithFriends": "Invitez vos amis", "settingsConfirm": "O<PERSON>", "settingsMakeSureNoOneIsWatching": "Assurez-vous que personne ne regarde votre écran", "settingsManageAccounts": "<PERSON><PERSON><PERSON> les comptes", "settingsPrompt": "Voulez-vous vraiment continuer ?", "settingsSelectAvatar": "Sé<PERSON>ionner un avatar", "settingsSelectSecretPhrase": "<PERSON><PERSON><PERSON><PERSON>ner une Phrase secrète", "settingsShowPrivateKey": "Appuyez pour révéler votre clé privée", "settingsShowRecoveryPhrase": "Appuyez pour révéler votre phrase secrète", "settingsSubmitBetaFeedback": "Envoyer des commentaires sur la version bêta", "settingsUpdateAccountNameToast": "Nom du compte mis à jour", "settingsUpdateAvatarToast": "Avatar mis à jour", "settingsUpdateAvatarToastFailure": "Échec de la mise à jour de l'avatar !", "settingsWalletAddress": "<PERSON>resse du compte", "settingsWalletAddresses": "Adresses du compte", "settingsWalletNamePrimary": "Nom du compte", "settingsPlaceholderName": "Nom", "settingsWalletNameSecondary": "Modifiez le nom de votre portefeuille", "settingsYourAccounts": "Vos comptes", "settingsYourAccountsMultiChain": "Multi-cha<PERSON>ne", "settingsReportUser": "Signaler un utilisateur", "settingsNotifications": "Notifications", "settingsNotificationPreferences": "Préférences des notifications", "pushNotificationsPreferencesAllowNotifications": "Autoriser les notifications", "pushNotificationsPreferencesSentTokens": "Jetons envoyés", "pushNotificationsPreferencesSentTokensDescription": "Transferts sortants de jetons et NFT", "pushNotificationsPreferencesReceivedTokens": "Jetons reçus", "pushNotificationsPreferencesReceivedTokensDescription": "Transferts entrants de jetons et NFT", "pushNotificationsPreferencesDexSwap": "Échanges", "pushNotificationsPreferencesDexSwapDescription": "Échanges sur des applications reconnues", "pushNotificationsPreferencesOtherBalanceChanges": "Autres modifications du solde", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Autres transactions multi-jetons ayant un impact sur votre solde", "pushNotificationsPreferencesPhantomMarketing": "Mises à jour de Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Annonces de fonctionnalités et mises à jour générales", "pushNotificationsPreferencesDescription": "Ces paramètres contrôlent les notifications push pour ce portefeuille actif. Chaque portefeuille a ses propres paramètres de notification. Pour désactiver toutes les notifications push de Phantom, allez dans les <1>paramètres de votre appareil</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Impossible de synchroniser les préférences des notifications.", "connectSeedVaultConnectSeed": "Connecter un code de sauvegarde", "connectSeedVaultConnectSeedDescription": "Connectez Phantom au coffre à codes de sauvegarde sur votre téléphone", "connectSeedVaultSelectAnAccount": "Sélectionnez un compte", "connectSeedVaultSelectASeed": "Sélectionnez un code de sauvegarde", "connectSeedVaultSelectASeedDescription": "Choisissez le code de sauvegarde que vous souhaitez connecter à Phantom", "connectSeedVaultSelectAnAccountDescription": "Choisissez le compte que vous souhaitez configurer avec Phantom", "connectSeedVaultNoAccountsFound": "Aucun compte trouvé.", "connectSeedVaultSelectAccounts": "Sélectionner des comptes", "connectSeedVaultSelectAccountsDescription": "Choisissez les comptes que vous souhaitez configurer avec Phantom", "connectSeedVaultCompleteSetup": "Finalisez l'installation", "connectSeedVaultCompleteSetupDescription": "Vous êtes prêt ! Explorez web3 avec Phantom et utilisez votre coffre à codes de sauvegarde pour confirmer les transactions", "connectSeedVaultConnectAnotherSeed": "Connecter un autre code de sauvegarde", "connectSeedVaultConnectAllSeedsConnected": "Tous les codes de sauvegarde connectés", "connectSeedVaultNoSeedsConnected": "Aucun code de sauvegarde connecté. Appuyez sur le bouton ci-dessous pour identifier à partir du coffre à codes de sauvegarde.", "connectSeedVaultConnectAccount": "Connectez un compte", "connectSeedVaultLoadMore": "Charger plus", "connectSeedVaultNeedPermission": "Permission requise", "connectSeedVaultNeedPermissionDescription": "Allez dans Paramètres pour permettre à Phantom d'utiliser les permissions du Coffre à codes de sauvegarde.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} de frais", "stakeAmount": "<PERSON><PERSON>", "stakeAmountBalance": "Solde", "swapTopQuotes": "{{numQuotes}} meilleurs devis", "swapTopQuotesTitle": "Mei<PERSON><PERSON> devis", "swapProvidersTitle": "Fournisseurs", "swapProvidersFee": "{{fee}} de frais", "swapProvidersTagRecommended": "Mei<PERSON>ur rendement", "swapProvidersTagFastest": "Le plus rapide", "swapProviderEstimatedTimeHM": "{{hours}} h {{minutes}} m", "swapProviderEstimatedTimeM": "{{minutes}} m", "swapProviderEstimatedTimeS": "{{seconds}} s", "stakeReview": "Consulter", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "Commission", "stakeReviewConfirm": "Confirmer", "stakeReviewValidator": "Validateur", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Échec de la conversion d'enjeu", "convertStakeStatusErrorMessage": "Votre enjeu n'a pas pu être converti en {{poolTokenSymbol}}. Veuillez réessayer.", "convertStakeStatusLoadingTitle": "Conversion en {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Nous commençons le processus de conversion de vos {{stakedTokenSymbol}} en {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Conversion en {{poolTokenSymbol}} terminée !", "convertStakeStatusSuccessMessage": "Gagnez des récompenses supplémentaires avec vos JitoSOL <1>ici.</1>", "convertStakeStatusConvertMore": "Convertir plus", "convertStakePendingTitle": "Conversion d'enjeu en {{symbol}}", "convertToJitoSOL": "Convertir en JitoSOL", "convertToJitoSOLInfoDescription": "Convertissez vos SOL en JitoSOL pour gagner des récompenses et participer à l'écosystème Jito.", "convertToJitoSOLInfoTitle": "Convertir en JitoSOL", "convertStakeBannerTitle": "Convertissez votre enjeu en JitoSOL pour augmenter vos récompenses de jusqu'à 15 %", "convertStakeQuestBannerTitle": "Convertissez des SOL en enjeu en JitoSOL, et gagnez des récompenses. Plus d'informations.", "liquidStakeConvertInfoTitle": "Convertir en JitoSOL", "liquidStakeConvertInfoDescription": "Augmentez vos récompenses en convertissant votre enjeu SOL en JitoSOL. <1>Plus d'informations</1>", "liquidStakeConvertInfoFeature1Title": "Pourquoi placer des enjeux avec Ji<PERSON> ?", "liquidStakeConvertInfoFeature1Description": "Déposez pour obtenir des JitoSOL, ce qui augmente avec votre enjeu. Utilisez-le dans les protocoles DeFi pour augmenter vos gains. Échangez vos JitoSOL plus tard contre votre montant initial + les récompenses accumulées", "liquidStakeConvertInfoFeature2Title": "Récompenses moyennes plus élevées", "liquidStakeConvertInfoFeature2Description": "<PERSON>to répartit vos SOL entre les meilleurs validateurs avec les frais les plus bas. Les récompenses MEV augmentent encore plus vos revenus.", "liquidStakeConvertInfoFeature3Title": "Prise en charge du réseau Solana", "liquidStakeConvertInfoFeature3Description": "Le Liquid Staking sécurise les Solana en répartissant les enjeux sur plusieurs validateurs, ce qui réduit le risque lié aux validateurs à faible temps de fonctionnement.", "liquidStakeConvertInfoSecondaryButton": "Pas maintenant", "liquidStakeStartStaking": "Commencer l'enjeu", "liquidStakeReviewOrder": "Vérifier la commande", "convertStakeAccountListPageIneligibleSectionTitle": "Comptes d'enjeux non éligibles", "convertStakeAccountIneligibleBottomSheetTitle": "Comptes d'enjeux non éligibles", "convertStakeAccountListPageErrorTitle": "Échec de la récupération des comptes d'enjeu", "convertStakeAccountListPageErrorDescription": "Désolés, un problème est survenu et nous n'avons pas pu récupérer les comptes d'enjeu", "liquidStakeReviewYouPay": "Vous payez", "liquidStakeReviewYouReceive": "<PERSON><PERSON> rece<PERSON>", "liquidStakeReviewProvider": "Fournisseur", "liquidStakeReviewNetworkFee": "Frais du réseau", "liquidStakeReviewPageTitle": "Confirmation", "liquidStakeReviewConversionFootnote": "Lorsque vous posez un enjeu sur des jetons Solana en échange de JitoSOL, vous recevez une quantité légèrement inférieure de JitoSOL. <1>Plus d'informations</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Le pool d'enjeux de Jito prend en charge la plupart des validateurs Solana actifs. Vous ne pourrez pas convertir en JitoSOL les SOL mis en jeu de validateurs non pris en charge. De plus, les SOL nouvellement mis en jeu prennent environ 2 jours avant d'être éligibles pour la conversion en JitoSOL.", "selectAValidator": "Sélectionner un validateur", "validatorSelectionListTitle": "Sélectionner un validateur", "validatorSelectionListDescription": "Choisissez un validateur avec qui mettre vos SOL en jeu.", "stakeMethodDescription": "Gagnez des intérêts en utilisant vos jetons SOL pour aider Solana à se développer. <1>Plus d'informations</1>", "stakeMethodRecommended": "Recommandé", "stakeMethodEstApy": "Est. APY : ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Enjeu liquide", "stakeMethodSelectionLiquidStakingDescription": "Enjeu SOL pour obtenir des récompenses plus élevées, aidez à sécuriser Solana et recevez JitoSOL pour obtenir plus de récompenses.", "stakeMethodSelectionNativeStakingTitle": "<PERSON><PERSON><PERSON> nati<PERSON>", "stakeMethodSelectionNativeStakingDescription": "Enjeu SOL pour recevoir des récompenses tout en aidant à sécuriser Solana.", "liquidStakeMintStakeSOL": "Mettre des SOL en jeu", "mintJitoSOLInfoPageTitle": "Introduction de l'Enjeu liquide avec Jito", "mintJitoSOLFeature1Title": "Pourquoi placer des enjeux avec Ji<PERSON> ?", "mintJitoSOLFeature1Description": "Déposez pour obtenir des JitoSOL, ce qui augmente avec votre enjeu. Utilisez-le dans les protocoles DeFi pour augmenter vos gains. Échangez vos JitoSOL plus tard contre votre montant initial + les récompenses accumulées", "mintJitoSOLFeature2Title": "Récompenses moyennes plus élevées", "mintJitoSOLFeature2Description": "<PERSON>to répartit vos SOL entre les meilleurs validateurs avec les frais les plus bas. Les récompenses MEV augmentent encore plus vos revenus.", "mintJitoSOLFeature3Title": "Prise en charge du réseau Solana", "mintJitoSOLFeature3Description": "L'Enjeu liquide sécurise les Solana en répartissant les enjeux sur plusieurs validateurs, ce qui réduit le risque lié aux validateurs à faible temps de fonctionnement.", "mintLiquidStakePendingTitle": "Minting de l'enjeu liquide", "mintStakeStatusErrorTitle": "Échec du minting de l'enjeu liquide", "mintStakeStatusErrorMessage": "Votre enjeu liquide {{poolTokenSymbol}} n'a pas pu être mint. Veuillez réessayer.", "mintStakeStatusSuccessTitle": "Minting de l'enjeu liquide {{poolTokenSymbol}} terminé !", "mintStakeStatusLoadingTitle": "Minting de l'enjeu liquide {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "Nous commençons le processus de mint de votre enjeu liquide {{poolTokenSymbol}}.", "mintLiquidStakeAmountDescription": "Choisissez combien de SOL vous souhaitez mettre en jeu avec Jito", "mintLiquidStakeAmountProvider": "Fournisseur", "mintLiquidStakeAmountApy": "APY est.", "mintLiquidStakeAmountBestPrice": "Prix", "mintLiquidStakeAmountInsufficientBalance": "Solde insuffisant", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} requis pour mettre en jeu", "swapTooltipGotIt": "J'ai compris", "swapTabInsufficientFunds": "Fonds insuffisants", "swapNoAssetsFound": "Pas d'actifs", "swapNoTokensFound": "<PERSON><PERSON>n jeton trouvé", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "swapConfirmationGoBack": "Retour", "swapNoQuotesFound": "<PERSON><PERSON>n devis trouvé", "swapNotProviderFound": "Nous n'avons pas pu trouver de fournisseur pour cet échange de jetons. Essayez un autre jeton.", "swapAvailableOnMainnet": "Cette fonctionnalité est uniquement disponible sur Mainnet", "swapNotAvailableEVM": "Les échanges ne sont pas encore disponibles pour les comptes EVM", "swapNotAvailableOnSelectedNetwork": "Les échanges ne sont pas disponibles sur le réseau sélectionné", "singleChainSwapTab": "<PERSON><PERSON>", "crossChainSwapTab": "Sur les Réseaux", "allFilter": "Tous", "bridgeRefuelTitle": "<PERSON>e le plein", "bridgeRefuelDescription": "Faire le plein assure que vous puissiez payer les transactions après un pont.", "bridgeRefuelLabelBalance": "Vos {{symbol}}", "bridgeRefuelLabelReceive": "<PERSON><PERSON> rece<PERSON>", "bridgeRefuelLabelFee": "Coût estimé", "bridgeRefuelDismiss": "Continuer sans faire le plein", "bridgeRefuelEnable": "<PERSON><PERSON> <PERSON>lein", "unwrapWrappedSolError": "Échec du déballage", "unwrapWrappedSolLoading": "Déballage...", "unwrapWrappedSolSuccess": "Déballé", "unwrapWrappedSolViewTransaction": "Voir la transaction", "dappApprovePopupSignMessage": "Signer le message", "solanaPayFrom": "De", "solanaPayMessage": "Message", "solanaPayNetworkFee": "Frais du réseau", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Payer {{item}}", "solanaPayPayNow": "Payer maintenant", "solanaPaySending": "Envoi de {{item}}", "solanaPayReceiving": "Réception de {{item}}", "solanaPayMinting": "Minting de {{item}}", "solanaPayTransactionProcessing": "Votre transaction est en cours de traitement,\nveuillez patienter.", "solanaPaySent": "Envoyé !", "solanaPayReceived": "<PERSON><PERSON>u !", "solanaPayMinted": "Mint effectué !", "solanaPaySentNFT": "NFT envoyé !", "solanaPayReceivedNFT": "NFT reçu !", "solanaPayTokensSent": "Vos jetons ont bien été envoyés à {{to}}", "solanaPayTokensReceived": "Vous avez reçu de nouveaux jetons de {{from}}", "solanaPayViewTransaction": "Voir la transaction", "solanaPayTransactionFailed": "Échec de la transaction", "solanaPayConfirm": "Confirmer", "solanaPayTo": "à", "dappApproveConnectViewAccount": "Afficher votre compte Solana", "deepLinkInvalidLink": "Lien non valide", "deepLinkInvalidSplTokenSubtitle": "<PERSON><PERSON> contient un jeton que vous ne possédez pas ou que nous ne pouvons pas identifier.", "walletAvatarShowAllAccounts": "Afficher tous les comptes", "pushNotificationsGetInstantUpdates": "<PERSON><PERSON>vez des mises à jour instantanées", "pushNotificationsEnablePushNotifications": "Activez les notifications push sur les transferts, les échanges et les annonces terminés", "pushNotificationsEnable": "Activer", "pushNotificationsNotNow": "Pas maintenant", "onboardingAgreeToTermsOfServiceInterpolated": "J'accepte les <1>Conditions d'utilisation</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, je l'ai enregistré quelque part", "onboardingCreateNewWallet": "Créer un nouveau portefeuille", "onboardingErrorDuplicateSecretRecoveryPhrase": "Cette phrase secrète existe déjà dans votre portefeuille", "onboardingErrorInvalidSecretRecoveryPhrase": "Phrase de récupération secrète invalide", "onboardingFinished": "Tout est prêt !", "onboardingImportAccounts": "Importer des comptes", "onboardingImportImportingAccounts": "Importation des comptes…", "onboardingImportImportingFindingAccounts": "Recherche de comptes avec de l'activité", "onboardingImportAccountsLastActive": "Active il y a {{formattedTimestamp}}", "onboardingImportAccountsNeverUsed": "<PERSON><PERSON> utilis<PERSON>", "onboardingImportAccountsCreateNew": "Nouveau portefeuille", "onboardingImportAccountsDescription": "Choisir les comptes de portefeuilles à importer", "onboardingImportReadOnlyAccountDescription": "A<PERSON><PERSON>z une adresse ou un nom de domaine que vous voulez surveiller. Vous aurez accès en lecture seule et ne pourrez pas signer de transactions ou de messages.", "onboardingImportSecretRecoveryPhrase": "Importer une phrase secrète", "onboardingImportViewAccounts": "Affiche<PERSON> les comptes", "onboardingRestoreExistingWallet": "Restaurer un portefeuille existant avec votre phrase secrète de récupération de 12 ou 24 mots", "onboardingShowUnusedAccounts": "Afficher les comptes inutilisés", "onboardingShowMoreAccounts": "Afficher plus de comptes", "onboardingHideUnusedAccounts": "Masquer les comptes inutilisés", "onboardingSecretRecoveryPhrase": "Phrase de récupération secrète", "onboardingSelectAccounts": "Sélectionnez vos comptes", "onboardingStoreSecretRecoveryPhraseReminder": "C'est le seul moyen de récupérer votre compte. Veuillez la conserver dans un endroit sûr !", "useTokenMetasForMintsUnknownName": "Inconnu", "timeUnitMinute": "minute", "timeUnitMinutes": "minutes", "timeUnitHour": "heure", "timeUnitHours": "heures", "espNFTListWithPrice": "Vous avez listé {{NFTDisplayName}} pour {{priceAmount}} {{priceTokenSymbol}} sur {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Vous avez listé {{NFTDisplayName}} pour {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Vous avez mis en vente {{NFTDisplayName}} sur {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Vous avez mis en vente {{NFTDisplayName}}", "espNFTChangeListPriceWithPrice": "Vous avez mis à jour le listing de {{NFTDisplayName}}. Nouvelles valeurs : {{priceAmount}} {{priceTokenSymbol}} sur {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Vous avez mis à jour le listing de {{NFTDisplayName}}. Nouvelles valeurs : {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Vous avez mis à jour le listing de {{NFTDisplayName}} sur {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Vous avez mis à jour le listing de {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Vous avez fait une enchère de {{priceAmount}} {{priceTokenSymbol}} pour {{NFTDisplayName}} sur {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Vous avez fait une enchère de {{priceAmount}} {{priceTokenSymbol}} pour {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Vous avez fait une enchère de {{NFTDisplayName}} sur {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Vous avez fait une enchère de {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Nouvelle enchère de {{priceAmount}} {{priceTokenSymbol}} pour {{NFTDisplayName}} sur {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Nouvelle enchère de {{priceAmount}} {{priceTokenSymbol}} pour {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Nouvelle enchère pour {{NFTDisplayName}} sur {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Nouvelle enchère pour {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Vous avez annulé votre enchère de {{priceAmount}} {{priceTokenSymbol}} pour {{NFTDisplayName}} sur {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Vous avez annulé votre enchère de {{priceAmount}} {{priceTokenSymbol}} pour {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Vous avez annulé votre enchère pour {{NFTDisplayName}} sur {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Vous avez annulé votre enchère pour {{NFTDisplayName}}", "espNFTUnlist": "Vous avez délisté {{NFTDisplayName}} sur {{dAppName}}", "espNFTUnlistWithoutDApp": "V<PERSON> avez dé<PERSON>é {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Vous avez acheté {{NFTDisplayName}} pour {{priceAmount}} {{priceTokenSymbol}} sur {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Vous avez acheté {{NFTDisplayName}} pour {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "V<PERSON> avez <PERSON> {{NFTDisplayName}} sur {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "V<PERSON> a<PERSON> {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Vous avez vendu {{NFTDisplayName}} pour {{priceAmount}} {{priceTokenSymbol}} sur {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Vous avez vendu {{NFTDisplayName}} pour {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Vous avez vendu {{NFTDisplayName}} sur {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Vous avez vendu {{NFTDisplayName}}", "espDEXSwap": "Vous avez échangé {{downTokensTextFragment}} contre {{upTokensTextFragment}} sur {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Vous avez déposé {{downTokensTextFragment}} dans le pool de liquidité {{poolDisplayName}} sur {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Vous avez échangé {{downTokensTextFragment}} contre {{upTokensTextFragment}} sur {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Vous avez retiré {{upTokensTextFragment}} du pool de liquidité {{poolDisplayName}} sur {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Vous avez échangé {{downTokensTextFragment}} contre {{upTokensTextFragment}} sur {{dAppName}}", "espGenericTokenSend": "Vous avez envoyé {{downTokensTextFragment}}", "espGenericTokenReceive": "Vous avez reçu {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Vous avez échangé {{downTokensTextFragment}} contre {{upTokensTextFragment}}", "espUnknown": "INCONNU", "espUnknownNFT": "NFT inconnu", "espTextFragmentAnd": "et", "externalLinkWarningTitle": "Vous êtes sur le point de quitter Phantom", "externalLinkWarningDescription": "Et d'ouvrir {{url}}. Assurez-vous de faire confiance à cette source avant d'interagir avec elle.", "shortcutsWarningDescription": "<PERSON><PERSON><PERSON><PERSON> fournis par {{url}}. Assurez-vous de faire confiance à cette source avant d'interagir avec elle.", "lowTpsBanner": "Solana connaît une congestion du réseau", "lowTpsMessageTitle": "Congestion du réseau Solana", "lowTpsMessage": "En raison de la congestion de Solana, vos transactions peuvent échouer ou être retardées. Veuillez réessayer les transactions qui ont échoué.", "solanaSlow": "Le réseau Solana est inhabituellement lent", "solanaNetworkTemporarilyDown": "Le réseau Solana est temporairement hors service", "waitForNetworkRestart": "Veuillez attendre que le réseau redémarre. Vos fonds ne sont pas affectés.", "exploreCollectionsCarouselTitle": "Ce qui est populaire", "exploreDropsCarouselTitle": "<PERSON><PERSON><PERSON> de <PERSON>uf", "exploreSortFloor": "<PERSON><PERSON>", "exploreSortListed": "Listé", "exploreSortVolume": "Volume", "exploreFetchErrorSubtitle": "Veuillez réessayer plus tard.", "exploreFetchErrorTitle": "Échec de la récupération.", "exploreTopCollectionsTitle": "Meilleures collections NFT", "exploreTopListLess": "<PERSON>ins", "exploreTopListMore": "Plus", "exploreSeeMore": "Voir la suite", "exploreTrendingTokens": "Jetons tendance", "exploreVolumeTokens": "Volume le plus élevé", "explorePriceChangeTokens": "Gains les plus élevés", "explorePriceTokens": "Jetons par prix", "exploreMarketCapTokens": "Meilleurs jetons", "exploreTrendingSites": "Sites tendance", "exploreTopSites": "Meilleurs sites", "exploreTrendingCollections": "Collections tendance", "exploreTopCollections": "Meilleures collections", "collectiblesSearchCollectionsSection": "Collections", "collectiblesSearchItemsSection": "Articles", "collectiblesSearchNrOfItems": "{{ nrOfItems }} articles", "collectiblesSearchPlaceholderText": "Recherchez vos objets à collectionner", "collectionPinSuccess": "Collection épinglée", "collectionPinFail": "Échec de l'épinglage de la collection", "collectionUnpinSuccess": "Collection détachée", "collectionUnpinFail": "Échec du détachage de la collection", "collectionHideSuccess": "Collection masquée", "collectionHideFail": "La collection n'a pas pu être masquée", "collectionUnhideSuccess": "Collection montrée", "collectionUnhideFail": "La collection n'a pas pu être montrée", "collectiblesSpamSuccess": "Signalé comme spam", "collectiblesSpamFail": "Le signalement comme spam a échoué", "collectiblesSpamAndHiddenSuccess": "Signalé comme spam et masqué", "collectiblesNotSpamSuccess": "Signalé comme non spam", "collectiblesNotSpamFail": "Le signalement comme non spam a échoué", "collectiblesNotSpamAndUnhiddenSuccess": "Signalé comme non spam et affiché", "tokenPageSpamWarning": "Ce jeton n'a pas été vérifié. N'interagissez qu'avec des jetons en lesquels vous avez confiance.", "tokenSpamWarning": "Ce jeton a <PERSON><PERSON> masqué, car <PERSON> pense qu'il s'agit d'un spam.", "collectibleSpamWarning": "Cet objet de collection a été masqué, car <PERSON> pense qu'il s'agit d'un spam.", "collectionSpamWarning": "Ces objets de collection ont été masqués parce que Phantom pense qu'il s'agit de spam.", "emojiNoResults": "<PERSON><PERSON>n <PERSON> trouvé", "emojiSearchResults": "Résultats de recherche", "emojiSuggested": "<PERSON><PERSON><PERSON><PERSON>", "emojiSmileys": "Smileys et personnes", "emojiAnimals": "Animaux et nature", "emojiFood": "Nourriture et boissons", "emojiTravel": "Voyage et lieux", "emojiActivities": "Activités", "emojiObjects": "Objets", "emojiSymbols": "Symboles", "emojiFlags": "Drapeaux", "whichExtensionToConnectWith": "Avec quelle extension voulez-vous vous connecter ?", "configureInSettings": "Peut être configuré dans Paramètres → Portefeuille d'application par défaut.", "continueWith": "Continuer avec", "useMetaMask": "Utiliser Meta<PERSON>", "usePhantom": "Utiliser Phantom", "alwaysAsk": "Toujours demander", "dontAskMeAgain": "Ne plus me demander", "selectWalletSettingDescriptionLine1": "Certaines applications peuvent ne pas proposer l'option de se connecter avec Phantom.", "selectWalletSettingDescriptionLinePhantom": "Pour contourner cela, la connexion avec MetaMask ouvrira toujours Phantom à la place.", "selectWalletSettingDescriptionLineAlwaysAsk": "Pour contourner cela, quand vous vous connectez avec MetaMask, nous vous demanderons si vous voulez plutôt utiliser Phantom.", "selectWalletSettingDescriptionLineMetaMask": "Configurer MetaMask par défaut empêchera ces dApps de se connecter à Phantom.", "metaMaskOverride": "Portefeuille d'application par défaut", "metaMaskOverrideSettingDescriptionLine1": "Pour les connexions aux sites web qui ne proposent pas d'utiliser Phantom.", "refreshAndReconnectToast": "Actualisez et reconnectez-vous pour appliquer vos modifications", "autoConfirmUnavailable": "Indisponible", "autoConfirmReasonDappNotWhitelisted": "Indisponible parce que le contrat duquel elle provient n'est pas sur notre liste d'autorisation pour cette application.", "autoConfirmReasonSessionNotActive": "Indisponible parce qu'aucune session de confirmation automatique n'est active. Veuillez l'activer ci-dessous.", "autoConfirmReasonRateLimited": "Indisponible parce que le dapp que vous utilisez envoie trop de requêtes.", "autoConfirmReasonUnsupportedNetwork": "Indisponible parce que la confirmation automatique ne prend pas encore en charge ce réseau.", "autoConfirmReasonSimulationFailed": "Indisponible parce que nous ne pouvons pas garantir la sécurité.", "autoConfirmReasonTabNotFocused": "Indisponible parce que l'onglet du domaine pour lequel vous tentez une confirmation automatique n'est pas actif.", "autoConfirmReasonNotUnlocked": "Indisponible parce que le portefeuille n'était pas déverrouillé.", "rpcErrorUnauthorizedWrongAccount": "L'adresse de la transaction ne correspond pas à l'adresse du compte sélectionné.", "rpcErrorUnauthorizedUnknownSource": "La source de la demande RPC n'a pas pu être déterminée.", "transactionsDisabledTitle": "Transactions désactivées", "transactionsDisabledMessage": "Votre adresse n'est pas en mesure d'effectuer des transactions via Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Actif", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL copiée dans le presse-papiers", "notEnoughSolScanTransactionWarning": "Cette transaction peut échouer en raison d'un nombre insuffisant de SOL sur votre compte. Veuillez ajouter plus de SOL à votre compte et réessayer.", "transactionRevertedWarning": "Cette transaction a été annulée pendant la simulation. Des fonds peuvent être perdus si elle est envoyée.", "slippageToleranceExceeded": "Cette transaction a été annulée pendant la simulation. Tolérance de glissement dépassée.", "simulationWarningKnownMalicious": "Nous pensons que ce compte est malveillant. L'approuver peut entraîner une perte de fonds.", "simulationWarningPoisonedAddress": "Cette adresse ressemble étrangement à une adresse à laquelle vous avez récemment envoyé des fonds. <PERSON><PERSON><PERSON><PERSON> confirmer qu'il s'agit bien de l'adresse correcte afin d'éviter de perdre des fonds à cause d'une escroquerie.", "simulationWarningInteractingWithAccountWithoutActivity": "Ce compte ne présente aucune activité antérieure. L'envoi de fonds sur un compte inexistant peut entraîner une perte de fonds", "quests": "<PERSON><PERSON><PERSON><PERSON>", "questsClaimInProgress": "Récupération en cours", "questsVerifyingCompletion": "Vérification de l'avancée de la quête", "questsClaimError": "Erreur lors de la récupération de la récompense", "questsClaimErrorDescription": "Une erreur est survenue lors de la récupération de votre récompense. Veuillez réessayer plus tard.", "questsBadgeMobileOnly": "Mobile uniquement", "questsBadgeExtensionOnly": "Extension uniquement", "questsExplainerSheetButtonLabel": "<PERSON><PERSON><PERSON>", "questsNoQuestsAvailable": "<PERSON><PERSON>ne quête disponible", "questsNoQuestsAvailableDescription": "Aucune quête n'est actuellement disponible. Nous vous informerons dès que de nouvelles quêtes seront ajoutées.", "exploreLearn": "Apprendre", "exploreSites": "Sites", "exploreTokens": "Jet<PERSON>", "exploreQuests": "<PERSON><PERSON><PERSON><PERSON>", "exploreCollections": "Collections", "exploreFilterByall_networks": "Tous les réseaux", "exploreSortByrank": "Tendance", "exploreSortBytrending": "Tendance", "exploreSortByprice": "Prix", "exploreSortByprice-change": "Modification du prix", "exploreSortBytop": "Mei<PERSON><PERSON>", "exploreSortByvolume": "Volume", "exploreSortBygainers": "Gagnants", "exploreSortBylosers": "Perdants", "exploreSortBymarket-cap": "Plafond du march<PERSON>", "exploreSortBymarket_cap": "Plafond du march<PERSON>", "exploreTimeFrame1h": "1 h", "exploreTimeFrame24h": "24 h", "exploreTimeFrame7d": "7 j", "exploreTimeFrame30d": "30 j", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Objets à collectionner", "exploreCategoryMarketplace": "<PERSON><PERSON>", "exploreCategoryGaming": "<PERSON><PERSON> vid<PERSON>o", "exploreCategoryBridges": "Ponts", "exploreCategoryOther": "<PERSON><PERSON>", "exploreCategorySocial": "Social", "exploreCategoryCommunity": "Communauté", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "<PERSON><PERSON><PERSON>", "exploreCategoryArt": "Art", "exploreCategoryTools": "Outils", "exploreCategoryDeveloperTools": "Outils des développeurs", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Enjeu NFT", "exploreCategoryExplorer": "Explorateur", "exploreCategoryInscriptions": "Inscriptions", "exploreCategoryBridge": "<PERSON>", "exploreCategoryAirdrop": "Largage", "exploreCategoryAirdropChecker": "Vérificateur de largage", "exploreCategoryPoints": "Points", "exploreCategoryQuests": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryShop": "Boutique", "exploreCategoryProtocol": "Protocole", "exploreCategoryNamingService": "Service de nommage", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Traqueur de portefeuille", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volume", "exploreFloor": "<PERSON><PERSON>", "exploreCap": "Plafond du march<PERSON>", "exploreToken": "<PERSON><PERSON>", "explorePrice": "Prix", "explore24hVolume": "Volume 24 h", "exploreErrorButtonText": "<PERSON><PERSON><PERSON><PERSON>", "exploreErrorDescription": "Une erreur est survenue lors de la tentative de chargement du contenu d'exploration. Veuillez actualiser et réessayer", "exploreErrorTitle": "Échec du chargement du contenu d'exploration", "exploreNetworkError": "Une erreur de réseau est survenue. Veuillez réessayer plus tard.", "exploreTokensLegalDisclaimer": "Les listes de jetons sont générées à l'aide de données du marché fournies par divers fournisseurs tiers, notamment CoinGecko, Birdeye et Jupiter. Les performances sont basées sur la période de 24 heures qui précède. Les performances passées ne sont pas indicatives des performances futures.", "swapperTokensLegalDisclaimer": "Les listes de jetons en tendance sont générées à l'aide de données de marché provenant de divers fournisseurs tiers, notamment CoinGecko, Birdeye et Jupiter, et basées sur les jetons populaires échangés par les utilisateurs de Phantom via le Swapper au cours de la période indiquée. Les performances passées ne sont pas indicatives des performances futures.", "exploreLearnErrorTitle": "Échec du chargement du contenu appris", "exploreLearnErrorDescription": "Une erreur est survenue lors de la tentative de chargement du contenu appris. Veuillez actualiser et réessayer", "exploreShowMore": "Afficher plus", "exploreShowLess": "Affiche<PERSON> moins", "exploreVisitSite": "Visiter le site", "dappBrowserSearchScreenVisitSite": "Visiter le site", "dappBrowserSearchScreenSearchWithGoogle": "Rechercher sur Google", "dappBrowserSearchScreenSearchLinkYouCopied": "Lien que vous avez copié", "dappBrowserExtSearchPlaceholder": "Rechercher des sites, jetons", "dappBrowserSearchNoAppsTokens": "Ni applications ni jetons trouvés", "dappBrowserTabsLimitExceededScreenTitle": "Fermer les onglets plus anciens ?", "dappBrowserTabsLimitExceededScreenDescription": "Vous avez {{tabsCount}} onglets ouverts. Pour en ouvrir plus, vous devez en fermer certains.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "Fermer tous les onglets", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN : Ce domaine n'existe pas", "dappBrowserTabErrorHttp": "Bloqué. Veuillez utiliser le HTTPS", "dappBrowserTabError401Unauthorized": "401 Non autorisé", "dappBrowserTabError501UnhandledRequest": "501 Requête non traitée", "dappBrowserTabErrorTimeout": "DÉLAI D'ATTENTE EXPIRÉ : Le serveur a mis trop de temps à répondre", "dappBrowserTabErrorInvalidResponse": "Réponse non valide", "dappBrowserTabErrorEmptyResponse": "Réponse vide", "dappBrowserTabErrorGeneric": "Une erreur est survenue", "localizedErrorUnknownError": "Un problème est survenu, veuillez réessayer plus tard.", "localizedErrorUnsupportedCountry": "Nous sommes désolés, votre pays n'est pas encore pris en charge.", "localizedErrorTokensNotLoading": "Un problème est survenu lors du chargement de vos jetons. Veuillez réessayer.", "localizedErrorSwapperNoQuotes": "Aucun échange disponible en raison d'une paire non prise en charge, d'une faible liquidité ou d'un faible montant. Essayez d'ajuster le jeton ou le montant.", "localizedErrorSwapperRefuelNoQuotes": "Aucun devis n'a été trouvé. Essayez un autre jeton, un autre montant ou désactivez faire le plein.", "localizedErrorInsufficientSellAmount": "Quantité de jetons trop faible. Augmentez la valeur pour échanger en chaînes croisées.", "localizedErrorCrossChainUnavailable": "Les échanges inter-chaînes sont actuellement indisponibles. Veuillez réessayer plus tard.", "localizedErrorTokenNotTradable": "L'un des jetons sélectionnés n'est pas négociable. Veuillez sélectionner un autre jeton.", "localizedErrorCollectibleLocked": "Le compte du jeton est verrouillé.", "localizedErrorCollectibleListed": "Le compte du jeton est listé.", "spamActivityAction": "Voir les éléments masqués", "spamActivityTitle": "Activité masquée", "spamActivityWarning": "Cette transaction était masquée parce que Phantom a cru que ça pouvait être un spam.", "appAuthenticationFailed": "Échec de l'authentification", "appAuthenticationFailedDescription": "Un problème est survenu lors de votre tentative d'authentification. Veuillez réessayer.", "partialErrorBalanceChainName": "Nous avons des difficultés à mettre à jour vos soldes {{chainName}}. Vos fonds sont en sécurité.", "partialErrorGeneric": "Nous avons des difficultés à mettre à jour les réseaux, certains de vos soldes en jetons et de vos prix peuvent être périmés. Vos fonds sont en sécurité.", "partialErrorTokenDetail": "Nous avons des difficultés à mettre à jour votre solde en jetons. Vos fonds sont en sécurité.", "partialErrorTokenPrices": "Nous avons des difficultés à mettre à jour les prix de vos jetons. Vos fonds sont en sécurité.", "partialErrorTokensTrimmed": "Nous rencontrons des problèmes pour afficher tous les jetons de votre portefeuille. Vos fonds sont en sécurité.", "publicFungibleDetailAbout": "À propos", "publicFungibleDetailYourBalance": "Votre solde", "publicFungibleDetailInfo": "Informations", "publicFungibleDetailShowMore": "Afficher plus", "publicFungibleDetailShowLess": "Affiche<PERSON> moins", "publicFungibleDetailPerformance": "Performance sur 24 h", "publicFungibleDetailSecurity": "Sécurité", "publicFungibleDetailMarketCap": "Plafond du march<PERSON>", "publicFungibleDetailTotalSupply": "Approvisionnement total", "publicFungibleDetailCirculatingSupply": "Approvisionnement circulaire", "publicFungibleDetailMaxSupply": "Approvisionnement max", "publicFungibleDetailHolders": "Détenteurs", "publicFungibleDetailVolume": "Volume", "publicFungibleDetailTrades": "Échanges", "publicFungibleDetailTraders": "Négociants", "publicFungibleDetailUniqueWallets": "Portefeuilles uniques", "publicFungibleDetailTop10Holders": "10 meilleurs détenteurs", "publicFungibleDetailTop10HoldersTooltip": "Indique le pourcentage de l'approvisionnement total actuel détenu par les 10 premiers détenteurs du jeton. Il s'agit d'une mesure de la facilité avec laquelle le prix peut être manipulé.", "publicFungibleDetailMintable": "Mintable", "publicFungibleDetailMintableTooltip": "L'approvisionnement en jetons peut être augmenté par le propriétaire du contrat si un jeton est mintable.", "publicFungibleDetailMutableInfo": "Informations sujettes à modification", "publicFungibleDetailMutableInfoTooltip": "Si les informations relatives au jeton, telles que le nom, le logo et l'adresse du site web, sont mutables, elles peuvent être modifiées par le titudu contrat.", "publicFungibleDetailOwnershipRenounced": "Renonciation à la propriété", "publicFungibleDetailOwnershipRenouncedTooltip": "Si la propriété des jetons est abandonnée, personne ne peut exécuter des fonctions telles que le mint de nouveaux jetons.", "publicFungibleDetailUpdateAuthority": "Autorité de mise à jour", "publicFungibleDetailUpdateAuthorityTooltip": "L'autorité de mise à jour est l'adresse du portefeuille qui peut modifier les informations si un jeton est mutable.", "publicFungibleDetailFreezeAuthority": "Autorité de <PERSON>", "publicFungibleDetailFreezeAuthorityTooltip": "L'autorité de gel est l'adresse du portefeuille qui peut empêcher le transfert de fonds.", "publicFungibleUnverifiedToken": "Ce jeton n'a pas été vérifié. N'interagissez qu'avec des jetons en lesquels vous avez confiance.", "publicFungibleDetailSwap": "<PERSON><PERSON><PERSON> des {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Échangez des {{tokenSymbol}} avec l'application Phantom", "publicFungibleDetailLinkCopied": "Copié dans le presse-papier", "publicFungibleDetailContract": "Contrat", "publicFungibleDetailMint": "Mint", "unifiedTokenDetailTransactionActivity": "Activité", "unifiedTokenDetailSeeMoreTransactionActivity": "Voir la suite", "unifiedTokenDetailTransactionActivityError": "Échec du chargement de l'activité récente", "additionalNetworksTitle": "Réseaux supplémentaires", "copyAddressRowAdditionalNetworks": "Réseaux supplémentaires", "copyAddressRowAdditionalNetworksHeader": "Les réseaux suivants utilisent la même adresse qu'Ethereum :", "copyAddressRowAdditionalNetworksDescription": "Vous pouvez utiliser votre adresse Ethereum en toute sécurité pour envoyer et recevoir des actifs sur n'importe lequel de ces réseaux.", "cpeUnknownError": "<PERSON><PERSON><PERSON> inconnue", "cpeUnknownInstructionError": "Erreur d'instruction inconnue", "cpeAccountFrozen": "Le compte est gelé", "cpeAssetFrozen": "L'actif est gelé", "cpeInsufficientFunds": "Fonds insuffisants", "cpeInvalidAuthority": "Autorité non valide", "cpeBalanceBelowRent": "Solde inférieur au seuil d'exonération des loyers", "cpeNotApprovedForConfidentialTransfers": "Compte non approuvé pour les virements confidentiels", "cpeNotAcceptingDepositsOrTransfers": "Le compte n'accepte pas les dépôts ou les virements", "cpeNoMemoButRequired": "Pas de note dans l'instruction précédente ; nécessaire pour que le destinataire reçoive un virement", "cpeTransferDisabledForMint": "Le virement est désactivé pour ce mint", "cpeDepositAmountExceedsLimit": "Le montant du dépôt dépasse la limite maximale", "cpeInsufficientFundsForRent": "Fonds insuffisants pour le loyer", "reportIssueScreenTitle": "Signaler un problème", "publicFungibleReportIssuePrompt": "Quel problème au niveau de {{tokenName}} voulez-vous signaler ?", "publicFungibleReportIssueIncorrectInformation": "Informations incorrectes", "publicFungibleReportIssuePriceStale": "Le prix n'a pas été mis à jour", "publicFungibleReportIssuePriceMissing": "Le prix manque", "publicFungibleReportIssuePerformanceIncorrect": "La performance sur 24 h n'est pas correcte", "publicFungibleReportIssueLinkBroken": "Les liens sociaux ne fonctionnent pas", "publicFungibleDetailErrorLoading": "Donn<PERSON> du jeton indisponibles", "reportUserPrompt": "Quel problème au niveau de @{{username}} voulez-vous signaler ?", "reportUserOptionAbuseAndHarrassmentTitle": "Abus et harcèlement", "reportUserOptionAbuseAndHarrassmentDescription": "Harc<PERSON><PERSON> ciblé, incitation au harcèlement, menaces violentes, contenus et références haineux", "reportUserOptionPrivacyAndImpersonationTitle": "Vie privée et usurpation d'identité", "reportUserOptionPrivacyAndImpersonationDescription": "Partage ou menaces de divulguer des informations privées, prétendre être quelqu'un d'autre", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "Faux compte, escroqueries, liens malveillants", "reportUserSuccess": "Signalement de l'utilisateur envoyé.", "settingsClaimUsernameTitle": "<PERSON><PERSON><PERSON> un nom d'utilisateur", "settingsClaimUsernameDescription": "Une identité unique est aussi unique que votre portefeuille", "settingsClaimUsernameValueProp1": "Identité simplifiée", "settingsClaimUsernameValueProp1Description": "Dites adieu aux adresses longues et complexes, et bonjour à une identité conviviale", "settingsClaimUsernameValueProp2": "Plus rapide et plus facile", "settingsClaimUsernameValueProp2Description": "Envoyez et recevez facilement des cryptomonnaies, connectez-vous à votre portefeuille et connectez-vous avec vos amis", "settingsClaimUsernameValueProp3": "<PERSON><PERSON> synchroni<PERSON>", "settingsClaimUsernameValueProp3Description": "Connectez n'importe quel compte à votre nom d'utilisateur et il sera synchronisé sur tous vos appareils", "settingsClaimUsernameHelperText": "Votre nom unique pour votre compte Phantom", "settingsClaimUsernameValidationDefault": "Ce nom d'utilisateur ne peut être modifié ultérieurement", "settingsClaimUsernameValidationAvailable": "Nom d'utilisateur disponible", "settingsClaimUsernameValidationUnavailable": "Nom d'utilisateur indisponible", "settingsClaimUsernameValidationServerError": "Impossible de vérifier si le nom d'utilisateur est disponible. Veuillez réessayer plus tard", "settingsClaimUsernameValidationErrorLine1": "Nom d'utilisateur non valide.", "settingsClaimUsernameValidationErrorLine2": "Les noms d'utilisateur doivent comporter entre {{minChar}} et {{maxChar}} caractères et ne peuvent contenir que des lettres et des chiffres.", "settingsClaimUsernameValidationLoading": "Vérification de la disponibilité de ce nom d'utilisateur...", "settingsClaimUsernameSaveAndContinue": "Enregistrer et continuer", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON> un avatar", "settingsClaimUsernameAnonymousAuthTitle": "Auth anonyme", "settingsClaimUsernameAnonymousAuthDescription": "Connectez-vous anonymement à votre compte Phantom grâce à une signature", "settingsClaimUsernameAnonymousAuthBadge": "Découvrez le fonctionnement", "settingsClaimUsernameLinkWalletsTitle": "Associez vos portefeuilles", "settingsClaimUsernameLinkWalletsDescription": "Choisissez les portefeuilles qui s'affichent sur d'autres appareils avec votre nom d'utilisateur", "settingsClaimUsernameLinkWalletsBadge": "Pas consultable par le public", "settingsClaimUsernameConnectAccountsTitle": "Connecter des comptes", "settingsClaimUsernameConnectAccountsHelperText": "<PERSON>que adresse de chaîne sera liée à votre nom d'utilisateur. Vous pourrez les modifier ultérieurement.", "settingsClaimUsernameContinue": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameCreateUsername": "<PERSON><PERSON><PERSON> un nom d'utilisateur", "settingsClaimUsernameCreating": "Création d'un nom d'utilisateur...", "settingsClaimUsernameSuccess": "Nom d'utilisateur créé !", "settingsClaimUsernameError": "Nous avons rencontré une erreur lors de la création de votre nom d'utilisateur", "settingsClaimUsernameTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameSuccessHelperText": "Vous pouvez maintenant utiliser votre nouveau nom d'utilisateur dans tous vos portefeuilles Phantom", "settingsClaimUsernameSettingsSyncedTitle": "Paramètres synchronisés", "settingsClaimUsernameSettingsSyncedHelperText": "Dites adieu aux adresses longues et complexes, et bonjour à une identité conviviale", "settingsClaimUsernameSendToUsernameTitle": "Envoyer au nom d'utilisateur", "settingsClaimUsernameSendToUsernameHelperText": "Envoyez et recevez facilement des cryptomonnaies, connectez-vous à votre portefeuille et connectez-vous avec vos amis", "settingsClaimUsernameManageAddressesTitle": "Adresses publiques", "settingsClaimUsernameManageAddressesHelperText": "Tous les jetons ou objets de collection envoyés à votre nom d'utilisateur seront envoyés à ces adresses", "settingsClaimUsernameManageAddressesBadge": "Consultable par le public", "settingsClaimUsernameEditAddressesTitle": "<PERSON><PERSON><PERSON> les adresses publiques", "settingsClaimUsernameEditAddressesHelperText": "Tous les jetons ou objets de collection envoyés à votre nom d'utilisateur seront envoyés à ces adresses. sélectionnez une adresse par chaîne.", "settingsClaimUsernameEditAddressesError": "Une seule adresse par réseau est autorisée.", "settingsClaimUsernameEditAddressesEditAddress": "Modifier les adresses", "settingsClaimUsernameNoAddressesSaved": "Pas d'adresse publique enregistrée", "settingsClaimUsernameSave": "Enregistrer", "settingsClaimUsernameDone": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameWatching": "Surveillance de", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} compte(s)", "settingsClaimUsernameNoOfAccountsSingular": "1 compte", "settingsClaimUsernameEmptyAccounts": "Aucun compte", "settingsClaimUsernameSettingTitle": "<PERSON><PERSON>ez votre @nomdutilisateur", "settingsClaimUsernameSettingDescription": "Une identité unique pour votre portefeuille", "settingsManageUserProfileAbout": "À propos", "settingsManageUserProfileAboutUsername": "Nom d'utilisateur", "settingsManageUserProfileAboutBio": "Bio", "settingsManageUserProfileTitle": "<PERSON><PERSON><PERSON> le profil", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "Facteurs d'authentification", "settingsManageUserProfileAuthFactorsDescription": "Choisissez les phrases de sauvegarde ou les clés privées qui peuvent se connecter à votre Phantom Account.", "settingsManageUserProfileUpdateAuthFactorsToast": "Facteurs d'authentification mis à jour !", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Échec de la mise à jour des facteurs d'authentification !", "settingsManageUserProfileBiography": "Modifier la bio", "settingsManageUserProfileBiographyDescription": "A<PERSON><PERSON>z une courte biographie à votre profil", "settingsManageUserProfileUpdateBiographyToast": "Bio mise à jour !", "settingsManageUserProfileUpdateBiographyToastFailure": "Un problème est survenu. Réessayez", "settingsManageUserProfileBiographyNoUrlMessage": "Veuillez supprimer les URL de votre bio", "settingsManageUserProfileLinkedWallets": "Portefeuilles associés", "settingsManageUserProfileLinkedWalletsDescription": "Choisissez les portefeuilles qui s'affichent sur d'autres appareils lorsque vous vous connectez à votre compte Phantom.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Portefeuilles associés mis à jour !", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Échec de la mise à jour des portefeuilles associés !", "settingsManageUserProfilePrivacy": "Confidentialité", "settingsManageUserProfileUpdatePrivacyStateToast": "Confidentialité mise à jour !", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Échec de la mise à jour de la confidentialité !", "settingsManageUserProfilePublicAddresses": "Adresses publiques", "settingsManageUserProfileUpdatePublicAddressToast": "Adresse publique mise à jour !", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Échec de la mise à jour de l'adresse publique !", "settingsManageUserProfilePrivacyStatePublic": "Publique", "settingsManageUserProfilePrivacyStatePublicDescription": "Votre profil et vos adresses publiques sont visibles et peuvent être recherchées par n'importe qui", "settingsManageUserProfilePrivacyStatePrivate": "Priv<PERSON>", "settingsManageUserProfilePrivacyStatePrivateDescription": "Votre profil peut être recherché par n'importe qui, mais on doit vous demander votre permission pour pouvoir consulter votre profil et vos adresses publiques", "settingsManageUserProfilePrivacyStateInvisible": "Invisible", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Votre profil et vos adresses publiques sont cachées et ne peuvent pas être recherchés", "settingsDownloadPhantom": "Télécharger Phantom", "settingsLogOut": "Déconnexion", "seedlessAddAWalletPrimaryText": "Ajouter un portefeuille", "seedlessAddAWalletSecondaryText": "Connecter ou importer un portefeuille existant ", "seedlessAddSeedlessWalletPrimaryText": "Ajouter un portefeuille sans code de sauvegarde", "seedlessAddSeedlessWalletSecondaryText": "Utilisez votre ID Apple, Google ou votre adresse e-mail", "seedlessCreateNewWalletPrimaryText": "Créer un nouveau portefeuille ?", "seedlessCreateNewWalletSecondaryText": "Cette adresse e-mail n'est pas associé à un portefeuille. Voulez-vous en créer un ?", "seedlessCreateNewWalletButtonText": "Créer un portefeuille", "seedlessCreateNewWalletNoBundlePrimaryText": "Portefeuille introuvable", "seedlessCreateNewWalletNoBundleSecondaryText": "Cette adresse e-mail n'a pas de portefeuille", "seedlessCreateNewWalletNoBundleButtonText": "Retour", "seedlessEmailOptionsPrimaryText": "Sélectionnez votre adresse e-mail", "seedlessEmailOptionsSecondaryText": "Ajoutez un portefeuille avec votre compte Apple ou votre compte Google ", "seedlessEmailOptionsButtonText": "Continuer avec l'adresse e-mail", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Créez un portefeuille avec votre ID Apple", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Créez un portefeuille avec votre adresse Google", "seedlessAlreadyExistsPrimaryText": "Ce compte existe déjà", "seedlessAlreadyExistsSecondaryText": "Cette adresse e-mail est déjà associée à un portefeuille. Voulez-vous plutôt vous connecter ?", "seedlessSignUpWithAppleButtonText": "Inscription avec Apple", "seedlessContinueWithAppleButtonText": "Continuer avec Apple", "seedlessSignUpWithGoogleButtonText": "Inscription avec Google", "seedlessContinueWithGoogleButtonText": "Continuer avec Google", "seedlessCreateAPinPrimaryText": "<PERSON><PERSON><PERSON> un PIN", "seedlessCreateAPinSecondaryText": "Il est utilisé pour sécuriser votre portefeuille sur tous vos appareils. <1>Il ne peut pas être récupéré.</1>", "seedlessContinueText": "<PERSON><PERSON><PERSON>", "seedlessConfirmPinPrimaryText": "Confirmer votre PIN", "seedlessConfirmPinSecondaryText": "Si vous oubliez ce code PIN, vous ne pourrez pas récupérer votre portefeuille sur un nouvel appareil.", "seedlessConfirmPinButtonText": "<PERSON><PERSON><PERSON> un PIN", "seedlessConfirmPinError": "PIN incorrect. <PERSON><PERSON><PERSON>z réessayer", "seedlessAccountsImportedPrimaryText": "Comptes importés", "seedlessAccountsImportedSecondaryText": "Ces comptes seront automatiquement importés dans votre portefeuille", "seedlessPreviouslyImportedTag": "Importation précédente", "seedlessEnterPinPrimaryText": "Saisissez votre PIN", "seedlessEnterPinInvalidPinError": "PIN saisi incorrect. Seuls 4 chiffres sont autorisés", "seedlessEnterPinNumTriesLeft": "{{numTries}} tentatives restantes.", "seedlessEnterPinCooldown": "<PERSON><PERSON><PERSON><PERSON> dans {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "Le PIN doit avoir exactement 4 chiffres", "seedlessEnterPinMatch": "Les PIN correspondent", "seedlessDoneText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessEnterPinToSign": "Saisissez votre PIN pour signer cette transaction", "seedlessSigning": "Signature", "seedlessCreateSeed": "Créer un portefeuille de phrases de sauvegarde", "seedlessImportOptions": "Autres options d'importation", "seedlessImportPrimaryText": "Options d'importation", "seedlessImportSecondaryText": "Importez un portefeuille existant avec votre phrase de sauvegarde, votre clé privée ou votre portefeuille matériel", "seedlessImportSeedPhrase": "Importer une phrase de sauvegarde", "seedlessImportPrivateKey": "Importer une clé privée", "seedlessConnectHardwareWallet": "Connecter un portefeuille matériel", "seedlessTryAgain": "<PERSON><PERSON><PERSON><PERSON>", "seedlessCreatingWalletPrimaryText": "Création d'un portefeuille", "seedlessCreatingWalletSecondaryText": "Ajout d'un portefeuille social", "seedlessLoadingWalletPrimaryText": "Chargement du portefeuille", "seedlessLoadingWalletSecondaryText": "Importation et surveillance de vos portefeuilles liés", "seedlessLoadingWalletErrorPrimaryText": "Échec du chargement du portefeuille", "seedlessCreatingWalletErrorPrimaryText": "Échec de la création du portefeuille", "seedlessErrorSecondaryText": "<PERSON><PERSON><PERSON>z réessayer", "seedlessAuthAlreadyExistsErrorText": "L'adresse e-mail fournie appartient déjà à un autre compte Phantom", "seedlessAuthUnknownErrorText": "Une erreur inconnue est survenue. Veuillez réessayer plus tard", "seedlessAuthUnknownErrorTextRefresh": "Une erreur inconnue est survenue. Veuillez réessayer plus tard. Actualisez la page pour réessayer.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON> la fenêtre", "seedlessWalletExistsErrorPrimaryText": "Un portefeuille social existe déjà sur votre appareil", "seedlessWalletExistsErrorSecondaryText": "Veuillez revenir ou fermer cet écran", "seedlessValueProp1PrimaryText": "Installation transparente", "seedlessValueProp1SecondaryText": "Créez un portefeuille à l'aide d'un compte Google ou Apple et commencez à explorer le web3 en toute simplicité", "seedlessValueProp2PrimaryText": "S<PERSON><PERSON><PERSON><PERSON>", "seedlessValueProp2SecondaryText": "Votre portefeuille est stocké de manière sécurisée et décentralisée grâce à plusieurs facteurs", "seedlessValueProp3PrimaryText": "Récupération facile", "seedlessValueProp3SecondaryText": "Récupérez l'accès à votre portefeuille à l'aide de votre compte Google ou Apple et d'un code PIN à 4 chiffres", "seedlessLoggingIn": "Connexion...", "seedlessSignUpOrLogin": "Inscription ou connexion", "seedlessContinueByEnteringYourEmail": "Saisissez votre adresse e-mail pour continuer", "seedless": "Sans code de sauvegarde", "seed": "Phrases de sauvegarde", "seedlessVerifyPinPrimaryText": "Vérifier le PIN", "seedlessVerifyPinSecondaryText": "Veuillez saisir votre code PIN pour continuer", "seedlessVerifyPinVerifyButtonText": "Vérifier", "seedlessVerifyPinForgotButtonText": "Code oublié ?", "seedlessPinConfirmButtonText": "Confirmer", "seedlessVerifyToastPrimaryText": "Vérifiez votre PIN", "seedlessVerifyToastSecondaryText": "Nous vous demanderons parfois de vérifier votre code PIN pour éviter que vous l'oubliez. Si cela devait vous arriver, vous ne pourrez pas récupérer votre portefeuille.", "seedlessVerifyToastSuccessText": "Votre code PIN est vérifié !", "seedlessForgotPinPrimaryText": "Réinitialiser le PIN à partir d'un autre appareil", "seedlessForgotPinSecondaryText": "Pour des questions de sécurité, vous ne pouvez réinitialiser votre PIN qu'à partir d'autres appareils où vous êtes connecté", "seedlessForgotPinInstruction1PrimaryText": "<PERSON><PERSON>vrez l'autre appareil", "seedlessForgotPinInstruction1SecondaryText": "Allez sur un autre appareil où votre compte Phantom est connecté avec votre adresse e-mail", "seedlessForgotPinInstruction2PrimaryText": "Allez dans les Paramètres", "seedlessForgotPinInstruction2SecondaryText": "Dans les Paramètres, sélectionnez « Sécurité et confidentialité », puis « Réinitialiser le PIN »", "seedlessForgotPinInstruction3PrimaryText": "Définissez votre nouveau PIN", "seedlessForgotPinInstruction3SecondaryText": "Une fois votre nouveau PIN défini, vous pouvez vous connecter à votre portefeuille depuis cet appareil", "seedlessForgotPinButtonText": "J'ai déjà suivi ces étapes", "seedlessResetPinPrimaryText": "Réinitialiser le PIN", "seedlessResetPinSecondaryText": "Saisissez un nouveau PIN dont vous vous souviendrez. Il est utilisé pour sécuriser votre portefeuille sur tous vos appareils", "seedlessResetPinSuccessText": "Votre code PIN a été mis à jour !", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "En créant un portefeuille, vous acceptez nos <1>Conditions de service</1> et notre <5>Politique de confidentialité</5>", "pageNotFound": "Page introuvable", "pageNotFoundDescription": "Nous ne vous ignorons pas ! Cette page n'existe pas ou a été déplacée.", "webTokenPagesLegalDisclaimer": "Les informations tarifaires sont uniquement fournies à titre d'information et ne constituent pas un conseil financier. Les données de marché sont fournies par des tiers et Phantom ne garantit pas l'exactitude de ces informations.", "signUpOrLogin": "Connexion ou inscription", "portalOnboardingAgreeToTermsOfServiceInterpolated": "En créant un compte, vous acceptez nos <1>Conditions de service</1> et notre <5>Politique de confidentialité</5>", "feedNoActivity": "Pas encore d'activité", "followRequests": "Demandes d'abonnement", "following": "Abonnements", "followers": "Abonnés", "follower": "<PERSON><PERSON><PERSON><PERSON>", "joined": "Rejoint", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON><PERSON>'<PERSON>", "noFollowing": "Pas d'abonnement", "noUsersFound": "Aucun utilisateur trouvé", "viewProfile": "<PERSON><PERSON><PERSON><PERSON> le profil", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}