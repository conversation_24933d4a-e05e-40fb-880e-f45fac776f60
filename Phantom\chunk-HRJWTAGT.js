import{d as Q}from"./chunk-OYGO47TI.js";import{P as h,Pa as y,Ra as z,Sa as w,te as j,wc as W}from"./chunk-MZZEJ42N.js";import{Z as M,a as q,b as x,x as S}from"./chunk-ALUTR72U.js";import{ka as f}from"./chunk-L3A2KHJO.js";import{a as r}from"./chunk-4P36KWOF.js";import{a as wt}from"./chunk-7X4NV6OJ.js";import{f as ht,h as n,n as a}from"./chunk-3KENBVE7.js";n();a();var B=new h.Public<PERSON>ey("Config*****************************11111111");var _t="https://docs.solana.com/staking",Et="https://docs.solana.com/implemented-proposals/staking-rewards",It={type:"SolanaNative",data:{amount:"0",balance:new q(0),chain:{id:y.solana.mainnetID,imageUrl:y.solana.iconUrl,name:y.solana.name,symbol:y.solana.tokenSymbol},decimals:9,key:"SolanaNative",logoUri:"https://cdn.jsdelivr.net/gh/solana-labs/token-list@main/assets/mainnet/So*****************************111111111112/logo.png",name:"Solana",symbol:"SOL",tokenAddress:void 0,walletAddress:"",spamStatus:"NOT_VERIFIED"}},Ae=new Map([[z.Solana.Mainnet,It]]),Ot="https://www.jito.network/blog/lsts-vs-native-assets/",Ct="https://www.jito.network/staking/?mode=unstake",Rt="https://help.phantom.app/hc/en-us/articles/**************",Lt="https://www.jito.network/blog/reward-bearing-vs-rebasing-solana-liquid-staking-tokens/",fe=5,be=3e4;n();a();function G(t,e){let o=new x(t/100),i=new x(e/100);return o.minus(o.times(i)).times(100).decimalPlaces(2).toNumber()}n();a();var H=t=>(e,o)=>{let i=t.indexOf(e.voteAccountPubkey),s=t.indexOf(o.voteAccountPubkey);return(i>-1?i:1/0)-(s>-1?s:1/0)};n();a();n();a();n();a();var d="staking";var _=[d,"stakeAccounts"],E=[d,"stakeActivationData"],I=[d,"convertStakeAccountList"],O=[d,"convertStakeTransaction"],Nt=[d,"mintTokenTransaction"],C="averageValidatorApy",Dt="recommendedValidators",Ut="signatureStatus",Kt="stakeAccountMinBalance",Ft="stakeRewardsHistory",qt="stakeApy",Mt="validatorInfo",zt="voteAccounts",Wt="keybaseUserAvatar",b={keybaseUserAvatar(t){return[d,Wt,t]},signatureStatus(t){return[d,Ut,t]},stakeAccounts(t,e){return[..._,t,e]},stakeAccountMinBalance(){return[d,Kt]},stakeActivationData(t){return[...E,t]},stakeRewardsHistory(t){return[d,Ft,t]},stakeApy(){return[d,qt]},averageValidatorApy(){return[d,C]},recommendedValidators(){return[d,Dt]},validatorInfo(){return[d,Mt]},voteAccounts(t){return[d,zt,t]},convertStakeAccountList(t,e){return[...I,t,e]},liquidStakingSupportedTokens(){return[d,"liquidStakingSupportedTokens"]},getConvertStakeTransaction(t){return[...O,t]},getMintTokenTransaction(t){return[...Nt,t]},featureUsed(){return[d,"featureUsed"]}};var J=t=>()=>{t.invalidateQueries({predicate:e=>{let o=i=>i.every((s,c)=>e.queryKey[c]===s);return o(_)||o(E)||o(I)||o(O)}})};n();a();function Y(t,e=9){return t.match(new RegExp(`^\\d*(\\.\\d{0,${e}})?$`))}n();a();var X={type:"fungible",isNativeOfType:"sol",chain:"solana",name:"Solana",symbol:"SOL",iconUrl:"https://cdn.jsdelivr.net/gh/solana-labs/token-list@main/assets/mainnet/So*****************************111111111112/logo.png",decimals:9},jt="stake",Z=t=>new R(t,jt),R=class extends Q{constructor(){super(...arguments);this.createStakeAccountAndDelegateStake=o=>{this.analytics.capture("createStakeAccountAndDelegateStake",{data:{...o,...X}})};this.deactivateStake=()=>{this.analytics.capture("deactivateStake",{data:{...X}})}}};var Qt=M(W,t=>Z(t));n();a();var v=ht(wt());n();a();var tt=(t,e)=>t+e[1];function et(t){let e=Bt(t),o={commissionWeight:.6,epochCreditsWeight:.3,activatedStakeWeight:.1},i=Ht(e.maxEpochCredits,e.maxActivatedStake,o);return t.sort(i)}function Bt(t){let e={maxEpochCredits:0,maxActivatedStake:0};for(let o of t){let i=o.epochCredits.reduce(tt,0);e.maxEpochCredits<i&&(e.maxEpochCredits=i),e.maxActivatedStake<o.activatedStake&&(e.maxActivatedStake=o.activatedStake)}return e}function Gt(t,e,o,i,s){let c=(100-t)/100,p=e/o,l=Math.log10(i+1)/Math.log10(s+1);return{normalizedCommission:c,normalizedEpochCredits:p,normalizedActivatedStake:l}}function $(t,e,o,i){let{commission:s,epochCredits:c,activatedStake:p}=t,{commissionWeight:l,epochCreditsWeight:k,activatedStakeWeight:A}=i,{normalizedCommission:g,normalizedEpochCredits:T,normalizedActivatedStake:P}=Gt(s,c.reduce(tt,0),e,p,o);return(s>10?.5:1)*(l*g+k*T+A*P)}function Ht(t,e,o){return(i,s)=>{let c=$(i,t,e,o);return $(s,t,e,o)-c}}n();a();n();a();var Jt=(s=>(s[s.Always=1/0]="Always",s[s.Short=S({days:1})]="Short",s[s.Medium=S({days:3})]="Medium",s[s.Long=S({days:7})]="Long",s))(Jt||{}),L=(c=>(c[c.Immediate=0]="Immediate",c[c.Short=S({minutes:1})]="Short",c[c.Medium=S({minutes:2.5})]="Medium",c[c.Long=S({minutes:5})]="Long",c[c.Never=1/0]="Never",c))(L||{});var Yt=t=>async()=>(await t.getParsedProgramAccounts(B)).reduce((o,i)=>{if(i.account.data.parsed?.type!=="validatorInfo")return o;let s=i.pubkey.toString(),c=i.account.data.parsed.info.keys,[,p]=c;if(!p.signer)return o;let l=p.pubkey,k=i.account.data.parsed.info.configData;return o.push({identityPubkey:l,infoPubkey:s,info:k}),o},[]);function ot(t){let e=b.validatorInfo(),o=Yt(t);return f({queryKey:e,queryFn:o,placeholderData:[],staleTime:1/0})}n();a();var Xt=async t=>{let e=await t.getVoteAccounts();return[...e.current,...e.delinquent].map(o=>({identityPubkey:o.nodePubkey,voteAccountPubkey:o.votePubkey,commission:o.commission,lastVote:o.lastVote,epochCredits:o.epochCredits,activatedStake:o.activatedStake})).filter(o=>o.commission<99)};function nt(t){let e=b.voteAccounts(t.rpcEndpoint);return f({queryKey:e,queryFn:async()=>Xt(t),placeholderData:[],staleTime:1/0})}function Zt(t){let{isFetched:e,isFetching:o,isPending:i,isSuccess:s,isError:c,dataUpdatedAt:p,errorUpdatedAt:l,error:k,refetch:A,data:g}=nt(t),T=Math.max(p,l),{isFetched:P,isFetching:D,isPending:U,isSuccess:st,isError:ct,dataUpdatedAt:ut,errorUpdatedAt:dt,error:pt,refetch:K,data:V}=ot(t),lt=Math.max(ut,dt),mt=Math.max(T,lt),St=e&&P,kt=o||D,gt=s||st,yt=c||ct,At=k||pt,ft=i||U,bt=(0,v.useCallback)(async()=>{Promise.all([A(),K()])},[K,A]);return{results:(0,v.useMemo)(()=>{if(!g)return[];if(!V)return[];let vt=et(g),Tt=new Map(vt.map(m=>[m.identityPubkey,m])),Pt=new Map(V.map(m=>[m.identityPubkey,m])),F=[];for(let[m,Vt]of Tt){let xt=Pt.get(m);F.push({...Vt,...xt})}return F},[V,g]),dataUpdatedAt:mt,error:At,isSuccess:gt,isError:yt,isFetched:St,isFetching:kt,isPending:ft,refetch:bt}}n();a();function N(t){let e=!!t?.info.meta.lockup,o=new Date((t?.info.meta.lockup?.unixTimestamp??0)*1e3);return e&&o.getTime()>Date.now()}n();a();n();a();var $t=r.object({apy:r.number()}),te=r.object({validators:r.array(r.string())}),at=r.object({epoch:r.number(),effectiveSlot:r.number(),amount:r.number(),postBalance:r.number(),percentChange:r.number(),apr:r.number(),timestamp:r.number()}),ee=r.object({items:r.array(at)}),oe=r.object({address:r.string(),chainId:w,decimals:r.number(),logoURI:r.string(),name:r.string(),symbol:r.string()}),ne=r.object({caip19:r.string(),metadata:oe}),ae=r.object({vote_account:r.string()}),re=r.object({apy:r.string(),chainId:w,convertRatio:r.string(),name:r.string(),stakePoolAddress:r.string(),stakePoolTokenMetadata:ne,validators:r.array(ae)}),Lo=r.record(r.string(),r.array(re));n();a();n();a();var rt={transactionData:void 0,fungibleToStake:void 0,provider:void 0,convert:{validatorVote:"",stakeAccount:"",validatorName:"",eligibleAccountsCount:0},amount:"",navigateTo:null,skipDismissRouting:void 0},it=j(t=>({...rt,resetStaking:()=>t(rt),setTransactionData:e=>t({transactionData:e}),setFungibleToStake:e=>t({fungibleToStake:e}),setProvider:e=>t({provider:e}),setConvertStakeAccount:e=>t({convert:e}),setAmount:e=>t({amount:e}),setNavigateTo:e=>t({navigateTo:e}),setSkipDismissRouting:e=>t({skipDismissRouting:e})}));n();a();var ie=["liquidStakingSupportedTokens","featureUsed",C],se=t=>{let e=String(t.queryKey[1]);return ie.includes(e)};n();a();var ue=(t,e)=>{if(t.type==="initialized")return!1;let o=t?.info?.stake?.delegation?.voter??"",i=N(t);return t.activationState==="active"&&e.has(o)&&!i};var nn={identityPublicKey:"7GkMBmtrTZz8QbjSe1sXvAUtz7Pp42SQxfT5ymmJD4We",voteAccountPublicKey:"J2nUHEAgZFRyuJbFjdqPrAa9gyWDuc7hErtDQHPhsYRp"};export{_t as a,Et as b,Ae as c,Ot as d,Ct as e,Rt as f,Lt as g,fe as h,be as i,$t as j,te as k,Lo as l,G as m,b as n,J as o,H as p,Y as q,Qt as r,it as s,se as t,Jt as u,Zt as v,N as w,ue as x,nn as y};
//# sourceMappingURL=chunk-HRJWTAGT.js.map
