import{a as U}from"./chunk-TQNG3Z6X.js";import{F as g,Ja as xe,La as Se,Ma as Re,W as P,ea as Ne}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import{c as G,d as Ie}from"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import{h as Ee}from"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import{a as fe}from"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import"./chunk-VQVTLSDS.js";import{j}from"./chunk-OKP6DFCI.js";import{o as B,rb as Le}from"./chunk-WIQ4WVKX.js";import{q as w,v as Y,w as X,y as N}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import{b as ge}from"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as we}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{s as be,z as Ae}from"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import{a as Ce,g as ve,o as De}from"./chunk-SLQBAOEK.js";import{$d as he,Pa as S,Pb as M,Rb as ye,Sb as R,Xa as E}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as ke}from"./chunk-56SJOU6P.js";import{E as ue,ia as h}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{Ya as Te}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as me}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as ce,h as de,n as pe}from"./chunk-3KENBVE7.js";de();pe();var i=ce(me()),W=ce(me());var He=B.a`
  cursor: pointer;
`,Ye=B.div`
  display: flex;
  justify-content: space-between;
  gap: 8px;
`,L=B.span`
  cursor: pointer;
  color: ${p=>p.color||"white"};
  &:hover {
    color: #8a7ad8;
  }
`,Xe=B.div`
  margin: -16px -16px 0 -16px;
  width: calc(100% + 32px);
  height: calc(100% + 16px);
  overflow-y: auto;
  position: absolute;
  top: 0;
  left: 0;
`,Be=({label:p,url:m})=>i.createElement(He,{onClick:()=>self.open(m)},i.createElement(Le,{color:"#AB9FF2",noWrap:!0,lineHeight:14,maxWidth:`${ge/2}`,weight:600,size:14},p)),je=({activityItem:p})=>{let{interactionData:m,chainMeta:V}=p,{t:e}=ke(),{transactionType:_}=m,{status:Pe,networkFee:q,networkFeePayer:J}=V,D=V.chainId,b=Pe==="failed",{pushDetailView:Q}=Ee(),{data:[Z]}=Te(["enable-unified-token-pages"]),{getKnownAddressLabel:Ue}=Ae(),{data:ee}=be(),{handleHideModalVisibility:te}=Re(),z=(0,W.useCallback)(()=>te("activityItem"),[te]),{data:F}=he(),ae=F?.isReadOnly,r=(0,W.useCallback)((a,t)=>{Q(i.createElement(Xe,null,Z?i.createElement(xe,{caip19:a,title:t,entryPoint:"activityItemDetail"}):i.createElement(Se,{caip19:a,title:t,entryPoint:"activityItemDetail"})))},[Q,Z]),_e=J?F?.addresses.some(a=>a.address.toLowerCase()===J.toLowerCase()):!1,Fe=ue(p.timestamp*1e3,e("richTransactionDetailAt")),O=S.getTokenDecimals(D),A=S.getTokenSymbol(D),Oe=q?`-${h(q,O)} ${A}`:e("historyUnknownDappName"),I=i.createElement(g,{activityItem:p,size:"medium"}),u={label:e("richTransactionDetailDate"),value:Fe},ie=w(p.id),k=[];if(ee){let a=V.chainId,t=ee.explorers[a]??Ce.get(a).defaultExplorer,n=De({networkID:a,endpoint:"transaction",explorerType:t,param:ie});n!==""&&k.push({label:e("richTransactionDetailViewOnExplorer",{explorer:ve[t]}),value:n,type:"link"})}let x=E({chainId:D,slip44:S.getSlip44(D),resourceType:"nativeToken"}),y=b?{label:e("richTransactionDetailStatus"),value:e("richTransactionDetailFailed"),color:"#EB3742"}:{label:e("richTransactionDetailStatus"),value:e("historyStatusSucceeded"),color:"#21E56F"},C={label:e("richTransactionDetailNetworkFee"),value:_e?Oe:void 0},v={label:e("historyNetwork"),value:S.getNetworkName(D)},ne=Ne(),oe=(0,W.useCallback)((a,t)=>{we.capture("activityItemSwapAgainClicked",{data:{from_asset_caip19:t,to_asset_caip19:a}}),ne({buyFungibleCaip19:a,sellFungibleCaip19:t}),z()},[ne,z]),d;switch(_){case"TOKEN_SEND":{let t=m.balanceChanges[0],n=X(t,F?.addresses??[]),{token:{symbol:o,decimals:l,displayName:s,tokenType:c,isSpam:T},amount:f}=t,$=Y(c),se=E(N(t.token,D)),$e=n?"+":"-",K=l!==void 0?`${h(f,l)} ${o||""}`:e("transactionsUnknownAmount"),Me=`${$e}${K}`,H=w(n?t.from:t.to),le=ye(H,void 0,Ue(H,D)),We={label:e(n?"richTransactionDetailFrom":"richTransactionDetailTo"),value:i.createElement(U,{delayedChildren:i.createElement(P,{alignment:"topCenter",copyString:H},le)},le)},re=Ke=>i.createElement(L,{onClick:()=>r(se,t.token.symbol),color:n&&!b?"#21E56F":void 0},Ke),Ve=b?[{label:e("historyAttemptedAmount"),value:K}]:[],ze=b?e("transactionsSendFailed"):`${e("transactionsSent")}`;d={sections:[{rows:[u,y,We,v,C,...Ve,...k]}],title:n?e("transactionsReceived"):ze,primaryText:$?{value:s||e("richTransactionDetailUnknownNFT")}:b||l===void 0?{value:re(K)}:{value:re(Me)},secondaryText:{value:""},image:$?I:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(se,t.token.symbol)}),warning:n&&T?e("spamActivityWarning"):""};break}case"TOKEN_BURN":{let{amount:a,token:t}=m,n="";b||(t.displayName?a==="1"?n=t.displayName:n=`${t.displayName} (${a})`:n=e("richTransactionDetailUnknownNFT")),d={sections:[{rows:[u,y,v,C,...k]}],title:e("richTransactionDetailBurned"),primaryText:{value:n},secondaryText:{value:""},image:I};break}case"TOKEN_SWAP":{let{receiveAmount:a,receiveToken:t,sendAmount:n,sendToken:o,dapp:l}=m,s=R(w(o.id)||""),c=R(w(t.id)||""),T=E(N(o,D)),f=E(N(t,D));d={sections:[{rows:[u,y,v,C]},{title:e("richTransactionDetailSwapDetails"),rows:[{label:e("richTransactionDetailProvider"),value:l?.displayName&&l.logoURI?i.createElement(G,{name:l.displayName,imageURL:l.logoURI}):l?.displayName||e("historyUnknownDappName")},{label:e("richTransactionDetailYouPaid"),value:o.decimals!==void 0?`-${h(n,o.decimals)} ${o.symbol||""}`:e("transactionsUnknownAmount"),onClick:()=>r(T,o.symbol||s)},{label:e("richTransactionDetailYouReceived"),value:t.decimals!==void 0?`+${h(a,t.decimals)} ${t.symbol||""}`:e("transactionsUnknownAmount"),color:t.decimals!==void 0&&!b?"#21E56F":"",onClick:()=>r(f,t.symbol||c)},...k]}],title:e("richTransactionDetailTokenSwap"),primaryText:{value:i.createElement(i.Fragment,null,i.createElement(L,{onClick:()=>r(T,o.symbol||s)},o.symbol||s)," \u2192 ",i.createElement(L,{onClick:()=>r(f,t.symbol||c)},t.symbol||c))},secondaryText:{value:""},image:i.createElement(g,{activityItem:p,size:"medium",onClick:ae?void 0:()=>oe(f,T)}),swapAgain:ae?void 0:{sendToken:T,receiveToken:f}};break}case"BRIDGE_INIT":{let t=m.balanceChanges[0],n=X(t,F?.addresses??[]),{token:{symbol:o,decimals:l},amount:s}=t,c=E(N(t.token,D)),T=l!==void 0?`${h(s,l)} ${o||""}`:e("transactionsUnknownAmount"),f=p.interactionData.explorerUrl??`https://explorer.li.fi/tx/${ie}`,$={label:e("transactionBridgeStatusLink"),value:f,type:"link"};d={sections:[{rows:[u,y,v,C,$]}],title:b?e("transactionBridgeInitiatedFailed"):`${e("transactionBridgeInitiated")}`,primaryText:{value:i.createElement(L,{onClick:()=>r(c,o),color:n&&!b?"#21E56F":void 0},T)},secondaryText:{value:""},image:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(c,o)})};break}case"TOKEN_UNWRAP":{let{token:a,amount:t}=m,n=R(w(a.id)||""),o=E(N(a,D)),l=a.decimals!==void 0?`+${h(t,a.decimals)} ${a.symbol||""}`:e("transactionsUnknownAmount");d={sections:[{rows:[u,y,v,C]},{title:e("richTransactionDetailUnwrapDetails"),rows:[{label:e("richTransactionDetailYouReceived"),value:i.createElement(L,{onClick:()=>r(o,a.symbol),color:b?void 0:"#21E56F"},l)},...k]}],title:e("richTransactionDetailTokenUnwrap"),primaryText:{value:i.createElement(L,{onClick:()=>r(o,a.symbol)},a.symbol||n)},secondaryText:{value:""},image:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(o,a.symbol)})};break}case"WITHDRAW_STAKE":{let{amount:a,stakeAccount:t}=m,n=w(t),o=M(n,4);d={sections:[{rows:[u,y,{label:e("richTransactionDetailAccount"),value:i.createElement(U,{delayedChildren:i.createElement(P,{alignment:"topCenter",copyString:n},o)},o)},v,C,...k]}],title:e("richTransactionDetailWithdrawStake"),primaryText:{value:i.createElement(L,{onClick:()=>r(x,A),color:b?void 0:"#21E56F"},"+",h(a,O)," ",A)},secondaryText:{value:""},image:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(x,A)})};break}case"DEACTIVATE_STAKE":{let{amount:a,stakeAccount:t}=m,n=w(t),o=M(n,4);d={sections:[{rows:[u,y,{label:e("richTransactionDetailAccount"),value:i.createElement(U,{delayedChildren:i.createElement(P,{alignment:"topCenter",copyString:n},o)},o)},v,C,...k]}],title:e("richTransactionDetailUnstaked"),primaryText:{value:i.createElement(L,{onClick:()=>r(x,A)},h(a,O)," ",A)},secondaryText:{value:""},image:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(x,A)})};break}case"STAKE":{let{amount:a,stakeAccount:t,validatorName:n,validatorWebsite:o}=m,l=w(t),s=M(l,4);d={sections:[{rows:[u,y,{label:e("richTransactionDetailAccount"),value:i.createElement(U,{delayedChildren:i.createElement(P,{alignment:"topCenter",copyString:l},s)},s)},{label:"Validator",value:n&&o?i.createElement(Be,{label:n,url:o}):n},v,C,...k]}],title:e("richTransactionDetailStaked"),primaryText:{value:i.createElement(L,{onClick:()=>r(x,A)},"-",h(a,O)," ",A)},secondaryText:{value:""},image:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(x,A)})};break}case"COLLECTIBLE_LIST":case"COLLECTIBLE_BUY_ITEM":case"COLLECTIBLE_CANCEL_BID":case"COLLECTIBLE_BID_ITEM":case"COLLECTIBLE_UNLIST":case"COLLECTIBLE_SELL_ITEM":{let{dapp:a,item:t,forAmount:n,forAsset:o,listingUrl:l}=m,s=o.decimals!==void 0?`${h(n,o.decimals)} ${o.symbol||""}`:e("transactionsUnknownAmount"),c=t.displayName||e("richTransactionDetailUnknownNFT"),T={COLLECTIBLE_LIST:{priceRow:{label:e("richTransactionDetailListingPrice"),value:s},sectionTitle:e("richTransactionDetailListingDetails"),detailTitle:e("richTransactionDetailListed")},COLLECTIBLE_UNLIST:{priceRow:{label:e("richTransactionDetailOriginalListingPrice"),value:s},sectionTitle:e("richTransactionDetailListingDetails"),detailTitle:e("richTransactionDetailUnlisted")},COLLECTIBLE_BID_ITEM:{priceRow:{label:e("richTransactionDetailPrice"),value:s},sectionTitle:e("richTransactionDetailBidDetails"),detailTitle:e("richTransactionDetailBid")},COLLECTIBLE_CANCEL_BID:{priceRow:{label:e("richTransactionDetailPrice"),value:s},sectionTitle:e("richTransactionDetailBidDetails"),detailTitle:e("richTransactionDetailCancelBid")},COLLECTIBLE_BUY_ITEM:{priceRow:{label:e("richTransactionDetailPrice"),value:s},sectionTitle:e("richTransactionDetailPurchaseDetails"),detailTitle:e("richTransactionDetailBought")},COLLECTIBLE_SELL_ITEM:{priceRow:{label:e("richTransactionDetailPrice"),value:s},sectionTitle:e("richTransactionDetailSaleDetails"),detailTitle:e("richTransactionDetailSold")}};d={sections:[{rows:[u,y,v,C]},{title:T[_].sectionTitle,rows:[T[_].priceRow,{label:e("richTransactionDetailItem"),value:c&&l?i.createElement(Be,{label:c,url:l}):c},{label:e("richTransactionDetailMarketplace"),value:a?.displayName&&a?.logoURI?i.createElement(G,{name:a.displayName,imageURL:a.logoURI}):a?.displayName||""},...k]}],title:T[_].detailTitle,primaryText:{value:c},secondaryText:{value:""},image:I};break}case"CANCEL_TX":{d={sections:[{rows:[u,y,v,C,...k]}],title:e("transactionCancelled"),primaryText:{value:""},secondaryText:{value:""},image:I};break}case"TOKEN_APPROVAL":{let{dapp:a,token:t,isApproved:n}=m,o=a?.displayName||e("historyUnknownDappName"),l=a?.displayName?[{label:e("transactionApproveAppLabel"),value:a?.displayName}]:[],s=R(w(t.id)||""),c=Y(t.tokenType)?t.displayName:t.symbol,T=E(N(t,D));d={sections:[{rows:[u,y,v,C]},{title:e("transactionApproveDetailsTitle"),rows:[...l,{label:e("transactionApproveTokenLabel"),value:c||s,onClick:()=>r(T,c||s)},...k]}],title:i.createElement(L,{onClick:()=>r(T,c||s)},e(n?"transactionApproveToken":"transactionRevokeApproveToken",{tokenSymbol:c||""})),primaryText:{value:""},secondaryText:{value:o},image:i.createElement(g,{activityItem:p,size:"medium",onClick:()=>r(T,c||s)})};break}case"COLLECTION_APPROVAL":{let{dapp:a,collection:t,isApproved:n}=m,o=a?.displayName||e("historyUnknownDappName"),l=a?.displayName?[{label:e("transactionApproveAppLabel"),value:a?.displayName}]:[],s=t.displayName;d={sections:[{rows:[u,y,v,C]},{title:e("transactionApproveDetailsTitle"),rows:[...l,{label:e("transactionApproveCollectionLabel"),value:s},...k]}],title:e(n?"transactionApproveToken":"transactionRevokeApproveToken",{tokenSymbol:s}),primaryText:{value:""},secondaryText:{value:o},image:I};break}case"CANCEL_ORDER":{let{dapp:a}=m;d={sections:[{rows:[u,y,v,C,...k]}],title:e("transactionCancelOrder"),primaryText:{value:""},secondaryText:{value:a?.displayName||e("historyUnknownDappName")},image:I};break}case"UNCLASSIFIED_APP_INTERACTION":default:{let{dapp:a}=m,t=a?.displayName||e("historyUnknownDappName");d={sections:[{rows:[u,y,v,C,...k]}],title:e("richTransactionDetailAppInteraction"),primaryText:{value:""},secondaryText:{value:t},image:I};break}}return i.createElement(i.Fragment,null,i.createElement(Ie,{...d}),i.createElement(fe,{removeFooterExpansion:!1},i.createElement(Ye,null,i.createElement(j,{onClick:z},e("commandClose")),d.swapAgain&&i.createElement(j,{onClick:()=>oe(d.swapAgain.receiveToken,d.swapAgain.sendToken),theme:"primary"},e("commandSwapAgain")))))},ht=je;export{je as ActivityItemDetail,Be as Link,ht as default};
//# sourceMappingURL=ActivityItemDetail-NOPJ2N2N.js.map
