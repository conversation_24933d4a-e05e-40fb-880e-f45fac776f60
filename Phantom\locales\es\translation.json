{"commandAdd": "<PERSON><PERSON><PERSON>", "commandAccept": "Aceptar", "commandApply": "Aplicar", "commandApprove": "<PERSON><PERSON><PERSON>", "commandAllow": "<PERSON><PERSON><PERSON>", "commandBack": "Atrás", "commandBuy": "<PERSON><PERSON><PERSON>", "commandCancel": "<PERSON><PERSON><PERSON>", "commandClaim": "<PERSON><PERSON><PERSON><PERSON>", "commandClaimReward": "Reclame su recompensa", "commandClear": "Bo<PERSON>r", "commandClose": "<PERSON><PERSON><PERSON>", "commandConfirm": "Confirmar", "commandConnect": "Conectar", "commandContinue": "<PERSON><PERSON><PERSON><PERSON>", "commandConvert": "Convertir", "commandCopy": "Copiar", "commandCopyAddress": "<PERSON><PERSON><PERSON>", "commandCopyTokenAddress": "Copiar dirección del token", "commandCreate": "<PERSON><PERSON><PERSON>", "commandCreateTicket": "Crear incidencia", "commandDeny": "<PERSON><PERSON><PERSON>", "commandDismiss": "<PERSON><PERSON><PERSON>", "commandDontAllow": "No permitir", "commandDownload": "<PERSON><PERSON><PERSON>", "commandEdit": "<PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON> perfil", "commandEnableNow": "Activar ahora", "commandFilter": "<PERSON><PERSON><PERSON>", "commandFollow": "<PERSON><PERSON><PERSON>", "commandHelp": "<PERSON><PERSON><PERSON>", "commandLearnMore": "Más información", "commandLearnMore2": "Más información", "commandMint": "<PERSON><PERSON><PERSON><PERSON>", "commandMore": "Más", "commandNext": "Siguient<PERSON>", "commandNotNow": "<PERSON>ora no", "commandOpen": "Abrir", "commandOpenSettings": "<PERSON><PERSON><PERSON>", "commandPaste": "<PERSON><PERSON><PERSON>", "commandReceive": "Recibir", "commandReconnect": "Volver a conectar", "commandRecordVideo": "Grabar vídeo", "commandRequest": "Solicitar", "commandRetry": "Reintentar", "commandReview": "Rev<PERSON><PERSON>", "commandRevoke": "Revocar", "commandSave": "Guardar", "commandScanQRCode": "Escanear código QR", "commandSelect": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandSelectMedia": "Seleccionar medios", "commandSell": "Vender", "commandSend": "Enviar", "commandShare": "Compartir", "commandShowBalance": "<PERSON>rar saldo", "commandSign": "<PERSON><PERSON><PERSON>", "commandSignOut": "Sign Out", "commandStake": "Apostar", "commandMintLST": "Acuñar JitoSOL", "commandSwap": "Intercambiar", "commandSwapAgain": "Intercambiar de nuevo", "commandTakePhoto": "Tomar una foto", "commandTryAgain": "Reintentar", "commandViewTransaction": "Ver transacción", "commandReportAsNotSpam": "Marcar como no spam", "commandReportAsSpam": "Marcar como spam", "commandPin": "<PERSON><PERSON>", "commandBlock": "Bloquear", "commandUnblock": "Desb<PERSON>que<PERSON>", "commandUnstake": "Cancelar apuesta", "commandUnpin": "<PERSON><PERSON><PERSON>", "commandHide": "Ocultar", "commandUnhide": "<PERSON><PERSON><PERSON>", "commandBurn": "<PERSON><PERSON>", "commandReport": "<PERSON><PERSON><PERSON><PERSON>", "commandView": "<PERSON>er", "commandProceedAnywayUnsafe": "Proceder de todos modos (no seguro)", "commandUnfollow": "<PERSON><PERSON>", "commandUnwrap": "Desenvolver", "commandConfirmUnsafe": "Confirmar (no seguro)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, confirmar (no seguro)", "commandConfirmAnyway": "Confirma<PERSON> de todos modos", "commandReportIssue": "Informar sobre un problema", "commandSearch": "Buscar", "commandShowMore": "Mostrar más", "commandShowLess": "<PERSON><PERSON> menos", "pastParticipleClaimed": "Reclamada", "pastParticipleCompleted": "Completada", "pastParticipleCopied": "Copiado", "pastParticipleDone": "<PERSON><PERSON>", "pastParticipleDisabled": "Desactivado", "pastParticipleRequested": "Solicitado", "nounName": "Nombre", "nounNetwork": "Red", "nounNetworkFee": "Comisión de la red", "nounSymbol": "Símbolo", "nounType": "Tipo", "nounDescription": "Descripción", "nounYes": "Sí", "nounNo": "No", "amount": "Importe", "limit": "Límite", "new": "Nuevo", "gotIt": "Entendido", "internal": "Interno", "reward": "Recompensa", "seeAll": "Ver todo", "seeLess": "<PERSON>er menos", "viewAll": "Ver todo", "homeTab": "<PERSON><PERSON>o", "collectiblesTab": "Coleccionables", "swapTab": "Intercambiar", "activityTab": "Actividad", "exploreTab": "Explorador", "accountHeaderConnectedInterpolated": "Está conectado a {{origin}}", "accountHeaderConnectedToSite": "Está conectado a este sitio", "accountHeaderCopyToClipboard": "Copiar en el portapapeles", "accountHeaderNotConnected": "No se ha conectado a", "accountHeaderNotConnectedInterpolated": "No se ha conectado a {{origin}}", "accountHeaderNotConnectedToSite": "No se ha conectado a este sitio", "accountWithoutEnoughSolActionButtonCancel": "<PERSON><PERSON><PERSON>", "accountWithoutEnoughSolPrimaryText": "Insuficiente SOL", "accountWithoutEnoughSolSecondaryText": "Una cuenta involucrada en esta transacción no tiene suficientes SOL. La cuenta puede ser suya o de otra persona. Esta transacción se revertirá si se presenta.", "accountSwitcher": "<PERSON>or de cuenta", "addAccountHardwareWalletPrimaryText": "Conectar billetera de hardware", "addAccountHardwareWalletSecondaryText": "Utiliza tu billetera física Ledger", "addAccountHardwareWalletSecondaryTextMobile": "Utilice su billetera {{supportedHardwareWallets}}", "addAccountSeedVaultWalletPrimaryText": "Conectar la Bóveda de Semillas", "addAccountSeedVaultWalletSecondaryText": "Utilice una billetera de <PERSON>", "addAccountImportSeedPhrasePrimaryText": "Importar frase secreta de recuperación", "addAccountImportSeedPhraseSecondaryText": "Importar cuentas de otra billetera", "addAccountImportWalletPrimaryText": "Importar clave privada", "addAccountImportWalletSecondaryText": "Importar una cuenta monocadena", "addAccountImportWalletSolanaSecondaryText": "Importar una clave privada Solana", "addAccountLimitReachedText": "Ha alcanzado el límite de {{accountsCount}} cuentas en Phantom. Elimine las cuentas que no utilice antes de añadir otras.", "addAccountNoSeedAvailableText": "No tiene ninguna frase de semilla disponible. Importe una semilla existente para generar una cuenta.", "addAccountNewWalletPrimaryText": "Crear cuenta nueva", "addAccountNewWalletSecondaryText": "Generar una nueva dirección de billetera", "addAccountNewMultiChainWalletSecondaryText": "Añadir una nueva cuenta multicadena", "addAccountNewSingleChainWalletSecondaryText": "Añadir una nueva cuenta", "addAccountPrimaryText": "Añadir/vincular billetera", "addAccountSecretPhraseLabel": "Frase secreta", "addAccountSeedLabel": "<PERSON><PERSON>", "addAccountSeedIDLabel": "<PERSON> de semilla", "addAccountSecretPhraseDefaultLabel": "Frase secreta {{number}}", "addAccountPrivateKeyDefaultLabel": "Clave privada {{number}}", "addAccountZeroAccountsForSeed": "0 cuentas", "addAccountShowAccountForSeed": "Mostrar 1 cuenta", "addAccountShowAccountsForSeed": "Mostrar {{numOfAccounts}} cuentas", "addAccountHideAccountForSeed": "Ocultar 1 cuenta", "addAccountHideAccountsForSeed": "Ocultar {{numOfAccounts}} cuentas", "addAccountSelectSeedDescription": "Tu nueva cuenta se generará a partir de esta Frase secreta", "addAccountNumAccountsForSeed": "{{numOfAccounts}} cuentas", "addAccountOneAccountsForSeed": "1 cuenta", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON><PERSON> cuenta", "addAccountReadOnly": "<PERSON><PERSON>er<PERSON>", "addAccountReadOnlySecondaryText": "Ra<PERSON><PERSON>r cualquier dirección de billetera pública", "addAccountSolanaAddress": "Dirección de Solana", "addAccountEVMAddress": "Dirección de EVM", "addAccountBitcoinAddress": "Dirección de Bitcoin", "addAccountCreateSeedTitle": "<PERSON><PERSON>r una nueva cuenta", "addAccountCreateSeedExplainer": "¡Su billetera aún no tiene una frase secreta! Para crear una nueva billetera, le generaremos una frase de recuperación. Escríbala y guárdela.", "addAccountSecretPhraseHeader": "Su frase secreta", "addAccountNoSecretPhrases": "No hay frases secretas disponibles", "addAccountImportAccountActionButtonImport": "Importar", "addAccountImportAccountDuplicatePrivateKey": "Esta cuenta ya existe en su billetera", "addAccountImportAccountIncorrectFormat": "Formato incorrecto", "addAccountImportAccountInvalidPrivateKey": "Clave privada no válida", "addAccountImportAccountName": "Nombre", "addAccountImportAccountPrimaryText": "Importar clave privada", "addAccountImportAccountPrivateKey": "Clave privada", "addAccountImportAccountPublicKey": "Dirección o dominio", "addAccountImportAccountPrivateKeyRequired": "Se requiere la clave privada", "addAccountImportAccountNameRequired": "Se requiere el nombre", "addAccountImportAccountPublicKeyRequired": "La dirección pública es obligatoria", "addAccountImportAccountDuplicateAddress": "Esta dirección ya existe en su billetera", "addAddressAddressAlreadyAdded": "La dirección ya está añadida", "addAddressAddressAlreadyExists": "La dirección ya existe", "addAddressAddressInvalid": "La dirección no es válida", "addAddressAddressIsRequired": "Se requiere una dirección", "addAddressAddressPlaceholder": "Dirección", "addAddressLabelIsRequired": "Se requiere una etiqueta", "addAddressLabelPlaceholder": "Etiqueta", "addAddressPrimaryText": "<PERSON><PERSON><PERSON>", "addAddressToast": "Dirección añadida", "createAssociatedTokenAccountCostLabelInterpolated": "Esto costará {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Ya tiene esta cuenta token", "createAssociatedTokenAccountErrorInsufficientFunds": "Fondos insuficientes", "createAssociatedTokenAccountErrorInvalidMint": "Dirección mint no válida", "createAssociatedTokenAccountErrorInvalidName": "Nombre no válido", "createAssociatedTokenAccountErrorInvalidSymbol": "Símbolo no válido", "createAssociatedTokenAccountErrorUnableToCreateMessage": "No hemos podido crear su cuenta token. Inténtelo de nuevo.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Error al crear la cuenta", "createAssociatedTokenAccountErrorUnableToSendMessage": "No hemos podido enviar su transacción.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Error al enviar la transacción", "createAssociatedTokenAccountInputPlaceholderMint": "Dirección Mint", "createAssociatedTokenAccountInputPlaceholderName": "Nombre", "createAssociatedTokenAccountInputPlaceholderSymbol": "Símbolo", "createAssociatedTokenAccountLoadingMessage": "Estamos creando su cuenta token.", "createAssociatedTokenAccountLoadingTitle": "C<PERSON>ndo cuenta token", "createAssociatedTokenAccountPageHeader": "Crear cuenta token", "createAssociatedTokenAccountSuccessMessage": "¡Su cuenta token se ha creado correctamente!", "createAssociatedTokenAccountSuccessTitle": "Cuenta token creada", "createAssociatedTokenAccountViewTransaction": "Ver transacción", "assetDetailRecentActivity": "Actividad reciente", "assetDetailStakeSOL": "Apostar SOL", "assetDetailUnknownToken": "Token desconocido", "assetDetailUnwrapAll": "Unwrap todo", "assetDetailUnwrappingSOL": "Desenvolver SOL", "assetDetailUnwrappingSOLFailed": "Error al desenvolver SOL", "assetDetailViewOnExplorer": "Ver en {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Explorador", "assetDetailSaveToPhotos": "Guardar en Fotos", "assetDetailSaveToPhotosToast": "Guardado en Fotos", "assetDetailPinCollection": "<PERSON><PERSON>", "assetDetailUnpinCollection": "<PERSON><PERSON><PERSON>", "assetDetailHideCollection": "Ocultar co<PERSON>cci<PERSON>", "assetDetailUnhideCollection": "<PERSON><PERSON><PERSON>", "assetDetailTokenNameLabel": "Nombre del token", "assetDetailNetworkLabel": "Red", "assetDetailAddressLabel": "Dirección", "assetDetailPriceLabel": "Precio", "collectibleDetailSetAsAvatar": "E<PERSON>cer como avatar", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar selecci<PERSON>", "collectibleDetailShare": "Compartir coleccionable", "assetDetailTokenAddressCopied": "Dirección copiada", "assetDetailStakingLabel": "Apuestas", "assetDetailAboutLabel": "Acerca de {{fungibleName}}", "assetDetailPriceDetail": "Detalle del precio", "assetDetailHighlights": "Destacados", "assetDetailAllTimeReturn": "Rendimiento de todo el tiempo", "assetDetailAverageCost": "Coste medio", "assetDetailPriceHistoryUnavailable": "Historial de precios no disponible para este token", "assetDetailPriceHistoryInsufficientData": "Historial de precios no disponible para este intervalo de tiempo", "assetDetailPriceDataUnavailable": "Datos de precios no disponibles", "assetDetailPriceHistoryError": "Error al consultar el historial de precios", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1D", "assetDetailTimeFrame24h": "Precio 24h", "assetDetailTimeFrame1W": "1S", "assetDetailTimeFrame1M": "1M", "assetDetailTimeFrameYTD": "AÑO", "assetDetailTimeFrameAll": "TODOS", "sendAssetAmountLabelInterpolated": "Disponible {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "Presupuestos", "fiatRampNewQuote": "Nuevo presupuesto", "assetListSelectToken": "Se<PERSON><PERSON><PERSON><PERSON> token", "assetListSearch": "Buscar...", "assetListUnknownToken": "Token desconocido", "buyFlowHealthWarning": "Algunos de nuestros proveedores de pago están experimentando un gran tráfico. Los depósitos pueden retrasarse varias horas.", "assetVisibilityUnknownToken": "Token desconocido", "buyAssetInterpolated": "Comprar {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "La compra máxima es de {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "La compra mínima es de {{amount}}", "buyNoAssetsAvailable": "No hay activos Ethereum o Polygon disponibles", "buyThirdPartyScreenPaymentMethodSelector": "Pagar con", "buyThirdPartyScreenPaymentMethod": "Elija el método de pago", "buyThirdPartyScreenChoseQuote": "Introduzca el importe válido para el presupuesto", "buyThirdPartyScreenProviders": "<PERSON>veed<PERSON>", "buyThirdPartyScreenPaymentMethodTitle": "Métodos de pago", "buyThirdPartyScreenPaymentMethodEmptyState": "No hay métodos de pago disponibles en su región", "buyThirdPartyScreenPaymentMethodFooter": "Los pagos se realizan a través de socios de la red. Las comisiones pueden variar. Algunos métodos de pago no están disponibles en su región.", "buyThirdPartyScreenProvidersEmptyState": "No hay proveedores disponibles en su región", "buyThirdPartyScreenLoadingQuote": "Cargando presupuesto...", "buyThirdPartyScreenViewQuote": "Ver presupuesto", "gasEstimationErrorWarning": "Ha habido un problema al calcular la comisión de esta transacción. Puede fallar.", "gasEstimationCouldNotFetch": "No pudo recuperar la estimación de gasolina", "networkFeeCouldNotFetch": "No se pudo recuperar la comisión de red", "nativeTokenBalanceErrorWarning": "Ha habido un problema al obtener su saldo de tokens para esta transacción. Puede fallar.", "blocklistOriginCommunityDatabaseInterpolated": "Este sitio ha sido marcado como parte de una <1>base de datos mantenida por la comunidad</1> de estafas y sitios web de suplantación de identidad conocidos. Si cree que este sitio ha sido erróneamente marcado, <3>presente una incidencia</3>.", "blocklistOriginDomainIsBlocked": "¡{{domainName}} está bloqueado!", "blocklistOriginIgnoreWarning": "Ignorar este aviso y llevarme a {{domainName}} de todos modos.", "blocklistOriginSiteIsMalicious": "Phantom cree que este sitio web es malicioso y usarlo resulta inseguro.", "blocklistOriginThisDomain": "este dominio", "blocklistProceedAnyway": "Ignorar la advertencia, proceder de todos modos", "maliciousTransactionWarning": "Phantom cree que esta transacción es fraudulenta y no es segura para firmarla. Hemos desactivado la posibilidad de firmarla para protegerle a usted y a sus fondos.", "maliciousTransactionWarningIgnoreWarning": "Ignorar la advertencia, proceder de todos modos", "maliciousTransactionWarningTitle": "¡Transacción marcada!", "maliciousRequestBlockedTitle": "Solicitud bloqueada", "maliciousRequestWarning": "Este sitio web se ha marcado como malicioso. Puede estar intentando robar sus fondos o engañarle para que confirme una solicitud engañosa.", "maliciousSignatureRequestBlocked": "<PERSON>r su seguridad, <PERSON> ha bloqueado esta solicitud.", "maliciousRequestBlocked": "<PERSON>r su seguridad, <PERSON> ha bloqueado esta solicitud.", "maliciousRequestFrictionDescription": "Proceder no es seguro, por lo que Phantom bloqueó esta solicitud. Debería cerrar este diálogo.", "maliciousRequestAcknowledge": "Entiendo que podría perder todos mis fondos al utilizar este sitio web.", "maliciousRequestAreYouSure": "¿Seguro?", "siwErrorPopupTitle": "Solicitud de firma no válida", "siwParseErrorDescription": "La solicitud de firma de la aplicación no puede mostrarse debido a un formato no válido.", "siwVerificationErrorDescription": "Hubo 1 o más errores con la solicitud de firma de mensajes. Por su seguridad, asegúrese de que está utilizando la aplicación correcta e inténtelo de nuevo.", "siwErrorPagination": "{{n}} de {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Advertencia: la dirección de la aplicación no coincide con la dirección proporcionada para la firma.", "siwErrorMessage_DOMAIN_MISMATCH": "Advertencia: el dominio de la aplicación no coincide con el dominio proporcionado para la verificación.", "siwErrorMessage_URI_MISMATCH": "Advertencia: el nombre de host URI no coincide con el dominio.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Advertencia: el ID de la cadena no coincide con el ID de la cadena proporcionado para la verificación.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Advertencia: la fecha de emisión del mensaje está demasiado lejos en el pasado.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Advertencia: la fecha de emisión del mensaje está demasiado lejos en el futuro.", "siwErrorMessage_EXPIRED": "Advertencia: el mensaje ha caducado.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Advertencia: el mensaje expira antes de su emisión.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Advertencia: el mensaje expirará antes de ser válido.", "siwErrorShowErrorDetails": "Mostrar detalles del error", "siwErrorHideErrorDetails": "Ocultar detalles del error", "siwErrorIgnoreWarning": "Ignorar la advertencia, proceder de todos modos", "siwsTitle": "Solicitud de inicio de sesión", "siwsPermissions": "<PERSON><PERSON><PERSON>", "siwsAgreement": "Men<PERSON><PERSON>", "siwsAdvancedDetails": "<PERSON><PERSON><PERSON>", "siwsAlternateStatement": "{{domain}} quiere que inicie sesión con su cuenta de Solana:\n{{address}}", "siwsFieldLable_domain": "<PERSON>inio", "siwsFieldLable_address": "Dirección", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Versión", "siwsFieldLable_chainId": "ID de cadena", "siwsFieldLable_nonce": "<PERSON><PERSON>", "siwsFieldLable_issuedAt": "Emitido a las", "siwsFieldLable_expirationTime": "Caduca a las", "siwsFieldLable_requestId": "ID de solicitud", "siwsFieldLable_resources": "Recursos", "siwsVerificationErrorDescription": "Esta solicitud de registro no es válida. Esto significa que el sitio no es seguro o que su desarrollador cometió un error al enviar la solicitud.", "siwsErrorNumIssues": "{{n}} problemas", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Este ID de cadena no coincide con la red en la que se encuentra.", "siwsErrorMessage_DOMAIN_MISMATCH": "Este dominio no es en el que se está registrando.", "siwsErrorMessage_URI_MISMATCH": "Esta URI no es en la que se está registrando.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "La fecha de emisión del mensaje está demasiado lejos en el pasado.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "La fecha de emisión del mensaje está demasiado lejos en el futuro.", "siwsErrorMessage_EXPIRED": "El mensaje ha caducado.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "El mensaje caduca antes de su emisión.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "El mensaje caducará antes de ser válido.", "changeLockTimerPrimaryText": "Temporizador de bloqueo automático", "changeLockTimerSecondaryText": "¿Cuánto tiempo hay que esperar para bloquear la billetera después de que esté inactiva?", "changeLockTimerToast": "Temporizador de bloqueo automático actualizado", "changePasswordConfirmNewPassword": "Confirmar nueva contraseña", "changePasswordCurrentPassword": "Contraseña actual", "changePasswordErrorIncorrectCurrentPassword": "Contraseña actual incorrecta", "changePasswordErrorGeneric": "Algo ha ido mal, inténtelo de nuevo más tarde", "changePasswordNewPassword": "Nueva contraseña", "changePasswordPrimaryText": "Cambiar contraseña", "changePasswordToast": "Contraseña actualizada", "collectionsSpamCollections": "Colecciones de spam", "collectionsHiddenCollections": "Colecciones ocultas", "collectiblesReportAsSpam": "Denunciar como spam", "collectiblesReportAsSpamAndHide": "Denunciar como spam y ocultar", "collectiblesReportAsNotSpam": "Marcar como no spam", "collectiblesReportAsNotSpamAndUnhide": "Cancelar ocultar y marcar como no spam", "collectiblesReportNotSpam": "No spam", "collectionsManageCollectibles": "Gestionar lista de coleccionables", "collectibleDetailDescription": "Descripción", "collectibleDetailProperties": "Propiedades", "collectibleDetailOrdinalInfo": "Información ordinal", "collectibleDetailRareSatsInfo": "Información sobre Rare Sats", "collectibleDetailSatsInUtxo": "Sats en UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Número de Sat", "collectibleDetailSatName": "Nombre del Sat", "collectibleDetailInscriptionId": "ID de inscripción", "collectibleDetailInscriptionNumber": "Número de inscripción", "collectibleDetailStandard": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailCreated": "<PERSON><PERSON><PERSON>", "collectibleDetailViewOnExplorer": "Ver en {{explorer}}", "collectibleDetailList": "Listar", "collectibleDetailSellNow": "Vender por {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Liberar Bitcoin sobrantes", "collectibleDetailUtxoSplitterCtaSubtitle": "Tiene {{value}} de BTC para desbloquear", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Para proteger sus fondos, impedimos el envío de BTC en UTXOs con Rare Sats. Utilice el divisor UTXO de Magic Eden para liberar {{value}} de BTC de sus Rare Sats.", "collectibleDetailUtxoSplitterModalCtaButton": "Utilizar el divisor de UTXO", "collectibleDetailEasilyAccept": "Acepte la oferta más alta", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sats", "collectibleDetailSpamOverlayDescription": "Este coleccionable se ocultó porque Phantom cree que es spam.", "collectibleDetailSpamOverlayReveal": "Mostrar coleccionable", "collectibleBurnTermsOfService": "Entiendo que esto no se puede deshacer", "collectibleBurnTitleWithCount_one": "<PERSON><PERSON> token", "collectibleBurnTitleWithCount_other": "Quemar tokens", "collectibleBurnDescriptionWithCount_one": "Esta acción destruirá y eliminará permanentemente este token de su cartera.", "collectibleBurnDescriptionWithCount_other": "Esta acción destruirá y eliminará permanentemente estos tokens de su cartera.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Tokens", "collectibleBurnCta": "<PERSON><PERSON>", "collectibleBurnRebate": "Reembolso", "collectibleBurnRebateTooltip": "Al quemar este token, se depositará automáticamente una pequeña cantidad de SOL en su billetera.", "collectibleBurnNetworkFee": "Comisión de la red", "collectibleBurnNetworkFeeTooltip": "Importe requerido por la red Solana para tramitar la transacción", "unwrapButtonSwapTo": "Intercambiar a {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Retirar de {{withdrawalSource}} para {{chainSymbol}}", "unwrapModalEstimatedTime": "Tiempo estimado", "unwrapModalNetwork": "Red", "unwrapModalNetworkFee": "Comisión de la red", "unwrapModalTitle": "Resumen", "unsupportedChain": "Cadena no admitida", "unsupportedChainDescription": "Parece que no admitimos {{action}} para la red {{chainName}}.", "networkFeesTooltipLabel": "<PERSON><PERSON><PERSON>s de red {{chainName}}", "networkFeesTooltipDescription": "Las tarifas de {{chainName}} varían en función de varios factores. Puedes personalizarlas para que tu transacción sea más rápida (más cara) o más lenta (más barata).", "burnStatusErrorTitleWithCount_one": "No se ha podido quemar el token", "burnStatusErrorTitleWithCount_other": "Los tokens no se han quemado", "burnStatusSuccessTitleWithCount_one": "¡Token quemado!", "burnStatusSuccessTitleWithCount_other": "¡Tokens quemados!", "burnStatusLoadingTitleWithCount_one": "Quemando token...", "burnStatusLoadingTitleWithCount_other": "Quemando tokens...", "burnStatusErrorMessageWithCount_one": "No se ha podido quemar este token. Inténtelo de nuevo más tarde.", "burnStatusErrorMessageWithCount_other": "Estos tokens no se han podido quemar. Inténtelo de nuevo más tarde.", "burnStatusSuccessMessageWithCount_one": "Este token ha sido destruido permanentemente y se ha depositado {{rebateAmount}} SOL en su billetera.", "burnStatusSuccessMessageWithCount_other": "Estos tokens han sido destruidos permanentemente y se ha depositado {{rebateAmount}} SOL en su billetera.", "burnStatusLoadingMessageWithCount_one": "Este token está siendo destruido permanentemente y se despositará {{rebateAmount}} SOL en su billetera.", "burnStatusLoadingMessageWithCount_other": "Estos tokens están siendo destruidos permanentemente y se despositará {{rebateAmount}} SOL en su billetera.", "burnStatusViewTransactionText": "Ver transacción", "collectibleDisplayLoading": "Cargando...", "collectiblesNoCollectibles": "No coleccionables", "collectiblesPrimaryText": "Sus coleccionables", "collectiblesReceiveCollectible": "Recibir coleccionable", "collectiblesUnknownCollection": "Colección desconocida", "collectiblesUnknownCollectible": "Coleccionable desconocido", "collectiblesUniqueHolders": "Titulares únicos", "collectiblesSupply": "Suministro", "collectiblesUnknownTokens": "Tokens desconocidos", "collectiblesNrOfListed": "{{ nrOfListed }} listados", "collectiblesListed": "Listado", "collectiblesMintCollectible": "<PERSON><PERSON><PERSON><PERSON> coleccionable", "collectiblesYouMint": "Usted acuña", "collectiblesMintCost": "Coste de acuñación", "collectiblesMintFail": "<PERSON><PERSON>r al acuñar", "collectiblesMintFailMessage": "Ha habido un problema al acuñar su coleccionable. Inténtelo de nuevo.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "Acuñando...", "collectiblesMintingMessage": "Su coleccionable se está acuñando", "collectiblesMintShareSubject": "Echa un vistazo a esto", "collectiblesMintShareMessage": "¡He acuñado esto en @phantom!", "collectiblesMintSuccess": "Acuñado con éxito", "collectiblesMintSuccessMessage": "Su coleccionable ya está acuñado", "collectiblesMintSuccessQuestMessage": "Ha cumplido los requisitos de una Misión de Phantom. Pulse Reclamar su recompensa para obtener su coleccionable gratuito.", "collectiblesMintRequired": "Obligatorio", "collectiblesMintMaxLengthErrorMessage": "Longitud máxima superada", "collectiblesMintSafelyDismiss": "<PERSON><PERSON>e cerrar con seguridad esta ventana.", "collectiblesTrimmed": "Hemos llegado al límite del número de coleccionables que se pueden mostrar en este momento.", "collectiblesNonTransferable": "No transferible", "collectiblesNonTransferableYes": "Sí", "collectiblesSellOfferDetails": "Detalles de la oferta", "collectiblesSellYouSell": "Usted vende", "collectiblesSellGotIt": "Entendido", "collectiblesSellYouReceive": "Usted recibe", "collectiblesSellOffer": "<PERSON><PERSON><PERSON>", "collectiblesSoldCollectible": "Coleccionable vendido", "collectiblesSellMarketplace": "<PERSON><PERSON><PERSON>", "collectiblesSellCollectionFloor": "Precio base de la colección", "collectiblesSellDifferenceFromFloor": "Diferencia respecto al suelo", "collectiblesSellLastSalePrice": "Última oferta", "collectiblesSellEstimatedFees": "Comisiones estimadas", "collectiblesSellEstimatedProfitAndLoss": "Ganancias/pérdidas estimadas", "collectiblesSellViewOnMarketplace": "Ver en {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "El precio «Comprar ahora» más bajo de la colección en múltiples mercados.", "collectiblesSellProfitLossTooltip": "La ganancia/pérdida estimada se calcula en función del último precio de venta y del importe de la oferta menos las comisiones.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Regalías ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Comisión del mercado ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Comisión del mercado", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Red {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "El presupuesto incluye una comisión del {{phantomFeePercentage}} de Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "El presupuesto incluye regañçoas, comisión de la red, comisión de mercado y una comisión del {{phantomFeePercentage}} de Phantom", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "El presupuesto incluye regalías, comisión de red y una comisión de lmercado", "collectiblesSellTransactionFeeTooltipTitle": "Comisión por transacción", "collectiblesSellStatusLoadingTitle": "Aceptando oferta...", "collectiblesSellStatusLoadingIsSellingFor": "se vende por", "collectiblesSellStatusSuccessTitle": "¡{{collectibleName}} vendido!", "collectiblesSellStatusSuccessWasSold": "se vendió con éxito por", "collectiblesSellStatusErrorTitle": "Algo salió mal", "collectiblesSellStatusErrorSubtitle": "Hubo un problema al intentar vender", "collectiblesSellStatusViewTransaction": "Ver transacción", "collectiblesSellInsufficientFundsTitle": "Fondos insuficientes", "collectiblesSellInsufficientFundsSubtitle": "No hemos podido aceptar una oferta por este coleccionable porque no había fondos suficientes para pagar la cuota de red.", "collectiblesSellRecentlyTransferedNFTTitle": "Transferido recientemente", "collectiblesSellRecentlyTransferedNFTSubtitle": "Debe esperar 1 hora para aceptar ofertas después de una transferencia.", "collectiblesApproveCollection": "{{collectionName}} aprobado", "collectiblesSellNotAvailableAnymoreTitle": "Oferta no disponible", "collectiblesSellNotAvailableAnymoreSubtitle": "La oferta ya no está disponible. Cancele esta oferta e inténtelo de nuevo", "collectiblesSellFlaggedTokenTitle": "El coleccionable está marcado", "collectiblesSellFlaggedTokenSubtitle": "El coleccionable no se puede intercambiar, puede ser por múltiples razones como haberse denunciado como robado o apostado sin bloqueo", "collectiblesListOnMagicEden": "Anunciar en Magic Eden", "collectiblesListPrice": "Precio del anuncio", "collectiblesUseFloor": "<PERSON><PERSON> suelo", "collectiblesFloorPrice": "<PERSON><PERSON>", "collectiblesLastSalePrice": "Último precio de venta", "collectiblesTotalReturn": "Rendimiento total", "collectiblesOriginalPurchasePrice": "Precio de compra original", "collectiblesMagicEdenFee": "Comisión de Magic Eden", "collectiblesArtistRoyalties": "Regalías de artista", "collectiblesListNowButton": "Poner en venta", "collectiblesListAnywayButton": "Listar de todos modos", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "<PERSON>er <PERSON><PERSON>", "collectiblesListingViewTransaction": "Ver transacción", "collectiblesRemoveListing": "Eliminar listado", "collectiblesEditListing": "<PERSON>ar listado", "collectiblesEditListPrice": "Editar precio de venta", "collectiblesListPriceTooltip": "El Precio de venta es el precio al cual se vende un artículo. Los vendedores suelen fijar el Precio de venta para que sea igual o superior al Precio suelo.", "collectiblesFloorPriceTooltip": "El precio suelo es el precio de venta activo más bajo de un artículo de esta colección.", "collectiblesOriginalPurchasePriceTooltip": "Originalmente compró este artículo por esta cantidad.", "collectiblesPurchasedForSol": "Comprado por {{lastPurchasePrice}} SOL", "collectiblesUnableToLoadListings": "No se pueden cargar los listados", "collectiblesUnableToLoadListingsFrom": "No se pueden cargar los listados de {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "Sus listados y activos están seguros pero no hemos podido cargarlos desde {{marketplace}} en este momento. Inténtelo de nuevo más tarde.", "collectiblesBelowFloorPrice": "Por debajo del precio suelo", "collectiblesBelowFloorPriceMessage": "¿Seguro que quieres listar tu NFT por debajo del precio suelo?", "collectiblesMinimumListingPrice": "El precio mínimo es de 0,01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden cobra una comisión por las transacciones realizadas.", "collectiblesArtistRoyaltiesTooltip": "El creador de esta colección recibe un % de derechos de autor por cada venta realizada.", "collectibleScreenCollectionLabel": "Colección", "collectibleScreenPhotosPermissionTitle": "Per<PERSON>o <PERSON>oto<PERSON>", "collectibleScreenPhotosPermissionMessage": "Necesitamos su permiso para acceder a sus fotos. Vaya a Ajustes y actualice sus permisos.", "collectibleScreenPhotosPermissionOpenSettings": "<PERSON><PERSON><PERSON>", "listStatusErrorTitle": "Listado fallido", "editListStatusErrorTitle": "No se puede actualizar", "removeListStatusErrorTitle": "Eliminación del listado fallida", "listStatusSuccessTitle": "¡Listado creado!", "editListingStatusSuccessTitle": "¡Listado actualizado!", "removeListStatusSuccessTitle": "Listado eliminado de Magic Eden", "listStatusLoadingTitle": "Crear un listado...", "editListingStatusLoadingTitle": "Actualización del listado...", "removeListStatusLoadingTitle": "Eliminación del listado...", "listStatusErrorMessage": "No se pudo añadir {{name}} al catálogo de Magic Eden", "removeListStatusErrorMessage": "No se pudo eliminar {{name}} del catálogo de Magic Eden", "listStatusSuccessMessage": "{{name}} ahora está a la venta en Magic Eden por {{listCollectiblePrice}} SOL", "editListingStatusSuccessMessage": "{{name}} está ahora actualizado en Magic Eden por {{editListCollectiblePrice}} SOL", "removeListStatusSuccessMessage": "{{name}} se eliminó con éxito de Magic Eden", "listStatusLoadingMessage": "Añadiendo {{name}} al catálogo de Magic Eden por {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "Actualizando {{name}} en Magic Eden por {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Eliminando a {{name}} de Magic Eden. Esto puede llevar un tiempo.", "listStatusLoadingSafelyDismiss": "<PERSON><PERSON>e cerrar con seguridad esta ventana.", "listStatusViewOnMagicEden": "Ver en Magic Eden", "listStatusViewOnMarketplace": "Ver en {{marketplace}}", "listStatusLoadingDismiss": "<PERSON><PERSON><PERSON>", "listStatusViewTransaction": "Ver transacción", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Conecta tu billetera y asegúrate de que está desbloqueada. Una vez que la hayamos detectado, podrás elegir qué dirección quieres utilizar.", "connectHardwareFailedPrimaryText": "E<PERSON>r de conexión", "connectHardwareFailedSecondaryText": "Conecte su billetera y asegúrese de que está desbloqueada. Una vez que la descubramos podrá elegir qué dirección utilizar.", "connectHardwareFinishPrimaryText": "¡Cuenta añadida!", "connectHardwareFinishSecondaryText": "Ahora puede acceder a su billetera Ledger Nano desde Phantom. Vuelva a la extensión.", "connectHardwareNeedsPermissionPrimaryText": "Conectar una nueva billetera", "connectHardwareNeedsPermissionSecondaryText": "Pulse el botón siguiente para iniciar el proceso de conexión.", "connectHardwareSearchingPrimaryText": "Buscando la billetera...", "connectHardwareSearchingSecondaryText": "Conecte su billetera, asegúrese de que está desbloqueada y de que tiene permisos aprobados en su navegador.", "connectHardwarePermissionDeniedPrimary": "<PERSON><PERSON><PERSON> den<PERSON>ado", "connectHardwarePermissionDeniedSecondary": "Conceda permiso a Phantom para conectarse a su dispositivo Ledger", "connectHardwarePermissionUnableToConnect": "No es posible conectarse", "connectHardwarePermissionUnableToConnectDescription": "No hemos podido conectar con su dispositivo Ledger. Puede que necesitemos más permisos.", "connectHardwareSelectAddressAllAddressesImported": "Todas las direcciones importadas", "connectHardwareSelectAddressDerivationPath": "Ruta de derivación", "connectHardwareSelectAddressSearching": "Buscando...", "connectHardwareSelectAddressSelectWalletAddress": "Seleccione la dirección de la billetera", "connectHardwareSelectAddressWalletAddress": "Dirección de la billetera", "connectHardwareWaitingForApplicationSecondaryText": "Conecte su billetera y asegúrese de que está desbloqueada.", "connectHardwareWaitingForPermissionPrimaryText": "Se requiere permiso", "connectHardwareWaitingForPermissionSecondaryText": "Conecte su billetera, asegúrese de que está desbloqueada y de que tiene permisos aprobados en su navegador.", "connectHardwareAddAccountButton": "<PERSON><PERSON><PERSON> cue<PERSON>", "connectHardwareLedger": "Conecta tu Ledger", "connectHardwareStartConnection": "Haga clic en el botón de abajo para empezar a conectar su billetera física Ledger", "connectHardwarePairSuccessPrimary": "{{productName}} conectado", "connectHardwarePairSuccessSecondary": "Ha conectado correctamente su {{productName}}.", "connectHardwareSelectChains": "Seleccione las cadenas que desea conectar", "connectHardwareSearching": "Buscando...", "connectHardwareMakeSureConnected": "Conecte y desbloquee su billetera de hardware. Apruebe los permisos pertinentes del navegador.", "connectHardwareOpenAppDescription": "Desbloquee su billetera de hardware", "connectHardwareConnecting": "Conectando...", "connectHardwareConnectingDescription": "Estamos conectando con su dispositivo Ledger.", "connectHardwareConnectingAccounts": "Conectando sus cuentas...", "connectHardwareDiscoveringAccounts": "Buscando cuentas...", "connectHardwareDiscoveringAccountsDescription": "Buscamos actividad en sus cuentas.", "connectHardwareErrorLedgerLocked": "Ledger está bloqueado", "connectHardwareErrorLedgerLockedDescription": "Asegúrese de que su dispositivo Ledger está desbloqueado y vuelva a intentarlo.", "connectHardwareErrorLedgerGeneric": "Algo salió mal", "connectHardwareErrorLedgerGenericDescription": "No se han podido encontrar cuentas. Asegúrese de que su dispositivo Ledger está desbloqueado y vuelva a intentarlo.", "connectHardwareErrorLedgerPhantomLocked": "Vuelva a abrir Phantom e intente conectar su hardware de nuevo.", "connectHardwareFindingAccountsWithActivity": "Buscando cuentas {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "Encontrando cuentas {{chainName1}} o {{chainName2}}...", "connectHardwareFoundAccountsWithActivity": "Hemos encontrado {{numOfAccounts}} cuentas con actividad en su Ledger.", "connectHardwareFoundAccountsWithActivitySingular": "Hemos encontrado 1 cuenta con actividad en su Ledger.", "connectHardwareFoundSomeAccounts": "Hemos encontrado algunas cuentas en su dispositivo Ledger.", "connectHardwareViewAccounts": "Ver cuentas", "connectHardwareConnectAccounts": "Cuentas conectadas", "connectHardwareSelectAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON> cuenta<PERSON>", "connectHardwareChooseAccountsToConnect": "Elija las cuentas de billetera que desea conectar.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} cue<PERSON><PERSON>", "connectHardwareAccountsStepOfSteps": "Paso {{stepNum}} de {{totalSteps}}", "connectHardwareMobile": "Conectar Ledger", "connectHardwareMobileTitle": "Conecte su billetera de hardware Ledger", "connectHardwareMobileEnableBluetooth": "Activar Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Permitir el uso de Bluetooth para conectarse", "connectHardwareMobileEnableBluetoothSettings": "Vaya a Ajustes para permitir que Phantom utilice los permisos de Localización y Dispositivos Cercanos.", "connectHardwareMobilePairWithDevice": "Emparéjelo con su dispositivo Ledger", "connectHardwareMobilePairWithDeviceDescription": "Mantenga su dispositivo cerca para obtener la mejor señal", "connectHardwareMobileConnectAccounts": "Conectar cuentas", "connectHardwareMobileConnectAccountsDescription": "Buscaremos actividad en las cuentas que haya podido utilizar", "connectHardwareMobileConnectLedgerDevice": "Conecte su dispositivo Ledger", "connectHardwareMobileLookingForDevices": "Buscando dispositivos cercanos...", "connectHardwareMobileLookingForDevicesDescription": "Conecte su dispositivo Ledger y asegúrese de que está desbloqueado.", "connectHardwareMobileFoundDeviceSingular": "Hemos encontrado 1 dispositivo Ledger", "connectHardwareMobileFoundDevices": "Hemos encontrado {{numDevicesFound}} dispositivos Ledger", "connectHardwareMobileFoundDevicesDescription": "Seleccione a continuación un dispositivo Ledger para iniciar el emparejamiento.", "connectHardwareMobilePairingWith": "Emparejando con {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Siga las instrucciones de su dispositivo Ledger durante el emparejamiento.", "connectHardwareMobilePairingFailed": "Emparejamiento fallido", "connectHardwareMobilePairingFailedDescription": "No se puede emparejar con {{deviceName}}. Asegúrese de que su dispositivo está desbloqueado.", "connectHardwareMobilePairingSuccessful": "Emparejamiento exitoso", "connectHardwareMobilePairingSuccessfulDescription": "Ha emparejado y conectado correctamente su dispositivo Ledger.", "connectHardwareMobileOpenAppSingleChain": "Abra la aplicación {{chainName}} en su Ledger", "connectHardwareMobileOpenAppDualChain": "Abra la aplicación {{chainName1}} o {{chainName2}} en su Ledger", "connectHardwareMobileOpenAppDescription": "Asegúrese de que su dispositivo está desbloqueado.", "connectHardwareMobileStillCantFindDevice": "¿Sigue sin encontrar su dispositivo?", "connectHardwareMobileLostConnection": "Conexión perdida", "connectHardwareMobileLostConnectionDescription": "Hemos perdido la conexión con {{deviceName}}. Asegúrese de que su dispositivo está desbloqueado y vuelva a intentarlo.", "connectHardwareMobileGenericLedgerDevice": "Dispositivo Ledger", "connectHardwareMobileConnectDeviceSigning": "Conecte su {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Desbloquee su dispositivo Ledger y manténgalo cerca.", "connectHardwareMobileBluetoothDisabled": "El Bluetooth está desactivado", "connectHardwareMobileBluetoothDisabledDescription": "Active su Bluetooth y asegúrese de que su dispositivo Ledger está desbloqueado.", "connectHardwareMobileLearnMore": "Más información", "connectHardwareMobileBlindSigningDisabled": "La firma digital ciega está deshabilitada", "connectHardwareMobileBlindSigningDisabledDescription": "Asegúrese de que tiene la firma digital ciega activada en su dispositivo.", "connectHardwareMobileConfirmSingleChain": "Debe confirmar la transacción en su hardware de billetera. Asegúrese de que está desbloqueada.", "metamaskExplainerBottomSheetHeader": "Este sitio funciona con Phantom", "metamaskExplainerBottomSheetSubheader": "Seleccione MetaMask en el diálogo de conectar billetera para proceder.", "metamaskExplainerBottomSheetDontShowAgain": "No volver a mostrar", "ledgerStatusNotConnected": "Ledger no está conectado", "ledgerStatusConnectedInterpolated": "{{productName}} está conectado", "connectionClusterInterpolated": "Actualmente se encuentra en {{cluster}}", "connectionClusterTestnetMode": "Actualmente se encuentra en modo Testnet", "featureNotSupportedOnLocalNet": "Esta característica no es compatible cuando Solana Localnet está activada.", "readOnlyAccountBannerWarning": "Está observando esta cuenta", "depositAddress": "Dirección de recepción", "depositAddressChainInterpolated": "Su dirección {{chain}}", "depositAssetDepositInterpolated": "Recibir {{tokenSymbol}}", "depositAssetSecondaryText": "Esta dirección se puede utilizar sólo para recibir tokens compatibles.", "depositAssetTextInterpolated": "Utilice esta dirección para recibir tokens y coleccionables en <1>{{network}}</1>.", "depositAssetTransferFromExchange": "Transferencia de intercambio", "depositAssetShareAddress": "Compartir direcci<PERSON>", "depositAssetBuyOrDeposit": "Comprar o transferir", "depositAssetBuyOrDepositDesc": "Añada fondos a su billetera para empezar", "depositAssetTransfer": "Transferir", "editAddressAddressAlreadyAdded": "La dirección ya está añadida", "editAddressAddressAlreadyExists": "La dirección ya existe", "editAddressAddressIsRequired": "Se requiere una dirección", "editAddressPrimaryText": "<PERSON><PERSON>", "editAddressRemove": "Eliminar de la libreta de direcciones", "editAddressToast": "Dirección actualizada", "removeSavedAddressToast": "Dirección eliminada", "exportSecretErrorGeneric": "Algo ha ido mal, inténtelo de nuevo más tarde", "exportSecretErrorIncorrectPassword": "Contrase<PERSON>", "exportSecretPassword": "Contraseña", "exportSecretPrivateKey": "clave privada", "exportSecretSecretPhrase": "frase secreta", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "frase secreta de recuperación", "exportSecretSelectYourAccount": "Seleccione su cuenta", "exportSecretShowPrivateKey": "Mostrar clave privada", "exportSecretShowSecretRecoveryPhrase": "Mostrar frase secreta de recuperación", "exportSecretShowSecret": "Mostrar {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "¡<1>No</1> comparta su {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "Si alguien tuviera su {{secretNameText}} tendría el control total de su billetera.", "exportSecretOnlyWay": "Su {{secretNameText}} es la única manera de recuperar su cartera", "exportSecretDoNotShow": "No deje que nadie vea su {{secretNameText}}", "exportSecretWillNotShare": "No compartiré mi {{secretNameText}} con nadie, ni siquiera con Phantom.", "exportSecretNeverShare": "Nunca comparta su {{secretNameText}} con nadie", "exportSecretYourPrivateKey": "Su clave privada", "exportSecretYourSecretRecoveryPhrase": "Su frase secreta de recuperación", "exportSecretResetPin": "Restablecer su PIN", "fullPageHeaderBeta": "Beta", "fullPageHeaderHelp": "<PERSON><PERSON><PERSON>", "gasUpTo": "Máx. {{ amount }}", "timeDescription1hour": "Alrededor de 1 hora", "timeDescription30minutes": "Unos 30 minutos", "timeDescription10minutes": "Unos 10 minutos", "timeDescription2minutes": "Unos 2 minutos", "timeDescription30seconds": "Unos 30 segundos", "timeDescription15seconds": "Unos 15 segundos", "timeDescription10seconds": "Unos 10 segundos", "timeDescription5seconds": "Unos 5 segundos", "timeDescriptionAbbrev1hour": "1 hr", "timeDescriptionAbbrev30minutes": "30 min", "timeDescriptionAbbrev10minutes": "10 min", "timeDescriptionAbbrev2minutes": "2 min", "timeDescriptionAbbrev30seconds": "30 s", "timeDescriptionAbbrev15seconds": "15 s", "timeDescriptionAbbrev10seconds": "10 s", "timeDescriptionAbbrev5seconds": "5 s", "gasSlow": "<PERSON><PERSON>", "gasAverage": "Promedio", "gasFast": "<PERSON><PERSON><PERSON><PERSON>", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "Inténtelo de nuevo", "homeErrorDescription": "Hubo un error al intentar recuperar sus activos. Actualice e inténtelo de nuevo", "homeErrorTitle": "Error al obtener los activos", "homeManageTokenList": "Gestionar la lista de tokens", "interstitialDismissUnderstood": "Entendido", "interstitialBaseWelcomeTitle": "¡Phantom ya es compatible con Base!", "interstitialBaseWelcomeItemTitle_1": "Env<PERSON>e, reciba y compre tokens", "interstitialBaseWelcomeItemTitle_2": "Explore el ecosistema Base", "interstitialBaseWelcomeItemTitle_3": "Se<PERSON>ro y protegido", "interstitialBaseWelcomeItemDescription_1": "Transfiera y compre USDC y ETH en Base utilizando {{paymentMethod}}, tarjetas o Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Utilice Phantom con todas sus aplicaciones DeFi y NFT favoritas.", "interstitialBaseWelcomeItemDescription_3": "Manténgase seguro con el soporte de Ledger, el filtrado de spam y la simulación de transacciones.", "privacyPolicyChangedInterpolated": "Nuestra política de privacidad ha cambiado. <1>Más información</1>", "bitcoinAddressTypesBodyTitle": "Tipos de dirección Bitcoin", "bitcoinAddressTypesFeature1Title": "Acerca de las direcciones Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom admite Segwit nativo y Taproot, cada uno con su propio saldo. Puede enviar BTC u ordinales con cualquiera de los dos tipos de dirección.", "bitcoinAddressTypesFeature2Title": "Segwit nativo", "bitcoinAddressTypesFeature2Subtitle": "La dirección BTC por defecto en Phantom. Más antigua que Taproot pero compatible con todas las billeteras y servicios de intercambio.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Mejor para ordinales y BRC-20s, con las comisiones más baratas. Ajuste las direcciones en Preferencias -> Dirección Bitcoin preferida.", "headerTitleInfo": "Información", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Esta es su dirección <1>{{addressType}}</1>.", "invalidChecksumTitle": "¡Hemos mejorado su frase secreta!", "invalidChecksumFeature1ExportPhrase": "Exporte su nueva Frase Secreta", "invalidChecksumFeature1ExportPhraseDescription": "Haga una copia de seguridad de su nueva frase secreta junto con las claves privadas de sus antiguas cuentas.", "invalidChecksumFeature2FundsAreSafe": "Sus fondos están seguros y protegidos", "invalidChecksumFeature2FundsAreSafeDescription": "Esta mejora ha sido automatizada. Nadie en Phantom conoce su frase secreta ni tiene acceso a sus fondos.", "invalidChecksumFeature3LearnMore": "Más información", "invalidChecksumFeature3LearnMoreDescription": "Usted tenía una frase incompatible con la mayoría de las billeteras. Lea <1>este artículo de ayuda</1> para saber más sobre esto.", "invalidChecksumBackUpSecretPhrase": "Copia de seguridad de la frase secreta", "migrationFailureTitle": "Algo ha ido mal al migrar su cuenta", "migrationFailureFeature1": "Exporte su frase secreta", "migrationFailureFeature1Description": "Haga una copia de seguridad de su frase secreta antes de incorporarse.", "migrationFailureFeature2": "Incorporarse a Phantom", "migrationFailureFeature2Description": "Tendrá que volver a conectarse a Phantom para ver su cuenta.", "migrationFailureFeature3": "Más información", "migrationFailureFeature3Description": "Lea <1>este artículo de ayuda</1> para obtener más información al respecto.", "migrationFailureContinueToOnboarding": "Continuar con la incorporación", "migrationFailureUnableToFetchMnemonic": "No hemos podido cargar su frase secreta", "migrationFailureUnableToFetchMnemonicDescription": "Póngase en contacto con el servicio de asistencia y descargue los registros de la aplicación para depurarla", "migrationFailureContactSupport": "Póngase en contacto con el servicio de asistencia", "ledgerActionConfirm": "Confirmar en su Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Firma digital ciega deshabilitada", "ledgerActionErrorBlindSignDisabledSecondaryText": "Asegúrese de que la firma digital ciega está habilitada en su dispositivo de hardware y luego vuelva a intentar la acción", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Dispositivo de hardware desconectado durante el funcionamiento", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Cierre la extensión Phantom y vuelva a intentar la acción", "ledgerActionErrorDeviceLockedPrimaryText": "Dispositivo de hardware bloqueado", "ledgerActionErrorDeviceLockedSecondaryText": "Desbloquee su dispositivo de hardware y vuelva a intentar la acción", "ledgerActionErrorHeader": "Error de acción de Ledger", "ledgerActionErrorUserRejectionPrimaryText": "Transacción rechazada por el usuario", "ledgerActionErrorUserRejectionSecondaryText": "La acción fue rechazada en el dispositivo de hardware por el usuario", "ledgerActionNeedPermission": "Se requiere permiso", "ledgerActionNeedToConfirm": "Debe confirmar la transacción en su billetera de hardware. Asegúrese de que está desbloqueada, en la aplicación {{chainType}}.", "ledgerActionNeedToConfirmMany": "Tendrá que confirmar {{numberOfTransactions}} transacciones en su billetera de hardware. Asegúrese de que está desbloqueada, en la aplicación {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Tiene que confirmar la transacción en su billetera de hardware. Asegúrese de que está desbloqueada, en la aplicación {{chainType}}, y de que la firma digital ciega está habilitada.", "ledgerActionNeedToConfirmBlindMany": "Tendrá que confirmar {{numberOfTransactions}} transacciones en su billetera de hardware. Asegúrese de que está desbloqueada, en la aplicación {{chainType}}, y de que la firma digital ciega está habilitada.", "ledgerActionPleaseConnect": "Vincule su Ledger <PERSON>", "ledgerActionPleaseConnectAndConfirm": "Vincule su billetera de hardware, asegúrese de que esté desbloqueada y de haber aprobado los permisos en el explorador.", "maxInputAmount": "Importe", "maxInputMax": "Máx.", "notEnoughSolPrimaryText": "Insuficiente SOL", "notEnoughSolSecondaryText": "No tiene suficientes SOL en su cartera para esta transacción. Deposite más y vuelva a intentarlo.", "insufficientBalancePrimaryText": "No hay suficientes {{tokenSymbol}}", "insufficientBalanceSecondaryText": "No tiene suficiente {{tokenSymbol}} en su billetera para esta transacción.", "insufficientBalanceRemaining": "Restante", "insufficientBalanceRequired": "Necesario", "notEnoughSplTokensTitle": "No hay suficientes tokens", "notEnoughSplTokensDescription": "No tiene suficientes tokens en su cartera para esta transacción. Esta transacción se revertirá si se envía.", "transactionExpiredPrimaryText": "Transacción caducada", "transactionExpiredSecondaryText": "Ha esperado demasiado tiempo para confirmar la transacción y esta ha caducado. Esta transacción se revertirá si se presenta.", "transactionHasWarning": "Aviso de transacción", "tokens": "tokens", "notificationApplicationApprovalPermissionsAddressVerification": "Verifique que es el propietario de esta dirección", "notificationApplicationApprovalPermissionsTransactionApproval": "Solicitar la aprobación de las transacciones", "notificationApplicationApprovalPermissionsViewWalletActivity": "Ver el saldo y la actividad de su billetera", "notificationApplicationApprovalParagraphText": "La confirmación permitirá a este sitio ver los saldos y la actividad de la cuenta seleccionada.", "notificationApplicationApprovalActionButtonConnect": "Vincular", "notificationApplicationApprovalActionButtonSignIn": "In<PERSON><PERSON>", "notificationApplicationApprovalAllowApproval": "¿Permitir que el sitio se vincule?", "notificationApplicationApprovalAutoConfirm": "Confirmación automática de transacciones", "notificationApplicationApprovalConnectDisclaimer": "Conectarse sólo a sitios web de su confianza", "notificationApplicationApprovalSignInDisclaimer": "Inicie sesión solo en sitios web en los que confíe", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Este website no es seguro de usar y puede intentar robar sus fondos.", "notificationApplicationApprovalConnectUnknownApp": "Desconocido", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "No es posible conectarse a la aplicación", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Esta aplicación está intentando conectarse a {{appNetworkName}}, pero {{phantomNetworkName}} está seleccionado.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Para utilizar {{networkName}}, vaya a Ajustes para desarrolladores → Modo Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Red desconocida", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Actualmente Ledger no admite la conexión con otras aplicaciones móviles.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Cambie a una cuenta que no sea de Ledger o utilice el navegador de la aplicación e inténtelo de nuevo.", "notificationSignatureRequestConfirmTransaction": "Confirmar transacción", "notificationSignatureRequestConfirmTransactionCapitalized": "Confirmar transacción", "notificationSignatureRequestConfirmTransactions": "Confirmar transacciones", "notificationSignatureRequestConfirmTransactionsCapitalized": "Confirmar transacciones", "notificationSignatureRequestSignatureRequest": "Solicitud de firma", "notificationMessageHeader": "Men<PERSON><PERSON>", "notificationMessageCopied": "<PERSON><PERSON><PERSON> copiado", "notificationAutoConfirm": "Confirmación automática", "notificationAutoConfirmOff": "Desactivada", "notificationAutoConfirmOn": "Activada", "notificationConfirmFooter": "Confirme solo si confía en este sitio web.", "notificationEstimatedTime": "Tiempo estimado", "notificationPermissionRequestText": "Se trata únicamente de una solicitud de permiso. Es posible que la transacción no se ejecute inmediatamente.", "notificationBalanceChangesText": "Los cambios de saldo son estimados. Los importes y los activos implicados no están garantizados.", "notificationContractAddress": "Dirección contractual", "notificationAdvancedDetailsText": "<PERSON><PERSON><PERSON>", "notificationUnableToSimulateWarningText": "Actualmente no podemos estimar los cambios de saldo. Puede volver a intentarlo más tarde, o confirmar si confía en este sitio.", "notificationSignMessageParagraphText": "La firma de este mensaje demostrará que usted es el propietario de la cuenta seleccionada.", "notificationSignatureRequestScanFailedDescription": "No se ha podido escanear el mensaje en busca de problemas de seguridad. Proceda con precaución.", "notificationFailedToScan": "No se han podido simular los resultados de esta solicitud.\nLa confirmación no es segura y puede provocar pérdidas.", "notificationScanLoading": "Solicitud de escaneado", "notificationTransactionApprovalActionButtonConfirm": "Confirmar", "notificationTransactionApprovalActionButtonBack": "Atrás", "notificationTransactionApprovalEstimatedChanges": "Cambios estimados", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Las estimaciones se basan en simulaciones de transacción, por lo que no ofrecen ninguna garantía", "notificationTransactionApprovalHideAdvancedDetails": "Ocultar datos de transacción avanzados", "notificationTransactionApprovalNetworkFee": "Comisión de la red", "notificationTransactionApprovalNetwork": "Red", "notificationTransactionApprovalEstimatedTime": "Tiempo estimado", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "No se han encontrado cambios que afecten a la propiedad de los activos", "notificationTransactionApprovalSolanaAmountRequired": "Importe requerido por la red Solana para tramitar la transacción", "notificationTransactionApprovalUnableToSimulate": "No se puede simular. Asegúrese de confiar en este sitio web, ya que la aprobación puede conducir a la pérdida de fondos.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "No se pueden recuperar los cambios de saldo", "notificationTransactionApprovalViewAdvancedDetails": "Ver datos de transacción avanzados", "notificationTransactionApprovalKnownMalicious": "Esta transacción es fraudulenta. La firma conllevará la pérdida de fondos.", "notificationTransactionApprovalSuspectedMalicious": "Sospechamos que esta transacción es fraudulenta. Aprobarla puede llevar a la pérdida de fondos.", "notificationTransactionApprovalNetworkFeeHighWarning": "Las comisiones en la red son elevadas debido a la congestión en la red.", "notificationTransactionERC20ApprovalDescription": "<PERSON> lo confirma, esta aplicación podrá acceder a su saldo en cualquier momento, hasta el límite indicado a continuación.", "notificationTransactionERC20ApprovalContractAddress": "Dirección contractual", "notificationTransactionERC20Unlimited": "ilimitado", "notificationTransactionERC20ApprovalTitle": "Aprobar el gasto de {{tokenSymbol}}", "notificationTransactionERC20RevokeTitle": "Revocar el gasto de {{tokenSymbol}}", "notificationTransactionERC721RevokeTitle": "Revocar el acceso a {{tokenSymbol}}", "notificationTransactionERC20ApprovalAll": "Todos sus {{tokenSymbol}}", "notificationIncorrectModeTitle": "<PERSON><PERSON>", "notificationIncorrectModeInTestnetTitle": "Está en modo Testnet", "notificationIncorrectModeNotInTestnetTitle": "No está en modo Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} está intentando utilizar una mainnet, pero usted está en modo Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} está intentando utilizar una red de prueba, pero no está en modo Testnet", "notificationIncorrectModeInTestnetProceed": "<PERSON> continuar, desactive el modo Testnet.", "notificationIncorrectModeNotInTestnetProceed": "Para continuar, active el modo Testnet.", "notificationIncorrectEIP712ChainId": "Le impedimos firmar un mensaje que no estaba destinado a la red a la que está conectado actualmente", "notificationIncorrectEIP712ChainIdDescription": "Mensaje solicitado {{messageChainId}}, está conectado a {{connectedChainId}}", "notificationUnsupportedNetwork": "Red no admitida", "notificationUnsupportedNetworkDescription": "Este sitio web está intentando utilizar una red que Phantom no admite actualmente.", "notificationUnsupportedNetworkDescriptionInterpolated": "Para proceder con una extensión diferente, desactive <1>Ajustes → Billetera de aplicación predeterminada y seleccione Preguntar siempre</1>. A continuación, actualice la página y vuelva a conectarse.", "notificationUnsupportedAccount": "Cuenta no admitida", "notificationUnsupportedAccountDescription": "Este sitio web está intentando utilizar {{targetChainType}}, que esta cuenta {{chainType}} no admite.", "notificationUnsupportedAccountDescription2": "Cambie a una cuenta de una frase semilla o clave privada compatible e inténtelo de nuevo.", "notificationInvalidTransaction": "Transacción no válida", "notificationInvalidTransactionDescription": "La transacción recibida de esta app está malformada y no debería enviarse. Póngase en contacto con el desarrollador de esta app para informarle de este problema.", "notificationCopyTransactionText": "Copiar transacción", "notificationTransactionCopied": "Transacción copiada", "onboardingImportOptionsPageTitle": "Importar una billetera", "onboardingImportOptionsPageSubtitle": "Importe una billetera existente con su frase secreta, clave privada o billetera de hardware.", "onboardingImportPrivateKeyPageTitle": "Importar una clave privada", "onboardingImportPrivateKeyPageSubtitle": "Importe una billetera monocadena existente", "onboardingCreatePassword": "<PERSON><PERSON>r una contraseña", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Acepto los <1>Términos del servicio</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Confirmar con<PERSON>", "onboardingCreatePasswordDescription": "Le hará falta para desbloquear la billetera.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Frase secreta de recuperación inválida", "onboardingCreatePasswordPasswordPlaceholder": "Contraseña", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthMedium": "Media", "onboardingCreatePasswordPasswordStrengthStrong": "Alta", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "He guardado mi Frase secreta de recuperación", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Frase secreta de recuperación", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Esta frase es la ÚNICA manera de recuperar su billetera. ¡NO la comparta con nadie!", "onboardingImportWallet": "Importar billetera", "onboardingImportWalletImportExistingWallet": "Importar una billetera existente con su frase secreta de recuperación de 12 o 24 palabras.", "onboardingImportWalletRestoreWallet": "Restaurar billetera", "onboardingImportWalletSecretRecoveryPhrase": "Frase secreta de recuperación", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Frase secreta de recuperación no válida", "onboardingImportWalletIHaveWords": "Tengo una frase de recuperación de {{numWords}} palabras", "onboardingImportWalletIncorrectOrMisspelledWord": "La palabra {{wordIndex}} es incorrecta o está mal escrita", "onboardingImportWalletIncorrectOrMisspelledWords": "Las palabras {{wordIndexes}} son incorrectas o están mal escritas", "onboardingImportWalletScrollDown": "<PERSON><PERSON><PERSON><PERSON><PERSON> abajo", "onboardingImportWalletScrollUp": "Desp<PERSON><PERSON><PERSON> arriba", "onboardingSelectAccountsImportAccounts": "Importar cuentas", "onboardingSelectAccountsImportAccountsDescription": "Elija las cuentas de billetera que desea importar.", "onboardingSelectAccountsImportSelectedAccounts": "Importar cuentas seleccionadas", "onboardingSelectAccountsFindMoreAccounts": "Encontrar más cuentas", "onboardingSelectAccountsFindMoreNoneFound": "No se han encontrado cuentas", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} cuenta<PERSON> se<PERSON>", "onboardingSelectAccountSelectAllText": "<PERSON><PERSON><PERSON><PERSON><PERSON> todo", "onboardingAdditionalPermissionsTitle": "Utilizar aplicaciones con Phantom", "onboardingAdditionalPermissionsSubtitle": "Para que la experiencia con la aplicación sea lo más fluida posible, le recomendamos que permita a Phantom leer y modificar los datos de todos los sitios.", "interstitialAdditionalPermissionsTitle": "Utilizar aplicaciones con Phantom", "interstitialAdditionalPermissionsSubtitle": "Para continuar con su experiencia de uso de las aplicaciones sin interrupciones, le recomendamos que permita a Phantom leer y modificar los datos de todos los sitios.", "recentActivityPrimaryText": "Actividad reciente", "removeAccountActionButtonRemove": "Eliminar", "removeAccountRemoveWallet": "Eliminar cuenta", "removeAccountInterpolated": "Eliminar {{accountName}}", "removeAccountWarningLedger": "A pesar de eliminar esta billetera de Phantom, podrá volver a añadirla mediante la opción «Vincular billetera de hardware».", "removeAccountWarningSeedVault": "A pesar de eliminar esta billetera de Phantom, podrá volver a añadirla mediante la opción «Conectar billetera de la Bóveda de Semillas».", "removeAccountWarningPrivateKey": "Una vez que elimine esta billetera, Phantom no podrá recuperarla para usted. Asegúrese de tener una copia de seguridad de su clave privada.", "removeAccountWarningSeed": "A pesar de eliminar esta billetera de Phantom, podrá volver a recuperarla utilizando su mnemonic en esta u otra billetera.", "removeAccountWarningReadOnly": "La eliminación de esta cuenta no afectará a su billetera, ya que se trata de una billetera de solo lectura.", "removeSeedPrimaryText": "Eliminar frase secreta {{number}}", "removeSeedSecondaryText": "Esto eliminará todas las cuentas existentes en la frase secreta {{number}}. Asegúrese de tener guardada su frase secreta existente.", "resetSeedPrimaryText": "Reinicia la aplicación con una nueva frase secreta", "resetSeedSecondaryText": "Con ello se eliminarán todas las cuentas existentes y se sustituirán por otras nuevas. Asegúrese de tener una copia de seguridad de su frase secreta y de sus claves privadas.", "resetAppPrimaryText": "Restablecer y borrar la aplicación", "resetAppSecondaryText": "Esto eliminará todas las cuentas y datos existentes. Asegúrese de tener una copia de seguridad de su frase secreta y de sus claves privadas.", "richTransactionsDays": "días", "richTransactionsToday": "Hoy", "richTransactionsYesterday": "Ayer", "richTransactionDetailAccount": "C<PERSON><PERSON>", "richTransactionDetailAppInteraction": "Interacción con la aplicación", "richTransactionDetailAt": "a las", "richTransactionDetailBid": "<PERSON><PERSON><PERSON>", "richTransactionDetailBidDetails": "Detalles de la oferta", "richTransactionDetailBought": "Comprado", "richTransactionDetailBurned": "<PERSON><PERSON><PERSON>", "richTransactionDetailCancelBid": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailCompleted": "Completada", "richTransactionDetailConfirmed": "<PERSON><PERSON>rma<PERSON>", "richTransactionDetailDate": "<PERSON><PERSON>", "richTransactionDetailFailed": "Fallo", "richTransactionDetailFrom": "De", "richTransactionDetailItem": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailListed": "Listado", "richTransactionDetailListingDetails": "Detalles del listado", "richTransactionDetailListingPrice": "Precio de venta", "richTransactionDetailMarketplace": "<PERSON><PERSON><PERSON>", "richTransactionDetailNetworkFee": "Comisión de la red", "richTransactionDetailOriginalListingPrice": "Precio de venta original", "richTransactionDetailPending": "Pendiente", "richTransactionDetailPrice": "Precio", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailPurchaseDetails": "Detalles de la compra", "richTransactionDetailRebate": "Reembolso", "richTransactionDetailReceived": "Recibida", "richTransactionDetailSaleDetails": "Detalles de la venta", "richTransactionDetailSent": "Enviada", "richTransactionDetailSold": "Vendido", "richTransactionDetailStaked": "Apostado", "richTransactionDetailStatus": "Estado", "richTransactionDetailSwap": "Intercambiar", "richTransactionDetailSwapDetails": "Detalles del intercambio", "richTransactionDetailTo": "Para", "richTransactionDetailTokenSwap": "Intercambio de token", "richTransactionDetailUnknownNFT": "NFT desconocido", "richTransactionDetailUnlisted": "Deslistado", "richTransactionDetailUnstaked": "Sin apostar", "richTransactionDetailValidator": "Validador", "richTransactionDetailViewOnExplorer": "Ver en {{explorer}}", "richTransactionDetailWithdrawStake": "Retirar apuesta", "richTransactionDetailYouPaid": "Us<PERSON> ha pagado", "richTransactionDetailYouReceived": "Usted ha recibido", "richTransactionDetailUnwrapDetails": "Detalles de desenvoltura", "richTransactionDetailTokenUnwrap": "Desenvolver token", "activityItemsRefreshFailed": "Error al cargar las transacciones más nuevas.", "activityItemsPagingFailed": "Error al cargar las transacciones más antiguas.", "activityItemsTestnetNotAvailable": "El historial de transacciones de Testnet no está disponible en este momento", "historyUnknownDappName": "Desconocido", "historyStatusSucceeded": "Conseguido", "historyNetwork": "Red", "historyAttemptedAmount": "Cantidad intentada", "historyAmount": "Importe", "sendAddressBookButtonLabel": "Libro de direcciones", "addressBookSelectAddressBook": "Libro de direcciones", "sendAddressBookNoAddressesSaved": "No hay direcciones guardadas", "sendAddressBookRecentlyUsed": "Usadas recientemente", "addressBookSelectRecentlyUsed": "Usadas recientemente", "sendConfirmationLabel": "Etiqueta", "sendConfirmationMessage": "Men<PERSON><PERSON>", "sendConfirmationNetworkFee": "Comisión de la red", "sendConfirmationPrimaryText": "Confirmar env<PERSON>", "sendWarning_INSUFFICIENT_FUNDS": "Fondos insuficientes, esta transacción probablemente fallará si se envía.", "sendFungibleSummaryNetwork": "Red", "sendFungibleSummaryNetworkFee": "Comisión de la red", "sendFungibleSummaryEstimatedTime": "Tiempo estimado", "sendFungiblePendingEstimatedTime": "Estimaciones de tiempo", "sendFungibleSummaryEstimatedTimeDescription": "Las velocidades de transacción de Ethereum varían en función de varios factores. Puedes acelerarlas haciendo clic en «Tarifa de red».", "sendSummaryBitcoinPendingTxTitle": "No se ha podido enviar la transferencia", "sendSummaryBitcoinPendingTxDescription": "Solo puede tener pendiente una transferencia de BTC a la vez. Espere a que se complete para enviar una nueva transferencia.", "sendFungibleSatProtectionTitle": "Envío con Protección de Sats", "sendFungibleSatProtectionExplainer": "Phantom garantiza que sus Ordinales y BRC20 no se utilizarán para comisiones por transacciones o transferencias de Bitcoin.", "sendFungibleTransferFee": "Comisión por transferencia de token", "sendFungibleTransferFeeToolTip": "El creador de este token recibe una comisión por cada transferencia. No es una comisión cobrada o recaudada por Phantom.", "sendFungibleInterestBearingPercent": "Tipo de interés actual", "sendFungibleNonTransferable": "No transferible", "sendFungibleNonTransferableToolTip": "Este token no puede transferirse a otra cuenta.", "sendFungibleNonTransferableYes": "Sí", "sendStatusErrorMessageInterpolated": "Hubo un error al intentar enviar tokens a <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "No dispone de saldo suficiente para completar la transacción.", "sendStatusErrorTitle": "No se puede enviar", "sendStatusLoadingTitle": "Enviando...", "sendStatusSuccessMessageInterpolated": "Sus tokens se enviaron con éxito a <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "¡Enviado!", "sendStatusConfirmedSuccessTitle": "¡Enviado!", "sendStatusSubmittedSuccessTitle": "Transacción enviada", "sendStatusEstimatedTransactionTime": "Tiempo estimado de la transacción: {{time}}", "sendStatusViewTransaction": "Ver transacción", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> a <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>Se ha enviado {{uiAmount}} {{assetSymbol}}</2> con éxito a <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>Se ha enviado {{uiAmount}} {{assetSymbol}}</2> con éxito a <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>No se ha podido enviar {{uiAmount}} {{assetSymbol}}</2> a <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "C<PERSON><PERSON> de error {{code}}", "sendFormErrorInsufficientBalance": "<PERSON><PERSON> insuficiente", "sendFormErrorEmptyAmount": "Se requiere una cantidad", "sendFormInvalidAddress": "Dirección {{assetName}} no válida", "sendFormInvalidUsernameOrAddress": "Nombre de usuario o dirección no válidos", "sendFormErrorInvalidSolanaAddress": "Dirección de Solana inválida", "sendFormErrorInvalidTwitterHandle": "Este identificador de Twitter no está registrado", "sendFormErrorInvalidDomain": "Este dominio no está registrado", "sendFormErrorInvalidUsername": "Este nombre de usuario no está registrado", "sendFormErrorMinRequiredInterpolated": "Se requiere al menos {{minAmount}} {{tokenName}}", "sendRecipientTextareaPlaceholder": "Dirección SOL del destinatario", "sendRecipientTextAreaPlaceholder2": "Dirección de {{symbol}} del destinatario", "sendMemoOptional": "Memo (opcional)", "sendMemo": "Memo", "sendOptional": "opcional", "settings": "<PERSON><PERSON><PERSON><PERSON>", "settingsDapps": "dApps", "settingsSelectedAccount": "Cuenta seleccionada", "settingsAddressBookNoLabel": "Sin etiqueta", "settingsAddressBookPrimary": "Libro de direcciones", "settingsAddressBookRecentlyUsed": "Usadas Recientemente", "settingsAddressBookSecondary": "Gestionar las direcciones más utilizadas", "settingsAutoLockTimerPrimary": "Temporizador de bloqueo automático", "settingsAutoLockTimerSecondary": "Cambiar la duración del temporizador de bloqueo automático", "settingsChangeLanguagePrimary": "Cambiar I<PERSON>", "settingsChangeLanguageSecondary": "Cambiar el idioma de la pantalla", "settingsChangeNetworkPrimary": "Cambiar red", "settingsChangeNetworkSecondary": "Configurar los ajustes de red", "settingsChangePasswordPrimary": "Cambiar contraseña", "settingsChangePasswordSecondary": "Cambiar la contraseña de la pantalla de bloqueo", "settingsCompleteBetaSurvey": "Completar la encuesta Beta", "settingsDisplayLanguage": "Mostrar idioma", "settingsErrorCannotExportLedgerPrivateKey": "No se puede exportar la clave privada de Ledger", "settingsErrorCannotRemoveAllWallets": "No se pueden eliminar todas las cuentas", "settingsExportPrivateKey": "Mostrar clave privada", "settingsNetworkMainnetBeta": "Beta de la red principal", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Red Phantom RPC", "settingsTestNetworks": "Redes de Prueba", "settingsUseCustomNetworks": "Utilizar redes personalizadas", "settingsTestnetMode": "Modo Testnet", "settingsTestnetModeDescription": "Se aplica a saldos y conexiones de aplicaciones.", "settingsWebViewDebugging": "Depuración de la vista web", "settingsWebViewDebuggingDescription": "Le permite inspeccionar y depurar las vistas web del navegador de la aplicación.", "settingsTestNetworksInfo": "Cambiar a cualquier Red de prueba solo debe usarse para hacer pruebas. Los tokens en las Redes de prueba no tienen ningún valor monetario.", "settingsEmojis": "Emojis", "settingsNoAddresses": "No hay direcciones", "settingsAddressBookEmptyHeading": "Su Libreta de direcciones está vacía", "settingsAddressBookEmptyText": "Haga clic en los botones «+» o «Añadir dirección» para añadir sus direcciones favoritas", "settingsEditWallet": "<PERSON><PERSON> cuenta", "settingsNoTrustedApps": "No hay apps de confianza", "settingsNoConnections": "Aún no hay conexiones.", "settingsRemoveWallet": "Eliminar cuenta", "settingsResetApp": "Restablecer aplicación", "settingsBlocked": "Bloqueado", "settingsBlockedAccounts": "Cuentas bloqueadas", "settingsNoBlockedAccounts": "No hay cuentas bloqueadas.", "settingsRemoveSecretPhrase": "Eliminar Frase secreta", "settingsResetAppWithSecretPhrase": "Restablecer aplicación con Frase secreta", "settingsResetSecretRecoveryPhrase": "Restablecer frase secr. recuperación", "settingsShowSecretRecoveryPhrase": "Mostrar frase secreta recuperación", "settingsShowSecretRecoveryPhraseSecondary": "Mostrar frase de recuperación", "settingsShowSecretRecoveryPhraseTertiary": "Mostrar Frase secreta", "settingsTrustedAppsAutoConfirmActiveUntil": "Hasta {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Confirmación automática", "settingsTrustedAppsDisclaimer": "Habilitar la confirmación automática solo en sitios de confianza", "settingsTrustedAppsLastUsed": "Se usó hace {{formattedTimestamp}}", "settingsTrustedAppsPrimary": "Aplicaciones conectadas", "settingsTrustedApps": "Aplicaciones de confianza", "settingsTrustedAppsRevoke": "Revocar", "settingsTrustedAppsRevokeToast": "{{trustedApp}} desconectada", "settingsTrustedAppsSecondary": "Configure sus aplicaciones de confianza", "settingsTrustedAppsToday": "Hoy", "settingsTrustedAppsYesterday": "Ayer", "settingsTrustedAppsLastWeek": "La semana pasada", "settingsTrustedAppsBeforeYesterday": "Anteriormente", "settingsTrustedAppsDisconnectAll": "Desconectarse de todo", "settingsTrustedAppsDisconnectAllToast": "Todas las aplicaciones desconectadas", "settingsTrustedAppsEndAutoConfirmForAll": "Finalizar la confirmación automática para todos", "settingsTrustedAppsEndAutoConfirmForAllToast": "Todas las sesiones de confirmación automática finalizadas", "settingsSecurityPrimary": "Seguridad y privacidad", "settingsSecuritySecondary": "Actualice su configuración de seguridad", "settingsActiveNetworks": "Redes activas", "settingsActiveNetworksAll": "Todo", "settingsActiveNetworksSolana": "Solo Solana", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana es la red por defecto y siempre permanece activada.", "settingsDeveloperPrimary": "Ajustes para desarrolladores", "settingsAdvanced": "<PERSON><PERSON><PERSON><PERSON>", "settingsTransactions": "Ajustes de transacción", "settingsAutoConfirm": "Ajustes de confirmación automática", "settingsSecurityAnalyticsPrimary": "Compartir análisis anón<PERSON>", "settingsSecurityAnalyticsSecondary": "Habilitar para ayudarnos a mejorar", "settingsSecurityAnalyticsHelper": "Phantom no utiliza su información personal con fines analíticos", "settingsSuspiciousCollectiblesPrimary": "Ocultar objetos de colección sospechosos", "settingsSuspiciousCollectiblesSecondary": "Activar para ocultar los coleccionables marcados", "settingsPreferredBitcoinAddress": "Dirección preferida de Bitcoin", "settingsEnabledAddressesUpdated": "¡Direcciones visibles actualizadas!", "settingsEnabledAddresses": "Direcciones habilitadas", "settingsBitcoinPaymentAddressForApps": "Dirección de pagos para aplicaciones", "settingsBitcoinOrdinalsAddressForApps": "Dirección de Ordinales para aplicaciones", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Cuando los dos tipos de dirección anteriores están activados, para ciertas aplicaciones como Magic Eden, se utilizará su dirección Segwit nativa para financiar las compras. Los activos comprados se recibirán en su dirección Taproot.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "La dirección Bitcoin por defecto en Phantom para garantizar la compatibilidad.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Po<PERSON> defecto)", "settingsPreferredBitcoinAddressTaprootExplainer": "El tipo de dirección más moderno, normalmente con comisiones por transacción más baratas.", "settingsPreferredExplorers": "Explorador preferido", "settingsPreferredExplorersSecondary": "Cambie a su explorador de blockchain preferido", "settingsCustomGasControls": "Controles de combustible personalizados", "settingsSupportDesk": "Servic<PERSON> de asistencia", "settingsSubmitATicket": "Registrar una incidencia", "settingsAttachApplicationLogs": "Adjuntar registros de la aplicación", "settingsDownloadApplicationLogs": "Descargar los registros de la aplicación", "settingsDownloadApplicationLogsShort": "Descargar registros", "settingsDownloadApplicationLogsHelper": "Contiene datos locales, informes de bloqueos y direcciones de billeteras públicas para ayudar a resolver los problemas de asistencia técnica de Phantom", "settingsDownloadApplicationLogsWarning": "No se incluyen datos sensibles como frases de semilla o claves privadas.", "settingsWallet": "Bill<PERSON>a", "settingsPreferences": "Preferencias", "settingsSecurity": "Seguridad", "settingsDeveloper": "Desarrollador", "settingsSupport": "Asistencia", "settingsWalletShortcutsPrimary": "Mostrar accesos directos a la billetera", "settingsAppIcon": "Icono de la aplicación", "settingsAppIconDefault": "Por defecto", "settingsAppIconLight": "<PERSON><PERSON><PERSON>", "settingsAppIconDark": "Oscuro", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "C<PERSON><PERSON>", "settingsSearchResultSelected": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultExport": "Exportar", "settingsSearchResultSeed": "<PERSON><PERSON>", "settingsSearchResultTrusted": "De confianza", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Estado", "settingsSearchResultLogs": "Registros", "settingsSearchResultBiometric": "Biométrico", "settingsSearchResultTouch": "Táctil", "settingsSearchResultFace": "<PERSON><PERSON><PERSON>", "settingsSearchResultShortcuts": "Accesos directos", "settingsAllSitesPermissionsTitle": "Acceda a Phantom en todos los sitios", "settingsAllSitesPermissionsSubtitle": "Le permite utilizar aplicaciones sin problemas con Phantom sin hacer clic en la extensión", "settingsAllSitesPermissionsDisabled": "Su navegador no permite cambiar esta configuración", "settingsSolanaCopyTransaction": "Activar copiar transacción", "settingsSolanaCopyTransactionDetails": "Copiar datos de transacción serializados al portapapeles", "settingsAutoConfirmHeader": "Confirmación automática", "refreshWebpageToApplyChanges": "Actualice la página web para aplicar los cambios", "settingsExperimentalTitle": "Características experimentales", "settingsExprimentalSolanaActionsSubtitle": "Expanda automáticamente los botones de acción de Solana cuando se detecten enlaces relevantes en X.com", "stakeAccountCardActiveStake": "Apuesta activa", "stakeAccountCardBalance": "<PERSON><PERSON>", "stakeAccountCardRentReserve": "Reserva de alquiler", "stakeAccountCardRewards": "Última recompensa", "stakeAccountCardRewardsTooltip": "Esta es la recompensa más reciente que ha ganado por apostar. Se le recompensa cada 3 días.", "stakeAccountCardStakeAccount": "Dirección", "stakeAccountCardLockup": "Bloqueo hasta", "stakeRewardsHistoryTitle": "Historial de recompensas", "stakeRewardsActivityItemTitle": "Recompensas", "stakeRewardsHistoryEmptyList": "Ninguna recompensa", "stakeRewardsTime_zero": "Hoy", "stakeRewardsTime_one": "Ayer", "stakeRewardsTime_other": "Hace {{count}} días", "stakeRewardsItemsPagingFailed": "Error al cargar recompensas anteriores.", "stakeAccountCreateAndDelegateErrorStaking": "Se produjo un problema al apostar en este validador. Inténtelo de nuevo.", "stakeAccountCreateAndDelegateSolStaked": "SOL apostados", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Sus SOL comenzarán a dar fruto <1></1> en los próximos dos días cuando se active la cuenta objeto de «staking».", "stakeAccountCreateAndDelegateStakingFailed": "Apuesta fallida", "stakeAccountCreateAndDelegateStakingSol": "Apostando SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Estamos creando una cuenta de «staking» y, a continuación, delegaremos sus SOL a", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Estamos creando una cuenta de «staking» y, a continuación, delegaremos sus SOL a {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Ver transacción", "stakeAccountDeactivateStakeSolUnstaked": "Apuesta de SOL cancelada", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Podrá retirar sus ganancias <1></1> en los próximos dos días cuando se desactive su cuenta de «staking».", "stakeAccountDeactivateStakeSolUnstakedDescription": "Podrá retirar sus ganancias en los próximos dos días cuando se desactive su cuenta de «staking».", "stakeAccountDeactivateStakeUnstakingFailed": "Cancelación de la apuesta fallida", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Se produjo un problema al cancelar la apuesta en este validador. Inténtelo de nuevo.", "stakeAccountDeactivateStakeUnstakingSol": "Cancelando la apuesta de SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Estamos iniciando el proceso para cancelar la apuesta de sus SOL.", "stakeAccountDeactivateStakeViewTransaction": "Ver transacción", "stakeAccountDelegateStakeSolStaked": "SOL apostados", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Sus SOL comenzarán a dar fruto <1></1> en los próximos dos días cuando se active la cuenta objeto de «staking».", "stakeAccountDelegateStakeStakingFailed": "Apuesta fallida", "stakeAccountDelegateStakeStakingFailedDescription": "Se produjo un problema al apostar en este validador. Inténtelo de nuevo.", "stakeAccountDelegateStakeStakingSol": "Apostando SOL...", "stakeAccountDelegateStakeStakingSolDescription": "Estamos delegando sus SOL.", "stakeAccountDelegateStakeViewTransaction": "Ver transacción", "stakeAccountListActivationActivating": "Activando", "stakeAccountListActivationActive": "Activo", "stakeAccountListActivationInactive": "Inactivo", "stakeAccountListActivationDeactivating": "Desactivando", "stakeAccountListErrorFetching": "No hemos podido recuperar las cuentas de las apuestas. Vuelva a intentarlo más tarde.", "stakeAccountListNoStakingAccounts": "No hay cuentas de apuestas", "stakeAccountListReload": "Recargar", "stakeAccountListViewPrimaryText": "Su apuesta", "stakeAccountListViewStakeSOL": "Apostar SOL", "stakeAccountListItemStakeFee": "Comisión del {{fee}}", "stakeAccountViewActionButtonRestake": "Volver a apostar", "stakeAccountViewActionButtonUnstake": "Cancelar apuesta", "stakeAccountViewError": "Error", "stakeAccountViewPrimaryText": "Su apuesta", "stakeAccountViewRestake": "Volver a realizar el «staking»", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Sus SOL están actualmente conservados mediante «staking» con un validador. Tendrá que cancelar el «staking» de sus SOL para <1></1>acceder a esos fondos. <3>Más información</3>", "stakeAccountViewStakeInactive": {"part1": "Esta cuenta de apuestas está inactiva. Considere la posibilidad de retirar su apuesta o de buscar un validador en el que delegar.", "part2": "Más información"}, "stakeAccountViewStakeNotFound": "No se ha podido encontrar esta cuenta de apuestas.", "stakeAccountViewViewOnExplorer": "Ver en {{explorer}}", "stakeAccountViewWithdrawStake": "Retirar apuesta", "stakeAccountViewWithdrawUnstakedSOL": "Retirar la apuesta de SOL cancelada", "stakeAccountInsufficientFunds": "No hay suficiente SOL disponible para cancelar apuesta o retirar.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL retirados", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Sus SOL se han retirado.", "part2": "Esta cuenta de apuestas se eliminará automáticamente en los próximos minutos."}, "stakeAccountWithdrawStakeViewTransaction": "Ver transacción", "stakeAccountWithdrawStakeWithdrawalFailed": "<PERSON><PERSON><PERSON> fallido", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Se produjo un problema al retirar fondos de esta cuenta de apuestas. Inténtelo de nuevo.", "stakeAccountWithdrawStakeWithdrawingSol": "Retirando SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Estamos retirando su SOL desde esta cuenta de apuestas.", "startEarningSolAccount": "cuenta", "startEarningSolAccounts": "cuentas", "startEarningSolErrorClosePhantom": "Pulse aquí e inténtelo de nuevo", "startEarningSolErrorTroubleLoading": "Problemas al cargar el «stake»", "startEarningSolLoading": "Cargando...", "startEarningSolPrimaryText": "Empiece a ganar SOL", "startEarningSolSearching": "Buscando cuentas de «staking»", "startEarningSolStakeTokens": "Apueste tokens y gane recompensas", "startEarningSolYourStake": "Su apuesta", "unwrapFungibleTitle": "Intercambiar a {{tokenSymbol}}", "unwrapFungibleDescription": "Re<PERSON>rar de {{fromToken}} para {{toToken}}", "unwrapFungibleConfirmSwap": "Confirmar intercambio", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Comisiones estimadas", "swapFeesFees": "Comisiones", "swapFeesPhantomFee": "Comisión de Phantom", "swapFeesPhantomFeeDisclaimer": "Siempre encontramos el mejor precio posible de los mejores proveedores de liquidez. En este presupuesto se incluye automáticamente una comisión de {{feePercentage}}.", "swapFeesRate": "Precio", "swapFeesRateDisclaimer": "La mejor tarifa encontrada por Jupiter Aggregator a través de múltiples intercambios descentralizados.", "swapFeesRateDisclaimerMultichain": "La mejor tarifa encontrada en múltiples mercados descentralizados.", "swapFeesPriceImpact": "Impacto en el precio", "swapFeesHighPriceImpact": "Alto impacto en el precio", "swapFeesPriceImpactDisclaimer": "La diferencia entre el precio de mercado y el precio estimado según el tamaño de su operación.", "swapFeesSlippage": "Deslizamiento", "swapFeesHighSlippage": "Alta tolerancia al deslizamiento", "swapFeesHighSlippageDisclaimer": "Su transacción no se completará si el precio cambia desfavorablemente más de {{slippage}} %.", "swapTransferFee": "Comisión de transferencia", "swapTransferFeeDisclaimer": "La negociación de {{symbol}} $ incurre en una comisión de transferencia del {{feePercent}} % fijada por el creador del token, no por Phantom.", "swapTransferFeeDisclaimerMany": "Las operaciones con los tokens seleccionados conllevan una comisión del {{feePercent}} % fijada por los creadores de los tokens, no por Phantom.", "swapFeesSlippageDisclaimer": "Importe que el precio de su operación puede desviarse del presupuesto proporcionado.", "swapFeesProvider": "<PERSON><PERSON><PERSON><PERSON>", "swapFeesProviderDisclaimer": "El intercambio descentralizado utilizado para completar su operación de comercio.", "swapEstimatedTime": "Tiempo estimado", "swapEstimatedTimeShort": "Tiempo estimado", "swapEstimatedTimeDisclaimer": "El tiempo estimado de finalización del puente variará en función de varios factores que afectan a la velocidad de las transacciones.", "swapSettingsButtonCommand": "Abrir la configuración de intercambio", "swapQuestionRetry": "¿Reintent<PERSON>?", "swapUnverifiedTokens": "Tokens sin verificar", "swapSectionTitleTokens": "Tokens de {{section}}", "swapFlowYouPay": "Usted paga", "swapFlowYouReceive": "Usted recibe", "swapFlowActionButtonText": "<PERSON><PERSON><PERSON> orden", "swapAssetCardTokenNetwork": "{{symbol}} en {{network}}", "swapAssetCardMaxButton": "Máx.", "swapAssetCardSelectTokenAndNetwork": "Seleccione el token y la red", "swapAssetCardBuyTitle": "Usted recibe", "swapAssetCardSellTitle": "Usted paga", "swapAssetWarningUnverified": "Este token no está verificado. Interactúe únicamente con tokens en los que confíe.", "swapAssetWarningPermanentDelegate": "Un delegado puede quemar o transferir permanentemente estos tokens.", "swapSlippageSettingsTitle": "Ajustes de desviación", "swapSlippageSettingsSubtitle": "Su transacción no se realizará si el precio cambia más que la desviación. Un valor demasiado alto dará lugar a una operación desfavorable.", "swapSlippageSettingsCustom": "Personalizada", "swapSlippageSettingsHighSlippageWarning": "Su transacción puede adelantarse y resultar desfavorable.", "swapSlippageSettingsCustomMinError": "Introduzca un valor superior a {{minSlippage}} %.", "swapSlippageSettingsCustomMaxError": "Introduzca un valor inferior a {{maxSlippage}} %.", "swapSlippageSettingsCustomInvalidValue": "Introduzca un valor válido.", "swapSlippageSettingsAutoSubtitle": "Phantom encontrará el deslizamiento más bajo para intercambiar con éxito.", "swapSlippageSettingsAuto": "Automático", "swapSlippageSettingsFixed": "<PERSON><PERSON>", "swapSlippageOptInTitle": "Deslizamiento automático", "swapSlippageOptInSubtitle": "Phantom encontrará el deslizamiento más bajo para intercambiar con éxito. Puede cambiar esto en cualquier momento en Intercambiador → Ajustes de deslizamiento.", "swapSlippageOptInEnableOption": "Activar el deslizamiento automático", "swapSlippageOptInRejectOption": "Continuar con el Deslizamiento fijo", "swapQuoteFeeDisclaimer": "El presupuesto incluye una comisión del {{feePercentage}} de Phantom", "swapQuoteMissingContext": "Falta el contexto del presupuesto del intercambio", "swapQuoteErrorNoQuotes": "Intentando intercambiar sin presupuestos", "swapQuoteSolanaNetwork": "Red Solana", "swapQuoteNetwork": "Red", "swapQuoteOneTimeSerumAccount": "Cuenta única de Serum", "swapQuoteOneTimeTokenAccount": "Cuenta de tokens de un solo uso", "swapQuoteBridgeFee": "Comisión por intercambiar cadenas cruzadas", "swapQuoteDestinationNetwork": "Red de destino", "swapQuoteLiquidityProvider": "<PERSON><PERSON><PERSON><PERSON> de liquidez", "swapReviewFlowActionButtonPrimary": "Intercambiar", "swapReviewFlowPrimaryText": "<PERSON><PERSON><PERSON> orden", "swapReviewFlowYouPay": "Usted paga", "swapReviewFlowYouReceive": "Usted recibe", "swapReviewInsufficientBalance": "Fondos insuficientes", "ugcSwapWarningTitle": "Advertencia", "ugcSwapWarningBody1": "Este token se comercializa en el lanzador de tokens {{programName}}.", "ugcSwapWarningBody2": "El valor de estos tokens puede fluctuar salvajemente, dando lugar a ganancias o pérdidas financieras sustanciales. Opere bajo su propia responsabilidad.", "ugcSwapWarningConfirm": "Co<PERSON><PERSON><PERSON>", "bondingCurveProgressLabel": "Progreso de la curva de adherencia", "bondingCurveInfoTitle": "Curva de adhesión", "bondingCurveInfoDescription": "En un modelo de curva de adhesión, los precios de los tokens vienen determinados por la forma de la curva, aumentando a medida que se compran más tokens y disminuyendo a medida que se venden. Cuando se agoten los tokens, toda la liquidez se depositará en Raydium y se quemará.", "ugcFungibleWarningBanner": "Este token opera en {{programName}}", "ugcCreatedRowLabel": "Creado el", "ugcStatusRowLabel": "Estado", "ugcStatusRowValue": "<PERSON><PERSON><PERSON><PERSON>", "swapTxConfirmationReceived": "¡Recibido!", "swapTxConfirmationSwapFailed": "Intercambio fallido", "swapTxConfirmationSwapFailedStaleQuota": "El presupuesto ya no es válido. Inténtelo de nuevo.", "swapTxConfirmationSwapFailedSlippageLimit": "Su deslizamiento es demasiado bajo para este intercambio. Aumente su deslizamiento en la parte superior de la pantalla de intercambio e inténtelo de nuevo.", "swapTxConfirmationSwapFailedInsufficientBalance": "No hemos podido completar la solicitud. No dispone de saldo suficiente para completar la transacción.", "swapTxConfirmationSwapFailedEmptyRoute": "La liquidez para este par de tokens ha cambiado. No hemos podido encontrar un presupuesto adecuado. Inténtelo de nuevo o ajuste las cantidades de tokens.", "swapTxConfirmationSwapFailedAcountFrozen": "Este token ha sido congelado por su creador. No puede enviar ni intercambiar este token.", "swapTxConfirmationSwapFailedTryAgain": "El intercambio no se pudo realizar, inténtelo de nuevo", "swapTxConfirmationSwapFailedUnknownError": "No hemos podido completar el intercambio. Ninguno de sus fondos se ha visto afectado. Inténtelo de nuevo. ", "swapTxConfirmationSwapFailedSimulationTimeout": "No hemos podido simular el intercambio. Ninguno de sus fondos se ha visto afectado. Inténtelo de nuevo.", "swapTxConfirmationSwapFailedSimulationUnknownError": "No hemos podido completar el intercambio. Ninguno de sus fondos se ha visto afectado. Inténtelo de nuevo. ", "swapTxConfirmationSwapFailedInsufficientGas": "Su cuenta no tiene fondos suficientes para completar la transacción. Añada más fondos a su cuenta e inténtelo de nuevo.", "swapTxConfirmationSwapFailedLedgerReject": "El usuario ha rechazado el intercambio en el dispositivo de hardware.", "swapTxConfirmationSwapFailedLedgerConnectionError": "Se ha rechazado el intercambio debido a un error de conexión del dispositivo. Inténtelo de nuevo.", "swapTxConfirmationSwapFailedLedgerSignError": "Se ha rechazado el intercambio debido a un error de firma del dispositivo. Inténtelo de nuevo.", "swapTxConfirmationSwapFailedLedgerError": "Se ha rechazado el intercambio debido a un error del dispositivo. Inténtelo de nuevo.", "swapTxConfirmationSwappingTokens": "Intercambiando tokens...", "swapTxConfirmationTokens": "Tokens", "swapTxConfirmationTokensDeposited": "¡Listo! Los tokens se han en su billetera", "swapTxConfirmationTokensDepositedTitle": "¡Está hecho!", "swapTxConfirmationTokensDepositedBody": "Los tokens se han depositado en su billetera", "swapTxConfirmationTokensWillBeDeposited": "se depositarán en su billetera una vez que la transacción se haya completado", "swapTxConfirmationViewTransaction": "Ver transacción", "swapTxBridgeSubmitting": "Enviando transacción", "swapTxBridgeSubmittingDescription": "Intercambiando {{sellAmount}} en {{sellNetwork}} por {{buyAmount}} en {{buyNetwork}}", "swapTxBridgeFailed": "No se ha podido enviar la transacción", "swapTxBridgeFailedDescription": "No hemos podido completar la solicitud.", "swapTxBridgeSubmitted": "Transacción enviada", "swapTxBridgeSubmittedDescription": "Tiempo estimado de la transacción: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON><PERSON>e cerrar con seguridad esta ventana.", "swapperSwitchTokens": "Cambiar tokens", "swapperMax": "Máx.", "swapperTooltipNetwork": "Red", "swapperTooltipPrice": "Precio", "swapperTooltipAddress": "Contrato", "swapperTrendingSortBy": "Ordenar por", "swapperTrendingTimeFrame": "Duración", "swapperTrendingNetwork": "Red", "swapperTrendingRank": "Ra<PERSON>", "swapperTrendingTokens": "Tokens de tendencia", "swapperTrendingVolume": "Volumen", "swapperTrendingPrice": "Precio", "swapperTrendingPriceChange": "Cambio de precio", "swapperTrendingMarketCap": "Capitalización bursátil", "swapperTrendingTimeFrame1h": "1h", "swapperTrendingTimeFrame24h": "24h", "swapperTrendingTimeFrame7d": "7d", "swapperTrendingTimeFrame30d": "30d", "swapperTrendingNoTokensFound": "No se han encontrado tokens.", "switchToggle": "Cambiar", "termsOfServiceActionButtonAgree": "Estoy de acuerdo", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Al hacer clic en <1>«Acepto»</1>, acepta los <3>Términos y condiciones</3> del intercambio de tokens con Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Hemos modificado nuestros Términos del servicio. Al hacer clic en <1>«Acepto»</1>, acepta nuestros nuevos <3>Términos del servicio</3>.<5></5><6></6>Nuestros nuevos Términos del servicio incluyen una nueva <8>estructura de comisiones</8> para determinados productos.", "termsOfServicePrimaryText": "Condiciones del servicio", "tokenRowUnknownToken": "Token desconocido", "transactionsAppInteraction": "Interacción con la aplicación", "transactionsFailedAppInteraction": "Interacción fallida con la aplicación", "transactionsBidOnInterpolated": "<PERSON><PERSON><PERSON> por {{name}}", "transactionsBidFailed": "<PERSON><PERSON><PERSON> fallida", "transactionsBoughtInterpolated": "<PERSON><PERSON><PERSON><PERSON> {{name}}", "transactionsBoughtCollectible": "Coleccionable comprado", "transactionBridgeInitiated": "Puente iniciado", "transactionBridgeInitiatedFailed": "Error al iniciar el puente", "transactionBridgeStatusLink": "Comprobar estado en LI.FI", "transactionsBuyFailed": "Compra fallida", "transactionsBurnedSpam": "<PERSON><PERSON> quemado", "transactionsBurned": "<PERSON><PERSON><PERSON>", "transactionsUnwrapped": "Desenvuelto", "transactionsUnwrappedFailed": "<PERSON><PERSON>r al desenvolver", "transactionsCancelBidOnInterpolated": "Oferta cancelada en {{name}}", "transactionsCancelBidOnFailed": "No ha podido cancelar la oferta", "transactionsError": "Error", "transactionsFailed": "Fallo", "transactionsSwapped": "Intercambiado", "transactionsFailedSwap": "Intercambio fallido", "transactionsFailedBurn": "<PERSON><PERSON><PERSON>", "transactionsFrom": "De", "transactionsListedInterpolated": "Listado {{name}}", "transactionsListedFailed": "No se ha podido listar", "transactionsNoActivity": "Sin actividad", "transactionsReceived": "Recibido", "transactionsReceivedInterpolated": "Recibido: {{amount}} SOL", "transactionsSending": "Enviando...", "transactionsPendingCreateListingInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsPendingEditListingInterpolated": "<PERSON>ando {{name}}", "transactionsPendingSolanaPayTransaction": "Confirmación de la transacción de Solana Pay", "transactionsPendingRemoveListingInterpolated": "Quitando {{name}} de la lista", "transactionsPendingBurningInterpolated": "Quemand<PERSON> {{name}}", "transactionsPendingSending": "Enviando", "transactionsPendingSwapping": "Intercambiando", "transactionsPendingBridging": "<PERSON><PERSON><PERSON><PERSON>", "transactionsPendingApproving": "Apr<PERSON><PERSON><PERSON>", "transactionsPendingCreatingAndDelegatingStake": "Crear y delegar apuesta", "transactionsPendingDeactivatingStake": "Desactivar apuesta", "transactionsPendingDelegatingStake": "Delegar apuesta", "transactionsPendingWithdrawingStake": "Retirar apuesta", "transactionsPendingAppInteraction": "Interacción pendiente con la aplicación", "transactionsPendingBitcoinTransaction": "Transacción de BTC pendiente", "transactionsSent": "Enviado", "transactionsSendFailed": "Error al enviar", "transactionsSwapOn": "Intercambio en {{dappName}}", "transactionsSentInterpolated": "Enviado: {{amount}} SOL", "transactionsSoldInterpolated": "Vendido {{name}}", "transactionsSoldCollectible": "Coleccionable vendido", "transactionsSoldFailed": "<PERSON>enta fallida", "transactionsStaked": "Apostado", "transactionsStakedFailed": "Apuesta fallida", "transactionsSuccess": "Completado", "transactionsTo": "Para", "transactionsTokenSwap": "Intercambio de token", "transactionsUnknownAmount": "Desconocido", "transactionsUnlistedInterpolated": "Deslistado {{name}}", "transactionsUnstaked": "Sin apostar", "transactionsUnlistedFailed": "No se ha podido eliminar de la lista", "transactionsDeactivateStake": "Apuesta desactivada", "transactionsDeactivateStakeFailed": "Error al desactivar la apuesta", "transactionsWaitingForConfirmation": "Esperando confirmación", "transactionsWithdrawStake": "Retirar apuesta", "transactionsWithdrawStakeFailed": "Error al anular apuesta", "transactionCancelled": "Cancelado", "transactionCancelledFailed": "No se ha podido cancelar", "transactionApproveToken": "{{tokenSymbol}} aprobado", "transactionApproveTokenFailed": "No se ha podido aprobar {{tokenSymbol}}", "transactionApprovalFailed": "Aprobación fallida", "transactionRevokeApproveToken": "{{tokenSymbol}} revocado", "transactionRevokeApproveTokenFailed": "No se pudo revocar {{tokenSymbol}}", "transactionRevokeFailed": "Revocación fallida", "transactionApproveDetailsTitle": "Detalles de la aprobación", "transactionCancelOrder": "Cancelar pedido", "transactionCancelOrderFailed": "Error al cancelar el pedido", "transactionApproveAppLabel": "Aplicación", "transactionApproveAmountLabel": "Importe", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "Colección", "transactionApproveAllItems": "Aprobar todos los artículos", "transactionSpendUpTo": "Gastar hasta", "transactionCancel": "Cancelar transacción", "transactionPrioritizeCancel": "Priorizar la cancelación", "transactionSpeedUp": "Acelerar la transacción", "transactionCancelHelperText": "La transacción original puede completarse antes de ser cancelada.", "transactionSpeedUplHelperText": "Esto maximizará la velocidad de su transacción en función de las condiciones de la red.", "transactionCancelHelperMobile": "Costará <1>hasta {{amount}}</1> intentar cancelar esta transacción. La transacción original puede completarse antes de ser cancelada.", "transactionCancelHelperMobileWithEstimate": "Costará <1>hasta {{amount}}</1> intentar cancelar esta transacción. Debería completarse en aproximadamente {{timeEstimate}}. La transacción original puede completarse antes de ser cancelada.", "transactionSpeedUpHelperMobile": "Costará <1>hasta {{amount}}</1> maximizar la velocidad de esta transacción.", "transactionSpeedUpHelperMobileWithEstimate": "Costará <1>hasta {{amount}}</1> maximizar la velocidad de esta transacción. Debería completarse en aproximadamente {{timeEstimate}}.", "transactionEstimatedTime": "Tiempo estimado", "transactionCancelingSend": "Cancelando el envío", "transactionPrioritizingCancel": "Priorizando la cancelación", "transactionCanceling": "Cancelando", "transactionReplaceError": "Se ha producido un error. No se ha cargado ninguna comisión en su cuenta. Puede volver a intentarlo.", "transactionNotEnoughNative": "No hay suficientes {{nativeTokenSymbol}}", "transactionGasLimitError": "No se ha podido estimar el límite de gas", "transactionGasEstimationError": "No se ha podido estimar el gas", "pendingTransactionCancel": "<PERSON><PERSON><PERSON>", "pendingTransactionSpeedUp": "<PERSON><PERSON><PERSON>", "pendingTransactionStatus": "Estado", "pendingTransactionPending": "Pendiente", "pendingTransactionPendingInteraction": "Interacción pendiente", "pendingTransactionCancelling": "Cancelando", "pendingTransactionDate": "<PERSON><PERSON>", "pendingTransactionNetworkFee": "Comisión de la red", "pendingTransactionEstimatedTime": "Tiempo estimado", "pendingTransactionEstimatedTimeHM": "{{hours}} h {{minutes}} m", "pendingTransactionEstimatedTimeMS": "{{minutes}} m {{seconds}} s", "pendingTransactionEstimatedTimeS": "{{seconds}} s", "pendingTransactionsSendingTitle": "Enviando {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Desconocido", "pendingTransactionUnknownApp": "Aplicación desconocida", "permanentDelegateTitle": "Delegado", "permanentDelegateValue": "Permanente", "permanentDelegateTooltipTitle": "Delegación permanente", "permanentDelegateTooltipValue": "El Delegado permanente permite que otra cuenta gestione tokens en su nombre, incluido quemar o transferir.", "unlockActionButtonUnlock": "Desb<PERSON>que<PERSON>", "unlockEnterPassword": "Ingrese su contraseña", "unlockErrorIncorrectPassword": "Contrase<PERSON>", "unlockErrorSomethingWentWrong": "Algo ha ido mal, inténtelo de nuevo más tarde", "unlockForgotPassword": "¿Ha olvidado la contraseña?", "unlockPassword": "Contraseña", "forgotPasswordText": "Puede restablecer su contraseña introduciendo la frase de recuperación de 12 o 24 palabras de su billetera. Phantom no puede recuperar su contraseña por usted.", "appInfo": "Información sobre la aplicación", "lastUsed": "Último uso", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "No disponible con cuentas de hardware.", "trustedAppAutoConfirmDisclaimer1": "Mientras esté activa, Phantom confirmar<PERSON> todas las solicitudes de esta aplicación sin notificárselo ni pedirle confirmación.", "trustedAppAutoConfirmDisclaimer2": "Activarla puede poner sus fondos en riesgo de fraude. Utilice esta característica solo con aplicaciones en las que confíe.", "validationUtilsPasswordIsRequired": "Se requiere contraseña", "validationUtilsPasswordLength": "Contraseña de un mínimo de 8 caracteres", "validationUtilsPasswordsDontMatch": "Las contraseñas no coinciden", "validationUtilsPasswordCantBeSame": "No puede utilizar su antigua contraseña", "validatorCardEstimatedApy": "APY estimados", "validatorCardCommission": "Comisión", "validatorCardTotalStake": "Apuesta total", "validatorCardNumberOfDelegators": "n.º de delegados", "validatorListChooseAValidator": "Escoger un validador", "validatorListErrorFetching": "No hemos podido obtener los validadores. Vuelva a intentarlo más tarde.", "validatorListNoResults": "No hay resultados", "validatorListReload": "Recargar", "validatorInfoTooltip": "Validador", "validatorInfoTitle": "Validadores", "validatorInfoDescription": "Al apostar su SOL por un validador usted contribuye al rendimiento y la seguridad de la red Solana, todo ello mientras gana SOL a cambio.", "validatorApyInfoTooltip": "APY est.", "validatorApyInfoTitle": "APY estimado", "validatorApyInfoDescription": "Es la tasa de rentabilidad que gana por apostar su SOL al validador.", "validatorViewActionButtonStake": "Apostar", "validatorViewErrorFetching": "No se han podido recuperar validadores.", "validatorViewInsufficientBalance": "<PERSON><PERSON> insuficiente", "validatorViewMax": "Máx.", "validatorViewPrimaryText": "Iniciar apuesta", "validatorViewDescriptionInterpolated": "Seleccione cuántos SOL desea <1></1> conservar mediante «staking» con este validador. <3>Más información</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "Se requieren {{amount}} SOL para apostar", "validatorViewValidator": "Validador", "walletMenuItemsAddConnectWallet": "Añadir/vincular billetera", "walletMenuItemsBridgeAssets": "Puentear bienes", "walletMenuItemsHelpAndSupport": "Ayuda y soporte", "walletMenuItemsLockWallet": "Bloquear billetera", "walletMenuItemsResetSecretPhrase": "Restablecer la frase secreta", "walletMenuItemsShowMoreAccounts": "Mostrar {{count}} más...", "walletMenuItemsHideAccounts": "Ocultar cuentas", "toggleMultiChainHeader": "Multicadena", "disableMultiChainHeader": "<PERSON><PERSON> solo <PERSON>", "disableMultiChainDetail1Header": "A por todas con Solana", "disableMultiChainDetail1SecondaryText": "Gestione sus cuentas, tokens y coleccionables de Solana sin ver otras cadenas.", "disableMultiChainDetail2Header": "Volver a la multicadena en cualquier momento", "disableMultiChainDetail2SecondaryText": "Sus saldos existentes de Ethereum y Polygon se conservarán cuando vuelva a activar la multicadena.", "disableMultiChainButton": "Habilitar solo <PERSON>ana", "disabledMultiChainHeader": "Solo Solana habilitado", "disabledMultiChainText": "Puede volver a activar la multicadena en cualquier momento.", "enableMultiChainHeader": "Habilitar la multicadena", "enabledMultiChainHeader": "Multicadena habilitada", "enabledMultiChainText": "Ethereum y Polygon son ahora compatibles con su billetera.", "incompatibleAccountHeader": "Cuenta incompatible", "incompatibleAccountInterpolated": "Elimine estas cuentas solo Ethereum antes habilitando el modo solo Solana: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Novedades", "welcomeToMultiChainPrimaryText": "Una billetera para todo", "welcomeToMultiChainDetail1Header": "Compatible con Ethereum y Polygon", "welcomeToMultiChainDetail1SecondaryText": "Todos sus tokens y NFT de Solana, Ethereum y Polygon en un solo lugar.", "welcomeToMultiChainDetail2Header": "Utilice todas las aplicaciones que le gustan", "welcomeToMultiChainDetail2SecondaryText": "Conéctese a aplicaciones en varias cadenas sin cambiar de red.", "welcomeToMultiChainDetail3Header": "Importe su billetera MetaMask", "welcomeToMultiChainDetail3SecondaryText": "Importe fácilmente todas sus frases semilla a través de Ethereum y Polygon.", "welcomeToMultiChainIntro": "Le damos la bienvenida a Phantom Multichain", "welcomeToMultiChainIntroDesc": "Todos sus tokens y NFT de Solana, Ethereum y Polygon en un solo lugar. Su única cartera para todo.", "welcomeToMultiChainAccounts": "Cuentas multicadena rediseñadas", "welcomeToMultiChainAccountsDesc": "Rediseñada para multicadena, cada cuenta tiene ahora sus correspondientes direcciones ETH y Polygon.", "welcomeToMultiChainApps": "Funciona en todas partes", "welcomeToMultiChainAppsDesc": "Phantom es compatible con todas las aplicaciones de Ethereum, Polygon y Solana. Haga clic en «Conectar a MetaMask» y estará listo para empezar.", "welcomeToMultiChainImport": "Importe desde MetaMask, al instante", "welcomeToMultiChainImportDesc": "Importe sus frases secretas o claves privadas desde billeteras como MetaMask o Coinbase Wallet. Todo en un solo lugar.", "welcomeToMultiChainImportInterpolated": "<0>Importe sus frases secretas</0> o claves privadas desde billeteras como MetaMask o Coinbase Wallet. Todo en un solo lugar.", "welcomeToMultiChainTakeTour": "<PERSON>cer el recorrido", "welcomeToMultiChainSwapperTitle": "Intercambio en Ethereum,\nPolygon y Solana", "welcomeToMultiChainSwapperDetail1Header": "Compatible con Ethereum y Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Ahora puede intercambiar fácilmente tokens ERC-20 desde el interior de su billetera.", "welcomeToMultiChainSwapperDetail2Header": "Los mejores precios y comisiones superreducidas", "welcomeToMultiChainSwapperDetail2SecondaryText": "Más de 100 fuentes de liquidez y direccionamiento inteligente de órdenes para obtener la máxima rentabilidad.", "networkErrorTitle": "Error de red", "networkError": "Lamentablemente, no podemos acceder a la red. Inténtelo de nuevo más tarde.", "authenticationUnlockPhantom": "Desbloquear Phantom", "errorAndOfflineSomethingWentWrong": "Algo salió mal", "errorAndOfflineSomethingWentWrongTryAgain": "Inténtelo de nuevo.", "errorAndOfflineUnableToFetchAssets": "No hemos podido recuperar los activos. Inténtelo de nuevo más tarde.", "errorAndOfflineUnableToFetchCollectibles": "No hemos podido recuperar los objetos coleccionables. Inténtelo de nuevo más tarde.", "errorAndOfflineUnableToFetchSwap": "No hemos podido recuperar la información del intercambio. Inténtelo de nuevo más tarde.", "errorAndOfflineUnableToFetchTransactionHistory": "No podemos obtener su historial de transacciones en este momento. Compruebe su conexión a la red o inténtelo más tarde.", "errorAndOfflineUnableToFetchRewardsHistory": "No hemos podido recuperar el historial de recompensas. Vuelva a intentarlo más tarde.", "errorAndOfflineUnableToFetchBlockedUsers": "No hemos podido recuperar a los usuarios bloqueados. Inténtelo de nuevo más tarde.", "networkHealthSheetCloseButtonText": "Aceptar", "swapReviewError": "Algo salió mal al revisar su pedido. Inténtelo de nuevo.", "sendSelectToken": "Se<PERSON><PERSON><PERSON><PERSON> token", "swapBalance": "Saldo:", "swapTitle": "Intercambiar tokens", "swapSelectToken": "Se<PERSON><PERSON><PERSON><PERSON> token", "swapYouPay": "Usted paga", "swapYouReceive": "Usted recibe", "aboutPrivacyPolicy": "Política de privacidad", "aboutVersion": "Versión {{version}}", "aboutVisitWebsite": "Visitar el sitio web", "bottomSheetConnectTitle": "Conectar", "A11YbottomSheetConnectTitle": "Hoja inferior - Conectar", "A11YbottomSheetCommandClose": "Hoja inferior - <PERSON><PERSON><PERSON>", "A11YbottomSheetCommandBack": "Hoja inferior - Atrás", "bottomSheetSignTypedDataTitle": "<PERSON><PERSON><PERSON>", "bottomSheetSignMessageTitle": "<PERSON><PERSON><PERSON>", "bottomSheetSignInTitle": "In<PERSON><PERSON>", "bottomSheetSignInAndConnectTitle": "In<PERSON><PERSON>", "bottomSheetConfirmTransactionTitle": "Confirmar transacción", "bottomSheetConfirmTransactionsTitle": "Confirmar transacciones", "bottomSheetSolanaPayTitle": "Solicitud de pago de Solana", "bottomSheetAdvancedTitle": "<PERSON><PERSON><PERSON>", "bottomSheetReadOnlyAccountTitle": "Modo de solo visualización", "bottomSheetTransactionSettingsTitle": "Comisión de la red", "bottomSheetConnectDescription": "La conexión permitirá a este sitio ver los saldos y la actividad de la cuenta seleccionada.", "bottomSheetSignInDescription": "La firma de este mensaje demostrará que usted es el propietario de la cuenta seleccionada. Firme únicamente mensajes de aplicaciones en las que confíe.", "bottomSheetSignInAndConnectDescription": "La aprobación permitirá a este sitio ver los saldos y la actividad de la cuenta seleccionada.", "bottomSheetConfirmTransactionDescription": "Los cambios de saldo son estimados. Los importes y los activos implicados no están garantizados.", "bottomSheetConfirmTransactionsDescription": "Los cambios de saldo son estimados. Los importes y los activos implicados no están garantizados.", "bottomSheetSignTypedDataDescription": "Se trata únicamente de una solicitud de permiso. Es posible que la transacción no se ejecute inmediatamente.", "bottomSheetSignTypedDataSecondDescription": "Los cambios de saldo son estimados. Los importes y los activos implicados no están garantizados.", "bottomSheetSignMessageDescription": "La firma de este mensaje demostrará que usted es el propietario de la cuenta seleccionada. Firme únicamente mensajes de aplicaciones en las que confíe.", "bottomSheetReadOnlyAccountDescription": "No se puede realizar esta acción en modo solo vista.", "bottomSheetMessageRow": "Men<PERSON><PERSON>", "bottomSheetStatementRow": "Extracto", "bottomSheetAutoConfirmRow": "Confirmación automática", "bottomSheetAutoConfirmOff": "Desactivada", "bottomSheetAutoConfirmOn": "Activada", "bottomSheetAccountRow": "C<PERSON><PERSON>", "bottomSheetAdvancedRow": "<PERSON><PERSON><PERSON>", "bottomSheetContractRow": "Dirección contractual", "bottomSheetSpenderRow": "Dirección del gastador", "bottomSheetNetworkRow": "Red", "bottomSheetNetworkFeeRow": "Comisión de la red", "bottomSheetEstimatedTimeRow": "Tiempo estimado", "bottomSheetAccountRowDefaultAccountName": "C<PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Conectarse solo a sitios web de su confianza", "bottomSheetSignInRequestDisclaimer": "Inicie sesión solo en sitios web en los que confíe", "bottomSheetSignatureRequestDisclaimer": "Confirme solo si confía en este sitio web.", "bottomSheetFeaturedTransactionDisclaimer": "Verá una vista previa de la transacción antes de confirmarla en el paso siguiente.", "bottomSheetIgnoreWarning": "Ignorar la advertencia, proceder de todos modos", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "No se han encontrado cambios en el saldo. Proceda con precaución y confirme solo si confía en este sitio.", "bottomSheetReadOnlyWarning": "Solo está viendo esta dirección. Necesitará importar la frase secreta para poder firmar transacciones y mensajes.", "bottomSheetWebsiteIsUnsafeWarning": "Este sitio web no es seguro y pueden intentar robarle sus fondos.", "bottomSheetViewOnExplorer": "Ver en el explorador", "bottomSheetTransactionSubmitted": "Transacción enviada", "bottomSheetTransactionPending": "Transacción pendiente", "bottomSheetTransactionFailed": "La transacción ha fallado", "bottomSheetTransactionSubmittedDescription": "Se ha enviado su transacción. Puede verla en el explorador.", "bottomSheetTransactionFailedDescription": "Su transacción ha fallado. Inténtelo de nuevo.", "bottomSheetTransactionPendingDescription": "La transacción se está procesando...", "transactionsFromInterpolated": "De: {{from}}", "transactionsFromParagraphInterpolated": "De: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "Hoy", "transactionsToInterpolated": "Para: {{to}}", "transactionsToParagraphInterpolated": "Para: {{to}}", "transactionsYesterday": "Ayer", "addEditAddressAdd": "<PERSON><PERSON><PERSON>", "addEditAddressDelete": "Eliminar dirección", "addEditAddressDeleteTitle": "¿Seguro que quiere eliminar esta dirección?", "addEditAddressSave": "Guardar dirección", "dAppBrowserComingSoon": "¡Navegador dApp próximamente!", "dAppBrowserSearchPlaceholder": "Sitios, tokens, URL", "dAppBrowserOpenInNewTab": "Abrir en nueva pestaña", "dAppBrowserSuggested": "Sugerido", "dAppBrowserFavorites": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserBookmarks": "Marcadores", "dAppBrowserBookmarkAdd": "<PERSON><PERSON><PERSON>", "dAppBrowserBookmarkRemove": "Eliminar marcador", "dAppBrowserUsers": "Usuarios", "dAppBrowserRecents": "Recientes", "dAppBrowserFavoritesDescription": "Sus favoritos se mostrarán aquí", "dAppBrowserBookmarksDescription": "Sus marcadores se mostrarán aquí", "dAppBrowserRecentsDescription": "Las dapps conectadas recientemente aparecerán aquí", "dAppBrowserEmptyScreenDescription": "Escriba una URL o busque en la web", "dAppBrowserBlocklistScreenTitle": "¡{{origin}} está bloqueado! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom cree que este sitio web es malicioso y usarlo resulta inseguro.", "part2": "Este sitio ha sido marcado como parte de una base de datos mantenida por la comunidad de estafas y sitios web de suplantación de identidad conocidos. Si cree que este sitio ha sido erróneamente marcado, presente una incidencia."}, "dAppBrowserLoadFailedScreenTitle": "Error al cargar", "dAppBrowserLoadFailedScreenDescription": "Se ha producido un error al cargar esta página", "dAppBrowserBlocklistScreenIgnoreButton": "Ignorar la advertencia, mostrar de todos modos", "dAppBrowserActionBookmark": "Marcar como favorito", "dAppBrowserActionRemoveBookmark": "Eliminar marcador", "dAppBrowserActionRefresh": "Actualizar", "dAppBrowserActionShare": "Compartir", "dAppBrowserActionCloseTab": "<PERSON><PERSON>r pesta<PERSON>", "dAppBrowserActionEndAutoConfirm": "Finalizar autoconfirmación", "dAppBrowserActionDisconnectApp": "Desconectar aplicación", "dAppBrowserActionCloseAllTabs": "<PERSON><PERSON>r todas las pestañas", "dAppBrowserNavigationAddressPlaceholder": "Escriba una URL para buscar", "dAppBrowserTabOverviewMore": "Más", "dAppBrowserTabOverviewAddTab": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON>r pesta<PERSON>", "dAppBrowserClose": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewRemoveBookmark": "Eliminar marcador", "depositAssetListSuggestions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "depositUndefinedToken": "Lo sentimos, no es posible depositar este token", "onboardingImportRecoveryPhraseDetails": "Detalles", "onboardingCreateRecoveryPhraseVerifyTitle": "¿Ha escrito la Frase secreta de recuperación?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Sin la frase secreta de recuperación no podrá acceder a su clave ni a los activos asociados a ella.", "onboardingCreateRecoveryPhraseVerifyYes": "Sí", "onboardingCreateRecoveryPhraseErrorTitle": "Error", "onboardingCreateRecoveryPhraseErrorSubtitle": "No hemos podido generar una cuenta, inténtelo de nuevo.", "onboardingDoneDescription": "Ahora puede disfrutar plenamente de su billetera.", "onboardingDoneGetStarted": "Empezar", "zeroBalanceHeading": "¡Empecemos!", "zeroBalanceBuyCryptoTitle": "Comp<PERSON> criptom<PERSON>a", "zeroBalanceBuyCryptoDescription": "Compre su primera criptomoneda con una tarjeta de débito o de crédito.", "zeroBalanceDepositTitle": "Transferir criptomoneda", "zeroBalanceDepositDescription": "Deposite cripto desde otra billetera o bolsa.", "onboardingImportAccountsEmptyResult": "No se han encontrado cuentas", "onboardingImportAccountsAccountName": "Cuenta {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "Cuenta social", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Hemos encontrado {{numberOfWallets}} cuenta con actividad", "onboardingImportAccountsFoundAccounts_other": "Hemos encontrado {{numberOfWallets}} cuentas con actividad", "onboardingImportAccountsFoundAccountsNoActivity_one": "Hemos encontrado {{numberOfWallets}} cuenta", "onboardingImportAccountsFoundAccountsNoActivity_other": "Hemos encontrado {{numberOfWallets}} cuentas", "onboardingImportRecoveryPhraseLessThanTwelve": "La frase debe tener al menos 12 palabras.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "La frase debe tener exactamente 12 o 24 palabras.", "onboardingImportRecoveryPhraseWrongWord": "Palabras incorrectas: {{ words }}.", "onboardingProtectTitle": "Proteja su billetera", "onboardingProtectDescription": "Añadir seguridad biométrica le garantizará que usted es el único que puede acceder a su billetera.", "onboardingProtectButtonHeadlineDevice": "Dispositivo", "onboardingProtectButtonHeadlineFaceID": "ID facial", "onboardingProtectButtonHeadlineFingerprint": "<PERSON><PERSON> dactilar", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Utilizar la autenticación {{ authType }}", "onboardingProtectError": "Algo ha ido mal durante la autentificación. Inténtelo de nuevo.", "onboardingProtectBiometryIosError": "La autenticación biométrica está configurada en Phantom pero desactivada en los Ajustes del sistema. Abra Ajustes > Phantom > Face ID o Touch ID para volver a habilitarla.", "onboardingProtectRemoveAuth": "Deshabilitar autenticación", "onboardingProtectRemoveAuthDescription": "¿Seguro que quiere deshabilitar la autenticación?", "onboardingWelcomeTitle": "Le damos la bienvenida a Phantom", "onboardingWelcomeDescription": "<PERSON> empezar, cree una nueva billetera o importe una existente.", "onboardingWelcomeCreateWallet": "Crear nueva billetera", "onboardingWelcomeAlreadyHaveWallet": "Ya tengo una billetera", "onboardingWelcomeConnectSeedVault": "Conectar la Bóveda de Semillas", "onboardingSlide1Title": "Controlado por usted", "onboardingSlide1Description": "Su billetera está asegurada con acceso biométrico, detección de estafas y asistencia las 24 horas del día, los 7 días de la semana.", "onboardingSlide2Title": "El mejor hogar para\nsus NFTs", "onboardingSlide2Description": "Gestione listados, queme spam y manténgase al día con útiles notificaciones push.", "onboardingSlide3Title": "Haga más con sus tokens", "onboardingSlide3Description": "Almacene, intercambie, apueste, envíe y reciba sin salir de su billetera. ", "onboardingSlide4Title": "Descubra lo mejor de Web3", "onboardingSlide4Description": "Encuentre y conéctese a las principales aplicaciones y colecciones con el browser integrado en la aplicación.", "onboardingMultichainSlide5Title": "Una cartera para todo", "onboardingMultichainSlide5Description": "Experimente todo Solana, Ethereum y Polygon en una única interfaz fácil de usar.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Experimente todo lo relacionado con Solana, Ethereum, Polygon y Bitcoin en una única interfaz fácil de usar.", "requireAuth": "Requiere autenticación", "requireAuthImmediately": "Inmediatamente", "availableToSend": "Disponible para enviar", "sendEnterAmount": "Introducir importe", "sendEditMemo": "Editar memo", "sendShowLogs": "Mostrar registros de error", "sendHideLogs": "Ocultar registros de error", "sendGoBack": "Volver", "sendTransactionSuccess": "Los tokens se han enviado con éxito a", "sendInputPlaceholder": "@nombre_de_usuario o dirección", "sendInputPlaceholderV2": "nombre_de_usuario o dirección", "sendPeopleTitle": "Personas", "sendDomainTitle": "<PERSON><PERSON><PERSON>", "sendFollowing": "<PERSON><PERSON><PERSON><PERSON>", "sendRecentlyUsedAddressLabel": "Se usó hace {{formattedTimestamp}}", "sendRecipientAddress": "Dirección del destinatario", "sendTokenInterpolated": "Enviar {{tokenSymbol}}", "sendPasteFromClipboard": "<PERSON>egar desde el portapapeles", "sendScanQR": "Escanear código QR", "sendTo": "Para:", "sendRecipientZeroBalanceWarning": "Esta dirección de billetera no tiene saldo y no aparece en su historial de transacciones recientes. Asegúrese de que la dirección es correcta.", "sendUnknownAddressWarning": "Esta no es una dirección con la que haya interactuado recientemente. Proceda con precaución.", "sendSameAddressWarning": "Esta es su dirección actual. El envío conllevará comisiones por transferencia sin otros cambios de saldo.", "sendMintAccountWarning": "Esta es una dirección de cuenta de acuñación. No puede enviar fondos a esta dirección, ya que supondrá una pérdida permanente.", "sendCameraAccess": "Acceso a la cámara", "sendCameraAccessSubtitle": "Para escanear un código QR, es necesario habilitar el acceso a la cámara. ¿Desea abrir los Ajustes ahora?", "sendSettings": "<PERSON><PERSON><PERSON><PERSON>", "sendOK": "Aceptar", "invalidQRCode": "Este código QR no es válido.", "sendInvalidQRCode": "Este código QR no es una dirección válida", "sendInvalidQRCodeSubtitle": "Inténtelo de nuevo o con otro código QR.", "sendInvalidQRCodeSplToken": "Token no válido en el código QR", "sendInvalidQRCodeSplTokenSubtitle": "Este código QR contiene una ficha que no posee o que no podemos identificar.", "sendScanAddressToSend": "Escanee la dirección {{tokenSymbol}} para enviar fondos", "sendScanAddressToSendNoSymbol": "Escanear dirección para enviar fondos", "sendScanAddressToSendCollectible": "Escanee la dirección de SOL para enviar el coleccionable", "sendScanAddressToSendCollectibleMultichain": "Escanee la dirección para enviar el coleccionable", "sendSummary": "Resumen", "sendUndefinedToken": "Lo siento, no se pudo enviar este token", "sendNoTokens": "No hay tokens disponibles", "noBuyOptionsAvailableInCountry": "No hay opciones de compra disponibles en su país", "swapAvailableTokenDisclaimer": "Se dispone de un número limitado de tokens para puentear entre redes", "swapCrossSwapNetworkTooltipTitle": "Intercambiar entre redes", "swapCrossSwapNetworkTooltipDescription": "Al intercambiar entre redes, se recomienda utilizar los tokens disponibles para obtener el precio más bajo y las transacciones más rápidas.", "settingsAbout": "Acerca de Phantom", "settingsShareAppWithFriends": "Invite a sus amigos", "settingsConfirm": "Sí", "settingsMakeSureNoOneIsWatching": "Asegúrese de que nadie está mirando su pantalla", "settingsManageAccounts": "Gestión de cuentas", "settingsPrompt": "¿Seguro que quiere continuar?", "settingsSelectAvatar": "Seleccionar avatar", "settingsSelectSecretPhrase": "Seleccionar Frase secreta", "settingsShowPrivateKey": "Pulse para revelar su clave privada", "settingsShowRecoveryPhrase": "Pulse para revelar su frase secreta", "settingsSubmitBetaFeedback": "Envíe sus comentarios sobre la versión beta", "settingsUpdateAccountNameToast": "Nombre de la cuenta actualizado", "settingsUpdateAvatarToast": "Avatar actualizado", "settingsUpdateAvatarToastFailure": "¡No se ha podido actualizar el avatar!", "settingsWalletAddress": "Dirección de la cuenta", "settingsWalletAddresses": "Direcciones de cuenta", "settingsWalletNamePrimary": "Nombre de la cuenta", "settingsPlaceholderName": "Nombre", "settingsWalletNameSecondary": "Cambiar el nombre de su billetera", "settingsYourAccounts": "Sus cuentas", "settingsYourAccountsMultiChain": "Multi-red", "settingsReportUser": "Denunciar usuario", "settingsNotifications": "Notificaciones", "settingsNotificationPreferences": "Preferencias de notificaciones", "pushNotificationsPreferencesAllowNotifications": "Permitir notificaciones", "pushNotificationsPreferencesSentTokens": "Tokens enviados", "pushNotificationsPreferencesSentTokensDescription": "Transferencias salientes de tokens y NFT", "pushNotificationsPreferencesReceivedTokens": "Tokens recibidos", "pushNotificationsPreferencesReceivedTokensDescription": "Transferencias entrantes de tokens y NFT", "pushNotificationsPreferencesDexSwap": "Intercambios", "pushNotificationsPreferencesDexSwapDescription": "Intercambios en aplicaciones reconocidas", "pushNotificationsPreferencesOtherBalanceChanges": "Otros cambios en el saldo", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Otras transacciones con varios tokens que afectan a su saldo", "pushNotificationsPreferencesPhantomMarketing": "Actualizaciones de Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Anuncios de características y actualizaciones generales", "pushNotificationsPreferencesDescription": "Estos ajustes controlan las notificaciones para esta billetera activa. Cada billetera tiene su propia configuración de notificaciones. Para desactivar todas las notificaciones de Phantom, vaya a los <1>ajustes del dispositivo</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "No se pueden sincronizar las preferencias de notificación.", "connectSeedVaultConnectSeed": "Conectar una Semilla", "connectSeedVaultConnectSeedDescription": "Conecte Phantom a la Bóveda de Semillas en su teléfono", "connectSeedVaultSelectAnAccount": "Seleccione una cuenta", "connectSeedVaultSelectASeed": "Seleccione una Semilla", "connectSeedVaultSelectASeedDescription": "Elija a qué semilla desea conectarse a Phantom", "connectSeedVaultSelectAnAccountDescription": "Elija la cuenta que desea configurar con Phantom", "connectSeedVaultNoAccountsFound": "No se han encontrado cuentas.", "connectSeedVaultSelectAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON> cuenta<PERSON>", "connectSeedVaultSelectAccountsDescription": "Elija las cuentas que desea configurar con Phantom", "connectSeedVaultCompleteSetup": "Completar la configuración", "connectSeedVaultCompleteSetupDescription": "¡Ya está todo listo! Explore web3 con Phantom y utilice su Bóveda de Semillas para confirmar las transacciones", "connectSeedVaultConnectAnotherSeed": "Conectar otra Semilla", "connectSeedVaultConnectAllSeedsConnected": "Todas las semillas conectadas", "connectSeedVaultNoSeedsConnected": "No hay semillas conectadas. Pulse el botón de abajo para autorizar desde la Bóveda de Semillas.", "connectSeedVaultConnectAccount": "Conectar cuenta", "connectSeedVaultLoadMore": "<PERSON>gar más", "connectSeedVaultNeedPermission": "Se requiere permiso", "connectSeedVaultNeedPermissionDescription": "Vaya a Ajustes para permitir que Phantom utilice los permisos de Seed Vault.", "stakeApy": "APY {{apyPercentage}}", "stakeFee": "Comisión del {{fee}}", "stakeAmount": "Importe", "stakeAmountBalance": "<PERSON><PERSON>", "swapTopQuotes": "Los {{numQuotes}} mejores presupuestos", "swapTopQuotesTitle": "Los mejores presupuestos", "swapProvidersTitle": "<PERSON>veed<PERSON>", "swapProvidersFee": "Comisión del {{fee}}", "swapProvidersTagRecommended": "Mejor rentabilidad", "swapProvidersTagFastest": "<PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}} h {{minutes}} m", "swapProviderEstimatedTimeM": "{{minutes}} m", "swapProviderEstimatedTimeS": "{{seconds}} s", "stakeReview": "Rev<PERSON><PERSON>", "stakeReviewAccount": "C<PERSON><PERSON>", "stakeReviewCommissionFee": "Comisión", "stakeReviewConfirm": "Confirmar", "stakeReviewValidator": "Validador", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Error en la conversión de la apuesta", "convertStakeStatusErrorMessage": "Tu apuesta no se ha podido convertir a {{poolTokenSymbol}}. Vuelve a intentarlo.", "convertStakeStatusLoadingTitle": "Convirtiendo a {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Estamos iniciando el proceso para convertir tu {{stakedTokenSymbol}} en {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "¡Conversión a {{poolTokenSymbol}} completada!", "convertStakeStatusSuccessMessage": "Gana premios adicionales con tu JitoSOL <1>aquí.</1>", "convertStakeStatusConvertMore": "Convertir más", "convertStakePendingTitle": "Convertir Stake en {{symbol}}", "convertToJitoSOL": "Convertir a JitoSOL", "convertToJitoSOLInfoDescription": "Convierta su SOL en JitoSOL para ganar recompensas y participar en el ecosistema Jito.", "convertToJitoSOLInfoTitle": "Convertir a JitoSOL", "convertStakeBannerTitle": "Convierta su stake en JitoSOL para aumentar las recompensas hasta un 15 %", "convertStakeQuestBannerTitle": "Convierta el SOL apostado en JitoSOL y gane recompensas. Obtenga más información.", "liquidStakeConvertInfoTitle": "Convertir a JitoSOL", "liquidStakeConvertInfoDescription": "Aumente sus recompensas convirtiendo su stake SOL en JitoSOL. <1>Más información</1>", "liquidStakeConvertInfoFeature1Title": "¿Por qué apostar con Ji<PERSON>?", "liquidStakeConvertInfoFeature1Description": "Deposite para obtener JitoSOL, que crece con su apuesta. Utilícelo en los protocolos DeFi para obtener ganancias adicionales. Intercambie su JitoSOL más tarde por su importe inicial + las recompensas acumuladas", "liquidStakeConvertInfoFeature2Title": "Recompensas medias más altas", "liquidStakeConvertInfoFeature2Description": "Jito reparte su SOL entre los mejores validadores con las comisiones más bajas. Las recompensas MEV aumentan aún más sus ganancias.", "liquidStakeConvertInfoFeature3Title": "Apoyar la red Solana", "liquidStakeConvertInfoFeature3Description": "La apuesta líquida asegura Solana repartiendo la apuesta entre varios validadores, lo que reduce el riesgo de validadores con poco tiempo de actividad.", "liquidStakeConvertInfoSecondaryButton": "<PERSON>ora no", "liquidStakeStartStaking": "Iniciar apuesta", "liquidStakeReviewOrder": "<PERSON><PERSON>r pedido", "convertStakeAccountListPageIneligibleSectionTitle": "Cuentas de Stake no admisibles", "convertStakeAccountIneligibleBottomSheetTitle": "Cuentas de Stake no admisibles", "convertStakeAccountListPageErrorTitle": "Error al recuperar cuentas de stake", "convertStakeAccountListPageErrorDescription": "Lo sentimo<PERSON>, algo ha ido mal y no hemos podido recuperar las cuentas de stake", "liquidStakeReviewYouPay": "Usted paga", "liquidStakeReviewYouReceive": "Usted recibe", "liquidStakeReviewProvider": "<PERSON><PERSON><PERSON><PERSON>", "liquidStakeReviewNetworkFee": "Comisión de la red", "liquidStakeReviewPageTitle": "Confirmación", "liquidStakeReviewConversionFootnote": "<PERSON><PERSON><PERSON> hace «stake» de tokens de Solana a cambio de JitoSOL, recibirá una cantidad levemente inferior de JitoSOL. <1>Más información</1>", "convertStakeAccountIneligibleBottomSheetDescription": "El fondo de stake de Jito es compatible con la mayoría de los validadores Solana activos. No podrá convertir SOL apostados de validadores no admitidos a JitoSOL. Además, los SOL recién apostados tardan ~2 días antes de poder ser convertidos a JitoSOL.", "selectAValidator": "Seleccionar un Validador", "validatorSelectionListTitle": "Seleccionar un Validador", "validatorSelectionListDescription": "Elija un validador con el que apostar su SOL.", "stakeMethodDescription": "Devengue intereses usando sus tokens Solana para ayudar a crecer a Solana. <1>Más información</1>", "stakeMethodRecommended": "Recomendado", "stakeMethodEstApy": "APY est.: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Apuesta líquida", "stakeMethodSelectionLiquidStakingDescription": "Haga «stake» de SOL para ganar mayores recompensas, a<PERSON><PERSON> a proteger a Solana y recibir JitoSOL para obtener recompensas adicionales.", "stakeMethodSelectionNativeStakingTitle": "Apuesta nativa", "stakeMethodSelectionNativeStakingDescription": "Haga «stake» de SOL para recibir recompensas mientras ayuda a proteger a Solana.", "liquidStakeMintStakeSOL": "Apostar SOL", "mintJitoSOLInfoPageTitle": "Presentamos las Apuestas líquidas con Jito", "mintJitoSOLFeature1Title": "¿Por qué apostar con Ji<PERSON>?", "mintJitoSOLFeature1Description": "Deposite para obtener JitoSOL, que crece con su apuesta. Utilícelo en los protocolos DeFi para obtener ganancias adicionales. Intercambie su JitoSOL más tarde por su importe inicial + las recompensas acumuladas", "mintJitoSOLFeature2Title": "Recompensas medias más altas", "mintJitoSOLFeature2Description": "Jito reparte su SOL entre los mejores validadores con las comisiones más bajas. Las recompensas MEV aumentan aún más sus ganancias.", "mintJitoSOLFeature3Title": "Apoyar la red Solana", "mintJitoSOLFeature3Description": "La apuesta líquida asegura Solana repartiendo la apuesta entre varios validadores, lo que reduce el riesgo de validadores con poco tiempo de actividad.", "mintLiquidStakePendingTitle": "Acuñando «stake» líquido", "mintStakeStatusErrorTitle": "Error al acuñar «stake» líquido", "mintStakeStatusErrorMessage": "Su «stake» líquido de {{poolTokenSymbol}} no se ha podido acuñar. Inténtelo de nuevo.", "mintStakeStatusSuccessTitle": "¡Se ha completado la acuñación de «stake» líquido de {{poolTokenSymbol}}!", "mintStakeStatusLoadingTitle": "Acuñando «stake» líquido de {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "Estamos comenzando el proceso de acuñación de su «stake» líquido de {{poolTokenSymbol}}.", "mintLiquidStakeAmountDescription": "<PERSON><PERSON>to SOL le gustaría poner en «stake» con Jito", "mintLiquidStakeAmountProvider": "<PERSON><PERSON><PERSON><PERSON>", "mintLiquidStakeAmountApy": "APY est.", "mintLiquidStakeAmountBestPrice": "Precio", "mintLiquidStakeAmountInsufficientBalance": "<PERSON><PERSON> insuficiente", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} requerido para «stake»", "swapTooltipGotIt": "Entendido", "swapTabInsufficientFunds": "Fondos insuficientes", "swapNoAssetsFound": "Ningún activo", "swapNoTokensFound": "No se han encontrado tokens", "swapConfirmationTryAgain": "Reintentar", "swapConfirmationGoBack": "Volver", "swapNoQuotesFound": "No se han encontrado presupuestos", "swapNotProviderFound": "No hemos podido encontrar un proveedor para este intercambio de tokens. Pruebe con otro token.", "swapAvailableOnMainnet": "Esta función sólo está disponible en Mainnet", "swapNotAvailableEVM": "Los intercambios aún no están disponibles para las cuentas de EVM", "swapNotAvailableOnSelectedNetwork": "Los intercambios no están disponibles en la red seleccionada", "singleChainSwapTab": "En la red", "crossChainSwapTab": "A través de las redes", "allFilter": "Todo", "bridgeRefuelTitle": "Repostar", "bridgeRefuelDescription": "Repostar e garantiza que podrá pagar las transacciones después de realizar el puente.", "bridgeRefuelLabelBalance": "Su {{symbol}}", "bridgeRefuelLabelReceive": "Usted recibe", "bridgeRefuelLabelFee": "Coste estimado", "bridgeRefuelDismiss": "Continuar sin repostar", "bridgeRefuelEnable": "Activar repostaje", "unwrapWrappedSolError": "<PERSON><PERSON>r al desenvolver", "unwrapWrappedSolLoading": "Desenvolviendo...", "unwrapWrappedSolSuccess": "Desenvuelto", "unwrapWrappedSolViewTransaction": "Ver transacción", "dappApprovePopupSignMessage": "<PERSON><PERSON><PERSON>", "solanaPayFrom": "De", "solanaPayMessage": "Men<PERSON><PERSON>", "solanaPayNetworkFee": "Comisión de la red", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Pagar {{item}}", "solanaPayPayNow": "<PERSON><PERSON> ahora", "solanaPaySending": "Enviando {{item}}", "solanaPayReceiving": "Recibiendo {{item}}", "solanaPayMinting": "Acuñando {{item}}", "solanaPayTransactionProcessing": "Su transacción se está procesando,\nespere.", "solanaPaySent": "¡Enviado!", "solanaPayReceived": "¡Recibido!", "solanaPayMinted": "¡Acuñado!", "solanaPaySentNFT": "¡NFT enviado!", "solanaPayReceivedNFT": "¡NFT recibido!", "solanaPayTokensSent": "Sus tokens se enviaron con éxito a {{to}}", "solanaPayTokensReceived": "Ha recibido nuevos tokens de {{from}}", "solanaPayViewTransaction": "Ver transacción", "solanaPayTransactionFailed": "La transacción ha fallado", "solanaPayConfirm": "Confirmar", "solanaPayTo": "para", "dappApproveConnectViewAccount": "Ver su cuenta de Solana", "deepLinkInvalidLink": "Enlace no válido", "deepLinkInvalidSplTokenSubtitle": "Contiene un token que no posee o que no podemos identificar.", "walletAvatarShowAllAccounts": "Mostrar todas las cuentas", "pushNotificationsGetInstantUpdates": "Obtener actualizaciones instantáneas", "pushNotificationsEnablePushNotifications": "Habilitar las notificaciones sobre transferencias completadas, intercambios y anuncios", "pushNotificationsEnable": "Habilitar", "pushNotificationsNotNow": "<PERSON>ora no", "onboardingAgreeToTermsOfServiceInterpolated": "Acepto los <1>Términos del servicio</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "Vale, la he guardado", "onboardingCreateNewWallet": "<PERSON><PERSON>r una nueva billetera", "onboardingErrorDuplicateSecretRecoveryPhrase": "Esta frase secreta ya existe en su billetera", "onboardingErrorInvalidSecretRecoveryPhrase": "Frase secreta de recuperación inválida", "onboardingFinished": "¡Ya está todo listo!", "onboardingImportAccounts": "Importar cuentas", "onboardingImportImportingAccounts": "Importando cuentas...", "onboardingImportImportingFindingAccounts": "Encontrar cuentas con actividad", "onboardingImportAccountsLastActive": "Activo hace {{formattedTimestamp}}", "onboardingImportAccountsNeverUsed": "Nunca usado", "onboardingImportAccountsCreateNew": "Nueva billetera", "onboardingImportAccountsDescription": "Elija las cuentas de billetera que desea importar", "onboardingImportReadOnlyAccountDescription": "Añada una dirección o un nombre de dominio que desee vigilar. Solo tendrá acceso de visualización y no podrá firmar transacciones ni mensajes.", "onboardingImportSecretRecoveryPhrase": "Importar frase secreta", "onboardingImportViewAccounts": "Ver cuentas", "onboardingRestoreExistingWallet": "Restaurar una billetera existente con su frase secreta de recuperación de 12 o 24 palabras", "onboardingShowUnusedAccounts": "Mostrar cuentas no utilizadas", "onboardingShowMoreAccounts": "Mostrar más cuentas", "onboardingHideUnusedAccounts": "Ocultar cuentas no utilizadas", "onboardingSecretRecoveryPhrase": "Frase secreta de recuperación", "onboardingSelectAccounts": "Seleccione sus cuentas", "onboardingStoreSecretRecoveryPhraseReminder": "Solo así podrá recuperar la cuenta. ¡Guárdela en un lugar seguro!", "useTokenMetasForMintsUnknownName": "Desconocido", "timeUnitMinute": "minuto", "timeUnitMinutes": "minutos", "timeUnitHour": "hora", "timeUnitHours": "horas", "espNFTListWithPrice": "Ha listado {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}} en {{dAppName}}", "espNFTListWithPriceWithoutDApp": "Ha listado {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "Ha listado {{NFTDisplayName}} en {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "Ha puesto en venta {{NFTDisplayName}}", "espNFTChangeListPriceWithPrice": "Ha actualizado el listado de {{NFTDisplayName}} a {{priceAmount}} {{priceTokenSymbol}} en {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Ha actualizado el listado de {{NFTDisplayName}} a {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "Ha actualizado el listado de {{NFTDisplayName}} en {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Ha actualizado el listado de {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Ha pujado {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}} en {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Ha ofrecido {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Ha pujado por {{NFTDisplayName}} en {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Ha hecho una oferta por {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Nueva puja de {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}} en {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Nueva oferta de {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Nueva puja por {{NFTDisplayName}} en {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Nueva oferta por {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Ha cancelado su puja de {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}} en {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Ha cancelado su oferta de {{priceAmount}} {{priceTokenSymbol}} por {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Ha cancelado su puja por {{NFTDisplayName}} en {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Ha cancelado su oferta por {{NFTDisplayName}}", "espNFTUnlist": "Ha anulado la lista de {{NFTDisplayName}} en {{dAppName}}", "espNFTUnlistWithoutDApp": "Ha anulado la lista de {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "Ha comprado {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}} en {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "Ha comprado {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "Ha comprado {{NFTDisplayName}} en {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Ha comprado {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Ha vendido {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}} en {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "Ha vendido {{NFTDisplayName}} por {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Ha vendido {{NFTDisplayName}} en {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "Ha vendido {{NFTDisplayName}}", "espDEXSwap": "Ha cambiado {{downTokensTextFragment}} por {{upTokensTextFragment}} en {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Ha depositado {{downTokensTextFragment}} en el fondo de liquidez {{poolDisplayName}} en {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "Ha cambiado {{downTokensTextFragment}} por {{upTokensTextFragment}} en {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Ha retirado {{upTokensTextFragment}} del fondo de liquidez {{poolDisplayName}} en {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "Ha cambiado {{downTokensTextFragment}} por {{upTokensTextFragment}} en {{dAppName}}", "espGenericTokenSend": "Ha enviado {{downTokensTextFragment}}", "espGenericTokenReceive": "Ha recibido {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "Ha cambiado {{downTokensTextFragment}} por {{upTokensTextFragment}}", "espUnknown": "DESCONOCIDO", "espUnknownNFT": "NFT desconocido", "espTextFragmentAnd": "y", "externalLinkWarningTitle": "Está a punto de abandoar Phantom", "externalLinkWarningDescription": "Y abrir {{url}}. Asegúrese de que confía en esta fuente antes de interactuar con ella.", "shortcutsWarningDescription": "Atajos proporcionados por {{url}}. Asegúrese de confiar en esta fuente antes de interactuar con ella.", "lowTpsBanner": "Solana está experimentando una congestión en la red", "lowTpsMessageTitle": "Congestión de la red Solana", "lowTpsMessage": "Debido a la alta congestión de Solana, sus transacciones pueden fallar o retrasarse. Reintente las transacciones fallidas.", "solanaSlow": "La red Solana está inusualmente lenta", "solanaNetworkTemporarilyDown": "La red Solana está temporalmente fuera de servicio", "waitForNetworkRestart": "Espere a que la red se reinicie. Sus fondos no se verán afectados.", "exploreCollectionsCarouselTitle": "Popular", "exploreDropsCarouselTitle": "Novedades", "exploreSortFloor": "<PERSON><PERSON>", "exploreSortListed": "Listado", "exploreSortVolume": "Volumen", "exploreFetchErrorSubtitle": "Inténtelo más tarde.", "exploreFetchErrorTitle": "Fallo en la búsqueda.", "exploreTopCollectionsTitle": "Principales colecciones de NFT", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "Más", "exploreSeeMore": "<PERSON>er más", "exploreTrendingTokens": "Tokens de tendencia", "exploreVolumeTokens": "Volumen más alto", "explorePriceChangeTokens": "Mayores ganancias", "explorePriceTokens": "Tokens por precio", "exploreMarketCapTokens": "Principales tokens", "exploreTrendingSites": "Sitios de tendencia", "exploreTopSites": "Principales sitios", "exploreTrendingCollections": "Colecciones de tendencia", "exploreTopCollections": "Principales colecciones", "collectiblesSearchCollectionsSection": "Colecciones", "collectiblesSearchItemsSection": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchNrOfItems": "{{ nrOfItems }} <PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchPlaceholderText": "Buscar sus coleccionables", "collectionPinSuccess": "Colección fijada", "collectionPinFail": "No se ha podido fijar la colección", "collectionUnpinSuccess": "Colección desfijada", "collectionUnpinFail": "No se ha podido desfijar la colección", "collectionHideSuccess": "Coleccionable ocultado", "collectionHideFail": "La recaudación no se ha podido ocultar", "collectionUnhideSuccess": "Coleccionable revelado", "collectionUnhideFail": "La recaudación no se ha podido revelar", "collectiblesSpamSuccess": "Denunciado como spam", "collectiblesSpamFail": "Error al denunciar como spam", "collectiblesSpamAndHiddenSuccess": "Denunciado como spam y ocultado", "collectiblesNotSpamSuccess": "Marcado como no spam", "collectiblesNotSpamFail": "Error al marcar como no spam", "collectiblesNotSpamAndUnhiddenSuccess": "Marcado como no spam y ocultación cancelada", "tokenPageSpamWarning": "Este token no está verificado. Interactúe únicamente con tokens en los que confíe.", "tokenSpamWarning": "Este token se ocultó porque Phantom cree que es spam.", "collectibleSpamWarning": "Este coleccionable se ocultó porque Phantom cree que es spam.", "collectionSpamWarning": "Estos coleccionables se ocultaron porque «Phantom» cree que son spam.", "emojiNoResults": "No se han encontrado emojis", "emojiSearchResults": "Resultados de búsqueda", "emojiSuggested": "Sugerido", "emojiSmileys": "Smileys y personas", "emojiAnimals": "Animales y naturaleza", "emojiFood": "<PERSON><PERSON><PERSON> y bebida", "emojiTravel": "Viajes y lugares", "emojiActivities": "Actividades", "emojiObjects": "<PERSON><PERSON><PERSON><PERSON>", "emojiSymbols": "<PERSON><PERSON><PERSON><PERSON>", "emojiFlags": "Banderas", "whichExtensionToConnectWith": "¿Con qué extensión desea conectarse?", "configureInSettings": "Configurable en Ajustes → Billetera de aplicaciones predeterminada.", "continueWith": "Continuar con", "useMetaMask": "<PERSON><PERSON>", "usePhantom": "Usar Phantom", "alwaysAsk": "Preguntar siempre", "dontAskMeAgain": "No volver a preguntar", "selectWalletSettingDescriptionLine1": "Puede que algunas aplicaciones no ofrezcan la opción de conectar con Phantom.", "selectWalletSettingDescriptionLinePhantom": "Como solución provisional, la conexión con MetaMask siempre abrirá Phantom en su lugar.", "selectWalletSettingDescriptionLineAlwaysAsk": "Como solución provisional, cuando se conecte con MetaMask, le preguntaremos si desea utilizar Phantom en su lugar.", "selectWalletSettingDescriptionLineMetaMask": "Si establece MetaMask como predeterminada, esas aplicaciones no podrán conectarse a Phantom.", "metaMaskOverride": "Billetera de aplicación predeterminada", "metaMaskOverrideSettingDescriptionLine1": "Para conectarse a sitios web que no ofrecen la opción de utilizar Phantom.", "refreshAndReconnectToast": "Actualice y vuelva a conectarse para aplicar sus cambios", "autoConfirmUnavailable": "No disponible", "autoConfirmReasonDappNotWhitelisted": "No disponible porque el contrato del que procede no está en nuestra lista de permitidos para esta aplicación.", "autoConfirmReasonSessionNotActive": "No disponible porque no hay ninguna sesión de autoconfirmación activa. Habilítela a continuación.", "autoConfirmReasonRateLimited": "No disponible porque la dapp que está utilizando está enviando demasiadas peticiones.", "autoConfirmReasonUnsupportedNetwork": "No disponible porque la autoconfirmación aún no admite esta red.", "autoConfirmReasonSimulationFailed": "No disponible porque no podemos garantizar la seguridad.", "autoConfirmReasonTabNotFocused": "No disponible porque la pestaña del dominio en el que intenta autoconfirmar no está activa.", "autoConfirmReasonNotUnlocked": "No disponible porque la billetera no estaba desbloqueada.", "rpcErrorUnauthorizedWrongAccount": "La dirección de origen de la transacción no coincide con la dirección de la cuenta seleccionada.", "rpcErrorUnauthorizedUnknownSource": "No se ha podido determinar el origen de la solicitud RPC.", "transactionsDisabledTitle": "Transacciones desactivadas", "transactionsDisabledMessage": "Su dirección no puede realizar transacciones con Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Activo", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL copiada en el portapapeles", "notEnoughSolScanTransactionWarning": "Esta transacción puede fallar debido a la insuficiencia de SOL en su cuenta. Añada más SOL a su cuenta e inténtelo de nuevo.", "transactionRevertedWarning": "Esta transacción se revirtió durante la simulación. Los fondos pueden perderse si se envían.", "slippageToleranceExceeded": "Esta transacción se revirtió durante la simulación. Tolerancia de deslizamiento superada.", "simulationWarningKnownMalicious": "Creemos que esta cuenta es maliciosa. Aprobarla puede conllevar la pérdida de fondos.", "simulationWarningPoisonedAddress": "Esta dirección es sospechosamente similar a una dirección a la que usted envió fondos recientemente. Confirme que es la dirección correcta para evitar perder fondos por una estafa.", "simulationWarningInteractingWithAccountWithoutActivity": "Se trata de una cuenta sin ninguna actividad previa. El envío de fondos a una cuenta inexistente puede dar lugar a la pérdida de fondos", "quests": "Misiones", "questsClaimInProgress": "Reclamación en curso", "questsVerifyingCompletion": "Verificación de la finalización de la misión", "questsClaimError": "Error al reclamar la recompensa", "questsClaimErrorDescription": "Se ha producido un error al reclamar su recompensa. Inténtelo de nuevo más tarde.", "questsBadgeMobileOnly": "Solo móvil", "questsBadgeExtensionOnly": "Solo extensión", "questsExplainerSheetButtonLabel": "Entendido", "questsNoQuestsAvailable": "No hay misiones disponibles", "questsNoQuestsAvailableDescription": "Actualmente no hay misiones disponibles. Le avisaremos en cuanto se añadan nuevas.", "exploreLearn": "Aprender", "exploreSites": "Sit<PERSON>", "exploreTokens": "Tokens", "exploreQuests": "Misiones", "exploreCollections": "Colecciones", "exploreFilterByall_networks": "Todas las redes", "exploreSortByrank": "Tendencias", "exploreSortBytrending": "Tendencias", "exploreSortByprice": "Precio", "exploreSortByprice-change": "Cambio de precio", "exploreSortBytop": "Mejo<PERSON>", "exploreSortByvolume": "Volumen", "exploreSortBygainers": "Ganadores", "exploreSortBylosers": "Perdedores", "exploreSortBymarket-cap": "Capitalización bursátil", "exploreSortBymarket_cap": "Capitalización bursátil", "exploreTimeFrame1h": "1h", "exploreTimeFrame24h": "24h", "exploreTimeFrame7d": "7d", "exploreTimeFrame30d": "30d", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Coleccionables", "exploreCategoryMarketplace": "<PERSON><PERSON><PERSON>", "exploreCategoryGaming": "<PERSON><PERSON><PERSON>", "exploreCategoryBridges": "<PERSON><PERSON><PERSON>", "exploreCategoryOther": "<PERSON><PERSON><PERSON>", "exploreCategorySocial": "Social", "exploreCategoryCommunity": "Comunidad", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Apuestas", "exploreCategoryArt": "Arte", "exploreCategoryTools": "Herramientas", "exploreCategoryDeveloperTools": "Herramientas de desarrollador", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Apuestas de NFT", "exploreCategoryExplorer": "Explorador", "exploreCategoryInscriptions": "Inscripciones", "exploreCategoryBridge": "Puente", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Comprobador de Airdrop", "exploreCategoryPoints": "Punt<PERSON>", "exploreCategoryQuests": "Misiones", "exploreCategoryShop": "Tienda", "exploreCategoryProtocol": "Protocolo", "exploreCategoryNamingService": "<PERSON><PERSON><PERSON>", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Seguimiento de carteras", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volumen", "exploreFloor": "<PERSON><PERSON>", "exploreCap": "Capitalización bursátil", "exploreToken": "Token", "explorePrice": "Precio", "explore24hVolume": "Volumen de 24h", "exploreErrorButtonText": "Inténtelo de nuevo", "exploreErrorDescription": "Se ha producido un error al intentar cargar el contenido de exploración. Inténtelo de nuevo", "exploreErrorTitle": "Error al cargar el contenido de la exploración", "exploreNetworkError": "Se ha producido un error en la red. Inténtelo de nuevo más tarde.", "exploreTokensLegalDisclaimer": "Los listados de tokens se generan utilizando datos de mercado proporcionados por varios proveedores externos, entre ellos CoinGecko, Birdeye y Jupiter. El rendimiento se basa en el periodo anterior de 24 horas. El rendimiento pasado no es indicativo del rendimiento futuro.", "swapperTokensLegalDisclaimer": "Los listados de tokens de tendencia se generan utilizando datos de mercado de varios proveedores externos, incluidos CoinGecko, Birdeye y Jupiter, y se basan en los tokens populares intercambiados por los usuarios de Phantom a través del Swapper durante el periodo de tiempo indicado. El rendimiento pasado no es indicativo del rendimiento futuro.", "exploreLearnErrorTitle": "Error al cargar el contenido de aprendizaje", "exploreLearnErrorDescription": "Se ha producido un error al intentar cargar el contenido de aprendizaje. Inténtelo de nuevo", "exploreShowMore": "Mostrar más", "exploreShowLess": "<PERSON><PERSON> menos", "exploreVisitSite": "Visitar sitio", "dappBrowserSearchScreenVisitSite": "Visitar sitio", "dappBrowserSearchScreenSearchWithGoogle": "Buscar con Google", "dappBrowserSearchScreenSearchLinkYouCopied": "Enlace que ha copiado", "dappBrowserExtSearchPlaceholder": "Buscar sitios, tokens", "dappBrowserSearchNoAppsTokens": "No se han encontrado aplicaciones ni tokens", "dappBrowserTabsLimitExceededScreenTitle": "¿Cerrar pestañas antiguas?", "dappBrowserTabsLimitExceededScreenDescription": "Tiene {{tabsCount}} pestañas abiertas. Para abrir más, deberá cerrar algunas pestañas.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON>r todas las pestañas", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: Este dominio no existe", "dappBrowserTabErrorHttp": "Bloqueado, utilice HTTPS", "dappBrowserTabError401Unauthorized": "401 No autorizado", "dappBrowserTabError501UnhandledRequest": "501 Solicitud no atendida", "dappBrowserTabErrorTimeout": "TIEMPO DE ESPERA: El servidor tardó demasiado en responder", "dappBrowserTabErrorInvalidResponse": "Respuesta no válida", "dappBrowserTabErrorEmptyResponse": "Respuesta vacía", "dappBrowserTabErrorGeneric": "Se ha producido un error", "localizedErrorUnknownError": "Algo ha ido mal, inténtelo de nuevo más tarde.", "localizedErrorUnsupportedCountry": "Lo sentimos, su país no se admite actualmente.", "localizedErrorTokensNotLoading": "Ha habido un problema al cargar sus tokens. Inténtelo de nuevo.", "localizedErrorSwapperNoQuotes": "No hay intercambios disponibles debido a que no se admite el par, hay poca liquidez o el importe es bajo. Intente ajustar el token o la cantidad.", "localizedErrorSwapperRefuelNoQuotes": "No se han encontrado presupuestos. Pruebe con otro token, cantidad o desactive el repostaje.", "localizedErrorInsufficientSellAmount": "Cantidad de token demasiado baja. Aumente el valor para intercambiar la cadena cruzada.", "localizedErrorCrossChainUnavailable": "Los intercambios entre cadenas no están disponibles en este momento, inténtelo de nuevo más tarde.", "localizedErrorTokenNotTradable": "Uno de los tokens seleccionados no es negociable. Seleccione otro token.", "localizedErrorCollectibleLocked": "La cuenta del token está bloqueada.", "localizedErrorCollectibleListed": "La cuenta del token está listada.", "spamActivityAction": "Ver elementos ocultos", "spamActivityTitle": "Actividad oculta", "spamActivityWarning": "Esta transacción se ocultó porque Phantom cree que puede ser spam.", "appAuthenticationFailed": "Fallo en la autenticación", "appAuthenticationFailedDescription": "Ha habido un problema con su intento de autenticación. Inténtelo de nuevo.", "partialErrorBalanceChainName": "Estamos teniendo problemas para actualizar sus saldos de {{chainName}}. Sus fondos están a salvo.", "partialErrorGeneric": "Estamos teniendo problemas para actualizar las redes, algunos de sus saldos de tokens y precios pueden estar desfasados. Sus fondos están a salvo.", "partialErrorTokenDetail": "Estamos teniendo problemas para actualizar su saldo de tokens. Sus fondos están a salvo.", "partialErrorTokenPrices": "Estamos teniendo problemas para actualizar sus precios de tokens. Sus fondos están a salvo.", "partialErrorTokensTrimmed": "Tenemos problemas para mostrar todos los tokens de su cartera. Sus fondos están a salvo.", "publicFungibleDetailAbout": "Acerca de", "publicFungibleDetailYourBalance": "Su saldo", "publicFungibleDetailInfo": "Información", "publicFungibleDetailShowMore": "<PERSON>er más", "publicFungibleDetailShowLess": "<PERSON><PERSON> menos", "publicFungibleDetailPerformance": "Rendimiento en 24 horas", "publicFungibleDetailSecurity": "Seguridad", "publicFungibleDetailMarketCap": "Capitalización bursátil", "publicFungibleDetailTotalSupply": "Suministro total", "publicFungibleDetailCirculatingSupply": "Suministro circulante", "publicFungibleDetailMaxSupply": "Suministro máxi<PERSON>", "publicFungibleDetailHolders": "Titulares", "publicFungibleDetailVolume": "Volumen", "publicFungibleDetailTrades": "Operaciones", "publicFungibleDetailTraders": "Operadores", "publicFungibleDetailUniqueWallets": "Billeteras únicas", "publicFungibleDetailTop10Holders": "Los 10 principales titulares", "publicFungibleDetailTop10HoldersTooltip": "Indica el porcentaje de la oferta total actual en manos de los 10 mayores poseedores del token. Es una medida de la facilidad con la que se puede manipular el precio.", "publicFungibleDetailMintable": "Acuñable", "publicFungibleDetailMintableTooltip": "El suministro de tokens se puede incrementar por el propietario del contrato si un token es acuñable.", "publicFungibleDetailMutableInfo": "Información mutable", "publicFungibleDetailMutableInfoTooltip": "Si la información del token, como el nombre, el logotipo y la dirección del sitio web, es mutable, el propietario del contrato puede modificarla.", "publicFungibleDetailOwnershipRenounced": "Renuncia a la propiedad", "publicFungibleDetailOwnershipRenouncedTooltip": "Si se renuncia a la propiedad del token, nadie podrá ejecutar funciones como acuñar más tokens.", "publicFungibleDetailUpdateAuthority": "Actualizar la autoridad", "publicFungibleDetailUpdateAuthorityTooltip": "La autoridad de actualización es la dirección de la billetera que puede cambiar la información si un token es mutable.", "publicFungibleDetailFreezeAuthority": "Autoridad de congelación", "publicFungibleDetailFreezeAuthorityTooltip": "La autoridad de congelación es la dirección de la billetera que puede impedir la transferencia de fondos.", "publicFungibleUnverifiedToken": "Este token no está verificado. Interactúe únicamente con tokens en los que confíe.", "publicFungibleDetailSwap": "Intercambiar {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "Intercambiar {{tokenSymbol}} con la aplicación Phantom", "publicFungibleDetailLinkCopied": "Copiado al portapapeles", "publicFungibleDetailContract": "Contrato", "publicFungibleDetailMint": "<PERSON><PERSON><PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "Actividad", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON>er más", "unifiedTokenDetailTransactionActivityError": "Error al cargar la actividad reciente", "additionalNetworksTitle": "Redes adicionales", "copyAddressRowAdditionalNetworks": "Redes adicionales", "copyAddressRowAdditionalNetworksHeader": "Las siguientes redes utilizan la misma dirección que Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Puede utilizar su dirección Ethereum de forma segura para enviar y recibir activos en cualquiera de estas redes.", "cpeUnknownError": "Error descon<PERSON>", "cpeUnknownInstructionError": "Error de instrucción desconocido", "cpeAccountFrozen": "La cuenta está congelada", "cpeAssetFrozen": "El activo está congelado", "cpeInsufficientFunds": "Fondos insuficientes", "cpeInvalidAuthority": "Autoridad no válida", "cpeBalanceBelowRent": "Saldo por debajo del umbral de renta exenta", "cpeNotApprovedForConfidentialTransfers": "Cuenta no aprobada para transferencias confidenciales", "cpeNotAcceptingDepositsOrTransfers": "Cuenta que no acepta depósitos ni transferencias", "cpeNoMemoButRequired": "No hay memo en la instrucción previa; se requiere para que el destinatario reciba una transferencia", "cpeTransferDisabledForMint": "La transferencia está desactivada para este acuñado", "cpeDepositAmountExceedsLimit": "El importe depositado supera el límite máximo", "cpeInsufficientFundsForRent": "Fondos insuficientes para el alquiler", "reportIssueScreenTitle": "Informar de un problema", "publicFungibleReportIssuePrompt": "¿Sobre qué problema desea informar {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "Información incorrecta", "publicFungibleReportIssuePriceStale": "El precio no se actualiza", "publicFungibleReportIssuePriceMissing": "Falta el precio", "publicFungibleReportIssuePerformanceIncorrect": "El rendimiento de 24h es incorrecto", "publicFungibleReportIssueLinkBroken": "Enlaces sociales no accesibles", "publicFungibleDetailErrorLoading": "Datos del token no disponibles", "reportUserPrompt": "¿Sobre qué cuestión desea denunciar a @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "Abuso y acoso", "reportUserOptionAbuseAndHarrassmentDescription": "Acoso selectivo, incitación al acoso, amenazas violentas, contenidos y referencias de odio", "reportUserOptionPrivacyAndImpersonationTitle": "Privacidad y suplantación de identidad", "reportUserOptionPrivacyAndImpersonationDescription": "Compartir o amenazar con exponer información privada, hacerse pasar por otra persona", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "Cuenta falsa, estafas, enlaces maliciosos", "reportUserSuccess": "Denuncia a usuario enviada.", "settingsClaimUsernameTitle": "Crear nombre de usuario", "settingsClaimUsernameDescription": "Una identidad tan única como su billetera", "settingsClaimUsernameValueProp1": "Identidad simplificada", "settingsClaimUsernameValueProp1Description": "Diga adiós a las largas y complejas direcciones y hola a una identidad fácil de usar", "settingsClaimUsernameValueProp2": "Más rápido y más fácil", "settingsClaimUsernameValueProp2Description": "Envíe y reciba criptomonedas fácilmente, inicie sesión en su billetera y conéctese con sus amigos", "settingsClaimUsernameValueProp3": "Manténgase sincronizado", "settingsClaimUsernameValueProp3Description": "Conecte cualquier cuenta a su nombre de usuario y se sincronizará en todos sus dispositivos", "settingsClaimUsernameHelperText": "Su nombre único para su cuenta Phantom", "settingsClaimUsernameValidationDefault": "Este nombre de usuario no puede cambiarse posteriormente", "settingsClaimUsernameValidationAvailable": "Nombre de usuario disponible", "settingsClaimUsernameValidationUnavailable": "Nombre de usuario no disponible", "settingsClaimUsernameValidationServerError": "No se ha podido comprobar si el nombre de usuario está disponible. Inténtelo de nuevo más tarde", "settingsClaimUsernameValidationErrorLine1": "Nombre de usuario no válido.", "settingsClaimUsernameValidationErrorLine2": "Los nombres de usuario deben tener entre {{minChar}} y {{maxChar}} caracteres y solo pueden contener letras y números.", "settingsClaimUsernameValidationLoading": "Comprobando si este nombre de usuario está disponible...", "settingsClaimUsernameSaveAndContinue": "Guardar y continuar", "settingsClaimUsernameChooseAvatarTitle": "Elegir avatar", "settingsClaimUsernameAnonymousAuthTitle": "Autenticación anónima", "settingsClaimUsernameAnonymousAuthDescription": "Inicie sesión en su cuenta Phantom de forma anónima con una firma", "settingsClaimUsernameAnonymousAuthBadge": "Aprenda cómo funciona", "settingsClaimUsernameLinkWalletsTitle": "Vincule sus billeteras", "settingsClaimUsernameLinkWalletsDescription": "Elija las billeteras que aparecen en otros dispositivos con su nombre de usuario", "settingsClaimUsernameLinkWalletsBadge": "No visible públicamente", "settingsClaimUsernameConnectAccountsTitle": "Conectar cuentas", "settingsClaimUsernameConnectAccountsHelperText": "Cada dirección de la cadena estará conectada con su nombre de usuario. Podrá cambiarlas más adelante.", "settingsClaimUsernameContinue": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameCreateUsername": "Crear nombre de usuario", "settingsClaimUsernameCreating": "Creando nombre de usuario...", "settingsClaimUsernameSuccess": "¡Nombre de usuario creado!", "settingsClaimUsernameError": "Hemos encontrado un error al crear su nombre de usuario", "settingsClaimUsernameTryAgain": "Reintentar", "settingsClaimUsernameSuccessHelperText": "Ya puede utilizar su nuevo nombre de usuario en todas sus billeteras Phantom", "settingsClaimUsernameSettingsSyncedTitle": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameSettingsSyncedHelperText": "Diga adiós a las largas y complejas direcciones y hola a una identidad fácil de usar", "settingsClaimUsernameSendToUsernameTitle": "Enviar a nombre de usuario", "settingsClaimUsernameSendToUsernameHelperText": "Envíe y reciba criptomonedas fácilmente, inicie sesión en su billetera y conéctese con sus amigos", "settingsClaimUsernameManageAddressesTitle": "Direcciones públicas", "settingsClaimUsernameManageAddressesHelperText": "Todos los tokens o coleccionables enviados a su nombre de usuario se enviarán a estas direcciones", "settingsClaimUsernameManageAddressesBadge": "Visible públicamente", "settingsClaimUsernameEditAddressesTitle": "Gestionar direcciones públicas", "settingsClaimUsernameEditAddressesHelperText": "Todos los tokens o coleccionables enviados a su nombre de usuario se enviarán a estas direcciones. Seleccione una dirección por cadena.", "settingsClaimUsernameEditAddressesError": "Solo se permite una dirección por red.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON>ar <PERSON>", "settingsClaimUsernameNoAddressesSaved": "No se han guardado direcciones públicas", "settingsClaimUsernameSave": "Guardar", "settingsClaimUsernameDone": "<PERSON><PERSON>", "settingsClaimUsernameWatching": "Viendo", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} cuenta(s)", "settingsClaimUsernameNoOfAccountsSingular": "1 cuenta", "settingsClaimUsernameEmptyAccounts": "No hay cuenta(s)", "settingsClaimUsernameSettingTitle": "Cree su @nombre_de_usuario", "settingsClaimUsernameSettingDescription": "Una identidad única para su billetera", "settingsManageUserProfileAbout": "Acerca de", "settingsManageUserProfileAboutUsername": "Nombre de usuario", "settingsManageUserProfileAboutBio": "Biografía", "settingsManageUserProfileTitle": "Gestionar perfil", "settingsManageUserProfileManage": "Gestionar", "settingsManageUserProfileAuthFactors": "Factores de autenticación", "settingsManageUserProfileAuthFactorsDescription": "Elija qué frases de semilla o claves privadas pueden acceder a su cuenta Phantom.", "settingsManageUserProfileUpdateAuthFactorsToast": "¡Factores de autentificación actualizados!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "No se han podido actualizar los factores de autenticación.", "settingsManageUserProfileBiography": "Editar biografía", "settingsManageUserProfileBiographyDescription": "Añada una breve biografía a su perfil", "settingsManageUserProfileUpdateBiographyToast": "¡Biografía actualizada!", "settingsManageUserProfileUpdateBiographyToastFailure": "Ha ocurrido algo. Inténtelo de nuevo", "settingsManageUserProfileBiographyNoUrlMessage": "<PERSON><PERSON> cual<PERSON>er URL de su biografía", "settingsManageUserProfileLinkedWallets": "Billeteras vinculadas", "settingsManageUserProfileLinkedWalletsDescription": "Elija las billeteras que aparecen en otros dispositivos al iniciar sesión en su cuenta Phantom.", "settingsManageUserProfileUpdateLinkedWalletsToast": "¡Billeteras vinculadas actualizadas!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "¡Error al actualizar las billeteras vinculadas!", "settingsManageUserProfilePrivacy": "Privacidad", "settingsManageUserProfileUpdatePrivacyStateToast": "¡Privacidad actualizada!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "¡No se ha podido actualizar la privacidad!", "settingsManageUserProfilePublicAddresses": "Direcciones públicas", "settingsManageUserProfileUpdatePublicAddressToast": "¡Dirección pública actualizada!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "¡Error al actualizar la dirección pública!", "settingsManageUserProfilePrivacyStatePublic": "Público", "settingsManageUserProfilePrivacyStatePublicDescription": "Su perfil y direcciones públicas son visibles y buscables por cualquiera", "settingsManageUserProfilePrivacyStatePrivate": "Privada", "settingsManageUserProfilePrivacyStatePrivateDescription": "Cualquiera puede buscar en su perfil, pero los demás deben solicitar permiso para ver su perfil y sus direcciones públicas", "settingsManageUserProfilePrivacyStateInvisible": "No visible", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Su perfil y direcciones públicas están ocultos y no se pueden descubrir en ningún sitio", "settingsDownloadPhantom": "Descargar Phantom", "settingsLogOut": "<PERSON><PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "<PERSON><PERSON><PERSON> una billetera", "seedlessAddAWalletSecondaryText": "Iniciar sesión o importar una billetera existente ", "seedlessAddSeedlessWalletPrimaryText": "<PERSON><PERSON><PERSON> billetera sin semillas", "seedlessAddSeedlessWalletSecondaryText": "Utilice su ID de Apple, Google o correo electrónico", "seedlessCreateNewWalletPrimaryText": "¿<PERSON>rear una nueva billetera?", "seedlessCreateNewWalletSecondaryText": "Este correo electrónico no tiene billetera, ¿le gustaría crear una?", "seedlessCreateNewWalletButtonText": "Crear billetera", "seedlessCreateNewWalletNoBundlePrimaryText": "No se encuentra la billetera", "seedlessCreateNewWalletNoBundleSecondaryText": "Este correo electrónico no tiene billetera", "seedlessCreateNewWalletNoBundleButtonText": "Volver", "seedlessEmailOptionsPrimaryText": "Seleccione su correo electrónico", "seedlessEmailOptionsSecondaryText": "Añada una billetera con su cuenta de Apple o Google ", "seedlessEmailOptionsButtonText": "Continuar con el correo electrónico", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Crear billetera con su ID de Apple", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Crear billetera con su correo electrónico de Google", "seedlessAlreadyExistsPrimaryText": "La cuenta ya existe", "seedlessAlreadyExistsSecondaryText": "Este correo electrónico ya tiene una billetera creada, ¿le gustaría iniciar sesión en su lugar?", "seedlessSignUpWithAppleButtonText": "Registrarse con Apple", "seedlessContinueWithAppleButtonText": "Continuar con <PERSON>", "seedlessSignUpWithGoogleButtonText": "Registrarse con Google", "seedlessContinueWithGoogleButtonText": "Continuar con <PERSON>", "seedlessCreateAPinPrimaryText": "<PERSON><PERSON>r un PIN", "seedlessCreateAPinSecondaryText": "Se utiliza para asegurar su billetera en todos sus dispositivos. <1>No se puede recuperar.</1>", "seedlessContinueText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessConfirmPinPrimaryText": "Confirme su PIN", "seedlessConfirmPinSecondaryText": "Si olvida este PIN, no podrá recuperar su billetera en un nuevo dispositivo.", "seedlessConfirmPinButtonText": "Crear PIN", "seedlessConfirmPinError": "PIN incorrecto. Inténtelo de nuevo", "seedlessAccountsImportedPrimaryText": "Cuentas importadas", "seedlessAccountsImportedSecondaryText": "Estas cuentas se importarán automáticamente en su billetera", "seedlessPreviouslyImportedTag": "Importado anteriormente", "seedlessEnterPinPrimaryText": "Introduzca su PIN", "seedlessEnterPinInvalidPinError": "PIN incorrecto introducido. Solo se permiten números de 4 dígitos", "seedlessEnterPinNumTriesLeft": "{{numTries}} <PERSON><PERSON> restantes.", "seedlessEnterPinCooldown": "Vuelva a intentarlo en {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "El PIN debe tener exactamente 4 dígitos", "seedlessEnterPinMatch": "Los PIN coinciden", "seedlessDoneText": "<PERSON><PERSON>", "seedlessEnterPinToSign": "Introduzca su PIN para firmar esta transacción", "seedlessSigning": "Firma", "seedlessCreateSeed": "<PERSON>rear una billetera de frases de semilla", "seedlessImportOptions": "Otras opciones de importación", "seedlessImportPrimaryText": "Opciones de importación", "seedlessImportSecondaryText": "Importe una billetera existente con su frase de semilla secreta, clave privada o billetera de hardware", "seedlessImportSeedPhrase": "Importar frase de semilla secreta", "seedlessImportPrivateKey": "Importar clave privada", "seedlessConnectHardwareWallet": "Conectar billetera de hardware", "seedlessTryAgain": "Reintentar", "seedlessCreatingWalletPrimaryText": "<PERSON><PERSON><PERSON> billetera", "seedlessCreatingWalletSecondaryText": "<PERSON><PERSON>dir una billetera social", "seedlessLoadingWalletPrimaryText": "Cargando billetera", "seedlessLoadingWalletSecondaryText": "Importando y viendo sus billeteras vinculadas", "seedlessLoadingWalletErrorPrimaryText": "No se ha podido cargar la billetera", "seedlessCreatingWalletErrorPrimaryText": "No se ha podido crear la billetera", "seedlessErrorSecondaryText": "Inténtelo de nuevo", "seedlessAuthAlreadyExistsErrorText": "El correo electrónico facilitado ya pertenece a otra cuenta de Phantom", "seedlessAuthUnknownErrorText": "Se ha producido un error desconocido, vuelva a intentarlo más tarde", "seedlessAuthUnknownErrorTextRefresh": "Se ha producido un error desconocido, inténtelo de nuevo más tarde. Actualice la página para volver a intentarlo.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON> ventana", "seedlessWalletExistsErrorPrimaryText": "Ya existe una billetera social en su dispositivo", "seedlessWalletExistsErrorSecondaryText": "Vuelva atrás o cierre esta pantalla", "seedlessValueProp1PrimaryText": "Instalación sin problemas", "seedlessValueProp1SecondaryText": "Cree una billetera utilizando una cuenta de Google o Apple y comience a explorar web3 con facilidad", "seedlessValueProp2PrimaryText": "<PERSON><PERSON>ridad mejorada", "seedlessValueProp2SecondaryText": "Su billetera se almacena de forma segura y descentralizada a través de múltiples factores", "seedlessValueProp3PrimaryText": "Fácil recuperación", "seedlessValueProp3SecondaryText": "Recupere el acceso a su billetera con su cuenta de Google o Apple y un PIN de 4 dígitos", "seedlessLoggingIn": "Iniciando se<PERSON>...", "seedlessSignUpOrLogin": "Regístrese o inicie sesión", "seedlessContinueByEnteringYourEmail": "Continúe introduciendo su correo electrónico", "seedless": "<PERSON>llas", "seed": "<PERSON><PERSON> de <PERSON>lla", "seedlessVerifyPinPrimaryText": "Verificar PIN", "seedlessVerifyPinSecondaryText": "Introduzca su PIN para continuar", "seedlessVerifyPinVerifyButtonText": "Verificar", "seedlessVerifyPinForgotButtonText": "¿Lo olvidó?", "seedlessPinConfirmButtonText": "Confirmar", "seedlessVerifyToastPrimaryText": "Verifique su PIN", "seedlessVerifyToastSecondaryText": "Ocasionalmente le pediremos que verifique su PIN para que lo recuerde. Si lo olvida, no podrá recuperar su billetera.", "seedlessVerifyToastSuccessText": "¡Se ha verificado su número PIN!", "seedlessForgotPinPrimaryText": "Restablecer el PIN utilizando otro dispositivo", "seedlessForgotPinSecondaryText": "<PERSON>r seguridad, solo podr<PERSON> restablecer su PIN en otros dispositivos en los que esté conectado", "seedlessForgotPinInstruction1PrimaryText": "Abrir otro dispositivo", "seedlessForgotPinInstruction1SecondaryText": "Vaya a otro dispositivo en el que su cuenta Phantom haya iniciado sesión con su correo electrónico", "seedlessForgotPinInstruction2PrimaryText": "<PERSON><PERSON>", "seedlessForgotPinInstruction2SecondaryText": "<PERSON> Ajustes, seleccione «Seguridad y privacidad» y, a continuación, «Restablecer PIN».", "seedlessForgotPinInstruction3PrimaryText": "Establezca su nuevo PIN", "seedlessForgotPinInstruction3SecondaryText": "Una vez que haya establecido su nuevo PIN, ya puede iniciar sesión en su billetera en este dispositivo", "seedlessForgotPinButtonText": "He seguido estos pasos", "seedlessResetPinPrimaryText": "Restablecer PIN", "seedlessResetPinSecondaryText": "Introduzca un nuevo PIN que recordará. Se utiliza para asegurar su billetera en todos sus dispositivos", "seedlessResetPinSuccessText": "¡Su número PIN está actualizado!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Al crear una billetera, acepta nuestros <1>Términos del servicio</1> y nuestra <5>Política de privacidad</5>", "pageNotFound": "Página no encontrada", "pageNotFoundDescription": "¡No le hemos dejado tirado! Esta página no existe o se ha movido.", "webTokenPagesLegalDisclaimer": "La información sobre precios se proporciona únicamente con fines informativos y no constituye asesoramiento financiero. Los datos de mercado son proporcionados por terceros y Phantom no garantiza la exactitud de la información.", "signUpOrLogin": "Registrarse o iniciar sesión", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Al crear una cuenta, acepta nuestros <1>Términos del servicio</1> y nuestra <5>Política de privacidad</5>", "feedNoActivity": "Aún no hay actividad", "followRequests": "Solicitudes de seguimiento", "following": "<PERSON><PERSON><PERSON><PERSON>", "followers": "<PERSON><PERSON><PERSON><PERSON>", "follower": "<PERSON><PERSON><PERSON><PERSON>", "joined": "Se unió", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON><PERSON><PERSON>", "noFollowing": "Sigu<PERSON>do a nadie", "noUsersFound": "No se han encontrado usuarios", "viewProfile": "Ver perfil", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}