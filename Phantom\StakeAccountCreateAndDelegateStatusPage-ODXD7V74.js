import{a as S}from"./chunk-43DCCALR.js";import{a as N,b as z,c as H}from"./chunk-O5AAGNHJ.js";import"./chunk-Q3HNV7GN.js";import"./chunk-XJWRT6N6.js";import{f as v,l as E}from"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-75L54KUM.js";import"./chunk-ROF5SDVA.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-W27Z2YZM.js";import"./chunk-H3FFS4GT.js";import{a as P}from"./chunk-4VDZJDFB.js";import"./chunk-GMBAJ6CC.js";import{b as g}from"./chunk-PTZMRZUV.js";import"./chunk-OKP6DFCI.js";import{rb as A}from"./chunk-WIQ4WVKX.js";import{r as x}from"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import{M as b,N as L}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import{$d as I,P as k,Pa as C}from"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as T}from"./chunk-56SJOU6P.js";import"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as G}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as q,h as y,n as p}from"./chunk-3KENBVE7.js";y();p();var e=q(G());var J=n=>{let s={lamports:n.lamports,votePubkey:n.votePubkey,usdPerSol:n.usdPerSol,onClose:n.onClose,validatorName:n.validatorName},{t}=T(),{createStakeAccountAndDelegateStake:c,needsLedgerApproval:d,txHash:a,confirmationStatus:l,isError:u,error:r,onDeny:i}=U(s);if(d)return e.default.createElement(z,{ledgerAction:c,cancel:i});if(u){let o=r?.message;return N(r)?e.default.createElement(H,{ledgerActionError:r,onRetryClick:c,onCancelClick:i}):e.default.createElement(g,{icon:"error",title:t("stakeAccountCreateAndDelegateStakingFailed"),onClose:i,showButton:!!a,iconSize:"large"},e.default.createElement(A,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},o??t("stakeAccountCreateAndDelegateErrorStaking")),e.default.createElement(S,{txHash:a},t("stakeAccountCreateAndDelegateViewTransaction")))}return l==="finalized"?e.default.createElement(g,{icon:"success",title:t("stakeAccountCreateAndDelegateSolStaked"),onClose:n.onClose,iconSize:"large"},e.default.createElement(A,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},e.default.createElement(P,{i18nKey:"stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated"},"Your SOL will begin earning rewards",e.default.createElement("br",null)," in the next couple days once the stake account becomes active.")),e.default.createElement(S,{txHash:a},t("stakeAccountCreateAndDelegateViewTransaction"))):e.default.createElement(g,{icon:"loading",title:t("stakeAccountCreateAndDelegateStakingSol"),onClose:n.onClose,iconSize:"large"},e.default.createElement(A,{wordBreak:"break-word",color:"#777777",size:16,lineHeight:20.8},t("stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated",{validatorName:n.validatorName})),e.default.createElement(S,{txHash:a},t("stakeAccountCreateAndDelegateViewTransaction")))},Ae=J,U=({onClose:n,...s})=>{let{data:t,isSuccess:c}=I(),{accountIdentifier:d,isLedgerAccount:a,solanaChainAddress:l,solanaPublicKey:u,connection:r,networkID:i}=(0,e.useMemo)(()=>{let F=t?.identifier??"",f=(t?.addresses??[]).find(j=>C.isSolanaNetworkID(j.networkID)),O=new k.PublicKey(f?.address??""),Q=t?.type==="ledger",h=f?.networkID,Y=b(L(h));return{accountIdentifier:F,isLedgerAccount:Q,solanaChainAddress:f,solanaPublicKey:O,connection:Y,networkID:h}},[t]),o=E(r),m=(0,e.useCallback)(()=>{o.mutate({...s,accountIdentifier:d,senderAddress:l,authorizedPubkey:u,votePubkey:new k.PublicKey(s.votePubkey)})},[d,s,o,l,u]),D=x(),B=(0,e.useCallback)(()=>{D.denied({chainType:"solana",chainName:"solana",networkId:C.getSolanaNetworkIDValue(i),type:"delegate"}),n()},[i,n,D]);(0,e.useEffect)(()=>{if(c){if(!t||!l)throw new Error("Selected account undefined when trying to perform stake transaction.");a||m()}},[c]);let w=o.data?.id,K=v(r,2e3,w),M=a&&(o.isIdle||o.isPending),V=K.data?.value?.confirmationStatus;return{createStakeAccountAndDelegateStake:m,onDeny:B,needsLedgerApproval:M,txHash:w,confirmationStatus:V,isError:o.isError,error:o.error}};export{J as StakeAccountCreateAndDelegateStatusPage,Ae as default};
//# sourceMappingURL=StakeAccountCreateAndDelegateStatusPage-ODXD7V74.js.map
