import{Ma as b,fa as T,z as y}from"./chunk-JD6NH5K6.js";import"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import"./chunk-X3ESGVCB.js";import{A as M}from"./chunk-QINBGLLG.js";import"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import"./chunk-CCQRCL2K.js";import"./chunk-75L54KUM.js";import{a as v}from"./chunk-ROF5SDVA.js";import"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{a as t,c as k,e as d}from"./chunk-2NGYUYTC.js";import"./chunk-H3FFS4GT.js";import{a as P}from"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import{a as p}from"./chunk-VQVTLSDS.js";import"./chunk-OKP6DFCI.js";import{o as c,pb as x,qb as S}from"./chunk-WIQ4WVKX.js";import"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import"./chunk-7ZN4F6J4.js";import"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import"./chunk-X2SBUKU4.js";import"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import"./chunk-SLQBAOEK.js";import"./chunk-MZZEJ42N.js";import"./chunk-E3NPIRHS.js";import{m as f}from"./chunk-56SJOU6P.js";import{S as g}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as L}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{f as N,h,n as u}from"./chunk-3KENBVE7.js";h();u();var e=N(L());var w={liquid:e.default.createElement(k.Droplet,{size:16,color:"accentSuccess"}),native:e.default.createElement(k.Layers,{size:16,color:"accentSuccess"})},C=c(S).attrs({color:p.grayLight,size:16})`
  align-self: center;
  line-height: normal;
  max-width: 100%;
  margin-bottom: 16px;
`,B=c(S)`
  color: ${o=>o.theme.green};
  font-size: 11px;
  font-style: normal;
  font-weight: 600;
  line-height: 13px;
`,D=c.a.attrs({target:"_blank",rel:"noopener noreferrer"})`
  color: ${o=>o.theme.purple};
  text-decoration: none;
  cursor: pointer;
`,O=c.div`
  border-radius: 3px;
  padding: 0 4px;
  background-color: ${g(p.green,.1)};
  margin-left: 4px;
`,r={screen:t({overflowY:"scroll"}),page:t({paddingY:"screen",flexDirection:"column",display:"flex",alignItems:"center"}),pageTitle:t({marginTop:16}),options:t({display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",gap:8}),card:t({display:"flex",flexDirection:"row",alignItems:"flex-start",width:"100%",padding:"screen",borderRadius:16,backgroundColor:{base:"bgRow",hover:"bgButton"},cursor:"pointer"}),optionContent:t({display:"flex",flexDirection:"column",marginLeft:12}),titleWrapper:t({display:"flex",flexDirection:"row",alignItems:"center",marginBottom:4}),title:t({color:"white"}),icon:t({color:"accentSuccess"}),apy:t({marginTop:12})},I=({onSelectNativeStaking:o})=>{let{t:n}=f(),{handleHideModalVisibility:a}=b(),l=(0,e.useCallback)(()=>{a("stakingMethods")},[a]),s=T(),{options:i,learnMoreLink:m}=M({onLiquidStake:s,onNativeStake:o});return{headerTitle:n("liquidStakeStartStaking"),options:i,learnMoreLink:m,onBack:l}},z=({isRecommended:o,title:n,description:a,icon:l,apy:s,onClick:i})=>{let{t:m}=f();return e.default.createElement("div",{className:r.card,onClick:i},e.default.createElement(v,{diameter:32,color:g(p.green,.1)},l),e.default.createElement("div",{className:r.optionContent},e.default.createElement("div",{className:r.titleWrapper},e.default.createElement(d,{font:"bodySemibold",color:"white",children:n}),o?e.default.createElement(O,null,e.default.createElement(B,null,m("stakeMethodRecommended"))):null),e.default.createElement(d,{font:"caption",color:"textSecondary",children:a}),s?e.default.createElement(d,{font:"caption",color:"textPrimary",children:s,className:r.apy}):null))},q=e.default.memo(o=>{let{headerTitle:n,options:a,learnMoreLink:l,onBack:s}=o;return e.default.createElement("div",{className:r.screen},e.default.createElement(y,{leftButton:{type:"close",onClick:s},titleSize:"regular"},n),e.default.createElement("div",{className:r.page},e.default.createElement(x,null),e.default.createElement(C,null,e.default.createElement(P,{i18nKey:"stakeMethodDescription"},"Earn interest by using your SOL tokens to help Solana scale. ",e.default.createElement(D,{href:l},"Learn more"))),e.default.createElement("div",{className:r.options},a.map(i=>e.default.createElement(z,{key:i.title,...i,icon:w[i.type]})))))}),H=o=>{let n=I(o);return e.default.createElement(q,{...n})},Z=H;export{H as StakingMethodSelectionPage,Z as default};
//# sourceMappingURL=StakingMethodSelectionPage-GIVQVJ56.js.map
