import{a as E}from"./chunk-24U56MUI.js";import{a,c as s}from"./chunk-RLZITNCL.js";import{Ma as A}from"./chunk-JD6NH5K6.js";import{a as b,b as f,d as h,e as C}from"./chunk-XJZOYN2T.js";import{c as n}from"./chunk-2NGYUYTC.js";import{jb as p}from"./chunk-SD2LXVLD.js";import{a as S}from"./chunk-MHOQBMVI.js";import{a as T}from"./chunk-BTKBODVJ.js";import{Md as d}from"./chunk-MZZEJ42N.js";import{m as i}from"./chunk-56SJOU6P.js";import{a as M}from"./chunk-7X4NV6OJ.js";import{f as l,h as m,n as u}from"./chunk-3KENBVE7.js";m();u();var o=l(M()),I=l(T());var g="INVALIDATE_ACCOUNTS_MESSAGE",w=new S,y=()=>((0,o.useEffect)(()=>w.subscribe(async e=>{e===g&&d(p)}),[]),o.default.createElement(o.default.Fragment,null,o.default.createElement(B,null),o.default.createElement(H,null))),B=()=>{let{t:e}=i(),{handleShowModalVisibility:t}=A();return o.default.createElement("button",{"data-testid":"search-open-button",className:s,onClick:async()=>{t("searchPage")},"aria-label":e("commandSearch")},o.default.createElement(n.Search,{size:a}))},H=()=>{let e=chrome.sidePanel||I.default.sidebarAction,{isSidebarOpen:t}=E(),{t:r}=i(),{Icon:N,label:O,onClick:_}=(0,o.useMemo)(()=>({Icon:t?n.Maximize2:n.SidebarRight,label:r(t?"commandClose":"commandOpen"),onClick:t?h:C}),[t,r]);return(0,o.useEffect)(()=>{let c=!0;return(async()=>c&&e&&!b&&await f())(),()=>{c=!1}},[e]),e?o.default.createElement("button",{"data-testid":"side-panel-menu-open-button",onClick:_,"aria-label":O,className:s},o.default.createElement(N,{size:a})):null},G=y;export{g as a,G as b};
//# sourceMappingURL=chunk-P2CLTFKC.js.map
