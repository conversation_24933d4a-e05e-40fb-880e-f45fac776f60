import{a as bo}from"./chunk-ESXKWKRD.js";import{b as X}from"./chunk-QZG7YQTK.js";import{a as Ee,b as je}from"./chunk-7A6HLO4U.js";import{Ma as vo,ra as ho,sa as _o,ua as he}from"./chunk-JD6NH5K6.js";import{a as uo,b as go}from"./chunk-KJMFZ7XX.js";import{a as Ae,b as fo,m as Ie}from"./chunk-IWGMKDQE.js";import{g as ye}from"./chunk-DERIAD33.js";import{a as po,h as se,n as Q}from"./chunk-75L54KUM.js";import{D as Xe,G as Re,U as ae,W as eo,a as oe,aa as oo,c as G,e as I,i as Qe,n as V}from"./chunk-2NGYUYTC.js";import{a as mo}from"./chunk-OKP6DFCI.js";import{db as to,eb as no,fb as ro,gb as io,hb as ao,ib as so,jb as co,kb as lo,o as u,rb as ve}from"./chunk-WIQ4WVKX.js";import{$ as Ke,D as Ze,R as Ve,S as $e,da as Ye,fa as Je,t as qe}from"./chunk-F3RUX6TF.js";import{a as ft}from"./chunk-LDMZMUWY.js";import{h as We,i as we}from"./chunk-SLQBAOEK.js";import{Ac as De,Kd as fe,Ld as Ge,Ob as Fe,Pa as He,Pb as Ce,Rc as ie,Xb as Be,fc as re,oc as Oe,ve as S,xc as ge}from"./chunk-MZZEJ42N.js";import{m as U}from"./chunk-56SJOU6P.js";import{V as ze,c as Ue}from"./chunk-ALUTR72U.js";import{a as P}from"./chunk-7X4NV6OJ.js";import{f as k,h as s,n as c}from"./chunk-3KENBVE7.js";s();c();var ce="_51gazne8 _51gazn129",xo="_51gazn1v _51gazn35 _51gaznqk _51gazn1a2 _51gazn1k2 _51gazn129",Cn="_51gazn18w _51gazn129 _51gazn1ar _51gazn1b4 _51gazn1c3",ke="_51gazn18w _51gazn129 _51gazn1ar _51gazn1b4",$="_51gazn1bz _51gazn129 _51gaznce",So="_51gaznqk _51gazn1af",zo="tj8tqp5",Co="tj8tqp4",Ao="tj8tqp1c _51gazn18w _51gazn1c3 _51gazn13j _51gazn129 _51gazn1ar _51gazn1b4",yo="_51gazn18w _51gazn1c3 _51gazn13j _51gazn129 _51gazn1ar _51gazn1b5 _51gaznee";var R="_51gaznql _51gazn18w _51gazn1c3 _51gazn1ar _51gazn1b6 _51gazn13j _51gazn46 _51gazn5p _51gazn1v _51gazn35",An="_51gaznc3",yn="_51gazn18w _51gazn1c3 _51gazn13j _51gazn129 _51gazn1ar _51gazn1b5 _51gaznee";var Io="_51gazn129 _51gazne8 _51gazn1h1",Eo="_51gaznbr _51gazne6 _51gazn18w _51gazn1c3 _51gazngn",ko="_51gazn18w _51gazn129 _51gazn1ar _51gazngj",In="_51gaznql",To="_51gazn129 _51gaznbt _51gazne8",Po="tj8tqp1f _51gazn18w _51gazn1c3 _51gazn13j _51gazn129 _51gazn1ar _51gazn1b4",En="_51gaznql _51gazn18w _51gazn1c3 _51gazn129 _51gazngj";var kn="_51gaznqk _51gaznvm _51gaznta _51gazn1gq";var Tn="_51gaznql",Pn="_51gaznqk _51gazn1af",wn="tj8tqp2 _51gaznqk _51gazn1af",jn="tj8tqp1 _51gaznqk _51gazn1af";var Mn="_51gazn11d _51gazn12n",Ln="_51gazn18w _51gazn1c4 _51gazn4b",Nn="_51gazn18w _51gazn1c3 _51gazn1r",Un="_51gazn4k _51gazn5p _51gazn18w _51gazn1c3 _51gazn1ar _51gazngj _51gaznca _51gazn46c",Hn="_51gazn18w _51gazn1c3 _51gazn1au _51gazn46c",Fn="_51gazn4f _51gazn35 _51gazn5p _51gazn1v",Bn="_51gazn1bq _51gazn19m _51gazn129 _51gazn4f _51gazn5r",Te="tj8tqpu _51gaznnh _51gaznof _51gaznqb _51gaznpd _51gaznkx _51gaznk3 _51gaznlr _51gaznj9 _51gaznqg _51gaznqe _51gaznqi _51gaznqc _51gazn2s8 _51gazn2j2 _51gazn20q _51gazn29w",On="_51gaznn";var Dn="_51gazn18w _51gazn1ar";var le="_51gazn4f _51gazn5p _51gazn18w _51gazn1c3 _51gazn1ar _51gazngj",me="tj8tqpl _51gazn129 _51gazn19m",Wn="_51gazn4f _51gazn5p _51gaznj",Gn="_51gazn18w _51gazn1ar _51gazn1b6 _51gazngh";s();c();var O=k(P());var ht=u.nav`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`,_t=u.ul`
  display: flex;
  position: relative;
  z-index: 2;
  margin-top: 16px;
`,bt=u.div`
  position: absolute;
  width: 300vw;
  left: -100vw;
  bottom: 0px;
  height: 1px;
  background: #2c2d30;
`,vt=u.li`
  position: relative;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-width: 0;
  position: relative;
  user-select: none;
  margin-right: 20px;
  padding-bottom: 15px;
`,xt=u.div`
  position: absolute;
  z-index: 1;
  bottom: 0;
  left: -2px;
  height: 1px;
  width: calc(100% + 4px);
  background: #fff;
`,St=u(ve).attrs({size:16,lineHeight:19,weight:500})`
  color: ${e=>e.isSelected?"#fff":"#777"};
`,zt=u.div`
  height: 100%;
  display: ${e=>e.isVisible?"block":"none"};
`,Jn=O.default.memo(({children:e,selectedIndex:t,setSelectedIndex:n,tabs:i})=>O.default.createElement(ht,null,O.default.createElement(_t,{role:"tablist"},i.map((o,r)=>{let a=r===t;return O.default.createElement(vt,{key:o,role:"tab","aria-controls":o,"aria-selected":a,tabIndex:0,onClick:()=>n(r)},O.default.createElement(St,{isSelected:a},o),a?O.default.createElement(xt,{as:mo.div,layoutId:"underline"}):null)}),O.default.createElement(bt,null)),e)),Qn=O.default.memo(({children:e,selectedIndex:t,tabs:n})=>O.default.createElement(O.default.Fragment,null,O.default.Children.map(e,(i,o)=>{let r=t===o;return O.default.createElement(zt,{id:n[o],key:`page-${o}`,role:"tabpanel","aria-expanded":r,isVisible:r,tabIndex:r?0:-1,style:{width:"100%"}},i)})));s();c();var wo=k(ft()),de=k(P()),Ct=200,or=()=>{let e=de.default.useRef(null),[t,n]=(0,de.useState)(0),i=()=>{e.current&&n(e.current?.scrollTop)},o=(0,de.useMemo)(()=>(0,wo.default)(i,Ct),[]);return de.default.useEffect(()=>{let r=e.current;return r?.addEventListener("scroll",o,{capture:!1,passive:!0}),()=>{r?.removeEventListener("scroll",o),o.cancel()}},[o]),{scrollContainerRef:e,scrollPosition:t}};s();c();var H=k(P());s();c();var ue=k(P());s();c();var jo=46,At=2,Mo=`
  border: ${At}px solid white;
  padding: 0px;
`,Lo=u.div`
  position: relative;
  display: flex;
  width: ${jo}px;
  height: ${jo}px;
  cursor: pointer;
  border-radius: 10px;
  padding: 2px;

  &:hover {
    ${Mo}
  }

  ${e=>e.isSelected?Mo:""}
`;var yt=u.div`
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
`,No=ue.default.memo(()=>ue.default.createElement(yt,null,[...Array(9).keys()].map(e=>ue.default.createElement(ue.default.Fragment,{key:`collectible-avatar-loader-${e}`},ue.default.createElement(Lo,{isSelected:!1},ue.default.createElement(ho,{key:e,showBadge:!1}))))));s();c();var L=k(P());var xe=46,Fo=2,It=4,Uo=xe+Fo*2+It,Et=u.div`
  display: flex;
  gap: 10px;
`,Ho=`
  border: ${Fo}px solid white;
  padding: 0px;
`,kt=u.div`
  flex: 1;
  margin-top: 16px;
  margin-bottom: -20px;
`,Tt=u.div`
  position: relative;
  display: flex;
  width: ${xe}px;
  height: ${xe}px;
  cursor: pointer;
  border-radius: 10px;
  padding: 2px;
  overflow: hidden;
  &:hover {
    ${Ho}
  }
  ${e=>e.isSelected?Ho:""}
`,Pt=L.default.memo(e=>{let{collectible:t,previewIcon:n,onCollectibleSelect:i}=e,o=t?.media,r=o?.type??"image",a=Ke(o,r,!1,"small"),m=(0,L.useCallback)(()=>{i(a)},[a,i]);return r!=="image"?null:L.default.createElement(Tt,{"data-testid":`collectible-avatar-${t.name}`,isSelected:n.imageUrl===a,onClick:m},L.default.createElement(_o,{uri:a??"",width:xe,height:xe}))}),wt=e=>{let{style:t,index:n,collectibleRows:i,previewIcon:o,onCollectibleSelect:r}=e,a=i[n];return L.default.createElement(Et,{style:t},a.map(m=>L.default.createElement(Pt,{key:m.id,collectible:m,previewIcon:o,onCollectibleSelect:r})))},Bo=L.default.memo(e=>{let{collectibles:t,previewIcon:n,onCollectibleSelect:i}=e,o=bo({itemSize:Uo,horizontalPadding:0,margin:0}),r=(0,L.useMemo)(()=>Ue(t,o),[t,o]);return L.default.createElement(kt,null,L.default.createElement(Xe,null,({height:a,width:m})=>L.default.createElement(Re,{height:a,width:m,rowCount:r.length,rowHeight:Uo,rowRenderer:l=>L.default.createElement(wt,{...l,collectibleRows:r,previewIcon:n,onCollectibleSelect:i})})))});var jt=u.div`
  display: flex;
  flex-direction: column;
  height: 100%;
`,Nr=H.default.memo(e=>{let{accountIdentifier:t,previewIcon:n,onCollectibleSelect:i}=e,{t:o}=U(),[r,a]=(0,H.useState)(""),m=ze(r)??"",{data:l}=Ge(t),d=l?.addresses??[],f=(0,H.useCallback)(Y=>{a(Y.currentTarget.value)},[]),E=(0,H.useCallback)(()=>{a("")},[]),{data:p=[],isError:b,isPending:w,refetch:j}=qe({addresses:d}),{getIsHidden:Z,getIsSpam:K}=Ze(t??""),{collections:ee}=Ve({allCollections:p,getIsHidden:Z,getIsSpam:K}),{collectibles:y}=$e(ee,[],[],m),M=Je(y),ne=Ye(M);return H.default.createElement(jt,null,H.default.createElement(ye,{value:r,placeholder:o("assetListSearch"),onChange:f,showClearIcon:r.length>0,onClear:E}),b?H.default.createElement(go,{title:o("errorAndOfflineUnableToFetchCollectibles"),description:"",buttonText:o("homeErrorButtonText"),refetch:j}):w?H.default.createElement(fo,null,H.default.createElement(No,null)):y.length===0?H.default.createElement(uo,null,o("collectiblesNoCollectibles")):!!l&&H.default.createElement(Bo,{collectibles:ne,previewIcon:n,onCollectibleSelect:i}))});s();c();var g=k(P());s();c();var te=k(P());var Me={emojiSuggested:null,emojiSmileys:te.default.createElement(to,null),emojiAnimals:te.default.createElement(no,null),emojiFood:te.default.createElement(ro,null),emojiTravel:te.default.createElement(io,null),emojiActivities:te.default.createElement(ao,null),emojiObjects:te.default.createElement(so,null),emojiSymbols:te.default.createElement(co,null),emojiFlags:te.default.createElement(lo,null)},Se=Object.keys(Me),Pe=Se[1];s();c();var q=k(P());s();c();var pe=k(P());var Oo=["1f468","1f469","1f9d1"],Mt="fe0f",Lt="200d",Do=`
  background: #fff;
  svg,
  path {
    fill: #000;
  }
`,Nt=u.div`
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  cursor: pointer;
  transition: fill 200ms ease;
  min-height: 32px;
  width: 32px;
  margin: 4px 0;
  padding-top: 2px;
  svg,
  path {
    fill: #777777;
  }
  :hover {
    ${Do}
  }
  ${e=>e.isSelected?Do:""}

  font-family: "Twemoji Mozilla", "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji",
    "EmojiOne Color", "Android Emoji", sans-serif;
  font-size: 24px;
`,Wo=pe.default.memo(({code:e,name:t,skinTone:n,supportsSkinTone:i,previewIcon:o,onEmojiSelect:r})=>{let a=(0,pe.useMemo)(()=>Ee(e),[e]),m=(0,pe.useMemo)(()=>a?i&&n?Ut(e,n):e:"",[e,a,n,i]),l=m===o.unicode,d=(0,pe.useCallback)(()=>{r(m)},[m,r]);return a?pe.default.createElement(Nt,{"data-testid":t,role:"img",title:t,"aria-label":t,onClick:d,isSelected:l},m):null});function Ut(e,t){let n=[...e].map(l=>{let d=l.codePointAt(0);return d?d.toString(16):""}),i=n.findIndex(l=>l===Lt),o=n.findIndex(l=>l===Mt),r=o>-1;if(i===-1){let l=r?o:n.length;n.splice(l,0,t)}else r&&o<i?n[o]=t:(n.splice(i,0,t),Oo.includes(n[0])&&Oo.includes(n[n.length-1])&&n.splice(n.length,0,t));return n.map(l=>String.fromCodePoint(parseInt(l,16))).join("")}var Go=u.div`
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 16px;
`,qo=q.default.memo(({emojis:e,groupName:t,offset:n,previewIcon:i,scrollPosition:o,skinTone:r,onEmojiSelect:a,setActiveGroupName:m,setOffset:l})=>{let d=q.default.useRef(null),f=t===Pe,E=Vo({emojis:e,skinTone:r,previewIcon:i,onEmojiSelect:a});return q.default.useEffect(()=>{f&&d.current&&l(d.current.offsetTop)},[f,l]),q.default.useEffect(()=>{d.current&&o>=d.current.offsetTop-n&&m(t)},[t,n,o,m]),q.default.createElement(Go,{id:t,ref:d},E)}),Zo=q.default.memo(({emojis:e,groupName:t,previewIcon:n,skinTone:i,onEmojiSelect:o,resetActiveGroupName:r})=>{q.default.useEffect(()=>{r()},[r]);let a=Vo({emojis:e,skinTone:i,previewIcon:n,onEmojiSelect:o});return q.default.createElement(Go,{id:t},a)}),Vo=({emojis:e,previewIcon:t,skinTone:n,onEmojiSelect:i})=>(0,q.useMemo)(()=>e.map(o=>q.default.createElement(Wo,{key:o.n,code:o.c,name:o.n,skinTone:n,supportsSkinTone:o.t,previewIcon:t,onEmojiSelect:i})),[e,i,t,n]);s();c();var $o="_51gaznql";var Ht=200,Xo=-16,Ro=0,et=0,Ko=Xo+et,Ft=u.div`
  position: relative;
  z-index: 1;
  width: 100%;
  top: ${Ro}px;
  left: 0;
  padding: ${et}px 0;
  background: #222;
`,Bt=u.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16px;
`,Ot=u.div`
  padding-top: ${Xo}px;
`,Dt=u.div`
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  cursor: pointer;
  transition: all 150ms ease;
  height: 32px;
  width: 32px;
  background: ${e=>e.isActive?"#333":"inherit"};

  svg,
  path {
    fill: #777777;
  }
  :hover {
    background: #333;
    path {
      fill: #fff;
    }
  }
`,Wt=u.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 65px;
`,Gt=u.span`
  filter: grayscale(100%); // gray out emoji
  font-size: 38px;
  margin-bottom: 15px;
`,qt=u(ve).attrs({size:16,lineHeight:19,weight:500,textAlign:"center",color:"#777"})``,Yo=u.div`
  padding-top: ${Ko}px;
  margin-top: -${Ko}px;
`,Jo=u.div`
  position: sticky;
  top: -16px;
  padding-top: 8px;
  padding-bottom: 8px;
  background: #222;
`,Qo=u(ve).attrs({size:13,lineHeight:16,weight:600,color:"#777",textAlign:"left"})``,Zt=g.default.memo(({groupName:e,isActive:t,containerRef:n,setActiveGroupName:i})=>{let o=g.default.useRef(null),r=(0,g.useCallback)(()=>{if(n&&n.current){let a=ot(e),m=document.getElementById(a);m&&n.current.scrollTo(0,m.offsetTop-Ro)}i(e)},[n,e,i]);return e==="emojiSuggested"?null:g.default.createElement(Dt,{ref:o,isActive:t,onClick:r},Me[e])}),Li=g.default.memo(({onEmojiSelect:e,containerRef:t,scrollPosition:n,skinTone:i,previewIcon:o})=>{let{t:r}=U(),[a,m]=g.default.useState(Pe),[l,d]=g.default.useState(0),[f,E]=(0,g.useState)(""),p=ze(f,Ht)??"",b=(0,g.useCallback)(()=>{m(Se[0])},[]),w=(0,g.useMemo)(()=>Se.map(y=>{let M=y===a;return g.default.createElement(Zt,{key:`icon-${y}`,containerRef:t,groupName:y,isActive:M,setActiveGroupName:m})}),[a,t]),j=(0,g.useMemo)(()=>Se.map(y=>{let M=ot(y);return g.default.createElement(Yo,{key:M,id:M},g.default.createElement(Jo,null,g.default.createElement(Qo,null,r(y))),g.default.createElement(qo,{emojis:je[y],groupName:y,previewIcon:o,scrollPosition:n,setActiveGroupName:m,skinTone:i,onEmojiSelect:e,offset:l,setOffset:d}))}),[r,o,n,i,e,l]),Z=(0,g.useMemo)(()=>{if(!p)return null;let y=[],M=new Set,ne=r("emojiSearchResults"),Y=Object.values(je);for(let h of Y)for(let _ of h)_.n.includes(p)&&!M.has(_.n)&&(M.add(_.n),y.push(_));return g.default.createElement(Yo,null,g.default.createElement(Jo,null,g.default.createElement(Qo,null,ne)),y.length>0?g.default.createElement(Zo,{emojis:y,groupName:ne,previewIcon:o,skinTone:i,onEmojiSelect:e,resetActiveGroupName:b}):g.default.createElement(Wt,null,Ee("\u{1FAE5}")&&g.default.createElement(Gt,null,"\u{1FAE5}"),g.default.createElement(qt,null,r("emojiNoResults"))))},[p,o,i,e,b,r]),K=(0,g.useCallback)(y=>E(y.currentTarget.value),[]),ee=(0,g.useCallback)(()=>E(""),[]);return g.default.createElement("div",{className:$o},g.default.createElement(Ft,null,g.default.createElement(ye,{placeholder:r("assetListSearch"),value:f,showClearIcon:f.length>0,onChange:K,onClear:ee}),g.default.createElement(Bt,null,w)),g.default.createElement(Ot,null,p?Z:j))}),ot=e=>e?`emoji-group-${e}`:"";s();c();var D=k(P());var Le=u.div`
  display: flex;
  justify-content: center;
  align-items: center;
  border-radius: 8px;
  cursor: pointer;
  transition: fill 200ms ease;
  height: 24px;
  width: 24px;
  margin: 4px 0;
  :hover {
    background: #333;
  }
`,Vt=u.div`
  position: relative;
  margin-right: 8px;
  ${Le} {
    border-radius: 4px;
    margin: 0;
  }
`,$t=u.div`
  display: flex;
  align-items: center;
  position: absolute;
  z-index: 2;
  background: #333;
  box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.25);
  border-radius: 4px;
  top: 0;
  right: 0;
`,Kt=u.div`
  background: #474747;
  width: 1px;
  height: 12px;
`,Di=D.default.memo(({currentSkinTone:e,isVisible:t,onClick:n,onClose:i,onSelect:o})=>{let r=(0,D.useRef)(null);po(r,()=>{i()});let a=(0,D.useMemo)(()=>{let m=Object.values(We),l=m.findIndex(d=>d===e);return m.splice(l,1),m.push(e),m.map(d=>{let f="\u270B"+we[d],E=d===e;return D.default.createElement(D.default.Fragment,{key:`skin-tone-button-${f}`},E&&D.default.createElement(Kt,null),D.default.createElement(Le,{key:d,onClick:()=>o(d)},f))})},[e,o]);return D.default.createElement(Vt,{ref:r},D.default.createElement(Le,{onClick:n},"\u270B"+we[e]),t&&D.default.createElement($t,null,a))});s();c();var x=k(P());s();c();var F=k(P()),tt=()=>F.default.createElement("svg",{width:126,height:125,fill:"none"},F.default.createElement("path",{fill:"#252525",d:"M66.982 62.375c0 9.19-5.13 15.812-12.459 15.812-7.328 0-12.645-6.622-12.645-15.812s5.13-15.626 12.645-15.626 12.459 6.436 12.459 15.626ZM99.138 62.375c0 9.19-5.13 15.812-12.458 15.812s-12.646-6.622-12.646-15.812 5.13-15.626 12.646-15.626c7.515 0 12.458 6.436 12.458 15.626Z"}),F.default.createElement("path",{fill:"#AB9FF2",d:"M101.186 84.737c3.561-3.555 4.425-8.945 2.659-13.33-1.486-3.69-4.127-6.468-7.387-8.661-1.265-.852-2.609-1.585-3.995-2.238.155-.323.301-.647.434-.973 1.882-4.592 2.008-8.854 1.11-12.838-.606-2.703-1.712-5.164-3.014-7.48-1.154-2.053-2.724-3.815-4.733-5.18-1.862-1.264-3.53-2.726-5.797-3.562-2.74-1.008-5.566-1.72-8.935-1.54-1.2.064-2.386.186-3.566.41-1.168-1.547-2.268-3.144-3.78-4.428-2.066-1.755-4.228-3.263-6.9-4.108-2.54-.804-5.017-1.198-7.748-.442-3.865 1.068-6.681 3.36-8.5 6.826-1.919 3.659-2.552 7.55-2.277 11.551.129 1.894.462 3.724.986 5.543-1.192-.517-2.404-.99-3.717-1.259-2.712-.556-5.39-.855-8.219-.351-2.69.48-5.126 1.28-7.25 3.19-3.005 2.702-4.476 5.97-4.484 9.788-.008 4.03 1.263 7.67 3.41 10.983 1.96 3.028 4.495 5.502 7.705 7.79 1.136 1.056 2.808 1.961 4.503 2.735 2.628 1.197 5.406 2.02 8.397 2.248.404.032.806.053 1.206.062-.502 1.92-.831 3.919-1.012 6.049-.33 1.562-.265 3.478-.075 5.336.296 2.88.987 5.663 2.29 8.273 1.461 2.924 3.393 5.323 6.226 7.062 4.093 2.513 9.585 1.949 13.663-.894 3.43-2.392 5.774-5.663 7.439-9.38 1.129-2.52 1.87-5.174 2.433-7.842.037-.18.07-.36.104-.542 1.873.862 3.887 1.544 6.088 2.089 1.465.565 3.386.804 5.282.914 2.938.17 5.884-.057 8.814-.903 3.285-.948 6.17-2.435 8.634-4.894l.006-.004Z"}),F.default.createElement("g",{clipPath:"url(#a)"},F.default.createElement("path",{fill:"#fff",d:"M64.914 79.347c5.742 0 10.397-7.543 10.397-16.847 0-9.305-4.655-16.847-10.397-16.847S54.517 53.195 54.517 62.5c0 9.304 4.655 16.847 10.397 16.847ZM89.356 79.347c5.742 0 10.398-7.543 10.398-16.847 0-9.305-4.656-16.847-10.398-16.847S78.96 53.195 78.96 62.5c0 9.304 4.655 16.847 10.397 16.847Z"}),F.default.createElement("path",{fill:"#A89EEB",d:"M69.103 70.521c3.272 0 5.924-2.9 5.924-6.478 0-3.577-2.652-6.478-5.924-6.478-3.272 0-5.924 2.9-5.924 6.478s2.652 6.478 5.924 6.478ZM93.548 70.521c3.271 0 5.923-2.9 5.923-6.478 0-3.577-2.652-6.478-5.923-6.478-3.272 0-5.924 2.9-5.924 6.478s2.652 6.478 5.924 6.478Z"}),F.default.createElement("path",{fill:"#3C315B",d:"M93.942 68.826a4.788 4.788 0 0 0 4.793-4.783 4.788 4.788 0 0 0-4.793-4.783 4.788 4.788 0 0 0-4.792 4.783 4.788 4.788 0 0 0 4.792 4.783ZM69.487 68.826a4.788 4.788 0 0 0 4.792-4.783 4.788 4.788 0 0 0-4.792-4.783 4.788 4.788 0 0 0-4.793 4.783 4.788 4.788 0 0 0 4.793 4.783Z"}),F.default.createElement("path",{fill:"#fff",d:"M65.993 62.811c.674.486 1.834.03 2.592-1.02.757-1.049.826-2.293.153-2.78-.673-.485-1.833-.029-2.591 1.02-.758 1.05-.827 2.294-.154 2.78ZM90.577 62.819c.673.486 1.833.03 2.591-1.02.758-1.05.827-2.294.154-2.78-.674-.486-1.834-.03-2.591 1.02-.758 1.05-.827 2.294-.154 2.78Z"})),F.default.createElement("path",{fill:"#E2DFFE",d:"M59.058 31.616c-2.438 2.869-2.607 4.332-.842 8.07.151.32-.25.614-.51.371-3.024-2.819-4.468-3.102-7.941-1.654-.314.13-.589-.246-.37-.504 2.435-2.868 2.605-4.33.84-8.069-.151-.32.25-.613.51-.371 3.024 2.819 4.468 3.103 7.941 1.654.313-.13.589.246.369.504h.001l.002-.001ZM17.687 40.397c-4.72.89-6.063 2.207-7.312 7.332-.107.439-.74.408-.803-.04-.739-5.225-1.944-6.666-6.552-8.016-.416-.122-.387-.716.038-.796 4.717-.892 6.059-2.206 7.308-7.333.107-.439.74-.408.804.04.738 5.225 1.943 6.665 6.552 8.016.415.122.387.716-.039.796h.002l.002.001ZM122.511 42.275c-7.587 2.052-9.608 4.369-10.994 12.886-.119.73-1.156.76-1.316.036-1.867-8.427-4.016-10.623-11.704-12.24-.693-.146-.722-1.12-.038-1.303 7.58-2.053 9.601-4.367 10.988-12.888.118-.729 1.156-.759 1.315-.036 1.868 8.427 4.016 10.623 11.704 12.241.694.146.722 1.119.039 1.303h.003l.003.001Z"}),F.default.createElement("ellipse",{cx:30.732,cy:108.626,fill:"#AB9FF2",rx:11.057,ry:8.08,transform:"rotate(45.017 30.732 108.626)"}),F.default.createElement("defs",null,F.default.createElement("clipPath",{id:"a"},F.default.createElement("path",{fill:"#fff",d:"M54.517 45.653h45.237v33.69H54.517z"}))));s();c();var _e=k(P());s();c();var Ne="_51gazn98",nt="_51gazn6t",rt="_1u8pm702 _51gaznms _51gaznnq _51gaznpm _51gaznoo _51gazn18w _51gazn1c4 _51gazn1ar _51gazn1q _51gazn30 _51gazn48 _51gazn5i _51gaznbp _51gazne4",it="_1u8pm701 _51gaznms _51gaznnq _51gaznpm _51gaznoo _51gazn18w _51gazn1c4 _51gazn1ar _51gazn1q _51gazn30 _51gazn48 _51gazn5i _51gaznbp _51gazne4";var be=({leftIcon:e,text:t="",color:n="purple",rightIcon:i,onClick:o})=>{let r=n==="purple"?"accentPrimary":"accentSuccess";return _e.default.createElement("div",{className:n==="purple"?it:rt,onClick:o},e==="eye-off"&&_e.default.createElement(G.EyeOff,{color:r,size:16,className:Ne}),e==="eye"&&_e.default.createElement(G.Eye,{color:r,size:16,className:Ne}),_e.default.createElement(I,{font:"labelSemibold",color:r},t),i==="info"&&_e.default.createElement(G.Info,{color:r,size:16,className:nt}))};s();c();var C=k(P());s();c();var W=k(P()),at=()=>W.default.createElement("svg",{width:246,height:125,fill:"none"},W.default.createElement("path",{stroke:"#8D8C8A",strokeDasharray:"4 5",strokeLinecap:"round",d:"M75.205 27.228c.823-5.833 6.22-9.894 12.052-9.07l75.918 10.718c5.833.824 9.894 6.22 9.07 12.053l-3.821 27.067-97.04-13.701 3.82-27.067Z"}),W.default.createElement("circle",{cx:89.027,cy:33.051,r:7.5,stroke:"#8D8C8A",transform:"rotate(8.036 89.027 33.051)"}),W.default.createElement("rect",{width:47.002,height:8,x:119.287,y:29.75,fill:"#222",stroke:"#8D8C8A",rx:4,transform:"rotate(8.036 119.287 29.75)"}),W.default.createElement("path",{fill:"#FFD13F",d:"M63.919 49.815c0-7.052 5.716-12.768 12.767-12.768h91.775c7.051 0 12.768 5.716 12.768 12.768v32.72H63.919v-32.72Z"}),W.default.createElement("circle",{cx:81.276,cy:54.404,r:9.576,fill:"#FFFDF8",opacity:.5}),W.default.createElement("rect",{width:57.458,height:10.773,x:115.99,y:44.828,fill:"#FFFDF8",opacity:.5,rx:5.387}),W.default.createElement("path",{fill:"#FFD13F",fillRule:"evenodd",d:"M86.641 50.328a.566.566 0 0 1 .02.8l-7.002 7.355a.566.566 0 0 1-.82 0l-3.182-3.343a.566.566 0 0 1 .82-.78l2.772 2.913 6.592-6.925a.566.566 0 0 1 .8-.02Z",clipRule:"evenodd"}),W.default.createElement("path",{fill:"#9886E5",d:"M56.04 71.218c0-7.999 6.484-14.483 14.482-14.483h104.103c7.998 0 14.483 6.484 14.483 14.483v37.115H56.04V71.218Z"}),W.default.createElement("circle",{cx:75.728,cy:76.423,r:10.863,fill:"#FFFDF8",opacity:.4}),W.default.createElement("rect",{width:65.176,height:12.22,x:115.105,y:65.561,fill:"#FFFDF8",opacity:.4,rx:6.11}),W.default.createElement("path",{stroke:"#8D8C8A",strokeLinecap:"round",strokeWidth:4,d:"M49.25 108.333h147.325"}));s();c();var z=k(P());s();c();var v=k(P());var st=({showHeader:e=!1})=>{let{t}=U(),n=S(_=>_.username),i=S(_=>_.icon?.imageUrl??_.icon?.unicode??""),o=S(_=>_.secretIdentifiers),r=S(_=>_.accountIdentifiersToSync),a=S(_=>_.addresses),{handleHideModalVisibility:m}=vo(),{mutateAsync:l,error:d,isIdle:f,isError:E}=De(),p=(0,v.useRef)(null),[b,w]=(0,v.useState)(!1),[j,Z]=(0,v.useState)(!1),K=(0,v.useCallback)(()=>{p.current?.setUserName(n)},[n]),ee=(0,v.useCallback)(()=>{p.current?.fail()},[]),y=()=>{Z(_=>_===!1?_:!1),p.current?.tryAgain(),l({username:n,addresses:a,icon:i,secretIdentifiers:o,accountIdentifiers:r}).then(K).catch(ee)},M=self.innerHeight>eo.height,ne=()=>{p.current?.start(M)};(0,v.useEffect)(()=>{f&&l({username:n,addresses:a,icon:i,secretIdentifiers:o,accountIdentifiers:r}).then(K).catch(ee)},[f,l,n,a,i,o,K,ee,r]);let Y;d instanceof Error&&d.message!=="Unknown error"?Y=d.message:Y=t("settingsClaimUsernameError");let h=Qe(So,M?Co:zo);return v.default.createElement("div",{className:R,role:"button",tabIndex:0,onClick:()=>p.current?.startConfetti()},v.default.createElement(oo,{className:h,ref:p,onFailComplete:()=>Z(_=>_===!0?_:!0),onSuccessComplete:()=>w(_=>_===!0?_:!0),onRiveReady:ne}),e?v.default.createElement("div",{className:xo},v.default.createElement(Q,null,v.default.createElement(X,{numOfItems:6,currentIndex:5,maxVisible:5}))):null,b?v.default.createElement(v.default.Fragment,null,v.default.createElement("div",{className:M?yo:Ao},v.default.createElement(I,{align:"center",font:"heading3Semibold",children:t("settingsClaimUsernameSuccess")}),v.default.createElement(I,{align:"center",font:"caption",color:"textSecondary",children:t("settingsClaimUsernameSuccessHelperText")})),v.default.createElement(V,{className:$,theme:"primary",onClick:()=>m("claimUsername")},t("settingsClaimUsernameDone"))):null,j?v.default.createElement(v.default.Fragment,null,v.default.createElement("div",{className:Po},E&&v.default.createElement(I,{children:Y,color:"accentAlert",font:"caption"})),v.default.createElement(V,{className:$,theme:"primary",onClick:y},t("settingsClaimUsernameTryAgain"))):null)};s();c();var B=k(P()),ct=()=>B.default.createElement("svg",{width:126,height:125,fill:"none"},B.default.createElement("g",{clipPath:"url(#a)"},B.default.createElement("path",{fill:"#9886E5",d:"M72.341 57.281c-21.512-.125-44.916 25.994-45.057 42.954-.055 6.66 3.518 8.214 7.246 8.236 7.888.046 13.872-6.771 17.455-12.164-.44 1.196-.689 2.392-.698 3.541-.026 3.163 1.772 5.425 5.356 5.446 4.925.029 10.217-4.253 12.98-8.884a6.828 6.828 0 0 0-.3 1.865c-.019 2.205 1.212 3.6 3.746 3.615 7.984.046 16.131-14.04 16.234-26.4.08-9.63-4.727-18.137-16.963-18.209h.001Z"}),B.default.createElement("path",{stroke:"#222",strokeLinecap:"round",strokeMiterlimit:10,strokeWidth:2.25,d:"M61.984 72.608c.541-1.796 2.204-3.012 4-2.798 2.023.243 3.452 2.214 3.184 4.403M73.017 72.683c.541-1.795 2.204-3.011 4-2.798 2.022.243 3.45 2.214 3.183 4.403"}),B.default.createElement("g",{clipPath:"url(#b)"},B.default.createElement("path",{fill:"#9886E5",d:"M39.657 51.165a12.5 12.5 0 0 1-11.518-7.669c-2.675-6.362.337-13.7 6.7-16.375 1.306-.55 32.306-13.318 65.719-4.443 6.675 1.775 10.643 8.618 8.875 15.287-1.769 6.669-8.613 10.644-15.288 8.875-24.981-6.631-49.406 3.244-49.656 3.344a12.48 12.48 0 0 1-4.831.981Z"}),B.default.createElement("path",{fill:"#222",d:"M35.133 40.765c-1.35-3.625.325-7.144 4.3-8.625 3.337-1.244 6.65-.119 7.837 3.063.869 2.33.331 4.525-2.075 5.418l-.656.244-.631-1.081-.15.056c-.125 1.056-.688 1.744-1.632 2.1-1.718.644-3.43-.406-4.08-2.156-.65-1.75-.045-3.663 1.8-4.344.912-.337 1.668-.137 2.393.475l.15-.056-.394-1.05 1.4-.519 1.919 5.15c.981-.537 1.431-1.8.756-3.612-.969-2.607-3.35-3.538-6.194-2.482-3.125 1.163-4.643 3.775-3.468 6.944 1.18 3.169 3.85 4.238 6.925 3.094 1.262-.469 2.056-1.144 2.58-1.638l.076-.025.481 1.294a9.408 9.408 0 0 1-2.7 1.544c-3.956 1.475-7.294-.163-8.644-3.788l.007-.006Zm8.012-2.875c-.419-1.125-1.431-1.756-2.619-1.312-1.25.462-1.468 1.55-1.05 2.68.42 1.132 1.294 1.807 2.544 1.345 1.188-.444 1.544-1.582 1.125-2.707v-.006ZM49.351 38.303l1.313-.656c.356.63 1.081 1.006 2.269.712 1.025-.25 1.518-.775 1.356-1.437-.125-.507-.538-.688-1.269-.607l-1.55.163c-1.5.15-2.412-.425-2.7-1.581-.344-1.407.581-2.62 2.375-3.063 1.975-.487 3.075.469 3.475 1.275l-1.3.638c-.3-.482-.881-.857-1.906-.607-.881.22-1.381.707-1.231 1.325.118.488.537.688 1.225.62l1.862-.188c1.319-.138 2.188.45 2.469 1.6.362 1.468-.45 2.656-2.538 3.168-1.893.463-3.368-.225-3.85-1.362ZM56.776 36.359c-.237-1.456.77-2.544 2.694-2.863l2.019-.33-.063-.388c-.175-1.075-.9-1.469-1.975-1.288-1.043.169-1.493.738-1.506 1.45l-1.481-.087c.069-1.082.912-2.369 2.806-2.681 2.05-.338 3.419.462 3.7 2.193l.831 5.1-1.568.256-.188-1.156-.162.025c-.35.981-1.163 1.538-2.094 1.694-1.619.263-2.788-.55-3.013-1.925Zm3.12.563c1.28-.213 2.012-1.17 1.818-2.357l-.031-.175-1.938.319c-1.094.181-1.606.556-1.487 1.294.112.687.718 1.069 1.63.919h.007ZM65.77 35.283l-.412-4.468-1.782.162-.118-1.294 1.943-.18-.194-2.088 1.394-.456.225 2.412 2.119-.194.119 1.294-2.119.194.388 4.206c.056.6.337.85 1.1.781l1.1-.1.124 1.363-1.7.156c-1.312.119-2.062-.413-2.187-1.788ZM70.126 32.946c-.025-2.357 1.581-4.063 3.875-4.088 2.294-.025 3.931 1.65 3.956 4.006.026 2.357-1.58 4.063-3.874 4.088-2.294.025-3.932-1.65-3.957-4.006Zm6.206-.07c-.018-1.624-.918-2.655-2.318-2.637-1.4.02-2.281 1.063-2.263 2.688.019 1.625.919 2.656 2.319 2.637 1.4-.018 2.281-1.062 2.263-2.687ZM78.839 34.721l1.456-.187c.125.712.694 1.306 1.906 1.425 1.05.1 1.688-.231 1.75-.913.05-.518-.28-.825-1-.993l-1.518-.357c-1.469-.35-2.138-1.2-2.025-2.38.137-1.438 1.406-2.282 3.256-2.107 2.019.194 2.75 1.456 2.862 2.344l-1.437.175c-.125-.55-.55-1.094-1.6-1.2-.906-.088-1.538.212-1.6.843-.05.5.281.825.95.988l1.819.437c1.293.3 1.912 1.144 1.8 2.325-.144 1.507-1.3 2.357-3.438 2.15-1.944-.187-3.106-1.325-3.181-2.556v.006ZM88.795 26.784l1.556.337-.956 4.363.156.037c.457-.562 1.413-1.056 2.6-.794 1.62.357 2.482 1.588 2.075 3.444l-1.068 4.888-1.557-.338.988-4.506c.25-1.144-.256-2.031-1.3-2.263-1.156-.256-2.081.394-2.363 1.682l-.937 4.3-1.556-.338 2.362-10.806v-.006ZM97.102 32.234l1.512.494-2.419 7.38-1.512-.493 2.418-7.381Zm.606-2.419a.959.959 0 0 1 1.231-.625.964.964 0 0 1 .625 1.231.963.963 0 0 1-1.231.625c-.544-.175-.788-.718-.625-1.23Z"})),B.default.createElement("path",{fill:"#9886E5",d:"M100.955 59.742c-2.235.744-2.8 1.473-3.068 4.046-.023.22-.333.247-.393.033-.704-2.49-1.385-3.11-3.714-3.461-.21-.032-.235-.323-.034-.39 2.233-.745 2.798-1.472 3.066-4.046.023-.22.333-.247.393-.033.704 2.49 1.385 3.11 3.714 3.461.21.032.235.322.034.39h.002ZM30.224 71.734c-.077-5.65-1.206-7.416-6.326-9.636-.437-.191-.29-.927.186-.933 5.58-.087 7.3-1.281 9.541-6.47.202-.467.817-.345.824.165.077 5.648 1.207 7.412 6.329 9.635.437.19.29.926-.187.932-5.58.087-7.3 1.282-9.54 6.47-.203.468-.817.345-.824-.165l-.003.002ZM28.272 23.904c-1.046-1.664-1.7-1.983-3.622-1.716-.165.022-.254-.223-.114-.31 1.641-1.036 1.936-1.702 1.662-3.648-.025-.175.18-.25.274-.1 1.045 1.663 1.7 1.982 3.623 1.715.164-.023.254.222.113.31-1.64 1.036-1.935 1.702-1.661 3.648.024.175-.18.25-.275.1v.001ZM13.353 48.353c-2.864-4.557-4.658-5.433-9.922-4.7-.45.06-.696-.61-.31-.852 4.494-2.837 5.301-4.66 4.551-9.99-.068-.481.493-.686.751-.275 2.863 4.555 4.656 5.428 9.923 4.697.45-.061.696.61.311.851-4.495 2.837-5.301 4.661-4.551 9.991.067.48-.493.686-.752.275v.003ZM121.891 52.666c-6.099.788-7.91 2.37-9.886 8.828-.169.552-.977.466-1.024-.112-.556-6.73-1.99-8.66-7.779-10.727-.522-.187-.441-.944.108-1.014 6.094-.79 7.905-2.37 9.882-8.83.169-.552.976-.466 1.023.112.557 6.73 1.99 8.66 7.779 10.727.522.187.442.944-.108 1.014l.003.001.002.001Z"})),B.default.createElement("defs",null,B.default.createElement("clipPath",{id:"a"},B.default.createElement("path",{fill:"#fff",d:"M.5 0h125v125H.5z"})),B.default.createElement("clipPath",{id:"b"},B.default.createElement("path",{fill:"#fff",d:"M27.164 19.552h82.688v31.613H27.164z"}))));s();c();var T=k(P());var lt=({showHeader:e=!1})=>{let{t}=U(),{popDetailView:n}=se(),i=S(p=>p.secretIdentifiers),o=S(p=>p.addresses),r=S(p=>p.addAddress),a=S(p=>p.removeAddress),{data:m=[]}=fe(),l=i.map(p=>ie(m,p)).flat(),d=(p,b)=>{o[p]===b?a(p,b):r(p,b)},f=(0,T.useCallback)(()=>{n()},[n]),E=()=>self.open(re);return T.default.createElement("div",{className:R},e?T.default.createElement(Q,{icon:T.default.createElement(G.HelpCircle,{className:oe({cursor:"pointer"}),size:16,color:"textSecondary"}),disableIconBackground:!0,onLeftButtonClick:n,onIconClick:E},T.default.createElement(X,{numOfItems:6,currentIndex:4,maxVisible:5})):null,T.default.createElement("div",{className:me},T.default.createElement("div",{className:le},T.default.createElement(I,{font:"heading3Semibold",children:t("settingsClaimUsernameEditAddressesTitle")}),T.default.createElement(I,{font:"caption",color:"textSecondary",children:t("settingsClaimUsernameEditAddressesHelperText"),align:"center"})),T.default.createElement("div",{className:ce},l.map((p,b)=>T.default.createElement("div",{key:p.identifier,className:Eo},T.default.createElement("div",{className:ko},T.default.createElement(he,{size:"xsmall",accountIcon:p.icon,accountName:p.name,accountIndex:b}),T.default.createElement(I,{children:p.name,font:"captionSemibold"})),T.default.createElement(ae,{rows:p.addresses.map(({networkID:w,address:j})=>({type:"check",active:o[w]===j,start:T.default.createElement(Ae,{networkID:w,size:24}),topLeft:{text:Ce(j,4),after:T.default.createElement(Ie,{networkID:w,address:j})},onClick:()=>d(w,j),disabled:Object.keys(o).includes(w)&&o[w]!==j}))}))))),T.default.createElement(V,{theme:"primary",className:$,onClick:f},t("settingsClaimUsernameSave")))};var mt=({showHeader:e=!1})=>{let{t}=U(),n=S(d=>d.addresses),{pushDetailView:i,popDetailView:o}=se(),r=(0,z.useMemo)(()=>Object.entries(n).sort(Be).map(([f,E])=>({start:z.default.createElement(Ae,{networkID:f,size:24}),topLeft:{text:He.getNetworkName(f),after:z.default.createElement(Ie,{networkID:f,address:E})},topRight:Ce(E,4)})),[n]),a=ge(),m=(0,z.useCallback)(()=>{a.onPublicAddressesSaved(),i(z.default.createElement(st,{showHeader:e}))},[a,i,e]),l=()=>self.open(re);return z.default.createElement("div",{className:R},e?z.default.createElement(Q,{icon:z.default.createElement(G.HelpCircle,{className:oe({cursor:"pointer"}),size:16,color:"textSecondary"}),disableIconBackground:!0,onLeftButtonClick:o,onIconClick:l},z.default.createElement(X,{numOfItems:6,currentIndex:4,maxVisible:5})):null,z.default.createElement("div",{className:me},z.default.createElement("div",{className:le},z.default.createElement(ct,null),z.default.createElement(I,{font:"heading3Semibold",children:t("settingsClaimUsernameManageAddressesTitle")}),z.default.createElement(I,{font:"caption",color:"textSecondary",children:t("settingsClaimUsernameManageAddressesHelperText"),align:"center"}),z.default.createElement(be,{leftIcon:"eye",color:"green",text:t("settingsClaimUsernameManageAddressesBadge"),onClick:l})),z.default.createElement("div",{className:ce},r.length>0?z.default.createElement(ae,{rows:r}):z.default.createElement(I,{font:"captionSemibold",children:t("settingsClaimUsernameNoAddressesSaved"),align:"center",className:To})),z.default.createElement(I,{font:"captionSemibold",children:t("settingsClaimUsernameEditAddressesEditAddress"),color:"textSecondary",align:"center",className:Io,onPress:()=>i(z.default.createElement(lt,{showHeader:!0}))})),z.default.createElement(V,{theme:"primary",className:$,onClick:m},t("settingsClaimUsernameSaveAndContinue")))};var Yt=({id:e,isActive:t,account:n,accountIndex:i,onSelect:o})=>({type:"check",active:t,topLeft:{text:n.name,font:"bodySemibold"},start:C.default.createElement("ul",{className:ke},C.default.createElement("li",{className:Te,key:i},C.default.createElement(he,{size:"xsmall",accountIcon:n.icon,accountName:n.name,accountIndex:i}))),onClick:()=>o(e)}),pt=({showHeader:e=!1})=>{let{t}=U(),{pushDetailView:n,popDetailView:i}=se(),o=S(b=>b.accountIdentifiersToSync),r=(0,C.useMemo)(()=>new Set(o),[o]),a=S(b=>b.addAccountIdentifierToSync),m=S(b=>b.removeAccountIdentifierToSync),{data:l=[]}=fe(),d=(0,C.useMemo)(()=>l.filter(b=>Fe(b)).map((b,w)=>{let j=r.has(b.identifier),Z=j?m:a;return Yt({id:b.identifier,isActive:j,account:b,accountIndex:w,onSelect:Z})}),[r,l,a,m]),f=ge(),E=(0,C.useCallback)(()=>{f.onLinkWalletsSaved(r.size,l.length),n(C.default.createElement(mt,{showHeader:e}))},[r.size,l.length,f,n,e]),p=()=>self.open(re);return C.default.createElement("div",{className:R},e?C.default.createElement(Q,{icon:C.default.createElement(G.HelpCircle,{className:oe({cursor:"pointer"}),size:16,color:"textSecondary"}),disableIconBackground:!0,onLeftButtonClick:i,onIconClick:p},C.default.createElement(X,{numOfItems:6,currentIndex:3,maxVisible:5})):null,C.default.createElement("div",{className:me},C.default.createElement("div",{className:le},C.default.createElement(at,null),C.default.createElement(I,{font:"heading3Semibold",children:t("settingsClaimUsernameLinkWalletsTitle")}),C.default.createElement(I,{font:"caption",children:t("settingsClaimUsernameLinkWalletsDescription"),color:"textSecondary",align:"center"}),C.default.createElement(be,{leftIcon:"eye-off",text:t("settingsClaimUsernameLinkWalletsBadge"),onClick:p})),C.default.createElement("div",{className:ce},C.default.createElement(ae,{rows:d}))),C.default.createElement(V,{theme:"primary",className:$,onClick:E},t("settingsClaimUsernameSaveAndContinue")))};var Jt=5,dt=({accountTypePrefix:e,numberOfAccountsText:t,secretId:n,isActive:i,accounts:o,onSelect:r,disabled:a})=>{let m=ie(o,n);return{type:"check",active:i,topLeft:e,bottomLeft:{text:t,before:x.default.createElement("ul",{className:ke},m.slice(0,Jt).map((l,d)=>x.default.createElement("li",{className:Te,key:d},x.default.createElement(he,{size:"xsmall",accountIcon:l.icon,accountName:l.name,accountIndex:d}))))},onClick:()=>r(n),disabled:a}},n1=({showHeader:e=!1})=>{let{t}=U(),{pushDetailView:n,popDetailView:i}=se(),o=S(h=>h.secretIdentifiers),r=S(h=>h.addSecretIdentifier),a=S(h=>h.removeSecretIdentifier),m=S(h=>h.setAccountIdentifiersToSync),l=S(h=>h.addAddress),d=S(h=>h.clearAddresses),{data:f=[]}=fe(),{data:E}=Oe(),[p,b]=(0,x.useMemo)(()=>{let h=Object.keys(E||{}),_=new Set,N=new Set;for(let J of f)J.type==="seed"||J.type==="seedless"?J.seedIdentifier&&h.includes(J.seedIdentifier)&&_.add(J.seedIdentifier):J.type==="privateKey"&&J.privateKeyIdentifier&&h.includes(J.privateKeyIdentifier)&&N.add(J.privateKeyIdentifier);return[[..._],[...N]]},[f,E]),[w,j]=(0,x.useState)(!1);(0,x.useEffect)(()=>{w||o.length===0&&(p.length>0?(r(p[0]),j(!0)):b.length>0&&(r(b[0]),j(!0)))},[r,w,b,o.length,p]);let Z=(0,x.useCallback)(h=>{o.includes(h)?a(h):r(h),d()},[o,d,a,r]),K=ge(),ee=(0,x.useCallback)(()=>{let h=o[0];h&&ie(f,h)[0].addresses.forEach(({networkID:ut,address:gt})=>l(ut,gt));let _=o.flatMap(N=>ie(f,N)).map(N=>N.identifier);m(_),K.onAnonymousAuthSaved(o.length,p.length+b.length),n(x.default.createElement(pt,{showHeader:e}))},[f,l,K,b.length,n,o,p.length,m,e]),y=p.map((h,_)=>{let N=ie(f,h).length;return dt({accountTypePrefix:t("addAccountSecretPhraseDefaultLabel",{number:_+1}),numberOfAccountsText:N===1?t("settingsClaimUsernameNoOfAccountsSingular"):N>0?t("settingsClaimUsernameNoOfAccounts",{noOfAccounts:N}):t("settingsClaimUsernameEmptyAccounts"),secretId:h,isActive:o.includes(h),accounts:f,onSelect:Z})}),M=b.map((h,_)=>{let N=ie(f,h).length;return dt({accountTypePrefix:t("addAccountPrivateKeyDefaultLabel",{number:_+1}),numberOfAccountsText:N?t("settingsClaimUsernameNoOfAccounts",{noOfAccounts:N}):t("settingsClaimUsernameEmptyAccounts"),secretId:h,isActive:o.includes(h),accounts:f,onSelect:Z})}),ne=[...y,...M],Y=()=>self.open(re);return x.default.createElement("div",{className:R},e?x.default.createElement(Q,{icon:x.default.createElement(G.HelpCircle,{className:oe({cursor:"pointer"}),size:16,color:"textSecondary"}),disableIconBackground:!0,onLeftButtonClick:i,onIconClick:Y},x.default.createElement(X,{numOfItems:6,currentIndex:3,maxVisible:5})):null,x.default.createElement("div",{className:me},x.default.createElement("div",{className:le},x.default.createElement(tt,null),x.default.createElement(I,{font:"heading3Semibold",children:t("settingsClaimUsernameAnonymousAuthTitle")}),x.default.createElement(I,{font:"caption",children:t("settingsClaimUsernameAnonymousAuthDescription"),color:"textSecondary",className:oe({textAlign:"center"})}),x.default.createElement(be,{text:t("settingsClaimUsernameAnonymousAuthBadge"),rightIcon:"info",onClick:Y})),x.default.createElement("div",{className:ce},x.default.createElement(ae,{rows:ne}))),x.default.createElement(V,{theme:"primary",className:$,onClick:ee,disabled:!o?.length},t("settingsClaimUsernameSaveAndContinue")))};export{Jn as a,Qn as b,or as c,Nr as d,Li as e,Di as f,be as g,xo as h,Cn as i,$ as j,R as k,An as l,yn as m,In as n,En as o,kn as p,Tn as q,Pn as r,wn as s,jn as t,Mn as u,Ln as v,Nn as w,Un as x,Hn as y,Fn as z,Bn as A,On as B,Dn as C,le as D,Wn as E,Gn as F,dt as G,n1 as H};
//# sourceMappingURL=chunk-SYICDMYM.js.map
