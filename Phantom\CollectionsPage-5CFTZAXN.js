import{a as vi}from"./chunk-LUKK5BMR.js";import{a as ro}from"./chunk-LEECQOCO.js";import{a as Si}from"./chunk-K5EEWGKQ.js";import{a as _i,b as bi}from"./chunk-FI6K4ZQE.js";import{a as io}from"./chunk-ESXKWKRD.js";import{C as Xt,Ma as yt,da as pi,ga as Yt,ia as gi,pa as je,qa as Te,ra as Ci,sa as vt,w as ye}from"./chunk-JD6NH5K6.js";import{a as Qt,b as nt}from"./chunk-KJMFZ7XX.js";import"./chunk-WAFQTOB5.js";import"./chunk-CCUXU2GU.js";import{b as Vo}from"./chunk-SIDJ2NRC.js";import"./chunk-7UTGLKC7.js";import{e as eo,f as hi,g as to,i as oo}from"./chunk-AHRYSG4W.js";import"./chunk-QEXGR5WT.js";import"./chunk-P5LBFEHG.js";import{b as bt}from"./chunk-S24UABH5.js";import"./chunk-MHQYYZ7C.js";import{a as de}from"./chunk-X3ESGVCB.js";import"./chunk-QINBGLLG.js";import{a as fi}from"./chunk-SHAEZV7V.js";import"./chunk-IWGMKDQE.js";import{g as di}from"./chunk-DERIAD33.js";import"./chunk-EGXLQXDH.js";import{a as lt}from"./chunk-CCQRCL2K.js";import{h as Be,m as ui}from"./chunk-75L54KUM.js";import{a as Pe}from"./chunk-ROF5SDVA.js";import{f as mi}from"./chunk-Q67X6MF4.js";import"./chunk-IVMV7P4T.js";import"./chunk-ZON27MKP.js";import"./chunk-SMVAXKUF.js";import"./chunk-HPOS2V3B.js";import{b as ci}from"./chunk-XYJX6G2K.js";import"./chunk-MTQZ2G7K.js";import"./chunk-W27Z2YZM.js";import{A as Qr,C as an,E as Xr,F as Yr,G as ei,a as Fo,b as Kr,d as he,e as Bo,n as _t,o as Jr,y as nn}from"./chunk-2NGYUYTC.js";import{a as it}from"./chunk-H3FFS4GT.js";import"./chunk-4VDZJDFB.js";import"./chunk-XJTFMD4C.js";import"./chunk-GMBAJ6CC.js";import"./chunk-PTZMRZUV.js";import{a as jo}from"./chunk-VQVTLSDS.js";import{a as ni,b as ai,j as si}from"./chunk-OKP6DFCI.js";import{D as Zt,Da as li,Ea as Jt,J as Kt,o as y,qb as Se,ra as ri,rb as Y,u as ti,x as oi,ya as ii}from"./chunk-WIQ4WVKX.js";import{jb as Zr}from"./chunk-SD2LXVLD.js";import"./chunk-UCBZOSRF.js";import{$ as Fe,A as ot,B as Bt,C as jt,D as kr,E as Vt,F as Nr,G as Ut,H as Wr,K as Hr,R as Gr,S as Fr,U as $t,W as Go,aa as Br,ba as rt,ca as jr,ea as Vr,g as Mr,ga as Ur,ha as $r,j as Lr,q as Er,t as qr,u as Dr,w as Ho}from"./chunk-F3RUX6TF.js";import"./chunk-HRJWTAGT.js";import"./chunk-LURFXJDV.js";import{e as Pr}from"./chunk-V5T43K7V.js";import"./chunk-MNXYIK2W.js";import"./chunk-QALJXKGR.js";import{c as X}from"./chunk-MHOQBMVI.js";import"./chunk-GQEPK4C4.js";import"./chunk-BTKBODVJ.js";import{s as zr,u as Ft}from"./chunk-7ZN4F6J4.js";import{$b as Ar}from"./chunk-OUYKWOVO.js";import"./chunk-WFPABEAU.js";import"./chunk-THLBAMDB.js";import"./chunk-LDMZMUWY.js";import{c as rn,d as ln}from"./chunk-X2SBUKU4.js";import{a as tn}from"./chunk-OXFZHPMY.js";import"./chunk-OYGO47TI.js";import{l as Gt}from"./chunk-SLQBAOEK.js";import{$d as xe,L as Tr,P as on,ce as xr,i as Yl,j as Ct,k as en,l as St,m as Eo,n as qo,o as Do,p as ko}from"./chunk-MZZEJ42N.js";import{a as me}from"./chunk-E3NPIRHS.js";import{a as Wo,m as $}from"./chunk-56SJOU6P.js";import{P as Ir,V as Or,c as wr,i as Ht,x as No}from"./chunk-ALUTR72U.js";import"./chunk-N7UFQNLW.js";import{Ya as Rr}from"./chunk-L3A2KHJO.js";import"./chunk-4P36KWOF.js";import{a as B}from"./chunk-7X4NV6OJ.js";import"./chunk-UNDMYLJW.js";import{c as D,f as U,g as Xl,h as u,i as h,n as d}from"./chunk-3KENBVE7.js";var yi=D(Uo=>{"use strict";u();d();Object.defineProperty(Uo,"__esModule",{value:!0});var Ae=B();function sn(o,r){r===void 0&&(r=0);var t=Ae.useRef(!1),e=Ae.useRef(),l=Ae.useRef(o),n=Ae.useCallback(function(){return t.current},[]),i=Ae.useCallback(function(){t.current=!1,e.current&&clearTimeout(e.current),e.current=setTimeout(function(){t.current=!0,l.current()},r)},[r]),a=Ae.useCallback(function(){t.current=null,e.current&&clearTimeout(e.current)},[]);return Ae.useEffect(function(){l.current=o},[o]),Ae.useEffect(function(){return i(),a},[r]),[n,a,i]}Uo.default=sn});var Ti=D($o=>{"use strict";u();d();Object.defineProperty($o,"__esModule",{value:!0});var cn=B(),un=function(o){return(o+1)%1e6};function dn(){var o=cn.useReducer(un,0),r=o[1];return r}$o.default=dn});var wi=D(Zo=>{"use strict";u();d();Object.defineProperty(Zo,"__esModule",{value:!0});var Ri=(ln(),Xl(rn)),pn=Ri.__importDefault(yi()),fn=Ri.__importDefault(Ti());function mn(o){o===void 0&&(o=0);var r=fn.default();return pn.default(r,o)}Zo.default=mn});var Ve=D((kc,Tt)=>{u();d();var hn=Yl().default;function Ii(o){if(typeof WeakMap!="function")return null;var r=new WeakMap,t=new WeakMap;return(Ii=function(l){return l?t:r})(o)}function gn(o,r){if(!r&&o&&o.__esModule)return o;if(o===null||hn(o)!=="object"&&typeof o!="function")return{default:o};var t=Ii(r);if(t&&t.has(o))return t.get(o);var e={},l=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var n in o)if(n!=="default"&&Object.prototype.hasOwnProperty.call(o,n)){var i=l?Object.getOwnPropertyDescriptor(o,n):null;i&&(i.get||i.set)?Object.defineProperty(e,n,i):e[n]=o[n]}return e.default=o,t&&t.set(o,e),e}Tt.exports=gn,Tt.exports.__esModule=!0,Tt.exports.default=Tt.exports});var Jo=D((Hc,Re)=>{u();d();function Ko(){return Re.exports=Ko=Object.assign?Object.assign.bind():function(o){for(var r=1;r<arguments.length;r++){var t=arguments[r];for(var e in t)Object.prototype.hasOwnProperty.call(t,e)&&(o[e]=t[e])}return o},Re.exports.__esModule=!0,Re.exports.default=Re.exports,Ko.apply(this,arguments)}Re.exports=Ko,Re.exports.__esModule=!0,Re.exports.default=Re.exports});var at=D((Bc,Rt)=>{u();d();var Cn=en();function Sn(o,r,t){return r=Cn(r),r in o?Object.defineProperty(o,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):o[r]=t,o}Rt.exports=Sn,Rt.exports.__esModule=!0,Rt.exports.default=Rt.exports});var Oi=D(Qo=>{"use strict";u();d();Object.defineProperty(Qo,"__esModule",{value:!0});Qo.default=_n;function _n(o){var r=o.cellCount,t=o.cellSize,e=o.computeMetadataCallback,l=o.computeMetadataCallbackProps,n=o.nextCellsCount,i=o.nextCellSize,a=o.nextScrollToIndex,s=o.scrollToIndex,c=o.updateScrollOffsetForScrollToIndex;(r!==n||(typeof t=="number"||typeof i=="number")&&t!==i)&&(e(l),s>=0&&s===a&&c())}});var zi=D((Kc,wt)=>{u();d();function bn(o,r){if(o==null)return{};var t={},e=Object.keys(o),l,n;for(n=0;n<e.length;n++)l=e[n],!(r.indexOf(l)>=0)&&(t[l]=o[l]);return t}wt.exports=bn,wt.exports.__esModule=!0,wt.exports.default=wt.exports});var xi=D((Xc,It)=>{u();d();var vn=zi();function yn(o,r){if(o==null)return{};var t=vn(o,r),e,l;if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(o);for(l=0;l<n.length;l++)e=n[l],!(r.indexOf(e)>=0)&&Object.prototype.propertyIsEnumerable.call(o,e)&&(t[e]=o[e])}return t}It.exports=yn,It.exports.__esModule=!0,It.exports.default=It.exports});var we=D(P=>{"use strict";u();d();var Pi=me(),Tn=Ve();Object.defineProperty(P,"__esModule",{value:!0});P.bpfrpt_proptype_VisibleCellRange=P.bpfrpt_proptype_Alignment=P.bpfrpt_proptype_OverscanIndicesGetter=P.bpfrpt_proptype_OverscanIndices=P.bpfrpt_proptype_OverscanIndicesGetterParams=P.bpfrpt_proptype_RenderedSection=P.bpfrpt_proptype_ScrollbarPresenceChange=P.bpfrpt_proptype_Scroll=P.bpfrpt_proptype_NoContentRenderer=P.bpfrpt_proptype_CellSize=P.bpfrpt_proptype_CellSizeGetter=P.bpfrpt_proptype_CellRangeRenderer=P.bpfrpt_proptype_CellRangeRendererParams=P.bpfrpt_proptype_StyleCache=P.bpfrpt_proptype_CellCache=P.bpfrpt_proptype_CellRenderer=P.bpfrpt_proptype_CellRendererParams=P.bpfrpt_proptype_CellPosition=void 0;var tu=Tn(B()),lo=Pi(no()),C=Pi(it()),Rn=h.NODE_ENV==="production"?null:{columnIndex:C.default.number.isRequired,rowIndex:C.default.number.isRequired};P.bpfrpt_proptype_CellPosition=Rn;var wn=h.NODE_ENV==="production"?null:{columnIndex:C.default.number.isRequired,isScrolling:C.default.bool.isRequired,isVisible:C.default.bool.isRequired,key:C.default.string.isRequired,parent:C.default.object.isRequired,rowIndex:C.default.number.isRequired,style:C.default.object.isRequired};P.bpfrpt_proptype_CellRendererParams=wn;var In=h.NODE_ENV==="production"?null:C.default.func;P.bpfrpt_proptype_CellRenderer=In;var On=h.NODE_ENV==="production"?null:C.default.objectOf(C.default.node.isRequired);P.bpfrpt_proptype_CellCache=On;var zn=h.NODE_ENV==="production"?null:C.default.objectOf(C.default.object.isRequired);P.bpfrpt_proptype_StyleCache=zn;var xn=h.NODE_ENV==="production"?null:{cellCache:C.default.objectOf(C.default.node.isRequired).isRequired,cellRenderer:C.default.func.isRequired,columnSizeAndPositionManager:function(){return(typeof lo.default=="function"?C.default.instanceOf(lo.default).isRequired:C.default.any.isRequired).apply(this,arguments)},columnStartIndex:C.default.number.isRequired,columnStopIndex:C.default.number.isRequired,deferredMeasurementCache:C.default.object,horizontalOffsetAdjustment:C.default.number.isRequired,isScrolling:C.default.bool.isRequired,isScrollingOptOut:C.default.bool.isRequired,parent:C.default.object.isRequired,rowSizeAndPositionManager:function(){return(typeof lo.default=="function"?C.default.instanceOf(lo.default).isRequired:C.default.any.isRequired).apply(this,arguments)},rowStartIndex:C.default.number.isRequired,rowStopIndex:C.default.number.isRequired,scrollLeft:C.default.number.isRequired,scrollTop:C.default.number.isRequired,styleCache:C.default.objectOf(C.default.object.isRequired).isRequired,verticalOffsetAdjustment:C.default.number.isRequired,visibleColumnIndices:C.default.object.isRequired,visibleRowIndices:C.default.object.isRequired};P.bpfrpt_proptype_CellRangeRendererParams=xn;var Pn=h.NODE_ENV==="production"?null:C.default.func;P.bpfrpt_proptype_CellRangeRenderer=Pn;var An=h.NODE_ENV==="production"?null:C.default.func;P.bpfrpt_proptype_CellSizeGetter=An;var Mn=h.NODE_ENV==="production"?null:C.default.oneOfType([C.default.func,C.default.number]);P.bpfrpt_proptype_CellSize=Mn;var Ln=h.NODE_ENV==="production"?null:C.default.func;P.bpfrpt_proptype_NoContentRenderer=Ln;var En=h.NODE_ENV==="production"?null:{clientHeight:C.default.number.isRequired,clientWidth:C.default.number.isRequired,scrollHeight:C.default.number.isRequired,scrollLeft:C.default.number.isRequired,scrollTop:C.default.number.isRequired,scrollWidth:C.default.number.isRequired};P.bpfrpt_proptype_Scroll=En;var qn=h.NODE_ENV==="production"?null:{horizontal:C.default.bool.isRequired,vertical:C.default.bool.isRequired,size:C.default.number.isRequired};P.bpfrpt_proptype_ScrollbarPresenceChange=qn;var Dn=h.NODE_ENV==="production"?null:{columnOverscanStartIndex:C.default.number.isRequired,columnOverscanStopIndex:C.default.number.isRequired,columnStartIndex:C.default.number.isRequired,columnStopIndex:C.default.number.isRequired,rowOverscanStartIndex:C.default.number.isRequired,rowOverscanStopIndex:C.default.number.isRequired,rowStartIndex:C.default.number.isRequired,rowStopIndex:C.default.number.isRequired};P.bpfrpt_proptype_RenderedSection=Dn;var kn=h.NODE_ENV==="production"?null:{direction:C.default.oneOf(["horizontal","vertical"]).isRequired,scrollDirection:C.default.oneOf([-1,1]).isRequired,cellCount:C.default.number.isRequired,overscanCellsCount:C.default.number.isRequired,startIndex:C.default.number.isRequired,stopIndex:C.default.number.isRequired};P.bpfrpt_proptype_OverscanIndicesGetterParams=kn;var Nn=h.NODE_ENV==="production"?null:{overscanStartIndex:C.default.number.isRequired,overscanStopIndex:C.default.number.isRequired};P.bpfrpt_proptype_OverscanIndices=Nn;var Wn=h.NODE_ENV==="production"?null:C.default.func;P.bpfrpt_proptype_OverscanIndicesGetter=Wn;var Hn=h.NODE_ENV==="production"?null:C.default.oneOf(["auto","end","start","center"]);P.bpfrpt_proptype_Alignment=Hn;var Gn=h.NODE_ENV==="production"?null:{start:C.default.number,stop:C.default.number};P.bpfrpt_proptype_VisibleCellRange=Gn});var Ai=D(ao=>{"use strict";u();d();var Xo=me();Object.defineProperty(ao,"__esModule",{value:!0});ao.default=void 0;var Fn=Xo(Ct()),Bn=Xo(St()),st=Xo(at()),lu=we(),jn=function(){function o(r){var t=r.cellCount,e=r.cellSizeGetter,l=r.estimatedCellSize;(0,Fn.default)(this,o),(0,st.default)(this,"_cellSizeAndPositionData",{}),(0,st.default)(this,"_lastMeasuredIndex",-1),(0,st.default)(this,"_lastBatchedIndex",-1),(0,st.default)(this,"_cellCount",void 0),(0,st.default)(this,"_cellSizeGetter",void 0),(0,st.default)(this,"_estimatedCellSize",void 0),this._cellSizeGetter=e,this._cellCount=t,this._estimatedCellSize=l}return(0,Bn.default)(o,[{key:"areOffsetsAdjusted",value:function(){return!1}},{key:"configure",value:function(t){var e=t.cellCount,l=t.estimatedCellSize,n=t.cellSizeGetter;this._cellCount=e,this._estimatedCellSize=l,this._cellSizeGetter=n}},{key:"getCellCount",value:function(){return this._cellCount}},{key:"getEstimatedCellSize",value:function(){return this._estimatedCellSize}},{key:"getLastMeasuredIndex",value:function(){return this._lastMeasuredIndex}},{key:"getOffsetAdjustment",value:function(){return 0}},{key:"getSizeAndPositionOfCell",value:function(t){if(t<0||t>=this._cellCount)throw Error("Requested index ".concat(t," is outside of range 0..").concat(this._cellCount));if(t>this._lastMeasuredIndex)for(var e=this.getSizeAndPositionOfLastMeasuredCell(),l=e.offset+e.size,n=this._lastMeasuredIndex+1;n<=t;n++){var i=this._cellSizeGetter({index:n});if(i===void 0||isNaN(i))throw Error("Invalid size returned for cell ".concat(n," of value ").concat(i));i===null?(this._cellSizeAndPositionData[n]={offset:l,size:0},this._lastBatchedIndex=t):(this._cellSizeAndPositionData[n]={offset:l,size:i},l+=i,this._lastMeasuredIndex=t)}return this._cellSizeAndPositionData[t]}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._lastMeasuredIndex>=0?this._cellSizeAndPositionData[this._lastMeasuredIndex]:{offset:0,size:0}}},{key:"getTotalSize",value:function(){var t=this.getSizeAndPositionOfLastMeasuredCell(),e=t.offset+t.size,l=this._cellCount-this._lastMeasuredIndex-1,n=l*this._estimatedCellSize;return e+n}},{key:"getUpdatedOffsetForIndex",value:function(t){var e=t.align,l=e===void 0?"auto":e,n=t.containerSize,i=t.currentOffset,a=t.targetIndex;if(n<=0)return 0;var s=this.getSizeAndPositionOfCell(a),c=s.offset,p=c-n+s.size,m;switch(l){case"start":m=c;break;case"end":m=p;break;case"center":m=c-(n-s.size)/2;break;default:m=Math.max(p,Math.min(c,i));break}var f=this.getTotalSize();return Math.max(0,Math.min(f-n,m))}},{key:"getVisibleCellRange",value:function(t){var e=t.containerSize,l=t.offset,n=this.getTotalSize();if(n===0)return{};var i=l+e,a=this._findNearestCell(l),s=this.getSizeAndPositionOfCell(a);l=s.offset+s.size;for(var c=a;l<i&&c<this._cellCount-1;)c++,l+=this.getSizeAndPositionOfCell(c).size;return{start:a,stop:c}}},{key:"resetCell",value:function(t){this._lastMeasuredIndex=Math.min(this._lastMeasuredIndex,t-1)}},{key:"_binarySearch",value:function(t,e,l){for(;e<=t;){var n=e+Math.floor((t-e)/2),i=this.getSizeAndPositionOfCell(n).offset;if(i===l)return n;i<l?e=n+1:i>l&&(t=n-1)}return e>0?e-1:0}},{key:"_exponentialSearch",value:function(t,e){for(var l=1;t<this._cellCount&&this.getSizeAndPositionOfCell(t).offset<e;)t+=l,l*=2;return this._binarySearch(Math.min(t,this._cellCount-1),Math.floor(t/2),e)}},{key:"_findNearestCell",value:function(t){if(isNaN(t))throw Error("Invalid offset ".concat(t," specified"));t=Math.max(0,t);var e=this.getSizeAndPositionOfLastMeasuredCell(),l=Math.max(0,this._lastMeasuredIndex);return e.offset>=t?this._binarySearch(l,0,t):this._exponentialSearch(l,t)}}]),o}();ao.default=jn});var Mi=D(so=>{"use strict";u();d();Object.defineProperty(so,"__esModule",{value:!0});so.getMaxElementSize=void 0;var Vn=15e5,Un=16777100,$n=function(){return typeof self<"u"},Zn=function(){return!!self.chrome},Kn=function(){return $n()&&Zn()?Un:Vn};so.getMaxElementSize=Kn});var no=D(co=>{"use strict";u();d();var Ot=me();Object.defineProperty(co,"__esModule",{value:!0});co.default=void 0;var Jn=Ot(xi()),Qn=Ot(Ct()),Xn=Ot(St()),Li=Ot(at()),Yn=Ot(Ai()),ea=Mi(),pu=we(),ta=function(){function o(r){var t=r.maxScrollSize,e=t===void 0?(0,ea.getMaxElementSize)():t,l=(0,Jn.default)(r,["maxScrollSize"]);(0,Qn.default)(this,o),(0,Li.default)(this,"_cellSizeAndPositionManager",void 0),(0,Li.default)(this,"_maxScrollSize",void 0),this._cellSizeAndPositionManager=new Yn.default(l),this._maxScrollSize=e}return(0,Xn.default)(o,[{key:"areOffsetsAdjusted",value:function(){return this._cellSizeAndPositionManager.getTotalSize()>this._maxScrollSize}},{key:"configure",value:function(t){this._cellSizeAndPositionManager.configure(t)}},{key:"getCellCount",value:function(){return this._cellSizeAndPositionManager.getCellCount()}},{key:"getEstimatedCellSize",value:function(){return this._cellSizeAndPositionManager.getEstimatedCellSize()}},{key:"getLastMeasuredIndex",value:function(){return this._cellSizeAndPositionManager.getLastMeasuredIndex()}},{key:"getOffsetAdjustment",value:function(t){var e=t.containerSize,l=t.offset,n=this._cellSizeAndPositionManager.getTotalSize(),i=this.getTotalSize(),a=this._getOffsetPercentage({containerSize:e,offset:l,totalSize:i});return Math.round(a*(i-n))}},{key:"getSizeAndPositionOfCell",value:function(t){return this._cellSizeAndPositionManager.getSizeAndPositionOfCell(t)}},{key:"getSizeAndPositionOfLastMeasuredCell",value:function(){return this._cellSizeAndPositionManager.getSizeAndPositionOfLastMeasuredCell()}},{key:"getTotalSize",value:function(){return Math.min(this._maxScrollSize,this._cellSizeAndPositionManager.getTotalSize())}},{key:"getUpdatedOffsetForIndex",value:function(t){var e=t.align,l=e===void 0?"auto":e,n=t.containerSize,i=t.currentOffset,a=t.targetIndex;i=this._safeOffsetToOffset({containerSize:n,offset:i});var s=this._cellSizeAndPositionManager.getUpdatedOffsetForIndex({align:l,containerSize:n,currentOffset:i,targetIndex:a});return this._offsetToSafeOffset({containerSize:n,offset:s})}},{key:"getVisibleCellRange",value:function(t){var e=t.containerSize,l=t.offset;return l=this._safeOffsetToOffset({containerSize:e,offset:l}),this._cellSizeAndPositionManager.getVisibleCellRange({containerSize:e,offset:l})}},{key:"resetCell",value:function(t){this._cellSizeAndPositionManager.resetCell(t)}},{key:"_getOffsetPercentage",value:function(t){var e=t.containerSize,l=t.offset,n=t.totalSize;return n<=e?0:l/(n-e)}},{key:"_offsetToSafeOffset",value:function(t){var e=t.containerSize,l=t.offset,n=this._cellSizeAndPositionManager.getTotalSize(),i=this.getTotalSize();if(n===i)return l;var a=this._getOffsetPercentage({containerSize:e,offset:l,totalSize:n});return Math.round(a*(i-e))}},{key:"_safeOffsetToOffset",value:function(t){var e=t.containerSize,l=t.offset,n=this._cellSizeAndPositionManager.getTotalSize(),i=this.getTotalSize();if(n===i)return l;var a=this._getOffsetPercentage({containerSize:e,offset:l,totalSize:i});return Math.round(a*(n-e))}}]),o}();co.default=ta});var Ei=D(Yo=>{"use strict";u();d();Object.defineProperty(Yo,"__esModule",{value:!0});Yo.default=oa;function oa(){var o=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0,r={};return function(t){var e=t.callback,l=t.indices,n=Object.keys(l),i=!o||n.every(function(s){var c=l[s];return Array.isArray(c)?c.length>0:c>=0}),a=n.length!==Object.keys(r).length||n.some(function(s){var c=r[s],p=l[s];return Array.isArray(p)?c.join(",")!==p.join(","):c!==p});r=l,i&&a&&e(l)}}});var er=D(ge=>{"use strict";u();d();Object.defineProperty(ge,"__esModule",{value:!0});ge.default=na;ge.SCROLL_DIRECTION_VERTICAL=ge.SCROLL_DIRECTION_HORIZONTAL=ge.SCROLL_DIRECTION_FORWARD=ge.SCROLL_DIRECTION_BACKWARD=void 0;var _u=we(),ra=-1;ge.SCROLL_DIRECTION_BACKWARD=ra;var qi=1;ge.SCROLL_DIRECTION_FORWARD=qi;var ia="horizontal";ge.SCROLL_DIRECTION_HORIZONTAL=ia;var la="vertical";ge.SCROLL_DIRECTION_VERTICAL=la;function na(o){var r=o.cellCount,t=o.overscanCellsCount,e=o.scrollDirection,l=o.startIndex,n=o.stopIndex;return e===qi?{overscanStartIndex:Math.max(0,l),overscanStopIndex:Math.min(r-1,n+t)}:{overscanStartIndex:Math.max(0,l-t),overscanStopIndex:Math.min(r-1,n)}}});var Di=D(tr=>{"use strict";u();d();var aa=me();Object.defineProperty(tr,"__esModule",{value:!0});tr.default=sa;var Tu=aa(no()),Ru=we();function sa(o){var r=o.cellSize,t=o.cellSizeAndPositionManager,e=o.previousCellsCount,l=o.previousCellSize,n=o.previousScrollToAlignment,i=o.previousScrollToIndex,a=o.previousSize,s=o.scrollOffset,c=o.scrollToAlignment,p=o.scrollToIndex,m=o.size,f=o.sizeJustIncreasedFromZero,_=o.updateScrollIndexCallback,S=t.getCellCount(),b=p>=0&&p<S,T=m!==a||f||!l||typeof r=="number"&&r!==l;b&&(T||c!==n||p!==i)?_(p):!b&&S>0&&(m<a||S<e)&&s>t.getTotalSize()-m&&_(S-1)}});var rr=D(or=>{"use strict";u();d();Object.defineProperty(or,"__esModule",{value:!0});or.default=ca;var zu=we();function ca(o){for(var r=o.cellCache,t=o.cellRenderer,e=o.columnSizeAndPositionManager,l=o.columnStartIndex,n=o.columnStopIndex,i=o.deferredMeasurementCache,a=o.horizontalOffsetAdjustment,s=o.isScrolling,c=o.isScrollingOptOut,p=o.parent,m=o.rowSizeAndPositionManager,f=o.rowStartIndex,_=o.rowStopIndex,S=o.styleCache,b=o.verticalOffsetAdjustment,T=o.visibleColumnIndices,w=o.visibleRowIndices,I=[],E=e.areOffsetsAdjusted()||m.areOffsetsAdjusted(),j=!s&&!E,W=f;W<=_;W++)for(var N=m.getSizeAndPositionOfCell(W),R=l;R<=n;R++){var g=e.getSizeAndPositionOfCell(R),A=R>=T.start&&R<=T.stop&&W>=w.start&&W<=w.stop,z="".concat(W,"-").concat(R),M=void 0;j&&S[z]?M=S[z]:i&&!i.has(W,R)?M={height:"auto",left:0,position:"absolute",top:0,width:"auto"}:(M={height:N.size,left:g.offset+a,position:"absolute",top:N.offset+b,width:g.size},S[z]=M);var K={columnIndex:R,isScrolling:s,isVisible:A,key:z,parent:p,rowIndex:W,style:M},J=void 0;(c||s)&&!a&&!b?(r[z]||(r[z]=t(K)),J=r[z]):J=t(K),!(J==null||J===!1)&&(h.NODE_ENV!=="production"&&ua(p,J),I.push(J))}return I}function ua(o,r){h.NODE_ENV!=="production"&&r&&(r.type&&r.type.__internalCellMeasurerFlag&&(r=r.props.children),r&&r.props&&r.props.style===void 0&&o.__warnedAboutMissingStyle!==!0&&(o.__warnedAboutMissingStyle=!0,console.warn("Rendered cell should include style property for positioning.")))}});var ki=D(ct=>{"use strict";u();d();Object.defineProperty(ct,"__esModule",{value:!0});ct.caf=ct.raf=void 0;var re;typeof self<"u"||typeof self<"u"?re=self:re={};var da=re.requestAnimationFrame||re.webkitRequestAnimationFrame||re.mozRequestAnimationFrame||re.oRequestAnimationFrame||re.msRequestAnimationFrame||function(o){return re.setTimeout(o,1e3/60)},pa=re.cancelAnimationFrame||re.webkitCancelAnimationFrame||re.mozCancelAnimationFrame||re.oCancelAnimationFrame||re.msCancelAnimationFrame||function(o){re.clearTimeout(o)},fa=da;ct.raf=fa;var ma=pa;ct.caf=ma});var lr=D(Me=>{"use strict";u();d();var ha=me();Object.defineProperty(Me,"__esModule",{value:!0});Me.bpfrpt_proptype_AnimationTimeoutId=Me.requestAnimationTimeout=Me.cancelAnimationTimeout=void 0;var ir=ki(),ga=ha(it()),Ca=h.NODE_ENV==="production"?null:{id:ga.default.number.isRequired};Me.bpfrpt_proptype_AnimationTimeoutId=Ca;var Sa=function(r){return(0,ir.caf)(r.id)};Me.cancelAnimationTimeout=Sa;var _a=function(r,t){var e;Promise.resolve().then(function(){e=Date.now()});var l=function i(){Date.now()-e>=t?r.call():n.id=(0,ir.raf)(i)},n={id:(0,ir.raf)(l)};return n};Me.requestAnimationTimeout=_a});var $i=D(ut=>{"use strict";u();d();var te=me(),Vi=Ve();Object.defineProperty(ut,"__esModule",{value:!0});ut.default=ut.DEFAULT_SCROLLING_RESET_TIME_INTERVAL=void 0;var ba=te(Jo()),va=te(Ct()),ya=te(St()),Ta=te(Do()),Ra=te(ko()),H=te(qo()),wa=te(Eo()),q=te(at()),nr=Vi(B()),Ia=te(Qr()),Ni=te(Oi()),Wi=te(no()),Hi=te(Ei()),pe=Vi(er()),Gi=te(Di()),Oa=te(rr()),za=te(an()),xa=nn(),ar=lr(),L=we(),x=te(it()),Fi,Bi;function ji(o,r){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(o);r&&(e=e.filter(function(l){return Object.getOwnPropertyDescriptor(o,l).enumerable})),t.push.apply(t,e)}return t}function Ue(o){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?ji(t,!0).forEach(function(e){(0,q.default)(o,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):ji(t).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(t,e))})}return o}var Ui=150;ut.DEFAULT_SCROLLING_RESET_TIME_INTERVAL=Ui;var sr={OBSERVED:"observed",REQUESTED:"requested"},Pa=function(){return null},cr=(Bi=Fi=function(o){(0,wa.default)(r,o);function r(t){var e;(0,va.default)(this,r),e=(0,Ta.default)(this,(0,Ra.default)(r).call(this,t)),(0,q.default)((0,H.default)(e),"_onGridRenderedMemoizer",(0,Hi.default)()),(0,q.default)((0,H.default)(e),"_onScrollMemoizer",(0,Hi.default)(!1)),(0,q.default)((0,H.default)(e),"_deferredInvalidateColumnIndex",null),(0,q.default)((0,H.default)(e),"_deferredInvalidateRowIndex",null),(0,q.default)((0,H.default)(e),"_recomputeScrollLeftFlag",!1),(0,q.default)((0,H.default)(e),"_recomputeScrollTopFlag",!1),(0,q.default)((0,H.default)(e),"_horizontalScrollBarSize",0),(0,q.default)((0,H.default)(e),"_verticalScrollBarSize",0),(0,q.default)((0,H.default)(e),"_scrollbarPresenceChanged",!1),(0,q.default)((0,H.default)(e),"_scrollingContainer",void 0),(0,q.default)((0,H.default)(e),"_childrenToDisplay",void 0),(0,q.default)((0,H.default)(e),"_columnStartIndex",void 0),(0,q.default)((0,H.default)(e),"_columnStopIndex",void 0),(0,q.default)((0,H.default)(e),"_rowStartIndex",void 0),(0,q.default)((0,H.default)(e),"_rowStopIndex",void 0),(0,q.default)((0,H.default)(e),"_renderedColumnStartIndex",0),(0,q.default)((0,H.default)(e),"_renderedColumnStopIndex",0),(0,q.default)((0,H.default)(e),"_renderedRowStartIndex",0),(0,q.default)((0,H.default)(e),"_renderedRowStopIndex",0),(0,q.default)((0,H.default)(e),"_initialScrollTop",void 0),(0,q.default)((0,H.default)(e),"_initialScrollLeft",void 0),(0,q.default)((0,H.default)(e),"_disablePointerEventsTimeoutId",void 0),(0,q.default)((0,H.default)(e),"_styleCache",{}),(0,q.default)((0,H.default)(e),"_cellCache",{}),(0,q.default)((0,H.default)(e),"_debounceScrollEndedCallback",function(){e._disablePointerEventsTimeoutId=null,e.setState({isScrolling:!1,needToResetStyleCache:!1})}),(0,q.default)((0,H.default)(e),"_invokeOnGridRenderedHelper",function(){var i=e.props.onSectionRendered;e._onGridRenderedMemoizer({callback:i,indices:{columnOverscanStartIndex:e._columnStartIndex,columnOverscanStopIndex:e._columnStopIndex,columnStartIndex:e._renderedColumnStartIndex,columnStopIndex:e._renderedColumnStopIndex,rowOverscanStartIndex:e._rowStartIndex,rowOverscanStopIndex:e._rowStopIndex,rowStartIndex:e._renderedRowStartIndex,rowStopIndex:e._renderedRowStopIndex}})}),(0,q.default)((0,H.default)(e),"_setScrollingContainerRef",function(i){e._scrollingContainer=i}),(0,q.default)((0,H.default)(e),"_onScroll",function(i){i.target===e._scrollingContainer&&e.handleScrollEvent(i.target)});var l=new Wi.default({cellCount:t.columnCount,cellSizeGetter:function(a){return r._wrapSizeGetter(t.columnWidth)(a)},estimatedCellSize:r._getEstimatedColumnSize(t)}),n=new Wi.default({cellCount:t.rowCount,cellSizeGetter:function(a){return r._wrapSizeGetter(t.rowHeight)(a)},estimatedCellSize:r._getEstimatedRowSize(t)});return e.state={instanceProps:{columnSizeAndPositionManager:l,rowSizeAndPositionManager:n,prevColumnWidth:t.columnWidth,prevRowHeight:t.rowHeight,prevColumnCount:t.columnCount,prevRowCount:t.rowCount,prevIsScrolling:t.isScrolling===!0,prevScrollToColumn:t.scrollToColumn,prevScrollToRow:t.scrollToRow,scrollbarSize:0,scrollbarSizeMeasured:!1},isScrolling:!1,scrollDirectionHorizontal:pe.SCROLL_DIRECTION_FORWARD,scrollDirectionVertical:pe.SCROLL_DIRECTION_FORWARD,scrollLeft:0,scrollTop:0,scrollPositionChangeReason:null,needToResetStyleCache:!1},t.scrollToRow>0&&(e._initialScrollTop=e._getCalculatedScrollTop(t,e.state)),t.scrollToColumn>0&&(e._initialScrollLeft=e._getCalculatedScrollLeft(t,e.state)),e}return(0,ya.default)(r,[{key:"getOffsetForCell",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},l=e.alignment,n=l===void 0?this.props.scrollToAlignment:l,i=e.columnIndex,a=i===void 0?this.props.scrollToColumn:i,s=e.rowIndex,c=s===void 0?this.props.scrollToRow:s,p=Ue({},this.props,{scrollToAlignment:n,scrollToColumn:a,scrollToRow:c});return{scrollLeft:this._getCalculatedScrollLeft(p),scrollTop:this._getCalculatedScrollTop(p)}}},{key:"getTotalRowsHeight",value:function(){return this.state.instanceProps.rowSizeAndPositionManager.getTotalSize()}},{key:"getTotalColumnsWidth",value:function(){return this.state.instanceProps.columnSizeAndPositionManager.getTotalSize()}},{key:"handleScrollEvent",value:function(e){var l=e.scrollLeft,n=l===void 0?0:l,i=e.scrollTop,a=i===void 0?0:i;if(!(a<0)){this._debounceScrollEnded();var s=this.props,c=s.autoHeight,p=s.autoWidth,m=s.height,f=s.width,_=this.state.instanceProps,S=_.scrollbarSize,b=_.rowSizeAndPositionManager.getTotalSize(),T=_.columnSizeAndPositionManager.getTotalSize(),w=Math.min(Math.max(0,T-f+S),n),I=Math.min(Math.max(0,b-m+S),a);if(this.state.scrollLeft!==w||this.state.scrollTop!==I){var E=w!==this.state.scrollLeft?w>this.state.scrollLeft?pe.SCROLL_DIRECTION_FORWARD:pe.SCROLL_DIRECTION_BACKWARD:this.state.scrollDirectionHorizontal,j=I!==this.state.scrollTop?I>this.state.scrollTop?pe.SCROLL_DIRECTION_FORWARD:pe.SCROLL_DIRECTION_BACKWARD:this.state.scrollDirectionVertical,W={isScrolling:!0,scrollDirectionHorizontal:E,scrollDirectionVertical:j,scrollPositionChangeReason:sr.OBSERVED};c||(W.scrollTop=I),p||(W.scrollLeft=w),W.needToResetStyleCache=!1,this.setState(W)}this._invokeOnScrollMemoizer({scrollLeft:w,scrollTop:I,totalColumnsWidth:T,totalRowsHeight:b})}}},{key:"invalidateCellSizeAfterRender",value:function(e){var l=e.columnIndex,n=e.rowIndex;this._deferredInvalidateColumnIndex=typeof this._deferredInvalidateColumnIndex=="number"?Math.min(this._deferredInvalidateColumnIndex,l):l,this._deferredInvalidateRowIndex=typeof this._deferredInvalidateRowIndex=="number"?Math.min(this._deferredInvalidateRowIndex,n):n}},{key:"measureAllCells",value:function(){var e=this.props,l=e.columnCount,n=e.rowCount,i=this.state.instanceProps;i.columnSizeAndPositionManager.getSizeAndPositionOfCell(l-1),i.rowSizeAndPositionManager.getSizeAndPositionOfCell(n-1)}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},l=e.columnIndex,n=l===void 0?0:l,i=e.rowIndex,a=i===void 0?0:i,s=this.props,c=s.scrollToColumn,p=s.scrollToRow,m=this.state.instanceProps;m.columnSizeAndPositionManager.resetCell(n),m.rowSizeAndPositionManager.resetCell(a),this._recomputeScrollLeftFlag=c>=0&&(this.state.scrollDirectionHorizontal===pe.SCROLL_DIRECTION_FORWARD?n<=c:n>=c),this._recomputeScrollTopFlag=p>=0&&(this.state.scrollDirectionVertical===pe.SCROLL_DIRECTION_FORWARD?a<=p:a>=p),this._styleCache={},this._cellCache={},this.forceUpdate()}},{key:"scrollToCell",value:function(e){var l=e.columnIndex,n=e.rowIndex,i=this.props.columnCount,a=this.props;i>1&&l!==void 0&&this._updateScrollLeftForScrollToColumn(Ue({},a,{scrollToColumn:l})),n!==void 0&&this._updateScrollTopForScrollToRow(Ue({},a,{scrollToRow:n}))}},{key:"componentDidMount",value:function(){var e=this.props,l=e.getScrollbarSize,n=e.height,i=e.scrollLeft,a=e.scrollToColumn,s=e.scrollTop,c=e.scrollToRow,p=e.width,m=this.state.instanceProps;if(this._initialScrollTop=0,this._initialScrollLeft=0,this._handleInvalidatedGridSize(),m.scrollbarSizeMeasured||this.setState(function(S){var b=Ue({},S,{needToResetStyleCache:!1});return b.instanceProps.scrollbarSize=l(),b.instanceProps.scrollbarSizeMeasured=!0,b}),typeof i=="number"&&i>=0||typeof s=="number"&&s>=0){var f=r._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:i,scrollTop:s});f&&(f.needToResetStyleCache=!1,this.setState(f))}this._scrollingContainer&&(this._scrollingContainer.scrollLeft!==this.state.scrollLeft&&(this._scrollingContainer.scrollLeft=this.state.scrollLeft),this._scrollingContainer.scrollTop!==this.state.scrollTop&&(this._scrollingContainer.scrollTop=this.state.scrollTop));var _=n>0&&p>0;a>=0&&_&&this._updateScrollLeftForScrollToColumn(),c>=0&&_&&this._updateScrollTopForScrollToRow(),this._invokeOnGridRenderedHelper(),this._invokeOnScrollMemoizer({scrollLeft:i||0,scrollTop:s||0,totalColumnsWidth:m.columnSizeAndPositionManager.getTotalSize(),totalRowsHeight:m.rowSizeAndPositionManager.getTotalSize()}),this._maybeCallOnScrollbarPresenceChange()}},{key:"componentDidUpdate",value:function(e,l){var n=this,i=this.props,a=i.autoHeight,s=i.autoWidth,c=i.columnCount,p=i.height,m=i.rowCount,f=i.scrollToAlignment,_=i.scrollToColumn,S=i.scrollToRow,b=i.width,T=this.state,w=T.scrollLeft,I=T.scrollPositionChangeReason,E=T.scrollTop,j=T.instanceProps;this._handleInvalidatedGridSize();var W=c>0&&e.columnCount===0||m>0&&e.rowCount===0;I===sr.REQUESTED&&(!s&&w>=0&&(w!==this._scrollingContainer.scrollLeft||W)&&(this._scrollingContainer.scrollLeft=w),!a&&E>=0&&(E!==this._scrollingContainer.scrollTop||W)&&(this._scrollingContainer.scrollTop=E));var N=(e.width===0||e.height===0)&&p>0&&b>0;if(this._recomputeScrollLeftFlag?(this._recomputeScrollLeftFlag=!1,this._updateScrollLeftForScrollToColumn(this.props)):(0,Gi.default)({cellSizeAndPositionManager:j.columnSizeAndPositionManager,previousCellsCount:e.columnCount,previousCellSize:e.columnWidth,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToColumn,previousSize:e.width,scrollOffset:w,scrollToAlignment:f,scrollToIndex:_,size:b,sizeJustIncreasedFromZero:N,updateScrollIndexCallback:function(){return n._updateScrollLeftForScrollToColumn(n.props)}}),this._recomputeScrollTopFlag?(this._recomputeScrollTopFlag=!1,this._updateScrollTopForScrollToRow(this.props)):(0,Gi.default)({cellSizeAndPositionManager:j.rowSizeAndPositionManager,previousCellsCount:e.rowCount,previousCellSize:e.rowHeight,previousScrollToAlignment:e.scrollToAlignment,previousScrollToIndex:e.scrollToRow,previousSize:e.height,scrollOffset:E,scrollToAlignment:f,scrollToIndex:S,size:p,sizeJustIncreasedFromZero:N,updateScrollIndexCallback:function(){return n._updateScrollTopForScrollToRow(n.props)}}),this._invokeOnGridRenderedHelper(),w!==l.scrollLeft||E!==l.scrollTop){var R=j.rowSizeAndPositionManager.getTotalSize(),g=j.columnSizeAndPositionManager.getTotalSize();this._invokeOnScrollMemoizer({scrollLeft:w,scrollTop:E,totalColumnsWidth:g,totalRowsHeight:R})}this._maybeCallOnScrollbarPresenceChange()}},{key:"componentWillUnmount",value:function(){this._disablePointerEventsTimeoutId&&(0,ar.cancelAnimationTimeout)(this._disablePointerEventsTimeoutId)}},{key:"render",value:function(){var e=this.props,l=e.autoContainerWidth,n=e.autoHeight,i=e.autoWidth,a=e.className,s=e.containerProps,c=e.containerRole,p=e.containerStyle,m=e.height,f=e.id,_=e.noContentRenderer,S=e.role,b=e.style,T=e.tabIndex,w=e.width,I=this.state,E=I.instanceProps,j=I.needToResetStyleCache,W=this._isScrolling(),N={boxSizing:"border-box",direction:"ltr",height:n?"auto":m,position:"relative",width:i?"auto":w,WebkitOverflowScrolling:"touch",willChange:"transform"};j&&(this._styleCache={}),this.state.isScrolling||this._resetStyleCache(),this._calculateChildrenToRender(this.props,this.state);var R=E.columnSizeAndPositionManager.getTotalSize(),g=E.rowSizeAndPositionManager.getTotalSize(),A=g>m?E.scrollbarSize:0,z=R>w?E.scrollbarSize:0;(z!==this._horizontalScrollBarSize||A!==this._verticalScrollBarSize)&&(this._horizontalScrollBarSize=z,this._verticalScrollBarSize=A,this._scrollbarPresenceChanged=!0),N.overflowX=R+A<=w?"hidden":"auto",N.overflowY=g+z<=m?"hidden":"auto";var M=this._childrenToDisplay,K=M.length===0&&m>0&&w>0;return nr.createElement("div",(0,ba.default)({ref:this._setScrollingContainerRef},s,{"aria-label":this.props["aria-label"],"aria-readonly":this.props["aria-readonly"],className:(0,Ia.default)("ReactVirtualized__Grid",a),id:f,onScroll:this._onScroll,role:S,style:Ue({},N,{},b),tabIndex:T}),M.length>0&&nr.createElement("div",{className:"ReactVirtualized__Grid__innerScrollContainer",role:c,style:Ue({width:l?"auto":R,height:g,maxWidth:R,maxHeight:g,overflow:"hidden",pointerEvents:W?"none":"",position:"relative"},p)},M),K&&_())}},{key:"_calculateChildrenToRender",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state,n=e.cellRenderer,i=e.cellRangeRenderer,a=e.columnCount,s=e.deferredMeasurementCache,c=e.height,p=e.overscanColumnCount,m=e.overscanIndicesGetter,f=e.overscanRowCount,_=e.rowCount,S=e.width,b=e.isScrollingOptOut,T=l.scrollDirectionHorizontal,w=l.scrollDirectionVertical,I=l.instanceProps,E=this._initialScrollTop>0?this._initialScrollTop:l.scrollTop,j=this._initialScrollLeft>0?this._initialScrollLeft:l.scrollLeft,W=this._isScrolling(e,l);if(this._childrenToDisplay=[],c>0&&S>0){var N=I.columnSizeAndPositionManager.getVisibleCellRange({containerSize:S,offset:j}),R=I.rowSizeAndPositionManager.getVisibleCellRange({containerSize:c,offset:E}),g=I.columnSizeAndPositionManager.getOffsetAdjustment({containerSize:S,offset:j}),A=I.rowSizeAndPositionManager.getOffsetAdjustment({containerSize:c,offset:E});this._renderedColumnStartIndex=N.start,this._renderedColumnStopIndex=N.stop,this._renderedRowStartIndex=R.start,this._renderedRowStopIndex=R.stop;var z=m({direction:"horizontal",cellCount:a,overscanCellsCount:p,scrollDirection:T,startIndex:typeof N.start=="number"?N.start:0,stopIndex:typeof N.stop=="number"?N.stop:-1}),M=m({direction:"vertical",cellCount:_,overscanCellsCount:f,scrollDirection:w,startIndex:typeof R.start=="number"?R.start:0,stopIndex:typeof R.stop=="number"?R.stop:-1}),K=z.overscanStartIndex,J=z.overscanStopIndex,ne=M.overscanStartIndex,fe=M.overscanStopIndex;if(s){if(!s.hasFixedHeight()){for(var Ne=ne;Ne<=fe;Ne++)if(!s.has(Ne,0)){K=0,J=a-1;break}}if(!s.hasFixedWidth()){for(var We=K;We<=J;We++)if(!s.has(0,We)){ne=0,fe=_-1;break}}}this._childrenToDisplay=i({cellCache:this._cellCache,cellRenderer:n,columnSizeAndPositionManager:I.columnSizeAndPositionManager,columnStartIndex:K,columnStopIndex:J,deferredMeasurementCache:s,horizontalOffsetAdjustment:g,isScrolling:W,isScrollingOptOut:b,parent:this,rowSizeAndPositionManager:I.rowSizeAndPositionManager,rowStartIndex:ne,rowStopIndex:fe,scrollLeft:j,scrollTop:E,styleCache:this._styleCache,verticalOffsetAdjustment:A,visibleColumnIndices:N,visibleRowIndices:R}),this._columnStartIndex=K,this._columnStopIndex=J,this._rowStartIndex=ne,this._rowStopIndex=fe}}},{key:"_debounceScrollEnded",value:function(){var e=this.props.scrollingResetTimeInterval;this._disablePointerEventsTimeoutId&&(0,ar.cancelAnimationTimeout)(this._disablePointerEventsTimeoutId),this._disablePointerEventsTimeoutId=(0,ar.requestAnimationTimeout)(this._debounceScrollEndedCallback,e)}},{key:"_handleInvalidatedGridSize",value:function(){if(typeof this._deferredInvalidateColumnIndex=="number"&&typeof this._deferredInvalidateRowIndex=="number"){var e=this._deferredInvalidateColumnIndex,l=this._deferredInvalidateRowIndex;this._deferredInvalidateColumnIndex=null,this._deferredInvalidateRowIndex=null,this.recomputeGridSize({columnIndex:e,rowIndex:l})}}},{key:"_invokeOnScrollMemoizer",value:function(e){var l=this,n=e.scrollLeft,i=e.scrollTop,a=e.totalColumnsWidth,s=e.totalRowsHeight;this._onScrollMemoizer({callback:function(p){var m=p.scrollLeft,f=p.scrollTop,_=l.props,S=_.height,b=_.onScroll,T=_.width;b({clientHeight:S,clientWidth:T,scrollHeight:s,scrollLeft:m,scrollTop:f,scrollWidth:a})},indices:{scrollLeft:n,scrollTop:i}})}},{key:"_isScrolling",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state;return Object.hasOwnProperty.call(e,"isScrolling")?!!e.isScrolling:!!l.isScrolling}},{key:"_maybeCallOnScrollbarPresenceChange",value:function(){if(this._scrollbarPresenceChanged){var e=this.props.onScrollbarPresenceChange;this._scrollbarPresenceChanged=!1,e({horizontal:this._horizontalScrollBarSize>0,size:this.state.instanceProps.scrollbarSize,vertical:this._verticalScrollBarSize>0})}}},{key:"scrollToPosition",value:function(e){var l=e.scrollLeft,n=e.scrollTop,i=r._getScrollToPositionStateUpdate({prevState:this.state,scrollLeft:l,scrollTop:n});i&&(i.needToResetStyleCache=!1,this.setState(i))}},{key:"_getCalculatedScrollLeft",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state;return r._getCalculatedScrollLeft(e,l)}},{key:"_updateScrollLeftForScrollToColumn",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state,n=r._getScrollLeftForScrollToColumnStateUpdate(e,l);n&&(n.needToResetStyleCache=!1,this.setState(n))}},{key:"_getCalculatedScrollTop",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state;return r._getCalculatedScrollTop(e,l)}},{key:"_resetStyleCache",value:function(){var e=this._styleCache,l=this._cellCache,n=this.props.isScrollingOptOut;this._cellCache={},this._styleCache={};for(var i=this._rowStartIndex;i<=this._rowStopIndex;i++)for(var a=this._columnStartIndex;a<=this._columnStopIndex;a++){var s="".concat(i,"-").concat(a);this._styleCache[s]=e[s],n&&(this._cellCache[s]=l[s])}}},{key:"_updateScrollTopForScrollToRow",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props,l=arguments.length>1&&arguments[1]!==void 0?arguments[1]:this.state,n=r._getScrollTopForScrollToRowStateUpdate(e,l);n&&(n.needToResetStyleCache=!1,this.setState(n))}}],[{key:"getDerivedStateFromProps",value:function(e,l){var n={};e.columnCount===0&&l.scrollLeft!==0||e.rowCount===0&&l.scrollTop!==0?(n.scrollLeft=0,n.scrollTop=0):(e.scrollLeft!==l.scrollLeft&&e.scrollToColumn<0||e.scrollTop!==l.scrollTop&&e.scrollToRow<0)&&Object.assign(n,r._getScrollToPositionStateUpdate({prevState:l,scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}));var i=l.instanceProps;n.needToResetStyleCache=!1,(e.columnWidth!==i.prevColumnWidth||e.rowHeight!==i.prevRowHeight)&&(n.needToResetStyleCache=!0),i.columnSizeAndPositionManager.configure({cellCount:e.columnCount,estimatedCellSize:r._getEstimatedColumnSize(e),cellSizeGetter:r._wrapSizeGetter(e.columnWidth)}),i.rowSizeAndPositionManager.configure({cellCount:e.rowCount,estimatedCellSize:r._getEstimatedRowSize(e),cellSizeGetter:r._wrapSizeGetter(e.rowHeight)}),(i.prevColumnCount===0||i.prevRowCount===0)&&(i.prevColumnCount=0,i.prevRowCount=0),e.autoHeight&&e.isScrolling===!1&&i.prevIsScrolling===!0&&Object.assign(n,{isScrolling:!1});var a,s;return(0,Ni.default)({cellCount:i.prevColumnCount,cellSize:typeof i.prevColumnWidth=="number"?i.prevColumnWidth:null,computeMetadataCallback:function(){return i.columnSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.columnCount,nextCellSize:typeof e.columnWidth=="number"?e.columnWidth:null,nextScrollToIndex:e.scrollToColumn,scrollToIndex:i.prevScrollToColumn,updateScrollOffsetForScrollToIndex:function(){a=r._getScrollLeftForScrollToColumnStateUpdate(e,l)}}),(0,Ni.default)({cellCount:i.prevRowCount,cellSize:typeof i.prevRowHeight=="number"?i.prevRowHeight:null,computeMetadataCallback:function(){return i.rowSizeAndPositionManager.resetCell(0)},computeMetadataCallbackProps:e,nextCellsCount:e.rowCount,nextCellSize:typeof e.rowHeight=="number"?e.rowHeight:null,nextScrollToIndex:e.scrollToRow,scrollToIndex:i.prevScrollToRow,updateScrollOffsetForScrollToIndex:function(){s=r._getScrollTopForScrollToRowStateUpdate(e,l)}}),i.prevColumnCount=e.columnCount,i.prevColumnWidth=e.columnWidth,i.prevIsScrolling=e.isScrolling===!0,i.prevRowCount=e.rowCount,i.prevRowHeight=e.rowHeight,i.prevScrollToColumn=e.scrollToColumn,i.prevScrollToRow=e.scrollToRow,i.scrollbarSize=e.getScrollbarSize(),i.scrollbarSize===void 0?(i.scrollbarSizeMeasured=!1,i.scrollbarSize=0):i.scrollbarSizeMeasured=!0,n.instanceProps=i,Ue({},n,{},a,{},s)}},{key:"_getEstimatedColumnSize",value:function(e){return typeof e.columnWidth=="number"?e.columnWidth:e.estimatedColumnSize}},{key:"_getEstimatedRowSize",value:function(e){return typeof e.rowHeight=="number"?e.rowHeight:e.estimatedRowSize}},{key:"_getScrollToPositionStateUpdate",value:function(e){var l=e.prevState,n=e.scrollLeft,i=e.scrollTop,a={scrollPositionChangeReason:sr.REQUESTED};return typeof n=="number"&&n>=0&&(a.scrollDirectionHorizontal=n>l.scrollLeft?pe.SCROLL_DIRECTION_FORWARD:pe.SCROLL_DIRECTION_BACKWARD,a.scrollLeft=n),typeof i=="number"&&i>=0&&(a.scrollDirectionVertical=i>l.scrollTop?pe.SCROLL_DIRECTION_FORWARD:pe.SCROLL_DIRECTION_BACKWARD,a.scrollTop=i),typeof n=="number"&&n>=0&&n!==l.scrollLeft||typeof i=="number"&&i>=0&&i!==l.scrollTop?a:{}}},{key:"_wrapSizeGetter",value:function(e){return typeof e=="function"?e:function(){return e}}},{key:"_getCalculatedScrollLeft",value:function(e,l){var n=e.columnCount,i=e.height,a=e.scrollToAlignment,s=e.scrollToColumn,c=e.width,p=l.scrollLeft,m=l.instanceProps;if(n>0){var f=n-1,_=s<0?f:Math.min(f,s),S=m.rowSizeAndPositionManager.getTotalSize(),b=m.scrollbarSizeMeasured&&S>i?m.scrollbarSize:0;return m.columnSizeAndPositionManager.getUpdatedOffsetForIndex({align:a,containerSize:c-b,currentOffset:p,targetIndex:_})}return 0}},{key:"_getScrollLeftForScrollToColumnStateUpdate",value:function(e,l){var n=l.scrollLeft,i=r._getCalculatedScrollLeft(e,l);return typeof i=="number"&&i>=0&&n!==i?r._getScrollToPositionStateUpdate({prevState:l,scrollLeft:i,scrollTop:-1}):{}}},{key:"_getCalculatedScrollTop",value:function(e,l){var n=e.height,i=e.rowCount,a=e.scrollToAlignment,s=e.scrollToRow,c=e.width,p=l.scrollTop,m=l.instanceProps;if(i>0){var f=i-1,_=s<0?f:Math.min(f,s),S=m.columnSizeAndPositionManager.getTotalSize(),b=m.scrollbarSizeMeasured&&S>c?m.scrollbarSize:0;return m.rowSizeAndPositionManager.getUpdatedOffsetForIndex({align:a,containerSize:n-b,currentOffset:p,targetIndex:_})}return 0}},{key:"_getScrollTopForScrollToRowStateUpdate",value:function(e,l){var n=l.scrollTop,i=r._getCalculatedScrollTop(e,l);return typeof i=="number"&&i>=0&&n!==i?r._getScrollToPositionStateUpdate({prevState:l,scrollLeft:-1,scrollTop:i}):{}}}]),r}(nr.PureComponent),(0,q.default)(Fi,"propTypes",h.NODE_ENV==="production"?null:{"aria-label":x.default.string.isRequired,"aria-readonly":x.default.bool,autoContainerWidth:x.default.bool.isRequired,autoHeight:x.default.bool.isRequired,autoWidth:x.default.bool.isRequired,cellRenderer:function(){return(typeof L.bpfrpt_proptype_CellRenderer=="function"?L.bpfrpt_proptype_CellRenderer.isRequired?L.bpfrpt_proptype_CellRenderer.isRequired:L.bpfrpt_proptype_CellRenderer:x.default.shape(L.bpfrpt_proptype_CellRenderer).isRequired).apply(this,arguments)},cellRangeRenderer:function(){return(typeof L.bpfrpt_proptype_CellRangeRenderer=="function"?L.bpfrpt_proptype_CellRangeRenderer.isRequired?L.bpfrpt_proptype_CellRangeRenderer.isRequired:L.bpfrpt_proptype_CellRangeRenderer:x.default.shape(L.bpfrpt_proptype_CellRangeRenderer).isRequired).apply(this,arguments)},className:x.default.string,columnCount:x.default.number.isRequired,columnWidth:function(){return(typeof L.bpfrpt_proptype_CellSize=="function"?L.bpfrpt_proptype_CellSize.isRequired?L.bpfrpt_proptype_CellSize.isRequired:L.bpfrpt_proptype_CellSize:x.default.shape(L.bpfrpt_proptype_CellSize).isRequired).apply(this,arguments)},containerProps:x.default.object,containerRole:x.default.string.isRequired,containerStyle:x.default.object.isRequired,deferredMeasurementCache:x.default.object,estimatedColumnSize:x.default.number.isRequired,estimatedRowSize:x.default.number.isRequired,getScrollbarSize:x.default.func.isRequired,height:x.default.number.isRequired,id:x.default.string,isScrolling:x.default.bool,isScrollingOptOut:x.default.bool.isRequired,noContentRenderer:function(){return(typeof L.bpfrpt_proptype_NoContentRenderer=="function"?L.bpfrpt_proptype_NoContentRenderer.isRequired?L.bpfrpt_proptype_NoContentRenderer.isRequired:L.bpfrpt_proptype_NoContentRenderer:x.default.shape(L.bpfrpt_proptype_NoContentRenderer).isRequired).apply(this,arguments)},onScroll:x.default.func.isRequired,onScrollbarPresenceChange:x.default.func.isRequired,onSectionRendered:x.default.func.isRequired,overscanColumnCount:x.default.number.isRequired,overscanIndicesGetter:function(){return(typeof L.bpfrpt_proptype_OverscanIndicesGetter=="function"?L.bpfrpt_proptype_OverscanIndicesGetter.isRequired?L.bpfrpt_proptype_OverscanIndicesGetter.isRequired:L.bpfrpt_proptype_OverscanIndicesGetter:x.default.shape(L.bpfrpt_proptype_OverscanIndicesGetter).isRequired).apply(this,arguments)},overscanRowCount:x.default.number.isRequired,role:x.default.string.isRequired,rowHeight:function(){return(typeof L.bpfrpt_proptype_CellSize=="function"?L.bpfrpt_proptype_CellSize.isRequired?L.bpfrpt_proptype_CellSize.isRequired:L.bpfrpt_proptype_CellSize:x.default.shape(L.bpfrpt_proptype_CellSize).isRequired).apply(this,arguments)},rowCount:x.default.number.isRequired,scrollingResetTimeInterval:x.default.number.isRequired,scrollLeft:x.default.number,scrollToAlignment:function(){return(typeof L.bpfrpt_proptype_Alignment=="function"?L.bpfrpt_proptype_Alignment.isRequired?L.bpfrpt_proptype_Alignment.isRequired:L.bpfrpt_proptype_Alignment:x.default.shape(L.bpfrpt_proptype_Alignment).isRequired).apply(this,arguments)},scrollToColumn:x.default.number.isRequired,scrollTop:x.default.number,scrollToRow:x.default.number.isRequired,style:x.default.object.isRequired,tabIndex:x.default.number,width:x.default.number.isRequired}),Bi);(0,q.default)(cr,"defaultProps",{"aria-label":"grid","aria-readonly":!0,autoContainerWidth:!1,autoHeight:!1,autoWidth:!1,cellRangeRenderer:Oa.default,containerRole:"rowgroup",containerStyle:{},estimatedColumnSize:100,estimatedRowSize:30,getScrollbarSize:za.default,noContentRenderer:Pa,onScroll:function(){},onScrollbarPresenceChange:function(){},onSectionRendered:function(){},overscanColumnCount:0,overscanIndicesGetter:pe.default,overscanRowCount:10,role:"grid",scrollingResetTimeInterval:Ui,scrollToAlignment:"auto",scrollToColumn:-1,scrollToRow:-1,style:{},tabIndex:0,isScrollingOptOut:!1});(0,xa.polyfill)(cr);var Aa=cr;ut.default=Aa});var Ki=D(Ce=>{"use strict";u();d();Object.defineProperty(Ce,"__esModule",{value:!0});Ce.default=qa;Ce.SCROLL_DIRECTION_VERTICAL=Ce.SCROLL_DIRECTION_HORIZONTAL=Ce.SCROLL_DIRECTION_FORWARD=Ce.SCROLL_DIRECTION_BACKWARD=void 0;var Gu=we(),Ma=-1;Ce.SCROLL_DIRECTION_BACKWARD=Ma;var Zi=1;Ce.SCROLL_DIRECTION_FORWARD=Zi;var La="horizontal";Ce.SCROLL_DIRECTION_HORIZONTAL=La;var Ea="vertical";Ce.SCROLL_DIRECTION_VERTICAL=Ea;function qa(o){var r=o.cellCount,t=o.overscanCellsCount,e=o.scrollDirection,l=o.startIndex,n=o.stopIndex;return t=Math.max(1,t),e===Zi?{overscanStartIndex:Math.max(0,l-1),overscanStopIndex:Math.min(r-1,n+t)}:{overscanStartIndex:Math.max(0,l-t),overscanStopIndex:Math.min(r-1,n+1)}}});var Qi=D(ie=>{"use strict";u();d();var uo=me();Object.defineProperty(ie,"__esModule",{value:!0});Object.defineProperty(ie,"default",{enumerable:!0,get:function(){return Ji.default}});Object.defineProperty(ie,"Grid",{enumerable:!0,get:function(){return Ji.default}});Object.defineProperty(ie,"accessibilityOverscanIndicesGetter",{enumerable:!0,get:function(){return Da.default}});Object.defineProperty(ie,"defaultCellRangeRenderer",{enumerable:!0,get:function(){return ka.default}});Object.defineProperty(ie,"defaultOverscanIndicesGetter",{enumerable:!0,get:function(){return Na.default}});Object.defineProperty(ie,"bpfrpt_proptype_NoContentRenderer",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_NoContentRenderer}});Object.defineProperty(ie,"bpfrpt_proptype_Alignment",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_Alignment}});Object.defineProperty(ie,"bpfrpt_proptype_CellPosition",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_CellPosition}});Object.defineProperty(ie,"bpfrpt_proptype_CellSize",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_CellSize}});Object.defineProperty(ie,"bpfrpt_proptype_OverscanIndicesGetter",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_OverscanIndicesGetter}});Object.defineProperty(ie,"bpfrpt_proptype_RenderedSection",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_RenderedSection}});Object.defineProperty(ie,"bpfrpt_proptype_CellRendererParams",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_CellRendererParams}});Object.defineProperty(ie,"bpfrpt_proptype_Scroll",{enumerable:!0,get:function(){return Le.bpfrpt_proptype_Scroll}});var Ji=uo($i()),Da=uo(Ki()),ka=uo(rr()),Na=uo(er()),Le=we()});var ur=D(_e=>{"use strict";u();d();var Wa=me(),Ha=Ve();Object.defineProperty(_e,"__esModule",{value:!0});_e.bpfrpt_proptype_Scroll=_e.bpfrpt_proptype_RenderedRows=_e.bpfrpt_proptype_RowRenderer=_e.bpfrpt_proptype_RowRendererParams=void 0;var Zu=Ha(B()),ce=Wa(it()),Ga=h.NODE_ENV==="production"?null:{index:ce.default.number.isRequired,isScrolling:ce.default.bool.isRequired,isVisible:ce.default.bool.isRequired,key:ce.default.string.isRequired,parent:ce.default.object.isRequired,style:ce.default.object.isRequired};_e.bpfrpt_proptype_RowRendererParams=Ga;var Fa=h.NODE_ENV==="production"?null:ce.default.func;_e.bpfrpt_proptype_RowRenderer=Fa;var Ba=h.NODE_ENV==="production"?null:{overscanStartIndex:ce.default.number.isRequired,overscanStopIndex:ce.default.number.isRequired,startIndex:ce.default.number.isRequired,stopIndex:ce.default.number.isRequired};_e.bpfrpt_proptype_RenderedRows=Ba;var ja=h.NODE_ENV==="production"?null:{clientHeight:ce.default.number.isRequired,scrollHeight:ce.default.number.isRequired,scrollTop:ce.default.number.isRequired};_e.bpfrpt_proptype_Scroll=ja});var rl=D(po=>{"use strict";u();d();var be=me(),tl=Ve();Object.defineProperty(po,"__esModule",{value:!0});po.default=void 0;var Va=be(Jo()),Ua=be(Ct()),$a=be(St()),Za=be(Do()),Ka=be(ko()),zt=be(qo()),Ja=be(Eo()),$e=be(at()),V=tl(Qi()),Xi=tl(B()),Qa=be(Qr()),xt=ur(),Q=be(it()),Yi,el,ol=(el=Yi=function(o){(0,Ja.default)(r,o);function r(){var t,e;(0,Ua.default)(this,r);for(var l=arguments.length,n=new Array(l),i=0;i<l;i++)n[i]=arguments[i];return e=(0,Za.default)(this,(t=(0,Ka.default)(r)).call.apply(t,[this].concat(n))),(0,$e.default)((0,zt.default)(e),"Grid",void 0),(0,$e.default)((0,zt.default)(e),"_cellRenderer",function(a){var s=a.parent,c=a.rowIndex,p=a.style,m=a.isScrolling,f=a.isVisible,_=a.key,S=e.props.rowRenderer,b=Object.getOwnPropertyDescriptor(p,"width");return b&&b.writable&&(p.width="100%"),S({index:c,style:p,isScrolling:m,isVisible:f,key:_,parent:s})}),(0,$e.default)((0,zt.default)(e),"_setRef",function(a){e.Grid=a}),(0,$e.default)((0,zt.default)(e),"_onScroll",function(a){var s=a.clientHeight,c=a.scrollHeight,p=a.scrollTop,m=e.props.onScroll;m({clientHeight:s,scrollHeight:c,scrollTop:p})}),(0,$e.default)((0,zt.default)(e),"_onSectionRendered",function(a){var s=a.rowOverscanStartIndex,c=a.rowOverscanStopIndex,p=a.rowStartIndex,m=a.rowStopIndex,f=e.props.onRowsRendered;f({overscanStartIndex:s,overscanStopIndex:c,startIndex:p,stopIndex:m})}),e}return(0,$a.default)(r,[{key:"forceUpdateGrid",value:function(){this.Grid&&this.Grid.forceUpdate()}},{key:"getOffsetForRow",value:function(e){var l=e.alignment,n=e.index;if(this.Grid){var i=this.Grid.getOffsetForCell({alignment:l,rowIndex:n,columnIndex:0}),a=i.scrollTop;return a}return 0}},{key:"invalidateCellSizeAfterRender",value:function(e){var l=e.columnIndex,n=e.rowIndex;this.Grid&&this.Grid.invalidateCellSizeAfterRender({rowIndex:n,columnIndex:l})}},{key:"measureAllRows",value:function(){this.Grid&&this.Grid.measureAllCells()}},{key:"recomputeGridSize",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},l=e.columnIndex,n=l===void 0?0:l,i=e.rowIndex,a=i===void 0?0:i;this.Grid&&this.Grid.recomputeGridSize({rowIndex:a,columnIndex:n})}},{key:"recomputeRowHeights",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.Grid&&this.Grid.recomputeGridSize({rowIndex:e,columnIndex:0})}},{key:"scrollToPosition",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.Grid&&this.Grid.scrollToPosition({scrollTop:e})}},{key:"scrollToRow",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:0;this.Grid&&this.Grid.scrollToCell({columnIndex:0,rowIndex:e})}},{key:"render",value:function(){var e=this.props,l=e.className,n=e.noRowsRenderer,i=e.scrollToIndex,a=e.width,s=(0,Qa.default)("ReactVirtualized__List",l);return Xi.createElement(V.default,(0,Va.default)({},this.props,{autoContainerWidth:!0,cellRenderer:this._cellRenderer,className:s,columnWidth:a,columnCount:1,noContentRenderer:n,onScroll:this._onScroll,onSectionRendered:this._onSectionRendered,ref:this._setRef,scrollToRow:i}))}}]),r}(Xi.PureComponent),(0,$e.default)(Yi,"propTypes",h.NODE_ENV==="production"?null:{"aria-label":Q.default.string,autoHeight:Q.default.bool.isRequired,className:Q.default.string,estimatedRowSize:Q.default.number.isRequired,height:Q.default.number.isRequired,noRowsRenderer:function(){return(typeof V.bpfrpt_proptype_NoContentRenderer=="function"?V.bpfrpt_proptype_NoContentRenderer.isRequired?V.bpfrpt_proptype_NoContentRenderer.isRequired:V.bpfrpt_proptype_NoContentRenderer:Q.default.shape(V.bpfrpt_proptype_NoContentRenderer).isRequired).apply(this,arguments)},onRowsRendered:Q.default.func.isRequired,onScroll:Q.default.func.isRequired,overscanIndicesGetter:function(){return(typeof V.bpfrpt_proptype_OverscanIndicesGetter=="function"?V.bpfrpt_proptype_OverscanIndicesGetter.isRequired?V.bpfrpt_proptype_OverscanIndicesGetter.isRequired:V.bpfrpt_proptype_OverscanIndicesGetter:Q.default.shape(V.bpfrpt_proptype_OverscanIndicesGetter).isRequired).apply(this,arguments)},overscanRowCount:Q.default.number.isRequired,rowHeight:function(){return(typeof V.bpfrpt_proptype_CellSize=="function"?V.bpfrpt_proptype_CellSize.isRequired?V.bpfrpt_proptype_CellSize.isRequired:V.bpfrpt_proptype_CellSize:Q.default.shape(V.bpfrpt_proptype_CellSize).isRequired).apply(this,arguments)},rowRenderer:function(){return(typeof xt.bpfrpt_proptype_RowRenderer=="function"?xt.bpfrpt_proptype_RowRenderer.isRequired?xt.bpfrpt_proptype_RowRenderer.isRequired:xt.bpfrpt_proptype_RowRenderer:Q.default.shape(xt.bpfrpt_proptype_RowRenderer).isRequired).apply(this,arguments)},rowCount:Q.default.number.isRequired,scrollToAlignment:function(){return(typeof V.bpfrpt_proptype_Alignment=="function"?V.bpfrpt_proptype_Alignment.isRequired?V.bpfrpt_proptype_Alignment.isRequired:V.bpfrpt_proptype_Alignment:Q.default.shape(V.bpfrpt_proptype_Alignment).isRequired).apply(this,arguments)},scrollToIndex:Q.default.number.isRequired,scrollTop:Q.default.number,style:Q.default.object.isRequired,tabIndex:Q.default.number,width:Q.default.number.isRequired}),el);po.default=ol;(0,$e.default)(ol,"defaultProps",{autoHeight:!1,estimatedRowSize:30,onScroll:function(){},noRowsRenderer:function(){return null},onRowsRendered:function(){},overscanIndicesGetter:V.accessibilityOverscanIndicesGetter,overscanRowCount:10,scrollToAlignment:"auto",scrollToIndex:-1,style:{}})});var ll=D(Pt=>{"use strict";u();d();var Xa=me();Object.defineProperty(Pt,"__esModule",{value:!0});Object.defineProperty(Pt,"default",{enumerable:!0,get:function(){return il.default}});Object.defineProperty(Pt,"List",{enumerable:!0,get:function(){return il.default}});Object.defineProperty(Pt,"bpfrpt_proptype_RowRendererParams",{enumerable:!0,get:function(){return Ya.bpfrpt_proptype_RowRendererParams}});var il=Xa(rl()),Ya=ur()});var sl=D(fo=>{"use strict";u();d();Object.defineProperty(fo,"__esModule",{value:!0});fo.registerScrollListener=os;fo.unregisterScrollListener=rs;var dr=lr(),id=pr(),Ee=[],At=null,Ze=null;function nl(){Ze&&(Ze=null,document.body&&At!=null&&(document.body.style.pointerEvents=At),At=null)}function es(){nl(),Ee.forEach(function(o){return o.__resetIsScrolling()})}function ts(){Ze&&(0,dr.cancelAnimationTimeout)(Ze);var o=0;Ee.forEach(function(r){o=Math.max(o,r.props.scrollingResetTimeInterval)}),Ze=(0,dr.requestAnimationTimeout)(es,o)}function al(o){o.currentTarget===self&&At==null&&document.body&&(At=document.body.style.pointerEvents,document.body.style.pointerEvents="none"),ts(),Ee.forEach(function(r){r.props.scrollElement===o.currentTarget&&r.__handleWindowScrollEvent()})}function os(o,r){Ee.some(function(t){return t.props.scrollElement===r})||r.addEventListener("scroll",al),Ee.push(o)}function rs(o,r){Ee=Ee.filter(function(t){return t!==o}),Ee.length||(r.removeEventListener("scroll",al),Ze&&((0,dr.cancelAnimationTimeout)(Ze),nl()))}});var ul=D(Lt=>{"use strict";u();d();Object.defineProperty(Lt,"__esModule",{value:!0});Lt.getDimensions=is;Lt.getPositionOffset=ls;Lt.getScrollOffset=cl;var fr=function(r){return r===self},Mt=function(r){return r.getBoundingClientRect()};function is(o,r){if(o)if(fr(o)){var t=self,e=t.innerHeight,l=t.innerWidth;return{height:typeof e=="number"?e:0,width:typeof l=="number"?l:0}}else return Mt(o);else return{height:r.serverHeight,width:r.serverWidth}}function ls(o,r){if(fr(r)&&document.documentElement){var t=document.documentElement,e=Mt(o),l=Mt(t);return{top:e.top-l.top,left:e.left-l.left}}else{var n=cl(r),i=Mt(o),a=Mt(r);return{top:i.top+n.top-a.top,left:i.left+n.left-a.left}}}function cl(o){return fr(o)&&document.documentElement?{top:"scrollY"in self?self.scrollY:document.documentElement.scrollTop,left:"scrollX"in self?self.scrollX:document.documentElement.scrollLeft}:{top:o.scrollTop,left:o.scrollLeft}}});var dl=D(mr=>{"use strict";u();d();Object.defineProperty(mr,"__esModule",{value:!0});mr.default=ns;function ns(o,r){var t;typeof r<"u"?t=r:t=self;var e=typeof t.document<"u"&&t.document.attachEvent;if(!e){var l=function(){var R=t.requestAnimationFrame||t.mozRequestAnimationFrame||t.webkitRequestAnimationFrame||function(g){return t.setTimeout(g,20)};return function(g){return R(g)}}(),n=function(){var R=t.cancelAnimationFrame||t.mozCancelAnimationFrame||t.webkitCancelAnimationFrame||t.clearTimeout;return function(g){return R(g)}}(),i=function(g){var A=g.__resizeTriggers__,z=A.firstElementChild,M=A.lastElementChild,K=z.firstElementChild;M.scrollLeft=M.scrollWidth,M.scrollTop=M.scrollHeight,K.style.width=z.offsetWidth+1+"px",K.style.height=z.offsetHeight+1+"px",z.scrollLeft=z.scrollWidth,z.scrollTop=z.scrollHeight},a=function(g){return g.offsetWidth!=g.__resizeLast__.width||g.offsetHeight!=g.__resizeLast__.height},s=function(g){if(!(g.target.className&&typeof g.target.className.indexOf=="function"&&g.target.className.indexOf("contract-trigger")<0&&g.target.className.indexOf("expand-trigger")<0)){var A=this;i(this),this.__resizeRAF__&&n(this.__resizeRAF__),this.__resizeRAF__=l(function(){a(A)&&(A.__resizeLast__.width=A.offsetWidth,A.__resizeLast__.height=A.offsetHeight,A.__resizeListeners__.forEach(function(z){z.call(A,g)}))})}},c=!1,p="",m="animationstart",f="Webkit Moz O ms".split(" "),_="webkitAnimationStart animationstart oAnimationStart MSAnimationStart".split(" "),S="";{var b=t.document.createElement("fakeelement");if(b.style.animationName!==void 0&&(c=!0),c===!1){for(var T=0;T<f.length;T++)if(b.style[f[T]+"AnimationName"]!==void 0){S=f[T],p="-"+S.toLowerCase()+"-",m=_[T],c=!0;break}}}var w="resizeanim",I="@"+p+"keyframes "+w+" { from { opacity: 0; } to { opacity: 0; } } ",E=p+"animation: 1ms "+w+"; "}var j=function(g){if(!g.getElementById("detectElementResize")){var A=(I||"")+".resize-triggers { "+(E||"")+'visibility: hidden; opacity: 0; } .resize-triggers, .resize-triggers > div, .contract-trigger:before { content: " "; display: block; position: absolute; top: 0; left: 0; height: 100%; width: 100%; overflow: hidden; z-index: -1; } .resize-triggers > div { background: #eee; overflow: auto; } .contract-trigger:before { width: 200%; height: 200%; }',z=g.head||g.getElementsByTagName("head")[0],M=g.createElement("style");M.id="detectElementResize",M.type="text/css",o!=null&&M.setAttribute("nonce",o),M.styleSheet?M.styleSheet.cssText=A:M.appendChild(g.createTextNode(A)),z.appendChild(M)}},W=function(g,A){if(e)g.attachEvent("onresize",A);else{if(!g.__resizeTriggers__){var z=g.ownerDocument,M=t.getComputedStyle(g);M&&M.position=="static"&&(g.style.position="relative"),j(z),g.__resizeLast__={},g.__resizeListeners__=[],(g.__resizeTriggers__=z.createElement("div")).className="resize-triggers";var K='<div class="expand-trigger"><div></div></div><div class="contract-trigger"></div>';if(self.trustedTypes){var J=trustedTypes.createPolicy("react-virtualized-auto-sizer",{createHTML:function(){return K}});g.__resizeTriggers__.innerHTML=J.createHTML("")}else g.__resizeTriggers__.innerHTML=K;g.appendChild(g.__resizeTriggers__),i(g),g.addEventListener("scroll",s,!0),m&&(g.__resizeTriggers__.__animationListener__=function(fe){fe.animationName==w&&i(g)},g.__resizeTriggers__.addEventListener(m,g.__resizeTriggers__.__animationListener__))}g.__resizeListeners__.push(A)}},N=function(g,A){if(e)g.detachEvent("onresize",A);else if(g.__resizeListeners__.splice(g.__resizeListeners__.indexOf(A),1),!g.__resizeListeners__.length){g.removeEventListener("scroll",s,!0),g.__resizeTriggers__.__animationListener__&&(g.__resizeTriggers__.removeEventListener(m,g.__resizeTriggers__.__animationListener__),g.__resizeTriggers__.__animationListener__=null);try{g.__resizeTriggers__=!g.removeChild(g.__resizeTriggers__)}catch{}}};return{addResizeListener:W,removeResizeListener:N}}});var pr=D(dt=>{"use strict";u();d();var Ie=me(),hl=Ve();Object.defineProperty(dt,"__esModule",{value:!0});dt.default=dt.IS_SCROLLING_TIMEOUT=void 0;var as=Ie(Ct()),ss=Ie(St()),cs=Ie(Do()),us=Ie(ko()),ue=Ie(qo()),ds=Ie(Eo()),ee=Ie(at()),ps=hl(B()),fs=hl(tn()),mo=sl(),ho=ul(),ms=Ie(dl()),ve=Ie(it()),pl,fl;function ml(o,r){var t=Object.keys(o);if(Object.getOwnPropertySymbols){var e=Object.getOwnPropertySymbols(o);r&&(e=e.filter(function(l){return Object.getOwnPropertyDescriptor(o,l).enumerable})),t.push.apply(t,e)}return t}function hs(o){for(var r=1;r<arguments.length;r++){var t=arguments[r]!=null?arguments[r]:{};r%2?ml(t,!0).forEach(function(e){(0,ee.default)(o,e,t[e])}):Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(t)):ml(t).forEach(function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(t,e))})}return o}var gl=150;dt.IS_SCROLLING_TIMEOUT=gl;var Cl=function(){return typeof self<"u"?self:void 0},Sl=(fl=pl=function(o){(0,ds.default)(r,o);function r(){var t,e;(0,as.default)(this,r);for(var l=arguments.length,n=new Array(l),i=0;i<l;i++)n[i]=arguments[i];return e=(0,cs.default)(this,(t=(0,us.default)(r)).call.apply(t,[this].concat(n))),(0,ee.default)((0,ue.default)(e),"_window",Cl()),(0,ee.default)((0,ue.default)(e),"_isMounted",!1),(0,ee.default)((0,ue.default)(e),"_positionFromTop",0),(0,ee.default)((0,ue.default)(e),"_positionFromLeft",0),(0,ee.default)((0,ue.default)(e),"_detectElementResize",void 0),(0,ee.default)((0,ue.default)(e),"_child",void 0),(0,ee.default)((0,ue.default)(e),"state",hs({},(0,ho.getDimensions)(e.props.scrollElement,e.props),{isScrolling:!1,scrollLeft:0,scrollTop:0})),(0,ee.default)((0,ue.default)(e),"_registerChild",function(a){a&&!(a instanceof Element)&&console.warn("WindowScroller registerChild expects to be passed Element or null"),e._child=a,e.updatePosition()}),(0,ee.default)((0,ue.default)(e),"_onChildScroll",function(a){var s=a.scrollTop;if(e.state.scrollTop!==s){var c=e.props.scrollElement;c&&(typeof c.scrollTo=="function"?c.scrollTo(0,s+e._positionFromTop):c.scrollTop=s+e._positionFromTop)}}),(0,ee.default)((0,ue.default)(e),"_registerResizeListener",function(a){a===self?self.addEventListener("resize",e._onResize,!1):e._detectElementResize.addResizeListener(a,e._onResize)}),(0,ee.default)((0,ue.default)(e),"_unregisterResizeListener",function(a){a===self?self.removeEventListener("resize",e._onResize,!1):a&&e._detectElementResize.removeResizeListener(a,e._onResize)}),(0,ee.default)((0,ue.default)(e),"_onResize",function(){e.updatePosition()}),(0,ee.default)((0,ue.default)(e),"__handleWindowScrollEvent",function(){if(e._isMounted){var a=e.props.onScroll,s=e.props.scrollElement;if(s){var c=(0,ho.getScrollOffset)(s),p=Math.max(0,c.left-e._positionFromLeft),m=Math.max(0,c.top-e._positionFromTop);e.setState({isScrolling:!0,scrollLeft:p,scrollTop:m}),a({scrollLeft:p,scrollTop:m})}}}),(0,ee.default)((0,ue.default)(e),"__resetIsScrolling",function(){e.setState({isScrolling:!1})}),e}return(0,ss.default)(r,[{key:"updatePosition",value:function(){var e=arguments.length>0&&arguments[0]!==void 0?arguments[0]:this.props.scrollElement,l=this.props.onResize,n=this.state,i=n.height,a=n.width,s=this._child||fs.findDOMNode(this);if(s instanceof Element&&e){var c=(0,ho.getPositionOffset)(s,e);this._positionFromTop=c.top,this._positionFromLeft=c.left}var p=(0,ho.getDimensions)(e,this.props);(i!==p.height||a!==p.width)&&(this.setState({height:p.height,width:p.width}),l({height:p.height,width:p.width}))}},{key:"componentDidMount",value:function(){var e=this.props.scrollElement;this._detectElementResize=(0,ms.default)(),this.updatePosition(e),e&&((0,mo.registerScrollListener)(this,e),this._registerResizeListener(e)),this._isMounted=!0}},{key:"componentDidUpdate",value:function(e,l){var n=this.props.scrollElement,i=e.scrollElement;i!==n&&i!=null&&n!=null&&(this.updatePosition(n),(0,mo.unregisterScrollListener)(this,i),(0,mo.registerScrollListener)(this,n),this._unregisterResizeListener(i),this._registerResizeListener(n))}},{key:"componentWillUnmount",value:function(){var e=this.props.scrollElement;e&&((0,mo.unregisterScrollListener)(this,e),this._unregisterResizeListener(e)),this._isMounted=!1}},{key:"render",value:function(){var e=this.props.children,l=this.state,n=l.isScrolling,i=l.scrollTop,a=l.scrollLeft,s=l.height,c=l.width;return e({onChildScroll:this._onChildScroll,registerChild:this._registerChild,height:s,isScrolling:n,scrollLeft:a,scrollTop:i,width:c})}}]),r}(ps.PureComponent),(0,ee.default)(pl,"propTypes",h.NODE_ENV==="production"?null:{children:ve.default.func.isRequired,onResize:ve.default.func.isRequired,onScroll:ve.default.func.isRequired,scrollElement:ve.default.oneOfType([ve.default.any,function(){return(typeof Element=="function"?ve.default.instanceOf(Element):ve.default.any).apply(this,arguments)}]),scrollingResetTimeInterval:ve.default.number.isRequired,serverHeight:ve.default.number.isRequired,serverWidth:ve.default.number.isRequired}),fl);dt.default=Sl;(0,ee.default)(Sl,"defaultProps",{onResize:function(){},onScroll:function(){},scrollingResetTimeInterval:gl,scrollElement:Cl(),serverHeight:0,serverWidth:0})});var gr=D(pt=>{"use strict";u();d();var gs=Ve();Object.defineProperty(pt,"__esModule",{value:!0});Object.defineProperty(pt,"WindowScroller",{enumerable:!0,get:function(){return hr.default}});Object.defineProperty(pt,"IS_SCROLLING_TIMEOUT",{enumerable:!0,get:function(){return hr.IS_SCROLLING_TIMEOUT}});pt.default=void 0;var hr=gs(pr()),Cs=hr.default;pt.default=Cs});u();d();var O=U(B());u();d();var F=U(B()),_l=U(wi()),bl=U(ll()),vl=U(gr());u();d();var _s=16*2,bs=y.div`
  width: 100%;
  padding-bottom: ${5}px; // phantom adds 15px padding
`,Cr=y.div`
  display: flex;
  gap: ${10}px;
`,vs=y.div`
  align-self: center;
`;function yl(o){return typeof o.title=="string"}var ys=o=>{let{style:r,collapsedSections:t,onToggleCollapse:e,index:l,rows:n,renderCard:i,renderTitle:a}=o,{t:s}=$(),c=n[l];if(!c)return null;let p=c.sectionId,m=t[p],f=`row-${l}`;return p==="trimmed"?F.default.createElement(Cr,{key:f,style:r},F.default.createElement(vs,null,F.default.createElement(Xt,{color:"#FFDC62",title:s("collectiblesTrimmed"),titleMaxLines:3}))):yl(c)?F.default.createElement(Cr,{key:f,style:r,onClick:()=>e(p)},a&&a(c.title,m)):m?F.default.createElement(F.default.Fragment,null):F.default.createElement(Cr,{key:f,style:r},c.row.map((_,S)=>_?F.default.createElement(F.default.Fragment,{key:`card-${S}`},i(_)):null))},Ts=o=>{let{sections:r,renderCard:t,renderTitle:e,defaultRowHeight:l}=o,[n,i]=(0,F.useState)(()=>r.reduce((S,b)=>b.collapsible?{...S,[b.id]:b.initialCollapsed}:S,{})),a=io({itemSize:159,margin:10,horizontalPadding:_s}),s=(0,F.useMemo)(()=>{let S=[];for(let b of r){if(b.title&&S.push({sectionId:b.id,title:b.title}),n[b.id])continue;let w=wr(b.data,a);for(let I of w)S.push({sectionId:b.id,row:I})}return S},[r,n,a]),c=(0,F.useCallback)(S=>{i({...n,[S]:!n[S]})},[n,i]),p=(0,F.useCallback)(({index:S})=>{let b=s[S];return b.sectionId==="trimmed"?120:yl(b)?52:l},[l,s]),m=(0,F.useRef)(document.getElementById("tab-content")),f=(0,F.useMemo)(()=>(a-1)*10+a*159,[a]),_={width:f,margin:"0 auto"};return F.default.createElement(bs,null,F.default.createElement(vl.default,{scrollElement:m.current??void 0},({height:S=0,isScrolling:b,onChildScroll:T,scrollTop:w})=>F.default.createElement(bl.default,{style:_,autoHeight:!0,autoWidth:!0,height:S,width:f,isScrolling:b,onScroll:T,scrollTop:w,rowCount:s.length,overscanRowCount:4,rowHeight:p,rowRenderer:I=>F.default.createElement(ys,{...I,renderCard:t,renderTitle:e,rows:s,onToggleCollapse:c,collapsedSections:n})})))},Co=o=>{let[r]=(0,_l.default)(0);return r()?F.default.createElement(Ts,{...o}):null};u();d();var So=U(B());var Rs=y.div`
  display: grid;
  gap: ${10}px;
  padding-bottom: 15px;
`,Tl=So.default.memo(()=>{let r=`repeat(${io({itemSize:159,margin:0,horizontalPadding:0})}, ${159}px)`;return So.default.createElement(Rs,{style:{gridTemplateColumns:r}},[...Array(8).keys()].map(t=>So.default.createElement(Ci,{key:t})))});u();d();var Z=U(B());u();d();var Ke=U(B());var ws=y.div`
  position: absolute;
  left: 4px;
  right: 4px;
  ${({placement:o})=>{switch(o){case"bottom-left":return`
          bottom: 4px;
        `;case"top-left":return`
          top: 4px;
        `}}}
`,Is=y.div`
  background: rgba(24, 24, 24, 0.85);
  backdrop-filter: blur(15px);
  max-width: 100%;
  border-radius: 6px;
  display: inline-flex;
  justify-content: center;
  pointer-events: none;
  ${({placement:o})=>{switch(o){case"bottom-left":return`
          padding: 6px 10px;
        `;case"top-left":return`
          padding: 2px 6px;
        `}}}
`,Os=y.div`
  flex: 1;
  margin-left: 4px;
`,Rl=y(Se).attrs({lineHeight:17,weight:600,noWrap:!0})``,Sr=Ke.default.memo(o=>{let{name:r,count:t,placement:e,textSize:l,icon:n,testId:i}=o;return Ke.default.createElement(ws,{placement:e,"data-testid":i},Ke.default.createElement(Is,{placement:e},n,Ke.default.createElement(Rl,{size:l==="small"?12:14,noWrap:!0,textTransform:"capitalize"},r),t?Ke.default.createElement(Os,null,Ke.default.createElement(Rl,{opacity:.5,size:14},to(t))):null))});var zs=y.div`
  width: 100%;
  height: 100%;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
  cursor: pointer;
`,wl=y.div`
  margin-right: 2px;
`,xs=y.div`
  flex-direction: row;
  display: flex;
  align-items: center;
  margin-left: -4px;
`,_o=Z.default.memo(o=>{let{image:r,chainData:t,mediaType:e,name:l,nrOfItems:n,listedBadge:i,isPinned:a,isHidden:s,isSpam:c,onClick:p}=o,m=c&&s,f=(0,Z.useMemo)(()=>m?Z.default.createElement(je,null,Z.default.createElement(ro,{width:64})):r?Z.default.createElement(vt,{uri:r,showSkeletonBadge:!l}):Lr(t)?Z.default.createElement(Si,{...t.utxoDetails}):Z.default.createElement(je,null,Z.default.createElement(Te,{type:e})),[m,t,r,e,l]);return Z.default.createElement(zs,{onClick:p,role:"button","aria-label":l,"data-testid":`collectible-tile-${l}`},f,l?Z.default.createElement(Sr,{name:l,placement:"bottom-left",count:n,icon:a||s||c?Z.default.createElement(xs,null,c?Z.default.createElement(Zt,{fill:fi,height:16}):s?Z.default.createElement(As,null):Z.default.createElement(Ps,null)):null}):null,i?Z.default.createElement(Sr,{name:i,textSize:"small",placement:"top-left",testId:"listed-badge"}):null)}),Ps=()=>Z.default.createElement(wl,null,Z.default.createElement(li,{width:16,height:16})),As=()=>Z.default.createElement(wl,null,Z.default.createElement(Jt,{width:16,height:16}));u();d();var bo=y.div`
  width: ${159}px;
  height: ${159}px;
  margin-bottom: ${10}px;
`;u();d();var ft=U(B());var Ms=y.button`
  background-color: transparent;
  border: none;
  display: flex;
  flex-direction: row;
  justify-content: center;
  margin: 0px;
  cursor: pointer;
  padding: 8px;
  p {
    font-weight: 500;
  }
  &:hover {
    p {
      color: #ab9ff2 !important;
    }
    svg {
      fill: #ab9ff2;
      path {
        stroke: #ab9ff2;
      }
      circle {
        stroke: #ab9ff2;
      }
    }
  }
`,Ls=y(Y).attrs({size:16,color:"#777777",weight:500,margin:"0 0 0 10px",lineHeight:19,noWrap:!0})``,Il=ft.default.memo(o=>ft.default.createElement(Ms,{onClick:o.onClick},ft.default.createElement(ci,null,ft.default.createElement(ri,null)),ft.default.createElement(Ls,null,o.buttonText)));u();d();var oe=U(B());var Bl=U(gr());u();d();var Ol=o=>new Yr({fixedWidth:!0,defaultHeight:o});u();d();var k=U(B());u();d();var v=U(B());u();d();var De=U(B());var Es=y(de)`
  background-color: #2a2a2a;
  border-radius: 8px;
  padding: 12px;
  gap: 12px;
  cursor: pointer;

  :hover {
    opacity: 1;
    background: #333333;
  }
`,zl=({title:o,subtitle:r,onClick:t})=>De.default.createElement(Es,{onClick:t},De.default.createElement(Pe,{diameter:48,color:"rgba(33, 229, 111, 0.1)"},De.default.createElement(oi,{fill:"#21E56F"})),De.default.createElement(lt,null,De.default.createElement(de,{justify:"space-between",align:"center"},De.default.createElement(Y,{color:"#fffff",size:16,weight:600,textAlign:"left"},o)),De.default.createElement(Y,{color:"#999999",size:14,weight:400,lineHeight:18,textAlign:"left"},r)));u();d();var ke=U(B());var qs=y(de)`
  background-color: ${o=>o.theme.borderLight};
  border-radius: 8px;
  padding: 12px;
  gap: 12px;
  cursor: pointer;

  :hover {
    opacity: 1;
    background: #333333;
  }
`,Ds=y(Se).attrs({size:16,color:jo.white,weight:600})`
  text-align: left;
`,ks=y(Se).attrs({size:14,color:jo.grayLight,weight:400})`
  text-align: left;
  line-height: 18px;
`,xl=({title:o,subtitle:r,iconFill:t="#2a2a2a",onClick:e})=>ke.default.createElement(qs,{onClick:e},ke.default.createElement(Pe,{diameter:48,color:t,alpha:.2},ke.default.createElement(Kt,{width:20,height:20,fill:t})),ke.default.createElement(lt,null,ke.default.createElement(de,{justify:"space-between",align:"center"},ke.default.createElement(Ds,null,o)),ke.default.createElement(ks,null,r)));u();d();var Et=U(B());var Ns=y(Se).attrs({as:"span",weight:500})``,Ws=y(Se).attrs({as:"label",weight:500,color:"#999999"})``,vo=Et.default.memo(({title:o,value:r,sections:t})=>{let e=Number(r);return Et.default.createElement(ui,{sections:t},Et.default.createElement(Ns,null,o),e&&e>1?Et.default.createElement(Ws,null," ",to(e)):null)});u();d();var Je=U(B());var Hs=y.div`
  position: absolute;
  display: flex;
  align-items: center;
  pointer-events: none;
  left: 4px;
  top: 4px;
  padding: 4px 6px;
  margin: 8px;
  background: rgba(24, 24, 24, 0.85);
  backdrop-filter: blur(15px);
  border-radius: 6px;
`,Gs=y.div`
  padding-left: 6px;
  padding-right: 4px;
`,Pl=y(Se).attrs({textAlign:"left",weight:600,noWrap:!0})``,Al=Je.default.memo(o=>{let{title:r,amount:t,symbol:e}=o;return Je.default.createElement(Hs,null,Je.default.createElement(ii,null),Je.default.createElement(Gs,null,Je.default.createElement(Pl,{opacity:.5,size:12,lineHeight:16,textTransform:"capitalize"},r),Je.default.createElement(Pl,{noWrap:!0,lineHeight:20,textTransform:"capitalize"},`${t} ${e}`)))});u();d();var Qe=U(B());u();d();var mt=U(B());var Ml=({children:o,overlay:r,visible:t=!1,onViewToggled:e})=>{let[l,n]=(0,mt.useState)(t),i=(0,mt.useCallback)(()=>{n(!l),e?.()},[l,e]);return mt.default.createElement(Fs,null,l?r(i):o)},Fs=y.div``;var Ll=({children:o,isOverlayVisible:r,onOverlayPressed:t})=>Qe.default.createElement(Ml,{visible:r,overlay:Bs,onViewToggled:t},o),Bs=o=>Qe.default.createElement(js,null,Qe.default.createElement(Vs,null,Qe.default.createElement(Jt,{width:32,height:32,fill:"#999999"}),Qe.default.createElement(Us,null,Wo.t("collectibleDetailSpamOverlayDescription")),Qe.default.createElement($s,{onClick:o},Wo.t("collectibleDetailSpamOverlayReveal")))),js=y.div`
  aspect-ratio: 1;
  justify-content: center;
  border-radius: 8px;
  align-items: center;
  background-color: ${o=>o.theme.backgroundDark};
`,Vs=y.div`
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  padding: 16px;
  overflow: hidden;
`,Us=y(Y).attrs({size:15,weight:400,lineHeight:21,color:"#999999",margin:"12px 0px"})``,$s=y(Y).attrs({size:15,lineHeight:21,weight:500,color:"#ab9ff2",textAlign:"center"})`
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  &:hover {
    color: #7d66d9;
  }
`;u();d();var Oe=U(B());var El=Oe.default.memo(o=>{let{description:r,url:t}=o,{showExternalLinkWarningModal:e}=bt(),l=(0,Oe.useCallback)(()=>{t&&(X.capture("collectibleExternalLinkWarningPrompted",{data:{url:t.toString()}}),e({url:t}))},[e,t]),{t:n}=$();return Oe.default.createElement(Oe.default.Fragment,null,r&&Oe.default.createElement(Y,{weight:500,size:14,color:"#777",textAlign:"left"},n("collectibleDetailDescription")),r&&Oe.default.createElement(Y,{size:14,textAlign:"left",lineHeight:20,wordBreak:"break-word"},r),t&&Oe.default.createElement(si,{theme:"link",onClick:l,paddingY:0,lineHeight:20,fontSize:14,fontWeight:500},t.href))});var Ks=Fo({height:56,width:56,alignItems:"center",justifyContent:"center",marginRight:8,borderRadius:14,padding:16,backgroundColor:"accentPrimaryLight"}),ql=Fo({marginBottom:8});function Js(o,r,t){X.capture("walletShortcutsLinkOpenClick",_i({type:"collectible",hostname:o??"",collectionName:r??""},t)),self.open(t.uri)}var Qs=o=>{let{collectibleId:r}=o,{t}=$(),{data:e}=Ft(),l=e??Gt,{showCollectibleBurnModal:n}=bt(),{handleShowModalVisibility:i,handleHideModalVisibility:a}=yt(),{data:s}=xe(),c=s?.isReadOnly,p=s?.addresses??[],m=p.find(ae=>ae.networkID.startsWith("solana"))?.address,{data:f}=ot({id:r,addresses:p}),{ctaBanner:_,traits:S}=Er(f),b=(0,v.useCallback)(()=>{if(!_)return;let{title:ae,subtitle:tt,buttonText:Ao,uri:Mo,iconFill:Wt}=_.modal;X.capture("collectibleCtaClick",{data:{title:ae}}),i("interstitial",{bodyTitle:ae,bodyDescription:tt,icon:v.default.createElement(Pe,{diameter:72,color:Wt,alpha:.2},v.default.createElement(Kt,{width:36,height:36,fill:Wt})),alignBody:"center",FooterComponent:()=>v.default.createElement(_t,{theme:"primary",onClick:()=>{self.open(Mo,"_blank"),a("interstitial")}},Ao)})},[_,a,i]),{bidSummary:T,isListedOnSolana:w,sellSteps:I}=Hr({collectible:f}),E=(0,v.useMemo)(()=>{let ae=!!I.orderId&&I.steps.length>0;return!w&&T!==void 0&&!c&&ae},[I.orderId,I.steps.length,w,T,c]),j=(0,v.useCallback)((ae,tt)=>{X.capture("collectibleSellCTAClick"),i("instantSell",{collectible:ae,bidSummary:tt})},[i]),{shortcuts:W}=gi(f,l),{data:N}=zr(),R=Vr(f,N),g=Number(f?.balance??""),A=(0,v.useMemo)(()=>{if(!(!f||!m))return hi(f,m)},[f,m]),z=(0,v.useMemo)(()=>f?.listings?.magic_eden,[f?.listings?.magic_eden]),M=f?.collection.marketplaces?.some(ae=>ae.isVerified)??!1,K=!c&&!!A&&!M&&!z,{onReportNotSpam:J,onReportSpam:ne,isSpam:fe,spamStatus:Ne}=Vt({accountId:s?.identifier,collectible:f,toast:ye}),{isPinned:We,onTogglePin:Ro}=$t({collectionId:f?.collection.id,isSpam:fe,accountId:s?.identifier,networkId:f?.chain?.id,toast:ye}),{isHidden:wo,onToggleHide:Ye}=Bt({collectible:f,accountId:s?.identifier,toast:ye}),{mutate:qt}=xr(),He=f?.media,et=He?.type,Dt=Fe(He,et,!0,"large"),kt=(0,v.useCallback)(()=>{s?.identifier&&(X.capture("accountAvatarModified",{data:{type:"image"}}),qt({identifier:s.identifier,icon:{type:"image",imageUrl:Dt}}),ye.success(t("collectibleDetailSetAsAvatarSuccess")))},[t,qt,s?.identifier,Dt]),Io=(0,v.useCallback)(()=>{f&&n(f)},[n,f]),Oo=(0,v.useCallback)(()=>{R&&self.open(R.url,"_blank")},[R]),zo=(0,v.useMemo)(()=>Mr(f?.chainData)&&f?.chainData.standard!=="SemiFungible"&&!!f?.collection.id,[f?.chainData,f?.collection]),Ge=(0,v.useCallback)(()=>{z?.url&&self.open(z?.url)},[z?.url]),ht=Nr(f,{positiveColor:"#21E56F",negativeColor:"#EB3742"}),ze=Ur(f),xo=(0,v.useMemo)(()=>J?()=>void J():void 0,[J]),Po=(0,v.useMemo)(()=>ne?()=>void ne():void 0,[ne]),Nt=(0,v.useMemo)(()=>()=>void Ye(),[Ye]);return{collectible:f,ctaBanner:_,onCtaBannerClick:b,listing:z,bidSummary:T,showOfferButton:E,onClickOffer:j,isValidCollectible:zo,collectibleAsSPLTokenAccount:A,onListingURLClick:Ge,summaryItems:ht,chainSpecificSummarySection:ze,collectibleTraits:S,shortcuts:W,isReadOnlyAccount:c,isHidden:wo,amountToBurn:g,explorerName:R?.name,isBurnable:K,isPinned:We,onBurnToken:Io,onReportNotSpam:xo,onReportSpam:Po,onSetAsAvatar:kt,onToggleHide:Nt,onTogglePin:Ro,onViewOnExplorer:Oo,isSpam:fe,spamStatus:Ne}},Xs=v.default.memo(o=>{let{t:r}=$(),{showEditCollectibleListingModal:t,showCollectibleListingModal:e}=bt(),{handleShowModalVisibility:l,handleHideModalVisibility:n}=yt(),[i]=Pr(se=>[se.setSendCollectible]),a=Go(se=>se.setListPrice),s=Go(se=>se.setListCollectible),{collectible:c,listing:p,isValidCollectible:m,onListingURLClick:f,collectibleAsSPLTokenAccount:_,summaryItems:S,chainSpecificSummarySection:b,collectibleTraits:T,ctaBanner:w,onCtaBannerClick:I,bidSummary:E,showOfferButton:j,onClickOffer:W,shortcuts:N,isReadOnlyAccount:R,amountToBurn:g,explorerName:A,isBurnable:z,isHidden:M,isPinned:K,onBurnToken:J,onReportNotSpam:ne,onReportSpam:fe,onSetAsAvatar:Ne,onToggleHide:We,onTogglePin:Ro,onViewOnExplorer:wo,isSpam:Ye,spamStatus:qt}=o,He=(0,v.useMemo)(()=>Ar(c),[c]),et=(0,v.useMemo)(()=>!R&&!p&&!He,[He,R,p]),Dt=et&&N.length>0,{description:kt,externalUrl:Io,listings:Oo,media:zo}=c,Ge=c?.collection.name,ht=Ir(Io),ze=ht?.hostname,xo=Ye||!kt&&!ht?null:v.default.createElement(El,{description:kt,url:ht}),Po=()=>{let se=document.querySelector("audio"),Lo=document.querySelector("video"),gt=se??Lo;gt?.paused||gt?.pause()},Nt=(0,v.useMemo)(()=>c?.chainData?.compression?.compressed??!1,[c?.chainData]),ae=(0,v.useMemo)(()=>et&&R===!1&&m&&!p&&!!_&&!Nt&&!He,[et,m,p,_,Nt,R,He]),tt=(0,v.useCallback)(()=>{_&&(s({asset:_,listings:null}),e(_))},[s,_,e]),Ao=(0,v.useCallback)(()=>{if(ae)return tt()},[tt,ae]),Mo=(0,v.useCallback)(()=>{X.capture("walletShortcutsModalOpenClick",bi({type:"collectible",collectionName:Ge??"",hostname:ze??""})),l("shortcutsSheet",{type:"collectible",hostname:ze??"",shortcuts:N,collectionName:Ge??""})},[ze,N,Ge,l]),Wt=(0,v.useCallback)(()=>{X.capture("collectibleSpamOverlayDismissed",{data:{collectible:{id:c?.id,name:c?.name}}})},[c?.id,c?.name]),$l=Ye&&M,Zl=(0,v.useCallback)(()=>{Po(),i(c),l("sendCollectibleForm"),X.capture("selectSendAsset",{data:{chainId:c.chain?.id,name:c.name,symbol:c.symbol,type:"collectible"}})},[c,l,i]),Kl=(0,v.useMemo)(()=>N.map(se=>({text:se.label,type:`shortcut-${se.icon}`,onClick:()=>{Js(ze??"",Ge??"",se)}})),[Ge,ze,N]),vr=$r({amountToBurn:g,explorerName:A,isBurnable:z,isHidden:M,isPinned:K,isSpam:Ye,onBurnToken:J,onList:Ao,onReportNotSpam:ne,onReportSpam:fe,onSend:Zl,onSetAsAvatar:Ne,onShortcuts:Mo,onToggleHide:We,onTogglePin:Ro,onViewOnExplorer:wo,shortcuts:Kl,shouldDisplaySend:et,shouldDisplayShortcuts:Dt,shouldDisplayListOnSolana:ae});return v.default.createElement(he,{gap:"section",paddingBottom:"screen"},ne?v.default.createElement(Yt,{message:r("collectibleSpamWarning"),actions:[qt==="POSSIBLE_SPAM"&&{label:r("commandReportAsNotSpam"),onClick:ne}].filter(Ht)}):null,v.default.createElement(he,{direction:"row",justifyContent:"center"},v.default.createElement(Ll,{isOverlayVisible:$l,onOverlayPressed:Wt},v.default.createElement(he,{position:"relative"},v.default.createElement(vi,{media:zo,collectibleChainData:c.chainData}),p?.listingPriceUiAmount?v.default.createElement(Al,{title:r("collectiblesListed"),amount:p?.listingPriceUiAmount,symbol:"SOL"}):null))),!R&&v.default.createElement(pi,{headerText:c?.name,actions:vr.actions,shortcuts:vr.shortcuts,hostname:ze,uiContextName:"collectibleDetail",maxButtons:4}),!R&&p&&_?v.default.createElement(he,{gap:"list",width:"100%"},v.default.createElement(_t,{theme:"primary",disabled:!p?.url,onClick:f},r("listStatusViewOnMagicEden")),v.default.createElement(_t,{theme:"primary",onClick:()=>{s({asset:_,listings:Oo??null}),a(Tr(parseInt(p.listingPrice??"0",10)).toNumber()),t(_)}},r("collectiblesEditListing"))):null,w&&v.default.createElement(xl,{title:w.title,subtitle:w.subtitle,iconFill:w.iconFill,onClick:I}),v.default.createElement(ai,null,E&&j?v.default.createElement(ni.div,{style:{width:"100%"},initial:{height:0,opacity:0,scale:.8},animate:{height:"auto",opacity:1,scale:1},transition:{ease:"easeInOut",duration:.3}},v.default.createElement(zl,{title:r("collectibleDetailSellNow",{symbol:E.currencySymbol,amount:E.receiveAmountFormatted}),subtitle:r("collectibleDetailEasilyAccept"),onClick:()=>W(c,E)})):null),v.default.createElement(he,{direction:"row"},S.length>0?v.default.createElement(Vo,{header:xo,rows:S}):null),b&&b.items.length>0?v.default.createElement(he,{direction:"row"},v.default.createElement(he,null,v.default.createElement(Bo,{className:ql,font:"bodyMedium",color:"textTertiary"},b.label),v.default.createElement(he,{direction:"row"},v.default.createElement(Vo,{rows:b.items})))):null,!!T&&T.length>0&&v.default.createElement(he,null,v.default.createElement(Bo,{className:ql,font:"bodyMedium",color:"textTertiary"},r("collectibleDetailProperties")),v.default.createElement(he,{flexWrap:"wrap",direction:"row",gap:"list"},T.map(({type:se,value:Lo,modal:gt})=>v.default.createElement(Jr,{key:se,type:se,value:Lo,onClick:()=>{if(!gt)return;let{name:yr,description:Jl,iconUrl:Ql}=gt;l("interstitial",{bodyTitle:yr,bodyDescription:Jl,alignBody:"center",icon:v.default.createElement("img",{className:Ks,src:Ql}),FooterComponent:()=>v.default.createElement(_t,{theme:"primary",onClick:()=>n("interstitial")},r("gotIt"))})}})))))}),Xe=o=>{let{t:r}=$(),{collectibleId:t}=o,{collectible:e,...l}=Qs({collectibleId:t});return v.default.createElement(v.default.Fragment,null,v.default.createElement(vo,{title:e?.name??r("collectiblesUnknownCollectible"),value:e?.balance}),e?v.default.createElement(Xs,{collectible:e,...l}):v.default.createElement(nt,{title:r("errorAndOfflineSomethingWentWrong"),description:r("errorAndOfflineSomethingWentWrongTryAgain")}))};u();d();var G=U(B());var Ys=y.div`
  margin-bottom: 16px;
`,ec=o=>{let{id:r}=o,{t}=$(),{data:e}=xe(),l=e?.addresses??[],n=!!e?.isReadOnly,i=Ut({id:r,addresses:l}),a=(0,G.useMemo)(()=>i?.items??[],[i]),s=(0,G.useMemo)(()=>Math.ceil(a.length/2),[a.length]),c=rt(i),p=(0,G.useMemo)(()=>{if(i)return eo(i,c)},[i,c]),{onReportNotSpam:m,onReportSpam:f,isSpam:_,spamStatus:S}=Vt({accountId:e?.identifier,collectible:c,toast:ye}),{isPinned:b,onTogglePin:T}=$t({collectionId:i?.id,isSpam:_,accountId:e?.identifier,networkId:c?.chain?.id,toast:ye}),{isHidden:w,onToggleHide:I}=Bt({collectible:c,accountId:e?.identifier,toast:ye}),E=(0,G.useMemo)(()=>n?[]:[{key:"collection-group-1",data:[{key:"deposit",label:t(b?"assetDetailUnpinCollection":"assetDetailPinCollection"),onClick:T},{key:"hide",label:t(w?"assetDetailUnhideCollection":"assetDetailHideCollection"),onClick:I}]},{key:"collection-group-2",data:[...f?[{key:"report-spam",label:t("collectiblesReportAsSpam"),onClick:f,variant:"warning"}]:[],...m?[{key:"report-not-spam",label:t("collectiblesReportAsNotSpam"),onClick:m,variant:"info"}]:[]]}],[b,t,T,w,I,f,m,n]);return{collectibles:a,collectionName:p,isError:!i,gridRowCount:s,optionSections:E,onReportNotSpam:m,spamStatus:S,isSpam:_}},tc=o=>{let{id:r}=o,{t}=$(),{pushDetailView:e}=Be(),{data:l}=xe(),n=l?.addresses??[],{data:i}=ot({id:r,addresses:n}),a=i?.chainData,{getIsHidden:s,getIsSpam:c}=jt(l?.identifier??""),p=c(i),m=s(i),f=(0,G.useCallback)(()=>{if(!i?.chainData)return;let b=oo({chainID:i.chain.id,amount:i.balance??"",symbol:i.symbol??"",collectible:i.chainData}),{asset:T,...w}=b,I={...w,...T,spamStatus:i.collection.spamStatus};X.capture("assetDetailClick",{data:I}),e(G.default.createElement(Xe,{collectibleId:i.id}))},[i,e]),_=(0,G.useMemo)(()=>{if(i?.listings)return t("collectiblesListed")},[i,t]),S=Fe(i?.media,"image",!1,"medium");return{isError:!i,image:S,chainData:a,mediaType:i?.media?.type,listedBadge:_,onClick:f,isSpam:p,isHidden:m}},oc=G.default.memo(o=>{let{isError:r,...t}=tc(o);return G.default.createElement(bo,null,r?G.default.createElement(je,null,G.default.createElement(Te,{type:t.mediaType})):G.default.createElement(_o,{...t}))}),rc=G.default.memo(o=>{let{collectibles:r}=o,t=(0,G.useMemo)(()=>[{data:r,id:"collectibles",collapsible:!1,initialCollapsed:!1}],[r]);return G.default.createElement(Co,{sections:t,defaultRowHeight:169,renderCard:e=>G.default.createElement(oc,{...e})})}),yo=o=>{let{t:r}=$(),{isError:t,collectionName:e,optionSections:l,onReportNotSpam:n,spamStatus:i,...a}=ec(o);return G.default.createElement(G.default.Fragment,null,G.default.createElement(vo,{title:e??r("collectiblesUnknownCollection"),value:a.collectibles.length.toString(),sections:l}),n?G.default.createElement(Ys,null,G.default.createElement(Yt,{message:r("collectionSpamWarning"),actions:[i==="POSSIBLE_SPAM"&&{label:r("commandReportAsNotSpam"),onClick:n}].filter(Ht)})):null,t?G.default.createElement(nt,{title:r("errorAndOfflineUnableToFetchCollectibles"),description:"",buttonText:r("homeErrorButtonText")}):G.default.createElement(rc,{...a}))};u();d();var To=50,kl=y(de).attrs({align:"center",padding:"10px"})`
  background-color: #2a2a2a;
  border-radius: 6px;
  height: ${74}px;
  margin-bottom: ${10}px;
  cursor: pointer;
`,Nl=y(lt).attrs({justify:"center"})``,Wl=y(Y).attrs({size:16,weight:600,lineHeight:19,noWrap:!0,maxWidth:"190px"})`
  margin: 0 0 5px 0;
`,Hl=y(Y).attrs({color:"#777777",size:14,lineHeight:17,noWrap:!0})``,lc=y(Y).attrs({color:"#777777",size:16,lineHeight:22,noWrap:!0,weight:600,textAlign:"left",margin:"6px 0 8px"})``,Gl=y.div`
  width: 55px;
  aspect-ratio: 1;
  margin-right: 10px;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
`,Dl=o=>{let{collection:r,hideMedia:t}=o,{pushDetailView:e}=Be(),{t:l}=$(),n=rt(r),i=Fe(n?.media,"image",!1,"small"),a=(0,k.useCallback)(()=>{if(X.capture("collectibleSearchItemClick"),!!r)if(r.items.length===1){let[s]=r?.items??[];e(k.default.createElement(Xe,{collectibleId:s.id}))}else e(k.default.createElement(yo,{id:r.id}))},[r,e]);return r?k.default.createElement(kl,{onClick:a},k.default.createElement(Gl,null,t?k.default.createElement(ro,{width:40}):i?k.default.createElement(vt,{uri:i,width:To,height:To}):k.default.createElement(Te,{type:"image",width:42})),k.default.createElement(de,null,k.default.createElement(Nl,null,k.default.createElement(de,{align:"center",margin:"0 0 5px 0"},k.default.createElement(Wl,{style:{margin:"0"}},r.name),t?k.default.createElement(Zt,{height:16,fill:Kr.colors.legacy.accentWarning}):null),k.default.createElement(Hl,null,l("collectiblesSearchNrOfItems",{nrOfItems:r.items.length}))))):null},nc=o=>{let{collectible:r}=o,{pushDetailView:t}=Be(),e=Fe(r?.media,"image",!1,"small"),l=(0,k.useCallback)(()=>{X.capture("collectibleSearchItemClick"),r&&t(k.default.createElement(Xe,{collectibleId:r.id}))},[r,t]);return r?k.default.createElement(kl,{onClick:l},k.default.createElement(Gl,null,e?k.default.createElement(vt,{uri:e,width:To,height:To}):k.default.createElement(Te,{type:r.media?.type,width:42})),k.default.createElement(de,null,k.default.createElement(Nl,null,k.default.createElement(Wl,null,r.name),k.default.createElement(Hl,null,r.collection.name)))):null},Fl=o=>{let{item:r}=o;switch(r.type){case 0:return k.default.createElement(lc,null,r.title);case 1:return k.default.createElement(Dl,{collection:r.collection});case 2:return k.default.createElement(nc,{collectible:r.collectible});case 3:return k.default.createElement(Dl,{collection:r.collection,hideMedia:!0});default:return null}};var ac=y.div`
  width: 100%;
  padding-bottom: ${5}px;
`,sc=y.div`
  padding-bottom: 15px;
`,cc=o=>{let{index:r,data:t,style:e,parent:l,cellMeasurementCache:n}=o,i=t[r],a=`row-${r}-${i?.collectible?.id??i?.collection?.id??""}`;return oe.default.createElement(Xr,{cache:n,key:a,parent:l,rowIndex:r},oe.default.createElement("div",{style:e},oe.default.createElement(Fl,{item:i})))},uc=o=>{let{t:r}=$(),{collections:t,collectibles:e,hiddenCollections:l,spamCollections:n}=o,i={type:0,title:r("collectiblesSearchCollectionsSection")},a=t.map(T=>({type:1,collection:T})),s={type:0,title:r("collectiblesSearchItemsSection")},c=e.map(T=>({type:2,collectible:T})),p={type:0,title:r("collectionsHiddenCollections")},m=l.map(T=>({type:1,collection:T})),f={type:0,title:r("collectionsSpamCollections")},_=n.map(T=>({type:3,collection:T})),S=[...a.length>0?[i]:[],...a,...c.length>0?[s]:[],...c,...m.length>0?[p]:[],...m,..._.length>0?[f]:[],..._],b=S.length;return{searchRows:S,numSearchRows:b}},jl=oe.default.memo(o=>{let{t:r}=$(),{isTrimmed:t}=o,{searchRows:e,numSearchRows:l}=uc(o),n=Ol(84),i=(0,oe.useRef)(document.getElementById("tab-content"));return e.length===0?oe.default.createElement(oe.default.Fragment,null,oe.default.createElement(Qt,null,r("collectiblesNoCollectibles")),t&&oe.default.createElement(sc,null,oe.default.createElement(Xt,{color:"#FFDC62",title:r("collectiblesTrimmed")}))):oe.default.createElement(ac,null,oe.default.createElement(Bl.default,{scrollElement:i.current??void 0},({height:a=0,width:s=0,isScrolling:c,onChildScroll:p,scrollTop:m})=>oe.default.createElement(ei,{autoHeight:!0,autoWidth:!0,height:a,width:s,isScrolling:c,onScroll:p,scrollTop:m,rowCount:l,rowHeight:n.rowHeight,rowRenderer:f=>oe.default.createElement(cc,{...f,data:e,cellMeasurementCache:n})})))});u();d();u();d();var Vl=dc;function dc(o){var r=[].slice.call(arguments,1);return r.length?function(){for(var t=[].slice.call(arguments),e=[],l=0;l<r.length;l++){var n=r[l];e[l]=n===void 0?t.shift():n}return o.apply(this,e.concat(t))}:o}var Ul=Vl(mi,Zr);var pc=No({seconds:10}),fc=No({seconds:5}),mc=y.div`
  margin-bottom: 15px;
`,hc=y(de).attrs({justify:"space-between"})`
  padding: 12px 0;
  margin-bottom: ${10}px;
  &:hover {
    cursor: pointer;
  }
`,gc=y.div`
  transform: rotate(${o=>o.collapsed?"-90deg":"0deg"});
`,Cc=y(Pe).attrs({diameter:32})``,Sc=y(Y).attrs({size:17,weight:600,lineHeight:22})``,_c=y(Qt)`
  height: 0;
`,bc=y.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
`,vc=[],yc=()=>{let{data:[o]}=Rr(["enable-collections-spam-results"]),{data:r}=xe(),t=r?.addresses??[],{data:e=[],isPending:l,isError:n,isRefetching:i,isRefetchError:a,refetch:s}=qr({addresses:t,queryOptions:{staleTime:fc,refetchInterval:pc}}),c=Dr({addresses:t}),{data:p=[]}=Ho(r?.identifier??""),{getIsHidden:m,getIsSpam:f}=kr(r?.identifier??""),{collections:_,hiddenCollections:S,spamCollections:b}=Gr({allCollections:e,pinnedCollectionIds:p,getIsHidden:m,getIsSpam:f}),T=(0,O.useMemo)(()=>{let w=Math.ceil((_??[]).length/2),I=Math.ceil((S??[]).length/2);return w+I+(I>0?1:0)},[_,S]);return{collections:_,hiddenCollections:S,spamCollections:o??!1?b:vc,isLoading:l,isRefetching:i,isError:n,isRefetchError:a,gridRowCount:T,isTrimmed:c,refetch:()=>void s()}},Tc=o=>{let{id:r}=o,{t}=$(),{pushDetailView:e}=Be(),{data:l}=xe(),n=l?.addresses??[],i=Ut({id:r,addresses:n}),a=rt(i),{data:s}=ot({id:a?.id??"",addresses:n}),c=s?.chainData,{data:p=[]}=Ho(l?.identifier??""),m=Wr(p,i?.id??""),{getIsHidden:f,getIsSpam:_}=jt(l?.identifier??""),S=f(s),b=_(s),{data:T}=Ft(),w=T??Gt,I=(0,O.useCallback)(()=>{if(i)if(Ul(i,w),i.items.length===1){if(s?.chainData){let g=oo({chainID:s.chain.id,amount:s.balance??"",symbol:s.symbol??"",collectible:s.chainData}),{asset:A,...z}=g,M={...z,...A,spamStatus:i.spamStatus};X.capture("assetDetailClick",{data:M})}let[R]=i.items;e(O.default.createElement(Xe,{collectibleId:R.id}))}else e(O.default.createElement(yo,{id:i.id}))},[i,s,e,w]),E=(0,O.useMemo)(()=>{if(!i)return;let R=i.items.filter(g=>!!g.listings);if(R.length!==0)return i.items.length===1&&R.length===1?t("collectiblesListed"):t("collectiblesNrOfListed",{nrOfListed:R.length})},[i,t]),j=(0,O.useMemo)(()=>{if(i)return eo(i,s)},[i,s]),W=Br(s),N=jr(i);return{isError:!i,name:j,nrOfItems:N,image:W,chainData:c,mediaType:s?.media?.type,listedBadge:E,isPinned:m,isHidden:S,isSpam:b,onClick:I}},Rc=O.default.memo(o=>{let{isError:r,...t}=Tc(o);return O.default.createElement(bo,{key:o.id},r?O.default.createElement(je,null,O.default.createElement(Te,{width:48})):O.default.createElement(_o,{...t}))}),wc=O.default.memo(o=>O.default.createElement(hc,null,O.default.createElement(Sc,null,o.title),O.default.createElement(gc,{collapsed:o.collapsed},O.default.createElement(Cc,null,O.default.createElement(ti,{width:10,fill:"white"}))))),Ic=O.default.memo(o=>{let{t:r}=$(),{collections:t,gridRowCount:e,isTrimmed:l}=o,n=(0,O.useMemo)(()=>{let i=[{data:t,id:"collections",collapsible:!1,initialCollapsed:!1}];return l&&i.push({title:r("collectiblesTrimmed"),id:"trimmed",data:[],collapsible:!1,initialCollapsed:!1}),i},[t,l,r]);return e===0?O.default.createElement(bc,null,O.default.createElement(_c,null,r("collectiblesNoCollectibles"))):O.default.createElement(Co,{sections:n,defaultRowHeight:169,renderCard:i=>O.default.createElement(Rc,{...i}),renderTitle:(i,a)=>O.default.createElement(wc,{title:i,collapsed:a})})}),Oc=()=>{let{t:o}=$(),[r,t]=(0,O.useState)(""),e=Or(r)??"",{isLoading:l,isError:n,isRefetchError:i,isRefetching:a,refetch:s,isTrimmed:c,...p}=yc(),m=Fr(p.collections,p.hiddenCollections,p.spamCollections,e),f=p.collections.length+p.hiddenCollections.length===0,{handleShowModalVisibility:_}=yt();return O.default.createElement(O.default.Fragment,null,f?null:O.default.createElement(mc,null,O.default.createElement(di,{value:r,placeholder:o("collectiblesSearchPlaceholderText"),onChange:S=>t(S.currentTarget.value),showClearIcon:r.length>0,onClear:()=>t("")})),l||a&&f?O.default.createElement(Tl,null):f&&(n||i)?O.default.createElement(nt,{title:o("errorAndOfflineUnableToFetchCollectibles"),description:"",buttonText:o("homeErrorButtonText"),refetch:s}):e!==""?O.default.createElement(jl,{...m,isTrimmed:c}):O.default.createElement(O.default.Fragment,null,O.default.createElement(Ic,{...p,isTrimmed:c}),O.default.createElement(Il,{buttonText:o("collectionsManageCollectibles"),onClick:()=>_("collectiblesVisibility")}),O.default.createElement("div",null,"\xA0")))},Zh=Oc;export{Zh as default};
//# sourceMappingURL=CollectionsPage-5CFTZAXN.js.map
