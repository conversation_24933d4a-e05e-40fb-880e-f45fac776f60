import { FullConfig } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';
import * as rimraf from 'rimraf';

/**
 * Global teardown để dọn dẹp môi trường sau khi chạy test
 * - Xóa các trạng thái đăng nhập đã lưu (t<PERSON><PERSON> chọn)
 * - Dọn dẹp dữ liệu tạm
 */
async function globalTeardown(config: FullConfig): Promise<void> {
  console.log('=== Bắt đầu global teardown ===');
  
  // Kiểm tra cờ tham số từ dòng lệnh hoặc biến môi trường
  // Ví dụ: npm test -- --reset-login
  const shouldResetLogin = process.env.RESET_LOGIN === 'true' || 
                           process.argv.includes('--reset-login');
  
  if (shouldResetLogin) {
    console.log('Đang xóa trạng thái đăng nhập...');
    
    // Xóa file trạng thái đăng nhập
    const loginStatePath = path.join(__dirname, 'login-state.json');
    if (fs.existsSync(loginStatePath)) {
      fs.unlinkSync(loginStatePath);
      console.log('Đã xóa file login-state.json');
    }
    
    // Xóa file storage state
    const storageStatePath = path.join(__dirname, 'storageState.json');
    if (fs.existsSync(storageStatePath)) {
      fs.unlinkSync(storageStatePath);
      console.log('Đã xóa file storageState.json');
    }
    
    // Xóa thư mục dữ liệu người dùng
    const userDataDir = path.join(__dirname, 'user-data-dir');
    if (fs.existsSync(userDataDir)) {
      try {
        rimraf.sync(userDataDir);
        console.log('Đã xóa thư mục user-data-dir');
      } catch (error) {
        console.error('Lỗi khi xóa thư mục user-data-dir:', error);
      }
    }
  } else {
    console.log('Bỏ qua bước xóa trạng thái đăng nhập (sử dụng --reset-login để xóa)');
  }
  
  // Dọn dẹp các file tạm thời khác (nếu có)
  cleanupTempFiles();
  
  console.log('=== Kết thúc global teardown ===');
}

/**
 * Dọn dẹp các file tạm thời
 */
function cleanupTempFiles() {
  // Xóa các screenshot tự động tạo ra (tùy chọn)
  const screenshotFiles = [
    path.join(__dirname, 'setup-failed.png'),
    path.join(__dirname, 'login-failed.png')
  ];
  
  for (const file of screenshotFiles) {
    if (fs.existsSync(file)) {
      try {
        fs.unlinkSync(file);
        console.log(`Đã xóa file ${file}`);
      } catch (error) {
        console.error(`Lỗi khi xóa file ${file}:`, error);
      }
    }
  }
}

export default globalTeardown;
