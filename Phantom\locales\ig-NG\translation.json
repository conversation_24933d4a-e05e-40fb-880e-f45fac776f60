{"accountHeaderConnectedInterpolated": "You are connected to {{origin}}", "accountHeaderConnectedToSite": "You are connected to this site", "accountHeaderCopied": "Copied!", "accountHeaderCopyToClipboard": "Copy to clipboard", "accountHeaderNotConnected": "You are not connected to", "accountHeaderNotConnectedInterpolated": "You are not connected to {{origin}}", "accountHeaderNotConnectedToSite": "You are not connected to this site", "addAccountActionButtonClose": "Close", "addAccountHardwareWalletPrimaryText": "Connect hardware wallet", "addAccountHardwareWalletSecondaryText": "Use your Ledger wallet", "addAccountHardwareWalletSecondaryTextMobile": "Use your Ledger Nano S/X wallet", "addAccountImportWalletPrimaryText": "Import private key", "addAccountImportWalletSecondaryText": "Import an existing wallet", "addAccountNewWalletPrimaryText": "Create a new wallet", "addAccountNewWalletSecondaryText": "Generate a new wallet address", "addAccountPrimaryText": "Add / Connect Wallet", "addAccountImportAccountActionButtonImport": "Import", "addAccountImportAccountDuplicatePrivateKey": "This account already exists in your wallet", "addAccountImportAccountIncorrectFormat": "Incorrect format", "addAccountImportAccountInvalidPrivateKey": "Invalid Private Key", "addAccountImportAccountName": "Name", "addAccountImportAccountPrimaryText": "Import Private Key", "addAccountImportAccountPrivateKey": "Private key", "addAccountImportAccountPrivateKeyRequired": "Private key is required", "addAddressActionButtonPrimary": "Add", "addAddressActionButtonSecondary": "Cancel", "addAddressAddressAlreadyAdded": "Address is already added", "addAddressAddressAlreadyExists": "Address already exists", "addAddressAddressIsRequired": "Address is required", "addAddressAddressPlaceholder": "Address", "addAddressLabelIsRequired": "Label is required", "addAddressLabelPlaceholder": "Label", "addAddressPrimaryText": "Add Address", "addEditTokenActionButtonAdd": "Add", "addEditTokenActionButtonCancel": "Cancel", "addEditTokenActionButtonSave": "Save", "addEditTokenAddMetadata": "Add token metadata", "addEditTokenEditMetadata": "Edit token metadata", "addEditTokenErrorAccountNotFound": "Failed to find token account", "addEditTokenErrorDuplicateToken": "You already have this token", "addEditTokenErrorInvalidMint": "Invalid mint address", "addEditTokenErrorInvalidName": "Invalid name", "addEditTokenErrorInvalidSymbol": "Invalid symbol", "addEditTokenMintAddress": "Mint Address", "addEditTokenName": "Name", "addEditTokenSymbol": "Symbol", "addEditTokenThisWillCost": "This will cost", "addEditTokenThisWillCostInterpolated": "This will cost {{amount}} SOL", "assetDetailActionButtonDeposit": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailActionButtonSend": "Send", "assetDetailButtonCancel": "Cancel", "assetDetailEditTokenMetadata": "Edit token metadata", "assetDetailRecentActivity": "Recent Activity", "assetDetailStakeSOL": "Stake SOL", "assetDetailUnknownToken": "Unknown To<PERSON>", "assetDetailUnwrapAll": "Unwrap All", "assetDetailViewOnExplorer": "View on Explorer", "assetDetailViewOnSolscan": "View on Solscan", "assetListAddCustomToken": "Add custom token", "assetListSearch": "Search...", "assetListUnknownToken": "Unknown To<PERSON>", "assetSelectionClose": "Close", "assetVisibilityClose": "Close", "assetVisibilityUnknownToken": "Unknown To<PERSON>", "blocklistConnectionActionButtonClose": "Close", "blocklistConnectionDisabled": "<PERSON> believes this website is malicious and unsafe to use. We have disabled the ability to interact with it in order to protect you and your funds.", "blocklistConnectionIgnoreWarning": "Ignore warning, connect anyway", "blocklistOriginCommunityDatabaseInterpolated": "This site has been flagged as part of a <1>community-maintained database</1> of known phishing websites and scams. If you believe the site has been flagged in error, <3>please file an issue</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} is blocked!", "blocklistOriginIgnoreWarning": "Ignore this warning, take me to {{domainName}} anyway.", "blocklistOriginSiteIsMalicious": "<PERSON> believes this website is malicious and unsafe to use.", "blocklistOriginThisDomain": "this domain", "maliciousTransactionWarningButtonClose": "Close", "maliciousTransactionWarning": "Phantom believes this transaction is malicious and unsafe to sign. We have disabled the ability to sign it in order to protect you and your funds.", "maliciousTransactionWarningIgnoreWarning": "Ignore warning, proceed anyway", "maliciousTransactionWarningTitle": "Transaction flagged!", "changeLockTimerActionButtonPrimary": "Save", "changeLockTimerActionButtonSecondary": "Cancel", "changeLockTimerPrimaryText": "Auto-Lock Timer", "changeLockTimerSecondaryText": "How long should we wait to lock your wallet after it has been idle?", "changePasswordActionButtonPrimary": "Save", "changePasswordActionButtonSecondary": "Cancel", "changePasswordConfirmNewPassword": "Confirm new password", "changePasswordCurrentPassword": "Current password", "changePasswordErrorIncorrectCurrentPassword": "Incorrect current password", "changePasswordErrorGeneric": "Something went wrong, please try again later", "changePasswordNewPassword": "New password", "changePasswordPrimaryText": "Change password", "collectibleDetailDescription": "Description", "collectibleDetailProperties": "Properties", "collectibleDetailSend": "Send", "collectibleDetailViewOnSolscan": "View on Solscan", "collectibleDisplayLoading": "Loading...", "collectiblesNoCollectibles": "No collectibles", "collectiblesPrimaryText": "Your Collectibles", "collectiblesReceiveCollectible": "Receive Collectible", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Connect your hardware wallet and make sure it is unlocked. Once we’ve detected it you can choose which address you’d like to use.", "connectHardwareContinueActionButtonText": "Continue", "connectHardwareFailedPrimaryText": "Connection failed", "connectHardwareFailedRetryActionButtonText": "Retry", "connectHardwareFailedSecondaryText": "Please connect your hardware wallet and make sure it is unlocked. Once we discover it you can choose which address to use.", "connectHardwareFinishPrimaryText": "Account Added!", "connectHardwareFinishSecondaryText": "You can now access your Ledger Nano wallet from within Phantom. Please return to the extension.", "connectHardwareNeedsPermissionPrimaryText": "Connect a new wallet", "connectHardwareNeedsPermissionSecondaryText": "Click the button below to start the connection process.", "connectHardwareSearchingPrimaryText": "Searching for wallet...", "connectHardwareSearchingSecondaryText": "Connect your hardware wallet, make sure it is unlocked, and that you have approved permissions in your browser.", "connectHardwareSelectAddressAllAddressesImported": "All addresses imported", "connectHardwareSelectAddressDerivationPath": "Derivation path", "connectHardwareSelectAddressSearching": "Searching...", "connectHardwareSelectAddressSelectWalletAddress": "Select wallet address", "connectHardwareSelectAddressWalletAddress": "Wallet address", "connectHardwareWaitingForApplicationPrimaryText": "Open the Solana app on your Ledger", "connectHardwareWaitingForApplicationSecondaryText": "Please connect your hardware wallet and make sure it is unlocked.", "connectHardwareWaitingForPermissionPrimaryText": "Need permission", "connectHardwareWaitingForPermissionSecondaryText": "Connect your hardware wallet, make sure it is unlocked, and that you have approved permissions in your browser.", "assetQueriesUnableToConnect": "We’re unable to connect to Solana", "assetQueriesUnableToFetchTokenPrices": "We were unable to fetch token prices", "connectionClusterInterpolated": "You are currently on {{cluster}}", "copyDefaultCopyText": "Copy", "copyCopiedText": "Copied!", "depositAssetActionButtonClose": "Close", "depositAssetBuyWithMoonpay": "Buy with MoonPay", "depositAssetDeposit": "<PERSON><PERSON><PERSON><PERSON>", "depositAssetDepositInterpolated": "Deposit {{tokenSymbol}}", "depositAssetFTXTooltipLabel": "Transfer SOL and SPL tokens directly from your FTX.us account.", "depositAssetIntermediateDepositActionButtonClose": "Close", "depositAssetIntermediateDepositDeposit": "<PERSON><PERSON><PERSON><PERSON>", "depositAssetMoonPayTooltipLabel": "Easily buy SOL with a debit card, credit card or bank transfer.", "depositAssetPrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "depositAssetSecondaryText": "This address can only be used to receive SOL and SPL tokens on Solana.", "depositAssetSendFrom": "Send from wallet / exchange", "depositAssetTransferFromFTX": "Transfer from FTX", "depositAssetShareAddress": "Share address", "depositFlowActionButtonClose": "Close", "depositRowDepositSOL": "Deposit SOL", "depositRowDepositDisclaimer": "SOL is used to pay for transactions", "editAddressActionButtonCancel": "Cancel", "editAddressActionButtonSave": "Save", "editAddressAddressAlreadyAdded": "Address is already added", "editAddressAddressAlreadyExists": "Address already exists", "editAddressAddressIsRequired": "Address is required", "editAddressPrimaryText": "Edit Address", "editAddressRemove": "Remove from Address Book", "exportSecretActionButtonDone": "Done", "exportSecretActionButtonPrimary": "Next", "exportSecretActionButtonSecondary": "Cancel", "exportSecretErrorGeneric": "Something went wrong, please try again later", "exportSecretErrorIncorrectPassword": "Incorrect password", "exportSecretPassword": "Password", "exportSecretPrivateKey": "private key", "exportSecretSecretPhrase": "secret phrase", "exportSecretSecretRecoveryPhrase": "secret recovery phrase", "exportSecretShowPrivateKey": "Show private key", "exportSecretShowSecretRecoveryPhrase": "Show secret recovery phrase", "exportSecretWarningPrimaryInterpolated": "Do <1>not</1> share your {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "If someone has your {{secretNameText}} they will have full control of your wallet.", "exportSecretYourPrivateKey": "Your private key", "exportSecretYourSecretRecoveryPhrase": "Your secret recovery phrase", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "Help", "homeManageTokenList": "Manage token list", "homeDeposit": "<PERSON><PERSON><PERSON><PERSON>", "homeSend": "Send", "ledgerActionActionButtonCancel": "Cancel", "ledgerActionActionButtonContinue": "Continue", "ledgerActionApprove": "Approve on your Ledger Nano", "ledgerActionActionButtonRetry": "Retry", "ledgerActionErrorBlindSignDisabledPrimaryText": "Blind sign disabled", "ledgerActionErrorBlindSignDisabledSecondaryText": "Please make sure blind sign is enabled on your hardware device and then retry the action", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Hardware device disconnected during operation", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Please close the Phantom extension and then retry the action", "ledgerActionErrorDeviceLockedPrimaryText": "Hardware device locked", "ledgerActionErrorDeviceLockedSecondaryText": "Please unlock your hardware device and retry the action", "ledgerActionErrorHeader": "Ledger Action Error", "ledgerActionErrorUserRejectionPrimaryText": "User rejected transaction", "ledgerActionErrorUserRejectionSecondaryText": "The action was rejected on the hardware device by the user", "ledgerActionNeedPermission": "Need permission", "ledgerActionNeedToApprove": "You need to approve the transaction on your hardware wallet. Make sure it is unlocked and on the Solana app", "ledgerActionPleaseConnect": "Please connect your Ledger Nano", "ledgerActionPleaseConnectAndApprove": "Please connect your hardware wallet and make sure it is unlocked. Make sure you have approved permissions in your browser.", "maxInputAmount": "Amount", "maxInputMax": "Max", "notEnoughSolActionButtonCancel": "Cancel", "notEnoughSolPrimaryText": "Not enough SOL", "notEnoughSolSecondaryText": "You don’t have enough SOL in your wallet to pay for the transaction fee. Please deposit more and try again.", "notificationApplicationApprovalPermissionsPrimary": "This app would like to:", "notificationApplicationApprovalPermissionsTransactionApproval": "Request approval for transactions", "notificationApplicationApprovalPermissionsViewWalletActivity": "View your wallet balance & activity", "notificationApplicationApprovalActionButtonConnect": "Connect", "notificationApplicationApprovalActionButtonCancel": "Cancel", "notificationApplicationApprovalAllowApproval": "Allow site to connect?", "notificationApplicationApprovalAutoApprove": "Auto-approve transactions", "notificationApplicationApprovalConnectDisclaimer": "Only connect to websites you trust", "notificationSignatureRequestApproveTransaction": "Approve transaction", "notificationSignatureRequestApproveTransactionCapitalized": "Approve Transaction", "notificationSignatureRequestSignatureRequest": "Signature Request", "notificationTransactionApprovalActionButtonApprove": "Approve", "notificationTransactionApprovalActionButtonCancel": "Cancel", "notificationTransactionApprovalEstimatedBalanceChanges": "Estimated Balance Changes", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Estimates are based on transaction simulations and are not a guarantee", "notificationTransactionApprovalHideAdvancedDetails": "Hide advanced transaction details", "notificationTransactionApprovalNetworkFee": "Network Fee", "notificationTransactionApprovalNoBalanceChanges": "No balance changes found", "notificationTransactionApprovalSolanaAmountRequired": "Amount required by the Solana network to process the transaction", "notificationTransactionApprovalTransactionMayFailToConfirm": "Transaction may fail to confirm", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Unable to fetch balance changes", "notificationTransactionApprovalViewAdvancedDetails": "View advanced transaction details", "notificationTransactionApprovalSignUnableToSimulate": "This transaction cannot be simulated. Approving may lead to loss of funds.", "notificationTransactionApprovalKnownMalicious": "This transaction is malicious. Signing will lead to loss of funds.", "notificationTransactionApprovalSuspectedMalicious": "We suspect this transaction is malicious. Approving may lead to loss of funds.", "onboardingCreatePassword": "Create a password", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "I agree to the <1>Terms of Service</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Confirm Password", "onboardingCreatePasswordContinue": "Continue", "onboardingCreatePasswordDescription": "You will use this to unlock your wallet.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Invalid secret recovery phrase", "onboardingCreatePasswordPasswordPlaceholder": "Password", "onboardingCreatePasswordPasswordStrengthWeak": "Weak", "onboardingCreatePasswordPasswordStrengthMedium": "Medium", "onboardingCreatePasswordPasswordStrengthStrong": "Strong", "onboardingCreateRecoveryPhraseContinue": "Continue", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "I saved my Secret Recovery Phrase", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Secret Recovery Phrase", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "This phrase is the ONLY way to recover your wallet. Do NOT share it with anyone!", "onboardingCreateRecoveryPhraseSaveIn1Password": "Save in 1Password", "onboardingCreateRecoveryPhraseSaved": "Saved!", "onboardingImportWallet": "Import Wallet", "onboardingImportWalletImportExistingWallet": "Import an existing wallet with your 12 or 24-word secret recovery phrase.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON>", "onboardingImportWalletSecretRecoveryPhrase": "Secret Recovery Phrase", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Invalid Secret Recovery Phrase", "onboardingImportWalletIHaveWords": "I have a {{numWords}}-word recovery phrase", "onboardingImportWalletScrollDown": "Scroll down", "onboardingImportWalletScrollUp": "Scroll up", "onboardingKeyboardShortcut": "Keyboard shortcut", "onboardingKeyboardShortcutContinue": "Continue", "onboardingKeyboardShortcutDescription": "You can open Phantom at any time by using this handy keyboard shortcut.", "onboardingKeyboardShortcutMac": "Option + Shift + P", "onboardingKeyboardShortcutNotMac": "Alt + Shift + P", "onboardingKeyboardShortcutTry": "Try:", "onboardingPathSelectionCreateWallet": "Create a new wallet", "onboardingPathSelectionTagline": "A crypto wallet reimagined for DeFi & NFTs", "onboardingPathSelectionIHaveAWallet": "I already have a wallet", "onboardingSelectAccountsImportAccounts": "Import Accounts", "onboardingSelectAccountsImportAccountsDescription": "Choose wallet accounts to import.", "onboardingSelectAccountsImportSelectedAccounts": "Import Selected Accounts", "onboardingSocialsFinishAction": "Finish", "onboardingSocialsFinished": "You're all done!", "onboardingSocialsFinishedDescription": "Follow along with product updates or reach out if you have any questions.", "onboardingSocialsFollowOnTwitter": "Follow us on Twitter", "onboardingSocialsVisitHelpCenter": "Visit the help center", "recentActivityPrimaryText": "Recent Activity", "removeAccountActionButtonCancel": "Cancel", "removeAccountActionButtonRemove": "Remove", "removeAccountRemoveWallet": "Remove wallet", "removeAccountWarningLedger": "Even though you are removing this wallet from Phantom, you will be able to re-add it using the \"Connect Hardware Wallet\" flow.", "removeAccountWarningPrivateKey": "Once you remove this wallet, Phantom won’t be able to recover it for you. Make sure you have your private key backed up.", "removeAccountWarningSeed": "Even though you are removing this wallet from Phantom, you will be able to re-derive it using your mnemonic in this or another wallet.", "resetSeedActionButtonPrimary": "Continue", "resetSeedActionButtonSecondary": "Cancel", "resetSeedPrimaryText": "Resetting your secret recovery phrase", "resetSeedSecondaryText": "This will remove all existing wallets and replace them with new ones. Make sure you have your existing secret phrase and private keys backed up.", "richTransactionsDays": "days", "richTransactionsToday": "Today", "richTransactionsYesterday": "Yesterday", "richTransactionDetailAccount": "Account", "richTransactionDetailAt": "at", "richTransactionDetailCompleted": "Completed", "richTransactionDetailConfirmed": "Confirmed", "richTransactionDetailAppInteraction": "App Interaction", "richTransactionDetailDate": "Date", "richTransactionDetailFailed": "Failed", "richTransactionDetailFrom": "From", "richTransactionDetailNetworkFee": "Network Fee", "richTransactionDetailPending": "Pending", "richTransactionDetailProvider": "Provider", "richTransactionDetailReceived": "Received", "richTransactionDetailSent": "<PERSON><PERSON>", "richTransactionDetailStaked": "Staked", "richTransactionDetailStatus": "Status", "richTransactionDetailSwap": "<PERSON><PERSON><PERSON>", "richTransactionDetailSwapDetails": "Swap Details", "richTransactionDetailTo": "To", "richTransactionDetailTokenSwap": "To<PERSON>", "richTransactionDetailUnknownNFT": "Unknown NFT", "richTransactionDetailUnstaked": "Unstaked", "richTransactionDetailValidator": "Validator", "richTransactionDetailViewOnSolscan": "View on Solscan", "richTransactionDetailWithdrawStake": "Withdraw Stake", "richTransactionDetailYouPaid": "<PERSON> Paid", "richTransactionDetailYouReceived": "You Received", "sendAddressBookButtonLabel": "Address Book", "addressBookSelectAddressBook": "Address Book", "sendAddressBookNoAddressesSaved": "No addresses saved", "sendAddressBookRecentlyUsed": "Recently Used", "addressBookSelectRecentlyUsed": "Recently Used", "sendConfirmationActionButtonCancel": "Cancel", "sendConfirmationActionButtonSend": "Send", "sendConfirmationLabel": "Label", "sendConfirmationMessage": "Message", "sendConfirmationNetworkFee": "Network Fee", "sendConfirmationPrimaryText": "Confirm Send", "sendStatusErrorActionButtonCancel": "Cancel", "sendStatusErrorActionButtonRetry": "Retry", "sendStatusErrorMessageInterpolated": "There was an error attempting to send tokens to <1>{{uiRecipient}}</1>", "sendStatusErrorTitle": "Unable to send", "sendStatusLoadingTitle": "Sending...", "sendStatusSuccessClose": "Close", "sendStatusSuccessMessageInterpolated": "Your tokens were successfully sent to <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Sent!", "sendFormActionButtonNext": "Next", "sendFormActionButtonCancel": "Cancel", "sendFormErrorInsufficientBalance": "Insufficient balance", "sendFormErrorInvalidSolanaAddress": "Invalid <PERSON> address", "sendFormErrorInvalidTwitterHandle": "This Twitter handle is not registered", "sendFormErrorInvalidDomain": "This domain is not registered", "sendFormErrorMinRequiredInterpolated": "At least {{minAmount}} {{tokenName}} required", "sendFormSend": "Send", "sendRecipientTextareaPlaceholder": "Recipient's SOL address", "sendSelectionActionButtonClose": "Close", "settings": "Settings", "settingsAddressBookNoLabel": "No Label", "settingsAddressBookPrimary": "Address Book", "settingsAddressBookRecentlyUsed": "Recently Used", "settingsAddressBookSecondary": "Manage commonly used addresses", "settingsAutoLockTimerPrimary": "Auto-Lock Timer", "settingsAutoLockTimerSecondary": "Change your auto-lock timer duration", "settingsChangeLanguagePrimary": "Change Language", "settingsChangeLanguageSecondary": "Change the display language", "settingsChangeNetworkPrimary": "Change Network", "settingsChangeNetworkSecondary": "Configure your network settings", "settingsChangePasswordPrimary": "Change Password", "settingsChangePasswordSecondary": "Change your lock screen password", "settingsDisplayLanguage": "Display Language", "settingsErrorCannotExportLedgerPrivateKey": "Cannot export Ledger private key", "settingsErrorCannotRemoveAllWallets": "Cannot remove all wallets", "settingsExportPrivateKey": "Export Private Key", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNoAddresses": "No addresses", "settingsNoTrustedApps": "No trusted apps", "settingsRemoveWallet": "Remove Wallet", "settingsResetSecretRecoveryPhrase": "Reset Secret Recovery Phrase", "settingsShowSecretRecoveryPhrase": "Show Secret Recovery Phrase", "settingsTrustedAppsAutoApprove": "Auto-approve", "settingsTrustedAppsDisclaimer": "Only enable auto-approve on trusted sites", "settingsTrustedAppsPrimary": "Trusted Apps", "settingsTrustedAppsRevoke": "Revoke", "settingsTrustedAppsSecondary": "Configure your trusted applications", "stakeAccountCardActiveStake": "Active Stake", "stakeAccountCardBalance": "Balance", "stakeAccountCardRentReserve": "Rent Reserve", "stakeAccountCardRewards": "Rewards", "stakeAccountCardStakeAccount": "Stake Account", "stakeAccountCreateAndDelegateErrorStaking": "There was a problem staking to this validator. Please try again.", "stakeAccountCreateAndDelegateSolStaked": "SOL Staked!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Your SOL will begin earning rewards <1></1> in the next couple days once the stake account becomes active.", "stakeAccountCreateAndDelegateStakingFailed": "Staking Failed", "stakeAccountCreateAndDelegateStakingSol": "Staking SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "We’re creating a staking account, then delegating your SOL to", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "We’re creating a staking account, then delegating your SOL to {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "View Transaction", "stakeAccountDeactivateStakeSolUnstaked": "SOL Unstaked!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "You will be able to withdraw your stake <1></1> in the next couple days once the stake account becomes inactive.", "stakeAccountDeactivateStakeUnstakingFailed": "Unstaking Failed", "stakeAccountDeactivateStakeUnstakingFailedDescription": "There was a problem unstaking from this validator. Please try again.", "stakeAccountDeactivateStakeUnstakingSol": "Unstaking SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "We are starting the process to unstake your SOL.", "stakeAccountDeactivateStakeViewTransaction": "View Transaction", "stakeAccountDelegateStakeSolStaked": "SOL Staked!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Your SOL will begin earning rewards <1></1> in the next couple days once the stake account becomes active.", "stakeAccountDelegateStakeStakingFailed": "Staking Failed", "stakeAccountDelegateStakeStakingFailedDescription": "There was a problem staking to this validator. Please try again.", "stakeAccountDelegateStakeStakingSol": "Staking SOL...", "stakeAccountDelegateStakeStakingSolDescription": "We’re delegating your SOL.", "stakeAccountDelegateStakeViewTransaction": "View Transaction", "stakeAccountListActivationActivating": "Activating", "stakeAccountListActivationActive": "Active", "stakeAccountListActivationInactive": "Inactive", "stakeAccountListActivationDeactivating": "Deactivating", "stakeAccountListErrorFetching": "There was a problem fetching stake accounts:", "stakeAccountListNoStakingAccounts": "No Staking Accounts", "stakeAccountListReload": "Reload", "stakeAccountListViewPrimaryText": "Your Stake", "stakeAccountListViewStakeSOL": "Stake SOL", "stakeAccountViewActionButtonClose": "Close", "stakeAccountViewActionButtonRestake": "Restake", "stakeAccountViewActionButtonUnstake": "Unstake", "stakeAccountViewError": "Error", "stakeAccountViewPrimaryText": "Your Stake", "stakeAccountViewRestake": "Restake", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Your SOL is currently staked with a validator. You’ll need to unstake to <1></1>access these funds. <3>Learn more</3>", "stakeAccountViewStakeInactive": {"part1": "This stake account is inactive. Consider withdrawing its stake or finding a validator to delegate to.", "part2": "Learn more"}, "stakeAccountViewStakeNotFound": "This stake account could not be found.", "stakeAccountViewViewOnExplorer": "View on Explorer", "stakeAccountViewViewOnSolscan": "View on Solscan", "stakeAccountViewWithdrawStake": "Withdraw Stake", "stakeAccountViewWithdrawUnstakedSOL": "Withdraw Unstaked SOL", "stakeAccountWithdrawStakeSolWithdrawn": "SOL Withdrawn!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Your SOL has been withdrawn.", "part2": "This stake account will automatically be removed within the next few minutes."}, "stakeAccountWithdrawStakeViewTransaction": "View Transaction", "stakeAccountWithdrawStakeWithdrawalFailed": "Withdrawal Failed", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "There was a problem withdrawing from this stake account. Please try again.", "stakeAccountWithdrawStakeWithdrawingSol": "Withdrawing SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "We are withdrawing your SOL from this stake account.", "startEarningSolAccount": "account", "startEarningSolAccounts": "accounts", "startEarningSolErrorClosePhantom": "Close Phantom and try again", "startEarningSolErrorTroubleLoading": "Trouble loading stake", "startEarningSolLoading": "Loading...", "startEarningSolPrimaryText": "Start earning SOL", "startEarningSolSearching": "Searching for staking accounts", "startEarningSolStakeTokens": "Stake tokens and earn rewards", "startEarningSolYourStake": "Your stake", "swapFeesEstimatedFees": "Estimated Fees", "swapFeesRate": "Rate", "swapFeesSlippage": "Slippage", "swapFeesSlippageDisclaimer": "Your transaction will fail if the price \nchanges unfavorably more than this percentage.", "swapFeesSlippageTolerance": "Slippage Tolerance", "swapFeesPriceImpact": "Price Impact", "swapFeesPriceImpactDisclaimer": "The difference between the market price and estimated price based on your trade size.", "swapFlowYouPay": "You Pay", "swapFlowYouReceive": "You Receive", "swapFlowActionButtonText": "Review Order", "swapQuoteFeeDisclaimer": "Rate includes a {{feePercentage}} Phantom fee", "swapQuoteMissingContext": "Missing swap quote context", "swapQuoteErrorNoQuotes": "Trying to swap with no quotes", "swapQuoteSolanaNetwork": "Solana Network", "swapQuoteOneTimeTokenAccount": "One-time token account", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON><PERSON>", "swapReviewFlowActionButtonSecondary": "Cancel", "swapReviewFlowPrimaryText": "Review Order", "swapReviewFlowYouPay": "You Pay", "swapReviewFlowYouReceive": "You Receive", "swapTxConfirmationActionButtonClose": "Close", "swapTxConfirmationReceived": "Received!", "swapTxConfirmationSwapFailed": "Swap failed", "swapTxConfirmationSwapFailedSlippageLimit": "The swap has hit the slippage limit, please try again.", "swapTxConfirmationSwapFailedTryAgain": "The swap has failed, please try again", "swapTxConfirmationSwappingTokens": "Swapping tokens...", "swapTxConfirmationTokens": "Tokens", "swapTxConfirmationTokensDeposited": "It's done! Tokens have been deposited into your wallet", "swapTxConfirmationTokensWillBeDeposited": "will be deposited into your wallet once the transaction is complete", "swapTxConfirmationViewTransaction": "View Transaction", "swapperMax": "Max", "switchToggle": "Toggle", "termsOfServiceActionButtonAgree": "I Agree", "termsOfServiceActionButtonCancel": "Cancel", "termsOfServiceDisclaimerFeesDisabledInterpolated": "By clicking <1>\"I Agree\"</1> you accept the <3>Terms and Conditions</3> of swapping tokens with Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "We have revised our Terms of Service. By clicking <1>\"I Agree\"</1> you agree to our new <3>Terms of Service</3>.<5></5><6></6>Our new Terms of Service include a new <8>fee structure</8> for certain products.", "termsOfServicePrimaryText": "Terms of Service", "tokenRowUnknownToken": "Unknown To<PERSON>", "transactionStatusDetailActionButtonClose": "Close", "transactionsAppInteraction": "App Interaction", "transactionsError": "Error", "transactionsFailed": "Failed", "transactionsFrom": "From", "transactionsNoActivity": "No activity", "transactionsReceived": "Received", "transactionsReceivedInterpolated": "Received {{amount}} SOL", "transactionsSending": "Sending...", "transactionsSent": "<PERSON><PERSON>", "transactionsSwapOn": "Swap on", "transactionsSentInterpolated": "Sent {{amount}} SOL", "transactionsStaked": "Staked", "transactionsSuccess": "Success", "transactionsTo": "To", "transactionsTokenSwap": "To<PERSON>", "transactionsUnstaked": "Unstaked", "transactionsWaitingForConfirmation": "Waiting for confirmation", "transactionsWithdrawStake": "Withdraw Stake", "unlockActionButtonUnlock": "Unlock", "unlockEnterPassword": "Enter your password", "unlockErrorIncorrectPassword": "Incorrect password", "unlockErrorSomethingWentWrong": "Something went wrong, please try again later", "unlockForgotPassword": "Forgot password?", "unlockPassword": "Password", "validationUtilsPasswordIsRequired": "Password is required", "validationUtilsPasswordLength": "Password must be 8 characters long", "validationUtilsPasswordsDontMatch": "Passwords don't match", "validationUtilsPasswordCantBeSame": "You can't use your old password", "validatorCardCommission": "Commission", "validatorCardTotalStake": "Total Stake", "validatorCardNumberOfDelegators": "# of Delegators", "validatorListActionButtonCancel": "Cancel", "validatorListChooseAValidator": "Choose a Validator", "validatorListErrorFetching": "There was a problem fetching validators:", "validatorListNoResults": "No Results", "validatorListReload": "Reload", "validatorListSearch": "Search", "validatorViewActionButtonClose": "Close", "validatorViewActionButtonStake": "Stake", "validatorViewEdit": "Edit", "validatorViewErrorFetching": "Could not fetch validators.", "validatorViewInsufficientBalance": "Insufficient balance", "validatorViewMax": "Max", "validatorViewPrimaryText": "Start Staking", "validatorViewSecondaryTextInterpolated": "Choose how much SOL you'd like to <1></1> stake with this validator. <3>Learn more</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL required to stake", "validatorViewValidator": "Validator", "walletMenuItemsAddConnectWallet": "Add / Connect Wallet", "walletMenuItemsBridgeAssets": "Bridge Assets", "walletMenuItemsHelpAndSupport": "Help & Support", "walletMenuItemsLockWallet": "Lock Wallet", "walletMenuItemsResetSecretPhrase": "Reset Secret Phrase", "walletMenuItemsShowMoreAccounts": "Show {{count}} more...", "walletMenuItemsHideAccounts": "Hide accounts", "whatsNewOverlayActionButtonClose": "Close", "whatsNewOverlayNew": "New!", "whatsNewOverlayv1ActionGetAppNow": "Get the app now", "whatsNewOverlayv1PrimaryText": "Phantom for iOS is now available!", "whatsNewOverlayv1ScanWithCamera": "Scan with your iPhone camera", "whatsNewOverlayv1SecondaryText": "We're excited to announce that Phantom for iOS is now available in the app store! Experience the power of Phantom in your pocket!", "networkErrorTitle": "Network Error", "networkError": "Unfortunately we can't access the network. Please try again later.", "networkRetry": "Retry", "authenticationUnlockPhantom": "Unlock Phantom", "errorAndOfflineSomethingWentWrong": "Something went wrong", "errorAndOfflineSomethingWentWrongTryAgain": "Please try again.", "errorAndOfflineUnableToFetchAssets": "We were unable to fetch assets. Please try again later.", "errorAndOfflineUnableToFetchCollectibles": "We were unable to fetch collectibles. Please try again later.", "errorAndOfflineUnableToFetchSwap": "We were unable to fetch swap info. Please try again later.", "errorAndOfflineUnableToFetchTransactionHistory": "We were unable to fetch transaction history. Please try again later.", "swapReviewError": "Something went wrong while reviewing your order, please try again.", "sendSelectToken": "Select Token", "swapBalance": "Balance:", "swapTitle": "S<PERSON>p <PERSON>", "swapSelectToken": "Select Token", "aboutPrivacyPolicy": "Privacy Policy", "aboutVersion": "Version {{version}}", "aboutVisitWebsite": "Visit Website", "transactionsFromInterpolated": "From: {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "Today", "transactionsToInterpolated": "To: {{to}}", "transactionsYesterday": "Yesterday", "addEditTokenSuccessMessage": "Associated token account created", "addEditTokenFailureMessage": "There was a problem creating an associated token account. Please try again.", "addEditTokenLoadingMessage": "We're creating an associated token account", "addEditTokenSuccessTitle": "Token successfully added", "addEditTokenFailureTitle": "Adding token failed", "addEditTokenLoadingTitle": "Adding token", "addEditTokenAlreadyAdded": "You already have this token", "addEditTokenContinue": "Continue", "addEditTokenPaste": "Paste", "addEditTokenRequired": "Required", "addEditTokenViewTransaction": "View transaction", "addEditTokenMintAddressError": "Invalid or not supported address", "addEditTokenNameError": "Letters, numbers, underscores, hyphens and spaces only", "addEditTokenSymbolError": "Letters only", "addEditAddressAdd": "Add address", "addEditAddressCancel": "Cancel", "addEditAddressDelete": "Delete address", "addEditAddressDeleteTitle": "Are you sure you want to delete this address?", "addEditAddressPaste": "Paste", "addEditAddressSave": "Save address", "dAppBrowserComingSoon": "dApp Browser coming soon!", "dAppBrowserSearchPlaceholder": "Search or enter website", "dAppBrowserFavorites": "Favorites", "dAppBrowserTrustedApps": "Recently Connected", "dAppBrowserFavoritesDescription": "Your favorites will be shown here", "dAppBrowserEmptyScreenDescription": "Type a URL or search to access your favorite Solana apps", "dAppBrowserBlocklistScreenTitle": "{{origin}} is blocked! ", "dAppBrowserBlocklistScreenDescription": {"part1": "<PERSON> believes this website is malicious and unsafe to use.", "part2": "This site has been flagged as part of a community-maintained database of known phishing websites and scams. If you believe the site has been flagged in error, please file an issue."}, "dAppBrowserBlocklistScreenIgnoreButton": "Ignore warning, show anyway", "depositAssetListSuggestions": "Suggestions", "depositUndefinedToken": "Sorry, can't deposit this token", "onboardingImportRecoveryPhraseDetails": "Details", "onboardingCreateRecoveryPhraseVerifyTitle": "Written the Secret Recovery Phrase down?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Without the secret recovery phrase you will not be able to access your key or any assets associated with it.", "onboardingCreateRecoveryPhraseVerifyYes": "Yes", "onboardingCreateRecoveryPhraseErrorTitle": "Error", "onboardingCreateRecoveryPhraseErrorSubtitle": "We were unsuccessful in generating an account, please try again.", "onboardingDoneDescription": "You can now fully enjoy your wallet.", "onboardingDoneGetStarted": "Get Started", "onboardingImportAccountsEmptyResult": "No accounts found", "onboardingImportAccountsWalletName": "Wallet {{walletIndex}}", "onboardingImportRecoveryPhraseLessThanTwelve": "Phrase needs to be at least 12 words.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Phrase needs to be exactly 12 or 24 words.", "onboardingImportRecoveryPhraseWrongWord": "Incorrect words: {{ words }}.", "onboardingProtectTitle": "Protect your wallet", "onboardingProtectDescription": "Adding a biometric security will ensure that you are the only one that can access your wallet.", "onboardingProtectButtonHeadlineDevice": "<PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Fingerprint", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "Use {{ authType }} Authentication", "onboardingProtectError": "Something went wrong while authenticating, please try again", "onboardingProtectRemoveAuth": "Disable authentication", "onboardingProtectRemoveAuthDescription": "Are you sure you want to disable authentication?", "onboardingProtectNext": "Next", "onboardingWelcomeTitlePhantom": "Phantom is a friendly\nSolana wallet built for\nDeFi & NFTs", "onboardingWelcomeTitle": "A friendly Solana\nwallet built for\nDeFi & NFTs", "onboardingWelcomeCreateWallet": "Create a new wallet", "onboardingWelcomeAlreadyHaveWallet": "I already have a wallet", "onboardingSlide1Title": "Non-Custodial", "onboardingSlide1Description": "We <1>never have access</1> to any of your data or funds. Ever.", "onboardingSlide2Title": "A home for your NFTs", "onboardingSlide2Description": "We've taken special care to make sure your <1>NFTs</1> look great!", "onboardingSlide3Title": "Stake & Swap tokens", "onboardingSlide3Description": "Use our swapper to <1>safely</1> swap tokens at the <4>best prices</4>, instantly.", "onboardingSlide4Title": "Use dApps", "onboardingSlide4Description": "Explore the world of <1>blockchain applications</1> built on Solana.", "requireAuth": "Require authentication", "requireAuthImmediately": "Immediately", "sendEnterAmount": "Enter Amount", "sendShowLogs": "Show Error Logs", "sendHideLogs": "<PERSON>de Error <PERSON>", "sendGoBack": "Go Back", "sendTransactionSuccess": "Your tokens were successfully sent to", "sendInputPlaceholder": "Name or address", "sendRecentlyUsedAddressLabel": "Used {{formattedTimestamp}} ago", "sendRecipientAddress": "Recipient's address", "sendTokenInterpolated": "Send {{tokenSymbol}}", "sendPaste": "Paste", "sendPasteFromClipboard": "Paste from clipboard", "sendScanQR": "Scan QR Code", "sendTo": "To:", "sendCameraAccess": "Camera Access", "sendCameraAccessSubtitle": "To scan a QR code, camera access needs to be enabled. Would you like to open Settings now?", "sendCancel": "Cancel", "sendSettings": "Settings", "sendOK": "OK", "invalidQRCode": "This QR code is not valid.", "sendInvalidQRCode": "This QR code is not a valid address", "sendInvalidQRCodeSubtitle": "Try again or with another QR code.", "sendInvalidQRCodeSplToken": "Invalid token in QR code", "sendInvalidQRCodeSplTokenSubtitle": "This QR code contains a token that you don't own or we can't identify it.", "sendScanAddressToSend": "Scan {{tokenSymbol}} address to send funds", "sendScanAddressToSendCollectible": "Scan SOL address to send collectible", "sendSummary": "Summary", "sendUndefinedToken": "Sorry, can't send this token", "sendNoTokens": "No tokens available", "settingsAbout": "About Phantom", "settingsCancel": "Cancel", "settingsConfirm": "Yes", "settingsEdit": "Edit", "settingsEditWallet": "Edit <PERSON>", "settingsPrompt": "Are you sure you want to continue?", "settingsShowPrivateKey": "Tap to reveal your private key", "settingsShowRecoveryPhrase": "Tap to reveal your secret phrase", "settingsMakeSureNoOneIsWatching": "Make sure no one is watching your screen", "settingsSecurity": "Device Security", "settingsSubmitBetaFeedback": "Submit Beta Feedback", "settingsWalletAddress": "Wallet Address", "settingsWalletNamePrimary": "Wallet Name", "settingsWalletNameSecondary": "Change your wallet's name", "settingsYourAccounts": "Your Accounts", "settingsNotifications": "Notifications", "settingsNotificationPreferences": "Notification Preferences", "pushNotificationsPreferencesAllowNotifications": "Allow Notifications", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON>", "pushNotificationsPreferencesSentTokensDescription": "Outbound transfers of tokens and NFTs", "pushNotificationsPreferencesReceivedTokens": "Received <PERSON>s", "pushNotificationsPreferencesReceivedTokensDescription": "Inbound transfers of tokens and NFTs", "pushNotificationsPreferencesDexSwap": "Swaps", "pushNotificationsPreferencesDexSwapDescription": "Swaps on recognized applications", "pushNotificationsPreferencesOtherBalanceChanges": "Other Balance Changes", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Other multi-token transactions that impact your balance", "pushNotificationsPreferencesPhantomMarketing": "Updates From Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Feature announcements and general updates", "pushNotificationsPreferencesDescription": "These settings control push notifications on Solana for this active wallet. Each wallet has their own notification settings. To turn off all Phantom push notifications, go to your <1>device settings</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Unable to sync notification preferences.", "addAccountHardwareWalletComingSoon": "Coming Soon", "stakeAmount": "Amount", "stakeAmountNext": "Next", "stakeAmountBalance": "Balance", "stakeReview": "Review", "stakeReviewAccount": "Account", "stakeReviewCommissionFee": "Commission Fee", "stakeReviewConfirm": "Confirm", "stakeReviewValidator": "Validator", "swapTooltipGotIt": "Got it", "swapSetSlippageContinue": "Continue", "swapSetSlippageWarning": "You may receive {{slippage}}% less with the level of slippage", "swapTabInsufficientFunds": "Insufficient funds", "swapConfirmationTryAgain": "Try again", "swapConfirmationGoBack": "Go back", "unwrapWrappedSolClose": "Close", "unwrapWrappedSolError": "Unwrapping failed", "unwrapWrappedSolLoading": "Unwrapping...", "unwrapWrappedSolSuccess": "Unwrapped", "unwrapWrappedSolViewTransaction": "View Transaction", "dappApprovePopupSignMessage": "Sign Message", "solanaPayFrom": "From", "solanaPayMessage": "Message", "solanaPayNetworkFee": "Network Fee", "solanaPayFree": "Free", "solanaPayPay": "Pay", "solanaPayPayNow": "Pay Now", "solanaPaySent": "Sent!", "solanaPayTokensSent": "Your tokens were successfully sent to", "solanaPayViewTransaction": "View my transaction", "solanaPayTransactionFailed": "Transaction Failed", "solanaPayApprove": "Approve", "dappApproveConnectViewAccount": "View your Solana account", "deepLinkInvalidLink": "Invalid link", "deepLinkInvalidSplTokenSubtitle": "This contains a token that you don't own or we can't identify it.", "walletAvatarShowAllAccounts": "Show all accounts", "pushNotificationsGetInstantUpdates": "Get instant updates", "pushNotificationsEnablePushNotifications": "Enable push notifications about completed transfers, swaps and announcements", "pushNotificationsEnable": "Enable", "pushNotificationsNotNow": "Not now", "onboardingAgreeToTermsOfServiceInterpolated": "I agree to the <1>Terms of Service</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, I saved it somewhere", "onboardingCreateNewWallet": "Create New Wallet", "onboardingErrorInvalidSecretRecoveryPhrase": "Invalid secret recovery phrase", "onboardingFinished": "You're all done!", "onboardingImportAccounts": "Import Accounts", "onboardingImportAccountsLastUsed": "Used {{formattedTimestamp}} ago", "onboardingImportAccountsNeverUsed": "Never Used", "onboardingImportAccountsDescription": "Choose wallet accounts to import", "onboardingImportSecretRecoveryPhrase": "Import secret recovery phrase", "onboardingImportSelectedAccounts": "Import Selected Accounts", "onboardingRestoreExistingWallet": "Restore an existing wallet with your 12 or 24-word secret recovery phrase", "onboardingShowUnusedAccounts": "Show Unused Accounts", "onboardingShowMoreAccounts": "Show More Accounts", "onboardingHideUnusedAccounts": "Hide Unused Accounts", "onboardingSecretRecoveryPhrase": "Secret Recovery Phrase", "onboardingSelectAccounts": "Select Your Accounts", "onboardingStoreSecretRecoveryPhraseReminder": "This is the only way you will be able to recover your account. Please store it somewhere safe!", "timeUnitMinute": "minute", "timeUnitMinutes": "minutes", "timeUnitHour": "hour", "timeUnitHours": "hours", "espDexSwap": "You exchanged {{downTokensTextFragment}} for {{upTokensTextFragment}} on {{dAppName}}", "espNFTBid": "You bid {{downTokensTextFragment}} for {{upTokensTextFragment}} on {{dAppName}}", "espNFTBuy": "You bought {{nftName}} for {{downTokensTextFragment}} on {{dAppName}}", "espNFTCancelBid": "You cancelled a bid and received {{upTokensTextFragment}} on {{dAppName}}", "espNFTList": "You listed {{downTokensTextFragment}} on {{dAppName}}", "espNFTUnlist": "You unlisted {{upTokensTextFragment}} on {{dAppName}}", "espTokenReceive": "You received {{upTokensTextFragment}}", "espTokenSend": "You sent {{downTokensTextFragment}}", "espTokenTextFragment": "{{token1}} and {{token2}}", "espTransactionBalanceChange": "You exchanged {{downTokensTextFragment}} for {{upTokensTextFragment}}", "espUnknown": "UNKNOWN", "espUnknownNFT": "unknown NFT"}