import { test, expect } from '../base/TestBase';
import { ENV } from "../base/env";
import * as path from 'path';

test.describe('Buy and Sell Token tests', () => {
  const screenshotDir = path.join(process.cwd(), 'screenshots');

  test("Buy and Sell token with market order", async ({ page, tokenDetailPage }) => {
    try {
      const tokenUrl = ENV.TOKEN_URL;
      const buyAmount = "0.0001"; // Số lượng token nhỏ để test
      const buySlippage = "22";  // 22% slippage
      const buyPriorityFee = "0.00001"; // Priority fee nhỏ
      console.log(`Buy Parameters: Amount=${buyAmount}, Slippage=${buySlippage}%, Priority Fee=${buyPriorityFee}`);
      // Mua token
      await tokenDetailPage.buyToken(tokenUrl, buyAmount, buySlippage, buyPriorityFee);
      console.log(`Buy transaction completed successfully`);

      const sellAmount = "100%";
      const sellSlippage = "10";   // 10% slippage
      const sellPriorityFee = "0.00001"; // Priority fee nhỏ

      console.log(`Sell Parameters: Amount=${sellAmount}, Slippage=${sellSlippage}%, Priority Fee=${sellPriorityFee}`);

      // Bán token (đã bao gồm xử lý popup, chờ đợi và xác minh kết quả)
      await tokenDetailPage.sellToken(tokenUrl, sellAmount, sellSlippage, sellPriorityFee);

      console.log("\n==== COMPLETE BUY-SELL FLOW TEST COMPLETED ====\n");
    } catch (error) {
      console.error("\n!!! BUY-SELL FLOW TEST FAILED !!!");
      console.error("Error details:", error instanceof Error ? error.message : "Unknown error occurred");

      // Chụp màn hình lúc lỗi (nếu trang vẫn còn mở)
      try {
        const errorScreenshotPath = path.join(screenshotDir, `buy-sell-flow-error-${Date.now()}.png`);
        await page.screenshot({ path: errorScreenshotPath });
        console.error(`Error screenshot saved to ${errorScreenshotPath}`);
      } catch (screenshotError) {
        console.error("Could not take screenshot: Page may be closed");
      }
      throw error;
    }
  });

  test("Kiểm tra hiển thị transaction section và tải thêm giao dịch", async ({ page, tokenDetailPage }) => {
    try {
      // 1. Truy cập trang token
      const tokenUrl = ENV.TOKEN_URL;
      await tokenDetailPage.navigateToTokenPage(tokenUrl);
      console.log(`✅ Đã truy cập trang token: ${tokenUrl}`);

      // Đợi trang load hoàn tất
      await page.waitForLoadState('networkidle');

      // 2. Sử dụng phương thức tổng hợp để kiểm tra khu vực giao dịch
      await tokenDetailPage.verifyTransactionSectionWithScroll(screenshotDir);

    } catch (error) {
      console.error("\n!!! KIỂM TRA TRANSACTION SECTION THẤT BẠI !!!");
      console.error("Chi tiết lỗi:", error instanceof Error ? error.message : "Lỗi không xác định");

      // Chụp ảnh lỗi
      try {
        const errorScreenshotPath = path.join(screenshotDir, `transaction-section-error-${Date.now()}.png`);
        await page.screenshot({ path: errorScreenshotPath });
        console.error(`Đã lưu ảnh lỗi: ${errorScreenshotPath}`);
      } catch (screenshotError) {
        console.error("Không thể chụp ảnh lỗi");
      }

      throw error;
    }
  });

  test('Kiểm tra thông tin header của token hiển thị chính xác', async ({ page, tokenDetailPage }) => {
    try {
      // 1. Truy cập trang token cụ thể
      const tokenUrl = ENV.TOKEN_URL;
      console.log(`Truy cập trang token: ${tokenUrl}`);
      await tokenDetailPage.navigateToTokenPage(tokenUrl);

      // 2. Đợi trang tải hoàn toàn
      await page.waitForLoadState('domcontentloaded');
      await page.waitForTimeout(5000); // Thêm thời gian chờ để đảm bảo tất cả các phần tử được tải

      // 3. Sử dụng phương thức tổng hợp để kiểm tra thông tin header token
      await tokenDetailPage.verifyTokenHeaderInfo(screenshotDir);

    } catch (error) {
      console.error('\n!!! KIỂM TRA THÔNG TIN HEADER TOKEN THẤT BẠI !!!\n');
      console.error('Chi tiết lỗi:', error instanceof Error ? error.message : 'Lỗi không xác định');

      // Chụp ảnh lỗi
      try {
        const errorScreenshotPath = path.join(screenshotDir, `token-header-error-${Date.now()}.png`);
        await page.screenshot({
          path: errorScreenshotPath,
          fullPage: true
        });
        console.error(`Đã lưu ảnh lỗi: ${errorScreenshotPath}`);
      } catch (screenshotError) {
        console.error("Không thể chụp ảnh lỗi");
      }

      throw error;
    }
  });
});
