import{b as P}from"./chunk-SD2LXVLD.js";import{Bd as H,Pa as _,_ as E}from"./chunk-MZZEJ42N.js";import{m as y}from"./chunk-56SJOU6P.js";import{o as w}from"./chunk-ALUTR72U.js";import{I as L}from"./chunk-L3A2KHJO.js";import{a as C}from"./chunk-7X4NV6OJ.js";import{f as I,h as i,n as a}from"./chunk-3KENBVE7.js";i();a();i();a();var r=I(C());i();a();var O=t=>t.reduce((o,n)=>{let e=o;for(let c of n.accounts){let s=c.chain.name.toLowerCase();if(o[s]?.balance&&o[s]?.history)continue;let l="amount"in c?parseFloat(c.amount)>0:!1,u=c.hasAccountActivity;if(!o[s]){e={...e,[s]:{balance:l,history:u}};continue}e={...e,[s]:{...e[s],balance:l||e[s]?.balance,history:u||e[s]?.history}}}return e},{});i();a();var v=I(C()),U=t=>{let o=H(),n=(0,v.useCallback)(e=>o.includes(_.getChainID(e.chain.id)),[o]);return(0,v.useMemo)(()=>t?t.map(e=>e.status==="undiscovered"?{...e,accounts:e.accounts.filter(c=>n(c))}:{...e,accounts:e.accounts.filter(c=>n(c)&&c.hasAccountActivity)}):[],[n,t])};i();a();var b=I(C()),M=t=>{let[o,n]=(0,b.useState)(null);return(0,b.useEffect)(()=>{if(t.length){let e=new E(t);return n(e),()=>{n(null),e.cleanup()}}},[t]),o};var $=t=>(0,r.useMemo)(()=>t.reduce((o,n,e)=>(o[e]=e===0||n.isSelectedByDefault,o),{}),[t]),ee=(t,o)=>(0,r.useMemo)(()=>t?.reduce((n,e,c)=>o[c]?[...n,e]:n,[])??[],[t,o]),te=(t,o)=>{(0,r.useEffect)(()=>{o(n=>{let{length:e}=Object.keys(t),c={};for(let s=0;s<e;s++)c[s]=n[s]===void 0?t[s]:n[s];return c})},[t,o])},oe=3e3,ne=(t,o)=>{let{t:n}=y();return(0,r.useEffect)(()=>{let e;if(t){let{pages:c}=t,s=c.length>1,l=c[c.length-1].length===0;s&&l&&(o(n("onboardingSelectAccountsFindMoreNoneFound")),e=setTimeout(()=>{o(n("onboardingSelectAccountsFindMoreAccounts"))},oe))}return()=>{e&&clearTimeout(e)}},[t])},ce=({activeAccountsNumber:t,allAccountsNumber:o,hasDiscoveryFailed:n})=>{let{t:e}=y(),c=t>0&&!n;return(0,r.useMemo)(()=>o===0?e("onboardingImportImportingFindingAccounts"):t===0?e("onboardingImportAccountsEmptyResult"):o===1?c?e("onboardingImportAccountsFoundAccounts_one",{numberOfWallets:t}):e("onboardingImportAccountsFoundAccountsNoActivity_one",{numberOfWallets:o}):o>1?c?e("onboardingImportAccountsFoundAccounts_other",{numberOfWallets:t}):e("onboardingImportAccountsFoundAccountsNoActivity_other",{numberOfWallets:o}):"",[t,o,c,e])},W={extension:7,ios:7,android:7,web:7},se=({seed:t,navigationCallback:o,storageCallback:n,enabledAddressTypes:e,enabledChains:c})=>{let{t:s}=y(),l=s("onboardingSelectAccountsFindMoreAccounts"),[u,A]=(0,r.useState)({}),[S,T]=(0,r.useState)(!1),[B,R]=(0,r.useState)(!1),[k,K]=(0,r.useState)(l),Q=M(t),{data:D,status:p,fetchNextPage:N,isFetchingNextPage:j}=P({batchSize:W[L],hdWallet:Q,enabledAddressTypes:e,enabledChains:c}),g=(0,r.useMemo)(()=>D?.pages.flat()??[],[D]),x=U(g),q=$(x),m=ee(g,u),G=ce({activeAccountsNumber:x.length,allAccountsNumber:g.length,hasDiscoveryFailed:g[0]?.status==="undiscovered"});ne(D,K),te(q,A);let Y=(0,r.useCallback)(()=>R(!0),[]),V=(0,r.useCallback)(h=>{if(p!=="success")return;let d={...u};d[h]=!d[h],A(d)},[p,u,A]),Z=(0,r.useCallback)(()=>{p==="success"&&A(h=>{let d={...h},X=Object.values(d).every(Boolean);return Object.keys(d).forEach((ae,J)=>{d[J]=!X}),d})},[p,A]),z=(0,r.useCallback)(async()=>{S||(T(!0),await w(0),await n(m,O(m)),T(!1),o(O(m)))},[S,m,o,n]),F={expanded:B,findMoreAccountsButtonText:k,isFetchingMoreAccounts:j,isImporting:S,renderedPages:x,selectedAccounts:u,subtitle:G,findMoreAccounts:N,onExpand:Y,onImport:z,onSelect:V,onSelectAll:Z};return p!=="success"?{status:"loading",isImportButtonDisabled:!0,...F}:{status:"success",isImportButtonDisabled:m.length===0,...F}};i();a();export{W as a,se as b};
//# sourceMappingURL=chunk-MTQZ2G7K.js.map
