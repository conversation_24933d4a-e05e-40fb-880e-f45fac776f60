import{a as tr}from"./chunk-4P36KWOF.js";import{a as Ae}from"./chunk-7X4NV6OJ.js";import{c as xe,f as Q,h as x,n as _}from"./chunk-3KENBVE7.js";var Oe=xe((dt,mt)=>{x();_();(function(t,e){typeof dt=="object"&&typeof mt<"u"?mt.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self).dayjs=e()})(dt,function(){"use strict";var t=1e3,e=6e4,r=36e5,n="millisecond",i="second",u="minute",l="hour",y="day",o="week",a="month",w="quarter",M="year",b="date",O="Invalid Date",E=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,A=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,C={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(h){var p=["th","st","nd","rd"],g=h%100;return"["+h+(p[(g-20)%10]||p[g]||p[0])+"]"}},P=function(h,p,g){var f=String(h);return!f||f.length>=p?h:""+Array(p+1-f.length).join(g)+h},N={s:P,z:function(h){var p=-h.utcOffset(),g=Math.abs(p),f=Math.floor(g/60),d=g%60;return(p<=0?"+":"-")+P(f,2,"0")+":"+P(d,2,"0")},m:function h(p,g){if(p.date()<g.date())return-h(g,p);var f=12*(g.year()-p.year())+(g.month()-p.month()),d=p.clone().add(f,a),S=g-d<0,T=p.clone().add(f+(S?-1:1),a);return+(-(f+(g-d)/(S?d-T:T-d))||0)},a:function(h){return h<0?Math.ceil(h)||0:Math.floor(h)},p:function(h){return{M:a,y:M,w:o,d:y,D:b,h:l,m:u,s:i,ms:n,Q:w}[h]||String(h||"").toLowerCase().replace(/s$/,"")},u:function(h){return h===void 0}},V="en",z={};z[V]=C;var k="$isDayjsObject",D=function(h){return h instanceof m||!(!h||!h[k])},R=function h(p,g,f){var d;if(!p)return V;if(typeof p=="string"){var S=p.toLowerCase();z[S]&&(d=S),g&&(z[S]=g,d=S);var T=p.split("-");if(!d&&T.length>1)return h(T[0])}else{var $=p.name;z[$]=p,d=$}return!f&&d&&(V=d),d||!f&&V},s=function(h,p){if(D(h))return h.clone();var g=typeof p=="object"?p:{};return g.date=h,g.args=arguments,new m(g)},c=N;c.l=R,c.i=D,c.w=function(h,p){return s(h,{locale:p.$L,utc:p.$u,x:p.$x,$offset:p.$offset})};var m=function(){function h(g){this.$L=R(g.locale,null,!0),this.parse(g),this.$x=this.$x||g.x||{},this[k]=!0}var p=h.prototype;return p.parse=function(g){this.$d=function(f){var d=f.date,S=f.utc;if(d===null)return new Date(NaN);if(c.u(d))return new Date;if(d instanceof Date)return new Date(d);if(typeof d=="string"&&!/Z$/i.test(d)){var T=d.match(E);if(T){var $=T[2]-1||0,I=(T[7]||"0").substring(0,3);return S?new Date(Date.UTC(T[1],$,T[3]||1,T[4]||0,T[5]||0,T[6]||0,I)):new Date(T[1],$,T[3]||1,T[4]||0,T[5]||0,T[6]||0,I)}}return new Date(d)}(g),this.init()},p.init=function(){var g=this.$d;this.$y=g.getFullYear(),this.$M=g.getMonth(),this.$D=g.getDate(),this.$W=g.getDay(),this.$H=g.getHours(),this.$m=g.getMinutes(),this.$s=g.getSeconds(),this.$ms=g.getMilliseconds()},p.$utils=function(){return c},p.isValid=function(){return this.$d.toString()!==O},p.isSame=function(g,f){var d=s(g);return this.startOf(f)<=d&&d<=this.endOf(f)},p.isAfter=function(g,f){return s(g)<this.startOf(f)},p.isBefore=function(g,f){return this.endOf(f)<s(g)},p.$g=function(g,f,d){return c.u(g)?this[f]:this.set(d,g)},p.unix=function(){return Math.floor(this.valueOf()/1e3)},p.valueOf=function(){return this.$d.getTime()},p.startOf=function(g,f){var d=this,S=!!c.u(f)||f,T=c.p(g),$=function(G,Z){var oe=c.w(d.$u?Date.UTC(d.$y,Z,G):new Date(d.$y,Z,G),d);return S?oe:oe.endOf(y)},I=function(G,Z){return c.w(d.toDate()[G].apply(d.toDate("s"),(S?[0,0,0,0]:[23,59,59,999]).slice(Z)),d)},L=this.$W,B=this.$M,Y=this.$D,K="set"+(this.$u?"UTC":"");switch(T){case M:return S?$(1,0):$(31,11);case a:return S?$(1,B):$(0,B+1);case o:var W=this.$locale().weekStart||0,H=(L<W?L+7:L)-W;return $(S?Y-H:Y+(6-H),B);case y:case b:return I(K+"Hours",0);case l:return I(K+"Minutes",1);case u:return I(K+"Seconds",2);case i:return I(K+"Milliseconds",3);default:return this.clone()}},p.endOf=function(g){return this.startOf(g,!1)},p.$set=function(g,f){var d,S=c.p(g),T="set"+(this.$u?"UTC":""),$=(d={},d[y]=T+"Date",d[b]=T+"Date",d[a]=T+"Month",d[M]=T+"FullYear",d[l]=T+"Hours",d[u]=T+"Minutes",d[i]=T+"Seconds",d[n]=T+"Milliseconds",d)[S],I=S===y?this.$D+(f-this.$W):f;if(S===a||S===M){var L=this.clone().set(b,1);L.$d[$](I),L.init(),this.$d=L.set(b,Math.min(this.$D,L.daysInMonth())).$d}else $&&this.$d[$](I);return this.init(),this},p.set=function(g,f){return this.clone().$set(g,f)},p.get=function(g){return this[c.p(g)]()},p.add=function(g,f){var d,S=this;g=Number(g);var T=c.p(f),$=function(B){var Y=s(S);return c.w(Y.date(Y.date()+Math.round(B*g)),S)};if(T===a)return this.set(a,this.$M+g);if(T===M)return this.set(M,this.$y+g);if(T===y)return $(1);if(T===o)return $(7);var I=(d={},d[u]=e,d[l]=r,d[i]=t,d)[T]||1,L=this.$d.getTime()+g*I;return c.w(L,this)},p.subtract=function(g,f){return this.add(-1*g,f)},p.format=function(g){var f=this,d=this.$locale();if(!this.isValid())return d.invalidDate||O;var S=g||"YYYY-MM-DDTHH:mm:ssZ",T=c.z(this),$=this.$H,I=this.$m,L=this.$M,B=d.weekdays,Y=d.months,K=d.meridiem,W=function(Z,oe,ge,re){return Z&&(Z[oe]||Z(f,S))||ge[oe].slice(0,re)},H=function(Z){return c.s($%12||12,Z,"0")},G=K||function(Z,oe,ge){var re=Z<12?"AM":"PM";return ge?re.toLowerCase():re};return S.replace(A,function(Z,oe){return oe||function(ge){switch(ge){case"YY":return String(f.$y).slice(-2);case"YYYY":return c.s(f.$y,4,"0");case"M":return L+1;case"MM":return c.s(L+1,2,"0");case"MMM":return W(d.monthsShort,L,Y,3);case"MMMM":return W(Y,L);case"D":return f.$D;case"DD":return c.s(f.$D,2,"0");case"d":return String(f.$W);case"dd":return W(d.weekdaysMin,f.$W,B,2);case"ddd":return W(d.weekdaysShort,f.$W,B,3);case"dddd":return B[f.$W];case"H":return String($);case"HH":return c.s($,2,"0");case"h":return H(1);case"hh":return H(2);case"a":return G($,I,!0);case"A":return G($,I,!1);case"m":return String(I);case"mm":return c.s(I,2,"0");case"s":return String(f.$s);case"ss":return c.s(f.$s,2,"0");case"SSS":return c.s(f.$ms,3,"0");case"Z":return T}return null}(Z)||T.replace(":","")})},p.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},p.diff=function(g,f,d){var S,T=this,$=c.p(f),I=s(g),L=(I.utcOffset()-this.utcOffset())*e,B=this-I,Y=function(){return c.m(T,I)};switch($){case M:S=Y()/12;break;case a:S=Y();break;case w:S=Y()/3;break;case o:S=(B-L)/6048e5;break;case y:S=(B-L)/864e5;break;case l:S=B/r;break;case u:S=B/e;break;case i:S=B/t;break;default:S=B}return d?S:c.a(S)},p.daysInMonth=function(){return this.endOf(a).$D},p.$locale=function(){return z[this.$L]},p.locale=function(g,f){if(!g)return this.$L;var d=this.clone(),S=R(g,f,!0);return S&&(d.$L=S),d},p.clone=function(){return c.w(this.$d,this)},p.toDate=function(){return new Date(this.valueOf())},p.toJSON=function(){return this.isValid()?this.toISOString():null},p.toISOString=function(){return this.$d.toISOString()},p.toString=function(){return this.$d.toUTCString()},h}(),v=m.prototype;return s.prototype=v,[["$ms",n],["$s",i],["$m",u],["$H",l],["$W",y],["$M",a],["$y",M],["$D",b]].forEach(function(h){v[h[1]]=function(p){return this.$g(p,h[0],h[1])}}),s.extend=function(h,p){return h.$i||(h(p,m,s),h.$i=!0),s},s.locale=R,s.isDayjs=D,s.unix=function(h){return s(1e3*h)},s.en=z[V],s.Ls=z,s.p={},s})});var fr=xe((pt,gt)=>{x();_();(function(t,e){typeof pt=="object"&&typeof gt<"u"?gt.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self).dayjs_plugin_calendar=e()})(pt,function(){"use strict";return function(t,e,r){var n="h:mm A",i={lastDay:"[Yesterday at] "+n,sameDay:"[Today at] "+n,nextDay:"[Tomorrow at] "+n,nextWeek:"dddd [at] "+n,lastWeek:"[Last] dddd [at] "+n,sameElse:"MM/DD/YYYY"};e.prototype.calendar=function(u,l){var y=l||this.$locale().calendar||i,o=r(u||void 0).startOf("d"),a=this.diff(o,"d",!0),w="sameElse",M=a<-6?w:a<-1?"lastWeek":a<0?"lastDay":a<1?"sameDay":a<2?"nextDay":a<7?"nextWeek":w,b=y[M]||i[M];return typeof b=="function"?b.call(this,r()):this.format(b)}}})});var xt=xe((bt,wt)=>{x();_();(function(t,e){typeof bt=="object"&&typeof wt<"u"?wt.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self).dayjs_plugin_duration=e()})(bt,function(){"use strict";var t,e,r=1e3,n=6e4,i=36e5,u=864e5,l=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,y=31536e6,o=2628e6,a=/^(-|\+)?P(?:([-+]?[0-9,.]*)Y)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)W)?(?:([-+]?[0-9,.]*)D)?(?:T(?:([-+]?[0-9,.]*)H)?(?:([-+]?[0-9,.]*)M)?(?:([-+]?[0-9,.]*)S)?)?$/,w={years:y,months:o,days:u,hours:i,minutes:n,seconds:r,milliseconds:1,weeks:6048e5},M=function(z){return z instanceof N},b=function(z,k,D){return new N(z,D,k.$l)},O=function(z){return e.p(z)+"s"},E=function(z){return z<0},A=function(z){return E(z)?Math.ceil(z):Math.floor(z)},C=function(z){return Math.abs(z)},P=function(z,k){return z?E(z)?{negative:!0,format:""+C(z)+k}:{negative:!1,format:""+z+k}:{negative:!1,format:""}},N=function(){function z(D,R,s){var c=this;if(this.$d={},this.$l=s,D===void 0&&(this.$ms=0,this.parseFromMilliseconds()),R)return b(D*w[O(R)],this);if(typeof D=="number")return this.$ms=D,this.parseFromMilliseconds(),this;if(typeof D=="object")return Object.keys(D).forEach(function(h){c.$d[O(h)]=D[h]}),this.calMilliseconds(),this;if(typeof D=="string"){var m=D.match(a);if(m){var v=m.slice(2).map(function(h){return h!=null?Number(h):0});return this.$d.years=v[0],this.$d.months=v[1],this.$d.weeks=v[2],this.$d.days=v[3],this.$d.hours=v[4],this.$d.minutes=v[5],this.$d.seconds=v[6],this.calMilliseconds(),this}}return this}var k=z.prototype;return k.calMilliseconds=function(){var D=this;this.$ms=Object.keys(this.$d).reduce(function(R,s){return R+(D.$d[s]||0)*w[s]},0)},k.parseFromMilliseconds=function(){var D=this.$ms;this.$d.years=A(D/y),D%=y,this.$d.months=A(D/o),D%=o,this.$d.days=A(D/u),D%=u,this.$d.hours=A(D/i),D%=i,this.$d.minutes=A(D/n),D%=n,this.$d.seconds=A(D/r),D%=r,this.$d.milliseconds=D},k.toISOString=function(){var D=P(this.$d.years,"Y"),R=P(this.$d.months,"M"),s=+this.$d.days||0;this.$d.weeks&&(s+=7*this.$d.weeks);var c=P(s,"D"),m=P(this.$d.hours,"H"),v=P(this.$d.minutes,"M"),h=this.$d.seconds||0;this.$d.milliseconds&&(h+=this.$d.milliseconds/1e3,h=Math.round(1e3*h)/1e3);var p=P(h,"S"),g=D.negative||R.negative||c.negative||m.negative||v.negative||p.negative,f=m.format||v.format||p.format?"T":"",d=(g?"-":"")+"P"+D.format+R.format+c.format+f+m.format+v.format+p.format;return d==="P"||d==="-P"?"P0D":d},k.toJSON=function(){return this.toISOString()},k.format=function(D){var R=D||"YYYY-MM-DDTHH:mm:ss",s={Y:this.$d.years,YY:e.s(this.$d.years,2,"0"),YYYY:e.s(this.$d.years,4,"0"),M:this.$d.months,MM:e.s(this.$d.months,2,"0"),D:this.$d.days,DD:e.s(this.$d.days,2,"0"),H:this.$d.hours,HH:e.s(this.$d.hours,2,"0"),m:this.$d.minutes,mm:e.s(this.$d.minutes,2,"0"),s:this.$d.seconds,ss:e.s(this.$d.seconds,2,"0"),SSS:e.s(this.$d.milliseconds,3,"0")};return R.replace(l,function(c,m){return m||String(s[c])})},k.as=function(D){return this.$ms/w[O(D)]},k.get=function(D){var R=this.$ms,s=O(D);return s==="milliseconds"?R%=1e3:R=s==="weeks"?A(R/w[s]):this.$d[s],R||0},k.add=function(D,R,s){var c;return c=R?D*w[O(R)]:M(D)?D.$ms:b(D,this).$ms,b(this.$ms+c*(s?-1:1),this)},k.subtract=function(D,R){return this.add(D,R,!0)},k.locale=function(D){var R=this.clone();return R.$l=D,R},k.clone=function(){return b(this.$ms,this)},k.humanize=function(D){return t().add(this.$ms,"ms").locale(this.$l).fromNow(!D)},k.valueOf=function(){return this.asMilliseconds()},k.milliseconds=function(){return this.get("milliseconds")},k.asMilliseconds=function(){return this.as("milliseconds")},k.seconds=function(){return this.get("seconds")},k.asSeconds=function(){return this.as("seconds")},k.minutes=function(){return this.get("minutes")},k.asMinutes=function(){return this.as("minutes")},k.hours=function(){return this.get("hours")},k.asHours=function(){return this.as("hours")},k.days=function(){return this.get("days")},k.asDays=function(){return this.as("days")},k.weeks=function(){return this.get("weeks")},k.asWeeks=function(){return this.as("weeks")},k.months=function(){return this.get("months")},k.asMonths=function(){return this.as("months")},k.years=function(){return this.get("years")},k.asYears=function(){return this.as("years")},z}(),V=function(z,k,D){return z.add(k.years()*D,"y").add(k.months()*D,"M").add(k.days()*D,"d").add(k.hours()*D,"h").add(k.minutes()*D,"m").add(k.seconds()*D,"s").add(k.milliseconds()*D,"ms")};return function(z,k,D){t=D,e=D().$utils(),D.duration=function(c,m){var v=D.locale();return b(c,{$l:v},m)},D.isDuration=M;var R=k.prototype.add,s=k.prototype.subtract;k.prototype.add=function(c,m){return M(c)?V(this,c,1):R.bind(this)(c,m)},k.prototype.subtract=function(c,m){return M(c)?V(this,c,-1):s.bind(this)(c,m)}}})});var dr=xe((_t,Tt)=>{x();_();(function(t,e){typeof _t=="object"&&typeof Tt<"u"?Tt.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self).dayjs_plugin_relativeTime=e()})(_t,function(){"use strict";return function(t,e,r){t=t||{};var n=e.prototype,i={future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"};function u(y,o,a,w){return n.fromToBase(y,o,a,w)}r.en.relativeTime=i,n.fromToBase=function(y,o,a,w,M){for(var b,O,E,A=a.$locale().relativeTime||i,C=t.thresholds||[{l:"s",r:44,d:"second"},{l:"m",r:89},{l:"mm",r:44,d:"minute"},{l:"h",r:89},{l:"hh",r:21,d:"hour"},{l:"d",r:35},{l:"dd",r:25,d:"day"},{l:"M",r:45},{l:"MM",r:10,d:"month"},{l:"y",r:17},{l:"yy",d:"year"}],P=C.length,N=0;N<P;N+=1){var V=C[N];V.d&&(b=w?r(y).diff(a,V.d,!0):a.diff(y,V.d,!0));var z=(t.rounding||Math.round)(Math.abs(b));if(E=b>0,z<=V.r||!V.r){z<=1&&N>0&&(V=C[N-1]);var k=A[V.l];M&&(z=M(""+z)),O=typeof k=="string"?k.replace("%d",z):k(z,o,V.l,E);break}}if(o)return O;var D=E?A.future:A.past;return typeof D=="function"?D(O):D.replace("%s",O)},n.to=function(y,o){return u(y,o,this,!0)},n.from=function(y,o){return u(y,o,this)};var l=function(y){return y.$u?r.utc():r()};n.toNow=function(y){return this.to(l(this),y)},n.fromNow=function(y){return this.from(l(this),y)}}})});var wr=xe((Mt,vt)=>{x();_();(function(t,e){typeof Mt=="object"&&typeof vt<"u"?vt.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self).dayjs_plugin_isToday=e()})(Mt,function(){"use strict";return function(t,e,r){e.prototype.isToday=function(){var n="YYYY-MM-DD",i=r();return this.format(n)===i.format(n)}}})});var xr=xe((St,Ot)=>{x();_();(function(t,e){typeof St=="object"&&typeof Ot<"u"?Ot.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self).dayjs_plugin_isYesterday=e()})(St,function(){"use strict";return function(t,e,r){e.prototype.isYesterday=function(){var n="YYYY-MM-DD",i=r().subtract(1,"day");return this.format(n)===i.format(n)}}})});var Mn=xe((Na,Tn)=>{x();_();var mn=9007199254740991,Qi="[object Arguments]",es="[object Function]",ts="[object GeneratorFunction]",rs=/^(?:0|[1-9]\d*)$/;function ns(t,e,r){switch(r.length){case 0:return t.call(e);case 1:return t.call(e,r[0]);case 2:return t.call(e,r[0],r[1]);case 3:return t.call(e,r[0],r[1],r[2])}return t.apply(e,r)}function is(t,e){for(var r=-1,n=Array(t);++r<t;)n[r]=e(r);return n}function ss(t,e){return function(r){return t(e(r))}}var tt=Object.prototype,ze=tt.hasOwnProperty,pn=tt.toString,gn=tt.propertyIsEnumerable,os=ss(Object.keys,Object),dn=Math.max,cs=!gn.call({valueOf:1},"valueOf");function us(t,e){var r=ps(t)||ms(t)?is(t.length,String):[],n=r.length,i=!!n;for(var u in t)(e||ze.call(t,u))&&!(i&&(u=="length"||bn(u,n)))&&r.push(u);return r}function yn(t,e,r){var n=t[e];(!(ze.call(t,e)&&xn(n,r))||r===void 0&&!(e in t))&&(t[e]=r)}function as(t){if(!wn(t))return os(t);var e=[];for(var r in Object(t))ze.call(t,r)&&r!="constructor"&&e.push(r);return e}function fs(t,e){return e=dn(e===void 0?t.length-1:e,0),function(){for(var r=arguments,n=-1,i=dn(r.length-e,0),u=Array(i);++n<i;)u[n]=r[e+n];n=-1;for(var l=Array(e+1);++n<e;)l[n]=r[n];return l[e]=u,ns(t,this,l)}}function ls(t,e,r,n){r||(r={});for(var i=-1,u=e.length;++i<u;){var l=e[i],y=n?n(r[l],t[l],l,r,t):void 0;yn(r,l,y===void 0?t[l]:y)}return r}function hs(t){return fs(function(e,r){var n=-1,i=r.length,u=i>1?r[i-1]:void 0,l=i>2?r[2]:void 0;for(u=t.length>3&&typeof u=="function"?(i--,u):void 0,l&&ds(r[0],r[1],l)&&(u=i<3?void 0:u,i=1),e=Object(e);++n<i;){var y=r[n];y&&t(e,y,n,u)}return e})}function bn(t,e){return e=e??mn,!!e&&(typeof t=="number"||rs.test(t))&&t>-1&&t%1==0&&t<e}function ds(t,e,r){if(!_n(r))return!1;var n=typeof e;return(n=="number"?rt(r)&&bn(e,r.length):n=="string"&&e in r)?xn(r[e],t):!1}function wn(t){var e=t&&t.constructor,r=typeof e=="function"&&e.prototype||tt;return t===r}function xn(t,e){return t===e||t!==t&&e!==e}function ms(t){return gs(t)&&ze.call(t,"callee")&&(!gn.call(t,"callee")||pn.call(t)==Qi)}var ps=Array.isArray;function rt(t){return t!=null&&bs(t.length)&&!ys(t)}function gs(t){return ws(t)&&rt(t)}function ys(t){var e=_n(t)?pn.call(t):"";return e==es||e==ts}function bs(t){return typeof t=="number"&&t>-1&&t%1==0&&t<=mn}function _n(t){var e=typeof t;return!!t&&(e=="object"||e=="function")}function ws(t){return!!t&&typeof t=="object"}var xs=hs(function(t,e){if(cs||wn(e)||rt(e)){ls(e,_s(e),t);return}for(var r in e)ze.call(e,r)&&yn(t,r,e[r])});function _s(t){return rt(t)?us(t):as(t)}Tn.exports=xs});var Sn=xe((vn,nt)=>{x();_();(function(t,e){typeof define=="function"&&define.amd?define(e):typeof nt=="object"&&nt.exports?nt.exports=e():t.numeral=e()})(vn,function(){var t,e,r="2.0.6",n={},i={},u={currentLocale:"en",zeroFormat:null,nullFormat:null,defaultFormat:"0,0",scalePercentBy100:!0},l={currentLocale:u.currentLocale,zeroFormat:u.zeroFormat,nullFormat:u.nullFormat,defaultFormat:u.defaultFormat,scalePercentBy100:u.scalePercentBy100};function y(o,a){this._input=o,this._value=a}return t=function(o){var a,w,M,b;if(t.isNumeral(o))a=o.value();else if(o===0||typeof o>"u")a=0;else if(o===null||e.isNaN(o))a=null;else if(typeof o=="string")if(l.zeroFormat&&o===l.zeroFormat)a=0;else if(l.nullFormat&&o===l.nullFormat||!o.replace(/[^0-9]+/g,"").length)a=null;else{for(w in n)if(b=typeof n[w].regexps.unformat=="function"?n[w].regexps.unformat():n[w].regexps.unformat,b&&o.match(b)){M=n[w].unformat;break}M=M||t._.stringToNumber,a=M(o)}else a=Number(o)||null;return new y(o,a)},t.version=r,t.isNumeral=function(o){return o instanceof y},t._=e={numberToFormat:function(o,a,w){var M=i[t.options.currentLocale],b=!1,O=!1,E=0,A="",C=1e12,P=1e9,N=1e6,V=1e3,z="",k=!1,D,R,s,c,m,v,h,p,g,f;if(o=o||0,R=Math.abs(o),t._.includes(a,"(")?(b=!0,a=a.replace(/[\(|\)]/g,"")):(t._.includes(a,"+")||t._.includes(a,"-"))&&(p=t._.includes(a,"+")?a.indexOf("+"):o<0?a.indexOf("-"):-1,a=a.replace(/[\+|\-]/g,"")),t._.includes(a,"a")&&(D=a.match(/a(k|m|b|t)?/),D=D?D[1]:!1,t._.includes(a," a")&&(A=" "),a=a.replace(new RegExp(A+"a[kmbt]?"),""),R>=C&&!D||D==="t"?(A+=M.abbreviations.trillion,o=o/C):R<C&&R>=P&&!D||D==="b"?(A+=M.abbreviations.billion,o=o/P):R<P&&R>=N&&!D||D==="m"?(A+=M.abbreviations.million,o=o/N):(R<N&&R>=V&&!D||D==="k")&&(A+=M.abbreviations.thousand,o=o/V)),t._.includes(a,"[.]")&&(O=!0,a=a.replace("[.]",".")),v=o.toString().split(".")[0],h=a.split(".")[1],g=a.indexOf(","),E=(a.split(".")[0].split(",")[0].match(/0/g)||[]).length,h?(t._.includes(h,"[")?(h=h.replace("]",""),h=h.split("["),z=t._.toFixed(o,h[0].length+h[1].length,w,h[1].length)):z=t._.toFixed(o,h.length,w),v=z.split(".")[0],t._.includes(z,".")?z=M.delimiters.decimal+z.split(".")[1]:z="",O&&Number(z.slice(1))===0&&(z="")):v=t._.toFixed(o,0,w),A&&!D&&Number(v)>=1e3&&A!==M.abbreviations.trillion)switch(v=String(Number(v)/1e3),A){case M.abbreviations.thousand:A=M.abbreviations.million;break;case M.abbreviations.million:A=M.abbreviations.billion;break;case M.abbreviations.billion:A=M.abbreviations.trillion;break}if(t._.includes(v,"-")&&(v=v.slice(1),k=!0),v.length<E)for(var d=E-v.length;d>0;d--)v="0"+v;return g>-1&&(v=v.toString().replace(/(\d)(?=(\d{3})+(?!\d))/g,"$1"+M.delimiters.thousands)),a.indexOf(".")===0&&(v=""),f=v+z+(A||""),b?f=(b&&k?"(":"")+f+(b&&k?")":""):p>=0?f=p===0?(k?"-":"+")+f:f+(k?"-":"+"):k&&(f="-"+f),f},stringToNumber:function(o){var a=i[l.currentLocale],w=o,M={thousand:3,million:6,billion:9,trillion:12},b,O,E,A;if(l.zeroFormat&&o===l.zeroFormat)O=0;else if(l.nullFormat&&o===l.nullFormat||!o.replace(/[^0-9]+/g,"").length)O=null;else{O=1,a.delimiters.decimal!=="."&&(o=o.replace(/\./g,"").replace(a.delimiters.decimal,"."));for(b in M)if(A=new RegExp("[^a-zA-Z]"+a.abbreviations[b]+"(?:\\)|(\\"+a.currency.symbol+")?(?:\\))?)?$"),w.match(A)){O*=Math.pow(10,M[b]);break}O*=(o.split("-").length+Math.min(o.split("(").length-1,o.split(")").length-1))%2?1:-1,o=o.replace(/[^0-9\.]+/g,""),O*=Number(o)}return O},isNaN:function(o){return typeof o=="number"&&isNaN(o)},includes:function(o,a){return o.indexOf(a)!==-1},insert:function(o,a,w){return o.slice(0,w)+a+o.slice(w)},reduce:function(o,a){if(this===null)throw new TypeError("Array.prototype.reduce called on null or undefined");if(typeof a!="function")throw new TypeError(a+" is not a function");var w=Object(o),M=w.length>>>0,b=0,O;if(arguments.length===3)O=arguments[2];else{for(;b<M&&!(b in w);)b++;if(b>=M)throw new TypeError("Reduce of empty array with no initial value");O=w[b++]}for(;b<M;b++)b in w&&(O=a(O,w[b],b,w));return O},multiplier:function(o){var a=o.toString().split(".");return a.length<2?1:Math.pow(10,a[1].length)},correctionFactor:function(){var o=Array.prototype.slice.call(arguments);return o.reduce(function(a,w){var M=e.multiplier(w);return a>M?a:M},1)},toFixed:function(o,a,w,M){var b=o.toString().split("."),O=a-(M||0),E,A,C,P;return b.length===2?E=Math.min(Math.max(b[1].length,O),a):E=O,C=Math.pow(10,E),P=(w(o+"e+"+E)/C).toFixed(E),M>a-E&&(A=new RegExp("\\.?0{1,"+(M-(a-E))+"}$"),P=P.replace(A,"")),P}},t.options=l,t.formats=n,t.locales=i,t.locale=function(o){return o&&(l.currentLocale=o.toLowerCase()),l.currentLocale},t.localeData=function(o){if(!o)return i[l.currentLocale];if(o=o.toLowerCase(),!i[o])throw new Error("Unknown locale : "+o);return i[o]},t.reset=function(){for(var o in u)l[o]=u[o]},t.zeroFormat=function(o){l.zeroFormat=typeof o=="string"?o:null},t.nullFormat=function(o){l.nullFormat=typeof o=="string"?o:null},t.defaultFormat=function(o){l.defaultFormat=typeof o=="string"?o:"0.0"},t.register=function(o,a,w){if(a=a.toLowerCase(),this[o+"s"][a])throw new TypeError(a+" "+o+" already registered.");return this[o+"s"][a]=w,w},t.validate=function(o,a){var w,M,b,O,E,A,C,P;if(typeof o!="string"&&(o+="",console.warn&&console.warn("Numeral.js: Value is not string. It has been co-erced to: ",o)),o=o.trim(),o.match(/^\d+$/))return!0;if(o==="")return!1;try{C=t.localeData(a)}catch{C=t.localeData(t.locale())}return b=C.currency.symbol,E=C.abbreviations,w=C.delimiters.decimal,C.delimiters.thousands==="."?M="\\.":M=C.delimiters.thousands,P=o.match(/^[^\d]+/),P!==null&&(o=o.substr(1),P[0]!==b)||(P=o.match(/[^\d]+$/),P!==null&&(o=o.slice(0,-1),P[0]!==E.thousand&&P[0]!==E.million&&P[0]!==E.billion&&P[0]!==E.trillion))?!1:(A=new RegExp(M+"{2}"),o.match(/[^\d.,]/g)?!1:(O=o.split(w),O.length>2?!1:O.length<2?!!O[0].match(/^\d+.*\d$/)&&!O[0].match(A):O[0].length===1?!!O[0].match(/^\d+$/)&&!O[0].match(A)&&!!O[1].match(/^\d+$/):!!O[0].match(/^\d+.*\d$/)&&!O[0].match(A)&&!!O[1].match(/^\d+$/)))},t.fn=y.prototype={clone:function(){return t(this)},format:function(o,a){var w=this._value,M=o||l.defaultFormat,b,O,E;if(a=a||Math.round,w===0&&l.zeroFormat!==null)O=l.zeroFormat;else if(w===null&&l.nullFormat!==null)O=l.nullFormat;else{for(b in n)if(M.match(n[b].regexps.format)){E=n[b].format;break}E=E||t._.numberToFormat,O=E(w,M,a)}return O},value:function(){return this._value},input:function(){return this._input},set:function(o){return this._value=Number(o),this},add:function(o){var a=e.correctionFactor.call(null,this._value,o);function w(M,b,O,E){return M+Math.round(a*b)}return this._value=e.reduce([this._value,o],w,0)/a,this},subtract:function(o){var a=e.correctionFactor.call(null,this._value,o);function w(M,b,O,E){return M-Math.round(a*b)}return this._value=e.reduce([o],w,Math.round(this._value*a))/a,this},multiply:function(o){function a(w,M,b,O){var E=e.correctionFactor(w,M);return Math.round(w*E)*Math.round(M*E)/Math.round(E*E)}return this._value=e.reduce([this._value,o],a,1),this},divide:function(o){function a(w,M,b,O){var E=e.correctionFactor(w,M);return Math.round(w*E)/Math.round(M*E)}return this._value=e.reduce([this._value,o],a),this},difference:function(o){return Math.abs(t(this._value).subtract(o).value())}},t.register("locale","en",{delimiters:{thousands:",",decimal:"."},abbreviations:{thousand:"k",million:"m",billion:"b",trillion:"t"},ordinal:function(o){var a=o%10;return~~(o%100/10)===1?"th":a===1?"st":a===2?"nd":a===3?"rd":"th"},currency:{symbol:"$"}}),function(){t.register("format","bps",{regexps:{format:/(BPS)/,unformat:/(BPS)/},format:function(o,a,w){var M=t._.includes(a," BPS")?" ":"",b;return o=o*1e4,a=a.replace(/\s?BPS/,""),b=t._.numberToFormat(o,a,w),t._.includes(b,")")?(b=b.split(""),b.splice(-1,0,M+"BPS"),b=b.join("")):b=b+M+"BPS",b},unformat:function(o){return+(t._.stringToNumber(o)*1e-4).toFixed(15)}})}(),function(){var o={base:1e3,suffixes:["B","KB","MB","GB","TB","PB","EB","ZB","YB"]},a={base:1024,suffixes:["B","KiB","MiB","GiB","TiB","PiB","EiB","ZiB","YiB"]},w=o.suffixes.concat(a.suffixes.filter(function(b){return o.suffixes.indexOf(b)<0})),M=w.join("|");M="("+M.replace("B","B(?!PS)")+")",t.register("format","bytes",{regexps:{format:/([0\s]i?b)/,unformat:new RegExp(M)},format:function(b,O,E){var A,C=t._.includes(O,"ib")?a:o,P=t._.includes(O," b")||t._.includes(O," ib")?" ":"",N,V,z;for(O=O.replace(/\s?i?b/,""),N=0;N<=C.suffixes.length;N++)if(V=Math.pow(C.base,N),z=Math.pow(C.base,N+1),b===null||b===0||b>=V&&b<z){P+=C.suffixes[N],V>0&&(b=b/V);break}return A=t._.numberToFormat(b,O,E),A+P},unformat:function(b){var O=t._.stringToNumber(b),E,A;if(O){for(E=o.suffixes.length-1;E>=0;E--){if(t._.includes(b,o.suffixes[E])){A=Math.pow(o.base,E);break}if(t._.includes(b,a.suffixes[E])){A=Math.pow(a.base,E);break}}O*=A||1}return O}})}(),function(){t.register("format","currency",{regexps:{format:/(\$)/},format:function(o,a,w){var M=t.locales[t.options.currentLocale],b={before:a.match(/^([\+|\-|\(|\s|\$]*)/)[0],after:a.match(/([\+|\-|\)|\s|\$]*)$/)[0]},O,E,A;for(a=a.replace(/\s?\$\s?/,""),O=t._.numberToFormat(o,a,w),o>=0?(b.before=b.before.replace(/[\-\(]/,""),b.after=b.after.replace(/[\-\)]/,"")):o<0&&!t._.includes(b.before,"-")&&!t._.includes(b.before,"(")&&(b.before="-"+b.before),A=0;A<b.before.length;A++)switch(E=b.before[A],E){case"$":O=t._.insert(O,M.currency.symbol,A);break;case" ":O=t._.insert(O," ",A+M.currency.symbol.length-1);break}for(A=b.after.length-1;A>=0;A--)switch(E=b.after[A],E){case"$":O=A===b.after.length-1?O+M.currency.symbol:t._.insert(O,M.currency.symbol,-(b.after.length-(1+A)));break;case" ":O=A===b.after.length-1?O+" ":t._.insert(O," ",-(b.after.length-(1+A)+M.currency.symbol.length-1));break}return O}})}(),function(){t.register("format","exponential",{regexps:{format:/(e\+|e-)/,unformat:/(e\+|e-)/},format:function(o,a,w){var M,b=typeof o=="number"&&!t._.isNaN(o)?o.toExponential():"0e+0",O=b.split("e");return a=a.replace(/e[\+|\-]{1}0/,""),M=t._.numberToFormat(Number(O[0]),a,w),M+"e"+O[1]},unformat:function(o){var a=t._.includes(o,"e+")?o.split("e+"):o.split("e-"),w=Number(a[0]),M=Number(a[1]);M=t._.includes(o,"e-")?M*=-1:M;function b(O,E,A,C){var P=t._.correctionFactor(O,E),N=O*P*(E*P)/(P*P);return N}return t._.reduce([w,Math.pow(10,M)],b,1)}})}(),function(){t.register("format","ordinal",{regexps:{format:/(o)/},format:function(o,a,w){var M=t.locales[t.options.currentLocale],b,O=t._.includes(a," o")?" ":"";return a=a.replace(/\s?o/,""),O+=M.ordinal(o),b=t._.numberToFormat(o,a,w),b+O}})}(),function(){t.register("format","percentage",{regexps:{format:/(%)/,unformat:/(%)/},format:function(o,a,w){var M=t._.includes(a," %")?" ":"",b;return t.options.scalePercentBy100&&(o=o*100),a=a.replace(/\s?\%/,""),b=t._.numberToFormat(o,a,w),t._.includes(b,")")?(b=b.split(""),b.splice(-1,0,M+"%"),b=b.join("")):b=b+M+"%",b},unformat:function(o){var a=t._.stringToNumber(o);return t.options.scalePercentBy100?a*.01:a}})}(),function(){t.register("format","time",{regexps:{format:/(:)/,unformat:/(:)/},format:function(o,a,w){var M=Math.floor(o/60/60),b=Math.floor((o-M*60*60)/60),O=Math.round(o-M*60*60-b*60);return M+":"+(b<10?"0"+b:b)+":"+(O<10?"0"+O:O)},unformat:function(o){var a=o.split(":"),w=0;return a.length===3?(w=w+Number(a[0])*60*60,w=w+Number(a[1])*60,w=w+Number(a[2])):a.length===2&&(w=w+Number(a[0])*60,w=w+Number(a[1])),Number(w)}})}(),t})});x();_();var qn=/^-?(?:\d+(?:\.\d*)?|\.\d+)(?:e[+-]?\d+)?$/i,ft=Math.ceil,le=Math.floor,ie="[BigNumber Error] ",rr=ie+"Number primitive has more than 15 significant digits: ",fe=1e14,j=14,lt=9007199254740991,ht=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9,1e10,1e11,1e12,1e13],_e=1e7,ee=1e9;function nr(t){var e,r,n,i=N.prototype={constructor:N,toString:null,valueOf:null},u=new N(1),l=20,y=4,o=-7,a=21,w=-1e7,M=1e7,b=!1,O=1,E=0,A={prefix:"",groupSize:3,secondaryGroupSize:0,groupSeparator:",",decimalSeparator:".",fractionGroupSize:0,fractionGroupSeparator:"\xA0",suffix:""},C="0123456789abcdefghijklmnopqrstuvwxyz",P=!0;function N(s,c){var m,v,h,p,g,f,d,S,T=this;if(!(T instanceof N))return new N(s,c);if(c==null){if(s&&s._isBigNumber===!0){T.s=s.s,!s.c||s.e>M?T.c=T.e=null:s.e<w?T.c=[T.e=0]:(T.e=s.e,T.c=s.c.slice());return}if((f=typeof s=="number")&&s*0==0){if(T.s=1/s<0?(s=-s,-1):1,s===~~s){for(p=0,g=s;g>=10;g/=10,p++);p>M?T.c=T.e=null:(T.e=p,T.c=[s]);return}S=String(s)}else{if(!qn.test(S=String(s)))return n(T,S,f);T.s=S.charCodeAt(0)==45?(S=S.slice(1),-1):1}(p=S.indexOf("."))>-1&&(S=S.replace(".","")),(g=S.search(/e/i))>0?(p<0&&(p=g),p+=+S.slice(g+1),S=S.substring(0,g)):p<0&&(p=S.length)}else{if(q(c,2,C.length,"Base"),c==10&&P)return T=new N(s),D(T,l+T.e+1,y);if(S=String(s),f=typeof s=="number"){if(s*0!=0)return n(T,S,f,c);if(T.s=1/s<0?(S=S.slice(1),-1):1,N.DEBUG&&S.replace(/^0\.0*|\./,"").length>15)throw Error(rr+s)}else T.s=S.charCodeAt(0)===45?(S=S.slice(1),-1):1;for(m=C.slice(0,c),p=g=0,d=S.length;g<d;g++)if(m.indexOf(v=S.charAt(g))<0){if(v=="."){if(g>p){p=d;continue}}else if(!h&&(S==S.toUpperCase()&&(S=S.toLowerCase())||S==S.toLowerCase()&&(S=S.toUpperCase()))){h=!0,g=-1,p=0;continue}return n(T,String(s),f,c)}f=!1,S=r(S,c,10,T.s),(p=S.indexOf("."))>-1?S=S.replace(".",""):p=S.length}for(g=0;S.charCodeAt(g)===48;g++);for(d=S.length;S.charCodeAt(--d)===48;);if(S=S.slice(g,++d)){if(d-=g,f&&N.DEBUG&&d>15&&(s>lt||s!==le(s)))throw Error(rr+T.s*s);if((p=p-g-1)>M)T.c=T.e=null;else if(p<w)T.c=[T.e=0];else{if(T.e=p,T.c=[],g=(p+1)%j,p<0&&(g+=j),g<d){for(g&&T.c.push(+S.slice(0,g)),d-=j;g<d;)T.c.push(+S.slice(g,g+=j));g=j-(S=S.slice(g)).length}else g-=d;for(;g--;S+="0");T.c.push(+S)}}else T.c=[T.e=0]}N.clone=nr,N.ROUND_UP=0,N.ROUND_DOWN=1,N.ROUND_CEIL=2,N.ROUND_FLOOR=3,N.ROUND_HALF_UP=4,N.ROUND_HALF_DOWN=5,N.ROUND_HALF_EVEN=6,N.ROUND_HALF_CEIL=7,N.ROUND_HALF_FLOOR=8,N.EUCLID=9,N.config=N.set=function(s){var c,m;if(s!=null)if(typeof s=="object"){if(s.hasOwnProperty(c="DECIMAL_PLACES")&&(m=s[c],q(m,0,ee,c),l=m),s.hasOwnProperty(c="ROUNDING_MODE")&&(m=s[c],q(m,0,8,c),y=m),s.hasOwnProperty(c="EXPONENTIAL_AT")&&(m=s[c],m&&m.pop?(q(m[0],-ee,0,c),q(m[1],0,ee,c),o=m[0],a=m[1]):(q(m,-ee,ee,c),o=-(a=m<0?-m:m))),s.hasOwnProperty(c="RANGE"))if(m=s[c],m&&m.pop)q(m[0],-ee,-1,c),q(m[1],1,ee,c),w=m[0],M=m[1];else if(q(m,-ee,ee,c),m)w=-(M=m<0?-m:m);else throw Error(ie+c+" cannot be zero: "+m);if(s.hasOwnProperty(c="CRYPTO"))if(m=s[c],m===!!m)if(m)if(typeof self.crypto<"u"&&self.crypto&&(self.crypto.getRandomValues||self.crypto.randomBytes))b=m;else throw b=!m,Error(ie+"crypto unavailable");else b=m;else throw Error(ie+c+" not true or false: "+m);if(s.hasOwnProperty(c="MODULO_MODE")&&(m=s[c],q(m,0,9,c),O=m),s.hasOwnProperty(c="POW_PRECISION")&&(m=s[c],q(m,0,ee,c),E=m),s.hasOwnProperty(c="FORMAT"))if(m=s[c],typeof m=="object")A=m;else throw Error(ie+c+" not an object: "+m);if(s.hasOwnProperty(c="ALPHABET"))if(m=s[c],typeof m=="string"&&!/^.?$|[+\-.\s]|(.).*\1/.test(m))P=m.slice(0,10)=="0123456789",C=m;else throw Error(ie+c+" invalid: "+m)}else throw Error(ie+"Object expected: "+s);return{DECIMAL_PLACES:l,ROUNDING_MODE:y,EXPONENTIAL_AT:[o,a],RANGE:[w,M],CRYPTO:b,MODULO_MODE:O,POW_PRECISION:E,FORMAT:A,ALPHABET:C}},N.isBigNumber=function(s){if(!s||s._isBigNumber!==!0)return!1;if(!N.DEBUG)return!0;var c,m,v=s.c,h=s.e,p=s.s;e:if({}.toString.call(v)=="[object Array]"){if((p===1||p===-1)&&h>=-ee&&h<=ee&&h===le(h)){if(v[0]===0){if(h===0&&v.length===1)return!0;break e}if(c=(h+1)%j,c<1&&(c+=j),String(v[0]).length==c){for(c=0;c<v.length;c++)if(m=v[c],m<0||m>=fe||m!==le(m))break e;if(m!==0)return!0}}}else if(v===null&&h===null&&(p===null||p===1||p===-1))return!0;throw Error(ie+"Invalid BigNumber: "+s)},N.maximum=N.max=function(){return z(arguments,i.lt)},N.minimum=N.min=function(){return z(arguments,i.gt)},N.random=function(){var s=9007199254740992,c=Math.random()*s&2097151?function(){return le(Math.random()*s)}:function(){return(Math.random()*1073741824|0)*8388608+(Math.random()*8388608|0)};return function(m){var v,h,p,g,f,d=0,S=[],T=new N(u);if(m==null?m=l:q(m,0,ee),g=ft(m/j),b)if(self.crypto.getRandomValues){for(v=self.crypto.getRandomValues(new Uint32Array(g*=2));d<g;)f=v[d]*131072+(v[d+1]>>>11),f>=9e15?(h=self.crypto.getRandomValues(new Uint32Array(2)),v[d]=h[0],v[d+1]=h[1]):(S.push(f%1e14),d+=2);d=g/2}else if(self.crypto.randomBytes){for(v=self.crypto.randomBytes(g*=7);d<g;)f=(v[d]&31)*281474976710656+v[d+1]*1099511627776+v[d+2]*4294967296+v[d+3]*16777216+(v[d+4]<<16)+(v[d+5]<<8)+v[d+6],f>=9e15?self.crypto.randomBytes(7).copy(v,d):(S.push(f%1e14),d+=7);d=g/7}else throw b=!1,Error(ie+"crypto unavailable");if(!b)for(;d<g;)f=c(),f<9e15&&(S[d++]=f%1e14);for(g=S[--d],m%=j,g&&m&&(f=ht[j-m],S[d]=le(g/f)*f);S[d]===0;S.pop(),d--);if(d<0)S=[p=0];else{for(p=-1;S[0]===0;S.splice(0,1),p-=j);for(d=1,f=S[0];f>=10;f/=10,d++);d<j&&(p-=j-d)}return T.e=p,T.c=S,T}}(),N.sum=function(){for(var s=1,c=arguments,m=new N(c[0]);s<c.length;)m=m.plus(c[s++]);return m},r=function(){var s="0123456789";function c(m,v,h,p){for(var g,f=[0],d,S=0,T=m.length;S<T;){for(d=f.length;d--;f[d]*=v);for(f[0]+=p.indexOf(m.charAt(S++)),g=0;g<f.length;g++)f[g]>h-1&&(f[g+1]==null&&(f[g+1]=0),f[g+1]+=f[g]/h|0,f[g]%=h)}return f.reverse()}return function(m,v,h,p,g){var f,d,S,T,$,I,L,B,Y=m.indexOf("."),K=l,W=y;for(Y>=0&&(T=E,E=0,m=m.replace(".",""),B=new N(v),I=B.pow(m.length-Y),E=T,B.c=c(ye(ue(I.c),I.e,"0"),10,h,s),B.e=B.c.length),L=c(m,v,h,g?(f=C,s):(f=s,C)),S=T=L.length;L[--T]==0;L.pop());if(!L[0])return f.charAt(0);if(Y<0?--S:(I.c=L,I.e=S,I.s=p,I=e(I,B,K,W,h),L=I.c,$=I.r,S=I.e),d=S+K+1,Y=L[d],T=h/2,$=$||d<0||L[d+1]!=null,$=W<4?(Y!=null||$)&&(W==0||W==(I.s<0?3:2)):Y>T||Y==T&&(W==4||$||W==6&&L[d-1]&1||W==(I.s<0?8:7)),d<1||!L[0])m=$?ye(f.charAt(1),-K,f.charAt(0)):f.charAt(0);else{if(L.length=d,$)for(--h;++L[--d]>h;)L[d]=0,d||(++S,L=[1].concat(L));for(T=L.length;!L[--T];);for(Y=0,m="";Y<=T;m+=f.charAt(L[Y++]));m=ye(m,S,f.charAt(0))}return m}}(),e=function(){function s(v,h,p){var g,f,d,S,T=0,$=v.length,I=h%_e,L=h/_e|0;for(v=v.slice();$--;)d=v[$]%_e,S=v[$]/_e|0,g=L*d+S*I,f=I*d+g%_e*_e+T,T=(f/p|0)+(g/_e|0)+L*S,v[$]=f%p;return T&&(v=[T].concat(v)),v}function c(v,h,p,g){var f,d;if(p!=g)d=p>g?1:-1;else for(f=d=0;f<p;f++)if(v[f]!=h[f]){d=v[f]>h[f]?1:-1;break}return d}function m(v,h,p,g){for(var f=0;p--;)v[p]-=f,f=v[p]<h[p]?1:0,v[p]=f*g+v[p]-h[p];for(;!v[0]&&v.length>1;v.splice(0,1));}return function(v,h,p,g,f){var d,S,T,$,I,L,B,Y,K,W,H,G,Z,oe,ge,re,Ee,ce=v.s==h.s?1:-1,ne=v.c,X=h.c;if(!ne||!ne[0]||!X||!X[0])return new N(!v.s||!h.s||(ne?X&&ne[0]==X[0]:!X)?NaN:ne&&ne[0]==0||!X?ce*0:ce/0);for(Y=new N(ce),K=Y.c=[],S=v.e-h.e,ce=p+S+1,f||(f=fe,S=ae(v.e/j)-ae(h.e/j),ce=ce/j|0),T=0;X[T]==(ne[T]||0);T++);if(X[T]>(ne[T]||0)&&S--,ce<0)K.push(1),$=!0;else{for(oe=ne.length,re=X.length,T=0,ce+=2,I=le(f/(X[0]+1)),I>1&&(X=s(X,I,f),ne=s(ne,I,f),re=X.length,oe=ne.length),Z=re,W=ne.slice(0,re),H=W.length;H<re;W[H++]=0);Ee=X.slice(),Ee=[0].concat(Ee),ge=X[0],X[1]>=f/2&&ge++;do{if(I=0,d=c(X,W,re,H),d<0){if(G=W[0],re!=H&&(G=G*f+(W[1]||0)),I=le(G/ge),I>1)for(I>=f&&(I=f-1),L=s(X,I,f),B=L.length,H=W.length;c(L,W,B,H)==1;)I--,m(L,re<B?Ee:X,B,f),B=L.length,d=1;else I==0&&(d=I=1),L=X.slice(),B=L.length;if(B<H&&(L=[0].concat(L)),m(W,L,H,f),H=W.length,d==-1)for(;c(X,W,re,H)<1;)I++,m(W,re<H?Ee:X,H,f),H=W.length}else d===0&&(I++,W=[0]);K[T++]=I,W[0]?W[H++]=ne[Z]||0:(W=[ne[Z]],H=1)}while((Z++<oe||W[0]!=null)&&ce--);$=W[0]!=null,K[0]||K.splice(0,1)}if(f==fe){for(T=1,ce=K[0];ce>=10;ce/=10,T++);D(Y,p+(Y.e=T+S*j-1)+1,g,$)}else Y.e=S,Y.r=+$;return Y}}();function V(s,c,m,v){var h,p,g,f,d;if(m==null?m=y:q(m,0,8),!s.c)return s.toString();if(h=s.c[0],g=s.e,c==null)d=ue(s.c),d=v==1||v==2&&(g<=o||g>=a)?Be(d,g):ye(d,g,"0");else if(s=D(new N(s),c,m),p=s.e,d=ue(s.c),f=d.length,v==1||v==2&&(c<=p||p<=o)){for(;f<c;d+="0",f++);d=Be(d,p)}else if(c-=g,d=ye(d,p,"0"),p+1>f){if(--c>0)for(d+=".";c--;d+="0");}else if(c+=p-f,c>0)for(p+1==f&&(d+=".");c--;d+="0");return s.s<0&&h?"-"+d:d}function z(s,c){for(var m,v=1,h=new N(s[0]);v<s.length;v++)if(m=new N(s[v]),m.s)c.call(h,m)&&(h=m);else{h=m;break}return h}function k(s,c,m){for(var v=1,h=c.length;!c[--h];c.pop());for(h=c[0];h>=10;h/=10,v++);return(m=v+m*j-1)>M?s.c=s.e=null:m<w?s.c=[s.e=0]:(s.e=m,s.c=c),s}n=function(){var s=/^(-?)0([xbo])(?=\w[\w.]*$)/i,c=/^([^.]+)\.$/,m=/^\.([^.]+)$/,v=/^-?(Infinity|NaN)$/,h=/^\s*\+(?=[\w.])|^\s+|\s+$/g;return function(p,g,f,d){var S,T=f?g:g.replace(h,"");if(v.test(T))p.s=isNaN(T)?null:T<0?-1:1;else{if(!f&&(T=T.replace(s,function($,I,L){return S=(L=L.toLowerCase())=="x"?16:L=="b"?2:8,!d||d==S?I:$}),d&&(S=d,T=T.replace(c,"$1").replace(m,"0.$1")),g!=T))return new N(T,S);if(N.DEBUG)throw Error(ie+"Not a"+(d?" base "+d:"")+" number: "+g);p.s=null}p.c=p.e=null}}();function D(s,c,m,v){var h,p,g,f,d,S,T,$=s.c,I=ht;if($){e:{for(h=1,f=$[0];f>=10;f/=10,h++);if(p=c-h,p<0)p+=j,g=c,d=$[S=0],T=d/I[h-g-1]%10|0;else if(S=ft((p+1)/j),S>=$.length)if(v){for(;$.length<=S;$.push(0));d=T=0,h=1,p%=j,g=p-j+1}else break e;else{for(d=f=$[S],h=1;f>=10;f/=10,h++);p%=j,g=p-j+h,T=g<0?0:d/I[h-g-1]%10|0}if(v=v||c<0||$[S+1]!=null||(g<0?d:d%I[h-g-1]),v=m<4?(T||v)&&(m==0||m==(s.s<0?3:2)):T>5||T==5&&(m==4||v||m==6&&(p>0?g>0?d/I[h-g]:0:$[S-1])%10&1||m==(s.s<0?8:7)),c<1||!$[0])return $.length=0,v?(c-=s.e+1,$[0]=I[(j-c%j)%j],s.e=-c||0):$[0]=s.e=0,s;if(p==0?($.length=S,f=1,S--):($.length=S+1,f=I[j-p],$[S]=g>0?le(d/I[h-g]%I[g])*f:0),v)for(;;)if(S==0){for(p=1,g=$[0];g>=10;g/=10,p++);for(g=$[0]+=f,f=1;g>=10;g/=10,f++);p!=f&&(s.e++,$[0]==fe&&($[0]=1));break}else{if($[S]+=f,$[S]!=fe)break;$[S--]=0,f=1}for(p=$.length;$[--p]===0;$.pop());}s.e>M?s.c=s.e=null:s.e<w&&(s.c=[s.e=0])}return s}function R(s){var c,m=s.e;return m===null?s.toString():(c=ue(s.c),c=m<=o||m>=a?Be(c,m):ye(c,m,"0"),s.s<0?"-"+c:c)}return i.absoluteValue=i.abs=function(){var s=new N(this);return s.s<0&&(s.s=1),s},i.comparedTo=function(s,c){return Se(this,new N(s,c))},i.decimalPlaces=i.dp=function(s,c){var m,v,h,p=this;if(s!=null)return q(s,0,ee),c==null?c=y:q(c,0,8),D(new N(p),s+p.e+1,c);if(!(m=p.c))return null;if(v=((h=m.length-1)-ae(this.e/j))*j,h=m[h])for(;h%10==0;h/=10,v--);return v<0&&(v=0),v},i.dividedBy=i.div=function(s,c){return e(this,new N(s,c),l,y)},i.dividedToIntegerBy=i.idiv=function(s,c){return e(this,new N(s,c),0,1)},i.exponentiatedBy=i.pow=function(s,c){var m,v,h,p,g,f,d,S,T,$=this;if(s=new N(s),s.c&&!s.isInteger())throw Error(ie+"Exponent not an integer: "+R(s));if(c!=null&&(c=new N(c)),f=s.e>14,!$.c||!$.c[0]||$.c[0]==1&&!$.e&&$.c.length==1||!s.c||!s.c[0])return T=new N(Math.pow(+R($),f?s.s*(2-Pe(s)):+R(s))),c?T.mod(c):T;if(d=s.s<0,c){if(c.c?!c.c[0]:!c.s)return new N(NaN);v=!d&&$.isInteger()&&c.isInteger(),v&&($=$.mod(c))}else{if(s.e>9&&($.e>0||$.e<-1||($.e==0?$.c[0]>1||f&&$.c[1]>=24e7:$.c[0]<8e13||f&&$.c[0]<=9999975e7)))return p=$.s<0&&Pe(s)?-0:0,$.e>-1&&(p=1/p),new N(d?1/p:p);E&&(p=ft(E/j+2))}for(f?(m=new N(.5),d&&(s.s=1),S=Pe(s)):(h=Math.abs(+R(s)),S=h%2),T=new N(u);;){if(S){if(T=T.times($),!T.c)break;p?T.c.length>p&&(T.c.length=p):v&&(T=T.mod(c))}if(h){if(h=le(h/2),h===0)break;S=h%2}else if(s=s.times(m),D(s,s.e+1,1),s.e>14)S=Pe(s);else{if(h=+R(s),h===0)break;S=h%2}$=$.times($),p?$.c&&$.c.length>p&&($.c.length=p):v&&($=$.mod(c))}return v?T:(d&&(T=u.div(T)),c?T.mod(c):p?D(T,E,y,g):T)},i.integerValue=function(s){var c=new N(this);return s==null?s=y:q(s,0,8),D(c,c.e+1,s)},i.isEqualTo=i.eq=function(s,c){return Se(this,new N(s,c))===0},i.isFinite=function(){return!!this.c},i.isGreaterThan=i.gt=function(s,c){return Se(this,new N(s,c))>0},i.isGreaterThanOrEqualTo=i.gte=function(s,c){return(c=Se(this,new N(s,c)))===1||c===0},i.isInteger=function(){return!!this.c&&ae(this.e/j)>this.c.length-2},i.isLessThan=i.lt=function(s,c){return Se(this,new N(s,c))<0},i.isLessThanOrEqualTo=i.lte=function(s,c){return(c=Se(this,new N(s,c)))===-1||c===0},i.isNaN=function(){return!this.s},i.isNegative=function(){return this.s<0},i.isPositive=function(){return this.s>0},i.isZero=function(){return!!this.c&&this.c[0]==0},i.minus=function(s,c){var m,v,h,p,g=this,f=g.s;if(s=new N(s,c),c=s.s,!f||!c)return new N(NaN);if(f!=c)return s.s=-c,g.plus(s);var d=g.e/j,S=s.e/j,T=g.c,$=s.c;if(!d||!S){if(!T||!$)return T?(s.s=-c,s):new N($?g:NaN);if(!T[0]||!$[0])return $[0]?(s.s=-c,s):new N(T[0]?g:y==3?-0:0)}if(d=ae(d),S=ae(S),T=T.slice(),f=d-S){for((p=f<0)?(f=-f,h=T):(S=d,h=$),h.reverse(),c=f;c--;h.push(0));h.reverse()}else for(v=(p=(f=T.length)<(c=$.length))?f:c,f=c=0;c<v;c++)if(T[c]!=$[c]){p=T[c]<$[c];break}if(p&&(h=T,T=$,$=h,s.s=-s.s),c=(v=$.length)-(m=T.length),c>0)for(;c--;T[m++]=0);for(c=fe-1;v>f;){if(T[--v]<$[v]){for(m=v;m&&!T[--m];T[m]=c);--T[m],T[v]+=fe}T[v]-=$[v]}for(;T[0]==0;T.splice(0,1),--S);return T[0]?k(s,T,S):(s.s=y==3?-1:1,s.c=[s.e=0],s)},i.modulo=i.mod=function(s,c){var m,v,h=this;return s=new N(s,c),!h.c||!s.s||s.c&&!s.c[0]?new N(NaN):!s.c||h.c&&!h.c[0]?new N(h):(O==9?(v=s.s,s.s=1,m=e(h,s,0,3),s.s=v,m.s*=v):m=e(h,s,0,O),s=h.minus(m.times(s)),!s.c[0]&&O==1&&(s.s=h.s),s)},i.multipliedBy=i.times=function(s,c){var m,v,h,p,g,f,d,S,T,$,I,L,B,Y,K,W=this,H=W.c,G=(s=new N(s,c)).c;if(!H||!G||!H[0]||!G[0])return!W.s||!s.s||H&&!H[0]&&!G||G&&!G[0]&&!H?s.c=s.e=s.s=null:(s.s*=W.s,!H||!G?s.c=s.e=null:(s.c=[0],s.e=0)),s;for(v=ae(W.e/j)+ae(s.e/j),s.s*=W.s,d=H.length,$=G.length,d<$&&(B=H,H=G,G=B,h=d,d=$,$=h),h=d+$,B=[];h--;B.push(0));for(Y=fe,K=_e,h=$;--h>=0;){for(m=0,I=G[h]%K,L=G[h]/K|0,g=d,p=h+g;p>h;)S=H[--g]%K,T=H[g]/K|0,f=L*S+T*I,S=I*S+f%K*K+B[p]+m,m=(S/Y|0)+(f/K|0)+L*T,B[p--]=S%Y;B[p]=m}return m?++v:B.splice(0,1),k(s,B,v)},i.negated=function(){var s=new N(this);return s.s=-s.s||null,s},i.plus=function(s,c){var m,v=this,h=v.s;if(s=new N(s,c),c=s.s,!h||!c)return new N(NaN);if(h!=c)return s.s=-c,v.minus(s);var p=v.e/j,g=s.e/j,f=v.c,d=s.c;if(!p||!g){if(!f||!d)return new N(h/0);if(!f[0]||!d[0])return d[0]?s:new N(f[0]?v:h*0)}if(p=ae(p),g=ae(g),f=f.slice(),h=p-g){for(h>0?(g=p,m=d):(h=-h,m=f),m.reverse();h--;m.push(0));m.reverse()}for(h=f.length,c=d.length,h-c<0&&(m=d,d=f,f=m,c=h),h=0;c;)h=(f[--c]=f[c]+d[c]+h)/fe|0,f[c]=fe===f[c]?0:f[c]%fe;return h&&(f=[h].concat(f),++g),k(s,f,g)},i.precision=i.sd=function(s,c){var m,v,h,p=this;if(s!=null&&s!==!!s)return q(s,1,ee),c==null?c=y:q(c,0,8),D(new N(p),s,c);if(!(m=p.c))return null;if(h=m.length-1,v=h*j+1,h=m[h]){for(;h%10==0;h/=10,v--);for(h=m[0];h>=10;h/=10,v++);}return s&&p.e+1>v&&(v=p.e+1),v},i.shiftedBy=function(s){return q(s,-lt,lt),this.times("1e"+s)},i.squareRoot=i.sqrt=function(){var s,c,m,v,h,p=this,g=p.c,f=p.s,d=p.e,S=l+4,T=new N("0.5");if(f!==1||!g||!g[0])return new N(!f||f<0&&(!g||g[0])?NaN:g?p:1/0);if(f=Math.sqrt(+R(p)),f==0||f==1/0?(c=ue(g),(c.length+d)%2==0&&(c+="0"),f=Math.sqrt(+c),d=ae((d+1)/2)-(d<0||d%2),f==1/0?c="5e"+d:(c=f.toExponential(),c=c.slice(0,c.indexOf("e")+1)+d),m=new N(c)):m=new N(f+""),m.c[0]){for(d=m.e,f=d+S,f<3&&(f=0);;)if(h=m,m=T.times(h.plus(e(p,h,S,1))),ue(h.c).slice(0,f)===(c=ue(m.c)).slice(0,f))if(m.e<d&&--f,c=c.slice(f-3,f+1),c=="9999"||!v&&c=="4999"){if(!v&&(D(h,h.e+l+2,0),h.times(h).eq(p))){m=h;break}S+=4,f+=4,v=1}else{(!+c||!+c.slice(1)&&c.charAt(0)=="5")&&(D(m,m.e+l+2,1),s=!m.times(m).eq(p));break}}return D(m,m.e+l+1,y,s)},i.toExponential=function(s,c){return s!=null&&(q(s,0,ee),s++),V(this,s,c,1)},i.toFixed=function(s,c){return s!=null&&(q(s,0,ee),s=s+this.e+1),V(this,s,c)},i.toFormat=function(s,c,m){var v,h=this;if(m==null)s!=null&&c&&typeof c=="object"?(m=c,c=null):s&&typeof s=="object"?(m=s,s=c=null):m=A;else if(typeof m!="object")throw Error(ie+"Argument not an object: "+m);if(v=h.toFixed(s,c),h.c){var p,g=v.split("."),f=+m.groupSize,d=+m.secondaryGroupSize,S=m.groupSeparator||"",T=g[0],$=g[1],I=h.s<0,L=I?T.slice(1):T,B=L.length;if(d&&(p=f,f=d,d=p,B-=p),f>0&&B>0){for(p=B%f||f,T=L.substr(0,p);p<B;p+=f)T+=S+L.substr(p,f);d>0&&(T+=S+L.slice(p)),I&&(T="-"+T)}v=$?T+(m.decimalSeparator||"")+((d=+m.fractionGroupSize)?$.replace(new RegExp("\\d{"+d+"}\\B","g"),"$&"+(m.fractionGroupSeparator||"")):$):T}return(m.prefix||"")+v+(m.suffix||"")},i.toFraction=function(s){var c,m,v,h,p,g,f,d,S,T,$,I,L=this,B=L.c;if(s!=null&&(f=new N(s),!f.isInteger()&&(f.c||f.s!==1)||f.lt(u)))throw Error(ie+"Argument "+(f.isInteger()?"out of range: ":"not an integer: ")+R(f));if(!B)return new N(L);for(c=new N(u),S=m=new N(u),v=d=new N(u),I=ue(B),p=c.e=I.length-L.e-1,c.c[0]=ht[(g=p%j)<0?j+g:g],s=!s||f.comparedTo(c)>0?p>0?c:S:f,g=M,M=1/0,f=new N(I),d.c[0]=0;T=e(f,c,0,1),h=m.plus(T.times(v)),h.comparedTo(s)!=1;)m=v,v=h,S=d.plus(T.times(h=S)),d=h,c=f.minus(T.times(h=c)),f=h;return h=e(s.minus(m),v,0,1),d=d.plus(h.times(S)),m=m.plus(h.times(v)),d.s=S.s=L.s,p=p*2,$=e(S,v,p,y).minus(L).abs().comparedTo(e(d,m,p,y).minus(L).abs())<1?[S,v]:[d,m],M=g,$},i.toNumber=function(){return+R(this)},i.toPrecision=function(s,c){return s!=null&&q(s,1,ee),V(this,s,c,2)},i.toString=function(s){var c,m=this,v=m.s,h=m.e;return h===null?v?(c="Infinity",v<0&&(c="-"+c)):c="NaN":(s==null?c=h<=o||h>=a?Be(ue(m.c),h):ye(ue(m.c),h,"0"):s===10&&P?(m=D(new N(m),l+h+1,y),c=ye(ue(m.c),m.e,"0")):(q(s,2,C.length,"Base"),c=r(ye(ue(m.c),h,"0"),10,s,v,!0)),v<0&&m.c[0]&&(c="-"+c)),c},i.valueOf=i.toJSON=function(){return R(this)},i._isBigNumber=!0,i[Symbol.toStringTag]="BigNumber",i[Symbol.for("nodejs.util.inspect.custom")]=i.valueOf,t!=null&&N.set(t),N}function ae(t){var e=t|0;return t>0||t===e?e:e-1}function ue(t){for(var e,r,n=1,i=t.length,u=t[0]+"";n<i;){for(e=t[n++]+"",r=j-e.length;r--;e="0"+e);u+=e}for(i=u.length;u.charCodeAt(--i)===48;);return u.slice(0,i+1||1)}function Se(t,e){var r,n,i=t.c,u=e.c,l=t.s,y=e.s,o=t.e,a=e.e;if(!l||!y)return null;if(r=i&&!i[0],n=u&&!u[0],r||n)return r?n?0:-y:l;if(l!=y)return l;if(r=l<0,n=o==a,!i||!u)return n?0:!i^r?1:-1;if(!n)return o>a^r?1:-1;for(y=(o=i.length)<(a=u.length)?o:a,l=0;l<y;l++)if(i[l]!=u[l])return i[l]>u[l]^r?1:-1;return o==a?0:o>a^r?1:-1}function q(t,e,r,n){if(t<e||t>r||t!==le(t))throw Error(ie+(n||"Argument")+(typeof t=="number"?t<e||t>r?" out of range: ":" not an integer: ":" not a primitive number: ")+String(t))}function Pe(t){var e=t.c.length-1;return ae(t.e/j)==e&&t.c[e]%2!=0}function Be(t,e){return(t.length>1?t.charAt(0)+"."+t.slice(1):t)+(e<0?"e":"e+")+e}function ye(t,e,r){var n,i;if(e<0){for(i=r+".";++e;i+=r);t=i+t}else if(n=t.length,++e>n){for(i=r,e-=n;--e;i+=r);t+=i}else e<n&&(t=t.slice(0,e)+"."+t.slice(e));return t}var J=nr(),zs=J;x();_();var ir=t=>t.replace(/^(?:https?:\/\/)?(?:www\.)?/i,"").split("/")[0];x();_();var sr=(t,e)=>{let r=t,n=`${r.substring(0,e)}...`;return n.length>=t.length?t:r.length>e?n:r};x();_();x();_();var or=t=>t.charAt(0).toUpperCase()+t.slice(1).toLowerCase();x();_();function cr(t,e,r){let n="...",i=e+r+n.length;if(t.length<=i)return t;let u=t.substring(0,e),l=t.substring(t.length-r);return`${u}${n}${l}`}x();_();function ur(t,e){return t.localeCompare(e,void 0,{sensitivity:"base"})===0}x();_();x();_();var ar=t=>{let e=t.indexOf("#");if(e!==-1){let r=t.indexOf("?");return r!==-1&&r<e?t.substring(0,e):r!==-1&&r>e?t.substring(0,e)+t.substring(r,t.length):t.substring(0,e)}return t};x();_();var Zn={days:t=>t*864e5,hours:t=>t*36e5,minutes:t=>t*6e4,seconds:t=>t*1e3,milliseconds:t=>t,microseconds:t=>t/1e3,nanoseconds:t=>t/1e6};function De(t){let e=0;for(let[r,n]of Object.entries(t)){let i=Zn[r];e+=i(n)}return e}x();_();var yt=Q(Oe()),lr=Q(fr());yt.default.extend(lr.default);var hr=t=>(0,yt.default)(t).calendar();x();_();x();_();x();_();var Ue=Q(Oe()),mr=Q(xt()),pr=Q(dr());Ue.default.extend(pr.default);Ue.default.extend(mr.default);var Te=Ue.default,ko=new Date().getTimezoneOffset()*6e4;var gr=(t,e)=>{let r=Te(t),n=r.format("MMM D, YYYY"),i=r.format("h:mm a");return`${n} ${e||""} ${i}`};x();_();x();_();var yr=Q(Oe()),br=Q(xt());yr.default.extend(br.default);x();_();var ei=Q(Oe());x();_();var Fe=Q(Oe()),_r=Q(wr()),Tr=Q(xr());Fe.default.extend(_r.default);Fe.default.extend(Tr.default);var Mr=t=>{let e={Today:[],Yesterday:[],"Last Week":[],Earlier:[]};for(let r of t){let n=r.timestamp??0,i=Fe.default.unix(n/1e3),u="Earlier";i.isToday()?u="Today":i.isYesterday()?u="Yesterday":i.isAfter((0,Fe.default)().subtract(1,"week"))&&(u="Last Week"),e[u].push(r)}return Object.keys(e).reduce((r,n)=>({...r,[n]:e[n].sort((i,u)=>(i.timestamp==null&&(i.timestamp=0),u.timestamp==null&&(u.timestamp=0),i.timestamp<u.timestamp?1:i.timestamp>u.timestamp?-1:0)).map(i=>i.data)}),{})};x();_();var vr=(t,e=!1)=>{let r=Te.duration(Te().diff(Te(t))),n=[[r.years(),"y"],[r.months(),"mo"],[r.days(),"d"],[r.hours(),"h"],[r.minutes(),"m"],[r.seconds(),"s"],[r.milliseconds(),"ms"]].find(l=>l[0]!==0);if(!n)return"";let[i,u]=n;return e&&(u==="ms"||u==="s")?"< 1m":u==="ms"?"now":u==="s"?i<15?"now":"< 1m":`${i}${u}`};x();_();var Sr=(t,e)=>{let r=Math.floor(t/3600),n=Math.floor((t-r*3600)/60),i=t%60;return r>0?e("pendingTransactionEstimatedTimeHM",{hours:r,minutes:n}):n>0?e("pendingTransactionEstimatedTimeMS",{minutes:n,seconds:i}):e("pendingTransactionEstimatedTimeS",{seconds:i})};x();_();x();_();var je=t=>t/6e4;var Or=(t,e)=>{if(!e||e===0)return t("requireAuthImmediately");let r=je(e);return r===1?`1 ${t("timeUnitMinute")}`:r===60?`1 ${t("timeUnitHour")}`:r>60?`${r/60} ${t("timeUnitHours")}`:`${r} ${t("timeUnitMinutes")}`};x();_();var Nr=t=>t*6e4;x();_();var $r=De({seconds:10}),Er=De({seconds:5});x();_();var Ar=t=>{if(t)try{return new URL(t).origin}catch{return}};x();_();x();_();var Nt=t=>{if(t)try{return new URL(t).hostname}catch{return}};x();_();var Dr={origin:void 0,hostname:void 0},Fr=t=>{if(!t)return Dr;!t.startsWith("http://")&&!t.startsWith("https://")&&(t=`https://${t}`);try{let e=new URL(t);return{origin:e.origin,hostname:e.hostname}}catch{return Dr}};x();_();var Ir=({url:t,format:e})=>{if(!t)return null;!t.startsWith("http")&&t.split(".").length===2&&(t=`http://${t}`),t.startsWith("www.")&&(t=`https://${t}`),!t.startsWith("http")&&!t.startsWith("https")&&(t=`https://${t}`);try{let{hostname:r,href:n}=new URL(t);switch(e){case"hostname":return r.replace("www.","");case"root":return r.split(".").slice(-2).join(".");case"domain":return r.split(".").slice(-2)[0];case"full":return n;default:return r}}catch{return null}};x();_();var kr=["http:","https:","phantom:","solana:"],Lr=t=>{if(!t)return null;let e=null;if(/^https?:/i.test(t)&&!t.includes("://"))return null;let r=/^(?<specifiedProtocol>\w+:)/.exec(t),n=r?.groups?.specifiedProtocol.toLowerCase();if(n&&!kr.includes(n))return null;try{if(e=new URL(r?t:`https://${t}`),e&&e.protocol==="http:"&&(e.protocol="https:"),e&&!kr.some(i=>i===e?.protocol))return null}catch{}return e};x();_();var ni=/^(https?:\/\/)?(localhost(:\d+)|[\w-]+(\.[\w-]+)+(:\d+)?)(\/\S*)?/;function Ve(t){return t?ni.test(t):!1}x();_();var ii=["localhost","127.0.0.1"],$t={google:t=>`https://www.google.com/search?q=${t}`},Cr=t=>{try{let e=/^https?:\/\//i.test(t),r=Ve(t);if(r&&e)return t;if(!r)throw new Error("Query is not a valid URL");let n=e?t:`http://${t}`,i=new URL(n);if(ii.includes(i.hostname))return n;if(!t.includes("."))throw new Error("missing tld");return i.toString().replace(/^http:\/\//i,"https://").replace(/\/$/,"")}catch{let e=encodeURIComponent(t);return $t.google(e)}};x();_();x();_();x();_();var We=class{constructor(e,r,n,i){this.storage=e,this.ttlMs=r,this.asyncFunction=n,this.cacheKey=`${i}-data`,this.cacheExpirationKey=`${i}-expiration`,this.updateCache()}async updateCache(){try{let e=await this.asyncFunction();await this.storage.set(this.cacheKey,e),await this.storage.set(this.cacheExpirationKey,Date.now()+this.ttlMs)}catch(e){throw new Error("error updating cache: "+e?.message,{cause:e})}}async get(){let e=await this.storage.get(this.cacheKey);try{return e||(await this.updateCache(),await this.storage.get(this.cacheKey))}finally{let r=await this.storage.get(this.cacheExpirationKey);(typeof r!="number"||r<Date.now())&&await this.updateCache()}}};x();_();x();_();var Rr=(t,e)=>{let r=parseInt(t.slice(1,3),16),n=parseInt(t.slice(3,5),16),i=parseInt(t.slice(5,7),16);return typeof e=="number"?"rgba("+r+", "+n+", "+i+", "+e+")":"rgb("+r+", "+n+", "+i+")"};x();_();x();_();var Ne=Q(Ae());function zr(t,e=100){let[r,n]=(0,Ne.useState)(t),i=(0,Ne.useRef)();return(0,Ne.useEffect)(()=>(i.current&&clearTimeout(i.current),i.current=setTimeout(()=>{n(t)},e),()=>{i.current&&clearTimeout(i.current)}),[n,t,e]),r}x();_();function be(t){return Array.isArray?Array.isArray(t):Hr(t)==="[object Array]"}var ui=1/0;function ai(t){if(typeof t=="string")return t;let e=t+"";return e=="0"&&1/t==-ui?"-0":e}function fi(t){return t==null?"":ai(t)}function he(t){return typeof t=="string"}function Wr(t){return typeof t=="number"}function li(t){return t===!0||t===!1||hi(t)&&Hr(t)=="[object Boolean]"}function Yr(t){return typeof t=="object"}function hi(t){return Yr(t)&&t!==null}function se(t){return t!=null}function Et(t){return!t.trim().length}function Hr(t){return t==null?t===void 0?"[object Undefined]":"[object Null]":Object.prototype.toString.call(t)}var di="Incorrect 'index' type",mi=t=>`Invalid value for key ${t}`,pi=t=>`Pattern length exceeds max of ${t}.`,gi=t=>`Missing ${t} property in key`,yi=t=>`Property 'weight' in key '${t}' must be a positive integer`,Pr=Object.prototype.hasOwnProperty,At=class{constructor(e){this._keys=[],this._keyMap={};let r=0;e.forEach(n=>{let i=Kr(n);this._keys.push(i),this._keyMap[i.id]=i,r+=i.weight}),this._keys.forEach(n=>{n.weight/=r})}get(e){return this._keyMap[e]}keys(){return this._keys}toJSON(){return JSON.stringify(this._keys)}};function Kr(t){let e=null,r=null,n=null,i=1,u=null;if(he(t)||be(t))n=t,e=Br(t),r=Dt(t);else{if(!Pr.call(t,"name"))throw new Error(gi("name"));let l=t.name;if(n=l,Pr.call(t,"weight")&&(i=t.weight,i<=0))throw new Error(yi(l));e=Br(l),r=Dt(l),u=t.getFn}return{path:e,id:r,weight:i,src:n,getFn:u}}function Br(t){return be(t)?t:t.split(".")}function Dt(t){return be(t)?t.join("."):t}function bi(t,e){let r=[],n=!1,i=(u,l,y)=>{if(se(u))if(!l[y])r.push(u);else{let o=l[y],a=u[o];if(!se(a))return;if(y===l.length-1&&(he(a)||Wr(a)||li(a)))r.push(fi(a));else if(be(a)){n=!0;for(let w=0,M=a.length;w<M;w+=1)i(a[w],l,y+1)}else l.length&&i(a,l,y+1)}};return i(t,he(e)?e.split("."):e,0),n?r:r[0]}var wi={includeMatches:!1,findAllMatches:!1,minMatchCharLength:1},xi={isCaseSensitive:!1,includeScore:!1,keys:[],shouldSort:!0,sortFn:(t,e)=>t.score===e.score?t.idx<e.idx?-1:1:t.score<e.score?-1:1},_i={location:0,threshold:.6,distance:100},Ti={useExtendedSearch:!1,getFn:bi,ignoreLocation:!1,ignoreFieldNorm:!1,fieldNormWeight:1},U={...xi,...wi,..._i,...Ti},Mi=/[^ ]+/g;function vi(t=1,e=3){let r=new Map,n=Math.pow(10,e);return{get(i){let u=i.match(Mi).length;if(r.has(u))return r.get(u);let l=1/Math.pow(u,.5*t),y=parseFloat(Math.round(l*n)/n);return r.set(u,y),y},clear(){r.clear()}}}var Ie=class{constructor({getFn:e=U.getFn,fieldNormWeight:r=U.fieldNormWeight}={}){this.norm=vi(r,3),this.getFn=e,this.isCreated=!1,this.setIndexRecords()}setSources(e=[]){this.docs=e}setIndexRecords(e=[]){this.records=e}setKeys(e=[]){this.keys=e,this._keysMap={},e.forEach((r,n)=>{this._keysMap[r.id]=n})}create(){this.isCreated||!this.docs.length||(this.isCreated=!0,he(this.docs[0])?this.docs.forEach((e,r)=>{this._addString(e,r)}):this.docs.forEach((e,r)=>{this._addObject(e,r)}),this.norm.clear())}add(e){let r=this.size();he(e)?this._addString(e,r):this._addObject(e,r)}removeAt(e){this.records.splice(e,1);for(let r=e,n=this.size();r<n;r+=1)this.records[r].i-=1}getValueForItemAtKeyId(e,r){return e[this._keysMap[r]]}size(){return this.records.length}_addString(e,r){if(!se(e)||Et(e))return;let n={v:e,i:r,n:this.norm.get(e)};this.records.push(n)}_addObject(e,r){let n={i:r,$:{}};this.keys.forEach((i,u)=>{let l=i.getFn?i.getFn(e):this.getFn(e,i.path);if(se(l)){if(be(l)){let y=[],o=[{nestedArrIndex:-1,value:l}];for(;o.length;){let{nestedArrIndex:a,value:w}=o.pop();if(se(w))if(he(w)&&!Et(w)){let M={v:w,i:a,n:this.norm.get(w)};y.push(M)}else be(w)&&w.forEach((M,b)=>{o.push({nestedArrIndex:b,value:M})})}n.$[u]=y}else if(he(l)&&!Et(l)){let y={v:l,n:this.norm.get(l)};n.$[u]=y}}}),this.records.push(n)}toJSON(){return{keys:this.keys,records:this.records}}};function Gr(t,e,{getFn:r=U.getFn,fieldNormWeight:n=U.fieldNormWeight}={}){let i=new Ie({getFn:r,fieldNormWeight:n});return i.setKeys(t.map(Kr)),i.setSources(e),i.create(),i}function Si(t,{getFn:e=U.getFn,fieldNormWeight:r=U.fieldNormWeight}={}){let{keys:n,records:i}=t,u=new Ie({getFn:e,fieldNormWeight:r});return u.setKeys(n),u.setIndexRecords(i),u}function Ye(t,{errors:e=0,currentLocation:r=0,expectedLocation:n=0,distance:i=U.distance,ignoreLocation:u=U.ignoreLocation}={}){let l=e/t.length;if(u)return l;let y=Math.abs(n-r);return i?l+y/i:y?1:l}function Oi(t=[],e=U.minMatchCharLength){let r=[],n=-1,i=-1,u=0;for(let l=t.length;u<l;u+=1){let y=t[u];y&&n===-1?n=u:!y&&n!==-1&&(i=u-1,i-n+1>=e&&r.push([n,i]),n=-1)}return t[u-1]&&u-n>=e&&r.push([n,u-1]),r}var ve=32;function Ni(t,e,r,{location:n=U.location,distance:i=U.distance,threshold:u=U.threshold,findAllMatches:l=U.findAllMatches,minMatchCharLength:y=U.minMatchCharLength,includeMatches:o=U.includeMatches,ignoreLocation:a=U.ignoreLocation}={}){if(e.length>ve)throw new Error(pi(ve));let w=e.length,M=t.length,b=Math.max(0,Math.min(n,M)),O=u,E=b,A=y>1||o,C=A?Array(M):[],P;for(;(P=t.indexOf(e,E))>-1;){let R=Ye(e,{currentLocation:P,expectedLocation:b,distance:i,ignoreLocation:a});if(O=Math.min(R,O),E=P+w,A){let s=0;for(;s<w;)C[P+s]=1,s+=1}}E=-1;let N=[],V=1,z=w+M,k=1<<w-1;for(let R=0;R<w;R+=1){let s=0,c=z;for(;s<c;)Ye(e,{errors:R,currentLocation:b+c,expectedLocation:b,distance:i,ignoreLocation:a})<=O?s=c:z=c,c=Math.floor((z-s)/2+s);z=c;let m=Math.max(1,b-c+1),v=l?M:Math.min(b+c,M)+w,h=Array(v+2);h[v+1]=(1<<R)-1;for(let g=v;g>=m;g-=1){let f=g-1,d=r[t.charAt(f)];if(A&&(C[f]=+!!d),h[g]=(h[g+1]<<1|1)&d,R&&(h[g]|=(N[g+1]|N[g])<<1|1|N[g+1]),h[g]&k&&(V=Ye(e,{errors:R,currentLocation:f,expectedLocation:b,distance:i,ignoreLocation:a}),V<=O)){if(O=V,E=f,E<=b)break;m=Math.max(1,2*b-E)}}if(Ye(e,{errors:R+1,currentLocation:b,expectedLocation:b,distance:i,ignoreLocation:a})>O)break;N=h}let D={isMatch:E>=0,score:Math.max(.001,V)};if(A){let R=Oi(C,y);R.length?o&&(D.indices=R):D.isMatch=!1}return D}function $i(t){let e={};for(let r=0,n=t.length;r<n;r+=1){let i=t.charAt(r);e[i]=(e[i]||0)|1<<n-r-1}return e}var He=class{constructor(e,{location:r=U.location,threshold:n=U.threshold,distance:i=U.distance,includeMatches:u=U.includeMatches,findAllMatches:l=U.findAllMatches,minMatchCharLength:y=U.minMatchCharLength,isCaseSensitive:o=U.isCaseSensitive,ignoreLocation:a=U.ignoreLocation}={}){if(this.options={location:r,threshold:n,distance:i,includeMatches:u,findAllMatches:l,minMatchCharLength:y,isCaseSensitive:o,ignoreLocation:a},this.pattern=o?e:e.toLowerCase(),this.chunks=[],!this.pattern.length)return;let w=(b,O)=>{this.chunks.push({pattern:b,alphabet:$i(b),startIndex:O})},M=this.pattern.length;if(M>ve){let b=0,O=M%ve,E=M-O;for(;b<E;)w(this.pattern.substr(b,ve),b),b+=ve;if(O){let A=M-ve;w(this.pattern.substr(A),A)}}else w(this.pattern,0)}searchIn(e){let{isCaseSensitive:r,includeMatches:n}=this.options;if(r||(e=e.toLowerCase()),this.pattern===e){let E={isMatch:!0,score:0};return n&&(E.indices=[[0,e.length-1]]),E}let{location:i,distance:u,threshold:l,findAllMatches:y,minMatchCharLength:o,ignoreLocation:a}=this.options,w=[],M=0,b=!1;this.chunks.forEach(({pattern:E,alphabet:A,startIndex:C})=>{let{isMatch:P,score:N,indices:V}=Ni(e,E,A,{location:i+C,distance:u,threshold:l,findAllMatches:y,minMatchCharLength:o,includeMatches:n,ignoreLocation:a});P&&(b=!0),M+=N,P&&V&&(w=[...w,...V])});let O={isMatch:b,score:b?M/this.chunks.length:1};return b&&n&&(O.indices=w),O}},de=class{constructor(e){this.pattern=e}static isMultiMatch(e){return Ur(e,this.multiRegex)}static isSingleMatch(e){return Ur(e,this.singleRegex)}search(){}};function Ur(t,e){let r=t.match(e);return r?r[1]:null}var Ft=class extends de{constructor(e){super(e)}static get type(){return"exact"}static get multiRegex(){return/^="(.*)"$/}static get singleRegex(){return/^=(.*)$/}search(e){let r=e===this.pattern;return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}},It=class extends de{constructor(e){super(e)}static get type(){return"inverse-exact"}static get multiRegex(){return/^!"(.*)"$/}static get singleRegex(){return/^!(.*)$/}search(e){let n=e.indexOf(this.pattern)===-1;return{isMatch:n,score:n?0:1,indices:[0,e.length-1]}}},kt=class extends de{constructor(e){super(e)}static get type(){return"prefix-exact"}static get multiRegex(){return/^\^"(.*)"$/}static get singleRegex(){return/^\^(.*)$/}search(e){let r=e.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,this.pattern.length-1]}}},Lt=class extends de{constructor(e){super(e)}static get type(){return"inverse-prefix-exact"}static get multiRegex(){return/^!\^"(.*)"$/}static get singleRegex(){return/^!\^(.*)$/}search(e){let r=!e.startsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,e.length-1]}}},Ct=class extends de{constructor(e){super(e)}static get type(){return"suffix-exact"}static get multiRegex(){return/^"(.*)"\$$/}static get singleRegex(){return/^(.*)\$$/}search(e){let r=e.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[e.length-this.pattern.length,e.length-1]}}},Rt=class extends de{constructor(e){super(e)}static get type(){return"inverse-suffix-exact"}static get multiRegex(){return/^!"(.*)"\$$/}static get singleRegex(){return/^!(.*)\$$/}search(e){let r=!e.endsWith(this.pattern);return{isMatch:r,score:r?0:1,indices:[0,e.length-1]}}},Ke=class extends de{constructor(e,{location:r=U.location,threshold:n=U.threshold,distance:i=U.distance,includeMatches:u=U.includeMatches,findAllMatches:l=U.findAllMatches,minMatchCharLength:y=U.minMatchCharLength,isCaseSensitive:o=U.isCaseSensitive,ignoreLocation:a=U.ignoreLocation}={}){super(e),this._bitapSearch=new He(e,{location:r,threshold:n,distance:i,includeMatches:u,findAllMatches:l,minMatchCharLength:y,isCaseSensitive:o,ignoreLocation:a})}static get type(){return"fuzzy"}static get multiRegex(){return/^"(.*)"$/}static get singleRegex(){return/^(.*)$/}search(e){return this._bitapSearch.searchIn(e)}},Ge=class extends de{constructor(e){super(e)}static get type(){return"include"}static get multiRegex(){return/^'"(.*)"$/}static get singleRegex(){return/^'(.*)$/}search(e){let r=0,n,i=[],u=this.pattern.length;for(;(n=e.indexOf(this.pattern,r))>-1;)r=n+u,i.push([n,r-1]);let l=!!i.length;return{isMatch:l,score:l?0:1,indices:i}}},zt=[Ft,Ge,kt,Lt,Rt,Ct,It,Ke],jr=zt.length,Ei=/ +(?=(?:[^\"]*\"[^\"]*\")*[^\"]*$)/,Ai="|";function Di(t,e={}){return t.split(Ai).map(r=>{let n=r.trim().split(Ei).filter(u=>u&&!!u.trim()),i=[];for(let u=0,l=n.length;u<l;u+=1){let y=n[u],o=!1,a=-1;for(;!o&&++a<jr;){let w=zt[a],M=w.isMultiMatch(y);M&&(i.push(new w(M,e)),o=!0)}if(!o)for(a=-1;++a<jr;){let w=zt[a],M=w.isSingleMatch(y);if(M){i.push(new w(M,e));break}}}return i})}var Fi=new Set([Ke.type,Ge.type]),Pt=class{constructor(e,{isCaseSensitive:r=U.isCaseSensitive,includeMatches:n=U.includeMatches,minMatchCharLength:i=U.minMatchCharLength,ignoreLocation:u=U.ignoreLocation,findAllMatches:l=U.findAllMatches,location:y=U.location,threshold:o=U.threshold,distance:a=U.distance}={}){this.query=null,this.options={isCaseSensitive:r,includeMatches:n,minMatchCharLength:i,findAllMatches:l,ignoreLocation:u,location:y,threshold:o,distance:a},this.pattern=r?e:e.toLowerCase(),this.query=Di(this.pattern,this.options)}static condition(e,r){return r.useExtendedSearch}searchIn(e){let r=this.query;if(!r)return{isMatch:!1,score:1};let{includeMatches:n,isCaseSensitive:i}=this.options;e=i?e:e.toLowerCase();let u=0,l=[],y=0;for(let o=0,a=r.length;o<a;o+=1){let w=r[o];l.length=0,u=0;for(let M=0,b=w.length;M<b;M+=1){let O=w[M],{isMatch:E,indices:A,score:C}=O.search(e);if(E){if(u+=1,y+=C,n){let P=O.constructor.type;Fi.has(P)?l=[...l,...A]:l.push(A)}}else{y=0,u=0,l.length=0;break}}if(u){let M={isMatch:!0,score:y/u};return n&&(M.indices=l),M}}return{isMatch:!1,score:1}}},Bt=[];function Ii(...t){Bt.push(...t)}function Ut(t,e){for(let r=0,n=Bt.length;r<n;r+=1){let i=Bt[r];if(i.condition(t,e))return new i(t,e)}return new He(t,e)}var Je={AND:"$and",OR:"$or"},jt={PATH:"$path",PATTERN:"$val"},Vt=t=>!!(t[Je.AND]||t[Je.OR]),ki=t=>!!t[jt.PATH],Li=t=>!be(t)&&Yr(t)&&!Vt(t),Vr=t=>({[Je.AND]:Object.keys(t).map(e=>({[e]:t[e]}))});function Jr(t,e,{auto:r=!0}={}){let n=i=>{let u=Object.keys(i),l=ki(i);if(!l&&u.length>1&&!Vt(i))return n(Vr(i));if(Li(i)){let o=l?i[jt.PATH]:u[0],a=l?i[jt.PATTERN]:i[o];if(!he(a))throw new Error(mi(o));let w={keyId:Dt(o),pattern:a};return r&&(w.searcher=Ut(a,e)),w}let y={children:[],operator:u[0]};return u.forEach(o=>{let a=i[o];be(a)&&a.forEach(w=>{y.children.push(n(w))})}),y};return Vt(t)||(t=Vr(t)),n(t)}function Ci(t,{ignoreFieldNorm:e=U.ignoreFieldNorm}){t.forEach(r=>{let n=1;r.matches.forEach(({key:i,norm:u,score:l})=>{let y=i?i.weight:null;n*=Math.pow(l===0&&y?Number.EPSILON:l,(y||1)*(e?1:u))}),r.score=n})}function Ri(t,e){let r=t.matches;e.matches=[],se(r)&&r.forEach(n=>{if(!se(n.indices)||!n.indices.length)return;let{indices:i,value:u}=n,l={indices:i,value:u};n.key&&(l.key=n.key.src),n.idx>-1&&(l.refIndex=n.idx),e.matches.push(l)})}function zi(t,e){e.score=t.score}function Pi(t,e,{includeMatches:r=U.includeMatches,includeScore:n=U.includeScore}={}){let i=[];return r&&i.push(Ri),n&&i.push(zi),t.map(u=>{let{idx:l}=u,y={item:e[l],refIndex:l};return i.length&&i.forEach(o=>{o(u,y)}),y})}var we=class{constructor(e,r={},n){this.options={...U,...r},this.options.useExtendedSearch,this._keyStore=new At(this.options.keys),this.setCollection(e,n)}setCollection(e,r){if(this._docs=e,r&&!(r instanceof Ie))throw new Error(di);this._myIndex=r||Gr(this.options.keys,this._docs,{getFn:this.options.getFn,fieldNormWeight:this.options.fieldNormWeight})}add(e){se(e)&&(this._docs.push(e),this._myIndex.add(e))}remove(e=()=>!1){let r=[];for(let n=0,i=this._docs.length;n<i;n+=1){let u=this._docs[n];e(u,n)&&(this.removeAt(n),n-=1,i-=1,r.push(u))}return r}removeAt(e){this._docs.splice(e,1),this._myIndex.removeAt(e)}getIndex(){return this._myIndex}search(e,{limit:r=-1}={}){let{includeMatches:n,includeScore:i,shouldSort:u,sortFn:l,ignoreFieldNorm:y}=this.options,o=he(e)?he(this._docs[0])?this._searchStringList(e):this._searchObjectList(e):this._searchLogical(e);return Ci(o,{ignoreFieldNorm:y}),u&&o.sort(l),Wr(r)&&r>-1&&(o=o.slice(0,r)),Pi(o,this._docs,{includeMatches:n,includeScore:i})}_searchStringList(e){let r=Ut(e,this.options),{records:n}=this._myIndex,i=[];return n.forEach(({v:u,i:l,n:y})=>{if(!se(u))return;let{isMatch:o,score:a,indices:w}=r.searchIn(u);o&&i.push({item:u,idx:l,matches:[{score:a,value:u,norm:y,indices:w}]})}),i}_searchLogical(e){let r=Jr(e,this.options),n=(y,o,a)=>{if(!y.children){let{keyId:M,searcher:b}=y,O=this._findMatches({key:this._keyStore.get(M),value:this._myIndex.getValueForItemAtKeyId(o,M),searcher:b});return O&&O.length?[{idx:a,item:o,matches:O}]:[]}let w=[];for(let M=0,b=y.children.length;M<b;M+=1){let O=y.children[M],E=n(O,o,a);if(E.length)w.push(...E);else if(y.operator===Je.AND)return[]}return w},i=this._myIndex.records,u={},l=[];return i.forEach(({$:y,i:o})=>{if(se(y)){let a=n(r,y,o);a.length&&(u[o]||(u[o]={idx:o,item:y,matches:[]},l.push(u[o])),a.forEach(({matches:w})=>{u[o].matches.push(...w)}))}}),l}_searchObjectList(e){let r=Ut(e,this.options),{keys:n,records:i}=this._myIndex,u=[];return i.forEach(({$:l,i:y})=>{if(!se(l))return;let o=[];n.forEach((a,w)=>{o.push(...this._findMatches({key:a,value:l[w],searcher:r}))}),o.length&&u.push({idx:y,item:l,matches:o})}),u}_findMatches({key:e,value:r,searcher:n}){if(!se(r))return[];let i=[];if(be(r))r.forEach(({v:u,i:l,n:y})=>{if(!se(u))return;let{isMatch:o,score:a,indices:w}=n.searchIn(u);o&&i.push({score:a,key:e,value:u,idx:l,norm:y,indices:w})});else{let{v:u,n:l}=r,{isMatch:y,score:o,indices:a}=n.searchIn(u);y&&i.push({score:o,key:e,value:u,norm:l,indices:a})}return i}};we.version="7.0.0";we.createIndex=Gr;we.parseIndex=Si;we.config=U;we.parseQuery=Jr;Ii(Pt);x();_();x();_();function Ze(t,e,r,n){function i(u){return u instanceof r?u:new r(function(l){l(u)})}return new(r||(r=Promise))(function(u,l){function y(w){try{a(n.next(w))}catch(M){l(M)}}function o(w){try{a(n.throw(w))}catch(M){l(M)}}function a(w){w.done?u(w.value):i(w.value).then(y,o)}a((n=n.apply(t,e||[])).next())})}var Bi="ENTRIES",en="KEYS",tn="VALUES",te="",ke=class{constructor(e,r){let n=e._tree,i=Array.from(n.keys());this.set=e,this._type=r,this._path=i.length>0?[{node:n,keys:i}]:[]}next(){let e=this.dive();return this.backtrack(),e}dive(){if(this._path.length===0)return{done:!0,value:void 0};let{node:e,keys:r}=$e(this._path);if($e(r)===te)return{done:!1,value:this.result()};let n=e.get($e(r));return this._path.push({node:n,keys:Array.from(n.keys())}),this.dive()}backtrack(){if(this._path.length===0)return;let e=$e(this._path).keys;e.pop(),!(e.length>0)&&(this._path.pop(),this.backtrack())}key(){return this.set._prefix+this._path.map(({keys:e})=>$e(e)).filter(e=>e!==te).join("")}value(){return $e(this._path).node.get(te)}result(){switch(this._type){case tn:return this.value();case en:return this.key();default:return[this.key(),this.value()]}}[Symbol.iterator](){return this}},$e=t=>t[t.length-1],Ui=(t,e,r)=>{let n=new Map;if(e===void 0)return n;let i=e.length+1,u=i+r,l=new Uint8Array(u*i).fill(r+1);for(let y=0;y<i;++y)l[y]=y;for(let y=1;y<u;++y)l[y*i]=y;return rn(t,e,r,n,l,1,i,""),n},rn=(t,e,r,n,i,u,l,y)=>{let o=u*l;e:for(let a of t.keys())if(a===te){let w=i[o-1];w<=r&&n.set(y,[t.get(a),w])}else{let w=u;for(let M=0;M<a.length;++M,++w){let b=a[M],O=l*w,E=O-l,A=i[O],C=Math.max(0,w-r-1),P=Math.min(l-1,w+r);for(let N=C;N<P;++N){let V=b!==e[N],z=i[E+N]+ +V,k=i[E+N+1]+1,D=i[O+N]+1,R=i[O+N+1]=Math.min(z,k,D);R<A&&(A=R)}if(A>r)continue e}rn(t.get(a),e,r,n,i,w,l,y+a)}},Le=class t{constructor(e=new Map,r=""){this._size=void 0,this._tree=e,this._prefix=r}atPrefix(e){if(!e.startsWith(this._prefix))throw new Error("Mismatched prefix");let[r,n]=Qe(this._tree,e.slice(this._prefix.length));if(r===void 0){let[i,u]=Xt(n);for(let l of i.keys())if(l!==te&&l.startsWith(u)){let y=new Map;return y.set(l.slice(u.length),i.get(l)),new t(y,e)}}return new t(r,e)}clear(){this._size=void 0,this._tree.clear()}delete(e){return this._size=void 0,ji(this._tree,e)}entries(){return new ke(this,Bi)}forEach(e){for(let[r,n]of this)e(r,n,this)}fuzzyGet(e,r){return Ui(this._tree,e,r)}get(e){let r=Gt(this._tree,e);return r!==void 0?r.get(te):void 0}has(e){let r=Gt(this._tree,e);return r!==void 0&&r.has(te)}keys(){return new ke(this,en)}set(e,r){if(typeof e!="string")throw new Error("key must be a string");return this._size=void 0,Wt(this._tree,e).set(te,r),this}get size(){if(this._size)return this._size;this._size=0;let e=this.entries();for(;!e.next().done;)this._size+=1;return this._size}update(e,r){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;let n=Wt(this._tree,e);return n.set(te,r(n.get(te))),this}fetch(e,r){if(typeof e!="string")throw new Error("key must be a string");this._size=void 0;let n=Wt(this._tree,e),i=n.get(te);return i===void 0&&n.set(te,i=r()),i}values(){return new ke(this,tn)}[Symbol.iterator](){return this.entries()}static from(e){let r=new t;for(let[n,i]of e)r.set(n,i);return r}static fromObject(e){return t.from(Object.entries(e))}},Qe=(t,e,r=[])=>{if(e.length===0||t==null)return[t,r];for(let n of t.keys())if(n!==te&&e.startsWith(n))return r.push([t,n]),Qe(t.get(n),e.slice(n.length),r);return r.push([t,e]),Qe(void 0,"",r)},Gt=(t,e)=>{if(e.length===0||t==null)return t;for(let r of t.keys())if(r!==te&&e.startsWith(r))return Gt(t.get(r),e.slice(r.length))},Wt=(t,e)=>{let r=e.length;e:for(let n=0;t&&n<r;){for(let u of t.keys())if(u!==te&&e[n]===u[0]){let l=Math.min(r-n,u.length),y=1;for(;y<l&&e[n+y]===u[y];)++y;let o=t.get(u);if(y===u.length)t=o;else{let a=new Map;a.set(u.slice(y),o),t.set(e.slice(n,n+y),a),t.delete(u),t=a}n+=y;continue e}let i=new Map;return t.set(e.slice(n),i),i}return t},ji=(t,e)=>{let[r,n]=Qe(t,e);if(r!==void 0){if(r.delete(te),r.size===0)nn(n);else if(r.size===1){let[i,u]=r.entries().next().value;sn(n,i,u)}}},nn=t=>{if(t.length===0)return;let[e,r]=Xt(t);if(e.delete(r),e.size===0)nn(t.slice(0,-1));else if(e.size===1){let[n,i]=e.entries().next().value;n!==te&&sn(t.slice(0,-1),n,i)}},sn=(t,e,r)=>{if(t.length===0)return;let[n,i]=Xt(t);n.set(i+e,r),n.delete(i)},Xt=t=>t[t.length-1],Zt="or",on="and",Vi="and_not",Ce=class t{constructor(e){if(e?.fields==null)throw new Error('MiniSearch: option "fields" must be provided');let r=e.autoVacuum==null||e.autoVacuum===!0?Kt:e.autoVacuum;this._options=Object.assign(Object.assign(Object.assign({},Ht),e),{autoVacuum:r,searchOptions:Object.assign(Object.assign({},qr),e.searchOptions||{}),autoSuggestOptions:Object.assign(Object.assign({},Gi),e.autoSuggestOptions||{})}),this._index=new Le,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldIds={},this._fieldLength=new Map,this._avgFieldLength=[],this._nextId=0,this._storedFields=new Map,this._dirtCount=0,this._currentVacuum=null,this._enqueuedVacuum=null,this._enqueuedVacuumConditions=qt,this.addFields(this._options.fields)}add(e){let{extractField:r,tokenize:n,processTerm:i,fields:u,idField:l}=this._options,y=r(e,l);if(y==null)throw new Error(`MiniSearch: document does not have ID field "${l}"`);if(this._idToShortId.has(y))throw new Error(`MiniSearch: duplicate ID ${y}`);let o=this.addDocumentId(y);this.saveStoredFields(o,e);for(let a of u){let w=r(e,a);if(w==null)continue;let M=n(w.toString(),a),b=this._fieldIds[a],O=new Set(M).size;this.addFieldLength(o,b,this._documentCount-1,O);for(let E of M){let A=i(E,a);if(Array.isArray(A))for(let C of A)this.addTerm(b,o,C);else A&&this.addTerm(b,o,A)}}}addAll(e){for(let r of e)this.add(r)}addAllAsync(e,r={}){let{chunkSize:n=10}=r,i={chunk:[],promise:Promise.resolve()},{chunk:u,promise:l}=e.reduce(({chunk:y,promise:o},a,w)=>(y.push(a),(w+1)%n===0?{chunk:[],promise:o.then(()=>new Promise(M=>setTimeout(M,0))).then(()=>this.addAll(y))}:{chunk:y,promise:o}),i);return l.then(()=>this.addAll(u))}remove(e){let{tokenize:r,processTerm:n,extractField:i,fields:u,idField:l}=this._options,y=i(e,l);if(y==null)throw new Error(`MiniSearch: document does not have ID field "${l}"`);let o=this._idToShortId.get(y);if(o==null)throw new Error(`MiniSearch: cannot remove document with ID ${y}: it is not in the index`);for(let a of u){let w=i(e,a);if(w==null)continue;let M=r(w.toString(),a),b=this._fieldIds[a],O=new Set(M).size;this.removeFieldLength(o,b,this._documentCount,O);for(let E of M){let A=n(E,a);if(Array.isArray(A))for(let C of A)this.removeTerm(b,o,C);else A&&this.removeTerm(b,o,A)}}this._storedFields.delete(o),this._documentIds.delete(o),this._idToShortId.delete(y),this._fieldLength.delete(o),this._documentCount-=1}removeAll(e){if(e)for(let r of e)this.remove(r);else{if(arguments.length>0)throw new Error("Expected documents to be present. Omit the argument to remove all documents.");this._index=new Le,this._documentCount=0,this._documentIds=new Map,this._idToShortId=new Map,this._fieldLength=new Map,this._avgFieldLength=[],this._storedFields=new Map,this._nextId=0}}discard(e){let r=this._idToShortId.get(e);if(r==null)throw new Error(`MiniSearch: cannot discard document with ID ${e}: it is not in the index`);this._idToShortId.delete(e),this._documentIds.delete(r),this._storedFields.delete(r),(this._fieldLength.get(r)||[]).forEach((n,i)=>{this.removeFieldLength(r,i,this._documentCount,n)}),this._fieldLength.delete(r),this._documentCount-=1,this._dirtCount+=1,this.maybeAutoVacuum()}maybeAutoVacuum(){if(this._options.autoVacuum===!1)return;let{minDirtFactor:e,minDirtCount:r,batchSize:n,batchWait:i}=this._options.autoVacuum;this.conditionalVacuum({batchSize:n,batchWait:i},{minDirtCount:r,minDirtFactor:e})}discardAll(e){let r=this._options.autoVacuum;try{this._options.autoVacuum=!1;for(let n of e)this.discard(n)}finally{this._options.autoVacuum=r}this.maybeAutoVacuum()}replace(e){let{idField:r,extractField:n}=this._options,i=n(e,r);this.discard(i),this.add(e)}vacuum(e={}){return this.conditionalVacuum(e)}conditionalVacuum(e,r){return this._currentVacuum?(this._enqueuedVacuumConditions=this._enqueuedVacuumConditions&&r,this._enqueuedVacuum!=null?this._enqueuedVacuum:(this._enqueuedVacuum=this._currentVacuum.then(()=>{let n=this._enqueuedVacuumConditions;return this._enqueuedVacuumConditions=qt,this.performVacuuming(e,n)}),this._enqueuedVacuum)):this.vacuumConditionsMet(r)===!1?Promise.resolve():(this._currentVacuum=this.performVacuuming(e),this._currentVacuum)}performVacuuming(e,r){return Ze(this,void 0,void 0,function*(){let n=this._dirtCount;if(this.vacuumConditionsMet(r)){let i=e.batchSize||Jt.batchSize,u=e.batchWait||Jt.batchWait,l=1;for(let[y,o]of this._index){for(let[a,w]of o)for(let[M]of w)this._documentIds.has(M)||(w.size<=1?o.delete(a):w.delete(M));this._index.get(y).size===0&&this._index.delete(y),l%i===0&&(yield new Promise(a=>setTimeout(a,u))),l+=1}this._dirtCount-=n}yield null,this._currentVacuum=this._enqueuedVacuum,this._enqueuedVacuum=null})}vacuumConditionsMet(e){if(e==null)return!0;let{minDirtCount:r,minDirtFactor:n}=e;return r=r||Kt.minDirtCount,n=n||Kt.minDirtFactor,this.dirtCount>=r&&this.dirtFactor>=n}get isVacuuming(){return this._currentVacuum!=null}get dirtCount(){return this._dirtCount}get dirtFactor(){return this._dirtCount/(1+this._documentCount+this._dirtCount)}has(e){return this._idToShortId.has(e)}getStoredFields(e){let r=this._idToShortId.get(e);if(r!=null)return this._storedFields.get(r)}search(e,r={}){let{searchOptions:n}=this._options,i=Object.assign(Object.assign({},n),r),u=this.executeQuery(e,r),l=[];for(let[y,{score:o,terms:a,match:w}]of u){let M=a.length||1,b={id:this._documentIds.get(y),score:o*M,terms:Object.keys(w),queryTerms:a,match:w};Object.assign(b,this._storedFields.get(y)),(i.filter==null||i.filter(b))&&l.push(b)}return e===t.wildcard&&i.boostDocument==null||l.sort(Zr),l}autoSuggest(e,r={}){r=Object.assign(Object.assign({},this._options.autoSuggestOptions),r);let n=new Map;for(let{score:u,terms:l}of this.search(e,r)){let y=l.join(" "),o=n.get(y);o!=null?(o.score+=u,o.count+=1):n.set(y,{score:u,terms:l,count:1})}let i=[];for(let[u,{score:l,terms:y,count:o}]of n)i.push({suggestion:u,terms:y,score:l/o});return i.sort(Zr),i}get documentCount(){return this._documentCount}get termCount(){return this._index.size}static loadJSON(e,r){if(r==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJS(JSON.parse(e),r)}static loadJSONAsync(e,r){return Ze(this,void 0,void 0,function*(){if(r==null)throw new Error("MiniSearch: loadJSON should be given the same options used when serializing the index");return this.loadJSAsync(JSON.parse(e),r)})}static getDefault(e){if(Ht.hasOwnProperty(e))return Yt(Ht,e);throw new Error(`MiniSearch: unknown option "${e}"`)}static loadJS(e,r){let{index:n,documentIds:i,fieldLength:u,storedFields:l,serializationVersion:y}=e,o=this.instantiateMiniSearch(e,r);o._documentIds=qe(i),o._fieldLength=qe(u),o._storedFields=qe(l);for(let[a,w]of o._documentIds)o._idToShortId.set(w,a);for(let[a,w]of n){let M=new Map;for(let b of Object.keys(w)){let O=w[b];y===1&&(O=O.ds),M.set(parseInt(b,10),qe(O))}o._index.set(a,M)}return o}static loadJSAsync(e,r){return Ze(this,void 0,void 0,function*(){let{index:n,documentIds:i,fieldLength:u,storedFields:l,serializationVersion:y}=e,o=this.instantiateMiniSearch(e,r);o._documentIds=yield Xe(i),o._fieldLength=yield Xe(u),o._storedFields=yield Xe(l);for(let[w,M]of o._documentIds)o._idToShortId.set(M,w);let a=0;for(let[w,M]of n){let b=new Map;for(let O of Object.keys(M)){let E=M[O];y===1&&(E=E.ds),b.set(parseInt(O,10),yield Xe(E))}++a%1e3===0&&(yield cn(0)),o._index.set(w,b)}return o})}static instantiateMiniSearch(e,r){let{documentCount:n,nextId:i,fieldIds:u,averageFieldLength:l,dirtCount:y,serializationVersion:o}=e;if(o!==1&&o!==2)throw new Error("MiniSearch: cannot deserialize an index created with an incompatible version");let a=new t(r);return a._documentCount=n,a._nextId=i,a._idToShortId=new Map,a._fieldIds=u,a._avgFieldLength=l,a._dirtCount=y||0,a._index=new Le,a}executeQuery(e,r={}){if(e===t.wildcard)return this.executeWildcardQuery(r);if(typeof e!="string"){let b=Object.assign(Object.assign(Object.assign({},r),e),{queries:void 0}),O=e.queries.map(E=>this.executeQuery(E,b));return this.combineResults(O,b.combineWith)}let{tokenize:n,processTerm:i,searchOptions:u}=this._options,l=Object.assign(Object.assign({tokenize:n,processTerm:i},u),r),{tokenize:y,processTerm:o}=l,M=y(e).flatMap(b=>o(b)).filter(b=>!!b).map(Ki(l)).map(b=>this.executeQuerySpec(b,l));return this.combineResults(M,l.combineWith)}executeQuerySpec(e,r){let n=Object.assign(Object.assign({},this._options.searchOptions),r),i=(n.fields||this._options.fields).reduce((A,C)=>Object.assign(Object.assign({},A),{[C]:Yt(n.boost,C)||1}),{}),{boostDocument:u,weights:l,maxFuzzy:y,bm25:o}=n,{fuzzy:a,prefix:w}=Object.assign(Object.assign({},qr.weights),l),M=this._index.get(e.term),b=this.termResults(e.term,e.term,1,e.termBoost,M,i,u,o),O,E;if(e.prefix&&(O=this._index.atPrefix(e.term)),e.fuzzy){let A=e.fuzzy===!0?.2:e.fuzzy,C=A<1?Math.min(y,Math.round(e.term.length*A)):A;C&&(E=this._index.fuzzyGet(e.term,C))}if(O)for(let[A,C]of O){let P=A.length-e.term.length;if(!P)continue;E?.delete(A);let N=w*A.length/(A.length+.3*P);this.termResults(e.term,A,N,e.termBoost,C,i,u,o,b)}if(E)for(let A of E.keys()){let[C,P]=E.get(A);if(!P)continue;let N=a*A.length/(A.length+P);this.termResults(e.term,A,N,e.termBoost,C,i,u,o,b)}return b}executeWildcardQuery(e){let r=new Map,n=Object.assign(Object.assign({},this._options.searchOptions),e);for(let[i,u]of this._documentIds){let l=n.boostDocument?n.boostDocument(u,"",this._storedFields.get(i)):1;r.set(i,{score:l,terms:[],match:{}})}return r}combineResults(e,r=Zt){if(e.length===0)return new Map;let n=r.toLowerCase(),i=Wi[n];if(!i)throw new Error(`Invalid combination operator: ${r}`);return e.reduce(i)||new Map}toJSON(){let e=[];for(let[r,n]of this._index){let i={};for(let[u,l]of n)i[u]=Object.fromEntries(l);e.push([r,i])}return{documentCount:this._documentCount,nextId:this._nextId,documentIds:Object.fromEntries(this._documentIds),fieldIds:this._fieldIds,fieldLength:Object.fromEntries(this._fieldLength),averageFieldLength:this._avgFieldLength,storedFields:Object.fromEntries(this._storedFields),dirtCount:this._dirtCount,index:e,serializationVersion:2}}termResults(e,r,n,i,u,l,y,o,a=new Map){if(u==null)return a;for(let w of Object.keys(l)){let M=l[w],b=this._fieldIds[w],O=u.get(b);if(O==null)continue;let E=O.size,A=this._avgFieldLength[b];for(let C of O.keys()){if(!this._documentIds.has(C)){this.removeTerm(b,C,r),E-=1;continue}let P=y?y(this._documentIds.get(C),r,this._storedFields.get(C)):1;if(!P)continue;let N=O.get(C),V=this._fieldLength.get(C)[b],z=Hi(N,E,this._documentCount,V,A,o),k=n*i*M*P*z,D=a.get(C);if(D){D.score+=k,Ji(D.terms,e);let R=Yt(D.match,r);R?R.push(w):D.match[r]=[w]}else a.set(C,{score:k,terms:[e],match:{[r]:[w]}})}}return a}addTerm(e,r,n){let i=this._index.fetch(n,Qr),u=i.get(e);if(u==null)u=new Map,u.set(r,1),i.set(e,u);else{let l=u.get(r);u.set(r,(l||0)+1)}}removeTerm(e,r,n){if(!this._index.has(n)){this.warnDocumentChanged(r,e,n);return}let i=this._index.fetch(n,Qr),u=i.get(e);u==null||u.get(r)==null?this.warnDocumentChanged(r,e,n):u.get(r)<=1?u.size<=1?i.delete(e):u.delete(r):u.set(r,u.get(r)-1),this._index.get(n).size===0&&this._index.delete(n)}warnDocumentChanged(e,r,n){for(let i of Object.keys(this._fieldIds))if(this._fieldIds[i]===r){this._options.logger("warn",`MiniSearch: document with ID ${this._documentIds.get(e)} has changed before removal: term "${n}" was not present in field "${i}". Removing a document after it has changed can corrupt the index!`,"version_conflict");return}}addDocumentId(e){let r=this._nextId;return this._idToShortId.set(e,r),this._documentIds.set(r,e),this._documentCount+=1,this._nextId+=1,r}addFields(e){for(let r=0;r<e.length;r++)this._fieldIds[e[r]]=r}addFieldLength(e,r,n,i){let u=this._fieldLength.get(e);u==null&&this._fieldLength.set(e,u=[]),u[r]=i;let y=(this._avgFieldLength[r]||0)*n+i;this._avgFieldLength[r]=y/(n+1)}removeFieldLength(e,r,n,i){if(n===1){this._avgFieldLength[r]=0;return}let u=this._avgFieldLength[r]*n-i;this._avgFieldLength[r]=u/(n-1)}saveStoredFields(e,r){let{storeFields:n,extractField:i}=this._options;if(n==null||n.length===0)return;let u=this._storedFields.get(e);u==null&&this._storedFields.set(e,u={});for(let l of n){let y=i(r,l);y!==void 0&&(u[l]=y)}}};Ce.wildcard=Symbol("*");var Yt=(t,e)=>Object.prototype.hasOwnProperty.call(t,e)?t[e]:void 0,Wi={[Zt]:(t,e)=>{for(let r of e.keys()){let n=t.get(r);if(n==null)t.set(r,e.get(r));else{let{score:i,terms:u,match:l}=e.get(r);n.score=n.score+i,n.match=Object.assign(n.match,l),Xr(n.terms,u)}}return t},[on]:(t,e)=>{let r=new Map;for(let n of e.keys()){let i=t.get(n);if(i==null)continue;let{score:u,terms:l,match:y}=e.get(n);Xr(i.terms,l),r.set(n,{score:i.score+u,terms:i.terms,match:Object.assign(i.match,y)})}return r},[Vi]:(t,e)=>{for(let r of e.keys())t.delete(r);return t}},Yi={k:1.2,b:.7,d:.5},Hi=(t,e,r,n,i,u)=>{let{k:l,b:y,d:o}=u;return Math.log(1+(r-e+.5)/(e+.5))*(o+t*(l+1)/(t+l*(1-y+y*n/i)))},Ki=t=>(e,r,n)=>{let i=typeof t.fuzzy=="function"?t.fuzzy(e,r,n):t.fuzzy||!1,u=typeof t.prefix=="function"?t.prefix(e,r,n):t.prefix===!0,l=typeof t.boostTerm=="function"?t.boostTerm(e,r,n):1;return{term:e,fuzzy:i,prefix:u,termBoost:l}},Ht={idField:"id",extractField:(t,e)=>t[e],tokenize:t=>t.split(qi),processTerm:t=>t.toLowerCase(),fields:void 0,searchOptions:void 0,storeFields:[],logger:(t,e)=>{typeof console?.[t]=="function"&&console[t](e)},autoVacuum:!0},qr={combineWith:Zt,prefix:!1,fuzzy:!1,maxFuzzy:6,boost:{},weights:{fuzzy:.45,prefix:.375},bm25:Yi},Gi={combineWith:on,prefix:(t,e,r)=>e===r.length-1},Jt={batchSize:1e3,batchWait:10},qt={minDirtFactor:.1,minDirtCount:20},Kt=Object.assign(Object.assign({},Jt),qt),Ji=(t,e)=>{t.includes(e)||t.push(e)},Xr=(t,e)=>{for(let r of e)t.includes(r)||t.push(r)},Zr=({score:t},{score:e})=>e-t,Qr=()=>new Map,qe=t=>{let e=new Map;for(let r of Object.keys(t))e.set(parseInt(r,10),t[r]);return e},Xe=t=>Ze(void 0,void 0,void 0,function*(){let e=new Map,r=0;for(let n of Object.keys(t))e.set(parseInt(n,10),t[n]),++r%1e3===0&&(yield cn(0));return e}),cn=t=>new Promise(e=>setTimeout(e,t)),qi=/[\n\r\p{Z}\p{P}]+/u;var Re=Q(Ae()),un=(t,e,r,n=!1)=>{let i=(0,Re.useMemo)(()=>n?null:new we(t,r),[t,r,n]);return(0,Re.useMemo)(()=>n||!i?t:e?i.search(e).map(l=>l.item):t,[i,t,e,n])},an=(t,e,r,n=!1)=>{let i=(0,Re.useMemo)(()=>{if(n)return null;let l=new Ce(r);return l.addAll(t),l},[t,r,n]);return(0,Re.useMemo)(()=>n||!i?t:e?i.search(e).map(l=>l):t,[i,t,e,n])};x();_();x();_();var et=Q(Ae()),fn=(t,e=1e3)=>{let r=(0,et.useRef)(!1);return(0,et.useCallback)(()=>{r.current||(r.current=!0,t(),setTimeout(()=>{r.current=!1},e))},[t,e])};x();_();var Me=Q(Ae());function ln(t,e,r=100){let[n,i]=(0,Me.useState)(()=>t),u=(0,Me.useRef)(),l=(0,Me.useCallback)(y=>{i(y),u.current&&clearTimeout(u.current),u.current=setTimeout(()=>{e(y),u.current=void 0},r)},[r,e]);return(0,Me.useEffect)(()=>{u.current&&(clearTimeout(u.current),u.current=void 0),i(t)},[t]),[n,l]}x();_();var Qt=Q(Ae());x();_();function hn(t,e){let r=new WeakMap;return()=>{let n=t();if(n===void 0)return;let i=r.get(n);return i===void 0&&(i=e(n),r.set(n,i)),i}}x();_();J.config({EXPONENTIAL_AT:1e3,DECIMAL_PLACES:78});x();_();x();_();var On=Q(Mn()),me=Q(Sn());(0,On.default)(me.default.localeData("en"),{abbreviations:{thousand:"K",million:"M",billion:"B",trillion:"T"}});var Nn=(t,e="0,0.00%",r=!1)=>`${r&&t>0?"+":""}${(0,me.default)(t).format(t===0?"0%":e)}`;x();_();x();_();x();_();x();_();var $n=Ts;function Ts(t,e){if(typeof t!="function")throw new Error("`callback` should be a function");if(e!==void 0&&typeof e!="function")throw new Error("`resolver` should be a function");var r={},n=function(){var i=Array.prototype.slice.call(arguments),u=e?e.apply(this,i):JSON.stringify(i);return u in r||(r[u]=t.apply(this,i)),r[u]};return n.cache=r,n}var Ms=(...t)=>{let e=t.map(r=>r instanceof Map||r instanceof Set?Array.from(r):r);return JSON.stringify(e)},er=(t,e={forceRefresh:!1})=>{let{resolver:r=Ms}=e,n=$n(t,r),i=function(...u){if(e.forceRefresh){let l=r(...u);delete n.cache[l]}return n.apply(this,u)};return i.cache=n.cache,i};var vs=t=>new J(10).pow(t),pe=er(vs);var En=(t,e)=>e===0?new J(t):new J(t).multipliedBy(pe(e));x();_();var An=(t,e)=>new J(t).div(pe(e));x();_();var Dn=1e-5,Fn="0,0.[00000]",Ss="0,0.[00]a",Os=1e6,it=(t,e=Fn)=>{let r=new J(t);if(r.abs().isGreaterThan(0)&&r.abs().isLessThan(Dn))return`< ${r.isLessThan(0)?"-":""}${Dn}`;let n=(0,me.default)(r.toNumber());return e===Fn&&r.isGreaterThanOrEqualTo(Os)?n.format(Ss):n.format(e)};x();_();var In=(t,e)=>{let r=new J(t).div(pe(e)).toNumber();return it(r)};x();_();x();_();x();_();var st=t=>{let e=t?Math.floor(Math.log10(t)):0,r=e<0?Math.pow(10,1-e):100;return Math.round(t*r)/r};var ot=(t,e={})=>{let r=typeof t=="number"?t:J(t).toNumber(),{includePlusPrefix:n=!1,prefix:i="",infix:u="",suffix:l="",extendedDecimalsForSmallNumbers:y=!1,minimumAmount:o=.01,format:a="0,0.[00]",decimalFormatSmallNumbers:w="0.[00]",roundDecimals:M=!1}=e,b=r<0?"-":"",O=n&&r>0?"+":b,E=M?st(Math.abs(r)):Math.abs(r);if(Math.abs(r)>=o||r===0){let A=Math.abs(r)>1?e.compact?"0.[00]a":a:w,C=(0,me.default)(E).format(A);return`${i}${O}${u}${C}${l}`}else if(y){let A=new J(E).abs().toFormat();return`${i}${O}${u}${A}${l}`}else return`${i}${O}<${u}${new J(o).toFormat()}${l}`};var kn=(t,e={})=>ot(t,{...e,infix:"$",format:e.compact?"0.[00]a":e.format??"0,0.00",decimalFormatSmallNumbers:e.decimalFormatSmallNumbers??"0.00"});x();_();x();_();var Ln=t=>t.replace(/,|\.{2,}/g,".").replace(/^[0]+/g,"0").replace(/[^\d.]/g,"");x();_();var Cn=(t,e=9)=>{let r=(t.match(/\./g)||[]).length;return t.endsWith(".")&&r>1?t.substring(0,t.length-1):t.length===0||t.endsWith(".")||t.endsWith("0")?t:new J(t).decimalPlaces(e).toString()};x();_();var Rn=(t,e)=>new J(t).div(pe(e)).toString();x();_();x();_();x();_();var zn=(t,e)=>Array.apply(0,new Array(Math.ceil(t.length/e))).map((r,n)=>t.slice(n*e,(n+1)*e));x();_();x();_();x();_();x();_();var Pn=t=>{if(t.length!==0)return t[t.length-1]};x();_();function Bn(t,e){let r=[];for(let n=0;n<t.length;n++)r.push(t[n]),n<t.length-1&&r.push(e);return r}x();_();function Un(t){return t!=null}x();_();x();_();x();_();var jn=t=>[...Array(t).keys()];x();_();var Ns=(t,e)=>t+e,Vn=t=>t.reduce(Ns,0);x();_();x();_();var ct=t=>!!t;x();_();var ut=class t{constructor(e){this.buffer=[];this.pos=0;if(e<0)throw new RangeError("Invalid size.");this.size=e}static fromArray(e,r=0){let n=new t(r);return n.fromArray(e,r===0),n}getSize(){return this.size}getPos(){return this.pos}getBufferLength(){return this.buffer.length}add(...e){e.forEach(r=>{this.buffer[this.pos]=r,this.pos=(this.pos+1)%this.size})}get(e){if(e<0&&(e+=this.buffer.length),!(e<0||e>this.buffer.length))return this.buffer.length<this.size?this.buffer[e]:this.buffer[(this.pos+e)%this.size]}getFirst(){return this.get(0)}getLast(){return this.get(-1)}getFirstN(e){return e===0?[]:e<0?this.getLastN(-e):this.toArray().slice(0,e)}getLastN(e){return e===0?[]:e<0?this.getFirstN(-e):this.toArray().slice(-e)}remove(e,r=1){if(e<0&&(e+=this.buffer.length),e<0||e>this.buffer.length)return[];let n=this.toArray(),i=n.splice(e,r);return this.fromArray(n),i}removeFirst(){return this.remove(0)[0]}removeLast(){return this.remove(-1)[0]}toArray(){return this.buffer.slice(this.pos).concat(this.buffer.slice(0,this.pos))}fromArray(e,r=!1){if(!Array.isArray(e))throw new TypeError("Input value is not an array.");r&&this.resize(e.length),this.size!==0&&(this.buffer=e.slice(-this.size),this.pos=this.buffer.length%this.size)}clear(){this.buffer=[],this.pos=0}resize(e){if(e<0)throw new RangeError("The size does not allow negative values.");if(e===0)this.clear();else if(e!==this.size){let r=this.toArray();this.fromArray(r.slice(-e)),this.pos=this.buffer.length%e}this.size=e}isFull(){return this.buffer.length===this.size}isEmpty(){return this.buffer.length===0}};x();_();function Wn(t){return tr.any().array().transform(e=>e.map(r=>{let n=t.safeParse(r);return n.success?n.data:void 0}).filter(ct)).catch([])}x();_();x();_();var ks=new Error("Unsupported path."),Yn=(t,e=ks)=>{throw e};x();_();x();_();x();_();function Hn(t,e,r){let n=e.split("."),i=t;for(let u of n)if(i&&typeof i=="object"&&u in i)i=i[u];else return;return r?r(i):i}x();_();function Kn(t,e){return t in e}x();_();x();_();function at(t){return new Promise(e=>setTimeout(e,t))}x();_();function Cs(t,e){let r=typeof e.interval=="function"?e.interval:()=>e.interval;if(typeof t!="function")throw new Error("Invalid arguments for retryInternal");let n=1;async function i(){try{return await t()}catch(u){if(n++<e.times&&e.errorFilter(u))return await at(r(n-1)),i();throw u}}return i()}async function Gn(t,e,r=3,n=2e3,i){return Cs(t,{times:r,interval:i?()=>n:u=>n*Math.pow(2,u-1),errorFilter:e})}x();_();var Jn=async(t,e)=>new Promise((r,n)=>{let i=setTimeout(()=>{n("Promise timed out")},e);t.then(u=>{r(u),clearTimeout(i)}).catch(n)});x();_();function Rs(t){return`https://www.google.com/s2/favicons?domain=${t}&sz=256`}var sd=()=>{};export{J as a,zs as b,zn as c,Pn as d,Bn as e,Un as f,jn as g,Vn as h,ct as i,ut as j,Wn as k,Yn as l,Hn as m,Kn as n,at as o,Gn as p,Jn as q,or as r,cr as s,ur as t,ar as u,ir as v,sr as w,De as x,Oe as y,xt as z,Mr as A,hr as B,Te as C,vr as D,gr as E,Sr as F,je as G,Or as H,Nr as I,$r as J,Er as K,Nt as L,Ar as M,Fr as N,Ir as O,Lr as P,Cr as Q,We as R,Rr as S,fn as T,ln as U,zr as V,we as W,un as X,an as Y,hn as Z,Sn as _,Nn as $,Ln as aa,Cn as ba,er as ca,pe as da,En as ea,An as fa,Rn as ga,it as ha,In as ia,ot as ja,kn as ka,Rs as la,sd as ma};
/*! Bundled license information:

numeral/numeral.js:
  (*! @preserve
   * numeral.js
   * version : 2.0.6
   * author : Adam Draper
   * license : MIT
   * http://adamwdraper.github.com/Numeral-js/
   *)
*/
//# sourceMappingURL=chunk-ALUTR72U.js.map
