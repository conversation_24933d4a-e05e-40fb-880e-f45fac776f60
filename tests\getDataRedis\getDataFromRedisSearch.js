// Script to connect to RedisSearch using config from configRedis.json, get data for key 'solana_tokens',
// filter only tokens in tokenList.json, and sum their marketcap.

const {Redis} = require('ioredis');
const fs = require('fs');
const path = require('path');

// Load config
const config = require('./configRedis.json');
const redisConfig = config.redis_search; // Lấy thông tin connect redisearch từ configRedis.json

async function main() {
  const key = 'solana_tokens';

  // Load token list from file
  const tokenListPath = path.join(__dirname, 'tokenList.json');
  let tokenList;
  try {
    tokenList = JSON.parse(fs.readFileSync(tokenListPath, 'utf8'));
  } catch (e) {
    console.error('Failed to read tokenList.json:', e.message || e);
    process.exit(1);
  }
  const tokenSet = new Set(tokenList);

  // Connect to RedisSearch with username and password (nếu có)
  // redis_search có thể chỉ có api-key, nếu có thêm url/username/password thì bổ sung vào configRedis.json
  // Ví dụ: { "redis_search": { "url": "...", "username": "...", "password": "...", "x-api-key": "..." } }
  if (!redisConfig.url) {
    console.error('redis_search.url is missing in configRedis.json');
    process.exit(1);
  }


  const client = new Redis({
    host: redisConfig.url, // url ở đây là host, ví dụ: 'localhost'
    port: redisConfig.port,
    username: redisConfig.username,
    password: redisConfig.password,
    enableReadyCheck: false
  });

  const closeClient = async () => {
    try {
      if (client.status === 'ready' || client.status === 'connecting') await client.quit();
    } catch (e) {
      // ignore on shutdown
    }
  };

  process.on('SIGINT', async () => {
    await closeClient();
    process.exit(1);
  });
  process.on('SIGTERM', async () => {
    await closeClient();
    process.exit(1);
  });

  try {
    // Tìm kiếm từng key theo format solana_tokens:<tokenAddress> với tokenAddress từ tokenList.json
    const values = [];
    for (const tokenAddress of tokenList) {
      const redisKey = `solana_tokens:${tokenAddress}`;
   
      const type = await client.type(redisKey);
      if (type === 'hash') {
        const data = await client.hgetall(redisKey);
        // Nếu data là object không rỗng, thêm vào values
        if (data && Object.keys(data).length > 0) {
          // Nếu các value là JSON string, parse chúng
          let parsed = data;
          // Nếu chỉ có 1 trường, có thể là value dạng string
          if (Object.keys(data).length === 1 && typeof Object.values(data)[0] === 'string') {
            try {
              parsed = JSON.parse(Object.values(data)[0]);
            } catch {
              parsed = data;
            }
          }
          values.push(parsed);
        }
      }
    }


    // Tính tổng marketcap của các token đã lọc
    let totalMarketcap = 0;
    for (const item of values) {
      if (item && typeof item === 'object' && item.marketcap != null && !isNaN(Number(item.marketcap))) {
        totalMarketcap += Number(item.marketcap);
      }
    }

    console.log(`Tổng marketcap của các token trong tokenList.json: ${totalMarketcap}`);

    // Optionally, save filtered tokens to file
    const outputPath = path.join(__dirname, 'solanaTokensFiltered.json');
    fs.writeFileSync(outputPath, JSON.stringify(filteredTokens, null, 2), 'utf8');
    console.log(`Saved filtered tokens to ${outputPath}`);
  } catch (error) {
    console.error('Failed to read from Redis:', error.message || error);
    process.exitCode = 1;
  } finally {
    await closeClient();
  }
}

main();




