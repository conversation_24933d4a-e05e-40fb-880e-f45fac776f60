{"commandAdd": "Tambah", "commandAccept": "Terima", "commandApply": "<PERSON><PERSON><PERSON>", "commandApprove": "Luluskan", "commandAllow": "<PERSON><PERSON><PERSON>", "commandBack": "<PERSON><PERSON><PERSON>", "commandBuy": "Bel<PERSON>", "commandCancel": "Batalkan", "commandClaim": "<PERSON><PERSON><PERSON><PERSON>", "commandClaimReward": "<PERSON><PERSON><PERSON> ganjaran anda", "commandClear": "Padamkan", "commandClose": "<PERSON><PERSON><PERSON>", "commandConfirm": "<PERSON><PERSON><PERSON>", "commandConnect": "Sambungkan", "commandContinue": "Teruskan", "commandConvert": "<PERSON><PERSON>", "commandCopy": "<PERSON><PERSON>", "commandCopyAddress": "<PERSON><PERSON>", "commandCopyTokenAddress": "<PERSON><PERSON> alamat token", "commandCreate": "Cip<PERSON>", "commandCreateTicket": "<PERSON><PERSON><PERSON>", "commandDeny": "<PERSON><PERSON>", "commandDismiss": "Singkirkan", "commandDontAllow": "<PERSON><PERSON>", "commandDownload": "<PERSON>at turun", "commandEdit": "Sunting", "commandEditProfile": "Sunting Profil", "commandEnableNow": "<PERSON><PERSON><PERSON><PERSON>", "commandFilter": "<PERSON><PERSON>", "commandFollow": "<PERSON><PERSON><PERSON>", "commandHelp": "Bantuan", "commandLearnMore": "<PERSON><PERSON><PERSON> lebih lan<PERSON>t", "commandLearnMore2": "<PERSON><PERSON><PERSON>", "commandMint": "Kilang syiling", "commandMore": "<PERSON><PERSON><PERSON>", "commandNext": "Seterusnya", "commandNotNow": "<PERSON><PERSON><PERSON>", "commandOpen": "<PERSON><PERSON>", "commandOpenSettings": "<PERSON><PERSON>", "commandPaste": "<PERSON>l", "commandReceive": "Terima", "commandReconnect": "Sambung semula", "commandRecordVideo": "Rakam Video", "commandRequest": "<PERSON><PERSON><PERSON><PERSON>", "commandRetry": "Cuba semula", "commandReview": "<PERSON><PERSON>", "commandRevoke": "Membatalkan", "commandSave": "Simpan", "commandScanQRCode": "<PERSON><PERSON><PERSON>", "commandSelect": "<PERSON><PERSON><PERSON>", "commandSelectMedia": "Pilih Media", "commandSell": "<PERSON><PERSON>", "commandSend": "<PERSON><PERSON>", "commandShare": "Kong<PERSON>", "commandShowBalance": "Tunjukkan <PERSON>", "commandSign": "Tandatangan", "commandSignOut": "Sign Out", "commandStake": "<PERSON><PERSON><PERSON>", "commandMintLST": "Cetak JitoSOL", "commandSwap": "<PERSON><PERSON>", "commandSwapAgain": "<PERSON><PERSON>", "commandTakePhoto": "Ambil Gambar", "commandTryAgain": "Cuba Lagi", "commandViewTransaction": "<PERSON><PERSON>", "commandReportAsNotSpam": "Laporkan sebagai bukan spam", "commandReportAsSpam": "Laporkan sebagai spam", "commandPin": "<PERSON><PERSON><PERSON>", "commandBlock": "Sekat", "commandUnblock": "Nyahsekat", "commandUnstake": "Batalkan stake", "commandUnpin": "<PERSON><PERSON><PERSON>", "commandHide": "Sembunyikan", "commandUnhide": "Paparkan", "commandBurn": "<PERSON><PERSON><PERSON><PERSON>", "commandReport": "<PERSON><PERSON><PERSON>", "commandView": "Lihat", "commandProceedAnywayUnsafe": "Teruskan bagaimanapun (tidak selamat)", "commandUnfollow": "<PERSON><PERSON><PERSON><PERSON>", "commandUnwrap": "Membuka", "commandConfirmUnsafe": "Sahkan (tidak selamat)", "commandYesConfirmUnsafe": "Ya, sahkan (tidak selamat)", "commandConfirmAnyway": "<PERSON><PERSON><PERSON>", "commandReportIssue": "Laporkan <PERSON>", "commandSearch": "<PERSON><PERSON>", "commandShowMore": "<PERSON>n<PERSON>k lebih lagi", "commandShowLess": "<PERSON><PERSON><PERSON><PERSON> kurang", "pastParticipleClaimed": "Dituntut", "pastParticipleCompleted": "Lengka<PERSON>", "pastParticipleCopied": "Di<PERSON>in", "pastParticipleDone": "Se<PERSON><PERSON>", "pastParticipleDisabled": "Lumpuhkan", "pastParticipleRequested": "<PERSON><PERSON><PERSON>", "nounName": "<PERSON><PERSON>", "nounNetwork": "<PERSON><PERSON><PERSON><PERSON>", "nounNetworkFee": "<PERSON><PERSON>", "nounSymbol": "Simbol", "nounType": "<PERSON><PERSON>", "nounDescription": "<PERSON><PERSON><PERSON>", "nounYes": "Ya", "nounNo": "Tidak", "amount": "<PERSON><PERSON><PERSON>", "limit": "Had", "new": "<PERSON><PERSON><PERSON>", "gotIt": "<PERSON><PERSON><PERSON>", "internal": "<PERSON><PERSON>", "reward": "Ganjaran", "seeAll": "<PERSON><PERSON>a", "seeLess": "<PERSON><PERSON> se<PERSON>", "viewAll": "<PERSON><PERSON>a", "homeTab": "<PERSON><PERSON>", "collectiblesTab": "Kolektibel", "swapTab": "<PERSON><PERSON><PERSON><PERSON>", "activityTab": "Aktiviti", "exploreTab": "Penjelajah", "accountHeaderConnectedInterpolated": "Anda bersambung kepada {{origin}}", "accountHeaderConnectedToSite": "Anda bersambung kepada laman ini", "accountHeaderCopyToClipboard": "<PERSON>in ke papan klip", "accountHeaderNotConnected": "Anda tidak bersambung kepada", "accountHeaderNotConnectedInterpolated": "Anda tidak bersambung kepada {{origin}}", "accountHeaderNotConnectedToSite": "Anda tidak bersambung kepada laman ini", "accountWithoutEnoughSolActionButtonCancel": "Batalkan", "accountWithoutEnoughSolPrimaryText": "SOL yang tidak mencukupi", "accountWithoutEnoughSolSecondaryText": "Akaun yang terlibat dalam urus niaga ini tidak mempunyai SOL yang mencukupi. Akaun tersebut mungkin kepunyaan anda atau orang lain. Urus niaga ini akan berbalik jika diserahkan.", "accountSwitcher": "<PERSON><PERSON><PERSON>", "addAccountHardwareWalletPrimaryText": "Sambungkan Dompet Perkakasan", "addAccountHardwareWalletSecondaryText": "<PERSON><PERSON><PERSON> dompet perka<PERSON>an Ledger anda", "addAccountHardwareWalletSecondaryTextMobile": "Gunakan dompet {{supportedHardwareWallets}} anda", "addAccountSeedVaultWalletPrimaryText": "Sambungkan Kekubah Selamat", "addAccountSeedVaultWalletSecondaryText": "<PERSON><PERSON><PERSON> dompet da<PERSON>ada Seed <PERSON>", "addAccountImportSeedPhrasePrimaryText": "Import Rangkai Kata Pemulihan <PERSON>", "addAccountImportSeedPhraseSecondaryText": "Import akaun daripada dompet lain", "addAccountImportWalletPrimaryText": "Import Kunci <PERSON>", "addAccountImportWalletSecondaryText": "Import akaun rantai tunggal", "addAccountImportWalletSolanaSecondaryText": "Import kunci peribadi <PERSON>", "addAccountLimitReachedText": "<PERSON><PERSON> te<PERSON> had akaun {{accountsCount}} dalam Phantom. <PERSON><PERSON> singkirkan akaun yang tidak digunakan sebelum menambah akaun yang lain.", "addAccountNoSeedAvailableText": "Anda tiada frasa seed yang ada. Sila import seed yang ada untuk men<PERSON><PERSON><PERSON> akaun.", "addAccountNewWalletPrimaryText": "<PERSON><PERSON><PERSON>", "addAccountNewWalletSecondaryText": "<PERSON><PERSON><PERSON> alamat dompet baharu", "addAccountNewMultiChainWalletSecondaryText": "<PERSON><PERSON> akaun berbilang rantai baharu", "addAccountNewSingleChainWalletSecondaryText": "<PERSON><PERSON> akaun baharu", "addAccountPrimaryText": "Tambah / Sambung Dompet", "addAccountSecretPhraseLabel": "Rangkai Kata Rahsia", "addAccountSeedLabel": "Seed", "addAccountSeedIDLabel": "ID Seed", "addAccountSecretPhraseDefaultLabel": "Rangkai Kata Rahsia {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON><PERSON> {{number}}", "addAccountZeroAccountsForSeed": "0 akaun", "addAccountShowAccountForSeed": "Tunjukkan 1 akaun", "addAccountShowAccountsForSeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{numOfAccounts}}", "addAccountHideAccountForSeed": "Sembunyikan 1 akaun", "addAccountHideAccountsForSeed": "<PERSON><PERSON><PERSON><PERSON><PERSON> akaun {{numOfAccounts}}", "addAccountSelectSeedDescription": "<PERSON><PERSON><PERSON> baharu anda akan dijana da<PERSON>ada <PERSON> Kata Rahs<PERSON> ini", "addAccountNumAccountsForSeed": "{{numOfAccounts}} akaun", "addAccountOneAccountsForSeed": "1 akaun", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON><PERSON>", "addAccountReadOnly": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAccountReadOnlySecondaryText": "<PERSON><PERSON> sebarang alamat dompet awam", "addAccountSolanaAddress": "<PERSON><PERSON><PERSON>", "addAccountEVMAddress": "Alamat EVM", "addAccountBitcoinAddress": "Alamat Bitcoin", "addAccountCreateSeedTitle": "<PERSON><PERSON><PERSON> akaun baharu", "addAccountCreateSeedExplainer": "<PERSON><PERSON> anda masih tidak mempunyai rangkai kata rahsia! Untuk mencipta dompet baharu, kita akan menjanakan rangkai kata pemulihan bagi anda. Catatkan ini dan rahsiakannya.", "addAccountSecretPhraseHeader": "Rangkai Kata Rahsia Anda", "addAccountNoSecretPhrases": "<PERSON><PERSON>da <PERSON> Kata Rahsia Tersedia", "addAccountImportAccountActionButtonImport": "Import", "addAccountImportAccountDuplicatePrivateKey": "Akaun ini sudah ada dalam dompet anda", "addAccountImportAccountIncorrectFormat": "Format yang salah", "addAccountImportAccountInvalidPrivateKey": "<PERSON><PERSON><PERSON> Tidak Sah", "addAccountImportAccountName": "<PERSON><PERSON>", "addAccountImportAccountPrimaryText": "Import Kunci <PERSON>", "addAccountImportAccountPrivateKey": "<PERSON><PERSON><PERSON> per<PERSON>", "addAccountImportAccountPublicKey": "<PERSON><PERSON>t atau Domain", "addAccountImportAccountPrivateKeyRequired": "<PERSON><PERSON><PERSON> peribadi diperlukan", "addAccountImportAccountNameRequired": "<PERSON><PERSON>", "addAccountImportAccountPublicKeyRequired": "<PERSON><PERSON><PERSON>", "addAccountImportAccountDuplicateAddress": "<PERSON><PERSON><PERSON> ini sudah ada dalam dompet anda", "addAddressAddressAlreadyAdded": "<PERSON><PERSON><PERSON> sudah ditambah", "addAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON> sudah ada", "addAddressAddressInvalid": "<PERSON><PERSON><PERSON> adalah tidak sah", "addAddressAddressIsRequired": "<PERSON><PERSON><PERSON>", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "Label diperlukan", "addAddressLabelPlaceholder": "Label", "addAddressPrimaryText": "Tam<PERSON> Alamat", "addAddressToast": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountCostLabelInterpolated": "Kos ini ialah {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "Anda sudah mengambil akaun token ini", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON> yang tidak men<PERSON>", "createAssociatedTokenAccountErrorInvalidMint": "<PERSON><PERSON><PERSON> kilang wang tidak sah", "createAssociatedTokenAccountErrorInvalidName": "<PERSON>a tidak sah", "createAssociatedTokenAccountErrorInvalidSymbol": "Simbol tidak sah", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Kita tidak dapat mencipta akaun token anda. Sila cuba lagi.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "<PERSON><PERSON> men<PERSON>pta akaun", "createAssociatedTokenAccountErrorUnableToSendMessage": "Kita tidak dapat menghantar urus niaga anda.", "createAssociatedTokenAccountErrorUnableToSendTitle": "<PERSON><PERSON> men<PERSON> urus niaga", "createAssociatedTokenAccountInputPlaceholderMint": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderName": "<PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderSymbol": "Simbol", "createAssociatedTokenAccountLoadingMessage": "<PERSON>a mencipta akaun token anda.", "createAssociatedTokenAccountLoadingTitle": "<PERSON><PERSON><PERSON> akaun token", "createAssociatedTokenAccountPageHeader": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountSuccessMessage": "A<PERSON>un token anda berjaya dicipta!", "createAssociatedTokenAccountSuccessTitle": "Akaun token dicipta", "createAssociatedTokenAccountViewTransaction": "<PERSON><PERSON> urus niaga", "assetDetailRecentActivity": "Aktiviti Terbaru", "assetDetailStakeSOL": "SOL Stake", "assetDetailUnknownToken": "Token Tidak Diketahui", "assetDetailUnwrapAll": "Buat <PERSON>al Semu<PERSON>", "assetDetailUnwrappingSOL": "Membuka SOL", "assetDetailUnwrappingSOLFailed": "Membuka SOL telah gagal", "assetDetailViewOnExplorer": "<PERSON>hat pada {{explorer}}", "assetDetailViewOnExplorerDefaultExplorer": "Penjelajah", "assetDetailSaveToPhotos": "Simpan ke Foto", "assetDetailSaveToPhotosToast": "Disimpan ke Foto", "assetDetailPinCollection": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailUnpinCollection": "Tanggalkan Cemat <PERSON>", "assetDetailHideCollection": "Sembunyikan Koleksi", "assetDetailUnhideCollection": "Memperlihatkan Koleksi", "assetDetailTokenNameLabel": "<PERSON><PERSON>", "assetDetailNetworkLabel": "<PERSON><PERSON><PERSON><PERSON>", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "<PERSON><PERSON>", "collectibleDetailSetAsAvatar": "Tetapkan sebagai Avatar", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Set Avatar", "collectibleDetailShare": "Kongsikan Kolektibel", "assetDetailTokenAddressCopied": "<PERSON><PERSON><PERSON> disalin", "assetDetailStakingLabel": "Staking", "assetDetailAboutLabel": "Mengenai {{fungibleName}}", "assetDetailPriceDetail": "<PERSON><PERSON><PERSON>", "assetDetailHighlights": "Soroton", "assetDetailAllTimeReturn": "<PERSON><PERSON><PERSON>", "assetDetailAverageCost": "<PERSON><PERSON>", "assetDetailPriceHistoryUnavailable": "<PERSON><PERSON><PERSON> harga tidak tersedia untuk token ini", "assetDetailPriceHistoryInsufficientData": "<PERSON><PERSON><PERSON> harga tidak tersedia untuk julat masa ini", "assetDetailPriceDataUnavailable": "Data harga tidak tersedia", "assetDetailPriceHistoryError": "<PERSON><PERSON> sejarah harga", "assetDetailPriceHistoryNow": "<PERSON><PERSON>", "assetDetailTimeFrame1D": "1H", "assetDetailTimeFrame24h": "<PERSON><PERSON> 24j", "assetDetailTimeFrame1W": "1M", "assetDetailTimeFrame1M": "1B", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "SEMUA", "sendAssetAmountLabelInterpolated": "{{amount}} {{tokenSymbol}} yang tersedia", "fiatRampQuotes": "Sebut harga", "fiatRampNewQuote": "<PERSON><PERSON> harga baharu", "assetListSelectToken": "<PERSON><PERSON><PERSON>", "assetListSearch": "<PERSON><PERSON>...", "assetListUnknownToken": "Token Tidak Diketahui", "buyFlowHealthWarning": "Sesetengah penyedia pembayaran kami mengalami kesesakan lalu lintas. Deposit mungkin tertangguh selama beberapa jam.", "assetVisibilityUnknownToken": "Token Tidak Diketahui", "buyAssetInterpolated": "Beli {{tokenSymbol}}", "buyAssetScreenMaxPurchasePriceInterpolated": "Pembelian maksimum ialah {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Pembelian minimum ialah {{amount}}", "buyNoAssetsAvailable": "Tiada aset Ethereum atau Polygon yang tersedia", "buyThirdPartyScreenPaymentMethodSelector": "<PERSON><PERSON>", "buyThirdPartyScreenPaymentMethod": "<PERSON><PERSON><PERSON> ka<PERSON>h pem<PERSON>", "buyThirdPartyScreenChoseQuote": "<PERSON><PERSON><PERSON><PERSON> jumlah yang sah untuk sebut harga", "buyThirdPartyScreenProviders": "Penyedia", "buyThirdPartyScreenPaymentMethodTitle": "<PERSON><PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodEmptyState": "<PERSON><PERSON>da kaedah pembayaran yang tersedia di wilayah anda", "buyThirdPartyScreenPaymentMethodFooter": "Bayaran dikuasakan oleh rakan kong<PERSON> rang<PERSON>an. <PERSON><PERSON> mungkin berbeza. Sesetengah kaedah pembayaran tidak boleh didapati di wilayah anda.", "buyThirdPartyScreenProvidersEmptyState": "Tiada penyedia yang tersedia di wilayah anda", "buyThirdPartyScreenLoadingQuote": "Memuat sebut harga...", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON> sebut harga", "gasEstimationErrorWarning": "Terdapat masalah menganggar yuran untuk urus niaga ini. <PERSON>a mungkin gagal.", "gasEstimationCouldNotFetch": "Tidak dapat mengambil anggaran gas", "networkFeeCouldNotFetch": "Tidak dapat mengambil yuran rang<PERSON>an", "nativeTokenBalanceErrorWarning": "<PERSON>rda<PERSON>t masalah mendapat baki token anda untuk urus niaga ini. <PERSON>a mungkin gagal.", "blocklistOriginCommunityDatabaseInterpolated": "<PERSON>n ini telah ditandakan sebagai sebahagian daripada <1>pangkalan data dikekalkan oleh komuniti</1> laman web memancing data dan penipuan yang diketahui. <PERSON>ka anda percaya laman tersebut telah silap ditandakan, <3>sila kemukakan isu</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} disekat!", "blocklistOriginIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON> amaran ini, ambil saya ke {{domainName}} bagaimanapun.", "blocklistOriginSiteIsMalicious": "Phantom percaya laman web ini berniat jahat dan tidak selamat untuk digunakan.", "blocklistOriginThisDomain": "domain ini", "blocklistProceedAnyway": "<PERSON><PERSON><PERSON><PERSON> amaran, sambung bagaimanapun", "maliciousTransactionWarning": "Phantom percaya laman web ini berniat jahat dan tidak selamat untuk digunakan. Kita telah melumpuhkan keupayaan untuk log masuk untuk melindungi anda dan dana anda.", "maliciousTransactionWarningIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON> amaran, sambung bagaimanapun", "maliciousTransactionWarningTitle": "<PERSON><PERSON> niaga ditandai!", "maliciousRequestBlockedTitle": "<PERSON><PERSON><PERSON><PERSON> disekat", "maliciousRequestWarning": "<PERSON>n web ini dikenal pasti sebagai berniat jahat. Ia mungkin cuba mencuri dana anda atau memperdaya anda supaya mengesahkan permintaan menipu.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON> anda, Phantom telah menyekat permintaan ini.", "maliciousRequestBlocked": "<PERSON><PERSON> anda, Phantom telah menyekat permintaan ini.", "maliciousRequestFrictionDescription": "Meneruskan adalah tidak selamat, oleh itu Phantom menyekat permintaan ini. Anda harus menutup dialog ini.", "maliciousRequestAcknowledge": "<PERSON>a faham bahawa saya boleh kehilangan semua dana saya dengan menggunakan laman web ini.", "maliciousRequestAreYouSure": "<PERSON>kah anda pasti?", "siwErrorPopupTitle": "<PERSON><PERSON><PERSON><PERSON>", "siwParseErrorDescription": "Permintaan tandatangan aplikasi tidak boleh ditunjukkan disebabkan oleh pemformatan.", "siwVerificationErrorDescription": "Terdapat 1 atau lebih ralat(-ralat) dengan permintaan tandatangan mesej. <PERSON><PERSON> keselamatan anda, sila pastikan anda menggunakan aplikasi yang betul dan cuba lagi.", "siwErrorPagination": "{{n}} da<PERSON>ada {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Amaran: alamat aplikasi tidak padan dengan alamat yang disediakan untuk ditandatangani.", "siwErrorMessage_DOMAIN_MISMATCH": "Amaran: domain aplikasi tidak padan dengan domain yang disediakan untuk pengesahan.", "siwErrorMessage_URI_MISMATCH": "Amaran: nama hos URI tidak padan dengan domain.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Amaran: ID rantai tidak padan dengan ID rantai yang disediakan untuk pengesahan.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Amaran: ta<PERSON>h penerbitan mesej adalah terlalu lama dahulu.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Amaran: ta<PERSON>h penerbitan mesej adalah terlalu jauh ke masa depan.", "siwErrorMessage_EXPIRED": "Amaran: mesej telah tamat tempoh.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Amaran: me<PERSON>j akan tamat tempoh sebelum penerbitan.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Amaran: me<PERSON>j akan tamat tempoh sebelum ia menjadi sah.", "siwErrorShowErrorDetails": "<PERSON><PERSON><PERSON><PERSON><PERSON> butiran ralat", "siwErrorHideErrorDetails": "Sembunyi<PERSON> butiran ralat", "siwErrorIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON> amaran, sambung bagaimanapun", "siwsTitle": "Permintaan <PERSON>", "siwsPermissions": "Keizinan", "siwsAgreement": "<PERSON><PERSON><PERSON>", "siwsAdvancedDetails": "<PERSON><PERSON><PERSON>", "siwsAlternateStatement": "{{domain}} mahu anda log masuk dengan akaun <PERSON> anda:\n{{address}}", "siwsFieldLable_domain": "Domain", "siwsFieldLable_address": "<PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "<PERSON><PERSON><PERSON>", "siwsFieldLable_chainId": "ID rantai", "siwsFieldLable_nonce": "Nombor diguna sekali saja", "siwsFieldLable_issuedAt": "Dikeluarkan Di", "siwsFieldLable_expirationTime": "Tamat Tempoh Pada", "siwsFieldLable_requestId": "ID Permintaan", "siwsFieldLable_resources": "Sumber", "siwsVerificationErrorDescription": "Permintaan log masuk adalah tidak sah. Ini bermakna laman tersebut tidak selamat atau pembangun telah membuat kesilapan semasa menghantar permintaan.", "siwsErrorNumIssues": "{{n}} isu", "siwsErrorMessage_CHAIN_ID_MISMATCH": "ID rantai ini tidak padan dengan rangkaian yang anda menggunakan.", "siwsErrorMessage_DOMAIN_MISMATCH": "Domain ini bukan domain yang anda log masuk.", "siwsErrorMessage_URI_MISMATCH": "URI ini bukan URI yang anda log masuk.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "<PERSON><PERSON><PERSON> penerbitan mesej adalah terlalu lama dahulu.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "<PERSON><PERSON><PERSON> penerbitan mesej adalah terlalu jauh ke masa depan.", "siwsErrorMessage_EXPIRED": "<PERSON><PERSON><PERSON> telah tamat tempoh.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "<PERSON><PERSON><PERSON> akan tamat tempoh sebelum penerbitan.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "<PERSON><PERSON><PERSON> akan tamat tempoh sebelum ia menjadi sah.", "changeLockTimerPrimaryText": "Penjaga Masa Kunci Automatik", "changeLockTimerSecondaryText": "<PERSON><PERSON>a lamakah kita menunggu untuk mengunci dompet anda selepas ia melahu?", "changeLockTimerToast": "Penjaga masa kunci automatik dikemas kini", "changePasswordConfirmNewPassword": "<PERSON><PERSON><PERSON> kata laluan baharu", "changePasswordCurrentPassword": "<PERSON><PERSON> la<PERSON>an se<PERSON>a", "changePasswordErrorIncorrectCurrentPassword": "<PERSON>a la<PERSON>an semasa salah", "changePasswordErrorGeneric": "Se<PERSON><PERSON>u yang tidak kena berlaku, sila cuba lagi kemudian", "changePasswordNewPassword": "<PERSON><PERSON> la<PERSON>an baharu", "changePasswordPrimaryText": "<PERSON><PERSON> kata la<PERSON>an", "changePasswordToast": "<PERSON>a laluan dikemas kini", "collectionsSpamCollections": "<PERSON><PERSON><PERSON><PERSON>", "collectionsHiddenCollections": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesReportAsSpam": "Laporkan sebagai Spam", "collectiblesReportAsSpamAndHide": "Laporkan sebagai Spam dan Se<PERSON>", "collectiblesReportAsNotSpam": "Laporkan sebagai Bukan Spam", "collectiblesReportAsNotSpamAndUnhide": "Tidak sembunyikan dan laporkan bukan spam", "collectiblesReportNotSpam": "<PERSON><PERSON><PERSON>", "collectionsManageCollectibles": "Uruskan senarai kolektibel", "collectibleDetailDescription": "<PERSON><PERSON><PERSON>", "collectibleDetailProperties": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailOrdinalInfo": "Maklumat Ordinal", "collectibleDetailRareSatsInfo": "Maklumat Rare Sats", "collectibleDetailSatsInUtxo": "Sats di UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} sats", "collectibleDetailSatNumber": "Nombor Sat", "collectibleDetailSatName": "<PERSON><PERSON>", "collectibleDetailInscriptionId": "ID Inskripsi", "collectibleDetailInscriptionNumber": "Nombor Inskripsi", "collectibleDetailStandard": "Piawai", "collectibleDetailCreated": "<PERSON><PERSON><PERSON>", "collectibleDetailViewOnExplorer": "<PERSON>hat pada {{explorer}}", "collectibleDetailList": "<PERSON><PERSON><PERSON>", "collectibleDetailSellNow": "<PERSON><PERSON> {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Mengosongkan Bitcoin ganti", "collectibleDetailUtxoSplitterCtaSubtitle": "Anda ada {{value}} BTC untuk ditemui", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "<PERSON>gi melindungi dana anda, kita mencegah BTC dalam UTXO dengan Rare Sats daripada dihantar. Gunakan pemisah UTXO Magic Eden untuk mengosongkan {{value}} BTC daripada Rare Sats anda.", "collectibleDetailUtxoSplitterModalCtaButton": "<PERSON><PERSON>n <PERSON> UTXO", "collectibleDetailEasilyAccept": "<PERSON><PERSON> tawaran tertinggi", "collectibleDetailSatsCount_one": "{{count}} sat", "collectibleDetailSatsCount_other": "{{count}} sat", "collectibleDetailSpamOverlayDescription": "Kolektibel ini tersembunyi kerana Phantom mempercayai ia mungkin spam.", "collectibleDetailSpamOverlayReveal": "Tunjukkan Kolektibel", "collectibleBurnTermsOfService": "<PERSON>a faham ini tidak boleh tidak berbuat", "collectibleBurnTitleWithCount_one": "Hapuskan Token", "collectibleBurnTitleWithCount_other": "Hapuskan Token", "collectibleBurnDescriptionWithCount_one": "Tindakan ini akan menghap<PERSON>an dan membuang token ini selama-lamanya daripada dompet anda.", "collectibleBurnDescriptionWithCount_other": "Tindakan ini akan menghap<PERSON>an dan membuang token ini selama-lamanya daripada dompet anda.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Token", "collectibleBurnCta": "<PERSON><PERSON><PERSON><PERSON>", "collectibleBurnRebate": "Rebet", "collectibleBurnRebateTooltip": "Ju<PERSON>lah kecil SOL akan didepositkan ke dalam dompet anda secara automatik kerana membakar token ini.", "collectibleBurnNetworkFee": "<PERSON><PERSON>", "collectibleBurnNetworkFeeTooltip": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> rang<PERSON> untuk memproses urus niaga", "unwrapButtonSwapTo": "<PERSON><PERSON> kepada {{chainSymbol}}", "unwrapButtonWithdrawFrom": "Menarik balik daripada {{withdrawalSource}} untuk {{chainSymbol}}", "unwrapModalEstimatedTime": "<PERSON><PERSON>", "unwrapModalNetwork": "<PERSON><PERSON><PERSON><PERSON>", "unwrapModalNetworkFee": "<PERSON><PERSON>", "unwrapModalTitle": "<PERSON><PERSON><PERSON>", "unsupportedChain": "Rantai Tidak Disokong", "unsupportedChainDescription": "Nampaknya kita tidak menyokong {{action}} untuk rangkaian {{chainName}}.", "networkFeesTooltipLabel": "<PERSON><PERSON> {{chainName}}", "networkFeesTooltipDescription": "{{chainName}} yuran berbeza berdasarkan beberapa faktor. <PERSON>a boleh menyesuaikannya untuk membuat urus niaga anda lebih cepat (lebih mahal) atau lebih perlahan (lebih murah).", "burnStatusErrorTitleWithCount_one": "Token gagal di<PERSON>kan", "burnStatusErrorTitleWithCount_other": "Token gagal di<PERSON>kan", "burnStatusSuccessTitleWithCount_one": "Token terbakar!", "burnStatusSuccessTitleWithCount_other": "Token terbakar!", "burnStatusLoadingTitleWithCount_one": "<PERSON><PERSON><PERSON><PERSON>an token...", "burnStatusLoadingTitleWithCount_other": "<PERSON><PERSON><PERSON><PERSON>an token...", "burnStatusErrorMessageWithCount_one": "Token ini tidak boleh diha<PERSON>kan. Sila cuba lagi kemudian.", "burnStatusErrorMessageWithCount_other": "Token ini tidak boleh diha<PERSON>kan. Sila cuba lagi kemudian.", "burnStatusSuccessMessageWithCount_one": "Token ini telah dimusnahkan secara selama-lamanya dan <PERSON> {{rebateAmount}} telah didepositkan ke dalam dompet anda.", "burnStatusSuccessMessageWithCount_other": "Token ini telah dimusnahkan secara selama-lamanya dan <PERSON> {{rebateAmount}} telah didepositkan ke dalam dompet anda.", "burnStatusLoadingMessageWithCount_one": "Token ini telah dimusnahkan secara selama-lamanya dan <PERSON> {{rebateAmount}} akan didepositkan ke dalam dompet anda.", "burnStatusLoadingMessageWithCount_other": "Token ini telah dimusnahkan secara selama-lamanya dan <PERSON> {{rebateAmount}} akan didepositkan ke dalam dompet anda.", "burnStatusViewTransactionText": "<PERSON><PERSON> urus niaga", "collectibleDisplayLoading": "Memuat...", "collectiblesNoCollectibles": "Tiada kolektibel", "collectiblesPrimaryText": "Kolektibel Anda", "collectiblesReceiveCollectible": "Terima Kolektibel", "collectiblesUnknownCollection": "Koleksi Tidak Diketahui", "collectiblesUnknownCollectible": "Kolektibel Tidak Diketahui", "collectiblesUniqueHolders": "Pemegang Unik", "collectiblesSupply": "<PERSON><PERSON><PERSON>", "collectiblesUnknownTokens": "Token Tidak Diketahui", "collectiblesNrOfListed": "{{ nrOfListed }} Tersenarai", "collectiblesListed": "Tersenarai", "collectiblesMintCollectible": "Kolektibel Kilang Syiling", "collectiblesYouMint": "Kilang Syiling Anda", "collectiblesMintCost": "<PERSON><PERSON> kilang wang", "collectiblesMintFail": "<PERSON>lang syiling telah gagal", "collectiblesMintFailMessage": "Terdapat masalah mencetak kolektibel anda. Sila cuba semula.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "Pencetakan...", "collectiblesMintingMessage": "Kolektibel anda sedang dicetak", "collectiblesMintShareSubject": "Lihat ini", "collectiblesMintShareMessage": "Saya menempa ini di @phantom!", "collectiblesMintSuccess": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMintSuccessMessage": "Kolektibel anda dicetak sekarang", "collectiblesMintSuccessQuestMessage": "Anda telah memenuhi k<PERSON>luan untuk Pencarian Phantom. Ketik Tuntut ganjaran anda untuk mendapatkan kolektibel percuma anda.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMintMaxLengthErrorMessage": "Telah melebihi panjang maks", "collectiblesMintSafelyDismiss": "<PERSON>a boleh mengetepikan tetingkap ini dengan selamat.", "collectiblesTrimmed": "<PERSON><PERSON> te<PERSON> men<PERSON> had untuk bilangan kolektibel yang boleh dipaparkan sekarang juga.", "collectiblesNonTransferable": "Tak Boleh Pindah Milik", "collectiblesNonTransferableYes": "Ya", "collectiblesSellOfferDetails": "<PERSON><PERSON><PERSON>", "collectiblesSellYouSell": "<PERSON><PERSON>", "collectiblesSellGotIt": "<PERSON><PERSON><PERSON>", "collectiblesSellYouReceive": "<PERSON><PERSON>", "collectiblesSellOffer": "<PERSON><PERSON><PERSON>", "collectiblesSoldCollectible": "Kolektibel Dijual", "collectiblesSellMarketplace": "<PERSON><PERSON><PERSON>", "collectiblesSellCollectionFloor": "Lantai Koleksi", "collectiblesSellDifferenceFromFloor": "Per<PERSON><PERSON><PERSON> da<PERSON>ada lantai", "collectiblesSellLastSalePrice": "Jualan Terakhir", "collectiblesSellEstimatedFees": "<PERSON><PERSON>", "collectiblesSellEstimatedProfitAndLoss": "Keuntungan/Kerugian Anggaran", "collectiblesSellViewOnMarketplace": "Lihat pada {{marketplace}}", "collectiblesSellCollectionFloorTooltip": "<PERSON><PERSON> <PERSON><PERSON><PERSON>' yang terendah dalam koleksi seluruh berbilang pasaran.", "collectiblesSellProfitLossTooltip": "Ke<PERSON>ungan/Kerugian yang dianggarkan dikira berdasarkan harga jualan terakhir dan jumlah tawaran kurang yuran.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Royalti ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "<PERSON><PERSON> ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "<PERSON><PERSON>", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "Rangkaian {{chainName}}", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Sebut harga termasuk yuran Phantom {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Sebut ha<PERSON>, <PERSON><PERSON>, <PERSON><PERSON> dan yuran <PERSON> {{phantomFeePercentage}}", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "<PERSON>but ha<PERSON>, <PERSON><PERSON> dan <PERSON><PERSON>", "collectiblesSellTransactionFeeTooltipTitle": "<PERSON><PERSON>", "collectiblesSellStatusLoadingTitle": "<PERSON><PERSON><PERSON>...", "collectiblesSellStatusLoadingIsSellingFor": "di<PERSON><PERSON>", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} Dijual!", "collectiblesSellStatusSuccessWasSold": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "collectiblesSellStatusErrorTitle": "<PERSON><PERSON><PERSON><PERSON> Yang Tidak Kena Telah Berlaku", "collectiblesSellStatusErrorSubtitle": "Terdapat isu percubaan untuk menjual", "collectiblesSellStatusViewTransaction": "<PERSON><PERSON>", "collectiblesSellInsufficientFundsTitle": "<PERSON> yang tidak men<PERSON>", "collectiblesSellInsufficientFundsSubtitle": "Kita tidak dapat menerima tawaran pada kolektibel ini kerana terdapat dana yang tidak mencukupi untuk membayar yuran rang<PERSON>an.", "collectiblesSellRecentlyTransferedNFTTitle": "Dipindahkan baru-baru ini", "collectiblesSellRecentlyTransferedNFTSubtitle": "Anda mesti menu<PERSON>gu 1 jam untuk menerima bida selepas pindahan.", "collectiblesApproveCollection": "Diluluskan {{collectionName}}", "collectiblesSellNotAvailableAnymoreTitle": "<PERSON><PERSON><PERSON> tidak tersedia", "collectiblesSellNotAvailableAnymoreSubtitle": "<PERSON><PERSON><PERSON> tidak lagi tersedia ada. Batalkan bida ini dan cuba semula", "collectiblesSellFlaggedTokenTitle": "Kolektibel ditandakan", "collectiblesSellFlaggedTokenSubtitle": "Kolektibel tidak boleh didagang, ia mungkin untuk berbilang sebab seperti dilaporkan sebagai dicuri atau dipertaruhkan tanpa lokap", "collectiblesListOnMagicEden": "Disenaraikan pada Magic Eden", "collectiblesListPrice": "<PERSON><PERSON>", "collectiblesUseFloor": "<PERSON><PERSON><PERSON>", "collectiblesFloorPrice": "<PERSON><PERSON>", "collectiblesLastSalePrice": "<PERSON><PERSON>", "collectiblesTotalReturn": "<PERSON><PERSON><PERSON>", "collectiblesOriginalPurchasePrice": "<PERSON><PERSON>", "collectiblesMagicEdenFee": "Yuran Magic Eden", "collectiblesArtistRoyalties": "Royalti Artis", "collectiblesListNowButton": "<PERSON><PERSON><PERSON>", "collectiblesListAnywayButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesCreateListingTermsOfService": "By tapping <1>\"List Now\"</1> you agree to Magic Eden's <3>Terms of Service</3>", "collectiblesViewListing": "<PERSON><PERSON>", "collectiblesListingViewTransaction": "<PERSON><PERSON>", "collectiblesRemoveListing": "Singkirkan Penyenaraian", "collectiblesEditListing": "Sunting Penyenaraian", "collectiblesEditListPrice": "<PERSON><PERSON>", "collectiblesListPriceTooltip": "Harga Senarai ialah harga jualan untuk item. Lazimnya penjual menetapkan Harga Senarai supaya sama dengan atau melebihi Harga Lantai.", "collectiblesFloorPriceTooltip": "Harga Lantai ialah Harga Senarai aktif terendah untuk item dalam koleksi ini.", "collectiblesOriginalPurchasePriceTooltip": "<PERSON><PERSON><PERSON> anda membeli item ini untuk jumlah ini.", "collectiblesPurchasedForSol": "Di<PERSON>i untuk SOL {{lastPurchasePrice}}", "collectiblesUnableToLoadListings": "Tidak dapat memuat kolektibel tersenarai", "collectiblesUnableToLoadListingsFrom": "Tidak dapat memuat rentetan daripada {{marketplace}}", "collectiblesUnableToLoadListingsDescription": "Penyenaraian dan aset anda selamat tetapi kita tidak dapat memuatnya daripada {{marketplace}} pada masa ini. Sila cuba lagi kemudian.", "collectiblesBelowFloorPrice": "<PERSON><PERSON>", "collectiblesBelowFloorPriceMessage": "<PERSON><PERSON>h anda pasti anda ingin menyenaraikan NFT anda di bawah harga lantai?", "collectiblesMinimumListingPrice": "Harga minimum ialah 0.01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden mengenakan yuran atas urus niaga diselesaikan.", "collectiblesArtistRoyaltiesTooltip": "Pencipta koleksi ini menerima % royalti daripada setiap jualan yang diselesaikan.", "collectibleScreenCollectionLabel": "<PERSON><PERSON><PERSON><PERSON>", "collectibleScreenPhotosPermissionTitle": "Keizinan <PERSON>", "collectibleScreenPhotosPermissionMessage": "<PERSON><PERSON> me<PERSON> keizinan anda untuk melihat foto anda. <PERSON>la pergi ke Tetapan dan kemas kini keizinan anda.", "collectibleScreenPhotosPermissionOpenSettings": "<PERSON><PERSON>", "listStatusErrorTitle": "Penyenaraian Telah Gagal", "editListStatusErrorTitle": "Tidak dapat mengemas kini", "removeListStatusErrorTitle": "Menyingkirkan Penyenaraian Telah Gagal", "listStatusSuccessTitle": "Penyenaraian Dicipta!", "editListingStatusSuccessTitle": "Penyenaraian Dike<PERSON>!", "removeListStatusSuccessTitle": "Penyenaraian disingkirkan daripada Magic Eden", "listStatusLoadingTitle": "Mencipta <PERSON>...", "editListingStatusLoadingTitle": "<PERSON><PERSON><PERSON>...", "removeListStatusLoadingTitle": "Singkirkan Penyenaraian...", "listStatusErrorMessage": "{{name}} tidak boleh tersenarai pada Magic Eden", "removeListStatusErrorMessage": "{{name}} tidak boleh tidak disenaraikan pada Magic Eden", "listStatusSuccessMessage": "{{name}} sekarang tersenarai pada Magic Eden untuk SOL {{listCollectiblePrice}}", "editListingStatusSuccessMessage": "{{name}} sekarang dikemas kini pada Magic Eden untuk SOL {{editListCollectiblePrice}}", "removeListStatusSuccessMessage": "{{name}} be<PERSON><PERSON><PERSON> di<PERSON><PERSON><PERSON>n daripada Magic Eden", "listStatusLoadingMessage": "Penyenaraian {{name}} pada Magic Eden untuk {{listCollectiblePrice}} SOL.", "editListingStatusLoadingMessage": "<PERSON><PERSON><PERSON> kini {{name}} pada Magic Eden untuk {{editListCollectiblePrice}} SOL.", "removeListStatusLoadingMessage": "Menyingkirkan {{name}} daripada Magic Eden. Ini mungkin mengambil seketika.", "listStatusLoadingSafelyDismiss": "<PERSON>a boleh menyingkirkan tetingkap ini dengan selamat.", "listStatusViewOnMagicEden": "Lihat pada Magic Eden", "listStatusViewOnMarketplace": "Lihat pada {{marketplace}}", "listStatusLoadingDismiss": "Menyingkirkan", "listStatusViewTransaction": "<PERSON><PERSON>", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Sambungkan dompet perkakasan anda dan pastikan ia tidak berkunci. <PERSON>elah kita telah mengesannya anda boleh memilih alamat mana yang anda ingin menggunakan.", "connectHardwareFailedPrimaryText": "Sambungan gagal", "connectHardwareFailedSecondaryText": "Sila sambungkan dompet perkakasan anda dan pastikan ia tidak berkunci. <PERSON><PERSON>h kita menemuinya anda boleh memilih alamat mana yang anda ingin menggunakan.", "connectHardwareFinishPrimaryText": "A<PERSON>un <PERSON>!", "connectHardwareFinishSecondaryText": "<PERSON><PERSON><PERSON> anda boleh mencapai dompet Ledger Nano anda daripada dalam Phantom. <PERSON><PERSON> kembali ke aplikasi.", "connectHardwareNeedsPermissionPrimaryText": "Sambung dompet baharu", "connectHardwareNeedsPermissionSecondaryText": "<PERSON><PERSON> butang di bawah untuk memulakan proses sambungan.", "connectHardwareSearchingPrimaryText": "<PERSON><PERSON><PERSON> dompet...", "connectHardwareSearchingSecondaryText": "Sambungkan dompet perkakasan anda, pastikan ia tidak berkunci dan anda telah meluluskan keizinan dalam penyemak imbas anda.", "connectHardwarePermissionDeniedPrimary": "<PERSON><PERSON><PERSON><PERSON> di<PERSON>", "connectHardwarePermissionDeniedSecondary": "Berikan keizinan kepada Phantom untuk menyambung ke peranti Ledger anda", "connectHardwarePermissionUnableToConnect": "Tidak dapat menyambung", "connectHardwarePermissionUnableToConnectDescription": "Kita tidak dapat menyambung ke peranti Ledger anda. <PERSON>a mungkin memerlukan lebih keizinan.", "connectHardwareSelectAddressAllAddressesImported": "<PERSON><PERSON><PERSON> al<PERSON>t telah diimport", "connectHardwareSelectAddressDerivationPath": "<PERSON><PERSON>", "connectHardwareSelectAddressSearching": "<PERSON><PERSON><PERSON>...", "connectHardwareSelectAddressSelectWalletAddress": "<PERSON><PERSON><PERSON> do<PERSON>", "connectHardwareSelectAddressWalletAddress": "<PERSON><PERSON><PERSON>", "connectHardwareWaitingForApplicationSecondaryText": "Sila sambungkan dompet perkakasan anda dan pastikan ia tidak berkunci.", "connectHardwareWaitingForPermissionPrimaryText": "<PERSON><PERSON><PERSON> k<PERSON>an", "connectHardwareWaitingForPermissionSecondaryText": "Sambungkan dompet perkakasan anda, pastikan ia tidak berkunci dan anda telah meluluskan keizinan dalam penyemak imbas anda.", "connectHardwareAddAccountButton": "<PERSON><PERSON> Akaun", "connectHardwareLedger": "Sambungkan Ledger anda", "connectHardwareStartConnection": "<PERSON><PERSON> butang di bawah untuk bermula menyambung dompet perkakasan <PERSON> anda", "connectHardwarePairSuccessPrimary": "{{productName}} bersambung", "connectHardwarePairSuccessSecondary": "Anda telah berjaya <PERSON> {{productName}} anda.", "connectHardwareSelectChains": "<PERSON><PERSON><PERSON> rantai untuk menyambung", "connectHardwareSearching": "<PERSON><PERSON><PERSON>...", "connectHardwareMakeSureConnected": "Sambung dan membuka kunci dompet perkakasan anda. <PERSON><PERSON> l<PERSON> keizinan penyemak imbas yang berkaitan.", "connectHardwareOpenAppDescription": "<PERSON>la buka dompet perkakasan anda", "connectHardwareConnecting": "Sambungkan...", "connectHardwareConnectingDescription": "Kita sedang menyambung ke peranti Ledger anda.", "connectHardwareConnectingAccounts": "Men<PERSON><PERSON><PERSON><PERSON> akaun anda...", "connectHardwareDiscoveringAccounts": "<PERSON><PERSON><PERSON> akaun...", "connectHardwareDiscoveringAccountsDescription": "Kita sedang mencari aktiviti dalam akaun anda.", "connectHardwareErrorLedgerLocked": "Ledger <PERSON>", "connectHardwareErrorLedgerLockedDescription": "<PERSON><PERSON><PERSON> per<PERSON> anda tida<PERSON>, kemudian cuba lagi.", "connectHardwareErrorLedgerGeneric": "Sesuatu yang tidak kena telah berlaku", "connectHardwareErrorLedgerGenericDescription": "Tidak dapat mencari akaun. Pastikan per<PERSON> anda tidak berk<PERSON>, kemudian cuba lagi.", "connectHardwareErrorLedgerPhantomLocked": "<PERSON>la buka semula Phantom dan cuba sambungkan perkakasan sekali lagi.", "connectHardwareFindingAccountsWithActivity": "<PERSON><PERSON><PERSON> akaun {{chainName}}...", "connectHardwareFindingAccountsWithActivityDualChain": "<PERSON><PERSON><PERSON> {{chainName1}} atau {{chainName2}} akaun...", "connectHardwareFoundAccountsWithActivity": "<PERSON>a mendapati {{numOfAccounts}} akaun dengan aktiviti di Ledger anda.", "connectHardwareFoundAccountsWithActivitySingular": "Kita mendapati 1 akaun dengan aktiviti di Ledger anda.", "connectHardwareFoundSomeAccounts": "<PERSON>a menemui beberapa akaun pada peranti <PERSON> anda.", "connectHardwareViewAccounts": "<PERSON><PERSON>", "connectHardwareConnectAccounts": "<PERSON><PERSON><PERSON>", "connectHardwareSelectAccounts": "<PERSON><PERSON><PERSON>", "connectHardwareChooseAccountsToConnect": "<PERSON><PERSON><PERSON> akaun dompet untuk menyambung.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} <PERSON><PERSON><PERSON> ditambah", "connectHardwareAccountsStepOfSteps": "<PERSON><PERSON><PERSON> {{stepNum}} da<PERSON>ada {{totalSteps}}", "connectHardwareMobile": "Sambungkan Ledger", "connectHardwareMobileTitle": "Sambungkan dompet perkakasan Ledger anda", "connectHardwareMobileEnableBluetooth": "Bolehkan Bluetooth", "connectHardwareMobileEnableBluetoothDescription": "Benarkan keizinan menggunakan Bluetooth untuk menyambung", "connectHardwareMobileEnableBluetoothSettings": "Pergi ke Tetapan untuk membenarkan Phantom menggunakan keizinan Lokasi dan <PERSON>.", "connectHardwareMobilePairWithDevice": "<PERSON><PERSON><PERSON><PERSON> bersama dengan peranti <PERSON> anda", "connectHardwareMobilePairWithDeviceDescription": "Pastikan peranti anda berdekatan untuk mendapatkan isyarat yang terbaik", "connectHardwareMobileConnectAccounts": "<PERSON><PERSON><PERSON><PERSON> akaun", "connectHardwareMobileConnectAccountsDescription": "Kita akan perhati-perhatikan aktiviti dalam mana-mana akaun yang anda mungkin pernah menggunakan", "connectHardwareMobileConnectLedgerDevice": "Sambungkan peranti Ledger anda", "connectHardwareMobileLookingForDevices": "Mencari peranti berhampiran...", "connectHardwareMobileLookingForDevicesDescription": "<PERSON>la sambungkan peranti Ledger anda dan pastikan ia tidak berkunci.", "connectHardwareMobileFoundDeviceSingular": "Kita telah menemui 1 peranti Ledger", "connectHardwareMobileFoundDevices": "<PERSON>a telah menem<PERSON> {{numDevicesFound}} per<PERSON>", "connectHardwareMobileFoundDevicesDescription": "<PERSON><PERSON><PERSON> peranti Ledger di bawah untuk mula memasang.", "connectHardwareMobilePairingWith": "<PERSON><PERSON><PERSON> dengan {{deviceName}}", "connectHardwareMobilePairingWithDescription": "<PERSON><PERSON><PERSON> arahan pada peranti <PERSON>ger anda sambil memasang.", "connectHardwareMobilePairingFailed": "<PERSON><PERSON><PERSON> ber<PERSON>a me<PERSON>ang", "connectHardwareMobilePairingFailedDescription": "Tidak dapat memasang dengan {{deviceName}}. <PERSON><PERSON>n peranti anda tidak berkunci.", "connectHardwareMobilePairingSuccessful": "<PERSON><PERSON><PERSON><PERSON>", "connectHardwareMobilePairingSuccessfulDescription": "<PERSON>a telah berjaya memasang dan menyambungkan peranti Ledger anda.", "connectHardwareMobileOpenAppSingleChain": "<PERSON><PERSON> aplikasi {{chainName}} di Led<PERSON> anda", "connectHardwareMobileOpenAppDualChain": "<PERSON><PERSON> aplikasi {{chainName1}} atau {{chainName2}} di Ledger anda", "connectHardwareMobileOpenAppDescription": "<PERSON><PERSON><PERSON> per<PERSON> anda tidak berk<PERSON>.", "connectHardwareMobileStillCantFindDevice": "Masih tidak dapat mencari peranti anda?", "connectHardwareMobileLostConnection": "<PERSON><PERSON> sambungan", "connectHardwareMobileLostConnectionDescription": "<PERSON>a putus sambungan ke {{deviceName}}. <PERSON><PERSON>n peranti anda tidak be<PERSON>, kemudian cuba semula.", "connectHardwareMobileGenericLedgerDevice": "<PERSON><PERSON>", "connectHardwareMobileConnectDeviceSigning": "Sambungkan {{deviceName}} anda", "connectHardwareMobileConnectDeviceSigningDescription": "<PERSON><PERSON> kunci per<PERSON> anda dan simpannya berdekatan.", "connectHardwareMobileBluetoothDisabled": "Bluetooth dilumpuhkan", "connectHardwareMobileBluetoothDisabledDescription": "<PERSON><PERSON> b<PERSON>kan <PERSON>tooth anda dan pastikan per<PERSON> anda tidak berkun<PERSON>.", "connectHardwareMobileLearnMore": "<PERSON><PERSON><PERSON>", "connectHardwareMobileBlindSigningDisabled": "Tandatangan Rahs<PERSON>", "connectHardwareMobileBlindSigningDisabledDescription": "Pastikan tandatangan rahsia dibolehkan di peranti anda.", "connectHardwareMobileConfirmSingleChain": "Anda perlu meluluskan urus niaga pada dompet perkakasan anda. Pastikan ia tidak berkunci.", "metamaskExplainerBottomSheetHeader": "<PERSON>n ini berfungsi dengan Phantom", "metamaskExplainerBottomSheetSubheader": "<PERSON><PERSON>h MetaMask daripada dialog dompet sambung untuk meneruskan.", "metamaskExplainerBottomSheetDontShowAgain": "<PERSON><PERSON> tunjuk lagi", "ledgerStatusNotConnected": "Ledger tidak bersambung", "ledgerStatusConnectedInterpolated": "{{productName}} bersambung", "connectionClusterInterpolated": "Anda sekarang berada di {{cluster}}", "connectionClusterTestnetMode": "Pada masa ini anda berada dalam Mod Testnet", "featureNotSupportedOnLocalNet": "Ciri ini tidak disokong apabila Solana Localnet dibolehkan.", "readOnlyAccountBannerWarning": "Anda sedang memperhatikan akaun ini", "depositAddress": "<PERSON><PERSON><PERSON>", "depositAddressChainInterpolated": "<PERSON><PERSON><PERSON> {{chain}} And<PERSON>", "depositAssetDepositInterpolated": "Terima {{tokenSymbol}}", "depositAssetSecondaryText": "<PERSON><PERSON><PERSON> ini hanya dapat digunakan untuk menerima token yang serasi.", "depositAssetTextInterpolated": "<PERSON><PERSON><PERSON> alamat ini untuk menerima token dan kolektibel di <1>{{network}}</1>.", "depositAssetTransferFromExchange": "<PERSON><PERSON><PERSON> da<PERSON>ada <PERSON>n", "depositAssetShareAddress": "<PERSON><PERSON>", "depositAssetBuyOrDeposit": "Bel<PERSON> atau Pindahkan", "depositAssetBuyOrDepositDesc": "Membiayai dompet anda untuk bermula", "depositAssetTransfer": "Pindahkan", "editAddressAddressAlreadyAdded": "<PERSON><PERSON><PERSON> sudah ditambah", "editAddressAddressAlreadyExists": "<PERSON><PERSON><PERSON> sudah ada", "editAddressAddressIsRequired": "<PERSON><PERSON><PERSON>", "editAddressPrimaryText": "<PERSON><PERSON>", "editAddressRemove": "Singkirkan da<PERSON>", "editAddressToast": "<PERSON><PERSON><PERSON> kini", "removeSavedAddressToast": "<PERSON><PERSON><PERSON>", "exportSecretErrorGeneric": "Se<PERSON><PERSON>u yang tidak kena berlaku, sila cuba lagi kemudian", "exportSecretErrorIncorrectPassword": "<PERSON>a laluan yang salah", "exportSecretPassword": "<PERSON><PERSON>", "exportSecretPrivateKey": "kunci peribadi", "exportSecretSecretPhrase": "rangkai kata rahsia", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "rangkai kata pemulihan rahsia", "exportSecretSelectYourAccount": "<PERSON><PERSON><PERSON> akaun anda", "exportSecretShowPrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exportSecretShowSecretRecoveryPhrase": "<PERSON><PERSON><PERSON><PERSON><PERSON> rangkai kata pemulihan rahsia", "exportSecretShowSecret": "Paparkan {{secretNameText}}", "exportSecretWarningPrimaryInterpolated": "<1><PERSON><PERSON></1> be<PERSON><PERSON><PERSON> {{secretNameText}} anda!", "exportSecretWarningSecondaryInterpolated": "<PERSON>ka seseorang mempunyai {{secretNameText}} anda mereka akan mempunyai kawalan penuh dompet anda.", "exportSecretOnlyWay": "{{secretNameText}} anda adalah satu-satunya cara untuk mendapat semula dompet anda", "exportSecretDoNotShow": "<PERSON><PERSON> ben<PERSON>an sesiapa pun melihat {{secretNameText}} anda", "exportSecretWillNotShare": "<PERSON><PERSON> tidak akan be<PERSON> {{secretNameText}} dengan sesiapa pun, termasuk <PERSON>.", "exportSecretNeverShare": "<PERSON>an sama sekali be<PERSON> {{secretNameText}} anda dengan sesiapa pun", "exportSecretYourPrivateKey": "<PERSON><PERSON><PERSON>", "exportSecretYourSecretRecoveryPhrase": "Rangkai kata pemulihan rahsia anda", "exportSecretResetPin": "Set semula PIN anda", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "Bantuan", "gasUpTo": "Se<PERSON>ga {{ amount }}", "timeDescription1hour": "Kira-kira 1 jam", "timeDescription30minutes": "Kira-kira 30 minit", "timeDescription10minutes": "Kira-kira 10 minit", "timeDescription2minutes": "Kira-kira 2 minit", "timeDescription30seconds": "Kira-kira 30 saat", "timeDescription15seconds": "Kira-kira 15 saat", "timeDescription10seconds": "Kira-kira 10 saat", "timeDescription5seconds": "Kira-kira 5 saat", "timeDescriptionAbbrev1hour": "1 jam", "timeDescriptionAbbrev30minutes": "30min", "timeDescriptionAbbrev10minutes": "10min", "timeDescriptionAbbrev2minutes": "2min", "timeDescriptionAbbrev30seconds": "30s", "timeDescriptionAbbrev15seconds": "15s", "timeDescriptionAbbrev10seconds": "10s", "timeDescriptionAbbrev5seconds": "5s", "gasSlow": "<PERSON><PERSON><PERSON>", "gasAverage": "<PERSON><PERSON><PERSON>", "gasFast": "Pantas", "satsPerVirtualByte": "{{satsPerVirtualByte}} sats/vB", "satsAmount": "{{sats}} sats", "homeErrorButtonText": "Cuba lagi", "homeErrorDescription": "Terdapat ralat cuba mendapatkan kembali aset anda. <PERSON>la segar semula dan cuba lagi", "homeErrorTitle": "<PERSON><PERSON> menda<PERSON>t aset", "homeManageTokenList": "Uruskan senarai token", "interstitialDismissUnderstood": "<PERSON><PERSON><PERSON>", "interstitialBaseWelcomeTitle": "Kini Phantom menyokong Base!", "interstitialBaseWelcomeItemTitle_1": "<PERSON><PERSON>, terima dan beli token", "interstitialBaseWelcomeItemTitle_2": "Terokailah ekosistem Base", "interstitialBaseWelcomeItemTitle_3": "<PERSON><PERSON><PERSON> dan terjamin", "interstitialBaseWelcomeItemDescription_1": "Pindah dan beli USDC dan ETH pada Base menggunakan {{paymentMethod}}, kad atau Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Gunakan Phantom dengan semua aplikasi DeFi dan NFT kegemaran anda.", "interstitialBaseWelcomeItemDescription_3": "<PERSON><PERSON> selamat dengan sokongan Ledger, penapisan spam dan simulasi urus niaga.", "privacyPolicyChangedInterpolated": "<PERSON><PERSON> kami telah be<PERSON>. <1><PERSON><PERSON><PERSON></1>", "bitcoinAddressTypesBodyTitle": "<PERSON><PERSON>", "bitcoinAddressTypesFeature1Title": "Mengenai alamat Bitcoin", "bitcoinAddressTypesFeature1Subtitle": "Phantom menyokong Native <PERSON><PERSON><PERSON><PERSON> dan <PERSON>, setiap dengan baki sendirinya. <PERSON>a boleh menghantar BTC atau Ordinals dengan yang mana-mana jenis alamat.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Alamat BTC lalai di Phantom. Lebih lama daripada Taproot tetapi sesuai dengan semua dompet dan pertukaran.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Terbaik untuk Ordinals dan BRC-20, dengan yuran yang termahal. Laraskan alamat dalam <PERSON> -> Alamat Bitcoin Pilihan.", "headerTitleInfo": "Maklumat", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Ini ialah alamat <1>{{addressType}}</1> anda.", "invalidChecksumTitle": "Kita telah meningkatkan rangkai kata rahsia anda!", "invalidChecksumFeature1ExportPhrase": "Eksport Rangkai Kata Rahs<PERSON> baharu anda", "invalidChecksumFeature1ExportPhraseDescription": "<PERSON><PERSON> sand<PERSON>an rangkai kata rahsia baharu anda bersama dengan kunci peribadi akaun lama anda.", "invalidChecksumFeature2FundsAreSafe": "<PERSON> anda adalah selamat dan terjamin", "invalidChecksumFeature2FundsAreSafeDescription": "Peningkatan ini diautomasikan. Tiada sesiapa di Phantom tahu rangkai kata rahsia anda atau dapat mencapai dana anda.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON><PERSON> lebih lan<PERSON>t", "invalidChecksumFeature3LearnMoreDescription": "Anda mempunyai rangkai kata yang tidak serasi dengan kebanyakan dompet. Baca <1>artikel bantuan ini</1> untuk mengetahui lebih lanjut mengenai ini.", "invalidChecksumBackUpSecretPhrase": "Sandarkan rangkai kata rahsia", "migrationFailureTitle": "Sesuatu yang tidak kena telah berlaku menghi<PERSON><PERSON><PERSON> akaun anda", "migrationFailureFeature1": "Eksport rangkai kata rahsia anda", "migrationFailureFeature1Description": "<PERSON><PERSON> sandarkan rangkai kata rahsia anda sebelum menyertai.", "migrationFailureFeature2": "Sertai Phantom", "migrationFailureFeature2Description": "<PERSON>a akan perlu menyertai semula ke Phantom untuk melihat akaun anda.", "migrationFailureFeature3": "<PERSON><PERSON><PERSON> lebih lan<PERSON>t", "migrationFailureFeature3Description": "Baca <1>artike<PERSON> bantuan ini</1> untuk mengetahui lebih lanjut mengenai ini.", "migrationFailureContinueToOnboarding": "Teruskan ke penyer<PERSON>an", "migrationFailureUnableToFetchMnemonic": "Kita tidak dapat memuat rangkai kata rahsia anda", "migrationFailureUnableToFetchMnemonicDescription": "Sila hubungi pihak sokongan dan muat turun log aplikasi untuk nyahpepijat", "migrationFailureContactSupport": "Hubung<PERSON>", "ledgerActionConfirm": "<PERSON><PERSON><PERSON> pada Ledger <PERSON> anda", "ledgerActionErrorBlindSignDisabledPrimaryText": "Tandatangan rah<PERSON>", "ledgerActionErrorBlindSignDisabledSecondaryText": "Sila pastikan tandatangan rahsia dibolehkan pada peranti perkakasan anda dan seterusnya cuba semula tindakan tersebut", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Per<PERSON> perkakasan diputus sambungan semasa operasi", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "<PERSON>la tutup sambungan Phantom dan kemudian cuba semula tindakan tersebut", "ledgerActionErrorDeviceLockedPrimaryText": "Peranti perka<PERSON>an diku<PERSON>i", "ledgerActionErrorDeviceLockedSecondaryText": "<PERSON>la buka peranti perkakasan anda dan cuba semula tindakan tersebut", "ledgerActionErrorHeader": "<PERSON><PERSON>", "ledgerActionErrorUserRejectionPrimaryText": "<PERSON><PERSON> niaga ditolak o<PERSON>h pen<PERSON>una", "ledgerActionErrorUserRejectionSecondaryText": "Tindakan ditolak pada peranti perkakasan oleh pengguna", "ledgerActionNeedPermission": "<PERSON><PERSON><PERSON> k<PERSON>an", "ledgerActionNeedToConfirm": "Anda perlu mengesahkan urus niaga pada dompet perkakasan anda. Pastikan ia tidak dikunci, pada aplikasi {{chainType}}.", "ledgerActionNeedToConfirmMany": "Anda akan perlu mengesahkan urus niaga {{numberOfTransactions}} di dompet perkakasan anda. Pastikan ia tidak dikunci, di aplikasi {{chainType}}.", "ledgerActionNeedToConfirmBlind": "Anda perlu mengesahkan urus niaga di dompet perkakasan anda. Pastikan ia tidak dikunci, di aplikasi {{chainType}} dan tandatangan buta dibolehkan.", "ledgerActionNeedToConfirmBlindMany": "Anda akan perlu mengesahkan urus niaga {{numberOfTransactions}} di dompet perkakasan anda. Pastikan ia tidak dikunci, di aplikasi {{chainType}} dan tandatangan buta dibolehkan.", "ledgerActionPleaseConnect": "<PERSON><PERSON> sambungkan Ledger Nano anda", "ledgerActionPleaseConnectAndConfirm": "Sila sambungkan dompet perkakasan anda, pastikan ia tidak berkunci. Pastikan anda telah meluluskan keizinan dalam penyemak imbas anda.", "maxInputAmount": "<PERSON><PERSON><PERSON>", "maxInputMax": "<PERSON><PERSON>", "notEnoughSolPrimaryText": "SOL yang tidak cukup", "notEnoughSolSecondaryText": "Anda tidak ada SOL yang cukup dalam dompet anda untuk membayar urus niaga ini. <PERSON>la masukkan lebih banyak deposit dan cuba lagi.", "insufficientBalancePrimaryText": "{{tokenSymbol}} yang tidak mencu<PERSON>pi", "insufficientBalanceSecondaryText": "Anda tidak mempunyai {{tokenSymbol}} yang mencukupi dalam dompet anda untuk urus niaga ini.", "insufficientBalanceRemaining": "<PERSON>", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "Tidak ada token yang cukup", "notEnoughSplTokensDescription": "Anda tidak mempunyai token yang mencukupi dalam dompet anda untuk urus niaga ini. Urus niaga ini akan berbalik jika diserahkan.", "transactionExpiredPrimaryText": "<PERSON><PERSON> niaga telah tamat tempoh", "transactionExpiredSecondaryText": "Anda telah menunggu terlalu lama untuk mengesahkan urus niaga dan ia telah tamat tempoh. Urus niaga ini akan berbalik jika diserahkan.", "transactionHasWarning": "<PERSON><PERSON> u<PERSON> niaga", "tokens": "token", "notificationApplicationApprovalPermissionsAddressVerification": "<PERSON><PERSON><PERSON> anda memiliki alamat ini", "notificationApplicationApprovalPermissionsTransactionApproval": "<PERSON><PERSON><PERSON><PERSON> permintaan untuk urus niaga", "notificationApplicationApprovalPermissionsViewWalletActivity": "<PERSON>hat baki & aktiviti dompet anda", "notificationApplicationApprovalParagraphText": "Mengesahkan akan membenarkan laman ini untuk melihat baki dan aktiviti untuk akaun terpilih.", "notificationApplicationApprovalActionButtonConnect": "Sambung", "notificationApplicationApprovalActionButtonSignIn": "Log Ma<PERSON>k", "notificationApplicationApprovalAllowApproval": "<PERSON><PERSON><PERSON> tapak untuk menyambung?", "notificationApplicationApprovalAutoConfirm": "<PERSON><PERSON> niaga Sahkan Secara Automatik", "notificationApplicationApprovalConnectDisclaimer": "Hanya sambung kepada laman web yang anda percayai", "notificationApplicationApprovalSignInDisclaimer": "Hanya log masuk kepada laman web yang anda percayai", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Laman web ini tidak selamat digunakan dan mungkin cuba mencuri dana anda.", "notificationApplicationApprovalConnectUnknownApp": "Tidak diketahui", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "Tidak dapat menyambung ke aplikasi", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "Ap<PERSON>asi ini cuba menyambung ke {{appNetworkName}}, tetapi {{phantomNetworkName}} dipilih.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Untuk menggunakan {{networkName}}, pergi ke Te<PERSON>pan <PERSON> → Mod Testnet.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "<PERSON><PERSON><PERSON><PERSON>", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Menyambung ke aplikasi mudah alih lain tidak disokong oleh Ledger pada masa ini.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "<PERSON>la tukar kepada akaun bukan <PERSON> atau gunakan penyemak imbas dalam aplikasi dan cuba lagi.", "notificationSignatureRequestConfirmTransaction": "<PERSON><PERSON><PERSON> urus niaga", "notificationSignatureRequestConfirmTransactionCapitalized": "<PERSON><PERSON><PERSON>", "notificationSignatureRequestConfirmTransactions": "<PERSON><PERSON><PERSON> urus niaga", "notificationSignatureRequestConfirmTransactionsCapitalized": "<PERSON><PERSON><PERSON>", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON><PERSON><PERSON>", "notificationMessageHeader": "<PERSON><PERSON><PERSON>", "notificationMessageCopied": "<PERSON><PERSON><PERSON> disalin", "notificationAutoConfirm": "Sahkan Secara Automatik", "notificationAutoConfirmOff": "Padamkan", "notificationAutoConfirmOn": "<PERSON><PERSON><PERSON><PERSON>", "notificationConfirmFooter": "<PERSON>ya sahkan jika anda mempercayai laman web ini.", "notificationEstimatedTime": "<PERSON><PERSON>", "notificationPermissionRequestText": "Ini hanya permintaan keizinan sahaja. <PERSON><PERSON> niaga mungkin tidak dilaksanakan dengan serta-merta.", "notificationBalanceChangesText": "<PERSON><PERSON><PERSON> baki di<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "notificationContractAddress": "<PERSON><PERSON><PERSON>", "notificationAdvancedDetailsText": "<PERSON><PERSON><PERSON><PERSON>", "notificationUnableToSimulateWarningText": "Pada masa ini kita tidak dapat menganggar perubahan baki. <PERSON>a boleh mencuba lagi kemudian atau sahkan jika anda mempercayai laman ini.", "notificationSignMessageParagraphText": "Menandatangani mesej ini akan membuktikan anda mempunyai pemilikan akaun terpilih tersebut.", "notificationSignatureRequestScanFailedDescription": "Tidak dapat mengimbas mesej untuk isu keselamatan. Sila teruskan dengan berwaspada.", "notificationFailedToScan": "Gagal untuk mensimulasikan hasil permintaan ini.\nMengesahkan tidak selamat dan mungkin mengakibatkan kerugian.", "notificationScanLoading": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonConfirm": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalActionButtonBack": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatedChanges": "<PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatesBasedOnSimulations": "<PERSON><PERSON><PERSON> adalah berda<PERSON>kan simulasi urus niaga dan bukan jaminan", "notificationTransactionApprovalHideAdvancedDetails": "Sembunyikan butiran urus niaga lan<PERSON>t", "notificationTransactionApprovalNetworkFee": "<PERSON><PERSON>", "notificationTransactionApprovalNetwork": "<PERSON><PERSON><PERSON><PERSON>", "notificationTransactionApprovalEstimatedTime": "<PERSON><PERSON>", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Tidak menemui per<PERSON>han menjejaskan pemilikan aset", "notificationTransactionApprovalSolanaAmountRequired": "<PERSON><PERSON><PERSON><PERSON> o<PERSON> rang<PERSON> untuk memproses urus niaga", "notificationTransactionApprovalUnableToSimulate": "Tidak dapat menyelakukan. Pastikan anda mempercayai laman web ini memandangkan meluluskan boleh mengakibatkan kerugian dana.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Tidak dapat mendapat kembali per<PERSON>han baki", "notificationTransactionApprovalViewAdvancedDetails": "<PERSON><PERSON> butiran urus niaga lan<PERSON>t", "notificationTransactionApprovalKnownMalicious": "<PERSON><PERSON> niaga ini berniat jahat. Menandatangani akan mengakibatkan kerugian dana.", "notificationTransactionApprovalSuspectedMalicious": "Kita mengesyaki urus niaga ini berniat jahat. Meluluskan mungkin mengakibatkan kerugian dana.", "notificationTransactionApprovalNetworkFeeHighWarning": "<PERSON><PERSON> rangkaian dinaikkan disebabkan oleh kesesakan rangkaian.", "notificationTransactionERC20ApprovalDescription": "Mengesahkan akan membenarkan aplikasi ini untuk mengakses baki anda pada bila-bila masa, <PERSON><PERSON><PERSON> had di bawah.", "notificationTransactionERC20ApprovalContractAddress": "<PERSON><PERSON><PERSON>", "notificationTransactionERC20Unlimited": "tanpa had", "notificationTransactionERC20ApprovalTitle": "Luluskan {{tokenSymbol}} perbelanja<PERSON>", "notificationTransactionERC20RevokeTitle": "Batalkan {{tokenSymbol}} perbelanjaan", "notificationTransactionERC721RevokeTitle": "Batalkan {{tokenSymbol}} akses", "notificationTransactionERC20ApprovalAll": "Semua {{tokenSymbol}} anda", "notificationIncorrectModeTitle": "<PERSON><PERSON> sa<PERSON>", "notificationIncorrectModeInTestnetTitle": "Anda berada dalam mod Testnet", "notificationIncorrectModeNotInTestnetTitle": "<PERSON>a bukan berada dalam mod Testnet", "notificationIncorrectModeInTestnetDescription": "{{origin}} sedang cuba menggunakan mainnet, tetapi anda berada dalam mod Testnet", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} sedang cuba menggunakan testnet, tetapi anda bukan berada dalam mod Testnet", "notificationIncorrectModeInTestnetProceed": "<PERSON><PERSON><PERSON>, padam<PERSON> mod Testnet.", "notificationIncorrectModeNotInTestnetProceed": "<PERSON><PERSON><PERSON>, hid<PERSON><PERSON> mod Testnet.", "notificationIncorrectEIP712ChainId": "<PERSON>a mencegah anda daripada menandatangani mesej yang tidak dimaksudkan untuk rangkaian yang anda sedang bersambung pada masa ini", "notificationIncorrectEIP712ChainIdDescription": "<PERSON><PERSON><PERSON> diminta {{messageChainId}}, anda bersambung ke {{connectedChainId}}", "notificationUnsupportedNetwork": "Rangkaian tidak disokong", "notificationUnsupportedNetworkDescription": "Laman web ini cuba menggunakan rangkaian yang tidak disokong oleh Phantom pada masa ini.", "notificationUnsupportedNetworkDescriptionInterpolated": "Untuk meneruskan dengan sambungan yang berbeza, pad<PERSON><PERSON> <1><PERSON><PERSON><PERSON> → <PERSON><PERSON>, dan p<PERSON><PERSON></1>. <PERSON><PERSON><PERSON> segar semula halaman dan sambung semula.", "notificationUnsupportedAccount": "<PERSON><PERSON>un tidak disokong", "notificationUnsupportedAccountDescription": "Laman web ini cuba menggunakan {{targetChainType}}, yang akaun {{chainType}} ini tidak menyokong.", "notificationUnsupportedAccountDescription2": "<PERSON><PERSON><PERSON> kepada akaun daripada frasa seed yang serasi atau kunci peribadi dan cuba lagi.", "notificationInvalidTransaction": "<PERSON><PERSON> niaga tidak sah", "notificationInvalidTransactionDescription": "<PERSON><PERSON> niaga yang diterima daripada aplikasi ini cacat dan tidak harus diserahkan. Sila hubungi pembangun aplikasi ini untuk melaporkan isu ini kepada mereka.", "notificationCopyTransactionText": "<PERSON><PERSON> urus niaga", "notificationTransactionCopied": "<PERSON><PERSON> niaga disalin", "onboardingImportOptionsPageTitle": "Import dompet", "onboardingImportOptionsPageSubtitle": "Import dompet yang ada dengan rangkai kata rahsia, kunci peribadi atau dompet perkakasan anda.", "onboardingImportPrivateKeyPageTitle": "Import Kunci <PERSON>", "onboardingImportPrivateKeyPageSubtitle": "Import dompet rantai tunggal yang ada", "onboardingCreatePassword": "<PERSON><PERSON><PERSON> kata la<PERSON>an", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> <1><PERSON><PERSON></1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordDescription": "<PERSON>a akan menggunakan ini untuk membuka dompet anda.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Rangkai kata pemulihan rahsia yang tidak sah", "onboardingCreatePasswordPasswordPlaceholder": "<PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthWeak": "Lemah", "onboardingCreatePasswordPasswordStrengthMedium": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthStrong": "Ku<PERSON>", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "<PERSON><PERSON> <PERSON>hs<PERSON> saya", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Rangkai Kata Pemulihan <PERSON>", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Rangkai kata ini adalah SATU-SATUNYA cara untuk memulihkan dompet anda. JANGAN berkongsinya dengan sesiapa pun!", "onboardingImportWallet": "Import Dompet", "onboardingImportWalletImportExistingWallet": "Import dompet yang ada dengan rangkai kata pemulihan rahsia 12 atau 24 perkataan anda.", "onboardingImportWalletRestoreWallet": "Pulihkan Dompet", "onboardingImportWalletSecretRecoveryPhrase": "Rangkai Kata Pemulihan <PERSON>", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Rangkai Kata Pemulihan Rahsia yang tidak sah", "onboardingImportWalletIHaveWords": "<PERSON>a me<PERSON> {{numWords}}-rangkai kata pemulihan per<PERSON>an", "onboardingImportWalletIncorrectOrMisspelledWord": "Perkataan {{wordIndex}} adalah salah atau tersalah eja", "onboardingImportWalletIncorrectOrMisspelledWords": "Perkataan {{wordIndexes}} adalah salah atau tersalah eja", "onboardingImportWalletScrollDown": "<PERSON><PERSON> ke bawah", "onboardingImportWalletScrollUp": "Tatal ke atas", "onboardingSelectAccountsImportAccounts": "I<PERSON>rt A<PERSON>", "onboardingSelectAccountsImportAccountsDescription": "<PERSON><PERSON><PERSON> akaun dompet untuk mengimport.", "onboardingSelectAccountsImportSelectedAccounts": "Import <PERSON>", "onboardingSelectAccountsFindMoreAccounts": "<PERSON>i lebih banyak akaun", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON><PERSON> akaun di<PERSON>ui", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} <PERSON><PERSON> terp<PERSON>h", "onboardingSelectAccountSelectAllText": "<PERSON><PERSON><PERSON>", "onboardingAdditionalPermissionsTitle": "<PERSON><PERSON><PERSON> a<PERSON> dengan <PERSON>", "onboardingAdditionalPermissionsSubtitle": "<PERSON><PERSON> pengalaman yang paling selanjar, kita mengesyorkan supaya membenarkan Phantom untuk membaca dan mengubah data di semua tapak.", "interstitialAdditionalPermissionsTitle": "<PERSON><PERSON><PERSON> a<PERSON> dengan <PERSON>", "interstitialAdditionalPermissionsSubtitle": "Untuk meneruskan pengalaman anda menggunakan aplikasi tanpa gangguan, kita mengesyorkan supaya membenarkan Phantom untuk membaca dan mengubah data di semua tapak.", "recentActivityPrimaryText": "Aktiviti Terbaru", "removeAccountActionButtonRemove": "Singkirkan", "removeAccountRemoveWallet": "Singkirka<PERSON> akaun", "removeAccountInterpolated": "Singkirkan {{accountName}}", "removeAccountWarningLedger": "<PERSON><PERSON><PERSON><PERSON> anda menyingkirkan dompet ini daripada Phantom, anda akan dapat menambah semulanya menggunakan aliran \"Sambung Dompet Perkakasan\".", "removeAccountWarningSeedVault": "<PERSON><PERSON><PERSON><PERSON> anda menyingkirkan dompet ini daripada Phantom, anda akan dapat menambah semulanya menggunakan aliran \"Sambung Dompet Seed Vault\".", "removeAccountWarningPrivateKey": "<PERSON><PERSON><PERSON> anda men<PERSON>n dompet ini, <PERSON> tidak akan dapat memulihkannya untuk anda. <PERSON><PERSON><PERSON> anda menyandarkan kunci peribadi anda.", "removeAccountWarningSeed": "<PERSON><PERSON><PERSON><PERSON> anda menyingkirkan dompet ini daripada Phantom, anda akan dapat memperoleh semulanya menggunakan nemonik anda dalam dompet ini atau dompet lain.", "removeAccountWarningReadOnly": "<PERSON><PERSON><PERSON><PERSON><PERSON> akaun ini tidak akan menjejaskan dompet anda, kerana ia hanya dompet perhati sahaja.", "removeSeedPrimaryText": "Singkirkan Rangkai Kata Rahsia {{number}}", "removeSeedSecondaryText": "Ini akan menyingkirkan semua akaun yang ada dalam Rangkai Kata Rahsia {{number}}. Pastikan anda menyimpan rangkai kata rahsia yang ada anda.", "resetSeedPrimaryText": "Set semula aplikasi dengan rangkai kata rahsia baharu", "resetSeedSecondaryText": "Ini akan menyingkirkan semua akaun yang anda dan menggantikannya dengan yang baharu. Pastikan anda menyandarkan rangkai kata rahsia yang ada dan kunci peribadi anda.", "resetAppPrimaryText": "Set semula & padamkan aplikasi", "resetAppSecondaryText": "Ini akan menyingkirkan semua akaun dan data yang ada. Pastikan anda menyandarkan rangkai kata rahsia dan kunci peribadi anda.", "richTransactionsDays": "hari", "richTransactionsToday": "<PERSON> ini", "richTransactionsYesterday": "Semalam", "richTransactionDetailAccount": "<PERSON><PERSON><PERSON>", "richTransactionDetailAppInteraction": "Interaksi Aplikasi", "richTransactionDetailAt": "di", "richTransactionDetailBid": "Bida", "richTransactionDetailBidDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailBought": "<PERSON><PERSON><PERSON>", "richTransactionDetailBurned": "Dihapuskan", "richTransactionDetailCancelBid": "Batalkan Bida", "richTransactionDetailCompleted": "Lengka<PERSON>", "richTransactionDetailConfirmed": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailDate": "<PERSON><PERSON><PERSON>", "richTransactionDetailFailed": "<PERSON><PERSON> gagal", "richTransactionDetailFrom": "Daripada", "richTransactionDetailItem": "<PERSON><PERSON>", "richTransactionDetailListed": "Tersenarai", "richTransactionDetailListingDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailListingPrice": "<PERSON><PERSON>", "richTransactionDetailMarketplace": "<PERSON><PERSON><PERSON>", "richTransactionDetailNetworkFee": "<PERSON><PERSON>", "richTransactionDetailOriginalListingPrice": "<PERSON><PERSON>", "richTransactionDetailPending": "<PERSON><PERSON>", "richTransactionDetailPrice": "<PERSON><PERSON>", "richTransactionDetailProvider": "Penyedia", "richTransactionDetailPurchaseDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailRebate": "Rebet", "richTransactionDetailReceived": "Diterima", "richTransactionDetailSaleDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailSent": "<PERSON><PERSON><PERSON>", "richTransactionDetailSold": "<PERSON><PERSON><PERSON>", "richTransactionDetailStaked": "Diuntukkan", "richTransactionDetailStatus": "Status", "richTransactionDetailSwap": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailSwapDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailTo": "<PERSON><PERSON><PERSON>", "richTransactionDetailTokenSwap": "Pertukaran Token", "richTransactionDetailUnknownNFT": "NFT Tidak Diketahui", "richTransactionDetailUnlisted": "Tidak Tersenarai", "richTransactionDetailUnstaked": "Belum diuntukkan", "richTransactionDetailValidator": "<PERSON><PERSON><PERSON>", "richTransactionDetailViewOnExplorer": "<PERSON>hat pada {{explorer}}", "richTransactionDetailWithdrawStake": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailYouPaid": "<PERSON><PERSON>", "richTransactionDetailYouReceived": "<PERSON><PERSON>", "richTransactionDetailUnwrapDetails": "<PERSON><PERSON><PERSON>", "richTransactionDetailTokenUnwrap": "<PERSON><PERSON>", "activityItemsRefreshFailed": "<PERSON>l memuat urus niaga yang lebih baharu.", "activityItemsPagingFailed": "<PERSON>l memuat urus niaga yang lebih lama.", "activityItemsTestnetNotAvailable": "<PERSON><PERSON><PERSON> urus niaga Testnet tidak tersedia pada masa ini", "historyUnknownDappName": "Tidak diketahui", "historyStatusSucceeded": "<PERSON><PERSON><PERSON><PERSON>", "historyNetwork": "<PERSON><PERSON><PERSON><PERSON>", "historyAttemptedAmount": "<PERSON><PERSON><PERSON>", "historyAmount": "<PERSON><PERSON><PERSON>", "sendAddressBookButtonLabel": "<PERSON><PERSON>", "addressBookSelectAddressBook": "<PERSON><PERSON>", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON><PERSON> alamat disimpan", "sendAddressBookRecentlyUsed": "<PERSON><PERSON>", "addressBookSelectRecentlyUsed": "<PERSON><PERSON>", "sendConfirmationLabel": "Label", "sendConfirmationMessage": "<PERSON><PERSON><PERSON>", "sendConfirmationNetworkFee": "<PERSON><PERSON>", "sendConfirmationPrimaryText": "<PERSON><PERSON><PERSON>", "sendWarning_INSUFFICIENT_FUNDS": "<PERSON> yang tidak <PERSON>, urus niaga ini besar kemungkinannya akan gagal jika diserahkan.", "sendFungibleSummaryNetwork": "<PERSON><PERSON><PERSON><PERSON>", "sendFungibleSummaryNetworkFee": "<PERSON><PERSON>", "sendFungibleSummaryEstimatedTime": "<PERSON><PERSON>", "sendFungiblePendingEstimatedTime": "<PERSON><PERSON><PERSON>", "sendFungibleSummaryEstimatedTimeDescription": "Kelajuan urus niaga Ethereum berbeza berdasarkan beberapa faktor. <PERSON>a boleh mempercepatnya dengan mengklik pada “<PERSON><PERSON>.", "sendSummaryBitcoinPendingTxTitle": "<PERSON><PERSON>k boleh men<PERSON> pindahan", "sendSummaryBitcoinPendingTxDescription": "Anda hanya boleh ada satu pindahan BTC yang menunggu sekali. Sila tunggu sehingga ia telah selesai untuk menyerahkan pindahan baharu.", "sendFungibleSatProtectionTitle": "Menghantar dengan <PERSON>", "sendFungibleSatProtectionExplainer": "Phantom memastikan bahawa Ordinal dan BRC20 anda tidak akan digunakan untuk yuran urus niaga atau pindahan Bitcoin.", "sendFungibleTransferFee": "<PERSON>ran pindahn token", "sendFungibleTransferFeeToolTip": "Pencipta token ini menerima yuran pada setiap pindahan. Ini bukan yuran yang dicaj atau dikumpulkan oleh Phantom.", "sendFungibleInterestBearingPercent": "<PERSON><PERSON>", "sendFungibleNonTransferable": "Tak Boleh Pindah Milik", "sendFungibleNonTransferableToolTip": "Token ini tidak boleh dipindahkan ke akaun lain.", "sendFungibleNonTransferableYes": "Ya", "sendStatusErrorMessageInterpolated": "Terdapat ralat cuba menghantar token kepada <1>{{uiRecipient}}</1>", "sendStatusErrorMessageInsufficientBalance": "Anda tidak mempunyai baki yang mencukupi untuk menyelesaikan urus niaga.", "sendStatusErrorTitle": "Tidak dapat hantar", "sendStatusLoadingTitle": "Menghantar...", "sendStatusSuccessMessageInterpolated": "Token anda berjaya di<PERSON>tar ke <1>{{uiRecipient}}</1>", "sendStatusSuccessTitle": "Dihantar!", "sendStatusConfirmedSuccessTitle": "Dihantar!", "sendStatusSubmittedSuccessTitle": "<PERSON><PERSON> Di<PERSON>ah<PERSON>", "sendStatusEstimatedTransactionTime": "<PERSON><PERSON><PERSON>anggar<PERSON>: {{time}}", "sendStatusViewTransaction": "<PERSON><PERSON> urus niaga", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> kepada <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> telah berjaya di<PERSON>tar kepada <2>{{uiRecipient}}</2>", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> telah berjaya di<PERSON>tar kepada <2>{{uiRecipient}}</2>", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> gagal dihantar kepada <2>{{uiRecipient}}</2>", "sendFungibleSolanaErrorCode": "<PERSON><PERSON> {{code}}", "sendFormErrorInsufficientBalance": "Baki yang tidak mencukupi", "sendFormErrorEmptyAmount": "<PERSON><PERSON><PERSON>", "sendFormInvalidAddress": "Alamat {{assetName}} tidak sah", "sendFormInvalidUsernameOrAddress": "<PERSON>a pengguna atau alamat tidak sah", "sendFormErrorInvalidSolanaAddress": "<PERSON><PERSON><PERSON> tidak sah", "sendFormErrorInvalidTwitterHandle": "<PERSON>a pengguna Twitter tidak berdaftar", "sendFormErrorInvalidDomain": "Domain ini tidak berdaftar", "sendFormErrorInvalidUsername": "<PERSON>a pengguna ini tidak berdaftar", "sendFormErrorMinRequiredInterpolated": "Sekurang-kurangnya {{minAmount}} {{tokenName}} diperlukan", "sendRecipientTextareaPlaceholder": "Alamat SOL penerima", "sendRecipientTextAreaPlaceholder2": "<PERSON><PERSON><PERSON> {{symbol}} penerima", "sendMemoOptional": "Memo (tidak wajib)", "sendMemo": "Memo", "sendOptional": "pilihan", "settings": "Tetapan", "settingsDapps": "dApps", "settingsSelectedAccount": "<PERSON><PERSON><PERSON> te<PERSON>", "settingsAddressBookNoLabel": "Tiada Label", "settingsAddressBookPrimary": "<PERSON><PERSON>", "settingsAddressBookRecentlyUsed": "<PERSON><PERSON>", "settingsAddressBookSecondary": "Menguruskan alamat yang <PERSON>anya digunakan", "settingsAutoLockTimerPrimary": "Penjaga masa Kunci Automatik", "settingsAutoLockTimerSecondary": "Ubah tempoh penjaga masa kunci automatik anda", "settingsChangeLanguagePrimary": "<PERSON><PERSON>", "settingsChangeLanguageSecondary": "<PERSON><PERSON> bahasa paparan", "settingsChangeNetworkPrimary": "<PERSON><PERSON>", "settingsChangeNetworkSecondary": "Menetapkan tetapan rang<PERSON>an anda", "settingsChangePasswordPrimary": "<PERSON><PERSON>", "settingsChangePasswordSecondary": "<PERSON><PERSON> kata laluan skrin kunci anda", "settingsCompleteBetaSurvey": "Lengkapkan Tinjauan Beta", "settingsDisplayLanguage": "Tunjukkan Bahasa", "settingsErrorCannotExportLedgerPrivateKey": "Tidak dapat mengeksport kunci peribadi Ledger", "settingsErrorCannotRemoveAllWallets": "Tidak dapat menyingkirkan semua akaun", "settingsExportPrivateKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Rangkaian Phantom RPC", "settingsTestNetworks": "<PERSON><PERSON><PERSON><PERSON>", "settingsUseCustomNetworks": "<PERSON><PERSON><PERSON>", "settingsTestnetMode": "Mod Testnet", "settingsTestnetModeDescription": "<PERSON><PERSON><PERSON><PERSON> kepada baki dan sambungan aplikasi.", "settingsWebViewDebugging": "Nyahpepijat <PERSON>", "settingsWebViewDebuggingDescription": "Me<PERSON><PERSON><PERSON> anda memeriksa dan menyah pepijat paparan web penyemak imbas dalam aplikasi.", "settingsTestNetworksInfo": "<PERSON><PERSON><PERSON> kepada mana-mana rang<PERSON>an Testnet yang bermaksud bagi tujuan ujian sahaja. <PERSON><PERSON> sedar bahawa token di RangkaianTestnet tidak mempunyai sebarang nilai monetari.", "settingsEmojis": "<PERSON><PERSON><PERSON>", "settingsNoAddresses": "<PERSON><PERSON><PERSON>", "settingsAddressBookEmptyHeading": "<PERSON><PERSON> anda kosong", "settingsAddressBookEmptyText": "<PERSON><PERSON> butang “+” atau “Tambah Alamat” untuk menambah ke alamat kegemaran anda", "settingsEditWallet": "<PERSON><PERSON>", "settingsNoTrustedApps": "Tiada aplikasi dipercayai", "settingsNoConnections": "<PERSON><PERSON><PERSON> tiada sambungan lagi.", "settingsRemoveWallet": "Singkirkan A<PERSON>un", "settingsResetApp": "<PERSON>", "settingsBlocked": "Tersekat", "settingsBlockedAccounts": "<PERSON><PERSON><PERSON>", "settingsNoBlockedAccounts": "<PERSON><PERSON><PERSON> akaun tersekat.", "settingsRemoveSecretPhrase": "Singkirkan Rangkai Kata Rahsia", "settingsResetAppWithSecretPhrase": "<PERSON> Se<PERSON>p<PERSON> den<PERSON> Rangkai Kata Rahsia", "settingsResetSecretRecoveryPhrase": "<PERSON><PERSON>", "settingsShowSecretRecoveryPhrase": "<PERSON><PERSON>", "settingsShowSecretRecoveryPhraseSecondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsShowSecretRecoveryPhraseTertiary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsTrustedAppsAutoConfirmActiveUntil": "Sehingga {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Sahkan Secara Automatik", "settingsTrustedAppsDisclaimer": "<PERSON><PERSON> bolehkan sahkan secara automatik di laman yang dipercayai", "settingsTrustedAppsLastUsed": "Digunakan {{formattedTimestamp}} lalu", "settingsTrustedAppsPrimary": "Aplikasi Bersambung", "settingsTrustedApps": "Aplikasi Dipercayai", "settingsTrustedAppsRevoke": "Membatalkan", "settingsTrustedAppsRevokeToast": "{{trustedApp}} terputus sambungan", "settingsTrustedAppsSecondary": "Tetapkan aplikasi dipercayai anda", "settingsTrustedAppsToday": "<PERSON> ini", "settingsTrustedAppsYesterday": "Semalam", "settingsTrustedAppsLastWeek": "<PERSON><PERSON>", "settingsTrustedAppsBeforeYesterday": "<PERSON><PERSON><PERSON> awal", "settingsTrustedAppsDisconnectAll": "<PERSON><PERSON> sambungan daripada semua", "settingsTrustedAppsDisconnectAllToast": "<PERSON><PERSON><PERSON> a<PERSON><PERSON> terputus", "settingsTrustedAppsEndAutoConfirmForAll": "Tamatkan sahkan secara automatik untuk semua", "settingsTrustedAppsEndAutoConfirmForAllToast": "<PERSON><PERSON><PERSON> sesi sahkan secara automatik telah tamat", "settingsSecurityPrimary": "Keselamatan & Privasi", "settingsSecuritySecondary": "<PERSON><PERSON> kini tetapan keselamatan anda", "settingsActiveNetworks": "Rangkaian Aktif", "settingsActiveNetworksAll": "<PERSON><PERSON><PERSON>", "settingsActiveNetworksSolana": "<PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "<PERSON><PERSON> ialah rang<PERSON>an lalai dan sentiasa di<PERSON>.", "settingsDeveloperPrimary": "Tetapan <PERSON>", "settingsAdvanced": "<PERSON><PERSON><PERSON>", "settingsTransactions": "<PERSON><PERSON><PERSON>", "settingsAutoConfirm": "Tetapan <PERSON> Secara Automatik", "settingsSecurityAnalyticsPrimary": "Berkongsi Analitik <PERSON>", "settingsSecurityAnalyticsSecondary": "Bolehkan untuk membantu kami bertambah baik", "settingsSecurityAnalyticsHelper": "Phantom tidak menggunakan maklumat peribadi anda bagi tujuan analitik", "settingsSuspiciousCollectiblesPrimary": "Sembunyikan Kolektibel Yang Di<PERSON>rigai", "settingsSuspiciousCollectiblesSecondary": "Togol untuk menyembunyikan kolektibel yang ditandai", "settingsPreferredBitcoinAddress": "Alamat Bitcoin Pilihan", "settingsEnabledAddressesUpdated": "<PERSON><PERSON><PERSON> yang tampak dikemas kini!", "settingsEnabledAddresses": "<PERSON><PERSON><PERSON><PERSON>", "settingsBitcoinPaymentAddressForApps": "<PERSON><PERSON><PERSON> untuk Aplikasi", "settingsBitcoinOrdinalsAddressForApps": "Alamat Ordinal untuk Aplikasi", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "<PERSON><PERSON><PERSON> kedua-dua jenis alamat di atas dibolehkan, untuk aplikasi tertentu seperti Magic Eden, alamat Native Segwit anda akan digunakan untuk pembelian dana. Aset dibeli akan diterima dalam alamat <PERSON> anda.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "<PERSON><PERSON><PERSON> lalai di Phantom untuk menjamin k<PERSON>.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(<PERSON><PERSON>)", "settingsPreferredBitcoinAddressTaprootExplainer": "<PERSON><PERSON> al<PERSON>t yang paling moden, <PERSON><PERSON>a dengan yuran urus niaga yang lebih murah.", "settingsPreferredExplorers": "Explorer <PERSON><PERSON><PERSON>", "settingsPreferredExplorersSecondary": "<PERSON><PERSON> kepada explorer blok rantai pilihan anda", "settingsCustomGasControls": "Kawalan Min<PERSON> Tersuai", "settingsSupportDesk": "<PERSON><PERSON>", "settingsSubmitATicket": "<PERSON><PERSON><PERSON>", "settingsAttachApplicationLogs": "Lampirkan Log Aplikasi", "settingsDownloadApplicationLogs": "Muat Tu<PERSON> Lo<PERSON> Aplikasi", "settingsDownloadApplicationLogsShort": "<PERSON><PERSON>", "settingsDownloadApplicationLogsHelper": "Mengandungi data tempatan, laporan runtuh dan alamat dompet awam untuk membantu menyelesaikan isu Phantom Support", "settingsDownloadApplicationLogsWarning": "Tiada data sensitif seperti rangkai kata seed atau kunci peribadi termasuk.", "settingsWallet": "Dompet", "settingsPreferences": "<PERSON><PERSON><PERSON><PERSON>", "settingsSecurity": "Keselamatan", "settingsDeveloper": "Pembangun", "settingsSupport": "Sokongan", "settingsWalletShortcutsPrimary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsAppIcon": "<PERSON><PERSON>", "settingsAppIconDefault": "<PERSON><PERSON>", "settingsAppIconLight": "<PERSON><PERSON>", "settingsAppIconDark": "<PERSON><PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "<PERSON><PERSON><PERSON>", "settingsSearchResultSelected": "<PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultExport": "Eksport", "settingsSearchResultSeed": "Seed", "settingsSearchResultTrusted": "Dipercayai", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "<PERSON><PERSON><PERSON>", "settingsSearchResultLogs": "Log", "settingsSearchResultBiometric": "Biometrik", "settingsSearchResultTouch": "<PERSON><PERSON><PERSON>", "settingsSearchResultFace": "<PERSON><PERSON><PERSON>", "settingsSearchResultShortcuts": "<PERSON><PERSON><PERSON>", "settingsAllSitesPermissionsTitle": "Capai Phantom di semua tapak", "settingsAllSitesPermissionsSubtitle": "Benark<PERSON> anda untuk menggunakan aplikasi secara selanjar dengan Phantom tanpa mengklik pada sambungan", "settingsAllSitesPermissionsDisabled": "Penyemak imbas anda tidak menyokong mengubah tetapan ini", "settingsSolanaCopyTransaction": "<PERSON><PERSON><PERSON><PERSON>", "settingsSolanaCopyTransactionDetails": "Salinkan data urus niaga bersiri ke papan keratan", "settingsAutoConfirmHeader": "Sahkan Secara Automatik", "refreshWebpageToApplyChanges": "Segar semula laman web untuk mengenakan perubahan", "settingsExperimentalTitle": "Ciri-ciri <PERSON>", "settingsExprimentalSolanaActionsSubtitle": "Besarkan butang <PERSON> secara automatik semasa pautan berkaitan dikesan di X.com", "stakeAccountCardActiveStake": "Stake Aktif", "stakeAccountCardBalance": "Baki", "stakeAccountCardRentReserve": "<PERSON><PERSON><PERSON>", "stakeAccountCardRewards": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountCardRewardsTooltip": "Ini adalah ganjaran terbaru yang anda peroleh untuk staking. <PERSON>a akan diberikan ganjaran setiap 3 hari.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "Berkunci Sehingga", "stakeRewardsHistoryTitle": "<PERSON><PERSON><PERSON>", "stakeRewardsActivityItemTitle": "Ganjaran", "stakeRewardsHistoryEmptyList": "<PERSON><PERSON><PERSON> gan<PERSON>an", "stakeRewardsTime_zero": "<PERSON> ini", "stakeRewardsTime_one": "Semalam", "stakeRewardsTime_other": "{{count}} hari yang lalu", "stakeRewardsItemsPagingFailed": "<PERSON>l memuat ganjaran yang lebih lama.", "stakeAccountCreateAndDelegateErrorStaking": "<PERSON><PERSON><PERSON><PERSON> masalah 's<PERSON>' kepada pengesah ini. Sila cuba semula.", "stakeAccountCreateAndDelegateSolStaked": "Staking SOL berjaya!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "SOL anda akan mula mendapat ganjaran <1></1> buat beberapa hari setelah akaun stake menjadi aktif.", "stakeAccountCreateAndDelegateStakingFailed": "Staking Telah Gagal", "stakeAccountCreateAndDelegateStakingSol": "Staking SOL...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "<PERSON><PERSON> mencipta akaun staking, k<PERSON><PERSON>an menugaskan SOL anda kepada", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "<PERSON><PERSON> mencipta akaun staking, k<PERSON><PERSON>an menugaskan SOL anda kepada {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "<PERSON><PERSON>", "stakeAccountDeactivateStakeSolUnstaked": "Staking SOL dibatalkan!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Anda akan dapat menarik stake anda <1></1> dalam masa beberapa hari setelah akaun stake menjadi aktif.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Anda akan dapat mengeluarkan stake anda dalam masa beberapa hari setelah akaun stake menjadi tidak aktif.", "stakeAccountDeactivateStakeUnstakingFailed": "Staking Telah Gagal", "stakeAccountDeactivateStakeUnstakingFailedDescription": "<PERSON>rda<PERSON><PERSON> masalah memba<PERSON>kan 'staking' da<PERSON><PERSON> pengesah ini. Sila cuba semula.", "stakeAccountDeactivateStakeUnstakingSol": "Membatalkan Staking SOL...", "stakeAccountDeactivateStakeUnstakingSolDescription": "<PERSON><PERSON> memu<PERSON>an proses membatalkan stake SOL anda.", "stakeAccountDeactivateStakeViewTransaction": "<PERSON><PERSON>", "stakeAccountDelegateStakeSolStaked": "Staking SOL berjaya!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "SOL anda akan mula mendapat ganjaran <1></1> buat beberapa hari setelah akaun stake menjadi aktif.", "stakeAccountDelegateStakeStakingFailed": "Staking Telah Gagal", "stakeAccountDelegateStakeStakingFailedDescription": "<PERSON><PERSON><PERSON><PERSON> masalah 's<PERSON>' kepada pengesah ini. Sila cuba semula.", "stakeAccountDelegateStakeStakingSol": "Staking SOL...", "stakeAccountDelegateStakeStakingSolDescription": "Kita menugaskan SOL anda.", "stakeAccountDelegateStakeViewTransaction": "<PERSON><PERSON>", "stakeAccountListActivationActivating": "Mengaktifkan", "stakeAccountListActivationActive": "Aktif", "stakeAccountListActivationInactive": "Tidak aktif", "stakeAccountListActivationDeactivating": "Menyahaktif<PERSON>", "stakeAccountListErrorFetching": "Kita tidak dapat mengambil akaun stake. Sila cuba lagi kemudian.", "stakeAccountListNoStakingAccounts": "Tiada <PERSON>", "stakeAccountListReload": "<PERSON><PERSON> semula", "stakeAccountListViewPrimaryText": "<PERSON><PERSON>", "stakeAccountListViewStakeSOL": "SOL Stake", "stakeAccountListItemStakeFee": "yuran {{fee}}", "stakeAccountViewActionButtonRestake": "Staking semula", "stakeAccountViewActionButtonUnstake": "Batalkan staking", "stakeAccountViewError": "<PERSON><PERSON>", "stakeAccountViewPrimaryText": "<PERSON><PERSON>", "stakeAccountViewRestake": "Staking semula", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Pada masa ini SOL anda sedang stake dengan pengesah. <PERSON>a akan perlu membatalkan stake untuk <1></1>mencapai dana tersebut. <3><PERSON><PERSON><PERSON> lebih lan<PERSON>t</3>", "stakeAccountViewStakeInactive": {"part1": "Akaun stake ini tidak aktif. Pertimbangkan menarik stake atau mencari pengesah untuk menugaskan stake.", "part2": "<PERSON><PERSON><PERSON> lebih lan<PERSON>t"}, "stakeAccountViewStakeNotFound": "Akaun stake ini tidak boleh ditemui.", "stakeAccountViewViewOnExplorer": "<PERSON>hat pada {{explorer}}", "stakeAccountViewWithdrawStake": "Mengeluarkan Stake", "stakeAccountViewWithdrawUnstakedSOL": "Mengeluarkan SOL Dibatalkan Stake", "stakeAccountInsufficientFunds": "Tiada SOL yang mencukupi yang terseida untuk unstake atau keluarkan.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL Dikeluarkan!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "SOL anda telah dikeluarkan.", "part2": "Akaun stake ini akan disingkirkan secara automatik dalam beberapa minit kemudiannya."}, "stakeAccountWithdrawStakeViewTransaction": "<PERSON><PERSON>", "stakeAccountWithdrawStakeWithdrawalFailed": "Pengeluaran Telah Gagal", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Terdapat masalah mengeluarkan daripada akaun stake ini. Sila cuba semula.", "stakeAccountWithdrawStakeWithdrawingSol": "Mengeluarkan SOL...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Kita mengeluarkan SOL anda daripada akaun stake ini.", "startEarningSolAccount": "<PERSON>un", "startEarningSolAccounts": "<PERSON>un", "startEarningSolErrorClosePhantom": "Ketik di sini dan cuba lagi", "startEarningSolErrorTroubleLoading": "Masalah memuat stake", "startEarningSolLoading": "Memuat...", "startEarningSolPrimaryText": "<PERSON><PERSON> S<PERSON>", "startEarningSolSearching": "<PERSON><PERSON><PERSON> aka<PERSON> staking", "startEarningSolStakeTokens": "Token stake dan mendapat ganjaran", "startEarningSolYourStake": "<PERSON>ake anda", "unwrapFungibleTitle": "<PERSON><PERSON> kepada {{tokenSymbol}}", "unwrapFungibleDescription": "<PERSON><PERSON>k balik da<PERSON>ada {{fromToken}} untuk {{toToken}}", "unwrapFungibleConfirmSwap": "<PERSON><PERSON><PERSON>", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "<PERSON><PERSON>", "swapFeesFees": "<PERSON><PERSON>", "swapFeesPhantomFee": "<PERSON>ran <PERSON>", "swapFeesPhantomFeeDisclaimer": "<PERSON>a sentiasa mencari harga terbaik mungkin daripada penyedia kecairan utama. Yuran sebanyak {{feePercentage}} telah diambil kira secara automatik ke dalam sebut harga ini.", "swapFeesRate": "<PERSON><PERSON>", "swapFeesRateDisclaimer": "Harga terbaik ditemui oleh Jupiter Aggregator di seluruh berbilang pertukaran tak terpusat.", "swapFeesRateDisclaimerMultichain": "Harga terbaik ditemui di seluruh berbilang pertukaran tak terpusat.", "swapFeesPriceImpact": "<PERSON><PERSON>", "swapFeesHighPriceImpact": "<PERSON><PERSON>", "swapFeesPriceImpactDisclaimer": "Perbezaan antara harga pasaran dan harga anggaran berdasarkan saiz dagangan anda.", "swapFeesSlippage": "<PERSON><PERSON><PERSON><PERSON>", "swapFeesHighSlippage": "Toleransi <PERSON>ci<PERSON>", "swapFeesHighSlippageDisclaimer": "<PERSON><PERSON> niaga anda akan gagal jika harga berubah tak sesuai melebihi {{slippage}}%.", "swapTransferFee": "<PERSON><PERSON>", "swapTransferFeeDisclaimer": "Berdagang ${{symbol}} menanggung yuran pindahan {{feePercent}}% yang ditetapkan oleh pencipta token, bukan Phantom.", "swapTransferFeeDisclaimerMany": "Berdagang token terpilih menanggung yuran {{feePercent}}% yang ditetapkan oleh pencipta token, bukan Phantom.", "swapFeesSlippageDisclaimer": "<PERSON><PERSON><PERSON> harga perdagangan anda boleh melencong daripada sebut harga yang diberikan.", "swapFeesProvider": "Penyedia", "swapFeesProviderDisclaimer": "Pertukaran tak terpusat digunakan untuk melengkapkan perdagangan anda.", "swapEstimatedTime": "<PERSON><PERSON>", "swapEstimatedTimeShort": "<PERSON><PERSON><PERSON>", "swapEstimatedTimeDisclaimer": "<PERSON><PERSON>n anggaran untuk jambatan adalah berbeza-beza bergantung pada beberapa faktor yang menjejaskan kelajuan urus niaga.", "swapSettingsButtonCommand": "<PERSON><PERSON>", "swapQuestionRetry": "Cuba semula?", "swapUnverifiedTokens": "Token Tidak Disahkan", "swapSectionTitleTokens": "{{section}} Token", "swapFlowYouPay": "<PERSON><PERSON>", "swapFlowYouReceive": "<PERSON><PERSON>", "swapFlowActionButtonText": "<PERSON><PERSON>", "swapAssetCardTokenNetwork": "{{symbol}} pada {{network}}", "swapAssetCardMaxButton": "<PERSON><PERSON>", "swapAssetCardSelectTokenAndNetwork": "<PERSON><PERSON><PERSON> dan <PERSON>", "swapAssetCardBuyTitle": "<PERSON><PERSON>", "swapAssetCardSellTitle": "<PERSON><PERSON>", "swapAssetWarningUnverified": "Token ini tidak disahkan. <PERSON><PERSON> be<PERSON> dengan token yang anda percayai.", "swapAssetWarningPermanentDelegate": "Seorang wakil boleh menggunakan atau memindahkan token ini selama-lamanya.", "swapSlippageSettingsTitle": "<PERSON><PERSON><PERSON>", "swapSlippageSettingsSubtitle": "<PERSON><PERSON> niaga anda akan gagal jika perubahan harga melebihi gelin<PERSON>ran. <PERSON><PERSON> yang terlalu tinggi akan mengakibatkan perdagangan yang tak menguntungkan.", "swapSlippageSettingsCustom": "Tersuai", "swapSlippageSettingsHighSlippageWarning": "<PERSON><PERSON> niaga anda mungkin menjalankan perdagangan \"frontrun\" dan mengakibatkan perdagangan yang tak menguntungkan.", "swapSlippageSettingsCustomMinError": "<PERSON><PERSON> masukkan nilai yang lebih besar daripada {{minSlippage}}%.", "swapSlippageSettingsCustomMaxError": "<PERSON><PERSON> masukkan nilai kurang daripada {{maxSlippage}}%.", "swapSlippageSettingsCustomInvalidValue": "<PERSON>la masukkan nilai yang sah.", "swapSlippageSettingsAutoSubtitle": "Phantom akan mencari gelinciran terendah untuk pertukaran yang berjaya.", "swapSlippageSettingsAuto": "Automatik", "swapSlippageSettingsFixed": "Tetap", "swapSlippageOptInTitle": "Gelinciran Automatik", "swapSlippageOptInSubtitle": "Phantom akan mencari gelinciran terendah untuk pertukaran yang berjaya. <PERSON><PERSON> boleh menukar ini bila-bila masa di <PERSON> → Gelinciran.", "swapSlippageOptInEnableOption": "Bolehkan Gelinciran Automatik", "swapSlippageOptInRejectOption": "Teruskan dengan <PERSON>", "swapQuoteFeeDisclaimer": "Sebut harga termasuk yuran Phantom {{feePercentage}}", "swapQuoteMissingContext": "Hilang konteks sebut harga pertukaran", "swapQuoteErrorNoQuotes": "Cuba bertukar tanpa sebut harga", "swapQuoteSolanaNetwork": "<PERSON><PERSON><PERSON><PERSON>", "swapQuoteNetwork": "<PERSON><PERSON><PERSON><PERSON>", "swapQuoteOneTimeSerumAccount": "<PERSON><PERSON><PERSON>um satu kali", "swapQuoteOneTimeTokenAccount": "Akaun token satu kali", "swapQuoteBridgeFee": "<PERSON><PERSON> Rantai", "swapQuoteDestinationNetwork": "<PERSON><PERSON><PERSON><PERSON>", "swapQuoteLiquidityProvider": "<PERSON><PERSON><PERSON>", "swapReviewFlowActionButtonPrimary": "<PERSON><PERSON><PERSON><PERSON>", "swapReviewFlowPrimaryText": "<PERSON><PERSON>", "swapReviewFlowYouPay": "<PERSON><PERSON>", "swapReviewFlowYouReceive": "<PERSON><PERSON>", "swapReviewInsufficientBalance": "<PERSON>", "ugcSwapWarningTitle": "<PERSON><PERSON>", "ugcSwapWarningBody1": "Token ini berdagang pada pelancar token {{programName}}.", "ugcSwapWarningBody2": "<PERSON>lai token ini boleh naik turun gila, mengakibatkan keuntungan atau kerugian kewangan yang banyak. <PERSON>la berdagang atas tanggungan sendiri.", "ugcSwapWarningConfirm": "<PERSON><PERSON> faham", "bondingCurveProgressLabel": "<PERSON><PERSON><PERSON><PERSON> Curve", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "Di dalam model bonding curve, harga token ditentukan oleh bentuk lengkung, semakin bertambah semasa lebih banyak token dibeli dan berkurangan ketika token dijual. A<PERSON>bila token telah habis dijual, se<PERSON>a kecairan akan didepositkan ke Raydium dan terbakar.", "ugcFungibleWarningBanner": "Token ini berdagang pada {{programName}}", "ugcCreatedRowLabel": "<PERSON><PERSON><PERSON>", "ugcStatusRowLabel": "Status", "ugcStatusRowValue": "Tersenggat", "swapTxConfirmationReceived": "<PERSON>terima!", "swapTxConfirmationSwapFailed": "<PERSON><PERSON><PERSON><PERSON> telah gagal", "swapTxConfirmationSwapFailedStaleQuota": "Sebut harga tidak sah lagi. Sila cuba lagi.", "swapTxConfirmationSwapFailedSlippageLimit": "<PERSON><PERSON><PERSON><PERSON> anda terlalu rendah untuk pertukaran ini. <PERSON>la tambahkan gelinciran anda di atas skrin Pertukaran dan cuba lagi.", "swapTxConfirmationSwapFailedInsufficientBalance": "Kita tidak dapat melengkapkan permintaan. Anda tidak mempunyai baki yang mencukupi untuk melengkapkan urus niaga.", "swapTxConfirmationSwapFailedEmptyRoute": "Kecairan pasangan token ini telah diubah. Kita tidak dapat mencari sebut harga yang sesuai. Sila cuba lagi atau laraskan jumlah token.", "swapTxConfirmationSwapFailedAcountFrozen": "Token ini telah dibekukan oleh penciptanya. Anda tidak boleh menghantar atau menukar token ini.", "swapTxConfirmationSwapFailedTryAgain": "<PERSON><PERSON><PERSON><PERSON> telah gagal, sila cuba lagi", "swapTxConfirmationSwapFailedUnknownError": "Kita tidak dapat menyelesaikan pertukaran. Dana anda tidak terjejas. Sila cuba lagi. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Kita tidak dapat meniru per<PERSON>n. Dana anda tidak terjejas. Sila cuba lagi.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Kita tidak dapat menyelesaikan pertukaran. Dana anda tidak terjejas. Sila cuba lagi. ", "swapTxConfirmationSwapFailedInsufficientGas": "<PERSON><PERSON><PERSON> anda tidak mempunyai dana yang mencukupi untuk menyelesaikan urus niaga. <PERSON>la tambah lebih dana ke akaun anda dan cuba lagi.", "swapTxConfirmationSwapFailedLedgerReject": "Pertukara<PERSON> ditolak oleh pengguna pada peranti perkakasan.", "swapTxConfirmationSwapFailedLedgerConnectionError": "Pertu<PERSON><PERSON> ditolak disebabkan oleh ralat sambungan peranti. Sila cuba lagi.", "swapTxConfirmationSwapFailedLedgerSignError": "Pertukara<PERSON> ditolak disebabkan oleh ralat tandatangan peranti. Sila cuba lagi.", "swapTxConfirmationSwapFailedLedgerError": "Pertu<PERSON><PERSON> ditolak disebabkan oleh ralat peranti. Sila cuba lagi.", "swapTxConfirmationSwappingTokens": "<PERSON><PERSON><PERSON> token...", "swapTxConfirmationTokens": "Token", "swapTxConfirmationTokensDeposited": "Sudah selesai! Token telah dimasukkan ke dalam dompet anda", "swapTxConfirmationTokensDepositedTitle": "Sudah se<PERSON>ai!", "swapTxConfirmationTokensDepositedBody": "Token telah dimasukkan ke dalam dompet anda", "swapTxConfirmationTokensWillBeDeposited": "akan dimasukkan ke dalam dompet anda setelah urus niaga dilengka<PERSON>kan", "swapTxConfirmationViewTransaction": "<PERSON><PERSON>", "swapTxBridgeSubmitting": "<PERSON><PERSON><PERSON><PERSON>", "swapTxBridgeSubmittingDescription": "Menukar {{sellAmount}} di {{sellNetwork}} sebanyak {{buyAmount}} di {{buyNetwork}}", "swapTxBridgeFailed": "<PERSON><PERSON> niaga Telah Gagal Diserahkan", "swapTxBridgeFailedDescription": "Kita tidak dapat melengkapkan permintaan tersebut.", "swapTxBridgeSubmitted": "<PERSON><PERSON> Di<PERSON>ah<PERSON>", "swapTxBridgeSubmittedDescription": "<PERSON><PERSON><PERSON> U<PERSON>aga Dianggarkan: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON>a boleh mengetepikan tetingkap ini dengan selamat.", "swapperSwitchTokens": "<PERSON><PERSON> token", "swapperMax": "<PERSON><PERSON>", "swapperTooltipNetwork": "<PERSON><PERSON><PERSON><PERSON>", "swapperTooltipPrice": "<PERSON><PERSON>", "swapperTooltipAddress": "Kontrak", "swapperTrendingSortBy": "<PERSON><PERSON><PERSON>", "swapperTrendingTimeFrame": "Ra<PERSON><PERSON>", "swapperTrendingNetwork": "<PERSON><PERSON><PERSON><PERSON>", "swapperTrendingRank": "Ting<PERSON>", "swapperTrendingTokens": "Token Yang Popular Pada Ma<PERSON>", "swapperTrendingVolume": "<PERSON><PERSON><PERSON>", "swapperTrendingPrice": "<PERSON><PERSON>", "swapperTrendingPriceChange": "<PERSON><PERSON><PERSON>", "swapperTrendingMarketCap": "<PERSON><PERSON><PERSON><PERSON>", "swapperTrendingTimeFrame1h": "1j", "swapperTrendingTimeFrame24h": "24j", "swapperTrendingTimeFrame7d": "7h", "swapperTrendingTimeFrame30d": "30h", "swapperTrendingNoTokensFound": "Tiada token ditemui.", "switchToggle": "Togol", "termsOfServiceActionButtonAgree": "<PERSON><PERSON>", "termsOfServiceDisclaimerFeesDisabledInterpolated": "<PERSON><PERSON> men<PERSON> <1>\"<PERSON><PERSON>\"</1> anda <PERSON> <3><PERSON><PERSON> dan <PERSON></3> pertukaran token dengan <PERSON>.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "<PERSON>a telah menyemak Terma Perkhidmatan kami. <PERSON><PERSON> meng<PERSON> <1>\"<PERSON><PERSON>\"</1> anda bersetuju dengan <3><PERSON><PERSON></3> yang baru kami.<5></5><6></6>Terma <PERSON>idmatan yang baru kami merangkumi <8>struktur yuran</8> baharu untuk produk tertentu.", "termsOfServicePrimaryText": "<PERSON><PERSON>", "tokenRowUnknownToken": "Token Tidak Diketahui", "transactionsAppInteraction": "Interaksi aplikasi", "transactionsFailedAppInteraction": "Interaksi aplikasi telah gagal", "transactionsBidOnInterpolated": "Bida pada {{name}}", "transactionsBidFailed": "Bida telah gagal", "transactionsBoughtInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsBoughtCollectible": "Kolektibel Telah Dibeli", "transactionBridgeInitiated": "<PERSON><PERSON><PERSON>", "transactionBridgeInitiatedFailed": "<PERSON><PERSON><PERSON><PERSON>", "transactionBridgeStatusLink": "Semak Status di LI.FI", "transactionsBuyFailed": "<PERSON>i telah gagal", "transactionsBurnedSpam": "<PERSON><PERSON> terb<PERSON>", "transactionsBurned": "Dihapuskan", "transactionsUnwrapped": "<PERSON><PERSON><PERSON>", "transactionsUnwrappedFailed": "<PERSON><PERSON> telah gagal", "transactionsCancelBidOnInterpolated": "Bida dibatalkan pada {{name}}", "transactionsCancelBidOnFailed": "Gagal membatalkan bida", "transactionsError": "<PERSON><PERSON>", "transactionsFailed": "<PERSON><PERSON> gagal", "transactionsSwapped": "<PERSON><PERSON><PERSON>", "transactionsFailedSwap": "<PERSON><PERSON><PERSON><PERSON> telah gagal", "transactionsFailedBurn": "<PERSON> telah gagal", "transactionsFrom": "Daripada", "transactionsListedInterpolated": "Tersenarai {{name}}", "transactionsListedFailed": "Gagal untuk menyenaraikan", "transactionsNoActivity": "Tiada aktiviti", "transactionsReceived": "Diterima", "transactionsReceivedInterpolated": "SOL {{amount}} Diterima", "transactionsSending": "Menghantar...", "transactionsPendingCreateListingInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsPendingEditListingInterpolated": "Menyunting {{name}}", "transactionsPendingSolanaPayTransaction": "Mengesahkan Urus Niaga Bayar Solana", "transactionsPendingRemoveListingInterpolated": "Menyingkirkan daripada senarai {{name}}", "transactionsPendingBurningInterpolated": "Peleburan {{name}}", "transactionsPendingSending": "Menghantar", "transactionsPendingSwapping": "<PERSON><PERSON><PERSON>", "transactionsPendingBridging": "Menghubungkan", "transactionsPendingApproving": "Meluluskan", "transactionsPendingCreatingAndDelegatingStake": "<PERSON><PERSON><PERSON>t dan <PERSON><PERSON>n ta<PERSON>han", "transactionsPendingDeactivatingStake": "Menyahak<PERSON><PERSON><PERSON> ta<PERSON>han", "transactionsPendingDelegatingStake": "<PERSON><PERSON><PERSON>", "transactionsPendingWithdrawingStake": "<PERSON><PERSON><PERSON> balik ta<PERSON>han", "transactionsPendingAppInteraction": "Interaksi aplikasi belum se<PERSON>ai", "transactionsPendingBitcoinTransaction": "Urus niaga BTC belum se<PERSON>ai", "transactionsSent": "<PERSON><PERSON><PERSON>", "transactionsSendFailed": "<PERSON><PERSON> telah gagal", "transactionsSwapOn": "<PERSON><PERSON><PERSON><PERSON> pada {{dappName}}", "transactionsSentInterpolated": "SOL {{amount}} Dihantar", "transactionsSoldInterpolated": "Dijual {{name}}", "transactionsSoldCollectible": "Kolektibel Dijual", "transactionsSoldFailed": "Jualan telah gagal", "transactionsStaked": "Diuntukkan", "transactionsStakedFailed": "Stake telah gagal", "transactionsSuccess": "<PERSON><PERSON><PERSON>", "transactionsTo": "<PERSON><PERSON><PERSON>", "transactionsTokenSwap": "Pertukaran Token", "transactionsUnknownAmount": "Tidak diketahui", "transactionsUnlistedInterpolated": "Tidak tersenarai {{name}}", "transactionsUnstaked": "Batalkan stake", "transactionsUnlistedFailed": "Gagal untuk nyahsenarai", "transactionsDeactivateStake": "Menyahaktifkan stake", "transactionsDeactivateStakeFailed": "Gagal untuk menyahaktifkan stake", "transactionsWaitingForConfirmation": "<PERSON><PERSON><PERSON> pen<PERSON>", "transactionsWithdrawStake": "<PERSON><PERSON><PERSON><PERSON>", "transactionsWithdrawStakeFailed": "Unstake telah gagal", "transactionCancelled": "Di<PERSON><PERSON><PERSON>", "transactionCancelledFailed": "<PERSON><PERSON>", "transactionApproveToken": "Diluluskan {{tokenSymbol}}", "transactionApproveTokenFailed": "Gagal untuk meluluskan {{tokenSymbol}}", "transactionApprovalFailed": "<PERSON><PERSON><PERSON><PERSON> telah gagal", "transactionRevokeApproveToken": "Dibatalkan {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "<PERSON><PERSON> di<PERSON>an {{tokenSymbol}}", "transactionRevokeFailed": "<PERSON><PERSON> telah gagal", "transactionApproveDetailsTitle": "<PERSON><PERSON><PERSON>", "transactionCancelOrder": "<PERSON><PERSON><PERSON> pesanan", "transactionCancelOrderFailed": "Batalkan pesanan telah gagal", "transactionApproveAppLabel": "Aplikasi", "transactionApproveAmountLabel": "<PERSON><PERSON><PERSON>", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "<PERSON><PERSON><PERSON><PERSON>", "transactionApproveAllItems": "Luluskan semua butir", "transactionSpendUpTo": "<PERSON><PERSON><PERSON><PERSON>", "transactionCancel": "Batalkan <PERSON>", "transactionPrioritizeCancel": "Pembatalan Diutamakan", "transactionSpeedUp": "Mempercepatkan Urus Ni<PERSON>", "transactionCancelHelperText": "<PERSON><PERSON> niaga asal mungkin selesai sebelum ia batalkan.", "transactionSpeedUplHelperText": "Ini akan memaksim<PERSON>kan kelajuan urus niaga anda berdasarkan keadaan rang<PERSON>an.", "transactionCancelHelperMobile": "<PERSON><PERSON> be<PERSON> <1>se<PERSON><PERSON> {{amount}}</1> untuk percubaan membatalkan urus niaga ini. Urus niaga asal mungkin selesai sebelum ia dibatalkan.", "transactionCancelHelperMobileWithEstimate": "<PERSON><PERSON> be<PERSON> <1>se<PERSON><PERSON> {{amount}}</1> untuk percubaan membatalkan urus niaga ini. Ia seharusnya selesai dalam kira-kira {{timeEstimate}}. Urus niaga asal mungkin selesai sebelum ia dibatalkan.", "transactionSpeedUpHelperMobile": "<PERSON><PERSON> be<PERSON> <1>se<PERSON><PERSON> {{amount}}</1> untuk memaksimumkan kelajuan urus niaga ini.", "transactionSpeedUpHelperMobileWithEstimate": "<PERSON><PERSON> be<PERSON> <1>se<PERSON><PERSON> {{amount}}</1> untuk memaksimumkan kelajuan urus niaga ini. Ia sehar<PERSON>nya selesai dalam kira-kira {{timeEstimate}}.", "transactionEstimatedTime": "<PERSON><PERSON><PERSON> j<PERSON>", "transactionCancelingSend": "Membatalkan hantar", "transactionPrioritizingCancel": "Mengutamakan pem<PERSON>alan", "transactionCanceling": "Membatalkan", "transactionReplaceError": "Suatu ralat telah berlaku. Tiada yuran dikenakan kepada akaun anda.  <PERSON>a boleh cuba sekali lagi.", "transactionNotEnoughNative": "{{nativeTokenSymbol}} yang tidak mencukupi", "transactionGasLimitError": "<PERSON><PERSON> had minyak", "transactionGasEstimationError": "<PERSON>l menganggar minyak", "pendingTransactionCancel": "Batalkan", "pendingTransactionSpeedUp": "Mempercepatkan", "pendingTransactionStatus": "Status", "pendingTransactionPending": "Belum se<PERSON>ai", "pendingTransactionPendingInteraction": "Interaksi Bel<PERSON> Selesai", "pendingTransactionCancelling": "Membatalkan", "pendingTransactionDate": "<PERSON><PERSON><PERSON>", "pendingTransactionNetworkFee": "<PERSON><PERSON>", "pendingTransactionEstimatedTime": "<PERSON><PERSON><PERSON> j<PERSON>", "pendingTransactionEstimatedTimeHM": "{{hours}}j {{minutes}}m", "pendingTransactionEstimatedTimeMS": "{{minutes}}m {{seconds}}s", "pendingTransactionEstimatedTimeS": "{{seconds}}s", "pendingTransactionsSendingTitle": "Menghantar {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Tidak diketahui", "pendingTransactionUnknownApp": "Aplikasi Tidak Diketahui", "permanentDelegateTitle": "Berwakil", "permanentDelegateValue": "Kekal", "permanentDelegateTooltipTitle": "Perwakilan Kekal", "permanentDelegateTooltipValue": "<PERSON><PERSON><PERSON> kekal membe<PERSON>kan akaun lain untuk menguruskan token bagi pihak anda, ini termasuk burning atau memindahkan.", "unlockActionButtonUnlock": "<PERSON><PERSON><PERSON> kunci", "unlockEnterPassword": "<PERSON><PERSON><PERSON><PERSON> kata laluan anda", "unlockErrorIncorrectPassword": "<PERSON>a laluan yang salah", "unlockErrorSomethingWentWrong": "Se<PERSON><PERSON>u yang tidak kena berlaku, sila cuba lagi kemudian", "unlockForgotPassword": "<PERSON><PERSON><PERSON><PERSON> kata la<PERSON>an", "unlockPassword": "<PERSON><PERSON>", "forgotPasswordText": "Anda boleh mengeset semula kata laluan anda dengan memasukkan rangkai kata pemulihan perkataan 12-24 dompet anda. Phantom tidak boleh memulihkan kata laluan untuk anda.", "appInfo": "Maklumat Aplikasi", "lastUsed": "<PERSON><PERSON><PERSON><PERSON>", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "<PERSON><PERSON><PERSON> boleh didapati dengan akaun perka<PERSON>an.", "trustedAppAutoConfirmDisclaimer1": "<PERSON><PERSON> aktif, Phantom akan melu<PERSON>kan semua permintaan daripada aplikasi ini tanpa memaklumkan anda atau meminta pengesahan.", "trustedAppAutoConfirmDisclaimer2": "Membolehkan boleh meletakkan dana anda dalam risiko penipuan. <PERSON><PERSON> gunakan ciri ini dengan aplikasi yang anda percayai.", "validationUtilsPasswordIsRequired": "<PERSON><PERSON> dip<PERSON>an", "validationUtilsPasswordLength": "Kata laluan mestilah 8 aksara panjang", "validationUtilsPasswordsDontMatch": "<PERSON>a laluan tidak padan", "validationUtilsPasswordCantBeSame": "Anda tidak boleh menggunakan kata laluan lama anda", "validatorCardEstimatedApy": "APY Anggaran", "validatorCardCommission": "Komisen", "validatorCardTotalStake": "<PERSON><PERSON><PERSON>", "validatorCardNumberOfDelegators": "# <PERSON><PERSON><PERSON>i kuasa", "validatorListChooseAValidator": "<PERSON><PERSON><PERSON>", "validatorListErrorFetching": "Kita tidak dapat mengambil pengesah. Sila cuba lagi kemudian.", "validatorListNoResults": "<PERSON><PERSON><PERSON>", "validatorListReload": "<PERSON><PERSON> semula", "validatorInfoTooltip": "<PERSON><PERSON><PERSON>", "validatorInfoTitle": "<PERSON><PERSON><PERSON>", "validatorInfoDescription": "Dengan meletakkan stake SOL anda pada pengesah anda menyumbang terhadap prestasi dan keselamatan rangkaian <PERSON>, dan sambil anda melakukan ini anda akan memperoleh SOL sebagai ganjaran rangkaian.", "validatorApyInfoTooltip": "APY Anggaran", "validatorApyInfoTitle": "APY Anggaran", "validatorApyInfoDescription": "Ini adalah kadar pulangan yang anda peroleh kerana meletakkan stake SOL anda pada pengesah.", "validatorViewActionButtonStake": "Stake", "validatorViewErrorFetching": "Tidak dapat mendapat kembali pengesah.", "validatorViewInsufficientBalance": "Baki yang tidak mencukupi", "validatorViewMax": "<PERSON><PERSON>", "validatorViewPrimaryText": "<PERSON><PERSON>aking", "validatorViewDescriptionInterpolated": "<PERSON><PERSON><PERSON> jumlah SOL yang anda mahu untuk <1></1> stake dengan pengesah ini. <3><PERSON><PERSON><PERSON> lebih lanju</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL diperlukan untuk stake", "validatorViewValidator": "<PERSON><PERSON><PERSON>", "walletMenuItemsAddConnectWallet": "Tambah / Sambung Dompet", "walletMenuItemsBridgeAssets": "Mengarahkan <PERSON>", "walletMenuItemsHelpAndSupport": "Bantuan & Sokongan", "walletMenuItemsLockWallet": "<PERSON><PERSON><PERSON>", "walletMenuItemsResetSecretPhrase": "<PERSON><PERSON>", "walletMenuItemsShowMoreAccounts": "Tunjukkan {{count}} lebih...", "walletMenuItemsHideAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "toggleMultiChainHeader": "<PERSON><PERSON><PERSON><PERSON> ran<PERSON>an", "disableMultiChainHeader": "<PERSON><PERSON> sahaja", "disableMultiChainDetail1Header": "<PERSON><PERSON><PERSON><PERSON> se<PERSON> di Solana", "disableMultiChainDetail1SecondaryText": "<PERSON><PERSON><PERSON>, token dan kolektibel tanpa melihat rantaian lain.", "disableMultiChainDetail2Header": "Ke<PERSON>li ke Berbilang rantaian pada bila-bila masa", "disableMultiChainDetail2SecondaryText": "Baki Ethereum dan Polygon yang anda akan dikekalkan apabila anda membolehkan semula <PERSON> ran<PERSON>.", "disableMultiChainButton": "<PERSON><PERSON><PERSON><PERSON>", "disabledMultiChainHeader": "<PERSON><PERSON>", "disabledMultiChainText": "Anda boleh membolehkan semula berbilang rantaian pada bila-bila masa.", "enableMultiChainHeader": "<PERSON><PERSON>h<PERSON> Berbilang Rantaian", "enabledMultiChainHeader": "Berbilang Rantaian <PERSON>", "enabledMultiChainText": "Kini Ethereum dan Polygon disokong dalam dompet anda.", "incompatibleAccountHeader": "<PERSON><PERSON><PERSON>", "incompatibleAccountInterpolated": "<PERSON><PERSON> singkirkan akaun Ethereum sahaja ini yang membolehkan mod Solana sahaja: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "<PERSON><PERSON>u!", "welcomeToMultiChainPrimaryText": "<PERSON>tu <PERSON> untuk Segala-galanya", "welcomeToMultiChainDetail1Header": "Sokongan Ethereum dan Polygon", "welcomeToMultiChainDetail1SecondaryText": "Semua token dan <PERSON> anda da<PERSON>, Ethereum dan Polygon di satu tempat.", "welcomeToMultiChainDetail2Header": "<PERSON><PERSON><PERSON> semua aplikasi yang anda menyukai", "welcomeToMultiChainDetail2SecondaryText": "Sambungkan kepada aplikasi pada berbilang rantaian tanpa menukar rang<PERSON>an.", "welcomeToMultiChainDetail3Header": "Import dompet MetaMask anda", "welcomeToMultiChainDetail3SecondaryText": "Import secara mudah semua frasa biji anda di seluruh Ethereum dan Polygon.", "welcomeToMultiChainIntro": "Selamat datang ke Phantom Multichain", "welcomeToMultiChainIntroDesc": "Semua token dan <PERSON><PERSON> anda da<PERSON>, Ethereum, dan Polygon berada di satu tempat. <PERSON><PERSON> tunggal anda untuk segala-galanya.", "welcomeToMultiChainAccounts": "<PERSON><PERSON><PERSON> Rantai telah direka bentuk semula", "welcomeToMultiChainAccountsDesc": "<PERSON><PERSON><PERSON> bentuk semula untuk berbilang rantai, set<PERSON><PERSON> akaun sekarang mempunyai alamat ETH dan Polygon yang sepadan.", "welcomeToMultiChainApps": "Berfungsi Di Merata-rata Tempat", "welcomeToMultiChainAppsDesc": "Phantom adalah serasi dengan setiap aplikasi di Ethereum, Polygon, dan <PERSON>. Klik “Sambung ke MetaMask” dan anda sudah siap sedia.", "welcomeToMultiChainImport": "Import daripada MetaMask, dengan serta-merta", "welcomeToMultiChainImportDesc": "Import Frasa Rahsia atau Kunci Per<PERSON>di anda daripada dompet seperti MetaMask atau Coinbase Wallet. Segala-galanya di satu tempat.", "welcomeToMultiChainImportInterpolated": "<0>Import Frasa Rahsia atau</0> <PERSON><PERSON><PERSON> anda daripada dompet seperti MetaMask atau Coinbase Wallet. Segala-galanya di satu tempat.", "welcomeToMultiChainTakeTour": "<PERSON><PERSON><PERSON> lawatan", "welcomeToMultiChainSwapperTitle": "Bertukar di Ethereum,\nPolygon, & Solana", "welcomeToMultiChainSwapperDetail1Header": "Sokongan Ethereum dan Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "<PERSON><PERSON><PERSON> anda boleh menukar token ERC-20 daripada dalam dompet anda.", "welcomeToMultiChainSwapperDetail2Header": "<PERSON>rga Terbaik dan <PERSON> Rendah", "welcomeToMultiChainSwapperDetail2SecondaryText": "100+ sumber kecairan dan penghalaan pesanan pintar untuk pulangan maksimum.", "networkErrorTitle": "<PERSON><PERSON>", "networkError": "Malangnya kita tidak dapat mencapai rang<PERSON>an. Sila cuba lagi kemudian.", "authenticationUnlockPhantom": "Membuka Phantom", "errorAndOfflineSomethingWentWrong": "Sesuatu yang tidak kena telah berlaku", "errorAndOfflineSomethingWentWrongTryAgain": "Sila cuba lagi.", "errorAndOfflineUnableToFetchAssets": "Kita tidak dapat mendapat kembali aset. Sila cuba lagi kemudian.", "errorAndOfflineUnableToFetchCollectibles": "Kita tidak dapat mendapat kolektibel. Sila cuba lagi kemudian.", "errorAndOfflineUnableToFetchSwap": "Kita tidak dapat mendapat maklumat pertukaran. Sila cuba lagi kemudian.", "errorAndOfflineUnableToFetchTransactionHistory": "Kita tidak dapat memperoleh sejarah urus niaga anda sekarang juga. Semak sambungan rangkaian anda atau cuba lagi kemudian.", "errorAndOfflineUnableToFetchRewardsHistory": "Kita tidak dapat mengambil sejarah ganjaran. Sila cuba lagi kemudian.", "errorAndOfflineUnableToFetchBlockedUsers": "Kita tidak dapat mengambil pengguna tersekat. Sila cuba lagi kemudian.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Sesuatu yang tidak kena telah berlaku sambil menyemak semula pesanan anda, sila cuba lagi.", "sendSelectToken": "<PERSON><PERSON><PERSON>", "swapBalance": "Baki:", "swapTitle": "<PERSON><PERSON>", "swapSelectToken": "<PERSON><PERSON><PERSON>", "swapYouPay": "<PERSON><PERSON>", "swapYouReceive": "<PERSON><PERSON>", "aboutPrivacyPolicy": "<PERSON><PERSON>", "aboutVersion": "Versi {{version}}", "aboutVisitWebsite": "<PERSON><PERSON>n <PERSON>", "bottomSheetConnectTitle": "Sambungkan", "A11YbottomSheetConnectTitle": "Sambung Lembaran Bawah", "A11YbottomSheetCommandClose": "<PERSON><PERSON>", "A11YbottomSheetCommandBack": "Belakang Lembaran Bawa<PERSON>", "bottomSheetSignTypedDataTitle": "Tandatangan pesanan", "bottomSheetSignMessageTitle": "Tandatangan pesanan", "bottomSheetSignInTitle": "Log masuk", "bottomSheetSignInAndConnectTitle": "Log masuk", "bottomSheetConfirmTransactionTitle": "<PERSON><PERSON><PERSON> urus niaga", "bottomSheetConfirmTransactionsTitle": "<PERSON><PERSON><PERSON> urus niaga", "bottomSheetSolanaPayTitle": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetAdvancedTitle": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetReadOnlyAccountTitle": "<PERSON><PERSON>", "bottomSheetTransactionSettingsTitle": "<PERSON><PERSON>", "bottomSheetConnectDescription": "Menyambung akan membenarkan laman ini untuk melihat baki dan aktiviti untuk akaun terpilih.", "bottomSheetSignInDescription": "Menandatangani pesanan ini akan membuktikan anda mempunyai pemilikan akaun terpilih. <PERSON><PERSON> tandatangani pesanan daripada aplikasi yang anda percayai.", "bottomSheetSignInAndConnectDescription": "Meluluskan akan membenarkan laman ini untuk melihat baki dan aktiviti untuk akaun terpilih.", "bottomSheetConfirmTransactionDescription": "<PERSON><PERSON><PERSON> baki di<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "bottomSheetConfirmTransactionsDescription": "<PERSON><PERSON><PERSON> baki di<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "bottomSheetSignTypedDataDescription": "Ini hanya permintaan keizinan sahaja. <PERSON><PERSON> niaga mungkin tidak dilaksanakan dengan serta-merta.", "bottomSheetSignTypedDataSecondDescription": "<PERSON><PERSON><PERSON> baki di<PERSON>. <PERSON><PERSON><PERSON> dan aset yang terlibat tidak dijamin.", "bottomSheetSignMessageDescription": "Menandatangani pesanan ini akan membuktikan anda mempunyai pemilikan akaun terpilih. <PERSON><PERSON> tandatangani pesanan daripada aplikasi yang anda percayai.", "bottomSheetReadOnlyAccountDescription": "Tidak dapat menjalankan tindakan ini dalam mod lihat sahaja.", "bottomSheetMessageRow": "<PERSON><PERSON><PERSON>", "bottomSheetStatementRow": "<PERSON><PERSON><PERSON>", "bottomSheetAutoConfirmRow": "Sahkan Secara Automatik", "bottomSheetAutoConfirmOff": "Padamkan", "bottomSheetAutoConfirmOn": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetAccountRow": "<PERSON><PERSON><PERSON>", "bottomSheetAdvancedRow": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetContractRow": "<PERSON><PERSON><PERSON>", "bottomSheetSpenderRow": "<PERSON><PERSON><PERSON>", "bottomSheetNetworkRow": "<PERSON><PERSON><PERSON><PERSON>", "bottomSheetNetworkFeeRow": "<PERSON><PERSON>", "bottomSheetEstimatedTimeRow": "<PERSON><PERSON>", "bottomSheetAccountRowDefaultAccountName": "<PERSON><PERSON><PERSON>", "bottomSheetConnectRequestDisclaimer": "Hanya sambung kepada laman web yang anda percayai", "bottomSheetSignInRequestDisclaimer": "Hanya log masuk kepada laman web yang anda percayai", "bottomSheetSignatureRequestDisclaimer": "<PERSON>ya sahkan jika anda mempercayai laman web ini.", "bottomSheetFeaturedTransactionDisclaimer": "Anda akan dapat pratonton urus niaga sebelum anda mengesahkan langkah seterusnya.", "bottomSheetIgnoreWarning": "<PERSON><PERSON><PERSON><PERSON> amaran, sambung bagaimanapun", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "<PERSON><PERSON><PERSON> per<PERSON>han baki di<PERSON>ui. <PERSON><PERSON> terus dengan waspada dan hanya sahkan jika anda mempercayai laman ini.", "bottomSheetReadOnlyWarning": "Anda hanya memperhatikan alamat ini. Anda akan perlu mengimport rangkai kata rahsia untuk menandatangani urus niaga dan mesej.", "bottomSheetWebsiteIsUnsafeWarning": "<PERSON><PERSON> web ini tidak selamat dan mungkin cuba mencuri dana anda.", "bottomSheetViewOnExplorer": "Lihat pada Explorer", "bottomSheetTransactionSubmitted": "<PERSON><PERSON> Di<PERSON>ah<PERSON>", "bottomSheetTransactionPending": "<PERSON><PERSON> Belum Selesai", "bottomSheetTransactionFailed": "<PERSON><PERSON> Niaga Telah Gagal", "bottomSheetTransactionSubmittedDescription": "<PERSON><PERSON> niaga anda telah diserah<PERSON>. <PERSON>a boleh melihatnya pada explorer.", "bottomSheetTransactionFailedDescription": "<PERSON><PERSON> niaga anda telah gagal. Sila cuba lagi.", "bottomSheetTransactionPendingDescription": "<PERSON>rus niaga sedang diproses...", "transactionsFromInterpolated": "Daripada: {{from}}", "transactionsFromParagraphInterpolated": "Dar<PERSON><PERSON> {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON> ini", "transactionsToInterpolated": "Kepada: {{to}}", "transactionsToParagraphInterpolated": "<PERSON><PERSON><PERSON> {{to}}", "transactionsYesterday": "Semalam", "addEditAddressAdd": "<PERSON><PERSON> alamat", "addEditAddressDelete": "<PERSON><PERSON><PERSON>", "addEditAddressDeleteTitle": "<PERSON><PERSON>h anda pasti anda mahu memadamkan alamat ini?", "addEditAddressSave": "<PERSON><PERSON><PERSON>", "dAppBrowserComingSoon": "Penyemak imbas dApp akan tiba tidak lama lagi!", "dAppBrowserSearchPlaceholder": "Tapak, token, URL", "dAppBrowserOpenInNewTab": "<PERSON><PERSON> dalam tab baharu", "dAppBrowserSuggested": "Disyorkan", "dAppBrowserFavorites": "Kegemaran", "dAppBrowserBookmarks": "<PERSON>da buku", "dAppBrowserBookmarkAdd": "Tambah Tanda Buku", "dAppBrowserBookmarkRemove": "Singkirkan Tanda Buku", "dAppBrowserUsers": "Pengguna", "dAppBrowserRecents": "Terbaru", "dAppBrowserFavoritesDescription": "<PERSON><PERSON><PERSON><PERSON> anda akan ditun<PERSON>kkan di sini", "dAppBrowserBookmarksDescription": "<PERSON>da buku anda akan ditunjukkan di sini", "dAppBrowserRecentsDescription": "dapps yang baru-baru ini disambung akan muncul di sini", "dAppBrowserEmptyScreenDescription": "Taip URL atau cari di web", "dAppBrowserBlocklistScreenTitle": "{{origin}} disekat! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom percaya laman web ini berniat jahat dan tidak selamat untuk digunakan.", "part2": "<PERSON>n ini telah ditandakan sebagai sebahagian daripada pangkalan data dikekalkan oleh komuniti laman web memancing data dan penipuan yang diketahui. <PERSON><PERSON> anda percaya laman tersebut telah silap ditandakan, <>sila kemukakan isu."}, "dAppBrowserLoadFailedScreenTitle": "Gagal untuk memuat", "dAppBrowserLoadFailedScreenDescription": "Terdapat ralat memuat halaman ini", "dAppBrowserBlocklistScreenIgnoreButton": "<PERSON><PERSON><PERSON><PERSON> amaran, tunju<PERSON>n bag<PERSON>n", "dAppBrowserActionBookmark": "<PERSON>da buku", "dAppBrowserActionRemoveBookmark": "Singkirkan tanda buku", "dAppBrowserActionRefresh": "<PERSON><PERSON> semula", "dAppBrowserActionShare": "Kong<PERSON>", "dAppBrowserActionCloseTab": "Tutup tab", "dAppBrowserActionEndAutoConfirm": "Tamatkan Sahkan Secara Automatik", "dAppBrowserActionDisconnectApp": "Putus sambungan aplikasi", "dAppBrowserActionCloseAllTabs": "Tutup semua tab", "dAppBrowserNavigationAddressPlaceholder": "Taip URL untuk mencari", "dAppBrowserTabOverviewMore": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddTab": "Tambah Tab", "dAppBrowserTabOverviewClose": "<PERSON><PERSON><PERSON>", "dAppBrowserCloseTab": "<PERSON><PERSON><PERSON>", "dAppBrowserClose": "<PERSON><PERSON><PERSON>", "dAppBrowserTabOverviewAddBookmark": "Tambah Tanda Buku", "dAppBrowserTabOverviewRemoveBookmark": "Singkirkan Tanda Buku", "depositAssetListSuggestions": "Cadangan", "depositUndefinedToken": "Ma<PERSON>, tidak dapat mendepositkan token ini", "onboardingImportRecoveryPhraseDetails": "<PERSON><PERSON><PERSON>", "onboardingCreateRecoveryPhraseVerifyTitle": "Rangkai Kata Pemulihan Rahsia dicatatkan?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Tanpa rangkai kata pemulihan rahsia anda tidak akan dapat mencapai kunci anda atau mana-mana aset berkaitan dengannya.", "onboardingCreateRecoveryPhraseVerifyYes": "Ya", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON>a telah gagal menjana akaun, sila cuba lagi.", "onboardingDoneDescription": "<PERSON><PERSON><PERSON> anda boleh menikmati dompet anda sepen<PERSON>.", "onboardingDoneGetStarted": "Sebelum Be<PERSON>ula", "zeroBalanceHeading": "<PERSON><PERSON> kita bermula!", "zeroBalanceBuyCryptoTitle": "<PERSON><PERSON>", "zeroBalanceBuyCryptoDescription": "Beli kripto pertama anda dengan kad debit atau kredit.", "zeroBalanceDepositTitle": "Pindahkan Krip<PERSON>", "zeroBalanceDepositDescription": "Depositkan kripto daripada dompet atau pertukaran lain.", "onboardingImportAccountsEmptyResult": "<PERSON><PERSON><PERSON> akaun di<PERSON>ui", "onboardingImportAccountsAccountName": "Akaun {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON>", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "<PERSON><PERSON> {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "<PERSON>a mendapati akaun {{numberOfWallets}} dengan aktiviti", "onboardingImportAccountsFoundAccounts_other": "<PERSON>a mendapati akaun {{numberOfWallets}} dengan aktiviti", "onboardingImportAccountsFoundAccountsNoActivity_one": "<PERSON><PERSON> mendapati akaun {{numberOfWallets}}", "onboardingImportAccountsFoundAccountsNoActivity_other": "<PERSON><PERSON> mendapati akaun {{numberOfWallets}}", "onboardingImportRecoveryPhraseLessThanTwelve": "Rangkai kata hendaklah sekurang-kurangnya 12 perkataan.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Rangkai kata hendaklah betul-betul 12 atau 24 perkataan.", "onboardingImportRecoveryPhraseWrongWord": "<PERSON>kataan yang salah: {{ words }}.", "onboardingProtectTitle": "Lindungi dompet anda", "onboardingProtectDescription": "Menambah keselamatan biometrik akan memastikan bahawa anda satu-satunya yang dapat mencapai dompet anda.", "onboardingProtectButtonHeadlineDevice": "Per<PERSON>", "onboardingProtectButtonHeadlineFaceID": "ID Wajah", "onboardingProtectButtonHeadlineFingerprint": "Cap jari", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "<PERSON><PERSON><PERSON> {{ authType }}", "onboardingProtectError": "Sesuatu yang tidak kena telah berlaku sambil menges<PERSON>, sila cuba lagi", "onboardingProtectBiometryIosError": "Pengesahan biometrik dikonfigurasikan di Phantom tetapi dilumpuhkan dalam Tetapan Sistem. Sila buka Tetapan > Phantom > ID Wajah atau ID Sentuh untuk membolehkan semula.", "onboardingProtectRemoveAuth": "Lumpuhkan pengesahan", "onboardingProtectRemoveAuthDescription": "<PERSON><PERSON>h anda pasti anda mahu melumpuhkan pengesahan?", "onboardingWelcomeTitle": "Selamat datang ke Phantom", "onboardingWelcomeDescription": "Sebelum bermula, cipta dompet baharu atau import yang ada.", "onboardingWelcomeCreateWallet": "<PERSON><PERSON><PERSON> dompet baharu", "onboardingWelcomeAlreadyHaveWallet": "<PERSON>a sudah ada dompet", "onboardingWelcomeConnectSeedVault": "Sambungkan Kekubah Selamat Seed", "onboardingSlide1Title": "<PERSON><PERSON><PERSON> o<PERSON>h anda", "onboardingSlide1Description": "<PERSON><PERSON> anda dikunci dengan akses biometrik, pengesanan penipuan dan sokongan 24/7.", "onboardingSlide2Title": "Tempat terbaik untuk\nNFT anda", "onboardingSlide2Description": "Men<PERSON><PERSON><PERSON> penyenaraian, spam terbakar dan terus ikuti perkembangan pemberitahuan tolak yang berguna.", "onboardingSlide3Title": "Dapatkan lebih manfaat dengan token anda", "onboardingSlide3Description": "<PERSON><PERSON><PERSON>, tukar, taruh, hantar dan terima — tanpa perlu meninggalkan dompet anda. ", "onboardingSlide4Title": "Te<PERSON>i yang terbaik daripada Web3", "onboardingSlide4Description": "Cari dan menyambung kepada aplikasi terkemuka dan koleksi dengan penyemak imbas dalam aplikasi.", "onboardingMultichainSlide5Title": "Satu dompet untuk segala-galanya", "onboardingMultichainSlide5Description": "<PERSON><PERSON><PERSON>, Ethereum dan Polygon dalam antara muka ramah pengguna tunggal.", "onboardingMultichainSlide5DescriptionWithBitcoin": "<PERSON><PERSON><PERSON> se<PERSON>, Ethereum, Polygon dan Bitcoin dalam antara muka ramah pengguna tunggal.", "requireAuth": "<PERSON><PERSON><PERSON><PERSON> pen<PERSON>", "requireAuthImmediately": "<PERSON><PERSON> segera", "availableToSend": "Tersedia Untuk Dihantar", "sendEnterAmount": "<PERSON><PERSON><PERSON><PERSON>", "sendEditMemo": "Sunting Memo", "sendShowLogs": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sendHideLogs": "Sembunyikan Log Ralat", "sendGoBack": "<PERSON><PERSON><PERSON>", "sendTransactionSuccess": "Token anda berjaya di<PERSON>tar kepada", "sendInputPlaceholder": "@nama pengguna atau alamat", "sendInputPlaceholderV2": "nama pengguna atau alamat", "sendPeopleTitle": "Orang", "sendDomainTitle": "Domain", "sendFollowing": "Mengikut", "sendRecentlyUsedAddressLabel": "Digunakan {{formattedTimestamp}} lalu", "sendRecipientAddress": "<PERSON><PERSON><PERSON>", "sendTokenInterpolated": "Hantar {{tokenSymbol}}", "sendPasteFromClipboard": "<PERSON>l daripada papan klip", "sendScanQR": "<PERSON><PERSON><PERSON>", "sendTo": "Kepada:", "sendRecipientZeroBalanceWarning": "<PERSON><PERSON>t dompet ini tidak ada baki dan tidak muncul dalam sejarah urus niaga yang terbaru anda. <PERSON>la pastikan alamat adalah betul.", "sendUnknownAddressWarning": "Ini bukan alamat yang anda berinteraksi baru-baru ini. <PERSON>la teruskan dengan berwaspada.", "sendSameAddressWarning": "<PERSON>i ialah alamat semasa anda. Menghantar akan menanggung yuran pindahan dengan tiada perubahan baki yang lain.", "sendMintAccountWarning": "Ini ialah alamat akaun kilang syiling. Anda tidak dapat menghantar dana ke alamat ini kerana ia akan mengakibatkan kerugian tetap.", "sendCameraAccess": "<PERSON><PERSON><PERSON>", "sendCameraAccessSubtitle": "Untuk imbas kod QR, aks<PERSON> kamera perlu dibolehkan. <PERSON><PERSON><PERSON> anda mahu membuka <PERSON> sekarang?", "sendSettings": "Tetapan", "sendOK": "OK", "invalidQRCode": "Kod QR ini tidak sah.", "sendInvalidQRCode": "Kod QR ini bukan alamat yang sah", "sendInvalidQRCodeSubtitle": "Cuba lagi atau dengan kod QR lain.", "sendInvalidQRCodeSplToken": "Token dalam kod QR yang tidak sah", "sendInvalidQRCodeSplTokenSubtitle": "Kod QR ini mengandungi token yang anda tidak miliki atau kita tidak dapat mengenal pastinya.", "sendScanAddressToSend": "<PERSON><PERSON><PERSON> al<PERSON> {{tokenSymbol}} untuk mengirimkan dana", "sendScanAddressToSendNoSymbol": "<PERSON><PERSON><PERSON> alamat untuk men<PERSON>kan dana", "sendScanAddressToSendCollectible": "<PERSON><PERSON>s alamat SOL untuk menghantar kolektibel", "sendScanAddressToSendCollectibleMultichain": "<PERSON><PERSON><PERSON> alamat untuk menghantar kolektibel", "sendSummary": "<PERSON><PERSON><PERSON>", "sendUndefinedToken": "<PERSON><PERSON>, tidak dapat menghantar token ini", "sendNoTokens": "Tiada token tersedia", "noBuyOptionsAvailableInCountry": "<PERSON><PERSON>da pilihan Tidak Beli tersedia di negara anda", "swapAvailableTokenDisclaimer": "Ju<PERSON><PERSON> token yang terhad adalah tersedia untuk penyambungan antara <PERSON>an", "swapCrossSwapNetworkTooltipTitle": "<PERSON><PERSON><PERSON><PERSON>", "swapCrossSwapNetworkTooltipDescription": "<PERSON><PERSON><PERSON> se<PERSON><PERSON>h <PERSON>an disyorkan untuk menggunakan token yang tersedia ada untuk harga terendah dan urus niaga yang paling cepat.", "settingsAbout": "Tentang Phantom", "settingsShareAppWithFriends": "<PERSON><PERSON><PERSON> kawan anda", "settingsConfirm": "Ya", "settingsMakeSureNoOneIsWatching": "Pastikan tiada sesiapa yang memperhatikan skrin anda", "settingsManageAccounts": "<PERSON><PERSON><PERSON>", "settingsPrompt": "<PERSON><PERSON>h anda pasti anda mahu men<PERSON>?", "settingsSelectAvatar": "<PERSON><PERSON><PERSON>", "settingsSelectSecretPhrase": "<PERSON><PERSON><PERSON> Ra<PERSON>", "settingsShowPrivateKey": "Ketik untuk mendedahkan kunci peribadi anda", "settingsShowRecoveryPhrase": "Ketik untuk mendedahkan rangkai kata rahsia anda", "settingsSubmitBetaFeedback": "<PERSON><PERSON><PERSON>", "settingsUpdateAccountNameToast": "<PERSON><PERSON> akaun dikemas kini", "settingsUpdateAvatarToast": "Avatar dikemas kini", "settingsUpdateAvatarToastFailure": "Gagal mengemas kini Avatar!", "settingsWalletAddress": "<PERSON><PERSON><PERSON>", "settingsWalletAddresses": "<PERSON><PERSON><PERSON>", "settingsWalletNamePrimary": "<PERSON><PERSON>", "settingsPlaceholderName": "<PERSON><PERSON>", "settingsWalletNameSecondary": "Ubah nama dompet anda", "settingsYourAccounts": "<PERSON><PERSON><PERSON>", "settingsYourAccountsMultiChain": "<PERSON><PERSON><PERSON><PERSON> rantai", "settingsReportUser": "Laporkan Pengguna", "settingsNotifications": "Pemberitahuan", "settingsNotificationPreferences": "Keutamaan Pemberitahuan", "pushNotificationsPreferencesAllowNotifications": "Benarkan <PERSON>", "pushNotificationsPreferencesSentTokens": "<PERSON><PERSON>", "pushNotificationsPreferencesSentTokensDescription": "<PERSON><PERSON>han keluar token dan N<PERSON>", "pushNotificationsPreferencesReceivedTokens": "Token <PERSON>", "pushNotificationsPreferencesReceivedTokensDescription": "<PERSON><PERSON><PERSON> masuk token dan N<PERSON>", "pushNotificationsPreferencesDexSwap": "<PERSON><PERSON><PERSON><PERSON>", "pushNotificationsPreferencesDexSwapDescription": "Pertukaran pada aplikasi dikenali", "pushNotificationsPreferencesOtherBalanceChanges": "<PERSON><PERSON><PERSON>", "pushNotificationsPreferencesOtherBalanceChangesDescription": "<PERSON><PERSON> niaga berbilang token yang memberi kesan kepada baki anda", "pushNotificationsPreferencesPhantomMarketing": "<PERSON><PERSON>", "pushNotificationsPreferencesPhantomMarketingDescription": "Memaparkan pengumuman dan kemas kini am", "pushNotificationsPreferencesDescription": "Tetapan ini mengawal pemberitahuan tolak untuk dompet aktif ini. Setiap dompet mempunyai tetapan pemberitahuan sendiri. Untuk memadamkan semua pemberitahuan tolak Phantom, pergi ke <1>tetapan peranti</1> anda.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Tidak dapat menyegerakkan keutamaan pemberitahuan.", "connectSeedVaultConnectSeed": "Sambungkan Seed", "connectSeedVaultConnectSeedDescription": "Sambungkan Phantom kepada Seed Vault pada telefon anda", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeed": "<PERSON><PERSON><PERSON>", "connectSeedVaultSelectASeedDescription": "Pilih seed yang anda ingin menyambung kepada Phantom", "connectSeedVaultSelectAnAccountDescription": "<PERSON><PERSON><PERSON> akaun yang anda ingin sediakan dengan <PERSON>", "connectSeedVaultNoAccountsFound": "<PERSON><PERSON><PERSON> akaun di<PERSON>.", "connectSeedVaultSelectAccounts": "<PERSON><PERSON><PERSON>", "connectSeedVaultSelectAccountsDescription": "<PERSON><PERSON><PERSON> akaun yang anda ingin sediakan dengan <PERSON>", "connectSeedVaultCompleteSetup": "Lengkap<PERSON> persed<PERSON>", "connectSeedVaultCompleteSetupDescription": "Sudah siap! Terokai web3 dengan Phantom dan gunakan Seed <PERSON> anda untuk mengesahkan urus niaga", "connectSeedVaultConnectAnotherSeed": "Sambungkan Seed lain", "connectSeedVaultConnectAllSeedsConnected": "Semua seed disambung", "connectSeedVaultNoSeedsConnected": "Tiada seed disambungkan. <PERSON><PERSON>k butang di bawah untuk memberikan kebenaran daripada Seed Vault.", "connectSeedVaultConnectAccount": "<PERSON><PERSON><PERSON><PERSON> akaun", "connectSeedVaultLoadMore": "<PERSON><PERSON>", "connectSeedVaultNeedPermission": "<PERSON><PERSON><PERSON> k<PERSON>an", "connectSeedVaultNeedPermissionDescription": "Pergi ke Tetapan untuk membenarkan Phantom menggunakan keizinan Seed Vault.", "stakeApy": "APY {{apyPercentage}}", "stakeFee": "yuran {{fee}}", "stakeAmount": "<PERSON><PERSON><PERSON>", "stakeAmountBalance": "Baki", "swapTopQuotes": "<PERSON><PERSON> {{numQuotes}} <PERSON><PERSON><PERSON>", "swapTopQuotesTitle": "<PERSON><PERSON>", "swapProvidersTitle": "Penyedia", "swapProvidersFee": "yuran {{fee}}", "swapProvidersTagRecommended": "Pemulangan Terbaik", "swapProvidersTagFastest": "<PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}}j {{minutes}}m", "swapProviderEstimatedTimeM": "{{minutes}}m", "swapProviderEstimatedTimeS": "{{seconds}}s", "stakeReview": "<PERSON><PERSON>", "stakeReviewAccount": "<PERSON><PERSON><PERSON>", "stakeReviewCommissionFee": "<PERSON><PERSON>", "stakeReviewConfirm": "<PERSON><PERSON><PERSON>", "stakeReviewValidator": "<PERSON><PERSON><PERSON>", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Pertukaran Stake Telah Gagal", "convertStakeStatusErrorMessage": "<PERSON>ake anda tidak boleh ditukar kepada {{poolTokenSymbol}}. Sila cuba lagi.", "convertStakeStatusLoadingTitle": "Menukar kepada {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "<PERSON><PERSON> sedang memulakan proses untuk menukar {{stakedTokenSymbol}} yang dipertaruhkan anda kepada {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Pertukaran kepada {{poolTokenSymbol}} selesai!", "convertStakeStatusSuccessMessage": "<PERSON><PERSON><PERSON> ganjaran tambahan dengan JitoSOL anda<1>di sini.</1>", "convertStakeStatusConvertMore": "<PERSON><PERSON>", "convertStakePendingTitle": "<PERSON><PERSON><PERSON> stake kepada {{symbol}}", "convertToJitoSOL": "Tukar kepada JitoSOL", "convertToJitoSOLInfoDescription": "Tukar SOL anda kepada Jito SOL untuk memperoleh ganjaran dan menyertai ekosistem Ji<PERSON>.", "convertToJitoSOLInfoTitle": "Tukar kepada JitoSOL", "convertStakeBannerTitle": "Tukar stake anda kepada JitoSOL untuk menambahkan ganjaran sehingga 15%", "convertStakeQuestBannerTitle": "Tukar SOL distake kepada JitoSOL dan peroleh ganjaran. <PERSON><PERSON><PERSON> lebih lan<PERSON>.", "liquidStakeConvertInfoTitle": "Tukar kepada JitoSOL", "liquidStakeConvertInfoDescription": "Tingkatkan ganjaran anda dengan <PERSON>kar stake SOL anda kepada JitoSOL. <1><PERSON><PERSON><PERSON> lebih lan<PERSON>t</1>", "liquidStakeConvertInfoFeature1Title": "Mengapa stake dengan Jito?", "liquidStakeConvertInfoFeature1Description": "Depositkan untuk mendapatkan JitoSOL, yang meningkat dengan stake anda. Gunakannya dalam protokol DeFi untuk pendapatan tambahan. Tukar JitoSOL anda kemudian untuk jumlah asal + ganjaran terakru", "liquidStakeConvertInfoFeature2Title": "<PERSON><PERSON><PERSON> ganjaran yang lebih tinggi", "liquidStakeConvertInfoFeature2Description": "Jito menyebarkan SOL anda antara pengesah terbaik dengan yuran yang terendah. Ganjaran MEV meningkatkan pendapatan anda selanjutnya.", "liquidStakeConvertInfoFeature3Title": "Sokon<PERSON>", "liquidStakeConvertInfoFeature3Description": "Staking cair menjamin <PERSON> dengan menyebarkan stake di seluruh berbilang pengesah, mengurangkan risiko daripada pengesah berkendali yang rendah.", "liquidStakeConvertInfoSecondaryButton": "<PERSON><PERSON><PERSON>", "liquidStakeStartStaking": "<PERSON><PERSON>aking", "liquidStakeReviewOrder": "<PERSON><PERSON>", "convertStakeAccountListPageIneligibleSectionTitle": "<PERSON><PERSON><PERSON>", "convertStakeAccountIneligibleBottomSheetTitle": "<PERSON><PERSON><PERSON>", "convertStakeAccountListPageErrorTitle": "Gagal mengambil akaun stake", "convertStakeAccountListPageErrorDescription": "<PERSON><PERSON>, sesuatu yang tidak kena telah berlaku dan kita tidak dapat mengambil akaun stake", "liquidStakeReviewYouPay": "<PERSON><PERSON>", "liquidStakeReviewYouReceive": "<PERSON><PERSON>", "liquidStakeReviewProvider": "Penyedia", "liquidStakeReviewNetworkFee": "<PERSON><PERSON>", "liquidStakeReviewPageTitle": "<PERSON><PERSON><PERSON>", "liquidStakeReviewConversionFootnote": "<PERSON><PERSON><PERSON><PERSON> anda stake token <PERSON>ana sebagai tukaran JitoSOL anda akan menerima jumlah yang lebih sedikit daripada JitoSOL. <1><PERSON><PERSON><PERSON> lebih lanjut</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Kumpulan stake Jito menyokong kebanyakan pengesah Solana yang paling aktif. Anda tidak akan dapat menukar SOL yang distake daripada pengesah yang tidak disokong kepada JitoSOL. Di samping itu, SOL yang baru distake mengambil ~2 hari sebelum layak untuk pertukaran JitoSOL.", "selectAValidator": "<PERSON><PERSON><PERSON>", "validatorSelectionListTitle": "<PERSON><PERSON><PERSON>", "validatorSelectionListDescription": "<PERSON><PERSON>h pengesah untuk meletakkan stake SOL anda bersama.", "stakeMethodDescription": "<PERSON><PERSON>h faedah dengan menggunakan token SOL anda untuk membantu skala Solana. <1><PERSON><PERSON><PERSON> lebih lanjut</1>", "stakeMethodRecommended": "Disyorkan", "stakeMethodEstApy": "Anggaran APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Staking Cair", "stakeMethodSelectionLiquidStakingDescription": "Stakekan SOL untuk memperoleh ganjaran yang lebih tinggi, membantu men<PERSON><PERSON> & menerima JitoSOL untuk memperoleh ganjaran tambahan.", "stakeMethodSelectionNativeStakingTitle": "Staking Asli", "stakeMethodSelectionNativeStakingDescription": "Stakekan SOL untuk menerima ganjaran sambil membantu menja<PERSON>.", "liquidStakeMintStakeSOL": "SOL Stake", "mintJitoSOLInfoPageTitle": "Memperkenalkan Staking Cair dengan <PERSON>", "mintJitoSOLFeature1Title": "Mengapa stake dengan Jito?", "mintJitoSOLFeature1Description": "Depositkan untuk mendapatkan JitoSOL, yang meningkat dengan stake anda. Gunakannya dalam protokol DeFi untuk pendapatan tambahan. Tukar JitoSOL anda kemudian untuk jumlah asal + ganjaran terakru", "mintJitoSOLFeature2Title": "<PERSON><PERSON><PERSON> ganjaran yang lebih tinggi", "mintJitoSOLFeature2Description": "Jito menyebarkan SOL anda antara pengesah terbaik dengan yuran yang terendah. Ganjaran MEV meningkatkan pendapatan anda selanjutnya.", "mintJitoSOLFeature3Title": "Sokon<PERSON>", "mintJitoSOLFeature3Description": "Staking cair menjamin <PERSON> dengan menyebarkan stake di seluruh berbilang pengesah, mengurangkan risiko daripada pengesah berkendali yang rendah.", "mintLiquidStakePendingTitle": "Pencetakan stake cair", "mintStakeStatusErrorTitle": "Pencetakan Stake Cair Gagal", "mintStakeStatusErrorMessage": "Stake cair {{poolTokenSymbol}} anda tidak boleh dicetak. Sila cuba lagi.", "mintStakeStatusSuccessTitle": "Pencetakan stake cair {{poolTokenSymbol}} selesai!", "mintStakeStatusLoadingTitle": "Pencetakan stake cair {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "<PERSON><PERSON> sedang memulakan proses untuk mencetak stake cair {{poolTokenSymbol}} anda.", "mintLiquidStakeAmountDescription": "<PERSON><PERSON><PERSON> berapa banyak SOL yang anda ingin stake dengan <PERSON>to", "mintLiquidStakeAmountProvider": "Penyedia", "mintLiquidStakeAmountApy": "APY Anggaran", "mintLiquidStakeAmountBestPrice": "<PERSON><PERSON>", "mintLiquidStakeAmountInsufficientBalance": "Baki yang tidak mencukupi", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} diperlukan untuk stake", "swapTooltipGotIt": "<PERSON><PERSON><PERSON>", "swapTabInsufficientFunds": "<PERSON> yang tidak men<PERSON>", "swapNoAssetsFound": "Tiada Aset", "swapNoTokensFound": "Tiada token ditemui", "swapConfirmationTryAgain": "Cuba lagi", "swapConfirmationGoBack": "<PERSON><PERSON><PERSON>", "swapNoQuotesFound": "Tiada sebut harga ditemui", "swapNotProviderFound": "<PERSON>mi tidak dapat mencari penyedia untuk pertukaran token ini. Cuba token yang lain.", "swapAvailableOnMainnet": "Ciri ini hanya tersedia di Mainnet", "swapNotAvailableEVM": "<PERSON><PERSON><PERSON><PERSON> masih belum tersedia untuk akaun EVM", "swapNotAvailableOnSelectedNetwork": "Pertukaran tidak tersedia pada rangkaian terpilih", "singleChainSwapTab": "<PERSON><PERSON>", "crossChainSwapTab": "<PERSON><PERSON><PERSON><PERSON>", "allFilter": "<PERSON><PERSON><PERSON>", "bridgeRefuelTitle": "<PERSON><PERSON><PERSON> semula", "bridgeRefuelDescription": "<PERSON><PERSON><PERSON> semula memastikan anda membayar urus niaga selepas anda merentangi.", "bridgeRefuelLabelBalance": "{{symbol}} anda", "bridgeRefuelLabelReceive": "<PERSON><PERSON>", "bridgeRefuelLabelFee": "<PERSON><PERSON>", "bridgeRefuelDismiss": "Teruskan tanpa <PERSON>", "bridgeRefuelEnable": "Bolehkan Men<PERSON>", "unwrapWrappedSolError": "Unwrap telah gagal", "unwrapWrappedSolLoading": "Unwrap...", "unwrapWrappedSolSuccess": "<PERSON>w<PERSON> <PERSON><PERSON><PERSON>a", "unwrapWrappedSolViewTransaction": "<PERSON><PERSON>", "dappApprovePopupSignMessage": "Tandatangan <PERSON>", "solanaPayFrom": "Daripada", "solanaPayMessage": "<PERSON><PERSON><PERSON>", "solanaPayNetworkFee": "<PERSON><PERSON>", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "Bayar {{item}}", "solanaPayPayNow": "<PERSON><PERSON>", "solanaPaySending": "Menghantar {{item}}", "solanaPayReceiving": "<PERSON><PERSON><PERSON> {{item}}", "solanaPayMinting": "Pencetakan {{item}}", "solanaPayTransactionProcessing": "<PERSON><PERSON> niaga anda sedang memproses,\nsila tunggu.", "solanaPaySent": "Dihantar!", "solanaPayReceived": "<PERSON>terima!", "solanaPayMinted": "Dicetak!", "solanaPaySentNFT": "NFT Dihantar!", "solanaPayReceivedNFT": "NFT Diterima!", "solanaPayTokensSent": "Token anda berjaya dihantar kepada {{to}}", "solanaPayTokensReceived": "<PERSON>a men<PERSON> token baharu daripada {{from}}", "solanaPayViewTransaction": "<PERSON><PERSON> urus niaga", "solanaPayTransactionFailed": "<PERSON><PERSON> Niaga Telah Gagal", "solanaPayConfirm": "<PERSON><PERSON><PERSON>", "solanaPayTo": "kepada", "dappApproveConnectViewAccount": "<PERSON><PERSON> anda", "deepLinkInvalidLink": "Pautan tidak sah", "deepLinkInvalidSplTokenSubtitle": "Ini mengandungi token yang anda tidak miliki atau kita tidak dapat mengenal pastinya.", "walletAvatarShowAllAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON> semua akaun", "pushNotificationsGetInstantUpdates": "Dapatkan kemas kini segera", "pushNotificationsEnablePushNotifications": "Bolehkan pemberitahuan tolak mengenai pindahan yang telah dilengkapkan, pertukaran dan pengumuman", "pushNotificationsEnable": "<PERSON>leh<PERSON>", "pushNotificationsNotNow": "<PERSON><PERSON><PERSON>", "onboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> <1><PERSON><PERSON></1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, saya telah menyimpannya suatu tempat", "onboardingCreateNewWallet": "Cipta <PERSON>", "onboardingErrorDuplicateSecretRecoveryPhrase": "Rangkai kata rahsia ini sudah ada dalam dompet anda", "onboardingErrorInvalidSecretRecoveryPhrase": "Rangkai kata pemulihan rahsia yang tidak sah", "onboardingFinished": "Anda sudah selesai!", "onboardingImportAccounts": "I<PERSON>rt A<PERSON>", "onboardingImportImportingAccounts": "Mengimport Akaun...", "onboardingImportImportingFindingAccounts": "<PERSON><PERSON><PERSON> akaun dengan aktiviti", "onboardingImportAccountsLastActive": "Aktif {{formattedTimestamp}} lalu", "onboardingImportAccountsNeverUsed": "<PERSON><PERSON>", "onboardingImportAccountsCreateNew": "<PERSON><PERSON> baharu", "onboardingImportAccountsDescription": "<PERSON><PERSON><PERSON> akaun dompet untuk mengimport", "onboardingImportReadOnlyAccountDescription": "<PERSON><PERSON> alamat atau nama domain yang anda ingin memperhatikan. Anda hanya ada capaian lihat sahaja, tidak dapat menandatangani urus niaga atau mesej.", "onboardingImportSecretRecoveryPhrase": "Import Rangkai Kata Rahsia", "onboardingImportViewAccounts": "<PERSON><PERSON>", "onboardingRestoreExistingWallet": "Memulihkan dompet yang ada dengan rangkai kata pemulihan rahsia 12 atau 24 perkataan anda", "onboardingShowUnusedAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON> Digun<PERSON>", "onboardingShowMoreAccounts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "onboardingHideUnusedAccounts": "Sembunyikan Akaun T<PERSON> Digunakan", "onboardingSecretRecoveryPhrase": "Rangkai Kata Pemulihan <PERSON>", "onboardingSelectAccounts": "<PERSON><PERSON><PERSON>", "onboardingStoreSecretRecoveryPhraseReminder": "Inilah satu-satunya cara anda akan dapat memulihkan akaun anda. Sila simpannya di suatu tempat yang selamat!", "useTokenMetasForMintsUnknownName": "Tidak diketahui", "timeUnitMinute": "minit", "timeUnitMinutes": "minit", "timeUnitHour": "jam", "timeUnitHours": "jam", "espNFTListWithPrice": "<PERSON><PERSON> {{NFTDisplayName}} untuk {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTListWithPriceWithoutDApp": "<PERSON><PERSON> {{NFTDisplayName}} untuk {{priceAmount}} {{priceTokenSymbol}}", "espNFTListWithoutPrice": "<PERSON><PERSON> {{NFTDisplayName}} untuk dijual di {{dAppName}}", "espNFTListWithoutPriceWithoutDApp": "<PERSON><PERSON> {{NFTDisplayName}} untuk dijual", "espNFTChangeListPriceWithPrice": "<PERSON><PERSON> mengemas kini penyenaraian untuk {{NFTDisplayName}} ke {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTChangeListPriceWithPriceWithoutDApp": "Anda mengemas kini penyenaraian untuk {{NFTDisplayName}} ke {{priceAmount}} {{priceTokenSymbol}}", "espNFTChangeListPriceWithoutPrice": "<PERSON>a mengemas kini penyenaraian untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTChangeListPriceWithoutPriceWithoutDApp": "<PERSON>a mengemas kini penyenaraian untuk {{NFTDisplayName}}", "espNFTBidBidderWithPrice": "Anda membida {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Anda membida {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Anda membuat bida untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidBidderWithoutPriceWithoutDApp": "Anda membuat bida untuk {{NFTDisplayName}}", "espNFTBidListerWithPrice": "Bida baharu sebanyak {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "Bida baharu sebanyak {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "Bida baharu untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "Bida baharu untuk {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Anda telah membatalkan bida anda sebanyak {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}} di {{dAppName}}", "espNFTCancelBidWithPriceWithoutDApp": "Anda telah membatalkan bida anda sebanyak {{priceAmount}} {{priceTokenSymbol}} untuk {{NFTDisplayName}}", "espNFTCancelBidWithoutPrice": "Anda telah membatalkan bida anda sebanyak {{NFTDisplayName}} di {{dAppName}}", "espNFTCancelBidWithoutPriceWithoutDApp": "Anda telah membatalkan bida anda untuk {{NFTDisplayName}}", "espNFTUnlist": "Anda tidak tersenarai {{NFTDisplayName}} di {{dAppName}}", "espNFTUnlistWithoutDApp": "<PERSON><PERSON> {{NFTDisplayName}}", "espNFTBuyBuyerWithPrice": "And<PERSON> membeli {{NFTDisplayName}} sebanyak {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTBuyBuyerWithPriceWithoutDApp": "<PERSON><PERSON> membeli {{NFTDisplayName}} sebanyak {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuyBuyerWithoutPrice": "<PERSON><PERSON> membeli {{NFTDisplayName}} di {{dAppName}}", "espNFTBuyBuyerWithoutPriceWithoutDApp": "<PERSON><PERSON> me<PERSON> {{NFTDisplayName}}", "espNFTBuySellerWithPrice": "Anda menjual {{NFTDisplayName}} sebanyak {{priceAmount}} {{priceTokenSymbol}} di {{dAppName}}", "espNFTBuySellerWithPriceWithoutDApp": "<PERSON><PERSON> menjual {{NFTDisplayName}} sebanyak {{priceAmount}} {{priceTokenSymbol}}", "espNFTBuySellerWithoutPrice": "Anda menjual {{NFTDisplayName}} di {{dAppName}}", "espNFTBuySellerWithoutPriceWithoutDApp": "<PERSON><PERSON> men<PERSON> {{NFTDisplayName}}", "espDEXSwap": "<PERSON><PERSON> <PERSON>kar {{downTokensTextFragment}} untuk {{upTokensTextFragment}} di {{dAppName}}", "espDEXDepositLPWithPoolDisplay": "Anda mendepositkan {{downTokensTextFragment}} ke dalam {{poolDisplayName}} kumpulan kecairan di {{dAppName}}", "espDEXDepositLPWithoutPoolDisplay": "<PERSON><PERSON> <PERSON>kar {{downTokensTextFragment}} untuk {{upTokensTextFragment}} di {{dAppName}}", "espDEXWithdrawLPWithPoolDisplay": "Anda mengeluarkan {{upTokensTextFragment}} daripada {{poolDisplayName}} kumpulan kecairan di {{dAppName}}", "espDEXWithdrawLPWithoutPoolDisplay": "<PERSON><PERSON> <PERSON>kar {{downTokensTextFragment}} untuk {{upTokensTextFragment}} di {{dAppName}}", "espGenericTokenSend": "<PERSON>a men<PERSON> {{downTokensTextFragment}}", "espGenericTokenReceive": "Anda men<PERSON> {{upTokensTextFragment}}", "espGenericTransactionBalanceChange": "<PERSON><PERSON> <PERSON>kar {{downTokensTextFragment}} untuk {{upTokensTextFragment}}", "espUnknown": "TIDAK DIKETAHUI", "espUnknownNFT": "NFT tidak diketahui", "espTextFragmentAnd": "dan", "externalLinkWarningTitle": "<PERSON><PERSON> akan men<PERSON>", "externalLinkWarningDescription": "<PERSON> buka {{url}}. <PERSON><PERSON><PERSON> anda mempercayai sumber ini sebelum berinteraksi dengannya.", "shortcutsWarningDescription": "Jalan pintas disediakan oleh {{url}}. Pastikan anda mempercayai sumber ini sebelum berinteraksi dengannya.", "lowTpsBanner": "Solana sedang mengalami kesesakan rang<PERSON>an", "lowTpsMessageTitle": "<PERSON><PERSON><PERSON><PERSON>", "lowTpsMessage": "Disebabkan oleh kese<PERSON>kan <PERSON> yang tinggi, urus niaga anda mungkin gagal atau ditangguhkan. Sila cuba semula urus niaga yang gagal.", "solanaSlow": "<PERSON><PERSON><PERSON><PERSON> tidak <PERSON>", "solanaNetworkTemporarilyDown": "<PERSON><PERSON><PERSON><PERSON><PERSON> rang<PERSON> buat sementara waktu", "waitForNetworkRestart": "<PERSON>la tunggu untuk rangkaian bermula semula. Dana anda tidak terjejas.", "exploreCollectionsCarouselTitle": "<PERSON><PERSON> Yang <PERSON>", "exploreDropsCarouselTitle": "<PERSON><PERSON>", "exploreSortFloor": "Lantai", "exploreSortListed": "Tersenarai", "exploreSortVolume": "<PERSON><PERSON><PERSON>", "exploreFetchErrorSubtitle": "Sila cuba lagi kemudian.", "exploreFetchErrorTitle": "Gagal ambil.", "exploreTopCollectionsTitle": "Koleksi NFT utama", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "<PERSON><PERSON><PERSON>", "exploreSeeMore": "<PERSON><PERSON>", "exploreTrendingTokens": "Token Yang Popular Pada Ma<PERSON>", "exploreVolumeTokens": "<PERSON><PERSON><PERSON>", "explorePriceChangeTokens": "<PERSON><PERSON><PERSON><PERSON> Mendapat Manfaat Terbesar", "explorePriceTokens": "To<PERSON> mengikut <PERSON>", "exploreMarketCapTokens": "<PERSON><PERSON>", "exploreTrendingSites": "<PERSON><PERSON> Pada <PERSON>", "exploreTopSites": "Tapak <PERSON>", "exploreTrendingCollections": "<PERSON><PERSON><PERSON><PERSON> Yang <PERSON> Pada Ma<PERSON>", "exploreTopCollections": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchCollectionsSection": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesSearchItemsSection": "<PERSON><PERSON>", "collectiblesSearchNrOfItems": "Item {{ nrOfItems }}", "collectiblesSearchPlaceholderText": "<PERSON>i kolekt<PERSON> anda", "collectionPinSuccess": "<PERSON><PERSON><PERSON><PERSON>", "collectionPinFail": "<PERSON><PERSON> men<PERSON><PERSON> k<PERSON>i", "collectionUnpinSuccess": "Koleksi tidak <PERSON>", "collectionUnpinFail": "<PERSON><PERSON> men<PERSON><PERSON> k<PERSON>i", "collectionHideSuccess": "<PERSON><PERSON><PERSON><PERSON> disembunyikan", "collectionHideFail": "Gagal menyembunyikan koleksi", "collectionUnhideSuccess": "<PERSON><PERSON><PERSON><PERSON>", "collectionUnhideFail": "<PERSON><PERSON><PERSON><PERSON> gagal dip<PERSON>", "collectiblesSpamSuccess": "Dilaporkan sebagai spam", "collectiblesSpamFail": "<PERSON><PERSON><PERSON><PERSON> sebagai spam telah gagal", "collectiblesSpamAndHiddenSuccess": "Laporkan sebagai spam dan tersemb<PERSON>yi", "collectiblesNotSpamSuccess": "Dilaporkan sebagai bukan spam", "collectiblesNotSpamFail": "<PERSON><PERSON>or<PERSON> sebagai bukan spam telah gagal", "collectiblesNotSpamAndUnhiddenSuccess": "Laporkan sebagai bukan spam dan tidak disembunyikan", "tokenPageSpamWarning": "Token ini tidak disahkan. <PERSON><PERSON> be<PERSON> dengan token yang anda percayai.", "tokenSpamWarning": "Token ini tersembunyi kerana Phantom mempercayai ia ialah spam.", "collectibleSpamWarning": "Kolektibel ini tersembunyi kerana Phantom mempercayai ia ialah spam.", "collectionSpamWarning": "Kolektibel ini tersembunyi kerana Phantom mempercayai ia ialah spam.", "emojiNoResults": "Tiada emoji di<PERSON>ui", "emojiSearchResults": "<PERSON><PERSON>", "emojiSuggested": "Disyorkan", "emojiSmileys": "Emotikon & Orang", "emojiAnimals": "Haiwan & Alam <PERSON>", "emojiFood": "Makanan & Minuman", "emojiTravel": "Pengembaraan & Tempat-tempat", "emojiActivities": "Aktiviti", "emojiObjects": "<PERSON><PERSON><PERSON><PERSON>", "emojiSymbols": "Simbol", "emojiFlags": "<PERSON><PERSON>", "whichExtensionToConnectWith": "<PERSON><PERSON><PERSON>n yang manakah anda ingin menyambung?", "configureInSettings": "<PERSON><PERSON><PERSON> → <PERSON><PERSON> Aplikasi Lalai.", "continueWith": "<PERSON><PERSON><PERSON> den<PERSON>", "useMetaMask": "<PERSON><PERSON><PERSON>", "usePhantom": "Gunakan <PERSON>", "alwaysAsk": "<PERSON><PERSON><PERSON>", "dontAskMeAgain": "<PERSON>an tanya saya lagi", "selectWalletSettingDescriptionLine1": "Sesetengah aplikasi mungkin tidak menawarkan pilihan untuk menyambung dengan Phantom.", "selectWalletSettingDescriptionLinePhantom": "Sebagai cara untuk menye<PERSON>ai<PERSON> masalah, menyambung dengan MetaMask selalu akan membuka <PERSON> pula.", "selectWalletSettingDescriptionLineAlwaysAsk": "Sebagai cara untuk menye<PERSON><PERSON><PERSON> masalah, semasa anda menyam<PERSON>ng dengan <PERSON>, kita akan bertanya kepada anda jika anda mahu menggunakan Phantom pula.", "selectWalletSettingDescriptionLineMetaMask": "Menetapkan MetaMask sebagai lalai akan melumpuhkan dapps itu daripada menyambung kepada Phantom.", "metaMaskOverride": "Dompet Aplikasi Lalai", "metaMaskOverrideSettingDescriptionLine1": "Untuk menyambung ke laman web yang tidak menawarkan pilihan untuk menggunakan Phantom.", "refreshAndReconnectToast": "<PERSON>r semula dan sambung semula untuk mengenakan perubahan anda", "autoConfirmUnavailable": "Tidak tersedia", "autoConfirmReasonDappNotWhitelisted": "Tidak tersedia kerana kontrak yang ia berasal daripada adalah bukan pada senarai yang dibenarkan kami untuk aplikasi ini.", "autoConfirmReasonSessionNotActive": "Tidak tersedia kerana tiada sesi sahkan secara automatik yang aktif. Sila bolehkannya di bawah.", "autoConfirmReasonRateLimited": "Tidak tersedia kerana dapp yang anda sedang menggunakan menghantar terlalu banyak permintaan.", "autoConfirmReasonUnsupportedNetwork": "Tdak tersedia kerana sahkan secara automatik masih tidak menyokong rangkaian ini.", "autoConfirmReasonSimulationFailed": "Tidak tersedia kerana kita tidak boleh menjamin keselamatan.", "autoConfirmReasonTabNotFocused": "Tidak tersedia kerana tab domain yang anda sedang cuba sahkan secara automatik adalah tidak aktif.", "autoConfirmReasonNotUnlocked": "Tidak tersedia kerana dompet tidak berkunci.", "rpcErrorUnauthorizedWrongAccount": "<PERSON><PERSON> niaga daripada alamat tidak padan dengan alamat akaun terpilih.", "rpcErrorUnauthorizedUnknownSource": "Sumber permintaan RPC tidak boleh ditentukan.", "transactionsDisabledTitle": "<PERSON><PERSON> niaga di<PERSON>", "transactionsDisabledMessage": "<PERSON><PERSON><PERSON> anda tidak dapat menjalankan urus niaga menggunakan Phantom", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Aktif", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL disalin ke papan klip", "notEnoughSolScanTransactionWarning": "<PERSON>rus niaga ini mungkin gagal disebabkan oleh SOL yang tidak mencukupi dalam akaun anda. <PERSON>la tambah lebih banyak SOL ke akaun anda dan cuba semula.", "transactionRevertedWarning": "<PERSON><PERSON> niaga ini gagal semasa simulasi. Dana mungkin hilang jika diserahkan.", "slippageToleranceExceeded": "<PERSON><PERSON> niaga ini mengasal semasa simulasi. Telah melebihi toleransi gelinciran.", "simulationWarningKnownMalicious": "Kita percaya akaun ini berniat jahat. Meluluskan mungkin mengakibatkan kerugian dana.", "simulationWarningPoisonedAddress": "<PERSON>amat ini serupa secara mencurigakan dengan alamat yang anda kirim dana baru-baru ini. Sila sahkan ini ialah alamat yang betul untuk mengelakkan kehilangan dana kepada penipuan.", "simulationWarningInteractingWithAccountWithoutActivity": "Ini ialah akaun tanpa sebarang aktiviti sebelum ini. Mengirimkan dana kepada akaun yang tidak wujud boleh mengakibatkan kehilangan dana", "quests": "<PERSON><PERSON><PERSON>", "questsClaimInProgress": "<PERSON>ntutan <PERSON>g di<PERSON>n", "questsVerifyingCompletion": "Mengesahkan penyelesaian untuk usaha mencari", "questsClaimError": "<PERSON><PERSON>lapan menu<PERSON>ut ganjaran", "questsClaimErrorDescription": "Terdapat kesilapan menuntut ganjaranan anda. Sila cuba lagi kemudian.", "questsBadgeMobileOnly": "<PERSON><PERSON>", "questsBadgeExtensionOnly": "Sambunga<PERSON>", "questsExplainerSheetButtonLabel": "<PERSON><PERSON><PERSON>", "questsNoQuestsAvailable": "Tiada pencarian tersedia", "questsNoQuestsAvailableDescription": "Pada masa ini tiada pencarian tersedia. <PERSON>a akan member<PERSON><PERSON> anda setelah pencarian yang baharu ditambah.", "exploreLearn": "Belajar", "exploreSites": "Tapak", "exploreTokens": "Token", "exploreQuests": "<PERSON><PERSON><PERSON>", "exploreCollections": "<PERSON><PERSON><PERSON><PERSON>", "exploreFilterByall_networks": "<PERSON><PERSON><PERSON>", "exploreSortByrank": "Popular Pada Masa Ini", "exploreSortBytrending": "Popular", "exploreSortByprice": "<PERSON><PERSON>", "exploreSortByprice-change": "<PERSON><PERSON><PERSON>", "exploreSortBytop": "Terata<PERSON>", "exploreSortByvolume": "<PERSON><PERSON><PERSON>", "exploreSortBygainers": "<PERSON><PERSON> yang menang", "exploreSortBylosers": "Orang yang kalah", "exploreSortBymarket-cap": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBymarket_cap": "<PERSON><PERSON>", "exploreTimeFrame1h": "1j", "exploreTimeFrame24h": "24j", "exploreTimeFrame7d": "7h", "exploreTimeFrame30d": "30h", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Kolektibel", "exploreCategoryMarketplace": "<PERSON><PERSON><PERSON>", "exploreCategoryGaming": "Permainan", "exploreCategoryBridges": "Jambatan", "exploreCategoryOther": "Lain-lain", "exploreCategorySocial": "Sosial", "exploreCategoryCommunity": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Staking", "exploreCategoryArt": "<PERSON><PERSON>", "exploreCategoryTools": "Alat", "exploreCategoryDeveloperTools": "Alat Pembangun", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "Taruhan NFT", "exploreCategoryExplorer": "Penjelajah", "exploreCategoryInscriptions": "Inskripsi", "exploreCategoryBridge": "<PERSON><PERSON><PERSON>", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Penyemak Airdrop", "exploreCategoryPoints": "<PERSON>", "exploreCategoryQuests": "<PERSON><PERSON><PERSON>", "exploreCategoryShop": "Kedai", "exploreCategoryProtocol": "Protokol", "exploreCategoryNamingService": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Penjejak Portfolio", "exploreCategoryFitness": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryDePIN": "DePIN", "exploreVolume": "<PERSON><PERSON><PERSON>", "exploreFloor": "Lantai", "exploreCap": "<PERSON><PERSON>", "exploreToken": "Token", "explorePrice": "<PERSON><PERSON>", "explore24hVolume": "24j <PERSON><PERSON><PERSON>", "exploreErrorButtonText": "Cuba Lagi", "exploreErrorDescription": "Terdapat ralat cuba memuat kandungan teroka. <PERSON>la segar semula dan cuba lagi", "exploreErrorTitle": "<PERSON>l memuat kandungan teroka", "exploreNetworkError": "<PERSON>rda<PERSON>t ralat rang<PERSON>an. Sila cuba lagi kemudian.", "exploreTokensLegalDisclaimer": "Senarai token dijanakan menggunakan data pasaran yang disediakan oleh pelbagai penyedia pihak ketiga termasuk CoinGecko, Birdeye dan <PERSON>. Prestasi berdasarkan pada tempoh 24 jam terlebih dahulu. Prestasi lalu tidak menunjukkan prestasi masa depan.", "swapperTokensLegalDisclaimer": "Senarai token yang popular dijanakan menggunakan data pasaran daripada pelbagai penyedia pihak ketiga termasuk CoinGecko, Birdeye dan Jupiter dan berdasarkan pada token popular yang ditukar oleh pengguna Phantom melalui <PERSON>ukar sepanjang tempoh masa yang ditentukan. Prestasi lalu tidak menunjukkan prestasi masa depan.", "exploreLearnErrorTitle": "<PERSON>l memuat kandungan belajar", "exploreLearnErrorDescription": "Terdapat ralat cuba memuat kandungan belajar. <PERSON>la segar semula dan cuba lagi", "exploreShowMore": "<PERSON>n<PERSON>k lebih lagi", "exploreShowLess": "<PERSON><PERSON><PERSON><PERSON> kurang", "exploreVisitSite": "<PERSON><PERSON>", "dappBrowserSearchScreenVisitSite": "<PERSON><PERSON>", "dappBrowserSearchScreenSearchWithGoogle": "<PERSON><PERSON>", "dappBrowserSearchScreenSearchLinkYouCopied": "<PERSON><PERSON><PERSON>", "dappBrowserExtSearchPlaceholder": "<PERSON><PERSON> tapak, token", "dappBrowserSearchNoAppsTokens": "Tiada aplikasi atau token ditemui", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON><PERSON><PERSON>?", "dappBrowserTabsLimitExceededScreenDescription": "Anda mempunyai {{tabsCount}} tab yang terbuka. Untuk membuka lebih banyak, anda akan perlu menutup beberapa tab.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "<PERSON><PERSON><PERSON>", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: Domain ini tidak wujud", "dappBrowserTabErrorHttp": "<PERSON><PERSON><PERSON>, sila gunakan HTTPS", "dappBrowserTabError401Unauthorized": "401 Tidak dibenarkan", "dappBrowserTabError501UnhandledRequest": "501 Permintaan tidak dikendalikan", "dappBrowserTabErrorTimeout": "MASA REHAT: <PERSON><PERSON><PERSON> mengambil masa yang terlalu lama untuk membalas", "dappBrowserTabErrorInvalidResponse": "Tindak balas yang tidak sah", "dappBrowserTabErrorEmptyResponse": "Tindak balas kosong", "dappBrowserTabErrorGeneric": "Suatu ralat telah berlaku", "localizedErrorUnknownError": "Se<PERSON><PERSON>u yang tidak kena berlaku, sila cuba lagi kemudian.", "localizedErrorUnsupportedCountry": "<PERSON><PERSON> ma<PERSON>, negara anda tidak disokong pada masa ini.", "localizedErrorTokensNotLoading": "Terdapat masalah memuat naik token anda. Sila cuba semula.", "localizedErrorSwapperNoQuotes": "Tiada pertukaran tersedia disebabkan oleh pasangan yang tidak disokong, kecairan rendah atau jumlah yang rendah. Cuba laraskan token atau jumlah.", "localizedErrorSwapperRefuelNoQuotes": "Tiada sebut harga ditemui. Cuba token, jumlah yang berbeza atau lumpuhkan mengisi semula.", "localizedErrorInsufficientSellAmount": "Jumlah token terlalu rendah. Tingkatkan nilai untuk menukar Cross-Chain.", "localizedErrorCrossChainUnavailable": "<PERSON><PERSON><PERSON><PERSON> rantai silang tidak tersedia pada masa ini, sila cuba lagi kemudian.", "localizedErrorTokenNotTradable": "Salah satu daripada token terpilih tidak boleh didagang. <PERSON>la pilih token yang berbeza.", "localizedErrorCollectibleLocked": "Akaun token dikunci.", "localizedErrorCollectibleListed": "<PERSON><PERSON><PERSON> token disenaraikan.", "spamActivityAction": "<PERSON><PERSON> butiran yang tersemb<PERSON>yi", "spamActivityTitle": "<PERSON><PERSON><PERSON><PERSON>", "spamActivityWarning": "<PERSON><PERSON> niaga ini tersembunyi kerana Phantom mempercayai ia mungkin spam.", "appAuthenticationFailed": "<PERSON><PERSON>", "appAuthenticationFailedDescription": "Terdapat masalah dengan percubaan pengesahan anda. Sila cuba lagi.", "partialErrorBalanceChainName": "<PERSON>a mengalami kesukaran mengemas kini baki {{chainName}} and<PERSON>. <PERSON> anda selamat.", "partialErrorGeneric": "<PERSON>a mengalami kesukaran menge<PERSON> kini rang<PERSON>, <PERSON><PERSON><PERSON><PERSON> baki <PERSON> anda dan harga anda mungkin sudah lapuk.", "partialErrorTokenDetail": "<PERSON>a mengalami kesukaran mengemas kini baki token anda. <PERSON> anda selamat.", "partialErrorTokenPrices": "<PERSON>a mengalami kesukaran mengemas kini harga token anda. <PERSON> anda selamat.", "partialErrorTokensTrimmed": "<PERSON>a sedang mengalami masalah memaparkan semua token di portfolio anda. <PERSON> anda selamat.", "publicFungibleDetailAbout": "Tentang", "publicFungibleDetailYourBalance": "<PERSON><PERSON>", "publicFungibleDetailInfo": "Maklumat", "publicFungibleDetailShowMore": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailShowLess": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailPerformance": "24j <PERSON><PERSON><PERSON>", "publicFungibleDetailSecurity": "Keselamatan", "publicFungibleDetailMarketCap": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailTotalSupply": "<PERSON><PERSON><PERSON>", "publicFungibleDetailCirculatingSupply": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailMaxSupply": "<PERSON><PERSON><PERSON>", "publicFungibleDetailHolders": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailVolume": "<PERSON><PERSON><PERSON>", "publicFungibleDetailTrades": "Perdagangan", "publicFungibleDetailTraders": "Pedagang", "publicFungibleDetailUniqueWallets": "Dompet Unik", "publicFungibleDetailTop10Holders": "Pemegang 10 Teratas", "publicFungibleDetailTop10HoldersTooltip": "<PERSON><PERSON><PERSON><PERSON><PERSON> peratus jumlah bekalan semasa yang dipegang oleh 10 pemegang token teratas. Ia adalah cara betapa mudahnya harga boleh dimanipulasikan.", "publicFungibleDetailMintable": "<PERSON><PERSON><PERSON>", "publicFungibleDetailMintableTooltip": "<PERSON><PERSON><PERSON> token mungkin bertambah oleh pemilik kontrak jika token boleh dicetak.", "publicFungibleDetailMutableInfo": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailMutableInfoTooltip": "Jika maklumat token seperti nama, logo dan alamat laman web berubah-ubah ia boleh diubah oleh pemilik kontrak.", "publicFungibleDetailOwnershipRenounced": "Pemilikan <PERSON>", "publicFungibleDetailOwnershipRenouncedTooltip": "Jika pemilik token di<PERSON><PERSON>, tiada sesiapa pun boleh melaksanakan fungsi seperti mencetak lebih banyak token.", "publicFungibleDetailUpdateAuthority": "<PERSON><PERSON>", "publicFungibleDetailUpdateAuthorityTooltip": "<PERSON><PERSON> berkuasa kemas kini adalah alamat dompet yang boleh mengubah maklumat jika token berubah-ubah.", "publicFungibleDetailFreezeAuthority": "<PERSON><PERSON>", "publicFungibleDetailFreezeAuthorityTooltip": "<PERSON><PERSON> berk<PERSON>sa pembekuan ialah alamat dompet yang boleh mencegah dana daripada dipindahkan.", "publicFungibleUnverifiedToken": "Token ini tidak disahkan. <PERSON><PERSON> be<PERSON> dengan token yang anda percayai.", "publicFungibleDetailSwap": "<PERSON><PERSON> {{tokenSymbol}}", "publicFungibleDetailSwapDescription": "<PERSON><PERSON> {{tokenSymbol}} dengan aplikasi Phantom", "publicFungibleDetailLinkCopied": "<PERSON><PERSON>in ke papan klip", "publicFungibleDetailContract": "Kontrak", "publicFungibleDetailMint": "Kilang syiling", "unifiedTokenDetailTransactionActivity": "Aktiviti", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON><PERSON>", "unifiedTokenDetailTransactionActivityError": "Gagal untuk memuat aktiviti terkini", "additionalNetworksTitle": "<PERSON><PERSON><PERSON><PERSON>", "copyAddressRowAdditionalNetworks": "<PERSON><PERSON><PERSON><PERSON>", "copyAddressRowAdditionalNetworksHeader": "Rangkaian yang berikut menggunakan alamat yang sama seperti Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Anda boleh menggunakan alamat Ethereum anda secara selamat untuk menghantar dan menerima aset pada mana-mana rangkaian ini.", "cpeUnknownError": "<PERSON><PERSON> yang tidak di<PERSON>i", "cpeUnknownInstructionError": "<PERSON><PERSON> a<PERSON>an yang tidak di<PERSON>i", "cpeAccountFrozen": "<PERSON><PERSON><PERSON>", "cpeAssetFrozen": "<PERSON><PERSON>", "cpeInsufficientFunds": "<PERSON> yang tidak men<PERSON>", "cpeInvalidAuthority": "<PERSON><PERSON> berk<PERSON>sa yang tidak sah", "cpeBalanceBelowRent": "<PERSON>ki berada di bawah ambang pengecualian sewa", "cpeNotApprovedForConfidentialTransfers": "A<PERSON>un tidak diluluskan atas pindahan sulit", "cpeNotAcceptingDepositsOrTransfers": "Akaun tidak menerima deposit atau pindahan", "cpeNoMemoButRequired": "Tiada memo dalam arahan sebel<PERSON>; diperlukan untuk penerima bagi menerima pindahan", "cpeTransferDisabledForMint": "<PERSON><PERSON>han dilumpuhkan untuk kilang syiling ini", "cpeDepositAmountExceedsLimit": "Jumlah deposit telah melebihi had maksimum", "cpeInsufficientFundsForRent": "<PERSON> yang tidak mencukupi untuk sewa", "reportIssueScreenTitle": "Laporkan <PERSON>", "publicFungibleReportIssuePrompt": "A<PERSON><PERSON>h isu yang anda ingin laporkan mengenai {{tokenName}}?", "publicFungibleReportIssueIncorrectInformation": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleReportIssuePriceStale": "Harga tidak mengemas kini", "publicFungibleReportIssuePriceMissing": "<PERSON>rga tidak ada", "publicFungibleReportIssuePerformanceIncorrect": "Prestasi 24j adalah salah", "publicFungibleReportIssueLinkBroken": "Pautan sosial tidak boleh dicapai", "publicFungibleDetailErrorLoading": "Data token tidak tersedia", "reportUserPrompt": "<PERSON><PERSON><PERSON>h isu yang anda ingin laporkan mengenai @{{username}}?", "reportUserOptionAbuseAndHarrassmentTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "reportUserOptionAbuseAndHarrassmentDescription": "Gangguan disasarkan, men<PERSON><PERSON><PERSON> gangguan, an<PERSON><PERSON> ganas, kandungan dan rujukan yang terkutuk", "reportUserOptionPrivacyAndImpersonationTitle": "<PERSON><PERSON><PERSON><PERSON> dan <PERSON>", "reportUserOptionPrivacyAndImpersonationDescription": "Berkongsi atau mengancam untuk mendedahkan maklumat peribadi, menyamar sebagai orang lain", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "<PERSON><PERSON><PERSON> p<PERSON>u, pen<PERSON>uan, pautan hasad", "reportUserSuccess": "Laporan Pengguna Diserahkan.", "settingsClaimUsernameTitle": "Cipta Nama Pengguna", "settingsClaimUsernameDescription": "Identiti unik sama unik seperti dompet anda", "settingsClaimUsernameValueProp1": "Identiti Ringkas", "settingsClaimUsernameValueProp1Description": "Tidak menggunakan alamat rumit yang panjang dan gunakan identiti yang mesra pengguna pula", "settingsClaimUsernameValueProp2": "Lebih Cepat & Lebih Mudah", "settingsClaimUsernameValueProp2Description": "Hantar dan terima kripto dengan mudah, log masuk ke dompet anda, dan berhubung dengan kawan", "settingsClaimUsernameValueProp3": "Te<PERSON><PERSON>", "settingsClaimUsernameValueProp3Description": "Sambungkan sebarang akaun kepada nama pengguna anda ia akan diselaraskan merentasi semua peranti anda", "settingsClaimUsernameHelperText": "<PERSON>a unik anda untuk <PERSON> anda", "settingsClaimUsernameValidationDefault": "<PERSON>a pengguna ini tidak boleh diubah kemudian", "settingsClaimUsernameValidationAvailable": "<PERSON><PERSON> pengguna tersedia", "settingsClaimUsernameValidationUnavailable": "<PERSON><PERSON> pengguna tersedia", "settingsClaimUsernameValidationServerError": "Tidak dapat menyemak jika nama pengguna tersedia, sila cuba lagi kemudian", "settingsClaimUsernameValidationErrorLine1": "<PERSON>a pengguna tidak sah.", "settingsClaimUsernameValidationErrorLine2": "<PERSON>a pengguna mestilah antara {{minChar}} dan {{maxChar}} aksara panjang dan hanya boleh mengandungi huruf dan nombor.", "settingsClaimUsernameValidationLoading": "Menyemak sama ada nama pengguna ini tersedia...", "settingsClaimUsernameSaveAndContinue": "Simpan & Teruskan", "settingsClaimUsernameChooseAvatarTitle": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameAnonymousAuthTitle": "<PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameAnonymousAuthDescription": "Log masuk ke Akaun Phantom anda tanpa nama dengan tandatangan", "settingsClaimUsernameAnonymousAuthBadge": "Ketahui cara ini berfungsi", "settingsClaimUsernameLinkWalletsTitle": "Pautkan dompet anda", "settingsClaimUsernameLinkWalletsDescription": "<PERSON><PERSON><PERSON> dompet yang muncul pada peranti lain dengan nama pengguna anda", "settingsClaimUsernameLinkWalletsBadge": "Tidak <PERSON>", "settingsClaimUsernameConnectAccountsTitle": "Sambung<PERSON>", "settingsClaimUsernameConnectAccountsHelperText": "<PERSON><PERSON><PERSON> alamat rantai akan dihubungkan dengan nama pengguna anda. <PERSON>a boleh mengubah ini kemudian.", "settingsClaimUsernameContinue": "Teruskan", "settingsClaimUsernameCreateUsername": "Cipta Nama Pengguna", "settingsClaimUsernameCreating": "Mencipta nama pengguna...", "settingsClaimUsernameSuccess": "Nama Pengguna Dicipta!", "settingsClaimUsernameError": "Kita mengalami suatu ralat mencipta nama pengguna", "settingsClaimUsernameTryAgain": "Cuba Lagi", "settingsClaimUsernameSuccessHelperText": "<PERSON><PERSON> anda boleh menggunakan nama pengguna baharu anda di semua dompet <PERSON> anda", "settingsClaimUsernameSettingsSyncedTitle": "Tetapan <PERSON>", "settingsClaimUsernameSettingsSyncedHelperText": "Tidak menggunakan alamat rumit yang panjang dan gunakan identiti yang mesra pengguna pula", "settingsClaimUsernameSendToUsernameTitle": "<PERSON><PERSON> kepada <PERSON>", "settingsClaimUsernameSendToUsernameHelperText": "Hantar dan terima kripto dengan mudah, log masuk ke dompet anda, dan berhubung dengan kawan", "settingsClaimUsernameManageAddressesTitle": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameManageAddressesHelperText": "Sebarang token atau kolektibel yang dihantar ke nama pengguna anda akan dihantar ke alamat ini", "settingsClaimUsernameManageAddressesBadge": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameEditAddressesTitle": "Uruskan <PERSON>", "settingsClaimUsernameEditAddressesHelperText": "Sebarang token atau kolektibel yang dihantar ke nama pengguna anda akan dihantar ke alamat ini. <PERSON><PERSON>h satu alamat setiap rantai.", "settingsClaimUsernameEditAddressesError": "<PERSON><PERSON> satu alamat set<PERSON> rang<PERSON> di<PERSON>.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON><PERSON>", "settingsClaimUsernameNoAddressesSaved": "<PERSON><PERSON><PERSON> alamat awam disimpan", "settingsClaimUsernameSave": "Simpan", "settingsClaimUsernameDone": "Se<PERSON><PERSON>", "settingsClaimUsernameWatching": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} <PERSON><PERSON><PERSON>(-akaun)", "settingsClaimUsernameNoOfAccountsSingular": "1 Akaun", "settingsClaimUsernameEmptyAccounts": "<PERSON><PERSON><PERSON>(-akaun)", "settingsClaimUsernameSettingTitle": "Cipta @nama pengguna anda", "settingsClaimUsernameSettingDescription": "Identiti unik untuk dompet anda", "settingsManageUserProfileAbout": "Tentang", "settingsManageUserProfileAboutUsername": "<PERSON><PERSON>", "settingsManageUserProfileAboutBio": "Biografi", "settingsManageUserProfileTitle": "Uruskan Profil", "settingsManageUserProfileManage": "Uruskan", "settingsManageUserProfileAuthFactors": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactorsDescription": "<PERSON><PERSON><PERSON> frasa atau kunci peribadi seed yang boleh log masuk ke Akaun Phantom anda.", "settingsManageUserProfileUpdateAuthFactorsToast": "<PERSON><PERSON><PERSON> keizinan dikemas kini!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Gagal mengemas kini faktor keizinan!", "settingsManageUserProfileBiography": "<PERSON>ting Biografi", "settingsManageUserProfileBiographyDescription": "Tambah biografi singkat ke profil anda", "settingsManageUserProfileUpdateBiographyToast": "Biografi Dikemas <PERSON>!", "settingsManageUserProfileUpdateBiographyToastFailure": "Sesuatu Telah Berlaku. Cuba Lagi", "settingsManageUserProfileBiographyNoUrlMessage": "Sila keluarkan sebarang URL daripada biografi anda", "settingsManageUserProfileLinkedWallets": "Dompet <PERSON>rhubung", "settingsManageUserProfileLinkedWalletsDescription": "<PERSON><PERSON>h dompet yang muncul pada peranti lain semasa log masuk ke Akaun Phantom anda.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Dompet terpaut dikemas kini!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "<PERSON>l untuk mengemas kini dompet terpaut!", "settingsManageUserProfilePrivacy": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfileUpdatePrivacyStateToast": "Privasi dikemas kini!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Gagal mengemas kini privasi!", "settingsManageUserProfilePublicAddresses": "<PERSON><PERSON><PERSON>", "settingsManageUserProfileUpdatePublicAddressToast": "<PERSON><PERSON><PERSON> awam dikemas kini!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Gagal mengemas kini alamat alamat!", "settingsManageUserProfilePrivacyStatePublic": "<PERSON><PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePublicDescription": "Profil dan alamat awam anda tampak dan boleh dicari oleh sesiapa pun", "settingsManageUserProfilePrivacyStatePrivate": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePrivateDescription": "Profil anda boleh dicari oleh sesiapa pun tetapi orang lain mesti menyerahkan keizinan untuk melihat profil dan alamat awam anda", "settingsManageUserProfilePrivacyStateInvisible": "Tidak dapat dilihat", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Profil dan alamat awam anda tersembunyi dan tidak dapat ditemui di merata-rata tempat", "settingsDownloadPhantom": "Muat turun Phantom", "settingsLogOut": "<PERSON><PERSON>", "seedlessAddAWalletPrimaryText": "Tambah Dompet", "seedlessAddAWalletSecondaryText": "Log masuk atau import dompet yang ada ", "seedlessAddSeedlessWalletPrimaryText": "Tambah Dompet Rangkai Kata Rahsia", "seedlessAddSeedlessWalletSecondaryText": "Gunakan <PERSON> ID, Google atau E-mel anda", "seedlessCreateNewWalletPrimaryText": "Cipta Dompet Baharu?", "seedlessCreateNewWalletSecondaryText": "E-mel ini tidak mempunyai dompet, adakah anda mahu membuat dompet?", "seedlessCreateNewWalletButtonText": "<PERSON><PERSON><PERSON>", "seedlessCreateNewWalletNoBundlePrimaryText": "<PERSON><PERSON> tidak di<PERSON>", "seedlessCreateNewWalletNoBundleSecondaryText": "E-mel ini tidak ada dompet", "seedlessCreateNewWalletNoBundleButtonText": "<PERSON><PERSON><PERSON>", "seedlessEmailOptionsPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessEmailOptionsSecondaryText": "Tambah dompet dengan akaun Apple atau Google anda ", "seedlessEmailOptionsButtonText": "Teruskan dengan E-mel", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Buat dompet dengan ID anda", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Buat dompet dengan e-mel Google anda", "seedlessAlreadyExistsPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessAlreadyExistsSecondaryText": "E-mel ini sudah membuat dompet, adakah anda mahu log masuk pula?", "seedlessSignUpWithAppleButtonText": "Daftar diri dengan <PERSON>", "seedlessContinueWithAppleButtonText": "Teruskan dengan Apple", "seedlessSignUpWithGoogleButtonText": "Daftar diri dengan <PERSON>", "seedlessContinueWithGoogleButtonText": "Teruskan dengan Google", "seedlessCreateAPinPrimaryText": "Cipta PIN", "seedlessCreateAPinSecondaryText": "Ini digunakan untuk mengunci dompet anda pada semua peranti anda. <1>Ini tidak boleh dipulihkan.</1>", "seedlessContinueText": "Teruskan", "seedlessConfirmPinPrimaryText": "Sahkan PIN anda", "seedlessConfirmPinSecondaryText": "<PERSON>ka anda terlupa PIN ini, anda tidak akan dapat memulihkan dompet anda pada peranti baharu.", "seedlessConfirmPinButtonText": "Cipta PIN", "seedlessConfirmPinError": "PIN yang salah. Sila cuba lagi", "seedlessAccountsImportedPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessAccountsImportedSecondaryText": "Akaun ini akan diimport secara automatik di dompet anda", "seedlessPreviouslyImportedTag": "Diimport sebelum ini", "seedlessEnterPinPrimaryText": "Masukkan PIN anda", "seedlessEnterPinInvalidPinError": "PIN yang salah dimasukkan. Hanya nombor 4 angka dibenarkan", "seedlessEnterPinNumTriesLeft": "<PERSON><PERSON><PERSON><PERSON> {{numTries}} yang tinggal.", "seedlessEnterPinCooldown": "Cuba lagi dalam masa {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "PIN mestilah 4 angka dengan tepat", "seedlessEnterPinMatch": "<PERSON><PERSON> padan", "seedlessDoneText": "Se<PERSON><PERSON>", "seedlessEnterPinToSign": "Masukkan PIN anda untuk menandatangani urus niaga ini", "seedlessSigning": "Men<PERSON><PERSON><PERSON><PERSON>", "seedlessCreateSeed": "Cipta dompet rangkai kata rahsia", "seedlessImportOptions": "Pilihan import lain", "seedlessImportPrimaryText": "<PERSON>lihan Import", "seedlessImportSecondaryText": "Import dompet yang ada dengan rangkai kata rahsia, kunci peribadi atau dompet perkakasan anda", "seedlessImportSeedPhrase": "Import Rangkai Kata Rahsia", "seedlessImportPrivateKey": "Import Kunci <PERSON>", "seedlessConnectHardwareWallet": "Sambungkan Dompet Perkakasan", "seedlessTryAgain": "Cuba lagi", "seedlessCreatingWalletPrimaryText": "Mencipta dompet", "seedlessCreatingWalletSecondaryText": "Menambah dompet sosial", "seedlessLoadingWalletPrimaryText": "Memuat dompet", "seedlessLoadingWalletSecondaryText": "Mengimport dan memperhatikan dompet terpaut anda", "seedlessLoadingWalletErrorPrimaryText": "Gagal memuat dompet", "seedlessCreatingWalletErrorPrimaryText": "<PERSON><PERSON> mencipta dompet", "seedlessErrorSecondaryText": "Sila cuba lagi", "seedlessAuthAlreadyExistsErrorText": "E-mel yang diberikan sudah kepunyaan akaun Phantom yang berbeza", "seedlessAuthUnknownErrorText": "<PERSON>atu ralat yang tidak diketahui telah berlaku, sila cuba lagi kemudian", "seedlessAuthUnknownErrorTextRefresh": "<PERSON>atu ralat yang tidak diketahui telah berlaku, sila cuba lagi kemudian. Segar semula halaman untuk cuba lagi.", "seedlessAuthErrorCloseWindow": "<PERSON><PERSON><PERSON>", "seedlessWalletExistsErrorPrimaryText": "Dompet sosial sudah ada di peranti anda", "seedlessWalletExistsErrorSecondaryText": "<PERSON>la undur atau tutup skrin ini", "seedlessValueProp1PrimaryText": "<PERSON><PERSON><PERSON><PERSON>", "seedlessValueProp1SecondaryText": "Cipta dompet menggunakan akaun Google atau Apple dan mula menerokai web3 dengan mudahnya", "seedlessValueProp2PrimaryText": "Keselamatan yang dipertingkat", "seedlessValueProp2SecondaryText": "<PERSON><PERSON> anda disimpan dengan selamat dan dipencarkan merentasi berbilang faktor", "seedlessValueProp3PrimaryText": "<PERSON><PERSON><PERSON><PERSON> yang mudah", "seedlessValueProp3SecondaryText": "Pulih<PERSON> capaian ke dompet anda dengan akaun Google atau Apple dan PIN 4 angka anda", "seedlessLoggingIn": "Sedang log masuk...", "seedlessSignUpOrLogin": "Daftar Diri atau Log Masuk", "seedlessContinueByEnteringYourEmail": "Teruskan dengan memasukkan e-mel anda", "seedless": "Rahsia", "seed": "Rangkai Kata Rahsia", "seedlessVerifyPinPrimaryText": "Sahkan PIN", "seedlessVerifyPinSecondaryText": "Sila masukkan nombor PIN anda untuk meneruskan", "seedlessVerifyPinVerifyButtonText": "<PERSON><PERSON><PERSON>", "seedlessVerifyPinForgotButtonText": "Terlupa?", "seedlessPinConfirmButtonText": "<PERSON><PERSON><PERSON>", "seedlessVerifyToastPrimaryText": "Sahkan PIN Anda", "seedlessVerifyToastSecondaryText": "Sekali-sekala kita meminta anda untuk mengesahkan PIN anda supaya anda mengingatinya. <PERSON>ka anda terlupa, anda tidak akan dapat mendapat balik dompet anda.", "seedlessVerifyToastSuccessText": "Nombor PIN anda disahkan!", "seedlessForgotPinPrimaryText": "Set semula PIN menggunakan peranti lain", "seedlessForgotPinSecondaryText": "<PERSON><PERSON> kese<PERSON>n, anda hanya boleh set semula PIN anda di peranti lain di tempat yang anda log masuk", "seedlessForgotPinInstruction1PrimaryText": "<PERSON><PERSON>", "seedlessForgotPinInstruction1SecondaryText": "<PERSON>gi ke peranti lain yang mana akaun Phantom anda log masuk dengan e-mel anda", "seedlessForgotPinInstruction2PrimaryText": "<PERSON>gi ke Tetapan", "seedlessForgotPinInstruction2SecondaryText": "<PERSON>, <PERSON><PERSON><PERSON> “Keselamatan & Privasi” dan kem<PERSON> “Set Semula PIN”", "seedlessForgotPinInstruction3PrimaryText": "Tetapkan PIN Baharu Anda", "seedlessForgotPinInstruction3SecondaryText": "<PERSON><PERSON><PERSON> anda menetapkan PIN baharu anda, sekarang anda boleh log masuk ke dompet anda pada peranti ini", "seedlessForgotPinButtonText": "Saya telah melakukan langkah ini", "seedlessResetPinPrimaryText": "<PERSON> Semula PIN", "seedlessResetPinSecondaryText": "Masukkan PIN baharu yang anda akan ingat. Ini digunakan untuk mengunci dompet anda pada semua peranti anda", "seedlessResetPinSuccessText": "Nombor PIN anda dikemas kini!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> mencipta dompet, anda bersetuju dengan <1><PERSON><PERSON><PERSON></1> dan <5><PERSON><PERSON></5> kami", "pageNotFound": "Halaman tidak ditemui", "pageNotFoundDescription": "Kita tidak mengabaikan anda! Halaman ini tidak wujud atau telah beralih.", "webTokenPagesLegalDisclaimer": "Maklumat penentuan harga disediakan semata-matanya bagi tujuan liputan dan bukan nasihat kewangan. Data pasaran disediakan oleh pihak ketiga dan Phantom bukan perwakilan dari segi ketepatan maklumat.", "signUpOrLogin": "Daftar diri atau log masuk", "portalOnboardingAgreeToTermsOfServiceInterpolated": "<PERSON><PERSON> mencipta akaun, anda bersetuju dengan <1><PERSON><PERSON><PERSON></1> dan <5><PERSON><PERSON></5>", "feedNoActivity": "<PERSON><PERSON><PERSON> tiada aktiviti", "followRequests": "<PERSON><PERSON><PERSON><PERSON>", "following": "Mengikut", "followers": "Pengikut", "follower": "Pengikut", "joined": "Sertai", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "Tiada <PERSON>t", "noFollowing": "Tidak Mengikuti", "noUsersFound": "Tiada Pengguna Ditemui", "viewProfile": "<PERSON><PERSON>", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}