import{a as Et}from"./chunk-7X4NV6OJ.js";import{c as St,f as Pt,h as vt,n as yt}from"./chunk-3KENBVE7.js";var Ct=St((exports,module)=>{vt();yt();typeof navigator<"u"&&function(t,e){typeof exports=="object"&&typeof module<"u"?module.exports=e():typeof define=="function"&&define.amd?define(e):(t=typeof globalThis<"u"?globalThis:t||self,t.lottie=e())}(exports,function(){"use strict";var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(e){_useWebWorker=!!e},getWebWorker=function(){return _useWebWorker},setLocationHref=function(e){locationHref=e},getLocationHref=function(){return locationHref};function createTag(t){return document.createElement(t)}function extendPrototype(t,e){var r,i=t.length,s;for(r=0;r<i;r+=1){s=t[r].prototype;for(var n in s)Object.prototype.hasOwnProperty.call(s,n)&&(e.prototype[n]=s[n])}}function getDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)}function createProxyFunction(t){function e(){}return e.prototype=t,e}var audioControllerFactory=function(){function t(e){this.audios=[],this.audioFactory=e,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(r){this.audios.push(r)},pause:function(){var r,i=this.audios.length;for(r=0;r<i;r+=1)this.audios[r].pause()},resume:function(){var r,i=this.audios.length;for(r=0;r<i;r+=1)this.audios[r].resume()},setRate:function(r){var i,s=this.audios.length;for(i=0;i<s;i+=1)this.audios[i].setRate(r)},createAudio:function(r){return this.audioFactory?this.audioFactory(r):self.Howl?new self.Howl({src:[r]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(r){this.audioFactory=r},setVolume:function(r){this._volume=r,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var r,i=this.audios.length;for(r=0;r<i;r+=1)this.audios[r].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),createTypedArray=function(){function t(r,i){var s=0,n=[],a;switch(r){case"int16":case"uint8c":a=1;break;default:a=1.1;break}for(s=0;s<i;s+=1)n.push(a);return n}function e(r,i){return r==="float32"?new Float32Array(i):r==="int16"?new Int16Array(i):r==="uint8c"?new Uint8ClampedArray(i):t(r,i)}return typeof Uint8ClampedArray=="function"&&typeof Float32Array=="function"?e:t}();function createSizedArray(t){return Array.apply(null,{length:t})}function _typeof$6(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$6=function(r){return typeof r}:_typeof$6=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$6(t)}var subframeEnabled=!0,expressionsPlugin=null,expressionsInterfaces=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};(function(){var t=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],e,r=t.length;for(e=0;e<r;e+=1)BMMath[t[e]]=Math[t[e]]})();function ProjectInterface$1(){return{}}BMMath.random=Math.random,BMMath.abs=function(t){var e=_typeof$6(t);if(e==="object"&&t.length){var r=createSizedArray(t.length),i,s=t.length;for(i=0;i<s;i+=1)r[i]=Math.abs(t[i]);return r}return Math.abs(t)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(t){_shouldRoundValues=!!t}function bmRnd(t){return _shouldRoundValues?Math.round(t):t}function styleDiv(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(t,e,r,i){this.type=t,this.currentTime=e,this.totalTime=r,this.direction=i<0?-1:1}function BMCompleteEvent(t,e){this.type=t,this.direction=e<0?-1:1}function BMCompleteLoopEvent(t,e,r,i){this.type=t,this.currentLoop=r,this.totalLoops=e,this.direction=i<0?-1:1}function BMSegmentStartEvent(t,e,r){this.type=t,this.firstFrame=e,this.totalFrames=r}function BMDestroyEvent(t,e){this.type=t,this.target=e}function BMRenderFrameErrorEvent(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function BMConfigErrorEvent(t){this.type="configError",this.nativeError=t}function BMAnimationConfigErrorEvent(t,e){this.type=t,this.nativeError=e}var createElementID=function(){var t=0;return function(){return t+=1,idPrefix$1+"__lottie_element_"+t}}();function HSVtoRGB(t,e,r){var i,s,n,a,h,o,p,y;switch(a=Math.floor(t*6),h=t*6-a,o=r*(1-e),p=r*(1-h*e),y=r*(1-(1-h)*e),a%6){case 0:i=r,s=y,n=o;break;case 1:i=p,s=r,n=o;break;case 2:i=o,s=r,n=y;break;case 3:i=o,s=p,n=r;break;case 4:i=y,s=o,n=r;break;case 5:i=r,s=o,n=p;break;default:break}return[i,s,n]}function RGBtoHSV(t,e,r){var i=Math.max(t,e,r),s=Math.min(t,e,r),n=i-s,a,h=i===0?0:n/i,o=i/255;switch(i){case s:a=0;break;case t:a=e-r+n*(e<r?6:0),a/=6*n;break;case e:a=r-t+n*2,a/=6*n;break;case r:a=t-e+n*4,a/=6*n;break;default:break}return[a,h,o]}function addSaturationToRGB(t,e){var r=RGBtoHSV(t[0]*255,t[1]*255,t[2]*255);return r[1]+=e,r[1]>1?r[1]=1:r[1]<=0&&(r[1]=0),HSVtoRGB(r[0],r[1],r[2])}function addBrightnessToRGB(t,e){var r=RGBtoHSV(t[0]*255,t[1]*255,t[2]*255);return r[2]+=e,r[2]>1?r[2]=1:r[2]<0&&(r[2]=0),HSVtoRGB(r[0],r[1],r[2])}function addHueToRGB(t,e){var r=RGBtoHSV(t[0]*255,t[1]*255,t[2]*255);return r[0]+=e/360,r[0]>1?r[0]-=1:r[0]<0&&(r[0]+=1),HSVtoRGB(r[0],r[1],r[2])}var rgbToHex=function(){var t=[],e,r;for(e=0;e<256;e+=1)r=e.toString(16),t[e]=r.length===1?"0"+r:r;return function(i,s,n){return i<0&&(i=0),s<0&&(s=0),n<0&&(n=0),"#"+t[i]+t[s]+t[n]}}(),setSubframeEnabled=function(e){subframeEnabled=!!e},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(e){expressionsPlugin=e},getExpressionsPlugin=function(){return expressionsPlugin},setExpressionInterfaces=function(e){expressionsInterfaces=e},getExpressionInterfaces=function(){return expressionsInterfaces},setDefaultCurveSegments=function(e){defaultCurveSegments=e},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(e){idPrefix$1=e},getIdPrefix=function(){return idPrefix$1};function createNS(t){return document.createElementNS(svgNS,t)}function _typeof$5(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$5=function(r){return typeof r}:_typeof$5=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$5(t)}var dataManager=function(){var t=1,e=[],r,i,s={onmessage:function(){},postMessage:function(b){r({data:b})}},n={postMessage:function(b){s.onmessage({data:b})}};function a(f){if(self.Worker&&self.Blob&&getWebWorker()){var b=new Blob(["var _workerSelf = self; self.onmessage = ",f.toString()],{type:"text/javascript"}),g=URL.createObjectURL(b);return new Worker(g)}return r=f,s}function h(){i||(i=a(function(b){function g(){function C(L,x){var A,v,S=L.length,I,M,B,N;for(v=0;v<S;v+=1)if(A=L[v],"ks"in A&&!A.completed){if(A.completed=!0,A.hasMask){var H=A.masksProperties;for(M=H.length,I=0;I<M;I+=1)if(H[I].pt.k.i)E(H[I].pt.k);else for(N=H[I].pt.k.length,B=0;B<N;B+=1)H[I].pt.k[B].s&&E(H[I].pt.k[B].s[0]),H[I].pt.k[B].e&&E(H[I].pt.k[B].e[0])}A.ty===0?(A.layers=l(A.refId,x),C(A.layers,x)):A.ty===4?d(A.shapes):A.ty===5&&D(A)}}function u(L,x){if(L){var A=0,v=L.length;for(A=0;A<v;A+=1)L[A].t===1&&(L[A].data.layers=l(L[A].data.refId,x),C(L[A].data.layers,x))}}function c(L,x){for(var A=0,v=x.length;A<v;){if(x[A].id===L)return x[A];A+=1}return null}function l(L,x){var A=c(L,x);return A?A.layers.__used?JSON.parse(JSON.stringify(A.layers)):(A.layers.__used=!0,A.layers):null}function d(L){var x,A=L.length,v,S;for(x=A-1;x>=0;x-=1)if(L[x].ty==="sh")if(L[x].ks.k.i)E(L[x].ks.k);else for(S=L[x].ks.k.length,v=0;v<S;v+=1)L[x].ks.k[v].s&&E(L[x].ks.k[v].s[0]),L[x].ks.k[v].e&&E(L[x].ks.k[v].e[0]);else L[x].ty==="gr"&&d(L[x].it)}function E(L){var x,A=L.i.length;for(x=0;x<A;x+=1)L.i[x][0]+=L.v[x][0],L.i[x][1]+=L.v[x][1],L.o[x][0]+=L.v[x][0],L.o[x][1]+=L.v[x][1]}function _(L,x){var A=x?x.split("."):[100,100,100];return L[0]>A[0]?!0:A[0]>L[0]?!1:L[1]>A[1]?!0:A[1]>L[1]?!1:L[2]>A[2]?!0:A[2]>L[2]?!1:null}var T=function(){var L=[4,4,14];function x(v){var S=v.t.d;v.t.d={k:[{s:S,t:0}]}}function A(v){var S,I=v.length;for(S=0;S<I;S+=1)v[S].ty===5&&x(v[S])}return function(v){if(_(L,v.v)&&(A(v.layers),v.assets)){var S,I=v.assets.length;for(S=0;S<I;S+=1)v.assets[S].layers&&A(v.assets[S].layers)}}}(),F=function(){var L=[4,7,99];return function(x){if(x.chars&&!_(L,x.v)){var A,v=x.chars.length;for(A=0;A<v;A+=1){var S=x.chars[A];S.data&&S.data.shapes&&(d(S.data.shapes),S.data.ip=0,S.data.op=99999,S.data.st=0,S.data.sr=1,S.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},x.chars[A].t||(S.data.shapes.push({ty:"no"}),S.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}}(),R=function(){var L=[5,7,15];function x(v){var S=v.t.p;typeof S.a=="number"&&(S.a={a:0,k:S.a}),typeof S.p=="number"&&(S.p={a:0,k:S.p}),typeof S.r=="number"&&(S.r={a:0,k:S.r})}function A(v){var S,I=v.length;for(S=0;S<I;S+=1)v[S].ty===5&&x(v[S])}return function(v){if(_(L,v.v)&&(A(v.layers),v.assets)){var S,I=v.assets.length;for(S=0;S<I;S+=1)v.assets[S].layers&&A(v.assets[S].layers)}}}(),G=function(){var L=[4,1,9];function x(v){var S,I=v.length,M,B;for(S=0;S<I;S+=1)if(v[S].ty==="gr")x(v[S].it);else if(v[S].ty==="fl"||v[S].ty==="st")if(v[S].c.k&&v[S].c.k[0].i)for(B=v[S].c.k.length,M=0;M<B;M+=1)v[S].c.k[M].s&&(v[S].c.k[M].s[0]/=255,v[S].c.k[M].s[1]/=255,v[S].c.k[M].s[2]/=255,v[S].c.k[M].s[3]/=255),v[S].c.k[M].e&&(v[S].c.k[M].e[0]/=255,v[S].c.k[M].e[1]/=255,v[S].c.k[M].e[2]/=255,v[S].c.k[M].e[3]/=255);else v[S].c.k[0]/=255,v[S].c.k[1]/=255,v[S].c.k[2]/=255,v[S].c.k[3]/=255}function A(v){var S,I=v.length;for(S=0;S<I;S+=1)v[S].ty===4&&x(v[S].shapes)}return function(v){if(_(L,v.v)&&(A(v.layers),v.assets)){var S,I=v.assets.length;for(S=0;S<I;S+=1)v.assets[S].layers&&A(v.assets[S].layers)}}}(),w=function(){var L=[4,4,18];function x(v){var S,I=v.length,M,B;for(S=I-1;S>=0;S-=1)if(v[S].ty==="sh")if(v[S].ks.k.i)v[S].ks.k.c=v[S].closed;else for(B=v[S].ks.k.length,M=0;M<B;M+=1)v[S].ks.k[M].s&&(v[S].ks.k[M].s[0].c=v[S].closed),v[S].ks.k[M].e&&(v[S].ks.k[M].e[0].c=v[S].closed);else v[S].ty==="gr"&&x(v[S].it)}function A(v){var S,I,M=v.length,B,N,H,z;for(I=0;I<M;I+=1){if(S=v[I],S.hasMask){var W=S.masksProperties;for(N=W.length,B=0;B<N;B+=1)if(W[B].pt.k.i)W[B].pt.k.c=W[B].cl;else for(z=W[B].pt.k.length,H=0;H<z;H+=1)W[B].pt.k[H].s&&(W[B].pt.k[H].s[0].c=W[B].cl),W[B].pt.k[H].e&&(W[B].pt.k[H].e[0].c=W[B].cl)}S.ty===4&&x(S.shapes)}}return function(v){if(_(L,v.v)&&(A(v.layers),v.assets)){var S,I=v.assets.length;for(S=0;S<I;S+=1)v.assets[S].layers&&A(v.assets[S].layers)}}}();function k(L){L.__complete||(G(L),T(L),F(L),R(L),w(L),C(L.layers,L.assets),u(L.chars,L.assets),L.__complete=!0)}function D(L){L.t.a.length===0&&"m"in L.t.p}var V={};return V.completeData=k,V.checkColors=G,V.checkChars=F,V.checkPathProperties=R,V.checkShapes=w,V.completeLayers=C,V}if(n.dataManager||(n.dataManager=g()),n.assetLoader||(n.assetLoader=function(){function C(c){var l=c.getResponseHeader("content-type");return l&&c.responseType==="json"&&l.indexOf("json")!==-1||c.response&&_typeof$5(c.response)==="object"?c.response:c.response&&typeof c.response=="string"?JSON.parse(c.response):c.responseText?JSON.parse(c.responseText):null}function u(c,l,d,E){var _,T=new XMLHttpRequest;try{T.responseType="json"}catch{}T.onreadystatechange=function(){if(T.readyState===4)if(T.status===200)_=C(T),d(_);else try{_=C(T),d(_)}catch(F){E&&E(F)}};try{T.open(["G","E","T"].join(""),c,!0)}catch{T.open(["G","E","T"].join(""),l+"/"+c,!0)}T.send()}return{load:u}}()),b.data.type==="loadAnimation")n.assetLoader.load(b.data.path,b.data.fullPath,function(C){n.dataManager.completeData(C),n.postMessage({id:b.data.id,payload:C,status:"success"})},function(){n.postMessage({id:b.data.id,status:"error"})});else if(b.data.type==="complete"){var m=b.data.animation;n.dataManager.completeData(m),n.postMessage({id:b.data.id,payload:m,status:"success"})}else b.data.type==="loadData"&&n.assetLoader.load(b.data.path,b.data.fullPath,function(C){n.postMessage({id:b.data.id,payload:C,status:"success"})},function(){n.postMessage({id:b.data.id,status:"error"})})}),i.onmessage=function(f){var b=f.data,g=b.id,m=e[g];e[g]=null,b.status==="success"?m.onComplete(b.payload):m.onError&&m.onError()})}function o(f,b){t+=1;var g="processId_"+t;return e[g]={onComplete:f,onError:b},g}function p(f,b,g){h();var m=o(b,g);i.postMessage({type:"loadAnimation",path:f,fullPath:self.location.origin+self.location.pathname,id:m})}function y(f,b,g){h();var m=o(b,g);i.postMessage({type:"loadData",path:f,fullPath:self.location.origin+self.location.pathname,id:m})}function P(f,b,g){h();var m=o(b,g);i.postMessage({type:"complete",animation:f,id:m})}return{loadAnimation:p,loadData:y,completeAnimation:P}}(),ImagePreloader=function(){var t=function(){var u=createTag("canvas");u.width=1,u.height=1;var c=u.getContext("2d");return c.fillStyle="rgba(0,0,0,0)",c.fillRect(0,0,1,1),u}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(u,c,l){var d="";if(u.e)d=u.p;else if(c){var E=u.p;E.indexOf("images/")!==-1&&(E=E.split("/")[1]),d=c+E}else d=l,d+=u.u?u.u:"",d+=u.p;return d}function s(u){var c=0,l=setInterval(function(){var d=u.getBBox();(d.width||c>500)&&(this._imageLoaded(),clearInterval(l)),c+=1}.bind(this),50)}function n(u){var c=i(u,this.assetsPath,this.path),l=createNS("image");isSafari?this.testImageLoaded(l):l.addEventListener("load",this._imageLoaded,!1),l.addEventListener("error",function(){d.img=t,this._imageLoaded()}.bind(this),!1),l.setAttributeNS("http://www.w3.org/1999/xlink","href",c),this._elementHelper.append?this._elementHelper.append(l):this._elementHelper.appendChild(l);var d={img:l,assetData:u};return d}function a(u){var c=i(u,this.assetsPath,this.path),l=createTag("img");l.crossOrigin="anonymous",l.addEventListener("load",this._imageLoaded,!1),l.addEventListener("error",function(){d.img=t,this._imageLoaded()}.bind(this),!1),l.src=c;var d={img:l,assetData:u};return d}function h(u){var c={assetData:u},l=i(u,this.assetsPath,this.path);return dataManager.loadData(l,function(d){c.img=d,this._footageLoaded()}.bind(this),function(){c.img={},this._footageLoaded()}.bind(this)),c}function o(u,c){this.imagesLoadedCb=c;var l,d=u.length;for(l=0;l<d;l+=1)u[l].layers||(!u[l].t||u[l].t==="seq"?(this.totalImages+=1,this.images.push(this._createImageData(u[l]))):u[l].t===3&&(this.totalFootages+=1,this.images.push(this.createFootageData(u[l]))))}function p(u){this.path=u||""}function y(u){this.assetsPath=u||""}function P(u){for(var c=0,l=this.images.length;c<l;){if(this.images[c].assetData===u)return this.images[c].img;c+=1}return null}function f(){this.imagesLoadedCb=null,this.images.length=0}function b(){return this.totalImages===this.loadedAssets}function g(){return this.totalFootages===this.loadedFootagesCount}function m(u,c){u==="svg"?(this._elementHelper=c,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}function C(){this._imageLoaded=e.bind(this),this._footageLoaded=r.bind(this),this.testImageLoaded=s.bind(this),this.createFootageData=h.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return C.prototype={loadAssets:o,setAssetsPath:y,setPath:p,loadedImages:b,loadedFootages:g,destroy:f,getAsset:P,createImgData:a,createImageData:n,imageLoaded:e,footageLoaded:r,setCacheType:m},C}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(e,r){if(this._cbs[e])for(var i=this._cbs[e],s=0;s<i.length;s+=1)i[s](r)},addEventListener:function(e,r){return this._cbs[e]||(this._cbs[e]=[]),this._cbs[e].push(r),function(){this.removeEventListener(e,r)}.bind(this)},removeEventListener:function(e,r){if(!r)this._cbs[e]=null;else if(this._cbs[e]){for(var i=0,s=this._cbs[e].length;i<s;)this._cbs[e][i]===r&&(this._cbs[e].splice(i,1),i-=1,s-=1),i+=1;this._cbs[e].length||(this._cbs[e]=null)}}};var markerParser=function(){function t(e){for(var r=e.split(`\r
`),i={},s,n=0,a=0;a<r.length;a+=1)s=r[a].split(":"),s.length===2&&(i[s[0]]=s[1].trim(),n+=1);if(n===0)throw new Error;return i}return function(e){for(var r=[],i=0;i<e.length;i+=1){var s=e[i],n={time:s.tm,duration:s.dr};try{n.payload=JSON.parse(e[i].cm)}catch{try{n.payload=t(e[i].cm)}catch{n.payload={name:e[i].cm}}}r.push(n)}return r}}(),ProjectInterface=function(){function t(e){this.compositions.push(e)}return function(){function e(r){for(var i=0,s=this.compositions.length;i<s;){if(this.compositions[i].data&&this.compositions[i].data.nm===r)return this.compositions[i].prepareFrame&&this.compositions[i].data.xt&&this.compositions[i].prepareFrame(this.currentFrame),this.compositions[i].compInterface;i+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),renderers={},registerRenderer=function(e,r){renderers[e]=r};function getRenderer(t){return renderers[t]}function getRegisteredRenderer(){if(renderers.canvas)return"canvas";for(var t in renderers)if(renderers[t])return t;return""}function _typeof$4(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$4=function(r){return typeof r}:_typeof$4=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$4(t)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0),this.expressionsPlugin=getExpressionsPlugin()};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var r=getRenderer(e);this.renderer=new r(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,t.loop===""||t.loop===null||t.loop===void 0||t.loop===!0?this.loop=!0:t.loop===!1?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay="autoplay"in t?t.autoplay:!0,this.name=t.name?t.name:"",this.autoloadSegments=Object.prototype.hasOwnProperty.call(t,"autoloadSegments")?t.autoloadSegments:!0,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(t.path.lastIndexOf("\\")!==-1?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(t.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(t){dataManager.completeAnimation(t,this.configAnimation)},AnimationItem.prototype.setData=function(t,e){e&&_typeof$4(e)!=="object"&&(e=JSON.parse(e));var r={wrapper:t,animationData:e},i=t.attributes;r.path=i.getNamedItem("data-animation-path")?i.getNamedItem("data-animation-path").value:i.getNamedItem("data-bm-path")?i.getNamedItem("data-bm-path").value:i.getNamedItem("bm-path")?i.getNamedItem("bm-path").value:"",r.animType=i.getNamedItem("data-anim-type")?i.getNamedItem("data-anim-type").value:i.getNamedItem("data-bm-type")?i.getNamedItem("data-bm-type").value:i.getNamedItem("bm-type")?i.getNamedItem("bm-type").value:i.getNamedItem("data-bm-renderer")?i.getNamedItem("data-bm-renderer").value:i.getNamedItem("bm-renderer")?i.getNamedItem("bm-renderer").value:getRegisteredRenderer()||"canvas";var s=i.getNamedItem("data-anim-loop")?i.getNamedItem("data-anim-loop").value:i.getNamedItem("data-bm-loop")?i.getNamedItem("data-bm-loop").value:i.getNamedItem("bm-loop")?i.getNamedItem("bm-loop").value:"";s==="false"?r.loop=!1:s==="true"?r.loop=!0:s!==""&&(r.loop=parseInt(s,10));var n=i.getNamedItem("data-anim-autoplay")?i.getNamedItem("data-anim-autoplay").value:i.getNamedItem("data-bm-autoplay")?i.getNamedItem("data-bm-autoplay").value:i.getNamedItem("bm-autoplay")?i.getNamedItem("bm-autoplay").value:!0;r.autoplay=n!=="false",r.name=i.getNamedItem("data-name")?i.getNamedItem("data-name").value:i.getNamedItem("data-bm-name")?i.getNamedItem("data-bm-name").value:i.getNamedItem("bm-name")?i.getNamedItem("bm-name").value:"";var a=i.getNamedItem("data-anim-prerender")?i.getNamedItem("data-anim-prerender").value:i.getNamedItem("data-bm-prerender")?i.getNamedItem("data-bm-prerender").value:i.getNamedItem("bm-prerender")?i.getNamedItem("bm-prerender").value:"";a==="false"&&(r.prerender=!1),r.path?this.setParams(r):this.trigger("destroy")},AnimationItem.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e=this.animationData.layers,r,i=e.length,s=t.layers,n,a=s.length;for(n=0;n<a;n+=1)for(r=0;r<i;){if(e[r].id===s[n].id){e[r]=s[n];break}r+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(i=t.assets.length,r=0;r<i;r+=1)this.animationData.assets.push(t.assets[r]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(t){this.animationData=t;var e=getExpressionsPlugin();e&&e.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||t.length===0||!this.autoloadSegments){this.trigger("data_ready"),this.timeCompleted=this.totalFrames;return}var e=t.shift();this.timeCompleted=e.time*this.frameRate;var r=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(r,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},AnimationItem.prototype.loadSegments=function(){var t=this.animationData.segments;t||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=markerParser(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(e){this.triggerConfigError(e)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||this.renderer.rendererType!=="canvas")&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=getExpressionsPlugin();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(t,e){var r=typeof t=="number"?t:void 0,i=typeof e=="number"?e:void 0;this.renderer.updateContainerSize(r,i)},AnimationItem.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!(this.isLoaded===!1||!this.renderer))try{this.expressionsPlugin&&this.expressionsPlugin.resetFrame(),this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},AnimationItem.prototype.play=function(t){t&&this.name!==t||this.isPaused===!0&&(this.isPaused=!1,this.trigger("_play"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(t){t&&this.name!==t||this.isPaused===!1&&(this.isPaused=!0,this.trigger("_pause"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(t){t&&this.name!==t||(this.isPaused===!0?this.play():this.pause())},AnimationItem.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(t){for(var e,r=0;r<this.markers.length;r+=1)if(e=this.markers[r],e.payload&&e.payload.name===t)return e;return null},AnimationItem.prototype.goToAndStop=function(t,e,r){if(!(r&&this.name!==r)){var i=Number(t);if(isNaN(i)){var s=this.getMarkerData(t);s&&this.goToAndStop(s.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(t,e,r){if(!(r&&this.name!==r)){var i=Number(t);if(isNaN(i)){var s=this.getMarkerData(t);s&&(s.duration?this.playSegments([s.time,s.time+s.duration],!0):this.goToAndStop(s.time,!0))}else this.goToAndStop(i,e,r);this.play()}},AnimationItem.prototype.advanceTime=function(t){if(!(this.isPaused===!0||this.isLoaded===!1)){var e=this.currentRawFrame+t*this.frameModifier,r=!1;e>=this.totalFrames-1&&this.frameModifier>0?!this.loop||this.playCount===this.loop?this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(r=!0,e=this.totalFrames-1):e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):e<0?this.checkSegments(e%this.totalFrames)||(this.loop&&!(this.playCount--<=0&&this.loop!==!0)?(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0):(r=!0,e=0)):this.setCurrentRawFrameValue(e),r&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(t,e){var r=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?r=t:this.currentRawFrame+this.firstFrame>e&&(r=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,r!==-1&&this.goToAndStop(r,!0)},AnimationItem.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),_typeof$4(t[0])==="object"){var r,i=t.length;for(r=0;r<i;r+=1)this.segments.push(t[r])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(t){return this.segments.length?(this.adjustSegment(this.segments.shift(),t),!0):!1},AnimationItem.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.expressionsPlugin=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setLoop=function(t){this.loop=t},AnimationItem.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},AnimationItem.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var r=t.p;r.indexOf("images/")!==-1&&(r=r.split("/")[1]),e=this.assetsPath+r}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},AnimationItem.prototype.getAssetData=function(t){for(var e=0,r=this.assets.length;e<r;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(t,e,r){try{var i=this.renderer.getElementByPath(t);i.updateDocumentData(e,r)}catch{}},AnimationItem.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new BMCompleteEvent(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new BMDestroyEvent(t,this));break;default:this.triggerEvent(t)}t==="enterFrame"&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameMult)),t==="loopComplete"&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult)),t==="complete"&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(t,this.frameMult)),t==="segmentStart"&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames)),t==="destroy"&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(t,this))},AnimationItem.prototype.triggerRenderFrameError=function(t){var e=new BMRenderFrameErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},AnimationItem.prototype.triggerConfigError=function(t){var e=new BMConfigErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var animationManager=function(){var t={},e=[],r=0,i=0,s=0,n=!0,a=!1;function h(x){for(var A=0,v=x.target;A<i;)e[A].animation===v&&(e.splice(A,1),A-=1,i-=1,v.isPaused||P()),A+=1}function o(x,A){if(!x)return null;for(var v=0;v<i;){if(e[v].elem===x&&e[v].elem!==null)return e[v].animation;v+=1}var S=new AnimationItem;return f(S,x),S.setData(x,A),S}function p(){var x,A=e.length,v=[];for(x=0;x<A;x+=1)v.push(e[x].animation);return v}function y(){s+=1,G()}function P(){s-=1}function f(x,A){x.addEventListener("destroy",h),x.addEventListener("_active",y),x.addEventListener("_idle",P),e.push({elem:A,animation:x}),i+=1}function b(x){var A=new AnimationItem;return f(A,null),A.setParams(x),A}function g(x,A){var v;for(v=0;v<i;v+=1)e[v].animation.setSpeed(x,A)}function m(x,A){var v;for(v=0;v<i;v+=1)e[v].animation.setDirection(x,A)}function C(x){var A;for(A=0;A<i;A+=1)e[A].animation.play(x)}function u(x){var A=x-r,v;for(v=0;v<i;v+=1)e[v].animation.advanceTime(A);r=x,s&&!a?self.requestAnimationFrame(u):n=!0}function c(x){r=x,self.requestAnimationFrame(u)}function l(x){var A;for(A=0;A<i;A+=1)e[A].animation.pause(x)}function d(x,A,v){var S;for(S=0;S<i;S+=1)e[S].animation.goToAndStop(x,A,v)}function E(x){var A;for(A=0;A<i;A+=1)e[A].animation.stop(x)}function _(x){var A;for(A=0;A<i;A+=1)e[A].animation.togglePause(x)}function T(x){var A;for(A=i-1;A>=0;A-=1)e[A].animation.destroy(x)}function F(x,A,v){var S=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),I,M=S.length;for(I=0;I<M;I+=1)v&&S[I].setAttribute("data-bm-type",v),o(S[I],x);if(A&&M===0){v||(v="svg");var B=document.getElementsByTagName("body")[0];B.innerText="";var N=createTag("div");N.style.width="100%",N.style.height="100%",N.setAttribute("data-bm-type",v),B.appendChild(N),o(N,x)}}function R(){var x;for(x=0;x<i;x+=1)e[x].animation.resize()}function G(){!a&&s&&n&&(self.requestAnimationFrame(c),n=!1)}function w(){a=!0}function k(){a=!1,G()}function D(x,A){var v;for(v=0;v<i;v+=1)e[v].animation.setVolume(x,A)}function V(x){var A;for(A=0;A<i;A+=1)e[A].animation.mute(x)}function L(x){var A;for(A=0;A<i;A+=1)e[A].animation.unmute(x)}return t.registerAnimation=o,t.loadAnimation=b,t.setSpeed=g,t.setDirection=m,t.play=C,t.pause=l,t.stop=E,t.togglePause=_,t.searchAnimations=F,t.resize=R,t.goToAndStop=d,t.destroy=T,t.freeze=w,t.unfreeze=k,t.setVolume=D,t.mute=V,t.unmute=L,t.getRegisteredAnimations=p,t}(),BezierFactory=function(){var t={};t.getBezierEasing=r;var e={};function r(c,l,d,E,_){var T=_||("bez_"+c+"_"+l+"_"+d+"_"+E).replace(/\./g,"p");if(e[T])return e[T];var F=new u([c,l,d,E]);return e[T]=F,F}var i=4,s=.001,n=1e-7,a=10,h=11,o=1/(h-1),p=typeof Float32Array=="function";function y(c,l){return 1-3*l+3*c}function P(c,l){return 3*l-6*c}function f(c){return 3*c}function b(c,l,d){return((y(l,d)*c+P(l,d))*c+f(l))*c}function g(c,l,d){return 3*y(l,d)*c*c+2*P(l,d)*c+f(l)}function m(c,l,d,E,_){var T,F,R=0;do F=l+(d-l)/2,T=b(F,E,_)-c,T>0?d=F:l=F;while(Math.abs(T)>n&&++R<a);return F}function C(c,l,d,E){for(var _=0;_<i;++_){var T=g(l,d,E);if(T===0)return l;var F=b(l,d,E)-c;l-=F/T}return l}function u(c){this._p=c,this._mSampleValues=p?new Float32Array(h):new Array(h),this._precomputed=!1,this.get=this.get.bind(this)}return u.prototype={get:function(l){var d=this._p[0],E=this._p[1],_=this._p[2],T=this._p[3];return this._precomputed||this._precompute(),d===E&&_===T?l:l===0?0:l===1?1:b(this._getTForX(l),E,T)},_precompute:function(){var l=this._p[0],d=this._p[1],E=this._p[2],_=this._p[3];this._precomputed=!0,(l!==d||E!==_)&&this._calcSampleValues()},_calcSampleValues:function(){for(var l=this._p[0],d=this._p[2],E=0;E<h;++E)this._mSampleValues[E]=b(E*o,l,d)},_getTForX:function(l){for(var d=this._p[0],E=this._p[2],_=this._mSampleValues,T=0,F=1,R=h-1;F!==R&&_[F]<=l;++F)T+=o;--F;var G=(l-_[F])/(_[F+1]-_[F]),w=T+G*o,k=g(w,d,E);return k>=s?C(l,w,d,E):k===0?w:m(l,T,T+o,d,E)}},t}(),pooling=function(){function t(e){return e.concat(createSizedArray(e.length))}return{double:t}}(),poolFactory=function(){return function(t,e,r){var i=0,s=t,n=createSizedArray(s),a={newElement:h,release:o};function h(){var p;return i?(i-=1,p=n[i]):p=e(),p}function o(p){i===s&&(n=pooling.double(n),s*=2),r&&r(p),n[i]=p,i+=1}return a}}(),bezierLengthPool=function(){function t(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}}return poolFactory(8,t)}(),segmentsLengthPool=function(){function t(){return{lengths:[],totalLength:0}}function e(r){var i,s=r.lengths.length;for(i=0;i<s;i+=1)bezierLengthPool.release(r.lengths[i]);r.lengths.length=0}return poolFactory(8,t,e)}();function bezFunction(){var t=Math;function e(f,b,g,m,C,u){var c=f*m+b*C+g*u-C*m-u*f-g*b;return c>-.001&&c<.001}function r(f,b,g,m,C,u,c,l,d){if(g===0&&u===0&&d===0)return e(f,b,m,C,c,l);var E=t.sqrt(t.pow(m-f,2)+t.pow(C-b,2)+t.pow(u-g,2)),_=t.sqrt(t.pow(c-f,2)+t.pow(l-b,2)+t.pow(d-g,2)),T=t.sqrt(t.pow(c-m,2)+t.pow(l-C,2)+t.pow(d-u,2)),F;return E>_?E>T?F=E-_-T:F=T-_-E:T>_?F=T-_-E:F=_-E-T,F>-1e-4&&F<1e-4}var i=function(){return function(f,b,g,m){var C=getDefaultCurveSegments(),u,c,l,d,E,_=0,T,F=[],R=[],G=bezierLengthPool.newElement();for(l=g.length,u=0;u<C;u+=1){for(E=u/(C-1),T=0,c=0;c<l;c+=1)d=bmPow(1-E,3)*f[c]+3*bmPow(1-E,2)*E*g[c]+3*(1-E)*bmPow(E,2)*m[c]+bmPow(E,3)*b[c],F[c]=d,R[c]!==null&&(T+=bmPow(F[c]-R[c],2)),R[c]=F[c];T&&(T=bmSqrt(T),_+=T),G.percents[u]=E,G.lengths[u]=_}return G.addedLength=_,G}}();function s(f){var b=segmentsLengthPool.newElement(),g=f.c,m=f.v,C=f.o,u=f.i,c,l=f._length,d=b.lengths,E=0;for(c=0;c<l-1;c+=1)d[c]=i(m[c],m[c+1],C[c],u[c+1]),E+=d[c].addedLength;return g&&l&&(d[c]=i(m[c],m[0],C[c],u[0]),E+=d[c].addedLength),b.totalLength=E,b}function n(f){this.segmentLength=0,this.points=new Array(f)}function a(f,b){this.partialLength=f,this.point=b}var h=function(){var f={};return function(b,g,m,C){var u=(b[0]+"_"+b[1]+"_"+g[0]+"_"+g[1]+"_"+m[0]+"_"+m[1]+"_"+C[0]+"_"+C[1]).replace(/\./g,"p");if(!f[u]){var c=getDefaultCurveSegments(),l,d,E,_,T,F=0,R,G,w=null;b.length===2&&(b[0]!==g[0]||b[1]!==g[1])&&e(b[0],b[1],g[0],g[1],b[0]+m[0],b[1]+m[1])&&e(b[0],b[1],g[0],g[1],g[0]+C[0],g[1]+C[1])&&(c=2);var k=new n(c);for(E=m.length,l=0;l<c;l+=1){for(G=createSizedArray(E),T=l/(c-1),R=0,d=0;d<E;d+=1)_=bmPow(1-T,3)*b[d]+3*bmPow(1-T,2)*T*(b[d]+m[d])+3*(1-T)*bmPow(T,2)*(g[d]+C[d])+bmPow(T,3)*g[d],G[d]=_,w!==null&&(R+=bmPow(G[d]-w[d],2));R=bmSqrt(R),F+=R,k.points[l]=new a(R,G),w=G}k.segmentLength=F,f[u]=k}return f[u]}}();function o(f,b){var g=b.percents,m=b.lengths,C=g.length,u=bmFloor((C-1)*f),c=f*b.addedLength,l=0;if(u===C-1||u===0||c===m[u])return g[u];for(var d=m[u]>c?-1:1,E=!0;E;)if(m[u]<=c&&m[u+1]>c?(l=(c-m[u])/(m[u+1]-m[u]),E=!1):u+=d,u<0||u>=C-1){if(u===C-1)return g[u];E=!1}return g[u]+(g[u+1]-g[u])*l}function p(f,b,g,m,C,u){var c=o(C,u),l=1-c,d=t.round((l*l*l*f[0]+(c*l*l+l*c*l+l*l*c)*g[0]+(c*c*l+l*c*c+c*l*c)*m[0]+c*c*c*b[0])*1e3)/1e3,E=t.round((l*l*l*f[1]+(c*l*l+l*c*l+l*l*c)*g[1]+(c*c*l+l*c*c+c*l*c)*m[1]+c*c*c*b[1])*1e3)/1e3;return[d,E]}var y=createTypedArray("float32",8);function P(f,b,g,m,C,u,c){C<0?C=0:C>1&&(C=1);var l=o(C,c);u=u>1?1:u;var d=o(u,c),E,_=f.length,T=1-l,F=1-d,R=T*T*T,G=l*T*T*3,w=l*l*T*3,k=l*l*l,D=T*T*F,V=l*T*F+T*l*F+T*T*d,L=l*l*F+T*l*d+l*T*d,x=l*l*d,A=T*F*F,v=l*F*F+T*d*F+T*F*d,S=l*d*F+T*d*d+l*F*d,I=l*d*d,M=F*F*F,B=d*F*F+F*d*F+F*F*d,N=d*d*F+F*d*d+d*F*d,H=d*d*d;for(E=0;E<_;E+=1)y[E*4]=t.round((R*f[E]+G*g[E]+w*m[E]+k*b[E])*1e3)/1e3,y[E*4+1]=t.round((D*f[E]+V*g[E]+L*m[E]+x*b[E])*1e3)/1e3,y[E*4+2]=t.round((A*f[E]+v*g[E]+S*m[E]+I*b[E])*1e3)/1e3,y[E*4+3]=t.round((M*f[E]+B*g[E]+N*m[E]+H*b[E])*1e3)/1e3;return y}return{getSegmentsLength:s,getNewSegment:P,getPointInSegment:p,buildBezierData:h,pointOnLine2D:e,pointOnLine3D:r}}var bez=bezFunction(),initFrame=initialDefaultFrame,mathAbs=Math.abs;function interpolateValue(t,e){var r=this.offsetTime,i;this.propType==="multidimensional"&&(i=createTypedArray("float32",this.pv.length));for(var s=e.lastIndex,n=s,a=this.keyframes.length-1,h=!0,o,p,y;h;){if(o=this.keyframes[n],p=this.keyframes[n+1],n===a-1&&t>=p.t-r){o.h&&(o=p),s=0;break}if(p.t-r>t){s=n;break}n<a-1?n+=1:(s=0,h=!1)}y=this.keyframesMetadata[n]||{};var P,f,b,g,m,C,u=p.t-r,c=o.t-r,l;if(o.to){y.bezierData||(y.bezierData=bez.buildBezierData(o.s,p.s||o.e,o.to,o.ti));var d=y.bezierData;if(t>=u||t<c){var E=t>=u?d.points.length-1:0;for(f=d.points[E].point.length,P=0;P<f;P+=1)i[P]=d.points[E].point[P]}else{y.__fnct?C=y.__fnct:(C=BezierFactory.getBezierEasing(o.o.x,o.o.y,o.i.x,o.i.y,o.n).get,y.__fnct=C),b=C((t-c)/(u-c));var _=d.segmentLength*b,T,F=e.lastFrame<t&&e._lastKeyframeIndex===n?e._lastAddedLength:0;for(m=e.lastFrame<t&&e._lastKeyframeIndex===n?e._lastPoint:0,h=!0,g=d.points.length;h;){if(F+=d.points[m].partialLength,_===0||b===0||m===d.points.length-1){for(f=d.points[m].point.length,P=0;P<f;P+=1)i[P]=d.points[m].point[P];break}else if(_>=F&&_<F+d.points[m+1].partialLength){for(T=(_-F)/d.points[m+1].partialLength,f=d.points[m].point.length,P=0;P<f;P+=1)i[P]=d.points[m].point[P]+(d.points[m+1].point[P]-d.points[m].point[P])*T;break}m<g-1?m+=1:h=!1}e._lastPoint=m,e._lastAddedLength=F-d.points[m].partialLength,e._lastKeyframeIndex=n}}else{var R,G,w,k,D;if(a=o.s.length,l=p.s||o.e,this.sh&&o.h!==1)if(t>=u)i[0]=l[0],i[1]=l[1],i[2]=l[2];else if(t<=c)i[0]=o.s[0],i[1]=o.s[1],i[2]=o.s[2];else{var V=createQuaternion(o.s),L=createQuaternion(l),x=(t-c)/(u-c);quaternionToEuler(i,slerp(V,L,x))}else for(n=0;n<a;n+=1)o.h!==1&&(t>=u?b=1:t<c?b=0:(o.o.x.constructor===Array?(y.__fnct||(y.__fnct=[]),y.__fnct[n]?C=y.__fnct[n]:(R=o.o.x[n]===void 0?o.o.x[0]:o.o.x[n],G=o.o.y[n]===void 0?o.o.y[0]:o.o.y[n],w=o.i.x[n]===void 0?o.i.x[0]:o.i.x[n],k=o.i.y[n]===void 0?o.i.y[0]:o.i.y[n],C=BezierFactory.getBezierEasing(R,G,w,k).get,y.__fnct[n]=C)):y.__fnct?C=y.__fnct:(R=o.o.x,G=o.o.y,w=o.i.x,k=o.i.y,C=BezierFactory.getBezierEasing(R,G,w,k).get,o.keyframeMetadata=C),b=C((t-c)/(u-c)))),l=p.s||o.e,D=o.h===1?o.s[n]:o.s[n]+(l[n]-o.s[n])*b,this.propType==="multidimensional"?i[n]=D:i=D}return e.lastIndex=s,i}function slerp(t,e,r){var i=[],s=t[0],n=t[1],a=t[2],h=t[3],o=e[0],p=e[1],y=e[2],P=e[3],f,b,g,m,C;return b=s*o+n*p+a*y+h*P,b<0&&(b=-b,o=-o,p=-p,y=-y,P=-P),1-b>1e-6?(f=Math.acos(b),g=Math.sin(f),m=Math.sin((1-r)*f)/g,C=Math.sin(r*f)/g):(m=1-r,C=r),i[0]=m*s+C*o,i[1]=m*n+C*p,i[2]=m*a+C*y,i[3]=m*h+C*P,i}function quaternionToEuler(t,e){var r=e[0],i=e[1],s=e[2],n=e[3],a=Math.atan2(2*i*n-2*r*s,1-2*i*i-2*s*s),h=Math.asin(2*r*i+2*s*n),o=Math.atan2(2*r*n-2*i*s,1-2*r*r-2*s*s);t[0]=a/degToRads,t[1]=h/degToRads,t[2]=o/degToRads}function createQuaternion(t){var e=t[0]*degToRads,r=t[1]*degToRads,i=t[2]*degToRads,s=Math.cos(e/2),n=Math.cos(r/2),a=Math.cos(i/2),h=Math.sin(e/2),o=Math.sin(r/2),p=Math.sin(i/2),y=s*n*a-h*o*p,P=h*o*a+s*n*p,f=h*n*a+s*o*p,b=s*o*a-h*n*p;return[P,f,b,y]}function getValueAtCurrentTime(){var t=this.comp.renderedFrame-this.offsetTime,e=this.keyframes[0].t-this.offsetTime,r=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(t===this._caching.lastFrame||this._caching.lastFrame!==initFrame&&(this._caching.lastFrame>=r&&t>=r||this._caching.lastFrame<e&&t<e))){this._caching.lastFrame>=t&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var i=this.interpolateValue(t,this._caching);this.pv=i}return this._caching.lastFrame=t,this.pv}function setVValue(t){var e;if(this.propType==="unidimensional")e=t*this.mult,mathAbs(this.v-e)>1e-5&&(this.v=e,this._mdf=!0);else for(var r=0,i=this.v.length;r<i;)e=t[r]*this.mult,mathAbs(this.v[r]-e)>1e-5&&(this.v[r]=e,this._mdf=!0),r+=1}function processEffectsSequence(){if(!(this.elem.globalData.frameId===this.frameId||!this.effectsSequence.length)){if(this.lock){this.setVValue(this.pv);return}this.lock=!0,this._mdf=this._isFirstFrame;var t,e=this.effectsSequence.length,r=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)r=this.effectsSequence[t](r);this.setVValue(r),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function addEffect(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function ValueProperty(t,e,r,i){this.propType="unidimensional",this.mult=r||1,this.data=e,this.v=r?e.k*r:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function MultiDimensionalProperty(t,e,r,i){this.propType="multidimensional",this.mult=r||1,this.data=e,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var s,n=e.k.length;for(this.v=createTypedArray("float32",n),this.pv=createTypedArray("float32",n),this.vel=createTypedArray("float32",n),s=0;s<n;s+=1)this.v[s]=e.k[s]*this.mult,this.pv[s]=e.k[s];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=processEffectsSequence,this.setVValue=setVValue,this.addEffect=addEffect}function KeyframedValueProperty(t,e,r,i){this.propType="unidimensional",this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.frameId=-1,this._caching={lastFrame:initFrame,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=e,this.mult=r||1,this.elem=t,this.container=i,this.comp=t.comp,this.v=initFrame,this.pv=initFrame,this._isFirstFrame=!0,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.addEffect=addEffect}function KeyframedMultidimensionalProperty(t,e,r,i){this.propType="multidimensional";var s,n=e.k.length,a,h,o,p;for(s=0;s<n-1;s+=1)e.k[s].to&&e.k[s].s&&e.k[s+1]&&e.k[s+1].s&&(a=e.k[s].s,h=e.k[s+1].s,o=e.k[s].to,p=e.k[s].ti,(a.length===2&&!(a[0]===h[0]&&a[1]===h[1])&&bez.pointOnLine2D(a[0],a[1],h[0],h[1],a[0]+o[0],a[1]+o[1])&&bez.pointOnLine2D(a[0],a[1],h[0],h[1],h[0]+p[0],h[1]+p[1])||a.length===3&&!(a[0]===h[0]&&a[1]===h[1]&&a[2]===h[2])&&bez.pointOnLine3D(a[0],a[1],a[2],h[0],h[1],h[2],a[0]+o[0],a[1]+o[1],a[2]+o[2])&&bez.pointOnLine3D(a[0],a[1],a[2],h[0],h[1],h[2],h[0]+p[0],h[1]+p[1],h[2]+p[2]))&&(e.k[s].to=null,e.k[s].ti=null),a[0]===h[0]&&a[1]===h[1]&&o[0]===0&&o[1]===0&&p[0]===0&&p[1]===0&&(a.length===2||a[2]===h[2]&&o[2]===0&&p[2]===0)&&(e.k[s].to=null,e.k[s].ti=null));this.effectsSequence=[getValueAtCurrentTime.bind(this)],this.data=e,this.keyframes=e.k,this.keyframesMetadata=[],this.offsetTime=t.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=r||1,this.elem=t,this.container=i,this.comp=t.comp,this.getValue=processEffectsSequence,this.setVValue=setVValue,this.interpolateValue=interpolateValue,this.frameId=-1;var y=e.k[0].s.length;for(this.v=createTypedArray("float32",y),this.pv=createTypedArray("float32",y),s=0;s<y;s+=1)this.v[s]=initFrame,this.pv[s]=initFrame;this._caching={lastFrame:initFrame,lastIndex:0,value:createTypedArray("float32",y)},this.addEffect=addEffect}var PropertyFactory=function(){function t(r,i,s,n,a){i.sid&&(i=r.globalData.slotManager.getProp(i));var h;if(!i.k.length)h=new ValueProperty(r,i,n,a);else if(typeof i.k[0]=="number")h=new MultiDimensionalProperty(r,i,n,a);else switch(s){case 0:h=new KeyframedValueProperty(r,i,n,a);break;case 1:h=new KeyframedMultidimensionalProperty(r,i,n,a);break;default:break}return h.effectsSequence.length&&a.addDynamicProperty(h),h}var e={getProp:t};return e}();function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(e){this.dynamicProperties.indexOf(e)===-1&&(this.dynamicProperties.push(e),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){this._mdf=!1;var e,r=this.dynamicProperties.length;for(e=0;e<r;e+=1)this.dynamicProperties[e].getValue(),this.dynamicProperties[e]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(e){this.container=e,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=function(){function t(){return createTypedArray("float32",2)}return poolFactory(8,t)}();function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var r=0;r<e;)this.v[r]=pointPool.newElement(),this.o[r]=pointPool.newElement(),this.i[r]=pointPool.newElement(),r+=1},ShapePath.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(t,e,r,i,s){var n;switch(this._length=Math.max(this._length,i+1),this._length>=this._maxLength&&this.doubleArrayLength(),r){case"v":n=this.v;break;case"i":n=this.i;break;case"o":n=this.o;break;default:n=[];break}(!n[i]||n[i]&&!s)&&(n[i]=pointPool.newElement()),n[i][0]=t,n[i][1]=e},ShapePath.prototype.setTripleAt=function(t,e,r,i,s,n,a,h){this.setXYAt(t,e,"v",a,h),this.setXYAt(r,i,"o",a,h),this.setXYAt(s,n,"i",a,h)},ShapePath.prototype.reverse=function(){var t=new ShapePath;t.setPathData(this.c,this._length);var e=this.v,r=this.o,i=this.i,s=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],i[0][0],i[0][1],r[0][0],r[0][1],0,!1),s=1);var n=this._length-1,a=this._length,h;for(h=s;h<a;h+=1)t.setTripleAt(e[n][0],e[n][1],i[n][0],i[n][1],r[n][0],r[n][1],h,!1),n-=1;return t},ShapePath.prototype.length=function(){return this._length};var shapePool=function(){function t(){return new ShapePath}function e(s){var n=s._length,a;for(a=0;a<n;a+=1)pointPool.release(s.v[a]),pointPool.release(s.i[a]),pointPool.release(s.o[a]),s.v[a]=null,s.i[a]=null,s.o[a]=null;s._length=0,s.c=!1}function r(s){var n=i.newElement(),a,h=s._length===void 0?s.v.length:s._length;for(n.setLength(h),n.c=s.c,a=0;a<h;a+=1)n.setTripleAt(s.v[a][0],s.v[a][1],s.o[a][0],s.o[a][1],s.i[a][0],s.i[a][1],a);return n}var i=poolFactory(4,t,e);return i.clone=r,i}();function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)shapePool.release(this.shapes[t]);this._length=0};var shapeCollectionPool=function(){var t={newShapeCollection:s,release:n},e=0,r=4,i=createSizedArray(r);function s(){var a;return e?(e-=1,a=i[e]):a=new ShapeCollection,a}function n(a){var h,o=a._length;for(h=0;h<o;h+=1)shapePool.release(a.shapes[h]);a._length=0,e===r&&(i=pooling.double(i),r*=2),i[e]=a,e+=1}return t}(),ShapePropertyFactory=function(){var t=-999999;function e(u,c,l){var d=l.lastIndex,E,_,T,F,R,G,w,k,D,V=this.keyframes;if(u<V[0].t-this.offsetTime)E=V[0].s[0],T=!0,d=0;else if(u>=V[V.length-1].t-this.offsetTime)E=V[V.length-1].s?V[V.length-1].s[0]:V[V.length-2].e[0],T=!0;else{for(var L=d,x=V.length-1,A=!0,v,S,I;A&&(v=V[L],S=V[L+1],!(S.t-this.offsetTime>u));)L<x-1?L+=1:A=!1;if(I=this.keyframesMetadata[L]||{},T=v.h===1,d=L,!T){if(u>=S.t-this.offsetTime)k=1;else if(u<v.t-this.offsetTime)k=0;else{var M;I.__fnct?M=I.__fnct:(M=BezierFactory.getBezierEasing(v.o.x,v.o.y,v.i.x,v.i.y).get,I.__fnct=M),k=M((u-(v.t-this.offsetTime))/(S.t-this.offsetTime-(v.t-this.offsetTime)))}_=S.s?S.s[0]:v.e[0]}E=v.s[0]}for(G=c._length,w=E.i[0].length,l.lastIndex=d,F=0;F<G;F+=1)for(R=0;R<w;R+=1)D=T?E.i[F][R]:E.i[F][R]+(_.i[F][R]-E.i[F][R])*k,c.i[F][R]=D,D=T?E.o[F][R]:E.o[F][R]+(_.o[F][R]-E.o[F][R])*k,c.o[F][R]=D,D=T?E.v[F][R]:E.v[F][R]+(_.v[F][R]-E.v[F][R])*k,c.v[F][R]=D}function r(){var u=this.comp.renderedFrame-this.offsetTime,c=this.keyframes[0].t-this.offsetTime,l=this.keyframes[this.keyframes.length-1].t-this.offsetTime,d=this._caching.lastFrame;return d!==t&&(d<c&&u<c||d>l&&u>l)||(this._caching.lastIndex=d<u?this._caching.lastIndex:0,this.interpolateShape(u,this.pv,this._caching)),this._caching.lastFrame=u,this.pv}function i(){this.paths=this.localShapeCollection}function s(u,c){if(u._length!==c._length||u.c!==c.c)return!1;var l,d=u._length;for(l=0;l<d;l+=1)if(u.v[l][0]!==c.v[l][0]||u.v[l][1]!==c.v[l][1]||u.o[l][0]!==c.o[l][0]||u.o[l][1]!==c.o[l][1]||u.i[l][0]!==c.i[l][0]||u.i[l][1]!==c.i[l][1])return!1;return!0}function n(u){s(this.v,u)||(this.v=shapePool.clone(u),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function a(){if(this.elem.globalData.frameId!==this.frameId){if(!this.effectsSequence.length){this._mdf=!1;return}if(this.lock){this.setVValue(this.pv);return}this.lock=!0,this._mdf=!1;var u;this.kf?u=this.pv:this.data.ks?u=this.data.ks.k:u=this.data.pt.k;var c,l=this.effectsSequence.length;for(c=0;c<l;c+=1)u=this.effectsSequence[c](u);this.setVValue(u),this.lock=!1,this.frameId=this.elem.globalData.frameId}}function h(u,c,l){this.propType="shape",this.comp=u.comp,this.container=u,this.elem=u,this.data=c,this.k=!1,this.kf=!1,this._mdf=!1;var d=l===3?c.pt.k:c.ks.k;this.v=shapePool.clone(d),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=i,this.effectsSequence=[]}function o(u){this.effectsSequence.push(u),this.container.addDynamicProperty(this)}h.prototype.interpolateShape=e,h.prototype.getValue=a,h.prototype.setVValue=n,h.prototype.addEffect=o;function p(u,c,l){this.propType="shape",this.comp=u.comp,this.elem=u,this.container=u,this.offsetTime=u.data.st,this.keyframes=l===3?c.pt.k:c.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var d=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,d),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=i,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[r.bind(this)]}p.prototype.getValue=a,p.prototype.interpolateShape=e,p.prototype.setVValue=n,p.prototype.addEffect=o;var y=function(){var u=roundCorner;function c(l,d){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=d.d,this.elem=l,this.comp=l.comp,this.frameId=-1,this.initDynamicPropertyContainer(l),this.p=PropertyFactory.getProp(l,d.p,1,0,this),this.s=PropertyFactory.getProp(l,d.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return c.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var d=this.p.v[0],E=this.p.v[1],_=this.s.v[0]/2,T=this.s.v[1]/2,F=this.d!==3,R=this.v;R.v[0][0]=d,R.v[0][1]=E-T,R.v[1][0]=F?d+_:d-_,R.v[1][1]=E,R.v[2][0]=d,R.v[2][1]=E+T,R.v[3][0]=F?d-_:d+_,R.v[3][1]=E,R.i[0][0]=F?d-_*u:d+_*u,R.i[0][1]=E-T,R.i[1][0]=F?d+_:d-_,R.i[1][1]=E-T*u,R.i[2][0]=F?d+_*u:d-_*u,R.i[2][1]=E+T,R.i[3][0]=F?d-_:d+_,R.i[3][1]=E+T*u,R.o[0][0]=F?d+_*u:d-_*u,R.o[0][1]=E-T,R.o[1][0]=F?d+_:d-_,R.o[1][1]=E+T*u,R.o[2][0]=F?d-_*u:d+_*u,R.o[2][1]=E+T,R.o[3][0]=F?d-_:d+_,R.o[3][1]=E-T*u}},extendPrototype([DynamicPropertyContainer],c),c}(),P=function(){function u(c,l){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=c,this.comp=c.comp,this.data=l,this.frameId=-1,this.d=l.d,this.initDynamicPropertyContainer(c),l.sy===1?(this.ir=PropertyFactory.getProp(c,l.ir,0,0,this),this.is=PropertyFactory.getProp(c,l.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(c,l.pt,0,0,this),this.p=PropertyFactory.getProp(c,l.p,1,0,this),this.r=PropertyFactory.getProp(c,l.r,0,degToRads,this),this.or=PropertyFactory.getProp(c,l.or,0,0,this),this.os=PropertyFactory.getProp(c,l.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return u.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var l=Math.floor(this.pt.v)*2,d=Math.PI*2/l,E=!0,_=this.or.v,T=this.ir.v,F=this.os.v,R=this.is.v,G=2*Math.PI*_/(l*2),w=2*Math.PI*T/(l*2),k,D,V,L,x=-Math.PI/2;x+=this.r.v;var A=this.data.d===3?-1:1;for(this.v._length=0,k=0;k<l;k+=1){D=E?_:T,V=E?F:R,L=E?G:w;var v=D*Math.cos(x),S=D*Math.sin(x),I=v===0&&S===0?0:S/Math.sqrt(v*v+S*S),M=v===0&&S===0?0:-v/Math.sqrt(v*v+S*S);v+=+this.p.v[0],S+=+this.p.v[1],this.v.setTripleAt(v,S,v-I*L*V*A,S-M*L*V*A,v+I*L*V*A,S+M*L*V*A,k,!0),E=!E,x+=d*A}},convertPolygonToPath:function(){var l=Math.floor(this.pt.v),d=Math.PI*2/l,E=this.or.v,_=this.os.v,T=2*Math.PI*E/(l*4),F,R=-Math.PI*.5,G=this.data.d===3?-1:1;for(R+=this.r.v,this.v._length=0,F=0;F<l;F+=1){var w=E*Math.cos(R),k=E*Math.sin(R),D=w===0&&k===0?0:k/Math.sqrt(w*w+k*k),V=w===0&&k===0?0:-w/Math.sqrt(w*w+k*k);w+=+this.p.v[0],k+=+this.p.v[1],this.v.setTripleAt(w,k,w-D*T*_*G,k-V*T*_*G,w+D*T*_*G,k+V*T*_*G,F,!0),R+=d*G}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],u),u}(),f=function(){function u(c,l){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=c,this.comp=c.comp,this.frameId=-1,this.d=l.d,this.initDynamicPropertyContainer(c),this.p=PropertyFactory.getProp(c,l.p,1,0,this),this.s=PropertyFactory.getProp(c,l.s,1,0,this),this.r=PropertyFactory.getProp(c,l.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return u.prototype={convertRectToPath:function(){var l=this.p.v[0],d=this.p.v[1],E=this.s.v[0]/2,_=this.s.v[1]/2,T=bmMin(E,_,this.r.v),F=T*(1-roundCorner);this.v._length=0,this.d===2||this.d===1?(this.v.setTripleAt(l+E,d-_+T,l+E,d-_+T,l+E,d-_+F,0,!0),this.v.setTripleAt(l+E,d+_-T,l+E,d+_-F,l+E,d+_-T,1,!0),T!==0?(this.v.setTripleAt(l+E-T,d+_,l+E-T,d+_,l+E-F,d+_,2,!0),this.v.setTripleAt(l-E+T,d+_,l-E+F,d+_,l-E+T,d+_,3,!0),this.v.setTripleAt(l-E,d+_-T,l-E,d+_-T,l-E,d+_-F,4,!0),this.v.setTripleAt(l-E,d-_+T,l-E,d-_+F,l-E,d-_+T,5,!0),this.v.setTripleAt(l-E+T,d-_,l-E+T,d-_,l-E+F,d-_,6,!0),this.v.setTripleAt(l+E-T,d-_,l+E-F,d-_,l+E-T,d-_,7,!0)):(this.v.setTripleAt(l-E,d+_,l-E+F,d+_,l-E,d+_,2),this.v.setTripleAt(l-E,d-_,l-E,d-_+F,l-E,d-_,3))):(this.v.setTripleAt(l+E,d-_+T,l+E,d-_+F,l+E,d-_+T,0,!0),T!==0?(this.v.setTripleAt(l+E-T,d-_,l+E-T,d-_,l+E-F,d-_,1,!0),this.v.setTripleAt(l-E+T,d-_,l-E+F,d-_,l-E+T,d-_,2,!0),this.v.setTripleAt(l-E,d-_+T,l-E,d-_+T,l-E,d-_+F,3,!0),this.v.setTripleAt(l-E,d+_-T,l-E,d+_-F,l-E,d+_-T,4,!0),this.v.setTripleAt(l-E+T,d+_,l-E+T,d+_,l-E+F,d+_,5,!0),this.v.setTripleAt(l+E-T,d+_,l+E-F,d+_,l+E-T,d+_,6,!0),this.v.setTripleAt(l+E,d+_-T,l+E,d+_-T,l+E,d+_-F,7,!0)):(this.v.setTripleAt(l-E,d-_,l-E+F,d-_,l-E,d-_,1,!0),this.v.setTripleAt(l-E,d+_,l-E,d+_-F,l-E,d+_,2,!0),this.v.setTripleAt(l+E,d+_,l+E-F,d+_,l+E,d+_,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:i},extendPrototype([DynamicPropertyContainer],u),u}();function b(u,c,l){var d;if(l===3||l===4){var E=l===3?c.pt:c.ks,_=E.k;_.length?d=new p(u,c,l):d=new h(u,c,l)}else l===5?d=new f(u,c):l===6?d=new y(u,c):l===7&&(d=new P(u,c));return d.k&&u.addDynamicProperty(d),d}function g(){return h}function m(){return p}var C={};return C.getShapeProp=b,C.getConstructorFunction=g,C.getKeyframedConstructorFunction=m,C}();var Matrix=function(){var t=Math.cos,e=Math.sin,r=Math.tan,i=Math.round;function s(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function n(v){if(v===0)return this;var S=t(v),I=e(v);return this._t(S,-I,0,0,I,S,0,0,0,0,1,0,0,0,0,1)}function a(v){if(v===0)return this;var S=t(v),I=e(v);return this._t(1,0,0,0,0,S,-I,0,0,I,S,0,0,0,0,1)}function h(v){if(v===0)return this;var S=t(v),I=e(v);return this._t(S,0,I,0,0,1,0,0,-I,0,S,0,0,0,0,1)}function o(v){if(v===0)return this;var S=t(v),I=e(v);return this._t(S,-I,0,0,I,S,0,0,0,0,1,0,0,0,0,1)}function p(v,S){return this._t(1,S,v,1,0,0)}function y(v,S){return this.shear(r(v),r(S))}function P(v,S){var I=t(S),M=e(S);return this._t(I,M,0,0,-M,I,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,r(v),1,0,0,0,0,1,0,0,0,0,1)._t(I,-M,0,0,M,I,0,0,0,0,1,0,0,0,0,1)}function f(v,S,I){return!I&&I!==0&&(I=1),v===1&&S===1&&I===1?this:this._t(v,0,0,0,0,S,0,0,0,0,I,0,0,0,0,1)}function b(v,S,I,M,B,N,H,z,W,Y,Z,tt,q,K,Q,j){return this.props[0]=v,this.props[1]=S,this.props[2]=I,this.props[3]=M,this.props[4]=B,this.props[5]=N,this.props[6]=H,this.props[7]=z,this.props[8]=W,this.props[9]=Y,this.props[10]=Z,this.props[11]=tt,this.props[12]=q,this.props[13]=K,this.props[14]=Q,this.props[15]=j,this}function g(v,S,I){return I=I||0,v!==0||S!==0||I!==0?this._t(1,0,0,0,0,1,0,0,0,0,1,0,v,S,I,1):this}function m(v,S,I,M,B,N,H,z,W,Y,Z,tt,q,K,Q,j){var O=this.props;if(v===1&&S===0&&I===0&&M===0&&B===0&&N===1&&H===0&&z===0&&W===0&&Y===0&&Z===1&&tt===0)return O[12]=O[12]*v+O[15]*q,O[13]=O[13]*N+O[15]*K,O[14]=O[14]*Z+O[15]*Q,O[15]*=j,this._identityCalculated=!1,this;var at=O[0],ft=O[1],ot=O[2],st=O[3],ht=O[4],lt=O[5],et=O[6],pt=O[7],ut=O[8],rt=O[9],ct=O[10],it=O[11],mt=O[12],J=O[13],$=O[14],X=O[15];return O[0]=at*v+ft*B+ot*W+st*q,O[1]=at*S+ft*N+ot*Y+st*K,O[2]=at*I+ft*H+ot*Z+st*Q,O[3]=at*M+ft*z+ot*tt+st*j,O[4]=ht*v+lt*B+et*W+pt*q,O[5]=ht*S+lt*N+et*Y+pt*K,O[6]=ht*I+lt*H+et*Z+pt*Q,O[7]=ht*M+lt*z+et*tt+pt*j,O[8]=ut*v+rt*B+ct*W+it*q,O[9]=ut*S+rt*N+ct*Y+it*K,O[10]=ut*I+rt*H+ct*Z+it*Q,O[11]=ut*M+rt*z+ct*tt+it*j,O[12]=mt*v+J*B+$*W+X*q,O[13]=mt*S+J*N+$*Y+X*K,O[14]=mt*I+J*H+$*Z+X*Q,O[15]=mt*M+J*z+$*tt+X*j,this._identityCalculated=!1,this}function C(v){var S=v.props;return this.transform(S[0],S[1],S[2],S[3],S[4],S[5],S[6],S[7],S[8],S[9],S[10],S[11],S[12],S[13],S[14],S[15])}function u(){return this._identityCalculated||(this._identity=!(this.props[0]!==1||this.props[1]!==0||this.props[2]!==0||this.props[3]!==0||this.props[4]!==0||this.props[5]!==1||this.props[6]!==0||this.props[7]!==0||this.props[8]!==0||this.props[9]!==0||this.props[10]!==1||this.props[11]!==0||this.props[12]!==0||this.props[13]!==0||this.props[14]!==0||this.props[15]!==1),this._identityCalculated=!0),this._identity}function c(v){for(var S=0;S<16;){if(v.props[S]!==this.props[S])return!1;S+=1}return!0}function l(v){var S;for(S=0;S<16;S+=1)v.props[S]=this.props[S];return v}function d(v){var S;for(S=0;S<16;S+=1)this.props[S]=v[S]}function E(v,S,I){return{x:v*this.props[0]+S*this.props[4]+I*this.props[8]+this.props[12],y:v*this.props[1]+S*this.props[5]+I*this.props[9]+this.props[13],z:v*this.props[2]+S*this.props[6]+I*this.props[10]+this.props[14]}}function _(v,S,I){return v*this.props[0]+S*this.props[4]+I*this.props[8]+this.props[12]}function T(v,S,I){return v*this.props[1]+S*this.props[5]+I*this.props[9]+this.props[13]}function F(v,S,I){return v*this.props[2]+S*this.props[6]+I*this.props[10]+this.props[14]}function R(){var v=this.props[0]*this.props[5]-this.props[1]*this.props[4],S=this.props[5]/v,I=-this.props[1]/v,M=-this.props[4]/v,B=this.props[0]/v,N=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/v,H=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/v,z=new Matrix;return z.props[0]=S,z.props[1]=I,z.props[4]=M,z.props[5]=B,z.props[12]=N,z.props[13]=H,z}function G(v){var S=this.getInverseMatrix();return S.applyToPointArray(v[0],v[1],v[2]||0)}function w(v){var S,I=v.length,M=[];for(S=0;S<I;S+=1)M[S]=G(v[S]);return M}function k(v,S,I){var M=createTypedArray("float32",6);if(this.isIdentity())M[0]=v[0],M[1]=v[1],M[2]=S[0],M[3]=S[1],M[4]=I[0],M[5]=I[1];else{var B=this.props[0],N=this.props[1],H=this.props[4],z=this.props[5],W=this.props[12],Y=this.props[13];M[0]=v[0]*B+v[1]*H+W,M[1]=v[0]*N+v[1]*z+Y,M[2]=S[0]*B+S[1]*H+W,M[3]=S[0]*N+S[1]*z+Y,M[4]=I[0]*B+I[1]*H+W,M[5]=I[0]*N+I[1]*z+Y}return M}function D(v,S,I){var M;return this.isIdentity()?M=[v,S,I]:M=[v*this.props[0]+S*this.props[4]+I*this.props[8]+this.props[12],v*this.props[1]+S*this.props[5]+I*this.props[9]+this.props[13],v*this.props[2]+S*this.props[6]+I*this.props[10]+this.props[14]],M}function V(v,S){if(this.isIdentity())return v+","+S;var I=this.props;return Math.round((v*I[0]+S*I[4]+I[12])*100)/100+","+Math.round((v*I[1]+S*I[5]+I[13])*100)/100}function L(){for(var v=0,S=this.props,I="matrix3d(",M=1e4;v<16;)I+=i(S[v]*M)/M,I+=v===15?")":",",v+=1;return I}function x(v){var S=1e4;return v<1e-6&&v>0||v>-1e-6&&v<0?i(v*S)/S:v}function A(){var v=this.props,S=x(v[0]),I=x(v[1]),M=x(v[4]),B=x(v[5]),N=x(v[12]),H=x(v[13]);return"matrix("+S+","+I+","+M+","+B+","+N+","+H+")"}return function(){this.reset=s,this.rotate=n,this.rotateX=a,this.rotateY=h,this.rotateZ=o,this.skew=y,this.skewFromAxis=P,this.shear=p,this.scale=f,this.setTransform=b,this.translate=g,this.transform=m,this.multiply=C,this.applyToPoint=E,this.applyToX=_,this.applyToY=T,this.applyToZ=F,this.applyToPointArray=D,this.applyToTriplePoints=k,this.applyToPointStringified=V,this.toCSS=L,this.to2dCSS=A,this.clone=l,this.cloneFromProps=d,this.equals=c,this.inversePoints=w,this.inversePoint=G,this.getInverseMatrix=R,this._t=this.transform,this.isIdentity=u,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$3=function(r){return typeof r}:_typeof$3=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$3(t)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(t){setLocationHref(t)}function searchAnimations(){standalone===!0?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(t){setSubframeEnabled(t)}function setPrefix(t){setIdPrefix(t)}function loadAnimation(t){return standalone===!0&&(t.animationData=JSON.parse(animationData)),animationManager.loadAnimation(t)}function setQuality(t){if(typeof t=="string")switch(t){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10);break}else!isNaN(t)&&t>1&&setDefaultCurveSegments(t);getDefaultCurveSegments()>=50?roundValues(!1):roundValues(!0)}function inBrowser(){return typeof navigator<"u"}function installPlugin(t,e){t==="expressions"&&setExpressionsPlugin(e)}function getFactory(t){switch(t){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.12.2";function checkReady(){document.readyState==="complete"&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(t){for(var e=queryString.split("&"),r=0;r<e.length;r+=1){var i=e[r].split("=");if(decodeURIComponent(i[0])==t)return decodeURIComponent(i[1])}return null}var queryString="";if(standalone){var scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")}var readyStateCheckInterval=setInterval(checkReady,100);try{!((typeof exports>"u"?"undefined":_typeof$3(exports))==="object"&&typeof module<"u")&&!(typeof define=="function"&&define.amd)&&(self.bodymovin=lottie)}catch(t){}var ShapeModifiers=function(){var t={},e={};t.registerModifier=r,t.getModifier=i;function r(s,n){e[s]||(e[s]=n)}function i(s,n,a){return new e[s](n,a)}return t}();function ShapeModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},ShapeModifier.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier);function TrimModifier(){}extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(t,e){this.s=PropertyFactory.getProp(t,e.s,0,.01,this),this.e=PropertyFactory.getProp(t,e.e,0,.01,this),this.o=PropertyFactory.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(t){t.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(t,e,r,i,s){var n=[];e<=1?n.push({s:t,e}):t>=1?n.push({s:t-1,e:e-1}):(n.push({s:t,e:1}),n.push({s:0,e:e-1}));var a=[],h,o=n.length,p;for(h=0;h<o;h+=1)if(p=n[h],!(p.e*s<i||p.s*s>i+r)){var y,P;p.s*s<=i?y=0:y=(p.s*s-i)/r,p.e*s>=i+r?P=1:P=(p.e*s-i)/r,a.push([y,P])}return a.length||a.push([0,0]),a},TrimModifier.prototype.releasePathsData=function(t){var e,r=t.length;for(e=0;e<r;e+=1)segmentsLengthPool.release(t[e]);return t.length=0,t},TrimModifier.prototype.processShapes=function(t){var e,r;if(this._mdf||t){var i=this.o.v%360/360;if(i<0&&(i+=1),this.s.v>1?e=1+i:this.s.v<0?e=0+i:e=this.s.v+i,this.e.v>1?r=1+i:this.e.v<0?r=0+i:r=this.e.v+i,e>r){var s=e;e=r,r=s}e=Math.round(e*1e4)*1e-4,r=Math.round(r*1e4)*1e-4,this.sValue=e,this.eValue=r}else e=this.sValue,r=this.eValue;var n,a,h=this.shapes.length,o,p,y,P,f,b=0;if(r===e)for(a=0;a<h;a+=1)this.shapes[a].localShapeCollection.releaseShapes(),this.shapes[a].shape._mdf=!0,this.shapes[a].shape.paths=this.shapes[a].localShapeCollection,this._mdf&&(this.shapes[a].pathsData.length=0);else if(r===1&&e===0||r===0&&e===1){if(this._mdf)for(a=0;a<h;a+=1)this.shapes[a].pathsData.length=0,this.shapes[a].shape._mdf=!0}else{var g=[],m,C;for(a=0;a<h;a+=1)if(m=this.shapes[a],!m.shape._mdf&&!this._mdf&&!t&&this.m!==2)m.shape.paths=m.localShapeCollection;else{if(n=m.shape.paths,p=n._length,f=0,!m.shape._mdf&&m.pathsData.length)f=m.totalShapeLength;else{for(y=this.releasePathsData(m.pathsData),o=0;o<p;o+=1)P=bez.getSegmentsLength(n.shapes[o]),y.push(P),f+=P.totalLength;m.totalShapeLength=f,m.pathsData=y}b+=f,m.shape._mdf=!0}var u=e,c=r,l=0,d;for(a=h-1;a>=0;a-=1)if(m=this.shapes[a],m.shape._mdf){for(C=m.localShapeCollection,C.releaseShapes(),this.m===2&&h>1?(d=this.calculateShapeEdges(e,r,m.totalShapeLength,l,b),l+=m.totalShapeLength):d=[[u,c]],p=d.length,o=0;o<p;o+=1){u=d[o][0],c=d[o][1],g.length=0,c<=1?g.push({s:m.totalShapeLength*u,e:m.totalShapeLength*c}):u>=1?g.push({s:m.totalShapeLength*(u-1),e:m.totalShapeLength*(c-1)}):(g.push({s:m.totalShapeLength*u,e:m.totalShapeLength}),g.push({s:0,e:m.totalShapeLength*(c-1)}));var E=this.addShapes(m,g[0]);if(g[0].s!==g[0].e){if(g.length>1){var _=m.shape.paths.shapes[m.shape.paths._length-1];if(_.c){var T=E.pop();this.addPaths(E,C),E=this.addShapes(m,g[1],T)}else this.addPaths(E,C),E=this.addShapes(m,g[1])}this.addPaths(E,C)}}m.shape.paths=C}}},TrimModifier.prototype.addPaths=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)e.addShape(t[r])},TrimModifier.prototype.addSegment=function(t,e,r,i,s,n,a){s.setXYAt(e[0],e[1],"o",n),s.setXYAt(r[0],r[1],"i",n+1),a&&s.setXYAt(t[0],t[1],"v",n),s.setXYAt(i[0],i[1],"v",n+1)},TrimModifier.prototype.addSegmentFromArray=function(t,e,r,i){e.setXYAt(t[1],t[5],"o",r),e.setXYAt(t[2],t[6],"i",r+1),i&&e.setXYAt(t[0],t[4],"v",r),e.setXYAt(t[3],t[7],"v",r+1)},TrimModifier.prototype.addShapes=function(t,e,r){var i=t.pathsData,s=t.shape.paths.shapes,n,a=t.shape.paths._length,h,o,p=0,y,P,f,b,g=[],m,C=!0;for(r?(P=r._length,m=r._length):(r=shapePool.newElement(),P=0,m=0),g.push(r),n=0;n<a;n+=1){for(f=i[n].lengths,r.c=s[n].c,o=s[n].c?f.length:f.length+1,h=1;h<o;h+=1)if(y=f[h-1],p+y.addedLength<e.s)p+=y.addedLength,r.c=!1;else if(p>e.e){r.c=!1;break}else e.s<=p&&e.e>=p+y.addedLength?(this.addSegment(s[n].v[h-1],s[n].o[h-1],s[n].i[h],s[n].v[h],r,P,C),C=!1):(b=bez.getNewSegment(s[n].v[h-1],s[n].v[h],s[n].o[h-1],s[n].i[h],(e.s-p)/y.addedLength,(e.e-p)/y.addedLength,f[h-1]),this.addSegmentFromArray(b,r,P,C),C=!1,r.c=!1),p+=y.addedLength,P+=1;if(s[n].c&&f.length){if(y=f[h-1],p<=e.e){var u=f[h-1].addedLength;e.s<=p&&e.e>=p+u?(this.addSegment(s[n].v[h-1],s[n].o[h-1],s[n].i[0],s[n].v[0],r,P,C),C=!1):(b=bez.getNewSegment(s[n].v[h-1],s[n].v[0],s[n].o[h-1],s[n].i[0],(e.s-p)/u,(e.e-p)/u,f[h-1]),this.addSegmentFromArray(b,r,P,C),C=!1,r.c=!1)}else r.c=!1;p+=y.addedLength,P+=1}if(r._length&&(r.setXYAt(r.v[m][0],r.v[m][1],"i",m),r.setXYAt(r.v[r._length-1][0],r.v[r._length-1][1],"o",r._length-1)),p>e.e)break;n<a-1&&(r=shapePool.newElement(),C=!0,g.push(r),P=0)}return g};function PuckerAndBloatModifier(){}extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(t,e){var r=e/100,i=[0,0],s=t._length,n=0;for(n=0;n<s;n+=1)i[0]+=t.v[n][0],i[1]+=t.v[n][1];i[0]/=s,i[1]/=s;var a=shapePool.newElement();a.c=t.c;var h,o,p,y,P,f;for(n=0;n<s;n+=1)h=t.v[n][0]+(i[0]-t.v[n][0])*r,o=t.v[n][1]+(i[1]-t.v[n][1])*r,p=t.o[n][0]+(i[0]-t.o[n][0])*-r,y=t.o[n][1]+(i[1]-t.o[n][1])*-r,P=t.i[n][0]+(i[0]-t.i[n][0])*-r,f=t.i[n][1]+(i[1]-t.i[n][1])*-r,a.setTripleAt(h,o,p,y,P,f,n);return a},PuckerAndBloatModifier.prototype.processShapes=function(t){var e,r,i=this.shapes.length,s,n,a=this.amount.v;if(a!==0){var h,o;for(r=0;r<i;r+=1){if(h=this.shapes[r],o=h.localShapeCollection,!(!h.shape._mdf&&!this._mdf&&!t))for(o.releaseShapes(),h.shape._mdf=!0,e=h.shape.paths.shapes,n=h.shape.paths._length,s=0;s<n;s+=1)o.addShape(this.processPath(e[s],a));h.shape.paths=h.localShapeCollection}}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var t=[0,0];function e(o){var p=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||p,this.a&&o.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&o.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&o.skewFromAxis(-this.sk.v,this.sa.v),this.r?o.rotate(-this.r.v):o.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?o.translate(this.px.v,this.py.v,-this.pz.v):o.translate(this.px.v,this.py.v,0):o.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}function r(o){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||o){var p;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var y,P;if(p=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(y=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/p,0),P=this.p.getValueAtTime(this.p.keyframes[0].t/p,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(y=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/p,0),P=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/p,0)):(y=this.p.pv,P=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/p,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){y=[],P=[];var f=this.px,b=this.py;f._caching.lastFrame+f.offsetTime<=f.keyframes[0].t?(y[0]=f.getValueAtTime((f.keyframes[0].t+.01)/p,0),y[1]=b.getValueAtTime((b.keyframes[0].t+.01)/p,0),P[0]=f.getValueAtTime(f.keyframes[0].t/p,0),P[1]=b.getValueAtTime(b.keyframes[0].t/p,0)):f._caching.lastFrame+f.offsetTime>=f.keyframes[f.keyframes.length-1].t?(y[0]=f.getValueAtTime(f.keyframes[f.keyframes.length-1].t/p,0),y[1]=b.getValueAtTime(b.keyframes[b.keyframes.length-1].t/p,0),P[0]=f.getValueAtTime((f.keyframes[f.keyframes.length-1].t-.01)/p,0),P[1]=b.getValueAtTime((b.keyframes[b.keyframes.length-1].t-.01)/p,0)):(y=[f.pv,b.pv],P[0]=f.getValueAtTime((f._caching.lastFrame+f.offsetTime-.01)/p,f.offsetTime),P[1]=b.getValueAtTime((b._caching.lastFrame+b.offsetTime-.01)/p,b.offsetTime))}else P=t,y=P;this.v.rotate(-Math.atan2(y[1]-P[1],y[0]-P[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}}function i(){if(this.appliedTransformations=0,this.pre.reset(),!this.a.effectsSequence.length)this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1;else return;if(!this.s.effectsSequence.length)this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2;else return;if(this.sk)if(!this.sk.effectsSequence.length&&!this.sa.effectsSequence.length)this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3;else return;this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):!this.rz.effectsSequence.length&&!this.ry.effectsSequence.length&&!this.rx.effectsSequence.length&&!this.or.effectsSequence.length&&(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}function s(){}function n(o){this._addDynamicProperty(o),this.elem.addDynamicProperty(o),this._isDirty=!0}function a(o,p,y){if(this.elem=o,this.frameId=-1,this.propType="transform",this.data=p,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(y||o),p.p&&p.p.s?(this.px=PropertyFactory.getProp(o,p.p.x,0,0,this),this.py=PropertyFactory.getProp(o,p.p.y,0,0,this),p.p.z&&(this.pz=PropertyFactory.getProp(o,p.p.z,0,0,this))):this.p=PropertyFactory.getProp(o,p.p||{k:[0,0,0]},1,0,this),p.rx){if(this.rx=PropertyFactory.getProp(o,p.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(o,p.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(o,p.rz,0,degToRads,this),p.or.k[0].ti){var P,f=p.or.k.length;for(P=0;P<f;P+=1)p.or.k[P].to=null,p.or.k[P].ti=null}this.or=PropertyFactory.getProp(o,p.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(o,p.r||{k:0},0,degToRads,this);p.sk&&(this.sk=PropertyFactory.getProp(o,p.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(o,p.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(o,p.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(o,p.s||{k:[100,100,100]},1,.01,this),p.o?this.o=PropertyFactory.getProp(o,p.o,0,.01,o):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}a.prototype={applyToMatrix:e,getValue:r,precalculateMatrix:i,autoOrient:s},extendPrototype([DynamicPropertyContainer],a),a.prototype.addDynamicProperty=n,a.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty;function h(o,p,y){return new a(o,p,y)}return{getTransformProperty:h}}();function RepeaterModifier(){}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(t,e.c,0,null,this),this.o=PropertyFactory.getProp(t,e.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(t,e.tr,this),this.so=PropertyFactory.getProp(t,e.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(t,e,r,i,s,n){var a=n?-1:1,h=i.s.v[0]+(1-i.s.v[0])*(1-s),o=i.s.v[1]+(1-i.s.v[1])*(1-s);t.translate(i.p.v[0]*a*s,i.p.v[1]*a*s,i.p.v[2]),e.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),e.rotate(-i.r.v*a*s),e.translate(i.a.v[0],i.a.v[1],i.a.v[2]),r.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),r.scale(n?1/h:h,n?1/o:o),r.translate(i.a.v[0],i.a.v[1],i.a.v[2])},RepeaterModifier.prototype.init=function(t,e,r,i){for(this.elem=t,this.arr=e,this.pos=r,this.elemsData=i,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[r]);r>0;)r-=1,this._elements.unshift(e[r]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e]._processed=!1,t[e].ty==="gr"&&this.resetElements(t[e].it)},RepeaterModifier.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},RepeaterModifier.prototype.changeGroupRender=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)t[r]._render=e,t[r].ty==="gr"&&this.changeGroupRender(t[r].it,e)},RepeaterModifier.prototype.processShapes=function(t){var e,r,i,s,n,a=!1;if(this._mdf||t){var h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var o={it:this.cloneElements(this._elements),ty:"gr"};o.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,o),this._groups.splice(0,0,o),this._currentCopies+=1}this.elem.reloadShapes(),a=!0}n=0;var p;for(i=0;i<=this._groups.length-1;i+=1){if(p=n<h,this._groups[i]._render=p,this.changeGroupRender(this._groups[i].it,p),!p){var y=this.elemsData[i].it,P=y[y.length-1];P.transform.op.v!==0?(P.transform.op._mdf=!0,P.transform.op.v=0):P.transform.op._mdf=!1}n+=1}this._currentCopies=h;var f=this.o.v,b=f%1,g=f>0?Math.floor(f):Math.ceil(f),m=this.pMatrix.props,C=this.rMatrix.props,u=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var c=0;if(f>0){for(;c<g;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),c+=1;b&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,b,!1),c+=b)}else if(f<0){for(;c>g;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),c-=1;b&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-b,!0),c-=b)}i=this.data.m===1?0:this._currentCopies-1,s=this.data.m===1?1:-1,n=this._currentCopies;for(var l,d;n;){if(e=this.elemsData[i].it,r=e[e.length-1].transform.mProps.v.props,d=r.length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=this._currentCopies===1?this.so.v:this.so.v+(this.eo.v-this.so.v)*(i/(this._currentCopies-1)),c!==0){for((i!==0&&s===1||i!==this._currentCopies-1&&s===-1)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(C[0],C[1],C[2],C[3],C[4],C[5],C[6],C[7],C[8],C[9],C[10],C[11],C[12],C[13],C[14],C[15]),this.matrix.transform(u[0],u[1],u[2],u[3],u[4],u[5],u[6],u[7],u[8],u[9],u[10],u[11],u[12],u[13],u[14],u[15]),this.matrix.transform(m[0],m[1],m[2],m[3],m[4],m[5],m[6],m[7],m[8],m[9],m[10],m[11],m[12],m[13],m[14],m[15]),l=0;l<d;l+=1)r[l]=this.matrix.props[l];this.matrix.reset()}else for(this.matrix.reset(),l=0;l<d;l+=1)r[l]=this.matrix.props[l];c+=1,n-=1,i+=s}}else for(n=this._currentCopies,i=0,s=1;n;)e=this.elemsData[i].it,r=e[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,n-=1,i+=s;return a},RepeaterModifier.prototype.addShape=function(){};function RoundCornersModifier(){}extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(t,e){var r=shapePool.newElement();r.c=t.c;var i,s=t._length,n,a,h,o,p,y,P=0,f,b,g,m,C,u;for(i=0;i<s;i+=1)n=t.v[i],h=t.o[i],a=t.i[i],n[0]===h[0]&&n[1]===h[1]&&n[0]===a[0]&&n[1]===a[1]?(i===0||i===s-1)&&!t.c?(r.setTripleAt(n[0],n[1],h[0],h[1],a[0],a[1],P),P+=1):(i===0?o=t.v[s-1]:o=t.v[i-1],p=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)),y=p?Math.min(p/2,e)/p:0,C=n[0]+(o[0]-n[0])*y,f=C,u=n[1]-(n[1]-o[1])*y,b=u,g=f-(f-n[0])*roundCorner,m=b-(b-n[1])*roundCorner,r.setTripleAt(f,b,g,m,C,u,P),P+=1,i===s-1?o=t.v[0]:o=t.v[i+1],p=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)),y=p?Math.min(p/2,e)/p:0,g=n[0]+(o[0]-n[0])*y,f=g,m=n[1]+(o[1]-n[1])*y,b=m,C=f-(f-n[0])*roundCorner,u=b-(b-n[1])*roundCorner,r.setTripleAt(f,b,g,m,C,u,P),P+=1):(r.setTripleAt(t.v[i][0],t.v[i][1],t.o[i][0],t.o[i][1],t.i[i][0],t.i[i][1],P),P+=1);return r},RoundCornersModifier.prototype.processShapes=function(t){var e,r,i=this.shapes.length,s,n,a=this.rd.v;if(a!==0){var h,o;for(r=0;r<i;r+=1){if(h=this.shapes[r],o=h.localShapeCollection,!(!h.shape._mdf&&!this._mdf&&!t))for(o.releaseShapes(),h.shape._mdf=!0,e=h.shape.paths.shapes,n=h.shape.paths._length,s=0;s<n;s+=1)o.addShape(this.processPath(e[s],a));h.shape.paths=h.localShapeCollection}}this.dynamicProperties.length||(this._mdf=!1)};function floatEqual(t,e){return Math.abs(t-e)*1e5<=Math.min(Math.abs(t),Math.abs(e))}function floatZero(t){return Math.abs(t)<=1e-5}function lerp(t,e,r){return t*(1-r)+e*r}function lerpPoint(t,e,r){return[lerp(t[0],e[0],r),lerp(t[1],e[1],r)]}function quadRoots(t,e,r){if(t===0)return[];var i=e*e-4*t*r;if(i<0)return[];var s=-e/(2*t);if(i===0)return[s];var n=Math.sqrt(i)/(2*t);return[s-n,s+n]}function polynomialCoefficients(t,e,r,i){return[-t+3*e-3*r+i,3*t-6*e+3*r,-3*t+3*e,t]}function singlePoint(t){return new PolynomialBezier(t,t,t,t,!1)}function PolynomialBezier(t,e,r,i,s){s&&pointEqual(t,e)&&(e=lerpPoint(t,i,1/3)),s&&pointEqual(r,i)&&(r=lerpPoint(t,i,2/3));var n=polynomialCoefficients(t[0],e[0],r[0],i[0]),a=polynomialCoefficients(t[1],e[1],r[1],i[1]);this.a=[n[0],a[0]],this.b=[n[1],a[1]],this.c=[n[2],a[2]],this.d=[n[3],a[3]],this.points=[t,e,r,i]}PolynomialBezier.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},PolynomialBezier.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},PolynomialBezier.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},PolynomialBezier.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},PolynomialBezier.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(floatZero(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,r=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(r<0)return[];var i=Math.sqrt(r);return floatZero(i)?i>0&&i<1?[e]:[]:[e-i,e+i].filter(function(s){return s>0&&s<1})},PolynomialBezier.prototype.split=function(t){if(t<=0)return[singlePoint(this.points[0]),this];if(t>=1)return[this,singlePoint(this.points[this.points.length-1])];var e=lerpPoint(this.points[0],this.points[1],t),r=lerpPoint(this.points[1],this.points[2],t),i=lerpPoint(this.points[2],this.points[3],t),s=lerpPoint(e,r,t),n=lerpPoint(r,i,t),a=lerpPoint(s,n,t);return[new PolynomialBezier(this.points[0],e,s,a,!0),new PolynomialBezier(a,n,i,this.points[3],!0)]};function extrema(t,e){var r=t.points[0][e],i=t.points[t.points.length-1][e];if(r>i){var s=i;i=r,r=s}for(var n=quadRoots(3*t.a[e],2*t.b[e],t.c[e]),a=0;a<n.length;a+=1)if(n[a]>0&&n[a]<1){var h=t.point(n[a])[e];h<r?r=h:h>i&&(i=h)}return{min:r,max:i}}PolynomialBezier.prototype.bounds=function(){return{x:extrema(this,0),y:extrema(this,1)}},PolynomialBezier.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}};function intersectData(t,e,r){var i=t.boundingBox();return{cx:i.cx,cy:i.cy,width:i.width,height:i.height,bez:t,t:(e+r)/2,t1:e,t2:r}}function splitData(t){var e=t.bez.split(.5);return[intersectData(e[0],t.t1,t.t),intersectData(e[1],t.t,t.t2)]}function boxIntersect(t,e){return Math.abs(t.cx-e.cx)*2<t.width+e.width&&Math.abs(t.cy-e.cy)*2<t.height+e.height}function intersectsImpl(t,e,r,i,s,n){if(boxIntersect(t,e)){if(r>=n||t.width<=i&&t.height<=i&&e.width<=i&&e.height<=i){s.push([t.t,e.t]);return}var a=splitData(t),h=splitData(e);intersectsImpl(a[0],h[0],r+1,i,s,n),intersectsImpl(a[0],h[1],r+1,i,s,n),intersectsImpl(a[1],h[0],r+1,i,s,n),intersectsImpl(a[1],h[1],r+1,i,s,n)}}PolynomialBezier.prototype.intersections=function(t,e,r){e===void 0&&(e=2),r===void 0&&(r=7);var i=[];return intersectsImpl(intersectData(this,0,1),intersectData(t,0,1),0,e,i,r),i},PolynomialBezier.shapeSegment=function(t,e){var r=(e+1)%t.length();return new PolynomialBezier(t.v[e],t.o[e],t.i[r],t.v[r],!0)},PolynomialBezier.shapeSegmentInverted=function(t,e){var r=(e+1)%t.length();return new PolynomialBezier(t.v[r],t.i[r],t.o[e],t.v[e],!0)};function crossProduct(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function lineIntersection(t,e,r,i){var s=[t[0],t[1],1],n=[e[0],e[1],1],a=[r[0],r[1],1],h=[i[0],i[1],1],o=crossProduct(crossProduct(s,n),crossProduct(a,h));return floatZero(o[2])?null:[o[0]/o[2],o[1]/o[2]]}function polarOffset(t,e,r){return[t[0]+Math.cos(e)*r,t[1]-Math.sin(e)*r]}function pointDistance(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function pointEqual(t,e){return floatEqual(t[0],e[0])&&floatEqual(t[1],e[1])}function ZigZagModifier(){}extendPrototype([ShapeModifier],ZigZagModifier),ZigZagModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=PropertyFactory.getProp(t,e.s,0,null,this),this.frequency=PropertyFactory.getProp(t,e.r,0,null,this),this.pointsType=PropertyFactory.getProp(t,e.pt,0,null,this),this._isAnimated=this.amplitude.effectsSequence.length!==0||this.frequency.effectsSequence.length!==0||this.pointsType.effectsSequence.length!==0};function setPoint(t,e,r,i,s,n,a){var h=r-Math.PI/2,o=r+Math.PI/2,p=e[0]+Math.cos(r)*i*s,y=e[1]-Math.sin(r)*i*s;t.setTripleAt(p,y,p+Math.cos(h)*n,y-Math.sin(h)*n,p+Math.cos(o)*a,y-Math.sin(o)*a,t.length())}function getPerpendicularVector(t,e){var r=[e[0]-t[0],e[1]-t[1]],i=-Math.PI*.5,s=[Math.cos(i)*r[0]-Math.sin(i)*r[1],Math.sin(i)*r[0]+Math.cos(i)*r[1]];return s}function getProjectingAngle(t,e){var r=e===0?t.length()-1:e-1,i=(e+1)%t.length(),s=t.v[r],n=t.v[i],a=getPerpendicularVector(s,n);return Math.atan2(0,1)-Math.atan2(a[1],a[0])}function zigZagCorner(t,e,r,i,s,n,a){var h=getProjectingAngle(e,r),o=e.v[r%e._length],p=e.v[r===0?e._length-1:r-1],y=e.v[(r+1)%e._length],P=n===2?Math.sqrt(Math.pow(o[0]-p[0],2)+Math.pow(o[1]-p[1],2)):0,f=n===2?Math.sqrt(Math.pow(o[0]-y[0],2)+Math.pow(o[1]-y[1],2)):0;setPoint(t,e.v[r%e._length],h,a,i,f/((s+1)*2),P/((s+1)*2),n)}function zigZagSegment(t,e,r,i,s,n){for(var a=0;a<i;a+=1){var h=(a+1)/(i+1),o=s===2?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,p=e.normalAngle(h),y=e.point(h);setPoint(t,y,p,n,r,o/((i+1)*2),o/((i+1)*2),s),n=-n}return n}ZigZagModifier.prototype.processPath=function(t,e,r,i){var s=t._length,n=shapePool.newElement();if(n.c=t.c,t.c||(s-=1),s===0)return n;var a=-1,h=PolynomialBezier.shapeSegment(t,0);zigZagCorner(n,t,0,e,r,i,a);for(var o=0;o<s;o+=1)a=zigZagSegment(n,h,e,r,i,-a),o===s-1&&!t.c?h=null:h=PolynomialBezier.shapeSegment(t,(o+1)%s),zigZagCorner(n,t,o+1,e,r,i,a);return n},ZigZagModifier.prototype.processShapes=function(t){var e,r,i=this.shapes.length,s,n,a=this.amplitude.v,h=Math.max(0,Math.round(this.frequency.v)),o=this.pointsType.v;if(a!==0){var p,y;for(r=0;r<i;r+=1){if(p=this.shapes[r],y=p.localShapeCollection,!(!p.shape._mdf&&!this._mdf&&!t))for(y.releaseShapes(),p.shape._mdf=!0,e=p.shape.paths.shapes,n=p.shape.paths._length,s=0;s<n;s+=1)y.addShape(this.processPath(e[s],a,h,o));p.shape.paths=p.localShapeCollection}}this.dynamicProperties.length||(this._mdf=!1)};function linearOffset(t,e,r){var i=Math.atan2(e[0]-t[0],e[1]-t[1]);return[polarOffset(t,i,r),polarOffset(e,i,r)]}function offsetSegment(t,e){var r,i,s,n,a,h,o;o=linearOffset(t.points[0],t.points[1],e),r=o[0],i=o[1],o=linearOffset(t.points[1],t.points[2],e),s=o[0],n=o[1],o=linearOffset(t.points[2],t.points[3],e),a=o[0],h=o[1];var p=lineIntersection(r,i,s,n);p===null&&(p=i);var y=lineIntersection(a,h,s,n);return y===null&&(y=a),new PolynomialBezier(r,p,y,h)}function joinLines(t,e,r,i,s){var n=e.points[3],a=r.points[0];if(i===3||pointEqual(n,a))return n;if(i===2){var h=-e.tangentAngle(1),o=-r.tangentAngle(0)+Math.PI,p=lineIntersection(n,polarOffset(n,h+Math.PI/2,100),a,polarOffset(a,h+Math.PI/2,100)),y=p?pointDistance(p,n):pointDistance(n,a)/2,P=polarOffset(n,h,2*y*roundCorner);return t.setXYAt(P[0],P[1],"o",t.length()-1),P=polarOffset(a,o,2*y*roundCorner),t.setTripleAt(a[0],a[1],a[0],a[1],P[0],P[1],t.length()),a}var f=pointEqual(n,e.points[2])?e.points[0]:e.points[2],b=pointEqual(a,r.points[1])?r.points[3]:r.points[1],g=lineIntersection(f,n,a,b);return g&&pointDistance(g,n)<s?(t.setTripleAt(g[0],g[1],g[0],g[1],g[0],g[1],t.length()),g):n}function getIntersection(t,e){var r=t.intersections(e);return r.length&&floatEqual(r[0][0],1)&&r.shift(),r.length?r[0]:null}function pruneSegmentIntersection(t,e){var r=t.slice(),i=e.slice(),s=getIntersection(t[t.length-1],e[0]);return s&&(r[t.length-1]=t[t.length-1].split(s[0])[0],i[0]=e[0].split(s[1])[1]),t.length>1&&e.length>1&&(s=getIntersection(t[0],e[e.length-1]),s)?[[t[0].split(s[0])[0]],[e[e.length-1].split(s[1])[1]]]:[r,i]}function pruneIntersections(t){for(var e,r=1;r<t.length;r+=1)e=pruneSegmentIntersection(t[r-1],t[r]),t[r-1]=e[0],t[r]=e[1];return t.length>1&&(e=pruneSegmentIntersection(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}function offsetSegmentSplit(t,e){var r=t.inflectionPoints(),i,s,n,a;if(r.length===0)return[offsetSegment(t,e)];if(r.length===1||floatEqual(r[1],1))return n=t.split(r[0]),i=n[0],s=n[1],[offsetSegment(i,e),offsetSegment(s,e)];n=t.split(r[0]),i=n[0];var h=(r[1]-r[0])/(1-r[0]);return n=n[1].split(h),a=n[0],s=n[1],[offsetSegment(i,e),offsetSegment(a,e),offsetSegment(s,e)]}function OffsetPathModifier(){}extendPrototype([ShapeModifier],OffsetPathModifier),OffsetPathModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this.miterLimit=PropertyFactory.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=this.amount.effectsSequence.length!==0},OffsetPathModifier.prototype.processPath=function(t,e,r,i){var s=shapePool.newElement();s.c=t.c;var n=t.length();t.c||(n-=1);var a,h,o,p=[];for(a=0;a<n;a+=1)o=PolynomialBezier.shapeSegment(t,a),p.push(offsetSegmentSplit(o,e));if(!t.c)for(a=n-1;a>=0;a-=1)o=PolynomialBezier.shapeSegmentInverted(t,a),p.push(offsetSegmentSplit(o,e));p=pruneIntersections(p);var y=null,P=null;for(a=0;a<p.length;a+=1){var f=p[a];for(P&&(y=joinLines(s,P,f[0],r,i)),P=f[f.length-1],h=0;h<f.length;h+=1)o=f[h],y&&pointEqual(o.points[0],y)?s.setXYAt(o.points[1][0],o.points[1][1],"o",s.length()-1):s.setTripleAt(o.points[0][0],o.points[0][1],o.points[1][0],o.points[1][1],o.points[0][0],o.points[0][1],s.length()),s.setTripleAt(o.points[3][0],o.points[3][1],o.points[3][0],o.points[3][1],o.points[2][0],o.points[2][1],s.length()),y=o.points[3]}return p.length&&joinLines(s,P,p[0][0],r,i),s},OffsetPathModifier.prototype.processShapes=function(t){var e,r,i=this.shapes.length,s,n,a=this.amount.v,h=this.miterLimit.v,o=this.lineJoin;if(a!==0){var p,y;for(r=0;r<i;r+=1){if(p=this.shapes[r],y=p.localShapeCollection,!(!p.shape._mdf&&!this._mdf&&!t))for(y.releaseShapes(),p.shape._mdf=!0,e=p.shape.paths.shapes,n=p.shape.paths._length,s=0;s<n;s+=1)y.addShape(this.processPath(e[s],a,o,h));p.shape.paths=p.localShapeCollection}}this.dynamicProperties.length||(this._mdf=!1)};function getFontProperties(t){for(var e=t.fStyle?t.fStyle.split(" "):[],r="normal",i="normal",s=e.length,n,a=0;a<s;a+=1)switch(n=e[a].toLowerCase(),n){case"italic":i="italic";break;case"bold":r="700";break;case"black":r="900";break;case"medium":r="500";break;case"regular":case"normal":r="400";break;case"light":case"thin":r="200";break;default:break}return{style:i,weight:t.fWeight||r}}var FontManager=function(){var t=5e3,e={w:0,size:0,shapes:[],data:{shapes:[]}},r=[];r=r.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var i=127988,s=917631,n=917601,a=917626,h=65039,o=8205,p=127462,y=127487,P=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"];function f(x){var A=x.split(","),v,S=A.length,I=[];for(v=0;v<S;v+=1)A[v]!=="sans-serif"&&A[v]!=="monospace"&&I.push(A[v]);return I.join(",")}function b(x,A){var v=createTag("span");v.setAttribute("aria-hidden",!0),v.style.fontFamily=A;var S=createTag("span");S.innerText="giItT1WQy@!-/#",v.style.position="absolute",v.style.left="-10000px",v.style.top="-10000px",v.style.fontSize="300px",v.style.fontVariant="normal",v.style.fontStyle="normal",v.style.fontWeight="normal",v.style.letterSpacing="0",v.appendChild(S),document.body.appendChild(v);var I=S.offsetWidth;return S.style.fontFamily=f(x)+", "+A,{node:S,w:I,parent:v}}function g(){var x,A=this.fonts.length,v,S,I=A;for(x=0;x<A;x+=1)this.fonts[x].loaded?I-=1:this.fonts[x].fOrigin==="n"||this.fonts[x].origin===0?this.fonts[x].loaded=!0:(v=this.fonts[x].monoCase.node,S=this.fonts[x].monoCase.w,v.offsetWidth!==S?(I-=1,this.fonts[x].loaded=!0):(v=this.fonts[x].sansCase.node,S=this.fonts[x].sansCase.w,v.offsetWidth!==S&&(I-=1,this.fonts[x].loaded=!0)),this.fonts[x].loaded&&(this.fonts[x].sansCase.parent.parentNode.removeChild(this.fonts[x].sansCase.parent),this.fonts[x].monoCase.parent.parentNode.removeChild(this.fonts[x].monoCase.parent)));I!==0&&Date.now()-this.initTime<t?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)}function m(x,A){var v=document.body&&A?"svg":"canvas",S,I=getFontProperties(x);if(v==="svg"){var M=createNS("text");M.style.fontSize="100px",M.setAttribute("font-family",x.fFamily),M.setAttribute("font-style",I.style),M.setAttribute("font-weight",I.weight),M.textContent="1",x.fClass?(M.style.fontFamily="inherit",M.setAttribute("class",x.fClass)):M.style.fontFamily=x.fFamily,A.appendChild(M),S=M}else{var B=new OffscreenCanvas(500,500).getContext("2d");B.font=I.style+" "+I.weight+" 100px "+x.fFamily,S=B}function N(H){return v==="svg"?(S.textContent=H,S.getComputedTextLength()):S.measureText(H).width}return{measureText:N}}function C(x,A){if(!x){this.isLoaded=!0;return}if(this.chars){this.isLoaded=!0,this.fonts=x.list;return}if(!document.body){this.isLoaded=!0,x.list.forEach(function(Z){Z.helper=m(Z),Z.cache={}}),this.fonts=x.list;return}var v=x.list,S,I=v.length,M=I;for(S=0;S<I;S+=1){var B=!0,N,H;if(v[S].loaded=!1,v[S].monoCase=b(v[S].fFamily,"monospace"),v[S].sansCase=b(v[S].fFamily,"sans-serif"),!v[S].fPath)v[S].loaded=!0,M-=1;else if(v[S].fOrigin==="p"||v[S].origin===3){if(N=document.querySelectorAll('style[f-forigin="p"][f-family="'+v[S].fFamily+'"], style[f-origin="3"][f-family="'+v[S].fFamily+'"]'),N.length>0&&(B=!1),B){var z=createTag("style");z.setAttribute("f-forigin",v[S].fOrigin),z.setAttribute("f-origin",v[S].origin),z.setAttribute("f-family",v[S].fFamily),z.type="text/css",z.innerText="@font-face {font-family: "+v[S].fFamily+"; font-style: normal; src: url('"+v[S].fPath+"');}",A.appendChild(z)}}else if(v[S].fOrigin==="g"||v[S].origin===1){for(N=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),H=0;H<N.length;H+=1)N[H].href.indexOf(v[S].fPath)!==-1&&(B=!1);if(B){var W=createTag("link");W.setAttribute("f-forigin",v[S].fOrigin),W.setAttribute("f-origin",v[S].origin),W.type="text/css",W.rel="stylesheet",W.href=v[S].fPath,document.body.appendChild(W)}}else if(v[S].fOrigin==="t"||v[S].origin===2){for(N=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),H=0;H<N.length;H+=1)v[S].fPath===N[H].src&&(B=!1);if(B){var Y=createTag("link");Y.setAttribute("f-forigin",v[S].fOrigin),Y.setAttribute("f-origin",v[S].origin),Y.setAttribute("rel","stylesheet"),Y.setAttribute("href",v[S].fPath),A.appendChild(Y)}}v[S].helper=m(v[S],A),v[S].cache={},this.fonts.push(v[S])}M===0?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}function u(x){if(x){this.chars||(this.chars=[]);var A,v=x.length,S,I=this.chars.length,M;for(A=0;A<v;A+=1){for(S=0,M=!1;S<I;)this.chars[S].style===x[A].style&&this.chars[S].fFamily===x[A].fFamily&&this.chars[S].ch===x[A].ch&&(M=!0),S+=1;M||(this.chars.push(x[A]),I+=1)}}}function c(x,A,v){for(var S=0,I=this.chars.length;S<I;){if(this.chars[S].ch===x&&this.chars[S].style===A&&this.chars[S].fFamily===v)return this.chars[S];S+=1}return(typeof x=="string"&&x.charCodeAt(0)!==13||!x)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",x,A,v)),e}function l(x,A,v){var S=this.getFontByName(A),I=x;if(!S.cache[I]){var M=S.helper;if(x===" "){var B=M.measureText("|"+x+"|"),N=M.measureText("||");S.cache[I]=(B-N)/100}else S.cache[I]=M.measureText(x)/100}return S.cache[I]*v}function d(x){for(var A=0,v=this.fonts.length;A<v;){if(this.fonts[A].fName===x)return this.fonts[A];A+=1}return this.fonts[0]}function E(x){var A=0,v=x.charCodeAt(0);if(v>=55296&&v<=56319){var S=x.charCodeAt(1);S>=56320&&S<=57343&&(A=(v-55296)*1024+S-56320+65536)}return A}function _(x,A){var v=x.toString(16)+A.toString(16);return P.indexOf(v)!==-1}function T(x){return x===o}function F(x){return x===h}function R(x){var A=E(x);return A>=p&&A<=y}function G(x){return R(x.substr(0,2))&&R(x.substr(2,2))}function w(x){return r.indexOf(x)!==-1}function k(x,A){var v=E(x.substr(A,2));if(v!==i)return!1;var S=0;for(A+=2;S<5;){if(v=E(x.substr(A,2)),v<n||v>a)return!1;S+=1,A+=2}return E(x.substr(A,2))===s}function D(){this.isLoaded=!0}var V=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};V.isModifier=_,V.isZeroWidthJoiner=T,V.isFlagEmoji=G,V.isRegionalCode=R,V.isCombinedCharacter=w,V.isRegionalFlag=k,V.isVariationSelector=F,V.BLACK_FLAG_CODE_POINT=i;var L={addChars:u,addFonts:C,getCharData:c,getFontByName:d,measureText:l,checkLoadedFonts:g,setIsLoaded:D};return V.prototype=L,V}();function SlotManager(t){this.animationData=t}SlotManager.prototype.getProp=function(t){return this.animationData.slots&&this.animationData.slots[t.sid]?Object.assign(t,this.animationData.slots[t.sid].p):t};function slotFactory(t){return new SlotManager(t)}function RenderableElement(){}RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(e){this.renderableComponents.indexOf(e)===-1&&this.renderableComponents.push(e)},removeRenderableComponent:function(e){this.renderableComponents.indexOf(e)!==-1&&this.renderableComponents.splice(this.renderableComponents.indexOf(e),1)},prepareRenderableFrame:function(e){this.checkLayerLimits(e)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(e){this.data.ip-this.data.st<=e&&this.data.op-this.data.st>e?this.isInRange!==!0&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):this.isInRange!==!1&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var e,r=this.renderableComponents.length;for(e=0;e<r;e+=1)this.renderableComponents[e].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return this.data.ty===5?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var getBlendMode=function(){var t={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"};return function(e){return t[e]||""}}();function SliderEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function AngleEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function ColorEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,1,0,r)}function PointEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,1,0,r)}function LayerIndexEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function MaskIndexEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function CheckboxEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function NoValueEffect(){this.p={}}function EffectsManager(t,e){var r=t.ef||[];this.effectElements=[];var i,s=r.length,n;for(i=0;i<s;i+=1)n=new GroupEffect(r[i],e),this.effectElements.push(n)}function GroupEffect(t,e){this.init(t,e)}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(t,e){this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var r,i=this.data.ef.length,s,n=this.data.ef;for(r=0;r<i;r+=1){switch(s=null,n[r].ty){case 0:s=new SliderEffect(n[r],e,this);break;case 1:s=new AngleEffect(n[r],e,this);break;case 2:s=new ColorEffect(n[r],e,this);break;case 3:s=new PointEffect(n[r],e,this);break;case 4:case 7:s=new CheckboxEffect(n[r],e,this);break;case 10:s=new LayerIndexEffect(n[r],e,this);break;case 11:s=new MaskIndexEffect(n[r],e,this);break;case 5:s=new EffectsManager(n[r],e,this);break;default:s=new NoValueEffect(n[r],e,this);break}s&&this.effectElements.push(s)}};function BaseElement(){}BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var e=0,r=this.data.masksProperties.length;e<r;){if(this.data.masksProperties[e].mode!=="n"&&this.data.masksProperties[e].cl!==!1)return!0;e+=1}return!1},initExpressions:function(){var e=getExpressionInterfaces();if(e){var r=e("layer"),i=e("effects"),s=e("shape"),n=e("text"),a=e("comp");this.layerInterface=r(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var h=i.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(h),this.data.ty===0||this.data.xt?this.compInterface=a(this):this.data.ty===4?(this.layerInterface.shapeInterface=s(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):this.data.ty===5&&(this.layerInterface.textInterface=n(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var e=getBlendMode(this.data.bm),r=this.baseElement||this.layerElement;r.style["mix-blend-mode"]=e},initBaseData:function(e,r,i){this.globalData=r,this.comp=i,this.data=e,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}};function FrameElement(){}FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(e,r){var i,s=this.dynamicProperties.length;for(i=0;i<s;i+=1)(r||this._isParent&&this.dynamicProperties[i].propType==="transform")&&(this.dynamicProperties[i].getValue(),this.dynamicProperties[i]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(e){this.dynamicProperties.indexOf(e)===-1&&this.dynamicProperties.push(e)}};function FootageElement(t,e,r){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,r)}FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){var t=getExpressionInterfaces();if(t){var e=t("footage");this.layerInterface=e(this)}},FootageElement.prototype.getFootageData=function(){return this.footageData};function AudioElement(t,e,r){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,r),this._isPlaying=!1,this._canPlay=!1;var i=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(i),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}AudioElement.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var r=this._volume*this._volumeMultiplier;this._previousVolume!==r&&(this._previousVolume=r,this.audio.volume(r))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(t){this.audio.rate(t)},AudioElement.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){};function BaseRenderer(){}BaseRenderer.prototype.checkLayers=function(t){var e,r=this.layers.length,i;for(this.completeLayers=!0,e=r-1;e>=0;e-=1)this.elements[e]||(i=this.layers[e],i.ip-i.st<=t-this.layers[e].st&&i.op-i.st>t-this.layers[e].st&&this.buildItem(e)),this.completeLayers=this.elements[e]?this.completeLayers:!1;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t);default:return this.createNull(t)}},BaseRenderer.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(t){return new AudioElement(t,this.globalData,this)},BaseRenderer.prototype.createFootage=function(t){return new FootageElement(t,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(t){this.completeLayers=!1;var e,r=t.length,i,s=this.layers.length;for(e=0;e<r;e+=1)for(i=0;i<s;){if(this.layers[i].id===t[e].id){this.layers[i]=t[e];break}i+=1}},BaseRenderer.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(t,e,r){for(var i=this.elements,s=this.layers,n=0,a=s.length;n<a;)s[n].ind==e&&(!i[n]||i[n]===!0?(this.buildItem(n),this.addPendingElement(t)):(r.push(i[n]),i[n].setAsParent(),s[n].parent!==void 0?this.buildElementParenting(t,s[n].parent,r):t.setHierarchy(r))),n+=1},BaseRenderer.prototype.addPendingElement=function(t){this.pendingElements.push(t)},BaseRenderer.prototype.searchExtraCompositions=function(t){var e,r=t.length;for(e=0;e<r;e+=1)if(t[e].xt){var i=this.createComp(t[e]);i.initExpressions(),this.globalData.projectInterface.registerComposition(i)}},BaseRenderer.prototype.getElementById=function(t){var e,r=this.elements.length;for(e=0;e<r;e+=1)if(this.elements[e].data.ind===t)return this.elements[e];return null},BaseRenderer.prototype.getElementByPath=function(t){var e=t.shift(),r;if(typeof e=="number")r=this.elements[e];else{var i,s=this.elements.length;for(i=0;i<s;i+=1)if(this.elements[i].data.nm===e){r=this.elements[i];break}}return t.length===0?r:r.getElementByPath(t)},BaseRenderer.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new FontManager,this.globalData.slotManager=slotFactory(t),this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}};var effectTypes={TRANSFORM_EFFECT:"transformEFfect"};function TransformElement(){}TransformElement.prototype={initTransform:function(){var e=new Matrix;this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_localMatMdf:!1,_opMdf:!1,mat:e,localMat:e,localOpacity:1},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var e,r=this.finalTransform.mat,i=0,s=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<s;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(e=this.finalTransform.mProp.v.props,r.cloneFromProps(e),i=0;i<s;i+=1)r.multiply(this.hierarchy[i].finalTransform.mProp.v)}this.finalTransform._matMdf&&(this.finalTransform._localMatMdf=this.finalTransform._matMdf),this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v)},renderLocalTransform:function(){if(this.localTransforms){var e=0,r=this.localTransforms.length;if(this.finalTransform._localMatMdf=this.finalTransform._matMdf,!this.finalTransform._localMatMdf||!this.finalTransform._opMdf)for(;e<r;)this.localTransforms[e]._mdf&&(this.finalTransform._localMatMdf=!0),this.localTransforms[e]._opMdf&&!this.finalTransform._opMdf&&(this.finalTransform.localOpacity=this.finalTransform.mProp.o.v,this.finalTransform._opMdf=!0),e+=1;if(this.finalTransform._localMatMdf){var i=this.finalTransform.localMat;for(this.localTransforms[0].matrix.clone(i),e=1;e<r;e+=1){var s=this.localTransforms[e].matrix;i.multiply(s)}i.multiply(this.finalTransform.mat)}if(this.finalTransform._opMdf){var n=this.finalTransform.localOpacity;for(e=0;e<r;e+=1)n*=this.localTransforms[e].opacity*.01;this.finalTransform.localOpacity=n}}},searchEffectTransforms:function(){if(this.renderableEffectsManager){var e=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT);if(e.length){this.localTransforms=[],this.finalTransform.localMat=new Matrix;var r=0,i=e.length;for(r=0;r<i;r+=1)this.localTransforms.push(e[r])}}},globalToLocal:function(e){var r=[];r.push(this.finalTransform);for(var i=!0,s=this.comp;i;)s.finalTransform?(s.data.hasMask&&r.splice(0,0,s.finalTransform),s=s.comp):i=!1;var n,a=r.length,h;for(n=0;n<a;n+=1)h=r[n].mat.applyToPointArray(0,0,0),e=[e[0]-h[0],e[1]-h[1],0];return e},mHelper:new Matrix};function MaskElement(t,e,r){this.data=t,this.element=e,this.globalData=r,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var i=this.globalData.defs,s,n=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(n),this.solidPath="";var a,h=this.masksProperties,o=0,p=[],y,P,f=createElementID(),b,g,m,C,u="clipPath",c="clip-path";for(s=0;s<n;s+=1)if((h[s].mode!=="a"&&h[s].mode!=="n"||h[s].inv||h[s].o.k!==100||h[s].o.x)&&(u="mask",c="mask"),(h[s].mode==="s"||h[s].mode==="i")&&o===0?(b=createNS("rect"),b.setAttribute("fill","#ffffff"),b.setAttribute("width",this.element.comp.data.w||0),b.setAttribute("height",this.element.comp.data.h||0),p.push(b)):b=null,a=createNS("path"),h[s].mode==="n")this.viewData[s]={op:PropertyFactory.getProp(this.element,h[s].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,h[s],3),elem:a,lastPath:""},i.appendChild(a);else{o+=1,a.setAttribute("fill",h[s].mode==="s"?"#000000":"#ffffff"),a.setAttribute("clip-rule","nonzero");var l;if(h[s].x.k!==0?(u="mask",c="mask",C=PropertyFactory.getProp(this.element,h[s].x,0,null,this.element),l=createElementID(),g=createNS("filter"),g.setAttribute("id",l),m=createNS("feMorphology"),m.setAttribute("operator","erode"),m.setAttribute("in","SourceGraphic"),m.setAttribute("radius","0"),g.appendChild(m),i.appendChild(g),a.setAttribute("stroke",h[s].mode==="s"?"#000000":"#ffffff")):(m=null,C=null),this.storedData[s]={elem:a,x:C,expan:m,lastPath:"",lastOperator:"",filterId:l,lastRadius:0},h[s].mode==="i"){P=p.length;var d=createNS("g");for(y=0;y<P;y+=1)d.appendChild(p[y]);var E=createNS("mask");E.setAttribute("mask-type","alpha"),E.setAttribute("id",f+"_"+o),E.appendChild(a),i.appendChild(E),d.setAttribute("mask","url("+getLocationHref()+"#"+f+"_"+o+")"),p.length=0,p.push(d)}else p.push(a);h[s].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[s]={elem:a,lastPath:"",op:PropertyFactory.getProp(this.element,h[s].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,h[s],3),invRect:b},this.viewData[s].prop.k||this.drawPath(h[s],this.viewData[s].prop.v,this.viewData[s])}for(this.maskElement=createNS(u),n=p.length,s=0;s<n;s+=1)this.maskElement.appendChild(p[s]);o>0&&(this.maskElement.setAttribute("id",f),this.element.maskedElement.setAttribute(c,"url("+getLocationHref()+"#"+f+")"),i.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}MaskElement.prototype.getMaskProperty=function(t){return this.viewData[t].prop},MaskElement.prototype.renderFrame=function(t){var e=this.element.finalTransform.mat,r,i=this.masksProperties.length;for(r=0;r<i;r+=1)if((this.viewData[r].prop._mdf||t)&&this.drawPath(this.masksProperties[r],this.viewData[r].prop.v,this.viewData[r]),(this.viewData[r].op._mdf||t)&&this.viewData[r].elem.setAttribute("fill-opacity",this.viewData[r].op.v),this.masksProperties[r].mode!=="n"&&(this.viewData[r].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[r].invRect.setAttribute("transform",e.getInverseMatrix().to2dCSS()),this.storedData[r].x&&(this.storedData[r].x._mdf||t))){var s=this.storedData[r].expan;this.storedData[r].x.v<0?(this.storedData[r].lastOperator!=="erode"&&(this.storedData[r].lastOperator="erode",this.storedData[r].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[r].filterId+")")),s.setAttribute("radius",-this.storedData[r].x.v)):(this.storedData[r].lastOperator!=="dilate"&&(this.storedData[r].lastOperator="dilate",this.storedData[r].elem.setAttribute("filter",null)),this.storedData[r].elem.setAttribute("stroke-width",this.storedData[r].x.v*2))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,t+=" h-"+this.globalData.compSize.w,t+=" v-"+this.globalData.compSize.h+" ",t},MaskElement.prototype.drawPath=function(t,e,r){var i=" M"+e.v[0][0]+","+e.v[0][1],s,n;for(n=e._length,s=1;s<n;s+=1)i+=" C"+e.o[s-1][0]+","+e.o[s-1][1]+" "+e.i[s][0]+","+e.i[s][1]+" "+e.v[s][0]+","+e.v[s][1];if(e.c&&n>1&&(i+=" C"+e.o[s-1][0]+","+e.o[s-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),r.lastPath!==i){var a="";r.elem&&(e.c&&(a=t.inv?this.solidPath+i:i),r.elem.setAttribute("d",a)),r.lastPath=i}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var t={};t.createFilter=e,t.createAlphaToLuminanceFilter=r;function e(i,s){var n=createNS("filter");return n.setAttribute("id",i),s!==!0&&(n.setAttribute("filterUnits","objectBoundingBox"),n.setAttribute("x","0%"),n.setAttribute("y","0%"),n.setAttribute("width","100%"),n.setAttribute("height","100%")),n}function r(){var i=createNS("feColorMatrix");return i.setAttribute("type","matrix"),i.setAttribute("color-interpolation-filters","sRGB"),i.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),i}return t}(),featureSupport=function(){var t={maskType:!0,svgLumaHidden:!0,offscreenCanvas:typeof OffscreenCanvas<"u"};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),/firefox/i.test(navigator.userAgent)&&(t.svgLumaHidden=!1),t}(),registeredEffects$1={},idPrefix="filter_result_";function SVGEffects(t){var e,r="SourceGraphic",i=t.data.ef?t.data.ef.length:0,s=createElementID(),n=filtersFactory.createFilter(s,!0),a=0;this.filters=[];var h;for(e=0;e<i;e+=1){h=null;var o=t.data.ef[e].ty;if(registeredEffects$1[o]){var p=registeredEffects$1[o].effect;h=new p(n,t.effectsManager.effectElements[e],t,idPrefix+a,r),r=idPrefix+a,registeredEffects$1[o].countsAsEffect&&(a+=1)}h&&this.filters.push(h)}a&&(t.globalData.defs.appendChild(n),t.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+s+")")),this.filters.length&&t.addRenderableComponent(this)}SVGEffects.prototype.renderFrame=function(t){var e,r=this.filters.length;for(e=0;e<r;e+=1)this.filters[e].renderFrame(t)},SVGEffects.prototype.getEffects=function(t){var e,r=this.filters.length,i=[];for(e=0;e<r;e+=1)this.filters[e].type===t&&i.push(this.filters[e]);return i};function registerEffect$1(t,e,r){registeredEffects$1[t]={effect:e,countsAsEffect:r}}function SVGBaseElement(){}SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var e=null;if(this.data.td){this.matteMasks={};var r=createNS("g");r.setAttribute("id",this.layerId),r.appendChild(this.layerElement),e=r,this.globalData.defs.appendChild(r)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),e=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),this.data.ty===0&&!this.data.hd){var i=createNS("clipPath"),s=createNS("path");s.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var n=createElementID();if(i.setAttribute("id",n),i.appendChild(s),this.globalData.defs.appendChild(i),this.checkMasks()){var a=createNS("g");a.setAttribute("clip-path","url("+getLocationHref()+"#"+n+")"),a.appendChild(this.layerElement),this.transformedElement=a,e?e.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+n+")")}this.data.bm!==0&&this.setBlendMode()},renderElement:function(){this.finalTransform._localMatMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.localMat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.localOpacity)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this),this.searchEffectTransforms()},getMatte:function(e){if(this.matteMasks||(this.matteMasks={}),!this.matteMasks[e]){var r=this.layerId+"_"+e,i,s,n,a;if(e===1||e===3){var h=createNS("mask");h.setAttribute("id",r),h.setAttribute("mask-type",e===3?"luminance":"alpha"),n=createNS("use"),n.setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),h.appendChild(n),this.globalData.defs.appendChild(h),!featureSupport.maskType&&e===1&&(h.setAttribute("mask-type","luminance"),i=createElementID(),s=filtersFactory.createFilter(i),this.globalData.defs.appendChild(s),s.appendChild(filtersFactory.createAlphaToLuminanceFilter()),a=createNS("g"),a.appendChild(n),h.appendChild(a),a.setAttribute("filter","url("+getLocationHref()+"#"+i+")"))}else if(e===2){var o=createNS("mask");o.setAttribute("id",r),o.setAttribute("mask-type","alpha");var p=createNS("g");o.appendChild(p),i=createElementID(),s=filtersFactory.createFilter(i);var y=createNS("feComponentTransfer");y.setAttribute("in","SourceGraphic"),s.appendChild(y);var P=createNS("feFuncA");P.setAttribute("type","table"),P.setAttribute("tableValues","1.0 0.0"),y.appendChild(P),this.globalData.defs.appendChild(s);var f=createNS("rect");f.setAttribute("width",this.comp.data.w),f.setAttribute("height",this.comp.data.h),f.setAttribute("x","0"),f.setAttribute("y","0"),f.setAttribute("fill","#ffffff"),f.setAttribute("opacity","0"),p.setAttribute("filter","url("+getLocationHref()+"#"+i+")"),p.appendChild(f),n=createNS("use"),n.setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),p.appendChild(n),featureSupport.maskType||(o.setAttribute("mask-type","luminance"),s.appendChild(filtersFactory.createAlphaToLuminanceFilter()),a=createNS("g"),p.appendChild(f),a.appendChild(this.layerElement),p.appendChild(a)),this.globalData.defs.appendChild(o)}this.matteMasks[e]=r}return this.matteMasks[e]},setMatte:function(e){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+e+")")}};function HierarchyElement(){}HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(e){this.hierarchy=e},setAsParent:function(){this._isParent=!0},checkParenting:function(){this.data.parent!==void 0&&this.comp.buildElementParenting(this,this.data.parent,[])}};function RenderableDOMElement(){}(function(){var t={initElement:function(r,i,s){this.initFrame(),this.initBaseData(r,i,s),this.initTransform(r,i,s),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){if(!this.hidden&&(!this.isInRange||this.isTransparent)){var r=this.baseElement||this.layerElement;r.style.display="none",this.hidden=!0}},show:function(){if(this.isInRange&&!this.isTransparent){if(!this.data.hd){var r=this.baseElement||this.layerElement;r.style.display="block"}this.hidden=!1,this._isFirstFrame=!0}},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(r){this._mdf=!1,this.prepareRenderableFrame(r),this.prepareProperties(r,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}};extendPrototype([RenderableElement,createProxyFunction(t)],RenderableDOMElement)})();function IImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.assetData&&this.assetData.sid&&(this.assetData=e.slotManager.getProp(this.assetData)),this.initElement(t,e,r),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect};function ProcessedElement(t,e){this.elem=t,this.pos=e}function IShapeElement(){}IShapeElement.prototype={addShapeToModifiers:function(e){var r,i=this.shapeModifiers.length;for(r=0;r<i;r+=1)this.shapeModifiers[r].addShape(e)},isShapeInAnimatedModifiers:function(e){for(var r=0,i=this.shapeModifiers.length;r<i;)if(this.shapeModifiers[r].isAnimatedWithShape(e))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var e,r=this.shapes.length;for(e=0;e<r;e+=1)this.shapes[e].sh.reset();r=this.shapeModifiers.length;var i;for(e=r-1;e>=0&&(i=this.shapeModifiers[e].processShapes(this._isFirstFrame),!i);e-=1);}},searchProcessedElement:function(e){for(var r=this.processedElements,i=0,s=r.length;i<s;){if(r[i].elem===e)return r[i].pos;i+=1}return 0},addProcessedElement:function(e,r){for(var i=this.processedElements,s=i.length;s;)if(s-=1,i[s].elem===e){i[s].pos=r;return}i.push(new ProcessedElement(e,r))},prepareFrame:function(e){this.prepareRenderableFrame(e),this.prepareProperties(e,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(t,e,r){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=r,this.lvl=e,this._isAnimated=!!r.k;for(var i=0,s=t.length;i<s;){if(t[i].mProps.dynamicProperties.length){this._isAnimated=!0;break}i+=1}}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0};function SVGStyleData(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=t.hd===!0,this.pElem=createNS("path"),this.msElem=null}SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1};function DashProperty(t,e,r,i){this.elem=t,this.frameId=-1,this.dataProps=createSizedArray(e.length),this.renderer=r,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",e.length?e.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(i);var s,n=e.length||0,a;for(s=0;s<n;s+=1)a=PropertyFactory.getProp(t,e[s].v,0,0,this),this.k=a.k||this.k,this.dataProps[s]={n:e[s].n,p:a};this.k||this.getValue(!0),this._isAnimated=this.k}DashProperty.prototype.getValue=function(t){if(!(this.elem.globalData.frameId===this.frameId&&!t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,r=this.dataProps.length;for(this.renderer==="svg"&&(this.dashStr=""),e=0;e<r;e+=1)this.dataProps[e].n!=="o"?this.renderer==="svg"?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty);function SVGStrokeStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=r,this._isAnimated=!!this._isAnimated}extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData);function SVGFillStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=r}extendPrototype([DynamicPropertyContainer],SVGFillStyleData);function SVGNoStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=r}extendPrototype([DynamicPropertyContainer],SVGNoStyleData);function GradientProperty(t,e,r){this.data=e,this.c=createTypedArray("uint8c",e.p*4);var i=e.k.k[0].s?e.k.k[0].s.length-e.p*4:e.k.k.length-e.p*4;this.o=createTypedArray("float32",i),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=i,this.initDynamicPropertyContainer(r),this.prop=PropertyFactory.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}GradientProperty.prototype.comparePoints=function(t,e){for(var r=0,i=this.o.length/2,s;r<i;){if(s=Math.abs(t[r*4]-t[e*4+r*2]),s>.01)return!1;r+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!==this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,r=this.data.p*4,i,s;for(e=0;e<r;e+=1)i=e%4===0?100:255,s=Math.round(this.prop.v[e]*i),this.c[e]!==s&&(this.c[e]=s,this._cmdf=!t);if(this.o.length)for(r=this.prop.v.length,e=this.data.p*4;e<r;e+=1)i=e%2===0?100:1,s=e%2===0?Math.round(this.prop.v[e]*100):this.prop.v[e],this.o[e-this.data.p*4]!==s&&(this.o[e-this.data.p*4]=s,this._omdf=!t);this._mdf=!t}},extendPrototype([DynamicPropertyContainer],GradientProperty);function SVGGradientFillStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,r)}SVGGradientFillStyleData.prototype.initGradientData=function(t,e,r){this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.s=PropertyFactory.getProp(t,e.s,1,null,this),this.e=PropertyFactory.getProp(t,e.e,1,null,this),this.h=PropertyFactory.getProp(t,e.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(t,e.a||{k:0},0,degToRads,this),this.g=new GradientProperty(t,e.g,this),this.style=r,this.stops=[],this.setGradientData(r.pElem,e),this.setGradientOpacity(e,r),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(t,e){var r=createElementID(),i=createNS(e.t===1?"linearGradient":"radialGradient");i.setAttribute("id",r),i.setAttribute("spreadMethod","pad"),i.setAttribute("gradientUnits","userSpaceOnUse");var s=[],n,a,h;for(h=e.g.p*4,a=0;a<h;a+=4)n=createNS("stop"),i.appendChild(n),s.push(n);t.setAttribute(e.ty==="gf"?"fill":"stroke","url("+getLocationHref()+"#"+r+")"),this.gf=i,this.cst=s},SVGGradientFillStyleData.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var r,i,s,n=createNS("mask"),a=createNS("path");n.appendChild(a);var h=createElementID(),o=createElementID();n.setAttribute("id",o);var p=createNS(t.t===1?"linearGradient":"radialGradient");p.setAttribute("id",h),p.setAttribute("spreadMethod","pad"),p.setAttribute("gradientUnits","userSpaceOnUse"),s=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var y=this.stops;for(i=t.g.p*4;i<s;i+=2)r=createNS("stop"),r.setAttribute("stop-color","rgb(255,255,255)"),p.appendChild(r),y.push(r);a.setAttribute(t.ty==="gf"?"fill":"stroke","url("+getLocationHref()+"#"+h+")"),t.ty==="gs"&&(a.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),a.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),t.lj===1&&a.setAttribute("stroke-miterlimit",t.ml)),this.of=p,this.ms=n,this.ost=y,this.maskId=o,e.msElem=a}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData);function SVGGradientStrokeStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.initGradientData(t,e,r),this._isAnimated=!!this._isAnimated}extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(t,e,r){this.transform={mProps:t,op:e,container:r},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}var buildShapeString=function(e,r,i,s){if(r===0)return"";var n=e.o,a=e.i,h=e.v,o,p=" M"+s.applyToPointStringified(h[0][0],h[0][1]);for(o=1;o<r;o+=1)p+=" C"+s.applyToPointStringified(n[o-1][0],n[o-1][1])+" "+s.applyToPointStringified(a[o][0],a[o][1])+" "+s.applyToPointStringified(h[o][0],h[o][1]);return i&&r&&(p+=" C"+s.applyToPointStringified(n[o-1][0],n[o-1][1])+" "+s.applyToPointStringified(a[0][0],a[0][1])+" "+s.applyToPointStringified(h[0][0],h[0][1]),p+="z"),p},SVGElementsRenderer=function(){var t=new Matrix,e=new Matrix,r={createRenderFunction:i};function i(P){switch(P.ty){case"fl":return h;case"gf":return p;case"gs":return o;case"st":return y;case"sh":case"el":case"rc":case"sr":return a;case"tr":return s;case"no":return n;default:return null}}function s(P,f,b){(b||f.transform.op._mdf)&&f.transform.container.setAttribute("opacity",f.transform.op.v),(b||f.transform.mProps._mdf)&&f.transform.container.setAttribute("transform",f.transform.mProps.v.to2dCSS())}function n(){}function a(P,f,b){var g,m,C,u,c,l,d=f.styles.length,E=f.lvl,_,T,F,R;for(l=0;l<d;l+=1){if(u=f.sh._mdf||b,f.styles[l].lvl<E){for(T=e.reset(),F=E-f.styles[l].lvl,R=f.transformers.length-1;!u&&F>0;)u=f.transformers[R].mProps._mdf||u,F-=1,R-=1;if(u)for(F=E-f.styles[l].lvl,R=f.transformers.length-1;F>0;)T.multiply(f.transformers[R].mProps.v),F-=1,R-=1}else T=t;if(_=f.sh.paths,m=_._length,u){for(C="",g=0;g<m;g+=1)c=_.shapes[g],c&&c._length&&(C+=buildShapeString(c,c._length,c.c,T));f.caches[l]=C}else C=f.caches[l];f.styles[l].d+=P.hd===!0?"":C,f.styles[l]._mdf=u||f.styles[l]._mdf}}function h(P,f,b){var g=f.style;(f.c._mdf||b)&&g.pElem.setAttribute("fill","rgb("+bmFloor(f.c.v[0])+","+bmFloor(f.c.v[1])+","+bmFloor(f.c.v[2])+")"),(f.o._mdf||b)&&g.pElem.setAttribute("fill-opacity",f.o.v)}function o(P,f,b){p(P,f,b),y(P,f,b)}function p(P,f,b){var g=f.gf,m=f.g._hasOpacity,C=f.s.v,u=f.e.v;if(f.o._mdf||b){var c=P.ty==="gf"?"fill-opacity":"stroke-opacity";f.style.pElem.setAttribute(c,f.o.v)}if(f.s._mdf||b){var l=P.t===1?"x1":"cx",d=l==="x1"?"y1":"cy";g.setAttribute(l,C[0]),g.setAttribute(d,C[1]),m&&!f.g._collapsable&&(f.of.setAttribute(l,C[0]),f.of.setAttribute(d,C[1]))}var E,_,T,F;if(f.g._cmdf||b){E=f.cst;var R=f.g.c;for(T=E.length,_=0;_<T;_+=1)F=E[_],F.setAttribute("offset",R[_*4]+"%"),F.setAttribute("stop-color","rgb("+R[_*4+1]+","+R[_*4+2]+","+R[_*4+3]+")")}if(m&&(f.g._omdf||b)){var G=f.g.o;for(f.g._collapsable?E=f.cst:E=f.ost,T=E.length,_=0;_<T;_+=1)F=E[_],f.g._collapsable||F.setAttribute("offset",G[_*2]+"%"),F.setAttribute("stop-opacity",G[_*2+1])}if(P.t===1)(f.e._mdf||b)&&(g.setAttribute("x2",u[0]),g.setAttribute("y2",u[1]),m&&!f.g._collapsable&&(f.of.setAttribute("x2",u[0]),f.of.setAttribute("y2",u[1])));else{var w;if((f.s._mdf||f.e._mdf||b)&&(w=Math.sqrt(Math.pow(C[0]-u[0],2)+Math.pow(C[1]-u[1],2)),g.setAttribute("r",w),m&&!f.g._collapsable&&f.of.setAttribute("r",w)),f.e._mdf||f.h._mdf||f.a._mdf||b){w||(w=Math.sqrt(Math.pow(C[0]-u[0],2)+Math.pow(C[1]-u[1],2)));var k=Math.atan2(u[1]-C[1],u[0]-C[0]),D=f.h.v;D>=1?D=.99:D<=-1&&(D=-.99);var V=w*D,L=Math.cos(k+f.a.v)*V+C[0],x=Math.sin(k+f.a.v)*V+C[1];g.setAttribute("fx",L),g.setAttribute("fy",x),m&&!f.g._collapsable&&(f.of.setAttribute("fx",L),f.of.setAttribute("fy",x))}}}function y(P,f,b){var g=f.style,m=f.d;m&&(m._mdf||b)&&m.dashStr&&(g.pElem.setAttribute("stroke-dasharray",m.dashStr),g.pElem.setAttribute("stroke-dashoffset",m.dashoffset[0])),f.c&&(f.c._mdf||b)&&g.pElem.setAttribute("stroke","rgb("+bmFloor(f.c.v[0])+","+bmFloor(f.c.v[1])+","+bmFloor(f.c.v[2])+")"),(f.o._mdf||b)&&g.pElem.setAttribute("stroke-opacity",f.o.v),(f.w._mdf||b)&&(g.pElem.setAttribute("stroke-width",f.w.v),g.msElem&&g.msElem.setAttribute("stroke-width",f.w.v))}return r}();function SVGShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,r),this.prevViewData=[]}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var t,e=this.shapes.length,r,i,s=this.stylesList.length,n,a=[],h=!1;for(i=0;i<s;i+=1){for(n=this.stylesList[i],h=!1,a.length=0,t=0;t<e;t+=1)r=this.shapes[t],r.styles.indexOf(n)!==-1&&(a.push(r),h=r._isAnimated||h);a.length>1&&h&&this.setShapesAsAnimated(a)}},SVGShapeElement.prototype.setShapesAsAnimated=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(t,e){var r,i=new SVGStyleData(t,e),s=i.pElem;if(t.ty==="st")r=new SVGStrokeStyleData(this,t,i);else if(t.ty==="fl")r=new SVGFillStyleData(this,t,i);else if(t.ty==="gf"||t.ty==="gs"){var n=t.ty==="gf"?SVGGradientFillStyleData:SVGGradientStrokeStyleData;r=new n(this,t,i),this.globalData.defs.appendChild(r.gf),r.maskId&&(this.globalData.defs.appendChild(r.ms),this.globalData.defs.appendChild(r.of),s.setAttribute("mask","url("+getLocationHref()+"#"+r.maskId+")"))}else t.ty==="no"&&(r=new SVGNoStyleData(this,t,i));return(t.ty==="st"||t.ty==="gs")&&(s.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),s.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),s.setAttribute("fill-opacity","0"),t.lj===1&&s.setAttribute("stroke-miterlimit",t.ml)),t.r===2&&s.setAttribute("fill-rule","evenodd"),t.ln&&s.setAttribute("id",t.ln),t.cl&&s.setAttribute("class",t.cl),t.bm&&(s.style["mix-blend-mode"]=getBlendMode(t.bm)),this.stylesList.push(i),this.addToAnimatedContents(t,r),r},SVGShapeElement.prototype.createGroupElement=function(t){var e=new ShapeGroupData;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=getBlendMode(t.bm)),e},SVGShapeElement.prototype.createTransformElement=function(t,e){var r=TransformPropertyFactory.getTransformProperty(this,t,this),i=new SVGTransformData(r,r.o,e);return this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createShapeElement=function(t,e,r){var i=4;t.ty==="rc"?i=5:t.ty==="el"?i=6:t.ty==="sr"&&(i=7);var s=ShapePropertyFactory.getShapeProp(this,t,i,this),n=new SVGShapeData(e,r,s);return this.shapes.push(n),this.addShapeToModifiers(n),this.addToAnimatedContents(t,n),n},SVGShapeElement.prototype.addToAnimatedContents=function(t,e){for(var r=0,i=this.animatedContents.length;r<i;){if(this.animatedContents[r].element===e)return;r+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(t),element:e,data:t})},SVGShapeElement.prototype.setElementStyles=function(t){var e=t.styles,r,i=this.stylesList.length;for(r=0;r<i;r+=1)this.stylesList[r].closed||e.push(this.stylesList[r])},SVGShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var t,e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(t,e,r,i,s,n,a){var h=[].concat(n),o,p=t.length-1,y,P,f=[],b=[],g,m,C;for(o=p;o>=0;o-=1){if(C=this.searchProcessedElement(t[o]),C?e[o]=r[C-1]:t[o]._render=a,t[o].ty==="fl"||t[o].ty==="st"||t[o].ty==="gf"||t[o].ty==="gs"||t[o].ty==="no")C?e[o].style.closed=!1:e[o]=this.createStyleElement(t[o],s),t[o]._render&&e[o].style.pElem.parentNode!==i&&i.appendChild(e[o].style.pElem),f.push(e[o].style);else if(t[o].ty==="gr"){if(!C)e[o]=this.createGroupElement(t[o]);else for(P=e[o].it.length,y=0;y<P;y+=1)e[o].prevViewData[y]=e[o].it[y];this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,s+1,h,a),t[o]._render&&e[o].gr.parentNode!==i&&i.appendChild(e[o].gr)}else t[o].ty==="tr"?(C||(e[o]=this.createTransformElement(t[o],i)),g=e[o].transform,h.push(g)):t[o].ty==="sh"||t[o].ty==="rc"||t[o].ty==="el"||t[o].ty==="sr"?(C||(e[o]=this.createShapeElement(t[o],h,s)),this.setElementStyles(e[o])):t[o].ty==="tm"||t[o].ty==="rd"||t[o].ty==="ms"||t[o].ty==="pb"||t[o].ty==="zz"||t[o].ty==="op"?(C?(m=e[o],m.closed=!1):(m=ShapeModifiers.getModifier(t[o].ty),m.init(this,t[o]),e[o]=m,this.shapeModifiers.push(m)),b.push(m)):t[o].ty==="rp"&&(C?(m=e[o],m.closed=!0):(m=ShapeModifiers.getModifier(t[o].ty),e[o]=m,m.init(this,t,o,e),this.shapeModifiers.push(m),a=!1),b.push(m));this.addProcessedElement(t[o],o+1)}for(p=f.length,o=0;o<p;o+=1)f[o].closed=!0;for(p=b.length,o=0;o<p;o+=1)b[o].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){this.renderModifiers();var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var t,e=this.animatedContents.length,r;for(t=0;t<e;t+=1)r=this.animatedContents[t],(this._isFirstFrame||r.element._isAnimated)&&r.data!==!0&&r.fn(r.data,r.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null};function LetterProps(t,e,r,i,s,n){this.o=t,this.sw=e,this.sc=r,this.fc=i,this.m=s,this.p=n,this._mdf={o:!0,sw:!!e,sc:!!r,fc:!!i,m:!0,p:!0}}LetterProps.prototype.update=function(t,e,r,i,s,n){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var a=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,a=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,a=!0),this.sc!==r&&(this.sc=r,this._mdf.sc=!0,a=!0),this.fc!==i&&(this.fc=i,this._mdf.fc=!0,a=!0),this.m!==s&&(this.m=s,this._mdf.m=!0,a=!0),n.length&&(this.p[0]!==n[0]||this.p[1]!==n[1]||this.p[4]!==n[4]||this.p[5]!==n[5]||this.p[12]!==n[12]||this.p[13]!==n[13])&&(this.p=n,this._mdf.p=!0,a=!0),a};function TextProperty(t,e){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,e.d&&e.d.sid&&(e.d=t.globalData.slotManager.getProp(e.d)),this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},TextProperty.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(t){if(!((this.elem.globalData.frameId===this.frameId||!this.effectsSequence.length)&&!t)){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,r=this.keysIndex;if(this.lock){this.setCurrentData(this.currentData);return}this.lock=!0,this._mdf=!1;var i,s=this.effectsSequence.length,n=t||this.data.d.k[this.keysIndex].s;for(i=0;i<s;i+=1)r!==this.keysIndex?n=this.effectsSequence[i](n,n.t):n=this.effectsSequence[i](this.currentData,n.t);e!==n&&this.setCurrentData(n),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}},TextProperty.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,r=0,i=t.length;r<=i-1&&!(r===i-1||t[r+1].t>e);)r+=1;return this.keysIndex!==r&&(this.keysIndex=r),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(t){for(var e=[],r=0,i=t.length,s,n,a=!1,h=!1,o="";r<i;)a=h,h=!1,s=t.charCodeAt(r),o=t.charAt(r),FontManager.isCombinedCharacter(s)?a=!0:s>=55296&&s<=56319?FontManager.isRegionalFlag(t,r)?o=t.substr(r,14):(n=t.charCodeAt(r+1),n>=56320&&n<=57343&&(FontManager.isModifier(s,n)?(o=t.substr(r,2),a=!0):FontManager.isFlagEmoji(t.substr(r,4))?o=t.substr(r,4):o=t.substr(r,2))):s>56319?(n=t.charCodeAt(r+1),FontManager.isVariationSelector(s)&&(a=!0)):FontManager.isZeroWidthJoiner(s)&&(a=!0,h=!0),a?(e[e.length-1]+=o,a=!1):e.push(o),r+=o.length;return e},TextProperty.prototype.completeTextData=function(t){t.__complete=!0;var e=this.elem.globalData.fontManager,r=this.data,i=[],s,n,a,h=0,o,p=r.m.g,y=0,P=0,f=0,b=[],g=0,m=0,C,u,c=e.getFontByName(t.f),l,d=0,E=getFontProperties(c);t.fWeight=E.weight,t.fStyle=E.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),n=t.finalText.length,t.finalLineHeight=t.lh;var _=t.tr/1e3*t.finalSize,T;if(t.sz)for(var F=!0,R=t.sz[0],G=t.sz[1],w,k;F;){k=this.buildFinalText(t.t),w=0,g=0,n=k.length,_=t.tr/1e3*t.finalSize;var D=-1;for(s=0;s<n;s+=1)T=k[s].charCodeAt(0),a=!1,k[s]===" "?D=s:(T===13||T===3)&&(g=0,a=!0,w+=t.finalLineHeight||t.finalSize*1.2),e.chars?(l=e.getCharData(k[s],c.fStyle,c.fFamily),d=a?0:l.w*t.finalSize/100):d=e.measureText(k[s],t.f,t.finalSize),g+d>R&&k[s]!==" "?(D===-1?n+=1:s=D,w+=t.finalLineHeight||t.finalSize*1.2,k.splice(s,D===s?1:0,"\r"),D=-1,g=0):(g+=d,g+=_);w+=c.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&G<w?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=k,n=t.finalText.length,F=!1)}g=-_,d=0;var V=0,L;for(s=0;s<n;s+=1)if(a=!1,L=t.finalText[s],T=L.charCodeAt(0),T===13||T===3?(V=0,b.push(g),m=g>m?g:m,g=-2*_,o="",a=!0,f+=1):o=L,e.chars?(l=e.getCharData(L,c.fStyle,e.getFontByName(t.f).fFamily),d=a?0:l.w*t.finalSize/100):d=e.measureText(o,t.f,t.finalSize),L===" "?V+=d+_:(g+=d+_+V,V=0),i.push({l:d,an:d,add:y,n:a,anIndexes:[],val:o,line:f,animatorJustifyOffset:0}),p==2){if(y+=d,o===""||o===" "||s===n-1){for((o===""||o===" ")&&(y-=d);P<=s;)i[P].an=y,i[P].ind=h,i[P].extra=d,P+=1;h+=1,y=0}}else if(p==3){if(y+=d,o===""||s===n-1){for(o===""&&(y-=d);P<=s;)i[P].an=y,i[P].ind=h,i[P].extra=d,P+=1;y=0,h+=1}}else i[h].ind=h,i[h].extra=0,h+=1;if(t.l=i,m=g>m?g:m,b.push(g),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=m,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=b;var x=r.a,A,v;u=x.length;var S,I,M=[];for(C=0;C<u;C+=1){for(A=x[C],A.a.sc&&(t.strokeColorAnim=!0),A.a.sw&&(t.strokeWidthAnim=!0),(A.a.fc||A.a.fh||A.a.fs||A.a.fb)&&(t.fillColorAnim=!0),I=0,S=A.s.b,s=0;s<n;s+=1)v=i[s],v.anIndexes[C]=I,(S==1&&v.val!==""||S==2&&v.val!==""&&v.val!==" "||S==3&&(v.n||v.val==" "||s==n-1)||S==4&&(v.n||s==n-1))&&(A.s.rn===1&&M.push(I),I+=1);r.a[C].s.totalChars=I;var B=-1,N;if(A.s.rn===1)for(s=0;s<n;s+=1)v=i[s],B!=v.anIndexes[C]&&(B=v.anIndexes[C],N=M.splice(Math.floor(Math.random()*M.length),1)[0]),v.anIndexes[C]=N}t.yOffset=t.finalLineHeight||t.finalSize*1.2,t.ls=t.ls||0,t.ascent=c.ascent*t.finalSize/100},TextProperty.prototype.updateDocumentData=function(t,e){e=e===void 0?this.keysIndex:e;var r=this.copyData({},this.data.d.k[e].s);r=this.copyData(r,t),this.data.d.k[e].s=r,this.recalculate(e),this.setCurrentData(r),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},TextProperty.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var t=Math.max,e=Math.min,r=Math.floor;function i(n,a){this._currentTextLength=-1,this.k=!1,this.data=a,this.elem=n,this.comp=n.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(n),this.s=PropertyFactory.getProp(n,a.s||{k:0},0,0,this),"e"in a?this.e=PropertyFactory.getProp(n,a.e,0,0,this):this.e={v:100},this.o=PropertyFactory.getProp(n,a.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(n,a.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(n,a.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(n,a.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(n,a.a,0,.01,this),this.dynamicProperties.length||this.getValue()}i.prototype={getMult:function(a){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var h=0,o=0,p=1,y=1;this.ne.v>0?h=this.ne.v/100:o=-this.ne.v/100,this.xe.v>0?p=1-this.xe.v/100:y=1+this.xe.v/100;var P=BezierFactory.getBezierEasing(h,o,p,y).get,f=0,b=this.finalS,g=this.finalE,m=this.data.sh;if(m===2)g===b?f=a>=g?1:0:f=t(0,e(.5/(g-b)+(a-b)/(g-b),1)),f=P(f);else if(m===3)g===b?f=a>=g?0:1:f=1-t(0,e(.5/(g-b)+(a-b)/(g-b),1)),f=P(f);else if(m===4)g===b?f=0:(f=t(0,e(.5/(g-b)+(a-b)/(g-b),1)),f<.5?f*=2:f=1-2*(f-.5)),f=P(f);else if(m===5){if(g===b)f=0;else{var C=g-b;a=e(t(0,a+.5-b),g-b);var u=-C/2+a,c=C/2;f=Math.sqrt(1-u*u/(c*c))}f=P(f)}else m===6?(g===b?f=0:(a=e(t(0,a+.5-b),g-b),f=(1+Math.cos(Math.PI+Math.PI*2*a/(g-b)))/2),f=P(f)):(a>=r(b)&&(a-b<0?f=t(0,e(e(g,1)-(b-a),1)):f=t(0,e(g-a,1))),f=P(f));if(this.sm.v!==100){var l=this.sm.v*.01;l===0&&(l=1e-8);var d=.5-l*.5;f<d?f=0:(f=(f-d)/l,f>1&&(f=1))}return f*this.a.v},getValue:function(a){this.iterateDynamicProperties(),this._mdf=a||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,a&&this.data.r===2&&(this.e.v=this._currentTextLength);var h=this.data.r===2?1:100/this.data.totalChars,o=this.o.v/h,p=this.s.v/h+o,y=this.e.v/h+o;if(p>y){var P=p;p=y,y=P}this.finalS=p,this.finalE=y}},extendPrototype([DynamicPropertyContainer],i);function s(n,a,h){return new i(n,a,h)}return{getTextSelectorProp:s}}();function TextAnimatorDataProperty(t,e,r){var i={propType:!1},s=PropertyFactory.getProp,n=e.a;this.a={r:n.r?s(t,n.r,0,degToRads,r):i,rx:n.rx?s(t,n.rx,0,degToRads,r):i,ry:n.ry?s(t,n.ry,0,degToRads,r):i,sk:n.sk?s(t,n.sk,0,degToRads,r):i,sa:n.sa?s(t,n.sa,0,degToRads,r):i,s:n.s?s(t,n.s,1,.01,r):i,a:n.a?s(t,n.a,1,0,r):i,o:n.o?s(t,n.o,0,.01,r):i,p:n.p?s(t,n.p,1,0,r):i,sw:n.sw?s(t,n.sw,0,0,r):i,sc:n.sc?s(t,n.sc,1,0,r):i,fc:n.fc?s(t,n.fc,1,0,r):i,fh:n.fh?s(t,n.fh,0,0,r):i,fs:n.fs?s(t,n.fs,0,.01,r):i,fb:n.fb?s(t,n.fb,0,.01,r):i,t:n.t?s(t,n.t,0,0,r):i},this.s=TextSelectorProp.getTextSelectorProp(t,e.s,r),this.s.t=e.s.t}function TextAnimatorProperty(t,e,r){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=r,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(r)}TextAnimatorProperty.prototype.searchProperties=function(){var t,e=this._textData.a.length,r,i=PropertyFactory.getProp;for(t=0;t<e;t+=1)r=this._textData.a[t],this._animatorsData[t]=new TextAnimatorDataProperty(this._elem,r,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:i(this._elem,this._textData.p.a,0,0,this),f:i(this._elem,this._textData.p.f,0,0,this),l:i(this._elem,this._textData.p.l,0,0,this),r:i(this._elem,this._textData.p.r,0,0,this),p:i(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=i(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,!(!this._mdf&&!this._isFirstFrame&&!e&&(!this._hasMaskedPath||!this._pathData.m._mdf))){this._isFirstFrame=!1;var r=this._moreOptions.alignment.v,i=this._animatorsData,s=this._textData,n=this.mHelper,a=this._renderType,h=this.renderedLetters.length,o,p,y,P,f=t.l,b,g,m,C,u,c,l,d,E,_,T,F,R,G,w;if(this._hasMaskedPath){if(w=this._pathData.m,!this._pathData.n||this._pathData._mdf){var k=w.v;this._pathData.r.v&&(k=k.reverse()),b={tLength:0,segments:[]},P=k._length-1;var D;for(F=0,y=0;y<P;y+=1)D=bez.buildBezierData(k.v[y],k.v[y+1],[k.o[y][0]-k.v[y][0],k.o[y][1]-k.v[y][1]],[k.i[y+1][0]-k.v[y+1][0],k.i[y+1][1]-k.v[y+1][1]]),b.tLength+=D.segmentLength,b.segments.push(D),F+=D.segmentLength;y=P,w.v.c&&(D=bez.buildBezierData(k.v[y],k.v[0],[k.o[y][0]-k.v[y][0],k.o[y][1]-k.v[y][1]],[k.i[0][0]-k.v[0][0],k.i[0][1]-k.v[0][1]]),b.tLength+=D.segmentLength,b.segments.push(D),F+=D.segmentLength),this._pathData.pi=b}if(b=this._pathData.pi,g=this._pathData.f.v,l=0,c=1,C=0,u=!0,_=b.segments,g<0&&w.v.c)for(b.tLength<Math.abs(g)&&(g=-Math.abs(g)%b.tLength),l=_.length-1,E=_[l].points,c=E.length-1;g<0;)g+=E[c].partialLength,c-=1,c<0&&(l-=1,E=_[l].points,c=E.length-1);E=_[l].points,d=E[c-1],m=E[c],T=m.partialLength}P=f.length,o=0,p=0;var V=t.finalSize*1.2*.714,L=!0,x,A,v,S,I;S=i.length;var M,B=-1,N,H,z,W=g,Y=l,Z=c,tt=-1,q,K,Q,j,O,at,ft,ot,st="",ht=this.defaultPropsArray,lt;if(t.j===2||t.j===1){var et=0,pt=0,ut=t.j===2?-.5:-1,rt=0,ct=!0;for(y=0;y<P;y+=1)if(f[y].n){for(et&&(et+=pt);rt<y;)f[rt].animatorJustifyOffset=et,rt+=1;et=0,ct=!0}else{for(v=0;v<S;v+=1)x=i[v].a,x.t.propType&&(ct&&t.j===2&&(pt+=x.t.v*ut),A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),M.length?et+=x.t.v*M[0]*ut:et+=x.t.v*M*ut);ct=!1}for(et&&(et+=pt);rt<y;)f[rt].animatorJustifyOffset=et,rt+=1}for(y=0;y<P;y+=1){if(n.reset(),q=1,f[y].n)o=0,p+=t.yOffset,p+=L?1:0,g=W,L=!1,this._hasMaskedPath&&(l=Y,c=Z,E=_[l].points,d=E[c-1],m=E[c],T=m.partialLength,C=0),st="",ot="",at="",lt="",ht=this.defaultPropsArray;else{if(this._hasMaskedPath){if(tt!==f[y].line){switch(t.j){case 1:g+=F-t.lineWidths[f[y].line];break;case 2:g+=(F-t.lineWidths[f[y].line])/2;break;default:break}tt=f[y].line}B!==f[y].ind&&(f[B]&&(g+=f[B].extra),g+=f[y].an/2,B=f[y].ind),g+=r[0]*f[y].an*.005;var it=0;for(v=0;v<S;v+=1)x=i[v].a,x.p.propType&&(A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),M.length?it+=x.p.v[0]*M[0]:it+=x.p.v[0]*M),x.a.propType&&(A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),M.length?it+=x.a.v[0]*M[0]:it+=x.a.v[0]*M);for(u=!0,this._pathData.a.v&&(g=f[0].an*.5+(F-this._pathData.f.v-f[0].an*.5-f[f.length-1].an*.5)*B/(P-1),g+=this._pathData.f.v);u;)C+T>=g+it||!E?(R=(g+it-C)/m.partialLength,H=d.point[0]+(m.point[0]-d.point[0])*R,z=d.point[1]+(m.point[1]-d.point[1])*R,n.translate(-r[0]*f[y].an*.005,-(r[1]*V)*.01),u=!1):E&&(C+=m.partialLength,c+=1,c>=E.length&&(c=0,l+=1,_[l]?E=_[l].points:w.v.c?(c=0,l=0,E=_[l].points):(C-=m.partialLength,E=null)),E&&(d=m,m=E[c],T=m.partialLength));N=f[y].an/2-f[y].add,n.translate(-N,0,0)}else N=f[y].an/2-f[y].add,n.translate(-N,0,0),n.translate(-r[0]*f[y].an*.005,-r[1]*V*.01,0);for(v=0;v<S;v+=1)x=i[v].a,x.t.propType&&(A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),(o!==0||t.j!==0)&&(this._hasMaskedPath?M.length?g+=x.t.v*M[0]:g+=x.t.v*M:M.length?o+=x.t.v*M[0]:o+=x.t.v*M));for(t.strokeWidthAnim&&(Q=t.sw||0),t.strokeColorAnim&&(t.sc?K=[t.sc[0],t.sc[1],t.sc[2]]:K=[0,0,0]),t.fillColorAnim&&t.fc&&(j=[t.fc[0],t.fc[1],t.fc[2]]),v=0;v<S;v+=1)x=i[v].a,x.a.propType&&(A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),M.length?n.translate(-x.a.v[0]*M[0],-x.a.v[1]*M[1],x.a.v[2]*M[2]):n.translate(-x.a.v[0]*M,-x.a.v[1]*M,x.a.v[2]*M));for(v=0;v<S;v+=1)x=i[v].a,x.s.propType&&(A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),M.length?n.scale(1+(x.s.v[0]-1)*M[0],1+(x.s.v[1]-1)*M[1],1):n.scale(1+(x.s.v[0]-1)*M,1+(x.s.v[1]-1)*M,1));for(v=0;v<S;v+=1){if(x=i[v].a,A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),x.sk.propType&&(M.length?n.skewFromAxis(-x.sk.v*M[0],x.sa.v*M[1]):n.skewFromAxis(-x.sk.v*M,x.sa.v*M)),x.r.propType&&(M.length?n.rotateZ(-x.r.v*M[2]):n.rotateZ(-x.r.v*M)),x.ry.propType&&(M.length?n.rotateY(x.ry.v*M[1]):n.rotateY(x.ry.v*M)),x.rx.propType&&(M.length?n.rotateX(x.rx.v*M[0]):n.rotateX(x.rx.v*M)),x.o.propType&&(M.length?q+=(x.o.v*M[0]-q)*M[0]:q+=(x.o.v*M-q)*M),t.strokeWidthAnim&&x.sw.propType&&(M.length?Q+=x.sw.v*M[0]:Q+=x.sw.v*M),t.strokeColorAnim&&x.sc.propType)for(O=0;O<3;O+=1)M.length?K[O]+=(x.sc.v[O]-K[O])*M[0]:K[O]+=(x.sc.v[O]-K[O])*M;if(t.fillColorAnim&&t.fc){if(x.fc.propType)for(O=0;O<3;O+=1)M.length?j[O]+=(x.fc.v[O]-j[O])*M[0]:j[O]+=(x.fc.v[O]-j[O])*M;x.fh.propType&&(M.length?j=addHueToRGB(j,x.fh.v*M[0]):j=addHueToRGB(j,x.fh.v*M)),x.fs.propType&&(M.length?j=addSaturationToRGB(j,x.fs.v*M[0]):j=addSaturationToRGB(j,x.fs.v*M)),x.fb.propType&&(M.length?j=addBrightnessToRGB(j,x.fb.v*M[0]):j=addBrightnessToRGB(j,x.fb.v*M))}}for(v=0;v<S;v+=1)x=i[v].a,x.p.propType&&(A=i[v].s,M=A.getMult(f[y].anIndexes[v],s.a[v].s.totalChars),this._hasMaskedPath?M.length?n.translate(0,x.p.v[1]*M[0],-x.p.v[2]*M[1]):n.translate(0,x.p.v[1]*M,-x.p.v[2]*M):M.length?n.translate(x.p.v[0]*M[0],x.p.v[1]*M[1],-x.p.v[2]*M[2]):n.translate(x.p.v[0]*M,x.p.v[1]*M,-x.p.v[2]*M));if(t.strokeWidthAnim&&(at=Q<0?0:Q),t.strokeColorAnim&&(ft="rgb("+Math.round(K[0]*255)+","+Math.round(K[1]*255)+","+Math.round(K[2]*255)+")"),t.fillColorAnim&&t.fc&&(ot="rgb("+Math.round(j[0]*255)+","+Math.round(j[1]*255)+","+Math.round(j[2]*255)+")"),this._hasMaskedPath){if(n.translate(0,-t.ls),n.translate(0,r[1]*V*.01+p,0),this._pathData.p.v){G=(m.point[1]-d.point[1])/(m.point[0]-d.point[0]);var mt=Math.atan(G)*180/Math.PI;m.point[0]<d.point[0]&&(mt+=180),n.rotate(-mt*Math.PI/180)}n.translate(H,z,0),g-=r[0]*f[y].an*.005,f[y+1]&&B!==f[y+1].ind&&(g+=f[y].an/2,g+=t.tr*.001*t.finalSize)}else{switch(n.translate(o,p,0),t.ps&&n.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:n.translate(f[y].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[f[y].line]),0,0);break;case 2:n.translate(f[y].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[f[y].line])/2,0,0);break;default:break}n.translate(0,-t.ls),n.translate(N,0,0),n.translate(r[0]*f[y].an*.005,r[1]*V*.01,0),o+=f[y].l+t.tr*.001*t.finalSize}a==="html"?st=n.toCSS():a==="svg"?st=n.to2dCSS():ht=[n.props[0],n.props[1],n.props[2],n.props[3],n.props[4],n.props[5],n.props[6],n.props[7],n.props[8],n.props[9],n.props[10],n.props[11],n.props[12],n.props[13],n.props[14],n.props[15]],lt=q}h<=y?(I=new LetterProps(lt,at,ft,ot,st,ht),this.renderedLetters.push(I),h+=1,this.lettersChangedFlag=!0):(I=this.renderedLetters[y],this.lettersChangedFlag=I.update(lt,at,ft,ot,st,ht)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty);function ITextElement(){}ITextElement.prototype.initElement=function(t,e,r){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,r),this.textProperty=new TextProperty(this,t.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(t.t,this.renderType,this),this.initTransform(t,e,r),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)},ITextElement.prototype.createPathShape=function(t,e){var r,i=e.length,s,n="";for(r=0;r<i;r+=1)e[r].ty==="sh"&&(s=e[r].ks.k,n+=buildShapeString(s,s.i.length,!0,t));return n},ITextElement.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ITextElement.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ITextElement.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ITextElement.prototype.applyTextPropertiesToMatrix=function(t,e,r,i,s){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[r]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[r])/2,0,0);break;default:break}e.translate(i,s,0)},ITextElement.prototype.buildColor=function(t){return"rgb("+Math.round(t[0]*255)+","+Math.round(t[1]*255)+","+Math.round(t[2]*255)+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){},ITextElement.prototype.validateText=function(){(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)};var emptyShapeData={shapes:[]};function SVGTextLottieElement(t,e,r){this.textSpans=[],this.renderType="svg",this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(t){for(var e=0,r=t.length,i=[],s="";e<r;)t[e]==="\r"||t[e]===""?(i.push(s),s=""):s+=t[e],e+=1;return i.push(s),i},SVGTextLottieElement.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var r=t.shapes[0];if(r.it){var i=r.it[r.it.length-1];i.s&&(i.s.k[0]=e,i.s.k[1]=e)}}return t},SVGTextLottieElement.prototype.buildNewText=function(){this.addDynamicProperty(this);var t,e,r=this.textProperty.currentData;this.renderedLetters=createSizedArray(r?r.l.length:0),r.fc?this.layerElement.setAttribute("fill",this.buildColor(r.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),r.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(r.sc)),this.layerElement.setAttribute("stroke-width",r.sw)),this.layerElement.setAttribute("font-size",r.finalSize);var i=this.globalData.fontManager.getFontByName(r.f);if(i.fClass)this.layerElement.setAttribute("class",i.fClass);else{this.layerElement.setAttribute("font-family",i.fFamily);var s=r.fWeight,n=r.fStyle;this.layerElement.setAttribute("font-style",n),this.layerElement.setAttribute("font-weight",s)}this.layerElement.setAttribute("aria-label",r.t);var a=r.l||[],h=!!this.globalData.fontManager.chars;e=a.length;var o,p=this.mHelper,y="",P=this.data.singleShape,f=0,b=0,g=!0,m=r.tr*.001*r.finalSize;if(P&&!h&&!r.sz){var C=this.textContainer,u="start";switch(r.j){case 1:u="end";break;case 2:u="middle";break;default:u="start";break}C.setAttribute("text-anchor",u),C.setAttribute("letter-spacing",m);var c=this.buildTextContents(r.finalText);for(e=c.length,b=r.ps?r.ps[1]+r.ascent:0,t=0;t<e;t+=1)o=this.textSpans[t].span||createNS("tspan"),o.textContent=c[t],o.setAttribute("x",0),o.setAttribute("y",b),o.style.display="inherit",C.appendChild(o),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=o,b+=r.finalLineHeight;this.layerElement.appendChild(C)}else{var l=this.textSpans.length,d;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!h||!P||t===0){if(o=l>t?this.textSpans[t].span:createNS(h?"g":"text"),l<=t){if(o.setAttribute("stroke-linecap","butt"),o.setAttribute("stroke-linejoin","round"),o.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=o,h){var E=createNS("g");o.appendChild(E),this.textSpans[t].childSpan=E}this.textSpans[t].span=o,this.layerElement.appendChild(o)}o.style.display="inherit"}if(p.reset(),P&&(a[t].n&&(f=-m,b+=r.yOffset,b+=g?1:0,g=!1),this.applyTextPropertiesToMatrix(r,p,a[t].line,f,b),f+=a[t].l||0,f+=m),h){d=this.globalData.fontManager.getCharData(r.finalText[t],i.fStyle,this.globalData.fontManager.getFontByName(r.f).fFamily);var _;if(d.t===1)_=new SVGCompElement(d.data,this.globalData,this);else{var T=emptyShapeData;d.data&&d.data.shapes&&(T=this.buildShapeData(d.data,r.finalSize)),_=new SVGShapeElement(T,this.globalData,this)}if(this.textSpans[t].glyph){var F=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(F.layerElement),F.destroy()}this.textSpans[t].glyph=_,_._debug=!0,_.prepareFrame(0),_.renderFrame(),this.textSpans[t].childSpan.appendChild(_.layerElement),d.t===1&&this.textSpans[t].childSpan.setAttribute("transform","scale("+r.finalSize/100+","+r.finalSize/100+")")}else P&&o.setAttribute("transform","translate("+p.props[12]+","+p.props[13]+")"),o.textContent=a[t].val,o.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}P&&o&&o.setAttribute("d",y)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var t,e=this.textSpans.length,r;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<e;t+=1)r=this.textSpans[t].glyph,r&&(r.prepareFrame(this.comp.renderedFrame-this.data.st),r._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if(this.validateText(),(!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){this._sizeChanged=!0;var t,e,r=this.textAnimator.renderedLetters,i=this.textProperty.currentData.l;e=i.length;var s,n,a;for(t=0;t<e;t+=1)i[t].n||(s=r[t],n=this.textSpans[t].span,a=this.textSpans[t].glyph,a&&a.renderFrame(),s._mdf.m&&n.setAttribute("transform",s.m),s._mdf.o&&n.setAttribute("opacity",s.o),s._mdf.sw&&n.setAttribute("stroke-width",s.sw),s._mdf.sc&&n.setAttribute("stroke",s.sc),s._mdf.fc&&n.setAttribute("fill",s.fc))}};function ISolidElement(t,e,r){this.initElement(t,e,r)}extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var t=createNS("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)};function NullElement(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initFrame(),this.initTransform(t,e,r),this.initHierarchy()}NullElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement);function SVGRendererBase(){}extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(t){return new NullElement(t,this.globalData,this)},SVGRendererBase.prototype.createShape=function(t){return new SVGShapeElement(t,this.globalData,this)},SVGRendererBase.prototype.createText=function(t){return new SVGTextLottieElement(t,this.globalData,this)},SVGRendererBase.prototype.createImage=function(t){return new IImageElement(t,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(t){return new ISolidElement(t,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),this.renderConfig.focusable!==void 0&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var r=createNS("clipPath"),i=createNS("rect");i.setAttribute("width",t.w),i.setAttribute("height",t.h),i.setAttribute("x",0),i.setAttribute("y",0);var s=createElementID();r.setAttribute("id",s),r.appendChild(i),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")"),e.appendChild(r),this.layers=t.layers,this.elements=createSizedArray(t.layers.length)},SVGRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var t,e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.findIndexByInd=function(t){var e=0,r=this.layers.length;for(e=0;e<r;e+=1)if(this.layers[e].ind===t)return e;return-1},SVGRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!(e[t]||this.layers[t].ty===99)){e[t]=!0;var r=this.createItem(this.layers[t]);if(e[t]=r,getExpressionsPlugin()&&(this.layers[t].ty===0&&this.globalData.projectInterface.registerComposition(r),r.initExpressions()),this.appendElementInPos(r,t),this.layers[t].tt){var i="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(i===-1)return;if(!this.elements[i]||this.elements[i]===!0)this.buildItem(i),this.addPendingElement(r);else{var s=e[i],n=s.getMatte(this.layers[t].tt);r.setMatte(n)}}}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,r=this.elements.length;e<r;){if(this.elements[e]===t){var i="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,s=this.elements[i],n=s.getMatte(this.layers[e].tt);t.setMatte(n);break}e+=1}}},SVGRendererBase.prototype.renderFrame=function(t){if(!(this.renderedFrame===t||this.destroyed)){t===null?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var e,r=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=r-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<r;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(t,e){var r=t.getBaseElement();if(r){for(var i=0,s;i<e;)this.elements[i]&&this.elements[i]!==!0&&this.elements[i].getBaseElement()&&(s=this.elements[i].getBaseElement()),i+=1;s?this.layerElement.insertBefore(r,s):this.layerElement.appendChild(r)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"};function ICompElement(){}extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initTransform(t,e,r),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),(this.data.xt||!e.progressiveLoad)&&this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),!(!this.isInRange&&!this.data.xt)){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var r,i=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&(this.elements[r].prepareFrame(this.renderedFrame-this.layers[r].st),this.elements[r]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},ICompElement.prototype.setElements=function(t){this.elements=t},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()};function SVGCompElement(t,e,r){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)};function SVGRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var r="";if(e&&e.title){var i=createNS("title"),s=createElementID();i.setAttribute("id",s),i.textContent=e.title,this.svgElement.appendChild(i),r+=s}if(e&&e.description){var n=createNS("desc"),a=createElementID();n.setAttribute("id",a),n.textContent=e.description,this.svgElement.appendChild(n),r+=" "+a}r&&this.svgElement.setAttribute("aria-labelledby",r);var h=createNS("defs");this.svgElement.appendChild(h);var o=createNS("g");this.svgElement.appendChild(o),this.layerElement=o,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&e.hideOnTransparent===!1),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||e.runExpressions===void 0||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:h,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)};function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}ShapeTransformManager.prototype={addTransformSequence:function(e){var r,i=e.length,s="_";for(r=0;r<i;r+=1)s+=e[r].transform.key+"_";var n=this.sequences[s];return n||(n={transforms:[].concat(e),finalTransform:new Matrix,_mdf:!1},this.sequences[s]=n,this.sequenceList.push(n)),n},processSequence:function(e,r){for(var i=0,s=e.transforms.length,n=r;i<s&&!r;){if(e.transforms[i].transform.mProps._mdf){n=!0;break}i+=1}if(n)for(e.finalTransform.reset(),i=s-1;i>=0;i-=1)e.finalTransform.multiply(e.transforms[i].transform.mProps.v);e._mdf=n},processSequences:function(e){var r,i=this.sequenceList.length;for(r=0;r<i;r+=1)this.processSequence(this.sequenceList[r],e)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}};var lumaLoader=function(){var e="__lottie_element_luma_buffer",r=null,i=null,s=null;function n(){var o=createNS("svg"),p=createNS("filter"),y=createNS("feColorMatrix");return p.setAttribute("id",e),y.setAttribute("type","matrix"),y.setAttribute("color-interpolation-filters","sRGB"),y.setAttribute("values","0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0, 0.3, 0.3, 0.3, 0, 0"),p.appendChild(y),o.appendChild(p),o.setAttribute("id",e+"_svg"),featureSupport.svgLumaHidden&&(o.style.display="none"),o}function a(){r||(s=n(),document.body.appendChild(s),r=createTag("canvas"),i=r.getContext("2d"),i.filter="url(#"+e+")",i.fillStyle="rgba(0,0,0,0)",i.fillRect(0,0,1,1))}function h(o){return r||a(),r.width=o.width,r.height=o.height,i.filter="url(#"+e+")",r}return{load:a,get:h}};function createCanvas(t,e){if(featureSupport.offscreenCanvas)return new OffscreenCanvas(t,e);var r=createTag("canvas");return r.width=t,r.height=e,r}var assetLoader=function(){return{loadLumaCanvas:lumaLoader.load,getLumaCanvas:lumaLoader.get,createCanvas}}(),registeredEffects={};function CVEffects(t){var e,r=t.data.ef?t.data.ef.length:0;this.filters=[];var i;for(e=0;e<r;e+=1){i=null;var s=t.data.ef[e].ty;if(registeredEffects[s]){var n=registeredEffects[s].effect;i=new n(t.effectsManager.effectElements[e],t)}i&&this.filters.push(i)}this.filters.length&&t.addRenderableComponent(this)}CVEffects.prototype.renderFrame=function(t){var e,r=this.filters.length;for(e=0;e<r;e+=1)this.filters[e].renderFrame(t)},CVEffects.prototype.getEffects=function(t){var e,r=this.filters.length,i=[];for(e=0;e<r;e+=1)this.filters[e].type===t&&i.push(this.filters[e]);return i};function registerEffect(t,e){registeredEffects[t]={effect:e}}function CVMaskElement(t,e){this.data=t,this.element=e,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);var r,i=this.masksProperties.length,s=!1;for(r=0;r<i;r+=1)this.masksProperties[r].mode!=="n"&&(s=!0),this.viewData[r]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[r],3);this.hasMasks=s,s&&this.element.addRenderableComponent(this)}CVMaskElement.prototype.renderFrame=function(){if(this.hasMasks){var t=this.element.finalTransform.mat,e=this.element.canvasContext,r,i=this.masksProperties.length,s,n,a;for(e.beginPath(),r=0;r<i;r+=1)if(this.masksProperties[r].mode!=="n"){this.masksProperties[r].inv&&(e.moveTo(0,0),e.lineTo(this.element.globalData.compSize.w,0),e.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),e.lineTo(0,this.element.globalData.compSize.h),e.lineTo(0,0)),a=this.viewData[r].v,s=t.applyToPointArray(a.v[0][0],a.v[0][1],0),e.moveTo(s[0],s[1]);var h,o=a._length;for(h=1;h<o;h+=1)n=t.applyToTriplePoints(a.o[h-1],a.i[h],a.v[h]),e.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5]);n=t.applyToTriplePoints(a.o[h-1],a.i[0],a.v[0]),e.bezierCurveTo(n[0],n[1],n[2],n[3],n[4],n[5])}this.element.globalData.renderer.save(!0),e.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null};function CVBaseElement(){}var operationsMap={1:"source-in",2:"source-out",3:"source-in",4:"source-out"};CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){if(this.data.tt>=1){this.buffers=[];var e=this.globalData.canvasContext,r=assetLoader.createCanvas(e.canvas.width,e.canvas.height);this.buffers.push(r);var i=assetLoader.createCanvas(e.canvas.width,e.canvas.height);this.buffers.push(i),this.data.tt>=3&&!document._isProxy&&assetLoader.loadLumaCanvas()}this.canvasContext=this.globalData.canvasContext,this.transformCanvas=this.globalData.transformCanvas,this.renderableEffectsManager=new CVEffects(this),this.searchEffectTransforms()},createContent:function(){},setBlendMode:function(){var e=this.globalData;if(e.blendMode!==this.data.bm){e.blendMode=this.data.bm;var r=getBlendMode(this.data.bm);e.canvasContext.globalCompositeOperation=r}},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this),this.transformEffects=this.renderableEffectsManager.getEffects(effectTypes.TRANSFORM_EFFECT)},hideElement:function(){!this.hidden&&(!this.isInRange||this.isTransparent)&&(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},clearCanvas:function(e){e.clearRect(this.transformCanvas.tx,this.transformCanvas.ty,this.transformCanvas.w*this.transformCanvas.sx,this.transformCanvas.h*this.transformCanvas.sy)},prepareLayer:function(){if(this.data.tt>=1){var e=this.buffers[0],r=e.getContext("2d");this.clearCanvas(r),r.drawImage(this.canvasContext.canvas,0,0),this.currentTransform=this.canvasContext.getTransform(),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform)}},exitLayer:function(){if(this.data.tt>=1){var e=this.buffers[1],r=e.getContext("2d");this.clearCanvas(r),r.drawImage(this.canvasContext.canvas,0,0),this.canvasContext.setTransform(1,0,0,1,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.setTransform(this.currentTransform);var i=this.comp.getElementById("tp"in this.data?this.data.tp:this.data.ind-1);if(i.renderFrame(!0),this.canvasContext.setTransform(1,0,0,1,0,0),this.data.tt>=3&&!document._isProxy){var s=assetLoader.getLumaCanvas(this.canvasContext.canvas),n=s.getContext("2d");n.drawImage(this.canvasContext.canvas,0,0),this.clearCanvas(this.canvasContext),this.canvasContext.drawImage(s,0,0)}this.canvasContext.globalCompositeOperation=operationsMap[this.data.tt],this.canvasContext.drawImage(e,0,0),this.canvasContext.globalCompositeOperation="destination-over",this.canvasContext.drawImage(this.buffers[0],0,0),this.canvasContext.setTransform(this.currentTransform),this.canvasContext.globalCompositeOperation="source-over"}},renderFrame:function(e){if(!(this.hidden||this.data.hd)&&!(this.data.td===1&&!e)){this.renderTransform(),this.renderRenderable(),this.renderLocalTransform(),this.setBlendMode();var r=this.data.ty===0;this.prepareLayer(),this.globalData.renderer.save(r),this.globalData.renderer.ctxTransform(this.finalTransform.localMat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.localOpacity),this.renderInnerContent(),this.globalData.renderer.restore(r),this.exitLayer(),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement;function CVShapeData(t,e,r,i){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var s=4;e.ty==="rc"?s=5:e.ty==="el"?s=6:e.ty==="sr"&&(s=7),this.sh=ShapePropertyFactory.getShapeProp(t,e,s,t);var n,a=r.length,h;for(n=0;n<a;n+=1)r[n].closed||(h={transforms:i.addTransformSequence(r[n].transforms),trNodes:[]},this.styledShapes.push(h),r[n].elements.push(h))}CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated;function CVShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(t,e){var r={data:t,type:t.ty,preTransforms:this.transformsManager.addTransformSequence(e),transforms:[],elements:[],closed:t.hd===!0},i={};if(t.ty==="fl"||t.ty==="st"?(i.c=PropertyFactory.getProp(this,t.c,1,255,this),i.c.k||(r.co="rgb("+bmFloor(i.c.v[0])+","+bmFloor(i.c.v[1])+","+bmFloor(i.c.v[2])+")")):(t.ty==="gf"||t.ty==="gs")&&(i.s=PropertyFactory.getProp(this,t.s,1,null,this),i.e=PropertyFactory.getProp(this,t.e,1,null,this),i.h=PropertyFactory.getProp(this,t.h||{k:0},0,.01,this),i.a=PropertyFactory.getProp(this,t.a||{k:0},0,degToRads,this),i.g=new GradientProperty(this,t.g,this)),i.o=PropertyFactory.getProp(this,t.o,0,.01,this),t.ty==="st"||t.ty==="gs"){if(r.lc=lineCapEnum[t.lc||2],r.lj=lineJoinEnum[t.lj||2],t.lj==1&&(r.ml=t.ml),i.w=PropertyFactory.getProp(this,t.w,0,null,this),i.w.k||(r.wi=i.w.v),t.d){var s=new DashProperty(this,t.d,"canvas",this);i.d=s,i.d.k||(r.da=i.d.dashArray,r.do=i.d.dashoffset[0])}}else r.r=t.r===2?"evenodd":"nonzero";return this.stylesList.push(r),i.style=r,i},CVShapeElement.prototype.createGroupElement=function(){var t={it:[],prevViewData:[]};return t},CVShapeElement.prototype.createTransformElement=function(t){var e={transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,t.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,t,this)}};return e},CVShapeElement.prototype.createShapeElement=function(t){var e=new CVShapeData(this,t,this.stylesList,this.transformsManager);return this.shapes.push(e),this.addShapeToModifiers(e),e},CVShapeElement.prototype.reloadShapes=function(){this._isFirstFrame=!0;var t,e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(t){var e,r=this.stylesList.length;for(e=0;e<r;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.push(t)},CVShapeElement.prototype.removeTransformFromStyleList=function(){var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].closed||this.stylesList[t].transforms.pop()},CVShapeElement.prototype.closeStyles=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e].closed=!0},CVShapeElement.prototype.searchShapes=function(t,e,r,i,s){var n,a=t.length-1,h,o,p=[],y=[],P,f,b,g=[].concat(s);for(n=a;n>=0;n-=1){if(P=this.searchProcessedElement(t[n]),P?e[n]=r[P-1]:t[n]._shouldRender=i,t[n].ty==="fl"||t[n].ty==="st"||t[n].ty==="gf"||t[n].ty==="gs")P?e[n].style.closed=!1:e[n]=this.createStyleElement(t[n],g),p.push(e[n].style);else if(t[n].ty==="gr"){if(!P)e[n]=this.createGroupElement(t[n]);else for(o=e[n].it.length,h=0;h<o;h+=1)e[n].prevViewData[h]=e[n].it[h];this.searchShapes(t[n].it,e[n].it,e[n].prevViewData,i,g)}else t[n].ty==="tr"?(P||(b=this.createTransformElement(t[n]),e[n]=b),g.push(e[n]),this.addTransformToStyleList(e[n])):t[n].ty==="sh"||t[n].ty==="rc"||t[n].ty==="el"||t[n].ty==="sr"?P||(e[n]=this.createShapeElement(t[n])):t[n].ty==="tm"||t[n].ty==="rd"||t[n].ty==="pb"||t[n].ty==="zz"||t[n].ty==="op"?(P?(f=e[n],f.closed=!1):(f=ShapeModifiers.getModifier(t[n].ty),f.init(this,t[n]),e[n]=f,this.shapeModifiers.push(f)),y.push(f)):t[n].ty==="rp"&&(P?(f=e[n],f.closed=!0):(f=ShapeModifiers.getModifier(t[n].ty),e[n]=f,f.init(this,t,n,e),this.shapeModifiers.push(f),i=!1),y.push(f));this.addProcessedElement(t[n],n+1)}for(this.removeTransformFromStyleList(),this.closeStyles(p),a=y.length,n=0;n<a;n+=1)y[n].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(t,e){(t._opMdf||e.op._mdf||this._isFirstFrame)&&(e.opacity=t.opacity,e.opacity*=e.op.v,e._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){var t,e=this.stylesList.length,r,i,s,n,a,h,o=this.globalData.renderer,p=this.globalData.canvasContext,y,P;for(t=0;t<e;t+=1)if(P=this.stylesList[t],y=P.type,!((y==="st"||y==="gs")&&P.wi===0||!P.data._shouldRender||P.coOp===0||this.globalData.currentGlobalAlpha===0)){for(o.save(),a=P.elements,y==="st"||y==="gs"?(o.ctxStrokeStyle(y==="st"?P.co:P.grd),o.ctxLineWidth(P.wi),o.ctxLineCap(P.lc),o.ctxLineJoin(P.lj),o.ctxMiterLimit(P.ml||0)):o.ctxFillStyle(y==="fl"?P.co:P.grd),o.ctxOpacity(P.coOp),y!=="st"&&y!=="gs"&&p.beginPath(),o.ctxTransform(P.preTransforms.finalTransform.props),i=a.length,r=0;r<i;r+=1){for((y==="st"||y==="gs")&&(p.beginPath(),P.da&&(p.setLineDash(P.da),p.lineDashOffset=P.do)),h=a[r].trNodes,n=h.length,s=0;s<n;s+=1)h[s].t==="m"?p.moveTo(h[s].p[0],h[s].p[1]):h[s].t==="c"?p.bezierCurveTo(h[s].pts[0],h[s].pts[1],h[s].pts[2],h[s].pts[3],h[s].pts[4],h[s].pts[5]):p.closePath();(y==="st"||y==="gs")&&(o.ctxStroke(),P.da&&p.setLineDash(this.dashResetter))}y!=="st"&&y!=="gs"&&this.globalData.renderer.ctxFill(P.r),o.restore()}},CVShapeElement.prototype.renderShape=function(t,e,r,i){var s,n=e.length-1,a;for(a=t,s=n;s>=0;s-=1)e[s].ty==="tr"?(a=r[s].transform,this.renderShapeTransform(t,a)):e[s].ty==="sh"||e[s].ty==="el"||e[s].ty==="rc"||e[s].ty==="sr"?this.renderPath(e[s],r[s]):e[s].ty==="fl"?this.renderFill(e[s],r[s],a):e[s].ty==="st"?this.renderStroke(e[s],r[s],a):e[s].ty==="gf"||e[s].ty==="gs"?this.renderGradientFill(e[s],r[s],a):e[s].ty==="gr"?this.renderShape(a,e[s].it,r[s].it):e[s].ty;i&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(t,e){if(this._isFirstFrame||e._mdf||t.transforms._mdf){var r=t.trNodes,i=e.paths,s,n,a,h=i._length;r.length=0;var o=t.transforms.finalTransform;for(a=0;a<h;a+=1){var p=i.shapes[a];if(p&&p.v){for(n=p._length,s=1;s<n;s+=1)s===1&&r.push({t:"m",p:o.applyToPointArray(p.v[0][0],p.v[0][1],0)}),r.push({t:"c",pts:o.applyToTriplePoints(p.o[s-1],p.i[s],p.v[s])});n===1&&r.push({t:"m",p:o.applyToPointArray(p.v[0][0],p.v[0][1],0)}),p.c&&n&&(r.push({t:"c",pts:o.applyToTriplePoints(p.o[s-1],p.i[0],p.v[0])}),r.push({t:"z"}))}}t.trNodes=r}},CVShapeElement.prototype.renderPath=function(t,e){if(t.hd!==!0&&t._shouldRender){var r,i=e.styledShapes.length;for(r=0;r<i;r+=1)this.renderStyledShape(e.styledShapes[r],e.sh)}},CVShapeElement.prototype.renderFill=function(t,e,r){var i=e.style;(e.c._mdf||this._isFirstFrame)&&(i.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r._opMdf||this._isFirstFrame)&&(i.coOp=e.o.v*r.opacity)},CVShapeElement.prototype.renderGradientFill=function(t,e,r){var i=e.style,s;if(!i.grd||e.g._mdf||e.s._mdf||e.e._mdf||t.t!==1&&(e.h._mdf||e.a._mdf)){var n=this.globalData.canvasContext,a=e.s.v,h=e.e.v;if(t.t===1)s=n.createLinearGradient(a[0],a[1],h[0],h[1]);else{var o=Math.sqrt(Math.pow(a[0]-h[0],2)+Math.pow(a[1]-h[1],2)),p=Math.atan2(h[1]-a[1],h[0]-a[0]),y=e.h.v;y>=1?y=.99:y<=-1&&(y=-.99);var P=o*y,f=Math.cos(p+e.a.v)*P+a[0],b=Math.sin(p+e.a.v)*P+a[1];s=n.createRadialGradient(f,b,0,a[0],a[1],o)}var g,m=t.g.p,C=e.g.c,u=1;for(g=0;g<m;g+=1)e.g._hasOpacity&&e.g._collapsable&&(u=e.g.o[g*2+1]),s.addColorStop(C[g*4]/100,"rgba("+C[g*4+1]+","+C[g*4+2]+","+C[g*4+3]+","+u+")");i.grd=s}i.coOp=e.o.v*r.opacity},CVShapeElement.prototype.renderStroke=function(t,e,r){var i=e.style,s=e.d;s&&(s._mdf||this._isFirstFrame)&&(i.da=s.dashArray,i.do=s.dashoffset[0]),(e.c._mdf||this._isFirstFrame)&&(i.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r._opMdf||this._isFirstFrame)&&(i.coOp=e.o.v*r.opacity),(e.w._mdf||this._isFirstFrame)&&(i.wi=e.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0};function CVTextElement(t,e,r){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=!1;t.fc?(e=!0,this.values.fill=this.buildColor(t.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=e;var r=!1;t.sc&&(r=!0,this.values.stroke=this.buildColor(t.sc),this.values.sWidth=t.sw);var i=this.globalData.fontManager.getFontByName(t.f),s,n,a=t.l,h=this.mHelper;this.stroke=r,this.values.fValue=t.finalSize+"px "+this.globalData.fontManager.getFontByName(t.f).fFamily,n=t.finalText.length;var o,p,y,P,f,b,g,m,C,u,c=this.data.singleShape,l=t.tr*.001*t.finalSize,d=0,E=0,_=!0,T=0;for(s=0;s<n;s+=1){o=this.globalData.fontManager.getCharData(t.finalText[s],i.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily),p=o&&o.data||{},h.reset(),c&&a[s].n&&(d=-l,E+=t.yOffset,E+=_?1:0,_=!1),f=p.shapes?p.shapes[0].it:[],g=f.length,h.scale(t.finalSize/100,t.finalSize/100),c&&this.applyTextPropertiesToMatrix(t,h,a[s].line,d,E),C=createSizedArray(g-1);var F=0;for(b=0;b<g;b+=1)if(f[b].ty==="sh"){for(P=f[b].ks.k.i.length,m=f[b].ks.k,u=[],y=1;y<P;y+=1)y===1&&u.push(h.applyToX(m.v[0][0],m.v[0][1],0),h.applyToY(m.v[0][0],m.v[0][1],0)),u.push(h.applyToX(m.o[y-1][0],m.o[y-1][1],0),h.applyToY(m.o[y-1][0],m.o[y-1][1],0),h.applyToX(m.i[y][0],m.i[y][1],0),h.applyToY(m.i[y][0],m.i[y][1],0),h.applyToX(m.v[y][0],m.v[y][1],0),h.applyToY(m.v[y][0],m.v[y][1],0));u.push(h.applyToX(m.o[y-1][0],m.o[y-1][1],0),h.applyToY(m.o[y-1][0],m.o[y-1][1],0),h.applyToX(m.i[0][0],m.i[0][1],0),h.applyToY(m.i[0][0],m.i[0][1],0),h.applyToX(m.v[0][0],m.v[0][1],0),h.applyToY(m.v[0][0],m.v[0][1],0)),C[F]=u,F+=1}c&&(d+=a[s].l,d+=l),this.textSpans[T]?this.textSpans[T].elem=C:this.textSpans[T]={elem:C},T+=1}},CVTextElement.prototype.renderInnerContent=function(){this.validateText();var t=this.canvasContext;t.font=this.values.fValue,this.globalData.renderer.ctxLineCap("butt"),this.globalData.renderer.ctxLineJoin("miter"),this.globalData.renderer.ctxMiterLimit(4),this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var e,r,i,s,n,a,h=this.textAnimator.renderedLetters,o=this.textProperty.currentData.l;r=o.length;var p,y=null,P=null,f=null,b,g,m=this.globalData.renderer;for(e=0;e<r;e+=1)if(!o[e].n){if(p=h[e],p&&(m.save(),m.ctxTransform(p.p),m.ctxOpacity(p.o)),this.fill){for(p&&p.fc?y!==p.fc&&(m.ctxFillStyle(p.fc),y=p.fc):y!==this.values.fill&&(y=this.values.fill,m.ctxFillStyle(this.values.fill)),b=this.textSpans[e].elem,s=b.length,this.globalData.canvasContext.beginPath(),i=0;i<s;i+=1)for(g=b[i],a=g.length,this.globalData.canvasContext.moveTo(g[0],g[1]),n=2;n<a;n+=6)this.globalData.canvasContext.bezierCurveTo(g[n],g[n+1],g[n+2],g[n+3],g[n+4],g[n+5]);this.globalData.canvasContext.closePath(),m.ctxFill()}if(this.stroke){for(p&&p.sw?f!==p.sw&&(f=p.sw,m.ctxLineWidth(p.sw)):f!==this.values.sWidth&&(f=this.values.sWidth,m.ctxLineWidth(this.values.sWidth)),p&&p.sc?P!==p.sc&&(P=p.sc,m.ctxStrokeStyle(p.sc)):P!==this.values.stroke&&(P=this.values.stroke,m.ctxStrokeStyle(this.values.stroke)),b=this.textSpans[e].elem,s=b.length,this.globalData.canvasContext.beginPath(),i=0;i<s;i+=1)for(g=b[i],a=g.length,this.globalData.canvasContext.moveTo(g[0],g[1]),n=2;n<a;n+=6)this.globalData.canvasContext.bezierCurveTo(g[n],g[n+1],g[n+2],g[n+3],g[n+4],g[n+5]);this.globalData.canvasContext.closePath(),m.ctxStroke()}p&&this.globalData.renderer.restore()}};function CVImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.img=e.imageLoader.getAsset(this.assetData),this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var t=createTag("canvas");t.width=this.assetData.w,t.height=this.assetData.h;var e=t.getContext("2d"),r=this.img.width,i=this.img.height,s=r/i,n=this.assetData.w/this.assetData.h,a,h,o=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;s>n&&o==="xMidYMid slice"||s<n&&o!=="xMidYMid slice"?(h=i,a=h*n):(a=r,h=a/n),e.drawImage(this.img,(r-a)/2,(i-h)/2,a,h,0,0,this.assetData.w,this.assetData.h),this.img=t}},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null};function CVSolidElement(t,e,r){this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){this.globalData.renderer.ctxFillStyle(this.data.sc),this.globalData.renderer.ctxFillRect(0,0,this.data.sw,this.data.sh)};function CanvasRendererBase(){}extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(t){return new CVShapeElement(t,this.globalData,this)},CanvasRendererBase.prototype.createText=function(t){return new CVTextElement(t,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(t){return new CVImageElement(t,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(t){return new CVSolidElement(t,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(t){t[0]===1&&t[1]===0&&t[4]===0&&t[5]===1&&t[12]===0&&t[13]===0||this.canvasContext.transform(t[0],t[1],t[4],t[5],t[12],t[13])},CanvasRendererBase.prototype.ctxOpacity=function(t){this.canvasContext.globalAlpha*=t<0?0:t},CanvasRendererBase.prototype.ctxFillStyle=function(t){this.canvasContext.fillStyle=t},CanvasRendererBase.prototype.ctxStrokeStyle=function(t){this.canvasContext.strokeStyle=t},CanvasRendererBase.prototype.ctxLineWidth=function(t){this.canvasContext.lineWidth=t},CanvasRendererBase.prototype.ctxLineCap=function(t){this.canvasContext.lineCap=t},CanvasRendererBase.prototype.ctxLineJoin=function(t){this.canvasContext.lineJoin=t},CanvasRendererBase.prototype.ctxMiterLimit=function(t){this.canvasContext.miterLimit=t},CanvasRendererBase.prototype.ctxFill=function(t){this.canvasContext.fill(t)},CanvasRendererBase.prototype.ctxFillRect=function(t,e,r,i){this.canvasContext.fillRect(t,e,r,i)},CanvasRendererBase.prototype.ctxStroke=function(){this.canvasContext.stroke()},CanvasRendererBase.prototype.reset=function(){if(!this.renderConfig.clearCanvas){this.canvasContext.restore();return}this.contextData.reset()},CanvasRendererBase.prototype.save=function(){this.canvasContext.save()},CanvasRendererBase.prototype.restore=function(t){if(!this.renderConfig.clearCanvas){this.canvasContext.restore();return}t&&(this.globalData.blendMode="source-over"),this.contextData.restore(t)},CanvasRendererBase.prototype.configAnimation=function(t){if(this.animationItem.wrapper){this.animationItem.container=createTag("canvas");var e=this.animationItem.container.style;e.width="100%",e.height="100%";var r="0px 0px 0px";e.transformOrigin=r,e.mozTransformOrigin=r,e.webkitTransformOrigin=r,e["-webkit-transform"]=r,e.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.contextData.setContext(this.canvasContext),this.data=t,this.layers=t.layers,this.transformCanvas={w:t.w,h:t.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(t,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(t.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(t,e){this.reset();var r,i;t?(r=t,i=e,this.canvasContext.canvas.width=r,this.canvasContext.canvas.height=i):(this.animationItem.wrapper&&this.animationItem.container?(r=this.animationItem.wrapper.offsetWidth,i=this.animationItem.wrapper.offsetHeight):(r=this.canvasContext.canvas.width,i=this.canvasContext.canvas.height),this.canvasContext.canvas.width=r*this.renderConfig.dpr,this.canvasContext.canvas.height=i*this.renderConfig.dpr);var s,n;if(this.renderConfig.preserveAspectRatio.indexOf("meet")!==-1||this.renderConfig.preserveAspectRatio.indexOf("slice")!==-1){var a=this.renderConfig.preserveAspectRatio.split(" "),h=a[1]||"meet",o=a[0]||"xMidYMid",p=o.substr(0,4),y=o.substr(4);s=r/i,n=this.transformCanvas.w/this.transformCanvas.h,n>s&&h==="meet"||n<s&&h==="slice"?(this.transformCanvas.sx=r/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=i/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.h/this.renderConfig.dpr)),p==="xMid"&&(n<s&&h==="meet"||n>s&&h==="slice")?this.transformCanvas.tx=(r-this.transformCanvas.w*(i/this.transformCanvas.h))/2*this.renderConfig.dpr:p==="xMax"&&(n<s&&h==="meet"||n>s&&h==="slice")?this.transformCanvas.tx=(r-this.transformCanvas.w*(i/this.transformCanvas.h))*this.renderConfig.dpr:this.transformCanvas.tx=0,y==="YMid"&&(n>s&&h==="meet"||n<s&&h==="slice")?this.transformCanvas.ty=(i-this.transformCanvas.h*(r/this.transformCanvas.w))/2*this.renderConfig.dpr:y==="YMax"&&(n>s&&h==="meet"||n<s&&h==="slice")?this.transformCanvas.ty=(i-this.transformCanvas.h*(r/this.transformCanvas.w))*this.renderConfig.dpr:this.transformCanvas.ty=0}else this.renderConfig.preserveAspectRatio==="none"?(this.transformCanvas.sx=r/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr,this.transformCanvas.tx=0,this.transformCanvas.ty=0);this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText="");var t,e=this.layers?this.layers.length:0;for(t=e-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(t,e){if(!(this.renderedFrame===t&&this.renderConfig.clearCanvas===!0&&!e||this.destroyed||t===-1)){this.renderedFrame=t,this.globalData.frameNum=t-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||e,this.globalData.projectInterface.currentFrame=t;var r,i=this.layers.length;for(this.completeLayers||this.checkLayers(t),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&this.elements[r].prepareFrame(t-this.layers[r].st);if(this.globalData._mdf){for(this.renderConfig.clearCanvas===!0?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&this.elements[r].renderFrame();this.renderConfig.clearCanvas!==!0&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!(e[t]||this.layers[t].ty===99)){var r=this.createItem(this.layers[t],this,this.globalData);e[t]=r,r.initExpressions()}},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();t.checkParenting()}},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"};function CanvasContext(){this.opacity=-1,this.transform=createTypedArray("float32",16),this.fillStyle="",this.strokeStyle="",this.lineWidth="",this.lineCap="",this.lineJoin="",this.miterLimit="",this.id=Math.random()}function CVContextData(){this.stack=[],this.cArrPos=0,this.cTr=new Matrix;var t,e=15;for(t=0;t<e;t+=1){var r=new CanvasContext;this.stack[t]=r}this._length=e,this.nativeContext=null,this.transformMat=new Matrix,this.currentOpacity=1,this.currentFillStyle="",this.appliedFillStyle="",this.currentStrokeStyle="",this.appliedStrokeStyle="",this.currentLineWidth="",this.appliedLineWidth="",this.currentLineCap="",this.appliedLineCap="",this.currentLineJoin="",this.appliedLineJoin="",this.appliedMiterLimit="",this.currentMiterLimit=""}CVContextData.prototype.duplicate=function(){var t=this._length*2,e=0;for(e=this._length;e<t;e+=1)this.stack[e]=new CanvasContext;this._length=t},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.stack[this.cArrPos].opacity=1},CVContextData.prototype.restore=function(t){this.cArrPos-=1;var e=this.stack[this.cArrPos],r=e.transform,i,s=this.cTr.props;for(i=0;i<16;i+=1)s[i]=r[i];if(t){this.nativeContext.restore();var n=this.stack[this.cArrPos+1];this.appliedFillStyle=n.fillStyle,this.appliedStrokeStyle=n.strokeStyle,this.appliedLineWidth=n.lineWidth,this.appliedLineCap=n.lineCap,this.appliedLineJoin=n.lineJoin,this.appliedMiterLimit=n.miterLimit}this.nativeContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13]),(t||e.opacity!==-1&&this.currentOpacity!==e.opacity)&&(this.nativeContext.globalAlpha=e.opacity,this.currentOpacity=e.opacity),this.currentFillStyle=e.fillStyle,this.currentStrokeStyle=e.strokeStyle,this.currentLineWidth=e.lineWidth,this.currentLineCap=e.lineCap,this.currentLineJoin=e.lineJoin,this.currentMiterLimit=e.miterLimit},CVContextData.prototype.save=function(t){t&&this.nativeContext.save();var e=this.cTr.props;this._length<=this.cArrPos&&this.duplicate();var r=this.stack[this.cArrPos],i;for(i=0;i<16;i+=1)r.transform[i]=e[i];this.cArrPos+=1;var s=this.stack[this.cArrPos];s.opacity=r.opacity,s.fillStyle=r.fillStyle,s.strokeStyle=r.strokeStyle,s.lineWidth=r.lineWidth,s.lineCap=r.lineCap,s.lineJoin=r.lineJoin,s.miterLimit=r.miterLimit},CVContextData.prototype.setOpacity=function(t){this.stack[this.cArrPos].opacity=t},CVContextData.prototype.setContext=function(t){this.nativeContext=t},CVContextData.prototype.fillStyle=function(t){this.stack[this.cArrPos].fillStyle!==t&&(this.currentFillStyle=t,this.stack[this.cArrPos].fillStyle=t)},CVContextData.prototype.strokeStyle=function(t){this.stack[this.cArrPos].strokeStyle!==t&&(this.currentStrokeStyle=t,this.stack[this.cArrPos].strokeStyle=t)},CVContextData.prototype.lineWidth=function(t){this.stack[this.cArrPos].lineWidth!==t&&(this.currentLineWidth=t,this.stack[this.cArrPos].lineWidth=t)},CVContextData.prototype.lineCap=function(t){this.stack[this.cArrPos].lineCap!==t&&(this.currentLineCap=t,this.stack[this.cArrPos].lineCap=t)},CVContextData.prototype.lineJoin=function(t){this.stack[this.cArrPos].lineJoin!==t&&(this.currentLineJoin=t,this.stack[this.cArrPos].lineJoin=t)},CVContextData.prototype.miterLimit=function(t){this.stack[this.cArrPos].miterLimit!==t&&(this.currentMiterLimit=t,this.stack[this.cArrPos].miterLimit=t)},CVContextData.prototype.transform=function(t){this.transformMat.cloneFromProps(t);var e=this.cTr;this.transformMat.multiply(e),e.cloneFromProps(this.transformMat.props);var r=e.props;this.nativeContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13])},CVContextData.prototype.opacity=function(t){var e=this.stack[this.cArrPos].opacity;e*=t<0?0:t,this.stack[this.cArrPos].opacity!==e&&(this.currentOpacity!==t&&(this.nativeContext.globalAlpha=t,this.currentOpacity=t),this.stack[this.cArrPos].opacity=e)},CVContextData.prototype.fill=function(t){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fill(t)},CVContextData.prototype.fillRect=function(t,e,r,i){this.appliedFillStyle!==this.currentFillStyle&&(this.appliedFillStyle=this.currentFillStyle,this.nativeContext.fillStyle=this.appliedFillStyle),this.nativeContext.fillRect(t,e,r,i)},CVContextData.prototype.stroke=function(){this.appliedStrokeStyle!==this.currentStrokeStyle&&(this.appliedStrokeStyle=this.currentStrokeStyle,this.nativeContext.strokeStyle=this.appliedStrokeStyle),this.appliedLineWidth!==this.currentLineWidth&&(this.appliedLineWidth=this.currentLineWidth,this.nativeContext.lineWidth=this.appliedLineWidth),this.appliedLineCap!==this.currentLineCap&&(this.appliedLineCap=this.currentLineCap,this.nativeContext.lineCap=this.appliedLineCap),this.appliedLineJoin!==this.currentLineJoin&&(this.appliedLineJoin=this.currentLineJoin,this.nativeContext.lineJoin=this.appliedLineJoin),this.appliedMiterLimit!==this.currentMiterLimit&&(this.appliedMiterLimit=this.currentMiterLimit,this.nativeContext.miterLimit=this.appliedMiterLimit),this.nativeContext.stroke()};function CVCompElement(t,e,r){this.completeLayers=!1,this.layers=t.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var t=this.canvasContext;t.beginPath(),t.moveTo(0,0),t.lineTo(this.data.w,0),t.lineTo(this.data.w,this.data.h),t.lineTo(0,this.data.h),t.lineTo(0,0),t.clip();var e,r=this.layers.length;for(e=r-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()},CVCompElement.prototype.destroy=function(){var t,e=this.layers.length;for(t=e-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)};function CanvasRenderer(t,e){this.animationItem=t,this.renderConfig={clearCanvas:e&&e.clearCanvas!==void 0?e.clearCanvas:!0,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||"",runExpressions:!e||e.runExpressions===void 0||e.runExpressions},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||self.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas",this.renderConfig.clearCanvas&&(this.ctxTransform=this.contextData.transform.bind(this.contextData),this.ctxOpacity=this.contextData.opacity.bind(this.contextData),this.ctxFillStyle=this.contextData.fillStyle.bind(this.contextData),this.ctxStrokeStyle=this.contextData.strokeStyle.bind(this.contextData),this.ctxLineWidth=this.contextData.lineWidth.bind(this.contextData),this.ctxLineCap=this.contextData.lineCap.bind(this.contextData),this.ctxLineJoin=this.contextData.lineJoin.bind(this.contextData),this.ctxMiterLimit=this.contextData.miterLimit.bind(this.contextData),this.ctxFill=this.contextData.fill.bind(this.contextData),this.ctxFillRect=this.contextData.fillRect.bind(this.contextData),this.ctxStroke=this.contextData.stroke.bind(this.contextData),this.save=this.contextData.save.bind(this.contextData))}extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)};function HBaseElement(){}HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),this.data.bm!==0&&this.setBlendMode()},renderElement:function(){var e=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var r=this.finalTransform.mat.toCSS();e.transform=r,e.webkitTransform=r}this.finalTransform._opMdf&&(e.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting;function HSolidElement(t,e,r){this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var t;this.data.hasMask?(t=createNS("rect"),t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):(t=createTag("div"),t.style.width=this.data.sw+"px",t.style.height=this.data.sh+"px",t.style.backgroundColor=this.data.sc),this.layerElement.appendChild(t)};function HShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(t,e,r),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var t;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),t=this.svgElement;else{t=createNS("svg");var e=this.comp.data?this.comp.data:this.globalData.compSize;t.setAttribute("width",e.w),t.setAttribute("height",e.h),t.appendChild(this.shapesContainer),this.layerElement.appendChild(t)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=t},HShapeElement.prototype.getTransformedPoint=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)e=t[r].mProps.v.applyToPointArray(e[0],e[1],0);return e},HShapeElement.prototype.calculateShapeBoundingBox=function(t,e){var r=t.sh.v,i=t.transformers,s,n=r._length,a,h,o,p;if(!(n<=1)){for(s=0;s<n-1;s+=1)a=this.getTransformedPoint(i,r.v[s]),h=this.getTransformedPoint(i,r.o[s]),o=this.getTransformedPoint(i,r.i[s+1]),p=this.getTransformedPoint(i,r.v[s+1]),this.checkBounds(a,h,o,p,e);r.c&&(a=this.getTransformedPoint(i,r.v[s]),h=this.getTransformedPoint(i,r.o[s]),o=this.getTransformedPoint(i,r.i[0]),p=this.getTransformedPoint(i,r.v[0]),this.checkBounds(a,h,o,p,e))}},HShapeElement.prototype.checkBounds=function(t,e,r,i,s){this.getBoundsOfCurve(t,e,r,i);var n=this.shapeBoundingBox;s.x=bmMin(n.left,s.x),s.xMax=bmMax(n.right,s.xMax),s.y=bmMin(n.top,s.y),s.yMax=bmMax(n.bottom,s.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(t,e,r,i){for(var s=[[t[0],i[0]],[t[1],i[1]]],n,a,h,o,p,y,P,f=0;f<2;++f)a=6*t[f]-12*e[f]+6*r[f],n=-3*t[f]+9*e[f]-9*r[f]+3*i[f],h=3*e[f]-3*t[f],a|=0,n|=0,h|=0,n===0&&a===0||(n===0?(o=-h/a,o>0&&o<1&&s[f].push(this.calculateF(o,t,e,r,i,f))):(p=a*a-4*h*n,p>=0&&(y=(-a+bmSqrt(p))/(2*n),y>0&&y<1&&s[f].push(this.calculateF(y,t,e,r,i,f)),P=(-a-bmSqrt(p))/(2*n),P>0&&P<1&&s[f].push(this.calculateF(P,t,e,r,i,f)))));this.shapeBoundingBox.left=bmMin.apply(null,s[0]),this.shapeBoundingBox.top=bmMin.apply(null,s[1]),this.shapeBoundingBox.right=bmMax.apply(null,s[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,s[1])},HShapeElement.prototype.calculateF=function(t,e,r,i,s,n){return bmPow(1-t,3)*e[n]+3*bmPow(1-t,2)*t*r[n]+3*(1-t)*bmPow(t,2)*i[n]+bmPow(t,3)*s[n]},HShapeElement.prototype.calculateBoundingBox=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)t[r]&&t[r].sh?this.calculateShapeBoundingBox(t[r],e):t[r]&&t[r].it?this.calculateBoundingBox(t[r].it,e):t[r]&&t[r].style&&t[r].w&&this.expandStrokeBoundingBox(t[r].w,e)},HShapeElement.prototype.expandStrokeBoundingBox=function(t,e){var r=0;if(t.keyframes){for(var i=0;i<t.keyframes.length;i+=1){var s=t.keyframes[i].s;s>r&&(r=s)}r*=t.mult}else r=t.v*t.mult;e.x-=r,e.xMax+=r,e.y-=r,e.yMax+=r},HShapeElement.prototype.currentBoxContains=function(t){return this.currentBBox.x<=t.x&&this.currentBBox.y<=t.y&&this.currentBBox.width+this.currentBBox.x>=t.x+t.width&&this.currentBBox.height+this.currentBBox.y>=t.y+t.height},HShapeElement.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var t=this.tempBoundingBox,e=999999;if(t.x=e,t.xMax=-e,t.y=e,t.yMax=-e,this.calculateBoundingBox(this.itemsData,t),t.width=t.xMax<t.x?0:t.xMax-t.x,t.height=t.yMax<t.y?0:t.yMax-t.y,this.currentBoxContains(t))return;var r=!1;if(this.currentBBox.w!==t.width&&(this.currentBBox.w=t.width,this.shapeCont.setAttribute("width",t.width),r=!0),this.currentBBox.h!==t.height&&(this.currentBBox.h=t.height,this.shapeCont.setAttribute("height",t.height),r=!0),r||this.currentBBox.x!==t.x||this.currentBBox.y!==t.y){this.currentBBox.w=t.width,this.currentBBox.h=t.height,this.currentBBox.x=t.x,this.currentBBox.y=t.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var i=this.shapeCont.style,s="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";i.transform=s,i.webkitTransform=s}}};function HTextElement(t,e,r){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var t=createNS("g");this.maskedElement.appendChild(t),this.innerElem=t}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},HTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=this.innerElem.style,r=t.fc?this.buildColor(t.fc):"rgba(0,0,0,0)";e.fill=r,e.color=r,t.sc&&(e.stroke=this.buildColor(t.sc),e.strokeWidth=t.sw+"px");var i=this.globalData.fontManager.getFontByName(t.f);if(!this.globalData.fontManager.chars)if(e.fontSize=t.finalSize+"px",e.lineHeight=t.finalSize+"px",i.fClass)this.innerElem.className=i.fClass;else{e.fontFamily=i.fFamily;var s=t.fWeight,n=t.fStyle;e.fontStyle=n,e.fontWeight=s}var a,h,o=t.l;h=o.length;var p,y,P,f=this.mHelper,b,g="",m=0;for(a=0;a<h;a+=1){if(this.globalData.fontManager.chars?(this.textPaths[m]?p=this.textPaths[m]:(p=createNS("path"),p.setAttribute("stroke-linecap",lineCapEnum[1]),p.setAttribute("stroke-linejoin",lineJoinEnum[2]),p.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[m]?(y=this.textSpans[m],P=y.children[0]):(y=createTag("div"),y.style.lineHeight=0,P=createNS("svg"),P.appendChild(p),styleDiv(y)))):this.isMasked?p=this.textPaths[m]?this.textPaths[m]:createNS("text"):this.textSpans[m]?(y=this.textSpans[m],p=this.textPaths[m]):(y=createTag("span"),styleDiv(y),p=createTag("span"),styleDiv(p),y.appendChild(p)),this.globalData.fontManager.chars){var C=this.globalData.fontManager.getCharData(t.finalText[a],i.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily),u;if(C?u=C.data:u=null,f.reset(),u&&u.shapes&&u.shapes.length&&(b=u.shapes[0].it,f.scale(t.finalSize/100,t.finalSize/100),g=this.createPathShape(f,b),p.setAttribute("d",g)),this.isMasked)this.innerElem.appendChild(p);else{if(this.innerElem.appendChild(y),u&&u.shapes){document.body.appendChild(P);var c=P.getBBox();P.setAttribute("width",c.width+2),P.setAttribute("height",c.height+2),P.setAttribute("viewBox",c.x-1+" "+(c.y-1)+" "+(c.width+2)+" "+(c.height+2));var l=P.style,d="translate("+(c.x-1)+"px,"+(c.y-1)+"px)";l.transform=d,l.webkitTransform=d,o[a].yOffset=c.y-1}else P.setAttribute("width",1),P.setAttribute("height",1);y.appendChild(P)}}else if(p.textContent=o[a].val,p.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(p);else{this.innerElem.appendChild(y);var E=p.style,_="translate3d(0,"+-t.finalSize/1.2+"px,0)";E.transform=_,E.webkitTransform=_}this.isMasked?this.textSpans[m]=p:this.textSpans[m]=y,this.textSpans[m].style.display="block",this.textPaths[m]=p,m+=1}for(;m<this.textSpans.length;)this.textSpans[m].style.display="none",m+=1},HTextElement.prototype.renderInnerContent=function(){this.validateText();var t;if(this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),t=this.svgElement.style;var e="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";t.transform=e,t.webkitTransform=e}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),!(!this.lettersChangedFlag&&!this.textAnimator.lettersChangedFlag)){var r,i,s=0,n=this.textAnimator.renderedLetters,a=this.textProperty.currentData.l;i=a.length;var h,o,p;for(r=0;r<i;r+=1)a[r].n?s+=1:(o=this.textSpans[r],p=this.textPaths[r],h=n[s],s+=1,h._mdf.m&&(this.isMasked?o.setAttribute("transform",h.m):(o.style.webkitTransform=h.m,o.style.transform=h.m)),o.style.opacity=h.o,h.sw&&h._mdf.sw&&p.setAttribute("stroke-width",h.sw),h.sc&&h._mdf.sc&&p.setAttribute("stroke",h.sc),h.fc&&h._mdf.fc&&(p.setAttribute("fill",h.fc),p.style.color=h.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var y=this.innerElem.getBBox();this.currentBBox.w!==y.width&&(this.currentBBox.w=y.width,this.svgElement.setAttribute("width",y.width)),this.currentBBox.h!==y.height&&(this.currentBBox.h=y.height,this.svgElement.setAttribute("height",y.height));var P=1;if(this.currentBBox.w!==y.width+P*2||this.currentBBox.h!==y.height+P*2||this.currentBBox.x!==y.x-P||this.currentBBox.y!==y.y-P){this.currentBBox.w=y.width+P*2,this.currentBBox.h=y.height+P*2,this.currentBBox.x=y.x-P,this.currentBBox.y=y.y-P,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.svgElement.style;var f="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";t.transform=f,t.webkitTransform=f}}}};function HCameraElement(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initHierarchy();var i=PropertyFactory.getProp;if(this.pe=i(this,t.pe,0,0,this),t.ks.p.s?(this.px=i(this,t.ks.p.x,1,0,this),this.py=i(this,t.ks.p.y,1,0,this),this.pz=i(this,t.ks.p.z,1,0,this)):this.p=i(this,t.ks.p,1,0,this),t.ks.a&&(this.a=i(this,t.ks.a,1,0,this)),t.ks.or.k.length&&t.ks.or.k[0].to){var s,n=t.ks.or.k.length;for(s=0;s<n;s+=1)t.ks.or.k[s].to=null,t.ks.or.k[s].ti=null}this.or=i(this,t.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=i(this,t.ks.rx,0,degToRads,this),this.ry=i(this,t.ks.ry,0,degToRads,this),this.rz=i(this,t.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){var t,e=this.comp.threeDElements.length,r,i,s;for(t=0;t<e;t+=1)if(r=this.comp.threeDElements[t],r.type==="3d"){i=r.perspectiveElem.style,s=r.container.style;var n=this.pe.v+"px",a="0px 0px 0px",h="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";i.perspective=n,i.webkitPerspective=n,s.transformOrigin=a,s.mozTransformOrigin=a,s.webkitTransformOrigin=a,i.transform=h,i.webkitTransform=h}},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var t=this._isFirstFrame,e,r;if(this.hierarchy)for(r=this.hierarchy.length,e=0;e<r;e+=1)t=this.hierarchy[e].finalTransform.mProp._mdf||t;if(t||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(r=this.hierarchy.length-1,e=r;e>=0;e-=1){var i=this.hierarchy[e].finalTransform.mProp;this.mat.translate(-i.p.v[0],-i.p.v[1],i.p.v[2]),this.mat.rotateX(-i.or.v[0]).rotateY(-i.or.v[1]).rotateZ(i.or.v[2]),this.mat.rotateX(-i.rx.v).rotateY(-i.ry.v).rotateZ(i.rz.v),this.mat.scale(1/i.s.v[0],1/i.s.v[1],1/i.s.v[2]),this.mat.translate(i.a.v[0],i.a.v[1],i.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var s;this.p?s=[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:s=[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]];var n=Math.sqrt(Math.pow(s[0],2)+Math.pow(s[1],2)+Math.pow(s[2],2)),a=[s[0]/n,s[1]/n,s[2]/n],h=Math.sqrt(a[2]*a[2]+a[0]*a[0]),o=Math.atan2(a[1],h),p=Math.atan2(a[0],-a[2]);this.mat.rotateY(p).rotateX(-o)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var y=!this._prevMat.equals(this.mat);if((y||this.pe._mdf)&&this.comp.threeDElements){r=this.comp.threeDElements.length;var P,f,b;for(e=0;e<r;e+=1)if(P=this.comp.threeDElements[e],P.type==="3d"){if(y){var g=this.mat.toCSS();b=P.container.style,b.transform=g,b.webkitTransform=g}this.pe._mdf&&(f=P.perspectiveElem.style,f.perspective=this.pe.v+"px",f.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null};function HImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,r)}extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData),e=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(e),e.crossOrigin="anonymous",e.src=t,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)};function HybridRendererBase(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&e.hideOnTransparent===!1),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();t.checkParenting()}},HybridRendererBase.prototype.appendElementInPos=function(t,e){var r=t.getBaseElement();if(r){var i=this.layers[e];if(!i.ddd||!this.supports3d)if(this.threeDElements)this.addTo3dContainer(r,e);else{for(var s=0,n,a,h;s<e;)this.elements[s]&&this.elements[s]!==!0&&this.elements[s].getBaseElement&&(a=this.elements[s],h=this.layers[s].ddd?this.getThreeDContainerByPos(s):a.getBaseElement(),n=h||n),s+=1;n?(!i.ddd||!this.supports3d)&&this.layerElement.insertBefore(r,n):(!i.ddd||!this.supports3d)&&this.layerElement.appendChild(r)}else this.addTo3dContainer(r,e)}},HybridRendererBase.prototype.createShape=function(t){return this.supports3d?new HShapeElement(t,this.globalData,this):new SVGShapeElement(t,this.globalData,this)},HybridRendererBase.prototype.createText=function(t){return this.supports3d?new HTextElement(t,this.globalData,this):new SVGTextLottieElement(t,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(t){return this.camera=new HCameraElement(t,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(t){return this.supports3d?new HImageElement(t,this.globalData,this):new IImageElement(t,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(t){return this.supports3d?new HSolidElement(t,this.globalData,this):new ISolidElement(t,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(t){for(var e=0,r=this.threeDElements.length;e<r;){if(this.threeDElements[e].startPos<=t&&this.threeDElements[e].endPos>=t)return this.threeDElements[e].perspectiveElem;e+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(t,e){var r=createTag("div"),i,s;styleDiv(r);var n=createTag("div");if(styleDiv(n),e==="3d"){i=r.style,i.width=this.globalData.compSize.w+"px",i.height=this.globalData.compSize.h+"px";var a="50% 50%";i.webkitTransformOrigin=a,i.mozTransformOrigin=a,i.transformOrigin=a,s=n.style;var h="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";s.transform=h,s.webkitTransform=h}r.appendChild(n);var o={container:n,perspectiveElem:r,startPos:t,endPos:t,type:e};return this.threeDElements.push(o),o},HybridRendererBase.prototype.build3dContainers=function(){var t,e=this.layers.length,r,i="";for(t=0;t<e;t+=1)this.layers[t].ddd&&this.layers[t].ty!==3?(i!=="3d"&&(i="3d",r=this.createThreeDContainer(t,"3d")),r.endPos=Math.max(r.endPos,t)):(i!=="2d"&&(i="2d",r=this.createThreeDContainer(t,"2d")),r.endPos=Math.max(r.endPos,t));for(e=this.threeDElements.length,t=e-1;t>=0;t-=1)this.resizerElem.appendChild(this.threeDElements[t].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(t,e){for(var r=0,i=this.threeDElements.length;r<i;){if(e<=this.threeDElements[r].endPos){for(var s=this.threeDElements[r].startPos,n;s<e;)this.elements[s]&&this.elements[s].getBaseElement&&(n=this.elements[s].getBaseElement()),s+=1;n?this.threeDElements[r].container.insertBefore(t,n):this.threeDElements[r].container.appendChild(t);break}r+=1}},HybridRendererBase.prototype.configAnimation=function(t){var e=createTag("div"),r=this.animationItem.wrapper,i=e.style;i.width=t.w+"px",i.height=t.h+"px",this.resizerElem=e,styleDiv(e),i.transformStyle="flat",i.mozTransformStyle="flat",i.webkitTransformStyle="flat",this.renderConfig.className&&e.setAttribute("class",this.renderConfig.className),r.appendChild(e),i.overflow="hidden";var s=createNS("svg");s.setAttribute("width","1"),s.setAttribute("height","1"),styleDiv(s),this.resizerElem.appendChild(s);var n=createNS("defs");s.appendChild(n),this.data=t,this.setupGlobalData(t,s),this.globalData.defs=n,this.layers=t.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var t,e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var t=this.animationItem.wrapper.offsetWidth,e=this.animationItem.wrapper.offsetHeight,r=t/e,i=this.globalData.compSize.w/this.globalData.compSize.h,s,n,a,h;i>r?(s=t/this.globalData.compSize.w,n=t/this.globalData.compSize.w,a=0,h=(e-this.globalData.compSize.h*(t/this.globalData.compSize.w))/2):(s=e/this.globalData.compSize.h,n=e/this.globalData.compSize.h,a=(t-this.globalData.compSize.w*(e/this.globalData.compSize.h))/2,h=0);var o=this.resizerElem.style;o.webkitTransform="matrix3d("+s+",0,0,0,0,"+n+",0,0,0,0,1,0,"+a+","+h+",0,1)",o.transform=o.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var t=this.globalData.compSize.w,e=this.globalData.compSize.h,r,i=this.threeDElements.length;for(r=0;r<i;r+=1){var s=this.threeDElements[r].perspectiveElem.style;s.webkitPerspective=Math.sqrt(Math.pow(t,2)+Math.pow(e,2))+"px",s.perspective=s.webkitPerspective}}},HybridRendererBase.prototype.searchExtraCompositions=function(t){var e,r=t.length,i=createTag("div");for(e=0;e<r;e+=1)if(t[e].xt){var s=this.createComp(t[e],i,this.globalData.comp,null);s.initExpressions(),this.globalData.projectInterface.registerComposition(s)}};function HCompElement(t,e,r){this.layers=t.layers,this.supports3d=!t.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(t,e){for(var r=0,i;r<e;)this.elements[r]&&this.elements[r].getBaseElement&&(i=this.elements[r].getBaseElement()),r+=1;i?this.layerElement.insertBefore(t,i):this.layerElement.appendChild(t)},HCompElement.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)};function HybridRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&e.hideOnTransparent===!1),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"},runExpressions:!e||e.runExpressions===void 0||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)};var CompExpressionInterface=function(){return function(t){function e(r){for(var i=0,s=t.layers.length;i<s;){if(t.layers[i].nm===r||t.layers[i].ind===r)return t.elements[i].layerInterface;i+=1}return null}return Object.defineProperty(e,"_name",{value:t.data.nm}),e.layer=e,e.pixelAspect=1,e.height=t.data.h||t.globalData.compSize.h,e.width=t.data.w||t.globalData.compSize.w,e.pixelAspect=1,e.frameDuration=1/t.globalData.frameRate,e.displayStartTime=0,e.numLayers=t.layers.length,e}}();function _typeof$2(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$2=function(r){return typeof r}:_typeof$2=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$2(t)}function seedRandom(t,e){var r=this,i=256,s=6,n=52,a="random",h=e.pow(i,s),o=e.pow(2,n),p=o*2,y=i-1,P;function f(l,d,E){var _=[];d=d===!0?{entropy:!0}:d||{};var T=C(m(d.entropy?[l,c(t)]:l===null?u():l,3),_),F=new b(_),R=function(){for(var w=F.g(s),k=h,D=0;w<o;)w=(w+D)*i,k*=i,D=F.g(1);for(;w>=p;)w/=2,k/=2,D>>>=1;return(w+D)/k};return R.int32=function(){return F.g(4)|0},R.quick=function(){return F.g(4)/4294967296},R.double=R,C(c(F.S),t),(d.pass||E||function(G,w,k,D){return D&&(D.S&&g(D,F),G.state=function(){return g(F,{})}),k?(e[a]=G,w):G})(R,T,"global"in d?d.global:this==e,d.state)}e["seed"+a]=f;function b(l){var d,E=l.length,_=this,T=0,F=_.i=_.j=0,R=_.S=[];for(E||(l=[E++]);T<i;)R[T]=T++;for(T=0;T<i;T++)R[T]=R[F=y&F+l[T%E]+(d=R[T])],R[F]=d;_.g=function(G){for(var w,k=0,D=_.i,V=_.j,L=_.S;G--;)w=L[D=y&D+1],k=k*i+L[y&(L[D]=L[V=y&V+w])+(L[V]=w)];return _.i=D,_.j=V,k}}function g(l,d){return d.i=l.i,d.j=l.j,d.S=l.S.slice(),d}function m(l,d){var E=[],_=_typeof$2(l),T;if(d&&_=="object")for(T in l)try{E.push(m(l[T],d-1))}catch{}return E.length?E:_=="string"?l:l+"\0"}function C(l,d){for(var E=l+"",_,T=0;T<E.length;)d[y&T]=y&(_^=d[y&T]*19)+E.charCodeAt(T++);return c(d)}function u(){try{if(P)return c(P.randomBytes(i));var l=new Uint8Array(i);return(r.crypto||r.msCrypto).getRandomValues(l),c(l)}catch{var d=r.navigator,E=d&&d.plugins;return[+new Date,r,E,r.screen,c(t)]}}function c(l){return String.fromCharCode.apply(0,l)}C(e.random(),t)}function initialize$2(t){seedRandom([],t)}var propTypes={SHAPE:"shape"};function _typeof$1(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof$1=function(r){return typeof r}:_typeof$1=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof$1(t)}var ExpressionManager=function(){"use strict";var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null,_lottieGlobal={};initialize$2(BMMath);function resetFrame(){_lottieGlobal={}}function $bm_isInstanceOfArray(t){return t.constructor===Array||t.constructor===Float32Array}function isNumerable(t,e){return t==="number"||e instanceof Number||t==="boolean"||t==="string"}function $bm_neg(t){var e=_typeof$1(t);if(e==="number"||t instanceof Number||e==="boolean")return-t;if($bm_isInstanceOfArray(t)){var r,i=t.length,s=[];for(r=0;r<i;r+=1)s[r]=-t[r];return s}return t.propType?t.v:-t}var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(t,e){var r=_typeof$1(t),i=_typeof$1(e);if(isNumerable(r,t)&&isNumerable(i,e)||r==="string"||i==="string")return t+e;if($bm_isInstanceOfArray(t)&&isNumerable(i,e))return t=t.slice(0),t[0]+=e,t;if(isNumerable(r,t)&&$bm_isInstanceOfArray(e))return e=e.slice(0),e[0]=t+e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var s=0,n=t.length,a=e.length,h=[];s<n||s<a;)(typeof t[s]=="number"||t[s]instanceof Number)&&(typeof e[s]=="number"||e[s]instanceof Number)?h[s]=t[s]+e[s]:h[s]=e[s]===void 0?t[s]:t[s]||e[s],s+=1;return h}return 0}var add=sum;function sub(t,e){var r=_typeof$1(t),i=_typeof$1(e);if(isNumerable(r,t)&&isNumerable(i,e))return r==="string"&&(t=parseInt(t,10)),i==="string"&&(e=parseInt(e,10)),t-e;if($bm_isInstanceOfArray(t)&&isNumerable(i,e))return t=t.slice(0),t[0]-=e,t;if(isNumerable(r,t)&&$bm_isInstanceOfArray(e))return e=e.slice(0),e[0]=t-e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var s=0,n=t.length,a=e.length,h=[];s<n||s<a;)(typeof t[s]=="number"||t[s]instanceof Number)&&(typeof e[s]=="number"||e[s]instanceof Number)?h[s]=t[s]-e[s]:h[s]=e[s]===void 0?t[s]:t[s]||e[s],s+=1;return h}return 0}function mul(t,e){var r=_typeof$1(t),i=_typeof$1(e),s;if(isNumerable(r,t)&&isNumerable(i,e))return t*e;var n,a;if($bm_isInstanceOfArray(t)&&isNumerable(i,e)){for(a=t.length,s=createTypedArray("float32",a),n=0;n<a;n+=1)s[n]=t[n]*e;return s}if(isNumerable(r,t)&&$bm_isInstanceOfArray(e)){for(a=e.length,s=createTypedArray("float32",a),n=0;n<a;n+=1)s[n]=t*e[n];return s}return 0}function div(t,e){var r=_typeof$1(t),i=_typeof$1(e),s;if(isNumerable(r,t)&&isNumerable(i,e))return t/e;var n,a;if($bm_isInstanceOfArray(t)&&isNumerable(i,e)){for(a=t.length,s=createTypedArray("float32",a),n=0;n<a;n+=1)s[n]=t[n]/e;return s}if(isNumerable(r,t)&&$bm_isInstanceOfArray(e)){for(a=e.length,s=createTypedArray("float32",a),n=0;n<a;n+=1)s[n]=t/e[n];return s}return 0}function mod(t,e){return typeof t=="string"&&(t=parseInt(t,10)),typeof e=="string"&&(e=parseInt(e,10)),t%e}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(t,e,r){if(e>r){var i=r;r=e,e=i}return Math.min(Math.max(t,e),r)}function radiansToDegrees(t){return t/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(t){return t*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(t,e){if(typeof t=="number"||t instanceof Number)return e=e||0,Math.abs(t-e);e||(e=helperLengthArray);var r,i=Math.min(t.length,e.length),s=0;for(r=0;r<i;r+=1)s+=Math.pow(e[r]-t[r],2);return Math.sqrt(s)}function normalize(t){return div(t,length(t))}function rgbToHsl(t){var e=t[0],r=t[1],i=t[2],s=Math.max(e,r,i),n=Math.min(e,r,i),a,h,o=(s+n)/2;if(s===n)a=0,h=0;else{var p=s-n;switch(h=o>.5?p/(2-s-n):p/(s+n),s){case e:a=(r-i)/p+(r<i?6:0);break;case r:a=(i-e)/p+2;break;case i:a=(e-r)/p+4;break;default:break}a/=6}return[a,h,o,t[3]]}function hue2rgb(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+(e-t)*6*r:r<1/2?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function hslToRgb(t){var e=t[0],r=t[1],i=t[2],s,n,a;if(r===0)s=i,a=i,n=i;else{var h=i<.5?i*(1+r):i+r-i*r,o=2*i-h;s=hue2rgb(o,h,e+1/3),n=hue2rgb(o,h,e),a=hue2rgb(o,h,e-1/3)}return[s,n,a,t[3]]}function linear(t,e,r,i,s){if((i===void 0||s===void 0)&&(i=e,s=r,e=0,r=1),r<e){var n=r;r=e,e=n}if(t<=e)return i;if(t>=r)return s;var a=r===e?0:(t-e)/(r-e);if(!i.length)return i+(s-i)*a;var h,o=i.length,p=createTypedArray("float32",o);for(h=0;h<o;h+=1)p[h]=i[h]+(s[h]-i[h])*a;return p}function random(t,e){if(e===void 0&&(t===void 0?(t=0,e=1):(e=t,t=void 0)),e.length){var r,i=e.length;t||(t=createTypedArray("float32",i));var s=createTypedArray("float32",i),n=BMMath.random();for(r=0;r<i;r+=1)s[r]=t[r]+n*(e[r]-t[r]);return s}t===void 0&&(t=0);var a=BMMath.random();return t+a*(e-t)}function createPath(t,e,r,i){var s,n=t.length,a=shapePool.newElement();a.setPathData(!!i,n);var h=[0,0],o,p;for(s=0;s<n;s+=1)o=e&&e[s]?e[s]:h,p=r&&r[s]?r[s]:h,a.setTripleAt(t[s][0],t[s][1],p[0]+t[s][0],p[1]+t[s][1],o[0]+t[s][0],o[1]+t[s][1],s,!0);return a}function initiateExpression(elem,data,property){function noOp(t){return t}if(!elem.globalData.renderConfig.runExpressions)return noOp;var val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=val.indexOf("random")!==-1,elemType=elem.data.ty,transform,$bm_transform,content,effect,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||this.data.hd!==!0,wiggle=function t(e,r){var i,s,n=this.pv.length?this.pv.length:1,a=createTypedArray("float32",n);e=5;var h=Math.floor(time*e);for(i=0,s=0;i<h;){for(s=0;s<n;s+=1)a[s]+=-r+r*2*BMMath.random();i+=1}var o=time*e,p=o-Math.floor(o),y=createTypedArray("float32",n);if(n>1){for(s=0;s<n;s+=1)y[s]=this.pv[s]+a[s]+(-r+r*2*BMMath.random())*p;return y}return this.pv+a[0]+(-r+r*2*BMMath.random())*p}.bind(this);thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty));function loopInDuration(t,e){return loopIn(t,e,!0)}function loopOutDuration(t,e){return loopOut(t,e,!0)}this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface);function lookAt(t,e){var r=[e[0]-t[0],e[1]-t[1],e[2]-t[2]],i=Math.atan2(r[0],Math.sqrt(r[1]*r[1]+r[2]*r[2]))/degToRads,s=-Math.atan2(r[1],r[2])/degToRads;return[s,i,0]}function easeOut(t,e,r,i,s){return applyEase(easeOutBez,t,e,r,i,s)}function easeIn(t,e,r,i,s){return applyEase(easeInBez,t,e,r,i,s)}function ease(t,e,r,i,s){return applyEase(easeInOutBez,t,e,r,i,s)}function applyEase(t,e,r,i,s,n){s===void 0?(s=r,n=i):e=(e-r)/(i-r),e>1?e=1:e<0&&(e=0);var a=t(e);if($bm_isInstanceOfArray(s)){var h,o=s.length,p=createTypedArray("float32",o);for(h=0;h<o;h+=1)p[h]=(n[h]-s[h])*a+s[h];return p}return(n-s)*a+s}function nearestKey(t){var e,r=data.k.length,i,s;if(!data.k.length||typeof data.k[0]=="number")i=0,s=0;else if(i=-1,t*=elem.comp.globalData.frameRate,t<data.k[0].t)i=1,s=data.k[0].t;else{for(e=0;e<r-1;e+=1)if(t===data.k[e].t){i=e+1,s=data.k[e].t;break}else if(t>data.k[e].t&&t<data.k[e+1].t){t-data.k[e].t>data.k[e+1].t-t?(i=e+2,s=data.k[e+1].t):(i=e+1,s=data.k[e].t);break}i===-1&&(i=e+1,s=data.k[e].t)}var n={};return n.index=i,n.time=s/elem.comp.globalData.frameRate,n}function key(t){var e,r,i;if(!data.k.length||typeof data.k[0]=="number")throw new Error("The property has no keyframe at index "+t);t-=1,e={time:data.k[t].t/elem.comp.globalData.frameRate,value:[]};var s=Object.prototype.hasOwnProperty.call(data.k[t],"s")?data.k[t].s:data.k[t-1].e;for(i=s.length,r=0;r<i;r+=1)e[r]=s[r],e.value[r]=s[r];return e}function framesToTime(t,e){return e||(e=elem.comp.globalData.frameRate),t/e}function timeToFrames(t,e){return!t&&t!==0&&(t=time),e||(e=elem.comp.globalData.frameRate),t*e}function seedRandom(t){BMMath.seedrandom(randSeed+t)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(t,e){return typeof value=="string"?e===void 0?value.substring(t):value.substring(t,e):""}function substr(t,e){return typeof value=="string"?e===void 0?value.substr(t):value.substr(t,e):""}function posterizeTime(t){time=t===0?0:Math.floor(time*t)/t,value=valueAtTime(time)}var time,velocity,value,text,textIndex,textTotal,selectorValue,index=elem.data.ind,hasParent=!!(elem.hierarchy&&elem.hierarchy.length),parent,randSeed=Math.floor(Math.random()*1e6),globalData=elem.globalData;function executeExpression(t){return value=t,this.frameExpressionId===elem.globalData.frameId&&this.propType!=="textSelector"?value:(this.propType==="textSelector"&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),transform||(transform=elem.layerInterface("ADBE Transform Group"),$bm_transform=transform,transform&&(anchorPoint=transform.anchorPoint)),elemType===4&&!content&&(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),hasParent=!!(elem.hierarchy&&elem.hierarchy.length),hasParent&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt,scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath,_lottieGlobal],ob.resetFrame=resetFrame,ob}(),Expressions=function(){var t={};t.initExpressions=e,t.resetFrame=ExpressionManager.resetFrame;function e(r){var i=0,s=[];function n(){i+=1}function a(){i-=1,i===0&&o()}function h(p){s.indexOf(p)===-1&&s.push(p)}function o(){var p,y=s.length;for(p=0;p<y;p+=1)s[p].release();s.length=0}r.renderer.compInterface=CompExpressionInterface(r.renderer),r.renderer.globalData.projectInterface.registerComposition(r.renderer),r.renderer.globalData.pushExpression=n,r.renderer.globalData.popExpression=a,r.renderer.globalData.registerExpressionProperty=h}return t}(),MaskManagerInterface=function(){function t(r,i){this._mask=r,this._data=i}Object.defineProperty(t.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(t.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),this._mask.op.v*100}});var e=function(i){var s=createSizedArray(i.viewData.length),n,a=i.viewData.length;for(n=0;n<a;n+=1)s[n]=new t(i.viewData[n],i.masksProperties[n]);var h=function(p){for(n=0;n<a;){if(i.masksProperties[n].nm===p)return s[n];n+=1}return null};return h};return e}(),ExpressionPropertyInterface=function(){var t={pv:0,v:0,mult:1},e={pv:[0,0,0],v:[0,0,0],mult:1};function r(a,h,o){Object.defineProperty(a,"velocity",{get:function(){return h.getVelocityAtTime(h.comp.currentFrame)}}),a.numKeys=h.keyframes?h.keyframes.length:0,a.key=function(p){if(!a.numKeys)return 0;var y="";"s"in h.keyframes[p-1]?y=h.keyframes[p-1].s:"e"in h.keyframes[p-2]?y=h.keyframes[p-2].e:y=h.keyframes[p-2].s;var P=o==="unidimensional"?new Number(y):Object.assign({},y);return P.time=h.keyframes[p-1].t/h.elem.comp.globalData.frameRate,P.value=o==="unidimensional"?y[0]:y,P},a.valueAtTime=h.getValueAtTime,a.speedAtTime=h.getSpeedAtTime,a.velocityAtTime=h.getVelocityAtTime,a.propertyGroup=h.propertyGroup}function i(a){(!a||!("pv"in a))&&(a=t);var h=1/a.mult,o=a.pv*h,p=new Number(o);return p.value=o,r(p,a,"unidimensional"),function(){return a.k&&a.getValue(),o=a.v*h,p.value!==o&&(p=new Number(o),p.value=o,r(p,a,"unidimensional")),p}}function s(a){(!a||!("pv"in a))&&(a=e);var h=1/a.mult,o=a.data&&a.data.l||a.pv.length,p=createTypedArray("float32",o),y=createTypedArray("float32",o);return p.value=y,r(p,a,"multidimensional"),function(){a.k&&a.getValue();for(var P=0;P<o;P+=1)y[P]=a.v[P]*h,p[P]=y[P];return p}}function n(){return t}return function(a){return a?a.propType==="unidimensional"?i(a):s(a):n}}(),TransformExpressionInterface=function(){return function(t){function e(a){switch(a){case"scale":case"Scale":case"ADBE Scale":case 6:return e.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return e.rotation;case"ADBE Rotate X":return e.xRotation;case"ADBE Rotate Y":return e.yRotation;case"position":case"Position":case"ADBE Position":case 2:return e.position;case"ADBE Position_0":return e.xPosition;case"ADBE Position_1":return e.yPosition;case"ADBE Position_2":return e.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return e.anchorPoint;case"opacity":case"Opacity":case 11:return e.opacity;default:return null}}Object.defineProperty(e,"rotation",{get:ExpressionPropertyInterface(t.r||t.rz)}),Object.defineProperty(e,"zRotation",{get:ExpressionPropertyInterface(t.rz||t.r)}),Object.defineProperty(e,"xRotation",{get:ExpressionPropertyInterface(t.rx)}),Object.defineProperty(e,"yRotation",{get:ExpressionPropertyInterface(t.ry)}),Object.defineProperty(e,"scale",{get:ExpressionPropertyInterface(t.s)});var r,i,s,n;return t.p?n=ExpressionPropertyInterface(t.p):(r=ExpressionPropertyInterface(t.px),i=ExpressionPropertyInterface(t.py),t.pz&&(s=ExpressionPropertyInterface(t.pz))),Object.defineProperty(e,"position",{get:function(){return t.p?n():[r(),i(),s?s():0]}}),Object.defineProperty(e,"xPosition",{get:ExpressionPropertyInterface(t.px)}),Object.defineProperty(e,"yPosition",{get:ExpressionPropertyInterface(t.py)}),Object.defineProperty(e,"zPosition",{get:ExpressionPropertyInterface(t.pz)}),Object.defineProperty(e,"anchorPoint",{get:ExpressionPropertyInterface(t.a)}),Object.defineProperty(e,"opacity",{get:ExpressionPropertyInterface(t.o)}),Object.defineProperty(e,"skew",{get:ExpressionPropertyInterface(t.sk)}),Object.defineProperty(e,"skewAxis",{get:ExpressionPropertyInterface(t.sa)}),Object.defineProperty(e,"orientation",{get:ExpressionPropertyInterface(t.or)}),e}}(),LayerExpressionInterface=function(){function t(p){var y=new Matrix;if(p!==void 0){var P=this._elem.finalTransform.mProp.getValueAtTime(p);P.clone(y)}else{var f=this._elem.finalTransform.mProp;f.applyToMatrix(y)}return y}function e(p,y){var P=this.getMatrix(y);return P.props[12]=0,P.props[13]=0,P.props[14]=0,this.applyPoint(P,p)}function r(p,y){var P=this.getMatrix(y);return this.applyPoint(P,p)}function i(p,y){var P=this.getMatrix(y);return P.props[12]=0,P.props[13]=0,P.props[14]=0,this.invertPoint(P,p)}function s(p,y){var P=this.getMatrix(y);return this.invertPoint(P,p)}function n(p,y){if(this._elem.hierarchy&&this._elem.hierarchy.length){var P,f=this._elem.hierarchy.length;for(P=0;P<f;P+=1)this._elem.hierarchy[P].finalTransform.mProp.applyToMatrix(p)}return p.applyToPointArray(y[0],y[1],y[2]||0)}function a(p,y){if(this._elem.hierarchy&&this._elem.hierarchy.length){var P,f=this._elem.hierarchy.length;for(P=0;P<f;P+=1)this._elem.hierarchy[P].finalTransform.mProp.applyToMatrix(p)}return p.inversePoint(y)}function h(p){var y=new Matrix;if(y.reset(),this._elem.finalTransform.mProp.applyToMatrix(y),this._elem.hierarchy&&this._elem.hierarchy.length){var P,f=this._elem.hierarchy.length;for(P=0;P<f;P+=1)this._elem.hierarchy[P].finalTransform.mProp.applyToMatrix(y);return y.inversePoint(p)}return y.inversePoint(p)}function o(){return[1,1,1,1]}return function(p){var y;function P(m){b.mask=new MaskManagerInterface(m,p)}function f(m){b.effect=m}function b(m){switch(m){case"ADBE Root Vectors Group":case"Contents":case 2:return b.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return y;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return b.effect;case"ADBE Text Properties":return b.textInterface;default:return null}}b.getMatrix=t,b.invertPoint=a,b.applyPoint=n,b.toWorld=r,b.toWorldVec=e,b.fromWorld=s,b.fromWorldVec=i,b.toComp=r,b.fromComp=h,b.sampleImage=o,b.sourceRectAtTime=p.sourceRectAtTime.bind(p),b._elem=p,y=TransformExpressionInterface(p.finalTransform.mProp);var g=getDescriptor(y,"anchorPoint");return Object.defineProperties(b,{hasParent:{get:function(){return p.hierarchy.length}},parent:{get:function(){return p.hierarchy[0].layerInterface}},rotation:getDescriptor(y,"rotation"),scale:getDescriptor(y,"scale"),position:getDescriptor(y,"position"),opacity:getDescriptor(y,"opacity"),anchorPoint:g,anchor_point:g,transform:{get:function(){return y}},active:{get:function(){return p.isInRange}}}),b.startTime=p.data.st,b.index=p.data.ind,b.source=p.data.refId,b.height=p.data.ty===0?p.data.h:100,b.width=p.data.ty===0?p.data.w:100,b.inPoint=p.data.ip/p.comp.globalData.frameRate,b.outPoint=p.data.op/p.comp.globalData.frameRate,b._name=p.data.nm,b.registerMaskInterface=P,b.registerEffectsInterface=f,b}}(),propertyGroupFactory=function(){return function(t,e){return function(r){return r=r===void 0?1:r,r<=0?t:e(r-1)}}}(),PropertyInterface=function(){return function(t,e){var r={_name:t};function i(s){return s=s===void 0?1:s,s<=0?r:e(s-1)}return i}}(),EffectsExpressionInterface=function(){var t={createEffectsInterface:e};function e(s,n){if(s.effectsManager){var a=[],h=s.data.ef,o,p=s.effectsManager.effectElements.length;for(o=0;o<p;o+=1)a.push(r(h[o],s.effectsManager.effectElements[o],n,s));var y=s.data.ef||[],P=function(b){for(o=0,p=y.length;o<p;){if(b===y[o].nm||b===y[o].mn||b===y[o].ix)return a[o];o+=1}return null};return Object.defineProperty(P,"numProperties",{get:function(){return y.length}}),P}return null}function r(s,n,a,h){function o(b){for(var g=s.ef,m=0,C=g.length;m<C;){if(b===g[m].nm||b===g[m].mn||b===g[m].ix)return g[m].ty===5?y[m]:y[m]();m+=1}throw new Error}var p=propertyGroupFactory(o,a),y=[],P,f=s.ef.length;for(P=0;P<f;P+=1)s.ef[P].ty===5?y.push(r(s.ef[P],n.effectElements[P],n.effectElements[P].propertyGroup,h)):y.push(i(n.effectElements[P],s.ef[P].ty,h,p));return s.mn==="ADBE Color Control"&&Object.defineProperty(o,"color",{get:function(){return y[0]()}}),Object.defineProperties(o,{numProperties:{get:function(){return s.np}},_name:{value:s.nm},propertyGroup:{value:p}}),o.enabled=s.en!==0,o.active=o.enabled,o}function i(s,n,a,h){var o=ExpressionPropertyInterface(s.p);function p(){return n===10?a.comp.compInterface(s.p.v):o()}return s.p.setGroupProperty&&s.p.setGroupProperty(PropertyInterface("",h)),p}return t}(),ShapePathInterface=function(){return function(e,r,i){var s=r.sh;function n(h){return h==="Shape"||h==="shape"||h==="Path"||h==="path"||h==="ADBE Vector Shape"||h===2?n.path:null}var a=propertyGroupFactory(n,i);return s.setGroupProperty(PropertyInterface("Path",a)),Object.defineProperties(n,{path:{get:function(){return s.k&&s.getValue(),s}},shape:{get:function(){return s.k&&s.getValue(),s}},_name:{value:e.nm},ix:{value:e.ix},propertyIndex:{value:e.ix},mn:{value:e.mn},propertyGroup:{value:i}}),n}}(),ShapeExpressionInterface=function(){function t(g,m,C){var u=[],c,l=g?g.length:0;for(c=0;c<l;c+=1)g[c].ty==="gr"?u.push(r(g[c],m[c],C)):g[c].ty==="fl"?u.push(i(g[c],m[c],C)):g[c].ty==="st"?u.push(a(g[c],m[c],C)):g[c].ty==="tm"?u.push(h(g[c],m[c],C)):g[c].ty==="tr"||(g[c].ty==="el"?u.push(p(g[c],m[c],C)):g[c].ty==="sr"?u.push(y(g[c],m[c],C)):g[c].ty==="sh"?u.push(ShapePathInterface(g[c],m[c],C)):g[c].ty==="rc"?u.push(P(g[c],m[c],C)):g[c].ty==="rd"?u.push(f(g[c],m[c],C)):g[c].ty==="rp"?u.push(b(g[c],m[c],C)):g[c].ty==="gf"?u.push(s(g[c],m[c],C)):u.push(n(g[c],m[c],C)));return u}function e(g,m,C){var u,c=function(E){for(var _=0,T=u.length;_<T;){if(u[_]._name===E||u[_].mn===E||u[_].propertyIndex===E||u[_].ix===E||u[_].ind===E)return u[_];_+=1}return typeof E=="number"?u[E-1]:null};c.propertyGroup=propertyGroupFactory(c,C),u=t(g.it,m.it,c.propertyGroup),c.numProperties=u.length;var l=o(g.it[g.it.length-1],m.it[m.it.length-1],c.propertyGroup);return c.transform=l,c.propertyIndex=g.cix,c._name=g.nm,c}function r(g,m,C){var u=function(E){switch(E){case"ADBE Vectors Group":case"Contents":case 2:return u.content;default:return u.transform}};u.propertyGroup=propertyGroupFactory(u,C);var c=e(g,m,u.propertyGroup),l=o(g.it[g.it.length-1],m.it[m.it.length-1],u.propertyGroup);return u.content=c,u.transform=l,Object.defineProperty(u,"_name",{get:function(){return g.nm}}),u.numProperties=g.np,u.propertyIndex=g.ix,u.nm=g.nm,u.mn=g.mn,u}function i(g,m,C){function u(c){return c==="Color"||c==="color"?u.color:c==="Opacity"||c==="opacity"?u.opacity:null}return Object.defineProperties(u,{color:{get:ExpressionPropertyInterface(m.c)},opacity:{get:ExpressionPropertyInterface(m.o)},_name:{value:g.nm},mn:{value:g.mn}}),m.c.setGroupProperty(PropertyInterface("Color",C)),m.o.setGroupProperty(PropertyInterface("Opacity",C)),u}function s(g,m,C){function u(c){return c==="Start Point"||c==="start point"?u.startPoint:c==="End Point"||c==="end point"?u.endPoint:c==="Opacity"||c==="opacity"?u.opacity:null}return Object.defineProperties(u,{startPoint:{get:ExpressionPropertyInterface(m.s)},endPoint:{get:ExpressionPropertyInterface(m.e)},opacity:{get:ExpressionPropertyInterface(m.o)},type:{get:function(){return"a"}},_name:{value:g.nm},mn:{value:g.mn}}),m.s.setGroupProperty(PropertyInterface("Start Point",C)),m.e.setGroupProperty(PropertyInterface("End Point",C)),m.o.setGroupProperty(PropertyInterface("Opacity",C)),u}function n(){function g(){return null}return g}function a(g,m,C){var u=propertyGroupFactory(T,C),c=propertyGroupFactory(_,u);function l(F){Object.defineProperty(_,g.d[F].nm,{get:ExpressionPropertyInterface(m.d.dataProps[F].p)})}var d,E=g.d?g.d.length:0,_={};for(d=0;d<E;d+=1)l(d),m.d.dataProps[d].p.setGroupProperty(c);function T(F){return F==="Color"||F==="color"?T.color:F==="Opacity"||F==="opacity"?T.opacity:F==="Stroke Width"||F==="stroke width"?T.strokeWidth:null}return Object.defineProperties(T,{color:{get:ExpressionPropertyInterface(m.c)},opacity:{get:ExpressionPropertyInterface(m.o)},strokeWidth:{get:ExpressionPropertyInterface(m.w)},dash:{get:function(){return _}},_name:{value:g.nm},mn:{value:g.mn}}),m.c.setGroupProperty(PropertyInterface("Color",u)),m.o.setGroupProperty(PropertyInterface("Opacity",u)),m.w.setGroupProperty(PropertyInterface("Stroke Width",u)),T}function h(g,m,C){function u(l){return l===g.e.ix||l==="End"||l==="end"?u.end:l===g.s.ix?u.start:l===g.o.ix?u.offset:null}var c=propertyGroupFactory(u,C);return u.propertyIndex=g.ix,m.s.setGroupProperty(PropertyInterface("Start",c)),m.e.setGroupProperty(PropertyInterface("End",c)),m.o.setGroupProperty(PropertyInterface("Offset",c)),u.propertyIndex=g.ix,u.propertyGroup=C,Object.defineProperties(u,{start:{get:ExpressionPropertyInterface(m.s)},end:{get:ExpressionPropertyInterface(m.e)},offset:{get:ExpressionPropertyInterface(m.o)},_name:{value:g.nm}}),u.mn=g.mn,u}function o(g,m,C){function u(l){return g.a.ix===l||l==="Anchor Point"?u.anchorPoint:g.o.ix===l||l==="Opacity"?u.opacity:g.p.ix===l||l==="Position"?u.position:g.r.ix===l||l==="Rotation"||l==="ADBE Vector Rotation"?u.rotation:g.s.ix===l||l==="Scale"?u.scale:g.sk&&g.sk.ix===l||l==="Skew"?u.skew:g.sa&&g.sa.ix===l||l==="Skew Axis"?u.skewAxis:null}var c=propertyGroupFactory(u,C);return m.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",c)),m.transform.mProps.p.setGroupProperty(PropertyInterface("Position",c)),m.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",c)),m.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",c)),m.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",c)),m.transform.mProps.sk&&(m.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",c)),m.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",c))),m.transform.op.setGroupProperty(PropertyInterface("Opacity",c)),Object.defineProperties(u,{opacity:{get:ExpressionPropertyInterface(m.transform.mProps.o)},position:{get:ExpressionPropertyInterface(m.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(m.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(m.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(m.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(m.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(m.transform.mProps.sa)},_name:{value:g.nm}}),u.ty="tr",u.mn=g.mn,u.propertyGroup=C,u}function p(g,m,C){function u(d){return g.p.ix===d?u.position:g.s.ix===d?u.size:null}var c=propertyGroupFactory(u,C);u.propertyIndex=g.ix;var l=m.sh.ty==="tm"?m.sh.prop:m.sh;return l.s.setGroupProperty(PropertyInterface("Size",c)),l.p.setGroupProperty(PropertyInterface("Position",c)),Object.defineProperties(u,{size:{get:ExpressionPropertyInterface(l.s)},position:{get:ExpressionPropertyInterface(l.p)},_name:{value:g.nm}}),u.mn=g.mn,u}function y(g,m,C){function u(d){return g.p.ix===d?u.position:g.r.ix===d?u.rotation:g.pt.ix===d?u.points:g.or.ix===d||d==="ADBE Vector Star Outer Radius"?u.outerRadius:g.os.ix===d?u.outerRoundness:g.ir&&(g.ir.ix===d||d==="ADBE Vector Star Inner Radius")?u.innerRadius:g.is&&g.is.ix===d?u.innerRoundness:null}var c=propertyGroupFactory(u,C),l=m.sh.ty==="tm"?m.sh.prop:m.sh;return u.propertyIndex=g.ix,l.or.setGroupProperty(PropertyInterface("Outer Radius",c)),l.os.setGroupProperty(PropertyInterface("Outer Roundness",c)),l.pt.setGroupProperty(PropertyInterface("Points",c)),l.p.setGroupProperty(PropertyInterface("Position",c)),l.r.setGroupProperty(PropertyInterface("Rotation",c)),g.ir&&(l.ir.setGroupProperty(PropertyInterface("Inner Radius",c)),l.is.setGroupProperty(PropertyInterface("Inner Roundness",c))),Object.defineProperties(u,{position:{get:ExpressionPropertyInterface(l.p)},rotation:{get:ExpressionPropertyInterface(l.r)},points:{get:ExpressionPropertyInterface(l.pt)},outerRadius:{get:ExpressionPropertyInterface(l.or)},outerRoundness:{get:ExpressionPropertyInterface(l.os)},innerRadius:{get:ExpressionPropertyInterface(l.ir)},innerRoundness:{get:ExpressionPropertyInterface(l.is)},_name:{value:g.nm}}),u.mn=g.mn,u}function P(g,m,C){function u(d){return g.p.ix===d?u.position:g.r.ix===d?u.roundness:g.s.ix===d||d==="Size"||d==="ADBE Vector Rect Size"?u.size:null}var c=propertyGroupFactory(u,C),l=m.sh.ty==="tm"?m.sh.prop:m.sh;return u.propertyIndex=g.ix,l.p.setGroupProperty(PropertyInterface("Position",c)),l.s.setGroupProperty(PropertyInterface("Size",c)),l.r.setGroupProperty(PropertyInterface("Rotation",c)),Object.defineProperties(u,{position:{get:ExpressionPropertyInterface(l.p)},roundness:{get:ExpressionPropertyInterface(l.r)},size:{get:ExpressionPropertyInterface(l.s)},_name:{value:g.nm}}),u.mn=g.mn,u}function f(g,m,C){function u(d){return g.r.ix===d||d==="Round Corners 1"?u.radius:null}var c=propertyGroupFactory(u,C),l=m;return u.propertyIndex=g.ix,l.rd.setGroupProperty(PropertyInterface("Radius",c)),Object.defineProperties(u,{radius:{get:ExpressionPropertyInterface(l.rd)},_name:{value:g.nm}}),u.mn=g.mn,u}function b(g,m,C){function u(d){return g.c.ix===d||d==="Copies"?u.copies:g.o.ix===d||d==="Offset"?u.offset:null}var c=propertyGroupFactory(u,C),l=m;return u.propertyIndex=g.ix,l.c.setGroupProperty(PropertyInterface("Copies",c)),l.o.setGroupProperty(PropertyInterface("Offset",c)),Object.defineProperties(u,{copies:{get:ExpressionPropertyInterface(l.c)},offset:{get:ExpressionPropertyInterface(l.o)},_name:{value:g.nm}}),u.mn=g.mn,u}return function(g,m,C){var u;function c(d){if(typeof d=="number")return d=d===void 0?1:d,d===0?C:u[d-1];for(var E=0,_=u.length;E<_;){if(u[E]._name===d)return u[E];E+=1}return null}function l(){return C}return c.propertyGroup=propertyGroupFactory(c,l),u=t(g,m,c.propertyGroup),c.numProperties=u.length,c._name="Contents",c}}(),TextExpressionInterface=function(){return function(t){var e;function r(i){switch(i){case"ADBE Text Document":return r.sourceText;default:return null}}return Object.defineProperty(r,"sourceText",{get:function(){t.textProperty.getValue();var s=t.textProperty.currentData.t;return(!e||s!==e.value)&&(e=new String(s),e.value=s||new String(s),Object.defineProperty(e,"style",{get:function(){return{fillColor:t.textProperty.currentData.fc}}})),e}}),r}}();function _typeof(t){"@babel/helpers - typeof";return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?_typeof=function(r){return typeof r}:_typeof=function(r){return r&&typeof Symbol=="function"&&r.constructor===Symbol&&r!==Symbol.prototype?"symbol":typeof r},_typeof(t)}var FootageInterface=function(){var t=function(i){var s="",n=i.getFootageData();function a(){return s="",n=i.getFootageData(),h}function h(o){if(n[o])return s=o,n=n[o],_typeof(n)==="object"?h:n;var p=o.indexOf(s);if(p!==-1){var y=parseInt(o.substr(p+s.length),10);return n=n[y],_typeof(n)==="object"?h:n}return""}return a},e=function(i){function s(n){return n==="Outline"?s.outlineInterface():null}return s._name="Outline",s.outlineInterface=t(i),s};return function(r){function i(s){return s==="Data"?i.dataInterface:null}return i._name="Data",i.dataInterface=e(r),i}}(),interfaces={layer:LayerExpressionInterface,effects:EffectsExpressionInterface,comp:CompExpressionInterface,shape:ShapeExpressionInterface,text:TextExpressionInterface,footage:FootageInterface};function getInterface(t){return interfaces[t]||null}var expressionHelpers=function(){function t(a,h,o){h.x&&(o.k=!0,o.x=!0,o.initiateExpression=ExpressionManager.initiateExpression,o.effectsSequence.push(o.initiateExpression(a,h,o).bind(o)))}function e(a){return a*=this.elem.globalData.frameRate,a-=this.offsetTime,a!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<a?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(a,this._cachingAtTime),this._cachingAtTime.lastFrame=a),this._cachingAtTime.value}function r(a){var h=-.01,o=this.getValueAtTime(a),p=this.getValueAtTime(a+h),y=0;if(o.length){var P;for(P=0;P<o.length;P+=1)y+=Math.pow(p[P]-o[P],2);y=Math.sqrt(y)*100}else y=0;return y}function i(a){if(this.vel!==void 0)return this.vel;var h=-.001,o=this.getValueAtTime(a),p=this.getValueAtTime(a+h),y;if(o.length){y=createTypedArray("float32",o.length);var P;for(P=0;P<o.length;P+=1)y[P]=(p[P]-o[P])/h}else y=(p-o)/h;return y}function s(){return this.pv}function n(a){this.propertyGroup=a}return{searchExpressions:t,getSpeedAtTime:r,getVelocityAtTime:i,getValueAtTime:e,getStaticValueAtTime:s,setGroupProperty:n}}();function addPropertyDecorator(){function t(f,b,g){if(!this.k||!this.keyframes)return this.pv;f=f?f.toLowerCase():"";var m=this.comp.renderedFrame,C=this.keyframes,u=C[C.length-1].t;if(m<=u)return this.pv;var c,l;g?(b?c=Math.abs(u-this.elem.comp.globalData.frameRate*b):c=Math.max(0,u-this.elem.data.ip),l=u-c):((!b||b>C.length-1)&&(b=C.length-1),l=C[C.length-1-b].t,c=u-l);var d,E,_;if(f==="pingpong"){var T=Math.floor((m-l)/c);if(T%2!==0)return this.getValueAtTime((c-(m-l)%c+l)/this.comp.globalData.frameRate,0)}else if(f==="offset"){var F=this.getValueAtTime(l/this.comp.globalData.frameRate,0),R=this.getValueAtTime(u/this.comp.globalData.frameRate,0),G=this.getValueAtTime(((m-l)%c+l)/this.comp.globalData.frameRate,0),w=Math.floor((m-l)/c);if(this.pv.length){for(_=new Array(F.length),E=_.length,d=0;d<E;d+=1)_[d]=(R[d]-F[d])*w+G[d];return _}return(R-F)*w+G}else if(f==="continue"){var k=this.getValueAtTime(u/this.comp.globalData.frameRate,0),D=this.getValueAtTime((u-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(_=new Array(k.length),E=_.length,d=0;d<E;d+=1)_[d]=k[d]+(k[d]-D[d])*((m-u)/this.comp.globalData.frameRate)/5e-4;return _}return k+(k-D)*((m-u)/.001)}return this.getValueAtTime(((m-l)%c+l)/this.comp.globalData.frameRate,0)}function e(f,b,g){if(!this.k)return this.pv;f=f?f.toLowerCase():"";var m=this.comp.renderedFrame,C=this.keyframes,u=C[0].t;if(m>=u)return this.pv;var c,l;g?(b?c=Math.abs(this.elem.comp.globalData.frameRate*b):c=Math.max(0,this.elem.data.op-u),l=u+c):((!b||b>C.length-1)&&(b=C.length-1),l=C[b].t,c=l-u);var d,E,_;if(f==="pingpong"){var T=Math.floor((u-m)/c);if(T%2===0)return this.getValueAtTime(((u-m)%c+u)/this.comp.globalData.frameRate,0)}else if(f==="offset"){var F=this.getValueAtTime(u/this.comp.globalData.frameRate,0),R=this.getValueAtTime(l/this.comp.globalData.frameRate,0),G=this.getValueAtTime((c-(u-m)%c+u)/this.comp.globalData.frameRate,0),w=Math.floor((u-m)/c)+1;if(this.pv.length){for(_=new Array(F.length),E=_.length,d=0;d<E;d+=1)_[d]=G[d]-(R[d]-F[d])*w;return _}return G-(R-F)*w}else if(f==="continue"){var k=this.getValueAtTime(u/this.comp.globalData.frameRate,0),D=this.getValueAtTime((u+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(_=new Array(k.length),E=_.length,d=0;d<E;d+=1)_[d]=k[d]+(k[d]-D[d])*(u-m)/.001;return _}return k+(k-D)*(u-m)/.001}return this.getValueAtTime((c-((u-m)%c+u))/this.comp.globalData.frameRate,0)}function r(f,b){if(!this.k)return this.pv;if(f=(f||.4)*.5,b=Math.floor(b||5),b<=1)return this.pv;var g=this.comp.renderedFrame/this.comp.globalData.frameRate,m=g-f,C=g+f,u=b>1?(C-m)/(b-1):1,c=0,l=0,d;this.pv.length?d=createTypedArray("float32",this.pv.length):d=0;for(var E;c<b;){if(E=this.getValueAtTime(m+c*u),this.pv.length)for(l=0;l<this.pv.length;l+=1)d[l]+=E[l];else d+=E;c+=1}if(this.pv.length)for(l=0;l<this.pv.length;l+=1)d[l]/=b;else d/=b;return d}function i(f){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var b=this._transformCachingAtTime.v;if(b.cloneFromProps(this.pre.props),this.appliedTransformations<1){var g=this.a.getValueAtTime(f);b.translate(-g[0]*this.a.mult,-g[1]*this.a.mult,g[2]*this.a.mult)}if(this.appliedTransformations<2){var m=this.s.getValueAtTime(f);b.scale(m[0]*this.s.mult,m[1]*this.s.mult,m[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var C=this.sk.getValueAtTime(f),u=this.sa.getValueAtTime(f);b.skewFromAxis(-C*this.sk.mult,u*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var c=this.r.getValueAtTime(f);b.rotate(-c*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var l=this.rz.getValueAtTime(f),d=this.ry.getValueAtTime(f),E=this.rx.getValueAtTime(f),_=this.or.getValueAtTime(f);b.rotateZ(-l*this.rz.mult).rotateY(d*this.ry.mult).rotateX(E*this.rx.mult).rotateZ(-_[2]*this.or.mult).rotateY(_[1]*this.or.mult).rotateX(_[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var T=this.px.getValueAtTime(f),F=this.py.getValueAtTime(f);if(this.data.p.z){var R=this.pz.getValueAtTime(f);b.translate(T*this.px.mult,F*this.py.mult,-R*this.pz.mult)}else b.translate(T*this.px.mult,F*this.py.mult,0)}else{var G=this.p.getValueAtTime(f);b.translate(G[0]*this.p.mult,G[1]*this.p.mult,-G[2]*this.p.mult)}return b}function s(){return this.v.clone(new Matrix)}var n=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(f,b,g){var m=n(f,b,g);return m.dynamicProperties.length?m.getValueAtTime=i.bind(m):m.getValueAtTime=s.bind(m),m.setGroupProperty=expressionHelpers.setGroupProperty,m};var a=PropertyFactory.getProp;PropertyFactory.getProp=function(f,b,g,m,C){var u=a(f,b,g,m,C);u.kf?u.getValueAtTime=expressionHelpers.getValueAtTime.bind(u):u.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(u),u.setGroupProperty=expressionHelpers.setGroupProperty,u.loopOut=t,u.loopIn=e,u.smooth=r,u.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(u),u.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(u),u.numKeys=b.a===1?b.k.length:0,u.propertyIndex=b.ix;var c=0;return g!==0&&(c=createTypedArray("float32",b.a===1?b.k[0].s.length:b.k.length)),u._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:c},expressionHelpers.searchExpressions(f,b,u),u.k&&C.addDynamicProperty(u),u};function h(f){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),f*=this.elem.globalData.frameRate,f-=this.offsetTime,f!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<f?this._caching.lastIndex:0,this._cachingAtTime.lastTime=f,this.interpolateShape(f,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue}var o=ShapePropertyFactory.getConstructorFunction(),p=ShapePropertyFactory.getKeyframedConstructorFunction();function y(){}y.prototype={vertices:function(b,g){this.k&&this.getValue();var m=this.v;g!==void 0&&(m=this.getValueAtTime(g,0));var C,u=m._length,c=m[b],l=m.v,d=createSizedArray(u);for(C=0;C<u;C+=1)b==="i"||b==="o"?d[C]=[c[C][0]-l[C][0],c[C][1]-l[C][1]]:d[C]=[c[C][0],c[C][1]];return d},points:function(b){return this.vertices("v",b)},inTangents:function(b){return this.vertices("i",b)},outTangents:function(b){return this.vertices("o",b)},isClosed:function(){return this.v.c},pointOnPath:function(b,g){var m=this.v;g!==void 0&&(m=this.getValueAtTime(g,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(m));for(var C=this._segmentsLength,u=C.lengths,c=C.totalLength*b,l=0,d=u.length,E=0,_;l<d;){if(E+u[l].addedLength>c){var T=l,F=m.c&&l===d-1?0:l+1,R=(c-E)/u[l].addedLength;_=bez.getPointInSegment(m.v[T],m.v[F],m.o[T],m.i[F],R,u[l]);break}else E+=u[l].addedLength;l+=1}return _||(_=m.c?[m.v[0][0],m.v[0][1]]:[m.v[m._length-1][0],m.v[m._length-1][1]]),_},vectorOnPath:function(b,g,m){b==1?b=this.v.c:b==0&&(b=.999);var C=this.pointOnPath(b,g),u=this.pointOnPath(b+.001,g),c=u[0]-C[0],l=u[1]-C[1],d=Math.sqrt(Math.pow(c,2)+Math.pow(l,2));if(d===0)return[0,0];var E=m==="tangent"?[c/d,l/d]:[-l/d,c/d];return E},tangentOnPath:function(b,g){return this.vectorOnPath(b,g,"tangent")},normalOnPath:function(b,g){return this.vectorOnPath(b,g,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([y],o),extendPrototype([y],p),p.prototype.getValueAtTime=h,p.prototype.initiateExpression=ExpressionManager.initiateExpression;var P=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(f,b,g,m,C){var u=P(f,b,g,m,C);return u.propertyIndex=b.ix,u.lock=!1,g===3?expressionHelpers.searchExpressions(f,b.pt,u):g===4&&expressionHelpers.searchExpressions(f,b.ks,u),u.k&&f.addDynamicProperty(u),u}}function initialize$1(){addPropertyDecorator()}function addDecorator(){function t(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}TextProperty.prototype.getExpressionValue=function(e,r){var i=this.calculateExpression(r);if(e.t!==i){var s={};return this.copyData(s,e),s.t=i.toString(),s.__complete=!1,s}return e},TextProperty.prototype.searchProperty=function(){var e=this.searchKeyframes(),r=this.searchExpressions();return this.kf=e||r,this.kf},TextProperty.prototype.searchExpressions=t}function initialize(){addDecorator()}function SVGComposableEffect(){}SVGComposableEffect.prototype={createMergeNode:function t(e,r){var i=createNS("feMerge");i.setAttribute("result",e);var s,n;for(n=0;n<r.length;n+=1)s=createNS("feMergeNode"),s.setAttribute("in",r[n]),i.appendChild(s),i.appendChild(s);return i}};var linearFilterValue="0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0";function SVGTintFilter(t,e,r,i,s){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","linearRGB"),n.setAttribute("values",linearFilterValue+" 1 0"),this.linearFilter=n,n.setAttribute("result",i+"_tint_1"),t.appendChild(n),n=createNS("feColorMatrix"),n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),n.setAttribute("result",i+"_tint_2"),t.appendChild(n),this.matrixFilter=n;var a=this.createMergeNode(i,[s,i+"_tint_1",i+"_tint_2"]);t.appendChild(a)}extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v/100;this.linearFilter.setAttribute("values",linearFilterValue+" "+i+" 0"),this.matrixFilter.setAttribute("values",r[0]-e[0]+" 0 0 0 "+e[0]+" "+(r[1]-e[1])+" 0 0 0 "+e[1]+" "+(r[2]-e[2])+" 0 0 0 "+e[2]+" 0 0 0 1 0")}};function SVGFillFilter(t,e,r,i){this.filterManager=e;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),s.setAttribute("result",i),t.appendChild(s),this.matrixFilter=s}SVGFillFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[2].p.v,r=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+e[0]+" 0 0 0 0 "+e[1]+" 0 0 0 0 "+e[2]+" 0 0 0 "+r+" 0")}};function SVGStrokeEffect(t,e,r){this.initialized=!1,this.filterManager=e,this.elem=r,this.paths=[]}SVGStrokeEffect.prototype.initialize=function(){var t=this.elem.layerElement.children||this.elem.layerElement.childNodes,e,r,i,s;for(this.filterManager.effectElements[1].p.v===1?(s=this.elem.maskManager.masksProperties.length,i=0):(i=this.filterManager.effectElements[0].p.v-1,s=i+1),r=createNS("g"),r.setAttribute("fill","none"),r.setAttribute("stroke-linecap","round"),r.setAttribute("stroke-dashoffset",1),i;i<s;i+=1)e=createNS("path"),r.appendChild(e),this.paths.push({p:e,m:i});if(this.filterManager.effectElements[10].p.v===3){var n=createNS("mask"),a=createElementID();n.setAttribute("id",a),n.setAttribute("mask-type","alpha"),n.appendChild(r),this.elem.globalData.defs.appendChild(n);var h=createNS("g");for(h.setAttribute("mask","url("+getLocationHref()+"#"+a+")");t[0];)h.appendChild(t[0]);this.elem.layerElement.appendChild(h),this.masker=n,r.setAttribute("stroke","#fff")}else if(this.filterManager.effectElements[10].p.v===1||this.filterManager.effectElements[10].p.v===2){if(this.filterManager.effectElements[10].p.v===2)for(t=this.elem.layerElement.children||this.elem.layerElement.childNodes;t.length;)this.elem.layerElement.removeChild(t[0]);this.elem.layerElement.appendChild(r),this.elem.layerElement.removeAttribute("mask"),r.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=r},SVGStrokeEffect.prototype.renderFrame=function(t){this.initialized||this.initialize();var e,r=this.paths.length,i,s;for(e=0;e<r;e+=1)if(this.paths[e].m!==-1&&(i=this.elem.maskManager.viewData[this.paths[e].m],s=this.paths[e].p,(t||this.filterManager._mdf||i.prop._mdf)&&s.setAttribute("d",i.lastPath),t||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||i.prop._mdf)){var n;if(this.filterManager.effectElements[7].p.v!==0||this.filterManager.effectElements[8].p.v!==100){var a=Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v)*.01,h=Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v)*.01,o=s.getTotalLength();n="0 0 0 "+o*a+" ";var p=o*(h-a),y=1+this.filterManager.effectElements[4].p.v*2*this.filterManager.effectElements[9].p.v*.01,P=Math.floor(p/y),f;for(f=0;f<P;f+=1)n+="1 "+this.filterManager.effectElements[4].p.v*2*this.filterManager.effectElements[9].p.v*.01+" ";n+="0 "+o*10+" 0 0"}else n="1 "+this.filterManager.effectElements[4].p.v*2*this.filterManager.effectElements[9].p.v*.01;s.setAttribute("stroke-dasharray",n)}if((t||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",this.filterManager.effectElements[4].p.v*2),(t||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(this.filterManager.effectElements[10].p.v===1||this.filterManager.effectElements[10].p.v===2)&&(t||this.filterManager.effectElements[3].p._mdf)){var b=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(b[0]*255)+","+bmFloor(b[1]*255)+","+bmFloor(b[2]*255)+")")}};function SVGTritoneFilter(t,e,r,i){this.filterManager=e;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","linearRGB"),s.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),t.appendChild(s);var n=createNS("feComponentTransfer");n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("result",i),this.matrixFilter=n;var a=createNS("feFuncR");a.setAttribute("type","table"),n.appendChild(a),this.feFuncR=a;var h=createNS("feFuncG");h.setAttribute("type","table"),n.appendChild(h),this.feFuncG=h;var o=createNS("feFuncB");o.setAttribute("type","table"),n.appendChild(o),this.feFuncB=o,t.appendChild(n)}SVGTritoneFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v,s=i[0]+" "+r[0]+" "+e[0],n=i[1]+" "+r[1]+" "+e[1],a=i[2]+" "+r[2]+" "+e[2];this.feFuncR.setAttribute("tableValues",s),this.feFuncG.setAttribute("tableValues",n),this.feFuncB.setAttribute("tableValues",a)}};function SVGProLevelsFilter(t,e,r,i){this.filterManager=e;var s=this.filterManager.effectElements,n=createNS("feComponentTransfer");(s[10].p.k||s[10].p.v!==0||s[11].p.k||s[11].p.v!==1||s[12].p.k||s[12].p.v!==1||s[13].p.k||s[13].p.v!==0||s[14].p.k||s[14].p.v!==1)&&(this.feFuncR=this.createFeFunc("feFuncR",n)),(s[17].p.k||s[17].p.v!==0||s[18].p.k||s[18].p.v!==1||s[19].p.k||s[19].p.v!==1||s[20].p.k||s[20].p.v!==0||s[21].p.k||s[21].p.v!==1)&&(this.feFuncG=this.createFeFunc("feFuncG",n)),(s[24].p.k||s[24].p.v!==0||s[25].p.k||s[25].p.v!==1||s[26].p.k||s[26].p.v!==1||s[27].p.k||s[27].p.v!==0||s[28].p.k||s[28].p.v!==1)&&(this.feFuncB=this.createFeFunc("feFuncB",n)),(s[31].p.k||s[31].p.v!==0||s[32].p.k||s[32].p.v!==1||s[33].p.k||s[33].p.v!==1||s[34].p.k||s[34].p.v!==0||s[35].p.k||s[35].p.v!==1)&&(this.feFuncA=this.createFeFunc("feFuncA",n)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(n.setAttribute("color-interpolation-filters","sRGB"),t.appendChild(n)),(s[3].p.k||s[3].p.v!==0||s[4].p.k||s[4].p.v!==1||s[5].p.k||s[5].p.v!==1||s[6].p.k||s[6].p.v!==0||s[7].p.k||s[7].p.v!==1)&&(n=createNS("feComponentTransfer"),n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("result",i),t.appendChild(n),this.feFuncRComposed=this.createFeFunc("feFuncR",n),this.feFuncGComposed=this.createFeFunc("feFuncG",n),this.feFuncBComposed=this.createFeFunc("feFuncB",n))}SVGProLevelsFilter.prototype.createFeFunc=function(t,e){var r=createNS(t);return r.setAttribute("type","table"),e.appendChild(r),r},SVGProLevelsFilter.prototype.getTableValue=function(t,e,r,i,s){for(var n=0,a=256,h,o=Math.min(t,e),p=Math.max(t,e),y=Array.call(null,{length:a}),P,f=0,b=s-i,g=e-t;n<=256;)h=n/256,h<=o?P=g<0?s:i:h>=p?P=g<0?i:s:P=i+b*Math.pow((h-t)/g,1/r),y[f]=P,f+=1,n+=256/(a-1);return y.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e,r=this.filterManager.effectElements;this.feFuncRComposed&&(t||r[3].p._mdf||r[4].p._mdf||r[5].p._mdf||r[6].p._mdf||r[7].p._mdf)&&(e=this.getTableValue(r[3].p.v,r[4].p.v,r[5].p.v,r[6].p.v,r[7].p.v),this.feFuncRComposed.setAttribute("tableValues",e),this.feFuncGComposed.setAttribute("tableValues",e),this.feFuncBComposed.setAttribute("tableValues",e)),this.feFuncR&&(t||r[10].p._mdf||r[11].p._mdf||r[12].p._mdf||r[13].p._mdf||r[14].p._mdf)&&(e=this.getTableValue(r[10].p.v,r[11].p.v,r[12].p.v,r[13].p.v,r[14].p.v),this.feFuncR.setAttribute("tableValues",e)),this.feFuncG&&(t||r[17].p._mdf||r[18].p._mdf||r[19].p._mdf||r[20].p._mdf||r[21].p._mdf)&&(e=this.getTableValue(r[17].p.v,r[18].p.v,r[19].p.v,r[20].p.v,r[21].p.v),this.feFuncG.setAttribute("tableValues",e)),this.feFuncB&&(t||r[24].p._mdf||r[25].p._mdf||r[26].p._mdf||r[27].p._mdf||r[28].p._mdf)&&(e=this.getTableValue(r[24].p.v,r[25].p.v,r[26].p.v,r[27].p.v,r[28].p.v),this.feFuncB.setAttribute("tableValues",e)),this.feFuncA&&(t||r[31].p._mdf||r[32].p._mdf||r[33].p._mdf||r[34].p._mdf||r[35].p._mdf)&&(e=this.getTableValue(r[31].p.v,r[32].p.v,r[33].p.v,r[34].p.v,r[35].p.v),this.feFuncA.setAttribute("tableValues",e))}};function SVGDropShadowEffect(t,e,r,i,s){var n=e.container.globalData.renderConfig.filterSize,a=e.data.fs||n;t.setAttribute("x",a.x||n.x),t.setAttribute("y",a.y||n.y),t.setAttribute("width",a.width||n.width),t.setAttribute("height",a.height||n.height),this.filterManager=e;var h=createNS("feGaussianBlur");h.setAttribute("in","SourceAlpha"),h.setAttribute("result",i+"_drop_shadow_1"),h.setAttribute("stdDeviation","0"),this.feGaussianBlur=h,t.appendChild(h);var o=createNS("feOffset");o.setAttribute("dx","25"),o.setAttribute("dy","0"),o.setAttribute("in",i+"_drop_shadow_1"),o.setAttribute("result",i+"_drop_shadow_2"),this.feOffset=o,t.appendChild(o);var p=createNS("feFlood");p.setAttribute("flood-color","#00ff00"),p.setAttribute("flood-opacity","1"),p.setAttribute("result",i+"_drop_shadow_3"),this.feFlood=p,t.appendChild(p);var y=createNS("feComposite");y.setAttribute("in",i+"_drop_shadow_3"),y.setAttribute("in2",i+"_drop_shadow_2"),y.setAttribute("operator","in"),y.setAttribute("result",i+"_drop_shadow_4"),t.appendChild(y);var P=this.createMergeNode(i,[i+"_drop_shadow_4",s]);t.appendChild(P)}extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){if((t||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),t||this.filterManager.effectElements[0].p._mdf){var e=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(e[0]*255),Math.round(e[1]*255),Math.round(e[2]*255)))}if((t||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),t||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var r=this.filterManager.effectElements[3].p.v,i=(this.filterManager.effectElements[2].p.v-90)*degToRads,s=r*Math.cos(i),n=r*Math.sin(i);this.feOffset.setAttribute("dx",s),this.feOffset.setAttribute("dy",n)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(t,e,r){this.initialized=!1,this.filterManager=e,this.filterElem=t,this.elem=r,r.matteElement=createNS("g"),r.matteElement.appendChild(r.layerElement),r.matteElement.appendChild(r.transformedElement),r.baseElement=r.matteElement}SVGMatte3Effect.prototype.findSymbol=function(t){for(var e=0,r=_svgMatteSymbols.length;e<r;){if(_svgMatteSymbols[e]===t)return _svgMatteSymbols[e];e+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(t,e){var r=t.layerElement.parentNode;if(r){for(var i=r.children,s=0,n=i.length;s<n&&i[s]!==t.layerElement;)s+=1;var a;s<=n-2&&(a=i[s+1]);var h=createNS("use");h.setAttribute("href","#"+e),a?r.insertBefore(h,a):r.appendChild(h)}},SVGMatte3Effect.prototype.setElementAsMask=function(t,e){if(!this.findSymbol(e)){var r=createElementID(),i=createNS("mask");i.setAttribute("id",e.layerId),i.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(e);var s=t.globalData.defs;s.appendChild(i);var n=createNS("symbol");n.setAttribute("id",r),this.replaceInParent(e,r),n.appendChild(e.layerElement),s.appendChild(n);var a=createNS("use");a.setAttribute("href","#"+r),i.appendChild(a),e.data.hd=!1,e.show()}t.setMatte(e.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var t=this.filterManager.effectElements[0].p.v,e=this.elem.comp.elements,r=0,i=e.length;r<i;)e[r]&&e[r].data.ind===t&&this.setElementAsMask(this.elem,e[r]),r+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()};function SVGGaussianBlurEffect(t,e,r,i){t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width","300%"),t.setAttribute("height","300%"),this.filterManager=e;var s=createNS("feGaussianBlur");s.setAttribute("result",i),t.appendChild(s),this.feGaussianBlur=s}SVGGaussianBlurEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=.3,r=this.filterManager.effectElements[0].p.v*e,i=this.filterManager.effectElements[1].p.v,s=i==3?0:r,n=i==2?0:r;this.feGaussianBlur.setAttribute("stdDeviation",s+" "+n);var a=this.filterManager.effectElements[2].p.v==1?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",a)}};function TransformEffect(){}TransformEffect.prototype.init=function(t){this.effectsManager=t,this.type=effectTypes.TRANSFORM_EFFECT,this.matrix=new Matrix,this.opacity=-1,this._mdf=!1,this._opMdf=!1},TransformEffect.prototype.renderFrame=function(t){if(this._opMdf=!1,this._mdf=!1,t||this.effectsManager._mdf){var e=this.effectsManager.effectElements,r=e[0].p.v,i=e[1].p.v,s=e[2].p.v===1,n=e[3].p.v,a=s?n:e[4].p.v,h=e[5].p.v,o=e[6].p.v,p=e[7].p.v;this.matrix.reset(),this.matrix.translate(-r[0],-r[1],r[2]),this.matrix.scale(a*.01,n*.01,1),this.matrix.rotate(-p*degToRads),this.matrix.skewFromAxis(-h*degToRads,(o+90)*degToRads),this.matrix.translate(i[0],i[1],0),this._mdf=!0,this.opacity!==e[8].p.v&&(this.opacity=e[8].p.v,this._opMdf=!0)}};function SVGTransformEffect(t,e){this.init(e)}extendPrototype([TransformEffect],SVGTransformEffect);function CVTransformEffect(t){this.init(t)}return extendPrototype([TransformEffect],CVTransformEffect),registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),ShapeModifiers.registerModifier("zz",ZigZagModifier),ShapeModifiers.registerModifier("op",OffsetPathModifier),setExpressionsPlugin(Expressions),setExpressionInterfaces(getInterface),initialize$1(),initialize(),registerEffect$1(20,SVGTintFilter,!0),registerEffect$1(21,SVGFillFilter,!0),registerEffect$1(22,SVGStrokeEffect,!1),registerEffect$1(23,SVGTritoneFilter,!0),registerEffect$1(24,SVGProLevelsFilter,!0),registerEffect$1(25,SVGDropShadowEffect,!0),registerEffect$1(28,SVGMatte3Effect,!1),registerEffect$1(29,SVGGaussianBlurEffect,!0),registerEffect$1(35,SVGTransformEffect,!1),registerEffect(35,CVTransformEffect),lottie})});var At=St((gt,_t)=>{vt();yt();(function(t,e){typeof gt=="object"&&typeof _t<"u"?e(gt,Ct(),Et()):typeof define=="function"&&define.amd?define(["exports","lottie-web","react"],e):(t=typeof globalThis<"u"?globalThis:t||self,e(t["lottie-react"]={},t.Lottie,t.React))})(gt,function(t,e,r){"use strict";function i(w){return w&&typeof w=="object"&&"default"in w?w:{default:w}}var s=i(e),n=i(r);function a(w,k){var D=w==null?null:typeof Symbol<"u"&&w[Symbol.iterator]||w["@@iterator"];if(D!=null){var V,L,x,A,v=[],S=!0,I=!1;try{if(x=(D=D.call(w)).next,k===0){if(Object(D)!==D)return;S=!1}else for(;!(S=(V=x.call(D)).done)&&(v.push(V.value),v.length!==k);S=!0);}catch(M){I=!0,L=M}finally{try{if(!S&&D.return!=null&&(A=D.return(),Object(A)!==A))return}finally{if(I)throw L}}return v}}function h(w,k){var D=Object.keys(w);if(Object.getOwnPropertySymbols){var V=Object.getOwnPropertySymbols(w);k&&(V=V.filter(function(L){return Object.getOwnPropertyDescriptor(w,L).enumerable})),D.push.apply(D,V)}return D}function o(w){for(var k=1;k<arguments.length;k++){var D=arguments[k]!=null?arguments[k]:{};k%2?h(Object(D),!0).forEach(function(V){p(w,V,D[V])}):Object.getOwnPropertyDescriptors?Object.defineProperties(w,Object.getOwnPropertyDescriptors(D)):h(Object(D)).forEach(function(V){Object.defineProperty(w,V,Object.getOwnPropertyDescriptor(D,V))})}return w}function p(w,k,D){return k=c(k),k in w?Object.defineProperty(w,k,{value:D,enumerable:!0,configurable:!0,writable:!0}):w[k]=D,w}function y(w,k){if(w==null)return{};var D={},V=Object.keys(w),L,x;for(x=0;x<V.length;x++)L=V[x],!(k.indexOf(L)>=0)&&(D[L]=w[L]);return D}function P(w,k){if(w==null)return{};var D=y(w,k),V,L;if(Object.getOwnPropertySymbols){var x=Object.getOwnPropertySymbols(w);for(L=0;L<x.length;L++)V=x[L],!(k.indexOf(V)>=0)&&Object.prototype.propertyIsEnumerable.call(w,V)&&(D[V]=w[V])}return D}function f(w,k){return b(w)||a(w,k)||g(w,k)||C()}function b(w){if(Array.isArray(w))return w}function g(w,k){if(w){if(typeof w=="string")return m(w,k);var D=Object.prototype.toString.call(w).slice(8,-1);if(D==="Object"&&w.constructor&&(D=w.constructor.name),D==="Map"||D==="Set")return Array.from(w);if(D==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(D))return m(w,k)}}function m(w,k){(k==null||k>w.length)&&(k=w.length);for(var D=0,V=new Array(k);D<k;D++)V[D]=w[D];return V}function C(){throw new TypeError(`Invalid attempt to destructure non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}function u(w,k){if(typeof w!="object"||w===null)return w;var D=w[Symbol.toPrimitive];if(D!==void 0){var V=D.call(w,k||"default");if(typeof V!="object")return V;throw new TypeError("@@toPrimitive must return a primitive value.")}return(k==="string"?String:Number)(w)}function c(w){var k=u(w,"string");return typeof k=="symbol"?k:String(k)}var l=["animationData","loop","autoplay","initialSegment","onComplete","onLoopComplete","onEnterFrame","onSegmentStart","onConfigReady","onDataReady","onDataFailed","onLoadedImages","onDOMLoaded","onDestroy","lottieRef","renderer","name","assetsPath","rendererSettings"],d=function(k,D){var V=k.animationData,L=k.loop,x=k.autoplay,A=k.initialSegment,v=k.onComplete,S=k.onLoopComplete,I=k.onEnterFrame,M=k.onSegmentStart,B=k.onConfigReady,N=k.onDataReady,H=k.onDataFailed,z=k.onLoadedImages,W=k.onDOMLoaded,Y=k.onDestroy;k.lottieRef,k.renderer,k.name,k.assetsPath,k.rendererSettings;var Z=P(k,l),tt=r.useState(!1),q=f(tt,2),K=q[0],Q=q[1],j=r.useRef(),O=r.useRef(null),at=function(){var $;($=j.current)===null||$===void 0||$.play()},ft=function(){var $;($=j.current)===null||$===void 0||$.stop()},ot=function(){var $;($=j.current)===null||$===void 0||$.pause()},st=function($){var X;(X=j.current)===null||X===void 0||X.setSpeed($)},ht=function($,X){var U;(U=j.current)===null||U===void 0||U.goToAndPlay($,X)},lt=function($,X){var U;(U=j.current)===null||U===void 0||U.goToAndStop($,X)},et=function($){var X;(X=j.current)===null||X===void 0||X.setDirection($)},pt=function($,X){var U;(U=j.current)===null||U===void 0||U.playSegments($,X)},ut=function($){var X;(X=j.current)===null||X===void 0||X.setSubframe($)},rt=function($){var X;return(X=j.current)===null||X===void 0?void 0:X.getDuration($)},ct=function(){var $;($=j.current)===null||$===void 0||$.destroy(),j.current=void 0},it=function(){var $=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},X;if(O.current){(X=j.current)===null||X===void 0||X.destroy();var U=o(o(o({},k),$),{},{container:O.current});return j.current=s.default.loadAnimation(U),Q(!!j.current),function(){var dt;(dt=j.current)===null||dt===void 0||dt.destroy(),j.current=void 0}}};r.useEffect(function(){var J=it();return function(){return J?.()}},[V,L]),r.useEffect(function(){j.current&&(j.current.autoplay=!!x)},[x]),r.useEffect(function(){if(j.current){if(!A){j.current.resetSegments(!0);return}!Array.isArray(A)||!A.length||((j.current.currentRawFrame<A[0]||j.current.currentRawFrame>A[1])&&(j.current.currentRawFrame=A[0]),j.current.setSegment(A[0],A[1]))}},[A]),r.useEffect(function(){var J=[{name:"complete",handler:v},{name:"loopComplete",handler:S},{name:"enterFrame",handler:I},{name:"segmentStart",handler:M},{name:"config_ready",handler:B},{name:"data_ready",handler:N},{name:"data_failed",handler:H},{name:"loaded_images",handler:z},{name:"DOMLoaded",handler:W},{name:"destroy",handler:Y}],$=J.filter(function(U){return U.handler!=null});if($.length){var X=$.map(function(U){var dt;return(dt=j.current)===null||dt===void 0||dt.addEventListener(U.name,U.handler),function(){var bt;(bt=j.current)===null||bt===void 0||bt.removeEventListener(U.name,U.handler)}});return function(){X.forEach(function(U){return U()})}}},[v,S,I,M,B,N,H,z,W,Y]);var mt=n.default.createElement("div",o({style:D,ref:O},Z));return{View:mt,play:at,stop:ft,pause:ot,setSpeed:st,goToAndStop:lt,goToAndPlay:ht,setDirection:et,playSegments:pt,setSubframe:ut,getDuration:rt,destroy:ct,animationContainerRef:O,animationLoaded:K,animationItem:j.current}};function E(w){var k=w.getBoundingClientRect(),D=k.top,V=k.height,L=self.innerHeight-D,x=self.innerHeight+V;return L/x}function _(w,k,D){var V=w.getBoundingClientRect(),L=V.top,x=V.left,A=V.width,v=V.height,S=(k-x)/A,I=(D-L)/v;return{x:S,y:I}}var T=function(k){var D=k.wrapperRef,V=k.animationItem,L=k.mode,x=k.actions;r.useEffect(function(){var A=D.current;if(!(!A||!V||!x.length)){V.stop();var v=function(){var M=null,B=function(){var H=E(A),z=x.find(function(Y){var Z=Y.visibility;return Z&&H>=Z[0]&&H<=Z[1]});if(z){if(z.type==="seek"&&z.visibility&&z.frames.length===2){var W=z.frames[0]+Math.ceil((H-z.visibility[0])/(z.visibility[1]-z.visibility[0])*z.frames[1]);V.goToAndStop(W-V.firstFrame-1,!0)}z.type==="loop"&&(M===null||M!==z.frames||V.isPaused)&&(V.playSegments(z.frames,!0),M=z.frames),z.type==="play"&&V.isPaused&&(V.resetSegments(!0),V.play()),z.type==="stop"&&V.goToAndStop(z.frames[0]-V.firstFrame-1,!0)}};return document.addEventListener("scroll",B),function(){document.removeEventListener("scroll",B)}},S=function(){var M=function(z,W){var Y=z,Z=W;if(Y!==-1&&Z!==-1){var tt=_(A,Y,Z);Y=tt.x,Z=tt.y}var q=x.find(function(j){var O=j.position;return O&&Array.isArray(O.x)&&Array.isArray(O.y)?Y>=O.x[0]&&Y<=O.x[1]&&Z>=O.y[0]&&Z<=O.y[1]:O&&!Number.isNaN(O.x)&&!Number.isNaN(O.y)?Y===O.x&&Z===O.y:!1});if(q){if(q.type==="seek"&&q.position&&Array.isArray(q.position.x)&&Array.isArray(q.position.y)&&q.frames.length===2){var K=(Y-q.position.x[0])/(q.position.x[1]-q.position.x[0]),Q=(Z-q.position.y[0])/(q.position.y[1]-q.position.y[0]);V.playSegments(q.frames,!0),V.goToAndStop(Math.ceil((K+Q)/2*(q.frames[1]-q.frames[0])),!0)}q.type==="loop"&&V.playSegments(q.frames,!0),q.type==="play"&&(V.isPaused&&V.resetSegments(!1),V.playSegments(q.frames)),q.type==="stop"&&V.goToAndStop(q.frames[0],!0)}},B=function(z){M(z.clientX,z.clientY)},N=function(){M(-1,-1)};return A.addEventListener("mousemove",B),A.addEventListener("mouseout",N),function(){A.removeEventListener("mousemove",B),A.removeEventListener("mouseout",N)}};switch(L){case"scroll":return v();case"cursor":return S()}}},[L,V])},F=function(k){var D=k.actions,V=k.mode,L=k.lottieObj,x=L.animationItem,A=L.View,v=L.animationContainerRef;return T({actions:D,animationItem:x,mode:V,wrapperRef:v}),A},R=["style","interactivity"],G=function(k){var D,V,L,x=k.style,A=k.interactivity,v=P(k,R),S=d(v,x),I=S.View,M=S.play,B=S.stop,N=S.pause,H=S.setSpeed,z=S.goToAndStop,W=S.goToAndPlay,Y=S.setDirection,Z=S.playSegments,tt=S.setSubframe,q=S.getDuration,K=S.destroy,Q=S.animationContainerRef,j=S.animationLoaded,O=S.animationItem;return r.useEffect(function(){k.lottieRef&&(k.lottieRef.current={play:M,stop:B,pause:N,setSpeed:H,goToAndPlay:W,goToAndStop:z,setDirection:Y,playSegments:Z,setSubframe:tt,getDuration:q,destroy:K,animationContainerRef:Q,animationLoaded:j,animationItem:O})},[(D=k.lottieRef)===null||D===void 0?void 0:D.current]),F({lottieObj:{View:I,play:M,stop:B,pause:N,setSpeed:H,goToAndStop:z,goToAndPlay:W,setDirection:Y,playSegments:Z,setSubframe:tt,getDuration:q,destroy:K,animationContainerRef:Q,animationLoaded:j,animationItem:O},actions:(V=A?.actions)!==null&&V!==void 0?V:[],mode:(L=A?.mode)!==null&&L!==void 0?L:"scroll"})};Object.defineProperty(t,"LottiePlayer",{enumerable:!0,get:function(){return s.default}}),t.default=G,t.useLottie=d,t.useLottieInteractivity=F,Object.defineProperty(t,"__esModule",{value:!0})})});vt();yt();var Tt=Pt(At()),nt=Pt(Et()),wt=({stages:t,stageRef:e,path:r,autoplay:i,loop:s})=>{let n=(0,nt.useRef)(0),a=(0,nt.useRef)(0);(0,nt.useImperativeHandle)(e,()=>({setStage:P=>{let f=t.findIndex(b=>b.identifier===P);a.current=f},setStageNow:P=>{let f=t.findIndex(g=>g.identifier===P);(!t[n.current]?.shouldNotInterrupt||n.current!==a.current)&&(n.current=f,a.current=f,o(n.current))}}));let h=(0,nt.useRef)(null),o=(0,nt.useCallback)(P=>{t[P]&&h.current?.animationItem?.playSegments(t[P].segment,!0)},[t]),p=(0,nt.useCallback)(()=>{h.current?.playSegments(t[0].segment,!0)},[t]),y=(0,nt.useCallback)(()=>{let{autoAdvance:P,loop:f,nextIdentifier:b}=t[n.current]??{autoAdvance:!1,loop:!1,nextIdentifier:void 0};if(n.current!==a.current){n.current=a.current,o(n.current);return}if(b){let g=t.findIndex(m=>m.identifier===b);a.current=g,o(a.current);return}if(P){n.current=n.current+1,a.current=n.current,o(n.current);return}f&&o(n.current)},[o,t]);return nt.default.createElement(Tt.default,{lottieRef:h,animationData:r,autoplay:i,loop:s,onComplete:y,onDOMLoaded:p})};export{wt as a};
/*! Bundled license information:

lottie-web/build/player/lottie.js:
  (*!
   Transformation Matrix v2.0
   (c) Epistemex 2014-2015
   www.epistemex.com
   By Ken Fyrstenberg
   Contributions by leeoniya.
   License: MIT, header required.
   *)

lottie-react/build/index.umd.js:
  (*! goToAndStop must be relative to the start of the current segment *)
*/
//# sourceMappingURL=chunk-VHCQKD7Y.js.map
