import{d as l}from"./chunk-P5LBFEHG.js";import{o}from"./chunk-WIQ4WVKX.js";import{a as c}from"./chunk-7X4NV6OJ.js";import{f as m,h as a,n}from"./chunk-3KENBVE7.js";a();n();var e=m(c());var s=4,p=o.div`
  height: 100%;
  width: 100%;
  display: flex;
  background-color: ${i=>i.theme.backgroundDark};
  border-radius: 4%;
  align-items: center;
  justify-content: center;
  overflow: hidden;
`,g=o.img`
  width: 100%;
  height: 100%;
  align-items: center;
  justify-content: center;
`,h=o.div`
  display: flex;
  position: absolute;
  width: 100%;
  padding: 3.75%;
  bottom: 0;
  left: 0;
`,b=o.img`
  aspect-ratio: 1;
  width: 10%;
  border-radius: 20%;
  align-items: center;
  justify-content: center;
  background-color: ${i=>i.theme.purpleLight};
  padding: 2%;
  margin-right: 2.5%;
`,S=e.default.memo(({backgroundImageUrl:i,satributes:d=[]})=>{let r=Array.from(new Map(d.map(t=>[t.id,t])).values()).slice(0,s);return e.default.createElement(e.default.Fragment,null,e.default.createElement(p,null,e.default.createElement(g,{src:i??""})),r.length>0&&e.default.createElement(h,null,r.slice(0,s).map(t=>e.default.createElement(l,{label:t.displayName,ariaLabel:t.displayName},e.default.createElement(b,{src:t.url})))))});export{S as a};
//# sourceMappingURL=chunk-K5EEWGKQ.js.map
