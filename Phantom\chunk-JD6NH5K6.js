import{a as Fn,b as Ui,c as Vi,d as Mn,f as sp,i as lp,j as cp,l as dp,m as Yr,n as Sp}from"./chunk-KJMFZ7XX.js";import{a as tr}from"./chunk-WAFQTOB5.js";import{a as mo,b as rp,c as np,e as ip}from"./chunk-CCUXU2GU.js";import{a as fp,b as go}from"./chunk-SIDJ2NRC.js";import{a as Ki}from"./chunk-7UTGLKC7.js";import{a as fo}from"./chunk-AHRYSG4W.js";import{a as Ct}from"./chunk-QEXGR5WT.js";import{d as Wi}from"./chunk-P5LBFEHG.js";import{b as Dt}from"./chunk-S24UABH5.js";import{a as mp}from"./chunk-MHQYYZ7C.js";import{a as Ne}from"./chunk-X3ESGVCB.js";import{d as gp,h as qi,i as $s,m as yp,o as hp,p as er,t as bp,u as xp,y as Ri,z as ji}from"./chunk-QINBGLLG.js";import{a as zs,b as vp,c as wp}from"./chunk-SHAEZV7V.js";import{a as Zu,b as Qr,d as Xo,e as Zo,g as ap,i as Jr,k as up,m as pp}from"./chunk-IWGMKDQE.js";import{b as po,g as tp,h as op}from"./chunk-DERIAD33.js";import{a as Hi,b as ep}from"./chunk-EGXLQXDH.js";import{a as to}from"./chunk-CCQRCL2K.js";import{b as Ju,d as Yu,f as Xu,g as Be,h as ve,j as zi,m as mt,n as nt}from"./chunk-75L54KUM.js";import{a as oo}from"./chunk-ROF5SDVA.js";import{b as Us,c as Cp,d as Vs,e as Tp}from"./chunk-Q67X6MF4.js";import{a as eo}from"./chunk-IVMV7P4T.js";import{a as iu}from"./chunk-ZON27MKP.js";import{e as mu,l as Pp,m as kp}from"./chunk-SMVAXKUF.js";import{b as Bs,e as Bu,f as Dn,g as Nu,h as Ou,i as Rr,j as _u,k as $u,l as Ns,m as Os,n as zu,o as Uu,p as Vu,q as Hu,r as Wu,s as Ku,t as jr,u as Gr,v as qu,w as St,x as Ru,y as ju,z as Gu}from"./chunk-HPOS2V3B.js";import{c as $i}from"./chunk-XYJX6G2K.js";import{b as Ur,h as hd,i as bd,j as xd}from"./chunk-W27Z2YZM.js";import{D as lu,J as cu,K as Gt,L as $t,M as In,O as Li,P as An,U as pt,a as j,b as Et,c as Ie,d as E,e as H,f as jt,l as au,m as Kr,n as Yo,r as qr,s as Ls,v as su}from"./chunk-2NGYUYTC.js";import{a as fy}from"./chunk-H3FFS4GT.js";import{a as Qu}from"./chunk-4VDZJDFB.js";import{a as Do}from"./chunk-XJTFMD4C.js";import{a as fu}from"./chunk-GMBAJ6CC.js";import{a as _s}from"./chunk-VQVTLSDS.js";import{a as Xe,b as Fo,c as En,d as Ln,e as Fu,f as Fs,h as _i,j as zt,k as Lt,l as Mu}from"./chunk-OKP6DFCI.js";import{B as gu,D as Fi,E as yu,F as hu,I as bu,Ia as Eu,Ja as Lu,K as xu,L as Mi,O as Bi,P as vu,Q as wu,U as Su,V as Ni,X as Cu,Z as Oi,c as uo,ga as Tu,ia as Pu,ja as ku,k as pu,ka as Iu,la as Au,o as x,ob as Du,qb as Ms,rb as W,u as Ds}from"./chunk-WIQ4WVKX.js";import{$ as jd,Aa as Yd,Ha as Xd,Ia as Zd,Ja as eu,Ka as tu,Ma as ou,P as Ps,Q as Kd,Ra as ru,S as Pi,Sa as nu,T as ki,U as ks,V as Is,W as As,X as qd,Y as Rd,cb as Ai,db as Ei,ja as Hr,jb as Wr,la as Qd,oa as Jo,pa as Jd,q as Si,r as Ci,s as _d,t as $d,u as zd,v as br,x as Ud,z as Ti}from"./chunk-SD2LXVLD.js";import{c as Eo}from"./chunk-UCBZOSRF.js";import{g as Vd,h as Hd,i as Wd}from"./chunk-F3RUX6TF.js";import{e as Ii,s as Es,v as Gd}from"./chunk-HRJWTAGT.js";import{c as Sd,d as xi,i as wi,k as Ad,l as Ed,m as Ld,r as Fd,s as Bd}from"./chunk-V5T43K7V.js";import{d as Di,e as du,f as uu}from"./chunk-MNXYIK2W.js";import{a as yd,c as we}from"./chunk-MHOQBMVI.js";import{a as Dc}from"./chunk-BTKBODVJ.js";import{D as vs,H as ud,k as ed,l as td,n as mi,o as od,u as rd,v as nd,z as dd}from"./chunk-7ZN4F6J4.js";import{$b as Pd,Ab as Td,Ac as Od,Cc as Ts,I as pd,L as md,M as fd,N as hi,Sa as ws,Sb as Vr,Ta as kn,Ub as vi,Z as gd,db as vd,eb as wd,ec as kd,fc as Zt,hc as Id,pc as Dd,qc as Md,rb as Cd,sc as Nd,ub as bi,uc as Ss,wc as Cs}from"./chunk-OUYKWOVO.js";import{a as Ic}from"./chunk-OXFZHPMY.js";import{l as Zc}from"./chunk-SLQBAOEK.js";import{$d as ut,Ad as Jc,Db as Rc,Dd as Yc,Ed as Xc,Kd as id,Pa as Ye,Pb as hr,Q as Rt,Qb as ui,Ra as Qe,Rc as Gc,Rd as Qo,Sb as zr,T as co,Tc as Qc,Td as fi,Wa as Pn,Xa as Xt,Xd as ad,ie as bs,jb as qc,le as sd,pe as gi,se as ld,tc as pi,te as yi,uc as jc,ue as xs,wc as wt,we as cd,yb as di}from"./chunk-MZZEJ42N.js";import{K as Tn}from"./chunk-E3NPIRHS.js";import{a as se,b as Lo,m as $}from"./chunk-56SJOU6P.js";import{C as si,G as Vc,H as li,I as hs,J as Hc,K as Wc,S as dt,V as Kc,a as ms,b as Ac,f as Nc,ha as Go,i as Oc,ka as ci,l as _c,m as $c,n as ys,w as zc,x as Uc}from"./chunk-ALUTR72U.js";import{Ca as ai,Da as Mc,F as Ec,Ja as gs,K as Lc,La as Bc,Qa as At,Ya as bt,ba as Fc,ia as _t,ka as jo,la as It,ta as oe,u as fs}from"./chunk-L3A2KHJO.js";import{a as rt}from"./chunk-4P36KWOF.js";import{a as D}from"./chunk-7X4NV6OJ.js";import{f as I,h as c,i as b,m as Buffer,n as d}from"./chunk-3KENBVE7.js";c();d();var Xr=I(D()),Ip=(0,Xr.createContext)(null);function gy({children:e,seedlessRepository:t}){return Xr.default.createElement(Ip.Provider,{value:{seedlessRepository:t}},e)}function yy(){let e=(0,Xr.useContext)(Ip);if(!e)throw new Error("useSeedlessContext must be used within a SeedlessProvider");return e}function Je(){return yy().seedlessRepository}c();d();c();d();c();d();var or="/seedless",Ap="/seedless",Hs="https://phantom-wallet.zendesk.com/hc/en-us/articles/32775281256851";c();d();var Bn=class extends Error{constructor(t){super("Failed to backup share from Juicebox"),this.reasonCode=t,this.reasonString=Ws[t]}},it=class extends Error{constructor(t,o){super("Failed to recover share from Juicebox"),this.guessesRemaining=o,this.reasonCode=t,this.reasonString=Zr[t]}},Gi=class extends Error{constructor(t){super("Failed to delete share from Juicebox"),this.reasonCode=t,this.reasonString=Ep[t]}},Ws=(n=>(n[n.InvalidAuth=0]="InvalidAuth",n[n.UpgradeRequired=1]="UpgradeRequired",n[n.Assertion=2]="Assertion",n[n.Transient=3]="Transient",n))(Ws||{}),Zr=(s=>(s[s.InvalidPin=0]="InvalidPin",s[s.NotRegistered=1]="NotRegistered",s[s.InvalidAuth=2]="InvalidAuth",s[s.UpgradeRequired=3]="UpgradeRequired",s[s.Assertion=4]="Assertion",s[s.Transient=5]="Transient",s[s.CooldownTimer=6]="CooldownTimer",s))(Zr||{}),Ep=(i=>(i[i.InvalidAuth=0]="InvalidAuth",i[i.UpgradeRequired=1]="UpgradeRequired",i[i.RateLimitExceeded=2]="RateLimitExceeded",i[i.Assertion=3]="Assertion",i[i.Transient=4]="Transient",i))(Ep||{});var rr=e=>e instanceof it;globalThis.JuiceboxGetAuthToken=async e=>{let t=Buffer.from(e).toString("hex");return globalThis.JuiceboxTokens[t]};var Ks=class{constructor(t){this.encoder=new TextEncoder,this.provider=t}async getJwts(t){let o=await Rt.api().bearer(t).post(`${Ap}/v1/juiceboxtoken`);co(o)&&this.provider.setJuiceboxTokens(o.data)}async sendJuiceboxShare(t,o,r,n){await this.getJwts(n);try{await this.provider.backupShare(t,o,this.encoder.encode(r),this.encoder.encode("phantom"),7),oe.addBreadcrumb("Successful backup share to Juicebox","se*dless")}catch(i){oe.captureMessage("se*dless","Failed to backup share to Juicebox","error",{errorCode:i});let a=new Bn(i);throw oe.captureError(a,"se*dless"),a}return!0}async getJuiceboxShare(t,o,r){await this.getJwts(r);try{let n=await this.provider.recoverShare(t,this.encoder.encode(o),this.encoder.encode("phantom"));return oe.addBreadcrumb("Successfully recovered share from Juicebox","se*dless"),n}catch(n){let i=n.guessesRemaining!==void 0?n.guessesRemaining:n.guesses_remaining;oe.captureMessage("se*dless","Failed to recover share from Juicebox","error",{errorCode:n.reason,guessesRemaining:i});let a=new it(n.reason,i);throw oe.captureError(a,"se*dless"),a}}async deleteShare(t,o){await this.getJwts(o);try{await this.provider.deleteShare(t),oe.addBreadcrumb("Successfully deleted share from Juicebox","se*dless")}catch(r){oe.captureMessage("se*dless","Failed to delete share from Juicebox","error",{errorCode:r});let n=new Gi(r);throw oe.captureError(n,"se*dless"),n}}};c();d();c();d();var qs="@phantom/seedless",xr={hasSeedlessBundle:["seedless",`${qs}:has_seedless_bundle`],needsBundleUpdate:["seedless",`${qs}:needs_bundle_update`],timeLeftCooldown:["seedless",`${qs}:time_left_cooldown`]};function hy(){let e=xr.hasSeedlessBundle,t=Je(),o=pi();return jo({enabled:o,queryKey:e,queryFn:async()=>await t.hasSeedlessBundle()})}c();d();c();d();var Qi=async e=>{await e.invalidateQueries({queryKey:xr.hasSeedlessBundle})},en=async e=>{await e.invalidateQueries({queryKey:xr.needsBundleUpdate})},Ji=async e=>{await e.invalidateQueries({refetchType:"all",predicate:t=>t.queryKey[0]!=="authEnabled"})};function by(){let e=Je(),t=_t(),o=wt();return It({mutationFn:async n=>await e.backup(n),onSuccess:async()=>{o.capture("seedlessBackupSuccess"),await Qi(t),await en(t)},onError:()=>{o.capture("seedlessBackupError")}})}c();d();function js(){let e=Je();return It({mutationFn:async()=>e.backupCleanup()})}c();d();function Gs(){let e=Je(),t=_t(),o=wt();return It({mutationFn:async(n,i=!0)=>await e.recover(n,i),onSuccess:async()=>{o.capture("seedlessRecoverSuccess"),await en(t)},onError:()=>{o.capture("seedlessRecoverError")}})}c();d();function Qs(){let e=Je(),t=wt();return It({mutationFn:async(r,n=!0)=>{await e.verifyPin(r,n)},onSuccess:()=>{t.capture("seedlessVerifyPinSuccess")},onError:()=>{t.capture("seedlessVerifyPinError")}})}c();d();function xy(){let e=Je(),t=_t();return It({mutationFn:async()=>await e.deleteBundle(),onSuccess:async()=>{await Qi(t),await en(t)}})}c();d();function Js(){let e=Je(),t=wt();return It({mutationFn:async r=>await e.resetBundleFromDevice(r.pin,r.seedlessEntropies),onSuccess:()=>{t.capture("seedlessResetPinSuccess")},onError:()=>{t.capture("seedlessResetPinError")}})}c();d();function Ys(){let e=xr.timeLeftCooldown,t=Je(),o=pi();return jo({enabled:o,queryKey:e,queryFn:async()=>await t.timeLeftCooldown(),refetchOnMount:"always"})}c();d();var Yi=I(D()),Dp=1e3,Xs=()=>{let[e,t]=(0,Yi.useState)(0);return(0,Yi.useEffect)(()=>{if(!e)return;let o=setInterval(async()=>{t(e-Dp)},Dp);return()=>clearInterval(o)},[e]),{cooldownTimer:e,setCooldownTimer:t}};c();d();var vy=(e=!1)=>{let t=Je(),o=async()=>{let{expired:r,timestampInMs:n}=await t.getPinVerificationTimer(e);return{expired:r,timestampInMs:n}};return jo({queryKey:["seedlessPinVerificationLockTimerInMs"],queryFn:o})};c();d();var vr=()=>{let e=_t(),t=Je();return It({mutationFn:async()=>{await t.setPinVerificationTimer(Date.now())},onSuccess:async()=>{await e.invalidateQueries({queryKey:["seedlessPinVerificationLockTimerInMs"]})}})};c();d();var Mo=I(D());c();d();var yo=e=>{let t=Zs(e),o=JSON.stringify(t);return ai(Bc(o))};function tn(e){return/^\d+$/.test(e)}var ro=e=>e.length!==4?!1:tn(e),Zs=e=>{if(Array.isArray(e))return e.map(t=>typeof t=="object"?Zs(t):t).sort();if(typeof e=="object"&&e!==null){let t={},o=Object.keys(e).sort();for(let r of o)t[r]=Zs(e[r]);return t}else return e},Fp=e=>{let t=Math.floor(e/1e3),o=Math.floor(t/60).toString(),r=t%60>9?`${t%60}`:`0${t%60}`;return{minutes:o,seconds:r}};var el=({t:e,onVerifyCallback:t,onForgot:o,onSuccess:r,onError:n})=>{let[i,a]=(0,Mo.useState)(""),[s,l]=(0,Mo.useState)(!1),[u,p]=(0,Mo.useState)(void 0),f=_t(),{mutateAsync:m,isPending:g,isSuccess:y}=Qs(),{mutateAsync:h}=vr();(0,Mo.useEffect)(()=>{y&&(t(),setTimeout(()=>{r()},200))},[y,r,t]);let w=(0,Mo.useCallback)(v=>{l(!1),v!==""&&!tn(v)?(l(!0),a("")):a(v)},[l,a]),k=(0,Mo.useCallback)(async()=>{try{if(!ro(i)){l(!0);return}await m(i),p(void 0),oe.addBreadcrumb("se*dless","Successfully verified PIN for user","info"),await h(),await Ji(f)}catch(v){if(a(""),v instanceof Error&&oe.captureError(v,"se*dless"),v&&rr(v)&&v.reasonCode===0){if(l(!0),v.guessesRemaining===0){o();return}p(v.guessesRemaining)}else n()}},[n,o,i,f,h,p,m]),C=!i||i.length!==4;return{pin:i,isInvalidPin:s,triesLeft:u,setPin:w,onVerify:k,onVerifyCallback:t,onForgot:o,title:e("seedlessVerifyPinPrimaryText"),description:e("seedlessVerifyPinSecondaryText"),confirmButtonText:e("seedlessPinConfirmButtonText"),forgotButtonText:e("seedlessVerifyPinForgotButtonText"),disabled:C||g}};c();d();var on=I(D());var tl=({t:e,seedlessMetas:t,onExportEntropy:o,onResetCallback:r,onSuccess:n,onError:i})=>{let[a,s]=(0,on.useState)(""),{mutateAsync:l}=vr(),{mutateAsync:u,isSuccess:p,isPending:f}=Js(),{mutateAsync:m}=js();(0,on.useEffect)(()=>{p&&(r(),setTimeout(()=>{n()},400))},[p,r,n,e]);let g=(0,on.useCallback)(async()=>{if(t?.length)try{let y=await o();await u({pin:a,seedlessEntropies:y}),await l(),await m()}catch(y){y instanceof Error&&oe.captureError(y,"se*dless"),i()}},[t,a,m,u,l,o,i]);return{pin:a,setPin:s,onReset:g,title:e("seedlessVerifyPinPrimaryText"),description:e("seedlessResetPinSecondaryText"),confirmButtonText:e("seedlessPinConfirmButtonText"),disabled:f}};c();d();var wr=I(D());var wy=({t:e,hasEnrolledAuthentication:t,syncedAccounts:o,onAccountsAdded:r,onOnboardingDone:n,onOnboardingProtect:i,onImportAndSyncAccounts:a,onForgot:s})=>{let l=_t(),u=wt(),{refetch:p}=Ys(),{mutateAsync:f}=vr(),[m,g]=(0,wr.useState)(!1),[y,h]=(0,wr.useState)({isInvalidPin:!1,triesLeft:void 0,pin:""}),{mutateAsync:w}=Gs(),[k,C]=(0,wr.useState)(!1),{cooldownTimer:v,setCooldownTimer:F}=Xs(),{minutes:z,seconds:T}=Fp(v);return(0,wr.useEffect)(()=>{!y.triesLeft||y.triesLeft>0||s()},[y.triesLeft,s]),(0,wr.useEffect)(()=>{(async()=>{let{data:N=0}=await p();return N})().then(N=>F(N))},[]),{isLoading:k,isError:m,handleInput:M=>{h(N=>({...N,isInvalidPin:M!==""&&!tn(M),pin:M}))},onSubmit:async()=>{if(!k){if(!ro(y.pin)){h(M=>({...M,isInvalidPin:!0,triesLeft:void 0,pin:""}));return}try{C(!0),g(!1),h(N=>({...N,isInvalidPin:!1,triesLeft:void 0}));let{entropy:M}=await w(y.pin);await a(M),await f(),await Ji(l),u.capture("seedlessEnterPinContinue"),o.length>1&&r?r():!t&&n?n():i()}catch(M){if(M&&rr(M)&&(M.reasonCode===0||M.reasonCode===6)){u.capture("seedlessPinError");let N=M?.guessesRemaining;if(N===0){h(_=>({..._,isInvalidPin:!0,triesLeft:N,pin:""})),s();return}let L=M?.reasonCode===0,{data:V=0}=await p();F(V),u.capture("seedlessPinError",{data:{triesLeft:N,cooldownTimer:V}}),h(_=>({..._,isInvalidPin:L,triesLeft:N,pin:""}))}else oe.captureError(M,"se*dless"),g(!0)}finally{C(!1)}}},onForgot:s,pinAttemptsLeft:y.triesLeft,isPinError:y.isInvalidPin,hasCooldownTimer:v>0,disabled:v>0||y.isInvalidPin||!y.pin||y.pin.length<4,pin:y.pin,continueButtonText:e("seedlessContinueText"),forgotButtonText:e("seedlessVerifyPinForgotButtonText"),attemptsLeftText:e("seedlessEnterPinNumTriesLeft",{numTries:y.triesLeft}),cooldownTimerText:e("seedlessEnterPinCooldown",{minutesLeft:z,secondsLeft:T}),invalidPinText:e("seedlessEnterPinInvalidPinError"),title:e("seedlessEnterPinPrimaryText"),loadingTitle:e("seedlessLoadingWalletPrimaryText"),loadingDescription:e("seedlessLoadingWalletSecondaryText"),errorTitle:e("seedlessLoadingWalletErrorPrimaryText"),errorDescription:e("seedlessErrorSecondaryText"),tryAgainButtonText:e("seedlessTryAgain")}};c();d();c();d();c();d();var Sy=rt.record(rt.string(),rt.number()),Cy=rt.object({type:rt.union([rt.literal("Buffer"),rt.literal("Object")]),data:rt.array(rt.number())}),Ty=rt.union([Sy,Cy]),Mp=rt.object({seeds:rt.array(rt.tuple([rt.string(),Ty]))}),Bp=e=>Py(e)?new Uint8Array(Buffer.from(e.data)):new Uint8Array(Object.values(e)),rn=class extends Error{},Py=e=>!!e&&typeof e=="object"&&ys("type",e)&&typeof e.type=="string"&&ys("data",e)&&Array.isArray(e.data)&&e.data.every(t=>!Number.isNaN(parseInt(t))),Np=rt.object({deviceId:rt.string(),encryptionKey:rt.string().length(44).base64()});async function ol(e,t,o){let r=await e.decryptionKeySharePhantom.foldAsync(async u=>Buffer.from(new Uint8Array(u)).toString("base64")),n=Buffer.from(e.encryptedAccounts,"hex").toString("base64"),i=Buffer.from(e.encryptionKey,"hex").toString("base64"),a={ciphertext:n,encryptionKey:i,share:r,externalId:t,mode:"JUICEBOX"},s=await Rt.api().bearer(o).headers({"Content-Type":"application/json"}).post(`${or}/v1/bundles`,a),l=co(s);return l||oe.addBreadcrumb("se*dless","Failed to backup bundle to backend","error",{status:s.status,statusText:s.statusText}),l}async function Nn(e,t){let o=await Rt.api().bearer(t).get(`${or}/v1/bundles/${e}?mode=JUICEBOX`);if(o.status===404)throw new rn("bundle not found");if(!co(o)){oe.addBreadcrumb("se*dless","Failed to recover bundle from backend","error",{status:o.status,statusText:o.statusText});let s=new Error("Got invalid response from seedless backend: "+o.statusText);throw oe.captureError(s,"se*dless"),s}if(!("share"in o.data)){oe.addBreadcrumb("se*dless","No share found in bundle","error");let s=new Error("Did not receive share from seedless backend");throw oe.captureError(s,"se*dless"),s}let r=new At(Buffer.from(o.data.share,"base64")),n=Buffer.from(o.data.ciphertext,"base64").toString("hex"),i=Buffer.from(o.data.encryptionKey,"base64").toString("hex");return{id:o.data.externalId,encryptedAccounts:n,encryptionKey:i,decryptionKeyShare1:r}}async function Op(e,t){let o=await Rt.api().bearer(t).get(`${or}/v1/bundles/${e}?mode=JUICEBOX&populate=none`);oe.addBreadcrumb("se*dless","Checking if user has a se*dless bundle","info");let r=new Error("Failed to check for se*dless bundle.");switch(o.status){case 200:return oe.addBreadcrumb("se*dless","A se*dless bundle has been found for this user","info",{status:o.status,statusText:o.statusText}),!0;case 404:return oe.addBreadcrumb("se*dless","No se*dless bundle found for user","info",{status:o.status,statusText:o.statusText}),!1;default:throw oe.captureError(r,"se*dless"),r}}async function _p(e,t){let o=await Rt.api().bearer(t).headers({"Content-Type":"application/json"}).delete(`${or}/v1/bundles/${e}`,{mode:"JUICEBOX"});oe.addBreadcrumb("se*dless","Checking if user has a seedless bundle","info");let r=new Error("Failed to delete seedless bundle.");switch(o.status){case 200:case 204:return oe.addBreadcrumb("se*dless","A seedless bundle has been deleted for this user","info",{status:o.status,statusText:o.statusText}),!0;case 404:return oe.addBreadcrumb("se*dless","No seedless bundle found for user","info",{status:o.status,statusText:o.statusText}),!1;default:throw oe.captureError(r,"se*dless"),r}}async function rl(e,t){let o=await Rt.api().bearer(e).get(`${or}/v1/device-encryption-keys/${t}`);if(o.status===404)return oe.addBreadcrumb("se*dless","No device encryption key found for user","info"),null;if(!co(o))throw new Error("Unable to get device encryption key.");try{let{encryptionKey:r}=Np.parse(o.data);return new At(Buffer.from(r,"base64"))}catch{throw new Error("Unable to parse device encryption key from response.")}}async function $p(e,t,o){let r=await Rt.api().bearer(e).post(`${or}/v1/device-encryption-keys`,{deviceId:t,encryptionKey:o.fold(n=>Buffer.from(n).toString("base64"))});if(!co(r))throw new Error("Unable to set device encryption key.")}async function zp(e,t){let o=await Rt.api().bearer(e).delete(`${or}/v1/device-encryption-keys/${t}`);if(!co(o))throw new Error("Unable to delete device encryption key.")}c();d();c();d();c();d();c();d();function nn(e){if(!Number.isSafeInteger(e)||e<0)throw new Error(`positive integer expected, not ${e}`)}function nl(e){if(typeof e!="boolean")throw new Error(`boolean expected, not ${e}`)}function il(e){return e instanceof Uint8Array||e!=null&&typeof e=="object"&&e.constructor.name==="Uint8Array"}function Ze(e,...t){if(!il(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error(`Uint8Array expected of length ${t}, not of length=${e.length}`)}function al(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function Up(e,t){Ze(e);let o=t.outputLen;if(e.length<o)throw new Error(`digestInto() expects output buffer of length at least ${o}`)}var Bo=e=>new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4)),Vp=e=>new DataView(e.buffer,e.byteOffset,e.byteLength),ky=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;if(!ky)throw new Error("Non little-endian hardware is not supported");function sl(e){if(typeof e!="string")throw new Error(`string expected, got ${typeof e}`);return new Uint8Array(new TextEncoder().encode(e))}function Xi(e){if(typeof e=="string")e=sl(e);else if(il(e))e=e.slice();else throw new Error(`Uint8Array expected, got ${typeof e}`);return e}function Hp(...e){let t=0;for(let r=0;r<e.length;r++){let n=e[r];Ze(n),t+=n.length}let o=new Uint8Array(t);for(let r=0,n=0;r<e.length;r++){let i=e[r];o.set(i,n),n+=i.length}return o}function Wp(e,t){if(t==null||typeof t!="object")throw new Error("options must be defined");return Object.assign(e,t)}function Kp(e,t){if(e.length!==t.length)return!1;let o=0;for(let r=0;r<e.length;r++)o|=e[r]^t[r];return o===0}var ll=(e,t)=>(Object.assign(t,e),t);function cl(e,t,o,r){if(typeof e.setBigUint64=="function")return e.setBigUint64(t,o,r);let n=BigInt(32),i=BigInt(4294967295),a=Number(o>>n&i),s=Number(o&i),l=r?4:0,u=r?0:4;e.setUint32(t+l,a,r),e.setUint32(t+u,s,r)}c();d();var ft=(e,t)=>e[t++]&255|(e[t++]&255)<<8,dl=class{constructor(t){this.blockLen=16,this.outputLen=16,this.buffer=new Uint8Array(16),this.r=new Uint16Array(10),this.h=new Uint16Array(10),this.pad=new Uint16Array(8),this.pos=0,this.finished=!1,t=Xi(t),Ze(t,32);let o=ft(t,0),r=ft(t,2),n=ft(t,4),i=ft(t,6),a=ft(t,8),s=ft(t,10),l=ft(t,12),u=ft(t,14);this.r[0]=o&8191,this.r[1]=(o>>>13|r<<3)&8191,this.r[2]=(r>>>10|n<<6)&7939,this.r[3]=(n>>>7|i<<9)&8191,this.r[4]=(i>>>4|a<<12)&255,this.r[5]=a>>>1&8190,this.r[6]=(a>>>14|s<<2)&8191,this.r[7]=(s>>>11|l<<5)&8065,this.r[8]=(l>>>8|u<<8)&8191,this.r[9]=u>>>5&127;for(let p=0;p<8;p++)this.pad[p]=ft(t,16+2*p)}process(t,o,r=!1){let n=r?0:2048,{h:i,r:a}=this,s=a[0],l=a[1],u=a[2],p=a[3],f=a[4],m=a[5],g=a[6],y=a[7],h=a[8],w=a[9],k=ft(t,o+0),C=ft(t,o+2),v=ft(t,o+4),F=ft(t,o+6),z=ft(t,o+8),T=ft(t,o+10),O=ft(t,o+12),U=ft(t,o+14),M=i[0]+(k&8191),N=i[1]+((k>>>13|C<<3)&8191),L=i[2]+((C>>>10|v<<6)&8191),V=i[3]+((v>>>7|F<<9)&8191),_=i[4]+((F>>>4|z<<12)&8191),q=i[5]+(z>>>1&8191),Y=i[6]+((z>>>14|T<<2)&8191),te=i[7]+((T>>>11|O<<5)&8191),Z=i[8]+((O>>>8|U<<8)&8191),ne=i[9]+(U>>>5|n),B=0,ue=B+M*s+N*(5*w)+L*(5*h)+V*(5*y)+_*(5*g);B=ue>>>13,ue&=8191,ue+=q*(5*m)+Y*(5*f)+te*(5*p)+Z*(5*u)+ne*(5*l),B+=ue>>>13,ue&=8191;let Pe=B+M*l+N*s+L*(5*w)+V*(5*h)+_*(5*y);B=Pe>>>13,Pe&=8191,Pe+=q*(5*g)+Y*(5*m)+te*(5*f)+Z*(5*p)+ne*(5*u),B+=Pe>>>13,Pe&=8191;let ye=B+M*u+N*l+L*s+V*(5*w)+_*(5*h);B=ye>>>13,ye&=8191,ye+=q*(5*y)+Y*(5*g)+te*(5*m)+Z*(5*f)+ne*(5*p),B+=ye>>>13,ye&=8191;let xe=B+M*p+N*u+L*l+V*s+_*(5*w);B=xe>>>13,xe&=8191,xe+=q*(5*h)+Y*(5*y)+te*(5*g)+Z*(5*m)+ne*(5*f),B+=xe>>>13,xe&=8191;let he=B+M*f+N*p+L*u+V*l+_*s;B=he>>>13,he&=8191,he+=q*(5*w)+Y*(5*h)+te*(5*y)+Z*(5*g)+ne*(5*m),B+=he>>>13,he&=8191;let Le=B+M*m+N*f+L*p+V*u+_*l;B=Le>>>13,Le&=8191,Le+=q*s+Y*(5*w)+te*(5*h)+Z*(5*y)+ne*(5*g),B+=Le>>>13,Le&=8191;let _e=B+M*g+N*m+L*f+V*p+_*u;B=_e>>>13,_e&=8191,_e+=q*l+Y*s+te*(5*w)+Z*(5*h)+ne*(5*y),B+=_e>>>13,_e&=8191;let be=B+M*y+N*g+L*m+V*f+_*p;B=be>>>13,be&=8191,be+=q*u+Y*l+te*s+Z*(5*w)+ne*(5*h),B+=be>>>13,be&=8191;let Fe=B+M*h+N*y+L*g+V*m+_*f;B=Fe>>>13,Fe&=8191,Fe+=q*p+Y*u+te*l+Z*s+ne*(5*w),B+=Fe>>>13,Fe&=8191;let $e=B+M*w+N*h+L*y+V*g+_*m;B=$e>>>13,$e&=8191,$e+=q*f+Y*p+te*u+Z*l+ne*s,B+=$e>>>13,$e&=8191,B=(B<<2)+B|0,B=B+ue|0,ue=B&8191,B=B>>>13,Pe+=B,i[0]=ue,i[1]=Pe,i[2]=ye,i[3]=xe,i[4]=he,i[5]=Le,i[6]=_e,i[7]=be,i[8]=Fe,i[9]=$e}finalize(){let{h:t,pad:o}=this,r=new Uint16Array(10),n=t[1]>>>13;t[1]&=8191;for(let s=2;s<10;s++)t[s]+=n,n=t[s]>>>13,t[s]&=8191;t[0]+=n*5,n=t[0]>>>13,t[0]&=8191,t[1]+=n,n=t[1]>>>13,t[1]&=8191,t[2]+=n,r[0]=t[0]+5,n=r[0]>>>13,r[0]&=8191;for(let s=1;s<10;s++)r[s]=t[s]+n,n=r[s]>>>13,r[s]&=8191;r[9]-=8192;let i=(n^1)-1;for(let s=0;s<10;s++)r[s]&=i;i=~i;for(let s=0;s<10;s++)t[s]=t[s]&i|r[s];t[0]=(t[0]|t[1]<<13)&65535,t[1]=(t[1]>>>3|t[2]<<10)&65535,t[2]=(t[2]>>>6|t[3]<<7)&65535,t[3]=(t[3]>>>9|t[4]<<4)&65535,t[4]=(t[4]>>>12|t[5]<<1|t[6]<<14)&65535,t[5]=(t[6]>>>2|t[7]<<11)&65535,t[6]=(t[7]>>>5|t[8]<<8)&65535,t[7]=(t[8]>>>8|t[9]<<5)&65535;let a=t[0]+o[0];t[0]=a&65535;for(let s=1;s<8;s++)a=(t[s]+o[s]|0)+(a>>>16)|0,t[s]=a&65535}update(t){al(this);let{buffer:o,blockLen:r}=this;t=Xi(t);let n=t.length;for(let i=0;i<n;){let a=Math.min(r-this.pos,n-i);if(a===r){for(;r<=n-i;i+=r)this.process(t,i);continue}o.set(t.subarray(i,i+a),this.pos),this.pos+=a,i+=a,this.pos===r&&(this.process(o,0,!1),this.pos=0)}return this}destroy(){this.h.fill(0),this.r.fill(0),this.buffer.fill(0),this.pad.fill(0)}digestInto(t){al(this),Up(t,this),this.finished=!0;let{buffer:o,h:r}=this,{pos:n}=this;if(n){for(o[n++]=1;n<16;n++)o[n]=0;this.process(o,0,!0)}this.finalize();let i=0;for(let a=0;a<8;a++)t[i++]=r[a]>>>0,t[i++]=r[a]>>>8;return t}digest(){let{buffer:t,outputLen:o}=this;this.digestInto(t);let r=t.slice(0,o);return this.destroy(),r}};function Iy(e){let t=(r,n)=>e(n).update(Xi(r)).digest(),o=e(new Uint8Array(32));return t.outputLen=o.outputLen,t.blockLen=o.blockLen,t.create=r=>e(r),t}var qp=Iy(e=>new dl(e));c();d();var jp=e=>Uint8Array.from(e.split("").map(t=>t.charCodeAt(0))),Ay=jp("expand 16-byte k"),Ey=jp("expand 32-byte k"),Ly=Bo(Ay),Gp=Bo(Ey),w2=Gp.slice();function R(e,t){return e<<t|e>>>32-t}function ul(e){return e.byteOffset%4===0}var Zi=64,Dy=16,Qp=2**32-1,Rp=new Uint32Array;function Fy(e,t,o,r,n,i,a,s){let l=n.length,u=new Uint8Array(Zi),p=Bo(u),f=ul(n)&&ul(i),m=f?Bo(n):Rp,g=f?Bo(i):Rp;for(let y=0;y<l;a++){if(e(t,o,r,p,a,s),a>=Qp)throw new Error("arx: counter overflow");let h=Math.min(Zi,l-y);if(f&&h===Zi){let w=y/4;if(y%4!==0)throw new Error("arx: invalid block position");for(let k=0,C;k<Dy;k++)C=w+k,g[C]=m[C]^p[k];y+=Zi;continue}for(let w=0,k;w<h;w++)k=y+w,i[k]=n[k]^u[w];y+=h}}function pl(e,t){let{allowShortKeys:o,extendNonceFn:r,counterLength:n,counterRight:i,rounds:a}=Wp({allowShortKeys:!1,counterLength:8,counterRight:!1,rounds:20},t);if(typeof e!="function")throw new Error("core must be a function");return nn(n),nn(a),nl(i),nl(o),(s,l,u,p,f=0)=>{Ze(s),Ze(l),Ze(u);let m=u.length;if(p||(p=new Uint8Array(m)),Ze(p),nn(f),f<0||f>=Qp)throw new Error("arx: counter overflow");if(p.length<m)throw new Error(`arx: output (${p.length}) is shorter than data (${m})`);let g=[],y=s.length,h,w;if(y===32)h=s.slice(),g.push(h),w=Gp;else if(y===16&&o)h=new Uint8Array(32),h.set(s),h.set(s,16),w=Ly,g.push(h);else throw new Error(`arx: invalid 32-byte key, got length=${y}`);ul(l)||(l=l.slice(),g.push(l));let k=Bo(h);if(r){if(l.length!==24)throw new Error("arx: extended nonce must be 24 bytes");r(w,k,Bo(l.subarray(0,16)),k),l=l.subarray(16)}let C=16-n;if(C!==l.length)throw new Error(`arx: nonce must be ${C} or 16 bytes`);if(C!==12){let F=new Uint8Array(12);F.set(l,i?0:12-l.length),l=F,g.push(l)}let v=Bo(l);for(Fy(e,w,k,v,u,p,f,a);g.length>0;)g.pop().fill(0);return p}}function Xp(e,t,o,r,n,i=20){let a=e[0],s=e[1],l=e[2],u=e[3],p=t[0],f=t[1],m=t[2],g=t[3],y=t[4],h=t[5],w=t[6],k=t[7],C=n,v=o[0],F=o[1],z=o[2],T=a,O=s,U=l,M=u,N=p,L=f,V=m,_=g,q=y,Y=h,te=w,Z=k,ne=C,B=v,ue=F,Pe=z;for(let xe=0;xe<i;xe+=2)T=T+N|0,ne=R(ne^T,16),q=q+ne|0,N=R(N^q,12),T=T+N|0,ne=R(ne^T,8),q=q+ne|0,N=R(N^q,7),O=O+L|0,B=R(B^O,16),Y=Y+B|0,L=R(L^Y,12),O=O+L|0,B=R(B^O,8),Y=Y+B|0,L=R(L^Y,7),U=U+V|0,ue=R(ue^U,16),te=te+ue|0,V=R(V^te,12),U=U+V|0,ue=R(ue^U,8),te=te+ue|0,V=R(V^te,7),M=M+_|0,Pe=R(Pe^M,16),Z=Z+Pe|0,_=R(_^Z,12),M=M+_|0,Pe=R(Pe^M,8),Z=Z+Pe|0,_=R(_^Z,7),T=T+L|0,Pe=R(Pe^T,16),te=te+Pe|0,L=R(L^te,12),T=T+L|0,Pe=R(Pe^T,8),te=te+Pe|0,L=R(L^te,7),O=O+V|0,ne=R(ne^O,16),Z=Z+ne|0,V=R(V^Z,12),O=O+V|0,ne=R(ne^O,8),Z=Z+ne|0,V=R(V^Z,7),U=U+_|0,B=R(B^U,16),q=q+B|0,_=R(_^q,12),U=U+_|0,B=R(B^U,8),q=q+B|0,_=R(_^q,7),M=M+N|0,ue=R(ue^M,16),Y=Y+ue|0,N=R(N^Y,12),M=M+N|0,ue=R(ue^M,8),Y=Y+ue|0,N=R(N^Y,7);let ye=0;r[ye++]=a+T|0,r[ye++]=s+O|0,r[ye++]=l+U|0,r[ye++]=u+M|0,r[ye++]=p+N|0,r[ye++]=f+L|0,r[ye++]=m+V|0,r[ye++]=g+_|0,r[ye++]=y+q|0,r[ye++]=h+Y|0,r[ye++]=w+te|0,r[ye++]=k+Z|0,r[ye++]=C+ne|0,r[ye++]=v+B|0,r[ye++]=F+ue|0,r[ye++]=z+Pe|0}function My(e,t,o,r){let n=e[0],i=e[1],a=e[2],s=e[3],l=t[0],u=t[1],p=t[2],f=t[3],m=t[4],g=t[5],y=t[6],h=t[7],w=o[0],k=o[1],C=o[2],v=o[3];for(let z=0;z<20;z+=2)n=n+l|0,w=R(w^n,16),m=m+w|0,l=R(l^m,12),n=n+l|0,w=R(w^n,8),m=m+w|0,l=R(l^m,7),i=i+u|0,k=R(k^i,16),g=g+k|0,u=R(u^g,12),i=i+u|0,k=R(k^i,8),g=g+k|0,u=R(u^g,7),a=a+p|0,C=R(C^a,16),y=y+C|0,p=R(p^y,12),a=a+p|0,C=R(C^a,8),y=y+C|0,p=R(p^y,7),s=s+f|0,v=R(v^s,16),h=h+v|0,f=R(f^h,12),s=s+f|0,v=R(v^s,8),h=h+v|0,f=R(f^h,7),n=n+u|0,v=R(v^n,16),y=y+v|0,u=R(u^y,12),n=n+u|0,v=R(v^n,8),y=y+v|0,u=R(u^y,7),i=i+p|0,w=R(w^i,16),h=h+w|0,p=R(p^h,12),i=i+p|0,w=R(w^i,8),h=h+w|0,p=R(p^h,7),a=a+f|0,k=R(k^a,16),m=m+k|0,f=R(f^m,12),a=a+f|0,k=R(k^a,8),m=m+k|0,f=R(f^m,7),s=s+l|0,C=R(C^s,16),g=g+C|0,l=R(l^g,12),s=s+l|0,C=R(C^s,8),g=g+C|0,l=R(l^g,7);let F=0;r[F++]=n,r[F++]=i,r[F++]=a,r[F++]=s,r[F++]=w,r[F++]=k,r[F++]=C,r[F++]=v}var By=pl(Xp,{counterRight:!1,counterLength:4,allowShortKeys:!1}),Ny=pl(Xp,{counterRight:!1,counterLength:8,extendNonceFn:My,allowShortKeys:!1});var Oy=new Uint8Array(16),Jp=(e,t)=>{e.update(t);let o=t.length%16;o&&e.update(Oy.subarray(o))},_y=new Uint8Array(32);function Yp(e,t,o,r,n){let i=e(t,o,_y),a=qp.create(i);n&&Jp(a,n),Jp(a,r);let s=new Uint8Array(16),l=Vp(s);cl(l,0,BigInt(n?n.length:0),!0),cl(l,8,BigInt(r.length),!0),a.update(s);let u=a.digest();return i.fill(0),u}var Zp=e=>(t,o,r)=>(Ze(t,32),Ze(o),{encrypt:(i,a)=>{let s=i.length,l=s+16;a?Ze(a,l):a=new Uint8Array(l),e(t,o,i,a,1);let u=Yp(e,t,o,a.subarray(0,-16),r);return a.set(u,s),a},decrypt:(i,a)=>{let s=i.length,l=s-16;if(s<16)throw new Error("encrypted data must be at least 16 bytes");a?Ze(a,l):a=new Uint8Array(l);let u=i.subarray(0,-16),p=i.subarray(-16),f=Yp(e,t,o,u,r);if(!Kp(p,f))throw new Error("invalid tag");return e(t,o,u,a,1),a}}),E2=ll({blockSize:64,nonceLength:12,tagLength:16},Zp(By)),ml=ll({blockSize:64,nonceLength:24,tagLength:16},Zp(Ny));c();d();c();d();var Sr=typeof globalThis=="object"&&"crypto"in globalThis?globalThis.crypto:void 0;function em(e=32){if(Sr&&typeof Sr.getRandomValues=="function")return Sr.getRandomValues(new Uint8Array(e));throw new Error("crypto.getRandomValues must be defined")}function fl(){if(Sr&&typeof Sr.subtle=="object"&&Sr.subtle!=null)return Sr.subtle;throw new Error("crypto.subtle must be defined")}function gl(e){return nn(e.nonceLength),(t,...o)=>({encrypt:(r,...n)=>{let{nonceLength:i}=e,a=em(i),s=e(t,a,...o).encrypt(r,...n),l=Hp(a,s);return s.fill(0),l},decrypt:(r,...n)=>{let{nonceLength:i}=e,a=r.subarray(0,i),s=r.subarray(i);return e(t,a,...o).decrypt(s,...n)}})}var tm={async encrypt(e,t,o,r){let n=fl(),i=await n.importKey("raw",e,t,!0,["encrypt"]),a=await n.encrypt(o,i,r);return new Uint8Array(a)},async decrypt(e,t,o,r){let n=fl(),i=await n.importKey("raw",e,t,!0,["decrypt"]),a=await n.decrypt(o,i,r);return new Uint8Array(a)}},ho={CBC:"AES-CBC",CTR:"AES-CTR",GCM:"AES-GCM"};function $y(e,t,o){if(e===ho.CBC)return{name:ho.CBC,iv:t};if(e===ho.CTR)return{name:ho.CTR,counter:t,length:64};if(e===ho.GCM)return o?{name:ho.GCM,iv:t,additionalData:o}:{name:ho.GCM,iv:t};throw new Error("unknown aes block mode")}function yl(e){return(t,o,r)=>{Ze(t),Ze(o);let n={name:e,length:t.length*8},i=$y(e,o,r);return{encrypt(a){return Ze(a),tm.encrypt(t,n,i,a)},decrypt(a){return Ze(a),tm.decrypt(t,n,i,a)}}}}var z2=yl(ho.CBC),U2=yl(ho.CTR),V2=yl(ho.GCM);c();d();c();d();function hl(e){return self.crypto.getRandomValues(new Uint8Array(e))}var ta=new Uint8Array([0,255,200,8,145,16,208,54,90,62,216,67,153,119,254,24,35,32,7,112,161,108,12,127,98,139,64,70,199,75,224,14,235,22,232,173,207,205,57,83,106,39,53,147,212,78,72,195,43,121,84,40,9,120,15,33,144,135,20,42,169,156,214,116,180,124,222,237,177,134,118,164,152,226,150,143,2,50,28,193,51,238,239,129,253,48,92,19,157,41,23,196,17,68,140,128,243,115,66,30,29,181,240,18,209,91,65,162,215,44,233,213,89,203,80,168,220,252,242,86,114,166,101,47,159,155,61,186,125,194,69,130,167,87,182,163,122,117,79,174,63,55,109,71,97,190,171,211,95,176,88,175,202,94,250,133,228,77,138,5,251,96,183,123,184,38,74,103,198,26,248,105,37,179,219,189,102,221,241,210,223,3,141,52,217,146,13,99,85,170,73,236,188,149,60,132,11,245,230,231,229,172,126,110,185,249,218,142,154,201,36,225,10,21,107,58,160,81,244,234,178,151,158,93,34,136,148,206,25,1,113,76,165,227,197,49,187,204,31,45,59,82,111,246,46,137,247,192,104,27,100,4,6,191,131,56]),om=new Uint8Array([1,229,76,181,251,159,252,18,3,52,212,196,22,186,31,54,5,92,103,87,58,213,33,90,15,228,169,249,78,100,99,238,17,55,224,16,210,172,165,41,51,89,59,48,109,239,244,123,85,235,77,80,183,42,7,141,255,38,215,240,194,126,9,140,26,106,98,11,93,130,27,143,46,190,166,29,231,157,45,138,114,217,241,39,50,188,119,133,150,112,8,105,86,223,153,148,161,144,24,187,250,122,176,167,248,171,40,214,21,142,203,242,19,230,120,97,63,137,70,13,53,49,136,163,65,128,202,23,95,83,131,254,195,155,69,57,225,245,158,25,94,182,207,75,56,4,185,43,226,193,74,221,72,12,208,125,61,88,222,124,216,20,107,135,71,232,121,132,115,60,189,146,201,35,139,151,149,68,220,173,64,101,134,162,164,204,127,236,192,175,145,253,247,79,129,47,91,234,168,28,2,209,152,113,237,37,227,36,6,104,179,147,44,111,62,108,10,184,206,174,116,177,66,180,30,211,73,233,156,200,198,199,34,110,219,32,191,67,81,82,102,178,118,96,218,197,243,246,170,205,154,160,117,84,14,1]);function ea(e,t){if(!Number.isInteger(e)||e<0||e>255)throw new RangeError("Number is out of Uint8 range");if(!Number.isInteger(t)||t<0||t>255)throw new RangeError("Number is out of Uint8 range");return e^t}function zy(e,t){if(!Number.isInteger(e)||e<0||e>255)throw new RangeError("Number is out of Uint8 range");if(!Number.isInteger(t)||t<0||t>255)throw new RangeError("Number is out of Uint8 range");if(t===0)throw new Error("cannot divide by zero");let o=ta[e],r=ta[t],n=(o-r+255)%255,i=om[n];return e===0?0:i}function bl(e,t){if(!Number.isInteger(e)||e<0||e>255)throw new RangeError("Number is out of Uint8 range");if(!Number.isInteger(t)||t<0||t>255)throw new RangeError("Number is out of Uint8 range");let o=ta[e],r=ta[t],n=(o+r)%255,i=om[n];return e===0||t===0?0:i}function Uy(e,t,o){if(e.length!==t.length)throw new Error("sample length mistmatch");let r=e.length,n=0,i=0;for(let a=0;a<r;a++){n=1;for(let s=0;s<r;++s){if(a===s)continue;let l=ea(o,e[s]),u=ea(e[a],e[s]),p=zy(l,u);n=bl(n,p)}i=ea(i,bl(t[a],n))}return i}function Vy(e,t,o){if(t===0)throw new Error("cannot evaluate secret polynomial at zero");let r=e[o];for(let n=o-1;n>=0;n--){let i=e[n];r=ea(bl(r,t),i)}return r}function rm(){return hl(1)[0]}function Hy(){for(;;){let e=rm();if(e>0)return e}}function Wy(e,t){let o=new Uint8Array(t+1);o[0]=e;for(let r=1;r<=t;r++){let n=r===t;o[r]=n?Hy():rm()}return o}function Ky(){let e=new Uint8Array(255);for(let o=0;o<255;o++)e[o]=o+1;let t=hl(255);for(let o=0;o<255;o++){let r=t[o]%255,n=e[o];e[o]=e[r],e[r]=n}return e}var Ut={instanceOf(e,t,o){if(e.constructor!==t)throw new TypeError(o)},inRange(e,t,o,r){if(!(t<o&&e>=t&&e<o))throw new RangeError(r)},greaterThanOrEqualTo(e,t,o){if(e<t)throw new Error(o)},equalTo(e,t,o){if(e!==t)throw new Error(o)}};async function nm(e,t,o){Ut.instanceOf(e,Uint8Array,"secret must be a Uint8Array"),Ut.greaterThanOrEqualTo(e.byteLength,1,"secret cannot be empty"),Ut.instanceOf(t,Number,"shares must be a number"),Ut.inRange(t,2,256,"shares must be at least 2 and at most 255"),Ut.instanceOf(o,Number,"threshold must be a number"),Ut.inRange(o,2,256,"threshold must be at least 2 and at most 255"),Ut.greaterThanOrEqualTo(t,o,"shares cannot be less than threshold");let r=[],n=e.byteLength,i=Ky();for(let s=0;s<t;s++){let l=new Uint8Array(n+1);l[n]=i[s],r.push(l)}let a=o-1;for(let s=0;s<n;s++){let l=e[s],u=Wy(l,a);for(let p=0;p<t;++p){let f=i[p],m=Vy(u,f,a);r[p][s]=m}}return r}async function im(e){Ut.instanceOf(e,Array,"shares must be an Array"),Ut.inRange(e.length,2,256,"shares must have at least 2 and at most 255 elements");let t=e[0];Ut.instanceOf(t,Uint8Array,"each share must be a Uint8Array");for(let u of e)Ut.instanceOf(u,Uint8Array,"each share must be a Uint8Array"),Ut.greaterThanOrEqualTo(u.byteLength,2,"each share must be at least 2 bytes"),Ut.equalTo(u.byteLength,t.byteLength,"all shares must have the same byte length");let o=e.length,r=t.byteLength,n=r-1,i=new Uint8Array(n),a=new Uint8Array(o),s=new Uint8Array(o),l=new Set;for(let u=0;u<o;u++){let f=e[u][r-1];if(l.has(f))throw new Error("shares must contain unique values but a duplicate was found");l.add(f),a[u]=f}for(let u=0;u<n;u++){for(let p=0;p<o;++p)s[p]=e[p][u];i[u]=Uy(a,s,0)}return i}var am=async(e,t)=>{if(e.length!==32)throw new Error("encryption key must be 32 bytes");let o={seeds:[]};for(let u of t){let p="unused",f=await u.foldAsync(async m=>new Uint8Array(m));o.seeds.push([p,f])}let r=gl(ml)(e),n=sl(JSON.stringify(o)),i=r.encrypt(n),[a,s]=await nm(e,2,2);return{encryptedAccounts:ai(i),decryptionKeySharePhantom:At.from(a),decryptionKeyShareExternal:At.from(s),encryptionKey:"unused"}},xl=async e=>{let t=await qy(e.decryptionKeySharePhantom,e.decryptionKeyShareExternal),r=gl(ml)(t).decrypt(Mc(e.encryptedAccounts)),n=Mp.safeParse(JSON.parse(Buffer.from(r).toString()));if(!n.success)throw new Error("Could not parse encrypted seedless bundle");return n.data.seeds.map(s=>{let l=s[1],u=Bp(l);return At.from(u)})},qy=async(e,t)=>{let o=e.fold(i=>new Uint8Array(i)),r=t.fold(i=>new Uint8Array(i));return await im([o,r])};c();d();c();d();var sm={realms:[{id:"a4caa86247c60f61b2454d8cbdb1ba2a",address:"https://production-juicebox.phantom.dev"},{id:"********************************",address:"https://juicebox.rpcpool.com"}],register_threshold:2,recover_threshold:2,pin_hashing_mode:"Standard2019"};var vl=[sm],oa={};for(let e of vl)oa[yo(e)]=e;var wl=vl[vl.length-1];c();d();var lm={realms:[{id:"497efc0c17f850465b9387a549affb2b",address:"https://development-juicebox.phantom.dev"},{id:"********************************",address:"https://juicebox.rpcpool.com"}],register_threshold:2,recover_threshold:2,pin_hashing_mode:"Standard2019"};var cm={realms:[{id:"497efc0c17f850465b9387a549affb2b",address:"https://development-juicebox.phantom.dev/"},{id:"********************************",address:"https://juicebox.rpcpool.com"}],register_threshold:2,recover_threshold:2,pin_hashing_mode:"Standard2019"};var Sl=[lm,cm],ra={};for(let e of Sl)ra[yo(e)]=e;var Cl=Sl[Sl.length-1];c();d();var Tl="seedlessJuiceboxPinCooldownTimer",dm={5:30,4:60,3:120,2:300,1:300},Pl=async(e,t)=>{let o=dm[e];if(!(e in dm))return;let r=Date.now()+o*1e3;t.set(Tl,r)},kl=async e=>{await e.remove(Tl)},Il=async e=>{let t=await e.get(Tl),o=Date.now();return t===null||t<0?0:t-o};var On,na,Zy=e=>({backup:t=>o0(e,t),backupCleanup:()=>r0(),recover:(t,o)=>i0(e,t,o),recoverWithShare:t=>s0(e,t),verifyPin:(t,o)=>a0(e,t,o),resetBundleFromDevice:(t,o)=>n0(e,t,o),hasSeedlessBundle:()=>pm(e),deleteBundle:()=>mm(e),needsBundleUpdate:()=>l0(e),timeLeftCooldown:()=>c0(e),getPinVerificationTimer:t=>d0(e,t),setPinVerificationTimer:t=>u0(e,t),getDeviceEncryptionKey:()=>p0(e),generateDeviceEncryptionKey:gm,setDeviceEncryptionKey:t=>hm(e,t),deleteDeviceEncryptionKey:()=>m0(e),getOrCreateSeedlessDeviceID:()=>ym(e)}),e0=new iu,t0=(e,t)=>e.runExclusive?.(t)??e0.runExclusive(t),Al={},o0=async(e,t)=>{if(!ro(t))throw new Error("Invalid PIN provided. Only numbers are accepted.");return await xm(e),um(e,t,[na])},r0=async()=>{na=void 0,On=void 0},n0=async(e,t,o)=>{if(!ro(t))throw new Error("Invalid PIN provided. Only numbers are accepted.");return await mm(e),await xm(e),um(e,t,o)},um=async(e,t,o)=>{if(!ro(t))throw new Error("Invalid PIN provided. Only numbers are accepted.");if(!On)throw new Error("bundle encryption key should be set");let r=await e.authRepository.getUser();if(!r)throw new Error("User is not logged in");let{accessToken:n}=r,i=yo(Cr());if(await pm(e,i))return{entropy:o[0]};let s=On.fold(g=>g),l=await am(s,o),u=await l.decryptionKeyShareExternal.foldAsync(async g=>new Uint8Array(g));if(!await(await e.juiceboxClient()).sendJuiceboxShare(Cr(),u,t,n))throw new Error("Failed to send share to Juicebox");if(!await ol(l,i,n))throw new Error("Failed to backup bundle to backend");return oe.captureMessage("Seedless: successful backup","se*dless"),{entropy:o[0]}},i0=async({authRepository:e,juiceboxClient:t,storage:o},r,n)=>{if(await Il(o)>0)throw new it(6);if(!ro(r))throw new it(0);let a=await e.getUser();if(!a)throw new it(2);let{accessToken:s}=a,l=await Nn("latest",s),u=El()[l.id];if(!u)throw new it(3);let p=await t(),f=new Uint8Array;try{f=new Uint8Array(await p.getJuiceboxShare(u,r,s))}catch(h){throw rr(h)&&h.guessesRemaining!==void 0&&await Pl(h.guessesRemaining,o),h}await kl(o),n&&await bm(e,t,r,l,f);let m=new At(f),g={encryptedAccounts:l.encryptedAccounts,encryptionKey:l.encryptionKey,decryptionKeySharePhantom:l.decryptionKeyShare1,decryptionKeyShareExternal:m},y=await xl(g);if(y.length===0)throw oe.captureError(new Error("Decrypted bundles has no seeds"),"se*dless"),new it(1);return oe.captureMessage("Seedless: successful recovery","se*dless"),{entropy:y[0]}},a0=async({authRepository:e,juiceboxClient:t,storage:o},r,n)=>{if(!ro(r))throw new it(0);let i=await e.getUser();if(!i)throw new it(2);let{accessToken:a}=i,s=await Nn("latest",a),l=await t(),u=El()[s.id];if(!u)throw new it(1);let p=new Uint8Array;try{p=new Uint8Array(await l.getJuiceboxShare(u,r,a))}catch(m){throw rr(m)&&m.guessesRemaining!==void 0&&await Pl(m.guessesRemaining,o),m}if(await kl(o),p.every(m=>m===0))throw new it(1);n&&await bm(e,t,r,s,p),oe.captureMessage("Seedless: successful pin verification","se*dless")},s0=async({authRepository:e},t)=>{let o=await e.getUser();if(!o)throw new Error("User is not logged in");let{accessToken:r}=o,n=yo(Cr()),i=await Nn(n,r),a={encryptedAccounts:i.encryptedAccounts,encryptionKey:i.encryptionKey,decryptionKeySharePhantom:i.decryptionKeyShare1,decryptionKeyShareExternal:t},s=await xl(a);if(s.length===0)throw new Error("no seeds found in seedless bundle");return oe.captureMessage("Seedless: successful recovery with share","se*dless"),{entropy:s[0]}},pm=async({authRepository:e},t="latest")=>{let o=await e.getUser();if(!o)throw new Error("User is not logged in, cannot check for se*dless bundle");let{accessToken:r}=o;return await Op(t,r)},l0=async({authRepository:e})=>{let t=await e.getUser();if(!t)throw new Error("User is not logged in, cannot check for seedless bundle update");let{accessToken:o}=t,r,n=yo(Cr());try{r=await Nn("latest",o)}catch(a){if(a instanceof rn)return!1;throw a}return!!El()[r.id]&&r.id!==n},mm=async({authRepository:e})=>{let t=await e.getUser();if(!t)throw new Error("User is not logged in, cannot delete seedless bundle");let{accessToken:o}=t,r=yo(Cr());return await _p(r,o)},c0=async({storage:e})=>await Il(e),El=()=>fs()?oa:ra,Cr=()=>fs()?wl:Cl,d0=async({storage:e},t=!1)=>{let o=Date.now(),r=await e.get("seedlessPinVerificationLockTimerInMs")??Date.now();return{expired:t?o>=3e5:o>=r+12096e5,timestampInMs:r}},u0=async({storage:e},t)=>{await e.set("seedlessPinVerificationLockTimerInMs",t)},Ll=async e=>(await e.get("seedlessDeviceID"))?.deviceID??null,fm=async(e,t)=>{await e.set("seedlessDeviceID",{version:1,deviceID:t})},gm=()=>{let e=gs(32);try{return Promise.resolve(At.from(e))}finally{e.fill(0)}},ym=async({storage:e})=>{let t=await Ll(e);return t||(t=Tn(),await fm(e,t)),t},p0=async e=>{let{authRepository:t}=e,o=await t.getUser();if(!o)throw new Error("User not logged in, cannot retrieve device encryption key.");return t0(e,async()=>{let r=await ym(e),n=Al[r];if(n)return n;let i=await rl(o.accessToken,r);return i||(i=await gm(),await hm(e,i)),Al[r]=i,i})},hm=async({authRepository:e,storage:t},o)=>{let r=await e.getUser();if(!r)throw new Error("User not logged in, cannot set device encryption key.");let n=await Ll(t);return n||(n=Tn(),await fm(t,n)),await $p(r.accessToken,n,o),Al[n]=o,o},m0=async({authRepository:e,storage:t})=>{let o=await e.getUser();if(!o)throw new Error("User not logged in, cannot delete device encryption key.");let r=await Ll(t);if(!r)return null;let n=await rl(o.accessToken,r);return n?(await zp(o.accessToken,r),n):null},bm=async(e,t,o,r,n)=>{let i=await e.getUser();if(!i)throw new Error("User is not logged in");let{accessToken:a}=i,s=await t(),l=yo(Cr()),u={encryptedAccounts:r.encryptedAccounts,encryptionKey:r.encryptionKey,decryptionKeySharePhantom:r.decryptionKeyShare1,decryptionKeyShareExternal:new At(n)};if(r.id!==l){if(!await s.sendJuiceboxShare(Cr(),n,o,a))throw oe.captureError(new Error("Bundle upgrade: Failed to send share to Juicebox"),"se*dless"),new it(5);if(!await ol(u,l,a))throw oe.captureError(new Error("Bundle upgrade: Failed to send bundle to backend"),"se*dless"),new it(5)}},xm=async({mnemonicProvider:e})=>{na||(na=await e()),On||(On=new At(gs(32)))};c();d();c();d();var Qt=I(D());var f0={small:16,regular:22},g0=x.section`
  z-index: 1;
  background-color: #222222;
  padding: 10px 16px;
  display: flex;
  flex-shrink: 0;
  flex-direction: row;
  align-items: center;
  justify-content: ${e=>e.justifyContent};
  backdrop-filter: blur(10px);
  border-bottom: 1px solid #323232;
  height: ${e=>e.height}px;
  width: 100%;
`;g0.defaultProps={justifyContent:"center",height:Di};var y0=x(W).attrs({size:16,weight:500,lineHeight:25})``;y0.defaultProps={maxWidth:"280px",noWrap:!0};var h0=x.div`
  display: flex;
  flex-shrink: 0;
  justify-content: center;
  align-items: center;
  padding-bottom: 17px;
  position: relative;
  width: 100%;
`,b0=x(Gu)`
  position: absolute;
  right: 0;
`,x0=x($i)`
  position: absolute;
  left: 0;
`,ia=({children:e,shouldWrap:t,leftButton:o,dropdown:r,titleSize:n="regular"})=>{let i=(0,Qt.useMemo)(()=>{if(!o)return Qt.default.createElement("div",null);let s=o.type==="back"?"header--back":"header--close",l=o.type==="back"?Qt.default.createElement(Au,null):Qt.default.createElement(Oi,null);return Qt.default.createElement(x0,{"data-testid":s,onClick:o.onClick},l)},[o]),a=(r?.sections?.length??0)>0||(r?.items?.length??0)>0;return Qt.default.createElement(h0,null,i,Qt.default.createElement(W,{weight:500,size:f0[n],noWrap:!t,maxWidth:"280px"},e),r&&(a||r.onIconClick)?Qt.default.createElement(b0,{sections:r.sections,items:r.items,icon:r.icon||Qt.default.createElement(ku,null),onIconClick:r?.onIconClick}):Qt.default.createElement("div",null))};c();d();var Oe=I(D());c();d();var S=I(D());c();d();var ee=I(D()),Mm=I(Ic());c();d();var an=I(D(),1);c();d();var Cm=typeof self<"u"?an.default.useLayoutEffect:()=>{};function aa(...e){return(...t)=>{for(let o of e)typeof o=="function"&&o(...t)}}var _n=new Map,vm=new Set;function wm(){if(typeof self>"u")return;let e=o=>{let r=_n.get(o.target);r||(r=new Set,_n.set(o.target,r),o.target.addEventListener("transitioncancel",t)),r.add(o.propertyName)},t=o=>{let r=_n.get(o.target);if(r&&(r.delete(o.propertyName),r.size===0&&(o.target.removeEventListener("transitioncancel",t),_n.delete(o.target)),_n.size===0)){for(let n of vm)n();vm.clear()}};document.body.addEventListener("transitionrun",e),document.body.addEventListener("transitionend",t)}typeof document<"u"&&(document.readyState!=="loading"?wm():document.addEventListener("DOMContentLoaded",wm));function Dl(e){for(Sm(e)&&(e=e.parentElement);e&&!Sm(e);)e=e.parentElement;return e||document.scrollingElement||document.documentElement}function Sm(e){let t=self.getComputedStyle(e);return/(auto|scroll)/.test(t.overflow+t.overflowX+t.overflowY)}var zP=typeof self<"u"&&self.visualViewport;function Fl(e){var t;return typeof self<"u"&&self.navigator!=null?e.test(((t=self.navigator.userAgentData)===null||t===void 0?void 0:t.platform)||self.navigator.platform):!1}function v0(){return Fl(/^Mac/i)}function w0(){return Fl(/^iPhone/i)}function S0(){return Fl(/^iPad/i)||v0()&&navigator.maxTouchPoints>1}function Tm(){return w0()||S0()}function no(){return no=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},no.apply(this,arguments)}function sn(e,t){if(e==null)return{};var o={},r=Object.keys(e),n,i;for(i=0;i<r.length;i++)n=r[i],!(t.indexOf(n)>=0)&&(o[n]=e[n]);return o}var Pm="calc(100% - env(safe-area-inset-top) - 34px)",Bm=typeof self>"u",C0={ease:"easeOut",duration:.2},T0={ease:"linear",duration:.01},P0=.6,k0=500;function km(e,t){for(var o=e[0],r=Math.abs(e[0]-t),n=1;n<e.length;n++){var i=Math.abs(e[n]-t);i<r&&(o=e[n],r=i)}return o}function I0(e){var t=document.querySelector("body"),o=document.querySelector("#"+e);if(o){var r=24,n=self.innerHeight,i=(n-r)/n;t.style.backgroundColor="#000",o.style.overflow="hidden",o.style.willChange="transform",o.style.transition="transform 200ms ease-in-out, border-radius 200ms linear",o.style.transform="translateY(calc(env(safe-area-inset-top) + "+r/2+"px)) scale("+i+")",o.style.borderTopRightRadius="10px",o.style.borderTopLeftRadius="10px"}}function Im(e){var t=document.querySelector("body"),o=document.getElementById(e);function r(){o.style.removeProperty("overflow"),o.style.removeProperty("will-change"),o.style.removeProperty("transition"),t.style.removeProperty("background-color"),o.removeEventListener("transitionend",r)}o&&(o.style.removeProperty("border-top-right-radius"),o.style.removeProperty("border-top-left-radius"),o.style.removeProperty("transform"),o.addEventListener("transitionend",r))}function A0(e){for(var t=0;t<e.length;t++)if(e[t+1]>e[t])return!1;return!0}function Am(e){var t=e.snapTo,o=e.sheetHeight;return t<0&&console.warn("Snap point is out of bounds. Sheet height is "+o+" but snap point is "+(o+Math.abs(t))+"."),Math.max(Math.round(t),0)}function E0(e){return function(t){e.forEach(function(o){typeof o=="function"?o(t):o&&(o.current=t)})}}function L0(){return typeof self>"u"?!1:"ontouchstart"in self||navigator.maxTouchPoints>0}var Nl=Bm?ee.useEffect:ee.useLayoutEffect;function D0(e,t){var o=Nm(e);(0,ee.useEffect)(function(){t&&!o&&e?I0(t):t&&!e&&o&&Im(t)},[e,o]),(0,ee.useEffect)(function(){return function(){t&&e&&Im(t)}},[e])}function F0(e,t){var o=Nm(e),r=(0,ee.useRef)(!1),n=(0,ee.useCallback)(function(){r.current?(t.current.onCloseEnd==null||t.current.onCloseEnd(),r.current=!1):(t.current.onOpenEnd==null||t.current.onOpenEnd(),r.current=!0)},[e,o]);return(0,ee.useEffect)(function(){!o&&e?t.current.onOpenStart==null||t.current.onOpenStart():!e&&o&&(t.current.onCloseStart==null||t.current.onCloseStart())},[e,o]),{handleAnimationComplete:n}}function M0(){var e=(0,ee.useState)(0),t=e[0],o=e[1];return Nl(function(){var r=function(){return o(self.innerHeight)};return self.addEventListener("resize",r),r(),function(){return self.removeEventListener("resize",r)}},[]),t}function Nm(e){var t=(0,ee.useRef)();return(0,ee.useEffect)(function(){t.current=e}),t.current}function Em(e){var t=(0,ee.useRef)();return Nl(function(){t.current=e}),(0,ee.useCallback)(function(){for(var o=t.current,r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];return o?.apply(void 0,n)},[])}var Om=(0,ee.createContext)(void 0),la=function(){var t=(0,ee.useContext)(Om);if(!t)throw Error("Sheet context error");return t},_m=(0,ee.createContext)(void 0);function B0(e){var t=e.children,o=la(),r=(0,ee.useState)(!!o.disableDrag),n=r[0],i=r[1];function a(){o.disableDrag||i(!1)}function s(){n||i(!0)}return(0,ee.createElement)(_m.Provider,{value:{disableDrag:n,setDragEnabled:a,setDragDisabled:s}},t)}var $m=function(){var t=(0,ee.useContext)(_m);if(!t)throw Error("Sheet scroller context error");return t},Ml=typeof self<"u"&&self.visualViewport,N0=new Set(["checkbox","radio","range","color","file","image","button","submit","reset"]),sa=0,Bl;function O0(e){e===void 0&&(e={});var t=e,o=t.isDisabled;Cm(function(){if(!o)return sa++,sa===1&&(Tm()?Bl=$0():Bl=_0()),function(){sa--,sa===0&&Bl()}},[o])}function _0(){return aa(zn(document.documentElement,"paddingRight",self.innerWidth-document.documentElement.clientWidth+"px"),zn(document.documentElement,"overflow","hidden"))}function $0(){var e,t=0,o=function(m){e=Dl(m.target),!(e===document.documentElement&&e===document.body)&&(t=m.changedTouches[0].pageY)},r=function(m){if(e===document.documentElement||e===document.body){m.preventDefault();return}var g=m.changedTouches[0].pageY,y=e.scrollTop,h=e.scrollHeight-e.clientHeight;h!==0&&((y<=0&&g>t||y>=h&&g<t)&&m.preventDefault(),t=g)},n=function(m){var g=m.target;Dm(g)&&g!==document.activeElement&&(m.preventDefault(),g.style.transform="translateY(-2000px)",g.focus(),requestAnimationFrame(function(){g.style.transform=""}))},i=function(m){var g=m.target;Dm(g)&&(g.style.transform="translateY(-2000px)",requestAnimationFrame(function(){g.style.transform="",Ml&&(Ml.height<self.innerHeight?requestAnimationFrame(function(){Lm(g)}):Ml.addEventListener("resize",function(){return Lm(g)},{once:!0}))}))},a=function(){self.scrollTo(0,0)},s=self.pageXOffset,l=self.pageYOffset,u=aa(zn(document.documentElement,"paddingRight",self.innerWidth-document.documentElement.clientWidth+"px"),zn(document.documentElement,"overflow","hidden"),zn(document.body,"marginTop","-"+l+"px"));self.scrollTo(0,0);var p=aa($n(document,"touchstart",o,{passive:!1,capture:!0}),$n(document,"touchmove",r,{passive:!1,capture:!0}),$n(document,"touchend",n,{passive:!1,capture:!0}),$n(document,"focus",i,!0),$n(self,"scroll",a));return function(){u(),p(),self.scrollTo(s,l)}}function zn(e,t,o){var r=e.style[t];return e.style[t]=o,function(){e.style[t]=r}}function $n(e,t,o,r){return e.addEventListener(t,o,r),function(){e.removeEventListener(t,o,r)}}function Lm(e){for(var t=document.scrollingElement||document.documentElement;e&&e!==t;){var o=Dl(e);if(o!==document.documentElement&&o!==document.body&&o!==e){var r=o.getBoundingClientRect().top,n=e.getBoundingClientRect().top;n>r+e.clientHeight&&(o.scrollTop+=n-r)}e=o.parentElement}}function Dm(e){return e instanceof HTMLInputElement&&!N0.has(e.type)||e instanceof HTMLTextAreaElement||e instanceof HTMLElement&&e.isContentEditable}var No={wrapper:{position:"fixed",top:0,bottom:0,left:0,right:0,overflow:"hidden",pointerEvents:"none"},backdrop:{zIndex:1,position:"fixed",top:0,left:0,width:"100%",height:"100%",backgroundColor:"rgba(0, 0, 0, 0.2)",touchAction:"none",border:"none"},container:{zIndex:2,position:"absolute",left:0,bottom:0,width:"100%",backgroundColor:"#fff",borderTopRightRadius:"8px",borderTopLeftRadius:"8px",boxShadow:"0px -2px 16px rgba(0, 0, 0, 0.3)",display:"flex",flexDirection:"column",pointerEvents:"auto"},headerWrapper:{width:"100%"},header:{height:"40px",width:"100%",position:"relative",display:"flex",alignItems:"center",justifyContent:"center"},indicator:{width:"18px",height:"4px",borderRadius:"99px",backgroundColor:"#ddd"},content:{flexGrow:1,display:"flex",flexDirection:"column",minHeight:"0px",position:"relative"},scroller:{height:"100%",overflowY:"auto"}},z0=(0,ee.forwardRef)(function(e,t){var o=e.onOpenStart,r=e.onOpenEnd,n=e.onClose,i=e.onCloseStart,a=e.onCloseEnd,s=e.onSnap,l=e.children,u=e.isOpen,p=e.snapPoints,f=e.rootId,m=e.mountPoint,g=e.style,y=e.detent,h=y===void 0?"full-height":y,w=e.initialSnap,k=w===void 0?0:w,C=e.disableDrag,v=C===void 0?!1:C,F=e.prefersReducedMotion,z=F===void 0?!1:F,T=e.tweenConfig,O=T===void 0?C0:T,U=sn(e,["onOpenStart","onOpenEnd","onClose","onCloseStart","onCloseEnd","onSnap","children","isOpen","snapPoints","rootId","mountPoint","style","detent","initialSnap","disableDrag","prefersReducedMotion","tweenConfig"]),M=(0,ee.useRef)(null),N=En(0),L=M0(),V=Fu(),_=!!(z||V),q=no({type:"tween"},_?T0:O),Y=En(0),te=Ln(Y,function(he){return he>=L?-1:9999999}),Z=Ln(Y,function(he){return he>=L?"hidden":"visible"}),ne=(0,ee.useRef)({onOpenStart:o,onOpenEnd:r,onCloseStart:i,onCloseEnd:a});Nl(function(){ne.current={onOpenStart:o,onOpenEnd:r,onCloseStart:i,onCloseEnd:a}}),p&&(p=p.map(function(he){return he>0&&he<=1?Math.round(he*L):he<0?L+he:he}),console.assert(A0(p)||L===0,"Snap points need to be in descending order got: ["+p+"]"));var B=Em(function(he,Le){var _e=Le.delta,be=Y.getVelocity();be>0&&N.set(10),be<0&&N.set(-10),Y.set(Math.max(Y.get()+_e.y,0))}),ue=Em(function(he,Le){var _e=Le.velocity;if(_e.y>k0)n();else{var be=M.current,Fe=be.getBoundingClientRect().height,$e=Y.get(),et=0;if(p){var tt=p.map(function(_r){return Fe-Math.min(_r,Fe)});h==="content-height"&&!tt.includes(0)&&tt.unshift(0),et=km(tt,$e)}else $e/Fe>P0&&(et=Fe);if(et=Am({snapTo:et,sheetHeight:Fe}),Fs(Y,et,q),p&&s){var Ke=Math.abs(Math.round(p[0]-et)),ao=p.indexOf(km(p,Ke));s(ao)}var fr=Math.round(Fe),gr=et>=fr;gr&&n()}N.set(0)});(0,ee.useEffect)(function(){if(!(!p||!s)){var he=u?k:p.length-1;s(he)}},[u]),(0,ee.useImperativeHandle)(t,function(){return{y:Y,snapTo:function(Le){var _e=M.current;if(p&&p[Le]!==void 0&&_e!==null){var be=_e.getBoundingClientRect().height,Fe=p[Le],$e=Am({snapTo:be-Fe,sheetHeight:be});Fs(Y,$e,q),s&&s(Le),$e>=be&&n()}}}}),D0(u,f),O0({isDisabled:!u});var Pe=(0,ee.useMemo)(function(){var he={drag:"y",dragElastic:0,dragConstraints:{top:0,bottom:0},dragMomentum:!1,dragPropagation:!1,onDrag:B,onDragEnd:ue};return v?void 0:he},[v]),ye={y:Y,sheetRef:M,isOpen:u,initialSnap:k,snapPoints:p,detent:h,indicatorRotation:N,callbacks:ne,dragProps:Pe,windowHeight:L,animationOptions:q,reduceMotion:_,disableDrag:v},xe=(0,ee.createElement)(Om.Provider,{value:ye},(0,ee.createElement)(Xe.div,Object.assign({},U,{ref:t,style:no({},No.wrapper,{zIndex:te,visibility:Z},g)}),(0,ee.createElement)(Fo,null,u?(0,ee.createElement)(B0,null,ee.Children.map(l,function(he,Le){return(0,ee.cloneElement)(he,{key:"sheet-child-"+Le})})):null)));return Bm?xe:(0,Mm.createPortal)(xe,m??document.body)}),U0=(0,ee.forwardRef)(function(e,t){var o=e.children,r=e.style,n=r===void 0?{}:r,i=e.className,a=i===void 0?"":i,s=sn(e,["children","style","className"]),l=la(),u=l.y,p=l.isOpen,f=l.callbacks,m=l.snapPoints,g=l.initialSnap,y=g===void 0?0:g,h=l.sheetRef,w=l.windowHeight,k=l.detent,C=l.animationOptions,v=l.reduceMotion,F=F0(p,f),z=F.handleAnimationComplete,T=m?m[0]-m[y]:0,O=m?m[0]:null,U=O!==null?"min("+O+"px, "+Pm+")":Pm;return(0,ee.createElement)(Xe.div,Object.assign({},s,{ref:E0([h,t]),className:"react-modal-sheet-container "+a,style:no({},No.container,n,k==="full-height"&&{height:U},k==="content-height"&&{maxHeight:U},{y:u}),initial:v?!1:{y:w},animate:{y:T,transition:C},exit:{y:w,transition:C},onAnimationComplete:z}),o)}),V0=(0,ee.forwardRef)(function(e,t){var o=e.children,r=e.style,n=e.disableDrag,i=e.className,a=i===void 0?"":i,s=sn(e,["children","style","disableDrag","className"]),l=la(),u=$m(),p=n||u.disableDrag?void 0:l.dragProps;return(0,ee.createElement)(Xe.div,Object.assign({},s,{ref:t,className:"react-modal-sheet-content "+a,style:no({},No.content,r)},p),o)}),H0=(0,ee.forwardRef)(function(e,t){var o=e.children,r=e.style,n=e.disableDrag,i=sn(e,["children","style","disableDrag"]),a=la(),s=a.indicatorRotation,l=a.dragProps,u=n?void 0:l,p=Ln(s,function(m){return"translateX(2px) rotate("+m+"deg)"}),f=Ln(s,function(m){return"translateX(-2px) rotate("+-1*m+"deg)"});return(0,ee.createElement)(Xe.div,Object.assign({},i,{ref:t,style:no({},No.headerWrapper,r)},u),o||(0,ee.createElement)("div",{className:"react-modal-sheet-header",style:No.header},(0,ee.createElement)(Xe.span,{className:"react-modal-sheet-drag-indicator",style:no({},No.indicator,{transform:p})}),(0,ee.createElement)(Xe.span,{className:"react-modal-sheet-drag-indicator",style:no({},No.indicator,{transform:f})})))}),Fm=function(t){return!!t.onClick||!!t.onTap},W0=(0,ee.forwardRef)(function(e,t){var o=e.style,r=o===void 0?{}:o,n=e.className,i=n===void 0?"":n,a=sn(e,["style","className"]),s=Fm(a)?Xe.button:Xe.div,l=Fm(a)?"auto":"none";return(0,ee.createElement)(s,Object.assign({},a,{ref:t,className:"react-modal-sheet-backdrop "+i,style:no({},No.backdrop,r,{pointerEvents:l}),initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}}))}),K0=(0,ee.forwardRef)(function(e,t){var o=e.draggableAt,r=o===void 0?"top":o,n=e.children,i=e.style,a=e.className,s=a===void 0?"":a,l=sn(e,["draggableAt","children","style","className"]),u=$m();function p(y){var h=y.scrollTop,w=y.scrollHeight,k=y.clientHeight,C=w>k;if(C){var v=h<=0,F=w-h===k,z=r==="top"&&v||r==="bottom"&&F||r==="both"&&(v||F);z?u.setDragEnabled():u.setDragDisabled()}}function f(y){p(y.currentTarget)}function m(y){p(y.currentTarget)}var g=L0()?{onScroll:f,onTouchStart:m}:void 0;return(0,ee.createElement)("div",Object.assign({},l,{ref:t,className:"react-modal-sheet-scroller "+s,style:no({},No.scroller,i)},g),n)}),ln=z0;ln.Container=U0;ln.Header=H0;ln.Content=V0;ln.Backdrop=W0;ln.Scroller=K0;var Tr=ln;c();d();var Tt=I(D());var zm=({id:e,prompt:t,options:o,context:r,onClose:n})=>{let[i,a]=(0,Tt.useState)(""),s=(0,Tt.useCallback)(()=>{we.capture("issueReportedByUser",{data:{selected:i,key:e,context:r,options:o.map(u=>u.key)}}),n()},[i,e,o,r,n]),l=o.map(({key:u})=>({type:"check",id:u,topLeft:se.t(u),onClick:()=>a(u),active:i===u}));return Tt.default.createElement("div",null,Tt.default.createElement("div",{className:ca.wrapper},Tt.default.createElement(ia,{leftButton:{type:"close",onClick:n}},se.t("reportIssueScreenTitle")),Tt.default.createElement("div",{className:ca.table},Tt.default.createElement("div",{className:ca.prompt},Tt.default.createElement(H,{children:t,font:"title1"})),Tt.default.createElement(pt,{rows:l}))),Tt.default.createElement(eo,null,Tt.default.createElement("div",{className:ca.button},Tt.default.createElement(Mu,{buttons:[{text:se.t("commandSend"),onClick:s,disabled:i==="",theme:"primary"}]}))))},ca={wrapper:j({padding:16,backgroundColor:"bgWallet",height:"100%"}),prompt:j({paddingY:16}),table:j({height:"100%",borderRadius:"table",overflowY:"scroll"}),button:j({paddingX:16,paddingBottom:8})};c();d();var io=I(D()),Um=I(Ic());var q0=x(Xe.div)`
  width: 100%;
  height: ${e=>`calc(100% - ${e.headerHeight}px)`};
  margin: 0;
  padding: ${e=>(e.headerHeight??0)>0?"16px":"0"};
  background: #222;
  top: ${e=>e.headerHeight}px;
  position: absolute;
  z-index: ${e=>e.zIndex};
`,R0=x(Xe.div)`
  width: 100%;
  height: ${e=>`calc(100% - ${e.headerHeight}px)`};
  margin: 0;
  background: #000000;
  top: ${e=>e.headerHeight}px;
  position: absolute;
  z-index: ${e=>e.zIndex};
`;function j0(e){return e==="slideUpFromBottom"?{initial:{y:self.innerHeight},animate:{y:0},exit:{y:self.innerHeight},transition:{ease:"easeOut",duration:.3}}:{initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{ease:"easeOut",duration:.1}}}var G0=({children:e,isFullScreen:t,isOpen:o,animateOnFirstRender:r,presentationStyle:n="slideUpFromBottom",headerHeight:i=Di})=>{let a=j0(n);return io.default.createElement(Fo,{initial:r},o&&io.default.createElement(io.default.Fragment,null,io.default.createElement(R0,{zIndex:t?3:0,headerHeight:i,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{ease:"linear",duration:.3}}),io.default.createElement(q0,{zIndex:t?uu:0,headerHeight:i,...a},e)))},Vm=({modalId:e=du,...t})=>{let[o,r]=(0,io.useState)();return(0,io.useEffect)(()=>{let n;return e&&(n=setTimeout(()=>{let i=document.getElementById(e);if(!i)throw new Error(`Could not find node with id ${e}`);r(i)},200)),()=>{n&&clearTimeout(n)}},[e]),o?(0,Um.createPortal)(io.default.createElement(G0,{...t}),o):null};c();d();var Hm=x(Tr)`
  .react-modal-sheet-backdrop {
    /* custom styles */
  }
  .react-modal-sheet-container {
    background-color: rgb(34, 34, 34) !important;
  }
  .react-modal-sheet-header {
    /* custom styles */
  }
  .react-modal-sheet-drag-indicator {
    /* custom styles */
  }
  .react-modal-sheet-content {
    margin: 0 15px 15px;
  }
`,Wm=x.div`
  height: 15px;
`;c();d();var Jt=I(D());c();d();var re=I(D());c();d();var ir=I(D());var Q0=x.div`
  overflow-y: auto;
`,J0=x.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 5px;
  visibility: ${e=>e.isLoadingNext?"visible":"hidden"};
`,Km=({children:e,loadNext:t,hasNext:o,isLoadingNext:r,style:n,loadingElement:i=ir.default.createElement(Vi,null)})=>{let a=(0,ir.useRef)(null),s=(0,ir.useCallback)(()=>{if(a.current){let{scrollTop:l,scrollHeight:u,clientHeight:p}=a.current;u-(l+p)<=1&&o&&!r&&t()}},[t,o,r]);return ir.default.createElement(Q0,{style:n,onScroll:s,ref:a},e,ir.default.createElement(J0,{isLoadingNext:r},i))};c();d();var da=I(D());var Y0=x.div`
  padding: 16px 20px;
  background: ${e=>dt(e.color,.05)};
  border: 1px solid ${e=>dt(e.color,.08)};
  width: 100%;
  border-radius: 8px;
  display: flex;
  flex-direction: column;
  gap: 5px;
`,qm=24,X0=x(W).attrs({size:16,lineHeight:qm,weight:600})`
  color: ${e=>e.color};
  max-height: ${e=>e.maxLines?`${e.maxLines*qm}px`:"none"};
  overflow: ${e=>e.maxLines?"hidden":"visible"};
`,Z0=x(W).attrs({size:14,lineHeight:21})`
  color: ${e=>e.color};
`,ua=({title:e,description:t,color:o,titleMaxLines:r})=>da.default.createElement(Y0,{color:o},!!e&&da.default.createElement(X0,{color:o,maxLines:r},e),!!t&&da.default.createElement(Z0,{color:o},t));c();d();var ha=I(D());c();d();c();d();c();d();var ar=I(D());c();d();var le=I(D());c();d();var bo={small:44,medium:72,large:128};c();d();var Un=I(D());var cn=(e,t)=>{let o=Math.max(t,bo.large);return e.endsWith(".ico")?e:au(e,o,o)},pa=x.svg`
  display: inline-flex;
  width: ${({responsive:e,size:t})=>e?"100%":t};

  g {
    transform-origin: center;
  }

  g,
  rect,
  circle,
  use,
  symbol {
    transition-property: fill, stroke, opacity, filter;
    transition-timing-function: cubic-bezier(0.16, 1, 0.3, 1); // ease-out-expo https://easings.net/#easeOutExpo
    transition-duration: 500ms;
  }

  @keyframes opacity {
    0% {
      opacity: 1;
    }
    100% {
      opacity: 0.5;
    }
  }

  .background.loading {
    animation: opacity 1s ease-in-out alternate infinite;
    fill: rgba(255, 255, 255, 0.1);
  }

  img {
    transition:
      filter 200ms ease-out,
      opacity 100ms ease-out;
  }

  img.loading {
    opacity: 0;
    filter: blur(6px);
  }
`,ma=(e,t)=>`
@keyframes rotation-${e} {
  0% {
    stroke-dashoffset: ${t(0)};
  }
  100% {
    stroke-dashoffset: -${t(100)};
  }
}

@keyframes size-${e} {
  0%,
  100% {
    stroke-dasharray: ${t(10)}, ${t(90)};
  }
  33% {
    stroke-dasharray: ${t(75)}, ${t(25)};
  }
}

.${e} {
  animation: rotation-${e} 1s linear infinite, size-${e} 3s ease-in-out infinite;
}
`,eh=x.span`
  box-sizing: border-box;
  color: ${({color:e})=>e};
  display: inline-block;
  font-size: ${({size:e})=>Math.round(e/4)}px;
  font-weight: 600;
  letter-spacing: -0.05em;
  line-height: ${({size:e})=>e}px;
  overflow: hidden;
  padding: 0 ${({size:e})=>Math.round(e*.1)}px;
  text-align: center;
  text-overflow: ellipsis;
  user-select: none;
  white-space: nowrap;
  width: 100%;
`,Rm=(0,Un.memo)(({content:e,size:t,color:o})=>Un.default.createElement("foreignObject",{width:"100%",height:"100%"},Un.default.createElement(eh,{size:t,color:o},e.replaceAll(" ","").slice(0,5).trim())));c();d();var Pr=I(D());var Ol=(e,t,o)=>{let[r,n]=(0,Pr.useState)(void 0),[i,a]=(0,Pr.useState)(cn(e,t)),[s,l]=(0,Pr.useState)(e);e!==s&&(l(e),n(void 0),a(cn(e,t)));let u=(0,Pr.useCallback)(()=>{n("fail"),o&&o()},[o]),p=(0,Pr.useCallback)(()=>n("success"),[]);return{src:i,status:r,handleError:u,handleLoad:p}};c();d();var jm={18:"M0 9C0 6.20435 0 4.80653 0.456723 3.7039C1.06569 2.23373 2.23373 1.06569 3.7039 0.456723C4.80653 0 6.20435 0 9 0C11.7956 0 13.1935 0 14.2961 0.456723C15.7663 1.06569 16.9343 2.23373 17.5433 3.7039C18 4.80653 18 6.20435 18 9C18 11.7956 18 13.1935 17.5433 14.2961C16.9343 15.7663 15.7663 16.9343 14.2961 17.5433C13.1935 18 11.7956 18 9 18C6.20435 18 4.80653 18 3.7039 17.5433C2.23373 16.9343 1.06569 15.7663 0.456723 14.2961C0 13.1935 0 11.7956 0 9Z",24:"M0 12C0 8.27247 0 6.4087 0.608964 4.93853C1.42092 2.97831 2.97831 1.42092 4.93853 0.608964C6.4087 0 8.27247 0 12 0C15.7275 0 17.5913 0 19.0615 0.608964C21.0217 1.42092 22.5791 2.97831 23.391 4.93853C24 6.4087 24 8.27247 24 12C24 15.7275 24 17.5913 23.391 19.0615C22.5791 21.0217 21.0217 22.5791 19.0615 23.391C17.5913 24 15.7275 24 12 24C8.27247 24 6.4087 24 4.93853 23.391C2.97831 22.5791 1.42092 21.0217 0.608964 19.0615C0 17.5913 0 15.7275 0 12Z",40:"M0 19.2C0 12.4794 0 9.11906 1.30792 6.55211C2.4584 4.29417 4.29417 2.4584 6.55211 1.30792C9.11906 0 12.4794 0 19.2 0H20.8C27.5206 0 30.8809 0 33.4479 1.30792C35.7058 2.4584 37.5416 4.29417 38.6921 6.55211C40 9.11906 40 12.4794 40 19.2V20.8C40 27.5206 40 30.8809 38.6921 33.4479C37.5416 35.7058 35.7058 37.5416 33.4479 38.6921C30.8809 40 27.5206 40 20.8 40H19.2C12.4794 40 9.11906 40 6.55211 38.6921C4.29417 37.5416 2.4584 35.7058 1.30792 33.4479C0 30.8809 0 27.5206 0 20.8V19.2Z",48:"M0 24C0 16.5449 0 12.8174 1.21793 9.87706C2.84183 5.95662 5.95662 2.84183 9.87706 1.21793C12.8174 0 16.5449 0 24 0C31.4551 0 35.1826 0 38.1229 1.21793C42.0434 2.84183 45.1582 5.95662 46.7821 9.87706C48 12.8174 48 16.5449 48 24C48 31.4551 48 35.1826 46.7821 38.1229C45.1582 42.0434 42.0434 45.1582 38.1229 46.7821C35.1826 48 31.4551 48 24 48C16.5449 48 12.8174 48 9.87706 46.7821C5.95662 45.1582 2.84183 42.0434 1.21793 38.1229C0 35.1826 0 31.4551 0 24Z",64:"M0 32C0 22.6872 0 18.0308 1.38711 14.3118C3.61706 8.33304 8.33304 3.61706 14.3118 1.38711C18.0308 0 22.6872 0 32 0C41.3128 0 45.9692 0 49.6882 1.38711C55.667 3.61706 60.3829 8.33304 62.6129 14.3118C64 18.0308 64 22.6872 64 32C64 41.3128 64 45.9692 62.6129 49.6882C60.3829 55.667 55.667 60.3829 49.6882 62.6129C45.9692 64 41.3128 64 32 64C22.6872 64 18.0308 64 14.3118 62.6129C8.33304 60.3829 3.61706 55.667 1.38711 49.6882C0 45.9692 0 41.3128 0 32Z"},th="rgba(255, 255, 255, 0.04)",oh="rgba(255, 255, 255, 1)",rh="rgba(255, 255, 255, 0.85)",nh="rgba(0,0,0,0.8)",fa=e=>{let t=Tn(),o=`img-m-${t}`,r=`img-s-${t}`,n=`bdg-s-${t}`,i=`bdg-m-${t}`,a=`spnr-r-${t}`,s=Math.max(8,Math.round(e*.2)),l=1+s/10,u=l+s/20,p=e/3*2,f=Math.round(s*.75*2),m=e/2,g=(e-p)/2,y=m,h=s*2,w=e-h+u,k=e-s+u,C=w+(h-f)/2,v=w-l,F=2*l+h,z=l+h,T=M=>{let N=parseInt(Object.keys(jm).reduce((L,V)=>Math.abs(parseInt(V)-M)<Math.abs(parseInt(L)-M)?V:L));return{viewBox:`0 0 ${N} ${N}`,path:jm[N]}},O=T(e),U=T(h);return{badgeBorder:l,badgeBoxXY:w,badgeCXY:k,badgeIconOffset:C,badgeIconSize:f,badgeMaskBoxXY:v,badgeMaskId:i,badgeMaskSize:F,badgeOvershoot:u,badgeRadius:s,badgeRect:U,badgeShapeId:n,badgeSize:h,badgeSpinnerSize:z,fallbackBadgeBgColor:rh,fallbackBadgeFgColor:nh,fallbackBgColor:th,fallbackFgColor:oh,imageCXY:m,imageIconOffset:g,imageIconSize:p,imageMaskId:o,imageRadius:y,imageRect:O,imageShapeId:r,spinnerId:a}};var Gm=(0,le.memo)(({size:e=bo.small,image:t,badge:o,responsive:r=!1,loading:n=!1,dimmed:i=!1,onError:a,className:s,onClick:l})=>{let{badgeBorder:u,badgeBoxXY:p,badgeIconOffset:f,badgeIconSize:m,badgeMaskBoxXY:g,badgeMaskId:y,badgeMaskSize:h,badgeRadius:w,badgeRect:k,badgeShapeId:C,badgeSize:v,badgeSpinnerSize:F,fallbackBadgeBgColor:z,fallbackBadgeFgColor:T,fallbackBgColor:O,fallbackFgColor:U,imageCXY:M,imageIconOffset:N,imageIconSize:L,imageMaskId:V,imageRadius:_,imageRect:q,imageShapeId:Y,spinnerId:te}=fa(e),Z=Ol(typeof t.src=="string"?t.src:"",e,a),ne=Ol(typeof o?.src=="string"?o.src:"",v),B=xe=>{if(o?.shape==="square")return v*(4-e/60)*(xe/100);{let he=o?w:L/2;return 2*Math.PI*he*(xe/100)}},ue=(0,le.useCallback)(xe=>{xe.preventDefault()},[]),Pe=Ie.Image,ye=(0,le.useCallback)(xe=>{l&&l(xe)},[l]);return le.default.createElement(pa,{className:s,fill:"none",overflow:"visible",responsive:r,size:e,viewBox:`0 0 ${e} ${e}`,width:e,xmlns:"http://www.w3.org/2000/svg",onClick:ye,style:{cursor:l?"pointer":"default"}},le.default.createElement("defs",null,le.default.createElement("style",null,ma(te,B)),n&&le.default.createElement("symbol",{id:te,viewBox:`0 0 ${e} ${e}`,overflow:"visible"},!o&&le.default.createElement(le.default.Fragment,null,le.default.createElement("circle",{cx:M,cy:M,fill:"none",r:L/2,stroke:typeof t.src=="function"?t?.fgColor:U,strokeLinecap:"round",strokeOpacity:"0.25",strokeWidth:u}),le.default.createElement("circle",{className:te,cx:M,cy:M,fill:"none",r:L/2,stroke:typeof t.src=="function"?t?.fgColor:U,strokeLinecap:"round",strokeWidth:u})),o&&le.default.createElement("use",{className:te,fill:"none",height:F,href:`#${C}`,strokeWidth:u,stroke:o&&"bgColor"in o&&o.bgColor||t.fgColor||U,strokeLinecap:"round",width:F,x:g+u/2,y:g+u/2})),t.shape==="circle"&&le.default.createElement("symbol",{id:Y,viewBox:`0 0 ${e} ${e}`,overflow:"visible"},le.default.createElement("circle",{cx:_,cy:_,r:_})),t.shape==="square"&&le.default.createElement("symbol",{id:Y,viewBox:q.viewBox,overflow:"visible"},le.default.createElement("path",{d:q.path})),o&&ne.status!=="fail"&&le.default.createElement(le.default.Fragment,null,o.shape==="circle"&&le.default.createElement("symbol",{id:C,viewBox:`0 0 ${v} ${v}`,overflow:"visible"},le.default.createElement("circle",{cx:w,cy:w,r:w})),o.shape==="square"&&le.default.createElement("symbol",{id:C,viewBox:k.viewBox,overflow:"visible"},le.default.createElement("path",{d:k.path}))),le.default.createElement("mask",{id:V,x:"0",y:"0",width:e,height:e},le.default.createElement("use",{href:`#${Y}`,width:e,height:e,fill:"white"}),o&&ne.status!=="fail"&&le.default.createElement("use",{href:`#${C}`,x:g,y:g,width:h,height:h,fill:"black"})),typeof o?.src=="string"&&le.default.createElement("mask",{id:y},le.default.createElement("use",{href:`#${C}`,x:p,y:p,width:v,height:v,fill:"white"}))),le.default.createElement("g",{mask:`url(#${V})`,opacity:n||i?.5:1,style:{filter:i?"grayscale(100%)":n?"grayscale(25%)":""}},!("fullSize"in t&&t.fullSize)&&le.default.createElement("use",{href:`#${Y}`,width:e,height:e,fill:t.bgColor||O,className:`${Z.status} background`}),(Z.status==="fail"||t.src===null||t.src===void 0)&&("fallback"in t&&t.fallback?typeof t.fallback=="string"?le.default.createElement(Rm,{content:t.fallback,size:e,color:t.fgColor||U}):le.default.createElement("g",{transform:`scale(${n?.66:.75}) translate(${N},${N})`,opacity:"0.25"},t.fallback({width:L,fill:t.fgColor||U})):le.default.createElement("g",{color:"black",width:56},le.default.createElement("use",{href:`#${Y}`,width:e,height:e,fill:"black"}),le.default.createElement(Pe,{size:"50%",color:"gray",x:"25%",y:"25%"}))),typeof t.src=="string"&&Z.status!=="fail"&&le.default.createElement("foreignObject",{width:e,height:e},le.default.createElement("img",{className:Z.status,crossOrigin:"anonymous",height:e,onError:Z.handleError,onLoad:Z.handleLoad,src:Z.src,width:e,onDragStart:ue})),typeof t.src=="function"&&("fullSize"in t&&t.fullSize?t.src({width:e,height:e}):le.default.createElement("g",{transform:`${n&&!o?"scale(0.66) ":""}translate(${N},${N})`},t.src({width:L,height:L,fill:"fgColor"in t&&t.fgColor||U})))),o&&ne.status!=="fail"&&le.default.createElement("g",null,le.default.createElement("use",{href:`#${C}`,opacity:n?.2:1,fill:n?"transparent":typeof o.src=="string"&&ne.status==="success"?O:o.bgColor||z,x:p,y:p,width:v,height:v,className:`${ne.status} background`}),typeof o.src=="string"&&le.default.createElement("g",{mask:`url(#${y})`},le.default.createElement("foreignObject",{x:p,y:p,width:v,height:v},le.default.createElement("img",{className:ne.status,crossOrigin:"anonymous",height:v,onError:ne.handleError,onLoad:ne.handleLoad,src:ne.src,width:v}))),typeof o.src=="function"&&le.default.createElement("g",{transform:`translate(${f},${f})`},o.src({width:m,height:m,fill:n?"bgColor"in o&&o?.bgColor||z:"fgColor"in o&&o.fgColor||T}))),n&&le.default.createElement("use",{href:`#${te}`,x:"0",y:"0",width:e,height:e}))});c();d();var Ae=I(D());var Qm=(0,Ae.memo)(({size:e=bo.small,bgToken:t,fgToken:o,badge:r,responsive:n=!1,loading:i=!1,onError:a,className:s,onClick:l})=>{let{badgeBorder:u,fallbackBgColor:p,fallbackFgColor:f,imageMaskId:m,badgeMaskId:g,spinnerId:y}=fa(e),[h,w]=(0,Ae.useState)(void 0),[k,C]=(0,Ae.useState)(void 0),v=(0,Ae.useCallback)(V=>{h===void 0&&w(V?.complete?"success":"loading")},[h]),F=(0,Ae.useCallback)(V=>{k===void 0&&C(V?.complete?"success":"loading")},[k]);if(h==="fail"||k==="fail")return Ae.default.createElement(gt,{className:s,image:{type:"icon",preset:"swap"},badge:r,loading:i,onError:a,responsive:n,size:e});let z=Math.round(e/3.25),T=Math.round(e/2.75),O=2*z,U=2*T,M=e-U,N=e-T,L=V=>{let _=T+u/2;return 2*Math.PI*_*(V/100)};return Ae.default.createElement(pa,{className:s,fill:"none",overflow:"visible",responsive:n,size:e,viewBox:`0 0 ${e} ${e}`,width:e,xmlns:"http://www.w3.org/2000/svg",onClick:l,style:{cursor:l?"pointer":"default"}},Ae.default.createElement("defs",null,Ae.default.createElement("style",null,ma(y,L)),i&&Ae.default.createElement("symbol",{id:y,viewBox:`0 0 ${e} ${e}`,overflow:"visible"},Ae.default.createElement("circle",{className:y,cx:N,cy:N,fill:"none",r:T+u/2,stroke:f,strokeLinecap:"round",strokeWidth:u})),Ae.default.createElement("mask",{id:m,x:"0",y:"0",width:"100%",height:"100%"},Ae.default.createElement("circle",{cx:z,cy:z,r:z,fill:"white"}),Ae.default.createElement("circle",{cx:N,cy:N,r:T+u,fill:"black"})),Ae.default.createElement("mask",{id:g,x:"0",y:"0",width:"100%",height:"100%"},Ae.default.createElement("circle",{cx:N,cy:N,r:T,fill:"white"}))),Ae.default.createElement("g",{opacity:i?.5:1,style:{filter:i?"grayscale(25%)":""}},Ae.default.createElement("g",{mask:`url(#${m})`},Ae.default.createElement("circle",{r:z,cx:z,cy:z,fill:t.bgColor||p,className:`${h} background`}),typeof t.src=="string"?Ae.default.createElement("foreignObject",{width:O,height:O},Ae.default.createElement("img",{width:O,height:O,src:cn(t.src,O),ref:v,onError:()=>{w("fail"),a&&a()},onLoad:()=>w("success"),className:h})):Ae.default.createElement("g",{transform:`translate(${Math.floor(O/6)}, ${Math.floor(O/6)})`},t.src({width:Math.ceil(O/3)*2,fill:"fgColor"in t&&t.fgColor||f}))),Ae.default.createElement("g",{mask:`url(#${g})`},Ae.default.createElement("g",{transform:`translate(${M}, ${M})`},Ae.default.createElement("circle",{r:T,cx:T,cy:T,fill:o.bgColor||p,style:{filter:"drop-shadow(-2px -2px 5px rgba(0, 0, 0, 0.33))"},className:`${k} background`}),typeof o.src=="string"?Ae.default.createElement("foreignObject",{width:U,height:U},Ae.default.createElement("img",{src:cn(o.src,U),width:U,height:U,ref:F,onError:()=>{C("fail"),a&&a()},onLoad:()=>C("success"),className:k})):Ae.default.createElement("g",{transform:`translate(${Math.floor(U/6)}, ${Math.floor(U/6)})`},o.src({width:Math.ceil(U/3)*2,fill:"fgColor"in o&&o.fgColor||f}))))),i&&Ae.default.createElement("use",{href:`#${y}`,x:"0",y:"0",width:e,height:e}))});c();d();c();d();var J=I(D());var ih=x.div`
  position: relative;
  height: ${e=>e.diameter};
  width: ${e=>e.diameter};
  background-color: ${e=>e.color};
  border-radius: ${e=>e.shape==="square"?20:50}%;
  border: ${e=>e.border};
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  opacity: ${e=>e.opacity};
  mask: ${e=>e.mask};
`;ih.defaultProps={color:"blue",shape:"circle",diameter:"100%",border:"none"};var Jm=({width:e=94})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 94 94",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("g",{filter:"url(#filter0_i)"},J.default.createElement("circle",{cx:"47",cy:"47",r:"47",fill:"#181818"})),J.default.createElement("path",{d:"M32.1506 56.7183C32.4198 56.4452 32.7848 56.2919 33.1654 56.2919H68.2807C68.9203 56.2919 69.2406 57.077 68.788 57.536L61.8494 64.5735C61.5801 64.8466 61.2151 65 60.8345 65H25.7193C25.0796 65 24.7594 64.2148 25.2119 63.7558L32.1506 56.7183Z",fill:"url(#paint0_linear)"}),J.default.createElement("path",{d:"M32.1507 30.4265C32.4199 30.1534 32.7849 30 33.1655 30H68.2807C68.9204 30 69.2406 30.7852 68.7881 31.2442L61.8494 38.2817C61.5802 38.5548 61.2152 38.7081 60.8346 38.7081H25.7194C25.0797 38.7081 24.7595 37.9229 25.2119 37.464L32.1507 30.4265Z",fill:"url(#paint1_linear)"}),J.default.createElement("path",{d:"M61.8494 43.4886C61.5801 43.2156 61.2151 43.0622 60.8345 43.0622H25.7193C25.0796 43.0622 24.7594 43.8474 25.2119 44.3063L32.1506 51.3439C32.4198 51.617 32.7848 51.7704 33.1654 51.7704H68.2807C68.9203 51.7704 69.2406 50.9852 68.788 50.5262L61.8494 43.4886Z",fill:"url(#paint2_linear)"}),J.default.createElement("defs",null,J.default.createElement("filter",{id:"filter0_i",x:"0",y:"0",width:"94",height:"94",filterUnits:"userSpaceOnUse",colorInterpolationFilters:"sRGB"},J.default.createElement("feFlood",{floodOpacity:"0",result:"BackgroundImageFix"}),J.default.createElement("feBlend",{mode:"normal",in:"SourceGraphic",in2:"BackgroundImageFix",result:"shape"}),J.default.createElement("feColorMatrix",{in:"SourceAlpha",type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0",result:"hardAlpha"}),J.default.createElement("feOffset",null),J.default.createElement("feGaussianBlur",{stdDeviation:"2"}),J.default.createElement("feComposite",{in2:"hardAlpha",operator:"arithmetic",k2:"-1",k3:"1"}),J.default.createElement("feColorMatrix",{type:"matrix",values:"0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"}),J.default.createElement("feBlend",{mode:"normal",in2:"shape",result:"effect1_innerShadow"})),J.default.createElement("linearGradient",{id:"paint0_linear",x1:"54.7466",y1:"20.3993",x2:"29.8682",y2:"67.3446",gradientUnits:"userSpaceOnUse"},J.default.createElement("stop",{stopColor:"#00FFA3"}),J.default.createElement("stop",{offset:"1",stopColor:"#DC1FFF"})),J.default.createElement("linearGradient",{id:"paint1_linear",x1:"54.7466",y1:"20.3993",x2:"29.8682",y2:"67.3446",gradientUnits:"userSpaceOnUse"},J.default.createElement("stop",{stopColor:"#00FFA3"}),J.default.createElement("stop",{offset:"1",stopColor:"#DC1FFF"})),J.default.createElement("linearGradient",{id:"paint2_linear",x1:"54.7466",y1:"20.3993",x2:"29.8682",y2:"67.3446",gradientUnits:"userSpaceOnUse"},J.default.createElement("stop",{stopColor:"#00FFA3"}),J.default.createElement("stop",{offset:"1",stopColor:"#DC1FFF"})))),Ym=({width:e,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M12 15.8204L18.1317 12.2036L12 2.00002L5.86826 12.2036L12 15.8204Z",fill:t}),J.default.createElement("path",{d:"M12 22L18.1317 13.3533L12 16.9701L5.86826 13.3533L12 22Z",fill:t})),Xm=({width:e,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M17.1436 8.57817C16.7781 8.36929 16.3081 8.36929 15.8904 8.57817L12.9661 10.3014L10.9817 11.398L8.10968 13.1212C7.74414 13.3301 7.27417 13.3301 6.85641 13.1212L4.61098 11.7635C4.24545 11.5547 3.98435 11.1369 3.98435 10.6669V8.05597C3.98435 7.63822 4.19323 7.22047 4.61098 6.95937L6.85641 5.65389C7.22195 5.44501 7.69192 5.44501 8.10968 5.65389L10.3551 7.01159C10.7206 7.22047 10.9817 7.63822 10.9817 8.10819V9.83143L12.9661 8.68261V6.90715C12.9661 6.4894 12.7572 6.07164 12.3394 5.81055L8.1619 3.35624C7.79636 3.14737 7.32639 3.14737 6.90863 3.35624L2.62665 5.86277C2.20889 6.07164 2.00002 6.4894 2.00002 6.90715V11.8158C2.00002 12.2335 2.20889 12.6513 2.62665 12.9124L6.85641 15.3667C7.22195 15.5755 7.69192 15.5755 8.10968 15.3667L10.9817 13.6957L12.9661 12.5468L15.8381 10.8758C16.2037 10.6669 16.6736 10.6669 17.0914 10.8758L19.3368 12.1813C19.7024 12.3902 19.9635 12.8079 19.9635 13.2779V15.8889C19.9635 16.3066 19.7546 16.7244 19.3368 16.9855L17.1436 18.2909C16.7781 18.4998 16.3081 18.4998 15.8904 18.2909L13.6449 16.9855C13.2794 16.7766 13.0183 16.3588 13.0183 15.8889V14.2178L11.034 15.3667V17.0899C11.034 17.5077 11.2428 17.9254 11.6606 18.1865L15.8904 20.6408C16.2559 20.8497 16.7259 20.8497 17.1436 20.6408L21.3734 18.1865C21.7389 17.9776 22 17.5599 22 17.0899V12.1291C22 11.7113 21.7911 11.2936 21.3734 11.0325L17.1436 8.57817Z",fill:t})),_l=({width:e,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M20.9073 16.5239L17.9359 19.6285C17.8713 19.6959 17.7931 19.7497 17.7063 19.7864C17.6194 19.8232 17.5257 19.8421 17.4311 19.8421H3.34497C3.27776 19.8421 3.21201 19.8229 3.15581 19.787C3.0996 19.7511 3.05539 19.7 3.0286 19.6399C3.00181 19.5799 2.99361 19.5135 3.005 19.449C3.0164 19.3845 3.0469 19.3246 3.09276 19.2767L6.06644 16.1722C6.13086 16.1049 6.20876 16.0513 6.29532 16.0145C6.3819 15.9778 6.47529 15.9588 6.56973 15.9586H20.655C20.7222 15.9586 20.788 15.9777 20.8442 16.0136C20.9004 16.0495 20.9446 16.1007 20.9715 16.1607C20.9982 16.2208 21.0064 16.2871 20.995 16.3517C20.9836 16.4162 20.9531 16.4761 20.9073 16.5239ZM17.9359 10.2723C17.8713 10.2049 17.7931 10.1511 17.7063 10.1144C17.6194 10.0776 17.5257 10.0587 17.4311 10.0588H3.34497C3.27776 10.0588 3.21201 10.0779 3.15581 10.1138C3.0996 10.1497 3.05539 10.2008 3.0286 10.2609C3.00181 10.3209 2.99361 10.3873 3.005 10.4518C3.0164 10.5163 3.0469 10.5762 3.09276 10.6241L6.06644 13.7286C6.13086 13.7959 6.20876 13.8495 6.29532 13.8863C6.3819 13.923 6.47529 13.942 6.56973 13.9422H20.655C20.7222 13.9422 20.788 13.9231 20.8442 13.8872C20.9004 13.8512 20.9446 13.8001 20.9715 13.7401C20.9982 13.68 21.0064 13.6137 20.995 13.5491C20.9836 13.4846 20.9531 13.4247 20.9073 13.3768L17.9359 10.2723ZM3.34497 8.04234H17.4311C17.5257 8.04238 17.6194 8.02345 17.7063 7.9867C17.7931 7.94996 17.8713 7.89619 17.9359 7.82875L20.9073 4.72423C20.9531 4.67636 20.9836 4.61648 20.995 4.55195C21.0064 4.48742 20.9982 4.42105 20.9715 4.361C20.9446 4.30095 20.9004 4.24983 20.8442 4.21393C20.788 4.17802 20.7222 4.15889 20.655 4.15889H6.56973C6.47529 4.15905 6.3819 4.17808 6.29532 4.21482C6.20876 4.25156 6.13086 4.30522 6.06644 4.37248L3.09352 7.477C3.04771 7.52482 3.01723 7.58465 3.0058 7.64909C2.99438 7.71355 3.00251 7.77986 3.02921 7.83989C3.05591 7.89991 3.1 7.95103 3.15609 7.98699C3.21218 8.02295 3.27782 8.04218 3.34497 8.04234Z",fill:t})),$l=({width:e,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M12 15.8204L18.1317 12.2036L12 2.00002L5.86826 12.2036L12 15.8204Z",fill:t}),J.default.createElement("path",{d:"M12 22L18.1317 13.3533L12 16.9701L5.86826 13.3533L12 22Z",fill:t})),zl=({width:e,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M17.1436 8.57817C16.7781 8.36929 16.3081 8.36929 15.8904 8.57817L12.9661 10.3014L10.9817 11.398L8.10968 13.1212C7.74414 13.3301 7.27417 13.3301 6.85641 13.1212L4.61098 11.7635C4.24545 11.5547 3.98435 11.1369 3.98435 10.6669V8.05597C3.98435 7.63822 4.19323 7.22047 4.61098 6.95937L6.85641 5.65389C7.22195 5.44501 7.69192 5.44501 8.10968 5.65389L10.3551 7.01159C10.7206 7.22047 10.9817 7.63822 10.9817 8.10819V9.83143L12.9661 8.68261V6.90715C12.9661 6.4894 12.7572 6.07164 12.3394 5.81055L8.1619 3.35624C7.79636 3.14737 7.32639 3.14737 6.90863 3.35624L2.62665 5.86277C2.20889 6.07164 2.00002 6.4894 2.00002 6.90715V11.8158C2.00002 12.2335 2.20889 12.6513 2.62665 12.9124L6.85641 15.3667C7.22195 15.5755 7.69192 15.5755 8.10968 15.3667L10.9817 13.6957L12.9661 12.5468L15.8381 10.8758C16.2037 10.6669 16.6736 10.6669 17.0914 10.8758L19.3368 12.1813C19.7024 12.3902 19.9635 12.8079 19.9635 13.2779V15.8889C19.9635 16.3066 19.7546 16.7244 19.3368 16.9855L17.1436 18.2909C16.7781 18.4998 16.3081 18.4998 15.8904 18.2909L13.6449 16.9855C13.2794 16.7766 13.0183 16.3588 13.0183 15.8889V14.2178L11.034 15.3667V17.0899C11.034 17.5077 11.2428 17.9254 11.6606 18.1865L15.8904 20.6408C16.2559 20.8497 16.7259 20.8497 17.1436 20.6408L21.3734 18.1865C21.7389 17.9776 22 17.5599 22 17.0899V12.1291C22 11.7113 21.7911 11.2936 21.3734 11.0325L17.1436 8.57817Z",fill:t})),Ul=({width:e,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M7.99996 1C5.97853 1 1 5.97836 1 7.99988C1 10.0214 5.97853 14.9998 7.99996 14.9998C10.0214 14.9998 15 10.0213 15 7.99988C15 5.97844 10.0215 1 7.99996 1ZM6.90913 12.0026C6.05671 11.7704 3.76491 7.76146 3.99723 6.90905C4.22955 6.0566 8.23842 3.76487 9.09083 3.99719C9.94328 4.22947 12.2351 8.2383 12.0028 9.09075C11.7704 9.9432 7.76154 12.235 6.90913 12.0026Z",fill:t})),ga=({width:e,height:t,fill:o="white"})=>J.default.createElement("svg",{width:e,height:t,viewBox:"0 0 18 26",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M14.5119 12.0902C15.807 11.4229 16.6173 10.2476 16.428 8.28976C16.1731 5.61412 13.889 4.71692 11.0056 4.46141L11.005 0.750082H8.77096L8.77034 4.36381C8.18254 4.36381 7.58334 4.37549 6.98685 4.38759L6.98637 0.750575L4.75431 0.750364L4.7539 4.46078C4.27016 4.47077 3.79511 4.48027 3.33125 4.48027V4.46929L0.249756 4.46802L0.250173 6.88041C0.250173 6.88041 1.89999 6.84903 1.87281 6.8795C2.77737 6.88013 3.07273 7.41142 3.15768 7.87001L3.15837 12.098V18.0369C3.11854 18.3247 2.95095 18.7846 2.31845 18.7854C2.34723 18.811 0.693316 18.7851 0.693316 18.7851L0.250173 21.4827H3.15698C3.69863 21.4834 4.23081 21.4919 4.75292 21.4961L4.75487 25.2494L6.98637 25.2501L6.98574 21.5361C7.59968 21.5487 8.19227 21.5542 8.7711 21.5535L8.77048 25.2501H11.0045L11.0057 21.503C14.7609 21.2849 17.3899 20.3281 17.7168 16.7612C17.9802 13.8895 16.6439 12.6077 14.5119 12.0902ZM7.04024 7.04325C8.30022 7.04325 12.2629 6.63743 12.2634 9.29956C12.2629 11.8518 8.30148 11.5536 7.04024 11.5536V7.04325ZM7.03926 18.8037L7.04024 13.8306C8.55408 13.8302 13.3023 13.39 13.3029 16.316C13.3035 19.1221 8.55408 18.8026 7.03926 18.8037Z",fill:o})),Vl=({width:e,height:t,fill:o="#FFFFFF"})=>{let r=e||t||17;return J.default.createElement("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M9.51948 13.9003L17.4173 6.26045C17.7763 5.91318 18.3546 5.91318 18.7136 6.26045L19.7308 7.24437C20.0897 7.59164 20.0897 8.15113 19.7308 8.4791L10.1776 17.7395C9.81863 18.0868 9.24026 18.0868 8.88127 17.7395L4.25428 13.2637C3.91524 12.9357 3.91524 12.3762 4.25428 12.0289L5.29137 11.045C5.63041 10.6977 6.20879 10.6977 6.56778 11.045L9.51948 13.9003Z",fill:o}))},ya=({width:e=16,height:t=16,fill:o="#FFFFFF"})=>J.default.createElement("svg",{width:e,height:t,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M3.69466 12.3154C3.97135 12.5921 4.4362 12.5811 4.70182 12.321L8 9.02279L11.2926 12.321C11.5638 12.5921 12.0286 12.5921 12.2998 12.3154C12.571 12.0387 12.5765 11.5794 12.3053 11.3083L9.0127 8.01009L12.3053 4.71745C12.5765 4.44629 12.5765 3.98698 12.2998 3.71029C12.0231 3.43913 11.5638 3.43359 11.2926 3.70475L8 7.00293L4.70182 3.70475C4.4362 3.44466 3.96582 3.43359 3.69466 3.71029C3.4235 3.98698 3.42904 4.45182 3.68913 4.71191L6.9873 8.01009L3.68913 11.3138C3.42904 11.5739 3.41797 12.0443 3.69466 12.3154Z",fill:o})),Hl=({width:e,height:t,fill:o="#2D2D2D"})=>{let r=e||t||62;return J.default.createElement("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M5.70147 4.0003C5.58476 4.00441 5.47074 4.03658 5.36909 4.0941C5.26743 4.15162 5.18113 4.23278 5.11747 4.33074C5.05382 4.42869 5.01469 4.54054 5.0034 4.65682C4.99212 4.7731 5.009 4.89039 5.05262 4.99876L7.56284 11.2736H13.0007C13.1937 11.2736 13.3788 11.3503 13.5152 11.4868C13.6517 11.6233 13.7283 11.8084 13.7283 12.0015C13.7283 12.1945 13.6517 12.3797 13.5152 12.5162C13.3788 12.6527 13.1937 12.7294 13.0007 12.7294H7.56142L5.05262 19.0042C4.99813 19.1414 4.98666 19.2919 5.01974 19.4358C5.05283 19.5797 5.1289 19.71 5.23785 19.8096C5.3468 19.9092 5.48346 19.9733 5.62968 19.9933C5.77589 20.0133 5.92474 19.9883 6.05642 19.9217L20.5938 12.6541C20.7157 12.5941 20.8184 12.5012 20.8902 12.3859C20.9619 12.2705 21 12.1374 21 12.0015C21 11.8656 20.9619 11.7324 20.8902 11.6171C20.8184 11.5017 20.7157 11.4088 20.5938 11.3489L6.05642 4.08126C5.94689 4.0246 5.82474 3.99674 5.70147 4.0003V4.0003Z",fill:o}))},Zm=({width:e,height:t,fill:o="none"})=>{let r=e||t||32;return J.default.createElement("svg",{width:r,height:r,viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M16.1121 20L19.7726 16.3439C19.9182 16.1983 20 16.0009 20 15.795C20 15.5892 19.9182 15.3917 19.7726 15.2461L16.1121 11.59L15.0129 12.6879L17.3461 15.0182H6.74062V16.5718H17.3461L15.0129 18.9021L16.1121 20Z",fill:o}),J.default.createElement("path",{d:"M7.88794 4L4.22745 7.65606C4.08181 7.8017 4 7.99914 4 8.20498C4 8.41083 4.08181 8.60826 4.22745 8.75391L7.88794 12.41L8.98712 11.3121L6.65395 8.98177H17.2594V7.4282H6.65395L8.98712 5.09785L7.88794 4Z",fill:o}))},Wl=({width:e,height:t,fill:o="#777777"})=>{let r=e||t||16;return J.default.createElement("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M11.0737 19.4932L4.89026 13.7049L6.76695 11.9256L10.6888 15.6193V3.75H13.3354V15.6193L17.2572 11.9256L19.1339 13.7049L12.9504 19.4932L12.0121 20.3265L11.0737 19.4932Z",fill:o}))},Kl=({width:e=20,fill:t="#FFE920"})=>{let o=e;return J.default.createElement("svg",{width:o,height:o,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M13.0831 4.76662L14.3 8.46686C14.4444 8.93706 14.8776 9.24371 15.3726 9.24371H19.2917C20.3849 9.24371 20.8387 10.6339 19.9518 11.2676L16.7752 13.5777C16.3833 13.8639 16.2183 14.375 16.3627 14.8247L17.5797 18.525C17.9097 19.5676 16.734 20.4262 15.847 19.772L12.6705 17.4824C12.2786 17.1962 11.7423 17.1962 11.3504 17.4824L8.15327 19.772C7.26632 20.4058 6.06997 19.5471 6.42063 18.525L7.6376 14.8247C7.78199 14.3545 7.61698 13.8639 7.22507 13.5777L4.04856 11.288C3.16161 10.6543 3.61539 9.26415 4.70861 9.26415H8.62769C9.12273 9.26415 9.55589 8.9575 9.70028 8.48731L10.9173 4.76662C11.2679 3.74446 12.7324 3.74446 13.0831 4.76662Z",fill:t}))},ql=({width:e,height:t,fill:o="#2C2D30"})=>{let r=e||t||16;return J.default.createElement("svg",{width:r,height:r,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M10.7984 3.36833C10.7984 3.36833 8.52118 5.53552 6.92542 7.13129C3.88627 10.1704 4.00056 13.0106 4.00056 14.0688C4.00056 15.635 4.91907 17.5059 6.32436 18.9111C7.53917 20.126 8.90213 20.9894 11.6407 20.9894C14.3794 20.9894 15.8524 20.0371 16.8682 19.0339C18.2143 17.709 19.3317 16.0159 19.3317 13.5567C19.3317 10.9196 18.4428 9.41276 17.7995 8.82017C17.4354 8.48578 16.936 8.21488 16.4661 8.63393C15.9963 9.05297 15.6196 9.39583 15.463 9.53128C15.3063 9.66673 15.0651 9.5863 15.137 9.27308C15.209 8.95985 15.4291 8.8625 15.4291 8.06673C15.4291 7.27097 14.9635 6.24663 14.2016 5.31965L12.6651 3.46145C12.191 2.88579 11.3233 2.84347 10.79 3.3641L10.7984 3.36833Z",fill:o}))},kr=({width:e=22,fill:t="white"})=>J.default.createElement("svg",{width:e,height:e,viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},J.default.createElement("path",{d:"M7.98768 15C11.8606 15 15 11.8662 15 8C15 4.13376 11.8606 1 7.98768 1C4.31352 1 1.2996 3.82128 1 7.41144H10.2686V8.58856H1C1.2996 12.1787 4.31352 15 7.98768 15Z",fill:t}));var Vn={icon:{badge:{x:{bgColor:"#EB3742",shape:"circle",src:ya},send:{bgColor:"#AB9FF2",shape:"circle",src:Hl},receive:{bgColor:"#AB9FF2",shape:"circle",src:Wl},star:{bgColor:"#FFE920",shape:"circle",src:Kl},burn:{bgColor:"#EB3742",shape:"circle",src:ql},check:{bgColor:"#21E56F",shape:"circle",src:Vl}},image:{x:{bgColor:dt("#EB3742",.1),fgColor:"#EB3742",shape:"circle",src:ya},"x-bold":{bgColor:"#EB3742",fgColor:"rgba(0,0,0,0.75)",shape:"circle",src:ya},send:{bgColor:dt("#AB9FF2",.1),fgColor:"#AB9FF2",shape:"circle",src:Hl},receive:{bgColor:dt("#AB9FF2",.1),fgColor:"#AB9FF2",shape:"circle",src:Wl},swap:{bgColor:dt("#21E56F",.1),fgColor:"#21E56F",shape:"circle",src:Zm},star:{bgColor:dt("#FFE920",.1),fgColor:"#FFE920",shape:"circle",src:Kl},burn:{bgColor:dt("#EB3742",.1),fgColor:"#EB3742",shape:"circle",src:ql},check:{bgColor:dt("#21E56F",.1),fgColor:"#21E56F",shape:"circle",src:Vl}}},network:{badge:{solana:{src:_l,shape:"square"},ethereum:{src:$l,shape:"square"},polygon:{src:zl,shape:"square"},monad:{src:Ul,shape:"square"},bitcoin:{src:ga,shape:"square"},base:{src:kr,shape:"square"},sui:{src:kr,shape:"square"}},image:{solana:{src:_l,shape:"square",fgColor:"#222",bgColor:"#eee"},ethereum:{src:$l,shape:"square",fgColor:"#222",bgColor:"#eee"},polygon:{src:zl,shape:"square",fgColor:"#222",bgColor:"#eee"},monad:{src:Ul,shape:"square",fgColor:"#222",bgColor:"#eee"},bitcoin:{src:ga,shape:"square",fgColor:"#222",bgColor:"#eee"},base:{src:kr,shape:"square",fgColor:"#222",bgColor:"#eee"},sui:{src:kr,shape:"square",fgColor:"#222",bgColor:"#eee"}}},fungible:{image:{sol:{src:Jm,shape:"circle",fullSize:!0},eth:{src:Ym,shape:"circle",fgColor:"#222",bgColor:"#eee"},matic:{src:Xm,shape:"circle",fgColor:"#222",bgColor:"#eee"},btc:{src:ga,shape:"circle",fgColor:"#222",bgColor:"#eee"},base:{src:kr,shape:"circle",fgColor:"#222",bgColor:"#eee"},sui:{src:kr,shape:"circle",fgColor:"#222",bgColor:"#eee"}}}};var ah=["collectible","dapp","network"],gt=(0,ar.memo)(e=>{if("tokenSell"in e){let a=typeof e.tokenSell=="string"?Vn.fungible.image[e.tokenSell]:e.tokenSell,s=typeof e.tokenBuy=="string"?Vn.fungible.image[e.tokenBuy]:e.tokenBuy,{image:l,tokenBuy:u,tokenSell:p,onClick:f,...m}=e;return ar.default.createElement(Qm,{bgToken:a,fgToken:s,onClick:f,...m})}if("tokenType"in e)return ar.default.createElement(sh,{...e});let t,o;if("preset"in e.image){let a=e.image.type,s=e.image.preset;t=Vn[a].image[s]}else{let a=ah.includes(e.image.type)?"square":"circle";t={...e.image,shape:a}}if("badge"in e&&e.badge)if("preset"in e.badge){let a=e.badge.type,s=e.badge.preset;o=Vn[a].badge[s]}else o={...e.badge,shape:"square"};let{image:r,badge:n,...i}=e;return ar.default.createElement(Gm,{image:t,badge:o,...i})}),sh=(0,ar.memo)(({image:e,tokenType:t,chainMeta:o,...r})=>{let n=Xc(),i=Ye.getChainID(o.id),a=lh(n,t,i)?i:void 0;return ar.default.createElement(gt,{image:e,badge:a&&{type:"network",preset:a},...r})}),lh=(e,t,o)=>e?!1:Vr.get(o).alwaysShowNetworkBadge?!0:!vi(t);var Oo=I(D()),ef=(0,Oo.memo)(({activityItem:{interactionData:e,chainMeta:{status:t,chainId:o},owner:r},size:n="small",onClick:i})=>{let a=(0,Oo.useMemo)(()=>{let s=t==="failed",l=(()=>{switch(e.transactionType){case"TOKEN_SEND":{let m=e.balanceChanges[0],{token:{tokenType:g,logoURI:y,symbol:h,id:w},to:k}=m,C=r.toLowerCase()===k.toLowerCase()?"receive":"send",v=br(g)?void 0:h||zr(Si(w)||""),F=br(g)?"collectible":"fungible";return{badge:{type:"icon",preset:C},image:{type:F,src:y,fallback:v},onClick:i}}case"TOKEN_BURN":{let{token:{tokenType:f,logoURI:m,symbol:g,id:y}}=e,h=br(f)?void 0:g||zr(Si(y)||""),w=br(f)?"collectible":"fungible";return{badge:{type:"icon",preset:"burn"},image:{type:w,src:m,fallback:h},onClick:i}}case"TOKEN_SWAP":{let{receiveToken:f,sendToken:m,dapp:g}=e;return f.logoURI&&m.logoURI&&!s?{image:{type:"swap"},tokenBuy:{src:f.logoURI},tokenSell:{src:m.logoURI},onClick:i}:{image:{type:"icon",preset:"swap"},...g&&{badge:{src:g.logoURI}}}}case"BRIDGE_INIT":return{image:{type:"icon",preset:"swap"},onClick:i};case"TOKEN_UNWRAP":{let{token:f}=e;return f.logoURI?{image:{type:"fungible",src:f.logoURI},badge:{type:"icon",preset:"receive"},onClick:i}:{image:{type:"icon",preset:"swap"},onClick:i}}case"STAKE":case"WITHDRAW_STAKE":case"DEACTIVATE_STAKE":switch(o){case Qe.Solana.Devnet:case Qe.Solana.Localnet:case Qe.Solana.Testnet:case Qe.Solana.Mainnet:return{image:{type:"fungible",preset:"sol"},badge:{type:"icon",preset:"star"},onClick:i};default:return{image:{type:"icon",preset:"star"},onClick:i}}case"COLLECTIBLE_LIST":case"COLLECTIBLE_BUY_ITEM":case"COLLECTIBLE_CANCEL_BID":case"COLLECTIBLE_BID_ITEM":case"COLLECTIBLE_UNLIST":case"COLLECTIBLE_SELL_ITEM":{let{dapp:f,item:m}=e;return m.logoURI?{image:{type:"collectible",src:m.logoURI},...f&&{badge:{src:f.logoURI}},onClick:i}:{image:{type:"dapp",src:f?.logoURI},onClick:i}}case"TOKEN_APPROVAL":{let{dapp:f,token:m}=e,g=br(m.tokenType)?"collectible":"fungible";if(m.logoURI||!br(m.tokenType)){let y=m.symbol||zr(Si(m.id)||"");return{image:{type:g,src:m.logoURI,fallback:y},...f&&{badge:{src:f.logoURI}},onClick:i}}else return{image:{type:"dapp",src:f?.logoURI},onClick:i}}case"COLLECTION_APPROVAL":{let{dapp:f,collection:m}=e;return m.logoURI?{image:{type:"collectible",src:m.logoURI},...f&&{badge:{src:f.logoURI}},onClick:i}:{image:{type:"dapp",src:f?.logoURI},onClick:i}}case"CANCEL_TX":return{image:{type:"icon",preset:"x"},dimmed:!1,badge:void 0,onClick:i};case"CANCEL_ORDER":case"UNCLASSIFIED_APP_INTERACTION":default:{let{dapp:f}=e;return f?.logoURI?{image:{type:"dapp",src:f.logoURI},onClick:i}:{image:{type:"icon",preset:s?"x":"check"},dimmed:!1,badge:void 0,onClick:i}}}})(),u=l.image.type==="icon"&&l.image.preset==="x",p={...s&&!u&&{badge:{type:"icon",preset:"x"},dimmed:!0}};return{...l,...p}},[t,e,r,i,o]);return Oo.default.createElement(gt,{...a,size:bo[n]})}),tf=(0,Oo.memo)(({pendingTransactionType:e,size:t="small",logoUri:o,isError:r,isConfirmed:n})=>{let i=(0,Oo.useMemo)(()=>{let a=(()=>{switch(e){case"cancel":return{image:{type:"icon",preset:"x"}};case"send":return o?{image:{type:"fungible",src:o},badge:{type:"icon",preset:"send"}}:{image:{type:"icon",preset:"send"}};case"swap":return{image:{type:"icon",preset:"swap"}};case"createAccountAndDelegateStake":case"delegateStake":case"deactivateState":case"withdrawStake":return{image:{type:"icon",preset:"star"}};case"burn":return{image:{type:"icon",preset:"burn"}};case"dappInteraction":return o?{image:{type:"dapp",src:o}}:{image:{type:"icon",preset:"check"}};case"createListing":case"editListing":case"removeListing":return o?{image:{type:"collectible",src:o}}:{image:{type:"icon",preset:"check"}};case"unwrapSOL":return o?{image:{type:"fungible",src:o},badge:{type:"icon",preset:"receive"}}:{image:{type:"icon",preset:"check"}};case"createAssociatedTokenAccount":case"solanaPayTransaction":default:return o?{image:{type:"fungible",src:o}}:{image:{type:"icon",preset:"check"}}}})(),s=a.image.type==="icon"&&a.image.preset==="x",l={loading:!n,...r&&!s&&{badge:{type:"icon",preset:"x"},dimmed:!0}};return{...a,...l}},[n,r,o,e]);return Oo.default.createElement(gt,{...i,size:bo[t]})});var ch=({activityItem:e,onClick:t})=>{let{t:o}=$(),{getKnownAddressLabel:r}=dd(),{data:n}=ut(),i=_d(e,o,ef,r,n?.addresses??[]);return ha.default.createElement(jt,{verticalAlign:"baseline",onClick:t,start:i.start,topLeft:i.topLeft,topRight:i.topRight,bottomLeft:i.bottomLeft,bottomRight:i.bottomRight})},ba=(0,ha.memo)(ch);c();d();var of=I(D());var rf=({pendingTransaction:e,onClick:t})=>{let o=$d(e,tf);return of.default.createElement(jt,{truncate:"left",verticalAlign:"baseline",onClick:t,start:o.start,topLeft:o.topLeft,topRight:o.topRight,bottomLeft:o.bottomLeft,bottomRight:o.bottomRight})};var dh=x.div`
  display: flex;
  flex-direction: column;
  align-items: center;
`,jl=x.div`
  margin-top: 5px;
  margin-bottom: 5px;
  cursor: ${e=>e.onClick?"pointer":"default"};
`,uh=x(to)`
  align-items: stretch;
  display: flex;
  overflow: hidden;
`,Rl=x.div`
  padding-bottom: 10px;
  padding-top: 10px;
`,ph=x.div`
  margin-top: 50px;
`,mh=x.div`
  margin: 28px 0 12px;
`,fh=x.div`
  display: flex;
  flex-direction: column;
  gap: 10px;
`,gh=()=>re.default.createElement(re.default.Fragment,null,re.default.createElement(mh,null,re.default.createElement($t,{height:8,width:80})),re.default.createElement(fh,null,[1,2,3,4].map(e=>re.default.createElement(An,{key:`fungible-token-row-loader-${e}`})))),yh=e=>e.reduce((t,o)=>{let r=si((o?.timestamp??0)*1e3).format("YYYY-MM-DD");return(t[r]=t[r]||[]).push(o),t},{}),hh=(e,t,o)=>{let r=!1,n=yh(e),i=Object.keys(n).sort((a,s)=>s.localeCompare(a)).map(a=>{let s=si(a),l=si().diff(s,"days");r=r||l===0;let u=t("richTransactionsToday");return l>1?u=s.format("MMM D, YYYY"):l>0&&(u=t("richTransactionsYesterday")),{title:u,data:n[a]}});return o&&!r&&i.unshift({title:t("richTransactionsToday"),data:[]}),i},bh=(e,t,o)=>{let[r,n]=(0,re.useState)(!1);(0,re.useEffect)(()=>{t&&e.current&&r&&(e.current.scrollIntoView(!1),n(!1))},[t,e,r]),(0,re.useEffect)(()=>{o&&n(!0)},[o])},Ir=({header:e,pendingTransactions:t,dataPages:o,isLoading:r,fetchNextPage:n,refetch:i,hasNextPage:a,isFetchingNextPage:s,isError:l,isRefreshError:u,isRefreshingConfirmedTxs:p,isDeveloperMode:f,showActivityItemModal:m,showPendingTransactionModal:g})=>{let{t:y}=$(),h=(0,re.useRef)(null);bh(h,l,s);let w=(0,re.useMemo)(()=>(o||[]).reduce((O,U)=>[...O,...U?.results??[]],[]),[o]),k=(0,re.useRef)(null),C=(0,re.useMemo)(()=>{let O=w.filter(U=>!(t||[]).find(M=>{let N;switch(M.networkID){case Qe.Base.Mainnet:case Qe.Base.Sepolia:case Qe.Monad.Mainnet:case Qe.Monad.Testnet:case Qe.Monad.Devnet:case Qe.Ethereum.Mainnet:case Qe.Ethereum.Sepolia:case Qe.Polygon.Mainnet:case Qe.Polygon.Amoy:{N=M.data.transactions.some(L=>L.hash===U.chainMeta.transactionId);break}case Qe.Solana.Mainnet:case Qe.Solana.Testnet:case Qe.Solana.Devnet:case Qe.Solana.Localnet:{N=M.data.signature===U.chainMeta.transactionId;break}case Qe.Bitcoin.Mainnet:case Qe.Bitcoin.Testnet:N=M.data.txID===U.chainMeta.transactionId}return N}));return O.length?hh(O,y,p):[]},[w,y,t,p]),v=!!C.length;if(!v&&!r&&!l&&!t?.length)return re.default.createElement(re.default.Fragment,null,e,re.default.createElement(Fn,null,y("transactionsNoActivity")));let F=O=>{m({activityItem:O}),we.capture("activityItemDetailOpenClick",{data:{networkId:O.chainMeta.chainId,chainId:O.chainMeta.chainId,transactionType:O.interactionData.transactionType}})},z=O=>Ye.isEVMNetworkID(O.networkID)||Ye.isBitcoinNetworkID(O.networkID)?()=>{g?.({pendingTransactionId:O.id}),we.capture("transactionDetailOpenClick",{data:{chainID:O.networkID}})}:void 0,T=null;return l&&f?T=re.default.createElement(Rl,{ref:h},re.default.createElement(ua,{color:"#EB3742",title:y("activityItemsTestnetNotAvailable")})):l&&!f&&!v?T=re.default.createElement(ph,null,re.default.createElement(Ui,{title:y("errorAndOfflineSomethingWentWrong"),description:y("errorAndOfflineUnableToFetchTransactionHistory"),buttonText:y("commandRetry"),refetch:i})):l&&!f&&v&&(T=re.default.createElement(Rl,{ref:h},re.default.createElement(ua,{color:"#EB3742",title:y("activityItemsPagingFailed")}))),re.default.createElement(Km,{style:{margin:-15,padding:15},loadNext:n,hasNext:!!a,isLoadingNext:s},re.default.createElement(dh,{ref:k},e||null,re.default.createElement(uh,{"data-testid":"activity-list-transactions-container"},r&&re.default.createElement(gh,null),!r&&re.default.createElement(re.default.Fragment,null,u&&v&&re.default.createElement(Rl,null,re.default.createElement(ua,{color:"#EB3742",title:y("activityItemsRefreshFailed")})),re.default.createElement(Fo,null,!!t?.length&&re.default.createElement(vh,{pendingTransactions:t,onClickPendingTransaction:z})),C.map(({title:O,data:U},M)=>re.default.createElement(re.default.Fragment,{key:`${O}-${M}`},re.default.createElement(xh,{title:O,activityItems:U,isLoading:M===0&&p,onClickItem:F}))),T))))},xh=({title:e,activityItems:t,isLoading:o,onClickItem:r})=>re.default.createElement(re.default.Fragment,null,re.default.createElement(W,{size:16,lineHeight:19,textAlign:"left",weight:500,margin:"15px 0 5px 0",color:"#999999"},e),o&&re.default.createElement(jl,null,re.default.createElement(An,null)),t.map(n=>re.default.createElement(jl,{"data-testid":"activity-list-transaction-row",key:n.owner+"-"+n.id},re.default.createElement(ba,{activityItem:n,onClick:()=>r(n)})))),vh=({pendingTransactions:e,onClickPendingTransaction:t})=>{let{t:o}=$();return re.default.createElement(Xe.div,{exit:{height:0,opacity:0},transition:{duration:.2}},re.default.createElement(W,{size:16,lineHeight:19,textAlign:"left",weight:500,margin:"15px 0 5px 0",color:"#999999"},o("richTransactionDetailPending")),re.default.createElement(Fo,null,e.map(r=>re.default.createElement(Xe.div,{key:r.ownerAddress+"-"+r.id,exit:{height:0,opacity:0},transition:{duration:.2}},re.default.createElement(jl,{onClick:t(r)},re.default.createElement(rf,{pendingTransaction:r}))))))};var nf=({isSpam:e})=>{let{t}=$(),{data:o}=ut(),[r,n]=(0,Jt.useMemo)(()=>{let{addresses:h=[]}=o??{};return[h,h.map(w=>gd(w))]},[o]),i=Ci({accounts:n,isSpam:e}),{data:a}=Td(r);(0,Jt.useEffect)(()=>{a?.removedPendingTransactions&&a?.removedPendingTransactions.length>0&&i.refetch()},[a,n,e]);let{data:s=di,status:l}=fi(),u=s.isDeveloperMode,p=i.isPending||l==="pending",{handleShowModalVisibility:f,handleHideModalVisibility:m}=de(),g=(0,Jt.useCallback)(h=>f("activityItem",h),[f]),y=(0,Jt.useCallback)(h=>f("pendingTransaction",h),[f]);if(e){let h=()=>{m("spamActivity")};return Jt.default.createElement(Ir,{dataPages:i.data?.pages,fetchNextPage:i.fetchNextPage,hasNextPage:i.hasNextPage,header:Jt.default.createElement(mt,{onLeftButtonClick:h,useCloseButton:!0},t("spamActivityTitle")),isDeveloperMode:u,isError:i.isError,isFetchingNextPage:i.isFetchingNextPage,isLoading:p,isRefreshError:i.isRefetchError,isRefreshingConfirmedTxs:i.isRefetching,refetch:i.refetch,showActivityItemModal:g})}else{let h=[{label:t("spamActivityAction"),key:"view-spam-activity",onClick:()=>{f("spamActivity",void 0,{event:"showspamActivityModal"})}}];return Jt.default.createElement(Ir,{dataPages:i.data?.pages,fetchNextPage:i.fetchNextPage,hasNextPage:i.hasNextPage,header:Jt.default.createElement(mt,{items:h},t("recentActivityPrimaryText")),isDeveloperMode:u,isError:i.isError,isFetchingNextPage:i.isFetchingNextPage,isLoading:p,isRefreshError:i.isRefetchError,isRefreshingConfirmedTxs:i.isRefetching,pendingTransactions:a?.pendingTransaction||[],refetch:i.refetch,showActivityItemModal:g,showPendingTransactionModal:y})}};c();d();var qe=I(D());c();d();var Ta=I(D(),1),Wn=I(D(),1),$o=I(D(),1);c();d();var wh={data:""},Sh=e=>typeof self=="object"?((e?e.querySelector("#_goober"):self._goober)||Object.assign((e||document.head).appendChild(document.createElement("style")),{innerHTML:" ",id:"_goober"})).firstChild:e||wh;var Ch=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,Th=/\/\*[^]*?\*\/|  +/g,af=/\n+/g,sr=(e,t)=>{let o="",r="",n="";for(let i in e){let a=e[i];i[0]=="@"?i[1]=="i"?o=i+" "+a+";":r+=i[1]=="f"?sr(a,i):i+"{"+sr(a,i[1]=="k"?"":t)+"}":typeof a=="object"?r+=sr(a,t?t.replace(/([^,])+/g,s=>i.replace(/(^:.*)|([^,])+/g,l=>/&/.test(l)?l.replace(/&/g,s):s?s+" "+l:l)):i):a!=null&&(i=/^--/.test(i)?i:i.replace(/[A-Z]/g,"-$&").toLowerCase(),n+=sr.p?sr.p(i,a):i+":"+a+";")}return o+(t&&n?t+"{"+n+"}":n)+r},_o={},sf=e=>{if(typeof e=="object"){let t="";for(let o in e)t+=o+sf(e[o]);return t}return e},Ph=(e,t,o,r,n)=>{let i=sf(e),a=_o[i]||(_o[i]=(l=>{let u=0,p=11;for(;u<l.length;)p=101*p+l.charCodeAt(u++)>>>0;return"go"+p})(i));if(!_o[a]){let l=i!==e?e:(u=>{let p,f,m=[{}];for(;p=Ch.exec(u.replace(Th,""));)p[4]?m.shift():p[3]?(f=p[3].replace(af," ").trim(),m.unshift(m[0][f]=m[0][f]||{})):m[0][p[1]]=p[2].replace(af," ").trim();return m[0]})(e);_o[a]=sr(n?{["@keyframes "+a]:l}:l,o?"":"."+a)}let s=o&&_o.g?_o.g:null;return o&&(_o.g=_o[a]),((l,u,p,f)=>{f?u.data=u.data.replace(f,l):u.data.indexOf(l)===-1&&(u.data=p?l+u.data:u.data+l)})(_o[a],t,r,s),a},kh=(e,t,o)=>e.reduce((r,n,i)=>{let a=t[i];if(a&&a.call){let s=a(o),l=s&&s.props&&s.props.className||/^go/.test(s)&&s;a=l?"."+l:s&&typeof s=="object"?s.props?"":sr(s,""):s===!1?"":s}return r+n+(a??"")},"");function Hn(e){let t=this||{},o=e.call?e(t.p):e;return Ph(o.unshift?o.raw?kh(o,[].slice.call(arguments,1),t.p):o.reduce((r,n)=>Object.assign(r,n&&n.call?n(t.p):n),{}):o,Sh(t.target),t.g,t.o,t.k)}var lf,Gl,Ql,k4=Hn.bind({g:1}),Ft=Hn.bind({k:1});function cf(e,t,o,r){sr.p=t,lf=e,Gl=o,Ql=r}function Vt(e,t){let o=this||{};return function(){let r=arguments;function n(i,a){let s=Object.assign({},i),l=s.className||n.className;o.p=Object.assign({theme:Gl&&Gl()},s),o.o=/ *go\d+/.test(l),s.className=Hn.apply(o,r)+(l?" "+l:""),t&&(s.ref=a);let u=e;return e[0]&&(u=s.as||e,delete s.as),Ql&&u[0]&&Ql(s),lf(u,s)}return t?t(n):n}}var Ar=I(D(),1);var lr=I(D(),1),Ih=e=>typeof e=="function",Ca=(e,t)=>Ih(e)?e(t):e,Ah=(()=>{let e=0;return()=>(++e).toString()})(),uf=(()=>{let e;return()=>{if(e===void 0&&typeof self<"u"){let t=matchMedia("(prefers-reduced-motion: reduce)");e=!t||t.matches}return e}})(),Eh=20,va=new Map,Lh=1e3,df=e=>{if(va.has(e))return;let t=setTimeout(()=>{va.delete(e),Er({type:4,toastId:e})},Lh);va.set(e,t)},Dh=e=>{let t=va.get(e);t&&clearTimeout(t)},Jl=(e,t)=>{switch(t.type){case 0:return{...e,toasts:[t.toast,...e.toasts].slice(0,Eh)};case 1:return t.toast.id&&Dh(t.toast.id),{...e,toasts:e.toasts.map(i=>i.id===t.toast.id?{...i,...t.toast}:i)};case 2:let{toast:o}=t;return e.toasts.find(i=>i.id===o.id)?Jl(e,{type:1,toast:o}):Jl(e,{type:0,toast:o});case 3:let{toastId:r}=t;return r?df(r):e.toasts.forEach(i=>{df(i.id)}),{...e,toasts:e.toasts.map(i=>i.id===r||r===void 0?{...i,visible:!1}:i)};case 4:return t.toastId===void 0?{...e,toasts:[]}:{...e,toasts:e.toasts.filter(i=>i.id!==t.toastId)};case 5:return{...e,pausedAt:t.time};case 6:let n=t.time-(e.pausedAt||0);return{...e,pausedAt:void 0,toasts:e.toasts.map(i=>({...i,pauseDuration:i.pauseDuration+n}))}}},wa=[],Sa={toasts:[],pausedAt:void 0},Er=e=>{Sa=Jl(Sa,e),wa.forEach(t=>{t(Sa)})},Fh={blank:4e3,error:4e3,success:2e3,loading:1/0,custom:4e3},Mh=(e={})=>{let[t,o]=(0,Ta.useState)(Sa);(0,Ta.useEffect)(()=>(wa.push(o),()=>{let n=wa.indexOf(o);n>-1&&wa.splice(n,1)}),[t]);let r=t.toasts.map(n=>{var i,a;return{...e,...e[n.type],...n,duration:n.duration||((i=e[n.type])==null?void 0:i.duration)||e?.duration||Fh[n.type],style:{...e.style,...(a=e[n.type])==null?void 0:a.style,...n.style}}});return{...t,toasts:r}},Bh=(e,t="blank",o)=>({createdAt:Date.now(),visible:!0,type:t,ariaProps:{role:"status","aria-live":"polite"},message:e,pauseDuration:0,...o,id:o?.id||Ah()}),Kn=e=>(t,o)=>{let r=Bh(t,e,o);return Er({type:2,toast:r}),r.id},De=(e,t)=>Kn("blank")(e,t);De.error=Kn("error");De.success=Kn("success");De.loading=Kn("loading");De.custom=Kn("custom");De.dismiss=e=>{Er({type:3,toastId:e})};De.remove=e=>Er({type:4,toastId:e});De.promise=(e,t,o)=>{let r=De.loading(t.loading,{...o,...o?.loading});return e.then(n=>(De.success(Ca(t.success,n),{id:r,...o,...o?.success}),n)).catch(n=>{De.error(Ca(t.error,n),{id:r,...o,...o?.error})}),e};var Nh=(e,t)=>{Er({type:1,toast:{id:e,height:t}})},Oh=()=>{Er({type:5,time:Date.now()})},_h=e=>{let{toasts:t,pausedAt:o}=Mh(e);(0,Wn.useEffect)(()=>{if(o)return;let i=Date.now(),a=t.map(s=>{if(s.duration===1/0)return;let l=(s.duration||0)+s.pauseDuration-(i-s.createdAt);if(l<0){s.visible&&De.dismiss(s.id);return}return setTimeout(()=>De.dismiss(s.id),l)});return()=>{a.forEach(s=>s&&clearTimeout(s))}},[t,o]);let r=(0,Wn.useCallback)(()=>{o&&Er({type:6,time:Date.now()})},[o]),n=(0,Wn.useCallback)((i,a)=>{let{reverseOrder:s=!1,gutter:l=8,defaultPosition:u}=a||{},p=t.filter(g=>(g.position||u)===(i.position||u)&&g.height),f=p.findIndex(g=>g.id===i.id),m=p.filter((g,y)=>y<f&&g.visible).length;return p.filter(g=>g.visible).slice(...s?[m+1]:[0,m]).reduce((g,y)=>g+(y.height||0)+l,0)},[t]);return{toasts:t,handlers:{updateHeight:Nh,startPause:Oh,endPause:r,calculateOffset:n}}},$h=Ft`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
 transform: scale(1) rotate(45deg);
  opacity: 1;
}`,zh=Ft`
from {
  transform: scale(0);
  opacity: 0;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Uh=Ft`
from {
  transform: scale(0) rotate(90deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(90deg);
	opacity: 1;
}`,Vh=Vt("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#ff4b4b"};
  position: relative;
  transform: rotate(45deg);

  animation: ${$h} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;

  &:after,
  &:before {
    content: '';
    animation: ${zh} 0.15s ease-out forwards;
    animation-delay: 150ms;
    position: absolute;
    border-radius: 3px;
    opacity: 0;
    background: ${e=>e.secondary||"#fff"};
    bottom: 9px;
    left: 4px;
    height: 2px;
    width: 12px;
  }

  &:before {
    animation: ${Uh} 0.15s ease-out forwards;
    animation-delay: 180ms;
    transform: rotate(90deg);
  }
`,Hh=Ft`
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
`,Wh=Vt("div")`
  width: 12px;
  height: 12px;
  box-sizing: border-box;
  border: 2px solid;
  border-radius: 100%;
  border-color: ${e=>e.secondary||"#e0e0e0"};
  border-right-color: ${e=>e.primary||"#616161"};
  animation: ${Hh} 1s linear infinite;
`,Kh=Ft`
from {
  transform: scale(0) rotate(45deg);
	opacity: 0;
}
to {
  transform: scale(1) rotate(45deg);
	opacity: 1;
}`,qh=Ft`
0% {
	height: 0;
	width: 0;
	opacity: 0;
}
40% {
  height: 0;
	width: 6px;
	opacity: 1;
}
100% {
  opacity: 1;
  height: 10px;
}`,Rh=Vt("div")`
  width: 20px;
  opacity: 0;
  height: 20px;
  border-radius: 10px;
  background: ${e=>e.primary||"#61d345"};
  position: relative;
  transform: rotate(45deg);

  animation: ${Kh} 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
  animation-delay: 100ms;
  &:after {
    content: '';
    box-sizing: border-box;
    animation: ${qh} 0.2s ease-out forwards;
    opacity: 0;
    animation-delay: 200ms;
    position: absolute;
    border-right: 2px solid;
    border-bottom: 2px solid;
    border-color: ${e=>e.secondary||"#fff"};
    bottom: 6px;
    left: 6px;
    height: 10px;
    width: 6px;
  }
`,jh=Vt("div")`
  position: absolute;
`,Gh=Vt("div")`
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  min-width: 20px;
  min-height: 20px;
`,Qh=Ft`
from {
  transform: scale(0.6);
  opacity: 0.4;
}
to {
  transform: scale(1);
  opacity: 1;
}`,Jh=Vt("div")`
  position: relative;
  transform: scale(0.6);
  opacity: 0.4;
  min-width: 20px;
  animation: ${Qh} 0.3s 0.12s cubic-bezier(0.175, 0.885, 0.32, 1.275)
    forwards;
`,Yh=({toast:e})=>{let{icon:t,type:o,iconTheme:r}=e;return t!==void 0?typeof t=="string"?Ar.createElement(Jh,null,t):t:o==="blank"?null:Ar.createElement(Gh,null,Ar.createElement(Wh,{...r}),o!=="loading"&&Ar.createElement(jh,null,o==="error"?Ar.createElement(Vh,{...r}):Ar.createElement(Rh,{...r})))},Xh=e=>`
0% {transform: translate3d(0,${e*-200}%,0) scale(.6); opacity:.5;}
100% {transform: translate3d(0,0,0) scale(1); opacity:1;}
`,Zh=e=>`
0% {transform: translate3d(0,0,-1px) scale(1); opacity:1;}
100% {transform: translate3d(0,${e*-150}%,-1px) scale(.6); opacity:0;}
`,e1="0%{opacity:0;} 100%{opacity:1;}",t1="0%{opacity:1;} 100%{opacity:0;}",o1=Vt("div")`
  display: flex;
  align-items: center;
  background: #fff;
  color: #363636;
  line-height: 1.3;
  will-change: transform;
  box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1), 0 3px 3px rgba(0, 0, 0, 0.05);
  max-width: 350px;
  pointer-events: auto;
  padding: 8px 10px;
  border-radius: 8px;
`,r1=Vt("div")`
  display: flex;
  justify-content: center;
  margin: 4px 10px;
  color: inherit;
  flex: 1 1 auto;
  white-space: pre-line;
`,n1=(e,t)=>{let o=e.includes("top")?1:-1,[r,n]=uf()?[e1,t1]:[Xh(o),Zh(o)];return{animation:t?`${Ft(r)} 0.35s cubic-bezier(.21,1.02,.73,1) forwards`:`${Ft(n)} 0.4s forwards cubic-bezier(.06,.71,.55,1)`}},i1=$o.memo(({toast:e,position:t,style:o,children:r})=>{let n=e.height?n1(e.position||t||"top-center",e.visible):{opacity:0},i=$o.createElement(Yh,{toast:e}),a=$o.createElement(r1,{...e.ariaProps},Ca(e.message,e));return $o.createElement(o1,{className:e.className,style:{...n,...o,...e.style}},typeof r=="function"?r({icon:i,message:a}):$o.createElement($o.Fragment,null,i,a))});cf(lr.createElement);var a1=({id:e,className:t,style:o,onHeightUpdate:r,children:n})=>{let i=lr.useCallback(a=>{if(a){let s=()=>{let l=a.getBoundingClientRect().height;r(e,l)};s(),new MutationObserver(s).observe(a,{subtree:!0,childList:!0,characterData:!0})}},[e,r]);return lr.createElement("div",{ref:i,className:t,style:o},n)},s1=(e,t)=>{let o=e.includes("top"),r=o?{top:0}:{bottom:0},n=e.includes("center")?{justifyContent:"center"}:e.includes("right")?{justifyContent:"flex-end"}:{};return{left:0,right:0,display:"flex",position:"absolute",transition:uf()?void 0:"all 230ms cubic-bezier(.21,1.02,.73,1)",transform:`translateY(${t*(o?1:-1)}px)`,...r,...n}},l1=Hn`
  z-index: 9999;
  > * {
    pointer-events: auto;
  }
`,xa=16,O4=({reverseOrder:e,position:t="top-center",toastOptions:o,gutter:r,children:n,containerStyle:i,containerClassName:a})=>{let{toasts:s,handlers:l}=_h(o);return lr.createElement("div",{style:{position:"fixed",zIndex:9999,top:xa,left:xa,right:xa,bottom:xa,pointerEvents:"none",...i},className:a,onMouseEnter:l.startPause,onMouseLeave:l.endPause},s.map(u=>{let p=u.position||t,f=l.calculateOffset(u,{reverseOrder:e,gutter:r,defaultPosition:t}),m=s1(p,f);return lr.createElement(a1,{id:u.id,key:u.id,onHeightUpdate:l.updateHeight,className:u.visible?l1:"",style:m},u.type==="custom"?Ca(u.message,u):n?n(u):lr.createElement(i1,{toast:u,position:p}))}))},_4=De;c();d();var Pa=I(D()),pf=({text:e,copied:t})=>Pa.default.createElement(E,{direction:"row",alignItems:"center",justifyContent:"flex-end",gap:4,minWidth:96},Pa.default.createElement(H,{font:"caption",color:"textSecondary"},e),t&&Pa.default.createElement(Ie.Check,{name:"check",size:12,color:"textSecondary"}));c();d();var qn=I(D());c();d();var Yl=I(D()),mf=Yl.default.memo(({networkID:e,...t})=>{let r=`Network${Ye.getChainName(e)}`,n=Ie[r];return n?Yl.default.createElement(n,{...t}):null});var ff=({networkID:e,address:t,variant:o="transparentBackground"})=>{let r=Jc(),n=Qc(e,t,r);return qn.default.createElement(E,{gap:8,alignItems:"center",direction:"row"},o==="whiteBackground"?qn.default.createElement(Zu,{networkID:e,size:18}):qn.default.createElement(mf,{networkID:e,size:18,color:"white"}),qn.default.createElement(H,{font:"caption",color:"textPrimary"},n))};var c1=j({display:"flex",alignItems:"center",justifyContent:"center",cursor:"pointer"}),gf=()=>qe.default.createElement(E,{height:"100%"},qe.default.createElement(d1,null),qe.default.createElement(u1,null),qe.default.createElement(p1,null)),d1=()=>{let{t:e}=$(),{handleHideModalVisibility:t}=de();return qe.default.createElement(E,{direction:"row",alignItems:"center",padding:16,borderBottomWidth:1,borderBottomColor:"borderSecondary",borderBottomStyle:"solid"},qe.default.createElement("div",{className:c1,onClick:()=>t("additionalNetworks")},qe.default.createElement(Ie.X,{size:16})),qe.default.createElement(E,{justifyContent:"center",direction:"row",flex:1},qe.default.createElement(H,{font:"title1"},e("additionalNetworksTitle"))))},u1=()=>{let{t:e}=$(),{data:t}=ut(),[o,r]=(0,qe.useState)(null),[n,i]=(0,qe.useState)(null),a=bs(t?.addresses),s=l=>{n&&clearTimeout(n),r(l);let u=setTimeout(()=>r(null),3e3);i(u)};return qe.default.createElement(E,{paddingTop:16,paddingLeft:16,paddingRight:16,flex:1,overflow:"auto",gap:8},qe.default.createElement(H,{font:"body",color:"textPrimary"},e("copyAddressRowAdditionalNetworksHeader")),qe.default.createElement(pt,{rows:a.map(l=>({onClick:()=>s(l),"data-testid":`account-header-chain-${l.networkID}`,topLeft:qe.default.createElement(ff,{networkID:l.networkID,address:l.address,variant:"whiteBackground"}),bottomRight:qe.default.createElement(pf,{text:o===l?e("pastParticipleCopied"):hr(l.address,4),copied:o===l})}))}),qe.default.createElement(H,{font:"body",color:"textPrimary"},e("copyAddressRowAdditionalNetworksDescription")))},p1=()=>{let{t:e}=$(),{handleHideModalVisibility:t}=de(),{data:o}=ut(),n=bs(o?.addresses)[0],i=()=>{n&&(Hi(n.address),t("additionalNetworks"),De.success(e("pastParticipleCopied")))};return qe.default.createElement(E,{padding:16},qe.default.createElement(qr,{direction:"row"},qe.default.createElement(Yo,{onClick:()=>t("additionalNetworks"),theme:"secondary"},e("commandClose")),qe.default.createElement(Yo,{onClick:i,theme:"primary"},e("commandCopy"))))};c();d();var ke=I(D());c();d();var He=I(D());c();d();var zo=I(Dc());var C3=async()=>{let e=Do("hasOnboarded"),t=await Eo(zo.default.runtime,e);if("error"in t)throw new Error(t.error.message);return t.result},yf=async()=>{let e=Do("resetHasOnboarded"),t=await Eo(zo.default.runtime,e);if("error"in t)throw new Error(t.error.message);return t.result},T3=async e=>{let t=Do("unlockExtension",e),o=await oe.startSpan({name:"unlockExtension"},()=>Eo(zo.default.runtime,t));if("error"in o)throw new Error(o.error.message);if(!o.result)throw new Error("Missing unlock extension response.");let{isUnlocked:r,migrationResult:n}=o.result;if(r===void 0)throw new Error("Missing isUnlocked in unlock response.");if(n===void 0)throw new Error("Missing migrationResult in unlock response.");switch(n){case"success":await Wr.invalidateQueries({refetchType:"all"});break;case"no-migration":case"failure":break;default:_c(n)}return{isUnlocked:r,migrationResult:n}},P3=async()=>{let e=Do("IsExtensionUnlocked"),t=await oe.startSpan({name:"isExtensionUnlocked"},()=>Eo(zo.default.runtime,e));if("error"in t)throw new Error(t.error.message);return t.result},k3=async()=>{let e=Do("lockExtension"),t=await oe.startSpan({name:"lockExtension"},()=>Eo(zo.default.runtime,e));if("error"in t)throw new Error(t.error.message);return t.result},I3=async e=>{let t=Do("setPassword",e),o=await oe.startSpan({name:"setPassword"},()=>Eo(zo.default.runtime,t));if("error"in o)throw new Error(o.error.message);return o.result},ka=async e=>{let t=Do("verifyPassword",e),o=await oe.startSpan({name:"verifyPassword"},()=>Eo(zo.default.runtime,t));if("error"in o)throw new Error(o.error.message);return o.result},hf=async e=>{let t=Do("updatePassword",e),o=await oe.startSpan({name:"updatePassword"},()=>Eo(zo.default.runtime,t));if("error"in o)throw new Error(o.error.message);return o.result};var Ia=He.default.memo(({type:e,onSuccess:t})=>{let{t:o}=$(),{popDetailView:r}=ve(),{formState:{errors:n},register:i,handleSubmit:a,setError:s}=sd(),[l,u]=He.default.useState(!1),p=e==="privateKey",f=e==="seedless",m=o(p?"exportSecretShowPrivateKey":f?"exportSecretResetPin":"exportSecretShowSecretRecoveryPhrase"),g=o(p?"exportSecretPrivateKey":f?"exportSecretPIN":"exportSecretSecretPhrase"),y=(0,He.useCallback)(async({password:h})=>{try{if(!await ka(h))throw new Error(bf);l&&t(At.from(Buffer.from(h)))}catch(w){w.message&&s("password",{message:w.message.includes(bf)?o("exportSecretErrorIncorrectPassword"):o("exportSecretErrorGeneric")})}},[l,t,s,o]);return He.default.createElement(E,{height:"100%"},He.default.createElement(Jr,{onSubmit:a(y)},He.default.createElement(nt,{shouldWrap:!0},m),He.default.createElement(oo,{alpha:.1,color:"#EB3742",diameter:72,margin:"20px auto"},He.default.createElement(Fi,null)),f1.map(h=>He.default.createElement(m1,{key:h.text,children:h.children,text:h.text,secretNameText:g})),He.default.createElement(b1,null),He.default.createElement(y1,null,o("unlockEnterPassword")),He.default.createElement(po.WithWarning,{autoFocus:!1,placeholder:o("exportSecretPassword"),type:"password",warning:n.password,warningMessage:n.password?.message,...i("password",{required:!0})})),He.default.createElement(E,{marginTop:"auto"},He.default.createElement(x1,{onClick:()=>u(!l)},He.default.createElement(ip,{checked:l,onChange:h=>h.stopPropagation()}),He.default.createElement(h1,null,o("exportSecretWillNotShare",{secretNameText:g}))),He.default.createElement(Lt,{primaryDisabled:!l,primaryText:o("commandNext"),secondaryText:o("commandCancel"),onPrimaryClicked:a(y),onSecondaryClicked:r})))}),m1=({children:e,secretNameText:t,text:o})=>{let{t:r}=$();return He.default.createElement(Ne,{margin:"0 0 16px"},He.default.createElement(oo,{alpha:.1,color:"#EB3742",diameter:40,margin:"0 12px 0 0"},e),He.default.createElement(g1,null,r(o,{secretNameText:t})))},f1=[{children:He.default.createElement(yu,null),text:"exportSecretOnlyWay"},{children:He.default.createElement(hu,null),text:"exportSecretDoNotShow"}],bf="Incorrect password",Xl={weight:400,lineHeight:19,color:"#999",textAlign:"left"},g1=x(W).attrs({...Xl,size:14})``,y1=x(W).attrs({...Xl,size:16,margin:"0 0 8px"})``,h1=x(W).attrs({...Xl,size:16,margin:"0 0 0 10px"})``,b1=x.div`
  position: relative;
  width: 100%;
  margin: 4px 0 20px;
  &:after {
    content: "";
    position: absolute;
    height: 1px;
    width: calc(100% + 32px); // padding hack
    background: #333;
    left: -16px;
    right: -16px;
  }
`,x1=x(Ne).attrs({align:"center",margin:"0 0 16px"})`
  cursor: pointer;
`;c();d();var at=I(D());c();d();var Uo={content:"_9vsidt1 _51gazn1x _51gazn37 _51gazn129 _51gazn18p _51gazn5r",w100:"_51gazn129",pinInputContainer:"_51gazn1c4 _51gazn1b4 _51gazngc",pinInput:"_9vsidt5 _51gazn34b _51gazn1lb _51gazn2 _51gazn129",buttonWrapper:"_9vsidt6",textButton:"t8qixv0 t8qixv1 t8qixv6 t8qixve _51gazn5e _51gazn44 _51gazn6o _51gazn2u _51gazn18w _51gazn1ar _51gazn1b4 _51gazn129 _51gazn46a _51gazn33h _51gazn2zc",flexNone:"_51gazn1dm",table:"_51gazn19w _51gazngc _51gazn15o",toastPosition:"_9vsidta",errorTextContainer:"_51gazn1c4 _51gazn1ar",errorTextSeparation:"_51gazn98",navigationHeader:"_51gazn1ar _51gazn1c4 _51gazn1b8 _51gazn129"};var v1=(0,at.memo)(({pin:e,setPin:t,onReset:o,disabled:r,confirmButtonText:n,title:i,description:a})=>at.default.createElement(E,{flex:1},at.default.createElement(nt,null,i),at.default.createElement(E,{marginY:28,gap:8},at.default.createElement(H,{align:"center",font:"body",color:"textSecondary",children:a})),at.default.createElement(E,{className:Uo.pinInputContainer},at.default.createElement(po,{className:Uo.pinInput,value:e,onChange:t,maxLength:4,placeholder:"\u2022\u2022\u2022\u2022",color:Et.colors.legacy.textPrimary,type:"password",inputMode:"numeric",borderRadius:"16px"})),at.default.createElement(E,{marginTop:"auto"},at.default.createElement(qr,null,at.default.createElement(Yo,{theme:"primary",children:n,onClick:o,disabled:r}))))),Aa=({seedlessMetas:e,password:t,onVerifyCallback:o})=>{let{t:r}=$(),{popDetailView:n}=ve(),i=(0,at.useCallback)(()=>{n(),n(),o&&o()},[n,o]),a=tl({t:r,seedlessMetas:e,onExportEntropy:(0,at.useCallback)(async()=>await Promise.all(e.map(async function(s){let l=s.identifier;return await t.foldAsync(u=>mu(Buffer.from(u).toString("utf-8"),l))})),[e,t]),onResetCallback:i,onSuccess:(0,at.useCallback)(()=>De.success(r("seedlessResetPinSuccessText")),[r]),onError:(0,at.useCallback)(()=>De.error(r("localizedErrorUnknownError")),[r])});return at.default.createElement(v1,{...a,title:r("seedlessResetPinPrimaryText"),setPin:s=>a.setPin(s.target.value)})};var w1=(0,ke.memo)(({pin:e,isInvalidPin:t,triesLeft:o,setPin:r,onVerify:n,onVerifyCallback:i,onForgot:a,disabled:s,title:l,description:u,confirmButtonText:p,forgotButtonText:f})=>ke.default.createElement(E,{flex:1,padding:"screen"},ke.default.createElement(E,{className:Uo.navigationHeader},ke.default.createElement("a",{href:Hs,target:"_blank",rel:"noopener noreferrer"},ke.default.createElement(Ie.HelpCircle,{size:20,color:"white"})),ke.default.createElement(su,{label:"close",onClick:i,icon:"X",color:"white",backgroundColor:"bgWallet",shape:"circle"})),ke.default.createElement(E,{marginY:28,gap:8},ke.default.createElement(H,{align:"center",font:"heading3Semibold",children:l}),ke.default.createElement(H,{align:"center",font:"body",color:"textSecondary",children:u})),ke.default.createElement(E,{className:Uo.pinInputContainer},ke.default.createElement(po,{className:Uo.pinInput,value:e,onChange:m=>r(m.target.value),maxLength:4,placeholder:"\u2022\u2022\u2022\u2022",color:Et.colors.legacy.textPrimary,type:"password",inputMode:"numeric",borderRadius:"16px"})),t?ke.default.createElement(E,null,ke.default.createElement(E,{className:Uo.errorTextContainer,gap:4},ke.default.createElement(E,null,ke.default.createElement(Ie.XCircle,{size:14,color:"accentAlert"})),ke.default.createElement(H,{color:"accentAlert",font:"label",children:Lo("seedlessEnterPinInvalidPinError")})),ke.default.createElement(E,{className:Uo.errorTextContainer,gap:4},o?ke.default.createElement(H,{marginLeft:"screen",color:"accentAlert",font:"label",children:Lo("seedlessEnterPinNumTriesLeft",{numTries:o})}):null)):null,ke.default.createElement(E,{marginTop:"auto"},ke.default.createElement(qr,null,ke.default.createElement(Yo,{children:f,onClick:a}),ke.default.createElement(Yo,{children:p,onClick:n,disabled:s}))))),vf=()=>{let{handleHideModalVisibility:e}=de(),{pushDetailView:t,pushDetailViewCallback:o}=ve(),{t:r}=$(),{data:n=[]}=Qo("seedless-seeds-only"),i=(0,ke.useCallback)(()=>e("seedlessVerifyPinPage"),[e]),a=(0,ke.useCallback)(u=>{t(ke.default.createElement(E,{paddingX:"screen",paddingBottom:"screen",height:"100%"},ke.default.createElement(Aa,{password:u,seedlessMetas:n,onVerifyCallback:i})))},[t,n,i]),s=o(ke.default.createElement(E,{paddingX:"screen",paddingBottom:"screen",height:"100%"},ke.default.createElement(Ia,{type:"seedless",onSuccess:a}))),l=el({t:r,onVerifyCallback:i,onForgot:s,onSuccess:(0,ke.useCallback)(()=>De.success(r("seedlessVerifyToastSuccessText")),[r]),onError:(0,ke.useCallback)(u=>De.error(u??r("localizedErrorUnknownError")),[r])});return ke.default.createElement(w1,{...l})};c();d();var je=I(D());c();d();var Pt=I(D());c();d();var ae=I(D());var S1=74,C1=22,Zl=10,T1=S1+Zl,P1=C1+2*Zl,wf=x.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`,Cf=x.div`
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
`,Sf=x.div`
  margin-bottom: 16px;
`,k1=x.div``,I1=x.div`
  flex: 1 1 auto; // https://github.com/bvaughn/react-virtualized-auto-sizer#can-i-use-this-component-with-flexbox
`,A1=()=>ae.default.createElement(k1,null,[...Array(4)].map((e,t)=>ae.default.createElement(Vi,{key:`row-loader-${t}`,hideTextRight:!0}))),E1=ae.default.memo(({localizedError:e})=>ae.default.createElement(Cf,null,ae.default.createElement(Fn,null,e.message))),L1=ae.default.memo(({text:e})=>ae.default.createElement(Cf,null,ae.default.createElement(Fn,null,e))),D1=({title:e,iconUrl:t,button:o})=>ae.default.createElement(E,{direction:"row",paddingY:Zl,backgroundColor:"bgWallet",justifyContent:"space-between"},ae.default.createElement(E,{direction:"row"},t?ae.default.createElement(E,{marginRight:8},ae.default.createElement(fo,{src:t})):null,ae.default.createElement(H,{children:e,font:"bodyMedium"})),o?ae.default.createElement(H,{children:o.label,font:"labelSemibold",color:"textSecondary",onPress:o.onClick}):null),F1=({localizedError:e,isFetching:t,filterKey:o,initialFilterKey:r,filterLabels:n=[],alwaysShowFilters:i=!1,emptyListCopy:a="",sections:s,searchRef:l,renderItem:u,keyExtractor:p,setSearchQuery:f})=>{let[m,g]=(0,ae.useState)(""),y=(0,ae.useRef)(null),h=(0,ae.useRef)(null),{t:w}=$(),k=w("assetListSearch"),C=Yu(l),v=(0,ae.useMemo)(()=>!o||n.length===0||m===Mn?s:s.map(L=>({...L,data:L.data.filter(V=>$c(V,o)===m)})),[m,s,o,n.length]);(0,ae.useEffect)(()=>{setTimeout(()=>h.current?.focus(),200)},[]),(0,ae.useEffect)(()=>{y.current?.resetAfterIndex(0,!0),C!==l&&y.current?.scrollTo(0,0)},[v,l]),(0,ae.useEffect)(()=>{let L=y.current;return L&&L.scrollTop!==0&&(L.scrollTop=0),()=>{L?.current&&(L.current=null)}},[m]);let F=(0,ae.useCallback)(L=>{f?.(L.currentTarget.value)},[f]),z=(0,ae.useMemo)(()=>v.reduce((L,V)=>{if(V.header&&V.data.length>0){let q={data:V.header,type:"header"};L.push(q)}let _=V.data.map(q=>({data:q,type:"data"}));return L.concat(_)},[]),[v]),T=(0,ae.useMemo)(()=>o&&n.length>0?cp({data:s.flatMap(L=>L.data),activeFilterId:m,filterKey:o,filterLabels:n,alwaysShowFilters:i}):[],[m,n,o,i,s]);(0,ae.useEffect)(()=>{if(m||T.length===0)return;let L=T.find(V=>V.id===r);g(L?.id??T[0].id)},[T,r,m]);let O=T.length>2||T.length===2&&!T.find(L=>L.id===Mn),U=(0,ae.useCallback)(({index:L,style:V,data:_})=>{let q=_[L];return q?.type==="header"?ae.default.createElement("div",{key:`list-title-${q.data.title}`,style:V},ae.default.createElement(D1,{...q.data})):ae.default.createElement("div",{key:p(q.data,L),style:V},u({item:q.data,index:L}))},[p,u]),M=(0,ae.useMemo)(()=>z.length===0?()=>ae.default.createElement(L1,{text:a}):void 0,[a,z.length]),N=(0,ae.useCallback)(L=>z[L].type==="header"?P1:T1,[z]);return e?ae.default.createElement(wf,null,ae.default.createElement(E1,{localizedError:e})):ae.default.createElement(wf,null,ae.default.createElement(Sf,null,ae.default.createElement(tp,{ref:h,tabIndex:0,placeholder:k,onChange:F,maxLength:50})),O&&ae.default.createElement(Sf,null,ae.default.createElement(lp,{onPress:g,filters:T})),t?ae.default.createElement(A1,null):ae.default.createElement(I1,null,ae.default.createElement(lu,null,({height:L,width:V})=>ae.default.createElement(sp,{ref:y,innerElementType:M,height:L,itemSize:N,itemData:z,itemCount:z.length,width:V},U))))},Tf=ae.default.memo(F1);c();d();c();d();c();d();c();d();var Rn=I(D()),xo=e=>{let{data:[t]}=bt(["enable-multi-currency"]),{data:o}=ud(t),{data:r,isLoading:n}=Qd(o),i=(0,Rn.useMemo)(()=>({...e.intlNumberFormatOptions,currency:r?o:"USD"}),[e.intlNumberFormatOptions,r,o]);if(t){if(!o||n)return null;let{value:a,minimumResolutionValue:s,...l}=e,u=r?vs[o].languageCode:vs.USD.languageCode,p=r??1;return a&&(a=a*p),s&&(s=s*p),Rn.default.createElement(Gt,{...l,minimumResolutionValue:s,value:a,locale:u,intlNumberFormatOptions:i})}return Rn.default.createElement(Gt,{...e})};var ze=I(D());c();d();var Te=I(D());c();d();var xt={arrowLeft:"_1ceuka64 _1ceuka61 _51gaznqk _51gaznqx",arrowRight:"_1ceuka66 _1ceuka61 _51gaznqk _51gazntb",arrowUp:"_1ceuka68 _1ceuka61 _51gaznqk _51gaznrp",arrowUpLeft:"_1ceuka6a _1ceuka61 _51gaznqk _51gaznrl",childrenWrapper:"_51gazn18w _51gazn33h",childrenWrapperActive:"_51gazn18w _51gazn332",containerBottom:"_1ceuka6e _51gazn1a2 _51gaznqk _51gaznwk _51gaznrp",containerBottomLeft:"_1ceuka6g _51gazn1a2 _51gaznqk _51gaznwk",containerBottomRight:"_51gazn1a2 _51gaznqm _51gaznt6 _51gazn35",containerBottomRightDirect:"_51gazn1a2 _51gaznqm _51gaznt6",containerLeft:"_51gazn1a2 _51gaznqk _51gaznt1 _51gazn37",containerRight:"_51gazn1a2 _51gaznqm _51gaznr9 _51gazn1x",containerRightCenter:"_51gazn1a2 _51gaznqm _51gaznr9 _51gazn1v",containerTop:"_1ceuka6s _51gazn1a2 _51gaznqk _51gaznyw _51gazn5k",content:"_1ceuka6u _51gaznne _51gaznoc _51gaznq8 _51gaznpa _51gazn1ie _51gazn2zb",wrapper:"_51gaznql"};var M1=28,ec=16,B1=8,Pf=Te.default.memo(({alignment:e,children:t,content:o,tooltipRef:r,bottomOffset:n=0,paddingOffset:i=0,onMouseEnter:a,onMouseLeave:s,closeOnSecondClick:l=!0})=>{let u=(0,Te.useRef)(null),p=(0,Te.useRef)(null),[f,m]=(0,Te.useState)(!1),[g,y]=(0,Te.useState)(0),[h,w]=(0,Te.useState)(0),k=(0,Te.useCallback)(()=>{let T=u.current?.getBoundingClientRect();T&&(y(T.top-i),m(!0))},[i]),C=(0,Te.useCallback)(()=>{m(!1),y(0),w(0)},[]),v=(0,Te.useCallback)(()=>{f?C():k()},[C,f,k]),F=(0,Te.useCallback)(()=>{a&&a(),k()},[a,k]),z=(0,Te.useCallback)(()=>{s&&s(),C()},[C,s]);return(0,Te.useImperativeHandle)(r,()=>({close:C})),(0,Te.useEffect)(()=>{if(f&&p.current){let T=p.current.getBoundingClientRect(),O=self.innerHeight-B1-T.bottom-n;w(O<0?Math.abs(O):0)}},[f,o,g,n]),Te.default.createElement("div",{className:xt.wrapper,ref:u,onMouseEnter:F,onMouseLeave:z,"data-testid":"tooltip_interactive-wrapper"},Te.default.createElement("div",{className:f?xt.childrenWrapperActive:xt.childrenWrapper,onClick:l?v:k},t),f&&Te.default.createElement(N1,{alignment:e,top:g-h,bottomSpace:h},Te.default.createElement("div",{className:xt.content,ref:p},o)))}),N1=Te.default.memo(({alignment:e,children:t,top:o,bottomSpace:r=0})=>{switch(e){case"bottom":return Te.default.createElement("div",{className:xt.containerBottom,style:{top:o}},Te.default.createElement("div",{className:xt.arrowUp}),t);case"bottomLeft":return Te.default.createElement("div",{className:xt.containerBottomLeft,style:{top:o}},Te.default.createElement("div",{className:xt.arrowUpLeft}),t);case"bottomRight":return Te.default.createElement("div",{className:xt.containerBottomRight,style:{top:o+M1}},t);case"bottomRightDirect":return Te.default.createElement("div",{className:xt.containerBottomRightDirect,style:{top:o}},t);case"right":return Te.default.createElement("div",{className:xt.containerRight,style:{top:o}},Te.default.createElement("div",{className:xt.arrowLeft,style:{top:r+ec}}),t);case"rightCenter":return Te.default.createElement("div",{className:xt.containerRightCenter,style:{top:o}},t);case"left":return Te.default.createElement("div",{className:xt.containerLeft,style:{top:-r-ec}},Te.default.createElement("div",{className:xt.arrowRight,style:{top:r+ec}}),t);default:return null}});var O1=74,_1=x(Ne).attrs({align:"center",padding:"15px"})`
  cursor: pointer;
  background-color: #2a2a2a;
  border-radius: ${Et.radiusRow};
  height: ${O1}px;
  &:hover {
    background-color: #333;
  }
`,$1=x.figure`
  margin-right: 10px;
`,z1=x.div`
  margin-left: 10px;
  position: relative;
`,kf=x(Ne).attrs({justify:"space-between"})``,U1=x.div`
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
`,If=x(W).attrs({textAlign:"left"})`
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  word-break: break-word;
  text-overflow: ellipsis;
  word-break: break-all;
`,V1=j({marginLeft:4}),Af=ze.default.memo(e=>{let{tokenAddress:t,walletAddress:o,balance:r,chainMetadata:n,currencyValue:i,currencyChange:a,isLoadingPrices:s,logoUri:l,name:u,onClick:p,onMouseEnter:f,showCurrencyValues:m=!0,hideEmptyCurrency:g=!1,showBalance:y=!0,symbol:h,type:w,tooltip:k,spamStatus:C,visibilityOverrideValue:v,accessory:F}=e,z=t?hr(t):void 0,T=h??z??"",O=i!==void 0?ze.default.createElement(xo,{font:"captionMedium",value:i,minimumResolutionValue:.01,intlNumberFormatOptions:Cs}):g?"":"\u2013",U=a!==void 0?ze.default.createElement(xo,{signConfig:"always",color:"sentiment",font:"captionMedium",value:a,minimumResolutionValue:.01,intlNumberFormatOptions:Cs}):g?"":"-",M=y?`${Go(r)} ${T}`:T,N=`${h??(t&&hr(t))}`,L=`fungible-token-row-${Ye.getChainName(n.id).toLowerCase()}-${N}`;o&&Rc(n.id,o)&&(L+=`-${qc(n.id,o)}`);let V=wd(v,{spamStatus:C})==="POSSIBLE_SPAM";return ze.default.createElement(_1,{"data-testid":L,onMouseEnter:f,onClick:p,role:"button"},ze.default.createElement($1,null,ze.default.createElement(gt,{image:{type:"fungible",src:l,fallback:h||t},tokenType:w,chainMeta:n})),ze.default.createElement(U1,null,ze.default.createElement(kf,{margin:"0 0 2.5px 0"},ze.default.createElement(up,{networkID:n.id,walletAddress:o},ze.default.createElement(ze.default.Fragment,null,ze.default.createElement(If,{size:16,weight:600,lineHeight:19},u),V?ze.default.createElement(Fi,{className:V1,fill:Et.colors.legacy.accentWarning,height:16,width:16}):null)),m?s?ze.default.createElement(Ct,{width:"75px",height:"8px",borderRadius:"8px",backgroundColor:"#484848"}):ze.default.createElement(ze.default.Fragment,null,O):null),ze.default.createElement(kf,{margin:"2.5px 0 0 0"},ze.default.createElement(If,{size:14,lineHeight:17,color:"#777777"},M),m?s?ze.default.createElement(Ct,{width:"35px",height:"8px",borderRadius:"8px",backgroundColor:"#484848"}):ze.default.createElement(ze.default.Fragment,null,U):null)),F,k&&k.visible&&ze.default.createElement(z1,null,ze.default.createElement(Pf,{alignment:"left",index:0,content:k.content,bottomOffset:k.bottomOffset},ze.default.createElement(Tu,null))))}),Ef=(e,t)=>{let{balance:o,chain:r,name:n,symbol:i,logoUri:a,tokenAddress:s,usd:l,usd_24h_change:u,walletAddress:p,spamStatus:f,key:m}=e.data,g=n||Lo("assetDetailUnknownToken"),y=t?.get(m);return{balance:o,chainMetadata:r,currencyChange:u,currencyValue:l,logoUri:a,name:g,symbol:i,tokenAddress:s,walletAddress:p,spamStatus:f,visibilityOverrideValue:y,type:e.type}};var Lf="data.chain.id",Df=(e,t)=>`${e.type}-${t}`,W1={iconContainer:j({marginLeft:10})},K1=Pt.default.memo(({fungible:e,index:t,onFungiblePress:o,onInfoPress:r,showCurrencyValues:n,hideEmptyCurrency:i,showInfoTooltip:a,visibilityOverrides:s=new Map})=>{let l=(0,Pt.useCallback)(()=>{o(e,t)},[e,o,t]),u=(0,Pt.useMemo)(()=>Pt.default.createElement("div",{className:W1.iconContainer,onClick:r?p=>r(p,e):void 0},Pt.default.createElement(Ie.Info,{size:12})),[e,r]);return Pt.default.createElement(Af,{...Ef(e,s),onClick:l,showCurrencyValues:n,hideEmptyCurrency:i,isLoadingPrices:!1,accessory:a?u:null})}),tc=Pt.default.memo(({onFungiblePress:e,onInfoPress:t,direction:o,showCurrencyValues:r=!1,hideEmptyCurrency:n=!1,showInfoTooltip:i=!0,bottomOffset:a,...s})=>{let{data:l}=ut(),{data:u}=kd(l?.identifier??""),p=Jo(),f=(0,Pt.useCallback)(({item:h,index:w})=>Pt.default.createElement(K1,{fungible:h,index:w,visibilityOverrides:u,onFungiblePress:e,onInfoPress:t,showCurrencyValues:r,hideEmptyCurrency:n,showInfoTooltip:i,listBottomOffset:a}),[u,e,t,r,n,i,a]),m=Yc(),g=(0,Pt.useMemo)(()=>[o==="sell"?{id:Mn,label:se.t("allFilter")}:null,...m.filter(p).map(h=>({id:h,label:Ye.getChainName(h)}))].filter(Nc),[o,m,p]),y=o==="sell"?se.t("swapNoAssetsFound"):se.t("swapNoTokensFound");return s.enableLiveSearch?Pt.default.createElement(Tf,{...s,sections:s.sections??[],renderItem:f,fuseOptions:Ss,keyExtractor:Df,filterKey:Lf,filterLabels:g,alwaysShowFilters:o==="buy",emptyListCopy:y,setSearchQuery:s.setSearchQuery}):Pt.default.createElement(dp,{...s,data:s.data??[],renderItem:f,fuseOptions:Ss,keyExtractor:Df,filterKey:Lf,filterLabels:g,alwaysShowFilters:o==="buy",emptyListCopy:y,enableLiveSearch:s.enableLiveSearch})});c();d();var Ff=I(D());var q1={ugcWarning:"ugcWarning"},Mf=()=>{let{handleShowModalVisibility:e}=de();return(0,Ff.useCallback)(t=>{let o=q1[t];if(!o)throw new Error(`Select interceptor modal not found or configured for ${t}.`);e(o)},[e])};c();d();var ge=I(D());c();d();var Lr=I(D());var R1=3e3,j1=x.div`
  cursor: pointer;
`,Ea=({copyString:e,children:t,alignment:o})=>{let{t:r}=$(),[n,i,a]=ep(e),[s,l]=(0,Lr.useState)(!1),u=r(n?"pastParticipleCopied":"accountHeaderCopyToClipboard"),p=n?"#AB9FF2":"#000",f=()=>l(!0),m=()=>l(!1),g=()=>{i(),a(!0)};return(0,Lr.useEffect)(()=>{let y;return n&&(y=setTimeout(()=>{a(!1)},R1)),()=>{y&&clearTimeout(y)}},[n,a]),Lr.default.createElement(Wi,{label:u,ariaLabel:u,color:p,isVisible:s,triggerParams:{onMouseEnter:f,onMouseLeave:m},alignment:o},Lr.default.createElement(j1,{onClick:g},t))};c();d();var Hf=I(D());c();d();c();d();c();d();c();d();var oc=I(D()),La=({marginBottom:e=0})=>{let{t}=$(),{data:[o]}=bt(["enable-buy-flow-warning"]);return o?oc.default.createElement(E,{marginBottom:e},oc.default.createElement(cu,{level:"4-error"},t("buyFlowHealthWarning"))):null};c();d();var Bf=new Ps("Failed to get FiatRampProviderUrl"),rc=async(e,t,o,r,n)=>{try{let i={"x-client-platform":e,"x-client-app-version":t,"x-client-locale":o},a=await Rt.api().headers(i).bearer(n).post("/fiat_ramp/v2/onramp/provider_url",r);if(!co(a))throw Bf;return a.data}catch{throw Bf}};var fe=I(D());c();d();var nc={onClose:()=>{},context:"modal",fungible:void 0,purchaseAmount:"",purchaseType:"currency",currencySymbol:"usd",ownerPublicKey:"",price:0,minPurchaseAmount:Is,maxPurchaseAmount:ks,quickSelectDenominations:As,paymentMethod:void 0,provider:void 0,checkoutQuoteType:"recommended"},vo=yi(e=>({...nc,setState:t=>e(t)}));c();d();var Ge=I(D());c();d();var wo=I(D());c();d();var Vo=I(D());var Of=()=>Vo.default.createElement(Ct,{align:"center",width:"100%",height:"74px",backgroundColor:"#2D2D2D",borderRadius:"8px",margin:"0 0 10px 0",padding:"10px"},Vo.default.createElement(G1,null,Vo.default.createElement(Ct,{width:"44px",height:"44px",backgroundColor:"#434343",borderRadius:"50%"})),Vo.default.createElement(to,null,Vo.default.createElement(Ne,{margin:"0 0 10px",justify:"space-between"},Vo.default.createElement(Nf,{width:"120px"})),Vo.default.createElement(Ne,{justify:"space-between"},Vo.default.createElement(Nf,{width:"75px"})))),G1=x.div`
  width: 44px;
  height: 44px;
  margin-right: 15px;
`,Nf=x(Ct).attrs({height:"8px",backgroundColor:"#484848",borderRadius:"8px"})``;var Q1=20,Fa=({header:e,content:t,footer:o,isLoading:r,isEmpty:n,emptyStateText:i,context:a})=>wo.default.createElement("div",{className:Da.container,style:{padding:a==="publicFungible"?16:"initial"}},e&&wo.default.createElement("div",{className:Da.headerContainer},e),wo.default.createElement("div",{className:Da.contentContainer},r?wo.default.createElement("div",{style:{marginTop:Q1}},[1,2,3,4].map(s=>wo.default.createElement(Of,{key:s}))):n?wo.default.createElement("div",{className:Da.emptyStateContainer},wo.default.createElement(H,{children:i,font:"body",color:"textTertiary",align:"center"})):t,o&&a!=="publicFungible"&&wo.default.createElement(wo.default.Fragment,null,o))),Da={container:j({display:"flex",flexDirection:"column",height:"100%",maxHeight:"100%",minHeight:"100%",width:"100%",maxWidth:"100%",minWidth:"100%"}),headerContainer:j({paddingY:8}),contentContainer:j({display:"flex",flexDirection:"column",flex:1,overflowY:"auto",overflowX:"hidden"}),emptyStateContainer:j({display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",flex:1})};c();d();var Dr=I(D());var _f=Dr.default.memo(e=>{let{t}=$(),{name:o,logo:r,tag:n,quote:i,onClick:a,index:s}=e,l={start:Dr.default.createElement(fo,{className:j({size:44,borderRadius:"circle"}),src:r,alt:o}),topLeft:o,bottomLeft:n&&{text:n,color:s===0?"accentSuccess":"textSecondary"},onClick:()=>a(e)},u=i?{...l,topRight:i.displayDestinationAmount,bottomRight:`\u2248 ${i.displaySourceAmount}`}:{...l,end:Dr.default.createElement(E,{direction:"row",alignItems:"center",gap:4},Dr.default.createElement(H,{children:t("buyThirdPartyScreenViewQuote"),font:"caption",color:"textSecondary"}),Dr.default.createElement(Ie.ChevronRight,{color:"textSecondary",size:16}))};return Dr.default.createElement(jt,{...u})});c();d();var cr=I(D());c();d();var dn=I(D());var $f=({assets:e,name:t,onClick:o,estimatedArrival:r,description:n})=>dn.default.createElement(jt,{start:dn.default.createElement(fo,{className:j({size:44,borderRadius:"circle"}),src:e?.roundIcon,alt:t}),topLeft:t,bottomLeft:{before:dn.default.createElement(Ie.Clock,{color:"textSecondary",size:14}),text:r},end:n?dn.default.createElement(Wi,{label:n,ariaLabel:t},dn.default.createElement(Ie.Info,{color:"textSecondary",size:16})):void 0,onClick:o});var zf=()=>{let{t:e}=$(),{paymentMethods:t,moveToNextPage:o,closeModal:r,isLoading:n,context:i}=J1();return cr.default.createElement(Fa,{isLoading:n,isEmpty:t.length===0,emptyStateText:e("buyThirdPartyScreenPaymentMethodEmptyState"),header:cr.default.createElement(mt,null,e("buyThirdPartyScreenPaymentMethodTitle")),content:cr.default.createElement(E,{gap:8,marginTop:16,marginBottom:96},t.map(a=>cr.default.createElement($f,{key:a.id,onClick:()=>{o(a)},...a})),cr.default.createElement(H,{color:"textSecondary",children:e("buyThirdPartyScreenPaymentMethodFooter"),font:"caption"})),footer:cr.default.createElement(eo,null,cr.default.createElement(zt,{onClick:r},e("commandClose"))),context:i})},J1=()=>{let e=vo(),t=vo(s=>s.setState),{popDetailView:o}=ve(),{version:r}=chrome.runtime.getManifest(),{data:n,isPending:i}=Kd("web",r,se.language,{token:e.fungible?.caip19,purchaseAmount:e.purchaseAmount,purchaseType:e.purchaseType}),a=s=>{t({...e,paymentMethod:s,provider:void 0,checkoutQuoteType:"recommended"}),we.capture("fiatOnrampPaymentMethodSelected",{data:{paymentMethodId:s.id}}),o()};return{context:e.context,moveToNextPage:a,closeModal:e.onClose,isLoading:i,paymentMethods:n?.paymentMethods||[]}};var Uf=()=>{let{t:e}=$(),{pushDetailViewCallback:t}=ve(),{closeModal:o,onProviderSelected:r,isLoading:n,providers:i,paymentMethod:a,quoteTimeToExpiry:s,quoteTimeToExpiryColor:l,context:u}=Y1();return Ge.default.createElement(Fa,{isLoading:n,isEmpty:i.length===0,emptyStateText:e("buyThirdPartyScreenProvidersEmptyState"),header:Ge.default.createElement(mt,null,e("fiatRampQuotes")),content:Ge.default.createElement(Ge.default.Fragment,null,Ge.default.createElement(E,{gap:8,marginBottom:96},Ge.default.createElement(La,{marginBottom:12}),Ge.default.createElement(pt,{rows:[{type:"drawer",id:"selected-payment-method",onClick:t(Ge.default.createElement(zf,null)),start:a?.assets?.icon&&Ge.default.createElement(fo,{src:a.assets.icon,width:24,height:24}),topLeft:a?.name||e("buyThirdPartyScreenPaymentMethod")}]}),Ge.default.createElement(E,{direction:"row",justifyContent:"space-between"},Ge.default.createElement(H,{font:"bodyMedium",color:"textSecondary",children:e("buyThirdPartyScreenProviders")}),Ge.default.createElement("div",null,Ge.default.createElement(H,{font:"captionMedium",color:"textSecondary",children:`${e("fiatRampNewQuote")}:`})," ",Ge.default.createElement(H,{className:j({width:32}),font:"captionMedium",color:l,children:s}))),i.map((p,f)=>Ge.default.createElement(_f,{key:`${p.id}-${f}`,index:f,onClick:r,...p})))),footer:Ge.default.createElement(eo,null,Ge.default.createElement(zt,{onClick:o},e("commandClose"))),context:u})},Y1=()=>{let{popDetailView:e}=ve(),{handleHideModalVisibility:t}=de(),o=vo(),r=vo(F=>F.setState),{fungible:n,purchaseAmount:i,purchaseType:a,paymentMethod:s,ownerPublicKey:l,context:u}=o;if(!n)throw new Error("No fungible asset selected");let{version:p}=chrome.runtime.getManifest(),{data:f,isPending:m}=qd("web",p,se.language,{...a==="currency"?{fiatAmount:i}:{tokenAmount:i},token:n.caip19,paymentMethodId:s?.id,destinationAddress:{address:l,chainId:n.caip19.chainId,resourceType:"address"}}),g=s||f?.paymentMethod,y=f?.providers||[],h=(0,Ge.useCallback)(F=>{we.capture("fiatOnrampProviderSelected",{data:{chainId:f?.token?.chainId,providerId:F.id,paymentMethodId:g?.id,destinationAmount:F.quote?.displayDestinationAmount,sourceAmount:F.quote?.displaySourceAmount,asset:f?.token,tag:F.tag}}),r({...o,provider:F,paymentMethod:g,checkoutQuoteType:"selected"}),e()},[o,e,f?.token,g,r]),[w,k]=(0,Ge.useState)(Pi);Ju(()=>{m||y.length===0||(w===0?(k(Pi),Wr.invalidateQueries({refetchType:"active"})):k(w-1))},m?null:1e3);let C=w<10?`0:0${w}`:`0:${w}`,v=w<=10?"accentWarning":"textPrimary";return{providers:y,paymentMethod:g,onProviderSelected:h,closeModal:()=>t("onramp"),isLoading:m,quoteTimeToExpiry:C,quoteTimeToExpiryColor:v,context:u}};var Vf=()=>{let{t:e}=$(),{pushDetailView:t}=ve(),[o,r]=(0,fe.useState)(void 0),n=vo(),{quickSelectDenominations:i,minPurchaseAmount:a,maxPurchaseAmount:s,context:l,purchaseType:u,currencySymbol:p,fungible:f,onClose:m,ownerPublicKey:g,purchaseAmount:y,setState:h,paymentMethod:w,provider:k,checkoutQuoteType:C}=n,v=parseFloat(y),F=u==="currency",{name:z,symbol:T,decimals:O,caip19:U,logoURI:M}=f||{},N=U?.chainId,{data:L=""}=ut({select:Me=>Me?.name}),{data:V}=Cd({query:{data:U}}),_=V?V?.usd:void 0,q=(0,fe.useMemo)(()=>{if(o)return!1;let Me=y.length>0,Ot=v>0,so=Number.isFinite(v);return Me&&Ot&&so},[o,y,v]),Y=(0,fe.useMemo)(()=>{if(!_)return`??? ${T}`;if(F){let Me=Vr.get(N).formDisplayPrecision,Ot=kn(v,_,Me),so=Dd(Ot,Me);return y===""?`0 ${T}`:so.length+(T?.length??0)<15?`~${so} ${T}`:`~${so.substring(0,8)}... ${T}`}return y===""?"$0.00":`~${ci(ws(v,_))}`},[N,y,v,F,T,_]),[te,Z]=(0,fe.useState)(500),ne=Kc({purchaseAmount:y,purchaseType:u},te),{purchaseAmount:B,purchaseType:ue}=ne,Pe=(0,fe.useCallback)(Me=>{let Ot=parseFloat(Me),so=F?a:kn(a,_??0,ki),Cn=F?s:kn(s,_??0,ki),$r=Ot<so,ps=Ot>Cn,ni=$r?e("buyAssetScreenMinPurchasePriceInterpolated",{amount:F?`${a} ${p.toUpperCase()}`:`${so} ${T}`}):ps?e("buyAssetScreenMaxPurchasePriceInterpolated",{amount:F?`${s} ${p.toUpperCase()}`:`${Cn} ${T}`}):void 0;Z(500),h({...n,purchaseAmount:Me,checkoutQuoteType:"recommended"}),r(ni||void 0)},[n,p,s,a,h,F,T,e,_]),ye=(0,fe.useCallback)(Me=>{we.capture("fiatOnrampAmountSelected",{data:{networkId:N,chainId:Ye.getChainID(N),asset:{name:z,symbol:T,type:"fungible"},amount:Me}}),Z(100),h({...n,currencySymbol:"usd",purchaseAmount:Me,purchaseType:"currency",checkoutQuoteType:"recommended"}),r(void 0)},[n,z,N,h,T]),xe=(0,fe.useCallback)(()=>{if(!_)return;let Me=F?kn(v,_,O):ws(v,_);Z(100),h({...n,purchaseAmount:Me.toString(),purchaseType:F?"token":"currency"}),r(void 0)},[n,O,v,h,F,_]),he=(0,fe.useMemo)(()=>({...ue==="currency"?{fiatAmount:B}:{tokenAmount:B},token:U,destinationAddress:{address:g,chainId:U?.chainId,resourceType:"address"}}),[U,B,ue,g]),{version:Le}=chrome.runtime.getManifest(),_e=C==="selected",be=!_e&&q&&!!B,{data:Fe,isLoading:$e,isError:et}=Rd("web",Le,se.language,{...he,paymentMethodId:w?.id,providerId:k?.id},be),tt=_e?w:Fe?.paymentMethod,Ke=_e?k:Fe?.provider,ao=(0,fe.useCallback)(()=>{h({...n,paymentMethod:tt}),t(fe.default.createElement(Uf,null))},[n,t,tt,h]),fr=(0,fe.useMemo)(()=>be&&$e?{id:"recommended-quote-loading",topLeft:e("buyThirdPartyScreenLoadingQuote"),end:fe.default.createElement(_i,{diameter:20}),disabled:!0}:!q||!tt||!Ke||et?{type:"drawer",id:"no-quote",topLeft:e("buyThirdPartyScreenChoseQuote"),disabled:!0,onClick:()=>null}:{type:"drawer",id:"selected-quote",onClick:ao,topLeft:{text:tt?.name,before:tt?.assets?.icon?fe.default.createElement(fo,{src:tt?.assets?.icon,width:24,height:24}):void 0},topRight:{text:Ke?.name}},[ao,q,be,et,$e,tt,Ke,e]),[gr,_r]=(0,fe.useState)(!1),yr=q&&(!!Fe||!!w&&!!k),Sn=(0,fe.useMemo)(()=>({...he,paymentMethodId:tt?.id||"",providerId:Ke?.id||""}),[he,tt?.id,Ke?.id]),us=jc(),ri=async Me=>{if(yr){Me&&Me.preventDefault(),we.capture("fiatOnrampBuyClickedByUser",{data:{networkId:N,chainId:Ye.getChainID(N),asset:{name:z,symbol:T,type:"fungible"},purchaseType:u,amount:B,paymentMethod:tt?.id,provider:Ke?.id}}),h({...n,price:_??0}),_r(!0);try{let{url:Ot}=await rc("web",Le,se.language,Sn,us);await Ur({url:Ot})}catch(Ot){oe.captureError(Ot,"onRamp")}_r(!1)}};return fe.default.createElement(X1,{context:l},fe.default.createElement(Z1,null,fe.default.createElement(mt,null,e("buyAssetInterpolated",{tokenSymbol:T})),fe.default.createElement(eb,{iconUrl:M,width:94,alt:z??""}),fe.default.createElement(tb,null,fe.default.createElement(mp,{name:L,publicKey:g})),fe.default.createElement(ob,{onSubmit:ri},fe.default.createElement(ab,null,fe.default.createElement(op,{value:y,onKeyPress:Me=>!q&&Me.key==="Enter"&&Me.preventDefault(),onUserInput:Pe,placeholder:e("amount"),warning:!!o,decimalLimit:O}),fe.default.createElement(rb,null,F?"USD":T)),o?fe.default.createElement(W,{color:"#EB3742",size:16,textAlign:"left"},o):fe.default.createElement(Ne,{justify:"space-between"},fe.default.createElement(lb,null,fe.default.createElement(cb,{onClick:xe},_?fe.default.createElement(fe.default.Fragment,null,Y,_===0?null:fe.default.createElement(Iu,{width:13,height:13})):`??? ${T}`)),fe.default.createElement(nb,null,i.map(Me=>fe.default.createElement(ib,{key:Me,fontSize:12,fontWeight:600,borderRadius:"25px",theme:y===Me?"primary":"secondary",onClick:()=>ye(Me)},"$",Me)))))),fr&&fe.default.createElement("div",{className:j({marginBottom:16,width:"100%"})},fe.default.createElement(pt,{rows:[fr]})),fe.default.createElement(sb,{primaryText:e("commandBuy"),primaryTheme:q?"primary":"default",primaryDisabled:!yr||gr,onPrimaryClicked:ri,secondaryText:e("commandCancel"),onSecondaryClicked:m,primaryLoading:gr}))},X1=x(to).attrs({justify:"space-between"})`
  height: 100%;
  padding: ${e=>e.context==="publicFungible"?"16px":"0"};
`,Z1=x(to).attrs({align:"center"})`
  flex: 1;
`,eb=x(Ki)`
  margin: 20px 0;
`,tb=x(Ne)`
  cursor: not-allowed;
  padding: 12px 15px;
  background: #181818;
  border: 1px solid #2f2f2f;
  border-radius: 6px;
  box-shadow: inset 0px 0px 4px rgba(0, 0, 0, 0.25);
  margin-bottom: 10px;
  p:first-child {
    margin-right: 6px;
  }
`,ob=x.form`
  width: 100%;
`,rb=x(W).attrs({size:16,color:"#777777",lineHeight:27})`
  position: absolute;
  right: 12px;
  top: calc(50% - 27px / 2);
  display: flex;
  align-items: center;
`,nb=x.div`
  display: flex;
  flex-direction: row;
`,ib=x(zt)`
  margin: 0px 2px 0px 2px;
  padding: 4px 12px 4px 12px;
`,ab=x.div`
  position: relative;
  margin-bottom: 10px;
`,sb=x(Lt)`
  width: 100%;
`,lb=x.div`
  align-self: start;
  width: 50%;
`,cb=x(W).attrs({size:14,color:"#777777",hoverColor:"#AB9FF2"})`
  display: flex;
  align-items: center;
  &:hover {
    svg {
      fill: #ab9ff2;
    }
  }
  svg {
    fill: #777777;
    margin-left: 4px;
  }
`;var Ma=(e,t,o)=>{let{setState:r}=vo(),{closeAllModals:n}=de(),{pushDetailView:i,popDetailView:a}=ve(),{version:s}=chrome.runtime.getManifest(),{tokenToBuy:l,destinationAddress:u,quickSelectDenominations:p}=jd("web",s,se.language,e),f=!!l&&!!u;return{canBuy:f,openBuy:()=>{f&&(r({...nc,onClose:t==="modal"?n:a,ownerPublicKey:u,fungible:l.token,quickSelectDenominations:p,context:t}),we.capture("fiatOnrampInitiated"),o&&we.capture(o),i(Hf.default.createElement(Vf,null)))}}};c();d();var Ho=I(D());c();d();var Ht=I(D());var Kf=({stateType:e,progressPercentage:t})=>{let o=db(e,t),r=ub(e,t);return Ht.default.createElement(pb,{percentageContent:o,progress:r})},db=(e,t)=>{let o=Ht.default.createElement(H,{font:"bodySemibold",color:"textPrimary"},t,"%");switch(e){case"BondingCurve":return o;case"BondingCurveRefreshing":return Ht.default.createElement(E,{direction:"row",alignItems:"center"},Ht.default.createElement($t,{width:40,height:20,borderRadius:10}))}},ub=(e,t)=>{switch(e){case"BondingCurve":return Ht.default.createElement(fb,{progressPercentage:t});case"BondingCurveRefreshing":return Ht.default.createElement($t,{width:40,height:20,borderRadius:10})}},pb=({percentageContent:e,progress:t})=>{let{t:o}=$();return Ht.default.createElement(E,{gap:8,backgroundColor:"bgRow",borderTopRadius:"row",padding:"screen",borderColor:"bgWallet",borderBottomWidth:1,marginBottom:1},Ht.default.createElement(E,{direction:"row",gap:8,alignItems:"center",justifyContent:"space-between"},Ht.default.createElement(fp,{label:o("bondingCurveProgressLabel"),tooltipContent:o("bondingCurveInfoDescription")},Ht.default.createElement(H,{font:"captionMedium",color:"textPrimary"},e))),t)},mb=j({height:"100%",borderRadius:10,backgroundColor:"accentPrimary"}),fb=({progressPercentage:e})=>Ht.default.createElement(E,{width:"100%",height:8,borderRadius:10,backgroundColor:"bgArea"},Ht.default.createElement("div",{className:mb,style:{width:`${e}%`}}));var Ba=({caip19:e,rows:t})=>{let o=Ai(e);switch(o.type){case"BondingCurve":case"BondingCurveRefreshing":return Ho.default.createElement("div",null,Ho.default.createElement(Kf,{stateType:o.type,progressPercentage:o.progressPercentage}),Ho.default.createElement(go,{rows:t,borderRadius:`0 0 ${Et.radiusRow} ${Et.radiusRow}`}));case"Graduated":return Ho.default.createElement(gb,{rows:t,ugcProgram:o.ugcProgram});default:return Ho.default.createElement(go,{rows:t})}},gb=({rows:e,ugcProgram:t})=>{let{t:o}=$(),r=(0,Ho.useMemo)(()=>(e[0]={label:o("ugcCreatedRowLabel"),value:t.name},e[1]={label:o("ugcStatusRowLabel"),value:o("ugcStatusRowValue")},e),[e,o,t.name]);return Ho.default.createElement(go,{rows:r})};c();d();var So=I(D()),Na=({caip19:e,spamStatus:t})=>{let o=Ai(e),r=(0,So.useMemo)(()=>t?t!=="VERIFIED":!1,[t]);switch(o.type){case"BondingCurve":return o.progressPercentage<100?So.default.createElement(ac,{warning:se.t("ugcFungibleWarningBanner",{programName:o.ugcProgram?.name??""})}):r&&So.default.createElement(ac,{warning:se.t("publicFungibleUnverifiedToken")});case"NoBondingCurve":return r&&So.default.createElement(ac,{warning:se.t("publicFungibleUnverifiedToken")});default:return null}},ac=({warning:e})=>So.default.createElement(E,{direction:"row",flex:1,gap:8,backgroundColor:"bgRow",borderWidth:1,borderColor:"bgRow",borderRadius:"row",padding:16},So.default.createElement(E,{flexShrink:0},So.default.createElement(Ie.AlertTriangle,{size:18})),So.default.createElement(H,{font:"caption"},e));c();d();var Fr=I(D());c();d();var Co=new Od(we);var un=Fr.default.memo(({actions:e,shortcuts:t,hostname:o,headerText:r,maxButtons:n,uiContextName:i,disabled:a=!1})=>{let{t:s}=$(),l=e.primary.length+e.more.length+(t?.length??0),u=e.more.length>0,{handleShowModalVisibility:p,handleHideModalVisibility:f}=de(),m=(0,Fr.useCallback)((g,y)=>{Co.ctaBarTrackPrimaryButtonsClick({uiContext:{name:i},position:y,type:g.type,typeSpecificMetadata:g.typeSpecificMetadata,maxButtons:n,primaryActions:e.primary,moreActions:e.more})},[n,e.more,e.primary,i]);return Fr.default.createElement(E,{gap:8,direction:"row",width:"100%"},e.primary.map(g=>Fr.default.createElement(Ls,{key:g.type,label:g.singleWordAltText??g.text,disabled:a,icon:Ts(g.type),onClick:()=>{m(g,"primary"),g.onClick(g.type)}})),u?Fr.default.createElement(Ls,{disabled:a,label:s("commandMore"),icon:Ts("more"),onClick:()=>{Co.ctaBarTrackMoreButtonClick({uiContext:{name:i},maxButtons:n,totalButtons:l}),p("callToActionSheet",{headerText:r,actions:e,shortcuts:t,hostname:o,onClose:()=>{f("callToActionSheet")},trackAction:g=>{m(g,"more")}})}}):null)});c();d();var Yt=I(D());var hb=x.p`
  display: -webkit-box;
  -webkit-line-clamp: ${({isExpanded:e})=>e?"none":3};
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
`,Oa=({text:e})=>{let{t}=$(),[o,r]=(0,Yt.useState)(!1),[n,i]=(0,Yt.useState)(!1),a=(0,Yt.useRef)(null),s=()=>{r(!o)};return(0,Yt.useLayoutEffect)(()=>{(()=>{if(a.current){let u=a.current.scrollHeight>a.current.clientHeight;i(u)}})()},[e]),Yt.default.createElement("div",null,Yt.default.createElement(hb,{ref:a,className:sc.text,isExpanded:o,onClick:s},e),n&&Yt.default.createElement("div",{className:sc.readMoreContainer,onClick:s},Yt.default.createElement("span",{className:sc.readMoreText},t(o?"publicFungibleDetailShowLess":"publicFungibleDetailShowMore"))))},sc={text:j({font:"body",whiteSpace:"pre-line",color:"textSecondary"}),readMoreContainer:j({paddingTop:8}),readMoreText:j({color:"accentPrimary",cursor:"pointer"})};c();d();var Wt=I(D());c();d();var dr={priceContainer:"_51gazn4b _51gazn33 _51gazn5n _51gazn1t _51gazn129 _51gazn18w _51gazn1c3 _51gazn1b4 _51gazn1ar _51gazn19b",priceSubTextContainer:"_51gazn18w _51gazn1ar _51gazn1b4 _51gazn1c0 _51gazngi",priceChangePercentageContainer:"_1nbc0st4 _51gaznmq _51gaznno _51gaznpk _51gaznom _51gazn1p _51gazn2z _51gazn47 _51gazn5h _51gazn18w _51gazn1ar",priceLineChartContainer:"_1nbc0st7",timeFrameContainer:"_1nbc0st8 _51gaznbw _51gazn129 _51gazn18w _51gazngh",timeFrameButton:"_51gaznms _51gaznnq _51gaznpm _51gaznoo _51gazn18w _51gazn1b4 _51gazn1ar _51gazn1bq _51gazn1gq _51gazn33h _51gazn1k2 _51gazn1kx",timeFrameButtonActive:"_51gaznms _51gaznnq _51gaznpm _51gaznoo _51gazn18w _51gazn1b4 _51gazn1ar _51gazn1bq _51gazn1gq _51gazn33h _51gazn1k2 _51gazn1kx _51gazn1kw _51gazn1lc _51gazn34b"};var bb={left:0,right:32},jn=({caip19:e})=>{let{activeTimeFrame:t,currentPrice:o,currentPriceChange:r,formatTimeFrameText:n,formattedPercentageChange:i,isPending:a,errorText:s,overallPriceChange:l,prices:u,setActiveTimeFrame:p,setScrubValue:f,timeFrames:m}=bi({caip19:e,userLocale:navigator.language}),g=Kr(r),y=Kr(l);return Wt.default.createElement("div",null,Wt.default.createElement("div",{className:dr.priceContainer},Wt.default.createElement(Gt,{value:o,font:"heading1Semibold"}),Wt.default.createElement("div",{className:dr.priceSubTextContainer},Wt.default.createElement(Gt,{value:r,signConfig:"exceptZero",font:"title1Semibold",color:"sentiment"}),i?Wt.default.createElement("div",{className:`${dr.priceChangePercentageContainer} ${g.backgroundColor}`},Wt.default.createElement(H,{children:i,font:"captionSemibold",color:"bgWallet"})):null)),Wt.default.createElement("div",{className:dr.priceLineChartContainer},Wt.default.createElement(Li,{onMouseOverValueChange:f,interactive:!0,prices:u,formatTooltip:n,isLoading:a,errorText:s,lineColor:y.__colorValue__,xAxisPadding:bb})),Wt.default.createElement("div",{className:dr.timeFrameContainer},m.map(({timeFrame:h,label:w})=>Wt.default.createElement("div",{className:t===h?dr.timeFrameButtonActive:dr.timeFrameButton,key:h,onClick:()=>{Co.onPriceLineChartTimeFramePress({caip19:e,type:h}),p(h)}},Wt.default.createElement(H,{children:w,font:"labelBold"})))))};c();d();var yt=I(D()),xb=16,vb={discord:Ie.Discord,telegram:Ie.Telegram,website:Ie.Globe,x:Ie.XTwitter},wb={discord:"Discord",telegram:"Telegram",website:"Website",x:"X"},Sb=({link:{type:e,url:t},trackAction:o})=>{let r=vb[e],n=wb[e],i=(0,yt.useCallback)(()=>{self.open(t),o(t)},[o,t]);return yt.default.createElement("div",{className:_a.button,onClick:i},yt.default.createElement(r,{className:_a.image,size:14}),yt.default.createElement(H,{className:_a.text,color:"white",children:n}))},$a=({data:e})=>{let t=En(0),o=(0,yt.useRef)(null),[r,n]=(0,yt.useState)(0),i=(0,yt.useMemo)(()=>{let a=self.innerWidth-r-xb*2;return Math.min(0,a)},[r]);return(0,yt.useEffect)(()=>{o.current&&n(o.current.scrollWidth)},[e]),yt.default.createElement(Xe.div,{ref:o},yt.default.createElement(Xe.div,{className:_a.scrollView,style:{x:t},drag:"x",dragConstraints:{left:i,right:0}},e.links.map(a=>yt.default.createElement(Sb,{key:a.url,link:a,trackAction:e.trackAction}))))},_a={scrollView:j({display:"flex",flexDirection:"row",gap:8,overflow:"visible"}),button:j({display:"flex",flexDirection:"row",alignItems:"center",backgroundColor:{base:"bgRow",hover:"bgButton"},cursor:"pointer",borderRadius:48,paddingX:12,paddingY:6}),image:j({marginRight:4}),text:j({font:"captionSemibold",color:"white"})};c();d();var Gn=I(D());var qf=({data:e})=>{let{fungible:t,onPress:o}=e,{name:r,balance:n,symbol:i,tokenAddress:a,logoUri:s,chain:l,usd:u,usd_24h_change:p}=e.fungible.data,f=`${Go(n)} ${i}`,{t:m}=$();return Gn.default.createElement(jt,{start:Gn.default.createElement(gt,{image:{type:"fungible",src:s,fallback:i||a},tokenType:t.type,chainMeta:l}),topLeft:r??m("assetDetailUnknownToken"),bottomLeft:f,topRight:Gn.default.createElement(xo,{fontWeight:"semibold",value:u}),bottomRight:Gn.default.createElement(xo,{signConfig:"exceptZero",color:"sentiment",font:"caption",value:p}),onClick:o})};c();d();var jf=I(D());var Rf=(e,t)=>{if(e)return e;if(t)return Xt(Zt(t))},pn=()=>{let e=uo(),{closeAllModals:t}=de();return(0,jf.useCallback)(({sellFungible:r,sellFungibleCaip19:n,buyFungible:i,buyFungibleCaip19:a,sellAmount:s})=>{let l=Rf(n,r),u=Rf(a,i),p=new URLSearchParams;l&&p.append("sellFungible",l),u&&p.append("buyFungible",u),s&&p.append("sellAmount",s),t(),e(`/swap?${p.toString()}`)},[t,e])};c();d();var Ua=I(D()),lc={container:j({display:"flex",flexDirection:"column",height:"100%",maxHeight:"100%",minHeight:"100%",width:"100%",maxWidth:"100%",minWidth:"100%",overflow:"hidden"}),headerContainer:j({paddingX:16,paddingTop:16}),contentContainer:j({display:"flex",flexDirection:"column",flex:1,overflowY:"auto",overflowX:"hidden",paddingX:16,paddingBottom:"screen"})},mn=({header:e,content:t})=>Ua.default.createElement("div",{className:lc.container},Ua.default.createElement("div",{className:lc.headerContainer},e),Ua.default.createElement("div",{className:lc.contentContainer},t));c();d();var Q=I(D());c();d();var Ee=I(D());var Gf=({data:e,fetchStatus:t,currentPrice:o,currentBalance:r})=>{let{t:n}=$();switch(t){case"pending":return Ee.default.createElement(Ee.default.Fragment,null,Ee.default.createElement(E,{paddingBottom:8},Ee.default.createElement(H,{font:"bodySemibold",color:"textSecondary"},n("assetDetailHighlights"))),Ee.default.createElement(Cb,null));case"success":{if(e?.status!=="onboarded"||!o||!r||!e?.averageCost)return null;let i=ms(o).minus(ms(e.averageCost)).times(r).toString();return Ee.default.createElement(Ee.default.Fragment,null,Ee.default.createElement(E,{paddingBottom:8},Ee.default.createElement(H,{font:"bodySemibold",color:"textSecondary"},n("assetDetailHighlights"))),Ee.default.createElement(Tb,{pnl:i,averageCost:e.averageCost}))}case"error":return null}},Cb=()=>Ee.default.createElement($t,{testID:"holdings-loader",height:80,backgroundColor:"bgRow",borderRadius:"row"},Ee.default.createElement(E,{height:80,borderRadius:"row",direction:"row"},Ee.default.createElement(E,{flex:1},Ee.default.createElement(E,{justifyContent:"center",alignContent:"center",flex:1,gap:4,paddingLeft:16},Ee.default.createElement(In,{width:60,font:"caption"}),Ee.default.createElement(In,{width:100,font:"caption"}))),Ee.default.createElement(E,{width:1,backgroundColor:"bgArea"}),Ee.default.createElement(E,{flex:1},Ee.default.createElement(E,{justifyContent:"center",alignContent:"center",flex:1,gap:4,paddingLeft:16},Ee.default.createElement(In,{width:60,font:"caption"}),Ee.default.createElement(In,{width:100,font:"caption"}))))),Tb=Ee.default.memo(({pnl:e,averageCost:t})=>{let{t:o}=$();return Ee.default.createElement(E,{height:80,backgroundColor:"bgRow",borderRadius:"row",direction:"row"},Ee.default.createElement(E,{flex:1},Ee.default.createElement(E,{justifyContent:"center",alignContent:"center",flex:1,paddingLeft:16},Ee.default.createElement(Gt,{color:"sentiment",value:Number(e)}),Ee.default.createElement(H,{color:"textSecondary",font:"caption"},o("assetDetailAllTimeReturn")))),Ee.default.createElement(E,{width:1,backgroundColor:"bgArea"}),Ee.default.createElement(E,{flex:1},Ee.default.createElement(E,{justifyContent:"center",alignContent:"center",flex:1,paddingLeft:16},Ee.default.createElement(Gt,{value:Number(t)}),Ee.default.createElement(H,{color:"textSecondary",font:"caption"},o("assetDetailAverageCost")))))});c();d();var cc=I(D());var Va=()=>{let{handleShowModalVisibility:e}=de(),{trackMintModalContinue:t}=xp(),o=(0,cc.useCallback)(()=>{e("mintJitoInfo",{presentNext:!0})},[e]),r=(0,cc.useCallback)(()=>{t(),e("mintStakeAmount")},[e,t]);return ji({method:"mint",goToInfoPage:o,goToInitFlowPage:r})};c();d();var ur=I(D());var Pb=x.div`
  display: flex;
  flex-direction: column;
  background-color: ${e=>e.color};
  width: 100%;
  border-width: 1px;
  border-style: solid;
  border-color: ${e=>e.color};
  border-radius: 12px;
  gap: 8px;
`,kb=x.div`
  display: flex;
  margin: 16px;
  gap: 8px;
`,Ib=x.div`
  display: flex;
  justify-content: ${e=>e.buttonCount===1?"center":"space-between"};
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  padding: 4px 8px;
`,Ab=x.div`
  padding: 3px;
`,Eb=x.button`
  border: none;
  background: transparent;
  color: #222222;
  cursor: pointer;
  padding: 8px;
  &:nth-child(1) {
    font-weight: 500;
  }
`,Lb=x(W).attrs({size:14,weight:400,lineHeight:17,textAlign:"left",wordBreak:"break-word"})``,Ha=({message:e,inverted:t=!1,variant:o="warning",actions:r,Icon:n})=>{let i="#2A2A2A",a="#222222";switch(n??=o==="warning"&&!t?Lu:Eu,o){case"warning":i=zs;break;case"danger":i=vp;break;case"error":a=wp;break;default:i=zs}return ur.createElement(Pb,{color:i},ur.createElement(kb,null,ur.createElement(Ab,null,ur.createElement(n,{fill:a,width:18,height:18})),ur.createElement(Lb,{color:a},e)),r.length===0?null:ur.createElement(Ib,{buttonCount:r.length},r.map(s=>ur.createElement(Eb,{key:s.label,onClick:s.onClick,type:"button"},s.label))))};c();d();var gn=I(D());c();d();var Wa=I(D());c();d();var fn=I(D()),Qf=20,Jf=100,Db=5,Yf=()=>{let e=(0,fn.useRef)(null),[t,o]=(0,fn.useState)({fontSize:Jf,fontSizePrev:Qf,fontSizeMax:Jf,fontSizeMin:Qf}),{fontSize:r,fontSizeMax:n,fontSizeMin:i,fontSizePrev:a}=t;return(0,fn.useEffect)(()=>{let s=Math.abs(r-a)<=Db,l=e.current!==null&&(e.current.scrollHeight>e.current.offsetHeight||e.current.scrollWidth>e.current.offsetWidth),u=r>a;if(s){if(l){let g=a<r?a:r-(a-r);o({fontSize:g,fontSizeMax:n,fontSizeMin:i,fontSizePrev:a})}return}let p,f=n,m=i;l?(p=u?a-r:i-r,f=Math.min(n,r)):(p=u?n-r:a-r,m=Math.max(i,r)),o({fontSize:r+p/2,fontSizeMax:f,fontSizeMin:m,fontSizePrev:r})},[r,n,i,a,e]),[`${r}%`,e]};var Fb=x.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  font-size: ${e=>`${e.fontSize}px`};
  width: 100%;
`,Mb=x(W)`
  font-size: ${e=>e.fontSize};
  line-height: 120%;
  font-weight: ${e=>e.fontWeight};
`,Xf=Wa.default.memo(({children:e,className:t,maxFontSize:o,fontWeight:r=500})=>{let[n,i]=Yf();return Wa.default.createElement(Fb,{className:t,ref:i,fontSize:o},Wa.default.createElement(Mb,{fontSize:n,weight:r,noWrap:!0},e))});var Zf=gn.default.memo(e=>{let{mint:t,symbol:o,balance:r,dollarValue:n,showDollarValue:i}=e,a=t?zr(t):void 0,s=`${Go(r)} ${o??a}`;return gn.default.createElement(E,{alignItems:"center",paddingX:14,paddingY:8},gn.default.createElement(Xf,{maxFontSize:38},s),i&&gn.default.createElement(E,{marginTop:6},gn.default.createElement(H,{font:"title1Semibold",color:"textSecondary",children:n?ci(n):"\u2013"})))});c();d();var We=I(D());var Bb=[1,2],Nb=x(Ne)`
  height: 35px;
`,Ob=x(to).attrs({align:"center"})`
  background: #2a2a2a;
  padding: 16px 14px;
  border-radius: 6px;
  margin-bottom: 24px;
`,_b=x.div`
  width: 44px;
  height: 44px;
  margin-right: 10px;
`,Mr=x(Ct).attrs({height:"8px",backgroundColor:"#484848",borderRadius:"8px"})``,eg=We.default.memo(e=>We.default.createElement(We.default.Fragment,null,We.default.createElement(Nb,{justify:"center"},We.default.createElement(Mr,{width:"120px",height:"10px",margin:"0 0 7px 0"})),We.default.createElement(Ob,null,We.default.createElement(Ct,{width:"100%",height:"70px",padding:"15px 0 30px 0"},We.default.createElement(to,null,We.default.createElement(Ne,{justify:"center",margin:"0 auto"},We.default.createElement(Mr,{width:"120px",height:"10px",margin:"0 0 7px 0"})),We.default.createElement(Ne,{justify:"center",margin:"0 auto"},We.default.createElement(Mr,{width:"60px",height:"8px"})))),We.default.createElement(Lt,{primaryText:e.sendText,primaryDisabled:!0,primaryTheme:"default",secondaryText:e.depositText,secondaryDisabled:!0,secondaryTheme:"default",buttonPairStyle:"small"})),Bb.map(t=>We.default.createElement(Ct,{key:`fungible-detail-row-loader-${t}`,align:"center",width:"100%",height:"74px",backgroundColor:"#2D2D2D",borderRadius:"8px",margin:"0 0 10px 0",padding:"10px"},We.default.createElement(_b,null,We.default.createElement(Ct,{width:"44px",height:"44px",backgroundColor:"#434343",borderRadius:"50%"})),We.default.createElement(to,null,We.default.createElement(Ne,{margin:"0 0 10px",justify:"space-between"},We.default.createElement(Mr,{width:"120px"}),We.default.createElement(Mr,{width:"60px"})),We.default.createElement(Ne,{justify:"space-between"},We.default.createElement(Mr,{width:"75px"}),We.default.createElement(Mr,{width:"35px"})))))));c();d();var st=I(D());c();d();var To={container:"_51gaznmw _51gaznnu _51gaznpq _51gaznos _51gazn18w _51gazn1c3 _51gazn1kw _51gazn1lc _51gazn1gq",topContainer:"_51gazn4f _51gazn35 _51gazn5p _51gazn1v _51gazn18w _51gazn1ar _51gazn1b8 _51gaznlr _51gazn1zw _51gazn12u oef8dd2",topLeftContainer:"_51gazn18w _51gazn1ar _51gazngj",bottomContainer:"_51gazn4f _51gazn35 _51gazn5p _51gazn1v _51gazn18w _51gazn1ar _51gazngl _51gazn1b8",bottomLeftContainer:"_51gazn18w _51gazn1c3 _51gazngf _51gazn19b",bottomLeftPriceMoveContainer:"_51gazn18w _51gazn1c0 _51gazngh",chartContainer:"oef8dd9",priceContainer:"_51gazn1o _51gazn2y",textPercentContainer:"_51gaznmo _51gaznnm _51gaznpi _51gaznok _51gazn1o _51gazn2y"};var tg=({fungible:e,onPress:t})=>{let{t:o}=$(),r=Xt(Zt(e)),{prices:n,isPending:i,currentPrice:a,currentPriceChange:s,overallPriceChange:l,formattedPercentageChange:u}=bi({caip19:r}),p=Kr(s),f=Kr(l);return st.default.createElement("div",{className:To.container,onClick:t},st.default.createElement("div",{className:To.topContainer},st.default.createElement("div",{className:To.topLeftContainer},st.default.createElement(gt,{image:{type:"fungible",src:e.data.logoUri,fallback:e.data.symbol||e.data.tokenAddress},chainMeta:e.data.chain,size:24}),st.default.createElement(H,{children:e.data.name||o("assetDetailUnknownToken"),font:"caption"})),a?st.default.createElement(H,{children:o("assetDetailTimeFrame24h"),font:"caption",color:"textSecondary"}):null),st.default.createElement("div",{className:To.bottomContainer},st.default.createElement("div",{className:To.bottomLeftContainer},st.default.createElement(Gt,{value:a,font:"heading3"}),st.default.createElement("div",{className:To.bottomLeftPriceMoveContainer},s!==null?st.default.createElement("div",{className:To.priceContainer},st.default.createElement(Gt,{value:s,signConfig:"exceptZero",font:"labelMedium",color:"sentiment"})):null,u?st.default.createElement("div",{className:`${To.textPercentContainer} ${p.backgroundColor}`},st.default.createElement(H,{children:u,font:"labelSemibold",color:"bgWallet"})):null)),i||n.length>0?st.default.createElement("div",{className:To.chartContainer},st.default.createElement(Li,{interactive:!1,prices:n,pointer:!0,isLoading:i,lineColor:f.__colorValue__})):st.default.createElement(H,{color:"textSecondary"},o("assetDetailPriceDataUnavailable"))))};c();d();var Ce=I(D());c();d();var Ao=I(D());c();d();var dc=I(D());var Ka=()=>{let{handleShowModalVisibility:e}=de(),{trackConvertModalContinue:t}=yp(),o=(0,dc.useCallback)(()=>{e("convertJitoInfo")},[e]),r=(0,dc.useCallback)(()=>{t(),e("convertStakeAccounts")},[e,t]);return ji({method:"convert",goToInfoPage:o,goToInitFlowPage:r})};c();d();var ce=I(D());var og=()=>{let{t:e}=$();(0,ce.useEffect)(()=>(we.capture("showStakeAccountList"),()=>{we.capture("hideStakeAccountList")}),[]);let{data:t}=ut(),{solanaPublicKey:o,connection:r}=(0,ce.useMemo)(()=>{let s=(t?.addresses??[]).find(f=>Ye.isSolanaNetworkID(f.networkID)),l=s?.address??"",u=s?.networkID,p=fd(hi(u));return{solanaChainAddress:s,solanaPublicKey:l,connection:p}},[t]),n=qi(r,o),i=n.data??[],a=()=>{n.isFetching||n.refetch()};return ce.default.createElement(Sp,{isLoading:!n.isFetched},n.isError?ce.default.createElement(Ui,{title:e("errorAndOfflineSomethingWentWrong"),description:e("stakeAccountListErrorFetching"),refetch:a}):ce.default.createElement($b,{data:i,connection:r}),ce.default.createElement("br",null))},$b=e=>{let{t}=$(),o=Hb(e.connection),r=e.data.slice().sort((a,s)=>a.lamports>s.lamports?-1:1),n=r.filter(a=>a.type==="delegated"),i=r.filter(a=>a.type==="initialized");return!n.length&&!i.length?ce.default.createElement(zb,null,ce.default.createElement(W,{size:16,color:"#666666"},t("stakeAccountListNoStakingAccounts"))):ce.default.createElement(ce.default.Fragment,null,n.length>0&&ce.default.createElement(ce.default.Fragment,null,n.map(a=>{let{stake:s,voter:l}=a.info.stake.delegation,u=o.get(l),p=u?.info?.name,f=u?.info?.keybaseUsername,m=u?.info?.iconUrl,g=a.info.meta.rentExemptReserve;return ce.default.createElement(Ub,{key:a.pubkey,pubkey:a.pubkey,rentExemptReserve:Number(g),balance:a.lamports,delegatedStake:Number(s),inflationReward:a.inflationReward,voteAccountPubkey:l,name:p,keybaseUsername:f,iconUrl:m,connection:e.connection})})),i.length>0&&ce.default.createElement(ce.default.Fragment,null,i.map(a=>ce.default.createElement(Vb,{key:a.pubkey,pubkey:a.pubkey,balance:a.lamports,inflationReward:a.inflationReward,connection:e.connection}))))},zb=x.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: calc(100% - 42px);
`,rg=x(Yr)`
  display: grid;
  grid-template-columns: 44px auto;
  column-gap: 10px;
`,ng=x(Ki).attrs({width:44})``,ig=x.div`
  overflow: hidden;
`,qa=x.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
`,ag=x(W)`
  color: ${e=>e.activationState==="active"?"#21E56F":e.activationState==="inactive"?"#EB3742":e.activationState==="activating"||e.activationState==="deactivating"?"#FFE920":"#777777"};
`,Ub=e=>{let{t}=$(),{showStakeAccountDetailModal:o}=Dt(),{data:r}=gp(e.keybaseUsername),n=e.name??e.keybaseUsername??hr(e.voteAccountPubkey),a=$s(e.connection,e.pubkey).data,s=e.inflationReward&&e.inflationReward>0,u=a?.state==="inactive"?e.balance-e.rentExemptReserve:e.delegatedStake;return ce.default.createElement(rg,{onClick:()=>{o({stakeAccountPubkey:e.pubkey})}},ce.default.createElement(ng,{iconUrl:e.iconUrl??r}),ce.default.createElement(ig,null,ce.default.createElement(qa,null,ce.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left",noWrap:!0},n),ce.default.createElement(W,{size:14,weight:400,lineHeight:19,textAlign:"right",noWrap:!0},ce.default.createElement(tr,null,u))),ce.default.createElement(qa,null,ce.default.createElement(ag,{size:14,activationState:a?.state,lineHeight:19,textAlign:"left",textTransform:"capitalize",noWrap:!0},a?.state==="activating"?t("stakeAccountListActivationActivating"):"",a?.state==="active"?t("stakeAccountListActivationActive"):"",a?.state==="inactive"?t("stakeAccountListActivationInactive"):"",a?.state==="deactivating"?t("stakeAccountListActivationDeactivating"):""),ce.default.createElement(W,{size:14,color:`${s?"#21E56F":"#777777"}`,lineHeight:19,textAlign:"right",noWrap:!0},s?ce.default.createElement(ce.default.Fragment,null,"+",ce.default.createElement(tr,null,e.inflationReward)):"-"))))},Vb=e=>{let{t}=$(),{showStakeAccountDetailModal:o}=Dt(),n=$s(e.connection,e.pubkey).data,i=e.inflationReward&&e.inflationReward>0;return ce.default.createElement(rg,{onClick:()=>o({stakeAccountPubkey:e.pubkey})},ce.default.createElement(ng,null),ce.default.createElement(ig,null,ce.default.createElement(qa,null,ce.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left",noWrap:!0},e.pubkey),ce.default.createElement(W,{size:14,weight:400,lineHeight:19,textAlign:"right",noWrap:!0},ce.default.createElement(tr,null,e.balance))),ce.default.createElement(qa,null,ce.default.createElement(ag,{size:14,activationState:n?.state,lineHeight:19,textAlign:"left",textTransform:"capitalize",noWrap:!0},n?.state==="activating"?t("stakeAccountListActivationActivating"):"",n?.state==="active"?t("stakeAccountListActivationActive"):"",n?.state==="inactive"?t("stakeAccountListActivationInactive"):"",n?.state==="deactivating"?t("stakeAccountListActivationDeactivating"):""),ce.default.createElement(W,{size:14,color:`${i?"#21E56F":"#777777"}`,lineHeight:19,textAlign:"right",noWrap:!0},i&&"+",ce.default.createElement(tr,null,e.inflationReward)))))},Hb=e=>{let t=Gd(e),o=t.results??[],r=(0,ce.useRef)(o);return r.current=o,(0,ce.useMemo)(()=>new Map(r.current.map(i=>[i.voteAccountPubkey,i])),[t.dataUpdatedAt,r])};c();d();var ti=I(D());c();d();var G=I(D());c();d();var yn=I(D()),Ra=({partialErrorMessage:e})=>yn.default.createElement(E,{backgroundColor:"bgArea",padding:16,borderRadius:16,width:"100%",display:"flex",direction:"row",gap:8,position:"relative"},yn.default.createElement(E,null,yn.default.createElement(Ie.Info,{size:18,color:"textSecondary"})),yn.default.createElement(E,{flex:1},yn.default.createElement(H,{font:"caption",color:"textSecondary"},e)));c();d();var Nr=I(D());c();d();var Br=I(D());var sg=({fungible:e,leftButton:t})=>{let{handleShowModalVisibility:o}=de(),r=(0,Br.useCallback)(l=>o("activityItem",l),[o]),n=(0,Br.useCallback)(l=>o("pendingTransaction",l),[o]),{activityItemsData:i,isLoadingHistory:a,isDeveloperMode:s}=Ti({fungible:e});return Br.default.createElement(E,{paddingX:"screen",paddingTop:16},Br.default.createElement(ia,{leftButton:t},"Activity"),Br.default.createElement(Ir,{dataPages:i.data?.pages,isLoading:a,fetchNextPage:i.fetchNextPage,refetch:i.refetch,hasNextPage:i.hasNextPage,isFetchingNextPage:i.isFetchingNextPage,isError:i.isError,isRefreshError:i.isRefetchError,isRefreshingConfirmedTxs:!1,isDeveloperMode:s,showActivityItemModal:r,showPendingTransactionModal:n}))};c();d();var kt=I(D());var lg=({refetch:e})=>kt.default.createElement(kt.default.Fragment,null,kt.default.createElement(H,{color:"textSecondary",align:"center"},se.t("unifiedTokenDetailTransactionActivityError")),e&&kt.default.createElement(H,{align:"center",onPress:()=>e(),children:se.t("commandRetry")})),Wb=({fungible:e})=>{let{activityItemsData:t}=Ti({fungible:e}),{handleShowModalVisibility:o}=de(),r=(0,kt.useCallback)(n=>o("activityItem",{activityItem:n}),[o]);return kt.default.createElement(E,{gap:8},t.status==="error"?kt.default.createElement(lg,{refetch:t.refetch}):t.status==="success"?t.data.pages[0]?.results.slice(0,3).map((n,i)=>kt.default.createElement(E,{key:i,gap:8},kt.default.createElement(ba,{activityItem:n,onClick:()=>r(n)}))):Array.from({length:3}).map((n,i)=>kt.default.createElement(An,{key:`row-loader-${i}`})))},cg=({fungible:e})=>kt.default.createElement(fu,{fallback:kt.default.createElement(lg,null)},kt.default.createElement(Wb,{fungible:e}));var dg=({fungible:e})=>{let{pushDetailView:t,popDetailView:o}=ve();return Nr.default.createElement(E,{gap:8},Nr.default.createElement(E,{direction:"row",justifyContent:"space-between"},Nr.default.createElement(H,{font:"bodySemibold",color:"textSecondary"},se.t("unifiedTokenDetailTransactionActivity")),Nr.default.createElement(H,{color:"brandPrimary",onPress:()=>t(Nr.default.createElement(sg,{leftButton:{type:"back",onClick:o},fungible:e}))},se.t("unifiedTokenDetailSeeMoreTransactionActivity"))),Nr.default.createElement(cg,{fungible:e}))};c();d();var ug=I(D());var pg=({onMarkNotSpam:e})=>e?ug.default.createElement(Ha,{message:se.t("tokenSpamWarning"),variant:"warning",Icon:Ni,actions:[{label:se.t("commandReportAsNotSpam"),onClick:e}]}):null;c();d();var hn=I(D());var mg=({fungible:e})=>{let{name:t,balance:o,symbol:r,tokenAddress:n,logoUri:i,chain:a,usd:s,usd_24h_change:l}=e.data,u=`${Go(o)} ${r}`,{t:p}=$();return hn.default.createElement(jt,{truncate:"left",start:hn.default.createElement(gt,{image:{type:"fungible",src:i,fallback:r||n},tokenType:e.type,chainMeta:a}),topLeft:{text:t??p("assetDetailUnknownToken"),after:hn.default.createElement(pp,{networkID:a.id,address:e.data.walletAddress})},bottomLeft:u,topRight:hn.default.createElement(xo,{fontWeight:"semibold",value:s,testID:"here"}),bottomRight:hn.default.createElement(xo,{signConfig:"exceptZero",color:"sentiment",font:"caption",value:l})})};c();d();var Io=I(D());c();d();var ts=I(D());c();d();c();d();c();d();c();d();c();d();var Kb=I(D());var fg=Uc({minutes:5}),uc=(e,t)=>jo({enabled:e!==null&&t,gcTime:fg,staleTime:fg,refetchInterval:!1,refetchOnMount:!1,...Tp(e)});c();d();c();d();var hg=I(D());c();d();function qb(e,t){let o="type"in e&&e.type==="fungible"?e:null;if(!o)return!1;let r=o.platform==="all"||o.platform===t.platform,n=o.limitToTokenAddresses&&o.limitToTokenAddresses.length>0;return r&&!n?!0:r&&n?o.limitToTokenAddresses.some(i=>i===t.tokenAddress):!1}function Rb(e,t){let o=!("type"in e)||e.type==="collectible"?e:null;if(!o)return!1;let r=o.platform==="all"||o.platform===t.platform,n=o.limitToCollections&&o.limitToCollections.length>0;return r&&!n?!0:r&&n?o.limitToCollections.some(i=>i===t.collectionId):!1}var gg=(e,t)=>e?e.filter(o=>t.type==="fungible"?qb(o,t):Rb(o,t)).slice(0,20):[];c();d();var yg=({input:e,shortcuts:t})=>{if(t.length===0)return[];let o=JSON.stringify(t);return e.collectionId&&(o=o.replaceAll("{{collectionId}}",e.collectionId)),e.tokenId&&(o=o.replaceAll("{{tokenId}}",e.tokenId)),e.ownerAddress&&(o=o.replaceAll("{{ownerAddress}}",e.ownerAddress)),JSON.parse(o)};var bg=(e,t,o)=>(0,hg.useMemo)(()=>{let r=yg({input:t,shortcuts:e});return gg(r,o)},[e,t,o]);function Qn(e){return!!(e&&typeof e=="object"&&"type"in e&&e.type==="fungible")}function jb(e){return Qn(e)?Us(e.externalUrl):Us(e?.externalUrl)}function Gb(e){return Qn(e)?!1:e?.collection.isSpam??!0}function Qb(e,t){let o=jb(e),r=Cp(o)&&!!o&&!Gb(e)&&t,{data:n,isPending:i}=uc(r&&o?new URL(o):null,t),a=Qn(e)?"":Jb(e)??"",s=Qn(e)?e.tokenAddress:e?.id??"",l=Lc?"mobile":"desktop",u=Qn(e)?{platform:l,tokenAddress:s,type:"fungible"}:{platform:l,collectionId:a,type:"collectible"},p=bg(n??[],{collectionId:a,tokenId:s,ownerAddress:e?.owner},u);return{isLoading:!e||i&&r,shortcuts:r?p:[]}}var Jb=e=>{if(e?.chainData){if(Hd(e.chainData))return e.chainData.contract;if(Vd(e.chainData))return e.collection.id;if(Wd(e.chainData))return e.chainData.firstCreatedInscriptionId}};var me=I(D());c();d();c();d();var K=I(D()),Se=I(fy());c();d();var bn=I(D());function Yb(e,t){var o=(0,bn.useRef)(t);return e(function(){o.current=t}),(0,bn.useCallback)(function(){o.current&&o.current.apply(o,arguments)},[])}function xg(e){return Yb(bn.useEffect,e)}function ot(){return ot=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var o=arguments[t];for(var r in o)Object.prototype.hasOwnProperty.call(o,r)&&(e[r]=o[r])}return e},ot.apply(this,arguments)}function Ko(e,t){if(e==null)return{};var o={},r=Object.keys(e),n,i;for(i=0;i<r.length;i++)n=r[i],!(t.indexOf(n)>=0)&&(o[n]=e[n]);return o}var ja,A;(function(e){e.Idle="IDLE",e.Open="OPEN",e.Navigating="NAVIGATING",e.Dragging="DRAGGING",e.Interacting="INTERACTING"})(A||(A={}));var P;(function(e){e.ButtonMouseDown="BUTTON_MOUSE_DOWN",e.ButtonMouseUp="BUTTON_MOUSE_UP",e.Blur="BLUR",e.ClearNavSelection="CLEAR_NAV_SELECTION",e.ClearTypeahead="CLEAR_TYPEAHEAD",e.GetDerivedData="GET_DERIVED_DATA",e.KeyDownEscape="KEY_DOWN_ESCAPE",e.KeyDownEnter="KEY_DOWN_ENTER",e.KeyDownSpace="KEY_DOWN_SPACE",e.KeyDownNavigate="KEY_DOWN_NAVIGATE",e.KeyDownSearch="KEY_DOWN_SEARCH",e.KeyDownTab="KEY_DOWN_TAB",e.KeyDownShiftTab="KEY_DOWN_SHIFT_TAB",e.OptionTouchStart="OPTION_TOUCH_START",e.OptionMouseMove="OPTION_MOUSE_MOVE",e.OptionMouseEnter="OPTION_MOUSE_ENTER",e.OptionMouseDown="OPTION_MOUSE_DOWN",e.OptionMouseUp="OPTION_MOUSE_UP",e.OptionClick="OPTION_CLICK",e.ListMouseUp="LIST_MOUSE_UP",e.OptionPress="OPTION_PRESS",e.OutsideMouseDown="OUTSIDE_MOUSE_DOWN",e.OutsideMouseUp="OUTSIDE_MOUSE_UP",e.ValueChange="VALUE_CHANGE",e.PopoverPointerDown="POPOVER_POINTER_DOWN",e.PopoverPointerUp="POPOVER_POINTER_UP",e.UpdateAfterTypeahead="UPDATE_AFTER_TYPEAHEAD"})(P||(P={}));var Jn=mo({navigationValue:null}),X=mo({typeaheadQuery:null}),lt=mo({value:function(t,o){return o.value}}),Mt=mo({navigationValue:function(t,o){return o.value}}),Or=mo({navigationValue:function(t){var o=tx(t.value,t.options);if(o&&!o.disabled)return t.value;var r;return((r=t.options.find(function(n){return!n.disabled}))==null?void 0:r.value)||null}});function Ga(e,t){if(t.type===P.Blur){var o=t.refs,r=o.list,n=o.popover,i=t.relatedTarget,a=Ou(n);return!!(a?.activeElement!==r&&n&&!n.contains(i||a?.activeElement))}return!1}function pr(e,t){if(t.type===P.OutsideMouseDown||t.type===P.OutsideMouseUp){var o=t.refs,r=o.button,n=o.popover,i=t.relatedTarget;return!!(i!==r&&r&&!r.contains(i)&&n&&!n.contains(i))}return!1}function Wo(e,t){return!!e.options.find(function(o){return o.value===e.navigationValue})}function Qa(e,t){var o=t.refs,r=o.popover,n=o.list,i=t.relatedTarget;return r&&i&&r.contains(i)&&i!==n?!1:Wo(e)}function Bt(e,t){requestAnimationFrame(function(){t.refs.list&&t.refs.list.focus()})}function Ue(e,t){t.refs.button&&t.refs.button.focus()}function xn(e,t){return!t.disabled}function Po(e,t){return!(t.type===P.OptionTouchStart&&t&&t.disabled)}function ht(e,t){return"disabled"in t&&t.disabled?!1:"value"in t?t.value!=null:e.navigationValue!=null}function ct(e,t){t.callback&&t.callback(t.value)}function Xb(e,t){if(t.type===P.KeyDownEnter){var o=t.refs.hiddenInput;if(o&&o.form){var r=o.form.querySelector("button,[type='submit']");r&&r.click()}}}var Ja=mo({typeaheadQuery:function(t,o){return(t.typeaheadQuery||"")+o.query}}),Zb=mo({value:function(t,o){if(o.type===P.UpdateAfterTypeahead&&o.query){var r=Cg(t.options,o.query);if(r&&!r.disabled)return o.callback&&o.callback(r.value),r.value}return t.value}}),pc=mo({navigationValue:function(t,o){if(o.type===P.UpdateAfterTypeahead&&o.query){var r=Cg(t.options,o.query);if(r&&!r.disabled)return r.value}return t.navigationValue}}),Yn=(ja={},ja[P.GetDerivedData]={actions:mo(function(e,t){return ot({},e,t.data)})},ja[P.ValueChange]={actions:[lt,ct]},ja),ex=function(t){var o,r,n,i,a,s,l=t.value;return{id:"listbox",initial:A.Idle,context:{value:l,options:[],navigationValue:null,typeaheadQuery:null},states:(s={},s[A.Idle]={on:ot({},Yn,(o={},o[P.ButtonMouseDown]={target:A.Open,actions:[Or],cond:xn},o[P.KeyDownSpace]={target:A.Navigating,actions:[Or,Bt],cond:xn},o[P.KeyDownSearch]={target:A.Idle,actions:Ja,cond:xn},o[P.UpdateAfterTypeahead]={target:A.Idle,actions:[Zb],cond:xn},o[P.ClearTypeahead]={target:A.Idle,actions:X},o[P.KeyDownNavigate]={target:A.Navigating,actions:[Or,X,Bt],cond:xn},o[P.KeyDownEnter]={actions:[Xb],cond:xn},o))},s[A.Interacting]={entry:[Jn],on:ot({},Yn,(r={},r[P.ClearNavSelection]={actions:[Jn,Bt]},r[P.KeyDownEnter]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},r[P.KeyDownSpace]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},r[P.ButtonMouseDown]={target:A.Idle,actions:[Ue]},r[P.KeyDownEscape]={target:A.Idle,actions:[Ue]},r[P.OptionMouseDown]={target:A.Dragging},r[P.OutsideMouseDown]=[{target:A.Idle,cond:pr,actions:X},{target:A.Dragging,actions:X,cond:Wo}],r[P.OutsideMouseUp]=[{target:A.Idle,cond:pr,actions:X},{target:A.Navigating,cond:Wo},{target:A.Interacting,actions:X}],r[P.KeyDownEnter]=A.Interacting,r[P.Blur]=[{target:A.Idle,cond:Ga,actions:X},{target:A.Navigating,cond:Qa},{target:A.Interacting,actions:X}],r[P.OptionTouchStart]={target:A.Navigating,actions:[Mt,X],cond:Po},r[P.OptionClick]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},r[P.OptionPress]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},r[P.OptionMouseEnter]={target:A.Navigating,actions:[Mt,X],cond:Po},r[P.KeyDownNavigate]={target:A.Navigating,actions:[Mt,X,Bt]},r))},s[A.Open]={on:ot({},Yn,(n={},n[P.ClearNavSelection]={actions:[Jn]},n[P.KeyDownEnter]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},n[P.KeyDownSpace]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},n[P.ButtonMouseDown]={target:A.Idle,actions:[Ue]},n[P.KeyDownEscape]={target:A.Idle,actions:[Ue]},n[P.OptionMouseDown]={target:A.Dragging},n[P.OutsideMouseDown]=[{target:A.Idle,cond:pr,actions:X},{target:A.Dragging,cond:Wo},{target:A.Interacting,actions:X}],n[P.OutsideMouseUp]=[{target:A.Idle,cond:pr,actions:X},{target:A.Navigating,cond:Wo},{target:A.Interacting,actions:X}],n[P.Blur]=[{target:A.Idle,cond:Ga,actions:X},{target:A.Navigating,cond:Qa},{target:A.Interacting,actions:X}],n[P.ButtonMouseUp]={target:A.Navigating,actions:[Or,Bt]},n[P.ListMouseUp]={target:A.Navigating,actions:[Or,Bt]},n[P.OptionTouchStart]={target:A.Navigating,actions:[Mt,X],cond:Po},n[P.OptionClick]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},n[P.OptionPress]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},n[P.KeyDownNavigate]={target:A.Navigating,actions:[Mt,X,Bt]},n[P.KeyDownSearch]={target:A.Navigating,actions:Ja},n[P.UpdateAfterTypeahead]={actions:[pc]},n[P.ClearTypeahead]={actions:X},n[P.OptionMouseMove]=[{target:A.Dragging,actions:[Mt],cond:Po},{target:A.Dragging}],n))},s[A.Dragging]={on:ot({},Yn,(i={},i[P.ClearNavSelection]={actions:[Jn]},i[P.KeyDownEnter]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},i[P.KeyDownSpace]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},i[P.ButtonMouseDown]={target:A.Idle,actions:[Ue]},i[P.KeyDownEscape]={target:A.Idle,actions:[Ue]},i[P.OptionMouseDown]={target:A.Dragging},i[P.OutsideMouseDown]=[{target:A.Idle,cond:pr,actions:X},{target:A.Navigating,cond:Wo},{target:A.Interacting,actions:X}],i[P.OutsideMouseUp]=[{target:A.Idle,cond:pr,actions:X},{target:A.Navigating,cond:Wo,actions:Bt},{target:A.Interacting,actions:[X,Bt]}],i[P.Blur]=[{target:A.Idle,cond:Ga,actions:X},{target:A.Navigating,cond:Qa},{target:A.Interacting,actions:X}],i[P.ButtonMouseUp]={target:A.Navigating,actions:[Or,Bt]},i[P.OptionTouchStart]={target:A.Navigating,actions:[Mt,X],cond:Po},i[P.OptionClick]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},i[P.OptionPress]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},i[P.OptionMouseEnter]={target:A.Dragging,actions:[Mt,X],cond:Po},i[P.KeyDownNavigate]={target:A.Navigating,actions:[Mt,X,Bt]},i[P.KeyDownSearch]={target:A.Navigating,actions:Ja},i[P.UpdateAfterTypeahead]={actions:[pc]},i[P.ClearTypeahead]={actions:X},i[P.OptionMouseMove]=[{target:A.Navigating,actions:[Mt],cond:Po},{target:A.Navigating}],i[P.OptionMouseUp]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},i))},s[A.Navigating]={on:ot({},Yn,(a={},a[P.ClearNavSelection]={actions:[Jn,Bt]},a[P.KeyDownEnter]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},a[P.KeyDownSpace]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},a[P.ButtonMouseDown]={target:A.Idle,actions:[Ue]},a[P.KeyDownEscape]={target:A.Idle,actions:[Ue]},a[P.OptionMouseDown]={target:A.Dragging},a[P.OutsideMouseDown]=[{target:A.Idle,cond:pr,actions:X},{target:A.Navigating,cond:Wo},{target:A.Interacting,actions:X}],a[P.OutsideMouseUp]=[{target:A.Idle,cond:pr,actions:X},{target:A.Navigating,cond:Wo},{target:A.Interacting,actions:X}],a[P.Blur]=[{target:A.Idle,cond:Ga,actions:X},{target:A.Navigating,cond:Qa},{target:A.Interacting,actions:X}],a[P.ButtonMouseUp]={target:A.Navigating,actions:[Or,Bt]},a[P.OptionTouchStart]={target:A.Navigating,actions:[Mt,X],cond:Po},a[P.OptionClick]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},a[P.OptionPress]={target:A.Idle,actions:[lt,X,Ue,ct],cond:ht},a[P.OptionMouseEnter]={target:A.Navigating,actions:[Mt,X],cond:Po},a[P.KeyDownNavigate]={target:A.Navigating,actions:[Mt,X,Bt]},a[P.KeyDownSearch]={target:A.Navigating,actions:Ja},a[P.UpdateAfterTypeahead]={actions:[pc]},a[P.ClearTypeahead]={actions:X},a[P.OptionMouseMove]=[{target:A.Navigating,actions:[Mt],cond:Po},{target:A.Navigating}],a))},s)}};function Cg(e,t){if(t===void 0&&(t=""),!t)return null;var o=e.find(function(r){return!r.disabled&&r.label&&r.label.toLowerCase().startsWith(t.toLowerCase())});return o||null}function tx(e,t){return e?t.find(function(o){return o.value===e}):void 0}var ox=["as","aria-labelledby","aria-label","children","defaultValue","disabled","form","name","onChange","required","value","__componentName"],rx=["arrow","button","children","portal"],nx=["aria-label","arrow","as","children","onKeyDown","onMouseDown","onMouseUp"],ix=["as","children"],ax=["as","position","onBlur","onKeyDown","onMouseUp","portal","unstable_observableRefs"],sx=["as"],lx=["as","children","disabled","index","label","onClick","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseUp","onTouchStart","value"],cx=["as","label","children"],dx=["as"],ux=!1,Ya=zu("ListboxDescendantContext"),qo=Os("ListboxContext",{}),Tg=Os("ListboxGroupContext",{}),vn=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"div":r,i=t["aria-labelledby"],a=t["aria-label"],s=t.children,l=t.defaultValue,u=t.disabled,p=u===void 0?!1:u,f=t.form,m=t.name,g=t.onChange,y=t.required,h=t.value,w=t.__componentName,k=w===void 0?"ListboxInput":w,C=Ko(t,ox),v=(0,K.useRef)(h!=null),F=Vu(),z=F[0],T=F[1],O=(0,K.useRef)(null),U=(0,K.useRef)(null),M=(0,K.useRef)(null),N=(0,K.useRef)(null),L=(0,K.useRef)(null),V=(0,K.useRef)(null),_=(0,K.useRef)(null),q=np(ex({value:(v.current?h:l)||null})),Y=rp(q,{button:O,hiddenInput:U,highlightedOption:M,input:N,list:L,popover:V,selectedOption:_},ux),te=Y[0],Z=Y[1];function ne(be){be!==te.context.value&&g?.(be)}var B=Ns(C.id),ue=C.id||Gr("listbox-input",B),Pe=Rr(N,o),ye=(0,K.useMemo)(function(){var be=z.find(function(Fe){return Fe.value===te.context.value});return be?be.label:null},[z,te.context.value]),xe=mx(te.value),he={ariaLabel:a,ariaLabelledBy:i,buttonRef:O,disabled:p,highlightedOptionRef:M,isExpanded:xe,listboxId:ue,listboxValueLabel:ye,listRef:L,onValueChange:ne,popoverRef:V,selectedOptionRef:_,send:Z,state:te.value,stateData:te.context},Le=(0,K.useRef)(!1);if(!v.current&&l==null&&!Le.current&&z.length){Le.current=!0;var _e=z.find(function(be){return!be.disabled});_e&&_e.value&&Z({type:P.ValueChange,value:_e.value})}return ju(h,"value",k),fx(h,te.context.value,function(){Z({type:P.ValueChange,value:h})}),Bs(function(){Z({type:P.GetDerivedData,data:{options:z}})},[z,Z]),(0,K.useEffect)(function(){function be(Fe){var $e=Fe.target,et=Fe.relatedTarget;Sg(V.current,$e)||Z({type:P.OutsideMouseDown,relatedTarget:et||$e})}return xe&&self.addEventListener("mousedown",be),function(){self.removeEventListener("mousedown",be)}},[Z,xe]),(0,K.useEffect)(function(){function be(Fe){var $e=Fe.target,et=Fe.relatedTarget;Sg(V.current,$e)||Z({type:P.OutsideMouseUp,relatedTarget:et||$e})}return xe&&self.addEventListener("mouseup",be),function(){self.removeEventListener("mouseup",be)}},[Z,xe]),Ru("listbox"),(0,K.createElement)(n,ot({},C,{ref:Pe,"data-reach-listbox-input":"","data-state":xe?"expanded":"closed","data-value":te.context.value,id:ue}),(0,K.createElement)(qo.Provider,{value:he},(0,K.createElement)(Wu,{context:Ya,items:z,set:T},Dn(s)?s({id:ue,isExpanded:xe,value:te.context.value,selectedOptionRef:_,highlightedOptionRef:M,valueLabel:ye,expanded:xe}):s,(f||m||y)&&(0,K.createElement)("input",{ref:U,"data-reach-listbox-hidden-input":"",disabled:p,form:f,name:m,readOnly:!0,required:y,tabIndex:-1,type:"hidden",value:te.context.value||""}))))});b.NODE_ENV!=="production"&&(vn.displayName="ListboxInput",vn.propTypes={children:Se.default.oneOfType([Se.default.node,Se.default.func]),defaultValue:Se.default.string,disabled:Se.default.bool,form:Se.default.string,name:Se.default.string,onChange:Se.default.func,required:Se.default.bool,value:Se.default.string});var vg=(0,K.forwardRef)(function(t,o){var r=t.arrow,n=r===void 0?"\u25BC":r,i=t.button,a=t.children,s=t.portal,l=s===void 0?!0:s,u=Ko(t,rx);return(0,K.createElement)(vn,ot({},u,{__componentName:"Listbox",ref:o}),function(p){var f=p.value,m=p.valueLabel;return(0,K.createElement)(K.Fragment,null,(0,K.createElement)(hc,{arrow:n,children:i?Dn(i)?i({value:f,label:m}):i:void 0}),(0,K.createElement)(bc,{portal:l},(0,K.createElement)(Xn,null,a)))})});b.NODE_ENV!=="production"&&(vg.displayName="Listbox",vg.propTypes=ot({},vn.propTypes,{arrow:Se.default.oneOfType([Se.default.node,Se.default.bool]),button:Se.default.oneOfType([Se.default.func,Se.default.node]),children:Se.default.node}));var mc=(0,K.forwardRef)(function(t,o){var r=t["aria-label"],n=t.arrow,i=n===void 0?!1:n,a=t.as,s=a===void 0?"span":a,l=t.children,u=t.onKeyDown,p=t.onMouseDown,f=t.onMouseUp,m=Ko(t,nx),g=(0,K.useContext)(qo),y=g.buttonRef,h=g.send,w=g.ariaLabelledBy,k=g.disabled,C=g.isExpanded,v=g.listboxId,F=g.stateData,z=g.listboxValueLabel,T=F.value,O=Rr(y,o),U=Pg();function M(_){jr(_.nativeEvent)||(_.preventDefault(),_.stopPropagation(),h({type:P.ButtonMouseDown,disabled:k}))}function N(_){jr(_.nativeEvent)||(_.preventDefault(),_.stopPropagation(),h({type:P.ButtonMouseUp}))}var L=Gr("button",v),V=(0,K.useMemo)(function(){if(l){if(Dn(l))return l({isExpanded:C,label:z,value:T,expanded:C})}else return z;return l},[l,z,C,T]);return(0,K.createElement)(s,ot({"aria-disabled":k||void 0,"aria-expanded":C||void 0,"aria-haspopup":"listbox","aria-labelledby":r?void 0:[w,L].filter(Boolean).join(" "),"aria-label":r,role:"button",tabIndex:k?-1:0},m,{ref:O,"data-reach-listbox-button":"",id:L,onKeyDown:St(u,U),onMouseDown:St(p,M),onMouseUp:St(f,N)}),V,i&&(0,K.createElement)(px,null,Bu(i)?null:i))});b.NODE_ENV!=="production"&&(mc.displayName="ListboxButton",mc.propTypes={arrow:Se.default.oneOfType([Se.default.node,Se.default.bool]),children:Se.default.oneOfType([Se.default.node,Se.default.func])});var hc=(0,K.memo)(mc),fc=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"span":r,i=t.children,a=Ko(t,ix),s=(0,K.useContext)(qo),l=s.isExpanded;return(0,K.createElement)(n,ot({"aria-hidden":!0},a,{ref:o,"data-reach-listbox-arrow":"","data-expanded":l?"":void 0}),Dn(i)?i({isExpanded:l,expanded:l}):i||"\u25BC")});b.NODE_ENV!=="production"&&(fc.displayName="ListboxArrow",fc.propTypes={children:Se.default.oneOfType([Se.default.node,Se.default.func])});var px=(0,K.memo)(fc),gc=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"div":r,i=t.position,a=i===void 0?$u:i,s=t.onBlur,l=t.onKeyDown,u=t.onMouseUp,p=t.portal,f=p===void 0?!0:p,m=t.unstable_observableRefs,g=Ko(t,ax),y=(0,K.useContext)(qo),h=y.isExpanded,w=y.buttonRef,k=y.popoverRef,C=y.send,v=Rr(k,o),F=Pg();function z(){C({type:P.ListMouseUp})}var T=ot({hidden:!h,tabIndex:-1},g,{ref:v,"data-reach-listbox-popover":"",onMouseUp:St(u,z),onBlur:St(s,O),onKeyDown:St(l,F)});function O(U){var M=U.nativeEvent;requestAnimationFrame(function(){C({type:P.Blur,relatedTarget:M.relatedTarget||M.target})})}return f?(0,K.createElement)(_u,ot({},T,{as:n,targetRef:w,position:a,unstable_observableRefs:m})):(0,K.createElement)(n,T)});b.NODE_ENV!=="production"&&(gc.displayName="ListboxPopover",gc.propTypes={children:Se.default.node.isRequired,portal:Se.default.bool,position:Se.default.func});var bc=(0,K.memo)(gc),Xn=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"ul":r,i=Ko(t,sx),a=(0,K.useContext)(qo),s=a.listRef,l=a.ariaLabel,u=a.ariaLabelledBy,p=a.isExpanded,f=a.listboxId,m=a.stateData,g=m.value,y=m.navigationValue,h=Rr(o,s);return(0,K.createElement)(n,ot({"aria-activedescendant":kg(p?y:g),"aria-labelledby":l?void 0:u,"aria-label":l,role:"listbox",tabIndex:-1},i,{ref:h,"data-reach-listbox-list":"",id:Gr("listbox",f)}))});b.NODE_ENV!=="production"&&(Xn.displayName="ListboxList",Xn.propTypes={});var Xa=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"li":r,i=t.children,a=t.disabled,s=t.index,l=t.label,u=t.onClick,p=t.onMouseDown,f=t.onMouseEnter,m=t.onMouseLeave,g=t.onMouseMove,y=t.onMouseUp,h=t.onTouchStart,w=t.value,k=Ko(t,lx);if(b.NODE_ENV!=="production"&&!w)throw Error("A ListboxOption must have a value prop.");var C=(0,K.useContext)(qo),v=C.highlightedOptionRef,F=C.selectedOptionRef,z=C.send,T=C.isExpanded,O=C.onValueChange,U=C.state,M=C.stateData,N=M.value,L=M.navigationValue,V=(0,K.useState)(l),_=V[0],q=V[1],Y=l||_||"",te=(0,K.useRef)(null),Z=qu(te,null),ne=Z[0],B=Z[1],ue=(0,K.useMemo)(function(){return{element:ne,value:w,label:Y,disabled:!!a}},[a,ne,Y,w]);Uu(ue,Ya,s);var Pe=(0,K.useCallback)(function(Ke){!l&&Ke&&q(function(ao){return Ke.textContent&&ao!==Ke.textContent?Ke.textContent:ao||""})},[l]),ye=L?L===w:!1,xe=N===w,he=Rr(Pe,o,B,xe?F:null,ye?v:null);function Le(){z({type:P.OptionMouseEnter,value:w,disabled:!!a})}function _e(){z({type:P.OptionTouchStart,value:w,disabled:!!a})}function be(){z({type:P.ClearNavSelection})}function Fe(Ke){jr(Ke.nativeEvent)||(Ke.preventDefault(),z({type:P.OptionMouseDown}))}function $e(Ke){jr(Ke.nativeEvent)||z({type:P.OptionMouseUp,value:w,callback:O,disabled:!!a})}function et(Ke){jr(Ke.nativeEvent)||z({type:P.OptionClick,value:w,callback:O,disabled:!!a})}function tt(){(U===A.Open||L!==w)&&z({type:P.OptionMouseMove,value:w,disabled:!!a})}return(0,K.createElement)(n,ot({"aria-selected":(T?ye:xe)||void 0,"aria-disabled":a||void 0,role:"option"},k,{ref:he,id:kg(w),"data-reach-listbox-option":"","data-current-nav":ye?"":void 0,"data-current-selected":xe?"":void 0,"data-label":Y,"data-value":w,onClick:St(u,et),onMouseDown:St(p,Fe),onMouseEnter:St(f,Le),onMouseLeave:St(m,be),onMouseMove:St(g,tt),onMouseUp:St(y,$e),onTouchStart:St(h,_e)}),i)});b.NODE_ENV!=="production"&&(Xa.displayName="ListboxOption",Xa.propTypes={disabled:Se.default.bool,label:Se.default.string,value:Se.default.string.isRequired});var wg=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"div":r,i=t.label,a=t.children,s=Ko(t,cx),l=(0,K.useContext)(qo),u=l.listboxId,p=Gr("label",Ns(s.id),u);return(0,K.createElement)(Tg.Provider,{value:{labelId:p}},(0,K.createElement)(n,ot({"aria-labelledby":p,role:"group"},s,{"data-reach-listbox-group":"",ref:o}),i&&(0,K.createElement)(yc,null,i),a))});b.NODE_ENV!=="production"&&(wg.displayName="ListboxGroup",wg.propTypes={label:Se.default.oneOfType([Se.default.string,Se.default.element])});var yc=(0,K.forwardRef)(function(t,o){var r=t.as,n=r===void 0?"span":r,i=Ko(t,dx),a=(0,K.useContext)(Tg),s=a.labelId;return(0,K.createElement)(n,ot({role:"presentation"},i,{ref:o,"data-reach-listbox-group-label":"",id:s}))});b.NODE_ENV!=="production"&&(yc.displayName="ListboxGroupLabel",yc.propTypes={});function mx(e){return[A.Navigating,A.Open,A.Dragging,A.Interacting].includes(e)}function Pg(){var e=(0,K.useContext)(qo),t=e.send,o=e.disabled,r=e.onValueChange,n=e.stateData,i=n.navigationValue,a=n.typeaheadQuery,s=Hu(Ya),l=xg(r);(0,K.useEffect)(function(){a&&t({type:P.UpdateAfterTypeahead,query:a,callback:l});var f=self.setTimeout(function(){a!=null&&t({type:P.ClearTypeahead})},1e3);return function(){self.clearTimeout(f)}},[l,t,a]);var u=s.findIndex(function(f){var m=f.value;return m===i}),p=St(function(f){var m=f.key,g=Nu(m)&&m.length===1,y=s.find(function(w){return w.value===i});switch(m){case"Enter":t({type:P.KeyDownEnter,value:i,callback:r,disabled:!!(y!=null&&y.disabled||o)});return;case" ":f.preventDefault(),t({type:P.KeyDownSpace,value:i,callback:r,disabled:!!(y!=null&&y.disabled||o)});return;case"Escape":t({type:P.KeyDownEscape});return;case"Tab":var h=f.shiftKey?P.KeyDownShiftTab:P.KeyDownTab;t({type:h});return;default:g&&t({type:P.KeyDownSearch,query:m,disabled:o});return}},Ku(Ya,{currentIndex:u,orientation:"vertical",key:"index",rotate:!0,filter:function(m){return!m.disabled},callback:function(m){t({type:P.KeyDownNavigate,value:s[m].value,disabled:o})}}));return p}function kg(e){var t=(0,K.useContext)(qo),o=t.listboxId;return e?Gr("option-"+e,o):void 0}function Sg(e,t){return!!(e&&e.contains(t))}function fx(e,t,o){var r=(0,K.useRef)(e!=null),n=r.current;n&&e!==t&&o()}var ie=I(D());c();d();var Ig=I(D());var Ag=pu`
  background-color: #181818;
  padding-top: 3px;
  padding-bottom: 3px;
  border: 1px solid;
  width: 100%;
  border-color: #2f2f2f;
  border-radius: 6px;
  outline-color: transparent;
  outline-style: none;
`,Eg=x(hc)`
  ${Ag}
  height: 50px;
  cursor: pointer;
  &:hover {
    background: #000000;
    svg {
      fill: white;
    }
  }
  &[aria-disabled="true"] {
    cursor: auto;
    background-color: #181818;
  }
`,EB=x(bc)`
  padding: 0px;
  background: none;
  border: none;
  border-radius: 6px;
  &:focus {
    box-shadow: none;
  }
  &:focus-within {
    box-shadow: none;
  }
`,gx=x(({maxHeight:e,...t})=>Ig.default.createElement(Xn,{...t}))`
  ${Ag}
  display: flex;
  flex-direction: column;
  max-height: ${e=>e.maxHeight};
  overflow: auto;
  &:focus {
    box-shadow: none;
  }
  &:focus-within {
    box-shadow: none;
  }
`;gx.defaultProps={maxHeight:"180px"};var LB=x(Xa)`
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: space-between;
  padding: 12px;
  cursor: pointer;
  &:hover {
    background: #000000;
  }
  &[aria-selected="true"] {
    background: #000000;
  }
  [data-current] {
    background: #000000;
    font-weight: bold;
  }
  &:focus {
    box-shadow: none;
  }
  &:focus-within {
    box-shadow: none;
  }
`;c();d();var Ve=I(D());c();d();var Re=I(D());c();d();var xc=x.div`
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  overflow: hidden;
  background-color: ${e=>e.isPurple?"#34333f":"#2d2d2d"};
  background-image: ${e=>e.previewImage?`url(${e.previewImage})`:void 0};
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center;
`;c();d();var Zn=I(D());var Lg=Zn.default.memo(({type:e,width:t=48,fill:o="#474747"})=>{switch(e){case"video":return Zn.default.createElement(wu,{width:t,height:t,fill:o});case"audio":return Zn.default.createElement(vu,{width:t,height:t,fill:o});case"image":case"other":default:return Zn.default.createElement(Pu,{width:t,height:t,fill:o})}});c();d();var Za=I(D());var Dg=Za.default.memo(({showBadge:e=!0})=>Za.default.createElement(Ct,{aspectRatio:1,width:"100%",height:"100%",backgroundColor:"#2D2D2D",align:"flex-end",borderRadius:"6px",padding:"15px"},e?Za.default.createElement(Ct,{width:"100px",height:"10px",borderRadius:"6px",backgroundColor:"#494949"}):null));var yx=3,hx=x.img`
  transition: transform 0.5s ease;
  object-fit: cover;
  width: 100%;
  height: 100%;
  overflow: hidden;
  transform: ${e=>e.zoomState==="zoomedIn"?`scale(${yx})`:"scale(1)"};
  cursor: ${e=>{if(e.isZoomControlsEnabled){if(e.zoomState==="zoomedIn")return"zoom-out";if(e.zoomState==="zoomedOut")return"zoom-in"}else return"inherit"}};
`,Fg=Re.default.memo(e=>{let{uri:t,showSkeletonBadge:o=!1,isZoomControlsEnabled:r=!1}=e,[n,i]=(0,Re.useState)("loading"),[a,s]=(0,Re.useState)("zoomedOut"),l=(0,Re.useRef)(null),u=(0,Re.useCallback)(C=>{let v=C.nativeEvent.layerX,F=C.nativeEvent.layerY,z=C.currentTarget.offsetWidth,T=C.currentTarget.offsetHeight,O=v/z*100,U=F/T*100;l.current&&(l.current.style.transformOrigin=`${O}% ${U}%`)},[]),p=(0,Re.useCallback)(C=>{r&&(s(a==="zoomedIn"?"zoomedOut":"zoomedIn"),u(C))},[u,a,s,r]),f=(0,Re.useCallback)(C=>{a==="zoomedOut"||!r||u(C)},[u,a,r]),m=(0,Re.useCallback)(()=>{r&&(l.current&&(l.current.style.transformOrigin="center"),s("zoomedOut"))},[r]),g=(0,Re.useMemo)(()=>t!==null&&t.trim()!==""?t:null,[t]),y=(0,Re.useCallback)(()=>{i("success")},[i]),h=(0,Re.useCallback)(()=>{i("error")},[i]);return Re.default.createElement(Re.default.Fragment,null,n==="error"?Re.default.createElement(xc,null,Re.default.createElement(Lg,{type:"image"})):Re.default.createElement(xc,null,Re.default.createElement(hx,{ref:l,onMouseMove:f,onMouseLeave:m,onClick:p,src:g??"",onLoad:y,onError:h,zoomState:a,isZoomControlsEnabled:r})),n==="loading"?Re.default.createElement(Dg,{showBadge:o}):null)});var bx={xsmall:{size:10,weight:600},small:{size:12,weight:600},medium:{size:14,weight:700},large:{size:28,weight:700}},Mg={xsmall:24,small:32,medium:48,large:90},xx={xsmall:14,small:18,medium:24,large:48},vx=x.p`
  font-size: ${e=>e.size}px;
  font-weight: ${e=>e.weight};
`,wx=x.span`
  font-size: ${e=>e.emojiSize}px;
`,Sx=x(oo)(e=>`
  border: ${e.withBorder?"2px solid white":"none"};
  cursor: ${e.onClick?"pointer":"default"};
  overflow: hidden;
`),Bg=Ve.default.memo(e=>{let{accountName:t,accountIndex:o,size:r,weight:n}=e,i=cd(t,o),{textRef:a}=Ng(r,e.accountName);return Ve.default.createElement(vx,{ref:a,size:r,weight:n},i)}),Cx=Ve.default.memo(e=>{let{accountIcon:t,emojiSize:o,imageSize:r,iconSize:n,iconColor:i}=e;try{switch(t?.type??"default"){case"image":if(t?.imageUrl)return Ve.default.createElement(Fg,{uri:t.imageUrl,width:r,height:r});throw new Error("Icon type is Image, but no field: imageUrl found.");case"emoji":if(t?.unicode)return Ve.default.createElement(wx,{emojiSize:o},t.unicode);throw new Error("Icon type is Emoji, but no field: unicode found.");case"read-only-default":return Ve.default.createElement(Su,{width:o,height:o,fill:n==="large"?"#999":i});default:return Ve.default.createElement(Bg,{...e})}}catch(a){return oe.captureError(a,"account"),Ve.default.createElement(Bg,{...e})}}),es=Ve.default.memo(e=>{let{accountName:t,accountIndex:o,accountIcon:r}=e,{size:n,weight:i}=bx[e.size],{containerRef:a}=Ng(n,e.accountName),s=Tx(e.accountIcon,e.size);return Ve.default.createElement(Sx,{"data-testid":e.testID,color:s,diameter:Mg[e.size],ref:a,withBorder:e.withBorder,onClick:e.onClick},Ve.default.createElement(Cx,{accountIcon:r,accountName:t,accountIndex:o,emojiSize:xx[e.size],imageSize:Mg[e.size],size:n,weight:i,iconSize:e.size,iconColor:e.iconColor||"#000"}))}),Ng=(e,t)=>{let o=(0,Ve.useRef)(null),r=(0,Ve.useRef)(null);return(0,Ve.useEffect)(()=>{r.current&&(r.current.style.fontSize=`${e}px`),o.current&&r.current&&Og(o,r,e)},[t,e,o,r]),{containerRef:o,textRef:r}},Tx=(e,t)=>e&&e.type==="read-only-default"?t==="large"?"#181818":"#AB9FF2":"#333",Px=5,Og=(e,t,o)=>{let r=e.current?.clientWidth||0;(t.current?.clientWidth||0)>=r-Px&&t.current&&(o=o-1,t.current.style.fontSize=`${o}px`,Og(e,t,o))},lN=Ve.default.memo(e=>{let{accountName:t,accountIndex:o,accountIcon:r,onClick:n,withBorder:i}=e;return Ve.default.createElement(_g,null,Ve.default.createElement(es,{accountIndex:o,accountName:t,accountIcon:r,withBorder:i,size:"large"}),Ve.default.createElement(kx,{"data-testid":"edit-avatar-icon",onClick:n},Ve.default.createElement(xu,{fill:"#777"})))}),cN=Ve.default.memo(e=>{let{accountName:t,accountIndex:o,accountIcon:r,withBorder:n}=e;return Ve.default.createElement(_g,null,Ve.default.createElement(es,{accountIndex:o,accountName:t,accountIcon:r,withBorder:n,size:"large"}))}),dN=x(Ne).attrs({justify:"center"})`
  margin: 0 auto;
`,uN=x(Ne).attrs({justify:"center"})`
  margin: 20px auto;
`,_g=x.div`
  position: relative;
`,kx=x.div`
  position: absolute;
  bottom: -4px;
  right: -4px;

  height: 32px;
  width: 32px;

  display: flex;
  align-items: center;
  justify-content: center;

  cursor: pointer;

  border: 2px solid #222;
  border-radius: 50%;
  background: #333;

  &:hover {
    background: #ab9ff2;
    path {
      fill: #333;
    }
  }
`;var zg=({navigationCallback:e})=>{let{t}=$(),{data:o=[]}=id(),{data:r=[]}=Qo("all"),i=!(r?.length>0),a=xs(p=>p.editableAccountMetadata),s=xs(p=>p.setEditableAccountMetadata),l=(0,ie.useCallback)(p=>{s&&s({...a,seedIdentifier:p})},[a,s]),u=(0,ie.useCallback)(p=>{l(p),e(p)},[l,e]);return ie.default.createElement(ie.default.Fragment,null,ie.default.createElement(nt,{icon:ie.default.createElement(Bi,{fill:"#777"})},t("settingsSelectSecretPhrase")),ie.default.createElement(Qr,null,i?ie.default.createElement(H,{children:t("addAccountNoSecretPhrases"),font:"labelBold"}):ie.default.createElement(E,{gap:8},ie.default.createElement(Ex,null,r?.map((p,f)=>ie.default.createElement(Ix,{key:`seed-select-${p.identifier}`,accounts:Gc(o,p.identifier),seedIdentifier:p.identifier,onClick:u,index:f}))))))},Ix=({accounts:e,seedIdentifier:t,index:o,onClick:r})=>{let{t:n}=$(),[i,a]=(0,ie.useState)(!1),[s,l]=(0,ie.useState)(!1),u=(0,ie.useCallback)(()=>{a(!i)},[i,a]),p=(0,ie.useCallback)(()=>{s||r(t)},[s,r,t]),f=(0,ie.useMemo)(()=>e.length>1?n(i?"addAccountHideAccountsForSeed":"addAccountShowAccountsForSeed",{numOfAccounts:e.length}):e.length===1?n(i?"addAccountHideAccountForSeed":"addAccountShowAccountForSeed"):n("addAccountZeroAccountsForSeed"),[e.length,i,n]),m=(0,ie.useMemo)(()=>e.length>=1?{onMouseEnter:()=>l(!0),onMouseLeave:()=>l(!1),onClick:u}:null,[e.length,u]);return ie.default.createElement(Lx,{onClick:p},ie.default.createElement(Ug,null,ie.default.createElement(Dx,null,ie.default.createElement(Fx,null,ie.default.createElement(Mx,null,n("addAccountSecretPhraseDefaultLabel",{number:o+1})),ie.default.createElement(Nx,{...m},f)),ie.default.createElement(Ne,{width:"fit-content"},ie.default.createElement(Vg,null,ie.default.createElement(Ds,{fill:"#777",width:10})))),ie.default.createElement(Ax,{accounts:e,expanded:i})))},Ax=({accounts:e,expanded:t})=>{let[o,{height:r}]=Xu(),n=(0,ie.useMemo)(()=>({initial:{opacity:0,height:0},animate:{opacity:1,height:r},exit:{opacity:0,height:0},transition:{delay:0,duration:.2}}),[r]);return ie.default.createElement(Fo,{mode:"wait"},t&&e.length>0&&ie.default.createElement($g,{...n},ie.default.createElement($g,{ref:o},ie.default.createElement(Ox,null,e.map((i,a)=>ie.default.createElement(Ne,{key:`account-${i.identifier}`,justify:"flex-start"},ie.default.createElement(es,{size:"xsmall",accountIcon:i.icon,accountName:i.name,accountIndex:a}),ie.default.createElement(Bx,{style:{marginLeft:6}},i.name)))))))},Ex=x.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
  gap: 12px;
`,Lx=x(Ne).attrs({align:"center"})`
  position: relative;
  cursor: pointer;
  background-color: #2a2a2a;
  border-radius: 8px;
  &:hover {
    background-color: #333;
  }
`,Dx=x(Ne).attrs({justify:"space-between"})`
  padding: 15px;
`,Fx=x.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-start;
`,Mx=x(W).attrs({size:16,weight:500,lineHeight:19})``,Bx=x(W).attrs({size:15,weight:500,lineHeight:19})``,Nx=x(W).attrs({size:13,weight:500,lineHeight:19})`
  color: #ab9ff2;
  margin-top: 2px;
`,Ug=x.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`,Ox=x(Ug)`
  border-top: 1px solid #222222;
  padding: 15px;
  align-items: flex-start;
  gap: 12px;
`,$g=x(Xe.div)`
  overflow: hidden;
`,DN=({accounts:e,seedMetas:t,onClick:o,selectedSeedIdentifier:r})=>{let{t:n}=$(),i=(0,ie.useMemo)(()=>{if(!t||!r)return 0;let s=t.find(l=>l.identifier===r);return s?t.indexOf(s):0},[t,r]),a=(0,ie.useMemo)(()=>!e||!r?[]:e.filter(s=>(s.type==="seed"||s.type==="seedless")&&s.seedIdentifier===r),[e,r]);return ie.default.createElement(_x,null,ie.default.createElement(Vx,null,n("addAccountSecretPhraseLabel")),ie.default.createElement($x,{onClick:o,"data-testid":"select-secret-phrase-button"},ie.default.createElement(Ne,{justify:"space-between"},ie.default.createElement(Ne,null,ie.default.createElement(zx,null,n("addAccountSecretPhraseDefaultLabel",{number:i+1})," ",ie.default.createElement(Ux,{as:"span"},"(",a.length>1?n("addAccountNumAccountsForSeed",{numOfAccounts:a.length}):n("addAccountOneAccountsForSeed"),")"))),ie.default.createElement(Vg,null,ie.default.createElement(Ds,{fill:"#777",width:10})))),ie.default.createElement(Hg,null,n("addAccountSelectSeedDescription")))},_x=x(vn)`
  width: 100%;
  position: relative;
`,$x=x(Eg)`
  padding: 8px 16px 8px 12px;
`,Vg=x.div`
  svg {
    transform: rotate(270deg) translateX(2px);
  }
`,zx=x(W).attrs({size:16,weight:400,lineHeight:19})``,Ux=x(W).attrs({size:16,weight:400,lineHeight:19})`
  display: inline-flex;
  padding: 0 8px;
  color: #777777;
`,Hg=x(W).attrs({size:14,weight:400,lineHeight:19,textAlign:"left"})`
  margin-top: 8px;
  color: #777777;
`,Vx=x(Hg)`
  margin-top: 24px;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
  svg {
    margin-left: 6px;
  }
`;c();d();var ko=I(D());var ei=[0,1,5,10,15,30,60,240,480],Wg=()=>{let{t:e}=$(),{popDetailView:t}=ve(),{data:o}=mi(),{mutateAsync:r}=od(),n=(0,ko.useCallback)(async a=>{try{let s=hs(a);await r({lockTimeInMs:s}),we.capture("changeLockTimerDuration",{data:{mins:a}}),t(),o!==s&&De.success(e("changeLockTimerToast"))}catch(s){console.error(s)}},[o,t,r,e]),i=(0,ko.useMemo)(()=>{if(o===void 0)return[];let a=Vc(o);return Hx(a),ei.map(s=>({topLeft:{text:li(e,hs(s))},active:a===s,type:"check",onClick:()=>n(s)}))},[o,n,e]);return ko.default.createElement(ko.default.Fragment,null,ko.default.createElement(nt,null,e("changeLockTimerPrimaryText")),ko.default.createElement(Qr,null,ko.default.createElement(pt,{rows:i})))},Hx=async e=>{if(!ei.includes(e)){let t=ei.findIndex(o=>o>e);t?ei.splice(t,0,e):ei.push(e)}};c();d();var vt=I(D());c();d();var Kg=e=>{if(!e.length)return se.t("validationUtilsPasswordIsRequired");if(e.length<8)return se.t("validationUtilsPasswordLength")},qg=(e,t)=>{if(e&&t!==e)return`${se.t("validationUtilsPasswordsDontMatch")}`},Rg=(e,t)=>{if(t===e)return`${se.t("validationUtilsPasswordCantBeSame")}`};var Wx={currentPassword:"",password:"",confirmPassword:""},Kx={currentPassword:void 0,password:void 0,confirmPassword:void 0},jg=()=>{let{t:e}=$(),{popDetailView:t}=ve(),[o,r]=(0,vt.useState)(Wx),{currentPassword:n,password:i,confirmPassword:a}=o,[s,l]=(0,vt.useState)(Kx),u=s.password??s.confirmPassword??s.currentPassword,p=!n||!i||!a,[f,m]=(0,vt.useState)(!1),g=h=>{let w=h.currentTarget.name,k=h.currentTarget.value;r({...o,[w]:k})};return vt.default.createElement(Jr,{onSubmit:async h=>{h.preventDefault(),m(!0);let w,k=Kg(i)??Rg(n,i),C=qg(i,a);if(k||C)return l({...s,password:k,confirmPassword:C}),m(!1);try{if(!await ka(n))throw new Error("Incorrect password");await hf(a),m(!1),t(),De.success(e("changePasswordToast"))}catch(v){oe.captureError(v,"account"),w=v?.message.includes("Incorrect password")?e("changePasswordErrorIncorrectCurrentPassword"):e("changePasswordErrorGeneric"),l({...s,currentPassword:w}),m(!1)}}},vt.default.createElement(nt,null,e("changePasswordPrimaryText")),vt.default.createElement(Xo,{size:"large"},vt.default.createElement(Zo,null,vt.default.createElement(bu,null),vt.default.createElement(W,{size:26,weight:500},e("changePasswordPrimaryText")),vt.default.createElement(ap,null,vt.default.createElement(po,{type:"password",name:"currentPassword",placeholder:e("changePasswordCurrentPassword"),warning:!!s.currentPassword,onChange:g}),vt.default.createElement(po,{type:"password",name:"password",placeholder:e("changePasswordNewPassword"),warning:!!s.password,onChange:g}),vt.default.createElement(po.WithWarning,{type:"password",name:"confirmPassword",placeholder:e("changePasswordConfirmNewPassword"),warning:!!s.confirmPassword,warningMessage:u,onChange:g}))),vt.default.createElement(Lt,{primaryText:e("commandSave"),secondaryText:e("commandCancel"),onSecondaryClicked:t,primaryDisabled:p||f,primaryLoading:f})))};c();d();var Nt=I(D());var qx=x.div`
  border: 1px solid #ffdc62;
  border-radius: 6px;
  padding: 8px 16px;
`,Gg=Nt.default.memo(()=>{let{t:e}=$(),{downloadLogs:t,goSupportDesk:o,isFetching:r}=Pp();return Nt.default.createElement(Jr,{onSubmit:t},Nt.default.createElement(nt,null,e("settingsDownloadApplicationLogs")),Nt.default.createElement(Xo,{size:"medium"},Nt.default.createElement("div",null),Nt.default.createElement(Zo,null,Nt.default.createElement(oo,{diameter:96,color:dt("#AB9FF2",.1)},Nt.default.createElement(Cu,null)),Nt.default.createElement(W,{size:28,weight:500,lineHeight:34,margin:"20px 0 0",maxWidth:"280px"},e("settingsDownloadApplicationLogs")),Nt.default.createElement(W,{size:16,color:"#777777",lineHeight:19,margin:"0 0 4px"},e("settingsDownloadApplicationLogsHelper")),Nt.default.createElement(qx,null,Nt.default.createElement(W,{size:14,color:"#FFDC62",lineHeight:20,weight:500},e("settingsDownloadApplicationLogsWarning")))),Nt.default.createElement(Lt,{primaryDisabled:r,primaryText:e("commandDownload"),secondaryText:e("settingsSupportDesk"),onSecondaryClicked:o})))});c();d();var Kt=I(D());var Qg=({seedIdentifier:e})=>{let{t}=$(),{hideSettingsMenu:o}=zi(),{mutate:r}=ad(),{data:n=[]}=Qo("all"),i=n.findIndex(l=>l.identifier===e)??0,a=(0,Kt.useCallback)(()=>{r({seedIdentifier:e}),we.capture("walletSeeds",{data:{numOfSeeds:n?.length}}),o()},[o,r,e,n]),{popDetailView:s}=ve();return Kt.default.createElement(Kt.default.Fragment,null,Kt.default.createElement(nt,null,t("settingsRemoveSecretPhrase")),Kt.default.createElement(Xo,{size:"large"},Kt.default.createElement("div",null),Kt.default.createElement(Zo,null,Kt.default.createElement(Mi,null),Kt.default.createElement(W,{size:28,weight:500,lineHeight:34},t("removeSeedPrimaryText",{number:i+1})),Kt.default.createElement(W,{size:16,color:"#777777",lineHeight:19,maxWidth:"312px"},t("removeSeedSecondaryText",{number:i+1}))),Kt.default.createElement(Lt,{primaryText:t("commandContinue"),secondaryText:t("commandCancel"),primaryTheme:"primary",onPrimaryClicked:a,onSecondaryClicked:s})))};c();d();var qt=I(D()),Jg=I(Dc());var Rx=new yd,Yg=()=>{let{t:e}=$(),{mutateAsync:t}=ru(),{mutateAsync:o}=nu(),[r,n]=(0,qt.useState)("idle"),i=r==="resetting",a=async()=>{n("resetting"),we.capture("resetApp"),await Rx.clear(),await kp.logOut(),await yf(),await t(!0),await o(!0),Jg.default.runtime.reload()},{popDetailView:s}=ve();return qt.default.createElement(qt.default.Fragment,null,qt.default.createElement(nt,null,e("settingsResetApp")),qt.default.createElement(Xo,{size:"large"},qt.default.createElement("div",null),qt.default.createElement(Zo,null,qt.default.createElement(Mi,null),qt.default.createElement(W,{size:28,weight:500,lineHeight:34},e("resetSeedPrimaryText")),qt.default.createElement(W,{size:16,color:"#777777",lineHeight:19,maxWidth:"312px"},e("resetSeedSecondaryText"))),qt.default.createElement(Lt,{primaryDisabled:i,primaryLoading:i,primaryText:e("commandContinue"),secondaryText:e("commandCancel"),primaryTheme:"primary",onPrimaryClicked:a,onSecondaryClicked:s})))};var Xg=()=>{let{t:e}=$(),{pushDetailViewCallback:t,pushDetailView:o}=ve(),{additionalPermissionRows:r}=jx(),{data:n=[]}=Qo("seedless-seeds-only"),i=ld(),{data:a}=mi(),{data:s,isPending:l}=ed(),{mutate:u}=td(),{data:p,isPending:f}=rd(),{mutate:m}=nd(),g=(0,me.useCallback)(()=>{u({isAnalyticsOptedOut:!s})},[s,u]),y=(0,me.useCallback)(()=>{m({showWalletShortcuts:!p}),Wr.invalidateQueries({queryKey:[Vs]})},[p,m]),h=(0,me.useCallback)(T=>{o(me.default.createElement(Aa,{password:T,seedlessMetas:n}))},[o,n]),w=t(me.default.createElement(Ia,{type:"seedless",onSuccess:h})),k=(0,me.useMemo)(()=>{let T=[{topLeft:{text:e("settingsChangePasswordPrimary")},type:"drawer",onClick:t(me.default.createElement(jg,null))},{topLeft:{text:e("settingsAutoLockTimerPrimary")},topRight:{text:li(e,a)},type:"drawer",onClick:t(me.default.createElement(Wg,null))}];return n.length&&T.splice(1,0,{topLeft:{text:e("seedlessResetPinPrimaryText")},type:"drawer",onClick:w}),T},[n.length,a,t,w,e]),C=(0,me.useMemo)(()=>{let T=[{topLeft:{text:e("settingsResetApp"),color:"accentAlert"},onClick:t(me.default.createElement(Yg,null))}];return i&&T.unshift({topLeft:{text:e("settingsRemoveSecretPhrase"),color:"accentAlert"},onClick:t(me.default.createElement(zg,{navigationCallback:O=>o(me.default.createElement(Qg,{seedIdentifier:O}))}))}),T},[o,t,i,e]),v=(0,me.useMemo)(()=>l?[]:[{id:"toggleAnalytics",active:!s,type:"toggle",topLeft:{text:e("settingsSecurityAnalyticsPrimary")},onClick:g}],[s,l,g,e]),F=(0,me.useMemo)(()=>[{topLeft:{text:e("settingsDownloadApplicationLogs")},type:"drawer",onClick:t(me.default.createElement(Gg,null))}],[t,e]),z=(0,me.useMemo)(()=>f?[]:[{id:"toggleWalletShortcuts",topLeft:{text:e("settingsWalletShortcutsPrimary")},active:p??Zc,type:"toggle",onClick:y}],[p,f,y,e]);return me.default.createElement(me.default.Fragment,null,me.default.createElement(nt,null,e("settingsSecurityPrimary")),me.default.createElement(Qr,null,me.default.createElement(E,{gap:"section"},me.default.createElement(pt,{rows:k}),me.default.createElement(pt,{rows:F}),r.length>0?me.default.createElement(pt,{rows:r}):null,me.default.createElement(pt,{rows:z}),me.default.createElement(E,{gap:"list"},me.default.createElement(pt,{rows:v}),me.default.createElement(E,{paddingX:4},me.default.createElement(H,{font:"caption",children:e("settingsSecurityAnalyticsHelper"),color:"textSecondary"}))),me.default.createElement(pt,{rows:C}))))};function jx(){let e=wt(),{t}=$(),o=Ec(b.IS_FIREFOX),r=Fc.isFeatureEnabled("always-show-all-sites-permissions"),[n,i]=(0,me.useState)(null),[a,s]=(0,me.useState)(!1);(0,me.useEffect)(()=>{let m=!0;return(async()=>{let g=await hd();m&&i(g)})(),()=>{m=!1}},[i]);let[l,u]=(0,me.useState)(!1),p=(0,me.useCallback)(async()=>{u(!0);try{let m=!!n;if(m)await xd(),e.capture("allSitesPermissions",{data:{uiContext:"settings",oldValue:m,newValue:!1}}),i(!1);else{let g=await bd();e.capture("allSitesPermissions",{data:{uiContext:"settings",oldValue:m,newValue:g}}),i(g)}}catch(m){m instanceof Error&&m.message.includes("You cannot remove required permissions")?s(!0):(console.error("Failed to set permissions"),console.error(m))}finally{u(!1)}},[n,e]),f=(0,me.useMemo)(()=>o||r?n===null?[]:[{id:"toggleAllSitesPermissions",active:n,disabled:a||l,onClick:p,type:"toggle",topLeft:t("settingsAllSitesPermissionsTitle"),helperText:t(a?"settingsAllSitesPermissionsDisabled":"settingsAllSitesPermissionsSubtitle")}]:[],[o,r,n,a,l,p,t]);return(0,me.useMemo)(()=>({additionalPermissionRows:f}),[f])}var ey=e=>{if(e==="Settings: Security & Privacy")return Xg};var ty=()=>{let{showSettingsMenu:e}=zi(),{handleShowModalVisibility:t}=de(),o=uo();return(0,ts.useCallback)((n,i)=>{let{destinationType:a,url:s}=i;switch(a){case"External Link":Ur({url:s});break;case"Buy":t("onramp");break;case"Collectibles":o("/collectibles");break;case"Explore":o("/explore");break;case"Quests":o("/explore",{state:{page:"quests"}});break;case"Swapper":o("/swap");break;case"Settings: Claim Username":t("claimUsername");break;case"Settings: Import Seed Phrase":Ur({url:"onboarding.html?append=true"});break;case"Connect Hardware Wallet":Ur({url:"connect_hardware.html"});break;case"Convert to Jito":t("convertJitoInfo",{skipDismissRouting:!0});break;default:{let l=ey(a);if(!l)return;e(n,ts.default.createElement(l,null))}}},[o,e,t])};var Gx=x.button`
  background: none;
  background-color: rgba(60, 49, 91, 0.4);
  border: 1px solid rgb(60, 49, 91);
  border-radius: ${Et.radiusRow};
  cursor: pointer;
  height: 74px;
  padding: 10px 12px;
  width: 100%;

  &:hover {
    background-color: rgba(60, 49, 91, 0.6);
  }
`,Qx=x.div`
  align-items: center;
  display: flex;
`,Jx=x.div`
  margin-right: 12px;
`,Yx=x(W).attrs({lineHeight:17,size:14})`
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  flex: 1;
  overflow: hidden;
  text-align: left;
`,Xx=e=>{let t=Ka(),o=ty(),r=(0,Io.useCallback)(()=>{o({},{destinationType:"Quests"})},[o]),{data:[n]}=bt(["enable-jito-banner-deeplink-to-quests"]);return rs(),bp({fungible:e,goToConvertStake:n?r:t})},Zx=Io.default.memo(({title:e,onPress:t})=>Io.default.createElement(Gx,{onClick:t},Io.default.createElement(Qx,null,Io.default.createElement(Jx,null,Io.default.createElement(Du,null)),Io.default.createElement(Yx,{weight:600},e)))),os=({fungible:e})=>{let t=Xx(e);return t.canConvertStake?Io.default.createElement(Zx,{...t}):null};var tv=()=>G.default.createElement("div",{className:Ro.unverifiedBanner},G.default.createElement(Ie.AlertTriangle,{size:18}),G.default.createElement(H,{children:se.t("publicFungibleUnverifiedToken"),font:"caption"})),ns=({caip19:e,fungibleKey:t,title:o,entryPoint:r,splTokenAccount:n})=>{let i=pn(),a=(0,G.useMemo)(()=>B=>{we.capture("onPublicFungibleSwapClick",{data:{caip19:e}}),i({sellFungibleCaip19:B.sellFungibleCaip19,buyFungibleCaip19:B.buyFungibleCaip19})},[e,i]),{data:s}=ut(),{handleShowModalVisibility:l}=de(),{showDepositFungibleModal:u,hideDepositFungibleModal:p,showValidatorListModal:f,hideValidatorListModal:m}=Dt(),g=(0,G.useCallback)(B=>{self.open(B)},[]),y=Jo(),{canBuy:h,openBuy:w}=Ma(e,"publicFungible","fiatOnrampFromPublicFungible"),k=(0,G.useCallback)(B=>{navigator.clipboard.writeText(B),De(Lo("publicFungibleDetailLinkCopied"))},[]),{mutateAsync:C}=wi(),v=uo(),F=(0,G.useCallback)(async()=>{await C(),v("/notifications")},[v,C]),z=(0,G.useCallback)(()=>f({onClose:m}),[m,f]),T=Va(),O=(0,G.useCallback)(()=>{self.open(Ii,"_blank")},[]),U=Ri(e),M=Pn(e),{isSupported:N}=er(M.chainId),L=N&&"slip44"in M,V=xi(B=>B.setSendFungibleKey),_=(0,G.useCallback)(B=>{V(B.data.key,n??(B.type==="SPL"?B.data.splTokenAccountPubkey:void 0)),l("sendFungibleForm")},[V,l,n]),q=(0,G.useCallback)(B=>{u({accountName:s?.name??"",walletAddress:B.data.walletAddress,address:t,symbol:B.data.symbol,onClose:p,networkID:B.data.chain.id})},[p,u,t,s?.name]),Y=(0,G.useMemo)(()=>{let B=Pn(e);return B.resourceType==="address"?G.default.createElement(Ea,{copyString:B.address},ui(B.address)):null},[e]),{data:[te]}=bt(["enable-ugc-swaps"]),Z=Ei(e),ne=Bd({caip19:e,canBuy:h,canSwap:y,copyAddress:Y,entryPoint:r,fungibleAnalytics:Co,fungibleKey:t,includeUgcItemTypes:te,onNativeMintLSTPress:L?T:void 0,onReceive:q,onSend:_,onShare:k,onStakeLSTPress:U?T:void 0,onStakeSol:z,onUnstakeLSTPress:U?O:void 0,onUnwrapWrappedSol:F,openBuy:w,openLink:g,openSwapper:a,refetchBondingCurve:Z,title:o});return G.default.createElement(mn,{header:G.default.createElement(mt,null,ne.title),content:ne.isLoading?G.default.createElement(E,{gap:"list"},G.default.createElement(jn,{caip19:e}),G.default.createElement(E,{gap:8,direction:"row",className:Ro.ctaContainer},Array.from({length:4}).map((B,ue)=>G.default.createElement(E,{flexGrow:1,flexShrink:1,key:ue},G.default.createElement($t,{height:80,borderRadius:22})))),G.default.createElement(E,{gap:16,paddingTop:12,className:Ro.sectionTitle},G.default.createElement($t,{height:22,borderRadius:16,width:"40%"}),G.default.createElement($t,{height:80,borderRadius:16})),G.default.createElement(E,{gap:16,className:Ro.sectionTitle},G.default.createElement($t,{height:22,borderRadius:16,width:"40%"}),G.default.createElement($t,{height:200,borderRadius:16}))):G.default.createElement("div",{className:Ro.container},ne.items.map(B=>{let ue=ov(B);return ue?G.default.createElement(G.default.Fragment,{key:ue.key},ue.element):null}))})};function ov(e){if(e.type==="staking")return{key:e.type,element:G.default.createElement(G.default.Fragment,null,G.default.createElement(os,{fungible:e.data}),G.default.createElement(is,null))};if(e.type==="partialError")return{key:e.type,element:G.default.createElement(Ra,{partialErrorMessage:e.data})};if(e.type==="spamTreatment")return{key:e.type,element:G.default.createElement(pg,{onMarkNotSpam:e.data.markAsNotSpam})};if(e.type==="unverifiedBanner")return{element:G.default.createElement(tv,null),key:e.type};if(e.type==="sectionHeader")return{element:G.default.createElement(H,{color:"textSecondary",className:Ro.sectionTitle,children:e.data}),key:[e.type,e.data].join(":")};if(e.type==="about")return{element:G.default.createElement(Oa,{text:e.data}),key:e.type};if(e.type==="links")return{element:G.default.createElement("div",{className:Ro.linkContainer},G.default.createElement($a,{data:e.data})),key:e.type};if(e.type==="priceHistory")return{element:G.default.createElement(jn,{...e.data}),key:e.type};if(e.type==="cta")return{element:G.default.createElement("div",{className:Ro.ctaContainer},G.default.createElement(un,{actions:e.data.actions,uiContextName:"unifiedFungibleDetail",maxButtons:4})),key:e.type};if(e.type==="table")return oy(e.data.rows),{element:G.default.createElement(go,{rows:e.data.rows}),key:[e.type,e.data.key].join(":")};if(e.type==="ugcWarningBanner")return{element:G.default.createElement(Na,{caip19:e.data.caip19,spamStatus:e.data.spamStatus}),key:e.type};if(e.type==="tableWithBondingCurveProgress")return oy(e.data.rows),{element:G.default.createElement(Ba,{caip19:e.data.caip19,rows:e.data.rows}),key:[e.type,e.data.key].join(":")};if(e.type==="balance"){let{fungible:t}=e.data;return{element:G.default.createElement(G.default.Fragment,null,G.default.createElement(H,{color:"textSecondary",className:Ro.sectionTitle},se.t("publicFungibleDetailYourBalance")),G.default.createElement(E,null,G.default.createElement(mg,{fungible:t}))),key:e.type}}return e.type==="transactionHistory"?{element:G.default.createElement(E,{marginTop:16},G.default.createElement(dg,{fungible:e.data.fungible,caip19:e.data.caip19,fungibleKey:e.data.fungibleKey??""})),key:e.type}:e.type==="error"?{element:G.default.createElement(E,{flex:1,padding:16,justifyContent:"center",alignItems:"center"},G.default.createElement(H,{color:"textSecondary"},se.t("publicFungibleDetailErrorLoading"))),key:e.type}:null}function oy(e){for(let t of e)if("change"in t){let o=t.change;if(!t.value||typeof t.value!="string")return;let r=o.charAt(0)==="-";t.value=G.default.createElement("div",{className:j({flexDirection:"row"})},G.default.createElement(H,{font:"body",children:t.value}),G.default.createElement(H,{className:j({paddingLeft:6}),font:"body",color:r?"accentAlert":"accentSuccess",children:o}))}}var Ro={container:j({display:"flex",flexDirection:"column",gap:"list",paddingBottom:8}),sectionTitle:j({font:"bodySemibold",paddingTop:12}),about:j({font:"body"}),price:j({textAlign:"center",font:"heading1"}),linkContainer:j({paddingTop:16}),ctaContainer:j({paddingTop:16}),unverifiedBanner:j({display:"flex",flex:1,gap:8,backgroundColor:"bgRow",padding:16,borderRadius:16})};var rs=()=>{let{t:e}=$(),{data:[t]}=bt(["enable-unified-token-pages"]),o=uo(),{pushDetailView:r}=ve(),n=Es(a=>a.navigateTo),i=Es(a=>a.setNavigateTo);(0,ti.useEffect)(()=>{if(n){if(n.screen==="Activity")o("/notifications");else if(n.screen==="FungibleDetail"){let{type:a,data:s}=n.fungible,l=Zt(n.fungible),u=Xt(l);r(t?ti.default.createElement(ns,{caip19:u,fungibleKey:s.key,entryPoint:"liquidStaking"}):ti.default.createElement(as,{fungibleKey:s.key,networkID:s.chain.id,chainName:s.chain.name,name:s.name??e("assetDetailUnknownToken"),symbol:s.symbol,tokenAddress:s.tokenAddress,type:a,walletAddress:s.walletAddress}))}i(null)}},[o,n,i,r,e,t])};var vc={container:j({height:"100%",display:"flex",flexDirection:"column",padding:"screen"}),accountList:j({height:"100%",overflow:"auto"}),buttonContainer:j({width:"100%",paddingTop:"screen"})},ry=()=>{let{t:e}=$(),{showValidatorListModal:t,hideValidatorListModal:o}=Dt(),{handleShowModalVisibility:r}=de(),{isSupported:n,defaultProvider:i}=er(Ye.solana.mainnetID);rs();let{hasConvertibleStakeAccounts:a}=hp(i),s=(0,Ao.useCallback)(()=>{n?r("stakingMethods",{onSelectNativeStaking:()=>t({onClose:o})}):t({onClose:o})},[r,o,n,t]),l=Ka();return Ao.default.createElement("div",{className:vc.container},Ao.default.createElement(mt,{onIconClick:s,icon:Ao.default.createElement(Bi,null)},e("stakeAccountListViewPrimaryText")),Ao.default.createElement("div",{className:vc.accountList},Ao.default.createElement(og,null)),n&&a?Ao.default.createElement("div",{className:vc.buttonContainer},Ao.default.createElement(zt,{theme:"primary",onClick:l},e("convertToJitoSOL"))):null)};var rv=x(Yr)`
  display: grid;
  grid-template-columns: 44px auto;
  column-gap: 10px;
  margin-bottom: 0;
`,nv=x.div`
  overflow: hidden;
`,wc=x.div`
  display: grid;
  grid-template-columns: 1fr;
`,ny=x.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
`,is=()=>{let{t:e}=$(),{pushDetailView:t}=ve(),{showValidatorListModal:o,hideValidatorListModal:r}=Dt(),{handleShowModalVisibility:n}=de(),{data:i}=gi("solana"),{isSupported:a,isLoading:s}=er(i?.networkID),l=i?.address??"",u=(0,Ce.useMemo)(()=>md(i?.networkID),[i?.networkID]),p=qi(u,l),{isFetching:f,isError:m}=p,g=f||s,{totalStake:y,totalRewards:h,numAccounts:w}=(0,Ce.useMemo)(()=>{let C=p.data??[];return{totalStake:C.reduce((v,F)=>v+F.lamports,0),totalRewards:C.reduce((v,F)=>v+(F.inflationReward??0),0),numAccounts:C.length}},[p.data]),k=(0,Ce.useCallback)(()=>{if(m){p.refetch();return}g||(w>0?t(Ce.default.createElement(ry,null)):a?n("stakingMethods",{onSelectNativeStaking:()=>o({onClose:r})}):o({onClose:r}))},[a,n,r,m,g,w,t,o,p]);return Ce.default.createElement(rv,{role:"button",isDisabled:g,onClick:k},g?Ce.default.createElement(oo,{diameter:44,color:dt("#AB9FF2",.2)},Ce.default.createElement(_i,{diameter:28})):m?Ce.default.createElement(oo,{diameter:44,color:dt("#EB3742",.1)},Ce.default.createElement(gu,null)):Ce.default.createElement(oo,{diameter:44,color:`${dt(Et.colors.brand.green,.1)}`},Ce.default.createElement(Ie.TrendingUp,{color:"accentSuccess"})),Ce.default.createElement(nv,null,g?Ce.default.createElement(wc,null,Ce.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left"},e("startEarningSolLoading")),Ce.default.createElement(W,{color:"#777777",size:14,lineHeight:17,textAlign:"left",noWrap:!0},e("startEarningSolSearching"))):m?Ce.default.createElement(wc,null,Ce.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left"},e("startEarningSolErrorTroubleLoading")),Ce.default.createElement(W,{color:"#777777",size:14,lineHeight:17,textAlign:"left",noWrap:!0},e("startEarningSolErrorClosePhantom"))):w?Ce.default.createElement(Ce.default.Fragment,null,Ce.default.createElement(ny,null,Ce.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left"},e("startEarningSolYourStake")),Ce.default.createElement(W,{size:14,weight:400,lineHeight:17,textAlign:"right",noWrap:!0},Ce.default.createElement(tr,null,y))),Ce.default.createElement(ny,null,Ce.default.createElement(W,{color:"#777777",size:14,lineHeight:19,textAlign:"left",noWrap:!0},w," ",w===1?"account":"accounts"),Ce.default.createElement(W,{size:14,color:`${h>0?"#21E56F":"#777777"}`,lineHeight:17,textAlign:"right",noWrap:!0},h>0?Ce.default.createElement(Ce.default.Fragment,null,"+",Ce.default.createElement(tr,null,h)):"\u2013"))):Ce.default.createElement(wc,null,Ce.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left"},e("startEarningSolPrimaryText")),Ce.default.createElement(W,{color:"#777777",size:14,lineHeight:17,textAlign:"left",noWrap:!0},e("startEarningSolStakeTokens")))))};c();d();var mr=I(D());var iv=x(Yr)`
  display: grid;
  grid-template-columns: 44px auto;
  column-gap: 10px;
  margin-bottom: 0;
`,av=x.div`
  display: grid;
  grid-template-columns: 1fr;
`,sv=x(W)`
  margin-top: 5px;
`,iy=({fungible:e})=>{let{symbol:t,name:o,logoUri:r,chain:n}=e.data,{t:i}=$(),{handleShowModalVisibility:a}=de(),s=(0,mr.useCallback)(()=>{a("approveUnwrapFungible",{fungible:e})},[e,a]);return mr.default.createElement(iv,{onClick:s},mr.default.createElement(gt,{image:{type:"icon",preset:"swap"},size:48,badge:{src:r??""}}),mr.default.createElement(av,null,mr.default.createElement(W,{size:16,weight:600,lineHeight:19,textAlign:"left"},i("unwrapFungibleTitle",{tokenSymbol:t})),mr.default.createElement(sv,{color:"#777777",size:14,lineHeight:17,textAlign:"left"},i("unwrapFungibleDescription",{fromToken:o,toToken:n.symbol}))))};var lv=Ac(0);function cv(e){Hi(e),De.success(se.t("pastParticipleCopied"))}var wn=({children:e})=>Q.default.createElement(E,{marginTop:16,width:"100%"},e),dv=({children:e})=>Q.default.createElement(E,{marginBottom:8,width:"100%"},e),uv=({children:e})=>Q.default.createElement(E,{marginTop:16,marginBottom:8,width:"100%"},e),pv=({children:e})=>Q.default.createElement(E,{paddingBottom:16},e),Sc=({children:e})=>Q.default.createElement(E,{marginBottom:8},Q.default.createElement(H,{font:"bodySemibold",color:"textSecondary"},e)),mv=({children:e})=>Q.default.createElement(E,{display:"flex",direction:"column",gap:8},e),fv=e=>{let{networkID:t,fungibleKey:o,name:r,symbol:n,tokenAddress:i,walletAddress:a,splTokenAccount:s}=e,{data:[l]}=bt(["kill-brc20-sends"]),{showDepositFungibleModal:u,hideDepositFungibleModal:p}=Dt(),{handleShowModalVisibility:f}=de(),{pushDetailView:m}=ve(),{data:g,isPending:y}=ut(),h=g?.isReadOnly,{data:w=di,status:k}=fi(),C=w.isDeveloperMode,{fungible:v,refetch:F,isLoadingTokens:z,visibilityOverrides:T}=Id({key:o,splTokenAccount:s,useTokenQueryOptions:{staleTime:Wc,refetchInterval:Hc}}),{fungibleName:O,fungibleSymbol:U,fungibleBalance:M,fungiblePrice:N,fungibleTokenType:L}=(0,Q.useMemo)(()=>({fungibleName:v?.data.name??r,fungibleSymbol:v?.data.symbol??n,fungibleBalance:v?.data.balance??lv,fungiblePrice:v?.data.usd,fungibleTokenType:v?.type}),[v,r,n]),V=vd(T,{key:o,spamStatus:v?.data.spamStatus??"NOT_VERIFIED"}),{data:_}=gi({networkID:t,address:v?.data.walletAddress}),{t:q}=$(),Y=(0,Q.useMemo)(()=>({recentActivityText:q("assetDetailRecentActivity"),sendButtonText:q("commandSend"),receiveButtonText:q("commandReceive"),swapButtonText:q("commandSwap"),spamWarning:q("tokenSpamWarning"),reportAsNotSpam:q("commandReportAsNotSpam"),stakingLabel:q("assetDetailStakingLabel"),priceDetailLabel:q("assetDetailPriceDetail"),aboutLabel:q("assetDetailAboutLabel",{fungibleName:O})}),[q,O]),Z=zc(O,20),ne=Ud(v),B=(0,Q.useMemo)(()=>zd(_?[_]:[],t,ne),[_,t,ne]),ue=v?.data.tokenAddress===Nd&&!h,Pe=xi(lo=>lo.setSendFungibleKey),ye=(0,Q.useCallback)(()=>{v&&(Pe(v?.data.key,s),f("sendFungibleForm"))},[v,Pe,f,s]),xe=Vr.get(t).isPriceDetailSupported??!0,he=(0,Q.useCallback)(()=>{u({accountName:g?.name??"",walletAddress:a,address:i,symbol:U,onClose:p,networkID:t})},[g?.name,p,i,a,u,U,t]),{caip19:Le}=(0,Q.useMemo)(()=>{if(!v)return{};let lo=Zt(v);return{fungibleCaip19:lo,caip19:Xt(lo)}},[v]),_e=(0,Q.useCallback)(()=>{Le&&m(Q.default.createElement(ss,{caip19:Le,fungibleKey:o,title:r,entryPoint:"ownedFungibleDetail"}))},[m,Le,o,r]),be=Ye.isSolanaNetworkID(t)?hi(t)==="mainnet-beta":!0,$e=v?.type==="SolanaNative"&&!h,{summaryItems:et,shouldShowPartialError:tt,partialErrorMessage:Ke,holdingsQueryResult:ao,isHoldingsEnabled:fr}=Ld({fungibleKey:o,copyToClipboard:cv,account:g}),gr=pn(),_r=(0,Q.useCallback)(()=>{!v||!Le||(we.capture("fungibleSwapClick",{data:{caip19:Le}}),gr({buyFungible:v}))},[v,Le,gr]),{mutateAsync:yr}=Sd(),Sn=(0,Q.useCallback)(async()=>{!v||!g?.identifier||(we.capture("fungiblesReportAsNotSpam",{data:{caip19:Xt(Zt(v))}}),await yr({accountId:g.identifier,mutations:[{fungibleKey:v.data.key,visibility:"visible:reported_notSpam"}]}))},[g,v,yr]),us=(0,Q.useMemo)(()=>Pd(v),[v]),ri=Jo()(_?.networkID),{showValidatorListModal:Me,hideValidatorListModal:Ot}=Dt(),so=(0,Q.useCallback)(()=>Me({onClose:Ot}),[Ot,Me]),{mutateAsync:Cn}=wi(),$r=uo(),ps=(0,Q.useCallback)(async()=>{await Cn(),$r("/notifications")},[$r,Cn]),ni=(0,Q.useCallback)(()=>{self.open(Ii,"_blank")},[]),Cc=Va(),Tc=Ri(v),{isSupported:dy}=er(t),uy=dy&&v&&vi(v.type),Pc=Ad(v),py=(0,Q.useCallback)(()=>{self.open(Pc?.explorerUrl,"_blank")},[Pc?.explorerUrl]),my=(0,Q.useMemo)(()=>{let lo=Md({analytics:we,fungible:v,accountId:g?.identifier,setVisibilityOverrides:yr});return lo?()=>{lo?.(),$r("/")}:void 0},[v,$r,g?.identifier,yr]),{ctaActions:ii}=Ed({balance:M,canSwap:ri,chainAddress:_,fee:pd,fungible:v,isReadOnlyAccount:h,isSplNonTransferable:us,killBrc20Sends:l,onDepositPress:he,onMarkNotSpam:V==="POSSIBLE_SPAM"?Sn:void 0,onMarkAsSpam:my,onSendPress:ye,onStakeSolPress:so,onSwapPress:_r,onUnwrapWrappedSolPress:ps,onNativeMintLSTPress:uy?Cc:void 0,onStakeLSTPress:Tc?Cc:void 0,onUnstakeLSTPress:Tc?ni:void 0,onViewOnExplorerPress:py,type:L??"SolanaNative"}),kc=(0,Q.useMemo)(()=>({primary:[],more:ii.more.filter(lo=>lo.type==="viewOnExplorer")}),[ii]);return{data:(0,Q.useMemo)(()=>({onPriceDetailsClick:_e,chainAddress:_,fungible:v,tokenAddress:i,fungibleSymbol:U,fungibleBalance:M,fungiblePrice:N,summaryItems:et,canStake:$e,showUnwrapBlurEth:ue,showDollarValue:be,i18nStrings:Y,accounts:B,pageHeader:Z,isDeveloperMode:C,developerModeStatus:k,refetch:F,walletAddress:a,isReadOnlyAccount:h,derivedSpamStatus:V,onMarkNotSpam:Sn,ctaActions:ii,readOnlyCtaActions:kc,shouldShowPartialError:tt,partialErrorMessage:Ke,holdingsQueryResult:ao,isHoldingsEnabled:fr,showFungiblePriceDetail:xe}),[_e,_,v,i,U,M,N,et,$e,ue,be,Y,B,Z,C,k,F,a,h,V,Sn,ii,kc,tt,Ke,ao,fr,xe]),loading:y||z}},gv=Q.default.memo(e=>{let{onPriceDetailsClick:t,fungible:o,tokenAddress:r,fungibleSymbol:n,fungibleBalance:i,fungiblePrice:a,summaryItems:s,canStake:l,showUnwrapBlurEth:u,showDollarValue:p,i18nStrings:f,accounts:m=[],isDeveloperMode:g,developerModeStatus:y,isReadOnlyAccount:h,derivedSpamStatus:w,onMarkNotSpam:k,ctaActions:C,readOnlyCtaActions:v,shouldShowPartialError:F,partialErrorMessage:z,holdingsQueryResult:T,isHoldingsEnabled:O,showFungiblePriceDetail:U}=e,M=Q.default.createElement(dv,null,F?Q.default.createElement(pv,null,Q.default.createElement(Ra,{partialErrorMessage:z})):null,Q.default.createElement(Zf,{balance:i,symbol:n,mint:r,dollarValue:a,showDollarValue:p}),Q.default.createElement(uv,null,Q.default.createElement(un,{actions:h?v:C,uiContextName:"fungibleDetail",maxButtons:4})),w==="POSSIBLE_SPAM"&&Q.default.createElement(wn,null,Q.default.createElement(Ha,{message:f.spamWarning,variant:"warning",Icon:Ni,actions:[o?.data.spamStatus==="POSSIBLE_SPAM"&&{label:f.reportAsNotSpam,onClick:k}].filter(Oc)})),l&&Q.default.createElement(wn,null,Q.default.createElement(Sc,null,f.stakingLabel),Q.default.createElement(mv,null,Q.default.createElement(os,{fungible:o}),Q.default.createElement(is,null))),u&&o?Q.default.createElement(wn,null,Q.default.createElement(iy,{fungible:o})):null,U&&w!=="POSSIBLE_SPAM"&&o?Q.default.createElement(wn,null,Q.default.createElement(Sc,null,f.priceDetailLabel),Q.default.createElement(tg,{fungible:o,onPress:t})):null,O?Q.default.createElement(wn,null,Q.default.createElement(Gf,{data:T.data,fetchStatus:T.status,currentBalance:o?.data.balance,currentPrice:o?.data.price})):null,s.length>0?Q.default.createElement(wn,null,Q.default.createElement(Sc,null,f.aboutLabel),Q.default.createElement(go,{rows:s,designSystemOptIn:!0})):null),L=Ci({accounts:m,isSpam:!1}),V=L.isPending||y==="pending",{handleShowModalVisibility:_}=de(),q=(0,Q.useCallback)(te=>_("activityItem",te),[_]),Y=(0,Q.useCallback)(te=>_("pendingTransaction",te),[_]);return o===void 0?M:Q.default.createElement(Ir,{header:M,dataPages:L.data?.pages,isLoading:V,fetchNextPage:L.fetchNextPage,refetch:L.refetch,hasNextPage:L.hasNextPage,isFetchingNextPage:L.isFetchingNextPage,isError:L.isError,isRefreshError:L.isRefetchError,isRefreshingConfirmedTxs:!1,isDeveloperMode:g,showActivityItemModal:q,showPendingTransactionModal:Y})}),as=Q.default.memo(e=>{let{data:t,loading:o}=fv(e);return Q.default.createElement(mn,{header:Q.default.createElement(mt,null,t.pageHeader),content:o?Q.default.createElement(eg,{depositText:t.i18nStrings.receiveButtonText,sendText:t.i18nStrings.sendButtonText}):Q.default.createElement(gv,{...t})})});var yv=()=>ge.default.createElement("div",{className:oi.unverifiedBanner},ge.default.createElement(Ie.AlertTriangle,{size:18}),ge.default.createElement(H,{children:se.t("publicFungibleUnverifiedToken"),font:"caption"})),hv=ge.default.memo(({items:e})=>ge.default.createElement("div",{className:oi.container},e.map(t=>{let o=bv(t);return o?ge.default.createElement(ge.default.Fragment,{key:o.key},o.element):null}))),ss=({caip19:e,fungibleKey:t,title:o,entryPoint:r})=>{let n=pn(),i=(0,ge.useMemo)(()=>T=>{we.capture("onPublicFungibleSwapClick",{data:{caip19:e}}),n({sellFungibleCaip19:T.sellFungibleCaip19,buyFungibleCaip19:T.buyFungibleCaip19})},[e,n]),a=(0,ge.useCallback)(T=>{self.open(T)},[]),s=Jo(),{pushDetailView:l}=ve(),{handleShowModalVisibility:u,handleHideModalVisibility:p}=de(),f=(0,ge.useCallback)(T=>{let{tokenAddress:O,chainName:U,networkID:M,title:N,symbol:L,walletAddress:V,type:_,fungibleKey:q}=T;l(ge.default.createElement(as,{networkID:M,chainName:U,name:N,symbol:L,fungibleKey:q,tokenAddress:O,type:_,walletAddress:V}))},[l]),{canBuy:m,openBuy:g}=Ma(e,"publicFungible","fiatOnrampFromPublicFungible"),y=(0,ge.useCallback)(()=>{p("reportIssue")},[p]),h=(0,ge.useCallback)(()=>{u("reportIssue",{id:e,prompt:se.t("publicFungibleReportIssuePrompt",{tokenName:o}),options:[{key:"publicFungibleReportIssueIncorrectInformation"},{key:"publicFungibleReportIssuePriceStale"},{key:"publicFungibleReportIssuePriceMissing"},{key:"publicFungibleReportIssuePerformanceIncorrect"},{key:"publicFungibleReportIssueLinkBroken"}],context:"publicFungible",onClose:y})},[e,u,o,y]),w=(0,ge.useCallback)(T=>{navigator.clipboard.writeText(T),De(Lo("publicFungibleDetailLinkCopied"))},[]),k=(0,ge.useMemo)(()=>{let T=Pn(e);return T.resourceType==="address"?ge.default.createElement(Ea,{copyString:T.address},ui(T.address)):null},[e]),{data:[C]}=bt(["enable-ugc-swaps"]),v=Ei(e),F=Fd({caip19:e,canBuy:m,canSwap:s,entryPoint:r,fungibleKey:t,fungibleAnalytics:Co,navigateToBalance:f,openBuy:g,openReportIssue:h,openLink:a,openSwapper:i,title:o,onShare:w,copyAddress:k,includeUgcItemTypes:C,refetchBondingCurve:v}),z=(0,ge.useCallback)(()=>{F.actions&&u("callToActionSheet",{actions:F.actions,onClose:()=>{p("callToActionSheet")},trackAction:T=>{F.actions&&Co.ctaBarTrackPrimaryButtonsClick({uiContext:{name:"publicFungibleDetail"},position:"more",type:T.type,typeSpecificMetadata:T.typeSpecificMetadata,maxButtons:4,primaryActions:F.actions.primary,moreActions:F.actions.more})}})},[F.actions,p,u]);return ge.default.createElement(mn,{header:ge.default.createElement(mt,null,o),content:ge.default.createElement(hv,{onMenuOnlyCTAClick:z,...F})})};function bv(e){return e.type==="unverifiedBanner"?{element:ge.default.createElement(yv,null),key:e.type}:e.type==="sectionHeader"?{element:ge.default.createElement(H,{color:"textSecondary",className:oi.sectionTitle,children:e.data}),key:[e.type,e.data].join(":")}:e.type==="about"?{element:ge.default.createElement(Oa,{text:e.data}),key:e.type}:e.type==="links"?{element:ge.default.createElement("div",{className:oi.linkContainer},ge.default.createElement($a,{data:e.data})),key:e.type}:e.type==="priceHistory"?{element:ge.default.createElement(jn,{...e.data}),key:e.type}:e.type==="cta"?{element:ge.default.createElement("div",{className:oi.ctaContainer},ge.default.createElement(un,{actions:e.data.actions,uiContextName:"publicFungibleDetail",maxButtons:4})),key:e.type}:e.type==="table"?(ay(e.data.rows),{element:ge.default.createElement(go,{rows:e.data.rows}),key:[e.type,e.data.key].join(":")}):e.type==="ugcWarningBanner"?{element:ge.default.createElement(Na,{caip19:e.data.caip19,spamStatus:e.data.spamStatus}),key:e.type}:e.type==="tableWithBondingCurveProgress"?(ay(e.data.rows),{element:ge.default.createElement(Ba,{caip19:e.data.caip19,rows:e.data.rows}),key:[e.type,e.data.key].join(":")}):e.type==="balance"?{element:ge.default.createElement(qf,{data:e.data}),key:e.type}:e.type==="error"?{element:ge.default.createElement(E,{flex:1,padding:16,justifyContent:"center",alignItems:"center"},ge.default.createElement(H,{color:"textSecondary"},se.t("publicFungibleDetailErrorLoading"))),key:e.type}:null}function ay(e){for(let t of e)if("change"in t){let o=t.change;if(!t.value||typeof t.value!="string")return;let r=o.charAt(0)==="-";t.value=ge.default.createElement("div",{className:j({flexDirection:"row"})},ge.default.createElement(H,{font:"body",children:t.value}),ge.default.createElement(H,{className:j({paddingLeft:6}),font:"body",color:r?"accentAlert":"accentSuccess",children:o}))}}var oi={container:j({display:"flex",flexDirection:"column",gap:"list"}),sectionTitle:j({font:"bodySemibold",paddingTop:12}),about:j({font:"body"}),price:j({textAlign:"center",font:"heading1"}),linkContainer:j({paddingTop:16,paddingBottom:8}),ctaContainer:j({paddingTop:16}),unverifiedBanner:j({display:"flex",flex:1,gap:8,backgroundColor:"bgRow",padding:16,borderRadius:16})};c();d();var ls={assetSelectionContainer:"_51gazn18w _51gazn1c3 _51gaznql _51gazn13j _51gazn129",fullWidthDetailView:"_51gaznd1 _51gaznam _51gaznfg _51gazn87 _51gazn19w"};var xv=84,vv=()=>{let{t:e}=$(),t=wt(),{handleHideModalVisibility:o}=de(),{setSearchQuery:r}=Hr(),{pushDetailView:n}=ve(),{executeInterceptors:i}=tu(),a=Mf(),{data:[s]}=bt(["enable-unified-token-pages"]),l=Xd(),u=(0,je.useCallback)((m,g)=>{switch(t.capture("swapperAssetSelectionClosed",{data:{assetSelected:!0,name:m.data.name??"",symbol:m.data.symbol??"",tokenAddress:m.data.tokenAddress??"",tokenType:m.type,networkId:m.data.chain.id,type:"buy",searchQuery:Hr.getState().searchQuery,numSearchResults:Hr.getState().numSearchResults,searchRank:g}}),i(m,a)){case"interrupt":break;case"continue":r(""),l(m),o("swapBuyAssetSelect");break}},[t,i,a,r,l,o]),p=(0,je.useCallback)(()=>{t.capture("swapperAssetSelectionClosed",{data:{assetSelected:!1,type:"buy",searchQuery:Hr.getState().searchQuery,numSearchResults:Hr.getState().numSearchResults}}),r(""),o("swapBuyAssetSelect")},[t,o,r]),f=(0,je.useCallback)((m,g)=>{let{key:y,name:h}=g.data,w=Xt(Zt(g));n(je.default.createElement("div",{className:ls.fullWidthDetailView},s?je.default.createElement(ns,{caip19:w,fungibleKey:y,title:h??void 0,entryPoint:"swapperSearch"}):je.default.createElement(ss,{caip19:w,fungibleKey:y,title:h??void 0,entryPoint:"swapperSearch"}))),m.stopPropagation()},[n,s]);return{t:e,onClose:p,onSelect:u,onInfoPress:f}},wv=()=>{let e=vv(),{assets:t,initialFilterKey:o,isFetching:r,error:n,searchRef:i,setSearchQuery:a}=Yd();return{sections:ou(t),initialFilterKey:o,isFetching:r,error:n,searchRef:i,setSearchQuery:a,...e}},Sv=je.default.memo(()=>{let{error:e,initialFilterKey:t,isFetching:o,onClose:r,onInfoPress:n,onSelect:i,sections:a,searchRef:s,setSearchQuery:l,t:u}=wv();return je.default.createElement("div",{className:ls.assetSelectionContainer},je.default.createElement(tc,{sections:a??[],isFetching:o,localizedError:e,onFungiblePress:i,onInfoPress:n,initialFilterKey:t,showCurrencyValues:!0,hideEmptyCurrency:!0,showInfoTooltip:!0,direction:"buy",bottomOffset:xv,enableLiveSearch:!0,setSearchQuery:l,searchRef:s}),je.default.createElement(eo,null,je.default.createElement(zt,{onClick:r},u("commandClose"))))}),sy=()=>je.default.createElement(eu,null,je.default.createElement(Sv,null)),Cv=()=>{let{t:e}=$(),t=wt(),{handleHideModalVisibility:o}=de(),{assets:r,isLoading:n,error:i}=Jd({enablePrices:!0,enableSorting:!0}),a=Zd(),s=(0,je.useCallback)(u=>{t.capture("swapperAssetSelectionClosed",{data:{assetSelected:!0,symbol:u.data.symbol??"",networkId:u.data.chain.id,type:"sell"}}),a(u),o("swapSellAssetSelect")},[t,o,a]),l=(0,je.useCallback)(()=>{t.capture("swapperAssetSelectionClosed",{data:{assetSelected:!1,type:"sell"}}),o("swapSellAssetSelect")},[t,o]);return{assets:r,isFetching:n,error:i,onClose:l,onSelect:s,t:e}},Tv=je.default.memo(({assets:e,isFetching:t,error:o,onClose:r,onSelect:n,t:i})=>je.default.createElement("div",{className:ls.assetSelectionContainer},je.default.createElement(tc,{data:e??[],isFetching:t,localizedError:o,onFungiblePress:n,showCurrencyValues:!0,showInfoTooltip:!1,searchRef:"",direction:"sell"}),je.default.createElement(eo,null,je.default.createElement(zt,{onClick:r},i("commandClose"))))),ly=()=>{let e=Cv();return je.default.createElement(Tv,{...e})};var Pv=(0,S.lazy)(()=>import("./ActivityItemDetail-NOPJ2N2N.js")),kv=(0,S.lazy)(()=>import("./PendingTransactionDetail-6BCNFF7Y.js")),Iv=(0,S.lazy)(()=>import("./ApproveUnwrapFungible-QNBQNSBC.js")),Av=(0,S.lazy)(()=>import("./SendFungibleFormPage-35QM56BV.js")),Ev=(0,S.lazy)(()=>import("./SendFungibleSelectPage-IWT5XZZU.js")),Lv=(0,S.lazy)(()=>import("./SlippageSettings-R6PTHYZW.js")),Dv=(0,S.lazy)(()=>import("./CollectiblesVisibilityPage-B3ILZG3B.js")),Fv=(0,S.lazy)(()=>import("./NetworkHealth-RS54C5MZ.js")),Mv=(0,S.lazy)(()=>import("./WarningInfoModal-JT3BZDXM.js")),Bv=(0,S.lazy)(()=>import("./ShortcutsModal-4ANRHREU.js")),Nv=(0,S.lazy)(()=>import("./Modal-NXC3R7WZ.js")),Ov=(0,S.lazy)(()=>import("./InsufficientBalance-D3III4OA.js")),_v=(0,S.lazy)(()=>import("./AdditionalPermissions-GRVPRTLZ.js")),$v=(0,S.lazy)(()=>import("./BitcoinAddressTypes-YY5DLY5F.js")),zv=(0,S.lazy)(()=>import("./ForceUpgrade-RWQPT3LC.js")),Uv=(0,S.lazy)(()=>import("./InstantSellSummary-3FI6WCOZ.js")),Vv=(0,S.lazy)(()=>import("./FiatRampFungiblesPage-ZOWJ5EXG.js")),Hv=(0,S.lazy)(()=>import("./FungibleVisibilityPage-HBTZRC7G.js")),Wv=(0,S.lazy)(()=>import("./ReceivePage-PWVMHECE.js")),Kv=(0,S.lazy)(()=>import("./ClaimRewardModal-CZMVLHAO.js")),qv=(0,S.lazy)(()=>import("./SendCollectibleFormPage-NYQSE6RB.js")),Rv=(0,S.lazy)(()=>import("./ConvertToJitoSOLInfoPage-P6I5JQ44.js")),jv=(0,S.lazy)(()=>import("./ConvertStakeAccountListPage-OMMK2DUX.js")),Gv=(0,S.lazy)(()=>import("./ConvertStakeAccountStatusPage-GCTNCWUQ.js")),Qv=(0,S.lazy)(()=>import("./StakingMethodSelectionPage-GIVQVJ56.js")),Jv=(0,S.lazy)(()=>import("./MintJitoSOLInfoPage-USSPZ3YU.js")),Yv=(0,S.lazy)(()=>import("./MintLiquidStakeAmountPage-WGXG6SOK.js")),Xv=(0,S.lazy)(()=>import("./MintLiquidStakeStatusPage-7CWQCRRY.js")),Zv=(0,S.lazy)(()=>import("./SwapBridgeRefuelPage-7APIF4GR.js")),ew=(0,S.lazy)(()=>import("./SwapProvidersPage-ALI4Z25C.js")),tw=(0,S.lazy)(()=>import("./SwapReviewPage-GP6NNYRO.js")),ow=(0,S.lazy)(()=>import("./SwapConfirmationPage-QDXW4DYM.js")),rw=(0,S.lazy)(()=>import("./SwapTermsOfServicePage-TCEOT3ZV.js")),nw=(0,S.lazy)(()=>import("./ClaimUsernameIntroPage-MEL74X2Y.js")),iw=(0,S.lazy)(()=>import("./SearchPage-IHOXEZHN.js")),aw=(0,S.lazy)(()=>import("./UGCTradeWarning-57V5OJUY.js")),sw=(0,S.lazy)(()=>import("./AutoSlippageOptIn-OD6AK4IT.js")),pe=e=>({...e,visibility:!1,render:function(t){return S.default.createElement(Vm,{isOpen:this.visibility,isFullScreen:this.fullScreen,presentationStyle:this.presentationStyle,headerHeight:this.fullScreen?0:void 0},S.default.createElement(S.Suspense,null,e.render(t)))}}),lw=e=>({...e,visibility:!1,render:function(t){let o=()=>cs.getState().handleHideModalVisibility(this.modalName);return S.default.createElement(Hm,{rootId:"root",isOpen:this.visibility,onClose:o,detent:this.detent},S.default.createElement(Tr.Container,null,S.default.createElement(Tr.Header,null,S.default.createElement(Wm,null)),S.default.createElement(Tr.Content,{disableDrag:!0},e.render(t),S.default.createElement(zt,{onClick:()=>cs.getState().handleHideModalVisibility(this.modalName)},se.t("commandClose")))),S.default.createElement(Tr.Backdrop,{onTap:o}))}}),cw={fungibleVisibility:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(Hv,null))}}),sendFungibleForm:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(Av,null))}}),sendFungibleSelect:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(Ev,null))}}),collectiblesVisibility:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(Dv,null))}}),networkHealth:pe({render:function(e){return e?S.default.createElement(Fv,{...e}):null}}),instantSell:pe({render:function(e){return e?S.default.createElement(Be,null,S.default.createElement(Uv,{...e})):null}}),insufficientBalance:pe({render:function(e){return S.default.createElement(Be,null,S.default.createElement(Ov,{...e}))}}),approveUnwrapFungible:pe({render:function(e){return S.default.createElement(Be,null,S.default.createElement(Iv,{...e}))}}),bridgeRefuel:pe({fullScreen:!0,render:function(){return S.default.createElement(Be,null,S.default.createElement(Zv,null))}}),interstitial:pe({fullScreen:!0,render:function(e){return S.default.createElement(cy,{...e})}}),claimReward:pe({fullScreen:!0,render:function(e){return S.default.createElement(Kv,{...e})}}),claimUsername:pe({fullScreen:!0,render:function(){return S.default.createElement(Be,null,S.default.createElement(nw,null))}}),shortcutsSheet:lw({detent:"content-height",modalName:"shortcutsSheet",render(e){return S.default.createElement(Bv,{...e})}}),callToActionSheet:pe({fullScreen:!0,render(e){return S.default.createElement(Nv,{...e})}}),receive:pe({render(e){return S.default.createElement(Be,null,S.default.createElement(Wv,{...e}))}}),onramp:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(Vv,null))}}),swapSellAssetSelect:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(ly,null))}}),swapBuyAssetSelect:pe({render:function(){return S.default.createElement(Be,null,S.default.createElement(sy,null))}}),swapProviders:pe({render:function(){return S.default.createElement(ew,null)}}),swapReview:pe({fullScreen:!0,render:function(){return S.default.createElement(Be,null,S.default.createElement(tw,null))}}),swapConfirmation:pe({fullScreen:!0,render:function(){return S.default.createElement(ow,null)}}),swapTermsOfService:pe({fullScreen:!0,render:function(){return S.default.createElement(rw,null)}}),txSubmissionCheckFailure:pe({fullScreen:!0,render:function(e){return S.default.createElement(Be,null,S.default.createElement(Mv,{...e}))}}),additionalPermissions:pe({fullScreen:!0,render:()=>S.default.createElement(_v,null)}),bitcoinAddressTypes:pe({fullScreen:!0,render:()=>S.default.createElement($v,null)}),sendCollectibleForm:pe({render:()=>S.default.createElement(Be,null,S.default.createElement(qv,null))}),slippageSettings:pe({fullScreen:!0,render:function(){return S.default.createElement(Be,null,S.default.createElement(Lv,null))}}),spamActivity:pe({render(){return S.default.createElement(Be,{shouldPushDetailView:!0},S.default.createElement(nf,{isSpam:!0}))}}),activityItem:pe({render(e){return S.default.createElement(Be,null,S.default.createElement(Pv,{...e}))}}),pendingTransaction:pe({render(e){return S.default.createElement(Be,null,S.default.createElement(kv,{...e}))}}),forceUpgrade:pe({fullScreen:!0,render:function(e){return S.default.createElement(zv,{...e})}}),convertJitoInfo:pe({fullScreen:!0,render(e){return S.default.createElement(Rv,{...e})}}),convertStakeAccounts:pe({render(){return S.default.createElement(Be,null,S.default.createElement(jv,null))}}),convertStakeStatus:pe({fullScreen:!0,render(){return S.default.createElement(Gv,null)}}),stakingMethods:pe({render(e){return S.default.createElement(Be,null,S.default.createElement(Qv,{...e}))}}),mintJitoInfo:pe({fullScreen:!0,render(e){return S.default.createElement(Jv,{...e})}}),mintStakeAmount:pe({render(){return S.default.createElement(Be,null,S.default.createElement(Yv,null))}}),mintStakeStatus:pe({fullScreen:!0,render(){return S.default.createElement(Xv,null)}}),reportIssue:pe({fullScreen:!0,render(e){return S.default.createElement(Be,null,S.default.createElement(zm,{...e}))}}),searchPage:pe({fullScreen:!0,presentationStyle:"fadeIn",render(){return S.default.createElement(Be,null,S.default.createElement(iw,null))}}),ugcWarning:pe({fullScreen:!0,render(){return S.default.createElement(aw,null)}}),autoSlippageOptIn:pe({fullScreen:!0,render(){return S.default.createElement(sw,null)}}),seedlessVerifyPinPage:pe({fullScreen:!0,presentationStyle:"fadeIn",render(){return S.default.createElement(Be,null,S.default.createElement(vf,null))}}),additionalNetworks:pe({fullScreen:!0,render:function(){return S.default.createElement(Be,null,S.default.createElement(gf,null))}})},cs=yi((e,t)=>({modals:cw,getModal:o=>t().modals[o],closeAllModals:()=>{let{modals:o}=t(),r=Object.entries(o).filter(([n,i])=>i.visibility);r.length>0&&e(n=>{let i={...n.modals};for(let[a]of r)i[a].visibility=!1;return{modals:i}})},handleShowModalVisibility:(o,r,n)=>{e(i=>{let a=i.modals[o];return n&&we.capture(n.event,n?.payload),r?{modals:{...i.modals,[o]:{...a,data:r,visibility:!0}}}:{modals:{...i.modals,[o]:{...a,visibility:!0}}}})},handleHideModalVisibility:o=>{e(r=>{let n=r.modals[o];return{modals:{...r.modals,[o]:{...n,visibility:!1}}}})}})),de=()=>{let[e,t,o,r]=cs(a=>[a.getModal,a.closeAllModals,a.handleShowModalVisibility,a.handleHideModalVisibility]),{closeAllModals:n}=Dt();function i(){t(),n&&n()}return{getModal:e,closeAllModals:i,handleShowModalVisibility:o,handleHideModalVisibility:r}},TH=({children:e})=>{let t=cs(r=>r.modals),o=(0,S.useMemo)(()=>Object.entries(t).map(([r,n])=>S.default.createElement(S.Fragment,{key:r},n.render(n?.data))),[t]);return S.default.createElement(S.Fragment,null,e,o)};var ds=24,dw=x.div`
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  z-index: 9999;
`,uw=x($i)`
  left: 16px;
  position: absolute;
`,pw=x.div`
  align-items: center;
  border-bottom: 1px solid #323232;
  display: flex;
  height: 46px;
  padding: 16px;
`,mw=x.div`
  display: flex;
  flex: 1;
  justify-content: center;
`,fw=x(W).attrs({size:16,weight:500,lineHeight:25})``,gw=x.div`
  display: flex;
  flex: 1;
  flex-direction: column;
  overflow-y: scroll;
  padding: ${e=>e.alignBody==="center"?"16px 16px 0 16px":"16px"};
  justify-content: ${e=>e.alignBody==="center"?"flex-start":"top"};
  align-items: center;
`,yw=x(W).attrs({size:22,weight:600})`
  align-self: center;
  line-height: normal;
  max-width: 85%;
`,hw=x(Ms).attrs({color:_s.grayLight,size:16})`
  align-self: center;
  line-height: normal;
  margin-top: 8px;
  max-width: 85%;
`,bw=x.img`
  align-self: center;
  margin-bottom: 16px;
  max-width: 400px;
  max-height: 140px;
`,xw=x.div`
  margin-bottom: 16px;
  text-align: center;
`,vw=x.div`
  margin: 20px 9px 48px;
`,ww=x.div`
  display: flex;
  margin-bottom: 15px;
`,Sw=x(W).attrs({size:16,weight:500,textAlign:"left"})``,Cw=x(W).attrs({color:"#999",size:14,textAlign:"left",lineHeight:20})``,Tw=x.div`
  height: ${ds}px;
  width: ${ds}px;
  flex-shrink: 0;
  margin-top: 3px;
  margin-right: 14px;
`,Pw=x(eo)`
  height: auto;
  margin: 0;
`,kw=x.a.attrs({target:"_blank",rel:"noopener noreferrer"})`
  color: ${e=>e.theme.purple};
  text-decoration: none;
  cursor: pointer;
`,Iw=x(Ms).attrs({color:_s.grayLight,size:14})`
  align-self: center;
  line-height: normal;
  margin-bottom: 16px;
  max-width: 100%;
`,Aw=({icon:e,title:t,subtitle:o})=>Oe.default.createElement(ww,null,Oe.default.createElement(Tw,null,typeof e=="string"?Oe.default.createElement("img",{src:e,width:ds,height:ds}):e),Oe.default.createElement("div",null,Oe.default.createElement(Sw,null,t),Oe.default.createElement(Cw,null,o))),cy=e=>{let{handleHideModalVisibility:t}=de(),{bodyTitle:o,bodyDescription:r,details:n,icon:i,onDismiss:a,headerTitle:s,FooterComponent:l,alignBody:u="top",footerNotei18nKey:p,footerNoteLink:f}=e,m=(0,Oe.useCallback)(()=>{t("interstitial")},[t]),g=(0,Oe.useCallback)(()=>{a?.(),m()},[m,a]);return Oe.default.createElement(dw,null,Oe.default.createElement(pw,{onClick:g},Oe.default.createElement(uw,null,Oe.default.createElement(Oi,null)),Oe.default.createElement(mw,null,Oe.default.createElement(fw,null,s))),Oe.default.createElement(gw,{alignBody:u},typeof i=="string"?Oe.default.createElement(bw,{src:i}):Oe.default.createElement(xw,null,i),Oe.default.createElement(yw,null,o),r&&typeof r=="string"?Oe.default.createElement(hw,null,r):r,n&&Oe.default.createElement(vw,null,n.map(y=>Oe.default.createElement(Aw,{key:y.title,...y})))),l&&Oe.default.createElement(Pw,{removeFooterExpansion:!0},p&&f&&Oe.default.createElement(Iw,null,Oe.default.createElement(Qu,{i18nKey:p},"Our Privacy Policy has changed. ",Oe.default.createElement(kw,{href:f},"Learn More"))),Oe.default.createElement(l,null)))};export{Hs as a,it as b,Ks as c,gy as d,hy as e,by as f,js as g,Gs as h,Qs as i,xy as j,Js as k,Ys as l,vy as m,vr as n,ro as o,wy as p,Zy as q,Hn as r,Ft as s,cf as t,Vt as u,Ca as v,De as w,O4 as x,_4 as y,ia as z,Vm as A,cy as B,ua as C,bo as D,gt as E,ef as F,tf as G,nf as H,pf as I,ff as J,C3 as K,T3 as L,P3 as M,k3 as N,I3 as O,Ia as P,Uo as Q,xo as R,Pf as S,O1 as T,Af as U,Ef as V,Ea as W,La as X,nc as Y,vo as Z,Q1 as _,Fa as $,Vf as aa,Ma as ba,Co as ca,un as da,pn as ea,Va as fa,Ha as ga,Ra as ha,Qb as ia,vn as ja,hc as ka,Eg as la,EB as ma,gx as na,LB as oa,xc as pa,Lg as qa,Dg as ra,Fg as sa,Sx as ta,es as ua,lN as va,cN as wa,dN as xa,uN as ya,zg as za,DN as Aa,Wg as Ba,Kg as Ca,qg as Da,jg as Ea,Gg as Fa,Qg as Ga,Xg as Ha,ty as Ia,ns as Ja,as as Ka,ss as La,de as Ma,TH as Na};
/*! Bundled license information:

@noble/ciphers/esm/utils.js:
  (*! noble-ciphers - MIT License (c) 2023 Paul Miller (paulmillr.com) *)
*/
//# sourceMappingURL=chunk-JD6NH5K6.js.map
