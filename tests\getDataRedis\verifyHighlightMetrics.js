import axios from "axios";
import fs from "fs";
import pLimit from "p-limit";
import logger from "../helpers/logger.js";

const limit = pLimit(10);
const API_KEY = "luQFwXgd1N1q6Jf89F1jemCSlgd1N1qm52copMw";
const API_DETAIL_URL = "https://api.dex3.ai/dev/redisearch/token";

const apiAI = axios.create({
  baseURL: "https://api.dex3.ai/dev/redis",
  headers: { "x-api-key": API_KEY },
});

const launchpads = [
  {
    name: "pump-fun",
    displayName: "pump.fun",
  },
  {
    name: "lets_bonk",
    displayName: "LetsBonk",
  },
  {
    name: "bags",
    displayName: "Bags",
  },
  {
    name: "launch_coin",
    displayName: "LaunchCoin",
  },
  {
    name: "jup_studio",
    displayName: "Jup Studio",
  },
  {
    name: "dynamic_bonding_curve",
    displayName: "Dynamic BC",
  },
  {
    name: "moonshot_dbc",
    displayName: "Moonshot",
  },
  {
    name: "moonshot",
    displayName: "Moonit",
  },
  {
    name: "boop-fun",
    displayName: "boop.fun",
  },
  {
    name: "raydium_launchpad",
    displayName: "LaunchLab",
  },
];

async function fetchTokenDetails(tokens) {
  try {
    const { data } = await axios.post(
      API_DETAIL_URL,
      { tokens },
      {
        headers: {
          "x-api-key": API_KEY,
          "Content-Type": "application/json",
        },
      }
    );
    return data;
  } catch (error) {
    logger.error("Error fetching token details:", error.message);
    return [];
  }
}

function chunkArray(array, size) {
  const result = [];
  for (let i = 0; i < array.length; i += size) {
    result.push(array.slice(i, i + size));
  }
  return result;
}

async function main() {
  try {
    let tokenDetails = [];
    const verifiedTokens = await apiAI
      .get("?type=set&key=verified_tokens")
      .then((r) => r.data);

    const totalTokens = verifiedTokens.length;

    const batches = chunkArray(verifiedTokens, 100);
    let fetchedCount = 0;

    const results = await Promise.all(
      batches.map((batch) =>
        limit(async () => {
          const details = await fetchTokenDetails(batch);
          fetchedCount += batch.length;
          logger.updateLine(
            `Fetched ${fetchedCount}/${totalTokens} verified tokens...`
          );
          return details;
        })
      )
    );
    process.stdout.write("\n");

    tokenDetails = results.flat();

    const {
      categoriesMarketCapHighlight,
      categoriesVolumeTrading24hHighlight,
    } = tokenDetails.reduce(
      (acc, token) => {
        if (Array.isArray(token.categories) && token.categories.length > 0) {
          acc.categoriesMarketCapHighlight += token.marketCap || 0;
          acc.categoriesVolumeTrading24hHighlight += token.volume24h || 0;
        }
        return acc;
      },
      {
        categoriesMarketCapHighlight: 0,
        categoriesVolumeTrading24hHighlight: 0,
      }
    );

    logger.info(
      `Categories page - Market Cap highlight: $ ${categoriesMarketCapHighlight.toLocaleString()}`
    );
    logger.info(
      `Categories page - Volume 24h highlight: $ ${categoriesVolumeTrading24hHighlight.toLocaleString()}`
    );

    const launchpadNames = launchpads.map((lp) => lp.name);

    const {
      launchpadsMarketCapHighlight,
      launchpadsVolumeTrading24hHighlight,
    } = tokenDetails.reduce(
      (acc, token) => {
        if (launchpadNames.includes(token.createdPlatform)) {
          acc.launchpadsMarketCapHighlight += token.marketCap || 0;
          acc.launchpadsVolumeTrading24hHighlight += token.volume24h || 0;
        }
        return acc;
      },
      {
        launchpadsMarketCapHighlight: 0,
        launchpadsVolumeTrading24hHighlight: 0,
      }
    );

    logger.info(
      `Launchpads page - Market Cap highlight: $ ${launchpadsMarketCapHighlight.toLocaleString(
        "en-US",
        { minimumFractionDigits: 2, maximumFractionDigits: 2 }
      )}`
    );
    logger.info(
      `Launchpads page - Volume 24h highlight: $ ${launchpadsVolumeTrading24hHighlight.toLocaleString(
        "en-US",
        { minimumFractionDigits: 2, maximumFractionDigits: 2 }
      )}`
    );

    // fs.writeFileSync(
    //   "tokenDetails.json",
    //   JSON.stringify(tokenDetails, null, 2)
    // );
  } catch (error) {
    logger.error("Error from main function", error);
  }
}

main();
