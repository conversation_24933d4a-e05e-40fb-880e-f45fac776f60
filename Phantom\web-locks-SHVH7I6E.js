import{a as A}from"./chunk-UNDMYLJW.js";import{a as P,c as T,h as y,n as p}from"./chunk-3KENBVE7.js";var L=T((B,x)=>{y();p();var{EventEmitter:M}=A(),D=P("worker_threads"),{isMainThread:K,parentPort:k}=D,b=!K,q=0,E=1,h=null,c=class{constructor(e,t="exclusive",s=null){this.name=e,this.mode=t,this.queue=[],this.owner=!1,this.trying=!1,this.buffer=s||new SharedArrayBuffer(4),this.flag=new Int32Array(this.buffer,0,1),s||Atomics.store(this.flag,0,E)}enter(e){return new Promise(t=>{this.queue.push({handler:e,resolve:t}),this.trying=!0,setTimeout(()=>{this.tryEnter()},0)})}tryEnter(){if(this.queue.length===0||Atomics.exchange(this.flag,0,q)===q)return;this.owner=!0,this.trying=!1;let{handler:t,resolve:s}=this.queue.shift();t(this).finally(()=>{this.leave(),s()})}leave(){if(!this.owner)return;Atomics.store(this.flag,0,E),this.owner=!1;let e={webLocks:!0,kind:"leave",name:this.name};h.send(e),this.tryEnter()}},u=class{constructor(e){let t=[],s=[];this.held=t,this.pending=s;for(let n of e)n.queue.length>0&&s.push(...n.queue),n.owner&&t.push(n)}},a=class{constructor(){this.collection=new Map,this.workers=new Set,b&&k.on("message",e=>{this.receive(e)})}async request(e,t,s){typeof t=="function"&&(s=t,t={});let{mode:n="exclusive",signal:o=null}=t,r=this.collection.get(e);if(!r){r=new c(e,n),this.collection.set(e,r);let{buffer:v}=r,l={webLocks:!0,kind:"create",name:e,mode:n,buffer:v};h.send(l)}let g=r.enter(s),m=null;o?(m=new Promise((v,l)=>{o.on("abort",l)}),await Promise.race([g,m])):await g,setTimeout(()=>{r.tryEnter()},0)}query(){let e=new u;return Promise.resolve(e)}attach(e){this.workers.add(e),e.on("message",t=>{for(let s of this.workers)s!==e&&s.postMessage(t);this.receive(t)})}send(e){if(b){k.postMessage(e);return}for(let t of this.workers)t.postMessage(e)}receive(e){if(!e.webLocks)return;let{kind:t,name:s,mode:n,buffer:o}=e;if(t==="create"){let r=new c(s,n,o);this.collection.set(s,r);return}if(t==="leave")for(let r of this.collection.values())r.name===s&&r.trying&&r.tryEnter()}},f=class extends Error{constructor(e){super(e),this.name="AbortError"}},d=class extends M{constructor(){super(),this.aborted=!1,this.on("abort",()=>{this.aborted=!0})}},w=class{constructor(){this.signal=new d}abort(){let e=new f("The request was aborted");this.signal.emit("abort",e)}};h=new a;x.exports={locks:h,AbortController:w}});export default L();
//# sourceMappingURL=web-locks-SHVH7I6E.js.map
