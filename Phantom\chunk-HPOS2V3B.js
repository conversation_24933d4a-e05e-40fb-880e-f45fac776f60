import{c as on}from"./chunk-XYJX6G2K.js";import{a as rn}from"./chunk-H3FFS4GT.js";import{k as tn,o as se}from"./chunk-WIQ4WVKX.js";import{a as ir}from"./chunk-OXFZHPMY.js";import{a as P,b as ur}from"./chunk-7X4NV6OJ.js";import{c as ar,f as k,h as f,i as c,n as p}from"./chunk-3KENBVE7.js";var bn=ar((eo,wn)=>{f();p();var pn=["input","select","textarea","a[href]","button","[tabindex]","audio[controls]","video[controls]",'[contenteditable]:not([contenteditable="false"])'],Fe=pn.join(","),Ue=typeof Element>"u"?function(){}:Element.prototype.matches||Element.prototype.msMatchesSelector||Element.prototype.webkitMatchesSelector;function je(e,n){n=n||{};var r=[],t=[],o=e.querySelectorAll(Fe);n.includeContainer&&Ue.call(e,Fe)&&(o=Array.prototype.slice.apply(o),o.unshift(e));var a,i,u;for(a=0;a<o.length;a++)i=o[a],vn(i)&&(u=hn(i),u===0?r.push(i):t.push({documentOrder:a,tabIndex:u,node:i}));var d=t.sort(yr).map(function(m){return m.node}).concat(r);return d}je.isTabbable=gr;je.isFocusable=br;function vn(e){return!(!mn(e)||Ir(e)||hn(e)<0)}function gr(e){if(!e)throw new Error("No node provided");return Ue.call(e,Fe)===!1?!1:vn(e)}function mn(e){return!(e.disabled||Dr(e)||Tr(e))}var wr=pn.concat("iframe").join(",");function br(e){if(!e)throw new Error("No node provided");return Ue.call(e,wr)===!1?!1:mn(e)}function hn(e){var n=parseInt(e.getAttribute("tabindex"),10);return isNaN(n)?xr(e)?0:e.tabIndex:n}function yr(e,n){return e.tabIndex===n.tabIndex?e.documentOrder-n.documentOrder:e.tabIndex-n.tabIndex}function xr(e){return e.contentEditable==="true"}function gn(e){return e.tagName==="INPUT"}function Dr(e){return gn(e)&&e.type==="hidden"}function Er(e){return gn(e)&&e.type==="radio"}function Ir(e){return Er(e)&&!Cr(e)}function Nr(e){for(var n=0;n<e.length;n++)if(e[n].checked)return e[n]}function Cr(e){if(!e.name)return!0;var n=e.ownerDocument.querySelectorAll('input[type="radio"][name="'+e.name+'"]'),r=Nr(n);return!r||r===e}function Tr(e){return e.offsetParent===null||getComputedStyle(e).visibility==="hidden"}wn.exports=je});f();p();f();p();f();p();var E=k(P()),j=k(rn());f();p();var sr=c.NODE_ENV==="production";function dr(e,n){if(!sr){if(e)return;var r="Warning: "+n;typeof console<"u"&&console.warn(r);try{throw Error(r)}catch{}}}var q=dr;f();p();var U=k(P());f();p();var ne=k(P());f();p();var be=k(P());f();p();function we(){return!!(typeof self<"u"&&self.document&&self.document.createElement)}var K=we()?be.useLayoutEffect:be.useEffect;f();p();var ye=k(P());function xe(){var e=(0,ye.useState)(Object.create(null)),n=e[1];return(0,ye.useCallback)(function(){n(Object.create(null))},[])}var an=k(ir());var Ae=function(n){var r=n.children,t=n.type,o=t===void 0?"reach-portal":t,a=n.containerRef,i=(0,ne.useRef)(null),u=(0,ne.useRef)(null),d=xe();return c.NODE_ENV!=="production"&&(0,ne.useEffect)(function(){a!=null&&(c.NODE_ENV!=="production"&&q(typeof a=="object"&&"current"in a,"@reach/portal: Invalid value passed to the `containerRef` of a `Portal`. The portal will be appended to the document body, but if you want to attach it to another DOM node you must pass a valid React ref object to `containerRef`."),c.NODE_ENV!=="production"&&q(a?a.current!=null:!0,"@reach/portal: A ref was passed to the `containerRef` prop of a `Portal`, but no DOM node was attached to it. Be sure to pass the ref to a DOM component.\n\nIf you are forwarding the ref from another component, be sure to use the React.forwardRef API. See https://reactjs.org/docs/forwarding-refs.html."))},[a]),K(function(){if(i.current){var m=i.current.ownerDocument,v=a?.current||m.body;return u.current=m?.createElement(o),v.appendChild(u.current),d(),function(){u.current&&v&&v.removeChild(u.current)}}},[o,d,a]),u.current?(0,an.createPortal)(r,u.current):(0,ne.createElement)("span",{ref:i})};c.NODE_ENV!=="production"&&(Ae.displayName="Portal");f();p();var F=k(P()),De=k(rn());f();p();var cr=["bottom","height","left","right","top","width"],lr=function(n,r){return n===void 0&&(n={}),r===void 0&&(r={}),cr.some(function(t){return n[t]!==r[t]})},Y=new Map,un,fr=function e(){var n=[];Y.forEach(function(r,t){var o=t.getBoundingClientRect();lr(o,r.rect)&&(r.rect=o,n.push(r))}),n.forEach(function(r){r.callbacks.forEach(function(t){return t(r.rect)})}),un=self.requestAnimationFrame(e)};function pr(e,n){return{observe:function(){var t=Y.size===0;Y.has(e)?Y.get(e).callbacks.push(n):Y.set(e,{rect:void 0,hasRectChanged:!1,callbacks:[n]}),t&&fr()},unobserve:function(){var t=Y.get(e);if(t){var o=t.callbacks.indexOf(n);o>=0&&t.callbacks.splice(o,1),t.callbacks.length||Y.delete(e),Y.size||cancelAnimationFrame(un)}}}}var sn=pr;f();p();function $e(e){return typeof e=="boolean"}function re(e){return!!(e&&{}.toString.call(e)=="[object Function]")}function dn(e){return typeof e=="string"}var cn=function(n){var r=n.onChange,t=n.observe,o=t===void 0?!0:t,a=n.children,i=(0,F.useRef)(null),u=Ee(i,{observe:o,onChange:r});return a({ref:i,rect:u})};c.NODE_ENV!=="production"&&(cn.displayName="Rect",cn.propTypes={children:De.default.func.isRequired,observe:De.default.bool,onChange:De.default.func});function Ee(e,n,r){var t,o;if($e(n))t=n;else{var a;t=(a=n?.observe)!=null?a:!0,o=n?.onChange}re(r)&&(o=r),c.NODE_ENV!=="production"&&((0,F.useEffect)(function(){c.NODE_ENV!=="production"&&q(!$e(n),"Passing `observe` as the second argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `observe` property as the second argument (`useRect(ref, { observe })`).\nSee https://reach.tech/rect#userect-observe")},[n]),(0,F.useEffect)(function(){c.NODE_ENV!=="production"&&q(!re(r),"Passing `onChange` as the third argument to `useRect` is deprecated and will be removed in a future version of Reach UI. Instead, you can pass an object of options with an `onChange` property as the second argument (`useRect(ref, { onChange })`).\nSee https://reach.tech/rect#userect-onchange")},[r]));var i=(0,F.useState)(e.current),u=i[0],d=i[1],m=(0,F.useRef)(!1),v=(0,F.useRef)(!1),y=(0,F.useState)(null),h=y[0],g=y[1],x=(0,F.useRef)(o);return K(function(){x.current=o,e.current!==u&&d(e.current)}),K(function(){u&&!m.current&&(m.current=!0,g(u.getBoundingClientRect()))},[u]),K(function(){if(t){var b=u;if(v.current||(v.current=!0,b=e.current),!b){c.NODE_ENV!=="production"&&console.warn("You need to place the ref");return}var D=sn(b,function(N){x.current==null||x.current(N),g(N)});return D.observe(),function(){D.unobserve()}}},[t,u,e]),h}f();p();function te(e){return we()?e?e.ownerDocument:document:null}f();p();var fn=k(P());function ln(e,n){(n==null||n>e.length)&&(n=e.length);for(var r=0,t=new Array(n);r<n;r++)t[r]=e[r];return t}function vr(e,n){if(e){if(typeof e=="string")return ln(e,n);var r=Object.prototype.toString.call(e).slice(8,-1);if(r==="Object"&&e.constructor&&(r=e.constructor.name),r==="Map"||r==="Set")return Array.from(e);if(r==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r))return ln(e,n)}}function mr(e,n){var r;if(typeof Symbol>"u"||e[Symbol.iterator]==null){if(Array.isArray(e)||(r=vr(e))||n&&e&&typeof e.length=="number"){r&&(e=r);var t=0;return function(){return t>=e.length?{done:!0}:{done:!1,value:e[t++]}}}throw new TypeError(`Invalid attempt to iterate non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}return r=e[Symbol.iterator](),r.next.bind(r)}function hr(e,n){if(e!=null)if(re(e))e(n);else try{e.current=n}catch{throw new Error('Cannot assign value "'+n+'" to ref "'+e+'"')}}function oe(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return(0,fn.useCallback)(function(t){for(var o=mr(n),a;!(a=o()).done;){var i=a.value;hr(i,t)}},n)}var B=k(bn());function _r(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function ae(){return ae=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},ae.apply(this,arguments)}var kr=["as","targetRef","position","unstable_observableRefs"],ce=(0,U.forwardRef)(function(n,r){return(0,U.createElement)(Ae,null,(0,U.createElement)(yn,ae({ref:r},n)))});c.NODE_ENV!=="production"&&(ce.displayName="Popover");var yn=(0,U.forwardRef)(function(n,r){var t=n.as,o=t===void 0?"div":t,a=n.targetRef,i=n.position,u=i===void 0?Mr:i,d=n.unstable_observableRefs,m=d===void 0?[]:d,v=_r(n,kr),y=(0,U.useRef)(null),h=Ee(y,{observe:!v.hidden}),g=Ee(a,{observe:!v.hidden}),x=oe(y,r);return Pr(a,y),(0,U.createElement)(o,ae({"data-reach-popover":"",ref:x},v,{style:ae({position:"absolute"},Sr.apply(void 0,[u,g,h].concat(m)),v.style)}))});c.NODE_ENV!=="production"&&(yn.displayName="PopoverImpl");function Sr(e,n,r){for(var t=arguments.length,o=new Array(t>3?t-3:0),a=3;a<t;a++)o[a-3]=arguments[a];return r?e.apply(void 0,[n,r].concat(o.map(function(i){return i.current}))):{visibility:"hidden"}}function xn(e,n,r){return{top:r?e.top-n.height+self.pageYOffset+"px":e.top+e.height+self.pageYOffset+"px"}}var Mr=function(n,r){if(!n||!r)return{};var t=Dn(n,r),o=t.directionRight,a=t.directionUp;return ae({left:o?n.right-r.width+self.pageXOffset+"px":n.left+self.pageXOffset+"px"},xn(n,r,a))};var uo=function(n,r){if(!n||!r)return{};var t=Dn(n,r),o=t.directionUp;return ae({width:n.width,left:n.left},xn(n,r,o))};function Dn(e,n,r,t){r===void 0&&(r=0),t===void 0&&(t=0);var o={top:e.top-n.height<0,right:self.innerWidth<e.left+n.width-r,bottom:self.innerHeight<e.bottom+n.height-t,left:e.left+e.width-n.width<0},a=o.right&&!o.left,i=o.left&&!o.right,u=o.bottom&&!o.top,d=o.top&&!o.bottom;return{directionRight:a,directionLeft:i,directionUp:u,directionDown:d}}function Pr(e,n){var r=te(e.current);function t(s){s.key==="Tab"&&n.current&&(0,B.default)(n.current).length===0||(s.key==="Tab"&&s.shiftKey?m(s)?v(s):y(s)?h(s):x(s)&&D():s.key==="Tab"&&(a()?i(s):u()?d(s):g(s)&&D()))}(0,U.useEffect)(function(){return r.addEventListener("keydown",t),function(){r.removeEventListener("keydown",t)}},[]);function o(){var s=(0,B.default)(r),l=s&&e.current?s.indexOf(e.current):-1,I=s&&s[l+1];return n.current&&n.current.contains(I||null)?!1:I}function a(){return e.current?e.current===r.activeElement:!1}function i(s){var l=n.current&&(0,B.default)(n.current);l&&l[0]&&(s.preventDefault(),l[0].focus())}function u(){var s=n.current?n.current.contains(r.activeElement||null):!1;if(s){var l=n.current&&(0,B.default)(n.current);return!!(l&&l[l.length-1]===r.activeElement)}return!1}function d(s){var l=o();l&&(s.preventDefault(),l.focus())}function m(s){if(s.shiftKey){var l=o();return s.target===l}}function v(s){var l=n.current&&(0,B.default)(n.current),I=l&&l[l.length-1];I&&(s.preventDefault(),I.focus())}function y(s){var l=n.current&&(0,B.default)(n.current);return l?l.length===0?!1:s.target===l[0]:!1}function h(s){var l;s.preventDefault(),(l=e.current)==null||l.focus()}function g(s){var l=n.current?(0,B.default)(r).filter(function(I){return!n.current.contains(I)}):null;return l?s.target===l[l.length-1]:!1}function x(s){return s.target===(0,B.default)(r)[0]}var b=[];function D(){var s=n.current&&(0,B.default)(n.current);s&&(s.forEach(function(l){b.push([l,l.tabIndex]),l.tabIndex=-1}),r.addEventListener("focusin",N))}function N(){r.removeEventListener("focusin",N),b.forEach(function(s){var l=s[0],I=s[1];l.tabIndex=I})}}f();p();var w=k(P());f();p();var Ie=k(P());var Ve=!1,Or=0,En=function(){return++Or};function In(e){var n=e||(Ve?En():null),r=(0,Ie.useState)(n),t=r[0],o=r[1];return K(function(){t===null&&o(En())},[]),(0,Ie.useEffect)(function(){Ve===!1&&(Ve=!0)},[]),t!=null?String(t):void 0}f();p();var A=k(P());f();p();var Nn=k(P());function Ne(e,n){var r=(0,Nn.createContext)(n);return c.NODE_ENV!=="production"&&(r.displayName=e),r}f();p();function le(){}function Rr(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function Q(){return Q=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},Q.apply(this,arguments)}var Lr=["element","index"];function Cn(e,n){n===void 0&&(n={});var r=[];return Ne(e,Q({descendants:r,registerDescendant:le,unregisterDescendant:le},n))}function Tn(e,n,r){var t=xe(),o=(0,A.useContext)(n),a=o.registerDescendant,i=o.unregisterDescendant,u=o.descendants,d=r??u.findIndex(function(m){return m.element===e.element});return K(function(){return e.element||t(),a(Q({},e,{index:d})),function(){i(e.element)}},[e,t,d,a,i].concat(Object.values(e))),d}function _n(){return(0,A.useState)([])}function kn(e){return(0,A.useContext)(e).descendants}function Sn(e){var n=e.context,r=e.children,t=e.items,o=e.set,a=(0,A.useCallback)(function(u){var d=u.element,m=u.index,v=Rr(u,Lr);d&&o(function(y){var h;if(m!=null)return[].concat(y,[Q({},v,{element:d,index:m})]).sort(function(b,D){return b.index-D.index});if(y.length===0)h=[Q({},v,{element:d,index:0})];else if(y.find(function(b){return b.element===d}))h=y;else{var g=y.findIndex(function(b){return!b.element||!d?!1:!!(b.element.compareDocumentPosition(d)&Node.DOCUMENT_POSITION_PRECEDING)}),x=Q({},v,{element:d,index:g});g===-1?h=[].concat(y,[x]):h=[].concat(y.slice(0,g),[x],y.slice(g))}return h.map(function(b,D){return Q({},b,{index:D})})})},[]),i=(0,A.useCallback)(function(u){u&&o(function(d){return d.filter(function(m){return u!==m.element})})},[]);return(0,A.createElement)(n.Provider,{value:(0,A.useMemo)(function(){return{descendants:t,registerDescendant:a,unregisterDescendant:i}},[t,a,i])},r)}function Mn(e,n){var r=(0,A.useContext)(e),t=r.descendants,o=n.callback,a=n.currentIndex,i=n.filter,u=n.key,d=u===void 0?"index":u,m=n.orientation,v=m===void 0?"vertical":m,y=n.rotate,h=y===void 0?!0:y,g=n.rtl,x=g===void 0?!1:g;return function(D){if(!["ArrowDown","ArrowUp","ArrowLeft","ArrowRight","PageUp","PageDown","Home","End"].includes(D.key))return;var N=a??-1,s=i?t.filter(i):t;if(!s.length)return;var l=s.findIndex(function(V){return V.index===a});function I(){var V=N===$().index;return V?h?S():s[l]:s[(l+1)%s.length]}function _(){var V=N===S().index;return V?h?$():s[l]:s[(l-1+s.length)%s.length]}function S(){return s[0]}function $(){return s[s.length-1]}switch(D.key){case"ArrowDown":if(v==="vertical"||v==="both"){D.preventDefault();var L=I();o(d==="option"?L:L[d])}break;case"ArrowUp":if(v==="vertical"||v==="both"){D.preventDefault();var ie=_();o(d==="option"?ie:ie[d])}break;case"ArrowLeft":if(v==="horizontal"||v==="both"){D.preventDefault();var ee=(x?I:_)();o(d==="option"?ee:ee[d])}break;case"ArrowRight":if(v==="horizontal"||v==="both"){D.preventDefault();var X=(x?_:I)();o(d==="option"?X:X[d])}break;case"PageUp":D.preventDefault();var de=(D.ctrlKey?_:S)();o(d==="option"?de:de[d]);break;case"Home":D.preventDefault();var ue=S();o(d==="option"?ue:ue[d]);break;case"PageDown":D.preventDefault();var me=(D.ctrlKey?I:$)();o(d==="option"?me:me[d]);break;case"End":D.preventDefault();var he=$();o(d==="option"?he:he[d]);break}}}f();p();function fe(e){return"which"in e?e.which===3:"button"in e?e.button===2:!1}f();p();var Ce=k(P());function Te(e){var n=(0,Ce.useRef)(null);return(0,Ce.useEffect)(function(){n.current=e},[e]),n.current}f();p();function _e(){for(var e=arguments.length,n=new Array(e),r=0;r<e;r++)n[r]=arguments[r];return n.filter(function(t){return t!=null}).join("--")}f();p();var ke=k(P());function Pn(e,n){var r=(0,ke.useState)(n),t=r[0],o=r[1],a=(0,ke.useCallback)(function(i){e.current=i,o(i)},[]);return[t,a]}f();p();function R(e,n){return function(r){if(e&&e(r),!r.defaultPrevented)return n(r)}}function Z(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}function C(){return C=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},C.apply(this,arguments)}var Ar=["onKeyDown","onMouseDown","id","ref"],$r=["as"],Fr=["index","isLink","onClick","onDragStart","onMouseDown","onMouseEnter","onMouseLeave","onMouseMove","onMouseUp","onSelect","disabled","onFocus","valueText","ref"],Ur=["as"],jr=["id","onKeyDown","ref"],Vr=["as"],Kr=["onBlur","portal","position","ref"],Br=["as"],On="CLEAR_SELECTION_INDEX",He="CLICK_MENU_ITEM",pe="CLOSE_MENU",Hr="OPEN_MENU_AT_FIRST_ITEM",Ke="OPEN_MENU_AT_INDEX",Rn="OPEN_MENU_CLEARED",Be="SEARCH_FOR_ITEM",J="SELECT_ITEM_AT_INDEX",Ln="SET_BUTTON_ID",Me=Cn("DropdownDescendantContext"),We=Ne("DropdownContext",{}),Wr={triggerId:null,isExpanded:!1,typeaheadQuery:"",selectionIndex:-1},Xe=function(n){var r=n.id,t=n.children,o=(0,w.useRef)(null),a=(0,w.useRef)(null),i=(0,w.useRef)(null),u=_n(),d=u[0],m=u[1],v=In(r),y=r||_e("menu",v),h=_e("menu-button",y),g=(0,w.useReducer)(Zr,C({},Wr,{triggerId:h})),x=g[0],b=g[1],D=(0,w.useRef)(!1),N=(0,w.useRef)([]),s=(0,w.useRef)(!1),l=(0,w.useRef)({x:0,y:0}),I={dispatch:b,dropdownId:y,dropdownRef:a,mouseDownStartPosRef:l,popoverRef:i,readyToSelect:s,selectCallbacks:N,state:x,triggerClickedRef:D,triggerRef:o};return(0,w.useEffect)(function(){x.isExpanded?(self.__REACH_DISABLE_TOOLTIPS=!0,self.requestAnimationFrame(function(){Se(a.current)})):self.__REACH_DISABLE_TOOLTIPS=!1},[x.isExpanded]),(0,w.createElement)(Sn,{context:Me,items:d,set:m},(0,w.createElement)(We.Provider,{value:I},re(t)?t({isExpanded:x.isExpanded,isOpen:x.isExpanded}):t))};c.NODE_ENV!=="production"&&(Xe.displayName="DropdownProvider");function ze(e){var n=e.onKeyDown,r=e.onMouseDown,t=e.id,o=e.ref,a=Z(e,Ar),i=ve(),u=i.dispatch,d=i.dropdownId,m=i.mouseDownStartPosRef,v=i.triggerClickedRef,y=i.triggerRef,h=i.state,g=h.triggerId,x=h.isExpanded,b=oe(y,o),D=$n(),N=(0,w.useMemo)(function(){return D.findIndex(function(I){return!I.disabled})},[D]);(0,w.useEffect)(function(){t!=null&&t!==g&&u({type:Ln,payload:t})},[g,u,t]);function s(I){switch(I.key){case"ArrowDown":case"ArrowUp":I.preventDefault(),u({type:Ke,payload:{index:N}});break;case"Enter":case" ":u({type:Ke,payload:{index:N}});break}}function l(I){fe(I.nativeEvent)||(m.current={x:I.clientX,y:I.clientY},x||(v.current=!0),u(x?{type:pe}:{type:Rn}))}return{data:{isExpanded:x,controls:d},props:C({},a,{ref:b,id:g||void 0,onKeyDown:R(n,s),onMouseDown:R(r,l),type:"button"})}}var Xr=(0,w.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"button":r,o=Z(e,$r),a=ze(C({},o,{ref:n})),i=a.props;return(0,w.createElement)(t,C({"data-reach-dropdown-trigger":""},i))});c.NODE_ENV!=="production"&&(Xr.displayName="DropdownTrigger");function qe(e){var n=e.index,r=e.isLink,t=r===void 0?!1:r,o=e.onClick,a=e.onDragStart,i=e.onMouseDown,u=e.onMouseEnter,d=e.onMouseLeave,m=e.onMouseMove,v=e.onMouseUp,y=e.onSelect,h=e.disabled,g=e.onFocus,x=e.valueText,b=e.ref,D=Z(e,Fr),N=ve(),s=N.dispatch,l=N.dropdownRef,I=N.mouseDownStartPosRef,_=N.readyToSelect,S=N.selectCallbacks,$=N.triggerRef,L=N.state,ie=L.selectionIndex,ee=L.isExpanded,X=(0,w.useRef)(null),de=(0,w.useState)(x||""),ue=de[0],me=de[1],he=(0,w.useCallback)(function(T){!x&&T!=null&&T.textContent&&me(T.textContent)},[x]),V=(0,w.useRef)(!1),Ge=Pn(X,null),en=Ge[0],Xn=Ge[1],zn=(0,w.useMemo)(function(){return{element:en,key:ue,disabled:h,isLink:t}},[h,en,t,ue]),H=Tn(zn,Me,n),ge=H===ie&&!h,qn=oe(b,Xn,he);S.current[H]=y;function nn(){Se($.current),y&&y(),s({type:He})}function Yn(T){fe(T.nativeEvent)||t&&(h?T.preventDefault():nn())}function Qn(T){t&&T.preventDefault()}function Jn(T){fe(T.nativeEvent)||(t?V.current=!0:T.preventDefault())}function Zn(T){var z=te(l.current);!ge&&H!=null&&!h&&(l!=null&&l.current&&l.current!==z.activeElement&&X.current!==z.activeElement&&l.current.focus(),s({type:J,payload:{index:H}}))}function Gn(T){s({type:On})}function er(T){if(!_.current){var z=8,tr=Math.abs(T.clientX-I.current.x),or=Math.abs(T.clientY-I.current.y);(tr>z||or>z)&&(_.current=!0)}!ge&&H!=null&&!h&&s({type:J,payload:{index:H,dropdownRef:l}})}function nr(){_.current=!0,!ge&&H!=null&&!h&&s({type:J,payload:{index:H}})}function rr(T){if(!fe(T.nativeEvent)){if(!_.current){_.current=!0;return}t?V.current?V.current=!1:X.current&&X.current.click():h||nn()}}return(0,w.useEffect)(function(){if(ee){var T=self.setTimeout(function(){_.current=!0},400);return function(){self.clearTimeout(T)}}else _.current=!1},[ee,_]),(0,w.useEffect)(function(){var T=te(X.current);return T.addEventListener("mouseup",z),function(){T.removeEventListener("mouseup",z)};function z(){V.current=!1}},[]),{data:{disabled:h},props:C({id:An(H),tabIndex:-1},D,{ref:qn,"data-disabled":h?"":void 0,"data-selected":ge?"":void 0,"data-valuetext":ue,onClick:R(o,Yn),onDragStart:R(a,Qn),onMouseDown:R(i,Jn),onMouseEnter:R(u,Zn),onMouseLeave:R(d,Gn),onMouseMove:R(m,er),onFocus:R(g,nr),onMouseUp:R(v,rr)})}}var zr=(0,w.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=Z(e,Ur),a=qe(C({},o,{ref:n})),i=a.props;return(0,w.createElement)(t,C({"data-reach-dropdown-item":""},i))});c.NODE_ENV!=="production"&&(zr.displayName="DropdownItem");function Ye(e){e.id;var n=e.onKeyDown,r=e.ref,t=Z(e,jr),o=ve(),a=o.dispatch,i=o.triggerRef,u=o.dropdownRef,d=o.selectCallbacks,m=o.dropdownId,v=o.state,y=v.isExpanded,h=v.triggerId,g=v.selectionIndex,x=v.typeaheadQuery,b=$n(),D=oe(u,r);(0,w.useEffect)(function(){var _=Qr(b,x);x&&_!=null&&a({type:J,payload:{index:_,dropdownRef:u}});var S=self.setTimeout(function(){return x&&a({type:Be,payload:""})},1e3);return function(){return self.clearTimeout(S)}},[a,b,x,u]);var N=Te(b.length),s=Te(b[g]),l=Te(g);(0,w.useEffect)(function(){g>b.length-1?a({type:J,payload:{index:b.length-1,dropdownRef:u}}):N!==b.length&&g>-1&&s&&l===g&&b[g]!==s&&a({type:J,payload:{index:b.findIndex(function(_){return _.key===s?.key}),dropdownRef:u}})},[u,a,b,N,s,l,g]);var I=R(function(S){var $=S.key;if(y)switch($){case"Enter":case" ":var L=b.find(function(ee){return ee.index===g});L&&!L.disabled&&(S.preventDefault(),L.isLink&&L.element?L.element.click():(Se(i.current),d.current[L.index]&&d.current[L.index](),a({type:He})));break;case"Escape":Se(i.current),a({type:pe});break;case"Tab":S.preventDefault();break;default:if(dn($)&&$.length===1){var ie=x+$.toLowerCase();a({type:Be,payload:ie})}break}},Mn(Me,{currentIndex:g,orientation:"vertical",rotate:!1,filter:function(S){return!S.disabled},callback:function(S){a({type:J,payload:{index:S,dropdownRef:u}})},key:"index"}));return{data:{activeDescendant:An(g)||void 0,triggerId:h},props:C({tabIndex:-1},t,{ref:D,id:m,onKeyDown:R(n,I)})}}var qr=(0,w.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=Z(e,Vr),a=Ye(C({},o,{ref:n})),i=a.props;return(0,w.createElement)(t,C({"data-reach-dropdown-items":""},i))});c.NODE_ENV!=="production"&&(qr.displayName="DropdownItems");function Qe(e){var n=e.onBlur,r=e.portal,t=r===void 0?!0:r,o=e.position,a=e.ref,i=Z(e,Kr),u=ve(),d=u.triggerRef,m=u.triggerClickedRef,v=u.dispatch,y=u.dropdownRef,h=u.popoverRef,g=u.state.isExpanded,x=oe(h,a);return(0,w.useEffect)(function(){if(!g)return;var b=te(h.current);function D(N){m.current?m.current=!1:Jr(h.current,N.target)||v({type:pe})}return b.addEventListener("mousedown",D),function(){b.removeEventListener("mousedown",D)}},[m,d,v,y,h,g]),{data:{portal:t,position:o,targetRef:d,isExpanded:g},props:C({ref:x,hidden:!g,onBlur:R(n,function(b){b.currentTarget.contains(b.relatedTarget)||v({type:pe})})},i)}}var Yr=(0,w.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=Z(e,Br),a=Qe(C({},o,{ref:n})),i=a.data,u=i.portal,d=i.targetRef,m=i.position,v=a.props,y={"data-reach-dropdown-popover":""};return u?(0,w.createElement)(ce,C({},v,y,{as:t,targetRef:d,position:m})):(0,w.createElement)(t,C({},v,y))});c.NODE_ENV!=="production"&&(Yr.displayName="DropdownPopover");function Qr(e,n){if(n===void 0&&(n=""),!n)return null;var r=e.find(function(t){var o,a,i;return t.disabled?!1:(o=t.element)==null||(a=o.dataset)==null||(i=a.valuetext)==null?void 0:i.toLowerCase().startsWith(n)});return r?e.indexOf(r):null}function An(e){var n=(0,w.useContext)(We),r=n.dropdownId;return e!=null&&e>-1?_e("option-"+e,r):void 0}function Se(e){e&&e.focus()}function Jr(e,n){return!!(e&&e.contains(n))}function Zr(e,n){switch(n===void 0&&(n={}),n.type){case He:return C({},e,{isExpanded:!1,selectionIndex:-1});case pe:return C({},e,{isExpanded:!1,selectionIndex:-1});case Hr:return C({},e,{isExpanded:!0,selectionIndex:0});case Ke:return C({},e,{isExpanded:!0,selectionIndex:n.payload.index});case Rn:return C({},e,{isExpanded:!0,selectionIndex:-1});case J:{var r=n.payload.dropdownRef,t=r===void 0?{current:null}:r;if(n.payload.index>=0&&n.payload.index!==e.selectionIndex){if(t.current){var o=te(t.current);t.current!==o?.activeElement&&t.current.focus()}return C({},e,{selectionIndex:n.payload.max!=null?Math.min(Math.max(n.payload.index,0),n.payload.max):Math.max(n.payload.index,0)})}return e}case On:return C({},e,{selectionIndex:-1});case Ln:return C({},e,{triggerId:n.payload});case Be:return typeof n.payload<"u"?C({},e,{typeaheadQuery:n.payload}):e;default:return e}}function ve(){return(0,w.useContext)(We)}function $n(){return kn(Me)}f();p();var W=k(P()),Fn={};function Gr(e){if(c.NODE_ENV!=="production"){var n=typeof process<"u"?c:{NODE_ENV:"development"},r=n.NODE_ENV;if(Fn[e])return;Fn[e]=!0,r==="development"&&parseInt(self.getComputedStyle(document.body).getPropertyValue("--reach-"+e),10)!==1&&console.warn("@reach/"+e+` styles not found. If you are using a bundler like webpack or parcel include this in the entry file of your app before any of your own styles:
  
      import "@reach/`+e+`/styles.css";
  
    Otherwise you'll need to include them some other way:
  
      <link rel="stylesheet" type="text/css" href="node_modules/@reach/`+e+`/styles.css" />
  
    For more information visit https://ui.reach.tech/styling.
    `)}}function Un(e){if(c.NODE_ENV!=="production"){var n=(0,W.useRef)(e);(0,W.useEffect)(function(){return void(n.current=e)},[e]),(0,W.useEffect)(function(){return Gr(n.current)},[])}}function aa(e,n,r){if(c.NODE_ENV!=="production"){var t=(0,W.useRef)(e!=null),o=(0,W.useRef)({componentName:r,controlledPropName:n});(0,W.useEffect)(function(){o.current={componentName:r,controlledPropName:n}},[r,n]),(0,W.useEffect)(function(){var a=t.current,i=o.current,u=i.componentName,d=i.controlledPropName,m=e!=null;a!==m&&console.error("A component is changing an "+(a?"":"un")+"controlled `"+d+"` state of "+u+" to be "+(a?"un":"")+"controlled. This is likely caused by the value changing from undefined to a defined value, which should not happen. Decide between using a controlled or uncontrolled "+u+` element for the lifetime of the component.
      More info: https://fb.me/react-controlled-components`)},[e])}}var Vn=k(ur());function O(){return O=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var r=arguments[n];for(var t in r)Object.prototype.hasOwnProperty.call(r,t)&&(e[t]=r[t])}return e},O.apply(this,arguments)}function G(e,n){if(e==null)return{};var r={},t=Object.keys(e),o,a;for(a=0;a<t.length;a++)o=t[a],!(n.indexOf(o)>=0)&&(r[o]=e[o]);return r}var et=["as","id","children"],nt=["as"],rt=["as"],tt=["as"],ot=["as"],at=["as","component","onSelect"],it=["portal"],ut=["as"],Pe=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?E.Fragment:r,o=e.id,a=e.children,i=G(e,et);Un("menu-button");var u=(0,E.useMemo)(function(){try{return(0,Vn.isFragment)((0,E.createElement)(t,null))}catch{return!1}},[t]),d=u?{}:O({ref:n,id:o,"data-reach-menu":""},i);return(0,E.createElement)(t,d,(0,E.createElement)(Xe,{id:o,children:a}))});c.NODE_ENV!=="production"&&(Pe.displayName="Menu",Pe.propTypes={children:j.default.oneOfType([j.default.func,j.default.node])});var Oe=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"button":r,o=G(e,nt),a=ze(O({},o,{ref:n})),i=a.data,u=i.isExpanded,d=i.controls,m=a.props;return(0,E.createElement)(t,O({"aria-expanded":u?!0:void 0,"aria-haspopup":!0,"aria-controls":d},m,{"data-reach-menu-button":""}))});c.NODE_ENV!=="production"&&(Oe.displayName="MenuButton",Oe.propTypes={children:j.default.node});var Kn=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=G(e,rt),a=qe(O({},o,{ref:n})),i=a.data.disabled,u=a.props;return(0,E.createElement)(t,O({role:"menuitem"},u,{"aria-disabled":i||void 0,"data-reach-menu-item":""}))}),Re=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=G(e,tt);return(0,E.createElement)(Kn,O({},o,{ref:n,as:t}))});c.NODE_ENV!=="production"&&(Re.displayName="MenuItem",Re.propTypes={as:j.default.any,onSelect:j.default.func.isRequired});var Je=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=G(e,ot),a=Ye(O({},o,{ref:n})),i=a.data,u=i.activeDescendant,d=i.triggerId,m=a.props;return(0,E.createElement)(t,O({"aria-activedescendant":u,"aria-labelledby":d||void 0,role:"menu"},m,{"data-reach-menu-items":""}))});c.NODE_ENV!=="production"&&(Je.displayName="MenuItems",Je.propTypes={children:j.default.node});var jn=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"a":r,o=e.component,a=e.onSelect,i=G(e,at);return st(!o,"[@reach/menu-button]: Please use the `as` prop instead of `component`"),(0,E.createElement)(Kn,O({},i,{ref:n,"data-reach-menu-link":"",as:t,isLink:!0,onSelect:a||le}))});c.NODE_ENV!=="production"&&(jn.displayName="MenuLink",jn.propTypes={as:j.default.any});var Le=(0,E.forwardRef)(function(e,n){var r=e.portal,t=r===void 0?!0:r,o=G(e,it);return(0,E.createElement)(Ze,{portal:t},(0,E.createElement)(Je,O({},o,{ref:n,"data-reach-menu-list":""})))});c.NODE_ENV!=="production"&&(Le.displayName="MenuList",Le.propTypes={children:j.default.node.isRequired});var Ze=(0,E.forwardRef)(function(e,n){var r=e.as,t=r===void 0?"div":r,o=G(e,ut),a=Qe(O({},o,{ref:n})),i=a.data,u=i.portal,d=i.targetRef,m=i.position,v=a.props,y={"data-reach-menu-popover":""};return u?(0,E.createElement)(ce,O({},v,y,{as:t,targetRef:d,position:m})):(0,E.createElement)(t,O({},v,y))});c.NODE_ENV!=="production"&&(Ze.displayName="MenuPopover",Ze.propTypes={children:j.default.node});function st(e,n){if(c.NODE_ENV!=="production"){var r=(0,E.useRef)(n);(0,E.useEffect)(function(){r.current=n},[n]),(0,E.useEffect)(function(){c.NODE_ENV!=="production"&&q(e,r.current)},[e])}}var M=k(P());var Bn=se(Le)`
  background: #000000;
  box-shadow: 0px 4px 4px rgba(0, 0, 0, 0.25);
  border-radius: 6px;
  padding: 10px 17px;
  margin-top: 4px;
  width: ${e=>e.width};

  /* this is needed for the dropdown to correctly overlay on our settings popups */
  z-index: 999;
  position: relative;
`,Hn=se.div`
  overflow: scroll;
  max-height: ${e=>e.maxHeight};
`,Wn=se(Re)`
  font-size: 16px;
  padding: 7px 0px;
  display: flex;
  color: ${e=>e.variant==="warning"?"#B5373E":"#FFFFFF"};
  justify-content: space-between;
  ${e=>{let n=e.variant==="warning"?"#FF0000":"#AB9FF2";return tn`
      &[data-selected] {
        background: inherit;
        color: ${n};
        p {
          color: ${n};
        }
      }
    `}}
`,dt=se(Wn)`
  padding: 0;
`,ct=se.div`
  width: 100%;
  margin: 6px 0px;
  border-bottom: 1px solid #323232;
`,Na=({icon:e,items:n,sections:r,onIconClick:t,className:o,children:a,dropdownWidth:i="250px",noDropdownItemPadding:u=!1,disableIconBackground:d=!1})=>{let m="330px",v=(0,M.useMemo)(()=>u?dt:Wn,[u]);return M.default.createElement(Pe,null,({isExpanded:y})=>M.default.createElement(M.default.Fragment,null,M.default.createElement(Oe,{as:"span",className:o,onClick:t},e?d?e:M.default.createElement(on,{isActive:n&&y},e):a),r?M.default.createElement(Bn,{width:i},M.default.createElement(Hn,{maxHeight:m},r.map((h,g)=>h.data.length?M.default.createElement("div",{key:h.key},h.data.map((x,b)=>M.default.createElement(v,{"data-testid":`dropdown-section-${g}-item-${b}`,key:x.key,onSelect:x.onClick,variant:x.variant},x.label)),g!==r.length-1?M.default.createElement(ct,null):null):null))):null,n?M.default.createElement(Bn,{width:i},M.default.createElement(Hn,{maxHeight:m},n.map((h,g)=>M.default.createElement(v,{"data-testid":`dropdown-item-${g}`,key:h.key,onSelect:h.onClick,variant:h.variant},h.label)))):null))};export{q as a,K as b,Ae as c,sn as d,$e as e,re as f,dn as g,te as h,oe as i,ce as j,uo as k,In as l,Ne as m,Cn as n,Tn as o,_n as p,kn as q,Sn as r,Mn as s,fe as t,_e as u,Pn as v,R as w,Un as x,aa as y,Na as z};
//# sourceMappingURL=chunk-HPOS2V3B.js.map
