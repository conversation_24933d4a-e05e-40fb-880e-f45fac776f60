{"commandAdd": "Hinzufügen", "commandAccept": "Akzeptieren", "commandApply": "<PERSON><PERSON><PERSON>", "commandApprove": "<PERSON><PERSON><PERSON><PERSON>", "commandAllow": "Erlauben", "commandBack": "Zurück", "commandBuy": "<PERSON><PERSON><PERSON>", "commandCancel": "Abbrechen", "commandClaim": "An<PERSON>ern", "commandClaimReward": "<PERSON><PERSON><PERSON><PERSON> e<PERSON>", "commandClear": "<PERSON><PERSON>", "commandClose": "Schließen", "commandConfirm": "Bestätigen", "commandConnect": "Verbinden", "commandContinue": "Fortfahren", "commandConvert": "<PERSON><PERSON><PERSON><PERSON>", "commandCopy": "<PERSON><PERSON><PERSON>", "commandCopyAddress": "<PERSON><PERSON><PERSON> k<PERSON>", "commandCopyTokenAddress": "Token-<PERSON><PERSON><PERSON> kop<PERSON>en", "commandCreate": "<PERSON><PERSON><PERSON><PERSON>", "commandCreateTicket": "Ticket erstellen", "commandDeny": "<PERSON><PERSON><PERSON><PERSON>", "commandDismiss": "Schließen", "commandDontAllow": "<PERSON>cht erlauben", "commandDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandEdit": "<PERSON><PERSON><PERSON>", "commandEditProfile": "<PERSON><PERSON>", "commandEnableNow": "Jetzt aktivieren", "commandFilter": "Filtern", "commandFollow": "Folgen", "commandHelp": "<PERSON><PERSON><PERSON>", "commandLearnMore": "<PERSON><PERSON> er<PERSON>", "commandLearnMore2": "<PERSON><PERSON> er<PERSON>", "commandMint": "<PERSON><PERSON>", "commandMore": "<PERSON><PERSON>", "commandNext": "<PERSON><PERSON>", "commandNotNow": "<PERSON><PERSON>t nicht", "commandOpen": "<PERSON><PERSON><PERSON>", "commandOpenSettings": "Einstellungen öffnen", "commandPaste": "Einfügen", "commandReceive": "<PERSON><PERSON><PERSON><PERSON>", "commandReconnect": "Verbindung erneut herstellen", "commandRecordVideo": "Video aufnehmen", "commandRequest": "Anfragen", "commandRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "commandReview": "Überprüfen", "commandRevoke": "Widerrufen", "commandSave": "Speichern", "commandScanQRCode": "QR-Code scannen", "commandSelect": "Auswählen", "commandSelectMedia": "Medium auswählen", "commandSell": "Verkaufen", "commandSend": "Senden", "commandShare": "Teilen", "commandShowBalance": "<PERSON><PERSON><PERSON><PERSON> anzeigen", "commandSign": "Signieren", "commandSignOut": "Sign Out", "commandStake": "Einsatz", "commandMintLST": "JitoSOL minten", "commandSwap": "Tauschen", "commandSwapAgain": "<PERSON><PERSON><PERSON> tauschen", "commandTakePhoto": "Foto machen", "commandTryAgain": "<PERSON><PERSON><PERSON> versuchen", "commandViewTransaction": "Transaktion ansehen", "commandReportAsNotSpam": "Als Nicht-Spam melden", "commandReportAsSpam": "Als Spam melden", "commandPin": "Anheften", "commandBlock": "Blocken", "commandUnblock": "Entblocken", "commandUnstake": "Einsatz zurücknehmen", "commandUnpin": "<PERSON><PERSON><PERSON>", "commandHide": "Ausblenden", "commandUnhide": "Einblenden", "commandBurn": "Verbrennen", "commandReport": "Melden", "commandView": "<PERSON><PERSON><PERSON>", "commandProceedAnywayUnsafe": "Trotzdem fortfahren (usicher)", "commandUnfollow": "Nicht mehr folgen", "commandUnwrap": "Auspacken", "commandConfirmUnsafe": "Bestätigen (unsicher)", "commandYesConfirmUnsafe": "<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> (unsicher)", "commandConfirmAnyway": "Trotzdem bestätigen", "commandReportIssue": "Ein Problem melden", "commandSearch": "<PERSON><PERSON>", "commandShowMore": "<PERSON><PERSON> anzeigen", "commandShowLess": "<PERSON><PERSON> anzeigen", "pastParticipleClaimed": "<PERSON><PERSON><PERSON><PERSON>", "pastParticipleCompleted": "Abgeschlossen", "pastParticipleCopied": "<PERSON><PERSON><PERSON>", "pastParticipleDone": "<PERSON><PERSON><PERSON>", "pastParticipleDisabled": "Deaktiviert", "pastParticipleRequested": "Ang<PERSON><PERSON><PERSON>", "nounName": "Name", "nounNetwork": "Netzwerk", "nounNetworkFee": "Netzwerkgebühr", "nounSymbol": "Symbol", "nounType": "Art", "nounDescription": "Beschreibung", "nounYes": "<PERSON>a", "nounNo": "<PERSON><PERSON>", "amount": "Betrag", "limit": "Limit", "new": "<PERSON>eu", "gotIt": "Verstanden", "internal": "Intern", "reward": "<PERSON><PERSON><PERSON><PERSON>", "seeAll": "Alle anzeigen", "seeLess": "<PERSON><PERSON> anzeigen", "viewAll": "Alle anzeigen", "homeTab": "Start", "collectiblesTab": "Sammelobjekte", "swapTab": "Tauschen", "activityTab": "Aktivität", "exploreTab": "Erkunden", "accountHeaderConnectedInterpolated": "Sie sind verbunden mit {{origin}}", "accountHeaderConnectedToSite": "Sie sind verbunden mit dieser Seite", "accountHeaderCopyToClipboard": "In die Zwischenablage kopieren", "accountHeaderNotConnected": "Sie sind nicht verbunden mit", "accountHeaderNotConnectedInterpolated": "Sie sind nicht verbunden mit {{origin}}", "accountHeaderNotConnectedToSite": "Sie sind nicht verbunden mit dieser Seite", "accountWithoutEnoughSolActionButtonCancel": "Abbrechen", "accountWithoutEnoughSolPrimaryText": "Nicht genügend SOL", "accountWithoutEnoughSolSecondaryText": "Ein Konto, das an dieser Transaktion beteiligt ist, hat nicht genügend SOL. Das Konto kann Ihr eigenes oder das einer anderen Person sein. Diese Transaktion wird rückgängig gemacht, wenn sie eingereicht wird.", "accountSwitcher": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "addAccountHardwareWalletPrimaryText": "Hardware Wallet verbinden", "addAccountHardwareWalletSecondaryText": "Verwenden Sie Ihr Ledger-Hardware-Wallet", "addAccountHardwareWalletSecondaryTextMobile": "<PERSON>utzen Sie Ihr {{supportedHardwareWallets}}-Wallet", "addAccountSeedVaultWalletPrimaryText": "Seed <PERSON> verbinden", "addAccountSeedVaultWalletSecondaryText": "Verwenden eines Wallets von <PERSON>", "addAccountImportSeedPhrasePrimaryText": "Geheime Recovery-Phrase importieren", "addAccountImportSeedPhraseSecondaryText": "<PERSON><PERSON><PERSON> von einem anderen Wallet importieren", "addAccountImportWalletPrimaryText": "Privaten Schlüssel importieren", "addAccountImportWalletSecondaryText": "Ein Einzel-Chain-Konto importieren", "addAccountImportWalletSolanaSecondaryText": "Einen privaten Solana-Schlüssel importieren", "addAccountLimitReachedText": "Sie haben die maximale An<PERSON>hl von {{accountsCount}} <PERSON><PERSON><PERSON> in Phantom erreicht. Bitte entfernen Sie ungenutzte Konten, bevor Sie weitere Konten hinzufügen.", "addAccountNoSeedAvailableText": "Sie haben keine Seed-Ph<PERSON> verfügbar. Bitte importieren Sie einen bestehenden Seed, um ein Konto zu erstellen.", "addAccountNewWalletPrimaryText": "Neues Konto erstellen", "addAccountNewWalletSecondaryText": "<PERSON>eue Wallet-<PERSON><PERSON><PERSON> gene<PERSON>", "addAccountNewMultiChainWalletSecondaryText": "Ein neues Multi-Chain-Konto hinzufügen", "addAccountNewSingleChainWalletSecondaryText": "Neues Konto hinzufügen", "addAccountPrimaryText": "Wallet hinzufügen/verbinden", "addAccountSecretPhraseLabel": "Geheime Phrase", "addAccountSeedLabel": "Seed", "addAccountSeedIDLabel": "Seed-ID", "addAccountSecretPhraseDefaultLabel": "Geheime Phrase {{number}}", "addAccountPrivateKeyDefaultLabel": "<PERSON><PERSON> {{number}}", "addAccountZeroAccountsForSeed": "0 Konten", "addAccountShowAccountForSeed": "1 Konto anzeigen", "addAccountShowAccountsForSeed": "{{numOfAccounts}} Konten anzeigen", "addAccountHideAccountForSeed": "1 Konto ausblenden", "addAccountHideAccountsForSeed": "{{numOfAccounts}} Konten ausblenden", "addAccountSelectSeedDescription": "Ihr neues Konto wird aus dieser geheimen Phrase erstellt", "addAccountNumAccountsForSeed": "{{numOfAccounts}} Konten", "addAccountOneAccountsForSeed": "1 Konto", "addAccountGenerateAccountFromSeed": "<PERSON><PERSON> er<PERSON>", "addAccountReadOnly": "<PERSON><PERSON><PERSON> be<PERSON>", "addAccountReadOnlySecondaryText": "Verfolgen Sie jede öffentliche Wallet-Adresse", "addAccountSolanaAddress": "Solana<PERSON><PERSON><PERSON><PERSON>", "addAccountEVMAddress": "EVM-Adresse", "addAccountBitcoinAddress": "Bitcoin-Adresse", "addAccountCreateSeedTitle": "Neues Konto erstellen", "addAccountCreateSeedExplainer": "Ihr Wallet hat noch keine geheime Phrase! Um ein neues Wallet zu erstellen, generieren wir für Sie eine Recovery-Phrase. Schreiben Sie diese auf und behalten Sie sie für sich.", "addAccountSecretPhraseHeader": "Ihre geheime Phrase", "addAccountNoSecretPhrases": "<PERSON><PERSON> geheimen Phrasen verfügbar", "addAccountImportAccountActionButtonImport": "Importieren", "addAccountImportAccountDuplicatePrivateKey": "Dieses <PERSON> existiert in Ihrem Wallet bereits", "addAccountImportAccountIncorrectFormat": "Falsches Format", "addAccountImportAccountInvalidPrivateKey": "Ungültiger privater <PERSON><PERSON><PERSON><PERSON>", "addAccountImportAccountName": "Name", "addAccountImportAccountPrimaryText": "Privaten Schlüssel importieren", "addAccountImportAccountPrivateKey": "Private<PERSON><PERSON><PERSON>", "addAccountImportAccountPublicKey": "Adresse oder Domain", "addAccountImportAccountPrivateKeyRequired": "Privater <PERSON><PERSON><PERSON><PERSON> ist erforderlich", "addAccountImportAccountNameRequired": "Name ist erforderlich", "addAccountImportAccountPublicKeyRequired": "Öffentliche Adresse ist erforderlich", "addAccountImportAccountDuplicateAddress": "Diese Adresse existiert in Ihrem Wallet bereits", "addAddressAddressAlreadyAdded": "<PERSON>resse wurde bereits hinzugefügt", "addAddressAddressAlreadyExists": "Adresse existiert bereits", "addAddressAddressInvalid": "Ungültige Adresse", "addAddressAddressIsRequired": "<PERSON>resse ist erforderlich", "addAddressAddressPlaceholder": "<PERSON><PERSON><PERSON>", "addAddressLabelIsRequired": "Label ist erforderlich", "addAddressLabelPlaceholder": "Label", "addAddressPrimaryText": "<PERSON><PERSON><PERSON>", "addAddressToast": "<PERSON><PERSON><PERSON>", "createAssociatedTokenAccountCostLabelInterpolated": "Dies kostet {{solAmountFormatted}} SOL", "createAssociatedTokenAccountErrorAccountExists": "<PERSON>e haben dieses <PERSON>ken-Konto bereits", "createAssociatedTokenAccountErrorInsufficientFunds": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "createAssociatedTokenAccountErrorInvalidMint": "Ungültige Mint-Adresse", "createAssociatedTokenAccountErrorInvalidName": "Ungültiger Name", "createAssociatedTokenAccountErrorInvalidSymbol": "Ungültiges Symbol", "createAssociatedTokenAccountErrorUnableToCreateMessage": "Wir konnten Ihr Token-Konto nicht erstellen. Bitte versuchen Sie es erneut.", "createAssociatedTokenAccountErrorUnableToCreateTitle": "Token-Konto konnte nicht erstellt werden", "createAssociatedTokenAccountErrorUnableToSendMessage": "Wir konnten Ihre Transaktion nicht versenden.", "createAssociatedTokenAccountErrorUnableToSendTitle": "Senden der Transaktion fehlgeschlagen", "createAssociatedTokenAccountInputPlaceholderMint": "Mint<PERSON><PERSON><PERSON><PERSON>", "createAssociatedTokenAccountInputPlaceholderName": "Name", "createAssociatedTokenAccountInputPlaceholderSymbol": "Symbol", "createAssociatedTokenAccountLoadingMessage": "Wir erstellen Ihr Token-Konto.", "createAssociatedTokenAccountLoadingTitle": "Token-Konto wird erstellt", "createAssociatedTokenAccountPageHeader": "Token-Konto erstellen", "createAssociatedTokenAccountSuccessMessage": "<PERSON>hr Token-Konto wurde erfolgreich erstellt!", "createAssociatedTokenAccountSuccessTitle": "Token-Konto erstellt", "createAssociatedTokenAccountViewTransaction": "Transaktion ansehen", "assetDetailRecentActivity": "Neueste Aktivität", "assetDetailStakeSOL": "SOL einsetzen", "assetDetailUnknownToken": "Unbekannter Token", "assetDetailUnwrapAll": "Alle auspacken", "assetDetailUnwrappingSOL": "Entpacke SOL", "assetDetailUnwrappingSOLFailed": "SOL-Auspacken fehlgeschlagen", "assetDetailViewOnExplorer": "Auf {{explorer}} an<PERSON><PERSON>", "assetDetailViewOnExplorerDefaultExplorer": "Explorer", "assetDetailSaveToPhotos": "In Fotos speichern", "assetDetailSaveToPhotosToast": "In Fotos gespeichert", "assetDetailPinCollection": "Sammlung anheften", "assetDetailUnpinCollection": "<PERSON><PERSON><PERSON>", "assetDetailHideCollection": "Sammlung ausblenden", "assetDetailUnhideCollection": "Sammlung anzeigen", "assetDetailTokenNameLabel": "Token-Name", "assetDetailNetworkLabel": "Netzwerk", "assetDetailAddressLabel": "<PERSON><PERSON><PERSON>", "assetDetailPriceLabel": "Pre<PERSON>", "collectibleDetailSetAsAvatar": "Als Avatar festlegen", "collectibleDetailSetAsAvatarSingleWorkAlt": "Avatar", "collectibleDetailSetAsAvatarSuccess": "Avatar festgelegt", "collectibleDetailShare": "Sammelobjekt teilen", "assetDetailTokenAddressCopied": "<PERSON><PERSON><PERSON>", "assetDetailStakingLabel": "Einsatz", "assetDetailAboutLabel": "Über {{fungibleName}}", "assetDetailPriceDetail": "Preisdetails", "assetDetailHighlights": "Highlights", "assetDetailAllTimeReturn": "Gesamtrendite", "assetDetailAverageCost": "Durchschnittspreis", "assetDetailPriceHistoryUnavailable": "Der Preisverlauf für diesen Token ist nicht verfügbar", "assetDetailPriceHistoryInsufficientData": "Der Preisverlauf ist für diesen Zeitraum nicht verfügbar", "assetDetailPriceDataUnavailable": "Preisdaten nicht verfügbar", "assetDetailPriceHistoryError": "Fehler beim Abrufen des Preisverlaufs", "assetDetailPriceHistoryNow": "Now", "assetDetailTimeFrame1D": "1 T", "assetDetailTimeFrame24h": "24h-Pre<PERSON>", "assetDetailTimeFrame1W": "1 W", "assetDetailTimeFrame1M": "1 M", "assetDetailTimeFrameYTD": "YTD", "assetDetailTimeFrameAll": "ALLE", "sendAssetAmountLabelInterpolated": "Verfügbar {{amount}} {{tokenSymbol}}", "fiatRampQuotes": "<PERSON><PERSON><PERSON>", "fiatRampNewQuote": "<PERSON><PERSON><PERSON>", "assetListSelectToken": "Token wählen", "assetListSearch": "Suche ...", "assetListUnknownToken": "Unbekannter Token", "buyFlowHealthWarning": "Bei einigen unserer Zahlungsanbieter herrscht ein hohes Traffic-Aufkommen. Einzahlungen können sich um mehrere Stunden verzögern.", "assetVisibilityUnknownToken": "Unbekannter Token", "buyAssetInterpolated": "{{tokenSymbol}} kaufen", "buyAssetScreenMaxPurchasePriceInterpolated": "Der maximale Kaufpreis beträgt {{amount}}", "buyAssetScreenMinPurchasePriceInterpolated": "Der Mindestkaufpreis beträgt {{amount}}", "buyNoAssetsAvailable": "Keine Ethereum- oder Polygon-Vermögenswerte verfügbar", "buyThirdPartyScreenPaymentMethodSelector": "<PERSON><PERSON><PERSON><PERSON> mit", "buyThirdPartyScreenPaymentMethod": "Zahlungsmethode wählen", "buyThirdPartyScreenChoseQuote": "Geben Si<PERSON> einen gültigen Betrag für ein Angebot ein", "buyThirdPartyScreenProviders": "<PERSON><PERSON><PERSON>", "buyThirdPartyScreenPaymentMethodTitle": "Zahlungsmethoden", "buyThirdPartyScreenPaymentMethodEmptyState": "In Ihrer Region sind keine Zahlungsmethoden verfügbar", "buyThirdPartyScreenPaymentMethodFooter": "Zahlungen werden von Netzwerkpartnern durchgeführt. Die Gebühren können variieren. Einige Zahlungsarten sind in Ihrer Region nicht verfügbar.", "buyThirdPartyScreenProvidersEmptyState": "In Ihrer Region sind keine Anbieter verfügbar", "buyThirdPartyScreenLoadingQuote": "<PERSON><PERSON> …", "buyThirdPartyScreenViewQuote": "<PERSON><PERSON><PERSON> an<PERSON>", "gasEstimationErrorWarning": "Es gab ein Problem bei der Schätzung der Gebühr für diese Transaktion. Sie könnte fehlschlagen.", "gasEstimationCouldNotFetch": "Die Gas-Abschätzung konnte nicht abgerufen werden", "networkFeeCouldNotFetch": "Die Netzwerkgebühr konnte nicht abgerufen werden", "nativeTokenBalanceErrorWarning": "Es gab ein Problem beim Abrufen Ihres Token-Guthabens für diese Transaktion. Sie könnte fehlschlagen.", "blocklistOriginCommunityDatabaseInterpolated": "Diese Seite wurde als Teil einer <1>von der Gemeinschaft gepflegten Datenbank</1> bekan<PERSON>-Webseiten und Betrugsversuche gekennzeichnet. Wenn <PERSON> glauben, dass die Seite fälschlicherweise gekennzeichnet wurde, <3>melden bitte Sie einen Fehler</3>.", "blocklistOriginDomainIsBlocked": "{{domainName}} ist blockiert!", "blocklistOriginIgnoreWarning": "<PERSON><PERSON> ignorier<PERSON>, <PERSON>ch möchte trotzdem zu {{domainName}}.", "blocklistOriginSiteIsMalicious": "Phantom ist der Meinung, dass diese Website bösartig ist und nicht sicher verwendet werden kann.", "blocklistOriginThisDomain": "diese Domain", "blocklistProceedAnyway": "<PERSON>nung ignorieren, trotzdem fortfahren", "maliciousTransactionWarning": "Phantom ist der Meinung, dass diese Transaktion bösartig ist und nicht sicher signiert werden kann. Wir haben die Möglichkeit, sie zu signieren, deakti<PERSON>t, um Si<PERSON> und Ihr Geld zu schützen.", "maliciousTransactionWarningIgnoreWarning": "<PERSON>nung ignorieren, trotzdem fortfahren", "maliciousTransactionWarningTitle": "Transaktion angezeigt!", "maliciousRequestBlockedTitle": "<PERSON><PERSON><PERSON> block<PERSON>t", "maliciousRequestWarning": "Diese Website wurde als bösartig eingestuft. Sie versucht möglicherweise, <PERSON>hr Geld zu stehlen oder Sie dazu zu bringen, eine betrügerische Anfrage zu genehmigen.", "maliciousSignatureRequestBlocked": "<PERSON><PERSON> Ihrer Sicherheit hat Phantom diese Anfrage blockiert.", "maliciousRequestBlocked": "<PERSON><PERSON> Ihrer Sicherheit hat Phantom diese Anfrage blockiert.", "maliciousRequestFrictionDescription": "Das Fortfahren ist unsicher, deshalb hat Phantom diese Anfrage blockiert. Sie sollten diesen Dialog schließen.", "maliciousRequestAcknowledge": "Mir ist bewusst, dass ich durch die Nutzung dieser Website mein gesamtes Guthaben verlieren kann.", "maliciousRequestAreYouSure": "Sind sie sicher?", "siwErrorPopupTitle": "Ungültige Signaturanfrage", "siwParseErrorDescription": "Die Signaturanfrage der App kann aufgrund einer ungültigen Formatierung nicht angezeigt werden.", "siwVerificationErrorDescription": "Es gab 1 oder mehrere Fehler bei der Anfrage der Nachrichtensignatur. Vergewissern Sie sich zu Ihrer Sicherheit, dass Sie die richtige Anwendung verwenden und versuchen Sie es erneut.", "siwErrorPagination": "{{n}} von {{total}}", "siwErrorMessage_ADDRESS_MISMATCH": "Warnung: Die Adresse der Anwendung stimmt nicht mit der angegebenen Adresse für die Signatur überein.", "siwErrorMessage_DOMAIN_MISMATCH": "Warnung: Die Domain der Anwendung stimmt nicht mit der für die Verifizierung angegebenen Domain überein.", "siwErrorMessage_URI_MISMATCH": "Warnung: URI-Hostname stimmt nicht mit der Domain überein.", "siwErrorMessage_CHAIN_ID_MISMATCH": "Warnung: Die Chain-ID stimmt nicht mit der angegebenen Chain-ID für die Verifizierung überein.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Warnung: Das Ausstellungsdatum der Nachricht liegt zu weit in der Vergangenheit.", "siwErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Warnung: Das Ausstellungsdatum der Nachricht liegt zu weit in der Zukunft.", "siwErrorMessage_EXPIRED": "Warnung: Die Nachricht ist abgelaufen.", "siwErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Warnung: <PERSON> Nachricht läuft vor der Ausgabe ab.", "siwErrorMessage_VALID_AFTER_EXPIRATION": "Achtung: <PERSON> Nachricht läuft ab, bevor sie gültig wird.", "siwErrorShowErrorDetails": "Fehlerdetails anzeigen", "siwErrorHideErrorDetails": "Fehlerdetails ausblenden", "siwErrorIgnoreWarning": "<PERSON>nung ignorieren, trotzdem fortfahren", "siwsTitle": "Anmeldeanfrage", "siwsPermissions": "Genehmigungen", "siwsAgreement": "Nachricht", "siwsAdvancedDetails": "Erweiterte Details", "siwsAlternateStatement": "{{domain}} <PERSON><PERSON><PERSON><PERSON>, dass Si<PERSON> sich mit I<PERSON><PERSON>-Konto anmelden:\n{{address}}", "siwsFieldLable_domain": "Domain", "siwsFieldLable_address": "<PERSON><PERSON><PERSON>", "siwsFieldLable_uri": "URI", "siwsFieldLable_version": "Version", "siwsFieldLable_chainId": "Chain-ID", "siwsFieldLable_nonce": "Schlüssel", "siwsFieldLable_issuedAt": "Ausgestellt am", "siwsFieldLable_expirationTime": "Läuft ab am", "siwsFieldLable_requestId": "Anfrage-ID", "siwsFieldLable_resources": "Ressourcen", "siwsVerificationErrorDescription": "Diese Anmeldeanfrage ist ungültig. Dies bedeutet entweder, dass die Website unsicher ist oder dass der Entwickler beim Senden der Anfrage einen Fehler gemacht hat.", "siwsErrorNumIssues": "{{n}} Probleme", "siwsErrorMessage_CHAIN_ID_MISMATCH": "Diese Chain-ID stimmt nicht mit dem Netzwerk überein, in dem <PERSON> sich befinden.", "siwsErrorMessage_DOMAIN_MISMATCH": "Diese Domain ist nicht diejenige, bei der Sie sich anmelden.", "siwsErrorMessage_URI_MISMATCH": "Diese URI ist nicht dieje<PERSON>ge, bei der Si<PERSON> sich anmelden.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_PAST": "Das Ausstellungsdatum der Nachricht liegt zu weit in der Vergangenheit.", "siwsErrorMessage_ISSUED_TOO_FAR_IN_THE_FUTURE": "Das Ausstellungsdatum der Nachricht liegt zu weit in der Zukunft.", "siwsErrorMessage_EXPIRED": "Die Nachricht ist abgelaufen.", "siwsErrorMessage_EXPIRES_BEFORE_ISSUANCE": "Die Nachricht läuft vor der Ausgabe ab.", "siwsErrorMessage_VALID_AFTER_EXPIRATION": "Die Nachricht läuft ab, bevor sie gültig wird.", "changeLockTimerPrimaryText": "Timer für automatische Sperre", "changeLockTimerSecondaryText": "Wie lange sollten wir warten, Ihr Wallet zu sperren, wenn nicht benutzt worden ist?", "changeLockTimerToast": "Timer für automatische Sperre aktualisiert", "changePasswordConfirmNewPassword": "Neues Passwort bestätigen", "changePasswordCurrentPassword": "Aktuelles Passwort", "changePasswordErrorIncorrectCurrentPassword": "Falsches aktuelles Passwort", "changePasswordErrorGeneric": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal", "changePasswordNewPassword": "Neues Passwort", "changePasswordPrimaryText": "Passwort ändern", "changePasswordToast": "Passwort aktualisiert", "collectionsSpamCollections": "Spam-Kollektionen", "collectionsHiddenCollections": "Ausgeblendete Sammlungen", "collectiblesReportAsSpam": "Als Spam melden", "collectiblesReportAsSpamAndHide": "Als Spam melden und ausblenden", "collectiblesReportAsNotSpam": "Als Nicht-Spam melden", "collectiblesReportAsNotSpamAndUnhide": "Nicht-Spam einblenden und melden", "collectiblesReportNotSpam": "<PERSON><PERSON>", "collectionsManageCollectibles": "<PERSON><PERSON> von Sammelobjekten verwalten", "collectibleDetailDescription": "Beschreibung", "collectibleDetailProperties": "Eigenschaften", "collectibleDetailOrdinalInfo": "Ordinale Informationen", "collectibleDetailRareSatsInfo": "Rare-Sats-Infos", "collectibleDetailSatsInUtxo": "Sats in UTXO", "collectibleDetailSatsInUtxoValue": "{{satsInUtxo}} Sats", "collectibleDetailSatNumber": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "collectibleDetailSatName": "Sat-Name", "collectibleDetailInscriptionId": "Beschriftungs-ID", "collectibleDetailInscriptionNumber": "Beschriftungsnummer", "collectibleDetailStandard": "Standard", "collectibleDetailCreated": "<PERSON><PERSON><PERSON><PERSON>", "collectibleDetailViewOnExplorer": "Auf {{explorer}} an<PERSON><PERSON>", "collectibleDetailList": "Liste", "collectibleDetailSellNow": "<PERSON>erkauf<PERSON> für {{amount}} {{symbol}}", "collectibleDetailUtxoSplitterCtaTitle": "Überschüssige Bitcoin freigeben", "collectibleDetailUtxoSplitterCtaSubtitle": "Sie haben {{value}} BTC zum Freischalten", "collectibleDetailUtxoSplitterModalCtaTitle": "Rare Sats", "collectibleDetailUtxoSplitterModalCtaSubtitle": "Um Ihr Guthaben zu schütz<PERSON>, ver<PERSON><PERSON><PERSON> wir, dass BTC in UTXOs mit Rare Sats verschickt werden. Verwenden Sie den UTXO-<PERSON><PERSON> von Magic Eden, um {{value}} BTC aus Ihren Rare Sats freizugeben.", "collectibleDetailUtxoSplitterModalCtaButton": "UTXO-Splitter verwenden", "collectibleDetailEasilyAccept": "Höchstes Angebot annehmen", "collectibleDetailSatsCount_one": "{{count}} Sat", "collectibleDetailSatsCount_other": "{{count}} Sats", "collectibleDetailSpamOverlayDescription": "<PERSON>ses Sammelobjekt wurde ausgeblendet, weil <PERSON> glaubt, dass es sich um Spam handelt.", "collectibleDetailSpamOverlayReveal": "Sammelobjekt anzeigen", "collectibleBurnTermsOfService": "<PERSON>ch verstehe, dass dies nicht rückgängig gemacht werden kann.", "collectibleBurnTitleWithCount_one": "Token verbrennen", "collectibleBurnTitleWithCount_other": "Token verbrennen", "collectibleBurnDescriptionWithCount_one": "Durch diese Aktion wird dieser Token dauerhaft zerstört und aus Ihrem Wallet entfernt.", "collectibleBurnDescriptionWithCount_other": "Durch diese Aktion werden diese Token dauerhaft zerstört und aus Ihrem Wallet entfernt.", "collectibleBurnTokenWithCount_one": "Token", "collectibleBurnTokenWithCount_other": "Token", "collectibleBurnCta": "Verbrennen", "collectibleBurnRebate": "<PERSON><PERSON><PERSON>", "collectibleBurnRebateTooltip": "<PERSON><PERSON><PERSON> das Verbrennen dieses Tokens wird automatisch ein kleiner Betrag von SOL in Ihr Wallet eingezahlt.", "collectibleBurnNetworkFee": "Netzwerkgebühr", "collectibleBurnNetworkFeeTooltip": "Betrag, den das Solana-Netzwerk zur Abwicklung der Transaktion benötigt", "unwrapButtonSwapTo": "<PERSON><PERSON> zu {{chainSymbol}}", "unwrapButtonWithdrawFrom": "<PERSON><PERSON><PERSON><PERSON> von {{withdrawalSource}} für {{chainSymbol}}", "unwrapModalEstimatedTime": "Geschätzte Zeit", "unwrapModalNetwork": "Netzwerk", "unwrapModalNetworkFee": "Netzwerkgebühr", "unwrapModalTitle": "Zusammenfassung", "unsupportedChain": "Nicht unterstützte Chain", "unsupportedChainDescription": "Anscheinend unterstützen wir {{action}} für das Netzwerk {{chainName}} nicht.", "networkFeesTooltipLabel": "Netzwerkgebühren von {{chainName}}", "networkFeesTooltipDescription": "G<PERSON><PERSON><PERSON> von {{chainName}} sind von mehreren Faktoren abhängig. Sie können sie anpassen, um Ihre Transaktion schneller (teurer) oder langsamer (billiger) zu machen.", "burnStatusErrorTitleWithCount_one": "Token konnte nicht verbrannt werden", "burnStatusErrorTitleWithCount_other": "Token konnten nicht verbrannt werden", "burnStatusSuccessTitleWithCount_one": "Token verbrannt!", "burnStatusSuccessTitleWithCount_other": "Token verbrannt!", "burnStatusLoadingTitleWithCount_one": "Verbrenne Token …", "burnStatusLoadingTitleWithCount_other": "Verbrenne Token …", "burnStatusErrorMessageWithCount_one": "Dieser To<PERSON> konnte nicht verbrannt werden. Bitte versuchen Si<PERSON> es später noch einmal.", "burnStatusErrorMessageWithCount_other": "Diese To<PERSON> konnten nicht verbrannt werden. Bitte versuchen Si<PERSON> es später noch einmal.", "burnStatusSuccessMessageWithCount_one": "Dieser Token wurde dauerhaft zerstört und {{rebateAmount}} SOL wurden in Ihr Wallet eingezahlt.", "burnStatusSuccessMessageWithCount_other": "Diese Token wurden dauerhaft zerstört und {{rebateAmount}} SOL wurden in Ihr Wallet eingezahlt.", "burnStatusLoadingMessageWithCount_one": "Dieser Token wird dauerhaft zerstört und {{rebateAmount}} SOL werden in Ihr Wallet eingezahlt.", "burnStatusLoadingMessageWithCount_other": "Diese Token werden dauerhaft zerstört und {{rebateAmount}} SOL werden in Ihr Wallet eingezahlt.", "burnStatusViewTransactionText": "Transaktion ansehen", "collectibleDisplayLoading": "Lade ...", "collectiblesNoCollectibles": "<PERSON><PERSON>", "collectiblesPrimaryText": "Ihre Sammelobjekte", "collectiblesReceiveCollectible": "Sammelobjekte erhalten", "collectiblesUnknownCollection": "Unbekannte Sammlung", "collectiblesUnknownCollectible": "Unbekanntes Sammelobjekt", "collectiblesUniqueHolders": "Einzigartige Inhaber", "collectiblesSupply": "<PERSON><PERSON><PERSON>", "collectiblesUnknownTokens": "Unbekannte Token", "collectiblesNrOfListed": "{{ nrOfListed }} gelistet", "collectiblesListed": "<PERSON><PERSON><PERSON><PERSON>", "collectiblesMintCollectible": "Sammelobjekt minten", "collectiblesYouMint": "<PERSON>e minten", "collectiblesMintCost": "Mintkosten", "collectiblesMintFail": "Minting fehlgeschlagen", "collectiblesMintFailMessage": "Es gab ein Problem beim Minting Ihres Sammelobjekts. Bitte versuchen Sie es erneut.", "collectiblesMintCostFree": "<PERSON><PERSON><PERSON>", "collectiblesMinting": "Minting ...", "collectiblesMintingMessage": "Ihr Sammelobjekt wird gemintet", "collectiblesMintShareSubject": "<PERSON><PERSON> sich das an", "collectiblesMintShareMessage": "Das habe ich auf @phantom gemintet!", "collectiblesMintSuccess": "Minting erfolg<PERSON>ich", "collectiblesMintSuccessMessage": "Ihr Sammelobjekt wurde gemintet", "collectiblesMintSuccessQuestMessage": "Sie haben die Anforderungen für einen Phantomauftrag erfüllt. Tip<PERSON> Sie auf „Belohnung einfordern“, um Ihr kostenloses Sammelobjekt zu erhalten.", "collectiblesMintRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "collectiblesMintMaxLengthErrorMessage": "<PERSON><PERSON> Länge überschritten", "collectiblesMintSafelyDismiss": "<PERSON><PERSON> kö<PERSON>n dieses Fenster nun schließen.", "collectiblesTrimmed": "Wir haben das Limit für die Anzahl der Sammelobjekte erreicht, die im Moment angezeigt werden können.", "collectiblesNonTransferable": "Nicht übertragbar", "collectiblesNonTransferableYes": "<PERSON>a", "collectiblesSellOfferDetails": "Angebotsdetails", "collectiblesSellYouSell": "<PERSON>e verkaufen", "collectiblesSellGotIt": "Verstanden", "collectiblesSellYouReceive": "<PERSON>e erhalten", "collectiblesSellOffer": "<PERSON><PERSON><PERSON>", "collectiblesSoldCollectible": "Sammelobjekt verkauft", "collectiblesSellMarketplace": "Marktplatz", "collectiblesSellCollectionFloor": "Sammlung Mindestpreis", "collectiblesSellDifferenceFromFloor": "Differenz zum Mindestpreis", "collectiblesSellLastSalePrice": "Letzter Verkauf", "collectiblesSellEstimatedFees": "Geschätzte Gebühren", "collectiblesSellEstimatedProfitAndLoss": "Geschätzter Gewinn/Verlust", "collectiblesSellViewOnMarketplace": "Auf {{marketplace}} an<PERSON><PERSON>", "collectiblesSellCollectionFloorTooltip": "Der niedrigste „Sofort-Kaufen“-Preis in der Sammlung auf mehreren Marktplätzen.", "collectiblesSellProfitLossTooltip": "Der geschätzte Gewinn/Verlust wird auf der Grundlage des letzten Verkaufspreises und des Angebotsbetrags abzüglich der Gebühren berechnet.", "collectiblesSellEstimatedRoyaltiesFeesTooltipTitle": "Vergütung ({{royaltiesPercentage}})", "collectiblesSellEstimatedRoyaltiesFeesTooltipValue": "{{royaltiesValue}}", "collectiblesSellEstimatedMarketplaceFeesTooltipTitle": "Marktplatzgebühr ({{marketplaceFeePercentage}})", "collectiblesSellEstimatedMarketplaceFeeTooltipTitle": "Marktplatzgebühr", "collectiblesSellEstimatedMarketplaceFeesTooltipValue": "{{marketplaceFeeValue}}", "collectiblesSellEstimatedChainFeesTooltipTitle": "{{chainName}}-Netzwerk", "collectiblesSellEstimatedChainFeesTooltipValue": "{{chainFeeValue}}", "collectiblesSellEstimatedPhantomFeesTooltipEVM": "Angeb<PERSON> enthält eine {{phantomFeePercentage}} Phantom-Gebühr", "collectiblesSellEstimatedPhantomFeesTooltipSolana": "Das Angebot enthält Lizenzgebühren, Netzwerkgebühr, Marktplatzgebühr und eine {{phantomFeePercentage}} Phantom-Gebühr", "collectiblesSellEstimatedPhantomFeesTooltipSolanaNoPhantomFee": "Das Angebot umfasst Lizenzgebühren, Netzwerkgebühren und eine Marktplatzgebühr", "collectiblesSellTransactionFeeTooltipTitle": "Transaktionsgebühr", "collectiblesSellStatusLoadingTitle": "Akze<PERSON><PERSON><PERSON> …", "collectiblesSellStatusLoadingIsSellingFor": "wird verkauft für", "collectiblesSellStatusSuccessTitle": "{{collectibleName}} verkauft!", "collectiblesSellStatusSuccessWasSold": "wurde erfolgreich verkauft für", "collectiblesSellStatusErrorTitle": "Etwas ist schief gelaufen", "collectiblesSellStatusErrorSubtitle": "<PERSON>s gab ein Problem beim Versuch des Verkaufs", "collectiblesSellStatusViewTransaction": "Transaktion ansehen", "collectiblesSellInsufficientFundsTitle": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "collectiblesSellInsufficientFundsSubtitle": "Wir konnten kein Angebot für dieses Sammelobjekt annehmen, da nicht genügend Guthaben für die Zahlung der Netzwerk-Gebühr vorhanden waren.", "collectiblesSellRecentlyTransferedNFTTitle": "<PERSON><PERSON><PERSON>lich überwiesen", "collectiblesSellRecentlyTransferedNFTSubtitle": "Nach einem Transfer müssen Sie 1 Stunde warten, bis Sie Gebote annehmen können.", "collectiblesApproveCollection": "{{collectionName}} gene<PERSON><PERSON>t", "collectiblesSellNotAvailableAnymoreTitle": "Angebot nicht verfügbar", "collectiblesSellNotAvailableAnymoreSubtitle": "Das Angebot ist nicht mehr verfügbar. Stornieren Sie dieses Gebot und versuchen Si<PERSON> es erneut", "collectiblesSellFlaggedTokenTitle": "Sammelobjekt wurde gekennzeichnet", "collectiblesSellFlaggedTokenSubtitle": "Das Sammlerstück ist nicht handelbar, dies könnte aus verschiedenen Gründen wie als gestohlen gemeldet oder ohne Sperrung eingesetzt sein", "collectiblesListOnMagicEden": "<PERSON>f Magic Eden listen", "collectiblesListPrice": "Listenpreis", "collectiblesUseFloor": "Mindestpreis verwenden", "collectiblesFloorPrice": "Mindestpreis", "collectiblesLastSalePrice": "Letzter Verkaufspreis", "collectiblesTotalReturn": "Gesamtertrag", "collectiblesOriginalPurchasePrice": "Ursprünglicher Kaufpreis", "collectiblesMagicEdenFee": "Magic Eden-<PERSON><PERSON><PERSON><PERSON>", "collectiblesArtistRoyalties": "Künstlertantiemen", "collectiblesListNowButton": "Jetzt listen", "collectiblesListAnywayButton": "Trotzdem listen", "collectiblesCreateListingTermsOfService": "Indem Sie auf <1>\"<PERSON><PERSON><PERSON> listen\"</1> tip<PERSON>, erkl<PERSON>ren <PERSON> sich mit den <3>Nutzungsbedingungen</3> von Magic Eden einverstanden", "collectiblesViewListing": "Listung ansehen", "collectiblesListingViewTransaction": "Transaktion ansehen", "collectiblesRemoveListing": "Listung entfernen", "collectiblesEditListing": "Listung bearbeiten", "collectiblesEditListPrice": "Listenpreis bearbeiten", "collectiblesListPriceTooltip": "Der Listenpreis ist der Verkaufspreis für einen Artikel. Verkäufer legen den Listenpreis in der Regel so fest, dass er auf oder über dem Mindestpreis liegt.", "collectiblesFloorPriceTooltip": "Der Mindestpreis ist der niedrigste aktive Listenpreis für einen Artikel in dieser Sammlung.", "collectiblesOriginalPurchasePriceTooltip": "Sie haben diesen Artikel ursprünglich für diesen Betrag gekauft.", "collectiblesPurchasedForSol": "Für {{lastPurchasePrice}} SOL gekauft", "collectiblesUnableToLoadListings": "Listungen können nicht geladen werden", "collectiblesUnableToLoadListingsFrom": "Listungen von {{marketplace}} können nicht geladen werden", "collectiblesUnableToLoadListingsDescription": "Ihre Angebote und Vermögenswerte sind sicher, aber wir konnten sie derzeit nicht von {{marketplace}} laden. Bitte versuchen Sie es später noch einmal.", "collectiblesBelowFloorPrice": "Unter Mindestpreis", "collectiblesBelowFloorPriceMessage": "Sind <PERSON> sicher, dass Sie Ihr NFT unter dem Mindestpreis listen möchten?", "collectiblesMinimumListingPrice": "Mindestpreis ist 0.01 SOL", "collectiblesMagicEdenFeeTooltip": "Magic Eden nimmt eine Gebühr auf abgeschlossene Transaktionen.", "collectiblesArtistRoyaltiesTooltip": "Der Ersteller dieser Sammlung erhält eine prozentuale Vergütung für jeden abgeschlossenen Verkauf.", "collectibleScreenCollectionLabel": "<PERSON><PERSON><PERSON>", "collectibleScreenPhotosPermissionTitle": "Erlaubnis für Fotos", "collectibleScreenPhotosPermissionMessage": "Wir benötigen Ihre Erl<PERSON>, um auf Ihre Fotos zuzugreifen. Bitte gehen Sie zu Einstellungen und aktualisieren Sie Ihre Berechtigungen.", "collectibleScreenPhotosPermissionOpenSettings": "Einstellungen öffnen", "listStatusErrorTitle": "Listung fehlgeschlagen", "editListStatusErrorTitle": "Aktualisierung nicht möglich", "removeListStatusErrorTitle": "Listung entfernen fehlgeschlagen", "listStatusSuccessTitle": "Listung erstellt!", "editListingStatusSuccessTitle": "Listung aktualisiert!", "removeListStatusSuccessTitle": "Listung von Magic Eden entfernt", "listStatusLoadingTitle": "Listung wird erstellt ...", "editListingStatusLoadingTitle": "Listung wird aktualisiert ...", "removeListStatusLoadingTitle": "Listung wird entfernt ...", "listStatusErrorMessage": "{{name}} kon<PERSON> nicht auf Magic Eden gelistet werden", "removeListStatusErrorMessage": "<PERSON>ung von {{name}} auf Magic Eden konnte nicht aufgehoben werden", "listStatusSuccessMessage": "{{name}} ist jetzt auf Magic Eden für {{listCollectiblePrice}} SOL gelistet", "editListingStatusSuccessMessage": "{{name}} ist jetzt auf Magic Eden auf {{editListCollectiblePrice}} SOL aktualisiert", "removeListStatusSuccessMessage": "{{name}} wurde er<PERSON><PERSON><PERSON><PERSON><PERSON> von <PERSON> entfernt", "listStatusLoadingMessage": "{{name}} wird auf Magic Eden für {{listCollectiblePrice}} SOL gelistet.", "editListingStatusLoadingMessage": "{{name}} wird auf Magic Eden auf {{editListCollectiblePrice}} SOL aktualisiert.", "removeListStatusLoadingMessage": "<PERSON><PERSON><PERSON><PERSON> {{name}} von <PERSON>. Das kann eine Weile dauern.", "listStatusLoadingSafelyDismiss": "<PERSON><PERSON> kö<PERSON>n dieses Fenster nun schließen.", "listStatusViewOnMagicEden": "<PERSON><PERSON> <PERSON>", "listStatusViewOnMarketplace": "Auf {{marketplace}} an<PERSON><PERSON>", "listStatusLoadingDismiss": "Schließen", "listStatusViewTransaction": "Transaktion ansehen", "connectHardwareConnectedPrimaryText": "Ledger <PERSON>", "connectHardwareConnectedSecondaryText": "Verbinden Sie Ihr Hardware-Wallet und stellen Sie sicher, dass es freigeschaltet ist. <PERSON><PERSON><PERSON> wir es erkannt haben, können Sie die Adresse auswählen, die Si<PERSON> verwenden möchten.", "connectHardwareFailedPrimaryText": "Verbindung fehlgeschlagen", "connectHardwareFailedSecondaryText": "Bitte schließen Sie Ihr Hardware-Wallet an und stellen Si<PERSON> sicher, dass es freigeschaltet ist. <PERSON><PERSON>d wir es erkannt haben, können <PERSON> w<PERSON>hlen, welche Adresse Sie verwenden möchten.", "connectHardwareFinishPrimaryText": "Konto hinzugefügt!", "connectHardwareFinishSecondaryText": "<PERSON><PERSON> können nun von Phantom aus auf Ihr Ledger Nano Wallet zugreifen. Bitte kehren Sie zur Erweiterung zurück.", "connectHardwareNeedsPermissionPrimaryText": "Neues Wallet verbinden", "connectHardwareNeedsPermissionSecondaryText": "<PERSON>licken Sie auf die Schaltfläche unten, um den Verbindungsvorgang zu starten.", "connectHardwareSearchingPrimaryText": "Suche nach Wallet ...", "connectHardwareSearchingSecondaryText": "Schließen Sie Ihr Hardware-Wallet an, vergewissern <PERSON> sich, dass es freigeschaltet ist und dass Sie in Ihrem Browser über die entsprechenden Berechtigungen verfügen.", "connectHardwarePermissionDeniedPrimary": "Genehmigung verweigert", "connectHardwarePermissionDeniedSecondary": "<PERSON><PERSON>ilen Sie Phantom die Genehmigung, sich mit Ihrem Ledger-Gerät zu verbinden", "connectHardwarePermissionUnableToConnect": "<PERSON><PERSON>erbindung möglich", "connectHardwarePermissionUnableToConnectDescription": "Wir konnten keine Verbindung zu Ihrem Ledger-Gerät herstellen. Möglicherweise benötigen wir weitere Genehmigungen.", "connectHardwareSelectAddressAllAddressesImported": "Alle Adressen importiert", "connectHardwareSelectAddressDerivationPath": "Ableitungspfad", "connectHardwareSelectAddressSearching": "Suche ...", "connectHardwareSelectAddressSelectWalletAddress": "Wallet-Ad<PERSON><PERSON> au<PERSON>wählen", "connectHardwareSelectAddressWalletAddress": "Wall<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareWaitingForApplicationSecondaryText": "Bitte schließen Sie Ihr Hardware-Wallet an und stellen Si<PERSON> sicher, dass es entsperrt ist.", "connectHardwareWaitingForPermissionPrimaryText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectHardwareWaitingForPermissionSecondaryText": "Schließen Sie Ihr Hardware-Wallet an, vergewissern <PERSON> sich, dass es freigeschaltet ist und dass Sie in Ihrem Browser über die entsprechenden Berechtigungen verfügen.", "connectHardwareAddAccountButton": "<PERSON><PERSON> hi<PERSON>uf<PERSON>", "connectHardwareLedger": "Verbinden Sie Ihr Ledger", "connectHardwareStartConnection": "<PERSON>licken Sie auf die Schaltfläche unten, um die Verbindung zu Ihrem Ledger-Hardware-Wallet zu starten", "connectHardwarePairSuccessPrimary": "{{productName}} verbunden", "connectHardwarePairSuccessSecondary": "Sie haben Ihr {{productName}} erfolgreich verbunden.", "connectHardwareSelectChains": "Chains zum Verbinden auswählen", "connectHardwareSearching": "Suche ...", "connectHardwareMakeSureConnected": "Verbinden und entsperren Sie Ihr Hardware-Wallet. Bitte genehmigen Sie die entsprechenden Browser-Berechtigungen.", "connectHardwareOpenAppDescription": "Bitte entsperren Sie Ihr Hardware-Wallet", "connectHardwareConnecting": "Verbinden ...", "connectHardwareConnectingDescription": "Wir stellen eine Verbindung zu Ihrem Ledger-Gerät her.", "connectHardwareConnectingAccounts": "Verbinden Ihrer Konten ...", "connectHardwareDiscoveringAccounts": "<PERSON><PERSON> nach Konten …", "connectHardwareDiscoveringAccountsDescription": "Wir suchen nach Aktivitäten auf Ihren Konten.", "connectHardwareErrorLedgerLocked": "Ledger ist gesperrt", "connectHardwareErrorLedgerLockedDescription": "Vergewissern Si<PERSON> sich, dass Ihr Ledger-Gerät freigeschaltet ist, und versuchen Si<PERSON> es dann erneut.", "connectHardwareErrorLedgerGeneric": "Etwas ist schief gelaufen", "connectHardwareErrorLedgerGenericDescription": "<PERSON>s wurden keine Konten gefunden. Vergewissern Si<PERSON> sich, dass Ihr Ledger-Gerät freigeschaltet ist, und versuchen Si<PERSON> es dann erneut.", "connectHardwareErrorLedgerPhantomLocked": "Bitte öffnen Sie Phantom erneut und versuchen Si<PERSON> erneut, Ihre Hardware zu verbinden.", "connectHardwareFindingAccountsWithActivity": "{{chainName}}-Ko<PERSON>n finden ...", "connectHardwareFindingAccountsWithActivityDualChain": "{{chainName1}}- oder {{chainName2}}-Ko<PERSON>n finden ...", "connectHardwareFoundAccountsWithActivity": "Wir haben {{numOfAccounts}} Konten mit Aktivität in Ihrem Ledger gefunden.", "connectHardwareFoundAccountsWithActivitySingular": "Wir haben 1 Konto mit Aktivität in Ihrem Ledger gefunden.", "connectHardwareFoundSomeAccounts": "Wir haben einige Konten auf Ihrem Ledger-Gerät gefunden.", "connectHardwareViewAccounts": "Konten anzeigen", "connectHardwareConnectAccounts": "Konten verbunden", "connectHardwareSelectAccounts": "Konten wählen", "connectHardwareChooseAccountsToConnect": "Wählen Sie Wallet-Konten zum Verbinden aus.", "connectHardwareAccountsAddedInterpolated": "{{numOfAccounts}} hinzugefügte Konten", "connectHardwareAccountsStepOfSteps": "<PERSON><PERSON>itt {{stepNum}} von {{totalSteps}}", "connectHardwareMobile": "Ledger verbinden", "connectHardwareMobileTitle": "Verbinden Sie Ihr Ledger-Hardware-Wallet", "connectHardwareMobileEnableBluetooth": "Bluetooth aktivieren", "connectHardwareMobileEnableBluetoothDescription": "Erlauben Sie die Verwendung von Bluetooth für die Verbindung", "connectHardwareMobileEnableBluetoothSettings": "Gehen Sie zu Einstellungen, um Phantom die Verwendung von Standort und Geräten in der Nähe zu erlauben.", "connectHardwareMobilePairWithDevice": "Kopplung mit Ihrem Ledger-Gerät", "connectHardwareMobilePairWithDeviceDescription": "Behalten Sie Ihr Gerät in der Nähe, um das beste Signal zu erhalten", "connectHardwareMobileConnectAccounts": "Konten verbinden", "connectHardwareMobileConnectAccountsDescription": "Wir suchen nach Aktivitäten auf Konten, die Sie möglicherweise benutzt haben", "connectHardwareMobileConnectLedgerDevice": "Verbinden Sie Ihr Ledger-Gerät", "connectHardwareMobileLookingForDevices": "Suche nach Geräten in der Nähe...", "connectHardwareMobileLookingForDevicesDescription": "Bitte schließen Sie Ihren Ledger-Gerät an und stellen Si<PERSON> sicher, dass es entsperrt ist.", "connectHardwareMobileFoundDeviceSingular": "Wir haben 1 Ledger-Gerät gefunden", "connectHardwareMobileFoundDevices": "Wir haben {{numDevicesFound}} Ledger-Geräte gefunden", "connectHardwareMobileFoundDevicesDescription": "<PERSON><PERSON>hlen Si<PERSON> unten ein Ledger-Gerät aus, um die Kopplung zu starten.", "connectHardwareMobilePairingWith": "<PERSON><PERSON><PERSON> mit {{deviceName}}", "connectHardwareMobilePairingWithDescription": "Folgen Sie beim Koppeln den Anweisungen auf Ihrem Ledger.", "connectHardwareMobilePairingFailed": "Kopplung nicht erfolgreich", "connectHardwareMobilePairingFailedDescription": "Kann nicht mit {{deviceName}} gekoppelt werden. <PERSON><PERSON><PERSON>, dass Ihr Gerät freigeschaltet ist.", "connectHardwareMobilePairingSuccessful": "Kopplung erfolgreich", "connectHardwareMobilePairingSuccessfulDescription": "Sie haben Ihr Ledger-Gerät erfolgreich gekoppelt und angeschlossen.", "connectHardwareMobileOpenAppSingleChain": "<PERSON><PERSON><PERSON> Sie die {{chainName}}-App auf Ihr<PERSON> Ledger", "connectHardwareMobileOpenAppDualChain": "<PERSON><PERSON><PERSON> Sie die {{chainName1}}- oder {{chainName2}}-App auf Ihr<PERSON> Ledger", "connectHardwareMobileOpenAppDescription": "Vergewissern Si<PERSON> sich, dass Ihr Gerät entsperrt ist.", "connectHardwareMobileStillCantFindDevice": "Sie können Ihr Gerät immer noch nicht finden?", "connectHardwareMobileLostConnection": "Verbindung wurde getrennt", "connectHardwareMobileLostConnectionDescription": "Die Verbindung zu {{deviceName}} wurde unterbrochen. <PERSON><PERSON><PERSON>, dass Ihr Gerät freigeschaltet ist, und versuchen Si<PERSON> es erneut.", "connectHardwareMobileGenericLedgerDevice": "Ledger-Gerät", "connectHardwareMobileConnectDeviceSigning": "Verbinden Sie Ihr {{deviceName}}", "connectHardwareMobileConnectDeviceSigningDescription": "Entsperren Sie Ihr Ledger-Gerät und behalten Sie es in der Nähe.", "connectHardwareMobileBluetoothDisabled": "Bluetooth ist deaktiviert", "connectHardwareMobileBluetoothDisabledDescription": "Bitte aktivieren Sie Ihr Bluetooth und vergewissern Si<PERSON> sich, dass Ihr Ledger-Gerät entsperrt ist.", "connectHardwareMobileLearnMore": "<PERSON><PERSON> er<PERSON>", "connectHardwareMobileBlindSigningDisabled": "Blindsignatur ist deaktiviert", "connectHardwareMobileBlindSigningDisabledDescription": "Vergewissern <PERSON> sich, dass die Blindsignatur auf Ihrem Gerät aktiviert ist.", "connectHardwareMobileConfirmSingleChain": "Sie müssen die Transaktion auf Ihrem Hardware-Wallet bestätigen. <PERSON>elle<PERSON>, dass es entsperrt ist.", "metamaskExplainerBottomSheetHeader": "Diese Website funktioniert mit Phantom", "metamaskExplainerBottomSheetSubheader": "Wählen Sie MetaMask aus dem Dialogfeld „Wallet verbinden“, um fortzufahren.", "metamaskExplainerBottomSheetDontShowAgain": "Nicht mehr anzeigen", "ledgerStatusNotConnected": "Ledger ist nicht verbunden", "ledgerStatusConnectedInterpolated": "{{productName}} ist verbunden", "connectionClusterInterpolated": "Sie sind momentan auf {{cluster}}", "connectionClusterTestnetMode": "Sie befinden sich derzeit im Testnet-Modus", "featureNotSupportedOnLocalNet": "Diese Funktion wird nicht unterstützt, wenn Solana Localnet aktiviert ist.", "readOnlyAccountBannerWarning": "<PERSON><PERSON> beobachten dieses <PERSON>", "depositAddress": "Empfänger-Adresse", "depositAddressChainInterpolated": "Ihre {{chain}}-<PERSON><PERSON><PERSON>", "depositAssetDepositInterpolated": "{{tokenSymbol}} erhalten", "depositAssetSecondaryText": "Diese Adresse kann nur für den Empfang von kompatiblen Token verwendet werden.", "depositAssetTextInterpolated": "Verwenden Sie diese Adresse, um Token und Sammelobjekte auf <1>{{network}}</1> zu empfangen.", "depositAssetTransferFromExchange": "<PERSON> Börse ü<PERSON>wei<PERSON>", "depositAssetShareAddress": "<PERSON><PERSON><PERSON> te<PERSON>n", "depositAssetBuyOrDeposit": "Kaufen oder Überweisen", "depositAssetBuyOrDepositDesc": "Laden Sie Ihr Wallet mit <PERSON>n auf, um loszulegen", "depositAssetTransfer": "Überweisen", "editAddressAddressAlreadyAdded": "<PERSON>resse wurde bereits hinzugefügt", "editAddressAddressAlreadyExists": "Adresse existiert bereits", "editAddressAddressIsRequired": "<PERSON>resse ist erforderlich", "editAddressPrimaryText": "<PERSON><PERSON><PERSON> bear<PERSON>", "editAddressRemove": "Aus Adressbuch entfernen", "editAddressToast": "<PERSON><PERSON><PERSON> aktualisiert", "removeSavedAddressToast": "<PERSON><PERSON><PERSON> entfernt", "exportSecretErrorGeneric": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal", "exportSecretErrorIncorrectPassword": "Falsches Passwort", "exportSecretPassword": "Passwort", "exportSecretPrivateKey": "Private<PERSON><PERSON><PERSON>", "exportSecretSecretPhrase": "Geheime Phrase", "exportSecretPIN": "PIN", "exportSecretSecretRecoveryPhrase": "Geheime Recovery-Phrase", "exportSecretSelectYourAccount": "Wählen Sie Ihr Konto", "exportSecretShowPrivateKey": "Privaten Schlüssel anzeigen", "exportSecretShowSecretRecoveryPhrase": "Geheime Recovery-Phrase anzeigen", "exportSecretShowSecret": "{{secretNameText}} anzeigen", "exportSecretWarningPrimaryInterpolated": "Verraten Sie <1>nie</1> Ihre/n {{secretNameText}}!", "exportSecretWarningSecondaryInterpolated": "<PERSON><PERSON> jemand <PERSON>e/n {{secretNameText}} hat, hat er die volle Kontrolle über Ihr Wallet.", "exportSecretOnlyWay": "Ihr {{secretNameText}} ist die einzige Möglichkeit, Ihr Wallet wiederherzustellen", "exportSecretDoNotShow": "<PERSON><PERSON> Si<PERSON> niemanden Ihren {{secretNameText}} sehen", "exportSecretWillNotShare": "Ich werde meinen {{secretNameText}} mit niemandem teilen, auch nicht mit <PERSON>.", "exportSecretNeverShare": "<PERSON><PERSON><PERSON> Si<PERSON> Ihre(n) {{secretNameText}} niemals an Dritte weiter", "exportSecretYourPrivateKey": "Ihr Privater <PERSON>", "exportSecretYourSecretRecoveryPhrase": "<PERSON>hre geheime Recovery-Phrase", "exportSecretResetPin": "Setzen Sie Ihre PIN zurück", "fullPageHeaderBeta": "Beta!", "fullPageHeaderHelp": "<PERSON><PERSON><PERSON>", "gasUpTo": "Bis zu {{ amount }}", "timeDescription1hour": "Ungefähr 1 Stunde", "timeDescription30minutes": "Ungefähr 30 Minuten", "timeDescription10minutes": "Ungefähr 10 Minuten", "timeDescription2minutes": "Ungefähr 2 Minuten", "timeDescription30seconds": "Ungefähr 30 Sekunden", "timeDescription15seconds": "Ungefähr 15 Sekunden", "timeDescription10seconds": "Ungefähr 10 Sekunden", "timeDescription5seconds": "Ungefähr 5 Sekunden", "timeDescriptionAbbrev1hour": "1 h", "timeDescriptionAbbrev30minutes": "30 min", "timeDescriptionAbbrev10minutes": "10 min", "timeDescriptionAbbrev2minutes": "2 min", "timeDescriptionAbbrev30seconds": "30 s", "timeDescriptionAbbrev15seconds": "15 s", "timeDescriptionAbbrev10seconds": "10 s", "timeDescriptionAbbrev5seconds": "5 s", "gasSlow": "Langsam", "gasAverage": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "gasFast": "<PERSON><PERSON><PERSON>", "satsPerVirtualByte": "{{satsPerVirtualByte}} Sats/vB", "satsAmount": "{{sats}} Sats", "homeErrorButtonText": "<PERSON><PERSON><PERSON> versuchen", "homeErrorDescription": "Beim Abrufen Ihrer Vermögenswerte ist ein Fehler aufgetreten. Aktualisieren Sie, und versuchen Si<PERSON> es dann erneut", "homeErrorTitle": "Fehler beim Abrufen der Vermögenswerte", "homeManageTokenList": "Token-Liste verwalten", "interstitialDismissUnderstood": "Verstanden", "interstitialBaseWelcomeTitle": "Phantom unterstützt jetzt Base!", "interstitialBaseWelcomeItemTitle_1": "Token senden, empfangen und kaufen", "interstitialBaseWelcomeItemTitle_2": "Erkunden Sie das Base-Ökosystem", "interstitialBaseWelcomeItemTitle_3": "Sicher und zuverlässig", "interstitialBaseWelcomeItemDescription_1": "Überweisen und kaufen Sie auf Base USDC und ETH mit {{paymentMethod}}, Karten oder Coinbase.", "interstitialBaseWelcomeItemDescription_2": "Verwenden Sie Phantom mit all Ihren Lieblings-Apps für DeFi und NFT.", "interstitialBaseWelcomeItemDescription_3": "Mit der Unterstützung von <PERSON>, Spam-Filterung und Transaktionssimulation sind Sie auf der sicheren Seite.", "privacyPolicyChangedInterpolated": "Unsere Datenschutzrichtlinie hat sich geändert. <1><PERSON><PERSON> erfahren</1>", "bitcoinAddressTypesBodyTitle": "Bitcoin-Adresstypen", "bitcoinAddressTypesFeature1Title": "Über Bitcoin-Adressen", "bitcoinAddressTypesFeature1Subtitle": "Phantom unterstützt Native Segwi<PERSON> und <PERSON><PERSON>root, jew<PERSON><PERSON> mit eigenem Guthaben. Sie können BTC oder Ordinals mit beiden Adressarten senden.", "bitcoinAddressTypesFeature2Title": "Native Segwit", "bitcoinAddressTypesFeature2Subtitle": "Die Standard-BTC-Adresse in Phantom. <PERSON><PERSON> al<PERSON>, aber kompatibel mit allen Wallets und Börsen.", "bitcoinAddressTypesFeature3Title": "<PERSON><PERSON><PERSON>", "bitcoinAddressTypesFeature3Subtitle": "Am besten für Ordinals und BRC-20s, mit den günstigsten Gebühren. Passen Sie die Adressen unter Präferenzen -> Bevorzugte Bitcoin-Adresse an.", "headerTitleInfo": "Info", "bitcoinAddressTypeDepositAddressExplainerInterpolated": "Dies ist Ihre <1>{{addressType}}</1>-<PERSON><PERSON><PERSON>.", "invalidChecksumTitle": "Wir haben ein Upgrade Ihrer geheimen Phrase durchgeführt!", "invalidChecksumFeature1ExportPhrase": "Exportieren Sie Ihre neue geheime Phrase", "invalidChecksumFeature1ExportPhraseDescription": "Bitte sichern Sie Ihre neue geheime Phrase zusammen mit den privaten Schlüsseln Ihrer alten Konten.", "invalidChecksumFeature2FundsAreSafe": "<PERSON>hr Guthaben ist sicher und geschützt", "invalidChecksumFeature2FundsAreSafeDescription": "Dieses Upgrade wurde automatisiert. Niemand bei Phantom kennt Ihre geheime Phrase oder hat <PERSON>ng zu Ihrem <PERSON>n.", "invalidChecksumFeature3LearnMore": "<PERSON><PERSON> er<PERSON>", "invalidChecksumFeature3LearnMoreDescription": "Ihre Phrase war mit den meisten Geldbörsen nicht kompatibel. Lesen Sie <1>diesen Hi<PERSON><PERSON>kel</1>, um mehr darüber zu erfahren.", "invalidChecksumBackUpSecretPhrase": "Geheime Phrase sichern", "migrationFailureTitle": "Bei der Migration Ihres Kontos ist etwas schiefgelaufen", "migrationFailureFeature1": "Exportieren Sie Ihre geheime Phrase", "migrationFailureFeature1Description": "<PERSON><PERSON> sichern Sie Ihre geheime Phrase vor dem Onboarding.", "migrationFailureFeature2": "Onboarding bei Phantom", "migrationFailureFeature2Description": "<PERSON><PERSON> müssen erneut das Onboarding bei Phantom durchführen, um Ihr Konto einzu<PERSON>hen.", "migrationFailureFeature3": "<PERSON><PERSON> er<PERSON>", "migrationFailureFeature3Description": "<PERSON><PERSON> <1>diesen <PERSON></1>, um mehr darüber zu erfahren.", "migrationFailureContinueToOnboarding": "<PERSON><PERSON> zum Onboarding", "migrationFailureUnableToFetchMnemonic": "Wir konnten Ihre geheime Phrase nicht laden", "migrationFailureUnableToFetchMnemonicDescription": "Bitte wenden Si<PERSON> sich an den Support und laden Sie die Anwendungsprotokolle zur Fehlerbehebung herunter", "migrationFailureContactSupport": "Support kontaktieren", "ledgerActionConfirm": "Bestätigen Sie auf Ihrem Ledger Nano", "ledgerActionErrorBlindSignDisabledPrimaryText": "Blinde Signatur deaktiviert", "ledgerActionErrorBlindSignDisabledSecondaryText": "Vergewissern <PERSON>h, dass die blinde Signatur auf Ihrem Gerät aktiviert ist, und versuchen Sie die Aktion erneut", "ledgerActionErrorDeviceDisconnectedDuringOperationPrimaryText": "Hardware-Gerät wurde während des Betriebs getrennt", "ledgerActionErrorDeviceDisconnectedDuringOperationSecondaryText": "Bitte schließen Sie die Phantom-Erweiterung und versuchen Sie die Aktion erneut", "ledgerActionErrorDeviceLockedPrimaryText": "Hardware-<PERSON><PERSON><PERSON> g<PERSON>", "ledgerActionErrorDeviceLockedSecondaryText": "Bitte entsperren Sie Ihr Gerät und versuchen Sie die Aktion erneut", "ledgerActionErrorHeader": "Fehler bei Ledger-Aktionen", "ledgerActionErrorUserRejectionPrimaryText": "Nutzer lehnte Transaktion ab", "ledgerActionErrorUserRejectionSecondaryText": "Die Aktion wurde auf dem Hardware-Gerät vom Benutzer abgelehnt", "ledgerActionNeedPermission": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ledgerActionNeedToConfirm": "Sie müssen die Transaktion auf Ihrem Hardware-Wallet bestätigen. <PERSON><PERSON><PERSON>, dass es in der {{chainType}}-App entsperrt ist.", "ledgerActionNeedToConfirmMany": "<PERSON>e müssen {{numberOfTransactions}} Transaktionen auf Ihrem Hardware-Wallet bestätigen. <PERSON><PERSON><PERSON>, dass es in der {{chainType}}-App entsperrt ist.", "ledgerActionNeedToConfirmBlind": "Sie müssen die Transaktion auf Ihrem Hardware-Wallet bestätigen. <PERSON><PERSON><PERSON>, dass es in der {{chainType}}-App entsperrt ist und Blindsignatur aktiviert ist.", "ledgerActionNeedToConfirmBlindMany": "<PERSON>e müssen {{numberOfTransactions}} Transaktionen auf Ihrem Hardware-Wallet bestätigen. Stellen <PERSON> sic<PERSON>, dass es in der {{chainType}}-App entsperrt ist und Blindsignatur aktiviert ist.", "ledgerActionPleaseConnect": "Bitte verbinden Sie Ihr Ledger Nano", "ledgerActionPleaseConnectAndConfirm": "Bitte schließen Sie Ihr Hardware-Wallet an und stellen Si<PERSON> sicher, dass es freigeschaltet ist. Vergewissern Si<PERSON> sich, dass Sie in Ihrem Browser die entsprechenden Berechtigungen haben.", "maxInputAmount": "Betrag", "maxInputMax": "<PERSON>.", "notEnoughSolPrimaryText": "Nicht genügend SOL", "notEnoughSolSecondaryText": "Sie haben nicht genug SOL in Ihrem Wallet für diese Transaktion. Bitte zahlen Si<PERSON> mehr ein und versuchen Si<PERSON> es erneut.", "insufficientBalancePrimaryText": "Nicht genug {{tokenSymbol}}", "insufficientBalanceSecondaryText": "Sie haben nicht genug {{tokenSymbol}} in Ihrem Wallet für diese Transaktion.", "insufficientBalanceRemaining": "Verbleibend", "insufficientBalanceRequired": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notEnoughSplTokensTitle": "Nicht genügend Token", "notEnoughSplTokensDescription": "Sie haben nicht genug Token für diese Transaktion in Ihrem Wallet. Diese Transaktion wird rückgängig gemacht, wenn sie eingereicht wird.", "transactionExpiredPrimaryText": "Transaktion abgelaufen", "transactionExpiredSecondaryText": "Sie haben zu lange mit der Bestätigung der Transaktion gewartet und sie ist abgelaufen. Diese Transaktion wird rückgängig gemacht, wenn sie eingereicht wird.", "transactionHasWarning": "Transaktionswarnung", "tokens": "Token", "notificationApplicationApprovalPermissionsAddressVerification": "Überprüfen <PERSON>, ob Sie diese Adresse besitzen", "notificationApplicationApprovalPermissionsTransactionApproval": "Genehmigung von Transaktionen beantragen", "notificationApplicationApprovalPermissionsViewWalletActivity": "Anzeige des Kontostands und der Aktivitäten in Ihres Wallets", "notificationApplicationApprovalParagraphText": "Die Bestätigung ermöglicht es dieser Website, die Guthaben und Aktivitäten für das ausgewählte Konto einzu<PERSON>hen.", "notificationApplicationApprovalActionButtonConnect": "Verbinden", "notificationApplicationApprovalActionButtonSignIn": "Anmelden", "notificationApplicationApprovalAllowApproval": "Verbinden der Website zulassen?", "notificationApplicationApprovalAutoConfirm": "Transaktionen automatisch bestätigen", "notificationApplicationApprovalConnectDisclaimer": "<PERSON>ur Verbindungen zu Websites herstellen, denen Si<PERSON> vertrauen", "notificationApplicationApprovalSignInDisclaimer": "Melden Sie sich nur bei Websites an, denen Si<PERSON> vertrauen", "notificationApplicationApprovalWebsiteIsUnsafeWarning": "Diese Website ist unsicher und könnte versuchen, <PERSON><PERSON> Guthaben zu stehlen.", "notificationApplicationApprovalConnectUnknownApp": "Unbekannt", "notificationApplicationApprovalConnectIncorrectNetworkTitle": "<PERSON>ine Verbindung zur App möglich", "notificationApplicationApprovalConnectIncorrectNetworkDescription": "<PERSON><PERSON>, eine Verbindung zu {{appNetworkName}} herzustellen, aber {{phantomNetworkName}} ist ausgewählt.", "notificationApplicationApprovalConnectIncorrectNetworkPromptMultiChain": "Um {{networkName}} zu verwenden, gehen Sie zu Entwicklereinstellungen → Testnet-Modus.", "notificationApplicationApprovalConnectIncorrectNetworkUnknownNetwork": "Unbekanntes Netzwerk", "notificationApplicationApprovalConnectLedgerUnsupportedDescription": "Die Verbindung mit anderen mobilen Anwendungen wird von Ledger derzeit nicht unterstützt.", "notificationApplicationApprovalConnectLedgerUnsupportedPrompt": "Bitte wechseln Sie zu einem Nicht-Ledger-Konto oder verwenden Sie den In-App-Browser und versuchen Si<PERSON> es erneut.", "notificationSignatureRequestConfirmTransaction": "Transaktion bestätigen", "notificationSignatureRequestConfirmTransactionCapitalized": "Transaktion bestätigen", "notificationSignatureRequestConfirmTransactions": "Transaktionen bestätigen", "notificationSignatureRequestConfirmTransactionsCapitalized": "Transaktionen bestätigen", "notificationSignatureRequestSignatureRequest": "<PERSON><PERSON>g auf Signatur", "notificationMessageHeader": "Nachricht", "notificationMessageCopied": "Nachricht kopiert", "notificationAutoConfirm": "Auto-Bestätigung", "notificationAutoConfirmOff": "Aus", "notificationAutoConfirmOn": "An", "notificationConfirmFooter": "Bestätigen Sie nur, wenn <PERSON><PERSON> dieser Website vertrauen.", "notificationEstimatedTime": "Geschätzte Zeit", "notificationPermissionRequestText": "Dies ist nur eine Erlaubnisanfrage. Die Transaktion wird möglicherweise nicht sofort ausgeführt.", "notificationBalanceChangesText": "Veränderungen des Guthabens sind geschätzt. Die betreffenden Beträge und Vermögenswerte sind nicht garantiert.", "notificationContractAddress": "Vertragsadresse", "notificationAdvancedDetailsText": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notificationUnableToSimulateWarningText": "Wir sind derzeit nicht in der Lage, Guthabenänderungen abzuschätzen. Sie können es später noch einmal versuchen oder bestätigen, wenn Sie dieser Website vertrauen.", "notificationSignMessageParagraphText": "Mit der Signierung dieser Nachricht weisen Si<PERSON> nach, dass Sie Eigentümer des ausgewählten Kontos sind.", "notificationSignatureRequestScanFailedDescription": "<PERSON> Nachricht kann nicht auf Sicherheitsprobleme überprüft werden. Bitte gehen Sie mit Vorsicht vor.", "notificationFailedToScan": "Die Ergebnisse dieser Anfrage konnten nicht simuliert werden.\nDas Bestätigen ist unsicher und kann zu Verlusten führen.", "notificationScanLoading": "<PERSON><PERSON>", "notificationTransactionApprovalActionButtonConfirm": "Bestätigen", "notificationTransactionApprovalActionButtonBack": "Zurück", "notificationTransactionApprovalEstimatedChanges": "Voraussichtliche Änderungen", "notificationTransactionApprovalEstimatesBasedOnSimulations": "Die Schätzungen basieren auf Transaktionssimulationen und stellen keine Garantie dar", "notificationTransactionApprovalHideAdvancedDetails": "Erweiterte Transaktionsdetails ausblenden", "notificationTransactionApprovalNetworkFee": "Netzwerkgebühr", "notificationTransactionApprovalNetwork": "Netzwerk", "notificationTransactionApprovalEstimatedTime": "Geschätzte Zeit", "notificationTransactionApprovalNoAssetOwnershipImpactingChanges": "Keine Änderungen mit Auswirkungen auf den Besitz von Vermögenswerten gefunden", "notificationTransactionApprovalSolanaAmountRequired": "Betrag, den das Solana-Netzwerk zur Abwicklung der Transaktion benötigt", "notificationTransactionApprovalUnableToSimulate": "Kann nicht simuliert werden. Vergewissern Si<PERSON> sich, dass Si<PERSON> dieser Website vertrauen, denn eine Genehmigung kann zu Guthabenverlust führen.", "notificationTransactionApprovalUnableToFetchBalanceChanges": "Änderungen am Guthaben können nicht abgerufen werden", "notificationTransactionApprovalViewAdvancedDetails": "Erweiterte Transaktionsdetails anzeigen", "notificationTransactionApprovalKnownMalicious": "Diese Transaktion ist betrügerisch. Die Unterzeichnung führt zu Guthabenverlust.", "notificationTransactionApprovalSuspectedMalicious": "Wir vermuten, dass diese Transaktion betrügerisch ist. Die Genehmigung kann zu Guthabenverlust führen.", "notificationTransactionApprovalNetworkFeeHighWarning": "Die Netzwerkgebühren sind aufgrund von Netzwerküberlastungen erhöht.", "notificationTransactionERC20ApprovalDescription": "<PERSON><PERSON> <PERSON> bestätigen, kann diese App jederzeit auf Ihr Guthaben zugreifen, bis zu dem unten angegebenen Limit.", "notificationTransactionERC20ApprovalContractAddress": "Vertragsadresse", "notificationTransactionERC20Unlimited": "unbegrenzt", "notificationTransactionERC20ApprovalTitle": "{{tokenSymbol}}-Ausgaben genehmigen", "notificationTransactionERC20RevokeTitle": "{{tokenSymbol}}-Ausgaben widerrufen", "notificationTransactionERC721RevokeTitle": "{{tokenSymbol}}-Zugriff widerrufen", "notificationTransactionERC20ApprovalAll": "Alle Ihre {{tokenSymbol}}", "notificationIncorrectModeTitle": "<PERSON><PERSON><PERSON>", "notificationIncorrectModeInTestnetTitle": "<PERSON>e sind im Testnet-Modus", "notificationIncorrectModeNotInTestnetTitle": "<PERSON>e sind nicht im Testnet-Modus", "notificationIncorrectModeInTestnetDescription": "{{origin}} <PERSON><PERSON><PERSON><PERSON>, ein <PERSON> zu nutzen, aber Si<PERSON> sind im Testnet-Modus", "notificationIncorrectModeNotInTestnetDescription": "{{origin}} ve<PERSON><PERSON><PERSON>, ein <PERSON> zu nutzen, aber Sie sind nicht im Testnet-Modus", "notificationIncorrectModeInTestnetProceed": "Deaktivieren Sie den Testnet-Modus, um fortzufahren.", "notificationIncorrectModeNotInTestnetProceed": "Aktivieren Sie den Testnet-Modus, um fortzufahren.", "notificationIncorrectEIP712ChainId": "Wir haben <PERSON> geh<PERSON>, eine Nach<PERSON>t zu signieren, die nicht für das Netzwerk bestimmt war, mit dem Si<PERSON> gerade verbunden sind", "notificationIncorrectEIP712ChainIdDescription": "<PERSON><PERSON><PERSON><PERSON> angefordert {{messageChainId}}, Sie sind verbunden mit {{connectedChainId}}", "notificationUnsupportedNetwork": "Nicht unterstütztes Netzwerk", "notificationUnsupportedNetworkDescription": "Diese Website versucht, ein Netzwerk zu verwenden, das Phantom derzeit nicht unterstützt.", "notificationUnsupportedNetworkDescriptionInterpolated": "Um mit einer anderen Erweiterung fortzufahren, deaktivieren Sie <1>Einstellungen → Standard-App-Wallet, und wählen Sie Immer fragen</1>. Aktualisieren Sie dann die Seite und stellen Sie die Verbindung erneut her.", "notificationUnsupportedAccount": "Nicht unterstütztes Konto", "notificationUnsupportedAccountDescription": "Diese Website versucht, {{targetChainType}} zu verwenden, was dieses {{chainType}}-<PERSON><PERSON> nicht unterstützt.", "notificationUnsupportedAccountDescription2": "Wechseln Sie zu einem Konto mit einer kompatiblen Seed-Phrase oder einem privaten Schlüssel und versuchen Si<PERSON> es erneut.", "notificationInvalidTransaction": "Ungültige Transaktion", "notificationInvalidTransactionDescription": "<PERSON> von dieser App empfangene Transaktion ist fehlerhaft und sollte nicht übermittelt werden. Bitte kontaktieren Sie den Entwickler dieser App, um ihm dieses Problem zu melden.", "notificationCopyTransactionText": "Transaktion kopieren", "notificationTransactionCopied": "Transaktion kopiert", "onboardingImportOptionsPageTitle": "Importieren eines Wallets", "onboardingImportOptionsPageSubtitle": "Importieren Sie ein vorhandenes Wallet mit Ihrer geheimen Phrase, Ihrem privaten Schlüssel oder Ihrem Hardware-Wallet.", "onboardingImportPrivateKeyPageTitle": "Importieren eines privaten Schlüssels", "onboardingImportPrivateKeyPageSubtitle": "Importieren eines bestehenden Single-Chain-Wallets", "onboardingCreatePassword": "Passwort erstellen", "onboardingCreatePasswordAgreeToTermsOfServiceInterpolated": "Ich akzeptiere die <1>Nutzungsbedingungen</1>", "onboardingCreatePasswordConfirmPasswordPlaceholder": "Passwort bestätigen", "onboardingCreatePasswordDescription": "Damit können Sie Ihr Wallet entsperren.", "onboardingCreatePasswordErrorInvalidSecretRecoveryPhrase": "Ungültige geheime Recovery-Phrase", "onboardingCreatePasswordPasswordPlaceholder": "Passwort", "onboardingCreatePasswordPasswordStrengthWeak": "<PERSON><PERSON><PERSON>", "onboardingCreatePasswordPasswordStrengthMedium": "Medium", "onboardingCreatePasswordPasswordStrengthStrong": "<PERSON>", "onboardingCreateRecoveryPhraseSavedSecretRecoveryPhrase": "Ich habe meine geheime Recovery-Phrase gespeichert", "onboardingCreateRecoveryPhraseSecretRecoveryPhrase": "Geheime Recovery-Phrase", "onboardingCreateRecoveryPhraseSecretRecoveryPhraseReminder": "Diese Phrase ist der EINZIGE Weg, um Ihr Wallet wiederherzustellen. Teilen Sie ihn NICHT mit anderen!", "onboardingImportWallet": "Wallet importieren", "onboardingImportWalletImportExistingWallet": "Importieren Sie ein bestehendes Wallet mit Ihrer geheimen 12- oder 24-Wort-Recovery-Phrase.", "onboardingImportWalletRestoreWallet": "<PERSON><PERSON> wied<PERSON><PERSON><PERSON>len", "onboardingImportWalletSecretRecoveryPhrase": "Geheime Recovery-Phrase", "onboardingImportWalletErrorInvalidSecretRecoveryPhrase": "Ungültige geheime Recovery-Phrase", "onboardingImportWalletIHaveWords": "Ich habe eine {{numWords}}-Recovery-Phrase", "onboardingImportWalletIncorrectOrMisspelledWord": "Das Wort {{wordIndex}} ist falsch oder falsch geschrieben", "onboardingImportWalletIncorrectOrMisspelledWords": "Die Worte {{wordIndexes}} sind falsch oder falsch geschrieben", "onboardingImportWalletScrollDown": "Runterscrollen", "onboardingImportWalletScrollUp": "Raufscrollen", "onboardingSelectAccountsImportAccounts": "Konten importieren", "onboardingSelectAccountsImportAccountsDescription": "Wählen Sie Wallet-Konten zum Importieren aus.", "onboardingSelectAccountsImportSelectedAccounts": "Ausgewählte Konten importieren", "onboardingSelectAccountsFindMoreAccounts": "<PERSON><PERSON><PERSON> Konten finden", "onboardingSelectAccountsFindMoreNoneFound": "<PERSON><PERSON>n gefunden", "onboardingSelectAccountsNoOfAccountsSelected": "{{numOfAccounts}} Konten ausgewählt", "onboardingSelectAccountSelectAllText": "Alle auswählen", "onboardingAdditionalPermissionsTitle": "Apps mit Phantom verwenden", "onboardingAdditionalPermissionsSubtitle": "<PERSON><PERSON>r ein möglichst nahtloses App-<PERSON><PERSON><PERSON><PERSON> empfehlen wir, <PERSON> zu erlauben, Daten auf allen Seiten zu lesen und zu ändern.", "interstitialAdditionalPermissionsTitle": "Apps mit Phantom verwenden", "interstitialAdditionalPermissionsSubtitle": "Damit Sie die Apps weiterhin ohne Unterbrechung nutzen können, <PERSON><PERSON><PERSON><PERSON> wir, <PERSON> das Lesen und <PERSON><PERSON><PERSON> von Daten auf allen Seiten zu erlauben.", "recentActivityPrimaryText": "Neueste Aktivität", "removeAccountActionButtonRemove": "Entfernen", "removeAccountRemoveWallet": "<PERSON><PERSON> entfernen", "removeAccountInterpolated": "{{accountName}} entfernen", "removeAccountWarningLedger": "Auch wenn Sie dieses Wallet aus Phantom entfernen, können Sie es über den Workflow \"Hardware Wallet Verbinden\" wieder hinzufügen.", "removeAccountWarningSeedVault": "Auch wenn Sie dieses Wallet aus Phantom entfernen, können Sie es über den Workflow \"Seed Vault Wallet Verbinden\" wieder hinzufügen.", "removeAccountWarningPrivateKey": "<PERSON><PERSON><PERSON> dies<PERSON> en<PERSON>fernen, wird <PERSON> es nicht mehr für Si<PERSON> wiederherstellen können. <PERSON><PERSON><PERSON>, dass Si<PERSON> eine Sicherungskopie Ihres privaten Schlüssels haben.", "removeAccountWarningSeed": "Auch wenn Sie dieses Wallet aus Phantom entfernen, können Sie es unter Verwendung Ihrer Mnemonik in diesem oder einem anderen Wallet wiederherstellen.", "removeAccountWarningReadOnly": "Das Löschen dieses Kontos hat keine Auswirkungen auf Ihr Wallet, da es sich um ein reines Beobachtungs-Wallet handelt.", "removeSeedPrimaryText": "Geheime Phrase {{number}} wird entfernt", "removeSeedSecondaryText": "<PERSON><PERSON><PERSON> werden alle bestehenden Konten in der geheimen Phrase {{number}} gel<PERSON><PERSON><PERSON>. <PERSON><PERSON><PERSON>, dass Sie Ihre bestehende geheime Phrase gesichert haben.", "resetSeedPrimaryText": "App mit neuer geheimen Phrase zurücksetzen", "resetSeedSecondaryText": "Dad<PERSON>ch werden alle vorhandenen Konten gelöscht und durch neue Wallets ersetzt. <PERSON><PERSON><PERSON>, dass Sie Ihre bestehende geheime Phrase und privaten Schlüssel gesichert haben.", "resetAppPrimaryText": "<PERSON><PERSON> zurücksetzen und löschen", "resetAppSecondaryText": "Dad<PERSON>ch werden alle bestehenden Konten und Daten gelöscht. <PERSON><PERSON><PERSON>, dass Sie Ihre geheime Phrase und private Sc<PERSON><PERSON><PERSON> gesichert haben.", "richTransactionsDays": "Tage", "richTransactionsToday": "<PERSON><PERSON>", "richTransactionsYesterday": "Gestern", "richTransactionDetailAccount": "Ko<PERSON>", "richTransactionDetailAppInteraction": "App-Interaktion", "richTransactionDetailAt": "am", "richTransactionDetailBid": "<PERSON><PERSON><PERSON>", "richTransactionDetailBidDetails": "Details zum Gebot", "richTransactionDetailBought": "Gekauft", "richTransactionDetailBurned": "Verbrannt", "richTransactionDetailCancelBid": "Gebot stornieren", "richTransactionDetailCompleted": "Abgeschlossen", "richTransactionDetailConfirmed": "Bestätigt", "richTransactionDetailDate": "Datum", "richTransactionDetailFailed": "Fehlgeschlagen", "richTransactionDetailFrom": "<PERSON>", "richTransactionDetailItem": "Artikel", "richTransactionDetailListed": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailListingDetails": "Details zur Listung", "richTransactionDetailListingPrice": "Listenpreis", "richTransactionDetailMarketplace": "Marktplatz", "richTransactionDetailNetworkFee": "Netzwerkgebühr", "richTransactionDetailOriginalListingPrice": "Ursprünglicher Listenpreis", "richTransactionDetailPending": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailPrice": "Pre<PERSON>", "richTransactionDetailProvider": "<PERSON><PERSON><PERSON>", "richTransactionDetailPurchaseDetails": "Kauf-Details", "richTransactionDetailRebate": "<PERSON><PERSON><PERSON>", "richTransactionDetailReceived": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailSaleDetails": "Verkauf-Details", "richTransactionDetailSent": "Gesendet", "richTransactionDetailSold": "Verkauft", "richTransactionDetailStaked": "Eingesetzt", "richTransactionDetailStatus": "Status", "richTransactionDetailSwap": "Tauschen", "richTransactionDetailSwapDetails": "Tausch-Details", "richTransactionDetailTo": "An", "richTransactionDetailTokenSwap": "Token-Tausch", "richTransactionDetailUnknownNFT": "Unbekanntes NFT", "richTransactionDetailUnlisted": "<PERSON><PERSON><PERSON>", "richTransactionDetailUnstaked": "<PERSON>cht eingesetzt", "richTransactionDetailValidator": "<PERSON><PERSON><PERSON><PERSON>", "richTransactionDetailViewOnExplorer": "Auf {{explorer}} an<PERSON><PERSON>", "richTransactionDetailWithdrawStake": "Einsatz abheben", "richTransactionDetailYouPaid": "<PERSON><PERSON> be<PERSON>", "richTransactionDetailYouReceived": "<PERSON>e er<PERSON>ten", "richTransactionDetailUnwrapDetails": "Details zum Auspacken", "richTransactionDetailTokenUnwrap": "<PERSON><PERSON> auspacken", "activityItemsRefreshFailed": "Laden neuerer Transaktionen fehlgeschlagen.", "activityItemsPagingFailed": "Laden älterer Transaktionen fehlgeschlagen.", "activityItemsTestnetNotAvailable": "Testnet-Transaktionsverlauf zur Zeit nicht verfügbar", "historyUnknownDappName": "Unbekannt", "historyStatusSucceeded": "Erfolgreich", "historyNetwork": "Netzwerk", "historyAttemptedAmount": "Beabsichtigter Betrag", "historyAmount": "Betrag", "sendAddressBookButtonLabel": "Adressbuch", "addressBookSelectAddressBook": "Adressbuch", "sendAddressBookNoAddressesSaved": "<PERSON><PERSON>", "sendAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>", "addressBookSelectRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>", "sendConfirmationLabel": "Label", "sendConfirmationMessage": "Nachricht", "sendConfirmationNetworkFee": "Netzwerkgebühr", "sendConfirmationPrimaryText": "Senden bestätigen", "sendWarning_INSUFFICIENT_FUNDS": "Unzure<PERSON>ndes Guthaben, diese Transaktion wird wahrscheinlich fehlschlagen, wenn sie eingereicht wird.", "sendFungibleSummaryNetwork": "Netzwerk", "sendFungibleSummaryNetworkFee": "Netzwerkgebühr", "sendFungibleSummaryEstimatedTime": "Geschätzte Zeit", "sendFungiblePendingEstimatedTime": "Zeitschätzungen", "sendFungibleSummaryEstimatedTimeDescription": "Die Geschwindigkeit von Ethereum-Transaktionen hängt von mehreren Faktoren ab. Sie können die Geschwindigkeit erhöhen, indem Sie auf „Netzwerkgebühr“ klicken.", "sendSummaryBitcoinPendingTxTitle": "Transfer konnte nicht übermittelt werden", "sendSummaryBitcoinPendingTxDescription": "Sie können jeweils nur einen BTC-Transfer in Bearbeitung haben. Bitte warten Si<PERSON>, bis er abgeschlossen ist, um einen neuen Transfer zu tätigen.", "sendFungibleSatProtectionTitle": "Versenden mit Sat-Schutz", "sendFungibleSatProtectionExplainer": "Phantom stellt sicher, dass Ihre Ordinals und BRC20s nicht für Transaktionsgebühren oder Bitcoin-Überweisungen verwendet werden.", "sendFungibleTransferFee": "Token-Transfergebühr", "sendFungibleTransferFeeToolTip": "Der Ersteller dieses Tokens erhält für jeden Transfer eine Gebühr. Dies ist keine Gebühr, die von Phantom erhoben oder eingezogen wird.", "sendFungibleInterestBearingPercent": "Aktueller Zinssatz", "sendFungibleNonTransferable": "Nicht übertragbar", "sendFungibleNonTransferableToolTip": "Dieser To<PERSON> kann nicht zu einem anderen Konto übertragen werden.", "sendFungibleNonTransferableYes": "<PERSON>a", "sendStatusErrorMessageInterpolated": "Es ist ein Fehler beim Versuch aufgetreten, Token an <1>{{uiRecipient}}</1> zu senden", "sendStatusErrorMessageInsufficientBalance": "Sie haben nicht genügend Guthaben, um die Transaktion abzuschließen.", "sendStatusErrorTitle": "Senden nicht möglich", "sendStatusLoadingTitle": "Sende ...", "sendStatusSuccessMessageInterpolated": "Ihre Token wurden erfolgreich an <1>{{uiRecipient}}</1> gesendet", "sendStatusSuccessTitle": "Gesendet!", "sendStatusConfirmedSuccessTitle": "Gesendet!", "sendStatusSubmittedSuccessTitle": "Transaktion eingereicht", "sendStatusEstimatedTransactionTime": "Geschätzte Transaktionszeit: {{time}}", "sendStatusViewTransaction": "Transaktion ansehen", "sendFungibleLoadingMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> an <2>{{uiRecipient}}</2>", "sendFungibleSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> wurden erfolg<PERSON>ich an <2>{{uiRecipient}}</2> gesendet", "sendFungibleConfirmedSuccessMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> wurden erfolg<PERSON>ich an <2>{{uiRecipient}}</2> gesendet", "sendFungibleErrorMessageInterpolated": "<2>{{uiAmount}} {{assetSymbol}}</2> wurden nicht an <2>{{uiRecipient}}</2> gesendet", "sendFungibleSolanaErrorCode": "Fehlercode {{code}}", "sendFormErrorInsufficientBalance": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "sendFormErrorEmptyAmount": "Benötigter Betrag", "sendFormInvalidAddress": "{{assetName}}-<PERSON><PERSON><PERSON>", "sendFormInvalidUsernameOrAddress": "Benutzername oder Adresse ungültig", "sendFormErrorInvalidSolanaAddress": "Ungültige Solana-Adresse", "sendFormErrorInvalidTwitterHandle": "<PERSON>ses Twitter-Name ist nicht registriert", "sendFormErrorInvalidDomain": "Diese Domain ist nicht registriert", "sendFormErrorInvalidUsername": "Dieser Benutzername ist nicht registriert", "sendFormErrorMinRequiredInterpolated": "Mindestens {{minAmount}} {{tokenName}} er<PERSON><PERSON><PERSON>", "sendRecipientTextareaPlaceholder": "SOL-Adresse des Empfängers", "sendRecipientTextAreaPlaceholder2": "{{symbol}}-Adress<PERSON> des Empfängers", "sendMemoOptional": "Memo (optional)", "sendMemo": "Memo", "sendOptional": "optional", "settings": "Einstellungen", "settingsDapps": "dApps", "settingsSelectedAccount": "Gewähltes Konto", "settingsAddressBookNoLabel": "Kein Label", "settingsAddressBookPrimary": "Adressbuch", "settingsAddressBookRecentlyUsed": "<PERSON><PERSON><PERSON><PERSON>", "settingsAddressBookSecondary": "Häufig verwendete Adressen verwalten", "settingsAutoLockTimerPrimary": "Timer für automatische Sperre", "settingsAutoLockTimerSecondary": "Dauer des Timers für Ihre automatische Sperre ändern", "settingsChangeLanguagePrimary": "Sprache ändern", "settingsChangeLanguageSecondary": "Anzeigesprache ändern", "settingsChangeNetworkPrimary": "Netzwerk ändern", "settingsChangeNetworkSecondary": "Ihre Netzwerkeinstellungen konfigurieren", "settingsChangePasswordPrimary": "Passwort ändern", "settingsChangePasswordSecondary": "Passwort für die Bildschirmsperre ändern", "settingsCompleteBetaSurvey": "Beta-Umfrage abschließen", "settingsDisplayLanguage": "Anzeigesprache", "settingsErrorCannotExportLedgerPrivateKey": "Privater Ledger-Schlüssel kann nicht exportiert werden", "settingsErrorCannotRemoveAllWallets": "Kann nicht alle Konten entfernen", "settingsExportPrivateKey": "Privaten Schlüssel anzeigen", "settingsNetworkMainnetBeta": "Mainnet Beta", "settingsNetworkTestnet": "Testnet", "settingsNetworkDevnet": "Devnet", "settingsNetworkLocalhost": "Localhost", "settingsNetworkPhantomRPC": "Phantom-RPC-Netzwerk", "settingsTestNetworks": "Testnetzwerke", "settingsUseCustomNetworks": "Individuelle Netzwerke verwenden", "settingsTestnetMode": "Testnet-Modus", "settingsTestnetModeDescription": "Gilt für Salden und App-Verbindungen.", "settingsWebViewDebugging": "Debugging der Webansicht", "settingsWebViewDebuggingDescription": "Ermöglicht es Ihnen, die Webansichten des In-App-Browsers zu überprüfen und zu debuggen.", "settingsTestNetworksInfo": "Der Wechsel zu einem der Testnet-Netzwerke ist nur zu Testzwecken gedacht. <PERSON><PERSON> beachten Si<PERSON>, dass die Token in den Testnet-Netzwerken keinen Geldwert haben.", "settingsEmojis": "Emojis", "settingsNoAddresses": "<PERSON><PERSON>", "settingsAddressBookEmptyHeading": "<PERSON>hr Adressbuch ist leer", "settingsAddressBookEmptyText": "Klicken Sie auf die Schaltflächen „+“ oder „Adresse hinzufügen“, um Ihre bevorzugten Adressen hinzuzufügen", "settingsEditWallet": "<PERSON><PERSON> bearbeiten", "settingsNoTrustedApps": "<PERSON><PERSON> vertrauenswürdigen Anwendungen", "settingsNoConnections": "Noch keine Verbindungen.", "settingsRemoveWallet": "<PERSON><PERSON> entfernen", "settingsResetApp": "<PERSON><PERSON>", "settingsBlocked": "<PERSON><PERSON><PERSON><PERSON>", "settingsBlockedAccounts": "Geblockte Konten", "settingsNoBlockedAccounts": "<PERSON><PERSON> g<PERSON>.", "settingsRemoveSecretPhrase": "Geheime Phrase entfernen", "settingsResetAppWithSecretPhrase": "App mit geheimer Phrase zurücksetzen", "settingsResetSecretRecoveryPhrase": "Geheime Recovery-<PERSON><PERSON>", "settingsShowSecretRecoveryPhrase": "Geheime Recovery-Phrase anzeigen", "settingsShowSecretRecoveryPhraseSecondary": "Recovery-Phrase anzeigen", "settingsShowSecretRecoveryPhraseTertiary": "Geheime Phrase anzeigen", "settingsTrustedAppsAutoConfirmActiveUntil": "Bis {{formattedTimestamp}}", "settingsTrustedAppsAutoConfirm": "Auto-Bestätigung", "settingsTrustedAppsDisclaimer": "Aktivieren Sie Auto-Bestätigung nur für vertrauenswürdige Websites", "settingsTrustedAppsLastUsed": "Vor {{formattedTimestamp}} benutzt", "settingsTrustedAppsPrimary": "Verbundene Apps", "settingsTrustedApps": "Vertrauenswürdige Apps", "settingsTrustedAppsRevoke": "Widerrufen", "settingsTrustedAppsRevokeToast": "{{trustedApp}} getrennt", "settingsTrustedAppsSecondary": "Ihre vertrauenswürdigen Anwendungen konfigurieren", "settingsTrustedAppsToday": "<PERSON><PERSON>", "settingsTrustedAppsYesterday": "Gestern", "settingsTrustedAppsLastWeek": "Letzte Woche", "settingsTrustedAppsBeforeYesterday": "<PERSON><PERSON><PERSON>", "settingsTrustedAppsDisconnectAll": "Von allen trennen", "settingsTrustedAppsDisconnectAllToast": "Alle Apps getrennt", "settingsTrustedAppsEndAutoConfirmForAll": "Auto-Bestätigung für alle beenden", "settingsTrustedAppsEndAutoConfirmForAllToast": "Alle Auto-Bestätigungssitzungen beended", "settingsSecurityPrimary": "Sicherheit und Datenschutz", "settingsSecuritySecondary": "Aktualisieren Sie Ihre Sicherheitseinstellungen", "settingsActiveNetworks": "Aktive Netzwerke", "settingsActiveNetworksAll": "Alle", "settingsActiveNetworksSolana": "<PERSON><PERSON>", "settingsActiveNetworksSolanaDefaultNetworkExplainer": "Solana ist das Standardnetzwerk und bleibt immer aktiv.", "settingsDeveloperPrimary": "Entwickler-Einstellungen", "settingsAdvanced": "Erweiterte Einstellungen", "settingsTransactions": "Transaktionseinstellungen", "settingsAutoConfirm": "Auto-Bestätigungseinstellungen", "settingsSecurityAnalyticsPrimary": "Anonyme <PERSON><PERSON>", "settingsSecurityAnalyticsSecondary": "<PERSON><PERSON><PERSON> un<PERSON>, besser zu werden", "settingsSecurityAnalyticsHelper": "Phantom verwendet Ihre persönlichen Daten nicht für Analysezwecke", "settingsSuspiciousCollectiblesPrimary": "Verdächtige Sammelobjekte ausblenden", "settingsSuspiciousCollectiblesSecondary": "Einschalten, um gekennzeichnete Sammelobjekte auszublenden", "settingsPreferredBitcoinAddress": "Bevorzugte Bitcoin-Adresse", "settingsEnabledAddressesUpdated": "Sichtbare Adressen aktualisiert!", "settingsEnabledAddresses": "Aktivierte Adressen", "settingsBitcoinPaymentAddressForApps": "Zahlungsadresse für Apps", "settingsBitcoinOrdinalsAddressForApps": "Ordinals-Adresse für Apps", "settingsPreferredBitcoinAddressConnectToAppsExplainer": "Wenn beide oben genannten Adresstypen aktiviert sind, wird für bestimmte Apps wie Magic Eden Ihre native Segwit-Adresse zur Finanzierung von Käufen verwendet. Die gekauften Vermögenswerte werden über Ihre Taproot-Adresse empfangen.", "settingsPreferredBitcoinAddressNativeSegwitExplainer": "Die Standard-Bitcoin-Adresse in Phantom, um Kompatibilität zu gewährleisten.", "settingsPreferredBitcoinAddressNativeSegwitSecondaryTitle": "(Standard)", "settingsPreferredBitcoinAddressTaprootExplainer": "Der modernste Adresstyp, bei dem in der Regel niedrigere Transaktionsgebühren anfallen.", "settingsPreferredExplorers": "Bevorzugter Explorer", "settingsPreferredExplorersSecondary": "Wählen Sie Ihren bevorzugten Blockchain-Explorer", "settingsCustomGasControls": "Benutzerdefinierte Gas-Steuerung", "settingsSupportDesk": "Support-Zen<PERSON>um", "settingsSubmitATicket": "Ticket e<PERSON><PERSON>ichen", "settingsAttachApplicationLogs": "Anwendungsprotokolle anhängen", "settingsDownloadApplicationLogs": "App-<PERSON><PERSON><PERSON>", "settingsDownloadApplicationLogsShort": "<PERSON><PERSON><PERSON>", "settingsDownloadApplicationLogsHelper": "Enthält lokale Daten, Absturzberichte und öffentliche Wallet-Adressen, die bei der Lösung von Phantom-Support-Problemen helfen", "settingsDownloadApplicationLogsWarning": "Es sind keine sensiblen Daten wie Seed-Phrasen oder private <PERSON><PERSON><PERSON><PERSON> en<PERSON>.", "settingsWallet": "Wallet", "settingsPreferences": "Präferenzen", "settingsSecurity": "Sicherheit", "settingsDeveloper": "<PERSON><PERSON><PERSON><PERSON>", "settingsSupport": "Support", "settingsWalletShortcutsPrimary": "Wallet-Shortcuts anzeigen", "settingsAppIcon": "App-Icon", "settingsAppIconDefault": "Standard", "settingsAppIconLight": "Hell", "settingsAppIconDark": "<PERSON><PERSON><PERSON>", "settingsAppIconHoliday": "Holiday", "settingsSearchResultAccount": "Ko<PERSON>", "settingsSearchResultSelected": "Ausgewählt", "settingsSearchResultExport": "Exportieren", "settingsSearchResultSeed": "Seed", "settingsSearchResultTrusted": "<PERSON><PERSON><PERSON><PERSON>", "settingsSearchResultTestnet": "Testnet", "settingsSearchResultState": "Status", "settingsSearchResultLogs": "Protokolle", "settingsSearchResultBiometric": "Biometrisch", "settingsSearchResultTouch": "Touch", "settingsSearchResultFace": "Face", "settingsSearchResultShortcuts": "Tastaturkürzel", "settingsAllSitesPermissionsTitle": "Zugriff auf Phantom auf allen Seiten", "settingsAllSitesPermissionsSubtitle": "Ermöglicht Ihnen die nahtlose Nutzung von Apps mit Phantom, ohne auf die Erweiterung zu klicken", "settingsAllSitesPermissionsDisabled": "<PERSON><PERSON> Browser unterstützt das Ändern dieser Einstellung nicht", "settingsSolanaCopyTransaction": "„Transaktion kopieren“ aktivieren", "settingsSolanaCopyTransactionDetails": "Serialisierte Transaktionsdaten in die Zwischenablage kopieren", "settingsAutoConfirmHeader": "Auto-Bestätigung", "refreshWebpageToApplyChanges": "Aktualisieren Sie die Webseite, um die Änderungen zu übernehmen", "settingsExperimentalTitle": "Experimentelle Funktionen", "settingsExprimentalSolanaActionsSubtitle": "Solana-Aktionsschaltflächen automatisch erweitern, wenn relevante Links auf X.com erkannt werden", "stakeAccountCardActiveStake": "Aktiver Einsatz", "stakeAccountCardBalance": "<PERSON><PERSON><PERSON><PERSON>", "stakeAccountCardRentReserve": "Mietreserve", "stakeAccountCardRewards": "Letzte Belohnung", "stakeAccountCardRewardsTooltip": "Dies ist die letzte Belohnung, die Sie für Ihren Einsatz erhalten haben. Sie erhalten Belohnungen alle 3 Tage.", "stakeAccountCardStakeAccount": "<PERSON><PERSON><PERSON>", "stakeAccountCardLockup": "Sperrung bis", "stakeRewardsHistoryTitle": "Belohnungsverlau<PERSON>", "stakeRewardsActivityItemTitle": "Bel<PERSON>nungen", "stakeRewardsHistoryEmptyList": "<PERSON><PERSON>", "stakeRewardsTime_zero": "<PERSON><PERSON>", "stakeRewardsTime_one": "Gestern", "stakeRewardsTime_other": "vor {{count}} <PERSON>en", "stakeRewardsItemsPagingFailed": "Laden älterer Belohnungen fehlgeschlagen.", "stakeAccountCreateAndDelegateErrorStaking": "Bei dem Einsatz mit diesem Validierer ist ein Problem aufgetreten. Bitte versuchen Sie es erneut.", "stakeAccountCreateAndDelegateSolStaked": "SOL eingesetzt!", "stakeAccountCreateAndDelegateSolStakedDisclaimerInterpolated": "Ihre SOL werden in den nächsten Tagen mit dem Gewinn von Provisionen <1></1> beginnen, sobald das Einsatzkonto aktiv wird.", "stakeAccountCreateAndDelegateStakingFailed": "Einsatz fehlgeschlagen", "stakeAccountCreateAndDelegateStakingSol": "Setze SOL ein ...", "stakeAccountCreateAndDelegateStakingSolDisclaimer": "Wir erstellen ein Einsatzkonto und delegieren dann Ihr SOL an", "stakeAccountCreateAndDelegateStakingSolDisclaimerInterpolated": "Wir erstellen ein Einsatzkonto und delegieren dann Ihr SOL an {{validatorName}}", "stakeAccountCreateAndDelegateViewTransaction": "Transaktion ansehen", "stakeAccountDeactivateStakeSolUnstaked": "SOL-Einsatz zurückgenommen!", "stakeAccountDeactivateStakeSolUnstakedDescriptionInterpolated": "Sie werden Ihren Einsatz <1></1> in den nächsten Tagen abheben können, sobald das Einsatzkonto inaktiv wird.", "stakeAccountDeactivateStakeSolUnstakedDescription": "Sie werden Ihren Einsatz in den nächsten Tagen abheben können, sobald das Einsatzkonto inaktiv wird.", "stakeAccountDeactivateStakeUnstakingFailed": "Zurücknehmen des Einsatzes fehlgeschlagen", "stakeAccountDeactivateStakeUnstakingFailedDescription": "Es gab ein Problem beim Zurücknehmen des Einsatzes mit diesem Validierer. Bitte versuchen Sie es erneut.", "stakeAccountDeactivateStakeUnstakingSol": "Nehme SOL-Einsatz zurück ...", "stakeAccountDeactivateStakeUnstakingSolDescription": "Wir beginnen jetzt mit dem Verfahren zum Abheben Ihrer SOL.", "stakeAccountDeactivateStakeViewTransaction": "Transaktion ansehen", "stakeAccountDelegateStakeSolStaked": "SOL eingesetzt!", "stakeAccountDelegateStakeSolStakedDescriptionInterpolated": "Ihre SOL werden in den nächsten Tagen mit dem Gewinn von Provisionen <1></1> beginnen, sobald das Einsatzkonto aktiv wird.", "stakeAccountDelegateStakeStakingFailed": "Einsatz fehlgeschlagen", "stakeAccountDelegateStakeStakingFailedDescription": "Bei dem Einsatz mit diesem Validierer ist ein Problem aufgetreten. Bitte versuchen Sie es erneut.", "stakeAccountDelegateStakeStakingSol": "Setze SOL ein ...", "stakeAccountDelegateStakeStakingSolDescription": "Wir delegieren Ihre SOL.", "stakeAccountDelegateStakeViewTransaction": "Transaktion ansehen", "stakeAccountListActivationActivating": "Aktiviere", "stakeAccountListActivationActive": "Aktiv", "stakeAccountListActivationInactive": "Inaktiv", "stakeAccountListActivationDeactivating": "Deaktiviere", "stakeAccountListErrorFetching": "Wir konnten die Einsatzkonten nicht abrufen. Bitte versuchen Si<PERSON> es später noch einmal.", "stakeAccountListNoStakingAccounts": "<PERSON><PERSON>", "stakeAccountListReload": "Neu laden", "stakeAccountListViewPrimaryText": "Ihr Einsatz", "stakeAccountListViewStakeSOL": "SOL einsetzen", "stakeAccountListItemStakeFee": "{{fee}} <PERSON><PERSON><PERSON><PERSON>", "stakeAccountViewActionButtonRestake": "<PERSON><PERSON><PERSON> investieren", "stakeAccountViewActionButtonUnstake": "Einsatz zurücknehmen", "stakeAccountViewError": "<PERSON><PERSON>", "stakeAccountViewPrimaryText": "Ihr Einsatz", "stakeAccountViewRestake": "<PERSON><PERSON><PERSON> investieren", "stakeAccountViewSOLCurrentlyStakedInterpolated": "Ihre SOL sind derzeit mit einem Validierer eingesetzt. Sie müssen den Einsatz aufheben, um <1></1>auf diese Mittel zugreifen zu können. <3><PERSON>hr erfahren</3>", "stakeAccountViewStakeInactive": {"part1": "Dieses Einsatzkonto ist inaktiv. Ziehen Sie den Einsatz zurück oder suchen Si<PERSON> einen Validierer, an den Si<PERSON> ihn delegieren können.", "part2": "<PERSON><PERSON> er<PERSON>"}, "stakeAccountViewStakeNotFound": "Dieses Einsatzkonto konnte nicht gefunden werden.", "stakeAccountViewViewOnExplorer": "Auf {{explorer}} an<PERSON><PERSON>", "stakeAccountViewWithdrawStake": "Einsatz abheben", "stakeAccountViewWithdrawUnstakedSOL": "Nicht eingesetzte SOL abheben", "stakeAccountInsufficientFunds": "Nicht genügend SOL verfügbar zum Abheben oder Einsatz zurückzunehmen.", "stakeAccountWithdrawStakeSolWithdrawn": "SOL abgehoben!", "stakeAccountWithdrawStakeSolWithdrawnDescription": {"part1": "Ihr SOL wurde abgehoben.", "part2": "Dieses Einsatzkonto wird in den nächsten Minuten automatisch entfernt werden."}, "stakeAccountWithdrawStakeViewTransaction": "Transaktion ansehen", "stakeAccountWithdrawStakeWithdrawalFailed": "Abheben fehlgeschlagen", "stakeAccountWithdrawStakeWithdrawalFailedDescription": "Es gab ein Problem bei der Abhebung von diesem Einsatzkonto. Bitte versuchen Sie es erneut.", "stakeAccountWithdrawStakeWithdrawingSol": "Hebe SOL ab ...", "stakeAccountWithdrawStakeWithdrawingSolDescription": "Wir heben Ihre SOL von diesem Einsatzkonto ab.", "startEarningSolAccount": "Ko<PERSON>", "startEarningSolAccounts": "Konten", "startEarningSolErrorClosePhantom": "<PERSON><PERSON><PERSON> hier und versuchen Si<PERSON> es erneut", "startEarningSolErrorTroubleLoading": "Probleme beim Laden des Einsatzes", "startEarningSolLoading": "Lade ...", "startEarningSolPrimaryText": "Beginnen Sie SOL zu verdienen", "startEarningSolSearching": "Suche nach Einsatzkonten", "startEarningSolStakeTokens": "Token investieren und Belohnungen verdienen", "startEarningSolYourStake": "Ihr Einsatz", "unwrapFungibleTitle": "<PERSON><PERSON> zu {{tokenSymbol}}", "unwrapFungibleDescription": "<PERSON><PERSON><PERSON><PERSON> von {{fromToken}} für {{toToken}}", "unwrapFungibleConfirmSwap": "Tausch bestätigen", "unwrapFungibleConfirmSwapTitle": "{{fromToken}} → {{toToken}}", "swapFeesEstimatedFees": "Geschätzte Gebühren", "swapFeesFees": "Gebühren", "swapFeesPhantomFee": "Phantom-Gebühr", "swapFeesPhantomFeeDisclaimer": "Wir finden immer den bestmöglichen Preis bei den besten Liquiditätsanbietern. Eine Gebühr von {{feePercentage}} ist automatisch in diesem Angebot enthalten.", "swapFeesRate": "Pre<PERSON>", "swapFeesRateDisclaimer": "The best rate found by Jupiter Aggregator across multiple decentralized exchanges.", "swapFeesRateDisclaimerMultichain": "Der beste Kurs, den Jupiter Aggregator bei mehreren dezentralen Börsen gefunden hat.", "swapFeesPriceImpact": "Auswirkungen auf den Preis", "swapFeesHighPriceImpact": "Hohe Auswirkungen auf den Preis", "swapFeesPriceImpactDisclaimer": "Die Differenz zwischen dem Marktpreis und dem geschätzten Preis auf der Grundlage Ihrer Handelsgröße.", "swapFeesSlippage": "Verschiebung", "swapFeesHighSlippage": "Hohe Verschiebungstoleranz", "swapFeesHighSlippageDisclaimer": "Ihre Transaktion wird scheitern, wenn der Preis sich um mehr als {{slippage}} % ungünstig verändert.", "swapTransferFee": "Transfergebühr", "swapTransferFeeDisclaimer": "<PERSON><PERSON><PERSON> den Handel mit ${{symbol}} fällt eine Transfergebühr von {{feePercent}} % an, die vom Ersteller des Tokens festgelegt wird, nicht von <PERSON>.", "swapTransferFeeDisclaimerMany": "<PERSON><PERSON><PERSON> den <PERSON> mit den ausgewählten Token wird eine Gebühr von {{feePercent}} % erhoben, die von den Erstellern der Token festgelegt wird, nicht von <PERSON>.", "swapFeesSlippageDisclaimer": "Betrag, um den der Preis Ihres Handels vom angegebenen Angebot abweichen kann.", "swapFeesProvider": "<PERSON><PERSON><PERSON>", "swapFeesProviderDisclaimer": "Die dezentrale Börse, über die Sie Ihren Handel abwickeln.", "swapEstimatedTime": "Geschätzte Zeit", "swapEstimatedTimeShort": "Gesch. Zeit", "swapEstimatedTimeDisclaimer": "Die voraussichtliche Fertigstellung der Überbrückung hängt von mehreren Faktoren ab, die die Transaktionsgeschwindigkeit beeinflussen.", "swapSettingsButtonCommand": "Tausch-Einstellungen öffnen", "swapQuestionRetry": "<PERSON><PERSON><PERSON><PERSON><PERSON>?", "swapUnverifiedTokens": "Nicht verifizierte Token", "swapSectionTitleTokens": "{{section}} Token", "swapFlowYouPay": "<PERSON><PERSON> zahlen", "swapFlowYouReceive": "<PERSON>e erhalten", "swapFlowActionButtonText": "Bestellung überprüfen", "swapAssetCardTokenNetwork": "{{symbol}} in {{network}}", "swapAssetCardMaxButton": "<PERSON>.", "swapAssetCardSelectTokenAndNetwork": "Token und Netzwerk auswählen", "swapAssetCardBuyTitle": "<PERSON>e erhalten", "swapAssetCardSellTitle": "<PERSON><PERSON> zahlen", "swapAssetWarningUnverified": "Dieser Token wurde noch nicht verifiziert. Interagieren Sie nur mit Token, denen Si<PERSON> vertrauen.", "swapAssetWarningPermanentDelegate": "Ein Delegierter kann diese Token dauerhaft verbrennen oder überweisen.", "swapSlippageSettingsTitle": "Verschiebungseinstellungen", "swapSlippageSettingsSubtitle": "Ihre Transaktion wird scheitern, wenn sich der Preis stärker verändert als die Verschiebung. Ein zu hoher Wert führt zu einem ungünstigen Handel.", "swapSlippageSettingsCustom": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "swapSlippageSettingsHighSlippageWarning": "Ihr Geschäft kann vorzeitig ausgeführt werden und zu einem ungünstigen Handel führen.", "swapSlippageSettingsCustomMinError": "Bitte geben Si<PERSON> einen <PERSON> ein, der größer ist als {{minSlippage}} %.", "swapSlippageSettingsCustomMaxError": "<PERSON>te geben Si<PERSON> einen Wert ein, der kleiner ist als {{maxSlippage}} %.", "swapSlippageSettingsCustomInvalidValue": "Bitte geben Si<PERSON> einen gültigen Wert ein.", "swapSlippageSettingsAutoSubtitle": "Phantom wird die geringste Verschiebung für einen erfolgreichen Tausch finden.", "swapSlippageSettingsAuto": "Auto", "swapSlippageSettingsFixed": "Fest", "swapSlippageOptInTitle": "Auto-Verschiebung", "swapSlippageOptInSubtitle": "Phantom findet die niedrigste Verschiebung für einen erfolgreichen Tausch. Si<PERSON> können dies jederzeit unter Swapper → Verschiebungsinstellungen ändern.", "swapSlippageOptInEnableOption": "Auto-Verschiebung aktivieren", "swapSlippageOptInRejectOption": "Mit fester Verschiebung fortfahren", "swapQuoteFeeDisclaimer": "Angebot enthält eine {{feePercentage}} Phantom-Gebühr", "swapQuoteMissingContext": "Fehlender Kontext des Tauschangebots", "swapQuoteErrorNoQuotes": "Versuch eines Tausches ohne Preisangaben", "swapQuoteSolanaNetwork": "Solana-Netzwerk", "swapQuoteNetwork": "Netzwerk", "swapQuoteOneTimeSerumAccount": "Einmaliges Serum-Konto", "swapQuoteOneTimeTokenAccount": "Einmaliges Token-Konto", "swapQuoteBridgeFee": "Tauschgebühr über Chains", "swapQuoteDestinationNetwork": "Zielnetzwerk", "swapQuoteLiquidityProvider": "Liquiditätsanbieter", "swapReviewFlowActionButtonPrimary": "Tauschen", "swapReviewFlowPrimaryText": "Bestellung überprüfen", "swapReviewFlowYouPay": "<PERSON><PERSON> zahlen", "swapReviewFlowYouReceive": "<PERSON>e erhalten", "swapReviewInsufficientBalance": "Nicht genügend Guthaben", "ugcSwapWarningTitle": "<PERSON><PERSON><PERSON>", "ugcSwapWarningBody1": "Dieser Token wird auf dem Token-Launcher {{programName}} gehandelt.", "ugcSwapWarningBody2": "Der Wert dieser Token kann stark schwanken, was zu erheblichen finanziellen Gewinnen oder Verlusten führen kann. Bitte handeln Sie auf eigenes Risiko.", "ugcSwapWarningConfirm": "Verstanden", "bondingCurveProgressLabel": "Bonding-Curve-Fortschritt", "bondingCurveInfoTitle": "Bonding Curve", "bondingCurveInfoDescription": "In einem Bonding-Curve-Modell werden die Tokenpreise durch die Form der Kurve bestimmt, die steigt, wenn mehr Token gekauft werden, und sinkt, wenn Token verkauft werden. Wenn die Token ausverkauft sind, wird die gesamte Liquidität in Raydium eingezahlt und verbrannt.", "ugcFungibleWarningBanner": "Dieser Token wird auf {{programName}} gehandelt", "ugcCreatedRowLabel": "Erstellt am", "ugcStatusRowLabel": "Status", "ugcStatusRowValue": "Abgeschlossen", "swapTxConfirmationReceived": "<PERSON><PERSON><PERSON><PERSON>!", "swapTxConfirmationSwapFailed": "Tausch fehlgeschlagen", "swapTxConfirmationSwapFailedStaleQuota": "Das Angebot ist nicht mehr gültig. Bitte versuchen Si<PERSON> es erneut.", "swapTxConfirmationSwapFailedSlippageLimit": "Ihre Verschiebung ist für diesen Tausch zu niedrig. Bitte erhöhen Sie Ihre Verschiebung oben auf dem Tausch-Bildschirm und versuchen Sie es erneut.", "swapTxConfirmationSwapFailedInsufficientBalance": "Wir konnten die Anfrage nicht abschließen. Sie haben nicht genügend Guthaben, um die Transaktion abzuschließen.", "swapTxConfirmationSwapFailedEmptyRoute": "Die Liquidität für dieses Token-Paar hat sich geändert. Wir konnten kein passendes Angebot finden. Bitte versuchen Sie es erneut oder passen Sie die Beträge der Token an.", "swapTxConfirmationSwapFailedAcountFrozen": "Dieser Token wurde von seinem Ersteller eingefroren. <PERSON><PERSON> können diesen Token nicht senden oder tauschen.", "swapTxConfirmationSwapFailedTryAgain": "Der Tausch ist fehlgeschlagen, bitte versuchen Sie es erneut", "swapTxConfirmationSwapFailedUnknownError": "Der Tausch konnte nicht abgeschlossen werden. Ihr Guthaben ist davon nicht betroffen. Bitte versuchen Si<PERSON> es erneut. ", "swapTxConfirmationSwapFailedSimulationTimeout": "Der Tausch konnte nicht simuliert werden. Ihr Guthaben ist davon nicht betroffen. Bitte versuchen Si<PERSON> es erneut.", "swapTxConfirmationSwapFailedSimulationUnknownError": "Der Tausch konnte nicht abgeschlossen werden. Ihr Guthaben ist davon nicht betroffen. Bitte versuchen Si<PERSON> es erneut. ", "swapTxConfirmationSwapFailedInsufficientGas": "Das Guthaben auf Ihrem Konto reicht nicht aus, um die Transaktion abzuschließen. Bitte zahlen Sie Guthaben auf Ihr Konto ein und versuchen Si<PERSON> es erneut.", "swapTxConfirmationSwapFailedLedgerReject": "Der Tausch wurde vom Benutzer auf dem Hardware-Gerät abgelehnt.", "swapTxConfirmationSwapFailedLedgerConnectionError": "Der Tausch wurde aufgrund eines Geräteverbindungsfehlers abgelehnt. Bitte versuchen Sie es erneut.", "swapTxConfirmationSwapFailedLedgerSignError": "Der Tausch wurde aufgrund eines Gerätesignaturfehlers abgelehnt. Bitte versuchen Sie es erneut.", "swapTxConfirmationSwapFailedLedgerError": "Der Tausch wurde aufgrund eines Gerätefehlers abgelehnt. Bitte versuchen Sie es erneut.", "swapTxConfirmationSwappingTokens": "Tausche Token ...", "swapTxConfirmationTokens": "Token", "swapTxConfirmationTokensDeposited": "Es ist vollbracht! Die Token wurden in Ihr Wallet eingezahlt", "swapTxConfirmationTokensDepositedTitle": "Es ist vollbracht!", "swapTxConfirmationTokensDepositedBody": "Die Token wurden in Ihr Wallet eingezahlt", "swapTxConfirmationTokensWillBeDeposited": "wird nach Abschluss der Transaktion auf Ihr Wallet überwiesen", "swapTxConfirmationViewTransaction": "Transaktion ansehen", "swapTxBridgeSubmitting": "Übertragung der Transaktion", "swapTxBridgeSubmittingDescription": "<PERSON><PERSON> von {{sellAmount}} auf {{sellNetwork}} für {{buyAmount}} auf {{buyNetwork}}", "swapTxBridgeFailed": "Übertragung der Transaktion fehlgeschlagen", "swapTxBridgeFailedDescription": "Wir waren nicht in der Lage, die Anfrage zu bearbeiten.", "swapTxBridgeSubmitted": "Transaktion eingereicht", "swapTxBridgeSubmittedDescription": "Geschätzte Transaktionszeit: {{estimatedTime}}", "swapTxBridgeSubmittedDisclaimer": "<PERSON><PERSON> kö<PERSON>n dieses Fenster nun schließen.", "swapperSwitchTokens": "Token wechseln", "swapperMax": "<PERSON>.", "swapperTooltipNetwork": "Netzwerk", "swapperTooltipPrice": "Pre<PERSON>", "swapperTooltipAddress": "Vertrag", "swapperTrendingSortBy": "Sortieren nach", "swapperTrendingTimeFrame": "Zeitraum", "swapperTrendingNetwork": "Netzwerk", "swapperTrendingRank": "<PERSON>ng", "swapperTrendingTokens": "Beliebte Token", "swapperTrendingVolume": "Volumen", "swapperTrendingPrice": "Pre<PERSON>", "swapperTrendingPriceChange": "Preisänderung", "swapperTrendingMarketCap": "Marktkapitalisierung", "swapperTrendingTimeFrame1h": "1 h", "swapperTrendingTimeFrame24h": "24 h", "swapperTrendingTimeFrame7d": "7 d", "swapperTrendingTimeFrame30d": "30 d", "swapperTrendingNoTokensFound": "<PERSON><PERSON> gefunden.", "switchToggle": "Umschalten", "termsOfServiceActionButtonAgree": "Ich stimme zu", "termsOfServiceDisclaimerFeesDisabledInterpolated": "Indem Sie auf <1>\"Ich stimme zu\"</1> klick<PERSON>, akzeptieren Sie die <3>Bedingungen und Konditionen</3> für den Tausch von Token mit Phantom.", "termsOfServiceDiscliamerFeesEnabledInterpolated": "Wir haben unsere Servicebedingungen überarbeitet. Indem Sie auf <1>\"Ich stimme zu\"</1> klick<PERSON>, erkl<PERSON>ren Sie sich mit unseren neuen <3>Nutzungsbedingungen</3> einverstanden.<5></5><6></6>Unsere neuen Nutzungsbedingungen beinhalten eine neue <8>Gebührenstruktur</8> für bestimmte Produkte.", "termsOfServicePrimaryText": "Nutzungsbedingungen", "tokenRowUnknownToken": "Unbekannter Token", "transactionsAppInteraction": "App-Interaktion", "transactionsFailedAppInteraction": "App-Interaktion fehlgeschlagen", "transactionsBidOnInterpolated": "Geboten auf {{name}}", "transactionsBidFailed": "Gebot fehlgeschlagen", "transactionsBoughtInterpolated": "{{name}} gekauft", "transactionsBoughtCollectible": "Sammelobjekt gekauft", "transactionBridgeInitiated": "<PERSON><PERSON><PERSON><PERSON> initi<PERSON>", "transactionBridgeInitiatedFailed": "Initiierung der Brücke fehlgeschlagen", "transactionBridgeStatusLink": "Status auf LI.FI prüfen", "transactionsBuyFailed": "<PERSON><PERSON>", "transactionsBurnedSpam": "Spam verbrannt", "transactionsBurned": "Verbrannt", "transactionsUnwrapped": "Ausgepackt", "transactionsUnwrappedFailed": "Auspacken fehlgeschlagen", "transactionsCancelBidOnInterpolated": "G<PERSON>ot auf {{name}} storniert", "transactionsCancelBidOnFailed": "Stornierung des Gebots fehlgeschlagen", "transactionsError": "<PERSON><PERSON>", "transactionsFailed": "Fehlgeschlagen", "transactionsSwapped": "<PERSON><PERSON><PERSON>", "transactionsFailedSwap": "Tausch fehlgeschlagen", "transactionsFailedBurn": "Verbrennen fehlgeschlagen", "transactionsFrom": "<PERSON>", "transactionsListedInterpolated": "{{name}} gelistet", "transactionsListedFailed": "Listung fehlgeschlagen", "transactionsNoActivity": "Keine Aktivität", "transactionsReceived": "<PERSON><PERSON><PERSON><PERSON>", "transactionsReceivedInterpolated": "{{amount}} SOL erhalten", "transactionsSending": "Sende ...", "transactionsPendingCreateListingInterpolated": "<PERSON><PERSON><PERSON> {{name}}", "transactionsPendingEditListingInterpolated": "Bearbeite {{name}}", "transactionsPendingSolanaPayTransaction": "Bestätigung der Solana-Pay-Transaktion", "transactionsPendingRemoveListingInterpolated": "Entferne Listung von {{name}}", "transactionsPendingBurningInterpolated": "Verbrenne {{name}}", "transactionsPendingSending": "Send<PERSON>", "transactionsPendingSwapping": "<PERSON><PERSON>", "transactionsPendingBridging": "Überbrückung", "transactionsPendingApproving": "<PERSON><PERSON><PERSON><PERSON>", "transactionsPendingCreatingAndDelegatingStake": "Erstelle und delegiere Einsatz", "transactionsPendingDeactivatingStake": "Deaktiviere Einsatz", "transactionsPendingDelegatingStake": "Delegiere Einsatz", "transactionsPendingWithdrawingStake": "<PERSON><PERSON> Einsatz ab", "transactionsPendingAppInteraction": "Ausstehende App-Interaktion", "transactionsPendingBitcoinTransaction": "Ausstehende BTC-Transaktion", "transactionsSent": "Gesendet", "transactionsSendFailed": "Senden fehlgeschlagen", "transactionsSwapOn": "<PERSON><PERSON> auf {{dappName}}", "transactionsSentInterpolated": "{{amount}} SOL gesendet", "transactionsSoldInterpolated": "{{name}} verkauft", "transactionsSoldCollectible": "Sammelobjekt verkauft", "transactionsSoldFailed": "Verkauf fehlgeschlagen", "transactionsStaked": "Eingesetzt", "transactionsStakedFailed": "Einsatz fehlgeschlagen", "transactionsSuccess": "Erfolg", "transactionsTo": "An", "transactionsTokenSwap": "Token-Tausch", "transactionsUnknownAmount": "Unbekannt", "transactionsUnlistedInterpolated": "{{name}} nicht gelistet", "transactionsUnstaked": "<PERSON>cht eingesetzt", "transactionsUnlistedFailed": "Aufhebung der Listung fehlgeschlagen", "transactionsDeactivateStake": "Einsatz deaktiviert", "transactionsDeactivateStakeFailed": "Deaktivierung des Einsatzes fehlgeschlagen", "transactionsWaitingForConfirmation": "Warte auf Bestätigung", "transactionsWithdrawStake": "Einsatz abheben", "transactionsWithdrawStakeFailed": "Zurücknahme des Einsatzes fehlgeschlagen", "transactionCancelled": "<PERSON><PERSON><PERSON><PERSON>", "transactionCancelledFailed": "Stornierung fehlgeschlagen", "transactionApproveToken": "Genehmigte {{tokenSymbol}}", "transactionApproveTokenFailed": "{{tokenSymbol}} konnte nicht genehmigt werden", "transactionApprovalFailed": "Genehmigung fehlgeschlagen", "transactionRevokeApproveToken": "Widerrufene {{tokenSymbol}}", "transactionRevokeApproveTokenFailed": "{{tokenSymbol}} konnte nicht widerrufen werden", "transactionRevokeFailed": "Widerrufen fehlgeschlagen", "transactionApproveDetailsTitle": "Details zur Genehmigung", "transactionCancelOrder": "Bestellung stornieren", "transactionCancelOrderFailed": "Stornierung der Bestellung fehlgeschlagen", "transactionApproveAppLabel": "App", "transactionApproveAmountLabel": "Betrag", "transactionApproveTokenLabel": "Token", "transactionApproveCollectionLabel": "<PERSON><PERSON><PERSON>", "transactionApproveAllItems": "Alle Artikel genehmigen", "transactionSpendUpTo": "Ausgeben bis zu", "transactionCancel": "Transaktion stornieren", "transactionPrioritizeCancel": "Stornierung priorisieren", "transactionSpeedUp": "Transaktion beschleunigen", "transactionCancelHelperText": "Die ursprüngliche Transaktion kann abgeschlossen werden, bevor sie abgebrochen wird.", "transactionSpeedUplHelperText": "Dad<PERSON><PERSON> wird die Geschwindigkeit Ihrer Transaktion je nach Netzwerkbedingungen maximiert.", "transactionCancelHelperMobile": "<PERSON> Versuch, diese Transaktion zu stornieren, kostet <1>bis zu {{amount}}</1>. Die ursprüngliche Transaktion kann abgeschlossen werden, bevor sie storniert wird.", "transactionCancelHelperMobileWithEstimate": "<PERSON> Versuch, diese Transaktion zu stornieren, kostet <1>bis zu {{amount}}</1>. Sie sollte in etwa {{timeEstimate}} abgeschlossen sein. Die ursprüngliche Transaktion kann abgeschlossen werden, bevor sie abgebrochen wird.", "transactionSpeedUpHelperMobile": "Es wird <1>bis zu {{amount}}</1> kosten, die Geschwindigkeit dieser Transaktion zu maximieren.", "transactionSpeedUpHelperMobileWithEstimate": "Es wird <1>bis zu {{amount}}</1> kosten, um die Geschwindigkeit dieser Transaktion zu maximieren. Sie sollte in etwa {{timeEstimate}} abgeschlossen sein.", "transactionEstimatedTime": "Geschätzte Zeit", "transactionCancelingSend": "Storniere Senden", "transactionPrioritizingCancel": "Priorisiere Stornierung", "transactionCanceling": "Storniere", "transactionReplaceError": "Es ist ein Fehler aufgetreten. Ihrem Konto wurden keine Gebühren belastet.  Sie können es erneut versuchen.", "transactionNotEnoughNative": "Nicht genug {{nativeTokenSymbol}}", "transactionGasLimitError": "Schätzung des Gaslimits fehlgeschlagen", "transactionGasEstimationError": "Schätzung des Gas fehlgeschlagen", "pendingTransactionCancel": "Abbrechen", "pendingTransactionSpeedUp": "Beschleunigen", "pendingTransactionStatus": "Status", "pendingTransactionPending": "<PERSON><PERSON><PERSON><PERSON>", "pendingTransactionPendingInteraction": "Ausstehende Interaktion", "pendingTransactionCancelling": "Storniere", "pendingTransactionDate": "Datum", "pendingTransactionNetworkFee": "Netzwerkgebühr", "pendingTransactionEstimatedTime": "Geschätzte Zeit", "pendingTransactionEstimatedTimeHM": "{{hours}} h {{minutes}} m", "pendingTransactionEstimatedTimeMS": "{{minutes}} m {{seconds}} s", "pendingTransactionEstimatedTimeS": "{{seconds}} s", "pendingTransactionsSendingTitle": "Sende {{assetSymbol}}", "pendingTransactionsUnknownEstimatedTime": "Unbekannt", "pendingTransactionUnknownApp": "Unbekannte App", "permanentDelegateTitle": "<PERSON><PERSON><PERSON><PERSON>", "permanentDelegateValue": "Permanent", "permanentDelegateTooltipTitle": "Permanente Delegierung", "permanentDelegateTooltipValue": "Mit dem permanenten Delegieren können Token in Ihrem Namen von e<PERSON>m anderen Konto verwaltet werden, einschließlich Verbrennen oder Übertragen.", "unlockActionButtonUnlock": "Freischalten", "unlockEnterPassword": "Geben Sie Ihr Passwort ein", "unlockErrorIncorrectPassword": "Falsches Passwort", "unlockErrorSomethingWentWrong": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal", "unlockForgotPassword": "Passwort vergessen", "unlockPassword": "Passwort", "forgotPasswordText": "Sie können Ihr Passwort zurücksetzen, indem Sie die 12–24 Wörter der Recovery-Phrase Ihres Wallets eingeben. Phantom kann Ihr Passwort nicht für Sie wiederherstellen.", "appInfo": "App-Info", "lastUsed": "Zuletzt verwendet", "url": "URL", "trustedAppAutoConfirmDisabledHardwareAccount": "<PERSON>cht verfügbar mit Hardware-Konten.", "trustedAppAutoConfirmDisclaimer1": "<PERSON><PERSON><PERSON> aktiv, bestätigt Phantom alle Anfragen von dieser App, ohne <PERSON> zu benachrichtigen oder um Bestätigung zu bitten.", "trustedAppAutoConfirmDisclaimer2": "<PERSON>n Si<PERSON> diese Funktion aktivieren, besteht die Gefahr, dass Ihr Geld durch Betrug verloren geht. Verwenden Sie diese Funktion nur bei Apps, denen Si<PERSON> vertrauen.", "validationUtilsPasswordIsRequired": "Passwort ist erforderlich", "validationUtilsPasswordLength": "Das Passwort muss 8 <PERSON><PERSON><PERSON> lang sein", "validationUtilsPasswordsDontMatch": "Passwörter stimmen nicht überein", "validationUtilsPasswordCantBeSame": "Sie können Ihr altes Passwort nicht mehr verwenden", "validatorCardEstimatedApy": "Geschätzte APY", "validatorCardCommission": "Kommission", "validatorCardTotalStake": "Einsatz insgesamt", "validatorCardNumberOfDelegators": "# Delegierte", "validatorListChooseAValidator": "Wählen Sie einen Validierer", "validatorListErrorFetching": "Wir konnten keine Validierer abrufen. Bitte versuchen Sie es später noch einmal.", "validatorListNoResults": "<PERSON><PERSON>", "validatorListReload": "Neu laden", "validatorInfoTooltip": "<PERSON><PERSON><PERSON><PERSON>", "validatorInfoTitle": "<PERSON><PERSON><PERSON><PERSON>", "validatorInfoDescription": "Indem Sie Ihr SOL auf einen Validierer setzen, tragen Sie zur Leistung und Sicherheit des Solana-Netzwerks bei und verdienen im Gegenzug SOL.", "validatorApyInfoTooltip": "Gesch. APY", "validatorApyInfoTitle": "Geschätzte APY", "validatorApyInfoDescription": "Dies ist die Rendite, die <PERSON> erhalten, wenn Sie Ihren SOL-Einsatz auf den Validierer setzen.", "validatorViewActionButtonStake": "Einsatz", "validatorViewErrorFetching": "Konnte keine Validierer abrufen.", "validatorViewInsufficientBalance": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "validatorViewMax": "<PERSON>.", "validatorViewPrimaryText": "Starten Sie die Einsätze", "validatorViewDescriptionInterpolated": "<PERSON><PERSON><PERSON><PERSON>, wie viel SOL Sie <1></1>mit diesem Validierer einsetzen möchten. <3><PERSON><PERSON> erfahren</3>", "validatorViewAmountSOLRequiredToStakeInterpolated": "{{amount}} SOL erforderlich für den Einsatz", "validatorViewValidator": "<PERSON><PERSON><PERSON><PERSON>", "walletMenuItemsAddConnectWallet": "Wallet hinzufügen/verbinden", "walletMenuItemsBridgeAssets": "Vermögenswerte überbrücken", "walletMenuItemsHelpAndSupport": "Hilfe & Support", "walletMenuItemsLockWallet": "Wallet sperren", "walletMenuItemsResetSecretPhrase": "Geheime Phrase zurücksetzen", "walletMenuItemsShowMoreAccounts": "{{count}} mehr anzeigen ...", "walletMenuItemsHideAccounts": "Konten ausblenden", "toggleMultiChainHeader": "Multichain", "disableMultiChainHeader": "Nur-Solana-Modus", "disableMultiChainDetail1Header": "Setzen Sie voll auf Solana", "disableMultiChainDetail1SecondaryText": "Verwalten Sie Ihre Solana-Ko<PERSON>n, Token und Sammelobjekte, ohne andere Chains zu sehen.", "disableMultiChainDetail2Header": "Rückkehr zu Multichain jederzeit möglich", "disableMultiChainDetail2SecondaryText": "Ihr bestehendes Ethereum- und Polygon-Guthaben bleibt erhalten, wenn Sie Multichain wieder aktivieren.", "disableMultiChainButton": "Nur-Solana-Modus aktivieren", "disabledMultiChainHeader": "Nur-Solana-Modus aktiviert", "disabledMultiChainText": "Sie können Multichain jederzeit wieder aktivieren.", "enableMultiChainHeader": "Multichain aktivieren", "enabledMultiChainHeader": "Multichain aktiviert", "enabledMultiChainText": "Ethereum und Polygon werden jetzt in Ihrem Wallet unterstützt.", "incompatibleAccountHeader": "Inkompatibles Konto", "incompatibleAccountInterpolated": "Bitte entfernen Sie diese reinen Ethereum-Konten, bevor <PERSON> Nur-Solana-Modus zu aktivieren: <1>{{incompatibleAccounts}}</1>", "welcomeToMultiChainWhatsNew": "Was g<PERSON><PERSON>'s <PERSON><PERSON><PERSON>?", "welcomeToMultiChainPrimaryText": "Ein Wallet für alles", "welcomeToMultiChainDetail1Header": "Unterstützung von Ethereum und Polygon", "welcomeToMultiChainDetail1SecondaryText": "Alle Ihre Token und NFTs von <PERSON>ana, Ethereum und Polygon an einem Ort.", "welcomeToMultiChainDetail2Header": "Nutzen Sie alle Ihre Lieblingsapps", "welcomeToMultiChainDetail2SecondaryText": "Verbinden Sie sich mit Apps auf mehreren Chains, ohne das Netzwerk zu wechseln.", "welcomeToMultiChainDetail3Header": "Importieren Sie Ihr MetaMask-Wallet", "welcomeToMultiChainDetail3SecondaryText": "Importieren Sie ganz einfach alle Ihre Seed-Phrasen über Ethereum und Polygon.", "welcomeToMultiChainIntro": "Willkommen bei der Phantom Multichain", "welcomeToMultiChainIntroDesc": "Alle Ihre Token und NFTs von Solana, Ethereum und Polygon an einem Ort. Ihre eine Wallet für alles.", "welcomeToMultiChainAccounts": "Multichain-Konten überarbeitet", "welcomeToMultiChainAccountsDesc": "Überarbeitet für Multichain, jedes <PERSON> hat jetzt entsprechende ETH- und Polygon-Adressen.", "welcomeToMultiChainApps": "Funktioniert überall", "welcomeToMultiChainAppsDesc": "Phantom ist mit jeder App auf Ethereum, Polygon und Solana kompatibel. Klicken Sie auf „Verbinden mit MetaMask” und schon kann es los<PERSON>hen.", "welcomeToMultiChainImport": "Sofort aus MetaMask importieren", "welcomeToMultiChainImportDesc": "Importieren Sie Ihre geheimen Phrasen oder privaten Schlüssel aus Wallets wie MetaMask oder Coinbase Wallet. Alles an einem Ort.", "welcomeToMultiChainImportInterpolated": "<0>Importieren Sie Ihre geheimen Phrasen</0> oder privaten Schlüssel aus Wallets wie MetaMask oder Coinbase Wallet. Alles an einem Ort.", "welcomeToMultiChainTakeTour": "Tour unternehmen", "welcomeToMultiChainSwapperTitle": "Tausch auf Ethereum,\nPolygon und Solana", "welcomeToMultiChainSwapperDetail1Header": "Unterstützung von Ethereum und Polygon", "welcomeToMultiChainSwapperDetail1SecondaryText": "Jetzt können Sie ERC-20-Token ganz einfach in Ihrem Wallet tauschen.", "welcomeToMultiChainSwapperDetail2Header": "Beste Preise und super niedrige Gebühren", "welcomeToMultiChainSwapperDetail2SecondaryText": "Mehr als 100 Liquiditätsquellen und intelligentes Routing von Bestellungen für maximale Erträge.", "networkErrorTitle": "Netzwerkfehler", "networkError": "<PERSON><PERSON> können wir nicht auf das Netzwerk zugreifen. Bitte versuchen Si<PERSON> es später noch einmal.", "authenticationUnlockPhantom": "Phantom freischalten", "errorAndOfflineSomethingWentWrong": "Etwas ist schief gelaufen", "errorAndOfflineSomethingWentWrongTryAgain": "Bitte versuchen Sie es erneut.", "errorAndOfflineUnableToFetchAssets": "Wir konnten keine Vermögenswerte abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchCollectibles": "Wir konnten keine Sammelobjekte abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchSwap": "Wir konnten keine Tauschinformationen abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchTransactionHistory": "Wir können Ihren Transaktionsverlauf im Moment nicht abrufen. Überprüfen Sie Ihre Netzwerkverbindung oder versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchRewardsHistory": "Wir konnten keine Belohnungsinformationen abrufen. Bitte versuchen Sie es später noch einmal.", "errorAndOfflineUnableToFetchBlockedUsers": "Wir konnten keine geblockten Benutzer abrufen. Bitte versuchen Sie es später noch einmal.", "networkHealthSheetCloseButtonText": "OK", "swapReviewError": "Bei der Überprüfung Ihrer Bestellung ist ein Fehler aufgetreten. Bitte versuchen Sie es erneut.", "sendSelectToken": "Token wählen", "swapBalance": "Guthaben:", "swapTitle": "<PERSON><PERSON> tauschen", "swapSelectToken": "Token wählen", "swapYouPay": "<PERSON><PERSON> zahlen", "swapYouReceive": "<PERSON>e erhalten", "aboutPrivacyPolicy": "Datenschutz", "aboutVersion": "Version {{version}}", "aboutVisitWebsite": "Website besuchen", "bottomSheetConnectTitle": "Verbinden", "A11YbottomSheetConnectTitle": "Unterer Teil – Verbinden", "A11YbottomSheetCommandClose": "<PERSON><PERSON><PERSON> Teil – <PERSON><PERSON><PERSON><PERSON>", "A11YbottomSheetCommandBack": "Unterer Teil – Zurück", "bottomSheetSignTypedDataTitle": "Nachricht signieren", "bottomSheetSignMessageTitle": "Nachricht signieren", "bottomSheetSignInTitle": "Anmelden", "bottomSheetSignInAndConnectTitle": "Anmelden", "bottomSheetConfirmTransactionTitle": "Transaktion bestätigen", "bottomSheetConfirmTransactionsTitle": "Transaktionen bestätigen", "bottomSheetSolanaPayTitle": "Solana-Pay-Antrag", "bottomSheetAdvancedTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bottomSheetReadOnlyAccountTitle": "Nur-Ansicht-Modus", "bottomSheetTransactionSettingsTitle": "Netzwerkgebühr", "bottomSheetConnectDescription": "Die Verbindung ermöglicht es dieser Website, die Guthaben und Aktivitäten für das ausgewählte Konto einzu<PERSON>hen.", "bottomSheetSignInDescription": "Das Signieren dieser Nachricht beweist, dass Sie Eigentümer des ausgewählten Kontos sind. Signieren Sie nur Nachrichten von <PERSON>n, denen <PERSON> ve<PERSON>rauen.", "bottomSheetSignInAndConnectDescription": "Die Genehmigung ermöglicht es dieser Website, die Guthaben und Aktivitäten für das ausgewählte Konto einzu<PERSON>hen.", "bottomSheetConfirmTransactionDescription": "Veränderungen des Guthabens sind geschätzt. Die betreffenden Beträge und Vermögenswerte sind nicht garantiert.", "bottomSheetConfirmTransactionsDescription": "Veränderungen des Guthabens sind geschätzt. Die betreffenden Beträge und Vermögenswerte sind nicht garantiert.", "bottomSheetSignTypedDataDescription": "Dies ist nur eine Erlaubnisanfrage. Die Transaktion wird möglicherweise nicht sofort ausgeführt.", "bottomSheetSignTypedDataSecondDescription": "Veränderungen des Guthabens sind geschätzt. Die betreffenden Beträge und Vermögenswerte sind nicht garantiert.", "bottomSheetSignMessageDescription": "Das Signieren dieser Nachricht beweist, dass Sie Eigentümer des ausgewählten Kontos sind. Signieren Sie nur Nachrichten von <PERSON>n, denen <PERSON> ve<PERSON>rauen.", "bottomSheetReadOnlyAccountDescription": "Diese Aktion kann im Nur-Ansicht-Modus nicht durchgeführt werden.", "bottomSheetMessageRow": "Nachricht", "bottomSheetStatementRow": "Erklärung", "bottomSheetAutoConfirmRow": "Auto-Bestätigung", "bottomSheetAutoConfirmOff": "Aus", "bottomSheetAutoConfirmOn": "An", "bottomSheetAccountRow": "Ko<PERSON>", "bottomSheetAdvancedRow": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bottomSheetContractRow": "Vertragsadresse", "bottomSheetSpenderRow": "<PERSON><PERSON>der-<PERSON><PERSON><PERSON>", "bottomSheetNetworkRow": "Netzwerk", "bottomSheetNetworkFeeRow": "Netzwerkgebühr", "bottomSheetEstimatedTimeRow": "Geschätzte Zeit", "bottomSheetAccountRowDefaultAccountName": "Ko<PERSON>", "bottomSheetConnectRequestDisclaimer": "<PERSON>ur Verbindungen zu Websites herstellen, denen Si<PERSON> vertrauen", "bottomSheetSignInRequestDisclaimer": "Melden Sie sich nur bei Websites an, denen Si<PERSON> vertrauen", "bottomSheetSignatureRequestDisclaimer": "Bestätigen Sie nur, wenn <PERSON><PERSON> dieser Website vertrauen.", "bottomSheetFeaturedTransactionDisclaimer": "Sie sehen eine Vorschau der Transaktion, bevor Si<PERSON> im nächsten Schritt bestätigen.", "bottomSheetIgnoreWarning": "<PERSON>nung ignorieren, trotzdem fortfahren", "bottomSheetTransactionApprovalNoAssetOwnershipImpactingChanges": "<PERSON><PERSON> G<PERSON>abenänderungen gefunden. Bitte gehen Sie mit Bedacht vor und bestätigen Si<PERSON> nur, wenn <PERSON><PERSON> dieser Se<PERSON> vertrauen.", "bottomSheetReadOnlyWarning": "Sie beobachten diese Adresse nur. Sie müssen importieren, um Transaktionen und Nachrichten zu signieren.", "bottomSheetWebsiteIsUnsafeWarning": "Diese Website ist unsicher und könnte versuchen, <PERSON><PERSON> Guthaben zu stehlen.", "bottomSheetViewOnExplorer": "<PERSON><PERSON> <PERSON>", "bottomSheetTransactionSubmitted": "Transaktion eingereicht", "bottomSheetTransactionPending": "Transaktion ausstehend", "bottomSheetTransactionFailed": "Transaktion fehlgeschlagen", "bottomSheetTransactionSubmittedDescription": "Ihre Transaktion wurde eingereicht. Sie können sie im <PERSON> an<PERSON>.", "bottomSheetTransactionFailedDescription": "Ihre Transaktion ist fehlgeschlagen. Bitte versuchen Sie es erneut.", "bottomSheetTransactionPendingDescription": "Die Transaktion wird bearbeitet...", "transactionsFromInterpolated": "Von: {{from}}", "transactionsFromParagraphInterpolated": "Von {{from}}", "transactionsSolInterpolated": "{{amount}} SOL", "transactionsToday": "<PERSON><PERSON>", "transactionsToInterpolated": "An: {{to}}", "transactionsToParagraphInterpolated": "An {{to}}", "transactionsYesterday": "Gestern", "addEditAddressAdd": "<PERSON><PERSON><PERSON>", "addEditAddressDelete": "<PERSON><PERSON><PERSON>", "addEditAddressDeleteTitle": "Sind <PERSON> sicher, dass Sie diese Adresse löschen möchten?", "addEditAddressSave": "<PERSON><PERSON><PERSON> s<PERSON>", "dAppBrowserComingSoon": "dApp Browser ist demnächst verfügbar!", "dAppBrowserSearchPlaceholder": "Websites, Token, URL", "dAppBrowserOpenInNewTab": "In neuer Registerkarte öffnen", "dAppBrowserSuggested": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserFavorites": "<PERSON><PERSON>", "dAppBrowserBookmarks": "Lesezeichen", "dAppBrowserBookmarkAdd": "Lesezeichen hinzufügen", "dAppBrowserBookmarkRemove": "Lesezeichen entfernen", "dAppBrowserUsers": "<PERSON><PERSON><PERSON>", "dAppBrowserRecents": "<PERSON><PERSON><PERSON><PERSON>", "dAppBrowserFavoritesDescription": "Ihre Favoriten werden hier angezeigt", "dAppBrowserBookmarksDescription": "Ihre Lesezeichen werden hier angezeigt", "dAppBrowserRecentsDescription": "Zuletzt verbundene dApps werden hier angezeigt", "dAppBrowserEmptyScreenDescription": "<PERSON>eben Sie eine URL ein oder suchen Sie im Internet", "dAppBrowserBlocklistScreenTitle": "{{origin}} ist blockiert! ", "dAppBrowserBlocklistScreenDescription": {"part1": "Phantom ist der Meinung, dass diese Website bösartig ist und nicht sicher verwendet werden kann.", "part2": "Diese Seite wurde als Teil einer von der Gemeinschaft gepflegten Datenbank bekannter Phishing-Webseiten und Betrugsversuche gekennzeichnet. <PERSON><PERSON> glauben, dass die Seite fälschlicherweise gekennzeichnet wurde, melden bitte Si<PERSON> einen <PERSON>hler."}, "dAppBrowserLoadFailedScreenTitle": "Laden fehlgeschlagen", "dAppBrowserLoadFailedScreenDescription": "<PERSON><PERSON> dieser Seite ist ein Fehler aufgetreten", "dAppBrowserBlocklistScreenIgnoreButton": "<PERSON>nu<PERSON> ignorieren, trotzdem anzeigen", "dAppBrowserActionBookmark": "Lesezeichen", "dAppBrowserActionRemoveBookmark": "Lesezeichen entfernen", "dAppBrowserActionRefresh": "Aktualisieren", "dAppBrowserActionShare": "Teilen", "dAppBrowserActionCloseTab": "Registerkarte schließen", "dAppBrowserActionEndAutoConfirm": "Auto-Bestätigung beenden", "dAppBrowserActionDisconnectApp": "Getrennte App", "dAppBrowserActionCloseAllTabs": "Alle Registerkarten schließen", "dAppBrowserNavigationAddressPlaceholder": "<PERSON><PERSON>en Sie eine URL für die Suche ein", "dAppBrowserTabOverviewMore": "<PERSON><PERSON>", "dAppBrowserTabOverviewAddTab": "<PERSON><PERSON><PERSON> hinzufügen", "dAppBrowserTabOverviewClose": "Schließen", "dAppBrowserCloseTab": "Registerkarte schließen", "dAppBrowserClose": "Schließen", "dAppBrowserTabOverviewAddBookmark": "Lesezeichen hinzufügen", "dAppBrowserTabOverviewRemoveBookmark": "Lesezeichen entfernen", "depositAssetListSuggestions": "Vorschläge", "depositUndefinedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dieser Token kann nicht eingezahlt werden", "onboardingImportRecoveryPhraseDetails": "Details", "onboardingCreateRecoveryPhraseVerifyTitle": "Haben Sie die geheime Recovery-Phrase aufgeschrieben?", "onboardingCreateRecoveryPhraseVerifySubtitle": "Ohne die geheime Recovery-Phrase können Sie weder auf Ihren Schlüssel noch auf die damit verbundenen Vermögenswerte zugreifen.", "onboardingCreateRecoveryPhraseVerifyYes": "<PERSON>a", "onboardingCreateRecoveryPhraseErrorTitle": "<PERSON><PERSON>", "onboardingCreateRecoveryPhraseErrorSubtitle": "<PERSON>s ist uns nicht gelungen, ein Konto zu erstellen. Bitte versuchen Si<PERSON> es erneut.", "onboardingDoneDescription": "Jetzt können Sie Ihr Wallet in vollen Zügen genießen.", "onboardingDoneGetStarted": "Loslegen", "zeroBalanceHeading": "Legen wir los!", "zeroBalanceBuyCryptoTitle": "Kryptowährung kaufen", "zeroBalanceBuyCryptoDescription": "<PERSON>ufen Sie Ihre erste Kryptowährung mit einer Debit- oder Kreditkarte.", "zeroBalanceDepositTitle": "Kryptowährung übertragen", "zeroBalanceDepositDescription": "Zahlen Sie Kryptowährung von einem anderen Wallet oder einer Börse ein.", "onboardingImportAccountsEmptyResult": "<PERSON><PERSON>n gefunden", "onboardingImportAccountsAccountName": "Konto {{walletIndex}}", "onboardingImportAccountsSocialAccountName": "<PERSON><PERSON><PERSON>", "onboardingImportAccountsSMSAccountName": "Saga {{walletIndex}}", "onboardingImportAccountsLedgerAccountName": "Ledger {{walletIndex}}", "onboardingImportAccountsFoundAccounts_one": "Wir haben {{numberOfWallets}} Konto mit Aktivitäten gefunden", "onboardingImportAccountsFoundAccounts_other": "Wir haben {{numberOfWallets}} Konten mit Aktivität gefunden", "onboardingImportAccountsFoundAccountsNoActivity_one": "Wir fanden {{numberOfWallets}} Konto", "onboardingImportAccountsFoundAccountsNoActivity_other": "Wir fanden {{numberOfWallets}} Konten", "onboardingImportRecoveryPhraseLessThanTwelve": "Die Phrase muss mindestens 12 Wörter umfassen.", "onboardingImportRecoveryPhraseExactlyTwelveOrTwentyFour": "Die Phrase muss genau 12 oder 24 Wörter umfassen.", "onboardingImportRecoveryPhraseWrongWord": "Falsche Wörter: {{ words }}.", "onboardingProtectTitle": "Schützen Sie Ihr Wallet", "onboardingProtectDescription": "Mit der biometrischen Sicherheitsfunktion können Sie sicherstellen, dass nur Sie selbst Zugriff auf Ihr Wallet haben.", "onboardingProtectButtonHeadlineDevice": "G<PERSON><PERSON>", "onboardingProtectButtonHeadlineFaceID": "Face ID", "onboardingProtectButtonHeadlineFingerprint": "Fingerabdruck", "onboardingProtectButtonHeadlinePIN": "PIN", "onboardingProtectButtonSubheadline": "{{ authType }} Authentifizierung benutzen", "onboardingProtectError": "Bei der Authentifizierung ist ein Fehler aufgetreten, bitte versuchen Si<PERSON> es erneut", "onboardingProtectBiometryIosError": "Die biometrische Authentifizierung ist in Phantom konfiguriert, aber in den Systemeinstellungen deaktiviert. Bitte öffnen Sie Einstellungen > Phantom > Face ID oder Touch ID, um sie wieder zu aktivieren.", "onboardingProtectRemoveAuth": "Authentifizierung deaktivieren", "onboardingProtectRemoveAuthDescription": "Sind <PERSON> sicher, dass Sie die Authentifizierung deaktivieren möchten?", "onboardingWelcomeTitle": "Will<PERSON>mmen bei Phantom", "onboardingWelcomeDescription": "Um loszulegen, erstellen Si<PERSON> ein neues Wallet oder importieren Sie ein bestehendes.", "onboardingWelcomeCreateWallet": "Neues Wallet erstellen", "onboardingWelcomeAlreadyHaveWallet": "Ich habe bereits ein <PERSON>et", "onboardingWelcomeConnectSeedVault": "Seed <PERSON> verbinden", "onboardingSlide1Title": "<PERSON> festgelegt", "onboardingSlide1Description": "Ihr Wallet ist mit biometrischem Zugang, Betrugserkennung und 24/7-Support abgesichert.", "onboardingSlide2Title": "Das beste Zuhause für\nIhre NFTs", "onboardingSlide2Description": "Verwalten Sie Einträge, verbrennen Sie Spam, und bleiben Si<PERSON> mit hilfreichen Push-Benachrichtigungen auf dem Laufenden.", "onboardingSlide3Title": "<PERSON><PERSON> mehr aus Ihren Token", "onboardingSlide3Description": "<PERSON><PERSON><PERSON><PERSON>, taus<PERSON>, investieren, senden und empfangen Sie – ohne Ihr Wallet je zu verlassen. ", "onboardingSlide4Title": "Entdecken Sie das Beste vom Web3", "onboardingSlide4Description": "Finden Sie führende Apps und Sammlungen mit dem In-App-Browser und stellen Sie eine Verbindung zu ihnen her.", "onboardingMultichainSlide5Title": "Ein Wallet für alles", "onboardingMultichainSlide5Description": "Erleben Sie Solana, Ethereum und Polygon in einer einzigen benutzerfreundlichen Oberfläche.", "onboardingMultichainSlide5DescriptionWithBitcoin": "Erleben Sie Solana, Ethereum, Polygon und Bitcoin in einer einzigen benutzerfreundlichen Oberfläche.", "requireAuth": "Authentifizierung verlangen", "requireAuthImmediately": "Unmittelbar", "availableToSend": "Zum Versenden verfügbar", "sendEnterAmount": "<PERSON><PERSON> e<PERSON>ben", "sendEditMemo": "Memo bearbeiten", "sendShowLogs": "Fehlerprotokolle anzeigen", "sendHideLogs": "Fehlerprotokolle ausblenden", "sendGoBack": "Zurück", "sendTransactionSuccess": "Ihre Token wurden erfolgreich gesendet an", "sendInputPlaceholder": "@benutzername oder Adresse", "sendInputPlaceholderV2": "Benutzername oder Adresse", "sendPeopleTitle": "Le<PERSON>", "sendDomainTitle": "Domains", "sendFollowing": "Folgen", "sendRecentlyUsedAddressLabel": "Vor {{formattedTimestamp}} benutzt", "sendRecipientAddress": "Adresse des Empfängers", "sendTokenInterpolated": "{{tokenSymbol}} senden", "sendPasteFromClipboard": "Aus Zwischenablage einfügen", "sendScanQR": "QR-Code scannen", "sendTo": "An:", "sendRecipientZeroBalanceWarning": "<PERSON><PERSON> Wall<PERSON>-Adresse hat kein G<PERSON>aben und erscheint nicht in Ihrem aktuellen Transaktionsverlauf. <PERSON>te stellen <PERSON> sicher, dass die Adresse korrekt ist.", "sendUnknownAddressWarning": "Dies ist keine Adresse, mit der Sie in letzter Zeit interagiert hatten. Bitte gehen Sie mit Vorsicht vor.", "sendSameAddressWarning": "Dies ist Ihre aktuelle Adresse. Beim Versenden fallen Transfergebühren an, ohne dass sich das Guthaben ändert.", "sendMintAccountWarning": "Dies ist eine Mint-Konto-Adresse. Sie können kein <PERSON>aben an diese Adresse senden, da dies zu dauerhaftem Verlust führen würde.", "sendCameraAccess": "Kamerazugriff", "sendCameraAccessSubtitle": "Um einen QR-Code zu scannen, muss der Kamerazugriff aktiviert sein. Möchten Sie die Einstellungen jetzt öffnen?", "sendSettings": "Einstellungen", "sendOK": "OK", "invalidQRCode": "Dieser QR-Code ist nicht gültig.", "sendInvalidQRCode": "Dieser QR-Code ist keine gültige Adresse", "sendInvalidQRCodeSubtitle": "Versuchen Sie es erneut oder mit einem anderen QR-Code.", "sendInvalidQRCodeSplToken": "Ungültiger Token im QR-Code", "sendInvalidQRCodeSplTokenSubtitle": "Dieser QR-Code enthält einen Token, den Si<PERSON> nicht besitzen oder den wir nicht identifizieren können.", "sendScanAddressToSend": "<PERSON><PERSON><PERSON>e die {{tokenSymbol}} <PERSON><PERSON><PERSON>, um Geld zu senden", "sendScanAddressToSendNoSymbol": "Scannen Sie die Adresse für den Versand von Guthaben", "sendScanAddressToSendCollectible": "Scannen Sie die SOL-Adresse, um das Sammelobjekt zu versenden", "sendScanAddressToSendCollectibleMultichain": "Scannen Sie die Adresse, um das Sammelobjekt zu versenden", "sendSummary": "Zusammenfassung", "sendUndefinedToken": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dieser Token kann nicht gesendet werden", "sendNoTokens": "<PERSON><PERSON> verfügbar", "noBuyOptionsAvailableInCountry": "<PERSON><PERSON>ufop<PERSON>en in Ihrem Land verfügbar", "swapAvailableTokenDisclaimer": "Eine begrenzte Anzahl von Token ist für die Überbrückung zwischen den Netzen verfügbar", "swapCrossSwapNetworkTooltipTitle": "Netzwerkübergreifender Austausch", "swapCrossSwapNetworkTooltipDescription": "<PERSON><PERSON> zwischen den Netzwerken wird empfohlen, die verfügbaren Token für den niedrigsten Preis und die schnellsten Transaktionen zu verwenden.", "settingsAbout": "Über Phantom", "settingsShareAppWithFriends": "Laden Sie Ihre Freunde ein", "settingsConfirm": "<PERSON>a", "settingsMakeSureNoOneIsWatching": "<PERSON><PERSON><PERSON>, dass niemand Ihren Bildschirm beobachtet", "settingsManageAccounts": "<PERSON><PERSON><PERSON> verwalten", "settingsPrompt": "Sind Si<PERSON> sicher, dass Sie fortfahren wollen?", "settingsSelectAvatar": "Avatar wählen", "settingsSelectSecretPhrase": "Geheime Phrase auswählen", "settingsShowPrivateKey": "<PERSON><PERSON><PERSON> hi<PERSON>, um Ihren privaten Schlüssel zu offenbaren", "settingsShowRecoveryPhrase": "<PERSON><PERSON><PERSON>, um Ihre geheime Phrase zu offenbaren", "settingsSubmitBetaFeedback": "Beta-Feedback e<PERSON><PERSON><PERSON>n", "settingsUpdateAccountNameToast": "<PERSON>ntoname aktualisiert", "settingsUpdateAvatarToast": "Avatar aktualisiert", "settingsUpdateAvatarToastFailure": "Avatar konnte nicht aktualisiert werden!", "settingsWalletAddress": "Konto<PERSON><PERSON><PERSON><PERSON>", "settingsWalletAddresses": "Konto<PERSON><PERSON><PERSON><PERSON>", "settingsWalletNamePrimary": "<PERSON><PERSON><PERSON>", "settingsPlaceholderName": "Name", "settingsWalletNameSecondary": "Den Namen Ihres Wallets ändern", "settingsYourAccounts": "<PERSON><PERSON><PERSON>", "settingsYourAccountsMultiChain": "Multi-Chain", "settingsReportUser": "<PERSON><PERSON><PERSON> melden", "settingsNotifications": "Benachrichtigungen", "settingsNotificationPreferences": "Einstellungen für Benachrichtigungen", "pushNotificationsPreferencesAllowNotifications": "Benachrichtigungen erlauben", "pushNotificationsPreferencesSentTokens": "Gesendete Token", "pushNotificationsPreferencesSentTokensDescription": "Ausgehende Transfers von Token und NFTs", "pushNotificationsPreferencesReceivedTokens": "<PERSON><PERSON><PERSON><PERSON>", "pushNotificationsPreferencesReceivedTokensDescription": "Eingehende Transfers von Token und NFTs", "pushNotificationsPreferencesDexSwap": "Tauschvorgänge", "pushNotificationsPreferencesDexSwapDescription": "Tauschvorgänge bei erkannten Anwendungen", "pushNotificationsPreferencesOtherBalanceChanges": "Andere Guthabenänderungen", "pushNotificationsPreferencesOtherBalanceChangesDescription": "Andere Transaktionen mit mehreren Token, die sich auf Ihr Guthaben auswirken", "pushNotificationsPreferencesPhantomMarketing": "Aktualisierungen von Phantom", "pushNotificationsPreferencesPhantomMarketingDescription": "Funktionsankündigungen und allgemeine Aktualisierungen", "pushNotificationsPreferencesDescription": "Diese Einstellungen steuern die Push-Benachrichtigungen für dieses aktive Wallet. Jedes Wallet hat seine eigenen Benachrichtigungseinstellungen. Um alle Phantom-Push-Benachrichtigungen zu deaktivieren, gehen <PERSON><PERSON> zu Ihren <1>Geräteeinstellungen</1>.", "pushNotificationsPreferencesUnableToSyncNotificationPreferences": "Die Benachrichtigungseinstellungen können nicht synchronisiert werden.", "connectSeedVaultConnectSeed": "Einen Seed verbinden", "connectSeedVaultConnectSeedDescription": "Verbinden Sie Phantom mit dem Seed Vault auf Ihrem Telefon", "connectSeedVaultSelectAnAccount": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Konto", "connectSeedVaultSelectASeed": "Einen Seed wählen", "connectSeedVaultSelectASeedDescription": "<PERSON><PERSON><PERSON><PERSON> Sie den Seed, den <PERSON> mit Phantom verbinden möchten", "connectSeedVaultSelectAnAccountDescription": "<PERSON><PERSON><PERSON><PERSON> Sie das Konto, das Si<PERSON> bei Phantom einrichten möchten", "connectSeedVaultNoAccountsFound": "<PERSON><PERSON>n gefunden.", "connectSeedVaultSelectAccounts": "Konten wählen", "connectSeedVaultSelectAccountsDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON>, welche Konten Sie bei Phantom einrichten möchten", "connectSeedVaultCompleteSetup": "Einrichtung abschließen", "connectSeedVaultCompleteSetupDescription": "Alles ist bereit! Erkunden Sie web3 mit Phantom und verwenden Sie Ihren Seed Vault, um Transaktionen zu bestätigen", "connectSeedVaultConnectAnotherSeed": "Einen weiteren Seed verbinden", "connectSeedVaultConnectAllSeedsConnected": "Alle Seeds verbunden", "connectSeedVaultNoSeedsConnected": "<PERSON>ine Seeds verbunden. Tip<PERSON> Si<PERSON> auf die Schaltfläche unten, um den Seed aus dem Seed Vault zu autorisieren.", "connectSeedVaultConnectAccount": "Konto verbinden", "connectSeedVaultLoadMore": "<PERSON><PERSON> <PERSON>", "connectSeedVaultNeedPermission": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "connectSeedVaultNeedPermissionDescription": "<PERSON>ehen Sie zu Einstellungen, um Phantom die Verwendung von Seed-Vault-Genehmigungen zu erlauben.", "stakeApy": "{{apyPercentage}} APY", "stakeFee": "{{fee}} <PERSON><PERSON><PERSON><PERSON>", "stakeAmount": "Betrag", "stakeAmountBalance": "<PERSON><PERSON><PERSON><PERSON>", "swapTopQuotes": "Top {{numQuotes}} Angebote", "swapTopQuotesTitle": "Top-Ang<PERSON><PERSON>", "swapProvidersTitle": "<PERSON><PERSON><PERSON>", "swapProvidersFee": "{{fee}} <PERSON><PERSON><PERSON><PERSON>", "swapProvidersTagRecommended": "<PERSON><PERSON>", "swapProvidersTagFastest": "<PERSON><PERSON><PERSON><PERSON>", "swapProviderEstimatedTimeHM": "{{hours}} h {{minutes}} m", "swapProviderEstimatedTimeM": "{{minutes}} m", "swapProviderEstimatedTimeS": "{{seconds}} s", "stakeReview": "Überprüfen", "stakeReviewAccount": "Ko<PERSON>", "stakeReviewCommissionFee": "Kommissionsgebühr", "stakeReviewConfirm": "Bestätigen", "stakeReviewValidator": "<PERSON><PERSON><PERSON><PERSON>", "stakeReviewAPY": "APY", "convertStakeStatusErrorTitle": "Einsatzumwandlung fehlgeschlagen", "convertStakeStatusErrorMessage": "<PERSON>hr Einsatz konnte nicht in {{poolTokenSymbol}} umgewandelt werden. Bitte versuchen Sie es erneut.", "convertStakeStatusLoadingTitle": "Umwan<PERSON><PERSON> zu {{poolTokenSymbol}}", "convertStakeStatusLoadingMessage": "Wir beginnen jetzt mit der Umwandlung Ihres Einsatzes von {{stakedTokenSymbol}} zu {{poolTokenSymbol}}.", "convertStakeStatusSuccessTitle": "Umwandlung zu {{poolTokenSymbol}} abgeschlossen!", "convertStakeStatusSuccessMessage": "<PERSON><PERSON><PERSON> Si<PERSON> <1>hier</1> zusätzliche Belohnungen mit Ihren JitoSOL.", "convertStakeStatusConvertMore": "<PERSON><PERSON>", "convertStakePendingTitle": "Einsatz zu {{symbol}} umwandeln", "convertToJitoSOL": "In JitoSOL umwandeln", "convertToJitoSOLInfoDescription": "Wandeln Sie Ihr SOL in Jito SOL um, um Belohnungen zu verdienen und am Jito-Ökosystem teilzunehmen.", "convertToJitoSOLInfoTitle": "In JitoSOL umwandeln", "convertStakeBannerTitle": "Wandeln Sie Ihren Einsatz in JitoSOL um und erhöhen Sie Ihre Belohnungen um bis zu 15 %", "convertStakeQuestBannerTitle": "Wandeln Sie eingesetzte SOL in JitoSOL um und verdienen Sie Belohnungen. Erfahren Sie mehr.", "liquidStakeConvertInfoTitle": "In JitoSOL umwandeln", "liquidStakeConvertInfoDescription": "Steigern Sie Ihre Belohnungen, indem Sie Ihren SOL-Einsatz in JitoSOL umwandeln. <1>Mehr erfahren</1>", "liquidStakeConvertInfoFeature1Title": "Warum mit Jito e<PERSON>zen?", "liquidStakeConvertInfoFeature1Description": "Zahlen Si<PERSON> ein, um JitoSOL zu erhalten, das mit Ihrem Einsatz wächst. Verwenden Sie es in DeFi-Protokollen für zusätzliche Gewinne. Tauschen Sie Ihr JitoSOL später gegen Ihren ursprünglichen Betrag + angesammelte Belohnungen ein", "liquidStakeConvertInfoFeature2Title": "Höhere durchschnittliche Belohnungen", "liquidStakeConvertInfoFeature2Description": "Jito verteilt Ihr SOL auf die besten Validierer mit den niedrigsten Gebühren. MEV-Belohnungen steigern Ihren Verdienst zusätzlich.", "liquidStakeConvertInfoFeature3Title": "Unterstützen Sie das Solana-Netzwerk", "liquidStakeConvertInfoFeature3Description": "Liquid Staking sichert Solana ab, indem es die Einsätze auf mehrere Validierer verteilt und so das Risiko von Validierern mit geringer Betriebszeit reduziert.", "liquidStakeConvertInfoSecondaryButton": "<PERSON><PERSON>t nicht", "liquidStakeStartStaking": "Starten Sie die Einsätze", "liquidStakeReviewOrder": "Bestellung überprüfen", "convertStakeAccountListPageIneligibleSectionTitle": "Unzulässige Einsatzkonten", "convertStakeAccountIneligibleBottomSheetTitle": "Unzulässige Einsatzkonten", "convertStakeAccountListPageErrorTitle": "Einsatzkonten konnten nicht abgerufen werden", "convertStakeAccountListPageErrorDescription": "Leider ist etwas schiefgegangen und wir konnten die Einsatzkonten nicht abrufen", "liquidStakeReviewYouPay": "<PERSON><PERSON> zahlen", "liquidStakeReviewYouReceive": "<PERSON>e erhalten", "liquidStakeReviewProvider": "<PERSON><PERSON><PERSON>", "liquidStakeReviewNetworkFee": "Netzwerkgebühr", "liquidStakeReviewPageTitle": "Bestätigung", "liquidStakeReviewConversionFootnote": "<PERSON><PERSON> <PERSON>-Token im Tausch gegen JitoSOL einsetzen, erhalten Sie eine etwas geringere Menge an JitoSOL. <1><PERSON>hr erfahren</1>", "convertStakeAccountIneligibleBottomSheetDescription": "Der Einsatzpool von Jito unterstützt die meisten aktiven Solana-Validierer. <PERSON>e werden nicht in der Lage sein, eingesetzte SOL von nicht unterstützten Validatoren in JitoSOL umzuwandeln. Außerdem dauert es ca. 2 Tage, bis neu eingesetzte SOL für die Umwandlung in JitoSOL berechtigt sind.", "selectAValidator": "Wählen Sie einen Validierer", "validatorSelectionListTitle": "Wählen Sie einen Validierer", "validatorSelectionListDescription": "Wählen Sie einen Validierer für Ihren SOL-Einsatz.", "stakeMethodDescription": "Verdienen Sie Zinsen, indem Sie Ihre SOL-Token verwenden, um Solana bei der Skalierung zu helfen. <1><PERSON>hr erfahren</1>", "stakeMethodRecommended": "<PERSON><PERSON><PERSON><PERSON>", "stakeMethodEstApy": "Gesch. APY: ~{{apy}}", "stakeMethodSelectionLiquidStakingTitle": "Liquide Einsätze", "stakeMethodSelectionLiquidStakingDescription": "Setzen Sie SOL ein, um höhere Belohnungen zu erhalten, he<PERSON><PERSON>, <PERSON><PERSON> zu sichern und erhalten Sie JitoSOL, um zusätzliche Belohnungen zu erhalten.", "stakeMethodSelectionNativeStakingTitle": "Native Einsätze", "stakeMethodSelectionNativeStakingDescription": "<PERSON>zen Sie SOL ein, um Belohnungen zu erhalten, wä<PERSON><PERSON> <PERSON>, <PERSON><PERSON> zu sichern.", "liquidStakeMintStakeSOL": "SOL einsetzen", "mintJitoSOLInfoPageTitle": "Liquide Einsätze mit Jito", "mintJitoSOLFeature1Title": "Warum mit Jito e<PERSON>zen?", "mintJitoSOLFeature1Description": "Zahlen Si<PERSON> ein, um JitoSOL zu erhalten, das mit Ihrem Einsatz wächst. Verwenden Sie es in DeFi-Protokollen für zusätzliche Gewinne. Tauschen Sie Ihr JitoSOL später gegen Ihren ursprünglichen Betrag + angesammelte Belohnungen ein", "mintJitoSOLFeature2Title": "Höhere durchschnittliche Belohnungen", "mintJitoSOLFeature2Description": "Jito verteilt Ihr SOL auf die besten Validierer mit den niedrigsten Gebühren. MEV-Belohnungen steigern Ihren Verdienst zusätzlich.", "mintJitoSOLFeature3Title": "Unterstützen Sie das Solana-Netzwerk", "mintJitoSOLFeature3Description": "Liquid Staking sichert Solana ab, indem es die Einsätze auf mehrere Validierer verteilt und so das Risiko von Validierern mit geringer Betriebszeit reduziert.", "mintLiquidStakePendingTitle": "Minting von <PERSON>", "mintStakeStatusErrorTitle": "Minting von liquidem Einsatz fehlgeschlagen", "mintStakeStatusErrorMessage": "Ihr liquider Einsatz von {{poolTokenSymbol}} konnte nicht gemintet werden. Bitte versuchen Si<PERSON> es erneut.", "mintStakeStatusSuccessTitle": "Minting von liquidem Einsatz von {{poolTokenSymbol}} abgeschlossen!", "mintStakeStatusLoadingTitle": "Minting von liquidem Einsatz von {{poolTokenSymbol}}", "mintStakeStatusLoadingMessage": "Wir beginnen jetzt mit dem Minting Ihres liquiden Einsatzes von {{poolTokenSymbol}}.", "mintLiquidStakeAmountDescription": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON>, wie viel SOL Sie bei Jito einsetzen möchten", "mintLiquidStakeAmountProvider": "<PERSON><PERSON><PERSON>", "mintLiquidStakeAmountApy": "Gesch. APY", "mintLiquidStakeAmountBestPrice": "Pre<PERSON>", "mintLiquidStakeAmountInsufficientBalance": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "mintLiquidStakeAmountMinRequired": "{{amount}} {{symbol}} erforderlich für den Einsatz", "swapTooltipGotIt": "Verstanden", "swapTabInsufficientFunds": "<PERSON><PERSON><PERSON><PERSON> reicht nicht aus", "swapNoAssetsFound": "<PERSON><PERSON>", "swapNoTokensFound": "<PERSON><PERSON> gefunden", "swapConfirmationTryAgain": "<PERSON><PERSON><PERSON> versuchen", "swapConfirmationGoBack": "Zurück", "swapNoQuotesFound": "<PERSON><PERSON> gefunden", "swapNotProviderFound": "Wir konnten keinen Anbieter für diesen Token-Tausch finden. Versuchen Sie einen anderen Token.", "swapAvailableOnMainnet": "Diese Funktion ist nur im Mainnet verfügbar", "swapNotAvailableEVM": "Tauschs sind für EVM-Konten noch nicht verfügbar", "swapNotAvailableOnSelectedNetwork": "Tau<PERSON><PERSON> sind in dem gewählten Netz nicht verfügbar", "singleChainSwapTab": "Im <PERSON>z<PERSON>", "crossChainSwapTab": "Netzwerkübergreifend", "allFilter": "Alle", "bridgeRefuelTitle": "Refuel", "bridgeRefuelDescription": "Refuel stellt sicher, dass Sie nach der Überbrückung für Transaktionen bezahlen können.", "bridgeRefuelLabelBalance": "Ihre {{symbol}}", "bridgeRefuelLabelReceive": "<PERSON>e erhalten", "bridgeRefuelLabelFee": "Geschätzte Kosten", "bridgeRefuelDismiss": "Ohne Refuel fortsetzen", "bridgeRefuelEnable": "Refuel aktivieren", "unwrapWrappedSolError": "Auspacken fehlgeschlagen", "unwrapWrappedSolLoading": "Packe aus ...", "unwrapWrappedSolSuccess": "Ausgepackt", "unwrapWrappedSolViewTransaction": "Transaktion ansehen", "dappApprovePopupSignMessage": "Nachricht signieren", "solanaPayFrom": "<PERSON>", "solanaPayMessage": "Nachricht", "solanaPayNetworkFee": "Netzwerkgebühr", "solanaPayFree": "<PERSON><PERSON><PERSON>", "solanaPayPay": "{{item}} bezahlen", "solanaPayPayNow": "Jetzt bezahlen", "solanaPaySending": "Sende {{item}}", "solanaPayReceiving": "<PERSON><PERSON><PERSON><PERSON> {{item}}", "solanaPayMinting": "Minting von {{item}}", "solanaPayTransactionProcessing": "Ihre Transaktion wird bearbeitet,\nbitte warten <PERSON>.", "solanaPaySent": "Gesendet!", "solanaPayReceived": "<PERSON><PERSON><PERSON><PERSON>!", "solanaPayMinted": "Gemintet!", "solanaPaySentNFT": "NFT gesendet!", "solanaPayReceivedNFT": "NFT erhalten!", "solanaPayTokensSent": "Ihre Token wurden erfolgreich an {{to}} gesendet", "solanaPayTokensReceived": "Sie haben neue Token von {{from}} erhalten", "solanaPayViewTransaction": "Transaktion ansehen", "solanaPayTransactionFailed": "Transaktion fehlgeschlagen", "solanaPayConfirm": "Bestätigen", "solanaPayTo": "an", "dappApproveConnectViewAccount": "<PERSON>hr <PERSON>ana-<PERSON><PERSON> anzeigen", "deepLinkInvalidLink": "Ungültiger Link", "deepLinkInvalidSplTokenSubtitle": "Dies enthält einen To<PERSON>, den <PERSON> nicht besitzen oder den wir nicht identifizieren können.", "walletAvatarShowAllAccounts": "Alle Konten anzeigen", "pushNotificationsGetInstantUpdates": "Sofortige Updates erhalten", "pushNotificationsEnablePushNotifications": "Aktivier<PERSON> von Push-Benachrichtigungen über abgeschlossene Überweisungen, Tauschvorgänge und Ankündigungen", "pushNotificationsEnable": "Einschalten", "pushNotificationsNotNow": "<PERSON><PERSON>t nicht", "onboardingAgreeToTermsOfServiceInterpolated": "Ich akzeptiere die <1>Nutzungsbedingungen</1>", "onboardingConfirmSaveSecretRecoveryPhrase": "OK, ich habe es gespeichert", "onboardingCreateNewWallet": "Neues Wallet erstellen", "onboardingErrorDuplicateSecretRecoveryPhrase": "Diese geheime Phrase existiert in Ihrer Wallet bereits", "onboardingErrorInvalidSecretRecoveryPhrase": "Ungültige geheime Recovery-Phrase", "onboardingFinished": "Sie sind fertig!", "onboardingImportAccounts": "Konten importieren", "onboardingImportImportingAccounts": "Importiere Konten …", "onboardingImportImportingFindingAccounts": "Suche nach Konten mit Aktivität", "onboardingImportAccountsLastActive": "Vor {{formattedTimestamp}} aktiv", "onboardingImportAccountsNeverUsed": "<PERSON><PERSON>", "onboardingImportAccountsCreateNew": "<PERSON><PERSON><PERSON>", "onboardingImportAccountsDescription": "Wählen Sie Wallet-Konten zum Importieren aus", "onboardingImportReadOnlyAccountDescription": "Fügen Sie eine Adresse oder einen Domainnamen zum Beobachten hinzu. Sie haben nur Nur-Ansicht-Zugriff und können keine Transaktionen oder Nachrichten signieren.", "onboardingImportSecretRecoveryPhrase": "Geheime Phrase importieren", "onboardingImportViewAccounts": "Konten anzeigen", "onboardingRestoreExistingWallet": "<PERSON><PERSON><PERSON> ein bestehendes Wallet mit Ihrer geheimen 12- oder 24-Wort-Recovery-<PERSON><PERSON> wieder her", "onboardingShowUnusedAccounts": "Ungenutzte Konten anzeigen", "onboardingShowMoreAccounts": "Mehr Konten anzeigen", "onboardingHideUnusedAccounts": "Ungenutzte Konten ausblenden", "onboardingSecretRecoveryPhrase": "Geheime Recovery-Phrase", "onboardingSelectAccounts": "Wählen Sie Ihre Konten", "onboardingStoreSecretRecoveryPhraseReminder": "Dies ist die einzige Möglichkeit, Ihr Konto wiederherzustellen. Bitte bewahren Si<PERSON> es an einem sicheren Ort auf!", "useTokenMetasForMintsUnknownName": "Unbekannt", "timeUnitMinute": "Minute", "timeUnitMinutes": "Minutes", "timeUnitHour": "Stunde", "timeUnitHours": "Stunden", "espNFTListWithPrice": "Sie haben {{NFTDisplayName}} für {{priceAmount}} {{priceTokenSymbol}} auf {{dAppName}} gelistet", "espNFTListWithPriceWithoutDApp": "Sie haben {{NFTDisplayName}} für {{priceAmount}} {{priceTokenSymbol}} geslistet", "espNFTListWithoutPrice": "Sie haben {{NFTDisplayName}} zum Verkauf auf {{dAppName}} gelistet", "espNFTListWithoutPriceWithoutDApp": "Sie haben {{NFTDisplayName}} zum Verkauf gelistet", "espNFTChangeListPriceWithPrice": "Sie haben den Eintrag für {{NFTDisplayName}} auf {{priceAmount}} {{priceTokenSymbol}} auf {{dAppName}} aktualisiert", "espNFTChangeListPriceWithPriceWithoutDApp": "Sie haben den Eintrag für {{NFTDisplayName}} auf {{priceAmount}} {{priceTokenSymbol}} aktualisiert", "espNFTChangeListPriceWithoutPrice": "Sie haben den Eintrag für {{NFTDisplayName}} auf {{dAppName}} aktualisiert", "espNFTChangeListPriceWithoutPriceWithoutDApp": "Sie haben den Eintrag für {{NFTDisplayName}} aktualisiert", "espNFTBidBidderWithPrice": "Sie boten {{priceAmount}} {{priceTokenSymbol}} für {{NFTDisplayName}} auf {{dAppName}}", "espNFTBidBidderWithPriceWithoutDApp": "Sie boten {{priceAmount}} {{priceTokenSymbol}} für {{NFTDisplayName}}", "espNFTBidBidderWithoutPrice": "Sie haben ein <PERSON> für {{NFTDisplayName}} auf {{dAppName}} abgegeben", "espNFTBidBidderWithoutPriceWithoutDApp": "Sie haben ein G<PERSON> für {{NFTDisplayName}} abgegeben", "espNFTBidListerWithPrice": "<PERSON><PERSON><PERSON> von {{priceAmount}} {{priceTokenSymbol}} für {{NFTDisplayName}} auf {{dAppName}}", "espNFTBidListerWithPriceWithoutDApp": "<PERSON><PERSON><PERSON> von {{priceAmount}} {{priceTokenSymbol}} für {{NFTDisplayName}}", "espNFTBidListerWithoutPrice": "<PERSON><PERSON><PERSON> {{NFTDisplayName}} auf {{dAppName}}", "espNFTBidListerWithoutPriceWithoutDApp": "<PERSON><PERSON><PERSON> {{NFTDisplayName}}", "espNFTCancelBidWithPrice": "Sie haben Ihr G<PERSON>ot von {{priceAmount}} {{priceTokenSymbol}} für {{NFTDisplayName}} auf {{dAppName}} storniert", "espNFTCancelBidWithPriceWithoutDApp": "Sie haben Ihr Gebot von {{priceAmount}} {{priceTokenSymbol}} für {{NFTDisplayName}} storniert", "espNFTCancelBidWithoutPrice": "Sie haben Ihr G<PERSON>ot von {{NFTDisplayName}} auf {{dAppName}} storniert", "espNFTCancelBidWithoutPriceWithoutDApp": "Sie haben Ihr Gebot von {{NFTDisplayName}} stor<PERSON>t", "espNFTUnlist": "Sie haben {{NFTDisplayName}} auf {{dAppName}} entfernt", "espNFTUnlistWithoutDApp": "Sie haben {{NFTDisplayName}} entfernt", "espNFTBuyBuyerWithPrice": "Sie haben {{NFTDisplayName}} für {{priceAmount}} {{priceTokenSymbol}} auf {{dAppName}} gekauft", "espNFTBuyBuyerWithPriceWithoutDApp": "Sie haben {{NFTDisplayName}} für {{priceAmount}} {{priceTokenSymbol}} gekauft", "espNFTBuyBuyerWithoutPrice": "Sie haben {{NFTDisplayName}} auf {{dAppName}} gekauft", "espNFTBuyBuyerWithoutPriceWithoutDApp": "Sie haben {{NFTDisplayName}} gekauft", "espNFTBuySellerWithPrice": "Sie haben {{NFTDisplayName}} für {{priceAmount}} {{priceTokenSymbol}} auf {{dAppName}} verkauft", "espNFTBuySellerWithPriceWithoutDApp": "Sie haben {{NFTDisplayName}} für {{priceAmount}} {{priceTokenSymbol}} verkauft", "espNFTBuySellerWithoutPrice": "Sie haben {{NFTDisplayName}} auf {{dAppName}} verkauft", "espNFTBuySellerWithoutPriceWithoutDApp": "Sie haben {{NFTDisplayName}} verkauft", "espDEXSwap": "Sie haben {{downTokensTextFragment}} für {{upTokensTextFragment}} auf {{dAppName}} getauscht", "espDEXDepositLPWithPoolDisplay": "Sie haben {{downTokensTextFragment}} in den {{poolDisplayName}} Liquiditätspool auf {{dAppName}} eingezahlt", "espDEXDepositLPWithoutPoolDisplay": "Sie haben {{downTokensTextFragment}} für {{upTokensTextFragment}} auf {{dAppName}} getauscht", "espDEXWithdrawLPWithPoolDisplay": "Sie haben {{upTokensTextFragment}} von dem {{poolDisplayName}} Liquiditätspool auf {{dAppName}} abgehoben", "espDEXWithdrawLPWithoutPoolDisplay": "Sie haben {{downTokensTextFragment}} für {{upTokensTextFragment}} auf {{dAppName}} getauscht", "espGenericTokenSend": "Sie haben {{downTokensTextFragment}} gesendet", "espGenericTokenReceive": "Sie haben {{upTokensTextFragment}} erhalten", "espGenericTransactionBalanceChange": "Sie haben {{downTokensTextFragment}} für {{upTokensTextFragment}} getauscht", "espUnknown": "UNBEKANNT", "espUnknownNFT": "Unbekanntes NFT", "espTextFragmentAnd": "und", "externalLinkWarningTitle": "Sie werden Phantom verlassen", "externalLinkWarningDescription": "Und {{url}} öffnen. Vergewissern <PERSON> sich, dass Si<PERSON> dieser Quelle vertrauen, bevor <PERSON> mit ihr interagieren.", "shortcutsWarningDescription": "Eine Abkürzung wird durch {{url}} bereitgestellt. Bitte vergewissern <PERSON> sich, dass <PERSON><PERSON> dieser Que<PERSON> vertrauen, bevor <PERSON> mit ihr interagieren.", "lowTpsBanner": "Solana leidet unter einer Überlastung des Netzwerks", "lowTpsMessageTitle": "Überlastung des Solana-Netzwerks", "lowTpsMessage": "Aufgrund der hohen Auslastung von Solana können Ihre Transaktionen fehlschlagen oder sich verzögern. Bitte versuchen Sie fehlgeschlagene Transaktionen erneut.", "solanaSlow": "Solana-Netzwerk ist ungewöhnlich langsam", "solanaNetworkTemporarilyDown": "Das Solana-Netzwerk ist vorübergehend nicht verfügbar", "waitForNetworkRestart": "<PERSON>te warten Si<PERSON>, bis das Netzwerk neu gestartet ist. Ihr Guthaben ist davon nicht betroffen.", "exploreCollectionsCarouselTitle": "Was ist beliebt", "exploreDropsCarouselTitle": "<PERSON> g<PERSON><PERSON>'s Neues", "exploreSortFloor": "Mindest.", "exploreSortListed": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortVolume": "Volumen", "exploreFetchErrorSubtitle": "Bitte versuchen Sie es später erneut.", "exploreFetchErrorTitle": "Abruf fehlgeschlagen.", "exploreTopCollectionsTitle": "Top-NFT-Kollektionen", "exploreTopListLess": "<PERSON><PERSON>", "exploreTopListMore": "<PERSON><PERSON>", "exploreSeeMore": "<PERSON><PERSON> anzeigen", "exploreTrendingTokens": "Beliebte Token", "exploreVolumeTokens": "Höchstes Volumen", "explorePriceChangeTokens": "G<PERSON><PERSON><PERSON><PERSON> Gewinner", "explorePriceTokens": "Token nach Preis", "exploreMarketCapTokens": "Top-Token", "exploreTrendingSites": "Beliebte Seiten", "exploreTopSites": "Top-Seiten", "exploreTrendingCollections": "Beliebte Sammlungen", "exploreTopCollections": "Top-Kollektionen", "collectiblesSearchCollectionsSection": "Sammlungen", "collectiblesSearchItemsSection": "Artikel", "collectiblesSearchNrOfItems": "{{ nrOfItems }} Artikel", "collectiblesSearchPlaceholderText": "Durchsuchen Sie Ihre Sammelobjekte", "collectionPinSuccess": "Sammlung angeheftet", "collectionPinFail": "Konnte Sammlung nicht anheften", "collectionUnpinSuccess": "<PERSON><PERSON><PERSON>", "collectionUnpinFail": "Konnte Sammlung nicht lösen", "collectionHideSuccess": "Sammlung ausgeblendet", "collectionHideFail": "Konnte Sammlung nicht ausblenden", "collectionUnhideSuccess": "Sammlung angezeigt", "collectionUnhideFail": "Konnte Sammlung nicht anzeigen", "collectiblesSpamSuccess": "Als Spam gemeldet", "collectiblesSpamFail": "Meldung als Spam fehlgeschlagen", "collectiblesSpamAndHiddenSuccess": "Als Spam gemeldet und ausgeblendet", "collectiblesNotSpamSuccess": "Als Nicht-Spam gemeldet", "collectiblesNotSpamFail": "Meldung als Nicht-Spam fehlgeschlagen", "collectiblesNotSpamAndUnhiddenSuccess": "Als Nicht-Spam gemeldet und eingeblendet", "tokenPageSpamWarning": "Dieser Token wurde noch nicht verifiziert. Interagieren Sie nur mit Token, denen Si<PERSON> vertrauen.", "tokenSpamWarning": "Dieser Token wurde ausgeblendet, weil <PERSON> glaubt, dass es sich um Spam handelt.", "collectibleSpamWarning": "<PERSON>ses Sammelobjekt wurde ausgeblendet, weil <PERSON> glaubt, dass es sich um Spam handelt.", "collectionSpamWarning": "<PERSON><PERSON> Sam<PERSON>obje<PERSON>e wurden ausgeblendet, weil <PERSON> glaubt, dass es sich um Spam handelt.", "emojiNoResults": "<PERSON><PERSON> gefunden", "emojiSearchResults": "Suchergebnisse", "emojiSuggested": "<PERSON><PERSON><PERSON><PERSON>", "emojiSmileys": "Smileys & Leute", "emojiAnimals": "Tiere & Natur", "emojiFood": "Essen & Trinken", "emojiTravel": "Reisen & Orte", "emojiActivities": "Aktivitäten", "emojiObjects": "Objekte", "emojiSymbols": "Symbole", "emojiFlags": "Flaggen", "whichExtensionToConnectWith": "Mit welcher Erweiterung möchten Sie sich verbinden?", "configureInSettings": "Konfigurierbar unter Einstellungen → Standard-App-Wallet.", "continueWith": "Fortfahren mit", "useMetaMask": "MetaMask verwenden", "usePhantom": "Phantom verwenden", "alwaysAsk": "Immer fragen", "dontAskMeAgain": "Nicht mehr fragen", "selectWalletSettingDescriptionLine1": "Einige Apps bieten möglicherweise keine Option zur Verbindung mit Phantom.", "selectWalletSettingDescriptionLinePhantom": "Als Alternative dazu wird bei einer Verbindung mit MetaMask immer Phantom geöffnet.", "selectWalletSettingDescriptionLineAlwaysAsk": "Als Alternative dazu fragen wir Sie bei der Verbindung mit MetaMask, ob Sie stattdessen Phantom verwenden möchten.", "selectWalletSettingDescriptionLineMetaMask": "<PERSON>n Sie MetaMask als Standard einstellen, werden diese dApps daran geh<PERSON>, sich mit Phantom zu verbinden.", "metaMaskOverride": "Standard-App-Wallet", "metaMaskOverrideSettingDescriptionLine1": "<PERSON>ür die Verbindung zu Websites, die keine Option zur Verwendung von Phantom anbieten.", "refreshAndReconnectToast": "Aktualisieren und verbinden Sie sich erneut, um Ihre Änderungen zu übernehmen", "autoConfirmUnavailable": "Nicht verfügbar", "autoConfirmReasonDappNotWhitelisted": "<PERSON><PERSON>, <PERSON> <PERSON>, von dem sie stammt, nicht auf unserer Zulassungsliste für diese Anwendung steht.", "autoConfirmReasonSessionNotActive": "<PERSON><PERSON>, da keine Sitzung mit Auto-Bestätigung aktiv ist. Bitte aktivieren Si<PERSON> sie unten.", "autoConfirmReasonRateLimited": "<PERSON>cht verf<PERSON><PERSON><PERSON>, weil die von Ihnen verwendete App zu viele Anfragen sendet.", "autoConfirmReasonUnsupportedNetwork": "<PERSON>cht verfüg<PERSON>, weil Auto-Bestätigung dieses Netzwerk noch nicht unterstützt.", "autoConfirmReasonSimulationFailed": "<PERSON><PERSON> ve<PERSON>, da wir die Sicherheit nicht garantieren können.", "autoConfirmReasonTabNotFocused": "<PERSON><PERSON>, da die Registerkarte der Domain, für die Sie die Auto-Bestätigung durchführen möchten, nicht aktiv ist.", "autoConfirmReasonNotUnlocked": "<PERSON><PERSON> ve<PERSON>, da das Wallet nicht entsperrt wurde.", "rpcErrorUnauthorizedWrongAccount": "Die Adresse des Absenders der Transaktion stimmt nicht mit der ausgewählten Kontoadresse überein.", "rpcErrorUnauthorizedUnknownSource": "Die Quelle der RPC-Anfrage konnte nicht ermittelt werden.", "transactionsDisabledTitle": "Transaktionen deaktiviert", "transactionsDisabledMessage": "Ihre Adresse ist nicht in der Lage, Transaktionen mit Phantom durchzuführen", "settingsTrustedAppDetailsAutoConfirmActiveTitle": "Aktiv", "settingsTrustedAppDetailsCopiedToClipboardToast": "URL in die Zwischenablage kopiert", "notEnoughSolScanTransactionWarning": "Diese Transaktion kann fehlschlagen, da nicht genügend SOL auf Ihrem Konto vorhanden ist. Bitte fügen Sie mehr SOL zu Ihrem Konto hinzu und versuchen Sie es erneut.", "transactionRevertedWarning": "Diese Transaktion wurde während der Simulation rückgängig gemacht. Guthaben kann bei der Übermittlung verloren gehen.", "slippageToleranceExceeded": "Diese Transaktion wurde während der Simulation storniert. Verschiebungstoleranz überschritten.", "simulationWarningKnownMalicious": "Wir glauben, dass dieses Konto betrügerisch ist. Die Genehmigung kann zu Guthabenverlust führen.", "simulationWarningPoisonedAddress": "Diese Adresse hat verdächtige Ähnlichkeit mit einer Adresse, an die Sie kürzlich Geld geschickt haben. Bitte vergewissern <PERSON> sich, dass es sich um die richtige Adresse handelt, um zu verhindern, dass Sie Geld durch einen Betrug verlieren.", "simulationWarningInteractingWithAccountWithoutActivity": "Dies ist ein Konto ohne vorherige Aktivität. Die Überweisung von Geldern auf ein nicht existierendes Konto kann zum Verlust von Geldern führen", "quests": "Aufgaben", "questsClaimInProgress": "Anforderung läuft", "questsVerifyingCompletion": "Überprüfung des Abschlusses einer Aufgabe", "questsClaimError": "Fehler beim Anfordern der Belohnung", "questsClaimErrorDescription": "Es ist ein Fehler beim Anfordern Ihrer Belohnung aufgetreten. Bitte versuchen Si<PERSON> es später noch einmal.", "questsBadgeMobileOnly": "Nur mobil", "questsBadgeExtensionOnly": "Nur Erweiterung", "questsExplainerSheetButtonLabel": "Verstanden", "questsNoQuestsAvailable": "<PERSON><PERSON>ben verfügbar", "questsNoQuestsAvailableDescription": "Derzeit sind keine Aufgaben verfügbar. Wir werden Sie benachrichtigen, sobald neue hinzugefügt werden.", "exploreLearn": "<PERSON><PERSON><PERSON>", "exploreSites": "Websites", "exploreTokens": "Token", "exploreQuests": "Aufgaben", "exploreCollections": "Sammlungen", "exploreFilterByall_networks": "Alle Netzwerke", "exploreSortByrank": "Beliebt", "exploreSortBytrending": "Beliebt", "exploreSortByprice": "Pre<PERSON>", "exploreSortByprice-change": "Preisänderung", "exploreSortBytop": "Top", "exploreSortByvolume": "Volumen", "exploreSortBygainers": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBylosers": "<PERSON><PERSON><PERSON><PERSON>", "exploreSortBymarket-cap": "Marktkapitalisierung", "exploreSortBymarket_cap": "Marktkapitalisierung", "exploreTimeFrame1h": "1 h", "exploreTimeFrame24h": "24 h", "exploreTimeFrame7d": "7 d", "exploreTimeFrame30d": "30 d", "exploreCategoryDeFi": "<PERSON><PERSON><PERSON>", "exploreCategoryCollectibles": "Sammelobjekte", "exploreCategoryMarketplace": "Marktplatz", "exploreCategoryGaming": "Gaming", "exploreCategoryBridges": "Überbrückungen", "exploreCategoryOther": "<PERSON><PERSON>", "exploreCategorySocial": "Sozial", "exploreCategoryCommunity": "Community", "exploreCategorySocialFi": "SocialFi", "exploreCategoryStaking": "Einsatz", "exploreCategoryArt": "<PERSON><PERSON>", "exploreCategoryTools": "Werkzeuge", "exploreCategoryDeveloperTools": "Entwickler-Werkzeuge", "exploreCategoryHackathon": "<PERSON><PERSON><PERSON><PERSON>", "exploreCategoryNFTStaking": "NFT-Einsatz", "exploreCategoryExplorer": "Explorer", "exploreCategoryInscriptions": "Beschriftungen", "exploreCategoryBridge": "Überbrückung", "exploreCategoryAirdrop": "Airdrop", "exploreCategoryAirdropChecker": "Airdrop-Checker", "exploreCategoryPoints": "Punkte", "exploreCategoryQuests": "Aufgaben", "exploreCategoryShop": "Shop", "exploreCategoryProtocol": "Protokoll", "exploreCategoryNamingService": "Benennungsdienst", "exploreCategoryDAO": "DAO", "exploreCategoryPortfolioTracker": "Portfolio-Tracker", "exploreCategoryFitness": "Fitness", "exploreCategoryDePIN": "DePIN", "exploreVolume": "Volumen", "exploreFloor": "Mindest.", "exploreCap": "Marktkapitalisierung", "exploreToken": "Token", "explorePrice": "Pre<PERSON>", "explore24hVolume": "24h Volumen", "exploreErrorButtonText": "<PERSON><PERSON><PERSON> versuchen", "exploreErrorDescription": "Beim Laden des Inhalts „Erkunden“ ist ein Fehler aufgetreten. Bitte aktualisieren Sie und versuchen Si<PERSON> es erneut", "exploreErrorTitle": "Laden des Inhalts „Erkunden“ fehlgeschlagen", "exploreNetworkError": "Es ist ein Netzwerkfehler aufgetreten. Bitte versuchen Sie es später noch einmal.", "exploreTokensLegalDisclaimer": "Token-Listen werden anhand von <PERSON>, die von verschiedenen Drittanbietern wie CoinGecko, Birdeye und Jupiter bereitgestellt werden. Die Wertentwicklung basiert auf dem vorherigen Zeitraum von 24 Stunden. Die Wertentwicklung in der Vergangenheit ist kein Hinweis auf die zukünftige Wertentwicklung.", "swapperTokensLegalDisclaimer": "Trendlisten für Token werden anhand von Marktdaten verschiedener Drittanbieter wie CoinGecko, Birdeye und Jupiter erstellt und basieren auf beliebten Token, die von Phantom-Nutzern über den Swapper im angegebenen Zeitraum geswapt wurden. Die Wertentwicklung in der Vergangenheit ist kein Hinweis auf die zukünftige Wertentwicklung.", "exploreLearnErrorTitle": "Laden des Inhalts „Lernen“ fehlgeschlagen", "exploreLearnErrorDescription": "Beim Laden des Inhalts „Lernen“ ist ein Fehler aufgetreten. Bitte aktualisieren Sie und versuchen Si<PERSON> es erneut", "exploreShowMore": "<PERSON><PERSON> anzeigen", "exploreShowLess": "<PERSON><PERSON> anzeigen", "exploreVisitSite": "Website besuchen", "dappBrowserSearchScreenVisitSite": "Website besuchen", "dappBrowserSearchScreenSearchWithGoogle": "Mit Google suchen", "dappBrowserSearchScreenSearchLinkYouCopied": "Kopierter Link", "dappBrowserExtSearchPlaceholder": "Nach Websites, Token suchen", "dappBrowserSearchNoAppsTokens": "<PERSON><PERSON> Apps oder Token gefunden", "dappBrowserTabsLimitExceededScreenTitle": "<PERSON>ltere Registerkarten schließen?", "dappBrowserTabsLimitExceededScreenDescription": "Sie haben {{tabsCount}} Registerkarten geöffnet. Um mehr zu öffnen, müssen Sie einige Registerkarten schließen.", "dappBrowserTabsLimitExceededScreenCloseAllTabs": "Alle Registerkarten schließen", "dappBrowserTabErrorDomainDoesNotExist": "NXDOMAIN: Diese Domain existiert nicht", "dappBrowserTabErrorHttp": "<PERSON><PERSON><PERSON><PERSON>, bitte verwenden Sie HTTPS", "dappBrowserTabError401Unauthorized": "401 Nicht autorisiert", "dappBrowserTabError501UnhandledRequest": "501 Unbearbeitete Anfrage", "dappBrowserTabErrorTimeout": "ZEITÜBERSCHREITUNG: <PERSON> braucht zu lange zum Antworten", "dappBrowserTabErrorInvalidResponse": "Ungültige Antwort", "dappBrowserTabErrorEmptyResponse": "<PERSON><PERSON>", "dappBrowserTabErrorGeneric": "Ein Fehler ist aufgetreten", "localizedErrorUnknownError": "Ein Fehler ist aufgetreten, bitte versuchen Sie es später noch einmal.", "localizedErrorUnsupportedCountry": "Es tut uns leid, <PERSON>hr Land wird derzeit nicht unterstützt.", "localizedErrorTokensNotLoading": "<PERSON><PERSON>hrer Token ist ein Problem aufgetreten. Bitte versuchen Sie es erneut.", "localizedErrorSwapperNoQuotes": "Tauschen nicht verfüg<PERSON> aufgrund von nicht unterstütztem Paar, geringer Liquidität oder geringem Betrag. Versuchen Sie, den Token oder den Betrag anzupassen.", "localizedErrorSwapperRefuelNoQuotes": "<PERSON>ine Angebote gefunden. Versuchen Sie einen anderen <PERSON>, Betrag oder deaktivieren Sie Refuel.", "localizedErrorInsufficientSellAmount": "Tokenmenge zu niedrig. <PERSON><PERSON><PERSON><PERSON><PERSON> Si<PERSON> den Wert, um Cross-Chain zu tauschen.", "localizedErrorCrossChainUnavailable": "Cross-Chain-Tausch ist derzeit nicht verfügbar, bitte versuchen Si<PERSON> es später noch einmal.", "localizedErrorTokenNotTradable": "Einer der ausgewählten Token ist nicht handelbar. Bitte wählen Si<PERSON> einen anderen Token.", "localizedErrorCollectibleLocked": "Das Token-Konto ist gesperrt.", "localizedErrorCollectibleListed": "Das Token-Konto ist gelistet.", "spamActivityAction": "Ausgeblendete Elemente anzeigen", "spamActivityTitle": "Ausgeblendete Aktivität", "spamActivityWarning": "Diese Transaktion wurde ausgeblendet, weil <PERSON> glaubt, dass es sich um Spam handeln könnte.", "appAuthenticationFailed": "Authentifizierung fehlgeschlagen", "appAuthenticationFailedDescription": "Es gab ein Problem mit Ihrem Authentifizierungsversuch. Bitte versuchen Sie es erneut.", "partialErrorBalanceChainName": "Es gibt Probleme bei der Aktualisierung Ihres {{chainName}}-Guthabens. Ihr Guthaben ist sicher.", "partialErrorGeneric": "Es gibt Probleme bei der Aktualisierung von Netzwerken, daher sind einige Ihrer Token-Guthaben und Preise möglicherweise nicht mehr aktuell. Ihr Guthaben ist sicher.", "partialErrorTokenDetail": "Es gibt Probleme bei der Aktualisierung Ihres Token-Guthabens. Ihr Guthaben ist sicher.", "partialErrorTokenPrices": "Es gibt Probleme bei der Aktualisierung Ihrer Token-Preise. Ihr Guthaben ist sicher.", "partialErrorTokensTrimmed": "Es gibt Probleme bei der Anzeige der Token in Ihrem Portfolio. Ihr Guthaben ist sicher.", "publicFungibleDetailAbout": "<PERSON><PERSON>", "publicFungibleDetailYourBalance": "<PERSON><PERSON>", "publicFungibleDetailInfo": "Info", "publicFungibleDetailShowMore": "<PERSON><PERSON> anzeigen", "publicFungibleDetailShowLess": "<PERSON><PERSON> anzeigen", "publicFungibleDetailPerformance": "24h-<PERSON><PERSON><PERSON>", "publicFungibleDetailSecurity": "Sicherheit", "publicFungibleDetailMarketCap": "Marktkapitalisierung", "publicFungibleDetailTotalSupply": "Gesamtvorrat", "publicFungibleDetailCirculatingSupply": "Vorrat im Umlauf", "publicFungibleDetailMaxSupply": "<PERSON><PERSON>", "publicFungibleDetailHolders": "<PERSON><PERSON><PERSON>", "publicFungibleDetailVolume": "Volumen", "publicFungibleDetailTrades": "<PERSON>", "publicFungibleDetailTraders": "<PERSON><PERSON><PERSON><PERSON>", "publicFungibleDetailUniqueWallets": "Einzigartige Wallets", "publicFungibleDetailTop10Holders": "Top 10 Inhaber", "publicFungibleDetailTop10HoldersTooltip": "Zeigt den prozentualen Anteil des aktuellen Gesamtangebots an, der von den 10 größten Besitzern des Tokens gehalten wird. Es ist ein <PERSON> da<PERSON>ür, wie leicht der Preis manipuliert werden kann.", "publicFungibleDetailMintable": "Mintbar", "publicFungibleDetailMintableTooltip": "Die Anzahl der Token kann vom Vertragseigentümer erhöht werden, wenn ein Token mintbar ist.", "publicFungibleDetailMutableInfo": "Veränderliche Info", "publicFungibleDetailMutableInfoTooltip": "<PERSON><PERSON>-Informationen wie Name, Logo und Website-Adresse veränderbar sind, können sie vom Vertragsinhaber geändert werden.", "publicFungibleDetailOwnershipRenounced": "Besitz aufgegeben", "publicFungibleDetailOwnershipRenouncedTooltip": "<PERSON>n auf den Besitz von <PERSON> verzichtet wird, kann niemand mehr Funktionen wie das Minten weiterer Token ausführen.", "publicFungibleDetailUpdateAuthority": "Aktualisierungsinstanz", "publicFungibleDetailUpdateAuthorityTooltip": "Die Aktualisierungsautorität ist die Adresse des Wallets, die Informationen ändern kann, wenn ein Token veränderbar ist.", "publicFungibleDetailFreezeAuthority": "Einfrierinstanz", "publicFungibleDetailFreezeAuthorityTooltip": "Die Einfrierinstanz ist die Adresse des Wallets, die die Transaktion von Geldern verhindern kann.", "publicFungibleUnverifiedToken": "Dieser Token wurde noch nicht verifiziert. Interagieren Sie nur mit Token, denen Si<PERSON> vertrauen.", "publicFungibleDetailSwap": "{{tokenSymbol}} tauschen", "publicFungibleDetailSwapDescription": "{{tokenSymbol}} taus<PERSON> mit der Phantom-App", "publicFungibleDetailLinkCopied": "In die Zwischenablage kopiert", "publicFungibleDetailContract": "Vertrag", "publicFungibleDetailMint": "<PERSON><PERSON>", "unifiedTokenDetailTransactionActivity": "Aktivität", "unifiedTokenDetailSeeMoreTransactionActivity": "<PERSON><PERSON> anzeigen", "unifiedTokenDetailTransactionActivityError": "Letzte Aktivität konnte nicht geladen werden", "additionalNetworksTitle": "Zusätzliche Netzwerke", "copyAddressRowAdditionalNetworks": "Zusätzliche Netzwerke", "copyAddressRowAdditionalNetworksHeader": "Die folgenden Netzwerke verwenden dieselbe Adresse wie Ethereum:", "copyAddressRowAdditionalNetworksDescription": "Sie können Ihre Ethereum-Adresse sicher verwenden, um Vermögenswerte in jedem dieser Netzwerke zu senden und zu empfangen.", "cpeUnknownError": "Unbekannter Fehler", "cpeUnknownInstructionError": "Unbekannter Anweisungsfehler", "cpeAccountFrozen": "Konto ist eingefroren", "cpeAssetFrozen": "Vermögenswert ist eingefroren", "cpeInsufficientFunds": "Nicht genügend Guthaben", "cpeInvalidAuthority": "Ungültige Instanz", "cpeBalanceBelowRent": "<PERSON><PERSON><PERSON><PERSON> unter der mietfreien Schwelle", "cpeNotApprovedForConfidentialTransfers": "Konto nicht für vertrauliche Überweisungen zugelassen", "cpeNotAcceptingDepositsOrTransfers": "Konto nimmt keine Einzahlungen oder Überweisungen an", "cpeNoMemoButRequired": "<PERSON><PERSON> Me<PERSON> in der vorherigen Anweisung; erforderlich für den Empfänger, um eine Überweisung zu erhalten", "cpeTransferDisabledForMint": "Überweisung ist für diesen Mint deaktiviert", "cpeDepositAmountExceedsLimit": "Einzahlungsbetrag überschreitet Höchstgrenze", "cpeInsufficientFundsForRent": "Unzureichende Mittel für die Miete", "reportIssueScreenTitle": "Ein Problem melden", "publicFungibleReportIssuePrompt": "Welches Problem möchten Sie über {{tokenName}} melden?", "publicFungibleReportIssueIncorrectInformation": "Falsche Angabe", "publicFungibleReportIssuePriceStale": "Preis wird nicht aktualisiert", "publicFungibleReportIssuePriceMissing": "<PERSON><PERSON> fehlt", "publicFungibleReportIssuePerformanceIncorrect": "24h-Leistung ist falsch", "publicFungibleReportIssueLinkBroken": "Social-Links sind nicht erreichbar", "publicFungibleDetailErrorLoading": "Tokendaten nicht verfügbar", "reportUserPrompt": "Welches Problem möchten Sie über @{{username}} melden?", "reportUserOptionAbuseAndHarrassmentTitle": "Missbrauch und Belästigung", "reportUserOptionAbuseAndHarrassmentDescription": "Gezielte Belästigung, Anstiftung zur Belästigung, gewalttätige Drohungen, hasserfüllte Inhalte und Verweise", "reportUserOptionPrivacyAndImpersonationTitle": "Datenschutz und falsche Identität", "reportUserOptionPrivacyAndImpersonationDescription": "Weitergabe oder Androhung der Preisgabe privater Informationen, Vorgabe, jeman<PERSON> <PERSON><PERSON><PERSON> zu sein", "reportUserOptionSpamTitle": "Spam", "reportUserOptionSpamDescription": "Unechtes Konto, Betrug, bösartige Links", "reportUserSuccess": "<PERSON><PERSON><PERSON> zu Benutzer eingereicht.", "settingsClaimUsernameTitle": "Benutzernamen erstellen", "settingsClaimUsernameDescription": "Eine einzigartige Identität, so einzigartig wie Ihr Wallet", "settingsClaimUsernameValueProp1": "Vereinfachte Identität", "settingsClaimUsernameValueProp1Description": "Verabschieden Si<PERSON> sich von langen, komplexen Adressen und begrüßen Sie eine benutzerfreundliche Identität", "settingsClaimUsernameValueProp2": "Schneller & einfacher", "settingsClaimUsernameValueProp2Description": "Einfaches Senden und Empfangen von Kryptowährungen, Anmeldung bei Ihrem Wallet und Verbindung mit Freunden", "settingsClaimUsernameValueProp3": "Bleiben Sie synchron", "settingsClaimUsernameValueProp3Description": "Verbinden Sie ein beliebiges Konto mit Ihrem Benutzernamen und es wird auf all Ihren Geräten synchronisiert", "settingsClaimUsernameHelperText": "Ihr eindeutiger Name für Ihr Phantom-Konto", "settingsClaimUsernameValidationDefault": "Dieser Benutzername kann später nicht mehr geändert werden", "settingsClaimUsernameValidationAvailable": "Benutzername verfügbar", "settingsClaimUsernameValidationUnavailable": "Benutzername nicht verfügbar", "settingsClaimUsernameValidationServerError": "Es konnte nicht geprüft werden, ob der Benutzername verfügbar ist, bitte versuchen Sie es später noch einmal", "settingsClaimUsernameValidationErrorLine1": "Ungültiger Benutzername.", "settingsClaimUsernameValidationErrorLine2": "Benutzernamen müssen zwischen {{minChar}} und {{maxChar}} Zeichen lang sein und dürfen nur Buchstaben und Zahlen enthalten.", "settingsClaimUsernameValidationLoading": "<PERSON><PERSON><PERSON><PERSON>, ob dieser Benutzername verfügbar ist …", "settingsClaimUsernameSaveAndContinue": "Speichern & fortfahren", "settingsClaimUsernameChooseAvatarTitle": "Avatar wählen", "settingsClaimUsernameAnonymousAuthTitle": "Anonyme Authentifizierung", "settingsClaimUsernameAnonymousAuthDescription": "Melden Sie sich anonym mit einer Signatur bei Ihrem Phantom-Konto an", "settingsClaimUsernameAnonymousAuthBadge": "<PERSON><PERSON><PERSON><PERSON>, wie das funktioniert", "settingsClaimUsernameLinkWalletsTitle": "Verknüpfen Sie Ihr Wallet", "settingsClaimUsernameLinkWalletsDescription": "<PERSON><PERSON><PERSON><PERSON> Sie die Wallets, die auf anderen Geräten mit Ihrem Benutzernamen angezeigt werden", "settingsClaimUsernameLinkWalletsBadge": "Nicht öffentlich einsehbar", "settingsClaimUsernameConnectAccountsTitle": "Konten verbinden", "settingsClaimUsernameConnectAccountsHelperText": "Jede Chain-Adresse wird mit Ihrem Benutzernamen verknüpft. Si<PERSON> können diese später ändern.", "settingsClaimUsernameContinue": "Fortfahren", "settingsClaimUsernameCreateUsername": "Benutzernamen erstellen", "settingsClaimUsernameCreating": "<PERSON><PERSON><PERSON> …", "settingsClaimUsernameSuccess": "Benutzername erstellt!", "settingsClaimUsernameError": "<PERSON><PERSON>n Ihres Benutzernamens ist ein Fehler aufgetreten", "settingsClaimUsernameTryAgain": "<PERSON><PERSON><PERSON> versuchen", "settingsClaimUsernameSuccessHelperText": "<PERSON>e können jetzt Ihren neuen Benutzernamen in allen Phantom-Wallets verwenden", "settingsClaimUsernameSettingsSyncedTitle": "Synchronisierte Einstellungen", "settingsClaimUsernameSettingsSyncedHelperText": "Verabschieden Si<PERSON> sich von langen, komplexen Adressen und begrüßen Sie eine benutzerfreundliche Identität", "settingsClaimUsernameSendToUsernameTitle": "An Benutzernamen senden", "settingsClaimUsernameSendToUsernameHelperText": "Einfaches Senden und Empfangen von Kryptowährungen, Anmeldung bei Ihrem Wallet und Verbindung mit Freunden", "settingsClaimUsernameManageAddressesTitle": "Öffentliche Adressen", "settingsClaimUsernameManageAddressesHelperText": "Alle Token oder Sammelobjekte, die an Ihren Benutzernamen gesendet werden, werden an diese Adressen gesendet", "settingsClaimUsernameManageAddressesBadge": "Öffentlich einsehbar", "settingsClaimUsernameEditAddressesTitle": "Öffentliche Adressen verwalten", "settingsClaimUsernameEditAddressesHelperText": "Alle Token oder Sammelobjekte, die an Ihren Benutzernamen gesendet werden, werden an diese Adressen gesendet. Wählen Sie eine Adresse pro Chain.", "settingsClaimUsernameEditAddressesError": "Pro Netzwerk ist nur eine Adresse zulässig.", "settingsClaimUsernameEditAddressesEditAddress": "<PERSON><PERSON><PERSON> bearbeiten", "settingsClaimUsernameNoAddressesSaved": "<PERSON>ine öffentlichen Adressen gespeichert", "settingsClaimUsernameSave": "Speichern", "settingsClaimUsernameDone": "<PERSON><PERSON><PERSON>", "settingsClaimUsernameWatching": "Beobachte", "settingsClaimUsernameNoOfAccounts": "{{noOfAccounts}} Konto/Konten", "settingsClaimUsernameNoOfAccountsSingular": "1 Konto", "settingsClaimUsernameEmptyAccounts": "<PERSON><PERSON>", "settingsClaimUsernameSettingTitle": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> Ihren @benutzernamen", "settingsClaimUsernameSettingDescription": "Eine einzigartige Identität für Ihr Wallet", "settingsManageUserProfileAbout": "<PERSON><PERSON>", "settingsManageUserProfileAboutUsername": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfileAboutBio": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfileTitle": "<PERSON><PERSON> ver<PERSON>", "settingsManageUserProfileManage": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfileAuthFactors": "Auth-Faktoren", "settingsManageUserProfileAuthFactorsDescription": "<PERSON><PERSON><PERSON><PERSON>, welche Seed-Phrasen oder privaten Schlüssel sich bei Ihrem Phantom-Konto anmelden können.", "settingsManageUserProfileUpdateAuthFactorsToast": "Authentifizierungsfaktor aktualisiert!", "settingsManageUserProfileUpdateAuthFactorsToastFailure": "Authentifizierungsfaktoren konnten nicht aktualisiert werden!", "settingsManageUserProfileBiography": "Lebenslauf bearbeiten", "settingsManageUserProfileBiographyDescription": "Fügen Sie Ihrem Profil einen kurzen Lebenslauf hinzu", "settingsManageUserProfileUpdateBiographyToast": "Lebenslauf aktualisiert!", "settingsManageUserProfileUpdateBiographyToastFailure": "<PERSON>s ist ein Fehler aufgetreten. Versuchen Sie es erneut", "settingsManageUserProfileBiographyNoUrlMessage": "Bitte entfernen Sie eventuell vorhandene Links in Ihrem Lebenslauf", "settingsManageUserProfileLinkedWallets": "Verknüpfte Wallets", "settingsManageUserProfileLinkedWalletsDescription": "<PERSON><PERSON><PERSON><PERSON> Sie die Wallets, die auf anderen Geräten angezeigt werden, wenn Sie sich bei Ihrem Phantom-Konto anmelden.", "settingsManageUserProfileUpdateLinkedWalletsToast": "Verknüpfte Wallets aktualisiert!", "settingsManageUserProfileUpdateLinkedWalletsToastFailure": "Verknüpfte Wallets konnten nicht aktualisiert werden!", "settingsManageUserProfilePrivacy": "Datenschutz", "settingsManageUserProfileUpdatePrivacyStateToast": "Datenschutz aktualisiert!", "settingsManageUserProfileUpdatePrivacyStateToastFailure": "Datenschutz konnte nicht aktualisiert werden!", "settingsManageUserProfilePublicAddresses": "Öffentliche Adressen", "settingsManageUserProfileUpdatePublicAddressToast": "Öffentliche Adressen aktualisiert!", "settingsManageUserProfileUpdatePublicAddressToastFailure": "Aktualisierung der öffentlichen Adresse fehlgeschlagen!", "settingsManageUserProfilePrivacyStatePublic": "<PERSON><PERSON><PERSON><PERSON>", "settingsManageUserProfilePrivacyStatePublicDescription": "Ihr Profil und Ihre öffentlichen Adressen sind für jeden sicht- und suchbar", "settingsManageUserProfilePrivacyStatePrivate": "Privat", "settingsManageUserProfilePrivacyStatePrivateDescription": "Ihr Profil ist für jeden such<PERSON>, aber andere müssen um Berechtigung bitten, Ihr Profil und Ihre öffentlichen Adressen einzusehen.", "settingsManageUserProfilePrivacyStateInvisible": "Unsichtbar", "settingsManageUserProfilePrivacyStateInvisibleDescription": "Ihr Profil und Ihre öffentlichen Adressen sind überall versteckt und nicht entdeckbar", "settingsDownloadPhantom": "Phantom herunterladen", "settingsLogOut": "Abmelden", "seedlessAddAWalletPrimaryText": "<PERSON><PERSON> hinzufügen", "seedlessAddAWalletSecondaryText": "Anmelden oder ein bestehendes Wallet importieren ", "seedlessAddSeedlessWalletPrimaryText": "Wallet ohne Seed hinzufügen", "seedlessAddSeedlessWalletSecondaryText": "Verwenden Sie Ihre Apple ID, Google oder E-Mail-Adresse", "seedlessCreateNewWalletPrimaryText": "Neues Wallet erstellen?", "seedlessCreateNewWalletSecondaryText": "<PERSON><PERSON><PERSON> diese E-Mail-Adresse gibt es kein Wallet, möchten Si<PERSON> eins erstellen?", "seedlessCreateNewWalletButtonText": "<PERSON><PERSON> erstellen", "seedlessCreateNewWalletNoBundlePrimaryText": "Wallet wurde nicht gefunden", "seedlessCreateNewWalletNoBundleSecondaryText": "<PERSON>se E-Mail-Adresse hat kein Wallet", "seedlessCreateNewWalletNoBundleButtonText": "Zurück", "seedlessEmailOptionsPrimaryText": "Wählen Sie Ihre E-Mail-Adresse", "seedlessEmailOptionsSecondaryText": "<PERSON>ügen Si<PERSON> ein Wallet mit Ihrem Apple- oder Google-<PERSON><PERSON> hinzu ", "seedlessEmailOptionsButtonText": "Mit E-Mail-Adresse fortfa<PERSON>", "seedlessEmailOptionsApplePrimaryText": "Apple", "seedlessEmailOptionsAppleSecondaryText": "Wallet mit Ihrer Apple ID erstellen", "seedlessEmailOptionsGooglePrimaryText": "Google", "seedlessEmailOptionsGoogleSecondaryText": "Wallet mit Ihrer Google-E-Mail-Adresse erstellen", "seedlessAlreadyExistsPrimaryText": "Konto existiert bereits", "seedlessAlreadyExistsSecondaryText": "<PERSON><PERSON><PERSON> diese E-Mail-Adresse wurde bereits ein Wallet erstellt. Möchten Sie sich stattdessen anmelden?", "seedlessSignUpWithAppleButtonText": "Mit Apple registrieren", "seedlessContinueWithAppleButtonText": "<PERSON>t <PERSON> fortfahren", "seedlessSignUpWithGoogleButtonText": "Mit Google registrieren", "seedlessContinueWithGoogleButtonText": "Mit Google fortfahren", "seedlessCreateAPinPrimaryText": "<PERSON><PERSON><PERSON>n Sie eine PIN", "seedlessCreateAPinSecondaryText": "Diese wird verwendet, um Ihr Wallet auf allen Ihren Geräten zu sichern. <1>Sie kann nicht wiederhergestellt werden.</1>", "seedlessContinueText": "Fortfahren", "seedlessConfirmPinPrimaryText": "Bestätigen Sie Ihre PIN", "seedlessConfirmPinSecondaryText": "<PERSON>n Si<PERSON> diese PIN verge<PERSON>, können Sie Ihr Wallet auf einem neuen Gerät nicht wiederherstellen.", "seedlessConfirmPinButtonText": "PIN er<PERSON>llen", "seedlessConfirmPinError": "Falsche PIN. Bitte versuchen Sie es erneut", "seedlessAccountsImportedPrimaryText": "Konten importiert", "seedlessAccountsImportedSecondaryText": "Diese Konten werden automatisch in Ihr Wallet importiert", "seedlessPreviouslyImportedTag": "Zuletzt importiert", "seedlessEnterPinPrimaryText": "<PERSON><PERSON><PERSON> Sie Ihre PIN ein", "seedlessEnterPinInvalidPinError": "Falsche PIN eingegeben. Nur 4-stellige Zahlen sind erlaubt", "seedlessEnterPinNumTriesLeft": "{{numTries}} Versuche verbleibend.", "seedlessEnterPinCooldown": "Versuchen Sie es erneut in {{minutesLeft}}:{{secondsLeft}}", "seedlessEnterPinIncorrectLength": "Die PIN muss genau 4 <PERSON><PERSON><PERSON> lang sein", "seedlessEnterPinMatch": "P<PERSON>s stimmen überein", "seedlessDoneText": "<PERSON><PERSON><PERSON>", "seedlessEnterPinToSign": "<PERSON><PERSON><PERSON> Sie Ihre PIN ein, um diese Transaktion zu signieren", "seedlessSigning": "Signatur", "seedlessCreateSeed": "<PERSON><PERSON><PERSON><PERSON> ein Wallet mit Seed-Phrase", "seedlessImportOptions": "Andere Import-Möglichkeiten", "seedlessImportPrimaryText": "Import-Möglichkeiten", "seedlessImportSecondaryText": "Importieren Sie ein vorhandenes Wallet mit Ihrer Seed-Phrase, Ihrem privaten Schlüssel oder Ihrem Hardware-Wallet", "seedlessImportSeedPhrase": "Seed-Phrase importieren", "seedlessImportPrivateKey": "Privaten Schlüssel importieren", "seedlessConnectHardwareWallet": "Hardware Wallet verbinden", "seedlessTryAgain": "<PERSON><PERSON><PERSON> versuchen", "seedlessCreatingWalletPrimaryText": "<PERSON><PERSON><PERSON>", "seedlessCreatingWalletSecondaryText": "Füge Soziales Wallet hinzu", "seedlessLoadingWalletPrimaryText": "Lade Wall<PERSON>", "seedlessLoadingWalletSecondaryText": "Importiere und beobachte Ihre verknüpften Wallets", "seedlessLoadingWalletErrorPrimaryText": "Wallet konnte nicht geladen werden", "seedlessCreatingWalletErrorPrimaryText": "Wallet konnte nicht erstellt werden", "seedlessErrorSecondaryText": "Bitte versuchen Sie es erneut", "seedlessAuthAlreadyExistsErrorText": "Die angegebene E-Mail-Adresse gehört bereits zu einem anderen Phantom-Konto", "seedlessAuthUnknownErrorText": "Es ist ein unbekannter Fehler aufgetreten, bitte versuchen Si<PERSON> es später erneut", "seedlessAuthUnknownErrorTextRefresh": "Es ist ein unbekannter Fehler aufgetreten, bitte versuchen Sie es später erneut. Aktualisieren Sie die Seite, um es erneut zu versuchen.", "seedlessAuthErrorCloseWindow": "Fenster schließen", "seedlessWalletExistsErrorPrimaryText": "Ein Soziales Wallet ist auf Ihrem Gerät bereits vorhanden", "seedlessWalletExistsErrorSecondaryText": "Bitte gehen Sie zurück oder schließen Sie diesen Bildschirm", "seedlessValueProp1PrimaryText": "<PERSON><PERSON><PERSON>", "seedlessValueProp1SecondaryText": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> ein Wallet mit einem Google- oder Apple-Konto und erkunden Sie web3 mit Leichtigkeit", "seedlessValueProp2PrimaryText": "Erhöhte Sicherheit", "seedlessValueProp2SecondaryText": "Ihr Wallet ist sicher und dezentralisiert über mehrere Faktoren gespeichert", "seedlessValueProp3PrimaryText": "Einfache Wiederherstellung", "seedlessValueProp3SecondaryText": "Wiederherstellung des Zugangs zu Ihrem Wallet mit Ihrem Google- oder Apple-Konto und einer 4-stelligen PIN", "seedlessLoggingIn": "<PERSON><PERSON><PERSON><PERSON> …", "seedlessSignUpOrLogin": "Registrieren oder anmelden", "seedlessContinueByEnteringYourEmail": "Fahren Sie fort, indem Sie Ihre E-Mail-Adresse eingeben", "seedless": "<PERSON><PERSON>", "seed": "Seed-<PERSON><PERSON><PERSON>", "seedlessVerifyPinPrimaryText": "PIN verifizieren", "seedlessVerifyPinSecondaryText": "<PERSON>te geben Si<PERSON> Ihre PIN ein, um fortzufahren", "seedlessVerifyPinVerifyButtonText": "Verifizieren", "seedlessVerifyPinForgotButtonText": "<PERSON><PERSON><PERSON><PERSON>?", "seedlessPinConfirmButtonText": "Bestätigen", "seedlessVerifyToastPrimaryText": "Verifizieren Sie Ihre PIN", "seedlessVerifyToastSecondaryText": "Wir werden Si<PERSON>, <PERSON><PERSON><PERSON> <PERSON> zu verifizieren, damit <PERSON><PERSON> sie sich merken können. Wenn Sie sie vergessen, können Sie Ihr Wallet nicht wiederherstellen.", "seedlessVerifyToastSuccessText": "Ihre PIN wurde verifiziert!", "seedlessForgotPinPrimaryText": "PIN mit einem anderen Gerät zurücksetzen", "seedlessForgotPinSecondaryText": "Aus Sicherheitsgründen können Sie Ihre PIN nur auf anderen Geräten zurücksetzen, auf denen Sie angemeldet sind.", "seedlessForgotPinInstruction1PrimaryText": "Anderes Gerät ö<PERSON>", "seedlessForgotPinInstruction1SecondaryText": "Gehen Sie zu einem anderen Gerät, auf dem Ihr Phantom-Konto mit Ihrer E-Mail-Adresse angemeldet ist", "seedlessForgotPinInstruction2PrimaryText": "Gehen Sie zu den Einstellungen", "seedlessForgotPinInstruction2SecondaryText": "<PERSON><PERSON><PERSON><PERSON> Sie in den Einstellungen „Sicherheit und Datenschutz“ und dann „PIN zurücksetzen“.", "seedlessForgotPinInstruction3PrimaryText": "Legen Sie Ihre neue PIN fest", "seedlessForgotPinInstruction3SecondaryText": "Nachdem Sie Ihre neue PIN festgelegt haben, können <PERSON> sich nun mit diesem Gerät bei Ihrem Wallet anmelden", "seedlessForgotPinButtonText": "Ich habe diese Schritte durchgeführt", "seedlessResetPinPrimaryText": "PIN zurücksetzen", "seedlessResetPinSecondaryText": "<PERSON><PERSON>en Si<PERSON> eine neue PIN ein, die Si<PERSON> sich merken können. Diese wird verwendet, um Ihr Wallet auf allen Ihren Geräten zu sichern", "seedlessResetPinSuccessText": "Ihre PIN wurde aktualisiert!", "embeddedOnboardingAgreeToTermsOfServiceInterpolated": "Mit der Erstellung eines Wallets erklären Sie sich mit unseren <1>Nutzungsbedingungen</1> und <5>Datenschutzbestimmungen</5> einverstanden", "pageNotFound": "Seite wurde nicht gefunden", "pageNotFoundDescription": "Wir haben Sie nicht ignoriert! Diese Seite existiert nicht, oder sie wurde verschoben.", "webTokenPagesLegalDisclaimer": "Die Preisinformationen werden nur zu Informationszwecken bereitgestellt und stellen keine Finanzberatung dar. Die Marktdaten werden von Dritten zur Verfügung gestellt und Phantom übernimmt keine Gewähr für die Richtigkeit der Informationen.", "signUpOrLogin": "Registrieren oder Anmelden", "portalOnboardingAgreeToTermsOfServiceInterpolated": "Mit der Erstellung eines Kontos erklären Sie sich mit unseren <1>Nutzungsbedingungen</1> und <5>Datenschutzbestimmungen</5> einverstanden", "feedNoActivity": "Noch keine Aktivität", "followRequests": "Anfragen folgen", "following": "Folgen", "followers": "Folgende", "follower": "Folgender", "joined": "Beigetreten", "followersCount_one": "{{formattedCount}} follower", "followersCount_other": "{{formattedCount}} followers", "noFollowers": "<PERSON><PERSON>", "noFollowing": "<PERSON><PERSON>", "noUsersFound": "<PERSON><PERSON> gefunden", "viewProfile": "<PERSON><PERSON> anzeigen", "followRequestAccepted": "Follow Request Accepted", "followRequestDenied": "Follow Request Denied"}