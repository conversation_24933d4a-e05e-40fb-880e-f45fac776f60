import{a as m}from"./chunk-GMBAJ6CC.js";import{l as a,m as c}from"./chunk-56SJOU6P.js";import{a as T}from"./chunk-7X4NV6OJ.js";import{f as u,h as e,n as s}from"./chunk-3KENBVE7.js";e();s();var r=u(T());var P=r.default.memo(l=>{let{children:p,i18nKey:o,...n}=l,{t}=c(),i=(0,r.useMemo)(()=>(o?`${t(o,{...n,...n.values})}`:"").replace(/<\/?[^>]+(>|$)/g,""),[o,n,t]);return r.default.createElement(m,{fallback:i},r.default.createElement(a,{i18nKey:o,...n},p))});export{P as a};
//# sourceMappingURL=chunk-4VDZJDFB.js.map
