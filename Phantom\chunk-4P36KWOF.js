import{h as Oe,n as Ce}from"./chunk-3KENBVE7.js";Oe();Ce();var g;(function(r){r.assertEqual=n=>n;function e(n){}r.assertIs=e;function t(n){throw new Error}r.assertNever=t,r.arrayToEnum=n=>{let a={};for(let i of n)a[i]=i;return a},r.getValidEnumValues=n=>{let a=r.objectKeys(n).filter(o=>typeof n[n[o]]!="number"),i={};for(let o of a)i[o]=n[o];return r.objectValues(i)},r.objectValues=n=>r.objectKeys(n).map(function(a){return n[a]}),r.objectKeys=typeof Object.keys=="function"?n=>Object.keys(n):n=>{let a=[];for(let i in n)Object.prototype.hasOwnProperty.call(n,i)&&a.push(i);return a},r.find=(n,a)=>{for(let i of n)if(a(i))return i},r.isInteger=typeof Number.isInteger=="function"?n=>Number.isInteger(n):n=>typeof n=="number"&&isFinite(n)&&Math.floor(n)===n;function s(n,a=" | "){return n.map(i=>typeof i=="string"?`'${i}'`:i).join(a)}r.joinValues=s,r.jsonStringifyReplacer=(n,a)=>typeof a=="bigint"?a.toString():a})(g||(g={}));var ke;(function(r){r.mergeShapes=(e,t)=>({...e,...t})})(ke||(ke={}));var f=g.arrayToEnum(["string","nan","number","integer","float","boolean","date","bigint","symbol","function","undefined","null","array","object","unknown","promise","void","never","map","set"]),A=r=>{switch(typeof r){case"undefined":return f.undefined;case"string":return f.string;case"number":return isNaN(r)?f.nan:f.number;case"boolean":return f.boolean;case"function":return f.function;case"bigint":return f.bigint;case"symbol":return f.symbol;case"object":return Array.isArray(r)?f.array:r===null?f.null:r.then&&typeof r.then=="function"&&r.catch&&typeof r.catch=="function"?f.promise:typeof Map<"u"&&r instanceof Map?f.map:typeof Set<"u"&&r instanceof Set?f.set:typeof Date<"u"&&r instanceof Date?f.date:f.object;default:return f.unknown}},c=g.arrayToEnum(["invalid_type","invalid_literal","custom","invalid_union","invalid_union_discriminator","invalid_enum_value","unrecognized_keys","invalid_arguments","invalid_return_type","invalid_date","invalid_string","too_small","too_big","invalid_intersection_types","not_multiple_of","not_finite"]),Le=r=>JSON.stringify(r,null,2).replace(/"([^"]+)":/g,"$1:"),w=class r extends Error{constructor(e){super(),this.issues=[],this.addIssue=s=>{this.issues=[...this.issues,s]},this.addIssues=(s=[])=>{this.issues=[...this.issues,...s]};let t=new.target.prototype;Object.setPrototypeOf?Object.setPrototypeOf(this,t):this.__proto__=t,this.name="ZodError",this.issues=e}get errors(){return this.issues}format(e){let t=e||function(a){return a.message},s={_errors:[]},n=a=>{for(let i of a.issues)if(i.code==="invalid_union")i.unionErrors.map(n);else if(i.code==="invalid_return_type")n(i.returnTypeError);else if(i.code==="invalid_arguments")n(i.argumentsError);else if(i.path.length===0)s._errors.push(t(i));else{let o=s,l=0;for(;l<i.path.length;){let d=i.path[l];l===i.path.length-1?(o[d]=o[d]||{_errors:[]},o[d]._errors.push(t(i))):o[d]=o[d]||{_errors:[]},o=o[d],l++}}};return n(this),s}static assert(e){if(!(e instanceof r))throw new Error(`Not a ZodError: ${e}`)}toString(){return this.message}get message(){return JSON.stringify(this.issues,g.jsonStringifyReplacer,2)}get isEmpty(){return this.issues.length===0}flatten(e=t=>t.message){let t={},s=[];for(let n of this.issues)n.path.length>0?(t[n.path[0]]=t[n.path[0]]||[],t[n.path[0]].push(e(n))):s.push(e(n));return{formErrors:s,fieldErrors:t}}get formErrors(){return this.flatten()}};w.create=r=>new w(r);var te=(r,e)=>{let t;switch(r.code){case c.invalid_type:r.received===f.undefined?t="Required":t=`Expected ${r.expected}, received ${r.received}`;break;case c.invalid_literal:t=`Invalid literal value, expected ${JSON.stringify(r.expected,g.jsonStringifyReplacer)}`;break;case c.unrecognized_keys:t=`Unrecognized key(s) in object: ${g.joinValues(r.keys,", ")}`;break;case c.invalid_union:t="Invalid input";break;case c.invalid_union_discriminator:t=`Invalid discriminator value. Expected ${g.joinValues(r.options)}`;break;case c.invalid_enum_value:t=`Invalid enum value. Expected ${g.joinValues(r.options)}, received '${r.received}'`;break;case c.invalid_arguments:t="Invalid function arguments";break;case c.invalid_return_type:t="Invalid function return type";break;case c.invalid_date:t="Invalid date";break;case c.invalid_string:typeof r.validation=="object"?"includes"in r.validation?(t=`Invalid input: must include "${r.validation.includes}"`,typeof r.validation.position=="number"&&(t=`${t} at one or more positions greater than or equal to ${r.validation.position}`)):"startsWith"in r.validation?t=`Invalid input: must start with "${r.validation.startsWith}"`:"endsWith"in r.validation?t=`Invalid input: must end with "${r.validation.endsWith}"`:g.assertNever(r.validation):r.validation!=="regex"?t=`Invalid ${r.validation}`:t="Invalid";break;case c.too_small:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at least":"more than"} ${r.minimum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at least":"over"} ${r.minimum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${r.minimum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly equal to ":r.inclusive?"greater than or equal to ":"greater than "}${new Date(Number(r.minimum))}`:t="Invalid input";break;case c.too_big:r.type==="array"?t=`Array must contain ${r.exact?"exactly":r.inclusive?"at most":"less than"} ${r.maximum} element(s)`:r.type==="string"?t=`String must contain ${r.exact?"exactly":r.inclusive?"at most":"under"} ${r.maximum} character(s)`:r.type==="number"?t=`Number must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="bigint"?t=`BigInt must be ${r.exact?"exactly":r.inclusive?"less than or equal to":"less than"} ${r.maximum}`:r.type==="date"?t=`Date must be ${r.exact?"exactly":r.inclusive?"smaller than or equal to":"smaller than"} ${new Date(Number(r.maximum))}`:t="Invalid input";break;case c.custom:t="Invalid input";break;case c.invalid_intersection_types:t="Intersection results could not be merged";break;case c.not_multiple_of:t=`Number must be a multiple of ${r.multipleOf}`;break;case c.not_finite:t="Number must be finite";break;default:t=e.defaultError,g.assertNever(r)}return{message:t}},Ze=te;function ze(r){Ze=r}function he(){return Ze}var pe=r=>{let{data:e,path:t,errorMaps:s,issueData:n}=r,a=[...t,...n.path||[]],i={...n,path:a};if(n.message!==void 0)return{...n,path:a,message:n.message};let o="",l=s.filter(d=>!!d).slice().reverse();for(let d of l)o=d(i,{data:e,defaultError:o}).message;return{...n,path:a,message:o}},Ue=[];function u(r,e){let t=he(),s=pe({issueData:e,data:r.data,path:r.path,errorMaps:[r.common.contextualErrorMap,r.schemaErrorMap,t,t===te?void 0:te].filter(n=>!!n)});r.common.issues.push(s)}var x=class r{constructor(){this.value="valid"}dirty(){this.value==="valid"&&(this.value="dirty")}abort(){this.value!=="aborted"&&(this.value="aborted")}static mergeArray(e,t){let s=[];for(let n of t){if(n.status==="aborted")return m;n.status==="dirty"&&e.dirty(),s.push(n.value)}return{status:e.value,value:s}}static async mergeObjectAsync(e,t){let s=[];for(let n of t){let a=await n.key,i=await n.value;s.push({key:a,value:i})}return r.mergeObjectSync(e,s)}static mergeObjectSync(e,t){let s={};for(let n of t){let{key:a,value:i}=n;if(a.status==="aborted"||i.status==="aborted")return m;a.status==="dirty"&&e.dirty(),i.status==="dirty"&&e.dirty(),a.value!=="__proto__"&&(typeof i.value<"u"||n.alwaysSet)&&(s[a.value]=i.value)}return{status:e.value,value:s}}},m=Object.freeze({status:"aborted"}),ee=r=>({status:"dirty",value:r}),k=r=>({status:"valid",value:r}),be=r=>r.status==="aborted",we=r=>r.status==="dirty",de=r=>r.status==="valid",ue=r=>typeof Promise<"u"&&r instanceof Promise;function me(r,e,t,s){if(t==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof e=="function"?r!==e||!s:!e.has(r))throw new TypeError("Cannot read private member from an object whose class did not declare it");return t==="m"?s:t==="a"?s.call(r):s?s.value:e.get(r)}function je(r,e,t,s,n){if(s==="m")throw new TypeError("Private method is not writable");if(s==="a"&&!n)throw new TypeError("Private accessor was defined without a setter");if(typeof e=="function"?r!==e||!n:!e.has(r))throw new TypeError("Cannot write private member to an object whose class did not declare it");return s==="a"?n.call(r,t):n?n.value=t:e.set(r,t),t}var h;(function(r){r.errToObj=e=>typeof e=="string"?{message:e}:e||{},r.toString=e=>typeof e=="string"?e:e?.message})(h||(h={}));var oe,ce,C=class{constructor(e,t,s,n){this._cachedPath=[],this.parent=e,this.data=t,this._path=s,this._key=n}get path(){return this._cachedPath.length||(this._key instanceof Array?this._cachedPath.push(...this._path,...this._key):this._cachedPath.push(...this._path,this._key)),this._cachedPath}},Ee=(r,e)=>{if(de(e))return{success:!0,data:e.value};if(!r.common.issues.length)throw new Error("Validation failed but no issues detected.");return{success:!1,get error(){if(this._error)return this._error;let t=new w(r.common.issues);return this._error=t,this._error}}};function v(r){if(!r)return{};let{errorMap:e,invalid_type_error:t,required_error:s,description:n}=r;if(e&&(t||s))throw new Error(`Can't use "invalid_type_error" or "required_error" in conjunction with custom error map.`);return e?{errorMap:e,description:n}:{errorMap:(i,o)=>{var l,d;let{message:y}=r;return i.code==="invalid_enum_value"?{message:y??o.defaultError}:typeof o.data>"u"?{message:(l=y??s)!==null&&l!==void 0?l:o.defaultError}:i.code!=="invalid_type"?{message:o.defaultError}:{message:(d=y??t)!==null&&d!==void 0?d:o.defaultError}},description:n}}var _=class{constructor(e){this.spa=this.safeParseAsync,this._def=e,this.parse=this.parse.bind(this),this.safeParse=this.safeParse.bind(this),this.parseAsync=this.parseAsync.bind(this),this.safeParseAsync=this.safeParseAsync.bind(this),this.spa=this.spa.bind(this),this.refine=this.refine.bind(this),this.refinement=this.refinement.bind(this),this.superRefine=this.superRefine.bind(this),this.optional=this.optional.bind(this),this.nullable=this.nullable.bind(this),this.nullish=this.nullish.bind(this),this.array=this.array.bind(this),this.promise=this.promise.bind(this),this.or=this.or.bind(this),this.and=this.and.bind(this),this.transform=this.transform.bind(this),this.brand=this.brand.bind(this),this.default=this.default.bind(this),this.catch=this.catch.bind(this),this.describe=this.describe.bind(this),this.pipe=this.pipe.bind(this),this.readonly=this.readonly.bind(this),this.isNullable=this.isNullable.bind(this),this.isOptional=this.isOptional.bind(this)}get description(){return this._def.description}_getType(e){return A(e.data)}_getOrReturnCtx(e,t){return t||{common:e.parent.common,data:e.data,parsedType:A(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}_processInputParams(e){return{status:new x,ctx:{common:e.parent.common,data:e.data,parsedType:A(e.data),schemaErrorMap:this._def.errorMap,path:e.path,parent:e.parent}}}_parseSync(e){let t=this._parse(e);if(ue(t))throw new Error("Synchronous parse encountered promise.");return t}_parseAsync(e){let t=this._parse(e);return Promise.resolve(t)}parse(e,t){let s=this.safeParse(e,t);if(s.success)return s.data;throw s.error}safeParse(e,t){var s;let n={common:{issues:[],async:(s=t?.async)!==null&&s!==void 0?s:!1,contextualErrorMap:t?.errorMap},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:A(e)},a=this._parseSync({data:e,path:n.path,parent:n});return Ee(n,a)}async parseAsync(e,t){let s=await this.safeParseAsync(e,t);if(s.success)return s.data;throw s.error}async safeParseAsync(e,t){let s={common:{issues:[],contextualErrorMap:t?.errorMap,async:!0},path:t?.path||[],schemaErrorMap:this._def.errorMap,parent:null,data:e,parsedType:A(e)},n=this._parse({data:e,path:s.path,parent:s}),a=await(ue(n)?n:Promise.resolve(n));return Ee(s,a)}refine(e,t){let s=n=>typeof t=="string"||typeof t>"u"?{message:t}:typeof t=="function"?t(n):t;return this._refinement((n,a)=>{let i=e(n),o=()=>a.addIssue({code:c.custom,...s(n)});return typeof Promise<"u"&&i instanceof Promise?i.then(l=>l?!0:(o(),!1)):i?!0:(o(),!1)})}refinement(e,t){return this._refinement((s,n)=>e(s)?!0:(n.addIssue(typeof t=="function"?t(s,n):t),!1))}_refinement(e){return new T({schema:this,typeName:p.ZodEffects,effect:{type:"refinement",refinement:e}})}superRefine(e){return this._refinement(e)}optional(){return O.create(this,this._def)}nullable(){return Z.create(this,this._def)}nullish(){return this.nullable().optional()}array(){return I.create(this,this._def)}promise(){return V.create(this,this._def)}or(e){return W.create([this,e],this._def)}and(e){return q.create(this,e,this._def)}transform(e){return new T({...v(this._def),schema:this,typeName:p.ZodEffects,effect:{type:"transform",transform:e}})}default(e){let t=typeof e=="function"?e:()=>e;return new X({...v(this._def),innerType:this,defaultValue:t,typeName:p.ZodDefault})}brand(){return new le({typeName:p.ZodBranded,type:this,...v(this._def)})}catch(e){let t=typeof e=="function"?e:()=>e;return new Q({...v(this._def),innerType:this,catchValue:t,typeName:p.ZodCatch})}describe(e){let t=this.constructor;return new t({...this._def,description:e})}pipe(e){return fe.create(this,e)}readonly(){return K.create(this)}isOptional(){return this.safeParse(void 0).success}isNullable(){return this.safeParse(null).success}},Be=/^c[^\s-]{8,}$/i,We=/^[0-9a-z]+$/,qe=/^[0-9A-HJKMNP-TV-Z]{26}$/,Ye=/^[0-9a-fA-F]{8}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{4}\b-[0-9a-fA-F]{12}$/i,Je=/^[a-z0-9_-]{21}$/i,He=/^[-+]?P(?!$)(?:(?:[-+]?\d+Y)|(?:[-+]?\d+[.,]\d+Y$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:(?:[-+]?\d+W)|(?:[-+]?\d+[.,]\d+W$))?(?:(?:[-+]?\d+D)|(?:[-+]?\d+[.,]\d+D$))?(?:T(?=[\d+-])(?:(?:[-+]?\d+H)|(?:[-+]?\d+[.,]\d+H$))?(?:(?:[-+]?\d+M)|(?:[-+]?\d+[.,]\d+M$))?(?:[-+]?\d+(?:[.,]\d+)?S)?)??$/,Ge=/^(?!\.)(?!.*\.\.)([A-Z0-9_'+\-\.]*)[A-Z0-9_+-]@([A-Z0-9][A-Z0-9\-]*\.)+[A-Z]{2,}$/i,Xe="^(\\p{Extended_Pictographic}|\\p{Emoji_Component})+$",xe,Qe=/^(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])$/,Ke=/^(([a-f0-9]{1,4}:){7}|::([a-f0-9]{1,4}:){0,6}|([a-f0-9]{1,4}:){1}:([a-f0-9]{1,4}:){0,5}|([a-f0-9]{1,4}:){2}:([a-f0-9]{1,4}:){0,4}|([a-f0-9]{1,4}:){3}:([a-f0-9]{1,4}:){0,3}|([a-f0-9]{1,4}:){4}:([a-f0-9]{1,4}:){0,2}|([a-f0-9]{1,4}:){5}:([a-f0-9]{1,4}:){0,1})([a-f0-9]{1,4}|(((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2}))\.){3}((25[0-5])|(2[0-4][0-9])|(1[0-9]{2})|([0-9]{1,2})))$/,Fe=/^([0-9a-zA-Z+/]{4})*(([0-9a-zA-Z+/]{2}==)|([0-9a-zA-Z+/]{3}=))?$/,Re="((\\d\\d[2468][048]|\\d\\d[13579][26]|\\d\\d0[48]|[02468][048]00|[13579][26]00)-02-29|\\d{4}-((0[13578]|1[02])-(0[1-9]|[12]\\d|3[01])|(0[469]|11)-(0[1-9]|[12]\\d|30)|(02)-(0[1-9]|1\\d|2[0-8])))",et=new RegExp(`^${Re}$`);function Ie(r){let e="([01]\\d|2[0-3]):[0-5]\\d:[0-5]\\d";return r.precision?e=`${e}\\.\\d{${r.precision}}`:r.precision==null&&(e=`${e}(\\.\\d+)?`),e}function tt(r){return new RegExp(`^${Ie(r)}$`)}function Ae(r){let e=`${Re}T${Ie(r)}`,t=[];return t.push(r.local?"Z?":"Z"),r.offset&&t.push("([+-]\\d{2}:?\\d{2})"),e=`${e}(${t.join("|")})`,new RegExp(`^${e}$`)}function rt(r,e){return!!((e==="v4"||!e)&&Qe.test(r)||(e==="v6"||!e)&&Ke.test(r))}var M=class r extends _{_parse(e){if(this._def.coerce&&(e.data=String(e.data)),this._getType(e)!==f.string){let a=this._getOrReturnCtx(e);return u(a,{code:c.invalid_type,expected:f.string,received:a.parsedType}),m}let s=new x,n;for(let a of this._def.checks)if(a.kind==="min")e.data.length<a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:c.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),s.dirty());else if(a.kind==="max")e.data.length>a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:c.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!1,message:a.message}),s.dirty());else if(a.kind==="length"){let i=e.data.length>a.value,o=e.data.length<a.value;(i||o)&&(n=this._getOrReturnCtx(e,n),i?u(n,{code:c.too_big,maximum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}):o&&u(n,{code:c.too_small,minimum:a.value,type:"string",inclusive:!0,exact:!0,message:a.message}),s.dirty())}else if(a.kind==="email")Ge.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"email",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="emoji")xe||(xe=new RegExp(Xe,"u")),xe.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"emoji",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="uuid")Ye.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"uuid",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="nanoid")Je.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"nanoid",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="cuid")Be.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"cuid",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="cuid2")We.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"cuid2",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="ulid")qe.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"ulid",code:c.invalid_string,message:a.message}),s.dirty());else if(a.kind==="url")try{new URL(e.data)}catch{n=this._getOrReturnCtx(e,n),u(n,{validation:"url",code:c.invalid_string,message:a.message}),s.dirty()}else a.kind==="regex"?(a.regex.lastIndex=0,a.regex.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"regex",code:c.invalid_string,message:a.message}),s.dirty())):a.kind==="trim"?e.data=e.data.trim():a.kind==="includes"?e.data.includes(a.value,a.position)||(n=this._getOrReturnCtx(e,n),u(n,{code:c.invalid_string,validation:{includes:a.value,position:a.position},message:a.message}),s.dirty()):a.kind==="toLowerCase"?e.data=e.data.toLowerCase():a.kind==="toUpperCase"?e.data=e.data.toUpperCase():a.kind==="startsWith"?e.data.startsWith(a.value)||(n=this._getOrReturnCtx(e,n),u(n,{code:c.invalid_string,validation:{startsWith:a.value},message:a.message}),s.dirty()):a.kind==="endsWith"?e.data.endsWith(a.value)||(n=this._getOrReturnCtx(e,n),u(n,{code:c.invalid_string,validation:{endsWith:a.value},message:a.message}),s.dirty()):a.kind==="datetime"?Ae(a).test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{code:c.invalid_string,validation:"datetime",message:a.message}),s.dirty()):a.kind==="date"?et.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{code:c.invalid_string,validation:"date",message:a.message}),s.dirty()):a.kind==="time"?tt(a).test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{code:c.invalid_string,validation:"time",message:a.message}),s.dirty()):a.kind==="duration"?He.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"duration",code:c.invalid_string,message:a.message}),s.dirty()):a.kind==="ip"?rt(e.data,a.version)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"ip",code:c.invalid_string,message:a.message}),s.dirty()):a.kind==="base64"?Fe.test(e.data)||(n=this._getOrReturnCtx(e,n),u(n,{validation:"base64",code:c.invalid_string,message:a.message}),s.dirty()):g.assertNever(a);return{status:s.value,value:e.data}}_regex(e,t,s){return this.refinement(n=>e.test(n),{validation:t,code:c.invalid_string,...h.errToObj(s)})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}email(e){return this._addCheck({kind:"email",...h.errToObj(e)})}url(e){return this._addCheck({kind:"url",...h.errToObj(e)})}emoji(e){return this._addCheck({kind:"emoji",...h.errToObj(e)})}uuid(e){return this._addCheck({kind:"uuid",...h.errToObj(e)})}nanoid(e){return this._addCheck({kind:"nanoid",...h.errToObj(e)})}cuid(e){return this._addCheck({kind:"cuid",...h.errToObj(e)})}cuid2(e){return this._addCheck({kind:"cuid2",...h.errToObj(e)})}ulid(e){return this._addCheck({kind:"ulid",...h.errToObj(e)})}base64(e){return this._addCheck({kind:"base64",...h.errToObj(e)})}ip(e){return this._addCheck({kind:"ip",...h.errToObj(e)})}datetime(e){var t,s;return typeof e=="string"?this._addCheck({kind:"datetime",precision:null,offset:!1,local:!1,message:e}):this._addCheck({kind:"datetime",precision:typeof e?.precision>"u"?null:e?.precision,offset:(t=e?.offset)!==null&&t!==void 0?t:!1,local:(s=e?.local)!==null&&s!==void 0?s:!1,...h.errToObj(e?.message)})}date(e){return this._addCheck({kind:"date",message:e})}time(e){return typeof e=="string"?this._addCheck({kind:"time",precision:null,message:e}):this._addCheck({kind:"time",precision:typeof e?.precision>"u"?null:e?.precision,...h.errToObj(e?.message)})}duration(e){return this._addCheck({kind:"duration",...h.errToObj(e)})}regex(e,t){return this._addCheck({kind:"regex",regex:e,...h.errToObj(t)})}includes(e,t){return this._addCheck({kind:"includes",value:e,position:t?.position,...h.errToObj(t?.message)})}startsWith(e,t){return this._addCheck({kind:"startsWith",value:e,...h.errToObj(t)})}endsWith(e,t){return this._addCheck({kind:"endsWith",value:e,...h.errToObj(t)})}min(e,t){return this._addCheck({kind:"min",value:e,...h.errToObj(t)})}max(e,t){return this._addCheck({kind:"max",value:e,...h.errToObj(t)})}length(e,t){return this._addCheck({kind:"length",value:e,...h.errToObj(t)})}nonempty(e){return this.min(1,h.errToObj(e))}trim(){return new r({...this._def,checks:[...this._def.checks,{kind:"trim"}]})}toLowerCase(){return new r({...this._def,checks:[...this._def.checks,{kind:"toLowerCase"}]})}toUpperCase(){return new r({...this._def,checks:[...this._def.checks,{kind:"toUpperCase"}]})}get isDatetime(){return!!this._def.checks.find(e=>e.kind==="datetime")}get isDate(){return!!this._def.checks.find(e=>e.kind==="date")}get isTime(){return!!this._def.checks.find(e=>e.kind==="time")}get isDuration(){return!!this._def.checks.find(e=>e.kind==="duration")}get isEmail(){return!!this._def.checks.find(e=>e.kind==="email")}get isURL(){return!!this._def.checks.find(e=>e.kind==="url")}get isEmoji(){return!!this._def.checks.find(e=>e.kind==="emoji")}get isUUID(){return!!this._def.checks.find(e=>e.kind==="uuid")}get isNANOID(){return!!this._def.checks.find(e=>e.kind==="nanoid")}get isCUID(){return!!this._def.checks.find(e=>e.kind==="cuid")}get isCUID2(){return!!this._def.checks.find(e=>e.kind==="cuid2")}get isULID(){return!!this._def.checks.find(e=>e.kind==="ulid")}get isIP(){return!!this._def.checks.find(e=>e.kind==="ip")}get isBase64(){return!!this._def.checks.find(e=>e.kind==="base64")}get minLength(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxLength(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};M.create=r=>{var e;return new M({checks:[],typeName:p.ZodString,coerce:(e=r?.coerce)!==null&&e!==void 0?e:!1,...v(r)})};function st(r,e){let t=(r.toString().split(".")[1]||"").length,s=(e.toString().split(".")[1]||"").length,n=t>s?t:s,a=parseInt(r.toFixed(n).replace(".","")),i=parseInt(e.toFixed(n).replace(".",""));return a%i/Math.pow(10,n)}var P=class r extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte,this.step=this.multipleOf}_parse(e){if(this._def.coerce&&(e.data=Number(e.data)),this._getType(e)!==f.number){let a=this._getOrReturnCtx(e);return u(a,{code:c.invalid_type,expected:f.number,received:a.parsedType}),m}let s,n=new x;for(let a of this._def.checks)a.kind==="int"?g.isInteger(e.data)||(s=this._getOrReturnCtx(e,s),u(s,{code:c.invalid_type,expected:"integer",received:"float",message:a.message}),n.dirty()):a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:c.too_small,minimum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:c.too_big,maximum:a.value,type:"number",inclusive:a.inclusive,exact:!1,message:a.message}),n.dirty()):a.kind==="multipleOf"?st(e.data,a.value)!==0&&(s=this._getOrReturnCtx(e,s),u(s,{code:c.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):a.kind==="finite"?Number.isFinite(e.data)||(s=this._getOrReturnCtx(e,s),u(s,{code:c.not_finite,message:a.message}),n.dirty()):g.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,s,n){return new r({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:h.toString(n)}]})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}int(e){return this._addCheck({kind:"int",message:h.toString(e)})}positive(e){return this._addCheck({kind:"min",value:0,inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:0,inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:0,inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:0,inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}finite(e){return this._addCheck({kind:"finite",message:h.toString(e)})}safe(e){return this._addCheck({kind:"min",inclusive:!0,value:Number.MIN_SAFE_INTEGER,message:h.toString(e)})._addCheck({kind:"max",inclusive:!0,value:Number.MAX_SAFE_INTEGER,message:h.toString(e)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}get isInt(){return!!this._def.checks.find(e=>e.kind==="int"||e.kind==="multipleOf"&&g.isInteger(e.value))}get isFinite(){let e=null,t=null;for(let s of this._def.checks){if(s.kind==="finite"||s.kind==="int"||s.kind==="multipleOf")return!0;s.kind==="min"?(t===null||s.value>t)&&(t=s.value):s.kind==="max"&&(e===null||s.value<e)&&(e=s.value)}return Number.isFinite(t)&&Number.isFinite(e)}};P.create=r=>new P({checks:[],typeName:p.ZodNumber,coerce:r?.coerce||!1,...v(r)});var D=class r extends _{constructor(){super(...arguments),this.min=this.gte,this.max=this.lte}_parse(e){if(this._def.coerce&&(e.data=BigInt(e.data)),this._getType(e)!==f.bigint){let a=this._getOrReturnCtx(e);return u(a,{code:c.invalid_type,expected:f.bigint,received:a.parsedType}),m}let s,n=new x;for(let a of this._def.checks)a.kind==="min"?(a.inclusive?e.data<a.value:e.data<=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:c.too_small,type:"bigint",minimum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="max"?(a.inclusive?e.data>a.value:e.data>=a.value)&&(s=this._getOrReturnCtx(e,s),u(s,{code:c.too_big,type:"bigint",maximum:a.value,inclusive:a.inclusive,message:a.message}),n.dirty()):a.kind==="multipleOf"?e.data%a.value!==BigInt(0)&&(s=this._getOrReturnCtx(e,s),u(s,{code:c.not_multiple_of,multipleOf:a.value,message:a.message}),n.dirty()):g.assertNever(a);return{status:n.value,value:e.data}}gte(e,t){return this.setLimit("min",e,!0,h.toString(t))}gt(e,t){return this.setLimit("min",e,!1,h.toString(t))}lte(e,t){return this.setLimit("max",e,!0,h.toString(t))}lt(e,t){return this.setLimit("max",e,!1,h.toString(t))}setLimit(e,t,s,n){return new r({...this._def,checks:[...this._def.checks,{kind:e,value:t,inclusive:s,message:h.toString(n)}]})}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}positive(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!1,message:h.toString(e)})}negative(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!1,message:h.toString(e)})}nonpositive(e){return this._addCheck({kind:"max",value:BigInt(0),inclusive:!0,message:h.toString(e)})}nonnegative(e){return this._addCheck({kind:"min",value:BigInt(0),inclusive:!0,message:h.toString(e)})}multipleOf(e,t){return this._addCheck({kind:"multipleOf",value:e,message:h.toString(t)})}get minValue(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e}get maxValue(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e}};D.create=r=>{var e;return new D({checks:[],typeName:p.ZodBigInt,coerce:(e=r?.coerce)!==null&&e!==void 0?e:!1,...v(r)})};var L=class extends _{_parse(e){if(this._def.coerce&&(e.data=!!e.data),this._getType(e)!==f.boolean){let s=this._getOrReturnCtx(e);return u(s,{code:c.invalid_type,expected:f.boolean,received:s.parsedType}),m}return k(e.data)}};L.create=r=>new L({typeName:p.ZodBoolean,coerce:r?.coerce||!1,...v(r)});var z=class r extends _{_parse(e){if(this._def.coerce&&(e.data=new Date(e.data)),this._getType(e)!==f.date){let a=this._getOrReturnCtx(e);return u(a,{code:c.invalid_type,expected:f.date,received:a.parsedType}),m}if(isNaN(e.data.getTime())){let a=this._getOrReturnCtx(e);return u(a,{code:c.invalid_date}),m}let s=new x,n;for(let a of this._def.checks)a.kind==="min"?e.data.getTime()<a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:c.too_small,message:a.message,inclusive:!0,exact:!1,minimum:a.value,type:"date"}),s.dirty()):a.kind==="max"?e.data.getTime()>a.value&&(n=this._getOrReturnCtx(e,n),u(n,{code:c.too_big,message:a.message,inclusive:!0,exact:!1,maximum:a.value,type:"date"}),s.dirty()):g.assertNever(a);return{status:s.value,value:new Date(e.data.getTime())}}_addCheck(e){return new r({...this._def,checks:[...this._def.checks,e]})}min(e,t){return this._addCheck({kind:"min",value:e.getTime(),message:h.toString(t)})}max(e,t){return this._addCheck({kind:"max",value:e.getTime(),message:h.toString(t)})}get minDate(){let e=null;for(let t of this._def.checks)t.kind==="min"&&(e===null||t.value>e)&&(e=t.value);return e!=null?new Date(e):null}get maxDate(){let e=null;for(let t of this._def.checks)t.kind==="max"&&(e===null||t.value<e)&&(e=t.value);return e!=null?new Date(e):null}};z.create=r=>new z({checks:[],coerce:r?.coerce||!1,typeName:p.ZodDate,...v(r)});var re=class extends _{_parse(e){if(this._getType(e)!==f.symbol){let s=this._getOrReturnCtx(e);return u(s,{code:c.invalid_type,expected:f.symbol,received:s.parsedType}),m}return k(e.data)}};re.create=r=>new re({typeName:p.ZodSymbol,...v(r)});var U=class extends _{_parse(e){if(this._getType(e)!==f.undefined){let s=this._getOrReturnCtx(e);return u(s,{code:c.invalid_type,expected:f.undefined,received:s.parsedType}),m}return k(e.data)}};U.create=r=>new U({typeName:p.ZodUndefined,...v(r)});var B=class extends _{_parse(e){if(this._getType(e)!==f.null){let s=this._getOrReturnCtx(e);return u(s,{code:c.invalid_type,expected:f.null,received:s.parsedType}),m}return k(e.data)}};B.create=r=>new B({typeName:p.ZodNull,...v(r)});var $=class extends _{constructor(){super(...arguments),this._any=!0}_parse(e){return k(e.data)}};$.create=r=>new $({typeName:p.ZodAny,...v(r)});var R=class extends _{constructor(){super(...arguments),this._unknown=!0}_parse(e){return k(e.data)}};R.create=r=>new R({typeName:p.ZodUnknown,...v(r)});var E=class extends _{_parse(e){let t=this._getOrReturnCtx(e);return u(t,{code:c.invalid_type,expected:f.never,received:t.parsedType}),m}};E.create=r=>new E({typeName:p.ZodNever,...v(r)});var se=class extends _{_parse(e){if(this._getType(e)!==f.undefined){let s=this._getOrReturnCtx(e);return u(s,{code:c.invalid_type,expected:f.void,received:s.parsedType}),m}return k(e.data)}};se.create=r=>new se({typeName:p.ZodVoid,...v(r)});var I=class r extends _{_parse(e){let{ctx:t,status:s}=this._processInputParams(e),n=this._def;if(t.parsedType!==f.array)return u(t,{code:c.invalid_type,expected:f.array,received:t.parsedType}),m;if(n.exactLength!==null){let i=t.data.length>n.exactLength.value,o=t.data.length<n.exactLength.value;(i||o)&&(u(t,{code:i?c.too_big:c.too_small,minimum:o?n.exactLength.value:void 0,maximum:i?n.exactLength.value:void 0,type:"array",inclusive:!0,exact:!0,message:n.exactLength.message}),s.dirty())}if(n.minLength!==null&&t.data.length<n.minLength.value&&(u(t,{code:c.too_small,minimum:n.minLength.value,type:"array",inclusive:!0,exact:!1,message:n.minLength.message}),s.dirty()),n.maxLength!==null&&t.data.length>n.maxLength.value&&(u(t,{code:c.too_big,maximum:n.maxLength.value,type:"array",inclusive:!0,exact:!1,message:n.maxLength.message}),s.dirty()),t.common.async)return Promise.all([...t.data].map((i,o)=>n.type._parseAsync(new C(t,i,t.path,o)))).then(i=>x.mergeArray(s,i));let a=[...t.data].map((i,o)=>n.type._parseSync(new C(t,i,t.path,o)));return x.mergeArray(s,a)}get element(){return this._def.type}min(e,t){return new r({...this._def,minLength:{value:e,message:h.toString(t)}})}max(e,t){return new r({...this._def,maxLength:{value:e,message:h.toString(t)}})}length(e,t){return new r({...this._def,exactLength:{value:e,message:h.toString(t)}})}nonempty(e){return this.min(1,e)}};I.create=(r,e)=>new I({type:r,minLength:null,maxLength:null,exactLength:null,typeName:p.ZodArray,...v(e)});function F(r){if(r instanceof b){let e={};for(let t in r.shape){let s=r.shape[t];e[t]=O.create(F(s))}return new b({...r._def,shape:()=>e})}else return r instanceof I?new I({...r._def,type:F(r.element)}):r instanceof O?O.create(F(r.unwrap())):r instanceof Z?Z.create(F(r.unwrap())):r instanceof N?N.create(r.items.map(e=>F(e))):r}var b=class r extends _{constructor(){super(...arguments),this._cached=null,this.nonstrict=this.passthrough,this.augment=this.extend}_getCached(){if(this._cached!==null)return this._cached;let e=this._def.shape(),t=g.objectKeys(e);return this._cached={shape:e,keys:t}}_parse(e){if(this._getType(e)!==f.object){let d=this._getOrReturnCtx(e);return u(d,{code:c.invalid_type,expected:f.object,received:d.parsedType}),m}let{status:s,ctx:n}=this._processInputParams(e),{shape:a,keys:i}=this._getCached(),o=[];if(!(this._def.catchall instanceof E&&this._def.unknownKeys==="strip"))for(let d in n.data)i.includes(d)||o.push(d);let l=[];for(let d of i){let y=a[d],S=n.data[d];l.push({key:{status:"valid",value:d},value:y._parse(new C(n,S,n.path,d)),alwaysSet:d in n.data})}if(this._def.catchall instanceof E){let d=this._def.unknownKeys;if(d==="passthrough")for(let y of o)l.push({key:{status:"valid",value:y},value:{status:"valid",value:n.data[y]}});else if(d==="strict")o.length>0&&(u(n,{code:c.unrecognized_keys,keys:o}),s.dirty());else if(d!=="strip")throw new Error("Internal ZodObject error: invalid unknownKeys value.")}else{let d=this._def.catchall;for(let y of o){let S=n.data[y];l.push({key:{status:"valid",value:y},value:d._parse(new C(n,S,n.path,y)),alwaysSet:y in n.data})}}return n.common.async?Promise.resolve().then(async()=>{let d=[];for(let y of l){let S=await y.key,Se=await y.value;d.push({key:S,value:Se,alwaysSet:y.alwaysSet})}return d}).then(d=>x.mergeObjectSync(s,d)):x.mergeObjectSync(s,l)}get shape(){return this._def.shape()}strict(e){return h.errToObj,new r({...this._def,unknownKeys:"strict",...e!==void 0?{errorMap:(t,s)=>{var n,a,i,o;let l=(i=(a=(n=this._def).errorMap)===null||a===void 0?void 0:a.call(n,t,s).message)!==null&&i!==void 0?i:s.defaultError;return t.code==="unrecognized_keys"?{message:(o=h.errToObj(e).message)!==null&&o!==void 0?o:l}:{message:l}}}:{}})}strip(){return new r({...this._def,unknownKeys:"strip"})}passthrough(){return new r({...this._def,unknownKeys:"passthrough"})}extend(e){return new r({...this._def,shape:()=>({...this._def.shape(),...e})})}merge(e){return new r({unknownKeys:e._def.unknownKeys,catchall:e._def.catchall,shape:()=>({...this._def.shape(),...e._def.shape()}),typeName:p.ZodObject})}setKey(e,t){return this.augment({[e]:t})}catchall(e){return new r({...this._def,catchall:e})}pick(e){let t={};return g.objectKeys(e).forEach(s=>{e[s]&&this.shape[s]&&(t[s]=this.shape[s])}),new r({...this._def,shape:()=>t})}omit(e){let t={};return g.objectKeys(this.shape).forEach(s=>{e[s]||(t[s]=this.shape[s])}),new r({...this._def,shape:()=>t})}deepPartial(){return F(this)}partial(e){let t={};return g.objectKeys(this.shape).forEach(s=>{let n=this.shape[s];e&&!e[s]?t[s]=n:t[s]=n.optional()}),new r({...this._def,shape:()=>t})}required(e){let t={};return g.objectKeys(this.shape).forEach(s=>{if(e&&!e[s])t[s]=this.shape[s];else{let a=this.shape[s];for(;a instanceof O;)a=a._def.innerType;t[s]=a}}),new r({...this._def,shape:()=>t})}keyof(){return Me(g.objectKeys(this.shape))}};b.create=(r,e)=>new b({shape:()=>r,unknownKeys:"strip",catchall:E.create(),typeName:p.ZodObject,...v(e)});b.strictCreate=(r,e)=>new b({shape:()=>r,unknownKeys:"strict",catchall:E.create(),typeName:p.ZodObject,...v(e)});b.lazycreate=(r,e)=>new b({shape:r,unknownKeys:"strip",catchall:E.create(),typeName:p.ZodObject,...v(e)});var W=class extends _{_parse(e){let{ctx:t}=this._processInputParams(e),s=this._def.options;function n(a){for(let o of a)if(o.result.status==="valid")return o.result;for(let o of a)if(o.result.status==="dirty")return t.common.issues.push(...o.ctx.common.issues),o.result;let i=a.map(o=>new w(o.ctx.common.issues));return u(t,{code:c.invalid_union,unionErrors:i}),m}if(t.common.async)return Promise.all(s.map(async a=>{let i={...t,common:{...t.common,issues:[]},parent:null};return{result:await a._parseAsync({data:t.data,path:t.path,parent:i}),ctx:i}})).then(n);{let a,i=[];for(let l of s){let d={...t,common:{...t.common,issues:[]},parent:null},y=l._parseSync({data:t.data,path:t.path,parent:d});if(y.status==="valid")return y;y.status==="dirty"&&!a&&(a={result:y,ctx:d}),d.common.issues.length&&i.push(d.common.issues)}if(a)return t.common.issues.push(...a.ctx.common.issues),a.result;let o=i.map(l=>new w(l));return u(t,{code:c.invalid_union,unionErrors:o}),m}}get options(){return this._def.options}};W.create=(r,e)=>new W({options:r,typeName:p.ZodUnion,...v(e)});var j=r=>r instanceof Y?j(r.schema):r instanceof T?j(r.innerType()):r instanceof J?[r.value]:r instanceof H?r.options:r instanceof G?g.objectValues(r.enum):r instanceof X?j(r._def.innerType):r instanceof U?[void 0]:r instanceof B?[null]:r instanceof O?[void 0,...j(r.unwrap())]:r instanceof Z?[null,...j(r.unwrap())]:r instanceof le||r instanceof K?j(r.unwrap()):r instanceof Q?j(r._def.innerType):[],ye=class r extends _{_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.object)return u(t,{code:c.invalid_type,expected:f.object,received:t.parsedType}),m;let s=this.discriminator,n=t.data[s],a=this.optionsMap.get(n);return a?t.common.async?a._parseAsync({data:t.data,path:t.path,parent:t}):a._parseSync({data:t.data,path:t.path,parent:t}):(u(t,{code:c.invalid_union_discriminator,options:Array.from(this.optionsMap.keys()),path:[s]}),m)}get discriminator(){return this._def.discriminator}get options(){return this._def.options}get optionsMap(){return this._def.optionsMap}static create(e,t,s){let n=new Map;for(let a of t){let i=j(a.shape[e]);if(!i.length)throw new Error(`A discriminator value for key \`${e}\` could not be extracted from all schema options`);for(let o of i){if(n.has(o))throw new Error(`Discriminator property ${String(e)} has duplicate value ${String(o)}`);n.set(o,a)}}return new r({typeName:p.ZodDiscriminatedUnion,discriminator:e,options:t,optionsMap:n,...v(s)})}};function Te(r,e){let t=A(r),s=A(e);if(r===e)return{valid:!0,data:r};if(t===f.object&&s===f.object){let n=g.objectKeys(e),a=g.objectKeys(r).filter(o=>n.indexOf(o)!==-1),i={...r,...e};for(let o of a){let l=Te(r[o],e[o]);if(!l.valid)return{valid:!1};i[o]=l.data}return{valid:!0,data:i}}else if(t===f.array&&s===f.array){if(r.length!==e.length)return{valid:!1};let n=[];for(let a=0;a<r.length;a++){let i=r[a],o=e[a],l=Te(i,o);if(!l.valid)return{valid:!1};n.push(l.data)}return{valid:!0,data:n}}else return t===f.date&&s===f.date&&+r==+e?{valid:!0,data:r}:{valid:!1}}var q=class extends _{_parse(e){let{status:t,ctx:s}=this._processInputParams(e),n=(a,i)=>{if(be(a)||be(i))return m;let o=Te(a.value,i.value);return o.valid?((we(a)||we(i))&&t.dirty(),{status:t.value,value:o.data}):(u(s,{code:c.invalid_intersection_types}),m)};return s.common.async?Promise.all([this._def.left._parseAsync({data:s.data,path:s.path,parent:s}),this._def.right._parseAsync({data:s.data,path:s.path,parent:s})]).then(([a,i])=>n(a,i)):n(this._def.left._parseSync({data:s.data,path:s.path,parent:s}),this._def.right._parseSync({data:s.data,path:s.path,parent:s}))}};q.create=(r,e,t)=>new q({left:r,right:e,typeName:p.ZodIntersection,...v(t)});var N=class r extends _{_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.array)return u(s,{code:c.invalid_type,expected:f.array,received:s.parsedType}),m;if(s.data.length<this._def.items.length)return u(s,{code:c.too_small,minimum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),m;!this._def.rest&&s.data.length>this._def.items.length&&(u(s,{code:c.too_big,maximum:this._def.items.length,inclusive:!0,exact:!1,type:"array"}),t.dirty());let a=[...s.data].map((i,o)=>{let l=this._def.items[o]||this._def.rest;return l?l._parse(new C(s,i,s.path,o)):null}).filter(i=>!!i);return s.common.async?Promise.all(a).then(i=>x.mergeArray(t,i)):x.mergeArray(t,a)}get items(){return this._def.items}rest(e){return new r({...this._def,rest:e})}};N.create=(r,e)=>{if(!Array.isArray(r))throw new Error("You must pass an array of schemas to z.tuple([ ... ])");return new N({items:r,typeName:p.ZodTuple,rest:null,...v(e)})};var ve=class r extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.object)return u(s,{code:c.invalid_type,expected:f.object,received:s.parsedType}),m;let n=[],a=this._def.keyType,i=this._def.valueType;for(let o in s.data)n.push({key:a._parse(new C(s,o,s.path,o)),value:i._parse(new C(s,s.data[o],s.path,o)),alwaysSet:o in s.data});return s.common.async?x.mergeObjectAsync(t,n):x.mergeObjectSync(t,n)}get element(){return this._def.valueType}static create(e,t,s){return t instanceof _?new r({keyType:e,valueType:t,typeName:p.ZodRecord,...v(s)}):new r({keyType:M.create(),valueType:e,typeName:p.ZodRecord,...v(t)})}},ne=class extends _{get keySchema(){return this._def.keyType}get valueSchema(){return this._def.valueType}_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.map)return u(s,{code:c.invalid_type,expected:f.map,received:s.parsedType}),m;let n=this._def.keyType,a=this._def.valueType,i=[...s.data.entries()].map(([o,l],d)=>({key:n._parse(new C(s,o,s.path,[d,"key"])),value:a._parse(new C(s,l,s.path,[d,"value"]))}));if(s.common.async){let o=new Map;return Promise.resolve().then(async()=>{for(let l of i){let d=await l.key,y=await l.value;if(d.status==="aborted"||y.status==="aborted")return m;(d.status==="dirty"||y.status==="dirty")&&t.dirty(),o.set(d.value,y.value)}return{status:t.value,value:o}})}else{let o=new Map;for(let l of i){let d=l.key,y=l.value;if(d.status==="aborted"||y.status==="aborted")return m;(d.status==="dirty"||y.status==="dirty")&&t.dirty(),o.set(d.value,y.value)}return{status:t.value,value:o}}}};ne.create=(r,e,t)=>new ne({valueType:e,keyType:r,typeName:p.ZodMap,...v(t)});var ae=class r extends _{_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.parsedType!==f.set)return u(s,{code:c.invalid_type,expected:f.set,received:s.parsedType}),m;let n=this._def;n.minSize!==null&&s.data.size<n.minSize.value&&(u(s,{code:c.too_small,minimum:n.minSize.value,type:"set",inclusive:!0,exact:!1,message:n.minSize.message}),t.dirty()),n.maxSize!==null&&s.data.size>n.maxSize.value&&(u(s,{code:c.too_big,maximum:n.maxSize.value,type:"set",inclusive:!0,exact:!1,message:n.maxSize.message}),t.dirty());let a=this._def.valueType;function i(l){let d=new Set;for(let y of l){if(y.status==="aborted")return m;y.status==="dirty"&&t.dirty(),d.add(y.value)}return{status:t.value,value:d}}let o=[...s.data.values()].map((l,d)=>a._parse(new C(s,l,s.path,d)));return s.common.async?Promise.all(o).then(l=>i(l)):i(o)}min(e,t){return new r({...this._def,minSize:{value:e,message:h.toString(t)}})}max(e,t){return new r({...this._def,maxSize:{value:e,message:h.toString(t)}})}size(e,t){return this.min(e,t).max(e,t)}nonempty(e){return this.min(1,e)}};ae.create=(r,e)=>new ae({valueType:r,minSize:null,maxSize:null,typeName:p.ZodSet,...v(e)});var _e=class r extends _{constructor(){super(...arguments),this.validate=this.implement}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.function)return u(t,{code:c.invalid_type,expected:f.function,received:t.parsedType}),m;function s(o,l){return pe({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,he(),te].filter(d=>!!d),issueData:{code:c.invalid_arguments,argumentsError:l}})}function n(o,l){return pe({data:o,path:t.path,errorMaps:[t.common.contextualErrorMap,t.schemaErrorMap,he(),te].filter(d=>!!d),issueData:{code:c.invalid_return_type,returnTypeError:l}})}let a={errorMap:t.common.contextualErrorMap},i=t.data;if(this._def.returns instanceof V){let o=this;return k(async function(...l){let d=new w([]),y=await o._def.args.parseAsync(l,a).catch(ge=>{throw d.addIssue(s(l,ge)),d}),S=await Reflect.apply(i,this,y);return await o._def.returns._def.type.parseAsync(S,a).catch(ge=>{throw d.addIssue(n(S,ge)),d})})}else{let o=this;return k(function(...l){let d=o._def.args.safeParse(l,a);if(!d.success)throw new w([s(l,d.error)]);let y=Reflect.apply(i,this,d.data),S=o._def.returns.safeParse(y,a);if(!S.success)throw new w([n(y,S.error)]);return S.data})}}parameters(){return this._def.args}returnType(){return this._def.returns}args(...e){return new r({...this._def,args:N.create(e).rest(R.create())})}returns(e){return new r({...this._def,returns:e})}implement(e){return this.parse(e)}strictImplement(e){return this.parse(e)}static create(e,t,s){return new r({args:e||N.create([]).rest(R.create()),returns:t||R.create(),typeName:p.ZodFunction,...v(s)})}},Y=class extends _{get schema(){return this._def.getter()}_parse(e){let{ctx:t}=this._processInputParams(e);return this._def.getter()._parse({data:t.data,path:t.path,parent:t})}};Y.create=(r,e)=>new Y({getter:r,typeName:p.ZodLazy,...v(e)});var J=class extends _{_parse(e){if(e.data!==this._def.value){let t=this._getOrReturnCtx(e);return u(t,{received:t.data,code:c.invalid_literal,expected:this._def.value}),m}return{status:"valid",value:e.data}}get value(){return this._def.value}};J.create=(r,e)=>new J({value:r,typeName:p.ZodLiteral,...v(e)});function Me(r,e){return new H({values:r,typeName:p.ZodEnum,...v(e)})}var H=class r extends _{constructor(){super(...arguments),oe.set(this,void 0)}_parse(e){if(typeof e.data!="string"){let t=this._getOrReturnCtx(e),s=this._def.values;return u(t,{expected:g.joinValues(s),received:t.parsedType,code:c.invalid_type}),m}if(me(this,oe,"f")||je(this,oe,new Set(this._def.values),"f"),!me(this,oe,"f").has(e.data)){let t=this._getOrReturnCtx(e),s=this._def.values;return u(t,{received:t.data,code:c.invalid_enum_value,options:s}),m}return k(e.data)}get options(){return this._def.values}get enum(){let e={};for(let t of this._def.values)e[t]=t;return e}get Values(){let e={};for(let t of this._def.values)e[t]=t;return e}get Enum(){let e={};for(let t of this._def.values)e[t]=t;return e}extract(e,t=this._def){return r.create(e,{...this._def,...t})}exclude(e,t=this._def){return r.create(this.options.filter(s=>!e.includes(s)),{...this._def,...t})}};oe=new WeakMap;H.create=Me;var G=class extends _{constructor(){super(...arguments),ce.set(this,void 0)}_parse(e){let t=g.getValidEnumValues(this._def.values),s=this._getOrReturnCtx(e);if(s.parsedType!==f.string&&s.parsedType!==f.number){let n=g.objectValues(t);return u(s,{expected:g.joinValues(n),received:s.parsedType,code:c.invalid_type}),m}if(me(this,ce,"f")||je(this,ce,new Set(g.getValidEnumValues(this._def.values)),"f"),!me(this,ce,"f").has(e.data)){let n=g.objectValues(t);return u(s,{received:s.data,code:c.invalid_enum_value,options:n}),m}return k(e.data)}get enum(){return this._def.values}};ce=new WeakMap;G.create=(r,e)=>new G({values:r,typeName:p.ZodNativeEnum,...v(e)});var V=class extends _{unwrap(){return this._def.type}_parse(e){let{ctx:t}=this._processInputParams(e);if(t.parsedType!==f.promise&&t.common.async===!1)return u(t,{code:c.invalid_type,expected:f.promise,received:t.parsedType}),m;let s=t.parsedType===f.promise?t.data:Promise.resolve(t.data);return k(s.then(n=>this._def.type.parseAsync(n,{path:t.path,errorMap:t.common.contextualErrorMap})))}};V.create=(r,e)=>new V({type:r,typeName:p.ZodPromise,...v(e)});var T=class extends _{innerType(){return this._def.schema}sourceType(){return this._def.schema._def.typeName===p.ZodEffects?this._def.schema.sourceType():this._def.schema}_parse(e){let{status:t,ctx:s}=this._processInputParams(e),n=this._def.effect||null,a={addIssue:i=>{u(s,i),i.fatal?t.abort():t.dirty()},get path(){return s.path}};if(a.addIssue=a.addIssue.bind(a),n.type==="preprocess"){let i=n.transform(s.data,a);if(s.common.async)return Promise.resolve(i).then(async o=>{if(t.value==="aborted")return m;let l=await this._def.schema._parseAsync({data:o,path:s.path,parent:s});return l.status==="aborted"?m:l.status==="dirty"||t.value==="dirty"?ee(l.value):l});{if(t.value==="aborted")return m;let o=this._def.schema._parseSync({data:i,path:s.path,parent:s});return o.status==="aborted"?m:o.status==="dirty"||t.value==="dirty"?ee(o.value):o}}if(n.type==="refinement"){let i=o=>{let l=n.refinement(o,a);if(s.common.async)return Promise.resolve(l);if(l instanceof Promise)throw new Error("Async refinement encountered during synchronous parse operation. Use .parseAsync instead.");return o};if(s.common.async===!1){let o=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});return o.status==="aborted"?m:(o.status==="dirty"&&t.dirty(),i(o.value),{status:t.value,value:o.value})}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(o=>o.status==="aborted"?m:(o.status==="dirty"&&t.dirty(),i(o.value).then(()=>({status:t.value,value:o.value}))))}if(n.type==="transform")if(s.common.async===!1){let i=this._def.schema._parseSync({data:s.data,path:s.path,parent:s});if(!de(i))return i;let o=n.transform(i.value,a);if(o instanceof Promise)throw new Error("Asynchronous transform encountered during synchronous parse operation. Use .parseAsync instead.");return{status:t.value,value:o}}else return this._def.schema._parseAsync({data:s.data,path:s.path,parent:s}).then(i=>de(i)?Promise.resolve(n.transform(i.value,a)).then(o=>({status:t.value,value:o})):i);g.assertNever(n)}};T.create=(r,e,t)=>new T({schema:r,typeName:p.ZodEffects,effect:e,...v(t)});T.createWithPreprocess=(r,e,t)=>new T({schema:e,effect:{type:"preprocess",transform:r},typeName:p.ZodEffects,...v(t)});var O=class extends _{_parse(e){return this._getType(e)===f.undefined?k(void 0):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};O.create=(r,e)=>new O({innerType:r,typeName:p.ZodOptional,...v(e)});var Z=class extends _{_parse(e){return this._getType(e)===f.null?k(null):this._def.innerType._parse(e)}unwrap(){return this._def.innerType}};Z.create=(r,e)=>new Z({innerType:r,typeName:p.ZodNullable,...v(e)});var X=class extends _{_parse(e){let{ctx:t}=this._processInputParams(e),s=t.data;return t.parsedType===f.undefined&&(s=this._def.defaultValue()),this._def.innerType._parse({data:s,path:t.path,parent:t})}removeDefault(){return this._def.innerType}};X.create=(r,e)=>new X({innerType:r,typeName:p.ZodDefault,defaultValue:typeof e.default=="function"?e.default:()=>e.default,...v(e)});var Q=class extends _{_parse(e){let{ctx:t}=this._processInputParams(e),s={...t,common:{...t.common,issues:[]}},n=this._def.innerType._parse({data:s.data,path:s.path,parent:{...s}});return ue(n)?n.then(a=>({status:"valid",value:a.status==="valid"?a.value:this._def.catchValue({get error(){return new w(s.common.issues)},input:s.data})})):{status:"valid",value:n.status==="valid"?n.value:this._def.catchValue({get error(){return new w(s.common.issues)},input:s.data})}}removeCatch(){return this._def.innerType}};Q.create=(r,e)=>new Q({innerType:r,typeName:p.ZodCatch,catchValue:typeof e.catch=="function"?e.catch:()=>e.catch,...v(e)});var ie=class extends _{_parse(e){if(this._getType(e)!==f.nan){let s=this._getOrReturnCtx(e);return u(s,{code:c.invalid_type,expected:f.nan,received:s.parsedType}),m}return{status:"valid",value:e.data}}};ie.create=r=>new ie({typeName:p.ZodNaN,...v(r)});var nt=Symbol("zod_brand"),le=class extends _{_parse(e){let{ctx:t}=this._processInputParams(e),s=t.data;return this._def.type._parse({data:s,path:t.path,parent:t})}unwrap(){return this._def.type}},fe=class r extends _{_parse(e){let{status:t,ctx:s}=this._processInputParams(e);if(s.common.async)return(async()=>{let a=await this._def.in._parseAsync({data:s.data,path:s.path,parent:s});return a.status==="aborted"?m:a.status==="dirty"?(t.dirty(),ee(a.value)):this._def.out._parseAsync({data:a.value,path:s.path,parent:s})})();{let n=this._def.in._parseSync({data:s.data,path:s.path,parent:s});return n.status==="aborted"?m:n.status==="dirty"?(t.dirty(),{status:"dirty",value:n.value}):this._def.out._parseSync({data:n.value,path:s.path,parent:s})}}static create(e,t){return new r({in:e,out:t,typeName:p.ZodPipeline})}},K=class extends _{_parse(e){let t=this._def.innerType._parse(e),s=n=>(de(n)&&(n.value=Object.freeze(n.value)),n);return ue(t)?t.then(n=>s(n)):s(t)}unwrap(){return this._def.innerType}};K.create=(r,e)=>new K({innerType:r,typeName:p.ZodReadonly,...v(e)});function $e(r,e={},t){return r?$.create().superRefine((s,n)=>{var a,i;if(!r(s)){let o=typeof e=="function"?e(s):typeof e=="string"?{message:e}:e,l=(i=(a=o.fatal)!==null&&a!==void 0?a:t)!==null&&i!==void 0?i:!0,d=typeof o=="string"?{message:o}:o;n.addIssue({code:"custom",...d,fatal:l})}}):$.create()}var at={object:b.lazycreate},p;(function(r){r.ZodString="ZodString",r.ZodNumber="ZodNumber",r.ZodNaN="ZodNaN",r.ZodBigInt="ZodBigInt",r.ZodBoolean="ZodBoolean",r.ZodDate="ZodDate",r.ZodSymbol="ZodSymbol",r.ZodUndefined="ZodUndefined",r.ZodNull="ZodNull",r.ZodAny="ZodAny",r.ZodUnknown="ZodUnknown",r.ZodNever="ZodNever",r.ZodVoid="ZodVoid",r.ZodArray="ZodArray",r.ZodObject="ZodObject",r.ZodUnion="ZodUnion",r.ZodDiscriminatedUnion="ZodDiscriminatedUnion",r.ZodIntersection="ZodIntersection",r.ZodTuple="ZodTuple",r.ZodRecord="ZodRecord",r.ZodMap="ZodMap",r.ZodSet="ZodSet",r.ZodFunction="ZodFunction",r.ZodLazy="ZodLazy",r.ZodLiteral="ZodLiteral",r.ZodEnum="ZodEnum",r.ZodEffects="ZodEffects",r.ZodNativeEnum="ZodNativeEnum",r.ZodOptional="ZodOptional",r.ZodNullable="ZodNullable",r.ZodDefault="ZodDefault",r.ZodCatch="ZodCatch",r.ZodPromise="ZodPromise",r.ZodBranded="ZodBranded",r.ZodPipeline="ZodPipeline",r.ZodReadonly="ZodReadonly"})(p||(p={}));var it=(r,e={message:`Input not instance of ${r.name}`})=>$e(t=>t instanceof r,e),Ve=M.create,Pe=P.create,ot=ie.create,ct=D.create,De=L.create,dt=z.create,ut=re.create,lt=U.create,ft=B.create,ht=$.create,pt=R.create,mt=E.create,yt=se.create,vt=I.create,_t=b.create,gt=b.strictCreate,xt=W.create,kt=ye.create,bt=q.create,wt=N.create,Tt=ve.create,St=ne.create,Ot=ae.create,Ct=_e.create,Et=Y.create,Nt=J.create,Zt=H.create,jt=G.create,Rt=V.create,Ne=T.create,It=O.create,At=Z.create,Mt=T.createWithPreprocess,$t=fe.create,Vt=()=>Ve().optional(),Pt=()=>Pe().optional(),Dt=()=>De().optional(),Lt={string:r=>M.create({...r,coerce:!0}),number:r=>P.create({...r,coerce:!0}),boolean:r=>L.create({...r,coerce:!0}),bigint:r=>D.create({...r,coerce:!0}),date:r=>z.create({...r,coerce:!0})},zt=m,Bt=Object.freeze({__proto__:null,defaultErrorMap:te,setErrorMap:ze,getErrorMap:he,makeIssue:pe,EMPTY_PATH:Ue,addIssueToContext:u,ParseStatus:x,INVALID:m,DIRTY:ee,OK:k,isAborted:be,isDirty:we,isValid:de,isAsync:ue,get util(){return g},get objectUtil(){return ke},ZodParsedType:f,getParsedType:A,ZodType:_,datetimeRegex:Ae,ZodString:M,ZodNumber:P,ZodBigInt:D,ZodBoolean:L,ZodDate:z,ZodSymbol:re,ZodUndefined:U,ZodNull:B,ZodAny:$,ZodUnknown:R,ZodNever:E,ZodVoid:se,ZodArray:I,ZodObject:b,ZodUnion:W,ZodDiscriminatedUnion:ye,ZodIntersection:q,ZodTuple:N,ZodRecord:ve,ZodMap:ne,ZodSet:ae,ZodFunction:_e,ZodLazy:Y,ZodLiteral:J,ZodEnum:H,ZodNativeEnum:G,ZodPromise:V,ZodEffects:T,ZodTransformer:T,ZodOptional:O,ZodNullable:Z,ZodDefault:X,ZodCatch:Q,ZodNaN:ie,BRAND:nt,ZodBranded:le,ZodPipeline:fe,ZodReadonly:K,custom:$e,Schema:_,ZodSchema:_,late:at,get ZodFirstPartyTypeKind(){return p},coerce:Lt,any:ht,array:vt,bigint:ct,boolean:De,date:dt,discriminatedUnion:kt,effect:Ne,enum:Zt,function:Ct,instanceof:it,intersection:bt,lazy:Et,literal:Nt,map:St,nan:ot,nativeEnum:jt,never:mt,null:ft,nullable:At,number:Pe,object:_t,oboolean:Dt,onumber:Pt,optional:It,ostring:Vt,pipeline:$t,preprocess:Mt,promise:Rt,record:Tt,set:Ot,strictObject:gt,string:Ve,symbol:ut,transformer:Ne,tuple:wt,undefined:lt,union:xt,unknown:pt,void:yt,NEVER:zt,ZodIssueCode:c,quotelessJson:Le,ZodError:w});export{Bt as a};
//# sourceMappingURL=chunk-4P36KWOF.js.map
